import { XmlComponent } from '../xml-components';
import { DocumentDefaults } from './defaults';
import { HeaderStyleInfo } from './header-style-info/header-style-info';
import { HeaderStyleInfoType } from './header-style-info/header-style-info-type';
import { Header } from '../header';
import { IDefaultTextProperty, IDefaultParaProperty } from '../../../../model/DocumentCore';
import { Background } from './background';
import { IEditorBackground } from '../../../../common/commonDefines';
import { DynamicGridLine } from './dynamicGridLine';
import { BaseXmlComponent, IXmlResult } from '../xml-components/base';
export * from './border';

export class Styles extends XmlComponent {

  public get Root(): (BaseXmlComponent | string)[] {
    return this.root;
  }

  private headerStyleInfo: HeaderStyleInfo;
  private documentDefaults: DocumentDefaults;
  private background: Background;

  constructor(initialStyles?: BaseXmlComponent) {
    super('w:styles');
    if (initialStyles) {
      this.root.push(initialStyles);
    }
  }

  public push(style: XmlComponent): Styles {
    this.root.push(style);
    return this;
  }

  public createDocumentDefaults(): DocumentDefaults {
    this.documentDefaults = new DocumentDefaults();
    // console.log(defaults)
    this.push(this.documentDefaults);
    return this.documentDefaults;
  }

  public createHeaderStyleInfo(): HeaderStyleInfo {
    // w: type  defaults to be 'none'
    this.headerStyleInfo = new HeaderStyleInfo(HeaderStyleInfoType.NONE);
    this.push(this.headerStyleInfo);
    return this.headerStyleInfo;
  }

  public createEditorBackground(): Background {
    this.background = new Background();
    this.push(this.background);
    return this.background;
  }

  public setHeaderStyleInfo(headers: Header[]): void {

    let type = HeaderStyleInfoType.NONE;

    // w: type
    if (headers.length === 1) {
      // type: single
      type = HeaderStyleInfoType.SINGLE;
      this.headerStyleInfo.setType(type);
    } else if (headers.length > 1) {
      // type: multiple
      type = HeaderStyleInfoType.MULTIPLE;
      this.headerStyleInfo.setType(type);
    }

    // <headerx>
    this.headerStyleInfo.addHeaderPage(headers.length, type);

  }

  public setHeaderPage(index: number, startPage: number, endPage: number): void {
    this.headerStyleInfo.setHeaderPage(index, startPage, endPage);
  }

  public setDefaultTextProperty(DefaultTextProperty: IDefaultTextProperty): void {
    this.documentDefaults.setDefaultTextProperty(DefaultTextProperty);
  }

  public setDefaultParaProperty(DefaultParaProperty: IDefaultParaProperty): void {
    this.documentDefaults.setDefaultParaProperty(DefaultParaProperty);
  }

  public setEditorBackground(editorBackground: IEditorBackground): void {
    this.background.setEditorBackground(editorBackground);
  }

  public setDynamicGridLine(line: number): void {
    const dy = new DynamicGridLine(line);
    this.push(dy);
  }

  // this method shouldn't be used
  // public createParagraphStyle(styleId: string, name?: string): ParagraphStyle {
  //   const para = new ParagraphStyle(styleId, name);
  //   this.push(para);
  //   return para;
  // }
  public prepForXml(): IXmlResult {
    const result = super.prepForXml();
    const key = this.rootKey;
    const text = `<${key}${result.attrs || ''}>${result.text}</${key}>`;
    return {
      text,
      attrs: null
    };
  }
}
