import { ISerialTableObj } from '../serialize/serialInterface';
import { SerialTable } from '../serialize/serializer';
import ExcelTableBuilder from './ExcelTableBuilder';

/** 
 * 通过Table对象集合序列化对象，然后创建Excel的Blob对象
 */
export async function excelTableGenerator(tables: any[]): Promise<Blob> {
    const tableCollection: ISerialTableObj[] = [];
    for (const table of tables) {
        new SerialTable(table).serializedTo(tableCollection);
    }
    const builder = new ExcelTableBuilder(tableCollection).generate();
    return builder.toBlob();
}
