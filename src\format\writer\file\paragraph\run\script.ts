import { Attributes, XmlComponent } from '../../xml-components';

export abstract class VerticalAlign extends XmlComponent {
  constructor(type: string) {
    super('w:vertAlign');
    this.root.push(
      new Attributes({
        val: type,
      }),
    );
  }
}

export class SuperScript extends VerticalAlign {
  constructor() {
    super('superscript');
  }
}

// tslint:disable-next-line: max-classes-per-file
export class SubScript extends VerticalAlign {
  constructor() {
    super('subscript');
  }
}
