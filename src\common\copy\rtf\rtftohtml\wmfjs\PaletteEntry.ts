import { Blob } from './Blob';
import { Obj } from './Primitives';
export class PaletteEntry {
    public flag: number;
    public b: number;
    public g: number;
    public r: number;

    constructor(reader: Blob, copy?: PaletteEntry) {
        if (reader != null) {
            this.flag = reader.readUint8();
            this.b = reader.readUint8();
            this.g = reader.readUint8();
            this.r = reader.readUint8();
        } else {
            this.flag = copy.flag;
            this.b = copy.b;
            this.g = copy.g;
            this.r = copy.r;
        }
    }

    public clone(): PaletteEntry {
        return new PaletteEntry(null, this);
    }
}

export class Palette extends Obj {
    public start: number;
    public entries: PaletteEntry[];

    constructor(reader: Blob, copy?: Palette) {
        super('palette');
        if (reader != null) {
            this.start = reader.readUint16();
            const cnt = reader.readUint16();
            this.entries = [];
            while (cnt > 0) {
                this.entries.push(new PaletteEntry(reader));
            }
        } else {
            this.start = copy.start;
            this.entries = [];
            const len = copy.entries.length;
            for (let i = 0; i < len; i++) {
                this.entries.push(copy.entries[i]);
            }
        }
    }

    public clone(): Palette {
        return new Palette(null, this);
    }

    public toString(): string {
        return '{ #entries: ' + this.entries.length + '}'; // TODO
    }
}
