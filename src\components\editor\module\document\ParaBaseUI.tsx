import React from 'react';
import { IDocumentParagraph } from '../../../../model/ParaProperty';
import { FONT_STYLE_TYPE, FONT_WEIGHT_TYPE, numtoFixed2, RenderSectionBackgroundType, ViewModeType } from '../../../../common/commonDefines';
import Image from './Image';
import Paragraph from './Paragraph';
/* IFTRUE_WATER */
import { RandomMarkDom } from './RandomMark';
import { getCode } from '@/common/MarkFactory';;
/* FITRUE_WATER */
import { SPELL_CHECK } from '@/common/Spellcheck'
import { IDcoumentPortionTextDecoration } from '@/model/ParaPortionProperty';


interface IProps {
    content: IDocumentParagraph;
    className?: string;
    scale?: number;
    pageIndex?: number;
    host?: any;
    cellId?: number;
    option?: any;
    nHeaderFooter?: number;
    cellRect?: any;
    bShowContent?: boolean;
}

export class ParaBaseUI extends React.Component<IProps, {}> {

    private scale: number;
    private _content: IDocumentParagraph;

    /* IFTRUE_WATER */
    private _strChange: number = -1;
    private _str: any;
    /* FITRUE_WATER */
    private _ref: any;
    constructor(props: IProps) {
        super(props);
        this._ref = React.createRef();
    }
    public render(): any {
        const { scale, className } = this.props;
        let content = this.props.content;
        if (this._content) {
            content = this._content;
        }
        this.scale = scale;
        return this.renderParagraphContent(content, className);
    }





    public refresh(options: any): any {
        this._content = options;
        this.setState({}, () => {
            this._content = null;
            SPELL_CHECK.renderPara(this.props.pageIndex);
        });
        return true;
    }

    private renderParagraphContent(content: IDocumentParagraph, className: string = ''): any {
        if (!content) {
            return null;
        }
        let randomClassName;
        /* IFTRUE_WATER */
        randomClassName =  this.props.host.documentCore.getCoreBGen() && Math.random() < 0.4 ? getCode() : null;
        /* FITRUE_WATER */
        return (
            <React.Fragment>
                {this.getBackgroundContent(content)}
                {/* <g className={'newcontrol-focus-container' + className}>
                    {this.getSectionSelection(content, RenderSectionBackgroundType.NewControlFocus)}
                </g>
                <g className={'paragraphline-container' + className}>
                    {this.getSectionSelection(content, RenderSectionBackgroundType.ParagragphLineSelection)}
                </g> */}

                {this.getTextDecorationLine(content.textDecoration)}
                {this.getTextDecorationLine(content.dynamicGridLine)}
                <g className={randomClassName} ref={this._ref}>
                    {this.renderParagraph(content, this.props.cellId)}
                </g>
                {this.renderImage(content.images)}
                {this.renderNumbering(content)}
            </React.Fragment>
        );
    }

    private renderNumbering(content: IDocumentParagraph): any {
        const { startLine, numbering, cellId } = content;
        if (startLine !== 0 || !numbering) {
            return;
        }
        const {textPr, text, x, y, width} = numbering;
        const weight = FONT_WEIGHT_TYPE.get(textPr.fontWeight);
        const fontStyle = FONT_STYLE_TYPE.get(textPr.fontStyle);
        return (
            <text
                x={numtoFixed2(x)}
                y={numtoFixed2(y)}
                fontFamily={textPr.fontFamily}
                fontSize={textPr.fontSize}
                fontWeight={weight}
                fontStyle={fontStyle}
                fill={textPr.color}
                textDecoration={textPr.textDecorationLine}
            >
                {text}
            </text>
        );
    }

    private renderImage(images: any[]): any {
        if (!images || !images.length) {
            return null;
        }
        const { pageIndex, nHeaderFooter } = this.props;
        const host = this.props.host;
        const editorContainer = host.getContainer();
        const documentCore = host.documentCore;
        const handleRefresh = host.handleRefresh;
        const desc = (nHeaderFooter === 1 || nHeaderFooter === 2) ? '-hdrFtr-' : '-document-';
        let mask;
        const cellId = this.props.cellId;
        if (cellId != null) {
            const headerFooter = (nHeaderFooter === 1) ? '-header' : ((nHeaderFooter === 2) ? '-footer' : '');
            mask = 'url(#mask' + cellId + headerFooter + ')';
        }
        return images.map((item, index) => {
            return (
                <Image
                    key={item.id}
                    id={pageIndex + desc + item.id}
                    item={item}
                    editorContainer={editorContainer}
                    documentCore={documentCore}
                    handleRefresh={handleRefresh}
                    host={host}
                    mask={mask}
                />
            );
        });
    }

    private getTextDecorationLine(datas: IDcoumentPortionTextDecoration[]): any {
        const scale = this.scale;
        // const datas = para.textDecoration;
        if (!datas || !datas.length) {
            return null;
        }
        let mask;
        const cellId = this.props.cellId;
        const nHeaderFooter = this.props.nHeaderFooter;
        if (cellId != null) {
            const headerFooter = (nHeaderFooter === 1) ? '-header' : ((nHeaderFooter === 2) ? '-footer' : '');
            mask = 'url(#mask' + cellId + headerFooter + ')';
        }
        // console.log(datas)
        return datas.map((data, index) => {
            const y = numtoFixed2((data.y) * scale);
            if ( 0 === data.style || null == data.style ) {
                return (
                        <line
                            key={index}
                            x1={numtoFixed2(data.x * scale)}
                            y1={y}
                            x2={numtoFixed2((data.x + data.width) * scale)}
                            y2={y}
                            style={{ stroke: data.color, strokeWidth: '0.5px' }}
                            clipPath={mask}
                        />
                    );
            } else if ( 1 === data.style ) {
                return (
                    <g key={index}>
                        <line
                            key={index}
                            x1={numtoFixed2(data.x * scale)}
                            y1={y}
                            x2={numtoFixed2((data.x + data.width) * scale)}
                            y2={y}
                            style={{ stroke: data.color, strokeWidth: '0.5px' }}
                            clipPath={mask}
                        />
                        <line
                            key={index + 1}
                            x1={numtoFixed2(data.x * scale)}
                            y1={numtoFixed2((data.y + 2) * scale)}
                            x2={numtoFixed2((data.x + data.width) * scale)}
                            y2={numtoFixed2((data.y + 2) * scale)}
                            style={{ stroke: data.color, strokeWidth: '0.5px' }}
                            clipPath={mask}
                        />
                    </g>
                );
            } else if ( 2 === data.style ) {
                return (
                    <g key={index}>
                        <line
                            key={index}
                            x1={numtoFixed2(data.x * scale)}
                            y1={y}
                            x2={numtoFixed2((data.x + data.width) * scale)}
                            y2={y}
                            style={{ stroke: data.color, strokeWidth: '0.5px' }}
                            clipPath={mask}
                        />
                        <line
                            key={index + 1}
                            x1={numtoFixed2(data.x * scale)}
                            y1={numtoFixed2((data.y + 2) * scale)}
                            x2={numtoFixed2((data.x + data.width) * scale)}
                            y2={numtoFixed2((data.y + 2) * scale)}
                            style={{ stroke: data.color, strokeWidth: '0.5px' }}
                            clipPath={mask}
                        />
                        <line
                            key={index + 2}
                            x1={numtoFixed2(data.x * scale)}
                            y1={numtoFixed2((data.y + 4) * scale)}
                            x2={numtoFixed2((data.x + data.width) * scale)}
                            y2={numtoFixed2((data.y + 4) * scale)}
                            style={{ stroke: data.color, strokeWidth: '0.5px' }}
                            clipPath={mask}
                        />
                    </g>
                );
            } else if (99 === data.style) {
                // 波浪线渲染 - 使用SVG path绘制波浪效果
                const waveWidth = 4; // 波浪宽度
                const waveHeight = 1; // 波浪高度
                const startX = data.x * scale; // 保持数字类型
                const endX = (data.x + data.width) * scale; // 保持数字类型
                const totalWidth = endX - startX;
                const waveCount = Math.ceil(totalWidth / waveWidth);
                
                let pathData = `M ${numtoFixed2(startX)} ${y}`;
                for (let i = 0; i < waveCount; i++) {
                    const x1 = startX + i * waveWidth;
                    const x2 = x1 + waveWidth / 2;
                    const x3 = Math.min(x1 + waveWidth, endX);
                    if (x3 > x1) {
                        pathData += ` Q ${numtoFixed2(x2)} ${numtoFixed2(parseFloat(y) - waveHeight)} ${numtoFixed2(x3)} ${y}`;
                    }
                }
                
                return (
                    <path
                        key={index}
                        d={pathData}
                        stroke="red"
                        strokeWidth="0.5px"
                        fill="none"
                        clipPath={mask}
                    />
                );
            }
        });
    }

    private getBackgroundContent(para: IDocumentParagraph): any {
        const scale = this.scale;
        const datas = para.backgroundColor;
        if (!datas || !datas.length) {
            return null;
        }
        let mask;
        const cellId = this.props.cellId;
        if (cellId) {
            const nHeaderFooter = this.props.nHeaderFooter;
            const headerFooter = (nHeaderFooter === 1) ? '-header' : ((nHeaderFooter === 2) ? '-footer' : '');
            mask = 'url(#mask' + cellId + headerFooter + ')';
        }
        return datas.map((data, index) => {
            if (data.backgroundColor === '#ffffff') {
                return;
            }
            return (
                <rect
                    width={numtoFixed2(data.width * scale)}
                    stroke={data.backgroundColor}
                    key={index}
                    clipPath={mask}
                    height={numtoFixed2(data.height * scale)}
                    x={numtoFixed2(data.x * scale)}
                    y={numtoFixed2(data.y * scale)} // {(data.y + 3) * scale}
                    fill={data.backgroundColor}
                />
            );
        });
    }

    private getSectionSelection = (
        para: IDocumentParagraph,
        type: RenderSectionBackgroundType,
    ) => {
        if (!para.lines || !para.lines.length) {
            return null;
        }
        const scale = this.scale;
        const bNewControl = RenderSectionBackgroundType.NewControlCursorIn === type
                            || RenderSectionBackgroundType.NewControlFocus === type;
        return para.lines.map((line, lineIndex) => {
            
            if (para.startLine <= lineIndex && lineIndex <= para.endLine) {
                
                const { id, bottom, ranges, top } = line;
                const props = {
                    height: (bottom - top) * scale,
                    width: bNewControl ? 0 : ranges[0].width, //  would be overridden
                    x: ranges[0].xVisible, // would be overridden
                    y: top * scale, // (top + 3) * scale,
                };
                const className = `selection ${type}-${id}`;
                return (
                    <rect
                        key={id + 'para'}
                        className={className}
                        line-key={id}
                        pointerEvents={'none'}
                        {...props}
                    >
                        {id}
                    </rect>
                );
                
            }
            lineIndex++;
            
        });
    }

    private renderParagraph(item: IDocumentParagraph, id?: any): any {
        if (!item) {
            return null;
        }
        SPELL_CHECK.add({ref: this, content: item, pageIndex: this.props.pageIndex, option: this.props.option});
        /* IFTRUE_WATER */
        let tmpMark = null;
        if (!this.props.bShowContent || !this.props.host.documentCore.getCoreBGen() ||
             this.props.host.documentCore.getViewMode() === ViewModeType.WebView) {
            this._strChange = -1;
            this._str = null;
        } else {
            const curChange = this.props.host.documentCore.corePositionChange();
            if (curChange !== this._strChange || !this._str) {
                this._strChange = curChange;
                this._str = this.props.host.documentCore.getCorePosition();
            }
            tmpMark = <RandomMarkDom strTmp={this._str} />;
        }
        /* FITRUE_WATER */
        return (
            <React.Fragment>
                {this.renderTextBorder(item.points, item.id)}
                <Paragraph
                    id={item.id}
                    cellId={id}
                    key={item.index + item.id}
                    index={item.index}
                    pageIndex={this.props.pageIndex}
                    content={item.content}
                    lines={item.lines}
                    startLine={item.startLine}
                    endLine={item.endLine}
                    documentCore={this.props.host.documentCore}
                    option={this.props.option}
                    nHeaderFooter={this.props.nHeaderFooter}
                />
                {/*IFTRUE_WATER tmpMark FITRUE_WATER*/}
            </React.Fragment>
        );
    }

    private renderTextBorder(points: any[], id: any): any {
        // const { points } = this.props;
        if (!points || !points.length) {
            return null;
        }

        let x;
        let y;
        let height;
        let width;
        let bLine = false;
        const cellRect = this.props.cellRect;
        if (cellRect) {
            x = cellRect.x;
            y = cellRect.y;
            height = cellRect.height;
            width = cellRect.width;
        }

        return points.map((point, index) => {
            const style = {
                fill: 'transparent',
                stroke: null,
                strokeWidth: '1px'
            };
            let path: string = '';
            let currentColor: string = '#000';
            point.forEach((item) => {
                if (path === '' && item.bTextBorderRed === true) {
                    currentColor = 'red';
                }

                if (!cellRect ||
                    (cellRect && x <= item.x && item.x <= x + width && y <= item.y + 4 && item.y + 4 <= y + height)) {
                    path += numtoFixed2(item.x) + ',' + numtoFixed2(item.y + 4) + ' ';
                } else if (!bLine && cellRect) {
                    bLine = true;
                }
            });
            style.stroke = currentColor;
            if (!bLine) {
                return (
                    <polygon key={index + id} points={path} style={style} />
                );
            } else {
                return (
                    <polyline key={index + id} points={path} style={style} />
                );
            }
        });
    }
}
