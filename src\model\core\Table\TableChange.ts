import { ChangeBaseObjectProperty, ChangeBaseProperty, ChangeBaseContent,
    ChangeBase, ChangeBaseBoolProperty } from '../HistoryChange';
import { HistroyItemType } from '../HistoryDescription';
// import { Table } from '../Table';
import { DocumentBorder } from '../Style';
import { GenericBox } from 'src/common/commonDefines';
import { TableMeasurement, TableProperty } from '../TableProperty';
import { TableManager } from './TableManager';
import { TableBase } from '../TableBase';

export class ChangeTableBorderTop extends ChangeBaseObjectProperty {
    private type: HistroyItemType;

    constructor(changeClass: TableBase, old: DocumentBorder, news: DocumentBorder, color?: any) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.TableBorderTop;
    }

    public setValue(value: DocumentBorder): void {
        const table = this.changeClass as TableBase;
        if ( table ) {
            table.property.tableBorders.top = value;
        }
    }
}

export class ChangeTableBorderBottom extends ChangeBaseObjectProperty {
    private type: HistroyItemType;

    constructor(changeClass: TableBase, old: DocumentBorder, news: DocumentBorder, color?: any) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.TableBorderBottom;
    }

    public setValue(value: DocumentBorder): void {
        const table = this.changeClass as TableBase;
        if ( table ) {
            table.property.tableBorders.bottom = value;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeTableBorderLeft extends ChangeBaseObjectProperty {
    private type: HistroyItemType;

    constructor(changeClass: TableBase, old: DocumentBorder, news: DocumentBorder, color?: any) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.TableBorderLeft;
    }

    public setValue(value: DocumentBorder): void {
        const table = this.changeClass as TableBase;
        if ( table ) {
            table.property.tableBorders.left = value;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeTableBorderRight extends ChangeBaseObjectProperty {
    private type: HistroyItemType;

    constructor(changeClass: TableBase, old: DocumentBorder, news: DocumentBorder, color?: any) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.TableBorderRight;
    }

    public setValue(value: DocumentBorder): void {
        const table = this.changeClass as TableBase;
        if ( table ) {
            table.property.tableBorders.right = value;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeTableBorderInsideH extends ChangeBaseObjectProperty {
    private type: HistroyItemType;

    constructor(changeClass: TableBase, old: DocumentBorder, news: DocumentBorder, color?: any) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.TableBorderInsideH;
    }

    public setValue(value: DocumentBorder): void {
        const table = this.changeClass as TableBase;
        if ( table ) {
            table.property.tableBorders.insideH = value;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeTableBorderInsideV extends ChangeBaseObjectProperty {
    private type: HistroyItemType;

    constructor(changeClass: TableBase, old: DocumentBorder, news: DocumentBorder, color?: any) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.TableBorderInsideV;
    }

    public setValue(value: DocumentBorder): void {
        const table = this.changeClass as TableBase;
        if ( table ) {
            table.property.tableBorders.insideV = value;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeTableDefaultCellMargins extends ChangeBaseProperty {
    private type: HistroyItemType;

    constructor(changeClass: TableBase, old: GenericBox<TableMeasurement>,
                news: GenericBox<TableMeasurement>, color?: any) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.TableCellMargins;
    }

    public setValue(value: GenericBox<TableMeasurement>): void {
        const table = this.changeClass as TableBase;

        if ( table ) {
            table.property.tableCellMargin.top = value.top;
            table.property.tableCellMargin.bottom = value.bottom;
            table.property.tableCellMargin.left = value.left;
            table.property.tableCellMargin.right = value.right;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeTableAddRow extends ChangeBaseContent {

    constructor( changeClass: TableBase, pos: number, items: any, bAdd?: boolean ) {
        super(changeClass, pos, items, true);
        this.type = HistroyItemType.TableRowAddRow;
    }

    public undo(): void {
        if ( 0 >= this.items.length ) {
            return;
        }

        const table = this.changeClass as TableBase;
        if ( table ) {
            table.content[this.position].setIndex(-1);
            table.content.splice(this.position, 1);
            table.tableRowsBottom.splice(this.position, 1);
            table.rowsInfo.splice(this.position, 1);
            table.reIndex();
            table.checkCurCell();
        }
    }

    public redo(): void {
        if ( 0 >= this.items.length ) {
            return;
        }

        const table = this.changeClass as TableBase;
        if ( table ) {
            table.content.splice(this.position, 0, this.items[0]);
            table.tableRowsBottom.splice(this.position, 0);
            table.rowsInfo.splice(this.position, 0);
            table.reIndex();
            table.checkCurCell();
        }
    }

    public createReverseChange(): ChangeTableRemoveRow {
        return this.createReverseChangeBase(ChangeTableRemoveRow);
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeTableRemoveRow extends ChangeBaseContent {

    constructor( changeClass: TableBase, pos: number, items: any, bAdd?: boolean ) {
        super(changeClass, pos, items, true);
        this.type = HistroyItemType.TableRowRemoveRow;
    }

    public undo(): void {
        if ( 0 >= this.items.length ) {
            return;
        }

        const table = this.changeClass as TableBase;
        if ( table ) {
            table.content.splice(this.position, 0, this.items[0]);
            table.tableRowsBottom.splice(this.position, 0);
            table.rowsInfo.splice(this.position, 0);
            table.reIndex();
            table.checkCurCell();
        }
    }

    public redo(): void {
        if ( 0 >= this.items.length ) {
            return;
        }

        const table = this.changeClass as TableBase;
        if ( table ) {
            table.content[this.position].setIndex(-1);
            table.content.splice(this.position, 1);
            table.tableRowsBottom.splice(this.position, 1);
            table.rowsInfo.splice(this.position, 1);
            table.reIndex();
            table.checkCurCell();
        }
    }

    public createReverseChange(): ChangeTableAddRow {
        return this.createReverseChangeBase(ChangeTableAddRow);
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeTableAddTableName extends ChangeBase {
    private type: HistroyItemType = null;

    constructor( changeClass: TableBase ) {
        super(changeClass);
        this.type = HistroyItemType.TableAddTableName;
    }

    public undo(): void {
        const table = this.changeClass as TableBase;
        const tableManager = table ? table.getTableManager() : null;

        if ( tableManager ) {
            tableManager.removeTableNameByTable(table);
        }
    }

    public redo(): void {
        const table = this.changeClass as TableBase;
        const tableManager = table ? table.getTableManager() : null;

        if ( tableManager ) {
            tableManager.addTableNameByTable(table);
        }
    }
}

// export class ChangeTableRemoveTableName extends ChangeBase {
//     private type: HistroyItemType = null;
// tslint:disable-next-line: max-classes-per-file
export class ChangeTableRemoveTableNames extends ChangeBaseContent {
    constructor( changeClass: TableManager, pos: number, items: TableBase[], bAdd?: boolean ) {
        super(changeClass, pos, items, false);
        this.type = HistroyItemType.TableRemoveTableNames;
    }

    public undo(): void {
        const tableManager = this.changeClass as TableManager;
        // const tableManager = table ? table.getTableManager() : null;

        if ( tableManager && this.items && 0 < this.items.length ) {
            this.items.forEach((table) => {
                tableManager.addTableNameByTable(table);
            });
        }
    }

    public redo(): void {
        const tableManager = this.changeClass as TableManager;
        // const tableManager = table ? table.getTableManager() : null;

        if ( tableManager && this.items && 0 < this.items.length ) {
            this.items.forEach((table) => {
                tableManager.removeTableNameByTable(table);
            });
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeTableFixedRowHeight extends ChangeBaseBoolProperty {
    private type: HistroyItemType;

    constructor(changeClass: TableProperty, old: boolean, news: boolean, color?: any) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.TableFixedRowHeight;
    }

    public setValue(value: boolean): void {
        const tableProp = this.changeClass;
        if ( tableProp ) {
            tableProp.bFixedRowHeight = value;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeTableFixedColWidth extends ChangeBaseBoolProperty {
    private type: HistroyItemType;

    constructor(changeClass: TableProperty, old: boolean, news: boolean, color?: any) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.TableFixedColWidth;
    }

    public setValue(value: boolean): void {
        const tableProp = this.changeClass;
        if ( tableProp ) {
            tableProp.bFixedColWidth = value;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeTableReadOnly extends ChangeBaseBoolProperty {
    private type: HistroyItemType;

    constructor(changeClass: TableProperty, old: boolean, news: boolean, color?: any) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.TableReadOnly;
    }

    public setValue(value: boolean): void {
        const tableProp = this.changeClass;
        if ( tableProp ) {
            tableProp.bReadOnly = value;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeTableCanAddRow extends ChangeBaseBoolProperty {
    private type: HistroyItemType;

    constructor(changeClass: TableProperty, old: boolean, news: boolean, color?: any) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.TableCanAddRow;
    }

    public setValue(value: boolean): void {
        const tableProp = this.changeClass;
        if ( tableProp ) {
            tableProp.bCanAddRow = value;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeTableCanDeleteRow extends ChangeBaseBoolProperty {
    private type: HistroyItemType;

    constructor(changeClass: TableProperty, old: boolean, news: boolean, color?: any) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.TableCanDeleteRow;
    }

    public setValue(value: boolean): void {
        const tableProp = this.changeClass;
        if ( tableProp ) {
            tableProp.bCanDeleteRow = value;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeTableDeleteProtect extends ChangeBaseBoolProperty {
    private type: HistroyItemType;

    constructor(changeClass: TableProperty, old: boolean, news: boolean, color?: any) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.TableDeleteProtect;
    }

    public setValue(value: boolean): void {
        const tableProp = this.changeClass;
        if ( tableProp ) {
            tableProp.bDeleteProtect = value;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeTableTableGrid extends ChangeBaseProperty {
    private type: HistroyItemType;

    constructor(changeClass: TableBase, old: number[], news: number[], color?: any) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.TableTableGrid;
    }

    public setValue(value: number[]): void {
        const table = this.changeClass as TableBase;
        if ( table ) {
            table.tableGrid = value;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeTableReviewType extends ChangeBaseObjectProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: TableBase, old: any, news: any, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.TableContentReviewType;
    }

    public setValue( value: any ): void {
        const table = this.getClass();
        if ( table ) {
            table.reviewType = value.reviewType;
            table.reviewInfo = value.reviewInfo;
            table.updateTrackRevisions();
        }
    }
}
