import { XmlAttributeComponent, XmlComponent } from '../../xml-components';
import { Run } from '../run';

export interface IRevisionAttributesProperties {
    id: string;
    des: string;
    line: string;
    color: string;
    date: string;
    author: string;
    level: string;
    savedCount: string;
    record?: string;
    firstDate?: string;
}

class RevisionAttributes extends XmlAttributeComponent<IRevisionAttributesProperties> {
    protected xmlKeys: any = {
        level: 'level',
        line: 'line',
        color: 'color',
        des: 'des',
        date: 'date',
        author: 'author',
        id: 'id',
        savedCount: 'savedCount',
        record: 'record',
        firstDate: 'firstDate',
    };
}

export class Revision extends XmlComponent {
    // protected readonly properties: RunProperties;
    // protected properties: RevisionProperties;

    constructor(bInsert: boolean, attrs: IRevisionAttributesProperties) {
        const tag = bInsert ? 'w:ins' : 'w:del';
        super(tag);

        this.root.push(new RevisionAttributes(attrs));
    }

    public addRun(run: Run): void {
      this.root.push(run);
    }

    public addSubRevision(attrs: Revision): void {
        this.root.push(attrs);
    }
}
