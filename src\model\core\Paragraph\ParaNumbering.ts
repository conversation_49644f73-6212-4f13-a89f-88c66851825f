import TextProperty from '../TextProperty';
import { measure } from '../util';
import { ParaElementBase } from './ParaElementBase';

// interface IInternal {
//     sourceWidth: number;
//     sourceNumLvl?: number;
//     sourceNumId?: number;
//     sourceCalcValue?: string;
//     sourceNumInfo?: any;

//     finalWidth?: number;
//     finalNumLvl?: number;
//     finalNumId?: number;
//     finalCalcValue?: string;
//     finalNumInfo?: any;
// }
export class ParaNumbering  extends ParaElementBase {
    private height: number;
    private _width: number;
    // private widthNum: number;
    // private widthSuff: number;
    // private internal: IInternal;
    private textPr: TextProperty; // 排版后才能使用, 目前用于UI渲染
    constructor() {
        super();
    }

    public copy(bFlag: boolean): ParaNumbering {
        const num = new ParaNumbering();
        num.width = this._width;
        num.height = this.height;
        num.widthVisible = this.width;
        return num;
    }

    public get textProperty(): TextProperty {
        return this.textPr;
    }

    public measure(textPr: TextProperty, text?: string): number {
        if (!text) {
            this.content = '';
            this.width = 0;
            this._width = 0;
            this.widthVisible = this.width;
            return 0;
        }

        const ms = measure(text, textPr);
        if (!ms || !ms.length) {
            this.content = '';
            this.width = 0;
            this.widthVisible = this.width;
            return 0;
        }

        let height: number = 0;
        let width: number = 0;
        ms.forEach((m) => {
            width += m.width;
            if (m.height > height) {
                height = m.height;
            }
        });
        this._width = width;
        this.width = 0;
        this.height = height;
        this.widthVisible = 0;
        this.content = text;
        this.textPr = textPr;
        return height;
    }
}
