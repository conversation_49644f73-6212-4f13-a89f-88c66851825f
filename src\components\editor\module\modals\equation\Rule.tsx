import * as React from 'react';
import Dialog from '../../../ui/Dialog';
import Input from '../../../ui/Input';
import Button from '../../../ui/Button';
// import { message } from '../../../../../common/Message';
import { ParaEquation } from '../../../../../model/core/Paragraph/ParaDrawing';

interface IDialogProps {
    documentCore: any;
    visible: boolean;
    id: string;
    equation: ParaEquation;
    close: (name: string | number, bRefresh?: boolean) => void;
}

interface IState {
    bRefresh: boolean;
}

export default class Rule extends React.Component<IDialogProps, {}> {
    private visible: boolean;
    private data: number;
    private oldData: number;
    private equationElemDom: HTMLElement;

    constructor(props: any) {
        super(props);
        this.visible = this.props.visible;
        this.data = 0;
    }

    public render(): any {
        return (
            <Dialog
                id={this.props.id}
                preventDefault={true}
                visible={this.visible}
                open={this.open}
                footer={this.renderFooter()}
                width={400}
                title={'医学表达式'}
            >
                <div className='tooth-bit-map'>
                    <div className='editor-line'>
                        <div className='w-70'>疼痛指数</div>
                        <div className='right-auto'>
                            <Input
                                min={0}
                                max={10.00001}
                                type='number'
                                name='data'
                                onChange={this.onChange}
                                value={this.data}
                            />
                        </div>
                    </div>

                </div>

            </Dialog>
        );
    }

    public componentDidMount(): void {
        //
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        this.visible = nextProps.visible;
    }

    private renderFooter(): any {
        return (
            <span>
                <Button onClick={this.close}>取消</Button>
                <Button type='primary' onClick={this.confirm}>确认</Button>
            </span>
        );
    }

    private open = (): void => {
        const equation = this.props.equation;
        // let equationElemDom: HTMLElement;
        this.data = 0;
        const svg = equation.equationElem;
        if (svg) {
            const equationElemDom = this.equationElemDom = new DOMParser().parseFromString(
                svg,
                'text/xml',
            ).documentElement;
            const text = equationElemDom.querySelector('text');
            if (text) {
                this.oldData = this.data = +text.innerHTML.trim();
            }
        }

        this.setState({});
    }

    private onChange = (value: any, name: string): void => {
        this.data = value;
        this.setState({});
    }

    private close = (bRefresh: boolean = false): void => {
        this.props.close(this.props.id, bRefresh);
        this.visible = false;
        this.setState({});
    }

    private confirm = (): void => {
        if (Math.abs(this.data - this.oldData) < 0.01) {
            this.close();
            return;
        }
        const documentCore = this.props.documentCore;
        const dom = this.equationElemDom;
        const g = dom.querySelector('g');
        (g.children[1] as HTMLElement).style.transform = 'translate(' + (- 3.4 + (30 - 0.34 ) * (+this.data)) + 'px, 0px)';
        g.children[2].innerHTML = this.data + '';

        // 修改属性
        const svgStr = dom.outerHTML;
        const drawObj = documentCore.getDrawingObjects();
        const svgConvertedURI = drawObj.convertSVGToImageString(svgStr);
        // drawObj.setDrawingProp(this.props.equation.name, {
        documentCore.setDrawingProp(this.props.equation.name, {
            width: this.props.equation.width,
            src: svgConvertedURI,
            equationElem: svgStr,
        });
        this.close(true);
    }

}
