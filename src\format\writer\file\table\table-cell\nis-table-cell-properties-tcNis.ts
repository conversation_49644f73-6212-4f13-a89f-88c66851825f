// tslint:disable-next-line: max-line-length
import { addNode } from '@/common/struct/write';
import { CellTimeFormat, CodeValueItem, DataType, FONT_MAPPING, ICustomProps, INISProperty, isMacOs, NISTableCellType, parseBoolean, TABLE_CELL_PROPS_DEFAULT } from '../../../../../common/commonDefines';
import { customEncodeURIComponent } from '../../../../../common/commonMethods';
import { SerialNumber, Unit } from '../../paragraph/contentControl/contentControlElements';
import { CustomProperty } from '../../paragraph/contentControl/customProperty';
import { ListItems } from '../../paragraph/contentControl/listItems';
import { XmlComponent, XmlAttributeComponent } from '../../xml-components';

export interface ITcNisProperties {
    type: number;
}

// tslint:disable-next-line: max-classes-per-file
class ITcNisAttributes extends XmlAttributeComponent<ITcNisProperties> {
    protected xmlKeys: any = {
        type: 'type',
    };
}

// tslint:disable-next-line: max-classes-per-file
export class TcNis extends XmlComponent {

    private readonly customProperty: CustomProperty;
    private readonly listItems: ListItems;
    constructor(attrs: ITcNisProperties) {
        super('w:tcNis');
        if (attrs && NISTableCellType.Text !== attrs.type) {
            this.root.push(new ITcNisAttributes(attrs));
        }

        this.customProperty = new CustomProperty();
        this.listItems = new ListItems();
    }

    public addContent(nisProperty: INISProperty): TcNis {
        if (nisProperty == null) {
            return this;
        }
        const defaultTcNIS = TABLE_CELL_PROPS_DEFAULT.tcNis;
        const type = nisProperty.type;
        // common props
        // fontFamily?: string;
        // fontSize?: number;
        // paraSpacing?: LineSpacingType;
        // alignType?: AlignType;
        const {customProperty, bDiagonalLine, fontFamily, fontSize, paraSpacing, alignType, range,
            bShowCodeAndValue, serialNumber, codeLabel, valueLabel, gridLine} = nisProperty;
        if (fontFamily != null && defaultTcNIS.fontFamily !== fontFamily) {
            let curFont = fontFamily;
            if (isMacOs && FONT_MAPPING['宋体'].mac === curFont) {
                curFont = FONT_MAPPING['宋体'].windows;
            }
            this.root.push(new FontFamily(curFont));
        }
        if (fontSize != null && defaultTcNIS.fontSize !== fontSize) {
            this.root.push(new FontSize(fontSize + ''));
        }
        if (paraSpacing != null && defaultTcNIS.paraSpacing !== paraSpacing) {
            this.root.push(new ParaSpacing(paraSpacing + ''));
        }

        if (gridLine != null && gridLine.visible === true) {
            addNode('gridLine', JSON.stringify(gridLine), this.root);
        }

        if (alignType != null && defaultTcNIS.alignType !== alignType) {
            this.root.push(new AlignType(alignType + ''));
        }
        if (range != null && defaultTcNIS.range !== range) {
            this.root.push(new Range(range + ''));
        }
        if (serialNumber != null) {
            this.root.push(new SerialNumber(serialNumber + ''));
        }

        // if (customProperty instanceof Map && customProperty.size > 0) { // default empty map
        if (customProperty != null) {
            // this.root.push(new CustomProperty(JSON.stringify(customProperty)));

            // change customProperty to Map()
            const customPropertyMap = this.addCustomProps(customProperty); //

            // add customControlElements as well
            this.customProperty.addCustomProperties(customPropertyMap);
            if (customPropertyMap.size > 0) {
                this.root.push(this.customProperty);
            }
        }

        if (bDiagonalLine != null && defaultTcNIS.bDiagonalLine !== bDiagonalLine) {
            const result = bDiagonalLine === true ? '1' : '0';
            this.root.push(new BDiagonalLine(result));
        }

        if (bShowCodeAndValue != null) {
            const result = bShowCodeAndValue === true ? '1' : '0';
            this.root.push(new BShowCodeAndValue(result));
        }

        // type-specific props
        switch (type) {
            case NISTableCellType.Date: {
                const {dateBoxFormat, customFormat, dateTime, time, text, hideDateText} = nisProperty;
                if (dateBoxFormat != null && defaultTcNIS.dateBoxFormat !== dateBoxFormat) {
                    this.root.push(new DateBoxFormat(dateBoxFormat + ''));
                }
                if (customFormat != null) {
                    this.root.push(new CustomFormat(JSON.stringify(customFormat)));
                }
                if (dateTime != null) {
                    this.root.push(new DateTime(dateTime));
                }
                if (time != null) {
                    this.root.push(new Time(time + ''));
                }
                if (text != null) {
                    this.root.push(new Text(text));
                }
                if (hideDateText != null && hideDateText !== defaultTcNIS.hideDateText) {
                    const result = hideDateText === true ? '1' : '0';
                    this.root.push(new HideDateText(result));
                }
                break;
            }
            case NISTableCellType.Time: {
                const {timeFormat, cellText} = nisProperty;
                if (timeFormat !== defaultTcNIS.timeFormat) {
                    this.root.push(new TimeFormat(timeFormat));
                }

                if (cellText && !/^--[\s\S]*--$/.test(cellText)) {
                    this.root.push(new CellText(cellText));
                }
                break;
            }
            case NISTableCellType.BloodPressure: {
                const {bloodPressureFormat, cellText} = nisProperty;
                this.root.push(new BPFormat(bloodPressureFormat));
                if (cellText && !/^---[\s\S]*---$/.test(cellText)) {
                    this.root.push(new CellText(cellText));
                }
                break;
            }
            case NISTableCellType.List: {
                const {items, prefixContent, selectPrefixContent, separator, bRetrieve,
                    bShowValue, bCheckMultiple, selectType} = nisProperty;
                if (items != null) {
                    this.addItemList(items);
                    // this.root.push(new Items(JSON.stringify(items)));
                } 
                // else {
                //     // empty dropdown still need to preserve
                //     this.addItemList([]);
                // }
                if (prefixContent != null) {
                    this.root.push(new PrefixContent(prefixContent));
                }
                if (selectPrefixContent != null) {
                    this.root.push(new SelectPrefixContent(selectPrefixContent));
                }
                if (separator != null && defaultTcNIS.separator !== separator) {
                    this.root.push(new Separator(separator));
                }
                if (bRetrieve != null && defaultTcNIS.bRetrieve !== bRetrieve) {
                    const result = bRetrieve === true ? '1' : '0';
                    this.root.push(new BRetrieve(result));
                }
                if (bShowValue != null && bShowValue !== defaultTcNIS.bShowValue) {
                    const result = bShowValue === true ? '1' : '0';
                    this.root.push(new BShowValue(result));
                }
                if (bCheckMultiple != null && bCheckMultiple !== defaultTcNIS.bCheckMultiple) {
                    const result = bCheckMultiple === true ? '1' : '0';
                    this.root.push(new BCheckMultiple(result));
                }
                if (selectType != null && selectType !== defaultTcNIS.selectType) {
                    this.root.push(new SelectType(selectType + ''));
                }

                if (codeLabel != null) {
                    this.root.push(new CodeLabel(codeLabel));
                }

                if (valueLabel != null) {
                    this.root.push(new ValueLabel(valueLabel));
                }
                break;
            }
            case NISTableCellType.Quick: {
                const {items, bRetrieve} = nisProperty;
                if (items != null) {
                    this.addItemList(items);
                    // this.root.push(new Items(JSON.stringify(items)));
                }
                //  else {
                //     // empty dropdown still need to preserve
                //     this.addItemList([]);
                // }
                if (bRetrieve != null) {
                    const result = bRetrieve === true ? '1' : '0';
                    this.root.push(new BRetrieve(result));
                }
                break;
            }
            case NISTableCellType.Number: {
                const {items, maxValue, minValue, precision, minWarn, maxWarn, unit} = nisProperty;
                if (items != null) {
                    this.addItemList(items);
                    // this.root.push(new Items(JSON.stringify(items)));
                }
                // else {
                //     // empty dropdown still need to preserve
                //     this.addItemList([]);
                // }
                if (null != maxValue) {
                    this.root.push(new MaxValue(maxValue + ''));
                }
                if (null != minValue) {
                    this.root.push(new MinValue(minValue + ''));
                }
                if (null != precision) {
                    this.root.push(new Precision(precision + ''));
                }

                if (null != minWarn) {
                    this.root.push(new MinWarn(minWarn + ''));
                }
                if (null != maxWarn) {
                    this.root.push(new MaxWarn(maxWarn + ''));
                }

                if (unit) {
                    this.root.push(new Unit(unit));
                }
                break;
            }
            default: {
                break;
            }
        }
        return this;
    }

    private addCustomProps(props: ICustomProps[], bClearItems: boolean = true): Map<string, ICustomProps> {
        const customProperty: Map<string, ICustomProps> = new Map();
        if (!props) {
            return customProperty;
        }
        if (bClearItems) {
            customProperty.clear();
        }
        props.forEach((prop) => {
            if (prop.value === undefined) {
              prop.value = '';
            }
            customProperty.set(prop.name, prop);
            switch (prop.type) {
                case DataType.Boolean:
                    // prop.targetValue = prop.value ? parseBoolean(prop.value) : undefined;
                    prop.targetValue = prop.value ? parseBoolean(prop.value) : '';
                    break;
                case DataType.Number:
                    // prop.targetValue = prop.value ? Number(prop.value) : undefined;
                    prop.targetValue = prop.value ? Number(prop.value) : '';
                    break;
                default:
                    prop.targetValue = prop.value;
            }
        });
        return customProperty;
    }

    private addItemList(itemList: CodeValueItem[]): TcNis {
        this.root.push(this.listItems);

        // add list items
        this.listItems.addListItems(itemList);
        return this;
      }

}

// tslint:disable-next-line: max-classes-per-file
export class DateBoxFormat extends XmlComponent {
    constructor(val: string) {
        super('dateBoxFormat');
        this.root.push(val);
    }
}

// tslint:disable-next-line: max-classes-per-file
export class DateTime extends XmlComponent {
    constructor(val: string) {
        super('dateTime');
        this.root.push(val);
    }
}

// tslint:disable-next-line: max-classes-per-file
export class FontFamily extends XmlComponent {
    constructor(val: string) {
        super('fontFamily');
        this.root.push(val);
    }
}

// tslint:disable-next-line: max-classes-per-file
export class FontSize extends XmlComponent {
    constructor(val: string) {
        super('fontSize');
        this.root.push(val);
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ParaSpacing extends XmlComponent {
    constructor(val: string) {
        super('paraSpacing');
        this.root.push(val);
    }
}

// tslint:disable-next-line: max-classes-per-file
export class AlignType extends XmlComponent {
    constructor(val: string) {
        super('alignType');
        this.root.push(val);
    }
}

// tslint:disable-next-line: max-classes-per-file
export class Range extends XmlComponent {
    constructor(val: string) {
        super('range');
        this.root.push(val);
    }
}

// tslint:disable-next-line: max-classes-per-file
export class Time extends XmlComponent {
    constructor(val: string) {
        super('time');
        this.root.push(val);
    }
}

// tslint:disable-next-line: max-classes-per-file
export class TimeFormat extends XmlComponent {
    constructor(val: string) {
        super('timeFormat');
        this.root.push(val || CellTimeFormat.HM);
    }
}

// tslint:disable-next-line: max-classes-per-file
export class CellText extends XmlComponent {
    constructor(val: string) {
        super('cellText');
        this.root.push(val);
    }
}

// tslint:disable-next-line: max-classes-per-file
export class BPFormat extends XmlComponent {
    constructor(val: string) {
        super('bpFormat');
        this.root.push(val);
    }
}

// tslint:disable-next-line: max-classes-per-file
export class Text extends XmlComponent {
    constructor(val: string) {
        super('text');
        this.root.push(customEncodeURIComponent(val));
    }
}

// tslint:disable-next-line: max-classes-per-file
export class HideDateText extends XmlComponent {
    constructor(val: string) {
        super('hideDateText');
        this.root.push(val);
    }
}

// tslint:disable-next-line: max-classes-per-file
export class CustomFormat extends XmlComponent {
    constructor(flag: string) {
        super('customFormat');
        this.root.push(flag);
    }
}

// tslint:disable-next-line: max-classes-per-file
export class BDiagonalLine extends XmlComponent {
    constructor(val: string) {
        super('bDiagonalLine');
        this.root.push(customEncodeURIComponent(val));
    }
}

// tslint:disable-next-line: max-classes-per-file
export class BShowCodeAndValue extends XmlComponent {
    constructor(val: string) {
        super('bShowCodeAndValue');
        this.root.push(customEncodeURIComponent(val));
    }
}

// tslint:disable-next-line: max-classes-per-file
export class PrefixContent extends XmlComponent {
    constructor(val: string) {
        super('prefixContent');
        this.root.push(customEncodeURIComponent(val));
    }
}

// tslint:disable-next-line: max-classes-per-file
export class SelectPrefixContent extends XmlComponent {
    constructor(val: string) {
        super('selectPrefixContent');
        this.root.push(customEncodeURIComponent(val));
    }
}

// tslint:disable-next-line: max-classes-per-file
export class Separator extends XmlComponent {
    constructor(val: string) {
        super('separator');
        this.root.push(customEncodeURIComponent(val));
    }
}

// tslint:disable-next-line: max-classes-per-file
export class BRetrieve extends XmlComponent {
    constructor(val: string) {
        super('bRetrieve');
        this.root.push(val);
    }
}

// tslint:disable-next-line: max-classes-per-file
export class BShowValue extends XmlComponent {
    constructor(val: string) {
        super('bShowValue');
        this.root.push(val);
    }
}

// tslint:disable-next-line: max-classes-per-file
export class BCheckMultiple extends XmlComponent {
    constructor(val: string) {
        super('bCheckMultiple');
        this.root.push(val);
    }
}

// tslint:disable-next-line: max-classes-per-file
export class SelectType extends XmlComponent {
    constructor(val: string) {
        super('selectType');
        this.root.push(val);
    }
}

// tslint:disable-next-line: max-classes-per-file
export class CodeLabel extends XmlComponent {
    constructor(val: string) {
        super('codeLabel');
        this.root.push(customEncodeURIComponent(val));
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ValueLabel extends XmlComponent {
    constructor(val: string) {
        super('valueLabel');
        this.root.push(customEncodeURIComponent(val));
    }
}

// tslint:disable-next-line: max-classes-per-file
export class MaxValue extends XmlComponent {
    constructor(val: string) {
        super('maxValue');
        this.root.push(val);
    }
}

// tslint:disable-next-line: max-classes-per-file
export class MinValue extends XmlComponent {
    constructor(val: string) {
        super('minValue');
        this.root.push(val);
    }
}

// tslint:disable-next-line: max-classes-per-file
export class MaxWarn extends XmlComponent {
    constructor(val: string) {
        super('maxWarn');
        this.root.push(val);
    }
}

// tslint:disable-next-line: max-classes-per-file
export class MinWarn extends XmlComponent {
    constructor(val: string) {
        super('minWarn');
        this.root.push(val);
    }
}

// tslint:disable-next-line: max-classes-per-file
export class Precision extends XmlComponent {
    constructor(val: string) {
        super('precision');
        this.root.push(val);
    }
}
