import {ParaElementType} from './ParagraphContent';
import TextProperty, { TextVertAlign } from '../TextProperty';
import {measure} from '../util';
import {ParaElementBase, RepalceText} from './ParaElementBase';
import {TEXT_SCARE_NUM} from '../../StyleProperty';

/**
 * 文本内容：中英文字符，标点，数字，空格
 */
export default class ParaFixedHeightText extends ParaElementBase {
    public bTextFlag: boolean;
    public height: number; // 特殊情况，需要设置高度
    constructor(text: string) {
        super();
        this.content = text;
        this.bTextFlag = false;
        this.type = ParaElementType.ParaFixedHeight;

    }

    /**
     * 拷贝函数，默认深拷贝，
     * UI显示拷贝时，只需要拷贝content，widthVisible
     * @param bForUI
     */
    public copy(bForUI: boolean = false): ParaFixedHeightText {
        const paraText = new ParaFixedHeightText(this.content);

        paraText.positionX = this.positionX;
        paraText.positionY = this.positionY;
        paraText.dy = this.dy;

        if ( true === bForUI) {
            paraText.type = this.type;
            paraText.width = this.width;
            paraText.height = this.height;
            paraText.widthVisible = this.widthVisible;
            paraText.bVisible = this.bVisible;
            paraText.bViewSecret = this.bViewSecret;
        }

        return paraText;
    }

    public measure( textPr: TextProperty ): number {
        this.widthVisible = this.width;
        return this.height;
    }
}
