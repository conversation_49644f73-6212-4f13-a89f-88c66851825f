import {ParagraphRecalculateStateWrap} from './ParagraphRecalculateStateWrap';
import {ParagraphRecalculateStateCounter} from './ParagraphRecalculateStateCounter';
import {ParagraphRecalculateStateAlign} from './ParagraphRecalculateStateAlign';
import Paragraph from '../Paragraph';
import {RecalcResultType} from '../Document';
import ParaLine, {ParaLineInfo} from './ParaLine';
import ParaProperty from './ParaProperty';
import ParaPage from './ParaPage';
import {DocumentContentType, AlignType} from '../Style';
import {getPxForMM} from '../util';
// import {parse as circularParse, stringify as circularStringify} from 'flatted/esm';
import { WasmInstance } from '../../../common/WasmInstance';
/* IFTRUE_WATER */
import MarkFactory from '@/common/MarkFactory';
import { getWaterText } from '@/common/commonDefines';

function getCode(): number {
  //暂时屏蔽对水印的验证
  return 1;
  const str = MarkFactory.getInstance()
                    .getContentStr();
  const bSuccessed = getWaterText((str || '')
                    .replace(/[\s\r\n]+/g, '')
                    .split('')
                    .map(((item) => item.charCodeAt(0))));
  if (!bSuccessed) {
    // console.error(str, code, doms)
    return -10000;
  }

  // const n = [];
  try {
    let code = 0;
    // tslint:disable-next-line: prefer-for-of
    // for (let i = 0; i < str.length; i++) {
    //   n.push(escape(str[i])
    //             .replace(/\%u/g, '/u')
    //               );
    // }

    // n?.forEach((value) => {
    //   code += parseInt(value.split('/u')[1], 16);
    // });
    for (let i = 0, length = str.length; i < length; i++) {
      code += str[i].charCodeAt(0);
    }

    // console.log(str, code, n, doms)
    return code;
  } catch (error) {
    return -10000;
  }
}
/* FITRUE_WATER */

/**
 * 段落!排版实现类
 */
export default class ParagraphRecalculate {
  public para: Paragraph;
  public mPRSW: ParagraphRecalculateStateWrap;
  public mPRSC: ParagraphRecalculateStateCounter;
  public mPRSA: ParagraphRecalculateStateAlign;
  private girdLineHeight: number; // 网格线高度
  // private bLastPara: boolean; // 网格线是否单元格最后一个段落

  constructor( para: Paragraph ) {
    this.para = para;
    this.girdLineHeight = null;
    // this.bLastPara = null;
    
    this.mPRSW = new ParagraphRecalculateStateWrap(para);
    this.mPRSC = new ParagraphRecalculateStateCounter();
    this.mPRSA = new ParagraphRecalculateStateAlign();
  }

  public recalculateParagraphPage(curPage: number, bHidden?: boolean): RecalcResultType {
    // const para = this.para;
    // if (para.isTableCellContent()) {
    //   const doc = para.parent as any;
    //   const parent = doc?.parent;
    //   if (parent) {
    //     const line: INISCellGridLine = parent.getGridLine();
    //     if (line && line.height > 0) {
    //       if (line.color === undefined) {
    //         const border = parent.getBorder(2);
    //         if (border) {
    //           line.color = border.color.toString();
    //           line.boderHeight = border.size;
    //         }
    //       }
    //       this.girdLineHeight = line.height + line.boderHeight;
    //       // this.bLastPara = para.index === doc.getContent().length - 1;
    //     }
    //   }
    // }
    this.para.curPos.line = -1;
    this.mPRSW.bHidden = bHidden ? bHidden : this.para.isHidden();

    const recalcResult = this.recalculatePage(curPage);
    return recalcResult;
  }

  /**
   * 尝试快速计算发生更改的段落，如果没有新增行和移动，不需要进行全文重排，否则将开始重新排版。
   * @param simpleChanges
   * @returns number： -1 如果需要重排，则返回当前重排的页面id，否则返回-1
   */
  public recalculateFastRange(paraPos: any): number {
    this.mPRSW.bHidden = this.para.isHidden();
    if (this.para.pages.length <= 0) {
      return -1;
    }

    // if (true === this.parent.Is_HdrFtr(false))
    //    return -1;

    // const portion = simpleChanges[0].changeClass;
    // const paraPos = portion.getParaPosBySimpleChanges(simpleChanges);
    if ( null == paraPos ) {
      return -1;
    }

    const line  = paraPos.line;
    // const range = paraPos.range;

    if ( this.para.lines.length <= paraPos.line ) {
      return -1;
    }

    const parent = this.para.parent;
    if ( null == parent ||
        (parent.isTableCellContent() && parent.getTableRow()
            .isTableHeader() && parent.getTableRow().table
                .isRepeatHeader()) ) {
      return -1;
    }

    // 如果在行中有一个PageBreak，开始通常的重新计算，或者它是一个空段落。
    // key-wasm by tinyzhi
    // if (this.para.lines[line].info & ParaLineInfo.BreakPage ||
    //  (this.para.lines[line].info & ParaLineInfo.Empty && this.para.lines[line].info & ParaLineInfo.End)) {
    let nResult;
    /* IFTRUE_NOWATER */
    if (true) {
      nResult = WasmInstance.instance._ParagraphRecalculate_recalculateFastRange(this.para.lines[line].info,
        ParaLineInfo.BreakPage, ParaLineInfo.Empty, ParaLineInfo.End);
    }
    /* FITRUE_NOWATER */
    /* IFTRUE_WATER */
    if (true) {
      const res = WasmInstance.paragraphRecalculateRecalculateFastRangeWithInfo(this.para.lines[line].info,
        ParaLineInfo.BreakPage, ParaLineInfo.Empty, ParaLineInfo.End, getCode() - this.para.lines[line].info);
      const textArray = res.split(',');
      const text = MarkFactory.getInstance();
      if (textArray[1] !== text.getContentStr()) {
        text?.setContenStr(textArray[1]);
      }
      nResult = Number.parseInt(textArray[0], 0);
      // console.log('返回值: ' + res, '   当前行号： ')
      WasmInstance.freeArray(res);
    }
    /* FITRUE_WATER */

    if ( Boolean(nResult)) {
      return  -1;
    }

    if ( 0 === line && undefined !== this.para.getSectPr() ) {
      return -1;
    }

    let prevLine = line;
    // let prevRange = range;
    this.mPRSW.setFast(true);

    while ( 0 <= prevLine ) {
      // prevRange--;

      // if ( 0 > prevRange ) {
        prevLine--;

        if ( 0 > prevLine ) {
          break;
        }

        // prevRange = this.para.lines[prevLine].ranges.length - 1;
      // }

        if ( true === this.para.isEmptyRange(prevLine) ) {
        continue;
       } else {
        break;
       }
    }

    if ( 0 > prevLine ) {
      prevLine = line;
      // prevRange = range;
    }

    let nextLine = line;
    const linesCount = this.para.lines.length;

    while ( nextLine <= linesCount - 1 ) {
      nextLine++;

      if ( nextLine > linesCount - 1 ) {
        break;
      }

      if ( true === this.para.isEmptyRange(nextLine)) {
        continue;
      } else {
        break;
      }
    }

    if ( nextLine > linesCount - 1 ) {
      nextLine = line;
    }
    // console.log(circularParse(circularStringify(portion))); // no width
    let curLine = prevLine;
    // let curRange = prevRange;
    let result;

    while ( ( curLine < nextLine ) || ( curLine === nextLine ) ) {
      const tempResult = this.recalculateFastRanges(curLine);
      // console.log(circularParse(circularStringify(portion))); // HAVE width

      if ( -1 === tempResult ) {
        this.mPRSW.setFast(false);
        return -1;
      }

      if ( curLine === line ) {
        result = tempResult;
      }

      curLine++;
    }

    this.para.curPos.line = -1;
    this.para.curPos.range = -1;

    this.mPRSW.setFast(false);
    return this.para.getAbsolutePage(result);
  }

  /**
   * 快速计算/排版当前整个段落
   */
  public recalculateFastWholeParagraph(): number[] {
    if ( 0 >= this.para.pages.length ) {
      return [];
    }

    const parent = this.para.parent;
    if ( null == parent ||
        (parent.isTableCellContent() && parent.getTableRow()
            .isTableHeader() && parent.getTableRow().table
                .isRepeatHeader()) ) {
      return [];
    }

    // if ( this.para.parent.isHeaderFooter(false) ) {
    //   return [];
    // }

    if ( 1 === this.para.pages.length ) {
      this.mPRSW.setFast(true);

      const oldBounds = this.para.pages[0].bounds;
      const nFastRecal = this.recalculateParagraphPage(0);

      this.mPRSW.setFast(false);

      // key-wasm by tinyzhi
      // if ( (nFastRecal & RecalcResultType.RecalResultNextElement)
      //    && 1 === this.para.pages.length
      //    && true === this.para.pages[0].bounds.compare(oldBounds)
      //    && 0 < this.para.lines.length ) {
      let nResult1;
      /* IFTRUE_NOWATER */
      if (true) {
        nResult1 = WasmInstance.instance._ParagraphRecalculate_recalculateFastWholeParagraph(nFastRecal,
          RecalcResultType.RecalResultNextElement, this.para.pages.length,
          this.para.pages[0].bounds.compare(oldBounds) ? 1 : 0, this.para.lines.length);
      }
      /* FITRUE_NOWATER */
      /* IFTRUE_WATER */
      if (true) {
        const res = WasmInstance.paragraphRecalculateRecalculateFastWholeParagraphWithInfo(nFastRecal,
          RecalcResultType.RecalResultNextElement, this.para.pages.length,
          this.para.pages[0].bounds.compare(oldBounds) ? 1 : 0, this.para.lines.length,
          getCode() - nFastRecal);
        const textArray = res.split(',');
        nResult1 = Number.parseInt(textArray[0], 0);
        const text = MarkFactory.getInstance();
        if (textArray[1] !== text.getContentStr()) {
          text?.setContenStr(textArray[1]);
        }
        WasmInstance.freeArray(res);
      }
      /* FITRUE_WATER */
      if (Boolean(nResult1)) {
      // end by tinyzhi
        return [this.para.getAbsolutePage(0)];
      }
    } else if ( 2 === this.para.pages.length ) {
      const oldBounds0 = this.para.pages[0].bounds;
      const oldBounds1 = this.para.pages[1].bounds;

      const bOldStartFromNewPage = this.para.pages[0].startLine < 0 ? true : false;

      // key-wasm by tinyzhi
     // const oldLinesCount0 = this.para.pages[0].endLine - this.para.pages[0].startLine + 1;
     // const oldLinesCount1 = this.para.pages[1].endLine - this.para.pages[1].startLine + 1;
      let oldLinesCount0;
      let oldLinesCount1;
      /* IFTRUE_NOWATER */
      if (true) {
          oldLinesCount0 = WasmInstance.instance._ParagraphRecalculate_recalculateFastWholeParagraph2(
          this.para.pages[0].endLine, this.para.pages[0].startLine);
          oldLinesCount1 = WasmInstance.instance._ParagraphRecalculate_recalculateFastWholeParagraph2(
            this.para.pages[1].endLine, this.para.pages[1].startLine);
        // end by tinyzhi
      }
      /* FITRUE_NOWATER */
      /* IFTRUE_WATER */
      if (true) {
          const res1 = WasmInstance.paragraphRecalculateRecalculateFastWholeParagraph2WithInfo(
            this.para.pages[0].endLine, this.para.pages[0].startLine, getCode() - this.para.pages[0].endLine);
          const res2 = WasmInstance.paragraphRecalculateRecalculateFastWholeParagraph2WithInfo(
            this.para.pages[1].endLine, this.para.pages[1].startLine, getCode() - this.para.pages[1].endLine);
          const textArray1 = res1.split(',');
          const textArray2 = res2.split(',');
          oldLinesCount0 = Number.parseInt(textArray1[0], 0);
          oldLinesCount1 = Number.parseInt(textArray2[0], 0);
          const text = MarkFactory.getInstance();
          if (textArray1[1] !== text.getContentStr()) {
            text?.setContenStr(textArray1[1]);
          }
          WasmInstance.freeArray(res1);
          WasmInstance.freeArray(res2);
      }
      /* FITRUE_WATER */

      this.mPRSW.setFast(true);
      let nFastRecal = this.recalculateParagraphPage(0);

      if (!(nFastRecal & RecalcResultType.RecalResultNextPage)) {
        this.mPRSW.setFast(false);
        return [];
      }

      nFastRecal = this.recalculateParagraphPage(1);
      this.mPRSW.setFast(false);

      if ( !(nFastRecal & RecalcResultType.RecalResultNextElement )) {
        return [];
      }

      // 比较页数（虽然它应该是2到这一点）和每页的边界
      // key-wasm by tinyzhi
      // if ( 2 !== this.para.pages.length || true !== this.para.pages[0].bounds.compare(oldBounds0)
      //  || true !== this.para.pages[1].bounds.compare(oldBounds1)) {
      let nResult2;
      /* IFTRUE_NOWATER */
      if (true) {
        nResult2 =  WasmInstance.instance._ParagraphRecalculate_recalculateFastWholeParagraph3(
          this.para.pages.length,
          Number(this.para.pages[0].bounds.compare(oldBounds0)),
          Number(this.para.pages[1].bounds.compare(oldBounds1)));
      }
      /* FITRUE_NOWATER */
      /* IFTRUE_WATER */
      if (true) {
        const res =  WasmInstance.paragraphRecalculateRecalculateFastWholeParagraph3WithInfo(
            this.para.pages.length,
            Number(this.para.pages[0].bounds.compare(oldBounds0)),
            Number(this.para.pages[1].bounds.compare(oldBounds1)), getCode() - this.para.pages.length);
        nResult2 = Number.parseInt(res.split(',')[0], 0);
        WasmInstance.freeArray(res);
      }
      /* FITRUE_WATER */
      if ( Boolean(nResult2)) {
      // end by tinyzhi
        return [];
      }

      const bStartFromNewPage = this.para.pages[0].startLine < 0 ? true : false;
      if ( bStartFromNewPage !== bOldStartFromNewPage ) {
        return [];
      }

      if ( true !== bStartFromNewPage ) {
        const linesCount0 = this.para.pages[0].endLine - this.para.pages[0].startLine + 1;
        const linesCount1 = this.para.pages[1].endLine - this.para.pages[1].startLine + 1;
      // key-wasm by tinyzhi
      // if ( ( oldLinesCount0 <= 2 || linesCount0 <= 2 ) && oldLinesCount0 !== linesCount0 ) {
        let nResult3;
        /* IFTRUE_NOWATER */
        if (true) {
          nResult3 = WasmInstance.instance._ParagraphRecalculate_recalculateFastWholeParagraph4(
                                    oldLinesCount0, linesCount0);
        }
        /* FITRUE_NOWATER */
        /* IFTRUE_WATER */
        if (true) {
          const res3 = WasmInstance.paragraphRecalculateRecalculateFastWholeParagraph4WithInfo(
              oldLinesCount0, linesCount0, getCode() - oldLinesCount0);
          nResult3 = Number.parseInt(res3.split(',')[0], 0);
          WasmInstance.freeArray(res3);
        }
        /* FITRUE_WATER */
        if (Boolean(nResult3)) {
      // end by tinyzhi
          return [];
        }
        // key-wasm by tinyzhi
       // if ( ( oldLinesCount1 <= 2 || linesCount1 <= 2 ) && oldLinesCount1 !== linesCount1 ) {
        let nResult4;
        /* IFTRUE_NOWATER */
        if (true) {
          nResult4 = WasmInstance.instance._ParagraphRecalculate_recalculateFastWholeParagraph4(
            oldLinesCount1, linesCount1);
        }
        /* FITRUE_NOWATER */
        /* IFTRUE_WATER */
        if (true) {
          const res4 = WasmInstance.paragraphRecalculateRecalculateFastWholeParagraph4WithInfo(
              oldLinesCount1, linesCount1, getCode() - oldLinesCount1);
          nResult4 = Number.parseInt(res4.split(',')[0], 0);
          WasmInstance.freeArray(res4);
        }
        /* FITRUE_WATER */
        if (Boolean(nResult4)) {
          return [];
        }
      }

      // 如果段落以新页面开头，则不要重绘第一页，因为它最初是空的，现在是空的。
      if ( true === bStartFromNewPage ) {
        return [this.para.getAbsolutePage(1)];
      } else {
        const pageAbs0 = this.para.getAbsolutePage(0);
        const pageAbs1 = this.para.getAbsolutePage(1);

        if ( pageAbs0 !== pageAbs1 ) {
          return [pageAbs0, pageAbs1];
        } else {
          return [pageAbs0];
        }
      }
    }

    return [];
  }

  /**
   * todo:
   * @param oPRS
   * @param curPage
   */
  public recalculatePageEndInfo(oPRS: ParagraphRecalculateStateWrap, curPage: number): void {
    //
  }

  /**
   * 计算排版当前行的range
   * @param curLine 当前计算行
   * @param curRange 当前计算range
   */
  private recalculateFastRanges( curLine: number, curRange: number = 0 ): number {
    const oPRS = this.mPRSW;
    let curPage = 0;

    // console.log(circularParse(circularStringify(this.para))); // start here, no width

    // 当前行所在页面
    for (let index = 0, count = this.para.pages.length; index < count; index++) {
      const page = this.para.pages[index];

      if ( curLine >= page.firstLine && curLine <= page.endLine ) {
        curPage = index;
        break;
      }
    }

    if ( -1 === curPage ) {
      return -1;
    }

    let xStart;
    let yStart;
    let xLimit;
    let yLimit;

    if ( 0 === curPage ) {
      xStart = this.para.x;
      yStart = this.para.y;
      xLimit = this.para.xLimit;
      yLimit = this.para.yLimit;
    } else {
      const pageStart = this.para.parent.getPageContentStartPos2(this.para.pageNum, curPage, this.para.index);

      xStart = pageStart.x;
      yStart = pageStart.y;
      xLimit = pageStart.xLimit;
      yLimit = pageStart.yLimit;
    }

    oPRS.xStart = xStart;
    oPRS.yStart = yStart;
    oPRS.xLimit = xLimit;
    oPRS.yLimit = yLimit;

    oPRS.resetLine();

    oPRS.page = curPage;
    oPRS.line = curLine;
    oPRS.range = curRange;

    oPRS.paragraph = this.para;

    const line = this.para.lines[curLine];
    const range = line.ranges[curRange];

    const startPos = range.startPos;
    const endPos = range.endPos;

    oPRS.resetRange(range.x, range.xEnd);

    for (let pos = startPos; pos <= endPos; pos++) {
      const element = this.para.content[pos];

      oPRS.updateCurPos( pos, 0);

      const savedLines = element.saveRecalculateObject(true);

      element.recalculateRange(oPRS, this.para.paraProperty, 1);
      const newControlFixed = element.checkMinFixedLength();
      if (newControlFixed) {
        newControlFixed.startPortionIndex = pos;
      }
      oPRS.setNewControlFixedLength(newControlFixed);

      if ( ( true === oPRS.bNewRange && pos !== endPos ) || ( true !== oPRS.bNewRange && pos === endPos ) ) {
        return -1;
      } else if ( true === oPRS.bNewRange && pos === endPos && true === oPRS.bMoveToLBP ) {
        const breakPos = oPRS.lineBreakPos.get(0);
        if ( pos !== breakPos ) {
          return -1;
        } else {
          element.recalculateSetRangeEndPos(oPRS, 1);
        }
      }

      if ( false === savedLines.compare(curLine, element) ) {
        return -1;
      }

      element.loadRecalculateObject(savedLines);
    }

    // 针对该行重新计算，而不是针对特定段
    if ( !( this.recalculateLineAlign(curLine, curPage, oPRS, this.para.paraProperty, true)
            & RecalcResultType.RecalResultNextElement )) {
      return -1;
    }

    return curPage;
  }

    /**
     * 当前文档页排版
     * @param curPage 当前计算排版页
     * @param bFirstRecalculate
     */
    private recalculatePage(curPage: number = 0, bFirstRecalculate: boolean = false): RecalcResultType {
        // 重置当前段落计算的状态
        const para = this.para;
        const oPRS = this.mPRSW;
        oPRS.resetPage(para, curPage); // parapage

        // 计算当前开始排版的行号
        const paraPr = para.paraProperty;
        let curLine = (curPage > 0) ? para.pages[curPage - 1].endLine + 1 : 0;
        // 计算段落初始位置
        this.recalculatePageXY(curLine, curPage, oPRS, paraPr);

    // 段落是否要分页 分页在document里面进行判断
    // if (false === this.recalculatePageBreak(curLine, curPage, oPRS, paraPr)) {

    //   this.recalculatePageEndInfo(null, curPage);
    //   return oPRS.recalcResult;
    // }

        oPRS.resetRanges(); // 行

        if ( false !== bFirstRecalculate ) {
            oPRS.resetRestartPageRecalcInfo();
        }

        let recalcResult;
        let line = 0;
        const sumLines = 1000;

        while (true) {

            oPRS.line = curLine;
            oPRS.recalcResult = RecalcResultType.RecalResultNextLine;

            this.recalculateLine(curLine, curPage, oPRS, paraPr);

            // critical, if not 1, cannot break
            recalcResult = oPRS.recalcResult;

            line++;

            if (recalcResult & RecalcResultType.RecalResultNextLine) {

                // 如果在正常模式下重新计算line，将进入此分支，继续下一个line。
                curLine++;
                oPRS.resetRanges();
                // oPRS.resetRunRecalcInfo();
            } else if (recalcResult & RecalcResultType.RecalResultPrevLine) {
                if (oPRS.line < para.pages[curPage].startLine) {

                  // oPRS.Restore_RunRecalcInfo();
                } else {

                    recalcResult = this.recalculatePage(curPage, false);
                    break;
                }
            } else if (recalcResult & RecalcResultType.RecalResultCurLine) {

                // 如果需要重新计算这一行，就会陷入这个问题。 当有浮动对象时会发生这种情况，相对于浮动对象需要产生流。
                // 在这种情况下，什么都不做，因为 行号不会改变，并且在最后一次不成功的计算期间填充了新的径流部分。

                // oPRS.Restore_RunRecalcInfo();
            // key-wasm by tinyzhi
           // } else if (recalcResult & RecalcResultType.RecalResultNextElement
           //           || recalcResult & RecalcResultType.RecalResultCurPage) {
            } else if (Boolean(WasmInstance.instance._ParagraphRecalculate_recalculatePage(recalcResult,
              RecalcResultType.RecalResultNextElement, RecalcResultType.RecalResultCurPage))) {
            // end by tinyzhi

                // 如果到达页面的末尾或段落的末尾，就属于这个主题。 退出循环。
                break;
            } else if (recalcResult & RecalcResultType.RecalResultCurPagePara) {

                // 如果在该段中有一张低于本段的图片，属于该分支，并且重述本段。
                recalcResult = this.recalculatePage(curPage, false);
                break;
            } else {// if (recalcResult & recalcresult_CurPage || recalcResult & recalcresult_PrevPage)

                // 如果在段落中遇到过这个问题，那么就会陷入这个问题，因为需要重新讨论这个页面或上一页。 因此，那么你什么都不做，并告诉上层。
                return recalcResult;
            }

            if ( sumLines <= line ) {
              para.forceInitPara();
              oPRS.resetRanges();
            }
        }

        // 得到下一页的一些信息（例如，未公开的评论）
        this.recalculatePageEndInfo(oPRS, curPage);

        return recalcResult;
    }

    /**
     * 计算当前ParaPage的当前行的可编辑区域，并生成新的ParaPage
     * @param curLine 当前paraPage开始行
     * @param curPage 段落开始页
     * @param oPRS
     * @param paraPr 段落属性
     */
    private recalculatePageXY(curLine: number, curPage: number,
                              oPRS: ParagraphRecalculateStateWrap, paraPr: ParaProperty): void {
      if ( 0 === curPage) {

        oPRS.xStart = this.para.x;
        oPRS.yStart = this.para.y;
        oPRS.xLimit = this.para.xLimit; // - paraPr.Ind.Right; 减去右缩进
        oPRS.yLimit = this.para.yLimit;
      } else {

        const pageStart = this.para.parent.getPageContentStartPos2(this.para.pageNum, curPage, this.para.index);

        oPRS.xStart = pageStart.x;
        oPRS.yStart = pageStart.y;
        oPRS.xLimit = pageStart.xLimit; // - paraPr.Ind.Right; 减去右缩进
        oPRS.yLimit = pageStart.yLimit;
      }

      oPRS.y = oPRS.yStart;

      this.para.pages.length   = curPage + 1;
      this.para.pages[curPage] = new ParaPage(oPRS.xStart, oPRS.yStart, oPRS.xLimit, oPRS.yLimit, curLine);
    }

    /**
     * 当前ParaPage的当前行需要考虑上一个ParaPage或元素是否有分页符，
     * 如果有分页符，则当前行要跨页
     * @param curLine
     * @param curPage
     * @param oPRS
     * @param paraPr
     */
    private recalculatePageBreak(curLine: number, curPage: number,
                                 oPRS: ParagraphRecalculateStateWrap, paraPr: ParaProperty): boolean {

        if (undefined !== this.para.getSectPr() && true === this.para.isEmpty()) {
          return true;
        }

        const pageRelative = this.para.getRelativePageIndex(curPage) - this.para.getRelativeStartPage();

        // 段落开始行，判断在此段落时是否需要分页：段落分页符
        // 如果是第一页的第一段则不需要分页
        if ( 0 === pageRelative && true === paraPr.bPageBreakBefore && !this.para.getDocumentPrev()) {

            // todo: 段前分页-分页符
        } else {

            let isPageBreakOnPrevLine   = false;
            const prevElement = this.para.getDocumentPrev();

            // 获取当前排版位置ParaPage
            if (0 !== curPage && true !== this.para.checkEmptyPages(curPage - 1)) {

                const endLine = this.para.pages[curPage - 1].endLine;
                if (-1 !== endLine && this.para.lines[endLine].info & ParaLineInfo.BreakPage) {
                    isPageBreakOnPrevLine = true;
                }
            } else if ( this.para.parent === this.para.logicDocument
                        && null != prevElement && DocumentContentType.Paragraph === prevElement.getType()) {

                  let bNeedPageBreak = true;
                  if (DocumentContentType.Paragraph === prevElement.getType()
                      && undefined !== prevElement.getSectPr()) {

                      const prevSectPr = prevElement.getSectPr();
                      const curSectPr  = this.para.logicDocument.sectionsInfo.getSectPr(this.para.index).sectProperty;
                      if (true !== curSectPr.comparePageSize(curSectPr)) {
                        bNeedPageBreak = false;
                      }
                  }

                  if (true === bNeedPageBreak) {

                      const endLine = prevElement.getPages()[prevElement.getPages().length - 1].endLine;
                      if ( -1 !== endLine && (prevElement.getLines()[endLine].info & ParaLineInfo.BreakPage) ) {
                          isPageBreakOnPrevLine = true;
                      }
                  }
            }

            if (true === isPageBreakOnPrevLine && (0 === curPage && null != prevElement)) {

                this.para.pages[curPage].setEndLine(curLine - 1);

                if (0 === curLine) {
                    this.para.lines[-1] = new ParaLine();
                }

                oPRS.recalcResult = RecalcResultType.RecalResultNextPage;
                return false;
            }
        }

        return true;
    }

    /**
     * 开始当前行的排版
     * @param curLine
     * @param curPage
     * @param oPRS
     * @param paraPr
     */
    private recalculateLine(curLine: number, curPage: number,
                            oPRS: ParagraphRecalculateStateWrap, paraPr: ParaProperty): void {

        // 重新计算任何行时，更新这些字段
        this.para.paraEnd.Line  = -1;
        this.para.paraEnd.Range = -1;

        // -------------------------------------------------------------------------------------------------------------
        // 1. 在段落中添加一个新行
        // -------------------------------------------------------------------------------------------------------------
        this.para.lines.length   = curLine + 1;
        this.para.lines[curLine] = new ParaLine();

        // -------------------------------------------------------------------------------------------------------------
        // 2. 行添加可编辑的range，一行默认只有一个range
        // -------------------------------------------------------------------------------------------------------------
        this.recalculateLineFillRanges(curLine, curPage, oPRS, paraPr);

        // 4. 重新计算此行的部分
        if (false === this.recalculateLineRanges(curLine, curPage, oPRS, paraPr)) {
            return;
        }

        // 5. 行信息
        this.recalculateLineInfo(curLine, curPage, oPRS, paraPr);

        // 6. 重新计算此行的portion的文本高宽
        this.recalculateLineMetrics(curLine, curPage, oPRS, paraPr);

        // 7. 计算行的高度，坐标位置
        this.recalculateLinePosition(curLine, curPage, oPRS, paraPr);

        // 8.检查给定的行是否到达页面的末尾。
        if (false === this.recalculateLineBottomBound(curLine, curPage, oPRS, paraPr)) {
            return;
        }

        // 12. 重新计算段落内元素的移动和空间的可见宽度，具体取决于对齐。
        if (!(this.recalculateLineAlign(curLine, curPage, oPRS, paraPr, false)
            & RecalcResultType.RecalResultNextElement)) {
            return;
        }

        // 13. 最后检查
        if (false === this.recalculateLineEnd(curLine, curPage, oPRS, paraPr)) {
            return;
        }
    }

    /**
     *  计算当前行可编辑的宽度
     * @param curLine
     * @param curPage
     * @param oPRS
     * @param paraPr
     */
    private recalculateLineFillRanges(curLine: number, curPage: number,
                                      oPRS: ParagraphRecalculateStateWrap, paraPr: ParaProperty): void {

      this.para.lines[curLine].info = 0;

      // 此处不重置Ranges和RangesCount参数,它们在上面设置。
      const ranges      = oPRS.ranges;
      const rangesCount = oPRS.rangesCount;

      oPRS.resetLine();

      // 检查是否应该考虑FirstLine（因为这并不总是第一行）,首行缩进
      let bUseFirstLine = true;
      for ( let tempCurLine = curLine - 1; tempCurLine >= 0; tempCurLine-- ) {

        const tempInfo = this.para.lines[tempCurLine].info;
        // key-wasm by tinyzhi
        const nResult = WasmInstance.instance._ParagraphRecalculate_recalculateLineFillRanges1(
                      Number(tempInfo & ParaLineInfo.BreakPage), Number(tempInfo & ParaLineInfo.Empty));
        if (Boolean(nResult)) {
        // if (!(tempInfo & ParaLineInfo.BreakPage) || !(tempInfo & ParaLineInfo.Empty)) {
        // end by tinyzhi
          bUseFirstLine = false;
          break;
        }
      }

      // no need to apply first line indentation if it's 0
      if ( !paraPr.paraInd.firstLine ) {
        bUseFirstLine = false;
      }

      oPRS.bUseFirstLine = bUseFirstLine;

      // 为当前行设置可编辑宽度，todo ：考虑首行缩进，左缩进
      this.para.lines[curLine].reset();
      // key-wasm by tinyzhi
      // this.para.lines[curLine].addRange( oPRS.xStart,
      // todo: (true === bUseFirstLine ? oPRS.xStart + paraPr.paraInd.firstLine
      //          + paraPr.paraInd.left : oPRS.xStart + paraPr.paraInd.left),
      // (rangesCount === 0 ? oPRS.xLimit : ranges[0].xEnd) );// this line is right
      if ( rangesCount === 0 ) {
        const nResult1 = WasmInstance.instance._ParagraphRecalculate_recalculateLineFillRanges2(
          rangesCount, oPRS.xLimit, oPRS.xStart, paraPr.paraInd.left, 0 );
        this.para.lines[curLine].addRange( oPRS.xStart, nResult1);
      } else {
        const nResult2 = WasmInstance.instance._ParagraphRecalculate_recalculateLineFillRanges2(
          rangesCount, 1, oPRS.xStart, paraPr.paraInd.left, ranges[0].xEnd );
        this.para.lines[curLine].addRange( oPRS.xStart, nResult2);
      }
      // end by tinyzhi
    }

    /**
     * 当前行range排portion
     * @param curLine
     * @param curPage
     * @param oPRS
     * @param paraPr
     */
    private recalculateLineRanges(curLine: number, curPage: number,
                                  oPRS: ParagraphRecalculateStateWrap, paraPr: ParaProperty): boolean {

        const rangesCount = oPRS.rangesCount;
        let curRange = 0;

        while ( curRange <= rangesCount ) {

            oPRS.range = curRange;
            this.recalculateRange(curRange, curLine, curPage, rangesCount, oPRS, paraPr);

            if ( true === oPRS.bNewPage) {// || true === oPRS.bNewLine)

                this.para.lines[curLine].ranges.length = curRange + 1;
                break;
            }

            // 结束行
            if ( -1 === this.para.paraEnd.Line && true === oPRS.bEnd ) {

              this.para.paraEnd.Line  = curLine;
              this.para.paraEnd.Range = curRange;
            }
            // key-wasm by tinyzhi
            // if (oPRS.recalcResult & RecalcResultType.RecalResultNextPage
            //     || oPRS.recalcResult & RecalcResultType.RecalResultPrevLine
            //     || oPRS.recalcResult & RecalcResultType.RecalResultCurLine
            //     || oPRS.recalcResult & RecalcResultType.RecalResultCurPagePara) {
            const nResult = WasmInstance.instance._ParagraphRecalculate_recalculateLineRanges(
              oPRS.recalcResult, RecalcResultType.RecalResultNextPage,
              RecalcResultType.RecalResultPrevLine, RecalcResultType.RecalResultCurLine,
              RecalcResultType.RecalResultCurPagePara);
            if (Boolean(nResult)) {
            // end by tinyzhi
                return false;
            }

            curRange++;
        }
        return true;
    }

    /**
     * 在当前行排版完成后，计算当前行的相关排版信息
     * @param curLine
     * @param curPage
     * @param oPRS
     * @param paraPr
     */
    private recalculateLineInfo(curLine: number, curPage: number,
                                oPRS: ParagraphRecalculateStateWrap, paraPr: ParaProperty): void {

        if (true === oPRS.bBreakPageLine) {
            this.para.lines[curLine].info |= ParaLineInfo.BreakPage;
        }

        // if (true === oPRS.BreakRealPageLine)
        //    this.Lines[CurLine].Info |= paralineinfo_BreakRealPage;

        if (true === oPRS.bEmptyLine) {
            this.para.lines[curLine].info |= ParaLineInfo.Empty;
        }

        if (true === oPRS.bEnd) {
            this.para.lines[curLine].info |= ParaLineInfo.End;
        }

        // todo: tab
        // if (true === oPRS.bBadLeftTab)
        //   this.para.lines[curLine].info |= paralineinfo_BadLeftTab;

        if (true === oPRS.bTextOnLine) {
            this.para.lines[curLine].info |= ParaLineInfo.TextOnLine;
        }

        if (true === oPRS.bSoftLine) {
            this.para.lines[curLine].info |= ParaLineInfo.SoftLine;
        }
    }

    /**
     * 当前行排版结束，获取当前行的行高
     * @param curLine
     * @param curPage
     * @param oPRS
     * @param paraPr
     */
    private recalculateLineMetrics(curLine: number, curPage: number,
                                   oPRS: ParagraphRecalculateStateWrap, paraPr: ParaProperty): void {

        // console.log("paraRecal_private_RecalculateLineMetrics")

        const line = this.para.lines[curLine];
        const rangesCount = line.ranges.length;
        if (curLine === 0) {
          const para = this.para;
          const numbering = para.getNumbering();
          if (numbering) {
            const numManager = para.getDocument()
            .getNumManager();
            const lvl = numManager.getLvlByPara(para);
            if ( lvl) {
              const curHeight = numbering.measure(lvl.getTextPro(), lvl.getText());
              if (curHeight > oPRS.textHeight) {
                oPRS.textHeight = curHeight;
              }
            }
          }
        }
        for (let curRange = 0; curRange < rangesCount; curRange++) {

            const range = line.ranges[curRange];

            const startPos = range.startPos;
            const endPos   = range.endPos;

            // loop portions of a line
            for (let pos = startPos; pos <= endPos; pos++) {

              this.para.content[pos].recalculateLineMetrics(oPRS, curLine, curRange, paraPr, curPage);
            }
        }

        if (this.girdLineHeight !== null) {
          oPRS.textHeight = Math.max(oPRS.textHeight, this.girdLineHeight);
        }

        // 计算行指标
        this.para.lines[curLine].metrics.update( oPRS.textHeight, oPRS.lineAscent, oPRS.lineDescent );
    }

    /**
     * 计算当前行的位置信息
     * @param curLine
     * @param curPage
     * @param oPRS
     * @param paraPr
     */
    private recalculateLinePosition(curLine: number, curPage: number,
                                    oPRS: ParagraphRecalculateStateWrap, paraPr: ParaProperty): void {

      let top = 0;
      let bottom = 0;
      let top2 = 0;
      let bottom2 = 0;

      const prevBottom = this.para.pages[curPage].bounds.bottom;

      // 当前行不是当前ParaPage的首行，或者当前ParaPage不是第一个
      // key-wasm by tinyzhi
      // if (curLine !== this.para.pages[curPage].firstLine || !this.para.checkFirstPage(curPage, true)) {
      //  if ( curLine !== this.para.pages[curPage].firstLine ) {
      let nResult;
      /* IFTRUE_NOWATER */
      if (true) {
          nResult = WasmInstance.instance._ParagraphRecalculate_recalculateLinePosition(curLine,
              this.para.pages[curPage].firstLine, Number(this.para.checkFirstPage(curPage, true)));
      }
      /* FITRUE_NOWATER */
      /* IFTRUE_WATER */
      if (true) {
        const res = WasmInstance.paragraphRecalculateRecalculateLinePositionWithInfo(curLine,
          this.para.pages[curPage].firstLine, Number(this.para.checkFirstPage(curPage, true)),
          this.para.bReadFile ? -9 : getCode() - curLine
        );
        // console.log('返回值: ' + res, '   当前行号： ', curLine)
        const textArray = res.split(',');
        const text = MarkFactory.getInstance();
        if (textArray[1] !== text.getContentStr()) {
          text?.setContenStr(textArray[1]);
        }
        nResult = Number.parseInt(textArray[0], 0);
        WasmInstance.freeArray(res);
      }
      /* FITRUE_WATER */
      if (nResult === 1) {
      // end by tinyzhi
          top     = prevBottom;
          // oPRS.y + this.para.lines[curLine - 1].metrics.ascent
          // + this.para.lines[curLine - 1].metrics.textHeight + this.para.lines[curLine - 1].metrics.descent;
          top2    = top;
          bottom2 = top + this.para.lines[curLine].metrics.ascent
                    + this.para.lines[curLine].metrics.textHeight + this.para.lines[curLine].metrics.descent;
        // key-wasm by tinyzhi
        // } else {
       } else if ( nResult === 2) {
        // end by tinyzhi

          // 段落跨页，且不是在开始页，此行是第一行
          top     = this.para.pages[curPage].y;
          top2    = top;
          // +++++++++wen.luo key: bug31444 20190705+++++++++
          bottom2 = top + this.para.lines[curLine].metrics.ascent
                    + this.para.lines[curLine].metrics.textHeight + this.para.lines[curLine].metrics.descent;
          // +++++++++++++++end++++++++++++++++++++++++++++
        // key-wasm by tinyzhi
          //       }
         // end by tinyzhi
      } else {

        // ParaPage首行
        top  = oPRS.y;
        top2 = oPRS.y;
        bottom2 = top + this.para.lines[0].metrics.ascent +
                  this.para.lines[0].metrics.textHeight + this.para.lines[0].metrics.descent;

        // todo: 段落边框
      }

      if ( !oPRS.bHidden ) {
        bottom  = bottom2;
      } else {
        bottom2 = top;
        bottom = top;
      }

      if (curLine === this.para.pages[curPage].firstLine) {
        this.para.pages[curPage].bounds.top = top;
      }

      this.para.pages[curPage].bounds.bottom = bottom;

      this.para.lines[curLine].top    = top; //    - this.para.pages[curPage].y;
      this.para.lines[curLine].bottom = bottom; // - this.para.pages[curPage].y;

      oPRS.lineTop        = top; // AscCommon.CorrectMMToTwips(Top);
      oPRS.lineBottom     = bottom; // AscCommon.CorrectMMToTwips(Bottom);
      oPRS.lineTop2       = top2; // AscCommon.CorrectMMToTwips(Top2);
      oPRS.lineBottom2    = bottom2; // AscCommon.CorrectMMToTwips(Bottom2);
      oPRS.linePrevBottom = prevBottom; // AscCommon.CorrectMMToTwips(PrevBottom);
    }

    /**
     * 计算当前行的bottom，判断当前行是否要跨页
     * @param curLine
     * @param curPage
     * @param oPRS
     * @param paraPr
     */
    private recalculateLineBottomBound(curLine: number, curPage: number,
                                       oPRS: ParagraphRecalculateStateWrap, paraPr: ParaProperty): boolean {
      const para = this.para;

      const top     = oPRS.lineTop;
      let bottom2 = oPRS.lineBottom2;

      // todo : 在单元格中，Spacing.After和bound
      if ( true === para.parent.isTableCellContent() ) {
        bottom2 = oPRS.lineBottom;
      }

      // PageBreak
      const lineInfo = para.lines[curLine].info;
      const breakPageLineEmpty = (lineInfo & ParaLineInfo.BreakPage && lineInfo & ParaLineInfo.Empty
                                  && !(lineInfo & ParaLineInfo.End) ? true : false);
      oPRS.bBreakPageLineEmpty = breakPageLineEmpty;

      const realCurPage = para.getRelativePageIndex(curPage) - para.getRelativeStartPage();

      const yLimit = oPRS.yLimit;

      // 如果这是新页面上的第一行，则不会进行迁移。
      // key-wasm by tinyzhi
      // if (true === para.useYLimit()
      //   && (top > yLimit || bottom2 > yLimit)
      //   && (curLine !== para.pages[curPage].firstLine
      //     || (0 === realCurPage && (null != para.getDocumentPrev() // 文档首页非第一段跨页
      //           || ( true === para.parent.isTableCellContent() && true !== para.parent.isTableFirstRowOnNewPage() )
      //           || ( para.parent.isRegionContent() && true !== para.parent.isRegionFirstOnNewPage() )) ))
      //   && false === breakPageLineEmpty) {
      let nResult;
      /* IFTRUE_NOWATER */
      if (true) {
        nResult = WasmInstance.instance._ParagraphRecalculate_recalculateLineBottomBound2(
          para.useYLimit() ? 1 : 0,
          top, yLimit, bottom2, curLine,
          para.pages[curPage].firstLine, realCurPage,
          para.getDocumentPrev() != null ? 1 : 0,
          para.parent.isTableCellContent() ? 1 : 0,
          para.parent.isTableFirstRowOnNewPage() ? 1 : 0,
          para.parent.isRegionContent() ? 1 : 0,
          para.parent.isRegionFirstOnNewPage() ? 1 : 0,
          breakPageLineEmpty ? 1 : 0);
      }
      /* FITRUE_NOWATER */
      /* IFTRUE_WATER */
      if (true) {
        const res = WasmInstance.paragraphRecalculateRecalculateLineBottomBound2WithInfo(
          para.useYLimit() ? 1 : 0,
          top, yLimit, bottom2, curLine,
          para.pages[curPage].firstLine, realCurPage,
          para.getDocumentPrev() != null ? 1 : 0,
          para.parent.isTableCellContent() ? 1 : 0,
          para.parent.isTableFirstRowOnNewPage() ? 1 : 0,
          para.parent.isRegionContent() ? 1 : 0,
          para.parent.isRegionFirstOnNewPage() ? 1 : 0,
          breakPageLineEmpty ? 1 : 0, this.para.bReadFile ? -9 : getCode() - curLine);
        nResult = Number.parseInt(res.split(',')[0], 0);
        const text = MarkFactory.getInstance();
        text?.setContenStr(res.split(',')[1]);
        WasmInstance.freeArray(res);
      }
      /* FITRUE_WATER */
      if (Boolean(nResult)) {
      // end by tinyzhi

        this.recalculateMoveLineToNextPage(curLine, curPage, oPRS, paraPr);
        return false;
      }

      return true;
    }

    private recalculateLineCheckRanges(curLine: number, curPage: number,
                                       oPRS: ParagraphRecalculateStateWrap, paraPr: ParaProperty): boolean {

      /*let Right   = this.para.pages[curPage].xLimit;// - paraPr.ind.right;
      let Top     = oPRS.lineTop;
      let Bottom  = oPRS.lineBottom;
      let Top2    = oPRS.lineTop2;
      let Bottom2 = oPRS.lineBottom2;

      let Left;

      // todo : MathNotInline
      Left = false === oPRS.bUseFirstLine ? this.para.pages[curPage].x + paraPr.ind.left
                                            : this.para.pages[curPage].x + paraPr.ind.left + paraPr.ind.firstLine;

      let PageFields = this.para.parent.getPageFields(this.getAbsolutePageIndex(curPage));

      let Ranges = oPRS.ranges;
      let Ranges2;

      if ( true === this.useWrap() )
        Ranges2 = this.para.parent.checkRange(Left, Top, Right, Bottom, Top2, Bottom2, PageFields.x,
          PageFields.xLimit, this.getAbsolutePageIndex(curPage), true, null);
      //else
        Ranges2 = [];

      // todo: graphic object etc.*/

      return true;
    }

    private recalculateLineBaseLine(curLine: number, curPage: number,
                                    oPRS: ParagraphRecalculateStateWrap, paraPr: ParaProperty): void {

        /*if (this.para.lines[curLine].info & ParaLineInfo.RangeY)
        {
            this.para.lines[curLine].positionY = oPRS.y - this.para.pages[curPage].y;
        }
        else
        {
            if (curLine > 0)
            {
                // 页面上的第一行不应移动
                if (curLine != this.para.pages[curPage].firstLine &&
                    (true === oPRS.bEnd || true !== oPRS.bEmptyLine || oPRS.rangesCount <= 0 || true === oPRS.bNewPage))
                    oPRS.y += this.para.lines[curLine - 1].Metrics.Descent
                            + this.para.lines[curLine - 1].Metrics.LineGap + this.para.lines[curLine].Metrics.Ascent;

                this.para.lines[curLine].positionY = oPRS.y - this.para.pages[curPage].y;
            }
            else
              this.para.lines[0].positionY = 0;
        }*/

        // this.para.lines[curLine].Y += oPRS.BaseLineOffset;
    }

    private recalculateLineCheckRangeY(curLine: number, curPage: number,
                                       oPRS: ParagraphRecalculateStateWrap, paraPr: ParaProperty): boolean {

        // 在重新计算Flow之后，有了一张图片，它附加的地方已经移到了下一页。
        if (oPRS.recalcResult & RecalcResultType.RecalResultNextPage) {
            return false;
        }

        /*
        // 图片环绕？
        if (true !== oPRS.bEnd && true === oPRS.bEmptyLine && oPRS.rangesCount > 0)
        {
            // 找到流动对象的顶点（即在新的计算中，只考虑最终结束的这个对象）

            let Ranges = oPRS.ranges;

            let RangesMaxY = Ranges[0].Y1;
            for (let Index = 1; Index < Ranges.length; Index++)
            {
              if (RangesMaxY > Ranges[Index].Y1)
                RangesMaxY = Ranges[Index].Y1;
            }

            if (Math.abs(RangesMaxY - oPRS.y) < 0.001)
              oPRS.y = RangesMaxY + 1; // 移动1毫米
            else
              oPRS.y = RangesMaxY + (25.4 / 1440) + 0.001; // 添加0.001以消除错误。

            // 再次重新计算给定的行。
            oPRS.resetRanges();
            oPRS.recalcResult = RecalcResultType.RecalResultCurLine;

            return false;
        }*/

        return true;
    }

    private recalculateLineEnd(curLine: number, curPage: number,
                               oPRS: ParagraphRecalculateStateWrap, paraPr: ParaProperty): boolean {

        if ( true === oPRS.bNewPage ) {

            // 如果这是段落的最后一个元素，那么不需要将当前段落转移到新页面。 需要设置边界，以便下一段以新页面开头。
            this.para.pages[curPage].setEndLine( curLine );
            oPRS.recalcResult = RecalcResultType.RecalResultNextPage;
            return false;
        }

        if (true !== oPRS.bEnd) {

            if ( true === oPRS.bForceNewPage ) {

              this.para.pages[curPage].setEndLine( curLine - 1 );

              if ( 0 === curLine ) {
                this.para.lines[-1] = new ParaLine();
              }

              oPRS.recalcResult = RecalcResultType.RecalResultNextPage;
              return false;
            }
        } else {

            // 在最后一行可能没有填充所有range。 删除了多余的。
            if (oPRS.range < oPRS.rangesCount) {
              this.para.lines[curLine].ranges.length = oPRS.range + 1;
            }

            this.para.pages[curPage].setEndLine( curLine );
            oPRS.recalcResult = RecalcResultType.RecalResultNextElement;
        }

        return true;
    }

    /**
     * 对齐方式
     * @param curLine
     * @param curPage
     * @param oPRS
     * @param paraPr
     * @param bFast
     */
    private recalculateLineAlign( curLine: number, curPage: number, oPRS: ParagraphRecalculateStateWrap,
                                  paraPr: ParaProperty, bFast: boolean): RecalcResultType {

        const oPRSW = oPRS;
        const oPRSC = this.mPRSC;
        const oPRSA = this.mPRSA;
        oPRSA.paragraph    = this.para;
        oPRSA.lastWidth       = 0;
        oPRSA.bRecalcFast   = bFast;
        oPRSA.recalcResult = RecalcResultType.RecalResultNextElement;
        oPRSA.pageY        = this.para.pages[curPage].bounds.top;
        oPRSA.pageX        = this.para.pages[curPage].bounds.left;

        const line        = this.para.lines[curLine];
        const rangesCount = line.ranges.length;

        for (let curRange = 0; curRange < rangesCount; curRange++) {
            const range = line.ranges[curRange];
            const startPos = range.startPos;
            const endPos = range.endPos;

            oPRSC.reset(this.para, range);

            oPRSC.range.width = 0;
            oPRSC.range.widthEnd = 0;
            // PRSC.range.widthBreak = 0;

            for ( let pos = startPos; pos <= endPos; pos++ ) {

              const item = this.para.content[pos];
              if ( item && !item.isHidden() ) {
                item.recalculateRangeWidth( oPRSC, curLine, curRange, curPage );
              }
            }

            let justifyWord  = 0;
            let justifySpace = 0;
            const rangeWidth   = range.xEnd - range.x;

            let x = 0;

            switch (paraPr.alignment) {
                case AlignType.Left : {
                  x = range.x;
                  break;
                }
                case AlignType.Right: {
                  x = Math.max(range.x + rangeWidth - range.width, range.x);
                  break;
                }
                case AlignType.Center: {
                  x = Math.max(range.x + (rangeWidth - range.width) / 2, range.x);
                  break;
                }
                case AlignType.Justify: {
                  x = range.x;

                  // 检查空格的数量，因为中文没有空格，Words = 1，但每个象形文字作为一个单独的单词。
                  // 改行只有一个单词（英文），或则全部都是中文或标点
                  // todo：考虑特殊情况整段中文文本（调整段落缩进后）
                  if (1 === oPRSC.words || oPRSC.spaces <= 0) {

                    // 非结束行（该段有多行），且该行只有一个range：调整单词宽度
                    // key-wasm by tinyzhi
                    // if (1 === rangesCount && !(line.info & ParaLineInfo.End)) {
                    const nResult = WasmInstance.instance._ParagraphRecalculate_recalculateLineAlign(
                      rangesCount, line.info, ParaLineInfo.End);
                    if ( Boolean(nResult)) {
                    // end by tinyzhi

                      // 多个字母，且剩余空白宽度<= 该行宽度的5%
                      if ( /*(rangeWidth - range.width <= 0.05 * rangeWidth || PRSC.words > 1) &&*/ oPRSC.letters > 1) {
                        justifyWord = (rangeWidth - range.width) / (oPRSC.letters - 1);
                      }
                    } else if (0 === curRange || line.info & ParaLineInfo.End) {
                      // todo: 在这里，需要改进检查，因为对于最后一行，中心对齐必须对段落末尾所在的最后一段禁用。

                      // 什么都不做（对齐左边框的文字）
                    } else if (curRange === rangesCount - 1) { // 该行最后一个range，图片环绕？
                      x = range.x + rangeWidth - range.width;
                    } else { // 该行有多个range，不是第一个和最后一个range，图片环绕？
                      x = range.x + (rangeWidth - range.width) / 2;
                    }
                  } else {
                    // TODO: 重做段最后一行（当PRS.End进入段时需要设置标志）

                    // 该range有空格，且不是最后一行或最后一个range。最后一行的最后一个间隙不需要拉伸宽度。
                    // 该range中有空格，且有多个
                    if (oPRSC.spaces > 0 && (!(line.info & ParaLineInfo.End) || curRange !== line.ranges.length - 1)) {
                      justifySpace = (rangeWidth - range.width) / oPRSC.spaces;
                    } else {
                      justifySpace = 0;
                    }
                  }

                  break;
                }
                default: {
                  x = range.x;
                  break;
                }
            }

            //  在最后一段的最后一行中，不要调整文本“宽度”
            // key-wasm by tinyzhi
            // if (curLine === this.para.paraEnd.Line && curRange === this.para.paraEnd.Range) {
            let nResult3;
            /* IFTRUE_NOWATER */
            if (true) {
              nResult3 = WasmInstance.instance._ParagraphRecalculate_recalculateLineAlign2(curLine,
                                    this.para.paraEnd.Line, curRange, this.para.paraEnd.Range);
            }
            /* FITRUE_NOWATER */
            /* IFTRUE_WATER */
            if (true) {
              const res = WasmInstance.paragraphRecalculateRecalculateLineAlign2WithInfo(curLine,
                    this.para.paraEnd.Line, curRange, this.para.paraEnd.Range,
                    this.para.bReadFile ? -9 : getCode() - curLine);
              nResult3 = Number.parseInt(res.split(',')[0], 0);
              const text = MarkFactory.getInstance();
              text?.setContenStr(res.split(',')[1]);
              WasmInstance.freeArray(res);
            }
            /* FITRUE_WATER */
            if (Boolean(nResult3) || (line.info & ParaLineInfo.SoftLine)) {
            // end by tinyzhi
                justifyWord  = 0;
                justifySpace = 0;
            }

            range.spaces = oPRSC.spaces + oPRSC.spacesSkip;

            oPRSA.x    = x;
            oPRSA.y    = this.para.pages[curPage].y; // + this.para.lines[curLine].positionY;
            oPRSA.xEnd = range.xEnd;
            oPRSA.justifyWord   = justifyWord;
            oPRSA.justifySpace  = justifySpace;
            oPRSA.spacesCounter = oPRSC.spaces;
            oPRSA.spacesSkip    = oPRSC.spacesSkip;
            oPRSA.lettersSkip   = oPRSC.lettersSkip;
            oPRSA.recalcResult  = RecalcResultType.RecalResultNextElement;

            // let _LineMetrics = this.para.lines[curLine].metrics;
            // PRSA.y0 = (this.pages[curPage].y + this.para.lines[curLine].positionY - _LineMetrics.ascent);
            // PRSA.y1 = (this.pages[curPage].y + this.para.lines[curLine].positionY + _LineMetrics.descent);

            this.para.lines[curLine].ranges[curRange].xVisible = x;

            // if ( 0 === curRange )
            // 	this.para.lines[curLine].ranges[0].x = X - PRSW.xStart;

            for ( let pos = startPos; pos <= endPos; pos++ ) {

                const item = this.para.content[pos];
                if ( item && !item.isHidden() ) {
                  item.recalculateRangeSpaces(oPRSA, curLine, curRange, curPage);

                  if (!(oPRSA.recalcResult & RecalcResultType.RecalResultNextElement)) {

                      oPRSW.recalcResult = oPRSA.recalcResult;
                      return oPRSA.recalcResult;
                  }
              }
            }
        }

        return oPRSA.recalcResult;
    }

    /**
     * 计算portion在当前行的当前range的可编辑区域，如果放不下则断行，同时记录当前range在portion中的位置，
     * @param curRange
     * @param curLine
     * @param curPage
     * @param rangesCount
     * @param oPRS
     * @param paraPr
     */
    private recalculateRange(curRange: number, curLine: number, curPage: number, rangesCount: number,
                             oPRS: ParagraphRecalculateStateWrap, paraPr: ParaProperty): void {

        let startPortionPos = 0;
        if ( 0 === curLine ) {
            startPortionPos = 0;
        } else if ( 0 < curRange ) {
            startPortionPos = this.para.lines[curLine].ranges[curRange - 1].endPos;
        } else {
            startPortionPos = this.para.lines[curLine - 1].ranges[0].endPos;
        }

        const line = this.para.lines[curLine];
        const range = line.ranges[curRange];

        this.para.lines[curLine].setRangeStartPos(curRange, startPortionPos);

        // todo ：首行缩进, 且缩进值 ！=0，空行
        // if ( true === oPRS.bUseFirstLine && 0 !== curRange && true === oPRS.bEmptyLine ){
        if ( true === oPRS.bUseFirstLine && true === oPRS.bEmptyLine ) {
            // console.log("firstline")
            // console.log(paraPr)

            // get bUseFirstLine value
            let firstLineIndentation = paraPr.paraInd.firstLine;

            // cm to px
            firstLineIndentation = getPxForMM(firstLineIndentation);

            range.x += firstLineIndentation;

            // 首行缩进值
            /*if ( paraPr.ind.firstLine >= 0 )
            {
              range.x += paraPr.ind.firstLine;
            }
            else {
              range.x += (paraPr.ind.firstLine + paraPr.ind.left);
            }*/
        }

        // // 左缩进
        let leftIndentation = paraPr.paraInd.left;
        leftIndentation = getPxForMM(leftIndentation);
        range.x += leftIndentation;

        const x = range.x;
        const xEnd = ( curRange === rangesCount ? oPRS.xLimit : oPRS.ranges[curRange].xEnd);

        oPRS.resetRange(x, xEnd);

        let curPortionPos = startPortionPos;
        const contentLen = this.para.content.length;

        for (; curPortionPos < contentLen; curPortionPos++) {

            const paraPortion = this.para.content[curPortionPos];

            // todo：公式

            if ( ( 0 === curPortionPos && 0 === curLine && 0 === curRange) || curPortionPos !== startPortionPos ) {
                paraPortion.recalculateReset(curLine, curRange);
            }

            oPRS.updateCurPos(curPortionPos, 0);
            oPRS.lastItem.portionPos = curPortionPos;
            paraPortion.recalculateRange( oPRS, paraPr, 1, curPage );

            const newControlFixed = paraPortion.checkMinFixedLength();
            if (newControlFixed) {
              newControlFixed.startPortionIndex = curPortionPos;
            }
            oPRS.setNewControlFixedLength(newControlFixed);

            // todo：公式
            if ( true === oPRS.bNewRange ) {
                break;
            }
        }

        if ( curPortionPos >= contentLen ) {
            curPortionPos = contentLen - 1;
        }

        // 换行: 有2中情况
        // 1. 本行刚好排满，且本行是段落最后一行，此段排版结束
        // 2. 本行需要断行，要开始下一行的排版
        if ( oPRS.recalcResult & RecalcResultType.RecalResultNextLine ) {
            if (true === oPRS.bMoveToLBP) {
                this.recalculateRangeEndPos( oPRS, 0 );
            } else {
                this.para.lines[curLine].setRangeEndPos(curRange, curPortionPos);
            }
        }
    }

    /**
     * 对当前行排版结束后，保存当前range在portion中的结束位置
     * @param oPRS
     * @param depth
     */
    private recalculateRangeEndPos( oPRS: ParagraphRecalculateStateWrap, depth: number = 0 ): void {
        const curLine = oPRS.line;
        const curRange = oPRS.range;
        const curPortionPos = oPRS.lineBreakPos.get(depth);

        this.para.lines[curLine].setRangeEndPos(curRange, curPortionPos);
        this.para.content[curPortionPos].recalculateSetRangeEndPos(oPRS, depth + 1);
    }

    /**
     * 将当前行移到下一页
     * @param curLine
     * @param curPage
     * @param oPRS
     * @param paraPr
     */
    private recalculateMoveLineToNextPage(curLine: number, curPage: number,
                                          oPRS: ParagraphRecalculateStateWrap, paraPr: ParaProperty): boolean {
        // 上一页下边界的位置
        this.para.pages[curPage].bounds.bottom = oPRS.linePrevBottom;
        this.para.pages[curPage].setEndLine( curLine - 1 );

        oPRS.recalcResult = RecalcResultType.RecalResultNextPage;

        // 段落首行
        if ( 0 === curLine ) {
            this.para.lines[-1] = new ParaLine();
            // oPRS.recalcResult |= RecalcResultType.RecalResult_LastFromNewPage;
        } else {
            // oPRS.recalcResult |= RecalcResultType.RecalResult_NewPage;
        }

        return false;
    }
}
