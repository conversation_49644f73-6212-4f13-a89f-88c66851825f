import { ICustomToolbarItem } from "./commonDefines";


// 自定义工具栏配置
const customToolbar: ICustomToolbarItem[] = [];

export function getCustomToolbar(): ICustomToolbarItem[] {
    return [...customToolbar];
}

export function setCustomToolbar(newItems: ICustomToolbarItem[]): number {
    const res = customToolbar.splice(0, customToolbar.length, ...newItems);
    return res.length || newItems.length;
}