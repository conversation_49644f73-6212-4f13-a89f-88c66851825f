import { XmlComponent } from '../../xml-components';
import { ParagraphPropertiesDefaults } from './paragraph-properties';
import { RunPropertiesDefaults } from './run-properties';
import { HeaderPropertiesDefaults } from './header-properties';
import { FooterPropertiesDefaults } from './footer-properties';
import { IDefaultParaProperty, IDefaultTextProperty } from '../../../../../model/DocumentCore';

export class DocumentDefaults extends XmlComponent {
    private readonly runPropertiesDefaults: RunPropertiesDefaults;
    private readonly paragraphPropertiesDefaults: ParagraphPropertiesDefaults;
    private readonly headerPropertiesDefaults: HeaderPropertiesDefaults;
    private readonly footerPropertiesDefaults: FooterPropertiesDefaults;

    constructor() {
        super('w:docDefaults');
        this.runPropertiesDefaults = new RunPropertiesDefaults();
        this.paragraphPropertiesDefaults = new ParagraphPropertiesDefaults();
        this.headerPropertiesDefaults = new HeaderPropertiesDefaults();
        this.footerPropertiesDefaults = new FooterPropertiesDefaults();

        this.root.push(this.runPropertiesDefaults);
        this.root.push(this.paragraphPropertiesDefaults);

        // exist header / footer?
        this.root.push(this.headerPropertiesDefaults);
        this.root.push(this.footerPropertiesDefaults);
    }

    public setDefaultTextProperty(DefaultTextProperty: IDefaultTextProperty): void {
        this.runPropertiesDefaults.size(DefaultTextProperty.fontSize);
        this.runPropertiesDefaults.font(DefaultTextProperty.fontFamily);
    }

    public setDefaultParaProperty(DefaultParaProperty: IDefaultParaProperty): void {
        this.paragraphPropertiesDefaults.alignment(DefaultParaProperty.alignment);
        this.paragraphPropertiesDefaults.paraSpacing(DefaultParaProperty.paraSpacing);
    }
}
