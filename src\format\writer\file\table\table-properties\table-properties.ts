import { XmlComponent } from '../../xml-components';
// import { WidthType } from '../table-cell';
// import { PreferredTableWidth } from './table-width';
import { CustomProperty } from '../../paragraph/contentControl/customProperty';
import { ICustomProps } from '../../../../../common/commonDefines';

export class TableProperties extends XmlComponent {

    private readonly customProperty: CustomProperty;

    constructor() {
        super('w:tblPr');
        this.customProperty = new CustomProperty();
    }

    // public setWidth(type: WidthType, w: number | string): TableProperties {
    //     this.root.push(new PreferredTableWidth(type, w));
    //     return this;
    // }

    public setFixedRowHeight(val: string): TableProperties {
        this.root.push(new FixedRowHeight(val));
        return this;
    }

    public setFixedColWidth(val: string): TableProperties {
        this.root.push(new FixedColWidth(val));
        return this;
    }

    public setAddRowProtect(val: string): TableProperties {
        this.root.push(new AddRowProtect(val));
        return this;
    }

    public setDelRowProtect(val: string): TableProperties {
        this.root.push(new DelRowProtect(val));
        return this;
    }

    public setRepeatHeader(val: string): TableProperties {
        this.root.push(new RepeatHeader(val));
        return this;
    }

    public setDeleteProtect(val: string): TableProperties {
        this.root.push(new DeleteProtect(val));
        return this;
    }

    public setHeaderReadOnly(val: string): TableProperties {
        this.root.push(new HeaderReadOnly(val));
        return this;
    }

    public setEditProtect(val: string): TableProperties {
        this.root.push(new EditProtect(val));
        return this;
    }

    public enableRowAction(val: string): TableProperties {
        if (val) {
            this.root.push(new EnableRowAction(val));
        }

        return this;
    }

    public setCellLeft(val: string): TableProperties {
        this.root.push(new CellLeft(val));
        return this;
    }

    public setCellRight(val: string): TableProperties {
        this.root.push(new CellRight(val));
        return this;
    }

    public setCellTop(val: string): TableProperties {
        this.root.push(new CellTop(val));
        return this;
    }

    public setCellBottom(val: string): TableProperties {
        this.root.push(new CellBottom(val));
        return this;
    }

    public addCustomProperty(properties: Map<string, ICustomProps>): TableProperties {
        this.root.push(this.customProperty);

        this.customProperty.addCustomProperties(properties);
        return this;
    }

    public setColumnIDs(columnIDs: string[]): TableProperties {
        this.root.push(new ColumnIDs(columnIDs.join(',')));
        return this;
    }

    public setFixedLeft(val: string): TableProperties {
        this.root.push(new FixedLeft(val));
        return this;
    }

    public setFixedRight(val: string): TableProperties {
        this.root.push(new FixedRight(val));
        return this;
    }

    public setBFixedHeader(val: string): TableProperties {
        this.root.push(new BFixedHeader(val));
        return this;
    }

}

export class FixedRowHeight extends XmlComponent {
    constructor(flag: string) {
        super('fixedRowHeight');
        this.root.push(flag);

    }
}

// tslint:disable-next-line: max-classes-per-file
export class FixedColWidth extends XmlComponent {
    constructor(flag: string) {
        super('fixedColWidth');
        this.root.push(flag);
    }
}

// tslint:disable-next-line: max-classes-per-file
export class AddRowProtect extends XmlComponent {
    constructor(flag: string) {
        super('addRowProtect');
        this.root.push(flag);
    }
}

// tslint:disable-next-line: max-classes-per-file
export class DelRowProtect extends XmlComponent {
    constructor(flag: string) {
        super('delRowProtect');
        this.root.push(flag);
    }
}

// tslint:disable-next-line: max-classes-per-file
export class DeleteProtect extends XmlComponent {
    constructor(flag: string) {
        super('deleteProtect');
        this.root.push(flag);
    }
}

// tslint:disable-next-line: max-classes-per-file
export class HeaderReadOnly extends XmlComponent {
    constructor(flag: string) {
        super('headerReadOnly');
        this.root.push(flag);
    }
}

// tslint:disable-next-line: max-classes-per-file
export class RepeatHeader extends XmlComponent {
    constructor(flag: string) {
        super('repeatHeader');
        this.root.push(flag);
    }
}

// tslint:disable-next-line: max-classes-per-file
export class EditProtect extends XmlComponent {
    constructor(flag: string) {
        super('editProtect');
        this.root.push(flag);
    }
}

// tslint:disable-next-line: max-classes-per-file
export class EnableRowAction extends XmlComponent {
    constructor(flag: string) {
        super('enableRowAction');
        this.root.push(flag);
    }
}

// tslint:disable-next-line: max-classes-per-file
export class CellLeft extends XmlComponent {
    constructor(value: string) {
        super('cellLeft');
        this.root.push(value);
    }
}

// tslint:disable-next-line: max-classes-per-file
export class CellRight extends XmlComponent {
    constructor(value: string) {
        super('cellRight');
        this.root.push(value);
    }
}

// tslint:disable-next-line: max-classes-per-file
export class CellTop extends XmlComponent {
    constructor(value: string) {
        super('cellTop');
        this.root.push(value);
    }
}

// tslint:disable-next-line: max-classes-per-file
export class CellBottom extends XmlComponent {
    constructor(value: string) {
        super('cellBottom');
        this.root.push(value);
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ColumnIDs extends XmlComponent {
    constructor(value: string) {
        super('columnIDs');
        this.root.push(value);
    }
}

// tslint:disable-next-line: max-classes-per-file
export class FixedLeft extends XmlComponent {
    constructor(value: string) {
        super('fixedLeft');
        this.root.push(value);
    }
}

// tslint:disable-next-line: max-classes-per-file
export class FixedRight extends XmlComponent {
    constructor(value: string) {
        super('fixedRight');
        this.root.push(value);
    }
}

// tslint:disable-next-line: max-classes-per-file
export class BFixedHeader extends XmlComponent {
    constructor(value: string) {
        super('bFixedHeader');
        this.root.push(value);
    }
}
