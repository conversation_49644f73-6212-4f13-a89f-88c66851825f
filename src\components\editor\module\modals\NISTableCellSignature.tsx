import * as React from 'react';
import Dialog from '../../ui/Dialog';
import Button from '../../ui/Button';
import Checkbox from '../../ui/CheckboxItem';
import { CustomPropertyElementType } from '../../../../common/commonDefines';
import CustomPropertyBtn from './CustomProperty';
import { IFRAME_MANAGER } from '../../../../common/IframeManager';
import Input from '../../ui/Input';
import { NISTableCellCommon } from './NISTableCellCommonProps';

interface IDialogProps {
    documentCore: any;
    visible: boolean;
    id?: string;
    close: (name: string | number, bRefresh?: boolean) => void;
    refresh: (bRefresh: boolean, timeout?: number) => void;
}

interface IState {
    bRefresh: boolean;
}

export default class NISTableCellSignature extends React.Component<IDialogProps, IState> {
    private cell: any;
    private visible: any;
    private bCustomProperty: boolean;
    private nisProps: any;
    constructor(props: any) {
        super(props);
        this.cell = {
            bProtected: false,
            customProperty: undefined,
            serialNumber: undefined,
            visible: undefined,
        };
        this.state = {
            bRefresh: false,
        };
        this.nisProps = {};
        this.visible = this.props.visible;
        this.setDialogValue();
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        this.visible = nextProps.visible;
    }

    public render(): any {

        return (
            <Dialog
                id={this.props.id}
                visible={this.visible}
                width={320}
                // close={this.close}
                open={this.open}
                preventDefault={false}
                title='签名单元格'
                confirm={this.confirm}
                footer={this.renderFooter()}
            >
                <div>
                    <div className='editor-line'>
                        <div className='w-70'>内部名称</div>
                        <div className='right-auto'>
                            <Input
                                name='serialNumber'
                                value={this.cell.serialNumber}
                                onChange={this.onChange}
                                readonly={true}
                                placeholder={''}
                                disabled={true}
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <span className='title'>属性：</span>
                    </div>
                    <div className='editor-line'>
                        <Checkbox
                            name='bProtected'
                            value={this.cell.bProtected}
                            disabled={false}
                            onChange={this.onChange}
                        >
                            禁止编辑
                        </Checkbox>
                        <Checkbox
                            name='visible'
                            value={this.cell.visible}
                            disabled={false}
                            onChange={this.onChange}
                        >
                            网格线
                        </Checkbox>
                    </div>
                    <NISTableCellCommon prop={this.nisProps}/>
                    <div className='editor-line'>
                        <CustomPropertyBtn
                            name='customProperty'
                            properties={this.cell.customProperty}
                            documentCore={this.props.documentCore}
                            onChange={this.onChange}
                            close={this.onClose}
                            id='bCustomProperty'
                            visible={this.bCustomProperty}
                            type={CustomPropertyElementType.TableCell}
                        />
                    </div>
                </div>
            </Dialog>
        );
    }

    private renderFooter(): any {
        return (
            <span>
                <Button onClick={this.close}>取消</Button>
                <Button type='primary' onClick={this.confirm}>确认</Button>
            </span>
        );
    }

    private setDialogValue(): void {
        const cellProps = this.props.documentCore.getTableCellProps();
        if (cellProps) {
            this.cell.bProtected = cellProps.bProtected;
            this.nisProps = cellProps.nisProperty;
            this.cell.customProperty = cellProps.nisProperty ? cellProps.nisProperty.customProperty : undefined;
            this.cell.serialNumber = cellProps.nisProperty ? cellProps.nisProperty.serialNumber : undefined;
            this.cell.visible = cellProps.nisProperty?.gridLine?.visible;
        }
    }

    private open = (): void => {
        // const table = this.table;
        this.setDialogValue();
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private close = (bRefresh?: boolean): void => {
        this.props.close(this.props.id, bRefresh);
        this.visible = false;
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private onClose = (id: string, bRefresh: boolean): void => {
        this[id] = false;
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private onChange = (value: any, name: string): void => {
        this.cell[name] = value;
    }

    private confirm = (id?: any): void => {
        const { documentCore } = this.props;
        IFRAME_MANAGER.setDocId(documentCore.getCurrentId());
        documentCore.setTableCellProps({bProtected: this.cell.bProtected,
            nisProperty: {
                customProperty: this.cell.customProperty,
                serialNumber: this.cell.serialNumber,
                gridLine: {
                    visible: this.cell.visible
                }
            }
        });

        this.close(true);
    }

}
