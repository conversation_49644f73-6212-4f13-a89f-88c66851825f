import * as React from 'react';
import {CascadeEdit} from './CascadeEdit';
import {EventEdit} from './EventEdit';
import Checkbox from '../../../ui/CheckboxItem';
import { CodeAndValue, NewControlType } from '../../../../../common/commonDefines';
import SearchID from './SearchID';
import {
    GlobalEvent as gEvent
} from '../../../../../common/GlobalEvent';
import { message } from '../../../../../common/Message';

interface IProps {
    width: number;
    visible?: boolean;
}
export default class RightCascadeManagerUI extends React.Component<IProps, {}> {
    private visible: boolean;
    private type: number;
    private cascades: any;
    private events: any;
    private documentCore: any;
    private host: any;
    private mainRef: any;
    private cascadeRef: any;
    private eventRef: any;
    private cascadeVisible: boolean;
    private eventVisible: boolean;
    private disabled: boolean;
    private searchRef: any;
    private searchData: any;
    private newControl: any;
    private newControlName: string;
    private addCascase: any;
    private scrollbtn: any;
    private startX: number;
    private width: number;

    constructor(props: any) {
        super(props);
        this.mainRef = React.createRef();
        this.cascadeRef = React.createRef();
        this.eventRef = React.createRef();
        this.searchRef = React.createRef();
        this.scrollbtn = React.createRef();
    }

    public render(): any {
        return (
        <div className='cascade-right' ref={this.mainRef}  style={{width: this.props.width}}>
            <div>
                <span>当前选中元素</span>
                <label>{this.newControlName}</label>
            </div>
            <dl>
                <dt  className='title'>
                    <Checkbox
                        value={this.cascadeVisible}
                        name='cascadeVisible'
                        onChange={this.onChange}
                        disabled={!this.newControlName}
                    >
                        普通级联
                    </Checkbox>
                </dt>
                <dd>
                    {this.renderCascades()}
                </dd>
                <dt className='title'>
                    <Checkbox
                        value={this.eventVisible}
                        name='eventVisible'
                        onChange={this.onChange}
                        disabled={!this.newControlName}
                    >
                        求和级联
                    </Checkbox>
                </dt>
                <dd>
                    {this.renderEvents()}
                </dd>
            </dl>
            <SearchID ref={this.searchRef}/>
            <div className='scroll-btn' ref={this.scrollbtn}></div>
        </div>
        );
    }

    public componentDidMount(): void {
        const dom: HTMLDivElement = this.scrollbtn.current;
        if (dom) {
            dom.addEventListener('mousedown', this.mouseDown);
            document.addEventListener('mouseup', this.mouseUp);
            this.mainRef.current.parentNode.addEventListener('mousemove', this.mouseMove);
        }
        // resize
        // const container = this.mainRef.current as HTMLDivElement;
        // const curWidth = container.getBoundingClientRect().width; 
        // const scale = window.devicePixelRatio || 1;
        // const width = Math.round(curWidth / scale);
        // container.style.width = width + 'px';
    }

    public componentWillUnmount(): void {
        const dom: HTMLDivElement = this.scrollbtn.current;
        if (dom) {
            dom.removeEventListener('mousedown', this.mouseDown);
            document.removeEventListener('mouseup', this.mouseUp);
            this.mainRef.current.parentNode.removeEventListener('mousemove', this.mouseMove);
        }
    }

    public open(name: string, type: string): void {
        this.newControlName = name;
        const newControl = this.newControl = this.documentCore.getNewControlByName(name);
        if (newControl) {
            this.type = newControl.getType();
            this.cascades = newControl.getCascades();
            this.events = newControl.getEvent();
            this.disabled = ![NewControlType.TextBox, NewControlType.NumberBox].includes(this.type);
            // this.searchRef.current.onClose();
            const bChange = true;
            switch (type) {
                case '1': {
                    this.eventVisible = false;
                    this.cascadeVisible = true;
                    break;
                }
                case '2': {
                    this.cascadeVisible = false;
                    this.eventVisible = true;
                    break;
                }
            }
            if (bChange) {
                this.setState({}, () => {
                    this.openComponent();
                });
            } else {
                this.setState({});
                this.openComponent();
            }
            // gEvent.setEvent(this.host.docId, 'CascadeTipVisible', false);
        }
    }

    public close = (): void => {
        this.eventVisible = false;
        this.cascadeVisible = false;
        this.cascadeRef.current?.close();
        this.eventRef.current?.close();
        this.searchRef.current?.onClose();
        this.newControlName = undefined;
    }

    public init(host: any, callback: any): void {
        if (!host) {
            return;
        }
        this.addCascase = callback;
        this.host = host;
        this.documentCore = host.state.documentCore;
        this.searchRef.current.init(host, this.searchOnChange);
    }

    public validData(): boolean {
        let result: boolean = true;
        if (this.cascades && this.cascadeRef.current) {
            result = this.cascadeRef.current.validData() && result;
        }

        if (this.events && this.eventRef.current) {
            const elem = this.eventRef.current;
            const documentCore = this.documentCore;
            if (this.events.key === CodeAndValue.Expression) {
                result = result && elem.validateMixExpression(documentCore.checkNewControlsType.bind(documentCore));
            } else {
                result = result && elem.validData();
                const res = documentCore.checkNewControlEvent(this.events);
                if (res) {
                    message.error(res, {time: 10000});
                    return false;
                }
            }
        }
        this.searchRef.current.onClose();
        return result;
    }

    private mouseDown = (e) => {
        this.startX = e.x;
        this.width = this.mainRef.current.clientWidth;
        this.mainRef.current.parentNode.className += ' move';
    }

    private mouseUp = (e) => {
        if (this.startX === undefined) {
            return;
        }
        this.startX = undefined;
        const dom = this.mainRef.current.parentNode;
        dom.className = dom.className.replace(' move', '');
        // const sub = e.x - 
    }

    private mouseMove = (e) => {
        if (this.startX === undefined) {
            return;
        }

        const min = 282;
        const max = 622;
        const width = this.startX - e.x + this.width;
        if (width > max || width < min) {
            return;
        }
        this.mainRef.current.style.width = width + 'px';
    }

    private openComponent(bCascades: boolean = true, bEvent: boolean = true): void {
        if (bCascades && this.cascadeRef.current) {
            this.cascadeRef.current.open(this.cascades, this.type, this.setCascades, this.newControl.items);
        }
        if (bEvent && this.eventRef.current) {
            this.eventRef.current.open(this.events, this.disabled, this.setEvent);
        }
    }

    private setCascades = (datas: any[], type?: number): void => {
        if (!this.newControl || this.cascades && this.cascades.length && !type) {
            return;
        }
        this.cascades = datas;
        this.newControl.setCascades(datas);
        this.addCascase(this.newControl.getNewControlName(), null, type);
    }

    private setEvent = (data: any, type?: number): void => {
        if (!this.newControl || this.events && !type) {
            return;
        }

        this.newControl.setEvent(data);
        this.events = data;
        this.addCascase(null, this.newControl.getNewControlName(), type);
    }

    private updateEventSource(): void {
        if (!this.newControl || !this.events) {
            return;
        }

        this.newControl.updateEventSource();
    }

    /** 查询输入框，内容变更时触发 */
    private searchOnChange = (value: any) => {
        const data = this.searchData;
        if (this.events?.key === CodeAndValue.Expression) {
            this.events.expression = value;
        } else {
            if (!data) {
                return;
            }
            if (data.action !== undefined) {
                data.target = value;
            } else {
                data.source = value;
                this.setEvent(this.events);
                this.updateEventSource();
            }
        }
        this.setState({});
    }

    private onChange = (value: any, name: string) => {
        this[name] = value;
        this.setState({}, () => {
            if (value === true) {
                if (name === 'cascadeVisible') {
                    this.openComponent(true, false);
                } else {
                    this.openComponent(false, true);
                }
            }
        });
    }

    private searchClick = (e: any, data: any): any => {
        if( this.host != null )
            gEvent.setEvent(this.host.docId, 'CascadeTipVisible', true);
        // search-btn
        const target: HTMLDivElement = e.target;
        // cascade-right
        const offsetParent: HTMLDivElement = target.offsetParent as any;
        let x = Math.max(offsetParent.clientWidth - target.offsetLeft - 40, 0);
        if (x > 190) {
            const dom = this.mainRef.current;
            // if (dom.clientWidth - 340 > x) {

            // }
            x = Math.min(dom.clientWidth - 330, x);
            x = Math.max(0, x);
        }
        const bound = offsetParent.getBoundingClientRect();
        let curY = e.y - bound.top;
        let y = target.offsetTop;
        const clientY = bound.height - 75;
        if (clientY < curY) {
            y -= 110;
        } else {
            y += 36;
        }
        // const x = e.clientX - 400;
        // const y = e.clientY + 26;
        this.searchData = data;
        this.searchRef.current.open(x, y, data, this.events, e);
    }

    private renderCascades(): any {
        if (!this.cascadeVisible) {
            return null;
        }

        return (
            <CascadeEdit
                documentCore={this.documentCore}
                type={this.type}
                properties={this.cascades}
                visible={this.visible}
                ref={this.cascadeRef}
                list={this.newControl?.newControlItems}
                onClick={this.searchClick}
            />
        );
    }

    private renderEvents(): any {
        if (!this.eventVisible) {
            return null;
        }

        return (
            <EventEdit
                ref={this.eventRef}
                hasMix={this.newControl?.isNumberBox()}
                onClick={this.searchClick}
            />
        );
    }
}
