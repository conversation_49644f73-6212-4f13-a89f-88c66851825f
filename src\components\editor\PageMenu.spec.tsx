// import * as React from 'react'; 
// import { DocumentCore } from "../../model/DocumentCore";
// import * as ReactTestUtils from 'react-dom/test-utils'; // ES6
// import { configure, shallow , render, mount} from 'enzyme';
// import * as Adapter from 'enzyme-adapter-react-16';
// import jasmineEnzyme from 'jasmine-enzyme';

// import PageMenu from './PageMenu';

// // seems not work for importing vars, TODO if have time
// // import {colorConfig} from './PageConfig';
// // import {spacingConfig} from './PageConfig';


// configure({adapter: new Adapter()});


// describe('<PageMenu />', () => {
//     let wrapper;
//     const documentCore = new DocumentCore();
//     const { pageProperty } = documentCore.render();
//     let defaultComponent = (<PageMenu 
//         equationType={0}
//         getParagraphPro={null} 
//         pageProperty={pageProperty} 
//         // colorConfig={{menu: null, dropdown: null, toolbar: null}} 
//         // spacingConfig={{menuHeight: null}} 
//         changePagePro={null} 
//         changeParagraphPro = {null}
//         refresh={null}
//         handleClickOutsideDropdown = {null}
//         clickOutsideDropdown = {false}
//         testDocument = {null}
//         imageConfigModalType = {null}
//         handleModalState = {null}
//         xmlProps={null}
//     />);

//     beforeEach(() => {
//         jasmineEnzyme();
//     });

//     it('PageMenu render without crash', () => {
        
//         // console.log(pageProperty);
//         shallow(defaultComponent);
//     });

//     it('PageMenu\'s spacing and color is configurable', () => {
//         let colorConfig = {
//             menu: 'yellow',
//             dropdown: 'black',
//           }
          
//         let spacingConfig = {
//             menuHeight: '0.5cm',
//         };
//         wrapper = shallow(<PageMenu equationType={0} pageProperty={pageProperty} changePagePro={null} refresh={null} imageConfigModalType ={null} handleModalState = {null} xmlProps={null}/>);
        
//         //menu
//         expect(wrapper.get(0).props.style.backgroundColor).toBe(colorConfig.menu);
//         expect(wrapper.get(0).props.style.height).toBe(spacingConfig.menuHeight);
//         expect(wrapper).toHaveStyle('height', spacingConfig.menuHeight);
        
//         //dropdown
//         expect(wrapper.find('.dropdown').get(0).props.style.backgroundColor).toBe(colorConfig.dropdown);
//     });

    
// });