import * as React from 'react';
import Dialog from '../../ui/Dialog';
import {LineSpacingType, ToolbarIndex} from '../../../../common/commonDefines';
import Input from '../../ui/Input';
import Checkbox from '../../ui/CheckboxItem';
import Button from '../../ui/Button';
import SelectUI from '../../ui/select/Select';
import Radio from '../../ui/Radio';
import { message } from '../../../../common/Message';
import { getPxForMM } from '../../../../model/core/util';
import {
    GlobalEvent as gEvent,
    GlobalEventName as gEventName,
} from '../../../../common/GlobalEvent';

interface IProps {
    host?: any;
    documentCore: any;
    visible: boolean;
    id: string;
    close: (name: string | number, bRefresh?: boolean) => void;
}

interface IState {
    bRefresh: boolean;
}

interface IParaProperty {
    alignment: number;
    bPageBreakBefore: boolean;
    paraLeftInd: number;
    paraInd: number;
    paraIndType: number;
    paraSpacing: number;
    paraSpacingType: number;
    bWordWrap: boolean;
}

interface ISpaceProp {
    key: string;
    value: number;
    disabled?: boolean;
    bDisabled?: boolean;
    cell: string;
    defaultValue: number;
}

interface IPropNameKey {
    key: string;
    value: any;
    bDisabled?: boolean;
    disabled?: boolean;
}

export default class Paragraph extends React.Component<IProps, IState> {
    private host: any;
    private visible: boolean;
    private paraPro: IParaProperty;
    private paraSpacingTypes: ISpaceProp[];
    private paraProUI: {
        disabled: boolean,
        cell: string,
    };
    private paraIndTypes: IPropNameKey[];
    private alignments: IPropNameKey[];
    private pageBreakDisabled: boolean = true;
    constructor(props: any) {
        super(props);
        this.host = props.host;
        this.state = {
            bRefresh: false,
        };

        this.paraSpacingTypes = [
            {key: '单倍行距', value: LineSpacingType.Single, bDisabled: true, cell: '倍', defaultValue: 1},
            {key: '1.5倍行距', value: LineSpacingType.SingeHalf, bDisabled: true, cell: '倍', defaultValue: 1.5},
            {key: '2倍行距', value: LineSpacingType.Double, bDisabled: true, cell: '倍', defaultValue: 2},
            {key: '多倍行距', value: LineSpacingType.Multi, bDisabled: false, cell: '倍', defaultValue: 3},
            {key: '最小值', value: LineSpacingType.Min, bDisabled: true, cell: '倍', defaultValue: 1},
            // {key: '固定值', value: LineSpacingType.Fixed, bDisabled: false, cell: 'cm', defaultValue: 0.55},
            {key: '', value: -1, disabled: true, bDisabled: true, cell: '', defaultValue: undefined},
        ];

        this.paraProUI = {disabled: true, cell: '倍'};
        this.paraPro = {
            alignment: undefined,
            bPageBreakBefore: undefined,
            paraLeftInd: undefined,
            paraInd: undefined,
            paraIndType: undefined,
            paraSpacing: undefined,
            paraSpacingType: undefined,
            bWordWrap: undefined,
        };

        this.paraIndTypes = [
            {
                key: '首行缩进',
                value: 1,
            },
            {
                key: '悬挂缩进',
                value: 2,
            },
            {
                key: '',
                value: -1,
                disabled: true,
            },
        ];
        this.alignments = [
            {
                key: '左对齐',
                value: 0,
            },
            {
                key: '右对齐',
                value: 2,
            },
            {
                key: '两端对齐',
                value: 3,
            },
        ];
        this.visible = this.props.visible;
    }

    public render(): any {
        return (
            <Dialog
                visible={this.visible}
                width={350}
                open={this.open}
                title='段落设置'
                footer={this.renderFooter()}
                id={this.props.id}
            >
                <div>
                    <div className='editor-line'>
                        <span className='title'>缩进</span>
                    </div>
                    <div className='editor-line'>
                        <div className='w-120'>左缩进</div>
                        <div className='right-auto-120'>
                            <Input
                                onChange={this.onChange}
                                value={this.paraPro.paraLeftInd}
                                name='paraLeftInd'
                                type='number'
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <div className='w-120'>
                            <SelectUI
                                name='paraIndType'
                                onChange={this.onChange}
                                data={this.paraIndTypes}
                                value={this.paraPro.paraIndType}
                            />
                        </div>
                        <div className='right-auto-120'>
                            <Input
                                disabled={this.paraPro.paraIndType === undefined}
                                onChange={this.onChange}
                                value={this.paraPro.paraInd}
                                name='paraInd'
                                type='number'
                                min={0}
                                renderAppend={this.renderCell.bind(this, 'cm')}
                            />
                        </div>
                    </div>

                    <div className='editor-line'>
                        <span className='title'>行距</span>
                    </div>
                    <div className='editor-line'>
                        <div className='w-120'>
                            <SelectUI
                                name='paraSpacingType'
                                onChange={this.onChange}
                                data={this.paraSpacingTypes}
                                value={this.paraPro.paraSpacingType}
                            />
                        </div>
                        <div className='right-auto-120'>
                            <Input
                                disabled={this.paraProUI.disabled}
                                onChange={this.onChange}
                                type='number'
                                value={this.paraPro.paraSpacing}
                                name='paraSpacing'
                                renderAppend={this.renderCell.bind(this, this.paraProUI.cell)}
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <span className='title'>对齐</span>
                    </div>
                    <div className='editor-line'>
                        <Radio
                            data={this.alignments}
                            name='alignment'
                            onChange={this.onChange}
                            value={this.paraPro.alignment}
                        />
                    </div>
                    <div className='editor-line'>
                        <span className='title'>特殊</span>
                    </div>
                    <div className='editor-line'>
                        <Checkbox
                            name='bPageBreakBefore'
                            value={this.paraPro.bPageBreakBefore}
                            onChange={this.onChange}
                            disabled={this.pageBreakDisabled}
                        >
                            换页符
                        </Checkbox>
                    </div>
                    <div className='editor-line'>
                        <Checkbox
                            name='bWordWrap'
                            value={this.paraPro.bWordWrap}
                            onChange={this.onChange}
                        >
                            允许英文中间换行
                        </Checkbox>
                    </div>
                    <div className='editor-line'>
                        <div className='line-active-btn' onClick={this.removeEmptyParagragh}>
                            删除表格，区域末尾的空段
                        </div>
                    </div>
                </div>
            </Dialog>
        );
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        this.visible = nextProps.visible;
    }

    private renderCell(cell: string): string {
        return cell;
    }

    private renderFooter(): any {
        return (
            <span>
                <Button onClick={this.close}>取消</Button>
                <Button type='primary' onClick={this.confirm}>确认</Button>
            </span>
        );
    }

    private close = (bRefresh?: boolean): void => {
        this.visible = false;
        this.setState({ bRefresh: !this.state.bRefresh });
        this.props.close(this.props.id, bRefresh);
    }

    private setParaPro(): any {
        const para = this.paraPro;
        if (typeof para.paraLeftInd !== 'number' || isNaN(para.paraLeftInd)) {
            para.paraLeftInd = 0;
        } else {
            para.paraLeftInd = para.paraLeftInd * 10;
        }
        if (typeof para.paraInd !== 'number'  || isNaN(para.paraInd)) {
            para.paraInd = 0;
        } else {
            para.paraInd = para.paraInd * 10;
        }
        if (para.paraSpacingType === -1) {
            para.paraSpacingType = undefined;
            para.paraSpacing = undefined;
        }
        // 悬挂缩进
        if ([1, 2].includes(para.paraIndType)) {
            if (para.paraIndType === 2 && para.paraInd) {
                if (para.paraLeftInd !== undefined) {
                    para.paraLeftInd += para.paraInd;
                }
                para.paraInd = -para.paraInd;
            }
        } else {
            para.paraInd = 0;
            para.paraIndType = undefined;
        }
    }

    private confirm = (id: any): void => {
        const documentCore = this.props.documentCore;
        const para = documentCore.getCurrentParagraph();

        if ( para && null != this.paraPro.paraInd ) {
            const bounds = para.getPageBounds(0);
            const page = para.getPages()[0];
            if ( page.xLimit < bounds.left + getPxForMM(this.paraPro.paraInd * 10) ) {
                message.error('缩进尺寸太大，请重新输入');
                return;
            }
        }
        this.setParaPro();
        documentCore.setParagraphProperty1(this.paraPro);
        this.close(true);
    }

    // private paragraphOpen(): void {
    //     this.setParaPro();
    // }

    private open = (): void => {
        const documentCore = this.props.documentCore;
        const para = documentCore.getSelectedParaPro();
        if (para.paraLeftInd !== undefined) {
            para.paraLeftInd = para.paraLeftInd / 10 || 0;
        }
        if (para.paraInd !== undefined) {
            para.paraInd = para.paraInd / 10 || 0;
        }
        // 悬挂缩进
        if (para.paraIndType === 2) {
            para.paraLeftInd += para.paraInd;
            para.paraInd = -para.paraInd;
        }
        para.bPageBreakBefore = para.bPageBreakBefore || false;
        this.pageBreakDisabled = true;
        if (para.bPageBreakBefore === true) {
            this.pageBreakDisabled = false;
        }
        let lineSpaceDisabled = true;
        let cell: string = '倍';
        switch (para.paraSpacingType) {
            case LineSpacingType.Single:
            case LineSpacingType.Min:
                para.paraSpacing = 1;
                break;

            case LineSpacingType.SingeHalf:
                para.paraSpacing = 1.5;
                break;

            case LineSpacingType.Double:
                para.paraSpacing = 2;
                break;

            case LineSpacingType.Multi:
                lineSpaceDisabled = false;
                // need to convert to times
                // let ratio = para.paraSpacing;
                // ratio = (ratio - 1) / 0.473; // 2 / 0.15;
                // para.paraSpacing = Math.round(ratio * 100) / 100;
                break;

            case LineSpacingType.Fixed:
                lineSpaceDisabled = false;
                cell = 'cm';
                // para.paraSpacing = para.paraSpacing;
                break;
            default:
                para.paraSpacingType = -1;
                cell = '';
                // para.paraSpacing = 1;
                break;
        }

        this.paraPro = para;
        this.paraProUI = {disabled: lineSpaceDisabled, cell};
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private paraSpacingChange(value: any): void {
        const obj = this.paraSpacingTypes.find((type) => type.value === value);
        if (obj) {
            this.paraProUI.disabled = obj.bDisabled;
            this.paraProUI.cell = obj.cell;
            this.paraPro.paraSpacing = obj.defaultValue;
            this.setState({bRefresh: !this.state.bRefresh});
        }
    }

    private paraRadioChange(name: string, e: any): void {
        this.paraPro[name] = parseInt(e.target.value, 10);
    }

    private onChange = (value: any, name: string): void => {
        const oldValue = this.paraPro[name];
        this.paraPro[name] = value;
        switch (name) {
            case 'paraSpacingType': {
                this.paraSpacingChange(value);
                break;
            }
            case 'paraIndType': {
                if (oldValue === undefined) {
                    this.setState({ bRefresh: !this.state.bRefresh });
                }
                break;
            }
        }
        // this.setState({bRefresh: !this.state.bRefresh});
    }

    private removeEmptyParagragh = (): any => {
        if ( this.props.documentCore.isEmptyParagraphAfterTableAndRegion() ) {
            this.visible = false;
            this.props.close(this.props.id, false);
            gEvent.setEvent(this.host.docId, gEventName.DialogEvent, ToolbarIndex.EmptyParagraphRemove);
        } else {
            message.error('此删除只针对表格或区域后的空段落。');
        }
    }
}

export class EmptyParagraghRemove extends React.Component<IProps, IState> {
    private visible: boolean;
    constructor(props: any) {
        super(props);
        this.state = {
            bRefresh: false,
        };
        this.visible = this.props.visible;
    }

    public render(): any {
        return (
            <Dialog
                visible={this.visible}
                width={350}
                // open={this.open}
                title=''
                footer={this.renderFooter()}
                id={this.props.id}
            >
                <div className='editor-line'>
                    <span>请确认：</span>
                </div>
                <div className='editor-line' style={{textAlign: 'center'}}>
                    <span>
                        此操作将会删除当前光标位置的空段落。
                    </span>
                </div>
            </Dialog>
        );
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        this.visible = nextProps.visible;
    }

    private renderFooter(): any {
        return (
            <span>
                <Button onClick={this.close}>取消</Button>
                <Button type='primary' onClick={this.confirm}>
                    确认
                </Button>
            </span>
        );
    }

    private confirm = (id?: any): void => {
        const res = this.props.documentCore.deleteEmptyParaAfterTableAndRegion();

        if ( !res ) {
            message.error('失败！此删除只针对表格或区域后的空段落。');
        }
        this.close(id, res);
    }

    private close = (name: string | number, bRefresh?: boolean): void => {
        this.visible = false;
        this.setState({bRefresh: !this.state.bRefresh});
        this.props.close(this.props.id, bRefresh);
    }
}
