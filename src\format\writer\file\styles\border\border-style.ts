export enum BorderStyle {
    SINGLE = 'single',
// tslint:disable-next-line: sy-enum-name
    DASH_DOT_STROKED = 'dashDotStroked',
    DASHED = 'dashed',
// tslint:disable-next-line: sy-enum-name
    DASH_SMALL_GAP = 'dashSmallGap',
// tslint:disable-next-line: sy-enum-name
    DOT_DASH = 'dotDash',
// tslint:disable-next-line: sy-enum-name
    DOT_DOT_DASH = 'dotDotDash',
    DOTTED = 'dotted',
    DOUBLE = 'double',
// tslint:disable-next-line: sy-enum-name
    DOUBLE_WAVE = 'doubleWave',
    INSET = 'inset',
    NIL = 'nil',
    NONE = 'none',
    OUTSET = 'outset',
    THICK = 'thick',
// tslint:disable-next-line: sy-enum-name
    THICK_THIN_LARGE_GAP = 'thickThinLargeGap',
// tslint:disable-next-line: sy-enum-name
    THICK_THIN_MEDIUM_GAP = 'thickThinMediumGap',
// tslint:disable-next-line: sy-enum-name
    THICK_THIN_SMALL_GAP = 'thickThinSmallGap',
// tslint:disable-next-line: sy-enum-name
    THIN_THICK_LARGE_GAP = 'thinThickLargeGap',
// tslint:disable-next-line: sy-enum-name
    THIN_THICK_MEDIUM_GAP = 'thinThickMediumGap',
// tslint:disable-next-line: sy-enum-name
    THIN_THICK_SMALL_GAP = 'thinThickSmallGap',
// tslint:disable-next-line: sy-enum-name
    THIN_THICK_THIN_LARGE_GAP = 'thinThickThinLargeGap',
// tslint:disable-next-line: sy-enum-name
    THIN_THICK_THIN_MEDIUM_GAP = 'thinThickThinMediumGap',
// tslint:disable-next-line: sy-enum-name
    THIN_THICK_THIN_SMALL_GAP = 'thinThickThinSmallGap',
// tslint:disable-next-line: sy-enum-name
    THREE_D_EMBOSS = 'threeDEmboss',
// tslint:disable-next-line: sy-enum-name
    THREE_D_ENGRAVE = 'threeDEngrave',
    TRIPLE = 'triple',
    WAVE = 'wave',
}
