
import TextProperty from "../TextProperty";
import { measure } from "../util";
import { ParaElementBase } from "./ParaElementBase";
import { ParaElementType } from "./ParagraphContent";

export class ParaSoftLine extends ParaElementBase {
    constructor() {
        super();
    
        this.content = String.fromCharCode(0xFFEC); // '🠓'; '🠋';// String.fromCharCode(0xFFEC); // String.fromCharCode(0x2193);
        this.type = ParaElementType.ParaNewLine;
    }

    public measure(textPr: TextProperty, text?: string): number {
        const m = measure(String.fromCharCode(0x2193), textPr)[0];

        return m.height;
    }

    public copy(bForUI: boolean = false): ParaElementBase {
        const item = new ParaSoftLine();

        item.positionX = this.positionX;
        item.positionY = this.positionY;
        item.bVisible = this.bVisible;
        item.type = this.type;
        item.bViewSecret = this.bViewSecret;
        if ( true === bForUI) {
            item.width = this.width;
            item.widthVisible = this.widthVisible;
            item.bViewSecret = this.bViewSecret;
        }

        return item;
    }

    public isSoftLine(): boolean {
        return true;
    }
}
