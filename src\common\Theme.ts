export interface INewControlTheme {
  ConfirmStyle?: number;
  DefaultTextColor?: string;
  DefaultBackgroundColor?: string;
  DefaultBackgroundColor2?: string;
  DefaultFocusHighLightColor?: string;
  DefaultCursorInLightColor?: string;
  DefaultFocusHighLightColor2?: string;
  DefaultCursorInLightColor2?: string;
  DefaultNewControlBorderColor2?: string;
  DefaultNewControlBorderColor?: string;
  DefaultNewControlStartBorder?: string;
  DefaultNewControlEndBorder?: string;
  NewControlSectionStartBorder?: string;
  NewControlSectionEndBorder?: string;
  InputContentColor?: string;
  DefaultMustInputTextColor?: string;
  DefaultMustInputBorderColor?: string;
  DefaultMustInputChar?: string;
  DefaultPlaceHolderContent?: string;
  DefaultSecretReplaceChar?: string;
  DefaultReplaceSpaceChar?: string;
  DefaultItemSeparator?: string;
  DefaultPrefixSeparator?: string;
  DefaultBackgroundColorCanntEdit?: string;
  EnableNotAllowedCursorStyle?: boolean;

  DefaultRegionBorderColor?: string;
  ShowRegionOperator?: boolean;

  DefaultNewControlSectionRangeBorderColor?: string;
  // ShowNewControlSectionRangeBorder?: boolean;
  ShowSectionBorder?: boolean; // 是否显示节范围边框
  DefaultMessageTipColor?: string;
  DefaultMessageTipBackgroundColor?: string;
  MouseStyleInUneditableStruct?: number;
  RegionCustomIcon?: string;

  EnableDefaultStyleAfterTitle?: boolean;
}

export interface ITheme {
  NewControl?: INewControlTheme;
  ErrorTextColor?: string;
  DefaultBackgroudColor?: string;
  HeaderFooterOpacity?: number;
}

const defaultTheme = {
  NewControl: {
    ConfirmStyle: 1,
    DefaultTextColor : '#788190',   // 默认占位符颜色. was: #ACB4C1
    DefaultBackgroundColor : '#F0FAFF',//'#E5F5FF', // 213 229 244 背景色. was: #F0F2F5
    DefaultBackgroundColor2 : '#F0FAFF', // '#fdf6e9'. was: #F0F2F5
    DefaultFocusHighLightColor : '#85E6EB', // '#009FFF',  // 178 202 246 focus高亮色. was: #CCEEFF
    DefaultCursorInLightColor : '#93F2FF',
    DefaultFocusHighLightColor2 : '#85E6EB', // '#FFEFD3'. was: #CCEEFF
    DefaultCursorInLightColor2 : '#93F2FF', // '#FFE79C',
    DefaultNewControlBorderColor2 : '#4286F3',//'#008FEA', // '#FFBF52'
    DefaultNewControlBorderColor : '#4286F3',//'#008FEA', // '#0000ff', // '#ACB4C1',   // 默认边框颜色
    DefaultNewControlStartBorder : '\u005B',
    DefaultNewControlEndBorder : '\u005D',
    NewControlSectionStartBorder : '\u3010',
    NewControlSectionEndBorder : '\u3011',
    InputContentColor : '#000000',      // 文本内容颜色
    DefaultMustInputTextColor : '#EB4537',//'#EA0000',  // 必填项占位符内容颜色
    DefaultMustInputBorderColor : '#EB4537',//'#FF3143',  // 必填项[]颜色
    DefaultMustInputChar : '*',     // 必填项. was: '*'
    DefaultPlaceHolderContent : '  ', // 默认占位符内容：两个空格
    DefaultSecretReplaceChar : '*',     // 保密显示替换文本
    DefaultReplaceSpaceChar : '\u00a0',      // 替换空格
    DefaultItemSeparator : '，',       // 默认内容分隔符，可设置
    DefaultPrefixSeparator : '；',       // 默认前缀分隔符，不可设置
    DefaultBackgroundColorCanntEdit : '#E0E0E0',//'#DDDDE3',
    EnableNotAllowedCursorStyle : true, // 是否启用光标“不可编辑”样式

    DefaultRegionBorderColor: '#ACB4C1',
    ShowRegionOperator: false,

    DefaultNewControlSectionRangeBorderColor: '#0000FF',
    ShowNewControlSectionRangeBorder: true,
    DefaultMessageTipColor: '#FFFFFF',
    DefaultMessageTipBackgroundColor: '#54627B',
    MouseStyleInUneditableStruct: 0,
    RegionCustomIcon: 'default',

    EnableDefaultStyleAfterTitle: false,
  },
  ErrorTextColor:'#EB4537',//ErrorTextColor: '#FFB3B9',
  DefaultBackgroudColor: '#F0F0F0',// DefaultBackgroudColor: '#EBEFF2',
  HeaderFooterOpacity: 1,
};

let theme: ITheme;
setTheme(defaultTheme);

export function getTheme(): ITheme {
  return theme;
}

export function setTheme(themeSettings: ITheme): void {
  const defaultNewControlTheme = defaultTheme.NewControl;
  const newControlTheme = themeSettings.NewControl;
  const NewControl = {...defaultNewControlTheme, ...newControlTheme };
  const ErrorTextColor = themeSettings.ErrorTextColor || defaultTheme.ErrorTextColor;
  const DefaultBackgroudColor = themeSettings.DefaultBackgroudColor || defaultTheme.DefaultBackgroudColor;
  const HeaderFooterOpacity = themeSettings.HeaderFooterOpacity >= 0 && themeSettings.HeaderFooterOpacity <= 1 ?
                            themeSettings.HeaderFooterOpacity :
                            defaultTheme.HeaderFooterOpacity;
  theme = {NewControl, ErrorTextColor, DefaultBackgroudColor, HeaderFooterOpacity};
}

window['__EMR_EDITOR_SET_THEME__'] = setTheme; // 用于调试
