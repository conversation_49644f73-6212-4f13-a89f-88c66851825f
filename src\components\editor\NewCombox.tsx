import * as React from 'react';
import { DocumentCore } from '../../model/DocumentCore';
import { INewControlProperty, NewControlType, CodeValueItem } from '../../common/commonDefines';
import retrieve from '../../common/Retrieve';

interface INewComboxListProps {
    documentCore: DocumentCore;
    newControlPropety: INewControlProperty;
    closeNewComboxList?: () => void;
    refresh?: () => void;
    position?: {left: number, top: number, width: number};
}

interface INewComboxListPropsState {
    curIndex?: number;      // 当前选中item索引
    bReflash: boolean;
}

export class NewCombox extends React.Component<INewComboxListProps, INewComboxListPropsState> {
    private bSelect: boolean;
    private selectItemsPos: Set<number>;
    private bMulti: boolean;
    private newControlItems: CodeValueItem[];
    private searchItems: CodeValueItem[];
    private keyword: string;
    private unSearchKeyword: string;
    private searchTime: any;
    private inputRef: any;
    private bSearch: boolean;
    constructor(props: any) {
        super(props);

        this.state = {
            curIndex: -1,
            bReflash: false,
        };
        this.bSelect = false;
        this.selectItemsPos = new Set();
        this.bMulti = false;
        this.searchItems = this.newControlItems = this.props.newControlPropety.newControlItems;
        this.inputRef = React.createRef();
        this.bSearch = this.props.newControlPropety.retrieve;
    }

    public render(): any {
        const position = this.props.position;
        let left: number;
        let top: number;
        let width: number;
        if (position) {
            left = position.left;
            top = position.top;
            width = position.width;
        } else {
            const bounds = this.props.documentCore.getNewControlsFocusBounds();
            const lastLine = bounds.bounds[bounds.bounds.length - 1];
            left = lastLine.x;
            top = lastLine.y + lastLine.height; // + 3;
            width = 150 > lastLine.width ? 150 : lastLine.width;
        }
        return (
            <div className='new-control-combox-list' onMouseDown={this.handleMouseDown.bind(this, 'combox-list')} style={{top, left, width}}>
                {this.renderSearchContent()}
                {this.renderTable()}
                {this.renderBtns()}
            </div>
            );
    }

    public componentDidMount(): void {
        if (this.bSearch) {
            setTimeout(() => {
                this.inputRef.current.focus();
            }, 300);
        }
    }

    // public UNSAFE_componentWillReceiveProps(nextProps: any): void {
    //     this.searchItems = this.newControlItems = nextProps.newControlPropety.newControlItems;
    // }

    // private test(): void {
    //     const arrs = ['是的访问法个', '的佛那个人购买费共计', '低分狗溶剂法狗儿烹饪机构的交付给', '的方便么卤肉卷偶觉得发给我京东方',
    //     'fgml的烦恼够味儿解耦股么地方个人个', '么法平均打排位非贫困的分配给，二等分更肉片京东方', '人提供巨额融合柔而佛光和偶然间鹅肉感觉',
    //     '地方个人偶尔加工恶如，我的狗耳机而孤独感解耦'];
    //     const len = arrs.length;
    //     let arrIndex: number = 0;
    //     const datas: CodeValueItem[] = [];
    //     for (let index = 0; index < 1100; index++) {
    //         datas.push(new CodeValueItem(arrs[arrIndex++] + index, index.toString(), false));
    //         if (arrIndex === len) {
    //             arrIndex = 0;
    //         }
    //     }
    //     retrieve.search('s', 'a');
    //     this.searchItems = this.newControlItems = datas;
    // }

    private handleSearch(e: any): void {
        const keyword = e.target.value || '';
        if (/[\u4E00-\u9FA5]/.test(keyword)) {
            return;
        }
        this.keyword = keyword.toUpperCase();
        this.setState({bReflash: !this.state.bReflash});
        clearTimeout(this.searchTime);
        this.searchTime = setTimeout(() => {
            this.search();
        }, 50);
    }

    private handleFocus(): void {
        if (!this.inputRef || !this.inputRef.current) {
            return;
        }
        setTimeout(() => {
            this.inputRef.current.focus();
        }, 50);
    }

    private handleKeyDown = (e: any): void => {
        if (e.keyCode === 13) {
            this.selectOneControl();
        }
    }

    private async search(): Promise<void> {
        const list = this.newControlItems;
        if (!list || list.length === 0) {
            return;
        }

        if (!this.keyword) {
            this.searchItems = this.newControlItems;
            this.selectItemsPos.clear();
            this.bSelect = false;
            this.setState({bReflash: !this.state.bReflash});
            return;
        }

        if (this.unSearchKeyword && this.keyword.indexOf(this.unSearchKeyword) === 0) {
            return;
        }

        const items = this.searchItems = [];
        const keyword = this.keyword;
        for (const item of list) {
            const res = await retrieve.search(item.code, keyword);
            if (res.bSuccess === true) {
                items.push(item);
            }
        }

        if (items.length === 0) {
            this.unSearchKeyword = this.keyword;
            this.selectItemsPos.clear();
            this.bSelect = true;
        } else {
            items.sort((a, b) => {
                return a.code.length - b.code.length;
            });
            if (this.bMulti === false) {
                this.selectItemsPos.clear();
            }
            this.selectItemsPos.add(0);
            this.bSelect = true;
            this.unSearchKeyword = undefined;
            // this.setSelect(0);
        }
        this.setState({bReflash: !this.state.bReflash});
    }

    // private setSelect(index?: number): void {
    //     this.searchItems.forEach((item) => item.bSelect = false);
    //     if (index !== undefined) {
    //         this.searchItems[index].bSelect = true;
    //     }
    // }

    private renderBtns(): any {
        if (this.bMulti === false) {
            return null;
        }
        return (
            <div className='combox-list-button-container'>
                <button className='button' onClick={this.handleButtonClick.bind(this)}>确定</button>
                <button className='button' onClick={this.handleButtonClick.bind(this)}>取消</button>
                <button className='button' onClick={this.handleButtonClick.bind(this)}>复位</button>
            </div>
        );
    }

    private renderSearchContent(): any {
        if (this.bSearch !== true) {
            return null;
        }
        return (
            <div>
                <input
                    ref={this.inputRef}
                    style={{width: '95%'}}
                    value={this.keyword || ''}
                    onKeyDown={this.handleKeyDown}
                    // tslint:disable-next-line: jsx-no-bind
                    onChange={this.handleSearch.bind(this)}
                />
            </div>
        );
    }

    private renderTable(): any {
        const list = this.searchItems;
        this.bMulti = ( NewControlType.MultiCombox === this.props.newControlPropety.newControlType
                || NewControlType.MultiListBox === this.props.newControlPropety.newControlType );
        if ( null == list || 0 === list.length ) {
            return null;
        } else {
            return (<div className='combox-item-list'>
                    <table className='combox-item' id='combox-item'
                        onMouseDown={this.handleMouseDown.bind(this, 'select-combox-item')}>
                            <tbody>
                            {this.renderList()}</tbody>
                    </table>
                </div>);
        }
    }

    private renderList(): any {
        const list = this.searchItems;
        this.bMulti = ( NewControlType.MultiCombox === this.props.newControlPropety.newControlType
            || NewControlType.MultiListBox === this.props.newControlPropety.newControlType );
        if ( null == list || 0 === list.length ) {
            return ;
        }

        if ( false === this.bSelect ) {
            list.forEach((item, index) => {
                if ( true === item.bSelect ) {
                    this.selectItemsPos.add(index);
                }
            });
        }

        const newArry = [];
        if ( true === this.bMulti ) {
            for ( let index = 0; index < list.length; index++) {
                const item = list[index];
                const color = this.selectItemsPos.has(index) ? '#009FFF' : '' ;

                newArry.push((<tr className='table-tr' key={index} id={index.toString()} style={{background: color}}>
                            <td id={index.toString()}>
                                <input type='checkbox' id={index.toString()} checked={this.selectItemsPos.has(index)} onChange={this.handleMouseDown.bind(this)}/>
                                <label htmlFor={index.toString()} id={index.toString()}>{item.code}</label>
                            </td>
                        </tr>));
            }
        } else {
            for ( let index = 0; index < list.length; index++) {
                const item = list[index];
                const color = this.selectItemsPos.has(index) ? '#009FFF' : '' ;

                newArry.push((<tr className='table-tr' key={index} id={index.toString()} style={{background: color}}>
                            <td id={index.toString()}>
                                <label id={index.toString()}>{item.code}</label>
                            </td>
                        </tr>));
            }
        }

        return newArry;
    }

    private handleMouseDown(type: string, e: any): void {
        if ( 'select-combox-item' === type ) {
            const table = document.getElementById('combox-item') as HTMLTableElement;
            if ( null != table && 0 < table.rows.length
                && ( 'TD' === e.target.tagName || 'INPUT' === e.target.tagName || 'LABEL' === e.target.tagName) ) {
                const curRowIndex = Number.parseInt(e.target.id, 0);
                this.bSelect = true;

                if ( null != curRowIndex ) {
                    if ( this.state.curIndex !== curRowIndex ) {
                        if ( -1 !== this.state.curIndex && table.rows[this.state.curIndex] ) {
                            table.rows[this.state.curIndex].bgColor = '';
                        }
                        table.rows[curRowIndex].bgColor = '#009FFF';

                        // const td = table.rows[curRowIndex].children[0];
                        // const input = td.firstElementChild as HTMLInputElement;

                        if ( false === this.bMulti ) {
                            this.selectItemsPos.clear();
                        }
                    }

                    if (this.bMulti === false) {
                        if (this.selectItemsPos.has(curRowIndex) === false) {
                            this.selectItemsPos.add(curRowIndex);
                        }

                        this.selectOneControl();
                        return;
                    }

                    this.handleFocus();

                    if ( false === this.selectItemsPos.has(curRowIndex) ) {
                        this.selectItemsPos.add(curRowIndex);
                    } else {
                        this.selectItemsPos.delete(curRowIndex);
                    }
                    this.setState({curIndex: curRowIndex});
                }
            }
        }
    }

    private selectOneControl(): void {
        const { documentCore, newControlPropety } = this.props;
        const newControl = documentCore.getNewControlByName(newControlPropety.newControlName);
        const list = this.searchItems;
        if ( null == list || 0 === list.length ) {
            return;
        }

        const pos = [];
        const sourceList = this.newControlItems;
        const bSmame = sourceList === list;
        this.selectItemsPos.forEach((value) => {
            if (!bSmame) {
                const data = list[value];
                value = sourceList.findIndex((item) => item === data);
            }
            pos.push(value);
        });
        if (this.bMulti === false) {
            this.selectItemsPos.clear();
        }
        newControl.setNewControlListItems(pos);
        this.props.closeNewComboxList();
        this.props.refresh();
    }

    private handleButtonClick(e: any): void {
        const { documentCore, newControlPropety } = this.props;
        const newControl = documentCore.getNewControlByName(newControlPropety.newControlName);
        const list = this.searchItems;

        this.bSelect = false;

        if ( null == newControl ) {
            return ;
        }

        switch (e.target.innerHTML) {
            case '确定':
                if ( null == list || 0 === list.length ) {
                    break;
                }

                const pos = [];
                const sourceList = this.newControlItems;
                const bStart = sourceList.length !== list.length;
                this.selectItemsPos.forEach((value) => {
                    if (bStart) {
                        const data = list[value];
                        value = sourceList.findIndex((item) => item === data);
                    }
                    pos.push(value);
                });
                newControl.setNewControlListItems(pos);
                this.props.refresh();

            case '取消':
                break;

            case '复位':
                this.selectItemsPos.clear();
                newControl.resetSelectItems();
                this.props.refresh();
                break;

            default:
                break;
        }

        this.props.closeNewComboxList();
    }
}

// interface INewComboxDropButtonProps {
//     documentCore: DocumentCore;
//     newControlPropety: INewControlProperty;
//     showNewComboxList?: (bClickDropButton: boolean) => void;
//     closeNewComboxList?: () => void;
//     refresh?: () => void;
//     // newControlComboxRefs?: any;
// }

// export class NewComboxDropButton extends React.Component<INewComboxDropButtonProps> {
//     constructor(props: any) {
//         super(props);
//     }

//     public render(): any {
//         return (<span className='drop-button-container' onMouseDown={this.handleMouseDown.bind(this)}/>);
//     }

//     private handleMouseDown(e: any): void {
//         this.props.showNewComboxList(true);
//     }
// }
