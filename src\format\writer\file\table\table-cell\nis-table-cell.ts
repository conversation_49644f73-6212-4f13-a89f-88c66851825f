// http://officeopenxml.com/WPtableGrid.php
import { Paragraph } from '../../paragraph';
import { XmlComponent } from '../../xml-components';

import { Table } from '../table';
import { ICellWidthProperties } from './table-cell-properties';
import { INISProperty, ITableCellBorder } from '../../../../../common/commonDefines';
import { NISTableCellProperties } from './nis-table-cell-properties';
import { IXmlResult } from '../../xml-components/base';

export class NISTableCell extends XmlComponent {
    private properties: NISTableCellProperties;

    constructor() {
        super('w:tc');
        this.initializeProperties();
    }

    public addContent(content: Paragraph | Table): NISTableCell {
        this.root.push(content);
        return this;
    }

    public prepForXml(): IXmlResult {
        // Cells must end with a paragraph
        const retval = super.prepForXml();
        if (!retval) {
            return undefined;
        }

        // const content = retval['w:tc'];
        // if (!content[content.length - 1]['w:p']) {
        //     content.push(new Paragraph().prepForXml());
        // }
        return retval;
    }

    public createParagraph(text?: string): Paragraph {
        const para = new Paragraph(text);
        this.addContent(para);
        return para;
    }

    public get CellProperties(): NISTableCellProperties {
        return this.properties;
    }

    public addGridSpan(val: number): NISTableCell {
        this.properties.addGridSpan(val);
        return this;
    }

    public addVMerge(val: number): NISTableCell {
        this.properties.addVMerge(val);
        return this;
    }

    public addCellWidth(cellWidth: ICellWidthProperties): NISTableCell {
        this.properties.addCellWidth(cellWidth);
        return this;
    }

    public addTopBorder(border: ITableCellBorder): NISTableCell {
        this.properties.addTopBorder(border);
        return this;
    }

    public addRightBorder(border: ITableCellBorder): NISTableCell {
        this.properties.addRightBorder(border);
        return this;
    }

    public addBottomBorder(border: ITableCellBorder): NISTableCell {
        this.properties.addBottomBorder(border);
        return this;
    }

    public addLeftBorder(border: ITableCellBorder): NISTableCell {
        this.properties.addLeftBorder(border);
        return this;
    }

    public addCellProtected(val: string): NISTableCell {
        this.properties.addCellProtected(val);
        return this;
    }

    public addCellFormula(val: string): NISTableCell {
        this.properties.addCellFormula(val);
        return this;
    }

    public addCellVertAlign(val: number): NISTableCell {
        this.properties.addCellVertAlign(val);
        return this;
    }

    public addTcNis(nisProperty: INISProperty): NISTableCell {
        this.properties.addTcNis(nisProperty);
        return this;
    }

    /**
     * initialize properties if not
     */
    private initializeProperties(): void {
        if (!this.properties) {
            this.properties = new NISTableCellProperties();

            // should be the first child element
            // this.root.push(this.properties);
            this.root.unshift(this.properties);
        }
    }
}
