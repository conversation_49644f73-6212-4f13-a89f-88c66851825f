import Dialog from "@/components/editor/ui/Dialog";
import { DocumentCore } from "@/model/DocumentCore";
import React from "react";
import CommentPanel from "./CommentPanel";
import { Comment } from "@/model/core/Comment/Comment";

interface IProps {
    bInsertNewComment?: boolean;
    id: string;
    visible: boolean;
    documentCore: DocumentCore;
    close: (name: string, bRefresh?: boolean) => void;
    refresh: (flag: boolean) => void;
}


export default class CommentPanelDialog extends React.Component<IProps> {
    private visible: boolean;
    private commentList: Comment[] = [];

    constructor(props: IProps) {
        super(props);
        this.visible = props.visible;
    }

    public render() {
        const fullHeight = window.document.firstElementChild.clientHeight - 50;
        const height = fullHeight > 600 ? 600 : fullHeight < 230 ? 230 : fullHeight;
        return (
            <Dialog
                bCloseIcon={true}
                title="批注管理面板"
                visible={this.visible}
                width={400}
                height={height}
                close={this.close}
                open={this.refreshComments}
                noModal={true}
            >
                <CommentPanel
                    height={height}
                    bInsertNewComment={this.props.bInsertNewComment}
                    commentList={this.commentList}
                    documentCore={this.props.documentCore}
                    userName={this.props.documentCore.getCommentStatusInfo().userName}
                    refresh={this.refreshComments}
                    activeComment={this.handleActiveComment}
                />
            </Dialog>
        );
    }

    public UNSAFE_componentWillReceiveProps(nextProps: Readonly<IProps>, nextContext: any): void {
        this.visible = nextProps.visible;
    }

    private handleActiveComment = (name?: string) => {
        if (this.props.documentCore.activeComment(name) === 0) {
            this.props.refresh(true);
        }
    }


    private close = () => {
        this.props.close(this.props.id);
        this.visible = false;
        this.setState({});
    }

    private refreshComments = (bRefreshUI = false) => {
        const { documentCore, visible } = this.props;
        // 面板不可见时不进行更新
        if (!visible) return;
        if (bRefreshUI) {
            this.props.refresh(true);
        }
        this.commentList = documentCore.getAllComments();
        this.setState({});
    }
}