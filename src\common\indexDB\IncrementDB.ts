
export class IncrementDB {
    private mDB: any;
    private dbName: string;
    private version: string;
    constructor(dbName: string, version: string) {
        this.dbName = dbName;
        this.version = version;
        this.mDB = null; // new IndexDB(dbName, 1);
        // this.idb = null;
        // this.table = [];
        // this._status = false; // 是否先添加了表
        // this._dep = new Dep();
        this.openDB('hzEditor');

    }

    public openDB(tbName: string): void {
        // this.mDB.open(tbName);
        // const request = indexedDB.open(tbName, 1);

        // request.onsuccess = (e: any) => {
        //     // this.mDB = e.target.result;
        //     // this.success(e.target.result);
        //     this.mDB = request.result;
        // };

        // request.onerror = (e: any) => {
        //     alert('Open database error: ' + e.target.errorCode);
        // };

        // request.onupgradeneeded = (e: any) => {
        //     const db = e.target.result;
        //     if ( !db.objectStoreNames.contains(tbName) ) {
        //         const objectStore = db.createObjectStore(tbName, {keyPath: 'key'});
        //         objectStore.createIndex('key', 'key', {unique: false});
        //         objectStore.createIndex('value', 'value', {unique: true});
        //     }
        // };
    }

    public insertItem(key: any, value: any): void {
        // const request = indexedDB.open(this.dbName, 1);

        // request.onsuccess = (e: any) => {
        //     // console.log(e.target.result)
        //     // this.mDB = e.target.result;
        //     this.mDB = request.result;

        //     const objectStore = this.getObjectStore('readwrite');
        //     objectStore.put({key, value});
        // };
        // // const request = this.getObjectStore('readwrite').add({key, value});

        // // request.onsuccess = (e: any) => {
        // //     console.log('insert success');
        // // };

        // request.onerror = (e: any) => {
        //     console.log('insert error');
        // };
    }

    public getItem(key: any): Promise<any> {
        return new Promise((resolve, reject) => {
            // const objectStore = this.getObjectStore('readonly');
            // objectStore.openCursor().onsuccess = (e: any) => {
            //     const cursor = e.target.result;

            //     if ( cursor ) {
            //         if ( cursor.key === key ) {
            //             // return cursor.value;
            //             return resolve(cursor.value);
            //         }

            //         cursor.continue();
            //     }

            //     return resolve(null);
            // };
        });
    }

    public update(key: any, value: any): void {
        const request = this.getObjectStore('readwrite').put(key, value);

        request.onsuccess = (e: any) => {
            console.log('update success');
        };

        request.onerror = (e: any) => {
            console.log('update error');
        };
    }

    public delete(): void {
        const objectStore = this.getObjectStore('readwrite');
    }

    private getObjectStore(readwrite: string): any {
        const transaction = this.mDB.transaction([this.dbName], readwrite);
        return transaction.objectStore(this.dbName);
    }
}

// export const incrementDB = new IncrementDB('hzEditor', '1.0');

