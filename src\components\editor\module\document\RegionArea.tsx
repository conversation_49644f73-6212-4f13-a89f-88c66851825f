import { numtoFixed2 } from "@/common/commonDefines";
import React from "react";

interface IProps {
    content?: any;
    index: number;
}

export class RegionArea extends React.Component<IProps> {
    private _content: any;
    constructor(props: IProps) {
        super(props);
        this._content = this.props.content;
    }

    public render(): React.ReactNode {
        return <React.Fragment>{this.getRegionBorder(this._content.rects)}</React.Fragment>
    }

    public UNSAFE_componentWillReceiveProps(nextProps): void {
        this._content = nextProps.content;
    }

    public refresh(option): any {
        this._content = option.content;
        this.setState({});
    }

    private getRegionBorder(rects: any[]): any {
        if (!rects.length) {
            return;
        }
        const index = this.props.index;
        const style = { fill: 'transparent', strokeWidth: '1', stroke: '#999' };
        const style1: any = {...style};
        const style2: any = {
            color: '#777',
            fontSize: 11,
            cursor: 'pointer',
            pointerEvents: 'none'
        }
        style1.cursor = 'pointer';
        style1.pointerEvents = 'all';
        const curBorder = [];
        rects.forEach((rect) => {
            const {lines, name} = rect;
            let firstLine: any;
            for (let idx = 0, length = lines.length; idx < length; idx += 2) {
                const line = lines[idx];
                if (line.pageIndex === index) {
                    if (!firstLine) {
                        firstLine = line;
                    }
                    const line2 = lines[idx + 1];
                    const x1 = numtoFixed2(line.x);
                    const x2 = numtoFixed2(line2.width);
                    const y1 = numtoFixed2(line.y);
                    const y2 = numtoFixed2(line2.y);
                    curBorder.push((
                        <line
                            key={`${name}-${idx}-1`}
                            x1={x1}
                            y1={y1}
                            x2={x1}
                            y2={y2}
                            style={style}
                        />
                    ));
                    curBorder.push((
                        <line
                            key={`${name}-${idx}-2`}
                            x1={x2}
                            y1={y1}
                            x2={x2}
                            y2={y2}
                            style={style}
                        />
                    ));
                    if (line.bStart) {
                        curBorder.push((
                            <line
                                key={`${name}-${idx}-3`}
                                x1={x1}
                                y1={y1}
                                x2={x2}
                                y2={y1}
                                style={style}
                            />
                        ));
                    }
                    if (line2.bEnd) {
                        curBorder.push((
                            <line
                                key={`${name}-${idx}-4`}
                                x1={x1}
                                y1={y2}
                                x2={x2}
                                y2={y2}
                                style={style}
                            />
                        ));
                    }
                }
            }
            if (firstLine) {
                const x1 = numtoFixed2(firstLine.x - 14);
                curBorder.push(<rect data-index={name + '?1'} style={style1} x={x1} y={numtoFixed2(firstLine.y)} width={10} height={10} key={`${name}-line-1`}></rect>);
                curBorder.push(<text style={style2} x={numtoFixed2(firstLine.x - 13)} y={numtoFixed2(firstLine.y + 9)} key={`${name}-line-2`}>+</text>);
            }
        });
        if (!curBorder.length) {
            return;
        }

        return (<g>{curBorder}</g>);
    }
}