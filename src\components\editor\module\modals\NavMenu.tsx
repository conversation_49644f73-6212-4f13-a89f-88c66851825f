import * as React from 'react';
import Dialog from '../../ui/Dialog';
import { INavMenuItem, ToolbarIndex, NewControlType,
    MenuItemIndex } from '../../../../common/commonDefines';
import '../../style/navMenu.less';
import {
    GlobalEvent as gEvent,
    GlobalEventName as gEventName,
} from '../../../../common/GlobalEvent';
import Checkbox from '../../ui/CheckboxItem';
import { message } from '@/common/Message';

interface IProps {
    documentCore: any;
    visible: boolean;
    id: string;
    host: any;
    refresh: (bRefresh: boolean, num?: number) => void;
    close: (name: string | number, bRefresh?: boolean) => void;
}

interface IKeyProps {
    name: string;
    id: string;
}
const name1: IKeyProps[] = [{id: 'elements', name: '全部元素'}, {id: 'sections', name: '节'}, {id: 'signature', name: '签名'},
    {id: 'regions', name: '区域'}, {id: 'images', name: '图片'}, {id: 'equations', name: '医学表达式'},
    {id: 'tables', name: '表格'}, {id: 'comments', name: '批注'}];
const name2: IKeyProps[] = [{id: 'undeletes', name: '禁止删除'}, {id: 'unedits', name: '禁止编辑'},
    {id: 'mustInputs', name: '必填项'}, {id: 'reverseEdits', name: '反向编辑'},
    {id: 'copyProtect', name: '禁止拷贝'}, {id: 'hidden', name: '隐藏元素'}, {id: 'serialNumber2', name: '编码重复'}];
    // , {id: 'serialNumber', name: '重复校验'}

interface IState {
    showSerialNumber: boolean;
}

export default class NavMenu extends React.Component<IProps, IState> {
    private _documentCore: any;
    private visible: boolean;
    private nav1: IKeyProps[];
    private nav2: IKeyProps[];
    private data: INavMenuItem;
    private activeIndexs: any[][];
    private myRef: any;
    private timeout: any;
    private rightMenu: boolean;
    private activeName: string;
    private activeIndex: string[];
    private host: any;
    private position: {left: number, top: number};
    private docId: number;
    // private image: string;
    // private callback: any;
    private timeout2: any;
    private activeNames: string[];
    // private activeName: string;

    private searchName: string;

    constructor(props: IProps) {
        super(props);
        this._documentCore = props.documentCore;
        this.host = props.host;
        this.nav1 = name1;
        this.nav2 = name2;
        this.data = {};
        this.visible = this.props.visible;
        const arrs1 = new Array(name1.length);
        const arrs2 = new Array(name2.length);
        // arrs1[0] = true;
        this.activeIndexs = [[false, false, false], arrs1, arrs2];
        this.myRef = React.createRef();
        this.position = {left: 0, top: 0};
        this.state = {
            showSerialNumber: true,
        };
        this.activeNames = [];
        this.searchName = '';
    }

    public render(): any {
        const arrs = this.activeIndexs[0];
        let className1 = 'title icon';
        if (arrs[0] === true) {
            className1 += ' active';
        }
        let className2 = 'title icon';
        if (arrs[1] === true) {
            className2 += ' active';
        }
        let className3 = 'title icon';
        if (arrs[2] === true) {
            className3 += ' active';
        }
        let rightMenuClass = 'operate';
        if (this.rightMenu === true) {
            rightMenuClass += ' active';
        }
        return (
            <Dialog
                visible={this.visible}
                width={280}
                height={400}
                left='5%'
                top='10%'
                scale={true}
                bCloseIcon={true}
                noModal={true}
                open={this.open}
                close={this.close}
                title='导航'
                id={this.props.id}
            >
                <div className='nav-menu' ref={this.myRef}>
                    <div className='search'>
                        <i
                            onClick={this.confirm}
                            className={'' + (this.searchName ? ' active' : ' disabled')}
                        />
                        <input
                            value={this.searchName}
                            onChange={this.onInputChange}
                            placeholder='请输入查找元素名'
                            // ref={this.inputRef}
                            onKeyDown={this.keydown}
                        />
                    </div>
                    <div className={className3} data-index='0-2'>
                        <span>元素</span>
                        <Checkbox
                            value={this.state.showSerialNumber}
                            name={'showSerialNumber'}
                            onChange={this.onChange}
                        >
                            详细信息
                        </Checkbox>
                    </div>
                    {this.renderUl(this.data.structAndRegions, [2, 2])}
                    <div className={className1} data-index='0-0'>
                        <span>分类</span>
                    </div>
                    <div className='content'>
                        {this.renderList(1)}
                    </div>
                    <div className={className2} data-index='0-1'>筛选</div>
                    <div className='content'>
                        {this.renderList(2)}
                    </div>
                    <div className={rightMenuClass} style={this.position}>
                        <div data-type='0'>属性</div>
                        <div data-type='1'>删除</div>
                    </div>
                </div>
            </Dialog>
        );
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        this.visible = nextProps.visible;
    }

    public componentDidMount(): void {
        const dom = this.myRef.current;
        // dom.addEventListener('focus', this.open);
        dom.addEventListener('click', this.onClick);
        dom.addEventListener('contextmenu', this.onContextMenu);
        this.docId = this._documentCore.getCurrentId();
        gEvent.addEvent(this.docId, gEventName.NewControlChange, this.nameChange);
        gEvent.addEvent(this.docId, gEventName.RegionChange, this.nameChange);
    }

    public componentWillUnmount(): void {
        const dom = this.myRef.current;
        if (!dom) {
            console.log('undelete event nav');
            return;
        }
        // dom.removeEventListener('focus', this.open);
        dom.removeEventListener('click', this.onClick);
        dom.removeEventListener('contextmenu', this.onContextMenu);
        gEvent.deleteEvent(this.docId, gEventName.NewControlChange, this.nameChange);
        gEvent.deleteEvent(this.docId, gEventName.RegionChange, this.nameChange);
    }

    private nameChange = (element: any): void => {
        if (!element || this.visible !== true) {
            return;
        }
        const name = element.name;
        if (this.activeName === name) {
            return;
        }
        this.activeNames.push(name);
        clearTimeout(this.timeout2);
        this.timeout2 = setTimeout(() => {
            this.activeName = this.activeNames[0];
            this.setState({});
            this.activeNames = [];
        }, 15);
    }

    private onContextMenu = (e: any): void => {
        e.preventDefault();
        if (this.rightMenu === true) {
            this.rightMenu = false;
            this.setState({});
            return;
        }
        const target = e.target;
        if (target.nodeName !== 'LI') {
            return;
        }
        // console.log(e)
        // console.dir(target)
        this.position = {
            left: e.layerX + 20,
            top: e.layerY + 40,
        };
        const dataIndex = target.getAttribute('data-index');
        if (!dataIndex) {
            return;
        }
        const arrs = dataIndex.split('-');
        const name = target.textContent;
        this.activeIndex = arrs;
        this.activeName = name;
        // switch (arrs[1]) {
        //     case '0':
        //     case '1':
        //     case '2': {
        //         this.rightMenu = true;
        //         break;
        //     }
        //     default: {
        //         return;
        //     }
        // }
        // if (arrs[1] === '5') {
        //     return;
        // }
        if (arrs[1]) {
            this.rightMenu = true;
        }
        this.setState({});
    }

    private setStructProps(): void {
        const name = this.getStructName();
        if (!name) {
            return;
        }

        const props = this._documentCore.getNewControlPropByName(name);
        if (!props) {
            return;
        }

        let nType: ToolbarIndex;
        switch (props.newControlType) {
            case NewControlType.Combox:
            case NewControlType.ListBox:
            case NewControlType.MultiCombox:
            case NewControlType.MultiListBox: {
                nType = ToolbarIndex.SingleOrMultipleBox;
                break;
            }
            case NewControlType.Section:
            case NewControlType.TextBox: {
                nType = ToolbarIndex.TextBox;
                break;
            }
            case NewControlType.NumberBox: {
                nType = ToolbarIndex.NumberBox;
                break;
            }
            case NewControlType.DateTimeBox: {
                nType = ToolbarIndex.DateBox;
                break;
            }
            case NewControlType.RadioButton: {
                nType = ToolbarIndex.Radio;
                break;
            }
            case NewControlType.CheckBox: {
                nType = ToolbarIndex.Checkbox;
                break;
            }
            case NewControlType.TextBox: {
                nType = ToolbarIndex.TextBox;
                break;
            }
            case NewControlType.SignatureBox: {
                nType = ToolbarIndex.SignatureBox;
                break;
            }
            case NewControlType.MultiRadio: {
                nType = ToolbarIndex.MultiRadio;
                break;
            }
            case NewControlType.AddressBox: {
                nType = ToolbarIndex.AddressBox;
                break;
            }
        }
        gEvent.setEvent(this.host.docId, gEventName.DialogEvent, nType, props);
        // this.close();
    }

    private deleteStruct(): void {
        const name = this.getStructName();
        if (!name) {
            return;
        }

        const res = this._documentCore.deleteNewControl(name);
        if (res === 0) {
            this.props.refresh(true);
        }
    }

    private operate(type: string): void {
        const arrs = this.activeIndex;
        if (arrs[0] === '2') {
            this.structExec(type);
            return;
        }
        switch (arrs[1]) {
            case '0':
            case '1':
            case '2': { // 结构化元素
                this.structExec(type);
                break;
            }
            case '3': { // 结构化元素
                this.regionExec(type);
                break;
            }
            case '4': { // 图片
                this.imageExec(type);
                break;
            }
            case '5': { // 医学公式
                this.equationExec(type);
                break;
            }
            case '6': { // 表格
                // this.jumpTable(name);
                this.tableExec(type);
                break;
            }
            case '7': { // 表格
                // this.jumpTable(name);
                this.commentExec(type);
                break;
            }
            default:
        }
    }

    private commentExec(type: string): void {
        switch (type) {
            case '1': {
                const res = this._documentCore.deleteCommentByName(this.activeName);
                if (res === true) {
                    this.props.refresh(true);
                }
                break;
            }
        }
    }

    private setImageProps(): void {
        const name = this.activeName;
        if (!name) {
            return;
        }
        const image = this._documentCore.getDrawingByName(name);
        if (!image) {
            return;
        }
        gEvent.setEvent(this.host.docId, gEventName.DialogEvent, MenuItemIndex.Image, image);
    }

    private deleteImage(): void {
        const name = this.activeName;
        if (!name) {
            return;
        }

        const res = this._documentCore.deleteDrawing(name);
        if (res === 0) {
            this.props.refresh(true);
        }
    }

    private async setEquationProps(): Promise<void> {
        const name = this.activeName;
        if (!name) {
            return;
        }
        await this.jumpImage(name);
        gEvent.setEvent(this.host.docId, gEventName.DialogEvent, MenuItemIndex.Formula);
    }

    private structExec(type: string): void {
        if (this.data.regions.includes(this.getStructName())) {
            this.regionExec(type);
            return;
        }
        switch (type) {
            case '0': {
                this.setStructProps();
                break;
            }
            case '1': {
                this.deleteStruct();
                break;
            }
        }
    }

    private regionExec(type: string): void {
        switch (type) {
            case '0': {
                this.setRegionProps();
                break;
            }
            case '1': {
                this.deleteRegion();
                break;
            }
        }
    }

    private setRegionProps(): void {
        const name = this.getStructName();
        if (!name) {
            return;
        }

        const props = this._documentCore.getRegionPropsByName(name);
        if (!props) {
            return;
        }

        gEvent.setEvent(this.host.docId, gEventName.DialogEvent, ToolbarIndex.Region, props);
    }

    private deleteRegion(): void {
        const name = this.getStructName();
        if (!name) {
            return;
        }

        const res = this._documentCore.deleteRegion(name);
        if (res === 0) {
            this.props.refresh(true);
        }
    }

    private imageExec(type: string): void {
        switch (type) {
            case '0': {
                this.setImageProps();
                break;
            }
            case '1': {
                this.deleteImage();
                break;
            }
        }
    }

    private equationExec(type: string): void {
        switch (type) {
            case '0': {
                this.setEquationProps();
                break;
            }
            case '1': {
                this.deleteImage();
                break;
            }
        }
    }

    private tableExec(type: string): void {
        switch (type) {
            case '0': {
                this.setTableProps();
                break;
            }
            case '1': {
                this.deleteTable();
                break;
            }
        }
    }

    private deleteTable(): void {
        const res = this._documentCore.deleteTableByName(this.activeName);
        if (res === 0) {
            this.props.refresh(true);
        }
    }

    private setTableProps(): void {
        gEvent.setEvent(this.host.docId, gEventName.DialogEvent, MenuItemIndex.Table, {name: this.activeName});
    }

    private onClick = (e: any): void => {
        // this.blurImage();
        const target = e.target;
        if (target.className && target.className.indexOf('title') > -1) {
            this.titleClick(target);
        } else if (target.nodeName === 'LI') {
            this.liClick(target);
        } else if (this.rightMenu) {
            const type = target.getAttribute('data-type');
            if (type) {
                this.operate(type);
            }
        }
        if (this.rightMenu) {
            this.rightMenu = false;
            this.setState({});
        }
    }

    private titleClick(target: HTMLElement): void {
        const dataIndex = target.getAttribute('data-index');
        if (!dataIndex) {
            return;
        }
        if (target.className.indexOf(' active') === -1) {
            target.className += ' active';
        } else {
            target.className = target.className.replace(' active', '');
        }
        // const arrs = dataIndex.split('-');
        // const activeIndexs = this.activeIndexs[arrs[0]];
        // const flag = activeIndexs[arrs[1]];
        // activeIndexs[arrs[1]] = flag !== true ? true : false;
        // this.setState({});
    }

    private liClick(target: HTMLElement): void {
        const dataIndex = target.getAttribute('data-index');
        if (!dataIndex) {
            return;
        }
        const arrs = dataIndex.split('-');
        // const name = target.textContent;
        const name = target.getAttribute('data-value');
        this.activeName = name;
        this.setState({});
        if (arrs[0] === '2') {
            this.jumpStruct(name);
            return;
        }

        switch (arrs[1]) {
            case '0':
            case '1':
            case '2': { // 结构化元素
                this.jumpStruct(name);
                break;
            }
            case '3': { // 区域
                this.jumpRegion(name);
                break;
            }
            case '4':
            case '5': { // 图片
                this.jumpImage(name);
                break;
            }
            case '6': { // 表格
                this.jumpTable(name);
                break;
            }
            case '7': { // 批注
                this.jumpComment(name);
                break;
            }
            default:
        }
    }

    // private blurImage(): void {
    //     if (!this.image) {
    //         return;
    //     }

    //     gEvent.setEvent(this.host.docId, gEventName.SelectImage, this.image, false);
    //     this.image = null;
    // }

    private jumpStruct(name: string): void {
        const regions = this.data.regions;
        if (regions.includes(name)) {
            this.jumpRegion(name);
            return;
        }

        if (this.showSerialNumberText()) {
            name = name.split('   显示名称:')[0];
        }

        this._documentCore.cursorJumpOutOfOneStruct(name, 3);
        this.props.refresh(true);
    }

    private jumpRegion(name: string): void {
        const res = this._documentCore.jumpInRegion(name);
        if (res === 0) {
            this.props.refresh(true);
        }
    }

    private jumpComment(name: string): void {
        const res = this._documentCore.moveCursorInComment(name);
        if (res === true) {
            this.props.refresh(true);
        }
    }

    private jumpImage(name: string): Promise<void> {
        return new Promise((resolve, reject) => {
            const res = this._documentCore.selectedImageByName(name);
            if (res === 0) {
                this.props.refresh(true);
                // this.timeout2 = undefined;
                // gEvent.addEvent(this.host.docId, gEventName.MoveCursor, this.cursorChange);
                // this.callback = resolve;
                // this.props.refresh(true);
                // this.image = name;
                // this.timeout2 = setTimeout(() => {
                //     this.timeout2 = null;
                //     gEvent.setEvent(this.host.docId, gEventName.SelectImage, name, true);
                //     gEvent.deleteEvent(this.host.docId, gEventName.MoveCursor, this.cursorChange);
                //     if (this.callback) {
                //         this.callback();
                //         this.callback = null;
                //     }
                // }, 20);
            }
        });
    }

    // private cursorChange = (): void => {
    //     if (this.timeout2 === null) {
    //         return;
    //     }
    //     clearTimeout(this.timeout2);
    //     gEvent.setEvent(this.host.docId, gEventName.SelectImage, this.image, true);
    //     gEvent.deleteEvent(this.host.docId, gEventName.MoveCursor, this.cursorChange);
    //     if (this.callback) {
    //         this.callback();
    //         this.callback = null;
    //     }
    // }

    private jumpTable(name: string): void {
        const res = this._documentCore.moveCursorToTableByName(name);
        if (res === 0) {
            this.props.refresh(true);
        }
    }

    private open = (): void => {
        this.updateData();
        this.setTimeUpdateData();
    }

    private updateData(): void {
        if (this.visible !== true) {
            return;
        }
        this.data = this._documentCore.getNavMenus();
        this.setState({});
    }

    private setTimeUpdateData(): void {
        clearInterval(this.timeout);
        this.timeout = setInterval(() => {
            this.updateData();
        }, 6000);
    }

    private close = (): void => {
        this.searchName = '';
        this.visible = false;
        clearInterval(this.timeout);
        this.props.close(this.props.id, false);
        this.setState({});
    }

    private renderList(index: number): any {
        const arrs = this.activeIndexs[index];
        const list = this['nav' + index];
        const menus = this.data;
        return list.map((item, itemIndex: number) => {
            let datas = menus[item.id];
            let ul: any;
            let className = 'title';
            if (item.id === 'signature') {
                datas = [].concat(menus['signature1'], menus['signature2']);
                ul = (
                    <div className='item'>
                        <span className='item-title'>一般</span>
                        {this.renderUl(menus['signature1'], [2, itemIndex])}
                        <span className='item-title'>集合</span>
                        {this.renderUl(menus['signature2'], [2, itemIndex])}
                    </div>
                );
            } else if (menus[item.id] && !Array.isArray(menus[item.id]) && typeof menus[item.id] === 'object') {
                // datas = [].concat(menus['signature1'], menus['signature2']);
                const childs = menus[item.id] || {};
                const serialNumbers = [];
                const keys = Object.keys(childs);
                keys.forEach((key) => {
                    const items = childs[key];
                    if (items.length < 2) {
                        return;
                    }
                    serialNumbers.push(
                        <React.Fragment key={key}>
                            <span className='item-title icon' key={key}>{key}</span>
                            {this.renderUl(items, [2, itemIndex])}
                        </React.Fragment>
                    );
                });
                if (serialNumbers.length) {
                    className += ' icon';
                    ul = (
                        <div className='item'>
                            {serialNumbers}
                        </div>
                    );
                }
            } else {
                ul = this.renderUl(datas, [index, itemIndex], true);
            }
            const bFont = datas && datas.length > 0;
            if (bFont) {
                className += ' icon';
                if (arrs[itemIndex] === true) {
                    className += ' active';
                }
            }
            return (
                <div className='list' key={item.id}>
                    <span className={className} data-index={index + '-' + itemIndex}>{item.name}</span>
                    {ul}
                </div>
            );
        });
    }

    private renderUl(datas: string[], indexs: number[], fromUI: boolean = false): any {
        if (!datas || datas.length === 0) {
            return;
        }
        const activeName = this.activeName;
        const serialNumbers = this.data.serialNumber;
        const mustInputHasContent = this.data.mustInputHasContent;
        const bShowSerialNumber = this.showSerialNumberText();
        const seperateContent = '⠀    ';
        const lis = datas.map((data, index) => {
            let className = '';
            let textContent = data;
            if (activeName === data) {
                className += 'active ';
            }

            // add color identifier in mustInput item
            if (fromUI && indexs[0] === 2 && indexs[1] === 2 && data in mustInputHasContent) {
                if (mustInputHasContent[data]) {
                    className += 'value ';
                } else {
                    className += 'noValue ';
                }
            }

            if (bShowSerialNumber && serialNumbers && 2 === indexs[0]) {
                // if serialNo enabled && within '导航', change text
                if (data in serialNumbers && serialNumbers[data]) {
                    // textContent += `   显示名称: ${serialNumbers[data] || ''}`;
                    textContent += (seperateContent + `${serialNumbers[data] || ''}`);
                }

                if (activeName === textContent) {
                    className += 'active ';
                }
            }

            // tslint:disable-next-line: max-line-length
            return (<li className={className} key={data + index} data-index={indexs.join('-')} data-value={data}>{textContent}</li>);
        });
        return (<ul className='content'>{lis}</ul>);
    }

    private onChange = (value: any, name: string): void => {
        this.setState<never>({[name]: !this.state[name]});
    }

    private showSerialNumberText(): boolean {
        return this.state.showSerialNumber === true;
    }

    private getStructName(): string {
        let name = this.activeName;
        if (!name) {
            return;
        }

        if (this.showSerialNumberText()) {
            // name = name.split('   显示名称:')[0];
            const seperateContent = '⠀    ';
            name = name.split(seperateContent)[0];
        }

        return (name || this.activeName);
    }

    private onInputChange = (event: any): void => {
        const keyword = event.target.value;
        if (keyword === this.searchName) {
            return;
        }

        this.searchName = keyword;
        this.setState({});
    }

    private confirm = (): void => {
        if (!this.searchName) {
            return;
        }

        const newControl = this._documentCore.getNewControlByName(this.searchName);
        if (newControl) {
            this._documentCore.cursorJumpOutOfOneStruct(this.searchName, 3);
            this.activeName = this.searchName;
            this.updateData();
            this.props.refresh(true);
            return;
        } else {
            const region = this._documentCore.getRegionByName(this.searchName);
            if (region) {
                this._documentCore.jumpInRegion(this.searchName);
                this.activeName = this.searchName;
                this.updateData();
                this.props.refresh(true);
            } else {
                message.warning('未找到该结构化元素', {time: 3000});
            }
        }
    }

    private keydown = (e: any) => {
        if (13 === e.keyCode) {
            this.confirm();
        }
    }
}
