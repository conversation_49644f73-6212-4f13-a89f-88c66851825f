import * as React from 'react';
import { INewControlProperty, NewControlContentSecretType, NewControlType } from '../../../../common/commonDefines';
import Input from '../../ui/Input';
import Checkbox from '../../ui/CheckboxItem';
import Select from '../../ui/select/Select';
import CustomProperty from './CustomProperty';
import { NewControlManageInfo } from './NewControlManage';

interface IDialogProps {
    documentCore: any;
    refresh: () => void;
    property: INewControlProperty;
    flag: number;
    visible?: boolean;
}

export default class NewComboxBox extends React.Component<IDialogProps> {
    private newControlTypeText: string;
    private playTypes: any[];
    private newControl: INewControlProperty;
    constructor(props: IDialogProps) {
        super(props);
        this.playTypes = [
            {key: '不保密', value: NewControlContentSecretType.DontSecret},
            {key: '全部保密', value: NewControlContentSecretType.AllSecret},
            {key: '部分保密', value: NewControlContentSecretType.PartSecret},
        ];
        this.init(this.props);
    }

    public UNSAFE_componentWillReceiveProps(nextProps: IDialogProps): void {
        const props = this.props;
        const flag = props.flag;
        const nextFlag = nextProps.flag;
        if (nextFlag === flag) {
            return;
        }
        // if (visible === nextVisible) {
        //     if (props.property.newControlName === nextProps.property.newControlName) {
        //         return;
        //     }
        // }
        // if (nextProps.property.newControlName === this.props.property.newControlName) {
        //     return;
        // }
        this.init(nextProps);
    }

    public render(): any {
        return (
            <React.Fragment>
                <div className='editor-line'>
                        <span className='title'>常规</span>
                    </div>
                    <div className='editor-line'>
                        <div className='w-70'>名称</div>
                        <div className='right-auto'>
                            <Input
                                name='newControlName'
                                value={this.newControl.newControlName}
                                onChange={this.onChange}
                                onBlur={this.onBlur}
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <div className='w-70'>内部名称</div>
                        <div className='right-auto'>
                            <Input
                                name='newControlSerialNumber'
                                value={this.newControl.newControlSerialNumber}
                                onChange={this.onChange}
                                readonly={true}
                                placeholder={''}
                                disabled={true}
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <div className='w-70'>提示符</div>
                        <div className='right-auto'>
                            <Input
                                name='newControlInfo'
                                value={this.newControl.newControlInfo}
                                onChange={this.onChange}
                                onBlur={this.onBlur}
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <div className='w-70'>占位符</div>
                        <div className='right-auto'>
                            <Input
                                name='newControlPlaceHolder'
                                value={this.newControl.newControlPlaceHolder}
                                onChange={this.onChange}
                                onBlur={this.onBlur}
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <div className='w-70'>标题</div>
                        <div className='right-auto'>
                            <Input
                                name='newControlTitle'
                                value={this.newControl.newControlTitle}
                                onChange={this.onChange}
                                onBlur={this.onBlur}
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <div className='w-70'>类型</div>
                        <div className='right-auto'>
                            <Input
                                name='newControlTitle'
                                value={this.newControlTypeText}
                                disabled={true}
                            />
                        </div>
                    </div>
                    <div className='editor-line editor-multi-line'>
                        <span className='title w-70'>属性</span>
                        <div className='right-auto newcontrol-prop'>
                            <div className='editor-line'>
                                <div className='w-050'>
                                    <Checkbox
                                        name='isNewControlHidden'
                                        disabled={false}
                                        value={this.newControl.isNewControlHidden}
                                        onChange={this.checkedChange}
                                    >
                                        隐藏
                                    </Checkbox>
                                </div>
                                <div className='w-050'>
                                    <Checkbox
                                        name='isNewControlCanntDelete'
                                        value={this.newControl.isNewControlCanntDelete}
                                        onChange={this.checkedChange}
                                    >
                                        禁止删除
                                    </Checkbox>
                                </div>
                            </div>

                            <div className='editor-line'>
                                <div className='w-050'>
                                    <Checkbox
                                        name='isNewControlCanntEdit'
                                        value={this.newControl.isNewControlCanntEdit}
                                        onChange={this.checkedChange}
                                    >
                                        禁止编辑
                                    </Checkbox>
                                </div>
                                <div className='w-050'>
                                    <Checkbox
                                        name='isNewControlMustInput'
                                        value={this.newControl.isNewControlMustInput}
                                        onChange={this.checkedChange}
                                    >
                                        必填项
                                    </Checkbox>
                                </div>
                            </div>

                            <div className='editor-line'>
                                <div className='w-050'>
                                    <Checkbox
                                        name='isNewControlShowBorder'
                                        value={this.newControl.isNewControlShowBorder}
                                        onChange={this.checkedChange}
                                    >
                                        显示边框
                                    </Checkbox>
                                </div>

                                <div className='w-050'>
                                    <Checkbox
                                        name='isNewControlReverseEdit'
                                        value={this.newControl.isNewControlReverseEdit}
                                        onChange={this.checkedChange}
                                    >
                                        反向编辑
                                    </Checkbox>
                                </div>
                            </div>

                            <div className='editor-line'>
                                <div className='w-050'>
                                    <Checkbox
                                        name='isNewControlHiddenBackground'
                                        value={this.newControl.isNewControlHiddenBackground}
                                        onChange={this.checkedChange}
                                    >
                                        隐藏背景色
                                    </Checkbox>
                                </div>
                                <div className='w-050'>
                                    <Checkbox
                                        name='tabJump'
                                        value={this.newControl.tabJump}
                                        onChange={this.checkedChange}
                                    >
                                        键盘跳转
                                    </Checkbox>
                                </div>
                            </div>
                        </div>
                    </div>
                    {this.renderExternal()}
                    <div className='editor-line'>
                        <span className='title'>自定义属性</span>
                    </div>
                    <div className='editor-line'>
                        <CustomProperty
                            name='customProperty'
                            properties={this.newControl.customProperty}
                            documentCore={this.props.documentCore}
                        />
                    </div>
            </React.Fragment>
        );
    }

    private renderExternal(): any {
        if (this.newControl.newControlType !== NewControlType.TextBox) {
            return null;
        }
        return (
            <React.Fragment>
                <div className='editor-line'>
                    <span className='title'>扩展属性</span>
                </div>
                <div className='editor-line'>
                    <div className='w-70'>
                        隐私显示
                    </div>
                    <div className='right-auto'>
                        <Select
                            data={this.playTypes}
                            value={this.newControl.newControlDisplayType}
                            name='newControlDisplayType'
                            onChange={this.checkedChange}
                        />
                    </div>
                </div>
                <div className='editor-line'>
                    <div className='w-70'>
                        固定长度
                    </div>
                    <div className='right-auto'>
                        <Input
                            disabled={true}
                            onChange={this.onChange}
                            value={this.newControl.newControlFixedLength}
                            name='newControlFixedLength'
                            type='number'
                            onBlur={this.onBlur}
                            renderAppend={this.renderCell}
                        />
                    </div>
                </div>
                <div className='editor-line'>
                    <div className='w-70'>
                        最大长度
                    </div>
                    <div className='right-auto'>
                        <Input
                            onChange={this.onChange}
                            value={this.newControl.newControlMaxLength}
                            name='newControlMaxLength'
                            type='number'
                            onBlur={this.onBlur}
                            renderAppend={this.renderCell}
                        />
                    </div>
                </div>
            </React.Fragment>
        );
    }

    private renderCell(): any {
        return '字符';
    }

    private init(props: IDialogProps): void {
        const newControl = this.newControl = {newControlName: null};
        const property = props.property;
        if (!property) {
            return;
        }
        Object.keys(property)
        .forEach((key) => {
            newControl[key] = property[key];
        });
        switch (property.newControlType) {
            case NewControlType.TextBox: {
                this.newControlTypeText = '简单元素';
                break;
            }
            default: {
                this.newControlTypeText = '节';
            }
        }
    }

    private onChange = (value: any, name: string): void => {
        this.newControl[name] = value;
    }

    private checkedChange = (value: any, name: string): void => {
        this.onChange(value, name);
        this.confirm(name);
    }

    private isValidData(name: string): boolean {
        const value = this.newControl[name];
        return true;
    }

    private onBlur = (name: string): void => {
        this.confirm(name);
    }

    private confirm(name: string): any {
        const value = this.newControl[name];
        NewControlManageInfo.confirm(name, value);
    }
}
