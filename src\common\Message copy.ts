import { getEditorDomContainer } from './commonDefines';
import {IFRAME_MANAGER} from './IframeManager';

export interface IMessageOption {
    title?: string;
    width?: string;
    height?: string;
    left?: string;
    right?: string;
    className?: string;
    time?: number;
    btns?: string[];
    autoClose?: boolean;
    onlyBtnClose?: boolean;
    cancelDefault?: boolean;
}

enum MessageType {
    Info,
    Warning,
    Error,
    Confirm,
}

// let isHasMessageStyle: boolean = false;
let prevCancelDefault: boolean = false;
class Message {
    protected _resolve: (index: number) => void;
    protected _reject: (index: number) => void;
    protected _type: number;
    private _msgDom: HTMLDivElement;
    private _msgTitleDom: HTMLSpanElement;
    private _msgBodyDom: HTMLSpanElement;
    private _msgBtnDom: HTMLDivElement;
    private _defaultTitle: string;
    private _defaultBtns: string[];
    private _bChangeTitle: boolean;
    private _bChangeBtn: boolean;
    private _defatultTime: number;
    private _clearTime: any;
    private _onlyBtnClose: boolean;

    private _container: HTMLElement;
    constructor(container?: HTMLElement) {
        this._container = container;
    }

    public info(content: string, options?: IMessageOption): Promise<number> {
        this._type = MessageType.Info;
        this._init();
        this._setMessage(content, options);
        return new Promise((resolve, reject) => {
            this._resolve = resolve;
            this._reject = reject;
        });
    }

    public warning(content: string, options?: IMessageOption): Promise<number> {
        this._type = MessageType.Warning;
        this._init();
        this._setMessage(content, options);
        return new Promise((resolve, reject) => {
            this._resolve = resolve;
            this._reject = reject;
        });
    }

    public error(content: string, options?: IMessageOption): Promise<number> {
        this._type = MessageType.Error;
        this._init();
        this._setMessage(content, options);
        return new Promise((resolve, reject) => {
            this._resolve = resolve;
            this._reject = reject;
        });
    }

    protected _init(className: string = '', cancelDefault: boolean = false): void {
        if (this._msgDom && prevCancelDefault === cancelDefault) {
            return;
        }
        prevCancelDefault = cancelDefault;

        this._defaultBtns = ['确定'];
        this._defaultTitle = '提示信息';
        this._bChangeTitle = false;
        this._bChangeBtn = false;
        this._defatultTime = 5000;

        if (this._type === MessageType.Error) {
            this._defaultBtns = [];
            this._defaultTitle = '警示';
            this._defatultTime = 3000;
            this._bChangeTitle = true;
        } else if (this._type === MessageType.Confirm) {
            this._defaultTitle = '二次确认';
            this._bChangeTitle = true;
        }

        const additionalClass = cancelDefault === true ? 'cancelDefault' : '';
        const msgTemplate = `
            <div class="editor-message-box">
                <div class="editor-message-title">
                    <span>提示信息</span>
                    <div class="editor-message-close" data-index="0">&#x2715;</div>
                </div>
                <div class="editor-message-body">
                    <span></span>
                </div>
                <div class="editor-message-btns ${additionalClass}">
                    <span data-index="0">确定</span>
                </div>
            </div>
        `;

        const container = this._container || getEditorDomContainer();
        const doc = container.ownerDocument;
        const dom = doc.createElement('div');
        dom.className = 'editor-message' + className;
        dom.innerHTML = msgTemplate;
        // if (isHasMessageStyle === false && !this._container) {
        //     isHasMessageStyle  = true;
        //     const style = doc.createElement('style');
        //     style.type = 'text/css';
        //     style.innerHTML = getMessageStyle();
        //     container.appendChild(style);
        // }
        container.appendChild(dom);
        this._msgDom = dom;
        this._msgTitleDom = dom.querySelector('.editor-message-title>span');
        this._msgBtnDom = dom.querySelector('.editor-message-btns');
        this._msgBodyDom = dom.querySelector('.editor-message-body>span');

        this._addEvent();
    }

    protected _setMessage(content: string, options: IMessageOption): void {
        if (!content) {
            return;
        }
        const obj: IMessageOption = (options || {}) as IMessageOption;
        let title: string;
        let btns: string[];
        if (obj.title !== undefined) {
            this._bChangeTitle = true;
            title = obj.title;
        } else if (this._bChangeTitle === true) {
            this._bChangeTitle = false;
            title = this._defaultTitle;
        }

        if (obj.btns && obj.btns.length) {
            this._bChangeBtn = true;
            btns = obj.btns;
        } else if (this._bChangeBtn) {
            this._bChangeBtn = false;
            btns = this._defaultBtns;
        }

        if (title !== undefined) {
            this._msgTitleDom.innerHTML = title;
        }

        if (btns) {
            this._msgBtnDom.innerHTML = btns.map((btn, index) => {
                return `<span data-index="${index}">${btn}</span>`;
            })
            .join('');
        } else {
            this._msgBtnDom.innerHTML = '';
        }

        if (this._type === MessageType.Error) {
            this._msgBodyDom.innerHTML =
            `
                <div class="editor-message-warning">&#x26A0;</div>
                <p class="editor-message-content">${content}</p>
            `;
        } else {
            this._msgBodyDom.innerHTML = content;
        }

        this._show();
        if (obj.autoClose === false) {
            return;
        }
        const time = obj.time !== undefined ? obj.time : this._defatultTime;
        clearTimeout(this._clearTime);
        this._clearTime = setTimeout(() => {
            this._hide();
            this._resolve(-1);
        }, time);
    }

    private _handleClick = (event: Event): void => {
        const target = event.target as HTMLElement;
        const index = target.getAttribute('data-index');
        if (index) {
            return this._btnsClick(+index);
        }
        if (this._onlyBtnClose === true) {
            return;
        }
        if (this._type === MessageType.Confirm || this._type === MessageType.Error) {
            return;
        }
        this._close();
        if (!index) {
            this._resolve(-1);
        }
    }

    private _btnsClick(index: number): void {
        // console.log(index)
        if (this._type === MessageType.Confirm) {
            if (index === 0) {
                this._reject(index);
            } else {
                this._resolve(index);
            }
        } else {
            this._resolve(index);
        }

        this._close();
    }

    private _addEvent(): void {
        this._msgDom.addEventListener('click', this._handleClick);
        // this._msgDom.addEventListener('mouseup', (e) => {
        //     e.stopPropagation();
        //     e.preventDefault();
        // });
        // this._msgDom.addEventListener('mousemove', (e) => {
        //     e.stopPropagation();
        //     e.preventDefault();
        // });
        // this._msgDom.addEventListener('mousedown', (e) => {
        //     e.stopPropagation();
        //     e.preventDefault();
        // });
    }

    private _close(): void {
        this._hide();
        clearTimeout(this._clearTime);
    }

    private _hide(): void {
        this._msgDom.className = this._msgDom.className.replace(/ active/, '');
    }

    private _show(): void {
        const className = this._msgDom.className;
        if (className.indexOf(' active') > -1) {
            return;
        }
        this._msgDom.className = className + ' active';
    }
}

class MessageConfirm extends Message {
    public confirm(content: string, options?: IMessageOption): Promise<number> {
        this._type = MessageType.Confirm;
        const cancelDefault = (options != null) ? options.cancelDefault : false;
        this._init(' editor-confirm', cancelDefault);
        options = options || {};
        options.time = 10e10;
        options.btns = ['取消', '确认'];
        this._setMessage(content, options);
        return new Promise((resolve, reject) => {
            this._resolve = resolve;
            this._reject = reject;
        });
    }
}

// tslint:disable-next-line: max-classes-per-file
class MessageManager {
    private _managers: any;
    constructor() {
        this._managers = {};
        IFRAME_MANAGER.deleteCallback((id: number) => {
            delete this._managers[id];
        });
    }

    public info(content: string, options?: IMessageOption): Promise<number> {
        return this.getMessage()
            .info(content, options);
    }

    public error(content: string, options?: IMessageOption): Promise<number> {
        return this.getMessage()
            .error(content, options);
    }

    public warning(content: string, options?: IMessageOption): Promise<number> {
        return this.getMessage()
            .warning(content, options);
    }

    private getMessage(): Message {
        const id = IFRAME_MANAGER.getDocId();
        let msg = this._managers[id];
        if (!msg) {
            const win = IFRAME_MANAGER.getWindow(id);
            msg = this._managers[id] = new Message(win.document.body.firstElementChild as HTMLElement);
        }
        return msg;
    }
}

// tslint:disable-next-line: max-classes-per-file
class ConfirmManager {
    private _managers: any;
    constructor() {
        this._managers = {};
        IFRAME_MANAGER.deleteCallback((id: number) => {
            delete this._managers[id];
        });
    }

    public confirm(content: string, options?: IMessageOption): Promise<number> {
        return this.getMessage()
            .confirm(content, options);
    }

    private getMessage(): MessageConfirm {
        const id = IFRAME_MANAGER.getDocId();
        let msg = this._managers[id];
        if (!msg) {
            const win = IFRAME_MANAGER.getWindow(id);
            msg = this._managers[id] = new MessageConfirm(win.document.body.firstElementChild as HTMLElement);
        }
        return msg;
    }
}

export let message = new MessageManager();

export let layer = new ConfirmManager();
