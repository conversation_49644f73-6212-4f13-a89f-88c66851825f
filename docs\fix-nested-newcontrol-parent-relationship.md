# 修复嵌套 NewControl 父子关系问题

## 问题描述

在 `replaceSpecificStructPropWithBackStream` 接口中，当源内容包含嵌套的 sections 和简单元素（sdt）时，简单元素在替换过程中丢失父子关系，导致无法正确建立层级结构。

## 问题根因

1. **解析过程使用临时文档**：`getSpecificStructContentWithBackStream` 创建临时文档进行解析，与当前文档的 `newControlManager` 不是同一实例
2. **unclosedNewControls 栈被重置**：每次调用 `tTraverseSection` 时，`unclosedNewControls` 数组都是空的，无法维护正确的嵌套层次
3. **边界部分未正确重建**：`bInsertFile: true` 情况下，NewControl 的 `startBorderPortion` 和 `endBorderPortion` 没有被正确设置

## 解决方案

### 1. 目标父节点上下文传递机制

**修改文件：** `src/common/external/OperateDocument.ts`

```typescript
// 在 replaceSpecificStructPropWithBackStream 中提取目标父节点名称
const targetParentName = targets.length > 0 ? (targets[0] as any).name : undefined;

// 传递给解析方法
const result: any[][] = await this.getSpecificStructContentWithBackStream(sources, content, type, targetParentName);

// 在临时文档中设置目标父节点上下文
if (targetParentName) {
    doc.getNewControlManager().setTargetParentContext(targetParentName);
}
```

### 2. 解析阶段使用目标父节点上下文

**修改文件：** `src/format/reader/paragraph.ts`

```typescript
// 在 tTraverseSection 中优先使用目标父节点上下文
const targetParentContext = newControlManager.getTargetParentContext();
const parentName = targetParentContext || parentControl?.getNewControlName();

// 在 tTraverseSdt 中同样使用目标父节点上下文
const targetParentContext = newControlManager.getTargetParentContext();
const parentName = targetParentContext || lastElemInUnclosedNewControls?.getNewControlName();
```

### 3. 边界部分重建机制

**修改文件：** `src/model/core/NewControlManager.ts`

```typescript
// 新增目标父节点上下文管理方法
public setTargetParentContext(parentName: string): void {
    this.targetParentContext = parentName;
}

public getTargetParentContext(): string | undefined {
    return this.targetParentContext;
}

public clearTargetParentContext(): void {
    this.targetParentContext = undefined;
}

// 新增专门处理 bInsertFile 情况的方法
private adjustInsertFileNewControls(parent: DocumentContentBase, pasteNewControls: IPasteNewControl[]): NewControl[] {
    // 设置正确的父文档
    newControlContent.setParent(parent);

    // 重建边界部分
    this.rebuildNewControlBorders(newControl, parent);
}

// 重建 NewControl 的边界部分
private rebuildNewControlBorders(newControl: NewControl, parent: DocumentContentBase): void {
    // 确保 startBorderPortion 和 endBorderPortion 的段落父级正确
}
```

## 核心流程

1. **目标识别**：从 `targets` 中提取目标父节点名称（如 `section_9rVYPsWY`）
2. **上下文传递**：将目标父节点名称作为 `targetParentContext` 传递给临时文档的解析过程
3. **解析优化**：在解析阶段，当 `unclosedNewControls` 为空时，使用 `targetParentContext` 作为 `parentName`
4. **边界重建**：在 `adjustInsertFileNewControls` 中重建 NewControl 的边界部分和父文档关系

## 修改影响

- ✅ 支持复杂嵌套结构替换（新功能）
- ✅ 保持简单结构替换正常工作（原有功能）
- ✅ 嵌套简单元素正确建立父子关系
- ✅ NewControl 边界部分正确初始化

## 验证方法

1. **复杂嵌套场景**：调用包含多层嵌套 sections 和 sdt 的替换操作
2. **简单场景**：确保原有的简单替换功能不受影响
3. **文本设置**：验证替换后的 NewControl 可以正常设置文本内容

## 设计优势

1. **命名清晰**：`setTargetParentContext` 明确表达了设置目标父节点上下文的意图
2. **职责合理**：功能归属于 `NewControlManager`，符合其管理 NewControl 关系的职责
3. **独立性强**：不依赖于 Region 相关的概念，避免命名混淆
4. **向后兼容**：不影响现有的 `setRegionContext` 功能和其他流程
