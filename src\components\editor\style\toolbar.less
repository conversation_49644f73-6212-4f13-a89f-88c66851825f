@import './global.less';

@keyframes show-tip  {
    0% {
        height: 0;
    }
    5% {
        height: 22px;
    }
    95% {
        height: 22px;
    }
    100% {
        height: 0;
    }
}

.hz-editor-container {
    .select-color .flexbox-fix:last-child {
        & > div:last-child {
            position: relative;
            width: 100% !important;
            height: 20px !important;
            &::after {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                line-height: 20px;
                text-align: center;
                content: '无颜色';
                pointer-events: none;
            }
        }
    }

    .editor-toolbar {
        position: relative;
        width: 100%;

        margin: 0 auto;
        padding: 12px 0;
        font-size: @fontSize;
        font-family: @fontFamily;
        color: @color;

        @bgColor: #EBEFF2;
        @bgHoverColor: #fff;
        @activeBgColor: #737B80;

        background: @bgColor;

        & *,
        & {
            box-sizing: border-box;
        }

        .menu-select-list {
            display: none;
            position: absolute;
            z-index: 9;
            margin-top: 10px;

            &.active {
                display: block;
            }

            & > ul {
                max-height: 200px;
                padding: 12px 0;
                text-align: left;
                box-shadow: 0px 2px 20px 0px rgba(39, 53, 70, 0.2);
                border-radius: 4px;
                border: 1px solid rgba(221, 221, 227, 1);
                overflow: auto;
                background-color: #fff;

                & > li {
                    height: 20px;
                    line-height: 20px;
                    padding: 0 23px;
                    cursor: pointer;
                    background-color: #fff;

                    &:hover,
                    &.active {
                        background-color: @bgHoverColor;
                    }

                    & > i {
                        display: none;
                    }

                    &.active {
                        position: relative;
                        & > i {
                            display: inline-block;
                            position: absolute;
                            top: 6px;
                            left: 8px;
                            width: 8px;
                            height: 8px;
                            background: url(../images/selected.png) no-repeat center;
                        }
                    }

                    .icon {
                        display: inline-block;
                        width: 16px;
                        height: 16px;
                        margin-right: 5px;
                        position: relative;
                        top: 3px;
                    }
                    & > span.iconfont {
                        color: #000;
                    }
                    // span.leftAlign {
                    //     background: url(../images/leftAlign.png) no-repeat center;
                    // }

                    // span.centerAlign {
                    //     background: url(../images/centerAlign.png) no-repeat center;
                    // }

                    // span.rightAlign {
                    //     background: url(../images/rightAlign.png) no-repeat center;
                    // }

                    // span.justifyAlign {
                    //     background: url(../images/justifyAlign.png) no-repeat center;
                    // }

                }
            }
        }

        .toolbar-view-scale {
            position: absolute;
            z-index: 2;
            width: 140px;
            text-align: left;
            background: #fff;
            border-radius: 3px;
            border: 1px solid #ddd;

            .editor-radio {
                width: 100%;
                .radio-item {
                    display: block;
                    width: 100%;
                    margin-right: auto;
                    padding: 6px 12px;
                    &:hover {
                        background-color: #e6f7ff;
                    }
                    input {
                        height: 27px;
                        top: 0px;
                    }
                }
            }

            & > label {
                display: block;
                padding-left: 12px;
                line-height: 28px;
            }
            .input {
                padding: 0 12px;
                padding-bottom: 6px;
                & > span {
                    input {
                        width: 100px;
                        outline: none;
                        -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
                        border: 1px solid @borderColor;
                        &:active, &:focus, &:hover {
                            border-color: @activeColor;
                        }
                    }
                    label {
                        padding-left: 3px;
                    }
                }
            }
            .scale-btns {
                padding: 12px;
                padding-top: 6px;
                padding-bottom: 8px;
                text-align: right;
                border-top: 1px solid @borderColor;
                span {
                    display: inline-block;
                    padding-left: 10px;
                    cursor: pointer;
                    &:last-child {
                        color: @activeColor;
                    }
                }
            }
        }

        & > ul {
            display: flex;
            text-align: center;
            justify-content: left;
            padding-right: 10px;

            & > li {
                flex: 0 auto;
                position: relative;
                height: 28px;
                line-height: 28px;
                justify-content: center;
                cursor: pointer;

                &::before {
                    content: '';
                }

                & > span {
                    display: inline-block;
                    width: 28px;
                    height: 28px;
                    margin-right: 2px;
                    text-align: center;
                }

                &:hover span {
                    background-color: @bgHoverColor;
                }

                &.active, &:active {
                    span {
                        background-color: @activeBgColor;
                    }
                    span.iconfont {
                        line-height: 28px; // 与按钮高度相同，确保垂直居中
                    }
                }

                &.disabled span {
                    cursor: default;
                    background-color: none;
                    opacity: 0.3;
                    &:hover {
                        background-color: none;
                    }
                }

                & > span.iconfont {
                    font-size: 20px;
                    color: #000;

                    &.custom {
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        img {
                            width: 1em;
                            height: 1em;
                        }
                    }

                }
                &.align > span {
                    width: 36px;
                    padding-left: 1px;
                }

                // &.autoFormat {
                //     position: relative;
    
                //     &:hover::after {
                //         content: '删除文档末尾的空字符以及空段落';
                //         position: absolute;
                //         top: 105%;
                //         left: 0;
                //         border: 1px solid #ACB4C1;
                //         display: block;
                //         padding: 2px 4px;
                //         z-index: 100;
                //         font-size: 12px;
                //         line-height: 1.5em;
                //         color: #737B80;
                //         background-color: white;
                //         white-space: nowrap;
                //     }
                // }

                & > label {
                    display: none;
                    position: absolute;
                    top: 105%;
                    left: 0;
                    z-index: 100;
                    height: 0;
                    padding: 0 4px;
                    font-size: 12px;
                    line-height: 22px;
                    color: #fff;
                    background-color: #000;
                    white-space: nowrap;
                    box-shadow: 1px 1px 3px #999;
                    opacity: 0.7;
                    pointer-events: none;
                    overflow: hidden;
                }
                &:hover {
                    & > label {
                        display: block;
                        animation: show-tip 4s  1 ease;
                    }
                }
            }

            [data-index='separator'] {
                flex: 0 auto;
                width: auto;
                line-height: 34px;

                & > span {
                    display: inline-block;
                    width: 0;
                    height: 24px;
                    margin: 0 7px;
                    vertical-align: sub;
                    border: none;
                    border-left: 1px solid #aaa;
                }
            }

            // .cut > span {
            //     background: url(../images/cut.png) no-repeat center;
            // }

            // .copy > span {
            //     background: url(../images/copy.png) no-repeat center;
            // }

            // .paste > span {
            //     background: url(../images/paste.png) no-repeat center;
            // }

            // .undo > span {
            //     background: url(../images/undo.png) no-repeat center;
            // }

            // .redo > span {
            //     background: url(../images/redo.png) no-repeat center;
            // }

            // .clearFormats > span {
            //     background: url(../images/clearFormats.png) no-repeat center;
            // }

            // .clearFormats-disabled > span {
            //     background: url(../images/clearFormats-disabled.png) no-repeat center;
            // }

            // .align > span {
            //     background: url(../images/leftAlign.png) no-repeat center;
            // }

            [data-index='align'] {
                & > .leftAlign {
                    .select-btn-prefix();
                }

                & > .centerAlign {
                    .select-btn-prefix();
                }

                & > .rightAlign {
                    .select-btn-prefix();
                }

                & > .justifyAlign {
                    .select-btn-prefix();
                }

                & > span {
                    width: 32px;
                    padding-left: 2px;
                    text-align: left;
                }

                .select-btn {
                    top: -1px;
                    right: 2px;
                }
            }

            // .leftIndent > span {
            //     background: url(../images/leftIndent.png) no-repeat center;
            // }

            // .rightIndent > span {
            //     background: url(../images/rightIndent.png) no-repeat center;
            // }

            .select-btn-prefix {
                width: 36px;
            }

            [data-index='fontSize'] {
                & > span {
                    .select-btn-prefix();
                    width: 50px;
                    padding-right: 4px;
                    font-size: 14px;
                }
            }

            [data-index='fontFamily'] {
                & > span {
                    width: 60px;
                    padding-right: 13px;
                    font-size: 14px;
                    overflow: hidden;
                    color: #54627b;
                }
            }

            // .bold > span {
            //     background: url(../images/bold.png) no-repeat center;
            // }

            // .bold-active {
            //     background-color: @bgColor;
            //     & > span {
            //         // background: url(../images/bold-active.png) no-repeat center;
            //     }
            // }
            // .bold-disabled {
            //     background-color: @bgColor;
            //     & > span {
            //         // background: url(../images/bold-disabled.png) no-repeat center;
            //     }
            // }

            [data-index='color'] {
                margin-left: 5px;
                & > span {
                    // .select-btn-prefix();
                    width: 31px;
                    // background: url(../images/color.png) no-repeat center;
                    // background: url(../images/color.png) no-repeat center;
                }

                i {
                    display: none;
                }

                & > .color-indicator {
                    display: inline-block;
                    width: 19px;
                    background-color: #000;
                    height: 4px;
                    position: absolute;
                    left: 6px;
                    bottom: 6px;
                    border-radius: 10px;
                }
            }
            [data-index='backgroundColor'] {
                & > span {
                    position: relative;
                    z-index: 1;
                    padding-left: 3px;
                    width: 38px;
                    text-align: left;
                    &:hover {
                        background-color: transparent;
                    }
                }
                & > .color-indicator {
                    height: 22px;
                    width: 22px;
                    position: absolute;
                    z-index: 0;
                    top: 3px;
                    left: 3px;
                    // left: 0.5px;
                }
                i {
                    z-index: 2;
                }
            }

            // .scale {
            //     & > span {
            //         background: url(../images/viewRatio.png) no-repeat center;
            //     }
            // }
            // .backgroundColor,
            // .backgroundColor-white {
            //     & > span {
            //         width: 38px;
            //         background: url(../images/backgroundColor-letter.png) no-repeat
            //             center;
            //         position: relative;
            //         top: 1px;
            //         z-index: 2;
            //     }

            //     i {
            //         display: none;
            //     }

            //     & > .color-indicator {
            //         height: 18px;
            //         width: 18px;
            //         position: absolute;
            //         top: 7px;
            //         // left: 0.5px;
            //     }
            // }

            // .backgroundColor-white {
            //     & > span {
            //         background: url(../images/backgroundColor-white-letter.png)
            //             no-repeat center;
            //     }
            // }

            // .underline > span {
            //     background: url(../images/underline.png) no-repeat center;
            // }

            // .underline-active {
            //     background-color: @bgColor;
            //     & > span {
            //         background: url(../images/underline-active.png) no-repeat center;
            //     }
            // }

            // .italic > span {
            //     background: url(../images/italic.png) no-repeat center;
            // }

            // .subScript > span {
            //     background: url(../images/sub.png) no-repeat center;
            // }

            // .superScript > span {
            //     background: url(../images/sup.png) no-repeat center;
            // }


            // .list > span {
            //     .select-btn-prefix();
            //     background: url(../images/list.png) no-repeat center;
            // }

            & > li[data-index='lineHeight'] {
                & > span {
                    .select-btn-prefix();
                    padding-left: 3px;
                    text-align: left;
                    // background: url(../images/lineHeight.png) no-repeat center;
                }
                .select-btn {
                    top: -1px;
                    right: 2px;
                }
            }

            // .file > span {
            //     background: url(../images/file.png) no-repeat center;
            // }

            // .table > span {
            //     background: url(../images/table.png) no-repeat center;
            // }

            // .image > span {
            //     background: url(../images/image.png) no-repeat center;
            // }

            // .equation > span {
            //     background: url(../images/equation.png) no-repeat center;
            // }

            // .search > span {
            //     background: url(../images/search.png) no-repeat center;
            // }

            // .doPrint > span {
            //     background: url(../images/print.png) no-repeat center;
            // }

            // .export > span {
            //     background: url(../images/export.png) no-repeat center;
            // }

            .select-btn {
                display: inline-block;
                position: absolute;
                top: 0;
                right: 5px;
                width: 9px;
                height: 30px;
                line-height: 30px;
                font-style: normal;
                &::before {
                    display: inline-block;
                    width: 0;
                    height: 0;
                    margin-top: 13px;
                    border: 4px solid transparent;
                    border-top: 4px solid @color;

                    content: ' ';
                }
            }

            // 更多按钮
            .moreIcons  {
                span {
                    width: 38px;
                    padding-right: 12px;
                    font-size: 22px;
                    line-height: 1;
                    background: url(../images/more.png) no-repeat 3px 50%;
                }
                .select-btn {
                    right: 4px;
                }
            }

            &.last-line {
                display: none;
                position: absolute;
                top: 40px;
                z-index: 2;
                background: #fff;
                &.active {
                    display: block;
                    padding-top: 3px;
                }
                li {
                    height: 28px;
                    span {
                        border: 1px solid #999;
                        border-bottom-width: 0;
                    }

                    & > label {
                        right: 0;
                        left: auto;
                    }
                }
                li:last-child span {
                    border-bottom-width: 1px;
                }
            }
        }
    }

    &.toolbar-center  .editor-toolbar {
        & > ul {
            justify-content: center;
        }
    }
}
