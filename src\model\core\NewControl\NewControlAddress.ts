import { HierarchyLevel, INewControlProperty, IStructParamJson } from '../../../common/commonDefines';
import { IAddressPair } from '../../../components/editor/module/document/NewAddressbox';
import DocumentContentBase from '../DocumentContentBase';
import { NewControl } from './NewControl';
import { ChangeNewControlAddrSelection } from './NewControlChange';
// import addressData from '../../../common/resources/china-division.json';
// import addressData from '../../../common/resources/Regions.json';
// import addressData from '../../../common/resources/china-city.json';
import { AddressType, chinaCity, IAddressKey } from '../../../common/ChinaCity';

/**
 * 结构化元素: 地址框
 */
export class NewControlAddress extends NewControl {
  public hierarchy: HierarchyLevel;
  public province: IAddressPair;
  public city: IAddressPair;
  public county: IAddressPair;

  constructor(parent: DocumentContentBase, name: string, property: INewControlProperty, text?: string) {
    super(parent, name, property, '');
    if (property != null) {
      // console.log(property)
      const {hierarchy, province, city, county} = property;
      this.hierarchy = hierarchy || HierarchyLevel.Third; // 不传参数，默认3层
      this.province = province;
      this.city = city;
      this.county = county;
    }
  }

  public getHierarchy(): HierarchyLevel {
    return this.hierarchy;
  }

  public getProvince(): IAddressPair {
    return this.province;
  }

  public getCity(): IAddressPair {
    return this.city;
  }

  public getCounty(): IAddressPair {
    return this.county;
  }

  public setProvince(province: IAddressPair): void {
    this.province = province;
  }

  public setCity(city: IAddressPair): void {
    this.city = city;
  }

  public setCounty(county: IAddressPair): void {
    this.county = county;
  }

  public setProperty(property: INewControlProperty): number {
    const res = super.setProperty(property);

    const history = this.getHistory();
    if ( history ) {
      const oldObj = {
        province: this.province,
        city: this.city,
        county: this.county,
        hierarchy: this.hierarchy,
      };
      const newObj = {
        province: property.province,
        city: property.city,
        county: property.county,
        hierarchy: property.hierarchy,
      };
      history.addChange(new ChangeNewControlAddrSelection(this, oldObj, newObj));
    }

    const {hierarchy, province, city, county} = property;

    // 原来逻辑为 != null
    if (hierarchy !== undefined) {
      this.hierarchy = hierarchy;
    }
    if (province !== undefined) {
      this.province = province;
    }
    if (city !== undefined) {
      this.city = city;
    }
    if (county !== undefined) {
      this.county = county;
    }

    this.setDirty();

    return res;
  }

  public setNewControlTextByJson(json: IStructParamJson): number {
    const prop: {
      province: {code: string},
      city: {code: string},
      county: {code: string}
    } = json.content_text;
    if (!prop || typeof prop !== 'object') {
      return;
    }

    return this.setAddress(prop as any, false);
    // let text = '';
    // const newControl = this;
    // const addressCollection = addressData;
    // // const addressCollection = composeAddressHierarchy(addressData);

    // const {province, city, county} = prop;
    // let provinceIndex;
    // let cityIndex;
    // let countyIndex = -1;

    // if (province != null) {
    //     for (let i = 0, len = addressCollection.length; i < len; i++) {
    //         const provinceItem = addressCollection[i];
    //         if (!provinceItem) {
    //           break;
    //         }
    //         if (province.code === provinceItem['code']) {
    //             provinceIndex = i;

    //             const provincePair: IAddressPair = {code: provinceItem['code'], name: provinceItem['name']};
    //             newControl.setProvince(provincePair);
    //             text += provincePair.name;
    //             break;
    //         }
    //     }
    // } else {
    //     newControl.setProvince(null);
    // }

    // if (city != null && provinceIndex != null) {
    //     const provinceInstance = addressCollection[provinceIndex];
    //     // console.log(provinceInstance)
    //     for (let i = 0, len = provinceInstance.children.length; i < len; i++) {
    //         const cityItem = provinceInstance.children[i];
    //         if (!cityItem) {
    //           break;
    //         }
    //         // console.log(cityItem)
    //         if (city.code === cityItem['code']) {
    //             cityIndex = i;

    //             const cityPair: IAddressPair = {code: cityItem['code'], name: cityItem['name']};
    //             newControl.setCity(cityPair);
    //             text += cityPair.name;

    //             break;
    //         }
    //     }
    // } else {
    //     newControl.setCity(null);
    // }

    // if (county != null && provinceIndex != null && cityIndex != null) {
    //     const cityInstance = addressCollection[provinceIndex].children[cityIndex];
    //     for (let i = 0, len = cityInstance.children.length; i < len; i++) {
    //         const countyItem = cityInstance.children[i];
    //         if (!countyItem) {
    //           break;
    //         }
    //         if (county.code === countyItem['code']) {
    //             countyIndex = i;

    //             const countyPair: IAddressPair = {code: countyItem['code'], name: countyItem['name']};
    //             newControl.setCounty(countyPair);
    //             text += countyPair.name;

    //             break;
    //         }
    //     }
    // } else {
    //     newControl.setCounty(null);
    // }

    // return super.setNewControlText(text, false);
  }

  public setAddress(option: {province: IAddressKey, city: IAddressKey, county: IAddressKey}, bRefresh?: boolean): number {
    let text = '';
    const {province, city, county} = option;
    let data1: IAddressKey;
    let data2: IAddressKey;
    if (province) {
      data1 = chinaCity.get(province.code, AddressType.Province);
      if (data1) {
        text = data1.name;
        this.setProvince(data1);
      } else {
        this.setProvince(null);
      }
    }

    if (data1 && city) {
      data2 = chinaCity.get(city.code, AddressType.City);
      if (data2) {
        text += data2.name;
        this.setCity(data2);
      } else {
        this.setCity(null);
      }
    }

    if (data2 && county) {
      const data3 = chinaCity.get(county.code, AddressType.County);
      if (data3) {
        text += data3.name;
        this.setCounty(data3);
      } else {
        this.setCounty(null);
      }
    }

    return super.setNewControlText(text, bRefresh);
  }

  /**
   * 复位选择项
   */
   public resetSelectItems(): void {

    // if ( !this.isPlaceHolderContent() ) {
    this.addPlaceHolderContent();
    this.recalculate();
    this.setTextFlag(true);
    this.setDirty();
    // }
  }

}

export function composeAddressHierarchy(address: any): any[] {
  const result = [];

  // compose level1
  for (const region of address) {
    if (region.Level === 1) {
      result.push({
        ID: region.ID,
        Level: region.Level,
        name: region.Name,
        code: region.AdDivCode,
        ParentID: region.ParentID,
        children: [],
      });
    }
  }

  // compose level2, dont attach since level3 is gigantic
  const level2Collection = [];
  for (const region of address) {
    if (region.Level === 2) {
      level2Collection.push({
        ID: region.ID,
        Level: region.Level,
        name: region.Name,
        code: region.AdDivCode,
        ParentID: region.ParentID,
        children: [],
      });
    }
  }

  // compose level3
  for (const region of address) {
    if (region.Level === 3) {
      for (const levelTwo of level2Collection) {
        if (levelTwo.ID === region.ParentID) {
          const code = region.AdDivCode;
          if (code == null) {
            // tslint:disable-next-line: no-console
            // console.log('city code null detected');
            // code = 'error' + (Math.random() * 1000000).toFixed();
            continue;
          }
          levelTwo.children.push({
            ID: region.ID,
            Level: region.Level,
            name: region.Name,
            code,
            ParentID: region.ParentID,
          });
        }
      }
    }
  }

  // attach level 2 obj
  for (const levelTwo of level2Collection) {
    for (const levelOne of result) {
      if (levelTwo.ParentID === levelOne.ID) {
        levelOne.children.push(levelTwo);
      }
    }
  }

  // console.log(result)
  return result;
}
