# 文本框级联实现流程

## 概述

本文档详细说明了如何在富文本编辑器中为文本框控件增加"包含"和"不包含"级联条件的完整实现流程。级联功能允许控件之间建立联动关系，当一个控件的值满足特定条件时，可以触发对另一个控件的操作。

## 实现步骤

### 1. 级联条件定义

首先，确认级联条件已在枚举类型中定义。在本项目中，级联条件定义在`commonDefines.ts`文件的`CascadeTriggerCondition`枚举中：

```typescript
export enum CascadeTriggerCondition {
    // 其他条件...
    Includes = 5,    // 包含
    UnIncludes = 6,  // 不包含
    // 其他条件...
}
```

### 2. 级联对话框UI支持

确保级联设置对话框中的文本框控件可以选择"包含"和"不包含"条件。这通常在级联对话框组件中实现：

```typescript
// 在NewCascade.tsx文件中
// 为文本框控件添加"包含"和"不包含"条件选项
const conditions = [
    { value: CascadeTriggerCondition.Equal, label: '等于' },
    { value: CascadeTriggerCondition.NotEqual, label: '不等于' },
    { value: CascadeTriggerCondition.Empty, label: '为空' },
    { value: CascadeTriggerCondition.NotEmpty, label: '不为空' },
    { value: CascadeTriggerCondition.Includes, label: '包含' },
    { value: CascadeTriggerCondition.UnIncludes, label: '不包含' }
];
```

### 3. 级联条件判断逻辑

在`NewControlManager.ts`文件中的`isTrigger`方法中添加对文本框"包含"和"不包含"条件的判断逻辑：

```typescript
// 在isTrigger方法中
case CascadeTriggerCondition.Includes: {
    if (oldControl.isNewTextBox()) {
        // 文本框的包含判断
        if (!value || (typeof value === 'string' && !value.includes(data.logicText))) {
            bUnValid = true;
        }
    } else {
        // 多选控件的包含判断
        if (!value || !oldControl.isContainSelected(data.logicText)) {
            bUnValid = true;
        }
    }
    break;
}
case CascadeTriggerCondition.UnIncludes: {
    if (oldControl.isNewTextBox()) {
        // 文本框的不包含判断
        if (!value || (typeof value === 'string' && value.includes(data.logicText))) {
            bUnValid = true;
        }
    } else {
        // 多选控件的不包含判断
        if (value && oldControl.isContainSelected(data.logicText)) {
            bUnValid = true;
        }
    }
    break;
}
```

### 4. 级联触发机制

确保在文本框内容变化时能正确触发级联。这涉及到以下几个关键步骤：

#### 4.1 文本框内容变化触发级联

在`NewControl.ts`文件中的`setNewControlText`方法中，当文本框内容变化时会调用`triggerCascade`方法：

```typescript
public setNewControlText(sText: string, bRefresh?: boolean): number {
    // ...省略其他代码...
    const res = this.content.setNewControlText(sText, this.bMustInput,
                    this.viewSecretType, this.isHidden2(), bRefresh);
    if (res === ResultType.Success) {
        this.setNewContentHidden();
        this.setTextFlag(true);
        this.triggerCascade(); // 触发级联
        this.setDirty();
    }
    return res;
}
```

#### 4.2 级联触发流程

级联触发的流程如下：

1. `NewControl.triggerCascade()` → 获取CascadeManager实例并调用其triggerCascade方法
2. `CascadeManager.triggerCascade(newControl)` → 将控件添加到级联映射中，调用NewControlManager.triggerCascadeMap
3. `NewControlManager.triggerCascadeMap()` → 处理级联映射，获取级联条件，调用triggerNewControls
4. `NewControlManager.triggerNewControls()` → 处理级联条件，调用isTrigger判断条件是否满足，执行级联操作

#### 4.3 确保文本框值正确传递

在`triggerNewControls`方法中，需要确保文本框的值正确设置，以便`isTrigger`方法能正确判断条件：

```typescript
// 在triggerNewControls方法中
const oldNewControl = newControlNames.get(info.oldName);
if (oldNewControl && oldNewControl.isCheckBox()) {
    info.value = oldNewControl['isSelected']() ? 'true' : 'false';
} else if (oldNewControl && oldNewControl.isNewDateBox()) {
    info.value = oldNewControl.isPlaceHolderContent() ? 'false' : 'true';
} else if (oldNewControl && oldNewControl.isNewTextBox()) {
    // 确保文本框的值正确设置
    info.value = oldNewControl.getNewControlText();
} else {
    info.value = oldNewControl.getNewControlText2();
}
```

### 5. 级联操作执行

当条件满足时，系统会执行相应的级联操作，如显示/隐藏控件、设置控件值等。这些操作在`triggerAction`方法中处理。

## 调试与测试

实现完成后，可以通过以下步骤测试文本框级联功能：

1. 创建两个文本框控件A和B
2. 为控件A设置级联条件："当B的值包含特定文本时，显示A"
3. 修改控件B的内容，观察控件A是否正确显示/隐藏

## 常见问题与解决方案

### 问题1：级联条件不触发

可能原因：
- 级联条件判断逻辑有误
- 文本框值未正确传递给级联条件判断方法
- 级联触发机制未正确实现

解决方案：
- 检查`isTrigger`方法中的条件判断逻辑
- 确保在`triggerNewControls`方法中正确设置文本框的值
- 调试级联触发流程，确保每个步骤都正确执行

### 问题2：级联条件判断不准确

可能原因：
- 字符串比较逻辑有误
- 值类型转换问题

解决方案：
- 确保使用正确的字符串比较方法
- 添加类型检查和转换，确保值的类型正确

## 总结

文本框级联功能的实现涉及多个组件和方法的协同工作。通过正确定义级联条件、实现条件判断逻辑、确保级联触发机制正常工作，以及正确传递文本框的值，可以实现文本框的"包含"和"不包含"级联条件功能。

这种级联机制使得控件之间可以建立更复杂的联动关系，提升了表单的交互性和智能性。
