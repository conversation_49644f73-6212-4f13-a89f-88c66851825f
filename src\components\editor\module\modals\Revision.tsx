import * as React from 'react';
import Dialog from '../../ui/Dialog';
import Button from '../../ui/Button';
import Input from '../../ui/Input';
// tslint:disable-next-line: max-line-length
import { IRevisionSetting, REVISION_DIALOG_COLOR2, REVISION_DIALOG_LEVEL, REVISION_DIALOG_STYLE, REVISION_INFO } from '../../../../common/commonDefines';
import Select from '../../ui/select/Select';
import { message } from '../../../../common/Message copy';

interface IDialogProps {
    documentCore: any;
    id: string;
    close: (name: string | number, bRefresh?: boolean) => void;
    visible?: boolean;
}

interface IState {
    bRefresh: boolean;
}

export default class Revision extends React.Component<IDialogProps, IState> {
    private visible: boolean;
    private revision: IRevisionSetting;
    private color: any[];
    private style: any[];
    private level: any[];
    constructor(props: any) {
        super(props);
        this.state = {
            bRefresh: false,
        };
        this.visible = this.props.visible;
        // this.style = [{key: '单线', value: 0},
        //             {key: '双线', value: 1},
        //             {key: '三线', value: 2}];
        //             // {key: '双删除线', value: 3}];
        // this.color = [{key: '红色', value: '红色'},
        //             {key: '绿色', value: '绿色'},
        //             {key: '蓝色', value: '蓝色'}];
        // this.level = [{key: 1, value: 1},
        //             {key: 2, value: 2},
        //             {key: 3, value: 3},
        //             {key: 4, value: 4}];
        // this.revision = {
        //     userName: '用户1',
        //     userId: '0001',
        //     description: '',
        //     style: this.style[0].value,
        //     color: this.color[0].value,
        //     level: this.level[0].value,
        // };
        this.style = REVISION_DIALOG_STYLE;
        this.color = REVISION_DIALOG_COLOR2;
        this.level = REVISION_DIALOG_LEVEL;
        this.revision = REVISION_INFO;
    }

    public render(): any {
        return (
            <Dialog
                id={this.props.id}
                preventDefault={true}
                open={this.open}
                visible={this.visible}
                footer={this.renderFooter()}
                width={350}
                title={'留痕设置'}
            >
                <div>
                    <div className='editor-line'>
                        <span className='w-70'>姓名</span>
                        <div className='right-auto'>
                            <Input
                                name='userName'
                                type='string'
                                // readonly={true}
                                value={this.revision.userName}
                                onChange={this.onChange}
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <span className='w-70'>工号</span>
                        <div className='right-auto'>
                            <Input
                                name='userId'
                                type='string'
                                value={this.revision.userId}
                                onChange={this.onChange}
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <span className='w-70'>备注</span>
                        <div className='right-auto'>
                            <Input
                                name='description'
                                type='string'
                                value={this.revision.description}
                                onChange={this.onChange}
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <span className='w-70'>风格</span>
                        <div className='right-auto'>
                            <Select
                                name='style'
                                data={this.style}
                                // type='string'
                                value={this.revision.style}
                                onChange={this.onChange}
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <span className='w-70'>颜色</span>
                        <div className='right-auto'>
                            <Select
                                name='color'
                                data={this.color}
                                // type='string'
                                value={this.revision.color}
                                onChange={this.onChange}
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <span className='w-70'>权限级别</span>
                        <div className='right-auto'>
                            <Select
                                name='level'
                                // type='number'
                                data={this.level}
                                value={this.revision.level}
                                onChange={this.onChange}
                            />
                        </div>
                    </div>
                </div>
            </Dialog>
        );
    }

    public componentDidMount(): void {
        this.setState({bRefresh: !this.state.bRefresh});
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        this.visible = nextProps.visible;
    }

    private renderFooter(): any {
        return (
            <span>
                <Button onClick={this.close}>取消</Button>
                <Button type='primary' onClick={this.confirm}>确认</Button>
            </span>
        );
    }

    private open = (): void => {
        const setting = this.props.documentCore.getCurRevisionSetting();
        this.revision = {
            userName: setting ? setting.userName : '用户1',
            userId: setting ? setting.userId : '0001',
            description: setting ? setting.description : '',
            style: setting ? setting.style : this.style[0].value,
            color: setting ? setting.color : this.color[0].value,
            level: setting ? setting.level : this.level[0].value,
        };
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private onChange = (value: any, name: string): void => {
        this.revision[name] = value;
    }

    private close = (bRefresh?: boolean): void => {
        this.props.close(this.props.id, bRefresh);
        this.visible = false;
        this.revision = {
            userName: '用户1',
            userId: '0001',
            description: '',
            style: this.style[0].value,
            color: this.color[0].value,
            level: 1,
        };
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private confirm = (): void => {
        const revision = this.revision;
        const revInfo = {
            userName: revision.userName,
            userId: revision.userId,
            description: revision.description,
            style: revision.style,
            color: revision.color,
            level: revision.level,
        };

        if (!revInfo.userName || !revInfo.userId) {
            message.error('修订人姓名和工号不能为空');
            return;
        }

        this.props.documentCore.setRevision(revInfo);

        this.close(true);
    }
}

export class RevisionAcceptReject extends React.Component<IDialogProps, IState> {
    private visible: boolean;
    constructor(props: any) {
        super(props);
        this.state = {
            bRefresh: false,
        };
        this.visible = this.props.visible;
    }

    public render(): any {
        return (
            <Dialog
                id={this.props.id}
                preventDefault={true}
                visible={this.visible}
                // footer={this.renderFooter()}
                width={350}
                bCloseIcon={true}
                close={this.close}
                title={'接受修订'}
            >
                <div>
                    <div className='editor-line' style={{textAlign: 'center'}}>
                        {/* <span className='w-70'>全部接受</span> */}
                        {/* <span style={{paddingLeft: '20px'}}> */}
                            <Button onClick={this.confirm}>
                                全部接受
                            </Button>
                        {/* </span> */}
                        {/* <span className='w-70'>全部拒绝</span> */}
                        {/* <span style={{paddingLeft: '20px'}}>
                            <Button onClick={this.cancel}>
                            全部拒绝
                            </Button>
                        </span> */}
                    </div>
                </div>
            </Dialog>
        );
    }

    public componentDidMount(): void {
        this.setState({bRefresh: !this.state.bRefresh});
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        this.visible = nextProps.visible;
    }

    private onClick = (e: any) => {
        this.close(true);
    }

    private close = (bRefresh?: boolean): void => {
        this.props.close(this.props.id, bRefresh);
        this.visible = false;
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private cancel = (): void => {
        this.props.documentCore.rejectRevisionChanges();

        this.close(true);
    }

    private confirm = (): void => {
        this.props.documentCore.acceptRevisionChanges();

        this.close(true);
    }
}
