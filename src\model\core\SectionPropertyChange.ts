import { ChangeBaseBoolProperty, ChangeBaseLongProperty, ChangeBaseProperty } from './HistoryChange';
import { HistroyItemType } from './HistoryDescription';
import { SectionProperty } from './SectionProperty';

export class ChangeSectionPageSize extends ChangeBaseProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: SectionProperty, old: any, news: any, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.SectionPageSize;
    }

    public setValue(value: any): void {
        const sectionProperty = this.getClass();
        if ( sectionProperty ) {
            sectionProperty.pageSize.height = value.height;
            sectionProperty.pageSize.width = value.width;
        }
    }
}

export class ChangeSectionPageMargins extends ChangeBaseProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: SectionProperty, old: any, news: any, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.SectionPageMargins;
    }

    public setValue(value: any): void {
        const sectionProperty = this.getClass();
        if ( sectionProperty ) {
            sectionProperty.pageMargins.paddingLeft   = value.left;
            sectionProperty.pageMargins.paddingTop    = value.top;
            sectionProperty.pageMargins.paddingRight  = value.right;
            sectionProperty.pageMargins.paddingBottom = value.bottom;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeSectionPageMarginsHeader extends ChangeBaseLongProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: SectionProperty, old: number, news: number, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.SectionPageMarginsHeader;
    }

    public setValue(value: number): void {
        const sectionProperty = this.getClass();
        if ( sectionProperty ) {
            sectionProperty.pageMargins.header = value;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeSectionPageMarginsFooter extends ChangeBaseBoolProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: SectionProperty, old: number, news: number, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.SectionPageMarginsFooter;
    }

    public setValue(value: number): void {
        const sectionProperty = this.getClass();
        if ( sectionProperty ) {
            sectionProperty.pageMargins.footer = value;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeSectionTitlePage extends ChangeBaseBoolProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: SectionProperty, old: boolean, news: boolean, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.SectionTitlePage;
    }

    public setValue(value: boolean): void {
        const sectionProperty = this.getClass();
        if ( sectionProperty ) {
            sectionProperty.titlePage = value;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeSectionFirstPageDiff extends ChangeBaseBoolProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: SectionProperty, old: boolean, news: boolean, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.SectionFirstPageDiff;
    }

    public setValue(value: boolean): void {
        const sectionProperty = this.getClass();
        if ( sectionProperty ) {
            sectionProperty.firstPageDiff = value;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeSectionHeaderDefault extends ChangeBaseProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: SectionProperty, old: any, news: any, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.SectionHeaderDefault;
    }

    public setValue(value: any): void {
        const sectionProperty = this.getClass();
        if ( sectionProperty ) {
            sectionProperty.headerDefault = value;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeSectionFooterDefault extends ChangeBaseProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: SectionProperty, old: any, news: any, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.SectionFooterDefault;
    }

    public setValue(value: any): void {
        const sectionProperty = this.getClass();
        if ( sectionProperty ) {
            sectionProperty.footerDefault = value;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeSectionHeaderFirst extends ChangeBaseProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: SectionProperty, old: any, news: any, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.SectionHeaderFirst;
    }

    public setValue(value: any): void {
        const sectionProperty = this.getClass();
        if ( sectionProperty ) {
            sectionProperty.headerFirst = value;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeSectionFooterFirst extends ChangeBaseProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: SectionProperty, old: any, news: any, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.SectionFooterFirst;
    }

    public setValue(value: any): void {
        const sectionProperty = this.getClass();
        if ( sectionProperty ) {
            sectionProperty.footerFirst = value;
        }
    }
}
