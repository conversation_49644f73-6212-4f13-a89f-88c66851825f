import * as React from 'react';
import { DocumentCore } from '../../../../../model/DocumentCore';
import ParaDrawing, {
    ParaEquation,
} from '../../../../../model/core/Paragraph/ParaDrawing';
import { ISvgWidthIncreasedProps } from '../../../../../model/MedEquationProperty';
import { EquationType } from '../../../../../common/commonDefines';
import { EquationRefs } from '../../../../../common/MedEquation';
import Dialog from '../../../ui/Dialog';
import Button from '../../../ui/Button';

// 月经公式相关常量定义
// 基础宽度（像素）
const MEN_EQUATION_BASE_WIDTH = 106;
// 默认字体大小（像素）
const MEN_EQUATION_DEFAULT_FONT_SIZE = 80;
// 使用日期时的字体大小（像素）
const MEN_EQUATION_DATE_FONT_SIZE = 70;
// 使用日期时的宽度倍数
const MEN_EQUATION_DATE_WIDTH_MULTIPLIER = 1.5;

interface IOrdinaryEquationVals {
    firstVal: string;
    secondVal: string;
    thirdVal: string;
    fourthVal: string;
}

interface IFractionEquationVals {
    firstVal: string;
    secondVal: string;
}

interface IMenEquationVals {
    initialAge: string;
    duration: string;
    period: string;
    lastAge: string;
    unitEnabled: boolean;
    initialDateEnabled: boolean;
    lastDateEnabled: boolean;
}

interface IMedEquationEditModalProps {
    documentCore: DocumentCore;
    // handleModalState?: (type: string) => void;
    // refresh: () => void;
    equation: any;
    visible: boolean;
    id: string;
    close: (id: string, bRefresh?: boolean, timeout?: number) => void;
}

interface IMedEquationEditModalState {
    ordinaryEquationVals: IOrdinaryEquationVals;
    fractionEquationVals: IFractionEquationVals;
    menEquationVals: IMenEquationVals;
    isEquationEditEnabled: boolean; // not in use yet
    svgWidthIncreasedProps?: ISvgWidthIncreasedProps;
    bRefresh: boolean;
}

export default class EditEquation extends React.Component<
    IMedEquationEditModalProps,
    IMedEquationEditModalState
> {
    private revisedEquationContainerRef: any; // container to render svg for measure

    // equation form ref
    private ordinaryEquationForm: any;
    private fractionEquationForm: any;
    private menEquationForm: any;
    private equationRefs: EquationRefs;
    private visible: boolean;
    private equationType: EquationType;

    constructor(props: IMedEquationEditModalProps) {
        super(props);

        this.revisedEquationContainerRef = React.createRef();
        this.equationRefs = new EquationRefs();
        this.ordinaryEquationForm = React.createRef();
        this.fractionEquationForm = React.createRef();
        this.menEquationForm = React.createRef();
        this.visible = this.props.visible;

        this.state = {
            ordinaryEquationVals: {
                firstVal: '',
                secondVal: '',
                thirdVal: '',
                fourthVal: '',
            },
            fractionEquationVals: {
                firstVal: '',
                secondVal: '',
            },
            menEquationVals: {
                initialAge: '',
                duration: '',
                period: '',
                lastAge: '',
                unitEnabled: true,
                initialDateEnabled: false,
                lastDateEnabled: false,
            },
            isEquationEditEnabled: false,
            svgWidthIncreasedProps: {
                ordinaryEquation: {
                    leftAmount: 0,
                    rightAmount: 0,
                },
                fractionEquation: {
                    amount: 0,
                },
                menEquation: {
                    leftAmount: 0,
                    middleAmount: 0,
                    rightAmount: 0,
                },
                // unit: 80,
                fractionTextPadding: 110,
                ordinaryTextPadding: 35,
                menTextPadding: 20,
                menSVGPadding: 32,
            },
            bRefresh: false,
        };
        this.equationType = EquationType.Ordinary;
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        if (nextProps.visible === true) {
            this.visible = nextProps.visible;
            this.init(nextProps);
        }
    }

    public componentDidMount(): void {
        this.init(this.props);
    }

    public render(): any {
        if (this.equationType === undefined) {
            return null;
        }

        const display1 = {
            display:
                this.equationType === EquationType.Ordinary
                    ? 'block'
                    : 'none',
        };

        const display2 = {
            display:
                this.equationType === EquationType.Fraction
                    ? 'block'
                    : 'none',
        };
        const display3 = {
            display:
                this.equationType ===
                EquationType.Menstruation
                    ? 'block'
                    : 'none',
        };

        const render1 = !this.state.menEquationVals.initialDateEnabled ? (
            <input
                id='menstruation-initialAge'
                name='menstruation-initialAge'
                className='menstruation-text'
                value={this.state.menEquationVals.initialAge}
                onChange={this.handleEquationEditModalChange}
                maxLength={20}
            />
        ) : (
            <input
                type='date'
                id='menstruation-initialAge'
                name='menstruation-initialAge'
                className='menstruation-date'
                value={this.state.menEquationVals.initialAge}
                onChange={this.handleEquationEditModalChange}
            />
        );

        const render2 = !this.state.menEquationVals.lastDateEnabled ? (
            <input
                id='menstruation-lastAge'
                name='menstruation-lastAge'
                className='menstruation-text'
                value={this.state.menEquationVals.lastAge}
                onChange={this.handleEquationEditModalChange}
                maxLength={20}
            />
        ) : (
            <input
                type='date'
                id='menstruation-lastAge'
                name='menstruation-lastAge'
                className='menstruation-date'
                value={this.state.menEquationVals.lastAge}
                onChange={this.handleEquationEditModalChange}
            />
        );

        return (
            <Dialog
                width={500}
                visible={this.visible}
                footer={this.renderFooter()}
                title='医学表达式'
                id={this.props.id}
            >
                <div className='editor-edit-equation'>
                    <form
                        ref={this.ordinaryEquationForm}
                        onSubmit={this.preventSubmit}
                        style={display1}
                    >
                        <div className='config-descriptor'>Z/P牙位记录法</div>
                        <div className='detail-block equation-detail-block'>
                            <div className='detail-item left-item top-item'>
                                <div>左上</div>
                                <input
                                    id='ordinary-firstVal'
                                    name='ordinary-firstVal'
                                    value={this.state.ordinaryEquationVals.firstVal}
                                    onChange={this.handleEquationEditModalChange}
                                    maxLength={20}
                                />
                            </div>
                            <div className='detail-item right-item top-item'>
                                <div>右上</div>
                                <input
                                    id='ordinary-secondVal'
                                    name='ordinary-secondVal'
                                    className=''
                                    value={this.state.ordinaryEquationVals.secondVal}
                                    onChange={this.handleEquationEditModalChange}
                                    maxLength={20}
                                />
                            </div>
                        </div>
                        <div className='detail-block equation-detail-block'>
                            <div className='detail-item left-item bottom-item'>
                                <div>左下</div>
                                <input
                                    id='ordinary-thirdVal'
                                    name='ordinary-thirdVal'
                                    className=''
                                    value={this.state.ordinaryEquationVals.thirdVal}
                                    onChange={this.handleEquationEditModalChange}
                                    maxLength={20}
                                />
                            </div>
                            <div className='detail-item right-item bottom-item'>
                                <div>右下</div>
                                <input
                                    id='ordinary-fourthVal'
                                    name='ordinary-fourthVal'
                                    className=''
                                    value={this.state.ordinaryEquationVals.fourthVal}
                                    onChange={this.handleEquationEditModalChange}
                                    maxLength={20}
                                />
                            </div>
                        </div>
                    </form>

                    <form
                        ref={this.fractionEquationForm}
                        onSubmit={this.preventSubmit}
                        style={display2}
                    >
                        <div className='config-descriptor'>分式编辑</div>
                        <div className='detail-block fraction-block'>
                            <div>No1 值</div>
                            <input
                                id='fraction-firstVal'
                                name='fraction-firstVal'
                                className=''
                                value={this.state.fractionEquationVals.firstVal}
                                onChange={this.handleEquationEditModalChange}
                                maxLength={20}
                                required={true}
                            />
                        </div>
                        <div className='detail-block fraction-block'>
                            <div>No2 值</div>
                            <input
                                id='fraction-secondVal'
                                name='fraction-secondVal'
                                className=''
                                value={this.state.fractionEquationVals.secondVal}
                                onChange={this.handleEquationEditModalChange}
                                maxLength={20}
                                required={true}
                            />
                        </div>
                    </form>

                    <form
                        ref={this.menEquationForm}
                        onSubmit={this.preventSubmit}
                        style={display3}
                    >
                        <div className='config-descriptor'>月经史表达式编辑</div>
                        <div className='menstruation-block'>
                            <input
                                id='menstruation-unit'
                                type='checkbox'
                                name='menstruation-unit'
                                className='menstruation-checkbox'
                                checked={this.state.menEquationVals.unitEnabled}
                                onChange={this.handleEquationEditModalChange}
                            />
                            单位
                            <div>初潮年龄</div>
                            {render1}
                            <input
                                id='menstruation-date-initial'
                                type='checkbox'
                                name='menstruation-date-initial'
                                className='menstruation-checkbox'
                                checked={this.state.menEquationVals.initialDateEnabled}
                                onChange={this.handleEquationEditModalChange}
                            />
                            使用日期
                        </div>
                        <div className='menstruation-block'>
                            <div className='menstruation-upper-block'>
                                <div>经期(天)</div>
                                <input
                                    id='menstruation-duration'
                                    name='menstruation-duration'
                                    className='menstruation-text'
                                    value={this.state.menEquationVals.duration}
                                    onChange={this.handleEquationEditModalChange}
                                    maxLength={20}
                                />
                            </div>
                            <div className='menstruation-bottom-block'>
                                <div>周期(天)</div>
                                <input
                                    id='menstruation-period'
                                    name='menstruation-period'
                                    className='menstruation-text'
                                    value={this.state.menEquationVals.period}
                                    onChange={this.handleEquationEditModalChange}
                                    maxLength={20}
                                />
                            </div>
                        </div>
                        <div className='menstruation-block'>
                            <div>
                                末次月经/
                                <br />
                                绝经年龄
                            </div>
                            {render2}
                            <input
                                id='menstruation-date-last'
                                type='checkbox'
                                name='menstruation-date-last'
                                className='menstruation-checkbox'
                                checked={this.state.menEquationVals.lastDateEnabled}
                                onChange={this.handleEquationEditModalChange}
                            />
                            使用日期
                        </div>
                    </form>

                    {/* container for revised equation to measure height/width */}
                    <div
                        ref={this.revisedEquationContainerRef}
                        className='revised-equation-container'
                    />
                </div>
            </Dialog>
        );
    }

    private init(props: any): void {
        const documentCore = props.documentCore;
        const paraEquation = props.equation || documentCore.getSelectedImage() as ParaEquation;
        this.equationType = paraEquation.equationType;
        let equationElemDom = null;
        if (paraEquation.equationElem) {
            equationElemDom = new DOMParser().parseFromString(
                paraEquation.equationElem,
                'text/xml',
            ).documentElement;
        }
        // console.log(equationElemDom)

        if (!equationElemDom) {
            // const equationRefs  = this.equationRefs;
            equationElemDom = this.equationRefs.getEquationByType(paraEquation.equationType)
            .cloneNode(true);
            // switch (paraEquation.equationType) {
            //     case EquationType.Fraction:
            //         equationElemDom = equationRefs.fractionEquationRef.current.cloneNode(
            //             true,
            //         );
            //         break;

            //     case EquationType.Ordinary:
            //         equationElemDom = equationRefs.ordEquationRef.current.cloneNode(
            //             true,
            //         );
            //         break;

            //     case EquationType.Menstruation:
            //         equationElemDom = equationRefs.menEquationRef.current.cloneNode(
            //             true,
            //         );
            //         equationElemDom.setAttribute('width', 106);
            //         equationElemDom.setAttribute('height', 50);
            //         break;

            //     default:
            //         equationElemDom = equationRefs.fractionEquationRef.current.cloneNode(
            //             true,
            //         );
            //         break;
            // }

            // cast change to string and store in paraEquation
            const svgEquationElemStr = new XMLSerializer().serializeToString(
                equationElemDom,
            );
            //documentCore
            //    .getDrawingObjects()
            //    .applyDrawingSvgElemStr(svgEquationElemStr);
            documentCore.applyDrawingSvgElemStr(svgEquationElemStr);
        }
        // console.log(paraEquation.equationElem)

        const texts = equationElemDom.querySelectorAll('.equation-text');

        let isEquationEditEnabled = false;

        switch (paraEquation.equationType) {
            case EquationType.Fraction:
                const fractionEquationVals = JSON.parse(
                    JSON.stringify(this.state.fractionEquationVals),
                ) as IFractionEquationVals;
                fractionEquationVals.firstVal = texts[0].innerHTML;
                fractionEquationVals.secondVal = texts[1].innerHTML;
                isEquationEditEnabled = this.checkEquationEditEnabled(
                    fractionEquationVals,
                );
                this.setState({
                    fractionEquationVals,
                    isEquationEditEnabled,
                });
                break;

            case EquationType.Ordinary:
                const ordinaryEquationVals = JSON.parse(
                    JSON.stringify(this.state.ordinaryEquationVals),
                ) as IOrdinaryEquationVals;

                ordinaryEquationVals.firstVal = texts[0].innerHTML;
                ordinaryEquationVals.secondVal = texts[1].innerHTML;
                ordinaryEquationVals.thirdVal = texts[2].innerHTML;
                ordinaryEquationVals.fourthVal = texts[3].innerHTML;

                isEquationEditEnabled = this.checkEquationEditEnabled(
                    ordinaryEquationVals,
                );
                this.setState({
                    ordinaryEquationVals,
                    isEquationEditEnabled,
                });
                break;
            case EquationType.Menstruation:
                const menEquationVals = JSON.parse(
                    JSON.stringify(this.state.menEquationVals),
                ) as IMenEquationVals;
                const hiddenValueElem = equationElemDom.querySelector(
                    '.hidden-values',
                );

                menEquationVals.initialAge = texts[0].innerHTML;
                menEquationVals.duration = texts[1].innerHTML;
                menEquationVals.period = texts[2].innerHTML;
                menEquationVals.lastAge = texts[3].innerHTML;
                menEquationVals.unitEnabled = hiddenValueElem.classList.contains(
                    'unitEnabled',
                )
                    ? true
                    : false;
                menEquationVals.initialDateEnabled = hiddenValueElem.classList.contains(
                    'initialDateEnabled',
                )
                    ? true
                    : false;
                menEquationVals.lastDateEnabled = hiddenValueElem.classList.contains(
                    'lastDateEnabled',
                )
                    ? true
                    : false;

                // remove '岁'/'年' if unit checked
                if (menEquationVals.unitEnabled) {
                    menEquationVals.initialAge = menEquationVals.initialDateEnabled
                        ? menEquationVals.initialAge
                        : menEquationVals.initialAge.slice(
                                0,
                                menEquationVals.initialAge.length - 1,
                            );
                    menEquationVals.duration = menEquationVals.duration.slice(
                        0,
                        menEquationVals.duration.length - 1,
                    );
                    menEquationVals.period = menEquationVals.period.slice(
                        0,
                        menEquationVals.period.length - 1,
                    );
                    menEquationVals.lastAge = menEquationVals.lastDateEnabled
                        ? menEquationVals.lastAge
                        : menEquationVals.lastAge.slice(
                                0,
                                menEquationVals.lastAge.length - 1,
                            );
                }

                isEquationEditEnabled = this.checkEquationEditEnabled(
                    menEquationVals,
                );
                // console.log(menEquationVals, isEquationEditEnabled)
                this.setState({ menEquationVals, isEquationEditEnabled });
                break;

            default:
                break;
        }

        this.setState({bRefresh: !this.state.bRefresh});
    }

    private renderFooter(): any {
        return (
            <span>
                <Button onClick={this.close}>取消</Button>
                <Button
                    type='primary'
                    onClick={this.confirm}
                    disabled={this.state.isEquationEditEnabled ? false : true}
                >
                    确认
                </Button>
            </span>
        );
    }

    private close = (bRefresh?: boolean): void => {
        this.props.close(this.props.id, bRefresh, 10);
        this.visible = false;
        // this.equation.name = '公式' + Math.ceil(Math.random() * 1000);
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private handleEquationEditModalChange = (e: any) => {
        const { documentCore } = this.props;

        let isEquationEditEnabled = false;

        if (this.equationType === EquationType.Fraction) {
            const fractionEquationVals: IOrdinaryEquationVals = JSON.parse(
                JSON.stringify(this.state.fractionEquationVals),
            );
            switch (e.target.id) {
                case 'fraction-firstVal':
                    fractionEquationVals.firstVal = e.target.value;
                    break;

                case 'fraction-secondVal':
                    fractionEquationVals.secondVal = e.target.value;
                    break;

                default:
                    break;
            }

            isEquationEditEnabled = this.checkEquationEditEnabled(
                fractionEquationVals,
            );
            this.setState({ fractionEquationVals, isEquationEditEnabled });
        } else if (this.equationType === EquationType.Ordinary) {
            const ordinaryEquationVals: IOrdinaryEquationVals = JSON.parse(
                JSON.stringify(this.state.ordinaryEquationVals),
            );

            switch (e.target.id) {
                case 'ordinary-firstVal':
                    ordinaryEquationVals.firstVal = e.target.value;
                    break;

                case 'ordinary-secondVal':
                    ordinaryEquationVals.secondVal = e.target.value;
                    break;

                case 'ordinary-thirdVal':
                    ordinaryEquationVals.thirdVal = e.target.value;
                    break;

                case 'ordinary-fourthVal':
                    ordinaryEquationVals.fourthVal = e.target.value;
                    break;

                default:
                    break;
            }

            isEquationEditEnabled = this.checkEquationEditEnabled(
                ordinaryEquationVals,
            );
            this.setState({ ordinaryEquationVals, isEquationEditEnabled });
        } else if (this.equationType === EquationType.Menstruation) {
            const menEquationVals: IMenEquationVals = JSON.parse(
                JSON.stringify(this.state.menEquationVals),
            );

            const paraEquation = this.props.documentCore.getSelectedImage() as ParaEquation;
            const equationElemDom: any = new DOMParser().parseFromString(
                paraEquation.equationElem,
                'text/xml',
            ).documentElement;
            const hiddenValueElem = equationElemDom.querySelector(
                '.hidden-values',
            );

            switch (e.target.id) {
                case 'menstruation-initialAge':
                    menEquationVals.initialAge = e.target.value;
                    break;

                case 'menstruation-duration':
                    menEquationVals.duration = e.target.value;
                    break;

                case 'menstruation-period':
                    menEquationVals.period = e.target.value;
                    break;

                case 'menstruation-lastAge':
                    menEquationVals.lastAge = e.target.value;
                    break;

                case 'menstruation-unit':
                    menEquationVals.unitEnabled = !menEquationVals.unitEnabled;
                    if (menEquationVals.unitEnabled) {
                        hiddenValueElem.classList.add('unitEnabled');
                    } else {
                        hiddenValueElem.classList.remove('unitEnabled');
                    }

                    // console.log(hiddenValueElem);
                    break;

                case 'menstruation-date-initial':
                    menEquationVals.initialDateEnabled = !menEquationVals.initialDateEnabled;

                    // remove string when changing from text to date and vice versa
                    if (menEquationVals.initialDateEnabled) {
                        menEquationVals.initialAge = '';
                        hiddenValueElem.classList.add('initialDateEnabled');
                    } else {
                        menEquationVals.initialAge = '';
                        hiddenValueElem.classList.remove('initialDateEnabled');
                    }
                    break;

                case 'menstruation-date-last':
                    menEquationVals.lastDateEnabled = !menEquationVals.lastDateEnabled;

                    // remove string when changing from text to date and vice versa
                    if (menEquationVals.lastDateEnabled) {
                        menEquationVals.lastAge = '';
                        hiddenValueElem.classList.add('lastDateEnabled');
                    } else {
                        menEquationVals.lastAge = '';
                        hiddenValueElem.classList.remove('lastDateEnabled');
                    }
                    break;

                default:
                    break;
            }
            // cast change to string and store in paraEquation
            const svgEquationElemStr = new XMLSerializer().serializeToString(
                equationElemDom,
            );
            //documentCore
            //    .getDrawingObjects()
            //    .applyDrawingSvgElemStr(svgEquationElemStr);
            documentCore.applyDrawingSvgElemStr(svgEquationElemStr);

            isEquationEditEnabled = this.checkEquationEditEnabled(
                menEquationVals,
            );
            this.setState({ menEquationVals, isEquationEditEnabled });
        }
    }

    private confirm = (): void => {
        const { documentCore } = this.props;
        
        // 应用修改后的公式值
        let equationElemDom = null;
        
        if (this.props.equation) {
            // 解析原始SVG以获取原始尺寸
            const originalElemDom = new DOMParser().parseFromString(
                this.props.equation.equationElem,
                'text/xml'
            ).documentElement;
            
            // 保存原始尺寸信息
            const originalWidth = originalElemDom.getAttribute('width');
            const originalHeight = originalElemDom.getAttribute('height');
            
            // 复制DOM用于修改内容
            equationElemDom = originalElemDom.cloneNode(true);
            
            const texts = equationElemDom.querySelectorAll('.equation-text');
            
            switch (this.equationType) {
                case EquationType.Fraction:
                    texts[0].innerHTML = this.state.fractionEquationVals.firstVal;
                    texts[1].innerHTML = this.state.fractionEquationVals.secondVal;
                    break;
                
                case EquationType.Ordinary:
                    texts[0].innerHTML = this.state.ordinaryEquationVals.firstVal;
                    texts[1].innerHTML = this.state.ordinaryEquationVals.secondVal;
                    texts[2].innerHTML = this.state.ordinaryEquationVals.thirdVal;
                    texts[3].innerHTML = this.state.ordinaryEquationVals.fourthVal;
                    break;
                
                case EquationType.Menstruation:
                    // 处理月经史表达式
                    const hiddenValueElem = equationElemDom.querySelector('.hidden-values');
                    
                    // 添加或删除单位标记
                    if (this.state.menEquationVals.unitEnabled) {
                        hiddenValueElem.classList.add('unitEnabled');
                    } else {
                        hiddenValueElem.classList.remove('unitEnabled');
                    }
                    
                    // 处理日期启用状态
                    const initialDateEnabled = this.state.menEquationVals.initialDateEnabled;
                    const lastDateEnabled = this.state.menEquationVals.lastDateEnabled;
                    const usesDate = initialDateEnabled || lastDateEnabled;
                    
                   
                    if (initialDateEnabled) {
                        hiddenValueElem.classList.add('initialDateEnabled');
                    } else {
                        hiddenValueElem.classList.remove('initialDateEnabled');
                    }
                    
                    if (lastDateEnabled) {
                        hiddenValueElem.classList.add('lastDateEnabled');
                    } else {
                        hiddenValueElem.classList.remove('lastDateEnabled');
                    }
                    
                    // 设置文本值，根据是否启用单位添加后缀
                    let initialAge = this.state.menEquationVals.initialAge;
                    let duration = this.state.menEquationVals.duration;
                    let period = this.state.menEquationVals.period;
                    let lastAge = this.state.menEquationVals.lastAge;
                    
                    // 如果启用单位并且不是日期，则添加单位后缀
                    if (this.state.menEquationVals.unitEnabled) {
                        if (!this.state.menEquationVals.initialDateEnabled && initialAge) {
                            initialAge += '岁';
                        }
                        if (duration) {
                            duration += '天';
                        }
                        if (period) {
                            period += '天';
                        }
                        if (!this.state.menEquationVals.lastDateEnabled && lastAge) {
                            lastAge += '岁';
                        }
                    }
                    
                    texts[0].innerHTML = initialAge;
                    texts[1].innerHTML = duration;
                    texts[2].innerHTML = period;
                    texts[3].innerHTML = lastAge;
                    break;
            }
            
            // 如果是月经公式，根据日期选项状态调整宽度和字体大小
            if (this.equationType === EquationType.Menstruation) {
                const initialDateEnabled = this.state.menEquationVals.initialDateEnabled;
                const lastDateEnabled = this.state.menEquationVals.lastDateEnabled;
                const usesDate = initialDateEnabled || lastDateEnabled;
                
                if (usesDate) {
                    // 计算新的宽度和字体大小
                    const newWidth = Math.round(MEN_EQUATION_BASE_WIDTH * MEN_EQUATION_DATE_WIDTH_MULTIPLIER);
                    
                    // 设置新的宽度
                    equationElemDom.setAttribute('width', newWidth.toString());
                    equationElemDom.setAttribute('data-uses-date', 'true');
                    
                    // 调整所有文本元素的字体大小
                    const textElements = equationElemDom.querySelectorAll('text');
                    textElements.forEach(textElement => {
                        textElement.setAttribute('style', `font-size: ${MEN_EQUATION_DATE_FONT_SIZE}px;`);
                    });
                } else {
                    // 如果不使用日期，使用原始尺寸
                    if (originalWidth) {
                        equationElemDom.setAttribute('width', originalWidth);
                    }
                    if (originalHeight) {
                        equationElemDom.setAttribute('height', originalHeight);
                    }
                    
                    // 设置默认字体大小
                    const textElements = equationElemDom.querySelectorAll('text');
                    textElements.forEach(textElement => {
                        textElement.setAttribute('style', `font-size: ${MEN_EQUATION_DEFAULT_FONT_SIZE}px;`);
                    });
                }
            } else {
                // 其他类型的公式保留原始尺寸
                if (originalWidth) {
                    equationElemDom.setAttribute('width', originalWidth);
                }
                if (originalHeight) {
                    equationElemDom.setAttribute('height', originalHeight);
                }
            }
            
            // 将修改后的DOM转换为字符串并应用到文档
            const svgEquationElemStr = new XMLSerializer().serializeToString(equationElemDom);
            documentCore.applyDrawingSvgElemStr(svgEquationElemStr);
            
            // 应用SVG元素宽高到文档
            try {
                // 如果有SVG转换为图片的方法，则应用
                if (documentCore.getDrawingObjects && 
                    typeof documentCore.getDrawingObjects().convertSVGToImageString === 'function') {
                    const svgConvertedURI = documentCore.getDrawingObjects().convertSVGToImageString(equationElemDom);
                    
                    // 获取日期选项状态
                    let width;
                    const height = originalHeight ? parseInt(originalHeight, 10) : 26;
                    
                    // 如枟是月经公式并且使用日期选项，使用调整后的宽度
                    if (this.equationType === EquationType.Menstruation) {
                        const initialDateEnabled = this.state.menEquationVals.initialDateEnabled;
                        const lastDateEnabled = this.state.menEquationVals.lastDateEnabled;
                        const usesDate = initialDateEnabled || lastDateEnabled;
                        
                        if (usesDate) {
                            // 使用定义的常量计算宽度
                            width = Math.round(MEN_EQUATION_BASE_WIDTH * MEN_EQUATION_DATE_WIDTH_MULTIPLIER);
                        } else {
                            width = originalWidth ? parseInt(originalWidth, 10) : 100;
                        }
                    } else {
                        width = originalWidth ? parseInt(originalWidth, 10) : 100;
                    }
                    
                    documentCore.applyDrawingHref(svgConvertedURI, width, height);
                }
                
                // 尝试重新计算文档
                if (documentCore.getDocument && 
                    typeof documentCore.getDocument().recalculate === 'function') {
                    documentCore.getDocument().recalculate();
                }
                
                // 尝试强制刷新文档
                if (typeof documentCore.recalculateAllForce === 'function') {
                    documentCore.recalculateAllForce();
                }
            } catch (error) {
                console.error("应用公式修改时出错:", error);
            }
        }
        
        // 关闭对话框并请求刷新
        this.close(true);
    }

    private checkEquationEditEnabled(EquationVals: | IOrdinaryEquationVals
            | IFractionEquationVals
            | IMenEquationVals ,
    ): boolean {
        const isEquationEditEnabled = true;

        switch (this.equationType) {
            case EquationType.Fraction:
                break;
            case EquationType.Ordinary:
                break;
            case EquationType.Menstruation:
                break;

            default:
                break;
        }

        return isEquationEditEnabled;
    }

    private preventSubmit = (e: any): void => {
        e.preventDefault();
    }
}

/**
 * Calculate and set width of diff text parts in a realtime equation
 */
export function setEquationTextPos(svgEquationElem: any, revisedEquationContainerRef: any,
                                   stateSvgWidthIncreasedProps: ISvgWidthIncreasedProps,
                                   equationType: EquationType, documentCore: DocumentCore): ISvgWidthIncreasedProps {

    const revisedEquationContainer = revisedEquationContainerRef.current;
    const svgWidthIncreasedProps: ISvgWidthIncreasedProps = JSON.parse(JSON.stringify(stateSvgWidthIncreasedProps));

    // append svg for measure preparation
    revisedEquationContainer.appendChild(svgEquationElem);
    const svg = revisedEquationContainer.querySelector('.medical-equation-wrapper');
    const texts = svg.querySelectorAll('.equation-text');
    const graphicObjects = documentCore.getDrawingObjects();

    if (equationType === EquationType.Ordinary) {

        const ordinaryEquation = svgWidthIncreasedProps.ordinaryEquation;
        // reset
        ordinaryEquation.leftAmount = 0;
        ordinaryEquation.rightAmount = 0;

        for (let i = 0; i < texts.length; i++) {
            if (i % 2 === 0) {
                // even
                // tslint:disable-next-line:max-line-length
                ordinaryEquation.leftAmount = (measureSVG(texts[i]).width > ordinaryEquation.leftAmount) ?
                measureSVG(texts[i]).width : ordinaryEquation.leftAmount;
            } else {
                // odd
                // tslint:disable-next-line:max-line-length
                ordinaryEquation.rightAmount = (measureSVG(texts[i]).width > ordinaryEquation.rightAmount) ?
                measureSVG(texts[i]).width : ordinaryEquation.rightAmount;
            }
        }

    } else if (equationType === EquationType.Fraction) {

        const fractionEquation = svgWidthIncreasedProps.fractionEquation;
        // reset
        fractionEquation.amount = 0;

        for (const text of texts) {
            fractionEquation.amount = (measureSVG(text).width > fractionEquation.amount) ?
            measureSVG(text).width : fractionEquation.amount;
        }

    } else if (equationType === EquationType.Menstruation) {

        const menEquation = svgWidthIncreasedProps.menEquation;

        // reset
        menEquation.middleAmount = 0;

        for (let i = 0; i < texts.length; i++) {
            if (i === 0) {
                menEquation.leftAmount = measureSVG(texts[i]).width;
            } else if (i === 1 || i === 2) {
                menEquation.middleAmount = (measureSVG(texts[i]).width > menEquation.middleAmount) ?
                measureSVG(texts[i]).width : menEquation.middleAmount;
            } else if (i === 3) {
                menEquation.rightAmount = measureSVG(texts[i]).width;
            }
        }
    }

    // remove appended svg child
    revisedEquationContainer.removeChild(svg);
    // console.log(revisedEquationContainer);

    const logicDocument = graphicObjects.getDocument();
    if (logicDocument != null) {
        logicDocument.setDirty();
    }
    return svgWidthIncreasedProps;
}

/**
 * Measure svg after rendering
 */
export function measureSVG(elem: any): any {
    const bbox = elem.getBBox();
    return {width: bbox.width, height: bbox.height};
}
