// AICheckDlg.less
// Dialog固定定位样式
.fixed-dialog {
    position: fixed !important;
    margin: 0 !important;
    transform: none !important;
}

.ai-check-dialog {
    :global(.dialog-content) {
        max-width: 100% !important; 
        height: calc(100vh / 3) !important;
        padding: 0 !important;
    }

    &.collapsed {
        width: 24px !important;
        
        .ai-dialog-container {
            width: 24px;
        }
    }

    .ai-dialog-container {
        display: flex;
        flex-direction: column;
        height: 100%;
        background: #fff;
        border-radius: 4px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        transition: width 0.3s;
        width: 300px;

        &.collapsed {
            .ai-dialog-nav,
            .ai-dialog-content,
            .ai-dialog-close {
                display: none;
            }

            .ai-dialog-collapse {
                svg {
                    transform: rotate(180deg);
                }
            }
        }
    }

    .ai-dialog-nav {
        padding: 8px 4px;
        background: #fff;
        border-bottom: 1px solid #e8e8e8;

        .nav-row {
            display: flex;
            justify-content: space-around;
            margin-bottom: 8px;

            &:last-child {
                margin-bottom: 0;
            }
        }

        .nav-item {
            display: flex;
            align-items: center;
            padding: 4px 8px;
            cursor: pointer;
            border-radius: 4px;
            transition: all 0.2s;
            color: #666;

            &:hover {
                background: #f5f5f5;
            }

            &.active {
                background: #e6f7ff;
                color: #1890ff;
            }

            svg {
                margin-right: 4px;
            }

            span {
                font-size: 12px;
                white-space: nowrap;
            }
        }
    }

    .ai-dialog-content {
        flex: 1;
        padding: 12px;
        overflow-y: auto;
        font-size: 12px;
    }

    .ai-dialog-close {
        position: absolute;
        top: 8px;
        right: 8px;
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-size: 16px;
        color: #999;
        border-radius: 50%;
        
        &:hover {
            background: #f5f5f5;
            color: #666;
        }
    }

    .ai-dialog-collapse {
        position: absolute;
        top: 50%;
        left: -16px;
        transform: translateY(-50%);
        width: 16px;
        height: 32px;
        background: #f0f0f0;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 4px 0 0 4px;
        z-index: 1;

        &:hover {
            background: #e0e0e0;
        }

        svg {
            transition: transform 0.3s;
        }
    }
}

.ai-content-wrapper {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.error-list {
    flex: 1;
    overflow-y: auto;
}

.error-item {
    border-bottom: 1px solid #e8e8e8;
    
    &:last-child {
        border-bottom: none;
    }
}

.error-item-header {
    width: 100%;
    display: flex;
    align-items: center;
    padding: 8px 12px;
    cursor: pointer;
    background: none;
    border: none;
    text-align: left;
    
    &:hover {
        background-color: #fafafa;
    }
}

.error-type {
    display: inline-block;
    padding: 2px 6px;
    background: #f0f0f0;
    border-radius: 2px;
    font-size: 12px;
    margin-right: 8px;
    color: #666;
}

.error-content {
    flex: 1;
    display: flex;
    align-items: center;
    font-size: 12px;
    overflow: hidden;
}

.error-text {
    color: #ff4d4f;
    margin-right: 4px;
}

.arrow {
    margin: 0 4px;
    color: #999;
}

.correction-text {
    color: #52c41a;
    margin-left: 4px;
}

.expand-icon {
    font-size: 12px;
    color: #999;
    transition: transform 0.2s;
    margin-left: 8px;
    
    &.open {
        transform: rotate(180deg);
    }
}

.explanation {
    padding: 12px;
    background: #fafafa;
    font-size: 12px;
}

.explanation-title {
    font-weight: 500;
    margin-bottom: 4px;
    color: #666;
}

.explanation-content {
    color: #333;
    line-height: 1.5;
    margin-bottom: 8px;
}

.error-position {
    color: #999;
    font-size: 12px;
}

.corrected-text-section,
.summary-section {
    padding: 12px;
    border-top: 1px solid #e8e8e8;
}

.section-title {
    font-size: 12px;
    font-weight: 500;
    color: #666;
    margin-bottom: 4px;
}

.section-content {
    font-size: 12px;
    color: #333;
    line-height: 1.5;
    user-select: text;
    cursor: text;  
}

.summary-points {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.summary-point {
    padding-left: 8px;
    position: relative;
    line-height: 1.6;
    color: #333;
}

.summary-text {
    line-height: 1.6;
    color: #333;
}

.summary-section {
    padding: 16px;
    border-top: 1px solid #e8e8e8;
    background: #fafafa;
}

.section-title {
    font-size: 13px;
    font-weight: 500;
    color: #666;
    margin-bottom: 12px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
}

.replace-button {
    padding: 2px 8px;
    font-size: 12px;
    color: #1890ff;
    background: #e6f7ff;
    border: 1px solid #91d5ff;
    border-radius: 2px;
    cursor: pointer;
    transition: all 0.3s;
    
    &:hover {
        background: #bae7ff;
        border-color: #69c0ff;
    }
    
    &:active {
        background: #91d5ff;
        border-color: #1890ff;
    }
}

.copy-button {
    padding: 2px 8px;
    font-size: 12px;
    color: #1890ff;
    background: #e6f7ff;
    border: 1px solid #91d5ff;
    border-radius: 2px;
    cursor: pointer;
    transition: all 0.3s;
    
    &:hover {
        background: #bae7ff;
        border-color: #69c0ff;
    }
    
    &:active {
        background: #91d5ff;
        border-color: #1890ff;
    }
}

.restore-button {
    padding: 2px 8px;
    font-size: 12px;
    margin: 0 4px;
    color: #1890ff;
    background: #e6f7ff;
    border: 1px solid #91d5ff;
    border-radius: 2px;
    cursor: pointer;
    transition: all 0.3s;
    
    &:hover:not(.disabled) {
        background: #bae7ff;
        border-color: #69c0ff;
    }
    
    &:active:not(.disabled) {
        background: #91d5ff;
        border-color: #1890ff;
    }
    
    &.disabled {
        color: rgba(0, 0, 0, 0.25);
        background: #f5f5f5;
        border-color: #d9d9d9;
        cursor: not-allowed;
    }
}

.replace-button, .copy-button {
    &.disabled {
        color: rgba(0, 0, 0, 0.25);
        background: #f5f5f5;
        border-color: #d9d9d9;
        cursor: not-allowed;
    }
}
