import { DocumentBorder } from '../Style';

export class TableCellInfo {
    public startGridCol: number;  // 单元格开始的行数
    public xGridStart: number;  // 单元格x开始位置
    public xGridEnd: number;  // 单元格x结束位置
    public xCellStart: number;
    public xCellEnd: number;
    public xContentStart: number;  // 单元格内容x开始位置
    public xContentEnd: number;  // 单元格内容x结束位置

    constructor(startGridCol: number = 0, xGridStart: number = 0, xGridEnd: number = 0,
                xCellStart: number = 0, xCellEnd: number = 0, xContentStart: number = 0, xContentEnd: number = 0) {
        this.startGridCol = startGridCol;
        this.xGridStart = xGridStart;
        this.xGridEnd = xGridEnd;
        this.xCellStart = xCellStart;
        this.xCellEnd = xCellEnd;
        this.xContentStart = xContentStart;
        this.xContentEnd = xContentEnd;
    }
}

/**
 * 单元格边框属性
 */
export class TableCellBorderInfo {
    public top: DocumentBorder[];
    public left: DocumentBorder[];
    public right: DocumentBorder[];
    public bottom: DocumentBorder[];

    public bottomBeforeCount: number;
    public bottomAfterCount: number;
    public maxLeft: number;
    public maxRight: number;

    constructor() {
        this.top = null;
        this.bottom = null;
        this.left = null;
        this.right = null;

        this.bottomBeforeCount = -1;
        this.bottomAfterCount = -1;

        this.maxLeft = 0;
        this.maxRight = 0;
    }

    public copy(): TableCellBorderInfo {
        const info = new TableCellBorderInfo();
        info.left = this.left?.map((item) => item.copy());
        info.right = this.right?.map((item) => item.copy());
        info.top = this.top?.map((item) => item.copy());
        info.bottom = this.bottom?.map((item) => item.copy());
        info.bottomAfterCount = this.bottomAfterCount;
        info.bottomBeforeCount = this.bottomBeforeCount;
        info.maxLeft = this.maxLeft;
        info.maxRight = this.maxRight;

        return info;
    }
}
