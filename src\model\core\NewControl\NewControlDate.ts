import { NewControl } from './NewControl';
import DocumentContentBase from '../DocumentContentBase';
import { INewControlProperty, DateBoxFormat, DATE_FORMAT_STRING, isValiDate,
  ICustomFormatDateProps,
  dateFormat,
  ResultType,
  IStructParamJson,
  NewControlErrorInfo,
  ErrorTextColor} from '../../../common/commonDefines';
import { ChangeNewControlDateBoxFormat, ChangeNewControlStartDate, ChangeNewControlEndDate,
  ChangeNewControlDateBoxTime, ChangeNewControlDateBoxText, ChangeNewControlPrintChecked } from './NewControlChange';

/**
 * 结构化元素: 日期框
 */
export class NewControlDate extends NewControl {
  private dateBoxFormat: DateBoxFormat;
  private customFormat: ICustomFormatDateProps;
  private dateTime: string; // must be yyyy-mm-dd hh:mm:ss
  private text: string;
  private time: Date;
  private startDate: string;
  private endDate: string;
  private bUnValidNumber: boolean;
  private _warnTip: string;
  private _warnText: string;
  private bPrintSelected: boolean;

  constructor(parent: DocumentContentBase, name: string, property: INewControlProperty, sText?: string ) {
    super(parent, name, property, sText && isValiDate(sText) ? sText : null);
    this.dateBoxFormat = property.dateBoxFormat;
    if (this.dateBoxFormat === undefined) {
      this.dateBoxFormat = DateBoxFormat.DateAndHMS;
    }
    this.setCustomDateBoxFormat(property.customFormat);
    // start/end date
    this.startDate = property.startDate;
    this.endDate = property.endDate;
    this.bPrintSelected = property.printSelected === undefined ? false : property.printSelected;
    if (property != null && property.dateTime != null) {
      this.setDateTime(property.dateTime);
    }
  }

  public setEmptyDate(bRefresh: boolean = false): number {
    if (this.text === '-') {
      return ResultType.UnEdited;
    }

    this.time = undefined;
    this.text = '-';
    this.dateTime = '';
    return super.setNewControlText('-', bRefresh);
  }

  public setNewControlText(sText: string, bRefresh?: boolean): number {
    if ( sText === '-') {
      return this.setEmptyDate(bRefresh);
    } else if ('' === sText ) {
      return this.resetDateBoxContent();
    }
    const change = this.setDateTime(sText);
    if (change === ResultType.Success) {
      return this.setDateBoxText();
    }

    if (change === ResultType.UnEdited) {
      return change;
    }

    return super.setNewControlText(sText, bRefresh);
  }

  public setNewControlTextByJson(json: IStructParamJson): number {
    let text = json.all_data;
    if (!text) {
      return ResultType.UnEdited;
    }
    if (text === '-') {
      return this.setEmptyDate();
    }

    const reg = /^Date=\d{4}[\/\-年]\d{1,2}[\/\-月]\d{1,2}(日)? Time=\d{1,2}[:\/时]\d{1,2}[:\/分]\d{1,2}(秒)?$/;
    if (!reg.test(text)) {
      return ResultType.UnEdited;
    }
    text = text.replace(/Date=|Time=/g, '');

    const change = this.setDateTime(text);
    if (change !== ResultType.Success) {
      return change;
    }
    this.setDateTimeText();
    text = this.text;

    return super.setNewControlText(text, false);
  }

  public setDateBoxText(bRefresh?: boolean): number {
    const bChange = this.setDateTimeText();
    if (bChange === false) {
      return ResultType.UnEdited;
    }
    return super.setNewControlText(this.text, bRefresh);
  }

  public getAllDate(): string {
    const curDate = this.getDateTime();
    if (curDate == null) {
      return '';
    }
    const dates = curDate?.split(' ');
    if (dates == null) {
      return '';
    }

    return `Date=${dates[0]} Time=${dates[1]}`;

  }

  public setCustomDateBoxFormat(format: ICustomFormatDateProps, propType?: DateBoxFormat): boolean {
    // console.log(format)
    if (format === undefined || this.dateBoxFormat !== DateBoxFormat.AutoCustom) {
      // dateBoxFormat may not be set yet, better solution?
      if (propType == null || (propType != null && propType !== DateBoxFormat.AutoCustom)) {
        return false;
      }
    }
    this.customFormat = {
      day: '', //
      dayAfter: '', //
      hour: '', //
      hourAndMinute: '', //
      millisecond: '',
      minute: '', //
      minuteAndSecond: '',
      month: '', //
      monthAnyDay: '', //
      second: '',
      secondAfter: '',
      year: '', //
      yearAndMonth: '', //
      millisecondAfter: '',
    };

    // this.customFormat = format;

    for (const key in format) {
      if (key != null) {
        let val = format[key];
        if (val == null) {
          val = '';
        }
        this.customFormat[key] = val;
      }
    }
    this.setDirty();
    return true;
  }

  public setDateTime(dateTime?: string): number {
    // if (this.dateTime && this.dateTime.split('.')[0] === dateTime) {
    //   return false;
    // }

    // console.log(dateTime)
    let time = new Date();
    if (dateTime != null) {
      time = new Date(dateTime);
    }
    if (this.dateTime === dateTime) {
      return ResultType.UnEdited;
    }
    if (time && !isNaN(time.getTime())) {
      // normal date object
      const history = this.getHistory();
      if ( history ) {
        history.addChange(new ChangeNewControlDateBoxTime(this, this.dateTime, dateTime));
      }

      this.dateTime = dateTime;
      this.time = time;
      return ResultType.Success;
    } else {
      // date object with 'invalid date' str
      this.dateTime = '';
      this.time = null;
    }

    this.setDirty();
    return ResultType.Failure;

  }

  public getCustomDateFormat(): ICustomFormatDateProps {
    return this.customFormat || {};
  }

  public getDateTime(): string {
    return this.dateTime;
  }

  public setWarnTip(tip?: string, text?: string): number {
    if (text !== this._warnText) {
      this._warnText = text;
      this.setMaxLengthBgColor(tip ? ErrorTextColor.Default : undefined);
      this.bUnValidNumber = undefined;
    }
    if (this._warnTip === tip) {
      return ResultType.UnEdited;
    }
    this._warnTip = tip;
    return ResultType.Success;
  }

  public hasWarnTip(): boolean {
    return !!this._warnTip;
  }

  public getWarnTip(): string {
    const warnTip = this._warnTip;
    if (!warnTip) {
      return;
    }
    // const cascades = this.getCascades();
    // if (cascades.find((cascade) => cascade.actionText === warnTip)) {
    //   return warnTip;
    // } else {
    //   this._warnTip = undefined;
    //   this.setMaxLengthBgColor();
    // }
    return warnTip;
  }

  public getStartDate(): string {
    return this.startDate;
  }

  public getEndDate(): string {
    return this.endDate;
  }

  public setProperty(property: INewControlProperty): number {
    let res = super.setProperty(property);

    if (this.setCustomDateBoxFormat(property.customFormat, property.dateBoxFormat)) {
      res = ResultType.Success;
    }
    const history = this.getHistory();

    // set dateBoxFormat
    if (property.dateBoxFormat != null &&
      this.dateBoxFormat !== property.dateBoxFormat ) { // 0 is enum

      if ( history ) {
        history.addChange(new ChangeNewControlDateBoxFormat(this, this.dateBoxFormat, property.dateBoxFormat));
      }
      res = ResultType.Success;
      this.dateBoxFormat = property.dateBoxFormat;
      this.setDateBoxText();
    } else if (property.dateBoxFormat === DateBoxFormat.AutoCustom) {
      this.setDateBoxText();
      res = ResultType.Success;
    } else if (property.startDate != null && this.startDate !== property.startDate ) {
      if ( history ) {
        history.addChange(new ChangeNewControlStartDate(this, this.startDate, property.startDate));
      }
      this.startDate = property.startDate;
      res = ResultType.Success;
      // this.setDateBoxText();
    } else if (property.endDate != null && this.endDate !== property.endDate ) {
      if ( history ) {
        history.addChange(new ChangeNewControlEndDate(this, this.endDate, property.endDate));
      }
      this.endDate = property.endDate;
      res = ResultType.Success;
      // this.setDateBoxText();
    }

    // set startdate/enddate
    this.startDate = property.startDate;
    this.endDate = property.endDate;
    if (property.dateTime != null) {
      this.setDateTime(property.dateTime);
    }
    if (ResultType.Success === this.setPrintSelected(property.printSelected)) {
      res = ResultType.Success;
    }
    return res;
  }

  public getProperty(): INewControlProperty {
    const newControlProperty = super.getProperty();
    newControlProperty.dateBoxFormat = this.dateBoxFormat;
    newControlProperty.customFormat = this.customFormat;
    newControlProperty.startDate = this.startDate;
    newControlProperty.endDate = this.endDate;
    newControlProperty.dateTime = this.dateTime;
    newControlProperty.printSelected = this.bPrintSelected;

    return newControlProperty;
  }

  public getDateBoxFormat(): DateBoxFormat {
    return this.dateBoxFormat;
  }

  public setDateBoxFormat(type: DateBoxFormat, customFormat?: ICustomFormatDateProps): boolean {
    if (type === undefined || type === this.dateBoxFormat) {
      return this.setCustomDateBoxFormat(customFormat);
    }
    if (!(type in DateBoxFormat)) {
      // crash safe
      type = DateBoxFormat.DateAndHMS;
    }

    const history = this.getHistory();
    if ( history ) {
      history.addChange(new ChangeNewControlDateBoxFormat(this, this.dateBoxFormat, type));
    }
    this.dateBoxFormat = type;
    this.setCustomDateBoxFormat(customFormat);
    this.setDirty();
    return true;
  }

  public getDateBoxFormatString(): string {
    let strResult = DATE_FORMAT_STRING[this.dateBoxFormat];

    if ( this.customFormat && (DateBoxFormat.AutoCustom === this.dateBoxFormat) ) {
      // const cusFormat = this.customFormat;
      // const customFormat = {
      //   Year: cusFormat.year,
      //   YearSeparat: cusFormat.yearAndMonth,
      //   Month: cusFormat.month,
      //   MonthSeparat: cusFormat.monthAnyDay,
      //   Day: cusFormat.day,
      //   DaySeparat: cusFormat.dayAfter,
      //   Hour: cusFormat.hour,
      //   HourSeparat: cusFormat.hourAndMinute,
      //   Minute: cusFormat.minute,
      //   MinuteSeparat: cusFormat.minuteAndSecond,
      //   Second: cusFormat.second,
      //   SecondSeparat: cusFormat.secondAfter,
      //   Millisecond: cusFormat.millisecond,
      //   MillisecondSeparat: cusFormat.millisecondAfter,
      // };

      // strResult = JSON.stringify(customFormat);
    //   strResult = this.getCustomDateBoxFormatString();
        strResult = this.getDateFormat();
    }

    return strResult;
  }

  public getExternalDateBoxFormatString(): string {
    let strResult = DATE_FORMAT_STRING[this.dateBoxFormat];

    if ( this.customFormat && (DateBoxFormat.AutoCustom === this.dateBoxFormat) ) {
      const cusFormat = this.customFormat;
      const customFormat = {
        Year: cusFormat.year,
        YearSeparat: cusFormat.yearAndMonth,
        Month: cusFormat.month,
        MonthSeparat: cusFormat.monthAnyDay,
        Day: cusFormat.day,
        DaySeparat: cusFormat.dayAfter,
        Hour: cusFormat.hour,
        HourSeparat: cusFormat.hourAndMinute,
        Minute: cusFormat.minute,
        MinuteSeparat: cusFormat.minuteAndSecond,
        Second: cusFormat.second,
        SecondSeparat: cusFormat.secondAfter,
        Millisecond: cusFormat.millisecond,
        MillisecondSeparat: cusFormat.millisecondAfter,
      };

      strResult = JSON.stringify(customFormat);
    }

    return strResult;
  }

  public getAllDateText(): string {
    if (!this.time || !this.dateTime) {
      return ResultType.StringEmpty;
    }

    const res = dateFormat(this.time, 'yyyy-MM-dd HH:mm:ss')
    .split(' ');
    return `Date=${res[0]} Time=${res[1] || ''}`;
  }

  public getCustomDateBoxFormatString(): string {
    let result = '';
    const customFormat = this.customFormat;
    // console.log(customFormat)
    if (this.dateBoxFormat === DateBoxFormat.AutoCustom && customFormat != null) {
      const {year, yearAndMonth, month, monthAnyDay, day, dayAfter, hour, hourAndMinute, minute,
        minuteAndSecond, second, secondAfter, millisecond, millisecondAfter} = customFormat;
      result = year + yearAndMonth + month + monthAnyDay + day + dayAfter + hour + hourAndMinute + minute +
        minuteAndSecond + second + secondAfter + millisecond;
      if (millisecondAfter != null) {
        result += millisecondAfter;
      }
    }
    return result;
  }

  public resetDateBoxContent(): number {
    if (this.isPlaceHolderContent()) {
      return ResultType.UnEdited;
    }

    // this.addPlaceHolderContent();
    super.setNewControlText('');

    const history = this.getHistory();
    if ( history ) {
      history.addChange(new ChangeNewControlDateBoxTime(this, this.dateTime, undefined));
      history.addChange(new ChangeNewControlDateBoxText(this, this.text, ''));
    }

    this.dateTime = undefined;
    this.text = '';

    this.recalculate();
    this.triggerCascade();
    this.setDirty();
    return ResultType.Success;
  }

  public setErrorTextBgColor2(type?: number): string {
    const warnTip = this._warnTip;
    if (warnTip) {
      const cascades = this.getCascades();
      if (cascades.find((cascade) => cascade.actionText === warnTip)) {
        this.setMaxLengthBgColor(ErrorTextColor.Default);
        this.bUnValidNumber = undefined;
        return warnTip;
      } else {
        this._warnTip = undefined;
        this.setMaxLengthBgColor();
      }
    }
    return;
    // const startPortion = this.getStartBorderPortion();
    // const endPortion = this.getEndBorderPortion();
    // const contents = startPortion.paragraph.content;
    // let bStart: boolean = false;
    // for (let index = 0, length = contents.length; index < length; index++) {
    //     const portion = contents[index];
    //     if (portion === startPortion) {
    //         bStart = true;
    //         continue;
    //     }
    //     if (portion === endPortion) {
    //         break;
    //     }
    //     if (bStart) {
    //         portion.textProperty.setMaxLengthBackgroundColor(color);
    //     }
    // }
  }

  public setTextErrorBgColor(type: number): boolean {
    // console.log(type)
    if (type === 1) {
      if (this.bUnValidNumber) {
        this.setMaxLengthBgColor();
        return true;
      }
      return false;
    }
    this.text = this.getNewControlText();
    // console.log(this.time)
    // console.log(this.text)
    // console.log(this)
    const res = this.isValidDate(this.text);
    // console.log(res)
    if (res !== NewControlErrorInfo.Success) {
      this.setDateTime(this.text);
      this.setMaxLengthBgColor(ErrorTextColor.Default);
      return true;
    } else if (this.bUnValidNumber) {
      this.setMaxLengthBgColor();
      return true;
    }

    return false;
  }

  public isValidDate(dateStr?: string, setDateTime: boolean = true): NewControlErrorInfo {
    let checkedDateStr = dateStr;
    if (dateStr == null) {
      checkedDateStr = this.getNewControlText();
      this.text = checkedDateStr;
    }
    // console.log(dateStr)
    if (checkedDateStr === '') {
      // empty str is allowed?
      return NewControlErrorInfo.Success;
    }

    // console.log(this)

    const result = this.checkDateTextValid(checkedDateStr, setDateTime);
    // console.log(result)

    return result;
    // const dateObj = new Date(checkedDateStr);
    // if (dateObj instanceof Date && !isNaN(dateObj as any)) {
    //   return NewControlErrorInfo.Success;
    // }
    // return NewControlErrorInfo.Success;
  }

  public setMaxLengthBgColor(color?: string): boolean {
    const startPortion = this.getStartBorderPortion();
    const endPortion = this.getEndBorderPortion();
    const contents = startPortion.paragraph.content;
    let bStart: boolean = false;
    for (let index = 0, length = contents.length; index < length; index++) {
        const portion = contents[index];
        if (portion === startPortion) {
            bStart = true;
            continue;
        }
        if (portion === endPortion) {
            break;
        }
        if (bStart) {
            portion.textProperty.setMaxLengthBackgroundColor(color);
        }
    }
    this.bUnValidNumber = color !== undefined;
    return true;
  }

  public clearText(): void {
    this.text = '';
  }

  /**
   * 获取当前光标所在日期框的位置：年、月、日、时、分、秒
   * 当光标处于秒时，默认返回年的位置
   * 位置定义：
   * 年：0，月：1，日：2，时：3，分：4，秒：0，
   * 如果只有占位符，返回年
   * @returns
   */
  public getCursorInPosType(): number {
    let type = 0;

    if (!this.isPlaceHolderContent()) {
      const para = this.getStartBorderInParagraph();
      const startBorderPortion = this.getStartBorderPortion();
      const curPortionIndex = para.curPos.contentPos;
      const startPortionIndex = para.getPortionIndexById(startBorderPortion.id);

      const curPortion = para.content[curPortionIndex];
      const curPos = curPortion.portionContentPos;

      if (DateBoxFormat.AutoCustom === this.dateBoxFormat && this.customFormat) {
        if (curPortionIndex !== startPortionIndex) {
          let {year, yearAndMonth, month, monthAnyDay, day, dayAfter,
                hour, hourAndMinute, minute} = this.customFormat;

          month = month ? ((month.length < (this.time.getMonth() + 1 + '').length) ? this.time.getMonth() + 1 + '' : month) : '';
          day = day ? ((day.length < (this.time.getDate() + '').length) ? this.time.getDate() + '' : day) : '';

          const yearMonthLen = year.length + (year ? yearAndMonth.length : 0);
          const yearMonthDayLen = yearMonthLen + month.length + (month ? monthAnyDay.length : 0);
          const yearMonthDayHourLen = yearMonthDayLen + day.length + (day ? dayAfter.length : 0);
          const yearMonthDayHourMinuteLen = yearMonthDayHourLen + hour.length + (hour ? hourAndMinute.length : 0);
          if (0 < curPos && curPos <= year.length) {
            type = 0;
          } else if (year.length < curPos && curPos <= yearMonthLen + month.length) {
            type = this.getCurType(1);
          } else if (yearMonthLen + month.length < curPos && curPos <= yearMonthDayLen + day.length) {
            type = this.getCurType(2);
          } else if (yearMonthDayLen + day.length < curPos && curPos <= yearMonthDayHourLen + hour.length) {
            type = this.getCurType(3);
          } else if (yearMonthDayHourLen + hour.length < curPos && curPos <= yearMonthDayHourMinuteLen + minute.length) {
            type = this.getCurType(4);
          } else {
            type = this.getCurType(0);
          }
        } else {
          type = this.getCurType(0);
        }
      } else if (DateBoxFormat.AutoCustom !== this.dateBoxFormat) {
        const bHMS = DateBoxFormat.HMS === this.dateBoxFormat;

        if (curPortionIndex !== startPortionIndex) {
          // const format = DATE_FORMAT_STRING[this.dateBoxFormat];
          if (!bHMS) {
            // 'yyyy-MM-dd HH:mm:ss'
            if (0 < curPos && curPos <= 4) {
              type = 0;
            } else if (4 < curPos && curPos <= 7) {
              type = 1;
            } else if (7 < curPos && curPos <= 10) {
              type = 2;
            } else if (10 < curPos && curPos <= 13) {
              type = 3;
            } else if (13 < curPos && curPos <= 16) {
              type = 4;
            } else {
              type = 0;
            }
          } else {
            // 'HH:mm:ss'
            if (0 < curPos && curPos <= 2) {
              type = 3;
            } else if (2 < curPos && curPos <= 5) {
              type = 4;
            } else {
              type = 3;
            }
          }
        } else {
          type = bHMS ? 3 : 0;
        }
      }
    } else {
      if (DateBoxFormat.AutoCustom === this.dateBoxFormat) {
        type = this.getCurType(0);

      } else {
        type = DateBoxFormat.HMS === this.dateBoxFormat ? 3 : 0;
      }
    }

    return type;
  }

  public isPrintSelected(): boolean {
      return this.bPrintSelected;
  }

  public getPrintSelected(): boolean {
      return this.bPrintSelected;
  }

  public setPrintSelected(bPrintSelected: boolean): number {
      if (this.bPrintSelected === bPrintSelected || bPrintSelected == null) {
          return ResultType.UnEdited;
      }

      const history = this.getHistory();
      if ( history ) {
          history.addChange(new ChangeNewControlPrintChecked(this, this.bPrintSelected, bPrintSelected));
      }

      this.bPrintSelected = bPrintSelected;
      this.setDirty();
      return ResultType.Success;
  }

  public toPrint(): void {
      super.toPrint();
      if (this.isHidden()) {
          return;
      }

      if (this.bPrintSelected) {
          this.setHidden(true, false);
      }
  }

  private checkDateTextValid(dateText: string, setDateTime: boolean = true): NewControlErrorInfo {
    // console.log(dateText)
    let result = NewControlErrorInfo.Success;

    if (dateText == null) {
      return result;
    }
    // 目前日期框不能键入修改，可以直接判断格式
    if (dateFormat(this.time, this.getDateBoxFormatString()) !== dateText) {
        result = NewControlErrorInfo.DateOutofRange;
    }
    return result;

    // get separation contents
    const dateboxFormat = this.getDateBoxFormat();

    const separatorCollection = {
      ymSep: '-',
      mdSep: '-',
      dHSep: ' ',
      hmSep: ':',
      msSep: ':',
      sfSep: '',
      postF: '',
    };

    switch (dateboxFormat) {
      case DateBoxFormat.DateAndHMS: {
        // default suffice
        break;
      }
      case DateBoxFormat.DateAndHM: {
        separatorCollection.msSep = '';
        break;
      }
      case DateBoxFormat.Date: {
        separatorCollection.dHSep = '';
        separatorCollection.hmSep = '';
        separatorCollection.msSep = '';
        break;
      }
      case DateBoxFormat.HMS: {
        separatorCollection.ymSep = '';
        separatorCollection.mdSep = '';
        separatorCollection.dHSep = '';
        break;
      }
      case DateBoxFormat.AutoCustom: {
        const {yearAndMonth, monthAnyDay, dayAfter, hourAndMinute, minuteAndSecond,
          secondAfter, millisecondAfter,
          year, month, day, hour, minute, second, millisecond} = this.getCustomDateFormat();
        separatorCollection.ymSep = yearAndMonth;
        separatorCollection.mdSep = monthAnyDay;
        separatorCollection.dHSep = dayAfter;
        separatorCollection.hmSep = hourAndMinute;
        separatorCollection.msSep = minuteAndSecond;
        separatorCollection.sfSep = secondAfter;
        separatorCollection.postF = millisecondAfter;

        // customized according to prev val
        // const years = [{key: 'yyyy', value: 'yyyy'}, {key: 'yy', value: 'yy'}, {key: '无', value: ''}];
        // const months = [{key: 'MM', value: 'MM'}, {key: 'M', value: 'M'}, {key: '无', value: ''}];
        // const days = [{key: 'dd', value: 'dd'}, {key: 'd', value: 'd'}, {key: '无', value: ''}];
        // const hours = [{key: 'HH', value: 'HH'}, {key: '无', value: ''}];
        // const minutes = [{key: 'mm', value: 'mm'}, {key: '无', value: ''}];
        // const seconds = [{key: 'ss', value: 'ss'}, {key: '无', value: ''}];
        // const milliseconds = [{key: 'fff', value: 'fff'}, {key: '无', value: ''}];
        if (year === '') {
          // 无
          separatorCollection.ymSep = '';
        }
        if (month === '') {
          separatorCollection.mdSep = '';
        }
        if (day === '') {
          separatorCollection.dHSep = '';
        }
        if (hour === '') {
          separatorCollection.hmSep = '';
        }
        if (minute === '') {
          separatorCollection.msSep = '';
        }
        if (second === '') {
          separatorCollection.sfSep = '';
        }
        if (millisecond === '') {
          separatorCollection.postF = '';
        }

        break;
      }
      default: {
        break;
      }
    }

    // check DateSeparatorError error
    let checkedDateText = dateText;
    // check DateOutofRange error
    let remedyDateText: any = dateText;

    // tslint:disable-next-line: forin
    for (const key in separatorCollection) {
      const value = separatorCollection[key];
      if ( value !== '') {
        const index = checkedDateText.indexOf(value);
        if (index === -1) {
          result = NewControlErrorInfo.DateSeparatorError;
          break;
        } else {
          checkedDateText = checkedDateText.slice(0, index) + checkedDateText.slice(index + value.length);
        }

        // check DateOutofRange, change separators to placeholder
        const remedyIndex = remedyDateText.indexOf(value);
        if (remedyIndex !== -1) {
          switch (key) {
            case 'ymSep':
            case 'mdSep': {
              remedyDateText = remedyDateText.slice(0, remedyIndex) + 'hyphen' +
                remedyDateText.slice(remedyIndex + value.length);
              break;
            }
            case 'dHSep': {
              remedyDateText = remedyDateText.slice(0, remedyIndex) + 'space' +
                remedyDateText.slice(remedyIndex + value.length);
              break;
            }
            case 'hmSep':
            case 'msSep': {
              remedyDateText = remedyDateText.slice(0, remedyIndex) + 'colon' +
                remedyDateText.slice(remedyIndex + value.length);
              break;
            }
            case 'sfSep': {
              remedyDateText = remedyDateText.slice(0, remedyIndex) + 'dot' +
                remedyDateText.slice(remedyIndex + value.length);
              break;
            }
            case 'postF': {
              remedyDateText = remedyDateText.slice(0, remedyIndex) + 'void' +
                remedyDateText.slice(remedyIndex + value.length);
              break;
            }
            default: {
              break;
            }
          }
        } else {
          // tslint:disable-next-line: no-console
          console.log('check DateOutofRange error');
        }
      }
    }

    if (result === NewControlErrorInfo.Success) {
      // DateSeparatorError pass, continue checking DateOutofRange

      // console.log(remedyDateText);

      remedyDateText = remedyDateText.replaceAll('hyphen', '-')
        .replace('space', ' ')
        .replaceAll('colon', ':')
        .replace('dot', '.')
        .replace('void', '');

      const placeholders = ['-', ' ', ':', '.'];
      const lastIndex = remedyDateText.length - 1;
      if (placeholders.includes(remedyDateText[lastIndex]) === true) {
        remedyDateText = remedyDateText.slice(0, lastIndex);
      }

      // add back to standard string according to types
      if (this.dateBoxFormat === DateBoxFormat.HMS) {
        const date = new Date();
        remedyDateText = date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate() + ' ' + remedyDateText;
      } else if (this.dateBoxFormat === DateBoxFormat.AutoCustom) {
        const date = new Date();
        const {yearAndMonth, monthAnyDay, dayAfter, hourAndMinute, minuteAndSecond,
          secondAfter, millisecondAfter,
          year, month, day, hour, minute, second, millisecond} = this.getCustomDateFormat();
        if (year === 'yy') {
          remedyDateText = date.getFullYear() + remedyDateText.slice(2);
        }
        if (month === '') {
          // TODO: fix custom dates string
        }
        // TODO: more fix to standard
      }

      // console.log(remedyDateText);

      const dateObj = new Date(remedyDateText);
      if (dateObj instanceof Date && !isNaN(dateObj as any)) {
        // success

        // special cases
        switch (this.dateBoxFormat) {
          case DateBoxFormat.DateAndHMS: {
            if (remedyDateText.indexOf('::') > -1) {
              result = NewControlErrorInfo.DateOutofRange;
            } else if (this.checkDayValidity(this.dateBoxFormat, remedyDateText) === false) {
              result = NewControlErrorInfo.DateOutofRange;
            } else if (remedyDateText.endsWith('/') === true) {
              result = NewControlErrorInfo.DateOutofRange;
            }
            break;
          }
          case DateBoxFormat.Date: {
            if (this.checkDayValidity(this.dateBoxFormat, remedyDateText) === false) {
              result = NewControlErrorInfo.DateOutofRange;
            }
            break;
          }
          case DateBoxFormat.HMS: {
            if (remedyDateText.indexOf('::') > -1) {
              result = NewControlErrorInfo.DateOutofRange;
            } else if (remedyDateText.endsWith('/') === true) {
              result = NewControlErrorInfo.DateOutofRange;
            }
            break;
          }
          case DateBoxFormat.DateAndHM: {
            if (this.checkDayValidity(this.dateBoxFormat, remedyDateText) === false) {
              result = NewControlErrorInfo.DateOutofRange;
            }
            break;
          }
          default: {
            break;
          }
        }

      } else {
        result = NewControlErrorInfo.DateOutofRange;
      }
    }

    // 内容校验时保留原始datetime
    // if (result === NewControlErrorInfo.Success) {
    //   if (dateText !== this.dateTime && setDateTime === true) {
    //     this.setDateTime(remedyDateText);
    //   }
    // }
    return result;

  }

  private setDateTimeText(): boolean {
    // if (!this.time) {
    //   if (!this.text) {
    //     return;
    //   }
    //   this.text = sText;
    //   // this.setDateBoxText(this.text);
    //   return true;
    // }
    // const date = this.dateTime;
    let format = DATE_FORMAT_STRING[this.dateBoxFormat];
    if (!format) {
      format = this.getDateFormat();
      if (!format) {
        return false;
      }
    }

    const text = dateFormat(this.time, format);
    if (text === this.text) {
      return false;
    }

    const history = this.getHistory();
    if ( history ) {
      history.addChange(new ChangeNewControlDateBoxText(this, this.text, text));
    }

    this.text = text;
    return true;
  }

  private checkDayValidity(dateboxFormat: DateBoxFormat, remedyDateText: string): boolean {
    let result = true;
    switch (dateboxFormat) {
      case DateBoxFormat.DateAndHMS:
      case DateBoxFormat.DateAndHM: {
        const lastIndexHyphen = remedyDateText.lastIndexOf('-');
        const lastIndexSpace = remedyDateText.lastIndexOf(' ');
        const day = remedyDateText.slice(lastIndexHyphen + 1, lastIndexSpace);
        if (day.length > 2) {
          result = false;
        } else {
          if (this.isDayValidInLeapYear(remedyDateText, day) === false) {
            result = false;
          }
        }
        break;
      }
      case DateBoxFormat.Date: {
        const lastIndexHyphen = remedyDateText.lastIndexOf('-');
        const day = remedyDateText.slice(lastIndexHyphen + 1);
        if (day.length > 2) {
          result = false;
        } else {
          if (this.isDayValidInLeapYear(remedyDateText, day) === false) {
            result = false;
          }
        }
        break;
      }
      default: {
        break;
      }
    }

    return result;
  }

  private isDayValidInLeapYear(remedyDateText: string, day: string): boolean {
    let result = true;

    const yearIndex = remedyDateText.indexOf('-');
    const year = +remedyDateText.slice(yearIndex - 4, yearIndex);
    const month = remedyDateText.slice(yearIndex + 1, yearIndex + 3);
    // console.log(year)
    // console.log(month)
    const bigMArr = [1, 3, 5, 7, 8, 10, 12];
    const smallMArr = [4, 6, 9, 11];

    if (bigMArr.includes(+month) === true) {
      if (+day < 1 || +day > 31) {
        result = false;
      }
    } else if (smallMArr.includes(+month) === true) {
      if (+day < 1 || +day > 30) {
        result = false;
      }
    } else if (month === '02') {
      if ((year % 100 === 0) ? (year % 400 === 0) : (year % 4 === 0)) {
        // is leap year
        if (+day < 1 || +day > 29) {
          result = false;
        }
      } else {
        if (+day < 1 || +day > 28) {
          result = false;
        }
      }
    }

    return result;
  }

  private getDateFormat(): string {
    return this.getYear() + this.getMonth() + this.getDay() + this.getHour() + this.getMinute()
    + this.getSecond() + this.getMillisecond();
  }

  private getYear(): string {
    const custom = this.customFormat;
    if (custom != null && custom.year) {
        return custom.year + (custom.yearAndMonth || '');
    }
    return '';
  }

  private getMonth(): string {
      const custom = this.customFormat;
      if (custom != null && custom.month) {
          return custom.month + (custom.monthAnyDay || '');
      }
      return '';
  }

  private getDay(): string {
      const custom = this.customFormat;
      if (custom != null && custom.day) {
          return custom.day + (custom.dayAfter || '');
      }
      return '';
  }

  private getHour(): string {
      const custom = this.customFormat;
      if (custom != null && custom.hour) {
          return custom.hour + (custom.hourAndMinute || '');
      }
      return '';
  }

  private getMinute(): string {
      const custom = this.customFormat;
      if (custom != null && custom.minute) {
          return custom.minute + (custom.minuteAndSecond || '');
      }
      return '';
  }

  private getSecond(): string {
      const custom = this.customFormat;
      if (custom != null && custom.second) {
          return custom.second + (custom.secondAfter || '');
      }
      return '';
  }

  private getMillisecond(): string {
      const custom = this.customFormat;
      if (custom != null && custom.millisecond) {
          return custom.millisecond + (custom.millisecondAfter || '');
      }
      return '';
  }

  /**
   * 当前位置格式是否生效，如果不生效往后遍历
   * @param pos 当前位置
   * @returns
   */
  private getCurType(pos: number): number {
    let type = 0;
    switch (pos) {
      case 0:
        if (this.customFormat.year) {
          type = 0;
          break;
        }
      case 1:
        if (this.customFormat.month) {
          type = 1;
          break;
        }
      case 2:
        if (this.customFormat.day) {
          type = 2;
          break;
        }
      case 3:
        if (this.customFormat.hour) {
          type = 3;
          break;
        }
      case 4:
        if (this.customFormat.minute) {
          type = 4;
          break;
        }
    }

    return type;
  }
}
