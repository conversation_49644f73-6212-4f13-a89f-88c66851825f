import Paragraph from '../Paragraph';
import {RecalcResultType} from '../Document';

/**
 * 对齐方式时统计各变量和状态
 */
export class ParagraphRecalculateStateAlign {
    public x: number = 0;
    public y: number = 0;
    public xEnd: number = 0;
    public justifyWord: number = 0; //
    public justifySpace: number = 0;
    public spacesCounter: number = 0;
    public spacesSkip: number = 0;
    public lettersSkip: number = 0;
    public lastWidth: number = 0;
    public paragraph: Paragraph = undefined;

    public recalcResult: RecalcResultType = 0;

    public y0: number = 0;
    public y1: number = 0;

    public curPage: number = 0;
    public pageX: number = 0;
    public pageY: number = 0;

    public bRecalcFast: boolean = false;
    public bRecalcFast2: boolean = false;

    constructor() {
        //
    }
}
