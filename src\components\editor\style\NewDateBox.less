.new-control-datebox-widget {
  position: absolute; 
  border: 1px solid #a0cdf7;;
  background-color: #FFF;
  user-select: none;
  z-index: 1800;
  font-family: "Helvetica Neue For Number", "PingFang SC","Hiragino Sans GB","Microsoft YaHei","Helvetica Neue",Helvetica, Arial, sans-serif!important;

  .focus-hidden {
    position: absolute;
    left: -2px;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: 0;
    opacity: 0.0001;
    outline: none;
    border: none;
  }

  .ym-wrapper {
    background-color: #dbecfb;
    height: 30px;
    position: relative;
    text-align: center;

    .ym-container {
      position: absolute;
      top: 0;
      background-color: #ffffff;
      z-index: 2;
      width: 0;
      height: 0;
      // width: 100%;
      // height: 237px;
      opacity: 0;
      // visibility: hidden;
      transition: width 0.15s linear;
      // opacity: 0;
      overflow: hidden;

      &.show {
        width: 100%;
        height: 237px;
        opacity: 1;
        // visibility: visible;
      }

      .ym-container-left, .ym-container-right {
        width: 50%;
        display: inline-block;
        min-width: 124px;
        position: absolute;
      }

      .ym-container-left {
        left: 0;
      }

      .ym-container-right {
        left: 125px;
      }

      .ym-table {
        width: 100%;
        min-width: 124px;
        height: 207px;
      }

      .button-wrapper {
        position: absolute;
        bottom: 0;
        width: 100%;
        min-width: 248px;
        height: 30px;

        .button {
          width: 50%;
        }
      }
    }

    .arrow-symbol, .year-scroll {
      cursor: pointer;
      font-weight: bold;
    }

    .arrow-symbol {
      font-weight: bold;
      font-size: 16px;
    }

    .arrow-left, .arrow-right {
      position: absolute;
      top: 2px;
    }

    .arrow-left {
      left: 10px;
    }

    .arrow-right {
      right: 10px;
    }

    .ym-content {
      font-size: 1.14em; /* 16px/14px = 1.14em */
      font-weight: bold;
      cursor: pointer;
      padding-top: 1px;
      position: relative;
      top: 3px;
    }
  }

  .date-wrapper {
    width: 99%;
    // border: 1px solid #a1cef7;
    // margin-bottom: 5px;
    height: 182px; // useless

  }

  table {
    // counter emr missing <doctype>
    text-align: center; 

    tr td, tr th {
      font-size: 1em; /* 使用相对单位，跟随父容器字体大小 */
    }
  }

  .date-wrapper, .ym-container {
    td:not(.disabled-text):not(.focused-text):hover {
      // background-color: lightcyan;
      background-color: #eeeeee;
      border-color: #d7d7d7;
      // border: 1px solid #d7d7d7;
      // border-radius: 5px;
    }

    // overwrite css in emr
    tr td, tr th {
      padding: .1rem;
      vertical-align: middle;
      border-bottom: none;
    }

    tr td {
      border-radius: 5px;
      // border: 1px solid #ffffff;

      &.focused-text {
        border-color: #bad8fc;
        background-color: #0c65d5;
        color: #ffffff;
        // border-radius: 5px;
      }
    }
  }

  .time-wrapper {
    position: relative;
    text-align: left;
    border-top: 1px solid #a1cef7;
    height: 25px;
    padding-top: 2px;

    div, input {
      display: inline-block;
    }

    input {
      position: relative;
      bottom: 1px;
      width: 40px;
      box-sizing: border-box;
      font-size: 12px;
    }

    .description, .separator {
      background-color: #F6F6F6;
      padding: 0 3px;
    }

    .description {
      margin-right: 5px;
    }

  }

  .preview-wrapper {
    border: 1px solid lightblue;
    margin: 8px;
    text-align: left;

    input, span {
      display: inline-block;
      border: none;
    }

    input[type=number]{
      font-size: 16px;
      
      &:focus, &::selection {
        background-color: rgb(229, 245, 255);
        color: #293750;
      }

      outline: none;
      text-align: center;
      padding: 5px 0;

      /* Chrome, Safari, Edge, Opera */
      &::-webkit-outer-spin-button,
      &::-webkit-inner-spin-button {
        -webkit-appearance: none;
        margin: 0;
      }

      /* Firefox */
      -moz-appearance: textfield;

      &.yearBox {
        width: 49px;
      }

      &.monthBox, &.dayBox, &.hourBox, &.minBox {
        width: 32px;
      }

    }

  }

  .button-wrapper {
    border-top: 1px solid #a1cef7;
    background-color: #dbecfb;
    height: 25px;
    margin-top: auto;

    .button {
      background-color: #dbecfb;
      font-size: 14px;
      width: 33%;
      cursor: pointer;
      border-color: rgb(118, 118, 118);
      border-width: 0;
      height: 100%;

      &:hover {
        background-color: #65aef8;
      }
    }
  }

  // --- optional css, when datebox type has no time boxes ---
  .no-time-main-button-wrapper {
    position: relative;
    top: 25px;
  }

  &.no-time-widget-height-compensation {
    height: 264px;
  }
  // --- optional css, when datebox type has no time boxes END ---

  .hidden {
    display: none;
  }

  .disabled-text {
    color: lightgrey;
  }

  .disabled-date {
    color: darkgrey;
  }
}





