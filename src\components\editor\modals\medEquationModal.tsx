import * as React from 'react';
import { DocumentCore } from '../../../model/DocumentCore';
// import * as ordEquationImage from '../../../common/resources/ord-rev.png';
// import * as menEquationImage from '../../../common/resources/men-rev.png';
// import * as fractionEquationImage from '../../../common/resources/fraction-rev.png';
import { IEquationProperties } from '../../../model/MedEquationProperty';
import { EquationType } from '../../../common/commonDefines';
import { ParaElementType } from '../../../model/core/Paragraph/ParagraphContent';
import { FRACTION_EQUATION, ORD_EQUATION, MEN_EQUATION } from '../../../common/resources/imageSources';

interface IMedEquationModalProps {
  documentCore: DocumentCore;
  closeModal: (type: string) => void;
  addInlineImage: (width: number, height: number, src: string, name: string, type: EquationType, svgElem?: any)
                  => void;
  showEquationModal?: boolean;
  equationRefs?: any;
}

interface IMedEquationModalState {
  equationProperties?: IEquationProperties;
  showError?: boolean;
}

export default class MedEquationModal extends React.Component<
  IMedEquationModalProps, IMedEquationModalState> {

  // private equationCalcContainerRef; // container to render svg for measure

  constructor(props: IMedEquationModalProps) {
    super(props);

    // this.equationCalcContainerRef = React.createRef();

    this.state = {
      equationProperties: {
        name: '公式', // placeholder
        type: EquationType.Fraction,
      },
      showError: false,
    };
  }

  UNSAFE_componentWillReceiveProps(nextProps: IMedEquationModalProps) {

    const {documentCore} = nextProps;
    // When equation modal is about to open
    if (this.props.showEquationModal === false && nextProps.showEquationModal === true) {
      const equationProperties: IEquationProperties = JSON.parse(JSON.stringify(this.state.equationProperties));
      equationProperties.name = documentCore.makeUniqueImageName(ParaElementType.ParaMedEquation);
      this.setState({equationProperties});
    }

  }

  render() {

    const {name, type} = this.state.equationProperties;

    return (
      <React.Fragment>
        <div className="config-window equation-window">
            
            <div className="config-descriptor">
              医学表达式
            </div>
            <div className="detail-block">
              <label htmlFor="equation-name" className="left-cell">表达式名：</label>
              <input 
                id="equation-name" 
                name="equation-name" 
                className="right-cell" 
                value={name} 
                onChange={this.handleEquationPropertyChange}
              />
            </div>
            <div className="detail-block">
              <label htmlFor="equation-type" className="left-cell">表达式类别：</label>
              <select 
                id="equation-type" 
                name="equation-type" 
                className="right-cell" 
                value={type} 
                onChange={this.handleEquationPropertyChange} 
              >
                <option value={EquationType.Fraction}>数学分式</option>
                <option value={EquationType.Ordinary}>普通公式</option>
                <option value={EquationType.Menstruation}>月经史</option>
              </select>
            </div>
            <div className="detail-block">
              <label htmlFor="equation-preview" className="left-cell preview-label">示例：</label>
              <img 
                src={this.getPreviewedImage()} 
                className="right-cell preview-image" 
              />
            </div>

            <div 
              className="error-popup" 
              style={{display: (this.state.showError) ? "block" : "none"}}
            >"{name}" 名称重复.
            </div>

            <div className="button-container">
              <button className="button" onClick={this.handleButtonClick}>取消</button>
              <button 
                className="button" 
                onClick={this.handleButtonClick} 
                disabled={name.length > 0 ? false : true}
              >确定
              </button>
            </div>
            
            {/* container for revised equation to measure height/width */}
            {/* <div ref={this.equationCalcContainerRef} className="revised-equation-container" /> */}

          </div>
          
      </React.Fragment>
    );
  }

  private getPreviewedImage(): any {
    const {type} = this.state.equationProperties;
    let previewedImage = null;
    switch (type) {
      case EquationType.Fraction:
        // previewedImage = fractionEquationImage;
        previewedImage = FRACTION_EQUATION.source;
        break;
      case EquationType.Ordinary:
        // previewedImage = ordEquationImage;
        previewedImage = ORD_EQUATION.source;
        break;
      case EquationType.Menstruation:
        // previewedImage = menEquationImage;
        previewedImage = MEN_EQUATION.source;
        break;

      default:
        break;
    }
    return previewedImage;
  }

  private handleEquationPropertyChange = (e) => {
    const equationProperties: IEquationProperties = JSON.parse(JSON.stringify(this.state.equationProperties));

    switch (e.target.id) {
      case "equation-name":
        let showError: boolean = JSON.parse(JSON.stringify(this.state.showError));

        equationProperties.name = e.target.value;
        showError = false;

        this.setState({equationProperties, showError});
        break;

      case "equation-type":
        equationProperties.type = e.target.value * 1;

        this.setState({equationProperties});
        break;

      default:
        break;
    }
    
  }

  private handleButtonClick = (e) => {

    switch (e.target.innerHTML) {
      case "确定":
        const {documentCore} = this.props;

        // check if name already exists
        if (!documentCore.checkUniqueImageName(this.state.equationProperties.name)) {
          this.setState({showError: true});
          return null;
        }

        console.time("equation");

        let imageWidth = 50;
        const imageHeight = 50;
        // select right template ref based on first modal selection
        let currentRef = null;
        const {equationRefs} = this.props;
        switch (this.state.equationProperties.type) {
          case EquationType.Fraction:
            currentRef = equationRefs.fractionEquationRef.current;
            break;
    
          case EquationType.Ordinary:
            currentRef = equationRefs.ordEquationRef.current;
            break;
    
          case EquationType.Menstruation:
            currentRef = equationRefs.menEquationRef.current;
            imageWidth = 106;
            break;
    
          default:
            currentRef = equationRefs.fractionEquationRef.current;
            break;
        }
    
        const svgEquationElem = currentRef.cloneNode(true);

        // check if predefined width need to be squeezed
        const maxWidth = documentCore.getMaxWidth(true);
        if (imageWidth > maxWidth) {

          imageWidth = maxWidth;
          // width squeezed, svg elements need to squeeze accordingly?
          // this.updateEquationElemInnerWidth(imageWidth, svgEquationElem);
        }

        svgEquationElem.setAttribute('width', imageWidth);
        svgEquationElem.setAttribute('height', imageHeight);

        const svgConvertedURI = documentCore.getDrawingObjects().convertSVGToImageString(svgEquationElem);
        // console.log(svgConvertedURI);

        console.timeEnd("equation");
    
        // close modal
        this.closeModal();

        const {name, type} = this.state.equationProperties;
        console.log(svgEquationElem)
        this.props.addInlineImage(imageWidth, imageHeight, svgConvertedURI, name, type, svgEquationElem);
        break;

      default: // 取消
        // close modal
        this.closeModal();
        break;
    }
    
  }

  // private updateEquationElemInnerWidth(imageWidth: number, svgEquationElem: any) {

  //   const {documentCore} = this.props;

  //   const svgWidthIncreasedProps = {
  //     ordinaryEquation: {
  //       leftAmount: 0,
  //       rightAmount: 0,
  //     },
  //     fractionEquation: {
  //       amount: 0,
  //     },
  //     menEquation: {
  //       leftAmount: 0,
  //       middleAmount: 0,
  //       rightAmount: 0,
  //     },
  //     // unit: 80,
  //     fractionTextPadding: 110,
  //     ordinaryTextPadding: 35,
  //     menTextPadding: 20,
  //     menSVGPadding: 32,
  //   };

  //   // Calculate and set width of diff text parts in a realtime equation
  //   const updatedSVGWidthIncreasedProps = documentCore.getDrawingObjects()
  //   .setEquationTextPos(svgEquationElem, this.equationCalcContainerRef, svgWidthIncreasedProps,
  //                       this.state.equationProperties.type);

  //   // Spread out all types of equation-related info first
  //   const {ordinaryEquation, fractionEquation, menEquation, fractionTextPadding, ordinaryTextPadding, 
  //     menTextPadding, menSVGPadding} = updatedSVGWidthIncreasedProps;

  //   const textContainers = svgEquationElem.querySelectorAll("text");
  //   const lines = svgEquationElem.querySelectorAll("line");

  //   const newWidth = imageWidth;
  //   const newViewBoxWidth = newWidth * 6;
  //   const viewBox = "0 0 " + newViewBoxWidth + " 300";

  //   svgEquationElem.setAttribute("viewBox", viewBox);
  //   svgEquationElem.setAttribute("width", newWidth);

  //   let equationType = this.state.equationProperties.type;
  //   if (equationType === EquationType.Fraction) {
  //     lines[0].setAttribute("x2", newViewBoxWidth);
  //     textContainers[0].setAttribute("x", newViewBoxWidth / 2);
  //     textContainers[1].setAttribute("x", newViewBoxWidth / 2);

  //   } else if (equationType === EquationType.Ordinary) {

  //     const middlePos = ordinaryTextPadding + ordinaryEquation.leftAmount + ordinaryTextPadding;

  //     textContainers[0].setAttribute("x", ordinaryTextPadding + ordinaryEquation.leftAmount / 2);
  //     textContainers[2].setAttribute("x", ordinaryTextPadding + ordinaryEquation.leftAmount / 2);
  //     textContainers[1].setAttribute("x", middlePos + ordinaryTextPadding + ordinaryEquation.rightAmount / 2);
  //     textContainers[3].setAttribute("x", middlePos + ordinaryTextPadding + ordinaryEquation.rightAmount / 2);
      
  //     lines[0].setAttribute("x2", newViewBoxWidth);
  //     lines[1].setAttribute("x1", middlePos);
  //     lines[1].setAttribute("x2", middlePos);
    
  //   } else if (equationType === EquationType.Menstruation) {
  //     const lineStart = menSVGPadding + menEquation.leftAmount + menTextPadding;
  //     const lineEnd = lineStart + menEquation.middleAmount + menTextPadding;

  //     lines[0].setAttribute("x1", lineStart);
  //     lines[0].setAttribute("x2", lineEnd);

  //     textContainers[0].setAttribute("x", lineStart - menTextPadding);
  //     textContainers[1].setAttribute("x", (lineStart + lineEnd) / 2);
  //     textContainers[2].setAttribute("x", (lineStart + lineEnd) / 2);
  //     textContainers[3].setAttribute("x", lineEnd + menTextPadding);
  //   }
  // }

  private closeModal() {
    if (this.state.showError) {
      this.setState({showError: false});
    }
    this.props.closeModal("equation");
  }
  
}
