import { HistroyItemType, HistoryDescriptionType } from './HistoryDescription';

/**
 * 文档改变基类
 * // 对象： Document: ChangeDocumentAddItem | ChangeDocumentRemoveItem
 * //       Paragraph: ChangeParagraphAddItem | ChangeParagraphRemoveItem
 * //       Portion: ChangePortionAddItem | ChangePortionRemoveItem
 * //       Table
 * //       etc....
 */
export class ChangeBase {
    public changeClass: any = null;  // 发生变化的对象：Document, Paragraph, Portion, Table, etc....

    public bReverted: boolean;

    constructor(changeClass: any) {
        this.changeClass = changeClass;
        this.bReverted = false;
    }

    /**
     * 撤销操作
     */
    public undo(): void {
        if ( this.changeClass && this.changeClass.undo ) {
            this.changeClass.undo();
        }
    }

    /**
     * 重做操作
     */
    public redo(): void {
        if ( this.changeClass && this.changeClass.redo ) {
            this.changeClass.redo();
        }
    }

    /**
     * 获取改变的对象
     */
    public getClass(): any {
    return this.changeClass;
    }

    /**
     * 内容是否改变
     */
    public isContentChange(): boolean {
        return false;
    }

    public createReverseChange(): any {
        return null;
    }

    public refreshRecalData(): void {
        if ( this.changeClass && this.changeClass.refreshRecalData ) {
            this.changeClass.refreshRecalData(this);
        }
    }

    public isAdd(): boolean {
        return false;
    }

    public getItemsCount(): number {
        return 0;
    }

    public load(): void {
        this.redo();
    }

    public isDirty(): boolean {
        return false;
    }
}

/**
 * 改变文档的内容
 */
export class ChangeBaseContent extends ChangeBase {
    public position: number;  // 发生change所在的位置
    public items: any[] = null;
    public bUseArray: boolean;  // 与items相关，如果items是数组，则为 true
    public posArray: number|boolean [];  // 与items相关，如果items是数组，则保存的是position的数组
    public bAdd: boolean;  // 是否是添加操作
    public type: HistroyItemType|HistoryDescriptionType = null;

    constructor( changeClass: any, pos: number, items: any, bAdd: boolean ) {
        super(changeClass);

        this.position = pos;
        this.items = items;
        this.bUseArray = false;
        this.posArray = [];
        this.bAdd = bAdd;

        this.bReverted = false;
    }

    public isContentChange(): boolean {
        return true;
    }

    public isAdd(): boolean {
        return this.bAdd;
    }

    public getItemsCount(): number {
        return this.items.length;
    }

    public isParagraphSimpleChanges(): boolean {
        return false;
    }

    public getItemsContent(): string {
        let text = '';
        this.items.forEach((item) => {
            text += item.content;
        });

        return text;
    }

    public getMinPos(): number {
        let pos = null;
        if (this.bUseArray && this.posArray instanceof Array) {
            for (let index = 0, count = this.posArray.length; index < count ; index++) {
                if (null == pos || pos > this.posArray[index]) {
                    pos = this.posArray[index];
                }
            }

            if (null == pos) {
                pos = 0;
            }
        } else {
            pos = this.position;
        }

        return pos;
    }

    // public loadIncrementDatas(data: any): void {
    //     this.position = data.position;
    //     this.items = ;
    // }

    protected createReverseChangeBase( className: any ): any {
        const changeClass = new className();
        changeClass.changeClass = this.changeClass;
        changeClass.position = this.position;
        changeClass.items = this.items;
        changeClass.bUseArray = this.bUseArray;
        changeClass.posArray = [];
        changeClass.bAdd = !this.bAdd;

        return changeClass;
    }
}

/**
 * 改变文档内容的属性
 */
// tslint:disable-next-line: max-classes-per-file
export class ChangeBaseProperty extends ChangeBase {
    public color: boolean;  // ?
    public old: any; // 属性改变之前的值
    public new: any; // 属性改变之后的值

    constructor( changeClass: any, old: any, news: any, color: boolean) {
        super(changeClass);

        this.old = old;
        this.new = news;
        this.color = ( true === color ) ? true : false;
    }

    public undo(): void {
        this.setValue(this.old);
    }

    public redo(): void {
        this.setValue(this.new);
    }

    public setValue(value: any): void {
        //
    }

    public loadIncrementDatas(datas: any): void {
        // const data = datas.data;
        this.new = datas.new;
        this.old = datas.old;
        this.color = datas.color;
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeBaseBoolProperty extends ChangeBaseProperty {

    constructor( changeClass: any, old: any, news: any, color: boolean) {
        super(changeClass, old, news, color);
    }

    public write(type: any): any {
        return {
            type,
            changeClass: this.changeClass.write(),
            new: this.new,
            old: this.old,
            color: this.color,
        };
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeBaseLongProperty extends ChangeBaseProperty {

    constructor( changeClass: any, old: any, news: any, color?: boolean) {
        super(changeClass, old, news, color);
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeBaseStringProperty extends ChangeBaseProperty {

    constructor( changeClass: any, old: any, news: any, color?: boolean) {
        super(changeClass, old, news, color);
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeBaseDoubleProperty extends ChangeBaseProperty {

    constructor( changeClass: any, old: any, news: any, color?: boolean) {
        super(changeClass, old, news, color);
    }
}

/**
 * 用于更改对象属性的基类
 */
// tslint:disable-next-line: max-classes-per-file
export class ChangeBaseObjectProperty extends ChangeBaseProperty {
    constructor( changeClass: any, old: any, news: any, color: boolean) {
        super(changeClass, old, news, color);
    }
}

/**
 * HistoryPoint的属性成员
 */
// tslint:disable-next-line: max-classes-per-file
export class ChangeContent {
    public changeClass: any = null;  // 发生变化的对象：Document, Paragraph, Portion, Table, etc....
    public data: ChangeBaseContent = null;
    public bNeedRecal: boolean;

    constructor( changeClass: any, data: ChangeBaseContent | any, bNeedRecal: boolean ) {
        this.changeClass = changeClass;
        this.data = data;
        this.bNeedRecal = bNeedRecal;
    }
}
