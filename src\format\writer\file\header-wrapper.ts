import { XmlComponent } from './xml-components';

// import { HeaderReferenceType } from "./document";
import { Header } from './header/header';
// import { Image, IMediaData, Media } from "./media";
// import { ImageParagraph, Paragraph } from "./paragraph";
// import { Relationships } from "./relationships";
// import { Table } from "./table";

// export interface IDocumentHeader {
//     readonly header: HeaderWrapper;
//     readonly type: HeaderReferenceType;
// }

export class HeaderWrapper {
    private readonly header: Header;
    // private readonly relationships: Relationships;

    constructor(initContent?: XmlComponent) {
        this.header = new Header(initContent);
    }

    // docx has many methods below

    public get Header(): Header {
        return this.header;
    }

}
