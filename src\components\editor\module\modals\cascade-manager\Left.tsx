import * as React from 'react';
import {
    GlobalEvent as gEvent,
} from '../../../../../common/GlobalEvent';

interface IDialogProps {
    documentCore: any;
    host: any;
    data: {cascades: string[], events: string[]};
}

export default class LeftCascadeManagerUI extends React.Component<IDialogProps, {}> {
    private visible: boolean;
    private activeName: string;
    private bShowBackground: boolean;
    private datas: {cascades?: string[], events?: string[]};
    private host: any;

    constructor(props: any) {
        super(props);
        this.state = {
            bRefresh: false,
        };
        this.bShowBackground = false; // this.props.documentCore.isShowCascadeBackground();
        this.datas = props.data;
    }

    public render(): any {
        let className = '';
        let colorMapClassName = 'colorHidden';
        if (this.bShowBackground) {
            className = 'active';
            colorMapClassName = '';
        }
        return (
        <div className='cascade-left'>
            <div>
                {/* <span>元素导航</span> */}
                <div className='title-icon'>
                    <div className={className} onClick={this.titleOnClick}><i/></div>
                    <span>颜色标识</span>
                </div>
            </div>
            <div>
                <div id='colorMap' className={colorMapClassName}>
                    <div className='colorBlock' style={{backgroundColor: '#B6F5B1'}}></div>
                    <span>普通</span>
                    <div className='colorBlock' style={{backgroundColor: '#FFE0A3'}}></div>
                    <span>求和</span>
                    <div className='colorBlock' style={{backgroundColor: '#C7D3FF'}}></div>
                    <span>全有</span>
                </div>
            </div>
            <div className='title-active'>
                <i>&#62;</i>
                <span className='title' onClick={this.onClick.bind(this, 1)}>普通</span>
                {this.renderCascades()}
            </div>
            <div>
                <i>&#62;</i>
                <span  className='title' onClick={this.onClick.bind(this, 2)}>求和</span>
                {this.renderEvents()}
            </div>
        </div>
        );
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        this.datas = nextProps.data;
    }

    public getShowCascadeBackground(): boolean {
        return this.bShowBackground;
    }

    public refresh(name: string, bAdd: boolean = false): void {
        if (bAdd) {
            let datas = this.datas.cascades;
            const actName = name.slice(0, -1);
            if (!datas) {
                datas = [actName];
                this.datas.cascades = datas;
            } else {
                if (!datas.includes(actName)) {
                    datas.push(actName);
                }
            }
        }

        this.activeName = name;
        this.setState({});
    }

    public init(host: any): void {
        this.host = host;
    }

    public close = (): void => {
        this.bShowBackground = false;
        this.activeName = undefined;
        this.datas = {};
    }

    public add(cascadeName: string, eventName: string, type: number): void {
        if (cascadeName) {
            const cascades = this.datas.cascades;
            if (type === 2) {
                cascades.splice(cascades.indexOf(cascadeName), 1);
                this.activeName = null;
            } else {
                cascades.push(cascadeName);
                this.activeName = cascadeName + 1;
            }
        }

        if (eventName) {
            const events = this.datas.events;
            if (type === 2) {
                events.splice(events.indexOf(cascadeName), 1);
                this.activeName = null;
            } else {
                this.datas.events.push(eventName);
                this.activeName = eventName + 2;
            }
        }
        this.setCascadeBackground();
        this.setState({});
    }

    private onClick = (type: number, e: any): void => {
        const target = e.target.parentNode;
        const className = target.className;
        if (className) {
            target.className = '';
        } else {
            target.className = 'title-active';
        }
    }

    private titleOnClick = (e: any) => {
        const target = e.target;
        const className = target.className;
        if (className) {
            // target.className = '';
            this.bShowBackground = false;
            this.setBackground();
        } else {
            // target.className = 'active';
            this.bShowBackground = true;
            this.setCascadeBackground();
        }
        this.setState({});
        // this.props.showCascadeBackground(this.bShowBackground);
    }

    private setCascadeBackground(): void {
        const data = this.datas;
        const names = {};
        if (data.events) {
            data.events.forEach((name) => names[name] = 2);
        }
        if (data.cascades) {
            data.cascades.forEach((name) => {
                if (2 !== names[name]) {
                    names[name] = 1;
                } else {
                    names[name] += 1;
                }
            });
        }
        this.setBackground(names);
    }

    private setBackground = (names?: any): void => {
        if(this.host!=null)
            gEvent.setEvent(this.host.docId, 'resetCascadeManagerBg', names, this.bShowBackground);
    }

    private renderCascades(): any {
        const datas = this.datas?.cascades;
        if (!datas || datas.length === 0) {
            return null;
        }
        const activeName = this.activeName;
        const lis = datas.map((data, index) => {
            let className = '';
            const name = data + '1';
            if (activeName === name) {
                className = 'active';
            }
            return (
                <li key={data} data-name={name} title={data}>
                    <span className={className}>{data}</span>
                </li>
            );
        });

        return (<ul>{lis}</ul>);
    }

    private renderEvents(): any {
        const datas = this.datas?.events;
        if (!datas || datas.length === 0) {
            return null;
        }

        const activeName = this.activeName;
        const lis = datas.map((data, index) => {
            let className = '';
            const name = data + '2';
            if (activeName === name) {
                className = 'active';
            }
            return (
                <li key={data}  data-name={name}  title={data}>
                    <span className={className}>{data}</span>
                </li>
            );
        });

        return (<ul>{lis}</ul>);
    }
}
