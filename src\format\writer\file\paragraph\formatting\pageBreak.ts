import { XmlAttributeComponent, XmlComponent } from '../../xml-components';

export class PageBreak extends XmlComponent {
    constructor() {
        super('w:lastRenderedPageBreak');
    }
}

export class WesNewLineAttributes extends XmlAttributeComponent<{ readonly val: number }> {
    protected readonly xmlKeys: any = { val: 'w:val' };
}

// tslint:disable-next-line: max-classes-per-file
export class WordWrap extends XmlComponent {
    constructor(wordWrap: number) {
        super('w:wordWrap');
        this.root.push(new WesNewLineAttributes({ val: wordWrap }));
    }
}

// tslint:disable-next-line: max-classes-per-file
export class NisSignAuthorAttributes extends XmlAttributeComponent<{ readonly val: string }> {
    protected readonly xmlKeys: any = { val: 'w:val' };
}

// tslint:disable-next-line: max-classes-per-file
export class NisSignAuthor extends XmlComponent {
    constructor(nisSignAuthor: string) {
        super('w:nisSignAuthor');
        this.root.push(new NisSignAuthorAttributes({ val: nisSignAuthor }));
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ParaHidden extends XmlComponent {
    constructor() {
        super('w:hidden');
    }
}
