@import './global.less';

.hz-editor-container .editor-tableSetting {
    .editor-tabs ul > li {
        float: left;
        width: 80px;
        height: 32px;
        font-size: 14px;
        line-height: 32px;
        text-align: center;
        border: 1px solid #ebeef5;
        border-radius: 5px 5px 0 0;
        background: #f8f9fc;
        transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        margin-right: 6px;
        &:hover {
            background: #fff;
            border-color: @activeColor;
            transform: translateY(-1px);
            cursor: pointer;
        }

        /* Modified 激活状态重构 */
        &.active {
            background: @activeColor;
            color: #fff;
            border-color: @activeColor;
            box-shadow: 0 2px 6px rgba(@activeColor, 0.3)
        }
    }

    .editor-tabs-content {
        clear: both;
        padding-top: 10px;
        font-size: 14px;
        line-height: 1.5;

        .full-line {
            height: 30px;
            margin-bottom: 20px;
        }

        .full-line .table-label {
            display: inline-block;
            width: 80px;
            font-weight: bold;
            margin-right: 10px;
        }

        .full-line .table-inline-block {
            width: 150px;
            padding: 6px;
            border-radius: 4px;
            border: 1px solid #ddd;
        }
        
        .table-btn {
            padding: 6px 12px;
            border-radius: 4px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            background-color: #fff;
            border: 1px solid #ddd;
        }

        .table-btn:hover {
            background-color: #f9f9f9;
        }

        ul.table {
            & > li {
                display: none;
                margin-left: 15px;
            }
            & > li.active {
                display: block;
            }
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        table th,
        table td {
            border: 1px solid #ccc;
            padding: 10px;
            text-align: left;
        }

        table th {
            background-color: #f0f0f0;
            font-weight: bold;
            font-size: 16px;
            font-weight: 600;
        }
    }

    // ============== 仅修改边框线Tab页 ==============
    &.nis-table .editor-tabs-content {
        ul.table > li[class*="TableBorderLine"] {
            .new-full-line {
                margin: 16px 0;  // 原始20px→16px

                > div {
                    display: flex;  // 新增弹性布局
                    gap: 12px;     // 新增间距
                    flex-wrap: wrap;
                }
            }

            // 边框按钮
            .hz-table-button {
                /* Modified */
                min-width: 88px;      // 原始未定义→88px
                height: 36px;         // 原始未定义→36px
                padding: 0 12px;      // 原始未定义→12px
                border: 2px solid;    // 原始1px→2px
                border-radius: 6px;   // 原始未定义→6px
                font-weight: 500;     // 新增字重
                transition: all 0.2s; // 新增过渡

                // 彩色按钮
                &[style*="background-color:"] {
                    border-color: rgba(0,0,0,0.08) !important;
                    color: #fff;
                    position: relative;
                    
                    &::after {  // 新增渐变遮罩
                        content: "";
                        position: absolute;
                        inset: 0;
                        background: linear-gradient(135deg, 
                            rgba(255,255,255,0.15), 
                            rgba(0,0,0,0.05));
                    }

                    &:hover {
                        transform: translateY(-2px);
                        box-shadow: 0 4px 12px rgba(0,0,0,0.12);
                    }
                }

                // 默认按钮
                &:not([style*="background-color:"]) {
                    background: #fff;
                    border-color: #dcdfe6;
                    color: #606266;
                    
                    &:hover {
                        border-color: @activeColor;
                        color: @activeColor;
                    }
                }
            }

            // 颜色输入框
            input[type="string"] {
                /* Modified */
                width: 120px;        // 原始未定义→120px
                height: 36px;         // 原始未定义→36px
                border: 2px solid #e4e7ed;  // 原始1px→2px
                border-radius: 6px;   // 原始未定义→6px
                padding: 0 12px;      // 原始未定义→12px
                
                &:focus {
                    border-color: @activeColor;
                    box-shadow: 0 0 0 3px rgba(@activeColor, 0.1);
                }
            }

            // 下拉框
            select {
                /* Modified */
                height: 36px;         // 原始未定义→36px
                border: 2px solid #e4e7ed;  // 原始1px→2px
                border-radius: 6px;   // 原始未定义→6px
                padding: 0 12px;     // 原始未定义→12px
                appearance: none;     // 隐藏原生箭头
                background: url("data:image/svg+xml,...") no-repeat right 8px center/12px;
            }
        }

        // 以下是原始未修改代码
        .col-btns {
            text-align: right;
            font-size: 18px;
            vertical-align: middle;
            color: #ACB4C1;
            & > span {
                display: inline-block;
                width: 20px;
                height: 20px;
                margin-left: 8px;
                cursor: pointer;
                border: 1px solid @borderColor;
                border-radius: 2px;
            }
            & > :first-child::before {
                margin-right: 2px;
                content: "<";
            }
            & > :last-child::after {
                content: ">";
            }
        }
    }

    // 以下是原始未修改代码
    .table-label {
        display: inline-block;
        width: 90px;
        font-weight: bold;
    }

    .table-inline-block {
        display: inline-block;
        width: 120px;
    }

    .table-btn {
        width: 35px;
        margin-left: 5px;
        padding: 0px 3px;
        font-size: 12px;
        cursor: pointer;
        border: 1px solid;
        border-radius: 5px;
        box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.2);
    }

    .pick-color {
        display: none;
        position: absolute;
        z-index: 9;
        margin-top: 10px;

        &.active {
            display: block;
        }

        & > ul {
            max-height: 200px;
            padding: 12px 0;
            text-align: left;
            box-shadow: 0px 2px 20px 0px rgba(39, 53, 70, 0.2);
            border-radius: 4px;
            border: 1px solid rgba(221, 221, 227, 1);
            overflow: auto;
            background-color: #fff;

            & > li {
                height: 20px;
                line-height: 20px;
                padding: 0 23px;
                cursor: pointer;
                background-color: #fff;

                & > i {
                    display: none;
                }
            }
        }
    }
}