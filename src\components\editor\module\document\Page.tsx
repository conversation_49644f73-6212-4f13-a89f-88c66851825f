import * as React from 'react';
// import { PageProperty } from '../../../../model/core/Style';
import Content from './Content';
import { IDocumentContent, DocumentCore, IRefreshPageData } from '../../../../model/DocumentCore';
import { DocCurPosType } from '../../../../model/core/Document';
import { NewControlDefaultSetting, DocumentSectionType, ViewModeType, numtoFixed2,
    numtoFixed } from '../../../../common/commonDefines';
import { getTheme } from '@hz-editor/theme';
// import { g } from '../../../../common/ParseWater';
import { NewControlBackground } from './NewControlBackground';
import NISTableBase from './NISTableBase';
import { NewControlLayer } from './NewControlLayer';
import { AsyncLoad } from '../AsyncLoad';
/* IFTRUE_WATER */
import { getCode} from '@/common/MarkFactory';
import { RandomMarkDom } from './RandomMark';
/* FITRUE_WATER */
import { getPagePadding } from '@/common/commonMethods';
import { RegionBackgroundArea } from './RegionBackgroundArea';
import { SPELL_CHECK } from '@/common/Spellcheck';
import { RegionArea } from './RegionArea';
// import { NewControlCascadeBackground } from './NewControlCascadeBackground';
// import NewControlCascadeTipPanel from '../modals/NewControlCascadeTipPanel';
const components = {
    NewControlCascadeTipPanel: () => {
        return import('../modals/NewControlCascadeTipPanel');
    }
};
interface IPageProps { // } extends PageProperty {
    showPageBorder?: boolean; // 边界是否显示
    contents: IDocumentContent[];
    index: number;
    editorContainer?: any;
    documentCore?: DocumentCore;
    handleRefresh?: any;
    // curPosType: number;
    cursorType: string;
    host?: any;
    dynamicHeight?: number;
    minPageIndex?: number;
    _bCascadeManage?: boolean;
    paddingLeft?: number;
    paddingTop?: number;
    paddingRight?: number;
    width?: number;
    height?: number;
    paddingBottom?: number;
}

// interface IPageState {
//     // curPosType: number;
// }

// 假设边框长度20
const borderLength = 20;
export default class Page extends React.Component<IPageProps, {}> {
    private scale: number;
    private viewMode: ViewModeType;
    private _left: any;
    private _right: any;
    private _rightHeader: any;
    private _leftHeader: any;
    private _header: any;

    private _zIndex: number;
    private _minY: number;
    private _subHeaderY: number;
    private _scrollFn: any;
    private _pageHeight: number;
    private _bWebModel: boolean;

    private _contentRef: any;
    private _content: IDocumentContent;
    private documentCore: DocumentCore;
    private _newBgRef: any;
    private _regionBgRef: any;
    private _fixedTableRef: any;
    private _newControlLayer: any;
    private _svgRef: any;
    private _regionAreaRef: any;

    /* IFTRUE_WATER */
    private _strTmp: any[];
    private _curChange: number = -1;
    private _posIdx: number = 0;
    private _firstMark: any = null;
    private _tailMark: any = null;
    /* FITRUE_WATER */

    constructor(props: IPageProps) {
        super(props);
        this._contentRef = React.createRef();
        this.documentCore = props.documentCore;
        this._fixedTableRef = React.createRef();
        // console.log('Page---------------constructor----------------')
        // this.state = {
        //   curPosType: DocCurPosType.Content,
        // };
        this._svgRef = React.createRef();
        this._regionBgRef = React.createRef();
        this._regionAreaRef = React.createRef();
    }

    public render(): any {
        const { showPageBorder, index, contents, host, cursorType, documentCore, minPageIndex,
            paddingLeft } = this.props;
        const actWidth = this.props.width;
        const actHeight = this.props.height;
        this.scale = 1;
        const viewMode = this.viewMode = documentCore.getViewMode();
        const scale =  host.getScale();
        const padding = showPageBorder && !documentCore.isInlineMode() ? this.renderPageBorder() : null;
        const content = contents[0];
        // this._content = content;
        this.copyContent(content);

        if (documentCore.isInlineMode() && content && content.height) {
            // inline 模式下,content.height === Infinite
            content.height = documentCore.render().pageProperty.height;
        }

        // console.log(this._content)
        // console.log(content);
        // const sectionPageSize = this.props.documentCore.getSectionPageSize();
        let height: number;
        let width = content.width * scale;
        let viewBox: string;
        const bDynamicHeight = this.props.documentCore.getDynamicHeightMode();
        this._subHeaderY = 0;
        this._zIndex = 0;

        // 水印坐标限制
        const waterPos = {
            x: 0,
            y: 0,
            maxX: 0,
            maxY: 0
        };

        if (viewMode === ViewModeType.WebView) {
            this._bWebModel = true;
            const page = this.props.documentCore.getPagePositionInfo(index);
            const pageHeight = content.height - page.y - (page.height - page.yLimit);
            height = pageHeight * scale;
            let finalHeight = pageHeight;

            if (bDynamicHeight === true && this.props.dynamicHeight > 0) {
                finalHeight = this.props.dynamicHeight;
                height = finalHeight * scale;
            }
            this._subHeaderY = page.y;
            // web视图下水印坐标(正文)
            waterPos.maxX = width;
            waterPos.y = page.y;
            waterPos.maxY = height;
            // viewBox = `0 ${page.y} ${Math.round(content.width)} ${Math.round(pageHeight)}`;
            viewBox = `0 ${page.y} ${Math.round(content.width)} ${Math.round(finalHeight)}`;
        } else if (viewMode === ViewModeType.CompactView) {
            width = Math.round(actWidth * scale);
            height = Math.round(actHeight * scale);
            // viewBox = `0 0 ${Math.round(content.width)} ${Math.round(content.height)}`;
            const {bottomLeft, topLeft} = this.getHeaderContentTrulyPosition();
            waterPos.x = paddingLeft - 1;
            waterPos.maxX = Math.round(actWidth);
            // footer
            waterPos.y = bottomLeft.y;
            waterPos.maxY = actHeight;
            // header
            // waterPos.maxY = topLeft.y;
            viewBox = `${Math.max(paddingLeft - getPagePadding.CompactWidth, 0)} ${0} ${Math.round(actWidth)} ${Math.round(actHeight)}`;
        } else {
            this._bWebModel = false;
            height = content.height * scale;
            let finalHeight = content.height;
            if (bDynamicHeight === true && this.props.dynamicHeight > 0) {
                finalHeight = this.props.dynamicHeight;
                height = finalHeight * scale;
            }
            const {bottomLeft, topLeft} = this.getHeaderContentTrulyPosition();
            waterPos.maxX = Math.round(content.width);
            // footer
            waterPos.y = bottomLeft.y;
            waterPos.maxY = finalHeight;
            // header
            // waterPos.maxY = topLeft.y;
            // viewBox = `0 0 ${Math.round(content.width)} ${Math.round(content.height)}`;
            viewBox = `0 0 ${Math.round(content.width)} ${Math.round(finalHeight)}`;
        }

        width = Math.round(width);
        height = Math.round(height);

        const contentArea = {
            pageWidth: width,
            pageHeight: height,
            y: waterPos.maxY,
            maxY: height,
        };
        const headerFooterDomNode = this.renderHeaderFooter(contentArea);
        // footer
        waterPos.y = contentArea.maxY;
        // header
        // waterPos.maxY = contentArea.y;

        // 绘制标准水印

        // console.log(showPageBorder, content);
        const theme = getTheme();
        this._pageHeight = height;
        // const newTableContent = this.setFixedTableContent(contents, width, height, viewBox, cursorType, index);
        const newTableContent = (
        <NISTableBase
            ref={this._fixedTableRef}
            height={height}
            width={width}
            content={contents[0]}
            viewBox={viewBox}
            cursorType={cursorType}
            host={host}
            documentCore={documentCore}
            minPageIndex={minPageIndex}
            index={index}
            webModel={this._bWebModel}
            scale={scale}
        />
        );
        /* IFTRUE_WATER */
        this._posIdx = 0;
        const bchange = documentCore.corePositionChange();
        if (!documentCore.getCoreBGen() || this.viewMode === ViewModeType.WebView) {
            this._curChange = -1;
            this._strTmp = null;
            this._firstMark = null;
            this._tailMark = null;
        } else if (this._curChange !== bchange || !this._strTmp || !this._strTmp.length) {
            this._curChange = bchange;
            this._strTmp = [
                documentCore.getCorePosition(false, true, waterPos)
            ];
            const tmpMark = this._strTmp[this._posIdx++];
            this._firstMark = <RandomMarkDom strTmp={tmpMark} />;
            this._tailMark = <RandomMarkDom coreGen={bchange} documentCore={documentCore} release={true} />;
        } else {
            this._posIdx++;
        }
        /* FITRUE_WATER */
        return (
            <React.Fragment>
                {this.renderNewControlCascadePanel()}
                <svg
                    width={width}
                    height={height}
                    page-id={index}
                    viewBox={viewBox}
                    type='page'
                    className='page-container'
                    cursor={cursorType}
                    id={'s' + index}
                    preserveAspectRatio={'xMaxYMax slice'}
                    ref={this._svgRef}
                >
                    {showPageBorder && padding}
                    <RegionArea content={content} index={this.props.index} ref={this._regionAreaRef}/>
                    {/*IFTRUE_WATER this._firstMark FITRUE_WATER*/}
                    <RegionBackgroundArea
                        documentCore={documentCore}
                        index={index}
                        ref={this._regionBgRef}
                    >
                        {this.renderNewControlBackground()}
                        {headerFooterDomNode}
                        {content && this.renderContent()}
                        {this.renderTableNewBorder()}
                    </RegionBackgroundArea>
                    {/*IFTRUE_WATER this._tailMark FITRUE_WATER*/}
                    <g className='region-border' stroke={theme.NewControl.DefaultRegionBorderColor}/>
                </svg>
                {newTableContent}
                {this.renderNewControlLayer()}
            </React.Fragment>
        );
    }

    public refresh(options: any = {}): any {
        const ref = this._contentRef.current;
        if (!ref) {
            return;
        }
        const index = this.props.index;
        this.refreshRegionBg();
        this.refreshNewContrlBg();
        const documentCore = this.documentCore;
        if (options.bPrevPage === true && options.ids) {
            // 更新前一页的段落或者单元格
            const curData = documentCore.getNextRefreshPageContent(index, options);
            return this.triggerRefresh(curData, false);
        }

        if (options.bNextPage === true) {
            options.bNextPage = undefined;
            let curData: any;
            if (options.ids) {
                curData = documentCore.getNextRefreshPageContent(index, options);
                return this.triggerRefresh(curData, false);
            }

            curData = documentCore.getContentByPageId(index);
            // this._content = curData;
            this.copyContent(curData);
            // ref.refresh({bNextPage: true, content: curData, bPageContent: true});
            this.triggerRefresh({bNextPage: true, content: curData, bPageContent: true}, true);
            return {bNextPage: true};
        }

        let data: IRefreshPageData = this.documentCore.getRefreshPageContent(index, this._content);
        if (data && data.bRefreshAll) { // 刷新所有的
            return data;
        }
        let bPageContent: boolean = false;
        if (data && !data.content) { // 刷新当前页及以下，有可能需要刷新上一页的当前段落
            if (documentCore.isInlineMode()) {
                // inline 模式下，段落下边距变化，则会刷新当前页
                return {
                    bRefreshAll: true
                };
            }
            data.content = documentCore.getContentByPageId(index);
            this.copyContent(data.content);
            data.bPageContent = true;
            if (data.bPrevPage !== true) {
                bPageContent = true;
            }
        } else if (!data) { // 直接刷新当前页及后面页
            const curData = documentCore.getContentByPageId(index);
            this.copyContent(curData);
            data = {
                bNextPage: true,
                ids: undefined,
                type: undefined,
                content: curData,
                bPageContent: true,
            };
            bPageContent = true;
        }
        // const res = ref.refresh(data);
        const res = this.triggerRefresh(data, bPageContent);
        this.refreshNewContrlBg();
        this.refreshNewControlLayer();
        if (bPageContent === true && data.bPrevPage !== true) {
            return {
                bNextPage: true,
            };
        }
        if (data.bNextPage === true || data.bPrevPage === true) {
            data.bPageContent = undefined;
            return data;
        }
        return res;
    }

    public componentWillUnmount(): void {
        this._contentRef = null;
        this._fixedTableRef = null;
        // console.log('Page---------------constructor----------------')
        // this.state = {
        //   curPosType: DocCurPosType.Content,
        // };
        this._svgRef = null;
        this._regionBgRef = null;
        this._regionAreaRef = null;
        this._content = null;
    }

    private triggerRefresh(options: IRefreshPageData, bPage: boolean = false): any {
        const fixedTableRef = this._fixedTableRef?.current;
        const ref = this._contentRef.current;
        let res;
        const pageIndex = this.props.index;
        SPELL_CHECK.setLocalDatas(pageIndex);
        if (bPage) {
            if (this._regionAreaRef.current) {
                this._regionAreaRef.current.refresh(options);
            }
            if (fixedTableRef) {
                fixedTableRef.refresh(options);
            }

            if (ref) {
                res = ref.refresh(options);
            }
            SPELL_CHECK.renderPage(pageIndex);
            return res;
        }

        if (options.bFixedContent) {
            if (fixedTableRef) {
                res = fixedTableRef.refresh(options);
            }
            SPELL_CHECK.renderPage(pageIndex);
            return res;
        }

        return ref.refresh(options);
    }

    private copyContent(content: any): void {
        this._content = content;
        return;
        if (!content) {
            this._content = content;
            return;
        }

        const tables = content.tables;
        if (!tables || tables.length !== 1) {
            this._content = content;
            return;
        }

        const table = tables[0];
        if (table.bFixedHeader !== true && !table.fixedLeft && !table.fixedRight || table.tableCells.length < 1) {
            this._content = content;
            return;
        }

        this._content = {...content};
        this._content.tables = [].concat(tables);
        this._content.tables[0] = {...table};
        (this._content.tables[0] as any).tableCells = [].concat(table.tableCells);
    }

    /**
     * 设置表格的冻结列
     * @param contents 表格内容
     * @param width 宽度
     * @param height 高度
     * @param viewBox 画布
     * @param cursorType 光标类型
     * @param index 页索引
     * @returns 冻结内容
     */
    private setFixedTableContent(contents: any[], width: number, height: number, viewBox: string,
                                 cursorType: string, index: number): any {
        const newData = this.getFixedCell(contents[0]);
        let newTableContent: any[];
        if (newData) {
            // console.log(newData);
            newTableContent = [];
            const options: any = {width, height, viewBox, cursorType, index, name: undefined, left: undefined};
            options.name = 'right';
            const position = this.getRightFixedPosition(height);
            const curLeft = options.left = position.x2;
            newTableContent.push(this.renderFixedSvg(newData.rightTables, options));
            options.name = 'left';
            options.left = position.x1;
            newTableContent.push(this.renderFixedSvg(newData.tables, options));
            options.name = 'header';
            options.left = undefined;
            options.top = position.y1;
            newTableContent.push(this.renderFixedSvg(newData.headerTables, options));
            options.left = curLeft;
            options.name = 'rightHeader';
            newTableContent.push(this.renderFixedSvg(newData.rightHeaderTables, options));
            options.name = 'leftHeader';
            options.left = position.x1;
            newTableContent.push(this.renderFixedSvg(newData.leftHeaderTables, options));
        }
        return newTableContent;
    }

    private renderFixedSvg(tables: any, options: any): any {
        if (!tables || tables.length === 0) {
            return;
        }
        const{width, height, index, cursorType, name, top} = options;
        let viewBox = options.viewBox;
        const tableOption = tables[0].option;
        let svgWidth = width;
        let svgHeight = height;
        if (tableOption) {
            const maxWidth = tableOption.maxWidth;
            const maxHeight = tableOption.maxHeight;
            const viewBoxs = viewBox.split(' ');
            // viewBoxs[0] = position.x;
            svgWidth = viewBoxs[2] = maxWidth;
            if (maxHeight) {
                svgHeight = viewBoxs[3] = maxHeight;
            }
            viewBox = viewBoxs.join(' ');
        }

        const newTables = this.renderFixedCellContent({tables, paragraphs: [], name});
        const styles: any = {position: 'absolute', left: options.left, top: top || 0,
        marginTop: 0, zIndex: ++this._zIndex};
        // const mask = this.createMask(tableOption, name);
        // let url;
        // if (mask) {
        //     url = `url(#mask-${name})`;
        // }

        return (
            <svg
                style={styles}
                ref={this['_' + name]}
                width={svgWidth}
                height={svgHeight}
                page-id={index}
                viewBox={viewBox}
                type='page'
                name={name}
                key={name + index}
                className='page-container'
                cursor={cursorType}
            >
                {newTables}
            </svg>
        );
    }

    private createMask(option: any, name: string): any {
        let height: any;
        switch (name) {
            case 'rightHeader': {
                height = option.maxHeight;
                break;
            }
            case 'right': {
                height = option.height;
                break;
            }
            default: {
                return;
            }
        }

        return (
            <defs>
                <clipPath id={'mask-' + name}>
                    <rect
                        x={option.minX - 1}
                        y={option.y}
                        width={option.maxWidth - option.minX + 1}
                        height={height + 1}
                        fill={'#fff'}
                    />
                </clipPath>
            </defs>
        );
    }

    private getRightFixedPosition(height: number): any {
        const obj = {x1: 0, x2: 0, y1: 0};
        const dom = this.props.host.currentRef.current;
        if (!dom) {
            return obj;
        }
        const first = dom.firstChild;
        if (!first) {
            return obj;
        }

        const { pageProperty: page } = this.props.documentCore.render();
        const scrollLeft = first.scrollLeft;
        const scrollTop = first.scrollTop;
        const rightMaxValue = dom.clientWidth - page.width + page.paddingRight - 16;
        let x2 = scrollLeft + rightMaxValue;
        if (x2 > 0) {
            x2 = 0;
        }

        const minValue = page.paddingLeft;
        let x1 = scrollLeft - minValue;
        if (x1 < 0) {
            x1 = 0;
        }
        obj.x1 = x1;
        obj.x2 = x2;

        if (this._minY !== undefined) {
            const minY = this._minY - this._subHeaderY;

            let subTop = scrollTop - minY;
            const minPageIndex = this.props.minPageIndex;
            if (minPageIndex !== 0) {
                subTop -= minPageIndex * height;
            }
            if (subTop < minY) {
                subTop = 0;
            }
            obj.y1 = subTop;
        }

        // this._clientWidth = dom.clientWidth;
        // const subWidth = this._clientWidth - page.width - scrollLeft;

        return obj; // subWidth + page.paddingRight;
    }
    private renderFixedCellContent(tables: any): any {
        const {host, documentCore, handleRefresh, editorContainer, index} = this.props;

        return (
            <Content
                content={tables}
                pageIndex={index}
                documentCore={documentCore}
                host={host}
                url={tables.url}
                key={tables.name + index}
                editorContainer={editorContainer}
                handleRefresh={handleRefresh}
                // bFromHeaderFooter={false}
            />
        );
    }

    /**
     * 头部冻结列
     * @param lines 列
     * @param cells 单元格
     * @param option 其他参数
     * @returns 新的列
     */
    private getFixedLine1(lines: any[], cells: any[], option: any): any[] {
        if (!lines || lines.length === 0) {
            return [];
        }

        let minY: number = 99999999;
        let maxY: number = 0;
        let minX = 99999999;
        let maxX = 0;
        // let minX2: number = 99999999;
        // let maxX2: number = 0;
        // const leftCells = other.left;
        // const rightCells = other.right;
        if (cells && cells.length > 0) {
            let maxWidth = 0;
            cells.forEach((cell) => {
                const cutY1 = cell.y;
                const cutY2 = cutY1 + cell.height;

                if (cutY1 < minY) {
                    minY = cutY1;
                }

                if (cutY2 > maxY) {
                    maxY = cutY2;
                }

                const cutX1 = cell.x;
                const cutX2 = cell.width + cutX1;
                if (cutX1 < minX) {
                    minX = cutX1;
                }

                if (cutX2 > maxWidth) {
                    maxX = maxWidth = cutX2;
                }
            });
            option.maxY = maxY;
            option.maxHeight = maxY;
            option.maxWidth = maxWidth;
            option.minX = minX;
            option.y = minY - 1;
            this._minY = minY;
            maxY++;
            minX--;
            maxX++;
        } else {
            return;
        }
        // if (rightCells && rightCells.length > 0) {
        //     rightCells.forEach((cell) => {
        //         const cutX1 = cell.x;
        //         const cutX2 = cell.width + cutX1;
        //         if (cutX1 < minX2) {
        //             minX2 = cutX1;
        //         }
        //         if (cutX2 > maxX2) {
        //             maxX2 = cutX2;
        //         }
        //     });
        //     minX2--;
        //     maxX2++;
        // }

        const len = lines.length - 1;
        // let line = lines[len];
        // const x1 = line.x1 + '';
        // const x2 = line.x2 + '';
        // const xs = [x1, x2];
        const newLines = [];
        // line = lines[len - 4];
        // const x3 = line.x2 + '';
        // const rightLines = [];
        // const leftLines = [];
        const type = option.type;
        let bChanged = false;
        for (let index = len; index >= 0; index--) {
            const curLine = lines[index];
            const y = curLine.y2;
            if (y > maxY) {
                continue;
            }

            const actX1 = curLine.x1;
            const actX2 = curLine.x2;
            const subX1 = actX1 - minX;
            const subX2 = maxX - actX2;
            if (subX1 > 0 && subX2 > 0) {
                newLines.push(curLine);
                lines.splice(index, 1);
                // 左冻结列的右边需要阴影
                if (type === 'leftHeader' && subX2 < 1.1 && actX2 - actX1 < 0.5) {
                    curLine.bLeftLineGap = true;
                    if (bChanged === false) {
                        bChanged = true;
                        option.maxWidth += 8;
                    }
                // 右冻结列的左侧需要阴影
                } else if (type === 'rightHeader' && subX1 < 1.1 && actX2 - actX1 < 0.5) {
                    curLine.bRightLineGap = true;
                }

                if (maxY - y < 1.5 && maxY - curLine.y1 < 1.5) {
                    curLine.className = ' header-bottom';
                }
            }

            // newLines.push(curLine);
            // lines.splice(index, 1);
        }

        return newLines;
    }

    private getFixedLine(lines: any[], cells: any[], option: any): any[] {
        if (!lines || lines.length === 0) {
            return [];
        }

        let minX1: number = 99999999;
        let maxX1: number = 0;
        // let minX2: number = 99999999;
        // let maxX2: number = 0;
        // const leftCells = other.left;
        // const rightCells = other.right;
        let minY: number = 99999999;
        let maxHeight: number = 0;
        if (cells && cells.length > 0) {
            cells.forEach((cell) => {
                const cutX1 = cell.x;
                const cutY1 = cell.y;
                const cutY2 = cutY1 + cell.height;
                const cutX2 = cell.width + cutX1;
                if (cutX1 < minX1) {
                    minX1 = cutX1;
                }
                if (cutX2 > maxX1) {
                    maxX1 = cutX2;
                }
                if (cutY1 < minY) {
                    minY = cutY1;
                }
                const height = cutY2 - minY;
                if (height > maxHeight) {
                    maxHeight = height;
                }
            });
            option.minX = minX1;
            option.maxWidth = maxX1;
            option.height = maxHeight + 1;
            option.y = minY;
            minX1--;
            maxX1++;
        }
        // if (rightCells && rightCells.length > 0) {
        //     rightCells.forEach((cell) => {
        //         const cutX1 = cell.x;
        //         const cutX2 = cell.width + cutX1;
        //         if (cutX1 < minX2) {
        //             minX2 = cutX1;
        //         }
        //         if (cutX2 > maxX2) {
        //             maxX2 = cutX2;
        //         }
        //     });
        //     minX2--;
        //     maxX2++;
        // }

        const len = lines.length - 1;
        // let line = lines[len];
        // const x1 = line.x1 + '';
        // const x2 = line.x2 + '';
        // const xs = [x1, x2];
        const newLines = [];
        // line = lines[len - 4];
        // const x3 = line.x2 + '';
        // const rightLines = [];
        // const leftLines = [];
        const type = option.type;
        let bChanged = false;
        for (let index = len; index >= 0; index--) {
            const curLine = lines[index];
            const actX1 = curLine.x1;
            const actX2 = curLine.x2;
            const subX2 = maxX1 - actX2;
            const subX1 = actX1 - minX1;
            if (subX1 > 0 && subX2 > 0) {
                newLines.push(curLine);
                lines.splice(index, 1);
                // 左冻结列的右边需要阴影
                if (type === 'left' && subX2 < 1.1 && actX2 - actX1 < 0.5) {
                    curLine.bLeftLineGap = true;
                    if (bChanged === false) {
                        bChanged = true;
                        option.maxWidth += 8;
                    }
                // 右冻结列的左侧需要阴影
                } else if (type === 'right' && subX1 < 1.1 && actX2 - actX1 < 0.5) {
                    curLine.bRightLineGap = true;
                }
            }
        }

        return newLines;
    }

    private getFixedCell(data: any): any {
        const tables = data.tables;
        if (!tables || tables.length === 0) {
            return;
        }
        this._minY = undefined;
        // console.log(tables);
        const leftHeaderTables = [];
        const rightHeaderTables = [];
        let headerTables = [];
        const leftTables = [];
        const rightTables = [];
        tables.forEach((table) => {
            const fixedLeft = table.fixedLeft || 0;
            const fixedRight = table.fixedRight || 0;
            if (fixedLeft === 0 && fixedRight === 0 && !table.bFixedHeader) {
                return;
            }

            const cells = table.tableCells;
            let newCells = [];
            let x1: number = 0;
            let x2: number = 999999999;
            if (fixedLeft > 0 && cells[fixedLeft - 1]) {
                x1 = cells[fixedLeft - 1].x + 0.5;
            }
            if (fixedRight > 0 && cells[cells.length - fixedRight]) {
                x2 = cells[cells.length - fixedRight].x - 0.5;
            }
            newCells = [];
            const cells1 = [];
            const cells2 = [];
            let bHeader: boolean = false;
            const bFixedHeader = table.bFixedHeader;
            const bHasHeader = headerTables.length > 0;
            for (let index = cells.length - 1; index >= 0; index--) {
                const cell = cells[index];
                const x = cell.x;
                if (bFixedHeader && cell.bHeader === true && !bHasHeader) {
                    bHeader = true;
                    headerTables.unshift(cell);
                    cells.splice(index, 1);
                } else if (x < x1) {
                    newCells.push(cell);
                    cells.splice(index, 1);
                    cells1.unshift(cell);
                } else if (x > x2) {
                    newCells.push(cell);
                    cells2.unshift(cell);
                    cells.splice(index, 1);
                }
            }

            let actTable: any = table;
            if (bHeader === false && !bHasHeader && table.bFixedHeader &&
                this.props.minPageIndex === this.props.index) {
                const newHeaders = this.props.documentCore.getTableHeaderContent(table.id);
                if (newHeaders) {
                    headerTables = newHeaders.tableCells;
                    bHeader = true;
                    actTable = newHeaders;
                }
            }

            if (bHeader) {
                const lTables = [];
                const rTables = [];
                for (let headerIndex = headerTables.length - 1; headerIndex >= 0; headerIndex--) {
                    const cell = headerTables[headerIndex];
                    const x = cell.x;
                    if (x < x1) {
                        lTables.unshift(cell);
                        headerTables.splice(headerIndex, 1);
                    } else if (x > x2) {
                        rTables.unshift(cell);
                        headerTables.splice(headerIndex, 1);
                    }
                }
                let current: any;
                // 头部左边冻结列
                current = this.addFixedTables(lTables, actTable, 'leftHeader');
                if (current) {
                    leftHeaderTables.push(current);
                }
                // 头部右边冻结列
                current = this.addFixedTables(rTables, actTable, 'rightHeader');
                if (current) {
                    rightHeaderTables.push(current);
                }
                // 头部冻结列
                current = this.addFixedTables(headerTables, actTable, 'header');
                headerTables = [];
                if (current) {
                    headerTables.push(current);
                }
            }
            // 左边冻结列
            let item = this.addFixedTables(cells1, table, 'left');
            if (item) {
                leftTables.push(item);
            }

            // 右边冻结列
            item = this.addFixedTables(cells2, table, 'right');
            // this.updateRightCellX(item.tableCells, item.tableBorderLines);
            if (item) {
                rightTables.push(item);
            }

            // 左右列
            // let other = {left: cells1, right: cells2, rightLines: undefined};
            // const lines = this.getFixedLine(table.tableBorderLines, cells1);
            // asideTables.push({tableBorderLines: lines, tableCells: newCells,
            //     tableBackground: [], other});
            // this.updateRightCellX(other.right, other.rightLines);
        });
        if (!leftTables.length && !rightTables.length && !headerTables.length && !leftHeaderTables.length
            && !rightHeaderTables.length) {
            return;
        }
        return {tables: leftTables, headerTables, leftHeaderTables, rightHeaderTables, rightTables};
    }

    private addFixedTables(cells: any[], table: any, type: string): any {
        if (cells.length === 0) {
            return;
        }
        let lastLines;
        const option = {
            type,
        };
        switch (type) {
            case 'right':
            case 'left': {
                lastLines = this.getFixedLine(table.tableBorderLines, cells, option);
                break;
            }
            case 'leftHeader':
            case 'rightHeader':
            case 'header': {
                lastLines = this.getFixedLine1(table.tableBorderLines, cells, option);
                break;
            }
            default: {
                return;
            }
        }

        return {tableBorderLines: lastLines, tableCells: cells, tableBackground: [], option};
    }

    // private updateRightCellX(cells: any[], lines: any[]): void {
    //     if (!cells || cells.length === 0) {
    //         return;
    //     }
    //     let clientWidth: number;
    //     if (this._dx === undefined) {
    //         const page = this.props.documentCore.getPageProperty();
    //         if (!dom) {
    //             return;
    //         }
    //         clientWidth = dom.clientWidth - 18;
    //         let subWidth = clientWidth - page.width;
    //         if (subWidth > -10) {
    //             return;
    //         }

    //         subWidth  += page.paddingRight;
    //         if (subWidth > -10) {
    //             return;
    //         }
    //         this._dx = clientWidth;
    //     } else {
    //         clientWidth = this._dx;
    //     }

    //     let current: any;
    //     let max: number = 0;
    //     cells.forEach((cell) => {
    //         if (cell.x > max) {
    //             max = cell.x;
    //             current = cell;
    //         }
    //     });
    //     const dx = current.x + current.width - clientWidth;
    //     cells.forEach((cell) => {
    //         cell.x -= dx;
    //     });
    //     lines.forEach((line) => {
    //         line.x1 -= dx;
    //         line.x2 -= dx;
    //     });
    // }

    private refreshNewContrlBg(): void {
        const ref = this._newBgRef;
        if (!ref || !ref.current) {
            return;
        }
        ref.current.refresh();
    }
    private refreshRegionBg(): void {
        const ref = this._regionBgRef;
        if (!ref || !ref.current) {
            return;
        }
        ref.current.refresh();
    }

    /**
     * get width of content
     */
    // private getTrulyWidth(): any {
    //     const { width, height, paddingRight, paddingLeft } = this.props;
    //     return width - paddingLeft - paddingRight;
    // }

    // /**
    //  * get height of content
    //  */
    // private getTrulyHeight(): any {
    //     const { height, paddingTop, paddingBottom } = this.props;
    //     return height - paddingTop - paddingBottom;
    // }

    // Header/footer observation
    // A header/footer's size/position should only resort from SectionPageMargins, not PageProperty.
    // Rendering should easily use those props prepared from recalculate().
    // If still need complex calculation in this file, sth is off
    // |_(|-) and _|(-|) are the indicators of page content section.
    // They have nothing to do with header/footer's size/position calculation.
    // But If header/footer area overlaps them, need to deal with the ui overlapping. (*1)
    // Page content's start pos is decided from Max(PageProperty.paddingTop, SectionPageMargins.paddingTop)
    // i.e, the bigger height between "header's height" and 页上边距
    // likewise as of Page content's end pos -> Max("footer's height", 页下边距)
    // if ("header's height" > 页上边距), refer to (*1) line. At that time, |- and -| are meaningless
    // likewise as of |_ and _| at page bottom.

    private getHeaderFromTop(): number {
        const {documentCore} = this.props;
        return documentCore.getPageMarginsHeader();
    }

    private getFooterFromBottom(): number {
        const {documentCore} = this.props;
        return documentCore.getPageMarginsFooter();
    }

    /**
     * get footer CONTENT area's height (static, literally, not dynamic)
     */
    private getFooterContentTrulyHeight(): number {
        const sectionPageMargins = this.props.documentCore.getSectionPageMargins();
        const footerFromBottom = this.getFooterFromBottom();

        return sectionPageMargins.paddingBottom - footerFromBottom;
    }

    private getRectPosition(): any {
        const props = this.props;
        const scale = this.scale;
        if (scale > 0.8) {
            return props;
        }
        const obj: any = {};
        // pageProperty!
        ['paddingRight', 'paddingLeft', 'paddingTop', 'paddingBottom']
        .forEach((key) => {
            const value = props[key];
            if (typeof value === 'number') {
                obj[key] = value * scale;
            }
        });
        obj.width = props.contents[0].width;
        obj.height = props.contents[0].height;
        return obj;
    }

    /**
     * get position of content
     */
    private getTrulyPosition(): any {
        // pageProperty!
        const {
            width,
            height,
            paddingRight,
            paddingLeft,
            paddingTop,
            paddingBottom,
        } = this.getRectPosition();
        return {
            topLeft: {
                x: paddingLeft,
                y: paddingTop,
            },
            topRight: {
                x: width - paddingRight,
                y: paddingTop,
            },
            bottomLeft: {
                x: paddingLeft,
                y: height - paddingBottom,
            },
            bottomRight: {
                x: width - paddingRight,
                y: height - paddingBottom,
            },
        };
    }

    /**
     * get position of header content
     */
    private getHeaderContentTrulyPosition(): any {
        const sectionPageSize = this.props.documentCore.getSectionPageSize();
        const sectionPageMargins = this.props.documentCore.getSectionPageMargins();
        const {width} = sectionPageSize;
        const {paddingLeft, paddingRight, header: headerFromTop} = sectionPageMargins;
        let paddingTop = sectionPageMargins.paddingTop;
        if (this.viewMode === ViewModeType.WebView) {
            paddingTop = 0;
        }
        return {
            topLeft: {
                x: paddingLeft,
                y: headerFromTop,
            },
            topRight: {
                x: width - paddingRight,
                y: headerFromTop,
            },
            bottomLeft: {
                x: paddingLeft,
                y: paddingTop,
            },
            bottomRight: {
                x: width - paddingRight,
                y: paddingTop,
            },
        };
    }

    /**
     * get position of footer content
     */
    private getFooterContentTrulyPosition(): any {
        const { documentCore } = this.props;
        const {
            height,
            width,
            paddingBottom,
            paddingLeft,
            paddingRight
        } = documentCore.render().pageProperty;
        const {footer: footerFromBottom} = documentCore.getSectionPageMargins();

        return {
            topLeft: {
                x: paddingLeft,
                y: height - paddingBottom,
            },
            topRight: {
                x: width - paddingRight,
                y: height - paddingBottom,
            },
            bottomLeft: {
                x: paddingLeft,
                y: height - footerFromBottom,
            },
            bottomRight: {
                x: width - paddingRight,
                y: height - footerFromBottom,
            },
        };
    }

    private renderHeaderFooter(contentArea: any = {}): any {
        if (this.viewMode === ViewModeType.WebView) {
            return null;
        }
        const { documentCore, index } = this.props;
        const logicDocument = documentCore.getDocument();
        const page = documentCore.getHdrFtr().pages[index];
        const scale = this.scale;
        const width = numtoFixed2(this.props.contents[0].width * scale);

        // ------------------------------------------  build header content
        const header = (page != null) ? page.header : null;
        const {
            topLeft: { x: topLeftX, y: topLeftY },
        } = this.getTrulyPosition();
        const {
            // topLeft: headerTopLeft,
            // topRight: headerTopRight,
            bottomLeft: headerBottomLeft,
            bottomRight: headerBottomRight,
        } = this.getHeaderContentTrulyPosition();
        let restoredHeaderYLimit = (header != null) ? header.content.yLimit / 10 : documentCore.getMaxHeight(null);
        if (!Number.isFinite(restoredHeaderYLimit))
        {
            // Infinite
            restoredHeaderYLimit = documentCore.render().pageProperty.height;
        }
        restoredHeaderYLimit *= scale;

        // header content bottom line 页眉正文线
        let lastLineInHeaderY = this.getLastLineInHeaderY(header) * scale;
        if (header != null) {
            if (lastLineInHeaderY > restoredHeaderYLimit || (header != null && header.content.getPages().length > 1)) {
                lastLineInHeaderY = restoredHeaderYLimit;
            }
        }
        lastLineInHeaderY = numtoFixed(lastLineInHeaderY);

        // line separator. Max(页上边距，页眉正文线y)
        const scaleTopLeftY = numtoFixed(topLeftY * scale);
        let lineSeparatorY = Math.max(scaleTopLeftY, lastLineInHeaderY);
        if (header != null) {
            if (lineSeparatorY > restoredHeaderYLimit || (header != null && header.content.getPages().length > 1)) {
                lineSeparatorY = restoredHeaderYLimit;
            }
        }
        lineSeparatorY = numtoFixed(lineSeparatorY);

        const isHeaderFooterEnabled = (logicDocument.isProtectedMode() === true) ? false : (logicDocument.curPos.type === DocCurPosType.HdrFtr ? true : false);
        const isHeaderEnabled = (logicDocument.getProtectHeaderFooter() === false ? (isHeaderFooterEnabled ? ((logicDocument.getShowHeader() === true) ? true : false) : false) : false);
        let headerContentLineState = isHeaderEnabled ? 'show' : (header ? 'opacity' : 'hide');
        const bShowHeader = (header && !header.isEmptyHeaderFooter());
        if (headerContentLineState === 'opacity') {
            if (bShowHeader !== true) {
                headerContentLineState = 'hide';
            }
        }
        if (lastLineInHeaderY === 0) { // no content intialized yet
            headerContentLineState = 'hide';
        }
        const isHeaderBorderUIEnabled = logicDocument.getShowHeaderBorder();
        const headerContentLineUIState = isHeaderBorderUIEnabled ? headerContentLineState : 'hide';

        const headerBottomLeftX = numtoFixed2(headerBottomLeft.x * scale);
        const headerBottomRightX = numtoFixed2(headerBottomRight.x * scale);
        const headerLineDom: any = (
            <>
                <line className={headerContentLineUIState} x1={headerBottomLeftX} y1={lastLineInHeaderY} x2={headerBottomRightX} y2={lastLineInHeaderY} stroke='dimgray' strokeWidth='1'/>
                <line className={isHeaderEnabled ? '' : 'hide'} x1={0} y1={lineSeparatorY} x2={width} y2={lineSeparatorY} stroke='gray' strokeDasharray='4,2'/>
                <rect className={isHeaderEnabled ? '' : 'hide'} width={38} height={19} x={2} y={lineSeparatorY} style={{stroke: 'gray', fill: '#f0f0f0'}} />
                <text className={isHeaderEnabled ? '' : 'hide'} x={8} y={lineSeparatorY + 14} style={{fontSize: '12px', opacity: 0.8}}>页眉</text>
            </>
        );

        // ----------------------------------------   build footer contents
        let footerInfo: any;
        let footerLineDom: any;
        let bShowFooter = false;
        if (!documentCore.isInlineMode())
        {
            const footer = (page != null) ? page.footer : null;
            const {
                topLeft: footerTopLeft,
                topRight: footerTopRight,
            } = this.getFooterContentTrulyPosition();
            const firstLineInFooterY = numtoFixed(this.getFirstLineInFooterY(footer) * scale);
            const footerTopLeftY = numtoFixed(footerTopLeft.y * scale);
            const footerLineSeparatorY = numtoFixed((firstLineInFooterY === 0)
                ? footerTopLeftY
                : Math.min(footerTopLeftY, firstLineInFooterY)) ; // footerTopRight.y?

            const isFooterBorderUIEnabled = logicDocument.getShowFooterBorder();
            const isFooterEnabled = (logicDocument.getProtectHeaderFooter() === false ? (isHeaderFooterEnabled ? ((logicDocument.getShowFooter() === true) ? true : false) : false) : false);

            let footerContentLineState = isFooterEnabled ? 'show' : (footer ? 'opacity' : 'hide');
            bShowFooter = (footer && !footer.isEmptyHeaderFooter());
            if (footerContentLineState === 'opacity') {
                if (bShowFooter !== true) {
                    footerContentLineState = 'hide';
                }
            }
            const footerTopLeftX = numtoFixed2(footerTopLeft.x * scale);
            const footerTopRightX = numtoFixed2(footerTopRight.x * scale);
            if (firstLineInFooterY === 0) {
                footerContentLineState = 'hide';
            }
            const footerContentLineUIState = isFooterBorderUIEnabled ? footerContentLineState : 'hide';
            footerLineDom = (
                <>
                    <line className={footerContentLineUIState} x1={footerTopLeftX} y1={firstLineInFooterY} x2={footerTopRightX} y2={firstLineInFooterY} stroke='dimgray' strokeWidth='1'/>
                    <line className={isFooterEnabled ? '' : 'hide'} x1={0} y1={footerLineSeparatorY} x2={width} y2={footerLineSeparatorY} stroke='gray' strokeDasharray='4,2'/>
                    <rect className={isFooterEnabled ? '' : 'hide'} width={38} height={19} x={2} y={footerLineSeparatorY - 19} style={{stroke: 'gray', fill: '#f0f0f0'}} />
                    <text className={isFooterEnabled ? '' : 'hide'} x={8} y={footerLineSeparatorY - 5} style={{fontSize: '12px', opacity: 0.8}}>页脚</text>
                </>
            );
            footerInfo = {
                footerContentLineState,
                footerLineSeparatorY,
            };
        }

        // 记录正文内容真实区域
        contentArea.y = lineSeparatorY;
        contentArea.maxY = footerInfo && footerInfo.footerLineSeparatorY || restoredHeaderYLimit;
        return (
            <g className='header-footer'>
                {/* header */ headerLineDom}
                {/* footer */ footerLineDom}
                {/* content */}
                {bShowHeader && this.renderHeaderContent(headerContentLineState, {height: lineSeparatorY})}
                {bShowFooter && this.renderFooterContent(footerInfo.footerContentLineState, {y: footerInfo.footerLineSeparatorY})}
            </g>
        );
    }

    private getLastLineInHeaderY(header: any): number {
        let lastLineInHeaderY = 0;
        if (header != null && header.getTrueContent().length > 0) {
            const contentArr = header.content.content;
            for (let index = contentArr.length - 1; index >= 0; index--) {
                const content = contentArr[index];
                if (content.isHidden()) {
                    continue;
                }
                // const element = contentArr[contentArr.length - 1];
                lastLineInHeaderY = content.getPageBounds(0).bottom;
                break;
            }
        }
        return lastLineInHeaderY;
    }

    private getFirstLineInFooterY(footer: any): number {
        let firstLineInFooterY = 0;
        if (footer != null && footer.getTrueContent().length > 0) {
            const contentArr = footer.content.content;
            const element = contentArr[0];
            if (element.getPageBounds(0) == null) {
                // console.warn('cannot retrieve element bounds');
                return 0;
            }
            firstLineInFooterY = element.getPageBounds(0).top;
        }
        return firstLineInFooterY;
    }

    // private getDocumentContentPageBoundsYLimit(headerFooter: HeaderFooter): number {
    //     let result = -1;
    //     if (headerFooter.isHeader() === true) {
    //         result = headerFooter.content.pages[0].bounds.bottom;
    //     } else {
    //         result = headerFooter.content.pages[0].bounds.top;
    //     }

    //     return result;
    // }

    private getPageBorderPosition(type: string): any {
        let {
            topLeft: { x: topLeftX, y: topLeftY },
            topRight: { x: topRightX, y: topRightY },
            bottomLeft: { x: bottomLeftX, y: bottomLeftY },
            bottomRight: { x: bottomRightX, y: bottomRightY },
        } = this.getTrulyPosition();
        topLeftX = numtoFixed(topLeftX);
        topLeftY = numtoFixed(topLeftY);
        topRightX = numtoFixed(topRightX);
        topRightY = numtoFixed(topRightY);
        bottomLeftX = numtoFixed(bottomLeftX);
        bottomLeftY = numtoFixed(bottomLeftY);
        bottomRightX = numtoFixed(bottomRightX);
        bottomRightY = numtoFixed(bottomRightY);
        // const scale = this.scale;
        // if (scale < 1) {
        //     const leftScaleX = scale * (1 - 0.92 - scale);
        //     const rightScaleX = scale * 1.3;
        //     topLeftX *= leftScaleX;
        //     topLeftY *= scale;
        //     topRightX *= rightScaleX;
        //     topRightY *= scale;
        //     bottomLeftX *= leftScaleX;
        //     bottomLeftY *= scale;
        //     bottomRightX *= rightScaleX;
        //     bottomRightY *= scale;
        // }

        switch (type) {
            case 'topLeft':
                return `${topLeftX},${numtoFixed2(topLeftY -
                    borderLength)} ${topLeftX},${topLeftY} ${numtoFixed2(topLeftX -
                    borderLength)},${topLeftY}`;
            case 'topRight':
                return `${topRightX},${numtoFixed2(topRightY -
                    borderLength)} ${topRightX},${topRightY} ${numtoFixed2(topRightX +
                    borderLength)},${topRightY}`;
            case 'bottomLeft':
                return `${bottomLeftX},${numtoFixed2(bottomLeftY +
                    borderLength)} ${bottomLeftX},${bottomLeftY} ${numtoFixed2(bottomLeftX -
                    borderLength)},${bottomLeftY}`;
            case 'bottomRight':
                return `${bottomRightX},${numtoFixed2(bottomRightY +
                    borderLength)} ${bottomRightX},${bottomRightY} ${numtoFixed2(bottomRightX +
                    borderLength)},${bottomRightY}`;
            default:
                return '';
        }
    }
    /**
     *  render padding of page's content
     */
    private renderPageBorder(): any {
        const styles = {
            stroke: '#ccc',
            fill: 'white',
            fillOpacity: 0,
        };
        const positions = ['topLeft', 'topRight', 'bottomLeft', 'bottomRight'];
        const {
            0: topLeft,
            1: topRight,
            2: bottomLeft,
            3: bottomRight,
        } = positions.map((value) => {
            return {
                points: this.getPageBorderPosition(value),
                ...styles,
            };
        });
        const gStyle = {transform: undefined};
        if (this.viewMode === ViewModeType.CompactView) {
            gStyle.transform = `translateX(${this.props.paddingLeft}px)`;
        }
        return (
            <g style={gStyle}>
                <polyline {...topLeft} />
                <polyline {...topRight} />
                <polyline {...bottomLeft} />
                <polyline {...bottomRight} />
            </g>
        );
    }

    /**
     * render content
     */
    private renderContent(): any {
        const { index, contents, documentCore } = this.props;
        // const height = this.getTrulyHeight();
        // const width = this.getTrulyWidth();
        // const { topLeft: { x, y } } = this.getTrulyPosition();
        const logicDocument = documentCore.getDocument();
        // const header = documentCore.getHdrFtr().pages[index].header;
        // header content bottom line
        // const lastLineInHeaderY = this.getLastLineInHeaderY(header);

        // line separator. Max(页上边距，页眉正文线y)
        // const lineSeparatorY = Math.max(y, lastLineInHeaderY);
        // tslint:disable-next-line: max-line-length
        const headerFooterEnabled = (logicDocument.isProtectedMode() === true) ? false : (logicDocument.getProtectHeaderFooter() === false ? (logicDocument.curPos.type === DocCurPosType.HdrFtr ? true : false) : false);
        let className = headerFooterEnabled
        ? 'header-footer-enabled'
        : '';
        className += className ? ' content' : 'content';
        /* IFTRUE_WATER */
        let tmpMark = null;
        if (this._strTmp) {
            if (this._posIdx === this._strTmp.length) {
                this._strTmp.push(documentCore.getCorePosition());
            }
            tmpMark = this._strTmp[this._posIdx++];
        }
        const markDom = <RandomMarkDom strTmp={tmpMark} />;
        /* FITRUE_WATER */
        return (
            <React.Fragment>
                {/*IFTRUE_WATER markDom FITRUE_WATER*/}
                <g
                    className={className}
                >
                    {/* <rect width={width} height={height} x={x} y={y} fill="white"/> */}
                    {/* <g className='newcontrol-backgroundColor-container'>
                        {this.renderNewControlBackground()}
                    </g> */}
                    <Content
                        ref={this._contentRef}
                        pageIndex={index}
                        content={contents[0]}
                        editorContainer={this.props.editorContainer}
                        documentCore={this.props.documentCore}
                        handleRefresh={this.props.handleRefresh}
                        host={this.props.host}
                        bShowContent={className === 'content'}
                        // bFromHeaderFooter={false}
                    />
                </g>
            </React.Fragment>
        );
    }

    private renderHeaderContent(headerContentLineState: string, rect: any): any {
        const { index, documentCore, contents } = this.props;
        const header = documentCore.getHdrFtr().pages[index].header;
        const headerContent = contents[1]; // : IDocumentContent = documentCore.getContentByPageId(index, 1);
        // console.log(headerContent)

        // let paraPortion = headerContent.paragraphs[0].content[0];
        // console.log(paraPortion)
        // paraPortion.positionY = 69;
        // for (const paraText of paraPortion.content) {
          // paraText.positionY = 69;
        // }
        // const height = this.getHeaderContentTrulyHeight();
        // console.log(header)
        // let curHeight = this.getLastLineInHeaderY(header);
        // let curHeight = this.getDocumentContentPageBoundsYLimit(header);
        // console.log(curHeight)
        // let headerLimit = 561 - 3;
        const scale = this.scale;
        const headerLimit = (header.content.yLimit / 10 - 3) * scale;
        const hdrContent = header.content.content;
        const page = hdrContent[hdrContent.length - 1].getPages()[0];
        // console.log(page)
        // let headerContentCopy = JSON.parse(JSON.stringify(headerContent));

        // let headerParas = headerContent.paragraphs;
        // console.log(headerParas[headerParas.length - 1].lines);

        // let i = 0;
        // while (headerParas[headerParas.length - 1].lines.length === 0) {
        //     headerParas.splice(headerParas.length - 1, 1);
        //     i++;
        //     if (i >= 10000) {
        //         // tslint:disable-next-line: no-console
        //         console.warn('loop exceeds 10000 times');
        //         break;
        //     }
        // }
        // // console.log(headerParas)
        // console.log(headerContent)

        if (page != null && headerContent) {
            let maxBottom = page.bounds.bottom * scale;
            const headerParas = headerContent.paragraphs;

            // if height exceeds, last line must exceed
            if (maxBottom > headerLimit) {
                let i = 0;
                while (maxBottom > headerLimit) {
                    const headerParasLen = headerParas.length - 1;
                    headerParas.splice(headerParasLen, 1);

                    if ( 0 === headerParas.length ) {
                        break;
                    }

                    maxBottom = headerParas[headerParasLen - 1].lines[0].bottom * scale;
                    // console.log(maxBottom)
                    i++;
                    if (i >= 10000) {
                        // tslint:disable-next-line: no-console
                        console.warn('loop exceeds 10000 times');
                        break;
                    }
                }
            }
            // console.log(headerContent)
        }

        // const width = documentCore.getSectionPageSize().width;
        // let { topLeft: { x, y } } = this.getHeaderContentTrulyPosition();
        // x = x * scale;
        // y = y * scale;
        const headerContentState = getTheme().HeaderFooterOpacity === 1 ? headerContentLineState : '';
        // const maskID = 'mask' + this.props.index + '-header';
        /* IFTRUE_WATER */
        let tmpMark = null;
        if (this._strTmp) {
            if (this._posIdx === this._strTmp.length) {
                this._strTmp.push(documentCore.getCorePosition());
            }
            tmpMark = this._strTmp[this._posIdx++];
        }
        const markDom = <RandomMarkDom strTmp={tmpMark} />;
        /* FITRUE_WATER */
        return (
            <React.Fragment>
                {/*IFTRUE_WATER markDom FITRUE_WATER*/}
                <g className={headerContentState}>
                    {this.renderMask({x: 0, y: 0, width: numtoFixed2(page.xLimit), height: rect.height})}
                    {/* <rect> indicates content area, no real use */}
                    {/* <rect width={width} height={height} x={x} y={y} fill="white"/> */}
                    {/* <g className='newcontrol-backgroundColor-container'>
                        {this.renderNewControlBackground(DocumentSectionType.Header)}
                    </g> */}
                    <Content
                        pageIndex={index}
                        content={headerContent}
                        editorContainer={this.props.editorContainer}
                        documentCore={this.props.documentCore}
                        handleRefresh={this.props.handleRefresh}
                        nHeaderFooter={1}
                        host={this.props.host}
                        mask={index}
                        bShowContent={headerContentState === ''}
                    />
                </g>
            </React.Fragment>
        );
    }

    private renderFooterContent(footerContentLineState: string, rect: any): any {
        const { index, documentCore, contents } = this.props;
        const footer = documentCore.getHdrFtr().pages[index].footer;
        const footerContent = contents[2]; // : IDocumentContent = documentCore.getContentByPageId(index, 2);
        const scale = this.scale;
        const footerLimit = (footer.content.yLimit / 10 - 3) * scale;
        const ftrContent = footer.content.content;
        const page = ftrContent[ftrContent.length - 1].getPages()[0];

        if (page != null && footerContent) {
            let maxBottom = page.bounds.bottom;
            const footerParas = footerContent.paragraphs;

            // if height exceeds, last line must exceed
            if (maxBottom > footerLimit) {
                let i = 0;
                while (maxBottom > footerLimit) {
                    const footerParasLen = footerParas.length - 1;
                    footerParas.splice(footerParasLen, 1);

                    if ( 0 === footerParas.length ) {
                        break;
                    }

                    maxBottom = footerParas[footerParasLen - 1].lines[0].bottom * scale;
                    // console.log(maxBottom)
                    i++;
                    if (i >= 10000) {
                        // tslint:disable-next-line: no-console
                        console.warn('loop exceeds 10000 times');
                        break;
                    }
                }
            }
            // console.log(footerContent)
        }

        // const height = this.getFooterContentTrulyHeight();
        // const width = documentCore.getSectionPageSize().width;
        // let { topLeft: { x, y } } = this.getFooterContentTrulyPosition();
        // x = x * scale;
        // y = y * scale;
        // console.log(footerContent)

        // const maskID = 'mask' + this.props.index + '-footer';
        const footerContentState = getTheme().HeaderFooterOpacity === 1 ? footerContentLineState : '';

        /* IFTRUE_WATER */
        let tmpMark = null;
        if (this._strTmp) {
            if (this._posIdx === this._strTmp.length) {
                this._strTmp.push(documentCore.getCorePosition());
            }
            tmpMark = this._strTmp[this._posIdx++];
        }
        const markDom = <RandomMarkDom strTmp={tmpMark} />;
        /* FITRUE_WATER */
        return (
            <React.Fragment>
                {/*IFTRUE_WATER markDom FITRUE_WATER*/}
                <g className={footerContentState}>
                    {this.renderMask({x: 0, y: rect.y, width: numtoFixed2(page.xLimit),
                                height: numtoFixed2(footerLimit - rect.y)})}
                    {/* <rect width={width} height={height} x={x} y={y + 2} fill="white"/> */}
                    {/* <g className='newcontrol-backgroundColor-container'>
                        {this.renderNewControlBackground(DocumentSectionType.Footer)}
                    </g> */}
                    <Content
                        pageIndex={index}
                        content={footerContent}
                        editorContainer={this.props.editorContainer}
                        documentCore={this.props.documentCore}
                        handleRefresh={this.props.handleRefresh}
                        nHeaderFooter={2}
                        host={this.props.host}
                        mask={index}
                        bShowContent={footerContentState === ''}
                    />
                </g>
            </React.Fragment>
        );
    }

    private renderNewControlBackground(areaType: DocumentSectionType = DocumentSectionType.Document): any {
        const props = this.props;
        if (props._bCascadeManage === true) {
            // this._newBgRef = React.createRef();
            // const visibility = this.props.documentCore.isShowCascadeBackground() ? 'visible' : 'hidden';
            // return (
            //     <g name="ShowCascadeBackgroundPanel" style={{visibility}}>
            //         <NewControlCascadeBackground
            //             documentCore={props.documentCore}
            //             scale={this.scale}
            //             pageIndex={props.index}
            //             ref={this._newBgRef}
            //             bCascadeManager={props._bCascadeManage}
            //         />
            //     </g>
            // );
            return null;
        } else {
            this._newBgRef = React.createRef();

            /* IFTRUE_WATER */
            let tmpMark = null;
            if (this._strTmp) {
                if (this._posIdx === this._strTmp.length) {
                    this._strTmp.push(props.documentCore.getCorePosition());
                }
                tmpMark = this._strTmp[this._posIdx++];
            }
            const markDom = <RandomMarkDom strTmp={tmpMark} />;
            /* FITRUE_WATER */
            return (
                <React.Fragment>
                    {/*IFTRUE_WATER markDom FITRUE_WATER*/}
                    <NewControlBackground
                        documentCore={props.documentCore}
                        scale={this.scale}
                        pageIndex={props.index}
                        ref={this._newBgRef}
                    />
                </React.Fragment>
            );
        }
        const documentCore = this.props.documentCore;
        const newControls = documentCore.getNewControlsWithoutFocus(this.props.index);
        const scale = this.scale;

        return newControls.map((newControl) => {
            if ( false === newControl.isHiddenBackground() || true === newControl.isEditProtect()) {
                const newControlsBounds = documentCore.getNewControlBounds(newControl.getNewControlName());
                if (newControlsBounds && 0 < newControlsBounds.bounds.length) {
                    // const newControl = documentCore.getNewControlByName(name);
                    const bPopWinNewControl = newControl.isPopWindowNewControl();
                    let fillColor = true !== bPopWinNewControl ? NewControlDefaultSetting.DefaultBackgroundColor :
                                                                      NewControlDefaultSetting.DefaultBackgroundColor2;
                    fillColor = newControl.getBgColorOfSignStructs() === 1 ?
                        fillColor : NewControlDefaultSetting.DefaultBackgroundColorCanntEdit;
                    // console.log(fillColor)

                    return newControlsBounds.bounds.map((bound, index) => {
                        if (bound && this.props.index === bound.pageIndex) {
                            return (
                                <rect
                                    width={numtoFixed2(bound.width * scale)}
                                    key={index}
                                    height={numtoFixed2(bound.height * scale)}
                                    x={numtoFixed2(bound.x * scale)}
                                    y={numtoFixed2(bound.y * scale)} // {(bound.y + 3) * scale}
                                    fill={fillColor}
                                />
                            );
                        }
                    });
                }
            }
        });
    }

    private renderTableNewBorder = () => {
        const bDisplay = this.props.documentCore.isMovingTableBorder();
        if (bDisplay === false) {
            return null;
        }
        let bColumn = false;
        let x1, y1, x2, y2 = 0;

        if ( bDisplay ) {
          const border = this.props.documentCore.getTableNewBorder();

          if ( null == border ) {
              return ;
          }

          bColumn = border.bColumn;
          const width = border.width;
          const height = border.height;
          x1 = bColumn ? border.x : 0;
          x2 = bColumn ? border.x : width;
          y1 = bColumn ? 0 : border.y;
          y2 = bColumn ? height : border.y;
        }
        let tmpClassName = null;
        /* IFTRUE_WATER */
        let tmpMark = null;
        if (this._strTmp) {
            if (this._posIdx === this._strTmp.length) {
                this._strTmp.push(this.props.documentCore.getCorePosition());
            }
            tmpMark = this._strTmp[this._posIdx++];
        }
        const markDom = <RandomMarkDom strTmp={tmpMark} />;
        tmpClassName = this.documentCore.getCoreBGen() && Math.random() < 0.4 ? getCode() : null;
        /* FITRUE_WATER */
        return (
            <React.Fragment>
                {/*IFTRUE_WATER markDom FITRUE_WATER*/}
                <g  className={tmpClassName} style={{display: bDisplay ? 'block' : 'none'}}>
                    <line
                        x1={numtoFixed2(x1)}
                        y1={numtoFixed2(y1)}
                        x2={numtoFixed2(x2)}
                        y2={numtoFixed2(y2)}
                        stroke={`rgb(51,102,204)`}
                        strokeDasharray='4,2'
                    />
                </g>
            </React.Fragment>
        );
      }

    private renderMask(rect: any): any {
        const maskID = 'mask' + this.props.index;
        const mask = (
            <clipPath id={maskID} key={maskID}>
                <rect key={this.props.index + '-mask'} x={rect.x} y={rect.y}
                    width={rect.width} height={rect.height} fill='#666' />
            </clipPath>
            );
        return (<defs>
                    {mask}
                </defs>);
    }

    private renderNewControlCascadePanel(): any {
        const host = this.props.host;
        if (host.isCascadeManager() !== true) {
            return [];
        }

        const documentCore = this.props.documentCore;
        const newControls = documentCore.getNewControlsWithoutFocus(this.props.index, null, true);
        const regions = documentCore.getRegionsWithoutFocus(this.props.index, null, true);
        const props = {
            host,
            newControls,
            regions,
            documentCore,
            svgRef: this._svgRef
        };
        this['__bCascadeManage'] = true;
        return (
            <AsyncLoad
                host={this}
                name='__bCascadeManage'
                component={components.NewControlCascadeTipPanel}
                props={props}
            />
        );
    }

    private renderNewControlLayer(): any {
        const props = this.props;
        this._newControlLayer = React.createRef();
        return (
        <NewControlLayer
            host={props.host}
            // scale={this.scale}
            pageIndex={props.index}
            ref={this._newControlLayer}
        />
        );
    }

    private refreshNewControlLayer(): void {
        const ref = this._newControlLayer;
        if (!ref || !ref.current) {
            return;
        }
        ref.current.refresh2();
    }
}
