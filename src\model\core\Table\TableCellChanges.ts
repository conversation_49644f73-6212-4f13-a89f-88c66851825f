import { ChangeBaseBoolProperty, ChangeBaseLongProperty, ChangeBaseObjectProperty } from '../HistoryChange';
import { HistroyItemType } from '../HistoryDescription';
import { TableCell } from './TableCell';
import { DocumentBorder } from '../Style';
import { GenericBox } from 'src/common/commonDefines';
import { TableMeasurement } from '../TableProperty';
import { TableCellProperty } from './TableCellProperty';

export class ChangeTableCellBorderTop extends ChangeBaseObjectProperty {
    private type: HistroyItemType;

    constructor(changeClass: TableCell, old: DocumentBorder, news: DocumentBorder, color?: any) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.TableCellBorderTop;
    }

    public setValue(value: DocumentBorder): void {
        const cell = this.changeClass as TableCell;

        if ( cell ) {
            cell.property.borders.top = value;
        }
    }
}

export class ChangeTableCellBorderBottom extends ChangeBaseObjectProperty {
    private type: HistroyItemType;

    constructor(changeClass: TableCell, old: DocumentBorder, news: DocumentBorder, color?: any) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.TableCellBorderBottom;
    }

    public setValue(value: DocumentBorder): void {
        const cell = this.changeClass as TableCell;

        if ( cell ) {
            cell.property.borders.bottom = value;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeTableCellBorderLeft extends ChangeBaseObjectProperty {
    private type: HistroyItemType;

    constructor(changeClass: TableCell, old: DocumentBorder, news: DocumentBorder, color?: any) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.TableCellBorderLeft;
    }

    public setValue(value: DocumentBorder): void {
        const cell = this.changeClass as TableCell;

        if ( cell ) {
            cell.property.borders.left = value;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeTableCellBorderRight extends ChangeBaseObjectProperty {
    private type: HistroyItemType;

    constructor(changeClass: TableCell, old: DocumentBorder, news: DocumentBorder, color?: any) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.TableCellBorderRight;
    }

    public setValue(value: DocumentBorder): void {
        const cell = this.changeClass as TableCell;

        if ( cell ) {
            cell.property.borders.right = value;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeTableCellMargins extends ChangeBaseObjectProperty {
    private type: HistroyItemType;

    constructor(changeClass: TableCell, old: GenericBox<TableMeasurement>,
                news: GenericBox<TableMeasurement>, color?: any) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.TableCellMargins;
    }

    public setValue(value: GenericBox<TableMeasurement>): void {
        const cell = this.changeClass as TableCell;

        if ( cell ) {
            cell.property.margin.top = value.top;
            cell.property.margin.bottom = value.bottom;
            cell.property.margin.left = value.left;
            cell.property.margin.right = value.right;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeTableCellWidth extends ChangeBaseObjectProperty {
    private type: HistroyItemType;

    constructor(changeClass: TableCell, old: TableMeasurement, news: TableMeasurement, color?: any) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.TableCellMargins;
    }

    public setValue(value: TableMeasurement): void {
        const cell = this.changeClass as TableCell;

        if ( cell ) {
            cell.property.cellWidth = value;
        }
    }
}

interface TableCellSlashValue {
    bCellLeftSlash: boolean;
    bCellRightSlash: boolean;
}
// tslint:disable-next-line: max-classes-per-file
export class ChangeTableCellSlash extends ChangeBaseObjectProperty {
    private type: HistroyItemType;

    constructor(changeClass: TableCell, old: TableCellSlashValue, news: TableCellSlashValue, color?: any) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.TableCellSlashChange;
    }

    public setValue(value: TableCellSlashValue): void {
        const cell = this.changeClass as TableCell;

        if ( cell && value ) {
            cell.property.bCellLeftSlash = value.bCellLeftSlash;
            cell.property.bCellRightSlash = value.bCellRightSlash;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeTableCellGridSpan extends ChangeBaseLongProperty {
    private type: HistroyItemType;

    constructor(changeClass: TableCell, old: number, news: number, color?: any) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.TableCellGridSpan;
    }

    public setValue(value: number): void {
        if ( this.changeClass ) {
            this.changeClass.property.gridSpan = value;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeTableCellProtected extends ChangeBaseBoolProperty {
    private type: HistroyItemType;

    constructor(changeClass: TableCellProperty, old: boolean, news: boolean, color?: any) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.TableCellProtected;
    }

    public setValue(value: boolean): void {
        if ( this.changeClass ) {
            this.changeClass.bProtected = value;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeTableCellVertAlign extends ChangeBaseLongProperty {
    private type: HistroyItemType;

    constructor(changeClass: TableCell, old: number, news: number, color?: any) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.TableCellVertAlign;
    }

    public setValue(value: number): void {
        if ( this.changeClass ) {
            this.changeClass.vertAlign = value;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeTableCellVerticalMerge extends ChangeBaseLongProperty {
    private type: HistroyItemType;

    constructor(changeClass: TableCell, old: number, news: number, color?: any) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.TableCellVerticalMerge;
    }

    public setValue(value: number): void {
        if ( this.changeClass ) {
            this.changeClass.property.verticalMerge = value;
        }
    }
}
