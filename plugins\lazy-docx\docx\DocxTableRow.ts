import { ISerialTableCellObj, ISerialTableRowObj, SerialObjType } from '../../serialize/serialInterface';
import { TableCell, TableRow } from 'docx';
import DocxTableCell from './DocxTableCell';
import { IAbstractDocx, IDocx } from './IDocx';

export default class DocxTableRow extends IAbstractDocx implements IDocx {
    constructor(private readonly tableRowObj: ISerialTableRowObj) { super(); }

    public buildTo(collector: TableRow[]): TableRow[] {
        if (!this.tableRowObj) {
            return collector;
        }
        const option: any = Object.assign({}, this.tableRowObj);
        this.deleteUnUsefulProp(option, 'type', 'children');
        const children: TableCell[] = [];
        for (const cell of this.tableRowObj.children) {
            if (cell.type === SerialObjType.TableCell) {
                new DocxTableCell(cell as ISerialTableCellObj).buildTo(children);
            }
        }
        option.children = children;
        const row = new TableRow(option);
        collector.push(row);
        return collector;
    }
}
