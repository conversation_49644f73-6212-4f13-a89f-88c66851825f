import React from 'react';
import TextArea from '../../../ui/TextArea';
import {
    GlobalEvent as gEvent,
    GlobalEventName as gEventName,
} from '../../../../../common/GlobalEvent';
import { CodeAndValue, MixFormulaParser } from '@/common/commonDefines';

interface ISearchIconProps {
    onClick: (e: any) => void;
}
export class SearchIcon extends React.Component<ISearchIconProps, {}> {
    private ref: any;
    constructor(props: any) {
        super(props);
        this.ref = React.createRef();
    }

    public render(): any {
        return (
            <div className='searchid-icon' ref={this.ref}/>
        );
    }

    public componentDidMount(): void {
        this.ref.current.addEventListener('click', this.onClick);
    }

    public componentWillUnmount(): void {
        this.ref.current?.removeEventListener('click', this.onClick);
    }

    private onClick = (e: any) => {
        if (this.props.onClick) {
            this.props.onClick(e);
        }
    }
}

interface IProps {
    documentCore: any;
    host: any;
}

export default class SearchID extends React.Component<{}, {}> {
    private visible: boolean;
    private values: string;
    private ref: any;
    private result: {pos?: number, name: string, newControl?: any, id?: string};
    private documentCore: any;
    private host: any;
    // 混合运算级联使用
    private events: any;
    private isExpression: boolean = false;
    private areaIndex: number = -1;
    private resultOnChange: (value: string) => void;
    constructor(props: any) {
        super(props);
        this.ref = React.createRef();
        this.result = {name: undefined};
    }

    public render(): any {
        return this.renderSearchInput();
    }

    public componentWillUnmount(): void {
        this.removeEvent();
    }

    public open(x: number, y: number, data: any, events: any, e: any): void {
        const dom = this.ref.current;
        if (!dom) {
            return;
        }
        this.events = {};
        dom.style.display = 'block';
        dom.style.right = x + 'px';
        dom.style.top = y + 'px';
        this.isExpression = false;
        let controlNameStr = '';
        if (!data && events?.key === CodeAndValue.Expression) {
            // key === expression
            this.isExpression = true;
            this.values = events.expression || '';
            controlNameStr = events.target;
            this.events = events;
        } else {
            let values: string;
            if (data.action !== undefined) {
                values = this.values = data.target;
            } else {
                values = this.values = data.source;
            }
            if (events && values && values.indexOf('*') !== -1) {
                this.values = events.target;
            }
            controlNameStr = this.values;
        }
        this.visible = true;
        this.updateEditorTips((controlNameStr || '').split(','));
        this.setState({});

    }

    public onClose = (): void => {
        if (this.visible === false) {
            return;
        }
        this.ref.current.style.display = 'none';
        this.visible = false;
        gEvent.setEvent(this.host.docId, 'CascadeTipVisible', false);
    }

    public init(host: any, callback: any): void {
        if (!host) {
            return;
        }
        this.host = host;
        this.documentCore = host.state.documentCore;
        this.resultOnChange = callback;
        gEvent.addEvent(host.docId, gEventName.TipDbClick, this.dblClick);

        const dom = this.ref.current;
        if (!dom || !dom.parentNode) {
            return;
        }
    }

    private updateEditorTips(controlNames: string[]): void {
        const id = this.host.docId;
        if (controlNames.length) {
            gEvent.setEvent(id, 'resetCascadeManagerFlag', controlNames);
        }
    }

    private dblClick = (newControl: any, bAdd?: boolean) => {
        if (!this.visible) {
            return;
        }
        let value = this.values || '';
        const name = newControl.getNewControlName();
        let controlNameStr = '';
        if (this.isExpression) {
            value = value.replace(/ /g, '');
            const [hasReplace, newValue] = MixFormulaParser.replaceElemNames(value, '', n => n === name);
            if (!hasReplace) {
                // 不存在匹配元素名，则追加元素
                let index = this.areaIndex !== -1 ? this.areaIndex : value.length;
                const fulName = `[${name}]`;
                value = value.substring(0, index) + fulName + value.substring(index);
                this.areaIndex = index + fulName.length;
            } else {
                value = newValue;
                this.areaIndex = -1;
            }
            const { elemNames } = MixFormulaParser.toInfixExpressions(value);
            controlNameStr = elemNames.join(',');
            this.events.target = controlNameStr;
        } else {
            if (value) {
                const arrs = value.split(',');
                const index = arrs.indexOf(name);
                if (index !== -1) {
                    if (bAdd === true) {
                        return;
                    }
                    arrs.splice(index, 1);
                    value = arrs.join(',');
                } else {
                    value += ',' + name;
                }
            } else {
                if (bAdd !== true) {
                    return;
                }
                value = name;
            }
            controlNameStr = value;
        }

        this.values = value;
        this.updateEditorTips((controlNameStr || '').split(','));
        this.resultOnChange(value);
        this.setState({});
    }

    private removeEvent = () => {
        gEvent.deleteEvent(this.host.docId, gEventName.TipDbClick, this.dblClick);
    }

    private renderSearchInput(): any {
        return (
            <div className='searchid-input' ref={this.ref}>
                <div>
                    <div>
                        <TextArea
                            value={this.values}
                            readonly={!this.isExpression}
                            name='values'
                            rows={4}
                            onChange={this.onChange}
                            onSelectionChange={this.onSelectionChange}
                        />
                    </div>
                    <div className='btns'>
                        <span onClick={this.onClose}>关闭</span>
                    </div>
                </div>
            </div>
        );
    }

    private onChange = (value: any, name: string) => {
        // name === 'values'
        this[name] = value;
        this.resultOnChange(value);
    }

    private onSelectionChange = (index: number) => {
        if (index != -1) {
            this.areaIndex = index;
        }
    }

}
