import * as React from 'react';
import '../style/toolbar.less';
import {GlobalEvent as gEvent, GlobalEventName as gEventName} from '../../../common/GlobalEvent';
import {fonts} from '../text/font';
import { MenuAction } from '../../../common/menu/MenuAction';
// tslint:disable-next-line: max-line-length
import { IMenuItem, IPropKey, menus, MenuItemIndex, alignMenus, lineHeights, toolbarImages } from '../../../common/menu/toolbarDefines';
import { EmrEditor } from '../Main';
import {SelectColor} from '../ui/SelectColor';
import { AlignType, EditorHeader, FONST_SIZE_PT, ICustomToolbarItem, isMacOs, LineSpacingType, NumberingType, ResultType, VIEW_SCALE } from '../../../common/commonDefines';
import '../style/iconfont/iconfont.css';
import Radio from '../ui/Radio';
import { IFRAME_MANAGER } from '../../../common/IframeManager';
import { message } from '../../../common/Message';
import { getCustomToolbar } from '@/common/GlobalConfig';

interface IProps {
    host: EmrEditor;
}

interface IState {
    bRefresh: boolean;
    bPasteEnabled: boolean;
    height: string;
}

interface IStyle {
    left: number;
    top: number;
}

const scaleDatas = [{key: '75%', value: 75}, {value: 100, key: '100%'},
    {value: 115, key: '115%'}, {value: 125, key: '125%'},
    {value: 150, key: '150%'}, {value: 200, key: '200%'}];

export default class Menu extends React.Component<IProps, IState> { // 'Toolbar' actually
    private oldProps: any;
    private datas: IMenuItem[];
    private customBarItems: ICustomToolbarItem[];
    private selectData: any[];
    private prop: IPropKey;
    private selecteName: string;
    private selectStyle: IStyle;
    private selectActive: boolean; // dropdown on/off
    private activeMenuIndex: string;
    private currentRef: any;
    private bItemClick: boolean;
    private host: any;
    private bActive: boolean;
    private menuAction: MenuAction;
    private activeItems: IPropKey[];
    private timeout: any;
    private color: string; // temp save var
    private textColor: string;
    private backgroundColor: string;
    private insertImageRef: React.RefObject<HTMLInputElement>;
    private bUse: boolean = false;
    private bEditLocked: boolean = false;
    private bOnImage: boolean = false;
    private bUndo: boolean = false;
    private bRedo: boolean = false;
    private toolbarRef: any = React.createRef();
    private moreIconRef: any = React.createRef();
    private lineIconLimit: number = -1;
    private documentCore: any;
    private lineType: any;
    private lastLineRef: any;
    private bRenderWhite: boolean = false;
    private bReaderMoreMenu: boolean = false;
    private menu: IMenuItem;
    private scaleValue: any;
    private fontSize: number;
    private selectAlignType: number;
    private resizeTimeout: any;

    constructor(props: IProps) {
        super(props);
        this.host = this.props.host;
        this.insertImageRef = React.createRef();

        this.state = {
            bRefresh: false,
            bPasteEnabled: false,
            height: EditorHeader.Toolbar + 'px',
        };
        this.prop = {
            name: 'name',
            value: 'value',
        };
        this.selectStyle = {left: 0, top: 0};
        this.currentRef = React.createRef();
        this.menuAction = new MenuAction(this.props.host);
        this.activeItems = [];
        this.color = this.textColor = this.backgroundColor = '#fff';
        this.documentCore = this.host.documentCore;
        this.lastLineRef = React.createRef();
        const curToolbar = this.documentCore.getToolbarItemStatus();
        this.datas = JSON.parse(JSON.stringify(curToolbar ? curToolbar : menus));
        this.customBarItems = getCustomToolbar();
    }

    public render(): any {
        let selectClass = 'menu-select-list'; // initially two font dropdowns
        let div: any;
        if (this.selectActive === true) { // like click event
            selectClass += ' active'; // display: block
            div = (
            <div className={selectClass} style={{...this.selectStyle}}>
                {/* dynamic list */}
                {
                    // tslint:disable-next-line: jsx-no-multiline-js
                    // tslint:disable-next-line: max-line-length
                    (this.activeMenuIndex !== MenuItemIndex.Color && this.activeMenuIndex !== MenuItemIndex.BackgroundColor) ?
                    (this.renderDropdown()) : (this.renderColorPicker())
                }
                {this.renderViewScale()}
            </div>
            );
        }
        const selection = this.documentCore.getDocumentSelection();
        this.bUse = selection.bUse && !selection.bStart;
        // console.log(this.host.documentCore.getDocumentSelection());
        // this.bOnImage = this.host.documentCore.getImageSelectionInfo() != null ? true : false;
        this.bUndo = this.host.documentCore.canUndo();
        this.bRedo = this.host.documentCore.canRedo();

        // set current alignType
        this.checkAlignment();

        // console.log(this.bEditLocked, this.bOnImage, this.bUndo, this.bRedo)
        this.checkPasteEnabled();

        return (
            <div className='editor-toolbar'>
                {this.renderListContainer()}
                {div}
                <input
                    className='insert-image'
                    type='file'
                    ref={this.insertImageRef}
                    accept='image/*'
                    onChange={this.handleChangeImage}
                />
            </div>
        );
    }

    public componentDidMount(): void {
        this.insertImageRef.current.ownerDocument.addEventListener('click', this.docClickEvent);
        gEvent.addEvent(this.host.docId, gEventName.Selection, this.selectionEvent);
        gEvent.addEvent(this.host.docId, gEventName.Mouseup, this.selectionEvent);
        gEvent.addEvent(this.host.docId, gEventName.Click, this.editorClick);
        gEvent.addEvent(this.host.docId, gEventName.ToolBarItemVisible, this.toolBarChange);
        gEvent.addEvent(this.host.docId, gEventName.ToolBarCustomChange, this.toolBarCustomChange);
        gEvent.addEvent(this.host.docId, gEventName.ContentChange, this.selectionEvent);
        gEvent.addEvent(this.host.docId, gEventName.KeyDown, this.selectionEvent);

        // add copy core, should only occur once
        if (this.menuAction.getCopyPaste() == null) {
            setTimeout(() => {
                this.menuAction.createCopyPasteSingleton();
                this.setState({});
            }, 200);
        }

        // 初始计算工具栏布局
        this.calculateToolbarLayout();
        
        // 添加窗口大小变化监听器
        window.addEventListener('resize', this.handleWindowResize);
    }

    public componentWillUnmount(): void {
        gEvent.deleteEvent(this.host.docId, gEventName.Selection, this.selectionEvent);
        gEvent.deleteEvent(this.host.docId, gEventName.Mouseup, this.selectionEvent);
        gEvent.deleteEvent(this.host.docId, gEventName.Click, this.editorClick);
        gEvent.deleteEvent(this.host.docId, gEventName.ToolBarItemVisible, this.toolBarChange);
        gEvent.deleteEvent(this.host.docId, gEventName.ToolBarCustomChange, this.toolBarCustomChange);
        gEvent.deleteEvent(this.host.docId, gEventName.ContentChange, this.selectionEvent);
        gEvent.deleteEvent(this.host.docId, gEventName.KeyDown, this.selectionEvent);
        this.insertImageRef.current?.ownerDocument?.removeEventListener('click', this.docClickEvent);
        
        // 移除窗口大小变化监听器
        window.removeEventListener('resize', this.handleWindowResize);
        // 清理 timeout
        if (this.resizeTimeout) {
            clearTimeout(this.resizeTimeout);
        }
    }

    // 处理窗口大小变化
    private handleWindowResize = (): void => {
        if (this.resizeTimeout) {
            clearTimeout(this.resizeTimeout);
        }
        this.resizeTimeout = setTimeout(() => {
            this.calculateToolbarLayout();
        }, 100);
    }

    // 计算工具栏布局
    private calculateToolbarLayout = (): void => {
        let toolbarWidth = 0;
        let currentWidth = 0;
        let moreIconElemWidth = 0;
        
        const toolbarElem: HTMLElement = this.toolbarRef.current;
        if (!toolbarElem) {
            return;
        }

        // 重置展开状态
        if (this.bReaderMoreMenu) {
            const dom = this.lastLineRef.current;
            if (dom) {
                dom.className = dom.className.replace(' active', '');
                this.bReaderMoreMenu = false;
            }
        }

        // 重置 lineIconLimit 以强制重新计算
        this.lineIconLimit = -1;
        
        // 移除这两行，因为固定内容不需要判断
        // toolbarWidth = toolbarElem.scrollWidth;
        const clientWidth = toolbarElem.clientWidth;

        const icons = toolbarElem.getElementsByTagName('li');
        const moreIconElem: HTMLElement = this.moreIconRef.current;
        if (moreIconElem != null) {
            moreIconElemWidth = moreIconElem.offsetWidth;
        } else { // hack
            moreIconElemWidth = 30;
        }

        for (let i = 0; i < icons.length; i++) {
            currentWidth += icons[i].offsetWidth;
            if (currentWidth + moreIconElemWidth >= clientWidth) {
                this.lineIconLimit = i;
                break;
            }
        }

        if (this.lineIconLimit === 0) {
            this.lineIconLimit = -1;
        }
        
        // 触发重新渲染
        this.setState({ bRefresh: !this.state.bRefresh });
    }

    // 修改工具栏按钮显示
    private toolBarChange = (itemObj: any): void => {
        // reload toolbar
        itemObj.result = false;
        // 分隔符显示控制
        let hideSplit = true;
        this.datas.forEach((barItem: IMenuItem) => {
            if (barItem.index === 'separator') {
                barItem.hide = hideSplit;
                hideSplit = true;
                return;
            }
            if (itemObj.hasOwnProperty(barItem.index)) {
                const isShow = itemObj[barItem.index];
                if (typeof isShow === 'boolean' && barItem.hide !== !isShow) {
                    barItem.hide = !isShow;
                    itemObj.result = true;
                }
            }
            hideSplit = hideSplit && barItem.hide;
        });
        if (itemObj.result) {
            this.documentCore.setToolbarItemStatus(JSON.parse(JSON.stringify(this.datas)));
            this.setState({});
        }
    }

    private toolBarCustomChange = (): void => {
        this.customBarItems = getCustomToolbar();
        this.setState({});
    }

    private editorClick = (event: any): void => {
        this.docClickEvent();
    }

    private renderViewScale(): any {
        const activeIndex = this.activeMenuIndex;
        if (activeIndex !== MenuItemIndex.Scale) {
            return;
        }
        const menu = this.datas.find((item) => item.index === activeIndex);
        if (!menu) {
            return;
        }
        this.menu = menu;
        let scaleValue = this.scaleValue;
        if (scaleValue == null) {
            scaleValue = '';
        }
        return (
        <div className='toolbar-view-scale' onClick={this.scaleClick}>
            <label>显示比例</label>
            <Radio value={this.scaleValue} data={scaleDatas} onChange={this.scaleChange} />
            <label>百分比</label>
            <div className='input'>
                <span>
                    <input value={scaleValue} onFocus={this.scaleFocus} onChange={this.scaleBlur} />
                </span>
                <label>%</label>
            </div>
            <div className='scale-btns'>
                <span data-index='btn-1'>取消</span>
                <span data-index='btn-2'>确认</span>
            </div>
        </div>
        );
    }

    private selectionEvent = (): void => {
        // this event is triggered in wither mouseEvent.ts or document.ts
        // render related trigger
        clearTimeout(this.timeout);
        this.timeout = setTimeout(() => {
            // get SELECTED AREA's text property
            const options = this.menuAction.getSelectMenuItems();
            // console.log(options) // selected area's text props
            this.activeItems = options.items;

            // if not in selection, need to reflect previous char's text property
            const font = options.font || {};
            this.parseColor(font.backgroundColor);
            // const textProperty: TextProperty = this.documentCore.getCurrentTextProps();
            // console.log(cursorPortion)
            // let textProperty: TextProperty = null;

            // if (textProperty != null) {
            //     font.fontSize = textProperty.fontSize;
            //     font.fontFamily = textProperty.fontFamily;
            // }
            // const font = options.font;
            const datas = this.datas;
            const fontSizeKey = MenuItemIndex.FontSize;
            const fontFamilyKey = MenuItemIndex.FontFamily;
            let activeIndex = 0;
            // prev char of current cursor
            for (let index = 0, length = datas.length; index < length; index++) { // only check font?
                const item = datas[index];
                if (item.index === fontSizeKey) {
                    this.fontSize = font.fontSize;
                    activeIndex++;
                } else if (item.index === fontFamilyKey) {
                    item.content = (fonts[0].options as any[]).find(item => item.value === font.fontFamily)?.name || font.fontFamily;
                    activeIndex++;
                }
                if (activeIndex === 2) {
                    break;
                }
            }

            // edit 'options' for real-time cursor props update
            // for (const item of options.items) {
            //     if (item.name === MenuItemIndex.Bold) {
            //         item.value = (textProperty != null) ? textProperty.fontWeight : item.value;
            //     } else if (item.name === MenuItemIndex.Underline) {
            //         item.value = (textProperty != null) ? (textProperty.textDecorationLine === TextDecorationLineType.Underline ? 1 : 0) : item.value;
            //     } else if (item.name === MenuItemIndex.Italic) {
            //         item.value = (textProperty != null) ? textProperty.fontStyle : item.value;
            //     } else if (item.name === MenuItemIndex.Sub) {
            //         item.value = (textProperty != null) ? (textProperty.vertAlign === TextVertAlign.Sub ? true : false) : item.value;
            //     } else if (item.name === MenuItemIndex.Sup) {
            //         item.value = (textProperty != null) ? (textProperty.vertAlign === TextVertAlign.Super ? true : false) : item.value;
            //     }
            // }

            if (!this.documentCore.isSelectionUse()) {
                const paraProp = this.documentCore.getParagraphProperty();
                this.selectAlignType = paraProp?.alignment;
            } else {
                this.selectAlignType = null;
            }

            this.oldProps = options;

            // if (options.items) { // options -> just selected area's text properties. nothing to do with current cursor
            //     const selectedColor = (options.items.find((item) => item.name === MenuItemIndex.Color)).value;
            //     const selectedBgColor = (options.items.find((item) => item.name
            //     === MenuItemIndex.BackgroundColor)).value;
            //     this.textColor = selectedColor ? selectedColor : '#000';
            //     this.backgroundColor = selectedBgColor ? selectedBgColor : '#000';
            // }

            this.textColor = font.color;
            this.backgroundColor = font.backgroundColor;

            this.setState({bRefresh: !this.state.bRefresh});
        }, 400);
    }

    private renderList(curLineIcons: IMenuItem[]): any { // toolbar line
        const activeItems = this.activeItems;
        return curLineIcons.filter((data) => data.hide !== true)
        .map((data, index) => {
            const dataIndex = data.index;
            let className = '';
            let iconClassName = 'iconfont ' + data.icon; // 'iconfont icon-editorAlignBetween';
            let colorBox = null;
            let style: any;

            switch (data.index) {
                case MenuItemIndex.JustifyAlign:
                case MenuItemIndex.RightAlign:
                case MenuItemIndex.CenterAlign:
                case MenuItemIndex.LeftAlign: {
                    if (data.value === this.selectAlignType) {
                        className = 'active';
                    }
                    break;
                }
                case MenuItemIndex.FontFamily: {
                    if (data.content) {
                        // mac conversion
                        // tslint:disable-next-line: newline-per-chained-call
                        // const isMacOs = (navigator.userAgent.toLowerCase().indexOf('mac') > -1);
                        if (isMacOs) {
                            // macValue -> name
                            // tslint:disable-next-line: max-line-length
                            const fontFamilyItem = ((fonts[0].options as any[]).find((item) => item.macValue === data.content));
                            if (fontFamilyItem) {
                                // console.log(fontFamilyItem.name)
                                data.content = fontFamilyItem.name;
                            }
                        }
                    } else {
                        // TODO
                        data.content = '宋体';
                    }
                    iconClassName = '';
                    break;
                }
                case MenuItemIndex.FontSize: {
                    if (this.fontSize) {
                        const size = this.fontSize;
                        const fontSizeItem = ((fonts[1].options as any[]).find((item) => item.value == size));
                        if (fontSizeItem) {
                            data.content = fontSizeItem.name;
                        } else {
                            data.content = FONST_SIZE_PT.parse(size) || data.content;
                        }
                    } else { // null, set default val
                        data.content = '五号';
                    }
                    iconClassName = '';
                    break;
                }
                case MenuItemIndex.Bold:
                case MenuItemIndex.Underline:
                case MenuItemIndex.Italic:
                case MenuItemIndex.Sub:
                case MenuItemIndex.Sup: {
                    // console.log(this.oldProps)
                    if (this.oldProps) {
                        const boldVal = (this.oldProps.items.find((item) => item.name === data.index)).value;
                        if (boldVal === 1) { // 1 / true
                            className += 'active';
                        }
                    }
                    break;
                }
                case MenuItemIndex.Copy:
                case MenuItemIndex.Cut: {
                    if (this.bUse === false) {
                        className += 'disabled';
                    }
                    // disable click
                    break;
                }

                case MenuItemIndex.Paste: {
                    if (!this.state.bPasteEnabled) {
                        className += 'disabled';
                    }
                    break;
                }
                case MenuItemIndex.Undo: {
                    if (!this.bUndo) {
                        className += 'disabled';
                    }
                    break;
                }
                case MenuItemIndex.Redo: {
                    if (!this.bRedo) {
                        className += 'disabled';
                    }
                    break;
                }
                case MenuItemIndex.AutoFormat: {
                    className = 'autoFormat';
                    break;
                }

                // feature not done
                case MenuItemIndex.ClearFormats: {
                    className += 'disabled';
                    break;
                }

                case MenuItemIndex.Color:
                    colorBox = (<div className='color-indicator' style={{backgroundColor: this.textColor}}/>);
                    break;
                case MenuItemIndex.BackgroundColor:
                    colorBox = (<div className='color-indicator' style={{backgroundColor: this.backgroundColor}} />);
                    if (this.bRenderWhite) {
                        style = {};
                        style.color = '#fff'; // contrast to bgColor
                    }
                    break;
                default: {
                    break;
                }
            }

            if (data.disabled === true) {
                className += ' disabled';
            }
            const value = data.value;
            if (value !== undefined  && activeItems.find((item) => item.name === dataIndex && item.value === value)) {
                className += ' active';
            }
            let tip;
            if (data.tip) {
                tip = (<label>{data.tip}</label>);
            }
            const dynamicContent = (data.index !== 'align' && data.index !== MenuItemIndex.LineHeight
                && data.index !== MenuItemIndex.Image) ? true : false;
            let iconDom = <span style={style} className={iconClassName}>{dynamicContent ? data.content : ''}</span>;
            if (data.source) {
                const item = data.source as ICustomToolbarItem;
                iconDom = <span style={style} className={iconClassName}>
                    <img src={item.imgUrl} />
                    </span>;
            }
            return (
                // one item of toolbar
                <li
                    onClick={this.menuClick.bind(this, data)}
                    key={data.name + index}
                    data-index={data.index}
                    className={className}
                >
                    {iconDom}
                    {tip}
                    {colorBox}
                    {this.renderSelectFlag(data)}
                </li>
            );
        });
    }

    private renderListContainer(): any {  // toolbar line container

        let lineCount = 1;
        const datas = [...this.datas, ...this.customBarItems.map(item => {
            return {
                icon: 'custom',
                tip: item.tip,
                source: item,
            }
        })];
        let curLineIcons = datas;
        if (this.lineIconLimit !== -1) {
            lineCount = Math.ceil(this.datas.length / this.lineIconLimit);
            // curLineIcons = this.datas.slice(0, this.lineIconLimit);
        }

        const lines = []; // array of elements
        for (let i = 0; i < lineCount; i++) { // consider 2 lines for now
            let curLine: any;
            if (this.lineIconLimit !== -1) {
                if (i === 0) {
                    curLineIcons = datas.slice(0, this.lineIconLimit);
                    curLine = (
                        <ul ref={this.toolbarRef} key={'toolbar' + i}>
                            {this.renderList(curLineIcons)}
                            <li
                                className='moreIcons'
                                ref={this.moreIconRef}
                                onClick={this.moreIconClick}
                                // key={'moreIcon' + i}
                            >
                                <span className='more'/>
                                <i className='select-btn' />
                            </li>
                        </ul>
                    );
                } else if (i === lineCount - 1) {
                    curLineIcons = datas.slice(this.lineIconLimit)
                    .filter((data: IMenuItem) => data.index !== 'separator');
                    curLine = (
                        <ul ref={this.lastLineRef} key={'toolbar' + i} className='last-line'>
                            {this.renderList(curLineIcons)}
                        </ul>
                    );
                } else {
                    continue;
                }
            } else {
                curLine = (
                    <ul ref={this.toolbarRef} key={'toolbar' + i}>
                        {this.renderList(curLineIcons)}
                    </ul>
                );
            }
            lines.push(curLine);
        }
        return lines;
    }

    private renderDropdown(): any {
        const datas = this.selectData;
        if (!datas) {
            return null;
        }
        const prop = this.prop;
        const key = prop.name;
        const valueKey = prop.value;
        const selecteName = this.selecteName;
        const lineType = this.lineType;
        const activeMenuIndex = this.activeMenuIndex;
        const arrs = [MenuItemIndex.Image, MenuItemIndex.LineHeight];
        const lis = datas.map((data) => {
            const name = data[key];
            let className = null;
            let indicator = null;
            let icon = null;
            if (!arrs.find((arr) => arr === activeMenuIndex) && selecteName === name || MenuItemIndex.LineHeight === activeMenuIndex
            && lineType === data.value) {
                className = 'active'; // bg color
                indicator = (<i/>);
            }
            if (data.icon) { // align
                icon = (<span className={'iconfont ' + data.icon} />);
            }
            return (
                <li key={name} className={className} onClick={this.dropdownClick.bind(this, data)}>
                    {indicator}
                    {icon}
                    <span className={className}>{name}</span>
                </li>
            );
        });
        return (<ul>{lis}</ul>);
    }

    private renderColorPicker(): any {
        let colorPicker = null;
        if (this.activeMenuIndex === MenuItemIndex.Color) {
            colorPicker = (<div>your mother is flying!</div>);
        } else if (this.activeMenuIndex === MenuItemIndex.BackgroundColor) {
            colorPicker = (<div>your father is flying!</div>);
        }
        // onChangeComplete
        return (
            <SelectColor 
                color={this.color} 
                onChange={(color) => {
                    // 确保传递正确格式的颜色对象
                    if (typeof color === 'object' && color !== null) {
                        this.handlerChangeColor(color);
                    }
                }}  
            />
        );
    }

    private renderSelectFlag(data: IMenuItem): any {
        // dropdown mark
        if (data.selected === true) {
            return (<i className='select-btn' />);
        }

        return null;
    }

    private menuClick = (data: IMenuItem, e: any): void => {
        if (data.source) {
            if (this.host?.externalEvent?.nsoCustomToolbarEvent) {// 自定义按钮事件
                this.host.externalEvent.nsoCustomToolbarEvent(data.source);
            }
            return;
        }
        let flag: boolean;

        // disabled scenario
        if (this.bUse === false) { // not in selection
            if (data.index === MenuItemIndex.Cut || data.index === MenuItemIndex.Copy) {
                return;
            }
        }
        if (this.bUndo === false) {
            if (data.index === MenuItemIndex.Undo) {
                return;
            }
        }
        if (this.bRedo === false) {
            if (data.index === MenuItemIndex.Redo) {
                return;
            }
        }
        // any condition that should lock doc edit
        if (this.host.documentCore.isProtectedMode() || this.isCursorAtUneditableStruct()) {
            if (data.index !== MenuItemIndex.Print && data.index !== MenuItemIndex.Scale) {
                return;
            }
        }
        if (this.host.documentCore.isImageOnClick()) { // when focus on image, some icons will be disabled
            const enabledArray: any = [MenuItemIndex.Redo, MenuItemIndex.Undo, MenuItemIndex.Copy, MenuItemIndex.Paste,
                MenuItemIndex.Image, MenuItemIndex.Print, MenuItemIndex.Scale];
            if (!enabledArray.includes(data.index)) {
                return;
            }
        }
        if (!this.state.bPasteEnabled) {
            if (data.index === MenuItemIndex.Paste) {
                return;
            }
        }

        // special catch here if it has dropdown
        switch (data.index) {
            case MenuItemIndex.FontSize:
                this.selectData = fonts[1].options;
                break;
            case MenuItemIndex.FontFamily:
                this.selectData = fonts[0].options;
                break;
            case MenuItemIndex.JustifyAlign:
            case MenuItemIndex.RightAlign:
            case MenuItemIndex.CenterAlign:
            case MenuItemIndex.LeftAlign: {
                flag = this.menuAction.setParagraphAlignment(data.value) === 0;
                this.selectAlignType = data.value;
                if (flag !== undefined) {
                    this.refresh();
                }
                return;
            }
            case 'align':
                this.selectData = alignMenus;
                break;
            case MenuItemIndex.Color:
            case MenuItemIndex.BackgroundColor:
                // will show color picker
                break;
            case MenuItemIndex.LineHeight:
                this.selectData = lineHeights;
                break;
            case MenuItemIndex.Image:
                this.selectData = toolbarImages;
                break;
            case MenuItemIndex.Scale: {
                // this.bViewScale = true;
                const scaleValue = this.documentCore.getViewScale();
                this.selectData = undefined;
                if (scaleValue !== null) {
                    // this.scaleValue = parseInt((scaleValue * 100 as any), 10);
                    this.scaleValue = Math.round(scaleValue * 100);
                }
                break;
            }
            case MenuItemIndex.Numbered: {
                // this.bViewScale = true;
                flag = this.documentCore.createNum({type: NumberingType.Number});
                if (flag !== undefined) {
                    this.refresh();
                }
                return;
            }
            case MenuItemIndex.AutoFormat: {
                if (this.documentCore.deleteRedundantEx(true, true, true, false) === ResultType.Success) {
                    this.refresh();
                }
                return;
            }
            case MenuItemIndex.Bullet: {
                // this.bViewScale = true;
                flag = this.documentCore.createNum({type: NumberingType.Bullet});
                if (flag !== undefined) {
                    this.refresh();
                }
                return;
            }
            case MenuItemIndex.Print:
                flag = this.menuAction.menuItemClick(data, this.oldProps);
                // this.toolbarRefresh();
                return;
            default:
                this.selectData = null;
                // features that have no dropdown / need to reverse current state
                flag = this.menuAction.menuItemClick(data, this.oldProps);
                this.toolbarRefresh();
                this.refresh();
                break;
        }

        if (flag !== undefined) {
            if (flag === true) {
                this.refresh();
            }

            return;
        }

        const index = data.index;
        if (this.bReaderMoreMenu === true && !this.datas.slice(this.lineIconLimit)
        .find((item) => item.index === index)) {
            const dom = this.lastLineRef.current;
            dom.className = dom.className.replace(' active', '');
            this.bReaderMoreMenu = false;
        }

        this.activeMenuIndex = data.index;
        const currentTarget = e.currentTarget;
        // console.dir(currentTarget)
        this.bActive = true;
        this.selectActive = true;
        this.selecteName = data.content;
        let left = currentTarget.offsetLeft;
        if (this.bReaderMoreMenu) {
            left = parseInt(this.lastLineRef.current.style.left, 10) - 105;
            if (data.index === MenuItemIndex.Scale) {
                left -= 37;
            }
        }
        this.selectStyle = {
            left,
            top: currentTarget.offsetTop + currentTarget.clientHeight,
        };
        this.setState({});
    }

    private moreIconClick = (e: any): void => {
        // if (this.state.height === '40px') {
        //     // this.setState({height: 'auto'});
        // } else {
        //     this.setState({height: '40px'});
        // }
        // let toolbarElem = this.toolbarRef.current;
        // if (toolbarElem != null) {
        //     console.log(toolbarElem.style.height)
        //     if (toolbarElem.style.height.length === 0 || toolbarElem.style.height !== '100%') {
        //         toolbarElem.style.height = '100%';
        //     } else {
        //         toolbarElem.style.height = '40px';
        //     }
        // }
        this.bActive = true;
        const dom = this.lastLineRef.current;
        if (dom.className.indexOf('active') > -1) {
            dom.className = dom.className.replace(' active', '');
            this.bReaderMoreMenu = false;
        } else {
            dom.className += ' active';
            let li = e.target;
            if (li.tagName !== 'LI') {
                li = li.parentNode;
            }
            this.bReaderMoreMenu = true;
            dom.style.left = li.offsetLeft + 'px';
        }

        if (this.selectActive !== true) {
            return;
        }

        this.selectActive = false;
        this.setState({bRefresh: !this.state.bRefresh});
        // console.dir(e.target);
        // console.log(e.clientX, dom.offsetLeft)
    }

    private scaleClick = (e: any) => {
        const dataIndex = e.target.getAttribute('data-index');
        if (dataIndex === 'btn-1') {
            this.activeMenuIndex = undefined;
            this.bActive = false;
        } else if (dataIndex === 'btn-2') {
            IFRAME_MANAGER.setDocId(this.host.docId);
            this.bActive = true;
            const scale = this.scaleValue;
            if (scale == null) {
                message.error('请输入比例值');
                return;
            }

            if (scale > VIEW_SCALE.max) {
                message.error('最大值不能超过' + VIEW_SCALE.max);
                return;
            }
            if (scale < VIEW_SCALE.min) {
                message.error('最小值不能小于' + VIEW_SCALE.min);
                return;
            }
            const obj = {
                result: 0,
            };
            gEvent.setEvent(this.host.docId, gEventName.ViewScale, scale / 100, obj);
            this.activeMenuIndex = undefined;
            this.bActive = false;
        } else {
            this.bActive = true;
        }
    }

    private scaleFocus = (e: any) => {
        gEvent.setEvent(this.host.docId, gEventName.Blur);
    }

    private scaleBlur = (e: any) => {
        const val = e.target.value;
        const oldValue = this.scaleValue;
        const value = parseInt(val, 10);
        if (val && isNaN(value)) {
            e.target.value = oldValue;
            return;
        }
        if (!isNaN(value) && value !== oldValue) {
            this.scaleValue = value;
        } else {
            this.scaleValue = undefined;
        }
        this.setState({});
    }

    private scaleChange = (value: any): void => {
        this.scaleValue = value;
        this.setState({});
    }

    private dropdownClick = (data: any): void => {
        const prop = this.prop;
        const valueKey = prop.value;
        const key = prop.name;
        const activeMenuIndex: any = this.activeMenuIndex;

        // console.log(data, activeMenuIndex)

        // register dropdowm items!
        const dropdownIndexCollection = [MenuItemIndex.FontSize, MenuItemIndex.FontFamily,
            MenuItemIndex.LineHeight, MenuItemIndex.Image];

        if (dropdownIndexCollection.includes(activeMenuIndex)) {
            // set toolbar icon content
            const obj = this.datas.find((item) => item.index === activeMenuIndex);
            obj.content = data[key];
            // console.log(obj);

            if (obj.index === MenuItemIndex.FontFamily) {
                const fontFamily = ((fonts[0].options as any[]).find((item) => item.name === obj.content)).value;
                this.menuAction.setTextFontFamily(fontFamily);
            } else if (obj.index === MenuItemIndex.FontSize) {
                // '初号' -> 58.7
                const fontSize = ((fonts[1].options as any[]).find((item) => item.name === obj.content)).value;
                this.menuAction.setTextFontSize(fontSize);
            // } else if (obj.index === 'align') {
            //     const alignType = (alignMenus.find((item) => item.name === obj.content)).value;
            //     this.menuAction.setParagraphAlignment(alignType);
            } else if (obj.index === MenuItemIndex.LineHeight) {
                const paraSpacingType = (lineHeights.find((item) => item.name === obj.content)).value;
                const paraSpacing = +obj.content;
                this.menuAction.setParaProps(obj.index, {paraSpacingType, paraSpacing});
            } else if (obj.index === MenuItemIndex.Image) {
                const toolbarImage = (toolbarImages.find((item) => item.name === obj.content)).value;
                if (toolbarImage === 2) {
                    // insert image
                    this.insertImageRef.current.click();
                    return;
                } else {
                    this.menuAction.insertEquation();
                }
            } else {
                
            }

        }

        this.refresh();
    }

    private docClickEvent = (): void => {
        if (this.bActive === true) {
            this.bActive = false;
            return;
        }

        if (this.bReaderMoreMenu === true) {
            const dom = this.lastLineRef.current;
            dom.className = dom.className.replace(' active', '');
            this.bReaderMoreMenu = false;
            // return;
        }

        if (this.selectActive !== true) {
            return;
        }

        this.selectActive = false;
        this.setState({bRefresh: !this.state.bRefresh});
        // this.toolbarRefresh();
    }

    private refresh(): any {
        this.host.handleRefresh();
    }

    private toolbarRefresh(): any {
        this.setState({});
    }

    private handlerChangeColor = (color: {hex: string, rgb?: object, hsv?: any}): void => {
        const activeIndex = this.activeMenuIndex;
        if (activeIndex !== MenuItemIndex.Color
            && activeIndex !== MenuItemIndex.BackgroundColor) {
            return;
        }
        this.color = color.hex;
        // console.log(this.color)
        this.selectActive = false;
        
        // 兼容@uiw/react-color库，它可能不提供hsv属性
        if (color.hsv) {
            const hsv = color.hsv;
            if (hsv.s > 0.5 || hsv.v < 0.8) {
                this.bRenderWhite = true;
            } else {
                this.bRenderWhite = false;
            }
        } else if (color.rgb) {
            // 如果没有hsv属性，尝试从rgb计算
            const rgb = color.rgb as any;
            const hsv = this.parseHSB([rgb.r || 0, rgb.g || 0, rgb.b || 0]);
            if (hsv[1] > 0.5 || hsv[2] < 0.8) {
                this.bRenderWhite = true;
            } else {
                this.bRenderWhite = false;
            }
        } else {
            // 如果既没有hsv也没有rgb，则从hex计算
            this.parseColor(color.hex);
        }

        if (activeIndex === MenuItemIndex.Color) {
          this.menuAction.setTextColor(this.color);
          this.textColor = this.color;
        } else if (activeIndex === MenuItemIndex.BackgroundColor) {
          this.menuAction.setBackgroundColor(this.color);
          this.backgroundColor = this.color;
        } else {
            // console.log('r u fuking kidding me?')
        }
        this.activeMenuIndex = undefined;
        this.toolbarRefresh();
        this.refresh();
    }

    private parseColor(color: string): void {
        if (!color) {
            this.bRenderWhite = false;
            return;
        }
        const arrs: number[] = [];
        color = color.replace('#', '');
        if (color.length === 6) {
            arrs.push(parseInt(color.slice(0, 2) , 16), parseInt(color.slice(2, 4) , 16),
            parseInt(color.slice(4, 6) , 16));
        } else {
            arrs.push(parseInt(color.slice(0, 1) , 16), parseInt(color.slice(1, 2) , 16),
            parseInt(color.slice(2, 3) , 16));
        }
        const hsv = this.parseHSB(arrs);
        if (hsv[1] > 0.5 || hsv[2] < 0.8) {
            this.bRenderWhite = true;
        } else {
            this.bRenderWhite = false;
        }
    }

    private parseHSB(arr: number[]): number[] {
        // tslint:disable-next-line: one-variable-per-declaration
        let h = 0, s = 0, v = 0;
        // tslint:disable-next-line: one-variable-per-declaration
        const r = arr[0], g = arr[1], b = arr[2];
        arr.sort((a, second) => {
            return a - second;
        });
        const max = arr[2];
        const min = arr[0];
        v = max / 255;
        s = max === 0 ? 0 : 1 - (min / max);
        if (max === min) {
            h = 0; // 事实上，max===min的时候，h无论为多少都无所谓
        } else if (max === r && g >= b) {
            h = 60 * ((g - b) / (max - min)) + 0;
        } else if (max === r && g < b) {
            h = 60 * ((g - b) / (max - min)) + 360;
        } else if (max === g) {
            h = 60 * ((b - r) / (max - min)) + 120;
        } else if (max === b) {
            h = 60 * ((r - g) / (max - min)) + 240;
        }
        h = h / 100;
        // s = parseInt(s * 100);
        // v = parseInt(v * 100);
        return [h, s, v];
    }

    private handleChangeImage = (e: any) => {

        const inputTarget = e.target as HTMLInputElement;
        // tslint:disable-next-line: newline-per-chained-call
        this.menuAction.handleChangeImage(inputTarget).then((result) => {
            // trick input field to "onChange" every time
            inputTarget.value = '';
            // this.refresh();
        // tslint:disable-next-line: newline-per-chained-call
        }).catch((error) => {
            // console.log(error);
        });

    }

    private checkPasteEnabled(): void {
        setTimeout(() => {
            this.menuAction.isPasteEnabled()
            .then((bPasteEnabled) => {
                if (this.state.bPasteEnabled !== bPasteEnabled) {
                    this.setState({bPasteEnabled});
                }
            });
        }, 10);
    }

    private isCursorAtUneditableStruct(): boolean {
        const curStruct = this.host.documentCore.getCurrentNewControl();
        if (curStruct != null && curStruct.isReadOnly()) {
            return true;
        }
        return false;
    }

    private checkAlignment(): void {
        // const alignment: AlignType = this.host.documentCore.getParagraphProperty().alignment;
        // const paraProp = this.documentCore.getParagraphProperty();
        // let alignMentContent = '左对齐';
        // if ( paraProp ) {
        //     const alignment = paraProp.alignment;

        //     switch (alignment) {
        //         case AlignType.Left: {
        //             alignMentContent = '左对齐';
        //             break;
        //         }
        //         case AlignType.Center: {
        //             alignMentContent = '居中';
        //             break;
        //         }
        //         case AlignType.Right: {
        //             alignMentContent = '右对齐';
        //             break;
        //         }
        //         case AlignType.Justify: {
        //             alignMentContent = '两端对齐';
        //             break;
        //         }
        //         default: {
        //             break;
        //         }
        //     }
        //     const lineHeight = paraProp.paraSpacing.lineSpacing;
        //     const arrs = [1.479, 1.7098, 1.949];
        //     let lineType = LineSpacingType.Single;
        //     if (arrs[2] - lineHeight < 0.01) {
        //         lineType = LineSpacingType.Double;
        //     } else if (arrs[1] - lineHeight < 0.01) {
        //         lineType = LineSpacingType.SingeHalf;
        //     }

        //     this.lineType = lineType;
        // }

        // const alignObj = this.datas.find((item) => item.index === 'align');
        // alignObj.content = alignMentContent;
    }

}
