import { IRevisionChange } from "@/common/commonDefines";
import Select from "@/components/editor/ui/select/Select";
import { ParagraphContentPos } from "@/model/core/Paragraph/ParagraphContent";
import React from "react";
import './style.less';

export interface IRevisions extends IRevisionChange {
    ids: number[];
    pagePos: ParagraphContentPos;
    count: number;
    key: string;
}

interface IProps {
    height: number;
    revisions: IRevisions[];
    userName: string;
    handleActive: (rev: IRevisions) => void; 
}

interface IState {
    activeKey: string;
    handleKey: string;
    handleIds: number[];
    selectedUser: string;
}

export class RevisionPanel extends React.Component<IProps, IState> {
    private selection: {
        key: string;
        value: string;
    }[];
    private ulRef: React.RefObject<HTMLUListElement>;
    private needRefresh: boolean = false;
    private isDbClick: boolean = false;
    
    constructor(props: IProps) {
        super(props);
        this.selection = [{
            key: '所有',
            value: '',
        }];
        this.state = {
            activeKey: '',
            handleKey: '',
            handleIds: [],
            selectedUser: '',
        };
        this.ulRef = React.createRef();
    }


    public render() {
        const {dels, full, inss, collection} = this.groupRevisions();
        return (
            <div className="revision-panel">
                <div className="header">
                    <div className="summary">
                        <label >摘要:</label>
                        <span>共<span className="place">{full}</span>处修订</span>
                    </div>
                    <div className="summary">
                        <label >新增:</label>
                        <span><span className="place">{inss}</span>处</span>
                    </div>
                    <div className="summary">
                        <label >删除:</label>
                        <span><span className="place">{dels}</span>处</span>
                    </div>
                    <div className="summary">
                        <Select
                            name="selection"
                            data={this.selection}
                            value={this.state.selectedUser}
                            onChange={this.handleSelectionChange}
                        />
                    </div>
                </div>
                <ul ref={this.ulRef} className="list">
                    {this.renderRevisionItems(collection)}
                </ul>
            </div>
        );
    }

    /**
     * 添加修订选中样式
     * @param id portion id
     */
    public activeRevision(id: number): void {
        // 规避双击面板时导致的重复激活
        if (this.isDbClick) {
            this.isDbClick = false;
            return;
        }
        const revisions = this.props.revisions.filter(item => item.ids.includes(id));
        if (revisions.length) {
            const sortedRevs = revisions.sort((a, b) => a.count - b.count);
            const target = sortedRevs[0];
            if (target.key === this.state.activeKey) return;
            this.needRefresh = true;
            this.setState({activeKey: target.key, handleKey: '', handleIds: []});
        } else if (this.state.activeKey) {
            this.needRefresh = true;
            this.setState({activeKey: '', handleKey: '', handleIds: []});
        }
    }

    public componentDidMount(): void {
        this.refreshActiveRevisionDom();
    }

    public componentDidUpdate(prevProps: Readonly<IProps>, prevState: Readonly<IState>, snapshot?: any): void {
        this.refreshActiveRevisionDom();
    }

    private renderRevisionItems(collection: IRevisions[]) {
        if (!collection.length) {
            return (
                <li className="item-empty">
                    目前尚未有修订内容
                </li>
            );
        }
        const {activeKey, handleKey} = this.state;
        let isFirstActiveKey = true;
        return collection.map(item => {
            let className = 'item';
            if (handleKey) {
                if (handleKey === item.key) {
                    className += ' active';
                }
            } else if (item.key === activeKey && isFirstActiveKey) {
                className += ' active';
                isFirstActiveKey = false;
            }
            return (
                <li className={className} key={item.ids[0] + item.userName + item.userId} onDoubleClick={this.handleActive(item)}>
                    <div className="title">
                        <strong>{item.userName}</strong> 于 {item.time.toLocaleString()} {item.type === 1 ? '新增内容' : '删除内容'}
                    </div>
                    <div className="rev-content">
                        {item.value}
                    </div>
                </li>
            );
        });
    }

    private groupRevisions() {
        const {revisions, userName} = this.props;
        const nameSet = new Set([userName]);
        const groups = {
            dels: 0,
            full: 0,
            inss: 0,
        };
        const collection = [];
        for (const item of revisions) {
            groups.full++;
            if (item.type === 1) {
                groups.inss++;
            } else if (item.type === 2) {
                groups.dels++;
            }
            if (this.state.selectedUser === item.userName) {
                collection.push(item);
            }
            nameSet.add(item.userName);
        }
        
        this.selection = [this.selection[0], ...Array.from(nameSet, name => ({key: name, value: name}))];
        
        return {
            ...groups,
            collection: this.state.selectedUser ? collection : revisions,
        }

    }

    private handleActive = (rev: IRevisions) => {
        return (e: any) => {
            e.stopPropagation();
            if (rev.key === this.state.handleKey) return;
            this.isDbClick = true;
            this.setState({activeKey: '', handleKey: rev.key, handleIds: [...rev.ids]});
            setTimeout(() => {
                this.props.handleActive(rev);
                //! 后续步骤在 内部事件循环处理之后执行 （如 this.activeRevision）
                this.isDbClick = false;
            }, 0);
        }
    }

    private handleSelectionChange = (value: string) => {
        if (this.state.selectedUser !== value) {
            this.setState({selectedUser: value});
        }
    }

    /** 刷新被激活的修订滚动 */
    private refreshActiveRevisionDom() {
        if (!this.needRefresh) return;
        this.needRefresh = false;
        const uldom = this.ulRef.current as HTMLUListElement;
        if (!uldom || !this.state.activeKey) return;
        const lidom = uldom.querySelector('li.active') as HTMLLIElement;
        if (!lidom) return;
        if (
            (uldom.scrollTop > lidom.offsetTop) // 在上隐藏区域
            || (uldom.scrollTop + uldom.offsetHeight < lidom.offsetTop + lidom.offsetHeight) // 在下隐藏区域
        ) {
            uldom.scrollTop = lidom.offsetTop;
        }
    }

}