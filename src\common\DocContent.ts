import DocumentContentElementBase from '../model/core/DocumentContentElementBase';
import { INewControlProperty } from './commonDefines';

interface IHdrFtrProp {
    bFirstPageDiff: boolean;
    bProtectHeaderFooter: boolean;
    bProtectHeaderFooterSaved?: boolean;
    bShowHeader: boolean;
    bShowFooter: boolean;
    bShowHeaderBorder?: boolean;
    bShowFooterBorder?: boolean;
}

interface IHeaderFooterProps {
    content: DocumentContentElementBase[];
    property?: object;
}

interface IHeaderProps {
    headerContent: Record<number, DocumentContentElementBase[]>;
    property?: object;
}

interface IFooterProps {
    footerContent: Record<number, DocumentContentElementBase[]>;
    property?: object;
}

interface IHeaderFooterOption {
    // header: IHeaderFooterProps;
    header: IHeaderProps;
    footer: IFooterProps;
}

export interface IDocStrcutInfo {
    name: string;
    parentName: string;
    start: any;
    end: any;
}

export interface IDocContent {
    pageProperty: any;
    headerFooters: IHeaderFooterOption[];
    headerFooterProp?: IHdrFtrProp;
    content: DocumentContentElementBase[];
    structs: INewControlProperty[];
    regions: INewControlProperty[];
    comments?: any[];
    styles?: {background: any};
    sectionInfo?: any[];
}
