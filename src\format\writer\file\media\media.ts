import { XmlComponent, XmlAttributeComponent } from '../xml-components';
import { IXmlResult } from '../xml-components/base';
import { Audios, Images, Paintings, Videos} from './images';

export interface IMediaAttributesProperties {
  readonly w?: string;
}
export class MediaAttributes extends XmlAttributeComponent<IMediaAttributesProperties> {
  protected readonly xmlKeys: any = {
      w: 'xmlns:w',
  };
}

export class Media extends XmlComponent {

  constructor() {
    super('Media');
    this.root.push(
      new MediaAttributes({
          w: 'http://schemas.openxmlformats.org/wordprocessingml/2006/main',
      }),
    );
  }

  public addImageContainer(imageContainer: Images): Media {
    this.root.push(imageContainer);
    return this;
  }

  public addAudioImageContainer(container: Audios): Media {
      this.root.push(container);
      return this;
  }

  public addVideoImageContainer(container: Videos): Media {
      this.root.push(container);
      return this;
  }

  public addPaintingContainer(paintingContainer: Paintings): Media {
    this.root.push(paintingContainer);
    return this;
  }

  public prepForXml(): IXmlResult {
    const result = super.prepForXml();
    const key = this.rootKey;
    const text = `<${key}${result.attrs}>${result.text}</${key}>`;
    return {
      text,
      attrs: null
    };
  }
}
