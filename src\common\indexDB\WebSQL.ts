export interface IOpenType {
    name: string;
    version: number;
    description: string;
    size: number;
}
export class WebSQL {
    private db: any;
    private keys: string[];
    constructor(options: IOpenType) {
        this.open(options);
    }

    public createTable(tableName: string, keyOption: object): void {
        if (!this.db || !keyOption) {
            return;
        }

        const keys = this.keys = Object.keys(keyOption);

        this.db.transaction((tx) => {
            // tslint:disable-next-line: max-line-length
            tx.executeSql(`CREATE TABLE IF NOT EXISTS ${tableName} (id INTEGER PRIMARY KEY AUTOINCREMENT,${keys.join(',')})`,
            [], (xx, result) => {
                // console.log(result);
            }, (xx, err) => {
                // console.log(err);
            });
        });
    }

    public removeTable(tableName: string): void {
        if (!this.db) {
            return;
        }
        this.db.transaction((tx) => {
            tx.executeSql(`DROP TABLE ${tableName}`,
            [], (xx, result) => {
                // console.log(result);
            }, (xx, err) => {
                // console.log(err);
            });
        });
    }

    public add(tableName: string, data: object): void {
        if (!this.db || !data) {
            return;
        }
        const keys = this.keys;
        const values = [];
        const values1: string[] = [];
        keys.forEach((key) => {
            values1.push('?');
            values.push(data[key]);
        });
        const str = `INSERT INTO ${tableName} (${keys.join(',')}) VALUES (${values1.join(',')})`;
        this.db.transaction((tx) => {
            tx.executeSql(str,
            values, (xx, result) => {
                // console.log(result);
            }, (xx, err) => {
                // console.log(err);
            });
        });
    }

    public query(tableName: string, query: string): Promise<any[]> {
        return new Promise((resolve, reject) => {
            this.db.transaction((tx) => {
                tx.executeSql(`SELECT * FROM ${tableName}${query || ''}`, [], (xx, result) => {
                    resolve(result.rows);
                }, (xx, err) => {
                    console.log(err);
                });
            });
        });
    }

    public delete(tableName: string, query: string): Promise<number> {
        return new Promise((resolve, reject) => {
            this.db.transaction((tx) => {
                tx.executeSql(`delete FROM ${tableName}${query || ''}`, [], (xx, result) => {
                    resolve(result.rows.length);
                }, (xx, err) => {
                    console.log(err);
                });
            });
        });
    }

    public sum(tableName: string): Promise<number> {
        return new Promise((resolve, reject) => {
            this.db.transaction((tx) => {
                tx.executeSql(`SELECT count(id) FROM ${tableName}`, [], (xx, result) => {
                    let num = 0;
                    if (result.rows.length) {
                        num = result.rows[0]['count(id)'];
                    }
                    resolve(num);
                }, (xx, err) => {
                    console.log(err);
                });
            });
        });
    }

    private open(options: IOpenType): void {
        // 'logs', 10001, 'test', 50 * 1024 * 1024
        try {
            this.db = window['openDatabase'](options.name, options.version, options.description, options.size);
        } catch (error) {
            console.log(error);
        }
    }
}
