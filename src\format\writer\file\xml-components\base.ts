
export interface IXmlResult {
    attrs: string;
    text: string;
}

export abstract class BaseXmlComponent {
    protected readonly rootKey: string;
    // tslint:disable-next-line:readonly-keyword
    protected deleted: boolean = false;

    constructor(rootKey: string) {
        this.rootKey = rootKey;
    }

    public abstract prepForXml(): IXmlResult;

    public get IsDeleted(): boolean {
        return this.deleted;
    }

    public getRootKey(): string {
        return this.rootKey;
    }
}

var XML_CHARACTER_MAP = {
    '&': '&amp;',
    '"': '&quot;',
    "'": '&apos;',
    '<': '&lt;',
    '>': '&gt;'
};

export function escapeForXML(string) {
    return string && string.replace
        ? string.replace(/([&"<>'])/g, function(str, item) {
            return XML_CHARACTER_MAP[item];
          })
        : string;
}
