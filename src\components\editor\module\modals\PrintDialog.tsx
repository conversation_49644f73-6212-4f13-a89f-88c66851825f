import PrintDialogModel from '../../../../common/print/PrintDialog';
import {PrintContent} from '../PrintContent';
import ReactDOM from 'react-dom/client';
import * as React from 'react';
import {printStyle} from '../../../../common/css/style';
import { CleanModeType, getEditorDomContainer, PrintType } from '../../../../common/commonDefines';
import { getPrintReviewStyle, getCPrintStyle } from '../../../../common/css/style';
import { EmrEditor } from '../../Main';
import '../../style/print.less';
/* IFTRUE_WATER */
import MarkFactory from '@/common/MarkFactory';
import { getCustomFontFaceStyleHtml } from '@/model/core/util-custom-font';
/* FITRUE_WATER */
import { editorCallback } from '@/common/GlobalTest';
let isHasMessageStyle: boolean = false;
let isCHasMessageStyle: boolean = false;

window['bPrintDouble'];
export default class PrintDialog extends PrintDialogModel {
    // public src: string;
    // private docId: number;
   
    private optionProps: any;
    private bOpenCliented: boolean;
    
    constructor(doc?: any, host: EmrEditor = null) {
        super(doc, host);
        // this.src = 'about:link';
        this.src = 'about:blank'; // cross origin
        this.printData = null;
        this.oPrintContentRef = React.createRef();
    }

    public open(isPrint: boolean, option?: any): void {
        if (isPrint) {
            /* IFTRUE_WATER */
            // 取消水印生成
            MarkFactory.getInstance().setBGenStart(false);
            /* FITRUE_WATER */
        }
        this.clearDom();
        this.isCPrint = false;
        this.option = option;
        this.isPrint = isPrint;
        this.addDialog();
    }

    public clearContent(): void {
        this.documentCore.clearContent();
    }

    public clear(): void {
        this.clearDom();
        this.documentCore = null;
        this.isPrint = false;
        this.isCPrint = false;
    }

    public getDocumentCore(): any {
        return this.documentCore;
    }

    public async openCDialog(option: any): Promise<void> {
        this.clearDom();
        await this.getPrintDouble();
        this.option = option;
        this.optionProps = option;
        this.isCPrint = true;
        if (this.isCPrint) {
            /* IFTRUE_WATER */
            // 取消水印生成
            MarkFactory.getInstance().setBGenStart(false);
            /* FITRUE_WATER */
        }
        // this.option = option;
        this.intPrintData(option);
        // console.log(this.printData)
        this.isPrint = this.printData.bDirectPrint;
        // try {
        //     printers = await this.getPrinters();
        // } catch (e) {
        //     console.log(e);
        //     message.error('cannot retrieve printers.');
        //     return ;
        // }
        let printerObj = await this.getPrinters()
        .catch((err) => {
            console.log(err);
            if (option != null) { // existing option means from API
                // const host = option.printerResultManager.getHost();
                // gEvent.setEvent(host.docId, GlobalEventName.PrinterResult, false);
                // console.log(option)
                option.callback(false);
            } else {
                console.log('第一次遍历找不到打印机列表');
            }

            // return ;
        });
        if (!printerObj) {
            this.openPrintClient();
            printerObj = await this.forEachClient();
            // if (!printerObj) {
            //     message.error('无法获取打印机列表', {title: '系统通知', btns: ['确认'], specialBlessing: true});
            // }
        }
        // console.log(printerObj);
        let printers = [];
        if (printerObj != null && printerObj['printers_name'] != null) {
            printers = printerObj['printers_name'];
        }
        // console.log(printers);
        // printerObj is undefined if rejected
        if (printerObj != null) {
        // if (printers.length > 0) {
            if ( this.isPrint &&
                (null == this.printData.printerName || '' === this.printData.printerName) ) {
                this.printData.printerName = printers[0];
            }

            if ( printers && 0 < printers.length && option && option.printerName && '' !== option.printerName ) {
                const index = printers.findIndex((value) => value === option.printerName);
                if ( 0 < index ) {
                    printers.splice(index, 1);
                    printers.unshift(option.printerName);
                }
            }

            this.addCDialog(printers);
        }
        // const startCPrint = await this.startCPrint();
        // console.log(startCPrint)
        if (option != null) { // existing option means from API
            // as long as getPrinters() is valid, regard as success
            option.callback(true);
        }
    }

    public onClose(fn: () => void): void {
        this.closeFn = fn;
    }

    private async  addDialog(): Promise<void> {
        const pagePro = this.getPagePro() as any;
        if (this.print) {
            this.intPrintDialog(pagePro);
            return;
        }
        let className = '';
        if (this.isPrint) {
            className = ' print-hide';
        }
        const src = this.src + '?time=' + (new Date()).getTime();
        const dom = document.createElement('div');
        const logicDocument = this.documentCore.getDocument();
        const bGridChecked = logicDocument.isDynamicGridLine() ? 'checked' : '';
        const bShowWaterMark = logicDocument.hasTextWaterMark() ? 'checked' : '';
        let bContinue = '';
        let bStartpage = '';
        const {startPageHdrFtr, startName} = this.option || {};
        if (startName) {
            bContinue = ' checked id="cont-checked"';
            this.option.type = PrintType.Continuous;
        }
        if (startPageHdrFtr === 1) {
            bStartpage = ' checked';
            this.option.startPageHdrFtr = true;
        }
        dom.id = 'print-dialog';
        dom.className = 'print-dialog' + className;
        // 页眉页脚：<input class='header-box' type='checkbox' data-type='1' checked='checked'  value='1' />
        dom.innerHTML = `
        <div class='print-dialog-header'>
            <span class='header-text'>预览打印</span>
            <span class='close-tag close X-line'></span>
        </div>
        <div class='print-dialog-box' style='max-width: ${pagePro.width + 20}px'>
            <div class='print-dialog-box-header'>
                <input type='checkbox'${bContinue} class='cont-print'/> 续打
                <div class='continue-block'>
                    <input type='checkbox'${bStartpage} class='cont-startpage-hf'/> 起始页打印页眉页脚
                </div>
                <input type='checkbox' class='batch-print'/> 套打
                <input type='checkbox' class='clean-mode' checked/> 清洁模式
                <input type='checkbox' class='show-watermark' ${bShowWaterMark}/> 显示水印
                <input type='checkbox' class='print-landscape'/> 水平打印
                <input type='checkbox' class='print-gridline' ${bGridChecked}/> 打印网格线
                

                <div class='print-dialog-box-header-clinc hidden'>
                    <p>
                        <input type='checkbox' class='clinc-model'  />门诊模式
                    </p>
                    <p class='clinc-radio-group'>
                        <input type='radio' value='top' disabled  name='clinc' class='clinc-radio'/>上页
                        <input type='radio' value='bottom' disabled  name='clinc' class='clinc-radio'/>下页
                    </p>
                </div>
                <div class='btns'>
                    <button type='6' class='close'>
                        <span class='closePreivew'></span>关闭
                    </button>
                    <button class='print-button' type='1'>
                        <span class='printPreview'></span>打印
                    </button>
                </div>
            </div>
            <div class='print-dialog-box-body'>
                <iframe src='${src}' style='width: 100%; height: 100%;' frameborder='0'></iframe>
            </div>
        </div>`;

        const container = getEditorDomContainer();
        if (isHasMessageStyle === false) {
            isHasMessageStyle  = true;
            const style = document.createElement('style');
            style.type = 'text/css';
            style.innerHTML = getPrintReviewStyle();
            container.appendChild(style);
        }
        container.appendChild(dom);
        await this.initIframe(dom);
        this.addEvent();
        this.updateContent();

        // print pre dialog from menu would never have CleanModeType.CleanModeSpecial type
        // const cleanModeDom: any = dom.querySelector('#clean-mode-predialog');
        // cleanModeDom.checked = (this.printData.clearMode === CleanModeType.CleanMode);
        // if (this.printData.clearMode === CleanModeType.CleanModeSpecial) {
        //     cleanModeDom.disabled = true;
        // }

        // gEvent.addEvent(this.docId, gEventName.UnMounted, this.deleteEvent);
        this.print.className = dom.className.replace(' visible', '');
        setTimeout(() => {
            if ( null != this.print ) {
                this.print.className += ' active';
            }
        }, 0);
        // console.log(date.getMinutes() + ':' + date.getSeconds() + '.' + date.getMilliseconds());
    }

    private addCDialog(printerList: string[]): void {
        // console.log(printerList)
        const pagePro = this.getPagePro() as any;
        if (this.print) {
            this.intPrintDialog(pagePro);
            setTimeout(() => {
                (this.print as any).style.height = '100%';
            }, 50);
            return;
        }
        let className = '';
        if (this.isPrint) {
            className = ' print-hide';
        }
        const src = this.src + '?time=' + (new Date()).getTime();
        const dom = document.createElement('div');
        dom.id = 'print-dialog-c';
        if (this.optionProps) {
            className += ' print-nis';
        }
        dom.className = 'print-dialog-c' + className;
        // console.log(pagePro.width)
        let printers = [];
        if (printerList != null && printerList.length > 0) {
            printers = printerList;
        } else {
            printers = ['printer1', 'printer2', 'printer3'];
        }
        let printerDom = `<select id='printer-select' class='dim-text'>`;
        for (const printer of printers) {
            const option = `<option value='${printer}'>${printer}</option>`;
            printerDom += option;
        }
        printerDom += '</select>';
        let bStartHeaderPage = '';
        if (this.printData.startPageHdrFtr) {
            bStartHeaderPage = ' checked';
        }
        const logicDocument = this.documentCore.getDocument();
        const bGridChecked = logicDocument.isDynamicGridLine() ? 'checked' : '';
        const bShowWaterMark = logicDocument.hasTextWaterMark() ? 'checked' : '';

        dom.innerHTML = `<div class='print-dialog-box-c' style='width: ${pagePro.width + 348}px;'>
            <div class='print-dialog-box-body-c'>
                <iframe src='${src}' style='width: 100%; height: 100%;' frameborder='0'></iframe>
                <div style='margin-top: -4px; width: 100%; padding: 6px 0; background: rgb(235, 239, 242); visibility: hidden;' id="curPageBar">
                    <div style='margin-left: 10px;' class='jumpPage-side jumpPage-left'>
                        <svg id='jumpHead' viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"><path fill="#54627B" d="M126.08 546.112l320 320 5.44 4.672a48.32 48.32 0 0 0 62.4-4.672l4.672-5.504 2.56-4.096a48 48 0 0 0-7.232-58.304l-286.08-286.08 286.08-286.08 4.672-5.44a48.32 48.32 0 0 0-4.672-62.4 48 48 0 0 0-67.84 0l-320 320-4.672 5.504-0.896 1.152a48 48 0 0 0 5.568 61.248z"></path><path fill="#54627B" d="M510.08 546.112l320 320 5.44 4.672a48.32 48.32 0 0 0 62.4-4.672l4.672-5.504 2.56-4.096a48 48 0 0 0-7.232-58.304l-286.08-286.08 286.08-286.08 4.672-5.44a48.32 48.32 0 0 0-4.672-62.4 48 48 0 0 0-67.84 0l-320 320-4.672 5.504-0.896 1.152a48 48 0 0 0 5.568 61.248z" ></path></svg>
                        <div style='width: 0px; height: 16px; border: 1px solid #ACB4C1;'></div>
                        <svg id='jumpPre' viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"><path fill="#54627B" d="M318.08 546.112l320 320 5.44 4.672a48.32 48.32 0 0 0 62.4-4.672l4.672-5.504 2.56-4.096a48 48 0 0 0-7.232-58.304l-286.08-286.08 286.08-286.08 4.672-5.44a48.32 48.32 0 0 0-4.672-62.4 48 48 0 0 0-67.84 0l-320 320-4.672 5.504-0.896 1.152a48 48 0 0 0 5.568 61.248z"></path></svg>
                    </div>
                    <div class='jumpPage-center'>
                        <input type='text' id='singlePage' style='text-align: center' readonly />共<span id='fullPage'></span>页
                    </div>
                    <div style='margin-right: 0px;' class='jumpPage-side jumpPage-right'>
                        <svg id='jumpNext' viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"><path fill="#54627B" d="M705.92 477.888l-320-320-5.44-4.672a48.32 48.32 0 0 0-62.4 4.672l-4.672 5.504-2.56 4.096a48 48 0 0 0 7.232 58.304l286.08 286.08-286.08 286.08-4.672 5.44a48.32 48.32 0 0 0 4.672 62.4 48 48 0 0 0 67.84 0l320-320 4.672-5.504 0.896-1.152a48 48 0 0 0-5.568-61.248z"></path></svg>
                        <div style='width: 0px; height: 16px; border: 1px solid #ACB4C1;'></div>
                        <svg id='jumpTail' viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"><path fill="#54627B" d="M897.92 477.888l-320-320-5.44-4.672a48.32 48.32 0 0 0-62.4 4.672l-4.672 5.504-2.56 4.096a48 48 0 0 0 7.232 58.304l286.08 286.08-286.08 286.08-4.672 5.44a48.32 48.32 0 0 0 4.672 62.4 48 48 0 0 0 67.84 0l320-320 4.672-5.504 0.896-1.152a48 48 0 0 0-5.568-61.248z"></path><path fill="#54627B" d="M513.92 477.888l-320-320-5.44-4.672a48.32 48.32 0 0 0-62.4 4.672l-4.672 5.504-2.56 4.096a48 48 0 0 0 7.232 58.304l286.08 286.08-286.08 286.08-4.672 5.44a48.32 48.32 0 0 0 4.672 62.4 48 48 0 0 0 67.84 0l320-320 4.672-5.504 0.896-1.152a48 48 0 0 0-5.568-61.248z"></path></svg>
                    </div>
                </div>
            </div>
            <div class='print-dialog-box-side'>
                <div class='print-box'>
                    <span>打印</span>
                    <span class='close X-line'></span>
                </div>
                <p class='cprint-header'>${this.printData.title}</p>
                <p>
                    <span class='dim-text'>打印机:</span> ${printerDom}
                </p>
                <div class='split-container'>
                    <div class='left-part'>
                        <span class='dim-text'>页码:</span>
                    </div>
                    <div class='right-part dim-text'>
                        <p>
                            <input type='radio' value='all' name='pageNum' class='clinc-radio all-print' checked/>全部
                        </p>
                        <p>
                            <input type='radio' value='single' name='pageNum' class='clinc-radio single-print'/>当前页
                            <span class='cont-single-page-text hidden'>
                                ：（第&nbsp;<span id='singlePageSpan' style='font-weight: bold;'>1</span>&nbsp;页）
                            </span>

                        </p>
                        <p>
                            <input type='radio' value='continous' name='pageNum' class='clinc-radio continuous-print'/>续打
                            <span class='cont-startpage-hf-text${bStartHeaderPage ? '' : ' hidden'}'>
                                <input type='checkbox' class='cont-startpage-hf'${bStartHeaderPage}/>
                                <span class='cont-startpage-hf-text-2'>起始页打印页眉页脚</span>
                            </span>
                        </p>
                        <p>
                            <input type='radio' value='batch' name='pageNum' class='clinc-radio continuous-print'/>套打
                        </p>
                        <p>
                            <input type='radio' value='specific' name='pageNum' class='clinc-radio specific-print'/>范围
                            <input type='text' id='specific-page' placeholder='例:1-5、8、11-13'/>
                        </p>
                    </div>
                </div>
                <p>
                    <span name='print-count' class='dim-text'>打印份数:</span>
                    <input id='print-count-number' type='number' min='1' max='99' step='1' value='1'>
                </p>
                <div class='split-container'>
                    <div class='left-part'>
                        <span class='dim-text'>更多设置:</span>
                    </div>
                    <div class='right-part right-part-more-configs dim-text'>
                        <p>双面打印<input id='double-print' name='double-print' class='c-checkbox' type='checkbox' /></p>
                        <p>清洁模式<input id='clean-mode' name='clean-mode' class='c-checkbox clean-mode' type='checkbox' /></p>
                        <p>显示水印<input id='show-watermark' name='show-watermark' class='c-checkbox show-watermark' type='checkbox' ${bShowWaterMark} /></p>
                        <p>水平打印<input id='print-landscape' name='print-landscape' class='c-checkbox print-landscape' type='checkbox' /> </p>
                        <p>打印网格线<input id='print-gridline' name='print-gridline' class='c-checkbox print-gridline' type='checkbox' ${bGridChecked}/></p>
                    </div>
                </div>
                <div class='btns'>
                    <button class='close c-button' >取消</button>
                    <button class='print c-button' >打印</button>
                </div>
            </div>
        </div>`;

        const container = getEditorDomContainer();
        if (isCHasMessageStyle === false) {
            isCHasMessageStyle  = true;
            const style = document.createElement('style');
            style.type = 'text/css';
            style.innerHTML = getCPrintStyle();
            container.appendChild(style);
        }
        const {_startName} = this.option || {};

        container.appendChild(dom);
        this.initIframe(dom, {hidden: 'hidden'});
        this.addCEvent();
        this.updateContent(true, true);

        if ( !this.isPrint ) {
            // gEvent.addEvent(this.docId, gEventName.UnMounted, this.deleteEvent);
            // console.log(this.print)
            this.print.className = dom.className.replace(' visible', '');
            (dom.querySelector('#double-print') as any).checked = this.printData.printDouble;
            const cleanModeDom: any = dom.querySelector('#clean-mode');
            cleanModeDom.checked = (this.printData.clearMode === CleanModeType.CleanMode);
            if (this.printData.clearMode === CleanModeType.CleanModeSpecial) {
                cleanModeDom.disabled = true;
            }
            // watermark
            (dom.querySelector('#show-watermark') as any).checked = this.printData.showWatermark;

            // except 1(default), other modes should go through this process
            if (this.printData.clearMode !== CleanModeType.CleanMode) {
                // do sth and recalc
                this.cleanModeChangeBase(this.printData.clearMode, true);
            }

            if (this.printData.showWatermark === false) {
                this.watermarkChangeBase(this.printData.showWatermark);
            }
            // landscape
            (dom.querySelector('#print-landscape') as any).checked = this.printData.landscape;
            // if (this.printData.landscape === true) {
            //     // do sth
            // }

            const printSelectDom: any = dom.querySelector('#printer-select');
            if (printSelectDom != null) {
                // this will always exist due to initialization
                // console.log(printers)
                const printerName = this.printData.printerName;

                // // maybe overdo
                // let validPrinter = false;
                // for (const printer of printers) {
                //     if (printer === printerName) {
                //         validPrinter = true;
                //     }
                // }
                if (printerName != null && printerName !== '') {
                    // just make sure coming from menu is fine
                    printSelectDom.value = this.printData.printerName;
                }
            }
            let printTypeStr = 'all-print';
            if (this.printData.printType === PrintType.Specific) {
                printTypeStr = 'specific-print';
            } else if (this.printData.printType === PrintType.Continuous || _startName) {
                printTypeStr = 'continuous-print';
            }
            
            // this will make mistakenly passed value always continuous-print! bad!!
            // const printTypeStr = this.printData.printType === PrintType.All ? 'all-print' :
            //     (this.printData.printType === PrintType.Specific) ? 'specific-print' : 'continuous-print';

            const printTypeDom: any = dom.querySelector(`input[name="pageNum"].${printTypeStr}`);
            if (printTypeDom != null) {
                printTypeDom.checked = true;
            }
            if (this.printData.printType === PrintType.Specific) {
                const range: any = dom.querySelector('#specific-page');
                if (range != null) {
                    range.value = this.printData.pageRange;
                }
            }
            const copyDom: any = dom.querySelector('#print-count-number');
            if (copyDom != null) {
                copyDom.value = this.printData.printCount;
            }

            // for animation
            dom.style.height = '0';
            setTimeout(() => {
                // console.log(this.print)
                if ( null != this.print ) {
                    this.print.className += ' active';
                }
            }, 0);
            // console.log(date.getMinutes() + ':' + date.getSeconds() + '.' + date.getMilliseconds());
            // for animation
            setTimeout(() => {
                dom.style.height = '100%';
            }, 50);
        } else {
            let type = this.printData.printType;
            if (_startName) {
                type = PrintType.Continuous;
            }
            this.reactVm.openCPrint(type, this.printData)
            .then( (result) => {
                this.close(result ? 1 : 2);
            });
        }
    }

    private async getPrinters(): Promise<any> {
        this.bOpenCliented = false;
        return new Promise((resolve, reject) => {
            const printerServerUrl = this.getPrinterServerUrl();
            const fetchUrl = `http://${printerServerUrl}/printers`;
            // console.log(fetchUrl);

            fetch(fetchUrl)
            .then((res) => res.json())
            .then(
                (result) => {
                    // console.log(result);
                    this.bOpenCliented = true;
                    resolve(result);
                },
                (error) => {
                    // console.log(error)
                    reject(error);
                }
            );
        });
    }

    private openPrintClient(): void {
        const a: any = document.createElement('a');
    }

    private async forEachClient(): Promise<any> {
        const indexs = [1, 2, 3, 4];
        for (const index of indexs) {
            const res = await this.setTimeRun();
            if (res) {
                this.bOpenCliented = true;
                return res;
            }
        }
    }

    private setTimeRun = async (): Promise<any> => {
        if (this.bOpenCliented === true) {
            return;
        }
        const res = await this.getPrinters()
        .catch((error) => {
            console.dir(error);
        });
        return res;
    }

    // private initIframe(dom: HTMLElement, option?: any): void {
    //     const iframe = dom.querySelector('iframe');
    //     const iframeContent = iframe.contentWindow;
    //     this.print = dom;
    //     const style = document.createElement('style');
    //     style.innerHTML = printStyle();
    //     const body = iframeContent.document.body;
    //     if (option) {
    //         body.style.overflow = option.hidden;
    //     }

    //     // iframeContent.document.body.style.overflow = 'hidden'; // hide outmost scroll bar
    //     body.innerHTML = `<div id='hz-editor-app' class='hz-editor-print'></div>`;
    //     iframeContent.document.head.appendChild(style);
    //     /* IFTRUE_WATER */
    //     // 自定义字体
    //     const customFontStr = getCustomFontFaceStyleHtml();
    //     if (customFontStr) {
    //         const cstyle = document.createElement('style');
    //         cstyle.innerHTML = customFontStr;
    //         iframeContent.document.head.appendChild(cstyle);
    //     }
    //     /* FITRUE_WATER */
    //     const currentReact = ReactDOM.render(<PrintContent />, iframeContent['hz-editor-app']);
    //     this.reactVm = currentReact;
    //     // pass printer server url inside
    //     const printerServerUrl = this.getPrinterServerUrl();
    //     this.reactVm.setPrinterServerUrl(printerServerUrl);
    // }

    private async initIframe(dom: HTMLElement, option?: any): Promise<void> {
        return new Promise((resolve) => {
          const iframe = dom.querySelector('iframe') as HTMLIFrameElement;
          const iframeContent = iframe.contentWindow;
          this.print = dom;
          const style = document.createElement('style');
          style.innerHTML = printStyle();
          const body = iframeContent?.document.body;
          if (body) {
            if (option) {
              body.style.overflow = option.hidden;
            }
    
            body.innerHTML = `<div id='hz-editor-app' class='hz-editor-print'></div>`;
            iframeContent?.document.head.appendChild(style);
    
            //add by tinyzhi. 不应该屏蔽
            // const customFontStr = getCustomFontFaceStyleHtml();
            // if (customFontStr) {
            //   const cstyle = document.createElement('style');
            //   cstyle.innerHTML = customFontStr;
            //   iframeContent?.document.head.appendChild(cstyle);
            // }
    
            const root = ReactDOM.createRoot(iframeContent?.document.getElementById('hz-editor-app') as HTMLElement);
            root.render(<PrintContent ref={this.reactVm2} />);
    
            // 确保组件渲染完成
            setTimeout(() => {
              const printerServerUrl = this.getPrinterServerUrl();
              const instance = this.reactVm2.current;
              if (instance) {
                this.reactVm = instance;
                instance.setPrinterServerUrl(printerServerUrl);
              } else {
                console.error('PrintContent instance is not available');
              }
              resolve(); // 解析 Promise
            }, 100); // 适当的延迟
          } else {
            resolve(); // 如果 body 不存在，直接解析 Promise
          }
        });
      }

    private addEvent(): void {
        this.print.addEventListener('mousewheel', this.preventDefault);
        this.print.querySelector('.btns')
            .addEventListener('click', this.bntsClick, false);
        const closeElems = this.print.querySelectorAll('.print-dialog .close');
        // tslint:disable-next-line: prefer-for-of
        for (let i = 0; i < closeElems.length; i++) {
            closeElems[i].addEventListener('click', this.close, false);
        }
        // this.print.querySelector('select')
        //     .addEventListener('change', this.selectChange, false);
        const clincCheckDom = this.clincCheckDom = this.print
        .querySelector('.print-dialog-box-header-clinc .clinc-model');
        clincCheckDom.addEventListener('change', this.clincCheckChange);
        this.clincRadioDom = this.print
        .querySelector('.print-dialog-box-header-clinc .clinc-radio-group');
        this.clincRadioDom.addEventListener('change', this.clincRadioChange);
        this.print.querySelector('.print-dialog-box .clean-mode')
            .addEventListener('change', this.cleanModeChange);
        this.print.querySelector('.print-dialog-box .show-watermark')
            .addEventListener('change', this.watermarkChange);
        this.print.querySelector('.print-dialog-box .print-gridline')
            .addEventListener('change', this.printGridLineChange);
        const contPrintDom: any = this.print.querySelector('.print-dialog-box .cont-print');
        const batchPrintDom: any = this.print.querySelector('.print-dialog-box .batch-print');
        contPrintDom.addEventListener('change', this.checkedChanged);
        batchPrintDom.addEventListener('change', this.checkedChanged);
    }

    private checkedChanged = (e): void => {
        const target = e.target;
        if (target.checked !== true) {
            if (target.id === 'cont-checked') {
                target.id = '';
            }
            return;
        }
        const className = target.className;
        const parent = target.parentNode;
        switch (className) {
            case 'cont-print': {
                if (target.checked) {
                    target.id = 'cont-checked';
                }
                const dom = parent.querySelector('.batch-print');
                if (dom && dom.checked) {
                    dom.checked = false;
                }
                break;
            }
            case 'batch-print': {
                const dom = parent.querySelector('.cont-print');
                if (dom && dom.checked) {
                    dom.checked = false;
                    target.id = '';
                }
                break;
            }
        }
    }

    private addCEvent(): void {
        const closeElems = this.print.querySelectorAll('.print-dialog-box-c .close');
        // tslint:disable-next-line: prefer-for-of
        for (let i = 0; i < closeElems.length; i++) {
            closeElems[i].addEventListener('click', this.slideOut, false);
        }
        this.print.querySelector('.print-dialog-box-c .print')
            .addEventListener('click', this.cPrintClick, false);
        this.print.querySelector('.print-dialog-box-c #specific-page')
            .addEventListener('blur', this.pageRangeBlur);
        const pageNumRadios = this.print.querySelectorAll('.print-dialog-box-c input[name=pageNum]');
        // tslint:disable-next-line: prefer-for-of
        for (let i = 0; i < pageNumRadios.length; i++) {
            pageNumRadios[i].addEventListener('change', this.pageNumChange, false);
        }
        this.print.querySelector('.print-dialog-box-c .clean-mode')
            .addEventListener('change', this.cleanModeCChange);
        this.print.querySelector('.print-dialog-box-c .show-watermark')
            .addEventListener('change', this.watermarkChange);
        // this.print.querySelector('.print-dialog-box-c .print-landscape')
        //     .addEventListener('change', this.orientationChange);
        this.print.addEventListener('transitionend', this.transitionEnd, false);
        this.print.querySelector('.print-dialog-box-c .print-gridline')
            .addEventListener('change', this.printGridLineChange);
    }

    private intPrintData(prop: any = ({} as any)): void {
        this.printData = {
            printData: (prop && prop.printData) || '',
            printerName: (prop && prop.printerName) || '', // be careful about init here
            printCount: (prop && prop.printCount) || 1,
            printDouble: window['bPrintDouble'] || false,
            pageCount: (prop && prop.pageCount) || 1,
            showWatermark: (prop && prop.showWatermark != null) ? prop.showWatermark : this.documentCore.getDocument()
                                                                            .hasTextWaterMark(),
            landscape: (prop && prop.landscape != null) ? prop.landscape : false,
            // helper props, not in api
            pageRange: (prop && prop.pageRange) || '',
            startPageHdrFtr: (prop && prop.startPageHdrFtr) || false,
            printType: (prop && prop.printType) || 1,
            clearMode: (prop && prop.clearMode != null) ? prop.clearMode : CleanModeType.CleanMode,
            bDirectPrint: (prop && prop.bDirectPrint) || false,
            title: (prop && prop.title) || '门诊病历',
            pageSize: (prop && prop.pageSize),
            printOrientPortrait: true,
        };
        
        // {prop : {}, callback : f()}
        // console.log(option)
        // const prop = option != null ? option.prop : null;
        
    }

    private getPrinterServerUrl(): string {
        let printerServerUrl = 'localhost:8888';
        const host = this.getHost();
        const documentCore = host != null ? host.getDocumentCore() : null;
        if (documentCore != null) {
            printerServerUrl = documentCore.getPrinterServerUrl();
        }

        return printerServerUrl;
    }

    private async getPrintDouble(): Promise<void> {
        if (window['bPrintDouble'] === undefined) {
            let sPrintDouble;
            const obj = editorCallback.getItem('printDouble');
            if (obj instanceof Promise) {
                sPrintDouble = await new Promise((resolve) => {
                    obj.then((res) => {
                        resolve(res);
                    })
                });
            } else {
                sPrintDouble = obj;
            }
            if (sPrintDouble === 'true') {
                window['bPrintDouble'] = true;
            } else {
                window['bPrintDouble'] = false;
            }
        }
    }
}


// export class PrintResultManager {
//     private host: EmrEditor;
//     private result: boolean;
//     public getPrinterResult: any;
//     constructor(host: EmrEditor) {
//         if (host != null) {
//             this.host = host;
//             this.init();
//         }
//     }

//     public printerResultReturned(result: boolean): void {
//         this.result = result;
//     }

//     public getHost(): EmrEditor {
//         return this.host;
//     }

//     private init(): void {
//         gEvent.addEvent(this.host.docId, GlobalEventName.PrinterResult, this.printerResultReturned);
//         this.getPrinterResult = (): Promise<boolean>  => {
//             return new Promise(async (resolve, reject) => {
//                 await this.printerResultReturned(this.result);
//             });
//         };
//     }
// }
