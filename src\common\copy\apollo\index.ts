import {Node} from './Node';
import Paragraph from '../../../model/core/Paragraph';
import ParaPortion from '../../../model/core/Paragraph/ParaPortion';
import {Table} from '../../../model/core/Table';
import {DocumentContentType} from '../../../model/core/Style';
import { SaveFileType } from '../DataType';
import DocumentContentElementBase from '../../../model/core/DocumentContentElementBase';
import { NewControl } from '../../../model/core/NewControl/NewControl';
import { INewControlProperty } from '../../commonDefines';
import { IDocContent } from '../../DocContent';
import { HeaderPro } from './NodeType';
export default class Apollo {
    private contents: DocumentContentElementBase[];
    private result: string;
    private parseNode: Node;
    private _doc: any;
    private _newControlProps: INewControlProperty[];
    private _regionProps: INewControlProperty[];
    private _newControls: any;

    constructor(contents: DocumentContentElementBase[], doc: any) {
        this._doc = doc;
        this._newControlProps = [];
        this.contents = contents || [];
        this._regionProps = [];
        this._newControls = {};
        this.parseNode = new Node(this);
        this.parse();
    }

    public parseAll(docContent: IDocContent): void {
        this.contents = docContent.content;
        const content = this.eachNode();
        const page = docContent.headerFooters[0];
        this.contents = page.header.headerContent['0'];
        const header = this.eachNode();
        this.contents = page.footer.footerContent['0'];
        const footer = this.eachNode();
        const pageProp = docContent.headerFooterProp;
        const doc = `
        <?xml version="1.0" encoding="UTF-8"?>
        <w:document xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main">
            <w:headerFooter>
                <w:hfPr
                    ${HeaderPro.ProtectHeaderFooter}="${pageProp.bProtectHeaderFooter}"
                    ${HeaderPro.ProtectHeaderFooterSaved}="${pageProp.bProtectHeaderFooterSaved}"
                    ${HeaderPro.ShowFooter}="${pageProp.bShowFooter}"
                    ${HeaderPro.ShowHeader}="${pageProp.bShowHeader}"
                ></w:hfPr>
                <w:header>
                    ${header}
                </w:header>
                <w:footer>
                    ${footer}
                </w:footer>
            </w:headerFooter>
            <w:body>${content}</w:body>
        </w:document>
        `;
        this._newControlProps = docContent.structs;
        this._regionProps = docContent.regions;
        this.result = doc;
        // console.log(doc)
    }

    public getPlainApollo(): string {
        return this.result;
    }

    public getApollo(): string {
        return SaveFileType.Apollo + btoa(encodeURIComponent(this.result));
    }

    public getNewControlByName(name: string): NewControl {
        const newControl = this._newControls[name];
        if (newControl) {
            return newControl;
        }
        return this._doc.getNewControlByName(name);
    }

    public addRegionPropsByName(name: string): INewControlProperty {
        const regionProps = this._doc.getRegionPropsByName(name);
        if (!regionProps) {
            return;
        }
        this._regionProps.push(regionProps);
        return regionProps;
    }

    public getRegionProps(): INewControlProperty[] {
        return this._regionProps;
    }

    public getNewControlProps(): INewControlProperty[] {
        return this._newControlProps;
    }

    public addNewControlProperty(newControl: NewControl): void {
        const name = newControl.getNewControlName();
        if (this._newControls[name]) {
            return;
        }
        const props = this._doc.getCustomForNewControl(newControl);
        if (!props) {
            return;
        }
        this._newControls[name] = newControl;
        props['bPlaceholder'] = newControl.isPlaceHolderContent();
        this._newControlProps.push(props);
    }

    private parse(): void {
        if (this.contents.length === 0) {
            return;
        }

        const result = this.eachNode();

        const doc = `
        <?xml version="1.0" encoding="UTF-8"?>
        <w:document xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main">
            <w:body>${result}</w:body></w:document>
        `;

        this.result = doc;
    }

    private addTable(table: Table): string {
        let result = '';
        // rows
        const node = this.parseNode;
        // let cols: number;
        // let widths: number[] = [];
        table.content.forEach((row) => {
            let colStr = '';
            row.content.forEach((cell) => {
                let sParagraph = '';
                cell.content.content.forEach((para) => {
                    sParagraph += this.addPara(para as Paragraph);
                });
                sParagraph = node.addColStyle(cell.property, cell.borderInfo) + sParagraph;
                colStr += node.addCol(sParagraph);
            });

            colStr = node.addRowStyle(row) + colStr;
            result += node.addRow(colStr);
        });
        result = node.addTableStyle(table.tableGrid, table) + result;
        result = node.addTable(result);

        return result;
    }

    private addRegion(region: any): string {
        let content = '';
        region.content.content.forEach((item) => {
            switch (item.getType()) {
                case DocumentContentType.Region: {
                    content += this.addRegion(item);
                    break;
                }
                case DocumentContentType.Table: {
                    content += this.addTable(item);
                    break;
                }
                case DocumentContentType.Paragraph: {
                    content += this.addPara(item);
                    break;
                }
            }
        });
        content = this.parseNode.addRegion(region, content);
        return content;
    }

    private addPara(par: Paragraph): string {
        let sPortion = '';
        const node = this.parseNode;
        const lastIndex = par.content.length - 1;
        // let nPlaceHolderCount: number = 0;
        par.content.forEach((por: ParaPortion, index: number) => {
            const contents = par.content;
            if (contents.length === 0) {
                return;
            }
            // let bEndStruct = false;
            // if (por.bPlaceHolder === true) {
            //     nPlaceHolderCount++;
            //     bEndStruct = this.isEndStruct(por);
            // } else if (nPlaceHolderCount === 1) {
            //     nPlaceHolderCount = 0;
            //     return;
            // }
            const text = node.addText(por);
            if (!text) {
                return;
            }
            // if (bEndStruct === true) {
            //     nPlaceHolderCount = 0;
            // }
            const style = node.addPorStyle(por.textProperty, por['bHidden']);
            sPortion += node.addPor(style + text);
        });
        const parStyle = node.addParStyle(par.paraProperty, par['bHidden']);
        const sParagraph = node.addPar(parStyle + sPortion);

        return sParagraph;
    }

    private isEndStruct(portion: ParaPortion): boolean {
        const text = portion.content[0];
        if (text && text['bStart'] === false) {
            return true;
        }

        return false;
    }

    private eachNode(): string {
        // const node = this.parseNode;
        let result = '';
        this.contents.forEach((item: any) => {
            const type = item.getType();
            if (type === DocumentContentType.Region) {
                result += this.addRegion(item);
            } else if (type === DocumentContentType.Table) {
                result += this.addTable(item as Table);
            } else {
                result += this.addPara(item as Paragraph);
            }
        });

        return result;
    }

}
