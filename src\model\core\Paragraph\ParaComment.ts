import { ParaElementBase } from './ParaElementBase';
import TextProperty from '../TextProperty';
import { ParaElementType } from './ParagraphContent';
import { idCounterImage } from '../util';

export class ParaComment extends ParaElementBase {
    public id: number;
    // public height: number;
    public bStart: boolean;
    public commentId: number;

    constructor(bStart: boolean, commentId?: number) {
        super();
        this.id = idCounterImage.getNewId();
        this.bStart = bStart;
        this.content = '';
        this.type = ParaElementType.ParaComment;
        this.commentId = commentId;

        this.width = 0; // 2px
        this.widthVisible = 0;
        // this.height = 0;
        this.bVisible = false;
    }

    public measure(textPr: TextProperty): number {
        return 0;
    }

    /**
     * 拷贝函数，默认深拷贝，
     * UI显示拷贝时，只需要拷贝content，widthVisible
     * @param bForUI
     */
    public copy(bFlag: boolean = false): ParaElementBase {
        const comment = new ParaComment(this.bStart);

        if ( true === bFlag) {
            comment.positionX = this.positionX;
            comment.positionY = this.positionY;
            comment.bVisible = this.bVisible;
        }

        comment.width = this.width;
        comment.widthVisible = this.widthVisible;
        // comment.height = this.height;

        return comment;
    }

    public setCommentId(id: number): void {
        this.commentId = id;
    }

    public getCommentId(): number {
        return this.commentId;
    }
}
