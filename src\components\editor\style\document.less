@import './global.less';
.hz-editor-container {
    .sketch-picker > div:nth-of-type(3) {
        display: none!important;
    }
    .newControl-tips {
        position: absolute;
        left: 0;
        top: 0;
        z-index: 1;
        min-height: 18px;
        min-width: 30px;
        margin-top: -14px;
        padding: 0 3px;
        font-size: 12px;
        cursor: default;
        color: #737B80;
        line-height: 16px;
        border: 1px solid #ACB4C1;
        border-radius: 2px;
        background: #fff;
        &.active, &:hover {
            color: #171819;
            border: 1px solid #171819;
        }

        i {
            position: absolute;
            display: inline-block;
            top: 2px;
            width: 12px;
            height: 12px;
            vertical-align: middle;
            background-image: url(../images/tips.png);
        }

        span {
            padding-left: 14px;
        }

        &:hover {
            i {
                background-image: url(../images/tips_active.png);
            }
        }

        i, span {
            pointer-events: none;
        }

        // &::before {
        //     display: inline-block;
        //     content: '┇';
        //     font-size: 14px;
        // }
    }

    .editor-morepage-view {
        .more-page {
            display: flex;
            right: 0;
            margin: 0 auto;
            & > .page-wrapper {
                margin: 0;
                position: relative;
            }
            & > div.page-wrapper + div {
                margin-left: 15px;
            }
        }
        
    }

    .editor-regexp-btn {
        display: flex;
        & > div:nth-of-type(2), & > div:nth-of-type(3) {
            flex: 1;
            min-width: 43px;
            padding-left: 14px;
            color: @activeColor;
            cursor: pointer;
            line-height: 30px;
        }

        & > div:nth-of-type(1) {
            flex: 8;
        }
    }

    .struct-regexp .editor-line:first-child {
        display: flex;

        & > span {
            flex: 1;
            min-width: 80px;
        }

        & > div {
            flex: 9;
        }
    }

    .row-setting-modify {
        display: none;
        position: absolute;
        z-index: 0;

        &.active {
            display: block;
        }

        & > span {
            position: absolute;
            top: 5px;
            font-size: 24px;
            color: #3664D9;
            width: 22px;
            height: 26px;
            line-height: 26px;
            background: #F3F3F3;
            border-radius: 4px;
            cursor: pointer;
            user-select: none;
            &:first-child {
                left: -25px;
            }
            &:last-child {
                right: -25px;
            }
        }
    }

    .row-tips-modify {
        display: none;
        position: absolute;
        z-index: 10; /* 确保不被其他元素遮挡 */
        white-space: nowrap; /* 防止文字换行 */
        overflow: visible; /* 确保不会隐藏 */
    }
    
    .row-tips-modify > span {
        position: absolute;
        top: 5px;
        font-size: 14px; /* 字体变小 */
        color: #3664D9; /* 颜色保持不变 */
        width: auto; /* 让宽度随文字变化 */
        height: 26px;
        line-height: 26px;
        background: rgba(243, 243, 243, 0.7);
        border-radius: 4px;
        cursor: pointer;
        user-select: none;
        text-align: right;
        direction: rtl; /* 让文字从右向左伸展 */
        padding: 0 5px; /* 增加内边距，让内容可见 */
        max-width: 300px; /* 限制最大宽度 */
    }

    .row-tips-modify.active {
        display: block; /* 只有加上 active 类时才显示 */
    }

    .newcontrol-focus-container, .tablecell-container, .paragraphline-container {
        & > rect {
            display: none;
            &.newcontrol-focus, &.selection-selected {
                display: block;
            }
        }

    }

    .ReactVirtualized__Grid__innerScrollContainer {
        & > div:first-child > div {
            svg {
                position: relative;
                z-index: 0;
            }
        }

        .table-border line {
            shape-rendering: crispedges;
        }

        svg[name='right'], svg[name='rightHeader'] {
            pointer-events: none;
            .table-container {
                pointer-events: all;
            }
        }

        & > div > div {
            svg.selecton-svg {
                position: absolute;
                z-index: 7;
                top: 0;
                margin-top: 0;
                pointer-events: none;
            }
        }

        .button-rect {
            stroke-width: 1.5;
            stroke: #ced4da;
            fill: url(#buttonGradient);
            pointer-events: none;
            shape-rendering: optimizeQuality;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.08)) drop-shadow(0 1px 2px rgba(0, 0, 0, 0.06));
            rx: 4;
            ry: 4;
            & + text {
                pointer-events: none;
                fill: #343a40;
                font-weight: 600;
                text-shadow: 0 1px 0 rgba(255, 255, 255, 0.8);
            }
        }

        .cursor-auto {
            .button-rect {
                pointer-events: all;
                cursor: pointer;
                transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
                &:hover {
                    fill: url(#buttonHoverGradient);
                    stroke: #6c757d;
                    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.12)) drop-shadow(0 1px 2px rgba(0, 0, 0, 0.08));
                    
                    & + text {
                        fill: #212529;
                    }
                }
                &:active {
                    fill: url(#buttonActiveGradient);
                    stroke: #868e96;
                    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
                    transition: all 0.05s ease;
                }
            }

            .opacity, .header-footer-enabled {
                .button-rect {
                    pointer-events: none;
                    cursor: pointer;
                }
            }
        }
    }

    .table-hoverbackground > rect {
        fill: #00a3ff;
        stroke-width: 0;
        fill-opacity: 0.1;
        pointer-events: none;
    }

    // .nis-table, svg[name='right'], svg[name='left'], svg[name='header'], svg[name='rightHeader'], svg[name='leftHeader']{
    //     rect.selection-selected {
    //         fill: #B2E2FF;
    //     }

    //     rect[data-hover="1"] {
    //         fill: #E5F5FF;
    //     }

    // }

    // svg[name='right'], svg[name='left'] {
    //     rect[data-hover="1"] {
    //         fill: #E5F5FF;
    //     }
    // }
    .revision-tips {
        display: none;
        position: absolute;
        z-index: 9;
        width: 240px;
        // min-width: 240px;
        padding: 0 12px;
        font-size: 12px;
        color: #293750;
        background: #FFFFFF;
        border-radius: 2px;
        border: 1px solid #ccc;

        // & > div:first-child {
        //     height: 33px;
        //     line-height: 33px;
        //     border-bottom: 1px solid #DDDDE3;

        //     label {
        //         float: left;
        //     }

        //     span {float: right;}
        // }

        & > div:nth-child(5n+1) {
            margin: 8px 0;
            min-height: 17px;
            // line-height: 17px;
            border-left: 3px;

            label {
                float: left;
                text-align: left;
            }

            span {float: right;}
        }

        & > div:nth-child(5n+2) {
            margin: 8px 0;
            height: 17px;
            line-height: 17px;
            border-left: 3px;

            label {
                float: left;
            }
        }

        & > div:nth-child(5n+3) {
            margin: 8px 0;
            padding-left: 5px;
            line-height: 17px;
            border-left: 3px solid #FF3143;
            text-align: left;
            & > label {
                font-weight: bold;
            }

            & > span {
                word-break: break-all;
            }
        }

        & > div:nth-child(5n+4) {
            margin: 8px 0;
            line-height: 17px;
            text-align: left;
        }

        & > div:nth-child(5n+5) {
            border-bottom: 1px solid #DDDDE3;
        }

        &.active {
            display: block;
        }

        &.visible {
            display: block;
            opacity: 0;
            user-select: none;
        }
    }

    .selections-rect {
        fill: rgba(0, 119, 255, 0.3);
        stroke-width: 0px;
    }

    .newcontrol-rect {
        fill: rgba(0, 241, 253, 0.3);
        stroke-width: 0px;
    }

    .subSign-tips {
        position: absolute;
        z-index: 9;
        left: 0;
        top: 0;
        z-index: 1;
        min-height: 22px;
        max-height: 30px;
        min-width: 30px;
        max-width: 280px;
        margin-top: -28px;
        // line-height: 30px;
        padding: 4px 8px;
        font-size: 14px;
        color: #FFFFFF;
        background: #54627B;
        border-radius: 2px;
        border: 1px solid #ccc;
        overflow: hidden;
        white-space: nowrap;

        span {
            padding-left: 5px;
            pointer-events: none;
            user-select: none;
        }

        &.active {
            display: block;
            user-select: none;
        }
    }

    svg[name='right'], svg[name='left'], svg[name='header'], svg[name='leftHeader'], svg[name='rightHeadr'] {
        pointer-events: none;
        .table-container {
            pointer-events: all;
        }
    }
    .search-dialog {
        position: absolute;
        right: 18px;
        z-index: 99;
        width: 320px;
        padding: 0px 20px 10px 20px;
        text-align: left;
        font-size: 14px;
        background-color: #fff;
        border: 1px solid #DDDDE3;
        box-shadow: 0px 1px 8px rgba(71, 74, 91, 0.25);

        & > div:first-child {
            display: flex;
            padding-top: 10px;
            cursor: move;
            align-items: center;
            & > label {
                flex: 9;
                font-weight: bold;
                pointer-events: none;
            }
            & > span {
                display: flex;
                width: 40px;
                height: 20px;
                line-height: 20px;
                flex: 1;
                cursor: pointer;
                & > i:first-child {
                    width: 20px;
                    height: 100%;
                    margin-right: 4px;
                    text-align: center;
                    background-color: transparent;
                    &.active, &:hover {
                        background-color: #ddd;
                    }
                }
            }
        }

        & > div:nth-child(2) {
            position: relative;
            input {
                width: 100%;
                height: 100%;
                padding-left: 28px;
                padding-right: 28px;
                font-size: 12px;
                outline: none;
                border: 1px solid @borderColor;
                &:active, &:hover, &:focus{
                    border-color: @activeColor;
                }

                &::placeholder {
                    color: @disabledColor;
                }
            }

            i {
                position: absolute;
                left: 2px;
                top: 2px;
                display: inline-block;
                width: 24px;
                height: 24px;
                text-align: center;
                line-height: 24px;
                cursor: pointer;
                &.disabled {
                    cursor: default;
                    color: #ccc;
                }
            }

            span {
                position: absolute;
                right: 5px;
                top: 0;
                height: 28px;
                line-height: 28px;
                font-size: 12px;
            }
        }

        & > div:nth-child(3) {
            font-size: 12px;
            .editor-checkbox {
                display: inline-block;
            }
        }

        & > div:last-child {
            display: flex;
            padding-top: 10px;
            span {
                flex: 1;
                width: 130px;
                line-height: 28px;
                cursor: pointer;
                text-align: center;
                border: 1px solid #ccc;
                border-radius: 2px;
                &:first-child {
                    margin-right: 10px;
                }

                &.disabled {
                    cursor: not-allowed;
                    color: #aaa;
                }
            }
        }
    }

    .newcontrol-cursorin-container {
        pointer-events: none;
    }
}

#hz-editor-app, #editor-container-id {
    height: 100%;
    & > div {
        height: 100%;
    }

    .hz-editor-instance {
        & > .hz-editor-container {
            height: 100%;
        }
    }

    .ReactVirtualized__Grid {
        > div:first-child {
            height: 100%!important;
        }
    }

    .hz-editor-container {
        display: flex;
        flex-direction: column;
        & > div:first-child {
            flex: 1;
            &.no-toolbar {
                flex: 0;
            }
        }
        & > div:nth-of-type(2) {
            flex: 99;
            height: 100%;
            width: 100%;
        }
    }
}
html {
    height: 100%;
}