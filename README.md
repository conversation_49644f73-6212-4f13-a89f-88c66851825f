# EmrEditor

## node.js 版本

推荐 v10.3.0


## 首次启动项目，先安装依赖

运行 `npm install` 安装依赖包


## 开发调试
npmn
运行 `npm start` 启动开发服务, 浏览器打开地址 `http://localhost:8081/`, 开发修改源代码文件会自动刷新页面 


## 编译源代码
运行 `npm run build` 编译文件, 编译后的文件资源将会放到`dist/hz-editor/`目录下


## 发布到外部npm服务器

运行`cd dist/hz-editor`切换到`dist/hz-editor`目录下，运行 `npm publish`发布 



## 运行测试

运行 `npm run test`运行单元测试 

## node js 新版本问题
运行 $env:NODE_OPTIONS = "--openssl-legacy-provider"

## 编译水印版本
npm run pack:watersite

## sdk发包
在sdk目录运行 npm pack && npm publish


## webpack5 编译备注
1 使用webpack5之后，相关插件需要升级
2 css-load 不能升级，否则工具栏图片加载失败
3 config.js 中需要添加
plugins: [
    new webpack.ProvidePlugin({
      process: 'process/browser',
    }),
]
4 react不能升级，变化较大，继续保持原版本
5 html-webpack-plugin 需要升级 否则test.html 拷贝会失败

## apidoc编译
npx apidoc  -o apidoc

## 发包的时候增加参数
npm run pack:watersite --node-flags --max-old-space-size=8192 --no-warnings
npm run pack:watersite:minifile --node-flags --max-old-space-size=8192 --no-warnings
npm run start --node-flags --max-old-space-size=8192 --no-warnings

## 图标更换
1 登录 https://icomoon.io/ 账号 <EMAIL>  password8
2 导入svg文件到网站，修改增加图标
3 工程导出成zip
4 覆盖文件
5 修改 iconfont.css
