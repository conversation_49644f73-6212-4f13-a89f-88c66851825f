/**
 * 鼠标事件处理类
 */
export default class MouseEventHandler {
    public pointX: number;
    public pointY: number;
    public x: number;
    public y: number;

    public type: MouseEventType;
    public button: MouseButtonType;
    public newControl?: any;

    public bShiftKey: boolean;
    public bCtrlKey: boolean;

    public clickCount: number;

    constructor() {
        this.pointX = 0;
        this.pointY = 0;
        this.type = MouseEventType.MouseButtonMove;
        this.button = MouseButtonType.MouseButtonLeft;

        this.bShiftKey = false;
        this.bCtrlKey = false;

        this.clickCount = 0;
    }
}

/**
 * 鼠标事件类型
 */
export enum MouseEventType {
    MouseButtonDown,
    MouseButtonUp,
    MouseButtonMove,
    MouseButtonWheel,
}

/**
 * 鼠标按键
 */
export enum MouseButtonType {
    MouseButtonLeft = 0,
    MouseButtonCenter,
    MouseButtonRight,
}

/**
 * 键盘特定事件: shift, ctrl, alt
 */
export class KeyBoardEvent {
    public bShiftKey: boolean;
    public bCtrlKey: boolean;
    public bAltKey: boolean;
    public bMacCmdKey: boolean;

    public keyCode: number;

    constructor() {
        this.bAltKey = false;
        this.bCtrlKey = false;
        this.bShiftKey = false;
        this.bMacCmdKey = false;
        this.keyCode = 0;
    }
}

export interface IMouseEvent {
    bShiftKey?: boolean;
    bCtrlKey: boolean;
    type: MouseEventType;
    clickCount: number;
}
// export const global_mouseEvent = new MouseEventHandler();
