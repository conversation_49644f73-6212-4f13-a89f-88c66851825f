import * as React from 'react';
import '../style/radio-commet.less';

interface IState {
    bReflash: boolean;
}

interface IPropKey {
    name: string;
    value: string;
    disabled: string;
}

interface IProps {
    onChange?: (value: any, name: string, item?: any) => void;
    data: object[];
    value: any;
    prop?: IPropKey;
    disabled?: boolean;
    readonly?: boolean;
    name?: string;
}

export default class RadioCommet extends React.Component<IProps, IState> {
    private value: any;
    private prop: IPropKey;
    private containerRef: any;
    constructor(props: any) {
        super(props);
        this.state = {
            bReflash: false,
        };
        this.value = props.value;
        this.prop = {
            name: 'key',
            value: 'value',
            disabled: 'disabled',
        };

        this.containerRef = React.createRef();
    }

    public render(): any {
        return (
        <div className='editor-radio' ref={this.containerRef}>
            {this.renderContent()}
        </div>
        );
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        this.value = nextProps.value;
    }

    public componentDidMount(): void {
        this.containerRef.current.addEventListener('change', this.containerChange);
    }

    public componentWillUnmount(): void {
        this.containerRef.current?.removeEventListener('change', this.containerChange);
    }

    private renderContent(): React.ReactNode {
        const props = this.props;
        const datas = props.data || [];
        if (datas.length === 0) {
            return null;
        }
        const prop = props.prop || this.prop;
        const activeValue = this.value;
        const keyName = prop.name;
        const keyValue = prop.value;
        const disabled = prop.disabled;
        const allDisabled = props.disabled === true;
        const name = 'editor-radio-' + Math.floor(Math.random() * 1000);
        return datas.map((data, index) => {
            const value = data[keyValue];
            // const curIndex = values.findIndex((actValue) => actValue === value);
            // const name = 'checkbox' + index;
            let className = '';
            if (activeValue === value) {
                className = 'radio-icon-checked';
            }

            let className1 = '';
            if (data[disabled] === true || allDisabled) {
                className1 = ' disabled';
            }

            return (
                <span
                    key={index}
                    className={'radio-item' + className1}
                >
                    <input type='radio' name={name} data-index={index}/>
                    <i className={className} />
                    <label>{data[keyName]}</label>
                </span>
            );
        });
    }

    private containerChange = (e: any): void => {
        const target = e.target;
        const index = target.getAttribute('data-index');
        if (!index) {
            return;
        }
        this.onChange(parseInt(index, 10), e);
    }

    private onChange = (index: number, e: any): void => {
        const {prop, data, onChange, name} = this.props;
        const item = data[index];
        const keyValue = (prop || this.prop).value;
        const value = item[keyValue];
        if (value === this.value) {
            return;
        }
        this.value = value;

        if (typeof onChange === 'function') {
            onChange(value, name, item);
        }
        e.target.checked = false;
        if (this.containerRef.current) {
            this.setState({bReflash: !this.state.bReflash});
        }
    }
}
