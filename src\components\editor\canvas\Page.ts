import { IDocumentContent } from '../../../model/DocumentCore';
import { ICanvasProps } from './common';
import { ContentUI } from './Content';

export class PageUI {
    private pageIndex: number;
    private height: number;
    private width: number;
    private host: any;
    private ctx: any;
    private props: IDocumentContent;

    constructor(props: ICanvasProps) {
        this.pageIndex = props.pageIndex;
        this.width = props.content.width;
        this.height = props.content.height;
        this.host = props.host;
        this.props = props.content;
        this.ctx = props.ctx;
        this.render();
    }

    public render(): void {
        if (!this.ctx) {
            return;
        }
        // const ctx = document.createElement('canvas');
        // ctx.style.width = this.width + 'px';
        // ctx.style.height = this.height + 'px';
        const content = {content: this.props};
        const obj = new ContentUI({content, ctx: this.ctx, pageIndex: this.pageIndex, host: this.host});
        // this.ctx = ctx;
        // this.ref.appendChild(ctx);
    }
}
