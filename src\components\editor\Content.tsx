import * as React from 'react';
import Paragraph from './Paragraph';
import Image from './Image';
import { IDocumentContent } from 'src/model/DocumentCore';
import { IDocumentTable } from 'src/model/TableProperty';
import Table from './Table';
import { RenderSectionBackgroundType } from '../../common/commonDefines';

interface IContentProps {
  pageIndex: number;
  content: IDocumentContent;
  editorContainer?: any;
  documentCore?: any;
  handleRefresh?: any;
  host?: any;
}

interface IContentStates {
}

export default class Content extends React.Component<IContentProps, IContentStates> {
  constructor(props) {
    super(props);
    // console.log('Content---------------constructor----------------')
  }

  public render(): any {
    const { content } = this.props;
    let className = '';
    if (content && content.tables.length > 0) {
      className = ' outer';
    }

    return (
      <React.Fragment>
        <g className='font-backgroundColor-container'>
            {this.getBackgroundContent(false)}
        </g>
        <g className={'newcontrol-focus-container' + className}>
            {this.getSectionSelection(content, RenderSectionBackgroundType.NewControlFocus)}
        </g>
        <g className={'newcontrol-cursorin-container' + className}>
            {this.getSectionSelection(content, RenderSectionBackgroundType.NewControlCursorIn)}
        </g>
        <g className={'paragraphline-container' + className}>
            {this.getSectionSelection(content, RenderSectionBackgroundType.ParagragphLineSelection)}
        </g>
        <g className='font-textdecorationline-container'>
            {this.getTextDecorationLine()}
        </g>
        <g className='image-wrapper'>
          {this.renderImage()}
        </g>
        <g>
          {this.renderContent()}
        </g>
        <g className='table-container'>
          {this.renderTableContent()}
          {this.renderTable()}
        </g>
      </React.Fragment>
    );
  }

  private renderParagraph(): any {
    const { content } = this.props;
    return content.paragraphs.map((item) => {
      return <Paragraph key={item.index} index={item.index} pageIndex={this.props.pageIndex}
      content={item.content} lines={item.lines} startLine={item.startLine} endLine={item.endLine}/>;
    });
  }

  private renderTableContent(): any {
    const { pageIndex, content } = this.props;
    return content.tables.map((item) => {
      // const height = item.height;
      // const width = item.width;
      const x = item.x;
      const y = item.y;

      return (
        <g key={item.id}>
          {/* <g className="table-container" offset-x={x} offset-y={y} >
              {this.getSectionSelection(item.content, RenderSectionBackgroundType.ParagragphLineSelection)}
          </g> */}
          <g className='tablecell-container' offset-x={x} offset-y={y} >
              {this.getCellsSelection(item)}
          </g>
            <Content pageIndex={pageIndex} content={item.content} editorContainer={this.props.editorContainer} 
                     documentCore={this.props.documentCore} handleRefresh={this.props.handleRefresh}/>
        </g>)
    });
  }

  private renderTable(): any {
    const { content } = this.props;
    return content.tables.map((item) => {
       return <Table key={item.id} id={item.id} index={item.index} x={item.x} y={item.y} height={item.height} width={item.width} content={item.content} 
                tableBorderLines={item.tableBorderLines} tableBackground={item.tableBackground} tableCells={item.tableCells}/>;
    });
  }

  private getSectionSelection = (content: IDocumentContent, type: RenderSectionBackgroundType) => {

    return content.paragraphs.map((para) => {
      return para.lines.map((line, lineIndex) => {
        if ( para.startLine <= lineIndex && lineIndex <= para.endLine ) {

          const { id, bottom, ranges, top } = line;
          const props = {
            height: bottom - top,
            width: ranges[0].width , //  would be overridden
            x: ranges[0].xVisible, // would be overridden
            y: top,
          };
          const className = `selection ${type}-${id}`;
          return <rect key={id} className={className} line-key={id} pointerEvents={'none'} {...props}/>;
        }
        lineIndex++;
      });
    });
  }

  private getCellsSelection = (table: IDocumentTable) => {
      return table.tableCells.map((cell) => {
        // const { id, height, width, positionX, positionY } = items;
        const { id, height, width, x, y, pageIndex } = cell;
        const props = {
          height,
          width , //  would be overridden
          x, // would be overridden
          y,
        };
        const className = `selection ${RenderSectionBackgroundType.TableCellSelection}-${id}-${pageIndex}`;
        return <rect key={id} className={className} cell-key={id} {...props}/>;
      });
  }

  private renderContent(): any {
    return this.renderParagraph();
  }

  private renderImage(): any {
    const { pageIndex, documentCore } = this.props;

    let images = [];
    // convert iterable to array
    images = Array.from(documentCore.getGraphicObject()
      .values()); // or [...drawingObjects.values()]
    // console.log(images)

    return images.map((item, index) => {
      if (item.getAbsolutePageIndex() === pageIndex) {
        return (
          <Image
            key={index}
            id={index}
            item={item}
            editorContainer={this.props.editorContainer}
            documentCore={this.props.documentCore}
            handleRefresh={this.props.handleRefresh}
            host={this.props.host}
          />
        );
      }
      return null;
    });
  }

  private getTextDecorationLine(): any {
    return this.props.content.paragraphs.map((para) => {
      const datas = para.textDecoration;
      return datas.map((data, index) => {
        return (<line key={index} x1={data.x} y1={data.y} x2={data.x + data.width} y2={data.y}
                style={{stroke: '#000', strokeWidth: '0.8px'}}/>);
      });
    });
  }

  private getBackgroundContent(isTable: boolean = true): any {
    return this.props.content.paragraphs.map((para) => {
      const datas = para.backgroundColor; // this.props.documentCore.getTextBackgroundColorLines(content);
      let sumNum = 0;
      if (isTable) {
        sumNum = 1;
      }
      return datas.map((data, index) => {
        return (<rect width={data.width + sumNum} key={index} height={data.height} x={data.x} y={data.y} fill={data.backgroundColor}></rect>);
      });
    });
  }

  // componentWillUnmount() {
  //   console.log('Content--------componentWillUnmount---------------')
  // }
}
