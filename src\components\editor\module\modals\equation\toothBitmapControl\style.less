
.graph {
    width: 100%;
    margin: auto;
    position: relative;
    font-family: '宋体';

    .header {
        position: absolute;
        top: 0px;
        left: 50%;
        transform: translateX(-50%);
    }

    .header, .footer {
        font-weight: bold;
    }

    .footer {
        height: 40px;
        width: 100%;
        line-height: 40px;
        text-align: center;
    }

    & > hr {
        margin: 2px 0;
    }

    .panel {
        width: 100%;
        display: grid;
        grid-template-columns: 20px 1fr 20px;
        margin: auto;

        .side {
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
        }

        div.op-title {
            display: flex;
            align-items: end;
            justify-content: center;
            padding-top: 2px;
            padding-bottom: 3px;
            border-bottom: 1px solid gray;
        }

        .op-tbl {
            border-spacing: 0;
            border-collapse: collapse;
            .op-tbl-row {
                .op-tbl-cell {
                    padding: 0;
                    width: 20px;
                    height: 20px;
                    text-align: center;
                    line-height: 20px;
                    background-color: white;
                    border: 1px solid black;
                    cursor: default;
                    user-select: none;

                    &.even {
                        background-color: lightblue;
                    }

                    &.active {
                        background-color: #009fff;
                    }
                }
            }
        }

        .center {
            padding: 2px 0;

            hr {
                margin: 2px 0;
            }
            
            &.title  {
                display: grid;
            }
        }
    }

}