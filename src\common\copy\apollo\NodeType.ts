export enum NodeType {
    P = 'p',
    PPr = 'pPr',
    R = 'r',
    RPr = 'rPr',
    T = 't',
    Tbl = 'tbl',
    Row = 'tr',
    Col = 'tc',
    TblPr = 'tblPr',
    RowPr = 'trPr',
    ColPr = 'tcPr',
    Width = 'tcW',
    Height = 'trHeight',
    Merge = 'vMerge',
    GridCol = 'gridCol',
    OMediaMath = 'oMediaMath',
    MathValue = 'mathValue',
    MathText = 'mathText',
    Drawing = 'drawing',
    U = 'u',
    I = 'i',
    B = 'b',
    Color = 'color',
    Strike = 'strike',
    Su = 'vertAlign',
    Bg = 'highlight',
    Family = 'rFonts',
    Jc = 'jc',
    Spacing = 'spacing',
    Ind = 'ind',
    Fill = 'fill',
    Size = 'rSize',
    SdtStart = 'sdtStart',
    SdtEnd = 'sdtEnd',
    Region = 'region',
    RegionPr = 'regionPr',
    SpacingType = 'spacingtype',
    VertAlign = 'vertAlign',
    Hidden = 'bHidden',
    Button =  'button'
}

export enum HeaderPro {
    ProtectHeaderFooter = 'protectHeaderFooter',
    ProtectHeaderFooterSaved = 'protectHeaderFooterSaved',
    ShowHeader = 'showHeader',
    ShowFooter = 'ShowFooter',
}

export const G_LABEL = `@#$$%null`;

// 结构化元素属性
export enum StructurePro {
    Name = 'name',
    SerialNumber = 'serialNumber',
    Type = 'type',
    IsMustFill = 'isMustFill',
    DeleteProtect = 'deleteProtect',
    EditProtect = 'editProtect',
    CopyProtect = 'copyProtect',
    ShowBorder = 'showBorder',
    ReverseEdit = 'bReverseEdit',
    BorderString = 'borderString',
    IsHidden = 'isHidden',
    Placeholder = 'placeholder',
    IsPlaceholder = 'bPlaceholder',
    ShowBackground = 'showBackground',
    FixedLength = 'fixedLength',
    MaxLength = 'maxLength',
    CustomProperty = 'customProperty',
    CodeValueItems = 'codeValueItems',
    PrefixContent = 'prefixContent',
    SelectPrefixContent = 'selectPrefixContent',
    Separator = 'separator',
    MinValue = 'minValue',
    MaxValue = 'maxValue',
    Precision = 'precision',
    LogicEvent = 'logicEvent',
    Unit = 'unit',
    ForceValidate = 'forceValidate',
    DateBoxFormat = 'dateBoxFormat',
    DateTime = 'dateTime',
    Retrieve = 'retrieve',
    ShowValue = 'showValue',
    DisplayType = 'displayType',
    TipsContent = 'tipsContent',
    Checked = 'checked',
    ShowRight = 'showRight',
    PrintSelected = 'printSelected',
    Label = 'label',
    LabelCode = 'labelCode',
    SpaceNum = 'spaceNum',
    ShowType = 'showType',
    SupportMultLines = 'supportMultLines',
    Title = 'title',
    ShowTitle = 'showTitle',
    AutoDateFormat= 'autoDateFormat',
    StartDate = 'startDate',
    EndDate = 'endDate',
    SignatureCount = 'signatureCount',
    PreText = 'preText',
    SignatureSeparator = 'signatureSeparator',
    PostText = 'postText',
    SignaturePlaceholder = 'signaturePlaceholder',
    SignatureRatio = 'signatureRatio',
    RowHeightRestriction = 'rowHeightRestriction',
    TabJump = 'tabJump',
    BTextBorder = 'bTextBorder',
    Province = 'province',
    City = 'city',
    County = 'county',
    Hierarchy = 'hierarchy',
    BShowCodeAndValue = 'bShowCodeAndValue',
    SignType = 'signType',
    AlwaysShow = 'alwaysShow',
    ShowSignBorder = 'showSignBorder',
	Identifier = 'identifier',
    HideHasTitle = 'hideHasTitle',
    Alignments = 'alignments',
    EventInfo = 'eventInfo',
    ValueLabel = 'valueLabel',
    CodeLabel = 'codeLabel',
    Color = 'color',
    BPrint = 'bPrint',
    Cascades = 'cascades',
}

// 段落部分属性

export enum PortionPro {
    Color = 'w:val',
    U = 'w:val="single"',
    Sub = 'w:val="subscript"',
    Super = 'w:val="superscript"',
    Bg = 'w:val',
    Family = 'w:eastAsia',
    Size = 'w:size',
    VertAlign = 'vertalign'
}

// 段落属性
export enum ParagraphPro {
    Jc = 'w:val', // 对齐方式：right，left，both，center。默认的是both
    Spacing = 'w:line', // 行距
    Ind = 'w:left', // left 左缩进，right右缩进,hanging悬挂缩进，firstLine首行缩进，后2者互斥, 这里应该是左缩进
    SpacingType = 'w:spacingType',
}

// 图片属性
export enum ImagePro {
    Src = 'href',
    Width = 'width',
    Height = 'height',
    Name = 'name',
    AnchorType = 'anchor-type',
    PreferRelativeResize = 'preferRelativeResize',
    SizeProtect = 'sizeProtect',
    DeleteProtect = 'deleteProtect',
    CopyProtect = 'copyProtect',
    Type = 'type',
    // EquationElem = 'equationElem',
    // SecondaryType = 'secondaryType', // 二级分类
}

export enum OMediaMathPro {
    Width = 'width',
    Height = 'height',
    Name = 'name',
    MathType = 'math-type', // 公式类型 分式 为1 普通公式为2 月经史为3
    UseUnit = 'useUnit', // 是否使用单位 1 为使用 0为不使用
    UseDate1 = 'useDate1',
    UseDate2 = 'UseDate2',
    // PreferRelativeResize = 'preferRelativeResize',
    // SizeProtect = 'sizeProtect',
    // DeleteProtect = 'deleteProtect',
    // CopyProtect = 'copyProtect',
    // EquationElem = 'equationElem',
    // SecondaryType = 'secondaryType', // 二级分类
}

// export enum TblPro {
// }
export enum TablePro {
    W = 'width',
    Val = 'w:val',
    Type = 'w:type',
    Span = 'w:span',
    Col = 'col',
    AddRowProtect = 'addRowProtect',
    DelRowProtect = 'delRowProtect',
    FixedRowHeight = 'fixedRowHeight',
    FixedColWidth = 'fixedColWidth',
    DeleteProtect = 'deleteProtect',
    EditProtect = 'editProtect',
    HeaderNum = 'headerNum',
    BorderColor = 'borderColor',
    CellsVertAlign = 'cellsVertAlign',
    BorderLineSize = 'borderLineSize',
    RowsAuto = 'rowsAuto',
    RowHeights = 'rowHeights',
    CellLeft = 'cellLeft',
    CellRight = 'cellRight',
    CellTop = 'cellTop',
    CellBottom = 'cellBottom',
    CustomProperty = 'customProperty',
}

export enum RowPro {
    CanSplit = 'canSplit',
    TableHeader = 'tableHeader',
    Rule = 'hRule',
}

export enum ColPro {
    Protected = 'protected',
    BorderLeft = 'left',
    BorderRight = 'right',
    BorderTop = 'top',
    BorderBottom = 'bottom',
    BorderInsideH = 'insideH',
    BorderInsideV = 'insideV',
    Border = 'border',
    Color = 'color',
    Linetype = 'linetype',
    Style = 'style',
    Visible = 'visible',
    BAuto = 'bAuto',
    BPrint = 'bPrint',
    Space = 'space',
    DashArray = 'dashArray',
    BottomAfterCount = 'bottomAfterCount',
    BottomBeforeCount = 'bottomBeforeCount',
    MaxLeft = 'maxLeft',
    MaxRight = 'maxRight',
}

export class NodeStyle {
    public [NodeType.I]: string = 'font-style: italic;';
    public [NodeType.B]: string = 'font-weight: 700;';
    public [NodeType.U]: string = 'text-decoration: underline;';
    public [NodeType.Strike]: string = null;
    public sub: string;
    public super: string;

    public [NodeType.Bg](str: string): string {
        return 'background-color: ' + str + ';';
    }

    public [NodeType.Color](str: string, key: string): string {
        switch (key) {
            case PortionPro.Color:
                return 'color: ' + str + ';';
            default:
                return '';
        }
    }

    // public [NodeType.Drawing](str: string, key: string): string {
    //     switch (key) {
    //         case ImagePro.Name:
    //             return 'name: ' + str + ';';
    //         case ImagePro.Src:
    //             return 'src: ' + str + ';';
    //         case ImagePro.Width:
    //             return 'width: ' + str + ';';
    //         case ImagePro.Height:
    //             return 'height: ' + str + ';';
    //         case ImagePro.CopyProtect:
    //             return 'copyLocked: ' + str + ';';
    //         case ImagePro.Name:
    //             return 'name: ' + str + ';';
    //         case ImagePro.Name:
    //             return 'name: ' + str + ';';
    //         case ImagePro.Name:
    //             return 'name: ' + str + ';';
    //         case ImagePro.Name:
    //             return 'name: ' + str + ';';
    //         default:
    //             return '';
    //     }
    // }

    public [NodeType.Family.toLowerCase()](str: string, key: string): string {
        switch (key) {
            case PortionPro.Family.toLowerCase():
                return 'font-family: ' + str + ';';
            default:
                return '';
        }
    }

    public [NodeType.Jc](str: string, key: string): string {
        switch (key) {
            case ParagraphPro.Jc:
                return 'text-align: ' + str + ';';
            default:
                return '';
        }
    }

    public [NodeType.Size.toLowerCase()](str: string, key: string): string {
        switch (key) {
            case PortionPro.Size:
                return 'font-size: ' + str + 'px;';
            default:
                return '';
        }
    }

    public [NodeType.Spacing](str: string, key: string): string {
        switch (key) {
            case ParagraphPro.Spacing:
                return `line-height: ${str};`;
            default:
                return '';
        }
    }

    public [NodeType.SpacingType](str: string, key: string): string {
        switch (key) {
            case ParagraphPro.SpacingType.toLowerCase():
                return `line-type: ${str};`;
            default:
                return '';
        }
    }

    public [NodeType.Ind](str: string, key: string): string {
        switch (key) {
            case ParagraphPro.Ind:
                return `text-index: ${str};`;
            default:
                return '';
        }
    }

    public [NodeType.Fill](str: string): string {
        return 'background-color: ' + str + ';';
    }
}
