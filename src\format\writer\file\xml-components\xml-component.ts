import { BaseXmlComponent, escapeForXML, IXmlResult } from './base';
// export { BaseXmlComponent };

export class XmlComponent extends BaseXmlComponent {
    // tslint:disable-next-line:readonly-keyword
    public root: (BaseXmlComponent | string)[];

    constructor(rootKey: string) {
        super(rootKey);
        this.root = new Array<BaseXmlComponent>();
    }

    public prepForXml(): IXmlResult {
        let attrs = '';
        if (!this.root.forEach) {
            if (this.rootKey === '_attr' && typeof this.root === 'object' && !this['xmlKeys']) {
                Object.keys(this.root)
                .forEach((key) => {
                    const value = this.root[key];
                    attrs += ` ${escapeForXML(key)}='${escapeForXML(value)}'`;
                });
                return {attrs, text: null};
            }
            return;
        }
        let text = '';
        
        this.root.forEach((c) => {
            if (c instanceof BaseXmlComponent) {
                if (c.IsDeleted) {
                    return;
                }
                const cmp = c.prepForXml();
                if (c.IsDeleted) {
                    return;
                }
                const rooKey = c.getRootKey();
                if (rooKey === '_attr') {
                    if (cmp) {
                        attrs += cmp.attrs;
                    }
                } else {
                    const key = escapeForXML(rooKey);
                    if (cmp) {
                        const curAttrs = cmp.attrs || '';
                        text += `<${key}${curAttrs}>${cmp.text || ''}</${key}>`;
                    } else {
                        text += `<${key}/>`;
                    }
                }
            } else if (c) {
                text += escapeForXML(c);
            }
            
        });
        return {text, attrs};

        // const children = this.root
        //     .filter((c) => {
        //         if (c instanceof BaseXmlComponent) {
        //             return !c.IsDeleted;
        //         }
        //         return true;
        //     })
        //     .map((comp) => {
        //         if (comp instanceof BaseXmlComponent) {
        //             return comp.prepForXml();
        //         }
        //         return comp;
        //     })
        //     .filter((comp) => comp !== undefined); // Exclude undefined
        // return {
        //     [this.rootKey]: children,
        // };
    }

    public addChildElement(child: XmlComponent | string): XmlComponent {
        this.root.push(child);

        return this;
    }

    public delete(): void {
        this.deleted = true;
    }

    // helper function
    public getRoot(): (BaseXmlComponent | string)[] {
        return this.root;
    }
}
