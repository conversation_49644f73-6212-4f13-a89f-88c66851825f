import { NewControl } from './NewControl';
import DocumentContentBase from '../DocumentContentBase';
// tslint:disable-next-line: max-line-length
import { INewControlProperty, SignatureCountType, ResultType, NewControlType, NewControlContentSecretType, SignatureType } from '../../../common/commonDefines';
import Paragraph from '../Paragraph';
import { NEW_CONTROL_NAME } from '../NewControlManager';
import { NewControlText } from './NewControlText';
import ParaPortion from '../Paragraph/ParaPortion';
import { ParaElementType } from '../Paragraph/ParagraphContent';
import { ParaNewControlBorder } from '../Paragraph/ParaNewControlBorder';
import TextProperty from '../TextProperty';

/**
 * 结构化元素: 签名控件
 */
export class NewControlSignature extends NewControl {
    private signatureCount: SignatureCountType;
    private preText: string;
    private signatureSeparator: string;
    private postText: string;
    private signaturePlaceholder: string;
    private signatureRatio: number;
    private rowHeightRestriction: boolean;
    private curPara: Paragraph;
    private curPos: number;

    private signType: number;
    private alwaysShow: number;
    private showSignBorder: boolean;

    constructor(parent: DocumentContentBase, name: string, property: INewControlProperty, sText?: string ) {
        // // never show outer border
        // property.isNewControlShowBorder = false;

        super(parent, name, property, sText);

        this.signatureCount = property.signatureCount;
        this.preText = property.preText || '';
        this.signatureSeparator = property.signatureSeparator || '';
        this.postText = property.postText || '';
        this.signaturePlaceholder = property.signaturePlaceholder || '';
        this.signatureRatio = property.signatureRatio;
        this.rowHeightRestriction = property.rowHeightRestriction;

        this.signType = property.signType;
        this.alwaysShow = property.alwaysShow;
        this.showSignBorder = property.showSignBorder;

        // always false
        this.setPlaceHolder(false);
    }

    public setProperty(property: INewControlProperty): number {
        // update props start from here!!
        // console.log(property)
        if (this.curPara == null) {
            const startBorderPortion = this.getStartBorderPortion();
            this.curPara = startBorderPortion.paragraph;
            if (this.curPos == null) {
                // see addToParagraph() for consistency
                this.curPos = this.curPara.getPortionIndexById(startBorderPortion.id) - 1;
            }
        }
        // console.log(this.curPara, this.curPos);
        let res = super.setProperty(property);
        res = this.setSignType(property.signType) && res;
        res = this.setSignatureCount(property.signatureCount) && res;
        res = this.setPreText(property.preText) && res;
        res = this.setSignatureSeparator(property.signatureSeparator) && res;
        res = this.setPostText(property.postText) && res;
        res = this.setSignaturePlaceholder(property.signaturePlaceholder) && res;
        res = this.setSignatureRatio(property.signatureRatio) && res;
        res = this.setRowHeightRestriction(property.rowHeightRestriction) && res;
        if ( this.isSignSet() ) {
            res = this.setAlwaysShow(property.alwaysShow) && res;
            res = this.setShowSignBorder(property.showSignBorder) && res;
        }
        return res;
    }

    public getProperty(): INewControlProperty {
        const newControlProperty = super.getProperty();
        newControlProperty.signatureCount = this.signatureCount;
        newControlProperty.preText = this.preText;
        newControlProperty.signatureSeparator = this.signatureSeparator;
        newControlProperty.postText = this.postText;
        newControlProperty.signaturePlaceholder = this.signaturePlaceholder;
        newControlProperty.signatureRatio = this.signatureRatio;
        newControlProperty.rowHeightRestriction = this.rowHeightRestriction;
        newControlProperty.signType = this.signType;
        newControlProperty.alwaysShow = this.alwaysShow;
        newControlProperty.showSignBorder = this.showSignBorder;

        return newControlProperty;
    }

    public getSignatureCount(): number {
        return this.signatureCount;
    }

    public setSignatureCount(signatureCount: SignatureCountType): number {
        if ( signatureCount === this.signatureCount || null == signatureCount ) {
            return ResultType.UnEdited;
        }

        // const history = this.getDocumentParent() ? this.getDocumentParent()
        //                     .getHistory() : null;
        // history.addChange(new ChangeNewControlSignatureCount(this, this.signatureCount, signatureCount));
        if ( SignatureType.Common === this.signType ) {
            this.updateSingatureCount(signatureCount);
            this.signatureCount = signatureCount;
        } else {
            this.updateSingatureCount2(signatureCount);
            this.signatureCount = signatureCount;

            this.setAlwaysShow2();
        }
        this.setDirty();

        return ResultType.Success;
    }

    public getSignedElementNames(): string[] {
        let res = [];
        const list = this.getAllSubTextStructs();
        if (list) {
            list.forEach((control) => {
                if (control && !control.isPlaceHolderContent()) {
                    res.push(control.getNewControlName());
                }
            });
        }

        return res;
    }

    public getPreText(): string {
        return this.preText;
    }

    public setPreText(preText: string): number {
        if ( preText === this.preText || null == preText ) {
            return ResultType.UnEdited;
        }

        // const history = this.getDocumentParent() ? this.getDocumentParent()
        //                     .getHistory() : null;
        // history.addChange(new ChangeNewControlSignatureCount(this, this.signatureCount, signatureCount));
        this.updatePreText(preText);
        this.preText = preText;
        this.setDirty();

        return ResultType.Success;
    }

    public getSignatureSeparator(): string {
        return this.signatureSeparator;
    }

    public setSignatureSeparator(signatureSeparator: string): number {
        if ( signatureSeparator === this.signatureSeparator || null == signatureSeparator
            || this.isSignSet() ) {
            return ResultType.UnEdited;
        }

        // const history = this.getDocumentParent() ? this.getDocumentParent()
        //                     .getHistory() : null;
        // history.addChange(new ChangeNewControlSignatureCount(this, this.signatureCount, signatureCount));
        this.updateSingatureSeparator(signatureSeparator);
        this.signatureSeparator = signatureSeparator;
        this.setDirty();

        return ResultType.Success;
    }

    public getPostText(): string {
        return this.postText;
    }

    public setPostText(postText: string): number {
        if ( postText === this.postText || null == postText || this.isSignSet()) {
            return ResultType.UnEdited;
        }

        // if ( this.isSignSet() ) {
        //     this.postText = postText;
        //     this.setDirty();
        //     return ResultType.UnEdited;
        // }

        // const history = this.getDocumentParent() ? this.getDocumentParent()
        //                     .getHistory() : null;
        // history.addChange(new ChangeNewControlSignatureCount(this, this.signatureCount, signatureCount));
        this.updatePostText(postText);
        this.postText = postText;
        this.setDirty();

        return ResultType.Success;
    }

    public getSignaturePlaceholder(): string {
        return this.signaturePlaceholder;
    }

    public setSignaturePlaceholder(signaturePlaceholder: string): number {
        if ( signaturePlaceholder === this.signaturePlaceholder || null == signaturePlaceholder ) {
            return ResultType.UnEdited;
        }

        // const history = this.getDocumentParent() ? this.getDocumentParent()
        //                     .getHistory() : null;
        // history.addChange(new ChangeNewControlSignatureCount(this, this.signatureCount, signatureCount));
        this.updateSignaturePlaceholder(signaturePlaceholder);
        this.signaturePlaceholder = signaturePlaceholder;
        this.setDirty();

        return ResultType.Success;
    }

    public getSignatureRatio(): number {
        return this.signatureRatio;
    }

    public setSignatureRatio(signatureRatio: number): number {
        if ( signatureRatio === this.signatureRatio || null == signatureRatio ) {
            return ResultType.UnEdited;
        }

        // const history = this.getDocumentParent() ? this.getDocumentParent()
        //                     .getHistory() : null;
        // history.addChange(new ChangeNewControlSignatureCount(this, this.signatureCount, signatureCount));
        this.signatureRatio = signatureRatio;
        this.setDirty();

        return ResultType.Success;
    }

    public getRowHeightRestriction(): boolean {
        return this.rowHeightRestriction;
    }

    public setRowHeightRestriction(rowHeightRestriction: boolean): number {
        if ( rowHeightRestriction === this.rowHeightRestriction || null == rowHeightRestriction ) {
            return ResultType.UnEdited;
        }

        // const history = this.getDocumentParent() ? this.getDocumentParent()
        //                     .getHistory() : null;
        // history.addChange(new ChangeNewControlSignatureCount(this, this.signatureCount, signatureCount));
        this.rowHeightRestriction = rowHeightRestriction;
        this.setDirty();

        return ResultType.Success;
    }

    public setSignType(signType: number): number {
        if ( signType === this.signType || null == signType ) {
            return ResultType.UnEdited;
        }

        // const history = this.getDocumentParent() ? this.getDocumentParent()
        //                     .getHistory() : null;
        // history.addChange(new ChangeNewControlSignatureCount(this, this.signatureCount, signatureCount));
        this.signType = signType;
        this.setDirty();

        return ResultType.Success;
    }

    public setAlwaysShow(alwaysShow: number): number {
        if ( alwaysShow === this.alwaysShow || null == alwaysShow
            || alwaysShow > this.signatureCount || 1 > alwaysShow ) {
            return ResultType.UnEdited;
        }

        // const history = this.getDocumentParent() ? this.getDocumentParent()
        //                     .getHistory() : null;
        // history.addChange(new ChangeNewControlSignatureCount(this, this.signatureCount, signatureCount));
        const hiddenNumber = this.alwaysShow - alwaysShow;
        let startPos;
        let endPos;
        const leafList = this.getLeafList();

        if ( 0 < hiddenNumber ) {
            const fisrtNewControl = leafList[0];
            const lastNewControl = leafList[leafList.length - alwaysShow - 1];
            const startPortion = fisrtNewControl.getStartBorderPortion();
            let endPortion = lastNewControl.getEndBorderPortion();

            startPos = startPortion.getCurInParaIndex();
            endPos = endPortion.getCurInParaIndex();

            if ( 1 < alwaysShow ) {
                endPortion = leafList[leafList.length - alwaysShow].getStartBorderPortion();
                endPos = endPortion.getCurInParaIndex() - 1;
            }

            for (let index = 0; index < leafList.length - alwaysShow; index++) {
                const newControl = leafList[index];
                if (newControl) {
                    newControl.setHidden(true);
                }
            }

        } else {
            const fisrtNewControl = leafList[leafList.length - alwaysShow];
            const lastNewControl = leafList[leafList.length - 1];
            const startPortion = fisrtNewControl.getStartBorderPortion();
            const endPortion = lastNewControl.getEndBorderPortion();

            startPos = startPortion.getCurInParaIndex();
            endPos = endPortion.getCurInParaIndex();

            for (let index = leafList.length - alwaysShow; index < leafList.length; index++) {
                const newControl = leafList[index];
                if (newControl) {
                    newControl.setHidden(false);
                }
            }
        }

        // this.curPara.setNewControlTextHidden(0 < hiddenNumber ? true : false, startPos, endPos);

        this.alwaysShow = alwaysShow;
        this.setDirty();

        return ResultType.Success;
    }

    public setAlwaysShow2(): boolean {
        if ( this.alwaysShow < this.signatureCount && this.isSignSet() ) {
            const leafList = this.getLeafList();

            if (leafList && 0 < leafList.length) {
                let startPos;
                let endPos;
                const fisrtNewControl = leafList[0];
                const lastNewControl = leafList[leafList.length - this.alwaysShow - 1];
                const startPortion = fisrtNewControl.getStartBorderPortion();
                let endPortion = lastNewControl.getEndBorderPortion();

                startPos = startPortion.getCurInParaIndex();
                endPos = endPortion.getCurInParaIndex();

                if ( 1 < this.alwaysShow ) {
                    endPortion = leafList[leafList.length - this.alwaysShow].getStartBorderPortion();
                    endPos = endPortion.getCurInParaIndex() - 1;
                }

                for (let index = 0; index < leafList.length - this.alwaysShow; index++) {
                    const newControl = leafList[index];
                    if (newControl) {
                        newControl.setHidden(true);
                    }
                }

                // startPortion.paragraph.setNewControlTextHidden(true, startPos, endPos);
                return true;
            }
        }

        return false;
    }

    public setShowSignBorder(showSignBorder: boolean): number {
        if ( showSignBorder === this.showSignBorder || null == showSignBorder ) {
            return ResultType.UnEdited;
        }

        // const history = this.getDocumentParent() ? this.getDocumentParent()
        //                     .getHistory() : null;
        // history.addChange(new ChangeNewControlSignatureCount(this, this.signatureCount, signatureCount));
        this.showSignBorder = showSignBorder;
        const leafList = this.getLeafList();
        if (leafList && 0 < leafList.length) {
            leafList.forEach((newControl) => {
                if (newControl && newControl.isShowBorder() !== showSignBorder) {
                    newControl.setShowBorder(showSignBorder);
                }
            });
        }
        this.setDirty();

        return ResultType.Success;
    }

    public setShowSignBorder2(): boolean {
        if ( this.isSignSet() && !this.showSignBorder ) {
            const leafList = this.getLeafList();
            if (leafList && 0 < leafList.length) {
                leafList.forEach((newControl) => {
                    if (newControl) {
                        newControl.setShowBorder(false);
                    }
                });

                return true;
            }
        }

        return false;
    }

    public getSignType(): number {
        return this.signType;
    }

    public getAlwaysShow(): number {
        return this.alwaysShow;
    }

    public getShowSignBorder(): boolean {
        return this.showSignBorder;
    }

    /**
     * 新增方法，专门处理签名控件的字体继承
     * 首先调用原始方法添加控件到段落，然后同步应用字体属性到所有子控件和文本元素
     */
    public addToParagraphWithFontInheritance(para: Paragraph, curPos: number): void {
        // 首先调用原始方法添加控件到段落
        this.addToParagragh(para, curPos);
        
        // 获取段落的字体属性
        const textProperty = new TextProperty();
        if (para && para.content && para.content.length > 0) {
            // 从段落的第一个portion获取字体属性
            const portion = para.content[0];
            if (portion && portion.textProperty) {
                textProperty.fontFamily = portion.textProperty.fontFamily;
                textProperty.fontSize = portion.textProperty.fontSize;
                textProperty.fontStyle = portion.textProperty.fontStyle;
                textProperty.fontWeight = portion.textProperty.fontWeight;
                textProperty.color = portion.textProperty.color;
                // 其他字体属性可以根据需要添加
            }
        }
        
        // 1. 同步应用字体属性到所有子控件
        const leafList = this.getLeafList();
        if (leafList && leafList.length > 0) {
            for (let i = 0; i < leafList.length; i++) {
                leafList[i].applyTextProperty(textProperty);
            }
        }
        
        // 2. 应用字体属性到段落中的ParaPortion元素
        if (para && para.content) {
            // 在签名控件的起始位置开始查找
            // 根据签名控件的类型和结构判断需要处理的范围
            const startPos = curPos + 1; // 控件后的第一个元素
            let endPos = startPos;
            
            // 对于普通签名控件，需要处理的范围是从控件后到最后一个子控件的位置
            if (SignatureType.Common === this.signType) {
                // 如果有子控件，范围就是到最后一个子控件的位置
                if (leafList && leafList.length > 0) {
                    // 每个子控件占据4个位置，加上前后缀和分隔符
                    endPos = curPos + (leafList.length * 4) + 3;
                } else {
                    // 如果没有子控件，则只处理前后缀
                    endPos = curPos + 3;
                }
            } else if (this.isSignSet()) {
                // 对于签名组，需要根据其结构确定范围
                // 这里简化处理，实际实现可能需要更复杂的逻辑
                if (leafList && leafList.length > 0) {
                    endPos = curPos + (leafList.length * 2) + 3;
                } else {
                    endPos = curPos + 3;
                }
            }
            
            // 遍历范围内的所有元素，应用字体属性到ParaPortion元素
            for (let i = startPos; i <= endPos && i < para.content.length; i++) {
                const element = para.content[i];
                if (element && element.type === ParaElementType.ParaPortion) {
                    // 对于ParaPortion元素，直接设置其textProperty
                    if (element.textProperty) {
                        element.textProperty.fontFamily = textProperty.fontFamily;
                        element.textProperty.fontSize = textProperty.fontSize;
                        element.textProperty.fontStyle = textProperty.fontStyle;
                        element.textProperty.fontWeight = textProperty.fontWeight;
                        element.textProperty.color = textProperty.color;
                    }
                }
            }
        }
    }

    public addToParagragh( para: Paragraph, curPos: number ): void {
        // console.log(curPos) // -> portion index
        super.addToParagragh(para, curPos);
        this.curPara = para;
        this.curPos = curPos;
        // console.log(this.curPara)

        let portion = new ParaPortion(para);

        if ( SignatureType.Common === this.signType ) {
            // pretext is always inserted
            portion.addText(this.preText);
            para.addToContent(curPos + 2, portion);

            // posttext is always added <- trick is to view portion index dynamically
            portion = new ParaPortion(para);
            portion.addText(this.postText);
            para.addToContent(curPos + 3, portion);

            // add leaflist textControls' ui
            const leafList = this.getLeafList();
            // console.log(leafList)
            const indexOfAmpersand = this.signatureSeparator.indexOf('&');
            for (let i = 0, len = leafList.length; i < len; i++) {
                // [{pre}[_]{sep1}[_]{sep2}[_]{post}{signph}]
                if (i > 0) {
                    // add separator if >1 signatures exist
                    let curSignSeparator = this.signatureSeparator;
                    if (indexOfAmpersand !== -1) {
                        if (i === 1) {
                            curSignSeparator = curSignSeparator.slice(0, indexOfAmpersand);
                        } else if (i === 2) {
                            curSignSeparator = curSignSeparator.slice(indexOfAmpersand + 1);
                        } else {
                            // tslint:disable-next-line: no-console
                            console.warn('more than 3 sub signs detected');
                        }
                    }
                    // console.log(i)
                    // console.log(curSignSeparator)
                    portion = new ParaPortion(para);
                    // portion.addText(this.signatureSeparator);
                    portion.addText(curSignSeparator);
                    // para's addToContent, not +1!
                    para.addToContent(curPos + (i * 4) + 2, portion);
                }
                // pay attention: it's newControl's addToParagragh! would + 1 automatically
                leafList[i].addToParagragh(para, curPos + (i * 4) + 2);
            }

            // console.log(para)
        } else if ( this.isSignSet() ) {
            const leafList = this.getLeafList();
            const startBorderPortion = this.getStartBorderPortion();
            const insertPos = para.getPortionIndexById(startBorderPortion.id);

            for (let i = leafList.length - 1; i >= 0; i--) {
                // [{pre}[_]{sep1}[_]{sep2}[_]{post}{signph}]
                // if ( leafList.length - 2 > i && this.signatureSeparator ) {
                //     portion = new ParaPortion(para);
                //     portion.addText(this.signatureSeparator);
                //     para.addToContent(insertPos + 1, portion);
                // }

                leafList[i].addToParagragh(para, (leafList.length - 1 === i ? insertPos + 1 : insertPos));

                if ( leafList.length - 1 === i ) {
                    portion.addText(this.preText);
                    para.addToContent(insertPos + 1, portion);
                }
            }
        }

    }

    public addSubStructs(signCount: number, prevSignCount: number): void {
        // signCount must > prevSignCount to enter here
        const addCount = signCount - prevSignCount;
        if (this.curPara == null) {
            return ;
        }
        const leafList = this.getLeafList();
        // console.log(leafList)

        let portion = new ParaPortion(this.curPara);
        const para = this.curPara;
        const leafListLen = leafList.length;

        // only check the first &. PRD suggested.
        const indexOfAmpersand = this.signatureSeparator.indexOf('&');
        for (let i = 0; i < addCount; i++) {
            const signatureEndBracketPortionIndex = this.getSignatureEndBracketPortionIndex();
            // console.log(signatureEndBracketPortionIndex)
            // add ui for newly added leaf
            if (i > 0) {
                portion = new ParaPortion(para);
            }
            let curSignSeparator = this.signatureSeparator;
            if (indexOfAmpersand !== -1) {
                const separatorIndex = prevSignCount + i;
                // console.log(separatorIndex)
                if (separatorIndex === 1) {
                    curSignSeparator = curSignSeparator.slice(0, indexOfAmpersand);
                } else if (separatorIndex === 2) {
                    curSignSeparator = curSignSeparator.slice(indexOfAmpersand + 1);
                    // console.log(curSignSeparator)
                } else {
                    // tslint:disable-next-line: no-console
                    console.warn('more than 3 sub signs detected');
                }
            }
            // add separator
            // portion.addText(this.signatureSeparator);
            portion.addText(curSignSeparator);
            
            // 为间隔字符portion应用字体属性
            const separatorTextProperty = new TextProperty();
            if (para && para.content && para.content.length > 0) {
                // 从段落的第一个portion获取字体属性
                const firstPortion = para.content[0];
                if (firstPortion && firstPortion.textProperty) {
                    separatorTextProperty.fontFamily = firstPortion.textProperty.fontFamily;
                    separatorTextProperty.fontSize = firstPortion.textProperty.fontSize;
                    separatorTextProperty.fontStyle = firstPortion.textProperty.fontStyle;
                    separatorTextProperty.fontWeight = firstPortion.textProperty.fontWeight;
                    separatorTextProperty.color = firstPortion.textProperty.color;
                }
            }
            // 应用字体属性到间隔字符portion
            if (portion.textProperty) {
                portion.textProperty.fontFamily = separatorTextProperty.fontFamily;
                portion.textProperty.fontSize = separatorTextProperty.fontSize;
                portion.textProperty.fontStyle = separatorTextProperty.fontStyle;
                portion.textProperty.fontWeight = separatorTextProperty.fontWeight;
                portion.textProperty.color = separatorTextProperty.color;
            }

            let potentialInsertedPortionIndex = signatureEndBracketPortionIndex - 2;

            // para's addToContent, not +1!
            const potentialInsertedPortion = para.content[potentialInsertedPortionIndex];

            // bugfix: save/load may remove empty portion
            if (potentialInsertedPortion.content[0] instanceof ParaNewControlBorder) {
                potentialInsertedPortionIndex += 1;
            }

            para.addToContent(potentialInsertedPortionIndex, portion);
            // pay attention: it's newControl's addToParagragh! would + 1 automatically
            // AND separator is added! so +1 one more time
            const newControl = leafList[leafListLen - addCount + i];
            newControl.addToParagragh(para, potentialInsertedPortionIndex - 1 + 1);
            
            // 为新增的子控件应用字体属性
            const textProperty = new TextProperty();
            if (para && para.content && para.content.length > 0) {
                // 从段落的第一个portion获取字体属性
                const portion = para.content[0];
                if (portion && portion.textProperty) {
                    textProperty.fontFamily = portion.textProperty.fontFamily;
                    textProperty.fontSize = portion.textProperty.fontSize;
                    textProperty.fontStyle = portion.textProperty.fontStyle;
                    textProperty.fontWeight = portion.textProperty.fontWeight;
                    textProperty.color = portion.textProperty.color;
                }
            }
            newControl.applyTextProperty(textProperty);

        }
    }

    public updatePreText(preText: string): void {
        if (preText === this.preText) {
            return ;
        }

        if (SignatureType.Common === this.signType) {
            if (this.curPara != null && this.curPos != null) {
                let preTextPortion = this.curPara.content[this.curPos + 2];
                // console.log(preTextPortion);
                const text = preTextPortion.getText();
                // this.preText hasnt updated yet
                if (text !== this.preText) {
                    if (text.length === 0) {
                        // empty portion. look one portion after
                        preTextPortion = this.curPara.content[this.curPos + 3];
                    } else {
                        // tslint:disable-next-line: no-console
                        console.warn('cannot correctly set signature preText');
                    }
                }

                preTextPortion.content = [];
                preTextPortion.addText(preText);
            }
        } else if (this.isSignSet()) {
            if (this.curPara != null && this.curPos != null) {
                const leafList = this.getLeafList();
                const lastNewControl = leafList ? leafList[leafList.length - 1] : null;
                const endBorderPortion = lastNewControl ? lastNewControl.getStartBorderPortion() : null;

                if ( endBorderPortion ) {
                    const pos = endBorderPortion.getCurInParaIndex();
                    let preTextPortion = this.curPara.content[pos - 1];
                    // console.log(preTextPortion);
                    const text = preTextPortion.getText();
                    // this.preText hasnt updated yet
                    if (!preTextPortion.isNewControlEnd() && !preTextPortion.isNewControlStart()) {
                        if (text !== this.preText) {
                            if (text.length === 0 && this.preText) {
                                // empty portion. look one portion after
                                preTextPortion = this.curPara.content[pos - 2];
                            }
                        }

                        preTextPortion.content = [];
                        preTextPortion.addText(preText);
                    } else {
                        const portion = new ParaPortion(this.curPara);
                        portion.addText(preText);
                        this.curPara.addToContent(pos, portion);
                    }
                }
            } else {
                // tslint:disable-next-line: no-console
                console.warn('cannot correctly set signature preText');
            }
        }
    }

    public updatePostText(postText: string): void {
        if (postText === this.postText) {
            return ;
        }

        if (this.curPara != null && this.curPos != null) {
            let postTextPortion = null;

            const signatureEndBracketPortionIndex = this.getSignatureEndBracketPortionIndex();
            postTextPortion = this.curPara.content[signatureEndBracketPortionIndex - 2];
            if (postTextPortion.content[0] instanceof ParaNewControlBorder) {
                // save and read process eliminated empty portion
                postTextPortion = this.curPara.content[signatureEndBracketPortionIndex - 1];
            }
            // console.log(postTextPortion)

            if (postTextPortion != null) {
                postTextPortion.content = [];
                postTextPortion.addText(postText);
            }

        }
    }

    public updateSingatureSeparator(signatureSeparator: string, bForceUpdate: boolean = false): void {
        if (signatureSeparator === this.signatureSeparator && bForceUpdate === false) {
            return ;
        }
        if (this.signatureCount < 2) {
            return ;
        }

        if ( SignatureType.Common === this.signType ) {
            let singaureSeparatorPortion = null;
            if (this.curPara != null && this.curPos != null) {
                const signatureEndBracketPortionIndex = this.getSignatureEndBracketPortionIndex();
                const subRightBracketIndexes = [];

                // find all SUB text structs' ] portion
                for (let i = this.curPos + 1; i < signatureEndBracketPortionIndex; i++) {
                    const curPortion = this.curPara.content[i];
                    const paraBorder = curPortion.content[0];
                    if (paraBorder != null && paraBorder.type === ParaElementType.ParaNewControlBorder) {
                        if (paraBorder.isNewControlEndBoder()) {
                            subRightBracketIndexes.push(i);
                        }
                    }
                }

                // remove the last index
                subRightBracketIndexes.splice(subRightBracketIndexes.length - 1, 1);
                // console.log(subRightBracketIndexes);

                const indexOfAmpersand = signatureSeparator.indexOf('&');
                for (const rightBracketIndex of subRightBracketIndexes) {
                    singaureSeparatorPortion = this.curPara.content[rightBracketIndex + 1];

                    let curSignSeparator = signatureSeparator;
                    if (indexOfAmpersand !== -1) {
                        // forward find, so not affected by read remove empty portion
                        if (rightBracketIndex === 5 || rightBracketIndex === 4) { // 4: first portion of para case
                            curSignSeparator = curSignSeparator.slice(0, indexOfAmpersand);
                        } else if (rightBracketIndex === 9 || rightBracketIndex === 8) { // 8: first portion of para case
                            curSignSeparator = curSignSeparator.slice(indexOfAmpersand + 1);
                        } else {
                            // tslint:disable-next-line: no-console
                            console.warn('more than 3 sub signs detected');
                        }
                    }

                    if (singaureSeparatorPortion != null) {
                        singaureSeparatorPortion.content = [];
                        // singaureSeparatorPortion.addText(signatureSeparator);
                        singaureSeparatorPortion.addText(curSignSeparator);
                    }
                }
            }
        // } else if (this.isSignSet()) {
        //     if (3 > this.signatureCount) {
        //         return ;
        //     }

        //     const leafList = this.getLeafList();
        //     for (let index = 1; index <= leafList.length - 2; index++) {
        //         const newControl = leafList[index];
        //         if (newControl) {
        //             const endBorderPortion = newControl.getStartBorderPortion();

        //             if ( endBorderPortion ) {
        //                 const pos = endBorderPortion.getCurInParaIndex();
        //                 let preTextPortion = this.curPara.content[pos - 1];
        //                 // console.log(preTextPortion);
        //                 const text = preTextPortion.getText();
        //                 // this.preText hasnt updated yet
        //                 if (!preTextPortion.isNewControlEnd()) {
        //                     if (text !== this.preText) {
        //                         if (text.length === 0 && this.signatureSeparator) {
        //                             // empty portion. look one portion after
        //                             preTextPortion = this.curPara.content[pos - 2];
        //                         }
        //                     }

        //                     preTextPortion.content = [];
        //                     preTextPortion.addText(signatureSeparator);
        //                 } else {
        //                     const portion = new ParaPortion(this.curPara);
        //                     portion.addText(signatureSeparator);
        //                     if (leafList[index - 1] && leafList[index - 1].isHidden()) {
        //                         portion.setHidden(true);
        //                     }
        //                     this.curPara.addToContent(pos, portion);
        //                 }
        //             }
        //         }
        //     }
        }
    }

    public updateSignaturePlaceholder(signaturePlaceholder: string): void {
        if (signaturePlaceholder === this.signaturePlaceholder) {
            return ;
        }
        const leafList = this.getLeafList();
        for (let i = 0, len = leafList.length; i < len; i++) {
            leafList[i].setPlaceHolderContent(signaturePlaceholder);
        }
    }

    public updateSingatureCount(signatureCount: SignatureCountType): void {
        if (signatureCount === this.signatureCount) {
            return ;
        }
        if (this.curPara == null || this.curPos == null) {
            return ;
        }

        if (signatureCount < this.signatureCount) {
            // remove text structs
            const removeCount = this.signatureCount - signatureCount;

            const signatureEndBracketPortionIndex = this.getSignatureEndBracketPortionIndex();
            const subStructsNames = [];

            // store sub structs' names. TODO: better
            for (let i = this.curPos + 1; i < signatureEndBracketPortionIndex; i++) {
                const curPortion = this.curPara.content[i];
                const paraBorder = curPortion.content[0];
                if (paraBorder != null && paraBorder.type === ParaElementType.ParaNewControlBorder) {
                    if (paraBorder.isNewControlEndBoder()) {
                        subStructsNames.push(paraBorder.getNewControlName());
                    }
                }
            }

            // console.log(subStructsNames);
            const endIndex = subStructsNames.length - 1;
            const newControlManager = this.getNewControlManager();

            for (let count = 0; count < removeCount; count++) {
                const curTextStructName = subStructsNames[endIndex - count];
                newControlManager.removeNewControlByName(curTextStructName);

                let startPortionIndex = null;
                let endPortionIndex = null;

                // remove ui
                for (let i = this.curPos + 1; i < signatureEndBracketPortionIndex; i++) {
                    const curPortion = this.curPara.content[i];
                    const paraBorder = curPortion.content[0];
                    if (paraBorder != null && paraBorder.type === ParaElementType.ParaNewControlBorder) {
                        if (paraBorder.getNewControlName() === curTextStructName) {
                            if (paraBorder.isNewControlStartBoder()) {
                                startPortionIndex = i;
                            }
                            if (paraBorder.isNewControlEndBoder()) {
                                endPortionIndex = i;
                                break;
                            }
                        }
                    }
                }
                // console.log(startPortionIndex, endPortionIndex)

                if (this.curPara) {
                    this.curPara.removeNewControlText(startPortionIndex, endPortionIndex);

                    // if signatureSeparator portion exists(must exist), also remove
                    const separatorPortionIndex = startPortionIndex - 1;
                    const separatorPortion = this.curPara.content[separatorPortionIndex];
                    const portionText = separatorPortion.getText();
                    // console.log(portionText)
                    // if (portionText === this.signatureSeparator) {
                    if (this.signatureSeparator.indexOf(portionText) !== -1) {
                        this.curPara.removePortion(separatorPortionIndex, 1);
                    }
                }
            }

        } else if (signatureCount > this.signatureCount) {
            // add text structs
            const addCount = signatureCount - this.signatureCount;

            // inside
            this.addSubTextStructs(addCount);

            // outside
            this.addSubStructs(signatureCount, this.signatureCount);
        }

        // force update necessary ui stuffs
        // this.signatureCount = signatureCount;
        // this.updateSingatureSeparator(this.signatureSeparator, true);
    }

    public getSubTextStructNameBySignCount(signCount: number): string {
        // 1, 2, 3
        let result = '';

        const leafList = this.getLeafList();
        const subStruct = leafList[signCount - 1];
        if (subStruct != null) {
            result = subStruct.getNewControlName();
        } else {
            // tslint:disable-next-line: no-console
            console.warn('cannot find required leafList in signature struct');
        }

        return result;
    }

    public getSubTextStructBySignCount(signCount: number): NewControlText {
        // 1, 2, 3
        let result = null;

        const leafList = this.getLeafList();
        const subStruct = leafList[signCount - 1];
        if (subStruct != null) {
            result = subStruct;
        } else {
            // tslint:disable-next-line: no-console
            console.warn('cannot find required leafList in signature struct');
        }

        return result;
    }

    public getAllSubTextStructs(): NewControlText[] {
        const result = [];

        const leafList = this.getLeafList();
        for (const leaf of leafList) {
            result.push(leaf);
        }

        return result;
    }

    public updateSingatureCount2(signatureCount: number): void {
        if (signatureCount === this.signatureCount) {
            return ;
        }
        if (this.curPara == null || this.curPos == null) {
            return ;
        }

        if (signatureCount < this.signatureCount) {
            // remove text structs
            const removeCount = this.signatureCount - signatureCount;
            const leafList = this.getLeafList();
            const newControlManager = this.getNewControlManager();

            let startPortion;
            let endPortion;
            for (let count = 0; count < removeCount; count++) {
                const newControl = leafList[0];
                const curTextStructName = newControl.getNewControlName();
                if ( 0 === count ) {
                    startPortion = newControl.getStartBorderPortion();
                }
                endPortion = newControl.getEndBorderPortion();
                newControlManager.removeNewControlByName(curTextStructName);
            }

            if ( 2 <= signatureCount ) {
                endPortion = leafList[0].getStartBorderPortion();
            }

            const startPos = startPortion.getCurInParaIndex();
            let endPos = endPortion.getCurInParaIndex();
            if ( 2 <= signatureCount ) {
                endPos--;
            }

            this.curPara.removeNewControlText(startPos, endPos);
        } else if (signatureCount > this.signatureCount) {
            // add text structs
            const addCount = signatureCount - this.signatureCount;

            // inside
            this.addSubTextStructs(addCount);

            // outside
            this.addSubStructs2(signatureCount, this.signatureCount);
        }
    }

    public addSubStructs2(signCount: number, prevSignCount: number): void {
        // signCount must > prevSignCount to enter here
        const addCount = signCount - prevSignCount;
        if (this.curPara == null) {
            return ;
        }
        const leafList = this.getLeafList();
        // console.log(leafList)

        const para = this.curPara;
        const startBorderPortion = this.getStartBorderPortion();
        const insertPos = para.getPortionIndexById(startBorderPortion.id);

        // only check the first &. PRD suggested.
        // const indexOfAmpersand = this.signatureSeparator.indexOf('&');
        let count = 0;
        for (let i = addCount - 1; i >= 0; i--) {
            count++;

            if ( (2 <= prevSignCount || 2 < count + prevSignCount) && this.signatureSeparator ) {
                const portion = new ParaPortion(this.curPara);
                portion.addText(this.signatureSeparator);
                para.addToContent(insertPos + 1, portion);
            }

            leafList[i].addToParagragh(para, insertPos);
        }
    }

    public getSubTextStructBySignName(name: string): NewControlText {
        let result = null;
        const leafList = this.getLeafList();

        // tslint:disable-next-line: prefer-for-of
        for (let index = 0; index < leafList.length; index++) {
            const newControl = leafList[index];
            if (name === newControl.getNewControlName()) {
                result = newControl;
                break;
            }
        }

        return result;
    }

    public isSignSet(): boolean {
        return (SignatureType.Set === this.signType);
    }

    public setNewControlText(sText: string, bRefresh?: boolean): number {
        return ResultType.UnEdited;
    }

    // helper function
    private getSignatureEndBracketPortionIndex(): number {
        let portionIndex = 0;
        let treatedCurPos = this.curPos;
        if (this.curPos < 0) {
            treatedCurPos = 0;
        }
        if (this.curPara != null && treatedCurPos != null) {
            for (let i = treatedCurPos, len = this.curPara.content.length; i < len; i++) {
                const curPortion = this.curPara.content[i];
                const paraBorder = curPortion.content[0];
                if (paraBorder != null && paraBorder.type === ParaElementType.ParaNewControlBorder) {
                    if (paraBorder.isNewControlEndBoder() &&
                        paraBorder.getNewControlName() === this.getNewControlName()) {

                        portionIndex = i;
                        break;
                    }
                }
            }
        }
        return portionIndex;
    }

    private addSubTextStructs(addCount: number): void {
        if (this.curPara == null) {
            return ;
        }

        const textProperty: INewControlProperty = {
            newControlName: undefined,
            newControlSerialNumber: undefined,
            newControlInfo: undefined,
            newControlPlaceHolder: this.signaturePlaceholder,
            newControlTitle: undefined,
            newControlType: NewControlType.TextBox,
            isNewControlHidden: false,
            isNewControlCanntDelete: true,
            isNewControlCanntEdit: true,
            isNewControlMustInput: false,
            isNewControlShowBorder: this.showSignBorder,
            isNewControlReverseEdit: false,
            isNewControlHiddenBackground: true,
            newControlDisplayType: NewControlContentSecretType.DontSecret,
            newControlMaxLength: undefined,
            newControlFixedLength: undefined,
            customProperty: undefined,
            tabJump: false,
        };

        const textControlCount = addCount;
        const newControlManager = this.getNewControlManager();
        let textUniqueName = newControlManager.makeUniqueName(textProperty.newControlType, textProperty.newControlName);
        let newControlText = new NewControlText(this.curPara.parent, textUniqueName, textProperty);
        const textNameLen = NEW_CONTROL_NAME.get(NewControlType.TextBox).length;
        const newControlNames = newControlManager.getNameMap();

        for (let i = 0; i < textControlCount; i++) {

            if (i !== 0) {
                textUniqueName = NEW_CONTROL_NAME.get(NewControlType.TextBox) +
                    (+textUniqueName.slice(textNameLen) + i) ;

                const loopSafeCheck = 100;
                let count = 1;
                while (newControlNames.has(textUniqueName)) {
                    textUniqueName = NEW_CONTROL_NAME.get(NewControlType.TextBox) +
                        (+textUniqueName.slice(textNameLen) + 1) ;
                    count++;
                    if (count > loopSafeCheck) {
                        // tslint:disable-next-line: no-console
                        console.warn('max loop time reached in newcontrolsignature');
                        break;
                    }
                }
                // console.log(textUniqueName)
                newControlText = new NewControlText(this.curPara.parent, textUniqueName, textProperty);
            }

            // add to leafList. two-way
            newControlText.setParent(this);

            const leafList = this.getLeafList();
            if ( SignatureType.Set !== this.signType ) {
                leafList.push(newControlText);
            } else {
                leafList.splice(0, 0, newControlText);
            }

            // map
            newControlNames.set(textUniqueName, newControlText);
        }
    }

}
