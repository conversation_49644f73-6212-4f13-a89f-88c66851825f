import { ITableCellProperty, ITableProperty, ResultType, NISTableCellType,
    MessageType,
    ITableCellContentProps,
    TableCellRange,
    NISTableCellColor,
    NISSelectType,
    INISRowProperty,
    INISProperty,
    CodeValueItem,
    DataType,
    NISRowType,
    INISTableInfoData,
    NISDateBoxFormat,
    INISCellGridLine,
    IExternalNisProps,
    CellTimeFormat} from '../../common/commonDefines';
import { IDrawSelectionsCell } from '../DocumentCore';
import Document from './Document';
import DocumentContentBase from './DocumentContentBase';
import { HistoryDescriptionType } from './HistoryDescription';
import { TableSelectionType } from './Selection';
import { TableCell } from './Table/TableCell';
import { TableRecalculate2 } from './Table/TableRecalculate2';
import { TableBase } from './TableBase';
import { GlobalEvent as gEvent , GlobalEventName as gEventName } from '../../common/GlobalEvent';
import { TableRow } from './Table/TableRow';
import { VerticalMergeType } from './Table/TableCellProperty';
import { TableRowLineRule } from './Table/TableRowProperty';
import { getPxForMM } from './util';
import { DocumentColor } from './Style';

export class NISTable extends TableBase {
    // private tableHeaderShadow: string;

    public static createID(table: NISTable, IDs?: string[]): string {
        let res = '';
        const rowIDs = table.getAllRowsIDs();
        const colIDs = table.getNISTableColIDs();
        do {
            res = '';
            res += Math.round((Math.random() * 100000000))
                        .toString();
        } while (rowIDs.includes(res) || colIDs.includes(res));

        return res;
    }

    constructor(parent: DocumentContentBase, logicDocument: Document, rows?: number, cols?: number,
                tableGrid?: number[], tableHeaderNum?: number, tableName?: string) {
        super(parent, logicDocument, rows, cols, tableGrid, tableHeaderNum, tableName);

        this.tableRecal = new TableRecalculate2(this);
        // this.tableHeaderShadow = '#C3C3C3';
    }

    /**
     * 是否是护理表格
     * @returns boolean
     */
    public isNISTable(): boolean {
        return true;
    }

    public setPropertys(props: ITableProperty): boolean {
        const properties = this.property;
        this.setCanAddRow(props.bCanAddRow);
        this.setCanDeleteRow(props.bCanDeleteRow);
        this.setDeleteProtect(props.bDeleteProtect);
        this.setFixedColWidth(props.bFixedColWidth);
        // this.setFixedRowHeight(props.bFixedRowHeight);
        properties['bFixedRowHeight'] = props.bFixedRowHeight;
        this.setTableReadOnly(props.bReadOnly);
        this.setHeaderReadOnly(props.bHeaderReadOnly);
        properties.addCustomProps(props.customProperty);
        this.setTableName(props.tableName);
        if (props['borders']) {
            properties.tableBorders = props['borders'];
        }
        // this.setTableBorders(props.tableBorders);

        return true;
    }

    public setTableProps( props: ITableProperty ): boolean {
        let bApplyToInnerTable = false;
        if ( false === this.selection.bUse ) {
            bApplyToInnerTable = this.curCell.content.setTableProps(props);
        }

        if ( true === bApplyToInnerTable ) {
            return true;
        }

        this.logicDocument.startAction(HistoryDescriptionType.DocumentSetTableProperty);

        if ( null != props.tableName && this.tableName !== props.tableName ) {
            this.setTableName(props.tableName);
        }

        if (props.fixedLeft != null) {
            this.property.setFixedLeft(props.fixedLeft);
        }

        if (props.fixedRight != null) {
            this.property.setFixedRight(props.fixedRight);
        }

        if (props.bFixedHeader != null) {
            this.property.setFixedHeader(props.bFixedHeader);
        }

        let bNeedChange = false;
        if ( null != props.columnWidth ) {
            bNeedChange = true;
            this.setColumnWidth(props.columnWidth);
        }

        if ( null != props.columnWidths ) {
            // if ( true === this.isFixedColWidth() ) {
                // this.setColumnWidth(props.columnWidth);
                bNeedChange = true;
                this.setColumnsWidth(props.columnWidths);
            // }
        }

        // const res = this.setCellGridLines(props.cellGirdLines);
        // if (res === ResultType.Success) {
        //     bNeedChange = true;
        // }

        if (props.bDeleteProtect != null && this.isDeleteProtect() !== props.bDeleteProtect ) {
            this.setDeleteProtect(props.bDeleteProtect);
        }

        if (props.bCanDeleteRow != null && this.canDeleteRow() !== props.bCanDeleteRow ) {
            this.setCanDeleteRow(props.bCanDeleteRow);
        }

        if (props.bCanAddRow != null && this.canAddRow() !== props.bCanAddRow ) {
            this.setCanAddRow(props.bCanAddRow);
        }

        if (props.bFixedRowHeight != null && this.isFixedRowHeight() !== props.bFixedRowHeight ) {
            this.setFixedRowHeight(props.bFixedRowHeight);
        }

        if (props.bFixedColWidth != null && this.isFixedColWidth() !== props.bFixedColWidth ) {
            this.setFixedColWidth(props.bFixedColWidth);
        }

        if (props.repeatHeaderNum != null && this.getTableRowsHeaderCount() !== props.repeatHeaderNum ) {
            bNeedChange = this.setTableRowsHeaderCount(props.repeatHeaderNum) || bNeedChange;
        }

        if (props.bRepeatHeader != null && this.isRepeatHeader() !== props.bRepeatHeader ) {
            bNeedChange = (this.setRepeatHeader(props.bRepeatHeader) && 2 <= this.pages.length) || bNeedChange;
        }

        if ( null != props.customProperty ) {
            this.property.setCustomProps(props.customProperty);
        }

        if ( null != props.bHeaderReadOnly ) {
            this.property.setHeaderReadOnly(props.bHeaderReadOnly);
        }

        if ( bNeedChange ) {
            this.parent.recalculate();
            this.parent.updateCursorXY();
            this.parent.setDirty(true);
        }

        this.logicDocument.endAction();
        return true;
    }

    public getTableProps(): ITableProperty {
        const props: ITableProperty = {
            tableName: this.tableName,
            tableCellWidth: 0,
            columnWidths: undefined,
            bRepeatHeader: this.isRepeatHeader(),
            repeatHeaderNum: 0,
            rows: this.content.length,
            cols: this.tableGrid.length,
            bFixedRowHeight: this.isFixedRowHeight(),
            bFixedColWidth: this.isFixedColWidth(),
            bCanAddRow: this.canAddRow(),
            bCanDeleteRow: this.canDeleteRow(),
            bReadOnly: this.isReadOnlyProtect(),
            bHeaderReadOnly: this.isHeaderReadOnly(),
            bDeleteProtect: this.isDeleteProtect(),
            customProperty: this.getCustomProps(),
            fixedRight: this.getFixedRight(),
            fixedLeft: this.getFixedLeft(),
            bFixedHeader: this.isFixedHeader(),
            columnIDs: undefined,
        };

        props.columnWidths = this.getAllColumnsWidth(); // getMMFromPx(this.getColumnWidth()) / 10;
        props.repeatHeaderNum = this.getTableRowsHeaderCount();
        props.columnIDs = this.getNISTableColIDs();

        if ( true === this.bApplyToAll ) {
            const curPos = { rowIndex: [0, this.content.length - 1], cellIndex: [0, this.tableGrid.length - 1]};
            props.curPos = curPos;
        } else if ( true === this.selection.bUse && TableSelectionType.TableCell === this.selection.type
            && 0 < this.selection.data.length ) {
            const startPos = this.selection.startPos.pos;
            const endPos = this.selection.endPos.pos;
            const startRowIndex = startPos.rowIndex > endPos.rowIndex ? endPos.rowIndex : startPos.rowIndex;
            const endRowIndex = startPos.rowIndex > endPos.rowIndex ? startPos.rowIndex : endPos.rowIndex;
            const startColIndex = startPos.cellIndex > endPos.cellIndex ? endPos.cellIndex : startPos.cellIndex;
            const endColIndex = startPos.cellIndex > endPos.cellIndex ? startPos.cellIndex : endPos.cellIndex;
            const curPos = { rowIndex: [startRowIndex, endRowIndex], cellIndex: [startColIndex, endColIndex]};
            props.curPos = curPos;
        } else {
            if (this.curCell != null) {
                const curPos = { rowIndex: [this.curCell.row.index], cellIndex: [this.curCell.index] };
                props.curPos = curPos;
            }
        }

        return props;
    }

    /**
     * 保存使用的获取属性方法
     * @returns 
     */
    public getTablePropsToSave(): ITableProperty {
        const props: ITableProperty = {
            tableName: this.tableName,
            tableCellWidth: 0,
            columnWidths: undefined,
            bRepeatHeader: this.isRepeatHeader(),
            repeatHeaderNum: 0,
            rows: this.content.length,
            cols: this.tableGrid.length,
            bFixedRowHeight: this.isFixedRowHeight(),
            bFixedColWidth: this.isFixedColWidth(),
            bCanAddRow: this.canAddRow(),
            bCanDeleteRow: this.property.isTableCanDeleteRow(),
            bReadOnly: this.isReadOnlyProtect(),
            bHeaderReadOnly: this.isHeaderReadOnly(),
            bDeleteProtect: this.isDeleteProtect(),
            customProperty: this.getCustomProps(),
            fixedRight: this.getFixedRight(),
            fixedLeft: this.getFixedLeft(),
            bFixedHeader: this.isFixedHeader(),
            columnIDs: undefined,
        };

        // props.columnWidths = this.getAllColumnsWidth(); // getMMFromPx(this.getColumnWidth()) / 10;
        props.repeatHeaderNum = this.getTableRowsHeaderCount();
        props.columnIDs = this.getNISTableColIDs();

        // if ( true === this.bApplyToAll ) {
        //     const curPos = { rowIndex: [0, this.content.length - 1], cellIndex: [0, this.tableGrid.length - 1]};
        //     props.curPos = curPos;
        // } else if ( true === this.selection.bUse && TableSelectionType.TableCell === this.selection.type
        //     && 0 < this.selection.data.length ) {
        //     const startPos = this.selection.startPos.pos;
        //     const endPos = this.selection.endPos.pos;
        //     const startRowIndex = startPos.rowIndex > endPos.rowIndex ? endPos.rowIndex : startPos.rowIndex;
        //     const endRowIndex = startPos.rowIndex > endPos.rowIndex ? startPos.rowIndex : endPos.rowIndex;
        //     const startColIndex = startPos.cellIndex > endPos.cellIndex ? endPos.cellIndex : startPos.cellIndex;
        //     const endColIndex = startPos.cellIndex > endPos.cellIndex ? startPos.cellIndex : endPos.cellIndex;
        //     const curPos = { rowIndex: [startRowIndex, endRowIndex], cellIndex: [startColIndex, endColIndex]};
        //     props.curPos = curPos;
        // } else {
        //     if (this.curCell != null) {
        //         const curPos = { rowIndex: [this.curCell.row.index], cellIndex: [this.curCell.index] };
        //         props.curPos = curPos;
        //     }
        // }

        return props;
    }

    public getTableCellProps(): ITableCellProperty {
        if ( this.isSelectionUse() && TableSelectionType.TableCell === this.selection.type ) {
            return null;
        }

        if ( this.curCell ) {
            return {
                cellFormula: this.curCell.getCellFormula(),
                bProtected: this.curCell.isCellProtected(),
                nisProperty: this.curCell.getNISProperty(),
            };
        }

        return null;
    }

    public getTableCellText(): string {
        const tableCell = this.curCell;
        let text = '';
        if (tableCell != null) {
            // const rootElems = tableCell.getContent();
            // for (const rootElem of rootElems) {
            //     if (rootElem instanceof Paragraph) {
            //         if (rootElem != null && rootElem.content != null) {
            //             for (const portion of rootElem.content) {
            //                 // portion.setReviewTypeWithInfo(ReviewType.Add, reviewInfo);
            //                 text += portion.getText();
            //             }
            //         }
            //     }
            // }
            text = tableCell.content.getContentText();
        }

        return text;
    }

    public getCurrentTable(): any {
        return this;
    }

    public getNISCellBounds(): IDrawSelectionsCell[] {
        const tableCell = this.curCell;
        let result: IDrawSelectionsCell[] = [];
        if (tableCell != null) {
            result = tableCell.getSelectionBounds();
        }

        return result;
    }

    public getNISCellGridLines(): {[key: number]: INISCellGridLine} {
        return;
        // const lines = this.property.cellGirdLines;
        // if (!lines) {
        //     return;
        // }

        // const keys = Object.keys(lines);
        // const res: {[key: number]: INISCellGridLine} = {};
        // keys.forEach((key) => {
        //     const line: INISCellGridLine = lines[key];
        //     // if (!line.height) {
        //     //     return;
        //     // }

        //     res[key] = {...line};
        // });

        // return res;
    }

    public setCellGridLines(lines: {[key: number]: INISCellGridLine}): number {
        return ResultType.UnEdited;
        // if (!lines) {
        //     return ResultType.UnEdited;
        // }

        // let curLines = this.property.cellGirdLines;
        // if (curLines == null) {
        //     curLines = {};
        // }
        // const keys = Object.keys(lines);
        // let bChange = false;
        // keys.forEach((key) => {
        //     const newGirdLine: INISCellGridLine = lines[key];
        //     if (newGirdLine != null) {
        //         const girdLine: INISCellGridLine = curLines[key];
        //         if (!girdLine) {
        //             curLines[key] = newGirdLine;
        //             bChange = true;
        //             return;
        //         }
                
        //         if (newGirdLine.visible != null && newGirdLine.visible !== girdLine.visible) {
        //             girdLine.visible = newGirdLine.visible;
        //             bChange = true;
        //         }
                
        //         if (newGirdLine.height != null && newGirdLine.height !== girdLine.height) {
        //             girdLine.height = newGirdLine.height;
        //             bChange = true;
        //         }

        //         if (newGirdLine.alignment != null && newGirdLine.alignment !== girdLine.alignment) {
        //             girdLine.alignment = newGirdLine.alignment;
        //             bChange = true;
        //         }

        //         if (newGirdLine.bprint != null && newGirdLine.bprint !== girdLine.bprint) {
        //             girdLine.bprint = newGirdLine.bprint;
        //             bChange = true;
        //         }
        //     }
        // });

        // if (bChange) {
        //     this.property.cellGirdLines = curLines;
        //     return ResultType.Success;
        // }

        // return ResultType.UnEdited;
    }

    public isExistSignCellInCurRow(): boolean {
        const row = this.curCell ? this.curCell.getRow() : null;
        if ( row && !row.isReadOnly() ) {
            return row.isExistSignCell();
        }

        return false;
    }

    public getSignCellsInCurRow(row?: TableRow): TableCell[] {
        row = row ? row : (this.curCell ? this.curCell.getRow() : null);
        if ( row ) {
            return row.getSignCellsInCurRow();
        }

        return null;
    }

    public setTableCellProps(props: ITableCellProperty): number {
        if ( this.isSelectionUse() && TableSelectionType.TableCell === this.selection.type ) {
            return ResultType.Failure;
        }

        if ( this.curCell && props) {
            return this.curCell.setCellProps(props);
        }

        return ResultType.Failure;
    }

    public setDiagonalLineCell(): boolean {
        if ( this.isSelectionUse() ) {
            return false;
        }

        const cell = this.curCell;
        if ( cell && !cell.row.isReadOnly() ) {
            return cell.setDiagonalLine();
        }

        return false;
    }

    public setHeaderReadOnly(bReadOnly: boolean): void {
        this.property.setHeaderReadOnly(bReadOnly);
    }

    public isHeaderReadOnly(): boolean {
        return this.property.isHeaderReadOnly();
    }

    public setTableRowReadOnly(): void {
        if ( this.isSelectionUse() ) {
            return ;
        }

        if ( this.curCell && !this.checkRowHasCellMerge() ) {
            this.curCell.row.setReadOnly(!this.curCell.row.isReadOnly());
        }
    }

    public setTableCellTypeProps(props: any): number {
        const selection = this.selection;
        if ( selection.bUse && selection.data ) {
            return ResultType.Failure;
        }

        if ( this.curCell && props ) {
            let bRecalculate = false;
            const row = this.curCell.row;
            const bHeader = row.isTableHeader();
            const ranges = props.range;
            const type = bHeader ? NISTableCellType.Text : props.type;
            const vertAlign = props.vertAlign;

            switch (ranges) {
                case TableCellRange.Current: {
                    // this.curCell.setCellType(type);
                    bRecalculate = this.curCell.setNISDefaultProps(type);
                    bRecalculate = this.curCell.setVertAlign(vertAlign) || bRecalculate;
                    break;
                }

                case TableCellRange.CurRow: {
                    row.content.forEach((cell) => {
                        // cell.setCellType(type);
                        bRecalculate = cell.setNISDefaultProps(type) || bRecalculate;
                        bRecalculate = cell.setVertAlign(vertAlign) || bRecalculate;
                    });
                    break;
                }

                case TableCellRange.CurCol: {
                    for (const curRow of this.content) {
                        const cell = curRow.getCell(this.curCell.index);
                        if ( cell && !curRow.isTableHeader() ) {
                            // cell.setCellType(type);
                            bRecalculate = cell.setNISDefaultProps(type) || bRecalculate;
                            bRecalculate = cell.setVertAlign(vertAlign) || bRecalculate;
                        }
                    }
                    break;
                }

                default:
                    return ResultType.Failure;
            }

            if ( bRecalculate ) {
                this.parent.recalculate();
                this.parent.updateCursorXY();
            }

            return ResultType.Success;
        }

        return ResultType.Failure;
    }

    public setTableCellContentProps(props: ITableCellContentProps): number {
        const selection = this.selection;
        if ( selection.bUse && selection.data ) {
            return ResultType.Failure;
        }

        if ( this.curCell && props ) {
            let bRecalculate = false;
            const ranges = props.range;

            switch (ranges) {
                case TableCellRange.Current: {
                    bRecalculate = this.curCell.setCellContentProps(props) || bRecalculate;
                    break;
                }

                case TableCellRange.CurRow: {
                    const row = this.curCell.row;
                    if ( row ) {
                        row.content.forEach((cell) => {
                            bRecalculate = cell.setCellContentProps(props) || bRecalculate;
                        });
                    }
                    break;
                }

                case TableCellRange.CurCol: {
                    for (const curRow of this.content) {
                        const cell = curRow.getCell(this.curCell.index);
                        if ( cell ) {
                            bRecalculate = cell.setCellContentProps(props) || bRecalculate;
                        }
                    }
                    break;
                }

                default:
                    return ResultType.Failure;
            }

            if ( bRecalculate ) {
                this.parent.recalculate();
                this.parent.updateCursorXY();
                return ResultType.Success;
            }

            return ResultType.UnEdited;
        }

        return ResultType.Failure;
    }

    public getTableCellContentProps(): ITableCellContentProps {
        const selection = this.selection;
        if ( selection.bUse && selection.data ) {
            return null;
        }

        if ( this.curCell ) {
            return this.curCell.getCellContentProps();
        }

        return null;
    }

    public canInput(): boolean {
        if ( !super.canInput() ) {
            return false;
        }

        if ( (this.isHeaderReadOnly() && this.curCell && this.curCell.row.isTableHeader())
            || (this.curCell && this.curCell.row.isReadOnly()) ) {
            return false;
        }

        // NIS dropdown/datebox cannot input
        if (this.curCell && this.curCell.property) {
            const nisProperty = this.curCell.property.nisProperty;
            if (nisProperty != null && (nisProperty.selectType === NISSelectType.Dropdown
                || nisProperty.type === NISTableCellType.Date
            )) {
                return false;
            }
        }

        return true;
    }

    public canDelete(): boolean {
        if ( !super.canDelete() ) {
            return false;
        }

        if ( (this.isHeaderReadOnly() && this.curCell && this.curCell.row.isTableHeader())
            || (this.curCell && (this.curCell.row.isReadOnly() || this.curCell.canDeleteContent() === false)) ) {
            return false;
        }

        return true;
    }

    public getCurrentCell(): TableCell {
        if ( this.isSelectionUse() && TableSelectionType.TableCell === this.selection.type ) {
            return null;
        }

        return this.curCell;
    }

    public getTableCellByXY(pointX: number, pointY: number, pageIndex: number): TableCell {
        if (typeof pointX !== 'number' || typeof pointY !== 'number' || typeof pageIndex !== 'number') {
            return null;
        }
        let cell = null;
        const cellPos = super.getCellByXY(pointX, pointY, pageIndex);

        if ( cellPos ) {
            cell = this.content[cellPos.rowIndex].getCell(cellPos.cellIndex);
        }

        return cell;
    }

    public getTableHeaderShadow(): string {
        return NISTableCellColor.TableHeaderBackground;
    }

    public setNISCellListItems(pos: any[]): number {
        let result = ResultType.Failure;
        const tableCell = this.curCell;
        if (tableCell != null && !tableCell.isCellProtected()) {
            result = tableCell.setNISCellListItems(pos);
        }
        return result;
    }

    public setNISTableColID(colInfo: string[]): boolean {
        this.property.setNISTableColIDs(colInfo);

        return true;
    }

    public getNISTableColNumber(): number {
        for (let index = 0, length = this.content.length; index < length; index++) {
            const row = this.content[index];
            if ( row && !row.isTableHeader() ) {
                return row.getCellsCount();
            }
        }

        return 0;
    }

    public getNISTableColIDs(): string[] {
        const colIDs = this.property.getNISTableColIDs();
        if (this.getNISTableColNumber() !== colIDs.length) {
            for (let index = 0, length = this.content.length; index < length; index++) {
                const row = this.content[index];
                if ( row && !row.isTableHeader() ) {
                    const cellsCount = row.getCellsCount();
                    colIDs.splice(length, colIDs.length - cellsCount);
                    break;
                }
            }
        }

        return colIDs;
    }

    public filterColID(serialNumberJson: string[]): string {
        const ids: string[] = [];
        if ( serialNumberJson && serialNumberJson.length) {
            const colIDs = this.getNISTableColIDs();
            const row = this.getNISFirstRow();

            if (row && this.isHiddenRow(row)) {
                const cellsCount = row.getCellsCount();

                serialNumberJson.forEach((value) => {
                    // for (let index = this.content.length; index >= 0; index--) {
                    //     const row = this.content[index];
                    //     if (row && !row.isTableHeader() && !this.isHiddenRow(row)) {
                    //         for (let cellIndex = 0, length = row.getCellsCount(); cellIndex < length; cellIndex++) {
                    //             const cell = row.getCell(cellIndex);
                    //             if (cell && value === cell.getSerialNumber() && !ids.includes(colIDs[cellIndex])) {
                    //                 ids.push(colIDs[cellIndex]);
                    //             }
                    //         }
                    //     }
                    // }
                    let colID = '';
                    for (let index = 0; index < cellsCount; index++) {
                        const cell = row.getCell(index);
                        if (cell && value === cell.getSerialNumber()) {
                            colID = colIDs[index];
                        }
                    }

                    ids.push(colID);
                });
            }
        }

        return JSON.stringify(ids);
    }

    public isNewNISTable(): number {
        let count = 0;

        for (let index = 0, length = this.content.length; index < length; index++) {
            const row = this.content[index];
            if ( row && !row.isTableHeader() ) {
                count++;

                if ( row.getNISCreator() ) {
                    return 0;
                }
            }

            if ( 1 < count ) {
                return 0;
            }
        }

        return (1 === count ? 1 : 0);
    }

    public hideNISFirstRow(bHide: boolean): number {
        let row: TableRow;
        let result = ResultType.Failure;
        for (let index = 0, length = this.content.length; index < length; index++) {
            const curRow = this.content[index];
            if ( curRow && !curRow.isTableHeader() ) {
                row = curRow;
                break;
            }
        }

        if ( row ) {
            const height = row.getCurrentRowHeight();
            const margins = this.property.tableCellMargin;
            const diff = margins.top.width + margins.bottom.width;
            if ( (bHide && 0 >= height - diff) || (!bHide && 0 <= height - diff) ) {
                if ( 0 >= height - diff ) {
                    return ResultType.UnEdited;
                }
            } else {
                const newHeight = (bHide ? -diff : getPxForMM(10));
                const cellBorders = {
                    bottom: {
                        size: (bHide ? 0 : 1),
                        color: new DocumentColor(0, 0, 0),
                        value: 1,
                    },
                    type: 1,
                };

                row.setHeight(newHeight, TableRowLineRule.Exact);
                const nisProperty = row.getNISRowProperty();
                nisProperty.type = (bHide ? NISRowType.TemplateRow : NISRowType.Normal);

                this.setCellBorders(cellBorders, false, row);
                result = ResultType.Success;
            }
        }

        if (ResultType.Success === result) {
            this.parent.recalculate();
            this.parent.updateCursorXY();
        }

        return result;
    }

    public getNISRowIDbyCurrentCursor(): string {
        if ( this.selection.bUse && TableSelectionType.TableCell === this.selection.type ) {
            const rowIDs: string[] = [];
            let startPos = this.selection.startPos.pos.rowIndex;
            let endPos = this.selection.endPos.pos.rowIndex;

            if ( startPos > endPos ) {
                const temp = startPos;
                startPos = endPos;
                endPos = temp;
            }

            for (let index = startPos; index <= endPos; index++) {
                const row = this.content[index];
                if ( row && !row.isTableHeader() ) {
                    const rowID = row.getNISRowID();
                    if ( rowID ) {
                        rowIDs.push(rowID);
                    } else {
                        return ResultType.StringEmpty;
                    }
                }
            }

            return rowIDs.toString();
        } else {
            if ( this.curCell && !this.curCell.row.isTableHeader() ) {
                return this.curCell.row.getNISRowID();
            }
        }

        return ResultType.StringEmpty;
    }

    public getNISAllRowsID(): string {
        // let result = ResultType.StringEmpty;
        const rowIDs = this.getAllRowsIDs();

        // for (let index = 0, length = this.content.length; index < length; index++) {
        //     const row = this.content[index];
        //     if ( row && !row.isTableHeader() ) {
        //         const rowID = row.getNISRowID();
        //         if ( rowID ) {
        //             // result = (ResultType.StringEmpty === result ? result : result + ',');
        //             // result += (rowID);
        //             rowIDs.push(rowID);
        //         } else {
        //             return ResultType.StringEmpty;
        //         }
        //     }
        // }

        return rowIDs.toString();
    }

    public getNISLastRowID(): string {
        const row = this.content[this.content.length - 1];
        if ( row && !row.isTableHeader() ) {
            return row.getNISRowID();
        }

        return ResultType.StringEmpty;
    }

    public getNISFirstRowID(): string {
        // let result = ResultType.StringEmpty;

        // for (let index = 0, length = this.content.length; index < length; index++) {
        //     const row = this.content[index];
        //     if ( row && !row.isTableHeader() ) {
        //         result = row.getNISRowID();
        //         break;
        //     }
        // }
        const row = this.getNISFirstRow();

        return (row ? row.getNISRowID() : ResultType.StringEmpty);
    }

    public moveRowToPosition(movedRow: any, PositionRow: string, direct: number): number {
        const sourceRow = this.getRowByRowID(movedRow.rowID);
        const descRow = this.getRowByRowID(PositionRow);
        const moveSteps = movedRow.number;

        if (sourceRow && descRow && sourceRow !== descRow
            && this.isNISContentRow(sourceRow) && this.isNISContentRow(descRow)
        ) {
            if (moveSteps && ((sourceRow.index + moveSteps > this.getRowsCount()) ||
                (sourceRow.index + moveSteps - 1 >= descRow.index && sourceRow.index < descRow.index))) {
                return ResultType.ParamError;
            }

            let result = ResultType.UnEdited;

            if (!moveSteps || 1 === moveSteps) {
                result = this.moveRowToPositionImp(sourceRow, descRow, direct);
            } else {
                const sourceIndex = sourceRow.index;
                const rows = this.content.splice(sourceIndex, moveSteps);
                this.reIndexContent(0);

                const insertPos = (1 === direct ? descRow.index : descRow.index + 1);
                this.content.splice(insertPos, 0, ...rows);

                result = ResultType.Success;
            }

            if (ResultType.Success === result) {
                this.logicDocument.setDirty();
                this.parent.recalculate();
                this.parent.updateCursorXY();
            }

            return result;
        }

        return ResultType.Failure;
    }

    public sortRow(rowJson: any): number {
        const dateColID = rowJson ? rowJson.DateCol : '';
        const timeColID = rowJson ? rowJson.TimeCol : '';
        if (rowJson && dateColID && timeColID && 1 === rowJson.sortRule) {
            const colIDs = this.getNISTableColIDs();
            const dateColIndex = colIDs.findIndex((value) => {
                return dateColID === value;
            });
            const timeColIndex = colIDs.findIndex((value) => {
                return timeColID === value;
            });

            if (-1 === dateColIndex || -1 === timeColIndex) {
                return ResultType.ParamError;
            }

            const result = [];
            let res  = [];
            for (let index = 0, length = this.content.length; index < length; index++) {
                const row = this.content[index];
                if (row.isTableHeader() || this.isHiddenRow(row)) {
                    continue;
                }

                if (NISRowType.Normal !== row.getNISRowType()) {
                    if (res.length) {
                        result.push(res);
                        res = [];
                    }
                    continue;
                }

                const dateCell = row ? row.getCell(dateColIndex) : null;
                const timeCell = row ? row.getCell(timeColIndex) : null;

                if (!row || !dateCell || !timeCell) {
                    res = [];
                    result.length = 0;

                    return ResultType.Failure;
                }

                const option = {
                    row,
                    date: dateCell.getDateTime2(),
                    time: timeCell.getTimeText(),
                };
                res.push(option);
            }

            if (res.length) {
                result.push(res);
                res = [];
            }

            if (result.length) {
                result.forEach((items) => {
                    this.sortRows(items);
                });
            }

            res = [];
            result.length = 0;
            this.logicDocument.setDirty();
            this.parent.recalculate();
            this.parent.updateCursorXY();
            return ResultType.Success;
        }

        return ResultType.Failure;
    }

    public sortRowInRange(beginRow: string, rowJson: string[]): number {
        const startRow = this.getRowByRowID(beginRow);

        if (startRow && !startRow.isTableHeader() && !this.isHiddenRow(startRow) && rowJson && rowJson.length) {
            const length = rowJson.length;
            const rows: TableRow[] = [];
            for (let index = 0; index < length; index++) {
                const row = this.getRowByRowID(rowJson[index]);
                if (row && this.isNISContentRow(row) && !rows.includes(row)) {
                    rows.push(row);
                } else {
                    return ResultType.ParamError;
                }
            }

            if (rows.includes(startRow)) {
                return ResultType.ParamError;
            }

            for (let index = rows.length - 1; index >= 0; index--) {
                const row = rows[index];
                this.moveRowToPositionImp(row, startRow, 2);
                this.reIndexContent(0);
            }

            this.logicDocument.setDirty();
            this.parent.recalculate();
            this.parent.updateCursorXY();
            return ResultType.Success;
        }

        return ResultType.Failure;
    }

    public setNISRowProp(rowID: string, prop: INISRowProperty): number {
        for (let index = 0, length = this.content.length; index < length; index++) {
            const row = this.content[index];
            if ( row && !row.isTableHeader() && row.getNISRowID() === rowID ) {
                return (row.setNISRowProperty(prop) ? ResultType.Success : ResultType.Failure);
            }
        }

        return ResultType.Failure;
    }

    public setNISRowText(rowID: string, sJson: any): number {
        const row = this.getRowByRowID(rowID);
        if ( row && sJson.length ) {
            let result = false;
            for (const item of sJson) {
                if ( item ) {
                    const colID = item.colID;
                    const content = item.text;

                    const cell = row.getCellByColID(colID);
                    result = this.setCellText(cell, content) || result;
                }
            }

            if (result) {
                this.parent.recalculate();
                this.parent.updateCursorXY();
            }
            return (result ? ResultType.Success : ResultType.Failure);
        }

        return ResultType.Failure;
    }

    public setRowInfo(sInfo: any): number {
        let row = this.getRowByRowID(sInfo.rowID);
        if ( row ) {
            if ( this.isHiddenRow(row) ) {
                return ResultType.Failure;
            }
        } else {
            // add new row
            const lastRowIndex = this.content.length - 1;
            const res = this.addTableRow(false,
                {insertIndex: lastRowIndex, count: 1,
                rowIndex: lastRowIndex, cellIndex: 0, bNotSelect: true});
            if ( res ) {
                row = this.content[lastRowIndex + 1];
            }
        }
        if (row) {
            // update row
            row.setNISRowID(sInfo.rowID);
            if (typeof sInfo.singedStatus === 'number') {
                row.setSignStatus(sInfo.singedStatus);
            }
            if (typeof sInfo.rowType === 'number') {
                row.setNISRowType(sInfo.rowType);
            }
            // update cells
            if (Array.isArray(sInfo.cellInfo)) {
                const cellIDMap = new Map(this.getNISTableColIDs()
                                              .map((colID, index) => [colID, index]));
                for (const cellInfo of sInfo.cellInfo) {
                    if (cellIDMap.has(cellInfo.colID)) {
                        const curCell = row.getCell(cellIDMap.get(cellInfo.colID));
                        if (typeof cellInfo.cellType === 'number') {
                            curCell.setCellType(cellInfo.cellType);
                        }
                        if (typeof cellInfo.text === 'string') {
                            // 设置文本
                            this.setCellTextByCell(curCell, cellInfo.text);
                        }
                        if (Array.isArray(cellInfo.customProperty)) {
                            const nisProperty = curCell.getNISProperty();
                            const customProperty = [];
                            for (const cp of cellInfo.customProperty) {
                                Object.keys(cp)
                                    .map((key: string) => {
                                        const value = cp[key];
                                        if (value !== null && value !== undefined) {
                                            customProperty.push({
                                                name: key,
                                                value,
                                                type: typeof value === 'number'
                                                    ? DataType.Number
                                                    : typeof value === 'boolean'
                                                    ? DataType.Boolean
                                                    : DataType.String
                                            });
                                        }
                                    });
                            }
                            nisProperty.customProperty = customProperty;
                            curCell.setNISCellProps(nisProperty);
                        }
                    }
                }
            }
            this.removeSelection();
            this.parent.recalculate();
            this.parent.updateCursorXY();
        }
        return ResultType.Success;
    }

    public getRowInfo(rowID: string, sParam: any): string {
        const contents = this.content;
        const colIDs = this.getNISTableColIDs();

        for (let index = 0, length = contents.length; index < length; index++) {
            const row = contents[index];
            if (row.getNISRowID() !== rowID) {
                continue;
            }
            if (row.isTableHeader() || this.isHiddenRow(row)) {
                break;
            }
            return JSON.stringify(this.getTableRowInfo(row, colIDs));
        }
        return ResultType.StringEmpty;
    }

    public getTableInfo(sJson: INISTableInfoData): any[] {
        const contents = this.content;
        const result = [];
        const colIDs = this.getNISTableColIDs();

        for (let index = 0, length = contents.length; index < length; index++) {
            const row = contents[index];
            if (row.isTableHeader() || this.isHiddenRow(row)) {
                continue;
            }
            const rowInfo = this.getTableRowInfo(row, colIDs);
            result.push(rowInfo);
        }
        return result;
    }

    public getTableInfo2(sJson: INISTableInfoData): any[] {
        const contents = this.content;
        const result = [];
        let res  = [];

        let firstRow: number = 0;
        for (let index = 0, length = contents.length; index < length; index++) {
            const row = contents[index];
            if (row.isTableHeader()) {
                continue;
            }

            if (firstRow === 0 && this.isHiddenRow(row)) {
                firstRow++;
                continue;
            }

            if (sJson.onlyNeedNoSum === 1 && row.getNISRowType() !== NISRowType.Normal) {
                if (res.length !== 0) {
                    result.push(res);
                    res = [];
                }
                continue;
            }

            const signStatus = row.getSignStatus();
            const options = {};
            let date: string;
            let time: string;
            const cells = row.content;
            for (let cellIndex = 0, len = cells.length; cellIndex < len; cellIndex++) {
                const cell = cells[cellIndex];
                if (cell.isDateCell()) {
                    if (date  === undefined && cell.isDateTimeCellByCustom(sJson.datePropName)) {
                        const cur = cell.getDateTime();
                        if (cur) {
                            date = cur.split(' ')[0];
                        } else {
                            date = '';
                        }
                    }
                } else if (cell.isTimeCell()) {
                    if (time === undefined && cell.isDateTimeCellByCustom(sJson.timePropName)) {
                        time = cell.getTimeText();
                    }
                }

                if (date !== undefined && time !== undefined) {
                    break;
                }
            }

            options[row.getNISRowID()] = {
                date,
                time,
                signStatus,
            };

            res.push(options);
        }
        result.push(res);

        return result;

    }

    public getHeaderRowTextByColID(colIDs: string[]): Map<string, string[]> {
        const map = new Map();
        const contents = this.content;
        const indexs = {};
        for (let index = contents.length - 1; index >= 0; index--) {
            const row = contents[index];
            if (row.isTableHeader() ) {
                return map;
            }

            const tableSumGrid = this.tableSumGrid;
            for (let girdIndex = 0, len = tableSumGrid.length; girdIndex < len; girdIndex++) {
                const cell = row.getCell(girdIndex);
                if (!cell) {
                    continue;
                }
                const id = cell.getNISColID();
                if (colIDs.includes(id)) {
                    // if (!map.has(id)) {
                    //     map.set(id, []);
                    // }
                    indexs[girdIndex] = id;
                }
            }

            break;
        }

        let options: any; // 上一行数据
        const arrs = []; // 行数据收集
        const texts = []; // 文本收集
        for (let index = 0, length = contents.length; index < length; index++) {
            const row = contents[index];
            if ( row && !row.isTableHeader() ) {
                break;
            }

            const cells = row.content;
            let curIndex = 0;
            // const option = options ? {...options} : null; // 当前行
            options = {}; // 重置上一行
            const curTexts: any = {};
            texts.push(curTexts);
            for (let cellIndex = 0, len = cells.length; cellIndex < len; cellIndex++) {
                const cell = cells[cellIndex];
                // const id = indexs[curIndex];
                const span = cell.getGridSpan();

                // const id = option ? option[curIndex] : indexs[curIndex];
                // for (let actIndex = 0; actIndex < span; actIndex++) {
                //     options[curIndex + actIndex] = id;
                // }
                const oldIndex = curIndex;
                curIndex += span;
                if (indexs[oldIndex] === undefined) {
                    continue;
                }

                if (cell.content.isEmpty()) {
                    curTexts[oldIndex] = '';
                    continue;
                }

                const text = cell.content.getSelectText(true);
                curTexts[oldIndex] = text;
                for (let actIndex = 0; actIndex < span; actIndex++) {
                    curTexts[oldIndex + actIndex] = text;
                }
                // if (!text) {
                //     continue;
                // }

                // map.get(id)
                // .push(text);
            }
            // arrs.push(options); // 新增上一行
        }

        const keys = Object.keys(indexs);
        const step = texts.length - 1;
        keys.forEach((key) => {
            const res = [];
            this.setHeaderCellText(step, +key, texts, res);
            map.set(key, res);
        });

        return map;
    }

    public setHeaderCellText(step: number, index: number, texts: any[], res: any[]): void {
        const actTexts = texts[step--];
        if (actTexts === undefined) {
            return;
        }
        if (actTexts[index]) {
            res.push(actTexts[index]);
        }

        if (step < 0) {
            return;
        }
        this.setHeaderCellText(step, index, texts, res);
    }

    public getNISRowProp(sJson: any): string {
        const result = {
            propValue: undefined,
        };

        if ( sJson ) {
            const row = this.getRowByRowID(sJson.rowID);
            if (row) {
                const props = row.getNISRowProperty();
                const prop = sJson.propName;

                if ( 'editProtect' === prop ) {
                    result.propValue = props.bReadOnly;
                } else if ( 'rowType' === prop ) {
                    result.propValue = props.type;
                } else if ( 'deleteProtect' === prop ) {
                    result.propValue = props.bDeleteProtect;
                } else {
                    result.propValue = (null != prop ? props[prop] : undefined);
                }
            }
        }

        return JSON.stringify(result);
    }

    public getNISRowCreator(rowID: string): string {
        const row = this.getRowByRowID(rowID);
        if ( row ) {
            return row.getNISCreator();
        }

        return ResultType.StringEmpty;
    }

    public setNISRowCreator(rowID: string, creator: string): number {
        const row = this.getRowByRowID(rowID);
        if ( row ) {
            return row.setNISCreator(creator);
        }

        return ResultType.Failure;
    }

    public getNISRowText(rowIDs: string[]): string {
        if (rowIDs && rowIDs.length) {
            const rowTextMap = Object.create(null);

            rowIDs.forEach((rowID) => {
                const row = this.getRowByRowID(rowID);
                const textArray = [];

                if ( row ) {
                    for (let index = 0, length = row.getCellsCount(); index < length; index++) {
                        const cell = row.getCell(index);
                        const text = cell.getCellContentText();
                        textArray.push('' !== text ? text : null);
                    }
                }

                rowTextMap[rowID] = textArray;
            });

            // const obj = Object.create(null);
            // for (const [k, v] of rowTextMap) {
            //     obj[k] = v;
            // }

            return JSON.stringify(rowTextMap);
        }

        return ResultType.StringEmpty;
    }

    public protectNISRows(rowIDs: string[], bFlag: boolean): boolean {
        let protectVal = true;
        if (bFlag != null) {
            protectVal = bFlag;
        }
        if ( rowIDs && rowIDs.length ) {
            let bChanged = false;
            rowIDs.forEach((rowID) => {
                const row = this.getRowByRowID(rowID);
                if ( row ) {
                    bChanged = row.setReadOnly(protectVal, true) || bChanged;
                }
            });
            if (bChanged) {
                this.logicDocument.recalculate();
                this.logicDocument.removeSelection();
                this.logicDocument.updateCursorXY();
            }
            return true;
        }

        return false;
    }

    public protectNISCurrentRow(bFlag: boolean): boolean {
        if ( this.selection.bUse && TableSelectionType.TableCell === this.selection.type ) {
            return false;
        }

        if ( this.curCell ) {
            const res = this.curCell.row.setReadOnly(bFlag, true);
            if (res === true) {
                this.logicDocument.recalculate();
                this.logicDocument.removeSelection();
                this.logicDocument.updateCursorXY();
            }
            return true;
        }

        return false;
    }

    public selectNISRow(rowID: string): boolean {
        const row = this.getRowByRowID(rowID);

        if ( row ) {
            this.parent.removeSelection();

            this.selection.bUse = true;
            this.selection.bStart = false;
            this.selection.startPos.pos = { rowIndex: row.index, cellIndex: 0 };
            this.selection.endPos.pos = { rowIndex: row.index, cellIndex: row.getCellsCount() - 1 };

            this.selection.type = TableSelectionType.TableCell;
            this.curCell = row.getCell(0);
            this.selection.curRow = row.index;

            this.updateSelectedCellsArray();

            this.parent.curPos.contentPos = this.index;
            this.parent.selection.bUse = true;
            this.parent.selection.bStart = false;
            this.parent.selection.startPos = this.index;
            this.parent.selection.endPos = this.index;
            this.parent.updateCursorXY();
            return true;
        }

        return false;
    }

    public getSelectedNISRowNumber(): number {
        if ( this.selection.bUse && TableSelectionType.TableCell === this.selection.type ) {
            const startPos = this.selection.startPos.pos;
            const endPos = this.selection.endPos.pos;

            return (Math.abs(endPos.rowIndex - startPos.rowIndex) + 1);
        } else if ( this.curCell ) {
            return 1;
        }

        return ResultType.Failure2;
    }

    public cancelSelecteNISRow(): boolean {
        if ( this.selection.bUse && TableSelectionType.TableCell === this.selection.type ) {
            // if ( !this.curCell ) {
            //     const startPos = this.selection.startPos.pos;
            //     const endPos = this.selection.endPos.pos;
            //     const rowIndex = Math.max(startPos.rowIndex, endPos.rowIndex);

            //     this.curCell = this.content[rowIndex].getCell(0);
            // } else {
            //     this.curCell.content.removeSelection();
            // }

            // // this.removeSelection();
            // this.selection.bStart = false;
            // this.selection.bUse = false;
            // this.selection.data = null;
            // this.selection.type = TableSelectionType.Common;

            // this.selection.startPos.pos = { rowIndex: 0, cellIndex: 0 };
            // this.selection.endPos.pos = { rowIndex: 0, cellIndex: 0 };
            // this.parent.updateCursorXY();
            this.removeSelection();

            return true;
        }

        return false;
    }

    public isCurrentNISRowCanDelete(): boolean {
        const rowsRange = this.getSelectedRowsRange();
        const startPos = rowsRange.start;
        const endPos = rowsRange.end;

        for (let index = startPos; index <= endPos; index++) {
            const row = this.content[index];
            if ( row.isDeleteProtect() ) {
                return false;
            }
        }

        return true;
    }

    public isCurrentNISRowSigned(): number {
        // const rowsRange = this.getSelectedRowsRange();
        // const startPos = rowsRange.start;
        // const endPos = rowsRange.end;

        // for (let index = startPos; index <= endPos; index++) {
        //     const row = this.content[index];
        //     if ( !row.isSignedRow() ) {
        //         return false;
        //     }
        // }
        if ( this.curCell && this.curCell.row ) {
            return (this.curCell.row.isSignedRow() ? 1 : 0);
        }

        return ResultType.Failure2;
    }

    public deleteNISCurrentRow(): boolean {
        const res = this.removeTableRow(null, true);
        if ( res ) {
            this.parent.recalculate();
            this.parent.updateCursorXY();
            return true;
        }

        return false;
    }

    public deleteNISRows(rowIDs: string[]): boolean {
        let result = false;

        if (rowIDs && rowIDs.length) {
            rowIDs.forEach((rowID) => {
                const row = this.getRowByRowID(rowID);

                if ( row ) {
                    const res = this.removeTableRow(row.index, true);
                    if ( res ) {
                        this.parent.recalculate();
                    }

                    result = res || result;
                }
            });

            if (result) {
                this.parent.updateCursorXY();
            }
        }

        return result;
    }

    public insertNISRowAtCurrentRow(sJson: any): boolean {
        let res = this.addTableRow(sJson.before);
        if ( res ) {
            const row = this.curCell.row;
            const contents = sJson.contents;
            let index = 0;
            for (const item of contents) {
                const colid = item.coldID;
                const bChanged = this.setNISColID(index++, colid);
                if (bChanged === false) {
                    continue;
                }
                const cell = row.getCellByColID(colid);
                res = this.setCellText(cell, item.text) || res;
            }

            if ( res ) {
                this.removeSelection();
                this.parent.recalculate();
                this.parent.updateCursorXY();
            }
        }

        return res;
    }

    public insertNISRows(sJson: any): string {
        const row = this.getRowByRowID(sJson.rowID);
        if ( row ) {
            const res = this.addTableRow(false,
                {insertIndex: row.index, count: sJson.number,
                rowIndex: row.index, cellIndex: 0, bNotSelect: true});
            if ( res ) {
                this.curCell = this.content[row.index + 1].getCell(0);
                this.parent.recalculate();
                this.parent.updateCursorXY();

                const rowIDs = [];
                for (let index = 1; index <= sJson.number; index++) {
                    const curRow = this.content[row.index + index];
                    if ( curRow ) {
                        rowIDs.push(curRow.getNISRowID());
                    }
                }

                return JSON.stringify(rowIDs);
            }
        }

        return ResultType.StringEmpty;
    }

    public setNISSignCellText(sJson: any): boolean {
        if ( null == sJson.colID ) {
            const row = this.getRowByRowID(sJson.rowID);
            if ( row ) {
                let res = false;
                // const creator = row.getNISCreator();
                for (let index = 0, length = row.getCellsCount(); index < length; index++) {
                    const cell = row.getCell(index);
                    if ( cell && cell.isSignCell() ) {
                        res = cell.content.setSignCellText(sJson.text, cell.getCellContentProps());
                        break;
                    }
                }

                if ( res ) {
                    this.parent.recalculate();
                    this.parent.updateCursorXY();
                }

                return res;
            }
        } else {
            const cell = this.getCellByRowIDColID(sJson.rowID, sJson.colID);
            if ( cell && cell.isSignCell() ) {
                const res = cell.content.setSignCellText(sJson.text, cell.getCellContentProps());
                if ( res ) {
                    this.parent.recalculate();
                    this.parent.updateCursorXY();

                    return true;
                }
            }

            return false;
        }

        return false;
    }

    public deleteNISSignCellsText(sJson: any): boolean {
        let res = false;
        for (const rowID of sJson) {
            const row = this.getRowByRowID(rowID);

            if ( rowID && row ) {
                res = row.deleteSignCellsText() || res;
            }
        }

        if ( res ) {
            this.parent.updateCursorXY();
        }

        return res;
    }

    public async signCurrentNISRow(sJson: any): Promise<boolean> {
        let res = false;
        if ( this.selection.bUse && TableSelectionType.TableCell === this.selection.type ) {
            return res;
        } else if ( this.curCell ) {
            res = await this.curCell.row.signCell(sJson);
            if ( res ) {
                this.parent.updateCursorXY();
            }
        }

        return res;
    }

    public async signNISRows(sJson: any): Promise<boolean> {
        let res = false;
        for (const item of sJson) {
            const row = this.getRowByRowID(item.rowID);

            if ( row ) {
                res = await row.signCell(item) || res;
            }
        }

        if ( res ) {
            this.parent.updateCursorXY();
        }

        return res;
    }

    public deleteCurrentNISRowSign(sJson: any): boolean {
        if ( this.curCell ) {
            const res = this.curCell.row.deleteSignContentByAuthor(sJson.name);
            if ( res ) {
                this.parent.recalculate();
                this.parent.updateCursorXY();
            }

            return res;
        }

        return false;
    }

    public deleteNISRowsSign(sJson: any): boolean {
        let result = false;
        const rowIDs = sJson.rowID;
        const names = sJson.name;

        if (rowIDs && rowIDs.length && names && names.length) {
            rowIDs.forEach((rowID, index) => {
                const row = this.getRowByRowID(rowID);

                if ( row ) {
                    result = row.deleteSignContentByAuthor(names[index]) || result;
                }
            });

            if ( result ) {
                this.parent.recalculate();
                this.parent.updateCursorXY();
            }
        }

        return result;
    }

    public getSignNumber(sJson: any): number {
        const row = this.getRowByRowID(sJson.rowID);
        const result = [];
        if ( row ) {
            const signCells = row.getSignCellsInCurRow();
            signCells.forEach((cell) => {
                if ( cell && !cell.content.isEmpty() ) {
                    const names = cell.getSignNames(true);
                    if ( names && 0 < names.length ) {
                        names.forEach((name) => {
                            result.push(name);
                        });
                    }
                }
            });
        }

        return result.length;
    }

    public getSignStatus(sJson: any): number {
        const row = this.getRowByRowID(sJson.rowID);
        if ( row ) {
            return row.getSignStatus();
        }

        return 0;
    }

    public setSignStatus(sJson: any): number {
        const row = this.getRowByRowID(sJson.rowID);
        if ( row ) {
            return row.setSignStatus(sJson.status);
        }

        return ResultType.Failure;
    }

    public filterNISSignRow(sJson: any): string[] {
        const signStatus = sJson.signStatus;
        const creator = sJson.useID;
        const rows = [];

        if (null == signStatus && null == creator) {
            return rows;
        }
        const firstRowId = this.getNISFirstRowID();
        for (let index = 0, length = this.content.length; index < length; index++) {
            const row = this.content[index];
            if (row.getNISRowID() === firstRowId && this.isHiddenRow(row)) {
                continue;
            }
            if ( row && !row.isTableHeader()
                && (null == signStatus || signStatus === row.getSignStatus())
                && (null == creator || creator === row.getNISCreator())
            ) {
                rows.push(row.getNISRowID());
            }
        }

        return rows;
    }

    public getSignNameByRow(rowID: string): string[] {
        const result = [];
        const row = this.getRowByRowID(rowID);

        if ( row ) {
            const signCells = row.getSignCellsInCurRow();
            signCells.forEach((cell) => {
                if ( cell && !cell.content.isEmpty() ) {
                    const names = cell.getSignNames();
                    if ( names && 0 < names.length ) {
                        names.forEach((name) => {
                            if (!result.includes(name)) {
                                result.push(name);
                            }
                        });
                    }
                }
            });
        }

        return result;
    }

    public insertSumRowAtCurrentRow(sJson: any): boolean {
        const res = this.addTableRow(sJson.before);
        if ( res ) {
            const row = this.curCell.row;
            let prefix = '';
            let sumStatus = 0;
            if (NISRowType.Sum === sJson.type) {
                prefix = 'sum';
                sumStatus = sJson.type;
            } else if (NISRowType.SumDetail === sJson.type) {
                prefix = 'sumD';
                sumStatus = sJson.type;
            }

            const rowID = prefix + row.getNISRowID();
            row.setNISRowID(rowID);

            const nisProperty = row.getNISRowProperty();
            nisProperty.sumKey = sJson.sumKey;
            nisProperty.type = sJson.type;
            nisProperty.sumStatus = sumStatus;
        }

        return res;
    }

    public insertSumRows(sJson: any): string {
        const row = this.getRowByRowID(sJson.rowID);
        if ( row ) {
            const res = this.addTableRow(false,
                {insertIndex: row.index, count: sJson.number,
                rowIndex: row.index, cellIndex: 0, bNotSelect: true});
            if ( res ) {
                const rowIDs = [];

                let prefix = '';
                const type = sJson.type;
                const sumKey = sJson.sumKey;
                let sumStatus = 0;
                if (NISRowType.Sum === type) {
                    prefix = 'sum';
                    sumStatus = type;
                } else if (NISRowType.SumDetail === type) {
                    prefix = 'sumD';
                    sumStatus = type;
                }

                for (let index = 1, length = sJson.number; index <= length; index++) {
                    const curRow = this.content[row.index + index];
                    if ( curRow ) {
                        const nisProperty = curRow.getNISRowProperty();
                        nisProperty.sumKey = sumKey;
                        nisProperty.type = type;
                        nisProperty.sumStatus = sumStatus;

                        const rowID = prefix + curRow.getNISRowID();
                        curRow.setNISRowID(rowID);
                        rowIDs.push(rowID);
                    }
                }

                return JSON.stringify(rowIDs);
            }
        }

        return ResultType.StringEmpty;
    }

    public deleteSumRows(sJson: any): boolean {
        let res = false;
        const sumKey = sJson.sumKey;

        for (let index = this.content.length - 1; index >= 0; index--) {
            const row = this.content[index];
            if ( row && (row.isSumRow() || row.isSumDetailRow()) && sumKey === row.getNISRowProperty().sumKey ) {
                res = this.removeTableRow(index, true) || res;
            }
        }

        if (res) {
            this.parent.recalculate();
            this.parent.updateCursorXY();
        }

        return res;
    }

    public setRowSumStatus(sJson: any): boolean {
        let res = false;
        const sumKey = sJson.sumKey;
        const rowIDs = sJson.rowID;

        if (rowIDs) {
            rowIDs.forEach((rowID) => {
                const row = this.getRowByRowID(rowID);
                if (row) {
                    const nisProperty = row.getNISRowProperty();
                    nisProperty.sumKey = sumKey;
                    nisProperty.sumStatus = NISRowType.SumNormal;
                    nisProperty.type = NISRowType.SumNormal;

                    res = true;
                }
            });
        }

        return res;
    }

    public getRowSumStatus(sJson: any): string {
        const result = [];
        if ( sJson && Array.isArray(sJson) ) {
            for (const rowID of sJson) {
                const row = this.getRowByRowID(rowID);
                if (row && !row.isTableHeader()) {
                    result.push(this.getCurrentRowSumStatus(row));
                } else {
                    result.push(ResultType.Failure2);
                }
            }
        }

        return JSON.stringify(result);
    }

    public getCurrentRowSumStatus(row?: TableRow): number {
        if (!row && this.curCell && this.curCell.row) {
            row = this.curCell.row;
        }

        if (row.isTableHeader()) {
            return ResultType.Failure2;
        }

        if (row.isSumRow() || row.isSumDetailRow()) {
            return row.getNISRowProperty().type;
        } else {
            return row.getSumStatus();
        }
    }

    public cleanRowSumStatus(sJson: any): boolean {
        let res = false;
        const sumKey = sJson.sumKey;

        for (let index = this.content.length - 1; index >= 0; index--) {
            const row = this.content[index];
            if ( row && sumKey === row.getNISRowProperty().sumKey ) {
                const nisProperty = row.getNISRowProperty();
                nisProperty.sumKey = '';
                nisProperty.sumStatus = NISRowType.Normal;
                nisProperty.type = NISRowType.Normal;

                res = true;
            }
        }

        return res;
    }

    public getLastSumRow(): string {
        for (let index = this.content.length - 1; index >= 0; index--) {
            const row = this.content[index];
            if ( row && row.isSumRow() && !row.isTableHeader() ) {
                const nisProperty = row.getNISRowProperty();

                return JSON.stringify({
                    rowID: row.getNISRowID(),
                    sumKey: nisProperty.sumKey,
                });
            }
        }

        return ResultType.StringEmpty;
    }

    public filterSumRow(sJson: any): string {
        const result = {
            rowID: []
        };
        const rowID = sJson.beginRowID;
        const status = sJson.status;
        const curRow = this.getRowByRowID(rowID);

        if (curRow) {
            for (let index = curRow.index; index >= 0; index--) {
                const row = this.content[index];
                if (row && row.getNISRowProperty() && this.isNISContentRow(row)) {
                    const nisProperty = row.getNISRowProperty();
                    switch (status) {
                        case NISRowType.Normal: // 未汇总
                            if ( !nisProperty.sumStatus ) {
                                result.rowID.push(row.getNISRowID());
                            }
                            break;

                        case NISRowType.SumNormal: // 已汇总
                            if ( 1 === nisProperty.sumStatus ) {
                                result.rowID.push(row.getNISRowID());
                            }
                            break;

                        case NISRowType.Sum: // 汇总行
                            if ( NISRowType.Sum === nisProperty.type ) {
                                result.rowID.push(row.getNISRowID());
                            }
                            break;

                        case NISRowType.SumDetail: // 3 汇总明细行
                            if ( NISRowType.SumDetail === nisProperty.type ) {
                                result.rowID.push(row.getNISRowID());
                            }
                            break;
                    }
                }
            }
        }

        return JSON.stringify(result);
    }

    public getNISRowsTextByJson(sJson: any): string {
        const result = [];
        const rowIDs = sJson.rowID;
        const colIDs = sJson.colID;

        if (rowIDs && colIDs && 0 < colIDs.length) {
            rowIDs.forEach((rowID) => {
                const row = this.getRowByRowID(rowID);
                if (row && row.getNISRowProperty()) {
                    const text = [];
                    const nisProperty = row.getNISRowProperty();

                    colIDs.forEach((colID) => {
                        const cell = row.getCellByColID(colID);
                        let content;
                        if ( cell ) {
                            content = cell.getCellContentText();
                        }

                        text.push(content);
                    });

                    result.push({
                        rowID: row.getNISRowID(),
                        text,
                        sign: nisProperty.signStatus,
                    });
                }
            });
        }

        return JSON.stringify(result);
    }

    public setNISCellType(rowID: string, colID: string, nType: number): boolean {
        let res = false;
        if ( rowID && colID ) {
            const cell = this.getCellByRowIDColID(rowID, colID);
            if (cell && nType !== cell.getCellType()) {
                res = cell.setNISDefaultProps(nType);
            }
        } else if ( rowID ) {
            const row = this.getRowByRowID(rowID);
            for (let index = row.getCellsCount() - 1; index >= 0; index--) {
                const cell = row.getCell(index);
                if (cell && nType !== cell.getCellType()) {
                    res = cell.setNISDefaultProps(nType) || res;
                }
            }
        } else if ( colID ) {
            const colIndex = this.getColIndexByColID(colID);
            for (let rowIndex = this.content.length - 1; rowIndex >= 0; rowIndex--) {
                const row = this.content[rowIndex];

                if (row && !row.isTableHeader()) {
                    const cell = row.getCell(colIndex);
                    if (cell && nType !== cell.getCellType()) {
                        res = cell.setNISDefaultProps(nType) || res;
                    }
                }

            }
        }

        if ( res ) {
            this.parent.recalculate();
            this.parent.updateCursorXY();
        }

        return res;
    }

    public getNISCellType(rowID: string, colID: string): number {
        const cell = this.getCellByRowIDColID(rowID, colID);

        return (cell ? cell.getCellType() : ResultType.Failure2);
    }

    public setNISCellLayout(rowID: string, colID: string, sJson: any): boolean {
        let res = false;
        const cell = this.getCellByRowIDColID(rowID, colID);
        if ( cell ) {
            const prop: ITableCellContentProps = {
                range: 0,
                fontFamily: sJson.font,
                fontSize: sJson.size,
                paraSpacing: sJson.space,
                alignType: sJson.margin,
            };
            res = cell.setCellContentProps(prop);
            if ( res ) {
                this.parent.recalculate();
                this.parent.updateCursorXY();
            }
        }

        return res;
    }

    public setNISCellText(sJson: any): boolean {
        if ( sJson ) {
            const rowID = sJson.rowID;
            const cellInfos = sJson.info;
            const row = this.getRowByRowID(rowID);

            if ( row && cellInfos && cellInfos.length ) {
                for (const item of cellInfos) {
                    if ( item ) {
                        const colID = item.colID;
                        const content = item.content;

                        const cell = this.getCellByRowIDColID(rowID, colID);
                        if ( cell && (cell.isTextCell() || cell.isQuickCell) && null != content ) {
                            cell.content.resetContentByText(content, cell.getCellContentProps());
                        }
                    }
                }

                this.parent.recalculate();
                this.parent.updateCursorXY();

                return true;
            }
        }

        return false;
    }

    public getNISCellText(sJson: any): string {
        if ( sJson ) {
            const row = this.getRowByRowID(sJson.rowID);
            const colIDs = sJson.info;
            if (row && this.isNISContentRow(row) && colIDs && Array.isArray(colIDs) && colIDs.length) {
                const texts = [];
                // for (const item of sJson) {
                //     if ( item ) {
                //         const rowID = item[0];
                //         const colID = item[1];

                //         const cell = this.getCellByRowIDColID(rowID, colID);
                //         if ( cell ) {
                //             texts.push(cell.getCellContentText());
                //         }
                //     }
                // }
                const tableColsID = this.getNISTableColIDs();
                for (let index = 0, count = colIDs.length; index < count; index++) {
                    const cellIndex = tableColsID.findIndex((id) => {
                        return id === colIDs[index];
                    });

                    if (-1 !== cellIndex) {
                        const cell = row.getCell(cellIndex);
                        if ( cell && !cell.isSignCell() ) {
                            texts.push(cell.getCellContentText());
                        } else {
                            texts.push('');
                        }
                    } else {
                        texts.push('');
                    }
                }

                return JSON.stringify(texts);
            }
        }

        return ResultType.StringEmpty;
    }

    public setNISCellProp(rowID: string, colID: string, sJson: IExternalNisProps): boolean {
        const cellProperty: ITableCellProperty = {
            bProtected: null,

            nisProperty: {
                // type: sJson.type,
                customProperty: null,
                serialNumber: null,
                // bDiagonalLine: sJson.bDiagonalLine,

                // fontFamily: sJson.fontFamily,
                // fontSize: sJson.fontSize,
                // paraSpacing: sJson.paraSpacing,
                // alignType: sJson.alignType,
            },
        };
        const nis = cellProperty.nisProperty;
        if ('serialNumber' in sJson) {
            if (typeof sJson.serialNumber === 'string') {
                nis.serialNumber = sJson.serialNumber;
            }
            delete sJson.serialNumber;
        }

        if ('editProtect' in sJson) {
            if (typeof sJson.editProtect === 'boolean') {
                cellProperty.bProtected = sJson.editProtect;
            }
            delete sJson.editProtect;
        }

        if ('minValue' in sJson) {
            if (typeof sJson.minValue === 'number') {
                nis.minValue = sJson.minValue;
            }
            delete sJson.minValue;
        }

        if ('maxValue' in sJson) {
            if (typeof sJson.maxValue === 'number') {
                nis.maxValue = sJson.maxValue;
            }
            delete sJson.maxValue;
        }

        if ('precision' in sJson) {
            if (typeof sJson.precision === 'number') {
                nis.precision = sJson.precision;
            }
            delete sJson.precision;
        }

        if ('unit' in sJson) {
            if (typeof sJson.unit === 'string') {
                nis.unit = sJson.unit;
            }
            delete sJson.unit;
        }

        if ('minWarn' in sJson) {
            if (typeof sJson.minWarn === 'number') {
                nis.minWarn = sJson.minWarn;
            }
            delete sJson.minWarn;
        }

        if ('maxWarn' in sJson) {
            if (typeof sJson.maxWarn === 'number') {
                nis.maxWarn = sJson.maxWarn;
            }
            delete sJson.maxWarn;
        }

        if ('gridLine' in sJson) {
            if (typeof sJson.gridLine === 'boolean') {
                nis.gridLine = {
                    visible: sJson.gridLine
                };
            }
            delete sJson.gridLine;
        }

        if ('bShowValue' in sJson) {
            if (typeof sJson.bShowValue === 'boolean') {
                nis.bShowValue = sJson.bShowValue;
            }
            delete sJson.bShowValue;
        }

        if ('bCheckMultiple' in sJson) {
            if (typeof sJson.bCheckMultiple === 'boolean') {
                nis.bCheckMultiple = sJson.bCheckMultiple;
            }
            delete sJson.bCheckMultiple;
        }

        if ('selectType' in sJson) {
            if ([NISSelectType.Combo, NISSelectType.Dropdown].includes(sJson.selectType)) {
                nis.selectType = sJson.selectType;
            }
            delete sJson.selectType;
        }
        
        if ('bRetrieve' in sJson) {
            if (typeof sJson.bRetrieve === 'boolean') {
                nis.bRetrieve = sJson.bRetrieve;
            }
            delete sJson.bRetrieve;
        }

        if ('dateBoxFormat' in sJson) {
            if ([NISDateBoxFormat.DateHyphen, NISDateBoxFormat.DateSlash, NISDateBoxFormat.FullDateHyphen, NISDateBoxFormat.FullDateSlash].includes(sJson.dateBoxFormat)) {
                nis.dateBoxFormat = sJson.dateBoxFormat;
            }
            delete sJson.dateBoxFormat;
        }

        if ('timeFormat' in sJson) {
            if ([CellTimeFormat.HM, CellTimeFormat.HM2].includes(sJson.timeFormat)) {
                nis.timeFormat = sJson.timeFormat;
            }
            delete sJson.timeFormat;
        }

        const props = [];
        for (const key in sJson ) {
            let value = sJson[key];
            const typeV = (null != value ? typeof value : undefined);
            let type;

            switch (typeV) {
                case 'number':
                    type = DataType.Number;
                    break;

                case 'boolean':
                    type = DataType.Boolean;
                    value = value.toString();
                    break;

                case 'string':
                default:
                    type = DataType.String;
                    break;
            }
            props.push({name: key, value, type});
        }

        if ( props.length ) {
            cellProperty.nisProperty.customProperty = props;
        }

        if ( rowID && colID ) {
            const cell = this.getCellByRowIDColID(rowID, colID);
            if ( cell ) {
                cell.setCellProps(cellProperty);

                return true;
            }
        } else if ( rowID ) {
            const row = this.getRowByRowID(rowID);
            for (let index = row.getCellsCount() - 1; index >= 0; index--) {
                const cell = row.getCell(index);
                if ( cell ) {
                    cell.setCellProps(cellProperty);
                }
            }

            return true;
        } else if ( colID ) {
            const colIndex = this.getColIndexByColID(colID);
            if ( -1 !== colIndex ) {
                this.content.forEach((row) => {
                    const cell = row.getCell(colIndex);

                    if ( cell ) {
                        cell.setCellProps(cellProperty);
                    }
                });

                return true;
            }
        }

        return false;
    }

    public setBPCellFormat(rowID: string, colID: string, format: any): number {
        const cell = this.getCellByRowIDColID(rowID, colID);
        if ( cell && cell.isBPCell() ) {
            return cell.setNisProperty({bloodPressureFormat: format});
        }

        return ResultType.Failure;
    }

    public setBPCellValue(rowID: string, colID: string, value: string): number {
        const cell = this.getCellByRowIDColID(rowID, colID);
        if ( cell && cell.isBPCell() ) {
            return cell.setNISCellBPText(value);
        }

        return ResultType.Failure;
    }

    public judgeTimeAndBpCell(sRowID: string): string {
        const row = this.getRowByRowID(sRowID);
        const cells = row?.content;

        const cellsInfo = [];
        for (const cell of cells) {
            if (cell.isTimeCell() || cell.isBPCell()) {
                if (cell.judgeTimeAndBpCell() === false) {
                    cellsInfo.push([sRowID, cell.getNISColID()]);
                }
            }
        }

        // console.log(cellsInfo)
        if (cellsInfo.length > 0) {
            return JSON.stringify(cellsInfo);
        } else {
            return '';
        }
    }

    public getNISCellProp(rowID: string, colID: string, sPropName: string): string {
        const cell = this.getCellByRowIDColID(rowID, colID);
        if ( cell ) {
            if (sPropName === 'serialNumber') {
                return cell.getNISProperty().serialNumber;
            }
            const customProperty = cell.getCustomProperty();
            if (customProperty && customProperty.length) {
                for (let index = 0, length = customProperty.length; index < length; index++) {
                    const pro = customProperty[index];
                    if ( pro && pro.name === sPropName) {
                        return pro.value;
                    }
                }
            }
        }

        return ResultType.StringEmpty;
    }

    public getNISCellsPropOfFirstRow(sPropName: string): string[] {
        const props = [];
        const row = this.getNISFirstRow();
        if ( row ) {
            for (let index = 0, length = row.getCellsCount(); index < length; index++) {
                let value = null;
                const cell = row.getCell(index);

                if ( cell ) {
                    const nisProperty = cell.getNISProperty();
                    const customProperty = cell.getCustomProperty();
                    if (sPropName === 'serialNumber') { // 固有属性
                        if (nisProperty.serialNumber !== undefined) {
                            value = nisProperty.serialNumber;
                        }
                    } else if (customProperty && customProperty.length) {
                        for (let index2 = 0, length2 = customProperty.length; index2 < length2; index2++) {
                            const pro = customProperty[index2];
                            if (pro && pro.name === sPropName) {
                                value = pro.value;
                                break;
                            }
                        }
                    }
                }

                props.push(value);
            }
        }

        return props;
    }

    public setNISDateCellFormat(rowID: string, colID: string, nType: number, sFormat: any): boolean {
        if ( colID ) {
            const cell = this.getCellByRowIDColID(rowID, colID);
            if ( cell && cell.isDateCell() ) {
                const newNisProps = cell.getNISProperty(); // new copy
                newNisProps.dateBoxFormat = nType;

                if (nType === NISDateBoxFormat.AutoCustom) {
                    try {
                        const sFormatObj = JSON.parse(sFormat);
                        if ('MM' !== sFormatObj.month) {
                            return false;
                        }

                        newNisProps.customFormat = sFormatObj;
                    } catch (error) {

                        // tslint:disable-next-line: no-console
                        console.log('日期自定义格式字符串不正确');
                        return false;
                    }
                }

                // critical
                cell.setNISCellProps(newNisProps);
                return true;
            }
        } else {
            const row = this.getRowByRowID(rowID);
            if (row) {
                for (let index = 0; index < row.getCellsCount(); index++) {
                    const cell = row.getCell(index);
                    if ( cell && cell.isDateCell() ) {
                        const newNisProps = cell.getNISProperty(); // new copy
                        newNisProps.dateBoxFormat = nType;

                        // critical
                        cell.setNISCellProps(newNisProps);
                        return true;
                    }
                }
            }
        }

        return false;
    }

    // for 'hideDateValue' prop exclusively
    public setNISDateCellProp(rowID: string, colID: string, sJson: string): boolean {
        if ( colID ) {
            const cell = this.getCellByRowIDColID(rowID, colID);
            if ( cell && cell.isDateCell() ) {
                const obj = JSON.parse(sJson);
                if (obj != null && obj.hideDateValue != null) {
                    const newNisProps = cell.getNISProperty(); // new copy
                    newNisProps.hideDateText = obj.hideDateValue;

                    // critical
                    cell.setNISCellProps(newNisProps);
                    return true;
                }
            }
        } else {
            const row = this.getRowByRowID(rowID);
            if (row) {
                for (let index = 0; index < row.getCellsCount(); index++) {
                    const cell = row.getCell(index);
                    if ( cell && cell.isDateCell() ) {
                        const obj = JSON.parse(sJson);
                        if (obj != null && obj.hideDateValue != null) {
                            const newNisProps = cell.getNISProperty(); // new copy
                            newNisProps.hideDateText = obj.hideDateValue;

                            // critical
                            cell.setNISCellProps(newNisProps);
                            return true;
                        }
                        break;
                    }
                }
            }
        }

        return false;
    }

    public setNISDateCellValue(rowID: string, colID: string, sValue: string): boolean {
        let res = false;
        if ( colID ) {
            const cell = this.getCellByRowIDColID(rowID, colID);
            if ( cell && cell.isDateCell() ) {
                res = cell.setNISCellDateTime(sValue);
                if (res) {
                    cell.content.setCellContentProps(cell.getCellContentProps());
                    this.parent.recalculate();
                    this.parent.updateCursorXY();
                }

                return res;
            }
        } else {
            const row = this.getRowByRowID(rowID);
            if (row) {
                for (let index = 0; index < row.getCellsCount(); index++) {
                    const cell = row.getCell(index);
                    if ( cell && cell.isDateCell() ) {
                        res = cell.setNISCellDateTime(sValue);
                        if (res) {
                            cell.content.setCellContentProps(cell.getCellContentProps());
                            this.parent.recalculate();
                            this.parent.updateCursorXY();
                        }

                        return res;
                    }
                }
            }
        }

        return res;
    }

    public setNISTimeCellFormat(rowID: string, colID: string, nType: number, sFormat: string): boolean {
        if ( colID ) {
            const cell = this.getCellByRowIDColID(rowID, colID);
            if ( cell && cell.isTimeCell() ) {
                const nisProperty: INISProperty = {
                    type: cell.getCellType(),
                    timeFormat: sFormat,
                };
                return (ResultType.Success === cell.setNisProperty(nisProperty));
            }
        } else {
            const row = this.getRowByRowID(rowID);
            if (row) {
                for (let index = 0; index < row.getCellsCount(); index++) {
                    const cell = row.getCell(index);
                    if ( cell && cell.isTimeCell() ) {
                        const nisProperty: INISProperty = {
                            type: cell.getCellType(),
                            timeFormat: sFormat,
                        };
                        return (ResultType.Success === cell.setNisProperty(nisProperty));
                    }
                }
            }
        }

        return false;
    }

    public setNISTimeCellValue(rowID: string, colID: string, sValue: string): boolean {
        let res = false;
        if ( colID ) {
            const cell = this.getCellByRowIDColID(rowID, colID);
            if ( cell && cell.isTimeCell() ) {
                res = cell.setNISCellTimeText(sValue);
                if (res) {
                    cell.content.setCellContentProps(cell.getCellContentProps());

                    this.parent.recalculate();
                    this.parent.updateCursorXY();
                    return true;
                }
            }
        } else {
            const row = this.getRowByRowID(rowID);
            if (row) {
                for (let index = 0; index < row.getCellsCount(); index++) {
                    const cell = row.getCell(index);
                    if ( cell && cell.isTimeCell() ) {
                        res = cell.setNISCellTimeText(sValue);
                        if (res) {
                            cell.content.setCellContentProps(cell.getCellContentProps());

                            this.parent.recalculate();
                            this.parent.updateCursorXY();
                            return true;
                        }
                    }
                }
            }
        }

        return res;
    }

    public canRemoveAtCurCell(direction: number): any {
        const selection = this.selection;
        if (selection.bUse) {
            const start = selection.startPos.pos;
            const end = selection.endPos.pos;
            if (start.rowIndex !== end.rowIndex || start.cellIndex !== end.cellIndex) {
                return;
            }

            return this.content[start.rowIndex]?.content[start.cellIndex]?.canRemoveAtCurCell(direction);
        }
        return this.curCell?.canRemoveAtCurCell(direction);
    }

    public setNISCompoundCellCodeAndValueByArray(
        rowID: string, colID: string, sJson: any, nType?: number): boolean {
        // const sJsonObj = JSON.parse(sJson);
        // console.log(sJsonObj)
        const {code: codeArr, value: valueArr} = sJson;
        const collection: CodeValueItem[] = [];
        if (codeArr != null && valueArr != null && codeArr.length === valueArr.length) {
            for (let i = 0, len = codeArr.length; i < len; i++) {
                // TODO: inherit bSelect?
                collection.push(new CodeValueItem(codeArr[i], valueArr[i], false));
            }
        }

        let result = false;
        const bCheckMultiple = (3 === nType || 4 === nType ? true : false);
        const selectType = (1 === nType || 4 === nType ? NISSelectType.Combo : NISSelectType.Dropdown);
        if ( rowID && colID ) {
            const cell = this.getCellByRowIDColID(rowID, colID);
            if ( cell && cell.isListCell() && cell.property ) {
                const newNisProps = cell.getNISProperty();
                newNisProps.items = collection;
                newNisProps.selectType = selectType;
                newNisProps.bCheckMultiple = bCheckMultiple;
                result = (ResultType.Success === cell.property.setNISProperty(newNisProps)) ? true : false;
                cell.setCellText('');
            }
        } else if ( colID ) {
            const colIndex = this.getColIndexByColID(colID);
            if ( -1 !== colIndex ) {
                this.content.forEach((row) => {
                    const cell = row.getCell(colIndex);
                    const newNisProps = cell.getNISProperty();
                    newNisProps.items = collection;
                    newNisProps.selectType = selectType;
                    newNisProps.bCheckMultiple = bCheckMultiple;

                    if ( cell && cell.isListCell() && cell.property ) {
                        result = (ResultType.Success === cell.property.setNISProperty(newNisProps)) ? true : false
                                    || result;
                        cell.setCellText('');
                    }
                });
            }
        }

        return result;
    }

    public setNISCompoundCellSeparator(rowID: string, colID: string, sSeparator: string): boolean {
        let result = false;

        if ( rowID && colID ) {
            const cell = this.getCellByRowIDColID(rowID, colID);
            if ( cell && cell.isListCell() && cell.property ) {
                const newNisProps = cell.getNISProperty();
                newNisProps.separator = sSeparator;
                result = (ResultType.Success === cell.property.setNISProperty(newNisProps)) ? true : false
                            || result;
                // cell.setCellText('');
            }
        } else if ( colID ) {
            const colIndex = this.getColIndexByColID(colID);
            if ( -1 !== colIndex ) {
                this.content.forEach((row) => {
                    const cell = row.getCell(colIndex);
                    const newNisProps = cell.getNISProperty();
                    newNisProps.separator = sSeparator;

                    if ( cell && cell.isListCell() && cell.property ) {
                        result = (ResultType.Success === cell.property.setNISProperty(newNisProps)) ? true : false
                                    || result;
                        // cell.setCellText('');
                    }
                });
            }
        }

        if ( result ) {
            this.parent.recalculate();
            this.parent.updateCursorXY();
        }

        return result;
    }

    public getNISCompoundCelllCurrentValue(rowID: string, colID: string, nType?: number): string {
        let result = '';
        const cell = this.getCellByRowIDColID(rowID, colID);
        if ( cell && cell.isListCell() ) {
            // return cell.getNISCompoundCelllCurrentValue(nType);

            const newNisProps = cell.getNISProperty(); // new copy
            if (newNisProps != null && newNisProps.items != null) {
                // console.log(newNisProps.items)
                const items = newNisProps.items;
                for (const item of items) {
                    if (item != null && item.bSelect === true) {
                        if (result.length > 0) {
                            result += ',' + item.value;
                        } else {
                            result += item.value;
                        }
                    }
                }

            }
        }

        return result;
    }

    public getNISCompoundCelllCurrentCode(rowID: string, colID: string, nType?: number): string {
        let result = '';
        const cell = this.getCellByRowIDColID(rowID, colID);
        if ( cell && cell.isListCell() ) {
            // return cell.getNISCompoundCelllCurrentCode(nType);

            const newNisProps = cell.getNISProperty(); // new copy
            if (newNisProps != null && newNisProps.items != null) {
                // console.log(newNisProps.items)
                const items = newNisProps.items;
                for (const item of items) {
                    if (item != null && item.bSelect === true) {
                        if (result.length > 0) {
                            result += ',' + item.code;
                        } else {
                            result += item.code;
                        }
                    }
                }
            }
        }

        return result;
    }

    public setNISNumCellMaxValue(rowID: string, colID: string, maxValue: number): number {
        let res = ResultType.Failure;
        const cell = this.getCellByRowIDColID(rowID, colID);
        if ( cell && cell.isNumberCell() ) {
            res = cell.setNISNumCellMaxValue(maxValue);
            if (ResultType.Success === res && !cell.isValidOfNumberCellContent()) {
                cell.resetNumberCellContent();

                this.parent.recalculate();
                this.parent.updateCursorXY();
            }
        }

        return res;
    }

    public getNISNumCellMaxValue(rowID: string, colID: string): number {
        const cell = this.getCellByRowIDColID(rowID, colID);
        if ( cell && cell.isNumberCell() ) {
            return cell.getNISNumCellMaxValue();
        }

        return ResultType.NumberNaN;
    }

    public setNISNumCellMinValue(rowID: string, colID: string, minValue: number): number {
        let res = ResultType.Failure;
        const cell = this.getCellByRowIDColID(rowID, colID);
        if ( cell && cell.isNumberCell() ) {
            res = cell.setNISNumCellMinValue(minValue);
            if (ResultType.Success === res && !cell.isValidOfNumberCellContent()) {
                cell.resetNumberCellContent();

                this.parent.recalculate();
                this.parent.updateCursorXY();
            }
        }

        return res;
    }

    public getNISNumCellMinValue(rowID: string, colID: string): number {
        const cell = this.getCellByRowIDColID(rowID, colID);
        if ( cell && cell.isNumberCell() ) {
            return cell.getNISNumCellMinValue();
        }

        return ResultType.NumberNaN;
    }

    public setNISNumCellPrecision(rowID: string, colID: string, precision: number): number {
        let res = ResultType.Failure;
        const cell = this.getCellByRowIDColID(rowID, colID);
        if ( cell && cell.isNumberCell() ) {
            res = cell.setNISNumCellPrecision(precision);
            if (ResultType.Success === res && !cell.isValidOfNumberCellContent()) {
                cell.resetNumberCellContent();

                this.parent.recalculate();
                this.parent.updateCursorXY();
            }
        }

        return res;
    }

    public getNISNumCellPrecision(rowID: string, colID: string): number {
        const cell = this.getCellByRowIDColID(rowID, colID);
        if ( cell && cell.isNumberCell() ) {
            return cell.getNISNumCellPrecision();
        }

        return ResultType.NumberNaN;
    }

    public setNISQuickCellValueByArray(rowID: string, colID: string, sJson: any): boolean {
        const cell = this.getCellByRowIDColID(rowID, colID);
        if ( cell && cell.isQuickCell() ) {
            // return cell.setNISQuickCellValueByArray(sJson);
            const newNisProps = cell.getNISProperty(); // new copy
            // console.log(newNisProps)

            // extract sJson
            // const sJsonArr = sJson.split(',');
            // console.log(sJsonArr)
            const collection: CodeValueItem[] = [];
            if (sJson.length > 0) {
                for (const item of sJson) {
                    // TODO: inherit bSelect?
                    collection.push(new CodeValueItem(item, '', false));
                }
                newNisProps.items = collection;
            }

            if (cell.property != null) {
                const result = (ResultType.Success === cell.property.setNISProperty(newNisProps)) ? true : false;
                // cell.setCellText('');

                return result;
            }
        }

        return false;
    }

    public isValidOfNumberCellContent(cell?: TableCell): boolean {
        const curCell = cell ? cell : this.curCell;
        if ( curCell.isNumberCell() ) {
            return curCell.isValidOfNumberCellContent();
        }

        return true;
    }

    public resetCurNumberCellContent(cell?: TableCell): void {
        const curCell = cell ? cell : this.curCell;
        if ( curCell.isNumberCell() ) {
            curCell.resetNumberCellContent();
        }
    }

    public getRowByRowID(rowID: string): TableRow {
        for (let index = 0, length = this.content.length; index < length; index++) {
            const row = this.content[index];
            if ( row && !row.isTableHeader() && row.getNISRowID() === rowID ) {
                return row;
            }
        }

        return null;
    }

    public getNISCellByColID(row: TableRow, colID: string): TableCell {
        const colIDs = this.property.getNISTableColIDs();

        for (let index = 0, length = row.content.length; index < length; index++) {
            const cell = row.getCell(index);
            if ( cell && colIDs[index] === colID ) {
                return cell;
            }
        }

        return null;
    }

    /**
     * 通过rowID和colID获取单元格
     * @param rowID
     * @param colID
     * @returns
     */
    public getCellByRowIDColID(rowID: string, colID: string): TableCell {
        const row = this.getRowByRowID(rowID);
        if ( row ) {
            return this.getNISCellByColID(row, colID);
        }

        return null;
    }

    /**
     * 通过colID获取所在行号
     * @param colID
     * @returns
     */
    public getColIndexByColID(colID: string): number {
        const colIDs = this.getNISTableColIDs();
        if ( colIDs && colIDs.length ) {
            return colIDs.findIndex((id) => id === colID);
        }

        // for (let rowIndex = 0, length = this.content.length; rowIndex < length; rowIndex++) {
        //     const row = this.content[rowIndex];

        //     for (let cellIndex = 0; cellIndex < row.content.length; cellIndex++) {
        //         const cell = row.getCell(cellIndex);
        //         if ( cell && colIDs[cellIndex] === colID ) {
        //             return cellIndex;
        //         }
        //     }
        // }

        return -1;
    }

    public initIDs(): void {
        const rowIDs = [];
        const colIDs = [];

        for (let rowIndex = 0, length = this.content.length; rowIndex < length; rowIndex++) {
            const row = this.content[rowIndex];
            const id = this.createID();
            rowIDs.push(id);
            row.setNISRowID(id);
        }

        for (let index = 0; index < this.cols; index++) {
            colIDs.push(this.createID());
        }

        this.property.setNISTableColIDs(colIDs);
    }

    public createID(): string {
        return NISTable.createID(this);
    }

    public setColumnIDs(columnIDs: string[]): void {
        if (columnIDs != null && columnIDs.length > 0) {
            this.property.setNISTableColIDs(columnIDs);
        }
    }

    public getCurrentNISCellColID(): string {
        if ( this.curCell && !(this.selection.bUse && TableSelectionType.TableCell === this.selection.type) ) {
            const colIDs = this.getNISTableColIDs();
            return colIDs[this.curCell.metrics.startGridCol];
        }

        return ResultType.StringEmpty;
    }

    public checkRowHasCellMerge(cell?: TableCell): boolean {
        if ( this.isSelectionUse() && TableSelectionType.TableCell === this.selection.type ) {
            return true;
        }

        cell = (cell ? cell : this.curCell);
        if ( cell && cell.row ) {
            const row = cell.row;
            for (let index = 0, length = row.content.length; index < length; index++) {
                const curCell = row.getCell(index);
                if ( curCell &&
                        (1 < this.getVMergeCount(curCell.index, row.index) ||
                        VerticalMergeType.Continue === curCell.getVMerge())
                    ) {
                    return true;
                }
            }
        }

        return false;
    }

    public isSelectedMultiRows(): boolean {
        let res = false;
        if ( this.isSelectionUse() && TableSelectionType.TableCell === this.selection.type ) {
            let rowIndex = -1;
            const selectedCellsArray = this.getSelectionArray();
            selectedCellsArray.forEach((pos) => {
                if ( -1 !== rowIndex && rowIndex !== pos.rowIndex ) {
                    res = true;
                    return;
                }
                rowIndex = pos.rowIndex;
            });
        } else if ( this.isSelectedAll() && 1 < this.content.length ) {
            res = true;
        }

        return res;
    }

    public removeSelection(): void {
        if ( false === this.selection.bUse ) {
            return ;
        }

        if ( 0 >= this.content.length ) {
            this.curCell = null;
        } else {
            if ( TableSelectionType.Text === this.selection.type ) {
                this.curCell = this.content[this.selection.startPos.pos.rowIndex]
                                                    .getCell(this.selection.startPos.pos.cellIndex);
                this.curCell.content.removeSelection();
            } else if ( 0 < this.content.length && this.content[0].getCellsCount() > 0 ) {
                if ( !this.curCell ) {
                    const startPos = this.selection.startPos.pos;
                    const endPos = this.selection.endPos.pos;
                    const rowIndex = Math.max(startPos.rowIndex, endPos.rowIndex);

                    this.curCell = this.content[rowIndex].getCell(0);
                } else {
                    this.curCell.content.removeSelection();
                }
            }
        }

        this.selection.bStart = false;
        this.selection.bUse = false;
        this.selection.data = null;
        this.selection.type = TableSelectionType.Common;

        this.selection.startPos.pos = { rowIndex: 0, cellIndex: 0 };
        this.selection.endPos.pos = { rowIndex: 0, cellIndex: 0 };
    }

    public copy(parent: DocumentContentBase, option?: any): NISTable {
        const table = new NISTable(parent, this.logicDocument);
        this.copy2(table, option);

        return table;
    }

    /**
     * 获取非表头的首行
     * @returns
     */
    public getNISFirstRow(): TableRow {
        let res = null;

        for (let index = 0, length = this.content.length; index < length; index++) {
            const row = this.content[index];
            if ( row && !row.isTableHeader() ) {
                res = row;
                break;
            }
        }

        return res;
    }

    public isReadOnly(bOnlySelectCells?: boolean): boolean {
        if ( this.isAdminMode() ) {
            return false;
        }

        return ( this.property.isTableReadOnlyProtect() || this.isCanntDelete()
                || ( !this.isSelectedAll() && false === this.canDeleteRow(bOnlySelectCells))
                || (this.curCell && this.curCell.row && this.curCell.row.isReadOnly()) );
    }

    public isHiddenRow(row: TableRow): boolean {
        const height = row.getCurrentRowHeight();
        if ( !row.isFixed() ) {
            return (1 > Math.abs(height));
        } else {
            const topMargin = row.getTopMargin();
            const bottomMargin = row.getBottomMargin();

            return (1 > Math.abs(height + topMargin + bottomMargin));
        }
    }

    public setNISRowTextByColID(rowID: string, colIDs: any): number {
        let res = ResultType.ParamError;
        const row = this.getRowByRowID(rowID);
        if (row) {
            for (const colID in colIDs) {
                const cell = row.getCellByColID(colID);
                if (cell) {
                    this.setCellText(cell, colIDs[colID]);
                    res = ResultType.Success;
                } else {
                    res = ResultType.ParamError;
                    return res;
                }
            }
        }

        return res;
    }

    public insertNISRowByColID(indexRowID: string, direction: number, colIDs: any): number {
        let res = ResultType.ParamError;
        let row = this.getRowByRowID(indexRowID);

        if (row) {
            const berfore = 1 === direction ? true : false;
            if ( this.addTableRow(berfore,
                {insertIndex: row.index, count: 1,
                rowIndex: row.index, cellIndex: 0, bNotSelect: true}) ) {

                row = berfore ? this.content[row.index - 1] : this.content[row.index + 1];
                for (const colID in colIDs) {
                    const cell = row.getCellByColID(colID);
                    if (cell) {
                        this.setCellText(cell, colIDs[colID]);
                        res = ResultType.Success;
                    } else {
                        res = ResultType.ParamError;
                        return res;
                    }
                }
            }
        }

        return res;
    }

    protected checkHitInBorder(pointX: number, pointY: number, curPage: number): any {
        if ( this.logicDocument ) {
            const cellPos = this.getCellByXY(pointX, pointY, curPage);
            const row = this.content[cellPos.rowIndex];
            const cell = row.getCell(cellPos.cellIndex);
            return (this.logicDocument.isNISEditMode() || cell.isDateCell() || cell.isSignCell()) ? { pos: cellPos,
                border: -1,
                rowIndex: cellPos.rowIndex } : super.checkHitInBorder(pointX, pointY, curPage);
        }

        return null;
    }

    protected checkMerge(): any {
        let cellType = -1;
        const selectedCellsArray = this.getSelectionArray();
        for (const pos of selectedCellsArray) {
            const row = this.content[pos.rowIndex];
            if ( row ) {
                const cell = row.getCell(pos.cellIndex);

                if ( -1 === cellType ) {
                    cellType = cell.getCellType();
                } else if ( cellType !== cell.getCellType() ) {
                    gEvent.setEvent(this.logicDocument.id, gEventName.MessageEvent, this, MessageType.TableCellType);
                    return { startGrid: 0, endGrid: 0, rowsInfo: null, bCanMerge: false };
                }
            }
        }

        // const canMerge = super.checkMerge();
        // let bCanMerge = true;
        // canMerge.bCanMerge = false;
        // return { startGrid, endGrid, rowsInfo, bCanMerge };
        return super.checkMerge();
    }

    protected getAllRowsIDs(): string[] {
        const rowIDs = [];

        for (let index = 0, length = this.content.length; index < length; index++) {
            const row = this.content[index];
            if ( row && !row.isTableHeader() ) {
                const rowID = row.getNISRowID();
                if ( rowID ) {
                    rowIDs.push(rowID);
                }
            }
        }

        return rowIDs;
    }

    protected isNISContentRow(row: TableRow): boolean {
        return (!row.isTableHeader() && !this.isHiddenRow(row));
    }

    protected moveRowToPositionImp(sourceRow: TableRow, descRow: TableRow, direct: number): number {
        const sourceIndex = sourceRow.index;
        const insertPos = (1 === direct ? descRow.index : descRow.index + 1);

        if (sourceIndex === insertPos || (sourceIndex + 1) === insertPos) {
            return ResultType.UnEdited;
        }

        this.content.splice(sourceIndex, 1);

        if (sourceIndex > insertPos) {
            this.content.splice(insertPos, 0, sourceRow);
        } else {
            this.content.splice(insertPos - 1, 0, sourceRow);
        }

        return ResultType.Success;
    }

    protected getFirstHiddenRow(): TableRow {
        for (let index = 0, length = this.content.length; index < length; index++) {
            const row = this.content[index];
            if (row && row.isTableHeader()) {
                continue;
            } else if (this.isHiddenRow(row)) {
                return row;
            } else {
                break;
            }
        }

        return null;
    }

    protected getFirstContentRow(): TableRow {
        for (let index = 0, length = this.content.length; index < length; index++) {
            const row = this.content[index];
            if (row && this.isNISContentRow(row)) {
                return row;
            }
        }

        return null;
    }

    private sortRows(items: any[]): void {
        const length = items.length;
        if (1 >= length) {
            return ;
        }

        for (let index = 0; index < length - 1; index++) {

            for (let index2 = index + 1; index2 < length; index2++) {
                const element = items[index];
                const nextElement = items[index2];
                const date = element.date;
                let time = element.time;
                const date2 = nextElement.date;
                let time2 = nextElement.time;

                if (date && date2) {
                    time = time ? time : '0:0';
                    time2 = time2 ? time2 : '0:0';
                    const timeString1 = (date + ' ' + time); // .replace('-', '/');
                    const timeString2 = (date2 + ' ' + time2); // .replace('-', '/');
                    if (new Date(Date.parse(timeString1)) > new Date(Date.parse(timeString2))) {
                        this.exchange(items, index, index2);
                    }
                } else if (date) {
                    this.exchange(items, index, index2);
                } else if (date2) {
                    // ;
                } else {
                    time = time ? time : '0:0';
                    time2 = time2 ? time2 : '0:0';
                    if (time !== time2) {
                        time = time.split(':');
                        time2 = time2.split(':');

                        if (time[0] > time2[0]) {
                            this.exchange(items, index, index2);
                        } else if (time[0] === time2[0]) {
                            if (time[1] > time2[1]) {
                                this.exchange(items, index, index2);
                            }
                        }
                    }
                }
            }
        }

        const startRow = items[0].row;
        for (let index = length - 1; index > 0; index--) {
            this.moveRowToPositionImp(items[index].row, startRow, 2);
            this.reIndexContent(0);
        }
    }

    private exchange(items: any[], pos1: number, pos2: number): void {
        const element = items[pos1];
        const nextElement = items[pos2];
        const temp = {
            row: nextElement.row,
            date: nextElement.date,
            time: nextElement.time,
        };
        items[pos2] = {
            row: element.row,
            date: element.date,
            time: element.time,
        };
        items[pos1] = temp;
    }

    private getTableRowInfo(row: TableRow, colIDs: string[]): any {
        const rowInfo = {
            rowID: row.getNISRowID(),
            signedStatus: row.getSignStatus(),
            rowType: row.getNISRowType(),
            cellInfo: [],
        };

        for (let cellIndex = 0, len = row.getCellsCount(); cellIndex < len; cellIndex++) {
            const cell = row.getCell(cellIndex);
            if (cell) {
                const cellType = cell.getCellType();
                let cellContent = '';

                switch (cellType) {
                    case NISTableCellType.Text:
                    case NISTableCellType.Quick:
                    case NISTableCellType.List:
                    case NISTableCellType.Number:
                        cellContent = cell.getCellContentText();
                        break;

                    case NISTableCellType.Date:
                        const cur = cell.getDateTime();
                        if (cur) {
                            cellContent = cur.split(' ')[0];
                        }
                        break;

                    case NISTableCellType.Time:
                        cellContent = cell.getTimeText();
                        break;

                    case NISTableCellType.BloodPressure:
                        cellContent = cell.getCellContentText();
                        if (cellContent) {
                            const pressList = cellContent.split('/');

                            if (pressList[0] && '---' === pressList[0] &&
                                pressList[1] && '---' === pressList[1]) {
                                cellContent = '';
                            }
                        }
                        break;

                    case NISTableCellType.Signature:
                        break;
                }

                const cellInfo = {
                    colID: colIDs[cellIndex],
                    cellType: cell.getCellType(),
                    text: cellContent,
                    customProperty: {},
                };

                const customProperty = cell.getCustomProperty();
                if (customProperty && customProperty.length) {
                    cellInfo['customProperty'] = customProperty.map((item) => {
                        const prop = {};
                        prop[item.name] = item.value;
                        return {...prop};
                    });
                }

                rowInfo.cellInfo.push(cellInfo);
            }
        }
        return rowInfo;
    }

    private setNISColID(index: number, id: string): boolean {
        if (typeof id !== 'string') {
            return false;
        }

        return this.property.setNISTableColID(index, id);
    }

    private setCellTextByCell(cell: TableCell, text: string, preCell?: TableCell): number {
        const nisProperty = cell.getNISProperty();
        switch (cell.getCellType()) {
            case NISTableCellType.Text:
            case NISTableCellType.Quick:{
                cell.content.resetContentByText(text, cell.getCellContentProps());
                break;
            }
            case NISTableCellType.List: {
                const pos = [];
                const selectItems = text.split('，');
                nisProperty.items.forEach((value, index) => {
                    if (selectItems.indexOf(value.code) !== -1) {
                        pos.push(index);
                    }
                });
                if (pos.length > 0) {
                    cell.setNISCellListItemsNoRecal(pos);
                }
                break;
            }
            case NISTableCellType.Number:
                cell.content.resetContentByText(text, cell.getCellContentProps());
                // if (!cell.isValidOfNumberCellContent()) {
                //     cell.resetNumberCellContent();
                // }
                break;

            case NISTableCellType.Date: {
                const res = cell.setNISCellDateTime(text);
                // if (/^(\d){4}-(\d){2}-(\d){2}/.test(text)) {
                //     const res = cell.setNISCellDateTime(text);
                //     if (res) {
                //         cell.content.setCellContentProps(cell.getCellContentProps());
                //     }
                // }
                break;
            }
            case NISTableCellType.Time: {
                cell.setNISCellTimeText(text);
                // const time = text.split(':');
                // if ( !(!time || 2 !== time.length
                //     || 0 > Number.parseInt(time[0], 0) || 24 <= Number.parseInt(time[0], 0)
                //     || 0 > Number.parseInt(time[1], 0) || 60 <= Number.parseInt(time[1], 0))) {
                //     const res = cell.setNISCellTimeText(text);
                //     if (res) {
                //         cell.content.setCellContentProps(cell.getCellContentProps());
                //     }
                // }
                break;
            }
            case NISTableCellType.BloodPressure:
                const matchs = /(\d{1,3})[\/ :]+(\d{1,3})/g.exec(text);
                if (matchs) {
                    cell.setNISCellBPText(`${matchs[1]}:${matchs[2]}`);
                }
                break;

            case NISTableCellType.Signature:
                break;
        }
        return ResultType.Success;
    }

    private setCellText(cell: TableCell, content: any): boolean {
        let result = false;

        if ( cell && null != content ) {
            const type = cell.getCellType();
            switch (type) {
                case NISTableCellType.Text:
                case NISTableCellType.Quick:
                case NISTableCellType.List:
                    result = cell.content.resetContentByText(content, cell.getCellContentProps()) || result;
                    break;

                case NISTableCellType.Date:
                    result = cell.setNISCellDateTime(content) || result;
                    break;

                case NISTableCellType.Time:
                    result = cell.setNISCellTimeText(content) || result;
                    break;

                case NISTableCellType.Number:
                    if (typeof content === 'number') {
                        result = cell.content.resetContentByText(content.toString(),
                                cell.getCellContentProps()) || result;
                        cell.resetNumberCellContent();
                    }
                    break;
            }
        }

        return result;
    }
}
