import ContentChanges, { ContentChangesElement } from '../ContentChanges';
import Document from '../Document';
// import { Table } from '../Table';
import { TableBase } from '../TableBase';
import { ChangeTableAddTableName } from './TableChange';
// import { ChangeTableAddTableName, ChangeTableRemoveTableName } from './TableChange';

export class TableManager {
    private parent: Document;
    private tableNames: Map<string, TableBase>;
    private tableCaches: TableBase[];
    private contentChanges: ContentChanges;

    constructor(doc: Document) {
        this.parent = doc;
        this.tableNames = new Map();
        this.tableCaches = [];
        this.contentChanges = new ContentChanges();
    }

    public getUniqueTableName(name?: string): string {
        if ( null != name ) {
            if (true === this.checkTableName(name)) {
                return name;
            }
        } else {
            name = '表格';
        }

        for (let number = 1; number < Number.MAX_SAFE_INTEGER; number++) {
            const temp = name + number;
            if ( false === this.tableNames.has(temp) ) {
                name = temp;
                break;
            }
        }

        return name;
    }

    public getTableMap(): Map<string, TableBase> {
        return this.tableNames;
    }

    // 专门用于文件读取表格
    public setTableName(table: TableBase, name: string, bLoadFile?: boolean): boolean {
        const bValid = this.checkTableName(name);
        if ((bLoadFile && !name && bValid) || bValid) {
            this.tableNames.delete(table.getTableName());
            this.tableNames.set(name, table);

            return true;
        }

        return false;
    }

    public getTableByName(name: string): TableBase {
        return this.tableNames.get(name);
    }

    public checkTableName(name: string): boolean {
        if ( 0 === name.length || this.tableNames.has(name) ) {
            return false;
        }

        if ( null == this.getTableByName(name) ) {
            this.tableNames.delete(name);
        }

        return true;
    }

    public add(table: TableBase, name: string): void {
        // const history = this.parent.getHistory();
        // history.addChange(new ChangeTableAddTableName(table));
        this.tableNames.set(name, table);
    }

    public clear(): void {
        this.parent = null;
        this.tableNames.clear();
    }

    public getTableById(id: number): TableBase {
        const tables = this.tableNames;
        for (const [name, table] of tables) {
            if (table.id === id) {
                return table;
            }
        }
    }

    public removeTableName(name: string): boolean {
        if (this.tableNames.has(name) === true) {
            // const history = this.parent.getHistory();
            // history.addChange(new ChangeTableRemoveTableName(this.tableNames.get(name)));
            this.tableNames.delete(name);
            return true;
        }

        return false;
    }

    public removeTableNameByTable(table: TableBase): void {
        if (table && this.tableNames.get(table.getTableName()) === table) {
            this.tableNames.delete(table.getTableName());
        }
    }

    public addTableNameByTable(table: TableBase): void {
        if (table) {
            this.tableNames.set(table.getTableName(), table);
        }
    }

    public addInsertTable(table: TableBase): void {
        this.tableCaches.push(table);
    }

    public getInsertTableCaches(): TableBase[] {
        return this.tableCaches;
    }

    public insertTableCaches(): void {
        const history = this.parent.getHistory();
        for (let index = 0, length = this.tableCaches.length; index < length; index++) {
            const table = this.tableCaches[index];
            table.setLogicDocument(this.parent);

            // let tableName = table.getTableName();
            // if ( true !== this.checkTableName(tableName)) {
            //     tableName = this.getUniqueTableName(tableName);
            //     table.tableName = tableName;
            // }
            this.tableNames.set(table.getTableName(), table);
            if ( history ) {
                history.addChange(new ChangeTableAddTableName(table));
            }
        }

        this.tableCaches = [];
    }

    public resetInsertTableCaches(): void {
        this.tableCaches = [];
    }

    public addContentChanges( changes: ContentChangesElement ): void {
        this.contentChanges.add(changes);
    }
}
