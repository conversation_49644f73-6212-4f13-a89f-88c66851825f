import * as React from 'react';
import Modal from './modals/index';
import {GlobalEvent as gEvent, GlobalEventName as gEventName} from '../../../common/GlobalEvent';
import { NewControlType } from '../../../common/commonDefines';
import { createRoot } from 'react-dom/client';
import {IFRAME_MANAGER} from '../../../common/IframeManager';
// import {NewCombox} from './document/NewCombox';
// import { NewAddressbox } from './document/NewAddressbox';
// import { NewDateBox2 } from '../NewDateBox2';

interface IEditorProps {
    host: any;
    documentCore: any;
}

interface IErrorBoundaryProps {
    children?: React.ReactNode;
}

class ErrorBoundary extends React.Component<IErrorBoundaryProps> {
    constructor(props: any) {
      super(props);
    }

    public componentDidCatch(error: any, info: any): void {
      // tslint:disable-next-line: no-console
      console.log(error, info);
    }

    public render(): any {
      return this.props.children;
    }
}

class Container extends React.Component<{}> {
    public name: string = 'SDKcontainerExt';
    private editorContainer: any;
    private myRef: any;
    private rightMenus: any[];
    private closeCallback: any;
    private bNewCombox: boolean;
    private bDateBox: boolean;
    private boxInfo: any;
    private selectList: any;
    private selectRef: any;
    private selectDom: any;
    private currentId: number;
    private errorCode: number;
    private bAddressBox: boolean;
    constructor(props: any) {
        super(props);
        this.editorContainer = {};
        this.myRef = React.createRef();
        this.rightMenus = [];
        this.selectRef = React.createRef();
        this.selectDom = React.createRef();
        this.errorCode = 10;
    }

    public render(): any {
        return (
            <React.Fragment>
                <ErrorBoundary><Modal host={this} ref={this.myRef} /></ErrorBoundary>
                <ErrorBoundary>{this.renderRightMenu()}</ErrorBoundary>
                <ErrorBoundary>{this.renderDateBox()}</ErrorBoundary>
                <ErrorBoundary>{this.renderListBox()}</ErrorBoundary>
                <ErrorBoundary>{this.renderSelectList()}</ErrorBoundary>
            </React.Fragment>
        );
    }

    public componentDidMount(): void {
        // this.addEvent();
    }

    public componentDidCatch(): void {
        // tslint:disable-next-line: no-console
        console.log(this, 'sdk container, componentDidCatch');
    }

    public componentWillUnmount(): void {
        // tslint:disable-next-line: no-console
        console.log(this, 'sdk container, componentWillUnmount');
    }

    public setEditorInfo(id: number, editor: any): void {
        if (this.editorContainer[id]) {
            return;
        }
        this.editorContainer[id] = {
            host: editor,
            documentCore: editor.state.documentCore,
        };
        this.addEvent(id);
    }

    public addRightMenu(id: number, menu: any): void {
        this.rightMenus.push({id, component: menu});
        this.setState({});
    }

    public getEditorInfo(id: number): IEditorProps {
        return this.editorContainer[id];
    }

    public showNewControlBox(id: number, info: any, close: () => void): void {
        const editorContainer = this.editorContainer[id];
        if (!editorContainer) {
            return;
        }
        const iframe = IFRAME_MANAGER.getIframe(id);
        const position = iframe.getBoundingClientRect();
        // console.log(position)
        info.left += position.left;
        info.top += position.top + 3;
        this.closeCallback = close;
        switch (info.type) {
            case NewControlType.DateTimeBox: {
                this.bDateBox = true;
                break;
            }
            case NewControlType.AddressBox: {
                this.bAddressBox = true;
                // console.log(position.left)
                // editor can have a delta distance, which is needed for addressbox to determine new xLimit
                info.deltaLeft = position.left;
                break;
            }
            default: {
                this.bNewCombox = true;
            }
        }
        this.currentId = id;
        this.boxInfo = info; // connection here
        this.setState({});
    }

    public closeBox(): void {
        if (this.bNewCombox !== true && this.bDateBox !== true && this.bAddressBox !== true) {
            return;
        }
        this.bNewCombox = false;
        this.bDateBox = false;
        this.setState({});
    }

    public setSelectList(component: any): Promise<any> {
        this.selectList = component;
        return new Promise((resolve, reject) => {
            this.setState({}, () => {
                return resolve({dom: this.selectDom.current, reactVm: this.selectRef.current});
            });
        });
    }

    public getSelectVm(): any {
        if (!this.selectRef || !this.selectRef.current) {
            return;
        }

        return {dom: this.selectDom.current, reactVm: this.selectRef.current};
    }

    public removeEditor = (id: number): void => {
        if (!this.editorContainer[id]) {
            return;
        }
        if (this.myRef.current) {
            this.myRef.current.clearDialog();
        }
        const index = this.rightMenus.findIndex((item) => item.id === id);
        if (index > -1) {
            this.rightMenus.splice(index, 1);
        }

        // IFRAME_MANAGER.deleteIframe(id);
        gEvent.deleteEvent(id, gEventName.DialogEvent, this.showTypeModal);
        // this.selectList = undefined;
        // this.selectDom = undefined;
        this.editorContainer[id] = undefined;
        this.bNewCombox = false;
        this.bDateBox = false;
        this.setState({});
    }

    private renderSelectList(): any {
        if (!this.selectList) {
            return null;
        }

        return (
        <div className='hz-editor-select-container' ref={this.selectDom}>
            <this.selectList ref={this.selectRef} />
        </div>
        );
    }

    private renderDateBox(): any {
        // if (this.bDateBox !== true) {
        //     return null;
        // }

        // const boxInfo = this.boxInfo;
        // if (!boxInfo) {
        //     return null;
        // }
        // const container = this.editorContainer[this.currentId];
        // return (
        // <NewDateBox2
        //     documentCore={container.documentCore}
        //     newControlPropety={boxInfo.property}
        //     closeNewBoxList={this.close}
        //     refresh={this.refresh}
        //     host={container.host}
        //     bFromEnter={boxInfo.bFromEnter}
        //     lineHeight={boxInfo.lineHeight}
        //     position={{top: boxInfo.top, left: boxInfo.left, width: boxInfo.width}}
        // />
        // );
    }

    private renderListBox(): any {
        // if (this.bNewCombox !== true) {
        //     return null;
        // }
        // const boxInfo = this.boxInfo;
        // if (!boxInfo) {
        //     return null;
        // }
        // const container = this.editorContainer[this.currentId];
        // return (
        // <NewCombox
        //     documentCore={container.documentCore}
        //     newControlPropety={boxInfo.property}
        //     closeNewComboxList={this.close}
        //     refresh={this.refresh}
        //     bFromEnter={boxInfo.bFromEnter}
        //     lineHeight={boxInfo.lineHeight}
        //     position={{top: boxInfo.top, left: boxInfo.left, width: boxInfo.width}}
        // />
        // );
    }

    private renderAddressBox(): any {
        // if (this.bAddressBox !== true) {
        //     return null;
        // }
        // const boxInfo = this.boxInfo;
        // // console.log(boxInfo)
        // if (!boxInfo) {
        //     return null;
        // }
        // const container = this.editorContainer[this.currentId];
        // return (
        // <NewAddressbox
        //     documentCore={container.documentCore}
        //     newControlPropety={boxInfo.property}
        //     closeNewBoxList={this.close}
        //     refresh={this.refresh}
        //     host={this}
        //     lineHeight={boxInfo.lineHeight}
        //     position={{top: boxInfo.top, left: boxInfo.left, width: boxInfo.width, deltaLeft: boxInfo.deltaLeft}}
        //     bFromEnter={boxInfo.bFromEnter}
        // />
        // );
    }

    private close = (bRefresh: boolean = true) => {
        this.closeCallback(bRefresh, true);
        this.bDateBox = false;
        this.bNewCombox = false;
        this.setState({});
    }

    private refresh = (bClose: boolean = true) => {
        const host = this.editorContainer[this.currentId].host;
        bClose && this.close(false);
        host.handleRefresh();
    }

    private renderRightMenu(): any {
        const editorContainers = this.editorContainer;
        return this.rightMenus.map((menu, index) => {
            const currentContainer = editorContainers[menu.id];
            return (
                <menu.component key={menu.id + index} host={currentContainer.host} />
            );
        });
    }

    private showTypeModal = (type: number, option?: any): void => {
        if (!this.myRef.current) {
            if (this.errorCode > 0) {
                this.errorCode--;
                this.setState({});
            }
            return;
        }
        this.myRef.current.showTypeModal(type, option);
    }

    private addEvent = (id: number): void => {
        gEvent.addEvent(id, gEventName.DialogEvent, this.showTypeModal);
        // gEvent.addEvent(id, gEventName.UnMounted, this.removeEvent);
    }
}

// tslint:disable-next-line: max-classes-per-file
class SDKcontainerExt {
    private _container: Container;
    constructor() {
        //
    }

    public isActiveSDK(): boolean {
        return this._container ? true : false;
    }

    public getSDK(): Container {
        return this._container;
    }

    public removeEditor(id: number): void {
        if (!this._container) {
            return;
        }
        this._container.removeEditor(id);
        delete this._container[id];
    }

    // public init(id: number, editor: any): Container {
    //     if (this._container) {
    //         this._container.setEditorInfo(id, editor);
    //         return this._container;
    //     }

    //     const dom = document.createElement('div');
    //     dom.className = 'editor-sdk-container hz-editor-container';
    //     document.body.appendChild(dom);

    //     this._container = ReactDOM.render(
    //         <Container/>,
    //         dom,
    //     ) as any;

    //     this._container.setEditorInfo(id, editor);
    //     return this._container;
    // }
    public init(id: number, editor: any): Container {
        if (this._container) {
            this._container.setEditorInfo(id, editor);
            return this._container;
        }
    
        const dom = document.createElement('div');
        dom.className = 'editor-sdk-container hz-editor-container';
        document.body.appendChild(dom);
    
        const root = createRoot(dom); // 创建并存储根实例
        this._container = root.render(<Container />) as any;
    
        this._container.setEditorInfo(id, editor);
        return this._container;
    }
}

// tslint:disable-next-line: sy-global-const-name
export const SDKcontainer = new SDKcontainerExt();
