// import * as React from 'react'; 
// import * as ReactTestUtils from 'react-dom/test-utils'; // ES6
// import { configure, shallow , render, mount} from 'enzyme';
// import * as Adapter from 'enzyme-adapter-react-16';

// import PageToolbar from './PageToolbar';
// import PageMenu from "./PageMenu";

// // seems not work for importing vars, TODO if have time
// // import {colorConfig} from './PageConfig';
// // import {spacingConfig} from './PageConfig';


// configure({adapter: new Adapter()});



// describe('<PageToolbar />', () => {
//     let wrapper;

//     it('PageToolbar render without crash', () => {
//         shallow(<PageToolbar spacingConfig={null} colorConfig={null} changeParagraphAlignment={null} refresh={null}/>);
//     });

//     it('PageToolbar\'s spacing and color is configurable', () => {
//         let colorConfig = {
//             toolbar: 'red'
//           }
          
//         let spacingConfig = {
//             toolbarHeight: '1cm',
//         };
//         wrapper = shallow(<PageToolbar spacingConfig={spacingConfig} colorConfig={colorConfig} changeParagraphAlignment={null} refresh={null}/>);
//         // console.log(wrapper.hasClass("hz-editor-toolbar"))
//         expect(wrapper.get(0).props.style.backgroundColor).toBe(colorConfig.toolbar);
//         expect(wrapper.get(0).props.style.height).toBe(spacingConfig.toolbarHeight);
//     });

    
// });