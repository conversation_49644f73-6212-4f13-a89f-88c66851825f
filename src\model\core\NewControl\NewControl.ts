import { ParaNewControlBorder } from '../Paragraph/ParaNewControlBorder';
import DocumentContentBase from '../DocumentContentBase';
import { NewControlManager } from '../NewControlManager';
import ParaPortion from '../Paragraph/ParaPortion';
import { INewControlProperty, NewControlContentSecretType, NewControlType, ResultType,
    NewControlDefaultSetting, CodeValueItem, DateBoxFormat, DataType,
    ICustomProps, filterChars, ICustomFormatDateProps, parseBoolean,
    DocumentSectionType,
    ICascade,
    MessageType,
    parseAndType,
    parseResultType,
    TRIANGLE_ARRAY,
    IStructParamJson,
    AlignType, CleanModeType,
    ITriggerEventInfo,
    filterChars2,
    IExternalDataProperty} from '../../../common/commonDefines';
import TextProperty, { TextVertAlign } from '../TextProperty';
import { ParagraphContentPos, ParaElementType } from '../Paragraph/ParagraphContent';
import Paragraph from '../Paragraph';
import { idCounter, getArrayByfilterChars} from '../util';
import ContentChanges, { ContentChangesElement } from '../ContentChanges';
import { ChangeNewControlRemoveItem, ChangNewControlBorder, ChangeNewControlName, ChangeNewControlTips,
    ChangeNewControlCanntEdit, ChangeNewControlCanntDelete, ChangeNewControlHiddenBackground,
    ChangeNewControlReverseEdit, ChangeNewControlHidden, ChangeNewControlShowBorder, ChangeNewControlViewSecretType,
    ChangeNewControlPlaceHolderContent, ChangeNewControlMustInput, ChangeNewControlAddItem,
    ChangeNewControlPlaceHolderPortion, // ChangeNewControlRemoveLeafItems,
    ChangeNewControlTitle, ChangNewControlParent, ChangeNewControlSerialNumber,
    ChangeNewControlCanntCopy, ChangNewControlParentControl } from './NewControlChange';
// import { HistoryDescriptionType } from '../HistoryDescription';
import { DocumentContent } from '../DocumentContent';
import History from '../History';
import ParaDrawing from '../Paragraph/ParaDrawing';
import DocumentContentElementBase from '../DocumentContentElementBase';
import { ParaElementBase } from '../Paragraph/ParaElementBase';
import Document from '../Document';
import { getDefaultFont, skipEscapeString } from '../../../common/commonMethods';
import { CascadeManager } from './CascadeManager';
import { ReviewInfo } from '../Revision';
// import { ChangeContent } from '../HistoryChange';
// import { HistroyItemType } from '../HistoryDescription';

/**
 * 结构化元素
 */
export abstract class NewControl {
    private id: number;
    private name: string;
    private serialNumber: string;
    // private cascades: ICascade[];
    // public parent: DocumentContentBase;

    private parentControl: NewControl;
    private leafList: NewControl[];
    private bContainLeaf: boolean;  // 包含子节点

    private bShowBorder: boolean;  // 显示边框
    private bHiddenBackground: boolean;  // 背景颜色高亮
    private bMustInput: boolean;
    private bReverseEdit: boolean;
    private bCanntEdit: boolean;
    private bCanntDelete: boolean;
    private bCanntCopy: boolean;
    private bHidden: boolean;
    private viewSecretType: NewControlContentSecretType;
    private content: NewControlContent;
    private tipsContent: string;
    // public placeHolder: ParaPortion;
    // public placeHolderContent: string;  // 占位符内容;
    private customProperty: Map<string, ICustomProps>;    // 用户自定义属性
    private contentChanges: ContentChanges;
    private bTabJump: boolean;
    // private _timeout: any;
    private bTextChanged: boolean;
    private identifier: string;
    // private triggerEventInfo: Map<string, any>;
    private bShowBgColor: boolean; // 只读模式下背景色显示
    private cascadeManager: CascadeManager;
    private cascades: ICascade[];
    private externalDataBind: IExternalDataProperty; // 外部数据源绑定

    constructor(parent: DocumentContentBase, name: string, property: INewControlProperty, sText?: string ) {
        this.id = idCounter.getNewId();
        this.name = name;

        // this.serialNumber = undefined;
        this.serialNumber = property.newControlSerialNumber || '';
        // this.parent = parent;
        this.parentControl = undefined;

        this.leafList = [];

        this.bShowBorder = false === property.isNewControlShowBorder ? false : true;
        this.bHiddenBackground = true !== property.isNewControlHiddenBackground ? false : true;
        this.bMustInput = true !== property.isNewControlMustInput ? false : true;
        this.bReverseEdit = true !== property.isNewControlReverseEdit ? false : true;
        this.bCanntEdit = true !== property.isNewControlCanntEdit ? false : true;
        this.bCanntDelete = true !== property.isNewControlCanntDelete ? false : true;
        this.bCanntCopy = true !== property.isNewControlCanntCopy ? false : true;
        this.bHidden = true !== property.isNewControlHidden ? false : true;
        this.viewSecretType = null != property.newControlDisplayType ?
                        property.newControlDisplayType : NewControlContentSecretType.DontSecret;
        this.bShowBgColor = true !== property.isShowBgColor ? false : true;

        this.content = new NewControlContent(parent, this.name, property.newControlType, property.newControlPlaceHolder,
                                this.bHidden, this.bShowBorder, this.bMustInput, sText, property.newControlTitle);
        this.tipsContent = property.newControlInfo || ''; // newControlInfo -> tipsContent -> helpTip

        this.bContainLeaf = property.newControlType === NewControlType.Section ? true : false;
        this.identifier = property.identifier;
        this.customProperty = new Map();
        this.contentChanges = new ContentChanges();
        this.bTabJump = property.tabJump === true;
        if (property.customProperty) {
            this.addCustomProps(property.customProperty, property.bClearItems);
        }
        if (property.cascade) {
            this.setCascades(property.cascade);
        }
        if (property.externalDataBind) {
            this.setSourceDataBind(property.externalDataBind);
        }
    }

    public getNewControlManager(): NewControlManager {
        let manager: NewControlManager = null;
        const parent = this.getDocumentParent();
        if ( parent ) {
            manager = parent.getNewControlManager();
        }

        return manager;
    }

    public getTipsContent(): string {
        return this.tipsContent;
    }

    public getIdentifier(): string {
        return this.identifier;
    }

    public getOperateContent(): NewControlContent {
        return this.content;
    }

    public setIdentifier(identifier: string): number {
        if (identifier == null || this.identifier === identifier) {
            return ResultType.UnEdited;
        }

        this.identifier = identifier;
        return ResultType.Success;
    }

    public getCascadeManager(): CascadeManager {
        if (this.cascadeManager) {
            return this.cascadeManager;
        }
        const newControlManager = this.getNewControlManager();
        if (!newControlManager) {
            return;
        }

        return this.cascadeManager = newControlManager.getCascadeManager();
    }

    public setTriggetEventInfo(info: ITriggerEventInfo): number {
        if (!info) {
            return ResultType.Failure;
        }
        const cascadeManager = this.getCascadeManager();
        if (!cascadeManager) {
            return ResultType.Failure;
        }
        return cascadeManager.setTriggetEventInfo(info, this);
    }

    public getTriggetEventInfo(): Map<string, any> {
        const cascadeManager = this.getCascadeManager();
        if (!cascadeManager) {
            return;
        }

        return cascadeManager.getTriggetEventInfo(this);
    }

    public getTriggetEventInfo2(): ITriggerEventInfo[] {
        const cascadeManager = this.getCascadeManager();
        if (!cascadeManager) {
            return;
        }

        return cascadeManager.getTriggetEventInfo2(this);
    }

    public removeTriggerEventInfo(name: string): void {
        const cascadeManager = this.getCascadeManager();
        if (!cascadeManager) {
            return;
        }

        cascadeManager.removeTriggerEventInfo(name, this);
    }

    public isTabJump(): boolean {
        return !this.isNotJumpNodes() && this.bTabJump;
    }

    public setTabJump(bTabJump: boolean): number {
        if (this.isNotJumpNodes()) {
            return ResultType.UnEdited;
        }

        if (bTabJump === this.bTabJump || null == bTabJump) {
            return ResultType.UnEdited;
        }
        this.bTabJump = bTabJump;
        return ResultType.Success;
    }

    public setTipsContent( content: string ): number {
        if ( content === this.tipsContent || null == content ) {
            return ResultType.UnEdited;
        }

        const history = this.getHistory();
        if ( history ) {
            history.addChange(new ChangeNewControlTips(this, this.tipsContent, content));
        }

        this.tipsContent = content;
        this.setDirty();
        return ResultType.Success;
    }

    public isContainSelected(text: string): boolean {
        return;
    }

    public setNewControlName(name: string): number {
        if ( name === this.name || null == name ) {
            return ResultType.UnEdited;
        }

        const history = this.getHistory();
        if ( history ) {
            history.addChange(new ChangeNewControlName(this, this.name, name));
        }

        const cascadeManager = this.getCascadeManager();
        if ( cascadeManager ) {
            cascadeManager.updateNewControlName(this.name, name);
        }
        const oldName = this.name;
        this.name = name;
        this.content.setNewControlName(name);
        this.getNewControlManager()
            .updateControlName(this, oldName);
        return ResultType.Success;
    }

    public getNewControlName(): string {
        return this.name;
    }

    public setTextFlag(flags: boolean): void {
        this.bTextChanged = flags;
    }

    public setDirty(): void {
        const para = this.content.getStartBorderPortion().paragraph;
        if (!para) {
            return;
        }
        const doc = para.getDocument();
        if ( doc ) {
            doc.setRegionDirty(para, true, this.bTextChanged);
            this.execFormulaCalc(doc);
        }
        this.setTextFlag(false);
    }

    public addCustomPropItem(key: string, value: string, type: DataType = DataType.String, targetValue?: any): number {
        this.customProperty.set(key, {name: key, value, type, targetValue: targetValue || value});
        return ResultType.Success;
    }

    public addCustomProps(props: ICustomProps[], bClearItems: boolean = true): number {
        if (!props) {
            return ResultType.UnEdited;
        }
        if (bClearItems) {
            this.customProperty.clear();
        }
        props.forEach((prop) => {
            if (prop.value === undefined) {
              prop.value = '';
            }
            this.customProperty.set(prop.name, prop);
            switch (prop.type) {
                case DataType.Boolean:
                    // prop.targetValue = prop.value ? parseBoolean(prop.value) : undefined;
                    prop.targetValue = prop.value ? parseBoolean(prop.value) : '';
                    break;
                case DataType.Number:
                    // prop.targetValue = prop.value ? Number(prop.value) : undefined;
                    prop.targetValue = prop.value ? Number(prop.value) : '';
                    break;
                default:
                    prop.targetValue = prop.value;
            }
        });
        return ResultType.Success;
    }

    public getCustomByPropName(name: string): any {
        const prop = this.customProperty.get(name);
        if (prop == null) {
            return;
        }

        return prop.targetValue || '';
    }

    public getCustomProps(): ICustomProps[] {
        const arrs = [];
        for (const [name, prop] of this.customProperty) {
            arrs.push(prop);
        }

        return arrs;
    }

    public isInCustomProp(name: string, value: any): boolean {
        const customProps = this.customProperty;
        for (const [actName, prop] of customProps) {
            if (prop.name === name && prop.targetValue === value) {
                return true;
            }
        }
        return false;
    }

    public setValueLabel(value: string): number {
        return ResultType.Success;
    }

    public getValueLabel(): string {
        return;
    }

    public setLabelCode(code: string): number {
        return ResultType.Success;
    }

    public getLabelCode(): string {
        return;
    }

    public isInCustomProp2(name: string, value: any): boolean {
        const customProps = this.customProperty;
        for (const [actName, prop] of customProps) {
            if (prop.name === name && prop.value === value) {
                return true;
            }
        }
        return false;
    }

    public isInCustomProp3(obj: object): boolean {
        const customProps = this.customProperty;
        const keys = Object.keys(obj);
        for (let index = 0, length = keys.length; index < length; index++) {
            const key = keys[index];
            const customProp = customProps.get(key);
            if (!customProp || customProp.value !== obj[key]) {
                return false;
            }
        }
        return true;
    }

    /**
     * 初始化占位符的字体颜色
     * @param textProperty
     */
    public initPlaceHolderDefaultTextProperty(textProperty: TextProperty): void {
        this.content.initPlaceHolderDefaultTextProperty(textProperty, this.bMustInput);
    }

    /**
     * 获取左边框所在位置
     * @param bAbsolute 是否为绝对位置
     */
    public getStartPos( bAbsolute: boolean = true ): ParagraphContentPos {
        const startBorderPortion = this.content.getStartBorderPortion();
        const startBorder = startBorderPortion ? startBorderPortion.content[0] as ParaNewControlBorder : undefined;

        if ( startBorder && startBorder.isNewControlStartBoder() ) {
            const parent = this.getDocumentParent();
            const para = parent.content[startBorder.paraIndex];

            if ( null == para || -1 === para.index ) {
                if (startBorderPortion.paragraph != null) {
                    this.content.setParent(startBorderPortion.paragraph.getParent(), this.getHistory());
                }
            }

            return startBorder.getBorderPos(this.getDocumentParent(), bAbsolute);
        }

        return null;
    }

    /**
     * 获取右边框所在位置
     * @param bAbsolute 是否为绝对位置
     */
    public getEndPos( bAbsolute: boolean = true ): ParagraphContentPos {
        const endBorderPortion = this.content.getEndBorderPortion();
        const endBorder = endBorderPortion ? endBorderPortion.content[0] as ParaNewControlBorder : undefined;

        if ( endBorder && ParaElementType.ParaNewControlBorder === endBorder.type ) {
            const parent = this.getDocumentParent();
            const para = parent.content[endBorder.paraIndex];

            if ( null == para || -1 === para.index ) {
                this.content.setParent(endBorderPortion.paragraph.getParent(), this.getHistory());
            }

            return endBorder.getBorderPos(this.getDocumentParent(), bAbsolute);
        }

        return null;
    }

    /**
     * 获取左边框所在portion
     */
    public getStartBorderPortion(): ParaPortion {
        return this.content.getStartBorderPortion();
    }

    /**
     * 获取右边框所在portion
     */
    public getEndBorderPortion(): ParaPortion {
        return this.content.getEndBorderPortion();
    }

    /**
     * 设置newControl当前所在段落的Parent：Document、TableCell、Head、Footer
     * @param parent
     */
    public setDocumentParent( parent: DocumentContentBase ): void {
        if ( parent === this.getDocumentParent() ) {
            return ;
        }

        this.content.setParent(parent);
    }

    public isTextBorder(): boolean {
        return;
    }

    public setLeafDocumentParent(parent: DocumentContentBase): void {
        const leafList = this.getLeafList();
        if ( leafList && 0 < leafList.length ) {
            for (const newControl of leafList) {
                newControl.setDocumentParent(parent);
                newControl.setLeafDocumentParent(parent);
            }
        }
    }

    public filterByName(results: NewControl[], names: string[], bParent?: boolean): void {
        const name = this.name;
        const actIndex = names.indexOf(name);
        if (actIndex !== -1) {
            names.splice(actIndex, 1);
            if (bParent === null) { // 把子类放进去
                results.unshift(this);
            } else if (bParent !== true) {
                results.unshift(this);
            } else {
                bParent = true;
            }
        }

        if (names.length === 0) {
            return;
        }

        const childs = this.getLeafList();
        if (childs && childs.length > 0) {
            for (let index = 0, len = childs.length; index < len; index++) {
                if (names.length === 0) {
                    break;
                }

                childs[index].filterByName(results, names, bParent);
            }
        }
    }

    /**
     * 获取newControl当前所在段落的Parent
     */
    public getDocumentParent(): DocumentContentBase {
        return this.content.getParent();
    }

    public getParagraphParent(): DocumentContentBase {
        return this.getParagraph()
            .getParent();
    }

    public getParagraph(): DocumentContentElementBase {
        return this.content.getStartBorderPortion().paragraph;
    }

    /**
     * 设置newControl当前所在Parent：Section
     * @param parent
     */
    public setParent( parent: NewControl ): void {
        this.parentControl = parent;
    }

    /**
     * 获取newControl当前所在Parent
     */
    public getParent(): NewControl {
        return this.parentControl;
    }

    public updateParent(parent: NewControl): void {
        const history = this.getHistory();
        if ( history ) {
            history.addChange(new ChangNewControlParentControl(this, this.parentControl, parent));
        }

        this.parentControl = parent;
    }

    /**
     * 获取newControl最外层Parent
     */
    public getTopParent(): NewControl {
        let parent = this.parentControl;
        let num = 0;
        const count = 10;

        while ( parent ) {
            if ( null == parent.parentControl ) {
                break;
            }

            num++;
            parent = parent.parentControl;

            if ( count <= num ) {
                break;
            }
        }

        return parent;
    }

    /**
     * 获取newControl当前所在范围的选择类型
     */
    public getCursorSelType( startPos: ParagraphContentPos, endPos: ParagraphContentPos,
                             type?: number, depth?: number ): CursorSelType {
        let result = CursorSelType.OutSideNewControl;
        const curStartPos = this.getStartPos();
        const curEndPos = this.getEndPos();

        // 当前newControl已经被删除
        if ( null == curStartPos || null == curEndPos ) {
            return result;
        }

        if ( -1 !== curStartPos.compare(startPos) ) {
            if ( -1 !== curStartPos.compare(endPos) ) {
                result = CursorSelType.OutSideNewControl;
            } else if ( 1 === endPos.compare(curStartPos) && 1 !== endPos.compare(curEndPos) ) {
                result = CursorSelType.ForwardOverNewControl;
            } else {
                result = CursorSelType.LayOverNewControl;
            }
        } else if ( -1 !== curEndPos.compare(startPos) ) {
            if ( -1 !== curEndPos.compare(endPos) ) {
                result = CursorSelType.InsideNewControl;
            } else {
                result = CursorSelType.BehindOverNewControl;
            }
        } else {
            result = CursorSelType.OutSideNewControl;
        }

        // console.log(result)
        return result;
    }

    /**
     * 当前位置插入的newControl是否合法
     * @param curPos
     * @param type
     * @param depth
     */
    public isValidAddNewControl( curPos: ParagraphContentPos, type: NewControlType, depth: number ): boolean {
        let result = false;

        const selType = this.getCursorSelType(curPos, curPos, type, depth);

        switch (selType) {
            case CursorSelType.OutSideNewControl:
                result = true;
                break;

            case CursorSelType.InsideNewControl: {
                if ( this.isNewSection() ) {
                    if ( true === this.bCanntEdit && !this.leafList?.length) {
                        // || this.isCursorInNewControlTitle(curPos.copy()) ) {
                        result = false;
                    } else if ( 0 < this.leafList?.length ) {
                        result = true;
                        // todo: check depth

                        const startIndex = findLowerBound(this.leafList, curPos);

                        if ( -1 < startIndex ) {
                            const newControl = this.leafList[startIndex];
                            if ( newControl ) {
                                result = newControl.isValidAddNewControl(curPos, type, depth) && result;
                            }
                        }
                    } else {
                        result = !this.bCanntEdit;
                    }
                }
                break;
            }
            default:
                break;
        }

        return result;
    }

    /**
     * 判断当前位置是否在newcontrol中
     * @param contentPos
     */
    public isCursorInNewControl( contentPos: ParagraphContentPos ): boolean {
        const selType = this.getCursorSelType(contentPos, contentPos);

        return ( CursorSelType.InsideNewControl === selType ? true : false );
    }

    /**
     * 当前位置是否在元素title内
     * @param contentPos 当前位置
     * @returns boolean
     */
    public isCursorInNewControlTitle(contentPos: ParagraphContentPos, direction?: number): boolean {
        const startPara = this.getStartBorderPortion().paragraph;
        if (startPara.index !== contentPos.get(0)) {
            return false;
        }

        // return startPara.isSelectedOrCursorInNewControlTitle();
        const curStartPos = this.getStartPos();
        curStartPos.pop();
        const startBorderPos = this.getStartBorderPortion()
                                        .getTextLength();
        const curPos = contentPos.pop();

        if (1 !== direction) {
            return (0 === curStartPos.compare(contentPos) && startBorderPos >= curPos);
        } else {
            return (0 === curStartPos.compare(contentPos) && startBorderPos > curPos);
        }
    }

    /**
     * 判断指定区域是否在newcontrol内
     * @param startPos 开始位置
     * @param endPos 结束位置
     */
    public isInsideNewControl( startPos: ParagraphContentPos, endPos: ParagraphContentPos ): boolean {
        const selType = this.getCursorSelType(startPos, endPos);

        return ( CursorSelType.InsideNewControl === selType ? true : false );
    }

    public isShowCodeAndValue(): boolean {
        return;
    }

    /**
     * 判断指定区域是否包含整个newcontrol
     * @param startPos 开始位置
     * @param endPos 结束位置
     */
    public isLayOverNewControl( startPos: ParagraphContentPos, endPos: ParagraphContentPos ): boolean {
        const selType = this.getCursorSelType(startPos, endPos);

        return ( CursorSelType.LayOverNewControl === selType ? true : false );
    }

    /**
     * 添加子newcontrol
     * @param newControl 子节点
     * @param pos
     */
    public addLeaf( newControl: NewControl, pos: ParagraphContentPos ): void {
        if ( null == newControl ) {
            return ;
        }

        // const curStartPos = this.getStartPos();
        // const curEndPos = this.getEndPos();

        // const leafStartPos = newControl.getStartPos();
        // const leafEndPos = newControl.getEndPos();

        // if ( !( 1 === leafStartPos.compare(curStartPos) && -1 === leafEndPos.compare(curEndPos))
        //     || 1 === leafStartPos.compare(leafEndPos) ) {
        //     return ;
        // }

        const count = this.leafList.length;
        const startIndex = findLowerBound(this.leafList, pos);
        const endIndex = findLowerBound(this.leafList, pos);

        const history = this.getHistory();

        if ( startIndex === endIndex ) {
            if ( -1 === startIndex ) {
                newControl.setParent(this);
                this.leafList.splice(startIndex + 1, 0, newControl);
                if ( history ) {
                    history.addChange(new ChangeNewControlAddItem(newControl, startIndex + 1, this));
                }
            } else if ( startIndex < count ) {
                const curControl = this.leafList[startIndex];
                if ( curControl ) {
                    const startPos = curControl.getStartPos();
                    const endPos = curControl.getEndPos();
                    if ( 1 !== startPos.compare(pos) && -1 !== endPos.compare(pos) ) {
                        curControl.addLeaf(newControl, pos);
                    } else if ( 1 === pos.compare(startPos) ) {
                        newControl.setParent(this);
                        this.leafList.splice(startIndex + 1, 0, newControl);
                        if ( history ) {
                            history.addChange(new ChangeNewControlAddItem(newControl, startIndex + 1, this));
                        }
                    }
                }
            }
        }
    }

    public addLeaf1(newControl: NewControl, startIndex?: number, bAdd?: boolean): void {
        newControl.setParent(this);
        if (startIndex !== undefined) {
            this.leafList.splice(++startIndex, 0, newControl);
        } else {
            this.leafList.push(newControl);
            startIndex = this.leafList.length - 1;
        }

        const history = this.getHistory();
        if ( history ) {
            history.addChange(new ChangeNewControlAddItem(newControl, startIndex, this, bAdd));
        }
    }

    public getLeafCount(): number {
        return this.leafList.length;
    }

    public getLeafList(): NewControl[] {
        return this.leafList;
    }

    public initPasteContent(): boolean {
        if (this.bHidden === true) {
            this.bHidden = false;
            this.setHidden(true, false);
            return true;
        }
        return false;
    }

    public updateBorderPortion(start: any, end: any): boolean {
        const content = this.content;
        content.setStartPosition(start);
        content.setEndPosition(end);
        return true;
    }

    /**
     * 获取子结点
     * @param startPos
     * @param endPos
     * @param bOverLay
     */
    public getLeafListNamesByPos( startPos: ParagraphContentPos, endPos: ParagraphContentPos,
                                  bOverLay: boolean = false ): string[] {
        const result: string[] = [];
        const count = this.leafList.length;

        if ( 0 === count || 1 === startPos.compare(endPos) ) {
            return result;
        }

        const startIndex = findLowerBound(this.leafList, startPos);
        const endIndex = findLowerBound(this.leafList, endPos);

        // startIndex != -1 : newControl中有子节点
        if ( -1 < startIndex && startIndex < count ) {
            const newControl = this.leafList[startIndex];
            const newControlEndPos = newControl.getEndPos();

            if ( newControl && 1 !== startPos.compare(newControlEndPos) ) {
                const newControlStartPos = newControl.getStartPos();
                if ( -1 === startPos.compare(newControlStartPos) && -1 === newControlEndPos.compare(endPos) ) {
                    result.push(newControl.getNewControlName());
                } else if (bOverLay && 1 === endPos.compare(newControl.getStartPos())) {
                    result.push(newControl.getNewControlName());
                }

                const leafListNames = newControl.getLeafListNamesByPos(startPos, endPos, bOverLay);

                for (const name of leafListNames) {
                    result.push(name);
                }
            } else if ( newControl && true === bOverLay && 1 === newControlEndPos.compare(startPos) ) {
                result.push(newControl.getNewControlName());
            }
        }

        for (let index = startIndex + 1; index <= endIndex && index < count; index++) {
            const newControl = this.leafList[index];
            if ( newControl ) {
                if ( index !== endIndex || 1 === endPos.compare(newControl.getEndPos()) ) {
                    result.push(newControl.getNewControlName());
                } else if ( true === bOverLay && 1 === endPos.compare(newControl.getStartPos()) ) {
                    result.push(newControl.getNewControlName());
                }
            }

            const leafListNames = newControl.getLeafListNamesByPos(newControl.getStartPos(), endPos, bOverLay);

            for (const name of leafListNames) {
                result.push(name);
            }
        }

        return result;
    }

    /**
     * 删除子节点
     * @param startPos
     * @param endPos
     */
    public deleteLeaf( startPos: ParagraphContentPos, endPos: ParagraphContentPos ): void {
        const count = this.leafList.length;

        if ( 0 === count ) {
            return ;
        }

        const newControlStartPos = this.getStartPos();
        const newControlEndPos = this.getEndPos();

        if ( 1 === startPos.compare(endPos) || 1 === startPos.compare(newControlEndPos)
            || -1 === endPos.compare(newControlStartPos)
            || ( 1 !== startPos.compare(newControlStartPos) && 1 === endPos.compare(newControlEndPos))) {
            return ;
        }

        const startIndex = findLowerBound(this.leafList, startPos);
        let endIndex = findLowerBound(this.leafList, endPos);

        // startIndex != -1 : newControl中有子节点
        if ( -1 < startIndex && startIndex < count ) {
            const newControl = this.leafList[startIndex];
            if ( newControl ) {
                const pos = newControl.getEndPos();
                if ( 1 !== startPos.compare(pos) ) {
                    newControl.deleteLeaf(startPos, endPos);
                }
            }
        }

        const history = this.getHistory();
        let cascadeManager: CascadeManager;
        if ( startIndex < endIndex && endIndex < count ) {
            const newControl = this.leafList[endIndex];
            if ( newControl ) {
                const aStartPos = newControl.getStartPos();
                const aEndPos = newControl.getEndPos();

                if ( -1 !== aStartPos.compare(startPos) && -1 === aEndPos.compare(endPos) ) {
                    // const leafList = newControl.getLeafList();
                    // if ( leafList && 1 <= leafList.length ) {
                    //     if ( history ) {
                    //         history.addChange(new ChangeNewControlRemoveLeafItems(newControl, leafList, []));
                    //     }
                    //     leafList.splice(0, leafList.length);
                    // }

                    const pos = this.leafList.indexOf(newControl);
                    cascadeManager = this.getCascadeManager();
                    if (cascadeManager) {
                        cascadeManager.deleteCascade(newControl);
                    }
                    this.getNewControlManager()?.
                            removeSourceData(newControl);
                    this.leafList.splice(pos, 1);
                    if ( history ) {
                        history.addChange(new ChangeNewControlRemoveItem(newControl, pos, this));
                    }
                } else if ( 1 !== startPos.compare(aEndPos) ) {
                    newControl.deleteLeaf(startPos, endPos);
                }
            }
        }
        if ( endIndex >= count ) {
            endIndex = count - 1;
        }

        for (let pos = endIndex - 1; pos >= startIndex + 1; pos--) {
            const newControl = this.leafList[pos];
            if ( history ) {
                history.addChange(new ChangeNewControlRemoveItem(newControl, pos, this));
            }
        }
        cascadeManager = this.getCascadeManager();
        if (cascadeManager) {
            cascadeManager.deleteEachNewControl(this.leafList.slice(startIndex + 1, endIndex));
        }
        this.leafList.splice(startIndex + 1, endIndex - startIndex - 1);
    }

    /**
     * 删除指定newcontrol
     * @param delNewControl
     */
    public remove( delNewControl: NewControl ): void {
        if ( null == delNewControl ) {
            return ;
        }

        const startPos = delNewControl.getStartPos();
        const endPos = delNewControl.getEndPos();

        const startIndex = findLowerBound(this.leafList, startPos);
        const endIndex = findLowerBound(this.leafList, endPos);

        const count = this.leafList.length;

        const history = this.getHistory();

        if ( -1 < endIndex && endIndex < count ) {
            const newControl = this.leafList[endIndex];

            if ( delNewControl.getNewControlName() === newControl.getNewControlName() ) {
                // const leafList = newControl.getLeafList();
                // if ( leafList && 1 <= leafList.length ) {
                //     if ( history ) {
                //         history.addChange(new ChangeNewControlRemoveLeafItems(newControl, leafList, []));
                //     }
                //     leafList.splice(0, leafList.length);
                // }

                const pos = this.leafList.indexOf(newControl);
                this.leafList.splice(pos, 1);
                if ( history ) {
                    history.addChange(new ChangeNewControlRemoveItem(newControl, pos, this));
                }
            } else if (  1 === startPos.compare(newControl.getStartPos())
                        && -1 === endPos.compare(newControl.getEndPos() ) ) {
                newControl.remove(delNewControl);
            }
        }
    }

    /**
     * 获取光标位置，或者指定位置所在的newControl
     * @param contentPos
     */
    public getPosNewControl( contentPos: ParagraphContentPos ): NewControl {
        if ( -1 === contentPos.compare(this.getStartPos() )
            || 1 === contentPos.compare(this.getEndPos() )) {
            return null;
        }

        const pos = findLowerBound(this.leafList, contentPos);

        if ( -1 === pos ) {
            return this;
        } else {
            const newControl = this.leafList[pos];
            if ( newControl && 1 !== contentPos.compare(newControl.getEndPos() ) ) {
                return newControl.getPosNewControl(contentPos);
            } else {
                return this;
            }
        }
    }

    public getNewControlNamesByPos( startPos: ParagraphContentPos, endPos: ParagraphContentPos,
                                    bOverLay: boolean = false, bLeaf: boolean = true): string[] {
        const result: string[] = [];

        // const startNewControl = this.getPosNewControl(startPos);
        // const endNewControl = this.getPosNewControl(endPos);
        const startIndex = findLowerBound(this.leafList, startPos);
        const endIndex = findLowerBound(this.leafList, endPos);

        for (let index = startIndex + 1; index <= endIndex; index++) {
            const element = this.leafList[index];
            if ( element ) {
                result.push(element.getNewControlName());
            }
        }

        return result;
    }

    public getId(): number {
        return this.id;
    }

    public setId( id: number ): void {
        if ( id === this.id ) {
            return ;
        }

        this.id = id;
    }

    public isMultiRadio(): boolean {
        return this.getType() === NewControlType.MultiRadio;
    }

    public isMultiAndRadio(): boolean {
        const type = this.getType();
        if ( type === NewControlType.MultiRadio || type === NewControlType.RadioButton) {
            return true;
        }

        return false;
    }

    /**
     * 获取newControl类型
     */
    public getType(): NewControlType {
        return this.content.getType();
    }

    /**
     * 设置newControl类型
     */
    public setType( type: number ): number {
        if ( type === this.getType() ) {
            return ResultType.UnEdited;
        }

        this.content.setType(type);
        return ResultType.Success;
    }

    /**
     * 是否编辑保护
     */
    public isEditProtect(): boolean {
        return this.bCanntEdit;
    }

    public setEditProtect( bCanntEdit: boolean ): number {
        if ( bCanntEdit === this.bCanntEdit || null == bCanntEdit ) {
            return ResultType.UnEdited;
        }

        const history = this.getHistory();
        if ( history ) {
            history.addChange(new ChangeNewControlCanntEdit(this, this.bCanntEdit, bCanntEdit));
        }
        this.bCanntEdit = bCanntEdit;
        this.setDirty();
        return ResultType.Success;
        // this.content.setEditProtect(bCanntEdit, this.parent);
    }

    public isDeleteProtect(): boolean {
        return this.bCanntDelete;
    }

    public setDeleteProtect( bCanntDelete: boolean ): number {
        if ( bCanntDelete === this.bCanntDelete || null == bCanntDelete ) {
            return ResultType.UnEdited;
        }

        const history = this.getHistory();
        if ( history ) {
            history.addChange(new ChangeNewControlCanntDelete(this, this.bCanntDelete, bCanntDelete));
        }
        this.bCanntDelete = bCanntDelete;
        this.setDirty();

        return ResultType.Success;
        // this.content.setDeleteProtect(bCanntDelete, this.parent);
    }

    public isCopyProtect(): boolean {
        return this.bCanntCopy;
    }

    public setCopyProtect( bCanntCopy: boolean ): number {
        if ( bCanntCopy === this.bCanntCopy || null == bCanntCopy ) {
            return ResultType.UnEdited;
        }

        const history = this.getHistory();
        if ( history ) {
            history.addChange(new ChangeNewControlCanntCopy(this, this.bCanntCopy, bCanntCopy));
        }
        this.bCanntCopy = bCanntCopy;
        this.setDirty();

        return ResultType.Success;
    }

    public isShowBgColor(): boolean {
        return this.bShowBgColor;
    }

    public setShowBgColor( bShowBgColor: boolean ): number {
        if (bShowBgColor === this.bShowBgColor || typeof bShowBgColor !== 'boolean') {
            return ResultType.UnEdited;
        }
        this.bShowBgColor = bShowBgColor;
        return ResultType.Success;
    }

    public isHiddenBackground(): boolean {
        return this.bHiddenBackground;
    }

    public setHiddenBackground( bHiddenBackground: boolean ): number {
        if ( bHiddenBackground === this.bHiddenBackground || null == bHiddenBackground ) {
            return ResultType.UnEdited;
        }

        const history = this.getHistory();
        if ( history ) {
            history.addChange(new ChangeNewControlHiddenBackground(this, this.bHiddenBackground, bHiddenBackground));
        }
        this.bHiddenBackground = bHiddenBackground;
        this.setDirty();

        return ResultType.Success;
    }

    public getBackgroundColor(): string {
        let color = null;

        if ( true !== this.bHiddenBackground ) {
            color = NewControlDefaultSetting.DefaultBackgroundColor ;
        }

        return color;
    }

    public getFocusHighLightColor(): string {
        return NewControlDefaultSetting.DefaultFocusHighLightColor;
    }

    public isReverseEdit(): boolean {
        return this.bReverseEdit;
    }

    public isReverseEdit2(): boolean {
        if ( this.bCanntEdit ) {
            return false;
        } else if ( this.bReverseEdit ) {
            return true;
        }

        let parentCount = 0;
        let parentControl = this.parentControl;
        const parentSum = 10;

        while ( parentControl ) {
            if ( parentControl.isEditProtect() ) {
                return false;
            } else if (parentControl.isReverseEdit()) {
                return true;
            }

            parentControl = parentControl.parentControl;
            parentCount++;

            if ( parentSum <= parentCount ) {
                break;
            }
        }

        return false;
    }

    public setErrorTextBgColor(type: number = 1): void {
        return;
    }

    public getNewPosition2(): {x: number, y: number} {
        if (this.isShowBorder() === true) {
            const startBorderPortion = this.getStartBorderPortion();
            const text = startBorderPortion.content[0];
            return {x: text.positionX + 2, y: text.positionY - startBorderPortion.textHeight};
        }

        return this.getNewPosition();
    }

    public getNewPosition(): {x: number, y: number} {
        if (this.isShowBorder() === true) {
            return;
        }

        const startBorderPortion = this.getStartBorderPortion();
        let contents: any[] = startBorderPortion.content;
        if (contents.length > 1 && this.getTitle()) {
            for (let index = 1, len = contents.length; index < len; index++) {
                const content = contents[index];
                if (content.content) {
                    const borderY = contents[0].positionY;
                    if (content.positionY - borderY < 0.01 ) {
                        return;
                    }
                    return {x: content.positionX, y: borderY};
                }
            }
            const text = contents[1];
            return {x: text.positionX, y: text.positionY};
        }
        contents = startBorderPortion.paragraph.content;
        const actIndex = contents.findIndex((item) => item === startBorderPortion);
        if (actIndex === -1) {
            return;
        }
        for (let curIndex = actIndex + 1, length = contents.length; curIndex < length; curIndex++) {
            const content = contents[curIndex] as ParaPortion;
            if (content.content.length !== 0) {
                const text = content.content[0];
                const borderY = startBorderPortion.content[0].positionY;
                if (text.positionY - borderY < 0.01 ) {
                    return;
                }
                return {x: text.positionX, y: borderY};
            }
        }

        return;
    }

    public getPosition(type: MessageType): {x: number, y: number, pageNum: number} {
        let text: ParaElementBase;
        let portion: ParaPortion;
        let subWidth: number = 0;
        switch (type) {
            case MessageType.EditTitle:
            case MessageType.DeleteTitle: {
                if (!this.getTitle()) {
                    return;
                }
                portion = this.getStartBorderPortion();
                text = portion.content[1];
                break;
            }
            case MessageType.UnDelete:
            case MessageType.UnEdited: {
                portion = this.getCurParaPortion();
                text = portion.getCurrentText();
                break;
            }
            case MessageType.MaxLength: {
                if (!this.isNewTextBox()) {
                    return;
                }
                // bRefresh = (this as any).setMaxLengthBgColor('#FF3143');
                portion = this.getStartBorderPortion();
                text = portion.content[portion.content.length - 1];
                subWidth = text.widthVisible;
                break;
            }
            case MessageType.SubSignTips:
            case MessageType.NewControlFocus:
            case MessageType.GroupCheckBox: {
                portion = this.getStartBorderPortion();
                text = portion.content[0];
                break;
            }
            default: {
                return;
            }
        }

        if (!text) {
            return;
        }

        const startPos = new ParagraphContentPos();
        startPos.clear();
        portion.getParentPos(startPos);
        const textIndex = portion.content.findIndex((item) => item === text);
        startPos.add(textIndex);
        const topIndex = this.getDocumentSectionType();
        startPos.splice(0, 0, [topIndex]);
        const pageNum = portion.paragraph.getCurrentPageByPos(startPos);

        return {pageNum, x: text.positionX + subWidth, y: text.positionY - portion.textHeight};
    }

    public getCurParaPortion(): ParaPortion {
        const parent = this.getParagraph();
        return parent.getCurParaPortion();
    }

    /**
     * 文件插入缓存级联数据，插入完会进行清空
     * @param cascades 级联数据
     */
    public setCascades1(cascades: ICascade[]): void {
        this.cascades = cascades;
    }

    public setCascades(cascades: ICascade[], bClearItems: boolean = true): number {
        if (!cascades) {
            return ResultType.UnEdited;
        }

        const cascadeManager = this.getCascadeManager();
        if (!cascadeManager) {
            return ResultType.Failure;
        }

        return cascadeManager.setCascades(this, cascades, bClearItems);
    }

    public contentChanged(fromType?: number): void {
        //
        // console.log(this)
    }

    /**
     * 插入文件时缓存级联配置数据
     */
    public getCascades1(): ICascade[] {
        return this.cascades;
    }

    public getCascades(): ICascade[] {
        const cascadeManager = this.getCascadeManager();
        if (!cascadeManager) {
            return;
        }

        return cascadeManager.getCascades(this);
    }

    public setEvent(event: any): number {
        return ResultType.UnEdited;
    }

    public getEvent(): any {
        return;
    }

    public updateEventSource(): void {
        const cascadeManager = this.getCascadeManager();
        if (!cascadeManager) {
            return;
        }

        return cascadeManager.updateEventSource(this);
    }

    public triggerCascade(): boolean {
        const cascadeManager = this.getCascadeManager();
        if (!cascadeManager) {
            return false;
        }
        return cascadeManager.triggerCascade(this);

        // const res = this.triggerEvent();
        // const datas = this.getCascades();
        // if (!datas || datas.length === 0) {
        //     return res;
        // }

        // return this.getDocumentParent()
        // .getNewControlManager()
        // .triggerCascade(this.name) || res;
    }

    public triggerGroup(): boolean {
        return this.getDocumentParent() ? this.getDocumentParent()
                    .getNewControlManager()
                        .triggerGroup(this) : false;
    }

    public triggerEvent(): boolean {
        const cascadeManager = this.getCascadeManager();
        if (!cascadeManager) {
            return false;
        }
        return cascadeManager.triggerEvent(this);

        // if (!this.getTriggetEventInfo()) {
        //     return false;
        // }

        // return this.getDocumentParent()
        // .getNewControlManager()
        // .triggerEvent(this.name);
    }

    public setReverseEdit( bReverseEdit: boolean ): number {
        if ( bReverseEdit === this.bReverseEdit || null == bReverseEdit ) {
            return ResultType.UnEdited;
        }

        const history = this.getHistory();
        if ( history ) {
            history.addChange(new ChangeNewControlReverseEdit(this, this.bReverseEdit, bReverseEdit));
        }
        this.bReverseEdit = bReverseEdit;
        this.setDirty();

        return ResultType.Success;
    }

    /**
     * 区域是否已经被隐藏，隐藏之后就不会展示
     * 父级区域是否已被隐藏
     */
    public isHiddenAtParent(bSource: boolean = false): boolean {
        if (this.bHidden) {
            return true;
        }

        let parentNewControl = this.parentControl;
        while (parentNewControl) {
            if (parentNewControl.isHidden()) {
                return true;
            }
            parentNewControl = parentNewControl.parentControl;
        }

        let parent: any = this.getDocumentParent();
        while (parent && parent instanceof DocumentContent) {
            if (parent.isTableCellContent()) {
                const row = parent.getTableRow();
                if (row && row.isFixed() && 0 >= row.getHeight().value) {
                    return true;
                }

                parent = (parent.parent as any).row.table;
            } else if (parent.isRegionContent()) {
                parent = parent.parent as any;
            } else {
                return false;
            }

            if (parent.isHidden(bSource)) {
                return true;
            }

            parent = parent.parent;
        }
        return false;
    }

    public isHidden(): boolean {
        return this.bHidden;
    }

    public isHidden2(): boolean {
        if (this.bHidden) {
            return true;
        }

        let parentNewControl = this.parentControl;
        while (parentNewControl) {
            if (parentNewControl.isHidden()) {
                return true;
            }
            parentNewControl = parentNewControl.parentControl;
        }

        return false;
    }

    public setNewContentHidden(): number {
        const bHidden = this.bHidden;
        if (!bHidden) {
            return ResultType.UnEdited;
        }
        return this.content.setContentHidden(bHidden);
    }

    public setLeafHide(leafs: NewControl[]): void {
        for (const leaf of leafs) {
            if (leaf.isHidden()) {
                leaf.setNewContentHidden();
            } else if (leaf.leafList.length) {
                this.setLeafHide(leaf.leafList);
            }
        }
    }

    public setHidden( bHidden: boolean, bUpdateCurPos: boolean = true ): number {
        if ( bHidden === this.bHidden || null == bHidden ) {
            return ResultType.UnEdited;
        }

        const history = this.getHistory();
        if ( history ) {
            history.addChange(new ChangeNewControlHidden(this, this.bHidden, bHidden));
        }
        this.bHidden = bHidden;
        const parent = this.getParent();
        if (parent && parent.isHidden2()) {
            return ResultType.Success;
        }
        const bNewSection = this.isNewSection();
        if (bNewSection) {
            this.content.setHidden(bHidden, bNewSection);
            if (bHidden === false) {
                const startPara = this.getStartBorderPortion().paragraph;
                // 修复节部分显示后，段落还是不显示的bug
                if (startPara.getHidden() === true ) {
                    startPara.setHidden(bHidden);
                }
            }
            this.setLeafHide(this.leafList);
        } else {
            this.content.setHidden(bHidden, bNewSection); 
        }
        if (bUpdateCurPos === true && bHidden === true) {
            const parent = this.getDocumentParent();
            const currentIndex = parent.curPos.contentPos;
            const startBorder = this.getStartBorderPortion();
            let para = startBorder.paragraph;
            let paraIndex: number = para.index;
            const endBorder = this.getEndBorderPortion();
            const endBorderIndex = endBorder.paragraph.index;
            if (currentIndex >= paraIndex && currentIndex <= endBorderIndex) {
                if (!para.isHidden()) {
                    const actIndex = para.content.findIndex((item) => item === startBorder);
                    const portionIndex = Math.max(0, actIndex - 1);
                    para.curPos.contentPos = portionIndex;
                    // para.content[portionIndex].portionContentPos = 0;
                    const portion = para.content[portionIndex];
                    let len = 0;
                    if (portionIndex !== actIndex) {
                        len = portion.content.length;
                    }
                    portion.portionContentPos = len;
                } else {
                    // const endBorder = this.getEndBorderPortion();
                    para = endBorder.paragraph;
                    if (!para.isHidden()) {
                        const portionIndex = Math.min(para.content.findIndex((portion) => portion === endBorder) + 1,
                        para.content.length - 1);
                        para.curPos.contentPos = portionIndex;
                        para.content[portionIndex].portionContentPos = 0;
                        paraIndex = para.index;
                    } else {
                        let curParaIndex = parent.getPrevElementIndex(paraIndex);
                        if (curParaIndex === paraIndex) {
                            paraIndex = para.index;
                            curParaIndex = parent.getNextElementIndex(paraIndex);
                            if (paraIndex === curParaIndex) { // 所有的元素都隐藏了
                                // const endIndex = para.content.findIndex((portion) => portion === endBorder);
                                para = startBorder.paragraph;
                                para.setHidden(false); // 只能设置第一个元素为不隐藏？？因为设置最后一个时，输入会被隐藏
                                const startIndex = para.content.findIndex((item) => item === startBorder);
                                para.setNewControlTextHidden(true, startIndex, null);
                            } else {
                                para = parent.content[curParaIndex] as any;
                            }
                            para.moveCursorToStartPos(false, false); // 光标移动到前面
                        } else {
                            para = startBorder.paragraph;
                            para = parent.content[curParaIndex] as any;
                            para.moveCursorToEndPos(false, false);
                        }
                        paraIndex = para.index;
                    }
                }

                parent.curPos.contentPos = paraIndex;
            }
            parent.updateCursorXY();
        }
        this.setDirty();

        return ResultType.Success;
    }

    public addContent(): void {
        return;
    }

    public setAlignments(type: AlignType): number {
        return;
    }

    public getAlignments(): AlignType {
        return;
    }

    public isShowBorder(): boolean {
        return this.bShowBorder;
    }

    public setShowBorder( bShowBorder: boolean ): number {
        if ( bShowBorder === this.bShowBorder || null == bShowBorder ) {
            return ResultType.UnEdited;
        }

        const history = this.getHistory();
        if ( history ) {
            history.addChange(new ChangeNewControlShowBorder(this, this.bShowBorder, bShowBorder));
        }
        this.bShowBorder = bShowBorder;
        this.content.setShowBorder(bShowBorder);
        this.setDirty();

        return ResultType.Success;
    }

    public isShowValue(): boolean {
        return false;
    }

    public getAllDate(): string {
        return '';
    }

    public setShowValue(bShowValue: boolean): number {
        return ResultType.UnEdited;
    }

    public isViewSecret(): boolean {
        return this.viewSecretType !== NewControlContentSecretType.DontSecret;
    }

    public getViewSecretType(): NewControlContentSecretType {
        return this.viewSecretType;
    }

    public setViewSecret( viewSecretType: NewControlContentSecretType ): number {
        if ( viewSecretType === this.viewSecretType || null == viewSecretType ) {
            return ResultType.UnEdited;
        }

        if ( NewControlType.TextBox !== this.getType() && NewControlType.NumberBox !== this.getType() ) {
            return ResultType.UnEdited;
        }

        const history = this.getHistory();
        if ( history ) {
            history.addChange(new ChangeNewControlViewSecretType(this, this.viewSecretType, viewSecretType));
        }

        this.viewSecretType = viewSecretType;

        this.setDirty();
        if ( true === this.content.hasOnlyPlaceHolderContent() ) {
            return ResultType.Success;
        }

        this.content.setViewSecret(this.viewSecretType);
        return ResultType.Success;
    }

    /**
     * 判断指定区域是否可以删除
     * @param startPos 开始位置
     * @param endPos 结束位置
     */
    public isValidDelete( startPos: ParagraphContentPos, endPos: ParagraphContentPos,
                          newControlNames?: string[] ): boolean {
        let result = true;
        const doc = this.getDocumentParent() ? this.getDocumentParent()
                                .getDocument() : null;
        const bAdminMode = doc ? doc.isAdminMode() : false;

        const startIndex = findLowerBound(this.leafList, startPos);
        const endIndex = findLowerBound(this.leafList, endPos);
        const newControlStartPos = this.getStartPos();
        const newControlEndPos = this.getEndPos();

        if ( 0 === startPos.compare(endPos) ) {
            if ( 1 === startPos.compare(newControlEndPos) ) {
                return result;
            } else if ( 1 === startPos.compare(newControlStartPos) && 1 !== startPos.compare(newControlEndPos)) {
                if ( this.isEditProtect() && !bAdminMode ) {
                    result = false;
                    if ( null != newControlNames ) {
                        newControlNames.push(this.name);
                    }
                }

                const count = this.getLeafCount();

                if ( -1 !== startIndex && startIndex < count ) {
                    const newControl = this.leafList[startIndex];
                    if ( newControl ) {
                        result = newControl.isValidDelete(startPos, endPos, newControlNames);
                    }
                }

                if ( startIndex + 1 < count ) {
                    const newControl = this.leafList[startIndex + 1];
                    result = newControl.isValidDelete(startPos, endPos, newControlNames);
                }
            }
        } else {
            const type = this.getCursorSelType(startPos, endPos);
            const count = this.getLeafCount();

            if ( CursorSelType.ForwardOverNewControl === type && ( this.getTitle() && 1 <= this.getTitle().length ) ) {
                if ( null != newControlNames ) {
                    newControlNames.push(this.name);
                }
                return false;
            }
            let bContainBorder = false;

            switch (type) {
                case CursorSelType.ForwardOverNewControl:
                case CursorSelType.BehindOverNewControl:
                    bContainBorder = true;
                case CursorSelType.InsideNewControl: {
                    if ( true === this.isEditProtect() && !bAdminMode ) {
                        result = false;
                        if ( null != newControlNames ) {
                            newControlNames.push(this.name);
                        }

                        if ( true === this.isNewSection() ) {
                            if (!this.getLeafList()?.length) {
                                return result;
                            } else {
                                if (-1 === startIndex) {
                                    return result;
                                } else if (1 === endPos.compare(
                                    this.leafList[this.leafList.length - 1].getEndPos())) {
                                    return result;
                                }
                            }
                        }
                    }

                    if (bContainBorder && !(this.isDeleteProtect() && !bAdminMode)
                        && this.canDeleteNewControl(startPos, endPos, type)) {
                        result = true;
                    }

                    // if ( true === this.content.placeHolder.bPlaceHolder ) {
                    //     result = false;
                    // }

                    if ( -1 !== startIndex && startIndex < count ) {
                        const newControl = this.leafList[startIndex];
                        if ( newControl ) {
                            result = newControl.isValidDelete(startPos, endPos, newControlNames);
                        }
                    }

                    for (let index = startIndex + 1; index <= endIndex && index < count; index++) {
                        const newControl = this.leafList[index];
                        if ( newControl ) {
                            result = newControl.isValidDelete(startPos, endPos, newControlNames);
                        }

                        if ( false === result ) {
                            if ( null != newControlNames ) {
                                newControlNames.push(this.name);
                            }
                            break;
                        }
                    }

                    break;
                }

                case CursorSelType.LayOverNewControl: {
                    if ( this.isDeleteProtect() && !bAdminMode ) {
                        result = false;
                    } else {
                        for (let index = 0; index < count; index++) {
                            const newControl = this.leafList[index];
                            if ( newControl ) {
                                result = newControl.isValidDelete(startPos, endPos, newControlNames);
                            }

                            if ( false === result ) {
                                if ( null != newControlNames ) {
                                    newControlNames.push(this.name);
                                }
                                break;
                            }
                        }
                    }
                    break;
                }

                default:
                    break;
            }
        }

        return result;
    }

    /**
     * 判断当前newcontrol的内容可以删除：下拉框的内容是不能删除或输入的
     * @param startPos 开始位置
     * @param endPos 结束位置
     */
    public isValidDeleteSpecialNewCtrls( startPos: ParagraphContentPos, endPos: ParagraphContentPos,
                                         newControlNames?: string[] ): boolean {
        let result = true;

        const startIndex = findLowerBound(this.leafList, startPos);
        const endIndex = findLowerBound(this.leafList, endPos);
        const type = this.getCursorSelType(startPos, endPos);
        const count = this.getLeafCount();

        switch (type) {
            case CursorSelType.ForwardOverNewControl:
            case CursorSelType.BehindOverNewControl:
            case CursorSelType.InsideNewControl: {
                result = !this.isSpecialNewControl();

                if ( 0 === count ) {
                    return result;
                }

                for (let index = startIndex; index <= endIndex && index < count; index++) {
                    const newControl = this.leafList[index];
                    if ( newControl ) {
                        result = newControl.isValidDeleteSpecialNewCtrls(startPos, endPos, newControlNames);
                    }

                    if ( false === result ) {
                        if ( null != newControlNames ) {
                            newControlNames.push(this.name);
                        }
                        break;
                    }
                }

                break;
            }

            default:
                break;
        }

        return result;
    }

    /**
     * 插入占位符：先删除newcontrol的内容，再插入占位符
     * @param bMustInput 是否为必填项
     */
    public addPlaceHolderContent(): boolean {
        // const startParaIndex = this.content.getStartBorder().paraIndex;
        // const para = this.getDocumentParent().content[startParaIndex] as Paragraph;

        this.content.addPlaceHolderContent(this.bMustInput);
        return true;
    }

    /**
     * 删除占位符或者内容
     */
    public removePlaceHolderContent(): void {
        this.content.removePlaceHolderContent();
    }

    /**
     * 设置占位符内容
     * @param placeHolderContent 占位符内容
     */
    public setPlaceHolderContent( placeHolderContent: string ): number {
        if ( this.content.getPlaceHolderContent() === placeHolderContent || null == placeHolderContent ) {
            return ResultType.UnEdited;
        }

        const history = this.getHistory();
        if ( history ) {
            history.addChange(new ChangeNewControlPlaceHolderContent(this,
                this.content.getPlaceHolderContent(), placeHolderContent));
        }
        this.setDirty();
        return this.content.setPlaceHolderContent(placeHolderContent, this.bMustInput);
    }

    public getPlaceHolder(): ParaPortion {
        return this.content.getPlaceHolder();
    }

    public getSerialNumber(): string {
        return this.serialNumber;
    }

    public setSerialNumber(serialNum: string): number {
        if ( serialNum === this.serialNumber || null == serialNum ) {
            return ResultType.UnEdited;
        }

        const history = this.getHistory();
        if ( history ) {
            history.addChange(new ChangeNewControlSerialNumber(this, this.serialNumber, serialNum));
        }
        this.serialNumber = serialNum;
        this.setDirty();

        return ResultType.Success;
    }

    /**
     * 读取文档使用：设置内容是否只有占位符
     */
    public setPlaceHolder(bPlaceHolder: boolean): void {
        const startBorderPortion = this.content.getStartBorderPortion();
        const endBorderPortion = this.content.getEndBorderPortion();

        if ( true === bPlaceHolder ) {
            const placeHolderPortion = this.content.getPlaceHolder();
            let textColor = placeHolderPortion.textProperty.color;
            if (textColor == null) {
                textColor = true !== this.bMustInput ?
                    NewControlDefaultSetting.DefaultTextColor : NewControlDefaultSetting.DefaultMustInputTextColor;
            }
        }

        startBorderPortion.setPlaceHolder1(bPlaceHolder);
        endBorderPortion.setPlaceHolder1(bPlaceHolder);
    }

    /**
     * 获取占位符内容
     */
    public getPlaceHolderContent(): string {
        return this.content.getPlaceHolderContent();
    }

    public setNewControlTextByJson(json: IStructParamJson): number {
        return ResultType.UnEdited;
    }

    public setNewControlText( sText: string, bRefresh?: boolean): number {
        if ( ( null == sText || '' === sText ) && this.content.hasOnlyPlaceHolderContent() ) {
            return ResultType.UnEdited;
        }

        sText = skipEscapeString(sText);
        const type = this.getType();
        if ( NewControlType.TextBox !== type && NewControlType.Section !== type) {
            sText = filterChars(sText);
        }

        // const history = this.getDocumentParent()
        //                 .getHistory();
        // history.createNewHistoryPoint(HistoryDescriptionType.DocumentSetNewControlText);
        const res = this.content.setNewControlText(sText, this.bMustInput,
                        this.viewSecretType, this.isHidden2(), bRefresh);
        if (res === ResultType.Success) {
            this.setNewContentHidden();
            this.setTextFlag(true);
            this.triggerCascade();
            this.setDirty();
        }
        return res;
    }

    public setNewControlText2(text: string): void {
        this.content.setNewControlText2(text, this.bMustInput, this.viewSecretType, this.isHidden2());
        this.triggerCascade();
    }

    public setNewControlImage(image: ParaDrawing): number {
        const result = this.content.setNewControlImage(image, this.bMustInput, this.viewSecretType, this.isHidden2());
        if (result === ResultType.Success) {
            this.setTextFlag(true);
            this.setDirty();
        }
        return result;
    }

    public getAllImagesName(): string[] {
        return this.content.getAllImagesName();
    }

    public setNewControlMixData(datas: any): number {
        const result = this.content.setNewControlMixData(datas, this.bMustInput, this.viewSecretType, this.isHidden2());
        if (result === ResultType.Success) {
            this.setTextFlag(true);
            this.setDirty();
        }
        return result;
    }

    public getNewControlText(): string {
        return this.content.getNewControlText();
    }

    public getNewControlTextAI(): string {
        return this.content.getNewControlTextAI();
    }

    /**
     * 不包含标题、不输出占位符等
     * @returns 结构化元素的text
     */
    public getNewControlText2(): string {
        if (this.isPlaceHolderContent()) {
            return '';
        }
        let res = this.content.getNewControlText();
        const title = this.getTitle();
        if (res) {
            if (title) {
                res = res.replace(new RegExp('^' + title), '');
            }
            const unit = this.getUnit();
            if (unit) {
                res = res.replace(new RegExp(unit + '$'), '');
            }
        }

        return res;
    }

    public getNewControlText3(): string {
        return this.getNewControlText2();
    }

    public getNewControlTextLength(bParaEnd: boolean = false): number {
        return this.content.getNewControlTextLength(bParaEnd);
    }

    /**
     * 删除newControl内容：只有占位符，则不进行删除
     */
    public removeNewControlContent(bRefresh: boolean = true): void {
        this.content.removeContent(bRefresh);
    }

    /**
     * newControl是否只读
     */
    public isReadOnly(): boolean {
        // let result = false;

        // if ( true === this.isEditProtect() ) {
        //     result = true;
        // } else if ( false === this.isReverseEdit() ) {
        //     if ( true === this.isReallyReadOnly() ) {
        //         result = true;
        //     } else {
        //         // todo;
        //     }
        // }
        // return result;

        const doc = this.getDocumentParent() ? this.getDocumentParent()
                                .getDocument() : null;
        const bAdminMode = doc ? doc.isAdminMode() : false;
        if ( bAdminMode ) {
            return false;
        }

        if (doc?.getProtectHeaderFooter() &&
            (DocumentSectionType.Document !== this.getDocumentSectionType())) {
            return true;
        }

        if ( this.bCanntEdit ) {
            return true;
        } else if ( this.bReverseEdit ) {
            return false;
        }

        let parentCount = 0;
        let parentControl = this.parentControl;
        const parentSum = 10;

        while ( parentControl ) {
            if ( parentControl.isEditProtect() ) {
                return true;
            } else if (parentControl.isReverseEdit()) {
                return false;
            }

            parentControl = parentControl.parentControl;
            parentCount++;

            if ( parentSum <= parentCount ) {
                break;
            }
        }

        const parentElement = this.getDocumentParent();
        if (parentElement instanceof DocumentContent) {
            const table = parentElement.getTable();
            if ( table ) {
                const cell = parentElement.getTableCell();
                if ( cell && cell.isNewControlReadOnly() ) {
                    return true;
                }

                if ( table.isCellSelection() && table.isNewControlReadOnly() ) {
                    return true;
                }
            }

            const region = parentElement.getRegion();
            if (region) {
                return region.isReadOnly();
            }
        }

        return false;
    }

    // /**
    //  * newControl是否只读，并考虑父属性
    //  */
    // public isReallyReadOnly(): boolean {
    //     let parent = this.parentControl;

    //     while ( null != parent ) {
    //         if ( parent.isEditProtect() ) {
    //             return true;
    //         } else {
    //             parent = parent.parentControl;
    //         }
    //     }

    //     return false;
    // }

    /**
     * 是否为文本元素
     */
    public isNewTextBox(): boolean {
        return this.getType() === NewControlType.TextBox;
    }

    /**
     * 是否为多选按钮
     */
    public isCheckBox(): boolean {
        return this.getType() === NewControlType.CheckBox;
    }

    /**
     * 是否为单选按钮
     */
    public isRadioButton(): boolean {
        return this.getType() === NewControlType.RadioButton;
    }

    /**
     * 是否为节
     */
    public isNewSection(): boolean {
        return this.getType() === NewControlType.Section;
    }

    /**
     * 是否为文本元素或节
     * @returns
     */
    public isTextBoxOrSection(): boolean {
        return (this.isNewTextBox() || this.isNewSection());
    }

    /**
     * 是否为元素(注：emr里的概念，数据元，里面不能敲回车的叫元素)
     */
    public isEmrStruct(bAddSign: boolean = false, bIgnoreSignElems: boolean = false): boolean {
        const type = this.getType();
        const nonStructs = [NewControlType.Empty, NewControlType.Region, NewControlType.Section,
            NewControlType.SignatureElement];
        if (bAddSign === true) {
            // 签名控件虽为元素，但没有占位符和边框设置，有时可以跳过查验
            nonStructs.push(NewControlType.SignatureBox);
        }

        if (bIgnoreSignElems === false) {
            // if it is substruct(newcontroltext) in newcontrolsignature, return false
            if (type === NewControlType.TextBox) {
                const parentControl = this.getParent();
                if (parentControl != null && parentControl.getType() === NewControlType.SignatureBox) {
                    return false;
                }
            }
        }

        return !nonStructs.includes(type);
    }

    /**
     * 是否为单选框
     */
    public isNewCombox(): boolean {
        return this.getType() === NewControlType.Combox;
    }

    public isAmongCombox(): boolean {
        const type = this.getType();
        if  (type === NewControlType.Combox || type === NewControlType.MultiCombox) {
            return true;
        }

        return false;
    }

    /**
     * 是否为多选框
     */
    public isNewMultiCombox(): boolean {
        return this.getType() === NewControlType.MultiCombox;
    }

    /**
     * 是否为单选下拉框
     */
    public isNewList(): boolean {
        return this.getType() === NewControlType.ListBox;
    }

    /**
     * 是否为多选下拉框
     */
    public isNewMultiList(): boolean {
        return this.getType() === NewControlType.MultiListBox;
    }

    public isMultiple(): boolean {
        const type = this.getType();
        if  (type === NewControlType.MultiListBox || type === NewControlType.MultiCombox) {
            return true;
        }

        return false;
    }

    /**
     * 是否为单选框/多选框 包含的类型 (5-8)
     */
    public isAmongComboxOrListStruct(): boolean {
        return this.isNewCombox() || this.isNewMultiCombox() || this.isNewList() || this.isNewMultiList();
    }

    public isNewDateBox(): boolean {
        return this.getType() === NewControlType.DateTimeBox;
    }

    public isMustInput(): boolean {
        return this.bMustInput;
    }

    public isNumberBox(): boolean {
        return this.getType() === NewControlType.NumberBox;
    }

    public isSignatureBox(): boolean {
        return this.getType() === NewControlType.SignatureBox;
    }

    public isAddressBox(): boolean {
        return this.getType() === NewControlType.AddressBox;
    }

    public setMustInput( bMustInput: boolean ): number {
        if ( bMustInput === this.bMustInput || null == bMustInput ) {
            return ResultType.UnEdited;
        }

        const history = this.getHistory();
        if ( history ) {
            history.addChange(new ChangeNewControlMustInput(this, this.bMustInput, bMustInput));
        }
        this.bMustInput = bMustInput;
        this.setDirty();
        this.content.setMustInput(this.bHidden, bMustInput);
        return ResultType.Success;
    }

    /**
     * 特殊的newControl元素，不允许用户进行输入、删除
     * @returns
     */
    public isSpecialNewControl(): boolean {
        const type = this.content.getType();
        if ( NewControlType.ListBox === type || NewControlType.MultiListBox === type
            || NewControlType.CheckBox === type || NewControlType.RadioButton === type
            || NewControlType.SignatureBox === type || NewControlType.MultiRadio === type
            || NewControlType.AddressBox === type || NewControlType.DateTimeBox === type) {
            return true;
        }

        return false;
    }

    /**
     * 设置newcontrol属性
     * @param property
     */
    public setProperty(property: INewControlProperty): number {
        let res = parseAndType(this.setNewControlName(property.newControlName));
        res = parseAndType(this.setTipsContent(property.newControlInfo)) & res;
        res = parseAndType(this.setPlaceHolderContent(property.newControlPlaceHolder)) & res;
        res = parseAndType(this.setTitle(property.newControlTitle)) & res;
        res = parseAndType(this.setSerialNumber(property.newControlSerialNumber)) & res;

        res = parseAndType(this.setHidden(property.isNewControlHidden)) & res;
        res = parseAndType(this.setDeleteProtect(property.isNewControlCanntDelete)) & res;
        res = parseAndType(this.setEditProtect(property.isNewControlCanntEdit)) & res;
        res = parseAndType(this.setCopyProtect(property.isNewControlCanntCopy)) & res;
        res = parseAndType(this.setShowBorder(property.isNewControlShowBorder)) & res;
        res = parseAndType(this.setMustInput(property.isNewControlMustInput)) & res;
        res = parseAndType(this.setReverseEdit(property.isNewControlReverseEdit)) & res;
        res = parseAndType(this.setHiddenBackground(property.isNewControlHiddenBackground)) & res;
        res = parseAndType(this.setViewSecret(property.newControlDisplayType)) & res;
        res = parseAndType(this.addCustomProps(property.customProperty, property.bClearItems)) & res;
        res = parseAndType(this.setCascades(property.cascade, property.bClearItems)) & res;
        res = parseAndType(this.setTabJump(property.tabJump)) & res;
        res = parseAndType(this.setIdentifier(property.identifier)) & res;
        res = parseAndType(this.setSourceDataBind(property.externalDataBind)) & res;
        return parseResultType(res);
    }

    public getProperty(): INewControlProperty {
        return {
            // dont forget
            newControlType: this.getType(),

            newControlName: this.getNewControlName(),
            newControlInfo: this.getTipsContent(),
            newControlPlaceHolder: this.getPlaceHolderContent(),
            newControlTitle: this.getTitle(),
            newControlSerialNumber: this.getSerialNumber(),

            isNewControlHidden: this.isHidden(),
            isNewControlCanntDelete: this.isDeleteProtect(),
            isNewControlCanntEdit: this.isEditProtect(),
            isNewControlCanntCopy: this.isCopyProtect(),
            isNewControlShowBorder: this.isShowBorder(),
            isNewControlMustInput: this.isMustInput(),
            isNewControlReverseEdit: this.isReverseEdit(),
            isNewControlHiddenBackground: this.isHiddenBackground(),
            newControlDisplayType: this.getViewSecretType(),
            customProperty: this.getCustomProps(),
            tabJump: this.isTabJump(),
        };
    }

    /**
     * 当前内容是否为占位符
     */
    public isPlaceHolderContent(): boolean {
        return this.content.hasOnlyPlaceHolderContent();
    }

    public setContentParent(parent: DocumentContentBase, history?: History): void {
        this.content.setParent(parent, history);
    }

    /**
     * 生产新的边框
     * @param para 边框所在段落
     * @param bStart 是否为左边框
     */
    public createNewControlBorder(para: Paragraph, bStart: boolean): ParaPortion {
        const portion = new ParaPortion(para);
        // const startBorder = this.content.getStartBorder();
        // const endBorder = this.content.getEndBorder();
        // const border = true === bStart ? startBorder.copy(true) : endBorder.copy(true);
        const bPopWinNewControl = this.isPopWindowNewControl();
        const bDefaultBorder = ( true !== this.isNewSection() );
        const border = new ParaNewControlBorder(this.name, bStart, bPopWinNewControl, this.bShowBorder, bDefaultBorder);

        border.setParaIndex(para.index);
        border.setPortionId(portion.id);
        portion.content.splice(0, 0, border);
        // portion.addToContent(0, border);

        if ( true === bStart && this.content.getEndBorderPortion() ) {
            const endBorderPortion = this.content.getEndBorderPortion();
            portion.textProperty = endBorderPortion.textProperty.copy();
            portion.bPlaceHolder = endBorderPortion.bPlaceHolder;
            portion.setReviewTypeWithInfo(endBorderPortion.getReviewType(), endBorderPortion.getReviewInfo());
        } else if ( false === bStart && this.content.getStartBorderPortion() ) {
            const startBorderPortion = this.content.getStartBorderPortion();
            portion.textProperty = startBorderPortion.textProperty.copy();
            portion.bPlaceHolder = startBorderPortion.bPlaceHolder;
            portion.setReviewTypeWithInfo(startBorderPortion.getReviewType(), startBorderPortion.getReviewInfo());
        }

        return portion;
    }

    /**
     * 设置边框
     * @param borderPortion 边框所在portion
     * @param bStart
     */
    public setBorder( borderPortion: ParaPortion, bStart: boolean ): void {
        const portion = true === bStart ? this.getStartBorderPortion() : this.getEndBorderPortion();

        const history = this.getHistory();
        if ( history ) {
            history.addChange(new ChangNewControlBorder(this, portion, borderPortion, bStart));
        }
        this.content.setBorder(borderPortion, bStart);
    }

    /**
     * 获取左边框
     */
    public getStartBorder(): ParaNewControlBorder {
        return this.content.getStartBorder();
    }

    /**
     * 获取右边框
     */
    public getEndBorder(): ParaNewControlBorder {
        return this.content.getEndBorder();
    }

    public getStartPageIndex(): number {
        return this.content.getStartPageIndex();
    }

    public getEndPageIndex(): number {
        return this.content.getEndPageIndex();
    }

    /**
     * 检查只含有占位符的newcontrol内的占位符内容是否完整
     */
    public checkPlaceHolderContent(): boolean {
        if ( true === this.content.hasOnlyPlaceHolderContent() ) {
            let placeHolderContent = this.content.getPlaceHolderContent();
            const placeHolder = this.content.getPlaceHolder();

            if ( this.isMustInput ) {
                placeHolderContent = NewControlDefaultSetting.DefaultMustInputChar + placeHolderContent;
                                    // + NewControlDefaultSetting.DefaultMustInputChar;
            }

            if ( placeHolder && placeHolderContent === placeHolder.getTextContent()) {
                return false;
            }

            return true;
        }

        const startContentPos = this.getStartPos(false);
        const startSearchPos = {
            pos: new ParagraphContentPos(),
            bFound: false,
            bSelection: false,
        };

        const startBorderPortion = this.getStartBorderPortion();
        // const startBorder = this.getStartBorder();
        const endBorder = this.getEndBorder();
        // const startParaIndex = startBorder.paraIndex;
        // this.parent.getParaIndexById(this.content.startBorder.paraId);
        const startPara = startBorderPortion.paragraph;

        startContentPos.update(startContentPos.get(1) + startBorderPortion.content.length, 1);
        startPara.getRightPos(startSearchPos, startContentPos);

        // console.log(startPara, startSearchPos)
        if ( true === startSearchPos.bFound ) {
            const portion = startPara.content[startSearchPos.pos.get(0)];
            const rightItem = portion.content[startSearchPos.pos.get(1) - 1] as ParaNewControlBorder;
            if ( rightItem && rightItem === endBorder
                && rightItem.getNewControlName() === endBorder.getNewControlName() ) {
                return true;
            // } else if ( true === this.content.getPlaceHolder().bPlaceHolder ) {
            //     return true;
            }
        } else {
            if ( startPara.index !== endBorder.paraIndex ) {
                const endContentPos = this.getEndPos(false);
                const endSearchPos = {
                    pos: new ParagraphContentPos(),
                    bFound: false,
                    bSelection: false,
                };

                // const endParaIndex = this.content.getEndBorder().paraIndex;
                // const endPara = this.getDocumentParent().content[endParaIndex] as Paragraph;
                const endPara = this.getEndBorderPortion().paragraph;

                endContentPos.update(endContentPos.get(1) + 1, 1);
                endPara.getLeftPos(endSearchPos, endContentPos);
                // console.log(endPara, endSearchPos)
                if ( false === endSearchPos.bFound) {
                    return true;
                }
            }
        }

        return false;
    }

    public getNewControlContent(): NewControlContent {
        return this.content;
    }

    /**
     * 将newcontrol插入段落
     * @param para 插入段落
     * @param curPos 插入位置
     */
    public addToParagragh( para: Paragraph, curPos: number ): void {
        const portion = para.content[curPos];

        let textProperty: any = {};
        const topDocument = (para == null ? null : para.getDocument());
        if (topDocument instanceof Document) {
            textProperty = new TextProperty(getDefaultFont(topDocument));
        } else {
            textProperty = new TextProperty();
        }

        if (portion != null && !portion.isRegionTitle(false) ) {
            textProperty = portion.textProperty.copy();
        }

        para.addToContent(curPos + 1, this.content.getEndBorderPortion());
        para.addToContent(curPos + 1, this.content.getPlaceHolder());
        para.addToContent(curPos + 1, this.content.getStartBorderPortion());

        if ( true === this.isPlaceHolderContent() ) {
            this.initPlaceHolderDefaultTextProperty(textProperty);
        } else {
            this.content.applyTextProperty(textProperty);
        }
        if (this.isHidden()) {
            this.bHidden = false;
            this.setHidden(true);
        }

        para.curPos.contentPos = curPos + 3;
    }

    /**
     * 设置整个元素的文本属性
     * @param textProperty
     */
    public applyTextProperty( textProperty: TextProperty ): number {
        return this.content.applyTextProperty(textProperty);
    }

    /**
     * 获取用户自定义属性
     */
    public getCustomProperty(): Map<string, ICustomProps> {
        return this.customProperty;
    }

    public addItem(pos: number, code: string, value: string): boolean {
        return false;
    }

    public removeItem( pos: number ): void {
        return ;
    }

    public modifyItem( pos: number, code: string, value: string ): boolean {
        return false;
    }

    public moveItem( pos: number, bUp: boolean ): boolean {
        return false;
    }

    public setItemPrefix( bSelected: boolean, content: string ): number {
        return ResultType.UnEdited;
    }

    public getItemList(): CodeValueItem[] {
        return undefined;
    }

    public getSelectPrefixContent(): string {
        return undefined;
    }

    public getPrefixContent(): string {
        return undefined;
    }

    public getSeparator(): string {
        return undefined;
    }

    public getDateBoxFormat(): DateBoxFormat {
        return undefined;
    }

    public getCustomDateFormat(): ICustomFormatDateProps {
        return undefined;
    }

    public setNewControlListItems(selectItemsPos: number[]): number {
        return -1;
    }

    public setRadioBoxChecked(x: number, y: number): boolean {
        return false;
    }

    public setCheckBoxSelect(bChecked: boolean): number {
        return ;
    }

    public setRadioSelect(sCode: string): number {
        return ;
    }

    public setDateBoxText(): void {
        return ;
    }

    public setDateTime(dateTime?: string): number {
        return ;
    }

    public setCheckBoxText(sCaption: string): number {
        return ;
    }

    public setSeparator(sSeparator: string): number {
        return ResultType.UnEdited;
    }

    public addItems(codes: string[], values: string[]): boolean {
        return false;
    }

    public getDateTime(): string {
        return ;
    }

    public getCheckLabel(): string {
        return ;
    }

    public updateItems(items: CodeValueItem[], bClearItems?: boolean): number {
        return ;
    }

    public getItems(): any[] {
        return ;
    }

    public setSelectByValue(sValue: string): number {
        return ;
    }

    public clearSelected(): number {
        return ;
    }

    public getSelectedValue(): string {
        return ;
    }

    public getSelectedIndexes(): string {
        return '';
    }

    public setSelectByIndex(sIndex: string): number {
        return ResultType.UnEdited;
    }

    public setItemTextColor(index: string, color: string): number {
        return ResultType.UnEdited;
    }

    public removeAllItems(): number {
        return ResultType.ParamError;
    }

    public getStartDate(): string {
        return undefined;
    }

    public getEndDate(): string {
        return undefined;
    }

    public getSignatureCount(): number {
        return undefined;
    }

    public getPreText(): string {
        return undefined;
    }

    public getSignatureSeparator(): string {
        return undefined;
    }

    public getPostText(): string {
        return undefined;
    }

    public getSignaturePlaceholder(): string {
        return undefined;
    }

    public getSignatureRatio(): number {
        return undefined;
    }

    public getRowHeightRestriction(): boolean {
        return undefined;
    }

    public resetDateBoxContent(): void {
        return ;
    }

    public clearText(): void {
        return ;
    }

    public getMinValue(): number {
        return undefined;
    }

    public getMaxValue(): number {
        return undefined;
    }

    public getPrecision(): number {
        return undefined;
    }

    public getUnit(): string {
        return undefined;
    }

    public getForceValidate(): boolean {
        return undefined;
    }

    public getMaxLength(): number {
        return undefined;
    }

    public getHideHasTitle(): boolean {
        return undefined;
    }

    public getFixedLength(): number {
        return undefined;
    }

    public isMoreThanMaxLength(): boolean {
        return false;
    }

    public getRetrieve(): boolean {
        return false;
    }

    /**
     * 复位选择项
     */
    public resetSelectItems(): void { return ; }

    public recalculate(): void {
        this.content.recalculate();
    }

    public getStartBorderInParagraph(): Paragraph {
        const startBorder = this.content.getStartBorderPortion();

        if ( startBorder ) {
            return startBorder.paragraph;
        }

        return null;
    }

    public getEndBorderInParagraph(): Paragraph {
        const endBorder = this.content.getEndBorderPortion();

        if ( endBorder ) {
            return endBorder.paragraph;
        }

        return null;
    }

    public addContentChanges( changes: ContentChangesElement ): void {
        this.contentChanges.add(changes);
    }

    public getTitle(): string {
        return this.content.getTitle();
    }

    public setTitle(title: string): number {
        if ( title == null || title === this.getTitle() ||
            !(NewControlType.TextBox === this.getType()
                || NewControlType.Section === this.getType()
                || this.isAmongComboxOrListStruct())
            ) {
            return ResultType.UnEdited;
        }

        const history = this.getHistory();
        if ( history ) {
            history.addChange(new ChangeNewControlTitle(this, this.getTitle(), title));
        }
        this.content.setTitle(title);

        const startPortion = this.getStartBorderPortion();
        startPortion.removeFromContent(1, startPortion.content.length - 1, true);
        if ( title && 1 <= title.length ) {
            startPortion.addText(title);
        }

        this.recalculate();
        return ResultType.Success;
    }

    public isPopWindowNewControl(): boolean {
        return this.content.isPopWindowNewControl();
    }

    public isErrorStatus(): boolean {
        return false;
    }

    public toPrint(): void {
        if (this.isPlaceHolderContent()) {
            this.setHidden(true, false);
        }

        if (this.viewSecretType !== NewControlContentSecretType.DontSecret) {
            this.viewSecretType = NewControlContentSecretType.DontSecret;
        }

        this.setShowBorder(false);
    }

    public browseTemplet(type: CleanModeType, nProtected?: number): void {
        // 根据需求，清洁模式下，只有占位符的结构化元素，只隐藏当前占位符不隐藏标题
        if (this.isPlaceHolderContent()) {
            const border = this.getStartBorderPortion();
            const endBorder = this.getEndBorderPortion();
            const para = border.paragraph;
            const contents = para.content;
            let startIndex = contents.findIndex((item) => item === border) + 1;
            // 若只有标题，根据属性隐藏标题
            const textTitleOnly = this.isNewControlTextTitleOnly();
            if ( textTitleOnly === true) {
                startIndex-- ;
            }
            const endIndex = contents.findIndex((item) => item === endBorder);

            // console.log(contents)
            // console.log(startIndex, endIndex)
            // console.log(type)
            let skipHidden = false;
            for (let index = startIndex; index < endIndex; index++) {

                if (type === CleanModeType.CleanModeSpecial) {
                    // if (skipHidden === true) {
                    //     skipHidden = false;
                    //     continue;
                    // }
                    if (textTitleOnly === true && index === startIndex) {
                        continue;
                    } else {
                        const tmpContent = contents[index];
                        // may exist empty portion
                        if (tmpContent.isEmpty(false) === false && tmpContent.isSpecialCharactor() === true) {
                            continue;
                        } else {
                            skipHidden = true;
                        }
                    }
                    // if ((textTitleOnly === false && index === startIndex)
                    //     || (textTitleOnly === true && index === startIndex + 1)) {
                    //     // TODO: textTitleOnly logic may improve
                        // const tmpContent = contents[index];
                        // // may exist empty portion
                        // if (tmpContent.isEmpty(false) === false) {
                        //     continue;
                        // } else {
                        //     skipHidden = true;
                        // }
                    // }
                }

                contents[index].setHidden(true);
            }
        }

        if (this.viewSecretType !== NewControlContentSecretType.DontSecret) {
            this.viewSecretType = NewControlContentSecretType.DontSecret;
        }

        this.setShowBorder(false);
        this.setHiddenBackground(true);
        // get rid of gray bg
        
        // change end portion border ']' -> '' to hide triangle elem
        if (TRIANGLE_ARRAY.includes(this.getType())) {
            const endBorderPortion = this.content.getEndBorderPortion();
            if (endBorderPortion != null && endBorderPortion.content != null && endBorderPortion.content.length > 0) {
                endBorderPortion.content[0].content = '';
            }
        }

    }

    public getDocumentSectionType(): DocumentSectionType {
        // document:
        // 1. content.parent is document
        // 2. content.parent.parent is region (headerfooter has no region)
        // 3. content.parent.parent is tablecell; tablecell.row.table.parent is document
        // header:
        // 1. content.parent.parent is header
        // 2. content.parent.parent is tablecell; tablecell.row.table.parent.parent is header
        // footer:
        // 1. content.parent.parent is footer
        // 2. content.parent.parent is tablecell; tablecell.row.table.parent.parent is footer

        // let result = AreaType.MainDocument;
        const contentParent = this.getDocumentParent();
        if (contentParent != null) {
            return contentParent.getDocumentSectionType();
        } else {
            return DocumentSectionType.Document;
        }

    }

    public getPrintSelected(): boolean {
        return;
    }

    public canDelete(): boolean {
        const parent = this.getDocumentParent();
        const selection = parent.getSelection();
        let startPos = selection.startPos;
        let endPos = selection.endPos;

        if ( startPos > endPos ) {
            const temp = startPos;
            startPos = endPos;
            endPos = temp;
        }

        if ( selection.bUse ) {
            for (let index = startPos; index <= endPos; index++) {
                const element = parent.content[index];
                if ( element && !element.canDelete() ) {
                    return false;
                }
            }
        }

        if ( parent && parent.isTrackRevisions() ) {

            const revisionsManager = parent.getRevisionsManager();
            const revsList = revisionsManager.getRevisionsList();

            if ( !revsList || 0 === revsList.size ) {
                return true;
            }

            for (let index = startPos; index <= endPos; index++) {
                const revs = revsList.get(index);

                if ( revs && 0 < revs.length ) {
                    const element = parent.content[index];
                    if ( element && !element.canDelete() ) {
                        return false;
                    }
                }
            }
        }

        return true;
    }

    public replaceTextToPara(): void {
        const startParaPortion = this.getStartBorderPortion();
        const startPara = startParaPortion ? startParaPortion.paragraph : null;
        const endParaPortion = this.getEndBorderPortion();
        const endPara = endParaPortion ? endParaPortion.paragraph : null;

        if ( startPara && startPara === endPara ) {
            const startPortionIndex = startPara.getPortionIndexById(startParaPortion.id);
            const endPortionIndex = endPara.getPortionIndexById(endParaPortion.id);
            for (let index = startPortionIndex; index <= endPortionIndex; index++) {
                const portion = startPara.content[index];
                // const newPortion = new ParaPortion(startPara, startPara.logicDocument, true);
                if ( portion && (portion.isNewControlStart() || portion.isNewControlEnd())) {
                    portion.repalceText(0);
                // } else {
                //     newPortion.concatToContent(portion.content);
                }
                // newPortions.push(newPortion);
            }

            // startPara.contentRemove2
        } else if ( startPara && endPara && startPara !== endPara ) {
            const startPortionIndex = startPara.getPortionIndexById(startParaPortion.id);
            const endPortionIndex = endPara.getPortionIndexById(endParaPortion.id);
            for (let index = startPortionIndex; index < startPara.content.length; index++) {
                const portion = startPara.content[index];
                if ( portion && (portion.isNewControlStart() || portion.isNewControlEnd())) {
                    portion.repalceText(0);
                }
            }

            const parent = this.getDocumentParent();
            for (let index = startPara.index + 1; index < endPara.index; index++) {
                const element = parent.content[index];
                if ( element && element instanceof Paragraph ) {
                    for (let index2 = 0; index2 < element.content.length; index2++) {
                        const portion = startPara.content[index2];
                        if ( portion && (portion.isNewControlStart() || portion.isNewControlEnd())) {
                            portion.repalceText(0);
                        }
                    }
                }
            }

            for (let index = 0; index <= endPortionIndex; index++) {
                const portion = endPara.content[index];
                if ( portion && (portion.isNewControlStart() || portion.isNewControlEnd())) {
                    portion.repalceText(0);
                }
            }
        }
    }

    // get sign control related structs bg color
    public getBgColorOfSignStructs(): number {
        // 1. normal 2. canntedit bg color
        let result = 1;
        if (this.isEditProtect()) {
            result = 2;
            if (this.isSignatureBox()) {
                // sign control itself has no bgcolor
                result = 1;
            } else if (this.isNewTextBox()) {
                const parentControl = this.getParent();
                // console.log(parentControl)
                if ( parentControl && parentControl.isSignatureBox()) {
                    // text structs within sign control.
                    // if text struct is signed, result = 2. else 1
                    if (this.getNewControlTextLength() <= 0) {
                        result = 1;
                    }
                }
            }
        }
        return result;
    }

    public getHistory(): History {
      return this.content.getHistory();
    }

    public isTrackRevisions(): boolean {
        return this.content.isTrackRevisions();
    }

    /**
     * 是否有修订痕迹(目前仅用于日期框)
     * @returns
     */
    public isReviewType(): boolean {
        if (!this.isNewDateBox()) {
            return false;
        }

        let paraPortion;
        if (this.isPlaceHolderContent()) {
            paraPortion = this.getPlaceHolder();
            return paraPortion?.isReviewType();
        }

        const startPortion = this.getStartBorderPortion();
        const para = startPortion.paragraph;
        const startPortionIndex = para.getPortionIndexById(startPortion.id);
        paraPortion = para.content[startPortionIndex + 1];
        return paraPortion?.isReviewType();
    }

    public getContentReviewInfo(): ReviewInfo {
        const startPortion = this.getStartBorderPortion();
        const para = startPortion.paragraph;
        const startPortionIndex = para.getPortionIndexById(startPortion.id);
        const paraPortion = para.content[startPortionIndex + 1];
        return paraPortion?.getReviewInfo();
    }

    public getGroup(): string {
        return undefined;
    }

    public setAlwaysShow2(): boolean {
        return false;
    }

    public removeCertainChar(sRemoveChar: string): boolean {
        return this.content.removeCertainChar(sRemoveChar);
    }

    public setSourceDataBind(props: IExternalDataProperty): number {
        let result = ResultType.UnEdited;
        if (!props || (!props.sourceObj && !this.externalDataBind?.sourceObj)) {
            return result;
        }

        if (!this.externalDataBind) {
            this.externalDataBind = {
                sourceObj: '',
                sourceKey: '',
                bReadOnly: undefined,
                commandUpdate: undefined,
            };
        }

        const sourceObj = this.externalDataBind.sourceObj;
        if (props.sourceObj !== this.externalDataBind.sourceObj) {
            this.externalDataBind.sourceObj = props.sourceObj;
            result = ResultType.Success;
        }

        if (props.sourceKey !== this.externalDataBind.sourceKey) {
            this.externalDataBind.sourceKey = props.sourceKey;
            result = ResultType.Success;
        }

        if (props.bReadOnly !== this.externalDataBind.bReadOnly) {
            this.externalDataBind.bReadOnly = props.bReadOnly;
            result = ResultType.Success;
        }

        if (props.commandUpdate !== this.externalDataBind.commandUpdate) {
            this.externalDataBind.commandUpdate = props.commandUpdate;
            result = ResultType.Success;
        }

        if (ResultType.Success === result) {
            const newControlManager = this.getNewControlManager();
            if (this.externalDataBind.sourceObj) {
                newControlManager?.addSourceData(this.name, this.externalDataBind);
            } else {
                newControlManager?.removeSourceData(this, sourceObj);
            }
        }

        return result;
    }

    public getSourceDataBind(): IExternalDataProperty {
        const datas = this.externalDataBind;
        if (datas) {
            return {
                sourceObj: datas.sourceObj,
                sourceKey: datas.sourceKey,
                bReadOnly: datas.bReadOnly,
                commandUpdate: datas.commandUpdate,
            };
        }

        return undefined;
    }

    /**
     * 获取当前监听元素的内容：不包含占位符
     * @returns
     */
    public getMonitorText(): string {
        let res = this.isPlaceHolderContent() ? this.getTitle() : '';
        return res + this.getNewControlText();
    }

    /*
     * 获取元素计算级联
     * @returns
     */
    public getEventInfoString(): string {
        let res = {};
        const eventInfo = this.cascadeManager.getEvent(this);
        if (eventInfo) {
            res = {
                expression: eventInfo.expression,
                target: eventInfo.target,
                event: (eventInfo.event || []).map((item) => {
                    return {...item};
                }),
                action: eventInfo.action,
                key: eventInfo.key,
            };
        }

        return JSON.stringify(res);
    }

    /**
     * 获取元素级联
     * @returns
     */
    public getLogicEventString(): string {
        let res = [];
        const logicEvents = this.getCascades();
        if (logicEvents?.length) {
            logicEvents.forEach((item) => {
                const cascade = {
                    target: item.target,
                    event: item.event,
                    logicText: item.logicText,
                    logic: item.logic,
                    action: item.action,
                    actionText: item.actionText,
                };
                res.push(cascade);
            })
        }

        return JSON.stringify(res);
    }

    /**
     * 元素是否在指定区域内
     * @param region
     * @returns
     */
    public isInRegion(region: any): boolean {
        let parent: any = this.getDocumentParent();
        while (parent && parent instanceof DocumentContent) {
            if (parent.isTableCellContent()) {
                parent = (parent.parent as any).row.table;
            } else if (parent.isRegionContent()) {
                parent = parent.parent as any;
                if (parent === region) {
                    return true;
                }
            } else {
                return false;
            }

            parent = parent.parent;
        }

        return false;
    }

    /**
     * 签名元素是否没有内容
     */
    public isContentEmpty(): boolean {
        return (0 === this.getNewControlTextLength());
    }

    // public getSignSubNewControl(): NewControl[] {
    //     return undefined;
    // }

    // private updateCursorXY(doc: any): void {
    //     doc.moveCursorToNewControlStart();
    // }

    private isNewControlTextTitleOnly(): boolean {
        if (this.isNewTextBox() && this.getHideHasTitle() === true) {
            const text = this.content.getNewControlText();
            if ( text === '' || text == null) {
                // should be true since already checked in placeholdercontent
                const title = this.getTitle();
                if (title != null && title !== '') {
                    return true;
                }
            }
        }

        return false;
    }

    private isNotJumpNodes(): boolean {
        if (this.isNewSection() || this.isSignatureBox()) {
            return true;
        }

        return false;
    }

    // private isSameCascade(cascades: ICascade[]): boolean {
    //     const oldCascades = this.cascades;
    //     const len = (oldCascades ? oldCascades.length : 0);
    //     if (cascades.length !== len) {
    //         return false;
    //     }
    //     for (let index = 0; index < len; index++) {
    //         const oldCascade = oldCascades[index];
    //         const actCascade = cascades[index];
    //         if (oldCascade == null || actCascade == null) {
    //             return false;
    //         }
    //         for (const key in oldCascade) {
    //             if (oldCascade[key] !== actCascade[key]) {
    //                 return false;
    //             }
    //         }
    //     }

    //     return true;
    // }

    private canDeleteNewControl(startPos: ParagraphContentPos, endPos: ParagraphContentPos, type: number): boolean {
        const result = (this.isCheckBox() || this.isAmongComboxOrListStruct()
                        || this.isMultiAndRadio()) ? true : false;
        if (result) {
            const para = this.content.getStartBorderPortion().paragraph;
            const searchPos = {
                pos: new ParagraphContentPos(),
                bFound: false,
                bSelection: true,
            };

            if (CursorSelType.BehindOverNewControl === type) {
                const startPosCo = startPos.copy();
                startPosCo.splice(0, startPosCo.getDepth() - 1);
                para.getLeftPos(searchPos, startPosCo);
                if (searchPos.bFound) {
                    const borderPos = this.getStartPos();
                    borderPos.splice(0, startPosCo.getDepth() - 1);
                    searchPos.pos.pop();

                    const portion = para.content[searchPos.pos.get()];
                    if (portion && this.getNewControlName() === portion.getStartNewControlName()) {
                        startPos.splice(startPos.data.length - 2, 2, searchPos.pos.data);

                        para.setParaContentPos(searchPos.pos, 0);
                        if (1 === para.getSelectionDirection()) {
                            para.setSelectionContentPos(searchPos.pos, para.getParaContentPos(true, false));
                        } else {
                            para.setSelectionContentPos(para.getParaContentPos(true, true), searchPos.pos);
                        }

                        return true;
                    }
                }
            } else if (CursorSelType.ForwardOverNewControl === type) {
                const endPosCo = endPos.copy();
                endPosCo.splice(0, endPosCo.getDepth() - 1);
                para.getRightPos(searchPos, endPosCo);
                if (searchPos.bFound) {
                    const borderPos = this.getEndPos();
                    borderPos.splice(0, endPosCo.getDepth() - 1);
                    searchPos.pos.pop();

                    const portion = para.content[searchPos.pos.get()];
                    if (portion && this.getNewControlName() === portion.getEndNewControlName()) {
                        endPos.splice(endPos.data.length - 2, 2, searchPos.pos.data);

                        para.setParaContentPos(searchPos.pos, 0);
                        if (1 === para.getSelectionDirection()) {
                            para.setSelectionContentPos(para.getParaContentPos(true, true), searchPos.pos);
                        } else {
                            para.setSelectionContentPos(searchPos.pos, para.getParaContentPos(true, false));
                        }
                        return true;
                    }
                }
            }
        }

        return result;
    }

    /**
     * 表格之外的结构化元素级联，导致表格内有元素参与公式计算
     * @param doc
     */
    private execFormulaCalc(doc: Document): void {
        const parent = this.getDocumentParent();
        const table = (parent && parent.isTableCellContent() && parent instanceof DocumentContent) ?
                        (parent.parent as any).row?.table : null;
        if (doc && table && table !== doc.getCurrentTable()) {
            table.curCell = (parent as any).parent as any;
            table.execFormulaCalc();
        }
    }
}

export class NewControlContent {
    private placeHolder: ParaPortion;
    private parent: DocumentContentBase;
    // private startBorder: ParaNewControlBorder;  // 前边框
    // private endBorder: ParaNewControlBorder;  // 后边框
    private startBorderPortion: ParaPortion;
    private endBorderPortion: ParaPortion;
    private placeHolderContent: string;  // 占位符内容;
    private title: string;
    private type: NewControlType;  // 控件类型

    constructor(parent: DocumentContentBase, name: string, type: NewControlType, placeHolderContent: string,
                bHidden: boolean, bShowBorder: boolean, bMustInput: boolean, sText?: string, sTitle?: string ) {
        this.parent = parent;
        this.type = type;

        const bPopWindowNewControl = this.isPopWindowNewControl();
        const startBorder = new ParaNewControlBorder(name, true, bPopWindowNewControl,
                                                    bShowBorder, NewControlType.Section !== type);
        const endBorder = new ParaNewControlBorder(name, false, bPopWindowNewControl,
                                                    bShowBorder, NewControlType.Section !== type);
        this.startBorderPortion = new ParaPortion(null, parent);
        this.endBorderPortion = new ParaPortion(null, parent);
        this.startBorderPortion.addToContent(0, startBorder);
        this.endBorderPortion.addToContent(0, endBorder);
        // if (bHidden === true) {
        //     this.startBorderPortion.setHidden(true);
        //     this.endBorderPortion.setHidden(true);
        // }
        // console.log(placeHolderContent)
        const types = [NewControlType.TextBox, NewControlType.Section, NewControlType.Combox,
            NewControlType.MultiCombox, NewControlType.ListBox, NewControlType.MultiListBox];
        this.placeHolderContent = null != placeHolderContent ? placeHolderContent
                                : NewControlDefaultSetting.DefaultPlaceHolderContent;  // 默认两个空格
        this.title = (types.includes(type)) ? (sTitle || '') : undefined;

        if ( this.title && 0 < this.title.length ) {
            // this.startBorderPortion.bPlaceHolder = false;
            this.startBorderPortion.addText(this.title);
            // this.startBorderPortion.bPlaceHolder = true;
        }

        if ( null == sText || '' === sText ) {
            this.startBorderPortion.bPlaceHolder = true;
            this.endBorderPortion.bPlaceHolder = true;

            this.placeHolder = this.initPlaceHolder(bHidden, bMustInput);
        } else {
            this.placeHolder = new ParaPortion(null, parent);
            this.placeHolder.addText(sText);
        }
    }

    public setEndPosition(end: any): void {
        this.endBorderPortion = end;
    }

    public setStartPosition(start: any): void {
        this.startBorderPortion = start;
    }

    public setParent(parent: DocumentContentBase, history?: History): void {
        if ( parent === this.parent ) {
            return ;
        }

        if ( history ) {
            history.addChange(new ChangNewControlParent(this, this.parent, parent));
        }

        this.parent = parent;
    }

    public getParent(): DocumentContentBase {
        return this.parent;
    }

    /**
     * 初始化占位符
     * @param bHidden 是否隐藏newcontrol
     * @param bMustInput 是否为必填项
     */
    public initPlaceHolder(bHidden: boolean, bMustInput: boolean, textProperty?: TextProperty): ParaPortion {
        const portion = new ParaPortion(null, this.parent);
        // portion.addToContent(0, this.startBorder);
        if ( textProperty ) {
            portion.setProperty(textProperty.copy());
        }

        let insertText = null;
        if ( true === bMustInput ) {
            insertText = NewControlDefaultSetting.DefaultMustInputChar;
            insertText += this.placeHolderContent;
            // insertText += NewControlDefaultSetting.DefaultMustInputChar;

            portion.textProperty.color = NewControlDefaultSetting.DefaultMustInputTextColor;
        } else {
            insertText = this.placeHolderContent;
            portion.textProperty.color = NewControlDefaultSetting.DefaultTextColor;
        }

        portion.addText(insertText);
        // portion.addToContent(portion.content.length, this.endBorder);
        // portion.bPlaceHolder = true;
        if ( true === bHidden ) {
            portion.setNewControlTextHidden(bHidden);
        }

        return portion;
    }

    /**
     * 获取占位符所在的portion
     */
    public getPlaceHolder(): ParaPortion {
        return this.placeHolder;
    }

    public setPlaceHolderPortion(portion: ParaPortion): void {
        const history = this.getHistory();
        if ( history ) {
            history.addChange(new ChangeNewControlPlaceHolderPortion(this, this.placeHolder, portion));
        }
        this.placeHolder = portion;
    }

    /**
     * 内容是否只有占位符
     */
    public hasOnlyPlaceHolderContent(): boolean {
        return this.startBorderPortion.bPlaceHolder;
    }

    /**
     * 获取左边框所在的portion
     */
    public getStartBorderPortion(): ParaPortion {
        return this.startBorderPortion;
    }

    /**
     * 获取右边框所在的portion
     */
    public getEndBorderPortion(): ParaPortion {
        return this.endBorderPortion;
    }

    /**
     * 设置占位符内容
     * @param content 新的占位符内容
     * @param bMustInput 是否为必填项
     */
    public setPlaceHolderContent(content: string, bMustInput: boolean): number {
        if ( content === this.placeHolderContent ) {
            return ResultType.UnEdited;
        }

        this.placeHolderContent = content;

        if ( true === this.startBorderPortion.bPlaceHolder ) {
            this.addPlaceHolderContent(bMustInput);
        }

        return ResultType.Success;
    }

    /**
     * 插入占位符：先删除newcontrol的内容，再插入占位符
     * @param bMustInput 是否为必填项
     */
    public addPlaceHolderContent( bMustInput: boolean ): void {
        // const bPlaceHolder = this.startBorderPortion.isPlaceHolder();
        const startPara = this.startBorderPortion.paragraph;
        const startPortionIndex = startPara.getPortionIndexById(this.startBorderPortion.id);
        const contentIndex = startPortionIndex + 1;

        const bTextBox = ((NewControlType.TextBox === this.getType() || NewControlType.Section === this.getType())
                        && 1 < this.startBorderPortion.content.length);
        let contentTextProperty = (bTextBox ? startPara.content[contentIndex]?.textProperty.copy() : null);

        this.removePlaceHolderContent();

        const newPortion = this.initPlaceHolder(false, bMustInput);

        const history = this.getHistory();
        if ( history ) {
            history.addChange(new ChangeNewControlPlaceHolderPortion(this, this.placeHolder, newPortion));
        }
        this.placeHolder = newPortion;

        startPara.addToContent(contentIndex, this.placeHolder);
        startPara.curPos.contentPos = contentIndex;
        if (!bTextBox) {
            const textProperty = this.placeHolder.textProperty.copy();
            textProperty.fontFamily = this.startBorderPortion.textProperty.fontFamily;
            textProperty.fontSize = this.startBorderPortion.textProperty.fontSize;
            textProperty.fontStyle = this.startBorderPortion.textProperty.fontStyle;
            textProperty.fontWeight = this.startBorderPortion.textProperty.fontWeight;
            textProperty.vertAlign = this.startBorderPortion.textProperty.vertAlign;
            textProperty.textDecorationLine = this.startBorderPortion.textProperty.textDecorationLine;
            this.placeHolder.setProperty(textProperty);
        } else {
            if (!contentTextProperty) {
                const topDocument = startPara?.getDocument();
                contentTextProperty = new TextProperty(getDefaultFont(topDocument));
            }
            contentTextProperty.color = newPortion.textProperty.color;
            this.placeHolder.setProperty(contentTextProperty);
        }

        if ( this.isTrackRevisions() && !this.startBorderPortion.isReviewType() ) {
            newPortion.resetReviewTypeAndInfo();
        }

        this.startBorderPortion.setPlaceHolder(true);
        this.endBorderPortion.setPlaceHolder(true);
    }

    /**
     * 删除占位符或者内容
     */
    public removePlaceHolderContent(bRefresh: boolean = true): void {
        const startPara = this.startBorderPortion.paragraph;
        const endPara = this.endBorderPortion.paragraph;
        const startPortionIndex = startPara.getPortionIndexById(this.startBorderPortion.id);
        const endPortionIndex = endPara.getPortionIndexById(this.endBorderPortion.id);

        if ( !this.hasOnlyPlaceHolderContent() ) {
            this.removeNewControlsInContent();
        }

        let bChanged = false;
        if ( true === this.hasOnlyPlaceHolderContent() || startPara === endPara ) {
            const paraContent = startPara.getContent();
            const startPortion = paraContent[startPortionIndex];
            const endPortion = paraContent[endPortionIndex];
            const types = [NewControlType.TextBox, NewControlType.Section, NewControlType.Combox,
                NewControlType.MultiCombox, NewControlType.ListBox, NewControlType.MultiListBox];
            if ( 1 < startPortion.content.length && !types.includes(this.type) ) {
                bChanged = startPortion.removeFromContent(1, startPortion.content.length - 1, true) || bChanged;
            }
            // const delItems = paraContent.slice(startPortionIndex + 1, endPortionIndex);

            // const history = this.parent.getHistory();
            // history.addChange(new ChangeParagraphRemoveItem(startPara, startPortionIndex + 1, delItems));
            // paraContent.splice(startPortionIndex + 1, endPortionIndex - startPortionIndex - 1);
            // startPara.curPos.contentPos = startPortionIndex;
            bChanged = startPara.contentRemove2(startPortionIndex + 1, endPortionIndex - startPortionIndex - 1) || bChanged;

            if ( 1 < endPortion.content.length ) {
                bChanged = endPortion.removeFromContent(0, endPortion.content.length - 1, true) || bChanged;
            }
            this.startBorderPortion.setPlaceHolder(false);
            this.endBorderPortion.setPlaceHolder(false);
        } else {
            // this.removeNewControlsInContent();

            startPara.removeNewControlText(startPortionIndex + 1, null);
            const endParaContents = endPara.getContent();
            const endParaPortions = endParaContents.slice(endPortionIndex);
            startPara.contentConcat(endParaPortions);
            this.setBorder(endParaPortions[0], false);

            this.parent.curPos.contentPos = startPara.index;
            startPara.curPos.contentPos = startPortionIndex;
            this.parent.contentRemove(startPara.index + 1, endPara.index - startPara.index);
            bChanged = true;
        }

        if (bRefresh === true && bChanged) {
            this.parent.recalculate();
        }
    }

    /**
     * 获取占位符内容
     */
    public getPlaceHolderContent(): string {
        return this.placeHolderContent;
    }

    /**
     * 设置元素的文本内容
     * @param sText 新插入文本
     * @param bMustInput 是否为必填项
     */
    public setNewControlText( sText: string, bMustInput: boolean, viewSecretType: NewControlContentSecretType,
                              bHidden?: boolean, bRefresh?: boolean): number {
        if ( null != sText ) {
            // const bOldPlaceHolder = this.hasOnlyPlaceHolderContent();
            const bPlaceHolder = ( '' === sText && NewControlType.CheckBox !== this.type ? true : false );
            // sText = ( bPlaceHolder ? this.placeHolderContent : sText);

            const startPara = this.startBorderPortion.paragraph;
            let startPortionIndex = startPara.getPortionIndexById(this.startBorderPortion.id);
            const curLine = (0 <= startPara.curPos.line ? startPara.curPos.line : this.placeHolder.getLineByPos(0));
            const bHiddenPortion = startPara.content[startPortionIndex + 1].bHidden;
            const bTextBox = (NewControlType.TextBox === this.type || NewControlType.Section === this.type)
                                && this.title;
            // const bHasChild = (NewControlType.Section === this.type && this.title
                                // && startPara.content[startPortionIndex + 1].isNewControlStart());
            const textProperty = (bTextBox ?
                // (!bHasChild ? startPara.content[startPortionIndex + 1].textProperty.copy() :
                                    new TextProperty(getDefaultFont(startPara.getDocument())) :
                                    this.startBorderPortion.textProperty.copy());
            this.removePlaceHolderContent(bRefresh);

            let realText = sText; // 实际插入的首个文本
            const textJson = []; // [key, value];
            let hasJson = false;

            try {
                const obj = JSON.parse(sText);
                for (const key of Object.keys(obj)) {
                    const value = obj[key];
                    const align = key.toLowerCase();
                    const vertAlign = align === 'normal' ? TextVertAlign.Baseline
                        : align === 'superscript' ? TextVertAlign.Super
                        : align === 'subscript' ? TextVertAlign.Sub
                        : textProperty.vertAlign;
                    if (value) {
                        textJson.push([vertAlign, value]); // 不插入非有效文本
                        hasJson = true;
                    }
                }
                if (hasJson) realText = textJson[0][1];
            } catch {}

            const textArray = getArrayByfilterChars(realText);

            let portion: ParaPortion = null;
            if ( bPlaceHolder ) {
                // textProperty.color = NewControlDefaultSetting.DefaultTextColor;
                portion = this.initPlaceHolder(bHidden, bMustInput, textProperty);
            } else {
                textProperty.color = NewControlDefaultSetting.InputContentColor;
                if (hasJson) {
                    textProperty.vertAlign = textJson[0][0];
                }

                portion = new ParaPortion(startPara);
                portion.addText(textArray[0] || '', null, null, true);
                portion.setProperty(textProperty.copy());
                if ( bHiddenPortion || bHidden ) {
                    portion.setNewControlTextHidden(true, true);
                }
            }

            this.setPlaceHolderPortion(portion);
            startPara.addToContent(startPortionIndex + 1, portion);
            if (bRefresh !== false) {
                const pos = startPara.getParaContentPos();
                pos.update2(startPortionIndex + 1, 0);
                pos.update2(portion.content.length, 1);
                startPara.setParaContentPos(pos, curLine);

                startPortionIndex = startPara.getCurPos().contentPos;
            }

            this.startBorderPortion.setPlaceHolder(bPlaceHolder);
            this.endBorderPortion.setPlaceHolder(bPlaceHolder);

            const addTextsWithNewline= (textArr: string[], pos?: number) => {
                textArr.forEach((text, index) => {
                    if (portion) {
                        portion.addParaItem(portion.content.length, ParaElementType.ParaNewLine);
                    }

                    portion = new ParaPortion(startPara);
                    portion.setProperty(textProperty.copy());
                    if (text) {
                        portion.addText(text, textProperty.copy(), 0, true);
                        if (bHidden || bHiddenPortion) {
                            portion.setNewControlTextHidden(true, true);
                        }
                    }

                    index = pos || 0;
                    startPara.addToContent(startPortionIndex + index, portion);
                    if (pos) {
                        pos++;
                    }
                });
            }

            if (!hasJson) {
                addTextsWithNewline(textArray.slice(1), 1);
            } else {
                for (const [align, text] of textJson.slice(1)) {
                    portion = null;
                    textProperty.vertAlign = align;
                    startPortionIndex += 1;
                    addTextsWithNewline(getArrayByfilterChars(text))
                }
            }

            if (bRefresh !== false) {
                this.parent.recalculate();
            }

            if ( !bPlaceHolder && NewControlContentSecretType.DontSecret !== viewSecretType ) {
                this.setViewSecret(viewSecretType);
            }

            // this.parent.updateCursorXY();
            return ResultType.Success;
        }

        return ResultType.ParamError;
    }

    public setNewControlText2( sText: string, bMustInput: boolean, viewSecretType: NewControlContentSecretType,
                               bHidden?: boolean ): void {
        this.removePlaceHolderContent(false);

        const startPara = this.startBorderPortion.paragraph;
        const startPortionIndex = startPara.getPortionIndexById(this.startBorderPortion.id);
        const textProperty = this.startBorderPortion.textProperty.copy();

        const portion = new ParaPortion(startPara);
        portion.addText(sText, textProperty.copy(), 0, true);
        if ( bHidden ) {
            portion.setNewControlTextHidden(bHidden, true);
        }

        this.setPlaceHolderPortion(portion);
        startPara.addToContent(startPortionIndex + 1, portion);

        this.startBorderPortion.setPlaceHolder(false);
        this.endBorderPortion.setPlaceHolder(false);

        // if ( !bPlaceHolder && NewControlContentSecretType.DontSecret !== viewSecretType ) {
        //     this.setViewSecret(viewSecretType);
        // }
    }

    public getAllImagesName(): string[] {
        let images = [];
        const startPara = this.startBorderPortion.paragraph;
        const endPara = this.endBorderPortion.paragraph;
        const startId = this.startBorderPortion.getId();
        const endId = this.endBorderPortion.getId();
        if (startPara.getId() !== endPara.getId()) { // section
            const paragraphs = startPara.getParent()
                                        .content
                                        .slice(startPara.index, endPara.index + 1);
            for (let index = 0; index < paragraphs.length; index++) {
                const para = paragraphs[index] as Paragraph;
                if (index === 0) {
                    images = images.concat(para.getAllImagesName(startId));
                } else if (index === paragraphs.length - 1) {
                    images = images.concat(para.getAllImagesName(null, endId));
                } else {
                    images = images.concat(para.getAllImagesName());
                }
            }
        } else {
            images = images.concat(startPara.getAllImagesName(startId, endId));
        }
        return images;
    }

    public setNewControlImage(image: ParaDrawing, bMustInput: boolean, viewSecretType: NewControlContentSecretType,
                              bHidden?: boolean): number {
        if ( null != image ) {
            // const bOldPlaceHolder = this.hasOnlyPlaceHolderContent();
            const bPlaceHolder = false;
            // sText = ( bPlaceHolder ? this.placeHolderContent : sText);

            this.removePlaceHolderContent();

            const startPara = this.startBorderPortion.paragraph;
            const startPortionIndex = startPara.getPortionIndexById(this.startBorderPortion.id);
            const textProperty = this.startBorderPortion.textProperty.copy();

            let portion: ParaPortion = null;
            if ( bPlaceHolder ) {
                // textProperty.color = NewControlDefaultSetting.DefaultTextColor;
                portion = this.initPlaceHolder(bHidden, bMustInput, textProperty);
            } else {
                // if ( bOldPlaceHolder ) {
                textProperty.color = NewControlDefaultSetting.InputContentColor;
                // }

                portion = new ParaPortion(startPara);
                portion.addToContent(0, image, false, true);
                portion.setProperty(textProperty.copy());
                if ( bHidden ) {
                    portion.setNewControlTextHidden(bHidden);
                }
            }

            this.setPlaceHolderPortion(portion);
            startPara.addToContent(startPortionIndex + 1, portion);

            const pos = startPara.getParaContentPos();
            pos.update2(startPortionIndex + 1, 0);
            pos.update2(portion.content.length, 1);
            startPara.setParaContentPos(pos, startPara.curPos.line);
            this.startBorderPortion.setPlaceHolder(bPlaceHolder);
            this.endBorderPortion.setPlaceHolder(bPlaceHolder);

            this.parent.recalculate();

            if ( !bPlaceHolder && NewControlContentSecretType.DontSecret !== viewSecretType ) {
                this.setViewSecret(viewSecretType);
            }

            this.parent.updateCursorXY();
            // return ResultType.Success;
        }

        return ResultType.Success;

    }

    public setNewControlMixData(datas: any, bMustInput: boolean, viewSecretType: NewControlContentSecretType,
                                bHidden?: boolean): number {
        let result = ResultType.UnEdited;
        this.removePlaceHolderContent();
        const startPara = this.startBorderPortion.paragraph;
        const textProperty = this.startBorderPortion.textProperty.copy();
        const topDocument = startPara.getTopDocument();
        textProperty.color = NewControlDefaultSetting.InputContentColor;
        let startPortionIndex = startPara.getPortionIndexById(this.startBorderPortion.id);

        for (const item of datas) {
            let data = item.data;
            if (item && data) {
                switch (item.type) {
                    case 1:
                        if (data) {
                            data = filterChars2(data);
                            const portion = new ParaPortion(startPara);
                            portion.addText(data, textProperty);
                            if ( bHidden ) {
                                portion.setNewControlTextHidden(bHidden);
                            }
                            startPara.addToContent(++startPortionIndex, portion);
                            result = ResultType.Success;
                        }
                        break;
                    case 2:
                        if (data && 0 < data.width && 0 < data.height && data.source) {
                            const portion = new ParaPortion(startPara);
                            const drawing = new ParaDrawing(topDocument, data.width, data.height, data.source);
                            drawing.setSizeLocked(true);
                            drawing.setPreserveAspectRatio(false);
                            portion.addToContent(0, drawing, false, true);
                            if ( bHidden ) {
                                portion.setNewControlTextHidden(bHidden);
                            }
                            startPara.addToContent(++startPortionIndex, portion);
                            result = ResultType.Success;
                        }
                        break;
                }
            }
        }

        // if ( ResultType.Success === result && !this.hasOnlyPlaceHolderContent()
        //     && NewControlContentSecretType.DontSecret !== viewSecretType ) {
        //     this.setViewSecret(viewSecretType);
        // }

        return result;
    }

    public isSelected(): boolean {
        return false;
    }


     /**
     * 获取元素的文本内容，带边框，如果只有占位符，则返回空字符 
     */
     public getNewControlTextAI(bParaEnd: boolean = false): string {
        let sText = '';
        if ( false === this.startBorderPortion.bPlaceHolder ) {
            const startPara = this.startBorderPortion.paragraph;
            const endPara = this.endBorderPortion.paragraph;
            const startParaIndex = startPara.index;
            const endParaIndex = endPara.index;
            const startPortionIndex = startPara.getPortionIndexById(this.startBorderPortion.id);
            const endPortionIndex = endPara.getPortionIndexById(this.endBorderPortion.id);
            const bSection = (NewControlType.Section === this.getType() ||
                                NewControlType.TextBox === this.getType());

            sText += startPara.getNewControlTextAI(startPortionIndex,
                startParaIndex === endParaIndex ? endPortionIndex : null, bParaEnd, bSection);

            for (let index = startParaIndex + 1; index < endParaIndex; index++) {
                const element = this.parent.content[index];
                if ( element && element.isParagraph() ) {
                    sText += '\n';
                    sText += element.getNewControlText(null, null, bParaEnd, bSection);
                }
            }

            if ( startParaIndex !== endParaIndex ) {
                sText += '\n';
                sText += endPara.getNewControlTextAI(null, endPortionIndex, bParaEnd, bSection);
            }
        }

        return sText;
    }

    /**
     * 获取元素的文本内容，如果只有占位符，则返回空字符
     */
    public getNewControlText(bParaEnd: boolean = false): string {
        let sText = '';
        if ( false === this.startBorderPortion.bPlaceHolder ) {
            const startPara = this.startBorderPortion.paragraph;
            const endPara = this.endBorderPortion.paragraph;
            const startParaIndex = startPara.index;
            const endParaIndex = endPara.index;
            const startPortionIndex = startPara.getPortionIndexById(this.startBorderPortion.id);
            const endPortionIndex = endPara.getPortionIndexById(this.endBorderPortion.id);
            const bSection = (NewControlType.Section === this.getType() ||
                                NewControlType.TextBox === this.getType());

            sText = startPara.getNewControlText(startPortionIndex,
                startParaIndex === endParaIndex ? endPortionIndex : null, bParaEnd, bSection);

            for (let index = startParaIndex + 1; index < endParaIndex; index++) {
                const element = this.parent.content[index];

                if ( element && element.isParagraph() ) {
                    sText += '\n';
                    sText += element.getNewControlText(null, null, bParaEnd, bSection);
                }
            }

            if ( startParaIndex !== endParaIndex ) {
                sText += '\n';
                sText += endPara.getNewControlText(null, endPortionIndex, bParaEnd, bSection);
            }
        }

        return sText;
    }

    public getNewControlTextLength(bParaEnd: boolean = false): number {
        return this.getNewControlText(bParaEnd).length;
    }

    /**
     * 删除newControl内容：如果只有占位符，则不进行删除
     */
    public removeContent(bRefresh: boolean): void {
        if ( false === this.startBorderPortion.bPlaceHolder ) {
            this.removePlaceHolderContent(bRefresh);
        }
    }

    public initPlaceHolderDefaultTextProperty( textProperty: TextProperty, bMustInput: boolean ): void {
        this.startBorderPortion.textProperty = textProperty.copy();
        this.endBorderPortion.textProperty = textProperty.copy();
        textProperty.color = true !== bMustInput ? NewControlDefaultSetting.DefaultTextColor :
                                            NewControlDefaultSetting.DefaultMustInputTextColor;
        // textProperty.backgroundColor = NewControlDefaultSetting.DefaultBackgroundColor;
        this.placeHolder.textProperty = textProperty;
    }

    public setShowBorder( bShowBorder: boolean ): void {
        // this.startBorder.bVisible = !bShowBorder;
        // this.endBorder.bVisible = !bShowBorder;
        const history = this.getHistory();
        this.startBorderPortion.content[0].setVisible(bShowBorder, history);
        this.endBorderPortion.content[0].setVisible(bShowBorder, history);
        // this.startBorderPortion.content[0].bVisible = bShowBorder;
        // this.endBorderPortion.content[0].bVisible = bShowBorder;
    }

    public setViewSecret( viewSecretType: NewControlContentSecretType ): void {
        // const startParaIndex = this.startBorder.paraIndex; // parent.getParaIndexById(this.startBorder.paraId);
        // const endParaIndex = this.startBorder.paraIndex; // parent.getParaIndexById(this.endBorder.paraId);
        const startPara = this.startBorderPortion.paragraph; // this.parent.content[startParaIndex] as Paragraph;
        const endPara = this.endBorderPortion.paragraph; // this.parent.content[endParaIndex] as Paragraph;
        const startParaIndex = startPara.index; // parent.getParaIndexById(this.startBorder.paraId);
        const endParaIndex = endPara.index;
        const startPortionIndex = startPara.getPortionIndexById(this.startBorderPortion.id);
        const endPortionIndex = endPara.getPortionIndexById(this.endBorderPortion.id); // this.endBorder.portionId);

        switch (viewSecretType) {
            case NewControlContentSecretType.DontSecret:
            case NewControlContentSecretType.AllSecret: {
                startPara.setNewControlViewSecret(viewSecretType, startPortionIndex + 1,
                                    startParaIndex === endParaIndex ? endPortionIndex : null );

                for (let index = startParaIndex + 1; index < endParaIndex; index++) {
                    const para = this.parent.content[index] as Paragraph;

                    if ( null != para ) {
                        para.setNewControlViewSecret(viewSecretType, null, null);
                    }
                }

                if ( startParaIndex !== endParaIndex ) {
                    endPara.setNewControlViewSecret(viewSecretType, null, endPortionIndex);
                }
                break;
            }

            case NewControlContentSecretType.PartSecret: {
                if ( startParaIndex === endParaIndex ) {
                    startPara.setNewControlViewSecret(viewSecretType, startPortionIndex + 1, endPortionIndex, true);
                } else {
                    let leftLength = 0;
                    let startParaLength = 0;
                    let leftPos = startParaIndex;
                    let rightLength = 0;
                    let endParaLength = 0;
                    let rightPos = endParaIndex;

                    startParaLength = startPara.getTextLengthByPos(startPortionIndex + 1, null, true);
                    leftLength += startParaLength;

                    if ( 1 > leftLength ) {
                        leftPos = -1;
                        for (let index = startParaIndex + 1; index < endParaIndex; index++) {
                            const para = this.parent.content[index] as Paragraph;

                            if ( null != para ) {
                                leftLength += para.getTextLength();
                                if ( 1 <= leftLength ) {
                                    leftPos = index;
                                    break;
                                }
                            }
                        }
                    }

                    endParaLength = endPara.getTextLengthByPos(null, endPortionIndex, true);
                    rightLength += endParaLength;

                    if ( 1 > rightLength ) {
                        rightPos = -1;
                        for (let index = endParaIndex - 1; index > startParaIndex; index--) {
                            const para = this.parent.content[index] as Paragraph;

                            if ( null != para ) {
                                rightLength += para.getTextLength();
                                if ( 1 <= rightLength ) {
                                    rightPos = index;
                                    break;
                                }
                            }
                        }
                    }

                    if ( leftPos === rightPos ) {
                        if ( -1 !== leftPos ) {
                            const para = this.parent.content[leftPos] as Paragraph;
                            para.setNewControlViewSecret(viewSecretType, null, null, true);
                        }
                    } else if ( leftPos < rightPos ) {
                        if ( -1 === leftPos ) {
                            endPara.setNewControlViewSecret(viewSecretType, null, endPortionIndex, true);
                        } else {
                            let secretTextLength = 0;

                            if ( leftPos === startParaIndex ) {
                                secretTextLength = startPara.setNewControlViewSecret(
                                    viewSecretType, startPortionIndex + 1, null, false, true);
                            } else {
                                const para = this.parent.content[leftPos] as Paragraph;
                                secretTextLength = para.setNewControlViewSecret(
                                            viewSecretType, null, null, false, true);
                            }

                            for (let index = leftPos + 1; index < rightPos; index++) {
                                const para = this.parent.content[index] as Paragraph;

                                if ( null != para ) {
                                    secretTextLength += para.setNewControlViewSecret(
                                        NewControlContentSecretType.AllSecret, null, null);
                                }
                            }

                            // 只有2个字符
                            if ( 1 === secretTextLength && 1 === rightLength ) {
                                viewSecretType = NewControlContentSecretType.AllSecret;
                            }

                            if ( rightPos === endParaIndex ) {
                                endPara.setNewControlViewSecret(viewSecretType, null, endPortionIndex, false, false);
                            } else {
                                const para = this.parent.content[rightPos] as Paragraph;
                                para.setNewControlViewSecret(viewSecretType, null, null, false, false);
                            }
                        }
                    } else {
                        startPara.setNewControlViewSecret(viewSecretType, startPortionIndex + 1, null, true);
                    }
                }
                break;
            }

            default:
                break;
        }
    }

    public getNewControlName(): string {
        const startBorder = this.startBorderPortion.content[0] as ParaNewControlBorder;
        if ( startBorder ) {
            return startBorder.getNewControlName();
        } else {
            const endBorder = this.endBorderPortion.content[0] as ParaNewControlBorder;
            if ( endBorder ) {
                return endBorder.getNewControlName();
            }
        }

        return null;
    }

    public setNewControlName( name: string ): void {
        const startBorder = this.startBorderPortion.content[0] as ParaNewControlBorder;
        const endBorder = this.endBorderPortion.content[0] as ParaNewControlBorder;
        startBorder.setNewControlName(name);
        endBorder.setNewControlName(name);
    }

    public setMustInput( bHidden: boolean, bMustInput: boolean ): number {
        if ( true === this.startBorderPortion.bPlaceHolder ) {
            // this.removePlaceHolderContent();
            this.addPlaceHolderContent(bMustInput);
            return ResultType.Success;
        }

        return ResultType.UnEdited;
    }

    public setContentHidden(bHidden: boolean): number {
        const startPara = this.startBorderPortion.paragraph;
        const endPara = this.endBorderPortion.paragraph;
        const startParaIndex = startPara.index;
        const endParaIndex = endPara.index;
        const startPortionIndex = startPara.getPortionIndexById(this.startBorderPortion.id);
        const endPortionIndex = endPara.getPortionIndexById(this.endBorderPortion.id);

        startPara.setNewControlTextHidden(bHidden, startPortionIndex,
            startParaIndex === endParaIndex ? endPortionIndex : null );

        if ( startParaIndex !== endParaIndex ) {
            for (let index = startParaIndex + 1; index < endParaIndex; index++) {
                const para = this.parent.content[index] as Paragraph;
                if ( null != para ) {
                    para.setNewControlTextHidden(bHidden, null, null);
                }
            }
            endPara.setNewControlTextHidden(bHidden, null, endPortionIndex - 1);
        }

        return ResultType.Success;
    }

    /**
     * 隐藏整个newControl
     * @param bHidden
     */
    public setHidden( bHidden: boolean, bHidePara: boolean = false): number {
        // const startParaIndex = this.startBorder.paraIndex; // parent.getParaIndexById(this.startBorder.paraId);
        // const endParaIndex = this.endBorder.paraIndex; // parent.getParaIndexById(this.endBorder.paraId);
        const startPara = this.startBorderPortion.paragraph; // this.parent.content[startParaIndex] as Paragraph;
        const endPara = this.endBorderPortion.paragraph; // this.parent.content[endParaIndex] as Paragraph;
        const startParaIndex = startPara.index; // parent.getParaIndexById(this.startBorder.paraId);
        const endParaIndex = endPara.index;
        const startPortionIndex = startPara.getPortionIndexById(this.startBorderPortion.id);
        const endPortionIndex = endPara.getPortionIndexById(this.endBorderPortion.id); // this.endBorder.portionId);
        // 节隐藏
        if (bHidePara === true) {
            if (startParaIndex !== endParaIndex) {
                const contents = this.parent.content;
                if (startPara.content[0] === this.startBorderPortion) {
                    startPara.setHidden(bHidden);
                    // portion都得设置为该状态 143725
                    startPara.setPortionsHidden(bHidden);
                } else {
                    startPara.setNewControlTextHidden(bHidden, startPortionIndex,
                        startParaIndex === endParaIndex ? endPortionIndex : null );
                }

                for (let index = startParaIndex + 1; index < endParaIndex; index++) {
                    const para = contents[index] as Paragraph;
                    if (para) {
                        para.setHidden(bHidden);
                        // portion都得设置为该状态 143725
                        para.setPortionsHidden(bHidden);
                    }
                }

                if (endPara.isHideEndNewcontrol(this.endBorderPortion)) {
                    endPara.setHidden(bHidden);
                    // portion都得设置为该状态 143725
                    endPara.setPortionsHidden(bHidden);
                } else {
                    endPara.setNewControlTextHidden(bHidden, null, endPortionIndex);
                }
            } else {
                const contents = this.parent.content;
                if (contents.length > 1 && startPara.isHideStartNewcontrol(this.startBorderPortion) &&
                    endPara.isHideEndNewcontrol(this.endBorderPortion)) {
                    startPara.setHidden(bHidden);
                    // portion都得设置为该状态 143725
                    startPara.setPortionsHidden(bHidden);
                }

                startPara.setNewControlTextHidden(bHidden, startPortionIndex,
                        startParaIndex === endParaIndex ? endPortionIndex : null );
            }

            return ResultType.Success;
        }

        startPara.setNewControlTextHidden(bHidden, startPortionIndex,
            startParaIndex === endParaIndex ? endPortionIndex : null );

        for (let index = startParaIndex + 1; index < endParaIndex; index++) {
            const para = this.parent.content[index] as Paragraph;
            if ( null != para ) {
                para.setNewControlTextHidden(bHidden, null, null);
            }
        }

        if ( startParaIndex !== endParaIndex ) {
            endPara.setNewControlTextHidden(bHidden, null, endPortionIndex);
        }

        return ResultType.Success;
    }

    public getStartBorder(): ParaNewControlBorder {
        return this.startBorderPortion.content[0] as ParaNewControlBorder;
    }

    public getEndBorder(): ParaNewControlBorder {
        return this.endBorderPortion.content[0] as ParaNewControlBorder;
    }

    public getStartPageIndex(): number {
        const startPara = this.startBorderPortion.paragraph;
        return startPara.getAbsoluteStartPage();
    }

    public getEndPageIndex(): number {
        const endPara = this.endBorderPortion.paragraph;
        return endPara.getAbsoluteStartPage() + endPara.pages.length - 1;
    }

    public setBorder( borderPortion: ParaPortion, bStart: boolean ): void {
        // const border = borderPortion.content[0] as ParaNewControlBorder;

        if ( true === bStart ) {
            this.startBorderPortion = borderPortion;
            // this.placeHolder = borderPortion;
        } else {
            this.endBorderPortion = borderPortion;
        }
    }

    // public setDocumentParent( parent: DocumentContentBase ): void {
    //     if ( parent === this.parent ) {
    //         return ;
    //     }

    //     this.parent = parent;
    // }

    // public getDocumentParent(): DocumentContentBase {
    //     return this.parent;
    // }

    public applyTextProperty( textProperty: TextProperty ): number {
        const startPara = this.startBorderPortion.paragraph; // this.parent.content[startParaIndex] as Paragraph;
        const endPara = this.endBorderPortion.paragraph; // this.parent.content[endParaIndex] as Paragraph;
        const startPortionIndex = startPara.getPortionIndexById(this.startBorderPortion.id);
        const endPortionIndex = endPara.getPortionIndexById(this.endBorderPortion.id);

        let result: number = ResultType.UnEdited;
        for (let index = startPortionIndex; index <= endPortionIndex; index++) {
            const portion = startPara.getContent()[index];
            result = portion.applyProperty(textProperty.copy()) && result;
            portion.recalcInfo.bRecalc = true;
        }

        return result;
    }

    public getType(): NewControlType {
        return this.type;
    }

    public setType( type: number ): void {
        this.type = type;
    }

    public recalculate(): void {
        this.parent.recalculate();
        this.parent.updateCursorXY();
    }

    public getTitle(): string {
        const types = [NewControlType.TextBox, NewControlType.Section, NewControlType.Combox,
            NewControlType.MultiCombox, NewControlType.ListBox, NewControlType.MultiListBox];
        return types.includes(this.type) ?
                this.title : undefined;
    }

    public setTitle(title: string): void {
        const types = [NewControlType.TextBox, NewControlType.Section, NewControlType.Combox,
            NewControlType.MultiCombox, NewControlType.ListBox, NewControlType.MultiListBox];
        if ( types.includes(this.type) && null != title)  {
            this.title = title;
        }
    }

    public isPopWindowNewControl(): boolean {
        // return (NewControlType.TextBox !== this.type && NewControlType.Section !== this.type
        //         && NewControlType.NumberBox !== this.type);
        return false; // 放开所有结构化元素的边框
    }

    public getHistory(): History {
      return this.parent ? this.parent.getHistory() : null;
    }

    public isTrackRevisions(): boolean {
        return (this.parent ? this.parent.isTrackRevisions() : false);
    }

    public removeCertainChar(sRemoveChar: string): boolean {
        let result = false;
        const startPara = this.startBorderPortion.paragraph;
        const endPara = this.endBorderPortion.paragraph;
        const startParaIndex = startPara.index;
        const endParaIndex = endPara.index;
        const startPortionIndex = startPara.getPortionIndexById(this.startBorderPortion.id);
        const endPortionIndex = endPara.getPortionIndexById(this.endBorderPortion.id);
        if (startParaIndex === endParaIndex) {
            const paraContents = startPara.content;
            for (let index = startPortionIndex; index < endPortionIndex; index++) {
                const portion = paraContents[index];

                if (portion && portion.getTextLength()) {
                    const portionContents = portion.content;

                    if (portionContents) {
                        for (let index2 = portionContents.length - 1; index2 >= 0; index2--) {
                            const element = portionContents[index2];
                            if (sRemoveChar === element.content) {
                                portionContents.splice(index2, 1);

                                result = true;
                            }
                        }
                    }
                }
            }
        }

        return result;
    }

    public refreshRecalData(data?: any): void {
        const para = this.startBorderPortion.paragraph;

        if (para) {
            if (data instanceof ChangeNewControlPlaceHolderContent) {
                para.refreshRecalData2(para.getPageByLine(this.startBorderPortion.startLine));
            }
        }
    }

    public refreshRecalData2(): void {
        this.refreshRecalData();
    }

    // public isParaSimpleChanges( changes: ChangeContent[] ): boolean {
    //     const count = changes.length;

    //     for (let index = 0; index < count; index++) {
    //         const element = changes[index].data;
    //         const changeType = element.type;

    //         if ( HistroyItemType.NewControlPlaceHolderContent === changeType
    //             || HistroyItemType.NewControlPlaceHolderPortion === changeType ) {
    //             return true;
    //         } else {
    //             return false;
    //         }
    //     }

    //     return true;
    // }

    /**
     * 删除newControl的内容时，需要删除内容中包含的子元素
     */
    private removeNewControlsInContent(): void {
        if ( NewControlType.Section === this.type ) {
            const newControlManager = this.parent.getNewControlManager();
            const newControl = newControlManager.getNewControlByName(this.getNewControlName());
            if (!newControl) {
                return;
            }
            if ( 0 < newControl.getLeafCount() ) {
                const startBorder = this.getStartBorder();
                const endBorder = this.getEndBorder();
                const startContentPos = startBorder.getBorderPos(this.parent, true);
                const endContentPos = endBorder.getBorderPos(this.parent, true);

                // newControl.deleteLeaf(startContentPos, endContentPos);
                startContentPos.data[startContentPos.depth - 1] += 1;
                endContentPos.data[endContentPos.depth - 1] -= 1;

                newControlManager.remove(startContentPos, endContentPos);
            }
        }
    }
}

/**
 * 当前光标与newControl的位置关系
 */
export enum CursorSelType {
    OutSideNewControl = 0,  // 光标选择在newControl外
    InsideNewControl,       // 光标选择在newControl内
    LayOverNewControl,      // 光标选择覆盖整个newControl
    ForwardOverNewControl,  // 光标选择覆盖newControl的前边框
    BehindOverNewControl,   // 光标选择覆盖newControl的后边框
}

export function findLowerBound( newControls: NewControl[], pos: ParagraphContentPos ): number {
    let middle = -1;
    let index = middle;
    let high = newControls.length;
    let low = 0;

    while ( low < high ) {
        middle = Math.floor(( low + high ) / 2);
        const newControl = newControls[middle];

        if ( null == newControl ) {
            high = middle;
            continue;
        }

        const startPos = newControl.getStartPos();
        const endPos = newControl.getEndPos();
        if (!startPos || !startPos.compare || !endPos || !endPos.compare) {
            return -1;
        }
        if ( -1 === startPos.compare(pos) ) {
            index = middle;
            low = index + 1;
        } else if (1 === endPos.compare(pos)) {
            high = middle;
        } else {
            return middle;
        }
    }

    return index;
}
