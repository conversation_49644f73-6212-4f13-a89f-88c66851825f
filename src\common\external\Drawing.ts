import { DocumentCore } from '../../model/DocumentCore';
import { EmrEditor } from '../../components/editor/Main';
import { ResultType, EquationType, DrawingPropName, NewControlType, SignPicMode } from '../commonDefines';
import ParaDrawing, { ParaEquation } from '../../model/core/Paragraph/ParaDrawing';
import { EquationRefs } from '../MedEquation';
import { ExternalAction } from './ExternalAction';
import { NewControlSignature } from '../../model/core/NewControl/NewControlSignature';
import Paragraph from '../../model/core/Paragraph';

import { genarateBarcode, genarateQRCode } from '../commonMethods';

enum CorrectLevel {
    'L' = 'L',
    'M' = 'M',
    'Q' = 'Q',
    'H' = 'H',
}

export default class Drawing extends ExternalAction {
    private _documentCore: DocumentCore;
    private _host: EmrEditor;
    private _propNames: string[];
    private equationRefs: EquationRefs;
    constructor(host: EmrEditor) {
        super(host.state.documentCore);
        this._host = host;
        this._documentCore = host.state.documentCore;
        this._propNames = [DrawingPropName.Width, DrawingPropName.Height, DrawingPropName.Src,
            DrawingPropName.SizeProtect, DrawingPropName.PreserveAspectRatio, DrawingPropName.CopyProtect,
            DrawingPropName.DelectProtect,
        ];
    }

    /** 根据url插入图片 */
    public addImageWidthUrl(url: string): Promise<string> {
        return new Promise(async (resolve, reject) => {
            try {
                const result = await fetch(url, {method:'get'});
                const fr = new FileReader();
                fr.readAsDataURL(await result.blob());
                fr.onload = () => {
                    resolve(this.addImageWithString(fr.result as string));
                }
                fr.onerror = () => {
                    resolve(ResultType.StringEmpty);
                }
            } catch (error) {
                console.warn('url request error');
                resolve(ResultType.StringEmpty);
            }
        });
    }


    public insertBarcode(sJson: string): string {
        if (!sJson) {
            return ResultType.StringEmpty;
        }
        let obj: {name: string, text: string, width: string, height: string, showText: boolean, alignment: string};
        try {
            obj = JSON.parse(sJson);
        } catch (error) {
            return ResultType.StringEmpty;
        }

        if (!obj.name || !obj.text || !obj.width || !obj.height || isNaN(obj.width as any) || isNaN(obj.height as any)) {
            return ResultType.StringEmpty;
        }

        if (obj.showText != null && typeof obj.showText !== 'boolean' || obj.showText != null && obj.alignment && !['center', 'left', 'right'].includes(obj.alignment)) {
            return ResultType.StringEmpty;
        }

        if (!this._documentCore.canInput()) {
            return ResultType.StringEmpty;
        }
        const textAlign = obj.alignment || 'center';
        let bValid: boolean;
        const url: string = genarateBarcode({
            content: obj.text,
            width: +obj.width,
            height: +obj.height,
            bUse: obj.showText,
            textAlign,
            drawingObjects: this._documentCore.getDrawingObjects(),
            valid: (bVal) => bValid = bVal
        });

        if (!url || bValid === false) {
            return ResultType.StringEmpty;
        }

        // const source = this._documentCore.getDrawingObjects().convertSVGToImageString(url)
        const datas = {
            content: obj.text,
            bUse: obj.showText,
            textAlign,
            externalDataBind: undefined,
        }
        const name = this._documentCore.getBarcodeName(obj.name);
        this._documentCore.addInlineImage(+obj.width, +obj.height, url, name, 3, url, null, null, datas);
        this._host.handleRefresh();
        return name;
    }

    public async insertQRCode(sJson: string): Promise<string> {
        if (!sJson) {
            return ResultType.StringEmpty;
        }
        let obj: {name: string, text: string};
        try {
            obj = JSON.parse(sJson);
        } catch (error) {
            return ResultType.StringEmpty;
        }
        if (!obj.name || !obj.text || !this._documentCore.canInput()) {
            return ResultType.StringEmpty;
        }
        
        const url: string = await new Promise((resolve) => {
            genarateQRCode({
                content: obj.text,
                errorCL: CorrectLevel.H, // 'H',
                // type: 'svg',
                // quality: 0.3,
                // margin: 0,
                // color: {
                //     dark:"#000000",
                //     light:"#FFFFFF"
                // }
                callback: (err, data) => {
                    if (err) {
                        console.log(err);
                        return;
                    }
                    resolve(data);
                },
            });
        });

        if (!url) {
            return ResultType.StringEmpty;
        }

        const source = this._documentCore.getDrawingObjects().convertSVGToImageString(url)
        const datas = {
            content: obj.text,
            errorCL: CorrectLevel.H,
            externalDataBind: undefined,
        }
        const name = this._documentCore.getBarcodeName(obj.name);
        this._documentCore.addInlineImage(50, 50, source, name, 3, source, null, null, datas);
        this._host.handleRefresh();
        return name;
    }


    public addImageWithString(base64String: string, isEditable: boolean = false): Promise<string> {
        return new Promise((resolve, reject) => {
            if (!base64String || !this._documentCore.canInput()) {
                this.resetAdminMode();
                return resolve(ResultType.StringEmpty);
            }

            const newControl = this._documentCore.getCurrentNewControl();
            // console.log(newControl)
            const parentControl = newControl != null ? newControl.getParent() : null;
            if ((newControl != null && newControl.getType() === NewControlType.SignatureBox) ||
                (parentControl != null && parentControl.getType() === NewControlType.SignatureBox)) {
                this.resetAdminMode();
                return resolve(ResultType.StringEmpty);
            }

            const image = new Image();
            image.src = base64String;
            image.onload = () => {
                const width = image.width;
                const height = image.height;
                const size: {height: number, width: number} = {height, width};
                this.checkEquationSizeLimit(size);

                
                const name = isEditable ? this._documentCore.addInlineImage(size.width, size.height, base64String, '', EquationType.EditableSvg, '') :  this._documentCore.addInlineImage(size.width, size.height, base64String);
                
                if (name) {
                    this.resetAdminMode();
                    this._host.handleRefresh();
                    return resolve(name);
                }
                this.resetAdminMode();
                return resolve(ResultType.StringEmpty);
            };

            image.onerror = () => {
                this.resetAdminMode();
                return resolve(ResultType.StringEmpty);
            };
        });
    }

    public async addSignaturePicToSectionWithStream(sStructName: string, nMark: number, sBase64: string,
                                                    nWidht: number, nHeight: number): Promise<string> {
        if (!sBase64 || !(nHeight > 0 || nHeight === 0) || !(nWidht > 0 || nWidht === 0)) {
            this.resetAdminMode();
            return ResultType.StringEmpty;
        }

        if (!this._documentCore.canInput()) {
            this.resetAdminMode();
            return ResultType.StringEmpty;
        }

        if (sStructName) {
            if (typeof nMark !== 'number' || !(nMark >= 0 && nMark <= 3)) {
                this.resetAdminMode();
                return ResultType.StringEmpty;
            }
            const res = this._documentCore.jumpInStruct(sStructName, nMark);
            if (res !== ResultType.Success) {
                this.resetAdminMode();
                return ResultType.StringEmpty;
            }
        }

        if (nWidht === 0 || nHeight === 0) {
            this.resetAdminMode();
            return this.addImageWithString(sBase64);
        }

        
        const name = this._documentCore.addInlineImage(nWidht, nHeight, sBase64);
        
        if (name) {
            this.resetAdminMode();
            this._host.handleRefresh();
            return name;
        }
        this.resetAdminMode();
        return ResultType.StringEmpty;
    }

    public setImageName(sName: string, sNewName: string): number {
        if (!sName || !sNewName) {
            return ResultType.ParamError;
        }
        
        const res = this._documentCore.setDrawingName(sName, sNewName);
        
        return res;
    }

    public deleteImage(sName: string): number {
        if (!sName) {
            return ResultType.ParamError;
        }
        
        const result = this._documentCore.deleteDrawing(sName, true);
        
        if (result === ResultType.Success) {
            this._host.handleRefresh();
        }

        return result;
    }

    public setImageSize(sName: string, nWidth: number, nHeight: number): number {
        if (!sName || typeof nWidth !== 'number' || typeof nHeight !== 'number' || nWidth < 0 || nHeight < 0) {
            return ResultType.ParamError;
        }
        
        const result = this._documentCore.setDrawingProp(sName, {width: nWidth, height: nHeight}, true);
        
        if (result === ResultType.Success) {
            this._host.handleRefresh();
        }

        return result;
    }

    public getCurrentImageName(): string {
        const draw = this._documentCore.getSelectedDrawing();
        if (!draw) {
            return ResultType.StringEmpty;
        }

        return draw.name;
    }

    public setImageDeleteProtection(sName: string, bDeleteProtect: boolean): number {
        if (!sName) {
            return ResultType.ParamError;
        }
        
        const result = this._documentCore.setDrawingProp(sName, {deleteLocked: bDeleteProtect});
        
        return result;
    }

    public setImageCopyProtection(sName: string, bCopyProtect: boolean): number {
        if (!sName) {
            return ResultType.ParamError;
        }
        
        const result = this._documentCore.setDrawingProp(sName, {copyProtect: bCopyProtect});
        
        return result;
    }

    public setImageCustomProperty(sImageName: string, sPropName: string, sPropValue: any): number {
        if (!sImageName || !sPropName) {
            return ResultType.ParamError;
        }
        if (!this._propNames.includes(sPropName)) {
            return ResultType.ParamError;
        }
        if (sPropName === DrawingPropName.Src && (typeof sPropValue !== 'string' || !sPropValue)) {
            return ResultType.ParamError;
        }
        const obj = {};
        if (sPropName === DrawingPropName.Width || sPropName === DrawingPropName.Height) {
            if (typeof sPropValue !== 'number' && sPropValue <= 0) {
                return ResultType.ParamError;
            }
            const flag = this.setWidthOrHeight(sImageName, sPropName, sPropValue, obj);
            if (flag === false) {
                return ResultType.Failure;
            }
        }
        const propNames: string[] = [DrawingPropName.SizeProtect, DrawingPropName.PreserveAspectRatio,
            DrawingPropName.CopyProtect, DrawingPropName.DelectProtect,
        ];
        if (propNames.includes(sPropName) && typeof sPropValue !== 'boolean') {
            return ResultType.ParamError;
        }

        obj[this._getDrawingPropName(sPropName)] = sPropValue;
        
        let bActiveControl: boolean = false;
        if (sPropName === DrawingPropName.Height || sPropName === DrawingPropName.Src) {
            bActiveControl = true;
        }
        const result = this._documentCore.setDrawingProp(sImageName, obj, bActiveControl);
        
        if (result === ResultType.Success && (sPropName === DrawingPropName.Width
            || sPropName === DrawingPropName.Height || sPropName === DrawingPropName.Src)) {
            this._host.handleRefresh();
        }
        return result;
    }

    public getImageCustomProperty(sImageName: string, sPropName: string): string | number | boolean {
        if (!sImageName || !sPropName) {
            return ResultType.ParamError;
        }
        if (!this._propNames.includes(sPropName)) {
            return ResultType.Failure;
        }

        const draw = this._documentCore.getDrawingByName(sImageName);
        if (!draw) {
            return ResultType.ParamError;
        }

        const result = draw[this._getDrawingPropName(sPropName)];
        if (result === undefined) {
            return ResultType.ParamError;
        }

        // if (typeof result === 'boolean') {
        //     return result ? 1 : 0;
        // }

        return result;
    }

    public getAllImagesByCurrentDoc(): string {
        return this._documentCore.getImageNames()
            .join(',');
    }

    public insertMedicalformula(nType: number, sID: string, sJson: string): number {
        const drawing = this._documentCore.getDrawingObjects();
        if (drawing.checkUniqueImageName(sID) === false) {
            return ResultType.ParamError;
        }

        let props: any;
        try {
            props = JSON.parse(sJson);
        } catch (e) {
            return ResultType.ParamError;
        }

        if (!this._documentCore.canInput()) {
            return ResultType.Failure;
        }

        
        const svgEquationElemStr = this.setMedEquation(nType, props);
        const src = this._documentCore.getDrawingObjects()
             .convertSVGToImageString(svgEquationElemStr);

        const name = this._documentCore.addInlineImage(106, 50, src, sID, nType, svgEquationElemStr);
        
        if (name) {
            this._documentCore.getDrawingByName(name)
                .setPreload(true);
            this._documentCore.removeSelection();
            this._host.handleRefresh();
            return ResultType.Success;
        }

        return ResultType.Failure;
    }

    public setMedicalformulaText(sID: string, sJson: string): number {
        let props: any;
        try {
            props = JSON.parse(sJson);
        } catch (e) {
            return ResultType.ParamError;
        }
        const drawEquation = this._documentCore.getDrawingByName(sID) as ParaEquation;
        if (!drawEquation) {
            return ResultType.Failure;
        }

        const equationElem = this.setMedEquation(drawEquation.equationType, props, drawEquation.equationElem);
        if (!equationElem) {
            return ResultType.Failure;
        }

        
        const result = this._documentCore.getDrawingObjects()
             .setEquationElem(drawEquation.getDrawingName(), equationElem);

        drawEquation.setPreload(true);
        
        if (ResultType.Success === result) {
            this._documentCore.removeSelection();
            this._host.handleRefresh();
            return ResultType.Success;
        }

        return result;
    }

    public addSignPicToControlWithString(sName: string, index: number, sPicData: string): Promise<number> {
        return new Promise((resolve, reject) => {
            if (!sName || typeof index !== 'number' || typeof sPicData !== 'string') {
                this.resetAdminMode();
                return resolve(ResultType.ParamError);
            }

            const image = new Image();
            image.src = sPicData;
            image.onload = () => {
                const width = image.width;
                const height = image.height;

                const curPara = this._documentCore.getCurrentParagraph(); // this is just used to get topDoc
                const topDocument = curPara.getTopDocument();

                const curImage = new ParaDrawing(topDocument, width, height, sPicData);
                // curImage.setPreload(true);
                curImage.setSizeLocked(true);

                const newControl = this._documentCore.getNewControlByName(sName);
                // console.log(newControl);

                let subTextStructName = null;
                if (newControl != null && newControl.getType() === NewControlType.SignatureBox) {
                    if (newControl instanceof NewControlSignature) {
                        subTextStructName = newControl.getSubTextStructNameBySignCount(index);
                    }
                }
                // console.log(subTextStructName)

                const result = this._documentCore.setNewControlImage(subTextStructName, curImage);
                if (result === ResultType.Success) {
                    this._host.handleRefresh();
                }

                this.resetAdminMode();
                return resolve(result);
            };

            image.onerror = () => {
                this.resetAdminMode();
                return resolve(ResultType.Failure);
            };
        });
    }

    public addSignPicToControlWithString2(sName: string, index: number, sPicData: string,
                                          sJson?: string): Promise<number> {
        return new Promise((resolve, reject) => {
            if (!sName || typeof index !== 'number' || typeof sPicData !== 'string') {
                this.resetAdminMode();
                return resolve(ResultType.ParamError);
            }

            const image = new Image();
            image.src = sPicData;
            image.onload = () => {
                let width = image.width;
                let height = image.height;

                const cursorPara = this._documentCore.getCurrentParagraph(); // this is just used to get topDoc
                const topDocument = cursorPara.getTopDocument();

                const newControl = this._documentCore.getNewControlByName(sName);
                let subTextStructName = null;
                if (newControl != null && newControl.getType() === NewControlType.SignatureBox) {
                    if (newControl instanceof NewControlSignature) {
                        subTextStructName = newControl.getSubTextStructNameBySignCount(index);
                    }
                }
                // console.log(newControl);

                const curPara = (newControl != null) ? newControl.getParagraph() : null;
                // console.log(curPara)

                if (sJson != null && curPara instanceof Paragraph) {
                    const sJsonObj = JSON.parse(sJson);
                    const {mode, width: argWidth, height: argHeight} = sJsonObj;
                    // console.log(mode)

                    if (mode === SignPicMode.LineHeight) {
                        const lines = curPara.getLines();

                        // get the substruct that sign pic will be added into
                        const subStruct = this._documentCore.getNewControlByName(subTextStructName);

                        // startportion is used instead of 'the portion next' since it's [] yet
                        const startPortion = subStruct.getStartBorderPortion();
                        if (lines != null && startPortion != null) {
                            // console.log(startPortion.content[0])

                            // need 'paratext/paraborder' level of a portion to determine para line index
                            const lineIndex = startPortion.getLineByPos(0); // use '[' to get para line index

                            if (lineIndex >= 0) {
                                const lineHeight = lines[lineIndex].metrics.textHeight;

                                if (height > lineHeight) {
                                    const ratio = width / height;
                                    height = lineHeight;
                                    width = ratio * height;
                                }
                            }

                        } else {
                            // tslint:disable-next-line: no-console
                            console.warn('startPortion is empty');
                        }

                    } else if (mode === SignPicMode.CustomHeight) {
                        if (argWidth != null && argHeight != null) {
                            width = argWidth;
                            height = argHeight;
                        }
                    }
                }

                const curImage = new ParaDrawing(topDocument, width, height, sPicData);
                // curImage.setPreload(true);
                curImage.setSizeLocked(true);

                const result = this._documentCore.setNewControlImage(subTextStructName, curImage);
                if (result === ResultType.Success) {
                    this._host.handleRefresh();
                }

                this.resetAdminMode();
                return resolve(result);
            };

            image.onerror = () => {
                this.resetAdminMode();
                return resolve(ResultType.Failure);
            };
        });
    }

    // public addSignContentToControl(sName: string, sJson: string): Promise<number> {
    //     return new Promise((resolve, reject) => {
    //         if (!sName || !sJson) {
    //             this.resetAdminMode();
    //             return resolve(ResultType.ParamError);
    //         }

    //         let obj;
    //         let type;
    //         try {
    //             obj = JSON.parse(sJson);
    //             if (typeof obj.name !== 'string' || !obj.content || !obj.content.type || !obj.content.data) {
    //                 return resolve(ResultType.ParamError);
    //             }
    //             type = obj.content.type;
    //         } catch (error) {
    //             return resolve(ResultType.ParamError);
    //         }

    //         if (!obj.name || (1 !== type && 2 !== type && 3 !== type)) {
    //             return resolve(ResultType.ParamError);
    //         }

    //         const data = obj.content.data;
    //         if (1 === type) {
    //             if (typeof data !== 'string' || !data) {
    //                 return resolve(ResultType.ParamError);
    //             }
    //             const newControl = this._documentCore.getNewControlByName(sName);
    //             const subStruct = (newControl ? (newControl as NewControlSignature).getSubTextStructBySignName(obj.name)
    //                                     : null);
    //             if (!newControl || !newControl.isSignatureBox() || !subStruct) {
    //                 return resolve(ResultType.Failure);
    //             }

    //             const result = this._documentCore.setNewControlText(obj.name, data);

    //             if (result === ResultType.Success) {
    //                 if (obj.helpTips) {
    //                     subStruct.setTipsContent(obj.helpTips);
    //                 }
    //                 this._host.handleRefresh();
    //             }
    //             resolve(result);
    //         } else if (2 === type) {
    //             if (!data.source // || typeof data.width !== 'number' || typeof data.height !== 'number'
    //                 || (null != data.width && (0 >= data.width || typeof data.width !== 'number'))
    //                 || (null != data.height && (0 >= data.height || typeof data.height !== 'number'))
    //             ) {
    //                 return resolve(ResultType.ParamError);
    //             }

    //             const image = new Image();
    //             image.src = data.source;
    //             image.onload = () => {
    //                 const width = image.width;
    //                 const height = image.height;

    //                 const cursorPara = this._documentCore.getCurrentParagraph(); // this is just used to get topDoc
    //                 const topDocument = cursorPara.getTopDocument();

    //                 const newControl = this._documentCore.getNewControlByName(sName);
    //                 const subStruct = (newControl ? (newControl as NewControlSignature).
    //                                                     getSubTextStructBySignName(obj.name)
    //                                     : null);

    //                 if (!newControl || !newControl.isSignatureBox() || !subStruct) {
    //                     return resolve(ResultType.Failure);
    //                 }

    //                 if (null != data.width && 0 < data.width) {
    //                     if (null == data.height) {
    //                         data.height = data.width * height / width;
    //                     }
    //                 }

    //                 if (null != data.height && 0 < data.height) {
    //                     if (null == data.width) {
    //                         data.width = width * data.height / height;
    //                     }
    //                 }

    //                 const curImage = new ParaDrawing(topDocument, (data.width || width),
    //                                 (data.height || height), data.source);
    //                 curImage.setSizeLocked(true);
    //                 curImage.setPreserveAspectRatio(false);

    //                 const result = this._documentCore.setNewControlImage(obj.name, curImage);
    //                 if (result === ResultType.Success) {
    //                     if (obj.helpTips) {
    //                         subStruct.setTipsContent(obj.helpTips);
    //                     }
    //                     this._host.handleRefresh();
    //                 }

    //                 this.resetAdminMode();
    //                 return resolve(result);
    //             };

    //             image.onerror = () => {
    //                 this.resetAdminMode();
    //                 return resolve(ResultType.Failure);
    //             };
    //         } else if (3 === type) {
    //             if (!data || !Array.isArray(data)) {
    //                 return resolve(ResultType.ParamError);
    //             }

    //             const newControl = this._documentCore.getNewControlByName(sName);
    //             const subStruct = (newControl ? (newControl as NewControlSignature).getSubTextStructBySignName(obj.name)
    //                                         : null);
    //             if (!newControl || !newControl.isSignatureBox() || !subStruct) {
    //                 return resolve(ResultType.Failure);
    //             }

    //             let lastImageIndex = -1;
    //             data.forEach((value, index) => {
    //                 if (2 === value.type) {
    //                     lastImageIndex = index;
    //                 }
    //             });

    //             if ( -1 !== lastImageIndex ) {
    //                 data.forEach((value, index) => {
    //                     if (2 === value.type) {
    //                         const item = value.data;
    //                         const image = new Image();
    //                         image.src = item.source;
    //                         image.onload = () => {
    //                             const width = image.width;
    //                             const height = image.height;
    //                             if (null == item.width && null == item.height) {
    //                                 item.width = width;
    //                                 item.height = height;
    //                             } else {
    //                                 if (null != item.width && 0 < item.width) {
    //                                     if (null == item.height) {
    //                                         item.height = item.width * height / width;
    //                                     }
    //                                 }

    //                                 if (null != item.height && 0 < item.height) {
    //                                     if (null == item.width) {
    //                                         item.width = width * item.height / height;
    //                                     }
    //                                 }
    //                             }

    //                             if (index === lastImageIndex) {
    //                                 const result = this._documentCore.setNewControlMixData(obj.name, data);
    //                                 if (result === ResultType.Success) {
    //                                     if (obj.helpTips) {
    //                                         subStruct.setTipsContent(obj.helpTips);
    //                                     }
    //                                     this._host.handleRefresh();
    //                                 }

    //                                 this.resetAdminMode();
    //                                 return resolve(result);
    //                             }
    //                         };

    //                         image.onerror = () => {
    //                             this.resetAdminMode();
    //                             return resolve(ResultType.Failure);
    //                         };
    //                     }
    //                 });
    //             } else {
    //                 const result = this._documentCore.setNewControlMixData(obj.name, data);
    //                 if (result === ResultType.Success) {
    //                     if (obj.helpTips) {
    //                         subStruct.setTipsContent(obj.helpTips);
    //                     }
    //                     this._host.handleRefresh();
    //                 }

    //                 this.resetAdminMode();
    //                 return resolve(result);
    //             }
    //         }
    //     });
    // }
    public addSignContentToControl(sName: string, sJson: string | string[]): Promise<number> {
        return new Promise(async (resolve, reject) => {
            try {
                if (!sName || !sJson) {
                    this.resetAdminMode();
                    return resolve(ResultType.ParamError);
                }
        
                const jsonArray: any[] = [];
        
                try {
                    if (typeof sJson === 'string') {
                        const parsed = JSON.parse(sJson);
                        if (Array.isArray(parsed)) {
                            jsonArray.push(...parsed);
                        } else {
                            jsonArray.push(parsed);
                        }
                    } else if (Array.isArray(sJson)) {
                        jsonArray.push(...sJson);
                    } else {
                        return resolve(ResultType.ParamError);
                    }
                } catch (error) {
                    return resolve(ResultType.ParamError);
                }
        
                let finalResult = ResultType.Success;
                
                for (const obj of jsonArray) {
                    if (!obj || typeof obj.name !== 'string' || !obj.content || !obj.content.type || !obj.content.data) {
                        this.resetAdminMode();
                        return resolve(ResultType.ParamError);
                    }
        
                    const type = obj.content.type;
                    const data = obj.content.data;
        
                    const newControl = this._documentCore.getNewControlByName(sName);
                    const subStruct = newControl ? (newControl as NewControlSignature).getSubTextStructBySignName(obj.name) : null;
        
                    if (!newControl || !newControl.isSignatureBox() || !subStruct) {
                        this.resetAdminMode();
                        return resolve(ResultType.Failure);
                    }
        
                    if (type === 1) {
                        const result = this._documentCore.setNewControlText(obj.name, data);
                        if (result === ResultType.Success && obj.helpTips) {
                            subStruct.setTipsContent(obj.helpTips);
                            this._host.handleRefresh();
                        }
                        if (result !== ResultType.Success) {
                            finalResult = result;
                            break;
                        }
                    }
        
                    else if (type === 2) {
                        try {
                            const result = await new Promise<number>((res) => {
                                const image = new Image();
                                image.src = data.source;
                                
                                const timeoutId = setTimeout(() => {
                                    res(ResultType.Failure);
                                }, 5000); // 5秒超时保护
        
                                image.onload = () => {
                                    clearTimeout(timeoutId);
                                    let width = image.width;
                                    let height = image.height;
                                    const mode = data.mode;
        
                                    if (mode === SignPicMode.LineHeight) {
                                        const curPara = newControl.getParagraph();
                                        const lines = curPara?.getLines();
                                        const startPortion = subStruct.getStartBorderPortion();
        
                                        if (lines && startPortion) {
                                            const lineIndex = startPortion.getLineByPos(0);
                                            if (lineIndex >= 0) {
                                                const lineHeight = lines[lineIndex].metrics.textHeight;
                                                const ratio = width / height;
                                                height = lineHeight;
                                                width = lineHeight * ratio;
                                            }
                                        }
                                    } else if (mode === SignPicMode.CustomHeight) {
                                        if (data.width && data.height) {
                                            width = data.width;
                                            height = data.height;
                                        }
                                    } else {
                                        if (data.width && !data.height) {
                                            height = data.width * image.height / image.width;
                                            width = data.width;
                                        } else if (!data.width && data.height) {
                                            width = data.height * image.width / image.height;
                                            height = data.height;
                                        } else if (data.width && data.height) {
                                            width = data.width;
                                            height = data.height;
                                        }
                                    }
        
                                    const topDocument = this._documentCore.getCurrentParagraph().getTopDocument();
                                    const curImage = new ParaDrawing(topDocument, width, height, data.source);
                                    curImage.setSizeLocked(true);
                                    curImage.setPreserveAspectRatio(false);
        
                                    const result = this._documentCore.setNewControlImage(obj.name, curImage);
                                    if (result === ResultType.Success && obj.helpTips) {
                                        subStruct.setTipsContent(obj.helpTips);
                                        this._host.handleRefresh();
                                    }
        
                                    res(result);
                                };
        
                                image.onerror = () => {
                                    clearTimeout(timeoutId);
                                    res(ResultType.Failure);
                                };
                            });

                            if (result !== ResultType.Success) {
                                finalResult = result;
                                break;
                            }
                        } catch (error) {
                            finalResult = ResultType.Failure;
                            break;
                        }
                    }
        
                    else if (type === 3) {
                        try {
                            const topDocument = this._documentCore.getCurrentParagraph().getTopDocument();
                            const curPara = newControl.getParagraph();
                            const lines = curPara?.getLines();
                            const startPortion = subStruct.getStartBorderPortion();
                            const lineIndex = startPortion?.getLineByPos(0);
                            const lineHeight = (lines && lineIndex >= 0) ? lines[lineIndex].metrics.textHeight : null;
        
                            await Promise.all(data.map(async (item) => {
                                if (item.type === 2 && item.data?.source) {
                                    const imgData = item.data;
                                    await new Promise<void>((resImg) => {
                                        const img = new Image();
                                        img.src = imgData.source;
                                        
                                        const timeoutId = setTimeout(() => resImg(), 5000); // 5秒超时保护
        
                                        img.onload = () => {
                                            clearTimeout(timeoutId);
                                            let width = img.width;
                                            let height = img.height;
        
                                            const mode = imgData.mode;
        
                                            if (mode === SignPicMode.LineHeight && lineHeight) {
                                                const ratio = width / height;
                                                height = lineHeight;
                                                width = ratio * height;
                                            } else if (mode === SignPicMode.CustomHeight) {
                                                if (imgData.width && imgData.height) {
                                                    width = imgData.width;
                                                    height = imgData.height;
                                                }
                                            } else {
                                                if (imgData.width && !imgData.height) {
                                                    height = imgData.width * img.height / img.width;
                                                    width = imgData.width;
                                                } else if (!imgData.width && imgData.height) {
                                                    width = imgData.height * img.width / img.height;
                                                    height = imgData.height;
                                                } else if (imgData.width && imgData.height) {
                                                    width = imgData.width;
                                                    height = imgData.height;
                                                }
                                            }
        
                                            imgData.width = width;
                                            imgData.height = height;
        
                                            resImg();
                                        };
        
                                        img.onerror = () => {
                                            clearTimeout(timeoutId);
                                            resImg();
                                        };
                                    });
                                }
                            }));
        
                            const result = this._documentCore.setNewControlMixData(obj.name, data);
                            if (result === ResultType.Success && obj.helpTips) {
                                subStruct.setTipsContent(obj.helpTips);
                                this._host.handleRefresh();
                            }
                            
                            if (result !== ResultType.Success) {
                                finalResult = result;
                                break;
                            }
                        } catch (error) {
                            finalResult = ResultType.Failure;
                            break;
                        }
                    }
                }

                this.resetAdminMode();
                resolve(finalResult);
            } catch (error) {
                this.resetAdminMode();
                resolve(ResultType.Failure);
            }
        });
    }
    
    

    private setWidthOrHeight(imageName: string, name: string, value: number, obj: any): boolean {
        const draw = this._documentCore.getDrawingByName(imageName);
        if (!draw) {
            return false;
        }

        if (draw.sizeLocked) {
            return false;
        }

        if (draw.preserveAspectRatio !== true) {
            return true;
        }

        if (name === DrawingPropName.Height) {
            obj.width = Math.round(value / draw.height * draw.width);
        } else {
            obj.height = Math.round(value / draw.width * draw.height);
        }
        return true;
    }

    // private setMedEquation(type: number, props: any, equationElm?: string): string {
    //     // console.log(medEquation.fraction)
    //     const reg = /<tspan class="equation-text">[\s\S]*?<\/tspan>/g;
    //     const attr = />[\s\S]*?<\/tspan>/;
    //     // 值从上面开始，从左边开始
    //     let val: object;
    //     const keys = Object.keys(props);
    //     if (keys.length === 0) {
    //         return ResultType.StringEmpty;
    //     }
    //     if (!equationElm) {
    //         equationElm = this.getEquationStringByType(type);
    //         if (!equationElm) {
    //             return ResultType.StringEmpty;
    //         }
    //     }

    //     switch (type) {
    //         case EquationType.Ordinary:
    //             val = {
    //                 value1: props.value1,
    //                 value2: props.value2,
    //                 value3: props.value3,
    //                 value4: props.value4,
    //             };
    //             break;
    //         case EquationType.Fraction:
    //             val = {
    //                 value1: props.value1,
    //                 value2: props.value2,
    //             };
    //             break;
    //         case EquationType.Menstruation:
    //             val = {
    //                 value1: props.value2 === undefined ? undefined : props.value2 + '岁',  // 左边
    //                 value2: props.value1 !== undefined ? props.value1 + '天' : undefined, // 顶部
    //                 value3: props.value4 === undefined ? undefined : props.value4 + '岁', // 右边
    //                 value4: props.value3 === undefined ? undefined : props.value3 + '天', // 底部
    //             };
    //             break;
    //         case EquationType.PupilMapping:
    //             val = {
    //                 value1: props.value1,
    //                 value2: props.value2,
    //                 value3: props.value3,
    //                 value4: props.value4,
    //                 value5: props.value5,
    //                 value6: props.value6,
    //                 value7: props.value7,
    //             };
    //             break;
    //         default:
    //             return ResultType.StringEmpty;
    //     }

    //     let index = 1;
    //     let flag = false;
    //     equationElm = equationElm.replace(reg, (str) => {
    //         const value = val['value' + index++];
    //         if (value === undefined) {
    //             return str;
    //         }
    //         flag = true;
    //         return str.replace(attr, '>' + value + '</tspan>');
    //     });

    //     if (flag === false) {
    //         return ResultType.StringEmpty;
    //     }

    //     return equationElm;
    // }

    private setMedEquation(type: number, props: any, equationElm?: string): string {
        const textReg = /<text[^>]*>([\s\S]*?)<\/text>/g; // 正则表达式匹配 <text> 元素
        const textAttr = />([\s\S]*?)<\/text>/; // 捕获 <text> 元素中的内容

        const spanReg = /<tspan class="equation-text">[\s\S]*?<\/tspan>/g;
        const spanAttr = />[\s\S]*?<\/tspan>/;
    
        let val: object;
        const keys = Object.keys(props);
        if (keys.length === 0) {
            return ResultType.StringEmpty;
        }
        if (!equationElm) {
            equationElm = this.getEquationStringByType(type);
            if (!equationElm) {
                return ResultType.StringEmpty;
            }
        }

        let vReplaceType = 1;
    
        switch (type) {
            case EquationType.Ordinary: //1
                val = {
                    value1: props.value1,
                    value2: props.value2,
                    value3: props.value3,
                    value4: props.value4,
                };

                vReplaceType = 2;
                break;
            case EquationType.Fraction: //0

                val = {
                    value1: props.value1,
                    value2: props.value2,
                };

                vReplaceType = 2;
                break;
            case EquationType.Menstruation: //2

                val = {
                    value1: props.value2 === undefined ? undefined : props.value2 + '岁',  // 左边
                    value2: props.value1 !== undefined ? props.value1 + '天' : undefined, // 顶部
                    value3: props.value4 === undefined ? undefined : props.value4 + '岁', // 右边
                    value4: props.value3 === undefined ? undefined : props.value3 + '天', // 底部
                };

                vReplaceType = 2;
                break;
            case EquationType.PupilMapping://3
                val = {
                    value1: props.value1,
                    value2: props.value2,
                    value3: props.value3,
                    value4: props.value4,
                    value5: props.value5,
                    value6: props.value6,
                    value7: props.value7,
                };
                break;
            case EquationType.FetalHeartChart://4
                val = {
                    value1: props.value1,
                    value2: props.value2,
                    value3: props.value3,
                    value4: props.value4,
                    value5: props.value5,
                    value6: props.value6,
                };
                break;
            case EquationType.LightPositioningMap://5
                val = {
                    value1: props.value1,
                    value2: props.value2,
                    value3: props.value3,
                    value4: props.value4,
                    value5: props.value5,
                    value6: props.value6,
                    value7: props.value7,
                    value8: props.value8,
                    value9: props.value9,
                };
                break;
           
            case EquationType.ToothBitMap: //6
                val = {
                    value1: props.value1,
                    value2: props.value2,
                    value3: props.value3,
                    value4: props.value4,
                    value5: props.value5,
                    value6: props.value6,
                };
                break;
            case EquationType.Rule://8
                val = {
                    value1: props.value1,
                };
                break;
            case EquationType.Menstruation2://9
                val = {
                    value1: props.value1,
                    value2: props.value2,
                    value3: props.value3,
                    value4: props.value4,
                };
                break;
            case EquationType.Menstruation3://10
                val = {
                    value1: props.value1,
                    value2: props.value2,
                    value3: props.value3,
                    value4: props.value4,
                };
                break;
            case EquationType.Menstruation4://11
                val = {
                    value1: props.value1,
                    value2: props.value2,
                    value3: props.value3,
                };
                break;
            case EquationType.DiseasedLowerTeeth://12
                val = {
                    value1: props.value1,
                    value2: props.value2,
                    value3: props.value3,
                };
                break;
            case EquationType.DiseasedUpperTeeth://13
                val = {
                    value1: props.value1,
                    value2: props.value2,
                    value3: props.value3,
                };
                break;
            case EquationType.PermanentToothBitmap://14
                val = {
                    value1: props.value1,
                    value2: props.value2,
                    value3: props.value3,
                    value4: props.value4,
                };
                break;
            case EquationType.DeciduousToothBitmap://15
                val = {
                    value1: props.value1,
                    value2: props.value2,
                    value3: props.value3,
                    value4: props.value4,
                };
                 break;
            default:
                return ResultType.StringEmpty;
        }
    
        // Initialize the index
        let index = 1;
        let flag = false;
    
        if(vReplaceType ===  1){
            // Replace <text> elements with values from val
            equationElm = equationElm.replace(textReg, (str) => {
                const value = val['value' + index++];
                if (value === undefined) {
                    return str;
                }
                flag = true;
                return str.replace(textAttr, '>' + value + '</text>');
            });
        }else if(vReplaceType === 2)
        {
            equationElm = equationElm.replace(spanReg, (str) => {

            const value = val['value' + index++];
            if (value === undefined) {
                return str;
            }
            flag = true;

            return str.replace(spanAttr, '>' + value + '</tspan>');
        });
        }
    
        if (flag === false) {
            return ResultType.StringEmpty;
        }
    
        return equationElm;
    }
    

    private getEquationStringByType(type: number): string {
        const equation = this.getEquationRefs();
        return equation.getEquationStringByType(type);
    }

    private getEquationRefs(): EquationRefs {
        if (!this.equationRefs) {
            this.equationRefs = new EquationRefs();
        }
        return this.equationRefs;
    }

    private _getDrawingPropName(sPropName: string): string {
        switch (sPropName) {
            case DrawingPropName.Width:
                return 'width';
            case DrawingPropName.Height:
                    return 'height';
            case DrawingPropName.Src:
                return 'src';
            case DrawingPropName.DelectProtect:
                return 'deleteLocked';
            case DrawingPropName.SizeProtect:
                return 'sizeLocked';
            case DrawingPropName.CopyProtect:
                return 'copyProtect';
            case DrawingPropName.PreserveAspectRatio:
                return 'preserveAspectRatio';
        }
    }

    private checkEquationSizeLimit(size: {height: number, width: number}): void {
        if (size != null) {
            const documentCore = this._documentCore;
            const maxWidth = documentCore.getMaxWidth(true);
            const maxHeight = documentCore.getMaxHeight(true);

            if (size.width > maxWidth) {
                const wRatio = maxWidth / size.width;
                const hRatio = maxHeight / size.height;
                if (size.height * wRatio > maxHeight) {
                    size.height = maxHeight;
                    size.width *= hRatio;
                } else {
                    size.width = maxWidth;
                    size.height *= wRatio;
                }
            } else if (size.height > maxHeight) {
                const wRatio = maxWidth / size.width;
                const hRatio = maxHeight / size.height;
                if (size.width * hRatio > maxWidth) {
                    size.width = maxWidth;
                    size.height *= wRatio;
                } else {
                    size.height = maxHeight;
                    size.width *= hRatio;
                }
            }
        }
    }
}
