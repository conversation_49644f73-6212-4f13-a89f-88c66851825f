#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const pkg = require(path.resolve(__dirname, '../dist/package.json'));

let entryContent = fs.readFileSync(path.resolve(__dirname, '../dist/index.js'), 'utf8');

entryContent = `${entryContent}\nwindow.__EMR_EDITOR_VER__="${pkg.version}";\n`;
fs.writeFileSync(path.resolve(__dirname, '../dist/index.js'), entryContent, 'utf8');
