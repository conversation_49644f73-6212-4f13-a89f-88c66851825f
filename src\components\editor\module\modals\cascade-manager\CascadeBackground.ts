import {
    GlobalEvent as gEvent,
} from '../../../../../common/GlobalEvent';

export default class CascadeBackground {
    private host: any;
    private newControls: any;
    private dom: HTMLDivElement;
    private bVisible: boolean;
    constructor(newControls: any, host: any, dom: any) {
        this.newControls = newControls;
        this.bVisible = false;
        this.host = host;
        this.render(dom);
        this.addEvent();
    }

    public removeEvent(): void {
        gEvent.deleteEvent(this.host.docId, 'resetCascadeManagerBg', this.renderNewControlBackground);
    }

    private render(dom: HTMLDivElement): void {
        const g: any = document.createElementNS('http://www.w3.org/2000/svg', 'g');
        g.setAttribute('class', 'cascade-manager-background');
        dom.insertBefore(g, dom.children[2]);
        this.dom = g;
    }

    private addEvent(): void {
        gEvent.addEvent(this.host.docId, 'resetCascadeManagerBg', this.renderNewControlBackground);
    }

    private renderNewControlBackground = (names?: any, bVisible?: boolean): void => {
        if (bVisible === false && this.bVisible === true) {
            this.dom.innerHTML = '';
            this.bVisible = false;
            return;
        }

        if (this.bVisible === false && bVisible !== true) {
            return;
        }

        this.bVisible = true;
        const keys = Object.keys(names || {});
        if (keys.length === 0) {
            this.dom.innerHTML = '';
            return;
        }

        let rects = '';
        const newControls = this.newControls;
        keys.forEach((key) => {
            let color = '#B6F5B1';
            const bounds = newControls[key];
            if (!bounds) {
                return;
            }
            if (names[key] === 2) {
                color = '#FFE0A3';
            } else if (3 === names[key]) {
                color = '#C7D3FF';
            }
            bounds.forEach((bound) => {
                rects += `<rect fill="${color}" x="${bound.x}" y="${bound.y}" width="${bound.width}" height="${bound.height}"></rect>`;
            });
        });

        if (rects) {
            this.dom.innerHTML = rects;
        }
    }
}
