import * as React from 'react';
import Dialog from '../../ui/Dialog';
import '../../style/newcontrolCheckbox.less';
import { INewControlProperty, NewControlType, CustomPropertyElementType,
    isValidName } from '../../../../common/commonDefines';
import CustomProperty from './CustomProperty';
import Input from '../../ui/Input';
import Checkbox from '../../ui/CheckboxItem';
import Button from '../../ui/Button';
import {message} from '../../../../common/Message';
import { IFRAME_MANAGER } from '../../../../common/IframeManager';
import CascadeBtn from './NewCascade';
import { ExternalDataBind } from './ExternalDataBind';

interface IDialogProps {
    documentCore: any;
    visible: boolean;
    id: string;
    property?: INewControlProperty;
    close: (name: string | number, bRefresh?: boolean) => void;
}

interface IState {
    bRefresh: boolean;
}

export default class NewControlCheck extends React.Component<IDialogProps, IState> {
    private newControl: INewControlProperty;
    private visible: boolean;
    private bCustomProperty: boolean;
    private bCascade: boolean;
    private bDisableCascade: boolean;
    private resetSourceBind: boolean;
    private dataBind: any;
    constructor(props: any) {
        super(props);
        this.state = {
            bRefresh: false,
        };
        this.visible = this.props.visible;
        this.newControl = {newControlName: undefined};
        this.bDisableCascade = false;
    }

    public render(): any {
        return (
            <Dialog
                visible={this.visible}
                width={350}
                open={this.open}
                title='复选框'
                footer={this.renderFooter()}
                id={this.props.id}
            >
                <div className='newcontrol-checkbox'>
                    <div className='editor-line'>
                        <span className='title'>常规</span>
                    </div>
                    <div className='editor-line'>
                        <div className='w-70'>名称</div>
                        <div className='right-auto'>
                            <Input
                                name='newControlName'
                                value={this.newControl.newControlName}
                                onChange={this.onChange}
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <div className='w-70'>内部名称</div>
                        <div className='right-auto'>
                            <Input
                                name='newControlSerialNumber'
                                value={this.newControl.newControlSerialNumber}
                                onChange={this.onChange}
                                readonly={true}
                                placeholder={''}
                                disabled={true}
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <div className='w-70'>标识符</div>
                        <div className='right-auto'>
                            <Input
                                name='identifier'
                                value={this.newControl.identifier}
                                onChange={this.onChange}
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <div className='w-70'>提示符</div>
                        <div className='right-auto'>
                            <Input
                                name='newControlInfo'
                                value={this.newControl.newControlInfo}
                                onChange={this.onChange}
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <div className='w-050'>
                            <div className='w-70'>内容</div>
                            <div className='right-auto'>
                                <Input
                                    name='label'
                                    value={this.newControl.label}
                                    onChange={this.onChange}
                                />
                            </div>
                        </div>
                        <div className='w-050'>
                            <div className='w-50 code-text'>属性值</div>
                            <div className='right-auto right-auto-50'>
                                <Input
                                    name='labelCode'
                                    value={this.newControl.labelCode}
                                    onChange={this.onChange}
                                />
                            </div>
                        </div>
                    </div>
                    <div className='editor-line'>
                        <div className='w-70'>组</div>
                        <div className='right-auto'>
                            <Input
                                name='group'
                                value={this.newControl.group}
                                onChange={this.onChange}
                            />
                        </div>
                    </div>
                    <div className='editor-line editor-multi-line'>
                        <span className='title w-70'>属性</span>
                        <div className='right-auto newcontrol-prop'>
                            <div className='editor-line'>
                                <div className='w-050'>
                                    <Checkbox
                                        name='isNewControlHidden'
                                        value={this.newControl.isNewControlHidden}
                                        onChange={this.onChange}
                                    >
                                        隐藏
                                    </Checkbox>
                                </div>
                                <div className='w-050'>
                                    <Checkbox
                                        name='isNewControlCanntDelete'
                                        value={this.newControl.isNewControlCanntDelete}
                                        onChange={this.onChange}
                                    >
                                        禁止删除
                                    </Checkbox>
                                </div>
                            </div>

                            <div className='editor-line'>
                                <div className='w-050'>
                                    <Checkbox
                                        name='isNewControlShowBorder'
                                        value={this.newControl.isNewControlShowBorder}
                                        onChange={this.onChange}
                                    >
                                        显示边框
                                    </Checkbox>
                                </div>

                                <div className='w-050'>
                                    <Checkbox
                                        name='isNewControlHiddenBackground'
                                        value={this.newControl.isNewControlHiddenBackground}
                                        onChange={this.onChange}
                                    >
                                        隐藏背景色
                                    </Checkbox>
                                </div>
                            </div>
                            <div className='editor-line'>
                                <div className='w-050'>
                                    <Checkbox
                                        name='showRight'
                                        value={this.newControl.showRight}
                                        onChange={this.onChange}
                                    >
                                        勾选框居右
                                    </Checkbox>
                                </div>

                                <div className='w-050'>
                                    <Checkbox
                                        name='isNewControlReverseEdit'
                                        value={this.newControl.isNewControlReverseEdit}
                                        onChange={this.onChange}
                                    >
                                        反向编辑
                                    </Checkbox>
                                </div>
                            </div>

                            <div className='editor-line'>
                                <div className='w-050'>
                                    <Checkbox
                                        name='tabJump'
                                        value={this.newControl.tabJump}
                                        onChange={this.onChange}
                                    >
                                        键盘跳转
                                    </Checkbox>
                                </div>
                            </div>
                            <div className='editor-line'>
                                <CustomProperty
                                    name='customProperty'
                                    properties={this.newControl.customProperty}
                                    documentCore={this.props.documentCore}
                                    onChange={this.onChange}
                                    close={this.onClose}
                                    id='bCustomProperty'
                                    visible={this.bCustomProperty}
                                    type={CustomPropertyElementType.NewControl}
                                />
                            </div>
                        </div>
                    </div>

                    <div className='editor-line'>
                        <span className='title'>固有属性：</span>
                    </div>

                    <div>
                        <div className='editor-line'>
                            <div className='w-050'>
                                <Checkbox
                                    name='checked'
                                    value={this.newControl.checked}
                                    onChange={this.onChange}
                                >
                                    勾选
                                </Checkbox>
                            </div>

                            <div className='w-050'>
                                <Checkbox
                                    name='printSelected'
                                    value={this.newControl.printSelected}
                                    onChange={this.onChange}
                                >
                                    勾选才打印
                                </Checkbox>
                            </div>
                        </div>
                    </div>
                    <div className='editor-line'>
                        <div className='w-70'>
                            级联
                        </div>
                        <div className='right-auto'>
                            <CascadeBtn
                                visible={this.bCascade}
                                id='bCascade'
                                controlName={this.newControl.newControlName}
                                documentCore={this.props.documentCore}
                                properties={this.newControl.cascade}
                                name='cascade'
                                close={this.onClose}
                                onChange={this.onChange}
                                type={this.newControl.newControlType}
                                disable={this.bDisableCascade}
                            />
                        </div>
                    </div>
                    <ExternalDataBind
                            name={this.newControl.newControlName}
                            id='externalDataBind'
                            visible={this.visible}
                            documentCore={this.props.documentCore}
                            onChange={this.onChange}
                            close={this.onClose}
                            properties={this.dataBind}
                            resetId={'resetSourceBind'}
                    />
                </div>
            </Dialog>
        );
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        this.visible = nextProps.visible;
    }

    private renderFooter(): any {
        return (
            <span>
                <Button onClick={this.close}>取消</Button>
                <Button type='primary' onClick={this.confirm}>确认</Button>
            </span>
        );
    }

    private open = (): void => {
        const props = this.props.property;
        const newControl: INewControlProperty = this.newControl = {} as any;
        if (props === undefined) {
            this.init();
            newControl.newControlName = this.props.documentCore.makeUniqueNewControlName(NewControlType.CheckBox);
        } else {
            const keys = Object.keys(props);
            keys.push('cascade');
            keys.forEach((key) => {
                const val = props[key];
                newControl[key] = val;
            });
        }
        this.bDisableCascade = (newControl.group ? true : false);
        this.dataBind = newControl.externalDataBind;
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private onClose = (id: string, bRefresh: boolean): void => {
        this[id] = false;
        if ('externalDataBind' === id && bRefresh) {
            this.resetSourceBind = false;
        }
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private close = (bRefresh?: boolean): void => {
        this.visible = false;
        this.props.close(this.props.id, bRefresh);
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private confirm = (): void => {
        const documentCore = this.props.documentCore;
        const props = this.props.property;
        const newControl = this.newControl;
        IFRAME_MANAGER.setDocId(documentCore.getCurrentId());
        if (!isValidName(newControl.newControlName)) {
            message.error('名称不符合规范，请重新输入');
            return;
        }
        if (newControl.identifier && !isValidName(newControl.identifier)) {
            message.error('标识符不符合规范，请重新输入');
            return;
        }
        if ((!props || props.newControlName !== newControl.newControlName)
            && !documentCore.checkNewControlName(newControl.newControlName)) {
            message.error('该名称不符合，请重新填写');
            return;
        }
        if (this.newControl.group) {
            this.newControl.cascade = [];
        }

        if (this.resetSourceBind) {
            this.newControl.externalDataBind = {
                sourceObj: '',
                sourceKey: '',
                bReadOnly: false,
                commandUpdate: 1,
            }

            this.resetSourceBind = false;
        } else if (this.dataBind) {
            this.newControl.externalDataBind = this.dataBind;
            this.dataBind = undefined;
        }

        if (props === undefined) {
            documentCore.addNewControl(this.newControl);
        } else {
            documentCore.setNewControlProperty(this.newControl, props.newControlName);
        }

        this.close(true);
    }

    private init(): void {
        const newControl = this.newControl;
        newControl.newControlName = undefined;
        newControl.newControlSerialNumber = undefined;
        newControl.newControlInfo = undefined;
        newControl.newControlType = NewControlType.CheckBox;
        newControl.newControlPlaceHolder = '';
        newControl.isNewControlHidden = false;
        newControl.isNewControlCanntDelete = false;
        newControl.isNewControlShowBorder = false;
        newControl.isNewControlHiddenBackground = true;
        newControl.customProperty = undefined;
        newControl.label = '';
        newControl.labelCode = '';
        newControl.checked = false;
        newControl.printSelected = false;
        newControl.showRight = false;
        newControl.tabJump = true;
        newControl.cascade = undefined;
        newControl.group = undefined;
        newControl.identifier = undefined;
        newControl.isNewControlReverseEdit = false;
        newControl.externalDataBind = undefined;
        this.resetSourceBind = false;
    }

    private onChange = (value: any, name: string): void => {
        if ('resetSourceBind' !== name) {
            if ('externalDataBind' !== name) {
                this.newControl[name] = value;
            } else {
                this.dataBind = value;
            }
        } else {
            this.resetSourceBind = true;
        }

        if ( 'group' === name ) {
            const bDisplay = (value ? true : false); // value ? 'none' : '';
            if ( bDisplay !== this.bDisableCascade ) {
                this.bDisableCascade = (value ? true : false);
                this.setState({bRefresh: !this.state.bRefresh});
            }
        }
    }
}
