import * as ReactDOM from 'react-dom';
import * as React from 'react';
import EditorInterface, {IExternalEvent} from './common/external/IExternalInterface';
import { EmrEditor } from './components/editor/Main';

export default class Editor {
  private _editor: any;
  private _options: any;
  constructor(options: any) {
    this._options = options || {};
    // options.bShowMenu = true;
    // options.bShowToolbar = true;
    const dom = options.dom;
    if (dom) {
      if (options.height === undefined) {
        // tslint:disable-next-line: no-console
        console.error('请传入容器高度');
      }

      const id = options.id || 'text-id-1';
      const editor: EmrEditor = ReactDOM.render(
        // tslint:disable-next-line: jsx-wrap-multiline
        <EmrEditor
            id={id}
            height={options.height}
            isTest={options.isTest}
            bShowMenu={options.bShowMenu}
            bShowToolbar={options.bShowToolbar}
            bNeedCustomFont={options.bNeedCustomFont}
        />,
        dom,
      ) as any;

      this._editor = {
        getEditor: () => {
          return editor.getEditor();
        },
        setEvent: (events: IExternalEvent): void => {
          editor.setEditEvent(events);
        },
        removeEvent(sNames?: string[]): boolean {
          return editor.removeEvent(sNames);
        },
      };
    }
  }

  public getEditor(): EditorInterface {
    const result = this._editor.getEditor();
    return result;
  }

  public setEvent(events: IExternalEvent): any  {
    this._editor.setEvent(events);
    return this;
  }

  public removeEvent(sNames?: string[]): boolean  {
    return this._editor.removeEvent(sNames);
  }
}

// import * as ReactDOM from 'react-dom';
// import * as React from 'react';
// import EditorInterface, { IExternalEvent } from './common/external/IExternalInterface';
// import { EmrEditor } from './components/editor/Main';
// import { WasmInstance } from './common/WasmInstance';

// export default class Editor {
//   private _editor: any;
//   private _options: any;
//   private _div: any;
//   private _iframe: any;
//   private _visible: boolean;

//   constructor() {
//     // options.bShowMenu = true;
//     // options.bShowToolbar = true;
//   }

//   public getEditor(): EditorInterface {
//     return this._editor.getEditor();
//   }

//   public setEvent(events: IExternalEvent): any {
//     this._editor.setEvent(events);
//     return this;
//   }

//   public removeEvent(sNames?: string[]): boolean {
//     return this._editor.removeEvent(sNames);
//   }

//   public hideEditor(): void {
//     if (this._options.dom) {
//       this._editor.setVisible(false);
//       this._options.dom.innerHTML = '';
//     }
//   }

//   public showEditor(): void {
//     this._options.dom.appendChild(this._iframe);
//     const div = this.addContainer();
//     div.parentNode.replaceChild(this._div, div);
//     this._editor.setVisible(true);
//   }

//   public init(options: any): Promise<any> {
//     return new Promise((resolve, reject) => {
//       options = this._options = options || {};
//       const dom = options.dom;
//       if (dom) {
//         const iframe = this._iframe = document.createElement('iframe');
//         iframe.width = '100%';
//         iframe.height = '100%';
//         iframe.frameBorder = '0';
//         iframe.src = 'about:blank';
//         iframe.scrolling = 'no';
//         dom.appendChild(iframe);
//         const iframeDocument = iframe.contentDocument;
//         const div = this._div = iframeDocument.createElement('div');
//         div.id = 'editor-container-id';
//         iframeDocument.body.appendChild(div);
//         if (Editor['emrEditorCss']) {
//           const style = iframeDocument.createElement('style');
//           style.innerHTML = Editor['emrEditorCss'];
//           style.type = 'text/css';
//           iframeDocument.head.appendChild(style);
//         }

//         // if (options.height === undefined) {
//         //   // tslint:disable-next-line: no-console
//         //   console.error('请传入容器高度');
//         // }

//         if (WasmInstance.instance) {
//           this.initEditor(options, iframe, div);
//           resolve(this);
//           return;
//         }

//         // Should we allow external user to set the remote url here?
//         if (options.remoteUrl) {
//           WasmInstance.setRemoteUrl(options.remoteUrl);
//         }

//         WasmInstance.initializeAsync()
//           .then((instance: IWasmInstance) => {
//             this.initEditor(options, iframe, div);
//             resolve(this);
//           });
//       }
//     });
//   }

//   private initEditor(options: any, iframe: any, div: HTMLElement): void {
//     const id = options.id || 'text-id-1';
//     const editor: EmrEditor = ReactDOM.render(
//       // tslint:disable-next-line: jsx-wrap-multiline
//       <EmrEditor
//         win={iframe.contentWindow}
//         id={id}
//         height={options.height}
//         isTest={options.isTest}
//         bShowMenu={options.bShowMenu}
//         bShowToolbar={options.bShowToolbar}
//         iframe={iframe}
//       />,
//       div,
//     ) as any;

//     this._editor = {
//       getEditor: () => {
//         return editor.getEditor();
//       },
//       setEvent: (events: IExternalEvent): void => {
//         editor.setEditEvent(events);
//       },
//       removeEvent(sNames?: string[]): boolean {
//         return editor.removeEvent(sNames);
//       },
//       setVisible(flag: boolean): void {
//         this._visible = flag;
//         editor.setVisible(flag, this._iframe);
//       },
//     };
//   }

//   private addContainer(): HTMLDivElement {
//     const iframeDocument = this._iframe.contentDocument;
//     const div = iframeDocument.createElement('div');
//     div.id = 'editor-container-id';
//     iframeDocument.body.appendChild(div);
//     if (Editor['emrEditorCss']) {
//       const style = iframeDocument.createElement('style');
//       style.innerHTML = Editor['emrEditorCss'];
//       style.type = 'text/css';
//       iframeDocument.head.appendChild(style);
//     }

//     return div;
//   }
// }
