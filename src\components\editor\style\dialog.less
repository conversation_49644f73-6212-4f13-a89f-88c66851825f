@import './global.less';
@paddingWidth: 16px;
@paddingHeight: 16px;
.hz-editor-container .editor-dialog {
    display: none;
    position: fixed;
    z-index: 800;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    font-family: @fontFamily;
    font-size: @fontSize;

    // & * {
    //     box-sizing: border-box;
    // }
    &.no-modal {
        z-index: 103;
        width: auto;
        height: auto;
    }

    &.visible {
        display: block;
    }
    &.opacity0 {
        display: block;
        left: -300% !important;
        opacity: 0;
    }
    .dialog-box {
        position: absolute;
        z-index: 1;
        background: #fff;
        box-shadow:0px 2px 20px 0px rgba(39, 53, 70, 0.2);
        border-radius:4px;
        border:1px solid #dddde3;
    }
    &.in-middle > .dialog-box {
        top: 50%;
        transform: translateY(-50%);
    }
    .dialog-box-header {
        height: 40px;
        padding: 0 @paddingWidth;
        line-height: 40px;
        text-align: left;
        cursor: move;
        #less.title();
        border-bottom: 1px solid @borderColor;
    }

    .dialog-box-close {
        position: absolute;
        right: 8px;
        top: 6px;
        font-size: 20px;
        cursor: pointer;
    }

    .dialog-box-footer {
        position: absolute;
        left: 0;
        bottom: 0;
        width: 100%;
        height: 68px;
        padding: 0 @paddingWidth;
        text-align: center;
        box-sizing: border-box;
    }

    .dialog-box-footer-btns {
        padding-top: 20px;
    }

    .dialog-box-footer-btns .editor-button {
        margin-right: 10px;
        &:last-child {
            margin-right: 0;
        }
    }

    .dialog-box-body {
        height: calc(100% - 109px);
        padding: 10px @paddingWidth 5px @paddingWidth;
        overflow: auto;
        text-align: left;
        box-sizing: border-box;

        &::-webkit-scrollbar {
            width: 7px;
            height: 8px;
        }

        &::-webkit-scrollbar-thumb {
            /*滚动条里面小方块*/
            border-radius: 1px;
            background-color: #ddd;
        }
        &::-webkit-scrollbar-track {
            /*滚动条里面轨道*/
            box-shadow   : inset 0 0 2px rgba(0, 0, 0, 0.2);
            background   : #f2f2f2;
            border-radius: 10px;
        }

        /** 内部加载动画样式 */
        .loading span{
            display: inline-block;
            width: 15px;
            height: 100%;
            margin-right: 5px;
            border-radius: 50%;
            background: lightgreen;
            animation-delay: 0.3s;
            -webkit-animation: load 1.04s ease infinite;
        }
        .loading span:last-child{
            margin-right: 0px; 
        }
        @-webkit-keyframes load{
            0%{
                opacity: 1;
            }
            100%{
                opacity: 0;
            }
        }
        .loading span:nth-child(1){
            -webkit-animation-delay:0.13s;
        }
        .loading span:nth-child(2){
            -webkit-animation-delay:0.26s;
        }
        .loading span:nth-child(3){
            -webkit-animation-delay:0.39s;
        }
        .loading span:nth-child(4){
            -webkit-animation-delay:0.52s;
        }
        .loading span:nth-child(5){
            -webkit-animation-delay:0.65s;
        }
    }

    .dialog-box-place {
        display: block;
        width: 100%;
        height: 68px;
    }

    &.no-footer {
        .dialog-box-body {
            height: calc(100% - 45px);
        }

        .dialog-box-place {
            display: none;
        }
    }

    .dialog-box-scale {
        position: absolute;
        right: 0;
        bottom: 0;
        z-index: 999;
        width: 0;
        height: 0;
        cursor: nwse-resize;
        border: 6px solid #aaa;
        border-left-color: transparent;
        border-top-color: transparent;
    }

    &div.full-scale-box {
        width: 100%;
        height: 100%;
    }
}

.about-dialog {
    padding: 24px;
    text-align: center;

    .about-header {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 24px;

        .logo-icon {
            width: 32px;
            height: 32px;
            margin-right: 10px;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%233B82F6'%3E%3Cpath d='M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5'/%3E%3C/svg%3E");
            background-size: contain;
            background-repeat: no-repeat;
        }

        .product-name {
            font-size: 20px;
            font-weight: 600;
            color: #1f2937;
            margin: 0;
        }
    }

    .about-content {
        text-align: left;

        .version-info {
            margin-bottom: 20px;
            p {
                margin: 8px 0;
                color: #374151;
            }
        }

        .company-info {
            margin-bottom: 20px;
            p {
                margin: 8px 0;
                color: #374151;
            }
            a {
                color: #3B82F6;
                text-decoration: none;
                &:hover {
                    text-decoration: underline;
                }
            }
        }

        .copyright {
            margin-top: 24px;
            color: #6B7280;
            font-size: 13px;
            p {
                margin: 4px 0;
            }
        }
    }
}
