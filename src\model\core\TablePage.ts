import DocumentFrameBounds from './FrameBounds';
import { TableRow } from './Table/TableRow';
import { TableRowInfo } from './Table/TableRowProperty';
import { DocumentBorder } from './Style';

export class TablePage {
    public xOrigin: number;
    public x: number;
    public y: number;
    public xLimit: number;
    public yLimit: number;

    public firstRow: number; // 第一行
    public lastRow: number; // 末行
    public bLastRowSplit: boolean;
    public height: number;

    public bounds: DocumentFrameBounds;
    public maxTopBorder: number;
    public maxBottomBorder: number;
    public bottomBorders: DocumentBorder[];

    constructor(x: number, y: number, xLimit: number, yLimit: number, firstRow: number, maxTopBorder: number) {
        this.xOrigin = x;
        this.x = x;
        this.y = y;
        this.xLimit = xLimit;
        this.yLimit = yLimit;
        this.firstRow = firstRow;
        this.lastRow = firstRow;
        this.bLastRowSplit = false;
        this.height = 0;
        this.bounds = new DocumentFrameBounds(x, y, xLimit, y);
        this.maxTopBorder = maxTopBorder;
        this.maxBottomBorder = 0;
        this.bottomBorders = [];
    }

    public shift( dx: number, dy: number ): void {
        // todo
    }
}

export class TableHeaderInfo {
    public count: number;
    public pageIndex: number;
    public bHeaderRecalculate: boolean;
    public pages: TableHeaderPage[];

    constructor() {
        this.count = 0;
        this.pageIndex = 0;
        this.bHeaderRecalculate = false;
        this.pages = [];
    }
}

/**
 * 表格标题行page
 */
// tslint:disable-next-line: max-classes-per-file
export class TableHeaderPage {
    public rows: TableRow[];
    public rowsInfo: TableRowInfo[];
    public bDraw: boolean;

    constructor() {
        this.rows = [];
        this.rowsInfo = [];
        this.bDraw = false;
    }
}
