import { DocumentCore } from '../../../../model/DocumentCore';
import { ICursorProperty } from '../../../../model/CursorProperty';
import {GlobalEvent as gEvent, GlobalEventName as gEventName} from '../../../../common/GlobalEvent';

export default class Cursor {
    private _oldPosition: any;
    private cursor: any;
    private timer: any;
    private pageIndex: number;
    private host: any;
    private visible: boolean;
    private bRefresh: boolean;
    private documentCore: DocumentCore;
    private time3: any;
    constructor(host: any) {
        this.visible = true;
        this.host = host;
        this.documentCore = host.documentCore;
        this.addEvent();
        // this.createCursor();
    }

    public setCursorPosition(position: ICursorProperty): boolean {
        this.scaleValue(position);
        // const { y1, y2, pageNum } = position;
        let x = position.x;
        let y1 = position.y1;
        let y2 = position.y2;
        if (isNaN(y1)) {
            y1 = 0;
        }
        if (isNaN(y2)) {
            y2 = y1 + 20;
        }
        const pageNum = position.pageNum;
        const oldPosition: any = this._oldPosition || {};
        // 阻止多次点同一个点，光标还在跳动的情况
        if (
            x === oldPosition.x &&
            y1 === oldPosition.y1 &&
            pageNum === oldPosition.pageNum
        ) {
            return false;
        }
        this._oldPosition = { x, y1, y2, pageNum };

        //  console.log(position.x);
        const cursor = this.cursor;

        // cursor stroke width is cut half at 0
        if (x === 0) {
            x = 1;
        }

        cursor.setAttribute('x1', `${x}`);
        cursor.setAttribute('x2', `${x}`);
        cursor.setAttribute('y1', `${y1}`);
        cursor.setAttribute('y2', `${y2}`);
        this.pageIndex = pageNum;
        gEvent.setEvent(this.host.docId, gEventName.MoveCursor, position);
        return true;
    }

    public insertCursor = (pageNode: SVGElement, pageId?: number): boolean => {
        if (!this.cursor) {
            this.createCursor(pageNode);
        }
        let newPageId: number;
        const oldPageId = this.pageIndex;
        if (pageId !== undefined) {
            newPageId = pageId;
        } else if (pageNode) {
            newPageId = +pageNode.getAttribute('page-id');
        }

        if (pageNode && (newPageId !== oldPageId || !pageNode.parentNode.lastChild['id'])) {
            // move the same cursor to new page
            pageNode.appendChild(this.cursor);
            return true;
        }

        return false;
    }

    public cursorFocused(): void {
        clearInterval(this.timer);
        this.timer = setInterval(() => {
            const isHidden = this.cursor.style.visibility === 'hidden';
            this.cursor.style.visibility = isHidden ? 'initial' : 'hidden';
        }, 550);
    }

    public isVisible(): boolean {
        return this.visible;
    }

    public setCursorVisible = (bVisible: boolean): void => {
        clearTimeout(this.time3);
        this.time3 = setTimeout(() => {
            this.setCursorVisible3(bVisible);
        }, 1);
    }

    public setCursorVisible3 = (bVisible: boolean): void => {
        if (!this.cursor) {
            return;
        }
        const style = this.cursor.style;
        if (true === bVisible) {
            style.visibility = 'initial';
        } else {
            style.visibility = 'hidden';
            this.cursorBlur();
        }

        if (bVisible === this.isVisible()) {
            if (this.bRefresh === true) {
                this.cursorFocused();
                this.bRefresh = false;
            }
            return;
        }
        this.visible = bVisible;
        gEvent.setEvent(this.host.docId, gEventName.CursorVisible, bVisible);

        if (bVisible === true) {
            this.cursorFocused();
        } else {
            this.cursorBlur();
        }
    }

    public getCursorPosition(): ICursorProperty {
        return this._oldPosition;
    }

    public setCursorVisible2(bVisible: boolean): void {
        const style = this.cursor.style;
        if (true === bVisible) {
            style.visibility = 'initial';
            this.cursorFocused();
        } else {
            style.visibility = 'hidden';
            this.cursorBlur();
            this.bRefresh = true;
        }
    }

    public cursorBlur(): void {
        clearInterval(this.timer);
    }

    public getCursor(): any {
        return this.cursor;
    }

    public setNodeVisible = (bReadOnly: boolean): void => {
        if (!this.cursor) {
            return;
        }

        if ( 'none' !== this.cursor.style.display ) {
            if (true === bReadOnly) {
                this.cursor.style.display = 'none';
            }
        } else {
            if (true !== bReadOnly) {
                this.cursor.style.display = '';
            }
        }
    }

    private createCursor(pageNode: SVGElement): void {
        const cursor = pageNode.ownerDocument.createElementNS(
            'http://www.w3.org/2000/svg',
            'line',
        );
        cursor.setAttribute('id', 'cursor');
        cursor.setAttribute('stroke', 'black');
        cursor.setAttribute('stroke-width', '2');
        cursor.setAttribute('style', 'visibility:hidden');
        cursor.setAttribute('style', 'pointer-events:none');
        pageNode.appendChild(cursor);
        this.cursor = cursor;
    }

    private scaleValue(position: any): void {
        const scale = 1; // this.host.getScale();
        position.x *= scale;
        position.y1 *= scale;
        position.y2 *= scale;
    }

    private addEvent(): void {
        gEvent.addEvent(this.host.docId, gEventName.Readonly, this.setNodeVisible);
        gEvent.addEvent(this.host.docId, gEventName.UnMounted, this.removeEvent);
    }

    private removeEvent = (): void => {
        clearInterval(this.timer);
        gEvent.deleteEvent(this.host.docId, gEventName.Readonly, this.setNodeVisible);
        this.cursor = null;
        this.host = null;
        this.documentCore = null;
    }
}
