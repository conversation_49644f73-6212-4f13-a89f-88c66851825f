// import { IRect } from './util';
// import { isMacOs } from '../../common/commonMethods';

// // 字符测量缓存
// export const CACHES = new Map<string/*字体及字号*/, Map<string/*字符*/, IRect>>();

// export function initMeasureCaches(): void {
//     // let p1 = performance.now();
//     if ( 35 <= CACHES.size ) {
//         return ;
//     }

//     // 初号58.7 小初48 一号34.7 小一32 二号29.3 小二24 三号21.3 小三20 四号18.7 小四16 五号14 10号13.3 小五12(9号: 12) 8号10.7'☑', '☐' ⊙ ⊙ ⭘ ◯
//     const paraEnd = String.fromCharCode(0x21B5);
//     CACHES.set(
//         '58.7', new Map([
//             ['我', {width: 58.7, height: 58, type: 1}],
//             [' ', {width: 29.35, height: 58, type: 1}],
//             ['0', {width: 29.35, height: 58, type: 1}],
//             ['1', {width: 29.35, height: 58, type: 1}],
//             ['2', {width: 29.35, height: 58, type: 1}],
//             ['3', {width: 29.35, height: 58, type: 1}],
//             ['4', {width: 29.35, height: 58, type: 1}],
//             ['5', {width: 29.35, height: 58, type: 1}],
//             ['6', {width: 29.35, height: 58, type: 1}],
//             ['7', {width: 29.35, height: 58, type: 1}],
//             ['8', {width: 29.35, height: 58, type: 1}],
//             ['9', {width: 29.35, height: 58, type: 1}],
//             ['a', {width: 29.35, height: 58, type: 1}],
//             ['b', {width: 29.35, height: 58, type: 1}],
//             ['c', {width: 29.35, height: 58, type: 1}],
//             ['d', {width: 29.35, height: 58, type: 1}],
//             ['e', {width: 29.35, height: 58, type: 1}],
//             ['f', {width: 29.35, height: 58, type: 1}],
//             ['g', {width: 29.35, height: 58, type: 1}],
//             ['h', {width: 29.35, height: 58, type: 1}],
//             ['i', {width: 29.35, height: 58, type: 1}],
//             ['j', {width: 29.35, height: 58, type: 1}],
//             ['k', {width: 29.35, height: 58, type: 1}],
//             ['l', {width: 29.35, height: 58, type: 1}],
//             ['m', {width: 29.35, height: 58, type: 1}],
//             ['n', {width: 29.35, height: 58, type: 1}],
//             ['o', {width: 29.35, height: 58, type: 1}],
//             ['p', {width: 29.35, height: 58, type: 1}],
//             ['q', {width: 29.35, height: 58, type: 1}],
//             ['r', {width: 29.35, height: 58, type: 1}],
//             ['s', {width: 29.35, height: 58, type: 1}],
//             ['t', {width: 29.35, height: 58, type: 1}],
//             ['u', {width: 29.35, height: 58, type: 1}],
//             ['v', {width: 29.35, height: 58, type: 1}],
//             ['w', {width: 29.35, height: 58, type: 1}],
//             ['x', {width: 29.35, height: 58, type: 1}],
//             ['y', {width: 29.35, height: 58, type: 1}],
//             ['z', {width: 29.35, height: 58, type: 1}],
//             ['A', {width: 29.35, height: 58, type: 1}],
//             ['B', {width: 29.35, height: 58, type: 1}],
//             ['C', {width: 29.35, height: 58, type: 1}],
//             ['D', {width: 29.35, height: 58, type: 1}],
//             ['E', {width: 29.35, height: 58, type: 1}],
//             ['F', {width: 29.35, height: 58, type: 1}],
//             ['G', {width: 29.35, height: 58, type: 1}],
//             ['H', {width: 29.35, height: 58, type: 1}],
//             ['I', {width: 29.35, height: 58, type: 1}],
//             ['J', {width: 29.35, height: 58, type: 1}],
//             ['K', {width: 29.35, height: 58, type: 1}],
//             ['L', {width: 29.35, height: 58, type: 1}],
//             ['M', {width: 29.35, height: 58, type: 1}],
//             ['N', {width: 29.35, height: 58, type: 1}],
//             ['O', {width: 29.35, height: 58, type: 1}],
//             ['P', {width: 29.35, height: 58, type: 1}],
//             ['Q', {width: 29.35, height: 58, type: 1}],
//             ['R', {width: 29.35, height: 58, type: 1}],
//             ['S', {width: 29.35, height: 58, type: 1}],
//             ['T', {width: 29.35, height: 58, type: 1}],
//             ['U', {width: 29.35, height: 58, type: 1}],
//             ['V', {width: 29.35, height: 58, type: 1}],
//             ['W', {width: 29.35, height: 58, type: 1}],
//             ['X', {width: 29.35, height: 58, type: 1}],
//             ['Y', {width: 29.35, height: 58, type: 1}],
//             ['Z', {width: 29.35, height: 58, type: 1}],
//             [',', {width: 29.35, height: 58, type: 1}],
//             ['.', {width: 29.35, height: 58, type: 1}],
//             [';', {width: 29.35, height: 58, type: 1}],
//             [':', {width: 29.35, height: 58, type: 1}], ['：', {width: 58.7, height: 58, type: 1}],
//             ['/', {width: 29.35, height: 58, type: 1}],
//             ['?', {width: 29.35, height: 58, type: 1}],
//             ['(', {width: 29.35, height: 58, type: 1}],
//             [')', {width: 29.35, height: 58, type: 1}],
//             ['[', {width: 29.35, height: 58, type: 1}],
//             [']', {width: 29.35, height: 58, type: 1}],
//             ['=', {width: 29.35, height: 58, type: 1}],
//             ['-', {width: 29.35, height: 58, type: 1}],
//             ['+', {width: 29.35, height: 58, type: 1}],
//             ['*', {width: 29.35, height: 58, type: 1}],
//             ['<', {width: 29.35, height: 58, type: 1}],
//             ['>', {width: 29.35, height: 58, type: 1}],
//             ['，', {width: 58.7, height: 58, type: 1}],
//             ['。', {width: 58.7, height: 58, type: 1}],
//             ['；', {width: 58.7, height: 58, type: 1}],
//             ['’', {width: 58.7, height: 58, type: 1}],
//             ['“', {width: 58.7, height: 58, type: 1}],
//             ['？', {width: 58.7, height: 58, type: 1}],
//             ['、', {width: 58.7, height: 58, type: 1}],
//             ['（', {width: 58.7, height: 58, type: 1}],
//             ['）', {width: 58.7, height: 58, type: 1}],
//             ['【', {width: 58.7, height: 58, type: 1}],
//             ['】', {width: 58.7, height: 58, type: 1}],
//             ['】', {width: 58.7, height: 58, type: 1}],
//             [paraEnd, {width: 0, height: 58, type: 1}], ['{', {width: 29.35, height: 58, type: 1}],
//             ['}', {width: 29.35, height: 58, type: 1}],
//             ['☑', {width: 50.56, height: 58, type: 1}],
//             ['☐', {width: 50.56, height: 58, type: 1}],
//             ['⊙', {width: 58.7, height: 58, type: 1}],
//             ['⭘', {width: 58.7, height: 58, type: 1}],
//             ['◯', {width: 63.89, height: 58, type: 1}],
//             ['℃', {width: 58.7, height: 58, type: 1}], ['Ⅰ', {width: 58.7, height: 58, type: 1}],
//             ['Ⅱ', {width: 58.7, height: 58, type: 1}],
//         ])
//     );
//     CACHES.set(
//         '48', new Map([
//             ['我', {width: 48, height: 48, type: 1}],
//             [' ', {width: 24, height: 48, type: 1}],
//             ['0', {width: 24, height: 48, type: 1}],
//             ['1', {width: 24, height: 48, type: 1}],
//             ['2', {width: 24, height: 48, type: 1}],
//             ['3', {width: 24, height: 48, type: 1}],
//             ['4', {width: 24, height: 48, type: 1}],
//             ['5', {width: 24, height: 48, type: 1}],
//             ['6', {width: 24, height: 48, type: 1}],
//             ['7', {width: 24, height: 48, type: 1}],
//             ['8', {width: 24, height: 48, type: 1}],
//             ['9', {width: 24, height: 48, type: 1}],
//             ['a', {width: 24, height: 48, type: 1}],
//             ['b', {width: 24, height: 48, type: 1}],
//             ['c', {width: 24, height: 48, type: 1}],
//             ['d', {width: 24, height: 48, type: 1}],
//             ['e', {width: 24, height: 48, type: 1}],
//             ['f', {width: 24, height: 48, type: 1}],
//             ['g', {width: 24, height: 48, type: 1}],
//             ['h', {width: 24, height: 48, type: 1}],
//             ['i', {width: 24, height: 48, type: 1}],
//             ['j', {width: 24, height: 48, type: 1}],
//             ['k', {width: 24, height: 48, type: 1}],
//             ['l', {width: 24, height: 48, type: 1}],
//             ['m', {width: 24, height: 48, type: 1}],
//             ['n', {width: 24, height: 48, type: 1}],
//             ['o', {width: 24, height: 48, type: 1}],
//             ['p', {width: 24, height: 48, type: 1}],
//             ['q', {width: 24, height: 48, type: 1}],
//             ['r', {width: 24, height: 48, type: 1}],
//             ['s', {width: 24, height: 48, type: 1}],
//             ['t', {width: 24, height: 48, type: 1}],
//             ['u', {width: 24, height: 48, type: 1}],
//             ['v', {width: 24, height: 48, type: 1}],
//             ['w', {width: 24, height: 48, type: 1}],
//             ['x', {width: 24, height: 48, type: 1}],
//             ['y', {width: 24, height: 48, type: 1}],
//             ['z', {width: 24, height: 48, type: 1}],
//             ['A', {width: 24, height: 48, type: 1}],
//             ['B', {width: 24, height: 48, type: 1}],
//             ['C', {width: 24, height: 48, type: 1}],
//             ['D', {width: 24, height: 48, type: 1}],
//             ['E', {width: 24, height: 48, type: 1}],
//             ['F', {width: 24, height: 48, type: 1}],
//             ['G', {width: 24, height: 48, type: 1}],
//             ['H', {width: 24, height: 48, type: 1}],
//             ['I', {width: 24, height: 48, type: 1}],
//             ['J', {width: 24, height: 48, type: 1}],
//             ['K', {width: 24, height: 48, type: 1}],
//             ['L', {width: 24, height: 48, type: 1}],
//             ['M', {width: 24, height: 48, type: 1}],
//             ['N', {width: 24, height: 48, type: 1}],
//             ['O', {width: 24, height: 48, type: 1}],
//             ['P', {width: 24, height: 48, type: 1}],
//             ['Q', {width: 24, height: 48, type: 1}],
//             ['R', {width: 24, height: 48, type: 1}],
//             ['S', {width: 24, height: 48, type: 1}],
//             ['T', {width: 24, height: 48, type: 1}],
//             ['U', {width: 24, height: 48, type: 1}],
//             ['V', {width: 24, height: 48, type: 1}],
//             ['W', {width: 24, height: 48, type: 1}],
//             ['X', {width: 24, height: 48, type: 1}],
//             ['Y', {width: 24, height: 48, type: 1}],
//             ['Z', {width: 24, height: 48, type: 1}],
//             [',', {width: 24, height: 48, type: 1}],
//             ['.', {width: 24, height: 48, type: 1}],
//             [';', {width: 24, height: 48, type: 1}],
//             ['/', {width: 24, height: 48, type: 1}],
//             ['?', {width: 24, height: 48, type: 1}],
//             ['(', {width: 24, height: 48, type: 1}],
//             [')', {width: 24, height: 48, type: 1}],
//             ['[', {width: 24, height: 48, type: 1}],
//             [']', {width: 24, height: 48, type: 1}],
//             ['=', {width: 24, height: 48, type: 1}],
//             ['-', {width: 24, height: 48, type: 1}],
//             ['+', {width: 24, height: 48, type: 1}],
//             ['*', {width: 24, height: 48, type: 1}],
//             ['<', {width: 24, height: 48, type: 1}],
//             ['>', {width: 24, height: 48, type: 1}],
//             ['，', {width: 48, height: 48, type: 1}],
//             ['。', {width: 48, height: 48, type: 1}],
//             ['；', {width: 48, height: 48, type: 1}],
//             ['：', {width: 48, height: 48, type: 1}], [':', {width: 24, height: 48, type: 1}],
//             ['’', {width: 48, height: 48, type: 1}],
//             ['“', {width: 48, height: 48, type: 1}],
//             ['？', {width: 48, height: 48, type: 1}],
//             ['、', {width: 48, height: 48, type: 1}],
//             ['（', {width: 48, height: 48, type: 1}],
//             ['）', {width: 48, height: 48, type: 1}],
//             ['【', {width: 48, height: 48, type: 1}],
//             ['】', {width: 48, height: 48, type: 1}],
//             [paraEnd, {width: 0, height: 48, type: 1}],
//             ['{', {width: 24, height: 48, type: 1}],
//             ['}', {width: 24, height: 48, type: 1}],
//             ['☑', {width: 41.34, height: 48, type: 1}],
//             ['☐', {width: 41.34, height: 48, type: 1}],
//             ['⊙', {width: 48, height: 48, type: 1}],
//             ['⭘', {width: 48, height: 48, type: 1}],
//             ['◯', {width: 52.24, height: 48, type: 1}],
//             ['℃', {width: 48, height: 48, type: 1}], ['Ⅰ', {width: 48, height: 48, type: 1}],
//             ['Ⅱ', {width: 48, height: 48, type: 1}],
//         ])
//     );
//     CACHES.set(
//         '37.3', new Map([
//             ['我', {width: 37.3, height: 37, type: 1}],
//             [' ', {width: 18.65, height: 37, type: 1}],
//             ['0', {width: 18.65, height: 37, type: 1}],
//             ['1', {width: 18.65, height: 37, type: 1}],
//             ['2', {width: 18.65, height: 37, type: 1}],
//             ['3', {width: 18.65, height: 37, type: 1}],
//             ['4', {width: 18.65, height: 37, type: 1}],
//             ['5', {width: 18.65, height: 37, type: 1}],
//             ['6', {width: 18.65, height: 37, type: 1}],
//             ['7', {width: 18.65, height: 37, type: 1}],
//             ['8', {width: 18.65, height: 37, type: 1}],
//             ['9', {width: 18.65, height: 37, type: 1}],
//             ['a', {width: 18.65, height: 37, type: 1}],
//             ['b', {width: 18.65, height: 37, type: 1}],
//             ['c', {width: 18.65, height: 37, type: 1}],
//             ['d', {width: 18.65, height: 37, type: 1}],
//             ['e', {width: 18.65, height: 37, type: 1}],
//             ['f', {width: 18.65, height: 37, type: 1}],
//             ['g', {width: 18.65, height: 37, type: 1}],
//             ['h', {width: 18.65, height: 37, type: 1}],
//             ['i', {width: 18.65, height: 37, type: 1}],
//             ['j', {width: 18.65, height: 37, type: 1}],
//             ['k', {width: 18.65, height: 37, type: 1}],
//             ['l', {width: 18.65, height: 37, type: 1}],
//             ['m', {width: 18.65, height: 37, type: 1}],
//             ['n', {width: 18.65, height: 37, type: 1}],
//             ['o', {width: 18.65, height: 37, type: 1}],
//             ['p', {width: 18.65, height: 37, type: 1}],
//             ['q', {width: 18.65, height: 37, type: 1}],
//             ['r', {width: 18.65, height: 37, type: 1}],
//             ['s', {width: 18.65, height: 37, type: 1}],
//             ['t', {width: 18.65, height: 37, type: 1}],
//             ['u', {width: 18.65, height: 37, type: 1}],
//             ['v', {width: 18.65, height: 37, type: 1}],
//             ['w', {width: 18.65, height: 37, type: 1}],
//             ['x', {width: 18.65, height: 37, type: 1}],
//             ['y', {width: 18.65, height: 37, type: 1}],
//             ['z', {width: 18.65, height: 37, type: 1}],
//             ['A', {width: 18.65, height: 37, type: 1}],
//             ['B', {width: 18.65, height: 37, type: 1}],
//             ['C', {width: 18.65, height: 37, type: 1}],
//             ['D', {width: 18.65, height: 37, type: 1}],
//             ['E', {width: 18.65, height: 37, type: 1}],
//             ['F', {width: 18.65, height: 37, type: 1}],
//             ['G', {width: 18.65, height: 37, type: 1}],
//             ['H', {width: 18.65, height: 37, type: 1}],
//             ['I', {width: 18.65, height: 37, type: 1}],
//             ['J', {width: 18.65, height: 37, type: 1}],
//             ['K', {width: 18.65, height: 37, type: 1}],
//             ['L', {width: 18.65, height: 37, type: 1}],
//             ['M', {width: 18.65, height: 37, type: 1}],
//             ['N', {width: 18.65, height: 37, type: 1}],
//             ['O', {width: 18.65, height: 37, type: 1}],
//             ['P', {width: 18.65, height: 37, type: 1}],
//             ['Q', {width: 18.65, height: 37, type: 1}],
//             ['R', {width: 18.65, height: 37, type: 1}],
//             ['S', {width: 18.65, height: 37, type: 1}],
//             ['T', {width: 18.65, height: 37, type: 1}],
//             ['U', {width: 18.65, height: 37, type: 1}],
//             ['V', {width: 18.65, height: 37, type: 1}],
//             ['W', {width: 18.65, height: 37, type: 1}],
//             ['X', {width: 18.65, height: 37, type: 1}],
//             ['Y', {width: 18.65, height: 37, type: 1}],
//             ['Z', {width: 18.65, height: 37, type: 1}],
//             [',', {width: 18.65, height: 37, type: 1}],
//             ['.', {width: 18.65, height: 37, type: 1}],
//             [';', {width: 18.65, height: 37, type: 1}],
//             ['/', {width: 18.65, height: 37, type: 1}],
//             ['?', {width: 18.65, height: 37, type: 1}],
//             ['(', {width: 18.65, height: 37, type: 1}],
//             [')', {width: 18.65, height: 37, type: 1}],
//             ['[', {width: 18.65, height: 37, type: 1}],
//             [']', {width: 18.65, height: 37, type: 1}],
//             ['=', {width: 18.65, height: 37, type: 1}],
//             ['-', {width: 18.65, height: 37, type: 1}],
//             ['+', {width: 18.65, height: 37, type: 1}],
//             ['*', {width: 18.65, height: 37, type: 1}],
//             ['<', {width: 18.65, height: 37, type: 1}],
//             ['>', {width: 18.65, height: 37, type: 1}],
//             ['，', {width: 37.3, height: 37, type: 1}],
//             ['。', {width: 37.3, height: 37, type: 1}],
//             ['；', {width: 37.3, height: 37, type: 1}],
//             ['’', {width: 37.3, height: 37, type: 1}],
//             ['“', {width: 37.3, height: 37, type: 1}],
//             ['？', {width: 37.3, height: 37, type: 1}],
//             ['、', {width: 37.3, height: 37, type: 1}],
//             ['（', {width: 37.3, height: 37, type: 1}],
//             ['）', {width: 37.3, height: 37, type: 1}],
//             ['【', {width: 37.3, height: 37, type: 1}],
//             ['】', {width: 37.3, height: 37, type: 1}],
//             ['Ⅰ', {width: 37.3, height: 37, type: 1}],
//             ['Ⅱ', {width: 37.3, height: 37, type: 1}],
//             [':', {width: 18.65, height: 37, type: 1}],
//             ['：', {width: 37.3, height: 37, type: 1}],
//             ['{', {width: 18.65, height: 37, type: 1}],
//             ['}', {width: 18.65, height: 37, type: 1}],
//             ['☑', {width: 32.13, height: 37, type: 1}],
//             ['☐', {width: 32.13, height: 37, type: 1}],
//             ['⊙', {width: 37.3, height: 37, type: 1}],
//             ['⭘', {width: 37.3, height: 37, type: 1}],
//             ['◯', {width: 40.6, height: 37, type: 1}],
//             ['℃', {width: 37.3, height: 37, type: 1}],
//             [paraEnd, {width: 0, height: 37, type: 1}],
//         ])
//     );
//     CACHES.set(
//         '34.7', new Map([
//             ['我', {width: 34.7, height: 35, type: 1}], [' ', {width: 17.35, height: 35, type: 1}],
//             ['0', {width: 17.35, height: 35, type: 1}], ['1', {width: 17.35, height: 35, type: 1}],
//             ['2', {width: 17.35, height: 35, type: 1}], ['3', {width: 17.35, height: 35, type: 1}],
//             ['4', {width: 17.35, height: 35, type: 1}], ['5', {width: 17.35, height: 35, type: 1}],
//             ['6', {width: 17.35, height: 35, type: 1}], ['7', {width: 17.35, height: 35, type: 1}],
//             ['8', {width: 17.35, height: 35, type: 1}], ['9', {width: 17.35, height: 35, type: 1}],
//             ['a', {width: 17.35, height: 35, type: 1}],
//             ['b', {width: 17.35, height: 35, type: 1}],
//             ['c', {width: 17.35, height: 35, type: 1}],
//             ['d', {width: 17.35, height: 35, type: 1}],
//             ['e', {width: 17.35, height: 35, type: 1}],
//             ['f', {width: 17.35, height: 35, type: 1}],
//             ['g', {width: 17.35, height: 35, type: 1}],
//             ['h', {width: 17.35, height: 35, type: 1}],
//             ['i', {width: 17.35, height: 35, type: 1}],
//             ['j', {width: 17.35, height: 35, type: 1}],
//             ['k', {width: 17.35, height: 35, type: 1}],
//             ['l', {width: 17.35, height: 35, type: 1}],
//             ['m', {width: 17.35, height: 35, type: 1}],
//             ['n', {width: 17.35, height: 35, type: 1}],
//             ['o', {width: 17.35, height: 35, type: 1}],
//             ['p', {width: 17.35, height: 35, type: 1}],
//             ['q', {width: 17.35, height: 35, type: 1}],
//             ['r', {width: 17.35, height: 35, type: 1}],
//             ['s', {width: 17.35, height: 35, type: 1}],
//             ['t', {width: 17.35, height: 35, type: 1}],
//             ['u', {width: 17.35, height: 35, type: 1}],
//             ['v', {width: 17.35, height: 35, type: 1}],
//             ['w', {width: 17.35, height: 35, type: 1}],
//             ['x', {width: 17.35, height: 35, type: 1}],
//             ['y', {width: 17.35, height: 35, type: 1}],
//             ['z', {width: 17.35, height: 35, type: 1}],
//             ['A', {width: 17.35, height: 35, type: 1}],
//             ['B', {width: 17.35, height: 35, type: 1}],
//             ['C', {width: 17.35, height: 35, type: 1}],
//             ['D', {width: 17.35, height: 35, type: 1}],
//             ['E', {width: 17.35, height: 35, type: 1}],
//             ['F', {width: 17.35, height: 35, type: 1}],
//             ['G', {width: 17.35, height: 35, type: 1}],
//             ['H', {width: 17.35, height: 35, type: 1}],
//             ['I', {width: 17.35, height: 35, type: 1}],
//             ['J', {width: 17.35, height: 35, type: 1}],
//             ['K', {width: 17.35, height: 35, type: 1}],
//             ['L', {width: 17.35, height: 35, type: 1}],
//             ['M', {width: 17.35, height: 35, type: 1}],
//             ['N', {width: 17.35, height: 35, type: 1}],
//             ['O', {width: 17.35, height: 35, type: 1}],
//             ['P', {width: 17.35, height: 35, type: 1}],
//             ['Q', {width: 17.35, height: 35, type: 1}],
//             ['R', {width: 17.35, height: 35, type: 1}],
//             ['S', {width: 17.35, height: 35, type: 1}],
//             ['T', {width: 17.35, height: 35, type: 1}],
//             ['U', {width: 17.35, height: 35, type: 1}], ['V', {width: 17.35, height: 35, type: 1}],
//             ['W', {width: 17.35, height: 35, type: 1}], ['X', {width: 17.35, height: 35, type: 1}],
//             ['Y', {width: 17.35, height: 35, type: 1}], ['Z', {width: 17.35, height: 35, type: 1}],
//             [',', {width: 17.35, height: 35, type: 1}], ['.', {width: 17.35, height: 35, type: 1}],
//             [';', {width: 17.35, height: 35, type: 1}], ['/', {width: 17.35, height: 35, type: 1}],
//             ['?', {width: 17.35, height: 35, type: 1}], ['(', {width: 17.35, height: 35, type: 1}],
//             [')', {width: 17.35, height: 35, type: 1}], ['[', {width: 17.35, height: 35, type: 1}],
//             [']', {width: 17.35, height: 35, type: 1}], ['=', {width: 17.35, height: 35, type: 1}],
//             ['-', {width: 17.35, height: 35, type: 1}], ['+', {width: 17.35, height: 35, type: 1}],
//             ['*', {width: 17.35, height: 35, type: 1}], ['<', {width: 17.35, height: 35, type: 1}],
//             ['>', {width: 17.35, height: 35, type: 1}], ['，', {width: 34.7, height: 35, type: 1}],
//             ['。', {width: 34.7, height: 35, type: 1}], ['；', {width: 34.7, height: 35, type: 1}],
//             ['’', {width: 34.7, height: 35, type: 1}], ['“', {width: 34.7, height: 35, type: 1}],
//             ['？', {width: 34.7, height: 35, type: 1}], ['、', {width: 34.7, height: 35, type: 1}],
//             ['（', {width: 34.7, height: 35, type: 1}], ['）', {width: 34.7, height: 35, type: 1}],
//             ['【', {width: 34.7, height: 35, type: 1}], ['】', {width: 34.7, height: 35, type: 1}],
//             [paraEnd, {width: 0, height: 35, type: 1}], ['：', {width: 34.7, height: 35, type: 1}],
//             [':', {width: 17.35, height: 35, type: 1}],
//             ['{', {width: 17.35, height: 35, type: 1}],
//             ['}', {width: 17.35, height: 35, type: 1}],
//             ['☑', {width: 29.89, height: 35, type: 1}],
//             ['☐', {width: 29.89, height: 35, type: 1}],
//             ['⊙', {width: 34.7, height: 35, type: 1}],
//             ['⭘', {width: 34.7, height: 35, type: 1}],
//             ['◯', {width: 37.77, height: 35, type: 1}],
//             ['℃', {width: 34.7, height: 35, type: 1}], ['Ⅰ', {width: 34.7, height: 35, type: 1}],
//             ['Ⅱ', {width: 34.7, height: 35, type: 1}],
//         ])
//     );
//     CACHES.set(
//         '32', new Map([
//             ['我', {width: 32, height: 33, type: 1}], [' ', {width: 16, height: 33, type: 1}],
//             ['0', {width: 16, height: 33, type: 1}], ['1', {width: 16, height: 33, type: 1}],
//             ['2', {width: 16, height: 33, type: 1}], ['3', {width: 16, height: 33, type: 1}],
//             ['4', {width: 16, height: 33, type: 1}], ['5', {width: 16, height: 33, type: 1}],
//             ['6', {width: 16, height: 33, type: 1}], ['7', {width: 16, height: 33, type: 1}],
//             ['8', {width: 16, height: 33, type: 1}], ['9', {width: 16, height: 33, type: 1}],
//             ['a', {width: 16, height: 33, type: 1}], ['b', {width: 16, height: 33, type: 1}],
//             ['c', {width: 16, height: 33, type: 1}], ['d', {width: 16, height: 33, type: 1}],
//             ['e', {width: 16, height: 33, type: 1}], ['f', {width: 16, height: 33, type: 1}],
//             ['g', {width: 16, height: 33, type: 1}], ['h', {width: 16, height: 33, type: 1}],
//             ['i', {width: 16, height: 33, type: 1}], ['j', {width: 16, height: 33, type: 1}],
//             ['k', {width: 16, height: 33, type: 1}], ['l', {width: 16, height: 33, type: 1}],
//             ['m', {width: 16, height: 33, type: 1}], ['n', {width: 16, height: 33, type: 1}],
//             ['o', {width: 16, height: 33, type: 1}], ['p', {width: 16, height: 33, type: 1}],
//             ['q', {width: 16, height: 33, type: 1}], ['r', {width: 16, height: 33, type: 1}],
//             ['s', {width: 16, height: 33, type: 1}], ['t', {width: 16, height: 33, type: 1}],
//             ['u', {width: 16, height: 33, type: 1}], ['v', {width: 16, height: 33, type: 1}],
//             ['w', {width: 16, height: 33, type: 1}], ['x', {width: 16, height: 33, type: 1}],
//             ['y', {width: 16, height: 33, type: 1}], ['z', {width: 16, height: 33, type: 1}],
//             ['A', {width: 16, height: 33, type: 1}], ['B', {width: 16, height: 33, type: 1}],
//             ['C', {width: 16, height: 33, type: 1}], ['D', {width: 16, height: 33, type: 1}],
//             ['E', {width: 16, height: 33, type: 1}], ['F', {width: 16, height: 33, type: 1}],
//             ['G', {width: 16, height: 33, type: 1}], ['H', {width: 16, height: 33, type: 1}],
//             ['I', {width: 16, height: 33, type: 1}], ['J', {width: 16, height: 33, type: 1}],
//             ['K', {width: 16, height: 33, type: 1}], ['L', {width: 16, height: 33, type: 1}],
//             ['M', {width: 16, height: 33, type: 1}], ['N', {width: 16, height: 33, type: 1}],
//             ['O', {width: 16, height: 33, type: 1}], ['P', {width: 16, height: 33, type: 1}],
//             ['Q', {width: 16, height: 33, type: 1}], ['R', {width: 16, height: 33, type: 1}],
//             ['S', {width: 16, height: 33, type: 1}], ['T', {width: 16, height: 33, type: 1}],
//             ['U', {width: 16, height: 33, type: 1}], ['V', {width: 16, height: 33, type: 1}],
//             ['W', {width: 16, height: 33, type: 1}], ['X', {width: 16, height: 33, type: 1}],
//             ['Y', {width: 16, height: 33, type: 1}], ['Z', {width: 16, height: 33, type: 1}],
//             [',', {width: 16, height: 33, type: 1}], ['.', {width: 16, height: 33, type: 1}],
//             [';', {width: 16, height: 33, type: 1}], ['/', {width: 16, height: 33, type: 1}],
//             ['?', {width: 16, height: 33, type: 1}], ['(', {width: 16, height: 33, type: 1}],
//             [')', {width: 16, height: 33, type: 1}], ['[', {width: 16, height: 33, type: 1}],
//             [']', {width: 16, height: 33, type: 1}], ['=', {width: 16, height: 33, type: 1}],
//             ['-', {width: 16, height: 33, type: 1}], ['+', {width: 16, height: 33, type: 1}],
//             ['*', {width: 16, height: 33, type: 1}], ['<', {width: 16, height: 33, type: 1}],
//             ['>', {width: 16, height: 33, type: 1}], ['，', {width: 32, height: 33, type: 1}],
//             ['。', {width: 32, height: 33, type: 1}], ['；', {width: 32, height: 33, type: 1}],
//             ['’', {width: 32, height: 33, type: 1}], ['“', {width: 32, height: 33, type: 1}],
//             ['？', {width: 32, height: 33, type: 1}], ['、', {width: 32, height: 33, type: 1}],
//             ['（', {width: 32, height: 33, type: 1}], ['）', {width: 32, height: 33, type: 1}],
//             ['【', {width: 32, height: 33, type: 1}], ['】', {width: 32, height: 33, type: 1}],
//             [paraEnd, {width: 0, height: 32, type: 1}], ['：', {width: 32, height: 33, type: 1}],
//             [':', {width: 16, height: 33, type: 1}],
//             ['{', {width: 16, height: 33, type: 1}],
//             ['}', {width: 16, height: 33, type: 1}],
//             ['☑', {width: 27.56, height: 33, type: 1}],
//             ['☐', {width: 27.56, height: 33, type: 1}],
//             ['⊙', {width: 32, height: 33, type: 1}],
//             ['⭘', {width: 32, height: 33, type: 1}],
//             ['◯', {width: 34.83, height: 33, type: 1}],
//             ['℃', {width: 32, height: 33, type: 1}], ['Ⅰ', {width: 32, height: 33, type: 1}],
//             ['Ⅱ', {width: 32, height: 33, type: 1}],
//         ])
//     );
//     CACHES.set(
//         '29.3', new Map([
//             ['我', {width: 29.3, height: 29, type: 1}],
//             [' ', {width: 14.65, height: 29, type: 1}],
//             ['0', {width: 14.65, height: 29, type: 1}],
//             ['1', {width: 14.65, height: 29, type: 1}],
//             ['2', {width: 14.65, height: 29, type: 1}],
//             ['3', {width: 14.65, height: 29, type: 1}],
//             ['4', {width: 14.65, height: 29, type: 1}],
//             ['5', {width: 14.65, height: 29, type: 1}],
//             ['6', {width: 14.65, height: 29, type: 1}],
//             ['7', {width: 14.65, height: 29, type: 1}],
//             ['8', {width: 14.65, height: 29, type: 1}],
//             ['9', {width: 14.65, height: 29, type: 1}],
//             ['a', {width: 14.65, height: 29, type: 1}],
//             ['b', {width: 14.65, height: 29, type: 1}],
//             ['c', {width: 14.65, height: 29, type: 1}],
//             ['d', {width: 14.65, height: 29, type: 1}],
//             ['e', {width: 14.65, height: 29, type: 1}],
//             ['f', {width: 14.65, height: 29, type: 1}],
//             ['g', {width: 14.65, height: 29, type: 1}],
//             ['h', {width: 14.65, height: 29, type: 1}],
//             ['i', {width: 14.65, height: 29, type: 1}],
//             ['j', {width: 14.65, height: 29, type: 1}],
//             ['k', {width: 14.65, height: 29, type: 1}],
//             ['l', {width: 14.65, height: 29, type: 1}],
//             ['m', {width: 14.65, height: 29, type: 1}],
//             ['n', {width: 14.65, height: 29, type: 1}],
//             ['o', {width: 14.65, height: 29, type: 1}],
//             ['p', {width: 14.65, height: 29, type: 1}],
//             ['q', {width: 14.65, height: 29, type: 1}],
//             ['r', {width: 14.65, height: 29, type: 1}],
//             ['s', {width: 14.65, height: 29, type: 1}],
//             ['t', {width: 14.65, height: 29, type: 1}],
//             ['u', {width: 14.65, height: 29, type: 1}],
//             ['v', {width: 14.65, height: 29, type: 1}],
//             ['w', {width: 14.65, height: 29, type: 1}],
//             ['x', {width: 14.65, height: 29, type: 1}],
//             ['y', {width: 14.65, height: 29, type: 1}],
//             ['z', {width: 14.65, height: 29, type: 1}],
//             ['A', {width: 14.65, height: 29, type: 1}],
//             ['B', {width: 14.65, height: 29, type: 1}],
//             ['C', {width: 14.65, height: 29, type: 1}],
//             ['D', {width: 14.65, height: 29, type: 1}],
//             ['E', {width: 14.65, height: 29, type: 1}],
//             ['F', {width: 14.65, height: 29, type: 1}],
//             ['G', {width: 14.65, height: 29, type: 1}],
//             ['H', {width: 14.65, height: 29, type: 1}],
//             ['I', {width: 14.65, height: 29, type: 1}],
//             ['J', {width: 14.65, height: 29, type: 1}],
//             ['K', {width: 14.65, height: 29, type: 1}],
//             ['L', {width: 14.65, height: 29, type: 1}],
//             ['M', {width: 14.65, height: 29, type: 1}],
//             ['N', {width: 14.65, height: 29, type: 1}],
//             ['O', {width: 14.65, height: 29, type: 1}],
//             ['P', {width: 14.65, height: 29, type: 1}],
//             ['Q', {width: 14.65, height: 29, type: 1}],
//             ['R', {width: 14.65, height: 29, type: 1}],
//             ['S', {width: 14.65, height: 29, type: 1}],
//             ['T', {width: 14.65, height: 29, type: 1}],
//             ['U', {width: 14.65, height: 29, type: 1}],
//             ['V', {width: 14.65, height: 29, type: 1}],
//             ['W', {width: 14.65, height: 29, type: 1}],
//             ['X', {width: 14.65, height: 29, type: 1}],
//             ['Y', {width: 14.65, height: 29, type: 1}],
//             ['Z', {width: 14.65, height: 29, type: 1}],
//             [',', {width: 14.65, height: 29, type: 1}],
//             ['.', {width: 14.65, height: 29, type: 1}],
//             [';', {width: 14.65, height: 29, type: 1}], [':', {width: 14.65, height: 29, type: 1}],
//             ['/', {width: 14.65, height: 29, type: 1}],
//             ['?', {width: 14.65, height: 29, type: 1}],
//             ['(', {width: 14.65, height: 29, type: 1}],
//             [')', {width: 14.65, height: 29, type: 1}],
//             ['[', {width: 14.65, height: 29, type: 1}],
//             [']', {width: 14.65, height: 29, type: 1}],
//             ['=', {width: 14.65, height: 29, type: 1}],
//             ['-', {width: 14.65, height: 29, type: 1}],
//             ['+', {width: 14.65, height: 29, type: 1}],
//             ['*', {width: 14.65, height: 29, type: 1}],
//             ['<', {width: 14.65, height: 29, type: 1}],
//             ['>', {width: 14.65, height: 29, type: 1}],
//             ['，', {width: 29.3, height: 29, type: 1}],
//             ['。', {width: 29.3, height: 29, type: 1}],
//             ['；', {width: 29.3, height: 29, type: 1}],
//             ['’', {width: 29.3, height: 29, type: 1}],
//             ['“', {width: 29.3, height: 29, type: 1}],
//             ['？', {width: 29.3, height: 29, type: 1}],
//             ['、', {width: 29.3, height: 29, type: 1}],
//             ['（', {width: 29.3, height: 29, type: 1}],
//             ['）', {width: 29.3, height: 29, type: 1}],
//             ['【', {width: 29.3, height: 29, type: 1}],
//             ['】', {width: 29.3, height: 29, type: 1}],
//             [paraEnd, {width: 0, height: 29, type: 1}], ['：', {width: 29.3, height: 29, type: 1}],
//             ['{', {width: 14.65, height: 29, type: 1}],
//             ['}', {width: 14.65, height: 29, type: 1}],
//             ['☑', {width: 25.24, height: 29, type: 1}],
//             ['☐', {width: 25.24, height: 29, type: 1}],
//             ['⊙', {width: 29.3, height: 29, type: 1}],
//             ['⭘', {width: 29.3, height: 29, type: 1}],
//             ['◯', {width: 31.89, height: 29, type: 1}],
//             ['℃', {width: 29.3, height: 29, type: 1}], ['Ⅰ', {width: 29.3, height: 29, type: 1}],
//             ['Ⅱ', {width: 29.3, height: 29, type: 1}],
//         ])
//     );
//     CACHES.set(
//         '26.7', new Map([
//             ['我', {width: 26.7, height: 27, type: 1}],
//             [' ', {width: 13.35, height: 27, type: 1}],
//             ['0', {width: 13.35, height: 27, type: 1}],
//             ['1', {width: 13.35, height: 27, type: 1}],
//             ['2', {width: 13.35, height: 27, type: 1}],
//             ['3', {width: 13.35, height: 27, type: 1}],
//             ['4', {width: 13.35, height: 27, type: 1}],
//             ['5', {width: 13.35, height: 27, type: 1}],
//             ['6', {width: 13.35, height: 27, type: 1}],
//             ['7', {width: 13.35, height: 27, type: 1}],
//             ['8', {width: 13.35, height: 27, type: 1}],
//             ['9', {width: 13.35, height: 27, type: 1}],
//             ['a', {width: 13.35, height: 27, type: 1}],
//             ['b', {width: 13.35, height: 27, type: 1}],
//             ['c', {width: 13.35, height: 27, type: 1}],
//             ['d', {width: 13.35, height: 27, type: 1}],
//             ['e', {width: 13.35, height: 27, type: 1}],
//             ['f', {width: 13.35, height: 27, type: 1}],
//             ['g', {width: 13.35, height: 27, type: 1}],
//             ['h', {width: 13.35, height: 27, type: 1}],
//             ['i', {width: 13.35, height: 27, type: 1}],
//             ['j', {width: 13.35, height: 27, type: 1}],
//             ['k', {width: 13.35, height: 27, type: 1}],
//             ['l', {width: 13.35, height: 27, type: 1}],
//             ['m', {width: 13.35, height: 27, type: 1}],
//             ['n', {width: 13.35, height: 27, type: 1}],
//             ['o', {width: 13.35, height: 27, type: 1}],
//             ['p', {width: 13.35, height: 27, type: 1}],
//             ['q', {width: 13.35, height: 27, type: 1}],
//             ['r', {width: 13.35, height: 27, type: 1}],
//             ['s', {width: 13.35, height: 27, type: 1}],
//             ['t', {width: 13.35, height: 27, type: 1}],
//             ['u', {width: 13.35, height: 27, type: 1}],
//             ['v', {width: 13.35, height: 27, type: 1}],
//             ['w', {width: 13.35, height: 27, type: 1}],
//             ['x', {width: 13.35, height: 27, type: 1}],
//             ['y', {width: 13.35, height: 27, type: 1}],
//             ['z', {width: 13.35, height: 27, type: 1}],
//             ['A', {width: 13.35, height: 27, type: 1}],
//             ['B', {width: 13.35, height: 27, type: 1}],
//             ['C', {width: 13.35, height: 27, type: 1}],
//             ['D', {width: 13.35, height: 27, type: 1}],
//             ['E', {width: 13.35, height: 27, type: 1}],
//             ['F', {width: 13.35, height: 27, type: 1}],
//             ['G', {width: 13.35, height: 27, type: 1}],
//             ['H', {width: 13.35, height: 27, type: 1}],
//             ['I', {width: 13.35, height: 27, type: 1}],
//             ['J', {width: 13.35, height: 27, type: 1}],
//             ['K', {width: 13.35, height: 27, type: 1}],
//             ['L', {width: 13.35, height: 27, type: 1}],
//             ['M', {width: 13.35, height: 27, type: 1}],
//             ['N', {width: 13.35, height: 27, type: 1}],
//             ['O', {width: 13.35, height: 27, type: 1}],
//             ['P', {width: 13.35, height: 27, type: 1}],
//             ['Q', {width: 13.35, height: 27, type: 1}],
//             ['R', {width: 13.35, height: 27, type: 1}],
//             ['S', {width: 13.35, height: 27, type: 1}],
//             ['T', {width: 13.35, height: 27, type: 1}],
//             ['U', {width: 13.35, height: 27, type: 1}],
//             ['V', {width: 13.35, height: 27, type: 1}],
//             ['W', {width: 13.35, height: 27, type: 1}],
//             ['X', {width: 13.35, height: 27, type: 1}],
//             ['Y', {width: 13.35, height: 27, type: 1}],
//             ['Z', {width: 13.35, height: 27, type: 1}],
//             [',', {width: 13.35, height: 27, type: 1}],
//             ['.', {width: 13.35, height: 27, type: 1}],
//             [';', {width: 13.35, height: 27, type: 1}],
//             ['/', {width: 13.35, height: 27, type: 1}],
//             ['?', {width: 13.35, height: 27, type: 1}],
//             ['(', {width: 13.35, height: 27, type: 1}],
//             [')', {width: 13.35, height: 27, type: 1}],
//             ['[', {width: 13.35, height: 27, type: 1}],
//             [']', {width: 13.35, height: 27, type: 1}],
//             ['=', {width: 13.35, height: 27, type: 1}],
//             ['-', {width: 13.35, height: 27, type: 1}],
//             ['+', {width: 13.35, height: 27, type: 1}],
//             ['*', {width: 13.35, height: 27, type: 1}],
//             ['<', {width: 13.35, height: 27, type: 1}],
//             ['>', {width: 13.35, height: 27, type: 1}],
//             ['，', {width: 26.7, height: 27, type: 1}],
//             ['。', {width: 26.7, height: 27, type: 1}],
//             ['；', {width: 26.7, height: 27, type: 1}],
//             ['’', {width: 26.7, height: 27, type: 1}],
//             ['“', {width: 26.7, height: 27, type: 1}],
//             ['？', {width: 26.7, height: 27, type: 1}],
//             ['、', {width: 26.7, height: 27, type: 1}],
//             ['（', {width: 26.7, height: 27, type: 1}],
//             ['）', {width: 26.7, height: 27, type: 1}],
//             ['【', {width: 26.7, height: 27, type: 1}],
//             ['】', {width: 26.7, height: 27, type: 1}],
//             ['Ⅰ', {width: 26.7, height: 27, type: 1}],
//             ['Ⅱ', {width: 26.7, height: 27, type: 1}],
//             [':', {width: 13.35, height: 27, type: 1}],
//             ['：', {width: 26.7, height: 27, type: 1}], ['{', {width: 13.35, height: 27, type: 1}],
//             ['}', {width: 13.35, height: 27, type: 1}],
//             ['☑', {width: 23, height: 27, type: 1}],
//             ['☐', {width: 23, height: 27, type: 1}],
//             ['⊙', {width: 26.7, height: 27, type: 1}],
//             ['⭘', {width: 26.7, height: 27, type: 1}],
//             ['◯', {width: 29.06, height: 27, type: 1}],
//             ['℃', {width: 26.7, height: 27, type: 1}],
//             [paraEnd, {width: 0, height: 27, type: 1}],
//         ])
//     );
//     CACHES.set(
//         '24', new Map([
//             ['我', {width: 24, height: 24, type: 1}],
//             [' ', {width: 12, height: 24, type: 1}],
//             ['0', {width: 12, height: 24, type: 1}],
//             ['1', {width: 12, height: 24, type: 1}],
//             ['2', {width: 12, height: 24, type: 1}],
//             ['3', {width: 12, height: 24, type: 1}],
//             ['4', {width: 12, height: 24, type: 1}],
//             ['5', {width: 12, height: 24, type: 1}],
//             ['6', {width: 12, height: 24, type: 1}],
//             ['7', {width: 12, height: 24, type: 1}],
//             ['8', {width: 12, height: 24, type: 1}],
//             ['9', {width: 12, height: 24, type: 1}],
//             ['a', {width: 12, height: 24, type: 1}],
//             ['b', {width: 12, height: 24, type: 1}],
//             ['c', {width: 12, height: 24, type: 1}],
//             ['d', {width: 12, height: 24, type: 1}],
//             ['e', {width: 12, height: 24, type: 1}],
//             ['f', {width: 12, height: 24, type: 1}],
//             ['g', {width: 12, height: 24, type: 1}],
//             ['h', {width: 12, height: 24, type: 1}],
//             ['i', {width: 12, height: 24, type: 1}],
//             ['j', {width: 12, height: 24, type: 1}],
//             ['k', {width: 12, height: 24, type: 1}],
//             ['l', {width: 12, height: 24, type: 1}],
//             ['m', {width: 12, height: 24, type: 1}],
//             ['n', {width: 12, height: 24, type: 1}],
//             ['o', {width: 12, height: 24, type: 1}],
//             ['p', {width: 12, height: 24, type: 1}],
//             ['q', {width: 12, height: 24, type: 1}],
//             ['r', {width: 12, height: 24, type: 1}],
//             ['s', {width: 12, height: 24, type: 1}],
//             ['t', {width: 12, height: 24, type: 1}],
//             ['u', {width: 12, height: 24, type: 1}],
//             ['v', {width: 12, height: 24, type: 1}],
//             ['w', {width: 12, height: 24, type: 1}],
//             ['x', {width: 12, height: 24, type: 1}],
//             ['y', {width: 12, height: 24, type: 1}],
//             ['z', {width: 12, height: 24, type: 1}],
//             ['A', {width: 12, height: 24, type: 1}],
//             ['B', {width: 12, height: 24, type: 1}],
//             ['C', {width: 12, height: 24, type: 1}],
//             ['D', {width: 12, height: 24, type: 1}],
//             ['E', {width: 12, height: 24, type: 1}],
//             ['F', {width: 12, height: 24, type: 1}],
//             ['G', {width: 12, height: 24, type: 1}],
//             ['H', {width: 12, height: 24, type: 1}],
//             ['I', {width: 12, height: 24, type: 1}],
//             ['J', {width: 12, height: 24, type: 1}],
//             ['K', {width: 12, height: 24, type: 1}],
//             ['L', {width: 12, height: 24, type: 1}],
//             ['M', {width: 12, height: 24, type: 1}],
//             ['N', {width: 12, height: 24, type: 1}],
//             ['O', {width: 12, height: 24, type: 1}],
//             ['P', {width: 12, height: 24, type: 1}],
//             ['Q', {width: 12, height: 24, type: 1}],
//             ['R', {width: 12, height: 24, type: 1}],
//             ['S', {width: 12, height: 24, type: 1}],
//             ['T', {width: 12, height: 24, type: 1}],
//             ['U', {width: 12, height: 24, type: 1}],
//             ['V', {width: 12, height: 24, type: 1}],
//             ['W', {width: 12, height: 24, type: 1}],
//             ['X', {width: 12, height: 24, type: 1}],
//             ['Y', {width: 12, height: 24, type: 1}],
//             ['Z', {width: 12, height: 24, type: 1}],
//             [',', {width: 12, height: 24, type: 1}],
//             ['.', {width: 12, height: 24, type: 1}],
//             [';', {width: 12, height: 24, type: 1}],
//             ['/', {width: 12, height: 24, type: 1}],
//             ['?', {width: 12, height: 24, type: 1}],
//             ['(', {width: 12, height: 24, type: 1}],
//             [')', {width: 12, height: 24, type: 1}],
//             ['[', {width: 12, height: 24, type: 1}],
//             [']', {width: 12, height: 24, type: 1}],
//             ['=', {width: 12, height: 24, type: 1}],
//             ['-', {width: 12, height: 24, type: 1}],
//             ['+', {width: 12, height: 24, type: 1}],
//             ['*', {width: 12, height: 24, type: 1}],
//             ['<', {width: 12, height: 24, type: 1}],
//             ['>', {width: 12, height: 24, type: 1}], [':', {width: 12, height: 24, type: 1}],
//             ['，', {width: 24, height: 24, type: 1}],
//             ['。', {width: 24, height: 24, type: 1}],
//             ['；', {width: 24, height: 24, type: 1}],
//             ['’', {width: 24, height: 24, type: 1}],
//             ['“', {width: 24, height: 24, type: 1}],
//             ['？', {width: 24, height: 24, type: 1}],
//             ['、', {width: 24, height: 24, type: 1}],
//             ['（', {width: 24, height: 24, type: 1}],
//             ['）', {width: 24, height: 24, type: 1}],
//             ['【', {width: 24, height: 24, type: 1}],
//             ['】', {width: 24, height: 24, type: 1}],
//             [paraEnd, {width: 0, height: 24, type: 1}], ['：', {width: 24, height: 24, type: 1}],
//             ['{', {width: 12, height: 24, type: 1}],
//             ['}', {width: 12, height: 24, type: 1}],
//             ['☑', {width: 20.67, height: 24, type: 1}],
//             ['☐', {width: 20.67, height: 24, type: 1}],
//             ['⊙', {width: 24, height: 24, type: 1}],
//             ['⭘', {width: 24, height: 24, type: 1}],
//             ['◯', {width: 26.12, height: 24, type: 1}],
//             ['℃', {width: 24, height: 24, type: 1}], ['Ⅰ', {width: 24, height: 24, type: 1}],
//             ['Ⅱ', {width: 24, height: 24, type: 1}],
//         ])
//     );
//     CACHES.set(
//         '21.3', new Map([
//             ['我', {width: 21.3, height: 21, type: 1}],
//             [' ', {width: 10.65, height: 21, type: 1}],
//             ['0', {width: 10.65, height: 21, type: 1}],
//             ['1', {width: 10.65, height: 21, type: 1}],
//             ['2', {width: 10.65, height: 21, type: 1}],
//             ['3', {width: 10.65, height: 21, type: 1}],
//             ['4', {width: 10.65, height: 21, type: 1}],
//             ['5', {width: 10.65, height: 21, type: 1}],
//             ['6', {width: 10.65, height: 21, type: 1}],
//             ['7', {width: 10.65, height: 21, type: 1}],
//             ['8', {width: 10.65, height: 21, type: 1}],
//             ['9', {width: 10.65, height: 21, type: 1}],
//             ['a', {width: 10.65, height: 21, type: 1}],
//             ['b', {width: 10.65, height: 21, type: 1}],
//             ['c', {width: 10.65, height: 21, type: 1}],
//             ['d', {width: 10.65, height: 21, type: 1}],
//             ['e', {width: 10.65, height: 21, type: 1}],
//             ['f', {width: 10.65, height: 21, type: 1}],
//             ['g', {width: 10.65, height: 21, type: 1}],
//             ['h', {width: 10.65, height: 21, type: 1}],
//             ['i', {width: 10.65, height: 21, type: 1}],
//             ['j', {width: 10.65, height: 21, type: 1}],
//             ['k', {width: 10.65, height: 21, type: 1}],
//             ['l', {width: 10.65, height: 21, type: 1}],
//             ['m', {width: 10.65, height: 21, type: 1}],
//             ['n', {width: 10.65, height: 21, type: 1}],
//             ['o', {width: 10.65, height: 21, type: 1}],
//             ['p', {width: 10.65, height: 21, type: 1}],
//             ['q', {width: 10.65, height: 21, type: 1}],
//             ['r', {width: 10.65, height: 21, type: 1}],
//             ['s', {width: 10.65, height: 21, type: 1}],
//             ['t', {width: 10.65, height: 21, type: 1}],
//             ['u', {width: 10.65, height: 21, type: 1}],
//             ['v', {width: 10.65, height: 21, type: 1}],
//             ['w', {width: 10.65, height: 21, type: 1}],
//             ['x', {width: 10.65, height: 21, type: 1}],
//             ['y', {width: 10.65, height: 21, type: 1}],
//             ['z', {width: 10.65, height: 21, type: 1}],
//             ['A', {width: 10.65, height: 21, type: 1}],
//             ['B', {width: 10.65, height: 21, type: 1}],
//             ['C', {width: 10.65, height: 21, type: 1}],
//             ['D', {width: 10.65, height: 21, type: 1}],
//             ['E', {width: 10.65, height: 21, type: 1}],
//             ['F', {width: 10.65, height: 21, type: 1}],
//             ['G', {width: 10.65, height: 21, type: 1}],
//             ['H', {width: 10.65, height: 21, type: 1}],
//             ['I', {width: 10.65, height: 21, type: 1}],
//             ['J', {width: 10.65, height: 21, type: 1}],
//             ['K', {width: 10.65, height: 21, type: 1}],
//             ['L', {width: 10.65, height: 21, type: 1}],
//             ['M', {width: 10.65, height: 21, type: 1}],
//             ['N', {width: 10.65, height: 21, type: 1}],
//             ['O', {width: 10.65, height: 21, type: 1}],
//             ['P', {width: 10.65, height: 21, type: 1}],
//             ['Q', {width: 10.65, height: 21, type: 1}],
//             ['R', {width: 10.65, height: 21, type: 1}],
//             ['S', {width: 10.65, height: 21, type: 1}],
//             ['T', {width: 10.65, height: 21, type: 1}],
//             ['U', {width: 10.65, height: 21, type: 1}],
//             ['V', {width: 10.65, height: 21, type: 1}],
//             ['W', {width: 10.65, height: 21, type: 1}],
//             ['X', {width: 10.65, height: 21, type: 1}],
//             ['Y', {width: 10.65, height: 21, type: 1}],
//             ['Z', {width: 10.65, height: 21, type: 1}],
//             [',', {width: 10.65, height: 21, type: 1}],
//             ['.', {width: 10.65, height: 21, type: 1}],
//             [';', {width: 10.65, height: 21, type: 1}],
//             ['/', {width: 10.65, height: 21, type: 1}],
//             ['?', {width: 10.65, height: 21, type: 1}],
//             ['(', {width: 10.65, height: 21, type: 1}],
//             [')', {width: 10.65, height: 21, type: 1}],
//             ['[', {width: 10.65, height: 21, type: 1}],
//             [']', {width: 10.65, height: 21, type: 1}],
//             ['=', {width: 10.65, height: 21, type: 1}],
//             ['-', {width: 10.65, height: 21, type: 1}],
//             ['+', {width: 10.65, height: 21, type: 1}],
//             ['*', {width: 10.65, height: 21, type: 1}],
//             ['<', {width: 10.65, height: 21, type: 1}],
//             ['>', {width: 10.65, height: 21, type: 1}],
//             ['，', {width: 21.3, height: 21, type: 1}],
//             ['。', {width: 21.3, height: 21, type: 1}],
//             ['；', {width: 21.3, height: 21, type: 1}],
//             ['’', {width: 21.3, height: 21, type: 1}],
//             ['“', {width: 21.3, height: 21, type: 1}],
//             ['？', {width: 21.3, height: 21, type: 1}],
//             ['、', {width: 21.3, height: 21, type: 1}],
//             ['（', {width: 21.3, height: 21, type: 1}],
//             ['）', {width: 21.3, height: 21, type: 1}],
//             ['【', {width: 21.3, height: 21, type: 1}],
//             ['】', {width: 21.3, height: 21, type: 1}],
//             [paraEnd, {width: 0, height: 21, type: 1}],
//             ['：', {width: 21.3, height: 21, type: 1}], [':', {width: 10.65, height: 21, type: 1}],
//             ['{', {width: 10.65, height: 21, type: 1}],
//             ['}', {width: 10.65, height: 21, type: 1}],
//             ['☑', {width: 18.35, height: 21, type: 1}],
//             ['☐', {width: 18.35, height: 21, type: 1}],
//             ['⊙', {width: 21.3, height: 21, type: 1}],
//             ['⭘', {width: 21.3, height: 21, type: 1}],
//             ['◯', {width: 23.18, height: 21, type: 1}],
//             ['℃', {width: 21.3, height: 21, type: 1}], ['Ⅰ', {width: 21.3, height: 21, type: 1}],
//             ['Ⅱ', {width: 21.3, height: 21, type: 1}],
//         ])
//     );
//     CACHES.set(
//         '20', new Map([
//             ['我', {width: 20, height: 20, type: 1}],
//             [' ', {width: 10, height: 20, type: 1}],
//             ['0', {width: 10, height: 20, type: 1}],
//             ['1', {width: 10, height: 20, type: 1}],
//             ['2', {width: 10, height: 20, type: 1}],
//             ['3', {width: 10, height: 20, type: 1}],
//             ['4', {width: 10, height: 20, type: 1}],
//             ['5', {width: 10, height: 20, type: 1}],
//             ['6', {width: 10, height: 20, type: 1}],
//             ['7', {width: 10, height: 20, type: 1}],
//             ['8', {width: 10, height: 20, type: 1}],
//             ['9', {width: 10, height: 20, type: 1}],
//             ['a', {width: 10, height: 20, type: 1}],
//             ['b', {width: 10, height: 20, type: 1}],
//             ['c', {width: 10, height: 20, type: 1}],
//             ['d', {width: 10, height: 20, type: 1}],
//             ['e', {width: 10, height: 20, type: 1}],
//             ['f', {width: 10, height: 20, type: 1}],
//             ['g', {width: 10, height: 20, type: 1}],
//             ['h', {width: 10, height: 20, type: 1}],
//             ['i', {width: 10, height: 20, type: 1}],
//             ['j', {width: 10, height: 20, type: 1}],
//             ['k', {width: 10, height: 20, type: 1}],
//             ['l', {width: 10, height: 20, type: 1}],
//             ['m', {width: 10, height: 20, type: 1}],
//             ['n', {width: 10, height: 20, type: 1}],
//             ['o', {width: 10, height: 20, type: 1}],
//             ['p', {width: 10, height: 20, type: 1}],
//             ['q', {width: 10, height: 20, type: 1}],
//             ['r', {width: 10, height: 20, type: 1}],
//             ['s', {width: 10, height: 20, type: 1}],
//             ['t', {width: 10, height: 20, type: 1}],
//             ['u', {width: 10, height: 20, type: 1}],
//             ['v', {width: 10, height: 20, type: 1}],
//             ['w', {width: 10, height: 20, type: 1}],
//             ['x', {width: 10, height: 20, type: 1}],
//             ['y', {width: 10, height: 20, type: 1}],
//             ['z', {width: 10, height: 20, type: 1}],
//             ['A', {width: 10, height: 20, type: 1}],
//             ['B', {width: 10, height: 20, type: 1}],
//             ['C', {width: 10, height: 20, type: 1}],
//             ['D', {width: 10, height: 20, type: 1}],
//             ['E', {width: 10, height: 20, type: 1}],
//             ['F', {width: 10, height: 20, type: 1}],
//             ['G', {width: 10, height: 20, type: 1}],
//             ['H', {width: 10, height: 20, type: 1}],
//             ['I', {width: 10, height: 20, type: 1}],
//             ['J', {width: 10, height: 20, type: 1}],
//             ['K', {width: 10, height: 20, type: 1}],
//             ['L', {width: 10, height: 20, type: 1}],
//             ['M', {width: 10, height: 20, type: 1}],
//             ['N', {width: 10, height: 20, type: 1}],
//             ['O', {width: 10, height: 20, type: 1}],
//             ['P', {width: 10, height: 20, type: 1}],
//             ['Q', {width: 10, height: 20, type: 1}],
//             ['R', {width: 10, height: 20, type: 1}],
//             ['S', {width: 10, height: 20, type: 1}],
//             ['T', {width: 10, height: 20, type: 1}],
//             ['U', {width: 10, height: 20, type: 1}],
//             ['V', {width: 10, height: 20, type: 1}],
//             ['W', {width: 10, height: 20, type: 1}],
//             ['X', {width: 10, height: 20, type: 1}],
//             ['Y', {width: 10, height: 20, type: 1}],
//             ['Z', {width: 10, height: 20, type: 1}],
//             [',', {width: 10, height: 20, type: 1}],
//             ['.', {width: 10, height: 20, type: 1}],
//             [';', {width: 10, height: 20, type: 1}],
//             ['/', {width: 10, height: 20, type: 1}],
//             ['?', {width: 10, height: 20, type: 1}],
//             ['(', {width: 10, height: 20, type: 1}],
//             [')', {width: 10, height: 20, type: 1}],
//             ['[', {width: 10, height: 20, type: 1}],
//             [']', {width: 10, height: 20, type: 1}],
//             ['=', {width: 10, height: 20, type: 1}],
//             ['-', {width: 10, height: 20, type: 1}],
//             ['+', {width: 10, height: 20, type: 1}],
//             ['*', {width: 10, height: 20, type: 1}],
//             ['<', {width: 10, height: 20, type: 1}],
//             ['>', {width: 10, height: 20, type: 1}],
//             ['，', {width: 20, height: 20, type: 1}],
//             ['。', {width: 20, height: 20, type: 1}],
//             ['；', {width: 20, height: 20, type: 1}],
//             ['’', {width: 20, height: 20, type: 1}],
//             ['“', {width: 20, height: 20, type: 1}],
//             ['？', {width: 20, height: 20, type: 1}],
//             ['、', {width: 20, height: 20, type: 1}],
//             ['（', {width: 20, height: 20, type: 1}],
//             ['）', {width: 20, height: 20, type: 1}],
//             ['【', {width: 20, height: 20, type: 1}],
//             ['】', {width: 20, height: 20, type: 1}],
//             [paraEnd, {width: 0, height: 20, type: 1}],
//             ['：', {width: 20, height: 20, type: 1}], [':', {width: 10, height: 20, type: 1}],
//             ['{', {width: 10, height: 20, type: 1}],
//             ['}', {width: 10, height: 20, type: 1}],
//             ['☑', {width: 17.23, height: 20, type: 1}],
//             ['☐', {width: 17.23, height: 20, type: 1}],
//             ['⊙', {width: 20, height: 20, type: 1}],
//             ['⭘', {width: 20, height: 20, type: 1}],
//             ['◯', {width: 21.77, height: 20, type: 1}],
//             ['℃', {width: 20, height: 20, type: 1}], ['Ⅰ', {width: 20, height: 20, type: 1}],
//             ['Ⅱ', {width: 20, height: 20, type: 1}],
//         ])
//     );
//     CACHES.set(
//         '18.7', new Map([
//             ['我', {width: 18.7, height: 19, type: 1}],
//             [' ', {width: 9.35, height: 19, type: 1}],
//             ['0', {width: 9.35, height: 19, type: 1}],
//             ['1', {width: 9.35, height: 19, type: 1}],
//             ['2', {width: 9.35, height: 19, type: 1}],
//             ['3', {width: 9.35, height: 19, type: 1}],
//             ['4', {width: 9.35, height: 19, type: 1}],
//             ['5', {width: 9.35, height: 19, type: 1}],
//             ['6', {width: 9.35, height: 19, type: 1}],
//             ['7', {width: 9.35, height: 19, type: 1}],
//             ['8', {width: 9.35, height: 19, type: 1}],
//             ['9', {width: 9.35, height: 19, type: 1}],
//             ['a', {width: 9.35, height: 19, type: 1}],
//             ['b', {width: 9.35, height: 19, type: 1}],
//             ['c', {width: 9.35, height: 19, type: 1}],
//             ['d', {width: 9.35, height: 19, type: 1}],
//             ['e', {width: 9.35, height: 19, type: 1}],
//             ['f', {width: 9.35, height: 19, type: 1}],
//             ['g', {width: 9.35, height: 19, type: 1}],
//             ['h', {width: 9.35, height: 19, type: 1}],
//             ['i', {width: 9.35, height: 19, type: 1}],
//             ['j', {width: 9.35, height: 19, type: 1}],
//             ['k', {width: 9.35, height: 19, type: 1}],
//             ['l', {width: 9.35, height: 19, type: 1}],
//             ['m', {width: 9.35, height: 19, type: 1}],
//             ['n', {width: 9.35, height: 19, type: 1}],
//             ['o', {width: 9.35, height: 19, type: 1}],
//             ['p', {width: 9.35, height: 19, type: 1}],
//             ['q', {width: 9.35, height: 19, type: 1}],
//             ['r', {width: 9.35, height: 19, type: 1}],
//             ['s', {width: 9.35, height: 19, type: 1}],
//             ['t', {width: 9.35, height: 19, type: 1}],
//             ['u', {width: 9.35, height: 19, type: 1}],
//             ['v', {width: 9.35, height: 19, type: 1}],
//             ['w', {width: 9.35, height: 19, type: 1}],
//             ['x', {width: 9.35, height: 19, type: 1}],
//             ['y', {width: 9.35, height: 19, type: 1}],
//             ['z', {width: 9.35, height: 19, type: 1}],
//             ['A', {width: 9.35, height: 19, type: 1}],
//             ['B', {width: 9.35, height: 19, type: 1}],
//             ['C', {width: 9.35, height: 19, type: 1}],
//             ['D', {width: 9.35, height: 19, type: 1}],
//             ['E', {width: 9.35, height: 19, type: 1}],
//             ['F', {width: 9.35, height: 19, type: 1}],
//             ['G', {width: 9.35, height: 19, type: 1}],
//             ['H', {width: 9.35, height: 19, type: 1}],
//             ['I', {width: 9.35, height: 19, type: 1}],
//             ['J', {width: 9.35, height: 19, type: 1}],
//             ['K', {width: 9.35, height: 19, type: 1}],
//             ['L', {width: 9.35, height: 19, type: 1}],
//             ['M', {width: 9.35, height: 19, type: 1}],
//             ['N', {width: 9.35, height: 19, type: 1}],
//             ['O', {width: 9.35, height: 19, type: 1}],
//             ['P', {width: 9.35, height: 19, type: 1}],
//             ['Q', {width: 9.35, height: 19, type: 1}],
//             ['R', {width: 9.35, height: 19, type: 1}],
//             ['S', {width: 9.35, height: 19, type: 1}],
//             ['T', {width: 9.35, height: 19, type: 1}],
//             ['U', {width: 9.35, height: 19, type: 1}],
//             ['V', {width: 9.35, height: 19, type: 1}],
//             ['W', {width: 9.35, height: 19, type: 1}],
//             ['X', {width: 9.35, height: 19, type: 1}],
//             ['Y', {width: 9.35, height: 19, type: 1}],
//             ['Z', {width: 9.35, height: 19, type: 1}],
//             [',', {width: 9.35, height: 19, type: 1}],
//             ['.', {width: 9.35, height: 19, type: 1}],
//             [';', {width: 9.35, height: 19, type: 1}],
//             ['/', {width: 9.35, height: 19, type: 1}],
//             ['?', {width: 9.35, height: 19, type: 1}],
//             ['(', {width: 9.35, height: 19, type: 1}],
//             [')', {width: 9.35, height: 19, type: 1}],
//             ['[', {width: 9.35, height: 19, type: 1}],
//             [']', {width: 9.35, height: 19, type: 1}],
//             ['=', {width: 9.35, height: 19, type: 1}],
//             ['-', {width: 9.35, height: 19, type: 1}],
//             ['+', {width: 9.35, height: 19, type: 1}],
//             ['*', {width: 9.35, height: 19, type: 1}],
//             ['<', {width: 9.35, height: 19, type: 1}],
//             ['>', {width: 9.35, height: 19, type: 1}],
//             ['，', {width: 18.7, height: 19, type: 1}],
//             ['。', {width: 18.7, height: 19, type: 1}],
//             ['；', {width: 18.7, height: 19, type: 1}],
//             ['’', {width: 18.7, height: 19, type: 1}],
//             ['“', {width: 18.7, height: 19, type: 1}],
//             ['？', {width: 18.7, height: 19, type: 1}],
//             ['、', {width: 18.7, height: 19, type: 1}],
//             ['（', {width: 18.7, height: 19, type: 1}],
//             ['）', {width: 18.7, height: 19, type: 1}],
//             ['【', {width: 18.7, height: 19, type: 1}],
//             ['】', {width: 18.7, height: 19, type: 1}],
//             [paraEnd, {width: 0, height: 19, type: 1}],
//             ['：', {width: 18.7, height: 19, type: 1}], [':', {width: 9.35, height: 19, type: 1}],
//             ['{', {width: 9.35, height: 19, type: 1}],
//             ['}', {width: 9.35, height: 19, type: 1}],
//             ['☑', {width: 16.11, height: 19, type: 1}],
//             ['☐', {width: 16.11, height: 19, type: 1}],
//             ['⊙', {width: 18.7, height: 19, type: 1}],
//             ['⭘', {width: 18.7, height: 19, type: 1}],
//             ['◯', {width: 20.35, height: 19, type: 1}],
//             ['℃', {width: 18.7, height: 19, type: 1}],
//             ['Ⅰ', {width: 18.7, height: 19, type: 1}],
//             ['Ⅱ', {width: 18.7, height: 19, type: 1}],
//         ])
//     );
//     CACHES.set(
//         '16', new Map([
//             ['我', {width: 16, height: 16, type: 1}],
//             [' ', {width: 8, height: 16, type: 1}],
//             ['0', {width: 8, height: 16, type: 1}],
//             ['1', {width: 8, height: 16, type: 1}],
//             ['2', {width: 8, height: 16, type: 1}],
//             ['3', {width: 8, height: 16, type: 1}],
//             ['4', {width: 8, height: 16, type: 1}],
//             ['5', {width: 8, height: 16, type: 1}],
//             ['6', {width: 8, height: 16, type: 1}],
//             ['7', {width: 8, height: 16, type: 1}],
//             ['8', {width: 8, height: 16, type: 1}],
//             ['9', {width: 8, height: 16, type: 1}],
//             ['a', {width: 8, height: 16, type: 1}],
//             ['b', {width: 8, height: 16, type: 1}],
//             ['c', {width: 8, height: 16, type: 1}],
//             ['d', {width: 8, height: 16, type: 1}],
//             ['e', {width: 8, height: 16, type: 1}],
//             ['f', {width: 8, height: 16, type: 1}],
//             ['g', {width: 8, height: 16, type: 1}],
//             ['h', {width: 8, height: 16, type: 1}],
//             ['i', {width: 8, height: 16, type: 1}],
//             ['j', {width: 8, height: 16, type: 1}],
//             ['k', {width: 8, height: 16, type: 1}],
//             ['l', {width: 8, height: 16, type: 1}],
//             ['m', {width: 8, height: 16, type: 1}],
//             ['n', {width: 8, height: 16, type: 1}],
//             ['o', {width: 8, height: 16, type: 1}],
//             ['p', {width: 8, height: 16, type: 1}],
//             ['q', {width: 8, height: 16, type: 1}],
//             ['r', {width: 8, height: 16, type: 1}],
//             ['s', {width: 8, height: 16, type: 1}],
//             ['t', {width: 8, height: 16, type: 1}],
//             ['u', {width: 8, height: 16, type: 1}],
//             ['v', {width: 8, height: 16, type: 1}],
//             ['w', {width: 8, height: 16, type: 1}],
//             ['x', {width: 8, height: 16, type: 1}],
//             ['y', {width: 8, height: 16, type: 1}],
//             ['z', {width: 8, height: 16, type: 1}],
//             ['A', {width: 8, height: 16, type: 1}],
//             ['B', {width: 8, height: 16, type: 1}],
//             ['C', {width: 8, height: 16, type: 1}],
//             ['D', {width: 8, height: 16, type: 1}],
//             ['E', {width: 8, height: 16, type: 1}],
//             ['F', {width: 8, height: 16, type: 1}],
//             ['G', {width: 8, height: 16, type: 1}],
//             ['H', {width: 8, height: 16, type: 1}],
//             ['I', {width: 8, height: 16, type: 1}],
//             ['J', {width: 8, height: 16, type: 1}],
//             ['K', {width: 8, height: 16, type: 1}],
//             ['L', {width: 8, height: 16, type: 1}],
//             ['M', {width: 8, height: 16, type: 1}],
//             ['N', {width: 8, height: 16, type: 1}],
//             ['O', {width: 8, height: 16, type: 1}],
//             ['P', {width: 8, height: 16, type: 1}],
//             ['Q', {width: 8, height: 16, type: 1}],
//             ['R', {width: 8, height: 16, type: 1}],
//             ['S', {width: 8, height: 16, type: 1}],
//             ['T', {width: 8, height: 16, type: 1}],
//             ['U', {width: 8, height: 16, type: 1}],
//             ['V', {width: 8, height: 16, type: 1}],
//             ['W', {width: 8, height: 16, type: 1}],
//             ['X', {width: 8, height: 16, type: 1}],
//             ['Y', {width: 8, height: 16, type: 1}],
//             ['Z', {width: 8, height: 16, type: 1}],
//             [',', {width: 8, height: 16, type: 1}],
//             ['.', {width: 8, height: 16, type: 1}],
//             [';', {width: 8, height: 16, type: 1}],
//             ['/', {width: 8, height: 16, type: 1}],
//             ['?', {width: 8, height: 16, type: 1}],
//             ['(', {width: 8, height: 16, type: 1}],
//             [')', {width: 8, height: 16, type: 1}],
//             ['[', {width: 8, height: 16, type: 1}],
//             [']', {width: 8, height: 16, type: 1}],
//             ['=', {width: 8, height: 16, type: 1}],
//             ['-', {width: 8, height: 16, type: 1}],
//             ['+', {width: 8, height: 16, type: 1}],
//             ['*', {width: 8, height: 16, type: 1}],
//             ['<', {width: 8, height: 16, type: 1}],
//             ['>', {width: 8, height: 16, type: 1}],
//             ['，', {width: 16, height: 16, type: 1}],
//             ['。', {width: 16, height: 16, type: 1}],
//             ['；', {width: 16, height: 16, type: 1}],
//             ['’', {width: 16, height: 16, type: 1}],
//             ['“', {width: 16, height: 16, type: 1}],
//             ['？', {width: 16, height: 16, type: 1}],
//             ['、', {width: 16, height: 16, type: 1}],
//             ['（', {width: 16, height: 16, type: 1}],
//             ['）', {width: 16, height: 16, type: 1}],
//             ['【', {width: 16, height: 16, type: 1}],
//             ['】', {width: 16, height: 16, type: 1}],
//             [paraEnd, {width: 0, height: 16, type: 1}],
//             ['：', {width: 16, height: 16, type: 1}], [':', {width: 8, height: 16, type: 1}],
//             ['{', {width: 8, height: 16, type: 1}],
//             ['}', {width: 8, height: 16, type: 1}],
//             ['☑', {width: 13.78, height: 16, type: 1}],
//             ['☐', {width: 13.78, height: 16, type: 1}],
//             ['⊙', {width: 16, height: 16, type: 1}],
//             ['⭘', {width: 16, height: 16, type: 1}],
//             ['◯', {width: 17, height: 16, type: 1}],
//             ['℃', {width: 16, height: 16, type: 1}], ['Ⅰ', {width: 16, height: 16, type: 1}],
//             ['Ⅱ', {width: 16, height: 16, type: 1}],
//         ])
//     );
//     CACHES.set(
//         '14.7', new Map([
//             ['我', {width: 15, height: 15, type: 1}],
//             ['A', {width: 8, height: 15, type: 1}],
//             ['B', {width: 8, height: 15, type: 1}],
//             ['C', {width: 8, height: 15, type: 1}],
//             ['D', {width: 8, height: 15, type: 1}],
//             ['E', {width: 8, height: 15, type: 1}],
//             ['F', {width: 8, height: 15, type: 1}],
//             ['G', {width: 8, height: 15, type: 1}],
//             ['H', {width: 8, height: 15, type: 1}],
//             ['I', {width: 8, height: 15, type: 1}],
//             ['J', {width: 8, height: 15, type: 1}],
//             ['K', {width: 8, height: 15, type: 1}],
//             ['L', {width: 8, height: 15, type: 1}],
//             ['M', {width: 8, height: 15, type: 1}],
//             ['N', {width: 8, height: 15, type: 1}],
//             ['O', {width: 8, height: 15, type: 1}],
//             ['P', {width: 8, height: 15, type: 1}],
//             ['Q', {width: 8, height: 15, type: 1}],
//             ['R', {width: 8, height: 15, type: 1}],
//             ['S', {width: 8, height: 15, type: 1}],
//             ['T', {width: 8, height: 15, type: 1}],
//             ['U', {width: 8, height: 15, type: 1}],
//             ['V', {width: 8, height: 15, type: 1}],
//             ['W', {width: 8, height: 15, type: 1}],
//             ['X', {width: 8, height: 15, type: 1}],
//             ['Y', {width: 8, height: 15, type: 1}],
//             ['Z', {width: 8, height: 15, type: 1}],
//             [',', {width: 8, height: 15, type: 1}],
//             ['.', {width: 8, height: 15, type: 1}],
//             [';', {width: 8, height: 15, type: 1}],
//             ['/', {width: 8, height: 15, type: 1}],
//             ['?', {width: 8, height: 15, type: 1}],
//             ['(', {width: 8, height: 15, type: 1}],
//             [')', {width: 8, height: 15, type: 1}],
//             ['[', {width: 8, height: 15, type: 1}],
//             [']', {width: 8, height: 15, type: 1}],
//             ['=', {width: 8, height: 15, type: 1}],
//             ['-', {width: 8, height: 15, type: 1}],
//             ['+', {width: 8, height: 15, type: 1}],
//             ['*', {width: 8, height: 15, type: 1}],
//             ['<', {width: 8, height: 15, type: 1}],
//             ['>', {width: 8, height: 15, type: 1}],
//             ['，', {width: 15, height: 15, type: 1}],
//             ['。', {width: 15, height: 15, type: 1}],
//             ['；', {width: 15, height: 15, type: 1}],
//             ['’', {width: 15, height: 15, type: 1}],
//             ['“', {width: 15, height: 15, type: 1}],
//             ['？', {width: 15, height: 15, type: 1}],
//             ['、', {width: 15, height: 15, type: 1}],
//             ['（', {width: 15, height: 15, type: 1}],
//             ['）', {width: 15, height: 15, type: 1}],
//             ['【', {width: 15, height: 15, type: 1}],
//             ['】', {width: 15, height: 15, type: 1}], ['Ⅰ', {width: 15, height: 15, type: 1}],
//             ['Ⅱ', {width: 15, height: 15, type: 1}],
//             [':', {width: 8, height: 15, type: 1}],
//             ['：', {width: 15, height: 15, type: 1}],
//             ['{', {width: 8, height: 15, type: 1}],
//             ['}', {width: 8, height: 15, type: 1}],
//             ['☑', {width: 12.66, height: 15, type: 1}],
//             ['☐', {width: 12.66, height: 15, type: 1}],
//             ['⊙', {width: 15, height: 15, type: 1}],
//             ['⭘', {width: 14.7, height: 15, type: 1}],
//             ['◯', {width: 16, height: 15, type: 1}],
//             ['℃', {width: 15, height: 15, type: 1}],
//             [paraEnd, {width: 0, height: 15, type: 1}],
//         ])
//     );
//     CACHES.set(
//         '14', new Map([
//             ['我', {width: 14, height: 14, type: 1}],
//             [' ', {width: 7, height: 14, type: 1}],
//             ['0', {width: 7, height: 14, type: 1}],
//             ['1', {width: 7, height: 14, type: 1}],
//             ['2', {width: 7, height: 14, type: 1}],
//             ['3', {width: 7, height: 14, type: 1}],
//             ['4', {width: 7, height: 14, type: 1}],
//             ['5', {width: 7, height: 14, type: 1}],
//             ['6', {width: 7, height: 14, type: 1}],
//             ['7', {width: 7, height: 14, type: 1}],
//             ['8', {width: 7, height: 14, type: 1}],
//             ['9', {width: 7, height: 14, type: 1}],
//             ['a', {width: 7, height: 14, type: 1}],
//             ['b', {width: 7, height: 14, type: 1}],
//             ['c', {width: 7, height: 14, type: 1}],
//             ['d', {width: 7, height: 14, type: 1}],
//             ['e', {width: 7, height: 14, type: 1}],
//             ['f', {width: 7, height: 14, type: 1}],
//             ['g', {width: 7, height: 14, type: 1}],
//             ['h', {width: 7, height: 14, type: 1}],
//             ['i', {width: 7, height: 14, type: 1}],
//             ['j', {width: 7, height: 14, type: 1}],
//             ['k', {width: 7, height: 14, type: 1}],
//             ['l', {width: 7, height: 14, type: 1}],
//             ['m', {width: 7, height: 14, type: 1}],
//             ['n', {width: 7, height: 14, type: 1}],
//             ['o', {width: 7, height: 14, type: 1}],
//             ['p', {width: 7, height: 14, type: 1}],
//             ['q', {width: 7, height: 14, type: 1}],
//             ['r', {width: 7, height: 14, type: 1}],
//             ['s', {width: 7, height: 14, type: 1}],
//             ['t', {width: 7, height: 14, type: 1}],
//             ['u', {width: 7, height: 14, type: 1}],
//             ['v', {width: 7, height: 14, type: 1}],
//             ['w', {width: 7, height: 14, type: 1}],
//             ['x', {width: 7, height: 14, type: 1}],
//             ['y', {width: 7, height: 14, type: 1}],
//             ['z', {width: 7, height: 14, type: 1}],
//             ['A', {width: 7, height: 14, type: 1}],
//             ['B', {width: 7, height: 14, type: 1}],
//             ['C', {width: 7, height: 14, type: 1}],
//             ['D', {width: 7, height: 14, type: 1}],
//             ['E', {width: 7, height: 14, type: 1}],
//             ['F', {width: 7, height: 14, type: 1}],
//             ['G', {width: 7, height: 14, type: 1}],
//             ['H', {width: 7, height: 14, type: 1}],
//             ['I', {width: 7, height: 14, type: 1}],
//             ['J', {width: 7, height: 14, type: 1}],
//             ['K', {width: 7, height: 14, type: 1}],
//             ['L', {width: 7, height: 14, type: 1}],
//             ['M', {width: 7, height: 14, type: 1}],
//             ['N', {width: 7, height: 14, type: 1}],
//             ['O', {width: 7, height: 14, type: 1}],
//             ['P', {width: 7, height: 14, type: 1}],
//             ['Q', {width: 7, height: 14, type: 1}],
//             ['R', {width: 7, height: 14, type: 1}],
//             ['S', {width: 7, height: 14, type: 1}],
//             ['T', {width: 7, height: 14, type: 1}],
//             ['U', {width: 7, height: 14, type: 1}],
//             ['V', {width: 7, height: 14, type: 1}],
//             ['W', {width: 7, height: 14, type: 1}],
//             ['X', {width: 7, height: 14, type: 1}],
//             ['Y', {width: 7, height: 14, type: 1}],
//             ['Z', {width: 7, height: 14, type: 1}],
//             [',', {width: 7, height: 14, type: 1}],
//             ['.', {width: 7, height: 14, type: 1}],
//             [';', {width: 7, height: 14, type: 1}],
//             ['/', {width: 7, height: 14, type: 1}],
//             ['?', {width: 7, height: 14, type: 1}],
//             ['(', {width: 7, height: 14, type: 1}],
//             [')', {width: 7, height: 14, type: 1}],
//             ['[', {width: 7, height: 14, type: 1}],
//             [']', {width: 7, height: 14, type: 1}],
//             ['=', {width: 7, height: 14, type: 1}],
//             ['-', {width: 7, height: 14, type: 1}],
//             ['+', {width: 7, height: 14, type: 1}],
//             ['*', {width: 7, height: 14, type: 1}],
//             ['<', {width: 7, height: 14, type: 1}],
//             ['>', {width: 7, height: 14, type: 1}],
//             ['，', {width: 14, height: 14, type: 1}],
//             ['。', {width: 14, height: 14, type: 1}],
//             ['；', {width: 14, height: 14, type: 1}],
//             ['’', {width: 14, height: 14, type: 1}],
//             ['“', {width: 14, height: 14, type: 1}],
//             ['？', {width: 14, height: 14, type: 1}],
//             ['、', {width: 14, height: 14, type: 1}],
//             ['（', {width: 14, height: 14, type: 1}],
//             ['）', {width: 14, height: 14, type: 1}],
//             ['【', {width: 14, height: 14, type: 1}],
//             ['】', {width: 14, height: 14, type: 1}],
//             [paraEnd, {width: 0, height: 14, type: 1}],
//             ['：', {width: 14, height: 14, type: 1}], [':', {width: 7, height: 14, type: 1}],
//             ['{', {width: 7, height: 14, type: 1}],
//             ['}', {width: 7, height: 14, type: 1}],
//             ['☑', {width: 12.06, height: 14, type: 1}],
//             ['☐', {width: 12.06, height: 14, type: 1}],
//             ['⊙', {width: 14, height: 14, type: 1}],
//             ['⭘', {width: 14, height: 14, type: 1}],
//             ['◯', {width: 15.24, height: 14, type: 1}],
//             ['℃', {width: 14, height: 14, type: 1}], ['Ⅰ', {width: 14, height: 14, type: 1}],
//             ['Ⅱ', {width: 14, height: 14, type: 1}],
//         ])
//     );
//     CACHES.set(
//         '13.3', new Map([
//             ['我', {width: 13, height: 13, type: 1}],
//             [' ', {width: 7, height: 13, type: 1}],
//             ['0', {width: 7, height: 13, type: 1}],
//             ['1', {width: 7, height: 13, type: 1}],
//             ['2', {width: 7, height: 13, type: 1}],
//             ['3', {width: 7, height: 13, type: 1}],
//             ['4', {width: 7, height: 13, type: 1}],
//             ['5', {width: 7, height: 13, type: 1}],
//             ['6', {width: 7, height: 13, type: 1}],
//             ['7', {width: 7, height: 13, type: 1}],
//             ['8', {width: 7, height: 13, type: 1}],
//             ['9', {width: 7, height: 13, type: 1}],
//             ['a', {width: 7, height: 13, type: 1}],
//             ['b', {width: 7, height: 13, type: 1}],
//             ['c', {width: 7, height: 13, type: 1}],
//             ['d', {width: 7, height: 13, type: 1}],
//             ['e', {width: 7, height: 13, type: 1}],
//             ['f', {width: 7, height: 13, type: 1}],
//             ['g', {width: 7, height: 13, type: 1}],
//             ['h', {width: 7, height: 13, type: 1}],
//             ['i', {width: 7, height: 13, type: 1}],
//             ['j', {width: 7, height: 13, type: 1}],
//             ['k', {width: 7, height: 13, type: 1}],
//             ['l', {width: 7, height: 13, type: 1}],
//             ['m', {width: 7, height: 13, type: 1}],
//             ['n', {width: 7, height: 13, type: 1}],
//             ['o', {width: 7, height: 13, type: 1}],
//             ['p', {width: 7, height: 13, type: 1}],
//             ['q', {width: 7, height: 13, type: 1}],
//             ['r', {width: 7, height: 13, type: 1}],
//             ['s', {width: 7, height: 13, type: 1}],
//             ['t', {width: 7, height: 13, type: 1}],
//             ['u', {width: 7, height: 13, type: 1}],
//             ['v', {width: 7, height: 13, type: 1}],
//             ['w', {width: 7, height: 13, type: 1}],
//             ['x', {width: 7, height: 13, type: 1}],
//             ['y', {width: 7, height: 13, type: 1}],
//             ['z', {width: 7, height: 13, type: 1}],
//             ['A', {width: 7, height: 13, type: 1}],
//             ['B', {width: 7, height: 13, type: 1}],
//             ['C', {width: 7, height: 13, type: 1}],
//             ['D', {width: 7, height: 13, type: 1}],
//             ['E', {width: 7, height: 13, type: 1}],
//             ['F', {width: 7, height: 13, type: 1}],
//             ['G', {width: 7, height: 13, type: 1}],
//             ['H', {width: 7, height: 13, type: 1}],
//             ['I', {width: 7, height: 13, type: 1}],
//             ['J', {width: 7, height: 13, type: 1}],
//             ['K', {width: 7, height: 13, type: 1}],
//             ['L', {width: 7, height: 13, type: 1}],
//             ['M', {width: 7, height: 13, type: 1}],
//             ['N', {width: 7, height: 13, type: 1}],
//             ['O', {width: 7, height: 13, type: 1}],
//             ['P', {width: 7, height: 13, type: 1}],
//             ['Q', {width: 7, height: 13, type: 1}],
//             ['R', {width: 7, height: 13, type: 1}],
//             ['S', {width: 7, height: 13, type: 1}],
//             ['T', {width: 7, height: 13, type: 1}],
//             ['U', {width: 7, height: 13, type: 1}],
//             ['V', {width: 7, height: 13, type: 1}],
//             ['W', {width: 7, height: 13, type: 1}],
//             ['X', {width: 7, height: 13, type: 1}],
//             ['Y', {width: 7, height: 13, type: 1}],
//             ['Z', {width: 7, height: 13, type: 1}],
//             [',', {width: 7, height: 13, type: 1}],
//             ['.', {width: 7, height: 13, type: 1}],
//             [';', {width: 7, height: 13, type: 1}],
//             ['/', {width: 7, height: 13, type: 1}],
//             ['?', {width: 7, height: 13, type: 1}],
//             ['(', {width: 7, height: 13, type: 1}],
//             [')', {width: 7, height: 13, type: 1}],
//             ['[', {width: 7, height: 13, type: 1}],
//             [']', {width: 7, height: 13, type: 1}],
//             ['=', {width: 7, height: 13, type: 1}],
//             ['-', {width: 7, height: 13, type: 1}],
//             ['+', {width: 7, height: 13, type: 1}],
//             ['*', {width: 7, height: 13, type: 1}],
//             ['<', {width: 7, height: 13, type: 1}],
//             ['>', {width: 7, height: 13, type: 1}],
//             ['，', {width: 13, height: 13, type: 1}],
//             ['。', {width: 13, height: 13, type: 1}],
//             ['；', {width: 13, height: 13, type: 1}],
//             ['’', {width: 13, height: 13, type: 1}],
//             ['“', {width: 13, height: 13, type: 1}],
//             ['？', {width: 13, height: 13, type: 1}],
//             ['、', {width: 13, height: 13, type: 1}],
//             ['（', {width: 13, height: 13, type: 1}],
//             ['）', {width: 13, height: 13, type: 1}],
//             ['【', {width: 13, height: 13, type: 1}],
//             ['】', {width: 13, height: 13, type: 1}], ['Ⅰ', {width: 13, height: 13, type: 1}],
//             ['Ⅱ', {width: 13, height: 13, type: 1}],
//             [':', {width: 7, height: 13, type: 1}],
//             ['：', {width: 13, height: 13, type: 1}],
//             ['{', {width: 7, height: 13, type: 1}],
//             ['}', {width: 7, height: 13, type: 1}],
//             ['☑', {width: 11.46, height: 13, type: 1}],
//             ['☐', {width: 11.46, height: 13, type: 1}],
//             ['⊙', {width: 13, height: 13, type: 1}],
//             ['⭘', {width: 13.3, height: 13, type: 1}],
//             ['◯', {width: 14, height: 13, type: 1}],
//             ['℃', {width: 13, height: 13, type: 1}],
//             [paraEnd, {width: 0, height: 13, type: 1}],
//         ])
//     );
//     CACHES.set(
//         '12', new Map([
//             ['我', {width: 12, height: 12, type: 1}], [' ', {width: 6, height: 12, type: 1}],
//             ['0', {width: 6, height: 12, type: 1}], ['1', {width: 6, height: 12, type: 1}],
//             ['2', {width: 6, height: 12, type: 1}], ['3', {width: 6, height: 12, type: 1}],
//             ['4', {width: 6, height: 12, type: 1}], ['5', {width: 6, height: 12, type: 1}],
//             ['6', {width: 6, height: 12, type: 1}], ['7', {width: 6, height: 12, type: 1}],
//             ['8', {width: 6, height: 12, type: 1}], ['9', {width: 6, height: 12, type: 1}],
//             ['a', {width: 6, height: 12, type: 1}], ['b', {width: 6, height: 12, type: 1}],
//             ['c', {width: 6, height: 12, type: 1}], ['d', {width: 6, height: 12, type: 1}],
//             ['e', {width: 6, height: 12, type: 1}], ['f', {width: 6, height: 12, type: 1}],
//             ['g', {width: 6, height: 12, type: 1}], ['h', {width: 6, height: 12, type: 1}],
//             ['i', {width: 6, height: 12, type: 1}], ['j', {width: 6, height: 12, type: 1}],
//             ['k', {width: 6, height: 12, type: 1}], ['l', {width: 6, height: 12, type: 1}],
//             ['m', {width: 6, height: 12, type: 1}], ['n', {width: 6, height: 12, type: 1}],
//             ['o', {width: 6, height: 12, type: 1}], ['p', {width: 6, height: 12, type: 1}],
//             ['q', {width: 6, height: 12, type: 1}], ['r', {width: 6, height: 12, type: 1}],
//             ['s', {width: 6, height: 12, type: 1}], ['t', {width: 6, height: 12, type: 1}],
//             ['u', {width: 6, height: 12, type: 1}], ['v', {width: 6, height: 12, type: 1}],
//             ['w', {width: 6, height: 12, type: 1}], ['x', {width: 6, height: 12, type: 1}],
//             ['y', {width: 6, height: 12, type: 1}], ['z', {width: 6, height: 12, type: 1}],
//             ['A', {width: 6, height: 12, type: 1}], ['B', {width: 6, height: 12, type: 1}],
//             ['C', {width: 6, height: 12, type: 1}], ['D', {width: 6, height: 12, type: 1}],
//             ['E', {width: 6, height: 12, type: 1}], ['F', {width: 6, height: 12, type: 1}],
//             ['G', {width: 6, height: 12, type: 1}], ['H', {width: 6, height: 12, type: 1}],
//             ['I', {width: 6, height: 12, type: 1}], ['J', {width: 6, height: 12, type: 1}],
//             ['K', {width: 6, height: 12, type: 1}], ['L', {width: 6, height: 12, type: 1}],
//             ['M', {width: 6, height: 12, type: 1}], ['N', {width: 6, height: 12, type: 1}],
//             ['O', {width: 6, height: 12, type: 1}], ['P', {width: 6, height: 12, type: 1}],
//             ['Q', {width: 6, height: 12, type: 1}], ['R', {width: 6, height: 12, type: 1}],
//             ['S', {width: 6, height: 12, type: 1}], ['T', {width: 6, height: 12, type: 1}],
//             ['U', {width: 6, height: 12, type: 1}], ['V', {width: 6, height: 12, type: 1}],
//             ['W', {width: 6, height: 12, type: 1}], ['X', {width: 6, height: 12, type: 1}],
//             ['Y', {width: 6, height: 12, type: 1}], ['Z', {width: 6, height: 12, type: 1}],
//             [',', {width: 6, height: 12, type: 1}], ['.', {width: 6, height: 12, type: 1}],
//             [';', {width: 6, height: 12, type: 1}], ['/', {width: 6, height: 12, type: 1}],
//             ['?', {width: 6, height: 12, type: 1}], ['(', {width: 6, height: 12, type: 1}],
//             [')', {width: 6, height: 12, type: 1}], ['[', {width: 6, height: 12, type: 1}],
//             [']', {width: 6, height: 12, type: 1}], ['=', {width: 6, height: 12, type: 1}],
//             ['-', {width: 6, height: 12, type: 1}], ['+', {width: 6, height: 12, type: 1}],
//             ['*', {width: 6, height: 12, type: 1}], ['<', {width: 6, height: 12, type: 1}],
//             ['>', {width: 6, height: 12, type: 1}], ['，', {width: 12, height: 12, type: 1}],
//             ['。', {width: 12, height: 12, type: 1}], ['；', {width: 12, height: 12, type: 1}],
//             ['’', {width: 12, height: 12, type: 1}], ['“', {width: 12, height: 12, type: 1}],
//             ['？', {width: 12, height: 12, type: 1}], ['、', {width: 12, height: 12, type: 1}],
//             ['（', {width: 12, height: 12, type: 1}], ['）', {width: 12, height: 12, type: 1}],
//             ['【', {width: 12, height: 12, type: 1}], ['】', {width: 12, height: 12, type: 1}],
//             [paraEnd, {width: 0, height: 12, type: 1}],
//             ['：', {width: 12, height: 12, type: 1}], [':', {width: 6, height: 12, type: 1}],
//             ['{', {width: 6, height: 12, type: 1}],
//             ['}', {width: 6, height: 12, type: 1}],
//             ['☑', {width: 10.34, height: 12, type: 1}],
//             ['☐', {width: 10.34, height: 12, type: 1}],
//             ['⊙', {width: 12, height: 12, type: 1}],
//             ['⭘', {width: 12, height: 12, type: 1}],
//             ['◯', {width: 13, height: 12, type: 1}],
//             ['℃', {width: 12, height: 12, type: 1}], ['Ⅰ', {width: 12, height: 12, type: 1}],
//             ['Ⅱ', {width: 12, height: 12, type: 1}],
//         ])
//     );
//     CACHES.set(
//         '10.7', new Map([
//             ['我', {width: 10.7, height: 11, type: 1}],
//             [' ', {width: 5.35, height: 11, type: 1}],
//             ['0', {width: 5.35, height: 11, type: 1}],
//             ['1', {width: 5.35, height: 11, type: 1}],
//             ['2', {width: 5.35, height: 11, type: 1}],
//             ['3', {width: 5.35, height: 11, type: 1}],
//             ['4', {width: 5.35, height: 11, type: 1}],
//             ['5', {width: 5.35, height: 11, type: 1}],
//             ['6', {width: 5.35, height: 11, type: 1}],
//             ['7', {width: 5.35, height: 11, type: 1}],
//             ['8', {width: 5.35, height: 11, type: 1}],
//             ['9', {width: 5.35, height: 11, type: 1}],
//             ['a', {width: 5.35, height: 11, type: 1}],
//             ['b', {width: 5.35, height: 11, type: 1}],
//             ['c', {width: 5.35, height: 11, type: 1}],
//             ['d', {width: 5.35, height: 11, type: 1}],
//             ['e', {width: 5.35, height: 11, type: 1}],
//             ['f', {width: 5.35, height: 11, type: 1}],
//             ['g', {width: 5.35, height: 11, type: 1}],
//             ['h', {width: 5.35, height: 11, type: 1}],
//             ['i', {width: 5.35, height: 11, type: 1}],
//             ['j', {width: 5.35, height: 11, type: 1}],
//             ['k', {width: 5.35, height: 11, type: 1}],
//             ['l', {width: 5.35, height: 11, type: 1}],
//             ['m', {width: 5.35, height: 11, type: 1}],
//             ['n', {width: 5.35, height: 11, type: 1}],
//             ['o', {width: 5.35, height: 11, type: 1}],
//             ['p', {width: 5.35, height: 11, type: 1}],
//             ['q', {width: 5.35, height: 11, type: 1}],
//             ['r', {width: 5.35, height: 11, type: 1}],
//             ['s', {width: 5.35, height: 11, type: 1}],
//             ['t', {width: 5.35, height: 11, type: 1}],
//             ['u', {width: 5.35, height: 11, type: 1}],
//             ['v', {width: 5.35, height: 11, type: 1}],
//             ['w', {width: 5.35, height: 11, type: 1}],
//             ['x', {width: 5.35, height: 11, type: 1}],
//             ['y', {width: 5.35, height: 11, type: 1}],
//             ['z', {width: 5.35, height: 11, type: 1}],
//             ['A', {width: 5.35, height: 11, type: 1}],
//             ['B', {width: 5.35, height: 11, type: 1}],
//             ['C', {width: 5.35, height: 11, type: 1}],
//             ['D', {width: 5.35, height: 11, type: 1}],
//             ['E', {width: 5.35, height: 11, type: 1}],
//             ['F', {width: 5.35, height: 11, type: 1}],
//             ['G', {width: 5.35, height: 11, type: 1}],
//             ['H', {width: 5.35, height: 11, type: 1}],
//             ['I', {width: 5.35, height: 11, type: 1}],
//             ['J', {width: 5.35, height: 11, type: 1}],
//             ['K', {width: 5.35, height: 11, type: 1}],
//             ['L', {width: 5.35, height: 11, type: 1}],
//             ['M', {width: 5.35, height: 11, type: 1}],
//             ['N', {width: 5.35, height: 11, type: 1}],
//             ['O', {width: 5.35, height: 11, type: 1}],
//             ['P', {width: 5.35, height: 11, type: 1}],
//             ['Q', {width: 5.35, height: 11, type: 1}],
//             ['R', {width: 5.35, height: 11, type: 1}],
//             ['S', {width: 5.35, height: 11, type: 1}],
//             ['T', {width: 5.35, height: 11, type: 1}],
//             ['U', {width: 5.35, height: 11, type: 1}],
//             ['V', {width: 5.35, height: 11, type: 1}],
//             ['W', {width: 5.35, height: 11, type: 1}],
//             ['X', {width: 5.35, height: 11, type: 1}],
//             ['Y', {width: 5.35, height: 11, type: 1}],
//             ['Z', {width: 5.35, height: 11, type: 1}],
//             [',', {width: 5.35, height: 11, type: 1}],
//             ['.', {width: 5.35, height: 11, type: 1}],
//             [';', {width: 5.35, height: 11, type: 1}],
//             ['/', {width: 5.35, height: 11, type: 1}],
//             ['?', {width: 5.35, height: 11, type: 1}],
//             ['(', {width: 5.35, height: 11, type: 1}],
//             [')', {width: 5.35, height: 11, type: 1}],
//             ['[', {width: 5.35, height: 11, type: 1}],
//             [']', {width: 5.35, height: 11, type: 1}],
//             ['=', {width: 5.35, height: 11, type: 1}],
//             ['-', {width: 5.35, height: 11, type: 1}],
//             ['+', {width: 5.35, height: 11, type: 1}],
//             ['*', {width: 5.35, height: 11, type: 1}],
//             ['<', {width: 5.35, height: 11, type: 1}],
//             ['>', {width: 5.35, height: 11, type: 1}],
//             ['，', {width: 10.7, height: 11, type: 1}],
//             ['。', {width: 10.7, height: 11, type: 1}],
//             ['；', {width: 10.7, height: 11, type: 1}],
//             ['’', {width: 10.7, height: 11, type: 1}],
//             ['“', {width: 10.7, height: 11, type: 1}],
//             ['？', {width: 10.7, height: 11, type: 1}],
//             ['、', {width: 10.7, height: 11, type: 1}],
//             ['（', {width: 10.7, height: 11, type: 1}],
//             ['）', {width: 10.7, height: 11, type: 1}],
//             ['【', {width: 10.7, height: 11, type: 1}],
//             ['】', {width: 10.7, height: 11, type: 1}], ['Ⅰ', {width: 10.7, height: 11, type: 1}],
//             ['Ⅱ', {width: 10.7, height: 11, type: 1}],
//             [':', {width: 5.35, height: 11, type: 1}],
//             ['：', {width: 10.7, height: 11, type: 1}],
//             ['{', {width: 5.35, height: 11, type: 1}],
//             ['}', {width: 5.35, height: 11, type: 1}],
//             ['☑', {width: 9.22, height: 11, type: 1}],
//             ['☐', {width: 9.22, height: 11, type: 1}],
//             ['⊙', {width: 10.7, height: 11, type: 1}],
//             ['⭘', {width: 10.7, height: 11, type: 1}],
//             ['◯', {width: 11.65, height: 11, type: 1}],
//             ['℃', {width: 10.7, height: 11, type: 1}],
//             [paraEnd, {width: 0, height: 11, type: 1}],
//         ])
//     );
//     CACHES.set(
//         'Times New Roman58.7', new Map([
//             ['我', {width: 58.7, height: 65, type: 1}],
//             [' ', {width: 14.67, height: 65, type: 1}],
//             ['0', {width: 29.35, height: 65, type: 1}],
//             ['1', {width: 29.35, height: 65, type: 1}],
//             ['2', {width: 29.35, height: 65, type: 1}],
//             ['3', {width: 29.35, height: 65, type: 1}],
//             ['4', {width: 29.35, height: 65, type: 1}],
//             ['5', {width: 29.35, height: 65, type: 1}],
//             ['6', {width: 29.35, height: 65, type: 1}],
//             ['7', {width: 29.35, height: 65, type: 1}],
//             ['8', {width: 29.35, height: 65, type: 1}],
//             ['9', {width: 29.35, height: 65, type: 1}],
//             ['a', {width: 26.05, height: 65, type: 1}],
//             ['b', {width: 29.35, height: 65, type: 1}],
//             ['c', {width: 26.05, height: 65, type: 1}],
//             ['d', {width: 29.35, height: 65, type: 1}],
//             ['e', {width: 26.05, height: 65, type: 1}],
//             ['f', {width: 19.55, height: 65, type: 1}],
//             ['g', {width: 29.35, height: 65, type: 1}],
//             ['h', {width: 29.35, height: 65, type: 1}],
//             ['i', {width: 16.31, height: 65, type: 1}],
//             ['j', {width: 16.31, height: 65, type: 1}],
//             ['k', {width: 29.35, height: 65, type: 1}],
//             ['l', {width: 16.31, height: 65, type: 1}],
//             ['m', {width: 45.66, height: 65, type: 1}],
//             ['n', {width: 29.35, height: 65, type: 1}],
//             ['o', {width: 29.35, height: 65, type: 1}],
//             ['p', {width: 29.35, height: 65, type: 1}],
//             ['q', {width: 29.35, height: 65, type: 1}],
//             ['r', {width: 19.55, height: 65, type: 1}],
//             ['s', {width: 22.84, height: 65, type: 1}],
//             ['t', {width: 16.31, height: 65, type: 1}],
//             ['u', {width: 29.35, height: 65, type: 1}],
//             ['v', {width: 29.35, height: 65, type: 1}],
//             ['w', {width: 42.39, height: 65, type: 1}],
//             ['x', {width: 29.35, height: 65, type: 1}],
//             ['y', {width: 29.35, height: 65, type: 1}],
//             ['z', {width: 26.05, height: 65, type: 1}],
//             ['A', {width: 42.39, height: 65, type: 1}],
//             ['B', {width: 39.15, height: 65, type: 1}],
//             ['C', {width: 39.15, height: 65, type: 1}],
//             ['D', {width: 42.39, height: 65, type: 1}],
//             ['E', {width: 35.86, height: 65, type: 1}],
//             ['F', {width: 32.65, height: 65, type: 1}],
//             ['G', {width: 42.39, height: 65, type: 1}],
//             ['H', {width: 42.39, height: 65, type: 1}],
//             ['I', {width: 19.55, height: 65, type: 1}],
//             ['J', {width: 22.84, height: 65, type: 1}],
//             ['K', {width: 42.39, height: 65, type: 1}],
//             ['L', {width: 35.86, height: 65, type: 1}],
//             ['M', {width: 52.19, height: 65, type: 1}],
//             ['N', {width: 42.39, height: 65, type: 1}],
//             ['O', {width: 42.39, height: 65, type: 1}],
//             ['P', {width: 32.65, height: 65, type: 1}],
//             ['Q', {width: 42.39, height: 65, type: 1}],
//             ['R', {width: 39.15, height: 65, type: 1}],
//             ['S', {width: 32.65, height: 65, type: 1}],
//             ['T', {width: 35.86, height: 65, type: 1}],
//             ['U', {width: 42.39, height: 65, type: 1}],
//             ['V', {width: 42.39, height: 65, type: 1}],
//             ['W', {width: 55.4, height: 65, type: 1}],
//             ['X', {width: 42.39, height: 65, type: 1}],
//             ['Y', {width: 42.39, height: 65, type: 1}],
//             ['Z', {width: 35.86, height: 65, type: 1}],
//             [',', {width: 14.68, height: 65, type: 1}],
//             ['.', {width: 14.68, height: 65, type: 1}],
//             [';', {width: 16.31, height: 65, type: 1}],
//             ['/', {width: 16.31, height: 65, type: 1}],
//             ['?', {width: 26.05, height: 65, type: 1}],
//             ['(', {width: 19.55, height: 65, type: 1}],
//             [')', {width: 19.55, height: 65, type: 1}],
//             ['[', {width: 19.55, height: 65, type: 1}],
//             [']', {width: 19.55, height: 65, type: 1}],
//             ['=', {width: 33.1, height: 65, type: 1}],
//             ['-', {width: 19.55, height: 65, type: 1}],
//             ['+', {width: 33.1, height: 65, type: 1}],
//             ['*', {width: 29.35, height: 65, type: 1}],
//             ['<', {width: 33.1, height: 65, type: 1}],
//             ['>', {width: 33.1, height: 65, type: 1}],
//             ['，', {width: 58.7, height: 65, type: 1}],
//             ['。', {width: 58.7, height: 65, type: 1}],
//             ['；', {width: 58.7, height: 65, type: 1}],
//             ['’', {width: 19.55, height: 65, type: 1}],
//             ['“', {width: 26.05, height: 65, type: 1}],
//             ['？', {width: 58.7, height: 65, type: 1}],
//             ['、', {width: 58.7, height: 65, type: 1}],
//             ['（', {width: 58.7, height: 65, type: 1}],
//             ['）', {width: 58.7, height: 65, type: 1}],
//             ['【', {width: 58.7, height: 65, type: 1}],
//             ['】', {width: 58.7, height: 65, type: 1}],
//             ['Ⅰ', {width: 19.55, height: 65, type: 1}],
//             ['Ⅱ', {width: 37.95, height: 65, type: 1}],
//             [':', {width: 16.31, height: 65, type: 1}],
//             ['：', {width: 58.7, height: 65, type: 1}],
//             [paraEnd, {width: 0, height: 65, type: 1}], ['{', {width: 28.17, height: 65, type: 1}],
//             ['}', {width: 28.17, height: 65, type: 1}],
//             ['☑', {width: 50.56, height: 65, type: 1}],
//             ['☐', {width: 50.56, height: 65, type: 1}],
//             ['⊙', {width: 58.7, height: 65, type: 1}],
//             ['⭘', {width: 58.7, height: 65, type: 1}],
//             ['◯', {width: 63.89, height: 65, type: 1}],
//             ['℃', {width: 60.1, height: 65, type: 1}],
//         ])
//     );
//     CACHES.set(
//         'Times New Roman48', new Map([
//             ['我', {width: 48, height: 53, type: 1}],
//             [' ', {width: 12, height: 53, type: 1}],
//             ['0', {width: 24, height: 53, type: 1}],
//             ['1', {width: 24, height: 53, type: 1}],
//             ['2', {width: 24, height: 53, type: 1}],
//             ['3', {width: 24, height: 53, type: 1}],
//             ['4', {width: 24, height: 53, type: 1}],
//             ['5', {width: 24, height: 53, type: 1}],
//             ['6', {width: 24, height: 53, type: 1}],
//             ['7', {width: 24, height: 53, type: 1}],
//             ['8', {width: 24, height: 53, type: 1}],
//             ['9', {width: 24, height: 53, type: 1}],
//             ['a', {width: 21.3, height: 53, type: 1}],
//             ['b', {width: 24, height: 53, type: 1}],
//             ['c', {width: 21.3, height: 53, type: 1}],
//             ['d', {width: 24, height: 53, type: 1}],
//             ['e', {width: 21.3, height: 53, type: 1}],
//             ['f', {width: 15.98, height: 53, type: 1}],
//             ['g', {width: 24, height: 53, type: 1}],
//             ['h', {width: 24, height: 53, type: 1}],
//             ['i', {width: 13.34, height: 53, type: 1}],
//             ['j', {width: 13.34, height: 53, type: 1}],
//             ['k', {width: 24, height: 53, type: 1}],
//             ['l', {width: 13.34, height: 53, type: 1}],
//             ['m', {width: 37.34, height: 53, type: 1}],
//             ['n', {width: 24, height: 53, type: 1}],
//             ['o', {width: 24, height: 53, type: 1}],
//             ['p', {width: 24, height: 53, type: 1}],
//             ['q', {width: 24, height: 53, type: 1}],
//             ['r', {width: 15.98, height: 53, type: 1}],
//             ['s', {width: 18.68, height: 53, type: 1}],
//             ['t', {width: 13.34, height: 53, type: 1}],
//             ['u', {width: 24, height: 53, type: 1}],
//             ['v', {width: 24, height: 53, type: 1}],
//             ['w', {width: 34.66, height: 53, type: 1}],
//             ['x', {width: 24, height: 53, type: 1}],
//             ['y', {width: 24, height: 53, type: 1}],
//             ['z', {width: 21.3, height: 53, type: 1}],
//             ['A', {width: 34.66, height: 53, type: 1}],
//             ['B', {width: 32.02, height: 53, type: 1}],
//             ['C', {width: 32.02, height: 53, type: 1}],
//             ['D', {width: 34.66, height: 53, type: 1}],
//             ['E', {width: 29.32, height: 53, type: 1}],
//             ['F', {width: 26.7, height: 53, type: 1}],
//             ['G', {width: 34.66, height: 53, type: 1}],
//             ['H', {width: 34.66, height: 53, type: 1}],
//             ['I', {width: 15.98, height: 53, type: 1}],
//             ['J', {width: 18.68, height: 53, type: 1}],
//             ['K', {width: 34.66, height: 53, type: 1}],
//             ['L', {width: 29.32, height: 53, type: 1}],
//             ['M', {width: 42.68, height: 53, type: 1}],
//             ['N', {width: 34.66, height: 53, type: 1}],
//             ['O', {width: 34.66, height: 53, type: 1}],
//             ['P', {width: 26.7, height: 53, type: 1}],
//             ['Q', {width: 34.66, height: 53, type: 1}],
//             ['R', {width: 32.02, height: 53, type: 1}],
//             ['S', {width: 26.7, height: 53, type: 1}],
//             ['T', {width: 29.32, height: 53, type: 1}],
//             ['U', {width: 34.66, height: 53, type: 1}],
//             ['V', {width: 34.66, height: 53, type: 1}],
//             ['W', {width: 45.3, height: 53, type: 1}],
//             ['X', {width: 34.66, height: 53, type: 1}],
//             ['Y', {width: 34.66, height: 53, type: 1}],
//             ['Z', {width: 29.32, height: 53, type: 1}],
//             [',', {width: 12, height: 53, type: 1}],
//             ['.', {width: 12, height: 53, type: 1}],
//             [';', {width: 13.34, height: 53, type: 1}],
//             ['/', {width: 13.34, height: 53, type: 1}],
//             ['?', {width: 21.3, height: 53, type: 1}],
//             ['(', {width: 15.98, height: 53, type: 1}],
//             [')', {width: 15.98, height: 53, type: 1}],
//             ['[', {width: 15.98, height: 53, type: 1}],
//             [']', {width: 15.98, height: 53, type: 1}],
//             ['=', {width: 27.07, height: 53, type: 1}],
//             ['-', {width: 15.98, height: 53, type: 1}],
//             ['+', {width: 27.07, height: 53, type: 1}],
//             ['*', {width: 24, height: 53, type: 1}],
//             ['<', {width: 27.07, height: 53, type: 1}],
//             ['>', {width: 27.07, height: 53, type: 1}],
//             ['，', {width: 48, height: 53, type: 1}],
//             ['。', {width: 48, height: 53, type: 1}],
//             ['；', {width: 48, height: 53, type: 1}],
//             ['’', {width: 15.98, height: 53, type: 1}],
//             ['“', {width: 21.3, height: 53, type: 1}],
//             ['？', {width: 48, height: 53, type: 1}],
//             ['、', {width: 48, height: 53, type: 1}],
//             ['（', {width: 48, height: 53, type: 1}],
//             ['）', {width: 48, height: 53, type: 1}],
//             ['【', {width: 48, height: 53, type: 1}],
//             ['】', {width: 48, height: 53, type: 1}],
//             ['Ⅰ', {width: 15.98, height: 53, type: 1}],
//             ['Ⅱ', {width: 31.03, height: 53, type: 1}],
//             [':', {width: 13.34, height: 53, type: 1}],
//             ['：', {width: 48, height: 53, type: 1}],
//             [paraEnd, {width: 0, height: 53, type: 1}],
//             ['{', {width: 23.04, height: 53, type: 1}],
//             ['}', {width: 23.04, height: 53, type: 1}],
//             ['☑', {width: 41.34, height: 53, type: 1}],
//             ['☐', {width: 41.34, height: 53, type: 1}],
//             ['⊙', {width: 48, height: 53, type: 1}],
//             ['⭘', {width: 48, height: 53, type: 1}],
//             ['◯', {width: 52.24, height: 53, type: 1}],
//             ['℃', {width: 49.15, height: 53, type: 1}],
//         ])
//     );
//     CACHES.set(
//         'Times New Roman37.3', new Map([
//             ['我', {width: 37.3, height: 41, type: 1}],
//             [' ', {width: 9.32, height: 41, type: 1}],
//             ['0', {width: 18.65, height: 41, type: 1}],
//             ['1', {width: 18.65, height: 41, type: 1}],
//             ['2', {width: 18.65, height: 41, type: 1}],
//             ['3', {width: 18.65, height: 41, type: 1}],
//             ['4', {width: 18.65, height: 41, type: 1}],
//             ['5', {width: 18.65, height: 41, type: 1}],
//             ['6', {width: 18.65, height: 41, type: 1}],
//             ['7', {width: 18.65, height: 41, type: 1}],
//             ['8', {width: 18.65, height: 41, type: 1}],
//             ['9', {width: 18.65, height: 41, type: 1}],
//             ['a', {width: 16.56, height: 41, type: 1}],
//             ['b', {width: 18.65, height: 41, type: 1}],
//             ['c', {width: 16.56, height: 41, type: 1}],
//             ['d', {width: 18.65, height: 41, type: 1}],
//             ['e', {width: 16.56, height: 41, type: 1}],
//             ['f', {width: 12.42, height: 41, type: 1}],
//             ['g', {width: 18.65, height: 41, type: 1}],
//             ['h', {width: 18.65, height: 41, type: 1}],
//             ['i', {width: 10.36, height: 41, type: 1}],
//             ['j', {width: 10.36, height: 41, type: 1}],
//             ['k', {width: 18.65, height: 41, type: 1}],
//             ['l', {width: 10.36, height: 41, type: 1}],
//             ['m', {width: 29.01, height: 41, type: 1}],
//             ['n', {width: 18.65, height: 41, type: 1}],
//             ['o', {width: 18.65, height: 41, type: 1}],
//             ['p', {width: 18.65, height: 41, type: 1}],
//             ['q', {width: 18.65, height: 41, type: 1}],
//             ['r', {width: 12.42, height: 41, type: 1}],
//             ['s', {width: 14.52, height: 41, type: 1}],
//             ['t', {width: 10.36, height: 41, type: 1}],
//             ['u', {width: 18.65, height: 41, type: 1}],
//             ['v', {width: 18.65, height: 41, type: 1}],
//             ['w', {width: 26.94, height: 41, type: 1}],
//             ['x', {width: 18.65, height: 41, type: 1}],
//             ['y', {width: 18.65, height: 41, type: 1}],
//             ['z', {width: 16.56, height: 41, type: 1}],
//             ['A', {width: 26.94, height: 41, type: 1}],
//             ['B', {width: 24.88, height: 41, type: 1}],
//             ['C', {width: 24.88, height: 41, type: 1}],
//             ['D', {width: 26.94, height: 41, type: 1}],
//             ['E', {width: 22.78, height: 41, type: 1}],
//             ['F', {width: 20.74, height: 41, type: 1}],
//             ['G', {width: 26.94, height: 41, type: 1}],
//             ['H', {width: 26.94, height: 41, type: 1}],
//             ['I', {width: 12.42, height: 41, type: 1}],
//             ['J', {width: 14.52, height: 41, type: 1}],
//             ['K', {width: 26.94, height: 41, type: 1}],
//             ['L', {width: 22.78, height: 41, type: 1}],
//             ['M', {width: 33.17, height: 41, type: 1}],
//             ['N', {width: 26.94, height: 41, type: 1}],
//             ['O', {width: 26.94, height: 41, type: 1}],
//             ['P', {width: 20.74, height: 41, type: 1}],
//             ['Q', {width: 26.94, height: 41, type: 1}],
//             ['R', {width: 24.88, height: 41, type: 1}],
//             ['S', {width: 20.74, height: 41, type: 1}],
//             ['T', {width: 22.78, height: 41, type: 1}],
//             ['U', {width: 26.94, height: 41, type: 1}],
//             ['V', {width: 26.94, height: 41, type: 1}],
//             ['W', {width: 35.21, height: 41, type: 1}],
//             ['X', {width: 26.94, height: 41, type: 1}],
//             ['Y', {width: 26.94, height: 41, type: 1}],
//             ['Z', {width: 22.78, height: 41, type: 1}],
//             [',', {width: 9.32, height: 41, type: 1}],
//             ['.', {width: 9.32, height: 41, type: 1}],
//             [';', {width: 10.36, height: 41, type: 1}],
//             ['/', {width: 10.36, height: 41, type: 1}],
//             ['?', {width: 16.56, height: 41, type: 1}],
//             ['(', {width: 12.42, height: 41, type: 1}],
//             [')', {width: 12.42, height: 41, type: 1}],
//             ['[', {width: 12.42, height: 41, type: 1}],
//             [']', {width: 12.42, height: 41, type: 1}],
//             ['=', {width: 21.04, height: 41, type: 1}],
//             ['-', {width: 12.42, height: 41, type: 1}],
//             ['+', {width: 21.04, height: 41, type: 1}],
//             ['*', {width: 18.65, height: 41, type: 1}],
//             ['<', {width: 21.04, height: 41, type: 1}],
//             ['>', {width: 21.04, height: 41, type: 1}],
//             ['，', {width: 37.3, height: 41, type: 1}],
//             ['。', {width: 37.3, height: 41, type: 1}],
//             ['；', {width: 37.3, height: 41, type: 1}],
//             ['’', {width: 12.42, height: 41, type: 1}],
//             ['“', {width: 16.56, height: 41, type: 1}],
//             ['？', {width: 37.3, height: 41, type: 1}],
//             ['、', {width: 37.3, height: 41, type: 1}],
//             ['（', {width: 37.3, height: 41, type: 1}],
//             ['）', {width: 37.3, height: 41, type: 1}],
//             ['【', {width: 37.3, height: 41, type: 1}],
//             ['】', {width: 37.3, height: 41, type: 1}],
//             ['Ⅰ', {width: 12.42, height: 41, type: 1}],
//             ['Ⅱ', {width: 24.11, height: 41, type: 1}],
//             [':', {width: 10.36, height: 41, type: 1}],
//             ['：', {width: 37.3, height: 41, type: 1}],
//             [paraEnd, {width: 0, height: 41, type: 1}],
//             ['{', {width: 17.9, height: 41, type: 1}],
//             ['}', {width: 17.9, height: 41, type: 1}],
//             ['☑', {width: 32.13, height: 41, type: 1}],
//             ['☐', {width: 32.13, height: 41, type: 1}],
//             ['⊙', {width: 37.3, height: 41, type: 1}],
//             ['⭘', {width: 37.3, height: 41, type: 1}],
//             ['◯', {width: 40.6, height: 41, type: 1}],
//             ['℃', {width: 38.19, height: 41, type: 1}],
//         ])
//     );
//     CACHES.set(
//         'Times New Roman34.7', new Map([
//             ['我', {width: 34.7, height: 39, type: 1}],
//             [' ', {width: 8.67, height: 39, type: 1}],
//             ['0', {width: 17.35, height: 39, type: 1}],
//             ['1', {width: 17.35, height: 39, type: 1}],
//             ['2', {width: 17.35, height: 39, type: 1}],
//             ['3', {width: 17.35, height: 39, type: 1}],
//             ['4', {width: 17.35, height: 39, type: 1}],
//             ['5', {width: 17.35, height: 39, type: 1}],
//             ['6', {width: 17.35, height: 39, type: 1}],
//             ['7', {width: 17.35, height: 39, type: 1}],
//             ['8', {width: 17.35, height: 39, type: 1}],
//             ['9', {width: 17.35, height: 39, type: 1}],
//             ['a', {width: 15.4, height: 39, type: 1}],
//             ['b', {width: 17.35, height: 39, type: 1}],
//             ['c', {width: 15.4, height: 39, type: 1}],
//             ['d', {width: 17.35, height: 39, type: 1}],
//             ['e', {width: 15.4, height: 39, type: 1}],
//             ['f', {width: 11.56, height: 39, type: 1}],
//             ['g', {width: 17.35, height: 39, type: 1}],
//             ['h', {width: 17.35, height: 39, type: 1}],
//             ['i', {width: 9.64, height: 39, type: 1}],
//             ['j', {width: 9.64, height: 39, type: 1}],
//             ['k', {width: 17.35, height: 39, type: 1}],
//             ['l', {width: 9.64, height: 39, type: 1}],
//             ['m', {width: 26.99, height: 39, type: 1}],
//             ['n', {width: 17.35, height: 39, type: 1}],
//             ['o', {width: 17.35, height: 39, type: 1}],
//             ['p', {width: 17.35, height: 39, type: 1}],
//             ['q', {width: 17.35, height: 39, type: 1}],
//             ['r', {width: 11.56, height: 39, type: 1}],
//             ['s', {width: 13.5, height: 39, type: 1}],
//             ['t', {width: 9.64, height: 39, type: 1}],
//             ['u', {width: 17.35, height: 39, type: 1}],
//             ['v', {width: 17.35, height: 39, type: 1}],
//             ['w', {width: 25.06, height: 39, type: 1}],
//             ['x', {width: 17.35, height: 39, type: 1}],
//             ['y', {width: 17.35, height: 39, type: 1}],
//             ['z', {width: 15.4, height: 39, type: 1}],
//             ['A', {width: 25.06, height: 39, type: 1}],
//             ['B', {width: 23.14, height: 39, type: 1}],
//             ['C', {width: 23.14, height: 39, type: 1}],
//             ['D', {width: 25.06, height: 39, type: 1}],
//             ['E', {width: 21.2, height: 39, type: 1}],
//             ['F', {width: 19.3, height: 39, type: 1}],
//             ['G', {width: 25.06, height: 39, type: 1}],
//             ['H', {width: 25.06, height: 39, type: 1}],
//             ['I', {width: 11.56, height: 39, type: 1}],
//             ['J', {width: 13.5, height: 39, type: 1}],
//             ['K', {width: 25.06, height: 39, type: 1}],
//             ['L', {width: 21.2, height: 39, type: 1}],
//             ['M', {width: 30.85, height: 39, type: 1}],
//             ['N', {width: 25.06, height: 39, type: 1}],
//             ['O', {width: 25.06, height: 39, type: 1}],
//             ['P', {width: 19.3, height: 39, type: 1}],
//             ['Q', {width: 25.06, height: 39, type: 1}],
//             ['R', {width: 23.14, height: 39, type: 1}],
//             ['S', {width: 19.3, height: 39, type: 1}],
//             ['T', {width: 21.2, height: 39, type: 1}],
//             ['U', {width: 25.06, height: 39, type: 1}],
//             ['V', {width: 25.06, height: 39, type: 1}],
//             ['W', {width: 32.75, height: 39, type: 1}],
//             ['X', {width: 25.06, height: 39, type: 1}],
//             ['Y', {width: 25.06, height: 39, type: 1}],
//             ['Z', {width: 21.2, height: 39, type: 1}],
//             [',', {width: 8.68, height: 39, type: 1}],
//             ['.', {width: 8.68, height: 39, type: 1}],
//             [';', {width: 9.64, height: 39, type: 1}],
//             ['/', {width: 9.64, height: 39, type: 1}],
//             ['?', {width: 15.4, height: 39, type: 1}],
//             ['(', {width: 11.56, height: 39, type: 1}],
//             [')', {width: 11.56, height: 39, type: 1}],
//             ['[', {width: 11.56, height: 39, type: 1}],
//             [']', {width: 11.56, height: 39, type: 1}],
//             ['=', {width: 19.57, height: 39, type: 1}],
//             ['-', {width: 11.56, height: 39, type: 1}],
//             ['+', {width: 19.57, height: 39, type: 1}],
//             ['*', {width: 17.35, height: 39, type: 1}],
//             ['<', {width: 19.57, height: 39, type: 1}],
//             ['>', {width: 19.57, height: 39, type: 1}],
//             ['，', {width: 34.7, height: 39, type: 1}],
//             ['。', {width: 34.7, height: 39, type: 1}],
//             ['；', {width: 34.7, height: 39, type: 1}],
//             ['’', {width: 11.56, height: 39, type: 1}],
//             ['“', {width: 15.4, height: 39, type: 1}],
//             ['？', {width: 34.7, height: 39, type: 1}],
//             ['、', {width: 34.7, height: 39, type: 1}],
//             ['（', {width: 34.7, height: 39, type: 1}],
//             ['）', {width: 34.7, height: 39, type: 1}],
//             ['【', {width: 34.7, height: 39, type: 1}],
//             ['】', {width: 34.7, height: 39, type: 1}],
//             ['Ⅰ', {width: 11.56, height: 39, type: 1}],
//             ['Ⅱ', {width: 22.43, height: 39, type: 1}],
//             [':', {width: 9.64, height: 39, type: 1}],
//             ['：', {width: 34.7, height: 39, type: 1}],
//             [paraEnd, {width: 0, height: 39, type: 1}],
//             ['{', {width: 16.66, height: 39, type: 1}],
//             ['}', {width: 16.66, height: 39, type: 1}],
//             ['☑', {width: 29.89, height: 39, type: 1}],
//             ['☐', {width: 29.89, height: 39, type: 1}],
//             ['⊙', {width: 34.7, height: 39, type: 1}],
//             ['⭘', {width: 34.7, height: 39, type: 1}],
//             ['◯', {width: 37.77, height: 39, type: 1}],
//             ['℃', {width: 35.53, height: 39, type: 1}],
//         ])
//     );
//     CACHES.set(
//         'Times New Roman32', new Map([
//             ['我', {width: 32, height: 36, type: 1}],
//             [' ', {width: 8, height: 36, type: 1}],
//             ['0', {width: 16, height: 36, type: 1}],
//             ['1', {width: 16, height: 36, type: 1}],
//             ['2', {width: 16, height: 36, type: 1}],
//             ['3', {width: 16, height: 36, type: 1}],
//             ['4', {width: 16, height: 36, type: 1}],
//             ['5', {width: 16, height: 36, type: 1}],
//             ['6', {width: 16, height: 36, type: 1}],
//             ['7', {width: 16, height: 36, type: 1}],
//             ['8', {width: 16, height: 36, type: 1}],
//             ['9', {width: 16, height: 36, type: 1}],
//             ['a', {width: 14.2, height: 36, type: 1}],
//             ['b', {width: 16, height: 36, type: 1}],
//             ['c', {width: 14.2, height: 36, type: 1}],
//             ['d', {width: 16, height: 36, type: 1}],
//             ['e', {width: 14.2, height: 36, type: 1}],
//             ['f', {width: 10.66, height: 36, type: 1}],
//             ['g', {width: 16, height: 36, type: 1}],
//             ['h', {width: 16, height: 36, type: 1}],
//             ['i', {width: 8.89, height: 36, type: 1}],
//             ['j', {width: 8.89, height: 36, type: 1}],
//             ['k', {width: 16, height: 36, type: 1}],
//             ['l', {width: 8.89, height: 36, type: 1}],
//             ['m', {width: 24.89, height: 36, type: 1}],
//             ['n', {width: 16, height: 36, type: 1}],
//             ['o', {width: 16, height: 36, type: 1}],
//             ['p', {width: 16, height: 36, type: 1}],
//             ['q', {width: 16, height: 36, type: 1}],
//             ['r', {width: 10.66, height: 36, type: 1}],
//             ['s', {width: 12.45, height: 36, type: 1}],
//             ['t', {width: 8.89, height: 36, type: 1}],
//             ['u', {width: 16, height: 36, type: 1}],
//             ['v', {width: 16, height: 36, type: 1}],
//             ['w', {width: 23.11, height: 36, type: 1}],
//             ['x', {width: 16, height: 36, type: 1}],
//             ['y', {width: 16, height: 36, type: 1}],
//             ['z', {width: 14.2, height: 36, type: 1}],
//             ['A', {width: 23.11, height: 36, type: 1}],
//             ['B', {width: 21.34, height: 36, type: 1}],
//             ['C', {width: 21.34, height: 36, type: 1}],
//             ['D', {width: 23.11, height: 36, type: 1}],
//             ['E', {width: 19.55, height: 36, type: 1}],
//             ['F', {width: 17.8, height: 36, type: 1}],
//             ['G', {width: 23.11, height: 36, type: 1}],
//             ['H', {width: 23.11, height: 36, type: 1}],
//             ['I', {width: 10.66, height: 36, type: 1}],
//             ['J', {width: 12.45, height: 36, type: 1}],
//             ['K', {width: 23.11, height: 36, type: 1}],
//             ['L', {width: 19.55, height: 36, type: 1}],
//             ['M', {width: 28.45, height: 36, type: 1}],
//             ['N', {width: 23.11, height: 36, type: 1}],
//             ['O', {width: 23.11, height: 36, type: 1}],
//             ['P', {width: 17.8, height: 36, type: 1}],
//             ['Q', {width: 23.11, height: 36, type: 1}],
//             ['R', {width: 21.34, height: 36, type: 1}],
//             ['S', {width: 17.8, height: 36, type: 1}],
//             ['T', {width: 19.55, height: 36, type: 1}],
//             ['U', {width: 23.11, height: 36, type: 1}],
//             ['V', {width: 23.11, height: 36, type: 1}],
//             ['W', {width: 30.2, height: 36, type: 1}],
//             ['X', {width: 23.11, height: 36, type: 1}],
//             ['Y', {width: 23.11, height: 36, type: 1}],
//             ['Z', {width: 19.55, height: 36, type: 1}],
//             [',', {width: 8, height: 36, type: 1}],
//             ['.', {width: 8, height: 36, type: 1}],
//             [';', {width: 8.89, height: 36, type: 1}],
//             ['/', {width: 8.89, height: 36, type: 1}],
//             ['?', {width: 14.2, height: 36, type: 1}],
//             ['(', {width: 10.66, height: 36, type: 1}],
//             [')', {width: 10.66, height: 36, type: 1}],
//             ['[', {width: 10.66, height: 36, type: 1}],
//             [']', {width: 10.66, height: 36, type: 1}],
//             ['=', {width: 18.05, height: 36, type: 1}],
//             ['-', {width: 10.66, height: 36, type: 1}],
//             ['+', {width: 18.05, height: 36, type: 1}],
//             ['*', {width: 16, height: 36, type: 1}],
//             ['<', {width: 18.05, height: 36, type: 1}],
//             ['>', {width: 18.05, height: 36, type: 1}],
//             ['，', {width: 32, height: 36, type: 1}],
//             ['。', {width: 32, height: 36, type: 1}],
//             ['；', {width: 32, height: 36, type: 1}],
//             ['’', {width: 10.66, height: 36, type: 1}],
//             ['“', {width: 14.2, height: 36, type: 1}],
//             ['？', {width: 32, height: 36, type: 1}],
//             ['、', {width: 32, height: 36, type: 1}],
//             ['（', {width: 32, height: 36, type: 1}],
//             ['）', {width: 32, height: 36, type: 1}],
//             ['【', {width: 32, height: 36, type: 1}],
//             ['】', {width: 32, height: 36, type: 1}],
//             [paraEnd, {width: 0, height: 36, type: 1}],
//             ['{', {width: 15.36, height: 36, type: 1}],
//             ['}', {width: 15.36, height: 36, type: 1}],
//             ['☑', {width: 27.56, height: 36, type: 1}],
//             ['☐', {width: 27.56, height: 36, type: 1}],
//             ['⊙', {width: 32, height: 36, type: 1}],
//             ['⭘', {width: 32, height: 36, type: 1}],
//             ['◯', {width: 34.83, height: 36, type: 1}],
//             ['：', {width: 32, height: 36, type: 1}],
//             [':', {width: 8.89, height: 36, type: 1}],
//             ['℃', {width: 32.77, height: 36, type: 1}], ['Ⅰ', {width: 10.66, height: 36, type: 1}],
//             ['Ⅱ', {width: 20.69, height: 36, type: 1}],
//         ])
//     );
//     CACHES.set(
//         'Times New Roman29.3', new Map([
//             ['我', {width: 29.3, height: 32, type: 1}],
//             [' ', {width: 7.32, height: 32, type: 1}],
//             ['0', {width: 14.65, height: 32, type: 1}],
//             ['1', {width: 14.65, height: 32, type: 1}],
//             ['2', {width: 14.65, height: 32, type: 1}],
//             ['3', {width: 14.65, height: 32, type: 1}],
//             ['4', {width: 14.65, height: 32, type: 1}],
//             ['5', {width: 14.65, height: 32, type: 1}],
//             ['6', {width: 14.65, height: 32, type: 1}],
//             ['7', {width: 14.65, height: 32, type: 1}],
//             ['8', {width: 14.65, height: 32, type: 1}],
//             ['9', {width: 14.65, height: 32, type: 1}],
//             ['a', {width: 13, height: 32, type: 1}],
//             ['b', {width: 14.65, height: 32, type: 1}],
//             ['c', {width: 13, height: 32, type: 1}],
//             ['d', {width: 14.65, height: 32, type: 1}],
//             ['e', {width: 13, height: 32, type: 1}],
//             ['f', {width: 9.76, height: 32, type: 1}],
//             ['g', {width: 14.65, height: 32, type: 1}],
//             ['h', {width: 14.65, height: 32, type: 1}],
//             ['i', {width: 8.14, height: 32, type: 1}],
//             ['j', {width: 8.14, height: 32, type: 1}],
//             ['k', {width: 14.65, height: 32, type: 1}],
//             ['l', {width: 8.14, height: 32, type: 1}],
//             ['m', {width: 22.79, height: 32, type: 1}],
//             ['n', {width: 14.65, height: 32, type: 1}],
//             ['o', {width: 14.65, height: 32, type: 1}],
//             ['p', {width: 14.65, height: 32, type: 1}],
//             ['q', {width: 14.65, height: 32, type: 1}],
//             ['r', {width: 9.76, height: 32, type: 1}],
//             ['s', {width: 11.4, height: 32, type: 1}],
//             ['t', {width: 8.14, height: 32, type: 1}],
//             ['u', {width: 14.65, height: 32, type: 1}],
//             ['v', {width: 14.65, height: 32, type: 1}],
//             ['w', {width: 21.16, height: 32, type: 1}],
//             ['x', {width: 14.65, height: 32, type: 1}],
//             ['y', {width: 14.65, height: 32, type: 1}],
//             ['z', {width: 13, height: 32, type: 1}],
//             ['A', {width: 21.16, height: 32, type: 1}],
//             ['B', {width: 19.54, height: 32, type: 1}],
//             ['C', {width: 19.54, height: 32, type: 1}],
//             ['D', {width: 21.16, height: 32, type: 1}],
//             ['E', {width: 17.9, height: 32, type: 1}],
//             ['F', {width: 16.3, height: 32, type: 1}],
//             ['G', {width: 21.16, height: 32, type: 1}],
//             ['H', {width: 21.16, height: 32, type: 1}],
//             ['I', {width: 9.76, height: 32, type: 1}],
//             ['J', {width: 11.4, height: 32, type: 1}],
//             ['K', {width: 21.16, height: 32, type: 1}],
//             ['L', {width: 17.9, height: 32, type: 1}],
//             ['M', {width: 26.05, height: 32, type: 1}],
//             ['N', {width: 21.16, height: 32, type: 1}],
//             ['O', {width: 21.16, height: 32, type: 1}],
//             ['P', {width: 16.3, height: 32, type: 1}],
//             ['Q', {width: 21.16, height: 32, type: 1}],
//             ['R', {width: 19.54, height: 32, type: 1}],
//             ['S', {width: 16.3, height: 32, type: 1}],
//             ['T', {width: 17.9, height: 32, type: 1}],
//             ['U', {width: 21.16, height: 32, type: 1}],
//             ['V', {width: 21.16, height: 32, type: 1}],
//             ['W', {width: 27.65, height: 32, type: 1}],
//             ['X', {width: 21.16, height: 32, type: 1}],
//             ['Y', {width: 21.16, height: 32, type: 1}],
//             ['Z', {width: 17.9, height: 32, type: 1}],
//             [',', {width: 7.32, height: 32, type: 1}],
//             ['.', {width: 7.32, height: 32, type: 1}],
//             [';', {width: 8.14, height: 32, type: 1}],
//             ['/', {width: 8.14, height: 32, type: 1}],
//             ['?', {width: 13, height: 32, type: 1}],
//             ['(', {width: 9.76, height: 32, type: 1}],
//             [')', {width: 9.76, height: 32, type: 1}],
//             ['[', {width: 9.76, height: 32, type: 1}],
//             [']', {width: 9.76, height: 32, type: 1}],
//             ['=', {width: 16.52, height: 32, type: 1}],
//             ['-', {width: 9.76, height: 32, type: 1}],
//             ['+', {width: 16.52, height: 32, type: 1}],
//             ['*', {width: 14.65, height: 32, type: 1}],
//             ['<', {width: 16.52, height: 32, type: 1}],
//             ['>', {width: 16.52, height: 32, type: 1}],
//             ['，', {width: 29.3, height: 32, type: 1}],
//             ['。', {width: 29.3, height: 32, type: 1}],
//             ['；', {width: 29.3, height: 32, type: 1}],
//             ['’', {width: 9.76, height: 32, type: 1}],
//             ['“', {width: 13, height: 32, type: 1}],
//             ['？', {width: 29.3, height: 32, type: 1}],
//             ['、', {width: 29.3, height: 32, type: 1}],
//             ['（', {width: 29.3, height: 32, type: 1}],
//             ['）', {width: 29.3, height: 32, type: 1}],
//             ['【', {width: 29.3, height: 32, type: 1}],
//             ['】', {width: 29.3, height: 32, type: 1}],
//             [paraEnd, {width: 0, height: 32, type: 1}],
//             ['{', {width: 14.06, height: 32, type: 1}],
//             ['}', {width: 14.06, height: 32, type: 1}],
//             ['☑', {width: 25.24, height: 32, type: 1}],
//             ['☐', {width: 25.24, height: 32, type: 1}],
//             ['⊙', {width: 29.3, height: 32, type: 1}],
//             ['⭘', {width: 29.3, height: 32, type: 1}],
//             ['◯', {width: 31.89, height: 32, type: 1}],
//             ['：', {width: 29.3, height: 32, type: 1}],
//             [':', {width: 8.14, height: 32, type: 1}],
//             ['℃', {width: 30, height: 32, type: 1}],
//             ['Ⅰ', {width: 9.76, height: 32, type: 1}],
//             ['Ⅱ', {width: 18.94, height: 32, type: 1}],
//         ])
//     );
//     CACHES.set(
//         'Times New Roman26.7', new Map([
//             ['我', {width: 26.7, height: 30, type: 1}],
//             [' ', {width: 6.67, height: 30, type: 1}],
//             ['0', {width: 13.35, height: 30, type: 1}],
//             ['1', {width: 13.35, height: 30, type: 1}],
//             ['2', {width: 13.35, height: 30, type: 1}],
//             ['3', {width: 13.35, height: 30, type: 1}],
//             ['4', {width: 13.35, height: 30, type: 1}],
//             ['5', {width: 13.35, height: 30, type: 1}],
//             ['6', {width: 13.35, height: 30, type: 1}],
//             ['7', {width: 13.35, height: 30, type: 1}],
//             ['8', {width: 13.35, height: 30, type: 1}],
//             ['9', {width: 13.35, height: 30, type: 1}],
//             ['a', {width: 11.85, height: 30, type: 1}],
//             ['b', {width: 13.35, height: 30, type: 1}],
//             ['c', {width: 11.85, height: 30, type: 1}],
//             ['d', {width: 13.35, height: 30, type: 1}],
//             ['e', {width: 11.85, height: 30, type: 1}],
//             ['f', {width: 8.89, height: 30, type: 1}],
//             ['g', {width: 13.35, height: 30, type: 1}],
//             ['h', {width: 13.35, height: 30, type: 1}],
//             ['i', {width: 7.42, height: 30, type: 1}],
//             ['j', {width: 7.42, height: 30, type: 1}],
//             ['k', {width: 13.35, height: 30, type: 1}],
//             ['l', {width: 7.42, height: 30, type: 1}],
//             ['m', {width: 20.77, height: 30, type: 1}],
//             ['n', {width: 13.35, height: 30, type: 1}],
//             ['o', {width: 13.35, height: 30, type: 1}],
//             ['p', {width: 13.35, height: 30, type: 1}],
//             ['q', {width: 13.35, height: 30, type: 1}],
//             ['r', {width: 8.89, height: 30, type: 1}],
//             ['s', {width: 10.39, height: 30, type: 1}],
//             ['t', {width: 7.42, height: 30, type: 1}],
//             ['u', {width: 13.35, height: 30, type: 1}],
//             ['v', {width: 13.35, height: 30, type: 1}],
//             ['w', {width: 19.28, height: 30, type: 1}],
//             ['x', {width: 13.35, height: 30, type: 1}],
//             ['y', {width: 13.35, height: 30, type: 1}],
//             ['z', {width: 11.85, height: 30, type: 1}],
//             ['A', {width: 19.28, height: 30, type: 1}],
//             ['B', {width: 17.81, height: 30, type: 1}],
//             ['C', {width: 17.81, height: 30, type: 1}],
//             ['D', {width: 19.28, height: 30, type: 1}],
//             ['E', {width: 16.31, height: 30, type: 1}],
//             ['F', {width: 14.85, height: 30, type: 1}],
//             ['G', {width: 19.28, height: 30, type: 1}],
//             ['H', {width: 19.28, height: 30, type: 1}],
//             ['I', {width: 8.89, height: 30, type: 1}],
//             ['J', {width: 10.39, height: 30, type: 1}],
//             ['K', {width: 19.28, height: 30, type: 1}],
//             ['L', {width: 16.31, height: 30, type: 1}],
//             ['M', {width: 23.74, height: 30, type: 1}],
//             ['N', {width: 19.28, height: 30, type: 1}],
//             ['O', {width: 19.28, height: 30, type: 1}],
//             ['P', {width: 14.85, height: 30, type: 1}],
//             ['Q', {width: 19.28, height: 30, type: 1}],
//             ['R', {width: 17.81, height: 30, type: 1}],
//             ['S', {width: 14.85, height: 30, type: 1}],
//             ['T', {width: 16.31, height: 30, type: 1}],
//             ['U', {width: 19.28, height: 30, type: 1}],
//             ['V', {width: 19.28, height: 30, type: 1}],
//             ['W', {width: 25.2, height: 30, type: 1}],
//             ['X', {width: 19.28, height: 30, type: 1}],
//             ['Y', {width: 19.28, height: 30, type: 1}],
//             ['Z', {width: 16.31, height: 30, type: 1}],
//             [',', {width: 6.67, height: 30, type: 1}],
//             ['.', {width: 6.67, height: 30, type: 1}],
//             [';', {width: 7.42, height: 30, type: 1}],
//             ['/', {width: 7.42, height: 30, type: 1}],
//             ['?', {width: 11.85, height: 30, type: 1}],
//             ['(', {width: 8.89, height: 30, type: 1}],
//             [')', {width: 8.89, height: 30, type: 1}],
//             ['[', {width: 8.89, height: 30, type: 1}],
//             [']', {width: 8.89, height: 30, type: 1}],
//             ['=', {width: 15.06, height: 30, type: 1}],
//             ['-', {width: 8.89, height: 30, type: 1}],
//             ['+', {width: 15.06, height: 30, type: 1}],
//             ['*', {width: 13.35, height: 30, type: 1}],
//             ['<', {width: 15.06, height: 30, type: 1}],
//             ['>', {width: 15.06, height: 30, type: 1}],
//             ['，', {width: 26.7, height: 30, type: 1}],
//             ['。', {width: 26.7, height: 30, type: 1}],
//             ['；', {width: 26.7, height: 30, type: 1}],
//             ['’', {width: 8.89, height: 30, type: 1}],
//             ['“', {width: 11.85, height: 30, type: 1}],
//             ['？', {width: 26.7, height: 30, type: 1}],
//             ['、', {width: 26.7, height: 30, type: 1}],
//             ['（', {width: 26.7, height: 30, type: 1}],
//             ['）', {width: 26.7, height: 30, type: 1}],
//             ['【', {width: 26.7, height: 30, type: 1}],
//             ['】', {width: 26.7, height: 30, type: 1}],
//             ['Ⅰ', {width: 8.89, height: 30, type: 1}],
//             ['Ⅱ', {width: 17.26, height: 30, type: 1}],
//             [':', {width: 7.42, height: 30, type: 1}],
//             ['：', {width: 26.7, height: 30, type: 1}],
//             [paraEnd, {width: 0, height: 30, type: 1}], ['{', {width: 12.82, height: 30, type: 1}],
//             ['}', {width: 12.82, height: 30, type: 1}],
//             ['☑', {width: 23, height: 30, type: 1}],
//             ['☐', {width: 23, height: 30, type: 1}],
//             ['⊙', {width: 26.7, height: 30, type: 1}],
//             ['⭘', {width: 26.7, height: 30, type: 1}],
//             ['◯', {width: 29.06, height: 30, type: 1}],
//             ['℃', {width: 27.34, height: 30, type: 1}],
//         ])
//     );
//     CACHES.set(
//         'Times New Roman24', new Map([
//             ['我', {width: 24, height: 26, type: 1}],
//             [' ', {width: 6, height: 26, type: 1}],
//             ['0', {width: 12, height: 26, type: 1}],
//             ['1', {width: 12, height: 26, type: 1}],
//             ['2', {width: 12, height: 26, type: 1}],
//             ['3', {width: 12, height: 26, type: 1}],
//             ['4', {width: 12, height: 26, type: 1}],
//             ['5', {width: 12, height: 26, type: 1}],
//             ['6', {width: 12, height: 26, type: 1}],
//             ['7', {width: 12, height: 26, type: 1}],
//             ['8', {width: 12, height: 26, type: 1}],
//             ['9', {width: 12, height: 26, type: 1}],
//             ['a', {width: 10.65, height: 26, type: 1}],
//             ['b', {width: 12, height: 26, type: 1}],
//             ['c', {width: 10.65, height: 26, type: 1}],
//             ['d', {width: 12, height: 26, type: 1}],
//             ['e', {width: 10.65, height: 26, type: 1}],
//             ['f', {width: 7.99, height: 26, type: 1}],
//             ['g', {width: 12, height: 26, type: 1}],
//             ['h', {width: 12, height: 26, type: 1}],
//             ['i', {width: 6.67, height: 26, type: 1}],
//             ['j', {width: 6.67, height: 26, type: 1}],
//             ['k', {width: 12, height: 26, type: 1}],
//             ['l', {width: 6.67, height: 26, type: 1}],
//             ['m', {width: 18.67, height: 26, type: 1}],
//             ['n', {width: 12, height: 26, type: 1}],
//             ['o', {width: 12, height: 26, type: 1}],
//             ['p', {width: 12, height: 26, type: 1}],
//             ['q', {width: 12, height: 26, type: 1}],
//             ['r', {width: 7.99, height: 26, type: 1}],
//             ['s', {width: 9.34, height: 26, type: 1}],
//             ['t', {width: 6.67, height: 26, type: 1}],
//             ['u', {width: 12, height: 26, type: 1}],
//             ['v', {width: 12, height: 26, type: 1}],
//             ['w', {width: 17.33, height: 26, type: 1}],
//             ['x', {width: 12, height: 26, type: 1}],
//             ['y', {width: 12, height: 26, type: 1}],
//             ['z', {width: 10.65, height: 26, type: 1}],
//             ['A', {width: 17.33, height: 26, type: 1}],
//             ['B', {width: 16.01, height: 26, type: 1}],
//             ['C', {width: 16.01, height: 26, type: 1}],
//             ['D', {width: 17.33, height: 26, type: 1}],
//             ['E', {width: 14.66, height: 26, type: 1}],
//             ['F', {width: 13.35, height: 26, type: 1}],
//             ['G', {width: 17.33, height: 26, type: 1}],
//             ['H', {width: 17.33, height: 26, type: 1}],
//             ['I', {width: 7.99, height: 26, type: 1}],
//             ['J', {width: 9.34, height: 26, type: 1}],
//             ['K', {width: 17.33, height: 26, type: 1}],
//             ['L', {width: 14.66, height: 26, type: 1}],
//             ['M', {width: 21.34, height: 26, type: 1}],
//             ['N', {width: 17.33, height: 26, type: 1}],
//             ['O', {width: 17.33, height: 26, type: 1}],
//             ['P', {width: 13.35, height: 26, type: 1}],
//             ['Q', {width: 17.33, height: 26, type: 1}],
//             ['R', {width: 16.01, height: 26, type: 1}],
//             ['S', {width: 13.35, height: 26, type: 1}],
//             ['T', {width: 14.66, height: 26, type: 1}],
//             ['U', {width: 17.33, height: 26, type: 1}],
//             ['V', {width: 17.33, height: 26, type: 1}],
//             ['W', {width: 22.65, height: 26, type: 1}],
//             ['X', {width: 17.33, height: 26, type: 1}],
//             ['Y', {width: 17.33, height: 26, type: 1}],
//             ['Z', {width: 14.66, height: 26, type: 1}],
//             [',', {width: 6, height: 26, type: 1}],
//             ['.', {width: 6, height: 26, type: 1}],
//             [';', {width: 6.67, height: 26, type: 1}],
//             ['/', {width: 6.67, height: 26, type: 1}],
//             ['?', {width: 10.65, height: 26, type: 1}],
//             ['(', {width: 7.99, height: 26, type: 1}],
//             [')', {width: 7.99, height: 26, type: 1}],
//             ['[', {width: 7.99, height: 26, type: 1}],
//             [']', {width: 7.99, height: 26, type: 1}],
//             ['=', {width: 13.54, height: 26, type: 1}],
//             ['-', {width: 7.99, height: 26, type: 1}],
//             ['+', {width: 13.54, height: 26, type: 1}],
//             ['*', {width: 12, height: 26, type: 1}],
//             ['<', {width: 13.54, height: 26, type: 1}],
//             ['>', {width: 13.54, height: 26, type: 1}],
//             ['，', {width: 24, height: 26, type: 1}],
//             ['。', {width: 24, height: 26, type: 1}],
//             ['；', {width: 24, height: 26, type: 1}],
//             ['’', {width: 7.99, height: 26, type: 1}],
//             ['“', {width: 10.65, height: 26, type: 1}],
//             ['？', {width: 24, height: 26, type: 1}],
//             ['、', {width: 24, height: 26, type: 1}],
//             ['（', {width: 24, height: 26, type: 1}],
//             ['）', {width: 24, height: 26, type: 1}],
//             ['【', {width: 24, height: 26, type: 1}],
//             ['】', {width: 24, height: 26, type: 1}],
//             [paraEnd, {width: 0, height: 26, type: 1}],
//             ['{', {width: 11.52, height: 26, type: 1}],
//             ['}', {width: 11.52, height: 26, type: 1}],
//             ['☑', {width: 20.67, height: 26, type: 1}],
//             ['☐', {width: 20.67, height: 26, type: 1}],
//             ['⊙', {width: 24, height: 26, type: 1}],
//             ['⭘', {width: 24, height: 26, type: 1}],
//             ['◯', {width: 26.12, height: 26, type: 1}],
//             ['：', {width: 24, height: 26, type: 1}],
//             [':', {width: 6.67, height: 26, type: 1}],
//             ['℃', {width: 24.57, height: 26, type: 1}], ['Ⅰ', {width: 7.99, height: 26, type: 1}],
//             ['Ⅱ', {width: 15.52, height: 26, type: 1}],
//         ])
//     );
//     CACHES.set(
//         'Times New Roman21.3', new Map([
//             ['我', {width: 21.3, height: 24, type: 1}],
//             [' ', {width: 5.32, height: 24, type: 1}],
//             ['0', {width: 10.65, height: 24, type: 1}],
//             ['1', {width: 10.65, height: 24, type: 1}],
//             ['2', {width: 10.65, height: 24, type: 1}],
//             ['3', {width: 10.65, height: 24, type: 1}],
//             ['4', {width: 10.65, height: 24, type: 1}],
//             ['5', {width: 10.65, height: 24, type: 1}],
//             ['6', {width: 10.65, height: 24, type: 1}],
//             ['7', {width: 10.65, height: 24, type: 1}],
//             ['8', {width: 10.65, height: 24, type: 1}],
//             ['9', {width: 10.65, height: 24, type: 1}],
//             ['a', {width: 9.45, height: 24, type: 1}],
//             ['b', {width: 10.65, height: 24, type: 1}],
//             ['c', {width: 9.45, height: 24, type: 1}],
//             ['d', {width: 10.65, height: 24, type: 1}],
//             ['e', {width: 9.45, height: 24, type: 1}],
//             ['f', {width: 7.09, height: 24, type: 1}],
//             ['g', {width: 10.65, height: 24, type: 1}],
//             ['h', {width: 10.65, height: 24, type: 1}],
//             ['i', {width: 5.92, height: 24, type: 1}],
//             ['j', {width: 5.92, height: 24, type: 1}],
//             ['k', {width: 10.65, height: 24, type: 1}],
//             ['l', {width: 5.92, height: 24, type: 1}],
//             ['m', {width: 16.57, height: 24, type: 1}],
//             ['n', {width: 10.65, height: 24, type: 1}],
//             ['o', {width: 10.65, height: 24, type: 1}],
//             ['p', {width: 10.65, height: 24, type: 1}],
//             ['q', {width: 10.65, height: 24, type: 1}],
//             ['r', {width: 7.09, height: 24, type: 1}],
//             ['s', {width: 8.29, height: 24, type: 1}],
//             ['t', {width: 5.92, height: 24, type: 1}],
//             ['u', {width: 10.65, height: 24, type: 1}],
//             ['v', {width: 10.65, height: 24, type: 1}],
//             ['w', {width: 15.38, height: 24, type: 1}],
//             ['x', {width: 10.65, height: 24, type: 1}],
//             ['y', {width: 10.65, height: 24, type: 1}],
//             ['z', {width: 9.45, height: 24, type: 1}],
//             ['A', {width: 15.38, height: 24, type: 1}],
//             ['B', {width: 14.21, height: 24, type: 1}],
//             ['C', {width: 14.21, height: 24, type: 1}],
//             ['D', {width: 15.38, height: 24, type: 1}],
//             ['E', {width: 13.01, height: 24, type: 1}],
//             ['F', {width: 11.85, height: 24, type: 1}],
//             ['G', {width: 15.38, height: 24, type: 1}],
//             ['H', {width: 15.38, height: 24, type: 1}],
//             ['I', {width: 7.09, height: 24, type: 1}],
//             ['J', {width: 8.29, height: 24, type: 1}],
//             ['K', {width: 15.38, height: 24, type: 1}],
//             ['L', {width: 13.01, height: 24, type: 1}],
//             ['M', {width: 18.94, height: 24, type: 1}],
//             ['N', {width: 15.38, height: 24, type: 1}],
//             ['O', {width: 15.38, height: 24, type: 1}],
//             ['P', {width: 11.85, height: 24, type: 1}],
//             ['Q', {width: 15.38, height: 24, type: 1}],
//             ['R', {width: 14.21, height: 24, type: 1}],
//             ['S', {width: 11.85, height: 24, type: 1}],
//             ['T', {width: 13.01, height: 24, type: 1}],
//             ['U', {width: 15.38, height: 24, type: 1}],
//             ['V', {width: 15.38, height: 24, type: 1}],
//             ['W', {width: 20.1, height: 24, type: 1}],
//             ['X', {width: 15.38, height: 24, type: 1}],
//             ['Y', {width: 15.38, height: 24, type: 1}],
//             ['Z', {width: 13.01, height: 24, type: 1}],
//             [',', {width: 5.33, height: 24, type: 1}],
//             ['.', {width: 5.33, height: 24, type: 1}],
//             [';', {width: 5.92, height: 24, type: 1}],
//             ['/', {width: 5.92, height: 24, type: 1}],
//             ['?', {width: 9.45, height: 24, type: 1}],
//             ['(', {width: 7.09, height: 24, type: 1}],
//             [')', {width: 7.09, height: 24, type: 1}],
//             ['[', {width: 7.09, height: 24, type: 1}],
//             [']', {width: 7.09, height: 24, type: 1}],
//             ['=', {width: 12.01, height: 24, type: 1}],
//             ['-', {width: 7.09, height: 24, type: 1}],
//             ['+', {width: 12.01, height: 24, type: 1}],
//             ['*', {width: 10.65, height: 24, type: 1}],
//             ['<', {width: 12.01, height: 24, type: 1}],
//             ['>', {width: 12.01, height: 24, type: 1}],
//             ['，', {width: 21.3, height: 24, type: 1}],
//             ['。', {width: 21.3, height: 24, type: 1}],
//             ['；', {width: 21.3, height: 24, type: 1}],
//             ['’', {width: 7.09, height: 24, type: 1}],
//             ['“', {width: 9.45, height: 24, type: 1}],
//             ['？', {width: 21.3, height: 24, type: 1}],
//             ['、', {width: 21.3, height: 24, type: 1}],
//             ['（', {width: 21.3, height: 24, type: 1}],
//             ['）', {width: 21.3, height: 24, type: 1}],
//             ['【', {width: 21.3, height: 24, type: 1}],
//             ['】', {width: 21.3, height: 24, type: 1}],
//             [paraEnd, {width: 0, height: 24, type: 1}],
//             ['{', {width: 10.22, height: 24, type: 1}],
//             ['}', {width: 10.22, height: 24, type: 1}],
//             ['☑', {width: 18.35, height: 24, type: 1}],
//             ['☐', {width: 18.35, height: 24, type: 1}],
//             ['⊙', {width: 21.3, height: 24, type: 1}],
//             ['⭘', {width: 21.3, height: 24, type: 1}],
//             ['◯', {width: 23.18, height: 24, type: 1}],
//             ['：', {width: 21.3, height: 24, type: 1}],
//             [':', {width: 5.92, height: 24, type: 1}],
//             ['℃', {width: 21.81, height: 24, type: 1}],
//             ['Ⅰ', {width: 7.09, height: 24, type: 1}],
//             ['Ⅱ', {width: 13.77, height: 24, type: 1}],
//         ])
//     );
//     CACHES.set(
//         'Times New Roman20', new Map([
//             ['我', {width: 20, height: 22, type: 1}],
//             [' ', {width: 5, height: 22, type: 1}],
//             ['0', {width: 10, height: 22, type: 1}],
//             ['1', {width: 10, height: 22, type: 1}],
//             ['2', {width: 10, height: 22, type: 1}],
//             ['3', {width: 10, height: 22, type: 1}],
//             ['4', {width: 10, height: 22, type: 1}],
//             ['5', {width: 10, height: 22, type: 1}],
//             ['6', {width: 10, height: 22, type: 1}],
//             ['7', {width: 10, height: 22, type: 1}],
//             ['8', {width: 10, height: 22, type: 1}],
//             ['9', {width: 10, height: 22, type: 1}],
//             ['a', {width: 8.88, height: 22, type: 1}],
//             ['b', {width: 10, height: 22, type: 1}],
//             ['c', {width: 8.88, height: 22, type: 1}],
//             ['d', {width: 10, height: 22, type: 1}],
//             ['e', {width: 8.88, height: 22, type: 1}],
//             ['f', {width: 6.66, height: 22, type: 1}],
//             ['g', {width: 10, height: 22, type: 1}],
//             ['h', {width: 10, height: 22, type: 1}],
//             ['i', {width: 5.56, height: 22, type: 1}],
//             ['j', {width: 5.56, height: 22, type: 1}],
//             ['k', {width: 10, height: 22, type: 1}],
//             ['l', {width: 5.56, height: 22, type: 1}],
//             ['m', {width: 15.56, height: 22, type: 1}],
//             ['n', {width: 10, height: 22, type: 1}],
//             ['o', {width: 10, height: 22, type: 1}],
//             ['p', {width: 10, height: 22, type: 1}],
//             ['q', {width: 10, height: 22, type: 1}],
//             ['r', {width: 6.66, height: 22, type: 1}],
//             ['s', {width: 7.78, height: 22, type: 1}],
//             ['t', {width: 5.56, height: 22, type: 1}],
//             ['u', {width: 10, height: 22, type: 1}],
//             ['v', {width: 10, height: 22, type: 1}],
//             ['w', {width: 14.44, height: 22, type: 1}],
//             ['x', {width: 10, height: 22, type: 1}],
//             ['y', {width: 10, height: 22, type: 1}],
//             ['z', {width: 8.88, height: 22, type: 1}],
//             ['A', {width: 14.44, height: 22, type: 1}],
//             ['B', {width: 13.34, height: 22, type: 1}],
//             ['C', {width: 13.34, height: 22, type: 1}],
//             ['D', {width: 14.44, height: 22, type: 1}],
//             ['E', {width: 12.22, height: 22, type: 1}],
//             ['F', {width: 11.12, height: 22, type: 1}],
//             ['G', {width: 14.44, height: 22, type: 1}],
//             ['H', {width: 14.44, height: 22, type: 1}],
//             ['I', {width: 6.66, height: 22, type: 1}],
//             ['J', {width: 7.78, height: 22, type: 1}],
//             ['K', {width: 14.44, height: 22, type: 1}],
//             ['L', {width: 12.22, height: 22, type: 1}],
//             ['M', {width: 17.78, height: 22, type: 1}],
//             ['N', {width: 14.44, height: 22, type: 1}],
//             ['O', {width: 14.44, height: 22, type: 1}],
//             ['P', {width: 11.12, height: 22, type: 1}],
//             ['Q', {width: 14.44, height: 22, type: 1}],
//             ['R', {width: 13.34, height: 22, type: 1}],
//             ['S', {width: 11.12, height: 22, type: 1}],
//             ['T', {width: 12.22, height: 22, type: 1}],
//             ['U', {width: 14.44, height: 22, type: 1}],
//             ['V', {width: 14.44, height: 22, type: 1}],
//             ['W', {width: 18.88, height: 22, type: 1}],
//             ['X', {width: 14.44, height: 22, type: 1}],
//             ['Y', {width: 14.44, height: 22, type: 1}],
//             ['Z', {width: 12.22, height: 22, type: 1}],
//             [',', {width: 5, height: 22, type: 1}],
//             ['.', {width: 5, height: 22, type: 1}],
//             [';', {width: 5.56, height: 22, type: 1}],
//             ['/', {width: 5.56, height: 22, type: 1}],
//             ['?', {width: 8.88, height: 22, type: 1}],
//             ['(', {width: 6.66, height: 22, type: 1}],
//             [')', {width: 6.66, height: 22, type: 1}],
//             ['[', {width: 6.66, height: 22, type: 1}],
//             [']', {width: 6.66, height: 22, type: 1}],
//             ['=', {width: 11.28, height: 22, type: 1}],
//             ['-', {width: 6.66, height: 22, type: 1}],
//             ['+', {width: 11.28, height: 22, type: 1}],
//             ['*', {width: 10, height: 22, type: 1}],
//             ['<', {width: 11.28, height: 22, type: 1}],
//             ['>', {width: 11.28, height: 22, type: 1}],
//             ['，', {width: 20, height: 22, type: 1}],
//             ['。', {width: 20, height: 22, type: 1}],
//             ['；', {width: 20, height: 22, type: 1}],
//             ['’', {width: 6.66, height: 22, type: 1}],
//             ['“', {width: 8.88, height: 22, type: 1}],
//             ['？', {width: 20, height: 22, type: 1}],
//             ['、', {width: 20, height: 22, type: 1}],
//             ['（', {width: 20, height: 22, type: 1}],
//             ['）', {width: 20, height: 22, type: 1}],
//             ['【', {width: 20, height: 22, type: 1}],
//             ['】', {width: 20, height: 22, type: 1}],
//             [paraEnd, {width: 0, height: 22, type: 1}],
//             ['{', {width: 9.6, height: 22, type: 1}],
//             ['}', {width: 9.6, height: 22, type: 1}],
//             ['☑', {width: 17.23, height: 22, type: 1}],
//             ['☐', {width: 17.23, height: 22, type: 1}],
//             ['⊙', {width: 20, height: 22, type: 1}],
//             ['⭘', {width: 20, height: 22, type: 1}],
//             ['◯', {width: 21.77, height: 22, type: 1}],
//             ['：', {width: 20, height: 22, type: 1}],
//             [':', {width: 5.56, height: 22, type: 1}],
//             ['℃', {width: 20.48, height: 22, type: 1}],
//             ['Ⅰ', {width: 6.66, height: 22, type: 1}],
//             ['Ⅱ', {width: 12.93, height: 22, type: 1}],
//         ])
//     );
//     CACHES.set(
//         'Times New Roman18.7', new Map([
//             ['我', {width: 18.7, height: 21, type: 1}],
//             [' ', {width: 4.67, height: 21, type: 1}],
//             ['0', {width: 9.35, height: 21, type: 1}],
//             ['1', {width: 9.35, height: 21, type: 1}],
//             ['2', {width: 9.35, height: 21, type: 1}],
//             ['3', {width: 9.35, height: 21, type: 1}],
//             ['4', {width: 9.35, height: 21, type: 1}],
//             ['5', {width: 9.35, height: 21, type: 1}],
//             ['6', {width: 9.35, height: 21, type: 1}],
//             ['7', {width: 9.35, height: 21, type: 1}],
//             ['8', {width: 9.35, height: 21, type: 1}],
//             ['9', {width: 9.35, height: 21, type: 1}],
//             ['a', {width: 8.3, height: 21, type: 1}],
//             ['b', {width: 9.35, height: 21, type: 1}],
//             ['c', {width: 8.3, height: 21, type: 1}],
//             ['d', {width: 9.35, height: 21, type: 1}],
//             ['e', {width: 8.3, height: 21, type: 1}],
//             ['f', {width: 6.23, height: 21, type: 1}],
//             ['g', {width: 9.35, height: 21, type: 1}],
//             ['h', {width: 9.35, height: 21, type: 1}],
//             ['i', {width: 5.2, height: 21, type: 1}],
//             ['j', {width: 5.2, height: 21, type: 1}],
//             ['k', {width: 9.35, height: 21, type: 1}],
//             ['l', {width: 5.2, height: 21, type: 1}],
//             ['m', {width: 14.55, height: 21, type: 1}],
//             ['n', {width: 9.35, height: 21, type: 1}],
//             ['o', {width: 9.35, height: 21, type: 1}],
//             ['p', {width: 9.35, height: 21, type: 1}],
//             ['q', {width: 9.35, height: 21, type: 1}],
//             ['r', {width: 6.23, height: 21, type: 1}],
//             ['s', {width: 7.28, height: 21, type: 1}],
//             ['t', {width: 5.2, height: 21, type: 1}],
//             ['u', {width: 9.35, height: 21, type: 1}],
//             ['v', {width: 9.35, height: 21, type: 1}],
//             ['w', {width: 13.5, height: 21, type: 1}],
//             ['x', {width: 9.35, height: 21, type: 1}],
//             ['y', {width: 9.35, height: 21, type: 1}],
//             ['z', {width: 8.3, height: 21, type: 1}],
//             ['A', {width: 13.5, height: 21, type: 1}],
//             ['B', {width: 12.47, height: 21, type: 1}],
//             ['C', {width: 12.47, height: 21, type: 1}],
//             ['D', {width: 13.5, height: 21, type: 1}],
//             ['E', {width: 11.42, height: 21, type: 1}],
//             ['F', {width: 10.4, height: 21, type: 1}],
//             ['G', {width: 13.5, height: 21, type: 1}],
//             ['H', {width: 13.5, height: 21, type: 1}],
//             ['I', {width: 6.23, height: 21, type: 1}],
//             ['J', {width: 7.28, height: 21, type: 1}],
//             ['K', {width: 13.5, height: 21, type: 1}],
//             ['L', {width: 11.42, height: 21, type: 1}],
//             ['M', {width: 16.63, height: 21, type: 1}],
//             ['N', {width: 13.5, height: 21, type: 1}],
//             ['O', {width: 13.5, height: 21, type: 1}],
//             ['P', {width: 10.4, height: 21, type: 1}],
//             ['Q', {width: 13.5, height: 21, type: 1}],
//             ['R', {width: 12.47, height: 21, type: 1}],
//             ['S', {width: 10.4, height: 21, type: 1}],
//             ['T', {width: 11.42, height: 21, type: 1}],
//             ['U', {width: 13.5, height: 21, type: 1}],
//             ['V', {width: 13.5, height: 21, type: 1}],
//             ['W', {width: 17.65, height: 21, type: 1}],
//             ['X', {width: 13.5, height: 21, type: 1}],
//             ['Y', {width: 13.5, height: 21, type: 1}],
//             ['Z', {width: 11.42, height: 21, type: 1}],
//             [',', {width: 4.67, height: 21, type: 1}],
//             ['.', {width: 4.67, height: 21, type: 1}],
//             [';', {width: 5.2, height: 21, type: 1}],
//             ['/', {width: 5.2, height: 21, type: 1}],
//             ['?', {width: 8.3, height: 21, type: 1}],
//             ['(', {width: 6.23, height: 21, type: 1}],
//             [')', {width: 6.23, height: 21, type: 1}],
//             ['[', {width: 6.23, height: 21, type: 1}],
//             [']', {width: 6.23, height: 21, type: 1}],
//             ['=', {width: 10.55, height: 21, type: 1}],
//             ['-', {width: 6.23, height: 21, type: 1}],
//             ['+', {width: 10.55, height: 21, type: 1}],
//             ['*', {width: 9.35, height: 21, type: 1}],
//             ['<', {width: 10.55, height: 21, type: 1}],
//             ['>', {width: 10.55, height: 21, type: 1}],
//             ['，', {width: 18.7, height: 21, type: 1}],
//             ['。', {width: 18.7, height: 21, type: 1}],
//             ['；', {width: 18.7, height: 21, type: 1}],
//             ['’', {width: 6.23, height: 21, type: 1}],
//             ['“', {width: 8.3, height: 21, type: 1}],
//             ['？', {width: 18.7, height: 21, type: 1}],
//             ['、', {width: 18.7, height: 21, type: 1}],
//             ['（', {width: 18.7, height: 21, type: 1}],
//             ['）', {width: 18.7, height: 21, type: 1}],
//             ['【', {width: 18.7, height: 21, type: 1}],
//             ['】', {width: 18.7, height: 21, type: 1}],
//             [paraEnd, {width: 0, height: 21, type: 1}],
//             ['{', {width: 8.98, height: 21, type: 1}],
//             ['}', {width: 8.98, height: 21, type: 1}],
//             ['☑', {width: 16.11, height: 21, type: 1}],
//             ['☐', {width: 16.11, height: 21, type: 1}],
//             ['⊙', {width: 18.7, height: 21, type: 1}],
//             ['⭘', {width: 18.7, height: 21, type: 1}],
//             ['◯', {width: 20.35, height: 21, type: 1}],
//             ['：', {width: 18.7, height: 21, type: 1}],
//             [':', {width: 5.2, height: 21, type: 1}],
//             ['℃', {width: 19.15, height: 21, type: 1}],
//             ['Ⅰ', {width: 6.23, height: 21, type: 1}],
//             ['Ⅱ', {width: 12.09, height: 21, type: 1}],
//         ])
//     );
//     CACHES.set(
//         'Times New Roman16', new Map([
//             ['我', {width: 16, height: 17, type: 1}],
//             [' ', {width: 4, height: 17, type: 1}],
//             ['0', {width: 8, height: 17, type: 1}],
//             ['1', {width: 8, height: 17, type: 1}],
//             ['2', {width: 8, height: 17, type: 1}],
//             ['3', {width: 8, height: 17, type: 1}],
//             ['4', {width: 8, height: 17, type: 1}],
//             ['5', {width: 8, height: 17, type: 1}],
//             ['6', {width: 8, height: 17, type: 1}],
//             ['7', {width: 8, height: 17, type: 1}],
//             ['8', {width: 8, height: 17, type: 1}],
//             ['9', {width: 8, height: 17, type: 1}],
//             ['a', {width: 7.1, height: 17, type: 1}],
//             ['b', {width: 8, height: 17, type: 1}],
//             ['c', {width: 7.1, height: 17, type: 1}],
//             ['d', {width: 8, height: 17, type: 1}],
//             ['e', {width: 7.1, height: 17, type: 1}],
//             ['f', {width: 5.33, height: 17, type: 1}],
//             ['g', {width: 8, height: 17, type: 1}],
//             ['h', {width: 8, height: 17, type: 1}],
//             ['i', {width: 4.45, height: 17, type: 1}],
//             ['j', {width: 4.45, height: 17, type: 1}],
//             ['k', {width: 8, height: 17, type: 1}],
//             ['l', {width: 4.45, height: 17, type: 1}],
//             ['m', {width: 12.45, height: 17, type: 1}],
//             ['n', {width: 8, height: 17, type: 1}],
//             ['o', {width: 8, height: 17, type: 1}],
//             ['p', {width: 8, height: 17, type: 1}],
//             ['q', {width: 8, height: 17, type: 1}],
//             ['r', {width: 5.33, height: 17, type: 1}],
//             ['s', {width: 6.23, height: 17, type: 1}],
//             ['t', {width: 4.45, height: 17, type: 1}],
//             ['u', {width: 8, height: 17, type: 1}],
//             ['v', {width: 8, height: 17, type: 1}],
//             ['w', {width: 11.55, height: 17, type: 1}],
//             ['x', {width: 8, height: 17, type: 1}],
//             ['y', {width: 8, height: 17, type: 1}],
//             ['z', {width: 7.1, height: 17, type: 1}],
//             ['A', {width: 11.55, height: 17, type: 1}],
//             ['B', {width: 10.67, height: 17, type: 1}],
//             ['C', {width: 10.67, height: 17, type: 1}],
//             ['D', {width: 11.55, height: 17, type: 1}],
//             ['E', {width: 9.77, height: 17, type: 1}],
//             ['F', {width: 8.9, height: 17, type: 1}],
//             ['G', {width: 11.55, height: 17, type: 1}],
//             ['H', {width: 11.55, height: 17, type: 1}],
//             ['I', {width: 5.33, height: 17, type: 1}],
//             ['J', {width: 6.23, height: 17, type: 1}],
//             ['K', {width: 11.55, height: 17, type: 1}],
//             ['L', {width: 9.77, height: 17, type: 1}],
//             ['M', {width: 14.23, height: 17, type: 1}],
//             ['N', {width: 11.55, height: 17, type: 1}],
//             ['O', {width: 11.55, height: 17, type: 1}],
//             ['P', {width: 8.9, height: 17, type: 1}],
//             ['Q', {width: 11.55, height: 17, type: 1}],
//             ['R', {width: 10.67, height: 17, type: 1}],
//             ['S', {width: 8.9, height: 17, type: 1}],
//             ['T', {width: 9.77, height: 17, type: 1}],
//             ['U', {width: 11.55, height: 17, type: 1}],
//             ['V', {width: 11.55, height: 17, type: 1}],
//             ['W', {width: 15.1, height: 17, type: 1}],
//             ['X', {width: 11.55, height: 17, type: 1}],
//             ['Y', {width: 11.55, height: 17, type: 1}],
//             ['Z', {width: 9.77, height: 17, type: 1}],
//             [',', {width: 4, height: 17, type: 1}],
//             ['.', {width: 4, height: 17, type: 1}],
//             [';', {width: 4.45, height: 17, type: 1}],
//             ['/', {width: 4.45, height: 17, type: 1}],
//             ['?', {width: 7.1, height: 17, type: 1}],
//             ['(', {width: 5.33, height: 17, type: 1}],
//             [')', {width: 5.33, height: 17, type: 1}],
//             ['[', {width: 5.33, height: 17, type: 1}],
//             [']', {width: 5.33, height: 17, type: 1}],
//             ['=', {width: 9.02, height: 17, type: 1}],
//             ['-', {width: 5.33, height: 17, type: 1}],
//             ['+', {width: 9.02, height: 17, type: 1}],
//             ['*', {width: 8, height: 17, type: 1}],
//             ['<', {width: 9.02, height: 17, type: 1}],
//             ['>', {width: 9.02, height: 17, type: 1}],
//             ['，', {width: 16, height: 17, type: 1}],
//             ['。', {width: 16, height: 17, type: 1}],
//             ['；', {width: 16, height: 17, type: 1}],
//             ['’', {width: 5.33, height: 17, type: 1}],
//             ['“', {width: 7.1, height: 17, type: 1}],
//             ['？', {width: 16, height: 17, type: 1}],
//             ['、', {width: 16, height: 17, type: 1}],
//             ['（', {width: 16, height: 17, type: 1}],
//             ['）', {width: 16, height: 17, type: 1}],
//             ['【', {width: 16, height: 17, type: 1}],
//             ['】', {width: 16, height: 17, type: 1}],
//             [paraEnd, {width: 0, height: 17, type: 1}],
//             ['{', {width: 7.68, height: 17, type: 1}],
//             ['}', {width: 7.68, height: 17, type: 1}],
//             ['☑', {width: 13.78, height: 17, type: 1}],
//             ['☐', {width: 13.78, height: 17, type: 1}],
//             ['⊙', {width: 16, height: 17, type: 1}],
//             ['⭘', {width: 16, height: 17, type: 1}],
//             ['◯', {width: 17, height: 17, type: 1}],
//             ['：', {width: 16, height: 17, type: 1}],
//             [':', {width: 4.45, height: 17, type: 1}],
//             ['℃', {width: 16.38, height: 17, type: 1}],
//             ['Ⅰ', {width: 5.33, height: 17, type: 1}],
//             ['Ⅱ', {width: 10.34, height: 17, type: 1}],
//         ])
//     );
//     CACHES.set(
//         'Times New Roman14.7', new Map([
//             ['我', {width: 14.7, height: 16, type: 1}],
//             [' ', {width: 3.67, height: 16, type: 1}],
//             ['0', {width: 7.35, height: 16, type: 1}],
//             ['1', {width: 7.35, height: 16, type: 1}],
//             ['2', {width: 7.35, height: 16, type: 1}],
//             ['3', {width: 7.35, height: 16, type: 1}],
//             ['4', {width: 7.35, height: 16, type: 1}],
//             ['5', {width: 7.35, height: 16, type: 1}],
//             ['6', {width: 7.35, height: 16, type: 1}],
//             ['7', {width: 7.35, height: 16, type: 1}],
//             ['8', {width: 7.35, height: 16, type: 1}],
//             ['9', {width: 7.35, height: 16, type: 1}],
//             ['a', {width: 6.52, height: 16, type: 1}],
//             ['b', {width: 7.35, height: 16, type: 1}],
//             ['c', {width: 6.52, height: 16, type: 1}],
//             ['d', {width: 7.35, height: 16, type: 1}],
//             ['e', {width: 6.52, height: 16, type: 1}],
//             ['f', {width: 4.9, height: 16, type: 1}],
//             ['g', {width: 7.35, height: 16, type: 1}],
//             ['h', {width: 7.35, height: 16, type: 1}],
//             ['i', {width: 4.08, height: 16, type: 1}],
//             ['j', {width: 4.08, height: 16, type: 1}],
//             ['k', {width: 7.35, height: 16, type: 1}],
//             ['l', {width: 4.08, height: 16, type: 1}],
//             ['m', {width: 11.43, height: 16, type: 1}],
//             ['n', {width: 7.35, height: 16, type: 1}],
//             ['o', {width: 7.35, height: 16, type: 1}],
//             ['p', {width: 7.35, height: 16, type: 1}],
//             ['q', {width: 7.35, height: 16, type: 1}],
//             ['r', {width: 4.9, height: 16, type: 1}],
//             ['s', {width: 5.72, height: 16, type: 1}],
//             ['t', {width: 4.08, height: 16, type: 1}],
//             ['u', {width: 7.35, height: 16, type: 1}],
//             ['v', {width: 7.35, height: 16, type: 1}],
//             ['w', {width: 10.62, height: 16, type: 1}],
//             ['x', {width: 7.35, height: 16, type: 1}],
//             ['y', {width: 7.35, height: 16, type: 1}],
//             ['z', {width: 6.52, height: 16, type: 1}],
//             ['A', {width: 10.62, height: 16, type: 1}],
//             ['B', {width: 9.8, height: 16, type: 1}],
//             ['C', {width: 9.8, height: 16, type: 1}],
//             ['D', {width: 10.62, height: 16, type: 1}],
//             ['E', {width: 8.98, height: 16, type: 1}],
//             ['F', {width: 8.18, height: 16, type: 1}],
//             ['G', {width: 10.62, height: 16, type: 1}],
//             ['H', {width: 10.62, height: 16, type: 1}],
//             ['I', {width: 4.9, height: 16, type: 1}],
//             ['J', {width: 5.72, height: 16, type: 1}],
//             ['K', {width: 10.62, height: 16, type: 1}],
//             ['L', {width: 8.98, height: 16, type: 1}],
//             ['M', {width: 13.07, height: 16, type: 1}],
//             ['N', {width: 10.62, height: 16, type: 1}],
//             ['O', {width: 10.62, height: 16, type: 1}],
//             ['P', {width: 8.18, height: 16, type: 1}],
//             ['Q', {width: 10.62, height: 16, type: 1}],
//             ['R', {width: 9.8, height: 16, type: 1}],
//             ['S', {width: 8.18, height: 16, type: 1}],
//             ['T', {width: 8.98, height: 16, type: 1}],
//             ['U', {width: 10.62, height: 16, type: 1}],
//             ['V', {width: 10.62, height: 16, type: 1}],
//             ['W', {width: 13.87, height: 16, type: 1}],
//             ['X', {width: 10.62, height: 16, type: 1}],
//             ['Y', {width: 10.62, height: 16, type: 1}],
//             ['Z', {width: 8.98, height: 16, type: 1}],
//             [',', {width: 3.67, height: 16, type: 1}],
//             ['.', {width: 3.67, height: 16, type: 1}],
//             [';', {width: 4.08, height: 16, type: 1}],
//             ['/', {width: 4.08, height: 16, type: 1}],
//             ['?', {width: 6.52, height: 16, type: 1}],
//             ['(', {width: 4.9, height: 16, type: 1}],
//             [')', {width: 4.9, height: 16, type: 1}],
//             ['[', {width: 4.9, height: 16, type: 1}],
//             [']', {width: 4.9, height: 16, type: 1}],
//             ['=', {width: 8.29, height: 16, type: 1}],
//             ['-', {width: 4.9, height: 16, type: 1}],
//             ['+', {width: 8.29, height: 16, type: 1}],
//             ['*', {width: 7.35, height: 16, type: 1}],
//             ['<', {width: 8.29, height: 16, type: 1}],
//             ['>', {width: 8.29, height: 16, type: 1}],
//             ['，', {width: 14.7, height: 16, type: 1}],
//             ['。', {width: 14.7, height: 16, type: 1}],
//             ['；', {width: 14.7, height: 16, type: 1}],
//             ['’', {width: 4.9, height: 16, type: 1}],
//             ['“', {width: 6.52, height: 16, type: 1}],
//             ['？', {width: 14.7, height: 16, type: 1}],
//             ['、', {width: 14.7, height: 16, type: 1}],
//             ['（', {width: 14.7, height: 16, type: 1}],
//             ['）', {width: 14.7, height: 16, type: 1}],
//             ['【', {width: 14.7, height: 16, type: 1}],
//             ['】', {width: 14.7, height: 16, type: 1}],
//             ['Ⅰ', {width: 4.9, height: 16, type: 1}],
//             ['Ⅱ', {width: 9.5, height: 16, type: 1}],
//             [':', {width: 4.08, height: 16, type: 1}],
//             ['：', {width: 14.7, height: 16, type: 1}],
//             [paraEnd, {width: 0, height: 16, type: 1}],
//             ['{', {width: 7.06, height: 16, type: 1}],
//             ['}', {width: 7.06, height: 16, type: 1}],
//             ['☑', {width: 12.66, height: 16, type: 1}],
//             ['☐', {width: 12.66, height: 16, type: 1}],
//             ['⊙', {width: 14.7, height: 16, type: 1}],
//             ['⭘', {width: 14.7, height: 16, type: 1}],
//             ['◯', {width: 16, height: 16, type: 1}],
//             ['℃', {width: 15.05, height: 16, type: 1}],
//         ])
//     );
//     CACHES.set(
//         'Times New Roman14', new Map([
//             ['我', {width: 14, height: 15, type: 1}],
//             [' ', {width: 3.5, height: 15, type: 1}],
//             ['0', {width: 7, height: 15, type: 1}],
//             ['1', {width: 7, height: 15, type: 1}],
//             ['2', {width: 7, height: 15, type: 1}],
//             ['3', {width: 7, height: 15, type: 1}],
//             ['4', {width: 7, height: 15, type: 1}],
//             ['5', {width: 7, height: 15, type: 1}],
//             ['6', {width: 7, height: 15, type: 1}],
//             ['7', {width: 7, height: 15, type: 1}],
//             ['8', {width: 7, height: 15, type: 1}],
//             ['9', {width: 7, height: 15, type: 1}],
//             ['a', {width: 6.21, height: 15, type: 1}],
//             ['b', {width: 7, height: 15, type: 1}],
//             ['c', {width: 6.21, height: 15, type: 1}],
//             ['d', {width: 7, height: 15, type: 1}],
//             ['e', {width: 6.21, height: 15, type: 1}],
//             ['f', {width: 4.66, height: 15, type: 1}],
//             ['g', {width: 7, height: 15, type: 1}],
//             ['h', {width: 7, height: 15, type: 1}],
//             ['i', {width: 3.89, height: 15, type: 1}],
//             ['j', {width: 3.89, height: 15, type: 1}],
//             ['k', {width: 7, height: 15, type: 1}],
//             ['l', {width: 3.89, height: 15, type: 1}],
//             ['m', {width: 10.89, height: 15, type: 1}],
//             ['n', {width: 7, height: 15, type: 1}],
//             ['o', {width: 7, height: 15, type: 1}],
//             ['p', {width: 7, height: 15, type: 1}],
//             ['q', {width: 7, height: 15, type: 1}],
//             ['r', {width: 4.66, height: 15, type: 1}],
//             ['s', {width: 5.45, height: 15, type: 1}],
//             ['t', {width: 3.89, height: 15, type: 1}],
//             ['u', {width: 7, height: 15, type: 1}],
//             ['v', {width: 7, height: 15, type: 1}],
//             ['w', {width: 10.11, height: 15, type: 1}],
//             ['x', {width: 7, height: 15, type: 1}],
//             ['y', {width: 7, height: 15, type: 1}],
//             ['z', {width: 6.21, height: 15, type: 1}],
//             ['A', {width: 10.11, height: 15, type: 1}],
//             ['B', {width: 9.34, height: 15, type: 1}],
//             ['C', {width: 9.34, height: 15, type: 1}],
//             ['D', {width: 10.11, height: 15, type: 1}],
//             ['E', {width: 8.55, height: 15, type: 1}],
//             ['F', {width: 7.79, height: 15, type: 1}],
//             ['G', {width: 10.11, height: 15, type: 1}],
//             ['H', {width: 10.11, height: 15, type: 1}],
//             ['I', {width: 4.66, height: 15, type: 1}],
//             ['J', {width: 5.45, height: 15, type: 1}],
//             ['K', {width: 10.11, height: 15, type: 1}],
//             ['L', {width: 8.55, height: 15, type: 1}],
//             ['M', {width: 12.45, height: 15, type: 1}],
//             ['N', {width: 10.11, height: 15, type: 1}],
//             ['O', {width: 10.11, height: 15, type: 1}],
//             ['P', {width: 7.79, height: 15, type: 1}],
//             ['Q', {width: 10.11, height: 15, type: 1}],
//             ['R', {width: 9.34, height: 15, type: 1}],
//             ['S', {width: 7.79, height: 15, type: 1}],
//             ['T', {width: 8.55, height: 15, type: 1}],
//             ['U', {width: 10.11, height: 15, type: 1}],
//             ['V', {width: 10.11, height: 15, type: 1}],
//             ['W', {width: 13.21, height: 15, type: 1}],
//             ['X', {width: 10.11, height: 15, type: 1}],
//             ['Y', {width: 10.11, height: 15, type: 1}],
//             ['Z', {width: 8.55, height: 15, type: 1}],
//             [',', {width: 3.5, height: 15, type: 1}],
//             ['.', {width: 3.5, height: 15, type: 1}],
//             [';', {width: 3.89, height: 15, type: 1}],
//             ['/', {width: 3.89, height: 15, type: 1}],
//             ['?', {width: 6.21, height: 15, type: 1}],
//             ['(', {width: 4.66, height: 15, type: 1}],
//             [')', {width: 4.66, height: 15, type: 1}],
//             ['[', {width: 4.66, height: 15, type: 1}],
//             [']', {width: 4.66, height: 15, type: 1}],
//             ['=', {width: 7.9, height: 15, type: 1}],
//             ['-', {width: 4.66, height: 15, type: 1}],
//             ['+', {width: 7.9, height: 15, type: 1}],
//             ['*', {width: 7, height: 15, type: 1}],
//             ['<', {width: 7.9, height: 15, type: 1}],
//             ['>', {width: 7.9, height: 15, type: 1}],
//             ['，', {width: 14, height: 15, type: 1}],
//             ['。', {width: 14, height: 15, type: 1}],
//             ['；', {width: 14, height: 15, type: 1}],
//             ['’', {width: 4.66, height: 15, type: 1}],
//             ['“', {width: 6.21, height: 15, type: 1}],
//             ['？', {width: 14, height: 15, type: 1}],
//             ['、', {width: 14, height: 15, type: 1}],
//             ['（', {width: 14, height: 15, type: 1}],
//             ['）', {width: 14, height: 15, type: 1}],
//             ['【', {width: 14, height: 15, type: 1}],
//             ['】', {width: 14, height: 15, type: 1}],
//             [paraEnd, {width: 0, height: 15, type: 1}],
//             ['{', {width: 6.72, height: 15, type: 1}],
//             ['}', {width: 6.72, height: 15, type: 1}],
//             ['☑', {width: 12.06, height: 15, type: 1}],
//             ['☐', {width: 12.06, height: 15, type: 1}],
//             ['⊙', {width: 14, height: 15, type: 1}],
//             ['⭘', {width: 14, height: 15, type: 1}],
//             ['◯', {width: 15.24, height: 15, type: 1}],
//             ['：', {width: 14, height: 15, type: 1}],
//             [':', {width: 3.89, height: 15, type: 1}],
//             ['℃', {width: 14.33, height: 15, type: 1}],
//             ['Ⅰ', {width: 4.66, height: 15, type: 1}],
//             ['Ⅱ', {width: 9.05, height: 15, type: 1}],
//             [':', {width: 3.89, height: 15, type: 1}],
//             ['：', {width: 14, height: 15, type: 1}],
//         ])
//     );
//     CACHES.set(
//         'Times New Roman13.3', new Map([
//             ['我', {width: 13.3, height: 15, type: 1}],
//             [' ', {width: 3.32, height: 15, type: 1}],
//             ['0', {width: 6.65, height: 15, type: 1}],
//             ['1', {width: 6.65, height: 15, type: 1}],
//             ['2', {width: 6.65, height: 15, type: 1}],
//             ['3', {width: 6.65, height: 15, type: 1}],
//             ['4', {width: 6.65, height: 15, type: 1}],
//             ['5', {width: 6.65, height: 15, type: 1}],
//             ['6', {width: 6.65, height: 15, type: 1}],
//             ['7', {width: 6.65, height: 15, type: 1}],
//             ['8', {width: 6.65, height: 15, type: 1}],
//             ['9', {width: 6.65, height: 15, type: 1}],
//             ['a', {width: 5.9, height: 15, type: 1}],
//             ['b', {width: 6.65, height: 15, type: 1}],
//             ['c', {width: 5.9, height: 15, type: 1}],
//             ['d', {width: 6.65, height: 15, type: 1}],
//             ['e', {width: 5.9, height: 15, type: 1}],
//             ['f', {width: 4.43, height: 15, type: 1}],
//             ['g', {width: 6.65, height: 15, type: 1}],
//             ['h', {width: 6.65, height: 15, type: 1}],
//             ['i', {width: 3.7, height: 15, type: 1}],
//             ['j', {width: 3.7, height: 15, type: 1}],
//             ['k', {width: 6.65, height: 15, type: 1}],
//             ['l', {width: 3.7, height: 15, type: 1}],
//             ['m', {width: 10.35, height: 15, type: 1}],
//             ['n', {width: 6.65, height: 15, type: 1}],
//             ['o', {width: 6.65, height: 15, type: 1}],
//             ['p', {width: 6.65, height: 15, type: 1}],
//             ['q', {width: 6.65, height: 15, type: 1}],
//             ['r', {width: 4.43, height: 15, type: 1}],
//             ['s', {width: 5.18, height: 15, type: 1}],
//             ['t', {width: 3.7, height: 15, type: 1}],
//             ['u', {width: 6.65, height: 15, type: 1}],
//             ['v', {width: 6.65, height: 15, type: 1}],
//             ['w', {width: 9.6, height: 15, type: 1}],
//             ['x', {width: 6.65, height: 15, type: 1}],
//             ['y', {width: 6.65, height: 15, type: 1}],
//             ['z', {width: 5.9, height: 15, type: 1}],
//             ['A', {width: 9.6, height: 15, type: 1}],
//             ['B', {width: 8.87, height: 15, type: 1}],
//             ['C', {width: 8.87, height: 15, type: 1}],
//             ['D', {width: 9.6, height: 15, type: 1}],
//             ['E', {width: 8.12, height: 15, type: 1}],
//             ['F', {width: 7.4, height: 15, type: 1}],
//             ['G', {width: 9.6, height: 15, type: 1}],
//             ['H', {width: 9.6, height: 15, type: 1}],
//             ['I', {width: 4.43, height: 15, type: 1}],
//             ['J', {width: 5.18, height: 15, type: 1}],
//             ['K', {width: 9.6, height: 15, type: 1}],
//             ['L', {width: 8.12, height: 15, type: 1}],
//             ['M', {width: 11.83, height: 15, type: 1}],
//             ['N', {width: 9.6, height: 15, type: 1}],
//             ['O', {width: 9.6, height: 15, type: 1}],
//             ['P', {width: 7.4, height: 15, type: 1}],
//             ['Q', {width: 9.6, height: 15, type: 1}],
//             ['R', {width: 8.87, height: 15, type: 1}],
//             ['S', {width: 7.4, height: 15, type: 1}],
//             ['T', {width: 8.12, height: 15, type: 1}],
//             ['U', {width: 9.6, height: 15, type: 1}],
//             ['V', {width: 9.6, height: 15, type: 1}],
//             ['W', {width: 12.55, height: 15, type: 1}],
//             ['X', {width: 9.6, height: 15, type: 1}],
//             ['Y', {width: 9.6, height: 15, type: 1}],
//             ['Z', {width: 8.12, height: 15, type: 1}],
//             [',', {width: 3.33, height: 15, type: 1}],
//             ['.', {width: 3.33, height: 15, type: 1}],
//             [';', {width: 3.7, height: 15, type: 1}],
//             ['/', {width: 3.7, height: 15, type: 1}],
//             ['?', {width: 5.9, height: 15, type: 1}],
//             ['(', {width: 4.43, height: 15, type: 1}],
//             [')', {width: 4.43, height: 15, type: 1}],
//             ['[', {width: 4.43, height: 15, type: 1}],
//             [']', {width: 4.43, height: 15, type: 1}],
//             ['=', {width: 7.5, height: 15, type: 1}],
//             ['-', {width: 4.43, height: 15, type: 1}],
//             ['+', {width: 7.5, height: 15, type: 1}],
//             ['*', {width: 6.65, height: 15, type: 1}],
//             ['<', {width: 7.5, height: 15, type: 1}],
//             ['>', {width: 7.5, height: 15, type: 1}],
//             ['，', {width: 13.3, height: 15, type: 1}],
//             ['。', {width: 13.3, height: 15, type: 1}],
//             ['；', {width: 13.3, height: 15, type: 1}],
//             ['’', {width: 4.43, height: 15, type: 1}],
//             ['“', {width: 5.9, height: 15, type: 1}],
//             ['？', {width: 13.3, height: 15, type: 1}],
//             ['、', {width: 13.3, height: 15, type: 1}],
//             ['（', {width: 13.3, height: 15, type: 1}],
//             ['）', {width: 13.3, height: 15, type: 1}],
//             ['【', {width: 13.3, height: 15, type: 1}],
//             ['】', {width: 13.3, height: 15, type: 1}],
//             ['Ⅰ', {width: 4.43, height: 15, type: 1}],
//             ['Ⅱ', {width: 8.6, height: 15, type: 1}],
//             [':', {width: 3.7, height: 15, type: 1}],
//             ['：', {width: 13.3, height: 15, type: 1}],
//             [paraEnd, {width: 0, height: 15, type: 1}],
//             ['{', {width: 6.38, height: 15, type: 1}],
//             ['}', {width: 6.38, height: 15, type: 1}],
//             ['☑', {width: 11.46, height: 15, type: 1}],
//             ['☐', {width: 11.46, height: 15, type: 1}],
//             ['⊙', {width: 13.3, height: 15, type: 1}],
//             ['⭘', {width: 13.3, height: 15, type: 1}],
//             ['◯', {width: 14, height: 15, type: 1}],
//             ['℃', {width: 13.62, height: 15, type: 1}],
//         ])
//     );
//     CACHES.set(
//         'Times New Roman12', new Map([
//             ['我', {width: 12, height: 14, type: 1}],
//             [' ', {width: 3, height: 14, type: 1}],
//             ['0', {width: 6, height: 14, type: 1}],
//             ['1', {width: 6, height: 14, type: 1}],
//             ['2', {width: 6, height: 14, type: 1}],
//             ['3', {width: 6, height: 14, type: 1}],
//             ['4', {width: 6, height: 14, type: 1}],
//             ['5', {width: 6, height: 14, type: 1}],
//             ['6', {width: 6, height: 14, type: 1}],
//             ['7', {width: 6, height: 14, type: 1}],
//             ['8', {width: 6, height: 14, type: 1}],
//             ['9', {width: 6, height: 14, type: 1}],
//             ['a', {width: 5.33, height: 14, type: 1}],
//             ['b', {width: 6, height: 14, type: 1}],
//             ['c', {width: 5.33, height: 14, type: 1}],
//             ['d', {width: 6, height: 14, type: 1}],
//             ['e', {width: 5.33, height: 14, type: 1}],
//             ['f', {width: 4, height: 14, type: 1}],
//             ['g', {width: 6, height: 14, type: 1}],
//             ['h', {width: 6, height: 14, type: 1}],
//             ['i', {width: 3.33, height: 14, type: 1}],
//             ['j', {width: 3.33, height: 14, type: 1}],
//             ['k', {width: 6, height: 14, type: 1}],
//             ['l', {width: 3.33, height: 14, type: 1}],
//             ['m', {width: 9.33, height: 14, type: 1}],
//             ['n', {width: 6, height: 14, type: 1}],
//             ['o', {width: 6, height: 14, type: 1}],
//             ['p', {width: 6, height: 14, type: 1}],
//             ['q', {width: 6, height: 14, type: 1}],
//             ['r', {width: 4, height: 14, type: 1}],
//             ['s', {width: 4.67, height: 14, type: 1}],
//             ['t', {width: 3.33, height: 14, type: 1}],
//             ['u', {width: 6, height: 14, type: 1}],
//             ['v', {width: 6, height: 14, type: 1}],
//             ['w', {width: 8.67, height: 14, type: 1}],
//             ['x', {width: 6, height: 14, type: 1}],
//             ['y', {width: 6, height: 14, type: 1}],
//             ['z', {width: 5.33, height: 14, type: 1}],
//             ['A', {width: 8.67, height: 14, type: 1}],
//             ['B', {width: 8, height: 14, type: 1}],
//             ['C', {width: 8, height: 14, type: 1}],
//             ['D', {width: 8.67, height: 14, type: 1}],
//             ['E', {width: 7.33, height: 14, type: 1}],
//             ['F', {width: 6.67, height: 14, type: 1}],
//             ['G', {width: 8.67, height: 14, type: 1}],
//             ['H', {width: 8.67, height: 14, type: 1}],
//             ['I', {width: 4, height: 14, type: 1}],
//             ['J', {width: 4.67, height: 14, type: 1}],
//             ['K', {width: 8.67, height: 14, type: 1}],
//             ['L', {width: 7.33, height: 14, type: 1}],
//             ['M', {width: 10.67, height: 14, type: 1}],
//             ['N', {width: 8.67, height: 14, type: 1}],
//             ['O', {width: 8.67, height: 14, type: 1}],
//             ['P', {width: 6.67, height: 14, type: 1}],
//             ['Q', {width: 8.67, height: 14, type: 1}],
//             ['R', {width: 8, height: 14, type: 1}],
//             ['S', {width: 6.67, height: 14, type: 1}],
//             ['T', {width: 7.33, height: 14, type: 1}],
//             ['U', {width: 8.67, height: 14, type: 1}],
//             ['V', {width: 8.67, height: 14, type: 1}],
//             ['W', {width: 11.33, height: 14, type: 1}],
//             ['X', {width: 8.67, height: 14, type: 1}],
//             ['Y', {width: 8.67, height: 14, type: 1}],
//             ['Z', {width: 7.33, height: 14, type: 1}],
//             [',', {width: 3, height: 14, type: 1}],
//             ['.', {width: 3, height: 14, type: 1}],
//             [';', {width: 3.33, height: 14, type: 1}],
//             ['/', {width: 3.33, height: 14, type: 1}],
//             ['?', {width: 5.33, height: 14, type: 1}],
//             ['(', {width: 4, height: 14, type: 1}],
//             [')', {width: 4, height: 14, type: 1}],
//             ['[', {width: 4, height: 14, type: 1}],
//             [']', {width: 4, height: 14, type: 1}],
//             ['=', {width: 6.77, height: 14, type: 1}],
//             ['-', {width: 4, height: 14, type: 1}],
//             ['+', {width: 6.77, height: 14, type: 1}],
//             ['*', {width: 6, height: 14, type: 1}],
//             ['<', {width: 6.77, height: 14, type: 1}],
//             ['>', {width: 6.77, height: 14, type: 1}],
//             ['，', {width: 12, height: 14, type: 1}],
//             ['。', {width: 12, height: 14, type: 1}],
//             ['；', {width: 12, height: 14, type: 1}],
//             ['’', {width: 4, height: 14, type: 1}],
//             ['“', {width: 5.33, height: 14, type: 1}],
//             ['？', {width: 12, height: 14, type: 1}],
//             ['、', {width: 12, height: 14, type: 1}],
//             ['（', {width: 12, height: 14, type: 1}],
//             ['）', {width: 12, height: 14, type: 1}],
//             ['【', {width: 12, height: 14, type: 1}],
//             ['】', {width: 12, height: 14, type: 1}],
//             [paraEnd, {width: 0, height: 14, type: 1}],
//             ['{', {width: 5.76, height: 14, type: 1}],
//             ['}', {width: 5.76, height: 14, type: 1}],
//             ['☑', {width: 10.34, height: 14, type: 1}],
//             ['☐', {width: 10.34, height: 14, type: 1}],
//             ['⊙', {width: 12, height: 14, type: 1}],
//             ['⭘', {width: 12, height: 14, type: 1}],
//             ['◯', {width: 13, height: 14, type: 1}],
//             ['：', {width: 12, height: 14, type: 1}],
//             [':', {width: 3.33, height: 14, type: 1}],
//             ['℃', {width: 12.29, height: 14, type: 1}], ['Ⅰ', {width: 4, height: 14, type: 1}],
//             ['Ⅱ', {width: 7.76, height: 14, type: 1}],
//         ])
//     );
//     CACHES.set(
//         'Times New Roman10.7', new Map([
//             ['我', {width: 10.7, height: 12, type: 1}],
//             [' ', {width: 2.67, height: 12, type: 1}],
//             ['0', {width: 5.35, height: 12, type: 1}],
//             ['1', {width: 5.35, height: 12, type: 1}],
//             ['2', {width: 5.35, height: 12, type: 1}],
//             ['3', {width: 5.35, height: 12, type: 1}],
//             ['4', {width: 5.35, height: 12, type: 1}],
//             ['5', {width: 5.35, height: 12, type: 1}],
//             ['6', {width: 5.35, height: 12, type: 1}],
//             ['7', {width: 5.35, height: 12, type: 1}],
//             ['8', {width: 5.35, height: 12, type: 1}],
//             ['9', {width: 5.35, height: 12, type: 1}],
//             ['a', {width: 4.75, height: 12, type: 1}],
//             ['b', {width: 5.35, height: 12, type: 1}],
//             ['c', {width: 4.75, height: 12, type: 1}],
//             ['d', {width: 5.35, height: 12, type: 1}],
//             ['e', {width: 4.75, height: 12, type: 1}],
//             ['f', {width: 3.56, height: 12, type: 1}],
//             ['g', {width: 5.35, height: 12, type: 1}],
//             ['h', {width: 5.35, height: 12, type: 1}],
//             ['i', {width: 2.97, height: 12, type: 1}],
//             ['j', {width: 2.97, height: 12, type: 1}],
//             ['k', {width: 5.35, height: 12, type: 1}],
//             ['l', {width: 2.97, height: 12, type: 1}],
//             ['m', {width: 8.32, height: 12, type: 1}],
//             ['n', {width: 5.35, height: 12, type: 1}],
//             ['o', {width: 5.35, height: 12, type: 1}],
//             ['p', {width: 5.35, height: 12, type: 1}],
//             ['q', {width: 5.35, height: 12, type: 1}],
//             ['r', {width: 3.56, height: 12, type: 1}],
//             ['s', {width: 4.16, height: 12, type: 1}],
//             ['t', {width: 2.97, height: 12, type: 1}],
//             ['u', {width: 5.35, height: 12, type: 1}],
//             ['v', {width: 5.35, height: 12, type: 1}],
//             ['w', {width: 7.73, height: 12, type: 1}],
//             ['x', {width: 5.35, height: 12, type: 1}],
//             ['y', {width: 5.35, height: 12, type: 1}],
//             ['z', {width: 4.75, height: 12, type: 1}],
//             ['A', {width: 7.73, height: 12, type: 1}],
//             ['B', {width: 7.14, height: 12, type: 1}],
//             ['C', {width: 7.14, height: 12, type: 1}],
//             ['D', {width: 7.73, height: 12, type: 1}],
//             ['E', {width: 6.54, height: 12, type: 1}],
//             ['F', {width: 5.95, height: 12, type: 1}],
//             ['G', {width: 7.73, height: 12, type: 1}],
//             ['H', {width: 7.73, height: 12, type: 1}],
//             ['I', {width: 3.56, height: 12, type: 1}],
//             ['J', {width: 4.16, height: 12, type: 1}],
//             ['K', {width: 7.73, height: 12, type: 1}],
//             ['L', {width: 6.54, height: 12, type: 1}],
//             ['M', {width: 9.51, height: 12, type: 1}],
//             ['N', {width: 7.73, height: 12, type: 1}],
//             ['O', {width: 7.73, height: 12, type: 1}],
//             ['P', {width: 5.95, height: 12, type: 1}],
//             ['Q', {width: 7.73, height: 12, type: 1}],
//             ['R', {width: 7.14, height: 12, type: 1}],
//             ['S', {width: 5.95, height: 12, type: 1}],
//             ['T', {width: 6.54, height: 12, type: 1}],
//             ['U', {width: 7.73, height: 12, type: 1}],
//             ['V', {width: 7.73, height: 12, type: 1}],
//             ['W', {width: 10.1, height: 12, type: 1}],
//             ['X', {width: 7.73, height: 12, type: 1}],
//             ['Y', {width: 7.73, height: 12, type: 1}],
//             ['Z', {width: 6.54, height: 12, type: 1}],
//             [',', {width: 2.67, height: 12, type: 1}],
//             ['.', {width: 2.67, height: 12, type: 1}],
//             [';', {width: 2.97, height: 12, type: 1}],
//             ['/', {width: 2.97, height: 12, type: 1}],
//             ['?', {width: 4.75, height: 12, type: 1}],
//             ['(', {width: 3.56, height: 12, type: 1}],
//             [')', {width: 3.56, height: 12, type: 1}],
//             ['[', {width: 3.56, height: 12, type: 1}],
//             [']', {width: 3.56, height: 12, type: 1}],
//             ['=', {width: 6.03, height: 12, type: 1}],
//             ['-', {width: 3.56, height: 12, type: 1}],
//             ['+', {width: 6.03, height: 12, type: 1}],
//             ['*', {width: 5.35, height: 12, type: 1}],
//             ['<', {width: 6.03, height: 12, type: 1}],
//             ['>', {width: 6.03, height: 12, type: 1}],
//             ['，', {width: 10.7, height: 12, type: 1}],
//             ['。', {width: 10.7, height: 12, type: 1}],
//             ['；', {width: 10.7, height: 12, type: 1}],
//             ['’', {width: 3.56, height: 12, type: 1}],
//             ['“', {width: 4.75, height: 12, type: 1}],
//             ['？', {width: 10.7, height: 12, type: 1}],
//             ['、', {width: 10.7, height: 12, type: 1}],
//             ['（', {width: 10.7, height: 12, type: 1}],
//             ['）', {width: 10.7, height: 12, type: 1}],
//             ['【', {width: 10.7, height: 12, type: 1}],
//             ['】', {width: 10.7, height: 12, type: 1}],
//             ['Ⅰ', {width: 3.56, height: 12, type: 1}],
//             ['Ⅱ', {width: 6.92, height: 12, type: 1}],
//             [':', {width: 2.97, height: 12, type: 1}],
//             ['：', {width: 10.7, height: 12, type: 1}],
//             [paraEnd, {width: 0, height: 12, type: 1}], ['{', {width: 5.14, height: 12, type: 1}],
//             ['}', {width: 5.14, height: 12, type: 1}],
//             ['☑', {width: 9.22, height: 12, type: 1}],
//             ['☐', {width: 9.22, height: 12, type: 1}],
//             ['⊙', {width: 10.7, height: 12, type: 1}],
//             ['⭘', {width: 10.7, height: 12, type: 1}],
//             ['◯', {width: 11.65, height: 12, type: 1}],
//             ['℃', {width: 10.96, height: 12, type: 1}],
//         ])
//     );

//     // let p2 = performance.now();
//     // console.log(p2 - p1)

//     // let t1 = performance.now();

//     /*** mac fonts  ***/

//     if (isMacOs() === false) {
//         return ;
//     }

//     // tslint:disable-next-line: no-console
//     console.log('set up mac os fonts cache');

//     // as default mac font template
//     const stSong16Template: any = [
//         ['我', {width: 16, height: 16, type: 1}],
//         [' ', {width: 3.5, height: 16, type: 1}],
//         ['0', { width: 7.5, height: 16, type: 1 }],
//         ['1', { width: 7.5, height: 16, type: 1 }],
//         ['2', { width: 7.5, height: 16, type: 1 }],
//         ['3', { width: 7.5, height: 16, type: 1 }],
//         ['4', { width: 7.5, height: 16, type: 1 }],
//         ['5', { width: 7.5, height: 16, type: 1 }],
//         ['6', { width: 7.5, height: 16, type: 1 }],
//         ['7', { width: 7.5, height: 16, type: 1 }],
//         ['8', { width: 7.5, height: 16, type: 1 }],
//         ['9', { width: 7.5, height: 16, type: 1 }],
//         ['a', { width: 8.1, height: 16, type: 1 }],
//         ['b', { width: 8.35, height: 16, type: 1 }],
//         ['c', { width: 6.59, height: 16, type: 1 }],
//         ['d', { width: 8.21, height: 16, type: 1 }],
//         ['e', { width: 6.91, height: 16, type: 1 }],
//         ['f', { width: 5.82, height: 16, type: 1 }],
//         ['g', { width: 7.6, height: 16, type: 1 }],
//         ['h', { width: 8.51, height: 16, type: 1 }],
//         ['i', { width: 4.32, height: 16, type: 1 }],
//         ['j', { width: 3.54, height: 16, type: 1 }],
//         ['k', { width: 7.49, height: 16, type: 1 }],
//         ['l', { width: 4.35, height: 16, type: 1 }],
//         ['m', { width: 12.58, height: 16, type: 1 }],
//         ['n', { width: 8.51, height: 16, type: 1 }],
//         ['o', { width: 8.18, height: 16, type: 1 }],
//         ['p', { width: 8.42, height: 16, type: 1 }],
//         ['q', { width: 8.37, height: 16, type: 1 }],
//         ['r', { width: 5.78, height: 16, type: 1 }],
//         ['s', { width: 5.84, height: 16, type: 1 }],
//         ['t', { width: 5.12, height: 16, type: 1 }],
//         ['u', { width: 8.27, height: 16, type: 1 }],
//         ['v', { width: 7.5, height: 16, type: 1 }],
//         ['w', { width: 10.67, height: 16, type: 1 }],
//         ['x', { width: 7.33, height: 16, type: 1 }],
//         ['y', { width: 6.67, height: 16, type: 1 }],
//         ['z', { width: 6.83, height: 16, type: 1 }],
//         ['A', { width: 10.83, height: 16, type: 1 }],
//         ['B', { width: 9.84, height: 16, type: 1 }],
//         ['C', { width: 10.16, height: 16, type: 1 }],
//         ['D', { width: 12.34, height: 16, type: 1 }],
//         ['E', { width: 10.5, height: 16, type: 1 }],
//         ['F', { width: 9.01, height: 16, type: 1 }],
//         ['G', { width: 12.34, height: 16, type: 1 }],
//         ['H', { width: 12.16, height: 16, type: 1 }],
//         ['I', { width: 5.66, height: 16, type: 1 }],
//         ['J', { width: 5.33, height: 16, type: 1 }],
//         ['K', { width: 11.84, height: 16, type: 1 }],
//         ['L', { width: 9.17, height: 16, type: 1 }],
//         ['M', { width: 13.33, height: 16, type: 1 }],
//         ['N', { width: 12.34, height: 16, type: 1 }],
//         ['O', { width: 12.5, height: 16, type: 1 }],
//         ['P', { width: 9.01, height: 16, type: 1 }],
//         ['Q', { width: 12.34, height: 16, type: 1 }],
//         ['R', { width: 10, height: 16, type: 1 }],
//         ['S', { width: 7.66, height: 16, type: 1 }],
//         ['T', { width: 9.84, height: 16, type: 1 }],
//         ['U', { width: 11.33, height: 16, type: 1 }],
//         ['V', { width: 11.14, height: 16, type: 1 }],
//         ['W', { width: 14.46, height: 16, type: 1 }],
//         ['X', { width: 11.3, height: 16, type: 1 }],
//         ['Y', { width: 10.82, height: 16, type: 1 }],
//         ['Z', { width: 10.5, height: 16, type: 1 }],
//         [',', { width: 3.5, height: 16, type: 1 }],
//         ['.', { width: 3.5, height: 16, type: 1 }],
//         [';', { width: 3.5, height: 16, type: 1 }],
//         ['/', { width: 8, height: 16, type: 1 }],
//         ['?', { width: 5.84, height: 16, type: 1 }],
//         ['(', { width: 4.67, height: 16, type: 1 }],
//         [')', { width: 4.67, height: 16, type: 1 }],
//         ['[', { width: 4.34, height: 16, type: 1 }],
//         [']', { width: 4.34, height: 16, type: 1 }],
//         ['=', { width: 10.67, height: 16, type: 1 }],
//         ['-', { width: 5.01, height: 16, type: 1 }],
//         ['+', { width: 10.67, height: 16, type: 1 }],
//         ['*', { width: 6.83, height: 16, type: 1 }],
//         ['<', { width: 10.67, height: 16, type: 1 }],
//         ['>', { width: 10.67, height: 16, type: 1 }],
//         ['，', { width: 16, height: 16, type: 1 }],
//         ['。', { width: 16, height: 16, type: 1 }],
//         ['；', { width: 16, height: 16, type: 1 }],
//         ['’', { width: 4.1, height: 16, type: 1 }],
//         ['“', { width: 6.34, height: 16, type: 1 }],
//         ['？', { width: 16, height: 16, type: 1 }],
//         ['、', { width: 16, height: 16, type: 1 }],
//         ['（', { width: 16, height: 16, type: 1 }],
//         ['）', { width: 16, height: 16, type: 1 }],
//         ['【', { width: 16, height: 16, type: 1 }],
//         ['】', { width: 16, height: 16, type: 1 }],
//         [paraEnd, { width: 0, height: 16, type: 1 }],
//         ['：', { width: 16, height: 16, type: 1 }],
//         [':', { width: 3.5, height: 16, type: 1 }],
//         ['{', { width: 7.66, height: 16, type: 1 }],
//         ['}', { width: 7.66, height: 16, type: 1 }],
//         ['☑', { width: 13.28, height: 16, type: 1 }],
//         ['☐', { width: 13.28, height: 16, type: 1 }],
//         ['⊙', { width: 16, height: 16, type: 1 }],
//         ['⭘', { width: 14.08, height: 16, type: 1 }],
//         ['◯', { width: 16, height: 16, type: 1 }],
//         ['℃', { width: 16, height: 16, type: 1 }],
//     ];

//     CACHES.set(
//         'STSong16', new Map(stSong16Template)
//     );

//     // tslint:disable-next-line: max-line-length
//     const stSong20 = ['STSong20', [20, 20, 1], [4.38, 20, 1], [9.38, 20, 1], [9.38, 20, 1], [9.38, 20, 1], [9.38, 20, 1], [9.38, 20, 1], [9.38, 20, 1], [9.38, 20, 1], [9.38, 20, 1], [9.38, 20, 1], [9.38, 20, 1], [10.12, 20, 1], [10.44, 20, 1], [8.24, 20, 1], [10.26, 20, 1], [8.64, 20, 1], [7.28, 20, 1], [9.5, 20, 1], [10.64, 20, 1], [5.4, 20, 1], [4.42, 20, 1], [9.36, 20, 1], [5.44, 20, 1], [15.72, 20, 1], [10.64, 20, 1], [10.22, 20, 1], [10.52, 20, 1], [10.46, 20, 1], [7.22, 20, 1], [7.3, 20, 1], [6.4, 20, 1], [10.34, 20, 1], [9.38, 20, 1], [13.34, 20, 1], [9.16, 20, 1], [8.34, 20, 1], [8.54, 20, 1], [13.54, 20, 1], [12.3, 20, 1], [12.7, 20, 1], [15.42, 20, 1], [13.12, 20, 1], [11.26, 20, 1], [15.42, 20, 1], [15.2, 20, 1], [7.08, 20, 1], [6.66, 20, 1], [14.8, 20, 1], [11.46, 20, 1], [16.66, 20, 1], [15.42, 20, 1], [15.62, 20, 1], [11.26, 20, 1], [15.42, 20, 1], [12.5, 20, 1], [9.58, 20, 1], [12.3, 20, 1], [14.16, 20, 1], [13.92, 20, 1], [18.08, 20, 1], [14.12, 20, 1], [13.52, 20, 1], [13.12, 20, 1], [4.38, 20, 1], [4.38, 20, 1], [4.38, 20, 1], [10, 20, 1], [7.3, 20, 1], [5.84, 20, 1], [5.84, 20, 1], [5.42, 20, 1], [5.42, 20, 1], [13.34, 20, 1], [6.26, 20, 1], [13.34, 20, 1], [8.54, 20, 1], [13.34, 20, 1], [13.34, 20, 1], [20, 20, 1], [20, 20, 1], [20, 20, 1], [5.12, 20, 1], [7.92, 20, 1], [20, 20, 1], [20, 20, 1], [20, 20, 1], [20, 20, 1], [20, 20, 1], [20, 20, 1], [13.16, 20, 1], [20, 20, 1], [4.38, 20, 1], [9.58, 20, 1], [9.58, 20, 1], [16.6, 20, 1], [16.6, 20, 1], [20, 20, 1], [17.6, 20, 1], [20, 20, 1], [20, 20, 1]];
//     // tslint:disable-next-line: max-line-length
//     const stSong187 = ['STSong18.7', [18.7, 19, 1], [4.1, 19, 1], [8.77, 19, 1], [8.77, 19, 1], [8.77, 19, 1], [8.77, 19, 1], [8.77, 19, 1], [8.77, 19, 1], [8.77, 19, 1], [8.77, 19, 1], [8.77, 19, 1], [8.77, 19, 1], [9.46, 19, 1], [9.76, 19, 1], [7.7, 19, 1], [9.59, 19, 1], [8.08, 19, 1], [6.81, 19, 1], [8.88, 19, 1], [9.95, 19, 1], [5.05, 19, 1], [4.13, 19, 1], [8.75, 19, 1], [5.09, 19, 1], [14.7, 19, 1], [9.95, 19, 1], [9.56, 19, 1], [9.84, 19, 1], [9.78, 19, 1], [6.75, 19, 1], [6.83, 19, 1], [5.98, 19, 1], [9.67, 19, 1], [8.77, 19, 1], [12.47, 19, 1], [8.56, 19, 1], [7.8, 19, 1], [7.98, 19, 1], [12.66, 19, 1], [11.5, 19, 1], [11.87, 19, 1], [14.42, 19, 1], [12.27, 19, 1], [10.53, 19, 1], [14.42, 19, 1], [14.21, 19, 1], [6.62, 19, 1], [6.23, 19, 1], [13.84, 19, 1], [10.72, 19, 1], [15.58, 19, 1], [14.42, 19, 1], [14.6, 19, 1], [10.53, 19, 1], [14.42, 19, 1], [11.69, 19, 1], [8.96, 19, 1], [11.5, 19, 1], [13.24, 19, 1], [13.02, 19, 1], [16.9, 19, 1], [13.2, 19, 1], [12.64, 19, 1], [12.27, 19, 1], [4.1, 19, 1], [4.1, 19, 1], [4.1, 19, 1], [9.35, 19, 1], [6.83, 19, 1], [5.46, 19, 1], [5.46, 19, 1], [5.07, 19, 1], [5.07, 19, 1], [12.47, 19, 1], [5.85, 19, 1], [12.47, 19, 1], [7.98, 19, 1], [12.47, 19, 1], [12.47, 19, 1], [18.7, 19, 1], [18.7, 19, 1], [18.7, 19, 1], [4.79, 19, 1], [7.41, 19, 1], [18.7, 19, 1], [18.7, 19, 1], [18.7, 19, 1], [18.7, 19, 1], [18.7, 19, 1], [18.7, 19, 1], [12.31, 19, 1], [18.7, 19, 1], [4.1, 19, 1], [8.96, 19, 1], [8.96, 19, 1], [15.52, 19, 1], [15.52, 19, 1], [18.7, 19, 1], [16.25, 19, 1], [18.7, 19, 1], [18.7, 19, 1]];
//     // tslint:disable-next-line: max-line-length
//     const stSong14 = ['STSong14', [14, 14, 1], [3.07, 14, 1], [6.57, 14, 1], [6.57, 14, 1], [6.57, 14, 1], [6.57, 14, 1], [6.57, 14, 1], [6.57, 14, 1], [6.57, 14, 1], [6.57, 14, 1], [6.57, 14, 1], [6.57, 14, 1], [7.08, 14, 1], [7.31, 14, 1], [5.77, 14, 1], [7.18, 14, 1], [6.05, 14, 1], [5.1, 14, 1], [6.65, 14, 1], [7.45, 14, 1], [3.78, 14, 1], [3.09, 14, 1], [6.55, 14, 1], [3.81, 14, 1], [11, 14, 1], [7.45, 14, 1], [7.15, 14, 1], [7.36, 14, 1], [7.32, 14, 1], [5.05, 14, 1], [5.11, 14, 1], [4.48, 14, 1], [7.24, 14, 1], [6.57, 14, 1], [9.34, 14, 1], [6.41, 14, 1], [5.84, 14, 1], [5.98, 14, 1], [9.48, 14, 1], [8.61, 14, 1], [8.89, 14, 1], [10.79, 14, 1], [9.18, 14, 1], [7.88, 14, 1], [10.79, 14, 1], [10.64, 14, 1], [4.96, 14, 1], [4.66, 14, 1], [10.36, 14, 1], [8.02, 14, 1], [11.66, 14, 1], [10.79, 14, 1], [10.93, 14, 1], [7.88, 14, 1], [10.79, 14, 1], [8.75, 14, 1], [6.71, 14, 1], [8.61, 14, 1], [9.91, 14, 1], [9.74, 14, 1], [12.66, 14, 1], [9.88, 14, 1], [9.46, 14, 1], [9.18, 14, 1], [3.07, 14, 1], [3.07, 14, 1], [3.07, 14, 1], [7, 14, 1], [5.11, 14, 1], [4.09, 14, 1], [4.09, 14, 1], [3.79, 14, 1], [3.79, 14, 1], [9.34, 14, 1], [4.38, 14, 1], [9.34, 14, 1], [5.98, 14, 1], [9.34, 14, 1], [9.34, 14, 1], [14, 14, 1], [14, 14, 1], [14, 14, 1], [3.58, 14, 1], [5.54, 14, 1], [14, 14, 1], [14, 14, 1], [14, 14, 1], [14, 14, 1], [14, 14, 1], [14, 14, 1], [9.21, 14, 1], [14, 14, 1], [3.07, 14, 1], [6.71, 14, 1], [6.71, 14, 1], [11.62, 14, 1], [11.62, 14, 1], [14, 14, 1], [12.51, 14, 1], [14, 14, 1], [14, 14, 1]];
//     // tslint:disable-next-line: max-line-length
//     const stSong12 = ['STSong12', [12, 12, 1], [2.63, 12, 1], [5.63, 12, 1], [5.63, 12, 1], [5.63, 12, 1], [5.63, 12, 1], [5.63, 12, 1], [5.63, 12, 1], [5.63, 12, 1], [5.63, 12, 1], [5.63, 12, 1], [5.63, 12, 1], [6.07, 12, 1], [6.26, 12, 1], [4.94, 12, 1], [6.16, 12, 1], [5.18, 12, 1], [4.37, 12, 1], [5.7, 12, 1], [6.38, 12, 1], [3.24, 12, 1], [2.65, 12, 1], [5.62, 12, 1], [3.26, 12, 1], [9.43, 12, 1], [6.38, 12, 1], [6.13, 12, 1], [6.31, 12, 1], [6.28, 12, 1], [4.33, 12, 1], [4.38, 12, 1], [3.84, 12, 1], [6.2, 12, 1], [5.63, 12, 1], [8, 12, 1], [5.5, 12, 1], [5, 12, 1], [5.12, 12, 1], [8.12, 12, 1], [7.38, 12, 1], [7.62, 12, 1], [9.25, 12, 1], [7.87, 12, 1], [6.76, 12, 1], [9.25, 12, 1], [9.12, 12, 1], [4.25, 12, 1], [4, 12, 1], [8.88, 12, 1], [6.88, 12, 1], [10, 12, 1], [9.25, 12, 1], [9.37, 12, 1], [6.76, 12, 1], [9.25, 12, 1], [7.5, 12, 1], [5.75, 12, 1], [7.38, 12, 1], [8.5, 12, 1], [8.35, 12, 1], [10.85, 12, 1], [8.47, 12, 1], [8.11, 12, 1], [7.87, 12, 1], [2.63, 12, 1], [2.63, 12, 1], [2.63, 12, 1], [6, 12, 1], [4.38, 12, 1], [3.5, 12, 1], [3.5, 12, 1], [3.25, 12, 1], [3.25, 12, 1], [8, 12, 1], [3.76, 12, 1], [8, 12, 1], [5.12, 12, 1], [8, 12, 1], [8, 12, 1], [12, 12, 1], [12, 12, 1], [12, 12, 1], [3.07, 12, 1], [4.75, 12, 1], [12, 12, 1], [12, 12, 1], [12, 12, 1], [12, 12, 1], [12, 12, 1], [12, 12, 1], [7.9, 12, 1], [12, 12, 1], [2.63, 12, 1], [5.75, 12, 1], [5.75, 12, 1], [9.96, 12, 1], [9.96, 12, 1], [12, 12, 1], [10.79, 12, 1], [12, 12, 1], [12, 12, 1]];
//     // tslint:disable-next-line: max-line-length
//     const stSong213 = ['STSong21.3', [21.3, 21, 1], [4.66, 21, 1], [9.99, 21, 1], [9.99, 21, 1], [9.99, 21, 1], [9.99, 21, 1], [9.99, 21, 1], [9.99, 21, 1], [9.99, 21, 1], [9.99, 21, 1], [9.99, 21, 1], [9.99, 21, 1], [10.78, 21, 1], [11.12, 21, 1], [8.78, 21, 1], [10.93, 21, 1], [9.2, 21, 1], [7.75, 21, 1], [10.12, 21, 1], [11.33, 21, 1], [5.75, 21, 1], [4.71, 21, 1], [9.97, 21, 1], [5.79, 21, 1], [16.74, 21, 1], [11.33, 21, 1], [10.88, 21, 1], [11.2, 21, 1], [11.14, 21, 1], [7.69, 21, 1], [7.77, 21, 1], [6.82, 21, 1], [11.01, 21, 1], [9.99, 21, 1], [14.21, 21, 1], [9.76, 21, 1], [8.88, 21, 1], [9.1, 21, 1], [14.42, 21, 1], [13.1, 21, 1], [13.53, 21, 1], [16.42, 21, 1], [13.97, 21, 1], [11.99, 21, 1], [16.42, 21, 1], [16.19, 21, 1], [7.54, 21, 1], [7.09, 21, 1], [15.76, 21, 1], [12.2, 21, 1], [17.74, 21, 1], [16.42, 21, 1], [16.64, 21, 1], [11.99, 21, 1], [16.42, 21, 1], [13.31, 21, 1], [10.2, 21, 1], [13.1, 21, 1], [15.08, 21, 1], [14.82, 21, 1], [19.26, 21, 1], [15.04, 21, 1], [14.4, 21, 1], [13.97, 21, 1], [4.66, 21, 1], [4.66, 21, 1], [4.66, 21, 1], [10.65, 21, 1], [7.77, 21, 1], [6.22, 21, 1], [6.22, 21, 1], [5.77, 21, 1], [5.77, 21, 1], [14.21, 21, 1], [6.67, 21, 1], [14.21, 21, 1], [9.1, 21, 1], [14.21, 21, 1], [14.21, 21, 1], [21.3, 21, 1], [21.3, 21, 1], [21.3, 21, 1], [5.45, 21, 1], [8.43, 21, 1], [21.3, 21, 1], [21.3, 21, 1], [21.3, 21, 1], [21.3, 21, 1], [21.3, 21, 1], [21.3, 21, 1], [14.02, 21, 1], [21.3, 21, 1], [4.66, 21, 1], [10.2, 21, 1], [10.2, 21, 1], [17.68, 21, 1], [17.68, 21, 1], [21.3, 21, 1], [18.69, 21, 1], [21.3, 21, 1], [21.3, 21, 1]];
//     // tslint:disable-next-line: max-line-length
//     const stSong24 = ['STSong24', [24, 24, 1], [5.26, 24, 1], [11.26, 24, 1], [11.26, 24, 1], [11.26, 24, 1], [11.26, 24, 1], [11.26, 24, 1], [11.26, 24, 1], [11.26, 24, 1], [11.26, 24, 1], [11.26, 24, 1], [11.26, 24, 1], [12.14, 24, 1], [12.53, 24, 1], [9.89, 24, 1], [12.31, 24, 1], [10.37, 24, 1], [8.74, 24, 1], [11.4, 24, 1], [12.77, 24, 1], [6.48, 24, 1], [5.3, 24, 1], [11.23, 24, 1], [6.53, 24, 1], [18.86, 24, 1], [12.77, 24, 1], [12.26, 24, 1], [12.62, 24, 1], [12.55, 24, 1], [8.66, 24, 1], [8.76, 24, 1], [7.68, 24, 1], [12.41, 24, 1], [11.26, 24, 1], [16.01, 24, 1], [10.99, 24, 1], [10.01, 24, 1], [10.25, 24, 1], [16.25, 24, 1], [14.76, 24, 1], [15.24, 24, 1], [18.5, 24, 1], [15.74, 24, 1], [13.51, 24, 1], [18.5, 24, 1], [18.24, 24, 1], [8.5, 24, 1], [7.99, 24, 1], [17.76, 24, 1], [13.75, 24, 1], [19.99, 24, 1], [18.5, 24, 1], [18.74, 24, 1], [13.51, 24, 1], [18.5, 24, 1], [15, 24, 1], [11.5, 24, 1], [14.76, 24, 1], [16.99, 24, 1], [16.7, 24, 1], [21.7, 24, 1], [16.94, 24, 1], [16.22, 24, 1], [15.74, 24, 1], [5.26, 24, 1], [5.26, 24, 1], [5.26, 24, 1], [12, 24, 1], [8.76, 24, 1], [7.01, 24, 1], [7.01, 24, 1], [6.5, 24, 1], [6.5, 24, 1], [16.01, 24, 1], [7.51, 24, 1], [16.01, 24, 1], [10.25, 24, 1], [16.01, 24, 1], [16.01, 24, 1], [24, 24, 1], [24, 24, 1], [24, 24, 1], [6.14, 24, 1], [9.5, 24, 1], [24, 24, 1], [24, 24, 1], [24, 24, 1], [24, 24, 1], [24, 24, 1], [24, 24, 1], [15.8, 24, 1], [24, 24, 1], [5.26, 24, 1], [11.5, 24, 1], [11.5, 24, 1], [19.92, 24, 1], [19.92, 24, 1], [24, 24, 1], [20.93, 24, 1], [24, 24, 1], [24, 24, 1]];
//     // tslint:disable-next-line: max-line-length
//     const stSong293 = ['STSong29.3', [29.3, 29, 1], [6.42, 29, 1], [13.74, 29, 1], [13.74, 29, 1], [13.74, 29, 1], [13.74, 29, 1], [13.74, 29, 1], [13.74, 29, 1], [13.74, 29, 1], [13.74, 29, 1], [13.74, 29, 1], [13.74, 29, 1], [14.83, 29, 1], [15.29, 29, 1], [12.07, 29, 1], [15.03, 29, 1], [12.66, 29, 1], [10.67, 29, 1], [13.92, 29, 1], [15.59, 29, 1], [7.91, 29, 1], [6.48, 29, 1], [13.71, 29, 1], [7.97, 29, 1], [23.03, 29, 1], [15.59, 29, 1], [14.97, 29, 1], [15.41, 29, 1], [15.32, 29, 1], [10.58, 29, 1], [10.69, 29, 1], [9.38, 29, 1], [15.15, 29, 1], [13.74, 29, 1], [19.54, 29, 1], [13.42, 29, 1], [12.22, 29, 1], [12.51, 29, 1], [19.84, 29, 1], [18.02, 29, 1], [18.61, 29, 1], [22.59, 29, 1], [19.22, 29, 1], [16.5, 29, 1], [22.59, 29, 1], [22.27, 29, 1], [10.37, 29, 1], [9.76, 29, 1], [21.68, 29, 1], [16.79, 29, 1], [24.41, 29, 1], [22.59, 29, 1], [22.88, 29, 1], [16.5, 29, 1], [22.59, 29, 1], [18.31, 29, 1], [14.03, 29, 1], [18.02, 29, 1], [20.74, 29, 1], [20.39, 29, 1], [26.49, 29, 1], [20.69, 29, 1], [19.81, 29, 1], [19.22, 29, 1], [6.42, 29, 1], [6.42, 29, 1], [6.42, 29, 1], [14.65, 29, 1], [10.69, 29, 1], [8.56, 29, 1], [8.56, 29, 1], [7.94, 29, 1], [7.94, 29, 1], [19.54, 29, 1], [9.17, 29, 1], [19.54, 29, 1], [12.51, 29, 1], [19.54, 29, 1], [19.54, 29, 1], [29.3, 29, 1], [29.3, 29, 1], [29.3, 29, 1], [7.5, 29, 1], [11.6, 29, 1], [29.3, 29, 1], [29.3, 29, 1], [29.3, 29, 1], [29.3, 29, 1], [29.3, 29, 1], [29.3, 29, 1], [19.29, 29, 1], [29.3, 29, 1], [6.42, 29, 1], [14.03, 29, 1], [14.03, 29, 1], [24.32, 29, 1], [24.32, 29, 1], [29.3, 29, 1], [25.39, 29, 1], [29.3, 29, 1], [29.3, 29, 1]];
//     // tslint:disable-next-line: max-line-length
//     const stSong32 = ['STSong32', [32, 32, 1], [7.01, 32, 1], [15.01, 32, 1], [15.01, 32, 1], [15.01, 32, 1], [15.01, 32, 1], [15.01, 32, 1], [15.01, 32, 1], [15.01, 32, 1], [15.01, 32, 1], [15.01, 32, 1], [15.01, 32, 1], [16.19, 32, 1], [16.7, 32, 1], [13.18, 32, 1], [16.42, 32, 1], [13.82, 32, 1], [11.65, 32, 1], [15.2, 32, 1], [17.02, 32, 1], [8.64, 32, 1], [7.07, 32, 1], [14.98, 32, 1], [8.7, 32, 1], [25.15, 32, 1], [17.02, 32, 1], [16.35, 32, 1], [16.83, 32, 1], [16.74, 32, 1], [11.55, 32, 1], [11.68, 32, 1], [10.24, 32, 1], [16.54, 32, 1], [15.01, 32, 1], [21.34, 32, 1], [14.66, 32, 1], [13.34, 32, 1], [13.66, 32, 1], [21.66, 32, 1], [19.68, 32, 1], [20.32, 32, 1], [24.67, 32, 1], [20.99, 32, 1], [18.02, 32, 1], [24.67, 32, 1], [24.32, 32, 1], [11.33, 32, 1], [10.66, 32, 1], [23.68, 32, 1], [18.34, 32, 1], [26.66, 32, 1], [24.67, 32, 1], [24.99, 32, 1], [18.02, 32, 1], [24.67, 32, 1], [20, 32, 1], [15.33, 32, 1], [19.68, 32, 1], [22.66, 32, 1], [22.27, 32, 1], [28.93, 32, 1], [22.59, 32, 1], [21.63, 32, 1], [20.99, 32, 1], [7.01, 32, 1], [7.01, 32, 1], [7.01, 32, 1], [16, 32, 1], [11.68, 32, 1], [9.34, 32, 1], [9.34, 32, 1], [8.67, 32, 1], [8.67, 32, 1], [21.34, 32, 1], [10.02, 32, 1], [21.34, 32, 1], [13.66, 32, 1], [21.34, 32, 1], [21.34, 32, 1], [32, 32, 1], [32, 32, 1], [32, 32, 1], [8.19, 32, 1], [12.67, 32, 1], [32, 32, 1], [32, 32, 1], [32, 32, 1], [32, 32, 1], [32, 32, 1], [32, 32, 1], [21.06, 32, 1], [32, 32, 1], [7.01, 32, 1], [15.33, 32, 1], [15.33, 32, 1], [26.56, 32, 1], [26.56, 32, 1], [32, 32, 1], [27.66, 32, 1], [32, 32, 1], [32, 32, 1]];
//     // tslint:disable-next-line: max-line-length
//     const stSong347 = ['STSong34.7', [34.7, 35, 1], [7.6, 35, 1], [16.27, 35, 1], [16.27, 35, 1], [16.27, 35, 1], [16.27, 35, 1], [16.27, 35, 1], [16.27, 35, 1], [16.27, 35, 1], [16.27, 35, 1], [16.27, 35, 1], [16.27, 35, 1], [17.56, 35, 1], [18.11, 35, 1], [14.3, 35, 1], [17.8, 35, 1], [14.99, 35, 1], [12.63, 35, 1], [16.48, 35, 1], [18.46, 35, 1], [9.37, 35, 1], [7.67, 35, 1], [16.24, 35, 1], [9.44, 35, 1], [27.27, 35, 1], [18.46, 35, 1], [17.73, 35, 1], [18.25, 35, 1], [18.15, 35, 1], [12.53, 35, 1], [12.67, 35, 1], [11.1, 35, 1], [17.94, 35, 1], [16.27, 35, 1], [23.14, 35, 1], [15.89, 35, 1], [14.47, 35, 1], [14.82, 35, 1], [23.49, 35, 1], [21.34, 35, 1], [22.03, 35, 1], [26.75, 35, 1], [22.76, 35, 1], [19.54, 35, 1], [26.75, 35, 1], [26.37, 35, 1], [12.28, 35, 1], [11.56, 35, 1], [25.68, 35, 1], [19.88, 35, 1], [28.91, 35, 1], [26.75, 35, 1], [27.1, 35, 1], [19.54, 35, 1], [26.75, 35, 1], [21.69, 35, 1], [16.62, 35, 1], [21.34, 35, 1], [24.57, 35, 1], [24.15, 35, 1], [31.37, 35, 1], [24.5, 35, 1], [23.46, 35, 1], [22.76, 35, 1], [7.6, 35, 1], [7.6, 35, 1], [7.6, 35, 1], [17.35, 35, 1], [12.67, 35, 1], [10.13, 35, 1], [10.13, 35, 1], [9.4, 35, 1], [9.4, 35, 1], [23.14, 35, 1], [10.86, 35, 1], [23.14, 35, 1], [14.82, 35, 1], [23.14, 35, 1], [23.14, 35, 1], [34.7, 35, 1], [34.7, 35, 1], [34.7, 35, 1], [8.88, 35, 1], [13.74, 35, 1], [34.7, 35, 1], [34.7, 35, 1], [34.7, 35, 1], [34.7, 35, 1], [34.7, 35, 1], [34.7, 35, 1], [22.84, 35, 1], [34.7, 35, 1], [7.6, 35, 1], [16.62, 35, 1], [16.62, 35, 1], [28.8, 35, 1], [28.8, 35, 1], [34.7, 35, 1], [29.91, 35, 1], [34.7, 35, 1], [34.7, 35, 1]];
//     // tslint:disable-next-line: max-line-length
//     const stSong48 = ['STSong48', [48, 48, 1], [10.51, 48, 1], [22.51, 48, 1], [22.51, 48, 1], [22.51, 48, 1], [22.51, 48, 1], [22.51, 48, 1], [22.51, 48, 1], [22.51, 48, 1], [22.51, 48, 1], [22.51, 48, 1], [22.51, 48, 1], [24.29, 48, 1], [25.06, 48, 1], [19.78, 48, 1], [24.62, 48, 1], [20.74, 48, 1], [17.47, 48, 1], [22.8, 48, 1], [25.54, 48, 1], [12.96, 48, 1], [10.61, 48, 1], [22.46, 48, 1], [13.06, 48, 1], [37.73, 48, 1], [25.54, 48, 1], [24.53, 48, 1], [25.25, 48, 1], [25.1, 48, 1], [17.33, 48, 1], [17.52, 48, 1], [15.36, 48, 1], [24.82, 48, 1], [22.51, 48, 1], [32.02, 48, 1], [21.98, 48, 1], [20.02, 48, 1], [20.5, 48, 1], [32.5, 48, 1], [29.52, 48, 1], [30.48, 48, 1], [37.01, 48, 1], [31.49, 48, 1], [27.02, 48, 1], [37.01, 48, 1], [36.48, 48, 1], [16.99, 48, 1], [15.98, 48, 1], [35.52, 48, 1], [27.5, 48, 1], [39.98, 48, 1], [37.01, 48, 1], [37.49, 48, 1], [27.02, 48, 1], [37.01, 48, 1], [30, 48, 1], [22.99, 48, 1], [29.52, 48, 1], [33.98, 48, 1], [33.41, 48, 1], [43.39, 48, 1], [33.89, 48, 1], [32.45, 48, 1], [31.49, 48, 1], [10.51, 48, 1], [10.51, 48, 1], [10.51, 48, 1], [24, 48, 1], [17.52, 48, 1], [14.02, 48, 1], [14.02, 48, 1], [13.01, 48, 1], [13.01, 48, 1], [32.02, 48, 1], [15.02, 48, 1], [32.02, 48, 1], [20.5, 48, 1], [32.02, 48, 1], [32.02, 48, 1], [48, 48, 1], [48, 48, 1], [48, 48, 1], [12.29, 48, 1], [19.01, 48, 1], [48, 48, 1], [48, 48, 1], [48, 48, 1], [48, 48, 1], [48, 48, 1], [48, 48, 1], [31.59, 48, 1], [48, 48, 1], [10.51, 48, 1], [22.99, 48, 1], [22.99, 48, 1], [39.84, 48, 1], [39.84, 48, 1], [48, 48, 1], [41.27, 48, 1], [48, 48, 1], [48, 48, 1]];
//     // tslint:disable-next-line: max-line-length
//     const stSong587 = ['STSong58.7', [58.7, 58, 1], [12.86, 58, 1], [27.53, 58, 1], [27.53, 58, 1], [27.53, 58, 1], [27.53, 58, 1], [27.53, 58, 1], [27.53, 58, 1], [27.53, 58, 1], [27.53, 58, 1], [27.53, 58, 1], [27.53, 58, 1], [29.7, 58, 1], [30.64, 58, 1], [24.18, 58, 1], [30.11, 58, 1], [25.36, 58, 1], [21.37, 58, 1], [27.88, 58, 1], [31.23, 58, 1], [15.85, 58, 1], [12.97, 58, 1], [27.47, 58, 1], [15.97, 58, 1], [46.14, 58, 1], [31.23, 58, 1], [30, 58, 1], [30.88, 58, 1], [30.7, 58, 1], [21.19, 58, 1], [21.43, 58, 1], [18.78, 58, 1], [30.35, 58, 1], [27.53, 58, 1], [39.15, 58, 1], [26.88, 58, 1], [24.48, 58, 1], [25.06, 58, 1], [39.74, 58, 1], [36.1, 58, 1], [37.27, 58, 1], [45.26, 58, 1], [38.51, 58, 1], [33.05, 58, 1], [45.26, 58, 1], [44.61, 58, 1], [20.78, 58, 1], [19.55, 58, 1], [43.44, 58, 1], [33.64, 58, 1], [48.9, 58, 1], [45.26, 58, 1], [45.84, 58, 1], [33.05, 58, 1], [45.26, 58, 1], [36.69, 58, 1], [28.12, 58, 1], [36.1, 58, 1], [41.56, 58, 1], [40.86, 58, 1], [53.06, 58, 1], [41.44, 58, 1], [39.68, 58, 1], [38.51, 58, 1], [12.86, 58, 1], [12.86, 58, 1], [12.86, 58, 1], [29.35, 58, 1], [21.43, 58, 1], [17.14, 58, 1], [17.14, 58, 1], [15.91, 58, 1], [15.91, 58, 1], [39.15, 58, 1], [18.37, 58, 1], [39.15, 58, 1], [25.06, 58, 1], [39.15, 58, 1], [39.15, 58, 1], [58.7, 58, 1], [58.7, 58, 1], [58.7, 58, 1], [15.03, 58, 1], [23.25, 58, 1], [58.7, 58, 1], [58.7, 58, 1], [58.7, 58, 1], [58.7, 58, 1], [58.7, 58, 1], [58.7, 58, 1], [38.64, 58, 1], [58.7, 58, 1], [12.86, 58, 1], [28.12, 58, 1], [28.12, 58, 1], [48.73, 58, 1], [48.73, 58, 1], [58.7, 58, 1], [50.39, 58, 1], [58.7, 58, 1], [58.7, 58, 1]];
//     const stSongFonts = [stSong20, stSong187, stSong14, stSong12, stSong213, stSong24, stSong293, stSong32,
//         stSong347, stSong48, stSong587];

//     // tslint:disable-next-line: max-line-length
//     const stHeiti12 = ['STHeiti12', [12, 12, 1], [3.32, 12, 1], [6.65, 12, 1], [6.65, 12, 1], [6.65, 12, 1], [6.65, 12, 1], [6.65, 12, 1], [6.65, 12, 1], [6.65, 12, 1], [6.65, 12, 1], [6.65, 12, 1], [6.65, 12, 1], [8.2, 12, 1], [8.18, 12, 1], [7.76, 12, 1], [8.22, 12, 1], [7.8, 12, 1], [3.77, 12, 1], [8.08, 12, 1], [7.32, 12, 1], [2.4, 12, 1], [2.44, 12, 1], [6.02, 12, 1], [2.4, 12, 1], [11.26, 12, 1], [7.32, 12, 1], [7.86, 12, 1], [8.18, 12, 1], [8.18, 12, 1], [3.61, 12, 1], [4.66, 12, 1], [4.07, 12, 1], [7.3, 12, 1], [6.65, 12, 1], [9.97, 12, 1], [5.76, 12, 1], [6.43, 12, 1], [5.1, 12, 1], [8.88, 12, 1], [6.89, 12, 1], [9.76, 12, 1], [8.93, 12, 1], [6.43, 12, 1], [5.82, 12, 1], [10.46, 12, 1], [8.2, 12, 1], [2.71, 12, 1], [5.78, 12, 1], [7.09, 12, 1], [5.54, 12, 1], [11.03, 12, 1], [8.88, 12, 1], [10.43, 12, 1], [7.1, 12, 1], [10.45, 12, 1], [7.28, 12, 1], [5.98, 12, 1], [5.11, 12, 1], [7.86, 12, 1], [8.42, 12, 1], [11.52, 12, 1], [7.31, 12, 1], [7.1, 12, 1], [5.76, 12, 1], [3.32, 12, 1], [3.32, 12, 1], [3.32, 12, 1], [5.24, 12, 1], [5.94, 12, 1], [4.43, 12, 1], [4.43, 12, 1], [4.21, 12, 1], [4.21, 12, 1], [7.27, 12, 1], [3.98, 12, 1], [7.27, 12, 1], [5.1, 12, 1], [7.27, 12, 1], [7.27, 12, 1], [12, 12, 1], [12, 12, 1], [12, 12, 1], [12, 12, 1], [12, 12, 1], [12, 12, 1], [12, 12, 1], [12, 12, 1], [12, 12, 1], [12, 12, 1], [12, 12, 1], [12, 12, 1], [12, 12, 1], [3.32, 12, 1], [4.21, 12, 1], [4.21, 12, 1], [9.96, 12, 1], [9.96, 12, 1], [12, 12, 1], [10.79, 12, 1], [12, 12, 1], [12, 12, 1]];
//     // tslint:disable-next-line: max-line-length
//     const stHeiti14 = ['STHeiti14', [14, 14, 1], [3.88, 14, 1], [7.76, 14, 1], [7.76, 14, 1], [7.76, 14, 1], [7.76, 14, 1], [7.76, 14, 1], [7.76, 14, 1], [7.76, 14, 1], [7.76, 14, 1], [7.76, 14, 1], [7.76, 14, 1], [9.56, 14, 1], [9.55, 14, 1], [9.06, 14, 1], [9.59, 14, 1], [9.1, 14, 1], [4.4, 14, 1], [9.42, 14, 1], [8.54, 14, 1], [2.8, 14, 1], [2.84, 14, 1], [7.03, 14, 1], [2.8, 14, 1], [13.13, 14, 1], [8.54, 14, 1], [9.17, 14, 1], [9.55, 14, 1], [9.55, 14, 1], [4.21, 14, 1], [5.43, 14, 1], [4.75, 14, 1], [8.51, 14, 1], [7.76, 14, 1], [11.63, 14, 1], [6.72, 14, 1], [7.5, 14, 1], [5.95, 14, 1], [10.36, 14, 1], [8.04, 14, 1], [11.38, 14, 1], [10.42, 14, 1], [7.5, 14, 1], [6.79, 14, 1], [12.21, 14, 1], [9.56, 14, 1], [3.16, 14, 1], [6.75, 14, 1], [8.27, 14, 1], [6.47, 14, 1], [12.87, 14, 1], [10.36, 14, 1], [12.17, 14, 1], [8.29, 14, 1], [12.19, 14, 1], [8.5, 14, 1], [6.97, 14, 1], [5.96, 14, 1], [9.17, 14, 1], [9.83, 14, 1], [13.44, 14, 1], [8.53, 14, 1], [8.29, 14, 1], [6.72, 14, 1], [3.88, 14, 1], [3.88, 14, 1], [3.88, 14, 1], [6.12, 14, 1], [6.93, 14, 1], [5.17, 14, 1], [5.17, 14, 1], [4.91, 14, 1], [4.91, 14, 1], [8.48, 14, 1], [4.65, 14, 1], [8.48, 14, 1], [5.95, 14, 1], [8.48, 14, 1], [8.48, 14, 1], [14, 14, 1], [14, 14, 1], [14, 14, 1], [14, 14, 1], [14, 14, 1], [14, 14, 1], [14, 14, 1], [14, 14, 1], [14, 14, 1], [14, 14, 1], [14, 14, 1], [14, 14, 1], [14, 14, 1], [3.88, 14, 1], [4.91, 14, 1], [4.91, 14, 1], [11.62, 14, 1], [11.62, 14, 1], [14, 14, 1], [12.51, 14, 1], [14, 14, 1], [14, 14, 1]];
//     // tslint:disable-next-line: max-line-length
//     const stHeiti16 = ['STHeiti16', [16, 16, 1], [4.43, 16, 1], [8.86, 16, 1], [8.86, 16, 1], [8.86, 16, 1], [8.86, 16, 1], [8.86, 16, 1], [8.86, 16, 1], [8.86, 16, 1], [8.86, 16, 1], [8.86, 16, 1], [8.86, 16, 1], [10.93, 16, 1], [10.91, 16, 1], [10.35, 16, 1], [10.96, 16, 1], [10.4, 16, 1], [5.02, 16, 1], [10.77, 16, 1], [9.76, 16, 1], [3.2, 16, 1], [3.25, 16, 1], [8.03, 16, 1], [3.2, 16, 1], [15.01, 16, 1], [9.76, 16, 1], [10.48, 16, 1], [10.91, 16, 1], [10.91, 16, 1], [4.82, 16, 1], [6.21, 16, 1], [5.42, 16, 1], [9.73, 16, 1], [8.86, 16, 1], [13.3, 16, 1], [7.68, 16, 1], [8.58, 16, 1], [6.8, 16, 1], [11.84, 16, 1], [9.18, 16, 1], [13.01, 16, 1], [11.9, 16, 1], [8.58, 16, 1], [7.76, 16, 1], [13.95, 16, 1], [10.93, 16, 1], [3.62, 16, 1], [7.71, 16, 1], [9.46, 16, 1], [7.39, 16, 1], [14.7, 16, 1], [11.84, 16, 1], [13.9, 16, 1], [9.47, 16, 1], [13.94, 16, 1], [9.71, 16, 1], [7.97, 16, 1], [6.82, 16, 1], [10.48, 16, 1], [11.23, 16, 1], [15.36, 16, 1], [9.74, 16, 1], [9.47, 16, 1], [7.68, 16, 1], [4.43, 16, 1], [4.43, 16, 1], [4.43, 16, 1], [6.99, 16, 1], [7.92, 16, 1], [5.9, 16, 1], [5.9, 16, 1], [5.62, 16, 1], [5.62, 16, 1], [9.7, 16, 1], [5.31, 16, 1], [9.7, 16, 1], [6.8, 16, 1], [9.7, 16, 1], [9.7, 16, 1], [16, 16, 1], [16, 16, 1], [16, 16, 1], [16, 16, 1], [16, 16, 1], [16, 16, 1], [16, 16, 1], [16, 16, 1], [16, 16, 1], [16, 16, 1], [16, 16, 1], [16, 16, 1], [16, 16, 1], [4.43, 16, 1], [5.62, 16, 1], [5.62, 16, 1], [13.28, 16, 1], [13.28, 16, 1], [16, 16, 1], [14.08, 16, 1], [16, 16, 1], [16, 16, 1]]
//     // tslint:disable-next-line: max-line-length
//     const stHeiti187 = ['STHeiti18.7', [18.7, 19, 1], [5.18, 19, 1], [10.36, 19, 1], [10.36, 19, 1], [10.36, 19, 1], [10.36, 19, 1], [10.36, 19, 1], [10.36, 19, 1], [10.36, 19, 1], [10.36, 19, 1], [10.36, 19, 1], [10.36, 19, 1], [12.77, 19, 1], [12.75, 19, 1], [12.1, 19, 1], [12.81, 19, 1], [12.15, 19, 1], [5.87, 19, 1], [12.59, 19, 1], [11.41, 19, 1], [3.74, 19, 1], [3.8, 19, 1], [9.39, 19, 1], [3.74, 19, 1], [17.54, 19, 1], [11.41, 19, 1], [12.25, 19, 1], [12.75, 19, 1], [12.75, 19, 1], [5.63, 19, 1], [7.26, 19, 1], [6.34, 19, 1], [11.37, 19, 1], [10.36, 19, 1], [15.54, 19, 1], [8.98, 19, 1], [10.02, 19, 1], [7.95, 19, 1], [13.84, 19, 1], [10.73, 19, 1], [15.2, 19, 1], [13.91, 19, 1], [10.02, 19, 1], [9.07, 19, 1], [16.31, 19, 1], [12.77, 19, 1], [4.23, 19, 1], [9.01, 19, 1], [11.05, 19, 1], [8.64, 19, 1], [17.19, 19, 1], [13.84, 19, 1], [16.25, 19, 1], [11.07, 19, 1], [16.29, 19, 1], [11.35, 19, 1], [9.31, 19, 1], [7.97, 19, 1], [12.25, 19, 1], [13.13, 19, 1], [17.95, 19, 1], [11.39, 19, 1], [11.07, 19, 1], [8.98, 19, 1], [5.18, 19, 1], [5.18, 19, 1], [5.18, 19, 1], [8.17, 19, 1], [9.26, 19, 1], [6.9, 19, 1], [6.9, 19, 1], [6.56, 19, 1], [6.56, 19, 1], [11.33, 19, 1], [6.21, 19, 1], [11.33, 19, 1], [7.95, 19, 1], [11.33, 19, 1], [11.33, 19, 1], [18.7, 19, 1], [18.7, 19, 1], [18.7, 19, 1], [18.7, 19, 1], [18.7, 19, 1], [18.7, 19, 1], [18.7, 19, 1], [18.7, 19, 1], [18.7, 19, 1], [18.7, 19, 1], [18.7, 19, 1], [18.7, 19, 1], [18.7, 19, 1], [5.18, 19, 1], [6.56, 19, 1], [6.56, 19, 1], [15.52, 19, 1], [15.52, 19, 1], [18.7, 19, 1], [16.25, 19, 1], [18.7, 19, 1], [18.7, 19, 1]]
//     // tslint:disable-next-line: max-line-length
//     const stHeiti20 = ['STHeiti20', [20, 20, 1], [5.54, 20, 1], [11.08, 20, 1], [11.08, 20, 1], [11.08, 20, 1], [11.08, 20, 1], [11.08, 20, 1], [11.08, 20, 1], [11.08, 20, 1], [11.08, 20, 1], [11.08, 20, 1], [11.08, 20, 1], [13.66, 20, 1], [13.64, 20, 1], [12.94, 20, 1], [13.7, 20, 1], [13, 20, 1], [6.28, 20, 1], [13.46, 20, 1], [12.2, 20, 1], [4, 20, 1], [4.06, 20, 1], [10.04, 20, 1], [4, 20, 1], [18.76, 20, 1], [12.2, 20, 1], [13.1, 20, 1], [13.64, 20, 1], [13.64, 20, 1], [6.02, 20, 1], [7.76, 20, 1], [6.78, 20, 1], [12.16, 20, 1], [11.08, 20, 1], [16.62, 20, 1], [9.6, 20, 1], [10.72, 20, 1], [8.5, 20, 1], [14.8, 20, 1], [11.48, 20, 1], [16.26, 20, 1], [14.88, 20, 1], [10.72, 20, 1], [9.7, 20, 1], [17.44, 20, 1], [13.66, 20, 1], [4.52, 20, 1], [9.64, 20, 1], [11.82, 20, 1], [9.24, 20, 1], [18.38, 20, 1], [14.8, 20, 1], [17.38, 20, 1], [11.84, 20, 1], [17.42, 20, 1], [12.14, 20, 1], [9.96, 20, 1], [8.52, 20, 1], [13.1, 20, 1], [14.04, 20, 1], [19.2, 20, 1], [12.18, 20, 1], [11.84, 20, 1], [9.6, 20, 1], [5.54, 20, 1], [5.54, 20, 1], [5.54, 20, 1], [8.74, 20, 1], [9.9, 20, 1], [7.38, 20, 1], [7.38, 20, 1], [7.02, 20, 1], [7.02, 20, 1], [12.12, 20, 1], [6.64, 20, 1], [12.12, 20, 1], [8.5, 20, 1], [12.12, 20, 1], [12.12, 20, 1], [20, 20, 1], [20, 20, 1], [20, 20, 1], [20, 20, 1], [20, 20, 1], [20, 20, 1], [20, 20, 1], [20, 20, 1], [20, 20, 1], [20, 20, 1], [20, 20, 1], [20, 20, 1], [20, 20, 1], [5.54, 20, 1], [7.02, 20, 1], [7.02, 20, 1], [16.6, 20, 1], [16.6, 20, 1], [20, 20, 1], [17.6, 20, 1], [20, 20, 1], [20, 20, 1]]
//     // tslint:disable-next-line: max-line-length
//     const stHeiti213 = ['STHeiti21.3', [21.3, 21, 1], [5.9, 21, 1], [11.8, 21, 1], [11.8, 21, 1], [11.8, 21, 1], [11.8, 21, 1], [11.8, 21, 1], [11.8, 21, 1], [11.8, 21, 1], [11.8, 21, 1], [11.8, 21, 1], [11.8, 21, 1], [14.55, 21, 1], [14.53, 21, 1], [13.78, 21, 1], [14.59, 21, 1], [13.84, 21, 1], [6.69, 21, 1], [14.33, 21, 1], [12.99, 21, 1], [4.26, 21, 1], [4.32, 21, 1], [10.69, 21, 1], [4.26, 21, 1], [19.98, 21, 1], [12.99, 21, 1], [13.95, 21, 1], [14.53, 21, 1], [14.53, 21, 1], [6.41, 21, 1], [8.26, 21, 1], [7.22, 21, 1], [12.95, 21, 1], [11.8, 21, 1], [17.7, 21, 1], [10.22, 21, 1], [11.42, 21, 1], [9.05, 21, 1], [15.76, 21, 1], [12.23, 21, 1], [17.32, 21, 1], [15.85, 21, 1], [11.42, 21, 1], [10.33, 21, 1], [18.57, 21, 1], [14.55, 21, 1], [4.81, 21, 1], [10.27, 21, 1], [12.59, 21, 1], [9.84, 21, 1], [19.57, 21, 1], [15.76, 21, 1], [18.51, 21, 1], [12.61, 21, 1], [18.55, 21, 1], [12.93, 21, 1], [10.61, 21, 1], [9.07, 21, 1], [13.95, 21, 1], [14.95, 21, 1], [20.45, 21, 1], [12.97, 21, 1], [12.61, 21, 1], [10.22, 21, 1], [5.9, 21, 1], [5.9, 21, 1], [5.9, 21, 1], [9.31, 21, 1], [10.54, 21, 1], [7.86, 21, 1], [7.86, 21, 1], [7.48, 21, 1], [7.48, 21, 1], [12.91, 21, 1], [7.07, 21, 1], [12.91, 21, 1], [9.05, 21, 1], [12.91, 21, 1], [12.91, 21, 1], [21.3, 21, 1], [21.3, 21, 1], [21.3, 21, 1], [21.3, 21, 1], [21.3, 21, 1], [21.3, 21, 1], [21.3, 21, 1], [21.3, 21, 1], [21.3, 21, 1], [21.3, 21, 1], [21.3, 21, 1], [21.3, 21, 1], [21.3, 21, 1], [5.9, 21, 1], [7.48, 21, 1], [7.48, 21, 1], [17.68, 21, 1], [17.68, 21, 1], [21.3, 21, 1], [18.69, 21, 1], [21.3, 21, 1], [21.3, 21, 1]]
//     // tslint:disable-next-line: max-line-length
//     const stHeiti24 = ['STHeiti24', [24, 24, 1], [6.65, 24, 1], [13.3, 24, 1], [13.3, 24, 1], [13.3, 24, 1], [13.3, 24, 1], [13.3, 24, 1], [13.3, 24, 1], [13.3, 24, 1], [13.3, 24, 1], [13.3, 24, 1], [13.3, 24, 1], [16.39, 24, 1], [16.37, 24, 1], [15.53, 24, 1], [16.44, 24, 1], [15.6, 24, 1], [7.54, 24, 1], [16.15, 24, 1], [14.64, 24, 1], [4.8, 24, 1], [4.87, 24, 1], [12.05, 24, 1], [4.8, 24, 1], [22.51, 24, 1], [14.64, 24, 1], [15.72, 24, 1], [16.37, 24, 1], [16.37, 24, 1], [7.22, 24, 1], [9.31, 24, 1], [8.14, 24, 1], [14.59, 24, 1], [13.3, 24, 1], [19.94, 24, 1], [11.52, 24, 1], [12.86, 24, 1], [10.2, 24, 1], [17.76, 24, 1], [13.78, 24, 1], [19.51, 24, 1], [17.86, 24, 1], [12.86, 24, 1], [11.64, 24, 1], [20.93, 24, 1], [16.39, 24, 1], [5.42, 24, 1], [11.57, 24, 1], [14.18, 24, 1], [11.09, 24, 1], [22.06, 24, 1], [17.76, 24, 1], [20.86, 24, 1], [14.21, 24, 1], [20.9, 24, 1], [14.57, 24, 1], [11.95, 24, 1], [10.22, 24, 1], [15.72, 24, 1], [16.85, 24, 1], [23.04, 24, 1], [14.62, 24, 1], [14.21, 24, 1], [11.52, 24, 1], [6.65, 24, 1], [6.65, 24, 1], [6.65, 24, 1], [10.49, 24, 1], [11.88, 24, 1], [8.86, 24, 1], [8.86, 24, 1], [8.42, 24, 1], [8.42, 24, 1], [14.54, 24, 1], [7.97, 24, 1], [14.54, 24, 1], [10.2, 24, 1], [14.54, 24, 1], [14.54, 24, 1], [24, 24, 1], [24, 24, 1], [24, 24, 1], [24, 24, 1], [24, 24, 1], [24, 24, 1], [24, 24, 1], [24, 24, 1], [24, 24, 1], [24, 24, 1], [24, 24, 1], [24, 24, 1], [24, 24, 1], [6.65, 24, 1], [8.42, 24, 1], [8.42, 24, 1], [19.92, 24, 1], [19.92, 24, 1], [24, 24, 1], [20.93, 24, 1], [24, 24, 1], [24, 24, 1]]
//     // tslint:disable-next-line: max-line-length
//     const stHeiti293 = ['STHeiti29.3', [29.3, 29, 1], [8.12, 29, 1], [16.23, 29, 1], [16.23, 29, 1], [16.23, 29, 1], [16.23, 29, 1], [16.23, 29, 1], [16.23, 29, 1], [16.23, 29, 1], [16.23, 29, 1], [16.23, 29, 1], [16.23, 29, 1], [20.01, 29, 1], [19.98, 29, 1], [18.96, 29, 1], [20.07, 29, 1], [19.04, 29, 1], [9.2, 29, 1], [19.72, 29, 1], [17.87, 29, 1], [5.86, 29, 1], [5.95, 29, 1], [14.71, 29, 1], [5.86, 29, 1], [27.48, 29, 1], [17.87, 29, 1], [19.19, 29, 1], [19.98, 29, 1], [19.98, 29, 1], [8.82, 29, 1], [11.37, 29, 1], [9.93, 29, 1], [17.81, 29, 1], [16.23, 29, 1], [24.35, 29, 1], [14.06, 29, 1], [15.7, 29, 1], [12.45, 29, 1], [21.68, 29, 1], [16.82, 29, 1], [23.82, 29, 1], [21.8, 29, 1], [15.7, 29, 1], [14.21, 29, 1], [25.55, 29, 1], [20.01, 29, 1], [6.62, 29, 1], [14.12, 29, 1], [17.32, 29, 1], [13.54, 29, 1], [26.93, 29, 1], [21.68, 29, 1], [25.46, 29, 1], [17.35, 29, 1], [25.52, 29, 1], [17.79, 29, 1], [14.59, 29, 1], [12.48, 29, 1], [19.19, 29, 1], [20.57, 29, 1], [28.13, 29, 1], [17.84, 29, 1], [17.35, 29, 1], [14.06, 29, 1], [8.12, 29, 1], [8.12, 29, 1], [8.12, 29, 1], [12.8, 29, 1], [14.5, 29, 1], [10.81, 29, 1], [10.81, 29, 1], [10.28, 29, 1], [10.28, 29, 1], [17.76, 29, 1], [9.73, 29, 1], [17.76, 29, 1], [12.45, 29, 1], [17.76, 29, 1], [17.76, 29, 1], [29.3, 29, 1], [29.3, 29, 1], [29.3, 29, 1], [29.3, 29, 1], [29.3, 29, 1], [29.3, 29, 1], [29.3, 29, 1], [29.3, 29, 1], [29.3, 29, 1], [29.3, 29, 1], [29.3, 29, 1], [29.3, 29, 1], [29.3, 29, 1], [8.12, 29, 1], [10.28, 29, 1], [10.28, 29, 1], [24.32, 29, 1], [24.32, 29, 1], [29.3, 29, 1], [25.39, 29, 1], [29.3, 29, 1], [29.3, 29, 1]]
//     // tslint:disable-next-line: max-line-length
//     const stHeiti32 = ['STHeiti32', [32, 32, 1], [8.86, 32, 1], [17.73, 32, 1], [17.73, 32, 1], [17.73, 32, 1], [17.73, 32, 1], [17.73, 32, 1], [17.73, 32, 1], [17.73, 32, 1], [17.73, 32, 1], [17.73, 32, 1], [17.73, 32, 1], [21.86, 32, 1], [21.82, 32, 1], [20.7, 32, 1], [21.92, 32, 1], [20.8, 32, 1], [10.05, 32, 1], [21.54, 32, 1], [19.52, 32, 1], [6.4, 32, 1], [6.5, 32, 1], [16.06, 32, 1], [6.4, 32, 1], [30.02, 32, 1], [19.52, 32, 1], [20.96, 32, 1], [21.82, 32, 1], [21.82, 32, 1], [9.63, 32, 1], [12.42, 32, 1], [10.85, 32, 1], [19.46, 32, 1], [17.73, 32, 1], [26.59, 32, 1], [15.36, 32, 1], [17.15, 32, 1], [13.6, 32, 1], [23.68, 32, 1], [18.37, 32, 1], [26.02, 32, 1], [23.81, 32, 1], [17.15, 32, 1], [15.52, 32, 1], [27.9, 32, 1], [21.86, 32, 1], [7.23, 32, 1], [15.42, 32, 1], [18.91, 32, 1], [14.78, 32, 1], [29.41, 32, 1], [23.68, 32, 1], [27.81, 32, 1], [18.94, 32, 1], [27.87, 32, 1], [19.42, 32, 1], [15.94, 32, 1], [13.63, 32, 1], [20.96, 32, 1], [22.46, 32, 1], [30.72, 32, 1], [19.49, 32, 1], [18.94, 32, 1], [15.36, 32, 1], [8.86, 32, 1], [8.86, 32, 1], [8.86, 32, 1], [13.98, 32, 1], [15.84, 32, 1], [11.81, 32, 1], [11.81, 32, 1], [11.23, 32, 1], [11.23, 32, 1], [19.39, 32, 1], [10.62, 32, 1], [19.39, 32, 1], [13.6, 32, 1], [19.39, 32, 1], [19.39, 32, 1], [32, 32, 1], [32, 32, 1], [32, 32, 1], [32, 32, 1], [32, 32, 1], [32, 32, 1], [32, 32, 1], [32, 32, 1], [32, 32, 1], [32, 32, 1], [32, 32, 1], [32, 32, 1], [32, 32, 1], [8.86, 32, 1], [11.23, 32, 1], [11.23, 32, 1], [26.56, 32, 1], [26.56, 32, 1], [32, 32, 1], [27.66, 32, 1], [32, 32, 1], [32, 32, 1]]
//     // tslint:disable-next-line: max-line-length
//     const stHeiti347 = ['STHeiti34.7', [34.7, 35, 1], [9.61, 35, 1], [19.22, 35, 1], [19.22, 35, 1], [19.22, 35, 1], [19.22, 35, 1], [19.22, 35, 1], [19.22, 35, 1], [19.22, 35, 1], [19.22, 35, 1], [19.22, 35, 1], [19.22, 35, 1], [23.7, 35, 1], [23.67, 35, 1], [22.45, 35, 1], [23.77, 35, 1], [22.55, 35, 1], [10.9, 35, 1], [23.35, 35, 1], [21.17, 35, 1], [6.94, 35, 1], [7.04, 35, 1], [17.42, 35, 1], [6.94, 35, 1], [32.55, 35, 1], [21.17, 35, 1], [22.73, 35, 1], [23.67, 35, 1], [23.67, 35, 1], [10.44, 35, 1], [13.46, 35, 1], [11.76, 35, 1], [21.1, 35, 1], [19.22, 35, 1], [28.84, 35, 1], [16.66, 35, 1], [18.6, 35, 1], [14.75, 35, 1], [25.68, 35, 1], [19.92, 35, 1], [28.21, 35, 1], [25.82, 35, 1], [18.6, 35, 1], [16.83, 35, 1], [30.26, 35, 1], [23.7, 35, 1], [7.84, 35, 1], [16.73, 35, 1], [20.51, 35, 1], [16.03, 35, 1], [31.89, 35, 1], [25.68, 35, 1], [30.15, 35, 1], [20.54, 35, 1], [30.22, 35, 1], [21.06, 35, 1], [17.28, 35, 1], [14.78, 35, 1], [22.73, 35, 1], [24.36, 35, 1], [33.31, 35, 1], [21.13, 35, 1], [20.54, 35, 1], [16.66, 35, 1], [9.61, 35, 1], [9.61, 35, 1], [9.61, 35, 1], [15.16, 35, 1], [17.18, 35, 1], [12.8, 35, 1], [12.8, 35, 1], [12.18, 35, 1], [12.18, 35, 1], [21.03, 35, 1], [11.52, 35, 1], [21.03, 35, 1], [14.75, 35, 1], [21.03, 35, 1], [21.03, 35, 1], [34.7, 35, 1], [34.7, 35, 1], [34.7, 35, 1], [34.7, 35, 1], [34.7, 35, 1], [34.7, 35, 1], [34.7, 35, 1], [34.7, 35, 1], [34.7, 35, 1], [34.7, 35, 1], [34.7, 35, 1], [34.7, 35, 1], [34.7, 35, 1], [9.61, 35, 1], [12.18, 35, 1], [12.18, 35, 1], [28.8, 35, 1], [28.8, 35, 1], [34.7, 35, 1], [29.91, 35, 1], [34.7, 35, 1], [34.7, 35, 1]]
//     // tslint:disable-next-line: max-line-length
//     const stHeiti48 = ['STHeiti48', [48, 48, 1], [13.3, 48, 1], [26.59, 48, 1], [26.59, 48, 1], [26.59, 48, 1], [26.59, 48, 1], [26.59, 48, 1], [26.59, 48, 1], [26.59, 48, 1], [26.59, 48, 1], [26.59, 48, 1], [26.59, 48, 1], [32.78, 48, 1], [32.74, 48, 1], [31.06, 48, 1], [32.88, 48, 1], [31.2, 48, 1], [15.07, 48, 1], [32.3, 48, 1], [29.28, 48, 1], [9.6, 48, 1], [9.74, 48, 1], [24.1, 48, 1], [9.6, 48, 1], [45.02, 48, 1], [29.28, 48, 1], [31.44, 48, 1], [32.74, 48, 1], [32.74, 48, 1], [14.45, 48, 1], [18.62, 48, 1], [16.27, 48, 1], [29.18, 48, 1], [26.59, 48, 1], [39.89, 48, 1], [23.04, 48, 1], [25.73, 48, 1], [20.4, 48, 1], [35.52, 48, 1], [27.55, 48, 1], [39.02, 48, 1], [35.71, 48, 1], [25.73, 48, 1], [23.28, 48, 1], [41.86, 48, 1], [32.78, 48, 1], [10.85, 48, 1], [23.14, 48, 1], [28.37, 48, 1], [22.18, 48, 1], [44.11, 48, 1], [35.52, 48, 1], [41.71, 48, 1], [28.42, 48, 1], [41.81, 48, 1], [29.14, 48, 1], [23.9, 48, 1], [20.45, 48, 1], [31.44, 48, 1], [33.7, 48, 1], [46.08, 48, 1], [29.23, 48, 1], [28.42, 48, 1], [23.04, 48, 1], [13.3, 48, 1], [13.3, 48, 1], [13.3, 48, 1], [20.98, 48, 1], [23.76, 48, 1], [17.71, 48, 1], [17.71, 48, 1], [16.85, 48, 1], [16.85, 48, 1], [29.09, 48, 1], [15.94, 48, 1], [29.09, 48, 1], [20.4, 48, 1], [29.09, 48, 1], [29.09, 48, 1], [48, 48, 1], [48, 48, 1], [48, 48, 1], [48, 48, 1], [48, 48, 1], [48, 48, 1], [48, 48, 1], [48, 48, 1], [48, 48, 1], [48, 48, 1], [48, 48, 1], [48, 48, 1], [48, 48, 1], [13.3, 48, 1], [16.85, 48, 1], [16.85, 48, 1], [39.84, 48, 1], [39.84, 48, 1], [48, 48, 1], [41.27, 48, 1], [48, 48, 1], [48, 48, 1]]
//     // tslint:disable-next-line: max-line-length
//     const stHeiti587 = ['STHeiti58.7', [58.7, 58, 1], [16.26, 58, 1], [32.52, 58, 1], [32.52, 58, 1], [32.52, 58, 1], [32.52, 58, 1], [32.52, 58, 1], [32.52, 58, 1], [32.52, 58, 1], [32.52, 58, 1], [32.52, 58, 1], [32.52, 58, 1], [40.09, 58, 1], [40.03, 58, 1], [37.98, 58, 1], [40.21, 58, 1], [38.15, 58, 1], [18.43, 58, 1], [39.51, 58, 1], [35.81, 58, 1], [11.74, 58, 1], [11.92, 58, 1], [29.47, 58, 1], [11.74, 58, 1], [55.06, 58, 1], [35.81, 58, 1], [38.45, 58, 1], [40.03, 58, 1], [40.03, 58, 1], [17.67, 58, 1], [22.78, 58, 1], [19.9, 58, 1], [35.69, 58, 1], [32.52, 58, 1], [48.78, 58, 1], [28.18, 58, 1], [31.46, 58, 1], [24.95, 58, 1], [43.44, 58, 1], [33.69, 58, 1], [47.72, 58, 1], [43.67, 58, 1], [31.46, 58, 1], [28.47, 58, 1], [51.19, 58, 1], [40.09, 58, 1], [13.27, 58, 1], [28.29, 58, 1], [34.69, 58, 1], [27.12, 58, 1], [53.95, 58, 1], [43.44, 58, 1], [51.01, 58, 1], [34.75, 58, 1], [51.13, 58, 1], [35.63, 58, 1], [29.23, 58, 1], [25.01, 58, 1], [38.45, 58, 1], [41.21, 58, 1], [56.35, 58, 1], [35.75, 58, 1], [34.75, 58, 1], [28.18, 58, 1], [16.26, 58, 1], [16.26, 58, 1], [16.26, 58, 1], [25.65, 58, 1], [29.06, 58, 1], [21.66, 58, 1], [21.66, 58, 1], [20.6, 58, 1], [20.6, 58, 1], [35.57, 58, 1], [19.49, 58, 1], [35.57, 58, 1], [24.95, 58, 1], [35.57, 58, 1], [35.57, 58, 1], [58.7, 58, 1], [58.7, 58, 1], [58.7, 58, 1], [58.7, 58, 1], [58.7, 58, 1], [58.7, 58, 1], [58.7, 58, 1], [58.7, 58, 1], [58.7, 58, 1], [58.7, 58, 1], [58.7, 58, 1], [58.7, 58, 1], [58.7, 58, 1], [16.26, 58, 1], [20.6, 58, 1], [20.6, 58, 1], [48.73, 58, 1], [48.73, 58, 1], [58.7, 58, 1], [50.39, 58, 1], [58.7, 58, 1], [58.7, 58, 1]]
//     const stHeitiFonts = [stHeiti12, stHeiti14, stHeiti16, stHeiti187, stHeiti20, stHeiti213, stHeiti24,
//         stHeiti293, stHeiti32, stHeiti347, stHeiti48, stHeiti587];

//     // tslint:disable-next-line: max-line-length
//     const fangSong12 = ['FangSong_GB231212', [12, 12, 1], [6, 12, 1], [6, 12, 1], [6, 12, 1], [6, 12, 1], [6, 12, 1], [6, 12, 1], [6, 12, 1], [6, 12, 1], [6, 12, 1], [6, 12, 1], [6, 12, 1], [6, 12, 1], [6, 12, 1], [6, 12, 1], [6, 12, 1], [6, 12, 1], [6, 12, 1], [6, 12, 1], [6, 12, 1], [6, 12, 1], [6, 12, 1], [6, 12, 1], [6, 12, 1], [6, 12, 1], [6, 12, 1], [6, 12, 1], [6, 12, 1], [6, 12, 1], [6, 12, 1], [6, 12, 1], [6, 12, 1], [6, 12, 1], [6, 12, 1], [6, 12, 1], [6, 12, 1], [6, 12, 1], [6, 12, 1], [6, 12, 1], [6, 12, 1], [6, 12, 1], [6, 12, 1], [6, 12, 1], [6, 12, 1], [6, 12, 1], [6, 12, 1], [6, 12, 1], [6, 12, 1], [6, 12, 1], [6, 12, 1], [6, 12, 1], [6, 12, 1], [6, 12, 1], [6, 12, 1], [6, 12, 1], [6, 12, 1], [6, 12, 1], [6, 12, 1], [6, 12, 1], [6, 12, 1], [6, 12, 1], [6, 12, 1], [6, 12, 1], [6, 12, 1], [6, 12, 1], [6, 12, 1], [6, 12, 1], [6, 12, 1], [6, 12, 1], [6, 12, 1], [6, 12, 1], [6, 12, 1], [6, 12, 1], [6, 12, 1], [6, 12, 1], [6, 12, 1], [6, 12, 1], [6, 12, 1], [6, 12, 1], [12, 12, 1], [12, 12, 1], [12, 12, 1], [12, 12, 1], [12, 12, 1], [12, 12, 1], [12, 12, 1], [12, 12, 1], [12, 12, 1], [12, 12, 1], [12, 12, 1], [12, 12, 1], [12, 12, 1], [6, 12, 1], [6, 12, 1], [6, 12, 1], [9.96, 12, 1], [9.96, 12, 1], [12, 12, 1], [10.79, 12, 1], [12, 12, 1], [12, 12, 1]];
//     // tslint:disable-next-line: max-line-length
//     const fangSong14 = ['FangSong_GB231214', [14, 14, 1], [7, 14, 1], [7, 14, 1], [7, 14, 1], [7, 14, 1], [7, 14, 1], [7, 14, 1], [7, 14, 1], [7, 14, 1], [7, 14, 1], [7, 14, 1], [7, 14, 1], [7, 14, 1], [7, 14, 1], [7, 14, 1], [7, 14, 1], [7, 14, 1], [7, 14, 1], [7, 14, 1], [7, 14, 1], [7, 14, 1], [7, 14, 1], [7, 14, 1], [7, 14, 1], [7, 14, 1], [7, 14, 1], [7, 14, 1], [7, 14, 1], [7, 14, 1], [7, 14, 1], [7, 14, 1], [7, 14, 1], [7, 14, 1], [7, 14, 1], [7, 14, 1], [7, 14, 1], [7, 14, 1], [7, 14, 1], [7, 14, 1], [7, 14, 1], [7, 14, 1], [7, 14, 1], [7, 14, 1], [7, 14, 1], [7, 14, 1], [7, 14, 1], [7, 14, 1], [7, 14, 1], [7, 14, 1], [7, 14, 1], [7, 14, 1], [7, 14, 1], [7, 14, 1], [7, 14, 1], [7, 14, 1], [7, 14, 1], [7, 14, 1], [7, 14, 1], [7, 14, 1], [7, 14, 1], [7, 14, 1], [7, 14, 1], [7, 14, 1], [7, 14, 1], [7, 14, 1], [7, 14, 1], [7, 14, 1], [7, 14, 1], [7, 14, 1], [7, 14, 1], [7, 14, 1], [7, 14, 1], [7, 14, 1], [7, 14, 1], [7, 14, 1], [7, 14, 1], [7, 14, 1], [7, 14, 1], [7, 14, 1], [14, 14, 1], [14, 14, 1], [14, 14, 1], [14, 14, 1], [14, 14, 1], [14, 14, 1], [14, 14, 1], [14, 14, 1], [14, 14, 1], [14, 14, 1], [14, 14, 1], [14, 14, 1], [14, 14, 1], [7, 14, 1], [7, 14, 1], [7, 14, 1], [11.62, 14, 1], [11.62, 14, 1], [14, 14, 1], [12.51, 14, 1], [14, 14, 1], [14, 14, 1]];
//     // tslint:disable-next-line: max-line-length
//     const fangSong16 = ['FangSong_GB231216', [16, 16, 1], [8, 16, 1], [8, 16, 1], [8, 16, 1], [8, 16, 1], [8, 16, 1], [8, 16, 1], [8, 16, 1], [8, 16, 1], [8, 16, 1], [8, 16, 1], [8, 16, 1], [8, 16, 1], [8, 16, 1], [8, 16, 1], [8, 16, 1], [8, 16, 1], [8, 16, 1], [8, 16, 1], [8, 16, 1], [8, 16, 1], [8, 16, 1], [8, 16, 1], [8, 16, 1], [8, 16, 1], [8, 16, 1], [8, 16, 1], [8, 16, 1], [8, 16, 1], [8, 16, 1], [8, 16, 1], [8, 16, 1], [8, 16, 1], [8, 16, 1], [8, 16, 1], [8, 16, 1], [8, 16, 1], [8, 16, 1], [8, 16, 1], [8, 16, 1], [8, 16, 1], [8, 16, 1], [8, 16, 1], [8, 16, 1], [8, 16, 1], [8, 16, 1], [8, 16, 1], [8, 16, 1], [8, 16, 1], [8, 16, 1], [8, 16, 1], [8, 16, 1], [8, 16, 1], [8, 16, 1], [8, 16, 1], [8, 16, 1], [8, 16, 1], [8, 16, 1], [8, 16, 1], [8, 16, 1], [8, 16, 1], [8, 16, 1], [8, 16, 1], [8, 16, 1], [8, 16, 1], [8, 16, 1], [8, 16, 1], [8, 16, 1], [8, 16, 1], [8, 16, 1], [8, 16, 1], [8, 16, 1], [8, 16, 1], [8, 16, 1], [8, 16, 1], [8, 16, 1], [8, 16, 1], [8, 16, 1], [8, 16, 1], [16, 16, 1], [16, 16, 1], [16, 16, 1], [16, 16, 1], [16, 16, 1], [16, 16, 1], [16, 16, 1], [16, 16, 1], [16, 16, 1], [16, 16, 1], [16, 16, 1], [16, 16, 1], [16, 16, 1], [8, 16, 1], [8, 16, 1], [8, 16, 1], [13.28, 16, 1], [13.28, 16, 1], [16, 16, 1], [14.08, 16, 1], [16, 16, 1], [16, 16, 1]];
//     // tslint:disable-next-line: max-line-length
//     const fangSong187 = ['FangSong_GB231218.7', [18.7, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [18.7, 19, 1], [18.7, 19, 1], [18.7, 19, 1], [18.7, 19, 1], [18.7, 19, 1], [18.7, 19, 1], [18.7, 19, 1], [18.7, 19, 1], [18.7, 19, 1], [18.7, 19, 1], [18.7, 19, 1], [18.7, 19, 1], [18.7, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [9.35, 19, 1], [15.52, 19, 1], [15.52, 19, 1], [18.7, 19, 1], [16.25, 19, 1], [18.7, 19, 1], [18.7, 19, 1]];
//     // tslint:disable-next-line: max-line-length
//     const fangSong20 = ['FangSong_GB231220', [20, 20, 1], [10, 20, 1], [10, 20, 1], [10, 20, 1], [10, 20, 1], [10, 20, 1], [10, 20, 1], [10, 20, 1], [10, 20, 1], [10, 20, 1], [10, 20, 1], [10, 20, 1], [10, 20, 1], [10, 20, 1], [10, 20, 1], [10, 20, 1], [10, 20, 1], [10, 20, 1], [10, 20, 1], [10, 20, 1], [10, 20, 1], [10, 20, 1], [10, 20, 1], [10, 20, 1], [10, 20, 1], [10, 20, 1], [10, 20, 1], [10, 20, 1], [10, 20, 1], [10, 20, 1], [10, 20, 1], [10, 20, 1], [10, 20, 1], [10, 20, 1], [10, 20, 1], [10, 20, 1], [10, 20, 1], [10, 20, 1], [10, 20, 1], [10, 20, 1], [10, 20, 1], [10, 20, 1], [10, 20, 1], [10, 20, 1], [10, 20, 1], [10, 20, 1], [10, 20, 1], [10, 20, 1], [10, 20, 1], [10, 20, 1], [10, 20, 1], [10, 20, 1], [10, 20, 1], [10, 20, 1], [10, 20, 1], [10, 20, 1], [10, 20, 1], [10, 20, 1], [10, 20, 1], [10, 20, 1], [10, 20, 1], [10, 20, 1], [10, 20, 1], [10, 20, 1], [10, 20, 1], [10, 20, 1], [10, 20, 1], [10, 20, 1], [10, 20, 1], [10, 20, 1], [10, 20, 1], [10, 20, 1], [10, 20, 1], [10, 20, 1], [10, 20, 1], [10, 20, 1], [10, 20, 1], [10, 20, 1], [10, 20, 1], [20, 20, 1], [20, 20, 1], [20, 20, 1], [20, 20, 1], [20, 20, 1], [20, 20, 1], [20, 20, 1], [20, 20, 1], [20, 20, 1], [20, 20, 1], [20, 20, 1], [20, 20, 1], [20, 20, 1], [10, 20, 1], [10, 20, 1], [10, 20, 1], [16.6, 20, 1], [16.6, 20, 1], [20, 20, 1], [17.6, 20, 1], [20, 20, 1], [20, 20, 1]];
//     // tslint:disable-next-line: max-line-length
//     const fangSong213 = ['FangSong_GB231221.3', [21.3, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [21.3, 21, 1], [21.3, 21, 1], [21.3, 21, 1], [21.3, 21, 1], [21.3, 21, 1], [21.3, 21, 1], [21.3, 21, 1], [21.3, 21, 1], [21.3, 21, 1], [21.3, 21, 1], [21.3, 21, 1], [21.3, 21, 1], [21.3, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [10.65, 21, 1], [17.68, 21, 1], [17.68, 21, 1], [21.3, 21, 1], [18.69, 21, 1], [21.3, 21, 1], [21.3, 21, 1]];
//     // tslint:disable-next-line: max-line-length
//     const fangSong24 = ['FangSong_GB231224', [24, 24, 1], [12, 24, 1], [12, 24, 1], [12, 24, 1], [12, 24, 1], [12, 24, 1], [12, 24, 1], [12, 24, 1], [12, 24, 1], [12, 24, 1], [12, 24, 1], [12, 24, 1], [12, 24, 1], [12, 24, 1], [12, 24, 1], [12, 24, 1], [12, 24, 1], [12, 24, 1], [12, 24, 1], [12, 24, 1], [12, 24, 1], [12, 24, 1], [12, 24, 1], [12, 24, 1], [12, 24, 1], [12, 24, 1], [12, 24, 1], [12, 24, 1], [12, 24, 1], [12, 24, 1], [12, 24, 1], [12, 24, 1], [12, 24, 1], [12, 24, 1], [12, 24, 1], [12, 24, 1], [12, 24, 1], [12, 24, 1], [12, 24, 1], [12, 24, 1], [12, 24, 1], [12, 24, 1], [12, 24, 1], [12, 24, 1], [12, 24, 1], [12, 24, 1], [12, 24, 1], [12, 24, 1], [12, 24, 1], [12, 24, 1], [12, 24, 1], [12, 24, 1], [12, 24, 1], [12, 24, 1], [12, 24, 1], [12, 24, 1], [12, 24, 1], [12, 24, 1], [12, 24, 1], [12, 24, 1], [12, 24, 1], [12, 24, 1], [12, 24, 1], [12, 24, 1], [12, 24, 1], [12, 24, 1], [12, 24, 1], [12, 24, 1], [12, 24, 1], [12, 24, 1], [12, 24, 1], [12, 24, 1], [12, 24, 1], [12, 24, 1], [12, 24, 1], [12, 24, 1], [12, 24, 1], [12, 24, 1], [12, 24, 1], [24, 24, 1], [24, 24, 1], [24, 24, 1], [24, 24, 1], [24, 24, 1], [24, 24, 1], [24, 24, 1], [24, 24, 1], [24, 24, 1], [24, 24, 1], [24, 24, 1], [24, 24, 1], [24, 24, 1], [12, 24, 1], [12, 24, 1], [12, 24, 1], [19.92, 24, 1], [19.92, 24, 1], [24, 24, 1], [20.93, 24, 1], [24, 24, 1], [24, 24, 1]];
//     // tslint:disable-next-line: max-line-length
//     const fangSong293 = ['FangSong_GB231229.3', [29.3, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [29.3, 29, 1], [29.3, 29, 1], [29.3, 29, 1], [29.3, 29, 1], [29.3, 29, 1], [29.3, 29, 1], [29.3, 29, 1], [29.3, 29, 1], [29.3, 29, 1], [29.3, 29, 1], [29.3, 29, 1], [29.3, 29, 1], [29.3, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [14.65, 29, 1], [24.32, 29, 1], [24.32, 29, 1], [29.3, 29, 1], [25.39, 29, 1], [29.3, 29, 1], [29.3, 29, 1]];
//     // tslint:disable-next-line: max-line-length
//     const fangSong32 = ['FangSong_GB231232', [32, 33, 1], [16, 33, 1], [16, 33, 1], [16, 33, 1], [16, 33, 1], [16, 33, 1], [16, 33, 1], [16, 33, 1], [16, 33, 1], [16, 33, 1], [16, 33, 1], [16, 33, 1], [16, 33, 1], [16, 33, 1], [16, 33, 1], [16, 33, 1], [16, 33, 1], [16, 33, 1], [16, 33, 1], [16, 33, 1], [16, 33, 1], [16, 33, 1], [16, 33, 1], [16, 33, 1], [16, 33, 1], [16, 33, 1], [16, 33, 1], [16, 33, 1], [16, 33, 1], [16, 33, 1], [16, 33, 1], [16, 33, 1], [16, 33, 1], [16, 33, 1], [16, 33, 1], [16, 33, 1], [16, 33, 1], [16, 33, 1], [16, 33, 1], [16, 33, 1], [16, 33, 1], [16, 33, 1], [16, 33, 1], [16, 33, 1], [16, 33, 1], [16, 33, 1], [16, 33, 1], [16, 33, 1], [16, 33, 1], [16, 33, 1], [16, 33, 1], [16, 33, 1], [16, 33, 1], [16, 33, 1], [16, 33, 1], [16, 33, 1], [16, 33, 1], [16, 33, 1], [16, 33, 1], [16, 33, 1], [16, 33, 1], [16, 33, 1], [16, 33, 1], [16, 33, 1], [16, 33, 1], [16, 33, 1], [16, 33, 1], [16, 33, 1], [16, 33, 1], [16, 33, 1], [16, 33, 1], [16, 33, 1], [16, 33, 1], [16, 33, 1], [16, 33, 1], [16, 33, 1], [16, 33, 1], [16, 33, 1], [16, 33, 1], [32, 33, 1], [32, 33, 1], [32, 33, 1], [32, 33, 1], [32, 33, 1], [32, 33, 1], [32, 33, 1], [32, 33, 1], [32, 33, 1], [32, 33, 1], [32, 33, 1], [32, 33, 1], [32, 33, 1], [16, 33, 1], [16, 33, 1], [16, 33, 1], [26.56, 33, 1], [26.56, 33, 1], [32, 33, 1], [27.66, 33, 1], [32, 33, 1], [32, 33, 1]];
//     // tslint:disable-next-line: max-line-length
//     const fangSong347 = ['FangSong_GB231234.7', [34.7, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [34.7, 35, 1], [34.7, 35, 1], [34.7, 35, 1], [34.7, 35, 1], [34.7, 35, 1], [34.7, 35, 1], [34.7, 35, 1], [34.7, 35, 1], [34.7, 35, 1], [34.7, 35, 1], [34.7, 35, 1], [34.7, 35, 1], [34.7, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [17.35, 35, 1], [28.8, 35, 1], [28.8, 35, 1], [34.7, 35, 1], [29.91, 35, 1], [34.7, 35, 1], [34.7, 35, 1]];
//     // tslint:disable-next-line: max-line-length
//     const fangSong48 = ['FangSong_GB231248', [48, 48, 1], [24, 48, 1], [24, 48, 1], [24, 48, 1], [24, 48, 1], [24, 48, 1], [24, 48, 1], [24, 48, 1], [24, 48, 1], [24, 48, 1], [24, 48, 1], [24, 48, 1], [24, 48, 1], [24, 48, 1], [24, 48, 1], [24, 48, 1], [24, 48, 1], [24, 48, 1], [24, 48, 1], [24, 48, 1], [24, 48, 1], [24, 48, 1], [24, 48, 1], [24, 48, 1], [24, 48, 1], [24, 48, 1], [24, 48, 1], [24, 48, 1], [24, 48, 1], [24, 48, 1], [24, 48, 1], [24, 48, 1], [24, 48, 1], [24, 48, 1], [24, 48, 1], [24, 48, 1], [24, 48, 1], [24, 48, 1], [24, 48, 1], [24, 48, 1], [24, 48, 1], [24, 48, 1], [24, 48, 1], [24, 48, 1], [24, 48, 1], [24, 48, 1], [24, 48, 1], [24, 48, 1], [24, 48, 1], [24, 48, 1], [24, 48, 1], [24, 48, 1], [24, 48, 1], [24, 48, 1], [24, 48, 1], [24, 48, 1], [24, 48, 1], [24, 48, 1], [24, 48, 1], [24, 48, 1], [24, 48, 1], [24, 48, 1], [24, 48, 1], [24, 48, 1], [24, 48, 1], [24, 48, 1], [24, 48, 1], [24, 48, 1], [24, 48, 1], [24, 48, 1], [24, 48, 1], [24, 48, 1], [24, 48, 1], [24, 48, 1], [24, 48, 1], [24, 48, 1], [24, 48, 1], [24, 48, 1], [24, 48, 1], [48, 48, 1], [48, 48, 1], [48, 48, 1], [48, 48, 1], [48, 48, 1], [48, 48, 1], [48, 48, 1], [48, 48, 1], [48, 48, 1], [48, 48, 1], [48, 48, 1], [48, 48, 1], [48, 48, 1], [24, 48, 1], [24, 48, 1], [24, 48, 1], [39.84, 48, 1], [39.84, 48, 1], [48, 48, 1], [41.27, 48, 1], [48, 48, 1], [48, 48, 1]];
//     // tslint:disable-next-line: max-line-length
//     const fangSong587 = ['FangSong_GB231258.7', [58.7, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [58.7, 58, 1], [58.7, 58, 1], [58.7, 58, 1], [58.7, 58, 1], [58.7, 58, 1], [58.7, 58, 1], [58.7, 58, 1], [58.7, 58, 1], [58.7, 58, 1], [58.7, 58, 1], [58.7, 58, 1], [58.7, 58, 1], [58.7, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [29.35, 58, 1], [48.73, 58, 1], [48.73, 58, 1], [58.7, 58, 1], [50.39, 58, 1], [58.7, 58, 1], [58.7, 58, 1]];
//     const fangSongFonts = [fangSong12, fangSong14, fangSong16, fangSong187, fangSong20, fangSong213, fangSong24,
//         fangSong293, fangSong32, fangSong347, fangSong48, fangSong587];

//     // 初号58.7 小初48 一号34.7 小一32 二号29.3 小二24 三号21.3 小三20 四号18.7 小四16 五号14 小五12'☑', '☐' ⊙ ⊙ ⭘ ◯
//     // tslint:disable-next-line: max-line-length
//     const kaiti12 = ['STKaiti12', [12, 12, 1], [2.63, 12, 1], [5.63, 12, 1], [5.63, 12, 1], [5.63, 12, 1], [5.63, 12, 1], [5.63, 12, 1], [5.63, 12, 1], [5.63, 12, 1], [5.63, 12, 1], [5.63, 12, 1], [5.63, 12, 1], [6.13, 12, 1], [6.24, 12, 1], [5.06, 12, 1], [6.18, 12, 1], [5.15, 12, 1], [4.04, 12, 1], [5.76, 12, 1], [6.38, 12, 1], [3.34, 12, 1], [2.78, 12, 1], [6.23, 12, 1], [3.25, 12, 1], [9.4, 12, 1], [6.37, 12, 1], [6.13, 12, 1], [6.28, 12, 1], [6.25, 12, 1], [4.66, 12, 1], [4.61, 12, 1], [4.26, 12, 1], [6.35, 12, 1], [5.65, 12, 1], [8, 12, 1], [5.71, 12, 1], [5.51, 12, 1], [5.18, 12, 1], [8.39, 12, 1], [7.7, 12, 1], [7.73, 12, 1], [9.28, 12, 1], [7.94, 12, 1], [6.86, 12, 1], [9.25, 12, 1], [9.68, 12, 1], [4.25, 12, 1], [4, 12, 1], [8.92, 12, 1], [7.06, 12, 1], [11.16, 12, 1], [9.32, 12, 1], [9.31, 12, 1], [6.88, 12, 1], [9.2, 12, 1], [8.34, 12, 1], [5.81, 12, 1], [7.38, 12, 1], [8.57, 12, 1], [8.23, 12, 1], [10.74, 12, 1], [8.45, 12, 1], [8.06, 12, 1], [7.92, 12, 1], [2.63, 12, 1], [2.63, 12, 1], [2.63, 12, 1], [6, 12, 1], [4.38, 12, 1], [4.07, 12, 1], [3.95, 12, 1], [3.85, 12, 1], [3.88, 12, 1], [8, 12, 1], [3.76, 12, 1], [8, 12, 1], [5.12, 12, 1], [8, 12, 1], [8, 12, 1], [12, 12, 1], [12, 12, 1], [12, 12, 1], [2.99, 12, 1], [4.85, 12, 1], [12, 12, 1], [12, 12, 1], [12, 12, 1], [12, 12, 1], [12, 12, 1], [12, 12, 1], [7.9, 12, 1], [12, 12, 1], [2.63, 12, 1], [5.75, 12, 1], [5.75, 12, 1], [9.96, 12, 1], [9.96, 12, 1], [12, 12, 1], [10.79, 12, 1], [12, 12, 1], [10.15, 12, 1]];
//     // tslint:disable-next-line: max-line-length
//     const kaiti14 = ['STKaiti14', [14, 14, 1], [3.07, 14, 1], [6.57, 14, 1], [6.57, 14, 1], [6.57, 14, 1], [6.57, 14, 1], [6.57, 14, 1], [6.57, 14, 1], [6.57, 14, 1], [6.57, 14, 1], [6.57, 14, 1], [6.57, 14, 1], [7.15, 14, 1], [7.28, 14, 1], [5.91, 14, 1], [7.21, 14, 1], [6.01, 14, 1], [4.72, 14, 1], [6.72, 14, 1], [7.45, 14, 1], [3.89, 14, 1], [3.25, 14, 1], [7.27, 14, 1], [3.79, 14, 1], [10.96, 14, 1], [7.43, 14, 1], [7.15, 14, 1], [7.32, 14, 1], [7.29, 14, 1], [5.43, 14, 1], [5.38, 14, 1], [4.97, 14, 1], [7.41, 14, 1], [6.59, 14, 1], [9.34, 14, 1], [6.66, 14, 1], [6.43, 14, 1], [6.05, 14, 1], [9.79, 14, 1], [8.99, 14, 1], [9.02, 14, 1], [10.82, 14, 1], [9.27, 14, 1], [8.01, 14, 1], [10.79, 14, 1], [11.3, 14, 1], [4.96, 14, 1], [4.66, 14, 1], [10.4, 14, 1], [8.23, 14, 1], [13.02, 14, 1], [10.88, 14, 1], [10.86, 14, 1], [8.02, 14, 1], [10.74, 14, 1], [9.73, 14, 1], [6.78, 14, 1], [8.61, 14, 1], [10, 14, 1], [9.6, 14, 1], [12.53, 14, 1], [9.86, 14, 1], [9.41, 14, 1], [9.24, 14, 1], [3.07, 14, 1], [3.07, 14, 1], [3.07, 14, 1], [7, 14, 1], [5.11, 14, 1], [4.75, 14, 1], [4.61, 14, 1], [4.49, 14, 1], [4.52, 14, 1], [9.34, 14, 1], [4.38, 14, 1], [9.34, 14, 1], [5.98, 14, 1], [9.34, 14, 1], [9.34, 14, 1], [14, 14, 1], [14, 14, 1], [14, 14, 1], [3.49, 14, 1], [5.66, 14, 1], [14, 14, 1], [14, 14, 1], [14, 14, 1], [14, 14, 1], [14, 14, 1], [14, 14, 1], [9.21, 14, 1], [14, 14, 1], [3.07, 14, 1], [6.71, 14, 1], [6.71, 14, 1], [11.62, 14, 1], [11.62, 14, 1], [14, 14, 1], [12.51, 14, 1], [14, 14, 1], [11.84, 14, 1]];
//     // tslint:disable-next-line: max-line-length
//     const kaiti16 = ['STKaiti16', [16, 16, 1], [3.5, 16, 1], [7.5, 16, 1], [7.5, 16, 1], [7.5, 16, 1], [7.5, 16, 1], [7.5, 16, 1], [7.5, 16, 1], [7.5, 16, 1], [7.5, 16, 1], [7.5, 16, 1], [7.5, 16, 1], [8.18, 16, 1], [8.32, 16, 1], [6.75, 16, 1], [8.24, 16, 1], [6.86, 16, 1], [5.39, 16, 1], [7.68, 16, 1], [8.51, 16, 1], [4.45, 16, 1], [3.71, 16, 1], [8.3, 16, 1], [4.34, 16, 1], [12.53, 16, 1], [8.5, 16, 1], [8.18, 16, 1], [8.37, 16, 1], [8.34, 16, 1], [6.21, 16, 1], [6.14, 16, 1], [5.68, 16, 1], [8.46, 16, 1], [7.54, 16, 1], [10.67, 16, 1], [7.62, 16, 1], [7.34, 16, 1], [6.91, 16, 1], [11.18, 16, 1], [10.27, 16, 1], [10.3, 16, 1], [12.37, 16, 1], [10.59, 16, 1], [9.15, 16, 1], [12.34, 16, 1], [12.91, 16, 1], [5.66, 16, 1], [5.33, 16, 1], [11.89, 16, 1], [9.41, 16, 1], [14.88, 16, 1], [12.43, 16, 1], [12.42, 16, 1], [9.17, 16, 1], [12.27, 16, 1], [11.12, 16, 1], [7.74, 16, 1], [9.84, 16, 1], [11.42, 16, 1], [10.98, 16, 1], [14.32, 16, 1], [11.26, 16, 1], [10.75, 16, 1], [10.56, 16, 1], [3.5, 16, 1], [3.5, 16, 1], [3.5, 16, 1], [8, 16, 1], [5.84, 16, 1], [5.42, 16, 1], [5.26, 16, 1], [5.14, 16, 1], [5.17, 16, 1], [10.67, 16, 1], [5.01, 16, 1], [10.67, 16, 1], [6.83, 16, 1], [10.67, 16, 1], [10.67, 16, 1], [16, 16, 1], [16, 16, 1], [16, 16, 1], [3.98, 16, 1], [6.46, 16, 1], [16, 16, 1], [16, 16, 1], [16, 16, 1], [16, 16, 1], [16, 16, 1], [16, 16, 1], [10.53, 16, 1], [16, 16, 1], [3.5, 16, 1], [7.66, 16, 1], [7.66, 16, 1], [13.28, 16, 1], [13.28, 16, 1], [16, 16, 1], [14.08, 16, 1], [16, 16, 1], [13.54, 16, 1]];
//     // tslint:disable-next-line: max-line-length
//     const kaiti187 = ['STKaiti18.7', [18.7, 19, 1], [4.1, 19, 1], [8.77, 19, 1], [8.77, 19, 1], [8.77, 19, 1], [8.77, 19, 1], [8.77, 19, 1], [8.77, 19, 1], [8.77, 19, 1], [8.77, 19, 1], [8.77, 19, 1], [8.77, 19, 1], [9.56, 19, 1], [9.72, 19, 1], [7.89, 19, 1], [9.63, 19, 1], [8.02, 19, 1], [6.3, 19, 1], [8.98, 19, 1], [9.95, 19, 1], [5.2, 19, 1], [4.34, 19, 1], [9.71, 19, 1], [5.07, 19, 1], [14.64, 19, 1], [9.93, 19, 1], [9.56, 19, 1], [9.78, 19, 1], [9.74, 19, 1], [7.26, 19, 1], [7.18, 19, 1], [6.64, 19, 1], [9.89, 19, 1], [8.81, 19, 1], [12.47, 19, 1], [8.9, 19, 1], [8.58, 19, 1], [8.08, 19, 1], [13.07, 19, 1], [12.01, 19, 1], [12.04, 19, 1], [14.46, 19, 1], [12.38, 19, 1], [10.7, 19, 1], [14.42, 19, 1], [15.09, 19, 1], [6.62, 19, 1], [6.23, 19, 1], [13.89, 19, 1], [11, 19, 1], [17.39, 19, 1], [14.53, 19, 1], [14.51, 19, 1], [10.72, 19, 1], [14.34, 19, 1], [13, 19, 1], [9.05, 19, 1], [11.5, 19, 1], [13.35, 19, 1], [12.83, 19, 1], [16.74, 19, 1], [13.16, 19, 1], [12.57, 19, 1], [12.34, 19, 1], [4.1, 19, 1], [4.1, 19, 1], [4.1, 19, 1], [9.35, 19, 1], [6.83, 19, 1], [6.34, 19, 1], [6.15, 19, 1], [6, 19, 1], [6.04, 19, 1], [12.47, 19, 1], [5.85, 19, 1], [12.47, 19, 1], [7.98, 19, 1], [12.47, 19, 1], [12.47, 19, 1], [18.7, 19, 1], [18.7, 19, 1], [18.7, 19, 1], [4.66, 19, 1], [7.55, 19, 1], [18.7, 19, 1], [18.7, 19, 1], [18.7, 19, 1], [18.7, 19, 1], [18.7, 19, 1], [18.7, 19, 1], [12.31, 19, 1], [18.7, 19, 1], [4.1, 19, 1], [8.96, 19, 1], [8.96, 19, 1], [15.52, 19, 1], [15.52, 19, 1], [18.7, 19, 1], [16.25, 19, 1], [18.7, 19, 1], [15.82, 19, 1]];
//     // tslint:disable-next-line: max-line-length
//     const kaiti20 = ['STKaiti20', [20, 20, 1], [4.38, 20, 1], [9.38, 20, 1], [9.38, 20, 1], [9.38, 20, 1], [9.38, 20, 1], [9.38, 20, 1], [9.38, 20, 1], [9.38, 20, 1], [9.38, 20, 1], [9.38, 20, 1], [9.38, 20, 1], [10.22, 20, 1], [10.4, 20, 1], [8.44, 20, 1], [10.3, 20, 1], [8.58, 20, 1], [6.74, 20, 1], [9.6, 20, 1], [10.64, 20, 1], [5.56, 20, 1], [4.64, 20, 1], [10.38, 20, 1], [5.42, 20, 1], [15.66, 20, 1], [10.62, 20, 1], [10.22, 20, 1], [10.46, 20, 1], [10.42, 20, 1], [7.76, 20, 1], [7.68, 20, 1], [7.1, 20, 1], [10.58, 20, 1], [9.42, 20, 1], [13.34, 20, 1], [9.52, 20, 1], [9.18, 20, 1], [8.64, 20, 1], [13.98, 20, 1], [12.84, 20, 1], [12.88, 20, 1], [15.46, 20, 1], [13.24, 20, 1], [11.44, 20, 1], [15.42, 20, 1], [16.14, 20, 1], [7.08, 20, 1], [6.66, 20, 1], [14.86, 20, 1], [11.76, 20, 1], [18.6, 20, 1], [15.54, 20, 1], [15.52, 20, 1], [11.46, 20, 1], [15.34, 20, 1], [13.9, 20, 1], [9.68, 20, 1], [12.3, 20, 1], [14.28, 20, 1], [13.72, 20, 1], [17.9, 20, 1], [14.08, 20, 1], [13.44, 20, 1], [13.2, 20, 1], [4.38, 20, 1], [4.38, 20, 1], [4.38, 20, 1], [10, 20, 1], [7.3, 20, 1], [6.78, 20, 1], [6.58, 20, 1], [6.42, 20, 1], [6.46, 20, 1], [13.34, 20, 1], [6.26, 20, 1], [13.34, 20, 1], [8.54, 20, 1], [13.34, 20, 1], [13.34, 20, 1], [20, 20, 1], [20, 20, 1], [20, 20, 1], [4.98, 20, 1], [8.08, 20, 1], [20, 20, 1], [20, 20, 1], [20, 20, 1], [20, 20, 1], [20, 20, 1], [20, 20, 1], [13.16, 20, 1], [20, 20, 1], [4.38, 20, 1], [9.58, 20, 1], [9.58, 20, 1], [16.6, 20, 1], [16.6, 20, 1], [20, 20, 1], [17.6, 20, 1], [20, 20, 1], [16.92, 20, 1]];
//     // tslint:disable-next-line: max-line-length
//     const kaiti213 = ['STKaiti21.3', [21.3, 21, 1], [4.66, 21, 1], [9.99, 21, 1], [9.99, 21, 1], [9.99, 21, 1], [9.99, 21, 1], [9.99, 21, 1], [9.99, 21, 1], [9.99, 21, 1], [9.99, 21, 1], [9.99, 21, 1], [9.99, 21, 1], [10.88, 21, 1], [11.08, 21, 1], [8.99, 21, 1], [10.97, 21, 1], [9.14, 21, 1], [7.18, 21, 1], [10.22, 21, 1], [11.33, 21, 1], [5.92, 21, 1], [4.94, 21, 1], [11.05, 21, 1], [5.77, 21, 1], [16.68, 21, 1], [11.31, 21, 1], [10.88, 21, 1], [11.14, 21, 1], [11.1, 21, 1], [8.26, 21, 1], [8.18, 21, 1], [7.56, 21, 1], [11.27, 21, 1], [10.03, 21, 1], [14.21, 21, 1], [10.14, 21, 1], [9.78, 21, 1], [9.2, 21, 1], [14.89, 21, 1], [13.67, 21, 1], [13.72, 21, 1], [16.46, 21, 1], [14.1, 21, 1], [12.18, 21, 1], [16.42, 21, 1], [17.19, 21, 1], [7.54, 21, 1], [7.09, 21, 1], [15.83, 21, 1], [12.52, 21, 1], [19.81, 21, 1], [16.55, 21, 1], [16.53, 21, 1], [12.2, 21, 1], [16.34, 21, 1], [14.8, 21, 1], [10.31, 21, 1], [13.1, 21, 1], [15.21, 21, 1], [14.61, 21, 1], [19.06, 21, 1], [15, 21, 1], [14.31, 21, 1], [14.06, 21, 1], [4.66, 21, 1], [4.66, 21, 1], [4.66, 21, 1], [10.65, 21, 1], [7.77, 21, 1], [7.22, 21, 1], [7.01, 21, 1], [6.84, 21, 1], [6.88, 21, 1], [14.21, 21, 1], [6.67, 21, 1], [14.21, 21, 1], [9.1, 21, 1], [14.21, 21, 1], [14.21, 21, 1], [21.3, 21, 1], [21.3, 21, 1], [21.3, 21, 1], [5.3, 21, 1], [8.61, 21, 1], [21.3, 21, 1], [21.3, 21, 1], [21.3, 21, 1], [21.3, 21, 1], [21.3, 21, 1], [21.3, 21, 1], [14.02, 21, 1], [21.3, 21, 1], [4.66, 21, 1], [10.2, 21, 1], [10.2, 21, 1], [17.68, 21, 1], [17.68, 21, 1], [21.3, 21, 1], [18.69, 21, 1], [21.3, 21, 1], [18.02, 21, 1]];
//     // tslint:disable-next-line: max-line-length
//     const kaiti24 = ['STKaiti24', [24, 24, 1], [5.26, 24, 1], [11.26, 24, 1], [11.26, 24, 1], [11.26, 24, 1], [11.26, 24, 1], [11.26, 24, 1], [11.26, 24, 1], [11.26, 24, 1], [11.26, 24, 1], [11.26, 24, 1], [11.26, 24, 1], [12.26, 24, 1], [12.48, 24, 1], [10.13, 24, 1], [12.36, 24, 1], [10.3, 24, 1], [8.09, 24, 1], [11.52, 24, 1], [12.77, 24, 1], [6.67, 24, 1], [5.57, 24, 1], [12.46, 24, 1], [6.5, 24, 1], [18.79, 24, 1], [12.74, 24, 1], [12.26, 24, 1], [12.55, 24, 1], [12.5, 24, 1], [9.31, 24, 1], [9.22, 24, 1], [8.52, 24, 1], [12.7, 24, 1], [11.3, 24, 1], [16.01, 24, 1], [11.42, 24, 1], [11.02, 24, 1], [10.37, 24, 1], [16.78, 24, 1], [15.41, 24, 1], [15.46, 24, 1], [18.55, 24, 1], [15.89, 24, 1], [13.73, 24, 1], [18.5, 24, 1], [19.37, 24, 1], [8.5, 24, 1], [7.99, 24, 1], [17.83, 24, 1], [14.11, 24, 1], [22.32, 24, 1], [18.65, 24, 1], [18.62, 24, 1], [13.75, 24, 1], [18.41, 24, 1], [16.68, 24, 1], [11.62, 24, 1], [14.76, 24, 1], [17.14, 24, 1], [16.46, 24, 1], [21.48, 24, 1], [16.9, 24, 1], [16.13, 24, 1], [15.84, 24, 1], [5.26, 24, 1], [5.26, 24, 1], [5.26, 24, 1], [12, 24, 1], [8.76, 24, 1], [8.14, 24, 1], [7.9, 24, 1], [7.7, 24, 1], [7.75, 24, 1], [16.01, 24, 1], [7.51, 24, 1], [16.01, 24, 1], [10.25, 24, 1], [16.01, 24, 1], [16.01, 24, 1], [24, 24, 1], [24, 24, 1], [24, 24, 1], [5.98, 24, 1], [9.7, 24, 1], [24, 24, 1], [24, 24, 1], [24, 24, 1], [24, 24, 1], [24, 24, 1], [24, 24, 1], [15.8, 24, 1], [24, 24, 1], [5.26, 24, 1], [11.5, 24, 1], [11.5, 24, 1], [19.92, 24, 1], [19.92, 24, 1], [24, 24, 1], [20.93, 24, 1], [24, 24, 1], [20.3, 24, 1]];
//     // tslint:disable-next-line: max-line-length
//     const kaiti293 = ['STKaiti29.3', [29.3, 29, 1], [6.42, 29, 1], [13.74, 29, 1], [13.74, 29, 1], [13.74, 29, 1], [13.74, 29, 1], [13.74, 29, 1], [13.74, 29, 1], [13.74, 29, 1], [13.74, 29, 1], [13.74, 29, 1], [13.74, 29, 1], [14.97, 29, 1], [15.24, 29, 1], [12.36, 29, 1], [15.09, 29, 1], [12.57, 29, 1], [9.87, 29, 1], [14.06, 29, 1], [15.59, 29, 1], [8.15, 29, 1], [6.8, 29, 1], [15.21, 29, 1], [7.94, 29, 1], [22.94, 29, 1], [15.56, 29, 1], [14.97, 29, 1], [15.32, 29, 1], [15.27, 29, 1], [11.37, 29, 1], [11.25, 29, 1], [10.4, 29, 1], [15.5, 29, 1], [13.8, 29, 1], [19.54, 29, 1], [13.95, 29, 1], [13.45, 29, 1], [12.66, 29, 1], [20.48, 29, 1], [18.81, 29, 1], [18.87, 29, 1], [22.65, 29, 1], [19.4, 29, 1], [16.76, 29, 1], [22.59, 29, 1], [23.65, 29, 1], [10.37, 29, 1], [9.76, 29, 1], [21.77, 29, 1], [17.23, 29, 1], [27.25, 29, 1], [22.77, 29, 1], [22.74, 29, 1], [16.79, 29, 1], [22.47, 29, 1], [20.36, 29, 1], [14.18, 29, 1], [18.02, 29, 1], [20.92, 29, 1], [20.1, 29, 1], [26.22, 29, 1], [20.63, 29, 1], [19.69, 29, 1], [19.34, 29, 1], [6.42, 29, 1], [6.42, 29, 1], [6.42, 29, 1], [14.65, 29, 1], [10.69, 29, 1], [9.93, 29, 1], [9.64, 29, 1], [9.41, 29, 1], [9.46, 29, 1], [19.54, 29, 1], [9.17, 29, 1], [19.54, 29, 1], [12.51, 29, 1], [19.54, 29, 1], [19.54, 29, 1], [29.3, 29, 1], [29.3, 29, 1], [29.3, 29, 1], [7.3, 29, 1], [11.84, 29, 1], [29.3, 29, 1], [29.3, 29, 1], [29.3, 29, 1], [29.3, 29, 1], [29.3, 29, 1], [29.3, 29, 1], [19.29, 29, 1], [29.3, 29, 1], [6.42, 29, 1], [14.03, 29, 1], [14.03, 29, 1], [24.32, 29, 1], [24.32, 29, 1], [29.3, 29, 1], [25.39, 29, 1], [29.3, 29, 1], [24.79, 29, 1]];
//     // tslint:disable-next-line: max-line-length
//     const kaiti32 = ['STKaiti32', [32, 32, 1], [7.01, 32, 1], [15.01, 32, 1], [15.01, 32, 1], [15.01, 32, 1], [15.01, 32, 1], [15.01, 32, 1], [15.01, 32, 1], [15.01, 32, 1], [15.01, 32, 1], [15.01, 32, 1], [15.01, 32, 1], [16.35, 32, 1], [16.64, 32, 1], [13.5, 32, 1], [16.48, 32, 1], [13.73, 32, 1], [10.78, 32, 1], [15.36, 32, 1], [17.02, 32, 1], [8.9, 32, 1], [7.42, 32, 1], [16.61, 32, 1], [8.67, 32, 1], [25.06, 32, 1], [16.99, 32, 1], [16.35, 32, 1], [16.74, 32, 1], [16.67, 32, 1], [12.42, 32, 1], [12.29, 32, 1], [11.36, 32, 1], [16.93, 32, 1], [15.07, 32, 1], [21.34, 32, 1], [15.23, 32, 1], [14.69, 32, 1], [13.82, 32, 1], [22.37, 32, 1], [20.54, 32, 1], [20.61, 32, 1], [24.74, 32, 1], [21.18, 32, 1], [18.3, 32, 1], [24.67, 32, 1], [25.82, 32, 1], [11.33, 32, 1], [10.66, 32, 1], [23.78, 32, 1], [18.82, 32, 1], [29.76, 32, 1], [24.86, 32, 1], [24.83, 32, 1], [18.34, 32, 1], [24.54, 32, 1], [22.24, 32, 1], [15.49, 32, 1], [19.68, 32, 1], [22.85, 32, 1], [21.95, 32, 1], [28.64, 32, 1], [22.53, 32, 1], [21.5, 32, 1], [21.12, 32, 1], [7.01, 32, 1], [7.01, 32, 1], [7.01, 32, 1], [16, 32, 1], [11.68, 32, 1], [10.85, 32, 1], [10.53, 32, 1], [10.27, 32, 1], [10.34, 32, 1], [21.34, 32, 1], [10.02, 32, 1], [21.34, 32, 1], [13.66, 32, 1], [21.34, 32, 1], [21.34, 32, 1], [32, 32, 1], [32, 32, 1], [32, 32, 1], [7.97, 32, 1], [12.93, 32, 1], [32, 32, 1], [32, 32, 1], [32, 32, 1], [32, 32, 1], [32, 32, 1], [32, 32, 1], [21.06, 32, 1], [32, 32, 1], [7.01, 32, 1], [15.33, 32, 1], [15.33, 32, 1], [26.56, 32, 1], [26.56, 32, 1], [32, 32, 1], [27.66, 32, 1], [32, 32, 1], [27.07, 32, 1]];
//     // tslint:disable-next-line: max-line-length
//     const kaiti347 = ['STKaiti34.7', [34.7, 35, 1], [7.6, 35, 1], [16.27, 35, 1], [16.27, 35, 1], [16.27, 35, 1], [16.27, 35, 1], [16.27, 35, 1], [16.27, 35, 1], [16.27, 35, 1], [16.27, 35, 1], [16.27, 35, 1], [16.27, 35, 1], [17.73, 35, 1], [18.04, 35, 1], [14.64, 35, 1], [17.87, 35, 1], [14.89, 35, 1], [11.69, 35, 1], [16.66, 35, 1], [18.46, 35, 1], [9.65, 35, 1], [8.05, 35, 1], [18.01, 35, 1], [9.4, 35, 1], [27.17, 35, 1], [18.43, 35, 1], [17.73, 35, 1], [18.15, 35, 1], [18.08, 35, 1], [13.46, 35, 1], [13.32, 35, 1], [12.32, 35, 1], [18.36, 35, 1], [16.34, 35, 1], [23.14, 35, 1], [16.52, 35, 1], [15.93, 35, 1], [14.99, 35, 1], [24.26, 35, 1], [22.28, 35, 1], [22.35, 35, 1], [26.82, 35, 1], [22.97, 35, 1], [19.85, 35, 1], [26.75, 35, 1], [28, 35, 1], [12.28, 35, 1], [11.56, 35, 1], [25.78, 35, 1], [20.4, 35, 1], [32.27, 35, 1], [26.96, 35, 1], [26.93, 35, 1], [19.88, 35, 1], [26.61, 35, 1], [24.12, 35, 1], [16.79, 35, 1], [21.34, 35, 1], [24.78, 35, 1], [23.8, 35, 1], [31.06, 35, 1], [24.43, 35, 1], [23.32, 35, 1], [22.9, 35, 1], [7.6, 35, 1], [7.6, 35, 1], [7.6, 35, 1], [17.35, 35, 1], [12.67, 35, 1], [11.76, 35, 1], [11.42, 35, 1], [11.14, 35, 1], [11.21, 35, 1], [23.14, 35, 1], [10.86, 35, 1], [23.14, 35, 1], [14.82, 35, 1], [23.14, 35, 1], [23.14, 35, 1], [34.7, 35, 1], [34.7, 35, 1], [34.7, 35, 1], [8.64, 35, 1], [14.02, 35, 1], [34.7, 35, 1], [34.7, 35, 1], [34.7, 35, 1], [34.7, 35, 1], [34.7, 35, 1], [34.7, 35, 1], [22.84, 35, 1], [34.7, 35, 1], [7.6, 35, 1], [16.62, 35, 1], [16.62, 35, 1], [28.8, 35, 1], [28.8, 35, 1], [34.7, 35, 1], [29.91, 35, 1], [34.7, 35, 1], [29.36, 35, 1]];
//     // tslint:disable-next-line: max-line-length
//     const kaiti48 = ['STKaiti48', [48, 48, 1], [10.51, 48, 1], [22.51, 48, 1], [22.51, 48, 1], [22.51, 48, 1], [22.51, 48, 1], [22.51, 48, 1], [22.51, 48, 1], [22.51, 48, 1], [22.51, 48, 1], [22.51, 48, 1], [22.51, 48, 1], [24.53, 48, 1], [24.96, 48, 1], [20.26, 48, 1], [24.72, 48, 1], [20.59, 48, 1], [16.18, 48, 1], [23.04, 48, 1], [25.54, 48, 1], [13.34, 48, 1], [11.14, 48, 1], [24.91, 48, 1], [13.01, 48, 1], [37.58, 48, 1], [25.49, 48, 1], [24.53, 48, 1], [25.1, 48, 1], [25.01, 48, 1], [18.62, 48, 1], [18.43, 48, 1], [17.04, 48, 1], [25.39, 48, 1], [22.61, 48, 1], [32.02, 48, 1], [22.85, 48, 1], [22.03, 48, 1], [20.74, 48, 1], [33.55, 48, 1], [30.82, 48, 1], [30.91, 48, 1], [37.1, 48, 1], [31.78, 48, 1], [27.46, 48, 1], [37.01, 48, 1], [38.74, 48, 1], [16.99, 48, 1], [15.98, 48, 1], [35.66, 48, 1], [28.22, 48, 1], [44.64, 48, 1], [37.3, 48, 1], [37.25, 48, 1], [27.5, 48, 1], [36.82, 48, 1], [33.36, 48, 1], [23.23, 48, 1], [29.52, 48, 1], [34.27, 48, 1], [32.93, 48, 1], [42.96, 48, 1], [33.79, 48, 1], [32.26, 48, 1], [31.68, 48, 1], [10.51, 48, 1], [10.51, 48, 1], [10.51, 48, 1], [24, 48, 1], [17.52, 48, 1], [16.27, 48, 1], [15.79, 48, 1], [15.41, 48, 1], [15.5, 48, 1], [32.02, 48, 1], [15.02, 48, 1], [32.02, 48, 1], [20.5, 48, 1], [32.02, 48, 1], [32.02, 48, 1], [48, 48, 1], [48, 48, 1], [48, 48, 1], [11.95, 48, 1], [19.39, 48, 1], [48, 48, 1], [48, 48, 1], [48, 48, 1], [48, 48, 1], [48, 48, 1], [48, 48, 1], [31.59, 48, 1], [48, 48, 1], [10.51, 48, 1], [22.99, 48, 1], [22.99, 48, 1], [39.84, 48, 1], [39.84, 48, 1], [48, 48, 1], [41.27, 48, 1], [48, 48, 1], [40.61, 48, 1]];
//     // tslint:disable-next-line: max-line-length
//     const kaiti587 = ['STKaiti58.7', [58.7, 58, 1], [12.86, 58, 1], [27.53, 58, 1], [27.53, 58, 1], [27.53, 58, 1], [27.53, 58, 1], [27.53, 58, 1], [27.53, 58, 1], [27.53, 58, 1], [27.53, 58, 1], [27.53, 58, 1], [27.53, 58, 1], [30, 58, 1], [30.52, 58, 1], [24.77, 58, 1], [30.23, 58, 1], [25.18, 58, 1], [19.78, 58, 1], [28.18, 58, 1], [31.23, 58, 1], [16.32, 58, 1], [13.62, 58, 1], [30.47, 58, 1], [15.91, 58, 1], [45.96, 58, 1], [31.17, 58, 1], [30, 58, 1], [30.7, 58, 1], [30.58, 58, 1], [22.78, 58, 1], [22.54, 58, 1], [20.84, 58, 1], [31.05, 58, 1], [27.65, 58, 1], [39.15, 58, 1], [27.94, 58, 1], [26.94, 58, 1], [25.36, 58, 1], [41.03, 58, 1], [37.69, 58, 1], [37.8, 58, 1], [45.38, 58, 1], [38.86, 58, 1], [33.58, 58, 1], [45.26, 58, 1], [47.37, 58, 1], [20.78, 58, 1], [19.55, 58, 1], [43.61, 58, 1], [34.52, 58, 1], [54.59, 58, 1], [45.61, 58, 1], [45.55, 58, 1], [33.64, 58, 1], [45.02, 58, 1], [40.8, 58, 1], [28.41, 58, 1], [36.1, 58, 1], [41.91, 58, 1], [40.27, 58, 1], [52.54, 58, 1], [41.32, 58, 1], [39.45, 58, 1], [38.74, 58, 1], [12.86, 58, 1], [12.86, 58, 1], [12.86, 58, 1], [29.35, 58, 1], [21.43, 58, 1], [19.9, 58, 1], [19.31, 58, 1], [18.84, 58, 1], [18.96, 58, 1], [39.15, 58, 1], [18.37, 58, 1], [39.15, 58, 1], [25.06, 58, 1], [39.15, 58, 1], [39.15, 58, 1], [58.7, 58, 1], [58.7, 58, 1], [58.7, 58, 1], [14.62, 58, 1], [23.71, 58, 1], [58.7, 58, 1], [58.7, 58, 1], [58.7, 58, 1], [58.7, 58, 1], [58.7, 58, 1], [58.7, 58, 1], [38.64, 58, 1], [58.7, 58, 1], [12.86, 58, 1], [28.12, 58, 1], [28.12, 58, 1], [48.73, 58, 1], [48.73, 58, 1], [58.7, 58, 1], [50.39, 58, 1], [58.7, 58, 1], [49.66, 58, 1]];
//     const kaitiFonts = [kaiti12, kaiti14, kaiti16, kaiti187, kaiti20, kaiti213, kaiti24, kaiti293, kaiti32,
//         kaiti347, kaiti48, kaiti587];

//     // tslint:disable-next-line: max-line-length
//     const hiragino12 = ['Hiragino Sans GB12', [12, 12, 1], [2.88, 12, 1], [7.88, 12, 1], [7.88, 12, 1], [7.88, 12, 1], [7.88, 12, 1], [7.93, 12, 1], [7.88, 12, 1], [7.88, 12, 1], [7.88, 12, 1], [7.88, 12, 1], [7.88, 12, 1], [6.9, 12, 1], [7.75, 12, 1], [6.94, 12, 1], [7.75, 12, 1], [7.06, 12, 1], [5, 12, 1], [7.69, 12, 1], [7.4, 12, 1], [2.86, 12, 1], [3.18, 12, 1], [6.9, 12, 1], [2.78, 12, 1], [11.17, 12, 1], [7.42, 12, 1], [7.5, 12, 1], [7.76, 12, 1], [7.75, 12, 1], [5.16, 12, 1], [6.44, 12, 1], [4.7, 12, 1], [7.42, 12, 1], [6.53, 12, 1], [9.24, 12, 1], [6.43, 12, 1], [6.65, 12, 1], [5.98, 12, 1], [9, 12, 1], [8.54, 12, 1], [9.07, 12, 1], [9.1, 12, 1], [7.72, 12, 1], [7.22, 12, 1], [9.05, 12, 1], [9.34, 12, 1], [2.99, 12, 1], [6.1, 12, 1], [8.48, 12, 1], [7.22, 12, 1], [11.34, 12, 1], [9.29, 12, 1], [9.54, 12, 1], [7.94, 12, 1], [9.54, 12, 1], [8.71, 12, 1], [8.03, 12, 1], [7.79, 12, 1], [9.28, 12, 1], [8.6, 12, 1], [12.07, 12, 1], [8.66, 12, 1], [8.26, 12, 1], [8.03, 12, 1], [2.88, 12, 1], [2.88, 12, 1], [2.88, 12, 1], [5.69, 12, 1], [6.85, 12, 1], [4.27, 12, 1], [4.27, 12, 1], [4.15, 12, 1], [4.15, 12, 1], [7.87, 12, 1], [4.3, 12, 1], [7.87, 12, 1], [4.15, 12, 1], [6.14, 12, 1], [6.14, 12, 1], [12, 12, 1], [12, 12, 1], [12, 12, 1], [12, 12, 1], [12, 12, 1], [12, 12, 1], [12, 12, 1], [12, 12, 1], [12, 12, 1], [12, 12, 1], [12, 12, 1], [12, 12, 1], [12, 12, 1], [2.88, 12, 1], [4.16, 12, 1], [4.16, 12, 1], [9.96, 12, 1], [9.96, 12, 1], [12, 12, 1], [10.79, 12, 1], [12, 12, 1], [12, 12, 1]];
//     // tslint:disable-next-line: max-line-length
//     const hiragino14 = ['Hiragino Sans GB14', [14, 14, 1], [3.36, 14, 1], [9.2, 14, 1], [9.2, 14, 1], [9.2, 14, 1], [9.2, 14, 1], [9.25, 14, 1], [9.2, 14, 1], [9.2, 14, 1], [9.2, 14, 1], [9.2, 14, 1], [9.2, 14, 1], [8.05, 14, 1], [9.04, 14, 1], [8.09, 14, 1], [9.04, 14, 1], [8.23, 14, 1], [5.84, 14, 1], [8.97, 14, 1], [8.64, 14, 1], [3.33, 14, 1], [3.71, 14, 1], [8.05, 14, 1], [3.25, 14, 1], [13.03, 14, 1], [8.65, 14, 1], [8.75, 14, 1], [9.06, 14, 1], [9.04, 14, 1], [6.02, 14, 1], [7.52, 14, 1], [5.49, 14, 1], [8.65, 14, 1], [7.62, 14, 1], [10.78, 14, 1], [7.5, 14, 1], [7.76, 14, 1], [6.97, 14, 1], [10.5, 14, 1], [9.97, 14, 1], [10.58, 14, 1], [10.61, 14, 1], [9, 14, 1], [8.43, 14, 1], [10.56, 14, 1], [10.89, 14, 1], [3.49, 14, 1], [7.11, 14, 1], [9.9, 14, 1], [8.43, 14, 1], [13.23, 14, 1], [10.84, 14, 1], [11.13, 14, 1], [9.27, 14, 1], [11.13, 14, 1], [10.16, 14, 1], [9.37, 14, 1], [9.09, 14, 1], [10.82, 14, 1], [10.04, 14, 1], [14.08, 14, 1], [10.11, 14, 1], [9.63, 14, 1], [9.37, 14, 1], [3.36, 14, 1], [3.36, 14, 1], [3.36, 14, 1], [6.64, 14, 1], [7.99, 14, 1], [4.98, 14, 1], [4.98, 14, 1], [4.84, 14, 1], [4.84, 14, 1], [9.18, 14, 1], [5.01, 14, 1], [9.18, 14, 1], [4.84, 14, 1], [7.17, 14, 1], [7.17, 14, 1], [14, 14, 1], [14, 14, 1], [14, 14, 1], [14, 14, 1], [14, 14, 1], [14, 14, 1], [14, 14, 1], [14, 14, 1], [14, 14, 1], [14, 14, 1], [14, 14, 1], [14, 14, 1], [14, 14, 1], [3.36, 14, 1], [4.86, 14, 1], [4.86, 14, 1], [11.62, 14, 1], [11.62, 14, 1], [14, 14, 1], [12.51, 14, 1], [14, 14, 1], [14, 14, 1]];
//     // tslint:disable-next-line: max-line-length
//     const hiragino16 = ['Hiragino Sans GB16', [16, 16, 1], [3.84, 16, 1], [10.51, 16, 1], [10.51, 16, 1], [10.51, 16, 1], [10.51, 16, 1], [10.58, 16, 1], [10.51, 16, 1], [10.51, 16, 1], [10.51, 16, 1], [10.51, 16, 1], [10.51, 16, 1], [9.2, 16, 1], [10.34, 16, 1], [9.25, 16, 1], [10.34, 16, 1], [9.41, 16, 1], [6.67, 16, 1], [10.26, 16, 1], [9.87, 16, 1], [3.81, 16, 1], [4.24, 16, 1], [9.2, 16, 1], [3.71, 16, 1], [14.9, 16, 1], [9.89, 16, 1], [10, 16, 1], [10.35, 16, 1], [10.34, 16, 1], [6.88, 16, 1], [8.59, 16, 1], [6.27, 16, 1], [9.89, 16, 1], [8.7, 16, 1], [12.32, 16, 1], [8.58, 16, 1], [8.86, 16, 1], [7.97, 16, 1], [12, 16, 1], [11.39, 16, 1], [12.1, 16, 1], [12.13, 16, 1], [10.29, 16, 1], [9.63, 16, 1], [12.06, 16, 1], [12.45, 16, 1], [3.98, 16, 1], [8.13, 16, 1], [11.31, 16, 1], [9.63, 16, 1], [15.12, 16, 1], [12.38, 16, 1], [12.72, 16, 1], [10.59, 16, 1], [12.72, 16, 1], [11.62, 16, 1], [10.7, 16, 1], [10.38, 16, 1], [12.37, 16, 1], [11.47, 16, 1], [16.1, 16, 1], [11.55, 16, 1], [11.01, 16, 1], [10.7, 16, 1], [3.84, 16, 1], [3.84, 16, 1], [3.84, 16, 1], [7.58, 16, 1], [9.14, 16, 1], [5.7, 16, 1], [5.7, 16, 1], [5.54, 16, 1], [5.54, 16, 1], [10.5, 16, 1], [5.73, 16, 1], [10.5, 16, 1], [5.54, 16, 1], [8.19, 16, 1], [8.19, 16, 1], [16, 16, 1], [16, 16, 1], [16, 16, 1], [16, 16, 1], [16, 16, 1], [16, 16, 1], [16, 16, 1], [16, 16, 1], [16, 16, 1], [16, 16, 1], [16, 16, 1], [16, 16, 1], [16, 16, 1], [3.84, 16, 1], [5.55, 16, 1], [5.55, 16, 1], [13.28, 16, 1], [13.28, 16, 1], [16, 16, 1], [14.08, 16, 1], [16, 16, 1], [16, 16, 1]];
//     // tslint:disable-next-line: max-line-length
//     const hiragino187 = ['Hiragino Sans GB18.7', [18.7, 18, 1], [4.49, 18, 1], [12.29, 18, 1], [12.29, 18, 1], [12.29, 18, 1], [12.29, 18, 1], [12.36, 18, 1], [12.29, 18, 1], [12.29, 18, 1], [12.29, 18, 1], [12.29, 18, 1], [12.29, 18, 1], [10.75, 18, 1], [12.08, 18, 1], [10.81, 18, 1], [12.08, 18, 1], [11, 18, 1], [7.8, 18, 1], [11.99, 18, 1], [11.54, 18, 1], [4.45, 18, 1], [4.96, 18, 1], [10.75, 18, 1], [4.34, 18, 1], [17.41, 18, 1], [11.56, 18, 1], [11.69, 18, 1], [12.1, 18, 1], [12.08, 18, 1], [8.04, 18, 1], [10.04, 18, 1], [7.33, 18, 1], [11.56, 18, 1], [10.17, 18, 1], [14.4, 18, 1], [10.02, 18, 1], [10.36, 18, 1], [9.31, 18, 1], [14.02, 18, 1], [13.31, 18, 1], [14.14, 18, 1], [14.17, 18, 1], [12.02, 18, 1], [11.26, 18, 1], [14.1, 18, 1], [14.55, 18, 1], [4.66, 18, 1], [9.5, 18, 1], [13.22, 18, 1], [11.26, 18, 1], [17.67, 18, 1], [14.47, 18, 1], [14.87, 18, 1], [12.38, 18, 1], [14.87, 18, 1], [13.58, 18, 1], [12.51, 18, 1], [12.14, 18, 1], [14.46, 18, 1], [13.41, 18, 1], [18.81, 18, 1], [13.5, 18, 1], [12.87, 18, 1], [12.51, 18, 1], [4.49, 18, 1], [4.49, 18, 1], [4.49, 18, 1], [8.86, 18, 1], [10.68, 18, 1], [6.66, 18, 1], [6.66, 18, 1], [6.47, 18, 1], [6.47, 18, 1], [12.27, 18, 1], [6.69, 18, 1], [12.27, 18, 1], [6.47, 18, 1], [9.57, 18, 1], [9.57, 18, 1], [18.7, 18, 1], [18.7, 18, 1], [18.7, 18, 1], [18.7, 18, 1], [18.7, 18, 1], [18.7, 18, 1], [18.7, 18, 1], [18.7, 18, 1], [18.7, 18, 1], [18.7, 18, 1], [18.7, 18, 1], [18.7, 18, 1], [18.7, 18, 1], [4.49, 18, 1], [6.49, 18, 1], [6.49, 18, 1], [15.52, 18, 1], [15.52, 18, 1], [18.7, 18, 1], [16.25, 18, 1], [18.7, 18, 1], [18.7, 18, 1]];
//     // tslint:disable-next-line: max-line-length
//     const hiragino20 = ['Hiragino Sans GB20', [20, 20, 1], [4.8, 20, 1], [13.14, 20, 1], [13.14, 20, 1], [13.14, 20, 1], [13.14, 20, 1], [13.22, 20, 1], [13.14, 20, 1], [13.14, 20, 1], [13.14, 20, 1], [13.14, 20, 1], [13.14, 20, 1], [11.5, 20, 1], [12.92, 20, 1], [11.56, 20, 1], [12.92, 20, 1], [11.76, 20, 1], [8.34, 20, 1], [12.82, 20, 1], [12.34, 20, 1], [4.76, 20, 1], [5.3, 20, 1], [11.5, 20, 1], [4.64, 20, 1], [18.62, 20, 1], [12.36, 20, 1], [12.5, 20, 1], [12.94, 20, 1], [12.92, 20, 1], [8.6, 20, 1], [10.74, 20, 1], [7.84, 20, 1], [12.36, 20, 1], [10.88, 20, 1], [15.4, 20, 1], [10.72, 20, 1], [11.08, 20, 1], [9.96, 20, 1], [15, 20, 1], [14.24, 20, 1], [15.12, 20, 1], [15.16, 20, 1], [12.86, 20, 1], [12.04, 20, 1], [15.08, 20, 1], [15.56, 20, 1], [4.98, 20, 1], [10.16, 20, 1], [14.14, 20, 1], [12.04, 20, 1], [18.9, 20, 1], [15.48, 20, 1], [15.9, 20, 1], [13.24, 20, 1], [15.9, 20, 1], [14.52, 20, 1], [13.38, 20, 1], [12.98, 20, 1], [15.46, 20, 1], [14.34, 20, 1], [20.12, 20, 1], [14.44, 20, 1], [13.76, 20, 1], [13.38, 20, 1], [4.8, 20, 1], [4.8, 20, 1], [4.8, 20, 1], [9.48, 20, 1], [11.42, 20, 1], [7.12, 20, 1], [7.12, 20, 1], [6.92, 20, 1], [6.92, 20, 1], [13.12, 20, 1], [7.16, 20, 1], [13.12, 20, 1], [6.92, 20, 1], [10.24, 20, 1], [10.24, 20, 1], [20, 20, 1], [20, 20, 1], [20, 20, 1], [20, 20, 1], [20, 20, 1], [20, 20, 1], [20, 20, 1], [20, 20, 1], [20, 20, 1], [20, 20, 1], [20, 20, 1], [20, 20, 1], [20, 20, 1], [4.8, 20, 1], [6.94, 20, 1], [6.94, 20, 1], [16.6, 20, 1], [16.6, 20, 1], [20, 20, 1], [17.6, 20, 1], [20, 20, 1], [20, 20, 1]];
//     // tslint:disable-next-line: max-line-length
//     const hiragino213 = ['Hiragino Sans GB21.3', [21.3, 22, 1], [5.11, 22, 1], [13.99, 22, 1], [13.99, 22, 1], [13.99, 22, 1], [13.99, 22, 1], [14.08, 22, 1], [13.99, 22, 1], [13.99, 22, 1], [13.99, 22, 1], [13.99, 22, 1], [13.99, 22, 1], [12.25, 22, 1], [13.76, 22, 1], [12.31, 22, 1], [13.76, 22, 1], [12.52, 22, 1], [8.88, 22, 1], [13.65, 22, 1], [13.14, 22, 1], [5.07, 22, 1], [5.64, 22, 1], [12.25, 22, 1], [4.94, 22, 1], [19.83, 22, 1], [13.16, 22, 1], [13.31, 22, 1], [13.78, 22, 1], [13.76, 22, 1], [9.16, 22, 1], [11.44, 22, 1], [8.35, 22, 1], [13.16, 22, 1], [11.59, 22, 1], [16.4, 22, 1], [11.42, 22, 1], [11.8, 22, 1], [10.61, 22, 1], [15.97, 22, 1], [15.17, 22, 1], [16.1, 22, 1], [16.15, 22, 1], [13.7, 22, 1], [12.82, 22, 1], [16.06, 22, 1], [16.57, 22, 1], [5.3, 22, 1], [10.82, 22, 1], [15.06, 22, 1], [12.82, 22, 1], [20.13, 22, 1], [16.49, 22, 1], [16.93, 22, 1], [14.1, 22, 1], [16.93, 22, 1], [15.46, 22, 1], [14.25, 22, 1], [13.82, 22, 1], [16.46, 22, 1], [15.27, 22, 1], [21.43, 22, 1], [15.38, 22, 1], [14.65, 22, 1], [14.25, 22, 1], [5.11, 22, 1], [5.11, 22, 1], [5.11, 22, 1], [10.1, 22, 1], [12.16, 22, 1], [7.58, 22, 1], [7.58, 22, 1], [7.37, 22, 1], [7.37, 22, 1], [13.97, 22, 1], [7.63, 22, 1], [13.97, 22, 1], [7.37, 22, 1], [10.91, 22, 1], [10.91, 22, 1], [21.3, 22, 1], [21.3, 22, 1], [21.3, 22, 1], [21.3, 22, 1], [21.3, 22, 1], [21.3, 22, 1], [21.3, 22, 1], [21.3, 22, 1], [21.3, 22, 1], [21.3, 22, 1], [21.3, 22, 1], [21.3, 22, 1], [21.3, 22, 1], [5.11, 22, 1], [7.39, 22, 1], [7.39, 22, 1], [17.68, 22, 1], [17.68, 22, 1], [21.3, 22, 1], [18.69, 22, 1], [21.3, 22, 1], [21.3, 22, 1]];
//     // tslint:disable-next-line: max-line-length
//     const hiragino24 = ['Hiragino Sans GB24', [24, 24, 1], [5.76, 24, 1], [15.77, 24, 1], [15.77, 24, 1], [15.77, 24, 1], [15.77, 24, 1], [15.86, 24, 1], [15.77, 24, 1], [15.77, 24, 1], [15.77, 24, 1], [15.77, 24, 1], [15.77, 24, 1], [13.8, 24, 1], [15.5, 24, 1], [13.87, 24, 1], [15.5, 24, 1], [14.11, 24, 1], [10.01, 24, 1], [15.38, 24, 1], [14.81, 24, 1], [5.71, 24, 1], [6.36, 24, 1], [13.8, 24, 1], [5.57, 24, 1], [22.34, 24, 1], [14.83, 24, 1], [15, 24, 1], [15.53, 24, 1], [15.5, 24, 1], [10.32, 24, 1], [12.89, 24, 1], [9.41, 24, 1], [14.83, 24, 1], [13.06, 24, 1], [18.48, 24, 1], [12.86, 24, 1], [13.3, 24, 1], [11.95, 24, 1], [18, 24, 1], [17.09, 24, 1], [18.14, 24, 1], [18.19, 24, 1], [15.43, 24, 1], [14.45, 24, 1], [18.1, 24, 1], [18.67, 24, 1], [5.98, 24, 1], [12.19, 24, 1], [16.97, 24, 1], [14.45, 24, 1], [22.68, 24, 1], [18.58, 24, 1], [19.08, 24, 1], [15.89, 24, 1], [19.08, 24, 1], [17.42, 24, 1], [16.06, 24, 1], [15.58, 24, 1], [18.55, 24, 1], [17.21, 24, 1], [24.14, 24, 1], [17.33, 24, 1], [16.51, 24, 1], [16.06, 24, 1], [5.76, 24, 1], [5.76, 24, 1], [5.76, 24, 1], [11.38, 24, 1], [13.7, 24, 1], [8.54, 24, 1], [8.54, 24, 1], [8.3, 24, 1], [8.3, 24, 1], [15.74, 24, 1], [8.59, 24, 1], [15.74, 24, 1], [8.3, 24, 1], [12.29, 24, 1], [12.29, 24, 1], [24, 24, 1], [24, 24, 1], [24, 24, 1], [24, 24, 1], [24, 24, 1], [24, 24, 1], [24, 24, 1], [24, 24, 1], [24, 24, 1], [24, 24, 1], [24, 24, 1], [24, 24, 1], [24, 24, 1], [5.76, 24, 1], [8.33, 24, 1], [8.33, 24, 1], [19.92, 24, 1], [19.92, 24, 1], [24, 24, 1], [20.93, 24, 1], [24, 24, 1], [24, 24, 1]];
//     // tslint:disable-next-line: max-line-length
//     const hiragino293 = ['Hiragino Sans GB29.3', [29.3, 30, 1], [7.03, 30, 1], [19.25, 30, 1], [19.25, 30, 1], [19.25, 30, 1], [19.25, 30, 1], [19.37, 30, 1], [19.25, 30, 1], [19.25, 30, 1], [19.25, 30, 1], [19.25, 30, 1], [19.25, 30, 1], [16.85, 30, 1], [18.93, 30, 1], [16.94, 30, 1], [18.93, 30, 1], [17.23, 30, 1], [12.22, 30, 1], [18.78, 30, 1], [18.08, 30, 1], [6.97, 30, 1], [7.76, 30, 1], [16.85, 30, 1], [6.8, 30, 1], [27.28, 30, 1], [18.11, 30, 1], [18.31, 30, 1], [18.96, 30, 1], [18.93, 30, 1], [12.6, 30, 1], [15.73, 30, 1], [11.49, 30, 1], [18.11, 30, 1], [15.94, 30, 1], [22.56, 30, 1], [15.7, 30, 1], [16.23, 30, 1], [14.59, 30, 1], [21.97, 30, 1], [20.86, 30, 1], [22.15, 30, 1], [22.21, 30, 1], [18.84, 30, 1], [17.64, 30, 1], [22.09, 30, 1], [22.8, 30, 1], [7.3, 30, 1], [14.88, 30, 1], [20.72, 30, 1], [17.64, 30, 1], [27.69, 30, 1], [22.68, 30, 1], [23.29, 30, 1], [19.4, 30, 1], [23.29, 30, 1], [21.27, 30, 1], [19.6, 30, 1], [19.02, 30, 1], [22.65, 30, 1], [21.01, 30, 1], [29.48, 30, 1], [21.15, 30, 1], [20.16, 30, 1], [19.6, 30, 1], [7.03, 30, 1], [7.03, 30, 1], [7.03, 30, 1], [13.89, 30, 1], [16.73, 30, 1], [10.43, 30, 1], [10.43, 30, 1], [10.14, 30, 1], [10.14, 30, 1], [19.22, 30, 1], [10.49, 30, 1], [19.22, 30, 1], [10.14, 30, 1], [15, 30, 1], [15, 30, 1], [29.3, 30, 1], [29.3, 30, 1], [29.3, 30, 1], [29.3, 30, 1], [29.3, 30, 1], [29.3, 30, 1], [29.3, 30, 1], [29.3, 30, 1], [29.3, 30, 1], [29.3, 30, 1], [29.3, 30, 1], [29.3, 30, 1], [29.3, 30, 1], [7.03, 30, 1], [10.17, 30, 1], [10.17, 30, 1], [24.32, 30, 1], [24.32, 30, 1], [29.3, 30, 1], [25.39, 30, 1], [29.3, 30, 1], [29.3, 30, 1]];
//     // tslint:disable-next-line: max-line-length
//     const hiragino32 = ['Hiragino Sans GB32', [32, 32, 1], [7.68, 32, 1], [21.02, 32, 1], [21.02, 32, 1], [21.02, 32, 1], [21.02, 32, 1], [21.15, 32, 1], [21.02, 32, 1], [21.02, 32, 1], [21.02, 32, 1], [21.02, 32, 1], [21.02, 32, 1], [18.4, 32, 1], [20.67, 32, 1], [18.5, 32, 1], [20.67, 32, 1], [18.82, 32, 1], [13.34, 32, 1], [20.51, 32, 1], [19.74, 32, 1], [7.62, 32, 1], [8.48, 32, 1], [18.4, 32, 1], [7.42, 32, 1], [29.79, 32, 1], [19.78, 32, 1], [20, 32, 1], [20.7, 32, 1], [20.67, 32, 1], [13.76, 32, 1], [17.18, 32, 1], [12.54, 32, 1], [19.78, 32, 1], [17.41, 32, 1], [24.64, 32, 1], [17.15, 32, 1], [17.73, 32, 1], [15.94, 32, 1], [24, 32, 1], [22.78, 32, 1], [24.19, 32, 1], [24.26, 32, 1], [20.58, 32, 1], [19.26, 32, 1], [24.13, 32, 1], [24.9, 32, 1], [7.97, 32, 1], [16.26, 32, 1], [22.62, 32, 1], [19.26, 32, 1], [30.24, 32, 1], [24.77, 32, 1], [25.44, 32, 1], [21.18, 32, 1], [25.44, 32, 1], [23.23, 32, 1], [21.41, 32, 1], [20.77, 32, 1], [24.74, 32, 1], [22.94, 32, 1], [32.19, 32, 1], [23.1, 32, 1], [22.02, 32, 1], [21.41, 32, 1], [7.68, 32, 1], [7.68, 32, 1], [7.68, 32, 1], [15.17, 32, 1], [18.27, 32, 1], [11.39, 32, 1], [11.39, 32, 1], [11.07, 32, 1], [11.07, 32, 1], [20.99, 32, 1], [11.46, 32, 1], [20.99, 32, 1], [11.07, 32, 1], [16.38, 32, 1], [16.38, 32, 1], [32, 32, 1], [32, 32, 1], [32, 32, 1], [32, 32, 1], [32, 32, 1], [32, 32, 1], [32, 32, 1], [32, 32, 1], [32, 32, 1], [32, 32, 1], [32, 32, 1], [32, 32, 1], [32, 32, 1], [7.68, 32, 1], [11.1, 32, 1], [11.1, 32, 1], [26.56, 32, 1], [26.56, 32, 1], [32, 32, 1], [27.66, 32, 1], [32, 32, 1], [32, 32, 1]];
//     // tslint:disable-next-line: max-line-length
//     const hiragino347 = ['Hiragino Sans GB34.7', [34.7, 35, 1], [8.33, 35, 1], [22.8, 35, 1], [22.8, 35, 1], [22.8, 35, 1], [22.8, 35, 1], [22.94, 35, 1], [22.8, 35, 1], [22.8, 35, 1], [22.8, 35, 1], [22.8, 35, 1], [22.8, 35, 1], [19.95, 35, 1], [22.42, 35, 1], [20.06, 35, 1], [22.42, 35, 1], [20.4, 35, 1], [14.47, 35, 1], [22.24, 35, 1], [21.41, 35, 1], [8.26, 35, 1], [9.2, 35, 1], [19.95, 35, 1], [8.05, 35, 1], [32.31, 35, 1], [21.44, 35, 1], [21.69, 35, 1], [22.45, 35, 1], [22.42, 35, 1], [14.92, 35, 1], [18.63, 35, 1], [13.6, 35, 1], [21.44, 35, 1], [18.88, 35, 1], [26.72, 35, 1], [18.6, 35, 1], [19.22, 35, 1], [17.28, 35, 1], [26.02, 35, 1], [24.71, 35, 1], [26.23, 35, 1], [26.3, 35, 1], [22.31, 35, 1], [20.89, 35, 1], [26.16, 35, 1], [27, 35, 1], [8.64, 35, 1], [17.63, 35, 1], [24.53, 35, 1], [20.89, 35, 1], [32.79, 35, 1], [26.86, 35, 1], [27.59, 35, 1], [22.97, 35, 1], [27.59, 35, 1], [25.19, 35, 1], [23.21, 35, 1], [22.52, 35, 1], [26.82, 35, 1], [24.88, 35, 1], [34.91, 35, 1], [25.05, 35, 1], [23.87, 35, 1], [23.21, 35, 1], [8.33, 35, 1], [8.33, 35, 1], [8.33, 35, 1], [16.45, 35, 1], [19.81, 35, 1], [12.35, 35, 1], [12.35, 35, 1], [12.01, 35, 1], [12.01, 35, 1], [22.76, 35, 1], [12.42, 35, 1], [22.76, 35, 1], [12.01, 35, 1], [17.77, 35, 1], [17.77, 35, 1], [34.7, 35, 1], [34.7, 35, 1], [34.7, 35, 1], [34.7, 35, 1], [34.7, 35, 1], [34.7, 35, 1], [34.7, 35, 1], [34.7, 35, 1], [34.7, 35, 1], [34.7, 35, 1], [34.7, 35, 1], [34.7, 35, 1], [34.7, 35, 1], [8.33, 35, 1], [12.04, 35, 1], [12.04, 35, 1], [28.8, 35, 1], [28.8, 35, 1], [34.7, 35, 1], [29.91, 35, 1], [34.7, 35, 1], [34.7, 35, 1]];
//     // tslint:disable-next-line: max-line-length
//     const hiragino48 = ['Hiragino Sans GB48', [48, 48, 1], [11.52, 48, 1], [31.54, 48, 1], [31.54, 48, 1], [31.54, 48, 1], [31.54, 48, 1], [31.73, 48, 1], [31.54, 48, 1], [31.54, 48, 1], [31.54, 48, 1], [31.54, 48, 1], [31.54, 48, 1], [27.6, 48, 1], [31.01, 48, 1], [27.74, 48, 1], [31.01, 48, 1], [28.22, 48, 1], [20.02, 48, 1], [30.77, 48, 1], [29.62, 48, 1], [11.42, 48, 1], [12.72, 48, 1], [27.6, 48, 1], [11.14, 48, 1], [44.69, 48, 1], [29.66, 48, 1], [30, 48, 1], [31.06, 48, 1], [31.01, 48, 1], [20.64, 48, 1], [25.78, 48, 1], [18.82, 48, 1], [29.66, 48, 1], [26.11, 48, 1], [36.96, 48, 1], [25.73, 48, 1], [26.59, 48, 1], [23.9, 48, 1], [36, 48, 1], [34.18, 48, 1], [36.29, 48, 1], [36.38, 48, 1], [30.86, 48, 1], [28.9, 48, 1], [36.19, 48, 1], [37.34, 48, 1], [11.95, 48, 1], [24.38, 48, 1], [33.94, 48, 1], [28.9, 48, 1], [45.36, 48, 1], [37.15, 48, 1], [38.16, 48, 1], [31.78, 48, 1], [38.16, 48, 1], [34.85, 48, 1], [32.11, 48, 1], [31.15, 48, 1], [37.1, 48, 1], [34.42, 48, 1], [48.29, 48, 1], [34.66, 48, 1], [33.02, 48, 1], [32.11, 48, 1], [11.52, 48, 1], [11.52, 48, 1], [11.52, 48, 1], [22.75, 48, 1], [27.41, 48, 1], [17.09, 48, 1], [17.09, 48, 1], [16.61, 48, 1], [16.61, 48, 1], [31.49, 48, 1], [17.18, 48, 1], [31.49, 48, 1], [16.61, 48, 1], [24.58, 48, 1], [24.58, 48, 1], [48, 48, 1], [48, 48, 1], [48, 48, 1], [48, 48, 1], [48, 48, 1], [48, 48, 1], [48, 48, 1], [48, 48, 1], [48, 48, 1], [48, 48, 1], [48, 48, 1], [48, 48, 1], [48, 48, 1], [11.52, 48, 1], [16.66, 48, 1], [16.66, 48, 1], [39.84, 48, 1], [39.84, 48, 1], [48, 48, 1], [41.27, 48, 1], [48, 48, 1], [48, 48, 1]];
//     // tslint:disable-next-line: max-line-length
//     const hiragino587 = ['Hiragino Sans GB58.7', [58.7, 59, 1], [14.09, 59, 1], [38.57, 59, 1], [38.57, 59, 1], [38.57, 59, 1], [38.57, 59, 1], [38.8, 59, 1], [38.57, 59, 1], [38.57, 59, 1], [38.57, 59, 1], [38.57, 59, 1], [38.57, 59, 1], [33.75, 59, 1], [37.92, 59, 1], [33.93, 59, 1], [37.92, 59, 1], [34.52, 59, 1], [24.48, 59, 1], [37.63, 59, 1], [36.22, 59, 1], [13.97, 59, 1], [15.56, 59, 1], [33.75, 59, 1], [13.62, 59, 1], [54.65, 59, 1], [36.28, 59, 1], [36.69, 59, 1], [37.98, 59, 1], [37.92, 59, 1], [25.24, 59, 1], [31.52, 59, 1], [23.01, 59, 1], [36.28, 59, 1], [31.93, 59, 1], [45.2, 59, 1], [31.46, 59, 1], [32.52, 59, 1], [29.23, 59, 1], [44.02, 59, 1], [41.79, 59, 1], [44.38, 59, 1], [44.49, 59, 1], [37.74, 59, 1], [35.34, 59, 1], [44.26, 59, 1], [45.67, 59, 1], [14.62, 59, 1], [29.82, 59, 1], [41.5, 59, 1], [35.34, 59, 1], [55.47, 59, 1], [45.43, 59, 1], [46.67, 59, 1], [38.86, 59, 1], [46.67, 59, 1], [42.62, 59, 1], [39.27, 59, 1], [38.1, 59, 1], [45.38, 59, 1], [42.09, 59, 1], [59.05, 59, 1], [42.38, 59, 1], [40.39, 59, 1], [39.27, 59, 1], [14.09, 59, 1], [14.09, 59, 1], [14.09, 59, 1], [27.82, 59, 1], [33.52, 59, 1], [20.9, 59, 1], [20.9, 59, 1], [20.31, 59, 1], [20.31, 59, 1], [38.51, 59, 1], [21.01, 59, 1], [38.51, 59, 1], [20.31, 59, 1], [30.05, 59, 1], [30.05, 59, 1], [58.7, 59, 1], [58.7, 59, 1], [58.7, 59, 1], [58.7, 59, 1], [58.7, 59, 1], [58.7, 59, 1], [58.7, 59, 1], [58.7, 59, 1], [58.7, 59, 1], [58.7, 59, 1], [58.7, 59, 1], [58.7, 59, 1], [58.7, 59, 1], [14.09, 59, 1], [20.37, 59, 1], [20.37, 59, 1], [48.73, 59, 1], [48.73, 59, 1], [58.7, 59, 1], [50.39, 59, 1], [58.7, 59, 1], [58.7, 59, 1]];

//     const hiraginoFonts = [hiragino12, hiragino14, hiragino16, hiragino187, hiragino20, hiragino213, hiragino24,
//         hiragino293, hiragino32, hiragino347, hiragino48, hiragino587];

//     // aggregate
//     const macFontsData = stSongFonts.concat(stHeitiFonts, fangSongFonts, kaitiFonts, hiraginoFonts);

//     for (const macFontsDatum of macFontsData) {
//         const curFontTemplate = JSON.parse(JSON.stringify(stSong16Template));
//         for (let i = 1, len = macFontsDatum.length; i < len; i++) {
//             const itemList = macFontsDatum[i];
//             const templateItem = curFontTemplate[i - 1];
//             for (let j = 0, listLen = itemList.length; j < listLen; j++) {
//                 switch (j) {
//                     case 0 : {
//                         // if (i === 91) {
//                         //     console.log(templateItem)
//                         //     console.log(itemList)
//                         // }
//                         // paraEnd has no width!
//                         templateItem[1].width = (i === 91) ? 0 : itemList[j];
//                         break;
//                     }
//                     case 1: {
//                         templateItem[1].height = itemList[j];
//                         break;
//                     }
//                     case 2: {
//                         templateItem[1].type = itemList[j];
//                         break;
//                     }
//                     default: {
//                         break;
//                     }
//                 }
//             }
//         }
//         CACHES.set(
//             (macFontsDatum[0] as string), new Map(curFontTemplate)
//         );

//     }

//     // let t2 = performance.now();
//     // console.log(t2 - t1)
//     // console.log(CACHES)
// }
