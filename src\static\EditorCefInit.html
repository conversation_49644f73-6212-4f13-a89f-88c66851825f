<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
        }

        #editor-container {
            height: 100%;
            width: 100%;
            margin: 0;
            padding: 0;
        }
    </style>
    <script src="sdk.js"></script>
</head>
<body style="margin: 0;height: 100vh">
    <div id="editor-container"></div>
<script>
    (function () {
        window.onerror = (e) => {
            jsCallback.onerror(e);
        }
        let editor;
        let vm;
        function base64ToBlob(base64) {
            if (!base64 || typeof base64 !== 'string') {
                return;
            }
            const mimeString = base64.split(',')[0]
            .split(':')[1]
            .split(';')[0];
            const byteString = atob(base64.split(',')[1]);
            const arrayBuffer = new ArrayBuffer(byteString.length); 
            const intArray = new Uint8Array(arrayBuffer);
            for (let i = 0; i < byteString.length; i++) {
                intArray[i] = byteString.charCodeAt(i);
            }
            return new Blob([intArray], {type: mimeString});
        }

        function blobToBase64(blob) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.readAsDataURL(blob);
                reader.onloadend = () => {
                    resolve(reader.result);
                };

                reader.onerror = (e) => {
                    console.log(e);
                    reject(e.message);
                };
            });
        }

        // function callEvent(name) {
        //     if (!jsCallback) {
        //         return;
        //     }
        //     var arrs = Array.from(arguments);
        //     arrs.unshift(arguments.callee.name)
        //     jsCallback.callEvent.apply(undefined, arrs);
        // }

        // const blobObj = {
        //     base64ToBlob,
        //     blobToBase64
        // }

        // const events =  [
        //     'nsoFileOpenCompleted',
        //     'nsoKeyPressedEvent',
        //     'nsoStructClick',
        //     'nsoStructDBClick',
        //     'nsoStructGainFocus',
        //     'nsoStructLostFocus',
        //     'nsoStructChanged',
        //     'nsoRegionGainFocus',
        //     'nsoRegionLostFocus',
        //     'nsoRegionChanged',
        //     'nsoRegionDBClick',
        //     'nsoStructCheckChanged',
        //     'nsoFileModifyChanged',
        //     'nisCellDBClickEvent',
        //     'nisCellClickEvent',
        //     'nsoSendPrintDataCompleted',
        // ];

        function init(options) {
            // 防止没有返回
            const timeout = setTimeout(() => {
                jsCallback.callback('initEditor', 'false', 1);
                console.log('editor running timeout...');
            }, 30000);
            return new Promise(async (resolve, reject) => {
                try {
                    vm = await new EmrEditorSDK.Editor().init(options);
                    editor = await vm.getEditor();
                } catch (error) {
                    jsCallback.callback('initEditor', 'false', 1);
                    jsCallback.onerror(error);
                    return;
                }
                clearTimeout(timeout);
                // const eventObj = {};
                // events.forEach((event) => {
                //     eventObj[event] = function () {
                //         var arrs = Array.from(arguments);
                //         arrs.unshift(event);
                //         jsCallback.callEvent.apply(undefined, arrs);
                //     };
                // });
                // vm.setEvent(eventObj);
                jsCallback.callback('initEditor', 'true', 1);
                resolve(1);
            });
        }

        window.registerEvent = function(bDelete, name) {
            if (bDelete) {
                vm.removeEvent([name]);
                return;
            }
            const event = {};
            event[name] = function () {
                var arrs = Array.from(arguments);
                const position = arrs[2];
                if (position && typeof position === 'object') {
                    arrs[2] = position.x + ':' + position.y + ':' + position.height;
                }
                arrs.unshift(name);
                jsCallback.callEvent.apply(undefined, arrs);
            }
            vm.setEvent(event);
        }

        window.initEditor = function(option) {
            if (option) {
                try {
                    option = JSON.parse(option);
                } catch (error) {
                    option = {};
                }
            } else {
                option = {};
            }
            var version = option.version || (EmrEditorSDK && EmrEditorSDK.editorVersion);
            if (version) {
                version = '?v=' + version;
            } else {
                version = '';
            }
            option.src = '/index.html' + version;
            option.option = option.option || {};
            init({
                id: 'test-iframe',
                dom: document.getElementById('editor-container'),
                src: option.src,
                option: option.option
            });
        }

        const keys = {
            getStructsXmlInfoByFile: {
                keys: [0]
            },
            getStructsXmlInfoByFile2: {
                keys: [0]
            },
            openDocumentWithStream: {
                keys: [0]
            },
            setRegionFileLinkWithStream: {
                keys: [1]
            },
            insertFileWithStream: {
                keys: [0]
            },
            mergeDocumentsWithStream: {
                keys: [0]
            },
            replaceSpecificRegionPropWithBackStream: {
                keys: [2]
            },
            getStructsXmlInfoByParament: {
                keys: [0]
            },
            replaceSpecificStructPropWithBackStream: {
                keys: [2]
            },
            replaceHeader: {
                keys: [0]
            },
            replaceFooter: {
                keys: [0]
            }
        };

        function parseBlob(key, args) {
            if (!args || !args.length) {
                return;
            }
            const obj = keys[key];
            if (obj) {
                const indexs = obj.keys;
                for (let index = 0, len = indexs.length; index < len; index++) {
                    const curIndex = indexs[index];
                    const text = args[curIndex];
                    const blob = base64ToBlob(text);
                    if (!blob) {
                        continue;
                    }
                    args[curIndex] = blob;  
                }
            }
        }

        window.myEditor = new Proxy({}, {
            get(target, key, hander) {
                // 防止没有返回
                const timeout = setTimeout(() => {
                    jsCallback.callback(key, '', 1);
                    console.log('没有返回的函数: ' + key);
                }, 15000);
                return async function (...args) {
                    parseBlob(key, args);
                    
                    let res = await editor[key].apply(undefined, args);
                    if (res instanceof Blob) {
                        res = await blobToBase64(res);
                    } else if (key === 'saveStructContentToStreamByArray' && res instanceof Map) {
                        let text = '';
                        for (const [name, blob] of res) {
                            text += name + '[' + await blobToBase64(blob) + ']';
                        }
                        res = text.slice(0, -1);
                    }
                    clearTimeout(timeout);
                    jsCallback.callback(key, String(res), 1);
                    return res;
                    // return await target[prop].apply(target, args);
                };
            }
        }); 

        jsCallback.pageLoaded();
    })();
    

    // console.log(getEditor());
</script>
</body>
</html>