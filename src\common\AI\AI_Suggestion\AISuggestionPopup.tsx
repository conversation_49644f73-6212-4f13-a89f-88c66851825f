import * as React from 'react';
import './AISuggestionPopup.css';
import { AITextGenerationParams, AITextGenerationResponse } from './AITextGenerationTypes';

interface AISuggestionPopupProps {
  editorContainer: HTMLElement;
  initialSuggestions?: string[];
  textGenerationParams?: AITextGenerationParams;
  initialPosition?: { x: number; y: number };
  containerWidth: number;
  onAccept: (item: string) => void;
  onCancel: () => void;
  onSelect?: (index: number) => void;
}

interface ShowParams {
  suggestions?: string[];
  position: { x: number; y: number };
  containerWidth: number;
  onAccept: (item: string) => void;
  onCancel: () => void;
  onSelect?: (index: number) => void;
  // 文本生成参数
  textGenerationParams?: AITextGenerationParams;
}

interface AISuggestionPopupState {
  suggestions: string[];
  highlightedIndex: number;
  visible: boolean;
  position: { left: number; top: number };
  isLoading: boolean;
  error: string | null;
}

/**
 * AI建议弹窗组件
 * 用于在编辑器中显示AI生成的文本建议，支持键盘和鼠标交互
 */
export class AISuggestionPopup extends React.Component<AISuggestionPopupProps, AISuggestionPopupState> {
  private containerRef: React.RefObject<HTMLDivElement>;
  private popupWidth: number = 250; // 默认弹窗宽度，可根据实际情况调整
  private margin: number = 10; // 边距
  private onAcceptCallback: ((item: string) => void) | null = null;
  private onCancelCallback: (() => void) | null = null;
  private onSelectCallback: ((index: number) => void) | null = null;
  private isComposing: boolean = false; // 是否正在进行输入法组合输入

  constructor(props: AISuggestionPopupProps) {
    super(props);
    
    // 如果提供了初始建议和位置，则使用它们
    let position = { left: 0, top: 0 };
    if (props.initialPosition) {
      // 计算弹窗位置
      let left = props.initialPosition.x;
      const containerWidth = props.containerWidth || 800;
      if (props.initialPosition.x + this.popupWidth > containerWidth - this.margin) {
        // 如果右侧空间不足，改为右下对齐
        left = props.initialPosition.x - this.popupWidth;
      }
      position = { left, top: props.initialPosition.y };
    }
    
    this.state = {
      suggestions: props.initialSuggestions || [],
      highlightedIndex: -1,
      visible: props.initialSuggestions && props.initialSuggestions.length > 0,
      position,
      isLoading: false,
      error: null,
    };
    
    // 保存回调函数
    this.onAcceptCallback = props.onAccept || null;
    this.onCancelCallback = props.onCancel || null;
    this.onSelectCallback = props.onSelect || null;
    
    this.containerRef = React.createRef();

    // 绑定方法
    this.show = this.show.bind(this);
    this.hide = this.hide.bind(this);
    this.handleKeyDown = this.handleKeyDown.bind(this);
    this.handleItemClick = this.handleItemClick.bind(this);
    this.handleItemHover = this.handleItemHover.bind(this);
    this.handleClickOutside = this.handleClickOutside.bind(this);
    this.handleEditorInput = this.handleEditorInput.bind(this);
    this.handleCompositionStart = this.handleCompositionStart.bind(this);
    this.handleCompositionEnd = this.handleCompositionEnd.bind(this);
  }

  componentDidMount() {
    // 添加键盘事件监听器，使用捕获阶段（capture phase）
    // 这样可以在事件到达编辑器之前先处理它
    document.addEventListener('keydown', this.handleKeyDown, true);
    document.addEventListener('mousedown', this.handleClickOutside);
    document.addEventListener('input', this.handleEditorInput, true);
    document.addEventListener('compositionstart', this.handleCompositionStart, true);
    document.addEventListener('compositionend', this.handleCompositionEnd, true);
    
    // 如果提供了文本生成参数，则自动调用API获取生成结果
    if (this.props.textGenerationParams) {
      this.setState({ isLoading: true, error: null });
      
      // 调用API获取生成结果
      this.fetchSuggestions(this.props.textGenerationParams)
        .then(suggestions => {
          this.setState({
            suggestions,
            isLoading: false,
            error: null
          });
        })
        .catch(error => {
          this.setState({
            suggestions: ['生成文本失败，请重试'],
            isLoading: false,
            error: error instanceof Error ? error.message : '未知错误'
          });
        });
    }
  }

  componentWillUnmount() {
    // 移除键盘事件监听器，注意第三个参数必须与添加时一致
    document.removeEventListener('keydown', this.handleKeyDown, true);
    document.removeEventListener('mousedown', this.handleClickOutside);
    document.removeEventListener('input', this.handleEditorInput, true);
    document.removeEventListener('compositionstart', this.handleCompositionStart, true);
    document.removeEventListener('compositionend', this.handleCompositionEnd, true);
  }

  /**
   * 调用后端API获取文本生成结果
   * @param params 文本生成参数
   * @returns 生成的文本建议数组
   */
  private async fetchSuggestions(params: AITextGenerationParams): Promise<string[]> {
    try {
      const response = await fetch('http://localhost:7075/api/generate_text', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          patient_info: params.patientInfo,
          lab_data: params.labData,
          history_existing: params.historyExisting,
          need_generate_length: params.needGenerateLength
        })
      });
      
      const data: AITextGenerationResponse = await response.json();
      if (data.status === "success") {
        return data.data.choices;
      } else {
        throw new Error(data.message || '获取文本生成结果失败');
      }
    } catch (error) {
      console.error('调用文本生成API失败:', error);
      throw error;
    }
  }

  /**
   * 显示建议弹窗
   * @param params 显示参数
   */
  public async show(params: ShowParams): Promise<void> {
    const { suggestions, position, containerWidth, onAccept, onCancel, onSelect, textGenerationParams } = params;
    
    // 计算弹窗位置
    let left = position.x;
    if (position.x + this.popupWidth > containerWidth - this.margin) {
      // 如果右侧空间不足，改为右下对齐
      left = position.x - this.popupWidth;
    }

    // 保存回调函数
    this.onAcceptCallback = onAccept;
    this.onCancelCallback = onCancel;
    this.onSelectCallback = onSelect;

    // 首先显示加载状态
    const initialState = {
      suggestions: ['正在生成文本...'],
      highlightedIndex: -1,
      visible: true,
      position: { left, top: position.y },
      isLoading: true,
      error: null,
    };
    
    // 如果组件已挂载，使用 setState，否则直接设置 state
    if (this.containerRef.current) {
      this.setState(initialState);
    } else {
      this.state = initialState;
    }

    // 如果提供了文本生成参数，则调用API获取生成结果
    if (textGenerationParams) {
      try {
        const generatedSuggestions = await this.fetchSuggestions(textGenerationParams);
        
        // 更新状态，显示生成的建议
        const newState = {
          suggestions: generatedSuggestions,
          highlightedIndex: -1,
          visible: true,
          position: { left, top: position.y },
          isLoading: false,
          error: null,
        };
        
        if (this.containerRef.current) {
          this.setState(newState);
        } else {
          this.state = newState;
        }
      } catch (error) {
        // 处理错误情况
        const errorState = {
          suggestions: ['生成文本失败，请重试'],
          highlightedIndex: -1,
          visible: true,
          position: { left, top: position.y },
          isLoading: false,
          error: error instanceof Error ? error.message : '未知错误',
        };
        
        if (this.containerRef.current) {
          this.setState(errorState);
        } else {
          this.state = errorState;
        }
      }
    } else if (suggestions && suggestions.length > 0) {
      // 如果提供了现成的建议，直接显示
      const newState = {
        suggestions,
        highlightedIndex: -1,
        visible: true,
        position: { left, top: position.y },
        isLoading: false,
        error: null,
      };
      
      if (this.containerRef.current) {
        this.setState(newState);
      } else {
        this.state = newState;
      }
    }
  }

  /**
   * 隐藏建议弹窗
   * 只改变可见性，不卸载组件，便于再次显示
   */
  public hide(): void {
    if (this.state.visible) {
      this.setState({ visible: false });
    }
  }

  /**
   * 处理键盘事件
   * @param e 键盘事件
   */
  public handleKeyDown(e: KeyboardEvent): void {
    // 如果弹窗不可见，不处理任何键盘事件
    if (!this.state.visible) return;
    
    // 只对特定按键进行处理
    // 这些按键包括：方向键、Tab、Enter 和 Escape
    const specialKeys = ['Tab', 'ArrowDown', 'ArrowUp', 'Enter', 'Escape'];
    
    if (specialKeys.includes(e.key)) {
      // 对于特定按键，阻止默认行为和事件传播
      e.preventDefault();
      e.stopPropagation();
    }
    
    const { suggestions, highlightedIndex } = this.state;
    const suggestionsLength = suggestions.length;

    switch (e.key) {
      case 'Tab':
      case 'ArrowDown':
        this.setState({
          highlightedIndex: (highlightedIndex + 1) % suggestionsLength,
        });
        if (this.onSelectCallback) {
          this.onSelectCallback((highlightedIndex + 1) % suggestionsLength);
        }
        break;
      case 'ArrowUp':
        this.setState({
          highlightedIndex: (highlightedIndex - 1 + suggestionsLength) % suggestionsLength,
        });
        if (this.onSelectCallback) {
          this.onSelectCallback((highlightedIndex - 1 + suggestionsLength) % suggestionsLength);
        }
        break;
      case 'Enter':
        if (highlightedIndex >= 0 && highlightedIndex < suggestionsLength) {
          this.acceptSuggestion(suggestions[highlightedIndex]);
        }
        break;
      case 'Escape':
        this.cancelSuggestion();
        break;
      default:
        // 对于其他按键，不做任何处理
        // 允许事件继续传播到编辑器
        // 这样用户可以继续输入其他内容
        break;
    }
  }

  /**
   * 接受建议
   * @param suggestion 选中的建议
   */
  private acceptSuggestion(suggestion: string): void {
    if (this.onAcceptCallback) {
      this.onAcceptCallback(suggestion);
    }
    this.hide();
  }

  /**
   * 取消建议
   */
  private cancelSuggestion(): void {
    if (this.onCancelCallback) {
      this.onCancelCallback();
    }
    this.hide();
  }

  /**
   * 处理输入法组合开始事件
   * 当用户开始使用输入法输入时（如开始输入拼音）
   * @param e 组合开始事件
   */
  private handleCompositionStart(e: CompositionEvent): void {
    // 标记正在进行输入法组合输入
    this.isComposing = true;
    console.log('输入法组合开始');
  }

  /**
   * 处理输入法组合结束事件
   * 当用户完成输入法输入时（如选择了候选词）
   * @param e 组合结束事件
   */
  private handleCompositionEnd(e: CompositionEvent): void {
    // 标记输入法组合输入结束
    this.isComposing = false;
    console.log('输入法组合结束，文字已输入编辑器');
    
    // 如果弹窗可见，则隐藏
    if (this.state.visible) {
      console.log('输入法组合结束，隐藏AI建议弹窗');
      this.hide();
    }
  }

  /**
   * 处理编辑器输入事件
   * 当用户在编辑器中输入时，隐藏建议弹窗
   * @param e 输入事件
   */
  private handleEditorInput(e: Event): void {
    // 只有在非输入法组合状态下才处理input事件
    // 这样可以避免在中文输入过程中过早隐藏弹窗
    if (!this.isComposing && this.state.visible) {
      console.log('直接输入事件触发（非输入法组合），隐藏AI建议弹窗');
      this.hide();
    }
  }

  /**
   * 处理建议项点击事件
   * @param index 点击的建议项索引
   */
  private handleItemClick(index: number): void {
    const { suggestions } = this.state;
    if (index >= 0 && index < suggestions.length) {
      this.acceptSuggestion(suggestions[index]);
    }
  }

  /**
   * 处理建议项悬停事件
   * @param index 悬停的索引
   */
  private handleItemHover(index: number): void {
    this.setState({ highlightedIndex: index });
  }

  /**
   * 处理点击外部事件
   * @param e 鼠标事件
   */
  private handleClickOutside(e: MouseEvent): void {
    if (
      this.state.visible &&
      this.containerRef.current &&
      !this.containerRef.current.contains(e.target as Node)
    ) {
      this.cancelSuggestion();
    }
  }

  // render() {
  //   const { suggestions, highlightedIndex, visible, position, isLoading, error } = this.state;

  //   if (!visible) {
  //     return null;
  //   }

  //   return (
  //     <div
  //       className="ai-suggestion-popup"
  //       ref={this.containerRef}
  //       style={{
  //         left: `${position.left}px`,
  //       }}
  //     >
  //       <div className="ai-suggestion-list">
  //         {isLoading ? (
  //           // 加载状态显示
  //           <div className="ai-suggestion-item loading">
  //             正在生成文本...
  //             <span className="loading-spinner"></span>
  //           </div>
  //         ) : error ? (
  //           // 错误状态显示
  //           <div className="ai-suggestion-item error">
  //             {error}
  //             <button 
  //               className="error-retry" 
  //               onClick={() => {
  //                 if (this.props.textGenerationParams) {
  //                   this.setState({ isLoading: true, error: null });
  //                   this.fetchSuggestions(this.props.textGenerationParams)
  //                     .then(suggestions => {
  //                       this.setState({
  //                         suggestions,
  //                         isLoading: false,
  //                         error: null
  //                       });
  //                     })
  //                     .catch(error => {
  //                       this.setState({
  //                         isLoading: false,
  //                         error: error instanceof Error ? error.message : '未知错误'
  //                       });
  //                     });
  //                 }
  //               }}
  //             >
  //               重试
  //             </button>
  //           </div>
  //         ) : (
  //           // 正常显示建议列表
  //           suggestions.map((item, index) => (
  //             <div
  //               key={index}
  //               className={`ai-suggestion-item ${index === highlightedIndex ? 'highlighted' : ''}`}
  //               onClick={() => this.handleItemClick(index)}
  //               onMouseEnter={() => this.handleItemHover(index)}
  //             >
  //               {item}
  //             </div>
  //           ))
  //         )}
  //       </div>
  //     </div>
  //   );
  // }
  render() {
    const { suggestions, highlightedIndex, visible, position } = this.state;

    if (!visible) {
      return null;
    }

    return (
      <div
        className="ai-suggestion-popup"
        ref={this.containerRef}
        style={{
          left: `${position.left}px`,
          top: `${position.top}px`,
        }}
      >
        <div className="ai-suggestion-popup-content">
          {suggestions.map((suggestion, index) => (
            <div
              key={index}
              className={`ai-suggestion-item ${index === highlightedIndex ? 'highlighted' : ''}`}
              onClick={() => this.handleItemClick(index)}
              onMouseEnter={() => this.handleItemHover(index)}
            >
              {suggestion}
            </div>
          ))}
        </div>
      </div>
    );
  }
}

export default AISuggestionPopup;
