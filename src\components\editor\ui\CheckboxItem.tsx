import * as React from 'react';
import '../style/checkbox.less';

interface IState {
    bReflash: boolean;
}

interface IProps {
    onChange?: (value: any, name: string, e?: any) => void;
    value: boolean;
    disabled?: boolean;
    readonly?: boolean;
    name?: string;
    visible?: boolean;
    children?: React.ReactNode; // 添加 children 属性
}

export default class CheckboxItem extends React.Component<IProps, IState> {
    private value: boolean;
    private input: any;
    constructor(props: any) {
        super(props);
        this.state = {
            bReflash: false,
        };
        this.value = this.props.value;
        this.input = React.createRef();
    }

    public render(): any {
        const props = this.props;
        let className: string = '';
        if (this.value === true) {
            className = 'checkbox-icon-checked';
        }

        let className1 = 'checkbox-item';
        if (props.disabled === true) {
            className1 += ' disabled';
        }
        return (
            <div  className='editor-checkbox'>
                <span className={className1}>
                    <input type='checkbox' ref={this.input} tabIndex={-1} />
                    <i className={className} />
                    <label>{props.children}</label>
                </span>
            </div>
        );
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        this.value = nextProps.value;
    }

    public componentDidMount(): void {
        this.input.current.addEventListener('change', this.onChange);
    }

    public componentWillUnmount(): void {
        this.input.current?.removeEventListener('change', this.onChange);
    }

    private onChange = (e: any): void => {
        this.value = !this.value;
        const onChange = this.props.onChange;
        if (typeof onChange === 'function') {
            onChange(this.value, this.props.name, e);
        }
        this.setState({bReflash: !this.state.bReflash});
    }
}
