// 请不要直接修改TextMeasureSize.js， 修改完请在终端运行node setTextCache.js即可
import {CACHES} from './TextMeasureSize';
export function initIOSCaches() {
    CACHES.clear();

    const items = {"Times New Roman58.7":[['我'].map(i=> [i,58.7,65,1]),[' '].map(i=> [i,14.67,65,1]),['0','1','2','3','4','5','6','7','8','9','b','d','g','h','k','n','o','p','q','u','v','x','y','*'].map(i=> [i,29.35,65,1]),['a','c','e','z','?','“'].map(i=> [i,26.05,65,1]),['f','r','I','(',')','[',']','-','’','Ⅰ'].map(i=> [i,19.55,65,1]),['i','j','l','t',';','/',':'].map(i=> [i,16.31,65,1]),['m'].map(i=> [i,45.66,65,1]),['s','J'].map(i=> [i,22.84,65,1]),['w','A','D','G','H','K','N','O','Q','U','V','X','Y'].map(i=> [i,42.39,65,1]),['B','C','R'].map(i=> [i,39.15,65,1]),['E','L','T','Z'].map(i=> [i,35.86,65,1]),['F','P','S'].map(i=> [i,32.65,65,1]),['M'].map(i=> [i,52.19,65,1]),['W'].map(i=> [i,55.4,65,1]),[',','.'].map(i=> [i,14.68,65,1]),['=','+','<','>'].map(i=> [i,33.1,65,1]),['，','。','；','？','、','（','）','【','】','：','⊙','⭘'].map(i=> [i,58.7,65,1]),['Ⅱ'].map(i=> [i,37.95,65,1]),['↵'].map(i=> [i,0,65,1]),['{','}'].map(i=> [i,28.17,65,1]),['☑','☐'].map(i=> [i,50.56,65,1]),['◯'].map(i=> [i,63.89,65,1]),['℃'].map(i=> [i,60.1,65,1])],"Times New Roman48":[['我','，','。','；','？','、','（','）','【','】','：','⊙','⭘'].map(i=> [i,48,53,1]),[' ',',','.'].map(i=> [i,12,53,1]),['0','1','2','3','4','5','6','7','8','9','b','d','g','h','k','n','o','p','q','u','v','x','y','*'].map(i=> [i,24,53,1]),['a','c','e','z','?','“'].map(i=> [i,21.3,53,1]),['f','r','I','(',')','[',']','-','’','Ⅰ'].map(i=> [i,15.98,53,1]),['i','j','l','t',';','/',':'].map(i=> [i,13.34,53,1]),['m'].map(i=> [i,37.34,53,1]),['s','J'].map(i=> [i,18.68,53,1]),['w','A','D','G','H','K','N','O','Q','U','V','X','Y'].map(i=> [i,34.66,53,1]),['B','C','R'].map(i=> [i,32.02,53,1]),['E','L','T','Z'].map(i=> [i,29.32,53,1]),['F','P','S'].map(i=> [i,26.7,53,1]),['M'].map(i=> [i,42.68,53,1]),['W'].map(i=> [i,45.3,53,1]),['=','+','<','>'].map(i=> [i,27.07,53,1]),['Ⅱ'].map(i=> [i,31.03,53,1]),['↵'].map(i=> [i,0,53,1]),['{','}'].map(i=> [i,23.04,53,1]),['☑','☐'].map(i=> [i,41.34,53,1]),['◯'].map(i=> [i,52.24,53,1]),['℃'].map(i=> [i,49.15,53,1])],"Times New Roman37.3":[['我','，','。','；','？','、','（','）','【','】','：','⊙','⭘'].map(i=> [i,37.3,41,1]),[' ',',','.'].map(i=> [i,9.32,41,1]),['0','1','2','3','4','5','6','7','8','9','b','d','g','h','k','n','o','p','q','u','v','x','y','*'].map(i=> [i,18.65,41,1]),['a','c','e','z','?','“'].map(i=> [i,16.56,41,1]),['f','r','I','(',')','[',']','-','’','Ⅰ'].map(i=> [i,12.42,41,1]),['i','j','l','t',';','/',':'].map(i=> [i,10.36,41,1]),['m'].map(i=> [i,29.01,41,1]),['s','J'].map(i=> [i,14.52,41,1]),['w','A','D','G','H','K','N','O','Q','U','V','X','Y'].map(i=> [i,26.94,41,1]),['B','C','R'].map(i=> [i,24.88,41,1]),['E','L','T','Z'].map(i=> [i,22.78,41,1]),['F','P','S'].map(i=> [i,20.74,41,1]),['M'].map(i=> [i,33.17,41,1]),['W'].map(i=> [i,35.21,41,1]),['=','+','<','>'].map(i=> [i,21.04,41,1]),['Ⅱ'].map(i=> [i,24.11,41,1]),['↵'].map(i=> [i,0,41,1]),['{','}'].map(i=> [i,17.9,41,1]),['☑','☐'].map(i=> [i,32.13,41,1]),['◯'].map(i=> [i,40.6,41,1]),['℃'].map(i=> [i,38.19,41,1])],"Times New Roman34.7":[['我','，','。','；','？','、','（','）','【','】','：','⊙','⭘'].map(i=> [i,34.7,39,1]),[' '].map(i=> [i,8.67,39,1]),['0','1','2','3','4','5','6','7','8','9','b','d','g','h','k','n','o','p','q','u','v','x','y','*'].map(i=> [i,17.35,39,1]),['a','c','e','z','?','“'].map(i=> [i,15.4,39,1]),['f','r','I','(',')','[',']','-','’','Ⅰ'].map(i=> [i,11.56,39,1]),['i','j','l','t',';','/',':'].map(i=> [i,9.64,39,1]),['m'].map(i=> [i,26.99,39,1]),['s','J'].map(i=> [i,13.5,39,1]),['w','A','D','G','H','K','N','O','Q','U','V','X','Y'].map(i=> [i,25.06,39,1]),['B','C','R'].map(i=> [i,23.14,39,1]),['E','L','T','Z'].map(i=> [i,21.2,39,1]),['F','P','S'].map(i=> [i,19.3,39,1]),['M'].map(i=> [i,30.85,39,1]),['W'].map(i=> [i,32.75,39,1]),[',','.'].map(i=> [i,8.68,39,1]),['=','+','<','>'].map(i=> [i,19.57,39,1]),['Ⅱ'].map(i=> [i,22.43,39,1]),['↵'].map(i=> [i,0,39,1]),['{','}'].map(i=> [i,16.66,39,1]),['☑','☐'].map(i=> [i,29.89,39,1]),['◯'].map(i=> [i,37.77,39,1]),['℃'].map(i=> [i,35.53,39,1])],"Times New Roman32":[['我','，','。','；','？','、','（','）','【','】','⊙','⭘','：'].map(i=> [i,32,36,1]),[' ',',','.'].map(i=> [i,8,36,1]),['0','1','2','3','4','5','6','7','8','9','b','d','g','h','k','n','o','p','q','u','v','x','y','*'].map(i=> [i,16,36,1]),['a','c','e','z','?','“'].map(i=> [i,14.2,36,1]),['f','r','I','(',')','[',']','-','’','Ⅰ'].map(i=> [i,10.66,36,1]),['i','j','l','t',';','/',':'].map(i=> [i,8.89,36,1]),['m'].map(i=> [i,24.89,36,1]),['s','J'].map(i=> [i,12.45,36,1]),['w','A','D','G','H','K','N','O','Q','U','V','X','Y'].map(i=> [i,23.11,36,1]),['B','C','R'].map(i=> [i,21.34,36,1]),['E','L','T','Z'].map(i=> [i,19.55,36,1]),['F','P','S'].map(i=> [i,17.8,36,1]),['M'].map(i=> [i,28.45,36,1]),['W'].map(i=> [i,30.2,36,1]),['=','+','<','>'].map(i=> [i,18.05,36,1]),['↵'].map(i=> [i,0,36,1]),['{','}'].map(i=> [i,15.36,36,1]),['☑','☐'].map(i=> [i,27.56,36,1]),['◯'].map(i=> [i,34.83,36,1]),['℃'].map(i=> [i,32.77,36,1]),['Ⅱ'].map(i=> [i,20.69,36,1])],"Times New Roman29.3":[['我','，','。','；','？','、','（','）','【','】','⊙','⭘','：'].map(i=> [i,29.3,32,1]),[' ',',','.'].map(i=> [i,7.32,32,1]),['0','1','2','3','4','5','6','7','8','9','b','d','g','h','k','n','o','p','q','u','v','x','y','*'].map(i=> [i,14.65,32,1]),['a','c','e','z','?','“'].map(i=> [i,13,32,1]),['f','r','I','(',')','[',']','-','’','Ⅰ'].map(i=> [i,9.76,32,1]),['i','j','l','t',';','/',':'].map(i=> [i,8.14,32,1]),['m'].map(i=> [i,22.79,32,1]),['s','J'].map(i=> [i,11.4,32,1]),['w','A','D','G','H','K','N','O','Q','U','V','X','Y'].map(i=> [i,21.16,32,1]),['B','C','R'].map(i=> [i,19.54,32,1]),['E','L','T','Z'].map(i=> [i,17.9,32,1]),['F','P','S'].map(i=> [i,16.3,32,1]),['M'].map(i=> [i,26.05,32,1]),['W'].map(i=> [i,27.65,32,1]),['=','+','<','>'].map(i=> [i,16.52,32,1]),['↵'].map(i=> [i,0,32,1]),['{','}'].map(i=> [i,14.06,32,1]),['☑','☐'].map(i=> [i,25.24,32,1]),['◯'].map(i=> [i,31.89,32,1]),['℃'].map(i=> [i,30,32,1]),['Ⅱ'].map(i=> [i,18.94,32,1])],"Times New Roman26.7":[['我','，','。','；','？','、','（','）','【','】','：','⊙','⭘'].map(i=> [i,26.7,30,1]),[' ',',','.'].map(i=> [i,6.67,30,1]),['0','1','2','3','4','5','6','7','8','9','b','d','g','h','k','n','o','p','q','u','v','x','y','*'].map(i=> [i,13.35,30,1]),['a','c','e','z','?','“'].map(i=> [i,11.85,30,1]),['f','r','I','(',')','[',']','-','’','Ⅰ'].map(i=> [i,8.89,30,1]),['i','j','l','t',';','/',':'].map(i=> [i,7.42,30,1]),['m'].map(i=> [i,20.77,30,1]),['s','J'].map(i=> [i,10.39,30,1]),['w','A','D','G','H','K','N','O','Q','U','V','X','Y'].map(i=> [i,19.28,30,1]),['B','C','R'].map(i=> [i,17.81,30,1]),['E','L','T','Z'].map(i=> [i,16.31,30,1]),['F','P','S'].map(i=> [i,14.85,30,1]),['M'].map(i=> [i,23.74,30,1]),['W'].map(i=> [i,25.2,30,1]),['=','+','<','>'].map(i=> [i,15.06,30,1]),['Ⅱ'].map(i=> [i,17.26,30,1]),['↵'].map(i=> [i,0,30,1]),['{','}'].map(i=> [i,12.82,30,1]),['☑','☐'].map(i=> [i,23,30,1]),['◯'].map(i=> [i,29.06,30,1]),['℃'].map(i=> [i,27.34,30,1])],"Times New Roman24":[['我','，','。','；','？','、','（','）','【','】','⊙','⭘','：'].map(i=> [i,24,26,1]),[' ',',','.'].map(i=> [i,6,26,1]),['0','1','2','3','4','5','6','7','8','9','b','d','g','h','k','n','o','p','q','u','v','x','y','*'].map(i=> [i,12,26,1]),['a','c','e','z','?','“'].map(i=> [i,10.65,26,1]),['f','r','I','(',')','[',']','-','’','Ⅰ'].map(i=> [i,7.99,26,1]),['i','j','l','t',';','/',':'].map(i=> [i,6.67,26,1]),['m'].map(i=> [i,18.67,26,1]),['s','J'].map(i=> [i,9.34,26,1]),['w','A','D','G','H','K','N','O','Q','U','V','X','Y'].map(i=> [i,17.33,26,1]),['B','C','R'].map(i=> [i,16.01,26,1]),['E','L','T','Z'].map(i=> [i,14.66,26,1]),['F','P','S'].map(i=> [i,13.35,26,1]),['M'].map(i=> [i,21.34,26,1]),['W'].map(i=> [i,22.65,26,1]),['=','+','<','>'].map(i=> [i,13.54,26,1]),['↵'].map(i=> [i,0,26,1]),['{','}'].map(i=> [i,11.52,26,1]),['☑','☐'].map(i=> [i,20.67,26,1]),['◯'].map(i=> [i,26.12,26,1]),['℃'].map(i=> [i,24.57,26,1]),['Ⅱ'].map(i=> [i,15.52,26,1])],"Times New Roman21.3":[['我','，','。','；','？','、','（','）','【','】','⊙','⭘','：'].map(i=> [i,21.3,24,1]),[' '].map(i=> [i,5.32,24,1]),['0','1','2','3','4','5','6','7','8','9','b','d','g','h','k','n','o','p','q','u','v','x','y','*'].map(i=> [i,10.65,24,1]),['a','c','e','z','?','“'].map(i=> [i,9.45,24,1]),['f','r','I','(',')','[',']','-','’','Ⅰ'].map(i=> [i,7.09,24,1]),['i','j','l','t',';','/',':'].map(i=> [i,5.92,24,1]),['m'].map(i=> [i,16.57,24,1]),['s','J'].map(i=> [i,8.29,24,1]),['w','A','D','G','H','K','N','O','Q','U','V','X','Y'].map(i=> [i,15.38,24,1]),['B','C','R'].map(i=> [i,14.21,24,1]),['E','L','T','Z'].map(i=> [i,13.01,24,1]),['F','P','S'].map(i=> [i,11.85,24,1]),['M'].map(i=> [i,18.94,24,1]),['W'].map(i=> [i,20.1,24,1]),[',','.'].map(i=> [i,5.33,24,1]),['=','+','<','>'].map(i=> [i,12.01,24,1]),['↵'].map(i=> [i,0,24,1]),['{','}'].map(i=> [i,10.22,24,1]),['☑','☐'].map(i=> [i,18.35,24,1]),['◯'].map(i=> [i,23.18,24,1]),['℃'].map(i=> [i,21.81,24,1]),['Ⅱ'].map(i=> [i,13.77,24,1])],"Times New Roman20":[['我','，','。','；','？','、','（','）','【','】','⊙','⭘','：'].map(i=> [i,20,22,1]),[' ',',','.'].map(i=> [i,5,22,1]),['0','1','2','3','4','5','6','7','8','9','b','d','g','h','k','n','o','p','q','u','v','x','y','*'].map(i=> [i,10,22,1]),['a','c','e','z','?','“'].map(i=> [i,8.88,22,1]),['f','r','I','(',')','[',']','-','’','Ⅰ'].map(i=> [i,6.66,22,1]),['i','j','l','t',';','/',':'].map(i=> [i,5.56,22,1]),['m'].map(i=> [i,15.56,22,1]),['s','J'].map(i=> [i,7.78,22,1]),['w','A','D','G','H','K','N','O','Q','U','V','X','Y'].map(i=> [i,14.44,22,1]),['B','C','R'].map(i=> [i,13.34,22,1]),['E','L','T','Z'].map(i=> [i,12.22,22,1]),['F','P','S'].map(i=> [i,11.12,22,1]),['M'].map(i=> [i,17.78,22,1]),['W'].map(i=> [i,18.88,22,1]),['=','+','<','>'].map(i=> [i,11.28,22,1]),['↵'].map(i=> [i,0,22,1]),['{','}'].map(i=> [i,9.6,22,1]),['☑','☐'].map(i=> [i,17.23,22,1]),['◯'].map(i=> [i,21.77,22,1]),['℃'].map(i=> [i,20.48,22,1]),['Ⅱ'].map(i=> [i,12.93,22,1])],"Times New Roman18.7":[['我','，','。','；','？','、','（','）','【','】','⊙','⭘','：'].map(i=> [i,18.7,21,1]),[' ',',','.'].map(i=> [i,4.67,21,1]),['0','1','2','3','4','5','6','7','8','9','b','d','g','h','k','n','o','p','q','u','v','x','y','*'].map(i=> [i,9.35,21,1]),['a','c','e','z','?','“'].map(i=> [i,8.3,21,1]),['f','r','I','(',')','[',']','-','’','Ⅰ'].map(i=> [i,6.23,21,1]),['i','j','l','t',';','/',':'].map(i=> [i,5.2,21,1]),['m'].map(i=> [i,14.55,21,1]),['s','J'].map(i=> [i,7.28,21,1]),['w','A','D','G','H','K','N','O','Q','U','V','X','Y'].map(i=> [i,13.5,21,1]),['B','C','R'].map(i=> [i,12.47,21,1]),['E','L','T','Z'].map(i=> [i,11.42,21,1]),['F','P','S'].map(i=> [i,10.4,21,1]),['M'].map(i=> [i,16.63,21,1]),['W'].map(i=> [i,17.65,21,1]),['=','+','<','>'].map(i=> [i,10.55,21,1]),['↵'].map(i=> [i,0,21,1]),['{','}'].map(i=> [i,8.98,21,1]),['☑','☐'].map(i=> [i,16.11,21,1]),['◯'].map(i=> [i,20.35,21,1]),['℃'].map(i=> [i,19.15,21,1]),['Ⅱ'].map(i=> [i,12.09,21,1])],"Times New Roman16":[['我','，','。','；','？','、','（','）','【','】','⊙','⭘','：'].map(i=> [i,16,17,1]),[' ',',','.'].map(i=> [i,4,17,1]),['0','1','2','3','4','5','6','7','8','9','b','d','g','h','k','n','o','p','q','u','v','x','y','*'].map(i=> [i,8,17,1]),['a','c','e','z','?','“'].map(i=> [i,7.1,17,1]),['f','r','I','(',')','[',']','-','’','Ⅰ'].map(i=> [i,5.33,17,1]),['i','j','l','t',';','/',':'].map(i=> [i,4.45,17,1]),['m'].map(i=> [i,12.45,17,1]),['s','J'].map(i=> [i,6.23,17,1]),['w','A','D','G','H','K','N','O','Q','U','V','X','Y'].map(i=> [i,11.55,17,1]),['B','C','R'].map(i=> [i,10.67,17,1]),['E','L','T','Z'].map(i=> [i,9.77,17,1]),['F','P','S'].map(i=> [i,8.9,17,1]),['M'].map(i=> [i,14.23,17,1]),['W'].map(i=> [i,15.1,17,1]),['=','+','<','>'].map(i=> [i,9.02,17,1]),['↵'].map(i=> [i,0,17,1]),['{','}'].map(i=> [i,7.68,17,1]),['☑','☐'].map(i=> [i,13.78,17,1]),['◯'].map(i=> [i,17,17,1]),['℃'].map(i=> [i,16.38,17,1]),['Ⅱ'].map(i=> [i,10.34,17,1])],"Times New Roman14.7":[['我','，','。','；','？','、','（','）','【','】','：','⊙','⭘'].map(i=> [i,14.7,16,1]),[' ',',','.'].map(i=> [i,3.67,16,1]),['0','1','2','3','4','5','6','7','8','9','b','d','g','h','k','n','o','p','q','u','v','x','y','*'].map(i=> [i,7.35,16,1]),['a','c','e','z','?','“'].map(i=> [i,6.52,16,1]),['f','r','I','(',')','[',']','-','’','Ⅰ'].map(i=> [i,4.9,16,1]),['i','j','l','t',';','/',':'].map(i=> [i,4.08,16,1]),['m'].map(i=> [i,11.43,16,1]),['s','J'].map(i=> [i,5.72,16,1]),['w','A','D','G','H','K','N','O','Q','U','V','X','Y'].map(i=> [i,10.62,16,1]),['B','C','R'].map(i=> [i,9.8,16,1]),['E','L','T','Z'].map(i=> [i,8.98,16,1]),['F','P','S'].map(i=> [i,8.18,16,1]),['M'].map(i=> [i,13.07,16,1]),['W'].map(i=> [i,13.87,16,1]),['=','+','<','>'].map(i=> [i,8.29,16,1]),['Ⅱ'].map(i=> [i,9.5,16,1]),['↵'].map(i=> [i,0,16,1]),['{','}'].map(i=> [i,7.06,16,1]),['☑','☐'].map(i=> [i,12.66,16,1]),['◯'].map(i=> [i,16,16,1]),['℃'].map(i=> [i,15.05,16,1])],"Times New Roman14":[['我','，','。','；','？','、','（','）','【','】','⊙','⭘','：'].map(i=> [i,14,15,1]),[' ',',','.'].map(i=> [i,3.5,15,1]),['0','1','2','3','4','5','6','7','8','9','b','d','g','h','k','n','o','p','q','u','v','x','y','*'].map(i=> [i,7,15,1]),['a','c','e','z','?','“'].map(i=> [i,6.21,15,1]),['f','r','I','(',')','[',']','-','’','Ⅰ'].map(i=> [i,4.66,15,1]),['i','j','l','t',';','/',':'].map(i=> [i,3.89,15,1]),['m'].map(i=> [i,10.89,15,1]),['s','J'].map(i=> [i,5.45,15,1]),['w','A','D','G','H','K','N','O','Q','U','V','X','Y'].map(i=> [i,10.11,15,1]),['B','C','R'].map(i=> [i,9.34,15,1]),['E','L','T','Z'].map(i=> [i,8.55,15,1]),['F','P','S'].map(i=> [i,7.79,15,1]),['M'].map(i=> [i,12.45,15,1]),['W'].map(i=> [i,13.21,15,1]),['=','+','<','>'].map(i=> [i,7.9,15,1]),['↵'].map(i=> [i,0,15,1]),['{','}'].map(i=> [i,6.72,15,1]),['☑','☐'].map(i=> [i,12.06,15,1]),['◯'].map(i=> [i,15.24,15,1]),['℃'].map(i=> [i,14.33,15,1]),['Ⅱ'].map(i=> [i,9.05,15,1])],"Times New Roman13.3":[['◯'].map(i=> [i,14,15,1]),['↵'].map(i=> [i,0,15,1]),['我','，','。','；','？','、','（','）','【','】','：','⊙','⭘'].map(i=> [i,13.3,15,1]),[' '].map(i=> [i,3.32,15,1]),['0','1','2','3','4','5','6','7','8','9','b','d','g','h','k','n','o','p','q','u','v','x','y','*'].map(i=> [i,6.65,15,1]),['a','c','e','z','?','“'].map(i=> [i,5.9,15,1]),['f','r','I','(',')','[',']','-','’','Ⅰ'].map(i=> [i,4.43,15,1]),['i','j','l','t',';','/',':'].map(i=> [i,3.7,15,1]),['m'].map(i=> [i,10.35,15,1]),['s','J'].map(i=> [i,5.18,15,1]),['w','A','D','G','H','K','N','O','Q','U','V','X','Y'].map(i=> [i,9.6,15,1]),['B','C','R'].map(i=> [i,8.87,15,1]),['E','L','T','Z'].map(i=> [i,8.12,15,1]),['F','P','S'].map(i=> [i,7.4,15,1]),['M'].map(i=> [i,11.83,15,1]),['W'].map(i=> [i,12.55,15,1]),[',','.'].map(i=> [i,3.33,15,1]),['=','+','<','>'].map(i=> [i,7.5,15,1]),['Ⅱ'].map(i=> [i,8.6,15,1]),['{','}'].map(i=> [i,6.38,15,1]),['☑','☐'].map(i=> [i,11.46,15,1]),['℃'].map(i=> [i,13.62,15,1])],"Times New Roman12":[['我','，','。','；','？','、','（','）','【','】','⊙','⭘','：'].map(i=> [i,12,14,1]),[' ',',','.'].map(i=> [i,3,14,1]),['0','1','2','3','4','5','6','7','8','9','b','d','g','h','k','n','o','p','q','u','v','x','y','*'].map(i=> [i,6,14,1]),['a','c','e','z','?','“'].map(i=> [i,5.33,14,1]),['f','r','I','(',')','[',']','-','’','Ⅰ'].map(i=> [i,4,14,1]),['i','j','l','t',';','/',':'].map(i=> [i,3.33,14,1]),['m'].map(i=> [i,9.33,14,1]),['s','J'].map(i=> [i,4.67,14,1]),['w','A','D','G','H','K','N','O','Q','U','V','X','Y'].map(i=> [i,8.67,14,1]),['B','C','R'].map(i=> [i,8,14,1]),['E','L','T','Z'].map(i=> [i,7.33,14,1]),['F','P','S'].map(i=> [i,6.67,14,1]),['M'].map(i=> [i,10.67,14,1]),['W'].map(i=> [i,11.33,14,1]),['=','+','<','>'].map(i=> [i,6.77,14,1]),['↵'].map(i=> [i,0,14,1]),['{','}'].map(i=> [i,5.76,14,1]),['☑','☐'].map(i=> [i,10.34,14,1]),['◯'].map(i=> [i,13,14,1]),['℃'].map(i=> [i,12.29,14,1]),['Ⅱ'].map(i=> [i,7.76,14,1])],"Times New Roman10.7":[['我','，','。','；','？','、','（','）','【','】','：','⊙','⭘'].map(i=> [i,10.7,12,1]),[' ',',','.'].map(i=> [i,2.67,12,1]),['0','1','2','3','4','5','6','7','8','9','b','d','g','h','k','n','o','p','q','u','v','x','y','*'].map(i=> [i,5.35,12,1]),['a','c','e','z','?','“'].map(i=> [i,4.75,12,1]),['f','r','I','(',')','[',']','-','’','Ⅰ'].map(i=> [i,3.56,12,1]),['i','j','l','t',';','/',':'].map(i=> [i,2.97,12,1]),['m'].map(i=> [i,8.32,12,1]),['s','J'].map(i=> [i,4.16,12,1]),['w','A','D','G','H','K','N','O','Q','U','V','X','Y'].map(i=> [i,7.73,12,1]),['B','C','R'].map(i=> [i,7.14,12,1]),['E','L','T','Z'].map(i=> [i,6.54,12,1]),['F','P','S'].map(i=> [i,5.95,12,1]),['M'].map(i=> [i,9.51,12,1]),['W'].map(i=> [i,10.1,12,1]),['=','+','<','>'].map(i=> [i,6.03,12,1]),['Ⅱ'].map(i=> [i,6.92,12,1]),['↵'].map(i=> [i,0,12,1]),['{','}'].map(i=> [i,5.14,12,1]),['☑','☐'].map(i=> [i,9.22,12,1]),['◯'].map(i=> [i,11.65,12,1]),['℃'].map(i=> [i,10.96,12,1])],"STSong10.7":[['我','》','（'].map(i=> [i,10.7,10,1]),['！','？','《','；','，','、','。','）','：','【','】','⊙','⭘','℃'].map(i=> [i,10.72,10,1]),['“'].map(i=> [i,4.25,10,1]),['”'].map(i=> [i,4.31,10,1]),['‘'].map(i=> [i,2.59,10,1]),['’'].map(i=> [i,2.77,10,1]),[' '].map(i=> [i,5.35,10,1]),['0','5'].map(i=> [i,5.05,10,1]),['1','2','3','4','6','7','8','9','k','v'].map(i=> [i,5.03,10,1]),['a'].map(i=> [i,5.44,10,1]),['b'].map(i=> [i,5.59,10,1]),['d','_'].map(i=> [i,5.5,10,1]),['c'].map(i=> [i,4.42,10,1]),['e'].map(i=> [i,4.64,10,1]),['f'].map(i=> [i,3.91,10,1]),['g'].map(i=> [i,5.11,10,1]),['h','n'].map(i=> [i,5.7,10,1]),['i',']'].map(i=> [i,2.91,10,1]),['j'].map(i=> [i,2.38,10,1]),['l','['].map(i=> [i,2.92,10,1]),['m'].map(i=> [i,8.42,10,1]),['o'].map(i=> [i,5.48,10,1]),['p'].map(i=> [i,5.66,10,1]),['q'].map(i=> [i,5.61,10,1]),['r'].map(i=> [i,3.88,10,1]),['s','?'].map(i=> [i,3.92,10,1]),['t'].map(i=> [i,3.44,10,1]),['u'].map(i=> [i,5.55,10,1]),['w','<','>','=','#','+'].map(i=> [i,7.16,10,1]),['x'].map(i=> [i,4.92,10,1]),['y'].map(i=> [i,4.47,10,1]),['z'].map(i=> [i,4.59,10,1]),['A','Y'].map(i=> [i,7.25,10,1]),['B','T'].map(i=> [i,6.59,10,1]),['C'].map(i=> [i,6.81,10,1]),['D','G','N','Q'].map(i=> [i,8.27,10,1]),['E','Z'].map(i=> [i,7.03,10,1]),['F','P'].map(i=> [i,6.05,10,1]),['H'].map(i=> [i,8.14,10,1]),['I'].map(i=> [i,3.81,10,1]),['J','`'].map(i=> [i,3.58,10,1]),['K'].map(i=> [i,7.94,10,1]),['L'].map(i=> [i,6.14,10,1]),['M'].map(i=> [i,8.92,10,1]),['O'].map(i=> [i,8.38,10,1]),['R'].map(i=> [i,6.7,10,1]),['S','{','}'].map(i=> [i,5.14,10,1]),['U'].map(i=> [i,7.59,10,1]),['V'].map(i=> [i,7.45,10,1]),['W'].map(i=> [i,9.69,10,1]),['X'].map(i=> [i,7.58,10,1]),[',','.',':','!',';'].map(i=> [i,2.36,10,1]),['/','|'].map(i=> [i,5.36,10,1]),['\"'].map(i=> [i,4.36,10,1]),['~'].map(i=> [i,7.14,10,1]),['@'].map(i=> [i,9.83,10,1]),['$'].map(i=> [i,4.81,10,1]),['%'].map(i=> [i,8.81,10,1]),['^'].map(i=> [i,5.38,10,1]),['&'].map(i=> [i,7.81,10,1]),['*'].map(i=> [i,4.58,10,1]),['(',')'].map(i=> [i,3.14,10,1]),['-'].map(i=> [i,3.36,10,1]),['\\'].map(i=> [i,1.91,10,1]),['☑'].map(i=> [i,8.91,10,1]),['☐'].map(i=> [i,8.89,10,1]),['◯'].map(i=> [i,10.22,10,1]),['↵'].map(i=> [i,0,10,1])],"STSong12":[['j'].map(i=> [i,2.67,12,1]),['↵'].map(i=> [i,0,12,1]),['z','*'].map(i=> [i,5.14,12,1]),['我','！','？','《','》','；','，','、','。','（','）','：','【','】'].map(i=> [i,12,12,1]),['“'].map(i=> [i,4.77,12,1]),['”'].map(i=> [i,4.83,12,1]),['‘'].map(i=> [i,2.91,12,1]),['’'].map(i=> [i,3.09,12,1]),[' '].map(i=> [i,6,12,1]),['0','2','3','4','5','7','8','9','v'].map(i=> [i,5.64,12,1]),['1','6'].map(i=> [i,5.66,12,1]),['a'].map(i=> [i,6.09,12,1]),['b'].map(i=> [i,6.28,12,1]),['d'].map(i=> [i,6.17,12,1]),['c'].map(i=> [i,4.95,12,1]),['e'].map(i=> [i,5.2,12,1]),['f'].map(i=> [i,4.38,12,1]),['g'].map(i=> [i,5.72,12,1]),['h'].map(i=> [i,6.41,12,1]),['i'].map(i=> [i,3.25,12,1]),['k'].map(i=> [i,5.63,12,1]),['l'].map(i=> [i,3.28,12,1]),['m'].map(i=> [i,9.45,12,1]),['n'].map(i=> [i,6.39,12,1]),['o'].map(i=> [i,6.14,12,1]),['p'].map(i=> [i,6.33,12,1]),['q'].map(i=> [i,6.3,12,1]),['r'].map(i=> [i,4.34,12,1]),['s','?'].map(i=> [i,4.39,12,1]),['t'].map(i=> [i,3.86,12,1]),['u'].map(i=> [i,6.22,12,1]),['w','<','=','~','+'].map(i=> [i,8.02,12,1]),['x'].map(i=> [i,5.52,12,1]),['y'].map(i=> [i,5.02,12,1]),['A'].map(i=> [i,8.14,12,1]),['B','T'].map(i=> [i,7.41,12,1]),['C'].map(i=> [i,7.63,12,1]),['D','G','N'].map(i=> [i,9.27,12,1]),['E','Z'].map(i=> [i,7.89,12,1]),['F'].map(i=> [i,6.78,12,1]),['H'].map(i=> [i,9.13,12,1]),['I'].map(i=> [i,4.27,12,1]),['J','`'].map(i=> [i,4.02,12,1]),['K'].map(i=> [i,8.89,12,1]),['L'].map(i=> [i,6.89,12,1]),['M'].map(i=> [i,10.02,12,1]),['O'].map(i=> [i,9.39,12,1]),['P'].map(i=> [i,6.77,12,1]),['Q'].map(i=> [i,9.28,12,1]),['R'].map(i=> [i,7.52,12,1]),['S'].map(i=> [i,5.75,12,1]),['U'].map(i=> [i,8.52,12,1]),['V'].map(i=> [i,8.36,12,1]),['W'].map(i=> [i,10.86,12,1]),['X'].map(i=> [i,8.5,12,1]),['Y'].map(i=> [i,8.13,12,1]),[',','.',':','!'].map(i=> [i,2.64,12,1]),['/','^','|'].map(i=> [i,6.02,12,1]),['\"'].map(i=> [i,4.89,12,1]),['>','#'].map(i=> [i,8.03,12,1]),['@'].map(i=> [i,11.02,12,1]),['$'].map(i=> [i,5.39,12,1]),['%'].map(i=> [i,9.89,12,1]),['&'].map(i=> [i,8.77,12,1]),['(',')'].map(i=> [i,3.52,12,1]),['_'].map(i=> [i,6.16,12,1]),[';'].map(i=> [i,2.66,12,1]),['-'].map(i=> [i,3.77,12,1]),['{','}'].map(i=> [i,5.77,12,1]),['[',']'].map(i=> [i,3.27,12,1]),['\\'].map(i=> [i,2.14,12,1]),['☑'].map(i=> [i,9.98,12,1]),['☐'].map(i=> [i,9.97,12,1]),['⊙','⭘','℃'].map(i=> [i,12.02,12,1]),['◯'].map(i=> [i,11.45,12,1])],"STSong13.3":[['我','！','？','《','》','，','、','。','（','：','【','】','⊙','⭘','℃'].map(i=> [i,13.31,13,1]),['；','）'].map(i=> [i,13.33,13,1]),['“'].map(i=> [i,5.28,13,1]),['”'].map(i=> [i,5.38,13,1]),['‘'].map(i=> [i,3.22,13,1]),['’'].map(i=> [i,3.42,13,1]),[' '].map(i=> [i,6.65,13,1]),['0','1','3','4','5','6','8','9','v'].map(i=> [i,6.25,13,1]),['2','7'].map(i=> [i,6.27,13,1]),['a'].map(i=> [i,6.75,13,1]),['b','q'].map(i=> [i,6.97,13,1]),['d','_'].map(i=> [i,6.83,13,1]),['c'].map(i=> [i,5.5,13,1]),['e'].map(i=> [i,5.77,13,1]),['f','s'].map(i=> [i,4.86,13,1]),['g'].map(i=> [i,6.33,13,1]),['h','n'].map(i=> [i,7.09,13,1]),['i','['].map(i=> [i,3.61,13,1]),['j'].map(i=> [i,2.95,13,1]),['k'].map(i=> [i,6.23,13,1]),['l'].map(i=> [i,3.64,13,1]),['m'].map(i=> [i,10.47,13,1]),['o'].map(i=> [i,6.81,13,1]),['p'].map(i=> [i,7,13,1]),['r'].map(i=> [i,4.83,13,1]),['t'].map(i=> [i,4.28,13,1]),['u'].map(i=> [i,6.89,13,1]),['w','<','>','~','+'].map(i=> [i,8.89,13,1]),['x'].map(i=> [i,6.11,13,1]),['y'].map(i=> [i,5.56,13,1]),['z'].map(i=> [i,5.69,13,1]),['A','Y'].map(i=> [i,9.02,13,1]),['B','T'].map(i=> [i,8.2,13,1]),['C'].map(i=> [i,8.45,13,1]),['D','G'].map(i=> [i,10.28,13,1]),['E','Z'].map(i=> [i,8.73,13,1]),['F','P'].map(i=> [i,7.5,13,1]),['H'].map(i=> [i,10.11,13,1]),['I'].map(i=> [i,4.73,13,1]),['J'].map(i=> [i,4.44,13,1]),['K'].map(i=> [i,9.86,13,1]),['L'].map(i=> [i,7.64,13,1]),['M'].map(i=> [i,11.09,13,1]),['N','Q'].map(i=> [i,10.27,13,1]),['O'].map(i=> [i,10.41,13,1]),['R'].map(i=> [i,8.33,13,1]),['S','{','}'].map(i=> [i,6.39,13,1]),['U'].map(i=> [i,9.42,13,1]),['V'].map(i=> [i,9.28,13,1]),['W'].map(i=> [i,12.03,13,1]),['X'].map(i=> [i,9.41,13,1]),[',',':','!',';'].map(i=> [i,2.92,13,1]),['.'].map(i=> [i,2.94,13,1]),['/','|'].map(i=> [i,6.66,13,1]),['?'].map(i=> [i,4.88,13,1]),['\"'].map(i=> [i,5.42,13,1]),['=','#'].map(i=> [i,8.88,13,1]),['`'].map(i=> [i,4.45,13,1]),['@'].map(i=> [i,12.22,13,1]),['$'].map(i=> [i,5.98,13,1]),['%'].map(i=> [i,10.95,13,1]),['^'].map(i=> [i,6.67,13,1]),['&'].map(i=> [i,9.7,13,1]),['*'].map(i=> [i,5.7,13,1]),['('].map(i=> [i,3.89,13,1]),[')'].map(i=> [i,3.91,13,1]),['-'].map(i=> [i,4.17,13,1]),[']'].map(i=> [i,3.63,13,1]),['\\'].map(i=> [i,2.38,13,1]),['☑'].map(i=> [i,11.05,13,1]),['☐'].map(i=> [i,11.06,13,1]),['◯'].map(i=> [i,12.7,13,1]),['↵'].map(i=> [i,0,13,1])],"STSong14":[['*'].map(i=> [i,6,14,1]),['J'].map(i=> [i,4.67,14,1]),['b'].map(i=> [i,7.33,14,1]),['↵'].map(i=> [i,0,14,1]),['我','！','？','《','》','；','，','、','。','（','）','：','【','】'].map(i=> [i,14,14,1]),['“'].map(i=> [i,5.55,14,1]),['”'].map(i=> [i,5.66,14,1]),['‘'].map(i=> [i,3.38,14,1]),['’'].map(i=> [i,3.61,14,1]),[' '].map(i=> [i,7,14,1]),['0','1','3','4','5','7','8','9','v'].map(i=> [i,6.58,14,1]),['2','6'].map(i=> [i,6.59,14,1]),['a'].map(i=> [i,7.11,14,1]),['d','_'].map(i=> [i,7.19,14,1]),['c'].map(i=> [i,5.78,14,1]),['e'].map(i=> [i,6.08,14,1]),['f'].map(i=> [i,5.11,14,1]),['g'].map(i=> [i,6.66,14,1]),['h','n'].map(i=> [i,7.47,14,1]),['i'].map(i=> [i,3.8,14,1]),['j'].map(i=> [i,3.11,14,1]),['k'].map(i=> [i,6.56,14,1]),['l'].map(i=> [i,3.83,14,1]),['m'].map(i=> [i,11.02,14,1]),['o'].map(i=> [i,7.17,14,1]),['p'].map(i=> [i,7.38,14,1]),['q'].map(i=> [i,7.34,14,1]),['r'].map(i=> [i,5.06,14,1]),['s','?'].map(i=> [i,5.13,14,1]),['t'].map(i=> [i,4.5,14,1]),['u'].map(i=> [i,7.25,14,1]),['w','>','=','+'].map(i=> [i,9.36,14,1]),['x'].map(i=> [i,6.42,14,1]),['y'].map(i=> [i,5.86,14,1]),['z'].map(i=> [i,5.98,14,1]),['A'].map(i=> [i,9.5,14,1]),['B','T'].map(i=> [i,8.63,14,1]),['C'].map(i=> [i,8.91,14,1]),['D','G','N','Q'].map(i=> [i,10.81,14,1]),['E'].map(i=> [i,9.2,14,1]),['F','P'].map(i=> [i,7.89,14,1]),['H'].map(i=> [i,10.66,14,1]),['I'].map(i=> [i,4.97,14,1]),['K'].map(i=> [i,10.38,14,1]),['L'].map(i=> [i,8.05,14,1]),['M'].map(i=> [i,11.67,14,1]),['O'].map(i=> [i,10.95,14,1]),['R'].map(i=> [i,8.77,14,1]),['S','{','}'].map(i=> [i,6.72,14,1]),['U'].map(i=> [i,9.94,14,1]),['V'].map(i=> [i,9.75,14,1]),['W'].map(i=> [i,12.67,14,1]),['X'].map(i=> [i,9.91,14,1]),['Y'].map(i=> [i,9.48,14,1]),['Z'].map(i=> [i,9.19,14,1]),[','].map(i=> [i,3.09,14,1]),['.',':','!',';'].map(i=> [i,3.08,14,1]),['/','^','|'].map(i=> [i,7.02,14,1]),['\"'].map(i=> [i,5.7,14,1]),['<','~','#'].map(i=> [i,9.34,14,1]),['`'].map(i=> [i,4.69,14,1]),['@'].map(i=> [i,12.86,14,1]),['$'].map(i=> [i,6.3,14,1]),['%'].map(i=> [i,11.53,14,1]),['&'].map(i=> [i,10.22,14,1]),['('].map(i=> [i,4.09,14,1]),[')'].map(i=> [i,4.11,14,1]),['-'].map(i=> [i,4.39,14,1]),['[',']'].map(i=> [i,3.81,14,1]),['\\'].map(i=> [i,2.5,14,1]),['☑'].map(i=> [i,11.63,14,1]),['☐'].map(i=> [i,11.64,14,1]),['⊙','⭘','℃'].map(i=> [i,14.02,14,1]),['◯'].map(i=> [i,13.38,14,1])],"STSong15":[['↵'].map(i=> [i,0,15,1]),['j'].map(i=> [i,3.33,15,1]),[' '].map(i=> [i,7.5,15,1]),['我','！','？','《','》','；','，','、','。','（','）','：','【','】'].map(i=> [i,15,15,1]),['“'].map(i=> [i,5.95,15,1]),['”'].map(i=> [i,6.05,15,1]),['‘'].map(i=> [i,3.63,15,1]),['’'].map(i=> [i,3.86,15,1]),['0','1','3','4','5','7','8','9','k','v'].map(i=> [i,7.05,15,1]),['2','6'].map(i=> [i,7.06,15,1]),['a'].map(i=> [i,7.61,15,1]),['b'].map(i=> [i,7.84,15,1]),['d'].map(i=> [i,7.72,15,1]),['c'].map(i=> [i,6.19,15,1]),['e'].map(i=> [i,6.5,15,1]),['f'].map(i=> [i,5.47,15,1]),['g'].map(i=> [i,7.14,15,1]),['h','n'].map(i=> [i,8,15,1]),['i'].map(i=> [i,4.06,15,1]),['l'].map(i=> [i,4.09,15,1]),['m'].map(i=> [i,11.8,15,1]),['o','_'].map(i=> [i,7.69,15,1]),['p'].map(i=> [i,7.91,15,1]),['q'].map(i=> [i,7.86,15,1]),['r'].map(i=> [i,5.42,15,1]),['s'].map(i=> [i,5.5,15,1]),['t'].map(i=> [i,4.81,15,1]),['u'].map(i=> [i,7.77,15,1]),['w','>'].map(i=> [i,10.03,15,1]),['x'].map(i=> [i,6.88,15,1]),['y'].map(i=> [i,6.28,15,1]),['z','*'].map(i=> [i,6.42,15,1]),['A'].map(i=> [i,10.16,15,1]),['B'].map(i=> [i,9.25,15,1]),['C'].map(i=> [i,9.53,15,1]),['D'].map(i=> [i,11.59,15,1]),['E','Z'].map(i=> [i,9.84,15,1]),['F','P'].map(i=> [i,8.47,15,1]),['G','N','Q'].map(i=> [i,11.58,15,1]),['H'].map(i=> [i,11.42,15,1]),['I'].map(i=> [i,5.31,15,1]),['J','`'].map(i=> [i,5.02,15,1]),['K'].map(i=> [i,11.13,15,1]),['L'].map(i=> [i,8.61,15,1]),['M'].map(i=> [i,12.5,15,1]),['O'].map(i=> [i,11.73,15,1]),['R'].map(i=> [i,9.39,15,1]),['S','{','}'].map(i=> [i,7.2,15,1]),['T'].map(i=> [i,9.23,15,1]),['U'].map(i=> [i,10.64,15,1]),['V'].map(i=> [i,10.45,15,1]),['W'].map(i=> [i,13.58,15,1]),['X'].map(i=> [i,10.61,15,1]),['Y'].map(i=> [i,10.14,15,1]),[',','.',':','!',';'].map(i=> [i,3.3,15,1]),['/','^','|'].map(i=> [i,7.52,15,1]),['?'].map(i=> [i,5.48,15,1]),['\"'].map(i=> [i,6.11,15,1]),['<','=','~','#','+'].map(i=> [i,10.02,15,1]),['@'].map(i=> [i,13.78,15,1]),['$'].map(i=> [i,6.73,15,1]),['%'].map(i=> [i,12.36,15,1]),['&'].map(i=> [i,10.95,15,1]),['('].map(i=> [i,4.39,15,1]),[')'].map(i=> [i,4.41,15,1]),['-'].map(i=> [i,4.72,15,1]),['[',']'].map(i=> [i,4.08,15,1]),['\\'].map(i=> [i,2.67,15,1]),['☑','☐'].map(i=> [i,12.47,15,1]),['⊙','⭘','℃'].map(i=> [i,15.02,15,1]),['◯'].map(i=> [i,14.31,15,1])],"STSong14.7":[['K'].map(i=> [i,10.89,15,1]),['↵'].map(i=> [i,0,15,1]),['B'].map(i=> [i,9.05,15,1]),['W'].map(i=> [i,13.3,15,1]),['S','}'].map(i=> [i,7.05,15,1]),['{'].map(i=> [i,7.06,15,1]),['u'].map(i=> [i,7.61,15,1]),['h'].map(i=> [i,7.84,15,1]),['b'].map(i=> [i,7.69,15,1]),['m'].map(i=> [i,11.58,15,1]),['r'].map(i=> [i,5.31,15,1]),['t'].map(i=> [i,4.72,15,1]),['我','》','（'].map(i=> [i,14.7,15,1]),['！','？','《','；','，','、','。','）','：','【','】','⊙','⭘','℃'].map(i=> [i,14.72,15,1]),['“'].map(i=> [i,5.83,15,1]),['”'].map(i=> [i,5.92,15,1]),['‘'].map(i=> [i,3.56,15,1]),['’'].map(i=> [i,3.78,15,1]),[' '].map(i=> [i,7.35,15,1]),['0','2','3','4','5','7','8','9','v'].map(i=> [i,6.91,15,1]),['1','6'].map(i=> [i,6.92,15,1]),['a'].map(i=> [i,7.45,15,1]),['d'].map(i=> [i,7.56,15,1]),['c'].map(i=> [i,6.08,15,1]),['e'].map(i=> [i,6.36,15,1]),['f'].map(i=> [i,5.38,15,1]),['g'].map(i=> [i,6.98,15,1]),['i'].map(i=> [i,3.98,15,1]),['j'].map(i=> [i,3.27,15,1]),['k'].map(i=> [i,6.89,15,1]),['l'].map(i=> [i,4.02,15,1]),['n'].map(i=> [i,7.83,15,1]),['o'].map(i=> [i,7.53,15,1]),['p'].map(i=> [i,7.75,15,1]),['q'].map(i=> [i,7.7,15,1]),['s','?'].map(i=> [i,5.39,15,1]),['w','>','~','+'].map(i=> [i,9.83,15,1]),['x'].map(i=> [i,6.75,15,1]),['y'].map(i=> [i,6.14,15,1]),['z','*'].map(i=> [i,6.3,15,1]),['A'].map(i=> [i,9.97,15,1]),['C'].map(i=> [i,9.36,15,1]),['D','G','N'].map(i=> [i,11.34,15,1]),['E','Z'].map(i=> [i,9.66,15,1]),['F'].map(i=> [i,8.3,15,1]),['H'].map(i=> [i,11.19,15,1]),['I'].map(i=> [i,5.22,15,1]),['J'].map(i=> [i,4.92,15,1]),['L'].map(i=> [i,8.44,15,1]),['M'].map(i=> [i,12.27,15,1]),['O'].map(i=> [i,11.5,15,1]),['P'].map(i=> [i,8.28,15,1]),['Q'].map(i=> [i,11.36,15,1]),['R'].map(i=> [i,9.2,15,1]),['T'].map(i=> [i,9.06,15,1]),['U'].map(i=> [i,10.42,15,1]),['V'].map(i=> [i,10.25,15,1]),['X'].map(i=> [i,10.41,15,1]),['Y'].map(i=> [i,9.95,15,1]),[',','.',':','!',';'].map(i=> [i,3.23,15,1]),['/','^','|'].map(i=> [i,7.36,15,1]),['\"'].map(i=> [i,5.98,15,1]),['<','=','#'].map(i=> [i,9.81,15,1]),['`'].map(i=> [i,4.91,15,1]),['@'].map(i=> [i,13.5,15,1]),['$'].map(i=> [i,6.61,15,1]),['%'].map(i=> [i,12.11,15,1]),['&'].map(i=> [i,10.73,15,1]),['('].map(i=> [i,4.31,15,1]),[')'].map(i=> [i,4.3,15,1]),['_'].map(i=> [i,7.55,15,1]),['-'].map(i=> [i,4.61,15,1]),['[',']'].map(i=> [i,4,15,1]),['\\'].map(i=> [i,2.63,15,1]),['☑'].map(i=> [i,12.2,15,1]),['☐'].map(i=> [i,12.22,15,1]),['◯'].map(i=> [i,14.03,15,1])],"STSong16":[['\"'].map(i=> [i,6.52,16,1]),['↵'].map(i=> [i,0,16,1]),['我','！','？','《','》','；','，','、','。','（','）','：','【','】'].map(i=> [i,16,16,1]),['“'].map(i=> [i,6.34,16,1]),['”'].map(i=> [i,6.45,16,1]),['‘'].map(i=> [i,3.86,16,1]),['’'].map(i=> [i,4.13,16,1]),[' '].map(i=> [i,8,16,1]),['0','1','3','4','5','7','8','9','v'].map(i=> [i,7.52,16,1]),['2','6'].map(i=> [i,7.53,16,1]),['a'].map(i=> [i,8.13,16,1]),['b'].map(i=> [i,8.36,16,1]),['d'].map(i=> [i,8.22,16,1]),['c'].map(i=> [i,6.61,16,1]),['e'].map(i=> [i,6.92,16,1]),['f','?'].map(i=> [i,5.84,16,1]),['g'].map(i=> [i,7.61,16,1]),['h','n'].map(i=> [i,8.53,16,1]),['i'].map(i=> [i,4.33,16,1]),['j'].map(i=> [i,3.56,16,1]),['k'].map(i=> [i,7.5,16,1]),['l'].map(i=> [i,4.38,16,1]),['m'].map(i=> [i,12.58,16,1]),['o'].map(i=> [i,8.19,16,1]),['p'].map(i=> [i,8.44,16,1]),['q'].map(i=> [i,8.39,16,1]),['r'].map(i=> [i,5.78,16,1]),['s'].map(i=> [i,5.86,16,1]),['t'].map(i=> [i,5.14,16,1]),['u'].map(i=> [i,8.28,16,1]),['w','<','>','=','~','#','+'].map(i=> [i,10.69,16,1]),['x'].map(i=> [i,7.34,16,1]),['y'].map(i=> [i,6.69,16,1]),['z'].map(i=> [i,6.86,16,1]),['A'].map(i=> [i,10.84,16,1]),['B','T'].map(i=> [i,9.86,16,1]),['C'].map(i=> [i,10.17,16,1]),['D','G'].map(i=> [i,12.34,16,1]),['E','Z'].map(i=> [i,10.52,16,1]),['F'].map(i=> [i,9.03,16,1]),['H'].map(i=> [i,12.17,16,1]),['I'].map(i=> [i,5.69,16,1]),['J','`'].map(i=> [i,5.34,16,1]),['K'].map(i=> [i,11.86,16,1]),['L'].map(i=> [i,9.17,16,1]),['M'].map(i=> [i,13.34,16,1]),['N','Q'].map(i=> [i,12.36,16,1]),['O'].map(i=> [i,12.52,16,1]),['P'].map(i=> [i,9.02,16,1]),['R'].map(i=> [i,10.02,16,1]),['S','}'].map(i=> [i,7.67,16,1]),['U'].map(i=> [i,11.34,16,1]),['V'].map(i=> [i,11.16,16,1]),['W'].map(i=> [i,14.47,16,1]),['X'].map(i=> [i,11.31,16,1]),['Y'].map(i=> [i,10.83,16,1]),[',',':'].map(i=> [i,3.52,16,1]),['.','!',';'].map(i=> [i,3.53,16,1]),['/','^','|'].map(i=> [i,8.02,16,1]),['@'].map(i=> [i,14.69,16,1]),['$'].map(i=> [i,7.19,16,1]),['%'].map(i=> [i,13.17,16,1]),['&'].map(i=> [i,11.69,16,1]),['*'].map(i=> [i,6.84,16,1]),['(',')'].map(i=> [i,4.69,16,1]),['_'].map(i=> [i,8.2,16,1]),['-'].map(i=> [i,5.02,16,1]),['{'].map(i=> [i,7.69,16,1]),['['].map(i=> [i,4.36,16,1]),[']'].map(i=> [i,4.34,16,1]),['\\'].map(i=> [i,2.84,16,1]),['☑','☐'].map(i=> [i,13.3,16,1]),['⊙','⭘','℃'].map(i=> [i,16.02,16,1]),['◯'].map(i=> [i,15.28,16,1])],"STSong18.7":[['我','》','（','⊙'].map(i=> [i,18.7,19,1]),['！','？','《','；','，','、','。','）','：','【','】','⭘','℃'].map(i=> [i,18.72,19,1]),['“'].map(i=> [i,7.42,19,1]),['”'].map(i=> [i,7.53,19,1]),['‘'].map(i=> [i,4.52,19,1]),['’'].map(i=> [i,4.81,19,1]),[' '].map(i=> [i,9.35,19,1]),['0','1','3','4','5','7','8'].map(i=> [i,8.78,19,1]),['2','6','9','v'].map(i=> [i,8.8,19,1]),['a'].map(i=> [i,9.47,19,1]),['b'].map(i=> [i,9.78,19,1]),['d'].map(i=> [i,9.61,19,1]),['c'].map(i=> [i,7.72,19,1]),['e'].map(i=> [i,8.09,19,1]),['f'].map(i=> [i,6.83,19,1]),['g'].map(i=> [i,8.89,19,1]),['h','n'].map(i=> [i,9.97,19,1]),['i'].map(i=> [i,5.06,19,1]),['j'].map(i=> [i,4.16,19,1]),['k'].map(i=> [i,8.77,19,1]),['l','['].map(i=> [i,5.09,19,1]),['m'].map(i=> [i,14.72,19,1]),['o'].map(i=> [i,9.56,19,1]),['p'].map(i=> [i,9.86,19,1]),['q'].map(i=> [i,9.8,19,1]),['r'].map(i=> [i,6.77,19,1]),['s','?'].map(i=> [i,6.84,19,1]),['t'].map(i=> [i,6,19,1]),['u'].map(i=> [i,9.67,19,1]),['w','<','>','~','#','+'].map(i=> [i,12.48,19,1]),['x'].map(i=> [i,8.58,19,1]),['y'].map(i=> [i,7.81,19,1]),['z','*'].map(i=> [i,8,19,1]),['A'].map(i=> [i,12.67,19,1]),['B','T'].map(i=> [i,11.52,19,1]),['C'].map(i=> [i,11.89,19,1]),['D','G','N','Q'].map(i=> [i,14.44,19,1]),['E','Z'].map(i=> [i,12.28,19,1]),['F','P'].map(i=> [i,10.55,19,1]),['H'].map(i=> [i,14.22,19,1]),['I'].map(i=> [i,6.64,19,1]),['J','`'].map(i=> [i,6.23,19,1]),['K'].map(i=> [i,13.86,19,1]),['L'].map(i=> [i,10.73,19,1]),['M'].map(i=> [i,15.59,19,1]),['O'].map(i=> [i,14.61,19,1]),['R'].map(i=> [i,11.7,19,1]),['S','{','}'].map(i=> [i,8.97,19,1]),['U'].map(i=> [i,13.25,19,1]),['V'].map(i=> [i,13.03,19,1]),['W'].map(i=> [i,16.92,19,1]),['X'].map(i=> [i,13.22,19,1]),['Y'].map(i=> [i,12.66,19,1]),[',',':','!',';'].map(i=> [i,4.11,19,1]),['.'].map(i=> [i,4.13,19,1]),['/','|'].map(i=> [i,9.36,19,1]),['\"'].map(i=> [i,7.61,19,1]),['='].map(i=> [i,12.5,19,1]),['@'].map(i=> [i,17.16,19,1]),['$'].map(i=> [i,8.39,19,1]),['%'].map(i=> [i,15.41,19,1]),['^'].map(i=> [i,9.38,19,1]),['&'].map(i=> [i,13.64,19,1]),['('].map(i=> [i,5.48,19,1]),[')'].map(i=> [i,5.47,19,1]),['_'].map(i=> [i,9.59,19,1]),['-'].map(i=> [i,5.88,19,1]),[']'].map(i=> [i,5.08,19,1]),['\\'].map(i=> [i,3.33,19,1]),['☑'].map(i=> [i,15.53,19,1]),['☐'].map(i=> [i,15.55,19,1]),['◯'].map(i=> [i,17.84,19,1]),['↵'].map(i=> [i,0,19,1])],"STSong19":[['“'].map(i=> [i,7.53,19,1]),['Z'].map(i=> [i,12.48,19,1]),['R'].map(i=> [i,11.89,19,1]),['&'].map(i=> [i,13.86,19,1]),['B','T'].map(i=> [i,11.7,19,1]),['↵'].map(i=> [i,0,19,1]),['我','！','？','《','》','；','，','、','。','（','）','：','【','】'].map(i=> [i,19,19,1]),['”'].map(i=> [i,7.66,19,1]),['‘'].map(i=> [i,4.59,19,1]),['’'].map(i=> [i,4.88,19,1]),[' '].map(i=> [i,9.5,19,1]),['0','1','2','4','5','7','8'].map(i=> [i,8.92,19,1]),['3','6','9','v'].map(i=> [i,8.94,19,1]),['a'].map(i=> [i,9.63,19,1]),['b'].map(i=> [i,9.94,19,1]),['d'].map(i=> [i,9.77,19,1]),['c'].map(i=> [i,7.84,19,1]),['e'].map(i=> [i,8.22,19,1]),['f','s','?'].map(i=> [i,6.94,19,1]),['g'].map(i=> [i,9.03,19,1]),['h','n'].map(i=> [i,10.13,19,1]),['i'].map(i=> [i,5.14,19,1]),['j'].map(i=> [i,4.22,19,1]),['k'].map(i=> [i,8.91,19,1]),['l'].map(i=> [i,5.19,19,1]),['m'].map(i=> [i,14.95,19,1]),['o'].map(i=> [i,9.72,19,1]),['p'].map(i=> [i,10.02,19,1]),['q'].map(i=> [i,9.95,19,1]),['r'].map(i=> [i,6.88,19,1]),['t'].map(i=> [i,6.11,19,1]),['u'].map(i=> [i,9.83,19,1]),['w','<','=','~','#','+'].map(i=> [i,12.69,19,1]),['x'].map(i=> [i,8.72,19,1]),['y'].map(i=> [i,7.94,19,1]),['z','*'].map(i=> [i,8.13,19,1]),['A'].map(i=> [i,12.88,19,1]),['C'].map(i=> [i,12.08,19,1]),['D','Q'].map(i=> [i,14.67,19,1]),['E'].map(i=> [i,12.47,19,1]),['F'].map(i=> [i,10.72,19,1]),['G','N'].map(i=> [i,14.66,19,1]),['H'].map(i=> [i,14.47,19,1]),['I'].map(i=> [i,6.73,19,1]),['J','`'].map(i=> [i,6.34,19,1]),['K'].map(i=> [i,14.08,19,1]),['L'].map(i=> [i,10.91,19,1]),['M'].map(i=> [i,15.84,19,1]),['O'].map(i=> [i,14.86,19,1]),['P'].map(i=> [i,10.7,19,1]),['S','{'].map(i=> [i,9.11,19,1]),['U'].map(i=> [i,13.47,19,1]),['V'].map(i=> [i,13.23,19,1]),['W'].map(i=> [i,17.2,19,1]),['X'].map(i=> [i,13.42,19,1]),['Y'].map(i=> [i,12.86,19,1]),[',',':','!',';'].map(i=> [i,4.17,19,1]),['.'].map(i=> [i,4.19,19,1]),['/','^','|'].map(i=> [i,9.52,19,1]),['\"'].map(i=> [i,7.73,19,1]),['>'].map(i=> [i,12.7,19,1]),['@'].map(i=> [i,17.44,19,1]),['$'].map(i=> [i,8.53,19,1]),['%'].map(i=> [i,15.66,19,1]),['(',')'].map(i=> [i,5.56,19,1]),['_'].map(i=> [i,9.75,19,1]),['-'].map(i=> [i,5.97,19,1]),['}'].map(i=> [i,9.13,19,1]),['['].map(i=> [i,5.16,19,1]),[']'].map(i=> [i,5.17,19,1]),['\\'].map(i=> [i,3.38,19,1]),['☑'].map(i=> [i,15.8,19,1]),['☐'].map(i=> [i,15.78,19,1]),['⊙','⭘','℃'].map(i=> [i,19.02,19,1]),['◯'].map(i=> [i,18.14,19,1])],"STSong20":[['我','！','？','《','》','；','，','、','。','（','）','：','【','】'].map(i=> [i,20,20,1]),['“'].map(i=> [i,7.92,20,1]),['”'].map(i=> [i,8.06,20,1]),['‘'].map(i=> [i,4.83,20,1]),['’'].map(i=> [i,5.14,20,1]),[' '].map(i=> [i,10,20,1]),['0','3','6','9','v'].map(i=> [i,9.41,20,1]),['1','2','4','5','7','8'].map(i=> [i,9.39,20,1]),['a'].map(i=> [i,10.13,20,1]),['b'].map(i=> [i,10.45,20,1]),['d'].map(i=> [i,10.28,20,1]),['c'].map(i=> [i,8.25,20,1]),['e'].map(i=> [i,8.66,20,1]),['f'].map(i=> [i,7.3,20,1]),['g'].map(i=> [i,9.52,20,1]),['h','n'].map(i=> [i,10.66,20,1]),['i'].map(i=> [i,5.42,20,1]),['j'].map(i=> [i,4.44,20,1]),['k'].map(i=> [i,9.38,20,1]),['l'].map(i=> [i,5.45,20,1]),['m'].map(i=> [i,15.73,20,1]),['o'].map(i=> [i,10.23,20,1]),['p'].map(i=> [i,10.53,20,1]),['q'].map(i=> [i,10.48,20,1]),['r'].map(i=> [i,7.23,20,1]),['s','?'].map(i=> [i,7.31,20,1]),['t'].map(i=> [i,6.42,20,1]),['u'].map(i=> [i,10.34,20,1]),['w','<'].map(i=> [i,13.34,20,1]),['x'].map(i=> [i,9.19,20,1]),['y'].map(i=> [i,8.34,20,1]),['z'].map(i=> [i,8.56,20,1]),['A'].map(i=> [i,13.55,20,1]),['B'].map(i=> [i,12.33,20,1]),['C'].map(i=> [i,12.7,20,1]),['D','G','Q'].map(i=> [i,15.44,20,1]),['E','Z'].map(i=> [i,13.14,20,1]),['F','P'].map(i=> [i,11.28,20,1]),['H'].map(i=> [i,15.2,20,1]),['I'].map(i=> [i,7.09,20,1]),['J'].map(i=> [i,6.69,20,1]),['K'].map(i=> [i,14.81,20,1]),['L'].map(i=> [i,11.47,20,1]),['M'].map(i=> [i,16.69,20,1]),['N'].map(i=> [i,15.42,20,1]),['O'].map(i=> [i,15.64,20,1]),['R'].map(i=> [i,12.52,20,1]),['S','{','}'].map(i=> [i,9.59,20,1]),['T'].map(i=> [i,12.31,20,1]),['U'].map(i=> [i,14.17,20,1]),['V'].map(i=> [i,13.94,20,1]),['W'].map(i=> [i,18.09,20,1]),['X'].map(i=> [i,14.14,20,1]),['Y'].map(i=> [i,13.53,20,1]),[',','!'].map(i=> [i,4.39,20,1]),['.',':',';'].map(i=> [i,4.41,20,1]),['/','^','|'].map(i=> [i,10.02,20,1]),['\"'].map(i=> [i,8.13,20,1]),['>','=','~','#','+'].map(i=> [i,13.36,20,1]),['`'].map(i=> [i,6.67,20,1]),['@'].map(i=> [i,18.36,20,1]),['$'].map(i=> [i,8.97,20,1]),['%'].map(i=> [i,16.48,20,1]),['&'].map(i=> [i,14.59,20,1]),['*'].map(i=> [i,8.55,20,1]),['(',')'].map(i=> [i,5.86,20,1]),['_'].map(i=> [i,10.25,20,1]),['-'].map(i=> [i,6.27,20,1]),['[',']'].map(i=> [i,5.44,20,1]),['\\'].map(i=> [i,3.56,20,1]),['☑'].map(i=> [i,16.61,20,1]),['☐'].map(i=> [i,16.63,20,1]),['⊙','⭘','℃'].map(i=> [i,20.02,20,1]),['◯'].map(i=> [i,19.08,20,1]),['↵'].map(i=> [i,0,20,1])],"STSong21.3":[['.','!',';'].map(i=> [i,4.67,21,1]),[')'].map(i=> [i,6.23,21,1]),['↵'].map(i=> [i,0,21,1]),['我','！','？','《','》','，','、','。','（','：','【','】','⭘','℃'].map(i=> [i,21.31,21,1]),['；','）','⊙'].map(i=> [i,21.33,21,1]),['“'].map(i=> [i,8.45,21,1]),['”'].map(i=> [i,8.58,21,1]),['‘'].map(i=> [i,5.16,21,1]),['’'].map(i=> [i,5.47,21,1]),[' '].map(i=> [i,10.65,21,1]),['0','3','6','9','v'].map(i=> [i,10.02,21,1]),['1','2','4','5','7','8'].map(i=> [i,10,21,1]),['a'].map(i=> [i,10.78,21,1]),['b'].map(i=> [i,11.14,21,1]),['d'].map(i=> [i,10.95,21,1]),['c'].map(i=> [i,8.78,21,1]),['e'].map(i=> [i,9.22,21,1]),['f'].map(i=> [i,7.77,21,1]),['g'].map(i=> [i,10.14,21,1]),['h','n'].map(i=> [i,11.34,21,1]),['i'].map(i=> [i,5.77,21,1]),['j'].map(i=> [i,4.72,21,1]),['k'].map(i=> [i,9.98,21,1]),['l'].map(i=> [i,5.81,21,1]),['m'].map(i=> [i,16.77,21,1]),['o'].map(i=> [i,10.91,21,1]),['p'].map(i=> [i,11.22,21,1]),['q'].map(i=> [i,11.16,21,1]),['r'].map(i=> [i,7.7,21,1]),['s'].map(i=> [i,7.78,21,1]),['t'].map(i=> [i,6.83,21,1]),['u'].map(i=> [i,11.03,21,1]),['w','<','>','=','#','+'].map(i=> [i,14.22,21,1]),['x'].map(i=> [i,9.77,21,1]),['y'].map(i=> [i,8.91,21,1]),['z','*'].map(i=> [i,9.11,21,1]),['A'].map(i=> [i,14.44,21,1]),['B','T'].map(i=> [i,13.11,21,1]),['C'].map(i=> [i,13.55,21,1]),['D','G','N','Q'].map(i=> [i,16.44,21,1]),['E','Z'].map(i=> [i,13.98,21,1]),['F'].map(i=> [i,12,21,1]),['H'].map(i=> [i,16.2,21,1]),['I'].map(i=> [i,7.56,21,1]),['J','`'].map(i=> [i,7.11,21,1]),['K'].map(i=> [i,15.78,21,1]),['L'].map(i=> [i,12.22,21,1]),['M'].map(i=> [i,17.75,21,1]),['O'].map(i=> [i,16.66,21,1]),['P'].map(i=> [i,12.02,21,1]),['R'].map(i=> [i,13.33,21,1]),['S','{','}'].map(i=> [i,10.22,21,1]),['U'].map(i=> [i,15.09,21,1]),['V'].map(i=> [i,14.84,21,1]),['W'].map(i=> [i,19.27,21,1]),['X'].map(i=> [i,15.06,21,1]),['Y'].map(i=> [i,14.41,21,1]),[',',':'].map(i=> [i,4.69,21,1]),['/','^','|'].map(i=> [i,10.67,21,1]),['?'].map(i=> [i,7.8,21,1]),['\"'].map(i=> [i,8.66,21,1]),['~'].map(i=> [i,14.23,21,1]),['@'].map(i=> [i,19.55,21,1]),['$'].map(i=> [i,9.56,21,1]),['%'].map(i=> [i,17.55,21,1]),['&'].map(i=> [i,15.53,21,1]),['('].map(i=> [i,6.25,21,1]),['_'].map(i=> [i,10.92,21,1]),['-'].map(i=> [i,6.69,21,1]),['['].map(i=> [i,5.78,21,1]),[']'].map(i=> [i,5.8,21,1]),['\\'].map(i=> [i,3.78,21,1]),['☑'].map(i=> [i,17.7,21,1]),['☐'].map(i=> [i,17.69,21,1]),['◯'].map(i=> [i,20.33,21,1])],"STSong24":[['↵'].map(i=> [i,0,24,1]),['L'].map(i=> [i,13.77,24,1]),['我','！','？','《','》','；','，','、','。','（','）','：','【','】','℃'].map(i=> [i,24,24,1]),['“'].map(i=> [i,9.52,24,1]),['”'].map(i=> [i,9.66,24,1]),['‘'].map(i=> [i,5.8,24,1]),['’'].map(i=> [i,6.17,24,1]),[' '].map(i=> [i,12,24,1]),['0','2','4','5','7','8'].map(i=> [i,11.27,24,1]),['1','3','6','9','v'].map(i=> [i,11.28,24,1]),['a'].map(i=> [i,12.16,24,1]),['b'].map(i=> [i,12.55,24,1]),['d'].map(i=> [i,12.33,24,1]),['c'].map(i=> [i,9.91,24,1]),['e'].map(i=> [i,10.38,24,1]),['f'].map(i=> [i,8.75,24,1]),['g'].map(i=> [i,11.41,24,1]),['h'].map(i=> [i,12.78,24,1]),['i'].map(i=> [i,6.5,24,1]),['j'].map(i=> [i,5.31,24,1]),['k'].map(i=> [i,11.25,24,1]),['l'].map(i=> [i,6.55,24,1]),['m'].map(i=> [i,18.88,24,1]),['n'].map(i=> [i,12.8,24,1]),['o'].map(i=> [i,12.27,24,1]),['p'].map(i=> [i,12.64,24,1]),['q'].map(i=> [i,12.58,24,1]),['r'].map(i=> [i,8.67,24,1]),['s','?'].map(i=> [i,8.78,24,1]),['t'].map(i=> [i,7.69,24,1]),['u'].map(i=> [i,12.42,24,1]),['w','<','='].map(i=> [i,16.02,24,1]),['x'].map(i=> [i,11.02,24,1]),['y'].map(i=> [i,10.02,24,1]),['z','*'].map(i=> [i,10.27,24,1]),['A'].map(i=> [i,16.27,24,1]),['B','T'].map(i=> [i,14.78,24,1]),['C'].map(i=> [i,15.25,24,1]),['D','G','N'].map(i=> [i,18.52,24,1]),['E','Z'].map(i=> [i,15.77,24,1]),['F'].map(i=> [i,13.53,24,1]),['H'].map(i=> [i,18.25,24,1]),['I'].map(i=> [i,8.52,24,1]),['J'].map(i=> [i,8.02,24,1]),['K'].map(i=> [i,17.77,24,1]),['M'].map(i=> [i,20.02,24,1]),['O'].map(i=> [i,18.77,24,1]),['P'].map(i=> [i,13.52,24,1]),['Q'].map(i=> [i,18.53,24,1]),['R'].map(i=> [i,15.02,24,1]),['S','}'].map(i=> [i,11.5,24,1]),['U'].map(i=> [i,17.02,24,1]),['V'].map(i=> [i,16.72,24,1]),['W'].map(i=> [i,21.7,24,1]),['X'].map(i=> [i,16.97,24,1]),['Y'].map(i=> [i,16.23,24,1]),[',','.',':'].map(i=> [i,5.27,24,1]),['/','^','\\','|'].map(i=> [i,12.02,24,1]),['\"'].map(i=> [i,9.77,24,1]),['>','~','#','+'].map(i=> [i,16.03,24,1]),['`'].map(i=> [i,8,24,1]),['!',';'].map(i=> [i,5.28,24,1]),['@'].map(i=> [i,22.02,24,1]),['$'].map(i=> [i,10.77,24,1]),['%'].map(i=> [i,19.77,24,1]),['&'].map(i=> [i,17.52,24,1]),['('].map(i=> [i,7.02,24,1]),[')'].map(i=> [i,7.03,24,1]),['_'].map(i=> [i,12.3,24,1]),['-'].map(i=> [i,7.52,24,1]),['{'].map(i=> [i,11.52,24,1]),['['].map(i=> [i,6.53,24,1]),[']'].map(i=> [i,6.52,24,1]),['\''].map(i=> [i,4.27,24,1]),['☑','☐'].map(i=> [i,19.94,24,1]),['⊙','⭘'].map(i=> [i,24.02,24,1]),['◯'].map(i=> [i,22.89,24,1])],"STSong26.7":[['我','》','（'].map(i=> [i,26.7,27,1]),['！','？','《','；','，','、','。','）','：','【','】','⊙','⭘','℃'].map(i=> [i,26.72,27,1]),['“'].map(i=> [i,10.58,27,1]),['”'].map(i=> [i,10.75,27,1]),['‘'].map(i=> [i,6.45,27,1]),['’'].map(i=> [i,6.86,27,1]),[' '].map(i=> [i,13.35,27,1]),['0','2','4','5','7','9','v'].map(i=> [i,12.53,27,1]),['1','3','6','8'].map(i=> [i,12.55,27,1]),['a'].map(i=> [i,13.53,27,1]),['b'].map(i=> [i,13.95,27,1]),['d'].map(i=> [i,13.7,27,1]),['c'].map(i=> [i,11.02,27,1]),['e'].map(i=> [i,11.56,27,1]),['f'].map(i=> [i,9.73,27,1]),['g'].map(i=> [i,12.69,27,1]),['h','n'].map(i=> [i,14.22,27,1]),['i'].map(i=> [i,7.23,27,1]),['j'].map(i=> [i,5.91,27,1]),['k'].map(i=> [i,12.52,27,1]),['l'].map(i=> [i,7.28,27,1]),['m'].map(i=> [i,21,27,1]),['o'].map(i=> [i,13.66,27,1]),['p'].map(i=> [i,14.06,27,1]),['q'].map(i=> [i,13.98,27,1]),['r'].map(i=> [i,9.66,27,1]),['s','?'].map(i=> [i,9.75,27,1]),['t'].map(i=> [i,8.56,27,1]),['u'].map(i=> [i,13.83,27,1]),['w','<','>','=','+'].map(i=> [i,17.83,27,1]),['x'].map(i=> [i,12.23,27,1]),['y'].map(i=> [i,11.16,27,1]),['z','*'].map(i=> [i,11.42,27,1]),['A'].map(i=> [i,18.09,27,1]),['B'].map(i=> [i,16.42,27,1]),['C'].map(i=> [i,16.98,27,1]),['D'].map(i=> [i,20.59,27,1]),['E','Z'].map(i=> [i,17.53,27,1]),['F','P'].map(i=> [i,15.05,27,1]),['G','N','Q'].map(i=> [i,20.61,27,1]),['H'].map(i=> [i,20.3,27,1]),['I'].map(i=> [i,9.47,27,1]),['J','`'].map(i=> [i,8.91,27,1]),['K'].map(i=> [i,19.78,27,1]),['L'].map(i=> [i,15.31,27,1]),['M'].map(i=> [i,22.25,27,1]),['O'].map(i=> [i,20.86,27,1]),['R'].map(i=> [i,16.7,27,1]),['S','{'].map(i=> [i,12.8,27,1]),['T'].map(i=> [i,16.44,27,1]),['U'].map(i=> [i,18.92,27,1]),['V'].map(i=> [i,18.59,27,1]),['W'].map(i=> [i,24.16,27,1]),['X'].map(i=> [i,18.88,27,1]),['Y'].map(i=> [i,18.06,27,1]),[',','.',':',';'].map(i=> [i,5.86,27,1]),['/','^','|'].map(i=> [i,13.38,27,1]),['\"'].map(i=> [i,10.86,27,1]),['~','#'].map(i=> [i,17.81,27,1]),['!'].map(i=> [i,5.88,27,1]),['@'].map(i=> [i,24.5,27,1]),['$'].map(i=> [i,11.98,27,1]),['%'].map(i=> [i,21.98,27,1]),['&'].map(i=> [i,19.47,27,1]),['(',')'].map(i=> [i,7.81,27,1]),['_'].map(i=> [i,13.69,27,1]),['-'].map(i=> [i,8.38,27,1]),['}'].map(i=> [i,12.81,27,1]),['[',']'].map(i=> [i,7.25,27,1]),['\\'].map(i=> [i,13.36,27,1]),['\''].map(i=> [i,4.73,27,1]),['☑'].map(i=> [i,22.19,27,1]),['☐'].map(i=> [i,22.17,27,1]),['◯'].map(i=> [i,25.47,27,1]),['↵'].map(i=> [i,0,27,1])],"STSong29.3":[['我','！','？','《','》','，','、','。','（','：','【','】','⊙','⭘','℃'].map(i=> [i,29.31,29,1]),['；','）'].map(i=> [i,29.33,29,1]),['“'].map(i=> [i,11.63,29,1]),['”'].map(i=> [i,11.8,29,1]),['‘'].map(i=> [i,7.08,29,1]),['’'].map(i=> [i,7.52,29,1]),[' '].map(i=> [i,14.65,29,1]),['0','2','4','6','8'].map(i=> [i,13.77,29,1]),['1','3','5','7','9','v'].map(i=> [i,13.75,29,1]),['a'].map(i=> [i,14.84,29,1]),['b'].map(i=> [i,15.31,29,1]),['d'].map(i=> [i,15.05,29,1]),['c'].map(i=> [i,12.08,29,1]),['e'].map(i=> [i,12.69,29,1]),['f'].map(i=> [i,10.67,29,1]),['g'].map(i=> [i,13.94,29,1]),['h','n'].map(i=> [i,15.59,29,1]),['i'].map(i=> [i,7.94,29,1]),['j'].map(i=> [i,6.48,29,1]),['k'].map(i=> [i,13.73,29,1]),['l'].map(i=> [i,7.98,29,1]),['m'].map(i=> [i,23.05,29,1]),['o'].map(i=> [i,15,29,1]),['p'].map(i=> [i,15.42,29,1]),['q'].map(i=> [i,15.34,29,1]),['r'].map(i=> [i,10.59,29,1]),['s','?'].map(i=> [i,10.7,29,1]),['t'].map(i=> [i,9.39,29,1]),['u'].map(i=> [i,15.17,29,1]),['w','<','>','~','#','+'].map(i=> [i,19.56,29,1]),['x'].map(i=> [i,13.44,29,1]),['y'].map(i=> [i,12.23,29,1]),['z','*'].map(i=> [i,12.52,29,1]),['A'].map(i=> [i,19.86,29,1]),['B','T'].map(i=> [i,18.03,29,1]),['C'].map(i=> [i,18.63,29,1]),['D','G','N','Q'].map(i=> [i,22.61,29,1]),['E','Z'].map(i=> [i,19.23,29,1]),['F'].map(i=> [i,16.5,29,1]),['H'].map(i=> [i,22.28,29,1]),['I'].map(i=> [i,10.39,29,1]),['J','`'].map(i=> [i,9.78,29,1]),['K'].map(i=> [i,21.69,29,1]),['L'].map(i=> [i,16.81,29,1]),['M'].map(i=> [i,24.42,29,1]),['O'].map(i=> [i,22.89,29,1]),['P'].map(i=> [i,16.52,29,1]),['R'].map(i=> [i,18.33,29,1]),['S','{','}'].map(i=> [i,14.05,29,1]),['U'].map(i=> [i,20.77,29,1]),['V'].map(i=> [i,20.41,29,1]),['W'].map(i=> [i,26.5,29,1]),['X'].map(i=> [i,20.7,29,1]),['Y'].map(i=> [i,19.83,29,1]),[',','!'].map(i=> [i,6.42,29,1]),['.',':',';'].map(i=> [i,6.44,29,1]),['/','^','|'].map(i=> [i,14.67,29,1]),['\"'].map(i=> [i,11.91,29,1]),['='].map(i=> [i,19.55,29,1]),['@'].map(i=> [i,26.89,29,1]),['$'].map(i=> [i,13.14,29,1]),['%'].map(i=> [i,24.13,29,1]),['&'].map(i=> [i,21.38,29,1]),['('].map(i=> [i,8.58,29,1]),[')'].map(i=> [i,8.56,29,1]),['_'].map(i=> [i,15.02,29,1]),['-'].map(i=> [i,9.19,29,1]),['['].map(i=> [i,7.95,29,1]),[']'].map(i=> [i,7.97,29,1]),['\\'].map(i=> [i,14.66,29,1]),['\''].map(i=> [i,5.2,29,1]),['☑'].map(i=> [i,24.33,29,1]),['☐'].map(i=> [i,24.34,29,1]),['◯'].map(i=> [i,27.97,29,1]),['↵'].map(i=> [i,0,29,1])],"STSong32":[['↵'].map(i=> [i,0,32,1]),['我','！','？','《','》','；','，','、','。','（','）','：','【','】'].map(i=> [i,32,32,1]),['“'].map(i=> [i,12.69,32,1]),['”'].map(i=> [i,12.88,32,1]),['‘'].map(i=> [i,7.72,32,1]),['’'].map(i=> [i,8.22,32,1]),[' '].map(i=> [i,16,32,1]),['0','2','4','6','8'].map(i=> [i,15.03,32,1]),['1','3','5','7','9','v'].map(i=> [i,15.02,32,1]),['a'].map(i=> [i,16.22,32,1]),['b'].map(i=> [i,16.72,32,1]),['d'].map(i=> [i,16.42,32,1]),['c'].map(i=> [i,13.2,32,1]),['e'].map(i=> [i,13.84,32,1]),['f'].map(i=> [i,11.66,32,1]),['g'].map(i=> [i,15.22,32,1]),['h','n'].map(i=> [i,17.05,32,1]),['i'].map(i=> [i,8.64,32,1]),['j'].map(i=> [i,7.09,32,1]),['k'].map(i=> [i,15,32,1]),['l'].map(i=> [i,8.72,32,1]),['m'].map(i=> [i,25.16,32,1]),['o'].map(i=> [i,16.36,32,1]),['p'].map(i=> [i,16.86,32,1]),['q'].map(i=> [i,16.75,32,1]),['r'].map(i=> [i,11.56,32,1]),['s'].map(i=> [i,11.7,32,1]),['t'].map(i=> [i,10.25,32,1]),['u'].map(i=> [i,16.56,32,1]),['w','<','>','=','~','#','+'].map(i=> [i,21.36,32,1]),['x'].map(i=> [i,14.67,32,1]),['y'].map(i=> [i,13.36,32,1]),['z','*'].map(i=> [i,13.69,32,1]),['A'].map(i=> [i,21.67,32,1]),['B'].map(i=> [i,19.7,32,1]),['C'].map(i=> [i,20.33,32,1]),['D','G','N','Q'].map(i=> [i,24.69,32,1]),['E','Z'].map(i=> [i,21.02,32,1]),['F','P'].map(i=> [i,18.03,32,1]),['H'].map(i=> [i,24.33,32,1]),['I'].map(i=> [i,11.34,32,1]),['J','`'].map(i=> [i,10.67,32,1]),['K'].map(i=> [i,23.7,32,1]),['L'].map(i=> [i,18.34,32,1]),['M'].map(i=> [i,26.67,32,1]),['O'].map(i=> [i,25.02,32,1]),['R'].map(i=> [i,20.02,32,1]),['S','{','}'].map(i=> [i,15.34,32,1]),['T'].map(i=> [i,19.69,32,1]),['U'].map(i=> [i,22.67,32,1]),['V'].map(i=> [i,22.3,32,1]),['W'].map(i=> [i,28.94,32,1]),['X'].map(i=> [i,22.61,32,1]),['Y'].map(i=> [i,21.64,32,1]),[',',':','!'].map(i=> [i,7.02,32,1]),['.',';'].map(i=> [i,7.03,32,1]),['/','^','\\','|'].map(i=> [i,16.02,32,1]),['?'].map(i=> [i,11.69,32,1]),['\"'].map(i=> [i,13.02,32,1]),['@'].map(i=> [i,29.36,32,1]),['$'].map(i=> [i,14.36,32,1]),['%'].map(i=> [i,26.34,32,1]),['&'].map(i=> [i,23.34,32,1]),['(',')'].map(i=> [i,9.36,32,1]),['_'].map(i=> [i,16.39,32,1]),['-'].map(i=> [i,10.03,32,1]),['[',']'].map(i=> [i,8.69,32,1]),['\''].map(i=> [i,5.67,32,1]),['☑','☐'].map(i=> [i,26.58,32,1]),['⊙','⭘','℃'].map(i=> [i,32.02,32,1]),['◯'].map(i=> [i,30.53,32,1])],"STSong34.7":[['我','》','（','）','⊙'].map(i=> [i,34.7,35,1]),['！','？','《','；','，','、','。','：','【','】','⭘','℃'].map(i=> [i,34.72,35,1]),['“'].map(i=> [i,13.75,35,1]),['”'].map(i=> [i,13.97,35,1]),['‘'].map(i=> [i,8.38,35,1]),['’'].map(i=> [i,8.91,35,1]),[' '].map(i=> [i,17.35,35,1]),['0','2','4','6','8','9','v'].map(i=> [i,16.3,35,1]),['1','3','5','7'].map(i=> [i,16.28,35,1]),['a'].map(i=> [i,17.56,35,1]),['b'].map(i=> [i,18.13,35,1]),['d'].map(i=> [i,17.83,35,1]),['c'].map(i=> [i,14.3,35,1]),['e'].map(i=> [i,15,35,1]),['f'].map(i=> [i,12.64,35,1]),['g'].map(i=> [i,16.5,35,1]),['h'].map(i=> [i,18.48,35,1]),['i'].map(i=> [i,9.38,35,1]),['j'].map(i=> [i,7.69,35,1]),['k'].map(i=> [i,16.25,35,1]),['l'].map(i=> [i,9.45,35,1]),['m'].map(i=> [i,27.3,35,1]),['n'].map(i=> [i,18.47,35,1]),['o'].map(i=> [i,17.75,35,1]),['p'].map(i=> [i,18.27,35,1]),['q'].map(i=> [i,18.17,35,1]),['r'].map(i=> [i,12.55,35,1]),['s'].map(i=> [i,12.67,35,1]),['t'].map(i=> [i,11.13,35,1]),['u'].map(i=> [i,17.95,35,1]),['w','<','>','~'].map(i=> [i,23.16,35,1]),['x'].map(i=> [i,15.91,35,1]),['y'].map(i=> [i,14.48,35,1]),['z','*'].map(i=> [i,14.83,35,1]),['A'].map(i=> [i,23.52,35,1]),['B','T'].map(i=> [i,21.36,35,1]),['C'].map(i=> [i,22.05,35,1]),['D','G','N','Q'].map(i=> [i,26.77,35,1]),['E','Z'].map(i=> [i,22.78,35,1]),['F'].map(i=> [i,19.55,35,1]),['H'].map(i=> [i,26.39,35,1]),['I'].map(i=> [i,12.3,35,1]),['J'].map(i=> [i,11.58,35,1]),['K'].map(i=> [i,25.69,35,1]),['L'].map(i=> [i,19.91,35,1]),['M'].map(i=> [i,28.92,35,1]),['O'].map(i=> [i,27.11,35,1]),['P'].map(i=> [i,19.56,35,1]),['R'].map(i=> [i,21.7,35,1]),['S','}'].map(i=> [i,16.64,35,1]),['U'].map(i=> [i,24.58,35,1]),['V'].map(i=> [i,24.17,35,1]),['W'].map(i=> [i,31.38,35,1]),['X'].map(i=> [i,24.52,35,1]),['Y'].map(i=> [i,23.47,35,1]),[',',';'].map(i=> [i,7.61,35,1]),['.',':','!'].map(i=> [i,7.63,35,1]),['/','\\'].map(i=> [i,17.36,35,1]),['?'].map(i=> [i,12.69,35,1]),['\"'].map(i=> [i,14.09,35,1]),['=','#','+'].map(i=> [i,23.17,35,1]),['`'].map(i=> [i,11.56,35,1]),['@'].map(i=> [i,31.83,35,1]),['$'].map(i=> [i,15.55,35,1]),['%'].map(i=> [i,28.58,35,1]),['^','|'].map(i=> [i,17.38,35,1]),['&'].map(i=> [i,25.31,35,1]),['('].map(i=> [i,10.14,35,1]),[')'].map(i=> [i,10.16,35,1]),['_'].map(i=> [i,17.78,35,1]),['-'].map(i=> [i,10.88,35,1]),['{'].map(i=> [i,16.63,35,1]),['[',']'].map(i=> [i,9.42,35,1]),['\''].map(i=> [i,6.16,35,1]),['☑','☐'].map(i=> [i,28.81,35,1]),['◯'].map(i=> [i,33.11,35,1]),['↵'].map(i=> [i,0,35,1])],"STSong37.3":[['我','！','？','《','》','，','、','。','（','：','【','】','⭘','℃'].map(i=> [i,37.31,37,1]),['；','）','⊙'].map(i=> [i,37.33,37,1]),['“'].map(i=> [i,14.8,37,1]),['”'].map(i=> [i,15,37,1]),['‘'].map(i=> [i,9.02,37,1]),['’'].map(i=> [i,9.56,37,1]),[' '].map(i=> [i,18.65,37,1]),['0','2','4','7','9','v'].map(i=> [i,17.5,37,1]),['1','3','5','6','8'].map(i=> [i,17.52,37,1]),['a'].map(i=> [i,18.89,37,1]),['b'].map(i=> [i,19.5,37,1]),['d'].map(i=> [i,19.16,37,1]),['c'].map(i=> [i,15.39,37,1]),['e'].map(i=> [i,16.13,37,1]),['f'].map(i=> [i,13.59,37,1]),['g'].map(i=> [i,17.73,37,1]),['h','n'].map(i=> [i,19.86,37,1]),['i'].map(i=> [i,10.08,37,1]),['j'].map(i=> [i,8.27,37,1]),['k'].map(i=> [i,17.47,37,1]),['l'].map(i=> [i,10.16,37,1]),['m'].map(i=> [i,29.34,37,1]),['o'].map(i=> [i,19.08,37,1]),['p'].map(i=> [i,19.63,37,1]),['q'].map(i=> [i,19.53,37,1]),['r'].map(i=> [i,13.47,37,1]),['s','?'].map(i=> [i,13.64,37,1]),['t'].map(i=> [i,11.95,37,1]),['u'].map(i=> [i,19.3,37,1]),['w','~','#','+'].map(i=> [i,24.91,37,1]),['x'].map(i=> [i,17.09,37,1]),['y'].map(i=> [i,15.58,37,1]),['z','*'].map(i=> [i,15.94,37,1]),['A'].map(i=> [i,25.27,37,1]),['B','T'].map(i=> [i,22.95,37,1]),['C'].map(i=> [i,23.7,37,1]),['D','N','Q'].map(i=> [i,28.78,37,1]),['E','Z'].map(i=> [i,24.48,37,1]),['F','P'].map(i=> [i,21.02,37,1]),['G'].map(i=> [i,28.77,37,1]),['H'].map(i=> [i,28.36,37,1]),['I'].map(i=> [i,13.22,37,1]),['J','`'].map(i=> [i,12.44,37,1]),['K'].map(i=> [i,27.63,37,1]),['L'].map(i=> [i,21.39,37,1]),['M'].map(i=> [i,31.08,37,1]),['O'].map(i=> [i,29.14,37,1]),['R'].map(i=> [i,23.33,37,1]),['S','{'].map(i=> [i,17.88,37,1]),['U'].map(i=> [i,26.42,37,1]),['V'].map(i=> [i,25.98,37,1]),['W'].map(i=> [i,33.73,37,1]),['X'].map(i=> [i,26.34,37,1]),['Y'].map(i=> [i,25.23,37,1]),[',','.',':',';'].map(i=> [i,8.19,37,1]),['/','^'].map(i=> [i,18.66,37,1]),['\"'].map(i=> [i,15.16,37,1]),['<','>','='].map(i=> [i,24.89,37,1]),['!'].map(i=> [i,8.17,37,1]),['@'].map(i=> [i,34.22,37,1]),['$'].map(i=> [i,16.72,37,1]),['%'].map(i=> [i,30.72,37,1]),['&'].map(i=> [i,27.22,37,1]),['(',')'].map(i=> [i,10.91,37,1]),['_'].map(i=> [i,19.11,37,1]),['-'].map(i=> [i,11.69,37,1]),['}'].map(i=> [i,17.89,37,1]),['['].map(i=> [i,10.13,37,1]),[']'].map(i=> [i,10.11,37,1]),['\\','|'].map(i=> [i,18.67,37,1]),['\''].map(i=> [i,6.61,37,1]),['☑'].map(i=> [i,30.98,37,1]),['☐'].map(i=> [i,30.97,37,1]),['◯'].map(i=> [i,35.58,37,1]),['↵'].map(i=> [i,0,37,1])],"STSong48":[['我','！','？','《','》','；','，','、','。','（','）','：','【','】'].map(i=> [i,48,48,1]),['“'].map(i=> [i,19.02,48,1]),['”'].map(i=> [i,19.31,48,1]),['‘'].map(i=> [i,11.58,48,1]),['’'].map(i=> [i,12.31,48,1]),[' '].map(i=> [i,24,48,1]),['0','4','8'].map(i=> [i,22.52,48,1]),['1','2','3','5','6','7','9','v'].map(i=> [i,22.53,48,1]),['a'].map(i=> [i,24.31,48,1]),['b'].map(i=> [i,25.08,48,1]),['d'].map(i=> [i,24.64,48,1]),['c'].map(i=> [i,19.78,48,1]),['e'].map(i=> [i,20.75,48,1]),['f'].map(i=> [i,17.48,48,1]),['g'].map(i=> [i,22.81,48,1]),['h'].map(i=> [i,25.55,48,1]),['i'].map(i=> [i,12.97,48,1]),['j'].map(i=> [i,10.63,48,1]),['k'].map(i=> [i,22.48,48,1]),['l'].map(i=> [i,13.08,48,1]),['m'].map(i=> [i,37.73,48,1]),['n'].map(i=> [i,25.56,48,1]),['o'].map(i=> [i,24.53,48,1]),['p'].map(i=> [i,25.27,48,1]),['q'].map(i=> [i,25.13,48,1]),['r'].map(i=> [i,17.34,48,1]),['s','?'].map(i=> [i,17.53,48,1]),['t'].map(i=> [i,15.38,48,1]),['u'].map(i=> [i,24.83,48,1]),['w','<','>','=','~','#','+'].map(i=> [i,32.03,48,1]),['x'].map(i=> [i,22,48,1]),['y'].map(i=> [i,20.03,48,1]),['z','*'].map(i=> [i,20.52,48,1]),['A'].map(i=> [i,32.52,48,1]),['B'].map(i=> [i,29.53,48,1]),['C'].map(i=> [i,30.5,48,1]),['D','G'].map(i=> [i,37.02,48,1]),['E'].map(i=> [i,31.5,48,1]),['F'].map(i=> [i,27.05,48,1]),['H'].map(i=> [i,36.5,48,1]),['I'].map(i=> [i,17.02,48,1]),['J'].map(i=> [i,16,48,1]),['K'].map(i=> [i,35.53,48,1]),['L'].map(i=> [i,27.52,48,1]),['M'].map(i=> [i,40,48,1]),['N','Q'].map(i=> [i,37.03,48,1]),['O'].map(i=> [i,37.5,48,1]),['P'].map(i=> [i,27.03,48,1]),['R'].map(i=> [i,30.02,48,1]),['S','}'].map(i=> [i,23,48,1]),['T'].map(i=> [i,29.55,48,1]),['U'].map(i=> [i,34,48,1]),['V'].map(i=> [i,33.42,48,1]),['W'].map(i=> [i,43.41,48,1]),['X'].map(i=> [i,33.91,48,1]),['Y'].map(i=> [i,32.45,48,1]),['Z'].map(i=> [i,31.52,48,1]),[',',':','!'].map(i=> [i,10.52,48,1]),['.',';'].map(i=> [i,10.53,48,1]),['/','^','\\','|'].map(i=> [i,24.02,48,1]),['\"'].map(i=> [i,19.52,48,1]),['`'].map(i=> [i,15.98,48,1]),['@'].map(i=> [i,44.03,48,1]),['$'].map(i=> [i,21.53,48,1]),['%'].map(i=> [i,39.52,48,1]),['&'].map(i=> [i,35,48,1]),['(',')'].map(i=> [i,14.03,48,1]),['_'].map(i=> [i,24.59,48,1]),['-'].map(i=> [i,15.03,48,1]),['{'].map(i=> [i,23.02,48,1]),['['].map(i=> [i,13.03,48,1]),[']'].map(i=> [i,13.02,48,1]),['\''].map(i=> [i,8.52,48,1]),['☑','☐'].map(i=> [i,39.86,48,1]),['⊙','⭘','℃'].map(i=> [i,48.02,48,1]),['◯'].map(i=> [i,45.8,48,1]),['↵'].map(i=> [i,0,48,1])],"STSong58.7":[['我','》','；','）','⭘'].map(i=> [i,58.7,58,1]),['！','？','《','，','、','。','（','：','【','】','⊙','℃'].map(i=> [i,58.72,58,1]),['“'].map(i=> [i,23.25,58,1]),['”'].map(i=> [i,23.63,58,1]),['‘'].map(i=> [i,14.16,58,1]),['’'].map(i=> [i,15.05,58,1]),[' '].map(i=> [i,29.35,58,1]),['0'].map(i=> [i,27.53,58,1]),['1','2','3','4','5','6','7','8','9','v'].map(i=> [i,27.55,58,1]),['a'].map(i=> [i,29.72,58,1]),['b'].map(i=> [i,30.66,58,1]),['d'].map(i=> [i,30.13,58,1]),['c'].map(i=> [i,24.2,58,1]),['e'].map(i=> [i,25.38,58,1]),['f'].map(i=> [i,21.38,58,1]),['g'].map(i=> [i,27.91,58,1]),['h','n'].map(i=> [i,31.23,58,1]),['i'].map(i=> [i,15.88,58,1]),['j'].map(i=> [i,12.98,58,1]),['k'].map(i=> [i,27.48,58,1]),['l'].map(i=> [i,15.98,58,1]),['m'].map(i=> [i,46.16,58,1]),['o'].map(i=> [i,30.02,58,1]),['p'].map(i=> [i,30.89,58,1]),['q'].map(i=> [i,30.72,58,1]),['r'].map(i=> [i,21.2,58,1]),['s','?'].map(i=> [i,21.44,58,1]),['t'].map(i=> [i,18.81,58,1]),['u'].map(i=> [i,30.36,58,1]),['w','<','>','=','#','+'].map(i=> [i,39.17,58,1]),['x'].map(i=> [i,26.89,58,1]),['y'].map(i=> [i,24.5,58,1]),['z','*'].map(i=> [i,25.08,58,1]),['A'].map(i=> [i,39.75,58,1]),['B'].map(i=> [i,36.13,58,1]),['C'].map(i=> [i,37.28,58,1]),['D','G','N'].map(i=> [i,45.28,58,1]),['E','Z'].map(i=> [i,38.52,58,1]),['F','P'].map(i=> [i,33.06,58,1]),['H'].map(i=> [i,44.63,58,1]),['I'].map(i=> [i,20.8,58,1]),['J','`'].map(i=> [i,19.56,58,1]),['K'].map(i=> [i,43.45,58,1]),['L'].map(i=> [i,33.66,58,1]),['M'].map(i=> [i,48.91,58,1]),['O'].map(i=> [i,45.86,58,1]),['Q'].map(i=> [i,45.27,58,1]),['R'].map(i=> [i,36.7,58,1]),['S','}'].map(i=> [i,28.14,58,1]),['T'].map(i=> [i,36.11,58,1]),['U'].map(i=> [i,41.58,58,1]),['V'].map(i=> [i,40.88,58,1]),['W'].map(i=> [i,53.08,58,1]),['X'].map(i=> [i,41.45,58,1]),['Y'].map(i=> [i,39.7,58,1]),[',','.','!'].map(i=> [i,12.88,58,1]),['/','^','\\','|'].map(i=> [i,29.36,58,1]),['\"'].map(i=> [i,23.86,58,1]),[':',';'].map(i=> [i,12.86,58,1]),['~'].map(i=> [i,39.16,58,1]),['@'].map(i=> [i,53.84,58,1]),['$'].map(i=> [i,26.31,58,1]),['%'].map(i=> [i,48.33,58,1]),['&'].map(i=> [i,42.81,58,1]),['(',')'].map(i=> [i,17.16,58,1]),['_'].map(i=> [i,30.08,58,1]),['-'].map(i=> [i,18.39,58,1]),['{'].map(i=> [i,28.13,58,1]),['[',']'].map(i=> [i,15.92,58,1]),['\''].map(i=> [i,10.41,58,1]),['☑'].map(i=> [i,48.73,58,1]),['☐'].map(i=> [i,48.75,58,1]),['◯'].map(i=> [i,56,58,1]),['↵'].map(i=> [i,0,58,1])],"FangSong_GB231210.7":[['↵'].map(i=> [i,0,15,1]),['e','h'].map(i=> [i,5.95,15,1]),['A'].map(i=> [i,7.05,15,1]),['H'].map(i=> [i,7.72,15,1]),['F'].map(i=> [i,6.19,15,1]),['>'].map(i=> [i,6.5,15,1]),['x'].map(i=> [i,5.47,15,1]),['s','*'].map(i=> [i,5.42,15,1]),['d','o','q'].map(i=> [i,6.28,15,1]),['P'].map(i=> [i,6.89,15,1]),['N'].map(i=> [i,7.7,15,1]),['b','p'].map(i=> [i,6.3,15,1]),['W'].map(i=> [i,9.97,15,1]),['z'].map(i=> [i,5.22,15,1]),['1'].map(i=> [i,4.3,15,1]),['f'].map(i=> [i,4,15,1]),['我','》','（'].map(i=> [i,10.7,15,1]),['！','？','《','；','，','、','。','）','：','【','】','⭘'].map(i=> [i,10.72,15,1]),['“'].map(i=> [i,5.75,15,1]),['”','?'].map(i=> [i,5.77,15,1]),['‘','’'].map(i=> [i,3.66,15,1]),[' '].map(i=> [i,5.35,15,1]),['0','2','3','4','5','6','8','9','#','$'].map(i=> [i,6.44,15,1]),['7'].map(i=> [i,5.86,15,1]),['a','n','u'].map(i=> [i,6,15,1]),['c'].map(i=> [i,5.88,15,1]),['g'].map(i=> [i,6.34,15,1]),['i'].map(i=> [i,2.77,15,1]),['j'].map(i=> [i,2.88,15,1]),['k'].map(i=> [i,5.67,15,1]),['l'].map(i=> [i,2.53,15,1]),['m'].map(i=> [i,9.16,15,1]),['r'].map(i=> [i,3.92,15,1]),['t'].map(i=> [i,3.81,15,1]),['v'].map(i=> [i,5.17,15,1]),['w'].map(i=> [i,8.09,15,1]),['y'].map(i=> [i,5.33,15,1]),['B'].map(i=> [i,7.27,15,1]),['C'].map(i=> [i,7.8,15,1]),['D'].map(i=> [i,7.58,15,1]),['E'].map(i=> [i,6.83,15,1]),['G'].map(i=> [i,8.02,15,1]),['I'].map(i=> [i,2.56,15,1]),['J'].map(i=> [i,5.55,15,1]),['K'].map(i=> [i,7.39,15,1]),['L'].map(i=> [i,6.31,15,1]),['M'].map(i=> [i,9.45,15,1]),['O','Q'].map(i=> [i,8.22,15,1]),['R'].map(i=> [i,7.25,15,1]),['S'].map(i=> [i,6.78,15,1]),['T'].map(i=> [i,6.64,15,1]),['U'].map(i=> [i,7.66,15,1]),['V','X'].map(i=> [i,6.84,15,1]),['Y'].map(i=> [i,7.09,15,1]),['Z'].map(i=> [i,6.69,15,1]),[',','.',':',';'].map(i=> [i,2.84,15,1]),['/','~','_','\\'].map(i=> [i,5.36,15,1]),['\"'].map(i=> [i,4.58,15,1]),['<','=','-','+'].map(i=> [i,6.48,15,1]),['`',')','{','}','['].map(i=> [i,3.58,15,1]),['!','(',']'].map(i=> [i,3.59,15,1]),['@'].map(i=> [i,9.19,15,1]),['%'].map(i=> [i,10.39,15,1]),['^'].map(i=> [i,5.56,15,1]),['&'].map(i=> [i,7.78,15,1]),['|'].map(i=> [i,2.09,15,1]),['\''].map(i=> [i,2.64,15,1]),['☑','☐'].map(i=> [i,8.89,15,1]),['⊙'].map(i=> [i,9.31,15,1]),['◯'].map(i=> [i,10.22,15,1]),['℃'].map(i=> [i,9.88,15,1])],"FangSong_GB231212":[['`','(','['].map(i=> [i,4,17,1]),['↵'].map(i=> [i,0,17,1]),['我','！','？','《','》','；','，','、','。','（','）','：','【','】'].map(i=> [i,12,17,1]),['“','”','?'].map(i=> [i,6.45,17,1]),['‘'].map(i=> [i,4.09,17,1]),['’'].map(i=> [i,4.11,17,1]),[' '].map(i=> [i,6,17,1]),['0','2','3','5','6','8','9','#','$'].map(i=> [i,7.22,17,1]),['1'].map(i=> [i,4.83,17,1]),['4'].map(i=> [i,7.2,17,1]),['7','c'].map(i=> [i,6.58,17,1]),['a','n'].map(i=> [i,6.72,17,1]),['b','d','o','p','q'].map(i=> [i,7.05,17,1]),['e'].map(i=> [i,6.67,17,1]),['f'].map(i=> [i,4.5,17,1]),['g'].map(i=> [i,7.11,17,1]),['h'].map(i=> [i,6.69,17,1]),['i'].map(i=> [i,3.08,17,1]),['j'].map(i=> [i,3.22,17,1]),['k'].map(i=> [i,6.38,17,1]),['l'].map(i=> [i,2.83,17,1]),['m'].map(i=> [i,10.28,17,1]),['r'].map(i=> [i,4.41,17,1]),['s'].map(i=> [i,6.08,17,1]),['t'].map(i=> [i,4.27,17,1]),['u'].map(i=> [i,6.73,17,1]),['v'].map(i=> [i,5.8,17,1]),['w'].map(i=> [i,9.08,17,1]),['x'].map(i=> [i,6.13,17,1]),['y'].map(i=> [i,5.97,17,1]),['z'].map(i=> [i,5.86,17,1]),['A'].map(i=> [i,7.91,17,1]),['B'].map(i=> [i,8.14,17,1]),['C'].map(i=> [i,8.75,17,1]),['D'].map(i=> [i,8.48,17,1]),['E','X'].map(i=> [i,7.66,17,1]),['F'].map(i=> [i,6.94,17,1]),['G'].map(i=> [i,9,17,1]),['H'].map(i=> [i,8.66,17,1]),['I'].map(i=> [i,2.86,17,1]),['J','^'].map(i=> [i,6.22,17,1]),['K'].map(i=> [i,8.3,17,1]),['L'].map(i=> [i,7.06,17,1]),['M'].map(i=> [i,10.61,17,1]),['N'].map(i=> [i,8.64,17,1]),['O','Q'].map(i=> [i,9.22,17,1]),['P'].map(i=> [i,7.72,17,1]),['R'].map(i=> [i,8.13,17,1]),['S'].map(i=> [i,7.61,17,1]),['T'].map(i=> [i,7.44,17,1]),['U'].map(i=> [i,8.58,17,1]),['V'].map(i=> [i,7.69,17,1]),['W'].map(i=> [i,11.17,17,1]),['Y'].map(i=> [i,7.97,17,1]),['Z'].map(i=> [i,7.5,17,1]),[',','.',':',';'].map(i=> [i,3.19,17,1]),['/','~','_','\\'].map(i=> [i,6.02,17,1]),['\"'].map(i=> [i,5.14,17,1]),['<','+'].map(i=> [i,7.27,17,1]),['>','=','-'].map(i=> [i,7.28,17,1]),['!',')','{','}',']'].map(i=> [i,4.02,17,1]),['@'].map(i=> [i,10.31,17,1]),['%'].map(i=> [i,11.66,17,1]),['&'].map(i=> [i,8.73,17,1]),['*'].map(i=> [i,6.09,17,1]),['|'].map(i=> [i,2.36,17,1]),['\''].map(i=> [i,2.94,17,1]),['☑'].map(i=> [i,9.98,17,1]),['☐'].map(i=> [i,9.97,17,1]),['⊙'].map(i=> [i,10.44,17,1]),['⭘'].map(i=> [i,12.02,17,1]),['◯'].map(i=> [i,11.45,17,1]),['℃'].map(i=> [i,11.08,17,1])],"FangSong_GB231213.3":[['G'].map(i=> [i,9.97,19,1]),['&'].map(i=> [i,9.67,19,1]),['b','o','p','q'].map(i=> [i,7.81,19,1]),['0','2','4','5','6','8','9','#'].map(i=> [i,8,19,1]),['℃'].map(i=> [i,12.28,19,1]),['H'].map(i=> [i,9.59,19,1]),['↵'].map(i=> [i,0,19,1]),['L'].map(i=> [i,7.84,19,1]),['s'].map(i=> [i,6.73,19,1]),['U'].map(i=> [i,9.52,19,1]),['◯'].map(i=> [i,12.7,19,1]),['我','！','？','《','》','，','、','。','（','：','【','】','⭘'].map(i=> [i,13.31,19,1]),['；','）'].map(i=> [i,13.33,19,1]),['“','”','?'].map(i=> [i,7.16,19,1]),['‘'].map(i=> [i,4.55,19,1]),['’'].map(i=> [i,4.53,19,1]),[' '].map(i=> [i,6.65,19,1]),['1'].map(i=> [i,5.34,19,1]),['3','$'].map(i=> [i,7.98,19,1]),['7'].map(i=> [i,7.28,19,1]),['a','n'].map(i=> [i,7.45,19,1]),['d'].map(i=> [i,7.8,19,1]),['c'].map(i=> [i,7.3,19,1]),['e'].map(i=> [i,7.39,19,1]),['f'].map(i=> [i,4.98,19,1]),['g'].map(i=> [i,7.88,19,1]),['h'].map(i=> [i,7.41,19,1]),['i'].map(i=> [i,3.42,19,1]),['j'].map(i=> [i,3.56,19,1]),['k'].map(i=> [i,7.06,19,1]),['l'].map(i=> [i,3.14,19,1]),['m'].map(i=> [i,11.38,19,1]),['r'].map(i=> [i,4.86,19,1]),['t'].map(i=> [i,4.73,19,1]),['u'].map(i=> [i,7.47,19,1]),['v'].map(i=> [i,6.42,19,1]),['w'].map(i=> [i,10.06,19,1]),['x'].map(i=> [i,6.78,19,1]),['y'].map(i=> [i,6.61,19,1]),['z'].map(i=> [i,6.5,19,1]),['A'].map(i=> [i,8.75,19,1]),['B'].map(i=> [i,9.02,19,1]),['C'].map(i=> [i,9.7,19,1]),['D'].map(i=> [i,9.41,19,1]),['E','X'].map(i=> [i,8.48,19,1]),['F'].map(i=> [i,7.69,19,1]),['I'].map(i=> [i,3.17,19,1]),['J'].map(i=> [i,6.89,19,1]),['K'].map(i=> [i,9.19,19,1]),['M'].map(i=> [i,11.73,19,1]),['N'].map(i=> [i,9.58,19,1]),['O','Q'].map(i=> [i,10.22,19,1]),['P'].map(i=> [i,8.56,19,1]),['R'].map(i=> [i,9,19,1]),['S'].map(i=> [i,8.42,19,1]),['T'].map(i=> [i,8.25,19,1]),['V'].map(i=> [i,8.52,19,1]),['W'].map(i=> [i,12.38,19,1]),['Y'].map(i=> [i,8.83,19,1]),['Z'].map(i=> [i,8.31,19,1]),[',',';'].map(i=> [i,3.53,19,1]),['.',':'].map(i=> [i,3.52,19,1]),['/','~'].map(i=> [i,6.67,19,1]),['\"'].map(i=> [i,5.7,19,1]),['<','>','=','-','+'].map(i=> [i,8.06,19,1]),['`','(','{','['].map(i=> [i,4.44,19,1]),['!',')','}',']'].map(i=> [i,4.45,19,1]),['@'].map(i=> [i,11.42,19,1]),['%'].map(i=> [i,12.92,19,1]),['^'].map(i=> [i,6.91,19,1]),['*'].map(i=> [i,6.75,19,1]),['_','\\'].map(i=> [i,6.66,19,1]),['|'].map(i=> [i,2.61,19,1]),['\''].map(i=> [i,3.27,19,1]),['☑'].map(i=> [i,11.06,19,1]),['☐'].map(i=> [i,11.05,19,1]),['⊙'].map(i=> [i,11.56,19,1])],"FangSong_GB231214":[['U'].map(i=> [i,10,20,1]),['L'].map(i=> [i,8.25,20,1]),['G'].map(i=> [i,10.48,20,1]),['s','*'].map(i=> [i,7.09,20,1]),['◯'].map(i=> [i,13.36,20,1]),['V'].map(i=> [i,8.97,20,1]),['↵'].map(i=> [i,0,20,1]),['我','！','？','《','》','；','，','、','。','（','）','：','【','】'].map(i=> [i,14,20,1]),['“','”','?'].map(i=> [i,7.53,20,1]),['‘'].map(i=> [i,4.77,20,1]),['’'].map(i=> [i,4.78,20,1]),[' '].map(i=> [i,7,20,1]),['0','2','4','8','$'].map(i=> [i,8.41,20,1]),['1'].map(i=> [i,5.64,20,1]),['3','5','6','9','#'].map(i=> [i,8.42,20,1]),['7','c'].map(i=> [i,7.67,20,1]),['a','n'].map(i=> [i,7.84,20,1]),['b','d','o','p','q'].map(i=> [i,8.22,20,1]),['e'].map(i=> [i,7.78,20,1]),['f'].map(i=> [i,5.23,20,1]),['g'].map(i=> [i,8.3,20,1]),['h'].map(i=> [i,7.8,20,1]),['i'].map(i=> [i,3.59,20,1]),['j'].map(i=> [i,3.77,20,1]),['k'].map(i=> [i,7.42,20,1]),['l'].map(i=> [i,3.3,20,1]),['m'].map(i=> [i,11.98,20,1]),['r'].map(i=> [i,5.13,20,1]),['t'].map(i=> [i,4.98,20,1]),['u'].map(i=> [i,7.86,20,1]),['v'].map(i=> [i,6.77,20,1]),['w'].map(i=> [i,10.58,20,1]),['x'].map(i=> [i,7.14,20,1]),['y'].map(i=> [i,6.97,20,1]),['z'].map(i=> [i,6.83,20,1]),['A'].map(i=> [i,9.22,20,1]),['B','R'].map(i=> [i,9.48,20,1]),['C'].map(i=> [i,10.2,20,1]),['D'].map(i=> [i,9.91,20,1]),['E','X'].map(i=> [i,8.94,20,1]),['F'].map(i=> [i,8.09,20,1]),['H'].map(i=> [i,10.09,20,1]),['I'].map(i=> [i,3.33,20,1]),['J','^'].map(i=> [i,7.27,20,1]),['K'].map(i=> [i,9.67,20,1]),['M'].map(i=> [i,12.36,20,1]),['N'].map(i=> [i,10.08,20,1]),['O','Q'].map(i=> [i,10.75,20,1]),['P'].map(i=> [i,9.02,20,1]),['S'].map(i=> [i,8.86,20,1]),['T'].map(i=> [i,8.67,20,1]),['W'].map(i=> [i,13.03,20,1]),['Y'].map(i=> [i,9.28,20,1]),['Z'].map(i=> [i,8.75,20,1]),[',',';'].map(i=> [i,3.72,20,1]),['.',':'].map(i=> [i,3.7,20,1]),['/','~','_','\\'].map(i=> [i,7.02,20,1]),['\"'].map(i=> [i,6,20,1]),['<'].map(i=> [i,8.5,20,1]),['>','=','-','+'].map(i=> [i,8.48,20,1]),['`','!','(',')','{','[',']'].map(i=> [i,4.67,20,1]),['@'].map(i=> [i,12.03,20,1]),['%'].map(i=> [i,13.59,20,1]),['&'].map(i=> [i,10.17,20,1]),['}'].map(i=> [i,4.69,20,1]),['|'].map(i=> [i,2.75,20,1]),['\''].map(i=> [i,3.44,20,1]),['☑'].map(i=> [i,11.63,20,1]),['☐'].map(i=> [i,11.64,20,1]),['⊙'].map(i=> [i,12.17,20,1]),['⭘'].map(i=> [i,14.02,20,1]),['℃'].map(i=> [i,12.94,20,1])],"FangSong_GB231215":[['☐'].map(i=> [i,12.47,21,1]),['↵'].map(i=> [i,0,21,1]),['J'].map(i=> [i,7.77,21,1]),['w'].map(i=> [i,11.34,21,1]),['&'].map(i=> [i,10.91,21,1]),['^'].map(i=> [i,7.78,21,1]),['E'].map(i=> [i,9.56,21,1]),['我','！','？','《','》','；','，','、','。','（','）','：','【','】'].map(i=> [i,15,21,1]),['“'].map(i=> [i,8.06,21,1]),['”','?'].map(i=> [i,8.08,21,1]),['‘','’'].map(i=> [i,5.11,21,1]),[' '].map(i=> [i,7.5,21,1]),['0','2','3','4','5','6','8','9','#','$'].map(i=> [i,9.02,21,1]),['1'].map(i=> [i,6.03,21,1]),['7','c'].map(i=> [i,8.22,21,1]),['a'].map(i=> [i,8.39,21,1]),['b','d','o','q'].map(i=> [i,8.81,21,1]),['e'].map(i=> [i,8.34,21,1]),['f'].map(i=> [i,5.61,21,1]),['g'].map(i=> [i,8.88,21,1]),['h'].map(i=> [i,8.36,21,1]),['i'].map(i=> [i,3.86,21,1]),['j'].map(i=> [i,4.02,21,1]),['k'].map(i=> [i,7.95,21,1]),['l'].map(i=> [i,3.55,21,1]),['m'].map(i=> [i,12.83,21,1]),['n','u'].map(i=> [i,8.41,21,1]),['p'].map(i=> [i,8.8,21,1]),['r'].map(i=> [i,5.48,21,1]),['s'].map(i=> [i,7.59,21,1]),['t'].map(i=> [i,5.34,21,1]),['v'].map(i=> [i,7.25,21,1]),['x'].map(i=> [i,7.66,21,1]),['y'].map(i=> [i,7.45,21,1]),['z'].map(i=> [i,7.31,21,1]),['A'].map(i=> [i,9.88,21,1]),['B'].map(i=> [i,10.17,21,1]),['C'].map(i=> [i,10.94,21,1]),['D'].map(i=> [i,10.61,21,1]),['F'].map(i=> [i,8.67,21,1]),['G'].map(i=> [i,11.23,21,1]),['H'].map(i=> [i,10.81,21,1]),['I'].map(i=> [i,3.58,21,1]),['K'].map(i=> [i,10.36,21,1]),['L'].map(i=> [i,8.84,21,1]),['M'].map(i=> [i,13.25,21,1]),['N'].map(i=> [i,10.8,21,1]),['O','Q'].map(i=> [i,11.52,21,1]),['P'].map(i=> [i,9.66,21,1]),['R'].map(i=> [i,10.16,21,1]),['S'].map(i=> [i,9.5,21,1]),['T'].map(i=> [i,9.3,21,1]),['U'].map(i=> [i,10.72,21,1]),['V'].map(i=> [i,9.61,21,1]),['W'].map(i=> [i,13.95,21,1]),['X'].map(i=> [i,9.58,21,1]),['Y'].map(i=> [i,9.95,21,1]),['Z'].map(i=> [i,9.38,21,1]),[',','.',':'].map(i=> [i,3.97,21,1]),['/','~','_','\\'].map(i=> [i,7.52,21,1]),['\"'].map(i=> [i,6.42,21,1]),['<','>','=','-'].map(i=> [i,9.09,21,1]),['`',')','['].map(i=> [i,5,21,1]),['!','(','{','}',']'].map(i=> [i,5.02,21,1]),['@'].map(i=> [i,12.89,21,1]),['%'].map(i=> [i,14.56,21,1]),['*'].map(i=> [i,7.61,21,1]),[';'].map(i=> [i,3.98,21,1]),['+'].map(i=> [i,9.08,21,1]),['|'].map(i=> [i,2.94,21,1]),['\''].map(i=> [i,3.69,21,1]),['☑'].map(i=> [i,12.45,21,1]),['⊙'].map(i=> [i,13.05,21,1]),['⭘'].map(i=> [i,15.02,21,1]),['◯'].map(i=> [i,14.31,21,1]),['℃'].map(i=> [i,13.84,21,1])],"FangSong_GB231214.7":[['↵'].map(i=> [i,0,21,1]),['<','>','=','-','+'].map(i=> [i,8.91,21,1]),['T'].map(i=> [i,9.11,21,1]),['v'].map(i=> [i,7.11,21,1]),['☑','☐'].map(i=> [i,12.22,21,1]),['k'].map(i=> [i,7.8,21,1]),['L'].map(i=> [i,8.66,21,1]),['i'].map(i=> [i,3.78,21,1]),['7','c'].map(i=> [i,8.06,21,1]),['x'].map(i=> [i,7.5,21,1]),['*'].map(i=> [i,7.45,21,1]),['y'].map(i=> [i,7.31,21,1]),['K'].map(i=> [i,10.17,21,1]),['0','3','5','9','#'].map(i=> [i,8.84,21,1]),['C'].map(i=> [i,10.72,21,1]),['R'].map(i=> [i,9.95,21,1]),['E','X'].map(i=> [i,9.38,21,1]),['‘','’'].map(i=> [i,5.02,21,1]),['J'].map(i=> [i,7.61,21,1]),['我','》','（'].map(i=> [i,14.7,21,1]),['！','？','《','；','，','、','。','）','：','【','】','⭘'].map(i=> [i,14.72,21,1]),['“','”','?'].map(i=> [i,7.91,21,1]),[' '].map(i=> [i,7.35,21,1]),['1'].map(i=> [i,5.91,21,1]),['2','4','6','8','$'].map(i=> [i,8.83,21,1]),['a','n'].map(i=> [i,8.23,21,1]),['b','p'].map(i=> [i,8.64,21,1]),['d','o','q'].map(i=> [i,8.63,21,1]),['e'].map(i=> [i,8.17,21,1]),['f'].map(i=> [i,5.5,21,1]),['g'].map(i=> [i,8.7,21,1]),['h'].map(i=> [i,8.19,21,1]),['j'].map(i=> [i,3.94,21,1]),['l'].map(i=> [i,3.47,21,1]),['m'].map(i=> [i,12.58,21,1]),['r'].map(i=> [i,5.38,21,1]),['s'].map(i=> [i,7.44,21,1]),['t'].map(i=> [i,5.23,21,1]),['u'].map(i=> [i,8.25,21,1]),['w'].map(i=> [i,11.11,21,1]),['z'].map(i=> [i,7.17,21,1]),['A'].map(i=> [i,9.67,21,1]),['B'].map(i=> [i,9.97,21,1]),['D'].map(i=> [i,10.39,21,1]),['F'].map(i=> [i,8.5,21,1]),['G'].map(i=> [i,11.02,21,1]),['H'].map(i=> [i,10.59,21,1]),['I'].map(i=> [i,3.5,21,1]),['M'].map(i=> [i,12.98,21,1]),['N'].map(i=> [i,10.58,21,1]),['O'].map(i=> [i,11.3,21,1]),['P'].map(i=> [i,9.45,21,1]),['Q'].map(i=> [i,11.28,21,1]),['S'].map(i=> [i,9.31,21,1]),['U'].map(i=> [i,10.52,21,1]),['V'].map(i=> [i,9.41,21,1]),['W'].map(i=> [i,13.69,21,1]),['Y'].map(i=> [i,9.75,21,1]),['Z'].map(i=> [i,9.19,21,1]),[','].map(i=> [i,3.89,21,1]),['.',':',';'].map(i=> [i,3.91,21,1]),['/','~','_','\\'].map(i=> [i,7.36,21,1]),['\"'].map(i=> [i,6.3,21,1]),['`',')','['].map(i=> [i,4.92,21,1]),['!','(','{','}',']'].map(i=> [i,4.91,21,1]),['@'].map(i=> [i,12.63,21,1]),['%'].map(i=> [i,14.28,21,1]),['^'].map(i=> [i,7.63,21,1]),['&'].map(i=> [i,10.69,21,1]),['|'].map(i=> [i,2.89,21,1]),['\''].map(i=> [i,3.59,21,1]),['⊙'].map(i=> [i,12.78,21,1]),['◯'].map(i=> [i,14.03,21,1]),['℃'].map(i=> [i,13.56,21,1])],"FangSong_GB231216":[['Z'].map(i=> [i,10,22,1]),['↵'].map(i=> [i,0,22,1]),['我','！','？','《','》','；','，','、','。','（','）','：','【','】'].map(i=> [i,16,22,1]),['“'].map(i=> [i,8.59,22,1]),['”','?'].map(i=> [i,8.61,22,1]),['‘'].map(i=> [i,5.45,22,1]),['’'].map(i=> [i,5.47,22,1]),[' '].map(i=> [i,8,22,1]),['0','2','4','6','8','$'].map(i=> [i,9.61,22,1]),['1'].map(i=> [i,6.44,22,1]),['3','5','9','#'].map(i=> [i,9.63,22,1]),['7','c'].map(i=> [i,8.77,22,1]),['a','n'].map(i=> [i,8.95,22,1]),['b','d','o','p','q'].map(i=> [i,9.39,22,1]),['e','h'].map(i=> [i,8.91,22,1]),['f'].map(i=> [i,5.98,22,1]),['g'].map(i=> [i,9.47,22,1]),['i'].map(i=> [i,4.11,22,1]),['j'].map(i=> [i,4.3,22,1]),['k'].map(i=> [i,8.48,22,1]),['l'].map(i=> [i,3.77,22,1]),['m'].map(i=> [i,13.7,22,1]),['r'].map(i=> [i,5.86,22,1]),['s'].map(i=> [i,8.09,22,1]),['t'].map(i=> [i,5.7,22,1]),['u'].map(i=> [i,8.97,22,1]),['v'].map(i=> [i,7.73,22,1]),['w'].map(i=> [i,12.09,22,1]),['x'].map(i=> [i,8.16,22,1]),['y'].map(i=> [i,7.95,22,1]),['z'].map(i=> [i,7.81,22,1]),['A'].map(i=> [i,10.52,22,1]),['B'].map(i=> [i,10.86,22,1]),['C'].map(i=> [i,11.66,22,1]),['D'].map(i=> [i,11.31,22,1]),['E','X'].map(i=> [i,10.2,22,1]),['F'].map(i=> [i,9.25,22,1]),['G'].map(i=> [i,11.98,22,1]),['H','N'].map(i=> [i,11.53,22,1]),['I'].map(i=> [i,3.81,22,1]),['J'].map(i=> [i,8.3,22,1]),['K'].map(i=> [i,11.05,22,1]),['L'].map(i=> [i,9.42,22,1]),['M'].map(i=> [i,14.13,22,1]),['O'].map(i=> [i,12.28,22,1]),['P'].map(i=> [i,10.28,22,1]),['Q'].map(i=> [i,12.3,22,1]),['R'].map(i=> [i,10.83,22,1]),['S'].map(i=> [i,10.13,22,1]),['T'].map(i=> [i,9.92,22,1]),['U'].map(i=> [i,11.44,22,1]),['V'].map(i=> [i,10.25,22,1]),['W'].map(i=> [i,14.89,22,1]),['Y'].map(i=> [i,10.61,22,1]),[',',':'].map(i=> [i,4.23,22,1]),['.',';'].map(i=> [i,4.25,22,1]),['/','~','_','\\'].map(i=> [i,8.02,22,1]),['\"'].map(i=> [i,6.84,22,1]),['<','=','+'].map(i=> [i,9.7,22,1]),['>','-'].map(i=> [i,9.69,22,1]),['`','!','(',')','{','}','[',']'].map(i=> [i,5.34,22,1]),['@'].map(i=> [i,13.73,22,1]),['%'].map(i=> [i,15.53,22,1]),['^'].map(i=> [i,8.31,22,1]),['&'].map(i=> [i,11.63,22,1]),['*'].map(i=> [i,8.11,22,1]),['|'].map(i=> [i,3.13,22,1]),['\''].map(i=> [i,3.92,22,1]),['☑','☐'].map(i=> [i,13.3,22,1]),['⊙'].map(i=> [i,13.91,22,1]),['⭘'].map(i=> [i,16.02,22,1]),['◯'].map(i=> [i,15.28,22,1]),['℃'].map(i=> [i,14.77,22,1])],"FangSong_GB231218.7":[['↵'].map(i=> [i,0,26,1]),['我','》','（'].map(i=> [i,18.7,26,1]),['！','？','《','；','，','、','。','）','：','【','】','⭘'].map(i=> [i,18.72,26,1]),['“'].map(i=> [i,10.05,26,1]),['”','?'].map(i=> [i,10.06,26,1]),['‘','’'].map(i=> [i,6.38,26,1]),[' '].map(i=> [i,9.35,26,1]),['0','2','3','4','5','6','8','9','$'].map(i=> [i,11.23,26,1]),['1'].map(i=> [i,7.52,26,1]),['7','c'].map(i=> [i,10.25,26,1]),['a','n'].map(i=> [i,10.47,26,1]),['b','d','o','q'].map(i=> [i,10.97,26,1]),['e'].map(i=> [i,10.39,26,1]),['f'].map(i=> [i,7,26,1]),['g'].map(i=> [i,11.06,26,1]),['h'].map(i=> [i,10.41,26,1]),['i'].map(i=> [i,4.81,26,1]),['j'].map(i=> [i,5,26,1]),['k'].map(i=> [i,9.91,26,1]),['l'].map(i=> [i,4.42,26,1]),['m'].map(i=> [i,16,26,1]),['p'].map(i=> [i,10.98,26,1]),['r'].map(i=> [i,6.84,26,1]),['s'].map(i=> [i,9.45,26,1]),['t'].map(i=> [i,6.66,26,1]),['u'].map(i=> [i,10.48,26,1]),['v'].map(i=> [i,9.03,26,1]),['w'].map(i=> [i,14.14,26,1]),['x'].map(i=> [i,9.53,26,1]),['y'].map(i=> [i,9.28,26,1]),['z'].map(i=> [i,9.13,26,1]),['A'].map(i=> [i,12.31,26,1]),['B'].map(i=> [i,12.67,26,1]),['C'].map(i=> [i,13.63,26,1]),['D'].map(i=> [i,13.22,26,1]),['E','X'].map(i=> [i,11.92,26,1]),['F'].map(i=> [i,10.81,26,1]),['G'].map(i=> [i,14,26,1]),['H'].map(i=> [i,13.48,26,1]),['I'].map(i=> [i,4.44,26,1]),['J','^'].map(i=> [i,9.69,26,1]),['K'].map(i=> [i,12.92,26,1]),['L'].map(i=> [i,11.02,26,1]),['M'].map(i=> [i,16.5,26,1]),['N'].map(i=> [i,13.47,26,1]),['O','Q'].map(i=> [i,14.36,26,1]),['P'].map(i=> [i,12.02,26,1]),['R'].map(i=> [i,12.66,26,1]),['S'].map(i=> [i,11.83,26,1]),['T'].map(i=> [i,11.59,26,1]),['U'].map(i=> [i,13.38,26,1]),['V'].map(i=> [i,11.97,26,1]),['W'].map(i=> [i,17.41,26,1]),['Y'].map(i=> [i,12.39,26,1]),['Z'].map(i=> [i,11.69,26,1]),[',','.',':',';'].map(i=> [i,4.95,26,1]),['/','_','\\'].map(i=> [i,9.36,26,1]),['\"'].map(i=> [i,8,26,1]),['<','>','=','-','+'].map(i=> [i,11.33,26,1]),['~'].map(i=> [i,9.38,26,1]),['`','(','['].map(i=> [i,6.23,26,1]),['!',')','{','}',']'].map(i=> [i,6.25,26,1]),['@'].map(i=> [i,16.05,26,1]),['#'].map(i=> [i,11.25,26,1]),['%'].map(i=> [i,18.16,26,1]),['&'].map(i=> [i,13.59,26,1]),['*'].map(i=> [i,9.48,26,1]),['|'].map(i=> [i,3.66,26,1]),['\''].map(i=> [i,4.58,26,1]),['☑'].map(i=> [i,15.55,26,1]),['☐'].map(i=> [i,15.53,26,1]),['⊙'].map(i=> [i,16.25,26,1]),['◯'].map(i=> [i,17.84,26,1]),['℃'].map(i=> [i,17.27,26,1])],"FangSong_GB231219":[['↵'].map(i=> [i,0,26,1]),['>','=','+'].map(i=> [i,11.52,26,1]),['k'].map(i=> [i,10.06,26,1]),['7','c'].map(i=> [i,10.41,26,1]),['F'].map(i=> [i,10.98,26,1]),['y'].map(i=> [i,9.45,26,1]),['S'].map(i=> [i,12.02,26,1]),['g'].map(i=> [i,11.25,26,1]),['m'].map(i=> [i,16.25,26,1]),['我','！','？','《','》','；','，','、','。','（','）','：','【','】'].map(i=> [i,19,26,1]),['“'].map(i=> [i,10.2,26,1]),['”','?'].map(i=> [i,10.22,26,1]),['‘'].map(i=> [i,6.48,26,1]),['’'].map(i=> [i,6.47,26,1]),[' '].map(i=> [i,9.5,26,1]),['0','3','5','6','9','#'].map(i=> [i,11.42,26,1]),['1'].map(i=> [i,7.64,26,1]),['2','4','8','$'].map(i=> [i,11.41,26,1]),['a','n'].map(i=> [i,10.64,26,1]),['b','o','q'].map(i=> [i,11.16,26,1]),['d','p'].map(i=> [i,11.14,26,1]),['e'].map(i=> [i,10.56,26,1]),['f'].map(i=> [i,7.11,26,1]),['h'].map(i=> [i,10.58,26,1]),['i'].map(i=> [i,4.88,26,1]),['j'].map(i=> [i,5.09,26,1]),['l'].map(i=> [i,4.48,26,1]),['r'].map(i=> [i,6.95,26,1]),['s'].map(i=> [i,9.61,26,1]),['t'].map(i=> [i,6.75,26,1]),['u'].map(i=> [i,10.66,26,1]),['v'].map(i=> [i,9.17,26,1]),['w'].map(i=> [i,14.38,26,1]),['x'].map(i=> [i,9.67,26,1]),['z'].map(i=> [i,9.27,26,1]),['A'].map(i=> [i,12.5,26,1]),['B'].map(i=> [i,12.88,26,1]),['C'].map(i=> [i,13.84,26,1]),['D'].map(i=> [i,13.44,26,1]),['E'].map(i=> [i,12.11,26,1]),['G'].map(i=> [i,14.22,26,1]),['H'].map(i=> [i,13.7,26,1]),['I'].map(i=> [i,4.52,26,1]),['J'].map(i=> [i,9.84,26,1]),['K'].map(i=> [i,13.13,26,1]),['L'].map(i=> [i,11.19,26,1]),['M'].map(i=> [i,16.77,26,1]),['N'].map(i=> [i,13.69,26,1]),['O'].map(i=> [i,14.58,26,1]),['P'].map(i=> [i,12.22,26,1]),['Q'].map(i=> [i,14.59,26,1]),['R'].map(i=> [i,12.86,26,1]),['T'].map(i=> [i,11.78,26,1]),['U'].map(i=> [i,13.58,26,1]),['V'].map(i=> [i,12.16,26,1]),['W'].map(i=> [i,17.69,26,1]),['X'].map(i=> [i,12.13,26,1]),['Y'].map(i=> [i,12.59,26,1]),['Z'].map(i=> [i,11.86,26,1]),[',','.',':',';'].map(i=> [i,5.03,26,1]),['/','~','_','\\'].map(i=> [i,9.52,26,1]),['\"'].map(i=> [i,8.14,26,1]),['<','-'].map(i=> [i,11.5,26,1]),['`','!','(',')','{','}','[',']'].map(i=> [i,6.34,26,1]),['@'].map(i=> [i,16.31,26,1]),['%'].map(i=> [i,18.45,26,1]),['^'].map(i=> [i,9.86,26,1]),['&'].map(i=> [i,13.8,26,1]),['*'].map(i=> [i,9.64,26,1]),['|'].map(i=> [i,3.72,26,1]),['\''].map(i=> [i,4.64,26,1]),['☑'].map(i=> [i,15.8,26,1]),['☐'].map(i=> [i,15.78,26,1]),['⊙'].map(i=> [i,16.52,26,1]),['⭘'].map(i=> [i,19.02,26,1]),['◯'].map(i=> [i,18.13,26,1]),['℃'].map(i=> [i,17.53,26,1])],"FangSong_GB231220":[['我','！','？','《','》','；','，','、','。','（','）','：','【','】'].map(i=> [i,20,28,1]),['“','”','?'].map(i=> [i,10.75,28,1]),['‘'].map(i=> [i,6.81,28,1]),['’'].map(i=> [i,6.83,28,1]),[' '].map(i=> [i,10,28,1]),['0','2','3','4','5','6','8','9'].map(i=> [i,12.02,28,1]),['1'].map(i=> [i,8.03,28,1]),['7','c'].map(i=> [i,10.95,28,1]),['a'].map(i=> [i,11.19,28,1]),['b','o','p','q'].map(i=> [i,11.73,28,1]),['d'].map(i=> [i,11.75,28,1]),['e'].map(i=> [i,11.11,28,1]),['f'].map(i=> [i,7.48,28,1]),['g'].map(i=> [i,11.83,28,1]),['h'].map(i=> [i,11.14,28,1]),['i'].map(i=> [i,5.13,28,1]),['j'].map(i=> [i,5.36,28,1]),['k'].map(i=> [i,10.59,28,1]),['l'].map(i=> [i,4.72,28,1]),['m'].map(i=> [i,17.11,28,1]),['n'].map(i=> [i,11.2,28,1]),['r'].map(i=> [i,7.31,28,1]),['s'].map(i=> [i,10.13,28,1]),['t'].map(i=> [i,7.11,28,1]),['u'].map(i=> [i,11.22,28,1]),['v'].map(i=> [i,9.66,28,1]),['w'].map(i=> [i,15.11,28,1]),['x'].map(i=> [i,10.2,28,1]),['y'].map(i=> [i,9.94,28,1]),['z'].map(i=> [i,9.75,28,1]),['A'].map(i=> [i,13.16,28,1]),['B'].map(i=> [i,13.56,28,1]),['C'].map(i=> [i,14.58,28,1]),['D'].map(i=> [i,14.13,28,1]),['E','X'].map(i=> [i,12.77,28,1]),['F'].map(i=> [i,11.55,28,1]),['G'].map(i=> [i,14.98,28,1]),['H'].map(i=> [i,14.41,28,1]),['I'].map(i=> [i,4.77,28,1]),['J'].map(i=> [i,10.34,28,1]),['K'].map(i=> [i,13.81,28,1]),['L'].map(i=> [i,11.78,28,1]),['M'].map(i=> [i,17.66,28,1]),['N'].map(i=> [i,14.39,28,1]),['O','Q'].map(i=> [i,15.36,28,1]),['P'].map(i=> [i,12.86,28,1]),['R'].map(i=> [i,13.53,28,1]),['S'].map(i=> [i,12.66,28,1]),['T'].map(i=> [i,12.39,28,1]),['U'].map(i=> [i,14.3,28,1]),['V'].map(i=> [i,12.8,28,1]),['W'].map(i=> [i,18.61,28,1]),['Y'].map(i=> [i,13.25,28,1]),['Z'].map(i=> [i,12.5,28,1]),[',','.',':'].map(i=> [i,5.3,28,1]),['/','~','_','\\'].map(i=> [i,10.02,28,1]),['\"'].map(i=> [i,8.56,28,1]),['<','>','-','+'].map(i=> [i,12.11,28,1]),['='].map(i=> [i,12.13,28,1]),['`','!','(','}','[',']'].map(i=> [i,6.67,28,1]),['@'].map(i=> [i,17.17,28,1]),['#','$'].map(i=> [i,12,28,1]),['%'].map(i=> [i,19.41,28,1]),['^'].map(i=> [i,10.38,28,1]),['&'].map(i=> [i,14.53,28,1]),['*'].map(i=> [i,10.14,28,1]),[')','{'].map(i=> [i,6.69,28,1]),[';'].map(i=> [i,5.28,28,1]),['|'].map(i=> [i,3.92,28,1]),['\''].map(i=> [i,4.89,28,1]),['☑'].map(i=> [i,16.63,28,1]),['☐'].map(i=> [i,16.61,28,1]),['⊙'].map(i=> [i,17.38,28,1]),['⭘'].map(i=> [i,20.02,28,1]),['◯'].map(i=> [i,19.09,28,1]),['℃'].map(i=> [i,18.45,28,1]),['↵'].map(i=> [i,0,28,1])],"FangSong_GB231221.3":[['z'].map(i=> [i,10.39,30,1]),['↵'].map(i=> [i,0,30,1]),['我','！','？','《','》','，','、','。','（','：','【','】','⭘'].map(i=> [i,21.31,30,1]),['；','）'].map(i=> [i,21.33,30,1]),['“','”','?'].map(i=> [i,11.45,30,1]),['‘'].map(i=> [i,7.27,30,1]),['’'].map(i=> [i,7.25,30,1]),[' '].map(i=> [i,10.65,30,1]),['0','2','3','4','6','8','9','#','$'].map(i=> [i,12.8,30,1]),['1'].map(i=> [i,8.56,30,1]),['5'].map(i=> [i,12.78,30,1]),['7'].map(i=> [i,11.67,30,1]),['a','n'].map(i=> [i,11.92,30,1]),['b'].map(i=> [i,12.48,30,1]),['d','o','p','q'].map(i=> [i,12.5,30,1]),['c'].map(i=> [i,11.66,30,1]),['e'].map(i=> [i,11.84,30,1]),['f'].map(i=> [i,7.95,30,1]),['g'].map(i=> [i,12.61,30,1]),['h'].map(i=> [i,11.86,30,1]),['i'].map(i=> [i,5.47,30,1]),['j'].map(i=> [i,5.7,30,1]),['k'].map(i=> [i,11.28,30,1]),['l'].map(i=> [i,5.02,30,1]),['m'].map(i=> [i,18.23,30,1]),['r'].map(i=> [i,7.78,30,1]),['s'].map(i=> [i,10.78,30,1]),['t'].map(i=> [i,7.56,30,1]),['u'].map(i=> [i,11.95,30,1]),['v'].map(i=> [i,10.28,30,1]),['w'].map(i=> [i,16.09,30,1]),['x'].map(i=> [i,10.86,30,1]),['y'].map(i=> [i,10.58,30,1]),['A'].map(i=> [i,14.02,30,1]),['B'].map(i=> [i,14.44,30,1]),['C'].map(i=> [i,15.52,30,1]),['D'].map(i=> [i,15.05,30,1]),['E','X'].map(i=> [i,13.59,30,1]),['F'].map(i=> [i,12.3,30,1]),['G'].map(i=> [i,15.95,30,1]),['H'].map(i=> [i,15.34,30,1]),['I'].map(i=> [i,5.08,30,1]),['J'].map(i=> [i,11.02,30,1]),['K'].map(i=> [i,14.72,30,1]),['L'].map(i=> [i,12.53,30,1]),['M'].map(i=> [i,18.81,30,1]),['N'].map(i=> [i,15.33,30,1]),['O'].map(i=> [i,16.36,30,1]),['P'].map(i=> [i,13.69,30,1]),['Q'].map(i=> [i,16.34,30,1]),['R'].map(i=> [i,14.42,30,1]),['S'].map(i=> [i,13.47,30,1]),['T'].map(i=> [i,13.2,30,1]),['U'].map(i=> [i,15.22,30,1]),['V'].map(i=> [i,13.63,30,1]),['W'].map(i=> [i,19.81,30,1]),['Y'].map(i=> [i,14.11,30,1]),['Z'].map(i=> [i,13.31,30,1]),[',','.',':',';'].map(i=> [i,5.64,30,1]),['/','\\'].map(i=> [i,10.66,30,1]),['\"'].map(i=> [i,9.11,30,1]),['<','>','+'].map(i=> [i,12.91,30,1]),['=','-'].map(i=> [i,12.89,30,1]),['~','_'].map(i=> [i,10.67,30,1]),['`','!','(',')','{','}','[',']'].map(i=> [i,7.11,30,1]),['@'].map(i=> [i,18.28,30,1]),['%'].map(i=> [i,20.69,30,1]),['^'].map(i=> [i,11.05,30,1]),['&'].map(i=> [i,15.47,30,1]),['*'].map(i=> [i,10.8,30,1]),['|'].map(i=> [i,4.17,30,1]),['\''].map(i=> [i,5.22,30,1]),['☑'].map(i=> [i,17.69,30,1]),['☐'].map(i=> [i,17.7,30,1]),['⊙'].map(i=> [i,18.5,30,1]),['◯'].map(i=> [i,20.33,30,1]),['℃'].map(i=> [i,19.66,30,1])],"FangSong_GB231224":[['我','！','？','《','》','；','，','、','。','（','）','：','【','】'].map(i=> [i,24,33,1]),['“'].map(i=> [i,12.89,33,1]),['”','?'].map(i=> [i,12.91,33,1]),['‘'].map(i=> [i,8.17,33,1]),['’'].map(i=> [i,8.19,33,1]),[' '].map(i=> [i,12,33,1]),['0','3','5','6','9','$'].map(i=> [i,14.42,33,1]),['1'].map(i=> [i,9.64,33,1]),['2','4','8','#'].map(i=> [i,14.41,33,1]),['7','c'].map(i=> [i,13.14,33,1]),['a','n'].map(i=> [i,13.44,33,1]),['b','d','o','p','q'].map(i=> [i,14.08,33,1]),['e'].map(i=> [i,13.33,33,1]),['f'].map(i=> [i,8.97,33,1]),['g'].map(i=> [i,14.2,33,1]),['h'].map(i=> [i,13.36,33,1]),['i'].map(i=> [i,6.16,33,1]),['j'].map(i=> [i,6.44,33,1]),['k'].map(i=> [i,12.7,33,1]),['l'].map(i=> [i,5.66,33,1]),['m'].map(i=> [i,20.53,33,1]),['r'].map(i=> [i,8.78,33,1]),['s'].map(i=> [i,12.14,33,1]),['t'].map(i=> [i,8.53,33,1]),['u'].map(i=> [i,13.45,33,1]),['v'].map(i=> [i,11.58,33,1]),['w'].map(i=> [i,18.14,33,1]),['x'].map(i=> [i,12.23,33,1]),['y'].map(i=> [i,11.92,33,1]),['z'].map(i=> [i,11.7,33,1]),['A'].map(i=> [i,15.78,33,1]),['B'].map(i=> [i,16.27,33,1]),['C'].map(i=> [i,17.48,33,1]),['D'].map(i=> [i,16.95,33,1]),['E'].map(i=> [i,15.31,33,1]),['F'].map(i=> [i,13.86,33,1]),['G'].map(i=> [i,17.97,33,1]),['H'].map(i=> [i,17.3,33,1]),['I'].map(i=> [i,5.7,33,1]),['J'].map(i=> [i,12.42,33,1]),['K'].map(i=> [i,16.58,33,1]),['L'].map(i=> [i,14.13,33,1]),['M'].map(i=> [i,21.19,33,1]),['N'].map(i=> [i,17.27,33,1]),['O'].map(i=> [i,18.42,33,1]),['P'].map(i=> [i,15.42,33,1]),['Q'].map(i=> [i,18.44,33,1]),['R'].map(i=> [i,16.23,33,1]),['S'].map(i=> [i,15.19,33,1]),['T'].map(i=> [i,14.86,33,1]),['U'].map(i=> [i,17.16,33,1]),['V'].map(i=> [i,15.36,33,1]),['W'].map(i=> [i,22.33,33,1]),['X'].map(i=> [i,15.3,33,1]),['Y'].map(i=> [i,15.91,33,1]),['Z'].map(i=> [i,15,33,1]),[',',':',';'].map(i=> [i,6.34,33,1]),['.'].map(i=> [i,6.36,33,1]),['/','~','_','\\'].map(i=> [i,12.02,33,1]),['\"'].map(i=> [i,10.25,33,1]),['<','=','+'].map(i=> [i,14.53,33,1]),['>','-'].map(i=> [i,14.55,33,1]),['`','(','{','['].map(i=> [i,8,33,1]),['!',')','}',']'].map(i=> [i,8.02,33,1]),['@'].map(i=> [i,20.61,33,1]),['%'].map(i=> [i,23.3,33,1]),['^'].map(i=> [i,12.44,33,1]),['&'].map(i=> [i,17.45,33,1]),['*'].map(i=> [i,12.16,33,1]),['|'].map(i=> [i,4.69,33,1]),['\''].map(i=> [i,5.88,33,1]),['☑','☐'].map(i=> [i,19.94,33,1]),['⊙'].map(i=> [i,20.84,33,1]),['⭘'].map(i=> [i,24.02,33,1]),['◯'].map(i=> [i,22.91,33,1]),['℃'].map(i=> [i,22.14,33,1]),['↵'].map(i=> [i,0,33,1])],"FangSong_GB231226.7":[['U'].map(i=> [i,19.08,37,1]),['↵'].map(i=> [i,0,37,1]),['我','》','（'].map(i=> [i,26.7,37,1]),['！','？','《','；','，','、','。','）','：','【','】','⭘'].map(i=> [i,26.72,37,1]),['“'].map(i=> [i,14.34,37,1]),['”','?'].map(i=> [i,14.36,37,1]),['‘','’'].map(i=> [i,9.09,37,1]),[' '].map(i=> [i,13.35,37,1]),['0','2','3','4','6','8','9','#','$'].map(i=> [i,16.03,37,1]),['1'].map(i=> [i,10.73,37,1]),['5'].map(i=> [i,16.05,37,1]),['7','c'].map(i=> [i,14.63,37,1]),['a','n'].map(i=> [i,14.94,37,1]),['b','o','p'].map(i=> [i,15.66,37,1]),['d','q'].map(i=> [i,15.67,37,1]),['e'].map(i=> [i,14.83,37,1]),['f'].map(i=> [i,9.97,37,1]),['g'].map(i=> [i,15.8,37,1]),['h'].map(i=> [i,14.86,37,1]),['i'].map(i=> [i,6.86,37,1]),['j'].map(i=> [i,7.14,37,1]),['k'].map(i=> [i,14.14,37,1]),['l'].map(i=> [i,6.3,37,1]),['m'].map(i=> [i,22.84,37,1]),['r'].map(i=> [i,9.77,37,1]),['s'].map(i=> [i,13.48,37,1]),['t'].map(i=> [i,9.5,37,1]),['u'].map(i=> [i,14.97,37,1]),['v'].map(i=> [i,12.89,37,1]),['w'].map(i=> [i,20.17,37,1]),['x'].map(i=> [i,13.61,37,1]),['y'].map(i=> [i,13.25,37,1]),['z'].map(i=> [i,13.02,37,1]),['A'].map(i=> [i,17.56,37,1]),['B'].map(i=> [i,18.09,37,1]),['C'].map(i=> [i,19.45,37,1]),['D'].map(i=> [i,18.86,37,1]),['E'].map(i=> [i,17.03,37,1]),['F'].map(i=> [i,15.42,37,1]),['G'].map(i=> [i,19.98,37,1]),['H'].map(i=> [i,19.23,37,1]),['I'].map(i=> [i,6.34,37,1]),['J'].map(i=> [i,13.83,37,1]),['K'].map(i=> [i,18.44,37,1]),['L'].map(i=> [i,15.72,37,1]),['M'].map(i=> [i,23.56,37,1]),['N'].map(i=> [i,19.2,37,1]),['O','Q'].map(i=> [i,20.5,37,1]),['P'].map(i=> [i,17.16,37,1]),['R'].map(i=> [i,18.06,37,1]),['S'].map(i=> [i,16.89,37,1]),['T'].map(i=> [i,16.55,37,1]),['V'].map(i=> [i,17.08,37,1]),['W'].map(i=> [i,24.84,37,1]),['X'].map(i=> [i,17.02,37,1]),['Y'].map(i=> [i,17.69,37,1]),['Z'].map(i=> [i,16.69,37,1]),[',','.',':',';'].map(i=> [i,7.06,37,1]),['/','\\'].map(i=> [i,13.36,37,1]),['\"'].map(i=> [i,11.42,37,1]),['<','=','-','+'].map(i=> [i,16.17,37,1]),['>'].map(i=> [i,16.16,37,1]),['~','_'].map(i=> [i,13.38,37,1]),['`','!','(',')','{','}','[',']'].map(i=> [i,8.91,37,1]),['@'].map(i=> [i,22.92,37,1]),['%'].map(i=> [i,25.92,37,1]),['^'].map(i=> [i,13.84,37,1]),['&'].map(i=> [i,19.41,37,1]),['*'].map(i=> [i,13.52,37,1]),['|'].map(i=> [i,5.22,37,1]),['\''].map(i=> [i,6.53,37,1]),['☑'].map(i=> [i,22.19,37,1]),['☐'].map(i=> [i,22.17,37,1]),['⊙'].map(i=> [i,23.19,37,1]),['◯'].map(i=> [i,25.48,37,1]),['℃'].map(i=> [i,24.63,37,1])],"FangSong_GB231229.3":[['↵'].map(i=> [i,0,41,1]),['我','！','？','《','》','，','、','。','（','：','【','】','⭘'].map(i=> [i,29.31,41,1]),['；','）'].map(i=> [i,29.33,41,1]),['“','”','?'].map(i=> [i,15.75,41,1]),['‘'].map(i=> [i,9.98,41,1]),['’'].map(i=> [i,9.97,41,1]),[' '].map(i=> [i,14.65,41,1]),['0'].map(i=> [i,17.61,41,1]),['1'].map(i=> [i,11.77,41,1]),['2','3','4','5','6','8','9','#','$'].map(i=> [i,17.59,41,1]),['7','c'].map(i=> [i,16.05,41,1]),['a','n'].map(i=> [i,16.39,41,1]),['b','d','o','p'].map(i=> [i,17.19,41,1]),['e'].map(i=> [i,16.28,41,1]),['f'].map(i=> [i,10.94,41,1]),['g'].map(i=> [i,17.33,41,1]),['h'].map(i=> [i,16.31,41,1]),['i'].map(i=> [i,7.52,41,1]),['j'].map(i=> [i,7.84,41,1]),['k'].map(i=> [i,15.52,41,1]),['l'].map(i=> [i,6.89,41,1]),['m'].map(i=> [i,25.08,41,1]),['q'].map(i=> [i,17.17,41,1]),['r'].map(i=> [i,10.72,41,1]),['s'].map(i=> [i,14.81,41,1]),['t'].map(i=> [i,10.42,41,1]),['u'].map(i=> [i,16.42,41,1]),['v'].map(i=> [i,14.14,41,1]),['w'].map(i=> [i,22.13,41,1]),['x'].map(i=> [i,14.94,41,1]),['y'].map(i=> [i,14.55,41,1]),['z'].map(i=> [i,14.28,41,1]),['A'].map(i=> [i,19.27,41,1]),['B'].map(i=> [i,19.86,41,1]),['C'].map(i=> [i,21.34,41,1]),['D'].map(i=> [i,20.7,41,1]),['E','X'].map(i=> [i,18.67,41,1]),['F'].map(i=> [i,16.92,41,1]),['G'].map(i=> [i,21.94,41,1]),['H'].map(i=> [i,21.11,41,1]),['I'].map(i=> [i,6.95,41,1]),['J'].map(i=> [i,15.17,41,1]),['K'].map(i=> [i,20.23,41,1]),['L'].map(i=> [i,17.23,41,1]),['M'].map(i=> [i,25.86,41,1]),['N'].map(i=> [i,21.09,41,1]),['O','Q'].map(i=> [i,22.48,41,1]),['P'].map(i=> [i,18.83,41,1]),['R'].map(i=> [i,19.83,41,1]),['S'].map(i=> [i,18.53,41,1]),['T'].map(i=> [i,18.14,41,1]),['U'].map(i=> [i,20.94,41,1]),['V'].map(i=> [i,18.75,41,1]),['W'].map(i=> [i,27.27,41,1]),['Y'].map(i=> [i,19.41,41,1]),['Z'].map(i=> [i,18.31,41,1]),[',','.',':',';'].map(i=> [i,7.75,41,1]),['/','_','\\'].map(i=> [i,14.66,41,1]),['\"'].map(i=> [i,12.53,41,1]),['<','=','+'].map(i=> [i,17.73,41,1]),['>','-'].map(i=> [i,17.75,41,1]),['~'].map(i=> [i,14.67,41,1]),['`','!','(','}','['].map(i=> [i,9.77,41,1]),['@'].map(i=> [i,25.16,41,1]),['%'].map(i=> [i,28.44,41,1]),['^'].map(i=> [i,15.19,41,1]),['&'].map(i=> [i,21.3,41,1]),['*'].map(i=> [i,14.84,41,1]),[')','{',']'].map(i=> [i,9.78,41,1]),['|'].map(i=> [i,5.72,41,1]),['\''].map(i=> [i,7.17,41,1]),['☑'].map(i=> [i,24.33,41,1]),['☐'].map(i=> [i,24.34,41,1]),['⊙'].map(i=> [i,25.45,41,1]),['◯'].map(i=> [i,27.95,41,1]),['℃'].map(i=> [i,27.03,41,1])],"FangSong_GB231232":[['我','！','？','《','》','；','，','、','。','（','）','：','【','】'].map(i=> [i,32,45,1]),['“'].map(i=> [i,17.19,45,1]),['”','?'].map(i=> [i,17.2,45,1]),['‘'].map(i=> [i,10.89,45,1]),['’'].map(i=> [i,10.91,45,1]),[' '].map(i=> [i,16,45,1]),['0','2','3','4','5','9','#','$'].map(i=> [i,19.22,45,1]),['1'].map(i=> [i,12.84,45,1]),['6','8'].map(i=> [i,19.2,45,1]),['7'].map(i=> [i,17.53,45,1]),['a','n'].map(i=> [i,17.91,45,1]),['b','d','o','p','q'].map(i=> [i,18.77,45,1]),['c'].map(i=> [i,17.52,45,1]),['e'].map(i=> [i,17.78,45,1]),['f'].map(i=> [i,11.95,45,1]),['g'].map(i=> [i,18.92,45,1]),['h'].map(i=> [i,17.81,45,1]),['i'].map(i=> [i,8.2,45,1]),['j'].map(i=> [i,8.56,45,1]),['k'].map(i=> [i,16.95,45,1]),['l'].map(i=> [i,7.53,45,1]),['m'].map(i=> [i,27.38,45,1]),['r'].map(i=> [i,11.7,45,1]),['s'].map(i=> [i,16.17,45,1]),['t'].map(i=> [i,11.38,45,1]),['u'].map(i=> [i,17.94,45,1]),['v'].map(i=> [i,15.44,45,1]),['w'].map(i=> [i,24.17,45,1]),['x'].map(i=> [i,16.31,45,1]),['y'].map(i=> [i,15.88,45,1]),['z'].map(i=> [i,15.61,45,1]),['A'].map(i=> [i,21.03,45,1]),['B'].map(i=> [i,21.69,45,1]),['C'].map(i=> [i,23.31,45,1]),['D'].map(i=> [i,22.61,45,1]),['E'].map(i=> [i,20.39,45,1]),['F'].map(i=> [i,18.48,45,1]),['G'].map(i=> [i,23.95,45,1]),['H'].map(i=> [i,23.05,45,1]),['I'].map(i=> [i,7.61,45,1]),['J'].map(i=> [i,16.56,45,1]),['K'].map(i=> [i,22.09,45,1]),['L'].map(i=> [i,18.83,45,1]),['M'].map(i=> [i,28.23,45,1]),['N'].map(i=> [i,23.03,45,1]),['O','Q'].map(i=> [i,24.56,45,1]),['P'].map(i=> [i,20.55,45,1]),['R'].map(i=> [i,21.66,45,1]),['S'].map(i=> [i,20.23,45,1]),['T'].map(i=> [i,19.83,45,1]),['U'].map(i=> [i,22.86,45,1]),['V'].map(i=> [i,20.47,45,1]),['W'].map(i=> [i,29.77,45,1]),['X'].map(i=> [i,20.41,45,1]),['Y'].map(i=> [i,21.2,45,1]),['Z'].map(i=> [i,19.98,45,1]),[','].map(i=> [i,8.45,45,1]),['.',':',';'].map(i=> [i,8.47,45,1]),['/','~','_','\\'].map(i=> [i,16.02,45,1]),['\"'].map(i=> [i,13.67,45,1]),['<','>','=','-','+'].map(i=> [i,19.38,45,1]),['`','!','(',')','{','}','[',']'].map(i=> [i,10.67,45,1]),['@'].map(i=> [i,27.47,45,1]),['%'].map(i=> [i,31.05,45,1]),['^'].map(i=> [i,16.59,45,1]),['&'].map(i=> [i,23.25,45,1]),['*'].map(i=> [i,16.2,45,1]),['|'].map(i=> [i,6.25,45,1]),['\''].map(i=> [i,7.83,45,1]),['☑','☐'].map(i=> [i,26.58,45,1]),['⊙'].map(i=> [i,27.8,45,1]),['⭘'].map(i=> [i,32.02,45,1]),['◯'].map(i=> [i,30.53,45,1]),['℃'].map(i=> [i,29.52,45,1]),['↵'].map(i=> [i,0,45,1])],"FangSong_GB231234.7":[['我','》','（','）','⭘'].map(i=> [i,34.7,49,1]),['！','？','《','；','，','、','。','：','【','】'].map(i=> [i,34.72,49,1]),['“','?'].map(i=> [i,18.64,49,1]),['”'].map(i=> [i,18.66,49,1]),['‘','’'].map(i=> [i,11.81,49,1]),[' '].map(i=> [i,17.35,49,1]),['0','2','4','6','8','#'].map(i=> [i,20.84,49,1]),['1'].map(i=> [i,13.92,49,1]),['3','5','9','$'].map(i=> [i,20.83,49,1]),['7'].map(i=> [i,18.98,49,1]),['a','n'].map(i=> [i,19.42,49,1]),['b','d','o','p'].map(i=> [i,20.34,49,1]),['c'].map(i=> [i,19,49,1]),['e'].map(i=> [i,19.28,49,1]),['f'].map(i=> [i,12.95,49,1]),['g'].map(i=> [i,20.52,49,1]),['h'].map(i=> [i,19.31,49,1]),['i'].map(i=> [i,8.91,49,1]),['j'].map(i=> [i,9.28,49,1]),['k'].map(i=> [i,18.36,49,1]),['l'].map(i=> [i,8.17,49,1]),['m'].map(i=> [i,29.69,49,1]),['q'].map(i=> [i,20.36,49,1]),['r'].map(i=> [i,12.67,49,1]),['s'].map(i=> [i,17.55,49,1]),['t'].map(i=> [i,12.33,49,1]),['u'].map(i=> [i,19.45,49,1]),['v'].map(i=> [i,16.73,49,1]),['w'].map(i=> [i,26.22,49,1]),['x'].map(i=> [i,17.67,49,1]),['y'].map(i=> [i,17.23,49,1]),['z'].map(i=> [i,16.91,49,1]),['A'].map(i=> [i,22.81,49,1]),['B'].map(i=> [i,23.52,49,1]),['C'].map(i=> [i,25.28,49,1]),['D'].map(i=> [i,24.52,49,1]),['E'].map(i=> [i,22.11,49,1]),['F'].map(i=> [i,20.05,49,1]),['G'].map(i=> [i,25.97,49,1]),['H'].map(i=> [i,25,49,1]),['I'].map(i=> [i,8.23,49,1]),['J'].map(i=> [i,17.95,49,1]),['K'].map(i=> [i,23.97,49,1]),['L'].map(i=> [i,20.41,49,1]),['M'].map(i=> [i,30.63,49,1]),['N'].map(i=> [i,24.97,49,1]),['O','Q'].map(i=> [i,26.63,49,1]),['P'].map(i=> [i,22.3,49,1]),['R'].map(i=> [i,23.48,49,1]),['S'].map(i=> [i,21.94,49,1]),['T'].map(i=> [i,21.5,49,1]),['U'].map(i=> [i,24.8,49,1]),['V'].map(i=> [i,22.19,49,1]),['W'].map(i=> [i,32.28,49,1]),['X'].map(i=> [i,22.13,49,1]),['Y'].map(i=> [i,22.98,49,1]),['Z'].map(i=> [i,21.67,49,1]),[',','.'].map(i=> [i,9.17,49,1]),['/','~'].map(i=> [i,17.38,49,1]),['\"'].map(i=> [i,14.83,49,1]),[':',';'].map(i=> [i,9.19,49,1]),['<','=','-'].map(i=> [i,21,49,1]),['>','+'].map(i=> [i,21.02,49,1]),['`','(','{','['].map(i=> [i,11.56,49,1]),['!',')','}',']'].map(i=> [i,11.58,49,1]),['@'].map(i=> [i,29.78,49,1]),['%'].map(i=> [i,33.67,49,1]),['^'].map(i=> [i,18,49,1]),['&'].map(i=> [i,25.2,49,1]),['*'].map(i=> [i,17.58,49,1]),['_','\\'].map(i=> [i,17.36,49,1]),['|'].map(i=> [i,6.78,49,1]),['\''].map(i=> [i,8.48,49,1]),['☑'].map(i=> [i,28.83,49,1]),['☐'].map(i=> [i,28.81,49,1]),['⊙'].map(i=> [i,30.14,49,1]),['◯'].map(i=> [i,33.09,49,1]),['℃'].map(i=> [i,32.02,49,1]),['↵'].map(i=> [i,0,49,1])],"FangSong_GB231237.3":[['@'].map(i=> [i,32.02,53,1]),['↵'].map(i=> [i,0,53,1]),['我','！','？','《','》','，','、','。','（','：','【','】','⭘'].map(i=> [i,37.31,53,1]),['；','）'].map(i=> [i,37.33,53,1]),['“','”','?'].map(i=> [i,20.05,53,1]),['‘'].map(i=> [i,12.7,53,1]),['’'].map(i=> [i,12.69,53,1]),[' '].map(i=> [i,18.65,53,1]),['0','3','4','6','8','9','$'].map(i=> [i,22.39,53,1]),['1'].map(i=> [i,14.97,53,1]),['2','5','#'].map(i=> [i,22.41,53,1]),['7'].map(i=> [i,20.42,53,1]),['a','n'].map(i=> [i,20.88,53,1]),['b','d','o','q'].map(i=> [i,21.88,53,1]),['c'].map(i=> [i,20.41,53,1]),['e'].map(i=> [i,20.72,53,1]),['f'].map(i=> [i,13.94,53,1]),['g'].map(i=> [i,22.06,53,1]),['h'].map(i=> [i,20.75,53,1]),['i'].map(i=> [i,9.56,53,1]),['j'].map(i=> [i,9.97,53,1]),['k'].map(i=> [i,19.75,53,1]),['l'].map(i=> [i,8.78,53,1]),['m'].map(i=> [i,31.91,53,1]),['p'].map(i=> [i,21.86,53,1]),['r'].map(i=> [i,13.64,53,1]),['s'].map(i=> [i,18.84,53,1]),['t'].map(i=> [i,13.27,53,1]),['u'].map(i=> [i,20.91,53,1]),['v'].map(i=> [i,17.98,53,1]),['w'].map(i=> [i,28.17,53,1]),['x'].map(i=> [i,19.02,53,1]),['y'].map(i=> [i,18.52,53,1]),['z'].map(i=> [i,18.17,53,1]),['A'].map(i=> [i,24.53,53,1]),['B'].map(i=> [i,25.27,53,1]),['C'].map(i=> [i,27.17,53,1]),['D'].map(i=> [i,26.34,53,1]),['E'].map(i=> [i,23.78,53,1]),['F'].map(i=> [i,21.53,53,1]),['G'].map(i=> [i,27.92,53,1]),['H'].map(i=> [i,26.86,53,1]),['I'].map(i=> [i,8.86,53,1]),['J'].map(i=> [i,19.3,53,1]),['K'].map(i=> [i,25.77,53,1]),['L'].map(i=> [i,21.94,53,1]),['M'].map(i=> [i,32.92,53,1]),['N'].map(i=> [i,26.83,53,1]),['O','Q'].map(i=> [i,28.63,53,1]),['P'].map(i=> [i,23.97,53,1]),['R'].map(i=> [i,25.22,53,1]),['S'].map(i=> [i,23.59,53,1]),['T'].map(i=> [i,23.11,53,1]),['U'].map(i=> [i,26.64,53,1]),['V'].map(i=> [i,23.86,53,1]),['W'].map(i=> [i,34.7,53,1]),['X'].map(i=> [i,23.77,53,1]),['Y'].map(i=> [i,24.72,53,1]),['Z'].map(i=> [i,23.28,53,1]),[','].map(i=> [i,9.88,53,1]),['.',':',';'].map(i=> [i,9.86,53,1]),['/','~'].map(i=> [i,18.66,53,1]),['\"'].map(i=> [i,15.95,53,1]),['<','>','-','+'].map(i=> [i,22.58,53,1]),['='].map(i=> [i,22.59,53,1]),['`','!','(',')','{','}','[',']'].map(i=> [i,12.44,53,1]),['%'].map(i=> [i,36.2,53,1]),['^'].map(i=> [i,19.33,53,1]),['&'].map(i=> [i,27.09,53,1]),['*'].map(i=> [i,18.89,53,1]),['_','\\'].map(i=> [i,18.67,53,1]),['|'].map(i=> [i,7.28,53,1]),['\''].map(i=> [i,9.13,53,1]),['☑'].map(i=> [i,30.97,53,1]),['☐'].map(i=> [i,30.98,53,1]),['⊙'].map(i=> [i,32.39,53,1]),['◯'].map(i=> [i,35.59,53,1]),['℃'].map(i=> [i,34.41,53,1])],"FangSong_GB231248":[['我','！','？','《','》','；','，','、','。','（','）','：','【','】'].map(i=> [i,48,67,1]),['“'].map(i=> [i,25.78,67,1]),['”','?'].map(i=> [i,25.8,67,1]),['‘'].map(i=> [i,16.33,67,1]),['’'].map(i=> [i,16.34,67,1]),[' '].map(i=> [i,24,67,1]),['0','2','3','4','6','8','#','$'].map(i=> [i,28.81,67,1]),['1'].map(i=> [i,19.27,67,1]),['5','9'].map(i=> [i,28.83,67,1]),['7','c'].map(i=> [i,26.27,67,1]),['a','n'].map(i=> [i,26.84,67,1]),['b','d','p','q'].map(i=> [i,28.14,67,1]),['e'].map(i=> [i,26.66,67,1]),['f'].map(i=> [i,17.92,67,1]),['g'].map(i=> [i,28.39,67,1]),['h'].map(i=> [i,26.7,67,1]),['i'].map(i=> [i,12.3,67,1]),['j'].map(i=> [i,12.84,67,1]),['k'].map(i=> [i,25.41,67,1]),['l'].map(i=> [i,11.3,67,1]),['m'].map(i=> [i,41.05,67,1]),['o'].map(i=> [i,28.16,67,1]),['r'].map(i=> [i,17.53,67,1]),['s'].map(i=> [i,24.27,67,1]),['t'].map(i=> [i,17.05,67,1]),['u'].map(i=> [i,26.89,67,1]),['v'].map(i=> [i,23.16,67,1]),['w'].map(i=> [i,36.25,67,1]),['x'].map(i=> [i,24.45,67,1]),['y'].map(i=> [i,23.83,67,1]),['z'].map(i=> [i,23.39,67,1]),['A'].map(i=> [i,31.55,67,1]),['B'].map(i=> [i,32.52,67,1]),['C'].map(i=> [i,34.95,67,1]),['D'].map(i=> [i,33.91,67,1]),['E','X'].map(i=> [i,30.59,67,1]),['F'].map(i=> [i,27.72,67,1]),['G'].map(i=> [i,35.91,67,1]),['H'].map(i=> [i,34.58,67,1]),['I'].map(i=> [i,11.39,67,1]),['J'].map(i=> [i,24.84,67,1]),['K'].map(i=> [i,33.13,67,1]),['L'].map(i=> [i,28.25,67,1]),['M'].map(i=> [i,42.34,67,1]),['N'].map(i=> [i,34.53,67,1]),['O'].map(i=> [i,36.83,67,1]),['P'].map(i=> [i,30.83,67,1]),['Q'].map(i=> [i,36.84,67,1]),['R'].map(i=> [i,32.45,67,1]),['S'].map(i=> [i,30.36,67,1]),['T'].map(i=> [i,29.72,67,1]),['U'].map(i=> [i,34.3,67,1]),['V'].map(i=> [i,30.69,67,1]),['W'].map(i=> [i,44.66,67,1]),['Y'].map(i=> [i,31.78,67,1]),['Z'].map(i=> [i,29.97,67,1]),[',','.',':',';'].map(i=> [i,12.69,67,1]),['/','~','_','\\'].map(i=> [i,24.02,67,1]),['\"'].map(i=> [i,20.52,67,1]),['<','=','+'].map(i=> [i,29.05,67,1]),['>','-'].map(i=> [i,29.06,67,1]),['`','!','(',')','{','}','[',']'].map(i=> [i,16,67,1]),['@'].map(i=> [i,41.2,67,1]),['%'].map(i=> [i,46.58,67,1]),['^'].map(i=> [i,24.89,67,1]),['&'].map(i=> [i,34.86,67,1]),['*'].map(i=> [i,24.3,67,1]),['|'].map(i=> [i,9.38,67,1]),['\''].map(i=> [i,11.73,67,1]),['☑','☐'].map(i=> [i,39.86,67,1]),['⊙'].map(i=> [i,41.67,67,1]),['⭘'].map(i=> [i,48.02,67,1]),['◯'].map(i=> [i,45.8,67,1]),['℃'].map(i=> [i,44.27,67,1]),['↵'].map(i=> [i,0,67,1])],"FangSong_GB231258.7":[['我','》','；','）','⭘'].map(i=> [i,58.7,82,1]),['！','？','《','，','、','。','（','：','【','】'].map(i=> [i,58.72,82,1]),['“','?'].map(i=> [i,31.53,82,1]),['”'].map(i=> [i,31.55,82,1]),['‘','’'].map(i=> [i,19.97,82,1]),[' '].map(i=> [i,29.35,82,1]),['0','3','4','5','6','8','9','#','$'].map(i=> [i,35.23,82,1]),['1'].map(i=> [i,23.55,82,1]),['2'].map(i=> [i,35.25,82,1]),['7'].map(i=> [i,32.13,82,1]),['a','n'].map(i=> [i,32.83,82,1]),['b','o','q'].map(i=> [i,34.41,82,1]),['d','p'].map(i=> [i,34.42,82,1]),['c'].map(i=> [i,32.11,82,1]),['e'].map(i=> [i,32.59,82,1]),['f'].map(i=> [i,21.91,82,1]),['g'].map(i=> [i,34.7,82,1]),['h'].map(i=> [i,32.66,82,1]),['i'].map(i=> [i,15.05,82,1]),['j'].map(i=> [i,15.69,82,1]),['k'].map(i=> [i,31.06,82,1]),['l'].map(i=> [i,13.81,82,1]),['m'].map(i=> [i,50.2,82,1]),['r'].map(i=> [i,21.45,82,1]),['s'].map(i=> [i,29.66,82,1]),['t'].map(i=> [i,20.86,82,1]),['u'].map(i=> [i,32.88,82,1]),['v'].map(i=> [i,28.31,82,1]),['w'].map(i=> [i,44.33,82,1]),['x'].map(i=> [i,29.91,82,1]),['y'].map(i=> [i,29.13,82,1]),['z'].map(i=> [i,28.61,82,1]),['A'].map(i=> [i,38.58,82,1]),['B'].map(i=> [i,39.75,82,1]),['C'].map(i=> [i,42.75,82,1]),['D'].map(i=> [i,41.45,82,1]),['E'].map(i=> [i,37.42,82,1]),['F'].map(i=> [i,33.88,82,1]),['G'].map(i=> [i,43.92,82,1]),['H'].map(i=> [i,42.28,82,1]),['I'].map(i=> [i,13.94,82,1]),['J'].map(i=> [i,30.36,82,1]),['K'].map(i=> [i,40.52,82,1]),['L'].map(i=> [i,34.53,82,1]),['M'].map(i=> [i,51.78,82,1]),['N'].map(i=> [i,42.22,82,1]),['O','Q'].map(i=> [i,45.03,82,1]),['P'].map(i=> [i,37.7,82,1]),['R'].map(i=> [i,39.7,82,1]),['S'].map(i=> [i,37.11,82,1]),['T'].map(i=> [i,36.36,82,1]),['U'].map(i=> [i,41.92,82,1]),['V'].map(i=> [i,37.53,82,1]),['W'].map(i=> [i,54.61,82,1]),['X'].map(i=> [i,37.41,82,1]),['Y'].map(i=> [i,38.88,82,1]),['Z'].map(i=> [i,36.64,82,1]),[',',':'].map(i=> [i,15.52,82,1]),['.',';'].map(i=> [i,15.5,82,1]),['/','_','\\'].map(i=> [i,29.38,82,1]),['\"'].map(i=> [i,25.08,82,1]),['<','>','=','-','+'].map(i=> [i,35.53,82,1]),['~'].map(i=> [i,29.36,82,1]),['`','!','(',')','{','}','[',']'].map(i=> [i,19.56,82,1]),['@'].map(i=> [i,50.39,82,1]),['%'].map(i=> [i,56.95,82,1]),['^'].map(i=> [i,30.42,82,1]),['&'].map(i=> [i,42.63,82,1]),['*'].map(i=> [i,29.72,82,1]),['|'].map(i=> [i,11.45,82,1]),['\''].map(i=> [i,14.34,82,1]),['☑'].map(i=> [i,48.73,82,1]),['☐'].map(i=> [i,48.75,82,1]),['⊙'].map(i=> [i,50.97,82,1]),['◯'].map(i=> [i,56,82,1]),['℃'].map(i=> [i,54.14,82,1]),['↵'].map(i=> [i,0,82,1])],"STKaiti10.7":[['我','》','（'].map(i=> [i,10.7,10,1]),['！','？','《','；','，','、','。','）','：','【','】','⊙','⭘'].map(i=> [i,10.72,10,1]),[' '].map(i=> [i,5.35,10,1]),['0','6'].map(i=> [i,5.05,10,1]),['1','2','3','4','5','7','8','9'].map(i=> [i,5.03,10,1]),['b','q'].map(i=> [i,5.59,10,1]),['z'].map(i=> [i,4.64,10,1]),['x'].map(i=> [i,5.11,10,1]),['h'].map(i=> [i,5.7,10,1]),['l'].map(i=> [i,2.91,10,1]),['a','o'].map(i=> [i,5.48,10,1]),['p'].map(i=> [i,5.61,10,1]),['?'].map(i=> [i,3.92,10,1]),['>','=','~','+'].map(i=> [i,7.16,10,1]),['e','*'].map(i=> [i,4.59,10,1]),['T'].map(i=> [i,6.59,10,1]),['G'].map(i=> [i,8.27,10,1]),['t'].map(i=> [i,3.81,10,1]),['J','`'].map(i=> [i,3.58,10,1]),['{','}'].map(i=> [i,5.14,10,1]),['R'].map(i=> [i,7.45,10,1]),[',','.',':','!',';'].map(i=> [i,2.36,10,1]),['^','\\'].map(i=> [i,5.36,10,1]),['\"'].map(i=> [i,4.36,10,1]),['w','<','#'].map(i=> [i,7.14,10,1]),['@'].map(i=> [i,9.83,10,1]),['$'].map(i=> [i,4.81,10,1]),['/','|'].map(i=> [i,5.38,10,1]),['&'].map(i=> [i,7.81,10,1]),['-'].map(i=> [i,3.36,10,1]),['\''].map(i=> [i,1.91,10,1]),['☑'].map(i=> [i,8.91,10,1]),['☐'].map(i=> [i,8.89,10,1]),['◯'].map(i=> [i,10.22,10,1]),['↵'].map(i=> [i,0,10,1]),['“'].map(i=> [i,4.33,10,1]),['”'].map(i=> [i,4.34,10,1]),['‘','’'].map(i=> [i,2.67,10,1]),['d'].map(i=> [i,5.53,10,1]),['c'].map(i=> [i,4.53,10,1]),['f'].map(i=> [i,3.63,10,1]),['g'].map(i=> [i,5.16,10,1]),['i'].map(i=> [i,3,10,1]),['j'].map(i=> [i,2.48,10,1]),['k'].map(i=> [i,5.58,10,1]),['m'].map(i=> [i,8.41,10,1]),['n'].map(i=> [i,5.69,10,1]),['r'].map(i=> [i,4.17,10,1]),['s'].map(i=> [i,4.13,10,1]),['u'].map(i=> [i,5.67,10,1]),['v'].map(i=> [i,5.06,10,1]),['y'].map(i=> [i,4.94,10,1]),['A'].map(i=> [i,7.48,10,1]),['B'].map(i=> [i,6.89,10,1]),['C'].map(i=> [i,6.91,10,1]),['D'].map(i=> [i,8.28,10,1]),['E'].map(i=> [i,7.11,10,1]),['F'].map(i=> [i,6.13,10,1]),['H'].map(i=> [i,8.66,10,1]),['I'].map(i=> [i,3.8,10,1]),['K'].map(i=> [i,7.97,10,1]),['L'].map(i=> [i,6.31,10,1]),['M'].map(i=> [i,9.97,10,1]),['N'].map(i=> [i,8.33,10,1]),['O'].map(i=> [i,8.31,10,1]),['P'].map(i=> [i,6.16,10,1]),['Q'].map(i=> [i,8.22,10,1]),['S'].map(i=> [i,5.19,10,1]),['U'].map(i=> [i,7.66,10,1]),['V'].map(i=> [i,7.36,10,1]),['W'].map(i=> [i,9.59,10,1]),['X'].map(i=> [i,7.55,10,1]),['Y'].map(i=> [i,7.2,10,1]),['Z'].map(i=> [i,7.08,10,1]),['%'].map(i=> [i,8.83,10,1]),['('].map(i=> [i,3.64,10,1]),[')'].map(i=> [i,3.53,10,1]),['_'].map(i=> [i,5.42,10,1]),['['].map(i=> [i,3.45,10,1]),[']'].map(i=> [i,3.47,10,1]),['℃'].map(i=> [i,9.06,10,1])],"STKaiti12":[['C'].map(i=> [i,7.73,12,1]),['↵'].map(i=> [i,0,12,1]),['*'].map(i=> [i,5.14,12,1]),['Q'].map(i=> [i,9.22,12,1]),['我','！','？','《','》','；','，','、','。','（','）','：','【','】'].map(i=> [i,12,12,1]),[' '].map(i=> [i,6,12,1]),['0','1','3','4','5','6','7','9'].map(i=> [i,5.64,12,1]),['2','8'].map(i=> [i,5.66,12,1]),['z'].map(i=> [i,5.2,12,1]),['h','n'].map(i=> [i,6.39,12,1]),['a','o'].map(i=> [i,6.14,12,1]),['p'].map(i=> [i,6.3,12,1]),['?'].map(i=> [i,4.39,12,1]),['w','<','=','~','+'].map(i=> [i,8.02,12,1]),['y'].map(i=> [i,5.52,12,1]),['G'].map(i=> [i,9.27,12,1]),['J','`'].map(i=> [i,4.02,12,1]),['F','P'].map(i=> [i,6.89,12,1]),['R'].map(i=> [i,8.36,12,1]),['.',':','!'].map(i=> [i,2.64,12,1]),['/','^','\\','|'].map(i=> [i,6.02,12,1]),['\"'].map(i=> [i,4.89,12,1]),['>','#'].map(i=> [i,8.03,12,1]),['@'].map(i=> [i,11.02,12,1]),['$'].map(i=> [i,5.39,12,1]),['%'].map(i=> [i,9.89,12,1]),['&'].map(i=> [i,8.77,12,1]),[',',';'].map(i=> [i,2.66,12,1]),['-'].map(i=> [i,3.77,12,1]),['{','}'].map(i=> [i,5.77,12,1]),['l'].map(i=> [i,3.27,12,1]),['\''].map(i=> [i,2.14,12,1]),['☐'].map(i=> [i,9.98,12,1]),['☑'].map(i=> [i,9.97,12,1]),['⊙','⭘'].map(i=> [i,12.02,12,1]),['◯'].map(i=> [i,11.45,12,1]),['“','”'].map(i=> [i,4.86,12,1]),['‘','’'].map(i=> [i,3,12,1]),['b','k'].map(i=> [i,6.25,12,1]),['d'].map(i=> [i,6.2,12,1]),['c'].map(i=> [i,5.08,12,1]),['e'].map(i=> [i,5.16,12,1]),['f'].map(i=> [i,4.06,12,1]),['g'].map(i=> [i,5.78,12,1]),['i'].map(i=> [i,3.36,12,1]),['j'].map(i=> [i,2.8,12,1]),['m'].map(i=> [i,9.41,12,1]),['q'].map(i=> [i,6.27,12,1]),['r'].map(i=> [i,4.67,12,1]),['s'].map(i=> [i,4.63,12,1]),['t'].map(i=> [i,4.28,12,1]),['u'].map(i=> [i,6.36,12,1]),['v'].map(i=> [i,5.67,12,1]),['x'].map(i=> [i,5.73,12,1]),['A'].map(i=> [i,8.41,12,1]),['B'].map(i=> [i,7.72,12,1]),['D'].map(i=> [i,9.3,12,1]),['E'].map(i=> [i,7.95,12,1]),['H'].map(i=> [i,9.7,12,1]),['I'].map(i=> [i,4.25,12,1]),['K'].map(i=> [i,8.94,12,1]),['L'].map(i=> [i,7.06,12,1]),['M'].map(i=> [i,11.19,12,1]),['N','O'].map(i=> [i,9.33,12,1]),['S'].map(i=> [i,5.83,12,1]),['T'].map(i=> [i,7.39,12,1]),['U'].map(i=> [i,8.59,12,1]),['V'].map(i=> [i,8.23,12,1]),['W'].map(i=> [i,10.77,12,1]),['X'].map(i=> [i,8.47,12,1]),['Y'].map(i=> [i,8.08,12,1]),['Z'].map(i=> [i,7.92,12,1]),['('].map(i=> [i,4.08,12,1]),[')'].map(i=> [i,3.97,12,1]),['_'].map(i=> [i,6.06,12,1]),['['].map(i=> [i,3.88,12,1]),[']'].map(i=> [i,3.89,12,1]),['℃'].map(i=> [i,10.17,12,1])],"STKaiti13.3":[['我','！','？','《','》','，','、','。','（','：','【','】','⊙'].map(i=> [i,13.31,13,1]),['；','）','⭘'].map(i=> [i,13.33,13,1]),[' '].map(i=> [i,6.65,13,1]),['0','1','2','3','5','6','7','8'].map(i=> [i,6.25,13,1]),['4','9'].map(i=> [i,6.27,13,1]),['p'].map(i=> [i,6.97,13,1]),['z'].map(i=> [i,5.77,13,1]),['h'].map(i=> [i,7.09,13,1]),['a','o'].map(i=> [i,6.81,13,1]),['['].map(i=> [i,4.28,13,1]),['<','=','~','#'].map(i=> [i,8.89,13,1]),['y'].map(i=> [i,6.11,13,1]),['*'].map(i=> [i,5.69,13,1]),['I'].map(i=> [i,4.73,13,1]),['J','`'].map(i=> [i,4.44,13,1]),['G'].map(i=> [i,10.27,13,1]),['{','}'].map(i=> [i,6.39,13,1]),[',',';'].map(i=> [i,2.92,13,1]),['.',':','!'].map(i=> [i,2.94,13,1]),['/','^','|'].map(i=> [i,6.66,13,1]),['?'].map(i=> [i,4.88,13,1]),['w','>','+'].map(i=> [i,8.88,13,1]),['\\'].map(i=> [i,6.67,13,1]),['l'].map(i=> [i,3.63,13,1]),['\''].map(i=> [i,2.38,13,1]),['☐'].map(i=> [i,11.05,13,1]),['☑'].map(i=> [i,11.06,13,1]),['↵'].map(i=> [i,0,13,1]),['“','”'].map(i=> [i,5.39,13,1]),['‘'].map(i=> [i,3.31,13,1]),['’'].map(i=> [i,3.33,13,1]),['b','q'].map(i=> [i,6.94,13,1]),['d'].map(i=> [i,6.86,13,1]),['c'].map(i=> [i,5.63,13,1]),['e'].map(i=> [i,5.72,13,1]),['f'].map(i=> [i,4.5,13,1]),['g'].map(i=> [i,6.41,13,1]),['i'].map(i=> [i,3.7,13,1]),['j'].map(i=> [i,3.11,13,1]),['k'].map(i=> [i,6.91,13,1]),['m'].map(i=> [i,10.44,13,1]),['n'].map(i=> [i,7.08,13,1]),['r'].map(i=> [i,5.19,13,1]),['s'].map(i=> [i,5.11,13,1]),['t'].map(i=> [i,4.75,13,1]),['u'].map(i=> [i,7.05,13,1]),['v'].map(i=> [i,6.28,13,1]),['x'].map(i=> [i,6.36,13,1]),['A'].map(i=> [i,9.31,13,1]),['B'].map(i=> [i,8.56,13,1]),['C'].map(i=> [i,8.58,13,1]),['D'].map(i=> [i,10.3,13,1]),['E'].map(i=> [i,8.81,13,1]),['F','P'].map(i=> [i,7.63,13,1]),['H'].map(i=> [i,10.75,13,1]),['K'].map(i=> [i,9.89,13,1]),['L'].map(i=> [i,7.84,13,1]),['M'].map(i=> [i,12.39,13,1]),['N','O'].map(i=> [i,10.34,13,1]),['Q'].map(i=> [i,10.22,13,1]),['R'].map(i=> [i,9.27,13,1]),['S'].map(i=> [i,6.45,13,1]),['T'].map(i=> [i,8.19,13,1]),['U'].map(i=> [i,9.52,13,1]),['V'].map(i=> [i,9.14,13,1]),['W'].map(i=> [i,11.92,13,1]),['X'].map(i=> [i,9.38,13,1]),['Y'].map(i=> [i,8.95,13,1]),['Z'].map(i=> [i,8.8,13,1]),['\"'].map(i=> [i,5.41,13,1]),['@'].map(i=> [i,12.2,13,1]),['$'].map(i=> [i,5.97,13,1]),['%'].map(i=> [i,10.97,13,1]),['&'].map(i=> [i,9.72,13,1]),['('].map(i=> [i,4.53,13,1]),[')'].map(i=> [i,4.39,13,1]),['_'].map(i=> [i,6.73,13,1]),['-'].map(i=> [i,4.19,13,1]),[']'].map(i=> [i,4.31,13,1]),['◯'].map(i=> [i,12.69,13,1]),['℃'].map(i=> [i,11.27,13,1])],"STKaiti14":[['`'].map(i=> [i,4.67,14,1]),['p'].map(i=> [i,7.33,14,1]),['x'].map(i=> [i,6.67,14,1]),['↵'].map(i=> [i,0,14,1]),['我','！','？','《','》','；','，','、','。','（','）','：','【','】'].map(i=> [i,14,14,1]),['“'].map(i=> [i,5.66,14,1]),[' '].map(i=> [i,7,14,1]),['0','1','3','4','5','7','8','9'].map(i=> [i,6.58,14,1]),['2','6'].map(i=> [i,6.59,14,1]),['h'].map(i=> [i,7.47,14,1]),['a','o'].map(i=> [i,7.17,14,1]),['?'].map(i=> [i,5.13,14,1]),['['].map(i=> [i,4.5,14,1]),['w','<','=','~','#'].map(i=> [i,9.36,14,1]),['*'].map(i=> [i,5.98,14,1]),['T'].map(i=> [i,8.63,14,1]),['G'].map(i=> [i,10.81,14,1]),['I'].map(i=> [i,4.97,14,1]),['}'].map(i=> [i,6.72,14,1]),[',','.',':','!',';'].map(i=> [i,3.08,14,1]),['/','^','\\','|'].map(i=> [i,7.02,14,1]),['\"'].map(i=> [i,5.7,14,1]),['>','+'].map(i=> [i,9.34,14,1]),['J'].map(i=> [i,4.69,14,1]),['@'].map(i=> [i,12.86,14,1]),['%'].map(i=> [i,11.53,14,1]),['l'].map(i=> [i,3.81,14,1]),['☑','☐'].map(i=> [i,11.64,14,1]),['⊙','⭘'].map(i=> [i,14.02,14,1]),['”'].map(i=> [i,5.67,14,1]),['‘','’'].map(i=> [i,3.5,14,1]),['b'].map(i=> [i,7.3,14,1]),['d'].map(i=> [i,7.23,14,1]),['c'].map(i=> [i,5.92,14,1]),['e'].map(i=> [i,6.02,14,1]),['f'].map(i=> [i,4.73,14,1]),['g','{'].map(i=> [i,6.73,14,1]),['i'].map(i=> [i,3.91,14,1]),['j'].map(i=> [i,3.27,14,1]),['k'].map(i=> [i,7.28,14,1]),['m'].map(i=> [i,10.97,14,1]),['n'].map(i=> [i,7.45,14,1]),['q'].map(i=> [i,7.31,14,1]),['r'].map(i=> [i,5.45,14,1]),['s'].map(i=> [i,5.39,14,1]),['t'].map(i=> [i,4.98,14,1]),['u'].map(i=> [i,7.42,14,1]),['v'].map(i=> [i,6.61,14,1]),['y'].map(i=> [i,6.44,14,1]),['z'].map(i=> [i,6.06,14,1]),['A'].map(i=> [i,9.81,14,1]),['B'].map(i=> [i,9,14,1]),['C'].map(i=> [i,9.03,14,1]),['D'].map(i=> [i,10.84,14,1]),['E'].map(i=> [i,9.28,14,1]),['F'].map(i=> [i,8.02,14,1]),['H'].map(i=> [i,11.31,14,1]),['K'].map(i=> [i,10.41,14,1]),['L'].map(i=> [i,8.25,14,1]),['M'].map(i=> [i,13.05,14,1]),['N'].map(i=> [i,10.89,14,1]),['O'].map(i=> [i,10.88,14,1]),['P'].map(i=> [i,8.03,14,1]),['Q'].map(i=> [i,10.77,14,1]),['R'].map(i=> [i,9.73,14,1]),['S'].map(i=> [i,6.8,14,1]),['U'].map(i=> [i,10.02,14,1]),['V'].map(i=> [i,9.61,14,1]),['W'].map(i=> [i,12.55,14,1]),['X'].map(i=> [i,9.88,14,1]),['Y'].map(i=> [i,9.42,14,1]),['Z'].map(i=> [i,9.27,14,1]),['$'].map(i=> [i,6.28,14,1]),['&'].map(i=> [i,10.23,14,1]),['('].map(i=> [i,4.77,14,1]),[')'].map(i=> [i,4.63,14,1]),['_'].map(i=> [i,7.08,14,1]),['-'].map(i=> [i,4.41,14,1]),[']'].map(i=> [i,4.55,14,1]),['\''].map(i=> [i,2.48,14,1]),['◯'].map(i=> [i,13.36,14,1]),['℃'].map(i=> [i,11.86,14,1])],"STKaiti15":[['↵'].map(i=> [i,0,15,1]),[' '].map(i=> [i,7.5,15,1]),['我','！','？','《','》','；','，','、','。','（','）','：','【','】'].map(i=> [i,15,15,1]),['0','1','2','3','5','6','7','9'].map(i=> [i,7.05,15,1]),['4','8'].map(i=> [i,7.06,15,1]),['z'].map(i=> [i,6.5,15,1]),['h'].map(i=> [i,8,15,1]),['o'].map(i=> [i,7.69,15,1]),['p'].map(i=> [i,7.86,15,1]),['>'].map(i=> [i,10.03,15,1]),['*'].map(i=> [i,6.42,15,1]),['G'].map(i=> [i,11.58,15,1]),['`'].map(i=> [i,5.02,15,1]),['P'].map(i=> [i,8.61,15,1]),['{','}'].map(i=> [i,7.2,15,1]),['T'].map(i=> [i,9.23,15,1]),[',',':','!',';'].map(i=> [i,3.3,15,1]),['/','^','\\','|'].map(i=> [i,7.52,15,1]),['?'].map(i=> [i,5.48,15,1]),['\"'].map(i=> [i,6.11,15,1]),['w','<','=','~','#','+'].map(i=> [i,10.02,15,1]),['@'].map(i=> [i,13.78,15,1]),['$'].map(i=> [i,6.73,15,1]),['%'].map(i=> [i,12.36,15,1]),['&'].map(i=> [i,10.95,15,1]),['-'].map(i=> [i,4.72,15,1]),['l'].map(i=> [i,4.08,15,1]),['\''].map(i=> [i,2.67,15,1]),['☑','☐'].map(i=> [i,12.47,15,1]),['⊙','⭘'].map(i=> [i,15.02,15,1]),['◯'].map(i=> [i,14.31,15,1]),['y'].map(i=> [i,6.91,15,1]),['”'].map(i=> [i,6.08,15,1]),['q'].map(i=> [i,7.83,15,1]),['d'].map(i=> [i,7.75,15,1]),['E'].map(i=> [i,9.95,15,1]),['U'].map(i=> [i,10.73,15,1]),['s'].map(i=> [i,5.77,15,1]),['c'].map(i=> [i,6.34,15,1]),['I'].map(i=> [i,5.33,15,1]),['“'].map(i=> [i,6.06,15,1]),['‘'].map(i=> [i,3.73,15,1]),['’'].map(i=> [i,3.75,15,1]),['a'].map(i=> [i,7.67,15,1]),['b','k'].map(i=> [i,7.81,15,1]),['e'].map(i=> [i,6.45,15,1]),['f'].map(i=> [i,5.06,15,1]),['g'].map(i=> [i,7.22,15,1]),['i'].map(i=> [i,4.19,15,1]),['j'].map(i=> [i,3.48,15,1]),['m'].map(i=> [i,11.77,15,1]),['n'].map(i=> [i,7.97,15,1]),['r'].map(i=> [i,5.84,15,1]),['t'].map(i=> [i,5.34,15,1]),['u'].map(i=> [i,7.95,15,1]),['v'].map(i=> [i,7.08,15,1]),['x'].map(i=> [i,7.16,15,1]),['A'].map(i=> [i,10.5,15,1]),['B'].map(i=> [i,9.64,15,1]),['C'].map(i=> [i,9.67,15,1]),['D'].map(i=> [i,11.61,15,1]),['F'].map(i=> [i,8.59,15,1]),['H'].map(i=> [i,12.13,15,1]),['J'].map(i=> [i,5,15,1]),['K'].map(i=> [i,11.17,15,1]),['L'].map(i=> [i,8.83,15,1]),['M'].map(i=> [i,13.97,15,1]),['N'].map(i=> [i,11.67,15,1]),['O'].map(i=> [i,11.66,15,1]),['Q'].map(i=> [i,11.52,15,1]),['R'].map(i=> [i,10.44,15,1]),['S'].map(i=> [i,7.28,15,1]),['V'].map(i=> [i,10.3,15,1]),['W'].map(i=> [i,13.45,15,1]),['X'].map(i=> [i,10.58,15,1]),['Y'].map(i=> [i,10.09,15,1]),['Z'].map(i=> [i,9.91,15,1]),['.'].map(i=> [i,3.31,15,1]),['('].map(i=> [i,5.09,15,1]),[')'].map(i=> [i,4.95,15,1]),['_'].map(i=> [i,7.59,15,1]),['['].map(i=> [i,4.83,15,1]),[']'].map(i=> [i,4.86,15,1]),['℃'].map(i=> [i,12.7,15,1])],"STKaiti14.7":[['x'].map(i=> [i,7,15,1]),['℃'].map(i=> [i,12.45,15,1]),['↵'].map(i=> [i,0,15,1]),['z'].map(i=> [i,6.38,15,1]),['“','”'].map(i=> [i,5.95,15,1]),['g','{','}'].map(i=> [i,7.06,15,1]),['h'].map(i=> [i,7.84,15,1]),['i'].map(i=> [i,4.09,15,1]),['O'].map(i=> [i,11.42,15,1]),['我','》','（','⊙'].map(i=> [i,14.7,15,1]),['！','？','《','；','，','、','。','）','：','【','】','⭘'].map(i=> [i,14.72,15,1]),[' '].map(i=> [i,7.35,15,1]),['1','2','3','5','6','7','9'].map(i=> [i,6.91,15,1]),['0','4','8'].map(i=> [i,6.92,15,1]),['?'].map(i=> [i,5.38,15,1]),['a','o'].map(i=> [i,7.53,15,1]),['p'].map(i=> [i,7.7,15,1]),['w','<','=','#'].map(i=> [i,9.83,15,1]),['*'].map(i=> [i,6.3,15,1]),['I'].map(i=> [i,5.22,15,1]),['`'].map(i=> [i,4.92,15,1]),['G'].map(i=> [i,11.36,15,1]),['T'].map(i=> [i,9.06,15,1]),[',','.',':','!',';'].map(i=> [i,3.23,15,1]),['/','^','|'].map(i=> [i,7.36,15,1]),['\"'].map(i=> [i,5.98,15,1]),['>','~','+'].map(i=> [i,9.81,15,1]),['J'].map(i=> [i,4.91,15,1]),['&'].map(i=> [i,10.73,15,1]),['l'].map(i=> [i,4,15,1]),['\''].map(i=> [i,2.63,15,1]),['☑','☐'].map(i=> [i,12.22,15,1]),['◯'].map(i=> [i,14.03,15,1]),['‘'].map(i=> [i,3.66,15,1]),['u'].map(i=> [i,7.8,15,1]),['d'].map(i=> [i,7.58,15,1]),['B'].map(i=> [i,9.45,15,1]),['b'].map(i=> [i,7.66,15,1]),['q'].map(i=> [i,7.67,15,1]),['n'].map(i=> [i,7.81,15,1]),['%'].map(i=> [i,12.13,15,1]),['('].map(i=> [i,5,15,1]),['V'].map(i=> [i,10.09,15,1]),['Y'].map(i=> [i,9.91,15,1]),['’'].map(i=> [i,3.67,15,1]),['c'].map(i=> [i,6.22,15,1]),['e'].map(i=> [i,6.33,15,1]),['f'].map(i=> [i,4.97,15,1]),['j'].map(i=> [i,3.44,15,1]),['k'].map(i=> [i,7.64,15,1]),['m'].map(i=> [i,11.53,15,1]),['r'].map(i=> [i,5.72,15,1]),['s'].map(i=> [i,5.66,15,1]),['t'].map(i=> [i,5.23,15,1]),['v'].map(i=> [i,6.94,15,1]),['y'].map(i=> [i,6.77,15,1]),['A'].map(i=> [i,10.28,15,1]),['C'].map(i=> [i,9.48,15,1]),['D'].map(i=> [i,11.38,15,1]),['E'].map(i=> [i,9.75,15,1]),['F'].map(i=> [i,8.42,15,1]),['H'].map(i=> [i,11.88,15,1]),['K'].map(i=> [i,10.94,15,1]),['L'].map(i=> [i,8.66,15,1]),['M'].map(i=> [i,13.69,15,1]),['N'].map(i=> [i,11.44,15,1]),['P'].map(i=> [i,8.45,15,1]),['Q'].map(i=> [i,11.28,15,1]),['R'].map(i=> [i,10.23,15,1]),['S'].map(i=> [i,7.13,15,1]),['U'].map(i=> [i,10.52,15,1]),['W'].map(i=> [i,13.17,15,1]),['X'].map(i=> [i,10.36,15,1]),['Z'].map(i=> [i,9.72,15,1]),['@'].map(i=> [i,13.48,15,1]),['$'].map(i=> [i,6.59,15,1]),[')'].map(i=> [i,4.84,15,1]),['_'].map(i=> [i,7.44,15,1]),['-'].map(i=> [i,4.63,15,1]),['['].map(i=> [i,4.73,15,1]),[']'].map(i=> [i,4.75,15,1]),['\\'].map(i=> [i,7.38,15,1])],"STKaiti16":[['\"'].map(i=> [i,6.52,16,1]),['↵'].map(i=> [i,0,16,1]),['我','！','？','《','》','；','，','、','。','（','）','：','【','】'].map(i=> [i,16,16,1]),[' '].map(i=> [i,8,16,1]),['0','2','3','4','6','7','8'].map(i=> [i,7.52,16,1]),['1','5','9'].map(i=> [i,7.53,16,1]),['z'].map(i=> [i,6.92,16,1]),['h'].map(i=> [i,8.53,16,1]),['a','o'].map(i=> [i,8.19,16,1]),['p'].map(i=> [i,8.39,16,1]),['?'].map(i=> [i,5.86,16,1]),['['].map(i=> [i,5.14,16,1]),['w','<','>','=','~','#','+'].map(i=> [i,10.69,16,1]),['*'].map(i=> [i,6.86,16,1]),['T'].map(i=> [i,9.86,16,1]),['G'].map(i=> [i,12.34,16,1]),['t'].map(i=> [i,5.69,16,1]),['J','`'].map(i=> [i,5.34,16,1]),['F','P'].map(i=> [i,9.17,16,1]),['{'].map(i=> [i,7.67,16,1]),[',','.',':','!',';'].map(i=> [i,3.52,16,1]),['/','^','\\','|'].map(i=> [i,8.02,16,1]),['@'].map(i=> [i,14.69,16,1]),['$'].map(i=> [i,7.19,16,1]),['g','}'].map(i=> [i,7.69,16,1]),['l'].map(i=> [i,4.34,16,1]),['\''].map(i=> [i,2.84,16,1]),['☑','☐'].map(i=> [i,13.3,16,1]),['⊙','⭘'].map(i=> [i,16.02,16,1]),['◯'].map(i=> [i,15.28,16,1]),['“'].map(i=> [i,6.47,16,1]),['”'].map(i=> [i,6.48,16,1]),['‘'].map(i=> [i,3.98,16,1]),['’'].map(i=> [i,4,16,1]),['b','k'].map(i=> [i,8.33,16,1]),['d'].map(i=> [i,8.25,16,1]),['c'].map(i=> [i,6.77,16,1]),['e'].map(i=> [i,6.89,16,1]),['f'].map(i=> [i,5.41,16,1]),['i'].map(i=> [i,4.47,16,1]),['j'].map(i=> [i,3.72,16,1]),['m'].map(i=> [i,12.55,16,1]),['n'].map(i=> [i,8.52,16,1]),['q'].map(i=> [i,8.34,16,1]),['r'].map(i=> [i,6.22,16,1]),['s'].map(i=> [i,6.17,16,1]),['u'].map(i=> [i,8.48,16,1]),['v'].map(i=> [i,7.55,16,1]),['x'].map(i=> [i,7.64,16,1]),['y'].map(i=> [i,7.36,16,1]),['A'].map(i=> [i,11.2,16,1]),['B'].map(i=> [i,10.28,16,1]),['C'].map(i=> [i,10.33,16,1]),['D'].map(i=> [i,12.38,16,1]),['E'].map(i=> [i,10.61,16,1]),['H'].map(i=> [i,12.94,16,1]),['I'].map(i=> [i,5.67,16,1]),['K'].map(i=> [i,11.91,16,1]),['L'].map(i=> [i,9.42,16,1]),['M'].map(i=> [i,14.89,16,1]),['N'].map(i=> [i,12.45,16,1]),['O'].map(i=> [i,12.44,16,1]),['Q'].map(i=> [i,12.3,16,1]),['R'].map(i=> [i,11.14,16,1]),['S'].map(i=> [i,7.75,16,1]),['U'].map(i=> [i,11.44,16,1]),['V'].map(i=> [i,10.98,16,1]),['W'].map(i=> [i,14.34,16,1]),['X'].map(i=> [i,11.28,16,1]),['Y'].map(i=> [i,10.77,16,1]),['Z'].map(i=> [i,10.58,16,1]),['%'].map(i=> [i,13.19,16,1]),['&'].map(i=> [i,11.67,16,1]),['('].map(i=> [i,5.44,16,1]),[')'].map(i=> [i,5.28,16,1]),['_'].map(i=> [i,8.09,16,1]),['-'].map(i=> [i,5.03,16,1]),[']'].map(i=> [i,5.19,16,1]),['℃'].map(i=> [i,13.55,16,1])],"STKaiti18.7":[['我','》','（'].map(i=> [i,18.7,19,1]),['！','？','《','；','，','、','。','）','：','【','】','⊙','⭘'].map(i=> [i,18.72,19,1]),[' '].map(i=> [i,9.35,19,1]),['0','1','3','4','5','7','8'].map(i=> [i,8.78,19,1]),['2','6','9'].map(i=> [i,8.8,19,1]),['_'].map(i=> [i,9.47,19,1]),['z'].map(i=> [i,8.09,19,1]),['a','o'].map(i=> [i,9.56,19,1]),['p'].map(i=> [i,9.8,19,1]),['?'].map(i=> [i,6.84,19,1]),['w','>','=','~'].map(i=> [i,12.48,19,1]),['*'].map(i=> [i,8,19,1]),['T'].map(i=> [i,11.52,19,1]),['G'].map(i=> [i,14.44,19,1]),['I'].map(i=> [i,6.64,19,1]),['J'].map(i=> [i,6.23,19,1]),['P'].map(i=> [i,10.73,19,1]),['{','}'].map(i=> [i,8.97,19,1]),[',','.',':','!',';'].map(i=> [i,4.11,19,1]),['/','^','\\'].map(i=> [i,9.36,19,1]),['\"'].map(i=> [i,7.61,19,1]),['<','#','+'].map(i=> [i,12.5,19,1]),['@'].map(i=> [i,17.16,19,1]),['$'].map(i=> [i,8.39,19,1]),['%'].map(i=> [i,15.41,19,1]),['|'].map(i=> [i,9.38,19,1]),['l'].map(i=> [i,5.08,19,1]),['☐'].map(i=> [i,15.53,19,1]),['☑'].map(i=> [i,15.55,19,1]),['◯'].map(i=> [i,17.84,19,1]),['↵'].map(i=> [i,0,19,1]),['q'].map(i=> [i,9.77,19,1]),['x'].map(i=> [i,8.91,19,1]),['k'].map(i=> [i,9.72,19,1]),['h','n'].map(i=> [i,9.95,19,1]),['m'].map(i=> [i,14.66,19,1]),['('].map(i=> [i,6.34,19,1]),['℃'].map(i=> [i,15.84,19,1]),['F'].map(i=> [i,10.7,19,1]),['g'].map(i=> [i,9,19,1]),['v'].map(i=> [i,8.83,19,1]),['t'].map(i=> [i,6.66,19,1]),['“'].map(i=> [i,7.56,19,1]),['”'].map(i=> [i,7.58,19,1]),['‘'].map(i=> [i,4.66,19,1]),['’'].map(i=> [i,4.67,19,1]),['b'].map(i=> [i,9.73,19,1]),['d'].map(i=> [i,9.66,19,1]),['c'].map(i=> [i,7.91,19,1]),['e'].map(i=> [i,8.03,19,1]),['f'].map(i=> [i,6.31,19,1]),['i'].map(i=> [i,5.22,19,1]),['j'].map(i=> [i,4.36,19,1]),['r'].map(i=> [i,7.27,19,1]),['s'].map(i=> [i,7.2,19,1]),['u'].map(i=> [i,9.91,19,1]),['y'].map(i=> [i,8.61,19,1]),['A'].map(i=> [i,13.08,19,1]),['B'].map(i=> [i,12.03,19,1]),['C'].map(i=> [i,12.05,19,1]),['D'].map(i=> [i,14.48,19,1]),['E'].map(i=> [i,12.39,19,1]),['H'].map(i=> [i,15.11,19,1]),['K'].map(i=> [i,13.91,19,1]),['L'].map(i=> [i,11.02,19,1]),['M'].map(i=> [i,17.41,19,1]),['N'].map(i=> [i,14.55,19,1]),['O'].map(i=> [i,14.53,19,1]),['Q'].map(i=> [i,14.34,19,1]),['R'].map(i=> [i,13,19,1]),['S'].map(i=> [i,9.08,19,1]),['U'].map(i=> [i,13.36,19,1]),['V'].map(i=> [i,12.84,19,1]),['W'].map(i=> [i,16.75,19,1]),['X'].map(i=> [i,13.19,19,1]),['Y'].map(i=> [i,12.58,19,1]),['Z'].map(i=> [i,12.36,19,1]),['`'].map(i=> [i,6.25,19,1]),['&'].map(i=> [i,13.66,19,1]),[')'].map(i=> [i,6.17,19,1]),['-'].map(i=> [i,5.86,19,1]),['['].map(i=> [i,6.02,19,1]),[']'].map(i=> [i,6.06,19,1]),['\''].map(i=> [i,3.31,19,1])],"STKaiti19":[['_'].map(i=> [i,9.61,19,1]),['d'].map(i=> [i,9.8,19,1]),['T'].map(i=> [i,11.7,19,1]),['R'].map(i=> [i,13.22,19,1]),['↵'].map(i=> [i,0,19,1]),['我','！','？','《','》','；','，','、','。','（','）','：','【','】'].map(i=> [i,19,19,1]),[' '].map(i=> [i,9.5,19,1]),['0','1','3','4','6','7','8'].map(i=> [i,8.92,19,1]),['2','5','9'].map(i=> [i,8.94,19,1]),['z'].map(i=> [i,8.22,19,1]),['h'].map(i=> [i,10.13,19,1]),['a'].map(i=> [i,9.72,19,1]),['p'].map(i=> [i,9.95,19,1]),['<','>','=','~','#','+'].map(i=> [i,12.69,19,1]),['*'].map(i=> [i,8.13,19,1]),['G'].map(i=> [i,14.67,19,1]),['I'].map(i=> [i,6.73,19,1]),['J','`'].map(i=> [i,6.34,19,1]),['P'].map(i=> [i,10.91,19,1]),['}'].map(i=> [i,9.11,19,1]),[',','.',':'].map(i=> [i,4.17,19,1]),['!',';'].map(i=> [i,4.19,19,1]),['/','^','\\','|'].map(i=> [i,9.52,19,1]),['\"'].map(i=> [i,7.73,19,1]),['w'].map(i=> [i,12.7,19,1]),['@'].map(i=> [i,17.44,19,1]),['%'].map(i=> [i,15.66,19,1]),['{'].map(i=> [i,9.13,19,1]),['l'].map(i=> [i,5.16,19,1]),['☑','☐'].map(i=> [i,15.78,19,1]),['⊙','⭘'].map(i=> [i,19.02,19,1]),['◯'].map(i=> [i,18.14,19,1]),['r'].map(i=> [i,7.39,19,1]),['‘','’'].map(i=> [i,4.73,19,1]),['f'].map(i=> [i,6.42,19,1]),['“','”'].map(i=> [i,7.69,19,1]),['$'].map(i=> [i,8.52,19,1]),['t'].map(i=> [i,6.75,19,1]),['o'].map(i=> [i,9.73,19,1]),['c'].map(i=> [i,8.03,19,1]),['q'].map(i=> [i,9.91,19,1]),['b'].map(i=> [i,9.89,19,1]),['e'].map(i=> [i,8.17,19,1]),['g'].map(i=> [i,9.14,19,1]),['i'].map(i=> [i,5.3,19,1]),['j'].map(i=> [i,4.42,19,1]),['k'].map(i=> [i,9.88,19,1]),['m'].map(i=> [i,14.89,19,1]),['n'].map(i=> [i,10.11,19,1]),['s'].map(i=> [i,7.31,19,1]),['u'].map(i=> [i,10.08,19,1]),['v'].map(i=> [i,8.95,19,1]),['x'].map(i=> [i,9.05,19,1]),['y'].map(i=> [i,8.73,19,1]),['A'].map(i=> [i,13.3,19,1]),['B'].map(i=> [i,12.2,19,1]),['C'].map(i=> [i,12.27,19,1]),['D'].map(i=> [i,14.7,19,1]),['E'].map(i=> [i,12.59,19,1]),['F'].map(i=> [i,10.88,19,1]),['H'].map(i=> [i,15.34,19,1]),['K'].map(i=> [i,14.14,19,1]),['L'].map(i=> [i,11.19,19,1]),['M'].map(i=> [i,17.69,19,1]),['N'].map(i=> [i,14.78,19,1]),['O'].map(i=> [i,14.75,19,1]),['Q'].map(i=> [i,14.59,19,1]),['S'].map(i=> [i,9.2,19,1]),['U'].map(i=> [i,13.58,19,1]),['V'].map(i=> [i,13.05,19,1]),['W'].map(i=> [i,17.03,19,1]),['X'].map(i=> [i,13.39,19,1]),['Y'].map(i=> [i,12.78,19,1]),['Z'].map(i=> [i,12.56,19,1]),['?'].map(i=> [i,6.95,19,1]),['&'].map(i=> [i,13.88,19,1]),['('].map(i=> [i,6.45,19,1]),[')'].map(i=> [i,6.27,19,1]),['-'].map(i=> [i,5.95,19,1]),['['].map(i=> [i,6.13,19,1]),[']'].map(i=> [i,6.14,19,1]),['\''].map(i=> [i,3.39,19,1]),['℃'].map(i=> [i,16.09,19,1])],"STKaiti20":[['我','！','？','《','》','；','，','、','。','（','）','：','【','】'].map(i=> [i,20,20,1]),[' '].map(i=> [i,10,20,1]),['0','7'].map(i=> [i,9.41,20,1]),['1','2','3','4','5','6','8','9'].map(i=> [i,9.39,20,1]),['z'].map(i=> [i,8.66,20,1]),['h'].map(i=> [i,10.66,20,1]),['a','o'].map(i=> [i,10.23,20,1]),['?'].map(i=> [i,7.31,20,1]),['w','='].map(i=> [i,13.34,20,1]),['y'].map(i=> [i,9.19,20,1]),['*'].map(i=> [i,8.56,20,1]),['G'].map(i=> [i,15.44,20,1]),['I'].map(i=> [i,7.09,20,1]),['P'].map(i=> [i,11.47,20,1]),['{','}'].map(i=> [i,9.59,20,1]),['T'].map(i=> [i,12.31,20,1]),[',',':',';'].map(i=> [i,4.39,20,1]),['.','!'].map(i=> [i,4.41,20,1]),['/','^','\\','|'].map(i=> [i,10.02,20,1]),['<','>','~','#','+'].map(i=> [i,13.36,20,1]),['J','`'].map(i=> [i,6.67,20,1]),['&'].map(i=> [i,14.59,20,1]),['l'].map(i=> [i,5.44,20,1]),['\''].map(i=> [i,3.56,20,1]),['☑'].map(i=> [i,16.61,20,1]),['☐'].map(i=> [i,16.63,20,1]),['⊙','⭘'].map(i=> [i,20.02,20,1]),['↵'].map(i=> [i,0,20,1]),['A'].map(i=> [i,14,20,1]),['r'].map(i=> [i,7.78,20,1]),['“','”'].map(i=> [i,8.09,20,1]),['‘'].map(i=> [i,4.97,20,1]),['’'].map(i=> [i,5,20,1]),['b','k'].map(i=> [i,10.41,20,1]),['d'].map(i=> [i,10.31,20,1]),['c'].map(i=> [i,8.45,20,1]),['e'].map(i=> [i,8.59,20,1]),['f'].map(i=> [i,6.75,20,1]),['g'].map(i=> [i,9.63,20,1]),['i'].map(i=> [i,5.58,20,1]),['j'].map(i=> [i,4.64,20,1]),['m'].map(i=> [i,15.67,20,1]),['n'].map(i=> [i,10.64,20,1]),['p'].map(i=> [i,10.47,20,1]),['q'].map(i=> [i,10.44,20,1]),['s'].map(i=> [i,7.69,20,1]),['t'].map(i=> [i,7.13,20,1]),['u'].map(i=> [i,10.59,20,1]),['v'].map(i=> [i,9.44,20,1]),['x'].map(i=> [i,9.55,20,1]),['B'].map(i=> [i,12.86,20,1]),['C'].map(i=> [i,12.89,20,1]),['D'].map(i=> [i,15.47,20,1]),['E'].map(i=> [i,13.27,20,1]),['F'].map(i=> [i,11.45,20,1]),['H'].map(i=> [i,16.16,20,1]),['K'].map(i=> [i,14.88,20,1]),['L'].map(i=> [i,11.78,20,1]),['M'].map(i=> [i,18.61,20,1]),['N'].map(i=> [i,15.56,20,1]),['O'].map(i=> [i,15.53,20,1]),['Q'].map(i=> [i,15.36,20,1]),['R'].map(i=> [i,13.92,20,1]),['S'].map(i=> [i,9.69,20,1]),['U'].map(i=> [i,14.3,20,1]),['V'].map(i=> [i,13.73,20,1]),['W'].map(i=> [i,17.92,20,1]),['X'].map(i=> [i,14.09,20,1]),['Y'].map(i=> [i,13.45,20,1]),['Z'].map(i=> [i,13.22,20,1]),['\"'].map(i=> [i,8.14,20,1]),['@'].map(i=> [i,18.34,20,1]),['$'].map(i=> [i,8.98,20,1]),['%'].map(i=> [i,16.47,20,1]),['('].map(i=> [i,6.8,20,1]),[')'].map(i=> [i,6.59,20,1]),['_'].map(i=> [i,10.11,20,1]),['-'].map(i=> [i,6.28,20,1]),['['].map(i=> [i,6.44,20,1]),[']'].map(i=> [i,6.47,20,1]),['◯'].map(i=> [i,19.09,20,1]),['℃'].map(i=> [i,16.92,20,1])],"STKaiti21.3":[['.','!',';'].map(i=> [i,4.67,21,1]),['↵'].map(i=> [i,0,21,1]),['我','！','？','《','》','，','、','。','（','：','【','】','⭘'].map(i=> [i,21.31,21,1]),['；','）','⊙'].map(i=> [i,21.33,21,1]),[' '].map(i=> [i,10.65,21,1]),['2','4','7'].map(i=> [i,10.02,21,1]),['0','1','3','5','6','8','9'].map(i=> [i,10,21,1]),['_'].map(i=> [i,10.78,21,1]),['z'].map(i=> [i,9.22,21,1]),['h'].map(i=> [i,11.34,21,1]),['a','o'].map(i=> [i,10.91,21,1]),['p'].map(i=> [i,11.16,21,1]),['w','<','>','=','#','+'].map(i=> [i,14.22,21,1]),['T'].map(i=> [i,13.11,21,1]),['G'].map(i=> [i,16.44,21,1]),['I'].map(i=> [i,7.56,21,1]),['J','`'].map(i=> [i,7.11,21,1]),['P'].map(i=> [i,12.22,21,1]),['{','}'].map(i=> [i,10.22,21,1]),[',',':'].map(i=> [i,4.69,21,1]),['/','^','|'].map(i=> [i,10.67,21,1]),['?'].map(i=> [i,7.8,21,1]),['\"'].map(i=> [i,8.66,21,1]),['~'].map(i=> [i,14.23,21,1]),['@'].map(i=> [i,19.55,21,1]),['$'].map(i=> [i,9.56,21,1]),['%'].map(i=> [i,17.55,21,1]),['&'].map(i=> [i,15.53,21,1]),['-'].map(i=> [i,6.69,21,1]),['l'].map(i=> [i,5.78,21,1]),['\''].map(i=> [i,3.78,21,1]),['☑'].map(i=> [i,17.7,21,1]),['☐'].map(i=> [i,17.69,21,1]),['◯'].map(i=> [i,20.33,21,1]),['x'].map(i=> [i,10.16,21,1]),['X'].map(i=> [i,15.02,21,1]),['“','”'].map(i=> [i,8.63,21,1]),['s'].map(i=> [i,8.19,21,1]),['q'].map(i=> [i,11.11,21,1]),['u'].map(i=> [i,11.3,21,1]),['B'].map(i=> [i,13.69,21,1]),['‘'].map(i=> [i,5.3,21,1]),['’'].map(i=> [i,5.31,21,1]),['b'].map(i=> [i,11.09,21,1]),['d'].map(i=> [i,10.98,21,1]),['c'].map(i=> [i,9,21,1]),['e'].map(i=> [i,9.16,21,1]),['f'].map(i=> [i,7.19,21,1]),['g'].map(i=> [i,10.25,21,1]),['i'].map(i=> [i,5.94,21,1]),['j'].map(i=> [i,4.95,21,1]),['k'].map(i=> [i,11.08,21,1]),['m'].map(i=> [i,16.69,21,1]),['n'].map(i=> [i,11.33,21,1]),['r'].map(i=> [i,8.28,21,1]),['t'].map(i=> [i,7.58,21,1]),['v'].map(i=> [i,10.05,21,1]),['y'].map(i=> [i,9.78,21,1]),['A'].map(i=> [i,14.91,21,1]),['C'].map(i=> [i,13.73,21,1]),['D'].map(i=> [i,16.48,21,1]),['E'].map(i=> [i,14.11,21,1]),['F'].map(i=> [i,12.2,21,1]),['H'].map(i=> [i,17.2,21,1]),['K'].map(i=> [i,15.84,21,1]),['L'].map(i=> [i,12.53,21,1]),['M'].map(i=> [i,19.83,21,1]),['N'].map(i=> [i,16.56,21,1]),['O'].map(i=> [i,16.55,21,1]),['Q'].map(i=> [i,16.36,21,1]),['R'].map(i=> [i,14.81,21,1]),['S'].map(i=> [i,10.33,21,1]),['U'].map(i=> [i,15.23,21,1]),['V'].map(i=> [i,14.63,21,1]),['W'].map(i=> [i,19.08,21,1]),['Y'].map(i=> [i,14.33,21,1]),['Z'].map(i=> [i,14.06,21,1]),['*'].map(i=> [i,9.13,21,1]),['('].map(i=> [i,7.23,21,1]),[')'].map(i=> [i,7.02,21,1]),['['].map(i=> [i,6.86,21,1]),[']'].map(i=> [i,6.89,21,1]),['\\'].map(i=> [i,10.66,21,1]),['℃'].map(i=> [i,18.03,21,1])],"STKaiti24":[['↵'].map(i=> [i,0,24,1]),['P'].map(i=> [i,13.77,24,1]),['我','！','？','《','》','；','，','、','。','（','）','：','【','】'].map(i=> [i,24,24,1]),[' '].map(i=> [i,12,24,1]),['1','3','4','6','7','9'].map(i=> [i,11.27,24,1]),['0','2','5','8'].map(i=> [i,11.28,24,1]),['z'].map(i=> [i,10.38,24,1]),['h'].map(i=> [i,12.78,24,1]),['p'].map(i=> [i,12.58,24,1]),['?'].map(i=> [i,8.78,24,1]),['w','<','=','+'].map(i=> [i,16.02,24,1]),['*'].map(i=> [i,10.27,24,1]),['T'].map(i=> [i,14.78,24,1]),['G'].map(i=> [i,18.52,24,1]),['J'].map(i=> [i,8.02,24,1]),['.',':'].map(i=> [i,5.27,24,1]),['/','^','\\','|'].map(i=> [i,12.02,24,1]),['\"'].map(i=> [i,9.77,24,1]),['>','~','#'].map(i=> [i,16.03,24,1]),['`'].map(i=> [i,8,24,1]),[',','!',';'].map(i=> [i,5.28,24,1]),['@'].map(i=> [i,22.02,24,1]),['$'].map(i=> [i,10.77,24,1]),['%'].map(i=> [i,19.77,24,1]),['&'].map(i=> [i,17.52,24,1]),['{','}'].map(i=> [i,11.52,24,1]),['l'].map(i=> [i,6.52,24,1]),['\''].map(i=> [i,4.27,24,1]),['☑','☐'].map(i=> [i,19.94,24,1]),['⊙','⭘'].map(i=> [i,24.02,24,1]),['◯'].map(i=> [i,22.89,24,1]),['“'].map(i=> [i,9.7,24,1]),['”'].map(i=> [i,9.72,24,1]),['‘'].map(i=> [i,5.97,24,1]),['’'].map(i=> [i,5.98,24,1]),['a','o'].map(i=> [i,12.28,24,1]),['b'].map(i=> [i,12.5,24,1]),['d'].map(i=> [i,12.38,24,1]),['c'].map(i=> [i,10.14,24,1]),['e'].map(i=> [i,10.31,24,1]),['f'].map(i=> [i,8.11,24,1]),['g'].map(i=> [i,11.53,24,1]),['i'].map(i=> [i,6.69,24,1]),['j'].map(i=> [i,5.59,24,1]),['k'].map(i=> [i,12.47,24,1]),['m'].map(i=> [i,18.81,24,1]),['n'].map(i=> [i,12.75,24,1]),['q'].map(i=> [i,12.52,24,1]),['r'].map(i=> [i,9.33,24,1]),['s'].map(i=> [i,9.23,24,1]),['t'].map(i=> [i,8.53,24,1]),['u'].map(i=> [i,12.72,24,1]),['v'].map(i=> [i,11.31,24,1]),['x'].map(i=> [i,11.45,24,1]),['y'].map(i=> [i,11.03,24,1]),['A'].map(i=> [i,16.8,24,1]),['B'].map(i=> [i,15.42,24,1]),['C'].map(i=> [i,15.47,24,1]),['D'].map(i=> [i,18.56,24,1]),['E'].map(i=> [i,15.91,24,1]),['F'].map(i=> [i,13.75,24,1]),['H'].map(i=> [i,19.39,24,1]),['I'].map(i=> [i,8.5,24,1]),['K'].map(i=> [i,17.84,24,1]),['L'].map(i=> [i,14.13,24,1]),['M'].map(i=> [i,22.34,24,1]),['N'].map(i=> [i,18.66,24,1]),['O'].map(i=> [i,18.64,24,1]),['Q'].map(i=> [i,18.42,24,1]),['R'].map(i=> [i,16.7,24,1]),['S'].map(i=> [i,11.63,24,1]),['U'].map(i=> [i,17.16,24,1]),['V'].map(i=> [i,16.47,24,1]),['W'].map(i=> [i,21.5,24,1]),['X'].map(i=> [i,16.92,24,1]),['Y'].map(i=> [i,16.14,24,1]),['Z'].map(i=> [i,15.84,24,1]),['('].map(i=> [i,8.14,24,1]),[')'].map(i=> [i,7.92,24,1]),['_'].map(i=> [i,12.13,24,1]),['-'].map(i=> [i,7.53,24,1]),['['].map(i=> [i,7.72,24,1]),[']'].map(i=> [i,7.77,24,1]),['℃'].map(i=> [i,20.33,24,1])],"STKaiti26.7":[['我','》','（','⭘'].map(i=> [i,26.7,27,1]),['！','？','《','；','，','、','。','）','：','【','】','⊙'].map(i=> [i,26.72,27,1]),[' '].map(i=> [i,13.35,27,1]),['0','2','4','5','7','9'].map(i=> [i,12.53,27,1]),['1','3','6','8'].map(i=> [i,12.55,27,1]),['h'].map(i=> [i,14.22,27,1]),['a','o'].map(i=> [i,13.66,27,1]),['w','<','>','~','#','+'].map(i=> [i,17.83,27,1]),['*'].map(i=> [i,11.42,27,1]),['G'].map(i=> [i,20.59,27,1]),['I'].map(i=> [i,9.47,27,1]),['J','`'].map(i=> [i,8.91,27,1]),['}'].map(i=> [i,12.8,27,1]),['T'].map(i=> [i,16.44,27,1]),[',','.',':','!',';'].map(i=> [i,5.86,27,1]),['\"'].map(i=> [i,10.86,27,1]),['='].map(i=> [i,17.81,27,1]),['@'].map(i=> [i,24.5,27,1]),['$'].map(i=> [i,11.98,27,1]),['%'].map(i=> [i,21.98,27,1]),['{'].map(i=> [i,12.81,27,1]),['l'].map(i=> [i,7.25,27,1]),['/','^','\\','|'].map(i=> [i,13.36,27,1]),['☐'].map(i=> [i,22.19,27,1]),['☑'].map(i=> [i,22.17,27,1]),['↵'].map(i=> [i,0,27,1]),['“','”'].map(i=> [i,10.8,27,1]),['‘'].map(i=> [i,6.64,27,1]),['’'].map(i=> [i,6.67,27,1]),['b'].map(i=> [i,13.89,27,1]),['d'].map(i=> [i,13.77,27,1]),['c'].map(i=> [i,11.3,27,1]),['e'].map(i=> [i,11.47,27,1]),['f'].map(i=> [i,9,27,1]),['g'].map(i=> [i,12.84,27,1]),['i'].map(i=> [i,7.44,27,1]),['j'].map(i=> [i,6.2,27,1]),['k'].map(i=> [i,13.88,27,1]),['m'].map(i=> [i,20.92,27,1]),['n'].map(i=> [i,14.2,27,1]),['p'].map(i=> [i,13.97,27,1]),['q'].map(i=> [i,13.94,27,1]),['r'].map(i=> [i,10.38,27,1]),['s'].map(i=> [i,10.27,27,1]),['t'].map(i=> [i,9.5,27,1]),['u'].map(i=> [i,14.14,27,1]),['v'].map(i=> [i,12.58,27,1]),['x'].map(i=> [i,12.73,27,1]),['y'].map(i=> [i,12.27,27,1]),['z'].map(i=> [i,11.55,27,1]),['A'].map(i=> [i,18.69,27,1]),['B'].map(i=> [i,17.16,27,1]),['C'].map(i=> [i,17.2,27,1]),['D'].map(i=> [i,20.66,27,1]),['E'].map(i=> [i,17.69,27,1]),['F'].map(i=> [i,15.3,27,1]),['H'].map(i=> [i,21.56,27,1]),['K'].map(i=> [i,19.86,27,1]),['L'].map(i=> [i,15.7,27,1]),['M'].map(i=> [i,24.86,27,1]),['N'].map(i=> [i,20.75,27,1]),['O'].map(i=> [i,20.73,27,1]),['P'].map(i=> [i,15.33,27,1]),['Q'].map(i=> [i,20.48,27,1]),['R'].map(i=> [i,18.58,27,1]),['S'].map(i=> [i,12.94,27,1]),['U'].map(i=> [i,19.08,27,1]),['V'].map(i=> [i,18.33,27,1]),['W'].map(i=> [i,23.91,27,1]),['X'].map(i=> [i,18.81,27,1]),['Y'].map(i=> [i,17.97,27,1]),['Z'].map(i=> [i,17.64,27,1]),['?'].map(i=> [i,9.77,27,1]),['&'].map(i=> [i,19.48,27,1]),['('].map(i=> [i,9.06,27,1]),[')'].map(i=> [i,8.8,27,1]),['_'].map(i=> [i,13.5,27,1]),['-'].map(i=> [i,8.36,27,1]),['['].map(i=> [i,8.59,27,1]),[']'].map(i=> [i,8.64,27,1]),['\''].map(i=> [i,4.75,27,1]),['◯'].map(i=> [i,25.48,27,1]),['℃'].map(i=> [i,22.61,27,1])],"STKaiti29.3":[['我','！','？','《','》','，','、','。','（','：','【','】','⊙','⭘'].map(i=> [i,29.31,29,1]),['；','）'].map(i=> [i,29.33,29,1]),['0','3','5','7','9'].map(i=> [i,13.77,29,1]),['1','2','4','6','8'].map(i=> [i,13.75,29,1]),['z'].map(i=> [i,12.69,29,1]),['h'].map(i=> [i,15.59,29,1]),['p'].map(i=> [i,15.34,29,1]),['?'].map(i=> [i,10.7,29,1]),['w','<','>','=','#','+'].map(i=> [i,19.56,29,1]),['G'].map(i=> [i,22.61,29,1]),['I'].map(i=> [i,10.39,29,1]),['J','`'].map(i=> [i,9.78,29,1]),['{','}'].map(i=> [i,14.05,29,1]),['.',':'].map(i=> [i,6.42,29,1]),[',','!',';'].map(i=> [i,6.44,29,1]),['/','^','|'].map(i=> [i,14.67,29,1]),['~'].map(i=> [i,19.55,29,1]),['$'].map(i=> [i,13.14,29,1]),['%'].map(i=> [i,24.13,29,1]),['&'].map(i=> [i,21.38,29,1]),['-'].map(i=> [i,9.19,29,1]),['l'].map(i=> [i,7.95,29,1]),['\\'].map(i=> [i,14.66,29,1]),['\''].map(i=> [i,5.2,29,1]),['☑'].map(i=> [i,24.33,29,1]),['☐'].map(i=> [i,24.34,29,1]),['◯'].map(i=> [i,27.97,29,1]),['↵'].map(i=> [i,0,29,1]),['“'].map(i=> [i,11.86,29,1]),['”'].map(i=> [i,11.84,29,1]),['‘'].map(i=> [i,7.28,29,1]),['’'].map(i=> [i,7.31,29,1]),[' '].map(i=> [i,19.65,29,1]),['a','o'].map(i=> [i,14.98,29,1]),['b'].map(i=> [i,15.25,29,1]),['d'].map(i=> [i,15.11,29,1]),['c'].map(i=> [i,12.38,29,1]),['e'].map(i=> [i,12.59,29,1]),['f'].map(i=> [i,9.88,29,1]),['g'].map(i=> [i,14.09,29,1]),['i'].map(i=> [i,8.16,29,1]),['j'].map(i=> [i,6.83,29,1]),['k'].map(i=> [i,15.22,29,1]),['m'].map(i=> [i,22.95,29,1]),['n'].map(i=> [i,15.58,29,1]),['q'].map(i=> [i,15.28,29,1]),['r'].map(i=> [i,11.38,29,1]),['s'].map(i=> [i,11.28,29,1]),['t'].map(i=> [i,10.41,29,1]),['u'].map(i=> [i,15.52,29,1]),['v'].map(i=> [i,13.81,29,1]),['x'].map(i=> [i,13.97,29,1]),['y'].map(i=> [i,13.45,29,1]),['A'].map(i=> [i,20.48,29,1]),['B'].map(i=> [i,18.83,29,1]),['C'].map(i=> [i,18.89,29,1]),['D'].map(i=> [i,22.66,29,1]),['E'].map(i=> [i,19.42,29,1]),['F'].map(i=> [i,16.77,29,1]),['H'].map(i=> [i,23.66,29,1]),['K'].map(i=> [i,21.78,29,1]),['L'].map(i=> [i,17.25,29,1]),['M'].map(i=> [i,27.27,29,1]),['N'].map(i=> [i,22.78,29,1]),['O'].map(i=> [i,22.75,29,1]),['P'].map(i=> [i,16.8,29,1]),['Q'].map(i=> [i,22.48,29,1]),['R'].map(i=> [i,20.39,29,1]),['S'].map(i=> [i,14.19,29,1]),['T'].map(i=> [i,18.05,29,1]),['U'].map(i=> [i,20.92,29,1]),['V'].map(i=> [i,20.13,29,1]),['W'].map(i=> [i,26.23,29,1]),['X'].map(i=> [i,20.64,29,1]),['Y'].map(i=> [i,19.7,29,1]),['Z'].map(i=> [i,19.36,29,1]),['\"'].map(i=> [i,11.92,29,1]),['@'].map(i=> [i,26.88,29,1]),['*'].map(i=> [i,12.53,29,1]),['('].map(i=> [i,9.94,29,1]),[')'].map(i=> [i,9.66,29,1]),['_'].map(i=> [i,14.81,29,1]),['['].map(i=> [i,9.42,29,1]),[']'].map(i=> [i,9.48,29,1]),['℃'].map(i=> [i,24.8,29,1])],"STKaiti32":[['\"'].map(i=> [i,13,32,1]),['↵'].map(i=> [i,0,32,1]),['我','！','？','《','》','；','，','、','。','（','）','：','【','】'].map(i=> [i,32,32,1]),[' '].map(i=> [i,16,32,1]),['1','3','5','7','9'].map(i=> [i,15.03,32,1]),['0','2','4','6','8'].map(i=> [i,15.02,32,1]),['h'].map(i=> [i,17.05,32,1]),['a'].map(i=> [i,16.36,32,1]),['p'].map(i=> [i,16.75,32,1]),['?'].map(i=> [i,11.7,32,1]),['w','<','>','=','~','#','+'].map(i=> [i,21.36,32,1]),['*'].map(i=> [i,13.69,32,1]),['G'].map(i=> [i,24.69,32,1]),['I'].map(i=> [i,11.34,32,1]),['J','`'].map(i=> [i,10.67,32,1]),['P'].map(i=> [i,18.34,32,1]),['{','}'].map(i=> [i,15.34,32,1]),['T'].map(i=> [i,19.69,32,1]),['.','!'].map(i=> [i,7.02,32,1]),[',',':',';'].map(i=> [i,7.03,32,1]),['/','^','\\','|'].map(i=> [i,16.02,32,1]),['@'].map(i=> [i,29.36,32,1]),['$'].map(i=> [i,14.36,32,1]),['%'].map(i=> [i,26.34,32,1]),['&'].map(i=> [i,23.34,32,1]),['-'].map(i=> [i,10.03,32,1]),['l'].map(i=> [i,8.69,32,1]),['\''].map(i=> [i,5.67,32,1]),['☑','☐'].map(i=> [i,26.58,32,1]),['⊙','⭘'].map(i=> [i,32.02,32,1]),['◯'].map(i=> [i,30.53,32,1]),['“','”'].map(i=> [i,12.94,32,1]),['‘'].map(i=> [i,7.95,32,1]),['’'].map(i=> [i,7.98,32,1]),['b'].map(i=> [i,16.66,32,1]),['d'].map(i=> [i,16.5,32,1]),['c'].map(i=> [i,13.52,32,1]),['e'].map(i=> [i,13.75,32,1]),['f'].map(i=> [i,10.8,32,1]),['g'].map(i=> [i,15.38,32,1]),['i'].map(i=> [i,8.91,32,1]),['j'].map(i=> [i,7.44,32,1]),['k'].map(i=> [i,16.63,32,1]),['m'].map(i=> [i,25.08,32,1]),['n'].map(i=> [i,17,32,1]),['o'].map(i=> [i,16.38,32,1]),['q'].map(i=> [i,16.69,32,1]),['r'].map(i=> [i,12.42,32,1]),['s'].map(i=> [i,12.31,32,1]),['t'].map(i=> [i,11.38,32,1]),['u'].map(i=> [i,16.94,32,1]),['v'].map(i=> [i,15.09,32,1]),['x'].map(i=> [i,15.25,32,1]),['y'].map(i=> [i,14.7,32,1]),['z'].map(i=> [i,13.83,32,1]),['A'].map(i=> [i,22.39,32,1]),['B'].map(i=> [i,20.56,32,1]),['C'].map(i=> [i,20.63,32,1]),['D'].map(i=> [i,24.75,32,1]),['E'].map(i=> [i,21.2,32,1]),['F'].map(i=> [i,18.31,32,1]),['H'].map(i=> [i,25.84,32,1]),['K'].map(i=> [i,23.78,32,1]),['L'].map(i=> [i,18.84,32,1]),['M'].map(i=> [i,29.77,32,1]),['N'].map(i=> [i,24.89,32,1]),['O'].map(i=> [i,24.84,32,1]),['Q'].map(i=> [i,24.56,32,1]),['R'].map(i=> [i,22.27,32,1]),['S'].map(i=> [i,15.5,32,1]),['U'].map(i=> [i,22.88,32,1]),['V'].map(i=> [i,21.95,32,1]),['W'].map(i=> [i,28.66,32,1]),['X'].map(i=> [i,22.55,32,1]),['Y'].map(i=> [i,21.52,32,1]),['Z'].map(i=> [i,21.14,32,1]),['('].map(i=> [i,10.86,32,1]),[')'].map(i=> [i,10.55,32,1]),['_'].map(i=> [i,16.17,32,1]),['['].map(i=> [i,10.28,32,1]),[']'].map(i=> [i,10.36,32,1]),['℃'].map(i=> [i,27.09,32,1])],"STKaiti34.7":[['我','》','（','）'].map(i=> [i,34.7,35,1]),['！','？','《','；','，','、','。','：','【','】','⊙','⭘'].map(i=> [i,34.72,35,1]),[' '].map(i=> [i,17.35,35,1]),['0','2','4','5','7','9'].map(i=> [i,16.3,35,1]),['1','3','6','8'].map(i=> [i,16.28,35,1]),['z'].map(i=> [i,15,35,1]),['h'].map(i=> [i,18.47,35,1]),['a','o'].map(i=> [i,17.75,35,1]),['p'].map(i=> [i,18.17,35,1]),['w','<','>','~'].map(i=> [i,23.16,35,1]),['*'].map(i=> [i,14.83,35,1]),['T'].map(i=> [i,21.36,35,1]),['G'].map(i=> [i,26.77,35,1]),['I'].map(i=> [i,12.3,35,1]),['J','`'].map(i=> [i,11.58,35,1]),['}'].map(i=> [i,16.64,35,1]),['.',':','!',';'].map(i=> [i,7.61,35,1]),[','].map(i=> [i,7.63,35,1]),['/','|'].map(i=> [i,17.36,35,1]),['?'].map(i=> [i,12.69,35,1]),['=','#','+'].map(i=> [i,23.17,35,1]),['@'].map(i=> [i,31.83,35,1]),['^','\\'].map(i=> [i,17.38,35,1]),['&'].map(i=> [i,25.31,35,1]),['-'].map(i=> [i,10.88,35,1]),['{'].map(i=> [i,16.63,35,1]),['l'].map(i=> [i,9.42,35,1]),['\''].map(i=> [i,6.16,35,1]),['☐'].map(i=> [i,28.81,35,1]),['◯'].map(i=> [i,33.11,35,1]),['↵'].map(i=> [i,0,35,1]),['“','”'].map(i=> [i,14.03,35,1]),['‘'].map(i=> [i,8.63,35,1]),['’'].map(i=> [i,8.66,35,1]),['b'].map(i=> [i,18.06,35,1]),['d'].map(i=> [i,17.88,35,1]),['c'].map(i=> [i,14.66,35,1]),['e'].map(i=> [i,14.91,35,1]),['f'].map(i=> [i,11.72,35,1]),['g'].map(i=> [i,16.67,35,1]),['i'].map(i=> [i,9.66,35,1]),['j'].map(i=> [i,8.08,35,1]),['k'].map(i=> [i,18.02,35,1]),['m'].map(i=> [i,27.19,35,1]),['n'].map(i=> [i,18.44,35,1]),['q'].map(i=> [i,18.09,35,1]),['r'].map(i=> [i,13.47,35,1]),['s'].map(i=> [i,13.34,35,1]),['t'].map(i=> [i,12.33,35,1]),['u'].map(i=> [i,18.38,35,1]),['v'].map(i=> [i,16.36,35,1]),['x'].map(i=> [i,16.55,35,1]),['y'].map(i=> [i,15.94,35,1]),['A'].map(i=> [i,24.28,35,1]),['B'].map(i=> [i,22.28,35,1]),['C'].map(i=> [i,22.38,35,1]),['D'].map(i=> [i,26.83,35,1]),['E'].map(i=> [i,22.98,35,1]),['F'].map(i=> [i,19.88,35,1]),['H'].map(i=> [i,28.02,35,1]),['K'].map(i=> [i,25.8,35,1]),['L'].map(i=> [i,20.42,35,1]),['M'].map(i=> [i,32.28,35,1]),['N'].map(i=> [i,26.98,35,1]),['O'].map(i=> [i,26.94,35,1]),['P'].map(i=> [i,19.89,35,1]),['Q'].map(i=> [i,26.63,35,1]),['R'].map(i=> [i,24.13,35,1]),['S'].map(i=> [i,16.81,35,1]),['U'].map(i=> [i,24.8,35,1]),['V'].map(i=> [i,23.81,35,1]),['W'].map(i=> [i,31.08,35,1]),['X'].map(i=> [i,24.44,35,1]),['Y'].map(i=> [i,23.34,35,1]),['Z'].map(i=> [i,22.91,35,1]),['\"'].map(i=> [i,14.11,35,1]),['$'].map(i=> [i,15.56,35,1]),['%'].map(i=> [i,28.56,35,1]),['('].map(i=> [i,11.78,35,1]),[')'].map(i=> [i,11.42,35,1]),['_'].map(i=> [i,17.55,35,1]),['['].map(i=> [i,11.16,35,1]),[']'].map(i=> [i,11.22,35,1]),['☑'].map(i=> [i,28.83,35,1]),['℃'].map(i=> [i,29.36,35,1])],"STKaiti37.3":[['我','！','？','《','》','，','、','。','（','：','【','】','⭘'].map(i=> [i,37.31,37,1]),['；','）','⊙'].map(i=> [i,37.33,37,1]),[' '].map(i=> [i,18.65,37,1]),['2','4','7','9'].map(i=> [i,17.5,37,1]),['0','1','3','5','6','8'].map(i=> [i,17.52,37,1]),['z'].map(i=> [i,16.13,37,1]),['h'].map(i=> [i,19.86,37,1]),['a'].map(i=> [i,19.08,37,1]),['p'].map(i=> [i,19.53,37,1]),['w','<'].map(i=> [i,24.91,37,1]),['T'].map(i=> [i,22.95,37,1]),['G'].map(i=> [i,28.77,37,1]),['I'].map(i=> [i,13.22,37,1]),['J','`'].map(i=> [i,12.44,37,1]),['P'].map(i=> [i,21.39,37,1]),['}'].map(i=> [i,17.88,37,1]),[',',':','!',';'].map(i=> [i,8.19,37,1]),['^'].map(i=> [i,18.66,37,1]),['\"'].map(i=> [i,15.16,37,1]),['>','=','~','#','+'].map(i=> [i,24.89,37,1]),['.'].map(i=> [i,8.17,37,1]),['@'].map(i=> [i,34.22,37,1]),['%'].map(i=> [i,30.72,37,1]),['-'].map(i=> [i,11.69,37,1]),['{'].map(i=> [i,17.89,37,1]),['l'].map(i=> [i,10.13,37,1]),['/','\\','|'].map(i=> [i,18.67,37,1]),['\''].map(i=> [i,6.61,37,1]),['☑'].map(i=> [i,30.98,37,1]),['☐'].map(i=> [i,30.97,37,1]),['◯'].map(i=> [i,35.58,37,1]),['↵'].map(i=> [i,0,37,1]),['s'].map(i=> [i,14.34,37,1]),['t'].map(i=> [i,13.25,37,1]),['q'].map(i=> [i,19.45,37,1]),['_'].map(i=> [i,18.86,37,1]),['d'].map(i=> [i,19.23,37,1]),['“'].map(i=> [i,15.09,37,1]),['”'].map(i=> [i,15.08,37,1]),['‘'].map(i=> [i,9.27,37,1]),['’'].map(i=> [i,9.31,37,1]),['b'].map(i=> [i,19.42,37,1]),['c'].map(i=> [i,15.75,37,1]),['e'].map(i=> [i,16.02,37,1]),['f'].map(i=> [i,12.59,37,1]),['g'].map(i=> [i,17.91,37,1]),['i'].map(i=> [i,10.39,37,1]),['j'].map(i=> [i,8.67,37,1]),['k'].map(i=> [i,19.38,37,1]),['m'].map(i=> [i,29.22,37,1]),['n'].map(i=> [i,19.83,37,1]),['o'].map(i=> [i,19.06,37,1]),['r'].map(i=> [i,14.48,37,1]),['u'].map(i=> [i,19.75,37,1]),['v'].map(i=> [i,17.58,37,1]),['x'].map(i=> [i,17.77,37,1]),['y'].map(i=> [i,17.14,37,1]),['A'].map(i=> [i,26.08,37,1]),['B'].map(i=> [i,23.97,37,1]),['C'].map(i=> [i,24.03,37,1]),['D'].map(i=> [i,28.86,37,1]),['E'].map(i=> [i,24.7,37,1]),['F'].map(i=> [i,21.36,37,1]),['H'].map(i=> [i,30.13,37,1]),['K'].map(i=> [i,27.72,37,1]),['L'].map(i=> [i,21.95,37,1]),['M'].map(i=> [i,34.7,37,1]),['N'].map(i=> [i,29,37,1]),['O'].map(i=> [i,28.95,37,1]),['Q'].map(i=> [i,28.63,37,1]),['R'].map(i=> [i,25.94,37,1]),['S'].map(i=> [i,18.08,37,1]),['U'].map(i=> [i,26.64,37,1]),['V'].map(i=> [i,25.61,37,1]),['W'].map(i=> [i,33.39,37,1]),['X'].map(i=> [i,26.28,37,1]),['Y'].map(i=> [i,25.08,37,1]),['Z'].map(i=> [i,24.64,37,1]),['?'].map(i=> [i,13.63,37,1]),['$'].map(i=> [i,16.73,37,1]),['&'].map(i=> [i,27.2,37,1]),['*'].map(i=> [i,15.95,37,1]),['('].map(i=> [i,12.66,37,1]),[')'].map(i=> [i,12.28,37,1]),['['].map(i=> [i,11.98,37,1]),[']'].map(i=> [i,12.06,37,1]),['℃'].map(i=> [i,31.58,37,1])],"STKaiti48":[['我','！','？','《','》','；','，','、','。','（','）','：','【','】'].map(i=> [i,48,48,1]),[' '].map(i=> [i,24,48,1]),['3','7'].map(i=> [i,22.52,48,1]),['0','1','2','4','5','6','8','9'].map(i=> [i,22.53,48,1]),['z'].map(i=> [i,20.75,48,1]),['h'].map(i=> [i,25.55,48,1]),['p'].map(i=> [i,25.13,48,1]),['?'].map(i=> [i,17.53,48,1]),['w','<','>','=','~','#','+'].map(i=> [i,32.03,48,1]),['*'].map(i=> [i,20.52,48,1]),['J','`'].map(i=> [i,16,48,1]),['P'].map(i=> [i,27.52,48,1]),['G'].map(i=> [i,37.03,48,1]),['{'].map(i=> [i,23,48,1]),['T'].map(i=> [i,29.55,48,1]),[',',':'].map(i=> [i,10.52,48,1]),['.','!',';'].map(i=> [i,10.53,48,1]),['/','^','\\','|'].map(i=> [i,24.02,48,1]),['\"'].map(i=> [i,19.52,48,1]),['@'].map(i=> [i,44.03,48,1]),['&'].map(i=> [i,35,48,1]),['}'].map(i=> [i,23.02,48,1]),['l'].map(i=> [i,13.03,48,1]),['\''].map(i=> [i,8.52,48,1]),['☑','☐'].map(i=> [i,39.86,48,1]),['⊙','⭘'].map(i=> [i,48.02,48,1]),['↵'].map(i=> [i,0,48,1]),['“','”'].map(i=> [i,19.41,48,1]),['‘'].map(i=> [i,11.92,48,1]),['’'].map(i=> [i,11.95,48,1]),['a','o'].map(i=> [i,24.55,48,1]),['b'].map(i=> [i,24.98,48,1]),['d'].map(i=> [i,24.73,48,1]),['c'].map(i=> [i,20.27,48,1]),['e'].map(i=> [i,20.61,48,1]),['f'].map(i=> [i,16.19,48,1]),['g'].map(i=> [i,23.06,48,1]),['i'].map(i=> [i,13.36,48,1]),['j'].map(i=> [i,11.16,48,1]),['k'].map(i=> [i,24.92,48,1]),['m'].map(i=> [i,37.59,48,1]),['n'].map(i=> [i,25.5,48,1]),['q'].map(i=> [i,25.02,48,1]),['r'].map(i=> [i,18.64,48,1]),['s'].map(i=> [i,18.45,48,1]),['t'].map(i=> [i,17.05,48,1]),['u'].map(i=> [i,25.42,48,1]),['v'].map(i=> [i,22.61,48,1]),['x'].map(i=> [i,22.88,48,1]),['y'].map(i=> [i,22.05,48,1]),['A'].map(i=> [i,33.56,48,1]),['B'].map(i=> [i,30.83,48,1]),['C'].map(i=> [i,30.94,48,1]),['D'].map(i=> [i,37.11,48,1]),['E'].map(i=> [i,31.8,48,1]),['F'].map(i=> [i,27.47,48,1]),['H'].map(i=> [i,38.75,48,1]),['I'].map(i=> [i,17,48,1]),['K'].map(i=> [i,35.69,48,1]),['L'].map(i=> [i,28.23,48,1]),['M'].map(i=> [i,44.66,48,1]),['N'].map(i=> [i,37.31,48,1]),['O'].map(i=> [i,37.27,48,1]),['Q'].map(i=> [i,36.83,48,1]),['R'].map(i=> [i,33.38,48,1]),['S'].map(i=> [i,23.23,48,1]),['U'].map(i=> [i,34.28,48,1]),['V'].map(i=> [i,32.94,48,1]),['W'].map(i=> [i,42.98,48,1]),['X'].map(i=> [i,33.81,48,1]),['Y'].map(i=> [i,32.27,48,1]),['Z'].map(i=> [i,31.69,48,1]),['$'].map(i=> [i,21.52,48,1]),['%'].map(i=> [i,39.53,48,1]),['('].map(i=> [i,16.28,48,1]),[')'].map(i=> [i,15.81,48,1]),['_'].map(i=> [i,24.25,48,1]),['-'].map(i=> [i,15.05,48,1]),['['].map(i=> [i,15.42,48,1]),[']'].map(i=> [i,15.52,48,1]),['◯'].map(i=> [i,45.78,48,1]),['℃'].map(i=> [i,40.63,48,1])],"STKaiti58.7":[['我','》','；','）'].map(i=> [i,58.7,58,1]),['！','？','《','，','、','。','（','：','【','】','⊙','⭘'].map(i=> [i,58.72,58,1]),[' '].map(i=> [i,29.35,58,1]),['8','9'].map(i=> [i,27.53,58,1]),['0','1','2','3','4','5','6','7'].map(i=> [i,27.55,58,1]),['z'].map(i=> [i,25.38,58,1]),['a','o'].map(i=> [i,30.02,58,1]),['p'].map(i=> [i,30.72,58,1]),['?'].map(i=> [i,21.44,58,1]),['<','>','=','#','+'].map(i=> [i,39.17,58,1]),['*'].map(i=> [i,25.08,58,1]),['T'].map(i=> [i,36.13,58,1]),['G'].map(i=> [i,45.28,58,1]),['I'].map(i=> [i,20.8,58,1]),['J','`'].map(i=> [i,19.56,58,1]),['{'].map(i=> [i,28.14,58,1]),[',','.','!',';'].map(i=> [i,12.88,58,1]),['/','^','\\','|'].map(i=> [i,29.36,58,1]),['\"'].map(i=> [i,23.86,58,1]),[':'].map(i=> [i,12.86,58,1]),['w','~'].map(i=> [i,39.16,58,1]),['@'].map(i=> [i,53.84,58,1]),['$'].map(i=> [i,26.31,58,1]),['%'].map(i=> [i,48.33,58,1]),['&'].map(i=> [i,42.81,58,1]),['}'].map(i=> [i,28.13,58,1]),['l'].map(i=> [i,15.92,58,1]),['\''].map(i=> [i,10.41,58,1]),['☐'].map(i=> [i,48.73,58,1]),['☑'].map(i=> [i,48.75,58,1]),['↵'].map(i=> [i,0,58,1]),['“'].map(i=> [i,23.72,58,1]),['”'].map(i=> [i,23.73,58,1]),['‘'].map(i=> [i,14.58,58,1]),['’'].map(i=> [i,14.63,58,1]),['b'].map(i=> [i,30.55,58,1]),['d'].map(i=> [i,30.25,58,1]),['c'].map(i=> [i,24.78,58,1]),['e'].map(i=> [i,25.2,58,1]),['f'].map(i=> [i,19.8,58,1]),['g'].map(i=> [i,28.19,58,1]),['h'].map(i=> [i,31.25,58,1]),['i'].map(i=> [i,16.33,58,1]),['j'].map(i=> [i,13.64,58,1]),['k'].map(i=> [i,30.48,58,1]),['m'].map(i=> [i,45.97,58,1]),['n'].map(i=> [i,31.19,58,1]),['q'].map(i=> [i,30.59,58,1]),['r'].map(i=> [i,22.8,58,1]),['s'].map(i=> [i,22.55,58,1]),['t'].map(i=> [i,20.86,58,1]),['u'].map(i=> [i,31.06,58,1]),['v'].map(i=> [i,27.67,58,1]),['x'].map(i=> [i,27.97,58,1]),['y'].map(i=> [i,26.95,58,1]),['A'].map(i=> [i,41.05,58,1]),['B'].map(i=> [i,37.7,58,1]),['C'].map(i=> [i,37.81,58,1]),['D'].map(i=> [i,45.39,58,1]),['E'].map(i=> [i,38.88,58,1]),['F'].map(i=> [i,33.59,58,1]),['H'].map(i=> [i,47.38,58,1]),['K'].map(i=> [i,43.64,58,1]),['L'].map(i=> [i,34.53,58,1]),['M'].map(i=> [i,54.59,58,1]),['N'].map(i=> [i,45.63,58,1]),['O'].map(i=> [i,45.58,58,1]),['P'].map(i=> [i,33.64,58,1]),['Q'].map(i=> [i,45.05,58,1]),['R'].map(i=> [i,40.81,58,1]),['S'].map(i=> [i,28.42,58,1]),['U'].map(i=> [i,41.92,58,1]),['V'].map(i=> [i,40.28,58,1]),['W'].map(i=> [i,52.55,58,1]),['X'].map(i=> [i,41.34,58,1]),['Y'].map(i=> [i,39.47,58,1]),['Z'].map(i=> [i,38.75,58,1]),['('].map(i=> [i,19.92,58,1]),[')'].map(i=> [i,19.33,58,1]),['_'].map(i=> [i,29.66,58,1]),['-'].map(i=> [i,18.38,58,1]),['['].map(i=> [i,18.86,58,1]),[']'].map(i=> [i,18.98,58,1]),['◯'].map(i=> [i,55.98,58,1]),['℃'].map(i=> [i,49.69,58,1])],"Hiragino Sans GB10.7":[['我','》','（','“','⊙'].map(i=> [i,10.7,10,1]),['！','？','《','；','，','、','。','）','：','【','】','”','‘','’','⭘','℃'].map(i=> [i,10.72,10,1]),[' '].map(i=> [i,5.35,10,1]),['<'].map(i=> [i,5.5,10,1]),['>'].map(i=> [i,5.48,10,1]),['f'].map(i=> [i,4.47,10,1]),['w'].map(i=> [i,8.27,10,1]),['5','#'].map(i=> [i,7.03,10,1]),['o'].map(i=> [i,6.7,10,1]),['K'].map(i=> [i,7.58,10,1]),['`','_'].map(i=> [i,5.38,10,1]),['☐'].map(i=> [i,8.91,10,1]),['☑'].map(i=> [i,8.89,10,1]),['◯'].map(i=> [i,10.22,10,1]),['↵'].map(i=> [i,0,10,1]),['I'].map(i=> [i,2.67,10,1]),['E'].map(i=> [i,6.89,10,1]),['U'].map(i=> [i,8.28,10,1]),['P'].map(i=> [i,7.11,10,1]),['?'].map(i=> [i,6.13,10,1]),['e'].map(i=> [i,6.31,10,1]),['m'].map(i=> [i,9.97,10,1]),['0','1','2','3','6','7','8','9','=','+'].map(i=> [i,7.05,10,1]),['4'].map(i=> [i,7.09,10,1]),['a','k'].map(i=> [i,6.17,10,1]),['b','p'].map(i=> [i,6.94,10,1]),['d','q'].map(i=> [i,6.92,10,1]),['c'].map(i=> [i,6.2,10,1]),['g'].map(i=> [i,6.88,10,1]),['h','u'].map(i=> [i,6.63,10,1]),['i'].map(i=> [i,2.56,10,1]),['j'].map(i=> [i,2.84,10,1]),['l'].map(i=> [i,2.5,10,1]),['n'].map(i=> [i,6.64,10,1]),['r'].map(i=> [i,4.63,10,1]),['s','x'].map(i=> [i,5.75,10,1]),['t'].map(i=> [i,4.22,10,1]),['v'].map(i=> [i,5.83,10,1]),['y'].map(i=> [i,5.94,10,1]),['z'].map(i=> [i,5.34,10,1]),['A'].map(i=> [i,8.05,10,1]),['B'].map(i=> [i,7.63,10,1]),['C'].map(i=> [i,8.11,10,1]),['D'].map(i=> [i,8.13,10,1]),['F','L'].map(i=> [i,6.47,10,1]),['G'].map(i=> [i,8.08,10,1]),['H'].map(i=> [i,8.34,10,1]),['J'].map(i=> [i,5.45,10,1]),['M'].map(i=> [i,10.13,10,1]),['N'].map(i=> [i,8.3,10,1]),['O','Q'].map(i=> [i,8.52,10,1]),['R'].map(i=> [i,7.78,10,1]),['S','Z'].map(i=> [i,7.17,10,1]),['T'].map(i=> [i,6.97,10,1]),['V'].map(i=> [i,7.69,10,1]),['W'].map(i=> [i,10.78,10,1]),['X'].map(i=> [i,7.73,10,1]),['Y'].map(i=> [i,7.39,10,1]),[',','.',':',';'].map(i=> [i,2.58,10,1]),['/'].map(i=> [i,5.09,10,1]),['\"','(',')'].map(i=> [i,3.83,10,1]),['~'].map(i=> [i,5.81,10,1]),['!'].map(i=> [i,3.34,10,1]),['@'].map(i=> [i,9.7,10,1]),['$'].map(i=> [i,6.98,10,1]),['%'].map(i=> [i,9.42,10,1]),['^'].map(i=> [i,5.72,10,1]),['&'].map(i=> [i,8.53,10,1]),['*','{','[',']'].map(i=> [i,3.72,10,1]),['-'].map(i=> [i,3.84,10,1]),['}'].map(i=> [i,3.73,10,1]),['\\'].map(i=> [i,5.08,10,1]),['|'].map(i=> [i,4.03,10,1]),['\''].map(i=> [i,2.11,10,1])],"Hiragino Sans GB12":[['*',']'].map(i=> [i,4.16,12,1]),['_'].map(i=> [i,6.03,12,1]),['↵'].map(i=> [i,0,12,1]),['我','！','？','《','》','；','，','、','。','（','）','：','【','】','“','”','‘','’'].map(i=> [i,12,12,1]),['.'].map(i=> [i,2.91,12,1]),[' '].map(i=> [i,6,12,1]),['>'].map(i=> [i,6.17,12,1]),['^'].map(i=> [i,6.41,12,1]),['f'].map(i=> [i,5.02,12,1]),['0','3','5','7','+'].map(i=> [i,7.89,12,1]),['D'].map(i=> [i,9.13,12,1]),['U'].map(i=> [i,9.28,12,1]),['o'].map(i=> [i,7.52,12,1]),['K'].map(i=> [i,8.5,12,1]),['`'].map(i=> [i,6.02,12,1]),['<'].map(i=> [i,6.16,12,1]),['☑'].map(i=> [i,9.98,12,1]),['☐'].map(i=> [i,9.97,12,1]),['⊙','⭘','℃'].map(i=> [i,12.02,12,1]),['l'].map(i=> [i,2.8,12,1]),[')'].map(i=> [i,4.28,12,1]),['E'].map(i=> [i,7.72,12,1]),['N'].map(i=> [i,9.3,12,1]),['4','P'].map(i=> [i,7.95,12,1]),['m'].map(i=> [i,11.19,12,1]),['1','2','6','8','9','#'].map(i=> [i,7.91,12,1]),['a','k'].map(i=> [i,6.91,12,1]),['b','d','q'].map(i=> [i,7.77,12,1]),['c'].map(i=> [i,6.95,12,1]),['e'].map(i=> [i,7.08,12,1]),['g'].map(i=> [i,7.7,12,1]),['h','u'].map(i=> [i,7.42,12,1]),['i'].map(i=> [i,2.88,12,1]),['j'].map(i=> [i,3.2,12,1]),['n'].map(i=> [i,7.44,12,1]),['p'].map(i=> [i,7.78,12,1]),['r'].map(i=> [i,5.17,12,1]),['s'].map(i=> [i,6.47,12,1]),['t'].map(i=> [i,4.72,12,1]),['v'].map(i=> [i,6.55,12,1]),['w'].map(i=> [i,9.25,12,1]),['x'].map(i=> [i,6.45,12,1]),['y'].map(i=> [i,6.67,12,1]),['z'].map(i=> [i,5.98,12,1]),['A'].map(i=> [i,9.02,12,1]),['B'].map(i=> [i,8.56,12,1]),['C'].map(i=> [i,9.08,12,1]),['F'].map(i=> [i,7.25,12,1]),['G'].map(i=> [i,9.06,12,1]),['H'].map(i=> [i,9.34,12,1]),['I'].map(i=> [i,3.02,12,1]),['J'].map(i=> [i,6.11,12,1]),['L'].map(i=> [i,7.23,12,1]),['M'].map(i=> [i,11.36,12,1]),['O','Q'].map(i=> [i,9.56,12,1]),['R'].map(i=> [i,8.72,12,1]),['S','Z'].map(i=> [i,8.05,12,1]),['T','$'].map(i=> [i,7.81,12,1]),['V'].map(i=> [i,8.63,12,1]),['W'].map(i=> [i,12.08,12,1]),['X'].map(i=> [i,8.69,12,1]),['Y'].map(i=> [i,8.27,12,1]),[',',':',';'].map(i=> [i,2.89,12,1]),['/','\\'].map(i=> [i,5.7,12,1]),['?'].map(i=> [i,6.86,12,1]),['\"','('].map(i=> [i,4.3,12,1]),['='].map(i=> [i,7.88,12,1]),['~'].map(i=> [i,6.53,12,1]),['!'].map(i=> [i,3.75,12,1]),['@'].map(i=> [i,10.88,12,1]),['%'].map(i=> [i,10.56,12,1]),['&'].map(i=> [i,9.59,12,1]),['-'].map(i=> [i,4.31,12,1]),['{','['].map(i=> [i,4.17,12,1]),['}'].map(i=> [i,4.19,12,1]),['|'].map(i=> [i,4.52,12,1]),['\''].map(i=> [i,2.36,12,1]),['◯'].map(i=> [i,11.47,12,1])],"Hiragino Sans GB13.3":[['I'].map(i=> [i,3.33,14,1]),['`'].map(i=> [i,6.67,14,1]),['J'].map(i=> [i,6.77,14,1]),['↵'].map(i=> [i,0,14,1]),['l'].map(i=> [i,3.11,14,1]),['v'].map(i=> [i,7.25,14,1]),['p'].map(i=> [i,8.63,14,1]),['S'].map(i=> [i,8.91,14,1]),['0','7'].map(i=> [i,8.77,14,1]),['B'].map(i=> [i,9.48,14,1]),['~'].map(i=> [i,7.23,14,1]),['L'].map(i=> [i,8.02,14,1]),['F'].map(i=> [i,8.03,14,1]),['K'].map(i=> [i,9.42,14,1]),['}',']'].map(i=> [i,4.63,14,1]),['我','！','？','《','》','，','、','。','（','：','【','】','“','‘','’','⊙','⭘','℃'].map(i=> [i,13.31,14,1]),['；','）','”'].map(i=> [i,13.33,14,1]),[' '].map(i=> [i,6.65,14,1]),['1','2','3','5','6','8','9','#'].map(i=> [i,8.75,14,1]),['4'].map(i=> [i,8.81,14,1]),['a'].map(i=> [i,7.67,14,1]),['b','q'].map(i=> [i,8.61,14,1]),['d'].map(i=> [i,8.59,14,1]),['c'].map(i=> [i,7.7,14,1]),['e'].map(i=> [i,7.84,14,1]),['f'].map(i=> [i,5.56,14,1]),['g'].map(i=> [i,8.55,14,1]),['h'].map(i=> [i,8.22,14,1]),['i'].map(i=> [i,3.17,14,1]),['j'].map(i=> [i,3.55,14,1]),['k'].map(i=> [i,7.66,14,1]),['m'].map(i=> [i,12.39,14,1]),['n','u'].map(i=> [i,8.23,14,1]),['o'].map(i=> [i,8.33,14,1]),['r'].map(i=> [i,5.73,14,1]),['s'].map(i=> [i,7.16,14,1]),['t'].map(i=> [i,5.23,14,1]),['w'].map(i=> [i,10.25,14,1]),['x'].map(i=> [i,7.14,14,1]),['y'].map(i=> [i,7.39,14,1]),['z'].map(i=> [i,6.64,14,1]),['A'].map(i=> [i,9.98,14,1]),['C'].map(i=> [i,10.08,14,1]),['D'].map(i=> [i,10.09,14,1]),['E'].map(i=> [i,8.56,14,1]),['G'].map(i=> [i,10.05,14,1]),['H'].map(i=> [i,10.36,14,1]),['M'].map(i=> [i,12.59,14,1]),['N'].map(i=> [i,10.31,14,1]),['O'].map(i=> [i,10.58,14,1]),['P'].map(i=> [i,8.83,14,1]),['Q'].map(i=> [i,10.59,14,1]),['R'].map(i=> [i,9.67,14,1]),['T'].map(i=> [i,8.64,14,1]),['U'].map(i=> [i,10.3,14,1]),['V'].map(i=> [i,9.56,14,1]),['W'].map(i=> [i,13.39,14,1]),['X'].map(i=> [i,9.63,14,1]),['Y'].map(i=> [i,9.16,14,1]),['Z'].map(i=> [i,8.92,14,1]),[',','.',':',';'].map(i=> [i,3.2,14,1]),['/'].map(i=> [i,6.33,14,1]),['?'].map(i=> [i,7.61,14,1]),['\"','(',')'].map(i=> [i,4.75,14,1]),['<','>'].map(i=> [i,6.83,14,1]),['=','+'].map(i=> [i,8.73,14,1]),['!'].map(i=> [i,4.16,14,1]),['@'].map(i=> [i,12.05,14,1]),['$'].map(i=> [i,8.66,14,1]),['%'].map(i=> [i,11.72,14,1]),['^'].map(i=> [i,7.09,14,1]),['&'].map(i=> [i,10.63,14,1]),['*','['].map(i=> [i,4.61,14,1]),['_'].map(i=> [i,6.69,14,1]),['-'].map(i=> [i,4.78,14,1]),['{'].map(i=> [i,4.64,14,1]),['\\'].map(i=> [i,6.31,14,1]),['|'].map(i=> [i,5.02,14,1]),['\''].map(i=> [i,2.61,14,1]),['☑'].map(i=> [i,11.05,14,1]),['☐'].map(i=> [i,11.06,14,1]),['◯'].map(i=> [i,12.7,14,1])],"Hiragino Sans GB14":[['n','u'].map(i=> [i,8.67,14,1]),['↵'].map(i=> [i,0,14,1]),['我','！','？','《','》','；','，','、','。','（','）','：','【','】','“','”','‘','’'].map(i=> [i,14,14,1]),[',','.',':',';'].map(i=> [i,3.38,14,1]),[' '].map(i=> [i,7,14,1]),['<','>'].map(i=> [i,7.19,14,1]),['\\'].map(i=> [i,6.66,14,1]),['0','3','7','+'].map(i=> [i,9.2,14,1]),['o'].map(i=> [i,8.77,14,1]),['='].map(i=> [i,9.19,14,1]),['`'].map(i=> [i,7.02,14,1]),['☑','☐'].map(i=> [i,11.64,14,1]),['⊙','⭘','℃'].map(i=> [i,14.02,14,1]),['I'].map(i=> [i,3.5,14,1]),['l'].map(i=> [i,3.27,14,1]),['g'].map(i=> [i,9,14,1]),['N','U'].map(i=> [i,10.84,14,1]),['P'].map(i=> [i,9.28,14,1]),['?'].map(i=> [i,8.02,14,1]),['e'].map(i=> [i,8.25,14,1]),['m'].map(i=> [i,13.05,14,1]),['4'].map(i=> [i,9.27,14,1]),['◯'].map(i=> [i,13.36,14,1]),['/'].map(i=> [i,6.64,14,1]),['B'].map(i=> [i,9.98,14,1]),['V'].map(i=> [i,10.05,14,1]),['G'].map(i=> [i,10.58,14,1]),['C'].map(i=> [i,10.59,14,1]),['h'].map(i=> [i,8.64,14,1]),['-'].map(i=> [i,5.02,14,1]),['1','2','5','6','8','9','#'].map(i=> [i,9.22,14,1]),['a','k'].map(i=> [i,8.06,14,1]),['b','d','q'].map(i=> [i,9.06,14,1]),['c'].map(i=> [i,8.11,14,1]),['f'].map(i=> [i,5.84,14,1]),['i'].map(i=> [i,3.36,14,1]),['j'].map(i=> [i,3.72,14,1]),['p'].map(i=> [i,9.08,14,1]),['r'].map(i=> [i,6.03,14,1]),['s','x'].map(i=> [i,7.53,14,1]),['t'].map(i=> [i,5.5,14,1]),['v','~'].map(i=> [i,7.63,14,1]),['w'].map(i=> [i,10.8,14,1]),['y'].map(i=> [i,7.77,14,1]),['z'].map(i=> [i,6.98,14,1]),['A'].map(i=> [i,10.52,14,1]),['D'].map(i=> [i,10.64,14,1]),['E'].map(i=> [i,9.02,14,1]),['F','L'].map(i=> [i,8.44,14,1]),['H'].map(i=> [i,10.91,14,1]),['J'].map(i=> [i,7.13,14,1]),['K'].map(i=> [i,9.92,14,1]),['M'].map(i=> [i,13.25,14,1]),['O'].map(i=> [i,11.16,14,1]),['Q'].map(i=> [i,11.14,14,1]),['R'].map(i=> [i,10.17,14,1]),['S','Z'].map(i=> [i,9.39,14,1]),['T'].map(i=> [i,9.09,14,1]),['W'].map(i=> [i,14.11,14,1]),['X'].map(i=> [i,10.13,14,1]),['Y'].map(i=> [i,9.64,14,1]),['\"','(',')'].map(i=> [i,5,14,1]),['!'].map(i=> [i,4.36,14,1]),['@'].map(i=> [i,12.69,14,1]),['$'].map(i=> [i,9.11,14,1]),['%'].map(i=> [i,12.33,14,1]),['^'].map(i=> [i,7.48,14,1]),['&'].map(i=> [i,11.17,14,1]),['*','[',']'].map(i=> [i,4.86,14,1]),['_'].map(i=> [i,7.03,14,1]),['{','}'].map(i=> [i,4.88,14,1]),['|'].map(i=> [i,5.27,14,1]),['\''].map(i=> [i,2.73,14,1])],"Hiragino Sans GB15":[['l'].map(i=> [i,3.5,15,1]),['↵'].map(i=> [i,0,15,1]),['F'].map(i=> [i,9.05,15,1]),[' '].map(i=> [i,7.5,15,1]),['我','！','？','《','》','；','，','、','。','（','）','：','【','】','“','”','‘','’'].map(i=> [i,15,15,1]),['.',';'].map(i=> [i,3.63,15,1]),['<'].map(i=> [i,7.69,15,1]),['f'].map(i=> [i,6.28,15,1]),['o'].map(i=> [i,9.39,15,1]),['@'].map(i=> [i,13.58,15,1]),['`'].map(i=> [i,7.52,15,1]),['☑','☐'].map(i=> [i,12.47,15,1]),['⊙','⭘','℃'].map(i=> [i,15.02,15,1]),['◯'].map(i=> [i,14.31,15,1]),['-'].map(i=> [i,5.38,15,1]),['j'].map(i=> [i,3.98,15,1]),['_'].map(i=> [i,7.53,15,1]),['>'].map(i=> [i,7.7,15,1]),['C'].map(i=> [i,11.34,15,1]),['E'].map(i=> [i,9.66,15,1]),['{','}','['].map(i=> [i,5.22,15,1]),['B'].map(i=> [i,10.7,15,1]),['^'].map(i=> [i,8.02,15,1]),['\"','('].map(i=> [i,5.36,15,1]),['i'].map(i=> [i,3.59,15,1]),['0','2','3','5','6','8','9','#'].map(i=> [i,9.88,15,1]),['I'].map(i=> [i,3.75,15,1]),[')'].map(i=> [i,5.34,15,1]),['U'].map(i=> [i,11.61,15,1]),['e'].map(i=> [i,8.83,15,1]),['J'].map(i=> [i,7.64,15,1]),['T'].map(i=> [i,9.75,15,1]),['/','\\'].map(i=> [i,7.13,15,1]),['p'].map(i=> [i,9.72,15,1]),['1','7','=','+'].map(i=> [i,9.86,15,1]),['4'].map(i=> [i,9.92,15,1]),['a','k'].map(i=> [i,8.64,15,1]),['b','d','q'].map(i=> [i,9.7,15,1]),['c'].map(i=> [i,8.69,15,1]),['g'].map(i=> [i,9.63,15,1]),['h'].map(i=> [i,9.27,15,1]),['m'].map(i=> [i,13.98,15,1]),['n','u'].map(i=> [i,9.28,15,1]),['r'].map(i=> [i,6.47,15,1]),['s'].map(i=> [i,8.08,15,1]),['t'].map(i=> [i,5.89,15,1]),['v'].map(i=> [i,8.19,15,1]),['w'].map(i=> [i,11.56,15,1]),['x'].map(i=> [i,8.05,15,1]),['y'].map(i=> [i,8.33,15,1]),['z'].map(i=> [i,7.48,15,1]),['A'].map(i=> [i,11.27,15,1]),['D'].map(i=> [i,11.39,15,1]),['G'].map(i=> [i,11.33,15,1]),['H'].map(i=> [i,11.69,15,1]),['K'].map(i=> [i,10.63,15,1]),['L'].map(i=> [i,9.03,15,1]),['M'].map(i=> [i,14.2,15,1]),['N'].map(i=> [i,11.63,15,1]),['O'].map(i=> [i,11.94,15,1]),['P'].map(i=> [i,9.94,15,1]),['Q'].map(i=> [i,11.95,15,1]),['R'].map(i=> [i,10.91,15,1]),['S','Z'].map(i=> [i,10.05,15,1]),['V'].map(i=> [i,10.77,15,1]),['W'].map(i=> [i,15.11,15,1]),['X'].map(i=> [i,10.84,15,1]),['Y'].map(i=> [i,10.34,15,1]),[',',':'].map(i=> [i,3.61,15,1]),['?'].map(i=> [i,8.58,15,1]),['~'].map(i=> [i,8.16,15,1]),['!'].map(i=> [i,4.69,15,1]),['$'].map(i=> [i,9.77,15,1]),['%'].map(i=> [i,13.2,15,1]),['&'].map(i=> [i,11.97,15,1]),['*',']'].map(i=> [i,5.2,15,1]),['|'].map(i=> [i,5.64,15,1]),['\''].map(i=> [i,2.94,15,1])],"Hiragino Sans GB14.7":[['↵'].map(i=> [i,0,15,1]),['s','x'].map(i=> [i,7.91,15,1]),['^'].map(i=> [i,7.86,15,1]),['k'].map(i=> [i,8.47,15,1]),['C'].map(i=> [i,11.13,15,1]),['&'].map(i=> [i,11.73,15,1]),['我','》','（','“'].map(i=> [i,14.7,15,1]),['！','？','《','；','，','、','。','）','：','【','】','”','‘','’','⊙','⭘','℃'].map(i=> [i,14.72,15,1]),[' '].map(i=> [i,7.35,15,1]),['/','\\'].map(i=> [i,6.98,15,1]),['>'].map(i=> [i,7.53,15,1]),['+'].map(i=> [i,9.66,15,1]),['o'].map(i=> [i,9.2,15,1]),['K'].map(i=> [i,10.41,15,1]),['<'].map(i=> [i,7.55,15,1]),['☑','☐'].map(i=> [i,12.22,15,1]),['◯'].map(i=> [i,14.03,15,1]),['r'].map(i=> [i,6.34,15,1]),['\''].map(i=> [i,2.88,15,1]),['v'].map(i=> [i,8.02,15,1]),['!'].map(i=> [i,4.58,15,1]),['0','1','2','3','5','6','7','8','=','#'].map(i=> [i,9.67,15,1]),['*',']'].map(i=> [i,5.09,15,1]),['I'].map(i=> [i,3.67,15,1]),['e'].map(i=> [i,8.66,15,1]),['a'].map(i=> [i,8.45,15,1]),['`','_'].map(i=> [i,7.38,15,1]),['S','Z'].map(i=> [i,9.86,15,1]),['J'].map(i=> [i,7.48,15,1]),['N','U'].map(i=> [i,11.39,15,1]),['w'].map(i=> [i,11.33,15,1]),['X'].map(i=> [i,10.63,15,1]),['y'].map(i=> [i,8.16,15,1]),['4','P'].map(i=> [i,9.73,15,1]),['9'].map(i=> [i,9.69,15,1]),['b','d','p','q'].map(i=> [i,9.52,15,1]),['c'].map(i=> [i,8.52,15,1]),['f'].map(i=> [i,6.16,15,1]),['g'].map(i=> [i,9.44,15,1]),['h'].map(i=> [i,9.08,15,1]),['i'].map(i=> [i,3.52,15,1]),['j'].map(i=> [i,3.91,15,1]),['l'].map(i=> [i,3.42,15,1]),['m'].map(i=> [i,13.7,15,1]),['n'].map(i=> [i,9.11,15,1]),['t'].map(i=> [i,5.78,15,1]),['u'].map(i=> [i,9.09,15,1]),['z'].map(i=> [i,7.33,15,1]),['A'].map(i=> [i,11.05,15,1]),['B'].map(i=> [i,10.48,15,1]),['D'].map(i=> [i,11.16,15,1]),['E'].map(i=> [i,9.47,15,1]),['F'].map(i=> [i,8.88,15,1]),['G'].map(i=> [i,11.09,15,1]),['H'].map(i=> [i,11.45,15,1]),['L'].map(i=> [i,8.86,15,1]),['M'].map(i=> [i,13.91,15,1]),['O','Q'].map(i=> [i,11.7,15,1]),['R'].map(i=> [i,10.69,15,1]),['T'].map(i=> [i,9.55,15,1]),['V'].map(i=> [i,10.55,15,1]),['W'].map(i=> [i,14.81,15,1]),['Y'].map(i=> [i,10.13,15,1]),[','].map(i=> [i,3.53,15,1]),['.',':',';'].map(i=> [i,3.55,15,1]),['?'].map(i=> [i,8.41,15,1]),['\"','(',')'].map(i=> [i,5.25,15,1]),['~'].map(i=> [i,7.98,15,1]),['@'].map(i=> [i,13.33,15,1]),['$'].map(i=> [i,9.56,15,1]),['%'].map(i=> [i,12.94,15,1]),['-'].map(i=> [i,5.28,15,1]),['{'].map(i=> [i,5.13,15,1]),['}','['].map(i=> [i,5.11,15,1]),['|'].map(i=> [i,5.53,15,1])],"Hiragino Sans GB16":[['('].map(i=> [i,5.72,16,1]),['↵'].map(i=> [i,0,16,1]),['我','！','？','《','》','；','，','、','。','（','）','：','【','】','“','”','‘','’'].map(i=> [i,16,16,1]),[',','.',':',';'].map(i=> [i,3.86,16,1]),[' '].map(i=> [i,8,16,1]),['>'].map(i=> [i,8.22,16,1]),['f'].map(i=> [i,6.69,16,1]),['3','7'].map(i=> [i,10.52,16,1]),['o'].map(i=> [i,10.02,16,1]),['`'].map(i=> [i,8.02,16,1]),['<'].map(i=> [i,8.2,16,1]),['☑','☐'].map(i=> [i,13.3,16,1]),['⊙','⭘','℃'].map(i=> [i,16.02,16,1]),['I'].map(i=> [i,4,16,1]),['r'].map(i=> [i,6.89,16,1]),['g'].map(i=> [i,10.28,16,1]),['P'].map(i=> [i,10.61,16,1]),['e'].map(i=> [i,9.42,16,1]),['H'].map(i=> [i,12.45,16,1]),['0','1','2','5','6','8','9','#'].map(i=> [i,10.53,16,1]),['4'].map(i=> [i,10.59,16,1]),['a'].map(i=> [i,9.22,16,1]),['b'].map(i=> [i,10.36,16,1]),['d','q'].map(i=> [i,10.34,16,1]),['c'].map(i=> [i,9.27,16,1]),['h'].map(i=> [i,9.89,16,1]),['i'].map(i=> [i,3.81,16,1]),['j'].map(i=> [i,4.27,16,1]),['k'].map(i=> [i,9.2,16,1]),['l'].map(i=> [i,3.73,16,1]),['m'].map(i=> [i,14.91,16,1]),['n','u'].map(i=> [i,9.91,16,1]),['p'].map(i=> [i,10.38,16,1]),['s'].map(i=> [i,8.61,16,1]),['t'].map(i=> [i,6.3,16,1]),['v'].map(i=> [i,8.72,16,1]),['w'].map(i=> [i,12.33,16,1]),['x'].map(i=> [i,8.59,16,1]),['y'].map(i=> [i,8.88,16,1]),['z'].map(i=> [i,7.98,16,1]),['A'].map(i=> [i,12.02,16,1]),['B'].map(i=> [i,11.41,16,1]),['C'].map(i=> [i,12.11,16,1]),['D'].map(i=> [i,12.16,16,1]),['E'].map(i=> [i,10.3,16,1]),['F','L'].map(i=> [i,9.66,16,1]),['G'].map(i=> [i,12.08,16,1]),['J'].map(i=> [i,8.14,16,1]),['K'].map(i=> [i,11.33,16,1]),['M'].map(i=> [i,15.14,16,1]),['N','U'].map(i=> [i,12.39,16,1]),['O','Q'].map(i=> [i,12.73,16,1]),['R'].map(i=> [i,11.64,16,1]),['S','Z'].map(i=> [i,10.72,16,1]),['T'].map(i=> [i,10.39,16,1]),['V'].map(i=> [i,11.48,16,1]),['W'].map(i=> [i,16.11,16,1]),['X'].map(i=> [i,11.56,16,1]),['Y'].map(i=> [i,11.03,16,1]),['/','\\'].map(i=> [i,7.59,16,1]),['?'].map(i=> [i,9.16,16,1]),['\"',')'].map(i=> [i,5.7,16,1]),['=','+'].map(i=> [i,10.5,16,1]),['~'].map(i=> [i,8.7,16,1]),['!'].map(i=> [i,5,16,1]),['@'].map(i=> [i,14.48,16,1]),['$'].map(i=> [i,10.42,16,1]),['%'].map(i=> [i,14.08,16,1]),['^'].map(i=> [i,8.55,16,1]),['&'].map(i=> [i,12.77,16,1]),['*','['].map(i=> [i,5.55,16,1]),['_'].map(i=> [i,8.03,16,1]),['-'].map(i=> [i,5.75,16,1]),['{'].map(i=> [i,5.58,16,1]),['}',']'].map(i=> [i,5.56,16,1]),['|'].map(i=> [i,6.02,16,1]),['\''].map(i=> [i,3.14,16,1]),['◯'].map(i=> [i,15.27,16,1])],"Hiragino Sans GB18.7":[['我','》','（','“','⊙','⭘','℃'].map(i=> [i,18.7,18,1]),['！','？','《','；','，','、','。','）','：','【','】','”','‘','’'].map(i=> [i,18.72,18,1]),[' '].map(i=> [i,9.35,18,1]),['0','2','3','6','7','9','#'].map(i=> [i,12.3,18,1]),['1','5','8'].map(i=> [i,12.31,18,1]),['4'].map(i=> [i,12.38,18,1]),['a','k'].map(i=> [i,10.77,18,1]),['b','d','q'].map(i=> [i,12.09,18,1]),['c'].map(i=> [i,10.83,18,1]),['e'].map(i=> [i,11.02,18,1]),['f'].map(i=> [i,7.81,18,1]),['g'].map(i=> [i,12,18,1]),['h'].map(i=> [i,11.55,18,1]),['i'].map(i=> [i,4.47,18,1]),['j'].map(i=> [i,4.97,18,1]),['l'].map(i=> [i,4.36,18,1]),['m'].map(i=> [i,17.42,18,1]),['n'].map(i=> [i,11.58,18,1]),['o'].map(i=> [i,11.7,18,1]),['p'].map(i=> [i,12.11,18,1]),['r'].map(i=> [i,8.06,18,1]),['s'].map(i=> [i,10.06,18,1]),['t'].map(i=> [i,7.34,18,1]),['u'].map(i=> [i,11.56,18,1]),['v'].map(i=> [i,10.2,18,1]),['w'].map(i=> [i,14.41,18,1]),['x'].map(i=> [i,10.05,18,1]),['y'].map(i=> [i,10.38,18,1]),['z'].map(i=> [i,9.33,18,1]),['A'].map(i=> [i,14.03,18,1]),['B'].map(i=> [i,13.33,18,1]),['C'].map(i=> [i,14.16,18,1]),['D'].map(i=> [i,14.19,18,1]),['E'].map(i=> [i,12.05,18,1]),['F','L'].map(i=> [i,11.27,18,1]),['G'].map(i=> [i,14.13,18,1]),['H'].map(i=> [i,14.56,18,1]),['I'].map(i=> [i,4.67,18,1]),['J'].map(i=> [i,9.52,18,1]),['K'].map(i=> [i,13.23,18,1]),['M'].map(i=> [i,17.69,18,1]),['N'].map(i=> [i,14.5,18,1]),['O'].map(i=> [i,14.88,18,1]),['P'].map(i=> [i,12.39,18,1]),['Q'].map(i=> [i,14.89,18,1]),['R'].map(i=> [i,13.59,18,1]),['S','Z'].map(i=> [i,12.52,18,1]),['T'].map(i=> [i,12.16,18,1]),['U'].map(i=> [i,14.47,18,1]),['V'].map(i=> [i,13.42,18,1]),['W'].map(i=> [i,18.83,18,1]),['X'].map(i=> [i,13.52,18,1]),['Y'].map(i=> [i,12.89,18,1]),[','].map(i=> [i,4.52,18,1]),['.',':',';'].map(i=> [i,4.5,18,1]),['/'].map(i=> [i,8.88,18,1]),['?'].map(i=> [i,10.7,18,1]),['\"','(',')'].map(i=> [i,6.67,18,1]),['<'].map(i=> [i,9.59,18,1]),['>'].map(i=> [i,9.58,18,1]),['=','+'].map(i=> [i,12.28,18,1]),['~'].map(i=> [i,10.17,18,1]),['`'].map(i=> [i,9.38,18,1]),['!'].map(i=> [i,5.83,18,1]),['@'].map(i=> [i,16.94,18,1]),['$'].map(i=> [i,12.17,18,1]),['%'].map(i=> [i,16.45,18,1]),['^'].map(i=> [i,9.98,18,1]),['&'].map(i=> [i,14.92,18,1]),['*','[',']'].map(i=> [i,6.48,18,1]),['_'].map(i=> [i,9.39,18,1]),['-'].map(i=> [i,6.7,18,1]),['{','}'].map(i=> [i,6.5,18,1]),['\\'].map(i=> [i,8.89,18,1]),['|'].map(i=> [i,7.02,18,1]),['\''].map(i=> [i,3.67,18,1]),['☑'].map(i=> [i,15.53,18,1]),['☐'].map(i=> [i,15.55,18,1]),['◯'].map(i=> [i,17.86,18,1]),['↵'].map(i=> [i,0,18,1])],"Hiragino Sans GB19":[['z'].map(i=> [i,9.47,19,1]),['J'].map(i=> [i,9.67,19,1]),['7','=','+'].map(i=> [i,12.48,19,1]),['o'].map(i=> [i,11.89,19,1]),['b','d'].map(i=> [i,12.28,19,1]),['y'].map(i=> [i,10.55,19,1]),['0','1','2','3','5','6','8','9','#'].map(i=> [i,12.5,19,1]),['V'].map(i=> [i,13.64,19,1]),['↵'].map(i=> [i,0,19,1]),['我','！','？','《','》','；','，','、','。','（','）','：','【','】','“','”','‘','’'].map(i=> [i,19,19,1]),[' '].map(i=> [i,9.5,19,1]),['/'].map(i=> [i,9.03,19,1]),['f'].map(i=> [i,7.94,19,1]),['`'].map(i=> [i,9.52,19,1]),['>'].map(i=> [i,9.75,19,1]),['☑','☐'].map(i=> [i,15.78,19,1]),['⊙','⭘','℃'].map(i=> [i,19.02,19,1]),['◯'].map(i=> [i,18.14,19,1]),['i'].map(i=> [i,4.53,19,1]),['t'].map(i=> [i,7.47,19,1]),['\"','(',')'].map(i=> [i,6.78,19,1]),['{','}'].map(i=> [i,6.61,19,1]),['\\'].map(i=> [i,9.02,19,1]),['s'].map(i=> [i,10.22,19,1]),['<'].map(i=> [i,9.73,19,1]),['Y'].map(i=> [i,13.08,19,1]),['O'].map(i=> [i,15.11,19,1]),['G'].map(i=> [i,14.34,19,1]),['4'].map(i=> [i,12.58,19,1]),['T','$'].map(i=> [i,12.36,19,1]),['r'].map(i=> [i,8.17,19,1]),['l'].map(i=> [i,4.42,19,1]),['P'].map(i=> [i,12.59,19,1]),['e'].map(i=> [i,11.19,19,1]),['a','k'].map(i=> [i,10.95,19,1]),['c'].map(i=> [i,11,19,1]),['g'].map(i=> [i,12.19,19,1]),['h','n'].map(i=> [i,11.75,19,1]),['j'].map(i=> [i,5.05,19,1]),['m'].map(i=> [i,17.7,19,1]),['p'].map(i=> [i,12.31,19,1]),['q'].map(i=> [i,12.3,19,1]),['u'].map(i=> [i,11.77,19,1]),['v'].map(i=> [i,10.34,19,1]),['w'].map(i=> [i,14.64,19,1]),['x'].map(i=> [i,10.2,19,1]),['A'].map(i=> [i,14.27,19,1]),['B'].map(i=> [i,13.55,19,1]),['C'].map(i=> [i,14.38,19,1]),['D'].map(i=> [i,14.42,19,1]),['E'].map(i=> [i,12.23,19,1]),['F','L'].map(i=> [i,11.45,19,1]),['H'].map(i=> [i,14.8,19,1]),['I'].map(i=> [i,4.75,19,1]),['K'].map(i=> [i,13.44,19,1]),['M'].map(i=> [i,17.97,19,1]),['N'].map(i=> [i,14.73,19,1]),['Q'].map(i=> [i,15.13,19,1]),['R'].map(i=> [i,13.81,19,1]),['S'].map(i=> [i,12.72,19,1]),['U'].map(i=> [i,14.69,19,1]),['W'].map(i=> [i,19.14,19,1]),['X'].map(i=> [i,13.73,19,1]),['Z'].map(i=> [i,12.73,19,1]),[',',':',';'].map(i=> [i,4.58,19,1]),['.'].map(i=> [i,4.56,19,1]),['?'].map(i=> [i,10.86,19,1]),['~'].map(i=> [i,10.33,19,1]),['!'].map(i=> [i,5.92,19,1]),['@'].map(i=> [i,17.22,19,1]),['%'].map(i=> [i,16.72,19,1]),['^'].map(i=> [i,10.14,19,1]),['&'].map(i=> [i,15.16,19,1]),['*',']'].map(i=> [i,6.59,19,1]),['_'].map(i=> [i,9.53,19,1]),['-'].map(i=> [i,6.81,19,1]),['['].map(i=> [i,6.58,19,1]),['|'].map(i=> [i,7.14,19,1]),['\''].map(i=> [i,3.73,19,1])],"Hiragino Sans GB20":[['我','！','？','《','》','；','，','、','。','（','）','：','【','】','“','”','‘','’'].map(i=> [i,20,20,1]),[' '].map(i=> [i,10,20,1]),['8','+'].map(i=> [i,13.14,20,1]),['w'].map(i=> [i,15.42,20,1]),['o'].map(i=> [i,12.52,20,1]),['`'].map(i=> [i,10.02,20,1]),['<'].map(i=> [i,10.25,20,1]),['☐'].map(i=> [i,16.61,20,1]),['☑'].map(i=> [i,16.63,20,1]),['⊙','⭘','℃'].map(i=> [i,20.02,20,1]),['↵'].map(i=> [i,0,20,1]),['i'].map(i=> [i,4.77,20,1]),['t'].map(i=> [i,7.86,20,1]),['\"','('].map(i=> [i,7.14,20,1]),['h'].map(i=> [i,12.36,20,1]),['s'].map(i=> [i,10.75,20,1]),['J'].map(i=> [i,10.17,20,1]),['d','q'].map(i=> [i,12.94,20,1]),['I'].map(i=> [i,5,20,1]),[')'].map(i=> [i,7.13,20,1]),['U'].map(i=> [i,15.47,20,1]),['e'].map(i=> [i,11.78,20,1]),['◯'].map(i=> [i,19.09,20,1]),['0','1','2','3','5','6','7','9','#'].map(i=> [i,13.16,20,1]),['4'].map(i=> [i,13.23,20,1]),['a','k'].map(i=> [i,11.52,20,1]),['b'].map(i=> [i,12.92,20,1]),['c'].map(i=> [i,11.58,20,1]),['f'].map(i=> [i,8.36,20,1]),['g'].map(i=> [i,12.83,20,1]),['j'].map(i=> [i,5.33,20,1]),['l'].map(i=> [i,4.66,20,1]),['m'].map(i=> [i,18.63,20,1]),['n','u'].map(i=> [i,12.38,20,1]),['p'].map(i=> [i,12.95,20,1]),['r'].map(i=> [i,8.63,20,1]),['v'].map(i=> [i,10.89,20,1]),['x'].map(i=> [i,10.73,20,1]),['y'].map(i=> [i,11.09,20,1]),['z'].map(i=> [i,9.97,20,1]),['A'].map(i=> [i,15.02,20,1]),['B'].map(i=> [i,14.27,20,1]),['C'].map(i=> [i,15.13,20,1]),['D'].map(i=> [i,15.19,20,1]),['E'].map(i=> [i,12.88,20,1]),['F'].map(i=> [i,12.05,20,1]),['G'].map(i=> [i,15.09,20,1]),['H'].map(i=> [i,15.58,20,1]),['K'].map(i=> [i,14.16,20,1]),['L'].map(i=> [i,12.06,20,1]),['M'].map(i=> [i,18.91,20,1]),['N'].map(i=> [i,15.5,20,1]),['O','Q'].map(i=> [i,15.92,20,1]),['P'].map(i=> [i,13.25,20,1]),['R'].map(i=> [i,14.53,20,1]),['S','Z'].map(i=> [i,13.39,20,1]),['T'].map(i=> [i,13,20,1]),['V'].map(i=> [i,14.36,20,1]),['W'].map(i=> [i,20.14,20,1]),['X'].map(i=> [i,14.45,20,1]),['Y'].map(i=> [i,13.78,20,1]),[',','.',':',';'].map(i=> [i,4.81,20,1]),['/','\\'].map(i=> [i,9.5,20,1]),['?'].map(i=> [i,11.44,20,1]),['>'].map(i=> [i,10.27,20,1]),['='].map(i=> [i,13.13,20,1]),['~'].map(i=> [i,10.88,20,1]),['!'].map(i=> [i,6.23,20,1]),['@'].map(i=> [i,18.13,20,1]),['$'].map(i=> [i,13.02,20,1]),['%'].map(i=> [i,17.59,20,1]),['^'].map(i=> [i,10.67,20,1]),['&'].map(i=> [i,15.95,20,1]),['*','[',']'].map(i=> [i,6.94,20,1]),['_'].map(i=> [i,10.05,20,1]),['-'].map(i=> [i,7.17,20,1]),['{','}'].map(i=> [i,6.95,20,1]),['|'].map(i=> [i,7.52,20,1]),['\''].map(i=> [i,3.91,20,1])],"Hiragino Sans GB21.3":[['↵'].map(i=> [i,0,22,1]),['A'].map(i=> [i,16,22,1]),['|'].map(i=> [i,8,22,1]),['P'].map(i=> [i,14.13,22,1]),['\\'].map(i=> [i,10.13,22,1]),['V'].map(i=> [i,15.28,22,1]),['我','！','？','《','》','，','、','。','（','：','【','】','“','‘','’','⊙','⭘','℃'].map(i=> [i,21.31,22,1]),['；','）','”'].map(i=> [i,21.33,22,1]),[' '].map(i=> [i,10.65,22,1]),['0','2','3','6','7','9','#'].map(i=> [i,14.02,22,1]),['1','5','8'].map(i=> [i,14,22,1]),['4'].map(i=> [i,14.09,22,1]),['a','k'].map(i=> [i,12.27,22,1]),['b'].map(i=> [i,13.78,22,1]),['d','q'].map(i=> [i,13.77,22,1]),['c'].map(i=> [i,12.33,22,1]),['e'].map(i=> [i,12.55,22,1]),['f'].map(i=> [i,8.89,22,1]),['g'].map(i=> [i,13.67,22,1]),['h'].map(i=> [i,13.16,22,1]),['i'].map(i=> [i,5.09,22,1]),['j'].map(i=> [i,5.66,22,1]),['l'].map(i=> [i,4.95,22,1]),['m'].map(i=> [i,19.84,22,1]),['n'].map(i=> [i,13.19,22,1]),['o'].map(i=> [i,13.33,22,1]),['p'].map(i=> [i,13.8,22,1]),['r'].map(i=> [i,9.17,22,1]),['s'].map(i=> [i,11.45,22,1]),['t'].map(i=> [i,8.38,22,1]),['u'].map(i=> [i,13.17,22,1]),['v'].map(i=> [i,11.61,22,1]),['w'].map(i=> [i,16.42,22,1]),['x'].map(i=> [i,11.42,22,1]),['y'].map(i=> [i,11.81,22,1]),['z'].map(i=> [i,10.63,22,1]),['B'].map(i=> [i,15.17,22,1]),['C'].map(i=> [i,16.13,22,1]),['D'].map(i=> [i,16.16,22,1]),['E'].map(i=> [i,13.72,22,1]),['F'].map(i=> [i,12.83,22,1]),['G'].map(i=> [i,16.08,22,1]),['H'].map(i=> [i,16.59,22,1]),['I'].map(i=> [i,5.31,22,1]),['J'].map(i=> [i,10.84,22,1]),['K'].map(i=> [i,15.06,22,1]),['L'].map(i=> [i,12.84,22,1]),['M'].map(i=> [i,20.14,22,1]),['N'].map(i=> [i,16.5,22,1]),['O'].map(i=> [i,16.95,22,1]),['Q'].map(i=> [i,16.94,22,1]),['R'].map(i=> [i,15.48,22,1]),['S','Z'].map(i=> [i,14.27,22,1]),['T'].map(i=> [i,13.84,22,1]),['U'].map(i=> [i,16.48,22,1]),['W'].map(i=> [i,21.44,22,1]),['X'].map(i=> [i,15.41,22,1]),['Y'].map(i=> [i,14.66,22,1]),[','].map(i=> [i,5.14,22,1]),['.',':',';'].map(i=> [i,5.13,22,1]),['/'].map(i=> [i,10.11,22,1]),['?'].map(i=> [i,12.17,22,1]),['\"',')'].map(i=> [i,7.61,22,1]),['<','>'].map(i=> [i,10.92,22,1]),['=','+'].map(i=> [i,13.98,22,1]),['~'].map(i=> [i,11.58,22,1]),['`'].map(i=> [i,10.67,22,1]),['!'].map(i=> [i,6.64,22,1]),['@'].map(i=> [i,19.28,22,1]),['$'].map(i=> [i,13.86,22,1]),['%'].map(i=> [i,18.73,22,1]),['^'].map(i=> [i,11.38,22,1]),['&'].map(i=> [i,16.98,22,1]),['*','['].map(i=> [i,7.39,22,1]),['('].map(i=> [i,7.59,22,1]),['_'].map(i=> [i,10.69,22,1]),['-'].map(i=> [i,7.64,22,1]),['{','}'].map(i=> [i,7.41,22,1]),[']'].map(i=> [i,7.38,22,1]),['\''].map(i=> [i,4.17,22,1]),['☑'].map(i=> [i,17.69,22,1]),['☐'].map(i=> [i,17.7,22,1]),['◯'].map(i=> [i,20.33,22,1])],"Hiragino Sans GB24":[['↵'].map(i=> [i,0,24,1]),['我','！','？','《','》','；','，','、','。','（','）','：','【','】','“','”','‘','’'].map(i=> [i,24,24,1]),[' '].map(i=> [i,12,24,1]),['^'].map(i=> [i,12.8,24,1]),['o'].map(i=> [i,15.02,24,1]),['`'].map(i=> [i,12.02,24,1]),['<'].map(i=> [i,12.3,24,1]),['☑','☐'].map(i=> [i,19.94,24,1]),['⊙','⭘','℃'].map(i=> [i,24.02,24,1]),['U'].map(i=> [i,18.56,24,1]),['P'].map(i=> [i,15.91,24,1]),['e'].map(i=> [i,14.13,24,1]),['0','1','3','5','6','8','9','#'].map(i=> [i,15.78,24,1]),['2','7'].map(i=> [i,15.8,24,1]),['4'].map(i=> [i,15.88,24,1]),['a','k'].map(i=> [i,13.81,24,1]),['b','d'].map(i=> [i,15.52,24,1]),['c'].map(i=> [i,13.89,24,1]),['f'].map(i=> [i,10.03,24,1]),['g'].map(i=> [i,15.39,24,1]),['h'].map(i=> [i,14.83,24,1]),['i'].map(i=> [i,5.73,24,1]),['j'].map(i=> [i,6.38,24,1]),['l'].map(i=> [i,5.58,24,1]),['m'].map(i=> [i,22.36,24,1]),['n','u'].map(i=> [i,14.84,24,1]),['p'].map(i=> [i,15.55,24,1]),['q'].map(i=> [i,15.53,24,1]),['r'].map(i=> [i,10.33,24,1]),['s'].map(i=> [i,12.91,24,1]),['t'].map(i=> [i,9.42,24,1]),['v'].map(i=> [i,13.08,24,1]),['w'].map(i=> [i,18.48,24,1]),['x'].map(i=> [i,12.89,24,1]),['y'].map(i=> [i,13.31,24,1]),['z'].map(i=> [i,11.97,24,1]),['A'].map(i=> [i,18.02,24,1]),['B'].map(i=> [i,17.09,24,1]),['C'].map(i=> [i,18.16,24,1]),['D'].map(i=> [i,18.22,24,1]),['E'].map(i=> [i,15.44,24,1]),['F','L'].map(i=> [i,14.47,24,1]),['G'].map(i=> [i,18.11,24,1]),['H'].map(i=> [i,18.69,24,1]),['I'].map(i=> [i,6,24,1]),['J'].map(i=> [i,12.2,24,1]),['K'].map(i=> [i,16.98,24,1]),['M'].map(i=> [i,22.69,24,1]),['N'].map(i=> [i,18.59,24,1]),['O','Q'].map(i=> [i,19.09,24,1]),['R'].map(i=> [i,17.44,24,1]),['S','Z'].map(i=> [i,16.08,24,1]),['T'].map(i=> [i,15.59,24,1]),['V'].map(i=> [i,17.22,24,1]),['W'].map(i=> [i,24.17,24,1]),['X'].map(i=> [i,17.34,24,1]),['Y'].map(i=> [i,16.52,24,1]),[',','.'].map(i=> [i,5.77,24,1]),['/','\\'].map(i=> [i,11.39,24,1]),['?'].map(i=> [i,13.72,24,1]),['\"','(',')'].map(i=> [i,8.56,24,1]),[':',';'].map(i=> [i,5.78,24,1]),['>'].map(i=> [i,12.31,24,1]),['=','+'].map(i=> [i,15.75,24,1]),['~'].map(i=> [i,13.05,24,1]),['!'].map(i=> [i,7.48,24,1]),['@'].map(i=> [i,21.73,24,1]),['$'].map(i=> [i,15.63,24,1]),['%'].map(i=> [i,21.11,24,1]),['&'].map(i=> [i,19.16,24,1]),['*',']'].map(i=> [i,8.31,24,1]),['_'].map(i=> [i,12.03,24,1]),['-'].map(i=> [i,8.61,24,1]),['{','}'].map(i=> [i,8.34,24,1]),['['].map(i=> [i,8.33,24,1]),['|'].map(i=> [i,9.02,24,1]),['\''].map(i=> [i,4.7,24,1]),['◯'].map(i=> [i,22.91,24,1])],"Hiragino Sans GB26.7":[[' '].map(i=> [i,13.35,26,1]),['↵'].map(i=> [i,0,26,1]),['N'].map(i=> [i,20.67,26,1]),['I'].map(i=> [i,6.66,26,1]),['t'].map(i=> [i,10.48,26,1]),[')'].map(i=> [i,9.53,26,1]),['{'].map(i=> [i,9.28,26,1]),['/','\\'].map(i=> [i,12.67,26,1]),['h'].map(i=> [i,16.5,26,1]),['`'].map(i=> [i,13.38,26,1]),['b','d','q'].map(i=> [i,17.27,26,1]),['f'].map(i=> [i,11.14,26,1]),['}','['].map(i=> [i,9.27,26,1]),['<'].map(i=> [i,13.69,26,1]),['J'].map(i=> [i,13.58,26,1]),['P'].map(i=> [i,17.69,26,1]),['\"','('].map(i=> [i,9.52,26,1]),['r'].map(i=> [i,11.5,26,1]),['n','u'].map(i=> [i,16.52,26,1]),['B'].map(i=> [i,19.02,26,1]),['=','+'].map(i=> [i,17.53,26,1]),['我','》','（','“','”','⭘'].map(i=> [i,26.7,26,1]),['！','？','《','；','，','、','。','）','：','【','】','‘','’','⊙','℃'].map(i=> [i,26.72,26,1]),['0','2','3','5','6','8','9'].map(i=> [i,17.56,26,1]),['1','7','#'].map(i=> [i,17.55,26,1]),['4'].map(i=> [i,17.66,26,1]),['a'].map(i=> [i,15.36,26,1]),['c'].map(i=> [i,15.45,26,1]),['e'].map(i=> [i,15.72,26,1]),['g'].map(i=> [i,17.13,26,1]),['i'].map(i=> [i,6.36,26,1]),['j'].map(i=> [i,7.09,26,1]),['k'].map(i=> [i,15.38,26,1]),['l'].map(i=> [i,6.2,26,1]),['m'].map(i=> [i,24.88,26,1]),['o'].map(i=> [i,16.7,26,1]),['p'].map(i=> [i,17.3,26,1]),['s'].map(i=> [i,14.34,26,1]),['v'].map(i=> [i,14.55,26,1]),['w'].map(i=> [i,20.56,26,1]),['x'].map(i=> [i,14.33,26,1]),['y'].map(i=> [i,14.81,26,1]),['z'].map(i=> [i,13.31,26,1]),['A'].map(i=> [i,20.05,26,1]),['C'].map(i=> [i,20.2,26,1]),['D'].map(i=> [i,20.25,26,1]),['E'].map(i=> [i,17.19,26,1]),['F','L'].map(i=> [i,16.09,26,1]),['G'].map(i=> [i,20.14,26,1]),['H'].map(i=> [i,20.8,26,1]),['K'].map(i=> [i,18.89,26,1]),['M'].map(i=> [i,25.25,26,1]),['O','Q'].map(i=> [i,21.25,26,1]),['R'].map(i=> [i,19.39,26,1]),['S','Z'].map(i=> [i,17.88,26,1]),['T'].map(i=> [i,17.34,26,1]),['U'].map(i=> [i,20.64,26,1]),['V'].map(i=> [i,19.17,26,1]),['W'].map(i=> [i,26.88,26,1]),['X'].map(i=> [i,19.28,26,1]),['Y'].map(i=> [i,18.39,26,1]),[',','.',':',';'].map(i=> [i,6.42,26,1]),['?'].map(i=> [i,15.27,26,1]),['>'].map(i=> [i,13.67,26,1]),['~'].map(i=> [i,14.52,26,1]),['!'].map(i=> [i,8.31,26,1]),['@'].map(i=> [i,24.19,26,1]),['$'].map(i=> [i,17.38,26,1]),['%'].map(i=> [i,23.48,26,1]),['^'].map(i=> [i,14.25,26,1]),['&'].map(i=> [i,21.3,26,1]),['*',']'].map(i=> [i,9.25,26,1]),['_'].map(i=> [i,13.39,26,1]),['-'].map(i=> [i,9.58,26,1]),['|'].map(i=> [i,10.03,26,1]),['\''].map(i=> [i,5.22,26,1]),['☑'].map(i=> [i,22.17,26,1]),['☐'].map(i=> [i,22.19,26,1]),['◯'].map(i=> [i,25.48,26,1])],"Hiragino Sans GB29.3":[['↵'].map(i=> [i,0,30,1]),['r'].map(i=> [i,12.61,30,1]),['j'].map(i=> [i,7.78,30,1]),['v'].map(i=> [i,15.95,30,1]),['我','！','？','《','》','，','、','。','（','：','【','】','“','‘','’','⊙','⭘','℃'].map(i=> [i,29.31,30,1]),['；','）','”'].map(i=> [i,29.33,30,1]),[' '].map(i=> [i,14.65,30,1]),['0','1','2','3','5','6','7','8','9','#'].map(i=> [i,19.27,30,1]),['4'].map(i=> [i,19.39,30,1]),['a','k'].map(i=> [i,16.86,30,1]),['b','d'].map(i=> [i,18.94,30,1]),['c'].map(i=> [i,16.95,30,1]),['e'].map(i=> [i,17.25,30,1]),['f'].map(i=> [i,12.23,30,1]),['g'].map(i=> [i,18.8,30,1]),['h'].map(i=> [i,18.09,30,1]),['i'].map(i=> [i,6.98,30,1]),['l'].map(i=> [i,6.81,30,1]),['m'].map(i=> [i,27.3,30,1]),['n','u'].map(i=> [i,18.13,30,1]),['o'].map(i=> [i,18.33,30,1]),['p'].map(i=> [i,18.97,30,1]),['q'].map(i=> [i,18.95,30,1]),['s'].map(i=> [i,15.75,30,1]),['t'].map(i=> [i,11.5,30,1]),['w'].map(i=> [i,22.58,30,1]),['x'].map(i=> [i,15.72,30,1]),['y'].map(i=> [i,16.25,30,1]),['z'].map(i=> [i,14.61,30,1]),['A'].map(i=> [i,21.98,30,1]),['B'].map(i=> [i,20.88,30,1]),['C'].map(i=> [i,22.17,30,1]),['D'].map(i=> [i,22.22,30,1]),['E'].map(i=> [i,18.86,30,1]),['F','L'].map(i=> [i,17.66,30,1]),['G'].map(i=> [i,22.11,30,1]),['H'].map(i=> [i,22.81,30,1]),['I'].map(i=> [i,7.31,30,1]),['J'].map(i=> [i,14.89,30,1]),['K'].map(i=> [i,20.73,30,1]),['M'].map(i=> [i,27.7,30,1]),['N'].map(i=> [i,22.69,30,1]),['O'].map(i=> [i,23.31,30,1]),['P'].map(i=> [i,19.41,30,1]),['Q'].map(i=> [i,23.3,30,1]),['R'].map(i=> [i,21.3,30,1]),['S','Z'].map(i=> [i,19.61,30,1]),['T'].map(i=> [i,19.03,30,1]),['U'].map(i=> [i,22.67,30,1]),['V'].map(i=> [i,21.02,30,1]),['W'].map(i=> [i,29.5,30,1]),['X'].map(i=> [i,21.17,30,1]),['Y'].map(i=> [i,20.17,30,1]),[',','.',';'].map(i=> [i,7.05,30,1]),['/','\\'].map(i=> [i,13.91,30,1]),['?'].map(i=> [i,16.75,30,1]),['\"',')'].map(i=> [i,10.44,30,1]),[':'].map(i=> [i,7.06,30,1]),['<','>'].map(i=> [i,15.02,30,1]),['=','+'].map(i=> [i,19.23,30,1]),['~'].map(i=> [i,15.92,30,1]),['`'].map(i=> [i,14.67,30,1]),['!'].map(i=> [i,9.13,30,1]),['@'].map(i=> [i,26.53,30,1]),['$'].map(i=> [i,19.06,30,1]),['%'].map(i=> [i,25.77,30,1]),['^'].map(i=> [i,15.64,30,1]),['&'].map(i=> [i,23.36,30,1]),['*','[',']'].map(i=> [i,10.16,30,1]),['('].map(i=> [i,10.45,30,1]),['_'].map(i=> [i,14.7,30,1]),['-'].map(i=> [i,10.5,30,1]),['{'].map(i=> [i,10.19,30,1]),['}'].map(i=> [i,10.17,30,1]),['|'].map(i=> [i,11,30,1]),['\''].map(i=> [i,5.73,30,1]),['☑'].map(i=> [i,24.33,30,1]),['☐'].map(i=> [i,24.34,30,1]),['◯'].map(i=> [i,27.97,30,1])],"Hiragino Sans GB32":[['↵'].map(i=> [i,0,32,1]),['我','！','？','《','》','；','，','、','。','（','）','：','【','】','“','”','‘','’'].map(i=> [i,32,32,1]),[' '].map(i=> [i,16,32,1]),['f'].map(i=> [i,13.36,32,1]),['o'].map(i=> [i,20.02,32,1]),['`'].map(i=> [i,16.02,32,1]),['☑','☐'].map(i=> [i,26.58,32,1]),['⊙','⭘','℃'].map(i=> [i,32.02,32,1]),['◯'].map(i=> [i,30.53,32,1]),['I'].map(i=> [i,7.98,32,1]),['l'].map(i=> [i,7.44,32,1]),['U'].map(i=> [i,24.75,32,1]),['P'].map(i=> [i,21.2,32,1]),['0','2','6','8','9'].map(i=> [i,21.05,32,1]),['1','3','5','7','#'].map(i=> [i,21.03,32,1]),['4'].map(i=> [i,21.17,32,1]),['a','k'].map(i=> [i,18.41,32,1]),['b','d','q'].map(i=> [i,20.69,32,1]),['c'].map(i=> [i,18.52,32,1]),['e'].map(i=> [i,18.83,32,1]),['g'].map(i=> [i,20.53,32,1]),['h'].map(i=> [i,19.77,32,1]),['i'].map(i=> [i,7.63,32,1]),['j'].map(i=> [i,8.5,32,1]),['m'].map(i=> [i,29.81,32,1]),['n','u'].map(i=> [i,19.8,32,1]),['p'].map(i=> [i,20.72,32,1]),['r'].map(i=> [i,13.77,32,1]),['s'].map(i=> [i,17.2,32,1]),['t'].map(i=> [i,12.56,32,1]),['v'].map(i=> [i,17.42,32,1]),['w'].map(i=> [i,24.66,32,1]),['x'].map(i=> [i,17.17,32,1]),['y'].map(i=> [i,17.73,32,1]),['z'].map(i=> [i,15.95,32,1]),['A'].map(i=> [i,24.02,32,1]),['B'].map(i=> [i,22.8,32,1]),['C'].map(i=> [i,24.2,32,1]),['D'].map(i=> [i,24.28,32,1]),['E'].map(i=> [i,20.59,32,1]),['F','L'].map(i=> [i,19.28,32,1]),['G'].map(i=> [i,24.14,32,1]),['H'].map(i=> [i,24.91,32,1]),['J'].map(i=> [i,16.27,32,1]),['K'].map(i=> [i,22.64,32,1]),['M'].map(i=> [i,30.27,32,1]),['N'].map(i=> [i,24.78,32,1]),['O','Q'].map(i=> [i,25.45,32,1]),['R'].map(i=> [i,23.25,32,1]),['S','Z'].map(i=> [i,21.42,32,1]),['T'].map(i=> [i,20.78,32,1]),['V'].map(i=> [i,22.97,32,1]),['W'].map(i=> [i,32.2,32,1]),['X'].map(i=> [i,23.13,32,1]),['Y'].map(i=> [i,22.03,32,1]),[',',':'].map(i=> [i,7.69,32,1]),['.',';'].map(i=> [i,7.7,32,1]),['/'].map(i=> [i,15.17,32,1]),['?'].map(i=> [i,18.3,32,1]),['\"','(',')'].map(i=> [i,11.41,32,1]),['<','>'].map(i=> [i,16.41,32,1]),['=','+'].map(i=> [i,21,32,1]),['~'].map(i=> [i,17.39,32,1]),['!'].map(i=> [i,9.97,32,1]),['@'].map(i=> [i,28.98,32,1]),['$'].map(i=> [i,20.81,32,1]),['%'].map(i=> [i,28.14,32,1]),['^'].map(i=> [i,17.08,32,1]),['&'].map(i=> [i,25.52,32,1]),['*',']'].map(i=> [i,11.09,32,1]),['_'].map(i=> [i,16.05,32,1]),['-'].map(i=> [i,11.47,32,1]),['{','}'].map(i=> [i,11.13,32,1]),['['].map(i=> [i,11.08,32,1]),['\\'].map(i=> [i,15.19,32,1]),['|'].map(i=> [i,12.02,32,1]),['\''].map(i=> [i,6.25,32,1])],"Hiragino Sans GB34.7":[['我','》','（','）','”'].map(i=> [i,34.7,35,1]),['！','？','《','；','，','、','。','：','【','】','“','‘','’','⊙','⭘','℃'].map(i=> [i,34.72,35,1]),[' '].map(i=> [i,17.35,35,1]),['=','+'].map(i=> [i,22.78,35,1]),['o'].map(i=> [i,21.7,35,1]),['`'].map(i=> [i,17.38,35,1]),['<','>'].map(i=> [i,17.78,35,1]),['☑','☐'].map(i=> [i,28.81,35,1]),['↵'].map(i=> [i,0,35,1]),['I'].map(i=> [i,8.66,35,1]),['l'].map(i=> [i,8.08,35,1]),['P'].map(i=> [i,22.98,35,1]),['0','1','2','3','5','6','7','8','9','#'].map(i=> [i,22.81,35,1]),['4'].map(i=> [i,22.95,35,1]),['a','k'].map(i=> [i,19.97,35,1]),['b','d','q'].map(i=> [i,22.44,35,1]),['c'].map(i=> [i,20.08,35,1]),['e'].map(i=> [i,20.41,35,1]),['f'].map(i=> [i,14.5,35,1]),['g'].map(i=> [i,22.25,35,1]),['h'].map(i=> [i,21.42,35,1]),['i'].map(i=> [i,8.28,35,1]),['j'].map(i=> [i,9.2,35,1]),['m'].map(i=> [i,32.31,35,1]),['n','u'].map(i=> [i,21.47,35,1]),['p'].map(i=> [i,22.45,35,1]),['r'].map(i=> [i,14.94,35,1]),['s'].map(i=> [i,18.66,35,1]),['t'].map(i=> [i,13.61,35,1]),['v'].map(i=> [i,18.89,35,1]),['w'].map(i=> [i,26.73,35,1]),['x'].map(i=> [i,18.61,35,1]),['y'].map(i=> [i,19.23,35,1]),['z'].map(i=> [i,17.3,35,1]),['A'].map(i=> [i,26.05,35,1]),['B'].map(i=> [i,24.72,35,1]),['C'].map(i=> [i,26.25,35,1]),['D'].map(i=> [i,26.31,35,1]),['E'].map(i=> [i,22.33,35,1]),['F','L'].map(i=> [i,20.91,35,1]),['G'].map(i=> [i,26.19,35,1]),['H'].map(i=> [i,27.02,35,1]),['J'].map(i=> [i,17.64,35,1]),['K'].map(i=> [i,24.55,35,1]),['M'].map(i=> [i,32.8,35,1]),['N'].map(i=> [i,26.88,35,1]),['O','Q'].map(i=> [i,27.61,35,1]),['R'].map(i=> [i,25.2,35,1]),['S','Z'].map(i=> [i,23.23,35,1]),['T'].map(i=> [i,22.53,35,1]),['U'].map(i=> [i,26.84,35,1]),['V'].map(i=> [i,24.89,35,1]),['W'].map(i=> [i,34.92,35,1]),['X'].map(i=> [i,25.06,35,1]),['Y'].map(i=> [i,23.89,35,1]),[',','.',':',';'].map(i=> [i,8.34,35,1]),['/'].map(i=> [i,16.47,35,1]),['?'].map(i=> [i,19.83,35,1]),['\"','('].map(i=> [i,12.36,35,1]),['~'].map(i=> [i,18.86,35,1]),['!'].map(i=> [i,10.8,35,1]),['@'].map(i=> [i,31.42,35,1]),['$'].map(i=> [i,22.58,35,1]),['%'].map(i=> [i,30.52,35,1]),['^'].map(i=> [i,18.5,35,1]),['&'].map(i=> [i,27.67,35,1]),['*',']'].map(i=> [i,12.03,35,1]),[')'].map(i=> [i,12.38,35,1]),['_'].map(i=> [i,17.41,35,1]),['-'].map(i=> [i,12.44,35,1]),['{'].map(i=> [i,12.05,35,1]),['}'].map(i=> [i,12.06,35,1]),['['].map(i=> [i,12.02,35,1]),['\\'].map(i=> [i,16.45,35,1]),['|'].map(i=> [i,13.03,35,1]),['\''].map(i=> [i,6.78,35,1]),['◯'].map(i=> [i,33.09,35,1])],"Hiragino Sans GB37.3":[['我','！','？','《','》','，','、','。','（','：','【','】','“','‘','’','⊙','⭘','℃'].map(i=> [i,37.31,37,1]),['；','）','”'].map(i=> [i,37.33,37,1]),[' '].map(i=> [i,18.65,37,1]),['=','+'].map(i=> [i,24.48,37,1]),['o'].map(i=> [i,23.33,37,1]),['>'].map(i=> [i,19.11,37,1]),['`'].map(i=> [i,18.67,37,1]),['☐'].map(i=> [i,30.98,37,1]),['☑'].map(i=> [i,30.97,37,1]),['↵'].map(i=> [i,0,37,1]),['r'].map(i=> [i,16.05,37,1]),['/','\\'].map(i=> [i,17.69,37,1]),['-'].map(i=> [i,13.38,37,1]),['P'].map(i=> [i,24.7,37,1]),['e'].map(i=> [i,21.95,37,1]),['0','2','5','6','8','9'].map(i=> [i,24.52,37,1]),['1','3','7','#'].map(i=> [i,24.53,37,1]),['4'].map(i=> [i,24.67,37,1]),['a','k'].map(i=> [i,21.47,37,1]),['b','d','q'].map(i=> [i,24.11,37,1]),['c'].map(i=> [i,21.58,37,1]),['f'].map(i=> [i,15.56,37,1]),['g'].map(i=> [i,23.92,37,1]),['h'].map(i=> [i,23.03,37,1]),['i'].map(i=> [i,8.89,37,1]),['j'].map(i=> [i,9.91,37,1]),['l'].map(i=> [i,8.66,37,1]),['m'].map(i=> [i,34.73,37,1]),['n','u'].map(i=> [i,23.06,37,1]),['p'].map(i=> [i,24.16,37,1]),['s'].map(i=> [i,20.05,37,1]),['t'].map(i=> [i,14.64,37,1]),['v'].map(i=> [i,20.31,37,1]),['w'].map(i=> [i,28.73,37,1]),['x'].map(i=> [i,20.02,37,1]),['y'].map(i=> [i,20.67,37,1]),['z'].map(i=> [i,18.59,37,1]),['A'].map(i=> [i,27.98,37,1]),['B'].map(i=> [i,26.58,37,1]),['C'].map(i=> [i,28.22,37,1]),['D'].map(i=> [i,28.28,37,1]),['E'].map(i=> [i,24,37,1]),['F'].map(i=> [i,22.47,37,1]),['G'].map(i=> [i,28.14,37,1]),['H'].map(i=> [i,29.05,37,1]),['I'].map(i=> [i,9.3,37,1]),['J'].map(i=> [i,18.97,37,1]),['K'].map(i=> [i,26.38,37,1]),['L'].map(i=> [i,22.48,37,1]),['M'].map(i=> [i,35.25,37,1]),['N'].map(i=> [i,28.89,37,1]),['O','Q'].map(i=> [i,29.67,37,1]),['R'].map(i=> [i,27.09,37,1]),['S','Z'].map(i=> [i,24.97,37,1]),['T'].map(i=> [i,24.23,37,1]),['U'].map(i=> [i,28.84,37,1]),['V'].map(i=> [i,26.75,37,1]),['W'].map(i=> [i,37.53,37,1]),['X'].map(i=> [i,26.95,37,1]),['Y'].map(i=> [i,25.67,37,1]),[',','.',':',';'].map(i=> [i,8.97,37,1]),['?'].map(i=> [i,21.31,37,1]),['\"',')'].map(i=> [i,13.28,37,1]),['<'].map(i=> [i,19.13,37,1]),['~'].map(i=> [i,20.27,37,1]),['!'].map(i=> [i,11.61,37,1]),['@'].map(i=> [i,33.77,37,1]),['$'].map(i=> [i,24.27,37,1]),['%'].map(i=> [i,32.8,37,1]),['^'].map(i=> [i,19.89,37,1]),['&'].map(i=> [i,29.75,37,1]),['*','[',']'].map(i=> [i,12.92,37,1]),['('].map(i=> [i,13.3,37,1]),['_'].map(i=> [i,18.69,37,1]),['{'].map(i=> [i,12.95,37,1]),['}'].map(i=> [i,12.97,37,1]),['|'].map(i=> [i,14,37,1]),['\''].map(i=> [i,7.3,37,1]),['◯'].map(i=> [i,35.59,37,1])],"Hiragino Sans GB48":[['我','！','？','《','》','；','，','、','。','（','）','：','【','】','“','”','‘','’'].map(i=> [i,48,48,1]),[' '].map(i=> [i,24,48,1]),['f'].map(i=> [i,20.03,48,1]),['=','+'].map(i=> [i,31.5,48,1]),['o'].map(i=> [i,30.02,48,1]),['`'].map(i=> [i,24.02,48,1]),['<','>'].map(i=> [i,24.59,48,1]),['☑','☐'].map(i=> [i,39.86,48,1]),['⊙','⭘','℃'].map(i=> [i,48.02,48,1]),['◯'].map(i=> [i,45.8,48,1]),['↵'].map(i=> [i,0,48,1]),['l'].map(i=> [i,11.16,48,1]),['U'].map(i=> [i,37.11,48,1]),['e'].map(i=> [i,28.23,48,1]),['0','1','3','5','6','8','9','#'].map(i=> [i,31.55,48,1]),['2','7'].map(i=> [i,31.56,48,1]),['4'].map(i=> [i,31.75,48,1]),['a'].map(i=> [i,27.63,48,1]),['b','d'].map(i=> [i,31.02,48,1]),['c'].map(i=> [i,27.77,48,1]),['g'].map(i=> [i,30.78,48,1]),['h'].map(i=> [i,29.64,48,1]),['i'].map(i=> [i,11.44,48,1]),['j'].map(i=> [i,12.73,48,1]),['k'].map(i=> [i,27.61,48,1]),['m'].map(i=> [i,44.7,48,1]),['n'].map(i=> [i,29.67,48,1]),['p'].map(i=> [i,31.08,48,1]),['q'].map(i=> [i,31.03,48,1]),['r'].map(i=> [i,20.64,48,1]),['s'].map(i=> [i,25.8,48,1]),['t'].map(i=> [i,18.83,48,1]),['u'].map(i=> [i,29.69,48,1]),['v'].map(i=> [i,26.13,48,1]),['w'].map(i=> [i,36.97,48,1]),['x'].map(i=> [i,25.75,48,1]),['y'].map(i=> [i,26.61,48,1]),['z'].map(i=> [i,23.92,48,1]),['A'].map(i=> [i,36.02,48,1]),['B'].map(i=> [i,34.19,48,1]),['C'].map(i=> [i,36.3,48,1]),['D'].map(i=> [i,36.39,48,1]),['E'].map(i=> [i,30.88,48,1]),['F','L'].map(i=> [i,28.92,48,1]),['G'].map(i=> [i,36.2,48,1]),['H'].map(i=> [i,37.36,48,1]),['I'].map(i=> [i,11.97,48,1]),['J'].map(i=> [i,24.41,48,1]),['K'].map(i=> [i,33.94,48,1]),['M'].map(i=> [i,45.38,48,1]),['N'].map(i=> [i,37.17,48,1]),['O'].map(i=> [i,38.17,48,1]),['P'].map(i=> [i,31.78,48,1]),['Q'].map(i=> [i,38.19,48,1]),['R'].map(i=> [i,34.86,48,1]),['S','Z'].map(i=> [i,32.13,48,1]),['T'].map(i=> [i,31.17,48,1]),['V'].map(i=> [i,34.42,48,1]),['W'].map(i=> [i,48.31,48,1]),['X'].map(i=> [i,34.67,48,1]),['Y'].map(i=> [i,33.03,48,1]),[','].map(i=> [i,11.55,48,1]),['.',':',';'].map(i=> [i,11.53,48,1]),['/','\\'].map(i=> [i,22.77,48,1]),['?'].map(i=> [i,27.42,48,1]),['\"',')'].map(i=> [i,17.11,48,1]),['~'].map(i=> [i,26.08,48,1]),['!'].map(i=> [i,14.94,48,1]),['@'].map(i=> [i,43.47,48,1]),['$'].map(i=> [i,31.22,48,1]),['%'].map(i=> [i,42.2,48,1]),['^'].map(i=> [i,25.59,48,1]),['&'].map(i=> [i,38.28,48,1]),['*','[',']'].map(i=> [i,16.63,48,1]),['('].map(i=> [i,17.09,48,1]),['_'].map(i=> [i,24.06,48,1]),['-'].map(i=> [i,17.2,48,1]),['{','}'].map(i=> [i,16.67,48,1]),['|'].map(i=> [i,18.02,48,1]),['\''].map(i=> [i,9.38,48,1])],"Hiragino Sans GB58.7":[['我','》','；','）','”','℃'].map(i=> [i,58.7,59,1]),['！','？','《','，','、','。','（','：','【','】','“','‘','’','⊙','⭘'].map(i=> [i,58.72,59,1]),[' '].map(i=> [i,29.35,59,1]),['0','1','2','3','5','6','7','9','#'].map(i=> [i,38.58,59,1]),['4'].map(i=> [i,38.83,59,1]),['8'].map(i=> [i,38.59,59,1]),['a','k'].map(i=> [i,33.77,59,1]),['b','d','q'].map(i=> [i,37.94,59,1]),['c'].map(i=> [i,33.94,59,1]),['e'].map(i=> [i,34.53,59,1]),['f'].map(i=> [i,24.5,59,1]),['g'].map(i=> [i,37.64,59,1]),['h'].map(i=> [i,36.23,59,1]),['i'].map(i=> [i,13.98,59,1]),['j'].map(i=> [i,15.58,59,1]),['l'].map(i=> [i,13.63,59,1]),['m'].map(i=> [i,54.67,59,1]),['n'].map(i=> [i,36.3,59,1]),['o'].map(i=> [i,36.7,59,1]),['p'].map(i=> [i,37.98,59,1]),['r'].map(i=> [i,25.27,59,1]),['s'].map(i=> [i,31.53,59,1]),['t'].map(i=> [i,23.03,59,1]),['u'].map(i=> [i,36.28,59,1]),['v'].map(i=> [i,31.95,59,1]),['w'].map(i=> [i,45.22,59,1]),['x'].map(i=> [i,31.47,59,1]),['y'].map(i=> [i,32.55,59,1]),['z'].map(i=> [i,29.25,59,1]),['A'].map(i=> [i,44.03,59,1]),['B'].map(i=> [i,41.81,59,1]),['C'].map(i=> [i,44.39,59,1]),['D'].map(i=> [i,44.52,59,1]),['E'].map(i=> [i,37.75,59,1]),['F','L'].map(i=> [i,35.36,59,1]),['G'].map(i=> [i,44.28,59,1]),['H'].map(i=> [i,45.67,59,1]),['I'].map(i=> [i,14.64,59,1]),['J'].map(i=> [i,29.83,59,1]),['K'].map(i=> [i,41.52,59,1]),['M'].map(i=> [i,55.48,59,1]),['N'].map(i=> [i,45.45,59,1]),['O'].map(i=> [i,46.69,59,1]),['P'].map(i=> [i,38.88,59,1]),['Q'].map(i=> [i,46.67,59,1]),['R'].map(i=> [i,42.64,59,1]),['S','Z'].map(i=> [i,39.28,59,1]),['T'].map(i=> [i,38.11,59,1]),['U'].map(i=> [i,45.39,59,1]),['V'].map(i=> [i,42.11,59,1]),['W'].map(i=> [i,59.06,59,1]),['X'].map(i=> [i,42.39,59,1]),['Y'].map(i=> [i,40.41,59,1]),[',','.',':',';'].map(i=> [i,14.11,59,1]),['/'].map(i=> [i,27.83,59,1]),['?'].map(i=> [i,33.55,59,1]),['\"',')'].map(i=> [i,20.91,59,1]),['<'].map(i=> [i,30.06,59,1]),['>'].map(i=> [i,30.08,59,1]),['=','+'].map(i=> [i,38.52,59,1]),['~'].map(i=> [i,31.89,59,1]),['`'].map(i=> [i,29.36,59,1]),['!'].map(i=> [i,18.28,59,1]),['@'].map(i=> [i,53.14,59,1]),['$'].map(i=> [i,38.17,59,1]),['%'].map(i=> [i,51.61,59,1]),['^'].map(i=> [i,31.3,59,1]),['&'].map(i=> [i,46.81,59,1]),['*',']'].map(i=> [i,20.31,59,1]),['('].map(i=> [i,20.92,59,1]),['_'].map(i=> [i,29.42,59,1]),['-'].map(i=> [i,21.03,59,1]),['{','}'].map(i=> [i,20.39,59,1]),['['].map(i=> [i,20.33,59,1]),['\\'].map(i=> [i,27.84,59,1]),['|'].map(i=> [i,22.03,59,1]),['\''].map(i=> [i,11.45,59,1]),['☑','☐'].map(i=> [i,48.73,59,1]),['◯'].map(i=> [i,56,59,1]),['↵'].map(i=> [i,0,59,1])],"STHeiti10.7":[['我','》','（','“','⭘'].map(i=> [i,10.7,10,1]),['！','？','《','；','，','、','。','）','：','【','】','”','‘','’','⊙','℃'].map(i=> [i,10.72,10,1]),[' '].map(i=> [i,5.35,10,1]),['U'].map(i=> [i,7.03,10,1]),['A','N'].map(i=> [i,7.94,10,1]),['Z'].map(i=> [i,5.14,10,1]),['_'].map(i=> [i,5.36,10,1]),['w','☐'].map(i=> [i,8.91,10,1]),['☑'].map(i=> [i,8.89,10,1]),['◯'].map(i=> [i,10.22,10,1]),['↵'].map(i=> [i,0,10,1]),['x'].map(i=> [i,5.16,10,1]),['s'].map(i=> [i,4.17,10,1]),['D'].map(i=> [i,7.97,10,1]),['%'].map(i=> [i,8.31,10,1]),['B'].map(i=> [i,6.16,10,1]),['^','|'].map(i=> [i,7.2,10,1]),['t'].map(i=> [i,3.64,10,1]),['c'].map(i=> [i,6.94,10,1]),['y','E'].map(i=> [i,5.75,10,1]),['0','1','3','4','6','8','9','v','$'].map(i=> [i,5.94,10,1]),['S'].map(i=> [i,5.34,10,1]),['&'].map(i=> [i,8.11,10,1]),['e'].map(i=> [i,6.97,10,1]),['2','5','7'].map(i=> [i,5.95,10,1]),['a','H'].map(i=> [i,7.33,10,1]),['b','p','q'].map(i=> [i,7.31,10,1]),['d'].map(i=> [i,7.34,10,1]),['f'].map(i=> [i,3.38,10,1]),['g'].map(i=> [i,7.22,10,1]),['h','n'].map(i=> [i,6.55,10,1]),['i','l'].map(i=> [i,2.16,10,1]),['j'].map(i=> [i,2.19,10,1]),['k'].map(i=> [i,5.39,10,1]),['m'].map(i=> [i,10.05,10,1]),['o'].map(i=> [i,7.02,10,1]),['r'].map(i=> [i,3.23,10,1]),['u','X'].map(i=> [i,6.53,10,1]),['z','T','*'].map(i=> [i,4.56,10,1]),['C'].map(i=> [i,8.72,10,1]),['F'].map(i=> [i,5.2,10,1]),['G'].map(i=> [i,9.34,10,1]),['I'].map(i=> [i,2.44,10,1]),['J'].map(i=> [i,5.17,10,1]),['K'].map(i=> [i,6.34,10,1]),['L'].map(i=> [i,4.95,10,1]),['M'].map(i=> [i,9.84,10,1]),['O'].map(i=> [i,9.31,10,1]),['P','Y'].map(i=> [i,6.36,10,1]),['Q'].map(i=> [i,9.33,10,1]),['R'].map(i=> [i,6.52,10,1]),['V'].map(i=> [i,7.53,10,1]),['W'].map(i=> [i,10.28,10,1]),[',','.',':',';'].map(i=> [i,2.98,10,1]),['/'].map(i=> [i,4.69,10,1]),['?'].map(i=> [i,5.31,10,1]),['\"'].map(i=> [i,3.31,10,1]),['<','>','=','~','+'].map(i=> [i,6.5,10,1]),['`'].map(i=> [i,4.06,10,1]),['!'].map(i=> [i,3.17,10,1]),['@'].map(i=> [i,9.3,10,1]),['#'].map(i=> [i,7.72,10,1]),['(',')'].map(i=> [i,3.97,10,1]),['-'].map(i=> [i,3.56,10,1]),['{',']'].map(i=> [i,3.78,10,1]),['}','['].map(i=> [i,3.77,10,1]),['\\'].map(i=> [i,6.48,10,1]),['\''].map(i=> [i,2.14,10,1])],"STHeiti12":[['!'].map(i=> [i,3.56,12,1]),['?'].map(i=> [i,5.95,12,1]),['k'].map(i=> [i,6.03,12,1]),['↵'].map(i=> [i,0,12,1]),['我','！','？','《','》','；','，','、','。','（','）','：','【','】','“','”','‘','’'].map(i=> [i,12,12,1]),[' ','S'].map(i=> [i,6,12,1]),['A','N'].map(i=> [i,8.89,12,1]),['_'].map(i=> [i,6.02,12,1]),['Z'].map(i=> [i,5.77,12,1]),['w','☑'].map(i=> [i,9.98,12,1]),['☐'].map(i=> [i,9.97,12,1]),['⊙','⭘','℃'].map(i=> [i,12.02,12,1]),['x'].map(i=> [i,5.78,12,1]),['s'].map(i=> [i,4.67,12,1]),['D'].map(i=> [i,8.94,12,1]),['F'].map(i=> [i,5.83,12,1]),['d'].map(i=> [i,8.23,12,1]),['|'].map(i=> [i,8.08,12,1]),['B'].map(i=> [i,6.91,12,1]),['c'].map(i=> [i,7.77,12,1]),['y','E'].map(i=> [i,6.45,12,1]),['0','2','4','6','9'].map(i=> [i,6.67,12,1]),['o','U'].map(i=> [i,7.88,12,1]),['◯'].map(i=> [i,11.47,12,1]),['1','3','5','7','8','v','$'].map(i=> [i,6.66,12,1]),['a','b','q','H'].map(i=> [i,8.2,12,1]),['e'].map(i=> [i,7.83,12,1]),['f'].map(i=> [i,3.78,12,1]),['g','^'].map(i=> [i,8.09,12,1]),['h','X'].map(i=> [i,7.33,12,1]),['i','l'].map(i=> [i,2.42,12,1]),['j'].map(i=> [i,2.45,12,1]),['m'].map(i=> [i,11.27,12,1]),['n'].map(i=> [i,7.34,12,1]),['p'].map(i=> [i,8.19,12,1]),['r'].map(i=> [i,3.63,12,1]),['t'].map(i=> [i,4.09,12,1]),['u'].map(i=> [i,7.31,12,1]),['z','*'].map(i=> [i,5.11,12,1]),['C'].map(i=> [i,9.78,12,1]),['G'].map(i=> [i,10.48,12,1]),['I'].map(i=> [i,2.73,12,1]),['J'].map(i=> [i,5.8,12,1]),['K'].map(i=> [i,7.11,12,1]),['L'].map(i=> [i,5.56,12,1]),['M'].map(i=> [i,11.05,12,1]),['O'].map(i=> [i,10.44,12,1]),['P','Y'].map(i=> [i,7.13,12,1]),['Q'].map(i=> [i,10.47,12,1]),['R','<','='].map(i=> [i,7.3,12,1]),['T'].map(i=> [i,5.13,12,1]),['V'].map(i=> [i,8.44,12,1]),['W'].map(i=> [i,11.53,12,1]),[',','.',';'].map(i=> [i,3.34,12,1]),['/'].map(i=> [i,5.25,12,1]),['\"'].map(i=> [i,3.73,12,1]),[':'].map(i=> [i,3.33,12,1]),['>','~','+','\\'].map(i=> [i,7.28,12,1]),['`'].map(i=> [i,4.55,12,1]),['@'].map(i=> [i,10.42,12,1]),['#'].map(i=> [i,8.66,12,1]),['%'].map(i=> [i,9.31,12,1]),['&'].map(i=> [i,9.09,12,1]),['('].map(i=> [i,4.45,12,1]),[')'].map(i=> [i,4.44,12,1]),['-'].map(i=> [i,4,12,1]),['{','['].map(i=> [i,4.23,12,1]),['}',']'].map(i=> [i,4.22,12,1]),['\''].map(i=> [i,2.39,12,1])],"STHeiti13.3":[['我','！','？','《','》','，','、','。','（','：','【','】','“','‘','’','⊙','⭘','℃'].map(i=> [i,13.31,13,1]),['；','）','”'].map(i=> [i,13.33,13,1]),[' '].map(i=> [i,6.65,13,1]),['T'].map(i=> [i,5.69,13,1]),['A','N'].map(i=> [i,9.86,13,1]),['x'].map(i=> [i,6.39,13,1]),['_'].map(i=> [i,6.67,13,1]),['☑'].map(i=> [i,11.05,13,1]),['☐'].map(i=> [i,11.06,13,1]),['◯'].map(i=> [i,12.7,13,1]),['↵'].map(i=> [i,0,13,1]),['Z'].map(i=> [i,6.41,13,1]),[',','.',':',';'].map(i=> [i,3.7,13,1]),['s'].map(i=> [i,5.19,13,1]),['^','|'].map(i=> [i,8.95,13,1]),['f'].map(i=> [i,4.19,13,1]),['0','3','5','7','v','$'].map(i=> [i,7.38,13,1]),['1','2','4','6','8','9'].map(i=> [i,7.39,13,1]),['a','p'].map(i=> [i,9.09,13,1]),['b','q'].map(i=> [i,9.08,13,1]),['d'].map(i=> [i,9.13,13,1]),['c'].map(i=> [i,8.63,13,1]),['e'].map(i=> [i,8.66,13,1]),['g'].map(i=> [i,8.97,13,1]),['h','X'].map(i=> [i,8.13,13,1]),['i'].map(i=> [i,2.69,13,1]),['j'].map(i=> [i,2.7,13,1]),['k'].map(i=> [i,6.7,13,1]),['l'].map(i=> [i,2.67,13,1]),['m'].map(i=> [i,12.48,13,1]),['n'].map(i=> [i,8.14,13,1]),['o','U'].map(i=> [i,8.72,13,1]),['r'].map(i=> [i,4.02,13,1]),['t'].map(i=> [i,4.52,13,1]),['u'].map(i=> [i,8.11,13,1]),['w'].map(i=> [i,11.08,13,1]),['y','E'].map(i=> [i,7.14,13,1]),['z','*'].map(i=> [i,5.67,13,1]),['B'].map(i=> [i,7.66,13,1]),['C'].map(i=> [i,10.83,13,1]),['D'].map(i=> [i,9.91,13,1]),['F'].map(i=> [i,6.47,13,1]),['G','Q'].map(i=> [i,11.61,13,1]),['H'].map(i=> [i,9.11,13,1]),['I'].map(i=> [i,3.02,13,1]),['J'].map(i=> [i,6.42,13,1]),['K','Y'].map(i=> [i,7.88,13,1]),['L'].map(i=> [i,6.17,13,1]),['M'].map(i=> [i,12.23,13,1]),['O'].map(i=> [i,11.56,13,1]),['P'].map(i=> [i,7.89,13,1]),['R','<','=','~','+'].map(i=> [i,8.08,13,1]),['S'].map(i=> [i,6.64,13,1]),['V'].map(i=> [i,9.36,13,1]),['W'].map(i=> [i,12.78,13,1]),['/'].map(i=> [i,5.83,13,1]),['?'].map(i=> [i,6.59,13,1]),['\"'].map(i=> [i,4.13,13,1]),['>','\\'].map(i=> [i,8.06,13,1]),['`'].map(i=> [i,5.05,13,1]),['!'].map(i=> [i,3.94,13,1]),['@'].map(i=> [i,11.55,13,1]),['#'].map(i=> [i,9.59,13,1]),['%'].map(i=> [i,10.33,13,1]),['&'].map(i=> [i,10.08,13,1]),['(',')'].map(i=> [i,4.92,13,1]),['-'].map(i=> [i,4.42,13,1]),['{','}',']'].map(i=> [i,4.69,13,1]),['['].map(i=> [i,4.67,13,1]),['\''].map(i=> [i,2.66,13,1])],"STHeiti14":[['-'].map(i=> [i,4.67,14,1]),['J'].map(i=> [i,6.77,14,1]),['↵'].map(i=> [i,0,14,1]),['我','！','？','《','》','；','，','、','。','（','）','：','【','】','“','”','‘','’'].map(i=> [i,14,14,1]),[' ','S'].map(i=> [i,7,14,1]),['A','N'].map(i=> [i,10.38,14,1]),['o','U'].map(i=> [i,9.19,14,1]),['_'].map(i=> [i,7.02,14,1]),['w','☑','☐'].map(i=> [i,11.64,14,1]),['⊙','⭘','℃'].map(i=> [i,14.02,14,1]),['x','Z'].map(i=> [i,6.73,14,1]),['s'].map(i=> [i,5.45,14,1]),['%'].map(i=> [i,10.88,14,1]),['d'].map(i=> [i,9.61,14,1]),['^','|'].map(i=> [i,9.42,14,1]),['◯'].map(i=> [i,13.36,14,1]),['h','n','X'].map(i=> [i,8.55,14,1]),['I'].map(i=> [i,3.17,14,1]),['#'].map(i=> [i,10.09,14,1]),['a','p','q'].map(i=> [i,9.56,14,1]),['t'].map(i=> [i,4.75,14,1]),['B'].map(i=> [i,8.06,14,1]),['c'].map(i=> [i,9.08,14,1]),['y'].map(i=> [i,7.53,14,1]),['0','1','3','5','6','8','$'].map(i=> [i,7.77,14,1]),['e'].map(i=> [i,9.11,14,1]),['2','4','7','9','v'].map(i=> [i,7.78,14,1]),['b','H'].map(i=> [i,9.58,14,1]),['f'].map(i=> [i,4.42,14,1]),['g'].map(i=> [i,9.44,14,1]),['i','l'].map(i=> [i,2.81,14,1]),['j'].map(i=> [i,2.86,14,1]),['k'].map(i=> [i,7.05,14,1]),['m'].map(i=> [i,13.16,14,1]),['r'].map(i=> [i,4.23,14,1]),['u'].map(i=> [i,8.53,14,1]),['z'].map(i=> [i,5.95,14,1]),['C'].map(i=> [i,11.39,14,1]),['D'].map(i=> [i,10.44,14,1]),['E'].map(i=> [i,7.52,14,1]),['F'].map(i=> [i,6.81,14,1]),['G','Q'].map(i=> [i,12.22,14,1]),['K','P'].map(i=> [i,8.3,14,1]),['L'].map(i=> [i,6.48,14,1]),['M'].map(i=> [i,12.88,14,1]),['O'].map(i=> [i,12.19,14,1]),['R','<','>','=','~','+'].map(i=> [i,8.5,14,1]),['T','*'].map(i=> [i,5.97,14,1]),['V'].map(i=> [i,9.84,14,1]),['W'].map(i=> [i,13.45,14,1]),['Y'].map(i=> [i,8.31,14,1]),[',','.',':',';'].map(i=> [i,3.89,14,1]),['/'].map(i=> [i,6.14,14,1]),['?'].map(i=> [i,6.94,14,1]),['\"'].map(i=> [i,4.34,14,1]),['`'].map(i=> [i,5.31,14,1]),['!'].map(i=> [i,4.14,14,1]),['@'].map(i=> [i,12.16,14,1]),['&'].map(i=> [i,10.61,14,1]),['('].map(i=> [i,5.19,14,1]),[')'].map(i=> [i,5.17,14,1]),['{','['].map(i=> [i,4.92,14,1]),['}',']'].map(i=> [i,4.94,14,1]),['\\'].map(i=> [i,8.48,14,1]),['\''].map(i=> [i,2.78,14,1])],"STHeiti15":[['\"'].map(i=> [i,4.66,15,1]),['g','^'].map(i=> [i,10.11,15,1]),['↵'].map(i=> [i,0,15,1]),[' '].map(i=> [i,7.5,15,1]),['我','！','？','《','》','；','，','、','。','（','）','：','【','】','“','”','‘','’'].map(i=> [i,15,15,1]),['o','U'].map(i=> [i,9.84,15,1]),['x'].map(i=> [i,7.2,15,1]),['_'].map(i=> [i,7.52,15,1]),['☑','☐'].map(i=> [i,12.47,15,1]),['⊙','⭘','℃'].map(i=> [i,15.02,15,1]),['◯'].map(i=> [i,14.31,15,1]),['&'].map(i=> [i,11.36,15,1]),['a','b','p'].map(i=> [i,10.25,15,1]),['k'].map(i=> [i,7.55,15,1]),['C'].map(i=> [i,12.22,15,1]),['h','n','X'].map(i=> [i,9.16,15,1]),[')'].map(i=> [i,5.55,15,1]),['J'].map(i=> [i,7.25,15,1]),['('].map(i=> [i,5.56,15,1]),['P','Y'].map(i=> [i,8.89,15,1]),['Z'].map(i=> [i,7.22,15,1]),['s'].map(i=> [i,5.84,15,1]),['D'].map(i=> [i,11.17,15,1]),['F'].map(i=> [i,7.28,15,1]),['d'].map(i=> [i,10.3,15,1]),['|'].map(i=> [i,10.09,15,1]),['t'].map(i=> [i,5.09,15,1]),['q'].map(i=> [i,10.23,15,1]),['c'].map(i=> [i,9.72,15,1]),['?'].map(i=> [i,7.44,15,1]),['f'].map(i=> [i,4.73,15,1]),['0','1','2','3','4','6','7','8','9','v'].map(i=> [i,8.33,15,1]),['S'].map(i=> [i,7.48,15,1]),['e'].map(i=> [i,9.77,15,1]),['<','=','~','+'].map(i=> [i,9.11,15,1]),['>','\\'].map(i=> [i,9.09,15,1]),['K'].map(i=> [i,8.88,15,1]),['{','}','[',']'].map(i=> [i,5.28,15,1]),['5','$'].map(i=> [i,8.31,15,1]),['i','l'].map(i=> [i,3.02,15,1]),['j'].map(i=> [i,3.06,15,1]),['m'].map(i=> [i,14.09,15,1]),['r'].map(i=> [i,4.53,15,1]),['u'].map(i=> [i,9.14,15,1]),['w'].map(i=> [i,12.48,15,1]),['y','E'].map(i=> [i,8.06,15,1]),['z','*'].map(i=> [i,6.39,15,1]),['A','N'].map(i=> [i,11.11,15,1]),['B'].map(i=> [i,8.63,15,1]),['G'].map(i=> [i,13.09,15,1]),['H'].map(i=> [i,10.27,15,1]),['I'].map(i=> [i,3.41,15,1]),['L'].map(i=> [i,6.95,15,1]),['M'].map(i=> [i,13.8,15,1]),['O'].map(i=> [i,13.06,15,1]),['Q'].map(i=> [i,13.08,15,1]),['R'].map(i=> [i,9.13,15,1]),['T'].map(i=> [i,6.41,15,1]),['V'].map(i=> [i,10.53,15,1]),['W'].map(i=> [i,14.42,15,1]),[',','.',':',';'].map(i=> [i,4.17,15,1]),['/'].map(i=> [i,6.56,15,1]),['`'].map(i=> [i,5.69,15,1]),['!'].map(i=> [i,4.44,15,1]),['@'].map(i=> [i,13.02,15,1]),['#'].map(i=> [i,10.83,15,1]),['%'].map(i=> [i,11.64,15,1]),['-'].map(i=> [i,4.98,15,1]),['\''].map(i=> [i,2.98,15,1])],"STHeiti14.7":[['↵'].map(i=> [i,0,15,1]),['x','Z'].map(i=> [i,7.06,15,1]),['F'].map(i=> [i,7.14,15,1]),[',',';'].map(i=> [i,4.09,15,1]),['y'].map(i=> [i,7.91,15,1]),['T'].map(i=> [i,6.28,15,1]),['%'].map(i=> [i,11.42,15,1]),['D'].map(i=> [i,10.95,15,1]),['.',':'].map(i=> [i,4.08,15,1]),['我','》','（','“'].map(i=> [i,14.7,15,1]),['！','？','《','；','，','、','。','）','：','【','】','”','‘','’','⊙','⭘','℃'].map(i=> [i,14.72,15,1]),[' '].map(i=> [i,7.35,15,1]),['_'].map(i=> [i,7.36,15,1]),['☑','☐'].map(i=> [i,12.22,15,1]),['◯'].map(i=> [i,14.03,15,1]),['/'].map(i=> [i,6.44,15,1]),['{','}','[',']'].map(i=> [i,5.17,15,1]),['J'].map(i=> [i,7.09,15,1]),['o','U'].map(i=> [i,9.64,15,1]),['t'].map(i=> [i,5,15,1]),['d'].map(i=> [i,10.09,15,1]),['g'].map(i=> [i,9.91,15,1]),['s'].map(i=> [i,5.72,15,1]),['B'].map(i=> [i,8.45,15,1]),['f'].map(i=> [i,4.63,15,1]),['A','N'].map(i=> [i,10.91,15,1]),['a','b','p','q'].map(i=> [i,10.05,15,1]),['V'].map(i=> [i,10.34,15,1]),['1','2','3','5','6','7','8','v','$'].map(i=> [i,8.16,15,1]),['C'].map(i=> [i,11.97,15,1]),['c'].map(i=> [i,9.52,15,1]),['S'].map(i=> [i,7.33,15,1]),['m'].map(i=> [i,13.8,15,1]),['r'].map(i=> [i,4.44,15,1]),['0','4','9'].map(i=> [i,8.17,15,1]),['e'].map(i=> [i,9.58,15,1]),['h','n'].map(i=> [i,8.98,15,1]),['i','l'].map(i=> [i,2.95,15,1]),['j'].map(i=> [i,3,15,1]),['k'].map(i=> [i,7.41,15,1]),['u','R'].map(i=> [i,8.95,15,1]),['w'].map(i=> [i,12.23,15,1]),['z'].map(i=> [i,6.25,15,1]),['E'].map(i=> [i,7.89,15,1]),['G'].map(i=> [i,12.83,15,1]),['H'].map(i=> [i,10.06,15,1]),['I'].map(i=> [i,3.34,15,1]),['K'].map(i=> [i,8.7,15,1]),['L'].map(i=> [i,6.81,15,1]),['M'].map(i=> [i,13.52,15,1]),['O'].map(i=> [i,12.78,15,1]),['P','Y'].map(i=> [i,8.72,15,1]),['Q'].map(i=> [i,12.81,15,1]),['W'].map(i=> [i,14.13,15,1]),['X'].map(i=> [i,8.97,15,1]),['?'].map(i=> [i,7.3,15,1]),['\"'].map(i=> [i,4.56,15,1]),['<','>','=','~','\\'].map(i=> [i,8.92,15,1]),['`'].map(i=> [i,5.58,15,1]),['!'].map(i=> [i,4.34,15,1]),['@'].map(i=> [i,12.77,15,1]),['#'].map(i=> [i,10.59,15,1]),['^','|'].map(i=> [i,9.89,15,1]),['&'].map(i=> [i,11.14,15,1]),['*'].map(i=> [i,6.27,15,1]),['(',')'].map(i=> [i,5.44,15,1]),['-'].map(i=> [i,4.89,15,1]),['+'].map(i=> [i,8.94,15,1]),['\''].map(i=> [i,2.92,15,1])],"STHeiti16":[['Y'].map(i=> [i,9.5,16,1]),['↵'].map(i=> [i,0,16,1]),['我','！','？','《','》','；','，','、','。','（','）','：','【','】','“','”','‘','’'].map(i=> [i,16,16,1]),[' '].map(i=> [i,8,16,1]),['_'].map(i=> [i,8.02,16,1]),['Z'].map(i=> [i,7.69,16,1]),['☑','☐'].map(i=> [i,13.3,16,1]),['⊙','⭘','℃'].map(i=> [i,16.02,16,1]),['s'].map(i=> [i,6.22,16,1]),['^','|'].map(i=> [i,10.77,16,1]),['t'].map(i=> [i,5.44,16,1]),['c'].map(i=> [i,10.36,16,1]),['B'].map(i=> [i,9.2,16,1]),['0','2','3','5','6','8','9','v'].map(i=> [i,8.88,16,1]),['S'].map(i=> [i,7.98,16,1]),['o','U'].map(i=> [i,10.5,16,1]),['e'].map(i=> [i,10.42,16,1]),['◯'].map(i=> [i,15.27,16,1]),['1','4','7','$'].map(i=> [i,8.89,16,1]),['a'].map(i=> [i,10.95,16,1]),['d'].map(i=> [i,10.97,16,1]),['b','p'].map(i=> [i,10.92,16,1]),['f'].map(i=> [i,5.05,16,1]),['g'].map(i=> [i,10.78,16,1]),['h'].map(i=> [i,9.77,16,1]),['i','l'].map(i=> [i,3.22,16,1]),['j'].map(i=> [i,3.27,16,1]),['k'].map(i=> [i,8.05,16,1]),['m'].map(i=> [i,15.02,16,1]),['n'].map(i=> [i,9.78,16,1]),['q','H'].map(i=> [i,10.94,16,1]),['r'].map(i=> [i,4.83,16,1]),['u','X'].map(i=> [i,9.75,16,1]),['w'].map(i=> [i,13.31,16,1]),['x'].map(i=> [i,7.7,16,1]),['y','E'].map(i=> [i,8.58,16,1]),['z','T','*'].map(i=> [i,6.83,16,1]),['A','N'].map(i=> [i,11.84,16,1]),['C'].map(i=> [i,13.03,16,1]),['D'].map(i=> [i,11.92,16,1]),['F'].map(i=> [i,7.78,16,1]),['G'].map(i=> [i,13.97,16,1]),['I'].map(i=> [i,3.64,16,1]),['J'].map(i=> [i,7.72,16,1]),['K','P'].map(i=> [i,9.48,16,1]),['L'].map(i=> [i,7.41,16,1]),['M'].map(i=> [i,14.72,16,1]),['O'].map(i=> [i,13.92,16,1]),['Q'].map(i=> [i,13.95,16,1]),['R'].map(i=> [i,9.73,16,1]),['V'].map(i=> [i,11.25,16,1]),['W'].map(i=> [i,15.38,16,1]),[',',':',';'].map(i=> [i,4.45,16,1]),['.'].map(i=> [i,4.44,16,1]),['/'].map(i=> [i,7.02,16,1]),['?'].map(i=> [i,7.94,16,1]),['\"'].map(i=> [i,4.95,16,1]),['<','=','+','\\'].map(i=> [i,9.7,16,1]),['>','~'].map(i=> [i,9.72,16,1]),['`'].map(i=> [i,6.06,16,1]),['!'].map(i=> [i,4.73,16,1]),['@'].map(i=> [i,13.89,16,1]),['#'].map(i=> [i,11.53,16,1]),['%'].map(i=> [i,12.41,16,1]),['&'].map(i=> [i,12.13,16,1]),['('].map(i=> [i,5.91,16,1]),[')'].map(i=> [i,5.92,16,1]),['-'].map(i=> [i,5.33,16,1]),['{','['].map(i=> [i,5.64,16,1]),['}',']'].map(i=> [i,5.63,16,1]),['\''].map(i=> [i,3.19,16,1])],"STHeiti18.7":[['我','》','（','“'].map(i=> [i,18.7,19,1]),['！','？','《','；','，','、','。','）','：','【','】','”','‘','’','⊙','⭘','℃'].map(i=> [i,18.72,19,1]),[' '].map(i=> [i,9.35,19,1]),['-'].map(i=> [i,6.23,19,1]),['A','N'].map(i=> [i,13.86,19,1]),['_'].map(i=> [i,9.38,19,1]),['☐'].map(i=> [i,15.53,19,1]),['w','☑'].map(i=> [i,15.55,19,1]),['◯'].map(i=> [i,17.84,19,1]),['↵'].map(i=> [i,0,19,1]),['J'].map(i=> [i,9.03,19,1]),['.',';'].map(i=> [i,5.19,19,1]),['T'].map(i=> [i,7.98,19,1]),['k'].map(i=> [i,9.41,19,1]),['x'].map(i=> [i,9,19,1]),['h','n'].map(i=> [i,11.42,19,1]),[')'].map(i=> [i,6.91,19,1]),['s'].map(i=> [i,7.27,19,1]),['F'].map(i=> [i,9.08,19,1]),['^','|'].map(i=> [i,12.58,19,1]),['U'].map(i=> [i,12.27,19,1]),['g'].map(i=> [i,12.59,19,1]),['a','p','H'].map(i=> [i,12.78,19,1]),['W'].map(i=> [i,17.97,19,1]),['{','}','[',']'].map(i=> [i,6.58,19,1]),['0','1','2','4','5','6','7','8','9','v','$'].map(i=> [i,10.38,19,1]),['3'].map(i=> [i,10.39,19,1]),['b','q'].map(i=> [i,12.77,19,1]),['d'].map(i=> [i,12.83,19,1]),['c'].map(i=> [i,12.11,19,1]),['e'].map(i=> [i,12.17,19,1]),['f'].map(i=> [i,5.89,19,1]),['i'].map(i=> [i,3.77,19,1]),['j'].map(i=> [i,3.81,19,1]),['l'].map(i=> [i,3.75,19,1]),['m'].map(i=> [i,17.56,19,1]),['o'].map(i=> [i,12.25,19,1]),['r'].map(i=> [i,5.64,19,1]),['t'].map(i=> [i,6.36,19,1]),['u'].map(i=> [i,11.39,19,1]),['y','E'].map(i=> [i,10.03,19,1]),['z','*'].map(i=> [i,7.97,19,1]),['B'].map(i=> [i,10.75,19,1]),['C'].map(i=> [i,15.22,19,1]),['D'].map(i=> [i,13.92,19,1]),['G'].map(i=> [i,16.33,19,1]),['I'].map(i=> [i,4.23,19,1]),['K'].map(i=> [i,11.08,19,1]),['L'].map(i=> [i,8.66,19,1]),['M'].map(i=> [i,17.19,19,1]),['O'].map(i=> [i,16.27,19,1]),['P','Y'].map(i=> [i,11.09,19,1]),['Q'].map(i=> [i,16.3,19,1]),['R','='].map(i=> [i,11.36,19,1]),['S'].map(i=> [i,9.33,19,1]),['V'].map(i=> [i,13.14,19,1]),['X'].map(i=> [i,11.41,19,1]),['Z'].map(i=> [i,8.98,19,1]),[',',':'].map(i=> [i,5.2,19,1]),['/'].map(i=> [i,8.19,19,1]),['?'].map(i=> [i,9.27,19,1]),['\"'].map(i=> [i,5.78,19,1]),['<','>','~','+'].map(i=> [i,11.34,19,1]),['`'].map(i=> [i,7.08,19,1]),['!'].map(i=> [i,5.53,19,1]),['@'].map(i=> [i,16.23,19,1]),['#'].map(i=> [i,13.48,19,1]),['%'].map(i=> [i,14.5,19,1]),['&'].map(i=> [i,14.17,19,1]),['('].map(i=> [i,6.92,19,1]),['\\'].map(i=> [i,11.33,19,1]),['\''].map(i=> [i,3.72,19,1])],"STHeiti19":[['L'].map(i=> [i,8.78,19,1]),['S'].map(i=> [i,9.47,19,1]),['*'].map(i=> [i,8.09,19,1]),['0','1','3','4','6','7','9','v','$'].map(i=> [i,10.55,19,1]),['\"'].map(i=> [i,5.88,19,1]),['m'].map(i=> [i,17.84,19,1]),['↵'].map(i=> [i,0,19,1]),['我','！','？','《','》','；','，','、','。','（','）','：','【','】','“','”','‘','’'].map(i=> [i,19,19,1]),[' '].map(i=> [i,9.5,19,1]),['U'].map(i=> [i,12.47,19,1]),['A','N'].map(i=> [i,14.08,19,1]),['_'].map(i=> [i,9.52,19,1]),['f'].map(i=> [i,5.97,19,1]),['w','☑'].map(i=> [i,15.8,19,1]),['☐'].map(i=> [i,15.78,19,1]),['⊙','⭘','℃'].map(i=> [i,19.02,19,1]),['◯'].map(i=> [i,18.14,19,1]),['s'].map(i=> [i,7.39,19,1]),['e'].map(i=> [i,12.38,19,1]),['u','R'].map(i=> [i,11.56,19,1]),['-'].map(i=> [i,6.31,19,1]),['`'].map(i=> [i,7.2,19,1]),['x','Z'].map(i=> [i,9.14,19,1]),['^','|'].map(i=> [i,12.78,19,1]),['t'].map(i=> [i,6.45,19,1]),['c'].map(i=> [i,12.31,19,1]),['y'].map(i=> [i,10.2,19,1]),['%'].map(i=> [i,14.73,19,1]),['i','l'].map(i=> [i,3.81,19,1]),['2','5','8'].map(i=> [i,10.53,19,1]),['a','q','H'].map(i=> [i,12.98,19,1]),['b','p'].map(i=> [i,12.97,19,1]),['d'].map(i=> [i,13.02,19,1]),['g'].map(i=> [i,12.81,19,1]),['h','n'].map(i=> [i,11.61,19,1]),['j'].map(i=> [i,3.88,19,1]),['k'].map(i=> [i,9.55,19,1]),['o'].map(i=> [i,12.45,19,1]),['r'].map(i=> [i,5.73,19,1]),['z'].map(i=> [i,8.08,19,1]),['B'].map(i=> [i,10.92,19,1]),['C'].map(i=> [i,15.47,19,1]),['D'].map(i=> [i,14.16,19,1]),['E'].map(i=> [i,10.19,19,1]),['F'].map(i=> [i,9.23,19,1]),['G'].map(i=> [i,16.59,19,1]),['I'].map(i=> [i,4.31,19,1]),['J'].map(i=> [i,9.17,19,1]),['K'].map(i=> [i,11.25,19,1]),['M'].map(i=> [i,17.48,19,1]),['O'].map(i=> [i,16.52,19,1]),['P','Y'].map(i=> [i,11.27,19,1]),['Q'].map(i=> [i,16.56,19,1]),['T'].map(i=> [i,8.11,19,1]),['V'].map(i=> [i,13.34,19,1]),['W'].map(i=> [i,18.27,19,1]),['X'].map(i=> [i,11.58,19,1]),[',',':',';'].map(i=> [i,5.28,19,1]),['.'].map(i=> [i,5.27,19,1]),['/'].map(i=> [i,8.33,19,1]),['?'].map(i=> [i,9.42,19,1]),['<','>','=','~','+'].map(i=> [i,11.53,19,1]),['!'].map(i=> [i,5.61,19,1]),['@'].map(i=> [i,16.48,19,1]),['#'].map(i=> [i,13.69,19,1]),['&'].map(i=> [i,14.41,19,1]),['('].map(i=> [i,7.02,19,1]),[')'].map(i=> [i,7.03,19,1]),['{','}','[',']'].map(i=> [i,6.69,19,1]),['\\'].map(i=> [i,11.5,19,1]),['\''].map(i=> [i,3.78,19,1])],"STHeiti20":[['我','！','？','《','》','；','，','、','。','（','）','：','【','】','“','”','‘','’'].map(i=> [i,20,20,1]),[' ','_'].map(i=> [i,10,20,1]),['A','N'].map(i=> [i,14.81,20,1]),['T'].map(i=> [i,8.55,20,1]),['☑'].map(i=> [i,16.61,20,1]),['☐'].map(i=> [i,16.63,20,1]),['⊙','⭘','℃'].map(i=> [i,20.02,20,1]),['◯'].map(i=> [i,19.08,20,1]),['↵'].map(i=> [i,0,20,1]),['s'].map(i=> [i,7.78,20,1]),['/'].map(i=> [i,8.75,20,1]),['u'].map(i=> [i,12.17,20,1]),['^','|'].map(i=> [i,13.45,20,1]),['t'].map(i=> [i,6.8,20,1]),['c'].map(i=> [i,12.95,20,1]),['y','E'].map(i=> [i,10.73,20,1]),['0','1','2','4','5','6','7','8','9','v','$'].map(i=> [i,11.09,20,1]),['S'].map(i=> [i,9.97,20,1]),['o'].map(i=> [i,13.13,20,1]),['e'].map(i=> [i,13.02,20,1]),['3'].map(i=> [i,11.11,20,1]),['a','H'].map(i=> [i,13.67,20,1]),['b','p','q'].map(i=> [i,13.66,20,1]),['d'].map(i=> [i,13.72,20,1]),['f'].map(i=> [i,6.3,20,1]),['g'].map(i=> [i,13.48,20,1]),['h'].map(i=> [i,12.22,20,1]),['i','l'].map(i=> [i,4.02,20,1]),['j'].map(i=> [i,4.06,20,1]),['k'].map(i=> [i,10.06,20,1]),['m'].map(i=> [i,18.78,20,1]),['n','X'].map(i=> [i,12.2,20,1]),['r'].map(i=> [i,6.03,20,1]),['w'].map(i=> [i,16.64,20,1]),['x','Z'].map(i=> [i,9.61,20,1]),['z','*'].map(i=> [i,8.52,20,1]),['B'].map(i=> [i,11.5,20,1]),['C'].map(i=> [i,16.28,20,1]),['D'].map(i=> [i,14.89,20,1]),['F'].map(i=> [i,9.72,20,1]),['G'].map(i=> [i,17.45,20,1]),['I'].map(i=> [i,4.55,20,1]),['J'].map(i=> [i,9.66,20,1]),['K'].map(i=> [i,11.83,20,1]),['L'].map(i=> [i,9.25,20,1]),['M'].map(i=> [i,18.41,20,1]),['O'].map(i=> [i,17.39,20,1]),['P','Y'].map(i=> [i,11.86,20,1]),['Q'].map(i=> [i,17.44,20,1]),['R'].map(i=> [i,12.16,20,1]),['U'].map(i=> [i,13.11,20,1]),['V'].map(i=> [i,14.05,20,1]),['W'].map(i=> [i,19.22,20,1]),[','].map(i=> [i,5.56,20,1]),['.',':',';'].map(i=> [i,5.55,20,1]),['?'].map(i=> [i,9.92,20,1]),['\"'].map(i=> [i,6.2,20,1]),['<','=','~','+'].map(i=> [i,12.14,20,1]),['>'].map(i=> [i,12.13,20,1]),['`'].map(i=> [i,7.58,20,1]),['!'].map(i=> [i,5.91,20,1]),['@'].map(i=> [i,17.36,20,1]),['#'].map(i=> [i,14.42,20,1]),['%'].map(i=> [i,15.52,20,1]),['&'].map(i=> [i,15.16,20,1]),['(',')'].map(i=> [i,7.39,20,1]),['-'].map(i=> [i,6.66,20,1]),['{','}',']'].map(i=> [i,7.03,20,1]),['['].map(i=> [i,7.05,20,1]),['\\'].map(i=> [i,12.11,20,1]),['\''].map(i=> [i,3.98,20,1])],"STHeiti21.3":[['p','q'].map(i=> [i,14.55,21,1]),['↵'].map(i=> [i,0,21,1]),['我','！','？','《','》','，','、','。','（','：','【','】','“','‘','’','⭘','℃'].map(i=> [i,21.31,21,1]),['；','）','”','⊙'].map(i=> [i,21.33,21,1]),[' '].map(i=> [i,10.65,21,1]),['A','N'].map(i=> [i,15.78,21,1]),['☑'].map(i=> [i,17.7,21,1]),['☐'].map(i=> [i,17.69,21,1]),['◯'].map(i=> [i,20.33,21,1]),['`'].map(i=> [i,8.06,21,1]),['}'].map(i=> [i,7.5,21,1]),['r'].map(i=> [i,6.42,21,1]),['T'].map(i=> [i,9.09,21,1]),['\\'].map(i=> [i,12.89,21,1]),['a','H'].map(i=> [i,14.56,21,1]),['*'].map(i=> [i,9.08,21,1]),[',',':'].map(i=> [i,5.91,21,1]),['X'].map(i=> [i,12.98,21,1]),['!'].map(i=> [i,6.3,21,1]),['P','Y'].map(i=> [i,12.63,21,1]),['Z'].map(i=> [i,10.25,21,1]),['s'].map(i=> [i,8.28,21,1]),['^','|'].map(i=> [i,14.33,21,1]),['t'].map(i=> [i,7.23,21,1]),['_'].map(i=> [i,10.66,21,1]),['0','1','2','4','5','6','7','9','v','$'].map(i=> [i,11.81,21,1]),['3','8'].map(i=> [i,11.83,21,1]),['b'].map(i=> [i,14.53,21,1]),['d'].map(i=> [i,14.61,21,1]),['c'].map(i=> [i,13.8,21,1]),['e'].map(i=> [i,13.86,21,1]),['f'].map(i=> [i,6.7,21,1]),['g'].map(i=> [i,14.34,21,1]),['h'].map(i=> [i,13.02,21,1]),['i'].map(i=> [i,4.27,21,1]),['j'].map(i=> [i,4.34,21,1]),['k'].map(i=> [i,10.7,21,1]),['l'].map(i=> [i,4.28,21,1]),['m'].map(i=> [i,20,21,1]),['n'].map(i=> [i,13,21,1]),['o','U'].map(i=> [i,13.97,21,1]),['u'].map(i=> [i,12.97,21,1]),['w'].map(i=> [i,17.72,21,1]),['x'].map(i=> [i,10.23,21,1]),['y','E'].map(i=> [i,11.44,21,1]),['z'].map(i=> [i,9.06,21,1]),['B'].map(i=> [i,12.23,21,1]),['C'].map(i=> [i,17.34,21,1]),['D'].map(i=> [i,15.86,21,1]),['F'].map(i=> [i,10.34,21,1]),['G'].map(i=> [i,18.59,21,1]),['I'].map(i=> [i,4.83,21,1]),['J'].map(i=> [i,10.28,21,1]),['K'].map(i=> [i,12.59,21,1]),['L'].map(i=> [i,9.86,21,1]),['M'].map(i=> [i,19.59,21,1]),['O'].map(i=> [i,18.52,21,1]),['Q'].map(i=> [i,18.58,21,1]),['R','>','+'].map(i=> [i,12.94,21,1]),['S'].map(i=> [i,10.63,21,1]),['V'].map(i=> [i,14.97,21,1]),['W'].map(i=> [i,20.45,21,1]),['.',';'].map(i=> [i,5.92,21,1]),['/'].map(i=> [i,9.33,21,1]),['?'].map(i=> [i,10.56,21,1]),['\"'].map(i=> [i,6.59,21,1]),['<','=','~'].map(i=> [i,12.92,21,1]),['@'].map(i=> [i,18.48,21,1]),['#'].map(i=> [i,15.36,21,1]),['%'].map(i=> [i,16.52,21,1]),['&'].map(i=> [i,16.14,21,1]),['(',')'].map(i=> [i,7.88,21,1]),['-'].map(i=> [i,7.08,21,1]),['{','[',']'].map(i=> [i,7.48,21,1]),['\''].map(i=> [i,4.23,21,1])],"STHeiti24":[['!'].map(i=> [i,7.09,24,1]),['↵'].map(i=> [i,0,24,1]),['z','*'].map(i=> [i,10.22,24,1]),['我','！','？','《','》','；','，','、','。','（','）','：','【','】','“','”','‘','’'].map(i=> [i,24,24,1]),[' '].map(i=> [i,12,24,1]),['A','N'].map(i=> [i,17.77,24,1]),['_'].map(i=> [i,12.02,24,1]),['☑','☐'].map(i=> [i,19.94,24,1]),['⊙','⭘','℃'].map(i=> [i,24.02,24,1]),['Z'].map(i=> [i,11.53,24,1]),['s'].map(i=> [i,9.33,24,1]),['|'].map(i=> [i,16.14,24,1]),['c'].map(i=> [i,15.53,24,1]),['0','1','2','3','4','5','6','7','9','v','$'].map(i=> [i,13.31,24,1]),['S'].map(i=> [i,11.97,24,1]),['e'].map(i=> [i,15.63,24,1]),['◯'].map(i=> [i,22.91,24,1]),['8'].map(i=> [i,13.3,24,1]),['a','H'].map(i=> [i,16.41,24,1]),['b','p'].map(i=> [i,16.38,24,1]),['d'].map(i=> [i,16.45,24,1]),['f'].map(i=> [i,7.55,24,1]),['g'].map(i=> [i,16.17,24,1]),['h','n'].map(i=> [i,14.66,24,1]),['i','l'].map(i=> [i,4.81,24,1]),['j'].map(i=> [i,4.89,24,1]),['k'].map(i=> [i,12.06,24,1]),['m'].map(i=> [i,22.53,24,1]),['o','U'].map(i=> [i,15.73,24,1]),['q'].map(i=> [i,16.39,24,1]),['r'].map(i=> [i,7.23,24,1]),['t'].map(i=> [i,8.16,24,1]),['u'].map(i=> [i,14.61,24,1]),['w'].map(i=> [i,19.95,24,1]),['x'].map(i=> [i,11.55,24,1]),['y','E'].map(i=> [i,12.88,24,1]),['B'].map(i=> [i,13.8,24,1]),['C'].map(i=> [i,19.53,24,1]),['D'].map(i=> [i,17.88,24,1]),['F'].map(i=> [i,11.66,24,1]),['G'].map(i=> [i,20.94,24,1]),['I'].map(i=> [i,5.44,24,1]),['J'].map(i=> [i,11.59,24,1]),['K'].map(i=> [i,14.2,24,1]),['L'].map(i=> [i,11.09,24,1]),['M'].map(i=> [i,22.08,24,1]),['O'].map(i=> [i,20.88,24,1]),['P'].map(i=> [i,14.22,24,1]),['Q'].map(i=> [i,20.92,24,1]),['R'].map(i=> [i,14.59,24,1]),['T'].map(i=> [i,10.23,24,1]),['V'].map(i=> [i,16.86,24,1]),['W'].map(i=> [i,23.06,24,1]),['X'].map(i=> [i,14.63,24,1]),['Y'].map(i=> [i,14.23,24,1]),[',',':',';'].map(i=> [i,6.66,24,1]),['.'].map(i=> [i,6.67,24,1]),['/'].map(i=> [i,10.5,24,1]),['?'].map(i=> [i,11.89,24,1]),['\"'].map(i=> [i,7.44,24,1]),['<','>','=','~','+'].map(i=> [i,14.56,24,1]),['`'].map(i=> [i,9.08,24,1]),['@'].map(i=> [i,20.83,24,1]),['#'].map(i=> [i,17.3,24,1]),['%'].map(i=> [i,18.61,24,1]),['^'].map(i=> [i,16.16,24,1]),['&'].map(i=> [i,18.17,24,1]),['(',')'].map(i=> [i,8.88,24,1]),['-'].map(i=> [i,7.98,24,1]),['{','}','[',']'].map(i=> [i,8.44,24,1]),['\\'].map(i=> [i,14.53,24,1]),['\''].map(i=> [i,4.77,24,1])],"STHeiti26.7":[['我','》','（','“','”','⭘'].map(i=> [i,26.7,27,1]),['！','？','《','；','，','、','。','）','：','【','】','‘','’','⊙','℃'].map(i=> [i,26.72,27,1]),[' '].map(i=> [i,13.35,27,1]),['A','N'].map(i=> [i,19.78,27,1]),['_'].map(i=> [i,13.36,27,1]),['☐'].map(i=> [i,22.19,27,1]),['☑'].map(i=> [i,22.17,27,1]),['↵'].map(i=> [i,0,27,1]),['s'].map(i=> [i,10.38,27,1]),['B'].map(i=> [i,15.33,27,1]),['|'].map(i=> [i,17.97,27,1]),['t'].map(i=> [i,9.06,27,1]),['◯'].map(i=> [i,25.48,27,1]),['0','1','3','4','6','7','9','v'].map(i=> [i,14.81,27,1]),['2','5','8','$'].map(i=> [i,14.8,27,1]),['a','H'].map(i=> [i,18.25,27,1]),['b','p','q'].map(i=> [i,18.22,27,1]),['d'].map(i=> [i,18.31,27,1]),['c'].map(i=> [i,17.28,27,1]),['e'].map(i=> [i,17.38,27,1]),['f'].map(i=> [i,8.41,27,1]),['g'].map(i=> [i,17.98,27,1]),['h','n'].map(i=> [i,16.3,27,1]),['i','l'].map(i=> [i,5.36,27,1]),['j'].map(i=> [i,5.44,27,1]),['k'].map(i=> [i,13.41,27,1]),['m'].map(i=> [i,25.06,27,1]),['o'].map(i=> [i,17.52,27,1]),['r'].map(i=> [i,8.06,27,1]),['u'].map(i=> [i,16.25,27,1]),['w'].map(i=> [i,22.2,27,1]),['x','Z'].map(i=> [i,12.83,27,1]),['y','E'].map(i=> [i,14.33,27,1]),['z'].map(i=> [i,11.36,27,1]),['C'].map(i=> [i,21.73,27,1]),['D'].map(i=> [i,19.88,27,1]),['F'].map(i=> [i,12.97,27,1]),['G'].map(i=> [i,23.3,27,1]),['I'].map(i=> [i,6.05,27,1]),['J'].map(i=> [i,12.89,27,1]),['K'].map(i=> [i,15.8,27,1]),['L'].map(i=> [i,12.34,27,1]),['M'].map(i=> [i,24.55,27,1]),['O'].map(i=> [i,23.22,27,1]),['P','Y'].map(i=> [i,15.83,27,1]),['Q'].map(i=> [i,23.27,27,1]),['R'].map(i=> [i,16.22,27,1]),['S'].map(i=> [i,13.31,27,1]),['T'].map(i=> [i,11.39,27,1]),['U'].map(i=> [i,17.5,27,1]),['V'].map(i=> [i,18.77,27,1]),['W'].map(i=> [i,25.64,27,1]),['X'].map(i=> [i,16.28,27,1]),[',',':',';'].map(i=> [i,7.41,27,1]),['.'].map(i=> [i,7.42,27,1]),['/'].map(i=> [i,11.67,27,1]),['?'].map(i=> [i,13.23,27,1]),['\"'].map(i=> [i,8.27,27,1]),['<','>','~'].map(i=> [i,16.2,27,1]),['=','+'].map(i=> [i,16.19,27,1]),['`'].map(i=> [i,10.11,27,1]),['!'].map(i=> [i,7.89,27,1]),['@'].map(i=> [i,23.16,27,1]),['#'].map(i=> [i,19.23,27,1]),['%'].map(i=> [i,20.7,27,1]),['^'].map(i=> [i,17.95,27,1]),['&'].map(i=> [i,20.22,27,1]),['*'].map(i=> [i,11.38,27,1]),['('].map(i=> [i,9.86,27,1]),[')'].map(i=> [i,9.88,27,1]),['-'].map(i=> [i,8.89,27,1]),['{','}','['].map(i=> [i,9.39,27,1]),[']'].map(i=> [i,9.38,27,1]),['\\'].map(i=> [i,16.17,27,1]),['\''].map(i=> [i,5.3,27,1])],"STHeiti29.3":[['我','！','？','《','》','，','、','。','（','：','【','】','“','‘','’','⊙','⭘','℃'].map(i=> [i,29.31,29,1]),['；','）','”'].map(i=> [i,29.33,29,1]),[' '].map(i=> [i,14.65,29,1]),['_'].map(i=> [i,14.67,29,1]),['☑'].map(i=> [i,24.33,29,1]),['☐'].map(i=> [i,24.34,29,1]),['◯'].map(i=> [i,27.97,29,1]),['↵'].map(i=> [i,0,29,1]),['s'].map(i=> [i,11.38,29,1]),['^','|'].map(i=> [i,19.7,29,1]),['0','7'].map(i=> [i,16.23,29,1]),['1','2','3','4','5','6','8','9','v','$'].map(i=> [i,16.25,29,1]),['a'].map(i=> [i,20.03,29,1]),['b','p','q'].map(i=> [i,20,29,1]),['d'].map(i=> [i,20.08,29,1]),['c'].map(i=> [i,18.98,29,1]),['e'].map(i=> [i,19.06,29,1]),['f'].map(i=> [i,9.2,29,1]),['g'].map(i=> [i,19.73,29,1]),['h','n'].map(i=> [i,17.89,29,1]),['i','l'].map(i=> [i,5.88,29,1]),['j'].map(i=> [i,5.97,29,1]),['k'].map(i=> [i,14.72,29,1]),['m'].map(i=> [i,27.5,29,1]),['o','U'].map(i=> [i,19.2,29,1]),['r'].map(i=> [i,8.84,29,1]),['t'].map(i=> [i,9.95,29,1]),['u'].map(i=> [i,17.83,29,1]),['w'].map(i=> [i,24.36,29,1]),['x','Z'].map(i=> [i,14.08,29,1]),['y','E'].map(i=> [i,15.72,29,1]),['z','*'].map(i=> [i,12.47,29,1]),['A','N'].map(i=> [i,21.7,29,1]),['B'].map(i=> [i,16.83,29,1]),['C'].map(i=> [i,23.84,29,1]),['D'].map(i=> [i,21.81,29,1]),['F'].map(i=> [i,14.23,29,1]),['G'].map(i=> [i,25.56,29,1]),['H'].map(i=> [i,20.02,29,1]),['I'].map(i=> [i,6.64,29,1]),['J'].map(i=> [i,14.14,29,1]),['K'].map(i=> [i,17.33,29,1]),['L'].map(i=> [i,13.56,29,1]),['M'].map(i=> [i,26.94,29,1]),['O'].map(i=> [i,25.47,29,1]),['P'].map(i=> [i,17.36,29,1]),['Q'].map(i=> [i,25.55,29,1]),['R'].map(i=> [i,17.8,29,1]),['S'].map(i=> [i,14.61,29,1]),['T'].map(i=> [i,12.5,29,1]),['V'].map(i=> [i,20.58,29,1]),['W'].map(i=> [i,28.14,29,1]),['X'].map(i=> [i,17.86,29,1]),['Y'].map(i=> [i,17.38,29,1]),[',','.',':',';'].map(i=> [i,8.13,29,1]),['/'].map(i=> [i,12.83,29,1]),['?'].map(i=> [i,14.52,29,1]),['\"'].map(i=> [i,9.08,29,1]),['<','=','~','+'].map(i=> [i,17.77,29,1]),['>'].map(i=> [i,17.78,29,1]),['`'].map(i=> [i,11.08,29,1]),['!'].map(i=> [i,8.67,29,1]),['@'].map(i=> [i,25.41,29,1]),['#'].map(i=> [i,21.11,29,1]),['%'].map(i=> [i,22.73,29,1]),['&'].map(i=> [i,22.19,29,1]),['(',')'].map(i=> [i,10.83,29,1]),['-'].map(i=> [i,9.75,29,1]),['{','}',']'].map(i=> [i,10.3,29,1]),['['].map(i=> [i,10.31,29,1]),['\\'].map(i=> [i,17.73,29,1]),['\''].map(i=> [i,5.83,29,1])],"STHeiti32":[['↵'].map(i=> [i,0,32,1]),['K'].map(i=> [i,18.94,32,1]),['我','！','？','《','》','；','，','、','。','（','）','：','【','】','“','”','‘','’'].map(i=> [i,32,32,1]),[' '].map(i=> [i,16,32,1]),['_'].map(i=> [i,16.02,32,1]),['☑','☐'].map(i=> [i,26.58,32,1]),['⊙','⭘','℃'].map(i=> [i,32.02,32,1]),['◯'].map(i=> [i,30.53,32,1]),['x','Z'].map(i=> [i,15.38,32,1]),['^','|'].map(i=> [i,21.52,32,1]),['t'].map(i=> [i,10.86,32,1]),['c'].map(i=> [i,20.72,32,1]),['0','3','5','8'].map(i=> [i,17.73,32,1]),['S'].map(i=> [i,15.95,32,1]),['1','2','4','6','7','9','v','$'].map(i=> [i,17.75,32,1]),['a','H'].map(i=> [i,21.88,32,1]),['b','p','q'].map(i=> [i,21.84,32,1]),['d'].map(i=> [i,21.92,32,1]),['e'].map(i=> [i,20.83,32,1]),['f'].map(i=> [i,10.06,32,1]),['g'].map(i=> [i,21.55,32,1]),['h'].map(i=> [i,19.53,32,1]),['i'].map(i=> [i,6.42,32,1]),['j'].map(i=> [i,6.52,32,1]),['k'].map(i=> [i,16.08,32,1]),['l'].map(i=> [i,6.41,32,1]),['m'].map(i=> [i,30.03,32,1]),['n'].map(i=> [i,19.55,32,1]),['o','U'].map(i=> [i,20.97,32,1]),['r'].map(i=> [i,9.64,32,1]),['s'].map(i=> [i,12.44,32,1]),['u'].map(i=> [i,19.47,32,1]),['w'].map(i=> [i,26.61,32,1]),['y','E'].map(i=> [i,17.16,32,1]),['z','*'].map(i=> [i,13.63,32,1]),['A','N'].map(i=> [i,23.69,32,1]),['B'].map(i=> [i,18.39,32,1]),['C'].map(i=> [i,26.03,32,1]),['D'].map(i=> [i,23.83,32,1]),['F'].map(i=> [i,15.55,32,1]),['G'].map(i=> [i,27.91,32,1]),['I'].map(i=> [i,7.25,32,1]),['J'].map(i=> [i,15.44,32,1]),['L'].map(i=> [i,14.8,32,1]),['M'].map(i=> [i,29.42,32,1]),['O'].map(i=> [i,27.83,32,1]),['P'].map(i=> [i,18.95,32,1]),['Q'].map(i=> [i,27.89,32,1]),['R'].map(i=> [i,19.44,32,1]),['T'].map(i=> [i,13.66,32,1]),['V'].map(i=> [i,22.48,32,1]),['W'].map(i=> [i,30.73,32,1]),['X'].map(i=> [i,19.5,32,1]),['Y'].map(i=> [i,18.97,32,1]),[',','.',':',';'].map(i=> [i,8.88,32,1]),['/'].map(i=> [i,14,32,1]),['?'].map(i=> [i,15.86,32,1]),['\"'].map(i=> [i,9.91,32,1]),['<','>','=','~','+'].map(i=> [i,19.41,32,1]),['`'].map(i=> [i,12.13,32,1]),['!'].map(i=> [i,9.45,32,1]),['@'].map(i=> [i,27.75,32,1]),['#'].map(i=> [i,23.06,32,1]),['%'].map(i=> [i,24.81,32,1]),['&'].map(i=> [i,24.23,32,1]),['('].map(i=> [i,11.81,32,1]),[')'].map(i=> [i,11.83,32,1]),['-'].map(i=> [i,10.64,32,1]),['{','}','[',']'].map(i=> [i,11.25,32,1]),['\\'].map(i=> [i,19.38,32,1]),['\''].map(i=> [i,6.36,32,1])],"STHeiti34.7":[['我','》','（','）','”'].map(i=> [i,34.7,35,1]),['！','？','《','；','，','、','。','：','【','】','“','‘','’','⊙','⭘','℃'].map(i=> [i,34.72,35,1]),[' '].map(i=> [i,17.35,35,1]),['N'].map(i=> [i,25.69,35,1]),['_'].map(i=> [i,17.38,35,1]),['☑','☐'].map(i=> [i,28.81,35,1]),['◯'].map(i=> [i,33.11,35,1]),['↵'].map(i=> [i,0,35,1]),['x','Z'].map(i=> [i,16.67,35,1]),['|'].map(i=> [i,23.34,35,1]),['t'].map(i=> [i,11.78,35,1]),['y','E'].map(i=> [i,18.61,35,1]),['0','2','3','5','6','8','9','v'].map(i=> [i,19.23,35,1]),['S'].map(i=> [i,17.3,35,1]),['e'].map(i=> [i,22.58,35,1]),['1','4','7','$'].map(i=> [i,19.25,35,1]),['a','H'].map(i=> [i,23.72,35,1]),['b','q'].map(i=> [i,23.69,35,1]),['d'].map(i=> [i,23.78,35,1]),['c'].map(i=> [i,22.47,35,1]),['f'].map(i=> [i,10.91,35,1]),['g'].map(i=> [i,23.38,35,1]),['h'].map(i=> [i,21.17,35,1]),['i'].map(i=> [i,6.97,35,1]),['j'].map(i=> [i,7.05,35,1]),['k'].map(i=> [i,17.44,35,1]),['l'].map(i=> [i,6.95,35,1]),['m'].map(i=> [i,32.56,35,1]),['n'].map(i=> [i,21.19,35,1]),['o','U'].map(i=> [i,22.75,35,1]),['p'].map(i=> [i,23.67,35,1]),['r'].map(i=> [i,10.45,35,1]),['s'].map(i=> [i,13.48,35,1]),['u'].map(i=> [i,21.11,35,1]),['w'].map(i=> [i,28.86,35,1]),['z','*'].map(i=> [i,14.77,35,1]),['A'].map(i=> [i,25.7,35,1]),['B'].map(i=> [i,19.92,35,1]),['C'].map(i=> [i,28.23,35,1]),['D'].map(i=> [i,25.83,35,1]),['F'].map(i=> [i,16.84,35,1]),['G'].map(i=> [i,30.28,35,1]),['I'].map(i=> [i,7.86,35,1]),['J'].map(i=> [i,16.73,35,1]),['K'].map(i=> [i,20.53,35,1]),['L'].map(i=> [i,16.05,35,1]),['M'].map(i=> [i,31.91,35,1]),['O'].map(i=> [i,30.17,35,1]),['P','Y'].map(i=> [i,20.56,35,1]),['Q'].map(i=> [i,30.23,35,1]),['R'].map(i=> [i,21.08,35,1]),['T'].map(i=> [i,14.8,35,1]),['V'].map(i=> [i,24.38,35,1]),['W'].map(i=> [i,33.31,35,1]),['X'].map(i=> [i,21.14,35,1]),[',','.',':',';'].map(i=> [i,9.63,35,1]),['/'].map(i=> [i,15.17,35,1]),['?'].map(i=> [i,17.2,35,1]),['\"'].map(i=> [i,10.73,35,1]),['<','>','=','+'].map(i=> [i,21.05,35,1]),['~'].map(i=> [i,21.03,35,1]),['`'].map(i=> [i,13.14,35,1]),['!'].map(i=> [i,10.25,35,1]),['@'].map(i=> [i,30.09,35,1]),['#'].map(i=> [i,25,35,1]),['%'].map(i=> [i,26.91,35,1]),['^'].map(i=> [i,23.33,35,1]),['&'].map(i=> [i,26.28,35,1]),['('].map(i=> [i,12.83,35,1]),[')'].map(i=> [i,12.81,35,1]),['-'].map(i=> [i,11.53,35,1]),['{','['].map(i=> [i,12.19,35,1]),['}',']'].map(i=> [i,12.2,35,1]),['\\'].map(i=> [i,21,35,1]),['\''].map(i=> [i,6.88,35,1])],"STHeiti37.3":[['我','！','？','《','》','，','、','。','（','：','【','】','“','‘','’','⭘','℃'].map(i=> [i,37.31,37,1]),['；','）','”','⊙'].map(i=> [i,37.33,37,1]),['A','N'].map(i=> [i,27.63,37,1]),['_'].map(i=> [i,18.66,37,1]),['☑'].map(i=> [i,30.98,37,1]),['☐'].map(i=> [i,30.97,37,1]),['◯'].map(i=> [i,35.58,37,1]),['↵'].map(i=> [i,0,37,1]),['F'].map(i=> [i,18.09,37,1]),['H'].map(i=> [i,25.48,37,1]),['Z'].map(i=> [i,17.91,37,1]),['s'].map(i=> [i,14.48,37,1]),['^'].map(i=> [i,25.08,37,1]),['c'].map(i=> [i,24.16,37,1]),['y','E'].map(i=> [i,20.02,37,1]),['1','3','5','7','9'].map(i=> [i,20.67,37,1]),[' '].map(i=> [i,13.65,37,1]),['0','2','4','6','8','v','$'].map(i=> [i,20.69,37,1]),['a'].map(i=> [i,25.5,37,1]),['b','p','q'].map(i=> [i,25.45,37,1]),['d'].map(i=> [i,25.56,37,1]),['e'].map(i=> [i,24.25,37,1]),['f'].map(i=> [i,11.73,37,1]),['g'].map(i=> [i,25.11,37,1]),['h'].map(i=> [i,22.78,37,1]),['i'].map(i=> [i,7.47,37,1]),['j'].map(i=> [i,7.59,37,1]),['k'].map(i=> [i,18.73,37,1]),['l'].map(i=> [i,7.48,37,1]),['m'].map(i=> [i,35,37,1]),['n'].map(i=> [i,22.77,37,1]),['o','U'].map(i=> [i,24.45,37,1]),['r'].map(i=> [i,11.23,37,1]),['t'].map(i=> [i,12.67,37,1]),['u'].map(i=> [i,22.69,37,1]),['w'].map(i=> [i,31,37,1]),['x'].map(i=> [i,17.92,37,1]),['z'].map(i=> [i,15.86,37,1]),['B'].map(i=> [i,21.42,37,1]),['C'].map(i=> [i,30.34,37,1]),['D'].map(i=> [i,27.77,37,1]),['G'].map(i=> [i,32.55,37,1]),['I'].map(i=> [i,8.45,37,1]),['J'].map(i=> [i,18,37,1]),['K'].map(i=> [i,22.05,37,1]),['L'].map(i=> [i,17.25,37,1]),['M'].map(i=> [i,34.3,37,1]),['O'].map(i=> [i,32.42,37,1]),['P'].map(i=> [i,22.09,37,1]),['Q'].map(i=> [i,32.52,37,1]),['R'].map(i=> [i,22.66,37,1]),['S'].map(i=> [i,18.58,37,1]),['T'].map(i=> [i,15.91,37,1]),['V'].map(i=> [i,26.2,37,1]),['W'].map(i=> [i,35.83,37,1]),['X'].map(i=> [i,22.72,37,1]),['Y'].map(i=> [i,22.11,37,1]),[',',':'].map(i=> [i,10.36,37,1]),['.',';'].map(i=> [i,10.34,37,1]),['/'].map(i=> [i,16.31,37,1]),['?'].map(i=> [i,18.48,37,1]),['\"'].map(i=> [i,11.53,37,1]),['<','~'].map(i=> [i,22.61,37,1]),['>','=','+'].map(i=> [i,22.63,37,1]),['`'].map(i=> [i,14.13,37,1]),['!'].map(i=> [i,11.02,37,1]),['@'].map(i=> [i,32.36,37,1]),['#'].map(i=> [i,26.86,37,1]),['%'].map(i=> [i,28.92,37,1]),['&'].map(i=> [i,28.25,37,1]),['*'].map(i=> [i,15.88,37,1]),['('].map(i=> [i,13.78,37,1]),[')'].map(i=> [i,13.77,37,1]),['-'].map(i=> [i,12.41,37,1]),['{'].map(i=> [i,13.09,37,1]),['}','[',']'].map(i=> [i,13.11,37,1]),['\\'].map(i=> [i,22.58,37,1]),['|'].map(i=> [i,25.09,37,1]),['\''].map(i=> [i,7.39,37,1])],"STHeiti48":[['我','！','？','《','》','；','，','、','。','（','）','：','【','】','“','”','‘','’'].map(i=> [i,48,48,1]),[' '].map(i=> [i,24,48,1]),['A','N'].map(i=> [i,35.53,48,1]),['_'].map(i=> [i,24.02,48,1]),['☑','☐'].map(i=> [i,39.86,48,1]),['⊙','⭘','℃'].map(i=> [i,48.02,48,1]),['↵'].map(i=> [i,0,48,1]),['x'].map(i=> [i,23.06,48,1]),['s'].map(i=> [i,18.64,48,1]),['◯'].map(i=> [i,45.78,48,1]),['0','1','2','3','4','5','6','7','9','v','$'].map(i=> [i,26.61,48,1]),['S'].map(i=> [i,23.92,48,1]),['e'].map(i=> [i,31.22,48,1]),['8'].map(i=> [i,26.59,48,1]),['a','H'].map(i=> [i,32.8,48,1]),['b','p'].map(i=> [i,32.75,48,1]),['d'].map(i=> [i,32.89,48,1]),['c'].map(i=> [i,31.06,48,1]),['f'].map(i=> [i,15.09,48,1]),['g'].map(i=> [i,32.31,48,1]),['h','n'].map(i=> [i,29.3,48,1]),['i','l'].map(i=> [i,9.63,48,1]),['j'].map(i=> [i,9.75,48,1]),['k'].map(i=> [i,24.11,48,1]),['m'].map(i=> [i,45.03,48,1]),['o'].map(i=> [i,31.45,48,1]),['q'].map(i=> [i,32.77,48,1]),['r'].map(i=> [i,14.45,48,1]),['t'].map(i=> [i,16.3,48,1]),['u'].map(i=> [i,29.19,48,1]),['w'].map(i=> [i,39.91,48,1]),['y','E'].map(i=> [i,25.73,48,1]),['z','*'].map(i=> [i,20.42,48,1]),['B'].map(i=> [i,27.56,48,1]),['C'].map(i=> [i,39.05,48,1]),['D'].map(i=> [i,35.73,48,1]),['F'].map(i=> [i,23.3,48,1]),['G'].map(i=> [i,41.88,48,1]),['I'].map(i=> [i,10.86,48,1]),['J'].map(i=> [i,23.16,48,1]),['K'].map(i=> [i,28.39,48,1]),['L'].map(i=> [i,22.19,48,1]),['M'].map(i=> [i,44.13,48,1]),['O'].map(i=> [i,41.73,48,1]),['P'].map(i=> [i,28.42,48,1]),['Q'].map(i=> [i,41.83,48,1]),['R'].map(i=> [i,29.16,48,1]),['T'].map(i=> [i,20.45,48,1]),['U'].map(i=> [i,31.47,48,1]),['V'].map(i=> [i,33.7,48,1]),['W'].map(i=> [i,46.09,48,1]),['X'].map(i=> [i,29.25,48,1]),['Y'].map(i=> [i,28.44,48,1]),['Z'].map(i=> [i,23.05,48,1]),[',','.',';'].map(i=> [i,13.31,48,1]),['/'].map(i=> [i,21,48,1]),['?'].map(i=> [i,23.77,48,1]),['\"'].map(i=> [i,14.84,48,1]),[':'].map(i=> [i,13.3,48,1]),['<','>','~'].map(i=> [i,29.11,48,1]),['=','+'].map(i=> [i,29.09,48,1]),['`'].map(i=> [i,18.16,48,1]),['!'].map(i=> [i,14.17,48,1]),['@'].map(i=> [i,41.64,48,1]),['#'].map(i=> [i,34.58,48,1]),['%'].map(i=> [i,37.2,48,1]),['^','|'].map(i=> [i,32.28,48,1]),['&'].map(i=> [i,36.34,48,1]),['('].map(i=> [i,17.72,48,1]),[')'].map(i=> [i,17.73,48,1]),['-'].map(i=> [i,15.95,48,1]),['{','}','['].map(i=> [i,16.86,48,1]),[']'].map(i=> [i,16.88,48,1]),['\\'].map(i=> [i,29.05,48,1]),['\''].map(i=> [i,9.52,48,1])],"STHeiti58.7":[['我','》','；','）','”'].map(i=> [i,58.7,58,1]),['！','？','《','，','、','。','（','：','【','】','“','‘','’','⊙','⭘','℃'].map(i=> [i,58.72,58,1]),['A','N'].map(i=> [i,43.45,58,1]),['☑','☐'].map(i=> [i,48.73,58,1]),['↵'].map(i=> [i,0,58,1]),['x','Z'].map(i=> [i,28.19,58,1]),['s'].map(i=> [i,22.8,58,1]),['◯'].map(i=> [i,55.98,58,1]),[' '].map(i=> [i,16.35,58,1]),['0','1','2','4','5','6','8','9','v','$'].map(i=> [i,32.53,58,1]),['3','7'].map(i=> [i,32.55,58,1]),['a','H'].map(i=> [i,40.11,58,1]),['b'].map(i=> [i,40.06,58,1]),['d'].map(i=> [i,40.22,58,1]),['c'].map(i=> [i,37.98,58,1]),['e'].map(i=> [i,38.17,58,1]),['f'].map(i=> [i,18.45,58,1]),['g'].map(i=> [i,39.52,58,1]),['h'].map(i=> [i,35.83,58,1]),['i','l'].map(i=> [i,11.75,58,1]),['j'].map(i=> [i,11.94,58,1]),['k'].map(i=> [i,29.48,58,1]),['m'].map(i=> [i,55.08,58,1]),['n'].map(i=> [i,35.81,58,1]),['o','U'].map(i=> [i,38.47,58,1]),['p','q'].map(i=> [i,40.05,58,1]),['r'].map(i=> [i,17.69,58,1]),['t'].map(i=> [i,19.91,58,1]),['u'].map(i=> [i,35.72,58,1]),['w'].map(i=> [i,48.8,58,1]),['y','E'].map(i=> [i,31.48,58,1]),['z'].map(i=> [i,24.95,58,1]),['B'].map(i=> [i,33.72,58,1]),['C'].map(i=> [i,47.73,58,1]),['D'].map(i=> [i,43.69,58,1]),['F'].map(i=> [i,28.48,58,1]),['G'].map(i=> [i,51.2,58,1]),['I'].map(i=> [i,13.28,58,1]),['J'].map(i=> [i,28.3,58,1]),['K'].map(i=> [i,34.72,58,1]),['L'].map(i=> [i,27.13,58,1]),['M'].map(i=> [i,53.97,58,1]),['O'].map(i=> [i,51.02,58,1]),['P','Y'].map(i=> [i,34.77,58,1]),['Q'].map(i=> [i,51.16,58,1]),['R'].map(i=> [i,35.64,58,1]),['S'].map(i=> [i,29.25,58,1]),['T'].map(i=> [i,25.02,58,1]),['V'].map(i=> [i,41.22,58,1]),['W'].map(i=> [i,56.38,58,1]),['X'].map(i=> [i,35.77,58,1]),[',',':'].map(i=> [i,16.28,58,1]),['.',';'].map(i=> [i,16.27,58,1]),['/'].map(i=> [i,25.67,58,1]),['?'].map(i=> [i,29.08,58,1]),['\"'].map(i=> [i,18.14,58,1]),['<','=','~'].map(i=> [i,35.59,58,1]),['>','+'].map(i=> [i,35.58,58,1]),['`'].map(i=> [i,22.2,58,1]),['!'].map(i=> [i,17.33,58,1]),['@'].map(i=> [i,50.91,58,1]),['#'].map(i=> [i,42.28,58,1]),['%'].map(i=> [i,45.52,58,1]),['^','|'].map(i=> [i,39.45,58,1]),['&'].map(i=> [i,44.45,58,1]),['*'].map(i=> [i,24.97,58,1]),['(',')'].map(i=> [i,21.67,58,1]),['_'].map(i=> [i,29.38,58,1]),['-'].map(i=> [i,19.52,58,1]),['{',']'].map(i=> [i,20.63,58,1]),['}','['].map(i=> [i,20.61,58,1]),['\\'].map(i=> [i,35.53,58,1]),['\''].map(i=> [i,11.64,58,1])]};

    for (const size in items) {
        const arrs = [];
        items[size].forEach((item) => {
            const arr = item[0];
            const obj = {width: arr[1], height: arr[2], type: arr[3]};
            item.forEach((i) => {
                arrs.push([i[0], obj]);
            });
        });
        CACHES.set(size, new Map(arrs));
    }
}
