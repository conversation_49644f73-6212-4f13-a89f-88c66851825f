import {ParagraphContentPos} from './ParagraphContent';

/**
 * 左右光标位置查找
 */
export interface IParagraphSearchPos {
    pos: ParagraphContentPos;  // portion索引
    bFound: boolean;  // 是否找到
    bSelection: boolean;  // 是否是选择操作
}

/**
 * 鼠标光标位置定位
 */
export interface IParagraphSearchPosXY {
    pos: ParagraphContentPos; // 记录当前portion的索引
    textPos: ParagraphContentPos; // 记录当前portion的内容索引
    x: number; // 光标x
    y: number; // 光标y
    curX: number;
    curY: number;
    line: number;
    diffX: number;

    bInText?: boolean;
    bEnd?: boolean;
}

export interface ICurrentCursorPos {
    x: number;
    y: number;
    pageNum: number;
    line?: number;
    page?: number;
}
