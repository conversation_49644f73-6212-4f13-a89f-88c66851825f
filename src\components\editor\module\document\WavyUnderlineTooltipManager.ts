import '../../style/wavy-underline.less';
import { WavyUnderline, WavyUnderlineType, WavyUnderlineTypeConfig } from "../../../../model/core/WavyUnderline/WavyUnderline";

export class WavyUnderlineTooltipManager {
    private tooltip: HTMLElement | null = null;
    private isVisible: boolean = false;
    private showTimer: number | null = null;
    private hideTimer: number | null = null;
    
    /**
     * 显示提示框
     */
    public show(options: IWavyUnderlineTooltipOptions): void {
        // 清除隐藏定时器
        if (this.hideTimer) {
            clearTimeout(this.hideTimer);
            this.hideTimer = null;
        }
        
        // 检查是否已经显示相同内容
        if (this.tooltip && this.isVisible) {
            const currentContent = options.wavyUnderline.getName() + options.content;
            const existingContent = this.tooltip.getAttribute('data-content') || '';
            if (currentContent === existingContent) {
                return; // 避免重复显示
            }
        }
        
        // 立即创建提示框，不需要额外延迟
        this.createTooltip(options);
    }
    
    /**
     * 隐藏提示框
     */
    public hide(): void {
        // 清除显示定时器
        if (this.showTimer) {
            clearTimeout(this.showTimer);
            this.showTimer = null;
        }
        
        // 延迟隐藏，避免快速移动时闪烁
        this.hideTimer = window.setTimeout(() => {
            this.destroyTooltip();
        }, 3000); // 调整为3秒
    }
    
    /**
     * 立即销毁
     */
    public destroy(): void {
        if (this.showTimer) {
            clearTimeout(this.showTimer);
        }
        if (this.hideTimer) {
            clearTimeout(this.hideTimer);
        }
        this.destroyTooltip();
    }
    
    /**
     * 立即隐藏提示框（无延迟）
     */
    public hideImmediate(): void {
        if (this.showTimer) {
            clearTimeout(this.showTimer);
            this.showTimer = null;
        }
        if (this.hideTimer) {
            clearTimeout(this.hideTimer);
            this.hideTimer = null;
        }
        this.destroyTooltip();
    }
    
    /**
     * 创建提示框DOM
     */
    private createTooltip(options: IWavyUnderlineTooltipOptions): void {
        this.destroyTooltip();
        
        // 根据类型添加class
        const typeClass = options.type ? String(options.type).toLowerCase() : '';
        const tooltip = document.createElement('div');
        tooltip.className = `wavy-underline-tooltip active ${typeClass}`;
        tooltip.innerHTML = this.generateTooltipContent(options);
        
        // 设置数据属性，用于避免重复显示
        tooltip.setAttribute('data-content', options.wavyUnderline.getName() + options.content);
        
        // 只设置定位等必要样式
        Object.assign(tooltip.style, {
            position: 'fixed',
            left: `${options.x}px`,
            top: `${options.y - 40}px`,
            zIndex: '1000',
            pointerEvents: 'none',
        });
        
        document.body.appendChild(tooltip);
        this.tooltip = tooltip;
        this.isVisible = true;
        
        // 调整位置避免超出屏幕
        this.adjustTooltipPosition();
    }
    
    /**
     * 销毁提示框DOM
     */
    private destroyTooltip(): void {
        if (this.tooltip) {
            document.body.removeChild(this.tooltip);
            this.tooltip = null;
            this.isVisible = false;
        }
    }
    
    /**
     * 生成提示框内容
     */
    private generateTooltipContent(options: IWavyUnderlineTooltipOptions): string {
        const typeName = this.getTypeDisplayName(options.type);
        const typeClass = options.type ? String(options.type).toLowerCase() : '';
        return `
            <div class="wavy-tooltip-header">
                <span class="wavy-tooltip-type ${typeClass}">${typeName}</span>
            </div>
            <div class="wavy-tooltip-content">${options.content}</div>
        `;
    }
    
    /**
     * 获取类型显示名称
     */
    private getTypeDisplayName(type: WavyUnderlineType): string {
        switch (type) {
            case WavyUnderlineType.SpellCheck: return '拼写检查';
            case WavyUnderlineType.Grammar: return '语法检查';
            case WavyUnderlineType.Suggestion: return '修改建议';
            case WavyUnderlineType.Warning: return '警告';
            case WavyUnderlineType.Custom: return '自定义';
            default: return '提示';
        }
    }
    
    /**
     * 调整提示框位置避免超出屏幕
     */
    private adjustTooltipPosition(): void {
        if (!this.tooltip) return;
        
        const rect = this.tooltip.getBoundingClientRect();
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;
        
        // 水平方向调整
        if (rect.right > viewportWidth) {
            this.tooltip.style.left = `${viewportWidth - rect.width - 10}px`;
        }
        if (rect.left < 0) {
            this.tooltip.style.left = '10px';
        }
        
        // 垂直方向调整
        if (rect.top < 0) {
            this.tooltip.style.top = `${rect.bottom + 10}px`;
        }
        if (rect.bottom > viewportHeight) {
            this.tooltip.style.top = `${rect.top - rect.height - 10}px`;
        }
    }
}

interface IWavyUnderlineTooltipOptions {
    x: number;
    y: number;
    content: string;
    type: WavyUnderlineType;
    wavyUnderline: WavyUnderline;
}