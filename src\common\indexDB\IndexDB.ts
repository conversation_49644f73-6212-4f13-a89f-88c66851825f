import { TsIndexDb } from './TsIndexDb';
import { IDataBase } from '../log/LoggerType';


export class IndexDB extends TsIndexDb {
    private _maxCount: number;
    constructor(props: IDataBase) {
        super(props);
        this._maxCount = 500000;
    }

    public getCount(tableName: string): Promise<number> {
        return new Promise((resolve, reject) => {
            if (!this.db) {
                return resolve(-1);
            }
            const transaction = this.db.transaction(tableName, 'readonly');
            const store = transaction.objectStore(tableName);
            // const indexFunc = store.index('id');
            store.count()
            .onsuccess = (eve: any) => {
                if (eve) {
                    resolve(eve.target.result);
                } else {
                    reject(-1);
                }
            };
        });
    }

    public async deleteAboveDatas(tableName: string, delCount: number): Promise<number> {
        return this._deleteAboveDatas(tableName, delCount);
    }

    // public query(tableName: string, filter: (item: any) => boolean): Promise<any[]> {
    //     return this._vm
    //     .query({tableName, condition: filter});
    // }

    // public deleteAll(tableName: string): Promise<any> {
    //     return this._vm
    //     .delete({tableName, condition: (id) => {
    //         return id > 0;
    //     }});
    // }

    // public async init(name: string = 'logs', version: number = 14, tables: ITable[]): Promise<boolean> {
    //     try {
    //         this._vm = new TsIndexDb();
    //         await this._vm
    //         .openDb();
    //         this.db = this._vm['db'];
    //     } catch (error) {
    //         console.warn(error.target?.error);
    //         return false;
    //     }

    //     return true;
    // }

    /**
     * 数据超过五十万时，进行删除95%的数据
     * @param tableName 表格名称
     * @returns 实际删除数据
     */
    public deleteMaxDatas(tableName: string): Promise<number> {
        return new Promise(async (resolve) => {
            const count = await this.getCount(tableName);
            if (count < this._maxCount) {
                return resolve(0);
            }
            const mycount = await this._deleteAboveDatas(tableName, Math.floor(count * 0.95));
            resolve(mycount);
        });
    }

    /**
     * 删除前面的n行数据
     * @param tableName 表格名称
     * @param delCount 删除多少条
     * @returns 真实删除多少条
     */
    private async _deleteAboveDatas(tableName: string, count: number): Promise<number> {
        if (!this.db) {
            return 0;
        }
        let index = count;
        // count = index;
        const transaction = this.db.transaction(tableName, 'readwrite');
        const store = transaction.objectStore(tableName);

        return new Promise((resolve) => {
            store.openCursor().onsuccess = ((eve: any) => {
                const cursor = eve.target.result;
                if (cursor && index-- > 0) {
                    cursor.delete();
                    cursor.continue();
                }
            });
            resolve(count);
        });
    }
}
