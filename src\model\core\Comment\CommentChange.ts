import { ChangeBaseObjectProperty } from "../HistoryChange";
import { HistroyItemType } from "../HistoryDescription";
import { Comment } from "./Comment";
import { CommentManager } from "./CommentManager";


export class ChangeCommentAddItem extends ChangeBaseObjectProperty {
    private type: HistroyItemType = null;
    constructor( changeClass: Comment, manager: CommentManager, news: any, bAdd?: boolean) {
        super(changeClass, manager, news, bAdd);
        this.type = HistroyItemType.CommentAddItem;
    }

    public undo(): void {
        const comment = this.changeClass as Comment;
        const manager = this.old as CommentManager;
        manager.deleteCommentById(comment.getId(), false);
    }

    public redo(): void {
        const comment = this.changeClass as Comment;
        const manager = this.old as CommentManager;
        manager.addComment(comment, false);
        manager.sortCommentByNearestInsert();
    }
}

export class ChangeCommentRemoveItem extends ChangeBaseObjectProperty {
    private type: HistroyItemType = null;
    constructor( changeClass: Comment, manager: CommentManager, news: any, bAdd?: boolean) {
        super(changeClass, manager, news, bAdd);
        this.type = HistroyItemType.CommentRemoveItem;
    }

    public undo(): void {
        const comment = this.changeClass as Comment;
        const manager = this.old as CommentManager;
        manager.addComment(comment, false);
        manager.sortCommentByNearestInsert();
    }

    public redo(): void {
        const comment = this.changeClass as Comment;
        const manager = this.old as CommentManager;
        manager.deleteCommentById(comment.getId(), false);
    }
}
