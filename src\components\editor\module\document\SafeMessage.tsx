import * as React from 'react';
import { editorCallback } from '../../../../common/GlobalTest';

interface IProps {
    host: any;
    width: number;
    maxWidth: number;
}

class EditorServerMessageExt {
    private _callback: ((type: number) => void) [];
    constructor() {
        this._callback = [];
        this.init();
    }

    public addEvent(callback: (type: number) => void): void {
        this._callback.push((callback));
    }

    public deleteEvent(callback: (type: number) => void): void {
        const callbacks = this._callback;
        const index = callbacks.findIndex((item) => item === callback);
        if (index > -1) {
            callbacks.splice(index, 1);
        }
    }

    private init(): void {
        editorCallback.setFuncs(this._callback);
    }
}

// tslint:disable-next-line: sy-global-const-name
const EditorServerMessage = new EditorServerMessageExt();
export class SafeMessage extends React.Component<IProps> {
    private _datas: string[];
    private _messages: string[];
    private _activeIndex: number;
    private _bActive: boolean;
    private _ref: any;
    private _timer: any;
    private _timeouer: number;
    constructor(props: IProps) {
        super(props);
        this._datas = ['success', 'info', 'warning'];
        this._activeIndex = 0;
        this._messages = ['正常', '缓存模式\n服务器已断开，请及时处理', '网络错误，请及时处理'];
        this._ref = React.createRef();
    }

    public render(): any {
        const props = this.props;
        const left = (props.maxWidth - props.width) / 2;
        return (
            <div className='safe-message' ref={this._ref} style={{left}}>
                {this.renderList()}
                {this.renderMessage()}
            </div>
        );
    }

    public componentDidMount(): void {
        EditorServerMessage.addEvent(this.receiveMessage);
        this._ref.current.addEventListener('mouseover', this.mouseEnter);
    }

    public componentWillUnmount(): void {
        EditorServerMessage.deleteEvent(this.receiveMessage);
        this._ref.current?.removeEventListener('mouseover', this.mouseEnter);
    }

    private mouseEnter = (e: any): void => {
        const target = e.target;
        const className = target.className;
        if (className && className.indexOf('label') !== -1) {
            clearTimeout(this._timer);
            const old = this._bActive;
            this._bActive = true;
            if (old !== true) {
                this.setState({});
            }
            this._timer = setTimeout(() => {
                this._bActive = false;
                this.setState({});
            }, 5000);
        }
    }

    private receiveMessage = (type: number): void => {
        let activeIndex: number;
        switch (type) {
            case 1: {
                activeIndex = 0;
                break;
            }
            case 3:
            case 4: {
                activeIndex = 2;
                break;
            }
            default: {
                activeIndex = 1;
                this._timeouer = (type - 2000) / 24;
                // console.log(type)
                break;
            }
        }
        if (activeIndex === this._activeIndex) {
            return;
        }
        this._activeIndex = activeIndex;
        this.setState({});
    }

    private renderList(): any {
        const activeIndex = this._activeIndex;
        return this._datas.map((data, index) => {
            let className = data + ' label';
            if (activeIndex === index) {
                className += ' active';
            }

            return (<span key={data} className={className}/>);
        });
    }

    private renderMessage(): any {
        if (this._bActive !== true) {
            return null;
        }

        let message = this._messages[this._activeIndex];
        let time: any = this._timeouer;
        if (this._activeIndex === 1 && time != null) {
            time = time.toFixed(2);
            message = message.replace('${1}', time);
        }
        return (<div className='msg'><span className='msg-text'>{message}</span></div>);
    }
}
