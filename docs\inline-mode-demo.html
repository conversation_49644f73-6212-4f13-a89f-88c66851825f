<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>鸿至编辑器内联模式演示</title>
  <script src="http://localhost:7075/sdk.js"></script>
  <style>
    body {
      font-family: 'Microsoft YaHei', sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f5f5f5;
    }
    .container {
      max-width: 1000px;
      margin: 0 auto;
      background-color: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
    h1 {
      color: #333;
      text-align: center;
      margin-bottom: 20px;
    }
    .editor-container {
      width: 100%;
      border: 1px solid #ddd;
      border-radius: 4px;
      margin-bottom: 20px;
      min-height: 300px;
      position: relative;
      transition: height 0.3s ease;
    }
    .editor-wrapper {
      width: 100%;
      height: 100%;
      position: relative;
    }
    .controls {
      margin-bottom: 20px;
      display: flex;
      justify-content: center;
      gap: 10px;
    }
    button {
      padding: 8px 16px;
      background-color: #4285f4;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
    }
    button:hover {
      background-color: #3367d6;
    }
    .status {
      margin-top: 20px;
      padding: 10px;
      background-color: #f0f0f0;
      border-radius: 4px;
      font-size: 14px;
    }
    .info {
      color: #666;
      margin-bottom: 10px;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>鸿至编辑器内联模式演示</h1>
    
    <div class="info">
      此演示展示了编辑器在内联模式下，宽度固定而高度随内容自动调整的功能。
    </div>
    
    <div class="controls">
      <button id="createNew">创建新文档</button>
      <button id="toggleInlineMode">切换内联模式</button>
      <button id="setWidth600">设置宽度600px</button>
      <button id="setWidth800">设置宽度800px</button>
    </div>
    
    <div id="editor-container" class="editor-container">
      <div id="editor" class="editor-wrapper"></div>
    </div>
    
    <div class="status">
      <div>状态信息：</div>
      <div id="status-info">等待初始化...</div>
      <div>编辑器高度：<span id="editor-height">0</span>px</div>
      <div>编辑器宽度：<span id="editor-width">0</span>px</div>
    </div>
  </div>

  <script>
    // 全局变量
    let editor = null;
    let editorInstance = null;
    let isInlineMode = false;
    const editorContainer = document.getElementById('editor-container');
    const statusInfo = document.getElementById('status-info');
    const editorHeightDisplay = document.getElementById('editor-height');
    const editorWidthDisplay = document.getElementById('editor-width');
    
    // 更新状态信息
    function updateStatus(message) {
      statusInfo.textContent = message;
      console.log(message);
    }
    
    // 更新尺寸显示
    function updateSizeDisplay(width, height) {
      editorHeightDisplay.textContent = Math.round(height);
      editorWidthDisplay.textContent = Math.round(width);
    }
    
    // 内联模式下的高度变化监听器
    function handleInlineEvent(rectInfo) {
      if (!rectInfo) {
        return;
      }
      
      // 如果有高度信息，则处理高度变化事件
      if (rectInfo.height) {
        // 添加额外空间以确保内容不被裁剪
        const padding = 100; // 固定的额外空间
        const newHeight = Math.round(rectInfo.height) + padding;
        
        // 更新容器高度
        editorContainer.style.height = `${newHeight}px`;
        
        // 更新尺寸显示
        updateSizeDisplay(rectInfo.width, newHeight);
        updateStatus(`内联模式高度更新: ${newHeight}px`);
      }
    }
    
    // 初始化编辑器
    async function initEditor() {
      try {
        updateStatus("正在初始化编辑器...");
        
        const baseUrl = "http://localhost:7075";
        const vm = await new EmrEditorSDK.Editor().init({
          dom: document.getElementById('editor'),
          src: baseUrl,
          option: {
            bShowMenu: true,
            bShowToolbar: true,
            isTest: false,
            theme: {
              NewControl: {
                ShowRegionOperator: false,
              }
            }
          }
        });
        
        // 获取编辑器实例
        editor = await vm.getEditor();
        editorInstance = vm;
        
        // 设置事件监听
        vm.setEvent({          
          // 监听内联模式高度变化事件
          inlineHeightChange: (rectInfo) => {
            if (isInlineMode && rectInfo && rectInfo.height) {
              // 直接使用事件提供的高度信息
              handleInlineEvent({
                height: rectInfo.height,
                width: rectInfo.width,
                isInline: true,
                cursorX: rectInfo.cursorX,
                cursorY: rectInfo.cursorY
              });
              console.log("内联高度变化:", rectInfo.height);
            }
          },
          // 监听其他事件，如区域变化等
          nsoRegionChanged: (name, type, position) => {
            console.log("区域变化:", name, type, position);
          }
        });
        
        // 创建新文档
        await editor.createNew();
        updateStatus("编辑器初始化完成，已创建新文档");
        
        // 默认启用内联模式
        setTimeout(() => {
          toggleInlineMode();
        }, 1000);
        
        return editor;
      } catch (error) {
        updateStatus(`初始化失败: ${error.message}`);
        console.error("初始化错误:", error);
      }
    }
    
    // 切换内联模式
    async function toggleInlineMode() {
      if (!editor) {
        updateStatus("编辑器尚未初始化");
        return;
      }
      
      try {
        isInlineMode = !isInlineMode;
        
        
        if (isInlineMode) {
          // 获取编辑器容器的实际宽度
          
          const containerWidth = editorContainer.clientWidth;
          
          
          
          
          
          
          // 切换到内联模式，并传递容器宽度
          await editor.setInlineMode(isInlineMode, containerWidth);
          updateStatus(`已切换到内联模式，容器宽度: ${containerWidth}px`);
          
          
        } else {
          // 切换回普通模式
          await editor.setInlineMode(isInlineMode);
          updateStatus('已切换到普通模式');
        }
        
        // 更新按钮文本
        document.getElementById('toggleInlineMode').textContent = isInlineMode ? '关闭内联模式' : '开启内联模式';
      } catch (error) {
        console.error('切换内联模式失败:', error);
        updateStatus(`切换内联模式失败: ${error.message || error}`);
      }
    }
    
    // 设置内联模式宽度
    async function setInlineWidth(width) {
      if (!editor || !isInlineMode) {
        updateStatus("请先启用内联模式");
        return;
      }
      
      try {
        await editor.setInlinePageProp(width, Infinity);
        updateStatus(`内联模式宽度已设置为 ${width}px`);
        
        // 触发一次内容变化，以更新高度
        setTimeout(async () => {
          const rectInfo = await editor.getDocumentRect();
          if (rectInfo) {
            handleInlineHeightChange(rectInfo);
          }
        }, 100);
      } catch (error) {
        updateStatus(`设置宽度失败: ${error.message}`);
        console.error("设置宽度错误:", error);
      }
    }
    
    // 创建新文档
    async function createNewDocument() {
      if (!editor) {
        updateStatus("编辑器尚未初始化");
        return;
      }
      
      try {
        await editor.createNew();
        updateStatus("已创建新文档");
        
        // 如果当前是内联模式，重新应用内联模式设置
        if (isInlineMode) {
          const containerWidth = editorContainer.clientWidth - 2;
          await editor.setInlineMode(true);
          await editor.setInlinePageProp(containerWidth, Infinity);
          
          // 触发一次内容变化，以更新高度
          setTimeout(async () => {
            const rectInfo = await editor.getDocumentRect();
            if (rectInfo) {
              handleInlineHeightChange(rectInfo);
            }
          }, 100);
        }
      } catch (error) {
        updateStatus(`创建文档失败: ${error.message}`);
        console.error("创建文档错误:", error);
      }
    }
    
    // 页面加载完成后初始化
    window.addEventListener('DOMContentLoaded', () => {
      // 初始化编辑器
      initEditor();
      
      // 绑定按钮事件
      document.getElementById('createNew').addEventListener('click', createNewDocument);
      document.getElementById('toggleInlineMode').addEventListener('click', toggleInlineMode);
      document.getElementById('setWidth600').addEventListener('click', () => setInlineWidth(600));
      document.getElementById('setWidth800').addEventListener('click', () => setInlineWidth(800));
    });
  </script>
</body>
</html>
