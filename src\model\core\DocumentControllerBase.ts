import Document from './Document';
import HeaderFooter from './HeaderFooter';
import Styles from './Styles';
import { DocumentSectionType, EquationType, ImageMediaType, INewControlProperty, IRevisionChange, ResultType } from '../../common/commonDefines';
import { ParaElementBase } from './Paragraph/ParaElementBase';
import { IDrawSelectionBounds } from '../DocumentCore';
import DocumentContentElementBase from './DocumentContentElementBase';
import { ICursorProperty } from '../CursorProperty';
import TextProperty from './TextProperty';
import MouseEventHandler, { IMouseEvent } from '../../common/MouseEventHandler';
import Selection from './Selection';
import Paragraph from './Paragraph';
import { NewControl } from './NewControl/NewControl';
import { Region } from './Region';
import { DocumentElementState } from './HistoryState';
import { IOperateResult } from './History';

/**
 * Interface for classes that work with footers / auto shapes / footnotes
 */
export default class DocumentControllerBase {
  public logicDocument: Document;

  constructor(logicDocument: Document) {
    this.logicDocument = logicDocument;
  }

  public canCopy(): boolean {
    return true;
  }

  public isNotOverNewControl(): boolean {
    return false;
  }

  public getLogicDocument(): Document {
    return this.logicDocument;
  }

  /**
   * Is this class a footer controller
   * @param bReturnHeaderFooter: If true, then return a link to the footer.
   */
  public isHeaderFooter(bReturnHeaderFooter: boolean): boolean | HeaderFooter {
    if (bReturnHeaderFooter) {
      return null;
    }
    return false;
  }

  public isFootnote(bReturnFootnote: boolean): any {
    if (bReturnFootnote) {
      return null;
    }
    return false;
  }

  public getCurrentTextProps(): any {
    return;
  }

  /**
   * We get the absolute page number by relative.
   */
  public getAbsolutePage(curPage: number): number {
    return curPage;
  }

  /**
   * Check if the given class is upper in relation to other classes DocumentContent, Document
   * @param bReturnTopDocument: If true, then return the link to the document.
   */
  public isTopDocument(bReturnTopDocument: boolean): any {
    if (bReturnTopDocument) {
      return null;
    }
    return false;
  }

  public getStyles(): Styles {
    return this.logicDocument.getStyles();
  }

  public getTableStyleForPara(): void {
    return null;
  }

  public getTextBackGroundColor(): string {
    return undefined;
  }

  /**
   * Is this class a cell.
   */
  public isCell(bReturnCell: boolean): boolean {
    if (bReturnCell) {
      return null;
    }
    return false;
  }

  public getTheme(): void {
    // return this.logicDocument.getTheme();
    return null;
  }

  public getColorMap(): void {
    return null;
  }

  /**
   * We request information about the end of recalculation of the previous element.
   */
  public getPrevElementEndInfo(curElement: any): void {
    return null;
  }

  public getParentTextTransform(): void {
    return null;
  }

  public checkAutoFit(): void {
    return null;
  }

  public refreshRecalData2(): void {
    return null;
  }

  /**
   * Check if we are in the table.
   * @param bReturnTopTable: Returns an object or true / false
   */
  public isInTable(bReturnTopTable: boolean): any {
    if (bReturnTopTable) {
      return null;
    }
    return false;
  }

  public isInTableCell(): boolean {
      return false;
  }

  public isSelectedTableCells(): boolean {
      return false;
  }

  public getCurrentTable(tableName?: string): any {
      return undefined;
  }

  /**
   * The event that the content has changed and recounted.
   */
  public onContentRecalculate(): void {
    return null;
  }

  /**
   * We get the starting position for the given page.
   */
  public getPageContentStartPos(pageAbs: number): {x: number, y: number, xLimit: number, yLimit: number} {
    return {x: 0, y: 0, xLimit: 0, yLimit: 0};
  }

  /**
   * We expose the current element in this class.
   * @param bUpdateStates
   * @param pageAbs
   * @param oClass - reference to the child class from which this function was called
   */
  public setCurrentElement(bUpdateStates: boolean, pageAbs: number, oClass: any): void {
    return null;
  }

  public getNewControlBySelectArea(): string {
    return;
  }

  // -----------------------------
  // Pure virtual functions
  // -----------------------------

  /**
   * Is it possible to update the cursor position
   */
  public canUpdateTarget(): boolean {
    return true;
  }

  // public isPopWinNewControl(): boolean {
  //   return true;
  // }

  public canInput(): boolean {
      return true;
  }

  public canDelete(direction?: number): boolean {
      return true;
  }

  public canInsertNewControl(): boolean {
    return true;
  }

  /**
   * We recount the current position.
   */
  public recalculateCurPos(bUpdateX: boolean, bUpdateY: boolean): any {
    return {x : 0, y : 0, height : 0, pageNum : 0, internal : {line : 0, page : 0, range : 0}, transform : null};
  }

  /**
   * Get the current page number.
   */
  public getCurPage(): number {
    return -1;
  }

  /**
   * Add a new paragraph.
   * @param {boolean} bRecalculate - Count or not
   * @param {boolean} bForceAdd    - Add a paragraph, skipping all sorts of checks
   */
  public addNewParagraph(bRecalculate?: boolean, bForceAdd?: boolean): number {
    return null;
  }

  /**
   * 插入软回车
   * @returns
   */
  public addSoftNewParagraph(): number {
    return null;
  }

  public addInlineImage(width: number, height: number, src: string, name?: string,
                        type?: EquationType, svgElem?: any, mediaType?: ImageMediaType,
                        mediaSrc?: string, datas?: any): string {
    return null;
  }

  /**
   * Add an inline table.
   * @param nCols
   * @param nRows
   */
  public addInlineTable(nCols: number, nRows: number, tableHeaderNum?: number,
                        tableName?: string, bRepeatHeader?: boolean): boolean {
    return null;
  }

  /**
   * Add the item to the paragraph.
   * @param paraItem
   * @param {boolean} bRecal - Recalculate after performing this function
   */
  public addToParagraph(paraItem: ParaElementBase, bRecal?: boolean): number {
    return null;
  }

  /**
   * We delete the selected part of the document or based on the cursor position.
   */
  public remove(direction: number, bOnlyText: boolean, bOnlySelection?: boolean, bAddText?: boolean):
          IOperateResult {
    return null;
  }

  /**
   * Get the physical position of the cursor on the page.
   */
  public getCursorPosXY(): ICursorProperty {
    return {x: 0, y1: 0, y2: 0, pageNum: 0};
  }

  /**
   * Move the cursor to the beginning.
   * @param {boolean} bAddToSelect - do we add everything in between to the select
   */
  public moveCursorToStartPos(bAddToSelect: boolean): void {
    return null;
  }

  /**
   * Move the cursor to the end.
   * @param {boolean} bAddToSelect - do we add everything in between to the select
   */
  public moveCursorToEndPos(bAddToSelect: boolean): void {
    return null;
  }

  /**
   * Move the cursor to the left
   * @param {boolean} bShiftKey
   */
  public moveCursorLeft(bShiftKey: boolean): void {
    return null;
  }

  /**
   * Move the cursor to the right
   * @param {boolean} bShiftKey
   */
  public moveCursorRight(bShiftKey: boolean): void {
    return null;
  }

  /**
   * Move the cursor up.
   * @param {boolean} bShiftKey
   */
  public moveCursorUp(bShiftKey: boolean): void {
    return null;
  }

  /**
   * Move the cursor down.
   * @param {boolean} bShiftKey
   */
  public moveCursorDown(bShiftKey: boolean): void {
    return null;
  }

  /**
   * Move the cursor to the end of the line.
   * @param bAddToSelect Add offset to select
   */
  public moveCursorToEndOfLine(bAddToSelect: boolean): boolean {
    return false;
  }

  /**
   * Move the cursor to the beginning of the line.
   * @param bAddToSelect Add offset to select
   */
  public moveCursorToStartOfLine(bAddToSelect: boolean): boolean {
    return false;
  }

  /**
   * Move the cursor to the specified position on the page.
   */
  public moveCursorToXY(curPage: number, pointX: number, pointY: number, bAddToSelect: boolean = false): void {
    return null;
  }

  /**
   * Move the cursor to the next or previous cell.
   * @param {boolean} bNext
   */
  public moveCursorToCell(bNext: boolean): void {
    return null;
  }

  /**
   * We establish the fit of the paragraph.
   * @param alignment - fit type
   */
  public setParagraphAlignment(alignment: number): number {
    return null;
  }

  public setParagraphProperty(paraProperty: any): void {
    return null;
  }

  /**
   * We set whether or not to apply the distance between paragraphs of the same style.
   */
  public setParagraphContextualSpacing(bValue: boolean): void {
    //
  }

  /**
   * Set the gap by paragraph.
   */
  public setParagraphPageBreakBefore(bValue: boolean): void {
    //
  }

  /**
   * Set the settings for images.
   */
  public setImageProps(props: any): void {
    //
  }

  /**
   * Set the settings for tables.
   */
  public setTableProps(props: any): void {
    //
  }

  /**
   * remove selection
   */
  public removeSelection(): void {
    //
  }

  /**
   * Check if the select is empty.
   */
  public isSelectionEmpty(bContainParaEnd?: boolean): boolean {
    return true;
  }

  /**
   * We get the boundaries of the select.
   */
  public getSelectionBounds(mouseEvent?: IMouseEvent, pageIndex?: number): IDrawSelectionBounds {
    return null;
  }

  /**
   * Проверяем попадает ли заданная позиция в селект.
   */
  public checkPosInSelection(pageIndex: number, pointX: number, pointY: number, nearPos?: number): boolean {
    return false;
  }

  /**
   * Select all the contents.
   */
  public selectAll(): void {
    return null;
  }

  /**
   * get selected content
   * @param SelectedContent
   */
  public getSelectedContent(bKeepHalfStructBorder: boolean = false,
                            bSelectedContentToSave: boolean = false): DocumentContentElementBase[] {
    return null;
  }

  /**
   * Update the cursor view.
   */
  public updateCursorType(x: number, y: number, pageAbs: number, mouseEvent?: IMouseEvent): void {
    //
  }

  /**
   * Check if the select is currently in use.
   * @returns {boolean}
   */
  public isSelectionUse(): boolean {
    return false;
  }

  /**
   * Check if the text is highlighted now.
   * @returns {boolean}
   */
  public isTextSelectionUse(): boolean {
    return false;
  }

  /**
   * Get the XY of the current position.
   * @returns {{x: number, y: number}}
   */
  public getCurPosXY(): {x: number; y: number} {
    return {x: 0, y: 0};
  }

  /**
   * get Selected Text
   */
  public getSelectedText(bClearText: boolean, oPr: any): string {
    return '';
  }

  /**
   * Get the current paragraph.
   */
  public getCurrentParagraph(): Paragraph {
    return null;
  }

  /**
   * We collect information about the selected part of the document.
   * @param oInfo
   */
  public getSelectedElementsInfo(oInfo: any): void {
    //
  }

  /**
   * Add a row to the table.
   */
  public addTableRow(bBefore: boolean): boolean {
    return false;
  }

  /**
   * Delete the row of the table.
   */
  public removeTableRow(rowIndex?: number): boolean {
    return false;
  }

  public removeTableColumn(): boolean {
    return false;
  }

  public getSelection(): Selection { return ; }

  public getSelectText(): string {
    return;
  }

  public markTextWithWave(): number {
    return;
  }

  public getSelectionRangeStart(): string {
    return;
  }

  public getSelectionRangeEnd(): string {
    return;
  }

  public getNewControlBegin(sName: string): string {
    return;
  }

  public getNewControlEnd(sName: string): string {
    return;
  }

  public getParagraphProperty(): any {}

  /**
   * merge table cells
   */
  public mergeTableCells(bClearMerge?: boolean): boolean {
    //
    return true;
  }

  /**
   * split table cells
   */
  public splitTableCells(cols: number, rows: number): void {
    //
  }

  /**
   * delete table
   */
  public removeTable(): boolean {
    return false;
  }

  /**
   * Check if we can combine the table cells.
   * @returns {boolean}
   */
  public canMergeTableCells(): boolean {
    return false;
  }

  /**
   * update the state of the select and cursor.
   */
  public updateSelectionState(): void {
    //
  }

  /**
   * We get the current state of the select and cursor.
   */
  public getSelectionState(): any {
    return [];
  }

  /**
   * We set the current state of the select and cursor.
   * @param State
   * @param StateIndex
   */
  public setSelectionState(state: any, stateIndex: number): void {
    //
  }

  /**
   * Add a comment.
   * @param Comment
   */
  public addComment(comment: any): string {
    //
    return ResultType.StringEmpty;
  }

  /**
   * Check if you can add a comment.
   * @returns {boolean}
   */
  public canAddComment(): boolean {
    return false;
  }

  /**
   * We start the select from the current position.
   */
  public startSelectionFromCurPos(): void {
    //
  }

  /**
   * Get the settings for the current section.
   * @returns {?CSectionPr}
   */
  public getCurrentSectionPr(): void {
    return null;
  }

  public getDirectTextProperty(): TextProperty {
    return null;
  }

  /**
   * add new control
   * @param property
   * @param sText
   */
  public addNewControl(property: INewControlProperty, sText?: string): number {
    return 0;
  }

  public isCursorInNewControl(): boolean {
    return false;
  }

  // BELOW ARE CUSTOM METHODS

  public getDocumentSelection(): Selection {
    return null;
  }

  public getContentPosByXY(pageIndex: number, pointX: number, pointY: number): number {
    return null;
  }

  public isFocusInNewControl(mouseEvent?: MouseEventHandler, pageIndex?: number): boolean {
    return null;
  }

  public getFocusInNewControl(mouseEvent?: MouseEventHandler, pageIndex?: number): NewControl {
    return null;
  }

  public getFoucsInRegion(mouseEvent: MouseEventHandler, pageIndex: number): Region {
      return null;
  }

  public getParaContentByXY(mouseEvent: MouseEventHandler, pageIndex: number): any {
    return null;
  }

  public getCursorInNewControl(): NewControl {
    return null;
  }

  public getDocumentElementState(): DocumentElementState[][] {
    return [];
  }

  public setDocumentElementState(state: any, stateIndex: number): void {
    return ;
  }

  public getDocumentContent(): DocumentContentElementBase[] {
    return [];
  }

  public getFocusRevision(mouseEvent: MouseEventHandler, pageIndex: number): IRevisionChange[] {
      return null;
  }
  public getAllRevision(elemIds?: number[]): IRevisionChange[] {
    return [];
  }

  public getDocumentSectionType(): DocumentSectionType {
    return this.logicDocument.getDocumentSectionType();
  }

  public getTableCellByXY(pointX: number, pointY: number, pageIndex: number): any {
    return ;
  }

}
