import * as React from 'react';
import '../../style/select.less';
import InputUI from '../Input';
import SelectList from './SelectList';
import { createRoot } from 'react-dom/client'; 
// import { getEditorDomContainer } from '../../../../common/commonDefines';

interface IState {
    bRefresh: boolean;
}

interface IPropKey {
    name: string;
    value: string;
    disabled: string;
}

interface IProps {
    onChange?: (value: any, name: string, item?: any) => void;
    data: object[];
    value: any;
    prop?: IPropKey;
    disabled?: boolean;
    readonly?: boolean;
    name?: string;
    unDeleted?: boolean;
}

let selectObj: {dom: HTMLDivElement,  reactVm: SelectList};

const selectList = {};

const selectIds: number[] = [];
export default class Select extends React.Component<IProps, IState> {
    private _id: number;
    private _selectDOm: any;
    private value: any = '333';
    private prop: IPropKey;
    private label: string;
    private _domDoc: any;
    private _dataIndex: string;
    private root: any = null;
    constructor(props: any) {
        super(props);
        this._id = Math.random();
        selectIds.push(this._id);
        const prop: any = this.props.prop || {};
        this.prop = {
            name: prop.name || 'key',
            value: prop.value || 'value',
            disabled: prop.disabled || 'disabled',
        };
        this._selectDOm = React.createRef();
        this.state = {
            bRefresh: false,
        };
        this.value = this.props.value;
        this.label = this.getLabel();
    }

    public render(): any {
        let className = 'select-input';
        if (this.props.readonly === true) {
            className += ' readonly';
        }
        if (this.props.disabled === true) {
            className += ' disabled';
        }
        return (
            <div className='editor-select'>
                <div className={className} ref={this._selectDOm}>
                    <InputUI
                        onChange={this.onChangeByInput}
                        disabled={this.props.disabled}
                        unDeleted={this.props.unDeleted}
                        readonly={true}
                        value={this.label}
                        placeholder='请选择'
                        addonAfter={this.renderSelectBtn()}
                    />
                </div>
            </div>
        );
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        if (nextProps.value !== this.value) {
            this.value = nextProps.value;
            this.initLabelByValue();
        }
    }

    public componentDidMount(): void {
        this._domDoc = this._selectDOm.current.ownerDocument;
        this.createListDom();
        this.addEvent();
        this.initLabelByValue();
    }

    public componentWillUnmount(): void {
        this.deleteEvent();
        this.removeListDom();
    }

    private initLabelByValue(): void {
        this.label = this.getLabel();
    }

    private getLabel(): string {
        const value = this.value;
        if (value === undefined) {
            return '';
        }
        const datas = this.props.data;
        if (!datas || datas.length === 0) {
            return '';
        }

        const prop = this.prop;
        const lableKey = prop.name;
        const valueKey = prop.value;
        const data = datas.find((item) => item[valueKey] === value);
        let label: string = '';
        if (data) {
            label = data[lableKey];
        }

        return label;
    }

    private onClose = (id: number): void => {
        //
    }

    private onChangeByInput = (value: string): void => {
        this.value = value;
        this.gtSelectObj().reactVm
        .setValue(value);
        this.updateComponentValue(null);
    }

    private onChange = (value: any, item: any): void => {
        this.value = value;
        this.updateComponentValue(item);
        this.label = item[this.prop.name];
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private updateComponentValue(item: any): void {
        if (typeof this.props.onChange === 'function') {
            this.props.onChange(this.value, this.props.name, item);
        }
    }

    private getWidth(target: HTMLDivElement): number {
        let parentNode: HTMLDivElement = target;
        while (parentNode && parentNode.className.indexOf('inputWrapper') === -1) {
            parentNode = parentNode.parentNode as HTMLDivElement;
        }

        if (parentNode) {
            return parentNode.clientWidth;
        }
    }

    private selectHandleClick = (e: any): void => {
        if (this.props.readonly === true || this.props.disabled === true) {
            return;
        }

        const reactVm = this.gtSelectObj().reactVm;
        if (reactVm.isDisplay(this._id)) {
            return;
        }

        const target = e.target;
        const className = target.className;
        if (className === 'closeBtn') {
            return;
        }

        let width: number = this.getWidth(target);
        if (width === undefined) {
            width = target.parentNode.clientWidth;
        }
        // if (className.indexOf('addonAfterGroupWrapper') === -1) {
        //     width = target.offsetParent.clientWidth;
        // }

        const offsetLeft = target.offsetLeft;
        // const offsetTop = target.offsetTop;
        const x = e.clientX - e.offsetX  - offsetLeft;
        if (x < 2) {
            return;
        }
        const y = e.clientY - e.offsetY + target.clientHeight + 3;
        // console.log(x, y, offsetLeft, offsetTop)
        const props = this.props;
        this.gtSelectObj().reactVm
        .setData(props.data, this.value, props.disabled, props.readonly, {
            x,
            y,
            width,
            prop: this.prop,
            id: this._id,
            onChange: this.onChange,
            onClose: this.onClose,
        });
        // reactVm.setId(this._id);
        reactVm.setActive(true);
    }

    private addEvent(): void {
        const dom = this._selectDOm.current;
        if (dom) {
            dom.addEventListener('click', this.selectHandleClick, false);
            dom.addEventListener('keydown', this.preventDefault);
        }
    }

    private deleteEvent = (): void => {
        const dom = this._selectDOm.current;
        if (dom) {
            dom.removeEventListener('click', this.selectHandleClick, false);
            dom.removeEventListener('keydown', this.preventDefault);
        }
    }

    private preventDefault = (e: any): void => {
        if (e.keyCode ===  8) {
            e.preventDefault();
        }
    }

    private renderSelectBtn(): any {
        return (<span className='select-btn' />);
    }

    private gtSelectObj(): any {
        if (this._dataIndex === undefined) {
            const dom = this._domDoc.querySelector('#editor-container-id');
            if (dom) {
                this._dataIndex = dom.getAttribute('data-index');
            } else {
                this._dataIndex = '0';
            }
        }
        return selectList[this._dataIndex] || {};
    }

    // private createListDom(): void {
    //     if (this.gtSelectObj().dom != null) {
    //         return;
    //     }
    //     selectObj = selectList[this._dataIndex] = {dom: null, reactVm: null};
    //     const doc = this._domDoc;
    //     const div = doc.createElement('div');
    //     div.className = 'hz-editor-select-container';
    //     doc.body.appendChild(div);
    //     selectObj.dom = div;
    //     setTimeout(() => {
    //         const select = ReactDOM.render(
    //             <SelectList />,
    //             div,
    //         ) as any;
    //         selectObj.reactVm = select;
    //     }, 0);
    // }

    // private removeListDom(): void {
    //     const activeId = this._id;
    //     const index = selectIds.findIndex((id) => activeId === id);
    //     selectIds.splice(index, 1);
    //     selectObj = selectList[this._domDoc];
    //     if (selectIds.length === 0 && selectObj && selectObj.dom) {
    //         ReactDOM.unmountComponentAtNode(selectObj.dom);
    //         delete selectList[this._domDoc];
    //         selectObj = null;
    //     }
    // }

    private createListDom(): void {
        if (this.gtSelectObj().dom != null) {
            return;
        }
        selectObj = selectList[this._dataIndex] = { dom: null, reactVm: null };
        const doc = this._domDoc;
        const div = doc.createElement('div');
        div.className = 'hz-editor-select-container';
        doc.body.appendChild(div);
        selectObj.dom = div;
    
        setTimeout(() => {
            const root = createRoot(div);
            this.root[this._dataIndex] = root;
            root.render(<SelectList ref={(ref) => (selectObj.reactVm = ref)} />);
        }, 0);
    }
    
    private removeListDom(): void {
        const activeId = this._id;
        const index = selectIds.findIndex((id) => activeId === id);
        selectIds.splice(index, 1);
        selectObj = selectList[this._domDoc];
        if (selectIds.length === 0 && selectObj && selectObj.dom) {
            const root = this.root[this._dataIndex];
            if (root) {
                root.unmount(); // 使用 root.unmount 代替 ReactDOM.unmountComponentAtNode
                delete this.root[this._dataIndex];
            }
            selectObj.dom.remove(); // 从 DOM 中移除元素
            delete selectList[this._domDoc];
            selectObj = null;
        }
    }
}
