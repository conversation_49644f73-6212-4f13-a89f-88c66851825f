import ParaPortion from './ParaPortion';
import { ChangeBaseContent, ChangeBaseObjectProperty, ChangeBaseProperty, ChangeBase,
    ChangeBaseStringProperty, ChangeBaseDoubleProperty, ChangeBaseBoolProperty } from '../HistoryChange';
import { HistroyItemType, HistoryDescriptionType, changeObjectFactory } from '../HistoryDescription';
import { ParaElementBase } from './ParaElementBase';
import ParaText from './ParaText';

/**
 * Portion添加item
 */
export class ChangePortionAddItem extends ChangeBaseContent {
    // public color: boolean;

    constructor( changeClass: ParaPortion, pos: number, items: any, color?: boolean ) {
        super(changeClass, pos, items, true);

        // this.color = ( true === color ) ? true : false;
        this.type = HistroyItemType.ParaPortionAddItem;
    }

    /**
     * 删除加入的items
     */
    public undo(): void {
        const portion = this.changeClass;
        portion.content.splice(this.position, this.items.length);

        portion.recalcInfo.bMeasure = true;
    }

    /**
     * 重新加入items
     */
    public redo(): void {
        const portion = this.changeClass;
        const portionStart = portion.content.slice(0, this.position);
        const portionEnd = portion.content.slice(this.position);

        portion.content = portionStart.concat(this.items, portionEnd);
        portion.recalcInfo.bMeasure = true;

        // for (let index = 0, nCount = this.items.length; index < nCount; index++) {

        //     if ( this.items.setParent ) {
        //         this.items.setParent(portion);
        //     }
        // }
    }

    public createReverseChange(): ChangePortionRemoveItem {
        return this.createReverseChangeBase(ChangePortionRemoveItem);
    }

    public isDirty(): boolean {
        return (this.changeClass && this.changeClass.isTableCellContent());
    }

    public write(): any {
        const changeClass = this.changeClass.write(this.isAdd());
        return {
            type: this.type,
            position: this.position,
            content: this.getItemsContent(),
            changeClass,
        };
    }

    public loadIncrementDatas(datas: any): void {
        // const data = datas.data;
        this.position = datas.position;
        const items: any[] = [];
        const content = datas.content.split(',');
        content.forEach((text) => {
            const paraText = new ParaText(text);
            items.push(paraText);
        });

        this.items = items;

        const index = this.changeClass.getCurInParaIndex();
        const name = this.changeClass.getInsideNewControlName(index, true);
        if ( null !== name ) {
            const para = this.changeClass.paragraph;
            para.content[index - 1].bPlaceHolder = false;
            this.changeClass.content = [];
        }

        // this.changeClass = ReadFromIncrementDatas(data.changeClass);
    }
}

/**
 * Portion删除添加items
 */
export class ChangePortionRemoveItem extends ChangeBaseContent {
    private bLoad: boolean;
    private length: number;
    private portionIndex: number;

    constructor( changeClass: ParaPortion, pos: number, items: any, bAdd?: boolean ) {
        super(changeClass, pos, items, false);
        this.type = HistroyItemType.ParaPortionRemoveItem;
        this.bLoad = (-1 === pos ? true : false);
        this.portionIndex = -1;
    }

    public undo(): void {
        const portion = this.changeClass;
        const portionStart = portion.content.slice(0, this.position);
        const portionEnd = portion.content.slice(this.position);

        portion.content = portionStart.concat(this.items, portionEnd);
        portion.recalcInfo.bMeasure = true;

        // for (let index = 0, nCount = this.items.length; index < nCount; index++) {

        //     if ( this.items.setParent ) {
        //         this.items.setParent(portion);
        //     }
        // }
    }

    public redo(): void {
        const portion = this.changeClass;
        const removeLength = (this.bLoad ? this.length : this.items.length);
        portion.content.splice(this.position, removeLength);
        portion.recalcInfo.bMeasure = true;

        if ( this.bLoad && 0 === portion.content.length ) {
            const index = portion.getCurInParaIndex();
            const name = portion.getInsideNewControlName(index, true);
            if ( null !== name ) {
                const para = portion.paragraph;
                para.content[index - 1].bPlaceHolder = true;
                para.checkNewControlPlaceContent2(name);
            }
        }
    }

    public createReverseChange(): ChangePortionAddItem {
        return this.createReverseChangeBase(ChangePortionAddItem);
    }

    public isDirty(): boolean {
        return (this.changeClass && this.changeClass.isTableCellContent());
    }

    public write(): any {
        const changeClass = this.changeClass.write(this.isAdd());
        return {
            type: this.type,
            position: this.position,
            length: this.items.length,
            content: this.getItemsContent(),
            changeClass,
        };
    }

    public loadIncrementDatas(datas: any): void {
        // const data = datas.data;
        this.position = datas.position;
        const items: any[] = [];
        // const content = data.content.split(',');
        // data.content.forEach(text => {
        //     const paraText = new ParaText(text);
        //     items.push(paraText);
        // });
        for (let index = 0, length = datas.content.length; index < length; index++) {
        // datas.content.forEach((item) => {
            const item = datas.content[index];
            const paraText = new ParaText(item);
            items.push(paraText);
        }

        this.items = items;
        this.length = datas.content.length;
        // this.changeClass = ReadFromIncrementDatas(data.changeClass);
    }
}

/**
 * portion文本属性
 */
// tslint:disable-next-line: max-classes-per-file
export class ChangePortionTextProperty extends ChangeBaseObjectProperty {
    private type: HistroyItemType|HistoryDescriptionType = null;

    constructor( changeClass: ParaPortion, old: any, news: any, color: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.ParaPortionTextProperty;
    }

    public setValue( value: any ): void {
        const portion = this.getClass();
        portion.textProperty = value;
    }

    // public write(): any {
    //     return super.write(this.type);
    // }

}

/**
 * portion文本属性变化
 */
// tslint:disable-next-line: max-classes-per-file
export class ChangePortionPropertyChange extends ChangeBaseProperty {
    private type: HistroyItemType|HistoryDescriptionType = null;

    constructor( changeClass: ParaPortion, old: any, news: any, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.ParaPortionPropertyChange;
    }

    public setValue( value: any ): void {
        const portion = this.getClass();
        portion.textProperty.propertyChange = value.propertyChange;
    }
}

/**
 * portion开始分割
 */
// tslint:disable-next-line: max-classes-per-file
export class ChangePortionStartSplit extends ChangeBase {
    private position: number;
    private type: HistroyItemType|HistoryDescriptionType = null;

    constructor( changeClass: ParaPortion, pos: number ) {
        super(changeClass);
        this.position = pos;
        this.type = HistroyItemType.ParaPortionStartSplit;
    }

    public undo(): void {
        // let portion = this.changeClass;
        // portion.content.splice(0, portion.content.length);
    }

    public redo(): void {
        //
    }
}

/**
 * portion结束分割
 */
// tslint:disable-next-line: max-classes-per-file
export class ChangePortionEndSplit extends ChangeBase {
    private newPortion: ParaPortion;
    private type: HistroyItemType|HistoryDescriptionType = null;

    constructor( changeClass: ParaPortion, news: ParaPortion ) {
        super(changeClass);
        this.newPortion = news;
        this.type = HistroyItemType.ParaPortionEndSplit;
    }

    public undo(): void {
        // let portion = this.changeClass;
        // let para = portion.paragraph;
        // para.content[para.content.length - 1].content.concat(this.newPortion.content);
        // this.newPortion.content.splice(0, this.newPortion.content.length);
    }

    public redo(): void {
        //
    }
}

/**
 * portion文本字体
 */
// tslint:disable-next-line: max-classes-per-file
export class ChangePortionFont extends ChangeBaseStringProperty {
    private type: HistroyItemType|HistoryDescriptionType = null;

    constructor( changeClass: ParaPortion, old: string, news: string, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.ParaPortionFont;
    }

    public setValue( value: string ): void {
        const portion = this.getClass();
        portion.textProperty.fontFamily = value;
        portion.recalcInfo.bMeasure = true;
    }
}

/**
 * portion文本字号
 */
// tslint:disable-next-line: max-classes-per-file
export class ChangePortionFontSize extends ChangeBaseDoubleProperty {
    private type: HistroyItemType|HistoryDescriptionType = null;

    constructor( changeClass: ParaPortion, old: number, news: number, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.ParaPortionFontSize;
    }

    public setValue( value: number ): void {
        const portion = this.getClass();
        portion.textProperty.fontSize = value;
        portion.recalcInfo.bMeasure = true;
    }
}

/**
 * portion文本粗体
 */
// tslint:disable-next-line: max-classes-per-file
export class ChangePortionBold extends ChangeBaseDoubleProperty {
    private type: HistroyItemType|HistoryDescriptionType = null;

    constructor( changeClass: ParaPortion, old: number, news: number, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.ParaPortionBold;
    }

    public setValue( value: number ): void {
        const portion = this.getClass();
        portion.textProperty.fontWeight = value;
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangePortionDecorationLine extends ChangeBaseDoubleProperty {
    private type: HistroyItemType | HistoryDescriptionType = null;

    constructor(changeClass: ParaPortion, old: number, news: number, color?: boolean) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.ParaPortionDecorationLine;
    }

    public setValue(value: number): void {
        const portion = this.getClass();
        portion.textProperty.textDecorationLine = value;
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangePortionItalic extends ChangeBaseDoubleProperty {
    private type: HistroyItemType | HistoryDescriptionType = null;

    constructor(changeClass: ParaPortion, old: number, news: number, color?: boolean) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.ParaPortionItalic;
    }

    public setValue(value: number): void {
        const portion = this.getClass();
        portion.textProperty.fontStyle = value;
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangePortionBackgroundColor extends ChangeBaseStringProperty {
    private type: HistroyItemType | HistoryDescriptionType = null;

    constructor(changeClass: ParaPortion, old: string, news: string, color?: boolean) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.ParaPortionBackgroundColor;
    }

    public setValue(value: string): void {
        const portion = this.getClass();
        portion.textProperty.backgroundColor = value;
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangePortionColor extends ChangeBaseStringProperty {
    private type: HistroyItemType | HistoryDescriptionType = null;

    constructor(changeClass: ParaPortion, old: string, news: string, color?: boolean) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.ParaPortionColor;
    }

    public setValue(value: string): void {
        const portion = this.getClass();
        portion.textProperty.color = value;
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangePortionVertAlign extends ChangeBaseDoubleProperty {
    private type: HistroyItemType | HistoryDescriptionType = null;

    constructor(changeClass: ParaPortion, old: number, news: number, color?: boolean) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.ParaPortionVertAlign;
    }

    public setValue(value: number): void {
        const portion = this.getClass();
        portion.textProperty.vertAlign = value;
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeParaElementViewSecret extends ChangeBaseBoolProperty {
    private type: HistroyItemType | HistoryDescriptionType = null;

    constructor(changeClass: ParaElementBase, old: boolean, news: boolean, color?: boolean) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.ParaElementViewSecret;
    }

    public setValue(value: boolean): void {
        const element = this.getClass();
        element.bViewSecret = value;
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeParaElementVisible extends ChangeBaseBoolProperty {
    private type: HistroyItemType | HistoryDescriptionType = null;

    constructor(changeClass: ParaElementBase, old: boolean, news: boolean, color?: boolean) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.ParaElementVisible;
    }

    public setValue(value: boolean): void {
        const element = this.getClass();
        element.bVisible = value;
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangePortionHidden extends  ChangeBaseBoolProperty {
    private type: HistroyItemType | HistoryDescriptionType = null;

    constructor(changeClass: ParaPortion, old: boolean, news: boolean, color?: boolean) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.ParaPortionHidden;
    }

    public setValue(value: boolean): void {
        const element = this.getClass();
        element.bHidden = value;
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangePortionContentReviewInfo extends ChangeBaseObjectProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: ParaPortion, old: any, news: any, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.ParaPortionContentReviewInfo;
    }

    public setValue( value: any ): void {
        const portion = this.getClass();
        portion.reviewInfo = value;
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangePortionPrReviewInfo extends ChangeBaseObjectProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: ParaPortion, old: any, news: any, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.ParaPortionPrReviewInfo;
    }

    public setValue( value: any ): void {
        const portion = this.getClass();
        portion.textProperty.reviewInfo = value;
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangePortionReviewType extends ChangeBaseObjectProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: ParaPortion, old: any, news: any, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.ParaPortionReviewType;
    }

    public setValue( value: any ): void {
        const portion = this.getClass();
        if ( portion ) {
            portion.reviewType = value.reviewType;
            portion.reviewInfo = value.reviewInfo;
            portion.updateTrackRevisions();
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangePortionReplaceText extends ChangeBaseObjectProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: ParaPortion, old: any, news: any, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.ParaPortionReplaceText;
    }

    public setValue( value: any ): void {
        const portion = this.getClass();
        if ( portion && portion.content[0] ) {
            portion.content[0] = value;
        }
    }

    public isDirty(): boolean {
        return (this.changeClass && this.changeClass.isTableCellContent());
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeParaTextReplaceText extends ChangeBaseObjectProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: ParaElementBase, old: any, news: any, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.ParaTextReplaceText;
    }

    public setValue( value: any ): void {
        const paraElement = this.getClass();
        if ( paraElement ) {
            paraElement.content = value;
        }
    }
}

changeObjectFactory[HistroyItemType.ParaPortionAddItem] = ChangePortionAddItem;
changeObjectFactory[HistroyItemType.ParaPortionRemoveItem] = ChangePortionRemoveItem;
changeObjectFactory[HistroyItemType.ParaPortionTextProperty] = ChangePortionTextProperty;
// changePointObject[HistroyItemType.ParaPortionAddItem] = ChangePortionAddItem;
// changePointObject[HistroyItemType.ParaPortionAddItem] = ChangePortionAddItem;
