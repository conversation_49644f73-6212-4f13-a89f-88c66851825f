import * as React from 'react';
import { Sketch } from '@uiw/react-color';

interface IProps {
    color: string;
    onChangeComplete?: (obj: any) => void;
    onChange?: (obj: any) => void;
}
interface IState {
    color: string;
}

// 扩展更多常用颜色
const colors = [
    // 红色系
    '#c00000', '#ff0000', '#ff6666', '#ffcccc',
    // 橙色系
    '#ffa500', '#ff8c00', '#ffd700',
    // 黄色系
    '#ffff00', '#fffacd', '#f0e68c',
    // 绿色系
    '#008000', '#00ff00', '#98fb98', '#00fa9a',
    // 蓝色系
    '#0000ff', '#1e90ff', '#87ceeb', '#00bfff',
    // 紫色系
    '#7030a0', '#800080', '#ba55d3', '#9370db',
    // 黑白灰
    '#000000', '#333333', '#666666', '#999999', '#cccccc', '#ffffff'
];

export class SelectColor extends React.Component<IProps, IState> {
    private _ref: any;
    private _options: any;
    private _color: string;
    private _oldColor: string;
    constructor(props: IProps) {
        super(props);
        this.state = {
            color: this.props.color,
        };
        this._ref = React.createRef();
    }

    public render(): any {
        return (
            <div className='select-color' ref={this._ref}>
                <Sketch 
                    presetColors={colors} 
                    disableAlpha={true} 
                    color={this.state.color}
                    style={{ 
                        // 添加自定义样式隐藏输入框和文字
                        width: '230px'
                    }}
                    onChange={(color) => {
                        this._options = { hex: color.hex };
                        this.setState({ color: color.hex });
                        this.emitParent();
                    }}
                />
                <style>
                {`
                    /* 隐藏HEX、R、G、B输入框和文字 */
                    .w-color-sketch-fields,
                    .sketch-picker input,
                    .w-color-sketch input,
                    .w-color-sketch-field,
                    .w-color-sketch-field-label,
                    .w-color-sketch-field-wrap {
                        display: none !important;
                    }
                `}
                </style>
            </div>
        );
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        if (this.state.color !== nextProps.color) {
            this._color = nextProps.color;
            this.setState({color: nextProps.color});
        }
    }

    public componentDidMount(): void {
        this._ref.current.addEventListener('click', this.onClick);
    }

    public componentWillUnmount(): void {
        this._ref.current?.removeEventListener('click', this.onClick);
    }

    private onClick = (e: any): void => {
        const target = e.target;
        const className: string = target.className;
        if (className.search(/saturation-black|saturation-white/) > -1) {
            this.emitParent();
            return;
        } else {
            const attr = target.getAttribute('title');
            if (attr) {
                this.emitParent();
                return;
            }
        }
        e.stopPropagation();
    }

    private onChange = (options: any): void => {
        this._options = options;
        if (this.state.color !== options.hex) {
            this.setState({color: options.hex});
        }
    }

    private emitParent(): void {
        setTimeout(() => {
            const obj = this._options;
            if (this.props.onChange && obj && obj.hex !== this._color) {
                this.props.onChange(this._options);
            }
        }, 0);
    }
}
