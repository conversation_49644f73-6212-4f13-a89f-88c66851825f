// import { Table } from '../Table';
import { TableCell } from './TableCell';
import { TableRowProperty, TableRowHeight, TableRowLineRule } from './TableRowProperty';
import { TableCellInfo } from './TableCellInfo';
import { TableMeasurement } from '../TableProperty';
import { idCounter } from '../util';
import { VerticalMergeType } from './TableCellProperty';
import { ChangeTableRowAddCell, ChangeTableRowRemoveCell, ChangeTableRowProperty,
    ChangeTableRowTableHeader, ChangeTableRowHeight} from './TableRowChange';
import ContentChanges, { ContentChangesElement } from '../ContentChanges';
import { TableBase } from '../TableBase';
import { INISCellSignInfo, INISRowProperty, NISRowType } from '../../../common/commonDefines';
import { IDrawSelectionsCell } from 'src/model/DocumentCore';
import { HistroyItemType } from '../HistoryDescription';
import { ParagraphContentPos } from '../Paragraph/ParagraphContent';

/**
 * 表格单元行
 */
export class TableRow {
    public id: number;
    public index: number;
    public table: TableBase;

    public prev: TableRow;
    public next: TableRow;

    public content: TableCell[];

    public cellsInfo: TableCellInfo[];

    public metrics: {
        xMin: number;
        xMax: number;
    };

    public height: number;
    public pagesCount: number;

    public property: TableRowProperty;
    private contentChanges: ContentChanges;

    constructor( table: TableBase, cols: number ) {
        this.id = idCounter.getNewId();
        this.index = 0;

        this.prev = null;
        this.next = null;

        this.table = table;
        this.content = [];

        for (let index = 0; index < cols; index++) {
            this.content[index] = new TableCell(this, 0);
        }

        this.reIndex(0);

        this.cellsInfo = [];
        this.metrics = {
            xMin: 0,
            xMax: 0,
        };

        this.height = 0;
        this.pagesCount = 1;

        this.property = new TableRowProperty();
        this.contentChanges = new ContentChanges();

        if ( NURSING_FEATURE && this.table && this.table.isNISTable() ) {
            this.property.initNISRowProperty();
        }
    }

    /**
     * 设置当前行的宽度范围
     * @param xMin
     * @param xMax
     */
    public setMetricsX(xMin: number, xMax: number): void {
        this.metrics.xMin = xMin;
        this.metrics.xMax = xMax;
    }

    public getBefore(): {widthBefore: TableMeasurement, gridBefore: number} {
        return {
            widthBefore: this.property.widthBefore,
            gridBefore: this.property.gridBefore,
        };
    }

    public getAfter(): {widthAfter: TableMeasurement, gridAfter: number} {
        return {
            widthAfter: this.property.widthAfter,
            gridAfter: this.property.gridAfter,
        };
    }

    public setBefore(gridBefore: number, widthBefore?: TableMeasurement, bWidthBefore: boolean = false): void {
        if ( this.property.gridBefore !== gridBefore || this.property.widthBefore !== widthBefore ) {
            const oldBefore = { gridBefore: this.property.gridBefore, widthBefore: this.property.widthBefore };
            const newBefore = { gridBefore, widthBefore };

            if ( false === bWidthBefore ) {
                newBefore.widthBefore = oldBefore.widthBefore;
            } else if ( widthBefore ) {
                newBefore.widthBefore = new TableMeasurement(widthBefore.type, widthBefore.width);
            }

            // gHistory.addChange

            this.property.gridBefore = gridBefore;
            this.property.widthBefore = newBefore.widthBefore;
        }
    }

    public setAfter(gridAfter: number, widthAfter?: TableMeasurement, bWidthAfter: boolean = false): void {
        if ( this.property.gridAfter !== gridAfter || this.property.widthAfter !== widthAfter ) {
            const oldAfter = { gridAfter: this.property.gridAfter, widthAfter: this.property.widthAfter };
            const newAfter = { gridAfter, widthAfter };

            if ( false === bWidthAfter ) {
                newAfter.widthAfter = oldAfter.widthAfter;
            } else if ( widthAfter ) {
                newAfter.widthAfter = new TableMeasurement(widthAfter.type, widthAfter.width);
            }

            // gHistory.addChange

            this.property.gridAfter = gridAfter;
            this.property.widthAfter = newAfter.widthAfter;
        }
    }

    public getCell(index: number): TableCell {
        return this.content[index];
    }

    public getCellsCount(): number {
        return this.content.length;
    }

    public updateCellInfo(cellIndex: number): void {
        const cell = this.content[cellIndex];

        const startGridCol = cell.metrics.startGridCol;
        const xGridStart = cell.metrics.xGridStart;
        const xGridEnd = cell.metrics.xGridEnd;
        const xCellStart = cell.metrics.xCellStart;
        const xCellEnd = cell.metrics.xCellEnd;
        const xContentStart = cell.metrics.xContentStart;
        const xContentEnd = cell.metrics.xContentEnd;

        this.setCellInfo(cellIndex, startGridCol, xGridStart, xGridEnd,
                        xCellStart, xCellEnd, xContentStart, xContentEnd);
    }

    public setCellInfo(cellIndex: number, startGridCol: number, xGridStart: number, xGridEnd: number,
                       xCellStart: number, xCellEnd: number, xContentStart: number, xContentEnd: number): void {
        this.cellsInfo[cellIndex] = new TableCellInfo(startGridCol, xGridStart, xGridEnd,
                                                    xCellStart, xCellEnd, xContentStart, xContentEnd);
    }

    public getCellInfo(cellIndex: number): TableCellInfo {
        if (!this.cellsInfo[cellIndex] || undefined === this.cellsInfo[cellIndex].startGridCol) {
            this.table.recalculateGridCols();
        }

        return this.cellsInfo[cellIndex];
    }

    public getCellSpacing(): number {
        return this.property.cellSpacing;
    }

    public setParentPos(pos: ParagraphContentPos): void {
        pos.unShift(this.index);
        this.table.setParentPos(pos);
    }

    public isSplitOrMerge(): boolean {
        const cells = this.content;
        const table = this.table;
        for (let cellIndex = 0, len = cells.length; cellIndex < len; cellIndex++) {
            const cell = cells[cellIndex];
            if (cell.content.content[0].xLimit < 1) {
                return true;
            }
            if (table.getVMergeCount(cellIndex, this.index) > 1 || cell.getGridSpan() > 1) {
                return true;
            }
        }
    }

    /**
     * 获取指定单元格开始所在列数
     * @param cellIndex
     */
    public getStartGridCol(cellIndex: number): number {
        let curGridCol = this.getBefore().gridBefore;
        const max = Math.min(cellIndex - 1, this.content.length - 1);

        for (let index = 0; index <= max; index++) {
            const cell = this.getCell(index);
            const gridSpan = cell.getGridSpan();

            curGridCol += gridSpan;
        }

        return curGridCol;
    }

    public getHeight(): TableRowHeight {
        return this.property.height;
    }

    public setHeight(value: number, rule: TableRowLineRule): boolean {
        if ( ( !this.height && null == value)
            || ( TableRowLineRule.AtLeast === this.property.height.hRule && TableRowLineRule.Auto === rule )
            || ( this.property.height && rule === this.property.height.hRule
                && Math.abs(value - this.property.height.value) < 1 ) ) {
            return false;
        }

        const oldHeight = this.property.height;
        const newHeight = ( null != value ? new TableRowHeight(value, rule) : undefined);

        if (null == newHeight) {
            return false;
        }

        const history = this.table.getHistory();
        if ( history ) {
            history.addChange(new ChangeTableRowHeight(this, oldHeight, newHeight));
        }

        this.property.height = newHeight;
        return true;
    }

    public getCurrentRowHeight(): number {
        // let height = this.property.height.value;

        if ( this.table.isFixedRowHeight() ||
             (TableRowLineRule.Auto !== this.property.height.hRule &&
                TableRowLineRule.AtLeast !== this.property.height.hRule)
            ) {
            return this.property.height.value;
        }

        let rowH = 0;
        // const curRow = row.getIndex();
        const rowsInfo = this.table.rowsInfo;

        if ( rowsInfo[this.index] ) {
            for ( const curPage of rowsInfo[this.index].height ) {
                if ( null != curPage ) {
                    rowH += curPage;
                }
            }

            for ( const dy of rowsInfo[this.index].topDy ) {
                if ( null != dy ) {
                    rowH -= dy;
                    break;
                }
            }

            rowH -= ( this.getTopMargin() + this.getBottomMargin() );
        }

        return rowH;
    }

    public getTopMargin(): number {
        let margin = 0;

        for (let curCell = 0, count = this.getCellsCount(); curCell < count; curCell++) {
            const cell = this.getCell(curCell);

            if ( VerticalMergeType.Restart !== cell.getVMerge() ) {
                continue;
            }

            const margins = cell.getMargins();
            if ( margins.top.width > margin ) {
                margin = margins.top.width;
            }
        }

        return margin;
    }

    public getBottomMargin(): number {
        let margin = 0;

        for (let curCell = 0, count = this.getCellsCount(); curCell < count; curCell++) {
            const cell = this.getCell(curCell);

            if ( VerticalMergeType.Restart !== cell.getVMerge() ) {
                continue;
            }

            const vMergeCount = this.table.getVMergeCount(curCell, this.index);
            if ( 1 < vMergeCount ) {
                continue;
            }

            const margins = cell.getMargins();
            if ( margins.bottom.width > margin ) {
                margin = margins.bottom.width;
            }
        }

        return margin;
    }

    public setIndex(index: number): void {
        if ( index !== this.index ) {
            this.index = index;
        }
    }

    /**
     * 是否是表格标题行
     */
    // public isHeader(): boolean {
    //     return this.property.bTableHeader;
    // }

    public preDelete(): void {
        for (const cell of this.content) {
            for (const element of cell.content.content) {
                this.table.getDrawingObjects()
                .deleteImageFromParaTableRemoval(element.id, false);
            }

            // cell.content.removeAllNewControl();
        }
    }

    /**
     * 计算cells的上下关系
     * @param startIndex
     */
    public reIndex(startIndex: number): void {
        if ( null === startIndex ) {
            startIndex = 0;
        }

        for (let index = startIndex, cellsCount = this.content.length; index < cellsCount; index++) {
            const cell = this.content[index];
            cell.setIndex(index);
            cell.prev = ( index > 0 ? this.content[index - 1] : null );
            cell.next = ( index < cellsCount - 1 ? this.content[index + 1] : null );
        }
    }

    /** 移除内部包含的批注内容 */
    public removeContainedComments(bRemovePortion: boolean = false): void {
        for (const cell of this.content) {
            cell.removeContainedComments(bRemovePortion);
        }
    }

    public removeCell(index: number, bRemoveNewControl: boolean = true): void {
        const history = this.table.getHistory();
        if ( history ) {
            history.addChange(new ChangeTableRowRemoveCell(this, index, [this.content[index]]));
        }

        const delItem = this.content.splice(index, 1);

        delItem[0].content.removeAllNewControl(bRemoveNewControl);

        for (const element of delItem[0].content.content) {
            this.table.getDrawingObjects()
            .deleteImageFromParaTableRemoval(element.id, false);
        }

        this.cellsInfo.splice(index, 1);

        this.reIndex(index);
        this.checkCurCell();
    }

    public addCell(index: number, row: TableRow, cell: TableCell, bReIndex: boolean): TableCell {
        if ( !cell ) {
            cell = new TableCell(row);
        }

        const history = this.table.getHistory();
        if ( history && history.canAdd() ) {
            history.addChange(new ChangeTableRowAddCell(this, index, [cell]));
        }

        this.content.splice(index, 0, cell);
        this.cellsInfo.splice(index, 0);

        if ( true !== bReIndex ) {
            if ( 0 < index ) {
                this.content[index - 1].next = cell;
                cell.prev = this.content[index - 1];
            } else {
                cell.prev = null;
            }

            if ( this.content.length - 1 > index ) {
                this.content[index + 1].prev = cell;
                cell.next = this.content[index + 1];
            } else {
                cell.next = null;
            }

        } else {
            this.reIndex(index);
        }

        return cell;
    }

    public isEmptyRow(): boolean {
        const cells = this.content;
        const table = this.table;
        for (let index = 0, len = cells.length; index < len; index++) {
            const cell = cells[index];
            if (table.getVMergeCount(index, this.index) > 1 || cell.getGridSpan() > 1) {
                return false;
            }
            if (!cell.content.isEmptyCell()) {
                return false;
            }
        }

        return true;
    }

    public copy(table: TableBase, option?: any): TableRow {
        const newRow = new TableRow(table, 0);
        // newRow.setProperty(this.property.copy());
        newRow.property = this.property.copy();

        const history = this.table.getHistory();
        for (let index = 0, count = this.content.length; index < count; index++) {
            newRow.content[index] = this.content[index].copy(newRow, option);
            if ( history ) {
                history.addChange(new ChangeTableRowAddCell(newRow, index, [newRow.content[index]]));
            }
        }

        newRow.reIndex(0);

        return newRow;
    }

    public copyProperty(otherProperty: TableRowProperty): void {
        if ( undefined === otherProperty.widthBefore ) {
            this.setBefore(otherProperty.gridBefore, undefined);
        } else {
            this.setBefore(otherProperty.gridBefore,
                new TableMeasurement(otherProperty.widthBefore.type, otherProperty.widthBefore.width));
        }

        if ( undefined === otherProperty.widthAfter ) {
            this.setAfter(otherProperty.gridAfter, undefined);
        } else {
            this.setAfter(otherProperty.gridAfter,
                new TableMeasurement(otherProperty.widthAfter.type, otherProperty.widthAfter.width));
        }

        if ( undefined === otherProperty.height ) {
            this.setHeight(undefined, undefined);
        } else {
            this.setHeight(otherProperty.height.value, otherProperty.height.hRule);
        }

        if ( undefined === otherProperty.bTableHeader ) {
            this.setTableHeader(undefined);
        } else {
            this.setTableHeader(otherProperty.bTableHeader);
        }

        if ( undefined === otherProperty.bReadOnly ) {
            this.setReadOnly(false);
        } else {
            this.setReadOnly(otherProperty.bReadOnly);
        }
    }

    public setProperty(rowProperty: TableRowProperty): void {
        const history = this.table.getHistory();
        if ( history ) {
            history.addChange(new ChangeTableRowProperty(this, this.property, rowProperty));
        }

        this.property = rowProperty;
    }

    public setTableHeader(bTableHeader: boolean): void {
        if ( bTableHeader === this.property.bTableHeader ) {
            return;
        }

        const history = this.table.getHistory();
        if ( history ) {
            history.addChange(new ChangeTableRowTableHeader(this, this.property.bTableHeader, bTableHeader));
        }
        this.property.bTableHeader = bTableHeader;

        if (this.table.isNISTable()) {
            if (bTableHeader) {
                for (const cell of this.content) {
                    if ( !cell.isTextCell() ) {
                        cell.property.initNISProperty(this.table.logicDocument);
                        // cell.content.clearContent(true);
                    }
                }
            }

            this.setNISRowType(bTableHeader ? NISRowType.TableHeader : NISRowType.Normal);
        }
    }

    /**
     * 是否是表格标题行
     */
    public isTableHeader(): boolean {
        return this.property.bTableHeader;
    }

    public getTable(): TableBase {
        return this.table;
    }

    public getIndex(): number {
        return this.index;
    }

    /**
     * 检查当前光标所在单元格的合法性
     */
    public checkCurCell(): void {
        if ( this.table ) {
            this.table.checkCurCell();
        }
    }

    public addContentChanges( changes: ContentChangesElement ): void {
        this.contentChanges.add(changes);
    }

    public isAuto(): boolean {
        return TableRowLineRule.Auto === this.property.height.hRule;
    }

    public isFixed(): boolean {
        return TableRowLineRule.Exact === this.property.height.hRule;
    }

    /**
     * 是否行只读
     * @returns
     */
    public isReadOnly(): boolean {
        return this.property.bReadOnly;
    }

    public setReadOnly(bReadOnly: boolean, bNIS?: boolean): boolean {
        if ( bReadOnly === this.property.bReadOnly ) {
            return false;
        }

        let bChanged = false;
        if (bNIS === true) {
            const cells = this.content;
            for (let index = 0, length = this.content.length; index < length; index++) {
                const cell = cells[index];
                if (cell.isBPCell() || cell.isTimeCell()) {
                    bChanged = cell.setCellReadonly(bReadOnly) || bChanged;
                }
            }
        }

        // const history = this.table.getHistory();
        // if ( history ) {
        //     history.addChange(new ChangeTableRowTableHeader(this, this.property.bTableHeader, bTableHeader));
        // }
        this.property.bReadOnly = bReadOnly;
        this.property.readOnlyBackgroundColor = bReadOnly ? '#d3aa65' : '';
        return bChanged;
    }

    public isExistSignCell(): boolean {
        for (let index = 0, length = this.content.length; index < length; index++) {
            const cell = this.content[index];
            if ( cell && cell.isSignCell() ) {
                return true;
            }
        }

        return false;
    }

    public getSignCellsInCurRow(): TableCell[] {
        const cells: TableCell[] = [];
        for (let index = 0, length = this.content.length; index < length; index++) {
            const cell = this.content[index];
            if ( cell && cell.isSignCell() ) {
                cells.push(cell);
            }
        }

        return cells;
    }

    public getRowBounds2(pageIndex: number): IDrawSelectionsCell[] {
        const result: IDrawSelectionsCell[] = [];
        const contents = this.content;
        for (let index = 0, length = this.content.length; index < length; index++) {
            const res = contents[index].getSelectionBounds2(pageIndex);
            if (!res) {
                continue;
            }

            const cell = res[0];
            // if (res.length > 1) {
            //     for (let resIndex = 0, resLen = res.length; resIndex < resLen; resIndex++) {
            //         cell.height += res[resIndex].height;
            //     }
            // }

            if (cell) {
                result.push(cell);
            }
        }

        return result;
    }

    public getNISRowProperty(): INISRowProperty {
        return this.property.getNISRowProperty();
    }

    public getNISRowID(): string {
        return this.property.getRowID();
    }

    public setNISRowProperty(prop: INISRowProperty): boolean {
        if ( prop ) {
            this.property.setNISRowProperty(prop);

            return true;
        }

        return false;
    }

    public isSignedRow(): number {
        return this.property.getSignStatus();
    }

    public isDeleteProtect(): boolean {
        return this.property.isDeleteProtect();
    }

    // public setSignContent(sJson: string): boolean {
    //     let result = false;
    //     let cell: TableCell = null;

    //     for (const curCell of this.content) {
    //         if ( curCell && curCell.isSignCell() ) {
    //             cell = curCell;
    //             break;
    //         }
    //     }

    //     if ( cell ) {
    //         result = cell.content.setSignContent(sJson);
    //     }

    //     return result;
    // }

    public deleteSignContent(sJson: string): boolean {
        // let result = false;
        // let cell: TableCell = null;

        // for (const curCell of this.content) {
        //     if ( curCell && curCell.isSignCell() ) {
        //         cell = curCell;
        //         break;
        //     }
        // }

        // if ( cell ) {
        //     result = cell.content.deleteSignContent(sJson);
        // }

        return false;
    }

    public deleteSignContentByAuthor(author: string): boolean {
        let res = false;
        this.content.forEach((cell) => {
            if (cell && cell.isSignCell()) {
                res = cell.deleteSignContentByAuthor(author) || res;
            }
        });

        return res;
    }

    public async signCell(sJson: any): Promise<boolean> {
        let res = false;
        const signCells = this.getSignCellsInCurRow();
        if ( signCells && 0 < signCells.length ) {
            const signCell = signCells[0];

            if ( signCell ) {
                res = await signCell.content.setSignContent2(sJson);
                if ( res ) {
                    signCell.content.setCellContentProps(signCell.getCellContentProps());
                }
            }
        }

        return res;
    }

    public deleteSignCellsText(): boolean {
        const signCells = this.getSignCellsInCurRow();
        if ( signCells && 0 < signCells.length ) {
            const signCell = signCells[0];

            if ( signCell ) {
                signCell.content.clearContent(true);
            }
        }

        return true;
    }

    public setNISRowID(id: string): number {
        return this.property.setRowID(id);
    }

    public setNISCreator(creator: string): number {
        return this.property.setNISCreator(creator);
    }

    public getNISCreator(): string {
        return this.property.getNISCreator();
    }

    public getNISSignInfos(): INISCellSignInfo[] {
        return this.property.getNISSignInfos();
    }

    public getSignStatus(): number {
        return this.property.getSignStatus();
    }

    public setSignStatus(status: number): number {
        return this.property.setSignStatus(status);
    }

    public getSignsByAuthor(author: string): string[] {
        const newControlName = this.getNISRowID() + '-'  + author;
        const newControlManager = this.table.getNewControlManager();
        return newControlManager.getNewControlContainName(newControlName);
    }

    public getCellByColIndex(colIndex: number): TableCell {
        let cell: TableCell = null;
        for (let index = 0, length = this.content.length; index < length; index++) {
            const curCell = this.getCell(index);
            if ( curCell && curCell.metrics.startGridCol <= colIndex
                && curCell.metrics.startGridCol + curCell.getGridSpan() > colIndex ) {
                cell = curCell;
                break;
            }
        }

        return cell;
    }

    public getCellByColID(colID: string): TableCell {
        const colIDs = (this.table as any).getNISTableColIDs();

        for (let index = 0, length = this.content.length; index < length; index++) {
            const cell = this.getCell(index);
            if ( cell && colIDs[index] === colID ) {
                return cell;
            }
        }

        return null;
    }

    public isSumRow(): boolean {
        return this.property.isSumRow();
    }

    public isSumDetailRow(): boolean {
        return this.property.isSumDetailRow();
    }

    public getSumStatus(): number {
        return this.property.getSumStatus();
    }

    public setNISRowType(type: NISRowType): number {
        return this.property.setNISRowType(type);
    }

    public getNISRowType(): number {
        return this.property.getNISRowType();
    }

    public refreshRecalData(data: any): void {
        const table = this.table;
        if (!table) {
            return;
        }

        let bNeedRecalc = false;

        switch (data.type) {
            case HistroyItemType.DocumentContentAddItem:
            case HistroyItemType.DocumentContentAddItem: {
                bNeedRecalc = true;
                break;
            }
            default:
                break;
        }

        for (let index = 0, cellsCount = this.getCellsCount(); index < cellsCount; index++) {
            table.recalcInfo.addCell(this.getCell(index));
        }

        table.recalcInfo.recalcBorders();
        this.refreshRecalData2(0, 0);
    }

    public refreshRecalData2( index: number, pageIndex: number ): void {
        this.table.refreshRecalData2(this.index, pageIndex);
    }
}
