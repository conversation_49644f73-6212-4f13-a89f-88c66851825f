import { ISerialBase } from '../serialInterface';
import ISerial from './ISerial';
import SerialParagraph from './SerialParagraph';
import SerialTable from './SerialTable';

/** 将现有模型转换为简单的可序列化模型 （去除section） */
export default class SerialDocument implements ISerial {

    constructor(private readonly doc: any) { }

    public serializedTo(collector: ISerialBase[] = []): ISerialBase[] {
        if (!this.doc) {
            return collector;
        }
        if (Array.isArray(this.doc)) {
            this.buildChildren(this.doc, collector);
        } else if (Array.isArray(this.doc.content)) {
            this.buildChildren(this.doc.content, collector);
        }
        return collector;
    }

    /**
     * 将序列化内容填充存储容器中
     * @param contents 内容集合
     * @param collects 存储容器集合
     * @returns 存储容器
     */
    private buildChildren(contents: any[], collector: ISerialBase[] = []): ISerialBase[] {
        if (!contents) {
            return collector;
        }
        for (const content of contents) {
            if (content.isParagraph()) {
                new SerialParagraph(content).serializedTo(collector as any[]);
            } else if (content.isRegion()) {
                this.buildChildren(content.getContent(), collector);
            } else if (content.isTable()) {
                new SerialTable(content).serializedTo(collector as any);
            }
        }
        return collector;
    }

}
