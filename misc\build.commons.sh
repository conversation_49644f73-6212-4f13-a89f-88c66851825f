
#!/bin/bash

set -x

npm run pack:site --node-flags --max-old-space-size=8192 --no-warnings

. ./misc/inject_version.sh
. ./misc/publish.sh


export VER_TEMPLATE=$(sed s/__EMR_EDITOR_VER__/__EMR_EDITOR_VER__=\"\$\{NEW_VER\}\"/g ./dist/iframe/index.html)
echo $VER_TEMPLATE | envsubst > ./dist/iframe/index.html


echo "{\"version:\": \"$CI_COMMIT_REF_SLUG/$NEW_VER\"}" > ./dist/version.json


# upload dist folder to hz-editor-nis-website
mkdir -pvm 0700 .ssh
echo "$ssh_deploy_key" > .ssh/id_rsa-gitlab-ci
chmod 0400 .ssh/id_rsa-gitlab-ci
rm -rf ./hz-editor-nis-website/commons
cp -r dist/iframe ./hz-editor-nis-website/commons
mv .ssh ./hz-editor-nis-website/
cd hz-editor-nis-website
git config --global user.email $(git --no-pager show -s --format='%ae' HEAD)
git config --global user.name $(git --no-pager show -s --format='%an' HEAD)
sed -i "s/FOLDER=.*/FOLDER=$NEW_VER/" Dockerfile
git add commons
git commit -am "new release $NEW_VER from hz-editor commons" || true
git push origin site-commons|| true

