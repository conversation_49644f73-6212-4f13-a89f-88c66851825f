import Paragraph from '../Paragraph';
import PortionRecalculateObject from './PortionRecalculateObject';

export class ParagraphContentBase {
    public paragraph: Paragraph; // 所在段落
    public lines: number[];  // 记录porion内容元素在行中的位置信息
    public linesLength: number; // 占据的行数

    public startLine: number; // 开始的行号
    public startRange: number;  // 开始的range

    // spaces: number[] = []; // 两端对齐：插入字符的空格的位置
    // spaceWitdh: number = 0; // 插入的空格宽度

    constructor() {
        this.paragraph = null;
        this.lines = [];
        this.linesLength = 0;
        this.startLine = -1;
        this.startRange = -1;
    }

    public getParagraph(): Paragraph {
        return this.paragraph;
    }

    public saveRecalculateObject( bCopy: boolean ): PortionRecalculateObject {
        const obj = new PortionRecalculateObject(this.startLine, this.startRange);

        return obj;
    }

    public loadRecalculateObject( obj: PortionRecalculateObject ): void {
        this.startLine = obj.startLine;
        this.startRange = obj.startRange;
    }
}

export default class ParagraphContentWithContentBase extends ParagraphContentBase {

    constructor() {
        super();
        this.lines = [0];
    }

    public recalculateReset( startLine: number, startRange: number = 0): void {
        this.startRange = startRange;
        this.startLine = startLine;
        this.lines = [0];
    }

    public addRange(curLine: number, curRange: number = 0): number {
        //  删除有关行和行的不必要记录
        if (this.lines[0] >= curLine + 1) {
            const offset = this.getRangeOffset(curLine, 0) + curRange * 2;
            this.lines.splice(offset, this.lines.length - offset);

            if (this.lines[0] !== curLine + 1 && 0 === curRange) {
                this.lines.splice(curLine + 1, this.lines[0] - curLine);
            } else if (this.lines[0] !== curLine + 1 && 0 !== curRange) {
                this.lines.splice(curLine + 2, this.lines[0] - curLine - 1);
                this.lines[0] = curLine + 1;
            }
        }

        if (0 === curRange) {
            if (this.lines[0] !== curLine + 1) {
                // 添加有关新行的信息，首先是它的相对位移，然后更改行数本身
                const offsetValue = this.lines.length - curLine - 1;
                this.lines.splice(curLine + 1, 0, offsetValue);
                this.lines[0] = curLine + 1;
            }
        }

        const rangeOffset = 1 + this.lines[0] + this.lines[curLine + 1] + curRange * 2;

        // 插入curRange的startPos，endPos
        this.lines[rangeOffset + 0] = 0;
        this.lines[rangeOffset + 1] = 0;

        if (0 !== curLine || 0 !== curRange) {
            return this.lines[rangeOffset - 1];
        } else {
            return 0;
        }
    }

    /**
     * 获取portion在curLine中内容开始的索引
     * @param curLine
     * @param curRange
     */
    public getRangeStartPos(curLine: number, curRange: number = 0): number {
        return this.lines[this.getRangeOffset(curLine, curRange)];
    }

    /**
     * 获取portion在curLine中内容结束的索引
     * @param curLine
     * @param curRange
     */
    public getRangeEndPos(curLine: number, curRange: number = 0): number {
        return this.lines[this.getRangeOffset(curLine, curRange) + 1];
    }

    /**
     * 记录portion在curLine中内容的开始索引和结束索引
     * @param curLine
     * @param curRange
     * @param startPortionContentPos
     * @param endPortionContentPos
     */
    public fillRange(curLine: number, curRange: number = 0,
                     startPortionContentPos: number, endPortionContentPos: number): void {
        const rangeOffset = this.getRangeOffset(curLine, curRange);
        this.lines[rangeOffset + 0] = startPortionContentPos;
        this.lines[rangeOffset + 1] = endPortionContentPos;
    }

    /**
     * 设置当前行的range结束所在portion中的位置
     * @param curLine
     * @param curRange
     * @param curPortionPos
     */
    public fillRangeEndPos(curLine: number = 0, curRange: number = 0, curPortionContentPos: number = 0): void {
        const rangeOffset = this.getRangeOffset(curLine, curRange);
        this.lines[rangeOffset + 1] = curPortionContentPos;
    }

    /**
     * 获取portion内容所在的行数
     */
    public getLinesCount(): number {
        return this.lines[0];
    }

    /**
     * 获取portion当前行的range数量
     */
    public getRangesCount( curLine: number ): number {
        // 是否是最后一行
        if ( curLine === this.lines[0] - 1 ) {
            return ( this.lines.length - this.lines[1 + curLine] - ( this.lines[0] + 1) ) / 2;
        } else {
            return ( this.lines[1 + curLine + 1] - this.lines[1 + curLine]) / 2;
        }
    }

    private getRangeOffset(curLine: number, curRange: number = 0): number {
        return (1 + this.lines[0] + this.lines[1 + curLine] + curRange * 2);
    }
}
