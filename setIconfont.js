const fs = require('fs');
var cssPath = './src/components/editor/style/iconfont/iconfont.css';
fs.readFile(cssPath, function(err, content) {
    if (err || !content) {
        console.log('open error!');
        return;
    }
    var text = content.toString();
    text = text.replace(/url\(\'iconfont/g, 'url(\'\.\/iconfont');
    text = text.replace(/\.(?!iconfont)\w+?\:/g, function (m) {
        return '.iconfont' + m;
    });
    fs.writeFile(cssPath, text, function(err) {
        if (err) {
            return;
        }
        console.log('Saved success!');
    });
    console.log('open success!');
})
