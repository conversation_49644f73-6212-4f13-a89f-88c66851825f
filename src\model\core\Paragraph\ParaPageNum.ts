import { ParaElementBase } from './ParaElementBase';
import { ParaElementType } from './ParagraphContent';
import TextProperty from '../TextProperty';
import { measure } from '../util';
import ParaPortion from './ParaPortion';
import Document, { DocCurPosType } from '../Document';
import { ResultType, PageNumType, IPageNumProperty } from '../../../common/commonDefines';
import { DocumentContent } from '../DocumentContent';
import HeaderFooter from '../HeaderFooter';

export default class ParaPageNum extends ParaElementBase {
  public logicDocument: Document;
  private numWidths: number[];
  // private widths: number[];
  private parent: ParaPortion;
  private string: string; // real representation of pagenum
  private pageIndex: number; // current page, accurate?
  private widthTemp: number;
  // for ui dialog
  private pageNumType: PageNumType;
  private pageNumString: string; // just ui representation, no real semantic meaning
  private startIndex: number; // 起始页码

  constructor(logicDocument?: Document, pageNum?: number) {
    super();
    this.content = ' ';
    this.type = ParaElementType.ParaPageNum;
    this.numWidths = [];
    // this.widths = [];
    this.parent = null;
    this.string = ' ';
    this.pageIndex = -1;
    this.startIndex = 1;
    this.widthTemp = 0;
    this.pageNumType = null;
    this.pageNumString = '第[页码]页/总[页数]页';

    if (logicDocument != null) {
      this.logicDocument = logicDocument;
    }
    if (pageNum != null) {
      this.setPage(pageNum);
    }
  }

  public copy(bForUI: boolean = false): ParaPageNum {
    const newPageNum = new ParaPageNum();
    newPageNum.positionX = this.positionX;
    newPageNum.positionY = this.positionY;
    newPageNum.parent = this.parent;
    newPageNum.logicDocument = this.logicDocument;
    newPageNum.bViewSecret = this.bViewSecret;
    // console.trace()
    if ( true === bForUI) {
      newPageNum.type = this.type;
      newPageNum.content = this.content;
      newPageNum.string = this.string;
    }

    newPageNum.width = this.width;
    newPageNum.widthVisible = this.widthVisible;
    newPageNum.widthTemp = this.widthTemp;
    newPageNum.pageIndex = this.pageIndex;
    newPageNum.numWidths = this.numWidths.concat();
    // ui triple
    newPageNum.startIndex = this.startIndex;
    newPageNum.pageNumType = this.pageNumType;
    newPageNum.pageNumString = this.pageNumString;

    return newPageNum;
  }

  public measure(textPr: TextProperty, text?: string): number {
    // console.log(textPr)
    if (!text && ' ' === this.string) {
      this.string = this.getRenderedText(true, 1);
    }

    const testText = text != null ? text : this.string;
    const ms = measure(testText, textPr);

    let width = 0;
    for (let i = 0, len = ms.length; i < len; i++) {
      // console.log(ms[i].width)
      width += ms[i].width;
    }

    // this.width = width;
    // this.widthVisible = this.width;
    // this.width = m.width;
    // this.widthVisible = this.width;

    // measure() output and input are restrained; may have a better way
    this.widthTemp = width + 3;

    // console.trace()

    return ms[0].height;
  }

  // public measureCurrentPageNum(textPr: TextProperty, renderedText?: string): void {
  //   this.measure(textPr, renderedText);
  //   this.setWidth(this.widthTemp);
  // }

  /**
   * the method connect with pagenum modal
   */
  public setPage(pageNum: number, textPr?: TextProperty): number {
    const totalPages = this.getTotalPages();
    this.pageIndex = pageNum;
    const renderedPageIndex = this.pageIndex + this.startIndex - 1;
    // console.log(renderedPageIndex)
    this.string = '第' + renderedPageIndex + '页/总' + totalPages + '页'; // default

    if (this.pageNumType != null) {
      if (this.pageNumType === PageNumType.CurPage) {
        this.string = '' + renderedPageIndex;
        this.pageNumString = this.string;
      } else if (this.pageNumType === PageNumType.TotalPages) {
        this.string = '' + totalPages;
        this.pageNumString = this.string;
      } else if (this.pageNumType === PageNumType.PageNumString) {
        this.string = this.replacePageNumStringPlaceHolder(renderedPageIndex, totalPages);
        // console.log(this.string)
      }
    }

    if (textPr != null) {
      if (this.logicDocument != null) {
        if (this.logicDocument.getDocPosType() === DocCurPosType.HdrFtr) {
          // in headerfooter

          // in headerfooter, calculate and store every pagenum string width of each page
          for (let i = 1; i <= totalPages; i++) {
            if (i !== this.pageIndex) { // not current page. need to compose 'this.string' equivalent to calculate width
              const rPageIndex = i + this.startIndex - 1;
              // const renderedText = '第' + rPageIndex + '页/总' + totalPages + '页';
              const renderedText = this.replacePageNumStringPlaceHolder(rPageIndex, totalPages);
              //   console.log(renderedText)
              this.measure(textPr, renderedText);
            } else {
              // current page. just defaultly use 'this.string' to calculate width
              this.measure(textPr);
              this.setWidth(this.widthTemp);
            }
            // store diff page width. pay attention -1
            this.numWidths[i - 1] = this.widthTemp;

          }
        } else {
          // in main doc
          this.measure(textPr); // use this.string to measure
          this.setWidth(this.widthTemp);
        }
      } else {
        return ResultType.Failure;
      }

    }

    // let realWidth = 0;
    // for (let i = 0; i < len; i++) {
    //   const char = parseInt(this.string.charAt(i), 10);
    //   this.widths[i] = this.numWidths[char];
    //   realWidth += this.numWidths[char];
    // }

    // this.width = realWidth;
    // this.widthVisible = realWidth;
    return ResultType.Success;
  }

  public setPageNumFromReader(pageNum: number, textPr: TextProperty, totalPages: number,
                              headerFooter: HeaderFooter): void {
    this.pageIndex = pageNum;
    const renderedPageIndex = this.pageIndex + this.startIndex - 1;
    // console.log(renderedPageIndex)
    this.string = '第' + renderedPageIndex + '页/总' + totalPages + '页'; // default

    if (this.pageNumType != null) {
      if (this.pageNumType === PageNumType.CurPage) {
        this.string = '' + renderedPageIndex;
        this.pageNumString = this.string;
      } else if (this.pageNumType === PageNumType.TotalPages) {
        this.string = '' + totalPages;
        this.pageNumString = this.string;
      } else if (this.pageNumType === PageNumType.PageNumString) {
        this.string = this.replacePageNumStringPlaceHolder(renderedPageIndex, totalPages);
        // console.log(this.string)
      }
    }

    if (headerFooter == null) {
      // in main doc
      this.measure(textPr); // use this.string to measure
      this.setWidth(this.widthTemp);
    } else {
      // in headerfooter, calculate and store every pagenum string width of each page
      // this should only full iterate once
      for (let i = 1; i <= totalPages; i++) {
        // console.log(this.numWidths[i - 1])
        if (i !== this.pageIndex) { // not current page. need to compose 'this.string' equivalent to calculate width
          const rPageIndex = i + this.startIndex - 1;
          // const renderedText = '第' + rPageIndex + '页/总' + totalPages + '页';
          const renderedText = this.replacePageNumStringPlaceHolder(rPageIndex, totalPages);
          //   console.log(renderedText)
          this.measure(textPr, renderedText);
        } else {
          // current page. just defaultly use 'this.string' to calculate width
          this.measure(textPr);
          // this.setWidth(this.widthTemp);
        }
        // store diff page width. pay attention -1
        this.numWidths[i - 1] = this.widthTemp;
        // for now set everytime(bigger width will overlap smaller)
        this.setWidth(this.widthTemp)
      }
    }

  }

  public updatePageNumWidths(textPr: TextProperty, totalPages: number,
    inHeaderFooter?: boolean): void {
    // inHeaderFooter not in use yet
    // console.log(textPr, totalPages, inHeaderFooter, this.numWidths.length)
    // console.log(this)

    // only update at last page to reduce redundancy
    if (totalPages < this.numWidths.length) {
      // return;
    }
    // console.log('not returned')
    // if (inHeaderFooter === false) {
    //   // in main doc
    //   this.measure(textPr); // use this.string to measure
    //   this.setWidth(this.widthTemp);
    // } else {
    //   // in headerfooter, calculate and store every pagenum string width of each page
    //   // this should only full iterate once
    for (let i = 1; i <= totalPages; i++) {
      // console.log(this.numWidths[i - 1])
      if (i !== this.pageIndex) { // not current page. need to compose 'this.string' equivalent to calculate width
        const rPageIndex = i + this.startIndex - 1;
        // const renderedText = '第' + rPageIndex + '页/总' + totalPages + '页';
        const renderedText = this.replacePageNumStringPlaceHolder(rPageIndex, totalPages);
        //   console.log(renderedText)
        this.measure(textPr, renderedText);
      } else {
        // current page. just defaultly use 'this.string' to calculate width
        this.measure(textPr);
        // this.setWidth(this.widthTemp);
      }
      // store diff page width. pay attention -1
      this.numWidths[i - 1] = this.widthTemp;
      // for now set everytime(bigger width will overlap smaller)
      this.setWidth(this.widthTemp)
    }
    // }
  }

  // RecalculateObject?

  // not seem to be a usable method
  // public getPageNumValue(renderedText?: string): number {
  //   // TODO

  //   // total pages case
  //   if (this.pageNumType === PageNumType.TotalPages) {
  //     return this.pageIndex;
  //   }

  //   let testText = this.string;
  //   if (renderedText != null) {
  //     testText = renderedText;
  //   }
  //   const sepIndex = testText.indexOf('页');
  //   let rawString = "";
  //   if (sepIndex === -1) {
  //     rawString = testText;
  //   } else {
  //     rawString = testText.slice(1, sepIndex);
  //   }

  //   let nPageNum = parseInt(rawString, 10);
  //   if (isNaN(nPageNum)) {
  //     return 1;
  //   }

  //   // startindex
  //   if (this.startIndex !== 1) {
  //     const dist = this.startIndex - 1;
  //     nPageNum -= dist;
  //   }

  //   return nPageNum;
  // }

  public getType(): ParaElementType {
    return this.type;
  }

  public getString(): string {
    return this.string;
  }

  public setParent(oParent: any): void {
    this.parent = oParent;
  }

  public getParent(): ParaPortion {
    return this.parent;
  }

  public getTotalPages(): number {
    if (this.logicDocument != null) {
      return this.logicDocument.getPages().length;
    } else if ( this.parent && this.parent.paragraph && this.parent.paragraph.parent ) {
      const documentContent = this.parent.paragraph.parent;
      if (documentContent.isHeaderFooter(false) === true) {
        return (documentContent as DocumentContent).logicDocument.getPages().length;
      } else {
        return documentContent.getPages().length;
      }
    }

    return -1;
  }

  public getCurPage(): number {
    return this.pageIndex;
  }

  public setLogicDocument(logicDocument: Document): void {
    if (logicDocument != null) {
      this.logicDocument = logicDocument;
    }
  }

  public getLogicDocument(): Document {
    return this.logicDocument;
  }

  /**
   * return rendered text dynamically
   * @param bHeaderFooter /
   * @param realPage pageIndex from React-virtualized, not document.curPage
   */
  public getRenderedText(bHeaderFooter: boolean, realPage: number): string {
    let renderedText = this.string;

    const totalPages = this.getTotalPages();
    let realPageWStartIndex = realPage;

    // console.log(totalPages)
    if (realPage != null) {

      // considering startindex
      if (this.startIndex !== 1) {
        realPageWStartIndex += this.startIndex - 1;
      }
      switch (this.pageNumType) {
        case PageNumType.PageNumString: {
          // renderedText = '第' + realPageWStartIndex + '页/总' + totalPages + '页'; // default?
          // shouldn't touch this.string?
          renderedText = this.replacePageNumStringPlaceHolder(realPageWStartIndex, totalPages);
          // console.log(renderedText)
          break;
        }
        case PageNumType.CurPage: {
          renderedText = '' + realPageWStartIndex;
          break;
        }
        case PageNumType.TotalPages: {
          renderedText = '' + totalPages;
          break;
        }
        default: {
          break;
        }
      }

    }

    return renderedText;
  }

  public getNumWidths(): number[] {
    return this.numWidths;
  }

  public setPageNumType(pageNumType: PageNumType): void {
    this.pageNumType = pageNumType;
  }

  public getPageNumType(): PageNumType {
    return this.pageNumType;
  }

  public setPageNumString(pageNumString: string): void {
    this.pageNumString = pageNumString;
  }

  public getPageNumString(): string {
    return this.pageNumString;
  }

  public setStartIndex(startIndex: number): void {
    this.startIndex = startIndex;
  }

  public getStartIndex(): number {
    return this.startIndex;
  }

  public setPageNumProperty(pageNumProperty: IPageNumProperty): number {
    if (pageNumProperty == null) {
      return ResultType.Failure;
    }
    const {pageNumType, pageNumString, startIndex} = pageNumProperty;
    this.startIndex = startIndex;
    this.pageNumString = pageNumString;
    this.pageNumType = pageNumType;
  }

  public isParaPageNum(): boolean {
    return true;
  }

  private setWidth(width: number): void {
    this.width = width;
    this.widthVisible = this.width;
  }

  private replacePageNumStringPlaceHolder(pageIndex: number, totalPages: number): string {
    let text = this.pageNumString;
    const pageNumChars = '[页码]';
    const totalPageChars = '[页数]';
    text = text.replace(pageNumChars, pageIndex + '');
    text = text.replace(totalPageChars, totalPages + '');
    return text;
  }

}
