import * as React from 'react';
import { IDocumentParaLine } from '../../model/ParaLineProperty';
import ParaPortion from './ParaPortion';

export default class ParaLine extends React.Component<IDocumentParaLine, {}> {
  constructor(props) {
    super(props);
    // this.state = {}
    // console.log('ParaLine---------------constructor----------------')
  }

  renderPortion() {
    const { content } = this.props;
    const curLine = this.props.id;

    return content.map((item) => {
        if ( curLine >= item.startLine && item.startLine + item.getLinesCount() > curLine ) {
            const startPos = item.getRangeStartPos(curLine - item.startLine);
            const endPos = item.getRangeEndPos(curLine - item.startLine);
            return <ParaPortion key={item.id} id={item.id} content={item.content} type={item.type} textProperty={item.textProperty} 
            positionX={item.positionX} positionY={item.positionY} startPos={startPos} endPos={endPos}/>;
        }
    });
  }

  render() {
    // console.log('ParaLine---------------render----------------')
    const { id, index } = this.props;
    // return <tspan key={id} line-key={index}>{this.renderPortion()}</tspan>;
    return <g>{this.renderPortion()}</g>;
  }

  shouldComponentUpdate(nextProps, nextState) {
    const { id, content, startPos, endPos } = this.props;

    // console.log(this, nextProps)
    // if ( content !== nextProps.content )
        // return true;
        // console.log('ParaLine--------shouldComponentUpdate---------------')

    if ( startPos === nextProps.startPos && endPos === nextProps.endPos &&
         content[startPos].getRangeStartPos(id) === nextProps.content[nextProps.startPos].getRangeStartPos(id) &&
         content[startPos].getRangeEndPos(id) === nextProps.content[nextProps.startPos].getRangeEndPos(id) &&
         content[endPos].getRangeStartPos(id) === nextProps.content[nextProps.endPos].getRangeStartPos(id) &&
         content[endPos].getRangeEndPos(id) === nextProps.content[nextProps.endPos].getRangeEndPos(id) ) {
       
          if ( this.props.content.length === nextProps.content.length ) {
            for (let index = 0, count = this.props.content.length; index < count; index++) {
                const element = this.props.content[index];
                if ( element.content.length !== nextProps.content[index].content.length
                  || element.getTextContent() !== nextProps.content[index].getTextContent() ) {
                    return true;
                }
            }
          } else {
            return false;
          }
    }

    // console.log(this, nextProps)
    // console.log('llllllllllllllllllllllllllllllllllllllllllllllllllll')
    return true;
  }

  // static getDerivedStateFromProps(nextProps, prevState) {
  //   // if ( content !== nextProps.content )
  //       console.log('ParaLine--------getDerivedStateFromProps---------------')
  //       return null;
  // }

  // forceUpdate() {
  //   console.log('ParaLine--------forceUpdate---------------')
  // }

  // componentDidMount() {
  //   console.log('ParaLine--------componentDidMount---------------')
  // }

  // componentDidUpdate() {
  //   console.log('ParaLine--------componentDidUpdate---------------')
  // }

  // componentWillUnmount() {
  //   console.log('ParaLine--------componentWillUnmount---------------')
  // }

  // componentDidCatch() {
  //   console.log('ParaLine--------componentDidCatch---------------')
  // }

}
