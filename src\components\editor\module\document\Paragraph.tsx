import { INISCellGridLine, numtoFixed } from '@/common/commonDefines';
import * as React from 'react';
import { IDocumentParagraph } from '../../../../model/ParaProperty';
import ParaLine from './ParaLine';

export default class Paragraph extends React.Component<IDocumentParagraph, {}> {
    // private _height: number = 23.56;
    constructor(props: IDocumentParagraph) {
        super(props);
        // console.log('Paragraph---------------constructor----------------')
    }

    public render(): any {
        // const { index } = this.props;
        return (
            // <tspan para-id={index}>
            //   {this.renderLines()}
            // </tspan>
            this.renderLines()
        );
    }

    private renderLines(): any {
        const { content, lines, startLine, endLine, cellId, option } = this.props;
        if (1 === content.length && content[0].isEmpty(false)) {
            return this.renderCellGirdLines(lines[0], option, true);
        }

        return lines.map((item, index) => {
            if (startLine <= index && index <= endLine) {
                return (
                    <React.Fragment key={'line-' + item.id}>
                        {this.renderCellGirdLines(item, option, index === endLine)}
                        <ParaLine
                            key={item.id}
                            index={item.id}
                            id={index}
                            cellId={cellId}
                            content={content}
                            startPos={item.getStartPos()}
                            endPos={item.getEndPos()}
                            pageIndex={this.props.pageIndex}
                            documentCore={this.props.documentCore}
                            option={option}
                            nHeaderFooter={this.props.nHeaderFooter}
                        />
                    </React.Fragment>
                );
            }
            /// index++;
        });
    }

    private renderCellGirdLines(item: any, option: any, lastLine: boolean): any {
        if (!option) {
            return null;
        }

        const line: INISCellGridLine = option.gridLine;
        if (!line || !line.visible) {
            return null;
        }

        const {x, cellBoundX, cellBoundY, bLast} = option;
        const x1 = numtoFixed(x);
        const x2 = numtoFixed(cellBoundX);
        const y3 = numtoFixed(item.bottom);
        if (!bLast || !lastLine ) {
            return (<line className='nisgrid' key={'cellgridline-' + item.id} x1={x1} y1={y3} style={{ stroke: line.color || '#ACB4C1', strokeWidth: (line.borderHeight || 1) + 'px' }}
            x2={x2} y2={y3} />);
        }
        
        const arrs = [];
        const height = item.bottom - item.top;
        const style = { stroke: line.color || '#ACB4C1', strokeWidth: (line.borderHeight || 1) + 'px' };
        for (let y1 = item.bottom; y1 < cellBoundY; y1 = y1 + height) {
            if (y1 + height > cellBoundY) {
                break;
            }
            const y2 = numtoFixed(y1);

            arrs.push((<line className='nisgrid' key={y1 + 'cellgridline-' + item.id} x1={x1} y1={y2} style={style} x2={x2} y2={y2} />));

        }

        return arrs;
    }
}
