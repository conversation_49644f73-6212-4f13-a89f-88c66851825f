import {ParaElementType} from './Paragraph/ParagraphContent';
import { PageFormat, PAGE_FORMAT, FONT_MAPPING, isMacOs } from '../../common/commonDefines';
import { fonts } from '../../components/editor/text/font';
import { IFRAME_MANAGER } from '../../common/IframeManager';
import { WasmInstance } from '../../common/WasmInstance';
import { CACHES } from './TextMeasureSize';
import { skipEscapeString } from '../../common/commonMethods';
/* IFTRUE_WATER */
import { customFontCache, CustomFontReverseMap } from './util-custom-font';
/* FITRUE_WATER */

let ppi;
// let measureSVGContainer;
// let textElement;
// let textNode;
/**
 * 获取当前屏幕ppi
 */
export function getCurPPI(): number {
    if (ppi === undefined) {
        const div = document.createElement('div');
        div.style.width = '1in';
        const body = document.getElementsByTagName('body')[0];
        body.appendChild(div);
        const curPPI: any = document.defaultView.getComputedStyle(div, null)
                                    .getPropertyValue('width');
        body.removeChild(div);
        return ppi = parseFloat(curPPI);
    } else {
        return ppi;
    }
}

/**
 * MM毫米转换Px像素
 * @param mValue
 */
export function getPxForMM(mValue: number): number {
  // key-wasm by tinyzhi
   //return mValue * getCurPPI() / 25.4;
  const temp = getCurPPI();
  return WasmInstance.instance._Util_getPM(mValue, temp);
  // end by tinyzhi
}

/**
 * Px像素转换MM毫米
 * @param pxValue
 */
export function getMMFromPx(pxValue: number): number {
  // key-wasm by tinyzhi
   //return pxValue * 25.4 / getCurPPI();
  const temp = getCurPPI();
  return WasmInstance.instance._Util_getMP(pxValue, temp);
  // end by tinyzhi
}

/**
 * 接口：测量字符的高宽，类型
 */
export interface IRect {
    width: number;
    height: number;
    type?: ParaElementType;
}

// 字符测量缓存
// const CACHES = new Map<string/*字体及字号*/, Map<string/*字符*/, IRect>>();

export function initCaches(): void {
    // const ziti = ['STSong', 'stHeiti', 'FangSong_GB2312', 'STKaiti', 'Hiragino Sans GB', 'Times New Roman'];
    const ziti = ['STHeiti', 'STSong', 'FangSong_GB2312', 'STKaiti', 'Hiragino Sans GB', 'Times New Roman', '宋体'];
    // 初号58.7 小初(36号)48 一号(26号)34.7 小一(24号)32 二号(22号)29.3 小二(18号)24 三号(16号)21.3 小三20 四号(14号)18.7 小四(12号)16 五号14 小五12
    // const fontSize = ['58.7', '48', '34.7', '32', '29.3', '24', '21.3', '20', '18.7', '16', '14', '12'];
    // 10号13.3 小五12(9号: 12) 8号10.7 11号14.7 12号16 14号18.7 16号21.3 18号24 20号26.7 22号 24号 26号 28号 36号
    const fontSize = ['10.7', '12', '13.3', '14', '15', '14.7', '16', '18.7', '19', '20', '21.3',
                       '24', '26.7', '29.3', '32', '34.7', '37.3', '48', '58.7'];
    // const text = '{}☑☐⊙⭘◯：:℃'; // String.fromCharCode(0x21B5);
    const text = '我！？《》；，、。（）：【】“”‘’ 0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ,./?":<>=~`!@#$%^&*()_;./-+{}[]\\|\'☑☐⊙⭘◯℃' + String.fromCharCode(0x21B5);
    for (let index1 = 0; index1 < ziti.length; index1++) {
        const zi = ziti[index1];
        for (let index2 = 0; index2 < fontSize.length; index2++) {
            const size = fontSize[index2];
            const key = zi + size;
            measure(text, {
                fontSize: parseFloat(size),
                fontFamily: zi,
            });
        }
    }

    let s = '';
    CACHES.forEach((value, key) => {
        // console.info(key + ': ');
        s += 'CACHES.set(\'' + key + '\', new Map([ ';
        value.forEach((value1, key1) => {
            if (String.fromCharCode(0x21B5) === key1) {
                value1.width = 0;
                key1 = 'paraEnd';
            } else if ('\\' === key1) {
                key1 = '\\\\';
            } else if ('\'' === key1) {
                key1 = '\\\'';
            } else if ( ' ' === key1) {
                // value1.width =
            } else if ('a' === key1) {
                // console.info(key1, value1.width, value1.height);
                // return;
            }

            s += ('[\'' + key1 + '\', {width: ' + value1.width + ', height: '
                    + value1.height + ', type: ' + value1.type + '}],');
        });

        s += ']) );      ';
    });

    console.log(s)
}

// window['initCaches'] = function getFontCaches(): any {
//     // CACHES.clear();
//     // const ziti = ['STSong', 'stHeiti', 'FangSong_GB2312', 'STKaiti', 'Hiragino Sans GB', 'Times New Roman'];
//     const ziti = ['宋体', 'Times New Roman'];//, 'Times New Roman'];
//     // 初号58.7 小初(36号)48 一号(26号)34.7 小一(24号)32 二号(22号)29.3 小二(18号)24 三号(16号)21.3 小三20 四号(14号)18.7 小四(12号)16 五号14 小五12
//     // const fontSize = ['58.7', '48', '34.7', '32', '29.3', '24', '21.3', '20', '18.7', '16', '14', '12'];
//     // 10号13.3 小五12(9号: 12) 8号10.7 11号14.7 12号16 14号18.7 16号21.3 18号24 20号26.7 22号 24号 26号 28号 36号
//     const fontSize = ['18'];
//     // const text = '{}☑☐⊙⭘◯：:℃'; // String.fromCharCode(0x21B5);
//     const text =  '我 0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ,.;/?()[]=-+*<>，。；’“？、（）【】ⅠⅡ:：{}☑☐⊙⭘◯℃' + String.fromCharCode(0x21B5);
//     const myfonts = new Map();
//     let s = '';
//     for (let index1 = 0; index1 < ziti.length; index1++) {
//         const zi = ziti[index1];
//         let curMap = '';
//         for (let index2 = 0; index2 < fontSize.length; index2++) {
//             const size = fontSize[index2];
//             const key = zi + size;
//             const arrs = [];
//             let newKey = size + '';
//             if ('Times New Roman' === zi) {
//                 newKey = zi + newKey;
//             }
//             let arrsText = '';
//             for (let index3 =0, len = text.length; index3 < len; index3++) {
//                 const char = text.charAt(index3);
//                 const fonts = measure(char, {
//                     fontSize: parseFloat(size),
//                     fontFamily: zi,
//                 });
//                 // arrs.push([char, fonts[0]]);
//                 const {width, height, type} = fonts[0];
//                 arrsText += `, ['${char}', {width: ${width}, height: ${height}, type: ${type}}]`;
//             }
//             curMap += `CACHES.set('${newKey}', new Map([${arrsText.slice(2)}]))`;
//             // myfonts.set(newKey, arrs);
//         }
//         s += curMap + `;
//         `;
//     }
//     console.log(s)
    
    
//     return s;
// }

/**
 * 预先测量大量字符，用于大段文本初始化
 * @param {string} preparedChars
 * @param {Font} font
 */
function prepare(preparedChars: string, font: {fontSize: number, fontFamily: string}): void {
    // 去重字符
    let chars = [...new Set(preparedChars.split(''))];
    const key = getCacheKey(font);
    // 去除cache中存在的字符
    const cachedChars = CACHES.get(key);

    if (cachedChars) {
        const filteredChars = [];
        for (const chr of chars) {
            if (!cachedChars.get(chr)) {
                filteredChars.push(chr);
            }
        }
        chars = filteredChars;
    }
    if (chars.length === 0) {
        return;
    }
    const charsString = chars.join('');
    updateCache(charsString, font);
}

function getCacheKey(font: {fontSize: number, fontFamily?: string}): string {
    let tempFontFamily = font.fontFamily;
    if (isMacOs) {
        const fontsToBeTransferred = ['宋体', '黑体', '仿宋', '楷体', '幼圆'];
        if (fontsToBeTransferred.includes(tempFontFamily) === true) {
            tempFontFamily = FONT_MAPPING[tempFontFamily].mac;
            // console.log(tempFontFamily)
        }
    }
    const fontsArr = ['Times New Roman', 'STSong', 'STHeiti', 'FangSong_GB2312', 'STKaiti', 'Hiragino Sans GB'];
    /* IFTRUE_WATER */
    if (customFontCache) {
        for (const [key, value] of customFontCache) {
            fontsArr.push(value.family);
        }
    }
    /* FITRUE_WATER */
    // const fontsArr = ['Times New Roman'];
    // const text = (fontsArr.includes(font.fontFamily) === false) ?
    //         font.fontSize.toString() : font.fontFamily + font.fontSize;
    const text = (fontsArr.includes(tempFontFamily) === false) ?
            font.fontSize.toString() : tempFontFamily + font.fontSize;
    // console.log(text)
    return text;
}

/**
 * 测量指定字符的宽度
 * @param {string} chars
 * @param {Font} font
 * @returns {Rect[]}
 */
export function measure(chars: string, font: {fontSize: number, fontFamily: string}): IRect[] {
    if (!chars) return [];// 防止 chars 为空 by tinyzhi
    const result: IRect[] = [];
    let unCachedChars = '';
    const key = getCacheKey(font);
    let cache = CACHES.get(key);
    if (!cache) {
        cache = new Map();
        CACHES.set(key, cache);
    }

    /* IFTRUE_WATER */
    if (CustomFontReverseMap.has(font.fontFamily)) {
        const realFontName = CustomFontReverseMap.get(font.fontFamily);
        const customCache = customFontCache.get(realFontName).cache;
        for (let i = 0; i < chars.length; i++) {
            const char = chars[i];
            const cusRect = customCache[char];
            if (cusRect) {
                result[i] = {
                    width: cusRect.width * font.fontSize,
                    height: cusRect.height * font.fontSize,
                };
            } else if (cache.has(char)) {
                result[i] = cache.get(char);
            } else {
                unCachedChars += char;
            }
        }
        if (unCachedChars) {
            prepare(unCachedChars, font);
        }

        let unCacheIndex = 0;
        for (let i = 0; i < chars.length; i++) {
            if (!result[i]) {
                result[i] = cache.get(unCachedChars[unCacheIndex++]);
            }
        }

        return result;
    }
    /* FITRUE_WATER */

    for (let i = 0; i < chars.length; i++) {
        if (!cache.has(chars.charAt(i))) {
            unCachedChars += chars.charAt(i);
        }
    }

    if (unCachedChars) {
        prepare(unCachedChars, font);
    }

    for (let i = 0; i < chars.length; i++) {
        result.push(cache.get(chars.charAt(i)));
    }
    return result;
}

/**
 * 更新测量缓存
 * @param {string} chars
 * @param {Font} font
 */
function updateCache(chars: string, font: {fontSize: number, fontFamily: string}): void {
    const key = getCacheKey(font);
    let cache = CACHES.get(key);
    if (!cache) {
        cache = new Map();
        CACHES.set(key, cache);
    }

    measureChar(chars, font);
}

/**
 * 使用SVG的方式测量字符高宽
 * @param chars
 * @param font
 * @param isSym
 */
// export function measureChar(chars: string, font: {fontSize: number, fontFamily: string}): any {
export function measureChar(chars: string, font: {fontSize: number, fontFamily: string}): void {
    // let t1 = performance.now();
    const key = getCacheKey(font);
    const cache = CACHES.get(key);
    const textElement = IFRAME_MANAGER.getTextElement();
    const textNode = textElement.firstChild;
    // if (measureSVGContainer === undefined) {
    //     measureSVGContainer = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
    //     measureSVGContainer.id = 'svg-measure-container';
    //     measureSVGContainer.setAttribute('style', 'position: absolute; width: 0; height: 0');
    //     textElement = document.createElementNS('http://www.w3.org/2000/svg', 'text');
    //     textNode = document.createTextNode(chars.replace(/[ ]/g, '.'));
    //     textElement.appendChild(textNode);
    //     measureSVGContainer.appendChild(textElement);
    //     getEditorDomContainer()
    //     .appendChild(measureSVGContainer);
    // }

    textNode.nodeValue = chars.replace(/[ ]/g, '.');

    // tslint:disable-next-line: newline-per-chained-call

    let fontFamily = font.fontFamily;
    if (isMacOs) {
        // change render font family if mac
        const fontOptions = fonts[0].options;
        let index = -1;
        for (let i = 0; i < fontOptions.length; i++) {
            if (fontFamily === fontOptions[i].value) {
                index = i;
                break;
            }
        }
        if (index !== -1) {
            fontFamily = (fontOptions[index] as any).macValue;
        }
    }

    // textElement.style.fontFamily = font.fontFamily;
    textElement.style.fontFamily = fontFamily;
    textElement.style.fontSize = font.fontSize + 'px';
    (window as any).originCache = cache;

    // const temp = [];
    for (let i = 0; i < chars.length; i++) {
        const result = catchUnkownChar(textElement, i, chars);
        cache.set(chars.charAt(i), {width: result.width,
                height: result.height, type: result.type});

        // temp.push(chars.charAt(i), {width: parseFloat(result.width.toFixed(2)),
        //     height: result.height, type: ParaElementType.ParaText})
        // temp.push(parseFloat(result.width.toFixed(2)), result.height, ParaElementType.ParaText);
    }
    // document.body.removeChild(measureSVGContainer);
    textNode.nodeValue = '';
    // let t2 = performance.now();
    // console.log(t2 - t1)
    // return temp;
}

function catchUnkownChar(textElement: any, pos: number, chars: any): any {
    let result;
    try {
        const res = textElement.getExtentOfChar(pos);
        result = {
            width: parseFloat(res.width.toFixed(2)),
            height: res.height,
            type: ParaElementType.ParaText,
        };
    } catch (error) {
        const logs = IFRAME_MANAGER.getCatchMeasureLog();
        logs.push({
            outerHtml: textElement?.outerHTML,
            pos,
            chars,
            nodeValue: textElement?.firstChild?.nodeValue,
            msg: error.message,
        });
        result = {
            width: 0,
            height: 0,
            type: ParaElementType.Unkown,
        };
    }

    return result;
}

/**
 * id计算
 */
export class IdCounter {
    public userId: number;
    public idCounterLoad: number;

    constructor() {
        this.userId = null;
        this.idCounterLoad = 0;
    }

    public getNewId(): number {
        return ++this.idCounterLoad;
    }
}

/**
 * 图片id计算
 */
export class IdCounterImage {
    public userId: number;
    public idCounterImageLoad: number;

    constructor() {
        this.userId = null;
        this.idCounterImageLoad = 0;
    }

    public getNewId(): number {
        return ++this.idCounterImageLoad;
    }

    public getCurrentId(): number {
        return this.idCounterImageLoad;
    }
}

export let idCounter = new IdCounter();
export let idCounterImage = new IdCounterImage();

export function getAcitveNodeIndex(selection: any, bStart: boolean, bApplyToAll: boolean, contentLen: number): number {
    let activeIndex: number;
    if (bApplyToAll === true) {
        // key-wasm by tinyzhi
        // if (bStart === true) {
        //     activeIndex = 0;
        // } else {
        //     activeIndex = contentLen;
        // }
        activeIndex = WasmInstance.instance._Util_getAcitveNodeIndex(Number(bStart) + 3, contentLen);
        // end by tinyzhi
    } else {
        let startPos = selection.startPos;
        let end = selection.endPos;
        if (startPos > end) {
            const temp = startPos;
            startPos = end;
            end = temp;
        }
        if (bStart === true) {
            activeIndex = startPos;
        } else {
            activeIndex = end;
        }
    }

    return activeIndex < 0 ? 0 : activeIndex;
}

export function setFirstWordToLower(str: string): string {
    return str.replace(/(\w)([\w_]+)/, (a, a1, a2) => {
        return a1.toLowerCase() + a2;
    });
}

// export let idCounter = new IdCounter();

/**
 * 获取页面格式
 */
export function getPageFormatFromSize(width: number, height: number): string {
    let result: string = PageFormat.Custom;
    for (const key of Object.keys(PAGE_FORMAT)) {
        const item = PAGE_FORMAT[key];
        if (width === item[0] && height === item[1]) {
            result = key;
        }
    }
    return result;
}

export function getArrayByfilterChars(sText: string): string[] {
    if (!sText || typeof sText !== 'string') {
        return [];
    }

    sText = sText.replace(/\t|\r/g, '');
    sText = skipEscapeString(sText);

    return sText.split('\n');
}

// tslint:disable-next-line: sy-function-name
export function GUID(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
}
