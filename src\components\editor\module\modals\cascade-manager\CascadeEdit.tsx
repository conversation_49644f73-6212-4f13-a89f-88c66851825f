import React from 'react';
import Input from '../../../ui/Input';
import { CascadeActionType, CascadeTriggerCondition, CodeValueItem, ICascade, NewControlType
} from '../../../../../common/commonDefines';
import Select from '../../../ui/select/Select';
import { message } from '../../../../../common/Message';
import {SearchIcon} from './SearchID';

interface IProp {
    documentCore: any;
    type: number;
    visible: boolean;
    properties: any;
    list?: CodeValueItem[];
    onClick: (e: any, data: any) => void;
}

interface IPropKey {
    key: string;
    value: any;
    disabled?: boolean;
}

export class CascadeEdit extends React.Component<IProp, {}> {
    private cascadeProps: ICascade[];
    private currentProp: ICascade;
    private visible: boolean;
    private eventName: string;
    private actions: IPropKey[];
    private logicTypes: IPropKey[];
    private logicTexts: IPropKey[];
    private activeIndex: number;
    private curInputIndex: number;
    private logicDisabled: boolean;
    private onAddCallback: any;
    private bEmpty: boolean;
    private mustInputDisable: boolean;
    private type: number;
    private logicValues: any[];
    // private logicTypeDisabled: boolean;
    // private logicTextDisabled: boolean;

    constructor(props: any) {
        super(props);
        this.cascadeProps = [];
        this.logicTypes = [];
        this.visible = this.props.visible;
    }

    public render(): any {
        return (
            <div className='newcontrol-cascade cascade-manager'>{this.renderContent()}</div>
        );
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        this.visible = nextProps.visible;
    }

    public validData(): boolean {
        const datas = this.cascadeProps;
        if (datas.length === 0) {
            return true;
        }

        for (let index = datas.length - 1; index >= 0; index--) {
            const data = datas[index];
            const logicControl = this.props.documentCore.getNewControlByName(data.logicText);
            if (
                this.props.type === NewControlType.NumberBox &&
                logicControl &&
                !logicControl.isNumberBox()
            ) {
                message.error(`第${index + 1}行的逻辑值为元素id, 目前只支持数值框间的直接比较`);
                return false;
            }
            if (data.action !== CascadeActionType.SyncText && !data.logicText) {
                message.error(`级联第${index + 1}行的逻辑值为不能为空`);
                return false;
            }
            if (data.action === CascadeActionType.SetText && !data.actionText) {
                message.error(`级联第${index + 1}行的动作值为不能为空`);
                return false;
            }
            if (!data.target) {
                message.error(`级联第${index + 1}行的目标id值为不能`);
                return false;
            }
        }

        return true;
    }


    public open = (props: any, type: number, callback: (data: any) => void, list?: CodeValueItem[]): void => {
        let eventName: string;
        let logicTypes = [];
        this.logicDisabled = false;
        this.onAddCallback = callback;
        this.type = type;
        this.setAction(type);

        const syncNode = this.actions.find((item) => item.value === CascadeActionType.SyncText);

        if (syncNode) {
            syncNode.disabled = false;
        }
        logicTypes.push({key: '等于', value: CascadeTriggerCondition.Equal},
                {key: '不等于', value: CascadeTriggerCondition.Neq});
        switch (type) {
            case NewControlType.CheckBox: {
                eventName = 'CheckedChanged';
                syncNode.disabled = this.logicDisabled = true;
                break;
            }
            case NewControlType.RadioButton: {
                eventName = 'RadioChanged';
                syncNode.disabled = true;
                logicTypes.push({key: '未选中值', value: CascadeTriggerCondition.NoSelect});
                break;
            }
            case NewControlType.MultiRadio: {
                eventName = 'RadioChanged';
                syncNode.disabled = true;
                logicTypes = [];
                logicTypes.push({key: '包含', value: CascadeTriggerCondition.Includes});
                logicTypes.push({key: '不包含', value: CascadeTriggerCondition.UnIncludes});
                logicTypes.push({key: '未选中值', value: CascadeTriggerCondition.NoSelect});
                break;
            }
            case NewControlType.TextBox: {
                eventName = 'TextChanged';
                logicTypes.push({key: '为空', value: CascadeTriggerCondition.Empty});
                logicTypes.push({key: '不为空', value: CascadeTriggerCondition.UnEmpty});
                break;
            }
            case NewControlType.MultiCombox:
            case NewControlType.MultiListBox: {
                logicTypes = [];
                logicTypes.push({key: '包含', value: CascadeTriggerCondition.Includes});
                logicTypes.push({key: '不包含', value: CascadeTriggerCondition.UnIncludes});
            }
            case NewControlType.Combox:
            case NewControlType.ListBox: {
                eventName = 'SelectedChanged';
                logicTypes.push({key: '为空', value: CascadeTriggerCondition.Empty});
                logicTypes.push({key: '不为空', value: CascadeTriggerCondition.UnEmpty});
                break;
            }
            case NewControlType.NumberBox: {
                eventName = 'NumberChanged';
                logicTypes.push({key: '小于', value: CascadeTriggerCondition.Less},
                {key: '小于等于', value: CascadeTriggerCondition.LEQ},
                {key: '大于', value: CascadeTriggerCondition.Larger},
                {key: '大于等于', value: CascadeTriggerCondition.GEQ},
                {key: '范围', value: CascadeTriggerCondition.Range},
                {key: '等于空值', value: CascadeTriggerCondition.Empty});
                break;
            }
            case NewControlType.DateTimeBox: {
                eventName = 'TextChanged';
                logicTypes.push({key: '日期比较', value: CascadeTriggerCondition.DateCompare});
                this.logicTexts = [{key: '小于', value: CascadeTriggerCondition.Less},
                {key: '小于等于', value: CascadeTriggerCondition.LEQ},
                {key: '大于', value: CascadeTriggerCondition.Larger},
                {key: '大于等于', value: CascadeTriggerCondition.GEQ},
                {key: '等于', value: CascadeTriggerCondition.Equal}];
                this.logicDisabled = false;
                break;
            }
            default: {
                return;
            }
        }

        this.logicValues = (list || []).map((item) => {
            return {
                key: item.code,
                value: item.code
            }
        });
        this.logicTypes = logicTypes;
        this.eventName = eventName;
        this.props
        this.cascadeProps = props || [];
        this.setState({});
    }

    public close = (): void => {
        this.cascadeProps = [];
    }

    private setAction(type: number): void {
        this.actions = [];
        const checkDisable = type !== NewControlType.CheckBox &&
                             type !== NewControlType.NumberBox &&
                             NewControlType.DateTimeBox !== type;
        this.mustInputDisable = [NewControlType.CheckBox, NewControlType.DateTimeBox,
            NewControlType.TextBox, NewControlType.Combox,
            NewControlType.MultiCombox, NewControlType.ListBox,
            NewControlType.MultiListBox].includes(type);
        if (NewControlType.DateTimeBox !== type) {

            this.actions = [
                {key: '文本', value: CascadeActionType.SetText},
                {key: '同步文本', value: CascadeActionType.SyncText, disabled: true},
                {key: '显示', value: CascadeActionType.Show},
                {key: '隐藏', value: CascadeActionType.Hidden},
                {key: '只读', value: CascadeActionType.UnEdit},
                {key: '可编辑', value: CascadeActionType.Edit},
                {key: '勾选', value: CascadeActionType.Checked, disabled: checkDisable},
                {key: '取消勾选', value: CascadeActionType.Unchecked, disabled: checkDisable},
            ];
        } else {
            this.actions = [];
        }

        if (this.mustInputDisable) {
            this.actions.push({key: '设置必填项', value: CascadeActionType.SetMustItem,
                        disabled: false});
            this.actions.push({key: '取消必填项', value: CascadeActionType.UnsetMustItem,
                        disabled: false});
        }
        if (type === NewControlType.DateTimeBox) {
            this.actions.push({key: '同步文本', value: CascadeActionType.SyncText, disabled: false});
        }
    }

    private searchClick = (index: number, e: any) => {
        if (this.props.onClick) {
            this.props.onClick(e, this.cascadeProps[index]);
        }
    }

    private renderContent(): any {
        if (!this.cascadeProps || this.cascadeProps.length === 0) {
            return (<div className='no-data prop-add' onClick={this.rowClick.bind(this, 0)}>点击添加</div>);
        }
        return (
            <ul>
                <li>
                    <div>触发条件</div>
                    <div>逻辑</div>
                    <div>逻辑值</div>
                    <div>动作</div>
                    <div>动作值</div>
                    <div>目标id</div>
                    <div>操作</div>
                </li>
                {this.renderList()}
            </ul>
        );
    }

    private renderList(): any {
        return this.cascadeProps.map((prop, index) => {
            let ismustLogic = false;
            let tmpActions;
            let action: string;
            let bUnDateCompare: boolean = true;
            const bDate =  this.type === NewControlType.DateTimeBox;
            if (prop.logic !== CascadeTriggerCondition.DateCompare) {
                if (this.type === NewControlType.ListBox && 
                    this.eventName === 'SelectedChanged' && 
                    prop.logic === CascadeTriggerCondition.Equal) {
                    ismustLogic = true;
                } else {
                    ismustLogic = [CascadeTriggerCondition.Empty,
                               CascadeTriggerCondition.UnEmpty,
                               CascadeTriggerCondition.NoSelect].includes(prop.logic);
                }
                tmpActions = this.actions.map((item: IPropKey) => {
                    return Object.assign({}, item);
                });
                if (this.type === NewControlType.ListBox && 
                    this.eventName === 'SelectedChanged' && 
                    prop.logic === CascadeTriggerCondition.Equal) {
                    tmpActions.forEach((item: IPropKey) => {
                        if (item.value === CascadeActionType.SetMustItem ||
                            item.value === CascadeActionType.UnsetMustItem) {
                            item.disabled = false;
                        }
                    });
                } else if (this.type !== NewControlType.DateTimeBox ||
                    (prop.logic !== CascadeTriggerCondition.Equal &&
                    prop.logic !== CascadeTriggerCondition.Neq)) {
                    if (this.mustInputDisable === true) {
                        tmpActions.forEach((item: IPropKey) => {
                            if (item.value === CascadeActionType.SetMustItem ||
                                item.value === CascadeActionType.UnsetMustItem) {
                                item.disabled = !ismustLogic;
                            }
                        });
                    }
                    if (!ismustLogic && (prop.action === CascadeActionType.SetMustItem ||
                        prop.action === CascadeActionType.UnsetMustItem)) {
                        prop.action = tmpActions[0].value;
                    }
                }
                if (bDate) {
                    ismustLogic = true;
                }
            } else {
                tmpActions = [{key: '提示预警', value: CascadeActionType.WarnTip, disabled: true}];
                action = CascadeActionType.WarnTip;
                bUnDateCompare = false;
            }

            return (
                <li
                    key={index}
                    onClick={this.rowClick.bind(this, index)}
                    onMouseEnter={this.mouseEnter.bind(this, index)}
                >
                    <div className='prop-name'>
                        <Input
                            value={this.eventName}
                            name='eventName'
                            disabled={true}
                            onChange={this.onChange.bind(this, index)}
                        />
                    </div>
                    <div>
                        <Select
                            data={this.logicTypes}
                            // disabled={true}
                            value={prop.logic}
                            name='logic'
                            width={135}
                            disabled={prop.action === CascadeActionType.SyncText}
                            onChange={this.typeChange.bind(this, index)}
                        />
                    </div>
                    <div className='prop-name'>
                        {this.rednerLogicText(prop, index, ismustLogic)}
                    </div>
                    <div>
                        <Select
                            data={tmpActions}
                            // disabled={true}
                            value={action || prop.action}
                            width={120}
                            name='action'
                            disabled={!bUnDateCompare}
                            onChange={this.typeChange.bind(this, index)}
                        />
                    </div>
                    <div className='prop-name'>
                        <Input
                            value={prop.actionText}
                            name='actionText'
                            disabled={prop.action !== CascadeActionType.SetText && bUnDateCompare}
                            onChange={this.onChange.bind(this, index)}
                            onBlur={this.onBlur.bind(this, index)}
                            focus={this.onFocus}
                        />
                    </div>
                    <div className='prop-name'>
                        <Input
                            value={prop.target}
                            name='target'
                            onChange={this.onChange.bind(this, index)}
                            onBlur={this.onBlur.bind(this, index)}
                            focus={this.onFocus}
                        />
                        <SearchIcon onClick={this.searchClick.bind(this, index)} />
                    </div>

                    <div className='prop-btns'>
                        <span className='prop-add'>+</span>
                        <label className='prop-delete'>-</label>
                    </div>
                </li>
            );
        });
    }

    private mouseEnter = (index: number, e: any): void => {
        this.currentProp = this.cascadeProps[index];
    }

    private rednerLogicText(prop: ICascade, index: number, ismustLogic: boolean): any {

        if ([NewControlType.ListBox, NewControlType.MultiListBox, NewControlType.Combox, NewControlType.MultiCombox].includes(this.type)) {
            const datas = this.logicValues;
            return (
                <Select
                    data={datas}
                    width={130}
                    bWrite={true}
                    unDeleted={true}
                    value={prop.logicText}
                    name='logicText'
                    disabled={this.logicDisabled || prop.action === CascadeActionType.SyncText || ismustLogic}
                    onChange={this.typeChange.bind(this, index)}
                />
            );
        }
        if (prop.logic !== CascadeTriggerCondition.DateCompare) {
            let numLogicMessage;
            if (
                this.props.type === NewControlType.NumberBox &&
                ![
                    CascadeTriggerCondition.Range,
                    CascadeTriggerCondition.Empty,
                ].includes(prop.logic)
            ) {
                numLogicMessage = (
                    <div className='numberLogicMessage'>
                        允许输入数值框id
                        {/* 允许直接录入另外一个数值框的id, 逻辑变为2个数值框进行比较 */}
                    </div>
                );
            }
            return (
                <>
                    <Input
                        value={prop.logicText}
                        placeholder={prop.logic === CascadeTriggerCondition.Range ? '格式:[a,b]' : undefined}
                        name='logicText'
                        disabled={this.logicDisabled || prop.action === CascadeActionType.SyncText || ismustLogic}
                        onChange={this.onChange.bind(this, index)}
                        onBlur={this.onBlur.bind(this, index)}
                        focus={this.onFocus}
                    />
                    {numLogicMessage}
                </>
            );
        }

        return (
            <Select
                data={this.logicTexts}
                // disabled={true}
                width={121}
                value={prop.logicText}
                name='logicText'
                onChange={this.typeChange.bind(this, index)}
            />
        );
    }

    private rowClick(index: number, e: any): void {
        this.activeIndex = index;
        const target = e.target as HTMLDivElement;
        const className = target.className || '';
        if (this.curInputIndex !== index && className === 'prop-name') {
            this.curInputIndex = index;
        } else if (className.indexOf('prop-add') > -1) {
            this.addData(index);
            this.setState({});
        } else if (className === 'prop-delete') {
            this.deleteData(index);
            this.setState({});
        }
    }

    private typeChange = (index: number, value: any, name: string): void => {
        if (name === 'logic' && this.type === NewControlType.DateTimeBox) {
            const prop = this.cascadeProps[index];
            if (value === CascadeTriggerCondition.DateCompare) {
                prop.action = CascadeActionType.WarnTip;
                prop.logicText = CascadeTriggerCondition.Less;
            } else {
                prop.logicText = '有内容';
                prop.action = CascadeActionType.SetMustItem;
            }
        }
        this.onChange(index, value, name);
        this.setState({});
    }

    private onChange = (index: number, value: any, name: string): void => {
        this.currentProp = this.cascadeProps[index];
        this.currentProp[name] = value;
        if ([CascadeTriggerCondition.Empty,
            CascadeTriggerCondition.UnEmpty,
            CascadeTriggerCondition.NoSelect].includes(value)) {
            this.currentProp.logicText = ' ';
        } else if (value === CascadeTriggerCondition.Range) {
            this.currentProp.logicText = '';
        }
    }

    private addData = (index?: number): void => {
        if (!this.actions.length) {
            return;
        }
        let text = this.logicDisabled ? 'true' : undefined;
        if (NewControlType.DateTimeBox === this.type) {
            text = '有内容';
        }

        const data: ICascade = {
            event: this.eventName as any,
            logic: this.logicTypes[0].value,
            logicText: text,
            action: this.actions[0].value,
            actionText: undefined,
            target: undefined,
        };
        const bEmpty = this.cascadeProps.length === 0;
        if (index === undefined) {
            this.cascadeProps.push(data);
        } else {
            this.cascadeProps.splice(index + 1, 0, data);
        }
        this.currentProp = data;
        // this.bEmpty = bEmpty;
        if (bEmpty) {
            this.onAddCallback(this.cascadeProps, 1);
            this.bEmpty = false;
        }
    }

    private deleteData = (index: number): void => {
        const data = this.cascadeProps[index];
        // delete this._names[data.name];
        this.cascadeProps.splice(index, 1);
        this.currentProp = undefined;
        if (this.cascadeProps.length === 0) {
            this.onAddCallback(null, 2);
            this.bEmpty = false;
        }
    }

    private onBlur = (index: number, name: string, input: any): void => {
    }

    private onFocus = (name: string, e: any): void => {
        // this._activeName = e.target.value;
    }
}
