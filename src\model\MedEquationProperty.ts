import { EquationType } from '../common/commonDefines';

export interface IEquationProperties {
  name: string;
  type: EquationType;
}

export interface ISvgWidthIncreasedProps {
  ordinaryEquation: {
    leftAmount: number;
    rightAmount: number;
  };
  fractionEquation: {
    amount: number;
  };
  menEquation: {
    leftAmount: number;
    middleAmount: number;
    rightAmount: number;
  };
  fractionTextPadding: number;
  ordinaryTextPadding: number;
  menTextPadding: number;
  menSVGPadding: number;
}
