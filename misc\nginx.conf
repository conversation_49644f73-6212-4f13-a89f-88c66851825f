user  nginx;

worker_processes		1;
worker_rlimit_nofile    100000;

error_log  /var/log/nginx/error.log warn;
pid        /var/run/nginx.pid;

events {
	use epoll;
	multi_accept on;
	worker_connections  1024;
}

http {
    server_tokens off;
    include       /etc/nginx/mime.types;
    types {
        application/wasm wasm;
    }
    default_type  application/octet-stream;

    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    access_log  /var/log/nginx/access.log   main;
    error_log   /var/log/nginx/error.log    error;

    sendfile        on;
    #tcp_nopush     on;

    keepalive_timeout  65;

    #gzip  on;

    fastcgi_buffers           8 1024K;
    fastcgi_buffer_size       4K;
    client_max_body_size      2050m;
    client_body_buffer_size   1024k;

    include /etc/nginx/conf.d/app.conf;
}
