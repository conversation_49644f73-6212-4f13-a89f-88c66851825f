import Paragraph from '../../model/core/Paragraph';
import { <PERSON><PERSON><PERSON><PERSON>, IControlObj } from './run';
// tslint:disable-next-line: max-line-length
import { AlignType, ErrorMessages, INSERT_FILE_END_EMPTY_PARA, NewControlType, INewControlProperty, STD_START_DEFAULT, STD_TYPE_DEFAULT, CodeValueItem, ReviewType, REVISION_COLOR, TitlePortionType, rtNode, IModeFonts,
  IParseXmlNode,
  getFontFamilyVal,
  EXTERNAL_STRUCT_TYPE,
  isValidORGBColor,
  FileSource,
  NISTableCellType,
  nisDateFormat,
  NISDateBoxFormat} from '../../common/commonDefines';
import { AlignmentOptions } from '../writer/file/paragraph/formatting/alignment';
import Document from '../../model/core/Document';
// import {parse as circularParse, stringify as circularStringify} from 'flatted/esm';
import { IContentControlDescObj, IInsertFileContent, IUniqueImageProps } from './reader';
import TextProperty from '../../model/core/TextProperty';
import { Region } from '../../model/core/Region';
import DocumentContentElementBase from '../../model/core/DocumentContentElementBase';
import { TableCell } from '../../model/core/Table/TableCell';
import { logger } from '../../common/log/Logger';
import { readCustomProperties } from '../miscellaneous';
import HeaderFooter from '../../model/core/HeaderFooter';
import { NewControlCheck } from '../../model/core/NewControl/NewControlCheck';
import { NewControlRadio } from '../../model/core/NewControl/NewControlRadio';
import ParaPortion from '../../model/core/Paragraph/ParaPortion';
import { ReviewInfo } from '../../model/core/Revision';
import { getDefaultFont, safeDecodeURIComponent } from '../../common/commonMethods';
import { NewControl } from '../../model/core/NewControl/NewControl';
import { readerStructProps } from './commont';
import { ReadStruct } from '@/common/struct/read';
import { StructXmlName } from '@/common/struct/common';
import { Comment } from '@/model/core/Comment/Comment';

export class ParagraphReader {

  /**
   * traverse para
   * @param container Region/Table. DocumentContent is within table cell
   */
  public static traverseParagraph(node: any, document: Document, ContentControlDescObj: IContentControlDescObj,
                                  uniqueImagelist: Map<string, IUniqueImageProps>, bInsertFile: boolean = false,
                                  paras: DocumentContentElementBase[], bNoEndPara: boolean = false,
                                  container: Region | TableCell = null, headerFooter: HeaderFooter = null): void {


    let para = null;
    let skipAddCurPara = false;
    const documentVersion = document != null ? document.getDocumentVersion() : 0;
    if (bInsertFile === false) {
      if (container == null) {
        if (headerFooter == null) {
          para = new Paragraph(document, document);
        } else {
          para = new Paragraph(headerFooter.getContent(), document);
        }
      } else {
        // default
        para = new Paragraph(container.getOperateContent(), document);

        // if it's a region with title, the first para is already created
        const containerContent = container.getContent();
        if (containerContent.length === 1 && container instanceof Region) {
          // this condition should only enter once
          // Attention: if 1st para in region is para with ONLY title portion, the condition would always suffice
          // thus should develop a way to remedy that
          const firstTextNode = node.getElementsByTagName('w:t')[0];
          const firstTextPrNode = node.getElementsByTagName('w:rPr')[0];
          let firstText = '';
          let firstTextPr = null;
          if (firstTextNode != null) {
            firstText = firstTextNode.innerHTML;
          }
          if ( null != firstTextPrNode ) {
            firstTextPr = new TextProperty();
            RunReader.traverseRunProperty(firstTextPrNode, firstTextPr, document);
          }

          // when traversing 2nd para, the first w:t must never be title portion
          // make sure only the 1st para enter this
          if (firstText === container.getTitle()) {
            const thePara = containerContent[0];
            const theParaContent = thePara.getContent();
            // iff case: 1 title portion, 1 end portion
            // tslint:disable-next-line: max-line-length
            if (theParaContent.length === 2 && theParaContent[0] instanceof ParaPortion && theParaContent[1].isParaEndPortion()) {
              const thePortion = theParaContent[0];
              if (thePortion.getTextContent() === container.getTitle()) {
                para = thePara;
                skipAddCurPara = true;
                container.setTitlePortionTextProperty2(firstTextPr);
              }
            }
          }
        }

      }
    } else {
      // headerfooter ignored in insert file
      if (container == null) {
        para = new Paragraph(document, document);
      } else {
        para = new Paragraph(container.getOperateContent(), document);
      }
    }

    const paraChildren = node.children;

    // death stranding
    let {newControlManager} = ContentControlDescObj;
    const {newControlProperty, unclosedNewControls} = ContentControlDescObj;

    // these three vars should be meaningless if cross paragraph
    let bFollowedBySdtEnd = false; // just related to w:r
    let bFirstSiblingOfSdtStart = false; // just related to w:r
    let showPlaceholder = false; // if sdtstart has <showPlaceholder>, locate & save placeholder textprops

    let runCount = 0;
    const domParser = new DOMParser();
    const newControlNamesTempArr = []; // used at inserting file
    for (let i = 0; i < paraChildren.length; i++) {
      const paraChild = paraChildren[i];

      // if (paraChild.nodeName !== '#text') {

      /** w:pPr */
      if (paraChild.nodeName === 'w:pPr') {
        const indent = paraChild.getElementsByTagName('w:ind')[0];
        const spacing = paraChild.getElementsByTagName('w:spacing')[0];
        const alignment = paraChild.getElementsByTagName('w:jc')[0];
        const lastRenderedPageBreak = paraChild.getElementsByTagName('w:lastRenderedPageBreak')[0];
        const wordWrap = paraChild.getElementsByTagName('w:wordWrap')[0];

        /** indentation */
        try {
          if (indent) {
            if (indent.attributes && indent.attributes.length > 0) {
              if (indent.attributes.getNamedItem('w:left')) {
                // filtered at writer, should not need to check if isNaN
                para.paraProperty.paraInd.left =
                  Number(indent.attributes.getNamedItem('w:left').nodeValue);
              }
              if (indent.attributes.getNamedItem('w:firstLine')) {
                para.paraProperty.paraInd.firstLine =
                  Number(indent.attributes.getNamedItem('w:firstLine').nodeValue);
              }
              if (indent.attributes.getNamedItem('w:right')) {
                para.paraProperty.paraInd.right =
                  Number(indent.attributes.getNamedItem('w:right').nodeValue);
              }

            } else {
              throw new Error('w:ind has no attributes');
            }
          }
        } catch (error) {
          // alert(ErrorMessages.XmlError);
          console.warn(ErrorMessages.XmlError);
          window.dispatchEvent(new ErrorEvent('error', {message: ErrorMessages.XmlError, error}));
          const date = new Date();
          logger.error({id: document.id, code: error.stack,
            startTime: date, args: null, name: 'traverseParagraph.indent.attributes'});
        }

        /** line spacing */
        if (spacing) {
          // console.log(spacing)
          try {
            if (spacing.attributes && spacing.attributes.length > 0) {
              if (spacing.attributes.getNamedItem('w:lineType')) {
                para.paraProperty.paraSpacing.lineSpacingType =
                  Number(spacing.attributes.getNamedItem('w:lineType').nodeValue);
              }
              if (spacing.attributes.getNamedItem('w:line')) {
                para.paraProperty.paraSpacing.lineSpacing =
                  Number(spacing.attributes.getNamedItem('w:line').nodeValue);
              }
            } else {
              throw new Error('w:spacing has no attributes');
            }
          } catch (error) {
            // alert(ErrorMessages.XmlError);
            window.dispatchEvent(new ErrorEvent('error', {message: ErrorMessages.XmlError, error}));
            console.warn(ErrorMessages.XmlError);
            const date = new Date();
            logger.error({id: document.id, code: error.stack,
            startTime: date, args: null, name: 'traverseParagraph.spacing.attributes'});

          }
        }

        /** alignment */
        if (alignment) {
          // console.log(alignment);
          try {
            if (alignment.attributes && alignment.attributes.length > 0) {
              if (alignment.attributes.getNamedItem('w:val')) {
                // console.log(alignment.attributes.getNamedItem('w:val').nodeValue)
                let alignmentType = AlignType.Justify; // default to be justified
                switch (alignment.attributes.getNamedItem('w:val').nodeValue) {
                  case AlignmentOptions.BOTH:
                    alignmentType = AlignType.Justify;
                    break;
                  case AlignmentOptions.LEFT:
                    alignmentType = AlignType.Left;
                    break;
                  case AlignmentOptions.RIGHT:
                    alignmentType = AlignType.Right;
                    break;
                  case AlignmentOptions.CENTER:
                    alignmentType = AlignType.Center;
                    break;
                  default:
                    break;
                }

                para.paraProperty.alignment = alignmentType;
              }
            } else {
              throw new Error('w:jc has no attributes');
            }
          } catch (error) {
              // alert(ErrorMessages.XmlError);
              window.dispatchEvent(new ErrorEvent('error', {message: ErrorMessages.XmlError, error}));
              console.warn(ErrorMessages.XmlError);
              const date = new Date();
              logger.error({id: document.id, code: error.stack,
              startTime: date, args: null, name: 'traverseParagraph.alignment.attributes'});
            }
          }

        /** page break */
        if (lastRenderedPageBreak) {
          para.paraProperty.bPageBreakBefore = true;
        }

        if ( wordWrap ) {
          try {
            if (wordWrap.attributes && wordWrap.attributes.length > 0) {
              para.paraProperty.bWordWrap = ('1' === wordWrap.attributes.getNamedItem('w:val') ? true : false);
            } else {
              throw new Error('w:wordWrap has no attributes');
            }
          } catch (error) {
            // alert(ErrorMessages.XmlError);
            window.dispatchEvent(new ErrorEvent('error', {message: ErrorMessages.XmlError, error}));
            // tslint:disable-next-line: no-console
            console.warn(ErrorMessages.XmlError);
            const date = new Date();
            logger.error({id: document.id, code: error.stack,
            startTime: date, args: null, name: 'traverseParagraph.wordWrap.attributes'});

          }
        }

      } else if (paraChild.nodeName === 'w:r') { /** w:r */

        const nextNode = ParagraphReader.getTheFirstNonTextSiblingFollowed(paraChild);

        if (nextNode && nextNode.nodeName === 'sdtEnd') {
          bFollowedBySdtEnd = true;
        }

        const controlObj = {bFirstSiblingOfSdtStart, unclosedNewControls, bFollowedBySdtEnd};

        // if it's title portion/placeholder, ignore it
        const titlePortionType = this.isTitlePortion(paraChild, controlObj, container, i);

        if (titlePortionType === TitlePortionType.None) {
          if (this.isPlaceholderPortion(showPlaceholder, paraChild, controlObj) === false) {
            RunReader.traverseRun(paraChild, para, runCount, controlObj, uniqueImagelist, bInsertFile, document,
              headerFooter);
            runCount++;
          } else {
            const phPortion = RunReader.traverseRun(paraChild, para, runCount, controlObj, uniqueImagelist,
              bInsertFile, document, headerFooter, showPlaceholder);
            this.setPlaceholderTextProperty(phPortion, controlObj);
          }
        } else if (titlePortionType === TitlePortionType.Region) {
          // title portion in region is not like in struct
          runCount++;
        }

        // console.log(circularParse(circularStringify(container.getContent())));
        bFirstSiblingOfSdtStart = bFirstSiblingOfSdtStart ? false : bFirstSiblingOfSdtStart;
        bFollowedBySdtEnd = bFollowedBySdtEnd ? false : bFollowedBySdtEnd;
      } else if (paraChild.nodeName === 'sdtStart') { /** content control */

        if (ParagraphReader.shouldReadContentControl(bInsertFile, document)) {
          // let runChild = paraChild.childNodes[0];
          let runChild = paraChild.children[0];
          let borderStringStart = '';
          let bPlaceHolder = false; // this prop is in portion

          const nameAttr = paraChild.attributes.getNamedItem('name');
          if (nameAttr) {
            newControlProperty.newControlName = nameAttr.nodeValue;

            // check duplicate
            try {
              newControlManager = document.getNewControlManager();
              if (!newControlManager.checkNewControlName(newControlProperty.newControlName)) {
                if (!bInsertFile) {
                  throw new Error('find duplicate control name out of inserting file');
                } else {
                  // // when inserting file, also check the names which not set in newcontrolnames map yet
                  // if (newControlNamesTempArr.includes(newControlProperty.newControlName)) {
                  //   newControlProperty.newControlName = newControlManager.makeUniqueNameByInsertFile(
                  //     newControlProperty.newControlType, newControlNamesTempArr, newControlProperty.newControlName);
                  //   newControlNamesTempArr.push(newControlProperty.newControlName);
                  // } else {
                  //   // indicate from insert file
                  //     newControlProperty.bInsertFile = true;
                  // }
                }
              }
            } catch (error) {
              window.dispatchEvent(new ErrorEvent('error', {message: ErrorMessages.XmlCorrupted, error}));
            }
          }

          if (paraChild.attributes.getNamedItem('type')) {
            // newControlProperty.newControlType = Number(paraChild.attributes.getNamedItem('type').nodeValue);
            // newControlProperty.newControlType = +paraChild.attributes.getNamedItem('type').nodeValue;
            const type = +paraChild.attributes.getNamedItem('type').nodeValue;
            if (type === NewControlType.SignatureBox) {
              newControlProperty.bReadSign = true;
            }
            newControlProperty.newControlType = type;
          }

          let textProperty: TextProperty = null;
          while (runChild) {
            if ( 'w:rPr' === runChild.nodeName ) {
              textProperty = new TextProperty();
              RunReader.traverseRunProperty(runChild, textProperty, document);
            // } else if (runChild.nodeName !== '#text') {
            } else {
              const textNode = runChild.childNodes[0]; // usually just text node, but may exist childnodes
              // console.log(textNode)
              switch (runChild.nodeName) {
                case 'serialNumber': {
                  if (textNode != null) {
                    newControlProperty.newControlSerialNumber = textNode.nodeValue;
                  }
                  break;
                }

                case 'placeholder':
                  if (textNode != null) { // '' should catch as well
                    newControlProperty.newControlPlaceHolder = textNode.nodeValue;
                  } else {
                    newControlProperty.newControlPlaceHolder = '';
                  }
                  break;

                case 'isMustFill':
                  if (textNode) {
                    newControlProperty.isNewControlMustInput = textNode.nodeValue === '1' ? true : false;
                  }
                  break;

                case 'deleteProtect':
                  if (textNode) {
                    newControlProperty.isNewControlCanntDelete = textNode.nodeValue === '1' ? true : false;
                  }
                  break;

                case 'editProtect':
                  if (textNode) {
                    newControlProperty.isNewControlCanntEdit = textNode.nodeValue === '1' ? true : false;
                  }
                  break;

                case 'copyProtect':
                  if (textNode) {
                    newControlProperty.isNewControlCanntCopy = textNode.nodeValue === '1' ? true : false;
                  }
                  break;

                case 'showBorder':
                  if (textNode) {
                    newControlProperty.isNewControlShowBorder = textNode.nodeValue === '1' ? true : false;
                  }
                  break;

                case 'borderString':
                  if (textNode != null) {
                    // console.log(textNode.nodeValue);
                    borderStringStart = textNode.nodeValue;
                  }
                  break;

                case 'logicEvent': {
                  if (textNode != null) {
                    // console.log(textNode.nodeValue);
                    try {
                      if (textNode.nodeValue) {
                        newControlProperty.cascade = JSON.parse(textNode.nodeValue.replace(/&amp;/g, '&')
                        .replace(/&lt;/g, '<')
                        .replace(/&gt;/g, '>')
                        .replace(/&quot;/g, '"')
                        .replace(/&apos;/g, "'") as string);
                      }
                    } catch (error) {
                      //
                    }
                  }

                  break;
                }

                case 'editReverse':
                  if (textNode) {
                    newControlProperty.isNewControlReverseEdit = textNode.nodeValue === '1' ? true : false;
                  }
                  break;

                case 'backgroundColorHidden':
                  if (textNode) {
                    newControlProperty.isNewControlHiddenBackground = textNode.nodeValue === '1' ? true : false;
                  }
                  break;

                case 'customProperty': {
                  const customProperties = runChild.childNodes;
                  if (customProperties.length > 0) { // usually if in this block, length must be > 0

                    const customPropertyArr = readCustomProperties(customProperties);
                    // console.log(customPropertyArr)
                    newControlProperty.customProperty = customPropertyArr;
                  }
                  break;
                }

                case 'tabJump': {
                  if (textNode != null) {
                    newControlProperty.tabJump = textNode.nodeValue === '1' ? true : false;
                  }
                  break;
                }

                case 'newControlHidden': {
                  if (textNode != null) {
                    newControlProperty.isNewControlHidden = textNode.nodeValue === '1' ? true : false;
                  }
                  break;
                }

                case 'listItems': {
                  const listItems = runChild.childNodes;
                  // console.log(listItems)
                  if (listItems.length > 0) {
                    const listItemsArr = [];
                    for (const listItem of listItems) {

                      const listItemNodes = Array.from(listItem.childNodes); // so that find() can be used
                      const nameNode: any = listItemNodes.find((item) => (item as any).nodeName === 'name');
                      const valueNode: any = listItemNodes.find((item) => (item as any).nodeName === 'value');
                      const selectNode: any = listItemNodes.find((item) => (item as any).nodeName === 'select');

                      if ( null == nameNode || 0 === nameNode.childNodes.length ) {
                        continue;
                      }

                      const name = nameNode.childNodes[0].nodeValue;
                      const value = (valueNode != null && valueNode.childNodes[0] ) ?
                                                  valueNode.childNodes[0].nodeValue : '';

                      const select = (selectNode != null && selectNode.childNodes[0] ) ?
                                      (selectNode.childNodes[0].nodeValue === '1' ? true : false) : false;

                      listItemsArr.push(new CodeValueItem(name, value, select));
                    }
                    newControlProperty.newControlItems = listItemsArr;
                  }
                  break;
                }

                case 'helpTip': {
                  if (textNode != null) {
                    newControlProperty.newControlInfo = textNode.nodeValue;
                  }
                  break;
                }

                // ui only
                case 'showPlaceholder': {
                  if (textNode != null) {
                    if (textNode.nodeValue === '1') {
                      showPlaceholder = true;
                    }
                  }
                  break;
                }

                // text struct
                case 'secretType': { // shared in text and value struct
                  if (textNode != null) {
                    newControlProperty.newControlDisplayType = +textNode.nodeValue;
                  }
                  break;
                }

                case 'fixedLength': {
                  if (textNode != null) {
                    newControlProperty.newControlFixedLength = +textNode.nodeValue;
                  }
                  break;
                }

                case 'maxLength': {
                  if (textNode != null) {
                    newControlProperty.newControlMaxLength = +textNode.nodeValue;
                  }
                  break;
                }

                case 'title': {
                  if (textNode != null) {
                    newControlProperty.newControlTitle = textNode.nodeValue;
                  }
                  break;
                }

                // value struct
                case 'minValue': {
                  if (textNode != null) {
                    newControlProperty.minValue = +textNode.nodeValue;
                  }
                  break;
                }

                case 'maxValue': {
                  if (textNode != null) {
                    newControlProperty.maxValue = +textNode.nodeValue;
                  }
                  break;
                }

                case 'precision': {
                  if (textNode != null) {
                    newControlProperty.precision = +textNode.nodeValue;
                  }
                  break;
                }

                case 'unit': {
                  if (textNode != null) {
                    newControlProperty.unit = textNode.nodeValue;
                  }
                  break;
                }

                // multi struct
                case 'retrieve': {
                  if (textNode != null) {
                    newControlProperty.retrieve = textNode.nodeValue === '1' ? true : false;
                  }
                  break;
                }

                case 'selectPrefixContent': {
                  if (textNode != null) {
                    newControlProperty.selectPrefixContent = textNode.nodeValue;
                  }
                  break;
                }

                case 'prefixContent': {
                  if (textNode != null) {
                    newControlProperty.prefixContent = textNode.nodeValue;
                  }
                  break;
                }

                case 'separator': {
                  if (textNode != null) {
                    newControlProperty.separator = textNode.nodeValue;
                  }
                  break;
                }

                case 'showValue': {
                  if (textNode != null) {
                    newControlProperty.isShowValue = textNode.nodeValue === '1' ? true : false;
                  }
                  break;
                }

                // datebox struct
                case 'dateType': {
                  if (textNode != null) {
                    newControlProperty.dateBoxFormat = +textNode.nodeValue;
                  }
                  break;
                }

                case 'customDateFormat': {
                  if (textNode != null) {
                    let nodeValue = textNode.nodeValue;
                    if (nodeValue === '{}') {
                      break;
                    }
                    // handle unescaped string
                    const doc = domParser.parseFromString(nodeValue, 'text/html');
                    if (doc != null) {
                      nodeValue = doc.documentElement.textContent;
                    }

                    newControlProperty.customFormat = JSON.parse(nodeValue);
                  }
                  break;
                }

                case 'startDate': {
                  if (textNode != null) {
                    newControlProperty.startDate = textNode.nodeValue;
                  }
                  break;
                }

                case 'endDate': {
                  if (textNode != null) {
                    newControlProperty.endDate = textNode.nodeValue;
                  }
                  break;
                }

                // checkbox
                case 'showRight': { // also radiobutton
                  if (textNode != null) {
                    newControlProperty.showRight = textNode.nodeValue === '1' ? true : false;
                  }
                  break;
                }

                case 'checked': {
                  if (textNode != null) {
                    newControlProperty.checked = textNode.nodeValue === '1' ? true : false;
                  }
                  break;
                }

                case 'printSelected': {
                  if (textNode != null) {
                    newControlProperty.printSelected = textNode.nodeValue === '1' ? true : false;
                  }
                  break;
                }

                case 'label': {
                  if (textNode != null) {
                    newControlProperty.label = textNode.nodeValue;
                  }
                  break;
                }

                // radiobutton
                case 'showType': {
                  if (textNode != null) {
                    let showType = +textNode.nodeValue;
                    if (isNaN(showType) === true) {
                      showType = STD_START_DEFAULT.showType;
                    }
                    newControlProperty.showType = showType;
                  }
                  break;
                }

                case 'spaceNum': {
                  if (textNode != null) {
                    newControlProperty.spaceNum = +textNode.nodeValue;
                  }
                  break;
                }

                case 'supportMultLines': {
                    if (textNode != null && typeof textNode === 'string') {
                        newControlProperty.supportMultLines =
                            textNode === '1' ? true : false;
                    }
                    break;
                }

                // signature box
                case 'signatureCount': {
                  if (textNode != null) {
                    newControlProperty.signatureCount = +textNode.nodeValue;
                  }
                  break;
                }

                case 'preText': {
                  if (textNode != null) {
                    newControlProperty.preText = textNode.nodeValue;
                  }
                  break;
                }

                case 'signatureSeparator': {
                  if (textNode != null) {
                    newControlProperty.signatureSeparator = safeDecodeURIComponent(textNode.nodeValue, documentVersion);
                  }
                  break;
                }

                case 'postText': {
                  if (textNode != null) {
                    newControlProperty.postText = textNode.nodeValue;
                  }
                  break;
                }

                case 'signaturePlaceholder': {
                  if (textNode != null) {
                    newControlProperty.signaturePlaceholder = textNode.nodeValue;
                  }
                  break;
                }

                case 'signatureRatio': {
                  if (textNode != null) {
                    newControlProperty.signatureRatio = +textNode.nodeValue;
                  }
                  break;
                }

                case 'rowHeightRestriction': {
                  if (textNode != null) {
                    newControlProperty.rowHeightRestriction = textNode.nodeValue === '1' ? true : false;
                  }
                  break;
                }

                default:
                  break;
              }
            }
            runChild = runChild.nextElementSibling;
          } // end while

          // console.log(newControlProperty);
          // console.log(circularParse(circularStringify(newControlProperty)));

          if ( null == textProperty ) {
            textProperty = new TextProperty();
          }

          newControlManager = document.getNewControlManager();
          // control with duplicate name should already be handled
          let lastNewControl = newControlManager.createNewControl(para, newControlProperty);
          if (lastNewControl == null) {
            // tslint:disable-next-line: no-console
            // console.warn('duplicate control name found at opening document');
            const source = newControlProperty.bInsertFile ? FileSource.InsertFile : null;
            newControlProperty.newControlName = newControlManager.makeUniqueName(newControlProperty.newControlType,
                newControlProperty.newControlName, source);
            lastNewControl = newControlManager.createNewControl(para, newControlProperty);
          }
          // when insert file, have a temp controlNames array
          if (bInsertFile) {
            // if (newControlNamesTempArr.length === 0) {
            //   // if newControlNames is not empty, assign value to temp array
            //   const newControlNamesMap = newControlManager.getNameMap();
            //   if (newControlNamesMap.size !== 0) {
            //     for (const key of newControlNamesMap.keys()) {
            //       newControlNamesTempArr.push(key);
            //     }
            //   }
            // }

            // if ( false === newControlNamesTempArr.includes(lastNewControl.getNewControlName()) ) {
            //   newControlNamesTempArr.push(lastNewControl.getNewControlName());
            // }
          }

          // reset newControlProperty
          this.resetNewControlProperty(newControlProperty);

          // lastNewControl.initPlaceHolderDefaultTextProperty(
          //   lastNewControl.getNewControlContent()
          //   .getPlaceHolder().textProperty
          //   .copy());

          /** figure out portion.bPlaceHolder's val */
          /** if <sdtStart> is not strictly followed by <sdtEnd>, bPlaceHolder is false */
          const nextNode = ParagraphReader.getTheFirstNonTextSiblingFollowed(paraChild);
          // 11.3.2020: the next node MUST BE struct portion or PLACEHOLDER PORTION
          if (nextNode && nextNode.nodeName === 'sdtEnd') {
            // consider compatibility of 'no placeholder portion' case
            bPlaceHolder = true;
            bFirstSiblingOfSdtStart = false; // actually no use as of 11.4.2020

          } else {

            bPlaceHolder = false;
            if (nextNode) { // look out for null. still may be problematic
              bFirstSiblingOfSdtStart = true;

              // if a struct has title + placeholder, still bPlaceHolder may be true
              if (nextNode.nodeName === 'w:r') {
                // struct portion should be the only one btw sdtstart & end
                if (nextNode.nextElementSibling && nextNode.nextElementSibling.nodeName === 'sdtEnd') {
                  // 1. old compatible: [ title ] 2. [ {normal portion} ] 3. new: [ placeholder ]
                  if (lastNewControl.getTitle() === nextNode.textContent) {
                    bPlaceHolder = true;
                  }
                } else {
                  // [ title placeholder ]
                  // no need to deal with ph text props here, just override it
                }
              }

            }
          }

          /** create new seperation portion for [ always */
          const newControlContent = lastNewControl.getNewControlContent();
          const newControlPlaceHolder = newControlContent.getPlaceHolder();
          const newControlStartBorderPortion = lastNewControl.getStartBorderPortion();
          newControlStartBorderPortion.textProperty = textProperty;

          if (bPlaceHolder) {
            // newControlPlaceHolder.textProperty = textProperty.copy();
            // placeholder always has default text prop - not any more, will override using showPlaceholder
            // here just set a default text props
            newControlPlaceHolder.textProperty = new TextProperty();
          }

          // if (bPlaceHolder) {
          // newControlStartBorderPortion.setPlaceHolder1(bPlaceHolder);

          // const newControlEndBorderPortion = lastNewControl.getEndBorderPortion();
          // newControlEndBorderPortion.setPlaceHolder1(bPlaceHolder);
          lastNewControl.setPlaceHolder(bPlaceHolder);
            // newControlPlaceHolder.setPlaceHolder1(bPlaceHolder);
          // } else {
          //   newControlStartBorderPortion.setPlaceHolder1(bPlaceHolder);
          //   // newControlPlaceHolder.setPlaceHolder1(bPlaceHolder);
          // }

          if (!nextNode) { // null. means at end of para
            newControlPlaceHolder.content = newControlStartBorderPortion.content; // R.W: not in use
          }

          para.addToContent(runCount, newControlStartBorderPortion);
          runCount++; // not matter

          unclosedNewControls.push(lastNewControl);

          /** set map in content control manager no matter what */
          // newControlManager.getNameMap()
          //   .set(lastNewControl.getNewControlName(), lastNewControl);
        }

      } else if (paraChild.nodeName === 'sdtEnd') { /** sdtEnd */

        showPlaceholder = false;
        if (ParagraphReader.shouldReadContentControl(bInsertFile, document) && unclosedNewControls.length > 0) {
          // const newControlName = paraChild.attributes.getNamedItem('name').nodeValue;
          // const currentControl = newControlManager.getNameMap()
          //   .get(newControlName);›
          const currentControl = unclosedNewControls[unclosedNewControls.length - 1];
          const newControlStartBorderPortion = currentControl.getStartBorderPortion();
          let textProperty: TextProperty = null;

          if (newControlStartBorderPortion.bPlaceHolder) { // find current control's bPlaceholder
            // as of 7/8/2020 only startBorderPortion.bPlaceHolder stays updated
            para.addToContent(runCount, currentControl.getNewControlContent()
              .getPlaceHolder());
            runCount++;

          } else if (i === 0 || (i === 1 && paraChild.previousSibling.nodeName === '#text')) {
            /** sdtEnd is at start of para. It may be 0 or 1 based on formatted or not */
            // put ] in portion and add to para
            // const currentControlContent = currentControl.getNewControlContent();
            // const newControlEndBorder = currentControlContent.getEndBorder();

            // const portion = new ParaPortion(para);
            // portion.addToContent(0, newControlEndBorder);

            // para.addToContent(runCount, portion);
            // runCount++;

            // unclosedNewControls.pop();
          }

          const paraChildChildren = paraChild.children;
          if ( paraChildChildren && 0 < paraChildChildren.length ) {
            let nodeChild = paraChild.children[0];
            let bSuccess = false;

            while (nodeChild) {
              switch (nodeChild.nodeName) {
                case 'w:rPr':
                  textProperty = new TextProperty();
                  // RunReader.traverseRunProperty(paraChild.childNodes[0], textProperty);
                  RunReader.traverseRunProperty(paraChild.children[0], textProperty, document);
                  bSuccess = true;
                  break;
              }

              if ( bSuccess ) {
                break;
              }

              nodeChild = nodeChild.nextElementSibling;
            }
          }

          if ( null == textProperty ) {
            textProperty = new TextProperty();
          }

          /** create new seperation portion for ] always */
          const newControlEndBorderPortion = currentControl.getEndBorderPortion();
          newControlEndBorderPortion.textProperty = textProperty;
          para.addToContent(runCount, newControlEndBorderPortion); // will also set portion's parent!
          runCount++;

          unclosedNewControls.pop();

          /** if it's control check/radiobutton, need extra step to fill in inner content */
          if (currentControl instanceof NewControlCheck || currentControl instanceof NewControlRadio) {
            currentControl.addContent(); // lsy's method
          }

          /** set map in content control manager for any struct */
          // newControlNames[]  -> all structs
          if (!bInsertFile) {
            newControlManager.getNameMap()
            .set(currentControl.getNewControlName(), currentControl);
          }

          if (unclosedNewControls.length === 0) { // root
            if (!bInsertFile) {
              // if it's a root content control, add to "root content control array"(map already added in sdtstart)
              // newControls[]  -> root structs
              // NOTE the order of elements in the array SHOULD indicate structs appearance in doc
              if (headerFooter == null) {
                newControlManager.getAllNewControls()
                  .splice(newControlManager.getAllNewControls().length, 0, currentControl);
              } else {
                if (headerFooter.isHeader()) {
                  const structCount = ContentControlDescObj.headerStructCount;
                  // console.log(structCount)
                  newControlManager.getAllNewControls()
                    .splice(structCount, 0, currentControl);
                  ContentControlDescObj.headerStructCount++;
                } else {
                  // footer
                  newControlManager.getAllNewControls()
                  .splice(newControlManager.getAllNewControls().length, 0, currentControl);
                }
              }
            } else {
              // pasteControl need to fill up when insertFile
              // control order is important
              const contentPos = document.getCurContentPosInDoc(false, true);
              newControlManager.addPasteControl({control: currentControl, contentPos});
              // TODO?
            }

          } else {
            // 非根节点时，插入文件需set name map
            if (bInsertFile) {
              // newControlManager.getNameMap()
              // .set(currentControl.getNewControlName(), currentControl);
              newControlManager.addNewControlInMap(currentControl);
            }
            // add to leafList. two-way
            const lastElemInUnclosedNewControls = unclosedNewControls[unclosedNewControls.length - 1];
            currentControl.setParent(lastElemInUnclosedNewControls);
            lastElemInUnclosedNewControls.getLeafList()
              .push(currentControl);
          }
        }

      } else if ( 'w:ins' === paraChild.nodeName || 'w:del' === paraChild.nodeName ) {
        const attrs = paraChild.attributes;
        const revsionChildren = paraChild.children;
        const length = revsionChildren ? revsionChildren.length : 0;
        if ( attrs && attrs.length > 0 && length > 0) {
          const userId = attrs.getNamedItem('id') ? attrs.getNamedItem('id').nodeValue : null;
          const description = attrs.getNamedItem('des') ? attrs.getNamedItem('des').nodeValue : null;
          const date = attrs.getNamedItem('date') ? attrs.getNamedItem('date').nodeValue : null;
          const userName = attrs.getNamedItem('author') ? attrs.getNamedItem('author').nodeValue : null;
          const savedCount = attrs.getNamedItem('savedCount') ?
              Number.parseInt(attrs.getNamedItem('savedCount').nodeValue, 0) : 0;
          let line = attrs.getNamedItem('line') ? Number.parseInt(attrs.getNamedItem('line').nodeValue, 0) : 1;
          let color = attrs.getNamedItem('color') ? attrs.getNamedItem('color').nodeValue : null;
          let level = attrs.getNamedItem('level') ? Number.parseInt(attrs.getNamedItem('level').nodeValue, 0) : 1;

          const revisionManager = document.getRevisionsManager();
          const type = 'w:ins' === paraChild.nodeName ? ReviewType.Add : ReviewType.Remove;
          const reviewInfo = new ReviewInfo(document);

          const temp = date ? date.match('(.*)T(.*)') : null;
          const time = new Date();
          if ( temp && 3 === temp.length) {
            const fullYear = temp[1].match('(\\d*)-(\\d*)-(\\d*)');
            const fullHour = temp[2].match('(\\d*):(\\d*):(\\d*)');
            if ( date && fullYear && fullHour && 3 <= fullYear.length && 3 <= fullHour.length ) {
              // time.setFullYear(Number.parseInt(fullYear[1], 0),
              //     Number.parseInt(fullYear[2], 0), Number.parseInt(fullYear[3], 0));
              // time.setHours(Number.parseInt(fullHour[1], 0),
              //     Number.parseInt(fullHour[2], 0), Number.parseInt(fullHour[3], 0));

              const timeString = (fullYear[0] + ' ' + fullHour[0]).replace('-', '/');
              time.setTime(Date.parse(timeString));

              const tempTime = new Date();
              if ( time.getTime() > tempTime.getTime() ) {
                time.setTime(tempTime.getTime());
              }
            }
          }

          let bFind = false;
          REVISION_COLOR.forEach((value, key) => {
            if ( value === color) {
              bFind = true;
              color = key;
              return ;
            }
          });

          if ( (null === color || false === bFind) && !isValidORGBColor(color) ) {
            color = '红色';
          }

          if ( 0 > line ) {
            line = 0;
          } else if ( 2 < line ) {
            line = 2;
          }

          if ( 1 > level ) {
            level = 1;
          } else if ( 4 < level ) {
            level = 4;
          }

          reviewInfo.setRevisionSetting({
              userId, description, style: line, color, date: time, userName, level, savedCount});

          const controlObj = {bFirstSiblingOfSdtStart, unclosedNewControls, bFollowedBySdtEnd};
          for (let index = 0; index < length; index++) {
            const runChild = revsionChildren[index];

            RunReader.traverseRun(runChild, para, runCount, controlObj, uniqueImagelist, bInsertFile, document,
              headerFooter);
            para.content[runCount].setReviewTypeWithInfo(type, reviewInfo.copy());
            revisionManager.addRevisionByReviewInfo(para, reviewInfo);
            runCount++;
          }

          // if ( 0 === length ) {
          //   para.content[para.content.length - 1].setReviewTypeWithInfo(type, reviewInfo);
          //   runCount++;
          // }
        }
      }

      // }

    } // for

    para.setParaEndProperty();

    if (!bInsertFile) { // open file

      if (headerFooter == null) {
        if (container == null) {
          document.addToContent(document.content.length, para);
        } else {
          // add this para to region
          if (!skipAddCurPara) {
            container.addToContent(container.getContent().length, para);
          }
        }
      } else {
        // header footer
        const headerFooterOperateContent = headerFooter.content;
        const headerFooterContent = headerFooterOperateContent.content;
        // remove the default empty para if not populated yet
        if (headerFooterContent.length === 1 && headerFooterContent[0].isEmpty() === true) { // only exist empty para
          // headerFooterContent = [];
          headerFooterContent.splice(headerFooterContent.length - 1, 1);
        }

        if (container == null) {
          headerFooterOperateContent.addToContent(headerFooterContent.length, para);
        } else {
          container.addToContent(container.getContent().length, para);
        }

        // console.log(headerFooter)
      }

    } else { // insert file
      // console.dir(node)
      if (container == null) {
        paras.push(para);
        // if (!node.nextElementSibling || (node.nextElementSibling.nodeName === 'w:sectPr')) {
        if (node.nextElementSibling && (node.nextElementSibling.nodeName === 'w:sectPr')) {
          // TODO: always same lvl as sectpr? no! headerfooter insert file with region
          // add empty paras when required
          if (!bNoEndPara) {
            for (let i = 0; i < INSERT_FILE_END_EMPTY_PARA + 1; i++) {
              paras.push(new Paragraph(document, document));
            }
          }

          // console.log(paras)
          document.addPasteContent(paras, false);

          // add empty paragraph at end
          // for (let i = 0; i < INSERT_FILE_END_EMPTY_PARA; i++) {
          //   document.addToContent(document.selection.startPos + insertParaCount + 2,
          // new Paragraph(document, document));
          // }
        }
      } else {
        // add this para to region
        // container.addToContent(container.getContent().length, para);
        paras.push(para);
        // if (!node.nextElementSibling || (node.nextElementSibling.nodeName === 'w:sectPr')) {
        if (node.nextElementSibling && (node.nextElementSibling.nodeName === 'w:sectPr')) {

          // add empty paras when required
          if (!bNoEndPara) {
            for (let i = 0; i < INSERT_FILE_END_EMPTY_PARA + 1; i++) {
              paras.push(new Paragraph(document, document));
            }
          }

          // console.log(paras)
          document.addPasteContent(paras, false);

        }
      }

    }

  } // traverseP

  /**
   * txml traverse para
   * @param container Region/Table. DocumentContent is within table cell
   * @param index node index
   * @param nodes root node collection
   */
  public static tTraverseParagraph(node: rtNode, document: Document, ContentControlDescObj: IContentControlDescObj,
                                   uniqueImagelist: Map<string, IUniqueImageProps>, bInsertFile: boolean = false,
                                   paras: DocumentContentElementBase[], bNoEndPara: boolean = false,
                                   container: Region | TableCell = null, headerFooter: HeaderFooter = null,
                                   index: number, nodes: (string | number | rtNode)[]): void {


    let para = null;
    let skipAddCurPara = false;
    const documentVersion = document != null ? document.getDocumentVersion() : 0;
    if (bInsertFile === false) {
      if (container == null) {
        if (headerFooter == null) {
          para = new Paragraph(document, document);
        } else {
          para = new Paragraph(headerFooter.getContent(), document);
        }
      } else {
        // default
        para = new Paragraph(container.getOperateContent(), document);

        // if it's a region with title, the first para is already created
        const containerContent = container.getContent();
        if (containerContent.length === 1 && container instanceof Region) {
          // this condition should only enter once
          // Attention: if 1st para in region is para with ONLY title portion, the condition would always suffice
          // thus should develop a way to remedy that
          let firstTextNode: rtNode = null;
          let firstTextPr = null;
          for (const nodeChild of node.children) {
            // first find w:r then get first w:t
            // TODO: may be danger
            if (typeof nodeChild === 'object' && nodeChild.tagName === 'w:r') {
              for (const runChild of nodeChild.children) {
                if (typeof runChild === 'object' && runChild.tagName === 'w:rPr') {
                  firstTextPr = new TextProperty();
                  RunReader.tTraverseRunProperty(runChild, firstTextPr, document);
                }
                if (typeof runChild === 'object' && runChild.tagName === 'w:t') {
                  firstTextNode = runChild;
                  break;
                }
              }
            }
            if (firstTextNode != null) {
              break;
            }
          }

          let firstText = '';
          if (firstTextNode != null) {
            const tempText = firstTextNode.children[0];
            firstText = (typeof tempText === 'string') ? safeDecodeURIComponent(tempText, documentVersion) : firstText;
          }

          // when traversing 2nd para, the first w:t must never be title portion
          // make sure only the 1st para enter this
          if (firstText === container.getTitle()) {
            const thePara = containerContent[0];
            const theParaContent = thePara.getContent();
            // iff case: 1 title portion, 1 end portion
            // tslint:disable-next-line: max-line-length
            if (theParaContent.length === 2 && theParaContent[0] instanceof ParaPortion && theParaContent[1].isParaEndPortion()) {
              const thePortion = theParaContent[0];
              if (thePortion.getTextContent() === container.getTitle()) {
                para = thePara;
                skipAddCurPara = true;
                container.setTitlePortionTextProperty2(firstTextPr);
              }
            }
          }
        }
      }
    } else {
      // headerfooter ignored in insert file
      if (container == null) {
        para = new Paragraph(document, document);
      } else {
        para = new Paragraph(container.getOperateContent(), document);
      }
    }

    const paraChildren = node.children as rtNode[];

    // death stranding
    let {newControlManager} = ContentControlDescObj;
    const {newControlProperty, unclosedNewControls} = ContentControlDescObj;

    // these three vars should be meaningless if cross paragraph
    // let bFollowedBySdtEnd = false; // just related to w:r
    // let bFirstSiblingOfSdtStart = false; // just related to w:r
    let showPlaceholder = false; // if sdtstart has <showPlaceholder>, locate & save placeholder textprops

    let runCount = 0;
    // const domParser = new DOMParser();
    const newControlNamesTempArr = []; // used at inserting file

    for (let i = 0; i < paraChildren.length; i++) {
      const paraChild = paraChildren[i];

      if (typeof paraChild === 'object') {
        /** w:pPr */
        if (paraChild.tagName === 'w:pPr') {

          let indent: rtNode = null;
          let spacing: rtNode = null;
          let alignment: rtNode = null;
          let lastRenderedPageBreak: rtNode = null;
          let wordWrap: rtNode = null;
          let nisSignAuthor: rtNode = null;
          for (const nodeChild of paraChild.children) {
            if (typeof nodeChild === 'object') {
              if (nodeChild.tagName === 'w:ind') {
                indent = nodeChild;
              } else if (nodeChild.tagName === 'w:spacing') {
                spacing = nodeChild;
              } else if (nodeChild.tagName === 'w:jc') {
                alignment = nodeChild;
              } else if (nodeChild.tagName === 'w:lastRenderedPageBreak') {
                lastRenderedPageBreak = nodeChild;
              } else if ( 'w:wordWrap' === nodeChild.tagName ) {
                wordWrap = nodeChild;
              } else if (NURSING_FEATURE && 'w:nisSignAuthor' === nodeChild.tagName) {
                nisSignAuthor = nodeChild;
              }
            }
          }

          /** indentation */
          try {
            if (indent) {
              if (indent.attributes) {
                if (indent.attributes['w:left']) {
                  // filtered at writer, should not need to check if isNaN
                  para.paraProperty.paraInd.left =
                    Number(indent.attributes['w:left']);
                }
                if (indent.attributes['w:firstLine']) {
                  para.paraProperty.paraInd.firstLine =
                    Number(indent.attributes['w:firstLine']);
                }
                if (indent.attributes['w:right']) {
                  para.paraProperty.paraInd.right =
                    Number(indent.attributes['w:right']);
                }

              } else {
                throw new Error('w:ind has no attributes');
              }
            }
          } catch (error) {
            // alert(ErrorMessages.XmlError);
            // tslint:disable-next-line: no-console
            console.warn(ErrorMessages.XmlError);
            window.dispatchEvent(new ErrorEvent('error', {message: ErrorMessages.XmlError, error}));
            const date = new Date();
            logger.error({id: document.id, code: error.stack,
              startTime: date, args: null, name: 'traverseParagraph.indent.attributes'});
          }

          /** line spacing */
          if (spacing) {
            // console.log(spacing)
            try {
              if (spacing.attributes) {
                if (spacing.attributes['w:lineType']) {
                  para.paraProperty.paraSpacing.lineSpacingType =
                    Number(spacing.attributes['w:lineType']);
                }
                if (spacing.attributes['w:line']) {
                  para.paraProperty.paraSpacing.lineSpacing =
                    Number(spacing.attributes['w:line']);
                }
              } else {
                throw new Error('w:spacing has no attributes');
              }
            } catch (error) {
              // alert(ErrorMessages.XmlError);
              window.dispatchEvent(new ErrorEvent('error', {message: ErrorMessages.XmlError, error}));
              // tslint:disable-next-line: no-console
              console.warn(ErrorMessages.XmlError);
              const date = new Date();
              logger.error({id: document.id, code: error.stack,
              startTime: date, args: null, name: 'traverseParagraph.spacing.attributes'});

            }
          }

          /** alignment */
          if (alignment) {
            // console.log(alignment);
            try {
              if (alignment.attributes && alignment.attributes['w:val']) {
                const alignmentVal = alignment.attributes['w:val'];
                // console.log(alignment.attributes.getNamedItem('w:val').nodeValue)
                let alignmentType = AlignType.Justify; // default to be justified
                switch (alignmentVal) {
                  case AlignmentOptions.BOTH:
                    alignmentType = AlignType.Justify;
                    break;
                  case AlignmentOptions.LEFT:
                    alignmentType = AlignType.Left;
                    break;
                  case AlignmentOptions.RIGHT:
                    alignmentType = AlignType.Right;
                    break;
                  case AlignmentOptions.CENTER:
                    alignmentType = AlignType.Center;
                    break;
                  default:
                    break;
                }

                para.paraProperty.alignment = alignmentType;

              } else {
                throw new Error('w:jc has no attributes');
              }
            } catch (error) {
              // alert(ErrorMessages.XmlError);
              window.dispatchEvent(new ErrorEvent('error', {message: ErrorMessages.XmlError, error}));
              // tslint:disable-next-line: no-console
              console.warn(ErrorMessages.XmlError);
              const date = new Date();
              logger.error({id: document.id, code: error.stack,
              startTime: date, args: null, name: 'traverseParagraph.alignment.attributes'});
            }
          }

          /** page break */
          if (lastRenderedPageBreak) {
            para.paraProperty.bPageBreakBefore = true;
          }

          if ( wordWrap ) {
            try {
              if (wordWrap.attributes && wordWrap.attributes['w:val']) {
                para.paraProperty.bWordWrap = ('1' === wordWrap.attributes['w:val'] ? true : false);
              } else {
                throw new Error('w:wordWrap has no attributes');
              }
            } catch (error) {
              // alert(ErrorMessages.XmlError);
              window.dispatchEvent(new ErrorEvent('error', {message: ErrorMessages.XmlError, error}));
              // tslint:disable-next-line: no-console
              console.warn(ErrorMessages.XmlError);
              const date = new Date();
              logger.error({id: document.id, code: error.stack,
              startTime: date, args: null, name: 'traverseParagraph.wordWrap.attributes'});

            }
          }

          if (nisSignAuthor && nisSignAuthor.attributes && nisSignAuthor.attributes['w:val']) {
            para.nisSignAuthor = nisSignAuthor.attributes['w:val'];
          }
        } else if (paraChild.tagName === 'w:r') { /** w:r */
          // console.log(paraChild)
          // pay attention if it's i or index!
          // const nextNode = ParagraphReader.tGetTheFirstNonTextSiblingFollowed(i, paraChildren);
          // // console.log(nextNode)
          // if (nextNode && nextNode.tagName === 'sdtEnd') {
          //   bFollowedBySdtEnd = true;
          // }

          // const controlObj = {bFirstSiblingOfSdtStart, unclosedNewControls, bFollowedBySdtEnd};
          const controlObj = {unclosedNewControls};

          // if it's title portion/placeholder, ignore it
          const titlePortionType = this.tIsTitlePortion(paraChild, controlObj, container, i, document, paraChildren, i, false, documentVersion);
          if (titlePortionType === TitlePortionType.None) {
            if (this.tIsPlaceholderPortion(showPlaceholder, paraChild, controlObj, documentVersion) === false) {
              RunReader.tTraverseRun(paraChild, para, runCount, controlObj, uniqueImagelist, bInsertFile, document,
                headerFooter);
              runCount++;
            } else {
              const phPortion = RunReader.tTraverseRun(paraChild, para, runCount, controlObj, uniqueImagelist,
                bInsertFile, document, headerFooter, showPlaceholder);
              this.setPlaceholderTextProperty(phPortion, controlObj);
            }
          } else if (titlePortionType === TitlePortionType.Region) {
            // title portion in region is not like in struct
            runCount++;
          }

          // console.log(circularParse(circularStringify(container.getContent())));
          // bFirstSiblingOfSdtStart = bFirstSiblingOfSdtStart ? false : bFirstSiblingOfSdtStart;
          // bFollowedBySdtEnd = bFollowedBySdtEnd ? false : bFollowedBySdtEnd;
        } else if (paraChild.tagName === 'sdtStart') { /** content control */

          // check if prev struct lost ] part
          const prevControl = unclosedNewControls[unclosedNewControls.length - 1];
          if (prevControl != null) {
            const prevControlType = prevControl.getType();
            if (prevControlType !== NewControlType.Section && prevControlType !== NewControlType.SignatureBox) {
              // tslint:disable-next-line: no-console
              console.warn('sdtEnd missing detected, should be fixed automatically:)');
              // add ] component
              showPlaceholder = false;
              runCount = this.tTraverseSdtEnd(bInsertFile, document, paraChild, para, runCount, headerFooter,
                ContentControlDescObj);
              // remove the item from unclosednewcontrols
              // unclosedNewControls.pop(); // already poped
            }
          }

          if (ParagraphReader.shouldReadContentControl(bInsertFile, document)) {
            // let runChild = paraChild.childNodes[0];
            const runChildren = paraChild.children;
            let borderStringStart = '';
            let bPlaceHolder = false; // this prop is in portion

            const nameAttr = paraChild.attributes['name'];
            if (nameAttr) {
              newControlProperty.newControlName = nameAttr;

              // check duplicate
              try {
                newControlManager = document.getNewControlManager();
                if (!newControlManager.checkNewControlName(newControlProperty.newControlName)) {
                  if (!bInsertFile) {
                    throw new Error('find duplicate control name out of inserting file');
                  } else {
                    // when inserting file, also check the names which not set in newcontrolnames map yet
                    // if (newControlNamesTempArr.includes(newControlProperty.newControlName)) {
                    //   newControlProperty.newControlName = newControlManager.makeUniqueNameByInsertFile(
                    //     newControlProperty.newControlType, newControlNamesTempArr, newControlProperty.newControlName);
                    //   newControlNamesTempArr.push(newControlProperty.newControlName);
                    // } else {
                    //   // indicate from insert file
                    //   newControlProperty.bInsertFile = true;
                    // }
                  }
                }
              } catch (error) {
                window.dispatchEvent(new ErrorEvent('error', {message: ErrorMessages.XmlCorrupted, error}));
              }
            }

            if (paraChild.attributes['type']) {
              // newControlProperty.newControlType = Number(paraChild.attributes.getNamedItem('type').nodeValue);
              const type = +paraChild.attributes['type'];
              if (type === NewControlType.SignatureBox) {
                newControlProperty.bReadSign = true;
              }
              newControlProperty.newControlType = type;
            }

            let textProperty: TextProperty = null;
            for (const runChild of runChildren) {
              if (typeof runChild === 'object') {
                if ( 'w:rPr' === runChild.tagName ) {
                  textProperty = new TextProperty();
                  RunReader.tTraverseRunProperty(runChild, textProperty, document);
                // } else if (runChild.nodeName !== '#text') {
                } else {
                  const textNode = runChild.children[0];
                  const res = readerStructProps(newControlProperty, textNode, runChild, documentVersion);
                  if (res.borderStringStart !== undefined) {
                    borderStringStart = res.borderStringStart;
                  }
                  if (res.showPlaceholder !== undefined) {
                    showPlaceholder = res.showPlaceholder;
                  }
                }
              }

            } // end for

            // console.log(newControlProperty);
            // console.log(circularParse(circularStringify(newControlProperty)));

            if ( null == textProperty ) {
              textProperty = new TextProperty();
            }

            newControlManager = document.getNewControlManager();
            // control with duplicate name should already be handled
            let lastNewControl = newControlManager.createNewControl(para, newControlProperty);
            if (lastNewControl == null) {
              // tslint:disable-next-line: no-console
              // console.warn('duplicate control name detected, should be fixed automatically:)');
              const source = newControlProperty.bInsertFile ? FileSource.InsertFile : null;
              newControlProperty.newControlName = newControlManager.makeUniqueName(newControlProperty.newControlType,
                  newControlProperty.newControlName, source);
              lastNewControl = newControlManager.createNewControl(para, newControlProperty);
            }
            // when insert file, have a temp controlNames array
            if (bInsertFile) {
              // if (newControlNamesTempArr.length === 0) {
              //   // if newControlNames is not empty, assign value to temp array
              //   const newControlNamesMap = newControlManager.getNameMap();
              //   if (newControlNamesMap.size !== 0) {
              //     for (const key of newControlNamesMap.keys()) {
              //       newControlNamesTempArr.push(key);
              //     }
              //   }
              // }

              // if ( false === newControlNamesTempArr.includes(lastNewControl.getNewControlName()) ) {
              //   newControlNamesTempArr.push(lastNewControl.getNewControlName());
              // }
            }

            // reset newControlProperty
            this.resetNewControlProperty(newControlProperty);

            // lastNewControl.initPlaceHolderDefaultTextProperty(
            //   lastNewControl.getNewControlContent()
            //   .getPlaceHolder().textProperty
            //   .copy());

            /** figure out portion.bPlaceHolder's val */
            /** if <sdtStart> is not strictly followed by <sdtEnd>, bPlaceHolder is false */
            const nextNode = ParagraphReader.tGetTheFirstNonTextSiblingFollowed(i, paraChildren);
            // console.log(nextNode)
            // 11.3.2020: the next node MUST BE struct portion or PLACEHOLDER PORTION
            // console.log(paraChild)
            // console.log(nextNode)
            if (nextNode && nextNode.tagName === 'sdtEnd') {
              // consider compatibility of 'no placeholder portion' case
              bPlaceHolder = true;
              // bFirstSiblingOfSdtStart = false; // actually no use as of 11.4.2020

            } else {

              bPlaceHolder = false;
              if (nextNode) { // look out for null. still may be problematic
                // bFirstSiblingOfSdtStart = true;

                // if a struct has title + placeholder, still bPlaceHolder may be true
                if (nextNode.tagName === 'w:r') {
                  const potentialSdtEnd = paraChildren[i + 1 + 1];
                  let text = '';
                  for (const nextNodeChild of nextNode.children) {
                    if (typeof nextNodeChild === 'object' && nextNodeChild.tagName === 'w:t') {
                      text = safeDecodeURIComponent(nextNodeChild.children[0] as string, documentVersion);
                      break;
                    }
                  }
                  // console.log(text)
                  // console.log(lastNewControl.getTitle())
                  if (typeof potentialSdtEnd === 'object' && potentialSdtEnd.tagName === 'sdtEnd') {
                    // 1. old compatible: [ title ] 2. [ {normal portion} ] 3. new: [ placeholder ]
                    // 12.7.2020: not correct, can still be of [title rpr] pattern
                    if (lastNewControl.getTitle() === text) {
                      bPlaceHolder = true;
                    }
                  } else {
                    // [ title placeholder ]
                    // no need to deal with ph text props here, just override it
                  }
                }

              }
            }

            /** create new seperation portion for [ always */
            const newControlContent = lastNewControl.getNewControlContent();
            const newControlPlaceHolder = newControlContent.getPlaceHolder();
            const newControlStartBorderPortion = lastNewControl.getStartBorderPortion();
            newControlStartBorderPortion.textProperty = textProperty;

            if (bPlaceHolder) {
              // newControlPlaceHolder.textProperty = textProperty.copy();
              // placeholder always has default text prop - not any more, will override using showPlaceholder
              // here just set a default text props
              newControlPlaceHolder.textProperty = new TextProperty();
            }

            // if (bPlaceHolder) {
            // newControlStartBorderPortion.setPlaceHolder1(bPlaceHolder);

            // const newControlEndBorderPortion = lastNewControl.getEndBorderPortion();
            // newControlEndBorderPortion.setPlaceHolder1(bPlaceHolder);
            lastNewControl.setPlaceHolder(bPlaceHolder);
              // newControlPlaceHolder.setPlaceHolder1(bPlaceHolder);
            // } else {
            //   newControlStartBorderPortion.setPlaceHolder1(bPlaceHolder);
            //   // newControlPlaceHolder.setPlaceHolder1(bPlaceHolder);
            // }

            if (!nextNode) { // null. means at end of para
              newControlPlaceHolder.content = newControlStartBorderPortion.content; // R.W: not in use
            }

            para.addToContent(runCount, newControlStartBorderPortion);
            runCount++; // not matter

            unclosedNewControls.push(lastNewControl);

            /** set map in content control manager no matter what */
            // newControlManager.getNameMap()
            //   .set(lastNewControl.getNewControlName(), lastNewControl);
          }

        } else if (paraChild.tagName === 'sdtEnd') { /** sdtEnd */

          showPlaceholder = false;
          runCount = this.tTraverseSdtEnd(bInsertFile, document, paraChild, para, runCount, headerFooter,
            ContentControlDescObj);

        } else if ( 'w:ins' === paraChild.tagName || 'w:del' === paraChild.tagName ) {
          const attrs = paraChild.attributes;
          const revsionChildren = paraChild.children as rtNode[];
          const length = revsionChildren ? revsionChildren.length : 0;
          if ( attrs && 0 < length) {
            const userId = attrs['id'] ? attrs['id'] : null;
            const userName = attrs['author'] ? attrs['author'] : null;

            // TODO?
            const revisionManager = document.getRevisionsManager();
            const type = 'w:ins' === paraChild.tagName ? ReviewType.Add : ReviewType.Remove;
            const bLostInfo = (!userId && !userName);

            if (bLostInfo) {
              if (ReviewType.Remove === type) {
                continue;
              }
            }

            const reviewInfo = this.getReviewInfo(attrs, document, type);
            if (unclosedNewControls && unclosedNewControls[unclosedNewControls.length - 1]?.isNewDateBox()
              && ReviewType.Add === type && !reviewInfo['firstDate'] && !reviewInfo.getSavedRecord()) {
                reviewInfo['bFirstSet'] = true;
            }

            // const controlObj = {bFirstSiblingOfSdtStart, unclosedNewControls, bFollowedBySdtEnd};
            const controlObj = { unclosedNewControls };
            for (let index2 = 0; index2 < length; index2++) {
              const runChild = revsionChildren[index2];

              RunReader.tTraverseRun(runChild, para, runCount, controlObj, uniqueImagelist, bInsertFile, document,
                headerFooter);

              if (!bLostInfo) {
                para.content[runCount].setReviewTypeWithInfo(type, reviewInfo.copy());
                revisionManager.addRevisionByReviewInfo(para, reviewInfo);
              }
              runCount++;
            }

            // if ( 0 === length ) {
            //   para.content[para.content.length - 1].setReviewTypeWithInfo(type, reviewInfo);
            //   runCount++;
            // }
          }
        }
      }

    } // for

    para.setParaEndProperty();

    if (!bInsertFile) { // open file

      if (headerFooter == null) {
        if (container == null) {
          document.addToContent(document.content.length, para);
        } else {
          // add this para to region
          if (!skipAddCurPara) {
            container.addToContent(container.getContent().length, para);
          }
        }
      } else {
        // header footer
        const headerFooterOperateContent = headerFooter.content;
        const headerFooterContent = headerFooterOperateContent.content;
        // remove the default empty para if not populated yet
        // if (headerFooterContent.length === 1 && headerFooterContent[0].isEmpty() === true) { // only exist empty para
        //   // headerFooterContent = [];
        //   headerFooterContent.splice(headerFooterContent.length - 1, 1);
        // }

        if (container == null) {
          headerFooterOperateContent.addToContent(headerFooterContent.length, para);
        } else {
          container.addToContent(container.getContent().length, para);
        }

        // console.log(headerFooter)
      }

    } else { // insert file
      // console.dir(node)
      if (container == null) {
        paras.push(para);
        const nodesLen = nodes.length;
        if (nodesLen > 0) {
          const lastRootNode = nodes[nodesLen - 1];
          if (typeof lastRootNode === 'object' && lastRootNode.tagName === 'w:sectPr' &&
          index === nodesLen - 2) {
            // TODO: always same lvl as sectpr? no! headerfooter insert file with region
            // add empty paras when required
            if (!bNoEndPara) {
              for (let i = 0; i < INSERT_FILE_END_EMPTY_PARA + 1; i++) {
                paras.push(new Paragraph(document, document));
              }
            }

            // console.log(paras)
            document.addPasteContent(paras, false);
          }
        }
      } else {
        // add this para to region
        // container.addToContent(container.getContent().length, para);
        paras.push(para);
        const nodesLen = nodes.length;
        if (nodesLen > 0) {
          const lastRootNode = nodes[nodesLen - 1];
          if (typeof lastRootNode === 'object' && lastRootNode.tagName === 'w:sectPr' &&
            index === nodesLen - 2) {
            // add empty paras when required
            if (!bNoEndPara) {
              for (let i = 0; i < INSERT_FILE_END_EMPTY_PARA + 1; i++) {
                paras.push(new Paragraph(document, document));
              }
            }

            // console.log(paras)
            document.addPasteContent(paras, false);
          }
        }
      }

    }

  } // traverseP

  /**
   * txml traverse para
   * @param container Region/Table. DocumentContent is within table cell
   * @param index node index
   * @param nodes root node collection
   * @param bAddRegionToParas 1. insert, parent is region 2. if parent region needs to be added to Paras
   */
  public static tTraverseParagraph2(node: rtNode, document: Document, ContentControlDescObj: IContentControlDescObj,
                                    uniqueImagelist: Map<string, IUniqueImageProps>, bInsertFile: boolean = false,
                                    paras: DocumentContentElementBase[],
                                    container: Region | TableCell = null, headerFooter: HeaderFooter = null,
                                    modeFonts: IModeFonts = null, sectionEnds: any[] = [],
                                    bAddRegionToParas: boolean = true, insertRegionNames: string[] = []): void {

    let para = null;
    let skipAddCurPara = false;
    const documentVersion = document != null ? document.getDocumentVersion() : 0;
    const readNewControls = {};
    if (container == null) {
      para = new Paragraph(document, document);
      if (bInsertFile === false && headerFooter) {
        para = new Paragraph(headerFooter.getContent(), document);
      }
    } else {
      // default
      para = new Paragraph(container.getOperateContent(), document);

      // inserting file also needs to think about region recursion
      // if it's a region with title, the first para is already created
      const containerContent = container.getContent();
      if (containerContent.length === 1 && container instanceof Region) {
        // this condition should only enter once
        // Attention: if 1st para in region is para with ONLY title portion, the condition would always suffice
        // thus should develop a way to remedy that
        let firstTextNode: rtNode = null;
        let firstTextPr = null;
        for (const nodeChild of node.children) {
          // first find w:r then get first w:t
          // TODO: may be danger
          if (typeof nodeChild === 'object' && nodeChild.tagName === 'w:r') {
            for (const runChild of nodeChild.children) {
              if (typeof runChild === 'object' && runChild.tagName === 'w:rPr') {
                firstTextPr = new TextProperty(getDefaultFont(document));
                RunReader.tTraverseRunProperty(runChild, firstTextPr, document);
              }
              if (typeof runChild === 'object' && runChild.tagName === 'w:t') {
                firstTextNode = runChild;
                break;
              }
            }
          }
          if (firstTextNode != null) {
            break;
          }
        }

        let firstText = '';
        if (firstTextNode != null) {
          const tempText = firstTextNode.children[0];
          firstText = (typeof tempText === 'string') ? safeDecodeURIComponent(tempText, documentVersion) : firstText;
        }

        // when traversing 2nd para, the first w:t must never be title portion
        // make sure only the 1st para enter this
        if (firstText === container.getTitle()) {
          const thePara = containerContent[0];
          const theParaContent = thePara.getContent();
          // iff case: 1 title portion, 1 end portion
          // tslint:disable-next-line: max-line-length
          if (theParaContent && theParaContent.length === 2 && theParaContent[0] instanceof ParaPortion && theParaContent[1].isParaEndPortion()) {
            const thePortion = theParaContent[0];
            if (thePortion.getTextContent() === container.getTitle()) {
              para = thePara;
              skipAddCurPara = true;
              container.setTitlePortionTextProperty2(firstTextPr);
            }
          }
        }
      }
    }

    para.bReadFile = true;

    const paraChildren = node.children as rtNode[];

    // death stranding
    let { newControlManager } = ContentControlDescObj;
    const { newControlProperty, unclosedNewControls, strcuts } = ContentControlDescObj;

    // these three vars should be meaningless if cross paragraph
    // let bFollowedBySdtEnd = false; // just related to w:r
    // let bFirstSiblingOfSdtStart = false; // just related to w:r
    let showPlaceholder = false; // if sdtstart has <showPlaceholder>, locate & save placeholder textprops

    let runCount = 0;
    // const domParser = new DOMParser();
    const newControlNamesTempArr = []; // used at inserting file
    let numPr;
    const commentManager = document.getCommentManager();
    for (let i = 0; i < paraChildren.length; i++) {
      const paraChild = paraChildren[i];
      if (typeof paraChild === 'object') {
        const pTagName = paraChild.tagName;
        /** w:pPr */
        if (pTagName === 'w:numPr') {
          numPr = {};
          for (const nodeChild of paraChild.children) {
            if (typeof nodeChild !== 'object') {
              continue;
            }
            if (nodeChild.tagName === 'numType') {
              numPr.type = +nodeChild.children[0];
            } else if (nodeChild.tagName === 'value') {
              numPr.numId = nodeChild.children[0];
            }
          }
        } else if (pTagName === 'w:pPr') {
          ParagraphReader.parseParaProperty(para, paraChild);
        } else if (pTagName === 'w:r') { /** w:r */
          // console.log(paraChild)
          // pay attention if it's i or index!
          // const nextNode = ParagraphReader.tGetTheFirstNonTextSiblingFollowed(i, paraChildren);
          // // console.log(nextNode)
          // if (nextNode && nextNode.tagName === 'sdtEnd') {
          //   bFollowedBySdtEnd = true;
          // }

          // const controlObj = {bFirstSiblingOfSdtStart, unclosedNewControls, bFollowedBySdtEnd};
          const controlObj = { unclosedNewControls };

          // if it's title portion/placeholder, ignore it
          const titlePortionType = this.tIsTitlePortion(paraChild, controlObj, container, i, document, paraChildren, i, false, documentVersion);
          if (titlePortionType === TitlePortionType.None) {
            if (this.tIsPlaceholderPortion(showPlaceholder, paraChild, controlObj, documentVersion) === false) {
              // showPlaceholder must not be true
              showPlaceholder = false;
              RunReader.tTraverseRun(paraChild, para, runCount, controlObj, uniqueImagelist, bInsertFile, document,
                headerFooter, false, modeFonts);
              runCount++;
            } else {
              const phPortion = RunReader.tTraverseRun(paraChild, para, runCount, controlObj, uniqueImagelist,
                bInsertFile, document, headerFooter, showPlaceholder, modeFonts);
              this.setPlaceholderTextProperty(phPortion, controlObj);
            }
          } else if (titlePortionType === TitlePortionType.Region) {
            // title portion in region is not like in struct
            runCount++;
          }

          // console.log(circularParse(circularStringify(container.getContent())));
          // bFirstSiblingOfSdtStart = bFirstSiblingOfSdtStart ? false : bFirstSiblingOfSdtStart;
          // bFollowedBySdtEnd = bFollowedBySdtEnd ? false : bFollowedBySdtEnd;
        } else if (pTagName === 'sdt') { /* new sdt */
          runCount = this.tTraverseSdt(bInsertFile, document, paraChild,
            newControlNamesTempArr, para, runCount, container, i,
            uniqueImagelist, headerFooter, ContentControlDescObj, modeFonts, documentVersion);
          showPlaceholder = false;

        } else if (pTagName === 'sectionStart') { /** content control */
          let nameAttr = paraChild.attributes['name'];
          if (ContentControlDescObj.bHeaderFooter === true) {
            nameAttr = newControlManager.makeUniqueName(+paraChild.attributes['type'], nameAttr)
          }
          if (strcuts.start[nameAttr]) {
            console.warn('this is start border is error: ' + nameAttr);
            continue;
          }
          if (ParagraphReader.shouldReadContentControl((paraChild as IParseXmlNode).type === undefined && bInsertFile,
              document, paraChild.attributes['type'])
            && this.isSectionEndTupleExisted(paraChild, sectionEnds)) {
            // let runChild = paraChild.childNodes[0];
            const runChildren = paraChild.children;
            let borderStringStart = '';
            let bPlaceHolder = false; // this prop is in portion

            if (nameAttr) {
              newControlProperty.newControlName = nameAttr;

              // check duplicate
              try {
                newControlManager = document.getNewControlManager();
                if (!newControlManager.checkNewControlName(newControlProperty.newControlName)) {
                  if (!bInsertFile) {
                    throw new Error('find duplicate control name out of inserting file');
                  } else {
                    // when inserting file, also check the names which not set in newcontrolnames map yet
                    // if (newControlNamesTempArr.includes(newControlProperty.newControlName)) {
                    //   newControlProperty.newControlName = newControlManager.makeUniqueNameByInsertFile(
                    //     newControlProperty.newControlType, newControlNamesTempArr, newControlProperty.newControlName);
                    //   newControlNamesTempArr.push(newControlProperty.newControlName);
                    // } else {
                    //   // indicate from insert file
                    //   newControlProperty.bInsertFile = true;
                    // }
                  }
                }
              } catch (error) {
                window.dispatchEvent(new ErrorEvent('error', { message: ErrorMessages.XmlCorrupted, error }));
              }
            }

            if (paraChild.attributes['type']) {
              readNewControls[nameAttr] = new ReadStruct(newControlProperty);
              // newControlProperty.newControlType = Number(paraChild.attributes.getNamedItem('type').nodeValue);
              newControlProperty.newControlType = +paraChild.attributes['type'];
              // TODO: section shouldnt be sign box itself
            }
            strcuts.start[nameAttr] = true;
            let textProperty: TextProperty = null;
            for (const runChild of runChildren) {
              if (typeof runChild === 'object') {
                if ('w:rPr' === runChild.tagName) {
                  textProperty = new TextProperty(getDefaultFont(document));
                  RunReader.tTraverseRunProperty(runChild, textProperty, document);
                  // } else if (runChild.nodeName !== '#text') {
                } else {
                  const textNode = runChild.children[0];
                  // if (StructXmlName.Sdtcontent === runChild.tagName) {
                  //   sdtContentNode = runChild;
                  //   continue;
                  // }

                  if (StructXmlName.ShowPlaceholder === runChild.tagName) {
                    if (textNode === '1') {
                      showPlaceholder = true;
                    }
                    continue;
                  }
                  if (StructXmlName.BorderString === runChild.tagName) {
                    if (textNode && typeof textNode === 'string' ) {
                      borderStringStart = safeDecodeURIComponent(textNode as any, documentVersion);
                    }

                    continue;
                  }
                  // console.log(textNode)
                  readNewControls[nameAttr].readProp(textNode, documentVersion, runChild);
                }
              }

            } // end for

            // console.log(newControlProperty);
            // console.log(circularParse(circularStringify(newControlProperty)));

            if (null == textProperty) {
              textProperty = new TextProperty(getDefaultFont(document));
            }

            newControlManager = document.getNewControlManager();
            // control with duplicate name should already be handled
            let lastNewControl = newControlManager.createNewControl(para, newControlProperty);
            if (lastNewControl == null) {
              // tslint:disable-next-line: no-console
              // console.warn('duplicate control name detected, should be fixed automatically:)');
              const source = newControlProperty.bInsertFile ? FileSource.InsertFile : null;
              newControlProperty.newControlName = newControlManager.makeUniqueName(newControlProperty.newControlType,
                newControlProperty.newControlName, source);
              lastNewControl = newControlManager.createNewControl(para, newControlProperty);
            }
            // when insert file, have a temp controlNames array
            if (bInsertFile) {
              if (newControlNamesTempArr.length === 0) {
                // if newControlNames is not empty, assign value to temp array
                const newControlNamesMap = newControlManager.getNameMap();
                if (newControlNamesMap.size !== 0) {
                  for (const key of newControlNamesMap.keys()) {
                    newControlNamesTempArr.push(key);
                  }
                }
              }

              if (false === newControlNamesTempArr.includes(lastNewControl.getNewControlName())) {
                newControlNamesTempArr.push(lastNewControl.getNewControlName());
              }
            }

            // reset newControlProperty
            this.resetNewControlProperty(newControlProperty);

            // lastNewControl.initPlaceHolderDefaultTextProperty(
            //   lastNewControl.getNewControlContent()
            //   .getPlaceHolder().textProperty
            //   .copy());

            /** figure out portion.bPlaceHolder's val */
            /** if <sdtStart> is not strictly followed by <sdtEnd>, bPlaceHolder is false */
            const nextNode = ParagraphReader.tGetTheFirstNonTextSiblingFollowed(i, paraChildren);
            // console.log(nextNode)
            // 11.3.2020: the next node MUST BE struct portion or PLACEHOLDER PORTION
            // console.log(paraChild)
            // console.log(nextNode)
            if (nextNode && nextNode.tagName === 'sdtEnd') { // TODO, seems not problematic:('sectionEnd')
              // consider compatibility of 'no placeholder portion' case
              // bFirstSiblingOfSdtStart = false; // actually no use as of 11.4.2020

            } else {

              bPlaceHolder = false;
              if (nextNode) { // look out for null. still may be problematic
                // bFirstSiblingOfSdtStart = true;

                // if a struct has title + placeholder, still bPlaceHolder may be true
                if (nextNode.tagName === 'w:r') {
                  const potentialSdtEnd = paraChildren[i + 1 + 1];
                  let text = '';
                  for (const nextNodeChild of nextNode.children) {
                    if (typeof nextNodeChild === 'object' && nextNodeChild.tagName === 'w:t') {
                      text = safeDecodeURIComponent(nextNodeChild.children[0] as string, documentVersion);
                      break;
                    }
                  }
                  // console.log(text)
                  // console.log(lastNewControl.getTitle())
                  if (typeof potentialSdtEnd === 'object' && potentialSdtEnd.tagName === 'sdtEnd') {
                    // 1. old compatible: [ title ] 2. [ {normal portion} ] 3. new: [ placeholder ]
                    // 12.7.2020: not correct, can still be of [title rpr] pattern
                    if (lastNewControl.getTitle() === text) {
                      bPlaceHolder = true;
                    }
                  } else {
                    // [ title placeholder ]
                    // no need to deal with ph text props here, just override it
                  }
                }

              }
            }

            /** create new seperation portion for [ always */
            const newControlContent = lastNewControl.getNewControlContent();
            const newControlPlaceHolder = newControlContent.getPlaceHolder();
            const newControlStartBorderPortion = lastNewControl.getStartBorderPortion();
            newControlStartBorderPortion.textProperty = textProperty;

            if (bPlaceHolder) {
              // newControlPlaceHolder.textProperty = textProperty.copy();
              // placeholder always has default text prop - not any more, will override using showPlaceholder
              // here just set a default text props
              newControlPlaceHolder.textProperty = new TextProperty(getDefaultFont(document));
            }

            // if (bPlaceHolder) {
            // newControlStartBorderPortion.setPlaceHolder1(bPlaceHolder);

            // const newControlEndBorderPortion = lastNewControl.getEndBorderPortion();
            // newControlEndBorderPortion.setPlaceHolder1(bPlaceHolder);
            lastNewControl.setPlaceHolder(bPlaceHolder);
            // newControlPlaceHolder.setPlaceHolder1(bPlaceHolder);
            // } else {
            //   newControlStartBorderPortion.setPlaceHolder1(bPlaceHolder);
            //   // newControlPlaceHolder.setPlaceHolder1(bPlaceHolder);
            // }

            if (!nextNode) { // null. means at end of para
              newControlPlaceHolder.content = newControlStartBorderPortion.content; // R.W: not in use
            }

            para.addToContent(runCount, newControlStartBorderPortion);
            runCount++; // not matter

            unclosedNewControls.push(lastNewControl);

            /** set map in content control manager no matter what */
            // newControlManager.getNameMap()
            //   .set(lastNewControl.getNewControlName(), lastNewControl);
          }

        } else if (pTagName === 'sectionEnd') { /** sdtEnd */
          // 剔除已有的结构化元素
          const newControlName = paraChild.attributes['name'];
          if (strcuts.end[newControlName]) {
            console.warn('this is end border is error: ' + newControlName);
            continue;
          }
          showPlaceholder = false;
          if (ParagraphReader.shouldReadContentControl((paraChild as IParseXmlNode).type === undefined && bInsertFile,
            document, paraChild.attributes['type']) && unclosedNewControls.length > 0) {
            // const newControlName = paraChild.attributes.getNamedItem('name').nodeValue;
            // const currentControl = newControlManager.getNameMap()
            //   .get(newControlName);

            // check grasped sectionstart's validity - no need, already checked unclosedNewControls length
            const currentControl = unclosedNewControls[unclosedNewControls.length - 1];
            const newControlStartBorderPortion = currentControl.getStartBorderPortion();
            let textProperty: TextProperty = null;
            strcuts.end[newControlName] = true;
            if (newControlStartBorderPortion.bPlaceHolder) { // find current control's bPlaceholder
              // as of 7/8/2020 only startBorderPortion.bPlaceHolder stays updated
              para.addToContent(runCount, currentControl.getNewControlContent()
                .getPlaceHolder());
              runCount++;

            }
            // else if (i === 0 || (i === 1 && paraChild.previousSibling.nodeName === '#text')) {
            //   /** sdtEnd is at start of para. It may be 0 or 1 based on formatted or not */
            //   // put ] in portion and add to para
            //   // const currentControlContent = currentControl.getNewControlContent();
            //   // const newControlEndBorder = currentControlContent.getEndBorder();

            //   // const portion = new ParaPortion(para);
            //   // portion.addToContent(0, newControlEndBorder);

            //   // para.addToContent(runCount, portion);
            //   // runCount++;

            //   // unclosedNewControls.pop();
            // }

            const paraChildChildren = paraChild.children;
            if (paraChildChildren && 0 < paraChildChildren.length) {
              const nodeChildren = paraChild.children;
              let bSuccess = false;

              for (const nodeChild of nodeChildren) {
                if (typeof nodeChild === 'object') {
                  switch (nodeChild.tagName) {
                    case 'w:rPr':
                      textProperty = new TextProperty(getDefaultFont(document));
                      // RunReader.traverseRunProperty(paraChild.childNodes[0], textProperty);
                      RunReader.tTraverseRunProperty(nodeChild, textProperty, document);
                      bSuccess = true;
                      break;
                  }

                  if (bSuccess) {
                    break;
                  }
                }

              }
            }

            if (null == textProperty) {
              textProperty = new TextProperty(getDefaultFont(document));
            }

            /** create new seperation portion for ] always */
            const newControlEndBorderPortion = currentControl.getEndBorderPortion();
            newControlEndBorderPortion.textProperty = textProperty;
            para.addToContent(runCount, newControlEndBorderPortion); // will also set portion's parent!
            runCount++;
            if (currentControl.isHidden() && !bInsertFile) {
              currentControl.getNewControlContent().setHidden(true, currentControl.isNewSection());
            }

            unclosedNewControls.pop();

            /** if it's control check/radiobutton, need extra step to fill in inner content */
            if (currentControl instanceof NewControlCheck || currentControl instanceof NewControlRadio) {
              currentControl.addContent(); // lsy's method
            }

            /** set map in content control manager for any struct */
            // newControlNames[]  -> all structs
            if (!bInsertFile) {
              newControlManager.getNameMap()
                .set(currentControl.getNewControlName(), currentControl);
            }

            if (unclosedNewControls.length === 0) { // root
              if (!bInsertFile) {
                // if it's a root content control, add to "root content control array"(map already added in sdtstart)
                // newControls[]  -> root structs
                // NOTE the order of elements in the array SHOULD indicate structs appearance in doc
                if (headerFooter == null) {
                  newControlManager.getAllNewControls()
                    .splice(newControlManager.getAllNewControls().length, 0, currentControl);
                } else {
                  if (headerFooter.isHeader()) {
                    const structCount = ContentControlDescObj.headerStructCount;
                    // console.log(structCount)
                    newControlManager.getAllNewControls()
                      .splice(structCount, 0, currentControl);
                    ContentControlDescObj.headerStructCount++;
                  } else {
                    // footer
                    newControlManager.getAllNewControls()
                      .splice(newControlManager.getAllNewControls().length, 0, currentControl);
                  }
                }
              } else {
                // pasteControl need to fill up when insertFile
                // control order is important
                let contentPos: any;
                if ((paraChild as IParseXmlNode).type === undefined) {
                  contentPos = document.getCurContentPosInDoc(false, true);
                }
                newControlManager.addPasteControl({ control: currentControl, contentPos });
                // TODO?
              }

            } else {
              // 非根节点时，插入文件需set name map
              if (bInsertFile) {
                // newControlManager.getNameMap()
                // .set(currentControl.getNewControlName(), currentControl);
                newControlManager.addNewControlInMap(currentControl);
              }
              // add to leafList. two-way
              const lastElemInUnclosedNewControls = unclosedNewControls[unclosedNewControls.length - 1];
              currentControl.setParent(lastElemInUnclosedNewControls);
              lastElemInUnclosedNewControls.getLeafList()
                .push(currentControl);
            }
          }

        } else if ('w:ins' === pTagName || 'w:del' === pTagName) {
          const attrs = paraChild.attributes;
          const revsionChildren = paraChild.children as rtNode[];
          const length = revsionChildren ? revsionChildren.length : 0;
          if (length === 1 && revsionChildren[0] as any === '') {
            continue;
          }

          if (attrs && 0 < length) {
            const type = ('w:ins' === pTagName ? ReviewType.Add : ReviewType.Remove);
            const reviewInfo = this.getReviewInfo(attrs, document, type);
            const revisionManager = document.getRevisionsManager();
            // const controlObj = {bFirstSiblingOfSdtStart, unclosedNewControls, bFollowedBySdtEnd};
            const controlObj = { unclosedNewControls };
            const userId = attrs['id'] ? attrs['id'] : null;
            const userName = attrs['author'] ? attrs['author'] : null;
            const bLostInfo = (!userId && !userName);

            if (bLostInfo) {
              if (ReviewType.Remove === type) {
                continue;
              }
            }

            if (unclosedNewControls && unclosedNewControls[unclosedNewControls.length - 1]?.isNewDateBox()
              && ReviewType.Add === type && !reviewInfo['firstDate'] && !reviewInfo.getSavedRecord()) {
                reviewInfo['bFirstSet'] = true;
            }

            for (let index = 0; index < length; index++) {
              const runChild = revsionChildren[index];
              if ('w:ins' === runChild.tagName && 'w:del' === pTagName) {
                const secondRevInfo = this.getReviewInfo(runChild.attributes, document);
                const secondSetting = secondRevInfo.getRevisionSetting();
                const reviewSetting = reviewInfo.getRevisionSetting();

                reviewInfo.setRevisionSetting(secondSetting);
                reviewInfo.setDeleteInfo(secondRevInfo);
                secondRevInfo.setRevisionSetting(reviewSetting);
                continue;
              }
              if (!runChild || runChild.children == null) {
                continue;
              }
              RunReader.tTraverseRun(runChild, para, runCount, controlObj, uniqueImagelist, bInsertFile, document,
                headerFooter, false, modeFonts);

              if (!this.checkPortionRevisionMerge(para, runCount, type, reviewInfo)) {
                if (!bLostInfo) {
                  para.content[runCount].setReviewTypeWithInfo(type, reviewInfo.copy());
                  revisionManager.addRevisionByReviewInfo(para, reviewInfo);
                }
                runCount++;
              } else {
                const portion = para.content[runCount];
                para.content[runCount - 1].concatToContent(portion.content);
                para.content.splice(runCount, 1);
                para.curPos.contentPos = Math.max(0, --para.curPos.contentPos);
              }
            }

            // if (0 === length) {
            //   para.content[para.content.length - 1].setReviewTypeWithInfo(type, reviewInfo);
            //   runCount++;
            // }
          }
        } else if (pTagName === 'w:comment') {
            const startAttr = paraChild.attributes['start'];
            const idAttr = paraChild.attributes['id'];
            if (paraChild.children && startAttr && idAttr) {
                let commentPortion: ParaPortion;
                let commentDataNode: rtNode;
                for (const child of paraChild.children) {
                    if (typeof child === 'object') {
                        if (child.tagName === 'w:r') {
                            const controlObj = { unclosedNewControls };
                            commentPortion = RunReader.tTraverseRun(paraChild, para, runCount++,
                                controlObj, uniqueImagelist,
                                bInsertFile, document, headerFooter,
                                false, modeFonts);
                        } else if (child.tagName === 'w:reply') {
                            commentDataNode = child;
                        }
                    }
                }
                if (commentPortion) {
                    const bStart = startAttr === '1';
                    const comment = commentManager.prepareReadedComment(idAttr, commentPortion, bStart);
                    if (comment) {
                        commentPortion.addComment(bStart, comment.getId());
                        if (bStart && commentDataNode) {
                            this.tTraverseComment(commentDataNode, comment);
                        }
                    }
                }
            }
        }
      }

    } // for
    

    para.setParaEndProperty();

    if (!bInsertFile) { // open file

      if (headerFooter == null) {
        if (numPr) { // 项目符合进行添加
          const manager = document.getNumManager();
          manager.addDocNum(para, numPr.numId, numPr.type);
        }
        if (container == null) {
          document.addToContent(document.content.length, para);
        } else {
          // add this para to region
          if (!skipAddCurPara) {
            container.addToContent(container.getContent().length, para);
          }
        }
      } else {
        // header footer
        const headerFooterOperateContent = headerFooter.content;
        const headerFooterContent = headerFooterOperateContent.content;
        // remove the default empty para if not populated yet
        // if (headerFooterContent.length === 1 && headerFooterContent[0].isEmpty() === true) { // only exist empty para
        //   // headerFooterContent = [];
        //   headerFooterContent.splice(headerFooterContent.length - 1, 1);
        // }

        if (container == null) {
          headerFooterOperateContent.addToContent(headerFooterContent.length, para);
        } else {
          container.addToContent(container.getContent().length, para);
        }

        // console.log(headerFooter)
      }

    } else { // insert file
      // console.dir(node)
      if (container == null) {
        paras.push(para);
      } else { // has container
        if ((node as IParseXmlNode).parentNode) { // from replace()
          const contents = container.getContent();
          if (contents[0] !== para) {
            container.addToContent(contents.length, para);
          }
        } else if (!skipAddCurPara) { // not region title paragraph -> 1st para is not pre-existent
          // if ((container instanceof Region && container.index < 0) ||
          if (this.isNewlyInsertedRegion(container, insertRegionNames) === true ||
            container instanceof TableCell) { // table or newly created region
            // TODO: in region case, is container.index < 0 safe?
            // No! [Region] vs [Region, Paragraph]: (typical case: saveSelectAreaToStream())
            // If the prior case, index will be 0 after first child para is traversed in recalculate()

            container.addToContent(container.getContent().length, para);

            if (container instanceof Region) {
              // check if paras already has such region
              let regionCanAddToParas = true;
              for (const tempPara of paras) {
                if (tempPara instanceof Region) {
                  if (tempPara.getName() === container.getName()) {
                    regionCanAddToParas = false;
                    break;
                  }
                  // bInsertFromRegion === false
                }
              }
              if (regionCanAddToParas === true) {
                // bAddRegionToParas: if not inserted from region, and the para is in depth 2 region -> false
                // in this case, the region shouldn't be added to paras
                if (bAddRegionToParas === true) {
                  this.addRegionToParas(paras, container, document, insertRegionNames);
                }
              }
            }

          } else { // file must be inserted from the container
            paras.push(para);
          }

          return;
        } else { // region title paragraph
          if (container instanceof Region === false) {
            // tslint:disable-next-line: no-console
            // console.warn('skipAddCurPara is not with region');
          } else if (container instanceof Region) {
            if (bAddRegionToParas === true) { // check the var's def
              // this is the first para in region. The region mustn't be added yet
              this.addRegionToParas(paras, container, document, insertRegionNames);
            }
          }
        }
      }

    }

  } // traverseP

  /**
   * txml traverse para: section
   * @param container Region/Table. DocumentContent is within table cell
   * @param index node index
   * @param nodes root node collection
   * @param bAddRegionToParas 1. insert, parent is region 2. if parent region needs to be added to Paras
   */
  public static tTraverseParagraphForSec(node: rtNode, document: Document, ContentControlDescObj: IContentControlDescObj,
                                    uniqueImagelist: Map<string, IUniqueImageProps>, bInsertFile: boolean = false,
                                    paras: DocumentContentElementBase[],
                                    container: Region | TableCell = null, headerFooter: HeaderFooter = null,
                                    modeFonts: IModeFonts = null, bAddRegionToParas: boolean = true
  ): void {

    let para = null;
    let skipAddCurPara = false;
    const documentVersion = document != null ? document.getDocumentVersion() : 0;
    // const readNewControls = {};
    if (container == null) {
      para = new Paragraph(document, document);
      if (bInsertFile === false && headerFooter) {
        para = new Paragraph(headerFooter.getContent(), document);
      }
    } else {
      // default
      para = new Paragraph(container.getOperateContent(), document);

      // inserting file also needs to think about region recursion
      // if it's a region with title, the first para is already created
      const containerContent = container.getContent();
      if (containerContent.length === 1 && container instanceof Region) {
        // this condition should only enter once
        // Attention: if 1st para in region is para with ONLY title portion, the condition would always suffice
        // thus should develop a way to remedy that
        let firstTextNode: rtNode = null;
        let firstTextPr = null;
        for (const nodeChild of node.children) {
          // first find w:r then get first w:t
          // TODO: may be danger
          if (typeof nodeChild === 'object' && nodeChild.tagName === 'w:r') {
            for (const runChild of nodeChild.children) {
              if (typeof runChild === 'object' && runChild.tagName === 'w:rPr') {
                firstTextPr = new TextProperty(getDefaultFont(document));
                RunReader.tTraverseRunProperty(runChild, firstTextPr, document);
              }
              if (typeof runChild === 'object' && runChild.tagName === 'w:t') {
                firstTextNode = runChild;
                break;
              }
            }
          }
          if (firstTextNode != null) {
            break;
          }
        }

        let firstText = '';
        if (firstTextNode != null) {
          const tempText = firstTextNode.children[0];
          firstText = (typeof tempText === 'string') ? safeDecodeURIComponent(tempText, documentVersion) : firstText;
        }

        // when traversing 2nd para, the first w:t must never be title portion
        // make sure only the 1st para enter this
        if (firstText === container.getTitle()) {
          const thePara = containerContent[0];
          const theParaContent = thePara.getContent();
          // iff case: 1 title portion, 1 end portion
          // tslint:disable-next-line: max-line-length
          if (theParaContent && theParaContent.length === 2 && theParaContent[0] instanceof ParaPortion && theParaContent[1].isParaEndPortion()) {
            const thePortion = theParaContent[0];
            if (thePortion.getTextContent() === container.getTitle()) {
              para = thePara;
              skipAddCurPara = true;
              container.setTitlePortionTextProperty2(firstTextPr);
            }
          }
        }
      }
    }

    const paraChildren = node.children as rtNode[];

    // death stranding
    // let { newControlManager } = ContentControlDescObj;
    const { unclosedNewControls, buttons } = ContentControlDescObj;

    // these three vars should be meaningless if cross paragraph
    // let bFollowedBySdtEnd = false; // just related to w:r
    // let bFirstSiblingOfSdtStart = false; // just related to w:r
    let showPlaceholder = false; // if sdtstart has <showPlaceholder>, locate & save placeholder textprops

    let runCount = 0;
    // const domParser = new DOMParser();
    const newControlNamesTempArr = []; // used at inserting file
    let numPr;
    const commentManager = document.getCommentManager();
    for (let i = 0; i < paraChildren.length; i++) {
      const paraChild = paraChildren[i];
      if (typeof paraChild === 'object') {
        const pTagName = paraChild.tagName;
        /** w:pPr */
        if (pTagName === 'w:numPr') {
          numPr = {};
          for (const nodeChild of paraChild.children) {
            if (typeof nodeChild !== 'object') {
              continue;
            }
            if (nodeChild.tagName === 'numType') {
              numPr.type = +nodeChild.children[0];
            } else if (nodeChild.tagName === 'value') {
              numPr.numId = nodeChild.children[0];
            }
          }
        } else if (pTagName === 'w:pPr') {
          ParagraphReader.parseParaProperty(para, paraChild);
        } else if (pTagName === 'w:r') { /** w:r */
          // console.log(paraChild)
          // pay attention if it's i or index!
          // const nextNode = ParagraphReader.tGetTheFirstNonTextSiblingFollowed(i, paraChildren);
          // // console.log(nextNode)
          // if (nextNode && nextNode.tagName === 'sdtEnd') {
          //   bFollowedBySdtEnd = true;
          // }

          // const controlObj = {bFirstSiblingOfSdtStart, unclosedNewControls, bFollowedBySdtEnd};
          const controlObj = { unclosedNewControls,  buttons};

          // if it's title portion/placeholder, ignore it
          const titlePortionType = this.tIsTitlePortion(paraChild, controlObj, container, i, document, paraChildren, i, false, documentVersion);
          if (titlePortionType === TitlePortionType.None) {
            if (this.tIsPlaceholderPortion(showPlaceholder, paraChild, controlObj, documentVersion) === false) {
              // showPlaceholder must not be true
              showPlaceholder = false;
              RunReader.tTraverseRun(paraChild, para, runCount, controlObj, uniqueImagelist, bInsertFile, document,
                headerFooter, false, modeFonts);
              runCount++;
            } else {
              const phPortion = RunReader.tTraverseRun(paraChild, para, runCount, controlObj, uniqueImagelist,
                bInsertFile, document, headerFooter, showPlaceholder, modeFonts);
              this.setPlaceholderTextProperty(phPortion, controlObj);
            }
          } else if (titlePortionType === TitlePortionType.Region) {
            // title portion in region is not like in struct
            runCount++;
          }

          // console.log(circularParse(circularStringify(container.getContent())));
          // bFirstSiblingOfSdtStart = bFirstSiblingOfSdtStart ? false : bFirstSiblingOfSdtStart;
          // bFollowedBySdtEnd = bFollowedBySdtEnd ? false : bFollowedBySdtEnd;
        } else if (pTagName === 'sdt') { /* new sdt */
          runCount = this.tTraverseSdt(bInsertFile, document, paraChild,
            newControlNamesTempArr, para, runCount, container, i,
            uniqueImagelist, headerFooter, ContentControlDescObj, modeFonts, documentVersion);
          showPlaceholder = false;

        } else if (pTagName === 'section') { /* new section */
        runCount = this.tTraverseSection(bInsertFile, document, paraChild,
          newControlNamesTempArr, para, runCount, container, i,
          uniqueImagelist, headerFooter, ContentControlDescObj, modeFonts, documentVersion);
        showPlaceholder = false;

        } else if ('w:ins' === pTagName || 'w:del' === pTagName) {
          const attrs = paraChild.attributes;
          const revsionChildren = paraChild.children as rtNode[];
          const length = revsionChildren ? revsionChildren.length : 0;
          if (length === 1 && revsionChildren[0] as any === '') {
            continue;
          }

          if (attrs && 0 < length) {
            const type = ('w:ins' === pTagName ? ReviewType.Add : ReviewType.Remove);
            const reviewInfo = this.getReviewInfo(attrs, document, type);
            const revisionManager = document.getRevisionsManager();
            // const controlObj = {bFirstSiblingOfSdtStart, unclosedNewControls, bFollowedBySdtEnd};
            const controlObj = { unclosedNewControls };
            const userId = attrs['id'] ? attrs['id'] : null;
            const userName = attrs['author'] ? attrs['author'] : null;
            const bLostInfo = (!userId && !userName);

            if (bLostInfo) {
              if (ReviewType.Remove === type) {
                continue;
              }
            }

            if (unclosedNewControls && unclosedNewControls[unclosedNewControls.length - 1]?.isNewDateBox()
              && ReviewType.Add === type && !reviewInfo['firstDate'] && !reviewInfo.getSavedRecord()) {
                reviewInfo['bFirstSet'] = true;
            }

            for (let index = 0; index < length; index++) {
              const runChild = revsionChildren[index];
              if ('w:ins' === runChild.tagName && 'w:del' === pTagName) {
                const secondRevInfo = this.getReviewInfo(runChild.attributes, document);
                const secondSetting = secondRevInfo.getRevisionSetting();
                const reviewSetting = reviewInfo.getRevisionSetting();

                reviewInfo.setRevisionSetting(secondSetting);
                reviewInfo.setDeleteInfo(secondRevInfo);
                secondRevInfo.setRevisionSetting(reviewSetting);
                continue;
              }
              if (!runChild || runChild.children == null) {
                continue;
              }
              RunReader.tTraverseRun(runChild, para, runCount, controlObj, uniqueImagelist, bInsertFile, document,
                headerFooter, false, modeFonts);

              if (!this.checkPortionRevisionMerge(para, runCount, type, reviewInfo)) {
                if (!bLostInfo) {
                  para.content[runCount].setReviewTypeWithInfo(type, reviewInfo.copy());
                  revisionManager.addRevisionByReviewInfo(para, reviewInfo);
                }
                runCount++;
              } else {
                const portion = para.content[runCount];
                para.content[runCount - 1].concatToContent(portion.content);
                para.content.splice(runCount, 1);
                para.curPos.contentPos = Math.max(0, --para.curPos.contentPos);
              }
            }

            // if (0 === length) {
            //   para.content[para.content.length - 1].setReviewTypeWithInfo(type, reviewInfo);
            //   runCount++;
            // }
          }
        } else if (pTagName === 'w:comment') {
            const startAttr = paraChild.attributes['start'];
            const idAttr = paraChild.attributes['id'];
            if (paraChild.children && startAttr && idAttr) {
                let commentPortion: ParaPortion;
                let commentDataNode: rtNode;
                for (const child of paraChild.children) {
                    if (typeof child === 'object') {
                        if (child.tagName === 'w:r') {
                            const controlObj = { unclosedNewControls };
                            commentPortion = RunReader.tTraverseRun(paraChild, para, runCount++,
                                controlObj, uniqueImagelist,
                                bInsertFile, document, headerFooter, false, modeFonts);
                        } else if (child.tagName === 'w:reply') {
                            commentDataNode = child;
                        }
                    }
                }
                if (commentPortion) {
                    const bStart = startAttr === '1';
                    const comment = commentManager.prepareReadedComment(idAttr, commentPortion, bStart);
                    if (comment) {
                        commentPortion.addComment(bStart, comment.getId());
                        if (bStart && commentDataNode) {
                            this.tTraverseComment(commentDataNode, comment);
                        }
                    }
                }
            }
        }
      }

    } // for

    para.setParaEndProperty();

    if (!bInsertFile) { // open file

      if (headerFooter == null) {
        if (numPr) { // 项目符合进行添加
          const manager = document.getNumManager();
          manager.addDocNum(para, numPr.numId, numPr.type);
        }
        if (container == null) {
          document.addToContent(document.content.length, para);
        } else {
          // add this para to region
          if (!skipAddCurPara) {
            container.addToContent(container.getContent().length, para);
          }
        }
      } else {
        // header footer
        const headerFooterOperateContent = headerFooter.content;
        const headerFooterContent = headerFooterOperateContent.content;
        // remove the default empty para if not populated yet
        // if (headerFooterContent.length === 1 && headerFooterContent[0].isEmpty() === true) { // only exist empty para
        //   // headerFooterContent = [];
        //   headerFooterContent.splice(headerFooterContent.length - 1, 1);
        // }

        if (container == null) {
          headerFooterOperateContent.addToContent(headerFooterContent.length, para);
        } else {
          container.addToContent(container.getContent().length, para);
        }

        // console.log(headerFooter)
      }

    } else { // insert file
      // console.dir(node)
      if (container == null) {
        paras.push(para);
      } else { // has container
        if ((node as IParseXmlNode).parentNode) { // from replace()
          const contents = container.getContent();
          if (contents[0] !== para) {
            container.addToContent(contents.length, para);
          }
        } else if (!skipAddCurPara) { // not region title paragraph -> 1st para is not pre-existent
          // if ((container instanceof Region && container.index < 0) ||
          if (this.isNewlyInsertedRegion(container) === true ||
            container instanceof TableCell) { // table or newly created region
            // TODO: in region case, is container.index < 0 safe?
            // No! [Region] vs [Region, Paragraph]: (typical case: saveSelectAreaToStream())
            // If the prior case, index will be 0 after first child para is traversed in recalculate()

            container.addToContent(container.getContent().length, para);

            if (container instanceof Region) {
              // check if paras already has such region
              let regionCanAddToParas = true;
              for (const tempPara of paras) {
                if (tempPara instanceof Region) {
                  if (tempPara.getName() === container.getName()) {
                    regionCanAddToParas = false;
                    break;
                  }
                  // bInsertFromRegion === false
                }
              }
              if (regionCanAddToParas === true) {
                // bAddRegionToParas: if not inserted from region, and the para is in depth 2 region -> false
                // in this case, the region shouldn't be added to paras
                if (bAddRegionToParas === true) {
                  this.addRegionToParas(paras, container, document);
                }
              }
            }

          } else { // file must be inserted from the container
            paras.push(para);
          }

          return;
        } else { // region title paragraph
          if (container instanceof Region === false) {
            // tslint:disable-next-line: no-console
            // console.warn('skipAddCurPara is not with region');
          } else if (container instanceof Region) {
            if (bAddRegionToParas === true) { // check the var's def
              // this is the first para in region. The region mustn't be added yet
              this.addRegionToParas(paras, container, document);
            }
          }
        }
      }

    }

  } // traverseP

  public static tTraverseComment(dataNode: rtNode, comment: Comment): void {
    if (!comment || !dataNode || !dataNode.children) {
        return;
    }
    const data = comment.getData();
    const attributes = dataNode.attributes;
    data.updateForce({
        userName: attributes['userName'],
        time: new Date(attributes['time']),
        content: attributes['content'],
        isSolved: attributes['solved'] === '1',
    });
    if (dataNode.children) {
        for (const node of dataNode.children) {
            if (typeof node === 'object' && node.tagName === 'w:reply') {
                const replyAttr = node.attributes;
                const reply = data.addReply('');
                reply.updateForce({
                    userName: replyAttr['userName'],
                    time: new Date(replyAttr['time']),
                    content: replyAttr['content'],
                    isSolved: replyAttr['solved'] === '1',
                });
            }
        }
    }
  }

  /**
   * for new section: 需求 #248125
   */
  public static traverseParagraphForInsertFileSec(
    node: rtNode, insertFileObj: IInsertFileContent, ContentControlDescObj: IContentControlDescObj,
    uniqueImagelist: Map<string, IUniqueImageProps>,
    // paras: DocumentContentElementBase[],
    container: Region | TableCell = null): void {

    let para = null;
    let skipAddCurPara = false;

    const bInsertFile = true;
    const documentVersion = (insertFileObj ? insertFileObj.documentVersion : 0);
    // const readNewControls = {};
    if (container == null) {
      para = new Paragraph(null, insertFileObj.logicDocument);
    } else {
      // default
      para = new Paragraph(container.getOperateContent(), insertFileObj.logicDocument);

      // inserting file also needs to think about region recursion
      // if it's a region with title, the first para is already created
      const containerContent = container.getContent();
      if (containerContent.length === 1 && container instanceof Region && '' !== container.getTitle() ) {
        // this condition should only enter once
        // Attention: if 1st para in region is para with ONLY title portion, the condition would always suffice
        // thus should develop a way to remedy that
        let firstTextNode: rtNode = null;
        let firstTextPr = null;
        for (const nodeChild of node.children) {
          // first find w:r then get first w:t
          // TODO: may be danger
          if (typeof nodeChild === 'object' && nodeChild.tagName === 'w:r') {
            for (const runChild of nodeChild.children) {
              if (typeof runChild === 'object' && runChild.tagName === 'w:rPr') {
                firstTextPr = new TextProperty(getDefaultFont(insertFileObj.logicDocument));
                RunReader.tTraverseRunProperty(runChild, firstTextPr, null);
              }
              if (typeof runChild === 'object' && runChild.tagName === 'w:t') {
                firstTextNode = runChild;
                break;
              }
            }
          }
          if (firstTextNode != null) {
            break;
          }
        }

        let firstText = '';
        if (firstTextNode != null) {
          const tempText = firstTextNode.children[0];
          firstText = (typeof tempText === 'string') ? safeDecodeURIComponent(tempText, documentVersion) : firstText;
        }

        // when traversing 2nd para, the first w:t must never be title portion
        // make sure only the 1st para enter this
        if (firstText === container.getTitle()) {
          const thePara = containerContent[0];
          const theParaContent = thePara.getContent();
          // iff case: 1 title portion, 1 end portion
          // tslint:disable-next-line: max-line-length
          if (theParaContent && theParaContent.length === 2 && theParaContent[0] instanceof ParaPortion && theParaContent[1].isParaEndPortion()) {
            const thePortion = theParaContent[0];
            if (thePortion.getTextContent() === container.getTitle()) {
              para = thePara;
              skipAddCurPara = true;
              container.setTitlePortionTextProperty2(firstTextPr);
            }
          }
        }
      }
    }

    const paraChildren = node.children as rtNode[];

    // death stranding
    // let { newControlManager } = ContentControlDescObj;
    // const newControlManager = insertFileObj.newControlManager;
    const { unclosedNewControls } = ContentControlDescObj;
    const buttons = insertFileObj.buttons;

    // these three vars should be meaningless if cross paragraph
    // let bFollowedBySdtEnd = false; // just related to w:r
    // let bFirstSiblingOfSdtStart = false; // just related to w:r
    let showPlaceholder = false; // if sdtstart has <showPlaceholder>, locate & save placeholder textprops

    let runCount = 0;
    // const domParser = new DOMParser();
    const newControlNamesTempArr = []; // used at inserting file
    const controlObj = { unclosedNewControls, buttons };
    for (let i = 0; i < paraChildren.length; i++) {
      const paraChild = paraChildren[i];
      if (typeof paraChild === 'object') {
        const pTagName = paraChild.tagName;
        /** w:pPr */
        if (pTagName === 'w:pPr') {
          ParagraphReader.parseParaProperty(para, paraChild);
        } else if (pTagName === 'w:r') { /** w:r */
          // console.log(paraChild)
          // pay attention if it's i or index!
          // const nextNode = ParagraphReader.tGetTheFirstNonTextSiblingFollowed(i, paraChildren);
          // // console.log(nextNode)
          // if (nextNode && nextNode.tagName === 'sdtEnd') {
          //   bFollowedBySdtEnd = true;
          // }

          // const controlObj = {bFirstSiblingOfSdtStart, unclosedNewControls, bFollowedBySdtEnd};
          

          // if it's title portion/placeholder, ignore it
          const titlePortionType = this.tIsTitlePortion(paraChild, controlObj, container, i, null,
                                          paraChildren, i, false, documentVersion);
          if (titlePortionType === TitlePortionType.None) {
            if (this.tIsPlaceholderPortion(showPlaceholder, paraChild, controlObj, documentVersion) === false) {
              // showPlaceholder must not be true
              showPlaceholder = false;
              RunReader.tTraverseRun(paraChild, para, runCount, controlObj, uniqueImagelist, bInsertFile, insertFileObj.logicDocument,
                null, false, null, insertFileObj);
              runCount++;
            } else {
              const phPortion = RunReader.tTraverseRun(paraChild, para, runCount, controlObj, uniqueImagelist,
                bInsertFile, null, null, showPlaceholder, null, insertFileObj);
              this.setPlaceholderTextProperty(phPortion, controlObj);
            }
          } else if (titlePortionType === TitlePortionType.Region) {
            // title portion in region is not like in struct
            runCount++;
          }

          // console.log(circularParse(circularStringify(container.getContent())));
          // bFirstSiblingOfSdtStart = bFirstSiblingOfSdtStart ? false : bFirstSiblingOfSdtStart;
          // bFollowedBySdtEnd = bFollowedBySdtEnd ? false : bFollowedBySdtEnd;
        } else if (pTagName === 'sdt') { /* new sdt */

          runCount = this.traverseSdtInsertFile3(bInsertFile, insertFileObj.logicDocument, paraChild,
            newControlNamesTempArr, para, runCount, container, i,
            uniqueImagelist, null, ContentControlDescObj, insertFileObj);
          showPlaceholder = false;

        } else if (pTagName === 'section') { /** content control */
          runCount = this.tTraverseSectionForInsertFile(insertFileObj, paraChild,
            newControlNamesTempArr, para, runCount, null, i,
            uniqueImagelist, null, ContentControlDescObj, documentVersion);
          showPlaceholder = false;
        // } else if (pTagName === 'w:ins') {
        //   const attrs = paraChild.attributes;
        //   const revsionChildren = paraChild.children as rtNode[];
        //   const length = revsionChildren ? revsionChildren.length : 0;

        //   if (attrs && 0 < length) {
        //     const controlObj = { unclosedNewControls };
        //     for (let index = 0; index < length; index++) {
        //       const runChild = revsionChildren[index];
        //       RunReader.tTraverseRun(runChild, para, runCount, controlObj, uniqueImagelist, bInsertFile, null,
        //           null, false, null, insertFileObj);

        //       runCount++;
        //     }
        //   }
        } else if ('w:ins' === pTagName || 'w:del' === pTagName) {
          const document = insertFileObj.logicDocument;
          const attrs = paraChild.attributes;
          const revsionChildren = paraChild.children as rtNode[];
          const length = revsionChildren ? revsionChildren.length : 0;

          if (document.isTrackRevisions()) {
            if ('w:ins' === pTagName) {
              if (attrs && 0 < length) {
                const controlObj = { unclosedNewControls };
                for (let index = 0; index < length; index++) {
                  const runChild = revsionChildren[index];
                  RunReader.tTraverseRun(runChild, para, runCount, controlObj, uniqueImagelist, bInsertFile, null,
                      null, false, null, insertFileObj);

                  runCount++;
                }
              }
            }

            continue;
          }

          if (length === 1 && revsionChildren[0] as any === '') {
            continue;
          }

          if (attrs && 0 < length) {
            const type = ('w:ins' === pTagName ? ReviewType.Add : ReviewType.Remove);
            const reviewInfo = this.getReviewInfo(attrs, document, type);
            const revisionManager = document.getRevisionsManager();
            // const controlObj = {bFirstSiblingOfSdtStart, unclosedNewControls, bFollowedBySdtEnd};
            const controlObj = { unclosedNewControls };
            const userId = attrs['id'] ? attrs['id'] : null;
            const userName = attrs['author'] ? attrs['author'] : null;
            const bLostInfo = (!userId && !userName);

            if (bLostInfo) {
              if (ReviewType.Remove === type) {
                continue;
              }
            }

            if (unclosedNewControls && unclosedNewControls[unclosedNewControls.length - 1]?.isNewDateBox()
              && ReviewType.Add === type && !reviewInfo['firstDate'] && !reviewInfo.getSavedRecord()) {
                reviewInfo['bFirstSet'] = true;
            }

            for (let index = 0; index < length; index++) {
              const runChild = revsionChildren[index];
              if ('w:ins' === runChild.tagName && 'w:del' === pTagName) {
                const secondRevInfo = this.getReviewInfo(runChild.attributes, document);
                const secondSetting = secondRevInfo.getRevisionSetting();
                const reviewSetting = reviewInfo.getRevisionSetting();

                reviewInfo.setRevisionSetting(secondSetting);
                reviewInfo.setDeleteInfo(secondRevInfo);
                secondRevInfo.setRevisionSetting(reviewSetting);
                continue;
              }
              if (!runChild || runChild.children == null) {
                continue;
              }

              RunReader.tTraverseRun(runChild, para, runCount, controlObj, uniqueImagelist, bInsertFile, null,
                null, false, null, insertFileObj);

              if (!this.checkPortionRevisionMerge(para, runCount, type, reviewInfo)) {
                if (!bLostInfo) {
                  para.content[runCount].setReviewTypeWithInfo(type, reviewInfo.copy());
                  revisionManager.addRevisionByReviewInfo(para, reviewInfo);
                }
                runCount++;
              } else {
                const portion = para.content[runCount];
                para.content[runCount - 1].concatToContent(portion.content);
                para.content.splice(runCount, 1);
                para.curPos.contentPos = Math.max(0, --para.curPos.contentPos);
              }
            }
          }
        }
      }

    } // for

    para.setParaEndProperty();

    if (container == null) {
      // document.addToContent(document.content.length, para);
      insertFileObj.content.push(para);
    } else {
      if (!skipAddCurPara) {
        container.addToContent(container.getContent().length, para);
      }
    }

    return;

  } // traverseP

  public static traverseParagraphForInsertFile3(
    node: rtNode, insertFileObj: IInsertFileContent, ContentControlDescObj: IContentControlDescObj,
    uniqueImagelist: Map<string, IUniqueImageProps>,
    // paras: DocumentContentElementBase[],
    container: Region | TableCell = null, headerFooter: HeaderFooter = null,
    sectionEnds: any[] = []): void {

    let para = null;
    let skipAddCurPara = false;

    const bInsertFile = true;
    const documentVersion = (insertFileObj ? insertFileObj.documentVersion : 0);
    const readNewControls = {};
    if (container == null) {
      para = new Paragraph(insertFileObj.logicDocument, insertFileObj.logicDocument);
    } else {
      // default
      para = new Paragraph(container.getOperateContent(), insertFileObj.logicDocument);

      // inserting file also needs to think about region recursion
      // if it's a region with title, the first para is already created
      const containerContent = container.getContent();
      if (containerContent.length === 1 && container instanceof Region && '' !== container.getTitle() ) {
        // this condition should only enter once
        // Attention: if 1st para in region is para with ONLY title portion, the condition would always suffice
        // thus should develop a way to remedy that
        let firstTextNode: rtNode = null;
        let firstTextPr = null;
        for (const nodeChild of node.children) {
          // first find w:r then get first w:t
          // TODO: may be danger
          if (typeof nodeChild === 'object' && nodeChild.tagName === 'w:r') {
            for (const runChild of nodeChild.children) {
              if (typeof runChild === 'object' && runChild.tagName === 'w:rPr') {
                firstTextPr = new TextProperty(getDefaultFont(insertFileObj.logicDocument));
                RunReader.tTraverseRunProperty(runChild, firstTextPr, null);
              }
              if (typeof runChild === 'object' && runChild.tagName === 'w:t') {
                firstTextNode = runChild;
                break;
              }
            }
          }
          if (firstTextNode != null) {
            break;
          }
        }

        let firstText = '';
        if (firstTextNode != null) {
          const tempText = firstTextNode.children[0];
          firstText = (typeof tempText === 'string') ? safeDecodeURIComponent(tempText, documentVersion) : firstText;
        }

        // when traversing 2nd para, the first w:t must never be title portion
        // make sure only the 1st para enter this
        if (firstText === container.getTitle()) {
          const thePara = containerContent[0];
          const theParaContent = thePara.getContent();
          // iff case: 1 title portion, 1 end portion
          // tslint:disable-next-line: max-line-length
          if (theParaContent && theParaContent.length === 2 && theParaContent[0] instanceof ParaPortion && theParaContent[1].isParaEndPortion()) {
            const thePortion = theParaContent[0];
            if (thePortion.getTextContent() === container.getTitle()) {
              para = thePara;
              skipAddCurPara = true;
              container.setTitlePortionTextProperty2(firstTextPr);
            }
          }
        }
      }
    }

    const paraChildren = node.children as rtNode[];

    // death stranding
    // let { newControlManager } = ContentControlDescObj;
    const newControlManager = insertFileObj.newControlManager;
    const { newControlProperty, unclosedNewControls } = ContentControlDescObj;

    // these three vars should be meaningless if cross paragraph
    // let bFollowedBySdtEnd = false; // just related to w:r
    // let bFirstSiblingOfSdtStart = false; // just related to w:r
    let showPlaceholder = false; // if sdtstart has <showPlaceholder>, locate & save placeholder textprops

    let runCount = 0;
    // const domParser = new DOMParser();
    const newControlNamesTempArr = []; // used at inserting file
    const controlObj = { unclosedNewControls, buttons: insertFileObj.buttons };
    for (let i = 0; i < paraChildren.length; i++) {
      const paraChild = paraChildren[i];
      if (typeof paraChild === 'object') {
        const pTagName = paraChild.tagName;
        /** w:pPr */
        if (pTagName === 'w:pPr') {
          ParagraphReader.parseParaProperty(para, paraChild);
        } else if (pTagName === 'w:r') { /** w:r */
          // console.log(paraChild)
          // pay attention if it's i or index!
          // const nextNode = ParagraphReader.tGetTheFirstNonTextSiblingFollowed(i, paraChildren);
          // // console.log(nextNode)
          // if (nextNode && nextNode.tagName === 'sdtEnd') {
          //   bFollowedBySdtEnd = true;
          // }

          // const controlObj = {bFirstSiblingOfSdtStart, unclosedNewControls, bFollowedBySdtEnd};
          

          // if it's title portion/placeholder, ignore it
          const titlePortionType = this.tIsTitlePortion(paraChild, controlObj, container, i, null,
                                          paraChildren, i, false, documentVersion);
          if (titlePortionType === TitlePortionType.None) {
            if (this.tIsPlaceholderPortion(showPlaceholder, paraChild, controlObj, documentVersion) === false) {
              // showPlaceholder must not be true
              showPlaceholder = false;
              RunReader.tTraverseRun(paraChild, para, runCount, controlObj, uniqueImagelist, bInsertFile, null,
                headerFooter, false, null, insertFileObj);
              runCount++;
            } else {
              const phPortion = RunReader.tTraverseRun(paraChild, para, runCount, controlObj, uniqueImagelist,
                bInsertFile, null, headerFooter, showPlaceholder, null, insertFileObj);
              this.setPlaceholderTextProperty(phPortion, controlObj);
            }
          } else if (titlePortionType === TitlePortionType.Region) {
            // title portion in region is not like in struct
            runCount++;
          }

          // console.log(circularParse(circularStringify(container.getContent())));
          // bFirstSiblingOfSdtStart = bFirstSiblingOfSdtStart ? false : bFirstSiblingOfSdtStart;
          // bFollowedBySdtEnd = bFollowedBySdtEnd ? false : bFollowedBySdtEnd;
        } else if (pTagName === 'sdt') { /* new sdt */

          runCount = this.traverseSdtInsertFile3(bInsertFile, insertFileObj.logicDocument, paraChild,
            newControlNamesTempArr, para, runCount, container, i,
            uniqueImagelist, headerFooter, ContentControlDescObj, insertFileObj);
          showPlaceholder = false;

        } else if (pTagName === 'sectionStart') { /** content control */

          if (ParagraphReader.shouldReadContentControl((paraChild as IParseXmlNode).type === undefined,
              null, paraChild.attributes['type'])
            && this.isSectionEndTupleExisted(paraChild, sectionEnds)) {
            // let runChild = paraChild.childNodes[0];
            const runChildren = paraChild.children;
            let borderStringStart = '';
            let bPlaceHolder = false; // this prop is in portion

            const nameAttr = paraChild.attributes['name'];
            if (nameAttr) {
              newControlProperty.newControlName = nameAttr;
            }

            if (paraChild.attributes['type']) {
              // newControlProperty.newControlType = Number(paraChild.attributes.getNamedItem('type').nodeValue);
              newControlProperty.newControlType = +paraChild.attributes['type'];
              readNewControls[nameAttr] = new ReadStruct(newControlProperty);
              // TODO: section shouldnt be sign box itself
            }

            let textProperty: TextProperty = null;
            for (const runChild of runChildren) {
              if (typeof runChild === 'object') {
                if ('w:rPr' === runChild.tagName) {
                  textProperty = new TextProperty(getDefaultFont(insertFileObj.logicDocument));
                  RunReader.tTraverseRunProperty(runChild, textProperty, null);
                  // } else if (runChild.nodeName !== '#text') {
                } else {
                  const textNode = runChild.children[0];
                  // if (StructXmlName.Sdtcontent === runChild.tagName) {
                  //   sdtContentNode = runChild;
                  //   continue;
                  // }

                  if (StructXmlName.ShowPlaceholder === runChild.tagName) {
                    if (textNode === '1') {
                      showPlaceholder = true;
                    }
                    continue;
                  }
                  if (StructXmlName.BorderString === runChild.tagName) {
                    if (textNode && typeof textNode === 'string' ) {
                      borderStringStart = safeDecodeURIComponent(textNode as any, documentVersion);
                    }

                    continue;
                  }
                  // console.log(textNode)
                  readNewControls[nameAttr].readProp(textNode, documentVersion, runChild);
                  // console.log(textNode)
                  // const res = readerStructProps(newControlProperty, textNode, runChild, documentVersion);
                  // if (res.borderStringStart !== undefined) {
                  //   borderStringStart = res.borderStringStart;
                  // }
                  // if (res.showPlaceholder !== undefined) {
                  //   showPlaceholder = res.showPlaceholder;
                  // }
                }
              }

            } // end for

            // console.log(newControlProperty);
            // console.log(circularParse(circularStringify(newControlProperty)));

            if (null == textProperty) {
              textProperty = new TextProperty(getDefaultFont(insertFileObj.logicDocument));
            }

            // newControlManager = insertFileObj.newControlManager;
            // control with duplicate name should already be handled
            newControlProperty.bInsertFile = bInsertFile;
            let lastNewControl = newControlManager.createNewControl(para, newControlProperty);
            if (lastNewControl == null) {
              // tslint:disable-next-line: no-console
              // console.warn('duplicate control name detected, should be fixed automatically:)');
              const source = newControlProperty.bInsertFile ? FileSource.InsertFile : null;
              newControlProperty.newControlName = newControlManager.makeUniqueName(newControlProperty.newControlType,
                newControlProperty.newControlName, source);
              lastNewControl = newControlManager.createNewControl(para, newControlProperty);
            }

            // reset newControlProperty
            this.resetNewControlProperty(newControlProperty);

            /** figure out portion.bPlaceHolder's val */
            /** if <sdtStart> is not strictly followed by <sdtEnd>, bPlaceHolder is false */
            const nextNode = ParagraphReader.tGetTheFirstNonTextSiblingFollowed(i, paraChildren);
            // console.log(nextNode)
            // 11.3.2020: the next node MUST BE struct portion or PLACEHOLDER PORTION
            // console.log(paraChild)
            // console.log(nextNode)
            if (nextNode && nextNode.tagName === 'sdtEnd') { // TODO, seems not problematic:('sectionEnd')
              // consider compatibility of 'no placeholder portion' case
              bPlaceHolder = true;
              // bFirstSiblingOfSdtStart = false; // actually no use as of 11.4.2020

            } else {

              bPlaceHolder = false;
              if (nextNode) { // look out for null. still may be problematic
                // bFirstSiblingOfSdtStart = true;

                // if a struct has title + placeholder, still bPlaceHolder may be true
                if (nextNode.tagName === 'w:r') {
                  const potentialSdtEnd = paraChildren[i + 1 + 1];
                  let text = '';
                  for (const nextNodeChild of nextNode.children) {
                    if (typeof nextNodeChild === 'object' && nextNodeChild.tagName === 'w:t') {
                      text = safeDecodeURIComponent(nextNodeChild.children[0] as string, documentVersion);
                      break;
                    }
                  }
                  // console.log(text)
                  // console.log(lastNewControl.getTitle())
                  if (typeof potentialSdtEnd === 'object' && potentialSdtEnd.tagName === 'sdtEnd') {
                    // 1. old compatible: [ title ] 2. [ {normal portion} ] 3. new: [ placeholder ]
                    // 12.7.2020: not correct, can still be of [title rpr] pattern
                    if (lastNewControl.getTitle() === text) {
                      bPlaceHolder = true;
                    }
                  } else {
                    // [ title placeholder ]
                    // no need to deal with ph text props here, just override it
                  }
                }

              }
            }

            /** create new seperation portion for [ always */
            const newControlContent = lastNewControl.getNewControlContent();
            const newControlPlaceHolder = newControlContent.getPlaceHolder();
            const newControlStartBorderPortion = lastNewControl.getStartBorderPortion();
            newControlStartBorderPortion.textProperty = textProperty;

            if (bPlaceHolder) {
              // newControlPlaceHolder.textProperty = textProperty.copy();
              // placeholder always has default text prop - not any more, will override using showPlaceholder
              // here just set a default text props
              newControlPlaceHolder.textProperty = new TextProperty(getDefaultFont(insertFileObj.logicDocument));
            }

            lastNewControl.setPlaceHolder(bPlaceHolder);

            if (!nextNode) { // null. means at end of para
              newControlPlaceHolder.content = newControlStartBorderPortion.content; // R.W: not in use
            }

            para.addToContent(runCount, newControlStartBorderPortion);
            runCount++; // not matter

            unclosedNewControls.push(lastNewControl);

            /** set map in content control manager no matter what */
            // newControlManager.getNameMap()
            //   .set(lastNewControl.getNewControlName(), lastNewControl);
            newControlManager.addPasteControl({ control: lastNewControl, contentPos: null });
            insertFileObj.insertFileAllNewControlNames.set(lastNewControl.getNewControlName(), lastNewControl);
          }

        } else if (pTagName === 'sectionEnd') { /** sdtEnd */

          showPlaceholder = false;
          if (ParagraphReader.shouldReadContentControl((paraChild as IParseXmlNode).type === undefined,
              null, paraChild.attributes['type']) && unclosedNewControls.length > 0) {

            // check grasped sectionstart's validity - no need, already checked unclosedNewControls length
            const currentControl = unclosedNewControls[unclosedNewControls.length - 1];
            const newControlStartBorderPortion = currentControl.getStartBorderPortion();
            let textProperty: TextProperty = null;

            if (newControlStartBorderPortion.bPlaceHolder) { // find current control's bPlaceholder
              // as of 7/8/2020 only startBorderPortion.bPlaceHolder stays updated
              para.addToContent(runCount, currentControl.getNewControlContent()
                .getPlaceHolder());
              runCount++;

            }

            const paraChildChildren = paraChild.children;
            if (paraChildChildren && 0 < paraChildChildren.length) {
              const nodeChildren = paraChild.children;
              let bSuccess = false;

              for (const nodeChild of nodeChildren) {
                if (typeof nodeChild === 'object') {
                  switch (nodeChild.tagName) {
                    case 'w:rPr':
                      textProperty = new TextProperty(getDefaultFont(insertFileObj.logicDocument));
                      // RunReader.traverseRunProperty(paraChild.childNodes[0], textProperty);
                      RunReader.tTraverseRunProperty(nodeChild, textProperty, null);
                      bSuccess = true;
                      break;
                  }

                  if (bSuccess) {
                    break;
                  }
                }

              }
            }

            if (null == textProperty) {
              textProperty = new TextProperty(getDefaultFont(insertFileObj.logicDocument));
            }

            /** create new seperation portion for ] always */
            const newControlEndBorderPortion = currentControl.getEndBorderPortion();
            newControlEndBorderPortion.textProperty = textProperty;
            para.addToContent(runCount, newControlEndBorderPortion); // will also set portion's parent!
            runCount++;

            unclosedNewControls.pop();

            /** if it's control check/radiobutton, need extra step to fill in inner content */
            if (currentControl instanceof NewControlCheck || currentControl instanceof NewControlRadio) {
              currentControl.addContent(); // lsy's method
            }

            if (unclosedNewControls.length === 0) { // root
              if (bInsertFile) {
              //   // if it's a root content control, add to "root content control array"(map already added in sdtstart)
              //   // newControls[]  -> root structs
              //   // NOTE the order of elements in the array SHOULD indicate structs appearance in doc
                if (headerFooter == null) {
                  // newControlManager.getAllNewControls()
                  //   .splice(newControlManager.getAllNewControls().length, 0, currentControl);
                  // newControlManager.addPasteControl({ control: currentControl, contentPos: null });
                } else {
                  if (headerFooter.isHeader()) {
                    const structCount = ContentControlDescObj.headerStructCount;
                    // console.log(structCount)
                    // newControlManager.getAllNewControls()
                    //   .splice(structCount, 0, currentControl);
                    newControlManager.addPasteControl({ control: currentControl, contentPos: null });
                    ContentControlDescObj.headerStructCount++;
                  } else {
                    // footer
                    // newControlManager.getAllNewControls()
                    //   .splice(newControlManager.getAllNewControls().length, 0, currentControl);
                    newControlManager.addPasteControl({ control: currentControl, contentPos: null });
                    insertFileObj.insertFileAllNewControlNames.set(currentControl.getNewControlName(), currentControl);
                  }
                }
              } else {
                // pasteControl need to fill up when insertFile
                // control order is important
                // let contentPos: any;
                // if ((paraChild as IParseXmlNode).type === undefined) {
                //   contentPos = document.getCurContentPosInDoc(false, true);
                // }
                // newControlManager.addPasteControl({ control: currentControl, contentPos: null });
              }

            } else {
              // 非根节点时，插入文件需set name map
              if (!bInsertFile) {
                // newControlManager.addNewControlInMap(currentControl);
              }
              // add to leafList. two-way
              const lastElemInUnclosedNewControls = unclosedNewControls[unclosedNewControls.length - 1];
              currentControl.setParent(lastElemInUnclosedNewControls);
              lastElemInUnclosedNewControls.getLeafList()
                .push(currentControl);
            }
          }
        } else if (pTagName === 'w:ins') {
          const attrs = paraChild.attributes;
          const revsionChildren = paraChild.children as rtNode[];
          const length = revsionChildren ? revsionChildren.length : 0;

          if (attrs && 0 < length) {
            const controlObj = { unclosedNewControls };
            for (let index = 0; index < length; index++) {
              const runChild = revsionChildren[index];
              RunReader.tTraverseRun(runChild, para, runCount, controlObj, uniqueImagelist, bInsertFile, null,
                  headerFooter, false, null, insertFileObj);

              runCount++;
            }
          }
        }
      }

    } // for

    para.setParaEndProperty();

    if (container == null) {
      // document.addToContent(document.content.length, para);
      insertFileObj.content.push(para);
    } else {
      if (!skipAddCurPara) {
        container.addToContent(container.getContent().length, para);
      }
    }

    return;

  } // traverseP

  public static traverseParagraphForInsertFile2(
    node: rtNode, insertFileObj: IInsertFileContent, ContentControlDescObj: IContentControlDescObj,
    uniqueImagelist: Map<string, IUniqueImageProps>, bInsertFile: boolean = false,
    container: Region | TableCell = null, headerFooter: HeaderFooter = null
  ): void {


    let para = null;
    // let skipAddCurPara = false;
    const documentVersion = (insertFileObj ? insertFileObj.documentVersion : 0);

    if (container == null) {
      para = new Paragraph(null, insertFileObj.logicDocument);
    } else {
      para = new Paragraph(container.getOperateContent(), insertFileObj.logicDocument);
    }

    const paraChildren = node.children as rtNode[];

    // death stranding
    // let {newControlManager} = ContentControlDescObj;
    const newControlManager = insertFileObj.newControlManager;
    const {newControlProperty, unclosedNewControls} = ContentControlDescObj;

    // these three vars should be meaningless if cross paragraph
    // let bFollowedBySdtEnd = false; // just related to w:r
    // let bFirstSiblingOfSdtStart = false; // just related to w:r
    let showPlaceholder = false; // if sdtstart has <showPlaceholder>, locate & save placeholder textprops

    let runCount = 0;
    // const domParser = new DOMParser();
    // const newControlNamesTempArr = []; // used at inserting file

    for (let i = 0; i < paraChildren.length; i++) {
      const paraChild = paraChildren[i];

      if (typeof paraChild === 'object') {
        /** w:pPr */
        if (paraChild.tagName === 'w:pPr') {

          let indent: rtNode = null;
          let spacing: rtNode = null;
          let alignment: rtNode = null;
          let lastRenderedPageBreak: rtNode = null;
          let wordWrap: rtNode = null;
          for (const nodeChild of paraChild.children) {
            if (typeof nodeChild === 'object') {
              if (nodeChild.tagName === 'w:ind') {
                indent = nodeChild;
              } else if (nodeChild.tagName === 'w:spacing') {
                spacing = nodeChild;
              } else if (nodeChild.tagName === 'w:jc') {
                alignment = nodeChild;
              } else if (nodeChild.tagName === 'w:lastRenderedPageBreak') {
                lastRenderedPageBreak = nodeChild;
              } else if ( 'w:wordWrap' === nodeChild.tagName ) {
                wordWrap = nodeChild;
              }
            }
          }

          /** indentation */
          try {
            if (indent) {
              if (indent.attributes) {
                if (indent.attributes['w:left']) {
                  // filtered at writer, should not need to check if isNaN
                  para.paraProperty.paraInd.left =
                    Number(indent.attributes['w:left']);
                }
                if (indent.attributes['w:firstLine']) {
                  para.paraProperty.paraInd.firstLine =
                    Number(indent.attributes['w:firstLine']);
                }
                if (indent.attributes['w:right']) {
                  para.paraProperty.paraInd.right =
                    Number(indent.attributes['w:right']);
                }

              } else {
                throw new Error('w:ind has no attributes');
              }
            }
          } catch (error) {
            // alert(ErrorMessages.XmlError);
            // tslint:disable-next-line: no-console
            console.warn(ErrorMessages.XmlError);
            window.dispatchEvent(new ErrorEvent('error', {message: ErrorMessages.XmlError, error}));
            const date = new Date();
            logger.error({id: 0, code: error.stack,
              startTime: date, args: null, name: 'traverseParagraph.indent.attributes'});
          }

          /** line spacing */
          if (spacing) {
            // console.log(spacing)
            try {
              if (spacing.attributes) {
                if (spacing.attributes['w:lineType']) {
                  para.paraProperty.paraSpacing.lineSpacingType =
                    Number(spacing.attributes['w:lineType']);
                }
                if (spacing.attributes['w:line']) {
                  para.paraProperty.paraSpacing.lineSpacing =
                    Number(spacing.attributes['w:line']);
                }
              } else {
                throw new Error('w:spacing has no attributes');
              }
            } catch (error) {
              // alert(ErrorMessages.XmlError);
              window.dispatchEvent(new ErrorEvent('error', {message: ErrorMessages.XmlError, error}));
              // tslint:disable-next-line: no-console
              console.warn(ErrorMessages.XmlError);
              const date = new Date();
              logger.error({id: 0, code: error.stack,
              startTime: date, args: null, name: 'traverseParagraph.spacing.attributes'});

            }
          }

          /** alignment */
          if (alignment) {
            // console.log(alignment);
            try {
              if (alignment.attributes && alignment.attributes['w:val']) {
                const alignmentVal = alignment.attributes['w:val'];
                // console.log(alignment.attributes.getNamedItem('w:val').nodeValue)
                let alignmentType = AlignType.Justify; // default to be justified
                switch (alignmentVal) {
                  case AlignmentOptions.BOTH:
                    alignmentType = AlignType.Justify;
                    break;
                  case AlignmentOptions.LEFT:
                    alignmentType = AlignType.Left;
                    break;
                  case AlignmentOptions.RIGHT:
                    alignmentType = AlignType.Right;
                    break;
                  case AlignmentOptions.CENTER:
                    alignmentType = AlignType.Center;
                    break;
                  default:
                    break;
                }

                para.paraProperty.alignment = alignmentType;

              } else {
                throw new Error('w:jc has no attributes');
              }
            } catch (error) {
              // alert(ErrorMessages.XmlError);
              window.dispatchEvent(new ErrorEvent('error', {message: ErrorMessages.XmlError, error}));
              // tslint:disable-next-line: no-console
              console.warn(ErrorMessages.XmlError);
              const date = new Date();
              logger.error({id: 0, code: error.stack,
              startTime: date, args: null, name: 'traverseParagraph.alignment.attributes'});
            }
          }

          /** page break */
          if (lastRenderedPageBreak) {
            para.paraProperty.bPageBreakBefore = true;
          }

          if ( wordWrap ) {
            try {
              if (wordWrap.attributes && wordWrap.attributes['w:val']) {
                para.paraProperty.bWordWrap = ('1' === wordWrap.attributes['w:val'] ? true : false);
              } else {
                throw new Error('w:wordWrap has no attributes');
              }
            } catch (error) {
              // alert(ErrorMessages.XmlError);
              window.dispatchEvent(new ErrorEvent('error', {message: ErrorMessages.XmlError, error}));
              // tslint:disable-next-line: no-console
              console.warn(ErrorMessages.XmlError);
              const date = new Date();
              logger.error({id: 0, code: error.stack,
              startTime: date, args: null, name: 'traverseParagraph.wordWrap.attributes'});

            }
          }
        } else if (paraChild.tagName === 'w:r') { /** w:r */
          const controlObj = {unclosedNewControls};

          // if it's title portion/placeholder, ignore it
          const titlePortionType = this.tIsTitlePortion(paraChild, controlObj, container, i, null,
                                            paraChildren, i, false, documentVersion);
          if (titlePortionType === TitlePortionType.None) {
            if (this.tIsPlaceholderPortion(showPlaceholder, paraChild, controlObj, documentVersion) === false) {
              RunReader.tTraverseRun(paraChild, para, runCount, controlObj, uniqueImagelist, bInsertFile, null,
                headerFooter);
              runCount++;
            } else {
              const phPortion = RunReader.tTraverseRun(paraChild, para, runCount, controlObj, uniqueImagelist,
                bInsertFile, null, headerFooter, showPlaceholder);
              this.setPlaceholderTextProperty(phPortion, controlObj);
            }
          } else if (titlePortionType === TitlePortionType.Region) {
            // title portion in region is not like in struct
            runCount++;
          }

          // console.log(circularParse(circularStringify(container.getContent())));
          // bFirstSiblingOfSdtStart = bFirstSiblingOfSdtStart ? false : bFirstSiblingOfSdtStart;
          // bFollowedBySdtEnd = bFollowedBySdtEnd ? false : bFollowedBySdtEnd;
        } else if (paraChild.tagName === 'sdtStart') { /** content control */

          // check if prev struct lost ] part
          const prevControl = unclosedNewControls[unclosedNewControls.length - 1];
          if (prevControl != null) {
            const prevControlType = prevControl.getType();
            if (prevControlType !== NewControlType.Section && prevControlType !== NewControlType.SignatureBox) {
              // tslint:disable-next-line: no-console
              // console.warn('sdtEnd missing detected, should be fixed automatically:)');
              // add ] component
              showPlaceholder = false;
              runCount = this.traverseSdtEndInsertFile2(bInsertFile, null, paraChild, para, runCount,
                ContentControlDescObj, insertFileObj);
              // remove the item from unclosednewcontrols
              // unclosedNewControls.pop(); // already poped
            }
          }

          if (ParagraphReader.shouldReadContentControl(bInsertFile, null, paraChild.attributes['type'])) {
            // let runChild = paraChild.childNodes[0];
            const runChildren = paraChild.children;
            let borderStringStart = '';
            let bPlaceHolder = false; // this prop is in portion

            const nameAttr = paraChild.attributes['name'];
            if (nameAttr) {
              newControlProperty.newControlName = nameAttr;
            }

            if (paraChild.attributes['type']) {
              // newControlProperty.newControlType = Number(paraChild.attributes.getNamedItem('type').nodeValue);
              const type = +paraChild.attributes['type'];
              if (type === NewControlType.SignatureBox) {
                newControlProperty.bReadSign = true;
              }
              newControlProperty.newControlType = type;
            }

            let textProperty: TextProperty = null;
            for (const runChild of runChildren) {
              if (typeof runChild === 'object') {
                if ( 'w:rPr' === runChild.tagName ) {
                  textProperty = new TextProperty(getDefaultFont(insertFileObj.logicDocument));
                  RunReader.tTraverseRunProperty(runChild, textProperty, null);
                // } else if (runChild.nodeName !== '#text') {
                } else {
                  const textNode = runChild.children[0];
                  const res = readerStructProps(newControlProperty, textNode, runChild, documentVersion);
                  if (res.borderStringStart !== undefined) {
                    borderStringStart = res.borderStringStart;
                  }
                  if (res.showPlaceholder !== undefined) {
                    showPlaceholder = res.showPlaceholder;
                  }
                }
              }

            } // end for

            // console.log(newControlProperty);
            // console.log(circularParse(circularStringify(newControlProperty)));

            if ( null == textProperty ) {
              textProperty = new TextProperty(getDefaultFont(insertFileObj.logicDocument));
            }

            // control with duplicate name should already be handled
            newControlProperty.bInsertFile = bInsertFile;
            let lastNewControl = newControlManager.createNewControl(para, newControlProperty);
            if (lastNewControl == null) {
              // tslint:disable-next-line: no-console
              // console.warn('duplicate control name detected, should be fixed automatically:)');
              newControlProperty.newControlName = newControlManager.makeUniqueName(newControlProperty.newControlType,
                  newControlProperty.newControlName);
              this.checkInsertFileNewControlName(newControlProperty, insertFileObj);
              lastNewControl = newControlManager.createNewControl(para, newControlProperty);
            }

            // reset newControlProperty
            this.resetNewControlProperty(newControlProperty);

            /** figure out portion.bPlaceHolder's val */
            /** if <sdtStart> is not strictly followed by <sdtEnd>, bPlaceHolder is false */
            const nextNode = ParagraphReader.tGetTheFirstNonTextSiblingFollowed(i, paraChildren);
            // console.log(nextNode)
            // 11.3.2020: the next node MUST BE struct portion or PLACEHOLDER PORTION
            // console.log(paraChild)
            // console.log(nextNode)
            if (nextNode && nextNode.tagName === 'sdtEnd') {
              // consider compatibility of 'no placeholder portion' case
              bPlaceHolder = true;
              // bFirstSiblingOfSdtStart = false; // actually no use as of 11.4.2020

            } else {

              bPlaceHolder = false;
              if (nextNode) { // look out for null. still may be problematic
                // bFirstSiblingOfSdtStart = true;

                // if a struct has title + placeholder, still bPlaceHolder may be true
                if (nextNode.tagName === 'w:r') {
                  const potentialSdtEnd = paraChildren[i + 1 + 1];
                  let text = '';
                  for (const nextNodeChild of nextNode.children) {
                    if (typeof nextNodeChild === 'object' && nextNodeChild.tagName === 'w:t') {
                      text = safeDecodeURIComponent(nextNodeChild.children[0] as string, documentVersion);
                      break;
                    }
                  }
                  // console.log(text)
                  // console.log(lastNewControl.getTitle())
                  if (typeof potentialSdtEnd === 'object' && potentialSdtEnd.tagName === 'sdtEnd') {
                    // 1. old compatible: [ title ] 2. [ {normal portion} ] 3. new: [ placeholder ]
                    // 12.7.2020: not correct, can still be of [title rpr] pattern
                    if (lastNewControl.getTitle() === text) {
                      bPlaceHolder = true;
                    }
                  } else {
                    // [ title placeholder ]
                    // no need to deal with ph text props here, just override it
                  }
                }

              }
            }

            /** create new seperation portion for [ always */
            const newControlContent = lastNewControl.getNewControlContent();
            const newControlPlaceHolder = newControlContent.getPlaceHolder();
            const newControlStartBorderPortion = lastNewControl.getStartBorderPortion();
            newControlStartBorderPortion.textProperty = textProperty;

            if (bPlaceHolder) {
              // newControlPlaceHolder.textProperty = textProperty.copy();
              // placeholder always has default text prop - not any more, will override using showPlaceholder
              // here just set a default text props
              newControlPlaceHolder.textProperty = new TextProperty(getDefaultFont(insertFileObj.logicDocument));
            }

            lastNewControl.setPlaceHolder(bPlaceHolder);

            if (!nextNode) { // null. means at end of para
              newControlPlaceHolder.content = newControlStartBorderPortion.content; // R.W: not in use
            }

            para.addToContent(runCount, newControlStartBorderPortion);
            runCount++; // not matter

            unclosedNewControls.push(lastNewControl);

            /** set map in content control manager no matter what */
            // newControlManager.getNameMap()
            //   .set(lastNewControl.getNewControlName(), lastNewControl);
          }

        } else if (paraChild.tagName === 'sdtEnd') { /** sdtEnd */

          showPlaceholder = false;
          runCount = this.traverseSdtEndInsertFile2(bInsertFile, null, paraChild, para, runCount,
            ContentControlDescObj, insertFileObj);
        } else if ('w:ins' === paraChild.tagName) {
          const attrs = paraChild.attributes;
          const revsionChildren = paraChild.children as rtNode[];
          const length = revsionChildren ? revsionChildren.length : 0;

          if (attrs && 0 < length) {
            const controlObj = { unclosedNewControls };
            for (let index = 0; index < length; index++) {
              const runChild = revsionChildren[index];
              RunReader.tTraverseRun(runChild, para, runCount, controlObj, uniqueImagelist, bInsertFile, null,
                  headerFooter, false, null, insertFileObj);
              runCount++;
            }
          }
        }
      }
    } // for

    para.setParaEndProperty();

    if (container == null) {
      insertFileObj.content.push(para);
    } else {
      container.addToContent(container.getContent().length, para);
    }

  } // traverseP

  private static tTraverseSdt(bInsertFile: boolean = false, document: Document, paraChild: rtNode,
                              newControlNamesTempArr: any, para: Paragraph, runCount: number,
                              container: Region | TableCell = null, i: number,
                              uniqueImagelist: Map<string, IUniqueImageProps>,
                              headerFooter: HeaderFooter = null,
                              ContentControlDescObj: IContentControlDescObj,
                              modeFonts: IModeFonts = null, documentVersion: number,
                              insertFileObj?: IInsertFileContent): number {
    let rCount = runCount;
    // const documentVersion = document != null ? document.getDocumentVersion() : 0;
    const readNewControls = {};
    if (ParagraphReader.shouldReadContentControl((paraChild as IParseXmlNode).type === undefined && bInsertFile,
        document, paraChild.attributes['type'])) {

      let borderStringStart = '';
      let bPlaceHolder = false; // this prop is in portion
      const {newControlManager, newControlProperty, unclosedNewControls, bHeaderFooter } = ContentControlDescObj;

      let nameAttr = paraChild.attributes['name'];
      if (nameAttr) {
        nameAttr = newControlProperty.newControlName = bHeaderFooter === true ? newControlManager.makeUniqueName(+paraChild.attributes['type'], nameAttr) : nameAttr;
        newControlProperty.newControlName = nameAttr;
        if (bInsertFile === true) {
          newControlProperty._bRetainCascade = insertFileObj?.bRetainCascade;
        }
        readNewControls[nameAttr] = new ReadStruct(newControlProperty);
        // check duplicate
        try {
          // newControlManager = document.getNewControlManager();
          if (!newControlManager.checkNewControlName(newControlProperty.newControlName)) {
            if (!bInsertFile) {
              throw new Error('find duplicate control name out of inserting file');
            } else {
              // when inserting file, also check the names which not set in newcontrolnames map yet
              // if (newControlNamesTempArr.includes(newControlProperty.newControlName)) {
              //   newControlProperty.newControlName = newControlManager.makeUniqueNameByInsertFile(
              //     newControlProperty.newControlType, newControlNamesTempArr, newControlProperty.newControlName);
              //   newControlNamesTempArr.push(newControlProperty.newControlName);
              // } else {
              //   // indicate from insert file
              //   newControlProperty.bInsertFile = true;
              // }
            }
          }
        } catch (error) {
          window.dispatchEvent(new ErrorEvent('error', { message: ErrorMessages.XmlCorrupted, error }));
        }
      }

      if (paraChild.attributes['type']) {
        // newControlProperty.newControlType = Number(paraChild.attributes.getNamedItem('type').nodeValue);
        // newControlProperty.newControlType = +paraChild.attributes['type'];
        const type = +paraChild.attributes['type'];
        if (type === NewControlType.SignatureBox) {
          newControlProperty.bReadSign = true;
        }
        newControlProperty.newControlType = type;
      }

      let textProperty: TextProperty = null;
      if (modeFonts != null) {
        const {defaultFont, regionTitleFont} = modeFonts;

        textProperty = new TextProperty(getDefaultFont(document));
        if (defaultFont != null) {

          const fontFamilyVal = getFontFamilyVal(defaultFont.fontFamily);

          textProperty.fontFamily = fontFamilyVal;
          textProperty.fontSize = +defaultFont.fontSize;
        }
        if (regionTitleFont != null) {
          // no need elaborate here?
        }
        // console.log(textProperty)
      }
      const runChildren = paraChild.children;
      let sdtContentNode = null; // save to iterate after [ portion is inserted
      let showPlaceholder = false;
      for (const runChild of runChildren) {
        if (typeof runChild === 'object') {
          if ('w:rPr' === runChild.tagName) {
            // textProperty = new TextProperty();
            textProperty = (textProperty != null) ? textProperty : new TextProperty(getDefaultFont(document));
            RunReader.tTraverseRunProperty(runChild, textProperty, document);
            // } else if (runChild.nodeName !== '#text') {
          } else {
            const textNode = runChild.children[0];
            if (StructXmlName.Sdtcontent === runChild.tagName) {
              sdtContentNode = runChild;
              continue;
            }

            if (StructXmlName.ShowPlaceholder === runChild.tagName) {
              if (textNode === '1') {
                showPlaceholder = true;
              }
              continue;
            }
            if (StructXmlName.BorderString === runChild.tagName) {
              if (textNode && typeof textNode === 'string' ) {
                borderStringStart = safeDecodeURIComponent(textNode as any, documentVersion);
              }

              continue;
            }
            const name = newControlProperty.newControlName;
            readNewControls[name]?.readProp(textNode, documentVersion, runChild);
          }
        }

      } // end for

      // console.log(newControlProperty);
      // console.log(circularParse(circularStringify(newControlProperty)));

      if (null == textProperty) {
        textProperty = new TextProperty(getDefaultFont(document));
      }

      // console.log(newControlProperty)
      // control with duplicate name should already be handled
      newControlProperty.bInsertFile = bInsertFile;
      let currentControl = newControlManager.createNewControl(para, newControlProperty);
      if (currentControl == null) {
        // tslint:disable-next-line: no-console
        // console.warn('duplicate control name detected, should be fixed automatically:)');
        const source = newControlProperty.bInsertFile ? FileSource.InsertFile : null;
        newControlProperty.newControlName = newControlManager.makeUniqueName(newControlProperty.newControlType,
          newControlProperty.newControlName, source);
        currentControl = newControlManager.createNewControl(para, newControlProperty);
      }

      // when insert file, have a temp controlNames array
      if (bInsertFile) {
        if (insertFileObj?.bRetainCascade) {
            const { cascade, eventInfo } = newControlProperty;
            if (cascade?.length) {
                currentControl.setCascades1(newControlProperty.cascade);
            }
            if (eventInfo) {
                if (!insertFileObj.insertEventInfos) insertFileObj.insertEventInfos = new Map();
                insertFileObj.insertEventInfos.set(currentControl, eventInfo);
            }

            if (newControlProperty.newControlName !== currentControl.getNewControlName()) {
              insertFileObj.reNameNewControls.set(newControlProperty.newControlName, currentControl.getNewControlName());
            }
        }
        if (newControlNamesTempArr.length === 0) {
          // if newControlNames is not empty, assign value to temp array
          const newControlNamesMap = newControlManager.getNameMap();
          if (newControlNamesMap.size !== 0) {
            for (const key of newControlNamesMap.keys()) {
              newControlNamesTempArr.push(key);
            }
          }
        }

        if (false === newControlNamesTempArr.includes(currentControl.getNewControlName())) {
          newControlNamesTempArr.push(currentControl.getNewControlName());
        }
      }

      // reset newControlProperty
      this.resetNewControlProperty(newControlProperty);

      // ------

      /** create new seperation portion for [ always */
      const newControlContent = currentControl.getNewControlContent();
      const newControlPlaceHolder = newControlContent.getPlaceHolder();
      const newControlStartBorderPortion = currentControl.getStartBorderPortion();
      newControlStartBorderPortion.textProperty = textProperty;

      if (currentControl.isCheckBox() || currentControl.isMultiAndRadio()) {
        showPlaceholder = false;
      }

      if (currentControl.isCheckBox() && currentControl.isMustInput()) {
        currentControl.setMustInput(false);
      }

      // TODO: if it's reasonable
      bPlaceHolder = showPlaceholder;

      if (bPlaceHolder) {
        // newControlPlaceHolder.textProperty = textProperty.copy();
        // placeholder always has default text prop - not any more, will override using showPlaceholder
        // here just set a default text props
        newControlPlaceHolder.textProperty = new TextProperty(getDefaultFont(document));
      }

      currentControl.setPlaceHolder(bPlaceHolder);

      // if (!nextNode) { // null. means at end of para
      //   newControlPlaceHolder.content = newControlStartBorderPortion.content; // R.W: not in use
      // }

      para.addToContent(rCount, newControlStartBorderPortion);
      rCount++; // not matter

      // still need to add cuz isplaceholderportion
      unclosedNewControls.push(currentControl);

      /** set map in content control manager no matter what */
      // newControlManager.getNameMap()
      //   .set(lastNewControl.getNewControlName(), lastNewControl);

      // add sdtContent items
      if (sdtContentNode != null) {
        // w:r or sdt, TODO revision
        const commentManager = document.getCommentManager();
        const sdtChildren = sdtContentNode.children;
        for (let j = 0, len = sdtChildren.length; j < len; j++) {
          const sdtChild = sdtChildren[j];
          if (typeof sdtChild === 'object') {
            const sdtChildName = sdtChild.tagName;
            if (sdtChildName === 'w:r') {
              const controlObj = { unclosedNewControls };

              // if it's title portion/placeholder, ignore it
              const titlePortionType = this.tIsTitlePortion(sdtChild, controlObj, container, i, document,
                sdtChildren, j, true, documentVersion);
              if (titlePortionType === TitlePortionType.None) {
                if (this.tIsPlaceholderPortion(showPlaceholder, sdtChild, controlObj, documentVersion) === false) {
                  RunReader.tTraverseRun(sdtChild, para, rCount, controlObj, uniqueImagelist, bInsertFile, document,
                    headerFooter, false, modeFonts);
                  rCount++;
                } else {
                  const phPortion = RunReader.tTraverseRun(sdtChild, para, rCount, controlObj, uniqueImagelist,
                    bInsertFile, document, headerFooter, showPlaceholder, modeFonts);
                  this.setPlaceholderTextProperty(phPortion, controlObj);
                }
              } else if (titlePortionType === TitlePortionType.Region) {
                // title portion in region is not like in struct
                rCount++;
              }
            } else if (sdtChildName === 'sdt') {
              // TODO: may need recursive
              rCount = this.tTraverseSdt(bInsertFile, document, sdtChild,
                newControlNamesTempArr, para, rCount, container, i,
                uniqueImagelist, headerFooter, ContentControlDescObj, modeFonts, documentVersion, insertFileObj);
            } else if ('w:ins' === sdtChildName || 'w:del' === sdtChildName) {
              const attrs = sdtChild.attributes;
              const revsionChildren = sdtChild.children as rtNode[];
              const length = revsionChildren ? revsionChildren.length : 0;

              if (attrs && 0 < length) {
                const type = ('w:ins' === sdtChildName ? ReviewType.Add : ReviewType.Remove);
                const reviewInfo = this.getReviewInfo(attrs, document, type);
                const revisionManager = document.getRevisionsManager();
                const userId = attrs['id'] ? attrs['id'] : null;
                const userName = attrs['author'] ? attrs['author'] : null;
                const bLostInfo = (!userId && !userName);

                if (bLostInfo) {
                  if (ReviewType.Remove === type) {
                    continue;
                  }
                }

                // const controlObj = {bFirstSiblingOfSdtStart, unclosedNewControls, bFollowedBySdtEnd};
                const controlObj = { unclosedNewControls };
                // for (let index = 0; index < length; index++) {
                //   const runChild = revsionChildren[index];

                //   RunReader.tTraverseRun(runChild, para, rCount, controlObj, uniqueImagelist, bInsertFile, document,
                //     headerFooter, false, modeFonts);
                //   para.content[rCount].setReviewTypeWithInfo(type, reviewInfo.copy());
                //   revisionManager.addRevisionByReviewInfo(para, reviewInfo);
                //   rCount++;
                // }

                if (unclosedNewControls && unclosedNewControls[unclosedNewControls.length - 1]?.isNewDateBox()
                  && ReviewType.Add === type && !reviewInfo['firstDate'] && !reviewInfo.getSavedRecord()) {
                    reviewInfo['bFirstSet'] = true;
                }

                for (let index = 0; index < length; index++) {
                  const runChild = revsionChildren[index];

                  // if it's title portion/placeholder, ignore it
                  const titlePortionType = this.tIsTitlePortion(runChild, controlObj, container, i, document,
                    sdtChildren, j, true, documentVersion);
                  if (titlePortionType === TitlePortionType.None) {
                    if (this.tIsPlaceholderPortion(showPlaceholder, runChild, controlObj, documentVersion) === false) {
                      // TODO: since title is in portion with [,
                      // no elegant way yet to wrap revision element around title

                      if ('w:ins' === runChild.tagName && 'w:del' === sdtChildName) {
                        const secondRevInfo = this.getReviewInfo(runChild.attributes, document);
                        const secondSetting = secondRevInfo.getRevisionSetting();
                        const reviewSetting = reviewInfo.getRevisionSetting();

                        reviewInfo.setRevisionSetting(secondSetting);
                        reviewInfo.setDeleteInfo(secondRevInfo);
                        secondRevInfo.setRevisionSetting(reviewSetting);
                        continue;
                      }

                      RunReader.tTraverseRun(runChild, para, rCount, controlObj, uniqueImagelist,
                        bInsertFile, document, headerFooter, false, modeFonts);

                      if (!bLostInfo) {
                        para.content[rCount].setReviewTypeWithInfo(type, reviewInfo.copy());
                        revisionManager.addRevisionByReviewInfo(para, reviewInfo);
                      }
                      rCount++;
                    } else {
                      const phPortion = RunReader.tTraverseRun(runChild, para, rCount, controlObj, uniqueImagelist,
                        bInsertFile, document, headerFooter, showPlaceholder, modeFonts);
                      this.setPlaceholderTextProperty(phPortion, controlObj);

                      if (!bLostInfo) {
                        this.setPlaceholderRevision(type, reviewInfo.copy(), controlObj);
                        revisionManager.addRevisionByReviewInfo(para, reviewInfo);
                      }
                    }
                  } else if (titlePortionType === TitlePortionType.Region) {
                    // title portion in region is not like in struct
                    rCount++;
                  }
                }

                // if (0 === length) {
                //   para.content[para.content.length - 1].setReviewTypeWithInfo(type, reviewInfo);
                //   rCount++;
                // }
              }
            } else if (sdtChildName === 'w:comment') {
                const startAttr = sdtChild.attributes['start'];
                const idAttr = sdtChild.attributes['id'];
                if (sdtChild.children && startAttr && idAttr) {
                    let commentPortion: ParaPortion;
                    let commentDataNode: rtNode;
                    for (const child of sdtChild.children) {
                        if (typeof child === 'object') {
                            if (child.tagName === 'w:r') {
                                const controlObj = { unclosedNewControls };
                                commentPortion = RunReader.tTraverseRun(sdtChild, para, rCount++,
                                    controlObj, uniqueImagelist,
                                    bInsertFile, document, headerFooter,
                                    false, modeFonts);
                            } else if (child.tagName === 'w:reply') {
                                commentDataNode = child;
                            }
                        }
                    }
                    if (commentPortion) {
                        const bStart = startAttr === '1';
                        const comment = commentManager.prepareReadedComment(idAttr, commentPortion, bStart);
                        if (comment) {
                            commentPortion.addComment(bStart, comment.getId());
                            if (bStart && commentDataNode) {
                                this.tTraverseComment(commentDataNode, comment);
                            }
                        }
                    }
                }
            } else {
              // unknown limbo
            }
          }
        }
      }

      // --- start of sdtend
      showPlaceholder = false;
      // const currentControl = unclosedNewControls[unclosedNewControls.length - 1];
      // const newControlStartBorderPortion = currentControl.getStartBorderPortion();
      // let textProperty: TextProperty = null;
      // after [ is added, add real portion contents

      if (newControlStartBorderPortion.bPlaceHolder) { // find current control's bPlaceholder
        // as of 7/8/2020 only startBorderPortion.bPlaceHolder stays updated
        para.addToContent(rCount, currentControl.getNewControlContent()
          .getPlaceHolder());
        rCount++;

      }

      if (null == textProperty) {
        textProperty = new TextProperty(getDefaultFont(document));
      }

      /** create new seperation portion for ] always */
      const newControlEndBorderPortion = currentControl.getEndBorderPortion();
      newControlEndBorderPortion.textProperty = textProperty.copy();
      para.addToContent(rCount, newControlEndBorderPortion); // will also set portion's parent!
      rCount++;
      if (currentControl.isHidden() && !bInsertFile) {
        currentControl.getNewControlContent().setHidden(true, currentControl.isNewSection());
      }

      unclosedNewControls.pop();

      /** if it's control check/radiobutton, need extra step to fill in inner content */
      if (currentControl instanceof NewControlCheck || currentControl instanceof NewControlRadio) {
        currentControl.addContent(); // lsy's method
        // tslint:disable-next-line: no-console
        // console.warn('para delete portions !', rCount, para.content.length - 1)
        if (currentControl.isMultiAndRadio()) {
          (currentControl as any).checkCheckBoxSelect();
        }
        rCount = para.content.length - 1;
      }

      /** set map in content control manager for any struct */
      // newControlNames[]  -> all structs
      if (!bInsertFile) {
        newControlManager.getNameMap()
          .set(currentControl.getNewControlName(), currentControl);
      }

      if (unclosedNewControls.length === 0) { // root
        if (!bInsertFile) {
          // if it's a root content control, add to "root content control array"(map already added in sdtstart)
          // newControls[]  -> root structs
          // NOTE the order of elements in the array SHOULD indicate structs appearance in doc
          if (headerFooter == null) {
            newControlManager.getAllNewControls()
              .splice(newControlManager.getAllNewControls().length, 0, currentControl);
          } else {
            if (headerFooter.isHeader()) {
              const structCount = ContentControlDescObj.headerStructCount;
              // console.log(structCount)
              newControlManager.getAllNewControls()
                .splice(structCount, 0, currentControl);
              ContentControlDescObj.headerStructCount++;
            } else {
              // footer
              newControlManager.getAllNewControls()
                .splice(newControlManager.getAllNewControls().length, 0, currentControl);
            }
          }
        } else {
          // pasteControl need to fill up when insertFile
          // control order is important
          // const contentPos = document.getCurContentPosInDoc(false, true);
          newControlManager.addPasteControl({ control: currentControl, contentPos: null });
          // TODO?
        }

      } else {
        const lastElemInUnclosedNewControls = unclosedNewControls[unclosedNewControls.length - 1];
        // 非根节点时，插入文件需set name map
        if (bInsertFile) {
          // newControlManager.getNameMap()
          // .set(currentControl.getNewControlName(), currentControl);
          //  (lastElemInUnclosedNewControls && lastElemInUnclosedNewControls.getNewControlName())
          if ((paraChild as IParseXmlNode).type !== undefined) {
            newControlManager.addPasteControl({ control: currentControl, contentPos: undefined,
              parentName: (lastElemInUnclosedNewControls && lastElemInUnclosedNewControls.getNewControlName())});
            return rCount;
          } else {
          //   newControlManager.addNewControlInMap(currentControl);
            newControlManager.addPasteControl({ control: currentControl, contentPos: null, parentName: lastElemInUnclosedNewControls?.getNewControlName() });
          }
        }
        // add to leafList. two-way
        currentControl.setParent(lastElemInUnclosedNewControls);
        lastElemInUnclosedNewControls.getLeafList()
          .push(currentControl);
      }
    }
    return rCount;
  }

  private static tTraverseSection(bInsertFile: boolean = false, document: Document, paraChild: rtNode,
                              newControlNamesTempArr: any, para: Paragraph, runCount: number,
                              container: Region | TableCell = null, i: number,
                              uniqueImagelist: Map<string, IUniqueImageProps>,
                              headerFooter: HeaderFooter = null,
                              ContentControlDescObj: IContentControlDescObj,
                              modeFonts: IModeFonts = null, documentVersion: number): number {
    let rCount = runCount;
    // const documentVersion = document != null ? document.getDocumentVersion() : 0;
    const readNewControls = {};
    let nameAttr = paraChild.attributes['name'];
    // if (strcuts.start[nameAttr]) {
    //   console.warn('this is start border is error: ' + nameAttr);
    //   continue;
    // }
      const {newControlManager, newControlProperty, unclosedNewControls, bHeaderFooter } = ContentControlDescObj;
    if (ParagraphReader.shouldReadContentControl((paraChild as IParseXmlNode).type === undefined && bInsertFile,
        document, paraChild.attributes['type'])) {
      // let runChild = paraChild.childNodes[0];
      const runChildren = paraChild.children;
      let borderStringStart = '';
      // let bPlaceHolder = false; // this prop is in portion
      let showPlaceholder = false;

      if (nameAttr) {
        nameAttr = newControlProperty.newControlName = bHeaderFooter === true ? newControlManager.makeUniqueName(+paraChild.attributes['type'], nameAttr) : nameAttr;

        // check duplicate
        try {
          // newControlManager = document.getNewControlManager();
          if (!newControlManager.checkNewControlName(newControlProperty.newControlName)) {
            if (!bInsertFile) {
              throw new Error('find duplicate control name out of inserting file');
            }
          }
        } catch (error) {
          window.dispatchEvent(new ErrorEvent('error', { message: ErrorMessages.XmlCorrupted, error }));
        }
      }

      if (paraChild.attributes['type']) {
        readNewControls[nameAttr] = new ReadStruct(newControlProperty);
        // newControlProperty.newControlType = Number(paraChild.attributes.getNamedItem('type').nodeValue);
        newControlProperty.newControlType = +paraChild.attributes['type'];
        // TODO: section shouldnt be sign box itself
      }
      // strcuts.start[nameAttr] = true;
      let textProperty: TextProperty = null;
      let seContentNode;
      for (const runChild of runChildren) {
        if (typeof runChild === 'object') {
          if ('w:rPr' === runChild.tagName) {
            textProperty = new TextProperty(getDefaultFont(document));
            RunReader.tTraverseRunProperty(runChild, textProperty, document);
            // } else if (runChild.nodeName !== '#text') {
          } else {
            const textNode = runChild.children[0];
            if (StructXmlName.Secontent === runChild.tagName) {
              seContentNode = runChild;
              continue;
            }

            if (StructXmlName.ShowPlaceholder === runChild.tagName) {
              if (textNode === '1') {
                showPlaceholder = true;
              }
              continue;
            }
            if (StructXmlName.BorderString === runChild.tagName) {
              if (textNode && typeof textNode === 'string' ) {
                borderStringStart = safeDecodeURIComponent(textNode as any, documentVersion);
              }

              continue;
            }
            // console.log(textNode)
            readNewControls[nameAttr].readProp(textNode, documentVersion, runChild);
          }
        }

      } // end for

      // console.log(newControlProperty);
      // console.log(circularParse(circularStringify(newControlProperty)));

      if (null == textProperty) {
        textProperty = new TextProperty(getDefaultFont(document));
      }

      // control with duplicate name should already be handled
      newControlProperty.bInsertFile = bInsertFile;
      let lastNewControl = newControlManager.createNewControl(para, newControlProperty);
      if (lastNewControl == null) {
        // tslint:disable-next-line: no-console
        // console.warn('duplicate control name detected, should be fixed automatically:)');
        const source = newControlProperty.bInsertFile ? FileSource.InsertFile : null;
        newControlProperty.newControlName = newControlManager.makeUniqueName(newControlProperty.newControlType,
          newControlProperty.newControlName, source);
        lastNewControl = newControlManager.createNewControl(para, newControlProperty);
      }
      // when insert file, have a temp controlNames array
      if (bInsertFile) {
        if (newControlNamesTempArr.length === 0) {
          // if newControlNames is not empty, assign value to temp array
          const newControlNamesMap = newControlManager.getNameMap();
          if (newControlNamesMap.size !== 0) {
            for (const key of newControlNamesMap.keys()) {
              newControlNamesTempArr.push(key);
            }
          }
        }

        if (false === newControlNamesTempArr.includes(lastNewControl.getNewControlName())) {
          newControlNamesTempArr.push(lastNewControl.getNewControlName());
        }
      }

      // reset newControlProperty
      this.resetNewControlProperty(newControlProperty);

      // lastNewControl.initPlaceHolderDefaultTextProperty(
      //   lastNewControl.getNewControlContent()
      //   .getPlaceHolder().textProperty
      //   .copy());

      /** figure out portion.bPlaceHolder's val */
      /** if <sdtStart> is not strictly followed by <sdtEnd>, bPlaceHolder is false */
      // const nextNode = ParagraphReader.tGetTheFirstNonTextSiblingFollowed(i, runChildren);
      // console.log(nextNode)
      // 11.3.2020: the next node MUST BE struct portion or PLACEHOLDER PORTION
      // console.log(paraChild)
      // console.log(nextNode)
      // if (nextNode && nextNode.tagName === 'sdtEnd') { // TODO, seems not problematic:('sectionEnd')
      //   // consider compatibility of 'no placeholder portion' case
      //   // bFirstSiblingOfSdtStart = false; // actually no use as of 11.4.2020

      // } else {

      //   bPlaceHolder = false;
      //   if (nextNode) { // look out for null. still may be problematic
      //     // bFirstSiblingOfSdtStart = true;

      //     // if a struct has title + placeholder, still bPlaceHolder may be true
      //     if (nextNode.tagName === 'w:r') {
      //       const potentialSdtEnd = runChildren[i + 1 + 1];
      //       let text = '';
      //       for (const nextNodeChild of nextNode.children) {
      //         if (typeof nextNodeChild === 'object' && nextNodeChild.tagName === 'w:t') {
      //           text = safeDecodeURIComponent(nextNodeChild.children[0] as string, documentVersion);
      //           break;
      //         }
      //       }
      //       // console.log(text)
      //       // console.log(lastNewControl.getTitle())
      //       if (typeof potentialSdtEnd === 'object' /*&& potentialSdtEnd.tagName === 'sdtEnd'*/) {
      //         // 1. old compatible: [ title ] 2. [ {normal portion} ] 3. new: [ placeholder ]
      //         // 12.7.2020: not correct, can still be of [title rpr] pattern
      //         if (lastNewControl.getTitle() === text) {
      //           bPlaceHolder = true;
      //         }
      //       } else {
      //         // [ title placeholder ]
      //         // no need to deal with ph text props here, just override it
      //       }
      //     }

      //   }
      // }

      /** create new seperation portion for [ always */
      const newControlContent = lastNewControl.getNewControlContent();
      const newControlPlaceHolder = newControlContent.getPlaceHolder();
      const newControlStartBorderPortion = lastNewControl.getStartBorderPortion();
      newControlStartBorderPortion.textProperty = textProperty;

      if (showPlaceholder) {
        // newControlPlaceHolder.textProperty = textProperty.copy();
        // placeholder always has default text prop - not any more, will override using showPlaceholder
        // here just set a default text props
        newControlPlaceHolder.textProperty = new TextProperty(getDefaultFont(document));
      }

      lastNewControl.setPlaceHolder(showPlaceholder);

      // if (!nextNode) { // null. means at end of para
      //   newControlPlaceHolder.content = newControlStartBorderPortion.content; // R.W: not in use
      // }

      para.addToContent(runCount, newControlStartBorderPortion);
      rCount++; // not matter

      unclosedNewControls.push(lastNewControl);

      if (seContentNode) {
        // w:r or sdt, TODO revision
        const commentManager = document.getCommentManager();
        const sdtChildren = seContentNode.children;
        for (let j = 0, len = sdtChildren.length; j < len; j++) {
          const sdtChild = sdtChildren[j];
          if (typeof sdtChild === 'object') {
            const sdtChildName = sdtChild.tagName;
            if (sdtChildName === 'w:r') {
              const controlObj = { unclosedNewControls };

              // if it's title portion/placeholder, ignore it
              const titlePortionType = this.tIsTitlePortion(sdtChild, controlObj, container, i, document,
                sdtChildren, j, true, documentVersion);
              if (titlePortionType === TitlePortionType.None) {
                if (!this.tIsPlaceholderPortion(showPlaceholder, sdtChild, controlObj, documentVersion)) {
                  RunReader.tTraverseRun(sdtChild, para, rCount, controlObj, uniqueImagelist, bInsertFile, document,
                    headerFooter, false, modeFonts);
                  rCount++;

                  if (showPlaceholder) {
                    showPlaceholder = false;
                    lastNewControl.setPlaceHolder(false);
                  }
                } else {
                  const phPortion = RunReader.tTraverseRun(sdtChild, para, rCount, controlObj, uniqueImagelist,
                    bInsertFile, document, headerFooter, showPlaceholder, modeFonts);
                  this.setPlaceholderTextProperty(phPortion, controlObj);

                  para.addToContent(rCount, newControlPlaceHolder); // will also set portion's parent!
                  rCount++;
                }
              } else if (titlePortionType === TitlePortionType.Region) {
                // title portion in region is not like in struct
                rCount++;
              }
            } else if (sdtChildName === 'sdt') {
              // TODO: may need recursive
              rCount = this.tTraverseSdt(bInsertFile, document, sdtChild,
                newControlNamesTempArr, para, rCount, container, i,
                uniqueImagelist, headerFooter, ContentControlDescObj, modeFonts, documentVersion);
            } else if (sdtChildName === 'section') {
              // TODO: may need recursive
              rCount = this.tTraverseSection(bInsertFile, document, sdtChild,
                newControlNamesTempArr, para, rCount, container, i,
                uniqueImagelist, headerFooter, ContentControlDescObj, modeFonts, documentVersion);
            } else if ('w:ins' === sdtChildName || 'w:del' === sdtChildName) {
              const attrs = sdtChild.attributes;
              const revsionChildren = sdtChild.children as rtNode[];
              const length = revsionChildren ? revsionChildren.length : 0;

              if (attrs && 0 < length) {
                const type = ('w:ins' === sdtChildName ? ReviewType.Add : ReviewType.Remove);
                const reviewInfo = this.getReviewInfo(attrs, document, type);
                const revisionManager = document.getRevisionsManager();
                const userId = attrs['id'] ? attrs['id'] : null;
                const userName = attrs['author'] ? attrs['author'] : null;
                const bLostInfo = (!userId && !userName);

                if (bLostInfo) {
                  if (ReviewType.Remove === type) {
                    continue;
                  }
                }

                const controlObj = { unclosedNewControls };

                if (unclosedNewControls && unclosedNewControls[unclosedNewControls.length - 1]?.isNewDateBox()
                  && ReviewType.Add === type && !reviewInfo['firstDate'] && !reviewInfo.getSavedRecord()) {
                    reviewInfo['bFirstSet'] = true;
                }

                for (let index = 0; index < length; index++) {
                  const runChild = revsionChildren[index];

                  // if it's title portion/placeholder, ignore it
                  const titlePortionType = this.tIsTitlePortion(runChild, controlObj, container, i, document,
                    sdtChildren, j, true, documentVersion);
                  if (titlePortionType === TitlePortionType.None) {
                    if (!this.tIsPlaceholderPortion(showPlaceholder, runChild, controlObj, documentVersion)) {
                      // TODO: since title is in portion with [,
                      // no elegant way yet to wrap revision element around title

                      if ('w:ins' === runChild.tagName && 'w:del' === sdtChildName) {
                        const secondRevInfo = this.getReviewInfo(runChild.attributes, document);
                        const secondSetting = secondRevInfo.getRevisionSetting();
                        const reviewSetting = reviewInfo.getRevisionSetting();

                        reviewInfo.setRevisionSetting(secondSetting);
                        reviewInfo.setDeleteInfo(secondRevInfo);
                        secondRevInfo.setRevisionSetting(reviewSetting);
                        continue;
                      }

                      RunReader.tTraverseRun(runChild, para, rCount, controlObj, uniqueImagelist,
                        bInsertFile, document, headerFooter, false, modeFonts);

                      if (!bLostInfo) {
                        para.content[rCount].setReviewTypeWithInfo(type, reviewInfo.copy());
                        revisionManager.addRevisionByReviewInfo(para, reviewInfo);
                      }
                      rCount++;
                    } else {
                      const phPortion = RunReader.tTraverseRun(runChild, para, rCount, controlObj, uniqueImagelist,
                        bInsertFile, document, headerFooter, showPlaceholder, modeFonts);
                      this.setPlaceholderTextProperty(phPortion, controlObj);

                      if (!bLostInfo) {
                        this.setPlaceholderRevision(type, reviewInfo.copy(), controlObj);
                        revisionManager.addRevisionByReviewInfo(para, reviewInfo);
                      }
                      
                      para.addToContent(rCount, newControlPlaceHolder); // will also set portion's parent!
                      rCount++;
                    }
                  } else if (titlePortionType === TitlePortionType.Region) {
                    // title portion in region is not like in struct
                    rCount++;
                  }
                }

                // if (0 === length) {
                //   para.content[para.content.length - 1].setReviewTypeWithInfo(type, reviewInfo);
                //   rCount++;
                // }
              }
            } else if (sdtChildName === 'w:comment') {
                const startAttr = sdtChild.attributes['start'];
                const idAttr = sdtChild.attributes['id'];
                if (sdtChild.children && startAttr && idAttr) {
                    let commentPortion: ParaPortion;
                    let commentDataNode: rtNode;
                    for (const child of sdtChild.children) {
                        if (typeof child === 'object') {
                            if (child.tagName === 'w:r') {
                                const controlObj = { unclosedNewControls };
                                if (!this.tIsPlaceholderPortion(showPlaceholder, sdtChild, controlObj, documentVersion)) {
                                  commentPortion = RunReader.tTraverseRun(sdtChild, para, rCount++,
                                      controlObj, uniqueImagelist,
                                      bInsertFile, document, headerFooter,
                                      false, modeFonts);
                                } else {
                                  const phPortion = RunReader.tTraverseRun(sdtChild, para, rCount, controlObj, uniqueImagelist,
                                    bInsertFile, document, headerFooter, showPlaceholder, modeFonts);
                                  this.setPlaceholderTextProperty(phPortion, controlObj);

                                  para.addToContent(rCount, newControlPlaceHolder); // will also set portion's parent!
                                  rCount++;
                                }
                            } else if (child.tagName === 'w:reply') {
                                commentDataNode = child;
                            }
                        }
                    }
                    if (commentPortion) {
                        const bStart = startAttr === '1';
                        const comment = commentManager.prepareReadedComment(idAttr, commentPortion, bStart);
                        if (comment) {
                            commentPortion.addComment(bStart, comment.getId());
                            if (bStart && commentDataNode) {
                                this.tTraverseComment(commentDataNode, comment);
                            }
                        }
                    }
                }
            }
          }
        }
      }

      const newControlEndBorderPortion = lastNewControl.getEndBorderPortion();
      newControlEndBorderPortion.textProperty = textProperty;
      para.addToContent(rCount, newControlEndBorderPortion); // will also set portion's parent!
      rCount++;
      if (lastNewControl.isHidden() && !bInsertFile) {
        lastNewControl.getNewControlContent().setHidden(true, lastNewControl.isNewSection());
      }

      unclosedNewControls.pop();

      /** if it's control check/radiobutton, need extra step to fill in inner content */
      // if (lastNewControl instanceof NewControlCheck || lastNewControl instanceof NewControlRadio) {
      //   lastNewControl.addContent(); // lsy's method
      // }

      /** set map in content control manager for any struct */
      // newControlNames[]  -> all structs
      if (!bInsertFile) {
        newControlManager.getNameMap()
          .set(lastNewControl.getNewControlName(), lastNewControl);
      }

      if (unclosedNewControls.length === 0) { // root
        if (!bInsertFile) {
          // if it's a root content control, add to "root content control array"(map already added in sdtstart)
          // newControls[]  -> root structs
          // NOTE the order of elements in the array SHOULD indicate structs appearance in doc
          if (headerFooter == null) {
            newControlManager.getAllNewControls()
              .splice(newControlManager.getAllNewControls().length, 0, lastNewControl);
          } else {
            if (headerFooter.isHeader()) {
              const structCount = ContentControlDescObj.headerStructCount;
              // console.log(structCount)
              newControlManager.getAllNewControls()
                .splice(structCount, 0, lastNewControl);
              ContentControlDescObj.headerStructCount++;
            } else {
              // footer
              newControlManager.getAllNewControls()
                .splice(newControlManager.getAllNewControls().length, 0, lastNewControl);
            }
          }
        } else {
          // pasteControl need to fill up when insertFile
          // control order is important
          let contentPos: any;
          if ((paraChild as IParseXmlNode).type === undefined) {
            contentPos = document.getCurContentPosInDoc(false, true);
          }
          newControlManager.addPasteControl({ control: lastNewControl, contentPos });
          // TODO?
        }

      } else {
        // 非根节点时，插入文件需set name map
        if (bInsertFile) {
          // newControlManager.getNameMap()
          // .set(currentControl.getNewControlName(), currentControl);
          newControlManager.addNewControlInMap(lastNewControl);
        }
        // add to leafList. two-way
        const lastElemInUnclosedNewControls = unclosedNewControls[unclosedNewControls.length - 1];
        lastNewControl.setParent(lastElemInUnclosedNewControls);
        lastElemInUnclosedNewControls.getLeafList()
          .push(lastNewControl);
      }

      /** set map in content control manager no matter what */
      // newControlManager.getNameMap()
      //   .set(lastNewControl.getNewControlName(), lastNewControl);
    }
    return rCount;
  }

  private static tTraverseSectionForInsertFile(insertFileObj: IInsertFileContent, paraChild: rtNode,
                              newControlNamesTempArr: any, para: Paragraph, runCount: number,
                              container: Region | TableCell = null, i: number,
                              uniqueImagelist: Map<string, IUniqueImageProps>,
                              headerFooter: HeaderFooter = null,
                              ContentControlDescObj: IContentControlDescObj,
                              documentVersion: number): number {
    let rCount = runCount;
    const document = insertFileObj.logicDocument;
    // const documentVersion = document != null ? document.getDocumentVersion() : 0;
    const readNewControls = {};
    const nameAttr = paraChild.attributes['name'];
    // if (strcuts.start[nameAttr]) {
    //   console.warn('this is start border is error: ' + nameAttr);
    //   continue;
    // }
      const {newControlManager, newControlProperty, unclosedNewControls } = ContentControlDescObj;
    if (ParagraphReader.shouldReadContentControl((paraChild as IParseXmlNode).type === undefined,
        document, paraChild.attributes['type'])) {
      // let runChild = paraChild.childNodes[0];
      const runChildren = paraChild.children;
      let borderStringStart = '';
      // let bPlaceHolder = false; // this prop is in portion
      let showPlaceholder = false;

      if (nameAttr) {
        newControlProperty.newControlName = nameAttr;
      }

      newControlProperty.bInsertFile = true;
      if (paraChild.attributes['type']) {
        readNewControls[nameAttr] = new ReadStruct(newControlProperty);
        // newControlProperty.newControlType = Number(paraChild.attributes.getNamedItem('type').nodeValue);
        newControlProperty.newControlType = +paraChild.attributes['type'];
        // TODO: section shouldnt be sign box itself
      }
      // strcuts.start[nameAttr] = true;
      let textProperty: TextProperty = null;
      let seContentNode;
      for (const runChild of runChildren) {
        if (typeof runChild === 'object') {
          if ('w:rPr' === runChild.tagName) {
            textProperty = new TextProperty(getDefaultFont(document));
            RunReader.tTraverseRunProperty(runChild, textProperty, document);
            // } else if (runChild.nodeName !== '#text') {
          } else {
            const textNode = runChild.children[0];
            if (StructXmlName.Secontent === runChild.tagName) {
              seContentNode = runChild;
              continue;
            }

            if (StructXmlName.ShowPlaceholder === runChild.tagName) {
              if (textNode === '1') {
                showPlaceholder = true;
              }
              continue;
            }
            if (StructXmlName.BorderString === runChild.tagName) {
              if (textNode && typeof textNode === 'string' ) {
                borderStringStart = safeDecodeURIComponent(textNode as any, documentVersion);
              }

              continue;
            }
            // console.log(textNode)
            readNewControls[nameAttr].readProp(textNode, documentVersion, runChild);
          }
        }

      } // end for

      // console.log(newControlProperty);
      // console.log(circularParse(circularStringify(newControlProperty)));

      if (null == textProperty) {
        textProperty = new TextProperty(getDefaultFont(document));
      }

      // control with duplicate name should already be handled
      let lastNewControl = newControlManager.createNewControl(para, newControlProperty);
      if (lastNewControl == null) {
        // tslint:disable-next-line: no-console
        // console.warn('duplicate control name detected, should be fixed automatically:)');
        const source = newControlProperty.bInsertFile ? FileSource.InsertFile : null;
        newControlProperty.newControlName = newControlManager.makeUniqueName(newControlProperty.newControlType,
          newControlProperty.newControlName, source);
        lastNewControl = newControlManager.createNewControl(para, newControlProperty);
      }

      // when insert file, have a temp controlNames array
      if (newControlNamesTempArr.length === 0) {
        // if newControlNames is not empty, assign value to temp array
        const newControlNamesMap = newControlManager.getNameMap();
        if (newControlNamesMap.size !== 0) {
          for (const key of newControlNamesMap.keys()) {
            newControlNamesTempArr.push(key);
          }
        }

        if (false === newControlNamesTempArr.includes(lastNewControl.getNewControlName())) {
          newControlNamesTempArr.push(lastNewControl.getNewControlName());
        }
      }

      if (insertFileObj.bRetainCascade && newControlProperty.newControlName !== lastNewControl.getNewControlName()) {
        insertFileObj.reNameNewControls.set(newControlProperty.newControlName, lastNewControl.getNewControlName());
      }

      // reset newControlProperty
      this.resetNewControlProperty(newControlProperty);

      /** create new seperation portion for [ always */
      const newControlContent = lastNewControl.getNewControlContent();
      const newControlPlaceHolder = newControlContent.getPlaceHolder();
      const newControlStartBorderPortion = lastNewControl.getStartBorderPortion();
      newControlStartBorderPortion.textProperty = textProperty;

      if (showPlaceholder) {
        // newControlPlaceHolder.textProperty = textProperty.copy();
        // placeholder always has default text prop - not any more, will override using showPlaceholder
        // here just set a default text props
        newControlPlaceHolder.textProperty = new TextProperty(getDefaultFont(document));
      }

      lastNewControl.setPlaceHolder(showPlaceholder);

      // if (!nextNode) { // null. means at end of para
      //   newControlPlaceHolder.content = newControlStartBorderPortion.content; // R.W: not in use
      // }

      para.addToContent(runCount, newControlStartBorderPortion);
      rCount++; // not matter
      const lastSection = unclosedNewControls[unclosedNewControls.length - 1];
      unclosedNewControls.push(lastNewControl);

      newControlManager.addPasteControl({ control: lastNewControl, contentPos: null, parentName: lastSection?.getNewControlName()});
      insertFileObj.insertFileAllNewControlNames.set(lastNewControl.getNewControlName(), lastNewControl);

      if (seContentNode) {
        // w:r or sdt, TODO revision
        const commentManager = document.getCommentManager();
        const sdtChildren = seContentNode.children;
        for (let j = 0, len = sdtChildren.length; j < len; j++) {
          const sdtChild = sdtChildren[j];
          if (typeof sdtChild === 'object') {
            const sdtChildName = sdtChild.tagName;
            if (sdtChildName === 'w:r') {
              const controlObj = { unclosedNewControls, buttons:  insertFileObj.buttons};

              // if it's title portion/placeholder, ignore it
              const titlePortionType = this.tIsTitlePortion(sdtChild, controlObj, container, i, document,
                sdtChildren, j, true, documentVersion);
              if (titlePortionType === TitlePortionType.None) {
                if (!this.tIsPlaceholderPortion(showPlaceholder, sdtChild, controlObj, documentVersion)) {
                  RunReader.tTraverseRun(sdtChild, para, rCount, controlObj, uniqueImagelist, true, document,
                    headerFooter, false, null, insertFileObj);
                  rCount++;

                  if (showPlaceholder) {
                    showPlaceholder = false;
                    lastNewControl.setPlaceHolder(false);
                  }
                } else {
                  const phPortion = RunReader.tTraverseRun(sdtChild, para, rCount, controlObj, uniqueImagelist,
                    true, document, headerFooter, showPlaceholder, null, insertFileObj);
                  this.setPlaceholderTextProperty(phPortion, controlObj);

                  para.addToContent(rCount, newControlPlaceHolder); // will also set portion's parent!
                  rCount++;
                }
              } else if (titlePortionType === TitlePortionType.Region) {
                // title portion in region is not like in struct
                rCount++;
              }
            } else if (sdtChildName === 'sdt') {
              // TODO: may need recursive
              rCount = this.tTraverseSdt(true, document, sdtChild,
                newControlNamesTempArr, para, rCount, container, i,
                uniqueImagelist, headerFooter, ContentControlDescObj, null, documentVersion, insertFileObj);
            } else if (sdtChildName === 'section') {
              // TODO: may need recursive
              rCount = this.tTraverseSectionForInsertFile(insertFileObj, sdtChild,
                newControlNamesTempArr, para, rCount, container, i,
                uniqueImagelist, headerFooter, ContentControlDescObj, documentVersion);
            // } else if ('w:ins' === sdtChildName || 'w:del' === sdtChildName) {
            //   const attrs = sdtChild.attributes;
            //   const revsionChildren = sdtChild.children as rtNode[];
            //   const length = revsionChildren ? revsionChildren.length : 0;
    
            //   if (attrs && 0 < length) {
            //     const controlObj = { unclosedNewControls };
            //     for (let index = 0; index < length; index++) {
            //       const runChild = revsionChildren[index];
            //       RunReader.tTraverseRun(runChild, para, rCount, controlObj, uniqueImagelist, true, null,
            //           headerFooter, false, null, insertFileObj);
    
            //       rCount++;
            //     }
            //   }
              // const attrs = sdtChild.attributes;
              // const revsionChildren = sdtChild.children as rtNode[];
              // const length = revsionChildren ? revsionChildren.length : 0;

              // if (attrs && 0 < length) {
              //   const type = ('w:ins' === sdtChildName ? ReviewType.Add : ReviewType.Remove);
              //   const reviewInfo = this.getReviewInfo(attrs, document, type);
              //   const revisionManager = document.getRevisionsManager();
              //   const userId = attrs['id'] ? attrs['id'] : null;
              //   const userName = attrs['author'] ? attrs['author'] : null;
              //   const bLostInfo = (!userId && !userName);

              //   if (bLostInfo) {
              //     if (ReviewType.Remove === type) {
              //       continue;
              //     }
              //   }

              //   const controlObj = { unclosedNewControls };

              //   if (unclosedNewControls && unclosedNewControls[unclosedNewControls.length - 1]?.isNewDateBox()
              //     && ReviewType.Add === type && !reviewInfo['firstDate'] && !reviewInfo.getSavedRecord()) {
              //       reviewInfo['bFirstSet'] = true;
              //   }

              //   for (let index = 0; index < length; index++) {
              //     const runChild = revsionChildren[index];

              //     // if it's title portion/placeholder, ignore it
              //     const titlePortionType = this.tIsTitlePortion(runChild, controlObj, container, i, document,
              //       sdtChildren, j, true, documentVersion);
              //     if (titlePortionType === TitlePortionType.None) {
              //       if (!this.tIsPlaceholderPortion(showPlaceholder, runChild, controlObj, documentVersion)) {
              //         // TODO: since title is in portion with [,
              //         // no elegant way yet to wrap revision element around title

              //         if ('w:ins' === runChild.tagName && 'w:del' === sdtChildName) {
              //           const secondRevInfo = this.getReviewInfo(runChild.attributes, document);
              //           const secondSetting = secondRevInfo.getRevisionSetting();
              //           const reviewSetting = reviewInfo.getRevisionSetting();

              //           reviewInfo.setRevisionSetting(secondSetting);
              //           reviewInfo.setDeleteInfo(secondRevInfo);
              //           secondRevInfo.setRevisionSetting(reviewSetting);
              //           continue;
              //         }

              //         RunReader.tTraverseRun(runChild, para, rCount, controlObj, uniqueImagelist,
              //           true, document, headerFooter, false);

              //         if (!bLostInfo) {
              //           para.content[rCount].setReviewTypeWithInfo(type, reviewInfo.copy());
              //           revisionManager.addRevisionByReviewInfo(para, reviewInfo);
              //         }
              //         rCount++;
              //       } else {
              //         const phPortion = RunReader.tTraverseRun(runChild, para, rCount, controlObj, uniqueImagelist,
              //           true, document, headerFooter, showPlaceholder);
              //         this.setPlaceholderTextProperty(phPortion, controlObj);

              //         if (!bLostInfo) {
              //           this.setPlaceholderRevision(type, reviewInfo.copy(), controlObj);
              //           revisionManager.addRevisionByReviewInfo(para, reviewInfo);
              //         }
                      
              //         para.addToContent(rCount, newControlPlaceHolder); // will also set portion's parent!
              //         rCount++;
              //       }
              //     } else if (titlePortionType === TitlePortionType.Region) {
              //       // title portion in region is not like in struct
              //       rCount++;
              //     }
              //   }

            } else if ('w:ins' === sdtChildName || 'w:del' === sdtChildName) {
              const attrs = sdtChild.attributes;
              const revsionChildren = sdtChild.children as rtNode[];
              const length = revsionChildren ? revsionChildren.length : 0;
    
              if (document.isTrackRevisions()) {
                if ('w:ins' === sdtChildName) {
                  if (attrs && 0 < length) {
                    const controlObj = { unclosedNewControls, buttons: insertFileObj.buttons };
                    for (let index = 0; index < length; index++) {
                      const runChild = revsionChildren[index];
                      RunReader.tTraverseRun(runChild, para, rCount, controlObj, uniqueImagelist, true, null,
                                  headerFooter, false, null, insertFileObj);
    
                      rCount++;
                    }
                  }
                }
    
                continue;
              }
    
              if (length === 1 && revsionChildren[0] as any === '') {
                continue;
              }
    
              if (attrs && 0 < length) {
                const type = ('w:ins' === sdtChildName ? ReviewType.Add : ReviewType.Remove);
                const reviewInfo = this.getReviewInfo(attrs, document, type);
                const revisionManager = document.getRevisionsManager();
                // const controlObj = {bFirstSiblingOfSdtStart, unclosedNewControls, bFollowedBySdtEnd};
                const controlObj = { unclosedNewControls, buttons: insertFileObj.buttons };
                const userId = attrs['id'] ? attrs['id'] : null;
                const userName = attrs['author'] ? attrs['author'] : null;
                const bLostInfo = (!userId && !userName);
    
                if (bLostInfo) {
                  if (ReviewType.Remove === type) {
                    continue;
                  }
                }
    
                if (unclosedNewControls && unclosedNewControls[unclosedNewControls.length - 1]?.isNewDateBox()
                  && ReviewType.Add === type && !reviewInfo['firstDate'] && !reviewInfo.getSavedRecord()) {
                    reviewInfo['bFirstSet'] = true;
                }
    
                for (let index = 0; index < length; index++) {
                  const runChild = revsionChildren[index];
                  if ('w:ins' === runChild.tagName && 'w:del' === sdtChildName) {
                    const secondRevInfo = this.getReviewInfo(runChild.attributes, document);
                    const secondSetting = secondRevInfo.getRevisionSetting();
                    const reviewSetting = reviewInfo.getRevisionSetting();
    
                    reviewInfo.setRevisionSetting(secondSetting);
                    reviewInfo.setDeleteInfo(secondRevInfo);
                    secondRevInfo.setRevisionSetting(reviewSetting);
                    continue;
                  }
                  if (!runChild || runChild.children == null) {
                    continue;
                  }
    
                      RunReader.tTraverseRun(runChild, para, rCount, controlObj, uniqueImagelist, true, null,
                                  headerFooter, false, null, insertFileObj);
    
                  if (!this.checkPortionRevisionMerge(para, rCount, type, reviewInfo)) {
                    if (!bLostInfo) {
                      para.content[rCount].setReviewTypeWithInfo(type, reviewInfo.copy());
                      revisionManager.addRevisionByReviewInfo(para, reviewInfo);
                    }
                    rCount++;
                  } else {
                    const portion = para.content[rCount];
                    para.content[rCount - 1].concatToContent(portion.content);
                    para.content.splice(rCount, 1);
                    para.curPos.contentPos = Math.max(0, --para.curPos.contentPos);
                  }
                }
              }
            } else if (sdtChildName === 'w:comment') {
                const startAttr = sdtChild.attributes['start'];
                const idAttr = sdtChild.attributes['id'];
                if (sdtChild.children && startAttr && idAttr) {
                    let commentPortion: ParaPortion;
                    let commentDataNode: rtNode;
                    for (const child of sdtChild.children) {
                        if (typeof child === 'object') {
                            if (child.tagName === 'w:r') {
                                const controlObj = { unclosedNewControls, buttons: insertFileObj.buttons };
                                if (!this.tIsPlaceholderPortion(showPlaceholder, sdtChild, controlObj, documentVersion)) {
                                  commentPortion = RunReader.tTraverseRun(sdtChild, para, rCount++,
                                      controlObj, uniqueImagelist,
                                      true, document, headerFooter,
                                      false, null);
                                } else {
                                  const phPortion = RunReader.tTraverseRun(sdtChild, para, rCount, controlObj, uniqueImagelist,
                                    true, document, headerFooter, showPlaceholder, null);
                                  this.setPlaceholderTextProperty(phPortion, controlObj);

                                  para.addToContent(rCount, newControlPlaceHolder); // will also set portion's parent!
                                  rCount++;
                                }
                            } else if (child.tagName === 'w:reply') {
                                commentDataNode = child;
                            }
                        }
                    }
                    if (commentPortion) {
                        const bStart = startAttr === '1';
                        const comment = commentManager.prepareReadedComment(idAttr, commentPortion, bStart);
                        if (comment) {
                            commentPortion.addComment(bStart, comment.getId());
                            if (bStart && commentDataNode) {
                                this.tTraverseComment(commentDataNode, comment);
                            }
                        }
                    }
                }
            }
          }
        }
      }

      const newControlEndBorderPortion = lastNewControl.getEndBorderPortion();
      newControlEndBorderPortion.textProperty = textProperty;
      para.addToContent(rCount, newControlEndBorderPortion); // will also set portion's parent!
      rCount++;
      // if (lastNewControl.isHidden()) {
      //   lastNewControl.getNewControlContent().setHidden(true, lastNewControl.isNewSection());
      // }

      unclosedNewControls.pop();

      /** if it's control check/radiobutton, need extra step to fill in inner content */
      // if (lastNewControl instanceof NewControlCheck || lastNewControl instanceof NewControlRadio) {
      //   lastNewControl.addContent(); // lsy's method
      // }

      if (unclosedNewControls.length === 0) { // root
          //   // if it's a root content control, add to "root content control array"(map already added in sdtstart)
          //   // newControls[]  -> root structs
          //   // NOTE the order of elements in the array SHOULD indicate structs appearance in doc
          if (headerFooter == null) {
            // newControlManager.getAllNewControls()
            //   .splice(newControlManager.getAllNewControls().length, 0, currentControl);
            // newControlManager.addPasteControl({ control: currentControl, contentPos: null });
          } else {
            if (headerFooter.isHeader()) {
              const structCount = ContentControlDescObj.headerStructCount;
              // console.log(structCount)
              // newControlManager.getAllNewControls()
              //   .splice(structCount, 0, currentControl);
              newControlManager.addPasteControl({ control: lastNewControl, contentPos: null });
              ContentControlDescObj.headerStructCount++;
            } else {
              // footer
              // newControlManager.getAllNewControls()
              //   .splice(newControlManager.getAllNewControls().length, 0, currentControl);
              newControlManager.addPasteControl({ control: lastNewControl, contentPos: null });
              insertFileObj.insertFileAllNewControlNames.set(lastNewControl.getNewControlName(), lastNewControl);
            }
          }

        } else {
          // add to leafList. two-way
          const lastElemInUnclosedNewControls = unclosedNewControls[unclosedNewControls.length - 1];
          lastNewControl.setParent(lastElemInUnclosedNewControls);
          lastElemInUnclosedNewControls.getLeafList()
            .push(lastNewControl);
      }
    }
    return rCount;
  }

  private static getTheFirstNonTextSiblingFollowed(paraChild: any): any {
    // TODO: really need?
    let nextNode = paraChild.nextElementSibling;
    if (nextNode) {
      let count = 0;
      while (nextNode.nodeName === '#text') {
        nextNode = nextNode.nextElementSibling;
        // console.log(nextNode)
        if (!nextNode) {
          break;
        }
        if (count++ > 100) {
          // tslint:disable-next-line: no-console
          console.warn('maximum loop times reached');
        }
      }
    }

    return nextNode;
  }

  private static tGetTheFirstNonTextSiblingFollowed(index: number, nodes: (string | number | rtNode)[]): rtNode {
    // TODO: really need?
    let nextNode = nodes[index + 1];
    if (nextNode) {
      let count = 0;
      while (typeof nextNode === 'object' && nextNode.tagName === '#text') {
        nextNode = nodes[index + 1 + count + 1];
        // console.log(nextNode)
        if (!nextNode) {
          break;
        }
        if (count++ > 100) {
          // tslint:disable-next-line: no-console
          console.warn('maximum loop times reached');
        }
      }
    }

    return (typeof nextNode === 'object') ? nextNode : null;
  }

  private static shouldReadContentControl(bInsertFile: boolean, document: Document, type?: string): boolean {
    // aim at checking insert file
    let bReadContentControl = true;
    if (bInsertFile && document) {
      // console.log(document.getCursorInNewControl());
      // console.log(document.getCursorInNewControlProperty());
      const controlProperty = document.getCursorInNewControlProperty();
      if (controlProperty && controlProperty.newControlType !== NewControlType.Section) {
        // if currently in cc(not section), not allow to read cc to be inserted
        bReadContentControl = false;
      }

    }

    if (null != type) {
      bReadContentControl = (null != EXTERNAL_STRUCT_TYPE[type]);
    }

    return bReadContentControl;
  }

  private static resetNewControlProperty(newControlProperty: INewControlProperty): void {
    // tslint:disable-next-line: forin
    for (const key in newControlProperty) {
      newControlProperty[key] = undefined;
    }
    newControlProperty.newControlName = null;
    newControlProperty.newControlInfo = '';
    newControlProperty.newControlSerialNumber = STD_START_DEFAULT.serialNumber;
    newControlProperty.newControlPlaceHolder = STD_START_DEFAULT.placeholder;
    newControlProperty.newControlType = STD_TYPE_DEFAULT;
    newControlProperty.isNewControlHidden = STD_START_DEFAULT.newControlHidden === 1 ? true : false;
    newControlProperty.tabJump = STD_START_DEFAULT.tabJump === 1 ? true : false;
    newControlProperty.isNewControlCanntDelete = STD_START_DEFAULT.deleteProtect === 1 ? true : false;
    newControlProperty.isNewControlCanntEdit = STD_START_DEFAULT.editProtect === 1 ? true : false;
    newControlProperty.isNewControlCanntCopy = STD_START_DEFAULT.copyProtect === 1 ? true : false;
    newControlProperty.isNewControlMustInput = STD_START_DEFAULT.isMustFill === 1 ? true : false;
    newControlProperty.isNewControlShowBorder = STD_START_DEFAULT.showBorder === 1 ? true : false;
    newControlProperty.isNewControlReverseEdit = STD_START_DEFAULT.editReverse === 1 ? true : false,
    newControlProperty.isNewControlHiddenBackground = STD_START_DEFAULT.backgroundColorHidden === 1 ? true : false,
    newControlProperty.newControlDisplayType = 1;
    newControlProperty.newControlFixedLength = STD_START_DEFAULT.fixedLength;
    newControlProperty.newControlMaxLength = STD_START_DEFAULT.maxLength;
    newControlProperty.newControlTitle = STD_START_DEFAULT.title;
    newControlProperty.cascade = [];

    newControlProperty.customProperty = [];
    newControlProperty.newControlItems = [];
    newControlProperty.prefixContent = STD_START_DEFAULT.prefixContent;
    newControlProperty.selectPrefixContent = STD_START_DEFAULT.selectPrefixContent;
    newControlProperty.separator = STD_START_DEFAULT.separator;
    newControlProperty.minValue = STD_START_DEFAULT.minValue;
    newControlProperty.maxValue = STD_START_DEFAULT.maxValue;
    newControlProperty.precision = STD_START_DEFAULT.precision;
    newControlProperty.unit = STD_START_DEFAULT.unit;
    newControlProperty.forceValidate = STD_START_DEFAULT.forceValidate === 1 ? true : false;
    newControlProperty.hideHasTitle = STD_START_DEFAULT.hideHasTitle === 1 ? true : false;

    newControlProperty.dateBoxFormat = STD_START_DEFAULT.dateBoxFormat;
    newControlProperty.startDate = STD_START_DEFAULT.startDate;
    newControlProperty.endDate = STD_START_DEFAULT.endDate;
    newControlProperty.dateTime = STD_START_DEFAULT.dateTime;

    newControlProperty.retrieve = STD_START_DEFAULT.retrieve === 1 ? true : false;
    newControlProperty.isShowValue = STD_START_DEFAULT.showValue === 1 ? true : false;

    // not in outpatient, but do anyway
    newControlProperty.checked = false;
    newControlProperty.showRight = false;
    newControlProperty.printSelected = false;
    newControlProperty.label = '';
    newControlProperty.labelCode = STD_START_DEFAULT.labelCode;
    newControlProperty.group = undefined;
    newControlProperty.supportMultLines = STD_START_DEFAULT.supportMultLines === 1 ? true : false;

    // bInsertFile?: boolean; // this shouldn't reset
    newControlProperty.customFormat = null;

    // signature box
    newControlProperty.signatureCount = STD_START_DEFAULT.signatureCount;
    newControlProperty.preText = STD_START_DEFAULT.preText;
    newControlProperty.signatureSeparator = STD_START_DEFAULT.signatureSeparator;
    newControlProperty.postText = STD_START_DEFAULT.postText;
    newControlProperty.signaturePlaceholder = STD_START_DEFAULT.signaturePlaceholder;
    newControlProperty.signatureRatio = STD_START_DEFAULT.signatureRatio;
    newControlProperty.rowHeightRestriction = STD_START_DEFAULT.rowHeightRestriction === 1 ? true : false;
    newControlProperty.bReadSign = null;
    newControlProperty.bTextBorder = undefined;

    newControlProperty.signType = STD_START_DEFAULT.signType;
    newControlProperty.alwaysShow = STD_START_DEFAULT.alwaysShow;
    newControlProperty.showSignBorder = STD_START_DEFAULT.showSignBorder === 1 ? true : false;

    // addressbox
    newControlProperty.hierarchy = null;
    newControlProperty.province = null;
    newControlProperty.city = null;
    newControlProperty.county = null;
    newControlProperty.identifier = undefined;
    newControlProperty.showType = undefined;
  }

  /**
   * check if passed run is title run
   */
  private static isTitlePortion(paraChild: any, controlObj: IControlObj,
                                container: Region | TableCell, elemIndex: number): TitlePortionType {
    // TODO: more performance improve?

    if (container instanceof TableCell) {
      // return false;
      // TODO: table need extra steps dealing with title portion?
    }

    if (controlObj.unclosedNewControls != null && controlObj.unclosedNewControls.length > 0
      && controlObj.bFirstSiblingOfSdtStart ) { // struct
      const unclosedNewControls = controlObj.unclosedNewControls;
      const curNewControl = unclosedNewControls[unclosedNewControls.length - 1];

      if (curNewControl != null) {
        const runChild = paraChild.getElementsByTagName('w:t')[0]; // w:t may not be the first child, eg: w:rPr also can
        if (runChild != null) {
          const curNewControlContent = curNewControl.getNewControlContent();
          if (curNewControlContent != null && null != runChild.childNodes[0] ) {
            if (runChild.childNodes[0].nodeValue === curNewControlContent.getTitle()) {
              return TitlePortionType.Struct;
            }
          }
        }
      }
    } else if (container != null && (elemIndex === 0 || elemIndex === 1) && container instanceof Region) {
      // TODO: may not be accurate
      // region title is totally diff. <title> is not necessarily the first elem of <rgPr>, also can be <w:pPr>
      const regionTitle = container.getTitle();
      if (regionTitle != null) {
        const runChild = paraChild.getElementsByTagName('w:t')[0]; // w:t may not be the first child, eg: w:rPr also can
        if (runChild != null) {
          if (runChild.childNodes[0].nodeValue === regionTitle) {
            return TitlePortionType.Region;
          }
        }
      }
    }

    return TitlePortionType.None;

  }

  private static tIsTitlePortion(paraChild: rtNode, controlObj: IControlObj,
                                 container: Region | TableCell, elemIndex: number,
                                 document: Document, paraChildren: rtNode[], pIndex: number,
                                 bInSdtContentNode: boolean = false, documentVersion: number): TitlePortionType {
    // TODO: more performance improve?
    // make sure it only enters as the 1st child of sdtcontent or first sibling of sectionStart

    // documentVersion = document != null ? document.getDocumentVersion() : documentVersion;
    if (container instanceof TableCell) {
      // return false;
      // TODO: table need extra steps dealing with title portion?
    }

    if (controlObj.unclosedNewControls != null && controlObj.unclosedNewControls.length > 0) { // struct
      const unclosedNewControls = controlObj.unclosedNewControls;
      const curNewControl = unclosedNewControls[unclosedNewControls.length - 1];

      if (curNewControl != null) {
        let runChild: rtNode = null;
        for (const nodeChild of paraChild.children) {
          if (typeof nodeChild === 'object' && nodeChild.tagName === 'w:t') {
            // w:t may not be the first child, eg: w:rPr also can
            runChild = nodeChild;
            break;
          }
        }
        if (runChild != null) {
          const curNewControlContent = curNewControl.getNewControlContent();
          if (curNewControlContent != null && null != runChild.children[0]) {
            if (safeDecodeURIComponent(runChild.children[0] as string, documentVersion)
              === curNewControlContent.getTitle()) {
              // rule out the case that title and ph is the same
              if (curNewControl.getPlaceHolderContent() === curNewControlContent.getTitle()) {
                // console.log(paraChildren)
                // console.log(pIndex)
                if (bInSdtContentNode === true && pIndex !== 0) {
                  return TitlePortionType.None;
                } else if (bInSdtContentNode === false) {
                  const potentialSectionStart = paraChildren[pIndex - 1];
                  // console.log(potentialSectionStart)
                  if (potentialSectionStart != null && potentialSectionStart.tagName !== 'sectionStart') {
                    return TitlePortionType.None;
                  }
                }
              }
              return TitlePortionType.Struct;
            }
          }
        }
      }
    } else if (container != null && container instanceof Region && container.isShowTitle2()
            && 1 >= container.getContent()?.length) { // 显示标题时：初始化时container就有一个para了
      // 特殊情况下第一个portion为批注
      if (elemIndex === 2) {
        if (!container.getContent()[0]
        ?.getContent()[0]
        ?.isComment()) {
          return TitlePortionType.None;
        }
      } else if (!(elemIndex === 0 || elemIndex === 1)) {
        return TitlePortionType.None;
      }
      // TODO: may not be accurate
      // region title is totally diff. <title> is not necessarily the first elem of <rgPr>, also can be <w:pPr>
      const regionTitle = container.getTitle();
      if (regionTitle != null) {
        let runChild: rtNode = null;
        for (const nodeChild of paraChild.children) {
          if (typeof nodeChild === 'object' && nodeChild.tagName === 'w:t') {
            // w:t may not be the first child, eg: w:rPr also can
            runChild = nodeChild;
            break;
          }
        }
        if (runChild != null) {
          if (safeDecodeURIComponent(runChild.children[0] as string, documentVersion) === regionTitle) {
            return TitlePortionType.Region;
          }
        }
      }
    }

    return TitlePortionType.None;

  }

  private static isPlaceholderPortion(showPlaceholder: boolean, paraChild: any, controlObj: IControlObj): boolean {

    if (showPlaceholder === true &&
      controlObj.unclosedNewControls != null && controlObj.unclosedNewControls.length > 0) {

      const unclosedNewControls = controlObj.unclosedNewControls;
      const curNewControl = unclosedNewControls[unclosedNewControls.length - 1];

      // if there exists placeholder portion, it must be the portion right before sdtend --- not related here
      const textContent = paraChild.textContent;
      const placeholderContent = curNewControl.getPlaceHolderContent();
      // console.log(placeholderContent)
      if (textContent.indexOf(placeholderContent) !== -1) {
        return true;
      }

    }

    return false;
  }

  private static tIsPlaceholderPortion(
    showPlaceholder: boolean, paraChild: rtNode,
    controlObj: IControlObj, documentVersion: number
  ): boolean {

    // documentVersion = document != null ? document.getDocumentVersion() : documentVersion;
    if (showPlaceholder === true &&
      controlObj.unclosedNewControls != null && controlObj.unclosedNewControls.length > 0) {

      const unclosedNewControls = controlObj.unclosedNewControls;
      const curNewControl = unclosedNewControls[unclosedNewControls.length - 1];

      // if there exists placeholder portion, it must be the portion right before sdtend
      // console.log(paraChild)
      // get text
      let text = '';
      for (const runChild of paraChild.children) {
        if (typeof runChild === 'object' && runChild.tagName === 'w:t') {
          const runChildChildren = runChild.children;
          if (runChildChildren.length > 0) {
            text = safeDecodeURIComponent(runChildChildren[0] as string, documentVersion);
          }
        } else if ('object' === typeof runChild &&
                ('w:drawing' === runChild.tagName || 'Barcode' === runChild.tagName
                || 'QRCode' === runChild.tagName || 'pgNum' === runChild.tagName
                || 'oMediaMath' === runChild.tagName || 'w:br' === runChild.tagName)) {
          return false;
        }
      }
      // const textContent = paraChild.children[0] as string;
      // console.log(textContent)
      const placeholderContent = curNewControl.getPlaceHolderContent();
      // console.log(placeholderContent) // already safeDecoded
      if (text.indexOf(placeholderContent) !== -1) {
        return true;
      }
      return false;

    }

    return false;
  }

  private static setPlaceholderTextProperty(portion: ParaPortion, controlObj: IControlObj): void {
    const textProperty = portion.textProperty;
    const unclosedNewControls = controlObj.unclosedNewControls;
    const curNewControl = unclosedNewControls[unclosedNewControls.length - 1];

    const newControlContent = curNewControl.getNewControlContent();
    const newControlPlaceHolder = newControlContent.getPlaceHolder();
    newControlPlaceHolder.textProperty = textProperty.copy();

    curNewControl.setPlaceHolder(true);
  }

  private static setPlaceholderRevision(type: ReviewType, reviewInfo: ReviewInfo, controlObj: IControlObj): void {
    const unclosedNewControls = controlObj.unclosedNewControls;
    const curNewControl = unclosedNewControls[unclosedNewControls.length - 1];

    const newControlContent = curNewControl.getNewControlContent();
    const newControlPlaceHolder = newControlContent.getPlaceHolder();
    const info = reviewInfo.copy();
    if (info.getSavedRecord()) {
      info['bResetDate'] = true;
    }

    newControlPlaceHolder.setReviewTypeWithInfo(type, info);
  }

  private static tTraverseSdtEnd(bInsertFile: boolean = false, document: Document, paraChild: rtNode,
                                 para: Paragraph, runCount: number, headerFooter: HeaderFooter = null,
                                 ContentControlDescObj: IContentControlDescObj): number {

    let rCount = runCount;
    const {newControlManager, unclosedNewControls } = ContentControlDescObj;
    if (ParagraphReader.shouldReadContentControl(bInsertFile, document) && unclosedNewControls.length > 0 ) {
      // if unclosedNewControls.length === 0, error exists and the sdtEnd will be deserted
      // this may not cover all errorneous scenarios, eg. cur sdtEnd within section and the sdtEnd broken
      // if this really happens, uncomment following condition:
      // if (this.isSdtEndAuthentic(paraChild, unclosedNewControls) === false) {
      //   return rCount;
      // }

      // const newControlName = paraChild.attributes.getNamedItem('name').nodeValue;
      // const currentControl = newControlManager.getNameMap()
      //   .get(newControlName);›
      const currentControl = unclosedNewControls[unclosedNewControls.length - 1];
      const newControlStartBorderPortion = currentControl.getStartBorderPortion();
      let textProperty: TextProperty = null;

      if (newControlStartBorderPortion.bPlaceHolder) { // find current control's bPlaceholder
        // as of 7/8/2020 only startBorderPortion.bPlaceHolder stays updated
        para.addToContent(rCount, currentControl.getNewControlContent()
          .getPlaceHolder());
        rCount++;

      }

      const paraChildChildren = paraChild.children;
      if ( paraChildChildren && 0 < paraChildChildren.length ) {
        const nodeChildren = paraChild.children;
        let bSuccess = false;

        for (const nodeChild of nodeChildren) {
          if (typeof nodeChild === 'object') {
            switch (nodeChild.tagName) {
              case 'w:rPr':
                textProperty = new TextProperty();
                // RunReader.traverseRunProperty(paraChild.childNodes[0], textProperty);
                RunReader.tTraverseRunProperty(nodeChild, textProperty, document);
                bSuccess = true;
                break;
            }

            if ( bSuccess ) {
              break;
            }
          }

        }
      }

      if ( null == textProperty ) {
        textProperty = new TextProperty();
      }

      /** create new seperation portion for ] always */
      const newControlEndBorderPortion = currentControl.getEndBorderPortion();
      newControlEndBorderPortion.textProperty = textProperty;
      para.addToContent(rCount, newControlEndBorderPortion); // will also set portion's parent!
      rCount++;

      unclosedNewControls.pop();

      /** if it's control check/radiobutton, need extra step to fill in inner content */
      if (currentControl instanceof NewControlCheck || currentControl instanceof NewControlRadio) {
        currentControl.addContent(); // lsy's method
      }

      /** set map in content control manager for any struct */
      // newControlNames[]  -> all structs
      if (!bInsertFile) {
        newControlManager.getNameMap()
        .set(currentControl.getNewControlName(), currentControl);
      }

      if (unclosedNewControls.length === 0) { // root
        if (!bInsertFile) {
          // if it's a root content control, add to "root content control array"(map already added in sdtstart)
          // newControls[]  -> root structs
          // NOTE the order of elements in the array SHOULD indicate structs appearance in doc
          if (headerFooter == null) {
            newControlManager.getAllNewControls()
              .splice(newControlManager.getAllNewControls().length, 0, currentControl);
          } else {
            if (headerFooter.isHeader()) {
              const structCount = ContentControlDescObj.headerStructCount;
              // console.log(structCount)
              newControlManager.getAllNewControls()
                .splice(structCount, 0, currentControl);
              ContentControlDescObj.headerStructCount++;
            } else {
              // footer
              newControlManager.getAllNewControls()
              .splice(newControlManager.getAllNewControls().length, 0, currentControl);
            }
          }
        } else {
          // pasteControl need to fill up when insertFile
          // control order is important
          const contentPos = document.getCurContentPosInDoc(false, true);
          newControlManager.addPasteControl({control: currentControl, contentPos});
          // TODO?
        }

      } else {
        // 非根节点时，插入文件需set name map
        if (bInsertFile) {
          // newControlManager.getNameMap()
          // .set(currentControl.getNewControlName(), currentControl);
          newControlManager.addNewControlInMap(currentControl);
        }
        // add to leafList. two-way
        const lastElemInUnclosedNewControls = unclosedNewControls[unclosedNewControls.length - 1];
        currentControl.setParent(lastElemInUnclosedNewControls);
        lastElemInUnclosedNewControls.getLeafList()
          .push(currentControl);
      }
    }
    return rCount;

  }

  private static isSectionEndTupleExisted(paraChild: rtNode, sectionEnds: any[]): boolean {
    if (paraChild == null) {
      // tslint:disable-next-line: no-console
      console.warn('isSectionEndTupleExisted empty arguments');
      return true;
    }

    const paraChildAttrs = paraChild.attributes;
    const name = paraChildAttrs['name'];
    // console.log(paraChild)
    // console.log(sectionEnds)
    // console.log(name)
    if (name != null && sectionEnds && sectionEnds.length > 0 && sectionEnds.includes(name)) {
      return true;
    } else {
      // tslint:disable-next-line: no-console
      console.warn('dangling sectionStart detected');
      return false;
    }

    // same scope is not enough
    // const paraChildAttrs = paraChild.attributes;
    // const name = paraChildAttrs['name'];
    // const type = paraChildAttrs['type'];
    // for (let i = index + 1, len = paraChildren.length; i < len; i++) {
    //   const curParaChildAttrs = paraChildren[i].attributes;
    //   if (curParaChildAttrs && curParaChildAttrs['name'] && curParaChildAttrs['type']) {
    //     if (name === curParaChildAttrs['name'] && type === curParaChildAttrs['type']) {
    //       // console.log('return true')
    //       return true;
    //     }
    //   }
    // }

    // // tslint:disable-next-line: no-console
    // console.warn('dangling sectionStart detected');
    // return false;
  }

  /**
   * 当前sdtEnd与unclosedNewControls stack最后的元素比对，是否为一个结构化元素
   */
  private static isSdtEndAuthentic(paraChild: rtNode, unclosedNewControls: NewControl[]): boolean {
    const curName = paraChild.attributes['name'];
    const curType = paraChild.attributes['type'];
    const currentControl = unclosedNewControls[unclosedNewControls.length - 1];
    const newControlName = currentControl.getNewControlName();
    const newControlType = currentControl.getType();
    if (curName === newControlName && +curType === newControlType) {
      return true;
    }
    return false;
  }

  private static addRegionToParas(paras: DocumentContentElementBase[], region: Region, document: Document,
                                  insertRegionNames: string[] = []): void {

    // check duplicate name
    const regionManager = document.getRegionManager();
    const regionName = region.getName();
    // console.log(insertRegionNames)

    if (insertRegionNames.includes(regionName) === true) {
      // must mean it's an existing region
      // tslint:disable-next-line: no-console
      console.warn('region existed in paras');
      return ;
    }

    if (!regionManager.checkRegionName(regionName)) {
      let uniqueName = regionManager.makeUniqueName();
      let safeCount = 0;
      while (insertRegionNames.includes(uniqueName)) {
        uniqueName += '1';
        safeCount++;
        if (safeCount > 100) {
          // tslint:disable-next-line: no-console
          console.warn('cannot set unique region name in insertion');
          break;
        }
      }
      // the unique name here must be 1. unique to regionManager 2. unique to insertRegionNames
      // console.log(uniqueName)
      region.setNewControlName(uniqueName);
      insertRegionNames.push(uniqueName);
    }

    paras.push(region);
    // console.log(paras)
    // regionManager.addRegion(region, undefined);
  }

  /**
   * check if the container is a new region created during file insertion phase
   * @param container /
   * @param insertRegionNames the string[] for region names during insertion phase
   */
  private static isNewlyInsertedRegion(container: Region | TableCell, insertRegionNames: string[] = []): boolean {
    if (container instanceof Region) {
      const regionName = container.getNewControlName();
      if (container.index < 0 || insertRegionNames.includes(regionName)) {
        return true;
      }
    }

    return false;
  }

  private static traverseSdtInsertFile3(
    bInsertFile: boolean = false, document: Document, paraChild: rtNode,
    newControlNamesTempArr: any, para: Paragraph, runCount: number,
    container: Region | TableCell = null, i: number,
    uniqueImagelist: Map<string, IUniqueImageProps>,
    headerFooter: HeaderFooter = null,
    ContentControlDescObj: IContentControlDescObj,
    insertFileObj: IInsertFileContent, modeFonts: IModeFonts = null
  ): number {
    let rCount = runCount;
    const readNewControls = {};
    const documentVersion = insertFileObj ? insertFileObj.documentVersion : 0;
    if (ParagraphReader.shouldReadContentControl((paraChild as IParseXmlNode).type === undefined && bInsertFile,
        document, paraChild.attributes['type'])) {

      let borderStringStart = '';
      let bPlaceHolder = false; // this prop is in portion
      const {newControlManager, newControlProperty, unclosedNewControls, buttons } = ContentControlDescObj;

      const nameAttr = paraChild.attributes['name'];
      if (nameAttr) {
        newControlProperty.newControlName = nameAttr;
        if (bInsertFile === true) {
          newControlProperty._bRetainCascade = insertFileObj?.bRetainCascade;
        }
        readNewControls[newControlProperty.newControlName] = new ReadStruct(newControlProperty);
      }

      newControlProperty.bInsertFile = true;
      if (paraChild.attributes['type']) {
        const type = +paraChild.attributes['type'];
        if (type === NewControlType.SignatureBox) {
          newControlProperty.bReadSign = true;
        }
        newControlProperty.newControlType = type;
      }

      let textProperty: TextProperty = null;
      if (modeFonts != null) {
        const {defaultFont, regionTitleFont} = modeFonts;

        textProperty = new TextProperty(getDefaultFont(insertFileObj.logicDocument));
        if (defaultFont != null) {
          const fontFamilyVal = getFontFamilyVal(defaultFont.fontFamily);

          textProperty.fontFamily = fontFamilyVal;
          textProperty.fontSize = +defaultFont.fontSize;
        }
        if (regionTitleFont != null) {
          // no need elaborate here?
        }
        // console.log(textProperty)
      }
      const runChildren = paraChild.children;
      let sdtContentNode = null; // save to iterate after [ portion is inserted
      let showPlaceholder = false;
      for (const runChild of runChildren) {
        if (typeof runChild === 'object') {
          if ('w:rPr' === runChild.tagName) {
            // textProperty = new TextProperty();
            textProperty = (textProperty != null) ? textProperty : new TextProperty(getDefaultFont(insertFileObj.logicDocument));
            RunReader.tTraverseRunProperty(runChild, textProperty, document);
            // } else if (runChild.nodeName !== '#text') {
          } else {
            const textNode = runChild.children[0];
            // const res = readerStructProps(newControlProperty, textNode, runChild, documentVersion);
            // if (res.borderStringStart !== undefined) {
            //   borderStringStart = res.borderStringStart;
            // }
            // if (res.showPlaceholder !== undefined) {
            //   showPlaceholder = res.showPlaceholder;
            // }
            // if (res.sdtContentNode !== undefined) {
            //   sdtContentNode = res.sdtContentNode;
            // }

            if (StructXmlName.Sdtcontent === runChild.tagName) {
              sdtContentNode = runChild;
              continue;
            }

            if (StructXmlName.ShowPlaceholder === runChild.tagName) {
              if (textNode === '1') {
                showPlaceholder = true;
              }
              continue;
            }
            if (StructXmlName.BorderString === runChild.tagName) {
              if (textNode && typeof textNode === 'string' ) {
                borderStringStart = safeDecodeURIComponent(textNode as any, documentVersion);
              }

              continue;
            }
            const name = newControlProperty.newControlName;
            readNewControls[name]?.readProp(textNode, documentVersion, runChild);
          }
        }

      } // end for

      // console.log(newControlProperty);
      // console.log(circularParse(circularStringify(newControlProperty)));

      if (null == textProperty) {
        textProperty = new TextProperty(getDefaultFont(insertFileObj.logicDocument));
      }

      // control with duplicate name should already be handled
      let currentControl = newControlManager.createNewControl(para, newControlProperty);
      if (currentControl == null) {
        // tslint:disable-next-line: no-console
        // console.warn('duplicate control name detected, should be fixed automatically:)');
        const source = newControlProperty.bInsertFile ? FileSource.InsertFile : null;
        newControlProperty.newControlName = newControlManager.makeUniqueName(newControlProperty.newControlType,
          newControlProperty.newControlName, source);

        this.checkInsertFileNewControlName(newControlProperty, insertFileObj);
        currentControl = newControlManager.createNewControl(para, newControlProperty);
      }

      if (insertFileObj.bRetainCascade && newControlProperty.cascade?.length) {
        const cascadeManager = newControlManager.getCascadeManager();
        cascadeManager.setCascades(currentControl, newControlProperty.cascade, true);
      }

      if (insertFileObj.bRetainCascade && newControlProperty.newControlName !== currentControl.getNewControlName()) {
        insertFileObj.reNameNewControls.set(newControlProperty.newControlName, currentControl.getNewControlName());
      }

      if (insertFileObj.bRetainCascade && newControlProperty.eventInfo) {
        if (!insertFileObj.insertEventInfos) insertFileObj.insertEventInfos = new Map();
        insertFileObj.insertEventInfos.set(currentControl, newControlProperty.eventInfo);
      }

      // reset newControlProperty
      this.resetNewControlProperty(newControlProperty);

      // ------

      /** create new seperation portion for [ always */
      const newControlContent = currentControl.getNewControlContent();
      const newControlPlaceHolder = newControlContent.getPlaceHolder();
      const newControlStartBorderPortion = currentControl.getStartBorderPortion();
      newControlStartBorderPortion.textProperty = textProperty;

      if (currentControl.isCheckBox() || currentControl.isMultiAndRadio()) {
        showPlaceholder = false;
      }

      if (currentControl.isCheckBox() && currentControl.isMustInput()) {
        currentControl.setMustInput(false);
      }

      // TODO: if it's reasonable
      bPlaceHolder = showPlaceholder;

      if (bPlaceHolder) {
        // newControlPlaceHolder.textProperty = textProperty.copy();
        // placeholder always has default text prop - not any more, will override using showPlaceholder
        // here just set a default text props
        newControlPlaceHolder.textProperty = new TextProperty(getDefaultFont(insertFileObj.logicDocument));
      }

      currentControl.setPlaceHolder(bPlaceHolder);

      // if (!nextNode) { // null. means at end of para
      //   newControlPlaceHolder.content = newControlStartBorderPortion.content; // R.W: not in use
      // }

      para.addToContent(rCount, newControlStartBorderPortion);
      rCount++; // not matter

      // still need to add cuz isplaceholderportion
      unclosedNewControls.push(currentControl);

      /** set map in content control manager no matter what */
      // newControlManager.getNameMap()
      //   .set(lastNewControl.getNewControlName(), lastNewControl);
      const controlObj = { unclosedNewControls, buttons: insertFileObj.buttons };
      // add sdtContent items
      if (sdtContentNode != null) {
        // w:r or sdt, TODO revision
        const sdtChildren = sdtContentNode.children;
        for (let j = 0, len = sdtChildren.length; j < len; j++) {
          const sdtChild = sdtChildren[j];
          if (typeof sdtChild === 'object') {
            const sdtChildName = sdtChild.tagName;
            if (sdtChildName === 'w:r') {
              const controlObj = { unclosedNewControls, buttons };

              // if it's title portion/placeholder, ignore it
              const titlePortionType = this.tIsTitlePortion(sdtChild, controlObj, container, i, document,
                sdtChildren, j, true, documentVersion);
              if (titlePortionType === TitlePortionType.None) {
                if (this.tIsPlaceholderPortion(showPlaceholder, sdtChild, controlObj, documentVersion) === false) {
                  RunReader.tTraverseRun(sdtChild, para, rCount, controlObj, uniqueImagelist, bInsertFile, document,
                    headerFooter, false, modeFonts, insertFileObj);
                  rCount++;
                } else {
                  const phPortion = RunReader.tTraverseRun(sdtChild, para, rCount, controlObj, uniqueImagelist,
                    bInsertFile, document, headerFooter, showPlaceholder, modeFonts, insertFileObj);
                  this.setPlaceholderTextProperty(phPortion, controlObj);
                }
              } else if (titlePortionType === TitlePortionType.Region) {
                // title portion in region is not like in struct
                rCount++;
              }
            } else if (sdtChildName === 'sdt') {
              // TODO: may need recursive
              rCount = this.traverseSdtInsertFile3(bInsertFile, document, sdtChild,
                newControlNamesTempArr, para, rCount, container, i,
                uniqueImagelist, headerFooter, ContentControlDescObj, insertFileObj);
            // } else if ('w:ins' === sdtChildName) {
            //   const attrs = sdtChild.attributes;
            //   const revsionChildren = sdtChild.children as rtNode[];
            //   const length = revsionChildren ? revsionChildren.length : 0;

            //   if (attrs && 0 < length) {
            //     const controlObj = { unclosedNewControls };

            //     for (let index = 0; index < length; index++) {
            //       const runChild = revsionChildren[index];

            //       // if it's title portion/placeholder, ignore it
            //       const titlePortionType = this.tIsTitlePortion(runChild, controlObj, container, i, document,
            //         sdtChildren, j, true, documentVersion);
            //       if (titlePortionType === TitlePortionType.None) {
            //         if (this.tIsPlaceholderPortion(showPlaceholder, runChild, controlObj, documentVersion) === false) {
            //           // TODO: since title is in portion with [,
            //           // no elegant way yet to wrap revision element around title
            //           RunReader.tTraverseRun(runChild, para, rCount, controlObj, uniqueImagelist,
            //             bInsertFile, document, headerFooter, false, modeFonts, insertFileObj);
            //           rCount++;
            //         } else {
            //           const phPortion = RunReader.tTraverseRun(runChild, para, rCount, controlObj, uniqueImagelist,
            //             bInsertFile, document, headerFooter, showPlaceholder, modeFonts, insertFileObj);
            //           this.setPlaceholderTextProperty(phPortion, controlObj);
            //         }
            //       } else if (titlePortionType === TitlePortionType.Region) {
            //         // title portion in region is not like in struct
            //         rCount++;
            //       }
            //     }
            //   }
            }  else if ('w:ins' === sdtChildName || 'w:del' === sdtChildName) {
              document = !document ? insertFileObj.logicDocument : document;
              const attrs = sdtChild.attributes;
              const revsionChildren = sdtChild.children as rtNode[];
              const length = revsionChildren ? revsionChildren.length : 0;
    
              if (document.isTrackRevisions()) {
                if ('w:ins' === sdtChildName) {
                  if (attrs && 0 < length) {
                    
                    for (let index = 0; index < length; index++) {
                      const runChild = revsionChildren[index];

                      // if it's title portion/placeholder, ignore it
                      const titlePortionType = this.tIsTitlePortion(runChild, controlObj, container, i, document,
                        sdtChildren, j, true, documentVersion);
                      if (titlePortionType === TitlePortionType.None) {
                        if (this.tIsPlaceholderPortion(showPlaceholder, runChild, controlObj, documentVersion) === false) {
                          // TODO: since title is in portion with [,
                          // no elegant way yet to wrap revision element around title
                          RunReader.tTraverseRun(runChild, para, rCount, controlObj, uniqueImagelist,
                            bInsertFile, document, headerFooter, false, modeFonts, insertFileObj);
                          rCount++;
                        } else {
                          const phPortion = RunReader.tTraverseRun(runChild, para, rCount, controlObj, uniqueImagelist,
                            bInsertFile, document, headerFooter, showPlaceholder, modeFonts, insertFileObj);
                          this.setPlaceholderTextProperty(phPortion, controlObj);
                        }
                      } else if (titlePortionType === TitlePortionType.Region) {
                        // title portion in region is not like in struct
                        rCount++;
                      }
                    }
                  }
                }
    
                continue;
              }

              if (attrs && 0 < length) {
                const type = ('w:ins' === sdtChildName ? ReviewType.Add : ReviewType.Remove);
                const reviewInfo = this.getReviewInfo(attrs, document, type);
                const revisionManager = document.getRevisionsManager();
                const userId = attrs['id'] ? attrs['id'] : null;
                const userName = attrs['author'] ? attrs['author'] : null;
                const bLostInfo = (!userId && !userName);

                if (bLostInfo) {
                  if (ReviewType.Remove === type) {
                    continue;
                  }
                }

                // const controlObj = {bFirstSiblingOfSdtStart, unclosedNewControls, bFollowedBySdtEnd};
                // const controlObj = { unclosedNewControls };
                // for (let index = 0; index < length; index++) {
                //   const runChild = revsionChildren[index];

                //   RunReader.tTraverseRun(runChild, para, rCount, controlObj, uniqueImagelist, bInsertFile, document,
                //     headerFooter, false, modeFonts);
                //   para.content[rCount].setReviewTypeWithInfo(type, reviewInfo.copy());
                //   revisionManager.addRevisionByReviewInfo(para, reviewInfo);
                //   rCount++;
                // }

                if (unclosedNewControls && unclosedNewControls[unclosedNewControls.length - 1]?.isNewDateBox()
                  && ReviewType.Add === type && !reviewInfo['firstDate'] && !reviewInfo.getSavedRecord()) {
                    reviewInfo['bFirstSet'] = true;
                }

                for (let index = 0; index < length; index++) {
                  const runChild = revsionChildren[index];

                  // if it's title portion/placeholder, ignore it
                  const titlePortionType = this.tIsTitlePortion(runChild, controlObj, container, i, document,
                    sdtChildren, j, true, documentVersion);
                  if (titlePortionType === TitlePortionType.None) {
                    if (this.tIsPlaceholderPortion(showPlaceholder, runChild, controlObj, documentVersion) === false) {
                      // TODO: since title is in portion with [,
                      // no elegant way yet to wrap revision element around title

                      if ('w:ins' === runChild.tagName && 'w:del' === sdtChildName) {
                        const secondRevInfo = this.getReviewInfo(runChild.attributes, document);
                        const secondSetting = secondRevInfo.getRevisionSetting();
                        const reviewSetting = reviewInfo.getRevisionSetting();

                        reviewInfo.setRevisionSetting(secondSetting);
                        reviewInfo.setDeleteInfo(secondRevInfo);
                        secondRevInfo.setRevisionSetting(reviewSetting);
                        continue;
                      }

                      RunReader.tTraverseRun(runChild, para, rCount, controlObj, uniqueImagelist,
                        bInsertFile, document, headerFooter, false, modeFonts, insertFileObj);

                      if (!bLostInfo) {
                        para.content[rCount].setReviewTypeWithInfo(type, reviewInfo.copy());
                        revisionManager.addRevisionByReviewInfo(para, reviewInfo);
                      }
                      rCount++;
                    } else {
                      const phPortion = RunReader.tTraverseRun(runChild, para, rCount, controlObj, uniqueImagelist,
                        bInsertFile, document, headerFooter, showPlaceholder, modeFonts, insertFileObj);
                      this.setPlaceholderTextProperty(phPortion, controlObj);

                      if (!bLostInfo) {
                        this.setPlaceholderRevision(type, reviewInfo.copy(), controlObj);
                        revisionManager.addRevisionByReviewInfo(para, reviewInfo);
                      }
                    }
                  } else if (titlePortionType === TitlePortionType.Region) {
                    // title portion in region is not like in struct
                    rCount++;
                  }
                }

                // if (0 === length) {
                //   para.content[para.content.length - 1].setReviewTypeWithInfo(type, reviewInfo);
                //   rCount++;
                // }
              }
            } else {
              // unknown limbo
            }
          }
        }
      }

      // --- start of sdtend
      showPlaceholder = false;
      // const currentControl = unclosedNewControls[unclosedNewControls.length - 1];
      // const newControlStartBorderPortion = currentControl.getStartBorderPortion();
      // let textProperty: TextProperty = null;
      // after [ is added, add real portion contents

      if (newControlStartBorderPortion.bPlaceHolder) { // find current control's bPlaceholder
        // as of 7/8/2020 only startBorderPortion.bPlaceHolder stays updated
        para.addToContent(rCount, currentControl.getNewControlContent()
          .getPlaceHolder());
        rCount++;

      }

      if (null == textProperty) {
        textProperty = new TextProperty(getDefaultFont(insertFileObj.logicDocument));
      }

      /** create new seperation portion for ] always */
      const newControlEndBorderPortion = currentControl.getEndBorderPortion();
      newControlEndBorderPortion.textProperty = textProperty.copy();
      para.addToContent(rCount, newControlEndBorderPortion); // will also set portion's parent!
      rCount++;

      unclosedNewControls.pop();

      if (currentControl instanceof NewControlCheck || currentControl instanceof NewControlRadio) {
        currentControl.addContent(); // lsy's method
        // tslint:disable-next-line: no-console
        // console.warn('para delete portions !', rCount, para.content.length - 1)
        if (currentControl.isMultiAndRadio()) {
          (currentControl as any).checkCheckBoxSelect();
        }
        rCount = para.content.length - 1;
      }

      if (unclosedNewControls.length === 0) { // root
          newControlManager.addPasteControl({ control: currentControl, contentPos: null });
          insertFileObj.insertFileAllNewControlNames.set(currentControl.getNewControlName(), currentControl);
      } else {
        const lastElemInUnclosedNewControls = unclosedNewControls[unclosedNewControls.length - 1];
        // 非根节点时，插入文件需set name map
        if (bInsertFile) {
          // newControlManager.getNameMap()
          // .set(currentControl.getNewControlName(), currentControl);
          //  (lastElemInUnclosedNewControls && lastElemInUnclosedNewControls.getNewControlName())
          if ((paraChild as IParseXmlNode).type !== undefined) {
            insertFileObj.insertFileAllNewControlNames.set(currentControl.getNewControlName(), currentControl);
            newControlManager.addPasteControl({ control: currentControl, contentPos: undefined,
              parentName: (lastElemInUnclosedNewControls && lastElemInUnclosedNewControls.getNewControlName())});
            return rCount;
          } else {
          //   newControlManager.addNewControlInMap(currentControl);
            newControlManager.addPasteControl({ control: currentControl, contentPos: null });
            insertFileObj.insertFileAllNewControlNames.set(currentControl.getNewControlName(), currentControl);
          }
        }
        // add to leafList. two-way
        currentControl.setParent(lastElemInUnclosedNewControls);
        lastElemInUnclosedNewControls.getLeafList()
          .push(currentControl);
      }
    }
    return rCount;
  }

  private static traverseSdtEndInsertFile2(
    bInsertFile: boolean = false, document: Document, paraChild: rtNode,
    para: Paragraph, runCount: number, ContentControlDescObj: IContentControlDescObj,
    insertFileObj: IInsertFileContent
  ): number {

    let rCount = runCount;
    const {newControlManager, unclosedNewControls } = ContentControlDescObj;
    if (ParagraphReader.shouldReadContentControl(bInsertFile, document, paraChild.attributes['type'])
        && unclosedNewControls.length > 0 ) {
      // if unclosedNewControls.length === 0, error exists and the sdtEnd will be deserted
      // this may not cover all errorneous scenarios, eg. cur sdtEnd within section and the sdtEnd broken
      // if this really happens, uncomment following condition:
      // if (this.isSdtEndAuthentic(paraChild, unclosedNewControls) === false) {
      //   return rCount;
      // }

      const currentControl = unclosedNewControls[unclosedNewControls.length - 1];
      const newControlStartBorderPortion = currentControl.getStartBorderPortion();
      let textProperty: TextProperty = null;

      if (newControlStartBorderPortion.bPlaceHolder) { // find current control's bPlaceholder
        // as of 7/8/2020 only startBorderPortion.bPlaceHolder stays updated
        para.addToContent(rCount, currentControl.getNewControlContent()
          .getPlaceHolder());
        rCount++;

      }

      const paraChildChildren = paraChild.children;
      if ( paraChildChildren && 0 < paraChildChildren.length ) {
        const nodeChildren = paraChild.children;
        let bSuccess = false;

        for (const nodeChild of nodeChildren) {
          if (typeof nodeChild === 'object') {
            switch (nodeChild.tagName) {
              case 'w:rPr':
                textProperty = new TextProperty(getDefaultFont(insertFileObj.logicDocument));
                // RunReader.traverseRunProperty(paraChild.childNodes[0], textProperty);
                RunReader.tTraverseRunProperty(nodeChild, textProperty, document);
                bSuccess = true;
                break;
            }

            if ( bSuccess ) {
              break;
            }
          }

        }
      }

      if ( null == textProperty ) {
        textProperty = new TextProperty(getDefaultFont(insertFileObj.logicDocument));
      }

      /** create new seperation portion for ] always */
      const newControlEndBorderPortion = currentControl.getEndBorderPortion();
      newControlEndBorderPortion.textProperty = textProperty;
      para.addToContent(rCount, newControlEndBorderPortion); // will also set portion's parent!
      rCount++;

      unclosedNewControls.pop();

      /** if it's control check/radiobutton, need extra step to fill in inner content */
      // if (currentControl instanceof NewControlCheck || currentControl instanceof NewControlRadio) {
      //   currentControl.addContent(); // lsy's method
      // }

      if (unclosedNewControls.length === 0) { // root
          // pasteControl need to fill up when insertFile
          // control order is important
          // const contentPos = document.getCurContentPosInDoc(false, true);
          newControlManager.addPasteControl({control: currentControl, contentPos: null});
      } else {
        // 非根节点时，插入文件需set name map
        // add to leafList. two-way
        const lastElemInUnclosedNewControls = unclosedNewControls[unclosedNewControls.length - 1];
        currentControl.setParent(lastElemInUnclosedNewControls);
        lastElemInUnclosedNewControls.getLeafList()
          .push(currentControl);
      }
      insertFileObj.insertFileAllNewControlNames.set(currentControl.getNewControlName(), currentControl);
    }
    return rCount;

  }

  private static checkInsertFileNewControlName(
    newControlProperty: INewControlProperty, insertFileObj: IInsertFileContent
  ): void {
    let name = newControlProperty.newControlName;
    const newControlManager = insertFileObj.newControlManager;
    const insertFileAllNewControlNames = insertFileObj.insertFileAllNewControlNames;

    while ( insertFileAllNewControlNames.has(name)
          || !newControlManager.checkNewControlName(name) ) {
      name += '1';
    }

    newControlProperty.newControlName = name;
  }

  private static getReviewInfo(attrs: any, document: any, type?: ReviewType): ReviewInfo {
    const userId = attrs['id'] ? attrs['id'] : null;
    const description = attrs['des'] ? attrs['des'] : null;
    const date = attrs['date'] ? attrs['date'] : null;
    const userName = attrs['author'] ? attrs['author'] : null;
    const savedCount = attrs['savedCount'] ? Number.parseInt(attrs['savedCount'], 0) : 0;
    let line = attrs['line'] ? Number.parseInt(attrs['line'], 0) : 1;
    let color = attrs['color'] ? attrs['color'] : null;
    let level = attrs['level'] ? Number.parseInt(attrs['level'], 0) : 1;

    // TODO?
    // const type = 'w:ins' === paraChild.tagName ? ReviewType.Add : ReviewType.Remove;
    const reviewInfo = new ReviewInfo(document);

    const temp = date ? date.match('(.*)T(.*)') : null;
    const time = new Date();
    if (temp && 3 === temp.length) {
      // let timeStr = temp[0];
      // // add leading zero
      // timeStr = timeStr.replace(/-[\d]+-/, (match) => { // month
      //   // console.log(match)
      //   if (match.length === 4) {
      //     return '-' + (+match.slice(1, 3) + 1) + '-';
      //   } else if (match.length === 3) {
      //     const month = +match[1] + 1;
      //     if (match.length === 3) {
      //       return '-0' + month + '-';
      //     } else if (match.length === 4) {
      //       return '-' + month + '-';
      //     }
      //   }
      // }).replace(/-[\d]T/, (match) => {
      //   // console.log(match)
      //   return '-0' + match[1] + 'T';
      // });
      // time = new Date(timeStr);
      // // timeStr = timeStr.replace(/-[\d]T/, (match) => {
      // //   // console.log(match)
      // //   return '-0' + match[1] + 'T';
      // // });

      // time = new Date(timeStr);
      // console.log(timeStr)

      const fullYear = temp[1].match('(\\d*)-(\\d*)-(\\d*)');
      const fullHour = temp[2].match('(\\d*):(\\d*):(\\d*)');
      if (date && fullYear && fullHour && 3 <= fullYear.length && 3 <= fullHour.length) {
        // time.setFullYear(Number.parseInt(fullYear[1], 0),
        //   Number.parseInt(fullYear[2], 0), Number.parseInt(fullYear[3], 0));
        // time.setHours(Number.parseInt(fullHour[1], 0),
        //   Number.parseInt(fullHour[2], 0), Number.parseInt(fullHour[3], 0));

        const timeString = (fullYear[0] + ' ' + fullHour[0]).replace('-', '/');
        time.setTime(Date.parse(timeString));

        const tempTime = new Date();
        if (time.getTime() > tempTime.getTime()) {
          time.setTime(tempTime.getTime());
        }
      }
    }

    let bFind = false;
    REVISION_COLOR.forEach((value, key) => {
      if (value === color) {
        bFind = true;
        color = key;
        return;
      }
    });

    if ((null === color || false === bFind) && !isValidORGBColor(color)) {
      color = '红色';
    }

    if (0 > line) {
      line = 0;
    } else if (2 < line) {
      line = 2;
    }

    if (1 > level) {
      level = 1;
    } else if (4 < level) {
      level = 4;
    }

    reviewInfo.setRevisionSetting({
      userId, description, style: line, color, date: time, userName, level, savedCount
    });

    let record = attrs['record'];
    if (ReviewType.Add === type) {
      if (record) {
        record = safeDecodeURIComponent(attrs['record'], document.getDocumentVersion());
        const tempRecords = JSON.parse(record);
        if (tempRecords?.length) {
          const records = [];
          tempRecords.forEach((item) => {
            if (item) {
              records.push({
                userId: item.userId,
                userName: item.userName,
                date: new Date(item.date),
                description: item.description,
                // level: item.level,
                // color: item.color,
                // style: item.style,
                savedCount: item.savedCount,
                content: item.content,
              });
            }
          });
          reviewInfo.setSavedRecord(records);
        }
      }

      if (attrs['firstDate']) {
        reviewInfo['firstDate'] = attrs['firstDate'];
      }
    }

    return reviewInfo;
  }

  private static checkPortionRevisionMerge(
    para: Paragraph, runCount: number, type: number, reviewInfo: ReviewInfo
  ): boolean {
    let result = false;

    if ( 0 === runCount || para.content[runCount].isSingleParaEndPortion()) {
      return result;
    }

    const prePortion = para.content[runCount - 1];
    const preType = prePortion.getReviewType();
    const preReviewInfo = prePortion.getReviewInfo();
    const preSecondRev = preReviewInfo.getDeleteInfo();
    const secondRev = reviewInfo.getDeleteInfo();

    if (preType === type && preReviewInfo.isEqual(reviewInfo)
      && (preReviewInfo.getTime().toString() === reviewInfo.getTime().toString())
      && preReviewInfo.getColor() === reviewInfo.getColor()
      && ( (!preSecondRev && !secondRev) || (!!preSecondRev && !!secondRev && preSecondRev.isEqual(secondRev)
                  && preSecondRev.getTime().toString() === secondRev.getTime().toString()))
      && prePortion.textProperty.isEqual(para.content[runCount].textProperty)) {
      result = true;
    }

    return result;
  }

  private static parseParaProperty(para: any, paraChild: any): void {
    let indent: rtNode = null;
    let spacing: rtNode = null;
    let alignment: rtNode = null;
    let lastRenderedPageBreak: rtNode = null;
    let wordWrap: rtNode = null;
    let nisSignAuthor: rtNode = null;
    for (const nodeChild of paraChild.children) {
      if (typeof nodeChild === 'object') {
        if (nodeChild.tagName === 'w:ind') {
          indent = nodeChild;
        } else if (nodeChild.tagName === 'w:spacing') {
          spacing = nodeChild;
        } else if (nodeChild.tagName === 'w:jc') {
          alignment = nodeChild;
        } else if (nodeChild.tagName === 'w:lastRenderedPageBreak') {
          lastRenderedPageBreak = nodeChild;
        } else if ( 'w:wordWrap' === nodeChild.tagName ) {
          wordWrap = nodeChild;
        } else if ( NURSING_FEATURE && 'w:nisSignAuthor' === nodeChild.tagName ) {
          nisSignAuthor = nodeChild;
          if (nisSignAuthor && nisSignAuthor.attributes && nisSignAuthor.attributes['w:val']) {
            para.paraProperty.nisSignAuthor = nisSignAuthor.attributes['w:val'];
          }
        } else if ('w:hidden' === nodeChild.tagName) {
          para.bHidden = true;
        }
      }
    }

    /** indentation */
    // try {
      if (indent) {
        if (indent.attributes) {
          if (indent.attributes['w:left']) {
            // filtered at writer, should not need to check if isNaN
            para.paraProperty.paraInd.left =
              Number(indent.attributes['w:left']);
          }
          if (indent.attributes['w:firstLine']) {
            para.paraProperty.paraInd.firstLine =
              Number(indent.attributes['w:firstLine']);
          }
          if (indent.attributes['w:right']) {
            para.paraProperty.paraInd.right =
              Number(indent.attributes['w:right']);
          }

        // } else {
        //   throw new Error('w:ind has no attributes');
        }
      }
    // } catch (error) {
    //   // alert(ErrorMessages.XmlError);
    //   // tslint:disable-next-line: no-console
    //   console.warn(ErrorMessages.XmlError);
    //   window.dispatchEvent(new ErrorEvent('error', { message: ErrorMessages.XmlError, error }));
    //   const date = new Date();
    //   logger.error({
    //     id: 0, code: error.stack,
    //     startTime: date, args: null, name: 'traverseParagraph.indent.attributes'
    //   });
    // }

    /** line spacing */
    if (spacing) {
      // console.log(spacing)
      // try {
        if (spacing.attributes) {
          if (spacing.attributes['w:lineType']) {
            para.paraProperty.paraSpacing.lineSpacingType =
              Number(spacing.attributes['w:lineType']);
          }
          if (spacing.attributes['w:line']) {
            para.paraProperty.paraSpacing.lineSpacing =
              Number(spacing.attributes['w:line']);
          }
        // } else {
        //   throw new Error('w:spacing has no attributes');
        }
      // } catch (error) {
      //   // alert(ErrorMessages.XmlError);
      //   window.dispatchEvent(new ErrorEvent('error', { message: ErrorMessages.XmlError, error }));
      //   // tslint:disable-next-line: no-console
      //   console.warn(ErrorMessages.XmlError);
      //   const date = new Date();
      //   logger.error({
      //     id: 0, code: error.stack,
      //     startTime: date, args: null, name: 'traverseParagraph.spacing.attributes'
      //   });

      // }
    }

    /** alignment */
    if (alignment) {
      // console.log(alignment);
      // try {
        if (alignment.attributes && alignment.attributes['w:val']) {
          const alignmentVal = alignment.attributes['w:val'];
          // console.log(alignment.attributes.getNamedItem('w:val').nodeValue)
          let alignmentType = AlignType.Justify; // default to be justified
          switch (alignmentVal) {
            case AlignmentOptions.BOTH:
              alignmentType = AlignType.Justify;
              break;
            case AlignmentOptions.LEFT:
              alignmentType = AlignType.Left;
              break;
            case AlignmentOptions.RIGHT:
              alignmentType = AlignType.Right;
              break;
            case AlignmentOptions.CENTER:
              alignmentType = AlignType.Center;
              break;
            default:
              break;
          }

          para.paraProperty.alignment = alignmentType;

      //   } else {
      //     throw new Error('w:jc has no attributes');
        }
      // } catch (error) {
      //   // alert(ErrorMessages.XmlError);
      //   window.dispatchEvent(new ErrorEvent('error', { message: ErrorMessages.XmlError, error }));
      //   // tslint:disable-next-line: no-console
      //   console.warn(ErrorMessages.XmlError);
      //   const date = new Date();
      //   logger.error({
      //     id: 0, code: error.stack,
      //     startTime: date, args: null, name: 'traverseParagraph.alignment.attributes'
      //   });
      // }
    }

    /** page break */
    if (lastRenderedPageBreak) {
      para.paraProperty.bPageBreakBefore = true;
    }

    if ( wordWrap ) {
      // try {
        if (wordWrap.attributes && wordWrap.attributes['w:val']) {
          para.paraProperty.bWordWrap = ('1' === wordWrap.attributes['w:val'] ? true : false);
        // } else {
        //   throw new Error('w:wordWrap has no attributes');
        }
      // } catch (error) {
      //   // alert(ErrorMessages.XmlError);
      //   window.dispatchEvent(new ErrorEvent('error', {message: ErrorMessages.XmlError, error}));
      //   // tslint:disable-next-line: no-console
      //   console.warn(ErrorMessages.XmlError);
      //   const date = new Date();
      //   logger.error({id: 0, code: error.stack,
      //   startTime: date, args: null, name: 'traverseParagraph.wordWrap.attributes'});

      // }
    }
  }

  private static addNISCellContent(para: any, content: any, bTimeCell: boolean, modeFonts: IModeFonts = null): void {
    const paraContents = para.getContent();
    const length = paraContents.length;
    let firstPortion = paraContents[0];

    if (2 < length) {
      if (!bTimeCell) {
        para.removePortion(1, length - 2);
      }
    } else if (1 === length) {
      firstPortion = new ParaPortion(para as any);
      if (modeFonts != null) {
        const {defaultFont} = modeFonts;
  
        if (defaultFont != null) {
          const fontFamilyVal = getFontFamilyVal(defaultFont.fontFamily);
  
          firstPortion.textProperty.fontFamily = fontFamilyVal;
          firstPortion.textProperty.fontSize = +defaultFont.fontSize;
        }
      } else {
        firstPortion.textProperty = paraContents[0].textProperty.copy();
      }

      para.addToContent(0, firstPortion);
    }
    
    if (!bTimeCell) {
      firstPortion.content.splice(0);
      if (content) {
        firstPortion.addText(content);
      }
    } else {
      const hour = content[0] ? content[0] : '--';
      firstPortion.content.splice(0);
      firstPortion.addText(hour);

      const minutePortion = paraContents[2];
      const minute = content[1] ? content[1] : '--';

      minutePortion?.content.splice(0);
      minutePortion?.addText(minute);
    }
  }

  private static setDate(nisProperty: any, content: string): string {
    const dateBoxFormat = nisProperty.dateBoxFormat;
    const customFormat = nisProperty.customFormat;

    let dateTime = '';
    if (content) {
      if (dateBoxFormat === NISDateBoxFormat.AutoCustom) {
        let year = content.substring(0, content.indexOf(customFormat.yearAndMonth));

        let month = '';
        const months = content.substring(content.indexOf(customFormat.yearAndMonth) + customFormat.yearAndMonth.length)
                                  .split(customFormat.monthAnyDay);
        if (months[0]) {
          month = months[0];
        }

        if (year) {
          content = year;
        } else {
          year = '' + new Date().getFullYear();
        }

        if (month) {
          content = year ? '/' + month : month;
        }

        if (months[1]) {
          content = '/' + months[1];
        }

        dateTime = year + '-' + month + '-' + month[1];
      }

      nisProperty.time = new Date(content);

      if (!(nisProperty.time instanceof Date && !isNaN(nisProperty.time.getTime()))) {
        return undefined;
      }

      content = nisDateFormat(nisProperty.time, dateBoxFormat, customFormat);
      if (dateBoxFormat !== NISDateBoxFormat.AutoCustom && nisProperty.time) {
        dateTime = nisProperty.time.getFullYear() + '-'
                  + (nisProperty.time.getMonth() + 1) + '-' + nisProperty.time.getDate();
      }
    }

    nisProperty.time = '' !== content ? nisProperty.time : undefined;
    nisProperty.dateTime = dateTime;

    if (nisProperty.hideDateText === true) {
        content = '';
    }

    nisProperty.text = content;

    return content;
  }

  private static setTime(nisProperty: any, content: string): string[] {
    const timeFormat = nisProperty.timeFormat;
    const endStart = timeFormat.lastIndexOf('MM');
    const separator = timeFormat.substring(2, endStart);
    const time = content.split(separator);
    let cellText = undefined;
    if (1 === time.length) {
      let hour = +time[0];
      if (hour < 0) {
        time[0] = '00';
      } else if (24 < hour) {
        time[0] = '24';
      }

      if (1 === time[0].length) {
        time[0] = ('0' + time[0]);
      }

      cellText = time[0] + separator + '--';
    } else if (2 <= time.length) {
      time.splice(2);
      let hour = +time[0];
      if (hour < 0) {
        time[0] = '00';
      } else if (24 < hour) {
        time[0] = '24';
      }

      if (1 === time[0].length) {
        time[0] = ('0' + time[0]);
      }

      let min = +time[1];
      if (min < 0) {
        time[1] = '00';
      } else if (59 < min) {
        time[1] = '59';
      }

      if (1 === time[1].length) {
        time[1] = ('0' + time[1]);
      }

      if (time[0] && time[1]) {
        cellText = time[0] + separator + time[1];
      } else if (time[0]) {
        cellText = time[0] + separator + '--';
      } else if (time[1]) {
        cellText = '--' + time[1] + separator;
      }
    }

    nisProperty.cellText = cellText;

    return time;
  }

  private static setListItem(nisProperty: any, content: string): void {
    if (content) {
      let bChanged = false;
      const bShowValue = nisProperty.bShowValue;
      const bCheckMultiple = nisProperty.bCheckMultiple;
      const separator = nisProperty.separator;

      if (bCheckMultiple) {
        const arr = content.split(separator);
        nisProperty.items?.forEach((item) => {
          const value = !bShowValue ? item.code : item.value;

          if (-1 !== arr.findIndex((_value) => {
              return _value === value})) {
            item.bSelect = true;
            bChanged = true;
          } else {
            item.bSelect = false;
          }
        });
      } else {
        nisProperty.items?.forEach((item) => {
          const value = !bShowValue ? item.code : item.value;

          if (content === value && !bChanged) {
            item.bSelect = true;
            bChanged = true;
          } else {
            item.bSelect = false;
          }
        });
      }

      content = !bChanged ? '' : content;
    } else {
      nisProperty.items?.forEach((value) => {
        if (value.bSelect) {
          value.bSelect = false;
        }
      });
    }
  }

  // 设置外部接口单元格内容
  public static setNISCellContent(cell: TableCell, content: string, modeFonts: IModeFonts = null): void {
    // 
    const cellContents = cell.content;
    const nisProperty = cell.getNISProperty2();
    const type = nisProperty.type;
    const para = cellContents.content[0];

    if (1 < cellContents.content.length) {
      cellContents.content.splice(1);
    }

    switch (type) {
      case NISTableCellType.Date: {
        content = this.setDate(nisProperty, content);
        if (undefined !== content) {
          this.addNISCellContent(para, content, false, modeFonts);
        }
        break;
      }

      case NISTableCellType.Time: {
        const time = this.setTime(nisProperty, content);
        this.addNISCellContent(para, time, true, modeFonts);
        break;
      }

      case NISTableCellType.List: {
        this.setListItem(nisProperty, content);

        this.addNISCellContent(para, content, false, modeFonts);
        break;
      }

      case NISTableCellType.Number: {
        let num = +content;

        if ('' !== content) {
          const precision = nisProperty.precision;
          if (null != nisProperty.minValue && num < nisProperty.minValue) {
            num = nisProperty.minValue;
          } else if (null != nisProperty.maxValue && num > nisProperty.maxValue) {
            num = nisProperty.maxValue;
          }

          if (null != nisProperty.precision && 0 < precision) {
            num = +num.toFixed(precision);
          }
        }

        content = '' !== content ? num.toString() : content;
        this.addNISCellContent(para, content, false, modeFonts);
        break;
      }

      default: {
        this.addNISCellContent(para, content, false, modeFonts);
      }
    }
  }
}
