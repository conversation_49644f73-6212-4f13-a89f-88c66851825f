import * as React from 'react';
import Input from '../../ui/Input';
import { NISTableCellType } from '../../../../common/commonDefines';

interface IDialogProps {
    id?: string;
    prop: {
        type: number,
        colID?: string;
        rowID?: string;
    }
}

interface IState {
    bRefresh: boolean;
}
export class NISTableCellCommon extends React.Component<IDialogProps, IState> {
    private datas: {[key: number]: string};
    constructor(props: any) {
        super(props);
        this.datas = {
            [NISTableCellType.Quick]: '快捷文本',
            [NISTableCellType.Text]: '普通文本',
            [NISTableCellType.Date]: '日期',
            [NISTableCellType.Time]: '时间',
            [NISTableCellType.List]: '下拉选项',
            [NISTableCellType.Number]: '数值',
            [NISTableCellType.BloodPressure]: '血压',
            [NISTableCellType.Signature]: '签名'
        };
    }

    public render(): any {
        return (
            <div>
                <div className='editor-line'>
                    <div className='w-70'>单元格ID</div>
                    <div className='right-auto'>
                        <Input
                            name='serialNumber'
                            value={this.props.prop.colID}
                            readonly={true}
                            placeholder={''}
                            disabled={true}
                        />
                    </div>
                </div>
                <div className='editor-line'>
                    <div className='w-70'>行ID</div>
                    <div className='right-auto'>
                        <Input
                            name='serialNumber'
                            value={this.props.prop.rowID}
                            readonly={true}
                            placeholder={''}
                            disabled={true}
                        />
                    </div>
                </div>
                <div className='editor-line'>
                    <div className='w-70'>类型</div>
                    <div className='right-auto'>
                        <Input
                            name='serialNumber'
                            value={this.datas[this.props.prop.type]}
                            readonly={true}
                            placeholder={''}
                            disabled={true}
                        />
                    </div>
                </div>
            </div>
        );
    }

    public componentDidMount(): void {
        //this.setState({bRefresh: !this.state.bRefresh});
    }

    public componentWillReceiveProps(nextProps: any): void {
        // this.visible = nextProps.visible;
    }

    // private renderFooter(): any {
    //     return (
    //         <span>
    //             <Button onClick={this.close}>取消</Button>
    //             <Button type='primary' onClick={this.confirm}>确认</Button>
    //         </span>
    //     );
    // }

    // private open = (): void => {
    //     // this.value = this.props.type;
    //     this.setState({});
    //     //
    // }

    // private onChange = (value: any, name: string): void => {
    //     this.value = value;
    //     this.setState({bRefresh: !this.state.bRefresh});
    // }

    // private close = (bRefresh?: boolean): void => {
    //     this.value = null;
    //     this.props.close(this.props.id, bRefresh);
    //     this.visible = false;
    //     this.setState({bRefresh: !this.state.bRefresh});
    // }

    // private confirm = (): void => {
    //     switch (this.value) {
    //         case MenuItemIndex.DeleteRow: {
    //             this.props.documentCore.removeTableRow();
    //             break;
    //         }
    //         case MenuItemIndex.DeleteCol: {
    //             this.props.documentCore.removeTableColumn();
    //             break;
    //         }
    //         default:
    //             return;
    //     }

    //     this.close(true);
    // }

}
