import { PageProperty } from '../../model/StyleProperty';
import { message } from '../Message';
import { ICPrintData, PrintType } from '../commonDefines';

/* IFTRUE_WATER */
// import { MarkFactory } from '../MarkFactory';
/* FITRUE_WATER */

export default class Print {
    public host: any;
    public nextPageHtml: string;
    public title: string;
    public pagePosition: string; // 打印位置：top/bottom
    public documentCore: any;
    public pagePro: PageProperty;
    public iframeContainer: any;
    public bSelected: boolean;
    public showHeader: boolean; // 是否展示页眉页脚
    public truePrintContent: string;
    private rectReg: RegExp = /(\w+)="(\d+(\.\d+)?)"/g;
    // /<(rect)[^>]*?selection-selected[^<]*?<\/\1>/g
    private childReg: RegExp = /<(rect)[^>]*?selection-selected[^<]*?<\/\1>/g;
    private attrReg1: RegExp = /(width|x)="\d+(\.\d+)?"/g;
    // /<rect\s+class=(['"])(?:[\w\-\s](?!selection-selected))+?\1[^>]*?>/
    private attrReg2: RegExp = /<rect\s+((?!selection-selected)[^>])+>/g;
    //private bgReg: RegExp = /<(\w+)\s*class="paragraphline-container(\s+outer)?"[^\1]*?<\/\1>/g;
    private bgReg: RegExp = /<(\w+)\s*class="paragraphline-container(?:\s+outer)?"[\s\S]*?<\/\1>/g;

    private bgReg2: RegExp = /class=(["'])paragraphline-container(\s+outer)?\1/g;
    private bgReg3: RegExp = /selection-selected["']/g;
    private positionReg1: RegExp = /<(rect)[^<]+?<\/\1>/;
    private positionReg2: RegExp = /(\w+)="(\d+(\.\d+)?)"/g;
    private position: {width: number, height: number, x: number, y: number, xLimit: number};
    private count: number;
    private appendSvg: string;
    private editorContainer: HTMLElement;
    private printerServerUrl: string;
    private backgroundImage: string;
    private clipPaths: any;
    private _batch: boolean;
    private externalEvent: any = null;
    private disablePrintAction: boolean = false;
    private firstProcessedPageIndex: number; // 添加跟踪第一个处理页面的索引

    constructor(host: any) {
        this.host = host;
        this.documentCore = host.documentCore || host.state.documentCore;
        this.title = '文件标题';
        this.count = 0;
        this.showHeader = false;
        this.host = host;
        this.printerServerUrl = 'localhost:8888';
        this.externalEvent = null;
        this.disablePrintAction = false;
    }

    public setExternalEvent(event: any): void {
        this.externalEvent = event;
    }

    public setDisablePrintAction(disable: boolean): void {
        this.disablePrintAction = disable;
    }

    public print(type: number, pagePosition?: string, data?: ICPrintData): Promise<boolean> {
        return new Promise<boolean>((resolve, reject) => {
            this.pagePosition = pagePosition;
            this._batch = undefined;
            // let pager: HTMLElement;
            switch (type) {
                case 1:
                    this.bSelected = false;
                    break;
                case 2:
                    this.bSelected = true;
                    break;
                case 3: {
                    this.bSelected = true;
                    this._batch = true;
                    break;
                }
            }

            this.setTimePrint(this.printContent(data));         
            this.bSelected = false;
            if (this.nextPageHtml) {
                setTimeout(() => {
                    const div = document.createElement('div');
                    div.innerHTML = this.nextPageHtml;
                    this.setTimePrint(div);
                    resolve(true);
                }, 1000);
            } else {
                resolve(true);
            }
        });
    }

    public cPrint(type: number, data: ICPrintData): Promise<boolean> {
        return new Promise<boolean>((resolve, reject) => {
            // let pager: HTMLElement;
            switch (type) {
                case 1:
                    this.bSelected = false;
                    break;
                case 2:
                    this.bSelected = true;
                    break;
            }

            if (data._batch === true) {
                this.bSelected = true;
            }
            this._batch = data._batch;
            const pager = this.printContent(data);
            if (pager == null) {
                resolve(false);
            }
            this.setTimeCPrint(pager, data);

            this.bSelected = false;
            if (this.nextPageHtml) {
                setTimeout(() => {
                    const div = document.createElement('div');
                    div.innerHTML = this.nextPageHtml;
                    this.setTimeCPrint(div, data);
                    resolve(true);
                }, 1000);
            } else {
                resolve(true);
            }
        });
    }

    // public printOutpatientDoc(prop: any): void {

    // }

    public getPager(): PageProperty {
        const { pageProperty } = this.documentCore.render();
        return this.pagePro = pageProperty;
    }

    public getContents(data?: ICPrintData): object [] {
        if (!this.editorContainer) {
            this.editorContainer = this.host.myRef.current;
        }

        // each dom -> each page
        const doms = this.editorContainer
            .querySelectorAll('.ReactVirtualized__Grid__innerScrollContainer .page-wrapper> svg');
        // console.log(doms)
        const contents = [];
        const bSelected = this.bSelected; // 是否选择打印
        // const pagePosition = this.pagePosition;
        this.clipPaths = undefined;
        // let bFirstPageContPrint = true;
        for (let i = 0, ii = doms.length; i < ii; i++) {
            const dom = doms[i];
            const domPageId = dom.getAttribute('page-id');
            
            if (bSelected === true) {
                // if (!dom.querySelector('.selection-selected')) {
                const selectedDom = dom.querySelector('.selections')
                const hasSelection = selectedDom && selectedDom.children.length > 0;
                
                if (!hasSelection) {
                    if (contents.length > 0) {
                        break; // 选择结束了
                    }
                    continue;
                }
            }
            
            const currentPageIndex = parseInt(dom.getAttribute('page-id'), 10); // <div page-index='0' ...
            
            this.getPostion(i);
            const childs = dom.children;
            
            // console.log(dom)

            // const arrs = [1, 2];
            let text = '';
            // if (!this.backgroundImage) {
            this.backgroundImage = (dom.parentNode as HTMLDivElement).style.backgroundImage;
                // this.backgroundImage = this.documentCore.getEditorBackground();
                // console.log(this.backgroundImage);
            // }

            let index = 0;
            for (let i = 0, len = childs.length; i < len; i++) {
                const child = childs[i];
                const classList = child.classList;
                if (classList.contains('content') || classList.contains('header-footer')) {
                    text += this.svgfilter(child as any, currentPageIndex, data, index++);
                }
            }

            // for (let index = 0, len = arrs.length; index < len; index++) {
            //     // console.log(childs)
            //     const child: any = childs[arrs[index]]; // 1st child <polyline> ignored
            //     // if (child.classList.contains('header-footer')) {
            //     //     console.log('header-footer')
            //     //     // 续打时，起始页打印页眉页脚
            //     //     if (bSelected === true && bFirstPageContPrint === true &&
            //     //         data != null && data.startPageHdrFtr === false) {
            //     //         bFirstPageContPrint = false;
            //     //         console.log('continued')
            //     //         continue;
            //     //     }
            //     // }
            //     // const svg = child.innerHTML;
            //     text += this.svgfilter(child, currentPageIndex, data, index);
            //     // console.log(text)
            // }

            contents.push({content: text, pageIndex: currentPageIndex + 1});
        }
        return contents;
    }

    /**
     * 普通预览模式打印html内容设置
     * @param texts 整理的svg内容
     */
    public setPageContent(texts: object[], data?: ICPrintData): string {
        if (texts.length === 0) {
            return '';
        }
        let pagers = '';
        const title = this.title;
        const len = this.getPageTotal();
        const height = (this.pagePro.height - 2).toFixed(0);
        const width = this.pagePro.width.toFixed(0);
        const specificPages = []; // 1 or 2[start, end]
        if (data != null) {
            if (data.pageRange != null && data.pageRange.length > 0) {
                const pageRange = data.pageRange;
                const sepIndex = pageRange.indexOf('-');
                if (sepIndex !== -1) {
                    // '1-5'
                    specificPages.push(+pageRange.slice(0, sepIndex));
                    specificPages.push(+pageRange.slice(sepIndex + 1));
                } else {
                    // 6
                    specificPages.push(+pageRange);
                }
            }
        }
        // console.log(specificPages)
        let style = `height: ${height}px; width: ${width}px;`;
        if (this.backgroundImage && (!data || data.showWatermark !== false)) {
            style += `background-image: ${this.backgroundImage};`;
        }
        style = `style='${style}'`;
        const specificPagesLen = specificPages.length;
        const clipPaths = this.clipPaths || {};
        let className = '';
        if (this._batch) {
            className = ' batch';
        }

        /* IFTRUE_WATER */
        // 填充水印内容
        // const waterMark = MarkFactory.getInstance().getFullMark();
        // const {fontSize, fontFamily, fontColor, fontStyle, fontWeight} = waterMark.font;
        // const markStr = `<text fill="${fontColor}" x="${waterMark.xs}" y="${waterMark.ys}" font-size="${fontSize}" font-weight="${fontWeight}" font-family="${fontFamily}" font-style="${fontStyle}">
        //     ${waterMark.content}
        // </text>`;
        /* FITRUE_WATER */
        texts.forEach((item, index) => {
            if (specificPagesLen > 0) {
                if (specificPagesLen === 1) {
                    if (index + 1 !== specificPages[0]) {
                        return ;
                    }
                } else if (specificPagesLen === 2) {
                    const curIndex = index + 1;
                    if (curIndex < specificPages[0] || curIndex > specificPages[1]) {
                        return ;
                    }
                } else {
                    // tslint:disable-next-line: no-console
                    console.warn('page range has more than 2 pages');
                }
            }
            let header = '';
            let footer = '';
            if (this.showHeader) {
                header = `<div class="header">${title}</div>`;
                // tslint:disable-next-line: no-string-literal
                footer = `<div class="footer">${item['pageIndex'] + '/' + len}</div>`;
            }

            pagers += `<div class="pager${className}"${style}>
                <div class="header-bg"></div>
                ${header}
                <svg width="${width}" height="${height}">`;
            /* IFTRUE_WATER */
            // pagers += markStr;
            /* FITRUE_WATER */
            pagers += `
                ${item['content']}
                </svg>
                <div class="footer-bg"></div>
                ${footer}
            </div>`;
        });

        return pagers;
    }

    public setTimePrint(pager: Element): void {
        // console.log(pager)
        if (!pager) {
            message.warning('无法获取打印内容，(续打/套打)未选择区域或页码范围超出最大值)');
            return;
        }

        const childWindow = this.getFrameWindow();
        const body = childWindow.document.body;
        const pageProp = this.documentCore.getPageProperty();
        const height = pageProp.height.toFixed(0);
        const width = pageProp.width.toFixed(0);
        const pageSize = `<style>
                @media print {
                    @page {
                        size: ${width}px ${height}px;
                        margin-top: 0;
                        margin-bottom: 0;
                    }
                }
                </style>
        `
        body.innerHTML = pageSize;

        // apply true content, pager should already be ready
        if (pager) {
            body.appendChild(pager);
        }
        // 获取打印内容 此时 HTML 已完全处理好
      
       const printHtmlContent = childWindow.document.documentElement.outerHTML;
       if (this.externalEvent && typeof this.externalEvent.nsoPrinterDataEvent === 'function') {
           this.externalEvent.nsoPrinterDataEvent(printHtmlContent);
       }
       if(this.disablePrintAction){
         return;
       }

        // console.log(childWindow.document.documentElement.innerHTML)
        childWindow.print();
    }

    // public async setTimePrint2(pager: any, data?: Date): Promise<void> {
    //     // console.log(pager)
    //     const childWindow = this.getFrameWindow();
    //     const body = childWindow.document.body;
    //     const div = document.createElement('div');
    //     div.appendChild(pager);
    //     pager = div;
    //     // document.body.innerHTML = '';
    //     document.body.appendChild(pager);
    //     const date2 = new Date();
    //     let canvas = await html2canvas(pager, {
    //         allowTaint: false,   // 允许污染
    //         // taintTest: true,    // 在渲染前测试图片(没整明白有啥用)
    //         useCORS: true,      // 使用跨域(当allowTaint为true时这段代码没什么用,下面解释)
    //         // background: '#fff',
    //     });
    //     // console.log(canvas);
    //     const imgBlob = canvas.toDataURL( 'image/jpeg', 1.0 );
    //     // body.innerHTML = `<img src="${imgBlob}" />`;
    //     const img = document.createElement('img');
    //     img.src = imgBlob;
    //     // body.innerHTML = '';
    //     window['print-dialog'].outerHTML = '';
    //     canvas = null;
    //     div.outerHTML = '';
    //     body.appendChild(img);
    //     img.onload = () => {
    //         const date3 = new Date();
    //         // document.body.innerHTML = '';
    //         childWindow.print();
    //         console.log(new Date().getTime() - data.getTime());
    //         console.log(imgBlob);
    //         console.log(date3.getTime() - date2.getTime());
    //     };
    //     // console.log(imgBlob);

    //     // apply true content, pager should already be ready
    //     // body.appendChild(pager);

    //     // console.log(childWindow.document.documentElement.innerHTML)
    // }

    public setTimeCPrint(pager: Element, data: ICPrintData): void {

        if (!pager) {
            message.warning('无法获取打印内容，续打/套打 未选择区域或页码范围超出最大值)');
            return;
        }

        // pager is for real printing
        const childWindow = this.getFrameWindow();
        const body = childWindow.document.body;
        const pageProp = this.documentCore.getPageProperty();
        const height = pageProp.height.toFixed(0);
        const width = pageProp.width.toFixed(0);
        const pageSize = `<style>
                @media print {
                    @page {
                        size: ${width}px ${height}px;
                        margin-top: 0;
                        margin-bottom: 0;
                    }
                }
                </style>
        `
        body.innerHTML = pageSize;
        body.appendChild(pager);
        // console.trace()
        const contentData = childWindow.document.documentElement.outerHTML;
        if (contentData != null) {
            // console.log(contentData)
            data.printData = contentData;
        }
        // console.log(data);
        // console.log(JSON.stringify(data)); // will do this in api later
        // console.log(JSON.parse(JSON.stringify(data)).printData);
        // call c printing
        this.startCPrint(data);
        // childWindow.print();
    }

    public async startCPrint(printData: ICPrintData): Promise<any> {
        let data: ICPrintData = null;
        if (printData != null) {
            data = printData;
        } else {
            data = {
                printData: '',
                printerName: '',
                printCount: 1,
                printDouble: false,
                pageCount: 1,
                printOrientPortrait: true,
            };
        }

        // side effects
        data.printOrientPortrait = !data.landscape;

        // console.log(data)
        let printerServerUrl = 'localhost:8888';
        printerServerUrl = this.printerServerUrl;
        const fetchUrl = `http://${printerServerUrl}/printing`;
        // console.log(fetchUrl);

        fetch(fetchUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data),
        })
        .then((res) => res.json())
        .then(
            (result) => {
                console.log(result);
                if (result != null) {
                    if (result.status === 0) {
                        message.info('打印状态正常，打印中...');
                    } else {
                        message.warning(`打印状态异常，错误代码：${result.status}`);
                    }

                }
                return result;
            },
            (error) => {
                console.log(error);
                message.warning('打印失败，请联系技术人员');
                return error;
            }
        );
    }

    /**
     * 打印门诊模式打印html内容设置
     * @param texts 整理的svg内容
     */
    public setPageContent1(texts: object[]): string {
        if (texts.length === 0) {
            return '';
        }
        let pagers = '';
        if (!this.pagePro) {
            const { pageProperty } = this.documentCore.render();
            this.pagePro = pageProperty;
        }
        // const title = this.title;
        // const len = this.getPageTotal();
        const height = this.pagePro.height;
        const width = this.pagePro.width;
        const paddingTop = this.pagePro.paddingTop;

        // let header = '';
        // let footer = '';
        // if (this.showHeader) {
        //     header = `<div class="header">${title}</div>`;
        //     // tslint:disable-next-line: no-string-literal
        //     footer = `<div class="footer">${item['pageIndex'] + '/' + len}</div>`;
        // }
        let result: string;
        switch (this.pagePosition) {
            case 'top':
                if (texts.length >= 2) {
                    pagers += `
                        <div class='svg-content'>
                            <svg width="${width}" height="${height}" >${texts[0]['content']}</svg>
                        </div>
                        <div class='svg-content'>
                            <svg width="${width}" height="${height}" >${texts[1]['content']}</svg>
                        </div>
                    `;
                } else {
                    pagers += `<svg width="${width}" height="${height}" >${texts[0]['content']}</svg>`;
                }
                break;
            case 'bottom':
                if (texts.length >= 2) {
                    result = `<svg width="${width}" height="${height}" >${texts[1]['content']}</svg>`;
                }
                pagers += `
                    <div class='svg-content'>
                        <svg width="${width}" height="${height}" ></svg>
                    </div>
                    <div class='svg-content'>
                        <svg width="${width}" height="${height}" >${texts[0]['content']}</svg>
                    </div>
                `;
                break;
        }

        if (result !== undefined) {
            result = `<div class="pager">
                <div class="header-bg" style='height: ${paddingTop + 10}px;'></div>
                ${result}
                <div class="footer-bg"></div>
            </div>`;
        }
        this.nextPageHtml = result;

        pagers = `<div class="pager">
            <div class="header-bg" style='height: ${paddingTop + 10}px;'></div>
            ${pagers}
            <div class="footer-bg"></div>
        </div>`;

        return pagers;
    }

    public setPrinterServerUrl(str: string): void {
        this.printerServerUrl = str;
    }

    private getPageTotal(): number {
        const { total, pageProperty } = this.documentCore.render();
        this.pagePro = pageProperty;
        return total;
    }

    private getPagePositionInfo(pageIndex: number): object {
        return this.documentCore.getPagePositionInfo(pageIndex);
    }

    private svgfilter(child: HTMLElement, pageIndex: number, data?: ICPrintData, index?: number): string {
       
        // 在选择打印模式下，第一个被处理的页面就是起始页，使用一个静态变量来跟踪第一个处理的页面
        if (this.firstProcessedPageIndex === undefined) {
            this.firstProcessedPageIndex = pageIndex;
        }
        const isFirstProcessedPage = pageIndex === this.firstProcessedPageIndex;
        
        // 只有当当前页是第一个被处理的页面，且是页眉元素时，才根据 startPageHdrFtr 决定是否显示
        const shouldHide = index === 0 && data && this.bSelected === true && isFirstProcessedPage && data.startPageHdrFtr !== true;
       
        
        if (shouldHide) {
           
            return '';
        }
        // if (index === 0 && data && this.bSelected === true && pageIndex === 0 && data.startPageHdrFtr !== true) {
       //     return '';
       // }
        child = child.cloneNode(true) as any;
        this.mask();
        const removeChilds = child.querySelectorAll
        ('.newcontrol-focus-container,.paragraphline-container,.newcontrol-cursorin-container,.tablecell-container,.tablecellBackground');
        Array.from(removeChilds)
        .forEach((node) => {
            node.outerHTML = '';
        });

        if (index === 1 && this.clipPaths) {
            const clipPath = this.clipPaths[pageIndex];
            if (clipPath) {
                child.setAttribute('clip-path', `url(#print-mask-${pageIndex})`);
                const defs = document.createElement('defs');
                defs.innerHTML = clipPath;
                const firstChild = child.firstChild;
                child.insertBefore(defs, firstChild);
            }
        }

        let svg = child.outerHTML;
        // let appendStr = '';
        // let outer = this.paraSelection(svg);
        this.appendSvg = '';
        // svg = svg.replace(/<g\s+class="(newcontrol-|region-border)[\s\S]+?<\/g>/g, '');
        // svg = this.tableSelection(svg, pageIndex, data);
        // svg = svg.replace(this.bgReg, (str) => {
        //     appendStr += this.paraSelection(str);
        //     return '';
        // })
        svg = svg.replace(/¶/g, '');

        // svg += appendStr + this.appendSvg;

        return svg;
    }

    private mask(): any {
        if (!this.bSelected || this.clipPaths) {
            return;
        }

        const selections = this.documentCore.getSelectionBounds();
        if (!selections) {
            return;
        }
        // console.log(selections);
        this.clipPaths = {};
        const datas: any = {};
        const lines = selections.lines;
        const cells = selections.cells;
        let data: {pageIndex?: number, lines: any[], cells: any[]};
        let pageIndex;
        if (lines.length) {
            pageIndex = -1;
            lines.forEach((line) => {
                if (line.pageIndex !== pageIndex) {
                    pageIndex = line.pageIndex;
                    data = {pageIndex, lines: [line], cells: []};
                    datas[pageIndex] = data;
                } else {
                    data.lines.push(line);
                }
            });
        }
        if (cells.length) {
            pageIndex = -1;
            cells.forEach((cell) => {
                if (cell.pageIndex !== pageIndex) {
                    pageIndex = cell.pageIndex;
                    if (datas[pageIndex]) {
                        data = datas[pageIndex];
                        data.cells.push(cell);
                    } else {
                        data = {pageIndex, lines: [], cells: [cell]};
                        datas[pageIndex] = data;
                    }
                } else {
                    data.cells.push(cell);
                }
            });
        }
        const keys = Object.keys(datas);
        keys.forEach((key) => {
            this.eactMask(datas[key]);
        });
    }

    private eactMask(selections: any): void {
        const pageIndex = selections.pageIndex;
        let min: number = 99999999999999;
        let max: number = 0;

        let firstEle: {width: number, height: number, y: number, x: number};
        let lastEle: {width: number, height: number, y: number, x: number};
        const lines = selections.lines;
        // const page = this.documentCore.getPageProperty();
        let minX: number = 99999999999;
        let maxX: number = 0;
        // 先从行里面取出最小最大y值
        // if (lines && lines.length > 0) {
        //     let line = lines[0];
        //     min = selections.lines[0].y;
        //     // firstHeight = line.height;
        //     firstEle = line;
        //     line = lastEle = lines[lines.length - 1];
        //     max = line.y + line.height;
        // }
        const cells = selections.cells;

        // 再在cell里面比较，再进行切换
        // if (cells && cells.length > 0) {
        //     let cell = cells[0];
        //     if (min === undefined) {
        //         min = cell.y;
        //         firstEle = cell;
        //         cell = lastEle = cells[cells.length - 1];
        //         max = cell.y + cell.height;
        //     } else {
        //         if (cell.y < min) {
        //             firstEle = cell;
        //         }
        //         cell = cells[cells.length - 1];
        //         const maxY = cell.y + cell.height;
        //         if (maxY > max) {
        //             max = maxY;
        //         }
        //     }
        // }
        const datas = lines.concat(cells);
        // 取出最小x，最大x值
        for (let index = 0, len = datas.length; index < len; index++) {
            const line = datas[index];
            const x1: number = line.x;
            if (x1 < minX) {
                minX = x1;
            }

            const x2: number = x1 + line.width;
            if (x2 > maxX) {
                maxX = x2;
            }

            if (line.y < min) {
                min = line.y;
                firstEle = line;
            }
            const currentHeight = line.height + line.y;
            if (currentHeight - max > -0.1) {
                max = currentHeight;
                lastEle = line;
            }
        }
        if (!firstEle || !lastEle) {
            return;
        }
        // const page = this.documentCore.getPageProperty();
        // const x = page.x;
        // const xLimit = page.xLimit;
        // 对路径进行收集：需要绘制可视区域
        const points = [];
        // 第一行的左下角点
        points.push({x: firstEle.x, y: firstEle.y + firstEle.height});
        // 第一行左上角点
        points.push({x: firstEle.x, y: firstEle.y});
        // 第一行右上角点
        points.push({x: maxX, y: firstEle.y});
        if (lastEle.y - firstEle.y > 0.1) { // 多行
            points.push({x: maxX, y: lastEle.y});
            const currentX: number = lastEle.x + lastEle.width;
            if (maxX - currentX > 1) {
                points.push({x: currentX, y: lastEle.y});
            }

            points.push({x: currentX, y: lastEle.y + lastEle.height});
            points.push({x: minX, y: lastEle.y + lastEle.height});

            points.push({x: minX, y: firstEle.y + firstEle.height});
        } else {
            // 第一行右下角点
            points.push({x: maxX, y: lastEle.y + lastEle.height});
        }
        // 对所有点进行组装，组装成路径点阵
        let path: string = '';
        points.forEach((point, index) => {
            if (index === 0) {
                path += `${point.x},${point.y}`;
            } else {
                path += ` ${point.x},${point.y}`;
            }
        });

        this.clipPaths[pageIndex] = `
        <clipPath id="print-mask-${pageIndex}">
            <polygon points="${path}"
            style="fill:#fff;stroke-width:1"/>
        </clipPath>`;
    }

    /**
     * 获取各个单元格的属性
     * @param rects // 各个单元格
     */
    private getTabelCellAttrs(rects: string[]): any[] {
        const attrs = [];
        const addY = 1;
        rects.forEach((rect) => {
            const reg = this.positionReg2;
            let matchs = reg.exec(rect);
            const attr = {};
            while (matchs) {
                attr[matchs[1]] = matchs[2];
                matchs = reg.exec(rect);
            }

            const y1 = attr['y1'] = parseFloat(attr['y']);
            attr['y2'] = y1 + parseFloat(attr['height']);
            const x1 = attr['x1'] = parseFloat(attr['x']);
            attr['x2'] = x1 + parseFloat(attr['width']);

            if (attr['height']) {
                attr['height'] = parseFloat(attr['height']) + addY;
            }
            if (rect.indexOf('selection-selected') > -1) {
                attr['selected'] = true;
            }
            attrs.push(attr);
        });

        return attrs;
    }

    /**
     * 单元格重绘
     * @param attrs 单元个各个参数
     * @param onlyRect 只绘制单元格，不需要边框
     */
    private tableCellRerenderAttrs(rects: string[], actIndex?: number): any {
        let rectStr = '';
        // let rowIndex: number = 0;
        // let colIndex: number = 0;
        // let actY: string;
        // let maxColIndex: number = 0;
        // let lines = [];
        // let offsetX: string;
        // let offsetY: string;
        // 先重绘单元格
        // let bStart = true;
        rects.forEach((rect, index) => {
            // if (index === 0) {
            //     offsetX = attr.x;
            //     offsetY = attr.y;
            // }
            // let style = '';
            if (index === actIndex || actIndex === undefined && rect.indexOf('selection-selected') > -1) {
                // bStart = false;
                rect = rect.replace('<rect', `'<rect style="fill: #fff; opacity: 0;"'`);
                // attr.y = parseFloat(attr.y) - 1;
                // attr.height = parseFloat(attr.height) - 1;
            }

            // if (bStart === true) {
            //     attr.width = 1 + parseFloat(attr.width);
            //     attr.x = parseFloat(attr.x) - 2;
            //     style += ` stroke="#fff" stroke-width="1"`;
            // } else if (bStart === false && !attr.selected) {
            //     attr.x = parseFloat(attr.x) + 1;
            //     attr.width = 1 + parseFloat(attr.width);
            //     style += ` stroke="#fff" stroke-width="1"`;
            // }
            // if (actY !== attr.y && actY !== undefined) {
            //     rowIndex++;
            //     colIndex = 0;
            //     actY = attr.y;
            // }
            // colIndex++;
            // if (colIndex > maxColIndex) {
            //     maxColIndex = colIndex;
            // }

            // attr.rowIndex = rowIndex;
            // attr.colIndex = colIndex;
            // lines.push(attr);
            rectStr += rect;
        });

        // let lineStr = '';
        // // 再重绘边框
        // if (onlyRect === false) {
        //     const lineObj = {};

        //     lines = lines.filter((line) => line.selected);
        //     // actY = undefined;
        //     // const map = new Map();
        //     // let curRowIndex: number = 1;
        //     // let mapArrs = [];
        //     // let prevRowLine: any = null;
        //     const color = '#000';
        //     lines.forEach((line, index) => {
        //         const y1 = line.y1;
        //         const y2 = line.y2;
        //         const x1 = line.x1;
        //         const x2 = line.x2;

        //         lineObj['' + y1 + y2 + x1] = true;
        //         // 左边线
        //         lineStr += `<line x1="${x1}" y1="${y1}" x2="${x1}" y2="${y2}"
        //             stroke="${color}" stroke-width="0.5"></line>`;

        //         lineObj['' + x1 + x2 + y1] = true;
        //         // 上边线
        //         lineStr += `<line x1="${x1}" y1="${y1}" x2="${x2}" y2="${y1}
        //                 " stroke="${color}" stroke-width="0.5"></line>`;
        //     });

        //     lines.forEach((line) => {
        //         const y1 = line.y1;
        //         const y2 = line.y2;
        //         const x1 = line.x1;
        //         const x2 = line.x2;
        //         if (lineObj['' + y1 + y2 + x2] !== true) {
        //             // 右边线
        //             lineStr += `<line x1="${x2}" y1="${y1}" x2="${x2}" y2="${y2}"
        //             stroke="${color}" stroke-width="0.5"></line>`;
        //         }

        //         if (lineObj['' + x1 + x2 + y2] !== true) {
        //             // 下边线
        //             lineStr += `<line x1="${x1}" y1="${y2}" x2="${x2}" y2="${y2}"
        //             stroke="${color}" stroke-width="0.5"></line>`;
        //         }
        //     });
        // }
        return {line: undefined, rect: rectStr};
    }

    private getTableCellIndex(rects: string[]): number {
        const attr = {};
        // const rect = /<rect class="selection section-item-\d+ selection-selected"[^>]+?>/.exec(svg);
        // if (rect) {
        //     const str = rect[0];
        //     const reg = this.positionReg2;
        //     let matchs = reg.exec(str);
        //     while (matchs) {
        //         attr[matchs[1]] = matchs[2];
        //         matchs = reg.exec(str);
        //     }
        // }
        const reg = this.positionReg2;
        // let matchs = reg.exec(str);
        for (let index = 0, len = rects.length; index < len; index++) {
            const rect = rects[index];
            if (rect.indexOf('selection-selected') === -1) {
                continue;
            }
            let matchs = reg.exec(rect);
            while (matchs) {
                attr[matchs[1]] = matchs[2];
                matchs = reg.exec(rect);
            }
            return index;
        }
        // if (!attr['x']) {
        //     return -1;
        // }
        // const x: number = parseFloat(attr['x']);
        // const y = parseFloat(attr['y']);

        // return this.getCellToXY(x, y, attrs);
    }

    // private getCellToXY(x: number, y: number, attrs: any[]): number {
    //     for (let i = 0, len = attrs.length; i < len; i++) {
    //         const curAttr = attrs[i];
    //         const x1: number = curAttr['x1'];
    //         const x2: number = curAttr['x2'];
    //         if (x >= x1 && x <= x2) {
    //             const y1: number = curAttr['y1'];
    //             const y2: number = curAttr['y2'];
    //             if (y >= y1 && y <= y2) {
    //                 return i;
    //             }
    //         }
    //     }

    //     return -1;
    // }

    private tableParaSelection(rects: string[], cellAttr: any): string {
        const attrs = [];
        // const rectReg = /<rect class="selection section-item-\d+ selection-selected"[^>]+?>/g;
        // let rect = rectReg.exec(svg);
        // while (rect) {
        //     const rectStr = rect[0];
        //     const reg = this.positionReg2;
        //     let matchs = reg.exec(rectStr);
        //     const attr = {};
        //     while (matchs) {
        //         attr[matchs[1]] = matchs[2];
        //         matchs = reg.exec(rectStr);
        //     }
        //     attrs.push(attr);
        //     rect = rectReg.exec(svg);
        // }
        const reg = this.positionReg2;
        rects.forEach((rect) => {
            if (rect.indexOf('selection-selected') === -1) {
                return;
            }
            let matchs = reg.exec(rect);
            const attr = {};
            while (matchs) {
                attr[matchs[1]] = matchs[2];
                matchs = reg.exec(rect);
            }
            attrs.push(attr);
        });

        if (attrs.length === 0) {
            return '';
        }

        let res = '';
        const cellX = parseFloat(cellAttr.x);
        const cellY = parseFloat(cellAttr.y);
        const cellWidth = parseFloat(cellAttr.width);
        const cellX2 = cellWidth + cellX;
        const cellHeight = parseFloat(cellAttr.height);
        const lastIndex = attrs.length - 1;
        const num = 5.6;
        attrs.forEach((attr, index) => {
            const x = parseFloat(attr.x);
            const width = parseFloat(attr.width);
            const y = parseFloat(attr.y);
            const height = parseFloat(attr.height);
            const width1 = x - cellX;
            // 顶部
            if (index === 0) {
                const actHeight = y - cellY;
                if (actHeight > num) {
                    res += `<rect width="${cellWidth}" height="${actHeight}" x="${cellX}"
                        y="${cellY}" fill="#fff"></rect>`;
                }
            }

            // 左边
            if (width1 > num) {
                res += `<rect width="${width1}" height="${height + 1}" x="${cellX}" y="${y - 1}" fill="#fff"></rect>`;
            }

            // 右边
            const width2 = cellX2 - width - x;
            if (width2 > num) {
                res += `<rect width="${width2}" height="${height + 1}" x="${x + width}"
                    y="${y - 1}" fill="#fff"></rect>`;
            }

            // 底部
            if (lastIndex === index) {
                const actHeight = cellHeight + cellY - y - height;
                if (actHeight > 0.5) {
                    res += `<rect width="${cellWidth}" height="${actHeight}" x="${cellX}"
                     y="${y + height}" fill="#fff"></rect>`;
                }
            }
        });

        return res;
    }

    private tableSelection(svg: string, pageIndex: number, data?: ICPrintData): string {
        // 无表格情况下
        if (svg.search(/<g\s+class="tablecell-container"/) === -1) {
            return svg;
        }

        // new RegExp(leftReg + className + rightReg, 'g');
        // tslint:disable-next-line: max-line-length
        const reg = /<g\s+class="(paragraphline-containerselection [\w\-]+|tablecell-container)"*?>([\s\S]*?)<\/g>/g;

        if (this.bSelected !== true ||
            (this.bSelected === true && pageIndex !== 0) ||
            (this.bSelected === true && pageIndex === 0 && data && data.startPageHdrFtr === true)) {
            return svg.replace(reg, '');
        }

        // tslint:disable-next-line: max-line-length
        const tableReg = /data-key="(table-content|table-border)">([\s\S]+?)<\/g>\s*(?=<g\s+(data-key=|class="image-wrapper"))/g;
        const contentStrs = [];
        const borderStrs = [];
        // let tableMatch = tableReg.exec(svg);
        // while (tableMatch) {
            // if (tableMatch[1] === 'table-content') {
            //     contentStrs.push(tableMatch[2]);
            // } else {
            //     borderStrs.push(tableMatch[2]);
            // }
        //     tableMatch = tableReg.exec(svg);
        // }
        // const borderReg = /<g\s+class="(tableBorderLine)">((?!<\/g>)[\s\S])+<\/g>/g;
        // console.time('1');
        svg = svg.replace(tableReg, (str, str1, str2) => {
            if (str1 === 'table-content') {
                contentStrs.push(str2);
                str = str.replace(reg, () => {
                    return '';
                });
            } else {
                borderStrs.push(str2);
                if (str1 === 'table-border') {
                    str = '';
                }
            }
            return str;
        });
        // console.timeEnd('1');
        // console.log(contentStrs, borderStrs)

        // const paraStrs = [];
        // const tableCellStrs = [];
        // let bData = false;

        // const strReg = /(paragraphline-containerselection(?= [\w\-]+)|tablecell-container)/;
        // svg = svg.replace(reg, (str, str1, str2, str3) => {
        //     if (str1 === 'tablecell-container') {
        //         tableCellStrs.push(str2);
        //     } else {
        //         this.getRectStr(str2, paraStrs);
        //     }
        //     bData = true;

        //     return '';
        // });

        let rectStr = '';
        let borderStr = '';
        contentStrs.forEach((content, index) => {
            const obj = this.getSelecteTableStr(content, borderStrs[index]);
            if (!obj) {
                return;
            }
            rectStr += obj.rect;
            borderStr += obj.border;
        });

        // 除去单元格边框
        // svg = svg.replace(/<g\s+class="(tableBorderLine)">((?!<\/g>)[\s\S])+<\/g>/g, '');
        // 表格中是否有选中
        // if (bData) {
        //     let bSelectTableCell = true;
        //     // let rectStr = '';
        //     // let lineStr = '';
        //     const unSelectedStr = '';
        //     // const len = bgStrs.length;
        //     const selectedAttrs = {};

        //     if (paraStrs.length > 0) {
        //         const rects = paraStrs;
        //         const attrs = this.getTabelCellAttrs(rects);
        //         const actAttrIndex = this.getTableCellIndex(rects, attrs);
        //         const actAttr = attrs[actAttrIndex];
        //         if (actAttr) {
        //             actAttr.selected = true;
        //             const opt = this.tableCellRerenderAttrs(attrs, true);
        //             rectStr += opt.rect;
        //             lineStr += opt.line;
        //             rectStr += this.tableParaSelection(rects, actAttr);
        //             bSelectTableCell = false;
        //         }
        //     }
        //     if (bSelectTableCell && tableCellStrs.length > 0) {
        //         const attrs = this.getTabelCellAttrs(tableCellStrs);
        //         const opt = this.tableCellRerenderAttrs(attrs, true);
        //         rectStr += opt.rect;
        //         lineStr += opt.line;
        //         bSelectTableCell = true;
        //         selectedAttrs[0] = attrs.filter((attr) => attr.selected);
        //     }

        //     // let index = 0;
        //     // for (let i = 0; i < len; i = i + 2) {
        //     //     const cellBg = bgStrs[i];
        //     //     const paraBg = bgStrs[i + 1];
        //     //     if (cellBg.search(this.bgReg3) > -1) { // 单元格是否被选中

        //     //     } else if (paraBg.search(this.bgReg3) > -1) {
        //     //         // 单元格中某几个段落被选中了
        //     //         // paraStr += this.paraSelection(paraBg);

        //     //     } else { // 都没选中就隐藏
        //     //         unSelectedStr += cellBg.replace('<g', '<g fill="#fff"');
        //     //     }
        //     // }
        //     const borderStrs: string[] = [];
        //     svg = svg.replace(/<g\s+class="(tableBorderLine)">((?!<\/g>)[\s\S])+<\/g>/g, (str) => {
        //         borderStrs.push(str);
        //         return '';
        //     });
        //     let borderStr = '';
        //     if (bSelectTableCell === true) {
        //         borderStr = this.getTableCellBorder(borderStrs, selectedAttrs);
        //     }
        // }

        this.appendSvg = `<g fill="#fff">${rectStr}}</g>${borderStr}`;

        return svg;
    }

    private getRectsByCellContent(str: string): {para: string[], cell: string[]} {
        if (!str) {
            return;
        }
        const paraStrs = [];
        const tableCellStrs = [];
        const reg = /<g\s+class="(paragraphline-containerselection [\w\-]+|tablecell-container)"*?>([\s\S]*?)<\/g>/g;
        let match = reg.exec(str);
        while (match) {
            if (match[1] === 'tablecell-container') {
                tableCellStrs.push(match[2]);
            } else {
                this.getRectStr(match[2], paraStrs);
            }
            match = reg.exec(str);
        }

        if (!paraStrs.length && !tableCellStrs.length) {
            return;
        }
        return {para: paraStrs, cell: tableCellStrs};
    }

    private getSelecteTableStr(content: string, border: string): {rect: string, border: string} {
        if (!content || !border) {
            return;
        }

        const rectObj = this.getRectsByCellContent(content);
        if (!rectObj) {
            return;
        }

        const paraStrs = rectObj.para;
        const tableCellStrs = rectObj.cell;

        let bSelectTableCell = true;
        let rectStr = '';
        let lineStr = '';
        const unSelectedStr = '';
        // const len = bgStrs.length;
        let selectedAttrs = [];

        if (paraStrs.length > 0) {
            const rects = paraStrs;
            const actAttrIndex = this.getTableCellIndex(rects);
            const actAttr = this.getTabelCellAttrs(tableCellStrs.slice(actAttrIndex, actAttrIndex + 1))[0];
            if (actAttr) {
                actAttr.selected = true;
                const opt = this.tableCellRerenderAttrs(tableCellStrs, actAttrIndex);
                rectStr += opt.rect;
                lineStr += opt.line;
                rectStr += this.tableParaSelection(rects, actAttr);
                bSelectTableCell = false;
            }
        }

        if (bSelectTableCell && tableCellStrs.length > 0) {
            const attrs = this.getTabelCellAttrs(tableCellStrs);
            const opt = this.tableCellRerenderAttrs(tableCellStrs);
            rectStr += opt.rect;
            lineStr += opt.line;
            bSelectTableCell = true;
            selectedAttrs = attrs.filter((attr) => attr.selected);
        }

        let borderStr = '';
        if (bSelectTableCell === true) {
            const borderStrs: string[] = [];
            const borderReg = /<g\s+class="(tableBorderLine)">((?!<\/g>)[\s\S])+<\/g>/g;
            let match = borderReg.exec(border);
            while (match) {
                borderStrs.push(match[0]);
                match = borderReg.exec(border);
            }
            // border.replace(, (str) => {
            //     return str;
            // });
            borderStr = this.getTableCellBorder(borderStrs[0], selectedAttrs);
        }

        return {
            rect: rectStr + lineStr,
            border: borderStr,
        };
    }

    private getRectStr(str: string, arrs: string[]): void {
        const reg = /<rect[\s\S]*?<\/rect>/g;
        let match = reg.exec(str);
        while (match) {
            arrs.push(match[0]);
            match = reg.exec(str);
        }
    }

    private getTableCellBorder(border: string, attrs: any[]): string {
        let result = '';
        attrs.forEach((item) => {
            result += this.getBorder(item, border);
        });

        if (result) {
            return `<g>${result}</g>`;
        }

        return result;
    }

    // private getBorderAttrs(border: string): any[] {
    //     if (!border) {
    //         return [];
    //     }
    //     const reg = this.positionReg2;
    //     const attrs: any[] = [];
    //     const lineReg = /<line\s+([\s\S]+?)<\/line>/g;
    //     let lineMatches = lineReg.exec(border);
    //     while (lineMatches) {
    //         const attr: any = {};
    //         const str = lineMatches[1];
    //         let matchs = reg.exec(str);
    //         while (matchs) {
    //             attr[matchs[1]] = matchs[2];
    //             matchs = reg.exec(str);
    //         }

    //         lineMatches = lineReg.exec(border);
    //         attr.x1 = parseFloat(attr.x1);
    //         attr.x2 = parseFloat(attr.x2);
    //         attr.y1 = parseFloat(attr.y1);
    //         attr.y2 = parseFloat(attr.y2);
    //         attrs.push(attr);
    //     }

    // }

    private getBorder(rectAtrr: any, border: string): string {
        let result = '';
        const x1 = Math.floor(rectAtrr.x1);
        const x2 = Math.floor(rectAtrr.x2);
        const y1 = Math.floor(rectAtrr.y1);
        const y2 = Math.floor(rectAtrr.y2);
        // let reg = new RegExp(
        //     `<line x1="${x1}\\.\\d+" y1="${y2}\\.\\d+" x2="${x2}\\.\\d+" y2="${y2}\\.\\d+"[\\s\\S]+?<\\/line>`
        // );
        // border = border.replace(reg, (str) => {
        //     result += str;
        //     return '';
        // });

        // reg = new RegExp(
        //     `<line x1="${x1}\\.\\d+" y1="${y1}\\.\\d+" x2="${x2}\\.\\d+" y2="${y1}\\.\\d+"[\\s\\S]+?<\\/line>`
        // );
        // border = border.replace(reg, (str) => {
        //     result += str;
        //     return '';
        // });

        // reg = new RegExp(
        //     `<line x1="${x1}\\.\\d+" y1="${y1}\\.\\d+" x2="${x1}\\.\\d+" y2="${y2}\\.\\d+"[\\s\\S]+?<\\/line>`
        // );
        // border = border.replace(reg, (str) => {
        //     result += str;
        //     return '';
        // });

        // reg = new RegExp(
        //     `<line x1="${x2}\\.\\d+" y1="${y1}\\.\\d+" x2="${x2}\\.\\d+" y2="${y2}\\.\\d+"[\\s\\S]+?<\\/line>`
        // );
        // border = border.replace(reg, (str) => {
        //     result += str;
        //     return '';
        // });

        const arrs = [[x1, y2, x2, y2], [x1, y1, x2, y1], [x1, y1, x1, y2], [x2, y1, x2, y2]];
        arrs.forEach(((arr) => {
            const reg = new RegExp(
                // tslint:disable-next-line: max-line-length
                `<line x1="${arr[0]}\\.\\d+" y1="${arr[1]}\\.\\d+" x2="${arr[2]}\\.\\d+" y2="${arr[3]}\\.\\d+"[\\s\\S]+?<\\/line>`
            );
            border = border.replace(reg, (str) => {
                result += str;
                return '';
            });
        }));
        // borders[index] = border;

        return result;
    }

    // 段落选区
    private paraSelection(bgStr: string): string {
        let appendStr = '';
        if (this.bSelected === true) {
            bgStr = bgStr.replace(this.bgReg2,  (str) => {
                return str + ` fill="#fff"`;
            })
            .replace(this.bgReg3, (str) => {
                return str + ' style="fill: #fff; opacity: 0;"';
            });
            bgStr = this.replaceAttrs(bgStr);
            bgStr += this.addChild(bgStr);
            appendStr += bgStr;
        }

        return appendStr;
    }

    private getPostion(pageIndex: number): void {
        const page: any = this.getPagePositionInfo(pageIndex);
        this.position = {
            x: page.x,
            y: page.y,
            xLimit: page.xLimit,
            width: page.width,
            height: page.height,
        };
    }

    private replaceAttrs(bgStr: string): string {
        const reg = this.attrReg1;
        const position = this.position;
        const width = position.width;
        const x = position.x;
        bgStr = bgStr.replace(this.attrReg2, (str, str1, str2) => {
            return str.replace(reg, (m1, m2) => {
                if (m2 === 'width') {
                    return `width="${width}"`;
                }
                if (m2 === 'x') {
                    return `x="${x}"`;
                }
            });
        });
        return bgStr;
    }

    private addChild(bgStr: string): string {
        const reg = this.childReg;
        const matchs = bgStr.match(reg);
        if (!matchs) {
            return '';
        }
        const position = this.position;
        let str = '';
        str += this.getPrevRect(matchs[0]);
        str += this.getNextRect(matchs[matchs.length - 1]);
        if (!str) {
            return '';
        }

        return `<g fill="#fff" offset-x=${position.x} offset-y=${position.y}>${str}</g>`;
    }

    private getNextRect(str: string): string {
        const position = this.position;
        const reg1 = this.rectReg;
        let matchs = reg1.exec(str);
        const style = {};
        while (matchs) {
            style[matchs[1]] = matchs[2];
            matchs = reg1.exec(str);
        }
        // tslint:disable-next-line: no-string-literal
        const activeWidht = parseFloat(style['width']);
        // tslint:disable-next-line: no-string-literal
        const activeX = parseFloat(style['x']);
        const x = activeX + activeWidht;
        const width = position.xLimit - x;
        if (width < 0.2) {
            return '';
        }

        // tslint:disable-next-line: no-string-literal
        return `<rect width="${width}" height="${style['height']}" x="${x}" y="${style['y']}"></rect>`;
    }

    private getPrevRect(str: string): string {
        const position = this.position;
        const reg1 = this.rectReg;
        let matchs = reg1.exec(str);
        const style = {};
        while (matchs) {
            style[matchs[1]] = matchs[2];
            matchs = reg1.exec(str);
        }
        // tslint:disable-next-line: no-string-literal
        const x = parseFloat(style['x']);
        const width = x - position.x;
        if (width < 0.2) {
            return '';
        }
        // tslint:disable-next-line: no-string-literal
        return `<rect width="${width}" height="${style['height']}" x="${position.x}" y="${style['y']}"></rect>`;
    }

    /**
     * 设置门诊接口内容
     * @param texts 打印内容
     */
    private setPageContent2(texts: object[]): string {
        return;
    }

    public printContent(data?: ICPrintData): HTMLElement {
        // 重置firstProcessedPageIndex变量，确保每次打印时都能正确识别第一个处理的页面
        this.firstProcessedPageIndex = undefined;
      
        
        const texts = this.getContents(data);
     
        const pager = document.createElement('div');
        let pagers = '';
        if (this.pagePosition) {
            pagers = this.setPageContent1(texts);
        } else {
            pagers = this.setPageContent(texts, data);
        }
        pager.innerHTML = pagers;
        const firstPager = pager.querySelector('.pager');
        const otherPagers = pager.querySelectorAll('.pager:not(:first-child)');
        // console.log(firstPager)
        // console.log(otherPagers)

        // TODO: startPage has at most 2 paragraphline-container, others would have 3 max(last one not hdrftr)
        if (firstPager == null || 0 === texts.length) {
            // tslint:disable-next-line: no-console
            // console.warn('无法获取打印内容(续打未选择区域或页码范围超出最大值)');
            return null;
        }
        const covers = firstPager.querySelectorAll('.opacity ~ .paragraphline-container');
        if (data && data.printType === PrintType.Continuous) {
            // add hdrftr(remove cover) to first page according to config
            if (data.startPageHdrFtr === true) {
                // 无论起始页是哪一页，只要startPageHdrFtr为true，就应该显示页眉页脚
                // 所以对所有covers元素添加transparent类
                for (let i = 0, coverLen = covers.length; i < coverLen; i++) {
                    if (!covers[i].classList.contains('transparent')) {
                        covers[i].classList.add('transparent');
                    }
                }
            }

            // add hdrftr(remove cover) to other pages
            // tslint:disable-next-line: prefer-for-of
            for (let j = 0; j < otherPagers.length; j++) {
                const otherPageCovers = otherPagers[j].querySelectorAll('.opacity ~ .paragraphline-container');
                for (let k = 0; k < otherPageCovers.length; k++) {
                    // ignore the last one
                    if (k !== otherPageCovers.length - 1) {
                        if (!otherPageCovers[k].classList.contains('transparent')) {
                            otherPageCovers[k].classList.add('transparent');
                        }
                    }
                }
            }
        }
        return pager;
    }

    private getFrameWindow(): any {
        return this.iframeContainer;
    }

    private getSelectionStartPage(): number {
        let startPage = -1;
        const logicDocument = this.documentCore.getDocument();
        const selection = logicDocument.selection;
        const smallParaIndex = selection.startPos > selection.endPos ? selection.endPos : selection.startPos;
        const startPara = logicDocument.content[smallParaIndex];
        startPage = startPara.pageNum;

        return startPage;
    }

    // private getUrls(): string[] {
    //     let urls = [];
    //     let contents = this.getContents();
    //     contents.forEach(svg => {
    //         let width = this.width;
    //         var encodedData = window.btoa(unescape(encodeURIComponent(`<svg xmlns="http://www.w3.org/2000/svg"
    // version="1.1" style="width: ${width}px; background: #fff;">${svg}</svg>`)));
    //         let url = "data:image/svg+xml;base64," + encodedData;
    //         urls.push(url);
    //     });
    //     return urls;
    // }

    // private addFrame1(): void {
    //     if (window['printPDF']) {
    //         return;
    //     }
    //     let iframe = document.createElement('div');
    //     iframe.className = 'printFrame-box';
    //     iframe.innerHTML = `<iframe id="printPDF" src=""
    // frameborder="0" style="width: 100%;height: 1122px;"></iframe>`;
    //     document.body.appendChild(iframe);
    // }

    // private printImage(): HTMLElement {
    //     let pager = document.createElement('div');
    //     let pagers = '';
    //     let urls = this.getUrls();
    //     urls.forEach(src => {
    //         pagers += `<div class="pager"><img src="${src}" width="100%" height="1070" /></div>`;
    //     });
    //     pager.innerHTML = pagers;
    //     return pager;
    // }
}
