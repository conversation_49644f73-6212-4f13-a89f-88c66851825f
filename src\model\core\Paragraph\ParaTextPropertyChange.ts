import { ChangeBaseStringProperty, ChangeBaseDoubleProperty } from '../HistoryChange';
import { HistroyItemType, HistoryDescriptionType } from '../HistoryDescription';

export class ChangeParaTextPropertyFont extends ChangeBaseStringProperty {
    private type: HistroyItemType|HistoryDescriptionType = null;

    constructor( changeClass: any, old: any, news: any, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.ParaPortionFont;
    }

    public setValue( value: string ): void {
        const portion = this.getClass();
        portion.textProperty.fontFamily = value;
    }
}

/**
 * portion文本字号
 */
export class ChangePortionFontSize extends ChangeBaseDoubleProperty {
    private type: HistroyItemType|HistoryDescriptionType = null;

    constructor( changeClass: any, old: any, news: any, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.ParaPortionFont;
    }

    public setValue( value: number ): void {
        const portion = this.getClass();
        portion.textProperty.fontSize = value;
    }
}
