@label-width: 60px;
@span-width: 80px;
@active-color: #3664D9;
@bg-color: #3664D9;

.revision-panel {
    width: 100%;
    height: 100%;

    .header {
        display: grid;
        grid-template-columns: 35% 23% 23% 1fr;
        border-bottom: 1px solid black;
        padding: 8px 5px;

        &>div {
            display: flex;
            align-items: center;
        }

        .summary {
            &>span {
                .place {
                    font-weight: bold;
                }
            }
        }
    }

    ul.list {
        list-style: none;
        margin: 0;
        padding: 0;
        overflow: auto;
        position: relative;
        height: calc(100% - 50px);

        &::-webkit-scrollbar {
            width: 7px;
            height: 1px;
        }

        &::-webkit-scrollbar-thumb {
            /*滚动条里面小方块*/
            border-radius: 1px;
            background-color: #ddd;
        }

        &::-webkit-scrollbar-track {
            /*滚动条里面轨道*/
            box-shadow: inset 0 0 2px rgba(0, 0, 0, 0.2);
            background: #f2f2f2;
            border-radius: 10px;
        }

        li.item-empty {
            width: 100%;
            text-align: center;
            color: #949494;
            font-size: 12px;
            line-height: 16px;
            padding: 5px;
        }

        li.item {
            width: 100%;
            word-break: break-all;

            &.active,
            &:hover {
                .title {
                    cursor: pointer;
                    background-color: #3664D9;
                }

                .rev-content {
                    user-select: text;
                    background-color: #F3F3F3;
                }
            }


            .title {
                text-align: center;
                background-color: #009FFF;
                color: white;
                padding: 4px 2px;
                font-size: 14px;
                line-height: 20px;
            }

            .rev-content {
                padding: 5px;
            }
        }
    }
}