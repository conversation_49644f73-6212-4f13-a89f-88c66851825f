import { DocumentCore } from '../../model/DocumentCore';
import { EmrEditor } from '../../components/editor/Main';
import { ResultType } from '../commonDefines';
import {Comment} from '../../model/core/Comment/Comment';
import { ExternalAction } from './ExternalAction';
export default class ExternalComment extends ExternalAction {
    private _documentCore: DocumentCore;
    private _host: EmrEditor;
    constructor(host: EmrEditor) {
        super(host.state.documentCore);
        this._host = host;
        this._documentCore = host.state.documentCore;
    }

    /**
     * 设置批注用户名
     * @param sName 用户名
     */

    public setCommentAuthor(sName: string): number {
        if (!sName || typeof sName !== 'string') {
            return ResultType.ParamError;
        }
        const result = this._documentCore.setCommentAuthor(sName);

        if (result === ResultType.Success) {
            this._host.handleRefresh();
        }
        return result;
    }

    /**
     * 返回当前光标处的批注信息
     */

    public getCurrentCommentInfo(): string {

        const comment = this._documentCore.getCommentByCursor();
        if (!comment) {
            return ResultType.StringEmpty;
        }

        return JSON.stringify(this.getCommentInfo(comment));

    }

    /**
     * 根据批注的id 光标跳转到一个批注头位置
     * @param sName 批注名称
     */

    public jumpToOneCommentByName(sName: string): number {
        if (!sName || typeof sName !== 'string') {
            return ResultType.ParamError;

        }

        const res = this._documentCore.moveCursorInComment(sName);
        if (res === true) {
            this._host.handleRefresh();


            return ResultType.Success;
        }

        return ResultType.Failure;

    }

    // public setPostilsPropertyByAuthor(sAuthor: string, sPropertyName: string, bValue: boolean): number {
    //     const propNames = ['PostilCanDelete', 'PostilCanModify'];
    //     if (!sAuthor || !propNames.includes(sPropertyName) || bValue === undefined) {
    //         return ResultType.ParamError;
    //     }
    //     
    //     const res = this._documentCore.setCommentModifyByUserName(sPropertyName, bValue, sAuthor);
    //     
    //     return res;
    // }

    /**
     * 设置当前光标位置的批注是否可以编辑 是否可以删除
     * @param sPropertyName 批注的属性名 PostilCanDelete、PostilCanModify
     * @param bValue 属性值
     */
    public setPostilsPropertyByCurrentCursor(sPropertyName: string, bValue: boolean): number {
        const propNames = ['PostilCanDelete', 'PostilCanModify'];
        if (!propNames.includes(sPropertyName) || bValue === undefined) {
            return ResultType.ParamError;
        }

        const res = this._documentCore.setCommentModify(sPropertyName, bValue);

        return res;
    }

    /**
     * 根据某个条件显示或者隐藏批注
     * @param bShow 显示或者隐藏批注
     * @param sAuthor 人名 – 控制该人的批注, 为空时为全部
     */
    public showPostil(bShow: boolean, sAuthor?: string): number {
        if (bShow === undefined) {
            return ResultType.ParamError;
        }
        const res = this._documentCore.showCommentByUserName(bShow, sAuthor);
        if (res === ResultType.Success) {
            this._host.handleRefresh();
        }
        return res;
    }


    /**
     * 返回指定名称的批注信息
     * @param sName 批注名称
     */
    public getPostilInfoByName(sName: string): string {
        if (!sName) {
            return ResultType.StringEmpty;
        }
        const comment = this._documentCore.getCommentByName(sName);
        if (!comment) {
            return ResultType.StringEmpty;
        }

        return JSON.stringify(this.getCommentInfo(comment));

    }

    /**
     * 设置指定名称的批注是否可以编辑 是否可以删除
     * @param sName 批注名称
     * @param sPropertyName 属性名称：PostilCanDelete、PostilCanModify
     * @param bValue 属性值
     */
    public setPostilsPropertyByName(sName: string, sPropertyName: string, bValue: boolean): number {
        if (!sName || !sPropertyName || bValue === undefined) {
            return ResultType.ParamError;
        }
        const res = this._documentCore.setCommentModify(sPropertyName, bValue, sName);
        return res;
    }

    /**
     * 显示或者隐藏指定名称的批注
     * @param sName 批注名称
     * @param bShow 显示隐藏
     */
    public showPostilByName(sName: string, bShow: boolean): number {

        if (!sName || bShow === undefined || typeof sName !== 'string') {

            return ResultType.ParamError;
        }
        const res = this._documentCore.showCommentByName(sName, bShow);
        if (res === ResultType.Success) {
            this._host.handleRefresh();
        }

        return res;
    }

    public insertComment(sJson: string): string {
        if (!sJson || typeof sJson !== 'string') {
            return ResultType.StringEmpty;
        }

        let props = {} as any;
        // let userName;
        try {
            const obj = JSON.parse(sJson);
            // userName = obj.name;
            props = {
                //name: obj?.name,
                time: new Date(),
                content: obj.content || '',
                userName: obj.userName,
            }
        } catch (error) {
            return ResultType.StringEmpty;
        }

        const res = this._documentCore.addCommentByCursor(props);
        if (res !== ResultType.StringEmpty) {
            this._documentCore.showCommentPanel(true);
            const comment = this._documentCore.getCommentByName(res);
            comment.getData().setUserName(props.userName);
            comment.getData().setContent(props.content);

            this._host.handleRefresh();
        }

        return res;
    }

    public deleteComment(name: string): number {
        if (!name || typeof name !== 'string') {
            return ResultType.ParamError;
        }

        const res = this._documentCore.deleteCommentByName(name);
        if (res) {
            this._host.handleRefresh();
            return ResultType.Success;
        }

        return ResultType.UnEdited;
    }

    public modifyComment(name: string, sJson: string): number {
        if (!name || typeof name !== 'string' || !sJson || typeof sJson !== 'string') {
            return ResultType.ParamError;
        }

        let props = {};
        try {
            const obj = JSON.parse(sJson);
            props = {
                // id: obj?.id,
                content: obj.content,
            }
        } catch (error) {
            return ResultType.ParamError;
        }

        const res = this._documentCore.updateComment(name, props);
        if (res) {
            this._host.handleRefresh();
            return ResultType.Success;
        }

        return ResultType.ParamError;
    }

    public getCommentContent(name: string): string {
        if (!name || typeof name !== 'string') {
            return ResultType.StringEmpty;
        }

        const comment = this._documentCore.getCommentByName(name);
        if (!comment) {
            return ResultType.StringEmpty;
        }

        const data = comment.getData();
        return `${data.getContent()}`;
    }

    public getAllComments(): string {
        const comments = this._documentCore.getAllComments();
        const res = [];
        comments.forEach((comment) => {
            res.push(this.getCommentInfo(comment));
        });

        return JSON.stringify(res);
    }

    public addCommentReply(name: string, sJson: string): number {
        if (!name || typeof name !== 'string' || !sJson || typeof sJson !== 'string') {
            return ResultType.ParamError;
        }

        let props = {} as any;
        try {
            const obj = JSON.parse(sJson);
            props = {
                userName: obj.userName,
                content: obj.content,
            }
        } catch (error) {
            return ResultType.ParamError;
        }

        const res = this._documentCore.addCommentReply(name, props.content, props.userName);
        if (res) {
            this._host.handleRefresh();
            return ResultType.Success;
        }

        return ResultType.ParamError;
    }

    public showCommentPanel(bShow: boolean): number {
        if (null == bShow || typeof bShow !== 'boolean') {
            return ResultType.ParamError;
        }

        const res = this._documentCore.showCommentPanel(bShow);
        if (ResultType.Success === res) {
            this._host.handleRefresh();
        }

        return res;
    }

    private getCommentInfo(comment: Comment): any {
        const data = comment.getData();

        if (data) {
            return {
                userName: data.getUserName(),
                content: data.getContent(),
                name: comment.getName(),
                time: data.getStringTime(),
                reply: data.replies.length ? data.replies.map((reply) => {
                    return {
                        userName: reply.getUserName(),
                        content: reply.getContent(),
                        time: reply.getLastTime(),
                    };
                }) : undefined,
            };
        }

        return ResultType.StringEmpty;
    }

    private getTime(sTime: string): Date {
        if (!sTime) {
            return new Date();
        }

        const date = new Date(sTime);
        if (!date) {
            return new Date();
        }

        return date;
    }
}
