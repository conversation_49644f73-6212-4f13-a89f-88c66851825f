import { NewControlDefaultSetting, numtoFixed2 } from "@/common/commonDefines";
import { DocumentCore } from "@/model/DocumentCore";
import { getTheme } from "@hz-editor/theme";
import React from "react";

interface IProps {
    children?: any;
    documentCore: DocumentCore;
    index: number;
}

export class RegionBackgroundArea extends React.Component<IProps> {
    constructor(props: IProps) {
        super(props);
    }

    public refresh(): void {
        this.setState({});
    }

    public render(): any {
        const regionAreaInfo = this.getRegionAreaInfos();
        return (
            <>
            {this.renderRegionBackground(regionAreaInfo.backgroundAreas)}
            {this.props.children}
            {this.renderRegionBorder(regionAreaInfo.borders)}
            </>
        );
    }

    private getRegionAreaInfos(): {
        borders: any[];
        backgroundAreas: any[];
    } {
        const { documentCore, index } = this.props;
        const borders = [];
        const backgroundAreas = [];
        const style = { fill: 'transparent', strokeWidth: '1' };
        const backgroundColor = NewControlDefaultSetting.DefaultBackgroundColor;
        const style1: any = {...style};
        const style2: any = {
            color: '#111',
            fontSize: 10,
            cursor: 'pointer',
            pointerEvents: 'none'
        }
        style1.cursor = 'pointer';
        style1.pointerEvents = 'all';
        for (const region of documentCore.getRegionsWithoutFocus(index, undefined, undefined, true, true)) {
            if (!region) {
                continue;
            }
            const showBorder = region.isLoadCache() || region.getAlwaysShow();
            if (showBorder || region.isShowBackgroundColor()) {
                const name = region.getName();
                const lines = documentCore.getRegionBoundLines(region, true);
                const curBorder = [];
                const curArea = [];
                let firstLine;
                for (let idx = 0, length = lines.length; idx < length; idx += 2) {
                    const line = lines[idx];
                    if (line.pageIndex === index) {
                        if (!firstLine) {
                            firstLine = line;
                        }
                        const line2 = lines[idx + 1];
                        const x1 = numtoFixed2(line.x);
                        const x2 = numtoFixed2(line2.width);
                        const y1 = numtoFixed2(line.y);
                        const y2 = numtoFixed2(line2.y);
                        curArea.push({
                            color: backgroundColor,
                            id: `${name}-${idx}`,
                            xStart: x1,
                            xEnd: x2,
                            yStart: y1,
                            yEnd: y2,
                        });
                        curBorder.push((
                            <line
                                key={`${name}-${idx}-1`}
                                x1={x1}
                                y1={y1}
                                x2={x1}
                                y2={y2}
                                style={style}
                            />
                        ));
                        curBorder.push((
                            <line
                                key={`${name}-${idx}-2`}
                                x1={x2}
                                y1={y1}
                                x2={x2}
                                y2={y2}
                                style={style}
                            />
                        ));
                        if (line.bStart) {
                            curBorder.push((
                                <line
                                    key={`${name}-${idx}-3`}
                                    x1={x1}
                                    y1={y1}
                                    x2={x2}
                                    y2={y1}
                                    style={style}
                                />
                            ));
                        }
                        if (line2.bEnd) {
                            curBorder.push((
                                <line
                                    key={`${name}-${idx}-4`}
                                    x1={x1}
                                    y1={y2}
                                    x2={x2}
                                    y2={y2}
                                    style={style}
                                />
                            ));
                        }
                    }
                }
                
                if (showBorder) {
                    borders.push(...curBorder);
                }
                if (region.isShowBackgroundColor()) {
                    backgroundAreas.push(...curArea);
                }

                if (firstLine) {
                    const x1 = numtoFixed2(firstLine.x - 14);
                    borders.push(<rect data-index={name + '?2'} style={style1} x={x1} y={numtoFixed2(firstLine.y)} width={10} height={10} key={`${name}-line-1`}></rect>);
                    borders.push(<text style={style2} x={numtoFixed2(firstLine.x - 11)} y={numtoFixed2(firstLine.y + 8)} key={`${name}-line-2`}>-</text>);
                }
            }
        }
        return { borders, backgroundAreas };
    }

    private renderRegionBackground(backgroundAreas: {
        xEnd: number;
        xStart: number;
        yEnd: number;
        yStart: number;
        color: string;
    }[]) {
        const rects = [];
        let index = 0;
        for (const area of backgroundAreas) {
            rects.push(<rect
                key={'rgarea' + index++}
                fill={area.color}
                x={area.xStart}
                y={area.yStart}
                width={area.xEnd - area.xStart}
                height={area.yEnd - area.yStart}
            />);
        }
        return (
            <g>{rects}</g>
        );
    }

    private renderRegionBorder = (borders: any[]) => {
        return (
            <g className='region-border-still' stroke={getTheme().NewControl.DefaultRegionBorderColor}>
                {borders}
            </g>
        );
    }
}