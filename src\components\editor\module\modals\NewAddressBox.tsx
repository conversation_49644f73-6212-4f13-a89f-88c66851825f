import * as React from 'react';
// tslint:disable-next-line: max-line-length
import { CustomPropertyElementType, HierarchyLevel, INewControlProperty, isValidName, NewControlType, STD_START_DEFAULT } from '../../../../common/commonDefines';
import Dialog from '../../ui/Dialog';
import '../../style/addressbox.less';
import Button from '../../ui/Button';
import Input from '../../ui/Input';
import Checkbox from '../../ui/CheckboxItem';
import CustomProperty from './CustomProperty';
import Select from '../../ui/select/Select';
import { IFRAME_MANAGER } from '../../../../common/IframeManager';
import { message } from '../../../../common/Message';
import { ExternalDataBind } from './ExternalDataBind';

interface IDialogProps {
  documentCore: any;
  visible: boolean;
  property?: INewControlProperty;
  id: string;
  close: (name: string | number, bRefresh?: boolean) => void;
}

interface IState {
  bRefresh: boolean;
}

export default class NewAddressBox extends React.Component<IDialogProps, IState> {
  private addressBoxProperty: INewControlProperty;
  private visible: any;
  private bCustomProperty: boolean;
  private hierarchy: any[];
  private resetSourceBind: boolean;
  private dataBind: any;
  // TODO: data store

  constructor(props: any) {
    super(props);
    this.state = {
      bRefresh: false,
    };
    this.visible = this.props.visible;
    this.addressBoxProperty = {newControlName: undefined};
    this.hierarchy = [
      {key: '1级', value: HierarchyLevel.First},
      {key: '2级', value: HierarchyLevel.Second},
      {key: '3级', value: HierarchyLevel.Third},
    ];
  }

  public UNSAFE_componentWillReceiveProps(nextProps: any): void {
    this.visible = nextProps.visible;
  }

  public render(): any {
    return (
      <Dialog
        visible={this.visible}
        width={350}
        open={this.open}
        title='地址框'
        footer={this.renderFooter()}
        id={this.props.id}
      >
        <div className='address-box'>
          <div className='editor-line'>
            <span className='title'>常规</span>
          </div>
          <div className='editor-line'>
            <div className='w-70'>名称:</div>
            <div className='right-auto'>
              <Input
                name='newControlName'
                value={this.addressBoxProperty.newControlName}
                onChange={this.onChange}
              />
            </div>
          </div>
              <div className='editor-line'>
                  <div className='w-70'>内部名称</div>
                  <div className='right-auto'>
                      <Input
                          name='newControlSerialNumber'
                          value={this.addressBoxProperty.newControlSerialNumber}
                          onChange={this.onChange}
                          readonly={true}
                          placeholder={''}
                          disabled={true}
                      />
                  </div>
              </div>
          <div className='editor-line'>
              <div className='w-70'>标识符</div>
              <div className='right-auto'>
                  <Input
                      name='identifier'
                      value={this.addressBoxProperty.identifier}
                      onChange={this.onChange}
                  />
              </div>
          </div>
          <div className='editor-line'>
            <div className='w-70'>提示符:</div>
            <div className='right-auto'>
              <Input
                name='newControlInfo'
                value={this.addressBoxProperty.newControlInfo}
                onChange={this.onChange}
              />
            </div>
          </div>
          <div className='editor-line'>
            <div className='w-70'>占位符:</div>
            <div className='right-auto'>
              <Input
                name='newControlPlaceHolder'
                value={this.addressBoxProperty.newControlPlaceHolder}
                onChange={this.onChange}
              />
            </div>
          </div>
          <div className='editor-line'>
            <div className='w-70'>层级:</div>
            <div className='right-auto'>
              <Select
                data={this.hierarchy}
                name='hierarchy'
                value={this.addressBoxProperty.hierarchy}
                onChange={this.onChange}
              />
            </div>
          </div>
          <div className='editor-line editor-multi-line'>
            <span className='title w-70'>属性</span>
            <div className='right-auto newcontrol-prop'>
              <div className='editor-line'>
                <div className='w-050'>
                  <Checkbox
                    name='isNewControlHidden'
                    disabled={false}
                    value={this.addressBoxProperty.isNewControlHidden}
                    onChange={this.onChange}
                  >
                    隐藏
                  </Checkbox>
                </div>
                <div className='w-050'>
                  <Checkbox
                    name='isNewControlCanntDelete'
                    value={this.addressBoxProperty.isNewControlCanntDelete}
                    onChange={this.onChange}
                  >
                    禁止删除
                  </Checkbox>
                </div>
              </div>
              <div className='editor-line'>
                <div className='w-050'>
                  <Checkbox
                    name='isNewControlCanntEdit'
                    value={this.addressBoxProperty.isNewControlCanntEdit}
                    onChange={this.onChange}
                  // disabled={true}
                  >
                    禁止编辑
                  </Checkbox>
                </div>
                <div className='w-050'>
                  <Checkbox
                    name='isNewControlMustInput'
                    value={this.addressBoxProperty.isNewControlMustInput}
                    onChange={this.onChange}
                  >
                    必填项
                  </Checkbox>
                </div>
              </div>
              <div className='editor-line'>
                <div className='w-050'>
                  <Checkbox
                    name='isNewControlReverseEdit'
                    value={this.addressBoxProperty.isNewControlReverseEdit}
                    onChange={this.onChange}
                  >
                    反向编辑
                  </Checkbox>
                </div>
                <div className='w-050'>
                  <Checkbox
                    name='isNewControlShowBorder'
                    value={this.addressBoxProperty.isNewControlShowBorder}
                    onChange={this.onChange}
                  >
                    显示边框
                  </Checkbox>
                </div>
              </div>
              <div className='editor-line'>
                <div className='w-050'>
                  <Checkbox
                    name='isNewControlHiddenBackground'
                    value={this.addressBoxProperty.isNewControlHiddenBackground}
                    onChange={this.onChange}
                  >
                    隐藏背景色
                  </Checkbox>
                </div>
                <div className='w-050'>
                  <Checkbox
                    name='tabJump'
                    value={this.addressBoxProperty.tabJump}
                    onChange={this.onChange}
                  >
                      键盘跳转
                  </Checkbox>
                </div>
              </div>
              <div className='editor-line'>
                <CustomProperty
                  name='customProperty'
                  properties={this.addressBoxProperty.customProperty}
                  documentCore={this.props.documentCore}
                  onChange={this.onChange}
                  close={this.onClose}
                  id='bCustomProperty'
                  visible={this.bCustomProperty}
                  type={CustomPropertyElementType.NewControl}
                />
              </div>

            </div>
          </div>
          <ExternalDataBind
                  name={this.addressBoxProperty.newControlName}
                  id='externalDataBind'
                  visible={this.visible}
                  documentCore={this.props.documentCore}
                  onChange={this.onChange}
                  close={this.onClose}
                  properties={this.dataBind}
                  resetId={'resetSourceBind'}
          />
        </div>
      </Dialog>
    );
  }

  private onClose = (id: string, bRefresh: boolean): void => {
    this[id] = false;
    if (id === 'bCustomProperty' && bRefresh) {
        this.setState({bRefresh: !this.state.bRefresh});
    } else if ('externalDataBind' === id && bRefresh) {
      this.resetSourceBind = false;
      this.setState({bRefresh: !this.state.bRefresh});
    }
  }

  private open = (): void => {
    const props = this.props.property;
    // console.log(props)
    const newControlProps: INewControlProperty = this.addressBoxProperty = {} as any;
    if (props === undefined) {
      this.init();
      newControlProps.newControlName = this.props.documentCore.makeUniqueNewControlName(NewControlType.AddressBox);
    } else {
      const keys = Object.keys(props);
      keys.forEach((key) => {
          const val = props[key];
          newControlProps[key] = val;
      });
    }
    this.dataBind = newControlProps.externalDataBind;
    this.setState({bRefresh: !this.state.bRefresh});
  }

  private init(): void {
    const addressBoxProperty = this.addressBoxProperty;
    addressBoxProperty.newControlName = undefined;
    addressBoxProperty.newControlSerialNumber = undefined;
    addressBoxProperty.newControlInfo = undefined;
    addressBoxProperty.newControlPlaceHolder = '请选择地址';
    addressBoxProperty.newControlType = NewControlType.AddressBox;
    addressBoxProperty.isNewControlHidden = false;
    addressBoxProperty.isNewControlCanntDelete = false;
    addressBoxProperty.isNewControlCanntEdit = false;
    addressBoxProperty.isNewControlShowBorder = true;
    addressBoxProperty.isNewControlHiddenBackground = true;
    addressBoxProperty.isNewControlCanntCopy = false;
    addressBoxProperty.customProperty = undefined;
    addressBoxProperty.tabJump = true;

    // extension
    addressBoxProperty.hierarchy = STD_START_DEFAULT.hierarchy;
    addressBoxProperty.province = null;
    addressBoxProperty.city = null;
    addressBoxProperty.county = null;
    addressBoxProperty.identifier = undefined;
    addressBoxProperty.externalDataBind = undefined;
    this.resetSourceBind = false;
  }

  private renderFooter(): any {
    return (
        <span>
            <Button onClick={this.close}>取消</Button>
            <Button type='primary' onClick={this.confirm}>确认</Button>
        </span>
    );
  }

  private onChange = (value: any, name: string): void => {
    // this.addressBoxProperty[name] = value;
    if ('resetSourceBind' !== name) {
        if ('externalDataBind' !== name) {
            this.addressBoxProperty[name] = value;
        } else {
            this.dataBind = value;
        }
    } else {
        this.resetSourceBind = true;
    }
    // console.log(name, value)
    if (name === 'hierarchy') {
      if (value === HierarchyLevel.Second) {
        this.addressBoxProperty.county = null;
      } else if (value === HierarchyLevel.First) {
        this.addressBoxProperty.county = null;
        this.addressBoxProperty.city = null;
      }
    }
  }

  private close = (bRefresh?: boolean): void => {
    this.props.close(this.props.id, bRefresh);
    this.visible = false;
    this.setState({bRefresh: !this.state.bRefresh});
  }

  private confirm = (): void => {
    const documentCore = this.props.documentCore;
    IFRAME_MANAGER.setDocId(documentCore.getCurrentId());
    const props = this.props.property;
    const addressBoxProperty = this.addressBoxProperty;
    // console.log(addressBoxProperty)
    if (addressBoxProperty.identifier && !isValidName(addressBoxProperty.identifier)) {
      message.error('标识符不符合规范，请重新输入');
      return;
    }

    if (!isValidName(addressBoxProperty.newControlName)) {
      message.error('名称不符合规范，请重新输入');
      return;
    }
    if ((!props || props.newControlName !== addressBoxProperty.newControlName)
      && !documentCore.checkNewControlName(addressBoxProperty.newControlName)) {
      message.error('已存在该名字，请重新命名');
      return;
    }

    if (this.resetSourceBind) {
        addressBoxProperty.externalDataBind = {
            sourceObj: '',
            sourceKey: '',
            bReadOnly: false,
            commandUpdate: 1,
        }

        this.resetSourceBind = false;
    } else if (this.dataBind) {
        addressBoxProperty.externalDataBind = this.dataBind;
    }

    if (props === undefined) {
      documentCore.addNewControl(addressBoxProperty);
    } else {
      documentCore.setNewControlProperty(addressBoxProperty, props.newControlName);

      // also update text
      this.updateAddressText();
    }

    this.close(true);
  }

  private updateAddressText(): void {
    const {documentCore} = this.props;
    const addressBoxProperty = this.addressBoxProperty;

    // get text
    const {province, city, county} = addressBoxProperty;
    let text = '';
    if (province != null) {
      text += province.name;
    }
    if (city != null) {
      text += city.name;
    }
    if (county != null) {
      text += county.name;
    }

    if (text != null && text.length > 0) {
      documentCore.setNewControlText(this.addressBoxProperty.newControlName, text);
      documentCore.recalculate();
    }
  }

}
