import * as React from 'react';
import Dialog from '../../ui/Dialog';
import {message} from '../../../../common/Message';
import Input from '../../ui/Input';
import Button from '../../ui/Button';
import { IExternalDataProperty, isValidName } from '../../../../common/commonDefines';
import { IFRAME_MANAGER } from '../../../../common/IframeManager';
import { ExternalDataBind } from './ExternalDataBind';
// import QRCode from 'qrcode';
import Select from '../../ui/select/Select';
import { genarateQRCode } from '@/common/commonMethods';

interface IProps {
    documentCore: any;
    visible: boolean;
    id: string;
    image?: any;
    close: (name: string | number, bRefresh?: boolean) => void;
}

interface IState {
    bRefresh: boolean;
}

/**
 * 纠错能力
 */
enum CorrectLevel {
    'L' = 'L',
    'M' = 'M',
    'Q' = 'Q',
    'H' = 'H',
}

export default class InsertQRCode extends React.Component<IProps, IState> {
    private image: {imageName: string, imageSource: string, imageWidth: number, imageHeight: number,
        content: string, errorCL: string, externalDataBind: IExternalDataProperty};

    // private showImageError: string = '';
    // private imageName: string;
    // private imageSizeRatio: number;
    // private timeout: any;
    private visible: boolean;
    private resetSourceBind: boolean;
    private dataBind: any;
    private errorCL: any[];
    // private bInputValid: boolean;
    constructor(props: any) {
        super(props);
        this.state = {
            bRefresh: false,
        };
        this.image = {
            imageName: '',
            imageSource: undefined,
            imageWidth: 50,
            imageHeight: 50,
            content: '',
            errorCL: CorrectLevel.H,
            externalDataBind: undefined,
        };
        this.errorCL = [
            {
                key: 'L：7%的字符可被修正',
                value: CorrectLevel.L,
            },
            {
                key: 'M：15%的字符可被修正',
                value: CorrectLevel.M,
            },
            {
                key: 'Q：25%的字符可被修正',
                value: CorrectLevel.Q,
            },
            {
                key: 'H：30%的字符可被修正',
                value: CorrectLevel.H,
            },
        ];
        this.visible = this.props.visible;
    }

    public render(): any {
        // TODO: bSignPic?
        const visibility = this.image.imageSource ? 'visible' : 'hidden';
        return (
            <Dialog
                visible={this.visible}
                width={320}
                open={this.open}
                title={'二维码设置'}
                footer={this.renderFooter()}
                id={this.props.id}
            >
                <div>
                    <div className='editor-line'>
                        <div className='w-70'>名称：</div>
                        <div className='right-auto'>
                            <Input
                                value={this.image.imageName}
                                onChange={this.onChange}
                                name='imageName'
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <div className='w-70'>文本内容：</div>
                        <div className='right-auto'>
                            <Input
                                value={this.image.content}
                                onChange={this.onChange}
                                name='content'
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <div className='w-70'>纠错能力</div>
                        <div className='right-auto'>
                            <Select
                                name='errorCL'
                                onChange={this.onChange}
                                data={this.errorCL}
                                value={this.image.errorCL}
                            />
                        </div>
                    </div>
                    <div className='editor-line editor-multi-line' style={{marginBottom: 20}}>
                        <div className='w-70'>预览：</div>
                        <div className='right-auto' style={{marginTop: 5, marginBottom: 5, border: '1px solid #D5D4DC'}}>
                            <img id='QRCode' width='100%' height={90} style={{visibility, scale: '60%'}} src={this.image.imageSource} />
                        </div>
                        <div style={{border: '1px solid #D5D4DC', width: 40, float: 'right'}}>
                            <span style={{margin: 5, lineHeight: 1.5}} onClick={this.updateQRCode}>刷新</span>
                        </div>
                    </div>
                    <ExternalDataBind
                        name={this.image.imageName}
                        id='externalDataBind'
                        visible={this.visible}
                        documentCore={this.props.documentCore}
                        onChange={this.onChange}
                        close={this.onClose}
                        properties={this.dataBind}
                        resetId={'resetSourceBind'}
                        bBarcode={true}
                    />
                </div>
            </Dialog>
        );
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        this.visible = nextProps.visible;
    }

    private renderFooter(): any {
        return (
            <span>
                <Button onClick={this.close}>取消</Button>
                <Button type='primary' onClick={this.confirm}>确认</Button>
            </span>
        );
    }

    private onClose = (id: string, bRefresh: boolean): void => {
        this[id] = false;
        if ('externalDataBind' === id && bRefresh) {
            this.resetSourceBind = false;
            this.setState({bRefresh: !this.state.bRefresh});
        }
    }

    private close = (bRefresh?: boolean): void => {
        this.visible = false;
        this.setState({bRefresh: !this.state.bRefresh});
        this.props.close(this.props.id, bRefresh);
    }

    private open = (): void => {
        const { image, documentCore }= this.props;
        if (!image) {
            this.image = {
                imageName: documentCore.getBarcodeName(),
                imageSource: undefined,
                imageWidth: 50,
                imageHeight: 50,
                errorCL: CorrectLevel.H,
                content: '',
                externalDataBind: undefined,
            };
        } else {
            this.image.imageName = image.name;
            this.image.content = image.content;
            this.image.imageSource = image.src;
            this.image.imageWidth = image.width;
            this.image.imageHeight = image.height;
            this.image.errorCL = image.errorCL;
            this.image.externalDataBind = image.externalDataBind;
            this.dataBind = image.externalDataBind;
        }

        this.resetSourceBind = false;
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private onChange = (value: any, name: string): void => {
        if ('resetSourceBind' !== name) {
            if ('externalDataBind' !== name) {
                this.image[name] = value;
                // if (name === 'content' || 'bUse' === name || 'textAlign' === name || name === 'imageHeight') {
                //     this.genarate();
                //     this.setState({bRefresh: !this.state.bRefresh});
                // }
            } else {
                this.dataBind = value;
            }
        } else {
            this.resetSourceBind = true;
        }
    }

    private confirm = (): void => {
        const documentCore = this.props.documentCore;
        if (documentCore.isProtectedMode()) {
            this.close(true);
            return;
        }
        IFRAME_MANAGER.setDocId(documentCore.getCurrentId());
        const image = this.image;
        if (!isValidName(image.imageName)) {
            message.error('名称不符合规范，请重新输入');
            return;
        }
        if ((!this.props.image && !documentCore.checkUniqueImageName(image.imageName)) ||
            (this.props.image && !documentCore.checkUniqueImageNameOtherThanSelectedImage(image.imageName))) {
            message.error('名称已存在，请重新命名');
            return ;
        }

        // if (!this.isContentValid()) {
        //     return ;
        // }

        if (this.resetSourceBind) {
            this.image.externalDataBind = {
                sourceObj: '',
                sourceKey: '',
                bReadOnly: false,
                commandUpdate: 1,
            }

            this.resetSourceBind = false;
        } else if (this.dataBind) {
            this.image.externalDataBind = this.dataBind;
            this.dataBind = undefined;
        }

        this.genarate();

        if (this.props.image) {
            documentCore.setDrawingProp(this.props.image.name, {name: image.imageName,
                width: image.imageWidth, height: image.imageHeight, content: image.content,
                externalDataBind: image.externalDataBind, src: image.imageSource, errorCL: image.errorCL
            });
        } else {
            // if (!this.image.imageSource) {
            // this.genarate();
            // }

            const datas = {
                content: image.content,
                errorCL: image.errorCL,
                externalDataBind: image.externalDataBind,
            }

            documentCore.addInlineImage(image.imageWidth, image.imageHeight, image.imageSource, image.imageName,
                3, image.imageSource, null, null, datas);
        }

        this.close(true);
    }

    private genarate = (): void => {
        if (!this.image.content) {
            this.image.imageSource = undefined;
            return ;
        }

        // const image = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
        // new QRCode(image, {
        //     text : this.image.content,
        //     width : 50,
        //     height : 50,
        //     useSVG: true,
        //     });
        // this.image.imageSource = this.props.documentCore.getDrawingObjects().convertSVGToImageString(image);
        // QRCode.toString(this.image.content, {
        //     errorCorrectionLevel: this.image.errorCL, // 'H',
        //     type: 'svg',
        //     // quality: 0.3,
        //     margin: 0,
        //     // color: {
        //     //     dark:"#000000",
        //     //     light:"#FFFFFF"
        //     // }
        // }, this.callback);
        genarateQRCode({
            content: this.image.content,
            errorCL: this.image.errorCL, // 'H',
            // type: 'svg',
            // quality: 0.3,
            // margin: 0,
            // color: {
            //     dark:"#000000",
            //     light:"#FFFFFF"
            // }
            callback: this.callback,
        });
    }

    private callback = (err, url) => {
        if (err) {
            console.log(err);
        }
        this.image.imageSource = this.props.documentCore.getDrawingObjects()
                                    ?.convertSVGToImageString(url);
    }

    private isContentValid = (): boolean => {
        if (!this.image.content) {
            message.error('请输入文本内容');
            return false;
        }

        return true;
    }

    private updateQRCode = (): void => {
        this.genarate();
        this.setState({bRefresh: !this.state.bRefresh});
    }
}
