import { XmlComponent } from '../../xml-components';
// tslint:disable-next-line: max-line-length
import { NewControlContentSecretType, DateBoxFormat, CodeValueItem, ICustomFormatDateProps, NewControlType, ICascade, HierarchyLevel } from '../../../../../common/commonDefines';
import { customEncodeURIComponent } from '../../../../../common/commonMethods';
import { IAddressPair } from 'src/components/editor/module/document/NewAddressbox';

export interface IContentControlStartElementVals { // 结构化所有属性
  serialNumber?: string;
  placeholder: string;
  helpTip?: string; // 提示信息
  isMustFill: number;
  deleteProtect: number;
  editProtect: number;
  copyProtect: number;
  showBorder: number;
  borderString: string;
  editReverse: number;
  backgroundColorHidden: number;
  customProperty: Map<string, any>;
  newControlHidden?: number;
  tabJump?: number;
  showRight?: number; // 勾选框居右 (checkbox, radiobox)
  cascade?: ICascade[];
  bTextBorder?: number;

  // text struct
  secretType?: NewControlContentSecretType; // both in text and value struct
  fixedLength?: number;
  maxLength?: number;
  title?: string;
  hideHasTitle?: number;

  // value struct
  minValue?: number;
  maxValue?: number;
  precision?: number; // 精度
  unit?: string;
  forceValidate?: number;

  // multi struct
  retrieve?: number;
  selectPrefixContent?: string;
  prefixContent?: string;
  separator?: string;
  itemList?: CodeValueItem[];
  showValue?: number;
  codeLabel?: string;
  valueLabel?: string;

  // date struct
  dateBoxFormat?: DateBoxFormat;
  customFormat?: ICustomFormatDateProps;
  startDate?: string;
  endDate?: string;
  dateTime?: string; // iso string

  // checkbox
  checked?: number;
  printSelected?: number;
  label?: string;
  group?: string;
  labelCode?: string;

  // radiobox
  showType?: NewControlType;
  spaceNum?: number;
  supportMultLines?: number;

  // signature
  signatureCount?: number;
  preText?: string;
  signatureSeparator?: string;
  postText?: string;
  signaturePlaceholder?: string;
  signatureRatio?: number;
  rowHeightRestriction?: number; // boolean actually
  bShowCodeAndValue?: number;

  signType?: number;
  alwaysShow?: number;
  showSignBorder?: number;

  // ui only
  showPlaceholder?: number;

  // address
  hierarchy?: HierarchyLevel;
  province?: IAddressPair;
  city?: IAddressPair;
  county?: IAddressPair;
  identifier?: string;
  alignments?: number;
  eventInfo?: object;
}

export interface IContentControlEndElementVals {
  borderString: string;
}

export class SerialNumber extends XmlComponent {
  constructor(text: string) {
      super('serialNumber');
      if (text != null) {
        // console.log(text)
        this.root.push(text);
      }
  }
}

export class Placeholder extends XmlComponent {
    constructor(text: string) {
        super('placeholder');
        if (text != null) {
          // console.log(text)
          this.root.push(customEncodeURIComponent(text));
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class CodeLabel extends XmlComponent {
  constructor(text: string) {
      super('codeLabel');
      if (text != null) {
        // console.log(text)
        this.root.push(customEncodeURIComponent(text));
      }
  }
}

// tslint:disable-next-line: max-classes-per-file
export class ValueLabel extends XmlComponent {
  constructor(text: string) {
      super('valueLabel');
      if (text != null) {
        // console.log(text)
        this.root.push(customEncodeURIComponent(text));
      }
  }
}

// tslint:disable-next-line: max-classes-per-file
export class Identifier extends XmlComponent {
  constructor(text: string) {
      super('identifier');
      if (text != null) {
        // console.log(text)
        this.root.push(customEncodeURIComponent(text));
      }
  }
}

// tslint:disable-next-line: max-classes-per-file
export class HelpTip extends XmlComponent {
  constructor(text: string) {
      super('helpTip');
      if (text != null) {
        this.root.push(text);
      }
  }
}

// tslint:disable-next-line: max-classes-per-file
export class IsMustFill extends XmlComponent {
  constructor(flag: string) {
      super('isMustFill');
      if (flag) {
        this.root.push(flag);
      }
  }
}

// tslint:disable-next-line: max-classes-per-file
export class DeleteProtect extends XmlComponent {
  constructor(flag: string) {
      super('deleteProtect');
      if (flag) {
        this.root.push(flag);
      }
  }
}

// tslint:disable-next-line: max-classes-per-file
export class EditProtect extends XmlComponent {
  constructor(flag: string) {
      super('editProtect');
      if (flag) {
        this.root.push(flag);
      }
  }
}

// tslint:disable-next-line: max-classes-per-file
export class TextBorder extends XmlComponent {
  constructor(flag: string) {
      super('bTextBorder');
      if (flag) {
        this.root.push(flag);
      }
  }
}

// tslint:disable-next-line: max-classes-per-file
export class CopyProtect extends XmlComponent {
  constructor(flag: string) {
      super('copyProtect');
      if (flag) {
        this.root.push(flag);
      }
  }
}

// tslint:disable-next-line: max-classes-per-file
export class LogicEvent extends XmlComponent {
  constructor(text: string) {
    super('logicEvent');
    if (text) {
      this.root.push(text);
    }
  }
}

// tslint:disable-next-line: max-classes-per-file
export class ShowBorder extends XmlComponent {
  constructor(flag: string) {
      super('showBorder');
      if (flag) {
        this.root.push(flag);
      }
  }
}

// tslint:disable-next-line: max-classes-per-file
export class ShowCodeAndValue extends XmlComponent {
  constructor(flag: string) {
      super('bShowCodeAndValue');
      if (flag) {
        this.root.push(flag);
      }
  }
}

// tslint:disable-next-line: max-classes-per-file
export class BorderString extends XmlComponent {
  constructor(borderString: string) {
      super('borderString');
      if (borderString) {
        this.root.push(borderString);
      }
  }
}

// tslint:disable-next-line: max-classes-per-file
export class EditReverse extends XmlComponent {
  constructor(flag: string) {
      super('editReverse');
      if (flag) {
        this.root.push(flag);
      }
  }
}

// tslint:disable-next-line: max-classes-per-file
export class Hidden extends XmlComponent {
  constructor(flag: string) {
      super('hidden');
      if (flag) {
        this.root.push(flag);
      }
  }
}

// tslint:disable-next-line: max-classes-per-file
export class BackgroundColorHidden extends XmlComponent {
  constructor(flag: string) {
      super('backgroundColorHidden');
      if (flag) {
        this.root.push(flag);
      }
  }
}

// tslint:disable-next-line: max-classes-per-file
export class SecretType extends XmlComponent {
  constructor(flag: string) {
      super('secretType');
      if (flag) {
        this.root.push(flag);
      }
  }
}

// tslint:disable-next-line: max-classes-per-file
export class FixedLength extends XmlComponent {
  constructor(text: string) {
      super('fixedLength');
      if (text) {
        this.root.push(text);
      }
  }
}

// tslint:disable-next-line: max-classes-per-file
export class MaxLength extends XmlComponent {
  constructor(text: string) {
      super('maxLength');
      if (text) {
        this.root.push(text);
      }
  }
}

// tslint:disable-next-line: max-classes-per-file
export class Title extends XmlComponent {
  constructor(text: string) {
      super('title');
      if (text) {
        this.root.push(customEncodeURIComponent(text));
      }
  }
}
// tslint:disable-next-line: max-classes-per-file
export class HideHasTitle extends XmlComponent {
  constructor(text: string) {
      super('hideHasTitle');
      if (text) {
        this.root.push(text);
      }
  }
}
// tslint:disable-next-line: max-classes-per-file
export class TipsContent extends XmlComponent {
  constructor(text: string) {
      super('tip');
      if (text) {
        this.root.push(customEncodeURIComponent(text));
      }
  }
}

// tslint:disable-next-line: max-classes-per-file
export class ShowTitle extends XmlComponent {
  constructor(flag: string) {
      super('showTitle');
      if (flag) {
        this.root.push(flag);
      }
  }
}

// tslint:disable-next-line: max-classes-per-file
export class MinValue extends XmlComponent {
  constructor(text: string) {
      super('minValue');
      if (text) {
        this.root.push(text);
      }
  }
}

// tslint:disable-next-line: max-classes-per-file
export class Alignments extends XmlComponent {
  constructor(text: string) {
      super('interAlign');
      if (text) {
        this.root.push(text);
      }
  }
}

// tslint:disable-next-line: max-classes-per-file
export class EventInfo extends XmlComponent {
  constructor(text: string) {
      super('eventInfo');
      if (text) {
        this.root.push(text);
      }
  }
}

// tslint:disable-next-line: max-classes-per-file
export class MaxValue extends XmlComponent {
  constructor(text: string) {
      super('maxValue');
      if (text) {
        this.root.push(text);
      }
  }
}

// tslint:disable-next-line: max-classes-per-file
export class Precision extends XmlComponent {
  constructor(text: string) {
      super('precision');
      if (text) {
        this.root.push(text);
      }
  }
}

// tslint:disable-next-line: max-classes-per-file
export class Unit extends XmlComponent {
  constructor(text: string) {
      super('unit');
      if (text) {
        this.root.push(text);
      }
  }
}

// tslint:disable-next-line: max-classes-per-file
export class ForceValidate extends XmlComponent {
  constructor(text: string) {
      super('forceValidate');
      if (text != null) {
        this.root.push(text);
      }
  }
}

// tslint:disable-next-line: max-classes-per-file
export class Retrieve extends XmlComponent {
  constructor(flag: string) {
      super('retrieve');
      if (flag) {
        this.root.push(flag);
      }
  }
}

// tslint:disable-next-line: max-classes-per-file
export class SelectPrefixContent extends XmlComponent {
  constructor(text: string) {
      super('selectPrefixContent');
      if (text) {
        this.root.push(text);
      }
  }
}

// tslint:disable-next-line: max-classes-per-file
export class PrefixContent extends XmlComponent {
  constructor(text: string) {
      super('prefixContent');
      if (text) {
        this.root.push(text);
      }
  }
}

// tslint:disable-next-line: max-classes-per-file
export class Separator extends XmlComponent {
  constructor(text: string) {
      super('separator');
      if (text) {
        this.root.push(text);
      }
  }
}

// tslint:disable-next-line: max-classes-per-file
export class DateType extends XmlComponent {
  constructor(flag: string) {
      super('dateType');
      if (flag) {
        this.root.push(flag);
      }
  }
}

// tslint:disable-next-line: max-classes-per-file
export class CustomDateFormat extends XmlComponent {
  constructor(flag: string) {
      super('customDateFormat');
      if (flag) {
        this.root.push(flag);
      }
  }
}

// tslint:disable-next-line: max-classes-per-file
export class StartDate extends XmlComponent {
  constructor(flag: string) {
      super('startDate');
      if (flag) {
        this.root.push(flag);
      }
  }
}

// tslint:disable-next-line: max-classes-per-file
export class EndDate extends XmlComponent {
  constructor(flag: string) {
      super('endDate');
      if (flag) {
        this.root.push(flag);
      }
  }
}

// tslint:disable-next-line: max-classes-per-file
export class DateTime extends XmlComponent {
  constructor(flag: string) {
      super('dateTime');
      if (flag) {
        this.root.push(flag);
      }
  }
}

// tslint:disable-next-line: max-classes-per-file
export class ShowRight extends XmlComponent {
  constructor(flag: string) {
      super('showRight');
      if (flag) {
        this.root.push(flag);
      }
  }
}

// tslint:disable-next-line: max-classes-per-file
export class TabJump extends XmlComponent {
  constructor(flag: string) {
      super('tabJump');
      if (flag) {
        this.root.push(flag);
      }
  }
}

// tslint:disable-next-line: max-classes-per-file
export class Checked extends XmlComponent {
  constructor(flag: string) {
      super('checked');
      if (flag) {
        this.root.push(flag);
      }
  }
}

// tslint:disable-next-line: max-classes-per-file
export class PrintSelected extends XmlComponent {
  constructor(flag: string) {
      super('printSelected');
      if (flag) {
        this.root.push(flag);
      }
  }
}

// tslint:disable-next-line: max-classes-per-file
export class Label extends XmlComponent {
  constructor(text: string) {
      super('label');
      if (text) {
        this.root.push(text);
      }
  }
}

// tslint:disable-next-line: max-classes-per-file
export class LabelCode extends XmlComponent {
  constructor(text: string) {
      super('labelCode');
      if (text) {
        this.root.push(text);
      }
  }
}

// tslint:disable-next-line: max-classes-per-file
export class NewControlHidden extends XmlComponent {
  constructor(flag: string) {
      super('newControlHidden');
      if (flag) {
        this.root.push(flag);
      }
  }
}

// tslint:disable-next-line: max-classes-per-file
export class ShowPlaceholder extends XmlComponent {
  constructor(flag: string) {
      super('showPlaceholder');
      if (flag) {
        this.root.push(flag);
      }
  }
}

// tslint:disable-next-line: max-classes-per-file
export class ShowType extends XmlComponent {
  constructor(flag: string) {
      super('showType');
      if (flag) {
        this.root.push(flag);
      }
  }
}

// tslint:disable-next-line: max-classes-per-file
export class SpaceNum extends XmlComponent {
  constructor(text: string) {
      super('spaceNum');
      if (text) {
        this.root.push(text);
      }
  }
}

export class SupportMultLines extends XmlComponent {
  constructor(text: string) {
      super('supportMultLines');
      if (text) {
        this.root.push(text);
      }
  }
}

// tslint:disable-next-line: max-classes-per-file
export class SignatureCount extends XmlComponent {
  constructor(flag: string) {
      super('signatureCount');
      if (flag) {
        this.root.push(flag);
      }
  }
}

// tslint:disable-next-line: max-classes-per-file
export class PreText extends XmlComponent {
  constructor(text: string) {
      super('preText');
      if (text != null) {
        this.root.push(text);
      }
  }
}

// tslint:disable-next-line: max-classes-per-file
export class SignatureSeparator extends XmlComponent {
  constructor(text: string) {
      super('signatureSeparator');
      if (text != null) {
        this.root.push(text);
      }
  }
}

// tslint:disable-next-line: max-classes-per-file
export class PostText extends XmlComponent {
  constructor(text: string) {
      super('postText');
      if (text != null) {
        this.root.push(text);
      }
  }
}

// tslint:disable-next-line: max-classes-per-file
export class SignaturePlaceholder extends XmlComponent {
  constructor(text: string) {
      super('signaturePlaceholder');
      if (text) {
        this.root.push(text);
      }
  }
}

// tslint:disable-next-line: max-classes-per-file
export class SignatureRatio extends XmlComponent {
  constructor(text: string) {
      super('signatureRatio');
      if (text) {
        this.root.push(text);
      }
  }
}

// tslint:disable-next-line: max-classes-per-file
export class RowHeightRestriction extends XmlComponent {
  constructor(flag: string) {
      super('rowHeightRestriction');
      if (flag) {
        this.root.push(flag);
      }
  }
}

// tslint:disable-next-line: max-classes-per-file
export class Hierarchy extends XmlComponent {
  constructor(text: string) {
      super('hierarchy');
      if (text) {
        this.root.push(text);
      }
  }
}

// tslint:disable-next-line: max-classes-per-file
export class Province extends XmlComponent {
  constructor(text: string) {
      super('province');
      if (text) {
        this.root.push(text);
      }
  }
}

// tslint:disable-next-line: max-classes-per-file
export class City extends XmlComponent {
  constructor(text: string) {
      super('city');
      if (text) {
        this.root.push(text);
      }
  }
}

// tslint:disable-next-line: max-classes-per-file
export class County extends XmlComponent {
  constructor(text: string) {
      super('county');
      if (text) {
        this.root.push(text);
      }
  }
}

// tslint:disable-next-line: max-classes-per-file
export class ShowValue extends XmlComponent {
  constructor(text: string) {
    super('showValue');
    if (text) {
      this.root.push(text);
    }
  }
}

// tslint:disable-next-line: max-classes-per-file
export class Group extends XmlComponent {
  constructor(text: string) {
    super('group');
    if (text) {
      this.root.push(text);
    }
  }
}

// tslint:disable-next-line: max-classes-per-file
export class SignType extends XmlComponent {
  constructor(text: string) {
    super('signType');
    if (text) {
      this.root.push(text);
    }
  }
}

// tslint:disable-next-line: max-classes-per-file
export class AlwaysShow extends XmlComponent {
  constructor(text: string) {
    super('alwaysShow');
    if (text) {
      this.root.push(text);
    }
  }
}

// tslint:disable-next-line: max-classes-per-file
export class ShowSignBorder extends XmlComponent {
  constructor(text: string) {
    super('showSignBorder');
    if (text) {
      this.root.push(text);
    }
  }
}
