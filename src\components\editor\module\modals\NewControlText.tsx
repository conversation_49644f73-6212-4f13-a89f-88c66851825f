import * as React from 'react';
import Dialog from '../../ui/Dialog';
import { INewControlProperty, NewControlType, NewControlContentSecretType,
    NewControlDefaultSetting, CustomPropertyElementType, isValidName, EventType,
    AlignType} from '../../../../common/commonDefines';
import Input from '../../ui/Input';
import Checkbox from '../../ui/CheckboxItem';
import Button from '../../ui/Button';
import Select from '../../ui/select/Select';
import CustomProperty from './CustomProperty';
import {message} from '../../../../common/Message';
import { IFRAME_MANAGER } from '../../../../common/IframeManager';
import CascadeBtn from './NewCascade';
import EventBtn from './NewCascadeEvent';
import { ExternalDataBind } from './ExternalDataBind';
import StructRegExpBtn from './StructRegExpSet';

interface IDialogProps {
    documentCore: any;
    visible: boolean;
    id: string;
    property?: INewControlProperty;
    close: (name: string | number, bRefresh?: boolean) => void;
    type?: number;
}

interface IState {
    bRefresh: boolean;
    isVisible: boolean;
}

const newControlLengthUnit = ['厘米', '字符'];
const newControlLengthType = ['newControlFixedLength', 'newControlMaxLength'];

export default class NewControlText extends React.Component<IDialogProps, IState> {
    private newControl: INewControlProperty;
    // private displayType: boolean;
    // private _currentName: string;
    // private refContainer: any;
    private playTypes: any[];
    private bCustomProperty: boolean;
    private visible: any;
    private types: { key: string; value: NewControlType; }[];
    private bCascade: boolean;
    private displayLengthType: any[];
    private newControlLenType: number;
    private newControlLenUnitType: string;
    private newControlLenPropType: string;
    private newControlLength: number;
    private title: string;
    private disabledJumpKey: boolean;
    private alignments: any[];
	private bCascadeEvent: boolean;
    private resetSourceBind: boolean;
    private dataBind: any;
    private bStructRegExpBtn: boolean;
    constructor(props: any) {
        super(props);
        this.state = {
            bRefresh: false,
            isVisible: props.visible,
        };
        this.visible = this.props.visible;
        this.newControl = {newControlName: undefined};

        this.playTypes = [
            {key: '不保密', value: NewControlContentSecretType.DontSecret},
            {key: '全部保密', value: NewControlContentSecretType.AllSecret},
            {key: '部分保密', value: NewControlContentSecretType.PartSecret},
        ];

        this.types = [
            {key: '简单元素', value: NewControlType.TextBox},
            {key: '节', value: NewControlType.Section},
        ];
        this.displayLengthType = [
            {key: '固定长度', value: 0},
            {key: '最大长度', value: 1},
        ];
        this.newControlLenType = 0;
        this.newControlLenUnitType = newControlLengthUnit[0];
        this.newControlLenPropType = newControlLengthType[0];
        this.alignments = [
            {
                key: '默认',
                value: AlignType.Left,
            },
            // {
            //     key: '右对齐',
            //     value: AlignType.Right,
            // },
            {
                key: '居中对齐',
                value: AlignType.Center,
            },
        ];
    }

    public render(): any {
        return (
            <Dialog
                visible={this.visible}
                width={350}
                top='15%'
                open={this.open}
                title={this.title}
                footer={this.renderFooter()}
                id={this.props.id}
            >
                <div>
                    <div className='editor-line'>
                        <span className='title'>常规</span>
                    </div>
                    <div className='editor-line'>
                        <div className='w-70'>名称</div>
                        <div className='right-auto'>
                            <Input
                                name='newControlName'
                                value={this.newControl.newControlName}
                                onChange={this.onChange}
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <div className='w-70'>内部名称</div>
                        <div className='right-auto'>
                            <Input
                                name='newControlSerialNumber'
                                value={this.newControl.newControlSerialNumber}
                                onChange={this.onChange}
                                readonly={true}
                                placeholder={''}
                                disabled={true}
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <div className='w-70'>标识符</div>
                        <div className='right-auto'>
                            <Input
                                name='identifier'
                                value={this.newControl.identifier}
                                onChange={this.onChange}
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <div className='w-70'>提示符</div>
                        <div className='right-auto'>
                            <Input
                                name='newControlInfo'
                                value={this.newControl.newControlInfo}
                                onChange={this.onChange}
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <div className='w-70'>占位符</div>
                        <div className='right-auto'>
                            <Input
                                name='newControlPlaceHolder'
                                value={this.newControl.newControlPlaceHolder}
                                onChange={this.onChange}
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <div className='w-70'>标题</div>
                        <div className='right-auto'>
                            <Input
                                name='newControlTitle'
                                value={this.newControl.newControlTitle}
                                onChange={this.onChange}
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <div className='w-70'>类型</div>
                        <div className='right-auto'>
                            <Select
                                data={this.types}
                                value={this.newControl.newControlType}
                                name='newControlType'
                                disabled={true}
                                onChange={this.typeChange}
                            />
                        </div>
                    </div>
                    <div className='editor-line editor-multi-line'>
                        <span className='title w-70'>属性</span>
                        <div className='right-auto newcontrol-prop'>
                            <div className='editor-line'>
                                <div className='w-050'>
                                    <Checkbox
                                        name='isNewControlHidden'
                                        disabled={false}
                                        value={this.newControl.isNewControlHidden}
                                        onChange={this.onChange}
                                    >
                                        隐藏
                                    </Checkbox>
                                </div>
                                <div className='w-050'>
                                    <Checkbox
                                        name='isNewControlCanntDelete'
                                        value={this.newControl.isNewControlCanntDelete}
                                        onChange={this.onChange}
                                    >
                                        禁止删除
                                    </Checkbox>
                                </div>
                            </div>

                            <div className='editor-line'>
                                <div className='w-050'>
                                    <Checkbox
                                        name='isNewControlCanntEdit'
                                        value={this.newControl.isNewControlCanntEdit}
                                        onChange={this.onChange}
                                    >
                                        禁止编辑
                                    </Checkbox>
                                </div>
                                <div className='w-050'>
                                    <Checkbox
                                        name='isNewControlMustInput'
                                        value={this.newControl.isNewControlMustInput}
                                        onChange={this.onChange}
                                    >
                                        必填项
                                    </Checkbox>
                                </div>
                            </div>

                            <div className='editor-line'>
                                <div className='w-050'>
                                    <Checkbox
                                        name='isNewControlShowBorder'
                                        value={this.newControl.isNewControlShowBorder}
                                        onChange={this.onChange}
                                    >
                                        显示边框
                                    </Checkbox>
                                </div>

                                <div className='w-050'>
                                    <Checkbox
                                        name='isNewControlReverseEdit'
                                        value={this.newControl.isNewControlReverseEdit}
                                        onChange={this.onChange}
                                    >
                                        反向编辑
                                    </Checkbox>
                                </div>
                            </div>

                            <div className='editor-line'>
                                <div className='w-050'>
                                    <Checkbox
                                        name='isNewControlHiddenBackground'
                                        value={this.newControl.isNewControlHiddenBackground}
                                        onChange={this.onChange}
                                    >
                                        隐藏背景色
                                    </Checkbox>
                                </div>
                                <div className='w-050'>
                                    <Checkbox
                                        name='tabJump'
                                        disabled={this.newControl.newControlType === NewControlType.Section}
                                        value={this.newControl.tabJump}
                                        onChange={this.onChange}
                                    >
                                        键盘跳转
                                    </Checkbox>
                                </div>
                            </div>
                            {this.renderTextBorder()}
                            {this.renderPrintHidden()}
                            <div className='editor-line'>
                                <CustomProperty
                                    name='customProperty'
                                    properties={this.newControl.customProperty}
                                    documentCore={this.props.documentCore}
                                    onChange={this.onChange}
                                    close={this.onClose}
                                    id='bCustomProperty'
                                    visible={this.bCustomProperty}
                                    type={CustomPropertyElementType.NewControl}
                                />
                            </div>
                        </div>
                    </div>
                    {this.renderExternal()}
                    <StructRegExpBtn
                        id='regExp'
                        onChange={this.onChange}
                        property={this.newControl.regExp}
                        visible={true}
                        close={this.onClose}
                        documentCore={this.props.documentCore}
                    />
                </div>
            </Dialog>
        );
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        this.visible = nextProps.visible;
    }
    // public componentDidUpdate(prevProps: IDialogProps): void {
    //     if (prevProps.visible !== this.props.visible) {
    //         this.setState({ isVisible: this.props.visible });
    //     }
    // }

    private renderCell = (): string => {
        return this.newControlLenUnitType;
    }

    private renderTextBorder = (): any => {
        if (this.newControl.newControlType !== NewControlType.TextBox) {
            return null;
        }

        return (
        <div className='editor-line'>
            <div className='w-050'>
                <Checkbox
                    name='bTextBorder'
                    value={this.newControl.bTextBorder}
                    onChange={this.onChange}
                >
                    带框字符
                </Checkbox>
            </div>
            <div className='w-050'>
                <Checkbox
                    name='hideHasTitle'
                    value={this.newControl.hideHasTitle}
                    onChange={this.onChange}
                >
                    仅有标题隐藏
                </Checkbox>
            </div>
        </div>
        );
    }

    private renderPrintHidden(): any {
        if (
            this.newControl.newControlType !== NewControlType.TextBox &&
            this.newControl.newControlType !== NewControlType.Section
        ) {
            return null;
        }

        return (
            <div className='editor-line'>
                <div className='w-050'>
                    <Checkbox
                        name='printSelected'
                        value={this.newControl.printSelected}
                        onChange={this.onChange}
                    >
                        打印时隐藏
                    </Checkbox>
                </div>
            </div>
        );
    }

    private renderExternal(): any {
        if (this.newControl.newControlType !== NewControlType.TextBox) {
            return (<React.Fragment>
                        <ExternalDataBind
                            name={this.newControl.newControlName}
                            id='externalDataBind'
                            visible={this.visible}
                            documentCore={this.props.documentCore}
                            onChange={this.onChange}
                            close={this.onClose}
                            properties={this.dataBind}
                            resetId={'resetSourceBind'}
                        />
                    </React.Fragment>);
        }
        return (
            <React.Fragment>
                <div className='editor-line'>
                    <span className='title'>扩展属性</span>
                </div>
                <div className='editor-line'>
                    <div className='w-70'>
                        隐私显示
                    </div>
                    <div className='right-auto'>
                        <Select
                            data={this.playTypes}
                            value={this.newControl.newControlDisplayType}
                            name='newControlDisplayType'
                            onChange={this.onChange}
                        />
                    </div>
                </div>
                <div className='editor-line'>
                    <div className='w-70'>
                        级联
                    </div>
                    <div className='right-auto'>
                        <CascadeBtn
                            visible={this.bCascade}
                            id='bCascade'
                            controlName={this.newControl.newControlName}
                            documentCore={this.props.documentCore}
                            properties={this.newControl.cascade}
                            name='cascade'
                            close={this.onClose}
                            onChange={this.onChange}
                            type={this.newControl.newControlType}
                        />
                    </div>
                </div>
                <div className='editor-line'>
                    <div className='w-70'>
                        求和级联
                    </div>
                    <div className='right-auto'>
                        <EventBtn
                            visible={this.bCascadeEvent}
                            id='bCascadeEvent'
                            controlName={this.newControl.newControlName}
                            documentCore={this.props.documentCore}
                            properties={this.newControl.eventInfo}
                            name='eventInfo'
                            close={this.onClose}
                            onChange={this.onChange}
                            type={EventType.Sum}
                        />
                    </div>
                </div>
                <div className='editor-line'>
                    <div className='w-70'>
                        长度限制
                    </div>
                    <div className='w-90'>
                        <Select
                            data={this.displayLengthType}
                            value={this.newControlLenType}
                            name='displayLengthType'
                            onChange={this.onChangeDisLength}
                        />
                    </div>
                    <div className='w-40'>
                        <Input
                            type={'number'}
                            name={this.newControlLenUnitType}
                            value={this.newControlLength}
                            onChange={this.onChangeLength}
                            renderAppend={this.renderCell}
                        />
                    </div>
                </div>
                <div className='editor-line'>
                    <div className='w-70'>
                        内部对齐
                    </div>
                    <div className='right-auto'>
                        <Select
                            name='alignments'
                            onChange={this.onChange}
                            data={this.alignments}
                            value={this.newControl.alignments}
                        />
                    </div>
                </div>
                <ExternalDataBind
                    name={this.newControl.newControlName}
                    id='externalDataBind'
                    visible={this.visible}
                    documentCore={this.props.documentCore}
                    onChange={this.onChange}
                    close={this.onClose}
                    properties={this.dataBind}
                    resetId={'resetSourceBind'}
                />
            </React.Fragment>
        );
    }

    private renderFooter(): any {
        return (
            <span>
                <Button onClick={this.close}>取消</Button>
                <Button type='primary' onClick={this.confirm}>确认</Button>
            </span>
        );
    }

    private onClose = (id: string, bRefresh: boolean): void => {
        this[id] = false;
        if ((id === 'bCustomProperty' || id === 'bCascade' || id === 'bCascadeEvent') && bRefresh) {
            this.setState({bRefresh: !this.state.bRefresh});
        } else if ('externalDataBind' === id && bRefresh) {
            this.resetSourceBind = false;
            this.setState({bRefresh: !this.state.bRefresh});
        }
    }

    private open = (): void => {
        const props = this.props.property;
        const newControl: INewControlProperty = this.newControl;
        this.disabledJumpKey = false;
        if (props === undefined) {
            this.init();
            newControl.newControlType = this.props.type;
            newControl.newControlName = this.props.documentCore.makeUniqueNewControlName(this.props.type);
        } else {
            const keys = Object.keys(props);
            keys.push('cascade');
            keys.push('eventInfo');
            keys.forEach((key) => {
                const val = props[key];
                newControl[key] = val;
            });

            if (newControl.alignments === undefined) {
                newControl.alignments = AlignType.Left;
            }

            if (newControl.newControlType === NewControlType.Section) {
                this.disabledJumpKey = true;
            }

            if ( null != newControl.newControlFixedLength &&
                !(!isNaN(newControl.newControlMaxLength) && 0 < newControl.newControlMaxLength) ) {
                this.newControlLenType = 0;
                this.newControlLenPropType = newControlLengthType[0];
                this.newControlLenUnitType = newControlLengthUnit[0];
                const fixedLen = newControl.newControlFixedLength;
                this.newControlLength = fixedLen === 0 ? undefined : fixedLen;
            } else if ( null != newControl.newControlMaxLength ) {
                this.newControlLenType = 1;
                this.newControlLenPropType = newControlLengthType[1];
                this.newControlLenUnitType = newControlLengthUnit[1];
                const maxLen = newControl.newControlMaxLength;
                this.newControlLength = maxLen === 0 ? undefined : maxLen;
            } else {
                this.newControlLenType = 0;
                this.newControlLenPropType = newControlLengthType[0];
                this.newControlLenUnitType = newControlLengthUnit[0];
                this.newControlLength = undefined;
            }
        }
        this.title = newControl.newControlType === NewControlType.Section ? '节' : '文本框';
        this.dataBind = newControl.externalDataBind;
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private close = (bRefresh?: boolean): void => {
        this.props.close(this.props.id, bRefresh);
        this.visible = false;
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private confirm = (): void => {
        // let flag = this.unitBlur();
        // if (flag === false) {
        //     return;
        // }
        // flag = this.blurValidNum();
        // if (flag === false) {
        //     return;
        // }

        // this._currentName = null;
        const documentCore = this.props.documentCore;
        const props = this.props.property;
        const newControl = this.newControl;
        IFRAME_MANAGER.setDocId(documentCore.getCurrentId());
        if (!isValidName(newControl.newControlName)) {
            message.error('名称不符合规范，请重新输入');
            return;
        }
        if (newControl.identifier && !isValidName(newControl.identifier)) {
            message.error('标识符不符合规范，请重新输入');
            return;
        }
        if ((!props || props.newControlName !== newControl.newControlName)
            && !documentCore.checkNewControlName(newControl.newControlName)) {
            message.error('已存在该名字，请重新命名');
            return;
        }

        if ( 0 > newControl.newControlFixedLength ) {
            message.error('固定长度不能小于0！');
            return;
        }

        if ( 0 > newControl.newControlMaxLength ) {
            message.error('最大长度不能小于0！');
            return;
        }

        if ( 'number' !== typeof newControl.newControlFixedLength ) {
            newControl.newControlFixedLength = undefined;
        }

        if ( 'number' !== typeof newControl.newControlMaxLength ) {
            newControl.newControlMaxLength = undefined;
        } else if ( 0 < newControl.newControlMaxLength ) {
            newControl.newControlMaxLength = Math.round(newControl.newControlMaxLength);
        }

        // if ( null != newControl.newControlMaxLength && 1 > parseInt(newControl.newControlMaxLength.toString(), 0) ) {
        //     message.error('最大长度不能小于1！');
        //     return;
        // }

        if (this.resetSourceBind) {
            this.newControl.externalDataBind = {
                sourceObj: '',
                sourceKey: '',
                bReadOnly: false,
                commandUpdate: 1,
            }

            this.resetSourceBind = false;
        } else if (this.dataBind) {
            this.newControl.externalDataBind = this.dataBind;
            this.dataBind = undefined;
        }

        if (props === undefined) {
            this.handleLengthCornerCase();
            documentCore.addNewControl(this.newControl);
        } else {
            this.handleLengthCornerCase();
            documentCore.setNewControlProperty(this.newControl, props.newControlName);
        }

        this.close(true);
    }

    private init(): void {
        const newControl = this.newControl;
        newControl.newControlName = undefined;
        newControl.newControlSerialNumber = undefined;
        newControl.newControlInfo = undefined;
        newControl.newControlPlaceHolder = NewControlDefaultSetting.DefaultPlaceHolderContent;
        newControl.newControlTitle = undefined;
        newControl.newControlType = NewControlType.TextBox;
        newControl.isNewControlHidden = false;
        newControl.isNewControlCanntDelete = false;
        newControl.isNewControlCanntEdit = false;
        newControl.isNewControlMustInput = false;
        newControl.isNewControlShowBorder = true;
        newControl.isNewControlReverseEdit = false;
        newControl.isNewControlHiddenBackground = true;
        newControl.newControlDisplayType = NewControlContentSecretType.DontSecret;
        newControl.newControlMaxLength = undefined;
        newControl.newControlFixedLength = undefined;
        newControl.customProperty = undefined;
        newControl.tabJump = this.props.type !== NewControlType.Section;
        newControl.cascade = undefined;
        newControl.bTextBorder = undefined;
        newControl.hideHasTitle = false;
        newControl.identifier = undefined;
        newControl.alignments = AlignType.Left;
        newControl.eventInfo = undefined;
        newControl.externalDataBind = undefined;
        newControl.regExp = undefined;
        newControl.printSelected = false;
        this.resetSourceBind = false;
        this.newControlLength = undefined;
    }

    private handleLengthCornerCase(): void {
        if (this.newControl.newControlFixedLength == null) {
            this.newControl.newControlFixedLength = 0;
        }
        if (this.newControl.newControlMaxLength == null) {
            this.newControl.newControlMaxLength = 0;
        }
    }

    private onChange = (value: any, name: string): void => {
        if ('resetSourceBind' !== name) {
            if ('externalDataBind' !== name) {
                this.newControl[name] = value;
            } else {
                this.dataBind = value;
            }
        } else {
            this.resetSourceBind = true;
        }
    }

    private onChangeDisLength = (value: any): void => {
        switch (value) {
            case 0:
                this.newControlLenType = 0;
                this.newControlLenPropType = newControlLengthType[0];
                this.newControlLenUnitType = newControlLengthUnit[0];
                this.newControlLength = undefined;
                this.newControl.newControlFixedLength = undefined;
                this.newControl.newControlMaxLength = undefined;
                break;
            case 1:
                this.newControlLenType = 1;
                this.newControlLenPropType = newControlLengthType[1];
                this.newControlLenUnitType = newControlLengthUnit[1];
                this.newControlLength = undefined;
                this.newControl.newControlFixedLength = undefined;
                this.newControl.newControlMaxLength = undefined;
                break;
            default:
                break;
        }
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private onChangeLength = (value: any): void => {
        switch (this.newControlLenPropType) {
            case newControlLengthType[0]:
                // this.newControlLength = value;
                this.newControl.newControlFixedLength = value;
                this.newControl.newControlMaxLength = undefined;
                break;
            case newControlLengthType[1]:
                this.newControl.newControlFixedLength = undefined;
                this.newControl.newControlMaxLength = value;
                break;
            default:
                this.newControl.newControlFixedLength = undefined;
                this.newControl.newControlMaxLength = undefined;
                break;
        }
    }

    private typeChange = (value: any, name: string): void => {
        this.newControl[name] = value;
        if ( value === NewControlType.TextBox || value === NewControlType.Section ) {
            this.newControl.newControlName = this.props.documentCore.makeUniqueNewControlName(value);
            if (value === NewControlType.Section) {
                this.disabledJumpKey = true;
                this.newControl.tabJump = false;
            } else {
                this.disabledJumpKey = false;
                this.newControl.tabJump = true;
            }
        }

        this.setState({bRefresh: !this.state.bRefresh});
    }
}
