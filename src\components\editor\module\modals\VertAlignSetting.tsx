import * as React from 'react';
import Dialog from '../../ui/Dialog';
import Button from '../../ui/Button';
import Radio from '../../ui/Radio';
import { MenuItemIndex } from '@/common/commonDefines';

interface IDialogProps {
    documentCore: any;
    visible: boolean;
    id: string;
    type: number;
    close: (name: string | number, bRefresh?: boolean) => void;
}

interface IState {
    bRefresh: boolean;
}

export default class VertAlignSetting extends React.Component<IDialogProps, IState> {
    private visible: boolean;
    // private value: number;
    private vertAlignTypeDatas: any[];
    private vertAlignValue: number; // for radio button
    private bTableCell: boolean;
    private type: number;

    constructor(props: any) {
        super(props);
        this.state = {
            bRefresh: false,
        };
        this.visible = this.props.visible;
        this.vertAlignTypeDatas = [
            {key: '顶部', value: 0},
            {key: '居中', value: 1},
            {key: '底部', value: 2},
        ];
        this.type = this.props.type;
        this.open();
    }

    public render(): any {
        const title = (this.bTableCell ? '单元格内容' : '对齐方式');
                        // (MenuItemIndex.FormulaAlign !== this.type) ? '图片对齐方式' : '公式对齐方式');
        const span = (this.bTableCell ? '文本对齐方式' : '');
        return (
            <Dialog
                id={this.props.id}
                preventDefault={true}
                visible={this.visible}
                footer={this.renderFooter()}
                width={250}
                title={title}
            >
                <div>
                    <div className='editor-line'>
                        <span>{span}</span>
                    </div>
                    <div className='editor-line'>
                        <Radio
                            value={this.vertAlignValue}
                            data={this.vertAlignTypeDatas}
                            name='cellVertAlign'
                            onChange={this.onChange}
                        />
                    </div>
                </div>
            </Dialog>
        );
    }

    public componentDidMount(): void {
        this.setState({bRefresh: !this.state.bRefresh});
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        this.visible = nextProps.visible;
        if ( this.visible ) {
            this.type = nextProps.type;
            this.open();
        }
    }

    private renderFooter(): any {
        return (
            <span>
                <Button onClick={this.close}>取消</Button>
                <Button type='primary' onClick={this.confirm}>确认</Button>
            </span>
        );
    }

    private open = (): void => {
        const { documentCore } = this.props;
        this.bTableCell = (MenuItemIndex.TableCellVertAlign === this.type);
        this.vertAlignValue = this.bTableCell ? documentCore.getTableCellsVertAlign() :
                                    documentCore.getSelectedImage()?.
                                                    getVertAlign();
    }

    private onChange = (value: any, name: string): void => {
        this.vertAlignValue = value;
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private close = (bRefresh?: boolean): void => {
        this.vertAlignValue = null;
        if (!this.bTableCell) {
            bRefresh = true;
        }
        this.props.close(this.props.id, bRefresh);
        this.visible = false;
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private confirm = (): void => {
        if (this.bTableCell) {
            this.props.documentCore.setTableCellsVertAlign(this.vertAlignValue);
        } else {
            this.props.documentCore.setImageVertAlign(this.vertAlignValue);
        }

        this.close(true);
    }

}