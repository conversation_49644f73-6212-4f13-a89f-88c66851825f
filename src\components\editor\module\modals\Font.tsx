import * as React from 'react';
import Dialog from '../../ui/Dialog';
import '../../style/font.less';
import Checkbox from '../../ui/CheckboxItem';
import Button from '../../ui/Button';
import SelectList from '../../select/list';
import { fonts } from '../../text/font';

interface IDialogProps {
    documentCore: any;
    visible: boolean;
    id: string;
    close: (name: string | number, bRefresh?: boolean) => void;
}

interface IState {
    bRefresh: boolean;
}

interface IFontProperty {
    fontSize: number;
    fontFamily: string;
    fontStyle: number;
    fontWeight: number;
    subscript: boolean;
    superscript: boolean;
    textDecorationLine: boolean;
}

export default class Font extends React.Component<
    IDialogProps,
    IState
> {
    // 存储结果属性
    private font: IFontProperty;
    // 这里用来展示
    private fontFormat: { font: object; textDecoration: object };
    private visible: boolean;
    constructor(props: any) {
        super(props);
        this.state = {
            bRefresh: false,
        };

        this.font = {
            fontSize: undefined,
            fontFamily: undefined,
            fontStyle: undefined,
            subscript: undefined,
            fontWeight: undefined,
            superscript: undefined,
            textDecorationLine: undefined,
        };
        this.fontFormat = { font: {}, textDecoration: {} };
        this.visible = this.props.visible;
    }

    public render(): any {
        return (
            <Dialog
                visible={this.visible}
                width={400}
                open={this.open}
                title='设置字体'
                footer={this.renderFooter()}
                id={this.props.id}
            >
                <div className='editor-font'>
                    <div className={'font-top'}>
                        <ul>
                            <li>
                                <div className='title'>字体</div>
                                <SelectList
                                    height={150}
                                    data={fonts[0].options}
                                    props={{ name: 'name', value: 'value' }}
                                    value={this.font.fontFamily}
                                    change={this.fontFamilyChang}
                                />
                            </li>
                            <li>
                                <div className='title'>字形</div>
                                <SelectList
                                    height={150}
                                    data={fonts[2].options}
                                    props={{ name: 'name', value: 'value' }}
                                    value={this.font.fontStyle}
                                    change={this.fontStyleChange}
                                />
                            </li>
                            <li>
                                <div className='title'>大小</div>
                                <SelectList
                                    data={fonts[1].options}
                                    height={150}
                                    props={{ name: 'name', value: 'value' }}
                                    value={this.font.fontSize}
                                    change={this.fontSizeChange}
                                />
                            </li>
                        </ul>
                    </div>

                    <div className='editor-line'>
                        <span className='title'>效果</span>
                    </div>
                    <div className='editor-line'>
                        <Checkbox
                            value={this.font.textDecorationLine}
                            onChange={this.onChange}
                            name='textDecorationLine'
                        >
                            下划线
                        </Checkbox>

                        <Checkbox
                            value={this.font.superscript}
                            onChange={this.onChange}
                            name='superscript'
                        >
                            上标
                        </Checkbox>

                        <Checkbox
                            value={this.font.subscript}
                            onChange={this.onChange}
                            name='subscript'
                        >
                            下标
                        </Checkbox>
                    </div>

                    <div className='font-footer'>
                        <span className='title'>预览</span>
                        <div>
                            <p style={this.fontFormat.font}>
                                <span style={this.fontFormat.textDecoration}>智能编辑器</span>
                            </p>
                        </div>
                    </div>
                </div>
            </Dialog>
        );
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        this.visible = nextProps.visible;
    }

    private renderFooter(): any {
        return (
            <span>
                <Button onClick={this.close}>取消</Button>
                <Button type='primary' onClick={this.confirm}>
                    确认
                </Button>
            </span>
        );
    }

    private close = (bRefresh?: boolean): void => {
        this.visible = false;
        this.setState({bRefresh: !this.state.bRefresh});
        this.props.close(this.props.id, bRefresh);
        this.clearFont();
    }

    private open = (): void => {
        const documentCore = this.props.documentCore;
        const attr = documentCore.getSelectedTextPro();
        const font = this.font;
        const style: any = this.font;
        style.textDecorationLine = attr.textDecorationLine === 1;
        style.subscript = attr.vertAlign === 1;
        style.superscript = attr.vertAlign === 2;
        font.fontFamily = attr.fontFamily;
        font.fontStyle = undefined;
        if (attr.fontStyle && attr.fontWeight) {
            font.fontStyle = 3;
        } else if (attr.fontStyle) {
            font.fontStyle = 1;
        } else if (attr.fontWeight) {
            font.fontStyle = 2;
        } else if (attr.fontStyle === 0 && attr.fontWeight === 0) {
            font.fontStyle = 0;
        }
        if (
            (style.superscript && style.textDecorationLine) ||
            (style.subscript && style.textDecorationLine)
        ) {
            style.superscript = false;
            style.textDecorationLine = false;
            style.subscript = false;
        }
        font.fontSize = attr.fontSize;
        this.setState({ bRefresh: !this.state.bRefresh });
    }

    private confirm = (id?: number | string): void => {
        const font = this.getFont();
        const documentCore = this.props.documentCore;
        documentCore.setTextProperty(font);
        this.close(true);
    }

    private clearFont(): void {
        this.fontFormat = {
            font: {},
            textDecoration: {},
        };

        this.font = {
            fontSize: null,
            fontFamily: null,
            fontStyle: null,
            subscript: null,
            fontWeight: null,
            superscript: null,
            textDecorationLine: null,
        };
    }

    private getFont(): any {
        const font = this.font;
        let fontStyle: number = font.fontStyle;
        let fontWeight: number;
        switch (fontStyle) {
            case 0:
                fontWeight = 0;
                fontStyle = 0;
                break;
            case 1:
                fontStyle = 1;
                break;
            case 2:
                fontWeight = 1;
                fontStyle = 0;
                break;
            case 3:
                fontWeight = 1;
                fontStyle = 1;
                break;
            default:
                fontWeight = undefined;
                fontStyle = undefined;
        }

        font.fontStyle = fontStyle;
        font.fontWeight = fontWeight;

        return font;
    }

    private setPreviewFont(key?: string): void {
        const font = this.font;
        const style = {};
        const textDecorationStyle = {};
        this.fontFormat = {
            font: style,
            textDecoration: textDecorationStyle,
        };
        let flag: boolean = false;
        if (key !== undefined) {
            switch (key) {
                case 'superscript':
                    font.subscript = false;
                    break;
                case 'subscript':
                    font.superscript = false;
                    break;
                default:
                    flag = true;
            }
        }

        if (flag === true && font.superscript) {
            font.subscript = false;
            flag = false;
        }

        if (font.fontSize !== undefined) {
            style['fontSize'] = font.fontSize;
            flag = true;
        }
        if (font.fontFamily !== undefined) {
            style['fontFamily'] = font.fontFamily;
            flag = true;
        }

        if (font.subscript === true) {
            style['transform'] = 'translate(0, 5px) scale(.5)';
            flag = true;
        }
        if (font.superscript === true) {
            style['transform'] = 'translate(0, -5px) scale(.5)';
            flag = true;
        }
        if (font.textDecorationLine === true) {
            textDecorationStyle['borderBottom'] = '1px solid #000';
            flag = true;
        }

        if (font.fontStyle !== undefined) {
            switch (font.fontStyle) {
                case 1:
                    style['fontStyle'] = 'italic';
                    flag = true;
                    break;
                case 2:
                    style['fontWeight'] = 'bold';
                    flag = true;
                    break;
                case 3:
                    style['fontStyle'] = 'italic';
                    style['fontWeight'] = 'bold';
                    flag = true;
                    break;
                default:
            }
        }
        if (flag === true) {
            this.setState({ bRefresh: !this.state.bRefresh });
        }
    }

    private onChange = (value: boolean, name: string): void => {
        // const value = e.target.checked;
        this.font[name] = value;
        this.setPreviewFont(name);
    }

    private fontFamilyChang = (item: any): void => {
        this.font.fontFamily = item.value;
        this.setPreviewFont();
        // this.fontFormat.fontFamily = item.value;
    }

    private fontStyleChange = (item: any): void => {
        this.font.fontStyle = item.value;
        this.setPreviewFont();
        // this.fontFormat.fontStyle = item.value;
    }

    private fontSizeChange = (item: any): void => {
        this.font.fontSize = item.value;
        this.setPreviewFont();
        // this.fontFormat.fontStyle = this.font.fontSize;
    }
}
