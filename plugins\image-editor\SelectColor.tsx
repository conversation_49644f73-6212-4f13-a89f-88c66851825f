import * as React from 'react';
import { Sketch } from '@uiw/react-color';

// 扩展更多常用颜色
const colors = [
    // 红色系
    '#c00000', '#ff0000', '#ff6666', '#ffcccc',
    // 橙色系
    '#ffa500', '#ff8c00', '#ffd700',
    // 黄色系
    '#ffff00', '#fffacd', '#f0e68c',
    // 绿色系
    '#008000', '#00ff00', '#98fb98', '#00fa9a',
    // 蓝色系
    '#0000ff', '#1e90ff', '#87ceeb', '#00bfff',
    // 紫色系
    '#7030a0', '#800080', '#ba55d3', '#9370db',
    // 黑白灰
    '#000000', '#333333', '#666666', '#999999', '#cccccc', '#ffffff'
];

interface IProps {
    color: string;
    onChangeComplete?: (obj: any) => void;
    onChange?: (obj: any) => void;
}
interface IState {
    color: string;
}

export class SelectColor extends React.Component<IProps, IState> {
    private _ref: any;
    private _options: any;
    private _color: string;
    private _oldColor: string;
    private _visible: boolean;
    private _loadEvent: boolean;
    constructor(props: IProps) {
        super(props);
        this.state = {
            color: this.props.color,
        };
        this._ref = React.createRef();
    }

    public render(): any {
        let node;
        if (this._visible) {
            node = (
                <Sketch 
                    style={{ marginLeft: 20, width: '230px' }}
                    color={this.state.color}
                    presetColors={colors}
                    disableAlpha={true}
                    onChange={(color) => {
                        this._options = { hex: color.hex };
                        this.setState({ color: color.hex });
                        this.emitParent();
                    }}
                />
            );
        }
        return (
            <div className='select-color' ref={this._ref}>
                {node}
                <style>
                {`
                    /* 隐藏HEX、R、G、B输入框和文字 */
                    .w-color-sketch-fields,
                    .sketch-picker input,
                    .w-color-sketch input,
                    .w-color-sketch-field,
                    .w-color-sketch-field-label,
                    .w-color-sketch-field-wrap {
                        display: none !important;
                    }

                    /* 隐藏颜色信息头部区域 */
                    .w-color-sketch-wrap > div:not(.w-color-sketch-saturation):not(.w-color-sketch-hue):not(.w-color-sketch-alpha):not(.w-color-sketch-presets) {
                        display: none !important;
                    }

                    /* 直接隐藏标签文字 */
                    .w-color-sketch-label,
                    label[title="hex"],
                    label[title="r"],
                    label[title="g"],
                    label[title="b"],
                    div:has(> label[title="hex"]),
                    div:has(> label[title="r"]),
                    div:has(> label[title="g"]),
                    div:has(> label[title="b"]) {
                        display: none !important;
                    }

                    /* 隐藏整个颜色值区域 */
                    .w-color-sketch-swatch,
                    .sketch-picker .flexbox-fix {
                        display: none !important;
                    }

                    /* 提高饱和度和色相选择器的高度 */
                    .w-color-sketch-saturation {
                        height: 150px !important;
                    }

                    /* 调整预设颜色区域样式，让颜色块更大 */
                    .w-color-sketch-presets span,
                    .sketch-picker .w-color-sketch-presets span {
                        width: 20px !important;
                        height: 20px !important;
                        margin-right: 5px !important;
                        margin-bottom: 5px !important;
                    }
                    
                    /* 增加预设颜色区域高度，显示更多行 */
                    .w-color-sketch-presets,
                    .sketch-picker .w-color-sketch-presets {
                        height: auto !important;
                        max-height: 120px !important;
                        overflow-y: auto !important;
                        padding-top: 5px !important;
                    }
                `}
                </style>
            </div>
        );
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        if (this.state.color !== nextProps.color) {
            this._color = nextProps.color;
            this.setState({color: nextProps.color});
        }
    }
    // public componentDidUpdate(prevProps: any): void {
    //     // 检查当前 props 与之前的 props 是否不同
    //     if (this.state.color !== this.props.color) {
    //         this._color = this.props.color;
    //         this.setState({ color: this.props.color });
    //     }
    // }

    public componentDidMount(): void {
        this._ref.current?.addEventListener('click', this.onClick);
        document.addEventListener('click', this.docClick);
    }

    public componentWillUnmount(): void {
        this._ref.current?.removeEventListener('click', this.onClick);
        document.removeEventListener('click', this.docClick);
    }

    public setColorVisible(flag: boolean) {
        this._visible = flag;
        this.setState({});
    }

    private docClick = (): void => {
        if (!this._visible) {
            return;
        }
        this._visible = false;
        this.setState({});
    }

    private onClick = (e: any): void => {
        e.stopPropagation();
        const target = e.target;
        const className: string = target.className;
        if (className.search(/saturation-black|saturation-white/) > -1) {
            this.emitParent();
            return;
        } else {
            const attr = target.getAttribute('title');
            if (attr) {
                this._options = {hex: attr};
                this.emitParent();
                this.docClick();
                return;
            }
        }
    }

    private onChange = (options: any): void => {
        this._options = options;
        
        if (this.state.color !== options.hex) {
            this.setState({color: options.hex});
        }
    }

    private emitParent(): void {
        setTimeout(() => {
            const obj = this._options;
            if (this.props.onChange && obj && obj.hex !== this._color) {
                this.props.onChange(this._options);
            }
        }, 0);
    }
}
