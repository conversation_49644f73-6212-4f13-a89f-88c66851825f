import { Run } from '../run';
import { Text } from './run-components/text';
import { Tab } from './tab';
import { PageCount } from '../../footer/footer-page-count';
import { PageCurrent } from '../../footer/footer-page-current';
import { IDrawingAttributesProperties, Drawing, EditableDrawing, IBarcodeAllProperties, Barcode, QRCode } from '../../drawing';
import { IMedEquationAttributesProperties, MedEquation } from '../../drawing/medEquation/medEquation';
import { IPageNumAttributesProperties, PageNum } from './run-components/pageNum';
import { SoftNewPara } from './softNewPara';
import { BaseXmlComponent } from '../../xml-components/base';
import { addAttr, addNode } from '@/common/struct/write';

export class TextRun extends Run {
    constructor(text?: string) {
        super();
        if (text) {
            this.root.push(new Text(text));
        }
    }

    public getRoot(): (BaseXmlComponent | string)[] {
        return this.root;
    }

    /** Methods below will create elements with SAME LEVEL of w:t */

    public addText(text: string): void {
        this.root.push(new Text(text));
    }

    public tab(): void {
        this.root.push(new Tab());
    }

    public addParaButton(item: any): void {
        const {color, bPrint, content, name} = item;
        const node = addNode('w:button', content, this.root);
        const obj = {};
        if (color != null) {
          obj['color'] = color;
        } 
        if (bPrint === true) {
          obj['bPrint'] = bPrint;
        }
        if (name) {
          obj['name'] = name;
        }
        addAttr(obj, node);
    }

    public addPageDomain(currentPage: number, totalPages: number): void {
        if (currentPage && totalPages) {
            this.root.push(new PageCurrent(currentPage));
            this.root.push(new PageCount(totalPages));
        }
    }

    public addImage(attrs: IDrawingAttributesProperties): void {
        this.root.push(new Drawing(attrs));
    }

    public addEditableImage(attrs: IDrawingAttributesProperties): void {
        this.root.push(new EditableDrawing(attrs));
    }

    public addMedEquation(attrs: IMedEquationAttributesProperties, mathValue: string, mathText: string): void {
        const medEquation = new MedEquation(attrs);
        medEquation.addMathValue(mathValue);
        medEquation.addMathText(mathText);
        this.root.push(medEquation);
    }

    public addPageNum(props: IPageNumAttributesProperties, totalPages?: number, curPage?: number): void {
        this.root.push(new PageNum(props, totalPages, curPage));
    }

    public addSoftNewPara(): void {
        this.root.push(new SoftNewPara());
    }

    public addBarcode(attrs: IBarcodeAllProperties): void {
        this.root.push(new Barcode(attrs));
    }

    public addQRCode(attrs: IBarcodeAllProperties): void {
        this.root.push(new QRCode(attrs));
    }
}
