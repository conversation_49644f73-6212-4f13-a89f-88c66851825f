
.dialog-bigimage {
    width: 100%;
    height: 100%;
    overflow: auto;

    .hidden {
        display: none;
    }

    .big {
        cursor: grab;
    }

    .loading-container {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .loading-animation {
        /* 加载动画样式，这里使用了一个简单的旋转动画作为示例 */
        animation: spin 1s infinite linear;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #3498db;
        border-radius: 50%;
        width: 40px;
        height: 40px;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
}