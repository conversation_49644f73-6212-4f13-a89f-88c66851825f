// const urlencode = require('urlencode');
// const customTagMap = require('./customTagMap');
// const translateStyle = require('./translateStyle');
// var style = '';
// let cnReg = new RegExp('[\\u4E00-\\u9FFF]+','g');
// const encodeText = (text)=>{
//     text = text.replace(/&emsp;&emsp;/g, '');
//     text = text.replace(cnReg, (str) => {
//         let gbk = urlencode(str,'GBK');
//         return gbk.replace(/%/g,'\\\'');
//     });

//     return text;
// }

// let blockTags = ['div','p','h1','h2','h3','h4','h5','h6'],
//     markTags = ['b','i','u','sub'],
//     customTags = ['a','ol','ul','li','center','table','td','th','tr','sup'];
// const matchTag = (node)=>{
//     let tag = (node.tagName || '').toLowerCase();
//     let str = '';
//     if (tag === 'em') {
//         tag = 'b';
//     }
//     if (blockTags.includes(tag)) {
//         str = getTagRTF(node,'{\\pard ', '\\sb70\\par}')
//     } else if (markTags.includes(tag)) {
//         str = getTagRTF(node, `{\\${tag}`, '}');
//     } else if (customTags.includes(tag)) {
//         let custom = customTagMap[tag];
//         custom.init && custom.init(node);
//         str = getTagRTF(node, custom.start, custom.end);
//     } else {
//         str = getTagRTF(node)
//     }
//     return str;
// }
// const matchStyle = (node)=>{
//     if (!node.getAttribute) {
//         return;
//     }
//     let style = node.getAttribute('style');
//     if (!style) {
//         return;
//     }
//     let styleRTF = getStyleRTF(style);
//     return styleRTF;
// }
// const getTagRTF = (node, start, end) => {
//     let str = '';
//     start = start || '';
//     end = end || '';
//     let activeStyle = matchStyle(node);
//     style = activeStyle ? activeStyle : style;
//     if (!node.childNodes || node.childNodes.length === 0) {
//         if (node.textContent && node.textContent !== '&emsp;&emsp;') {
//             console.log(style)
//             str += (start || '{\\rtlch') + style;
//             let text = encodeText(node.textContent);
//             str += `${text}${end || '}'}`;
//         }
//     } else {
//         str += start;
//         let childNodeStr = mapNode(node.childNodes);
//         if (childNodeStr) {
//             str = str + childNodeStr + end;
//         } else {
//             // str = ''
//         }
//     }
//     return str;
// }
// const getStyleRTF = (style)=>{
//     let styleObj = parseStyle(style);
//     let str = '';
//     let fontSize = styleObj['font-size'],
//         lineHeight = styleObj['line-height'],
//         marginTop = styleObj['margin-top'],
//         textIndent = styleObj['text-indent'],
//         color = styleObj['color'];
//     str += fontSize ? translateStyle.getFontSizeRTF(fontSize) : '';
//     str += lineHeight ? translateStyle.getLineHeightRTF(lineHeight) : '';
//     str += marginTop ? translateStyle.getMarginTopRTF(marginTop) : '';
//     str += textIndent ? translateStyle.getTextIndentRTF(textIndent) : '';
//     str += color ? translateStyle.getColorRTF(color) : '';
//     return str;
// }
// const parseStyle = (style) => {
//     let styles = style.split(';');
//     let styleObj = {};
//     for (let stringStyle of styles) {
//         if (stringStyle) {
//             let splitStyle = stringStyle.split(':');
//             styleObj[splitStyle[0].trim()] = splitStyle[1].trim();
//         }
//     }
//     return styleObj;
// }
// const mapNode = (nodes) => {
//     let str = ''
//     for (let node of nodes) {
//         if (node.nodeType === 8) {
//             continue;
//         }
//         if (!node.tagName && !node.textContent) {
//             continue;
//         }
//         str += matchTag(node);
//     }
//     return str;
// }
// const transHtml = (html)=>{
//     let rtf_str = "{\\rtf1\\ansi\\ansicpg936\\cocoartf1504\\cocoasubrtf830{\\fonttbl{\\f0\\fbidi \\fnil \\fcharset134 }}{\\colortbl;\\red255\\green255\\blue255;\\red246\\green250\\blue211;}"
//     let div = document.createElement('div');
//     div.innerHTML = html.replace(/\r|\n|&#13;/g, '');
//     let result = mapNode(div.childNodes);
//     rtf_str = rtf_str + result + "\\par}";
//     return rtf_str;
// }

export default {
    parse: {}
};
