import * as React from 'react';
import Dialog from '../../ui/Dialog';
import Button from '../../ui/Button';
import Radio from '../../ui/Radio';
import '../../style/nistableCellSetting.less';
import { IFRAME_MANAGER } from '../../../../common/IframeManager';
import { DocumentCore } from '../../../../model/DocumentCore';

interface IProps {
    documentCore: any;
    visible: boolean;
    id: string;
    close: (name: string | number, bRefresh?: boolean) => void;
}

interface IState {
    bRefresh: boolean;
}

interface IPropNameKey {
    key: string;
    value: any;
    bDisabled?: boolean;
    disabled?: boolean;
}

export class NISTableCellSetting extends React.Component<IProps, IState> {
    private visible: boolean;
    private typeRange: any;
    private typeRangeValue: number;
    private cellType: IPropNameKey[];
    private cellTypeValue: number;
    // private cellVertAlignTypeDatas: any[];
    // private cellVertAlignValue: number;

    constructor(props: any) {
        super(props);
        this.state = {
            bRefresh: false,
        };
        this.typeRange = [
            {key: '单个单元格', value: 0},
            {key: '当前行', value: 1},
            {key: '当前列', value: 2},
        ];
        this.cellType = [
            {
                key: '快捷文本',
                value: 0,
            },
            {
                key: '普通文本',
                value: 1,
            },
            {
                key: '下拉选项',
                value: 2,
            },
            {
                key: '日期',
                value: 3,
            },
            {
                key: '时间',
                value: 4,
            },
            {
                key: '签名',
                value: 5,
            },
            {
                key: '数值',
                value: 6,
            },
            // {
            //     key: '血压',
            //     value: 7,
            // },
        ];

        this.visible = this.props.visible;

        this.typeRangeValue = 0;
        this.cellTypeValue = 0;
        // this.cellVertAlignValue = 0;
    }

    public render(): any {
        return (
            <Dialog
                visible={this.visible}
                width={360}
                open={this.open}
                title='单元格类型设置'
                footer={this.renderFooter()}
                id={this.props.id}
            >
                <div className='nistableCellSetting'>
                    <div className='editor-line'>
                        <span className='title'>类型设置范围（不包含表头）</span>
                    </div>
                    <div className='editor-line'>
                        <Radio value={this.typeRangeValue} data={this.typeRange} name='typeRangeValue' onChange={this.onChange}/>
                    </div>
                    <div className='editor-line'>
                        <span className='title'>单元格类型设置</span>
                    </div>
                    <div className='editor-line editor-multi-line nis-type'>
                        <Radio value={this.cellTypeValue} data={this.cellType} name='cellTypeValue' onChange={this.onChange}/>
                    </div>
                </div>
            </Dialog>
        );
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        this.visible = nextProps.visible;
    }

    private renderFooter(): any {
        return (
            <span>
                <Button onClick={this.close}>取消</Button>
                <Button type='primary' onClick={this.confirm}>
                    确认
                </Button>
            </span>
        );
    }

    private open = (): void => {
        this.typeRangeValue = 0;
        this.cellTypeValue = 0;
        // const typeCellProps: ITableCellProperty = this.props.documentCore.getTableCellProps();
        // if (typeCellProps != null) {
        //     this.cellTypeValue = typeCellProps.type;
        // }
        // this.cellVertAlignValue = 0; // this.props.documentCore.getTableCellsVertAlign();
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private onChange = (value: any, name: string): void => {
        this[name] = value;
    }

    private confirm = (id?: any): void => {
        const documentCore: DocumentCore = this.props.documentCore;
        IFRAME_MANAGER.setDocId(documentCore.getCurrentId());

        documentCore.setNISTableCellTypeProps(
            {
                range: this.typeRangeValue,
                type: this.cellTypeValue,
            }
        );

        this.close(true);
    }

    private close = (bRefresh?: boolean): void => {
        this.props.close(this.props.id, bRefresh);
        this.visible = false;
        this.setState({bRefresh: !this.state.bRefresh});
    }
}
