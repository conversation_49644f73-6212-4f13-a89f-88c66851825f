import { Helper } from './Helper';
import { Bitmap<PERSON><PERSON>Header, BitmapBase } from './BitmapBase';
import { Blob } from './Blob';

export class BitmapInfoHeader {
    public width: number;
    public height: number;
    public planes: number;
    public bitcount: number;
    public compression: number;
    public sizeimage: number;
    public xpelspermeter: number;
    public ypelspermeter: number;
    public clrused: number;
    public clrimportant: number;

    constructor(reader: Blob, skipsize: boolean) {
        if (skipsize) {
            reader.skip(4);
        }
        this.width = reader.readInt32();
        this.height = reader.readInt32();
        this.planes = reader.readUint16();
        this.bitcount = reader.readUint16();
        this.compression = reader.readUint32();
        this.sizeimage = reader.readUint32();
        this.xpelspermeter = reader.readInt32();
        this.ypelspermeter = reader.readInt32();
        this.clrused = reader.readUint32();
        this.clrimportant = reader.readUint32();
    }

    public colors(): number {
        if (this.clrused !== 0) {
            return this.clrused < 256 ? this.clrused : 256;
        } else {
            // tslint:disable-next-line: no-bitwise
            return this.bitcount > 8 ? 0 : 1 << this.bitcount;
        }
    }
}

export class BitmapInfo extends BitmapBase {
    // tslint:disable-next-line: variable-name
    private _reader: Blob;
    // tslint:disable-next-line: variable-name
    private _offset: number;
    // tslint:disable-next-line: variable-name
    private _usergb: boolean;
    // tslint:disable-next-line: variable-name
    private _infosize: number;
    // tslint:disable-next-line: variable-name
    private _header: BitmapCoreHeader | BitmapInfoHeader;

    constructor(reader: Blob, usergb: boolean) {
        super();
        this._reader = reader;
        this._offset = reader.pos;
        this._usergb = usergb;
        const hdrsize = reader.readUint32();
        this._infosize = hdrsize;
        if (hdrsize === Helper.GDI.BITMAPCOREHEADER_SIZE) {
            this._header = new BitmapCoreHeader(reader, false);
            this._infosize += this._header.colors() * (usergb ? 3 : 2);
        } else {
            this._header = new BitmapInfoHeader(reader, false);
            const masks =
                (this._header as BitmapInfoHeader).compression === Helper.GDI.BitmapCompression.BI_BITFIELDS ? 3 : 0;
            if (hdrsize <= Helper.GDI.BITMAPINFOHEADER_SIZE + (masks * 4)) {
                this._infosize = Helper.GDI.BITMAPINFOHEADER_SIZE + (masks * 4);
            }
            this._infosize += this._header.colors() * (usergb ? 4 : 2);
        }
    }

    public getWidth(): number {
        return this._header.width;
    }

    public getHeight(): number {
        return Math.abs(this._header.height);
    }

    public infosize(): number {
        return this._infosize;
    }

    public header(): BitmapCoreHeader | BitmapInfoHeader {
        return this._header;
    }
}
