import { XmlComponent } from '../../xml-components';

export class LastModifiedBy extends XmlComponent {
  constructor(name: string) {
      super('LastModifiedBy');
      if (name != null) {
        this.root.push(name);
      }
  }
}

export class LastSavedT extends XmlComponent {
  constructor(date: string) {
      super('LastSavedT');
      if (date != null) {
        this.root.push(date);
      }
  }
}

// tslint:disable-next-line: max-classes-per-file
// export class LastPrintedT extends XmlComponent {
//   constructor(date: string) {
//       super('LastPrintedT');
//       if (date != null) {
//         this.root.push(date);
//       }
//   }
// }

// tslint:disable-next-line: max-classes-per-file
export class CreatedBy extends XmlComponent {
  constructor(ip: string) {
      super('CreatedBy');
      if (ip != null) {
        this.root.push(ip);
      }
  }
}

// tslint:disable-next-line: max-classes-per-file
export class CreatedTime extends XmlComponent {
  constructor(ip: string) {
      super('CreatedTime');
      if (ip != null) {
        this.root.push(ip);
      }
  }
}

// tslint:disable-next-line: max-classes-per-file
export class LastButOne extends XmlComponent {
  constructor(ip: string) {
      super('LastButOne');
      if (ip != null) {
        this.root.push(ip);
      }
  }
}