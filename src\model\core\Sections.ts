import { getPxForMM } from './util';
import HeaderFooter from './HeaderFooter';
import { SectionProperty } from './SectionProperty';
import { PageOrientation } from '../../format/writer/file/document/body/section-properties/page-size';

/**
 * section: general conception.  主要包含 结，正文，页眉页脚。
 */

export class SectionPageSize {
    public width: number;
    public height: number;
    public orient: string;

    constructor() {
        this.width = getPxForMM(210);
        this.height = getPxForMM(297);
        this.orient = PageOrientation.PORTRAIT;
    }
}

export class SectionPageMargins {
    // 这些值是静态的。只有在设置时才会变！
    // 即使内容增多，行数增多，使得"实际"的paddingTop(or others)增大, paddingTop也不会变
    // 块。暂时未用到？
    public paddingTop: number;
    public paddingLeft: number;
    public paddingRight: number;
    public paddingBottom: number;

    public header: number; // header from top
    public footer: number; // footer from bottom

    constructor() {
        this.paddingTop = getPxForMM(25.4);
        this.paddingLeft = getPxForMM(31.7);
        this.paddingRight = getPxForMM(31.7);
        this.paddingBottom = getPxForMM(25.4);

        this.header = getPxForMM(12.5);
        this.footer = getPxForMM(12.5);
    }
}

// tslint:disable-next-line: max-classes-per-file
export class SectionPageNumInfo {
    public firstPage: number;
    public curPage: number;
    public bFirst: boolean;
    public bEven: boolean;
    public pageNum: number;

    constructor(firstPage: number, curPage: number, bFirst: boolean, bEven: boolean, pageNum: number) {
        this.firstPage = firstPage;
        this.curPage = curPage;
        this.bFirst = bFirst;
        this.bEven = bEven;

        this.pageNum   = pageNum;
    }

    public comparePageNumInfo(other: any): boolean {
        if ( undefined === other || null === other || this.curPage !== other.curPage ||
             this.bFirst !== other.bFirst || this.bEven !== other.bEven || this.pageNum !== other.pageNum ) {
            return false;
        }

        return true;
    }
}

export interface ISectionHeaderFooter {
    header: HeaderFooter;
    footer: HeaderFooter;
    sectProperty: SectionProperty;
}

export enum SectionIndex {
    Content = 0,
    Header,
    Footer,
}
