import { DocumentCore } from '../../model/DocumentCore';
import { EmrEditor } from '../../components/editor/Main';
// import Paragraph from '../../model/core/Paragraph';
// import ParaPortion from '../../model/core/Paragraph/ParaPortion';
import PrintDialog from '../../components/editor/module/modals/PrintDialog';
// tslint:disable-next-line: max-line-length
import { ResultType, IPrintOutpatient, ToolbarIndex, IStructJson, LOOP_THRESHOLD, IStructContent, ExportType, PAGE_FORMAT, isValidBlob, FileSaveType, EDITOR_VERSION, ErrorMessages,
    APO_XMLS_ARRAY, ViewModeType, IModeFonts, ISaveModJson, NeedsignalContent, InsertFilePositionType, CleanModeType,
    FONST_SIZE_PX, PrintType, NISTableCellType, OperateType, FONT_MAPPING,
    isValidORGBColor,
    IOpenDocOption,
    IReadDocOption,
    ICustomToolbarItem,
    IContinuePrint,
    ICPrintData} from '../commonDefines';

import { FormatWriter } from '../../format/writer/writer';
import { Reader } from '../../format/reader/reader';
import {GlobalEvent as gEvent, GlobalEventName as gEventName} from '../GlobalEvent';
import { ExternalAction } from './ExternalAction';
import { logger } from '../log/Logger';
import { ExportHtml } from '../../components/editor/module/ExportHtml';
import { PageProperty } from '../../model/StyleProperty';
import { getPxForMM } from '../../model/core/util';
import { saveAs } from 'file-saver';
import { message } from '../Message';
import {ParseContent, ParseXml } from './ParseXml';
import TextProperty from '../../model/core/TextProperty';
import { WasmInstance } from '../WasmInstance';
import { IOpenFileAPIPars, resetDocumentDefaultFont, skipEscapeString } from '../commonMethods';
import { setCustomToolbar } from '../GlobalConfig';
import { OptimizedParseXml, OptimizedParseContent } from './OptimizedParseXml'; // 添加优化版
import * as JSZip from '../jszip';


interface IDirectPrintDocJson {
    defaultPrinterName?: string;
    copies?: number;
    doubleprint?: number;
    pageNumberType?: number;
    startPageHdrFtr?: number;
    needDialog?: number;
    title?: string;
    pageSize?: string;
    cleanMode?: number;
    showWatermark?: boolean;
    pageRange?: string;
    landscape?: number;
    printOrientPortrait?: boolean; // legacy prop, is representation of printOrientPortrait
}

export default class OperateDocument extends ExternalAction {
    private _printVm: PrintDialog;
    private _documentCore: DocumentCore;
    private _host: EmrEditor;
    private _testIframe: any;
    private _closeOptions: any[];
    constructor(host: EmrEditor) {
        super(host.state.documentCore);
        this._host = host;
        this._documentCore = host.state.documentCore;
    }

    public createNew(): number {
        this._documentCore.createNewDocument();
        this._host.clearDatas();
        this._host.removeAllListen();
        this._host.handleRefresh();
        return ResultType.Success;
    }

    public convertStringToUInt8Arrs(sText: string): Uint8Array {
        if (!sText) {
            return null;
        }

        let arrs: string[];
        try {
            arrs = window.atob(sText)
            .split(',');
        } catch (err) {
            return null;
        }

        if (arrs.length === 0) {
            return null;
        }

        const len = arrs.length;
        const uint8Arrs = new Uint8Array(len);
        for (let index = 0; index < len; index++) {
            const num = +arrs[index];
            if (isNaN(num)) {
                return null;
            }
            uint8Arrs[index] = num;
        }
        return uint8Arrs;
    }

    public async exportToOtherFormat(nFormatType?: ExportType): Promise<string> {
        const date = new Date();
        return new Promise((resolve, reject) => {
            if (this.isEmptyDocument()) {
                resolve(ResultType.StringEmpty);
            }
            switch (nFormatType) {
                case ExportType.PDF: {
                        this.resetAdminMode();
                        resolve(null);
                        break;
                    }
                case ExportType.HTML: { 
                    this._host.exportToHtml(CleanModeType.CleanStruct)
                    .then((text) => {
                        resolve(text);
                    }).then(() => {
                        this._documentCore.recalculateAllForce();
                        this._host.handleRefresh();
                    });
                    break;
                }
                case ExportType.TEXT: {
                    const text = this._documentCore.getAllText(true);
                    logger.interface({
                        id: this._host.docId, name: 'exportToOtherFormatWithStream', startTime: date,
                        args: [nFormatType]
                    });
                    resolve(text);
                    break;
                }
                case ExportType.DOCX: {
                    this._documentCore.saveToDocxBlob().then((res) => {
                        logger.interface({
                            id: this._host.docId, name: 'exportToOtherFormatWithStream', startTime: date,
                            args: [nFormatType]
                        });
                        this.resetAdminMode();
                        const reader = new FileReader();
                        reader.readAsDataURL(res);
                        reader.onloadend = () => {
                            resolve(reader.result as string);
                        };

                        reader.onerror = (e) => {
                            console.log(e);
                            reject(ResultType.StringEmpty);
                        };
                    });
                    break;
                }
                case ExportType.EXCEL: {
                    this._documentCore.saveToExcelByTables().then((res) => {
                        logger.interface({
                            id: this._host.docId, name: 'exportToOtherFormatWithStream', startTime: date,
                            args: [nFormatType]
                        });
                        this.resetAdminMode();
                        const reader = new FileReader();
                        reader.readAsDataURL(res);
                        reader.onloadend = () => {
                            resolve(reader.result as string);
                        };

                        reader.onerror = (e) => {
                            console.log(e);
                            reject(ResultType.StringEmpty);
                        };
                    });
                    break;
                }
                default: {
                    this.resetAdminMode();
                    resolve(null);
                }
            }
        });
    }

    /**
     * 把当前打开的文件,导出成PDF或HTML文件。
     * @param nFormatType 导出HTML或PDF文件的类型 (1.pdf(TODO), 2.html, 3.text, 4.docx)
     */
    public exportToOtherFormatWithStream(nFormatType: ExportType): Promise<Blob> {
        const date = new Date();
        return new Promise((resolve, reject) => {
            if (this.isEmptyDocument()) {
                resolve(null);
            }
            switch (nFormatType) {
                case ExportType.PDF: {
                    this.resetAdminMode();
                    resolve(null);
                    break;
                }
                case ExportType.HTML: {
                    const html = new ExportHtml(this._host);
                    html.getHtml()
                        .then((res) => {
                            logger.interface({
                                id: this._host.docId, name: 'exportToOtherFormatWithStream', startTime: date,
                                args: [nFormatType]
                            });
                            this.resetAdminMode();
                            resolve(res);
                        });
                    break;
                }
                case ExportType.TEXT: {
                    const text = this._documentCore.getAllText(true);
                    logger.interface({
                        id: this._host.docId, name: 'exportToOtherFormatWithStream', startTime: date,
                        args: [nFormatType]
                    });
                    resolve(new Blob([text], {type: 'text/plain'}));
                    break;
                }
                case ExportType.DOCX: {
                    this._documentCore.saveToDocxBlob().then((res) => {
                        logger.interface({
                            id: this._host.docId, name: 'exportToOtherFormatWithStream', startTime: date,
                            args: [nFormatType]
                        });
                        this.resetAdminMode();
                        resolve(res);
                    });
                    break;
                }
                case ExportType.EXCEL: {
                    this._documentCore.saveToExcelByTables().then((res) => {
                        logger.interface({
                            id: this._host.docId, name: 'exportToOtherFormatWithStream', startTime: date,
                            args: [nFormatType]
                        });
                        this.resetAdminMode();
                        resolve(res);
                    });
                    break;
                }
                case ExportType.CLEANPDF: {
                    const html = new ExportHtml(this._host, false, CleanModeType.CleanMode);
                    html.getCleanHtml()
                        .then((res) => {
                            logger.interface({
                                id: this._host.docId, name: 'exportToOtherFormatWithStream', startTime: date,
                                args: [nFormatType]
                            });
                            this.resetAdminMode();
                            resolve(res);
                        });
                    break;
                }
                default: {
                    this.resetAdminMode();
                    resolve(null);
                }
            }
        });
    }

    /**
     * 设置服务端url
     * @param url 服务端url
     */
    public setRemoteUrl(url: string): number {
        if (!url || typeof url !== 'string') {
            return ResultType.ParamError;
        }

        WasmInstance.instance.ccall('SetRemoteUrl', 'null', ['string'], [url]);

        return ResultType.Success;
    }

    /**
     * 设置http服务端url
     * @param url 服务端url
     */
    public async setHttpSvrUrl(url: string): Promise<number> {
        if (!url || typeof url !== 'string') {
            return ResultType.ParamError;
        }
        this._host.pdfSvrUrl = url;

        return ResultType.Success;
    }

      /**
     * 设置http服务端url
     * @param url 服务端url
     */
      public async setOfdSvrUrl(url: string): Promise<number> {
        if (!url || typeof url !== 'string') {
            return ResultType.ParamError;
        }
        this._host.odfSvrUrl = url;

        return ResultType.Success;
    }

    public async openDocumentWithString(sText: string, mode: string): Promise<number> {
        const date = new Date();
        const uint8Arrs = this.convertStringToUInt8Arrs(sText);
        if (uint8Arrs == null) {
            this.resetAdminMode();
            return ResultType.Failure;
        }

        let obj: IOpenDocOption;
        if (mode) {
            try {
                obj = JSON.parse(mode);
            } catch (error) {
                this.resetAdminMode();
                return ResultType.ParamError;
            }
        }

        if (this.isEmptyDocument()) {
            return ResultType.Failure;
        }

        const docMode = (obj != null) ? obj.mode : null;
        const page = this.getPageProperty(obj);
        const viewMode = (obj != null) ? obj.view : null;
        const defaultFont = this.getDefaultFont(obj);
        const regionTitleFont = this.getRegionTitleFont(obj);
        const operate = (obj != null) ? (obj.operate === 'new' ? OperateType.New : OperateType.Open) : OperateType.Open;

        const cleanMode = (obj != null) ? obj.cleanMode : null;
        const nisTableJson = (obj != null) ? obj.nisTableJson : null;

        if (nisTableJson && !(nisTableJson instanceof Array)) {
            this.resetAdminMode();
            return ResultType.ParamError;
        }

        const bRelayRecalc = obj?.bRelayRecalc ? obj?.bRelayRecalc : null;
        let modeFonts: IModeFonts = null;
        if (defaultFont != null) {
            modeFonts = {defaultFont, regionTitleFont: null};
        }
        let options = this._closeOptions;
        if (!options) {
            options = this._closeOptions = [];
        }
        const closeObj = {
            bClose: false,
        };
        options.push(closeObj);
        if (regionTitleFont != null) {
            if (modeFonts == null) {
                modeFonts = {defaultFont: null, regionTitleFont};
            } else {
                modeFonts.regionTitleFont = regionTitleFont;
            }
        }

        this._documentCore.createNewDocument(true);

        this._host.clearDatas();

        // 根据需求修改修订状态
        this._documentCore.changeRevisionState2(true);
        this._documentCore.setRegionContentDefaultFont(defaultFont);
        this._documentCore.setRelayRecalc(bRelayRecalc);

        const disableSaveDialog = (obj != null) ? obj.disableSaveDialog : null;
        if (disableSaveDialog != null) {
            if (typeof disableSaveDialog === 'boolean') {
                this._documentCore.setBDisableSaveDialog(disableSaveDialog);
            }
        } else {
            // always need to reset
            this._documentCore.setBDisableSaveDialog(false);
        }

        const reader = new Reader(this._documentCore.getDocument());
        resetDocumentDefaultFont(this._documentCore.getDocument());

        let result: any;
        const flag = true;
        const option: IReadDocOption = {time: 0, pageProperty: page, closeObj, operate, nisTableJson,
                                        bLoadCache: obj?.bLoadCache, recalcPromise: null};
        let time = new Date().getTime() - date.getTime();
        try {
            const params: IOpenFileAPIPars = {options: option, modeFonts, bRecalc: flag, bNoEndPara: false};
            result = await reader.readFromString2(uint8Arrs, params);
            if (this._documentCore.getDocument() == null) {
                return ResultType.Failure;
            }
        } catch (e) {
            this.resetAdminMode();
            options.splice(options.findIndex((item) => item === closeObj), 1);
            return e;
        }
        options.splice(options.findIndex((item) => item === closeObj), 1);

        if (result === ResultType.Success) {
            if (viewMode != null) {
                if (viewMode === 'more') {
                    this._documentCore.setViewMode(ViewModeType.MorePage);
                } else if (viewMode === 'page') {
                    this._documentCore.setViewMode(ViewModeType.BreakPageView);
                } else if (viewMode === 'compact') {
                    this._documentCore.setViewMode(ViewModeType.CompactView);
                }
            }
            if (cleanMode != null) {
                if (cleanMode === 'true') {
                    // 一旦开启无法切换回来
                    this._documentCore.browseTemplet(1, 0); // parameters not in use
                }
            }
            if (docMode != null) {
                if (docMode === 'readonly') {
                    this._documentCore.protectDoc(true);
                } else if (docMode === 'normal') {
                    this._documentCore.protectDoc(false);
                } else if ('strict' === docMode) {
                    this._documentCore.setStrictMode(true);
                }
            }

            /* IFTRUE_WATER */
            // 刷新水印dom及坐标
            this._documentCore.resetCorePosition(true, false);
            /* FITRUE_WATER */

            this._documentCore.setDirty(false);
            this.resetAdminMode();
            this._host.resetMenuDatas();
            this._host.mainRefresh();
            if (null == docMode || 'readonly' !== docMode) {
                this._host.initCursorPos();
            } else {
                this._host.onFocus(false);
            }
        }

        const endTime = new Date().getTime();
        time += option.time + endTime - date.getTime();
        const finalDate = new Date(endTime - time);

        if (this._host.externalEvent) {
            this._host.externalEvent.nsoFileOpenCompleted(sText);
        }
        logger.interface({id: this._host.docId, name: 'openDocumentWithString', startTime: finalDate,
            result, args: [sText, mode]});
        this._host.openCompleted();
        return result;
    }

    public async openDocumentWithStream(content: Blob, mode: string): Promise<number> {
        let date = new Date();
        let obj: IOpenDocOption;
        if (mode) {
            try {
                obj = JSON.parse(mode);
            } catch (error) {
                this.resetAdminMode();
                return ResultType.ParamError;
            }
        }

        if (obj != null && obj.fileFormat === 'txt') {
            if (!content || content.type !== 'text/plain') {
                this.resetAdminMode();
                return ResultType.ParamError;
            }
        } else if (!content || isValidBlob(content) === false) {
            this.resetAdminMode();
            return ResultType.ParamError;
        }
        if (this.isEmptyDocument()) {
            return ResultType.Failure;
        }
        // console.log(content);
        // console.log(mode);
        const docMode = (obj != null) ? obj.mode : null;
        const page = this.getPageProperty(obj);
        const viewMode = (obj != null) ? obj.view : null;
        const defaultFont = this.getDefaultFont(obj);
        const regionTitleFont = this.getRegionTitleFont(obj);
        const operate = (obj != null) ? (obj.operate === 'new' ? OperateType.New : OperateType.Open) : OperateType.Open;

        const cleanMode = (obj != null) ? obj.cleanMode : null;
        const nisTableJson = (obj != null) ? obj.nisTableJson : null;

        if (nisTableJson && !(nisTableJson instanceof Array)) {
            this.resetAdminMode();
            return ResultType.ParamError;
        }

        const bRelayRecalc = obj?.bRelayRecalc ? obj?.bRelayRecalc : null;
        // console.log(defaultFont)
        let modeFonts: IModeFonts = null;
        if (defaultFont != null) {
            modeFonts = {defaultFont, regionTitleFont: null};
        }
        let options = this._closeOptions;
        if (!options) {
            options = this._closeOptions = [];
        }
        const closeObj = {
            bClose: false,
        };
        options.push(closeObj);
        if (regionTitleFont != null) {
            if (modeFonts == null) {
                modeFonts = {defaultFont: null, regionTitleFont};
            } else {
                modeFonts.regionTitleFont = regionTitleFont;
            }
        }
        this._documentCore.createNewDocument(true);

        this._host.clearDatas();

        // 根据需求修改修订状态
        this._documentCore.changeRevisionState2(true);
        // setRegionContentFont(this.getDefaultFont(obj));
        this._documentCore.setRegionContentDefaultFont(defaultFont);
        this._documentCore.setRelayRecalc(bRelayRecalc);

        const disableSaveDialog = (obj != null) ? obj.disableSaveDialog : null;
        if (disableSaveDialog != null) {
            if (typeof disableSaveDialog === 'boolean') {
                this._documentCore.setBDisableSaveDialog(disableSaveDialog);
            }
        } else {
            // always need to reset
            this._documentCore.setBDisableSaveDialog(false);
        }

        const reader = new Reader(this._documentCore.getDocument());
        resetDocumentDefaultFont(this._documentCore.getDocument());

        let result: any;
        // const flag = page === undefined;
        const flag = true;
        const option: IReadDocOption = {time: 0, pageProperty: page, closeObj, operate, nisTableJson,
                                        bLoadCache: obj?.bLoadCache, recalcPromise: null};
        let time = new Date().getTime() - date.getTime();
        // console.log(modeFonts)
        try {
            if (obj != null && obj.fileFormat === 'txt') {
                const tmpResult = await reader.readFromTxtStream(content, false, option);
                result = tmpResult ? ResultType.Success : ResultType.Failure;
            } else {
                // result = await reader.readFromStream(content, false, false, flag, option, modeFonts);
                /* IFTRUE_WATER */
                if (true) {
                    const res = WasmInstance?.paragraphRecalculateRecalculateLinePositionWithInfo(0, 0, 0, -9);
                    WasmInstance?.freeArray(res);
                }
                /* FITRUE_WATER */
                const params: IOpenFileAPIPars = {options: option, modeFonts, bRecalc: flag, bNoEndPara: false};
                result = await reader.readFromStream2(content, params);
            }
            if (this._documentCore.getDocument() == null) {
                return ResultType.Failure;
            }
        } catch (e) {
            this.resetAdminMode();
            options.splice(options.findIndex((item) => item === closeObj), 1);
            return e;
        }
        options.splice(options.findIndex((item) => item === closeObj), 1);

        date = new Date();
        if (result === ResultType.Success) {
            // if (page) {
            //
            //     this._documentCore.setPageProperty(page);
            //
            //     // this._documentCore.recalculate();
            // }
            if (viewMode != null) {
                if (viewMode === 'more') {
                    this._documentCore.setViewMode(ViewModeType.MorePage);
                }
                // if (viewMode === 'web') {
                //     this._documentCore.setViewMode(ViewModeType.WebView);
                // } 
                else if (viewMode === 'page') {
                    this._documentCore.setViewMode(ViewModeType.BreakPageView);
                } else if (viewMode === 'compact') {
                    this._documentCore.setViewMode(ViewModeType.CompactView);
                }
            }
            if (cleanMode != null) {
                if (cleanMode === 'true') {
                    // 一旦开启无法切换回来
                    this._documentCore.browseTemplet(1, 0); // parameters not in use
                }
            }
            if (docMode != null) {
                if (docMode === 'readonly') {
                    this._documentCore.protectDoc(true);
                } else if (docMode === 'normal') {
                    this._documentCore.protectDoc(false);
                } else if ('strict' === docMode) {
                    this._documentCore.setStrictMode(true);
                }
            }

            // 非只读状态下，尝试开启数据埋点
            // if (docMode !== 'readonly') {
            //const patientID = obj?.patientID;
           // const apoName = obj?.apoName;
           // const outpatientNum = obj?.inpatId;
           // this._documentCore.initElementMonitor({
           //     patientID, apoName, outpatientNum, apoID: obj?.apoID
           // });
            // }

            /* IFTRUE_WATER */
            // 刷新水印dom及坐标
            this._documentCore.resetCorePosition(true, false);
            /* FITRUE_WATER */

            this._documentCore.setDirty(false);
            // this._host.resetCursor();
            this.resetAdminMode();
            // this._host.handleRefresh();
            this._host.resetMenuDatas();
            this._host.mainRefresh();
            if (null == docMode || 'readonly' !== docMode) {
                this._host.initCursorPos();
            } else {
                this._host.onFocus(false);
            }

            // if (!bRelayRecalc && 'object' === typeof option.recalcPromise) {
            //     await option.recalcPromise;
            // }
        } else if (result === ResultType.NeedDebug) {
            // 0008 save file
            const fileReader = new FileReader();
            fileReader.readAsDataURL(content);
            fileReader.onloadend = () => {
                const base64data = fileReader.result;
                // tslint:disable-next-line: no-console
                console.log(base64data);
                this.resetAdminMode();
                // tslint:disable-next-line: max-line-length
                const dateString = '' + date.getFullYear() + (date.getMonth() + 1) + date.getDate() + date.getHours() + date.getMinutes();
                const errorString = dateString + 'CN0008' + (Math.random() * 10000).toFixed();
                const fileType: string = FileSaveType.APO + ';version=' + EDITOR_VERSION;
                const blob = new Blob([base64data], {type: fileType});
                saveAs(blob, errorString + '.apod');
                message.error(ErrorMessages.XmlCorrupted);
            };
        }

        const endTime = new Date().getTime();
        time += option.time + endTime - date.getTime();
        date = new Date(endTime - time);
        logger.interface({id: this._host.docId, name: 'openDocumentWithStream', startTime: date,
            result, args: [content, mode]});
        // if (this._host.externalEvent) {
        //     this._host.externalEvent.nsoFileOpenCompleted(null);
        // }
        this._host.openCompleted();
        return result;
    }

    public async saveToString(sModJson?: string): Promise<string> {
        let date = new Date();
        if (this.isEmptyDocument()) {
            return ResultType.StringEmpty;
        }
        // prepare for html generation if pages <= 3
        const formatWriter = new FormatWriter(this._host);

        const xmlProps: any = {
            documentCore: this._documentCore,
            properties: this.getCustomPropertiesInSettings(),
            selectedArea: [],
        };
        let blob: Blob = null;
        let obj: ISaveModJson = null;
        if (sModJson != null) {
            try {
                obj = JSON.parse(sModJson);
                // error tolerant
                if (obj && obj.NeedsignalContent != null) {
                    obj.needsignalContent = obj.NeedsignalContent;
                }
                if (obj && obj.NeedRevision != null) {
                    obj.needRevision = obj.NeedRevision;
                }
            } catch (e) {
                // to do
            }
        }
        const options: any = {time: 0};
        const time = new Date().getTime() - date.getTime();
        try {
            blob = await formatWriter.generateBlob(xmlProps, true, false, options, obj, false);
        } catch (e) {
            this.resetAdminMode();
            return e + '';
        }

        if (!blob || blob.size === 0) {
            this.resetAdminMode();
            return ResultType.StringEmpty;
        }

        // Convert blob to ArrayBuffer, then to base64 string
        let buffer: ArrayBuffer;
        try {
            buffer = await blob.arrayBuffer();
        } catch (e) {
            this.resetAdminMode();
            return e + '';
        }

        const arrays = new Uint8Array(buffer);
        const result = window.btoa(arrays.join(','));

        this.resetAdminMode();
        date = new Date(new Date().getTime() - time - options.time);
        logger.interface({id: this._host.docId, name: 'saveToString', startTime: date,
            result: ResultType.Success, args: [sModJson]});
        // console.log(result);
        return result;
    }

    public async saveToStream(sModJson: string): Promise<Blob> {
        let date = new Date();
        if (this.isEmptyDocument()) {
            return null;
        }
        // prepare for html generation if pages <= 3
        const formatWriter = new FormatWriter(this._host);

        const xmlProps: any = {
            documentCore: this._documentCore,
            properties: this.getCustomPropertiesInSettings(),
            selectedArea: [],
        };
        let blob: Blob = null;
        let obj: ISaveModJson = null;
        if (sModJson != null) {
            try {
                obj = JSON.parse(sModJson);
                // error tolerant
                if (obj && obj.NeedsignalContent != null) {
                    obj.needsignalContent = obj.NeedsignalContent;
                }
                if (obj && obj.NeedRevision != null) {
                    obj.needRevision = obj.NeedRevision;
                }
            } catch (e) {
                // to do
            }
        }
        const options: any = {time: 0};
        const time = new Date().getTime() - date.getTime();
        try {
            blob = await formatWriter.generateBlob(xmlProps, true, false, options, obj, false);
        } catch (e) {
            // this.resetAdminMode();
            // return e + '';
        }
        this.resetAdminMode();
        date = new Date(new Date().getTime() - time - options.time);
        logger.interface({id: this._host.docId, name: 'saveToStream', startTime: date,
            result: ResultType.Success, args: []});
        // console.log(result);
        return blob;
    }

    /**
     * 保存为docx
     */
    public saveToDocx(): Promise<Blob> {
        return this._documentCore.saveToDocxBlob();
    }

    /***
     * 为AI设计的 只保留content部分
     */
    public async saveStructContentToStreamAI(sName: string): Promise<string> {
        if (!sName) {
            this.resetAdminMode();
            return null;
        }

        const newcontrol = this._documentCore.getNewControlByName(sName);
        if(!newcontrol){
            this.resetAdminMode();
            return null;
        }

        newcontrol.addCustomPropItem("AI-id",sName,1);

        const contents = this._documentCore.getElementsByNewControl(sName);
        if (contents.length === 0) {
            this.resetAdminMode();
            return null;
        }

        const formatWriter = new FormatWriter();
        const xmlProps: any = {
            documentCore: this._documentCore,
            properties: this.getCustomPropertiesInSettings(),
            selectedArea: contents,
        };

        let blob: Blob = null;
        const options: any = {time: 0, bSaveStructContent: true};
        try {
            blob = await formatWriter.generateContentBlob(xmlProps, false, true, options);
             // Extract content.xml from blob
             const originalXml = await this.extractContentXmlFromBlob(blob);
            
             // Generate simplified XML (placeholder for future implementation)
             const simplifiedXml = await this.generateSimplifiedXml(originalXml);
             
             // Create JSON response
             const response = JSON.stringify({
                 originalXml,
                 simplifiedXml
             });
             
             this.resetAdminMode();
             return response;
 
        } catch (e) {
            this.resetAdminMode();
            return null;

        }
    }

    public async saveStructContentToStream(sName: string): Promise<Blob> {
        let date = new Date();
        if (!sName) {
            this.resetAdminMode();
            return null;
        }

        const contents = this._documentCore.getElementsByNewControl(sName);
        if (contents.length === 0) {
            this.resetAdminMode();
            return null;
        }

        const formatWriter = new FormatWriter();
        const xmlProps: any = {
            documentCore: this._documentCore,
            properties: this.getCustomPropertiesInSettings(),
            selectedArea: contents,
        };

        let blob: Blob = null;
        const options: any = {time: 0, bSaveStructContent: true};
        const time = new Date().getTime() - date.getTime();
        try {
            blob = await formatWriter.generateBlob(xmlProps, false, true, options);
        } catch (e) {
            // this.resetAdminMode();
            // return e + '';
        }
        this.resetAdminMode();
        date = new Date(new Date().getTime() - time - options.time);
        logger.interface({id: this._host.docId, name: 'saveStructContentToStream', startTime: date,
            result: ResultType.Success, args: []});
        return blob;
    }

    public async saveStructContentToStreamByArray(sNameJson: string): Promise<Map<string, Blob>> {
        const date = new Date();
        if (!sNameJson) {
            this.resetAdminMode();
            return null;
        }
        if (this.isEmptyDocument()) {
            return null;
        }
        let names: string[];
        try {
            names = JSON.parse(sNameJson);
        } catch (error) {
            this.resetAdminMode();
            return null;
        }
        if (!names || names.length === 0) {
            this.resetAdminMode();
            return null;
        }
        const promises: any[] = [];
        const arrs: any[] = [];
        const formatWriter = new FormatWriter();
        const xmlProps: any = {
            documentCore: this._documentCore,
            properties: this.getCustomPropertiesInSettings(),
            selectedArea: null,
        };
        // const time = new Date().getTime() - date.getTime();
        names.forEach((name) => {
            const contents = this._documentCore.getElementsByNewControl(name);
            if (contents.length === 0) {
                return null;
            }
            xmlProps.selectedArea = contents;
            const options: any = {name};
            arrs.push(options);
            promises.push(formatWriter.generateBlob(xmlProps, false, true, options));
        });

        let results;
        try {
            results = await Promise.all(promises);
        } catch (error) {
            this.resetAdminMode();
            return null;
        }
        const map = new Map();
        arrs.forEach((name, index) => {
            map.set(name.name, results[index]);
        });
        this.resetAdminMode();
        logger.interface({id: this._host.docId, name: 'saveStructContentToStreamByArray', startTime: date,
            result: ResultType.Success, args: []});
        return map;
    }

    public async saveSelectAreaToString(): Promise<string> {
        const date = new Date();
        const contents = this._documentCore.getSelectedContentToSave();
        // console.log(contents)
        if (!contents || contents.length === 0) {
            this.resetAdminMode();
            return '';
        }
        // const apollo = new Apollo(contents, this._documentCore.getDocument());
        // return apollo.getApollo();

        const formatWriter = new FormatWriter();

        const xmlProps: any = {
            documentCore: this._documentCore,
            properties: this.getCustomPropertiesInSettings(),
            selectedArea: contents,
        };

        let buffer: ArrayBuffer;
        try {
            buffer = await formatWriter.generate(xmlProps, false, true);
        } catch (e) {
            this.resetAdminMode();
            return e + '';
        }

        if (!buffer || buffer.byteLength === 0) {
            return ResultType.StringEmpty;
        }

        const arrays = new Uint8Array(buffer);
        const result = window.btoa(arrays.join(','));
        this.resetAdminMode();
        logger.interface({id: this._host.docId, name: 'saveSelectAreaToString', startTime: date,
            result: ResultType.Success, args: []});
        return result;
    }

    public async saveSelectAreaToStream(sModJson: string): Promise<Blob> {
        const date = new Date();
        let obj: ISaveModJson = null;
        if (sModJson != null) {
            try {
                obj = JSON.parse(sModJson);
            } catch (error) {
                // to do;
            }
        }

        let structType: number;
        if (obj && typeof obj.NeedStruct === 'number') {
            structType = obj.NeedStruct;
        }
        const contents = this._documentCore.getSelectedContentToSave(structType);
        // const contents = this._documentCore.getSelectedContent();
        // TODO: #108183 全部选中区域(其实没全部选中)，有时会返回[region]无paragraph情况
        // console.log(contents)
        if (!contents || contents.length === 0) {
            this.resetAdminMode();
            return null;
        }
        // const apollo = new Apollo(contents, this._documentCore.getDocument());
        // return apollo.getApollo();

        const formatWriter = new FormatWriter();

        const xmlProps: any = {
            documentCore: this._documentCore,
            properties: this.getCustomPropertiesInSettings(),
            selectedArea: contents,
        };

        // error tolerant
        if (obj && obj.NeedsignalContent != null) {
            obj.needsignalContent = obj.NeedsignalContent;
        }
        if (obj && obj.NeedRevision != null) {
            obj.needRevision = obj.NeedRevision;
        }

        let blob: Blob = null;
        try {
            blob = await formatWriter.generateBlob(xmlProps, false, true, {}, obj);
        } catch (e) {
            // this.resetAdminMode();
            // return e + '';
        }
        this.resetAdminMode();
        logger.interface({id: this._host.docId, name: 'saveSelectAreaToStream', startTime: date,
            result: ResultType.Success, args: []});
        return blob;
    }

    /**
     * 保存指定Region内容为一个子文档 为AI设计，并返回包含XML内容的JSON字符串
     * @param sName 区域名称
     * @returns JSON字符串，包含原始XML和精简后的XML
     */
    public async saveRegionContentToStreamAI(sName: string): Promise<string> {
        const region = this._documentCore.getRegionByName(sName);
        if (region == null) {
            this.resetAdminMode();
            return null;
        }

        region.addCustomPropItem("AI-id",sName,1);

        let obj: ISaveModJson = null;
        obj = {needsignalContent: NeedsignalContent.Clear, needregionStruct: NeedsignalContent.Keep};
    
        const {needregionStruct} = obj;
        let content: any[] = [region];
        if (needregionStruct === NeedsignalContent.Clear) {
            content = region.getContent();
        }
        if (content.length === 0 || (content != null && content[0] == null)) {
            this.resetAdminMode();
            return null;
        }

        const xmlProps: any = {
            documentCore: this._documentCore,
            properties: this.getCustomPropertiesInSettings(),
            selectedArea: content,
        };

        const formatWriter = new FormatWriter();
        let blob: Blob = null;
        try {
            blob = await formatWriter.generateContentBlob(xmlProps, false, true, {}, obj);
            
            // Extract content.xml from blob
            const originalXml = await this.extractContentXmlFromBlob(blob);
            
            // Generate simplified XML (placeholder for future implementation)
            const simplifiedXml = await this.generateSimplifiedXml(originalXml);
            
            // Create JSON response
            const response = JSON.stringify({
                originalXml,
                simplifiedXml
            });
            
            this.resetAdminMode();
            return response;
        } catch (e) {
            console.error("Error in saveRegionContentToStreamAI:", e);
            this.resetAdminMode();
            return null;
        }
    }

    /**
     * 从Blob中提取content.xml内容
     * @param blob 文档Blob
     * @returns content.xml内容的字符串
     */
    private async extractContentXmlFromBlob(blob: Blob): Promise<string> {
        try {
            // Convert blob to ArrayBuffer
            const buffer = await blob.arrayBuffer();
            
            // Remove the 24-byte header to get the actual zip content
            // Based on the addFileHeader function reference in fileToBlobApi
            const zipBuffer = buffer.slice(24);
            
            // Use JSZip to extract content from the zip file
            const JSZip = require('jszip'); // Assuming JSZip is available or imported
            const zip = await JSZip.loadAsync(zipBuffer);
            
            // Extract content.xml
            const contentFile = zip.file("Document.xml");
            if (!contentFile) {
                throw new Error("Content XML file not found in the archive");
            }
            
            // Get the content as text
            const contentXml = await contentFile.async("string");
            return contentXml;
        } catch (error) {
            console.error("Error extracting content.xml:", error);
            return "";
        }
    }

    /**
 * 生成简化版的XML给大模型处理
 * @param originalXml 原始XML内容
 * @returns 简化后的JSON字符串
 */
    private async generateSimplifiedXml(originalXml: string): Promise<string> {
        try {
            const parser = new DOMParser();
            const xmlDoc = parser.parseFromString(originalXml, "text/xml");
            
            const result = {
                paragraphs: [] as Array<{
                    id: string;
                    content: string;
                    textUnits: Array<{
                        id: string;
                        xpath: string;
                        originalText: string;
                    }>;
                }>
            };
    
            // 遍历所有段落
            const paragraphs = xmlDoc.getElementsByTagName("w:p");
            for (let pIndex = 0; pIndex < paragraphs.length; pIndex++) {
                const paragraph = paragraphs[pIndex];
                const textNodes = paragraph.getElementsByTagName("w:t");
                
                const paragraphObj = {
                    id: `p${pIndex}`,
                    content: "",
                    textUnits: [] as Array<{
                        id: string;
                        xpath: string;
                        originalText: string;
                    }>
                };
    
                // 收集文本单元
                for (let tIndex = 0; tIndex < textNodes.length; tIndex++) {
                    const textNode = textNodes[tIndex];
                    const text = textNode.textContent?.trim() || "";
                    
                    if (text) {
                        const xpath = this.generateXPath(textNode);
                        paragraphObj.content += text;
                        paragraphObj.textUnits.push({
                            id: `${paragraphObj.id}_t${tIndex}`,
                            xpath,
                            originalText: text
                        });
                    }
                }
    
                if (paragraphObj.textUnits.length > 0) {
                    result.paragraphs.push(paragraphObj);
                }
            }
    
            return JSON.stringify(result, null, 2);
        } catch (error) {
            console.error("Error generating simplified XML:", error);
            throw new Error("Failed to generate simplified XML");
        }
    }
    
    /**
     * 生成精确的XPath路径
     */
    private generateXPath(node: Node): string {
        const pathSegments: string[] = [];
        let current: Node | null = node;
    
        while (current && current.nodeType === Node.ELEMENT_NODE) {
            let index = 0;
            let hasPreviousSiblings = false;
            
            // 计算同级节点中的位置
            const siblings = current.parentNode?.childNodes || [];
            for (let i = 0; i < siblings.length; i++) {
                const sibling = siblings[i];
                if (sibling === current) break;
                if (sibling.nodeType === Node.ELEMENT_NODE && 
                    sibling.nodeName === current.nodeName) {
                    index++;
                }
            }
    
            pathSegments.unshift(
                index > 0 ? `${current.nodeName}[${index + 1}]` : current.nodeName
            );
            
            current = current.parentNode;
        }
    
        return `/${pathSegments.join("/")}`;
    }

    /**
     * 保存指定Region内容为一个子文档
     * @param sName /
     * @param sModJson /
     */
    public async saveRegionContentToStream(sName: string, sModJson: string): Promise<Blob> {
        const region = this._documentCore.getRegionByName(sName);
        // console.log(region)
        if (region == null) {
            this.resetAdminMode();
            return null;
        }

        // if (this.isEmptyDocument()) {
        //     return null;
        // }

        let obj: ISaveModJson = null;
        if (sModJson != null) {
            try {
                obj = JSON.parse(sModJson);
                // error tolerant
                if (obj.NeedsignalContent != null) {
                    obj.needsignalContent = obj.NeedsignalContent;
                }
                if (obj.NeedregionStruct != null) {
                    obj.needregionStruct = obj.NeedregionStruct;
                }
                // if not exist, set to default
                if (obj.needsignalContent == null) {
                    obj.needsignalContent = NeedsignalContent.Clear;
                }
                if (obj.needregionStruct == null) {
                    obj.needregionStruct = NeedsignalContent.Keep;
                }
                if (obj && obj.NeedRevision != null) {
                    obj.needRevision = obj.NeedRevision;
                }
            } catch (e) {

                obj = {needsignalContent: NeedsignalContent.Clear, needregionStruct: NeedsignalContent.Keep};
            }
        } else {
            // if sModJson not exist, set to default
            obj = {needsignalContent: NeedsignalContent.Clear, needregionStruct: NeedsignalContent.Keep};

        }

        const {needregionStruct} = obj;
        let content: any[] = [region];
        if (needregionStruct === NeedsignalContent.Clear) {
            content = region.getContent();
        }
        if (content.length === 0 || (content != null && content[0] == null)) {
            this.resetAdminMode();
            return null;
        }

        // console.log(content)
        const xmlProps: any = {
            documentCore: this._documentCore,
            properties: this.getCustomPropertiesInSettings(),
            selectedArea: content,
        };

        const formatWriter = new FormatWriter();
        let blob: Blob = null;
        try {
            blob = await formatWriter.generateBlob(xmlProps, false, true, {}, obj);
        } catch (e) {
            // this.resetAdminMode();
            // return e + '';
        }

        this.resetAdminMode();
        return blob;
    }

    public close(): void {
        this._documentCore.sendMonitorData(true);
        this._documentCore.close();
        this._host.clearDom();
        // TODO:
    }

    /**
     * 关闭当前打开的病历。
     * 讨论点：关闭后病历显示空白，回收部分资源。
     */

     public closeDoc(bRefresh?: boolean): void {
        this._documentCore.sendMonitorData(true);
        this._documentCore.createNewDocument();
        this._host.clearDatas();

        const closeOptions = this._closeOptions || [];
        closeOptions.forEach((obj) => {
            obj.bClose = true;
        });

        if (bRefresh === true) {
            this._host.handleRefresh();
        }
    }

    public setDebugMode(flag: boolean): void {
        if (flag === true) {
            logger.open(this._host.id);
        } else {
            logger.close(this._host.id);
        }
    }

    public setMenuBarVisible(bVisible: boolean): boolean {
        if (typeof bVisible !== 'boolean') {
            return false;
        }
        return this._host.showMenu(bVisible);
    }

    /** 设置自定义工具栏 */
    public setCustomToolbar(items: ICustomToolbarItem[]): number {
        if (setCustomToolbar(items)) {
            gEvent.setEvent(this._host.docId, gEventName.ToolBarCustomChange, items);
            return ResultType.Success;
        }
        
        return ResultType.UnEdited;
    }


    public disableMenuItem(sJson: string): number {
        if (!sJson) {
            return ResultType.ParamError;
        }
        let option: any;
        try {
            option = JSON.parse(sJson);
        } catch (error) {
            return ResultType.ParamError;
        }

        if (typeof option !== 'object') {
            return ResultType.ParamError;
        }

        // 检查是否有主菜单项的键
        const hasMainMenuItems = 'helper' in option || 'Helper' in option;
        
        // 触发右键菜单事件
        gEvent.setEvent(this._host.docId, gEventName.RightMenuVisible, option);
        
        // 如果有主菜单项，也触发主菜单事件
        if (hasMainMenuItems) {
            const mainMenuOption = { ...option };
            gEvent.setEvent(this._host.docId, gEventName.MainMenuVisible, mainMenuOption);
        }

        return option.result || ResultType.Success;
    }

    public showSystemDialog(nType: number): number {
        try {
            // 根据nType确定对话框类型
            let dialogType;
            switch (nType) {
                case 4: // 医学表达式对话框
                    dialogType = ToolbarIndex.MedEquation;
                    break;
                // 其他类型可以在此添加
                case 1: // 字体对话框
                    dialogType = ToolbarIndex.Char;
                    break;
                case 2: // 段落对话框
                    dialogType = ToolbarIndex.Paragraph;
                    break;
                case 3: // 表格属性对话框
                    dialogType = ToolbarIndex.TableProps;
                    break;
                case 5: // 特殊字符对话框
                    dialogType = ToolbarIndex.SpecialCharacter;
                    break;
                default:
                    return 1; // 未知类型，返回失败
            }
            // 使用全局事件触发对话框显示
            gEvent.setEvent(this._host.docId, gEventName.DialogEvent, dialogType);
            return 0; // 成功
        } catch (error) {
            return 1; // 失败
        }
    }

    public showToolBarItem(sJson: string): boolean {
        if (!sJson || typeof sJson !== 'string') {
            return false;
        }
        let option: any;
        try {
            option = JSON.parse(sJson);
        } catch (error) {
            return false;
        }

        if (typeof option !== 'object') {
            return false;
        }
        gEvent.setEvent(this._host.docId, gEventName.ToolBarItemVisible, option);
        return option.result;
    }

    /**
     * 只读模式下控制某些视图属性
     */
    public setViewPropInReadonlyMode(sJson: string): number {
        if (!sJson) {
            return ResultType.ParamError;
        }
        let obj: any;
        try {
            obj = JSON.parse(sJson);
        } catch (error) {
            return ResultType.ParamError;
        }

        if (typeof obj !== 'object') {
            return ResultType.ParamError;
        }
        let result: number = ResultType.UnEdited;
        if (obj.hasOwnProperty('showRightMenu') && typeof obj.showRightMenu === 'boolean') {
            const toption = {readonlyMode: {bEnable: obj.showRightMenu, result: ResultType.UnEdited}};
            gEvent.setEvent(this._host.docId, gEventName.RightMenuVisible, toption);
            result = result === ResultType.UnEdited ? toption.readonlyMode.result : result;
        }
        const option = {result};
        if (obj.hasOwnProperty('showSignHelptips') && typeof obj.showSignHelptips === 'boolean') {
            option['showSignHelptips'] = obj.showSignHelptips;
        }
        let bRefresh: boolean = false;
        if (obj.hasOwnProperty('showBgColor') && typeof obj.showBgColor === 'boolean') {
            option['showBgColor'] = obj.showBgColor;
            bRefresh = true;
        }
        option['showRevision'] = obj.showRevision;

        gEvent.setEvent(this._host.docId, gEventName.ViewPropChange, option);
        this.resetAdminMode();
        if (bRefresh && option.result === ResultType.Success && this._documentCore.isProtectedMode()) {
            this._host.handleRefresh();
        }
        return option.result;
    }

    /**
     * 是否显示超过高度的文本
     * @param bShow true: 显示， false: 不显示  默认不显示
     */
     public showShelteredText(bShow: boolean): number {
        if (typeof bShow !== 'boolean') {
            return ResultType.ParamError;
        }

        return this._documentCore.showShelteredText(bShow);
    }


    public showRevisionPanel(flag: boolean) {
        if (typeof flag !== 'boolean') {
            return ResultType.ParamError;
        }
        gEvent.setEvent(this._host.docId, gEventName.DialogEvent, ToolbarIndex.ReviewPanel, flag);
    }

    public async generateAutoTestFiles(): Promise<string[]> {
        const html = new ExportHtml(this._host, true);
        const result = await html.exportAutoTestFiles();
        return result;
    }

    public compareAutoTestFiles(autoFiles1: string[], autoFiles2: string[]): string | number {
        // html, structs, image, table
        let result: number = 0;
        const html1 = autoFiles1[0];
        const structs1 = autoFiles1[1];
        const image1 = autoFiles1[2];
        const table1 = autoFiles1[3];
        const html2 = autoFiles2[0];
        const structs2 = autoFiles2[1];
        const image2 = autoFiles2[2];
        const table2 = autoFiles2[3];

        result = this.compareAutoTestHtml(html1, html2) === true ? 1 : 0;
        // console.log(result)
        if (result === 1) {
            let subResult = 'true';
            subResult = this.compareAutoTestStructs(structs1, structs2);
            if (subResult !== 'true') {
                return subResult;
            }
            subResult = this.compareAutoTestImages(image1, image2);
            if (subResult !== 'true') {
                return subResult;
            }
            subResult = this.compareAutoTestTables(table1, table2);
            if (subResult !== 'true') {
                return subResult;
            }
        }

        return result;
    }

    /**
     * 控制工具栏是否显示
     * @param bVisible 显示工具栏控制标志
     * @param toolBarName 工具栏名称：暂时没用
     */
    public setSpecificToolBarVisible(bVisible: boolean, toolBarName?: string): boolean {
        if (typeof bVisible !== 'boolean') {
            return false;
        }
        return this._host.showToolbar(bVisible, toolBarName);
    }

    public clearUndoList(): void {
        this._documentCore.clearUndoList();
    }

    public async continuePrintDoc(sPrintJson: string): Promise<number> {
        if (!sPrintJson) {
            return ResultType.ParamError;
        }

        if (this.isEmptyDocument()) {
            return ResultType.Failure;
        }

        let option: IContinuePrint;
        try {
            option = JSON.parse(sPrintJson);
        } catch (e) {
            console.warn(e);
            return ResultType.ParamError;
        }

        const {startName, endName, type, needDialog} = option;

        if (!startName) {
            return ResultType.ParamError;
        }
        
        const doc = this._documentCore;
        const regionManeger = doc.getDocument().getRegionManager();
        const startIndexs = regionManeger.getRegionIndexs(startName);
        if (!startIndexs) {
            return ResultType.ParamError;
        }

        if (endName && endName !== startName) {
            const endIndexs = regionManeger.getRegionIndexs(endName);
            if (!endIndexs) {
                return ResultType.ParamError;
            }

            for (let index = 0, startLen = startIndexs.length, endLen = endIndexs.length; index < startLen && index < endLen; index++) {
                if (startIndexs[index] > endIndexs[index]) {
                    return ResultType.ParamError;
                }
            }
        }

        switch (type) {
            case 2: { // C端打印
                const res = await new Promise((resolve) => {
                    const callback = async (result) => {
                        if (result === false) {
                            // resolve promise can cross function layer restriction
                            resolve(ResultType.Failure2);
                        }
                        resolve(ResultType.Success);
                    };
                    const prop: ICPrintData = {
                        printData: null,
                        printerName: option.defaultPrinterName,
                        printCount: 1,
                        printDouble: option.doubleprint !== 0,
                        pageCount: 0,
                        
                        // helper props, not in api
                        printType: 1,
                        pageSize: option.pageSize,
                        startPageHdrFtr: 1 === option.startPageHdrFtr,
                        bDirectPrint: 1 !== needDialog,
                        title: '',
                        clearMode: option.cleanMode != null ? option.cleanMode : CleanModeType.CleanMode,
                        showWatermark: option.showWatermark == null ? true : option.showWatermark,
                        landscape: option.landscape,
                        printOrientPortrait: true,
                        _startName: startName,
                        _endName: endName,
                        callback
                    };
                    if (this._host.isPrintModel()) {
                        this._host.openPrintDialog(prop);
                        return;
                    }
                    // retu
                    // setEvent() can pass method var
                    gEvent.setEvent(this._host.docId, gEventName.DialogEvent, ToolbarIndex.CPrint, { ...prop, callback});
        
                    this.resetAdminMode();
                })
                break;
            }
            default: { // B端打印
                gEvent.setEvent(this._host.docId, gEventName.DialogEvent, ToolbarIndex.ReviewPrint, 1 !== needDialog, option);
            }
        }
    }

    public closePrintPreview(): void {
        gEvent.setEvent(this._host.docId, gEventName.DialogEvent, ToolbarIndex.CloseReviewPrint);
    }


    public printDoc(flag: boolean): void {
        // if (!this._printVm) {
        //     this._printVm = new PrintDialog(this._documentCore);
        // }

        // this._printVm.open(!flag);
        gEvent.setEvent(this._host.docId, gEventName.DialogEvent, ToolbarIndex.ReviewPrint, !flag);
        // this._host.handleRefresh();
        // clearInterval(this._host.timer);
        // this._host.setCursorVisible(false);
    }

    public async directPrintDoc(sPrintJson: string): Promise<number> {
        return new Promise((resolve, reject) => {
            const prop: ICPrintData = {
                printData: null,
                printerName: '',
                printCount: 1,
                printDouble: false,
                pageCount: 0,
                // helper props, not in api
                printType: 1,
                pageSize: null,
                startPageHdrFtr: false,
                bDirectPrint: false,
                title: '',
                clearMode: CleanModeType.CleanMode,
                showWatermark: true,
                landscape: false,
                printOrientPortrait: true,
            };

            if ( sPrintJson) {
                try {
                    const option: IDirectPrintDocJson = JSON.parse(sPrintJson) || {};

                    prop.printerName = option.defaultPrinterName;
                    prop.printCount = option.copies,
                    prop.printDouble = option.doubleprint === undefined ? false : (option.doubleprint !== 0),
                    prop.pageCount = 0,
                        // helper props, not in api
                    prop.printType = option.pageNumberType,
                    prop.startPageHdrFtr = option && 0 !== option.startPageHdrFtr,
                    prop.bDirectPrint = option && 1 !== option.needDialog;
                    prop.title = option && option.title;
                    prop.pageSize = option.pageSize;
                    prop.clearMode = option.cleanMode != null ? option.cleanMode : CleanModeType.CleanMode;

                    if (option.showWatermark != null && typeof option.showWatermark !== 'boolean') {
                        resolve(ResultType.ParamError);
                        return ResultType.ParamError;
                    }
                    prop.showWatermark = option.showWatermark != null ? option.showWatermark : true;

                    // prop.landscape = option.landscape != null ? (option.landscape === 1) : false;
                    prop.landscape = option.printOrientPortrait != null ? !option.printOrientPortrait : false;
                    if (prop.printType === PrintType.Specific) {
                        if (option.pageRange != null && typeof option.pageRange === 'string') {
                            prop.pageRange = option.pageRange;
                        } else {
                            // 设置3的时候 如果没有range 就直接返回
                            return ResultType.ParamError;
                        }
                    }
                } catch (error) {
                    // prop = null;
                }
            }

            if (prop.pageRange != null) {
                const pageCount = this._documentCore.getPageCount();
                const pageRange = prop.pageRange;
                const sepIndex = pageRange.indexOf('-');
                if (sepIndex !== -1) {
                    const pageNumber = +pageRange.slice(0, sepIndex);
                    const latterPageNumber = +pageRange.slice(sepIndex + 1);
                    if (isNaN(pageNumber) || isNaN(latterPageNumber)
                        || pageNumber <= 0 || latterPageNumber <= 0 || latterPageNumber <= pageNumber
                        || pageNumber > pageCount || latterPageNumber > pageCount) {
                        // tslint:disable-next-line: no-console
                        console.warn('pageRange is incorrect, print process interrupted');
                        return ResultType.ParamError;
                    }
                } else {
                    if (isNaN(+pageRange) || +pageRange <= 0 || +pageRange > pageCount) {
                        // tslint:disable-next-line: no-console
                        console.warn('pageRange is incorrect, print process interrupted');
                        return ResultType.ParamError;
                    }
                }
            }

            // console.log(prop)
            if (this.isEmptyDocument()) {
                return ResultType.Failure;
            }

            // if (!this._printVm) {
            //     this._printVm = new PrintDialog(this._documentCore);
            // }

            const callback = async (result) => {
                if (result === false) {
                    // resolve promise can cross function layer restriction
                    resolve(ResultType.Failure2);
                }
                resolve(ResultType.Success);
            };
            if (this._host.isPrintModel()) {
                this._host.openPrintDialog({ ...prop, callback});
                return;
            }
            // retu
            // setEvent() can pass method var
            gEvent.setEvent(this._host.docId, gEventName.DialogEvent, ToolbarIndex.CPrint, { ...prop, callback});

            this.resetAdminMode();
        });
    }

    public printOutpatientDoc(sPrintName: string, printMode: number, pageType: number, firstPageDistance: number,
                              pageMidDistance?: number, lastPageDistance?: number, pageWidth?: number,
                              pageHeight?: number): Promise<boolean> {
        return new Promise((resolve, reject) => {
            sPrintName = sPrintName || 'printDefault';
            if (!sPrintName || pageType === undefined || firstPageDistance === undefined || printMode === undefined) {
                this.resetAdminMode();
                resolve(false);
                return;
            }

            if (typeof printMode !== 'number' || typeof pageType !== 'number' || typeof firstPageDistance !== 'number'
                || !this.isValidNum(pageMidDistance) || !this.isValidNum(lastPageDistance) ||
                !this.isValidNum(pageWidth) || !this.isValidNum(pageHeight)) {
                this.resetAdminMode();
                resolve(false);
                return;
            }

            if (this.isEmptyDocument()) {
                resolve(false);
                return;
            }

            if (!this._printVm) {
                this._printVm = new PrintDialog(this._documentCore);
            }
            const prop: IPrintOutpatient = {
                sPrintName,
                firstPageDistance,
                pageType,
                pageWidth,
                pageHeight,
                pageMidDistance,
                lastPageDistance,
                type: 5,
            };
            gEvent.setEvent(this._host.docId, gEventName.DialogEvent, ToolbarIndex.PrintOutpatient, prop);
            this.resetAdminMode();
            resolve(true);
            // const print = new Print(this._host);
            // this.setPintIframe();
            // print.getPrintOutpatientContent(prop)
            // .then((result) => {
            //
            //     if (!result) {
            //         resolve(false);
            //     }
            //     this.testPrint(result);
            //     resolve(true);
            // })
            // .catch((error) => {
            //
            // });
        });
    }

    public insertTextAtCurrentCursor(sText: string): number {
        if (typeof sText !== 'string') {
            return ResultType.Failure;
        }
        if (!sText || !this._documentCore.canInsertTextInNISTable()) {
            return ResultType.Failure;
        }
        // const para = new Paragraph(this._documentCore.getDocument());
        // const portion = new ParaPortion(para);
        // portion.addText(sText);
        // para.addToContent(0, portion);

        const result = this._documentCore.insertText(skipEscapeString(sText));

        if (result === ResultType.Success) {
            this._host.handleRefresh();
            return ResultType.Success;
        }
        return ResultType.Failure;
    }

    public async insertFileWithString(base64String: string): Promise<number> {
        const date = new Date();
        if (this.isEmptyDocument()) {
            return ResultType.Failure;
        }

        const res = await this.baseInsertFile(base64String, true);

        this.resetAdminMode();
        logger.interface({id: this._host.docId, name: 'insertFileWithString', startTime: date,
            result: res, args: {base64String}});
        return res;
        // const apollo = new ParseApollo();
        // const node = apollo.parse(base64String);
        // const paste = new PasteParagraph(this._documentCore.getDocument());
        // const contents = paste.createNode(node);
        // const result = this._documentCore.insertContent(contents);
        // if (result) {
        //     this._host.handleRefresh();
        //     return ResultType.Success;
        // }
        // return ResultType.Failure;
    }

    public async insertFileWithStream(content: Blob, options?: any | string): Promise<number> {
        const date = new Date();
        if (this.isEmptyDocument()) {
            return ResultType.Failure;
        }

        let insertOption: any = {};
        if (options) {
            if (typeof options === 'string') {
                try {
                    insertOption = JSON.parse(options);
                } catch {
                    ;
                }
            } else {
                insertOption = options;
            }
        }
        const res = await this.baseInsertBlobFile(content, true, insertOption);

        this.resetAdminMode();
        logger.interface({id: this._host.docId, name: 'insertFileWithStream', startTime: date,
            result: res, args: {content}});
        return res;
        // const apollo = new ParseApollo();
        // const node = apollo.parse(base64String);
        // const paste = new PasteParagraph(this._documentCore.getDocument());
        // const contents = paste.createNode(node);
        // const result = this._documentCore.insertContent(contents);
        // if (result) {
        //     this._host.handleRefresh();
        //     return ResultType.Success;
        // }
        // return ResultType.Failure;
    }

     /**
     * 往指定区域的插入一个文件流 专门为sortRegions准备 忽略区域内的保护状态
     * @param strName 区域名称
     * @param cContent 链接文件的base64 数据流
     */

     public async setRegionFileLinkWithStreamForSort(strName: string, cContent: Blob, option?: string): Promise<number> {
        const date = new Date();
        if (!strName || !isValidBlob(cContent)) {
            this.resetAdminMode();
            return ResultType.ParamError;
        }

        let sJson;
        if (option) {
            try {
                sJson = JSON.parse(option);
            } catch (error) {
                this.resetAdminMode();
                return ResultType.ParamError;
            }
        }

        const res = this._documentCore.selectRegionByName(strName);
        if (res !== ResultType.Success) {
            this.resetAdminMode();
            return res;
        }
        const region = this._documentCore.getRegionByName(strName);
        const parentRegion = region.getParentRegion();
        const bParentRegion = !(parentRegion && parentRegion.isRegion());
        const type = bParentRegion ? InsertFilePositionType.ParentRegion : InsertFilePositionType.LeafRegion;
        const options = {
            bSelectedRegionContent: true,
            type,
            bMergeLinkInterface: true,

            retainCascade: sJson?.retainCascade
        };
        
        const logicDocument = this._documentCore.getDocument();
        const bEditorProtect = region.isEditProtect();
        region.setEditProtect(false);

        // this only exists in interface, so not go through documentCore
        let title = region.getTitle();
        let bVisible = region.isShowTitle2();
        if (region.isShowTitle()) {
            logicDocument.removeSelection();

            const para = region.getContent()[0];
            const curPos = para.getCurPos();
            curPos.contentPos = 0;

            region.setTitle('', false);

            this._documentCore.recalculate();
            
            region.selectTopPos();
            region.selectAll();
        }

        logicDocument.setSelectedContentFlag(true);
        const res1 = await this.baseInsertBlobFile2(cContent, true, options);//该函数忽略保护状态
        logicDocument.setSelectedContentFlag(false);

        let res2;
        if (title && bVisible) {
            res2 = region.setTitle(title, bVisible);
        }

        // const res2 = region.updateTitle();
        if (res2 === ResultType.Success) {
            this._documentCore.recalculate();
            this._host.handleRefresh();

        }
        region.setEditProtect(bEditorProtect);

        this.resetAdminMode();
        return res1;
    }

    /**
     * 往指定区域的插入一个文件流
     * @param strName 区域名称
     * @param cContent 链接文件的base64 数据流
     */

    public async setRegionFileLinkWithStream(strName: string, cContent: Blob, option?: string): Promise<number> {
        const date = new Date();
        if (!strName || !isValidBlob(cContent)) {
            this.resetAdminMode();
            return ResultType.ParamError;
        }

        let sJson;
        if (option) {
            try {
                sJson = JSON.parse(option);
            } catch (error) {
                this.resetAdminMode();
                return ResultType.ParamError;
            }
        }

        const res = this._documentCore.selectRegionByName(strName);
        if (res !== ResultType.Success) {
            this.resetAdminMode();
            return res;
        }
        const region = this._documentCore.getRegionByName(strName);
        const parentRegion = region.getParentRegion();
        const bParentRegion = !(parentRegion && parentRegion.isRegion());
        const type = bParentRegion ? InsertFilePositionType.ParentRegion : InsertFilePositionType.LeafRegion;
        const options = {
            bSelectedRegionContent: true,
            type,
            bMergeLinkInterface: true,

            retainCascade: sJson?.retainCascade
        };
        
        const logicDocument = this._documentCore.getDocument();
        const bEditorProtect = region.isEditProtect();
        region.setEditProtect(false);

        // this only exists in interface, so not go through documentCore
        let title = region.getTitle();
        let bVisible = region.isShowTitle2();
        if (region.isShowTitle()) {
            logicDocument.removeSelection();

            const para = region.getContent()[0];
            const curPos = para.getCurPos();
            curPos.contentPos = 0;

            region.setTitle('', false);

            this._documentCore.recalculate();
            
            region.selectTopPos();
            region.selectAll();
        }

        logicDocument.setSelectedContentFlag(true);
        logicDocument.getNewControlManager().setRegionContext(strName);//启动特殊的重命名机制
        const res1 = await this.baseInsertBlobFile(cContent, true, options);
        logicDocument.getNewControlManager().clearRegionContext();//重命名机制结束
        logicDocument.setSelectedContentFlag(false);

        let res2;
        if (title && bVisible) {
            res2 = region.setTitle(title, bVisible);
        }

        // const res2 = region.updateTitle();
        if (res2 === ResultType.Success) {
            this._documentCore.recalculate();
            this._host.handleRefresh();

        }
        region.setEditProtect(bEditorProtect);

        this.resetAdminMode();
        logger.interface({id: this._host.docId, name: 'setRegionFileLinkWithStream', startTime: date,

            result: res, args: [strName, cContent, option]});

        return res1;
    }

    public async mergeDocumentsWithString(base64String: string): Promise<number> {
        if (this.isEmptyDocument()) {
            return ResultType.Failure;
        }
        const date = new Date();

        const res = await this.baseInsertFile(base64String);

        this.resetAdminMode();
        logger.interface({id: this._host.docId, name: 'mergeDocumentsWithString', startTime: date,
            result: res, args: {base64String}});
        return res;
    }

    public async mergeDocumentsWithStream(content: Blob, options?: string): Promise<number> {
        if (this.isEmptyDocument()) {
            return ResultType.Failure;
        }
        let sJson;
        if (options) {
            try {
                sJson = JSON.parse(options);
            } catch (error) {
                this.resetAdminMode();
                return ResultType.ParamError;
            }
        }
        const date = new Date();

        if (!this._documentCore.isCursorInEmptyParagraph()) {
            this.resetAdminMode();
            return ResultType.Invalid;
        }

        const res = await this.baseInsertBlobFile(content, false, 
            {bMergeLinkInterface: true, retainCascade: sJson?.retainCascade});

        this.resetAdminMode();
        logger.interface({id: this._host.docId, name: 'mergeDocumentsWithStream', startTime: date,
            result: res, args: [content, options]});

        return res;
    }

    public setDynamicHeightMode(bShow: boolean): number {
        const curDHMode = this._documentCore.getDynamicHeightMode();
        if (bShow !== curDHMode) {
            this._documentCore.setDynamicHeightMode(bShow);
            this._host.handleRefresh();
            return ResultType.Success;
        } else {
            return ResultType.UnEdited;
        }
    }
    /**
     * 变更Inline模式
     * @param flag 是否启用内联模式
     * @param containerWidth 容器宽度（可选）
     */
     public setInlineMode(flag: boolean, containerWidth?: number): number {
        const curMode = this._documentCore.isInlineMode();
        if (flag !== curMode) {
            this._documentCore.setInlineMode(flag, containerWidth);
            this._host.inlineModeChange(flag);
            this._host.handleRefresh();
            return ResultType.Success;
        } else {
            return ResultType.UnEdited;
        }
    }

    public async replaceSpecificStructPropWithBackString(sCurStructJson: string, sSourceStructJson: string,
                                                         sSourceBase64String: string,
                                                         type: number = 1): Promise<string> {
        if (!sCurStructJson || !sSourceBase64String || !sSourceStructJson) {
            this.resetAdminMode();
            return ResultType.StringEmpty;
        }

        // 将 base64 字符串转换为 Blob
        const uint8Arrs = this.convertStringToUInt8Arrs(sSourceBase64String);
        if (uint8Arrs == null) {
            this.resetAdminMode();
            return ResultType.StringEmpty;
        }
        const content = new Blob([uint8Arrs], { type: 'application/apollo-zip' });

        // 调用 Stream 版本的实现
        return await this.replaceSpecificStructPropWithBackStream(sCurStructJson, sSourceStructJson, content, type);
    }

    /**
     * 替换文档中指定结构的内容
     * @param sCurStructJson 当前文档中目标结构的JSON字符串
     * @param sSourceStructJson 源文档中对应结构的JSON字符串
     * @param content 源文档的Blob内容
     * @param type 结构类型：1=NewControl，2=Region
     * @returns 替换结果的JSON字符串
     */
    public async replaceSpecificStructPropWithBackStream(
        sCurStructJson: string,
        sSourceStructJson: string,
        content: Blob,
        type: number = 1
    ): Promise<string> {
        if (!sCurStructJson || content == null || !sSourceStructJson) {
            this.resetAdminMode();
            return ResultType.StringEmpty;
        }

        const date = new Date();
        let targets: object[];
        let sources: object[];

        // 解析和验证JSON参数
        try {
            targets = JSON.parse(sCurStructJson);
            if (!targets || !targets.length) {
                this.resetAdminMode();
                return ResultType.StringEmpty;
            }
            if (!this.checkedKeys(targets)) {
                this.resetAdminMode();
                return ResultType.StringEmpty;
            }

            sources = JSON.parse(sSourceStructJson);
            if (!sources || !sources.length) {
                this.resetAdminMode();
                return ResultType.StringEmpty;
            }
            if (targets.length !== sources.length) {
                this.resetAdminMode();
                return ResultType.StringEmpty;
            }
            if (!this.checkedKeys(sources)) {
                this.resetAdminMode();
                return ResultType.StringEmpty;
            }
        } catch (error) {
            this.resetAdminMode();
            return ResultType.StringEmpty;
        }

        let res: string[][] = [];

        if (type === 2) {
            // Region 替换
            res = await this.replaceRegionWithTraditionalMethod(targets, sources, content, type);
        } else {
            // NewControl 替换
            res = await this.replaceNewControlWithHybridMethod(targets, sources, content, type);
        }

        let json = ResultType.StringEmpty;
        this.resetAdminMode();
        if (res.length > 0) {
            this._host.handleRefresh();
            json = JSON.stringify(res);
        }
        logger.interface({
            id: this._host.docId,
            name: 'replaceSpecificStructPropWithBackStream',
            startTime: date,
            result: json,
            args: [sCurStructJson, sSourceStructJson, 'content']
        });

        return json;
    }
    
    //为AI抽取结构化文本 by tinyhzi
    public async extractContentForAICorrection(sName: string,nType:number): Promise<string> {
        if(nType === 1 )//region
        {
            return this.saveRegionContentToStreamAI(sName);

        }else if( nType === 2)//struct
        {
            return this.saveStructContentToStreamAI(sName);
        }       
    }

    public async replaceContentWithAICorrection(sName:string,sOriginalContent:string,nType:number): Promise<number> {
          if (!sName || !sOriginalContent) {
            this.resetAdminMode();
            return ResultType.ParamError;
          }
        const sContent: string = sOriginalContent;
      
        const jsonArray = [{ "AI-id": sName }];
        const sCurStructJson = JSON.stringify(jsonArray);
        const sources = JSON.parse(sCurStructJson);
        const result: any[][] = await this.getSpecificStructContentWithBackStreamAI(sources, sContent, nType);
      
        if (!result || !result.length) {
            this.resetAdminMode();
            return ResultType.Failure;
          }
      
        const obj = result.find((item) => item);
        if (!obj) {
            this.resetAdminMode();
            return ResultType.Failure;
        } else {
          const doc = this._documentCore.getDocument();
            // tslint:disable-next-line: prefer-for-of
            for (let index = 0; index < result.length; index++) {
                const element = result[0];
                if (element) {
                    element.forEach((item) => {
                        if (item) {
                            item.setLogicDocument(doc);

                            if (item.isRegion() && !!item.content) {
                                const contents = item.content;
                                contents.setLogicDocument(doc);
                                contents.content?.forEach((item2) => {
                                    if (item2) {
                                        item2.setLogicDocument(doc);
            }
          });
                            }
                        }
                    });
                }
            }
        }
        let res: string[][];
        if (nType === 1) {
            res = this._documentCore.replaceRegionTexts(sources, result);
        } else {
            res = this._documentCore.replaceNewControlTexts(sources, result);
        }
      
        let json = ResultType.StringEmpty;
        this.resetAdminMode();
          if (res.length > 0) {
            this._host.handleRefresh();
            json = JSON.stringify(res);
          }
      
        return  ResultType.Success;
      }

    /**
     * 从源 Blob 中筛选特定结构并生成新的 Blob
     * @param sourceBlob 源文档的 Blob 内容
     * @param filterCriteria 筛选条件数组
     * @param targetName 目标结构名称（筛选出的结构将使用此名称）
     * @param type 结构类型：1=NewControl(sdt)，2=Region(section)
     * @returns 筛选后的新 Blob
     */
    public async generateFilteredBlob(
        sourceBlob: Blob,
        filterCriteria: any[],
        targetName: string,
        type: number = 1
    ): Promise<Blob | null> {
        try {
            // 1. 分离文件头和内容
            const fileReader = new FileReader();
            const reader = new Reader(this._documentCore.getDocument());

            return new Promise((resolve, reject) => {
                // 先绑定事件处理器，再调用 readAsArrayBuffer
                fileReader.onloadend = async () => {
                    try {
                        const rawBuffer = fileReader.result as ArrayBuffer;
                        if (!rawBuffer) {
                            resolve(null);
                            return;
                        }

                        const { headerBuffer, contentBuffer } = reader.separateRawBuffer(new Uint8Array(rawBuffer));
                        const { versionNum } = reader.getHeaderBufferItems(headerBuffer);

                        // 2. 解压内容 Blob
                        const contentBlob = new Blob([contentBuffer], { type: 'application/apollo-zip' });
                        const zip = new JSZip();
                        const loadedZip = await zip.loadAsync(contentBlob);

                        // 3. 提取所有文件
                        const files: { [key: string]: string } = {};
                        for (const [filename, file] of Object.entries(loadedZip.files)) {
                            if (!(file as any).dir) {
                                files[filename] = await (file as any).async('string');
                            }
                        }

                        // 4. 筛选 document.xml 内容
                        const originalDocumentXml = files['Document.xml'];
                        if (!originalDocumentXml) {
                            throw new Error('Document.xml not found');
                        }

                        const filteredDocumentXml = this.filterDocumentXml(originalDocumentXml, filterCriteria, targetName, type);
                        if (!filteredDocumentXml) {
                            resolve(null); // 没有匹配的内容
                            return;
                        }                     
                        // 5. 重新打包成新的 Blob
                        const newBlob = await this.repackageFilteredBlob(files, filteredDocumentXml, headerBuffer);
                        resolve(newBlob);

                    } catch (error) {
                        resolve(null);
                    }
                };

                fileReader.onerror = () => {
                    resolve(null);
                };

                // 最后调用 readAsArrayBuffer
                fileReader.readAsArrayBuffer(sourceBlob);
            });

        } catch (error) {
            return null;
        }
    }

    /**
     * 筛选 document.xml 内容，只保留匹配的结构
     * @param documentXml 原始 document.xml 内容
     * @param filterCriteria 筛选条件
     * @param targetName 目标结构名称
     * @param type 结构类型
     * @returns 筛选后的 document.xml 内容
     */
    private filterDocumentXml(documentXml: string, filterCriteria: any[], targetName: string, type: number): string | null {
        try {
            // 解析 XML
            const parser = new DOMParser();
            const xmlDoc = parser.parseFromString(documentXml, 'text/xml');

            // 检查解析错误
            const parseError = xmlDoc.getElementsByTagName('parsererror');
            if (parseError.length > 0) {
                return null;
            }

            // 获取 body 节点
            const bodyNode = xmlDoc.getElementsByTagName('w:body')[0];
            if (!bodyNode) {
                return null;
            }

            // 创建新的 body 节点
            const newBodyNode = xmlDoc.createElement('w:body');

            // 确定要查找的标签名
            const targetTags = type === 2 ? ['section'] : ['sdt', 'section'];

            // 查找所有段落节点
            const paragraphs = bodyNode.getElementsByTagName('w:p');

            const matchedParagraphs: Element[] = [];

            for (let i = 0; i < paragraphs.length; i++) {
                const paragraph = paragraphs[i];               

                const filteredParagraph = this.filterParagraphContent(paragraph, filterCriteria, targetTags, targetName);

                if (filteredParagraph) {
                    matchedParagraphs.push(filteredParagraph);
				}
            }
            // 如果没有匹配的段落，返回 null
            if (matchedParagraphs.length === 0) {
                return null;
            }

            // 将匹配的段落添加到新的 body 中
            matchedParagraphs.forEach(p => newBodyNode.appendChild(p));

            // 保留 sectPr 节点（节属性）
            const sectPrNodes = bodyNode.getElementsByTagName('w:sectPr');
            if (sectPrNodes.length > 0) {
                newBodyNode.appendChild(sectPrNodes[0].cloneNode(true));
            }

            // 替换原来的 body
            const documentNode = xmlDoc.getElementsByTagName('w:document')[0];
            documentNode.replaceChild(newBodyNode, bodyNode);

            // 序列化回 XML 字符串
            const serializer = new XMLSerializer();
            const result = serializer.serializeToString(xmlDoc);
            return result;

        } catch (error) {
            return null;
        }
    }

    /**
     * 筛选段落内容，只保留匹配的结构
     * @param paragraph 段落节点
     * @param filterCriteria 筛选条件
     * @param targetTags 目标标签名数组
     * @returns 筛选后的段落节点，如果没有匹配内容则返回 null
     */
    private filterParagraphContent(paragraph: Element, filterCriteria: any[], targetTags: string[], targetName: string): Element | null {
        const xmlDoc = paragraph.ownerDocument;
        const matchedElements: Element[] = [];

        

        // 查找段落中的所有目标标签
        targetTags.forEach(tagName => {
            const elements = paragraph.getElementsByTagName(tagName);
           

            for (let i = 0; i < elements.length; i++) {
                const element = elements[i];
                const elementName = element.getAttribute('name');
                // 检查是否包含匹配的 customProperty
                const customProps = element.getElementsByTagName('customProperty');
                for (let j = 0; j < customProps.length; j++) {
                    const customProp = customProps[j];
                    // 检查是否匹配筛选条件
                    const isMatched = filterCriteria.some(criteria => {
                        // 检查 name 匹配
                        if (criteria.name && criteria.name === elementName) {                         
                            return true;
                        }
                        // 检查 propName 和 propValue 匹配
                        if (criteria.propName && criteria.propValue) {
                            // 查找匹配的 propName 子元素
                            const propElements = customProp.getElementsByTagName(criteria.propName);
                            for (let k = 0; k < propElements.length; k++) {
                                const propElement = propElements[k];
                                const propValue = propElement.textContent || propElement.innerHTML;
                                if (propValue === criteria.propValue) {                             
                                    return true;
                                }
                            }
                        }
                        // 检查 customProperty 中的直接属性匹配
                        // 遍历筛选条件中的所有属性
                        for (const [propName, propValue] of Object.entries(criteria)) {
                            if (propName === 'name') continue; // name 已经在上面处理过了
                            // 在 customProperty 中查找对应的标签
                            const propElements = customProp.getElementsByTagName(propName);
                            for (let k = 0; k < propElements.length; k++) {
                                const propElement = propElements[k];
                                const actualValue = propElement.textContent || propElement.innerHTML;
                                if (actualValue === propValue) {                             
                                    return true;
                                }
                            }
                        }
                        return false;
                    });

                    if (isMatched) {
                        const clonedElement = element.cloneNode(true) as Element;
                        // 替换 name 属性为目标名称
                        clonedElement.setAttribute('name', targetName);
                        matchedElements.push(clonedElement);
                        break;
                    }
                }
            }
        });
        // 如果没有匹配的元素，返回 null
        if (matchedElements.length === 0) {
            return null;
        }
        // 创建新的段落节点
        const newParagraph = xmlDoc.createElement('w:p');
        // 保留段落属性 (w:pPr)
        const pPrNodes = paragraph.getElementsByTagName('w:pPr');
        if (pPrNodes.length > 0) {
            newParagraph.appendChild(pPrNodes[0].cloneNode(true));      
        }
        // 添加匹配的元素
        matchedElements.forEach(element => {
            newParagraph.appendChild(element);
        });      
        return newParagraph;
    }

    /**
     * 重新打包筛选后的内容为 Blob
     * @param originalFiles 原始文件内容
     * @param filteredDocumentXml 筛选后的 document.xml
     * @param headerBuffer 原始文件头
     * @returns 新的 Blob
     */
    private async repackageFilteredBlob(
        originalFiles: { [key: string]: string },
        filteredDocumentXml: string,
        headerBuffer: Uint8Array
    ): Promise<Blob> {
        // 创建新的 ZIP
        const newZip = new JSZip();

        // 添加所有原始文件，但替换 Document.xml
        for (const [filename, content] of Object.entries(originalFiles)) {
            if (filename === 'Document.xml') {
                newZip.file(filename, filteredDocumentXml);
            } else {
                newZip.file(filename, content);
            }
        }

        // 生成 ZIP 内容
        const zipContent = await newZip.generateAsync({
            type: 'uint8array',
            compression: 'DEFLATE',
            compressionOptions: { level: 6 }
        });

        // 添加原始文件头
        const totalLength = headerBuffer.length + zipContent.length;
        const finalBuffer = new Uint8Array(totalLength);

        finalBuffer.set(headerBuffer, 0);
        finalBuffer.set(zipContent, headerBuffer.length);

        return new Blob([finalBuffer], { type: 'application/apollo-zip' });
    }

    public setPrinterServerUrl(url: string): number {
        if (url == null || url.length === 0) {
            return ResultType.Failure;
        }
        this._documentCore.setPrinterServerUrl(url);
        return ResultType.Success;
    }

    public forbidMoveTableBorder(bFixed: boolean): number {
        if (null == bFixed || typeof bFixed !== 'boolean') {
            return ResultType.ParamError;
        }

        if ( bFixed === this._documentCore.isForbidMoveTableBoder() ) {
            return ResultType.UnEdited;
        }

        this._documentCore.forbidMoveTableBorder(bFixed);
        return ResultType.Success;
    }

    public checkFirstTableRowHeightInHeader(sTableName: string): number {
        let result = ResultType.Success;
        if (sTableName) {
            const table = this._documentCore.getTableByName(sTableName);
            if (table) {
                result = table.checkFirstTableRowHeight();
            } else {
                return ResultType.ParamError;
            }
        } else {
            const header = this._documentCore.getHeaderFooter(true);
            const contents = (header && header.content && header.content.content);
            if (contents) {
                for (const element of contents) {
                    if (element && element.isTable()) {
                        if (ResultType.Invalid === element.checkFirstTableRowHeight()) {
                            return ResultType.Invalid;
                        }
                    }
                }
            }
        }

        return result;
    }

    public setDynamicGridLine(bEnable: boolean, sParam: string): number {
        let color = 'blue';
        try {
            if (sParam) {
                const param = JSON.parse(sParam);
                if (param && isValidORGBColor(param.color)) {
                    color = param.color;
                } else {
                    return ResultType.ParamError;
                }
            }
        } catch (error) {
            return ResultType.ParamError;
        }

        const doc = this._documentCore.getDocument();

        const res = doc.setDynamicGridLine(bEnable, color);

        if (ResultType.Success === res) {
            this._host.handleRefresh();
        }
        return res;
    }

    public enableSouceBindInRegion(sRegion:string,nControl:number):number{
        return this._documentCore.enableSouceBindInRegion(sRegion,nControl);
    }

    public getSourceBindJson(): string {
        return this._documentCore.getSourceBindJson();
    }

    public setSourceBindJson(sJson: string): number {
        if (!sJson || typeof sJson !== 'string') {
            return ResultType.ParamError;
        }

        const datas = JSON.parse(sJson);
        if (!datas || typeof datas !== 'object') {
            return ResultType.ParamError;
        }

        let res = ResultType.UnEdited;
        let bRefresh = false;
        const document = this._documentCore.getDocument();
        const newControlManager = document.getNewControlManager();
        const drawingObjects = document.getDrawingObjects();
        const sourceBindMap = document.getSourceBindMap();

        for (const obj in datas) {
            if (obj && sourceBindMap.has(obj)) {
                const data = datas[obj];
                if (!data || typeof data !== 'object') {
                    return ResultType.ParamError;
                }

                const binds = sourceBindMap.get(obj);

                for (let index = 0, length = binds.length; index < length; index++) {
                    const element = binds[index];
                    const newControl = newControlManager.getNewControlByName(element?.name);

                    if (newControl && data.hasOwnProperty(element.sourceKey)) {
                        const sourceKey = data[element.sourceKey];
                        const bReadOnly = data[element.bReadOnly];
                        const commandUpdate = data[element.commandUpdate];

                        res = newControl.setSourceDataBind({
                            sourceObj: obj,
                            sourceKey: element.sourceKey,
                            bReadOnly: bReadOnly ? bReadOnly : element.bReadOnly,
                            commandUpdate: commandUpdate ? commandUpdate : element.commandUpdate,
                        });

                        if (1 === element.commandUpdate && !(element.bReadOnly && newControl.isEditProtect())) {
                            let res2;
                            if (newControl.isNumberBox()) {
                                res2 = newControl.setNewControlTextByJson({name: element.name,
                                                        content_text: sourceKey});
                                if (ResultType.Success === res2) {
                                    document.recalculate();
                                    document.updateCursorXY();
                                }
                            } else {
                                res2 = newControl.setNewControlText(sourceKey);
                            }
                            bRefresh = (ResultType.Success === res2 || bRefresh);
                            res = ResultType.Success;
                        }
                    } else if (!newControl) {
                        const barcode = drawingObjects.getDrawingByName(element?.name) as any;
                        if (barcode?.setSourceDataBind && data.hasOwnProperty(element.sourceKey)) {
                            const sourceKey = data[element.sourceKey];
                            const bReadOnly = data[element.bReadOnly];
                            const commandUpdate = data[element.commandUpdate];

                            res = barcode.setSourceDataBind({
                                sourceObj: obj,
                                sourceKey: element.sourceKey,
                                bReadOnly: bReadOnly ? bReadOnly : element.bReadOnly,
                                commandUpdate: commandUpdate ? commandUpdate : element.commandUpdate,
                            });

                            if (1 === element.commandUpdate && barcode.content !== sourceKey) {
                                barcode.content = sourceKey;
                                barcode.updateBarcode();
                                bRefresh = true;
                                res = ResultType.Success;
                            }
                        }
                    }
                }
            }
        }

        if (bRefresh) {
            const newControlManager = this._documentCore.getDocument().getNewControlManager();
            newControlManager.updateAllCascades();
            this._host.handleRefresh();
        }

        return res;
    }

    public async saveNISTableFile(): Promise<any[]> {
        const date = new Date();
        const promises: any[] = [];
        const arrs: any[] = [];
        const formatWriter = new FormatWriter();
        const xmlProps: any = {
            documentCore: this._documentCore,
            properties: this.getCustomPropertiesInSettings(),
            selectedArea: null,
        };

        const sModJson = {
            nisTable: []};
        const blob = await formatWriter.generateBlob(xmlProps, false, false, {}, sModJson);
        promises.push(blob);
        promises.push(JSON.stringify(sModJson.nisTable));

        // let results;
        // try {
        //     results = await Promise.all(promises);
        // } catch (error) {
        //     this.resetAdminMode();
        //     return null;
        // }
        // const map = new Map();
        // arrs.forEach((name, index) => {
        //     map.set(name.name, results[index]);
        // });
        this.resetAdminMode();
        logger.interface({id: this._host.docId, name: 'saveStructContentToStreamByArray', startTime: date,
            result: ResultType.Success, args: []});
        return promises;
    }

    /**
     * 将后台base64流文档中reserve属性名（该属性值为eye）的结构的内容替换到前台文档结构中（该结构也有reserve属性，并且该属性值也为eye）.
     * 后台base64流文档如果存在多个reserve属性名（该属性值为eye）的结构，以第一个为准。前台文档中如果存在多个reserve属性名（该属性值为eys）的结构，则所有的结构内容都会被替换
     * @param sSourceStructJson 其中的type暂时没有作用
     * @param sSourceBase64String /
     * @param type 1: 全部为结构化元素 2: 全部为区域 即：不存在sSourceStructJson既有结构化又有区域的情况
     */
    private getSpecificStructContentWithBackString(sSourceStructJson: any[],
                                                   sSourceBase64String: string,
                                                   type: number = 1): Promise<any[]> {
        const uint8Arrs = this.convertStringToUInt8Arrs(sSourceBase64String);
        if (uint8Arrs == null) {
            return null;
        }

        const reader = new Reader(this._documentCore.getDocument());

        return new Promise((resolve) => {
            // tslint:disable-next-line: newline-per-chained-call
            reader.getFileXMLFromString(uint8Arrs).then((fileXML: string) => {
                const result = this.prepareContentTextArray(fileXML, sSourceStructJson, type);
                resolve(result);
            });
        });
    }

    /**
     * 将后台base64流文档中reserve属性名（该属性值为eye）的结构的内容替换到前台文档结构中（该结构也有reserve属性，并且该属性值也为eye）.
     * 后台base64流文档如果存在多个reserve属性名（该属性值为eye）的结构，以第一个为准。前台文档中如果存在多个reserve属性名（该属性值为eys）的结构，则所有的结构内容都会被替换
     * @param sSourceStructJson 其中的type暂时没有作用
     * @param content /
     * @param type 1: 全部为结构化元素 2: 全部为区域 即：不存在sSourceStructJson既有结构化又有区域的情况
     */
    private getSpecificStructContentWithBackStream(sSourceStructJson: any[],
                                                   content: Blob,
                                                   type: number = 1): Promise<any[]> {
        return new Promise((resolve) => {
            const doc = this._documentCore.createNewEmptyDocumentForInteface(true);
            const bRevision = this._documentCore.isTrackRevisions();
            // const document = this._documentCore.getDocument
            if (bRevision) {
                doc.setTrackRevisions(true);
                doc.setRevision(this._documentCore.getCurRevisionSetting());
            }

            doc.setRegionContentDefaultFont(this._documentCore.getDocument()
                                                                .getRegionContentDefaultFont());
            const reader = new Reader();
            // tslint:disable-next-line: newline-per-chained-call

            reader.getFileXMLFromBlob(content, APO_XMLS_ARRAY.slice(0, 2)).then((res) => {
                if (!res?.fileXML.length) {
                    resolve([]);
                }

                doc.setDocumentVersion(res.versionNum);
                const result = this.prepareContentArray(res.fileXML, sSourceStructJson, type, doc);

                // const result = this.prepareContentTextArray(fileXML, sSourceStructJson, type);
                resolve(result);
            });
        });
    }

        /**
     * 优化版：将后台Blob文档中的结构内容提取出来，使用优化的XML节点查找算法
     * @param sSourceStructJson 源结构JSON
     * @param content Blob内容
     * @param type 类型：1=结构化元素，2=区域
     */
    private getSpecificStructContentWithBackStreamOptimized(sSourceStructJson: any[],
        content: Blob,
        type: number = 1): Promise<any[]> {
        return new Promise((resolve) => {
            const doc = this._documentCore.createNewEmptyDocumentForInteface(true);
            const bRevision = this._documentCore.isTrackRevisions();

            if (bRevision) {
                doc.setTrackRevisions(true);
                doc.setRevision(this._documentCore.getCurRevisionSetting());
            }

            doc.setRegionContentDefaultFont(this._documentCore.getDocument()
                    .getRegionContentDefaultFont());
            const reader = new Reader();

            reader.getFileXMLFromBlob(content, APO_XMLS_ARRAY.slice(0, 2)).then((res) => {
                if (!res?.fileXML.length) {
                    resolve([]);
                    return;
                }

                doc.setDocumentVersion(res.versionNum);
                const result = this.prepareContentArrayOptimized(res.fileXML, sSourceStructJson, type, doc);
                resolve(result);
            });
        });
    }

    /**
    * 优化版：使用优化的XML节点查找算法准备内容数组
    */
    private prepareContentArrayOptimized(fileXML: string[], sSourceStructJson: any[], type: number, doc: any): any[][] {
        const tagNames: string[] = [];
        if (type === 1) {
            tagNames.push('sectionStart', 'section', 'sdt');
        } else {
            tagNames.push('rg');
        }

        // 使用优化版ParseXml类
        const optimizedParser = new OptimizedParseXml(fileXML[0]);
        const nodes: any[][] = optimizedParser.filterNodesByCustomPropsOptimized(tagNames, sSourceStructJson);

        if (!nodes || !nodes.length) {
            return;
        }
        if (!nodes.find((item) => item != null)) {
            return;
        }

        // 使用优化版ParseContent类
        const optimizedContent = new OptimizedParseContent(doc, fileXML[1]);
        const result: any[][] = optimizedContent.parseOptimized(nodes);

        const document = this._documentCore.getDocument();
        const manager = document.getNewControlManager();
        manager.clearPasteControls();
        manager.addPasteControls(doc.getNewControlManager()
        .getInsertNewControlCaches());
        const drawObj = document.getDrawingObjects();
        const maps = doc.getDrawingObjects()
        .getGraphicObject();
        for (const [name, drawing] of maps) {
            drawObj.addInsertGraphicObject(drawing);
        }

        return result;
    }

    /**
    * 优化版：使用优化的XML节点查找算法替换特定结构属性
    * 此函数与原始replaceSpecificStructPropWithBackStream功能相同，但使用了优化的XML解析
    */
    public async replaceSpecificStructPropWithBackStreamOptimized(
        sCurStructJson: string, sSourceStructJson: string,
        content: Blob, type: number = 1): Promise<string> {
        if (!sCurStructJson || content == null || !sSourceStructJson) {
            this.resetAdminMode();
            return ResultType.StringEmpty;
        }
        const date = new Date();
        let targets: object[];
        let sources: object[];
        try {
            targets = JSON.parse(sCurStructJson);
            if (!targets || !targets.length) {
                this.resetAdminMode();
                return ResultType.StringEmpty;
            }
            if (!this.checkedKeys(targets)) {
                this.resetAdminMode();
                return ResultType.StringEmpty;
            }
            sources = JSON.parse(sSourceStructJson);
            if (!sources || !sources.length) {
                this.resetAdminMode();
                return ResultType.StringEmpty;
            }
            if (targets.length !== sources.length) {
                this.resetAdminMode();
                return ResultType.StringEmpty;
            }
            if (!this.checkedKeys(sources)) {
                this.resetAdminMode();
                return ResultType.StringEmpty;
            }
        } catch (error) {
            this.resetAdminMode();
            return ResultType.StringEmpty;
        }

    // 使用优化版函数获取内容
    const result: any[][] = await this.getSpecificStructContentWithBackStreamOptimized(sources, content, type);
    if (!result || !result.length) {
        this.resetAdminMode();
        return ResultType.StringEmpty;
    }

    // 处理替换内容
    let res: string[][];
    if (type === 2) {
        res = this._documentCore.replaceRegionTexts(targets, result);
    } else {
        res = this._documentCore.replaceNewControlTexts(targets, result);
    }

    // 界面刷新
    this._host.handleRefresh();

    // 记录日志
    logger.interface({id: this._host.docId, name: 'replaceSpecificStructPropWithBackStream', startTime: date,
        result: JSON.stringify(res), args: [sCurStructJson, sSourceStructJson, 'content']});

    return JSON.stringify(res);
    }

    private prepareContentArray(fileXML: string[], sSourceStructJson: any[], type: number, doc: any): any[][] {
        // console.time('测试时间2');
        const tagNames: string[] = [];
        if (type === 1) {

            tagNames.push('sectionStart', 'section','sdt');

        } else {
            tagNames.push('rg');
        }
        const nodes: any[][] = new ParseXml(fileXML[0])
        .filterNodesByCustomProps(tagNames, sSourceStructJson);
        if (!nodes || !nodes.length) {
            return;
        }
        if (!nodes.find((item) => item != null)) {
            return;
        }
        const result: any[][] = new ParseContent(doc, fileXML[1]).parse(nodes);
        // console.timeEnd('测试时间2');
        // console.log(nodes, result);
        const document = this._documentCore.getDocument();
        const manager = document.getNewControlManager();
        manager.clearPasteControls();
        manager.addPasteControls(doc.getNewControlManager()
                                    .getInsertNewControlCaches());
        const drawObj = document.getDrawingObjects();
        const maps = doc.getDrawingObjects()
        .getGraphicObject();
        for (const [name, drawing] of maps) {
            drawObj.addInsertGraphicObject(drawing);
        }

        // 清理临时文档的上下文
        const tempNewControlManager = doc.getNewControlManager();
        tempNewControlManager.clearRegionContext();

        return result;
    }
    
    private getSpecificStructContentWithBackStreamAI(
        sSourceStructJson: any[],
        content: string,
        type: number
    ): Promise<any[]> {
        return new Promise((resolve) => {
            const doc = this._documentCore.createNewEmptyDocumentForInteface(true);
            const bRevision = this._documentCore.isTrackRevisions();
    
            if (bRevision) {
                doc.setTrackRevisions(true);
                doc.setRevision(this._documentCore.getCurRevisionSetting());
            }
            
            doc.setRegionContentDefaultFont(this._documentCore.getDocument().getRegionContentDefaultFont());
    
            // content 现在是字符串，直接使用
            const xmlString = content; 
            const versionNum = "112"; // 直接使用 "112"

            // 构造字符串数组，第一个元素是转换后的 XML 字符串，第二个元素是空字符串
            const fileXMLArray: string[] = [xmlString, ""];

            // 调用 prepareContentArray 方法 region--1 struct --2
            const result = this.prepareContentArrayAI(fileXMLArray,sSourceStructJson, type, doc);
            resolve(result);
        });
    }

    private prepareContentArrayAI(fileXML: string[], sSourceStructJson: any[],type: number, doc: any): any[][] {
        const tagNames: string[] = [];
        if (type === 2) {
            tagNames.push('sectionStart', 'section', 'sdt');
        } else {
            tagNames.push('rg');
        }
    
        const nodes: any[][] = new ParseXml(fileXML[0])
        .filterNodesByCustomProps(tagNames, sSourceStructJson);
    
        if (!nodes || !nodes.length) {
            return;
        }
        if (!nodes.find((item) => item != null)) {
            return;
        }
    
        const result: any[][] = new ParseContent(doc, fileXML[1]).parse(nodes);
    
        const document = this._documentCore.getDocument();
        const manager = document.getNewControlManager();
        manager.clearPasteControls();
        manager.addPasteControls(doc.getNewControlManager().getInsertNewControlCaches());
    
        const drawObj = document.getDrawingObjects();
        const maps = doc.getDrawingObjects().getGraphicObject();
        for (const [name, drawing] of maps) {
            drawObj.addInsertGraphicObject(drawing);
        }
    
        return result;
    }

    private prepareContentTextArray(fileXML: string, sSourceStructJson: IStructJson[],
                                    type: number = 1): IStructContent[] {

        // console.log(fileXML)
        const parser = new DOMParser();
        const xmlDoc = parser.parseFromString(fileXML, 'text/xml');

        const retrievedPropNames = [];
        for (const structProps of sSourceStructJson) {
            if (structProps.propName != null) {
                // find custom prop child elems as required
                const propElems = xmlDoc.getElementsByTagName(structProps.propName);
                // console.log(propElems) // always return array even nothing got
                if (propElems.length > 0) {
                    for (let i = 0; i < propElems.length; i++) {
                        if (structProps.propValue === propElems[i].innerHTML) {
                            retrievedPropNames.push(propElems[i]);
                            // only save the first required struct
                            break;
                        }
                        if (i === propElems.length - 1) { // no required struct found
                            retrievedPropNames.push(null);
                        }
                    }
                } else {
                    retrievedPropNames.push(null);
                }
            }
        }
        // console.log(retrievedPropNames)

        const result: IStructContent[] = [];
        if (retrievedPropNames.length > 0) {
            for (const retrievedPropName of retrievedPropNames) {
                if (retrievedPropName != null) {
                    let curTextContent = '';

                    if (type === 1) {
                        const sdtStart = retrievedPropName.parentNode.parentNode;
                        // console.log(sdtStart.nodeName)
                        // console.dir(sdtStart)
                        if (sdtStart.nodeName !== 'sdtStart') {
                            // type === 1 should only contain structs
                            continue;
                        }

                        const structName = sdtStart.attributes.getNamedItem('name').nodeValue;
                        let node = sdtStart;
                        let safeCount = 0;

                        // may have recursive structs
                        while (node != null) {
                            if (node.nodeName === 'w:r') {
                                // console.log(node);
                                const runTexts = node.getElementsByTagName('w:t');
                                if (runTexts.length > 0) {
                                    // for (let i = 0; i < runTexts.length; i++) {
                                    for (const runText of runTexts) {
                                        curTextContent += runText.innerHTML;
                                        // if (i !== 0) {
                                        //     curTextContent += ' ';
                                        // }
                                    }
                                } else {
                                    // result.push({contentText: null});
                                }
                            }

                            if (node.nodeName === 'sdtEnd') { // travsered this whole struct
                                if (node.attributes.getNamedItem('name').nodeValue === structName) {
                                    break; // usually should break out before reach .nextsibling null
                                }
                            }

                            // not found sdtEnd till paraEnd, means cross para
                            if (node.nextSibling == null) {
                                let parentNode = node.parentNode;
                                if (parentNode.nodeName === 'w:p') { // only consider <w:p> for now
                                    parentNode = parentNode.nextSibling;
                                    if (parentNode.children.length > 0) {
                                        node = parentNode.children[0];
                                    }
                                }
                            } else {
                                node = node.nextSibling;
                            }

                            safeCount++;
                            if (safeCount > LOOP_THRESHOLD) {
                                // tslint:disable-next-line: no-console
                                console.warn('getSpecificStructContentWithBackString: max loop reached');
                            }
                        }
                    } else if (type === 2) {
                        const regionNode = retrievedPropName.parentNode.parentNode.parentNode;
                        // console.log(sdtStart.nodeName)
                        // console.dir(sdtStart)
                        if (regionNode.nodeName !== 'rg') {
                            // type === 1 should only contain structs
                            result.push({contentText: null});
                            continue;
                        }
                        // let node = regionNode.firstElementChild;
                        const node = regionNode;
                        // let safeCount = 0;
                        // console.dir(regionNode)
                        // console.log(regionNode.firstElementChild)
                        // may have recursive elements
                        // while (node != null) {
                        const potentialRuns = node.getElementsByTagName('w:r');
                        if (potentialRuns.length > 0) {
                            for (const run of potentialRuns) {
                                const runTexts = run.getElementsByTagName('w:t');
                                if (runTexts.length > 0) {
                                    // for (let i = 0; i < runTexts.length; i++) {
                                    for (const runText of runTexts) {
                                        curTextContent += runText.innerHTML;
                                        // if (i !== 0) {
                                        //     curTextContent += ' ';
                                        // }
                                    }
                                } else {
                                    // result.push({contentText: null});
                                }
                            }
                        }

                            // node = node.nextSibling;

                            // safeCount++;
                            // if (safeCount > LOOP_THRESHOLD) {
                            //     // tslint:disable-next-line: no-console
                            //     console.warn('getSpecificStructContentWithBackString: max loop reached');
                            // }
                        // }
                    } else {
                        // tslint:disable-next-line: no-console
                        console.warn('backstream type is unknown');
                    }

                    // "one retrievedPropName corresponds to one result" feels more right
                    result.push({contentText: curTextContent});

                } else {
                    result.push({contentText: null});
                }
            }
        }

        // console.log(result);
        return result;
    }

    private checkedKeys(arrs: object[]): boolean {
        for (let arrIndex = 0, len = arrs.length; arrIndex < len; arrIndex++) {
            const obj = arrs[arrIndex];
            const keys = Object.keys(obj);
            if (keys.length === 0) {
                return false;
            }
            for (let index = 0, length = keys.length; index < length; index++) {
                if ( typeof obj[keys[index]] !== 'string') {
                    return false;
                }
            }
        }

        return true;
    }

    private getCustomPropertiesInSettings(): Map<string, any> {
        // TODO: some logic to get "业务属性"
        const properties = new Map();
        // example data
        // properties.set('apple', 15);
        // properties.set('soul', 'dark');
        // properties.set('fire', 'fading');
        // properties.set('lords', 'throneless');
        return properties;
    }

    private async baseInsertFile(base64String: string, bNoEndPara: boolean = false): Promise<number> {
        if (!base64String || this._documentCore.isProtectedMode()) {
            return ResultType.Failure;
        }

        const type = this._documentCore.getInsertFilePositionType();

        if (/*InsertFilePositionType.HeaderFooter === type
            ||*/ InsertFilePositionType.Selection === type
            || InsertFilePositionType.OtherNewControlsExceptTextAndSection === type
            || false === this._documentCore.canInput() ) {
            return ResultType.Failure;
        }

        let arrs: string[];
        try {
            arrs = window.atob(base64String)
            .split(',');
        } catch (err) {
            return ResultType.Failure;
        }

        if (arrs.length === 0) {
            return ResultType.Failure;
        }

        const len = arrs.length;
        const uint8Arrs = new Uint8Array(len);
        for (let index = 0; index < len; index++) {
            const num = +arrs[index];
            if (isNaN(num)) {
                return ResultType.Failure;
            }
            uint8Arrs[index] = num;
        }

        const reader = new Reader(this._documentCore.getDocument());
        let result: any;
        try {
            result = await reader.readFromString(uint8Arrs, true, bNoEndPara, true, type);
        } catch (e) {
            return e;
        }

        if (result === ResultType.Success) {
            this._host.handleRefresh();
            // setTimeout(() => {
            //     this._host.testDocumentXml();
            // }, 10);
        }

        if (this._host.externalEvent) {
            this._host.externalEvent.nsoFileOpenCompleted(base64String);
        }

        return result;
    }

    private async baseInsertBlobFile(content: Blob, bNoEndPara: boolean = false,
                                     options: any = {}): Promise<number> {
        if (content == null || this._documentCore.isProtectedMode()
            || this._documentCore.isInNumberCellOfNISTable()
            || this._documentCore.isInNISTable()) {
            if (this.isInNISCellText() === false) {
                return ResultType.Failure;
            }
        }

        const bSelectedRegionContent = (options ? true === options.bSelectedRegionContent : false);
        const type = bSelectedRegionContent ? options.type : this._documentCore.getInsertFilePositionType();
        const bSelect = (bSelectedRegionContent
                    && (InsertFilePositionType.ParentRegion === type || InsertFilePositionType.LeafRegion === type))
                    ? false : (InsertFilePositionType.Selection === type);

        if (/*InsertFilePositionType.HeaderFooter === type
            ||*/ bSelect
            || InsertFilePositionType.OtherNewControlsExceptTextAndSection === type
            || false === this._documentCore.canInput() ) {
            return ResultType.Failure;
        }

        const reader = new Reader(this._documentCore.getDocument());
        let result: any;
        try {
            // result = await reader.readFromStream(content, true, bNoEndPara, true, options, null, type);
            const bRecalc = null != options.bRecalc ? options.bRecalc : true;
            const params: IOpenFileAPIPars = {
                options, bNoEndPara, bRecalc, modeFonts: null, bInsertFile: true, type};
            result = await reader.readFromStream2(content, params);
        } catch (e) {
            return e;
        }

        if (result === ResultType.Success) {
            this._host.handleRefresh();
            // setTimeout(() => {
            //     this._host.testDocumentXml();
            // }, 10);
        }

        if (this._host.externalEvent) {
            // this._host.externalEvent.nsoFileOpenCompleted(base64String);
        }

        return result;
    }

    private async baseInsertBlobFile2(content: Blob, bNoEndPara: boolean = false,
        options: any = {}): Promise<number> {
            if (content == null || this._documentCore.isProtectedMode()
            || this._documentCore.isInNumberCellOfNISTable()
            || this._documentCore.isInNISTable()) {
            if (this.isInNISCellText() === false) {
            return ResultType.Failure;
            }
        }

        const bSelectedRegionContent = (options ? true === options.bSelectedRegionContent : false);
        const type = bSelectedRegionContent ? options.type : this._documentCore.getInsertFilePositionType();
        const bSelect = (bSelectedRegionContent
        && (InsertFilePositionType.ParentRegion === type || InsertFilePositionType.LeafRegion === type))
        ? false : (InsertFilePositionType.Selection === type);

        // if (/*InsertFilePositionType.HeaderFooter === type
        // ||*/ bSelect
        // || InsertFilePositionType.OtherNewControlsExceptTextAndSection === type
        // || false === this._documentCore.canInput() ) {
        // return ResultType.Failure;
        // }

        const reader = new Reader(this._documentCore.getDocument());
        let result: any;
        try {
            // result = await reader.readFromStream(content, true, bNoEndPara, true, options, null, type);
            const bRecalc = null != options.bRecalc ? options.bRecalc : true;
            const params: IOpenFileAPIPars = {
            options, bNoEndPara, bRecalc, modeFonts: null, bInsertFile: true, type};
            result = await reader.readFromStream2(content, params);
        }
        catch (e) {
            return e;
        }

        if (result === ResultType.Success) {
            this._host.handleRefresh();
            // setTimeout(() => {
            //     this._host.testDocumentXml();
            // }, 10);
        }

        if (this._host.externalEvent) {
            // this._host.externalEvent.nsoFileOpenCompleted(base64String);
        }
        return result;
    }

    private isInNISCellText(): boolean {
        if (this._documentCore.isInNISTable()) {
            const cellType = this._documentCore.getNISTableCellType();
            if (cellType === NISTableCellType.Quick || cellType === NISTableCellType.Text) {
                return true;
            }
        }

        return false;
    }

    private isValidNum(num: number): boolean {
        if (num === undefined) {
            return true;
        }

        return typeof num === 'number';
    }

    private setPintIframe(): any {
        let iframe = this._testIframe;
        if (!iframe) {
            const dom = document.createElement('iframe');
            dom.setAttribute('style', 'position: fixed; top: 300%; left: 300%; width: 1px; height: 1px; opacity: 0;');
            document.body.appendChild(dom);
            this._testIframe = iframe = dom;
        }

        return iframe;
    }

    private testPrint(content: string): void {
        // const html = document.createElement('html');
        // html.innerHTML = content;
        const win = this._testIframe.contentWindow;
        const doc = win.document;
        doc.lastChild.innerHTML = content;
        win.print();
    }

    private getPageProperty(obj: any): PageProperty {
        if (!obj || !obj.page || typeof obj.page !== 'string') {
            return;
        }
        const arrs = obj.page.split(',');
        if (arrs.length === 0) {
            return;
        }
        const page = new PageProperty();
        let pagePro: number[];
        switch (arrs[0]) {
            case 'A3':
                pagePro = PAGE_FORMAT.A3;
                page.width = getPxForMM(pagePro[0] * 10);
                page.height = getPxForMM(pagePro[1] * 10);
                break;
            case 'A4':
                pagePro = PAGE_FORMAT.A4;
                page.width = getPxForMM(pagePro[0] * 10);
                page.height = getPxForMM(pagePro[1] * 10);
                break;
            case 'A5': {
                pagePro = PAGE_FORMAT.A5;
                page.width = getPxForMM(pagePro[0] * 10);
                page.height = getPxForMM(pagePro[1] * 10);
                break;
            }
            case 'custom':
                if (!arrs[1]) {
                    return;
                }
                arrs[1] = arrs[1].slice(arrs[1].indexOf('=') + 1);
                const fPageWidth = parseFloat(arrs[1]);
                if (isNaN(fPageWidth)) {
                    return;
                }

                if (!arrs[2]) {
                    return;
                }
                arrs[2] = arrs[2].slice(arrs[2].indexOf('=') + 1);
                const fPageHeight = parseFloat(arrs[2]);
                if (isNaN(fPageHeight)) {
                    return;
                }

                page.width = getPxForMM(fPageWidth * 10);
                page.height = getPxForMM(fPageHeight * 10);
                break;
            default:
        }
        if (page.height === 0) {
            page.height = undefined;
        }
        if (page.width === 0) {
            page.width = undefined;
        }

        return page;
    }

    private getDefaultFont(obj: any): TextProperty {
        if (!obj || !obj.defaultFont) {
            return;
        }
        const arrs = obj.defaultFont.split(',');
        if (arrs.length === 0) {
            return;
        }
        const textProps = new TextProperty();

        if (!this.setTextPropsByArrs(textProps, arrs)) {
            return;
        }
        // console.log(textProps)
        return textProps;
    }

    private getRegionTitleFont(obj: any): TextProperty {
        if (!obj || !obj.regionTitleFont) {
            return;
        }
        const arrs = obj.regionTitleFont.split(',');
        if (arrs.length === 0) {
            return;
        }
        const textProps = new TextProperty();
        // console.log(arrs)
        try {
            if (!this.setTextPropsByArrs(textProps, arrs)) {
                return;
            }
        } catch (e) {
            // todo
        }
        return textProps;
    }

    /**
     * used to set two Fonts of openDocumentWithStream() 'mode'
     */
    private setTextPropsByArrs(textProps: TextProperty, arrs: string[]): boolean {

        for (const arr of arrs) {
            if (arr.includes('Font')) {
                const fontFamily = arr.slice(arr.indexOf('=') + 1);

                if (!FONT_MAPPING[fontFamily]) return false;
                textProps.fontFamily = fontFamily;
            } else if (arr.includes('Size')) {
                const sizeStr = arr.slice(arr.indexOf('=') + 1);

                const pxSize = FONST_SIZE_PX[sizeStr];
                // console.log(pxSize)
                if (!pxSize) return false;
                textProps.fontSize = pxSize;
            } else {
                // no need implement yet
            }
        }
        return true;
    }

    private compareAutoTestHtml(html1: string, html2: string): boolean {
        // const macFonts = ['STSong'];
        // points='xxx' diff
        const reg = /<text[^>]*?>\s+<\/text>/g;
        const rHtml1 = html1.replace(/ points=".+?"/g, ' points=""')
            .replace(/ font-family=\"STSong\"/g, ' font-family=\"\u5b8b\u4f53\"')
            .replace(reg, '');
        const rHtml2 = html2.replace(/ points=".+?"/g, ' points=""')
            .replace(/ font-family=\"STSong\"/g, ' font-family=\"\u5b8b\u4f53\"');

        return rHtml1 === rHtml2 ;
    }

    private compareAutoTestStructs(structs1: string, structs2: string): string {
        let result = 'false';
        if (structs1 === structs2) {
            result = 'true';
        } else {
            const structs1Collection = JSON.parse(structs1);
            const structs2Collection = JSON.parse(structs2);
            const refKeys = ['customProperty', 'newControlItems', 'customFormat'];

            // consider potential new props
            for (const structs1ObjKey in structs1Collection) {
                if (structs1Collection[structs1ObjKey] != null) {
                    const structs1Obj = structs1Collection[structs1ObjKey];
                    const structs2Obj = structs2Collection[structs1Obj['newControlName']];
                    if (structs2Obj == null) {
                        return `struct name: "${structs1Obj['newControlName']}" is not found`;
                    }
                    // obj number should be the same
                    for (const key in (structs1Obj as any)) {
                        if (structs2Obj[key] === undefined) {
                            // new property, ignore
                            continue;
                        } else {
                            if (refKeys.includes(key) === true) {
                                // if (JSON.stringify(structs1Obj[key]) !== JSON.stringify(structs2Obj[key]) ) {
                                if (this.compareObjectArrs(structs1Obj[key], structs2Obj[key]) === false) {
                                    return this.outputDiffInfo(key, structs1Obj, structs2Obj);
                                    // return false;
                                }
                            } else {
                                // can compare directly
                                if (structs1Obj[key] !== structs2Obj[key]) {
                                    return this.outputDiffInfo(key, structs1Obj, structs2Obj);
                                    // return false;
                                }
                            }
                        }
                    }
                }
            }
            result = 'true';

        }

        return result;
    }

    private compareAutoTestImages(image1: string, image2: string): string {
        let result = 'false';
        if (image1 === image2) {
            result = 'true';
        } else {
            const image1Collection = JSON.parse(image1);
            const image2Collection = JSON.parse(image2);

            // consider potential new props
            for (const image1ObjKey in image1Collection) {
                if (image1Collection[image1ObjKey] != null) {
                    const image1Obj = image1Collection[image1ObjKey];
                    const image2Obj = image2Collection[image1Obj['name']];
                    if (image2Obj == null) {
                        return `image name: "${image1Obj['name']}" is not found`;
                    }
                    // obj number should be the same
                    for (const key in (image1Obj as any)) {
                        if (image2Obj[key] === undefined) {
                            // new property, ignore
                            continue;
                        } else {
                            if (image1Obj[key] !== image2Obj[key]) {
                                return this.outputDiffInfo(key, image1Obj, image2Obj);
                                // return false;
                            }
                        }
                    }
                }
            }
            result = 'true';

        }

        return result;
    }

    private compareAutoTestTables(table1: string, table2: string): string {
        let result = 'false';
        if (table1 === table2) {
            result = 'true';
        } else {
            const table1Collection = JSON.parse(table1);
            const table2Collection = JSON.parse(table2);
            // keys that pass by ref so cannt directly compare
            const refKeys = ['columnWidths', 'rowHeights', 'rowsAuto', 'tableDefaultMargins',
            'customProperty', 'curPos', 'tableCellMargins', 'tableBorders', 'cellBorders'];
            const ignoreKeys = ['content'];

            // consider potential new props
            for (const table1ObjKey in table1Collection) {
                if (table1Collection[table1ObjKey] != null) {
                    const table1Obj = table1Collection[table1ObjKey];
                    const table2Obj = table2Collection[table1Obj['tableName']];
                    // obj number should be the same
                    for (const key in (table1Obj as any)) {
                        if (table2Obj[key] === undefined) {
                            // new property, ignore
                            continue;
                        } else {
                            if (ignoreKeys.includes(key) === true) {
                                // console.log(key + ' ' + table1Obj[key])
                                if (key === 'content') {
                                    const bNoDiff = this.compareAutoTestTableRows(JSON.stringify(table1Obj[key]),
                                        JSON.stringify(table2Obj[key]));
                                    if (bNoDiff !== 'true') {
                                        return bNoDiff;
                                        // return false;
                                    }
                                }
                                continue;
                            } else if (refKeys.includes(key) === true) {
                                if (JSON.stringify(table1Obj[key]) !== JSON.stringify(table2Obj[key]) ) {
                                    return this.outputDiffInfo(key, table1Obj, table2Obj);
                                    // return false;
                                }
                            } else {
                                // can compare directly
                                if (table1Obj[key] !== table2Obj[key]) {
                                    return this.outputDiffInfo(key, table1Obj, table2Obj);
                                    // return false;
                                }
                            }
                        }
                    }
                }
            }

            result = 'true';
        }

        return result;
    }

    private compareAutoTestTableRows(table1Rows: string, table2Rows: string): string {
        let result = 'false';
        if (table1Rows === table2Rows) {
            result = 'true';
        } else {
            const table1RowCollection = JSON.parse(table1Rows);
            const table2RowCollection = JSON.parse(table2Rows);
            // keys that pass by ref so cannt directly compare
            const refKeys = ['height', 'cellSpacing', 'widthAfter', 'widthBefore'];
            const ignoreKeys = ['content'];

            // consider potential new props
            for (let i = 0, len = table1RowCollection.length; i < len; i++) {
                const table1Row = table1RowCollection[i];
                const table2Row = table2RowCollection[i];

                for (const key in (table1Row as any)) {
                    if (table2Row[key] === undefined) {
                        // new property, ignore
                        continue;
                    } else {
                        if (ignoreKeys.includes(key) === true) {
                            // console.log(key + ' ' + table1Row[key])
                            if (key === 'content') {
                                const bNoDiff = this.compareAutoTestTableCells(JSON.stringify(table1Row[key]),
                                    JSON.stringify(table2Row[key]));
                                if (bNoDiff !== 'true') {
                                    return bNoDiff;
                                    // return false;
                                }
                            }
                            continue;
                        } else if (refKeys.includes(key) === true) {
                            if (JSON.stringify(table1Row[key]) !== JSON.stringify(table2Row[key]) ) {
                                return this.outputDiffInfo(key, table1Row, table2Row);
                                // return false;
                            }
                        } else {
                            // can compare directly
                            if (table1Row[key] !== table2Row[key]) {
                                return this.outputDiffInfo(key, table1Row, table2Row);
                                // return false;
                            }
                        }
                    }
                }
            }

            result = 'true';
        }

        return result;
    }

    private compareAutoTestTableCells(table1Cells: string, table2Cells: string): string {
        let result = 'false';
        if (table1Cells === table2Cells) {
            result = 'true';
        } else {
            const table1CellCollection = JSON.parse(table1Cells);
            const table2CellCollection = JSON.parse(table2Cells);
            // keys that pass by ref so cannt directly compare
            const refKeys = ['borders', 'cellWidth', 'shadow'];
            // const ignoreKeys = [];

            // consider potential new props
            for (let i = 0, len = table1CellCollection.length; i < len; i++) {
                const table1Cell = table1CellCollection[i];
                const table2Cell = table2CellCollection[i];

                for (const key in (table1Cell as any)) {
                    if (table2Cell[key] === undefined) {
                        // new property, ignore
                        continue;
                    } else {
                        if (refKeys.includes(key) === true) {
                            if (JSON.stringify(table1Cell[key]) !== JSON.stringify(table2Cell[key]) ) {
                                return this.outputDiffInfo(key, table1Cell, table2Cell);
                                // return false;
                            }
                        } else {
                            // can compare directly
                            if (table1Cell[key] !== table2Cell[key]) {
                                return this.outputDiffInfo(key, table1Cell, table2Cell);
                                // return false;
                            }
                        }
                    }
                }
            }
            result = 'true';
        }

        return result;
    }

    private outputDiffInfo(key: string, obj1: any, obj2: any): string {
        // const result = [];
        // result.push(key + ' ' + obj1[key])
        // result.push(key + ' ' + obj2[key])
        // return result;
        const diffInfo = key + ' ' + JSON.stringify(obj1[key]) + ' ' + JSON.stringify(obj2[key]);
        // tslint:disable-next-line: no-console
        // console.log(diffInfo);
        return diffInfo;
    }

    private compareObjectArrs(arr1: any[], arr2: any[]): boolean {
        // console.log(arr1, arr2)
        if (arr1.length !== arr2.length) {
            return false;
        }
        if (arr1.length === 0 && arr2.length === 0) {
            return true;
        } else if (arr1.length === 0 || arr2.length === 0) {
            return false;
        }

        if (arr1.length > 0 && arr2.length > 0) {
            let keys = [];
            if (Object.keys(arr1[0]).length !== Object.keys(arr2[0]).length) {
                return false;
            }

            // get keys
            keys = Object.keys(arr1[0]);

            for (const obj1 of arr1) {
                for (let i = 0, len = arr2.length; i < len; i++) {
                    // console.log(len)
                    const obj2 = arr2[i];
                    let correct = 0, keyLen = keys.length;
                    for (const key of keys) {
                        if (obj1[key] === obj2[key]) {
                            correct++;
                        }
                    }
                    if (correct === keyLen) {
                        arr2.splice(i, 1);
                        break;
                    }
                    if (i === len - 1) {
                        return false;
                    }
                }
            }
        }
        return true;
    }

    /**
     * Region 替换：使用传统解析替换机制
     * @private
     */
    private async replaceRegionWithTraditionalMethod(targets: any[], sources: any[], content: Blob, type: number): Promise<string[][]> {
        const result: any[][] = await this.getSpecificStructContentWithBackStream(sources, content, type);
        if (!result || !result.length) {
            this.resetAdminMode();
            return [];
        }

        const obj = result.find((item) => item);
        if (!obj) {
            this.resetAdminMode();
            return [];
        } else {
            const doc = this._documentCore.getDocument();
            // tslint:disable-next-line: prefer-for-of
            for (let index = 0; index < result.length; index++) {
                const element = result[0]; // 保持原始逻辑：固定使用 result[0]
                if (element) {
                    element.forEach((item) => {
                        if (item) {
                            item.setLogicDocument(doc);

                            if (item.isRegion() && !!item.content) {
                                const contents = item.content;
                                contents.setLogicDocument(doc);
                                contents.content?.forEach((item2) => {
                                    if (item2) {
                                        item2.setLogicDocument(doc);
                                    }
                                });
                            }
                        }
                    });
                }
            }
        }

        return this._documentCore.replaceRegionTexts(targets, result);
    }

    /**
     * NewControl 替换：优先使用新机制，失败时回退到传统机制
     * @private
     */
    private async replaceNewControlWithHybridMethod(targets: any[], sources: any[], content: Blob, type: number): Promise<string[][]> {
        const res: string[][] = [];

        for (const target of targets) {
            const targetNames = this.resolveTargetName(target);
            if (!targetNames || targetNames.length === 0) continue;

            let fallbackResult: any = null;
            let hasSuccessfulNewMethod = false;

            // 处理所有匹配的控件名称
            for (const targetName of targetNames) {
                // 新机制：筛选 + 删除 + 插入
                const newMethodResult = await this.tryNewControlReplaceMethod(targetName, sources, content, type);
                if (newMethodResult.success) {
                    res.push([targetName]);
                    hasSuccessfulNewMethod = true;
                    continue;
                }

                // 如果新机制失败且还没有尝试传统机制，则尝试传统机制
                if (!fallbackResult) {
                    fallbackResult = await this.fallbackToTraditionalMethodForSingleTarget(targets, sources, content, type);
                }

                if (fallbackResult.shouldContinue) {
                    continue; // 筛选失败，跳过当前目标
                }
            }

            // 如果没有成功的新机制处理，且传统机制成功，收集结果
            if (!hasSuccessfulNewMethod && fallbackResult && fallbackResult.result && fallbackResult.result.length > 0) {
                res.push(...fallbackResult.result);
            }
        }

        return res;
    }

    /**
     * 解析目标名称（支持 name 属性和自定义属性匹配）
     * @private
     */
    private resolveTargetName(target: any): string[] {
        let targetName = (target as any).name;

        if (targetName) {
            // 如果有name属性，直接通过name获取
            const control = this._documentCore.getNewControlByName(targetName);
            if (control) {
                return [targetName];
            }
        } else {
            // 如果没有name属性，使用自定义属性匹配
            const allControls = this._documentCore.getAllNewControls2();
            const matchedControls = allControls.filter((item) => item.isInCustomProp3(target));

            // 返回所有匹配的控件名称
            if (matchedControls.length > 0) {
                return matchedControls.map(control => control.getNewControlName());
            }
        }

        return [];
    }

    /**
     * 尝试新的 NewControl 替换方法
     * @private
     */
    private async tryNewControlReplaceMethod(targetName: string, sources: any[], content: Blob, type: number): Promise<{success: boolean}> {
        try {
            // 步骤1：为当前目标生成筛选后的内容
            const filteredContent = await this.generateFilteredBlob(content, sources, targetName, type);
            if (!filteredContent) {
                return { success: false };
            }

            // 步骤2：选中目标结构的内容
            const selectResult = this._documentCore.selectNewControl(targetName);
            if (selectResult !== ResultType.Success) {
                return { success: false };
            }

            // 步骤3：删除选中的内容
            const logicDocument = this._documentCore.getDocument();
            const bRevision = logicDocument.isTrackRevisions();
            logicDocument.setTrackRevisions(false); // 临时关闭修订跟踪
            logicDocument.remove(1, false, true, false); // 删除选中内容
            logicDocument.setTrackRevisions(bRevision); // 恢复修订跟踪状态

            // 步骤4：使用标准插入流程插入筛选后的内容
            logicDocument.setSelectedContentFlag(true);
            const insertResult = await this.baseInsertBlobFile(filteredContent, true, {});
            logicDocument.setSelectedContentFlag(false);

            return { success: insertResult === ResultType.Success };
        } catch (error) {
            return { success: false };
        }
    }

    /**
     * 传统替换方法（单个目标处理，完整保留原始逻辑）
     * @private
     */
    private async fallbackToTraditionalMethodForSingleTarget(targets: any[], sources: any[], content: Blob, type: number): Promise<{shouldContinue: boolean, result?: string[][]}> {
        const result: any[][] = await this.getSpecificStructContentWithBackStream(sources, content, type);
        // console.log(result)
        if (!result || !result.length) {
            this.resetAdminMode();
            return {shouldContinue: false}; // 这里原始代码会 return ResultType.StringEmpty，我们转换为 shouldContinue
        }

        const obj = result.find((item) => item);
        if (!obj) {
            this.resetAdminMode();
            return {shouldContinue: false}; // 这里原始代码会 return ResultType.StringEmpty，我们转换为 shouldContinue
        } else {
            const doc = this._documentCore.getDocument();
            // tslint:disable-next-line: prefer-for-of
            for (let index = 0; index < result.length; index++) {
                const element = result[index]; // 修复：原来是 result[0]，现在改为 result[index]
                if (element) {
                    element.forEach((item) => {
                        if (item) {
                            item.setLogicDocument(doc);

                            if (item.isRegion() && !!item.content) {
                                const contents = item.content;
                                contents.setLogicDocument(doc);
                                contents.content?.forEach((item2) => {
                                    if (item2) {
                                        item2.setLogicDocument(doc);
                                    }
                                });
                            }
                        }
                    });
                }
            }
        }
        const res: string[][] = this._documentCore.replaceNewControlTexts(targets, result);
        return {shouldContinue: true, result: res}; // 返回处理结果
    }

    /**
     * 为元素设置 LogicDocument 引用（提取的公共方法）
     * @private
     */
    private setupLogicDocumentForElements(result: any[][]): void {
        const doc = this._documentCore.getDocument();

        for (let index = 0; index < result.length; index++) {
            const element = result[index];
            if (element) {
                element.forEach((item) => {
                    if (item) {
                        item.setLogicDocument(doc);

                        if (item.isRegion() && !!item.content) {
                            const contents = item.content;
                            contents.setLogicDocument(doc);
                            contents.content?.forEach((item2) => {
                                if (item2) {
                                    item2.setLogicDocument(doc);
                                }
                            });
                        }
                    }
                });
            }
        }
    }
}
