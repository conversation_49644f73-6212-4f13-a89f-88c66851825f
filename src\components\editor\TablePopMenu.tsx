import * as React from 'react';
import { DocumentCore } from '../../model/DocumentCore';
import { TableMenuModalType } from '../../common/commonDefines';

interface ITablePopMenuProps {
    documentCore?: DocumentCore;
    refresh: (timeout?: number) => void;
    handleTableMenuModalState: () => void;
    // bShowDeletePopMenu?: boolean;
    // bShowSplitPopMenu?: boolean;
    tablePopMenuModalType: TableMenuModalType;
}

interface ITablePopMenuState {
    // showFormatConfigModal?: boolean;
    // bDeleteRows: boolean;
    // bDeleteCols: boolean;
    splitTableCols: number;
    splitTableRows: number;
}

export class TablePopMenu extends React.Component<ITablePopMenuProps, ITablePopMenuState> {
    private bDeleteRows: boolean;
    private bDeleteCols: boolean;

    // private splitTableCols: number;
    // private splitTableRows: number;

    constructor(props) {
        super(props);

        this.state = {
            splitTableCols: 1,
            splitTableRows: 1,
        };

        this.bDeleteCols = false;
        this.bDeleteRows = false;
        // this.splitTableCols = 1;
        // this.splitTableRows = 1;
    }

    renderDeleteCellMenu() {
        return (
            <div className="table-pop-menu" style={{"display": ( TableMenuModalType.DeleteTableCells === this.props.tablePopMenuModalType ? "block": "none" )}}>
                <div className="table-pop-menu config-window" >
                    <div className="config-descriptor">
                        删除单元格？
                    </div>

                    <div className="detail-block">
                        <input type="radio" name="deleteTableCells" value="row" onClick={this.handleRadioButtonClick.bind(this, "deleteRow")}/>删除整行
                    </div>
                    <div className="detail-block">
                        <input type="radio" name="deleteTableCells" value="col" onClick={this.handleRadioButtonClick.bind(this, "deleteCol")}/>删除整列
                    </div>

                    <div className="button-container">
                        <button className="button" onClick={this.handleButtonClick.bind(this, "deleteTableCells")}>取消</button>
                        <button className="button" onClick={this.handleButtonClick.bind(this, "deleteTableCells")}>确定</button>
                    </div>
                </div>
            </div>
        );
    }

    renderSplitCellMenu() {
        return (
            <div className="table-pop-menu" style={{"display": ( TableMenuModalType.SplitTableCells === this.props.tablePopMenuModalType ? "block": "none" )}}>
                <div className="table-pop-menu config-window" >
                    <div className="config-descriptor">
                        拆分单元格？
                    </div>

                    <div className="detail-block">
                        <label htmlFor="table-cols" className="cols-cell">列数(C)：</label>
                        <input type="number" min={1} step={1} id={"table-cols"} className="table" value={this.state.splitTableCols} onChange={(e) => this.handleTableValueChange(e)} />
                    </div>
                    <div className="detail-block">
                        <label htmlFor="table-rows" className="rows-cell">行数(R)：</label>
                        <input type="number" min={1} step={1} id={"table-rows"} className="table" value={this.state.splitTableRows} onChange={(e) => this.handleTableValueChange(e)} />
                    </div>

                    <div className="button-container">
                        <button className="button" onClick={this.handleButtonClick.bind(this, "splitCell")}>取消</button>
                        <button className="button" onClick={this.handleButtonClick.bind(this, "splitCell")}>确定</button>
                    </div>
                </div>
            </div>
        );
    }

    render() {
        return (
            <div className="table-pop-menu config-window-container">
                {this.renderDeleteCellMenu()}
                {this.renderSplitCellMenu()}
            </div>
        );
    }

    handleRadioButtonClick(type, e) {
        switch (type) {
            case "deleteRow": {
                this.bDeleteRows = true;
                this.bDeleteCols = false;
                break;
            }

            case "deleteCol": {
                this.bDeleteRows = false;
                this.bDeleteCols = true;
                break;
            }
        }

    }

    handleTableValueChange(e) {
        let type = e.target.id;
        let splitTableCols = Number(e.target.value);
        let splitTableRows = Number(e.target.value);

        if ( type === "table-cols" ) {
            this.setState({splitTableCols: splitTableCols});
          }
      
          if ( type === "table-rows" ) {
            this.setState({splitTableRows: splitTableRows});
          }
    }

    handleButtonClick(type, e) {

        const { documentCore } = this.props;
        switch (e.target.innerHTML) {
            case "确定": {
                if ( "deleteTableCells" === type ) {
                    if ( true === this.bDeleteRows ) {
                        documentCore.removeTableRow();
                    } else if ( true === this.bDeleteCols ) {
                        documentCore.removeTableColumn();
                    }
                } else if ( "splitCell" === type ) {
                    if ( 1 !== this.state.splitTableCols || 1 !== this.state.splitTableRows ) {
                        documentCore.splitTableCells(this.state.splitTableRows, this.state.splitTableCols);
                    }
                }

                this.props.refresh(0);
                break;
            }

            case "取消": {
                // todo：需要重新focus，不然光标失效，Editor.updateCursor()
                break;
            }
        }

        this.props.handleTableMenuModalState();
    }
}
