import { INum, INumberingManager, INumberingProperty, NumberingType, ResultType } from '@/common/commonDefines';
import ContentChanges, { ContentChangesElement } from '../ContentChanges';
import DocumentContentElementBase from '../DocumentContentElementBase';
import { idCounter } from '../util';
import { ChangeNumberingAddLvl, ChangeNumberingParaNumTr, ChangeNumberingRemoveLvl } from './NumChanges';
import { NumLvl } from './NumLvl';

export class Numbered implements INum {
    private type: NumberingType;
    private id: number;
    private lvls: NumLvl[];
    private contentChanges: ContentChanges;
    private numbering: INumberingManager;
    private abstractNumId: number;
    constructor(type: NumberingType, manager?: INumberingManager) {
        this.type = type;
        this.numbering = manager;
        this.id = idCounter.getNewId();
        this.lvls = [];
        this.contentChanges = new ContentChanges();
    }

    public getId(): number {
        return this.id;
    }

    public getLvl(nLvl: number): NumLvl {
        return this.lvls[nLvl];
    }

    public addContentChanges( changes: ContentChangesElement ): void {
        this.contentChanges.add(changes);
    }

    public getAllLvl(): NumLvl[] {
        return this.lvls;
    }

    public getLvlPos(para: DocumentContentElementBase): number {
        return this.lvls.findIndex((item) => item.getPara() === para);
    }

    public getLvlByPara(para: DocumentContentElementBase): NumLvl {
        return this.lvls.find((item) => item.getPara() === para);
    }

    public getLvlById(paraId: number): NumLvl {
        return this.lvls.find((item) => item.getParaId() === paraId);
    }

    public addLvl(props: INumberingProperty): number {
        const {nLvl, para, textPro, bEnter} = props;
        // this.type = type;
        const item = new NumLvl(this.type, para, textPro);
        item.setText(nLvl + 1);
        let prev;
        if (bEnter) {
            let pos = nLvl;
            if (this.lvls.length === nLvl) {
                pos--;
            }
            prev = this.lvls[pos];
        }

        item.addParaNumbering(this.id, prev?.getParaInd());
        const history = this.numbering.history;
        history.addChange(new ChangeNumberingParaNumTr(para, {}, {numPr: para.getNumberingPr(),
            numbering: para.getNumbering()}));
        history.addChange(new ChangeNumberingAddLvl(this, nLvl, [item]));
        this.lvls.splice(nLvl, 0, item);
        return ResultType.Success;
    }

    public remove(): number {
        const lvls = this.lvls;
        const history = this.numbering.history;
        lvls.forEach((item) => {
            const obj: any = {};
            const para = item.getPara();
            obj.numPr = para.getNumberingPr();
            obj.numbering = para.getNumbering();
            history.addChange(new ChangeNumberingParaNumTr(para, obj, {}));
            item.remove();
        });
        history.addChange(new ChangeNumberingRemoveLvl(this, 0, lvls.slice(0)));
        this.lvls = [];
        return ResultType.Success;
    }

    public removeLvlByPara(para: DocumentContentElementBase): number {
        const index = this.lvls.findIndex((item) => item.getPara() === para);
        if (index === -1) {
            ResultType.Failure;
        }

        return this.removeLvl(index);
    }

    public removeLvl(nLvl: number): number {
        if (this.lvls.length === 0) {
            return ResultType.UnEdited;
        }

        const lvl = this.lvls.splice(nLvl, 1)[0];
        if (!lvl) {
            return ResultType.UnEdited;
        }
        const history = this.numbering.history;
        const obj: any = {};
        const para = lvl.getPara();
        obj.numPr = para.getNumberingPr();
        obj.numbering = para.getNumbering();
        history.addChange(new ChangeNumberingParaNumTr(para, obj, {}));
        history.addChange(new ChangeNumberingRemoveLvl(this, nLvl, [lvl]));
        lvl.remove();

        this.updateLvlTexts(nLvl);
        return ResultType.Success;
    }

    public updateLvlTexts(nLvl: number): void {
        if (!this.isNumbered()) {
            return;
        }
        const lvls = this.lvls;
        for (const len = lvls.length; nLvl < len; nLvl++) {
            const lvl = lvls[nLvl];
            lvl.setNumValue(nLvl + 1);
            lvl.setParaInd();
        }
    }

    public isNumbered(): boolean {
        return this.type === NumberingType.Number;
    }

    public isBullet(): boolean {
        return this.type === NumberingType.Bullet;
    }

    public getType(): number {
        return this.type;
    }
}
