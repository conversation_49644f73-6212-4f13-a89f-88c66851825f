/* IFTRUE_WATER */
import { getPxForMM, measure } from '../model/core/util';
import { DEFAULT_TEXT_PROPERTY, EXEC_DOM, exportText, fromCharCode, IDefaultFont, numtoFixed } from './commonDefines';
import JSEncrypt from './jsencrypt.js';

/** 字体样式 */
export interface IMarkFont {
    fontSize: number;
    fontFamily: string;
    className: string;
    fontColor: string;
    fontWeight: string;
    fontStyle: string;
    needClass?: boolean;
}

//const empStr = ['B','1','G','J','X','M','P','h','g','+','D','H','f','f','8','5','S','+','Q','6','7','x','v','J','w','7','o','C','q','M','Z','U','U','b','2','l','f','q','y','p','2','j','x','/','D','7','0','Z','0','q','9','e','y','e','b','y','i','r','e','U','5','L','S','/','z','Z','B','8','A','t','D','r','p','g','y','N','Z','s','G','T','g','m','N','g','M','c','y','9','t','f','t','8','R','e','w','0','h','H','W','b','s','T','p','E','A','K','0','R','7','2','0','X','a','X','x','k','4','Y','m','V','Z','w','G','l','s','P','d','1','2','k','e','J','Z','k','c','L','Z','m','J','R','N','U','8','E','V','h','z','7','9','k','l','q','o','J','p','E','i','J','f','P','G','C','Y','R','L','C','C','D','x','t','Y','B','y','3','S','G','o','V','G','D','y','N','i','6','+','k','P','5','w','/','a','Z','1','g','u','X','i','Y','B','i','6','T','d','D','H','V','o','E','R','t','q','V','L','R','Y','S','X','U','b','m','Q','y','V','I','9','Z','k','p','3','1','+','u','X','O','L','f','M','5','v','o','G','p','E','S','q','u','k','Y','z','M','e','3','D','l','e','E','6','r','3','J','J','0','M','D','w','L','4','B','f','L','z','F','/','n','m','c','g','5','p','O','C','1','o','G','M','v','G','f','p','a','q','n','z','B','Q','1','c','d','L','f','u','y','X','i','E','R','X','1','p','m','2','8','w','o','L','k','L','S','E','A','C','J','B','i','i','3','S','F','h','d','A','y','u','g','f','I','k','e','J','r','N','w','=','='];
const empStr = "hR97hPkSzUX8w0KW4AbDf3xoeIsCr8uVOXhXgYk3Y6pLoMtFSzWk8n7VhkI9L+ypL4cAIS9fMU8p7DlHxHdi8G5i5t3oQ6t/ce2Y5kNf72LAIsi2EzvXc8dpA6dn8fvdZLFwxgHYUShnfjlU4ioa7BjdAikU8UQbsIdUDCZz+Tvi//39zBvWtC7jUF2eUtZPIopd7evP2PEVfFsk2t1Gi+O9umq3+ouIkSvRgwjEgoUleAW9tEMw+qkStFyS1WrqzCFRhIeaOH3EKrkbDwdK8JkpKgAzCnoBmvIYY+qzt3uCGgjb4ENlC0myGZZ/k2YPrqtcJbUoqGm86BhB1FlT7A==";
/** 水印坐标对象 */
export interface IMarkRect {
    content: string;
    xs: string;
    ys: string;
    angle: number;
    font?: IMarkFont;
    needClass?: boolean;
}
const codeNum = 97;
export interface IMarkBounding {
    x: number;
    y: number;
    maxX: number;
    maxY: number;
}

//let empText = decrypto(empStr.join(''));
let empText = decrypto(empStr);

/** 随机水印工厂 */
export default class MarkFactory {

    // tslint:disable-next-line: sy-member-variable-typedef
    private static _instance;

    public static getInstance(): MarkFactory {
        if (!this._instance) {
            this._instance = new MarkFactory();
        }
        return this._instance;
    }
    public bPosChange: number = getRandom(0, 99);

    private content: string;
    private posXs: number[] = [];
    private posYs: number[] = [];

    private font: IDefaultFont;
    private angle: number = 0; // 旋转角度（暂时无用）
    private maxRepeat: number; // 水印最大重复个数
    private repeatHeights: number[] = []; // 随机高度
    private repeatWidths: number[] = []; // 随机宽度
    private curMaxRepeat: number; // 当前最大重复个数
    private threshold: number = 0.5; // 获取水印的随机概率
    // rotate position
    private rotPosXs: number[] = [];
    private rotPosYs: number[] = [];

    private generator: Generator<IMarkRect, IMarkRect | null, boolean>;
    private bGenStart: boolean = false; // 是否开始生成（用于跳过页眉）

    // random className
    private classHeadDom: HTMLElement;

    // used by str random position
    private startX: number = -1; // start X position
    private startY: number = -1; // start Y position
    private maxX: number = 500;
    private maxY: number = 300;
    private startPosXs: number[] = [];
    private startPosYs: number[] = [];
    private lineHeight: number = 20; // 行高
    private rectWidth: number; // 剩余宽度
    private startFonts: IMarkFont;
    private opacity: number = 4;
    private opacityMap = ['33', '80', '99', 'B3']; // 40, 50, 60, 70
    private _enterTail: boolean = false;

    // 静态样式内容
    private fontStyle: string[] = ['normal'];
    private fontWeight: string[] = ['normal'];

    constructor(water?: string,
                font: IDefaultFont = {
                fontFamily: DEFAULT_TEXT_PROPERTY.fontFamily,
                fontSize: DEFAULT_TEXT_PROPERTY.fontSize
                },
                angle: number = 0,
                maxRepeat: number = 1) {
        if (water == null) {
           // water = decrypto(empStr.join(''));
           water = decrypto(empStr);
        }
        empText = null;
        this.angle = angle;
        this.content = water;
        this.font = font;
        this.maxRepeat = maxRepeat;
        this.resetRandMarkClass(true);
        // this.resetRandom();
        this.initMark(this.content, this.font, this.angle, this.maxRepeat);
    }

    /**
     * 生成带坐标的水印
     */
    public initMark(water?: string,
                    font: IDefaultFont = {
                            fontFamily: DEFAULT_TEXT_PROPERTY.fontFamily,
                            fontSize: DEFAULT_TEXT_PROPERTY.fontSize
                        },
                         angle: number = 0,
                         maxRepeat: number = 1): void {
        if (water == null) {
            water = this.content;
        }
        this.resetRandMarkClass(true);
        this.font = Object.assign({}, font, { fontSize: 8 }); // 修改字体为10
        this.content = water;
        this.maxRepeat = maxRepeat;
        this.computePosition();
        this.rotate(angle);
        this.bGenStart = true;
    }

    public setStr(str?: string): void {
        // tslint:disable-next-line: no-unused-expression
        str && this.initMark(str);
    }

    public setBGenStart(bGen: boolean = true): void {
        if (this.bGenStart !== bGen) {
            this.bPosChange = (this.bPosChange + 1) % 100;
        }
        this.bGenStart = bGen;
    }

    public getBGenStart(): boolean {
        return this.bGenStart;
    }

    /**
     * 设置水印起始坐标
     */
    public setStartPos(startPos?: IMarkBounding): void {
        if (!startPos) {
            return;
        }
        const headSame = this.startX === startPos.x && this.startY === startPos.y;
        const tailSame = this.maxX === startPos.maxX && this.maxY === startPos.maxY;
        if (headSame && tailSame) {
            return;
        }
        this.startX = startPos.x;
        this.startY = startPos.y;
        if (startPos.maxX !== -1) {
            this.maxX = startPos.maxX;
        }
        if (startPos.maxY !== -1) {
            this.maxY = startPos.maxY;
        }
        // 水印显示位置变化时重置坐标样式： 页眉 / 正文
        if (!headSame) {
            this.computePosition();
            this.rotate(this.angle);
        }
        // 随机水印坐标
        this.randomStartPosition(!headSame || !tailSame);
    }

    /**
     * 随机化水印的起始位置
     */
    public randomStartPosition(randRepeat: boolean = true): void {
        if (this.maxX === -1 || this.maxY === -1) {
            return;
        }
        // 页眉
        // const maxHeight = Math.random() < 0.1 ? this.maxY : getPxForMM(10);
        // const minHeight = this.startY;
        // 页脚
        const maxHeight = this.maxY;
        const minHeight = Math.random() < 0.1 ? this.startY : this.maxY - getPxForMM(10);
        this.startPosXs = [];
        this.startPosYs = [];
        // todo: 待改进： 暂定不旋转坐标的情况
        if (randRepeat) {
            // 随机样式
            this.startFonts = {
                fontSize: this.font.fontSize,
                fontFamily: this.font.fontFamily,
                className: '',
                fontStyle: this.fontStyle[getRandom(0, this.fontStyle.length - 1)],
                fontWeight: this.fontWeight[getRandom(0, this.fontWeight.length - 1)],
                fontColor: this.getRandomColor(),
                needClass: false, // Math.random() < this.threshold ? true : false,
                // todo: 目前svg 十六进制透明度与opacity透明度不一致
            };
            // 一次初始化，后续进行使用
            const fullHeight = maxHeight - minHeight - this.lineHeight;
            // const lines = Math.floor(fullHeight / this.lineHeight / getRandom(1, 3));
            // const lineNum = lines < 1 ? 1 : lines;
            // const curMaxRepeat = lineNum > this.maxRepeat ? this.maxRepeat : lineNum;
            // const blockHeight = lineNum / this.curMaxRepeat * this.lineHeight;
            let blockHeight = fullHeight / this.maxRepeat;
            blockHeight = Math.max(this.lineHeight, blockHeight);

            this.repeatHeights = [];
            this.repeatWidths = [];
            let startHeight = minHeight + this.lineHeight;
            let endHeight = minHeight + blockHeight;
            for (let idx = 0; idx < this.maxRepeat; idx++) {
                // 随机坐标
                const curY = getRandom(startHeight, endHeight, true);
                const curX = getRandom(this.startX, this.maxX - this.rectWidth, true) + Math.random();
                this.repeatWidths.push(curX);
                this.repeatHeights.push(curY);

                let randNum = 1;
                if (this.maxRepeat > 1) {
                    do {
                        randNum = getRandom(1, 1.5, true);
                    } while(blockHeight * randNum > fullHeight);
                }
                startHeight += blockHeight * randNum;
                endHeight += blockHeight;
            }
            this.opacity = 4; // getRandom(4, 7);
        }
        this.curMaxRepeat = 0;
        for (let idx = 0; idx < this.maxRepeat; idx++, this.curMaxRepeat++) {
            if (idx > 0 && this.repeatHeights[idx] > maxHeight) {
                break;
            }
            // 随机坐标
            this.startPosXs.push(this.repeatWidths[idx]);
            this.startPosYs.push(this.repeatHeights[idx]);
        }
    }

    /** 通过生成器的方式，获取当前可使用的
     * @param release 是否生成当前剩余水印内容
     * @param reset 是否重置
     * @param startPos 水印起始坐标
     */
    public getCorePosition(release: boolean = false, reset: boolean = false,
                           startPos?: IMarkBounding): IMarkRect | null {
        if (reset === true) {
            this.setStartPos(startPos);
            this.resetRandom();
        }
        if (!this.generator) {
            this.setStartPos();
            this.resetRandom();
        }
        if (this._enterTail) {
            return undefined;
        }
        return this.generator.next(release).value;
    }
    /** 获取全部水印内容 */
    public getFullMark(): IMarkRect {
        const className = getCode(); // getCode(getRandom(5, 10));
        // 水印透明度 (40, 50, 60, 70)
        const opacity = this.opacity;
        const opacityHex = this.opacityMap[opacity - 4];
        this.classHeadDom.innerHTML += `.${className}{opacity:0.${opacity};}`;

        const pxs = [];
        const pys = [];
        let str = '';
        for (let repeatTime = 0; repeatTime < this.curMaxRepeat; repeatTime++) {
            for (let idx = 0; idx < this.rotPosXs.length; idx++) {
                if (this.content[idx] !== ' ') {
                    str += this.content[idx];
                    pxs.push(this.startPosXs[repeatTime] + this.rotPosXs[idx]);
                    pys.push(this.startPosYs[repeatTime]);
                }
            }
        }

        const ifont = { ...this.startFonts };
        ifont.className = className;
        ifont.fontColor = '#' + ifont.fontColor + opacityHex;
        const needClass = ifont.needClass;
        ifont.needClass = undefined;
        return {
            content: str,
            xs: pxs.join(' '),
            ys: pys.join(' '),
            angle: this.angle,
            font: ifont,
            needClass
        };
    }

    /**
     * 重置随机水印的样式内容
     * @param reset 是否重置样式name
     */
    public resetRandMarkClass(reset: boolean = false): void {
        if (reset !== true) {
            return;
        }
        if (!this.classHeadDom) {
            this.classHeadDom = document.createElement('style');
            if (document.head.childNodes.length > 0) {
                const idx = getRandom(0, document.head.childNodes.length - 1);
                document.head.insertBefore(this.classHeadDom, document.head.childNodes[idx]);
            } else {
                document.head.append(this.classHeadDom);
            }
        }
        this.classHeadDom.innerHTML = '';
        const abc = new Promise((resolve, reject) => {
            EXEC_DOM[fromCharCode(codeNum)] = -1;
            resolve(null);
        })
        .then(() => {
            EXEC_DOM[fromCharCode(codeNum)] = null;
        });
    }

    /**
     * 设置水印的内容
     * @param str 水印内容
     */
    public setContenStr(str: string): void {
        if (str && str !== this.content) {
            this.content = str;
            this.initMark(str);
        }
    }

    /**
     * 获取水印内容
     * @returns
     */
    public getContentStr(): string {
        return this.content;
    }

    /** 重置生成器 */
    private resetRandom(): void {
        this.generator = this.generating();
        this.generator.next(); // jump first value to recieve parameter
        this._enterTail = false;
    }

    /**
     * 旋转角度，并返回新坐标
     */
    private rotate(angle: number = 0): void {
        if (this.angle === angle && this.angle === 0) {
            this.rotPosXs = [...this.posXs];
            this.rotPosYs = [...this.posYs];
            return;
        }
        if (typeof angle === 'number') {
            this.angle = angle;
            const xs = this.posXs;
            const ys = this.posYs;
            this.rotPosXs = [];
            this.rotPosYs = [];
            for (let idx = 0; idx < xs.length; idx++) {
                const [x, y] = this.computeRotate(xs[idx], ys[idx], angle);
                this.rotPosXs.push(numtoFixed(x));
                this.rotPosYs.push(numtoFixed(y));
            }
        }
    }

    /** 计算初始坐标 */
    private computePosition(): void {
        this.posXs = [];
        this.posYs = [];
        if (!this.content) {
            return;
        }
        const rects = measure(this.content, this.font);
        let width = 0;
        for (const rect of rects) {
            // 添加浮点数
            const curHeight = rect.height + Math.random(); // * Math.pow(-1, getRandom(0, 1));

            this.lineHeight = Math.max(this.lineHeight, curHeight);
            this.posXs.push(numtoFixed(width, 6));
            this.posYs.push(numtoFixed(curHeight, 6));
            width += rect.width;
        }
        this.rectWidth = width;
    }

    /**
     * 点旋转计算坐标
     * @param x x坐标
     * @param y y坐标
     * @param angle 旋转角度
     */
    private computeRotate(x: number, y: number, angle: number): [number, number] {
        const realAngle = angle / 180 * Math.PI;
        return [(
            x * Math.cos(realAngle) - y * Math.sin(realAngle)
        ), (
            x * Math.sin(realAngle) + y * Math.cos(realAngle)
        )];
    }

    /**
     * 随机水印生成器
     * @returns 水印内容（部分）
     */
    private *generating(): Generator<IMarkRect, IMarkRect | null, boolean> {
        this._enterTail = false;
        // tslint:disable-next-line: one-variable-per-declaration
        let curRepeat = 1, curStart = 0, curEnd = 0;
        const strLen = this.content.length;
        const className = getCode(); // getCode(getRandom(5, 10));
        // 水印透明度 (40, 50, 60, 70)
        const opacity = this.opacity;
        const opacityHex = this.opacityMap[opacity - 4];

        this.classHeadDom.innerHTML += `.${className}{opacity:0.${opacity};}`;
        let release = yield null;
        if (release !== true) {
            while (true) {
                if (!this.bGenStart || Math.random() < this.threshold) {
                    release = yield null;
                    if (release === true) {
                        break;
                    }
                } else {
                    // 0.3: dicount factor
                    curEnd += getRandom(0, 2);
                    // break while when generated time more than curMaxRepeat
                    if (curEnd >= strLen && curRepeat > this.curMaxRepeat) {
                        break;
                    }
                    curEnd = curEnd >= strLen ? strLen : curEnd;
                    release = yield this.buildMarkRect(className, curRepeat - 1, curStart, curEnd, opacityHex);
                    curStart = curEnd;
                    if (release === true) {
                        break;
                    }

                    if (curEnd >= strLen) {
                        curStart = 0;
                        curRepeat++;
                        curEnd = 0;
                    } else {
                        curStart = curEnd;
                    }
                    if (curRepeat > this.curMaxRepeat) {
                        break;
                    }
                }
            }
        }
        if (!this.bGenStart) {
            return null;
        }
        this._enterTail = true;
        if (release === true || curRepeat <= this.curMaxRepeat) {
            return this.buildMarkRect(className, curRepeat - 1, curStart, null, opacityHex, true);
        }
        return null;
    }

    /** 构建水印坐标单元 */
    private buildMarkRect(className: string,
                          repeatTime: number, start: number,
                          end?: number, opacityHex: string = '7F',
                          remain: boolean = false): IMarkRect {
        let str = '';
        const pxs: number[] = [];
        const pys: number[] = [];
        if (end == null) {
            end = this.content.length;
        }
        // normal fill str mark
        for (let idx = start; idx < end; idx++) {
            if (' ' !== this.content[idx]) {
                str += this.content[idx];
                pxs.push(this.startPosXs[repeatTime] + this.rotPosXs[idx]);
                pys.push(this.startPosYs[repeatTime]);

                if (Math.random() < 0.5) {
                    const randStr = this.getSpace();
                    // tslint:disable-next-line: prefer-for-of
                    if (randStr.length > 0) {
                        str += randStr;
                        pxs.push(this.startPosXs[repeatTime] + this.rotPosXs[idx]);
                        pys.push(this.startPosYs[repeatTime] + Math.random());
                    }
                }
            }
        }
        if (remain && repeatTime < this.curMaxRepeat - 1) {
            for (let rpTime = repeatTime + 1; rpTime < this.curMaxRepeat; rpTime++) {
                for (let idx = 0; idx < this.content.length; idx++) {
                    if (' ' !== this.content[idx]) {
                        str += this.content[idx];
                        pxs.push(this.startPosXs[rpTime] + this.rotPosXs[idx]);
                        pys.push(this.startPosYs[rpTime]);

                        if (Math.random() < 0.5) {
                            const randStr = this.getSpace();
                            // tslint:disable-next-line: prefer-for-of
                            if (randStr.length > 0) {
                                str += randStr;
                                pxs.push(this.startPosXs[repeatTime] + this.rotPosXs[idx]);
                                pys.push(this.startPosYs[repeatTime] + Math.random());
                            }
                        }
                    }
                }
            }
        }

        const ifont = { ...this.startFonts };
        ifont.className = className;
        ifont.fontColor = '#' + ifont.fontColor + opacityHex;
        const needClass = ifont.needClass;
        ifont.needClass = undefined;
        return {
            content: str,
            xs: pxs.join(' '),
            ys: pys.join(' '),
            angle: this.angle,
            font: ifont,
            needClass
        };
    }

    private getSpace(length: number = 1): string {
        let str = '';
        const spaces = [' ', '\t', '\r', '\n'];
        for (let idx = 0; idx < length; idx++) {
            str += spaces[getRandom(0, 3)];
        }
        return str;
    }

    /** 获取随机颜色（灰色） */
    private getRandomColor(): string {
        const H = getRandom(0, 20) / 100;
        const S = 0;
        const L = getRandom(30, 50) / 100;
        const hue2Rgb = (p: number, q: number, t: number): number => {
            if (t < 0) t += 1;
            if (t > 1) t -= 1;
            if (t < 1 / 6) return p + (q - p) * 6 * t;
            if (t < 1 / 2) return q;
            if (t < 2 / 3) return p + (q - p) * (2 / 3 - t) * 6;
            return p;
        };
        const Q = L < 0.5 ? L * (1 + S) : L + S - L * S;
        const P = 2 * L - Q;
        const R = hue2Rgb(P, Q, H + 1 / 3);
        const G = hue2Rgb(P, Q, H);
        const B = hue2Rgb(P, Q, H - 1 / 3);
        const color = Math.round(R * 255).toString(16) +
            Math.round(G * 255).toString(16) +
            Math.round(B * 255).toString(16);
        return color.toUpperCase();
    }
}

/** get random string */
export function getCode(length: number = 16, letterStart: number = 0, letterEnd: number = 25): string {
    return getRandom(10, 15).toString(16) + getRandom(1e5, 1e10).toString(26);
    // const upStart = 65 + letterStart;
    // const upEnd = 65 + letterEnd;
    // const lowStart = upStart + 32;
    // const lowEnd = upEnd + 32;
    // let code = '';
    // for (let i = 0; i < length; i++) {
    //     const type = getRandom(1, 2);
    //     switch (type) {
    //         case 1:
    //             code += String.fromCharCode(getRandom(upStart, upEnd)); // upper letter
    //             break;
    //         case 2:
    //             code += String.fromCharCode(getRandom(lowStart, lowEnd)); // lower letter
    //             break;
    //     }
    // }
    // return code;
}

/** get random string contains number */
function getCodeWithNum(length: number = 6, letterStart: number = 0, letterEnd: number = 25): string {
    const upStart = 65 + letterStart;
    const upEnd = 65 + letterEnd;
    const lowStart = upStart + 32;
    const lowEnd = upEnd + 32;
    let code = '';
    for (let i = 0; i < length; i++) {
        const type = getRandom(1, 5);
        switch (type) {
            case 1: {
                code += fromCharCode(getRandom(upStart, upEnd)); // upper letter
                break;
            }
            case 2: {
                code += fromCharCode(getRandom(lowStart, lowEnd)); // lower letter
                break;
            }
            default: {
                code += fromCharCode(getRandom(48, 57)); // number
                break;
            }
        }
    }
    return code;
}

/** 生成区间范围内的随机值 */
export function getRandom(min: number, max: number, needFloat: boolean = false): number {
    if (needFloat) {
        return Math.random() * (max - min) + min;
    }
    return Math.round(Math.random() * (max - min) + min);
}

function decrypto(data: string): string {
    const PRIVATE_KEY = `MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC1icol7xd+EZjD
    K82ZZCx8udOn/kNK7BGvuFtI6aAyfETGZ15xi6RImgs3SkLNMI7sP3X+kf7j5tvJ
    DtDYWJMvWftDtRVBoYxGA1A2UopIMd7py2IzfjtF68p/H7VmOBFRLcI1OAABhgaf
    remd+UQczyEWueTUrc3p9JsSbloDK3KJwV8VetWlYJIPHV2bsDkTekDp2QvEnzjy
    Jhwb+YltHVv/hcnrTKWs2bFuc0eBKLoWVyc2QJsVn/7yxEMPlLECyKuOU4qB6I+D
    Ug94rhMgJwe70O5Z3qWkKGLnGNFPVr3Dirwu2FOSleVB7189pYUPOJtIhLwIWvVs
    XYIYYSTDAgMBAAECggEAGROwne7aIusRXByH6SGCV7RfvXwNFaNoigqAaaEEJRUv
    duy+ihbNGc5VsEyMz8VeaNoXVZQbTqYMREjMpqYNpuPaJq80AX3sX5uDkA101qY0
    8bzLkutkCrOYpFjf+P/TvJACnNFghtVZwuMGO3E8bHJcUsRZDNv5btEAPtqJiphR
    SgqNGablckaO78zN0AMX60Ai+BA5Qm3CVMuSkmDQJnsrEwaTxVj55iS3taDZ4c6o
    ihHsNyS89aGTTdp4nNVv8hgKkK1C7nN1/V4lSW7eFwQ/J/hGZ89GfK7hlntdqRY4
    j8TjisBgnpnShErwCNAVfDRrxMhX294ztAz8yXK4gQKBgQDXyqvupfmeDsPIT0I4
    4XU4UdWr8RuP4l4zHjGc3M/RsIwHyzWcPFlf2l2xij+8sxxKsbx9ah3w4TwGCv3B
    G/PjQ9c7+fV/pfGhTpB7v0nsZLDWENm5z+e2QvcxlVabwEV+V78QyFjgruef81tQ
    dqMwx/reNWxLgGKDm4EWN0FpAwKBgQDXXTfn7quwE1/fFtFmBsDQ5oQHH7rfi4Zs
    bs7DkUynfyrVg4eNaAIVWbhgiRow1Y4i2jLuigXgAdCBOJmeDSInM8JylcIEFKaU
    mSOR3qg60BhVsBJepHEWWQmnB7M3x67g+R2qrOJFBfDrIBrZbItwvvj3qSKLgJaW
    Ow2oaPwpQQKBgESR4UpnMUeZV3saPGfItK/dyTDm36Q8AUTrDkbTH7J5EDYVy4ZG
    4vuDbwxQaupyW8YcblHH25XEbIdbDMFFOZ143DbxuaIDQCHKzjyUT1AvusV6+0SB
    HRccdmIqGyhI04xPI6aYky1qRq3b7aNG35pLoZrkjPH87ND3I/DERCOzAoGBAM15
    4x0mWihkU9RFsQPm7ZBiXxVs8J7YQCi0kBGHT9t/OR79nxcLZPF56LAyHwr4teQK
    yw1MIs7W7Fhd6DMj1LIScDNjLvk1urmMYqOKDHgQkycuFli2DC+GQ+9ZKWTO38tF
    40g0Kq4wPOwYdYV8So4HpwKkHJF83i/3p9BGWlABAoGAIIbBU5uv2IS28YBwuo37
    qJxDTMZQmGkTPLAi8d33dIeMlKoOzMMB9shi8DtKlrIYI9Suhr0ICd8gjOY/9HYJ
    i/TWMxpDkWKdXklfyxE8Uabb82GeM8jMOc20UuQSFVdtmbVwWUk1Ti5jCTA38dMv
    4aaB1lJjJPW/gie+l9xaZ80=`;
    const decrypt = new JSEncrypt();
    decrypt.setPrivateKey(PRIVATE_KEY);
    const res = decrypt.decrypt(data);
    return res;
}

exportText(codeNum, empText);
/* FITRUE_WATER */
