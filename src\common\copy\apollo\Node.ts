import {NodeType, ParagraphPro, PortionPro, ImagePro, TablePro, StructurePro, OMediaMathPro,
    RowPro, ColPro, G_LABEL} from './NodeType';
import TextProperty from '../../../model/core/TextProperty';
import ParaProperty from '../../../model/core/Paragraph/ParaProperty';
import ParaPortion from '../../../model/core/Paragraph/ParaPortion';
import { encodeTag } from '../DataConver';
import {ParaElementType} from '../../../model/core/Paragraph/ParagraphContent';
import Apollo from './index';

import { DateBoxFormat, EquationType, PARA_BUTTON_PROP } from '../../../common/commonDefines';
import { escapeXML } from '@/utils/xml';

export class Node {
    private _host: Apollo;
    // private scale: number;
    // private scaleY: number;
    private textTypes: number[];
    constructor(host: Apollo) {
        this._host = host;
        this.textTypes = [ParaElementType.ParaDrawing, ParaElementType.ParaMedEquation,

        ParaElementType.ParaNewControlBorder, ParaElementType.ParaEnd, ParaElementType.ParaButton];

    }

    public addPar(content: string): string {
        return `
        <w:${NodeType.P}>
            ${content}
        </w:${NodeType.P}>`;
    }

    public addPor(str: string): string {
        return `
        <w:${NodeType.R}>
            ${str}
        </w:${NodeType.R}>
        `;
    }

    public addImage(item: any): string {
        // let equationElem = '';
        // if (item.equationElem) {
        //     equationElem = encodeURIComponent(item.equationElem);
        // }
        return `
        <w:${NodeType.Drawing} ${ImagePro.Src}="${item.src}" ${ImagePro.Width}="${item.width}"
            ${ImagePro.Height}="${item.height}" ${ImagePro.Name}="${item.name}" ${ImagePro.SizeProtect}="${
            item.sizeLocked === true ? 1 : 0}" ${ImagePro.Type}="${item.type}"
            ${ImagePro.PreferRelativeResize}="${item.preserveAspectRatio === true ? 1 : 0}"
            ${ImagePro.DeleteProtect}="${item.deleteLocked === true ? 1 : 0}" ${ImagePro.CopyProtect}="${
            item.copyLocked === true ? 1 : 0}"></w:${NodeType.Drawing}>
        `;
    }

    public addButton(item: any): string {
        const { content, bPrint, name, color} = item;
        let result = '';
        if (bPrint === true) {
            result = ` ${StructurePro.BPrint}="1"`;
        }

        if (color !== PARA_BUTTON_PROP.color) {
            result = ` ${StructurePro.Color}="${color}"`;
        }

        return `<w:${NodeType.Button} name="${name}"${result}>${escapeXML(content) || ''}</w:${NodeType.Button}>`;
    }

    public addMedEquation(item: any): string {
        let equationElem = '';
        if (item.equationElem) {
            equationElem = encodeURIComponent(item.equationElem);
        }
        // <${NodeType.MathText}>${equationElem}</${NodeType.MathText}>
        // ${ImagePro.SecondaryType}="${
        //     item.equationType === undefined ? '' : item.equationType}" ${ImagePro.EquationElem}="${equationElem}"
        return `
        <w:${NodeType.OMediaMath} ${OMediaMathPro.Width}="${item.width}"
            ${OMediaMathPro.Height}="${item.height}" ${OMediaMathPro.Name}="${item.name}"
            ${ImagePro.SizeProtect}="${item.sizeLocked === true ? 1 : 0}"
            ${OMediaMathPro.MathType}="${item.equationType}" ${ImagePro.Src}="${item.src}"
            ${ImagePro.PreferRelativeResize}="${item.preserveAspectRatio === true ? 1 : 0}"
            ${ImagePro.DeleteProtect}="${item.deleteLocked === true ? 1 : 0}" ${ImagePro.CopyProtect}="${
            item.copyLocked === true ? 1 : 0}" ${ImagePro.Type}="${item.type}">
                <${NodeType.MathValue}>${equationElem}</${NodeType.MathValue}>
                <${NodeType.MathText}></${NodeType.MathText}>
        </w:${NodeType.OMediaMath}>`;
    }

    public addContent(content: string): string {
        content = encodeTag(content);
        return `<w:${NodeType.T}>${content}</w:${NodeType.T}>`;
    }

    public addStruct(node: any): string {
        // node.name = node.name.replace(/(_start|_end)+$/, '');
        // if (node.bStart !== true) {
        //     return this.getNewControlString(undefined, node);
        // }
        const newControl = this._host.getNewControlByName(node.getNewControlName());
        if (!newControl) {
            return '';
        }
        this._host.addNewControlProperty(newControl);
        return this.getNewControlString(newControl, node);
    }

    public addRegion(node: any, content: string): string {
        const regionName = node.getName();
        const region = this._host.addRegionPropsByName(regionName);
        if (!region) {
            return;
        }
        let str = `
            <${NodeType.Hidden}>${node['bHidden']}</${NodeType.Hidden}>
            <${StructurePro.Name}>${region.newControlName}</${StructurePro.Name}>
            <${StructurePro.SerialNumber}>${region.newControlSerialNumber || ''}</${StructurePro.SerialNumber}>
            <${StructurePro.DeleteProtect}>${region.isNewControlCanntDelete ? 1 : 0}</${StructurePro.DeleteProtect}>
            <${StructurePro.EditProtect}>${region.isNewControlCanntEdit ? 1 : 0}</${StructurePro.EditProtect}>
            <${StructurePro.Title}>${region.newControlTitle || ''}</${StructurePro.Title}>
            <${StructurePro.TipsContent}>${region.newControlInfo || ''}</${StructurePro.TipsContent}>
            <${StructurePro.ReverseEdit}>${region.isNewControlReverseEdit ? 1 : 0}</${StructurePro.ReverseEdit}>
            <${StructurePro.IsHidden}>${region.isNewControlHidden ? 1 : 0}</${StructurePro.IsHidden}>
            <${StructurePro.ShowTitle}>${region.showTitle ? 1 : 0}</${StructurePro.ShowTitle}>
            <${StructurePro.MaxLength}>${region.newControlMaxLength}</${StructurePro.MaxLength}>
        `;
        if (region.customProperty && !region.customProperty.length) {
            const props = region.customProperty.map((item) => {
                return {
                    name: item.name,
                    value: item.value,
                    type: item.type,
                };
            });
            str += `<${StructurePro.CustomProperty}>${JSON.stringify(props)}</${StructurePro.CustomProperty}>`;
        }

        const identifier = region.identifier;
        if (identifier != null) {
            str += `<${StructurePro.Identifier}>${identifier}</${StructurePro.Identifier}>`;
        }

        return `<w:${NodeType.Region} name=${regionName}>
            <w:${NodeType.RegionPr}>${str}</w:${NodeType.RegionPr}>
            ${content}
        </w:${NodeType.Region} name=${regionName}>`;
    }

    public addText(portion: ParaPortion): string {
        let result = '';
        let content = '';
        const types: number[] = this.textTypes;
        let bBorderPortion: boolean;
        portion.content.forEach((item) => {
            if (types.includes(item.type)) {
                if (content) {
                    result += this.addContent(content);
                }
                content = '';
            }

            switch (item.type) {
                case ParaElementType.ParaEnd: {
                    return;
                }
                case ParaElementType.ParaDrawing:
                    if ((item as any).equationType === EquationType.EditableSvg) {
                        result += this.addMedEquation(item);
                        break;
                    }
                    result += this.addImage(item);
                    break;
                case ParaElementType.ParaMedEquation:
                    result += this.addMedEquation(item);
                    break;
                case ParaElementType.ParaNewControlBorder:
                    bBorderPortion = true;
                    result += this.addStruct(item);
                    break;
                case ParaElementType.ParaButton:
                    result += this.addButton(item);
                    break;
                default:
                    if (!bBorderPortion) {
                        content += item.content;
                    }
            }
        });

        if (content) {
            result += this.addContent(content);
        }

        return result;
    }

    public addParStyle(attrs: ParaProperty, bHidden: boolean): string {
        let res = '';
        if (attrs.alignment !== undefined) {
            res += `<w:${NodeType.Jc} ${ParagraphPro.Jc}="${attrs.alignment}"></w:${NodeType.Jc}>`;
        }
        res += `<w:${NodeType.Hidden} ${NodeType.Hidden}="${bHidden}"></w:${NodeType.Hidden}>`;
        if (attrs.paraSpacing !== undefined) {
            res += `<w:${NodeType.Spacing} ${ParagraphPro.Spacing}="${attrs.paraSpacing.lineSpacing}"
                ></w:${NodeType.Spacing}>`;
            res += `<${ParagraphPro.SpacingType} ${ParagraphPro.SpacingType}="${attrs.paraSpacing.lineSpacingType}"
            ></${ParagraphPro.SpacingType}>`;
        }
        if (attrs.paraInd !== undefined) {
            res += `<w:${NodeType.Ind} ${ParagraphPro.Ind}="${attrs.paraInd.left}"></w:${NodeType.Ind}>`;
        }

        return `<w:${NodeType.PPr}>${res}</w:${NodeType.PPr}>`;
    }

    public addPorStyle(attrs: TextProperty, bHidden: boolean): string {
        let res = '';
        res += `<w:${NodeType.Hidden} ${NodeType.Hidden}="${bHidden}"></w:${NodeType.Hidden}>`;
        if (attrs.fontWeight) {
            res += `<w:${NodeType.B}></w:${NodeType.B}>`;
        }
        if (attrs.fontStyle === 1) {
            res += `<w:${NodeType.I}></w:${NodeType.I}>`;
        }
        if (attrs.textDecorationLine === 1) {
            res += `<w:${NodeType.U} ${PortionPro.U}></w:${NodeType.U}>`;
        }
        if (attrs.backgroundColor) {
            res += `<w:${NodeType.Bg} ${PortionPro.Bg}="${attrs.backgroundColor}"
                ></w:${NodeType.Bg}>`;
        }
        if (attrs.fontFamily) {
            res += `<w:${NodeType.Family} ${PortionPro.Family}="${attrs.fontFamily.replace(/'|"/g, '')}"
                ></w:${NodeType.Family}>`;
        }
        if (attrs.color) {
            res += `<w:${NodeType.Color} ${PortionPro.Color}="${attrs.color}"
                ></w:${NodeType.Color}>`;
        }
        if (attrs.fontSize) {
            res += `<w:${NodeType.Size} ${PortionPro.Size}="${attrs.fontSize}"></w:${NodeType.Size}>`;
        }
        if (attrs.vertAlign) {
            res += `<w:${NodeType.VertAlign} w:${PortionPro.VertAlign}="${attrs.vertAlign}"></w:${NodeType.VertAlign}>`;
        }
        return `<w:${NodeType.RPr}>${res}</w:${NodeType.RPr}>`;
    }

    // private parseRgbColor(color: string): string {
    //     let strHex: string;
    //     if (/^rgb/i.test (color)) {
    //         const arr = color.replace(/(?:\(|\)|rgb|RGB)*/g, "").split(",");
    //         strHex = ((1 << 24) + (arr[0] << 16) + (arr[1] << 8) + parseInt(arr[2])).toString(16).substr(1);
    //     } else {
    //         strHex = color.replace('#', '');
    //     }
    //     return strHex;
    // }

    public addTable(content: string): string {
        return `<w:${NodeType.Tbl}>${content}</w:${NodeType.Tbl}>`;
    }

    public addTableStyle(cols: number[], table: any): string {
        if (cols.length === 0) {
            return '';
        }
        let res = '';
        cols.forEach((col, index) => {
            const name = TablePro.Col + index;
            res += `<${name} ${TablePro.W}="${col}"></${name}>`;
        });
        res = `<${NodeType.GridCol}>${res}</${NodeType.GridCol}>`;
        return `<w:${NodeType.TblPr}>${res + this.addTablePro(table)}</w:${NodeType.TblPr}>`;
    }

    /**
     * name
     */
    public addRow(content: string): string {
        return `<w:${NodeType.Row}>${content}</w:${NodeType.Row}>`;
    }

    public addRowStyle(row: any): string {
        // if (!row.height) {
        //     return '';
        // }
        let res = '';
        const props = row.property;
        if (props) {
            res += `<${RowPro.CanSplit}>${props.bCanSplit ? 1 : 0}</${RowPro.CanSplit}>`;
            res += `<${RowPro.TableHeader}>${props.TableHeader ? 1 : 0}</${RowPro.TableHeader}>`;
        }
        const height = row.property.height;
        return `<w:${NodeType.RowPr}  ${TablePro.Type}="dxa">
            <w:${NodeType.Height} ${TablePro.Val}="${height.value}"
                ${RowPro.Rule}="${height.hRule}"></w:${NodeType.Height}>
            ${res}
        </w:${NodeType.RowPr}>`;
    }

    public addCol(content: string): string {
        return `<w:${NodeType.Col}>${content}</w:${NodeType.Col}>`;
    }

    public addColStyle(pro: any, borders: any): string {
        const tableBorders: any = pro.borders;
        let border = '';
        border += this.addTableBoder(ColPro.BorderLeft, tableBorders);
        border += this.addTableBoder(ColPro.BorderRight, tableBorders);
        border += this.addTableBoder(ColPro.BorderBottom, tableBorders);
        border += this.addTableBoder(ColPro.BorderTop, tableBorders);
        if (border) {
            border = `<${ColPro.Border}>${border}</${ColPro.Border}>`;
        }
        // border = `<${ColPro.Border}>${border}</${ColPro.Border}>`;
        // border += `<${ColPro.BottomBeforeCount}>${tableBorders.bottomBeforeCount}</${ColPro.BottomBeforeCount}>`;
        // border += `<${ColPro.BottomAfterCount}>${tableBorders.bottomAfterCount}</${ColPro.BottomAfterCount}>`;
        // border += `<${ColPro.MaxLeft}>${tableBorders.maxLeft}</${ColPro.MaxLeft}>`;
        // border += `<${ColPro.MaxRight}>${tableBorders.maxRight}</${ColPro.MaxRight}>`;

        return `<w:${NodeType.ColPr}>
            <w:${NodeType.Width} ${TablePro.W}="${pro.cellWidth.width}" ${TablePro.Type}="auto"
                ${TablePro.Span}="${pro.gridSpan}">
            </w:${NodeType.Width}>
            <${ColPro.Protected}>${pro.bProtected ? 1 : 0}</${ColPro.Protected}>
            <w:${NodeType.Merge} ${TablePro.Val}="${pro.verticalMerge}"></w:${NodeType.Merge}>
            ${border}
        </w:${NodeType.ColPr}>`;
    }

    // private getNewControl(name: string): NewControl {
    //     const newControls = this._host.getSelectedNewControls();
    //     const newControl: NewControl = newControls.find((control) => control.getNewControlName() === name);
    //     return newControl;
    // }

    private getNewControlString(newControl: any, node: any): string {

        const name = node.getNewControlName();
        let str = `${StructurePro.Name}="${name}"`;

        let nodeName = NodeType.SdtStart;
        str += ` ${StructurePro.Type}="${newControl.getType()}">`;
        if (node.bStart === false) {
            // str += '>';
            nodeName = NodeType.SdtEnd;
        } else {
            nodeName = NodeType.SdtStart;
            const bHideBg = newControl.isHiddenBackground() ? 0 : 1;
            const bPlaceHolder = newControl.isPlaceHolderContent() ? 1 : 0;
            str += `
                <${StructurePro.IsMustFill}>${newControl.isMustInput() ? 1 : 0}</${StructurePro.IsMustFill}>
                <${StructurePro.DeleteProtect}>${newControl.isDeleteProtect() ? 1 : 0}</${StructurePro.DeleteProtect}>
                <${StructurePro.EditProtect}>${newControl.isEditProtect() ? 1 : 0}</${StructurePro.EditProtect}>
                <${StructurePro.CopyProtect}>${newControl.isCopyProtect() ? 1 : 0}</${StructurePro.CopyProtect}>
                <${StructurePro.ShowBackground}>${bHideBg}</${StructurePro.ShowBackground}>
                <${StructurePro.Placeholder}>${newControl.getPlaceHolderContent()}</${StructurePro.Placeholder}>
                <${StructurePro.IsPlaceholder}>${bPlaceHolder}</${StructurePro.IsPlaceholder}>
                <${StructurePro.SerialNumber}>${newControl.getSerialNumber() || ''}</${StructurePro.SerialNumber}>
                <${StructurePro.ShowBorder}>${newControl.isShowBorder() ? 1 : 0}</${StructurePro.ShowBorder}>
                <${StructurePro.ReverseEdit}>${newControl.isReverseEdit() ? 1 : 0}</${StructurePro.ReverseEdit}>
                <${StructurePro.IsHidden}>${newControl.isHidden() ? 1 : 0}</${StructurePro.IsHidden}>
                <${StructurePro.DisplayType}>${newControl.getViewSecretType()}</${StructurePro.DisplayType}>
                <${StructurePro.TipsContent}>${newControl.getTipsContent() || ''}</${StructurePro.TipsContent}>
                <${StructurePro.MaxLength}>${newControl.getMaxLength()}</${StructurePro.MaxLength}>
                <${StructurePro.TabJump}>${newControl.isTabJump()}</${StructurePro.TabJump}>
            `;
            const fixedLength = newControl.getFixedLength();
            if (typeof fixedLength === 'number') {
                str += `<${StructurePro.FixedLength}>${fixedLength}</${StructurePro.FixedLength}>`;
            }
            const bTextBorder = newControl.isTextBorder();
            if (bTextBorder != null) {
                str += `<${StructurePro.BTextBorder}>${bTextBorder ? 1 : 0}</${StructurePro.BTextBorder}>`;
            }
            const alignments = newControl.getAlignments();
            if (typeof alignments === 'number') {
                str += `<${StructurePro.Alignments}>${alignments}</${StructurePro.Alignments}>`;
            }
            // const eventInfo = newControl.getEvent();
            // if (eventInfo && typeof eventInfo === 'object') {
            //     str += `<${StructurePro.EventInfo}>${JSON.stringify(eventInfo)}</${StructurePro.EventInfo}>`;
            // }
            const identifier = newControl.getIdentifier();
            if (identifier != null) {
                str += `<${StructurePro.Identifier}>${identifier}</${StructurePro.Identifier}>`;
            }
            let retrieve: string = '';
            if (newControl['getRetrieve']) {
                const bRetrieve = newControl['getRetrieve']();
                retrieve = `<${StructurePro.Retrieve}>${bRetrieve ? 1 : 0}</${StructurePro.Retrieve}>`;
                const selectPrefixContent = newControl.getSelectPrefixContent();
                if (selectPrefixContent !== undefined) {
                    retrieve +=
                    `<${StructurePro.SelectPrefixContent}>${selectPrefixContent}</${StructurePro.SelectPrefixContent}>`;
                }

                const prefixContent = newControl.getPrefixContent();
                if (prefixContent !== undefined) {
                    retrieve += `<${StructurePro.PrefixContent}>${prefixContent}</${StructurePro.PrefixContent}>`;
                }

                const separator = newControl.getSeparator();
                if (separator !== undefined) {
                    retrieve += `<${StructurePro.Separator}>${separator}</${StructurePro.Separator}>`;
                }

                let codeLabel = newControl.getLabelCode();
                if (codeLabel !== undefined) {
                    if (!codeLabel) {
                        codeLabel = G_LABEL;
                    }
                    retrieve += `<${StructurePro.CodeLabel}>${codeLabel}</${StructurePro.CodeLabel}>`;
                }
                let valueLabel = newControl.getValueLabel();
                if (valueLabel !== undefined) {
                    if (!valueLabel) {
                        valueLabel = G_LABEL;
                    }
                    retrieve += `<${StructurePro.ValueLabel}>${valueLabel}</${StructurePro.ValueLabel}>`;
                }
            }

            const showCAV = newControl.isShowCodeAndValue();
            if (showCAV != null) {
                retrieve += `<${StructurePro.BShowCodeAndValue}>${showCAV ? 1 : 0}</${StructurePro.BShowCodeAndValue}>`;
            }

            str += retrieve;

            const obj = newControl.getCascadeManager()?.getCascade(name);
            if (obj) {
                const {cascades, events} = obj;
                const newObj = {};
                if (cascades?.length) {
                    newObj['cascades'] = cascades;
                }
                if (events && Object.keys(events).length) {
                    newObj['events'] = events;
                }
                if (Object.keys(newObj).length) {
                    str += `<${StructurePro.Cascades}>${JSON.stringify(newObj)}</${StructurePro.Cascades}>`;
                }

                // .replace(/[<>]/g, function (c) {
                //     switch (c) {
                //       case '>': 
                //         return '&gt;';
                //       case '<': 
                //         return '&lt;';
                //     }
                //   })
            }
            
            if (newControl.getItemList) {
                const items = newControl.getItemList();
                if (items && items.length > 0) {
                    str +=
                    `<${StructurePro.CodeValueItems}>${JSON.stringify(items.map((item) => {
                        return {
                            code: item.code,
                            value: item.value,
                            bSelect: item.bSelect,
                        };
                    }))}</${StructurePro.CodeValueItems}>`;
                }
            }
            let minValue: string = '';
            if (newControl['getMinValue']) {
                const min = newControl['getMinValue']();
                if (min !== undefined) {
                    minValue = `<${StructurePro.MinValue}>${min}</${StructurePro.MinValue}>`;
                }
                const max = newControl['getMaxValue']();
                if (max !== undefined) {
                    minValue += `<${StructurePro.MaxValue}>${max}</${StructurePro.MaxValue}>`;
                }
                const unit = newControl['getUnit']();
                if (unit !== undefined) {
                    minValue += `<${StructurePro.Unit}>${unit}</${StructurePro.Unit}>`;
                }

                const precision = newControl['getPrecision']();
                if (precision !== undefined) {
                    minValue += `<${StructurePro.Precision}>${precision}</${StructurePro.Precision}>`;
                }

                const forceValidate = newControl['getForceValidate']();
                if (forceValidate !== undefined) {
                    minValue +=
                        `<${StructurePro.ForceValidate}>${forceValidate ? 1 : 0}</${StructurePro.ForceValidate}>`;
                }
            }
            str += minValue;
            const customProperty = newControl.getCustomProps();
            let customStr: string = '';
            if (customProperty !== undefined && customProperty.length > 0) {
                const props = customProperty.map((item) => {
                    return {
                        name: item.name,
                        value: item.value,
                        type: item.type,
                    };
                });
                customStr = `<${StructurePro.CustomProperty}>${JSON.stringify(props)}</${StructurePro.CustomProperty}>`;
            }
            str += customStr;
            // 根据最新需求，级联不参与拷贝粘贴
            // const cascade = newControl.getCascades();
            // if (cascade && cascade.length > 0) {
            //     str += `<${StructurePro.LogicEvent}>${JSON.stringify(cascade)}</${StructurePro.LogicEvent}>`;
            // }

            if (newControl.getDateBoxFormat) {
                const format = newControl.getDateBoxFormat();
                if (format !== undefined) {
                    str += `<${StructurePro.DateBoxFormat}>${format}</${StructurePro.DateBoxFormat}>`;
                    if (format === DateBoxFormat.AutoCustom) {
                        str += `<${StructurePro.AutoDateFormat}>${JSON.stringify(
                            newControl.getCustomDateFormat())}</${StructurePro.AutoDateFormat}>`;
                    }
                }
            }

            if (newControl.getDateTime) {
                const dateTime = newControl.getDateTime();
                str += `<${StructurePro.DateTime}>${dateTime}</${StructurePro.DateTime}>`;
            }

            const title = newControl.getTitle();
            if (title !== undefined) {
                str += `<${StructurePro.Title}>${title}</${StructurePro.Title}>`;
            }

            if (newControl.isCheckBox()) {
                const label = newControl.getCheckLabel();
                if (label) {
                    str += `<${StructurePro.Label}>${label}</${StructurePro.Label}>`;
                }
                const checked = newControl.isSelected();
                str += `<${StructurePro.Checked}>${checked ? 1 : 0}</${StructurePro.Checked}>`;

                // get label code
                if (newControl.getLabelCode) {
                    const labelCode = newControl.getLabelCode();
                    str += `<${StructurePro.LabelCode}>${labelCode}</${StructurePro.LabelCode}>`;
                }
            }
            if (newControl.isShowRight) {
                const showRight = newControl.isShowRight();
                str += `<${StructurePro.ShowRight}>${showRight ? 1 : 0}</${StructurePro.ShowRight}>`;
            }

            if (newControl.isShowValue) {
                const showValue = newControl.isShowValue();
                str += `<${StructurePro.ShowValue}>${showValue ? 1 : 0}</${StructurePro.ShowValue}>`;
            }

            if (newControl.isPrintSelected) {
                const printSelected = newControl.isPrintSelected();
                str += `<${StructurePro.PrintSelected}>${printSelected ? 1 : 0}</${StructurePro.PrintSelected}>`;
            }

            if (newControl.getSpaceNum) {
                const spaceNum = newControl.getSpaceNum();
                str += `<${StructurePro.SpaceNum}>${spaceNum}</${StructurePro.SpaceNum}>`;
            }


            if (newControl.isSupportMultLines) {
                const supportMultLines = newControl.isSupportMultLines();
                str += `<${StructurePro.SupportMultLines}>${supportMultLines ? 1 : 0}</${StructurePro.SupportMultLines}>`;
            }

            if (newControl.getShowType) {
                const showType = newControl.getShowType();
                str += `<${StructurePro.ShowType}>${showType}</${StructurePro.ShowType}>`;
            }

            if (newControl.getStartDate) {
                const startDate = newControl.getStartDate();
                str += `<${StructurePro.StartDate}>${startDate || ''}</${StructurePro.StartDate}>`;
            }

            if (newControl.getEndDate) {
                const endDate = newControl.getEndDate();
                str += `<${StructurePro.EndDate}>${endDate || ''}</${StructurePro.EndDate}>`;
            }

            if (newControl.getSignatureCount) {
                const signatureCount = newControl.getSignatureCount();
                str += `<${StructurePro.SignatureCount}>${signatureCount}</${StructurePro.SignatureCount}>`;
            }

            if (newControl.getPreText) {
                const preText = newControl.getPreText();
                str += `<${StructurePro.PreText}>${preText}</${StructurePro.PreText}>`;
            }

            if (newControl.getSignatureSeparator) {
                const signatureSeparator = newControl.getSignatureSeparator();
                str += `<${StructurePro.SignatureSeparator}>${signatureSeparator}</${StructurePro.SignatureSeparator}>`;
            }

            if (newControl.getPostText) {
                const postText = newControl.getPostText();
                str += `<${StructurePro.PostText}>${postText}</${StructurePro.PostText}>`;
            }

            if (newControl.getSignaturePlaceholder) {
                const signaturePlaceholder = newControl.getSignaturePlaceholder();
                // tslint:disable-next-line: max-line-length
                str += `<${StructurePro.SignaturePlaceholder}>${signaturePlaceholder}</${StructurePro.SignaturePlaceholder}>`;
            }

            if (newControl.getSignatureRatio) {
                const signatureRatio = newControl.getSignatureRatio();
                str += `<${StructurePro.SignatureRatio}>${signatureRatio}</${StructurePro.SignatureRatio}>`;
            }

            if (newControl.getRowHeightRestriction) {
                const rowHeightRestriction = newControl.getRowHeightRestriction();
                // tslint:disable-next-line: max-line-length
                str += `<${StructurePro.RowHeightRestriction}>${rowHeightRestriction ? 1 : 0}</${StructurePro.RowHeightRestriction}>`;
            }

            if (newControl.getProvince) {
                const province = newControl.getProvince();
                str += `<${StructurePro.Province}>${JSON.stringify(province)}</${StructurePro.Province}>`;
            }

            if (newControl.getCity) {
                const city = newControl.getCity();
                str += `<${StructurePro.City}>${JSON.stringify(city)}</${StructurePro.City}>`;
            }

            if (newControl.getCounty) {
                const county = newControl.getCounty();
                str += `<${StructurePro.County}>${JSON.stringify(county)}</${StructurePro.County}>`;
            }

            if (newControl.getHierarchy) {
                const hierarchy = newControl.getHierarchy();
                str += `<${StructurePro.Hierarchy}>${hierarchy}</${StructurePro.Hierarchy}>`;
            }

            if (newControl.getSignType) {
                const signType = newControl.getSignType();
                // tslint:disable-next-line: max-line-length
                str += `<${StructurePro.SignType}>${signType}</${StructurePro.SignType}>`;
            }

            if (newControl.getAlwaysShow) {
                const alwaysShow = newControl.getAlwaysShow();
                str += `<${StructurePro.AlwaysShow}>${alwaysShow}</${StructurePro.AlwaysShow}>`;
            }

            if (newControl.getShowSignBorder) {
                const showSignBorder = newControl.getShowSignBorder();
                // tslint:disable-next-line: max-line-length
                str += `<${StructurePro.ShowSignBorder}>${showSignBorder ? 1 : 0}</${StructurePro.ShowSignBorder}>`;
            }

            if (newControl.getHideHasTitle) {
                const hideHasTitle = newControl.getHideHasTitle();
                // tslint:disable-next-line: max-line-length
                str += `<${StructurePro.HideHasTitle}>${hideHasTitle ? 1 : 0}</${StructurePro.HideHasTitle}>`;
            }

        }

        // console.log(str)

        return `<w:${nodeName} ${str}</w:${nodeName}>`;
    }

    private addTablePro(table: any): string {
        let res = '';
        const props = table.property;
        if (props) {
            const margins = props.tableCellMargin;
            const width = margins.top.width + margins.bottom.width;
            const rowsAuto: boolean[] = [];
            const rowHeights: number[] = [];
            table.content.forEach((row) => {
                let height = row.getCurrentRowHeight() + width;
                height = height / 10;
                rowHeights.push(parseFloat(height.toFixed(2)));

                const rowHeight = row.getHeight();
                rowsAuto.push(!rowHeight.isFixed());
            });
            res += `<${TablePro.AddRowProtect}>${props.bCanAddRow ? 0 : 1}</${TablePro.AddRowProtect}>`;
            res += `<${TablePro.DelRowProtect}>${props.bCanDeleteRow ? 0 : 1}</${TablePro.DelRowProtect}>`;
            res += `<${TablePro.FixedRowHeight}>${props.bFixedRowHeight ? 0 : 1}</${TablePro.FixedRowHeight}>`;
            res += `<${TablePro.FixedColWidth}>${props.bFixedColWidth ? 0 : 1}</${TablePro.FixedColWidth}>`;
            res += `<${TablePro.DeleteProtect}>${props.bDeleteProtect ? 1 : 0}</${TablePro.DeleteProtect}>`;
            res += `<${TablePro.EditProtect}>${props.bReadOnly ? 1 : 0}</${TablePro.EditProtect}>`;
            res += `<${TablePro.HeaderNum}>${table.getTableRowsHeaderCount() || 0}</${TablePro.HeaderNum}>`;
            res += `<${TablePro.RowsAuto}>${rowsAuto.join(',')}</${TablePro.RowsAuto}>`;
            res += `<${TablePro.RowHeights}>${rowHeights.join(',')}</${TablePro.RowHeights}>`;
            res += `<${TablePro.BorderLineSize}>${table.getBorderLineSize()}</${TablePro.BorderLineSize}>`;
            res += `<${TablePro.BorderColor}>${table.getBorderLineColor()}</${TablePro.BorderColor}>`;
            res += `<${TablePro.CellsVertAlign}>${table.getCellsVertAlign()}</${TablePro.CellsVertAlign}>`;
            const cellMargin = margins;
            res += `<${TablePro.CellLeft}>${cellMargin.left.width}</${TablePro.CellLeft}>`;
            res += `<${TablePro.CellRight}>${cellMargin.right.width}</${TablePro.CellRight}>`;
            res += `<${TablePro.CellTop}>${cellMargin.top.width}</${TablePro.CellTop}>`;
            res += `<${TablePro.CellBottom}>${cellMargin.bottom.width}</${TablePro.CellBottom}>`;

            const tableBorders = props.tableBorders;
            let border = '';
            border += this.addTableBoder(ColPro.BorderLeft, tableBorders);
            border += this.addTableBoder(ColPro.BorderTop, tableBorders);
            border += this.addTableBoder(ColPro.BorderRight, tableBorders);
            border += this.addTableBoder(ColPro.BorderBottom, tableBorders);
            border += this.addTableBoder(ColPro.BorderInsideH, tableBorders);
            border += this.addTableBoder(ColPro.BorderInsideV, tableBorders);
            if (border) {
                res += `<${ColPro.Border}>${border}</${ColPro.Border}>`;
            }

            const customProperty = props.customProperty;
            if (customProperty !== undefined && customProperty.size > 0) {
                const arrs = [];
                for (const [key, value] of customProperty) {
                    arrs.push(value);
                }
                res += `<${TablePro.CustomProperty}>${JSON.stringify(arrs)}</${TablePro.CustomProperty}>`;
            }
        }

        return res;
    }

    private addTableBoder(nodeName: string, props: any): string {
        const actBorder = props[nodeName];
        if (!actBorder) {
            return '';
        }
        let borders;
        if (actBorder.length) {
            borders = actBorder;
        } else {
            borders = [actBorder];
        }
        let res = '';
        borders.forEach((border) => {
            const actColor = border.color;
            const color = `${actColor.r},${actColor.g},${actColor.b}`;
            res += `<${nodeName} ${ColPro.Color}="${color}" ${ColPro.BAuto}="${border.bAuto ? 1 : 0}"
            ${ColPro.Style}="${border.value}" ${ColPro.Linetype}="${border.size}"
            ${ColPro.Visible}="${border.bVisible ? 1 : 0}" ${ColPro.Space}="${border.space}"
            ${ColPro.DashArray}="${border.dashArray || ''}" ${ColPro.BPrint}="${border.bPrint ? 1 : 0}"></${nodeName}>`;
        });

        return res;
    }
}
