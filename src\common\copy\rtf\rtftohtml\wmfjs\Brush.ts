import { PointS, Obj } from './Primitives';
import { ColorRef } from './Style';
import { PatternBitmap16, Bitmap16, DIBitmap } from './Bitmap';
import { Blob } from './Blob';
import { Helper } from './Helper';

export class Brush extends Obj {
    public style: number;
    public color: ColorRef;
    public pattern: Bitmap16;
    public colorusage: number;
    public dibpatternpt: DIBitmap;
    public hatchstyle: number;

    constructor(reader: Blob, copy: Brush | number, forceDibPattern?: boolean | PatternBitmap16) {
        super('brush');
        if (reader != null) {
            const dataLength = copy as number;
            const start = reader.pos;

            if (forceDibPattern === true || forceDibPattern === false) {
                this.style = reader.readUint16();
                if (forceDibPattern && this.style !== Helper.GDI.BrushStyle.BS_PATTERN) {
                    this.style = Helper.GDI.BrushStyle.BS_DIBPATTERNPT;
                }
                switch (this.style) {
                    case Helper.GDI.BrushStyle.BS_SOLID:
                        this.color = new ColorRef(reader);
                        break;
                    case Helper.GDI.BrushStyle.BS_PATTERN:
                        reader.skip(forceDibPattern ? 2 : 6);
                        this.pattern = new Bitmap16(reader, dataLength - (reader.pos - start));
                        break;
                    case Helper.GDI.BrushStyle.BS_DIBPATTERNPT:
                        this.colorusage = forceDibPattern ? reader.readUint16() : reader.readUint32();
                        if (!forceDibPattern) {
                            reader.skip(2);
                        }
                        this.dibpatternpt = new DIBitmap(reader, dataLength - (reader.pos - start));
                        break;
                    case Helper.GDI.BrushStyle.BS_HATCHED:
                        this.color = new ColorRef(reader);
                        this.hatchstyle = reader.readUint16();
                        break;
                }
            } else if (forceDibPattern instanceof PatternBitmap16) {
                this.style = Helper.GDI.BrushStyle.BS_PATTERN;
                this.pattern = forceDibPattern;
            }
        } else if (copy != null) {
            copy = copy as Brush;
            this.style = copy.style;
            switch (this.style) {
                case Helper.GDI.BrushStyle.BS_SOLID:
                    this.color = copy.color.clone();
                    break;
                case Helper.GDI.BrushStyle.BS_PATTERN:
                    this.pattern = copy.pattern.clone();
                    break;
                case Helper.GDI.BrushStyle.BS_DIBPATTERNPT:
                    this.colorusage = copy.colorusage;
                    this.dibpatternpt = copy.dibpatternpt;
                    break;
                case Helper.GDI.BrushStyle.BS_HATCHED:
                    this.color = copy.color.clone();
                    this.hatchstyle = copy.hatchstyle;
                    break;
            }
        }
    }

    public clone(): Brush {
        return new Brush(null, this);
    }

    public toString(): string {
        let ret = '{style: ' + this.style;
        switch (this.style) {
            case Helper.GDI.BrushStyle.BS_SOLID:
                ret += ', color: ' + this.color.toString();
                break;
            case Helper.GDI.BrushStyle.BS_DIBPATTERNPT:
                ret += ', colorusage: ' + this.colorusage;
                break;
            case Helper.GDI.BrushStyle.BS_HATCHED:
                ret += ', color: ' + this.color.toString() + ', hatchstyle: ' + this.hatchstyle;
                break;
        }
        return ret + '}';
    }
}

export class Pen extends Obj {
    public style: number;
    public width: PointS;
    public color: ColorRef;
    public linecap: number;
    public join: number;

    constructor(reader: Blob, style?: number, width?: PointS, color?: ColorRef, linecap?: number, join?: number) {
        super('pen');
        if (reader != null) {
            style = reader.readUint16();
            // tslint:disable-next-line: no-bitwise
            this.style = style & 0xFF;
            this.width = new PointS(reader);
            this.color = new ColorRef(reader);
            // tslint:disable-next-line: no-bitwise
            this.linecap = (style & (Helper.GDI.PenStyle.PS_ENDCAP_SQUARE | Helper.GDI.PenStyle.PS_ENDCAP_FLAT));
            // tslint:disable-next-line: no-bitwise
            this.join = (style & (Helper.GDI.PenStyle.PS_JOIN_BEVEL | Helper.GDI.PenStyle.PS_JOIN_MITER));
        } else {
            this.style = style;
            this.width = width;
            this.color = color;
            this.linecap = linecap;
            this.join = join;
        }
    }

    public clone(): Pen {
        return new Pen(null, this.style, this.width.clone(), this.color.clone(), this.linecap, this.join);
    }

    public toString(): string {
        return '{style: ' + this.style + ', width: ' + this.width.toString() + ', color: ' + this.color.toString()
            + ', linecap: ' + this.linecap + ', join: ' + this.join + '}';
    }
}
