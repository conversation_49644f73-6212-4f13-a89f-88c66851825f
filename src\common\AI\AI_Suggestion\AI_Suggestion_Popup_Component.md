# AI建议弹窗组件文档（编辑器内模块专用）

## 一、概述

AI建议弹窗组件是编辑器内部的一个独立模块，供各个子功能模块调用（如语义补全、自动续写、结构建议等）。其作用是在编辑器中，在光标位置附近弹出AI生成的文本建议，并支持键盘或鼠标交互选择插入。

---

## 二、功能设计（PRD 精炼版）

### 🎯 目标

- 提供可重用的建议弹窗组件
- 解耦各功能模块，统一展示交互行为
- 支持位置控制、键盘操作、插入确认等逻辑

---

### 📦 功能点一览

| 功能 | 描述 |
|------|------|
| 显示/隐藏弹窗 | 组件可由编辑器模块显式调用显示与关闭 |
| 智能定位 | 由调用方传入坐标及容器宽度，组件自动决定左下 or 右下对齐 |
| 展示内容 | 展示一组文本建议 |
| 键盘交互 | 支持 Tab/↓ 激活高亮，Enter 插入，Esc 取消 |
| 鼠标交互 | 鼠标点击某项选择插入，点击外部自动关闭 |
| 状态管理 | 内部管理选中项、高亮索引、是否可见状态 |
| 插入/取消回调 | 上层模块可绑定 `onAccept`, `onCancel`, `onSelect` 回调函数 |

---

### ⚙️ API 参数

#### `show(params)`

```ts
show({
  suggestions: string[],
  position: { x: number, y: number },
  containerWidth: number,
  onAccept: (item: string) => void,
  onCancel: () => void,
  onSelect?: (index: number) => void
})
```

#### `hide()`

隐藏当前弹窗。

---

## 三、弹窗定位逻辑

### 输入：
- `position.x`, `position.y`：光标相对编辑器容器的坐标
- `containerWidth`: 容器宽度

### 定位伪代码：

```ts
if (x + popupWidth < containerWidth - margin) {
  // 显示在左下（默认）
  left = x;
} else {
  // 改为右下对齐
  left = x - popupWidth;
}
top = y;
```

---

## 四、类设计结构（TypeScript 示例）

```ts
class AISuggestionPopup {
  private containerEl: HTMLElement;
  private suggestions: string[] = [];
  private highlightedIndex: number = -1;
  private visible: boolean = false;

  constructor(editorContainer: HTMLElement);

  show(params: {
    suggestions: string[],
    position: { x: number, y: number },
    containerWidth: number,
    onAccept: (item: string) => void,
    onCancel: () => void,
    onSelect?: (index: number) => void,
  }): void;

  hide(): void;

  handleKeyDown(e: KeyboardEvent): void; // 可供外部绑定
}
```

---

## 五、状态机（行为流）

```text
[隐藏状态]
   ↓ show()
[展示中]
   ↓ Tab / ↓
[高亮状态]
   ↓ Enter
[插入 → 隐藏]
   ↓ Esc / 输入其他字符
[取消 → 隐藏]
```

---

## 六、UI设计建议

- 背景：白色 + 投影阴影
- 建议项样式：hover 高亮，当前项加粗
- 插入反馈：支持外部插入预览（可选）
- 支持最大高度，超出滚动展示

---

## 七、调用方式示例（供编辑器模块使用）

```ts
editor.AISuggestion.show({
  suggestions: ['这个可以这样说', '也可以那样说'],
  position: { x: 120, y: 340 },
  containerWidth: 800,
  onAccept: (item) => editor.insertText(item),
  onCancel: () => editor.clearTempState(),
});
```

---

## 八、模块集成建议

- 注册为编辑器子模块：`editor.AISuggestion = new AISuggestionPopup(...)`
- 避免多实例挂载：建议维护为单例
- 可监听 `editorEventBus.emit('show-suggestion', {...})` 进行调用

---
