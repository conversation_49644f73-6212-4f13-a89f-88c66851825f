import {
    ALIGN_TYPE,
    CodeValueItem,
    INewControlProperty,
    rtNode,
    STD_START_DEFAULT,
} from '../../common/commonDefines';
import { safeDecodeURIComponent } from '../../common/commonMethods';
import { tReadCustomProperties } from '../miscellaneous';

export function readerStructProps(
    newControlProperty: INewControlProperty,
    textNode: any,
    runChild: rtNode,
    documentVersion?: number
): { borderStringStart: string; showPlaceholder: boolean, sdtContentNode: rtNode } {
    let borderStringStart: string;
    let showPlaceholder: boolean;
    let sdtContentNode: rtNode;
    switch (runChild.tagName) {
        case 'w:sdtcontent': {
            sdtContentNode = runChild;
            break;
        }
        case 'serialNumber': {
            if (textNode != null && typeof textNode === 'string') {
                newControlProperty.newControlSerialNumber = safeDecodeURIComponent(textNode, documentVersion);
            }
            break;
        }
        case 'identifier': {
            if (textNode != null && typeof textNode === 'string') {
                newControlProperty.identifier = safeDecodeURIComponent(textNode, documentVersion);
            }
            break;
        }

        case 'placeholder':
            if (textNode != null && typeof textNode === 'string') {
                // '' should catch as well
                newControlProperty.newControlPlaceHolder =
                    safeDecodeURIComponent(textNode, documentVersion);
            } else {
                newControlProperty.newControlPlaceHolder = '';
            }
            break;
        case 'codeLabel':
            if (textNode != null && typeof textNode === 'string') {
                newControlProperty.codeLabel =
                    safeDecodeURIComponent(textNode, documentVersion);
            } else {
                newControlProperty.codeLabel = '';
            }
            break;
        case 'valueLabel':
                if (textNode != null && typeof textNode === 'string') {
                    newControlProperty.valueLabel =
                        safeDecodeURIComponent(textNode, documentVersion);
                } else {
                    newControlProperty.valueLabel = '';
                }
                break;
        case 'isMustFill':
            if (textNode && typeof textNode === 'string') {
                newControlProperty.isNewControlMustInput =
                    textNode === '1' ? true : false;
            }
            break;

        case 'deleteProtect':
            if (textNode && typeof textNode === 'string') {
                newControlProperty.isNewControlCanntDelete =
                    textNode === '1' ? true : false;
            }
            break;

        case 'editProtect':
            if (textNode && typeof textNode === 'string') {
                newControlProperty.isNewControlCanntEdit =
                    textNode === '1' ? true : false;
            }
            break;

        case 'copyProtect':
            if (textNode && typeof textNode === 'string') {
                newControlProperty.isNewControlCanntCopy =
                    textNode === '1' ? true : false;
            }
            break;

        case 'showBorder':
            if (textNode && typeof textNode === 'string') {
                newControlProperty.isNewControlShowBorder =
                    textNode === '1' ? true : false;
            }
            break;
        case 'bTextBorder':
            if (textNode && typeof textNode === 'string') {
                newControlProperty.bTextBorder =
                    textNode === '1' ? true : false;
            }
            break;
        case 'bShowCodeAndValue':
            if (textNode && typeof textNode === 'string') {
                newControlProperty.bShowCodeAndValue =
                    textNode === '1' ? true : false;
            }
            break;

        case 'borderString':
            if (textNode != null && typeof textNode === 'string') {
                // console.log(textNode.nodeValue);
                borderStringStart = safeDecodeURIComponent(textNode, documentVersion);
            }
            break;

        case 'logicEvent': {
            if (textNode != null) {
                // console.log(textNode.nodeValue);
                try {
                    if (textNode) {
                        newControlProperty.cascade = JSON.parse(
                            (textNode as string)
                                .replace(/&amp;/g, '&')
                                .replace(/&lt;/g, '<')
                                .replace(/&gt;/g, '>')
                                .replace(/&quot;/g, '"')
                                .replace(/&apos;/g, '\'') as string
                        );
                    }
                    // newControlProperty.cascade = JSON.parse(textNode as string);
                } catch (error) {
                    //
                }
            }

            break;
        }

        case 'editReverse':
            if (textNode && typeof textNode === 'string') {
                newControlProperty.isNewControlReverseEdit =
                    textNode === '1' ? true : false;
            }
            break;

        case 'backgroundColorHidden':
            if (textNode && typeof textNode === 'string') {
                newControlProperty.isNewControlHiddenBackground =
                    textNode === '1' ? true : false;
            }
            break;

        case 'customProperty': {
            const customProperties = runChild.children;
            if (customProperties.length > 0) {
                // usually if in this block, length must be > 0

                const customPropertyArr =
                    tReadCustomProperties(customProperties, documentVersion);
                // console.log(customPropertyArr)
                newControlProperty.customProperty = customPropertyArr;
            }
            break;
        }

        case 'tabJump': {
            if (textNode != null && typeof textNode === 'string') {
                newControlProperty.tabJump = textNode === '1' ? true : false;
            }
            break;
        }

        case 'newControlHidden': {
            if (textNode != null && typeof textNode === 'string') {
                newControlProperty.isNewControlHidden =
                    textNode === '1' ? true : false;
            }
            break;
        }

        case 'listItems': {
            // let t1 = performance.now();
            const listItems = runChild.children;
            // console.log(listItems)
            if (listItems.length > 0) {
                const listItemsArr = [];
                for (const listItem of listItems) {
                    if (typeof listItem === 'object') {
                        // so that find() can be used
                        let nameNode: rtNode = null;
                        let valueNode: rtNode = null;
                        let selectNode: rtNode = null;
                        for (const listItemNode of listItem.children) {
                            if (typeof listItemNode === 'object') {
                                if (listItemNode.tagName === 'name') {
                                    nameNode = listItemNode;
                                } else if (listItemNode.tagName === 'value') {
                                    valueNode = listItemNode;
                                } else if (listItemNode.tagName === 'select') {
                                    selectNode = listItemNode;
                                }
                            }
                        }

                        if (
                            null == nameNode ||
                            0 === nameNode.children.length
                        ) {
                            continue;
                        }

                        // TODO: as
                        // const name = nameNode.children[0] as string;
                        // const value = (valueNode != null && valueNode.children[0] ) ?
                        //                             valueNode.children[0] as string : '';

                        // const select = (selectNode != null && selectNode.children[0] ) ?
                        //                 (selectNode.children[0] === '1' ? true : false) : false;
                        listItemsArr.push(new CodeValueItem(
                            safeDecodeURIComponent(nameNode.children[0] as string, documentVersion),
                            safeDecodeURIComponent(valueNode.children[0] as string, documentVersion),
                            (selectNode.children[0] as string) === '1' ? true : false)
                        );

                        // listItemsArr.push(
                        //     new CodeValueItem(
                        //         nameNode.children[0] as string,
                        //         valueNode.children[0] as string,
                        //         (selectNode.children[0] as string) === '1'
                        //             ? true
                        //             : false
                        //     )
                        // );
                    }
                }
                newControlProperty.newControlItems = listItemsArr;
            }
            // let t2 = performance.now();
            // window['add'] += t2 - t1;
            break;
        }

        case 'helpTip': {
            if (textNode != null && typeof textNode === 'string') {
                newControlProperty.newControlInfo = safeDecodeURIComponent(textNode, documentVersion);
            }
            break;
        }

        // ui only
        case 'showPlaceholder': {
            if (textNode != null && typeof textNode === 'string') {
                if (textNode === '1') {
                    showPlaceholder = true;
                }
            }
            break;
        }

        // text struct
        case 'secretType': {
            // shared in text and value struct
            if (textNode != null && typeof textNode === 'string') {
                newControlProperty.newControlDisplayType = +textNode;
            }
            break;
        }

        case 'fixedLength': {
            if (textNode != null && typeof textNode === 'string') {
                newControlProperty.newControlFixedLength = +textNode;
            }
            break;
        }

        case 'interAlign': {
            if (textNode != null && typeof textNode === 'string' && ALIGN_TYPE[textNode] != null) {
                newControlProperty.alignments = ALIGN_TYPE[textNode];
            }
            break;
        }
        case 'eventInfo': {
            if (textNode && typeof textNode === 'string') {
                try {
                    newControlProperty.eventInfo = JSON.parse(
                        (textNode as string)
                            .replace(/&amp;/g, '&')
                            .replace(/&lt;/g, '<')
                            .replace(/&gt;/g, '>')
                            .replace(/&quot;/g, '"')
                            .replace(/&apos;/g, '\'') as string
                    );
                } catch (error) {
                    // tslint:disable-next-line: no-console
                    console.error(error);
                }
            }
            break;
        }

        case 'maxLength': {
            if (textNode != null && typeof textNode === 'string') {
                newControlProperty.newControlMaxLength = +textNode;
            }
            break;
        }

        case 'title': {
            if (textNode != null && typeof textNode === 'string') {
                newControlProperty.newControlTitle = safeDecodeURIComponent(
                    textNode,
                    documentVersion
                );
            }
            break;
        }

        case 'hideHasTitle': {
            if (textNode && typeof textNode === 'string') {
                newControlProperty.hideHasTitle =
                    textNode === '1' ? true : false;
            }
            break;
        }

        // value struct
        case 'minValue': {
            if (textNode != null && typeof textNode === 'string') {
                newControlProperty.minValue = +textNode;
            }
            break;
        }

        case 'maxValue': {
            if (textNode != null && typeof textNode === 'string') {
                newControlProperty.maxValue = +textNode;
            }
            break;
        }

        case 'precision': {
            if (textNode != null && typeof textNode === 'string') {
                newControlProperty.precision = +textNode;
            }
            break;
        }

        case 'unit': {
            if (textNode != null && typeof textNode === 'string') {
                newControlProperty.unit = textNode;
            }
            break;
        }

        case 'forceValidate': {
            if (textNode != null && typeof textNode === 'string') {
                newControlProperty.forceValidate =
                    textNode === '1' ? true : false;
            }
            break;
        }

        // multi struct
        case 'retrieve': {
            if (textNode != null && typeof textNode === 'string') {
                newControlProperty.retrieve = textNode === '1' ? true : false;
            }
            break;
        }

        case 'selectPrefixContent': {
            if (textNode != null && typeof textNode === 'string') {
                newControlProperty.selectPrefixContent = safeDecodeURIComponent(textNode, documentVersion);
            }
            break;
        }

        case 'prefixContent': {
            if (textNode != null && typeof textNode === 'string') {
                newControlProperty.prefixContent = safeDecodeURIComponent(textNode, documentVersion);
            }
            break;
        }

        case 'separator': {
            if (textNode != null && typeof textNode === 'string') {
                newControlProperty.separator = safeDecodeURIComponent(textNode, documentVersion);
            }
            break;
        }

        case 'showValue': {
            if (textNode != null && typeof textNode === 'string') {
                newControlProperty.isShowValue =
                    textNode === '1' ? true : false;
            }
            break;
        }

        // datebox struct
        case 'dateType': {
            if (textNode != null && typeof textNode === 'string') {
                newControlProperty.dateBoxFormat = +textNode;
            }
            break;
        }

        case 'customDateFormat': {
            if (textNode != null && typeof textNode === 'string') {
                let nodeValue = textNode;
                if (nodeValue === '{}') {
                    break;
                }
                const domParser = new DOMParser();
                // handle unescaped string
                const doc = domParser.parseFromString(nodeValue, 'text/html');
                if (doc != null) {
                    nodeValue = doc.documentElement.textContent;
                }

                newControlProperty.customFormat = JSON.parse(nodeValue);
            }
            break;
        }

        case 'startDate': {
            if (textNode != null && typeof textNode === 'string') {
                newControlProperty.startDate = safeDecodeURIComponent(
                    textNode,
                    documentVersion
                );
            }
            break;
        }

        case 'endDate': {
            if (textNode != null && typeof textNode === 'string') {
                newControlProperty.endDate = safeDecodeURIComponent(
                    textNode,
                    documentVersion
                );
            }
            break;
        }

        case 'dateTime': {
            if (textNode != null && typeof textNode === 'string') {
                newControlProperty.dateTime = safeDecodeURIComponent(
                    textNode,
                    documentVersion
                );
            }
            break;
        }

        // checkbox
        case 'showRight': {
            // also radiobutton
            if (textNode != null && typeof textNode === 'string') {
                newControlProperty.showRight = textNode === '1' ? true : false;
            }
            break;
        }

        case 'checked': {
            if (textNode != null && typeof textNode === 'string') {
                newControlProperty.checked = textNode === '1' ? true : false;
            }
            break;
        }

        case 'printSelected': {
            if (textNode != null && typeof textNode === 'string') {
                newControlProperty.printSelected =
                    textNode === '1' ? true : false;
            }
            break;
        }

        case 'label': {
            if (textNode != null && typeof textNode === 'string') {
                newControlProperty.label = safeDecodeURIComponent(
                    textNode,
                    documentVersion
                );
            }
            break;
        }

        case 'labelCode': {
            if (textNode != null && typeof textNode === 'string') {
                newControlProperty.labelCode = safeDecodeURIComponent(
                    textNode,
                    documentVersion
                );
            }
            break;
        }

        case 'group': {
            if (textNode != null && typeof textNode === 'string') {
                newControlProperty.group = safeDecodeURIComponent(
                    textNode,
                    documentVersion
                );
            }
            break;
        }

        // radiobutton
        case 'showType': {
            if (textNode != null && typeof textNode === 'string') {
                let showType = +textNode;
                if (isNaN(showType) === true) {
                    showType = STD_START_DEFAULT.showType;
                }
                newControlProperty.showType = showType;
            }
            break;
        }

        case 'spaceNum': {
            if (textNode != null && typeof textNode === 'string') {
                newControlProperty.spaceNum = +textNode;
            }
            break;
        }

        case 'supportMultLines': {
            if (textNode != null && typeof textNode === 'string') {
                newControlProperty.supportMultLines =
                    textNode === '1' ? true : false;
            }
            break;
        }

        // signature box
        case 'signatureCount': {
            if (textNode != null && typeof textNode === 'string') {
                newControlProperty.signatureCount = +textNode;
            }
            break;
        }

        case 'preText': {
            if (textNode != null && typeof textNode === 'string') {
                newControlProperty.preText = safeDecodeURIComponent(
                    textNode,
                    documentVersion
                );
            }
            break;
        }

        case 'signatureSeparator': {
            if (textNode != null && typeof textNode === 'string') {
                newControlProperty.signatureSeparator = safeDecodeURIComponent(
                    textNode,
                    documentVersion
                );
            }
            break;
        }

        case 'postText': {
            if (textNode != null && typeof textNode === 'string') {
                newControlProperty.postText = safeDecodeURIComponent(textNode, documentVersion);
            }
            break;
        }

        case 'signaturePlaceholder': {
            if (textNode != null && typeof textNode === 'string') {
                newControlProperty.signaturePlaceholder = safeDecodeURIComponent(textNode, documentVersion);
            }
            break;
        }

        case 'signatureRatio': {
            if (textNode != null && typeof textNode === 'string') {
                newControlProperty.signatureRatio = +textNode;
            }
            break;
        }

        case 'rowHeightRestriction': {
            if (textNode != null && typeof textNode === 'string') {
                newControlProperty.rowHeightRestriction =
                    textNode === '1' ? true : false;
            }
            break;
        }

        case 'signType': {
            if (textNode != null && typeof textNode === 'string') {
                newControlProperty.signType = +textNode;
            }
            break;
        }

        case 'alwaysShow': {
            if (textNode != null && typeof textNode === 'string') {
                newControlProperty.alwaysShow = +textNode;
            }
            break;
        }

        case 'showSignBorder': {
            if (textNode != null && typeof textNode === 'string') {
                newControlProperty.showSignBorder =
                    textNode === '1' ? true : false;
            }
            break;
        }

        case 'hierarchy': {
            if (textNode != null && typeof textNode === 'string') {
                newControlProperty.hierarchy = +textNode;
            }
            break;
        }

        case 'province': {
            if (textNode != null && typeof textNode === 'string') {
                newControlProperty.province = JSON.parse(safeDecodeURIComponent(textNode, documentVersion));
            }
            break;
        }

        case 'city': {
            if (textNode != null && typeof textNode === 'string') {
                newControlProperty.city = JSON.parse(safeDecodeURIComponent(textNode, documentVersion));
            }
            break;
        }

        case 'county': {
            if (textNode != null && typeof textNode === 'string') {
                newControlProperty.county = JSON.parse(safeDecodeURIComponent(textNode, documentVersion));
            }
            break;
        }

        default:
            break;
    }

    return { borderStringStart, showPlaceholder, sdtContentNode };
}

export function readerRegionProps(regionProps: any, rgPropChild: rtNode, textNode: any,
                                  documentVersion?: number, insertFile?: {bRemoveHiddenRegion: boolean}): boolean {
    switch (rgPropChild.tagName) {
        case 'deleteProtect': {
          if (textNode) {
            regionProps.isNewControlCanntDelete = textNode === '1' ? true : false;
          }
          break;
        }

        case 'editProtect': {
          if (textNode) {
            regionProps.isNewControlCanntEdit = textNode === '1' ? true : false;
          }
          break;
        }

        case 'regExp': {
            if (textNode) {
              regionProps.regExp = safeDecodeURIComponent(textNode, documentVersion);
            }
            break;
          }

        case 'identifier': {
            if (textNode != null && typeof textNode === 'string') {
                regionProps.identifier = safeDecodeURIComponent(textNode, documentVersion);
            }
            break;
        }

        case 'placeholder': {
            if (textNode != null && typeof textNode === 'string') {
                regionProps.newControlPlaceHolder = safeDecodeURIComponent(textNode, documentVersion);
            }
            break;
        }

        case 'hidden': {
          if (textNode) {
            regionProps.isNewControlHidden = textNode === '1' ? true : false;
            // if (insertFile && regionProps.isNewControlHidden && insertFile.bRemoveHiddenRegion) {
            //     return false;
            // }
          }
          break;
        }

        case 'editReverse': {
          if (textNode) {
            regionProps.isNewControlReverseEdit = textNode === '1' ? true : false;
          }
          break;
        }

        case 'title': {
          if (textNode != null && typeof textNode === 'string') {
            regionProps.newControlTitle = safeDecodeURIComponent(textNode, documentVersion);
          }
          break;
        }
        case 'tip': {
          if (textNode != null && typeof textNode === 'string') {
            regionProps.newControlInfo = safeDecodeURIComponent(textNode, documentVersion);
          }
          break;
        }

        case 'showTitle': {
          if (textNode != null) {
            regionProps.showTitle = textNode === '1' ? true : false;
          }
          break;
        }

        case 'beginPos': {
          //
          break;
        }

        case 'endPos': {
          //
          break;
        }

        case 'serialNumber': {
          if (textNode != null && typeof textNode === 'string') {
            regionProps.newControlSerialNumber = textNode;
          }
          break;
        }

        case 'maxLength': {
          if (textNode != null && typeof textNode === 'string') {
            regionProps.newControlMaxLength = parseInt(textNode, 10);
          }
          break;
        }

        case 'customProperty': {
          const customProperties = rgPropChild.children;
          if (customProperties.length > 0) { // usually if in this block, length must be > 0

            const customPropertyArr = tReadCustomProperties(customProperties, documentVersion);
            // console.log(customPropertyArr)
            regionProps.customProperty = customPropertyArr;
          }
          break;
        }

        default: {
          break;
        }
    }
    return;
}
