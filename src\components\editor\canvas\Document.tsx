import React from 'react';
import {
    ViewModeType,
    FILL_COLOR,
    FillColorType,
    IEditorBackground,
    WatermarkType} from '../../../common/commonDefines';
import { FixedSizeList as List } from '../module/reactWindow/index.cjs';
import { PageUI } from './Page';

interface IDocumentProps {
    height?: number;
    headerHeight?: number;
    host: any;
    iframe?: any;
    unResizeCursor?: boolean;
}

interface IState {
    bRefresh: boolean;
}

// interface pageState {
//   pageInfo: any,
//   textPage: any,
// }

export class DocumentUI extends React.Component<IDocumentProps, IState> {
    // public external: IExternalInterface;
    public documentCore: any;
    public docId: number;
    public currentRef: any;
    public bPrint: boolean;
    private host: any;
    private textAreaRef: any;
    private pageRefs: any;
    private viewMode: ViewModeType;
    private _scale: any;
    private bRefresh: boolean;
    private pagesIndexs: any;

    constructor(props: IDocumentProps) {
        super(props);
        this.host = props.host;
        this.bPrint = this.host.bPrint;
        this.docId = this.host.docId;
        this.state = {bRefresh: false};
        this.documentCore = props.host.state.documentCore;
    }

    public componentDidMount(): void {
        // this.refreshPage();
    }

    public handleRefresh(): void {
        this.setState({ bRefresh: !this.state.bRefresh}, () => {
            this.refreshPage();
        });
    }

    public refreshPage(pageIndexs?: number[]): void {
        const pageRefs = this.pageRefs;
        const keys = Object.keys(pageRefs);
        const oldPages = this.pagesIndexs;
        if (pageIndexs && oldPages) {
            if (pageIndexs.length === oldPages.length) {
                let bChanged = false;
                for (let index = 0, len = pageIndexs.length; index < len; index++) {
                    if (pageIndexs[index] !== oldPages[index]) {
                        bChanged = true;
                        break;
                    }
                }
                if (bChanged === false) {
                    return;
                }
            }
        }
        if (pageIndexs) {
            this.pagesIndexs = pageIndexs.slice();
        }
        keys.forEach((key) => {
            this.renderPageContent(pageRefs[key], +key);
        });
    }

    public render(): any {
        const { total, pageProperty } = this.documentCore.render();
        // const scale = this.documentCore.getViewScale();
        // let checkIframeHeight = false;
        let height: number = this.props.height;

        this.bPrint = this.host.bPrint;
        if (height === undefined) {
            height = document.firstElementChild.clientHeight;
        }

        height = (height - this.props.headerHeight) || 0;
        // if (this.documentCore.getDynamicHeightMode() && this.documentCore.getPageCount() === 1) {
        //     const tempHeight = this.getLastPageRowHeight();
        //     if (tempHeight < height) {
        //       height = tempHeight;
        //     }
        // }

        // if (this.shouldCheckIframeHeight(height) === true ) {
        //     checkIframeHeight = true;
        // }
        // console.log(height)
        // console.log(this._height)
        // this._height = height;

        // if (checkIframeHeight === true) {
        //     this.checkIframeHeight(height);
        // }

        let width = this.host.clientWidth;
        let style = 'page-list development-mode';
        // if (this.host.id !== 'editor-id') {
        //     // initialized iframe
        //     width = pageProperty.width;
        //     style = 'page-list iframe-initialized';
        // }
        const scale = this._scale = this.documentCore.getViewScale();
        const viewMode = this.viewMode = this.documentCore.getViewMode();
        if (width === undefined) {
            width = document.documentElement.clientWidth;
        }

        let className = 'ReactVirtualized__Grid editor-page-content editor-default-view';
        const rowHeight = pageProperty.height + 20;
        if (viewMode === ViewModeType.WebView) {
            className += ' editor-web-view';
        } else {
            className += ' editor-other-view';
        }
        style = 'ReactVirtualized__Grid__innerScrollContainer ' + style;
        this.pageRefs = {};
        return (
            <div ref={this.currentRef} className={className}>
                <List
                    bRefresh={this.state.bRefresh}
                    className={style}
                    isScrolling={false}
                    showAll={this.bPrint}

                    // overscanRowCount={1}
                    // width={document.documentElement.clientWidth}
                    overscanCount={1}
                    onScroll={this.onScroll}
                    // height constraint for list (just drawing canvas ui height; nothing todo with pageproperty)
                    height={height}
                    itemCount={total}
                    itemSize={rowHeight} // page count
                    // onRowsRendered={this.onRowsRendered}
                    // rowHeight={this.getPageRowHeight}
                    // scrollToIndex={0} // 初始化打开文档时，可指定显示第几页
                >
                    {this.renderPages}
                </List>
                <textarea ref={this.textAreaRef} id='textarea_input'/>
            </div>
        );
    }

    private renderPageContent(page: any, index: number, dom?: any): any {
        if (dom) {
            return;
        }
        dom = page.ref.current;
        if (!dom) {
            return;
        }
        const canvas = document.createElement('canvas');
        canvas.width = page.width;
        canvas.height = page.height;
        canvas.style.background = '#fff';

        dom.innerHTML = '';
        dom.appendChild(canvas);
        const options = {
            content: page,
            host: this,
            pageIndex: index,
            ctx: canvas.getContext('2d'),
        };
        const aaa = new PageUI(options);
    }

    private onScroll = (options: any): void => {
        console.log(options)
        this.refreshPage(options.pageIndexs);
    }

    private renderPages = ({index, isVisible, style }: any): any => {
        if (true !== isVisible) {
            return null;
        }

        const documentCore = this.documentCore;
        // const getContentByPageId = documentCore.getContentByPageId;
        const { pageProperty, total } = documentCore.render();
        const pageProps = {...pageProperty};
        const pageWidth = parseInt((pageProperty.width * this._scale) as any, 10);
        const pageHeight = pageProperty.height * this._scale;
        let height = 20;
        if (this.viewMode === ViewModeType.WebView) {
            height = 0;
        }

        const htmlStyle = {
            ...style,
            width: pageWidth,
            height: style.height - height,
        };
        const contents = [];
        const isWebView = (ViewModeType.WebView !== documentCore.getViewMode());
        contents[0] = documentCore.getContentByPageId(index);
        contents[1] = ((isWebView && documentCore.hasHeaderFooter(true)) ?
                        documentCore.getContentByPageId(index, 1) : null);
        contents[2] = ((isWebView && documentCore.hasHeaderFooter(false)) ?
                        documentCore.getContentByPageId(index, 2) : null);
        const page = this.pageRefs[index] = {
            ref: React.createRef(),
            width: htmlStyle.width,
            height: htmlStyle.height,
            content: contents,
        };

        return (
            <div
                key={index}
                ref={this.pageRefs[index].ref}
                page-index={index}
                style={htmlStyle}
                className='page-wrapper'
            />
        );
    }

}
