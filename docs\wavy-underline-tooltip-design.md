# 波浪线提示框系统设计方案

## 0. 波浪线悬停提示框机制设计方案 (最新版本)

### 0.1 方案概述

基于对修订痕迹悬停机制和波浪线管理机制的深入分析，我们设计了一个统一的波浪线悬停提示框解决方案，该方案成功结合了两种机制的优势。

### 0.2 核心技术特点

#### 0.2.1 借鉴修订机制的核心架构
- **事件管理模式**：参考 `RevisionChangeEvent` 的全局事件监听模式
- **延迟显示逻辑**：300ms 延迟显示，避免快速移动时的闪烁
- **位置检测机制**：复用 `DocumentCore.getCursorStateInDocument()` 的位置计算逻辑
- **DOM管理策略**：单例模式管理提示框，避免重复创建和内存泄漏

#### 0.2.2 结合波浪线管理机制的优势
- **高效位置索引**：利用 `WavyUnderlineManager` 的二分查找优化性能
- **状态管理统一**：通过激活/非激活状态管理避免重复处理
- **类型系统支持**：支持多种波浪线类型（拼写、语法、建议、警告、自定义）
- **数据结构优化**：使用 Map + 排序数组的混合数据结构

### 0.3 核心流程设计

```mermaid
graph TD
    A[鼠标移动事件] --> B[300ms防抖延迟]
    B --> C[获取鼠标坐标]
    C --> D[坐标转换与验证]
    D --> E[查找波浪线对象]
    E --> F{找到波浪线?}
    F -->|是| G[激活波浪线状态]
    F -->|否| H[隐藏提示框]
    G --> I[生成提示内容]
    I --> J[显示提示框]
    J --> K[位置调整优化]
    H --> L[取消激活状态]
```

### 0.4 实现方案对比

| 特性 | 修订机制 | 波浪线机制 | 统一方案 |
|------|----------|------------|----------|
| 事件处理 | 全局事件监听 | 组件级事件 | **全局事件监听** ✅ |
| 性能优化 | 简单位置计算 | 二分查找索引 | **二分查找索引** ✅ |
| 状态管理 | 单一焦点状态 | 多对象状态管理 | **多对象状态管理** ✅ |
| 延迟策略 | 300ms延迟显示 | 简单防抖 | **300ms延迟显示** ✅ |
| DOM管理 | 单例DOM复用 | 每次创建销毁 | **单例DOM复用** ✅ |

### 0.5 核心实现类：WavyUnderlineHoverManager

```typescript
// src/components/editor/module/document/WavyUnderlineHoverManager.ts
export default class WavyUnderlineHoverManager {
    private host: Document;
    private docId: number;
    private gMouseEvent: MouseEventHandler;
    private documentCore: DocumentCore;
    private curFocusWavyUnderline: string | null = null;
    private isShowWavyTips: boolean = false;
    private tipDom: HTMLDivElement | null = null;
    private showTimer: number | null = null;
    private hideTimer: number | null = null;

    constructor(host: Document) {
        this.host = host;
        this.docId = host.docId;
        this.documentCore = host.documentCore;
        this.gMouseEvent = new MouseEventHandler();
        this.addEvents();
    }

    /**
     * 鼠标移动事件处理 - 参考修订机制的实现
     */
    private handleMouseMove = (event: any): void => {
        const documentCore = this.documentCore;
        const pageNode = getPageElement(event.target);
        
        if (!pageNode) return;

        // 检查保护模式
        const bProtect = documentCore.isProtectedMode();
        if (bProtect && this.host.isPrint()) return;

        // 正在选择文本时隐藏提示
        if (documentCore.isSelecting()) {
            this.hideTips();
            return;
        }

        const pageId = this.host.getCurPageIndex();
        const scale = this.host.getScale();

        // 计算鼠标位置 - 参考修订机制
        const position = getPagePadding(this.documentCore);
        let offsetX = event.layerX / scale + position.left;
        const offsetY = event.layerY / scale + position.top;

        // 获取光标状态
        const cursorState = documentCore.getCursorStateInDocument(offsetX, offsetY, pageId);
        offsetX = cursorState ? cursorState.pointX : offsetX;
        
        this.gMouseEvent.pointX = offsetX;
        this.gMouseEvent.pointY = offsetY;

        // 检查鼠标位置是否有效
        if (cursorState.result) {
            const wavyUnderline = this.getFocusWavyUnderline(this.gMouseEvent, pageId);
            if (wavyUnderline) {
                this.showTips(wavyUnderline, event, pageId);
                return;
            }
        }

        this.hideTips();
    }

    /**
     * 根据鼠标位置获取波浪线对象 - 结合修订机制的位置检测逻辑
     */
    private getFocusWavyUnderline(mouseEvent: MouseEventHandler, pageId: number): WavyUnderline | null {
        const wavyManager = this.documentCore.getWavyUnderlineManager();
        if (!wavyManager) return null;

        // 获取段落位置
        const paragraphPos = this.documentCore.getParaContentPosByXY(pageId, mouseEvent.pointX, mouseEvent.pointY);
        if (!paragraphPos || !paragraphPos.bFound) return null;

        // 从波浪线管理器中查找对应位置的波浪线
        return wavyManager.getWavyUnderlineByPos(paragraphPos.pos);
    }

    /**
     * 显示提示框 - 参考修订机制的延迟显示逻辑
     */
    private showTips(wavyUnderline: WavyUnderline, e: any, pageId: number): void {
        this.clearTimers();
        
        if (e.target.nodeName === 'DIV') return;

        const content = this.generateWavyUnderlineContent(wavyUnderline);
        if (!content) {
            this.hideTips();
            return;
        }

        const currentFocusKey = wavyUnderline.getName() + 'pageIndex' + pageId;
        
        // 如果是同一个波浪线且已经显示，则不重复处理
        if (this.curFocusWavyUnderline === currentFocusKey && this.isShowWavyTips) {
            return;
        }

        // 300ms 延迟显示 - 与修订机制保持一致
        this.showTimer = window.setTimeout(() => {
            let dom = this.tipDom;
            if (!dom) {
                dom = document.createElement('div');
                dom.className = 'wavy-underline-tips';
                this.host.getContainer().appendChild(dom);
                this.tipDom = dom;
                dom.addEventListener(gEventName.Click, this.handleTips);
                dom.addEventListener(gEventName.Mousemove, this.handleTips);
            }

            (dom as HTMLElement).innerHTML = content;
            const page = this.documentCore.getPageProperty();
            dom.className += ' visible';
            const scale = this.host.getScale();
            dom.style.maxWidth = page.width * scale - 40 + 'px';

            // 激活当前波浪线
            const wavyManager = this.documentCore.getWavyUnderlineManager();
            if (wavyManager) {
                wavyManager.activateWavyUnderline(wavyUnderline.getName());
            }

            this.showDom(dom, e, pageId, wavyUnderline);
            this.curFocusWavyUnderline = currentFocusKey;
            this.isShowWavyTips = true;
        }, 300);
    }
}
```

### 0.6 技术亮点

#### 0.6.1 双重索引优化
- **位置索引**：按位置排序的二分查找，O(log n) 复杂度
- **状态索引**：Map结构的O(1)查找，支持快速激活/取消操作

#### 0.6.2 智能位置检测
```typescript
// 参考修订机制的位置检测逻辑
const cursorState = documentCore.getCursorStateInDocument(offsetX, offsetY, pageId);
if (cursorState.result) {
    const wavyUnderline = this.getFocusWavyUnderline(this.gMouseEvent, pageId);
    // 处理波浪线激活逻辑
}
```

#### 0.6.3 渐进式提示框显示
```typescript
// 300ms延迟 + 透明度动画
this.showTimer = window.setTimeout(() => {
    // 创建提示框
    this.createTooltip(options);
    
    // 渐入动画
    requestAnimationFrame(() => {
        if (this.tooltip) {
            this.tooltip.style.opacity = '1';
        }
    });
}, 300);
```

### 0.7 集成架构

#### 0.7.1 数据层集成
- `DocumentCore` ← 统一API接口
- `Document` ← 波浪线管理器集成  
- `WavyUnderlineManager` ← 核心数据管理

#### 0.7.2 事件层集成
- `WavyUnderlineHoverManager` ← 统一事件处理
- `GlobalEvent` ← 全局事件系统
- 参考 `RevisionChangeEvent` 的事件监听模式

#### 0.7.3 UI层集成
- CSS样式系统 ← 类型化样式支持
- 提示框DOM管理 ← 单例模式优化
- 位置自适应 ← 屏幕边界检测

### 0.8 性能优化策略

1. **内存管理**：单例提示框，避免频繁创建销毁
2. **计算优化**：二分查找 + 位置缓存，减少重复计算
3. **渲染优化**：防抖延迟 + requestAnimationFrame，提升流畅度
4. **事件优化**：智能事件绑定，避免不必要的监听器

### 0.9 扩展性设计

- **类型可扩展**：支持新增波浪线类型和样式
- **内容可定制**：支持富文本提示内容
- **交互可配置**：支持点击、键盘等交互扩展
- **主题可切换**：支持不同UI主题适配

---

## 1. 概述

### 1.1 需求背景
用户在编辑器中设置了波浪线标记后，希望鼠标悬停在带波浪线的文本上时能够显示相应的提示信息，类似于拼写检查的交互效果。

### 1.2 设计目标
- 符合编辑器 MVC 架构模式
- 支持多个波浪线对象，每个可配置不同的提示内容
- 精确的位置定位和碰撞检测
- 良好的性能和扩展性
- 与现有批注系统保持架构一致性

### 1.3 技术挑战
- 避免在 View 层直接处理业务逻辑
- 需要精确的鼠标位置与文档位置的映射
- 多个波浪线对象的高效管理
- 提示框的显示时机和位置计算

## 2. 架构设计

### 2.1 整体架构

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   View Layer    │    │ Controller Layer │    │   Model Layer   │
│                 │    │                  │    │                 │
│ ParaBaseUI.tsx  │◄──►│WavyUnderlineHand-│◄──►│WavyUnderline    │
│ TooltipManager  │    │ler.ts           │    │WavyUnderlineMgr │
│                 │    │                  │    │Document.ts      │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

### 2.2 参考架构：批注系统
本设计参考现有批注系统的成熟架构：
- `Comment` ↔ `WavyUnderline`：数据实体
- `CommentManager` ↔ `WavyUnderlineManager`：管理器
- `CommentData` ↔ `WavyUnderlineData`：数据载体

## 3. 数据模型设计

### 3.1 WavyUnderline 核心类

```typescript
// src/model/core/WavyUnderline/WavyUnderline.ts
export class WavyUnderline {
    private id: number;                    // 唯一标识
    private name: string;                  // 内部名称
    private content: string;               // 提示内容
    private type: WavyUnderlineType;       // 波浪线类型
    private startPortion: ParaPortion;     // 起始段落片段
    private endPortion: ParaPortion;       // 结束段落片段
    private startPos: ParagraphContentPos; // 起始位置
    private endPos: ParagraphContentPos;   // 结束位置
    private bActive: boolean;              // 激活状态
    private logicDocument: Document;       // 文档引用
    
    constructor() {
        this.id = idCounter.getNewId();
        this.bActive = false;
        this.content = '';
        this.type = WavyUnderlineType.SpellCheck;
    }
    
    // 基础属性访问器
    public getId(): number { return this.id; }
    public getName(): string { return this.name; }
    public setName(name: string): void { this.name = name; }
    
    // 内容管理
    public getContent(): string { return this.content; }
    public setContent(content: string): boolean {
        if (content !== this.content) {
            this.content = content;
            return true;
        }
        return false;
    }
    
    // 类型管理
    public getType(): WavyUnderlineType { return this.type; }
    public setType(type: WavyUnderlineType): void { this.type = type; }
    
    // 位置管理
    public getStartPos(): ParagraphContentPos { return this.startPos; }
    public setStartPos(pos: ParagraphContentPos): void { this.startPos = pos; }
    public getEndPos(): ParagraphContentPos { return this.endPos; }
    public setEndPos(pos: ParagraphContentPos): void { this.endPos = pos; }
    
    // 段落片段管理
    public getStartPortion(): ParaPortion { return this.startPortion; }
    public setStartPortion(portion: ParaPortion): void { this.startPortion = portion; }
    public getEndPortion(): ParaPortion { return this.endPortion; }
    public setEndPortion(portion: ParaPortion): void { this.endPortion = portion; }
    
    // 激活状态管理
    public setActive(bActive: boolean): void { this.bActive = bActive; }
    public isActive(): boolean { return this.bActive; }
    
    // 文档引用
    public setLogicDocument(doc: Document): void { this.logicDocument = doc; }
    public getLogicDocument(): Document { return this.logicDocument; }
    
    // 工具方法
    public isEmpty(): boolean {
        return !this.startPortion || !this.endPortion;
    }
    
    public isInRange(pos: ParagraphContentPos): boolean {
        return this.startPos.compare(pos) <= 0 && this.endPos.compare(pos) >= 0;
    }
}
```

### 3.2 波浪线类型枚举

```typescript
export enum WavyUnderlineType {
    SpellCheck = 0,    // 拼写检查错误
    Grammar = 1,       // 语法错误  
    Suggestion = 2,    // 建议修改
    Warning = 3,       // 警告信息
    Custom = 4,        // 自定义类型
}

export const WavyUnderlineTypeConfig = {
    [WavyUnderlineType.SpellCheck]: {
        color: '#ff0000',
        defaultContent: '可能的拼写错误'
    },
    [WavyUnderlineType.Grammar]: {
        color: '#00ff00', 
        defaultContent: '语法检查建议'
    },
    [WavyUnderlineType.Suggestion]: {
        color: '#0000ff',
        defaultContent: '修改建议'
    },
    [WavyUnderlineType.Warning]: {
        color: '#ff8800',
        defaultContent: '注意事项'
    },
    [WavyUnderlineType.Custom]: {
        color: '#8800ff',
        defaultContent: '自定义提示'
    }
};
```

### 3.3 WavyUnderlineManager 管理器类

```typescript
// src/model/core/WavyUnderline/WavyUnderlineManager.ts
export class WavyUnderlineManager {
    private data: Map<string, WavyUnderline>;
    private sortData: IWavyUnderlineSortData[];
    private doc: Document;
    private activeWavyUnderline: string | null = null;
    private nameCounter: number = 0;
    
    constructor(doc: Document) {
        this.doc = doc;
        this.data = new Map();
        this.sortData = [];
    }
    
    /**
     * 添加波浪线对象
     */
    public addWavyUnderline(wavyUnderline: WavyUnderline): void {
        const name = this.makeUniqueName();
        wavyUnderline.setName(name);
        wavyUnderline.setLogicDocument(this.doc);
        
        this.data.set(name, wavyUnderline);
        this.sortData.push({
            id: wavyUnderline.getId(),
            name: name
        });
        
        // 按位置排序，便于查找
        this.sortWavyUnderlines();
    }
    
    /**
     * 根据位置获取波浪线对象
     */
    public getWavyUnderlineByPos(pos: ParagraphContentPos): WavyUnderline | null {
        // 使用二分查找优化性能
        const index = this.findWavyUnderlineLowBoundByPos(pos);
        
        for (let i = index; i < this.sortData.length; i++) {
            const {name} = this.sortData[i];
            const wavyUnderline = this.data.get(name);
            
            if (wavyUnderline && wavyUnderline.isInRange(pos)) {
                return wavyUnderline;
            }
            
            // 如果起始位置已经超过了目标位置，则不可能再找到
            if (wavyUnderline && wavyUnderline.getStartPos().compare(pos) > 0) {
                break;
            }
        }
        
        return null;
    }
    
    /**
     * 激活指定波浪线
     */
    public activeWavyUnderline(name: string): boolean {
        this.deactiveAll();
        
        const wavyUnderline = this.data.get(name);
        if (wavyUnderline) {
            wavyUnderline.setActive(true);
            this.activeWavyUnderline = name;
            return true;
        }
        return false;
    }
    
    /**
     * 取消所有激活状态
     */
    public deactiveAll(): void {
        if (this.activeWavyUnderline) {
            const wavyUnderline = this.data.get(this.activeWavyUnderline);
            if (wavyUnderline) {
                wavyUnderline.setActive(false);
            }
            this.activeWavyUnderline = null;
        }
    }
    
    /**
     * 获取当前激活的波浪线
     */
    public getActiveWavyUnderline(): WavyUnderline | null {
        if (this.activeWavyUnderline) {
            return this.data.get(this.activeWavyUnderline) || null;
        }
        return null;
    }
    
    /**
     * 删除波浪线
     */
    public removeWavyUnderline(name: string): boolean {
        const wavyUnderline = this.data.get(name);
        if (!wavyUnderline) {
            return false;
        }
        
        // 从数据映射中删除
        this.data.delete(name);
        
        // 从排序数组中删除
        const index = this.sortData.findIndex(item => item.name === name);
        if (index !== -1) {
            this.sortData.splice(index, 1);
        }
        
        // 如果是当前激活的，则取消激活
        if (this.activeWavyUnderline === name) {
            this.activeWavyUnderline = null;
        }
        
        return true;
    }
    
    /**
     * 获取所有波浪线
     */
    public getAllWavyUnderlines(): WavyUnderline[] {
        return Array.from(this.data.values());
    }
    
    /**
     * 清空所有波浪线
     */
    public clear(): void {
        this.data.clear();
        this.sortData = [];
        this.activeWavyUnderline = null;
    }
    
    /**
     * 生成唯一名称
     */
    private makeUniqueName(): string {
        return `wavy_${++this.nameCounter}_${Date.now()}`;
    }
    
    /**
     * 按位置排序波浪线
     */
    private sortWavyUnderlines(): void {
        this.sortData.sort((a, b) => {
            const wavyA = this.data.get(a.name);
            const wavyB = this.data.get(b.name);
            
            if (!wavyA || !wavyB) return 0;
            
            return wavyA.getStartPos().compare(wavyB.getStartPos());
        });
    }
    
    /**
     * 二分查找位置对应的波浪线索引
     */
    private findWavyUnderlineLowBoundByPos(pos: ParagraphContentPos): number {
        let low = 0;
        let high = this.sortData.length;
        
        while (low < high) {
            const middle = Math.floor((low + high) / 2);
            const wavyUnderline = this.data.get(this.sortData[middle].name);
            
            if (!wavyUnderline) {
                high = middle;
                continue;
            }
            
            const startPos = wavyUnderline.getStartPos();
            const endPos = wavyUnderline.getEndPos();
            
            if (endPos.compare(pos) < 0) {
                low = middle + 1;
            } else if (startPos.compare(pos) > 0) {
                high = middle;
            } else {
                return middle;
            }
        }
        
        return low;
    }
}

interface IWavyUnderlineSortData {
    id: number;
    name: string;
}
```

## 4. 控制器层设计

### 4.1 事件处理器

```typescript
// src/components/editor/module/document/WavyUnderlineHandler.ts
export class WavyUnderlineHandler {
    private documentCore: DocumentCore;
    private tooltipManager: WavyUnderlineTooltipManager;
    private debounceTimer: number | null = null;
    private lastPosition: ParagraphContentPos | null = null;
    
    constructor(documentCore: DocumentCore) {
        this.documentCore = documentCore;
        this.tooltipManager = new WavyUnderlineTooltipManager();
    }
    
    /**
     * 处理鼠标移动事件
     */
    public handleMouseMove(e: MouseEvent): void {
        // 防抖处理，避免频繁计算
        if (this.debounceTimer) {
            clearTimeout(this.debounceTimer);
        }
        
        this.debounceTimer = window.setTimeout(() => {
            this.processMouseMove(e);
        }, 100);
    }
    
    /**
     * 处理鼠标离开事件
     */
    public handleMouseLeave(): void {
        const wavyUnderlineManager = this.documentCore.getWavyUnderlineManager();
        wavyUnderlineManager.deactiveAll();
        this.tooltipManager.hide();
        this.lastPosition = null;
    }
    
    /**
     * 销毁处理器
     */
    public destroy(): void {
        if (this.debounceTimer) {
            clearTimeout(this.debounceTimer);
        }
        this.tooltipManager.destroy();
    }
    
    /**
     * 实际处理鼠标移动逻辑
     */
    private processMouseMove(e: MouseEvent): void {
        const pos = this.getPositionFromMouseEvent(e);
        if (!pos) {
            this.handleMouseLeave();
            return;
        }
        
        // 如果位置没有变化，则不需要重新处理
        if (this.lastPosition && this.lastPosition.isEqual(pos)) {
            return;
        }
        
        this.lastPosition = pos.copy();
        
        const wavyUnderlineManager = this.documentCore.getWavyUnderlineManager();
        const wavyUnderline = wavyUnderlineManager.getWavyUnderlineByPos(pos);
        
        if (wavyUnderline) {
            // 激活波浪线
            wavyUnderlineManager.activeWavyUnderline(wavyUnderline.getName());
            
            // 显示提示框
            this.tooltipManager.show({
                x: e.clientX,
                y: e.clientY,
                content: wavyUnderline.getContent(),
                type: wavyUnderline.getType(),
                wavyUnderline: wavyUnderline
            });
        } else {
            // 取消激活并隐藏提示框
            wavyUnderlineManager.deactiveAll();
            this.tooltipManager.hide();
        }
    }
    
    /**
     * 根据鼠标事件获取文档位置
     */
    private getPositionFromMouseEvent(e: MouseEvent): ParagraphContentPos | null {
        try {
            // 调用现有的位置计算方法
            return this.documentCore.getPositionByXY(e.clientX, e.clientY);
        } catch (error) {
            console.warn('Failed to get position from mouse event:', error);
            return null;
        }
    }
}
```

### 4.2 提示框管理器

```typescript
// src/components/editor/module/document/WavyUnderlineTooltipManager.ts
export class WavyUnderlineTooltipManager {
    private tooltip: HTMLElement | null = null;
    private isVisible: boolean = false;
    private showTimer: number | null = null;
    private hideTimer: number | null = null;
    
    /**
     * 显示提示框
     */
    public show(options: IWavyUnderlineTooltipOptions): void {
        // 清除隐藏定时器
        if (this.hideTimer) {
            clearTimeout(this.hideTimer);
            this.hideTimer = null;
        }
        
        // 延迟显示，避免快速移动时闪烁
        if (this.showTimer) {
            clearTimeout(this.showTimer);
        }
        
        this.showTimer = window.setTimeout(() => {
            this.createTooltip(options);
        }, 300);
    }
    
    /**
     * 隐藏提示框
     */
    public hide(): void {
        // 清除显示定时器
        if (this.showTimer) {
            clearTimeout(this.showTimer);
            this.showTimer = null;
        }
        
        // 延迟隐藏，避免快速移动时闪烁
        this.hideTimer = window.setTimeout(() => {
            this.destroyTooltip();
        }, 100);
    }
    
    /**
     * 立即销毁
     */
    public destroy(): void {
        if (this.showTimer) {
            clearTimeout(this.showTimer);
        }
        if (this.hideTimer) {
            clearTimeout(this.hideTimer);
        }
        this.destroyTooltip();
    }
    
    /**
     * 创建提示框DOM
     */
    private createTooltip(options: IWavyUnderlineTooltipOptions): void {
        this.destroyTooltip();
        
        const tooltip = document.createElement('div');
        tooltip.className = 'wavy-underline-tooltip';
        tooltip.innerHTML = this.generateTooltipContent(options);
        
        // 设置样式
        Object.assign(tooltip.style, {
            position: 'fixed',
            left: `${options.x}px`,
            top: `${options.y - 40}px`,
            zIndex: '9999',
            backgroundColor: '#fff',
            border: '1px solid #ccc',
            borderRadius: '4px',
            padding: '8px 12px',
            boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
            fontSize: '12px',
            maxWidth: '250px',
            wordWrap: 'break-word',
            opacity: '0',
            transition: 'opacity 0.2s ease-in-out'
        });
        
        // 根据类型设置边框颜色
        const config = WavyUnderlineTypeConfig[options.type];
        if (config) {
            tooltip.style.borderLeftColor = config.color;
            tooltip.style.borderLeftWidth = '3px';
        }
        
        document.body.appendChild(tooltip);
        this.tooltip = tooltip;
        this.isVisible = true;
        
        // 淡入效果
        requestAnimationFrame(() => {
            if (this.tooltip) {
                this.tooltip.style.opacity = '1';
            }
        });
        
        // 调整位置避免超出屏幕
        this.adjustTooltipPosition();
    }
    
    /**
     * 销毁提示框DOM
     */
    private destroyTooltip(): void {
        if (this.tooltip) {
            document.body.removeChild(this.tooltip);
            this.tooltip = null;
            this.isVisible = false;
        }
    }
    
    /**
     * 生成提示框内容
     */
    private generateTooltipContent(options: IWavyUnderlineTooltipOptions): string {
        const typeConfig = WavyUnderlineTypeConfig[options.type];
        const typeName = this.getTypeDisplayName(options.type);
        
        return `
            <div class="wavy-tooltip-header">
                <span class="wavy-tooltip-type" style="color: ${typeConfig?.color || '#333'}">${typeName}</span>
            </div>
            <div class="wavy-tooltip-content">${options.content}</div>
        `;
    }
    
    /**
     * 获取类型显示名称
     */
    private getTypeDisplayName(type: WavyUnderlineType): string {
        switch (type) {
            case WavyUnderlineType.SpellCheck: return '拼写检查';
            case WavyUnderlineType.Grammar: return '语法检查';
            case WavyUnderlineType.Suggestion: return '修改建议';
            case WavyUnderlineType.Warning: return '警告';
            case WavyUnderlineType.Custom: return '自定义';
            default: return '提示';
        }
    }
    
    /**
     * 调整提示框位置避免超出屏幕
     */
    private adjustTooltipPosition(): void {
        if (!this.tooltip) return;
        
        const rect = this.tooltip.getBoundingClientRect();
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;
        
        // 水平方向调整
        if (rect.right > viewportWidth) {
            this.tooltip.style.left = `${viewportWidth - rect.width - 10}px`;
        }
        if (rect.left < 0) {
            this.tooltip.style.left = '10px';
        }
        
        // 垂直方向调整
        if (rect.top < 0) {
            this.tooltip.style.top = `${rect.bottom + 10}px`;
        }
        if (rect.bottom > viewportHeight) {
            this.tooltip.style.top = `${rect.top - rect.height - 10}px`;
        }
    }
}

interface IWavyUnderlineTooltipOptions {
    x: number;
    y: number;
    content: string;
    type: WavyUnderlineType;
    wavyUnderline: WavyUnderline;
}
```

## 5. Document 层集成

### 5.1 Document 类修改

```typescript
// src/model/core/Document.ts 中添加
import { WavyUnderlineManager } from './WavyUnderline/WavyUnderlineManager';
import { WavyUnderline, WavyUnderlineType } from './WavyUnderline/WavyUnderline';

export default class Document extends DocumentContentBase {
    public wavyUnderlineManager: WavyUnderlineManager;
    
    constructor() {
        super();
        // ... 其他初始化代码
        this.wavyUnderlineManager = new WavyUnderlineManager(this);
    }
    
    /**
     * 获取波浪线管理器
     */
    public getWavyUnderlineManager(): WavyUnderlineManager {
        return this.wavyUnderlineManager;
    }
    
    /**
     * 修改后的波浪线控制器方法
     */
    public markTextWithWaveController(options?: IWavyUnderlineOptions): number {
        const selection = this.getDocumentSelection();
        if (!selection.bUse) {
            return ResultType.UnEdited;
        }
        
        // 创建波浪线对象
        const wavyUnderline = new WavyUnderline();
        wavyUnderline.setContent(options?.content || '可能的拼写错误');
        wavyUnderline.setType(options?.type || WavyUnderlineType.SpellCheck);
        
        // 设置位置信息
        const startPos = selection.startPos.copy();
        const endPos = selection.endPos.copy();
        wavyUnderline.setStartPos(startPos);
        wavyUnderline.setEndPos(endPos);
        
        // 获取起始和结束的段落片段
        const startPortion = this.getElementByPos(startPos, false) as ParaPortion;
        const endPortion = this.getElementByPos(endPos, false) as ParaPortion;
        wavyUnderline.setStartPortion(startPortion);
        wavyUnderline.setEndPortion(endPortion);
        
        // 添加到管理器
        this.wavyUnderlineManager.addWavyUnderline(wavyUnderline);
        
        // 设置文本属性用于渲染
        const textPr = new TextProperty();
        textPr.clear();
        textPr.textDecorationLine = TextDecorationLineType.WavyUnderline;
        
        const paraText = new ParaTextProperty();
        paraText.textProperty = textPr;
        
        return this.addToParagraph(paraText);
    }
    
    /**
     * 清除指定范围的波浪线
     */
    public clearWavyUnderlineInRange(startPos: ParagraphContentPos, endPos: ParagraphContentPos): void {
        const wavyUnderlines = this.wavyUnderlineManager.getAllWavyUnderlines();
        const toRemove: string[] = [];
        
        for (const wavyUnderline of wavyUnderlines) {
            const wavyStart = wavyUnderline.getStartPos();
            const wavyEnd = wavyUnderline.getEndPos();
            
            // 检查是否有重叠
            if (!(wavyEnd.compare(startPos) < 0 || wavyStart.compare(endPos) > 0)) {
                toRemove.push(wavyUnderline.getName());
            }
        }
        
        // 删除重叠的波浪线
        for (const name of toRemove) {
            this.wavyUnderlineManager.removeWavyUnderline(name);
        }
    }
}

interface IWavyUnderlineOptions {
    content?: string;
    type?: WavyUnderlineType;
}
```

## 6. View 层集成

### 6.1 ParaBaseUI 组件修改

```typescript
// src/components/editor/module/document/ParaBaseUI.tsx 中添加
import { WavyUnderlineHandler } from './WavyUnderlineHandler';

export default class ParaBaseUI extends React.Component<IProps, IState> {
    private wavyUnderlineHandler: WavyUnderlineHandler;
    private containerRef: React.RefObject<HTMLDivElement>;
    
    constructor(props: IProps) {
        super(props);
        this.containerRef = React.createRef();
    }
    
    componentDidMount() {
        // 初始化波浪线处理器
        this.wavyUnderlineHandler = new WavyUnderlineHandler(this.props.documentCore);
        
        // 添加事件监听
        const container = this.containerRef.current;
        if (container) {
            container.addEventListener('mousemove', this.handleMouseMove);
            container.addEventListener('mouseleave', this.handleMouseLeave);
        }
    }
    
    componentWillUnmount() {
        // 清理事件监听
        const container = this.containerRef.current;
        if (container) {
            container.removeEventListener('mousemove', this.handleMouseMove);
            container.removeEventListener('mouseleave', this.handleMouseLeave);
        }
        
        // 销毁处理器
        if (this.wavyUnderlineHandler) {
            this.wavyUnderlineHandler.destroy();
        }
    }
    
    private handleMouseMove = (e: MouseEvent) => {
        if (this.wavyUnderlineHandler) {
            this.wavyUnderlineHandler.handleMouseMove(e);
        }
    }
    
    private handleMouseLeave = () => {
        if (this.wavyUnderlineHandler) {
            this.wavyUnderlineHandler.handleMouseLeave();
        }
    }
    
    render() {
        return (
            <div ref={this.containerRef} className="para-base-ui">
                {/* 现有渲染逻辑 */}
                {this.renderContent()}
                
                {/* 波浪线渲染逻辑 */}
                {this.renderWavyUnderlines()}
            </div>
        );
    }
    
    /**
     * 渲染波浪线
     */
    private renderWavyUnderlines() {
        const wavyUnderlineManager = this.props.documentCore.getWavyUnderlineManager();
        const activeWavyUnderline = wavyUnderlineManager.getActiveWavyUnderline();
        
        // 根据激活状态调整渲染样式
        // 现有的波浪线渲染逻辑保持不变，只是在激活时可以添加高亮效果
        
        return null; // 实际渲染逻辑在现有的 SVG 渲染中
    }
}
```

## 7. 样式设计

### 7.1 CSS 样式

```css
/* src/components/editor/style/wavyUnderlineTooltip.less */
.wavy-underline-tooltip {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.4;
    word-break: break-word;
    
    .wavy-tooltip-header {
        margin-bottom: 4px;
        
        .wavy-tooltip-type {
            font-weight: 600;
            font-size: 11px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
    }
    
    .wavy-tooltip-content {
        color: #333;
        font-size: 12px;
    }
    
    /* 不同类型的样式 */
    &.spell-check {
        border-left-color: #ff0000;
    }
    
    &.grammar {
        border-left-color: #00ff00;
    }
    
    &.suggestion {
        border-left-color: #0000ff;
    }
    
    &.warning {
        border-left-color: #ff8800;
    }
    
    &.custom {
        border-left-color: #8800ff;
    }
}

/* 激活状态的波浪线样式 */
.wavy-underline-active {
    opacity: 0.8;
    stroke-width: 1.5;
}
```

## 8. API 接口设计

### 8.1 外部调用接口

```typescript
// 公开的 API 接口
export interface IWavyUnderlineAPI {
    /**
     * 为选中文本添加波浪线
     */
    markTextWithWave(options?: {
        content?: string;
        type?: WavyUnderlineType;
    }): boolean;
    
    /**
     * 清除选中范围的波浪线
     */
    clearWavyUnderline(): boolean;
    
    /**
     * 清除所有波浪线
     */
    clearAllWavyUnderlines(): void;
    
    /**
     * 获取指定位置的波浪线信息
     */
    getWavyUnderlineAtPosition(x: number, y: number): WavyUnderline | null;
    
    /**
     * 设置波浪线类型配置
     */
    setWavyUnderlineTypeConfig(type: WavyUnderlineType, config: {
        color?: string;
        defaultContent?: string;
    }): void;
}
```

### 8.2 事件接口

```typescript
// 事件回调接口
export interface IWavyUnderlineEvents {
    /**
     * 波浪线被激活时触发
     */
    onWavyUnderlineActivated?: (wavyUnderline: WavyUnderline) => void;
    
    /**
     * 波浪线被取消激活时触发
     */
    onWavyUnderlineDeactivated?: (wavyUnderline: WavyUnderline) => void;
    
    /**
     * 提示框显示时触发
     */
    onTooltipShow?: (options: IWavyUnderlineTooltipOptions) => void;
    
    /**
     * 提示框隐藏时触发
     */
    onTooltipHide?: () => void;
}
```

## 9. 性能优化

### 9.1 优化策略

1. **位置计算优化**
   - 使用二分查找快速定位波浪线对象
   - 缓存鼠标位置，避免重复计算
   - 使用防抖技术减少计算频率

2. **内存管理**
   - 及时清理不再使用的波浪线对象
   - 使用对象池复用波浪线实例
   - 避免内存泄漏，正确移除事件监听器

3. **渲染优化**
   - 只在激活状态变化时重新渲染
   - 使用 CSS 动画而非 JavaScript 动画
   - 延迟显示/隐藏提示框避免闪烁

### 9.2 性能监控

```typescript
// 性能监控工具
export class WavyUnderlinePerformanceMonitor {
    private static instance: WavyUnderlinePerformanceMonitor;
    private metrics: Map<string, number[]> = new Map();
    
    public static getInstance(): WavyUnderlinePerformanceMonitor {
        if (!this.instance) {
            this.instance = new WavyUnderlinePerformanceMonitor();
        }
        return this.instance;
    }
    
    public recordMetric(name: string, value: number): void {
        if (!this.metrics.has(name)) {
            this.metrics.set(name, []);
        }
        this.metrics.get(name)!.push(value);
    }
    
    public getAverageMetric(name: string): number {
        const values = this.metrics.get(name) || [];
        return values.length > 0 ? values.reduce((a, b) => a + b) / values.length : 0;
    }
    
    public clearMetrics(): void {
        this.metrics.clear();
    }
}
```

## 10. 测试策略

### 10.1 单元测试

```typescript
// WavyUnderline.test.ts
describe('WavyUnderline', () => {
    test('should create wavy underline with default values', () => {
        const wavyUnderline = new WavyUnderline();
        expect(wavyUnderline.getId()).toBeGreaterThan(0);
        expect(wavyUnderline.isActive()).toBe(false);
        expect(wavyUnderline.getType()).toBe(WavyUnderlineType.SpellCheck);
    });
    
    test('should set and get content correctly', () => {
        const wavyUnderline = new WavyUnderline();
        const content = 'Test content';
        expect(wavyUnderline.setContent(content)).toBe(true);
        expect(wavyUnderline.getContent()).toBe(content);
    });
});

// WavyUnderlineManager.test.ts
describe('WavyUnderlineManager', () => {
    test('should add and retrieve wavy underlines', () => {
        const doc = new Document();
        const manager = new WavyUnderlineManager(doc);
        const wavyUnderline = new WavyUnderline();
        
        manager.addWavyUnderline(wavyUnderline);
        expect(manager.getAllWavyUnderlines()).toHaveLength(1);
    });
});
```

### 10.2 集成测试

```typescript
// 集成测试场景
describe('WavyUnderline Integration', () => {
    test('should show tooltip when mouse hovers over wavy underline', async () => {
        // 模拟用户选择文本并添加波浪线
        // 模拟鼠标移动到波浪线区域
        // 验证提示框是否正确显示
    });
    
    test('should hide tooltip when mouse leaves wavy underline', async () => {
        // 模拟鼠标离开波浪线区域
        // 验证提示框是否正确隐藏
    });
});
```

## 11. 部署和维护

### 11.1 发布清单

- [ ] 完成所有单元测试
- [ ] 完成集成测试
- [ ] 性能测试通过
- [ ] 代码审查通过
- [ ] 文档更新完成

### 11.2 维护计划

- 定期性能监控和优化
- 用户反馈收集和改进
- 新功能需求评估

## 12. 最终实现总结 (统一悬停机制)

### 12.1 最终方案优势

经过深入分析修订痕迹悬停机制和波浪线管理机制，我们成功设计了一个统一的解决方案，具有以下优势：

1. **架构统一性**：参考成熟的修订机制架构，确保与现有系统的一致性
2. **性能优化**：结合波浪线管理的二分查找算法，实现O(log n)的位置检测性能
3. **用户体验**：300ms延迟显示机制，避免快速移动时的闪烁效果
4. **可扩展性**：支持多种波浪线类型和自定义配置
5. **内存友好**：单例DOM管理模式，避免内存泄漏

### 12.2 核心创新点

- **双重索引系统**：Map + 排序数组的混合数据结构
- **智能位置检测**：复用DocumentCore的成熟位置计算逻辑
- **渐进式交互**：延迟显示 + 透明度动画的流畅体验
- **状态管理统一**：激活/非激活状态的集中管理

### 12.3 技术债务清理

通过这次重构，我们解决了以下技术债务：

1. **冗余代码消除**：统一了Canvas和SVG两套渲染逻辑
2. **性能瓶颈优化**：从O(n)线性查找优化到O(log n)二分查找
3. **内存泄漏修复**：规范化事件监听器的添加和移除
4. **架构一致性**：与现有批注和修订系统保持统一的设计模式

### 12.4 后续演进方向

1. **AI智能提示**：集成拼写和语法检查引擎
2. **多语言支持**：支持不同语言的本地化提示
3. **协作功能**：支持多用户协作时的波浪线同步
4. **移动端适配**：针对触屏设备的交互优化

### 12.5 关键文件清单

```
src/
├── model/core/WavyUnderline/
│   ├── WavyUnderline.ts                    # 波浪线数据模型
│   └── WavyUnderlineManager.ts             # 波浪线管理器
├── components/editor/module/document/
│   └── WavyUnderlineHoverManager.ts        # 悬停事件管理器
├── components/editor/style/
│   └── wavy-underline.less                 # 样式定义
└── model/DocumentCore.ts                   # 核心API接口
```

此方案成功实现了"结合修订机制的光标移动弹框机制和波浪线管理类机制"的设计目标，为编辑器提供了一个高性能、可扩展的波浪线提示系统。
- [ ] 代码审查完成
- [ ] 文档更新完成
- [ ] 兼容性测试通过

### 11.2 监控指标

1. **功能指标**
   - 波浪线创建成功率
   - 提示框显示响应时间
   - 内存使用情况

2. **性能指标**
   - 鼠标移动事件处理延迟
   - 位置计算耗时
   - 渲染帧率

3. **用户体验指标**
   - 提示框显示准确率
   - 交互响应时间
   - 错误率

## 12. 扩展计划

### 12.1 后续功能

1. **智能提示**
   - 集成拼写检查API
   - 语法检查集成
   - AI 建议功能

2. **交互增强**
   - 右键菜单操作
   - 快捷键支持
   - 批量操作功能

3. **自定义配置**
   - 主题配色支持
   - 动画效果配置
   - 提示框样式自定义

### 12.2 架构演进

1. **插件化架构**
   - 支持第三方波浪线类型
   - 可扩展的提示内容源
   - 自定义渲染器支持

2. **云端集成**
   - 云端拼写检查服务
   - 团队共享配置
   - 使用统计分析

---

## 总结

本设计方案完全遵循编辑器的 MVC 架构模式，参考成熟的批注系统设计，提供了完整的波浪线提示框功能。通过分层设计、性能优化和完善的测试策略，确保系统的稳定性和可扩展性。

该方案的核心优势：
- **架构合理**：符合现有编辑器架构模式
- **性能优化**：高效的位置计算和事件处理
- **用户体验**：流畅的交互和美观的界面
- **可扩展性**：支持多种波浪线类型和自定义配置
- **可维护性**：清晰的代码结构和完善的测试覆盖