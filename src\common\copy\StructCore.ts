import { NewControl } from '../../model/core/NewControl/NewControl';
import Paragraph from '../../model/core/Paragraph';
import { G_LABEL, StructurePro } from './apollo/NodeType';
import ParaPortion from '../../model/core/Paragraph/ParaPortion';

import { NewControlType, CodeValueItem, DateBoxFormat, FileSource, ICascade, ICascadeEvent } from '../commonDefines';

import TextProperty from '../../model/core/TextProperty';
import { DocumentContentType } from '../../model/core/Style';
import DocumentContentElementBase from '../../model/core/DocumentContentElementBase';
import { Table } from '../../model/core/Table';

import { ParaElementType } from '@/model/core/Paragraph/ParagraphContent';


export default class StructCore {
    private structs: any;
    private prevIndex: number;
    private newControl: NewControl;
    private doc: any;
    private host: any;
    // private portion: ParaPortion;
    private style: TextProperty;
    private parentName: string;
    private names: Map<string, string>;
    private topNewControlName: string;
    private curNewControl: NewControl;

    private buttonManager: any;
    private _buttons: Map<string, any>;
    private _cascadeMap: Map<string, any>;


    constructor(doc: any, host?: any) {
        this.doc = doc;
        this.host = host;
        this.structs = {};

        this._buttons = new Map();
        this.names = new Map();
        this._cascadeMap = new Map();

    }

    public isCanntEdit(): boolean {
        const bNewControl = this.doc.isNotOverNewControl();
        if (bNewControl) {
            return true;
        }
        const newControl = this.getCurrentNewControl();
        if (!newControl) {
            return false;
        }
        this.curNewControl = newControl;
        this.topNewControlName = newControl.getNewControlName();
        return newControl.isReadOnly();
    }

    public isNewSection(): boolean {
        if (!this.curNewControl) {
            return false;
        }

        return this.curNewControl.isNewSection();
    }

    public isFocusInNewControl(): NewControl {
        return this.curNewControl;
    }

    public isPlaceholder(): boolean {
        if (!this.curNewControl) {
            return false;
        }

        return this.curNewControl.isPlaceHolderContent();
    }

    /**
     * 简单元素复制粘贴，把多个段落合并成一个段落
     * @param contents 多个
     * @param para 返回数据
     * @returns 不在这里返回
     */
    public getContentsToPara(contents: DocumentContentElementBase[], para: Paragraph): void {
        if (!contents || contents.length === 0) {
            return;
        }

        contents.forEach((content) => {
            if (content.isRegion()) {
                this.getContentsToPara((content as any).content.content, para);
            } else if (content.isTable()) {
                const table = content as Table;
                table.content.forEach((row) => {
                    row.content.forEach((cell) => {
                        this.getContentsToPara(cell.content.content, para);
                    });
                });
            } else if (content.isParagraph()) {
                const portions = content.getContent();
                let activeIndex = para.content.length - 1;
                for (let index = 0, len = portions.length - 1; index < len; index++) {
                    para.addToContent(activeIndex++, portions[index]);
                }
            }
        });
    }

    /**
     * 节的复制粘贴，把多个元素分解成多个段落
     */
    public getContentsToParas(contents: DocumentContentElementBase[], paras: DocumentContentElementBase[]): void {
        if (!contents || contents.length === 0) {
            return;
        }

        contents.forEach((content) => {
            if (content.isRegion()) {
                this.getContentsToParas((content as any).content.content, paras);
            } else if (content.isTable()) {
                const table = content as Table;
                table.content.forEach((row) => {
                    row.content.forEach((cell) => {
                        this.getContentsToParas(cell.content.content, paras);
                    });
                });
            } else if (content.isParagraph()) {
                paras.push(content);
            }
        });
    }

    public resetPaste(): void {
        this.doc.getNewControlManager()
        .clearPasteControls();

        this.buttonManager?.clearCaches();

    }

    public getCurrentNewControlName(): string {
        return this.topNewControlName;
    }

    public checkDatas(datas: any): boolean {
        if (!this.newControl || this.newControl.getType() === NewControlType.Section) {
            return true;
        }

        if (datas.length === 0) {
            return false;
        }

        const type = DocumentContentType.Paragraph;

        const obj = datas.find((data) => data.getType() !== type);
        if (obj) {
            return false;
        }
        return true;
    }

    public setParentName(name: string): void {
        this.parentName = this.names.get(name) || this.topNewControlName;
    }

    public setPortionTextProp(name: string, portionName: string, textProp: TextProperty): void {
        if (!textProp) {
            return;
        }
        const struct = this.structs[name];
        if (!struct) {
            return;
        }
        const portion = struct[portionName];
        if (!portion) {
            return;
        }
        portion.textProperty = textProp.copy();
    }


    public insertButton(node: any, portion: ParaPortion): void {
        let buttonManager = this.buttonManager;
        if (!buttonManager) {
            buttonManager = this.buttonManager = this.doc.getButtonManager();
        }
        const attrs = node.attrs;
        const name = this.doc.makeParaButtonName(attrs[StructurePro.Name], this._buttons);
        const pos = portion.content.length;
        portion.addParaItem(pos, ParaElementType.ParaButton);
        const button: any =  portion.content[pos];
        button.setButtonProps({name, color: attrs.color, content: attrs.content, bPrint: this.parseBoolean(attrs[StructurePro.BPrint])});
        buttonManager.addCache(button);
    }


    public insertStruct(node: any, para: Paragraph, portion?: ParaPortion): {isPlaceholder: boolean} {
        const newControl = this.getCurrentNewControl();
        const inNisTable = this.doc.isInNISTable();

        let structNode: any;
        let textProp: TextProperty;
        if (portion) {
            structNode = node;
            textProp = portion.textProperty;
            structNode.group = undefined;
            structNode.cascade = undefined;
            structNode.eventInfo = undefined;

            const newControlManager = this.doc.getNewControlManager();
            const cascades = newControlManager.cascadeManager.getCascade(structNode.newControlName);
            if (cascades) {
                this._cascadeMap.set(structNode.newControlName, cascades);
            }

            // this.portion = portion;
            // console.log(portion)
        } else {
            structNode = this.parseAttrs(node);
            textProp = this.style;
        }

        // this.oldName = structNode.newControlName;
        if (newControl) {
            switch (newControl.getType()) {
                case NewControlType.DateTimeBox:
                    return;
                case NewControlType.Section:
                    // if (this.portion) {
                    //     this.addStruct1(structNode, para);
                    //     break;
                    // }
                    if (this.parentName === undefined) {
                        this.parentName = newControl.getNewControlName();
                    }
                    this.addStruct(structNode, para, textProp);
                    break;
                default:
                    // if (structNode.bPlaceholder === true) {
                    //     this.addPlaceHolderContent1(structNode, para);
                    //     break;
                    // }
                    const bNewControlStart = (portion && portion.isNewControlStart());
                    const bNewControlEnd = (portion && portion.isNewControlEnd());
                    if ( structNode.newControlTitle && 0 < structNode.newControlTitle.length && bNewControlStart ) {
                        this.addNewControlTitle(structNode, para);
                    }

                    if (structNode.bPlaceholder && !bNewControlEnd ) {
                        this.addPlaceHolderContent(structNode, para);
                    }
            }
            return {isPlaceholder: structNode.bPlaceholder};
        }

        if (inNisTable) {
            // if (structNode.bPlaceholder === true) {
            //     this.addPlaceHolderContent1(structNode, para);
            //     break;
            // }
            const bNewControlStart = (portion && portion.isNewControlStart());
            const bNewControlEnd = (portion && portion.isNewControlEnd());
            if ( structNode.newControlTitle && 0 < structNode.newControlTitle.length && bNewControlStart ) {
                this.addNewControlTitle(structNode, para);
            }

            if (structNode.bPlaceholder && !bNewControlEnd ) {
                this.addPlaceHolderContent(structNode, para);
            }
            return {isPlaceholder: structNode.bPlaceholder};
        }
        // if (this.portion) {
        //     this.addStruct1(structNode, para);
        //     return;
        // }

        this.addStruct(structNode, para, textProp);
        return {isPlaceholder: structNode.bPlaceholder};
    }

    public getCurrentNewControl(): NewControl {
        if (this.newControl) {
            return this.newControl;
        }
        this.newControl = this.doc.getCurrentNewControl();
        return this.newControl;
    }


    public addCascadeCaches(): void {
        const map = this._cascadeMap;
        if (!map.size) {
            return;
        }

        const cascadeManager = this.doc.getNewControlManager().cascadeManager;
        cascadeManager.addCaches(null);
        const cascade = new Map();
        const names = this.names;
        for (const [name, option] of map) {
            let item;
            if (typeof option === 'string') {
                item = JSON.parse(option);
            } else {
                item = option;
            }

            const cascades: ICascade[] = item.cascades;
            const events: ICascadeEvent = item.events;
            const res: {cascades: ICascade[], events: ICascadeEvent} = {} as any;
            if (cascades?.length) {
                const newCascades = [];
                cascades.forEach((data) => {
                    const targets = (data.target || '').split(',');
                    if (!targets.length) {
                        return;
                    }
                    let curTargets: string[] = [];
                    for (let index = 0, len = targets.length; index < len; index++) {
                        const curName = targets[index];
                        const newName = names.get(curName);
                        if (!newName) {
                            return;
                        }
                        curTargets.push(newName);
                    }
                    const obj = {...data};
                    obj.target = curTargets.join(',');
                    newCascades.push(obj);
                });
                if (newCascades.length) {
                    res.cascades = newCascades;
                }
            }

            if (events && events.event.length) {
                const newEvent = [];
                const allTartgets = {};
                const allNames = (events.target || '').split(',');
                let expression = events.expression;
                for (let curIndex = 0, len = events.event.length; curIndex < len; curIndex++) {
                    const event = events.event[curIndex];
                    const curTarget = (event.source || '');
                    let targets;
                    if (curTarget === '*') {
                        targets = allNames
                    } else {
                        targets = curTarget.split(',');
                    }
                    if (!targets.length) {
                        continue;
                    }
                    let curTargets: string[] = [];
                    for (let index = 0, len = targets.length; index < len; index++) {
                        const curName = targets[index];
                        const newName = names.get(curName);
                        if (!newName) {
                            break;
                        }
                        const reg = new RegExp(curName, 'g');
                        expression = expression?.replace(reg, (all, t): string => {
                            const curindex = all.length + t;
                            const curText = expression.charAt(curindex);
                            if (!curText || ['*', '/', '+', ')', '}', ']', ' '].includes(curText)) {
                                return newName
                            }
                            return all;
                        });
                        curTargets.push(newName);
                        allTartgets[newName] = true;
                    }
                    if (curTargets.length) {
                        newEvent.push({source: curTargets.join(','), triggerType: event.triggerType});
                    }
                }
                if (newEvent.length) {
                    res.events = {
                        target: Object.keys(allTartgets).join(','),
                        event: newEvent,
                        key: events.key,
                        action: events.action,
                        expression: expression,
                    }
                }
            }
            if (Object.keys(res).length) {
                const newName = names.get(name);
                cascade.set(newName, res);
            }
        }

        if (cascade.size) {
            cascadeManager.addCaches(cascade);
        }
    }

    private getNewControl(name: string): NewControl {
        return this.doc.getNewControlByName(name);
    }

    private addNewControlTitle(structNode: any, para: Paragraph): void {
        const content = structNode.newControlTitle;
        if (!content || 0 >= content.length ) {
            return;
        }

        const portion = new ParaPortion(para);
        portion.addText(content);
        para.addToContent(para.content.length - 1, portion);
    }

    private addPlaceHolderContent(structNode: any, para: Paragraph): void {
        const content = structNode.newControlPlaceHolder;
        if (!content) {
            return;
        }

        const portion = new ParaPortion(para);
        portion.addText(content);
        this.structs[structNode.newControlName] = {placeholder: portion};
        para.addToContent(para.content.length - 1, portion);
    }

    private addStruct(item: any, para: Paragraph, textProp: TextProperty): void {
        const option: any = item;
        const name: string = item.newControlName;
        const othersPor = this.structs[name];
        if (othersPor !== undefined) {
            this.setPortionTextProp(name, 'end', textProp);
            if (othersPor.placeholder) {
                return;
            }
            const appendPortion = othersPor.end;
            para.addToContent(para.content.length - 1, appendPortion);
            this.addFillContent(item.newControlType, appendPortion);
            return;
        }

        this.structs[name] = this.createNewControlBorder(para, option, textProp);

        // newControl.content.placeHolder.addContentReplacePlaceHolder(null);
    }

    private parseAttrs(node: any): any {
        const attrs = node.attrs;
        if (attrs.bStart === false) {
            this.style = this.host.getTextProperty(node.parentNode.attrs.style);
            return {
                newControlName: attrs[StructurePro.Name],
                newControlType: parseInt(attrs[StructurePro.Type], 10) || 1,
            };
        }

        const dateType = this.parseDateBoxFormat(attrs[StructurePro.DateBoxFormat]);
        const codeKey = StructurePro.CodeLabel;
        const valueKey = StructurePro.ValueLabel;
        // const newControlProperty: INewControlProperty = {
        const newControlProperty = {
            newControlName: attrs[StructurePro.Name],
            newControlSerialNumber: attrs[StructurePro.SerialNumber],
            newControlInfo: attrs[StructurePro.TipsContent],
            newControlPlaceHolder: attrs[StructurePro.Placeholder],
            startDate: attrs[StructurePro.StartDate],
            endDate: attrs[StructurePro.EndDate],
            newControlType: parseInt(attrs[StructurePro.Type], 10) || 1,
            isNewControlHidden: attrs[StructurePro.IsHidden] === '1',
            isNewControlCanntDelete: attrs[StructurePro.DeleteProtect] === '1',
            isNewControlCanntEdit: attrs[StructurePro.EditProtect] === '1',
            isNewControlCanntCopy: attrs[StructurePro.CopyProtect] === '1',
            isNewControlMustInput: attrs[StructurePro.IsMustFill] === '1',
            isNewControlShowBorder: attrs[StructurePro.ShowBorder] === '1',
            isNewControlReverseEdit: attrs[StructurePro.ReverseEdit] === '1',
            isNewControlHiddenBackground: attrs[StructurePro.ShowBackground] === '0',
            isShowValue: attrs[StructurePro.ShowValue] === '1',
            newControlDisplayType: this.parseAttrToNum(attrs[StructurePro.DisplayType]),
            newControlFixedLength: this.parseAttrToNum(attrs[StructurePro.FixedLength]),
            alignments: this.parseAttrToNum(attrs[StructurePro.Alignments]),
            newControlMaxLength: this.parseAttrToNum(attrs[StructurePro.MaxLength]),
            customProperty: this.parseCustomAttr(attrs[StructurePro.CustomProperty]),
            newControlItems: this.parseAttrItem(attrs[StructurePro.CodeValueItems]),
            prefixContent: attrs[StructurePro.PrefixContent],
            selectPrefixContent: attrs[StructurePro.SelectPrefixContent],
            separator: attrs[StructurePro.Separator],
            minValue: this.parseAttrToNum(attrs[StructurePro.MinValue]),
            maxValue: this.parseAttrToNum(attrs[StructurePro.MaxValue]),
            precision: this.parseAttrToNum(attrs[StructurePro.Precision]), // 精度
            unit: attrs[StructurePro.Unit],
            forceValidate: this.parseBoolean(attrs[StructurePro.ForceValidate]),
            dateBoxFormat: dateType,
            identifier: attrs[StructurePro.Identifier],
            tabJump: attrs[StructurePro.TabJump] === 'true',
            retrieve:  this.parseBoolean(attrs[StructurePro.Retrieve]),
            bPlaceholder: attrs[StructurePro.IsPlaceholder] === '1',
            checked: this.parseBoolean(attrs[StructurePro.Checked]),
            showRight: this.parseBoolean(attrs[StructurePro.ShowRight]),
            printSelected: this.parseBoolean(attrs[StructurePro.PrintSelected]),
            label: attrs[StructurePro.Label],
            labelCode: attrs[StructurePro.LabelCode],
            showType: this.parseAttrToNum(attrs[StructurePro.ShowType]),
            spaceNum: this.parseAttrToNum(attrs[StructurePro.SpaceNum]),

            supportMultLines: attrs[StructurePro.SupportMultLines] === '1',

            newControlTitle: attrs[StructurePro.Title],
            customFormat: this.parseAutoDateFormat(dateType, attrs[StructurePro.AutoDateFormat]),
            dateTime: attrs[StructurePro.DateTime],
            signatureCount: parseInt(attrs[StructurePro.SignatureCount], 10) || 1,
            preText: attrs[StructurePro.PreText],
            signatureSeparator: attrs[StructurePro.SignatureSeparator],
            postText: attrs[StructurePro.PostText],

            signType: attrs[StructurePro.SignType],
            alwaysShow: attrs[StructurePro.AlwaysShow],
            showSignBorder: this.parseBoolean(attrs[StructurePro.ShowSignBorder]),

            // cascade: this.getCascades(attrs[StructurePro.LogicEvent]),
            signaturePlaceholder: attrs[StructurePro.SignaturePlaceholder],
            signatureRatio: this.parseAttrToNum(attrs[StructurePro.SignatureRatio]),
            rowHeightRestriction: this.parseBoolean(attrs[StructurePro.RowHeightRestriction]),
            bTextBorder: this.parseBoolean(attrs[StructurePro.BTextBorder]),
            province: this.parseAddressPair(attrs[StructurePro.Province]),
            city: this.parseAddressPair(attrs[StructurePro.City]),
            county: this.parseAddressPair(attrs[StructurePro.County]),
            hierarchy: parseInt(attrs[StructurePro.Hierarchy], 10) || 3, // default lvl 3
            bShowCodeAndValue: this.parseBoolean(attrs[StructurePro.BShowCodeAndValue]),

            // text only
            hideHasTitle: attrs[StructurePro.HideHasTitle] === '1',
            eventInfo: undefined,
            codeLabel: attrs[codeKey] === G_LABEL ? '' : attrs[codeKey],
            valueLabel: attrs[valueKey] === G_LABEL ? '' : attrs[valueKey],
        };


        if (attrs[StructurePro.Cascades]) {
            this._cascadeMap.set(newControlProperty.newControlName, attrs[StructurePro.Cascades]);
        }

        // const eventInfo = attrs[StructurePro.EventInfo];
        // if (eventInfo) {
        //     try {
        //         newControlProperty.eventInfo = JSON.parse(eventInfo);
        //     } catch (error) {
        //         // tslint:disable-next-line: no-console
        //         console.error(error);
        //     }
        // }
        this.style = this.host.getTextProperty(node.parentNode.attrs.style);

        return newControlProperty;
    }

    private getCascades(str: string): any[] {
        if (str) {
            try {
                return JSON.parse(str);
            } catch (e) {
                //
            }
        }
        return;
    }

    private parseDateBoxFormat(str: string): number {
        if (!str) {
            return;
        }

        const num = parseInt(str, 10);
        if (isNaN(num)) {
            return;
        }

        return num;
    }

    private parseBoolean(str: string): boolean {
        if (!str) {
            return;
        }

        return str === '1';
    }

    private parseAttrItem(str: string): any {
        if (!str) {
            return;
        }

        let res: any;
        try {
            res = JSON.parse(str);
        } catch (error) {
            return;
        }
        if (!res || res.length === 0) {
            return;
        }

        return res.map((item) => {
            return new CodeValueItem(item.code, item.value, item.bSelect);
        });
    }

    private parseAddressPair(str: string): object {
        // console.log(str)
        if (str == null) {
            return;
        }

        let obj: object;
        try {
            obj = JSON.parse(str);
            // console.log(obj)
        } catch (error) {
            return;
        }
        if (obj == null || Object.keys(obj).length === 0) {
            return;
        }
        return obj;
    }

    private parseAutoDateFormat(type: number, str: string): object {
        if (!str) {
            return;
        }
        if (type !== DateBoxFormat.AutoCustom) {
            return;
        }

        let obj: object;
        try {
            obj = JSON.parse(str);
        } catch (error) {
            return;
        }
        if (Object.keys(obj).length === 0) {
            return;
        }
        return obj;
    }

    private parseAttrToNum(str: string): number {
        if (!str) {
            return;
        }

        const res = parseFloat(str);
        if (isNaN(res)) {
            return;
        }

        return res;
    }

    private parseCustomAttr(str: string): any {
        if (!str) {
            return;
        }

        let res: any;
        try {
            res = JSON.parse(str);
        } catch (error) {
            return;
        }
        if (!res || res.length === 0) {
            return;
        }
        return res.map((prop) => {
            return {
                ...prop,
            };
        });
    }

    private createNewControlBorder(para: Paragraph, newControlProperty: any, textProp: TextProperty): any {
        const doc = this.doc;
        // const placeHolderContent = attrs.bPlaceholder ? attrs.placeHolder : '';
        // const newControlProperty = {
        //     newControlName: null,
        //     newControlInfo: '',
        //     newControlPlaceHolder: attrs.placeHolder,
        //     newControlType: attrs.controlType,
        //     isNewControlHidden: false,
        //     isNewControlCanntDelete: attrs.bCanntDelete,
        //     isNewControlCanntEdit: attrs.bCanntEdit,
        //     isNewControlMustInput: attrs.bMustInput,
        //     isNewControlShowBorder: attrs.bShowBorder,
        //     isNewControlReverseEdit: attrs.bReverseEdit,
        //     isNewControlHiddenBackground: attrs.bHiddenBackground,
        //     newControlDisplayType: 1,
        //     newControlFixedLength: 0,
        //     newControlMaxLength: 0,
        // };
        const oldName = newControlProperty.newControlName;
        const newControlManager = doc.getNewControlManager();
        const newName = newControlProperty.newControlName = newControlManager
            .makeUniqueName(newControlProperty.newControlType, oldName, FileSource.Copy);
        const newControl: NewControl = newControlManager.createNewControl(para, newControlProperty);
        // if ( newControlProperty.bPlaceholder && newControl ) {
        //     const oldControl = newControlManager.getNewControlByName(newName);
        //     const textProperty = oldControl ? oldControl.getStartBorderPortion().textProperty :
        //                                     newControl.getStartBorderPortion().textProperty;
        //     newControl.initPlaceHolderDefaultTextProperty(textProperty);
        // }
        this.names.set(oldName, newName);
        // let contentPos: any;
        if (this.prevIndex === undefined) {
            // const curPara: Paragraph = doc.getCurrentParagraph() as Paragraph;
            // if (!curPara) {
            //     return;
            // }

            // contentPos = curPara.getCurContentPosInDoc();
            newControlManager.clearPasteControls();
        }

        newControlManager.addPasteControl({control: newControl, undefined, parentName: this.parentName}, true);
        this.prevIndex = 1;
        this.parentName = undefined;

        const startPortion = newControl.getStartBorderPortion();
        startPortion.textProperty = textProp.copy();
        para.addToContent(para.getContent.length - 1, startPortion);
        if (newControlProperty.bPlaceholder === true) {
            const portion = newControl.getPlaceHolder();
            const endPortion = newControl.getEndBorderPortion();
            para.addToContent(para.getContent.length - 1, portion);
            para.addToContent(para.getContent.length - 1, endPortion);
            this.addFillContent(newControlProperty.newControlType, portion, newControl);
            return {placeholder: portion, end: endPortion};
        }

        newControl.getStartBorderPortion()
                .setPlaceHolder1(false);

        newControl.getEndBorderPortion()
                .setPlaceHolder1(false);


        // if (attrs.bMustInput === true && attrs.bPlaceholder !== true) {
        //     const contents = placeHolder.content;
        //     if (contents[1].content === '*') {
        //         contents.splice(contents.length - 2, 1);
        //         contents.splice(1, 1);
        //     }
        // }

        // placeHolder.setPlaceHolder1(attrs.bPlaceholder);
        // const endPortion = placeHolder
        //     .splitPortion(placeHolder.content.length / 2);

        return {end: newControl.getEndBorderPortion()};
    }

    private addFillContent(type: number, portion?: any, newControl?: any): void {
        const types = [NewControlType.CheckBox, NewControlType.RadioButton, NewControlType.MultiRadio];
        if (!types.includes(type)) {
            return;
        }

        if (newControl === undefined) {
            newControl = this.doc.getNewControlManager()
                .getPasteNewControlByName(portion.getEndNewControlName());
            if (!newControl) {
                return;
            }
        }

        newControl.addContent();
    }

    // private addRadioButton(type: string)
}
