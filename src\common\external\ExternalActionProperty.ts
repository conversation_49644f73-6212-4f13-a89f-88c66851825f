import OperateDocument from './OperateDocument';
import { DocumentCore } from '../../model/DocumentCore';
import { EmrEditor } from '../../components/editor/Main';
import DocumentProperty from './DocumentProperty';
import DocumentPage from './DocumentPage';
import OperateAISuggestion from './OperateAISuggestion';
import EditDocument from './EditDocument';
import NewControl from './NewControl';
import CursorPosition from './CursorPosition';
import Drawing from './Drawing';
import Comment from './Comment';
import ListenEvent from './ListenEvent';
import Table from './Table';
import Region from './Region';
import HeaderFooter from './HeaderFooter';
import Recension from './Recension';

export class ExternalActionProperty {
    protected _documentCore: DocumentCore;
    protected _host: EmrEditor;
    private _operateDocument: OperateDocument;
    private _documentProperty: DocumentProperty;
    private _documentPage: DocumentPage;
    private _editDocument: EditDocument;
    private _newControl: NewControl;
    private _cursorPosition: CursorPosition;
    private _drawing: Drawing;
    private _comment: Comment;
    private _listen: ListenEvent;
    private _table: Table;
    private _region: Region;
    private _header: HeaderFooter;
    private _recension: Recension;
    private _aiSuggestion: OperateAISuggestion;

    constructor(host: EmrEditor) {
        this._host = host;
        this._documentCore = host.state.documentCore;
    }

    protected _close(): void {
        this._documentCore = null;
        this._host = null;
        this._operateDocument = null;
        this._documentProperty = null;
        this._documentPage = null;
        this._editDocument = null;
        this._newControl = null;
        this._cursorPosition = null;
        this._drawing = null;
        this._comment = null;
        this._listen = null;
        this._table = null;
        this._region = null;
        this._header = null;
        this._recension = null;
    }

    protected _getHeader(): HeaderFooter {
        if (!this._header) {
            this._header = new HeaderFooter(this._host);
        }

        return this._header;
    }

    protected _getRecension(): Recension {
        if (!this._recension) {
            this._recension = new Recension(this._host);
        }

        return this._recension;
    }

    protected _getRegion(): Region {
        if (!this._region) {
            this._region = new Region(this._host);
        }

        return this._region;
    }

    protected _getTable(): Table {
        if (!this._table) {
            this._table = new Table(this._host);
        }

        return this._table;
    }

    protected _getDocmentProperty(): DocumentProperty {
        if (!this._documentProperty) {
            this._documentProperty = new DocumentProperty(this._host);
        }

        return this._documentProperty;
    }

    protected _getOperateDocument(): OperateDocument {
        if (!this._operateDocument) {
            this._operateDocument = new OperateDocument(this._host);
        }

        return this._operateDocument;
    }

    protected _getDocumentPage(): DocumentPage {
        if (!this._documentPage) {
            this._documentPage = new DocumentPage(this._host);
        }

        return this._documentPage;
    }

    protected _getListenEvents(): ListenEvent {
        if (!this._listen) {
            this._listen = new ListenEvent(this._host);
        }

        return this._listen;
    }

    protected _getEditDocument(): EditDocument {
        if (!this._editDocument) {
            this._editDocument = new EditDocument(this._host);
        }

        return this._editDocument;
    }

    protected _getNewControl(): NewControl {
        if (!this._newControl) {
            this._newControl = new NewControl(this._host);
        }
        return this._newControl;
    }

    protected _getCursorPosition(): CursorPosition {
        if (!this._cursorPosition) {
            this._cursorPosition = new CursorPosition(this._host);
        }

        return this._cursorPosition;
    }

    protected _getDrawing(): Drawing {
        if (!this._drawing) {
            this._drawing = new Drawing(this._host);
        }

        return this._drawing;
    }

    protected _getComment(): Comment {
        if (!this._comment) {
            this._comment = new Comment(this._host);
        }

        return this._comment;
    }

    protected _getAISuggestion(): OperateAISuggestion {
        if (!this._aiSuggestion) {
            this._aiSuggestion = new OperateAISuggestion(this._host);
        }

        return this._aiSuggestion;
    }
}
