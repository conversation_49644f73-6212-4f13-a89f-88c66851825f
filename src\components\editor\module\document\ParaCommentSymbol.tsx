import { DocumentCore } from "@/model/DocumentCore";
import React from "react";
import {
    GlobalEvent as gEvent,
    GlobalEventName as gEventName
} from '@/common/GlobalEvent';
import { CommentOperatorType, ResultType, ViewModeType } from "@/common/commonDefines";

interface IProps {
    bShowAnnotation: boolean;
    documentCore: DocumentCore;
    pageInfo: any;
    refresh: () => void;
}

interface IState {
    addNewPos: any;
}

export default class ParaCommentAddSymbol extends React.Component<IProps, IState> {
    private _symbolRef: any;
    private _docId: number;

    constructor(props: IProps) {
        super(props);
        this._symbolRef = React.createRef();
        this.state = {
            addNewPos: {
                pageIndex: 0,
                top: 0,
                bShow: false,
            },
        };
        this._docId = props.documentCore.getCurrentId();
    }

    public render(): React.ReactNode {
        const {pageIndex, top, bShow} = this.state.addNewPos;
        const {width, height, scale, paddingRight} = this.props.pageInfo;
        const pageHeight = height + 20;
        const htmlStyle: any = {
            top: Math.floor(pageIndex * pageHeight + top * scale) + 2 + 'px',
        };
        if (!bShow || !this.props.bShowAnnotation) {
            htmlStyle.display = 'none';
        }
        const realPageWidth = Math.floor(width * scale);
        let left = realPageWidth - 33;
        // 计算右侧定位边距
        const viewMode = this.props.documentCore.getViewMode();
        if (viewMode === ViewModeType.CompactView) {
            left = realPageWidth - 18;
            htmlStyle.width = '17px';
            htmlStyle.height = '17px';
        } else if (paddingRight < 42) {
            const release = Math.floor(42 - paddingRight);
            let width = 25;
            if (release < 8) {
                left += release;
            } else {
                left += 8;
                const sub = Math.ceil((release - 8) / 2);
                left += sub;
                width -= sub;
                left = left > realPageWidth - 18 ? realPageWidth - 18 : left;
                width = width < 17 ? 17 : width;
            }
            htmlStyle.width = width + 'px';
            htmlStyle.height = width + 'px';
        }
        htmlStyle.left = left + 'px';
        return (
            <div
                className='comment-add-symbol'
                style={htmlStyle}
                ref={this._symbolRef}
            >
                <span className='iconfont comments' />
            </div>
        );
    }

    public componentDidMount(): void {
        const dom = this._symbolRef.current as HTMLDivElement;
        if (dom) {
            dom.addEventListener('click', this.handleClick);
        }
        gEvent.addEvent(this._docId, gEventName.CommentChange, this.handleCommentChange);
        gEvent.addEvent(this._docId, gEventName.MoveCursor, this.handleCursorChange);
    }

    public componentWillUnmount(): void {
        const dom = this._symbolRef.current as HTMLDivElement;
        if (dom) {
            dom.removeEventListener('click', this.handleClick);
        }
        const docId = this._docId;
        gEvent.deleteEvent(docId, gEventName.CommentChange, this.handleCommentChange);
        gEvent.deleteEvent(docId, gEventName.MoveCursor, this.handleCursorChange);
    }

    private handleClick = (event: any) => {
        const {documentCore} = this.props;
        if (documentCore.showCommentPanel(true) === ResultType.Success) {
            this.props.refresh();
        }
        gEvent.setEvent(
            documentCore.getCurrentId(),
            gEventName.CommentChange,
            CommentOperatorType.New,
            this.state.addNewPos
        );
    }

    private handleCommentChange = (type: CommentOperatorType, posInfo?: any) => {
        if (!this.props.bShowAnnotation) {
            return;
        }
        if (type === CommentOperatorType.AddNewSymbol) {
            if (posInfo) {
                const {pageIndex, top, bShow} = this.state.addNewPos;
                if (
                    pageIndex !== posInfo.pageIndex ||
                    top !== posInfo.top ||
                    bShow !== posInfo.bShow
                ) {
                    this.setState({
                        addNewPos: {
                            pageIndex: posInfo.pageIndex,
                            top: Math.floor(posInfo.top),
                            bShow: !!posInfo.bShow,
                        }
                    });
                }
            } else {
                if (this.state.addNewPos.bShow) {
                    this.setState({
                        addNewPos: {
                            bShow: false,
                        }
                    });
                }
            }
        }
    }

    private handleCursorChange = (curPos: any) => {
        // if (!this.props.bShowAnnotation) {
        //     return;
        // }
        const documentCore = this.props.documentCore;
        if (0 === documentCore.getAllContents().length) {
            return;
        }

        const inUnInsert = documentCore.isCommentInUnInsertNewControl();
        documentCore.activeComment(inUnInsert ? '' : undefined);
        gEvent.setEvent(
            this._docId,
            gEventName.CommentChange,
            CommentOperatorType.AddNewSymbol,
            {
                pageIndex: curPos.pageNum,
                top: curPos.y1,
                bShow: !inUnInsert,
            }
        );
    }

}
