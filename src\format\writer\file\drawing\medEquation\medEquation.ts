import { EquationType } from '../../../../../common/commonDefines';
import { XmlComponent, XmlAttributeComponent } from '../../xml-components';
import { MathValue } from './mathValue';
import { MathText } from './mathText';
import { customEncodeURIComponent } from '../../../../../common/commonMethods';

export interface IMedEquationAttributesProperties {
  name: string;
  width: number;
  height: number;
  imageRatio: number;
  href?: string;
  source?: string;
  mathType: EquationType;
  vertAlign?: number,
}

export interface IMedEquationAllProperties {
  name: string;
  width: number;
  height: number;
  imageRatio: number;
  href?: string;
  source?: string;
  mathType: EquationType;
  mathValue: string; // equationElem Text
  mathText: string; // for future text abstraction
  vertAlign?: number,
}

class MedEquationAttributes extends XmlAttributeComponent<IMedEquationAttributesProperties> {
  // <T> -> type variable, shape this.root in base
  protected xmlKeys: any = {
    name: 'name',
    width: 'width',
    height: 'height',
    imageRatio: 'ratio',
    href: 'href', // will discard if empty
    source: 'source',
    mathType: 'math-type',
    vertAlign: 'vertAlign',
  };
}

export class MedEquation extends XmlComponent {
  constructor(attrs: IMedEquationAttributesProperties) {
    super('oMediaMath');
    this.root.push(new MedEquationAttributes(attrs));
  }

  public addMathValue(mathValue: string): MedEquation {
    this.root.push(new MathValue(customEncodeURIComponent(mathValue)));
    return this;
  }

  public addMathText(mathText: string): MedEquation {
    // has check, no need to check here
    this.root.push(new MathText(mathText));
    return this;
  }
}
