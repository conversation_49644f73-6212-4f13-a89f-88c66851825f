export function convertHexStringToBytes(str: string): number[] {
    const res = [];
    const len = str.length / 2;
    let index: number;
    for (index = 0; index < len; index++) {
        res.push(parseInt(str.substr(2 * index, 2), 16));
    }

    return res;
}

export function convertBytesToBase64(bytes: number[]): string {
    let res = '';
    const len = bytes.length;
    let index: number;
    const words = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';
    for (index = 0; index < len; index += 3) {
        const hex = bytes.slice(index, index + 3);
        const hexLen = hex.length;
        const datas = [];
        let curIndex: number;
        if (3 > hexLen) {
            for (curIndex = hexLen; 3 > curIndex; curIndex++) {
                hex[curIndex] = 0;
            }
        }

        // tslint:disable-next-line: no-bitwise
        datas[0] = (hex[0] & 252) >> 2;
        // tslint:disable-next-line: no-bitwise
        datas[1] = (hex[0] & 3) << 4 | hex[1] >> 4;
        // tslint:disable-next-line: no-bitwise
        datas[2] = (hex[1] & 15) << 2 | (hex[2] & 192) >> 6;
        // tslint:disable-next-line: no-bitwise
        datas[3] = hex[2] & 63;
        for (curIndex = 0; 4 > curIndex; curIndex++) {
            res = curIndex <= hexLen ? res + words.charAt(datas[curIndex]) : res + '\x3d';
        }
    }

    return res;
}

/**
 * 编码html标签组成的敏感部分
 * @param content: 内容
 */
export function encodeTag(content: string): string {
    if (!content) {
        return '';
    }

    return content.replace(/(<)|(>)|(')|(")/g, (m, m1, m2, m3, m4) => {
        if (m1) {
            return '&lt;';
        }
        if (m2) {
            return '&gt;';
        }
        if (m3) {
            return '&#x27;';
        }
        if (m4) {
            return '&quot;';
        }
    });
}

/**
 * 解码html标签组成的敏感部分
 * @param content: 内容
 */
export function decodeTag(content: string): string {
    if (!content) {
        return '';
    }

    return content.replace(/(&lt;)|(&gt;)|(&#x27;)|(&quot;)|(&nbsp;)|(\t)|(\r|\n)/g,
        (str, m1, m2, m3, m4, m5, m6, m7) => {
            if (m1) {
                return '<';
            }
            if (m2) {
                return '>';
            }
            if (m3) {
                return '\'';
            }
            if (m4) {
                return '"';
            }
            if (m5) {
                return ' ';
            }
            if (m6) {
                return '  ';
            }
            if (m7) {
                return '';
            }
    });
}
