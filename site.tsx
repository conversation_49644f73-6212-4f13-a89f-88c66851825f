import React, { createRef, RefObject } from 'react';
import { EmrEditor } from './src/components/editor/Main';
import { WasmInstance } from './src/common/WasmInstance';
import { isGlobalTestData, consoleLog, editorCallback } from './src/common/GlobalTest';
import ExternalEvent from './src/common/external/Event';
import {fetchWithSignature} from './src/common/ParseWater';
import { setTheme } from '@hz-editor/theme';
import { initCustomFont } from './src/model/core/util-custom-font'
import * as ReactDOM from 'react-dom/client';
//import * as ReactDOM from 'react-dom';
import { IExternalEvent } from '@/common/external/IExternalInterface';

interface IEditorOptions {
    bShowMenu?: boolean;
    bShowToolbar?: boolean;
    bNeedCustomFont?: boolean;
}

class EditorSite {
    private customCallbacks: {[key: string]: any};
    private customIndex: number;
    private currentEditorId: string;
    private editor: any;
    private vmEditorRef: RefObject<EmrEditor> = createRef<EmrEditor>();
    private tx: {a?: string};
    constructor() {
        this.addEvent();
    }

    public async init(url: string, token?: any): Promise<void> {
        await WasmInstance.createWasmInstsanceAsync();
        // this.dispatchEditorEvent2('aaa');
        /* IFTRUE_WATER */
        await this.getServerInfo();
        /* FITRUE_WATER */
        if (!token) {
            return;
        }
        await fetchWithSignature(url);
    }

    // public createEditor = (options: IEditorOptions = {}): string => {
    //     const appWrap = document.getElementById('hz-editor-app');
    //     const container = document.createElement('div');
    //     const id = `hz-editor-${Date.now()}`;
    //     container.className = 'hz-editor-instance';
    //     this.currentEditorId = container.id = id;
    //     appWrap.appendChild(container);

    //     const theme = options['theme'];
    //     if (options['textColorChangeInRevision'] === undefined) {
    //         options['textColorChangeInRevision'] = 1;
    //     }
    //     if (theme && typeof theme === 'object') {
    //         delete options['theme'];
    //         setTheme(theme);
    //     }
    //     const editor = ReactDOM.render(<EmrEditor {...options}/>, container) as any;

    //     editor.setInlineEvent((...params) => {
    //         parent.postMessage({__emrType: 'inline', eventType: 'inlineEvent', params, editorId: id}, '*');
    //     });
    //     const events = {};
    //     const keys = Object.keys(ExternalEvent.prototype);
    //     keys.forEach((name) => {
    //         events[name] = (...params) => {
    //             if (isGlobalTestData()) {
    //                 consoleLog.apply(undefined, params);
    //             }
    //             parent.postMessage({__emrType: 'event', eventType: name, params, editorId: id}, '*');
    //         };
    //     });

    //     editor.setEditEvent(events);
    //     this.editor = editor;
    //     return id;
    // }

    // //add by tinyzhi on 24.08.15
    public async createEditor(options: IEditorOptions = {}): Promise<string> {
        const appWrap = document.getElementById('hz-editor-app');
        const container = document.createElement('div');
        const id = `hz-editor-${Date.now()}`;
        container.className = 'hz-editor-instance';
        this.currentEditorId = container.id = id;
        appWrap.appendChild(container);
    
        const theme = options['theme'];
        if (options['textColorChangeInRevision'] === undefined) {
            options['textColorChangeInRevision'] = 1;
        }
        if (theme && typeof theme === 'object') {
            delete options['theme'];
            setTheme(theme);
        }
    
        // 创建 React Root 并渲染组件
        const root = ReactDOM.createRoot(container);
        root.render(<EmrEditor {...options} ref={this.vmEditorRef} />);
    
        // 等待组件实例渲染完成
        await new Promise<void>((resolve) => {
            const checkInstance = () => {
                if (this.vmEditorRef.current) {
                    resolve(); // 解析 Promise
                } else {
                    setTimeout(checkInstance, 100); // 继续检查
                }
            };
            checkInstance();
        });
    
        const editor = this.vmEditorRef.current as any;
        editor.setInlineEvent((...params) => {
            parent.postMessage({__emrType: 'inline', eventType: 'inlineEvent', params, editorId: id}, '*');
        });
        
        const events = {};
        const keys = Object.keys(ExternalEvent.prototype);
        keys.forEach((name) => {
            events[name] = (...params) => {
                if (isGlobalTestData()) {
                    consoleLog.apply(undefined, params);
                }
                parent.postMessage({__emrType: 'event', eventType: name, params, editorId: id}, '*');
            };
        });
        
        // 添加inlineHeightChange事件处理
        events['inlineHeightChange'] = (rectInfo) => {
            if (isGlobalTestData()) {
                consoleLog('inlineHeightChange', rectInfo);
            }
            // 将内联高度变化事件发送给父窗口
            parent.postMessage({__emrType: 'event', eventType: 'inlineHeightChange', params: [rectInfo], editorId: id}, '*');
            // 同时发送为内联事件，保持兼容性
            parent.postMessage({__emrType: 'inline', eventType: 'inlineEvent', params: [rectInfo], editorId: id}, '*');
        };
    
        editor.setEditEvent(events);
        this.editor = editor;
        return id;
    }
    
    
    public removeEditor(id: string): string {
        return;
    }

    /**
     * wasm回调
     * @param str 回调字符串
     */
    private dispatchEditorEvent2 = (str: string): void => {
        this.tx.a = str;
        editorCallback.text = str;
    }

    // 超时使用
    private dispatchEditorEvent1 = (): Promise<string> => {
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve('');
            }, 1000);
        });
    }

    // 设置wasm字符进行回调
    private dispatchEditorEvent3 = (): Promise<string> => {
        return new Promise((resolve) => {
            if (this.tx.a !== undefined) {
                return resolve(this.tx.a);
            }

            Object.defineProperty(this.tx, 'a' , {
                set: function(value) {
                    // this.a = value;
                    this.tx = null;
                    resolve(value);
                }
            });
        });
    }

    private getServerInfo(): Promise<string> {
        return new Promise((resolve) => {
            Promise.race([this.dispatchEditorEvent3(), this.dispatchEditorEvent1()])
            .then((res) => {
                resolve(res);
            });
        });
        // const fn = ;
        // const str = await (await fn)();
        // return str;
    }

    private addEvent(): void {
        window.addEventListener('message', async (e) => {
            const data = e.data;
            const {method, params = [], editorId, callId, __emrType} = data;

            if (!method && !callId && __emrType !== 'invork') {
                return;
            }

            if (isGlobalTestData()) {
                consoleLog('方法进行调用：', method, data);
            }

            let result: any;
            const isInstanceInvork = !!editorId;
            if (isInstanceInvork && editorId === this.currentEditorId) {
                const editor = this.editor; // ALL_EDITORS.get(editorId);
                if (method === '___custom') {
                    this.getCustomInfo(params);
                    return;
                }
                const instance = editor.getAsyncEditor();
                if (method === 'inlinePageProp') {
                    editor.inlinePageProp.apply(editor, params);
                    result = true;
                } else {
                    if (typeof instance[method] !== 'function') {
                        if (isGlobalTestData()) {
                            consoleLog(method, '方法找不到', instance[method]);
                        }
                        return;
                    }
                    result = await instance[method].apply(instance, params);
                }
            } else if (this[method] instanceof Function && ['init', 'createEditor'].includes(method)) {
                origin = e.origin;
                result = await this[method].apply(this, params);
            } else {
                throw new Error(`API ${method} not exits`);
            }
            if (isGlobalTestData()) {
                consoleLog('方法请求结束', method, result);
            }
            // const source: any = e.source;
            window.parent.postMessage({result, callId, editorId, __emrType: 'callback', method}, '*');
        }, false);

        editorCallback.errorGetItem = (key: string): Promise<string> => {
            return new Promise((resolve, reject) => {
                const index = this.customIndex;
                this.customCallbacks[index] = (params: any[]): void => {
                    delete this.customCallbacks[index];
                    editorCallback.data = params[0];
                    resolve(params[0]);
                };
                this.setCustomEvent({key, index: this.customIndex++}, 'getItem');
            });
        };

        editorCallback.errorSetItem = (key: string, value: string): Promise<void> => {
            return new Promise((resolve, reject) => {
                const index = this.customIndex;
                this.customCallbacks[index] = (params) => {
                    delete this.customCallbacks[index];
                    resolve();
                };
                this.setCustomEvent({key, value, index: this.customIndex++}, 'setItem');
            });
        };

        editorCallback.errorRemoveItem = (key: string): Promise<void> => {
            return new Promise((resolve, reject) => {
                const index = this.customIndex;
                this.customCallbacks[index] = (params: any): void => {
                    delete this.customCallbacks[index];
                    resolve();
                };
                this.setCustomEvent({key, index: this.customIndex++}, 'removeItem');
            });
        };

        this.customIndex = 0;
        this.tx = {};
        this.customCallbacks = {};
        editorCallback.addFunc(this.dispatchEditorEvent2);
    }

    private setCustomEvent(option: any, eventType: string, type?: string): void {
        const param = {eventType, editorId: this.currentEditorId, __emrType: type || 'custom', params: undefined};

        if (type && option) {
            Object.assign(param, option);
        }

        if (['getItem', 'removeItem', 'setItem'].includes(eventType)) {
            param.params = [option];
        } else if (option) {
            // Object.assign(param, option);
            param.params = Object.values(option);

        }

        window.parent.postMessage(param, '*');
    }

    private getCustomInfo(arrs: any[]): any {
        if (arrs && arrs[0]) {
            const fn = this.customCallbacks[arrs[0]];
            if ( fn && typeof fn === 'function') {
                fn(arrs.slice(1));
            }
        }
    }

    // private editorWriteLogs = (content: string) => {
    //     if (!content) {
    //         return;
    //     }

    //     if (isGlobalTestData()) {
    //         consoleLog(content);
    //     }

    //     this.setCustomEvent({result: content}, 'writeLog', 'external');
    // }

    // private setCurrentEditor(id: string): string {
    //     if (ALL_EDITORS.has(id)) {
    //       currentEditor = ALL_EDITORS.get(id);
    //     } else {
    //       throw new Error(`editor ${id} not exist!`);
    //     }

    //     ALL_EDITORS.forEach((editor, id) => {
    //       const container = document.getElementById(id);
    //       container.style.display = editor === currentEditor ? 'block' : 'none';
    //     });
    //     return id;
    // }
}

new EditorSite();
