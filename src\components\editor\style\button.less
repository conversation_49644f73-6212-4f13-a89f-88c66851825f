@import './global.less';
.hz-editor-container .editor-button {
    display: inline-block;
    line-height: 1;
    white-space: nowrap;
    cursor: pointer;
    background: #fff;
    border: 1px solid @borderColor;
    color: #606266;
    -webkit-appearance: none;
    text-align: center;
    box-sizing: border-box;
    outline: none;
    margin: 0;
    transition: .1s;
    font-weight: 600;
    font-family: @fontFamily;
    -moz-user-select: none;
    -webkit-user-select: none;
    -ms-user-select: none;
    padding: 8px 35px;
    font-size: @fontSize;
    border-radius: 2px;

    &.primary {
        color: #fff;
        background-color: @activeColor;
        border-color: @activeColor;
    }

    &.disabled {
        color: @readonlyColor;
        cursor: disabled;
        background-color: @disabledColor;
        border-color: @disabledColor;
    }
}