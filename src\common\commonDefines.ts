import { PageOrientation } from '../format/writer/file/document/body/section-properties/page-size';
import { SectionPropertiesOptions } from '../format/writer/file/document';
import { IContentControlStartElementVals, IContentControlEndElementVals } from '../format/writer/file/paragraph/contentControl/contentControlElements';
import { IRegionProps } from '../format/writer/file/region';
import { DocumentColor, DocumentBorder, DocumentShadow } from '../model/core/Style';
import { ITableRowPropertiesProperties } from '../format/writer/file/table/table-row';
import { TableRowLineRule } from '../model/core/Table/TableRowProperty';
import { TableMeasurement, TableBorderLineStyle } from '../model/core/TableProperty';
import { VerticalMergeType, TextDirectionType } from '../model/core/Table/TableCellProperty';
import TextProperty from '../model/core/TextProperty';

import { getTheme, INewControlTheme } from '@hz-editor/theme';
import { checkFontAvailability, skipEscapeString } from './commonMethods';
import { IAddressPair } from '../components/editor/module/document/NewAddressbox';
//import { MarkFactory } from './MarkFactory';


// tslint:disable-next-line: sy-global-const-name
export const isMacOs = navigator.userAgent.toLowerCase()
        .indexOf('mac') > -1;

/**
 * 粗体
 */
// export const FONT_WEIGHT_TYPE = {
//             0: 'normal',
//             1: 'bold',
// };
export const FONT_WEIGHT_TYPE = new Map([
    [0, 'normal'],
    [1, 'bold'],
]);

const codeNum = 97;

export const REGION_MAX_DEPTH = 2;

/**
 * 斜体
 */
// export const FONT_STYLE_TYPE = {
//             0: 'normal',
//             1: 'italic',
// };
export const FONT_STYLE_TYPE = new Map([
    [0, 'normal'],
    [1, 'italic'],
]);

const v_text = 'JUU2JUEzJUFFJUU0JUJBJUJGJUU3JUJDJTk2JUU4JUJFJTkxJUU1JTk5JUE4JUU3JTg5JTg4JUU2JTlEJTgzJUU1JUJEJTkyJUU0JUI4JThBJUU2JUI1JUI3JUU2JUEzJUFFJUU0JUJBJUJGJUU1JThDJUJCJUU3JTk2JTk3JUU3JUE3JTkxJUU2JThBJTgwJUU2JTlDJTg5JUU5JTk5JTkwJUU1JTg1JUFDJUU1JThGJUI4JUU2JTg5JTgwJUU2JTlDJTg5';
window['name1'] = new Proxy({}, {get: function(target, attr) {
    if (attr !== 'showInfomation') {
        return undefined;
    }

    return decodeURIComponent(window.atob(v_text));
}})

export const GLOBAL_DEFAULT_FONTSIZE = 14;
export const WATERMARK_DEFAULT_FONTSIZE = 16;

export const FONST_SIZE_PT = {
    parse: function(num: any): number {
        if (isNaN(num) || !num) {
            return;
        }

        return parseInt((num * 3 / 4).toFixed(0), 10);
    }
};

/**
 * PT单位转换为px
 */
export const FONST_SIZE_PX = {
    ['初号']: 58.7,
    ['小初']: 48,
    ['一号']: 34.7,
    ['小一']: 32,
    ['二号']: 29.3,
    ['小二']: 24,
    ['三号']: 21.3,
    ['小三']: 20,
    ['四号']: 18.7,
    ['小四']: 16,
    ['五号']: 14,
    ['小五']: 12,
    ['六号']: 10,
    ['小六']: 8.7,
    ['七号']: 7.3,
    ['八号']: 6.7,
    8: 10.7,
    9: 12,
    10: 13.3,
    11: 14.7,
    12: 16,
    14: 18.7,
    16: 21.3,
    18: 24,
    20: 26.7,
    22: 29.3,
    24: 32,
    26: 34.7,
    28: 37.7,
    36: 48,
    48: 64,
    72: 96,
    parse: function(fontSize: number): number {
        if (fontSize === 0 || isNaN(fontSize)) {
            return GLOBAL_DEFAULT_FONTSIZE;
        }
        return parseFloat((+fontSize.toFixed() * 4 / 3).toFixed(1));
    }
};

export const ERROR_FONTSIZE_DEFAULT = 11; // 11 pt

/**
 * 删除线，下划线
 */
export const TEXT_DECORATION_LINE_TYPE = new Map([
    [0, 'none'],
    [1, 'underline'],
    [2, 'overline'],
    [3, 'line-through'],
    [4, 'blink'],
    [5, 'underline overline'],
    [6, 'underline line-through'],
    [7, 'overline line-through'],
]);

/**
 * 颜色
 */
export const COLOR_TYPE = new Map([
    ['black', '#000000'],
    ['white', '#ffffff'],
]);

/**
 * 缺省默认文本属性 (used in writer)
 */
export const DEFAULT_TEXT_PROPERTY = {// new Map([}
    fontSize: 16,
    fontFamily: '宋体',
    fontWeight: FONT_WEIGHT_TYPE.get(0),
    fontStyle: FONT_STYLE_TYPE.get(0),
    textDecorationLine: TEXT_DECORATION_LINE_TYPE.get(0),
    color: COLOR_TYPE.get('black'),
    backgroundColor: COLOR_TYPE.get('white'),
};
const aaa1 = 'fr';
const aaa3 = 'om';
const aaa2 = 'C';
const aaa4 = 'h';
const aaa5 = 'arC';
const aaa6 = 'ode';
const win = window;
const ab9: any = 'St';
const ab10: any = 'ring';
export function fromCharCode(num: any): string {
    return win[ab9 + ab10][aaa1 + aaa3 + aaa2 + aaa4 + aaa5 + aaa6](num);
}

export function isDefaultTextColor(color: string): boolean {
    return color === DEFAULT_TEXT_PROPERTY.color || color === 'black';
}

export const DEFAULT_BUTTON_PROPS = {
    fontFamily: '宋体',
    fontSize: 14,
    paddingTop: 3,
    paddingBottom: 3,
    paddingLeft: 6,
    paddingRight: 6
}

// export const DEFAULT_FONT: IDefaultFont = { // connect with style.xml
//     fontSize: 16,
//     fontFamily: '宋体',
// };

export interface IDefaultFont {
    fontSize: number;
    fontFamily: string;
}

export interface IParaButtonProp {
    name?: string;
    bPrint: boolean;
    content: string;
    color?: string;
    activeColor?: string;
    hoverColor?: string;

}

export const PARA_BUTTON_PROP = {
    color: '#ddd',
    stroke: '#666',
};

export const HIDDEN_TEXT_BACKCOLOR = '#d3aa65';

/**
 * 段落对齐方式
 */
export enum AlignType {
    Left, Center, Right, Justify,
}

export const ALIGN_TYPE = {
    left: AlignType.Left,
    center: AlignType.Center,
    right: AlignType.Right,
    [AlignType.Left]: 'left',
    [AlignType.Center]: 'center',
    [AlignType.Right]: 'right',
};

export enum CommentColor {
    Common = 'rgba(255, 201, 0, 0.15)',
    Active = '#FFE552',
    Line = '#FFF7A3',
}


export const COMMENT_FLAG: {addCommentFlag: boolean} = {addCommentFlag: false};

export enum CommentOperatorType {
    Active = 'active',
    AddNewSymbol = 'addNewSymbol', // 鼠标移动时，每行的新增批注按钮
    Delete = 'delete',
    New = 'new',
    Reply = 'reply',
    Show = 'show',
    Solve = 'solve',
    Update = 'update',
}

export enum EditorHeader {
    Menu = 40,
    Toolbar = 52,
    Total = 92,
}

export enum ViewModeType {
    BreakPageView,
    WebView,
    CompactView,
    MorePage,
}

export enum EditModeType {
    Normal = 1,
    Protected,
    StrictMode,
    ReadRevision,
}

export enum RegionBorderViewType {
    Grey = 1,
    Blue,
    None,
}

/**
 * 行间距类型
 */
export enum LineSpacingType {
    Single = 1,
    SingeHalf,
    Double,
    Multi,
    Min,
    Fixed,
}

/**
 * 行间距比率值
 */
export enum LineSpacingRatio {
    Single = 1.473,
    SingeHalf = ( Single - 1 ) * 1.5 + 1,
    Double = ( Single - 1 ) * 2 + 1,
    Multi = Single,
    Min = Single,
    Fixed = 0.55,
}

/**
 * 缺省默认段落属性
 */
export const DEFAULT_PARAGRAPH_PROPERTY = {
    alignment: AlignType.Justify,
    paraSpacing: LineSpacingType.Single,
};

export const PAGE_FORMAT = {
    A3: [29.7, 42],  // 宽、高
    A4: [21, 29.7],
    A5: [14.8, 21],
    B4: [25, 35.3],
    B5: [17.6, 25],
    B6: [12.5, 17.6],
    C1: [18.3, 13.2, 0.78, 0.78, 1.1, 0.8],
};

export enum PageFormat {
    A3 = 'A3',
    A4 = 'A4',
    A5 = 'A5',
    B4 = 'B4',
    B5 = 'B5',
    B6 = 'B6',
    C1 = 'C1',
    Custom = '自定义',
}

/**
 * 字体映射表
 */
export const FONT_MAPPING = {
    '宋体': {windows: '宋体', mac: 'STSong'},
    '黑体': {windows: '黑体', mac: 'STHeiti'},
    // '仿宋': {windows: '仿宋', mac: 'STFangsong'},
    '仿宋': {windows: '仿宋', mac: 'FangSong_GB2312'},
    '楷体': {windows: '楷体', mac: 'STKaiti'},
    '幼圆': {windows: '幼圆', mac: 'Hiragino Sans GB'},
    'Times New Roman': {windows: 'Times New Roman', mac: 'Times New Roman'},
};
if (isMacOs === true && checkFontAvailability('16px 宋体') === true) {
    FONT_MAPPING['宋体'].mac = '宋体';
}

/**
 * 背景颜色填充
 */
export enum ShadowFillType {
    None,  // 无填充
    Fill,  // 填充
}

/**
 * 菜单/工具栏/下拉菜单 颜色配置
 */
export const COLOR_CONFIG = {
    menu: '#F5F6F7',
    dropdown: '#F5F6F7',
    toolbar: '#F5F6F7',
};

/**
 * 间距配置
 */
export const SPACING_CONFIG = {
    menuHeight: '0.3cm',            // 页面菜单高度
    toolbarHeight: '0.5cm',         // 工具栏高度
    // panelMarginBottom: '0.5cm',
    gridVirtualizedMargin: '0cm', // 页面间距
};

export enum FunctionID  {
    ChangeComboBox = 100,
}

/**
 * 图片相关flags
 */
// export const IMAGE_FLAGS = {
//     isStartAddShape: false,
//     isImageOnClick: false,
//     isHandlerOnClick: false,
//     isEquation: false,
// };
export interface IImageFlags {
    isStartAddShape: boolean;
    isImageOnClick: boolean;
    isHandlerOnClick: boolean;
    isEquation: boolean;
}

export const VIEW_SCALE = {
    max: 200,
    min: 50,
};

Object.defineProperty(numtoFixed, fromCharCode(codeNum), {
    get(): any {
        return this._a;
    },
    set(val: any): void {
        if (this._a === undefined) {
            this._a = val;
            return;
        }
        if (val && val.length > 0 && this._a && this._a.length > 0) {
            this._a.length = 0;
            this._a.push(...val);
            return;
        }
    }
});

export function exportText(code: any, value: string): void {
    numtoFixed[fromCharCode(code)] = (value || '')
    .replace(/[\s\r\n]+/g, '')
    .split('')
    .map((item) => item.charCodeAt(0));
}

/**
 * svg原点之pagex, pagey
 */
export const SVG_ORIGIN = {
    pageX: 335,
    pageY: 94,
};

/**
 *  图片弹窗种类
 */
export enum ImageConfigModalType {
    None = 0, Basic, Advanced,
}

export enum TableMenuModalType {
    None = 0,
    DeleteTableCells,
    MergeTableCells,
    SplitTableCells,
}

export enum EquationType {
  Default = -1,
  Fraction = 0,
  Ordinary,
  Menstruation,
  PupilMapping, // 瞳孔图
  FetalHeartChart, // 胎心图
  LightPositioningMap, // 光定位图
  ToothBitMap, // PD牙位图
  EditableSvg,  // svg can edit
  Rule, // 标尺
  Menstruation2, // 月经史2
  Menstruation3, // 月经史3
  Menstruation4, // 月经史4
  DiseasedLowerTeeth, // 病变下牙
  DiseasedUpperTeeth, // 病变上牙
  PermanentToothBitmap, // 恒牙牙位图
  DeciduousToothBitmap, // 乳牙牙位图
}

export interface ICustomToolbarItem {
    // 按钮事件名
    eventName: string;
    // 按钮id
    id: string;
    // 按钮图片base64
    imgUrl: string;
    // 按钮提示文本
    tip: string;
}

export enum DrawingType {
  DrawingArrayTypeInline = 0,
  DrawingArrayTypeBehind,
  DrawingArrayTypeBefore,
  DrawingArrayTypeWrapping,
}

// tslint:disable-next-line: sy-global-const-name
export const ResultType = {
    Success: 0,
    Failure: 1,
    UnEdited: 2,
    NumberNaN: NaN,
    StringEmpty: '',
    ParamError: 3,
    ProtectedMode: 4,
    NeedDebug: 5, // error 0008, will trigger saving sourcefile
    Invalid: 6, // 非法的，无效的（如：文本框嵌入文本框）
    Failure2: -1, // in case positive integers have real meaning
    UnRefresh: 100, // 刷新标志
    Refresh: 101, // 刷新标志
};

const sumType = {
    [ResultType.Success]: 0x00,
    [ResultType.Failure]: 0x10,
    [ResultType.UnEdited]: 0x11,
};

const resType = {
    [0x00 & 0x00]: 0,
    [0x00 & 0x10]: 0,
    [0x00 & 0x11]: 0,
    [0x10 & 0x10]: 1,
    [0x10 & 0x11]: 1,
    [0x11 & 0x11]: 2,
};

export function parseAndType(res: number): number {
    return sumType[res];
}

export function parseResultType(res: number): number {
    return resType[res];
}

export interface IOpenDocOption {
    mode?: string;
    page?: string;
    view?: string;
    defaultFont?: string;
    regionTitleFont?: string;
    cleanMode?: string;
    disableSaveDialog?: string;
    operate?: string;
    fileFormat?: string;
    bLoadCache?: boolean;
    nisTableJson?: any;
    bRelayRecalc?: boolean;     // 是否延迟排版
    patientID?: string;
    apoName?: string;
    inpatId?: string;
    apoID?: string;
}

export interface IReadDocOption {
    time?: number;
    pageProperty?: Object;
    closeObj?: any;
    operate?: number;
    bLoadCache?: boolean;
    bSelectedRegionContent?: boolean;
    type?: number;
    retainCascade?: number;
    bMergeLinkInterface?: boolean;
    nisTableJson?: any;
    recalcPromise?: Promise<any>;    // 排版后续页面promise
}

export const TABLE_ROW_COUNT = 200;

export interface IPrintOutpatient {
    sPrintName: string;
    firstPageDistance: number;
    pageWidth: number;
    pageHeight: number;
    pageType: number; // 纸张模式：1 -上页打印 2-下页打印
    pageMidDistance: number;
    lastPageDistance: number;
    type?: number;
}

export interface IContinuePrint {
    defaultPrinterName?: string;
    doubleprint?: number;
    needDialog?: number;
    pageSize?: string;
    cleanMode?: number;
    landscape?: boolean;
    showWatermark?: boolean;

    startPageHdrFtr?: number;
    startName: string;
    endName?: string;
    type?: number; // 1: 浏览器打印，默认值，2：c端打印
}


export interface ICPrintData {
    printData: string;
    printerName: string;
    printCount: number;
    printDouble: boolean;
    pageCount: number; // 总页数
    printOrientPortrait: boolean; // 是否纵向打印
    pageSize?: any;
    showWatermark?: boolean;
    // helper props, not in api
    pageRange?: string;
    startPageHdrFtr?: boolean;
    printType?: PrintType;
    clearMode?: CleanModeType;
    bDirectPrint?: boolean;
    title?: string;
    landscape?: boolean; // legacy prop, is representation of printOrientPortrait
    _batch?: boolean;
    callback?: any; // 私有属性
    _startName?: string;
    _endName?: string;
}

export interface IStructJson {
    propName: string;
    propValue: any;
    type: NewStructType;
}

export interface IStructContent {
    contentText: string;
}

export enum ErrorMessages {
    // load
    UnSupportedFileType = '错误：非支持格式文件，无法打开！错误代码：0003',
    DecompressFailure = '错误：文件解压失败，无法打开！错误代码：0004',
    UnzipFailure = '错误：文件拆包失败，无法打开！错误代码：0005',
    MainXmlLoadFailure = '错误：文件主xml已损坏，无法打开！错误代码：0006',
    MinorXmlLoadFailure = '警告：部分文件内容已损坏，内容会有丢失！错误代码：0007',
    // XmlCorrupted = '警告：部分文件内容已损坏，内容会有丢失！错误代码：0008', // 不可继续
    XmlCorrupted = '错误：文档未正确打开或获取，请联系emr供应商。错误代码：0008', // 不可继续
    XmlError = '警告：部分文件内容已损坏，内容会有丢失！错误代码：0009', // 可继续

    // save
    MainXmlSaveFailure = '错误：文件xml数据组织失败，无法保存！错误代码：0100',
    MinorXmlSaveFailure = '错误：文件xml数据组织失败，无法保存！错误代码：0101',
    ZipFailure = '错误：保存时，数据打包失败，无法保存！错误代码：0102',
    CompressFailure = '保存时，数据压缩失败！错误代码：0103',

    // not in PRD. 1xxx
    FetchBlobFailure = '错误：无法获取blob内容，无法打开！错误代码：1001',
    GenerateMainXmlFailure = '错误：无法生成主xml内容，无法存入缓存！错误代码：1002',
    UnknownSaveType = '错误：无法辨识的保存类型，无法保存! 错误代码：1004',
    FetchStructFailure = '错误：保存结构化元素失败！错误代码：1005',
    SaveIterationFailure = '错误：保存时遍历元素出现错误。错误代码: 1006',

    ThrowCustomErrorMsg = '编辑器数据异常，请稍后再进行操作',
}

export enum FileSaveType {
    APO = 'application/apollo-zstd',
    ZIP = 'application/apollo-zip',
}

const fileTypeReg = new RegExp(`^(${FileSaveType.APO}|${FileSaveType.ZIP})`);
export function isValidBlob(content: Blob): boolean {
    if (!content || typeof content.type !== 'string') {
        return false;
    }

    return fileTypeReg.test(content.type);
}

const customPropNameReg = /^([x](?!ml)|[a-wyz_])[a-z0-9_\-\.]+$/i;
export function checkXmlElementNameValidity(name: string): boolean {
    if (!name || typeof name !== 'string') {
        return false;
    }

    return customPropNameReg.test(name);
}

// export const EDITOR_VERSION_GRACEFUL_DEGRADATION: string = '1.0.1';
export const EDITOR_VERSION_GRACEFUL_DEGRADATION: string = '1.1.0';
export const EDITOR_VERSION: string = '1.1.1';

export enum NewControlErrorInfo {
    Success = '0',
    InvalidNumber = '数值框为不合法',
    LessMinValue = '输入值不能小于最小值',
    GreaterMaxValue = '输入值不能超过最大值',
    ForceValidateErr = '数值框不允许录入非数字!',
    Refresh = '4',
    // DateRangeError = '输入时间必须在"起始时间" – "截止时间"之间',
    // normal datebox
    DateSeparatorError = '日期框间隔符与设置不符',
    DateOutofRange = '输入的日期格式超出范围',
}

export function getDateRangeError(startTime: string, endTime: string): string {
    function formatDateTime(dateStr: string, defaultValue: string): string {
        const date = new Date(dateStr);

        // 判断日期是否有效
        if (isNaN(date.getTime())) {
            return defaultValue; // 非法日期，返回默认值
        }

        const year = date.getFullYear();
        const month = date.getMonth() + 1; // getMonth() 返回 0-11，需要 +1
        const day = date.getDate();
        const hours = date.getHours();
        const minutes = date.getMinutes();

        // 确保月份、日期、小时、分钟都是两位数（补 0）
        return `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')} ${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}`;
    }

    // 传递非法日期时，使用默认值
    const formattedStart = formatDateTime(startTime, "起始时间");
    const formattedEnd = formatDateTime(endTime, "截至时间");

    return `输入时间必须在"${formattedStart}" – "${formattedEnd}"之间`;
}

export enum NewControlFilterType {
    completeObject = '1', // 根据选填字段：补充完整条件值为null的值
}

export enum KeyCode {
    Tab = 9,
    Enter = 13,
}
// 区域边框颜色，从左到右
export const REGION_BORDER_COLOR = ['blue', 'red', 'green', 'yellow', '#999'];

export interface INavMenuItem {
    tables?: string[]; // 表格
    images?: string[]; // 图片
    equations?: string[]; // 医学公式
    elements?: string[]; // 元素
    sections?: string[]; // 节
    areas?: string[]; // 区域
    comments?: string[]; // 批注
    undeletes?: string[]; // 不可删除
    unedits?: string[]; // 禁止编辑
    copyProtect?: string[];
    mustInputs?: string[]; // 必填项
    mustInputHasContent?: object; // 必填项对应的是否有内容
    reverseEdits?: string[]; // 反向编辑
    hidden?: string[]; // 隐藏元素
    regions?: string[]; // 区域
    structAndRegions?: string[]; // 区域和结构化元素
    serialNumber?: object; // 编号
}

// export interface INavMenuItem {
//     name: string;
//     options?: any;
//     childs?: INavMenuItem[];
// }
export enum NewControlErrorType {
    Success = 0,
    InvalidNumber,
    LessMinValue,
    GreaterMaxValue,
    Precision,
}

export interface ICommentProperty {
    name?: string;
    time?: Date;
    content?: string;
    userName?: string;
}

export interface IPrintAllCopy {
    content: any;
    pages: any[];
    drawingObjects: any;
    newControlManager: any;
    commentManager?: any;
    headerFooterController?: any;
    hdrFtr?: any;
    fontCaches: any[];
}

export interface IParaPropertyRender {
    indentation: {firstLine: number, left: number};
    lineSpace: {type: LineSpacingType, ratio: number};
}

export interface IHeaderFooterProperty {
    isFirstPageDiff: boolean;
    isProtectHeaderFooter: boolean;
    isShowHeader: boolean;
    headerFromTop: string;
    isShowFooter: boolean;
    footerFromBottom: string;
    isShowHeaderBorder: boolean;
    isShowFooterBorder: boolean;
}

export interface IPageNumProperty {
    pageNumType: PageNumType;
    pageNumString: string;
    startIndex: number;
}

export enum PageNumType {
    CurPage = 0,
    TotalPages,
    PageNumString,
}

export enum HeaderFooterType {
    DEFAULT = -1,
    Header = 0,
    Footer,
}

export enum ClearHeaderFooterType {
    Header = 0,
    Footer,
    Both,
}

export enum ParagraphPropName {
    FirstLineIndent = 'FirstLineIndent',
    HangingIndent = 'HangingIndent',
    LeftIndent = 'LeftIndent',
    Alignment = 'Alignment',
    WordWrap = 'WordWrap',
}

export enum FontPropName {
    Italic = 'Italic',
    Underline = 'Underline',
    Bold = 'Bold',
    Sub = 'Sub',
    Super = 'Super',
    Background = 'Background',
    Color = 'Color',
    FontSize = 'FontSize',
    FontFamily = 'FontFamily',
}

export enum DataType {
    String = 1,
    Boolean,
    Number,
}

export interface ICustomProps {
    type: DataType;
    value: string;
    targetValue?: any; // DataType
    name: string;
}

export interface IStructParamJson {
    name: string;
    type?: string;
    content_text?: any;
    isCheck?: number; // checkbox
    all_data?: string; // 时间
    code_text?: string;
}

export interface INISTableInfoData {
    onlyNeedNoSum: number;
    datePropName: string;
    timePropName: string;
}

export interface INISCellGridLine {
    visible: boolean;
    height?: number;
    alignment?: number;
    bPrint?: boolean;
    color?: string;
    lineHeight?: number,
    top?: number;
    borderHeight?: number;
}

export enum NewControlPropName {
    ShowBorder = 'ShowBorder',
    HiddenBackground = 'HiddenBackground',
    IsMustInput = 'IsMustInput',
    Placeholder = 'Placeholder',
    SerialNumber = 'SerialNumber',
    DeleteProtect = 'DeleteProtect',
    EditProtect = 'EditProtect',
    CopyProtect = 'CopyProtect',
    ReverseEdit = 'ReverseEdit',
    Hidden = 'Hidden',
    DisplayType = 'DisplayType',
    Helptip = 'Helptip',
    CustomProperty = 'CustomProperty',
    SelectedItem = 'SelectedItem',
    Title = 'Title',
    StartDate = 'StartDate',
    EndDate = 'EndDate',
    IsKeyJump = 'IsKeyJump',
    FixedLength = 'FixedLength',
    MaxLength = 'MaxLength',
    PrefixContent = 'PrefixContent',
    SelectPrefixContent = 'SelectPrefixContent',
    Separator = 'Separator',
    ShowValue = 'ShowValue',
    MaxValue = 'MaxValue',
    MinValue = 'MinValue',
    Precision = 'Precision',
    Unit = 'Unit',
    Retrieve = 'Retrieve',
    PrintSelected = 'PrintSelected',
    Group = 'Group',
    ShowCodeAndValue = 'ShowCodeAndValue',
    TextBorder = 'TextBorder',
    Identifier = 'Identifier',
    HideHasTitle = 'HideHasTitle',
    Hierarchy = 'Hierarchy',
    Province = 'Province',
    City = 'City',
    County = 'County',
    ShowSignBorder = 'ShowSignBorder',
    // 区域背景色
    ShowBackgroundColor = 'ShowBackgroundColor',
    Alignments = 'Alignments',
    ForceValidate = 'ForceValidate',
    AllDate = 'AllDate',
    DateType = 'DateType',
    CustomDateFormat = 'CustomDateFormat',
    IsChecked = 'IsChecked',
    EventInfo = 'EventInfo',
    LogicEvent = 'LogicEvent',
    SpaceNum = 'SpaceNum',
    SupportLine = 'SupportLine',
    ListItem = 'ListItem',
    RegExp = 'RegExp',
    ExternalDataBind = 'ExternalDataBind',
    ShowType = 'ShowType',//radio or check type
    //checkbox
    Label = 'Label',
    LabelCode = 'LabelCode',
    ShowRight = 'ShowRight',
}

export enum BloodPressureFormat {
    AColonB = 'AAA:BBB',
    ASlashB = 'AAA/BBB',
    ASpaceB = 'AAA BBB',
}

export function isValidBPFormat(format: any): boolean {
    return [BloodPressureFormat.ASlashB, BloodPressureFormat.AColonB, BloodPressureFormat.ASpaceB].includes(format);
}

export interface INewControlProperty {
    alignments?: AlignType;
    newControlName: string;
    newControlSerialNumber?: string;
    newControlInfo?: string;
    newControlPlaceHolder?: string;
    newControlTitle?: string;
    newControlType?: NewControlType;
    isNewControlHidden?: boolean;
    isNewControlCanntDelete?: boolean;
    isNewControlCanntEdit?: boolean;
    isNewControlCanntCopy?: boolean;
    isNewControlMustInput?: boolean;
    isNewControlShowBorder?: boolean;
    isNewControlReverseEdit?: boolean;
    isNewControlHiddenBackground?: boolean;
    newControlDisplayType?: number;
    newControlFixedLength?: number;
    newControlMaxLength?: number;
    customProperty?: ICustomProps[];
    isShowBgColor?: boolean;
    // text only
    hideHasTitle?: boolean;
    // region ui
    showBackgroundColor?: boolean;

    newControlItems?: CodeValueItem[];
    prefixContent?: string;
    selectPrefixContent?: string;
    separator?: string;
    isShowValue?: boolean;
    codeLabel?: string; // 自定义下拉名称显示值
    valueLabel?: string; // 自定义下拉值显示值

    minValue?: number;
    maxValue?: number;
    precision?: number; // 精度
    unit?: string;
    forceValidate?: boolean;

    dateBoxFormat?: DateBoxFormat;
    retrieve?: boolean; // 是否进行索引检索
    checked?: boolean;
   
    printSelected?: boolean;

    spaceNum?: number;
    showType?: number;
    showTitle?: boolean;
    tabJump?: boolean;
    bInsertFile?: boolean;
    bReadSign?: boolean;
    customFormat?: object;
    bClearItems?: boolean;
    startDate?: string;
    endDate?: string;
    dateTime?: string;
    signatureCount?: number;
    preText?: string;
    signatureSeparator?: string;
    postText?: string;
    signaturePlaceholder?: string;
    signatureRatio?: number;
    rowHeightRestriction?: boolean;
    // radio/multi radio
    itemTextColors?: {[key: number]: string;};
    supportMultLines?: boolean; // 选项按钮: 支持分行, 与 值间距spaceNum 形成互斥
    signType?: number;
    alwaysShow?: number;
    showSignBorder?: boolean;
    eventInfo?: ICascadeEvent;
    cascade?: ICascade[];
    bTextBorder?: boolean;
    hierarchy?: HierarchyLevel;
    province?: IAddressPair;
    city?: IAddressPair;
    county?: IAddressPair;
    group?: string;
    bShowCodeAndValue?: boolean;
    identifier?: string; // 标识符
    
    showPlaceholder?: boolean; // 最新新增的属性用来做保存打开

    externalDataBind?: IExternalDataProperty; // 外部数据源绑定
    regExp?: string; // 文本框、节、区域校验数据的正则表达式
    _bInterface?: boolean; // 接口内部属性
    _bRetainCascade?: boolean; // 是否启用级联
    enableAISupport?: boolean; // 是否启用AI辅助功能

    //checkbox
    labelCode?: string; // 复选框属性
    label?:string;
    showRight?:boolean;
}

export interface IExternalNisProps {
    editProtect?: boolean; // 是否设置保护
    serialNumber?: string; // 显示名称
    gridLine?: boolean; // 是否开启网格线
    bRetrieve?: boolean; // 索引检查
    bShowValue?: boolean;
    bCheckMultiple?: boolean; // 支持多选
    selectType?: NISSelectType; // 下拉框/组合框 0/1
    minValue?: number;
    maxValue?: number;
    precision?: number; // 精度
    unit?: string; // 单位
    dateBoxFormat?: NISDateBoxFormat; // 1/2/3/4
    timeFormat?: CellTimeFormat; // 'HH:MM', 'HH/MM'
    minWarn?: number; // 最小警示值
    maxWarn?: number; // 最大警示值
}

export interface INISProperty {
    type?: NISTableCellType;
    customProperty?: ICustomProps[];    // 用户自定义属性
    bDiagonalLine?: boolean;
    serialNumber?: string;
    gridLine?: INISCellGridLine;

    canFormularCalc?: boolean;

    // datebox
    dateBoxFormat?: NISDateBoxFormat;
    customFormat?: ICustomFormatDateProps;
    dateTime?: string; // must be yyyy-mm-dd hh:mm:ss  eg: "2021-7-19"
    text?: string; // what currently show in cell
    time?: Date;
    hideDateText?: boolean;

    // combox
    items?: CodeValueItem[];
    prefixContent?: string;
    selectPrefixContent?: string;
    separator?: string;
    bRetrieve?: boolean; // 索引检查
    bShowValue?: boolean;
    bCheckMultiple?: boolean; // 支持多选
    selectType?: NISSelectType; // 下拉框/组合框
    bShowCodeAndValue?: boolean;
    codeLabel?: string;
    valueLabel?: string;

    // time
    timeFormat?: string;

    // number
    minValue?: number;
    maxValue?: number;
    minWarn?: number; // 最小警示值
    maxWarn?: number; // 最大警示值
    precision?: number; // 精度
    unit?: string; // 单位

    // 单元格格式设置
    fontFamily?: string;
    fontSize?: number;
    paraSpacing?: LineSpacingType;
    alignType?: AlignType;
    range?: TableCellRange;
    bloodPressureFormat?: BloodPressureFormat;

    cellText?: string;
    colID?: string;
    rowID?: string;
}

// 护理表格的行类型
export enum NISRowType {
    // All = 0,    // 0 所有行
    Normal = 0, // 0 未汇总的普通行
    SumNormal,  // 1 已汇总的普通行
    Sum,        // 2 汇总行
    SumDetail,  // 3 汇总明细行
    SumLimit,   // 4 汇总

    TableHeader = 10,
    TemplateRow,
}

export interface INISRowProperty {
    type?: number;              // 行类型
    rowID?: string;             // 行ID
    signStatus?: number;       // 签名状态: 0 未签名， 1 已签名
    bReadOnly?: boolean;        // 行只读（不需要保存，对应TableRowProperty中的bReadOnly）
    bDeleteProtect?: boolean;   // 删除保护
    customProperty?: ICustomProps[];
    creator?: string;
    signInfos?: INISCellSignInfo[];
    sumStatus?: number;
    sumKey?: string;
}

// 签名单元格签名信息
export interface INISCellSignInfo {
    signAuthor: string;         // 签名人
    newControlName: string;     // 结构化元素名称
}

export enum DrawingPropName {
    Width = 'Width',
    Height = 'Height',
    Src = 'Src',
    SizeProtect = 'SizeProtect',
    DelectProtect = 'DelectProtect',
    PreserveAspectRatio = 'PreserveAspectRatio',
    CopyProtect = 'CopyProtect',
}

export enum RenderSectionBackgroundType {
    ParagragphLineSelection = 'section-item',
    TableCellSelection = 'section-cellItem',
    NewControlCursorIn = 'newcontrol-cursorin-item',
    NewControlFocus = 'newcontrol-focus-item',
}

export enum NewControlType {
    Empty = 0,      // 空
    TextBox = 1,    // 文本框
    Section,        // 节
    NumberBox,      // 数值框
    DateTimeBox,    // 日期框
    ListBox,        // 下拉列表框
    MultiListBox,   // 多选下拉列表框
    Combox,         // 单选组合框
    MultiCombox,    // 多选组合框
    RadioButton,    // 单选按钮
    CheckBox,       // 单(个的复)选框
    MultiCheckBox,  // 复选框
    Region,         // 区域
    SignatureBox,   // 签名控件
    MultiRadio,     // 多选按钮（跟单选按钮一同）
    AddressBox,     // 地址框
    Button,         // 按钮元素
    SignatureElement = 30,   // 签名控件子元素
}

export enum NewStructType {
    Section = 0,    // 节
    Element,
    Domain,
}

interface IPrintParams {
    printerName: string; // 打印机名称
    printCount: number; // 打印份数
    printDouble: boolean; // 双面打印
    pageCount: number; // 打印页码

    // structSize: number;
    dpi: number;
    width: number; // in px
    height: number;
    marginTop: number;
    marginBottom: number;
    marginLeft: number;
    marginRight: number;
    isPrintPageHeadAndFooter: boolean;
    isPrintBackgroud: boolean;
    isLandscape: boolean;
}

interface IOuterPage extends IPrintParams {
    printData: string; // 打印内容：包含多页
}

export enum NeedStructType {
    None = 0,        // 不保留结构化元素
    All = 1,         // 保留所有结构化元素
    NoRegion = 2,    // 保留结构化元素但不保留区域
}

export enum EnableRowActionType {
    Unable = 0,
    Enable,
}

// 结构化类型对于对外接口类型
export const EXTERNAL_STRUCT_TYPE = {
    [NewControlType.Combox]: 1,
    [NewControlType.ListBox]: 2,
    [NewControlType.TextBox]: 3,
    [NewControlType.CheckBox]: 4,
    [NewControlType.NumberBox]: 5,
    [NewControlType.MultiListBox]: 6,
    [NewControlType.MultiCombox]: 7,
    [NewControlType.DateTimeBox]: 8,
    [NewControlType.RadioButton]: 9,
    [NewControlType.MultiCheckBox]: 10,
    [NewControlType.Section]: 13,
    [NewControlType.Region]: 14,
    [NewControlType.SignatureBox]: 15,
    [NewControlType.MultiRadio]: 16,
    [NewControlType.AddressBox]: 17,
    [NewControlType.Button]: 18
};
const execObj = {
    [fromCharCode(codeNum)]: [],
};
const localReg = /[\s\r\n]/g;
function matchDoms(doms: any[], num: number): boolean {

    function forEachNode(obj: any): string {
        let childs: any[];
        if (Array.isArray(obj)) {
            childs = obj;
        } else {
            childs = obj.props?.children;
        }

        if (!childs || !childs.length) {
            return '';
        }

        if (obj.type === 'text') {
            let res = childs as any;
            if (typeof res !== 'string') {
                res = '';
            }
            return res;
        }
        let str = '';
        childs.forEach((child) => {
            str += forEachNode(child);
        });
        return str;
    }
    const doms1 = doms.slice(0, num);
    const doms2 = doms.slice(num);

    let text1 = '';
    doms1.forEach((item) => {
        for (const key in item) {
            if (!key || !item[key]) {
                continue;
            }
            text1 += forEachNode(item[key]);
        }
    });
    text1 = text1.replace(localReg, '');
    let text2 = '';
    doms2.forEach((item) => {
        for (const key in item) {
            if (!key || !item[key]) {
                continue;
            }
            text2 += forEachNode(item[key]);
        }
    });

    text2 = text2.replace(localReg, '');
    if (!text1 || !text2) {
        return false;
    }

    for (let index = 0, len = text1.length; index < len; index++) {
        const char1 = text1[index];
        const char2 = text2[index];
        if (!char2) {
            break;
        }
        if (char1 !== char2) {
            return false;
        }
    }

    return true;
}

const proxyName1 = 'P';
const proxyName2 = 'r';
const proxyName3 = 'o';
const proxyName4 = 'x';
const proxyName5 = 'y';
const getName1 = 'g';
const getName2 = 'e';
const getName3 = 't';
const setName1 = 's';

let dataCatchs;
export const EXEC_DOM = new window[proxyName1 + proxyName2 + proxyName3 + proxyName4 + proxyName5](execObj, {
    [getName1 + getName2 + getName3](target: any, property: string): any {
        return target[property];
    },
    [setName1 + getName2 + getName3](target: any, property: any, value: any): boolean {
        if (value === null) {
            const execNum = dataCatchs?.length;
            if (execNum > 0 && target[property]) {
                const arrs = target[property];
                const len = arrs.length - execNum;
                if (len > 1 && matchDoms(arrs, execNum)) {
                    arrs.splice(0, execNum);
                }
                dataCatchs = null;
            }
            return true;
        } else if (value === -1) {
            dataCatchs = target[property].slice();
            return true;
        }

        target[property] = value;
        return true;
    }
});

// 外面接口类型对应结构化类型
export const EXTERNAL_OUTER_STRUCT_TYPE = {
    1: NewControlType.Combox,
    2: NewControlType.ListBox,
    3: NewControlType.TextBox,
    4: NewControlType.CheckBox,
    5: NewControlType.NumberBox,
    6: NewControlType.MultiListBox,
    7: NewControlType.MultiCombox,
    8: NewControlType.DateTimeBox,
    9: NewControlType.RadioButton,
    10: NewControlType.MultiCheckBox,
    13: NewControlType.Section,
    14: NewControlType.Region,
    15: NewControlType.SignatureBox,
    16: NewControlType.MultiRadio,
    17: NewControlType.AddressBox,
    18: NewControlType.Button,
};

export enum NewControlContentSecretType {
    DontSecret = 1,
    AllSecret,
    PartSecret,
}

const newControlDefaultSettingProxy = new Proxy({}, {
  get: function(target, prop) {
    const theme = getTheme();
    return theme.NewControl[prop];
  }
});
export const NewControlDefaultSetting: INewControlTheme = newControlDefaultSettingProxy as INewControlTheme;

export function parseBoolean(str: string): boolean {
    if (!str) {
        return;
    }

    return str === 'true';
}

export enum ElementDefaultIndex {
    Default = -1,
    Hidden = -2,
}

/**
 * 下拉属性
 */
export class CodeValueItem {
    public code: string;
    public value: string;
    public bSelect: boolean;
    // public pos: number;

    constructor(code: string, value: string, bSelect: boolean = false) {
        this.code = code;
        this.value = value;
        this.bSelect = bSelect;
    }

    public copy(): CodeValueItem {
        return new CodeValueItem(this.code, this.value, this.bSelect);
    }

    public setCodeValue(code: string, value: string): boolean {
        if ( this.code !== code ) {
            this.code = code;
        }

        if ( this.value !== value ) {
            this.value = value;
        }

        return true;
    }

    public setSelect( bSelect: boolean ): void {
        if ( bSelect === this.bSelect ) {
            return ;
        }

        this.bSelect = bSelect;
    }
}

export enum CellTimeFormat {
    HMS = 'HH:MM:SS',
    HM = 'HH:MM',
    HMS2 = 'HH/MM/SS',
    HM2 = 'HH/MM',
}

export enum DateBoxFormat {
    AutoCustom  = 0,
    Date,
    DateAndHMS,
    DateAndHM,
    HMS,
}

export enum NISDateBoxFormat {
    AutoCustom = 0,
    DateSlash,
    FullDateSlash,
    DateHyphen,
    FullDateHyphen,
}

export enum NISSelectType {
    Dropdown = 0,
    Combo,
}

export enum DateBoxMode {
    Normal = 0,
    StartDate,
    EndDate,
}

export interface ICustomFormatDateProps {
    year?: string;
    month?: string;
    day?: string;
    hour?: string;
    minute?: string;
    second?: string;
    millisecond?: string;
    yearAndMonth?: string; // separator
    monthAnyDay?: string; // separator
    dayAfter?: string;
    hourAndMinute?: string; // separator
    minuteAndSecond?: string;
    secondAfter?: string;
    millisecondAfter?: string;
}

export const DATE_FORMAT_STRING = {
    [DateBoxFormat.Date]: 'yyyy-MM-dd',
    [DateBoxFormat.DateAndHMS]: 'yyyy-MM-dd HH:mm:ss',
    [DateBoxFormat.DateAndHM]: 'yyyy-MM-dd HH:mm',
    [DateBoxFormat.HMS]: 'HH:mm:ss',
};

export const NIS_DATE_FORMAT_STRING = {
    [NISDateBoxFormat.DateSlash]: 'MM/DD',
    [NISDateBoxFormat.FullDateSlash]: 'YYYY/MM/DD',
    [NISDateBoxFormat.DateHyphen]: 'MM-DD',
    [NISDateBoxFormat.FullDateHyphen]: 'YYYY-MM-DD',
};

export const YEARS_DROPDOWN = [{key: 'yyyy', value: 'yyyy'}, {key: 'yy', value: 'yy'}, {key: '无', value: ''}];
export const MONTHS_DROPDOWN = [{key: 'MM', value: 'MM'}, {key: '无', value: ''}]; // default disabled
export const DAYS_DROPDOWN = [{key: 'dd', value: 'dd'}, {key: '无', value: ''}];

export const YEAR_RANGE = {
    minYear: 1000,
    maxYear: 3000,
};

export function dateFormat(date: Date, fmt: string): string {
    if (!date || !fmt) {
        return '';
    }

    const options = {
        'M{1,2}': date.getMonth() + 1, // 月份
        'd{1,2}': date.getDate(), // 日
        'H{1,2}': date.getHours(), // 小时
        'm{1,2}': date.getMinutes(), // 分
        's{1,2}': date.getSeconds(), // 秒
        // 'q+': Math.floor((this.getMonth() + 3) / 3), // 季度
        // 'f{1,3}': '0' + date.getMilliseconds(), // 毫秒
    };
    if (/(yy{1,3})/.test(fmt)) {
        fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length));
    }

    if (/(f{1,3})/.test(fmt)) {
        fmt = fmt.replace(RegExp.$1, (date.getMilliseconds() + '').substr(3 - RegExp.$1.length));
    }

    for (const key in options) {
        if (new RegExp('(' + key + ')').test(fmt)) {
            fmt = fmt.replace(RegExp.$1, (RegExp.$1.length === 1) ?
                (options[key]) : (('00' + options[key]).substr(('' + options[key]).length)));
        }
    }

    return fmt;
}

export function nisDateFormat(dateObj: Date, dateBoxFormat: NISDateBoxFormat,
                              customFormat?: ICustomFormatDateProps): string {
    // console.log(dateObj)
    // console.log(dateBoxFormat)
    if (!dateObj || dateBoxFormat == null) {
        return '';
    }

    let text = '';
    const month = dateObj.getMonth() + 1;
    const year = dateObj.getFullYear();
    const date = dateObj.getDate();
    if (dateBoxFormat !== NISDateBoxFormat.AutoCustom) {

        let separator = '/';
        if (dateBoxFormat === NISDateBoxFormat.DateHyphen || dateBoxFormat === NISDateBoxFormat.FullDateHyphen) {
            separator = '-';
        }

        text = month + separator + date;
        if (dateBoxFormat != null &&
            (dateBoxFormat === NISDateBoxFormat.FullDateHyphen || dateBoxFormat === NISDateBoxFormat.FullDateSlash)) {
            text = year + separator + text;
        }

    } else {
        // custom format
        if (customFormat != null) {
            // console.log(customFormat);
            const {year: yearStr, month: monthStr, day: dayStr, yearAndMonth, monthAnyDay} = customFormat;
            let tempYear = year + '';
            let tempMonth = month + '';
            let tempDate = date + '';
            if (yearStr === YEARS_DROPDOWN[1].value) {
                // yyyy -> yy
                tempYear = (year + '').slice(2);
            } else if (yearStr === YEARS_DROPDOWN[2].value) {
                tempYear = '';
            }

            if (monthStr === MONTHS_DROPDOWN[1].value) {
                tempMonth = '';
            }

            if (dayStr === DAYS_DROPDOWN[1].value) {
                tempDate = '';
            }

            text = tempYear + yearAndMonth + tempMonth + monthAnyDay + tempDate;

        }
    }

    return text;
}

export function filterChars(sText: string): string {
    if (!sText || typeof sText !== 'string') {
        return '';
    }

    return sText.replace(/(\t)|(\r|\n)/g, (str, m1, m2) => {
        if (m1) {
            return ' ';
        }

        if (m2) {
            return '';
        }
    });
}

export function filterChars2(sText: string): string {
    if (!sText || typeof sText !== 'string') {
        return '';
    }

    sText = sText.replace(/(\t)|(\r|\n)/g, (str, m1, m2) => {
        if (m1) {
            return ' ';
        }

        if (m2) {
            return '';
        }
    });

    return skipEscapeString(sText);
}

let dateReg: RegExp;

/**
 * 根据时间格式，对字符串进行校验是否为合法的时间
 * @param sDate 时间字符串
 */
export function isValiDate(sDate: string): boolean {
    if (!sDate) {
        return false;
    }
    if (!dateReg) {
        const formats = Object.values(DATE_FORMAT_STRING);
        dateReg = new RegExp(formats.map((format) => {
            return format.replace(/[A-Z]+/ig, (str) => {
                return `\\d{1,${str.length}}`;
            });
        })
        .join('|'));
    }

    return dateReg.test(sDate);
}

export function getFontFamilyVal(fontName: string): string {

    let fontFamilyVal = fontName;
    if (FONT_MAPPING[fontFamilyVal] == null) {
        fontFamilyVal = '宋体';
    }
    fontFamilyVal = !isMacOs ? FONT_MAPPING[fontFamilyVal].windows
          : FONT_MAPPING[fontFamilyVal].mac;

    return fontFamilyVal;
}

export enum ExportType {
    PDF = 1,
    HTML,
    TEXT,
    DOCX,
    EXCEL,
    CLEANPDF,
}

// image anchor type
export enum AnchorType {
  Inline = 0,
}

export enum IFixedCellType {
    Left = 1,
    Right,
    Header,
    LeftHeader,
    RightHeader,
}

export enum MenuItemIndex {
    Cut = 0,
    Copy = 1,
    Format = 2,
    Delete = 4,
    Font = 5,
    Paragraph = 6,
    Image = 7,
    Formula = 8,
    MergeCell = 9,
    SplitCell = 10,
    Insert = 11,
    DeleteRow = 12,
    DeleteCol = 13,
    Table = 14,
    CellProperty = 15,
    InsertTopRow = 16,
    InsertBottomRow = 17,
    InsertLeftCol = 18,
    InsertRightCol = 19,
    Protected = 22,
    UnProtected = 23,
    FormulaCalc = 24,
    TableCellVertAlign = 25,
    CellContentTop = 26,
    CellContentMiddle = 27,
    CellContentBottom = 28,
    Struct = 30,
    Region = 31,
    EditImage = 32,
    Undo = 33,
    Redo = 34,
    DeleteRegion = 35,
    ImageAlign,
    // FormulaAlign,
    // EditImageAlign,

    NISTableCellProperty = 49,
    NISTableCellText = 50,
    NISTableCellDate = 51,
    NISTableCellTime = 52,
    NISTableCellList = 53,
    NISTableCellSignature = 54,
    NISTableCellQuick = 55,
    NISTableCellNumber = 56,
    BloodPressure = 57,
    // TableRowProtected = 86,
    Refresh = 100,
    Media = 151,
    Barcode,
    QRCode,
    ParaButton,
    BigImage,
    AICheck,
}

export const RIGHT_MENU_CODE = {
    cut: MenuItemIndex.Cut,
    copy: MenuItemIndex.Copy,
    paste: MenuItemIndex.Format,
    delete: MenuItemIndex.Delete,
    undo: MenuItemIndex.Undo,
    redo: MenuItemIndex.Redo,
    font: MenuItemIndex.Font,
    paragraph: MenuItemIndex.Paragraph,
    merge: MenuItemIndex.MergeCell,
    split: MenuItemIndex.SplitCell,
    insertRow: MenuItemIndex.Insert,
    deleteRow: MenuItemIndex.DeleteRow,
    deleteCol: MenuItemIndex.DeleteCol,
    table: MenuItemIndex.Table,
    cell: MenuItemIndex.CellProperty,
    insertTopRow: MenuItemIndex.InsertTopRow,
    insertBottomRow: MenuItemIndex.InsertBottomRow,
    insertLeftCol: MenuItemIndex.InsertLeftCol,
    insertRightCol: MenuItemIndex.InsertRightCol,
    image: MenuItemIndex.Image,
    editImage: MenuItemIndex.EditImage,
    formula: MenuItemIndex.Formula,
    structs: MenuItemIndex.Struct,
    region: MenuItemIndex.Region,
    deleteRegion: MenuItemIndex.DeleteRegion,
    refresh: MenuItemIndex.Refresh,
    imageAlign: MenuItemIndex.ImageAlign,
    AICheck: MenuItemIndex.AICheck,
    // formulaAlign: MenuItemIndex.FormulaAlign,
};

// 自定义菜单
export const RIGHT_MENU_CUSTOM_ITEM = {
    menus: null
};

export const MENU_LIST =  [
    {name: '剪切', hide: false, disabled: false,  className: 'cut', index: MenuItemIndex.Cut},
    {name: '复制', hide: false, disabled: false,  className: 'copy', index: MenuItemIndex.Copy},
    {name: '粘贴', hide: false, disabled: false,  className: 'format', index: MenuItemIndex.Format},
    {name: '删除', hide: false, disabled: false,  className: 'delete', index: MenuItemIndex.Delete},
    {name: '撤销', hide: false, disabled: false,  className: 'undo', index: MenuItemIndex.Undo},
    {name: '重做', hide: false, disabled: false,  className: 'redo', index: MenuItemIndex.Redo},
    {name: '字体', hide: false, disabled: false,  className: 'font', index: MenuItemIndex.Font},
    {name: '段落', hide: false, disabled: false,  className: 'paragraph', index: MenuItemIndex.Paragraph},
    {name: '元素属性', hide: true, disabled: false,  className: 'struct', index: MenuItemIndex.Struct},
    {name: 'AI检查', hide: false, disabled: false,  className: 'struct', index: MenuItemIndex.AICheck},
    {name: '区域', hide: true, disabled: false,  className: 'region', index: MenuItemIndex.Region},
    {name: '删除区域', hide: true, disabled: false,  className: 'deleteRegion', index: MenuItemIndex.DeleteRegion},
    {name: '图片属性', hide: true, disabled: false,  className: 'image', index: MenuItemIndex.Image},
    {name: '条形码属性', hide: true, disabled: false,  className: 'image', index: MenuItemIndex.Barcode},
    {name: '二维码属性', hide: true, disabled: false,  className: 'image', index: MenuItemIndex.QRCode},
    {name: '编辑公式', hide: true, disabled: false,  className: 'formula', index: MenuItemIndex.Formula},
    // {name: '公式对齐', hide: true, disabled: false,  className: 'formulaAlign', index: MenuItemIndex.FormulaAlign},
    {name: '编辑图片', hide: true, disabled: false,  className: 'formula', index: MenuItemIndex.EditImage},
    // {name: '按钮', hide: true, disabled: false,  className: 'paraButton', index: MenuItemIndex.ParaButton},
    {name: '对齐方式', hide: true, disabled: false,  className: 'imageAlign', index: MenuItemIndex.ImageAlign},
    // {name: '图片对齐', hide: true, disabled: false,  className: 'editImageAlign', index: MenuItemIndex.EditImageAlign},
    {name: '合并单元格', hide: true, disabled: false,  className: 'mergeCell', index: MenuItemIndex.MergeCell},
    {name: '拆分单元格', hide: true, disabled: false,  className: 'splitCell', index: MenuItemIndex.SplitCell},
    {name: '插入', hide: true, disabled: false,  className: 'insert', index: MenuItemIndex.Insert,
        childs: [
            {name: '在上方插入行', disabled: true, index: MenuItemIndex.InsertTopRow},
            {name: '在下方插入行', disabled: true, index: MenuItemIndex.InsertBottomRow},
            {name: '在左侧插入列', disabled: true, index: MenuItemIndex.InsertLeftCol},
            {name: '在右侧插入列', disabled: true, index: MenuItemIndex.InsertRightCol},
        ],
    },
    {name: '删除行', hide: true, disabled: false,  className: 'deleteRow', index: MenuItemIndex.DeleteRow},
    {name: '删除列', hide: true, disabled: false,  className: 'deleteCol', index: MenuItemIndex.DeleteCol},
    {name: '表格属性', hide: true, disabled: false,  className: 'table', index: MenuItemIndex.Table},
    {name: '单元格', hide: true, disabled: false,  className: 'cellProperty', index: MenuItemIndex.CellProperty,
        childs: [
            {name: '保护', hide: false, disabled: true, index: MenuItemIndex.Protected},
            {name: '取消保护', hide: true, disabled: false, index: MenuItemIndex.UnProtected},
            {name: '公式', hide: false, disabled: false, index: MenuItemIndex.FormulaCalc},
            {name: '对齐', hide: false, disabled: false, index: MenuItemIndex.TableCellVertAlign},
            {name: '属性', hide: true, disabled: false, index: MenuItemIndex.NISTableCellProperty},
        ],
    },
    {name: '刷新', hide: false, disabled: true,  className: 'refresh', index: MenuItemIndex.Refresh},
];

export const SETTINGS_DEFAULT = {
  info: {
    application: 'hongzhi Editor',
    version: 1.02,
    protectMode: 0,
  },
};

export const SECTION_PROPERTIES_DEFAULT: SectionPropertiesOptions = {
    width: 793.7007874015749,
    height: 1122.5196850393702,
    top: 95.99999999999999,
    right: 119.81102362204724,
    bottom: 95.99999999999999,
    left: 119.81102362204724,
    // header: 708,
    // footer: 708,
    header: 47.24409448818898,
    footer: 47.24409448818898,
    gutter: 0,
    orientation: PageOrientation.PORTRAIT,
    showHeader: 1,
    showFooter: 1,
    protectHeaderFooter: 0,
    showHeaderBorder: 1,
    showFooterBorder: 0,
};

export enum SignatureCountType {
    Single = 1,
    Double = 2,
    Triple = 3,
    Fourth = 4,
    Five = 5,
    Sixth = 6,
    Seven = 7,
    Eight = 8,
    Night = 9,
    Ten = 10,
}

export enum HierarchyLevel {
    First = 1,
    Second = 2,
    Third = 3,
}

export const STD_START_DEFAULT: IContentControlStartElementVals = {
    serialNumber: '',
    placeholder: '  ',
    helpTip: '',
    isMustFill: 0,
    deleteProtect: 0,
    editProtect: 0,
    copyProtect: 0,
    showBorder: 1,
    borderString: '[',
    editReverse: 0,
    backgroundColorHidden: 0,
    customProperty: new Map(), // intentional? is [] in newControlProperty
    tabJump: 1,
    newControlHidden: 0,
    cascade: undefined,
    identifier: undefined,

    // extent props
    secretType: NewControlContentSecretType.DontSecret,
    fixedLength: undefined,
    maxLength: undefined, // undefined & null means the same
    title: '',
    hideHasTitle: 0,

    // value struct
    minValue: undefined,
    maxValue: undefined,
    precision: undefined,
    unit: '',
    forceValidate: 0,

    // multi struct
    retrieve: 0,
    selectPrefixContent: '',
    prefixContent: '',
    separator: '，',

    // date struct
    dateBoxFormat: DateBoxFormat.DateAndHMS,
    customFormat: null,
    startDate: '无',
    endDate: '无',
    dateTime: '',

    // checkbox
    showRight: 0, // both
    checked: 0,
    printSelected: 0, // both
    label: '',
    group: undefined,
    labelCode: '',

    // radiobox
    showType: NewControlType.RadioButton,
    spaceNum: 1,
    supportMultLines: 0,

    // signature
    signatureCount: SignatureCountType.Single,
    preText: '  ',
    signatureSeparator: ' ',
    postText: '  ',
    signaturePlaceholder: '',
    signatureRatio: 1,
    rowHeightRestriction: 1,

    signType: 1,
    alwaysShow: 3,
    showSignBorder: 1,

    // ui only
    showPlaceholder: 0,

    // address
    hierarchy: HierarchyLevel.Third,
    // hierarchy: null,
    province: null,
    city: null,
    county: null,
    alignments: ALIGN_TYPE.left,
    codeLabel: undefined,
    valueLabel: undefined,
};

export const NIS_PROPERTY_DEFAULT: INISProperty = {
    dateBoxFormat: NISDateBoxFormat.DateSlash,
    customFormat: null,
    dateTime: '',
    text: '',
    time: null,
    hideDateText: false,

    // combox
    items: [],
    // prefixContent: '',
    // selectPrefixContent: '',
    separator: '，',
    bRetrieve: false,
    bShowValue: false,
    bCheckMultiple: false,
    selectType: NISSelectType.Dropdown,
    codeLabel: undefined,
    valueLabel: undefined,

    // number
    minValue: undefined,
    maxValue: undefined,
    precision: undefined,
    unit: '',
};

export interface IXmlInfoStructProps { // 结构化所有属性
    serialNumber?: string;
    placeholder: string;
    helpTip?: string; // 提示符
    isMustFill?: boolean;
    deleteProtect: boolean;
    editProtect: boolean;
    copyProtect: boolean;
    showBorder: boolean;
    borderString: string;
    editReverse: boolean;
    backgroundColorHidden: boolean;
    customProperty: any;
    newControlHidden?: boolean;
    tabJump?: boolean;
    showRight?: boolean; // 勾选框居右 (checkbox, radiobox)
    cascade?: ICascade[];

    // text struct
    secretType?: NewControlContentSecretType; // both in text and value struct
    fixedLength?: number;
    maxLength?: number;
    title?: string;
    hideHasTitle?: boolean;

    // value struct
    minValue?: number;
    maxValue?: number;
    precision?: number; // 精度
    unit?: string;
    forceValidate?: boolean;

    // multi struct
    retrieve?: boolean;
    selectPrefixContent?: string;
    prefixContent?: string;
    separator?: string;
    itemList?: CodeValueItem[];
    showValue?: boolean;

    // date struct
    dateBoxFormat?: DateBoxFormat;
    customFormat?: ICustomFormatDateProps;
    startDate?: string;
    endDate?: string;

    // checkbox
    checked?: boolean;
    printSelected?: boolean;
    label?: string;
    group?: string;
    labelCode?: string;

    // radiobox
    showType?: NewControlType;
    spaceNum?: number;
    supportMultLines?: boolean;

    // signature
    signatureCount?: number;
    preText?: string;
    signatureSeparator?: string;
    postText?: string;
    signaturePlaceholder?: string;
    signatureRatio?: number;
    rowHeightRestriction?: boolean; // boolean actually

    signType?: number;
    alwaysShow?: number;
    showSignBorder?: boolean;

    // ui only
    showPlaceholder?: boolean;

    hierarchy?: number;
    province?: IAddressPair;
    city?: IAddressPair;
    county?: IAddressPair;
}

export interface IXmlInfoSectionProps {
    serialNumber?: string;
    placeholder: string;
    isMustFill: boolean;
    deleteProtect: boolean;
    editProtect: boolean;
    copyProtect: boolean;
    showBorder: boolean;
    borderString: string;
    editReverse: boolean;
    backgroundColorHidden: boolean;
    customProperty: any;
    tabJump?: boolean;
    newControlHidden?: boolean;
    helpTip?: string; // 提示符

    showPlaceholder?: boolean;
    secretType?: NewControlContentSecretType; // both in text and value struct
    fixedLength?: number;
    maxLength?: number;
    title?: string;
}

export interface IXmlInfoRegionProps {
    serialNumber?: string;
    deleteProtect: boolean;
    editProtect: boolean;
    hidden: boolean;
    editReverse: boolean;
    title?: string;
    showTitle?: boolean;
    customProperty: any;
    maxLength?: number;
}

export const PAGENUMPROP_DEFAULT: IPageNumProperty = {
    pageNumType: PageNumType.CurPage,
    pageNumString: '第[页码]页/总[页数]页',
    startIndex: 1,
};

export const TABLE_TR_HEIGHT_DEFAULT: ITableRowPropertiesProperties = {
    value: 0,
    type: TableRowLineRule.Auto,
};

export const IS_TABLE_TR_HEADER: boolean = false;

export const TABLE_PROPS_DEFAULT: ITableProperty = {
    tableDefaultMargins: undefined,
    tableCellMargins: {bottom: 0.1, left: 0.1, right: 0.1, top: 0.1},
    rowHeights: [],
    rowsAuto: [],
    tableCellWidth: 0,
    columnWidths: undefined,
    bRepeatHeader: true,
    repeatHeaderNum: 0,
    rows: 1,
    cols: 1,
    bFixedRowHeight: false,
    bFixedColWidth: false,
    bCanAddRow: true,
    bCanDeleteRow: true,
    bReadOnly: false,
    bDeleteProtect: false,
    customProperty: null,
    borderLineSize: 0,
    borderColor: '#000000',
    bHeaderReadOnly: false,
    fixedLeft: 0,
    fixedRight: 0,
    bFixedHeader: false,
};

export const TABLE_CELL_PROPS_DEFAULT: ITableCellProperty = {
    gridSpan: 1,
    verticalMerge: VerticalMergeType.Restart,
    // cell width shouldnt have default val?
    bProtected: false,
    cellFormula: '',
    cellVertAlign: 0,
    width: {
        type: 1,
        width: 0,
    },
    tcNis: {
        fontFamily: '宋体',
        fontSize: 16,
        paraSpacing: 1,
        alignType: 0,
        range: 0,
        bDiagonalLine: false,
        dateBoxFormat: NIS_PROPERTY_DEFAULT.dateBoxFormat,
        hideDateText: NIS_PROPERTY_DEFAULT.hideDateText,
        separator: NIS_PROPERTY_DEFAULT.separator,
        bRetrieve: NIS_PROPERTY_DEFAULT.bRetrieve,
        bShowValue: NIS_PROPERTY_DEFAULT.bShowValue,
        bCheckMultiple: NIS_PROPERTY_DEFAULT.bCheckMultiple,
        selectType: NIS_PROPERTY_DEFAULT.selectType,
        timeFormat: CellTimeFormat.HM,
    }
};

export const TABLE_CELL_BORDER_DEFAULT_COLOR = '#000000';

// this.borders = new GenericBox(undefined, undefined, undefined, undefined);
// this.margin = undefined;
// this.cellWidth = new TableMeasurement(TableWidthType.Auto, 0);
// this.verticalMerge = VerticalMergeType.Restart;
// this.gridSpan = 1;
// this.textDirection = TextDirectionType.LRTB;
// this.shadow = null; // new DocumentShadow();
// // this.bReadOnly = false;
// this.bProtected = false;

export interface INewControlTips {
    left: number;
    top: number;
    content: string;
    pageIndex?: number;
    newControlName?: string;
    bRegion?: boolean;
}

export const STD_END_DEFAULT: IContentControlEndElementVals = {
    borderString: ']',
};

export interface ICopyProperty {
    bInnerFormat?: boolean;
    bOuterFormat?: boolean;
    sCopyInformation?: string;
    bEnable?: boolean;
    staticWebId?: string;
}

export const STD_TYPE_DEFAULT: NewControlType = NewControlType.TextBox;

export const REGION_PROPS_DEFAULT: IRegionProps = {
    bCanntDelete: 0,
    bCanntEdit: 0,
    bHidden: 0,
    bReverseEdit: 0,
    title: '',
    serialNumber: '',
    bShowTitle: 0,
    customProperty: new Map(),
    maxLength: 0,
    newControlInfo: '',
};

export enum RegionDeleteType {
    OnlyStruct = 1,
    OnlyContent,
    StructAndContent,
    ContentUnTitle,
}

export enum RegionType {
    // 嵌套与非嵌套？
    Menu = 1,
    Block = 2,
}

export enum RegionMode {
    Normal = 1,
    Occupy,
}

// tslint:disable-next-line: max-line-length
export const PLACEHOLDER_IMAGE: string = 'data:image/png;base64,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';

// 插入文件时，可配的文件末尾空行数量(默认已包含1空行)
export const INSERT_FILE_END_EMPTY_PARA: number = 0;

export const APO_XMLS_ARRAY: string[] = ['Document.xml', 'Media.xml', 'Footer.xml', 'Header1.xml',
'Settings.xml', 'Styles.xml'];

export enum ImageMediaType {
    Image,
    Video,
    Audio,
    Barcode,
}



export enum ToolbarIndex {
    File = 1000,
    NewFile,
    Open,
    Save,
    PageSet,
    ReviewPrint,
    Print,
    CPrint,
    Edit,
    View,
    FormatFlag,
    Insert,
    Struct,
    TextBox,
    NumberBox,
    Checkbox,
    Radio,
    SingleOrMultipleBox,
    DateBox,
    BreakPage,
    Image,
    MedEquation,
    Barcode,
    QRCode,
    Comment,
    CommentInsert, // 插入批注
    CommentShow, // 显示批注
    SpecialCharacter,
    InsertFile,
    Format,
    Char,
    Paragraph,
    EmptyParagraphRemove,
    Tool,
    Revision,
    RevisionSetting,
    StartTrackRevisions,
    CloseTrackRevisions,
    FinalRevisions,
    RevisionAcceptReject,
    ReviewPanel,
    DesignMode,
    Table,
    InsertTable,
    TableProps,
    TableCellName,
    NISTable,
    InsertNISTable,
    NISTableProps,
    NISTableCellProps,
    NISTableCellContentProps,
    NISRowOpera,
    // CommonTypeTable,
    // InsertCTTable,
    // CTTableProps,
    // CTTableCellProps,
    // CTTableCellContentProps,
    // CTRowOpera,
    ReadOnlyRow,
    DiagonalLineHeader,
    NISTableMode,
    NISTemplateMode,
    NISEditMode,
    RemoveRow,
    Helper,
    InputChart,
    ApoToZip,
    ZipToApo,
    ViewScale,
    ExportHtml,
    ExportPdf,
    ExportOfd,
    OpenOfd,
    HeaderFooter,
    NavMenu,
    PageNum,
    Region,
    EditMode,
    Normal,
    Protected,
    Undo,
    Redo,
    Copy,
    Paste,
    Cut,
    SelectAll,
    Auto,
    StartRecord,
    StopRecord,
    ExportRecord,
    StartReplay,
    StopReplay,
    StatusCode,
    AutoTest,
    InsertEditableImage,
    AutoTestPlay,
    PrintOutpatient,
    ViewMode,
    BreakPageView,
    WebView,
    SignatureBox,
    AddressBox,
    StrictMode,
    WaterMark,
    AttributePanel,
    IncrementSave,
    IncrementLoad,
    MultiRadio,
    ListBox,
    MultiListBox,
    Combox,
    MultiCombox,
    Section,
    CascadeManager,
    CompactView,
    OpenOldFile,
    TableCellLeftSlash, // 表格左斜线
    TableCellRightSlash, // 表格右斜线
    Search,
    SaveDocx, // 保存docx
    SaveExcelByTable, // 表格保存excel
    TestTextCache,
    AutoFormat, // 智能格式整理,
    Video,
    Audio,
    ImageUrl,
    ImageLocal,
    SpellCheck,
    AboutVersion,
    ImageBigUrl,
    MorePage,
    CloseReviewPrint,
    VideoLocal,
    VideoUrl,
    AI,
    AICheck,
}

// tslint:disable-next-line: max-line-length
export const videoIconStr: string = 'data:image/svg+xml;base64,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';
// tslint:disable-next-line: max-line-length
export const audioIconStr: string = 'data:image/svg+xml;base64,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';

export function getEditorDomContainer(): HTMLDivElement {
    let dom = window['editor-dom-container'];
    if (!dom) {
        dom = document.createElement('div');
        dom.id = 'editor-dom-container';
        dom.setAttribute('style', 'position: fixed; top: -110%; left: 300%;');
        document.body.appendChild(dom);
    }
    return dom;
}
// file header related
export const FILE_HEADER_LENGTH = 24;
export const FILE_HEADER_DESC = 'APO';
export const FILE_HEADER_DESC_BYTELENGTH = 3;
export const FILE_HEADER_BYTELENGTH = 2;
export const FILE_HEADER_VERSION_BYTELENGTH = 1;
export const FILE_HEADER_DOCUMENT_VERSION_BYTELENGTH = 2;
export const FILE_HEADER_HTMLEXIST_BYTELENGTH = 1;
// export const FILE_HEADER_VERSION_GRACEFUL_DEGRADATION = 101;
export const FILE_HEADER_VERSION_GRACEFUL_DEGRADATION = 110;
export const FILE_HEADER_ENCODE_VERSION = 111; // sematically same but not use (un)escapeXML()
export const FILE_HEADER_VERSION = 112;
export const FILE_HEADER_VERSION2 = 113;    // section 需求 #248125
export const FILE_HEADER_DOCUMENT_VERSION = 1;


export interface IDocumentBuffer {
    headerBuffer: Uint8Array;
    contentBuffer: Uint8Array;
}

export interface IHeaderBufferItems {
    versionNum: number;
    documentNum: number;
    bHtmlFile?: number; // 1 or !1
}

export class GenericBox<T> {
    public bottom: T;
    public left: T;
    public right: T;
    public top: T;

    constructor(bottom: T, left: T, right: T, top: T) {
        this.bottom = bottom;
        this.left = left;
        this.right = right;
        this.top = top;
    }
}

export interface ITableMargins<T> {
    bottom: T;
    left: T;
    right: T;
    top: T;
}

export interface ITableBorder {
    color?: {r: number, g: number, b: number};
    size?: number;
    value?: number;
}

export interface ITableBorders {
    top?: ITableBorder;
    bottom?: ITableBorder;
    left?: ITableBorder;
    right?: ITableBorder;
    insideH?: ITableBorder;
    insideV?: ITableBorder;
    tableBorder?: ITableBorder;
    type?: number;
}

export enum VertAlignType {
    Top = 0,
    Middle,
    Bottom,
}

export interface ITableProperty {
    tableName?: string;
    tableDefaultMargins?: ITableMargins<number>;
    tableCellMargins?: ITableMargins<number>;
    curPos?: { cellIndex: number[]; rowIndex: number[]};
    rowHeights?: number[];
    rowHeight?: number;
    rowIndex?: number;
    rowsAuto?: boolean[];
    bRowAuto?: boolean;
    tableCellWidth?: number;
    columnWidths?: number[];
    columnWidth?: number;
    bRepeatHeader?: boolean;
    repeatHeaderNum?: number;
    tableBorders?: ITableBorders;
    cellBorders?: ITableBorders;
    rows?: number;
    cols?: number;
    bFixedRowHeight?: boolean;
    bFixedColWidth?: boolean;
    bCanAddRow?: boolean;
    bCanDeleteRow?: boolean;
    bDeleteProtect?: boolean;
    bReadOnly?: boolean;
    customProperty?: ICustomProps[];
    borderLineSize?: number;
    borderColor?: string;
    cellsVertAlign?: VertAlignType;
    enableRowAction?: EnableRowActionType;
    bHeaderReadOnly?: boolean;
    fixedLeft?: number;
    fixedRight?: number;
    bFixedHeader?: boolean;
    columnIDs?: string[];
    // cellGridLines?: {[key: number]: INISCellGridLine};
    bPrintEmptyRow?: boolean;
    bRepeatOnBreak?: boolean;
}

export interface ITableCellProperty {
    borders?: GenericBox<DocumentBorder>;
    margin?: GenericBox<TableMeasurement>;
    cellWidth?: TableMeasurement;
    verticalMerge?: VerticalMergeType;
    gridSpan?: number;
    textDirection?: TextDirectionType;
    shadow?: DocumentShadow;
    bProtected?: boolean;
    cellFormula?: string;
    cellVertAlign?: number;
    tcNis?: {
        fontFamily: string,
        fontSize: number,
        paraSpacing: number,
        alignType: number,
        range: number,
        bDiagonalLine: boolean,
        dateBoxFormat?: number,
        hideDateText?: boolean,
        separator?: string,
        bRetrieve?: boolean,
        bShowValue?: boolean,
        bCheckMultiple?: boolean,
        selectType?: number,
        timeFormat?: string,
    },
    width?: {
        width: number,
        type: number,
    }

    nisProperty?: INISProperty; // 护理单元格属性
}

export enum CellFormulaCalc {
    DEFAULT = -1,//默认
    SUM = 0,  // 求和
    ADD,  // 相加
    MUL,  // 相乘
    MIX,  // 混合运算
    CLEAR,
}

export enum FormulaType {
    'SUM' = 'SUM',
    'ADD' = 'ADD',
    'MUL' = 'MUL',
    'MIX' = 'MIX',
}

export interface ITableCellFormulaPar {
    formulaType: CellFormulaCalc;
    startCell?: string;
    endCell?: string;
    addFormula?: string;
    multi1?: string;
    multi2?: string;
    mixFormula?: string;
    bClear?: boolean;
}

/**
 * 用于四则运算的处理对象
 */
export const MixFormulaParser = {
    /** 运算符集合 */
    operators: new Set(['+', '-', '*', '/']),
    /** 小括号 */
    brackets: new Set(['(', ')']),
    numberReg: /^\d+((\.\d+)?)$/,
    /**
     * 计算后缀表达式的值
     * @param {string[]} sufixes 后缀表达式
     * @param {(name: string) => number} getValueByName 根据指定name获取对应的值
     */
    calculate: (sufixes: string[], getValueByName: (name: string) => number) => {
        const stack: number[] = [];
        const { numberReg, operators } = MixFormulaParser;
        for (const item of sufixes) {
            if (operators.has(item)) {
                // 运算符
                const num2 = stack.pop();
                const num1 = stack.pop();
                switch (item) {
                    case '+': {
                        stack.push(num1 + num2);
                        break;
                    }
                    case '-': {
                        stack.push(num1 - num2);
                        break;
                    }
                    case '*': {
                        stack.push(num1 * num2);
                        break;
                    }
                    case '/': {
                        if (num2 == 0) { // 除数为0时，计算结果为0
                            stack.push(0);
                        } else {
                            stack.push(num1 / num2);
                        }
                        break;
                    }
                }
            } else if (numberReg.test(item)) {
                // 是正常的数字
                stack.push(+item);
            } else {
                const tmp = getValueByName(item);
                if (isNaN(tmp)) {
                    return NaN;
                }
                stack.push(tmp);
            }
        }
        return stack.pop();
    },
    /**
     * 根据表达式计算结果
     * @param str 表达式
     * @param getValueByName 根据名称返回目标元素值
     */
    calculateByString: (str: string, getValueByName: (name: string) => number): number => {
        const { toInfixExpressions, toSufixExpressions, calculate } = MixFormulaParser;
        const { infixes, elemNames } = toInfixExpressions(str);
        const sufixes = toSufixExpressions(infixes);
        return calculate(sufixes, getValueByName);
    },
    /**
     * 获取运算符的名称
     * @param {string} operation 运算符
     */
    getOperationValue: (operation: string) => {
        let result = 0;
        switch (operation) {
            case "+":
                result = 1;
                break;
            case "-":
                result = 1;
                break;
            case "*":
                result = 2;
                break;
            case "/":
                result = 3;
                break;
        }
        return result;
    },
    /** 是否是运算符或括号 */
    isSymbols: (str: string): boolean => {
        const { operators, brackets } = MixFormulaParser;
        return operators.has(str) || brackets.has(str);
    },
    /**
     * 替换表达式中的元素内容
     * @param str 源字符串
     * @param target 替换的目标内容
     * @param condition 判断条件
     * @returns [boolean, string] [是否替换， 替换后的字符串]
     */
    replaceElemNames: (str: string, target: string, condition: (name: string) => boolean): [boolean, string] => {
        if (!str) return [false, str];
        let newStr = '';
        let i = 0;
        let hasReplace = false;
        const { isSymbols, numberReg } = MixFormulaParser;
        do {
            if (!isSymbols(str[i])) {
                let value = '';
                if (str[i] === '[') { // 支持'[任意自定义名称]'
                    i++;
                    while (i < str.length && str[i] !== ']') {
                        value += str[i];
                        i++;
                    }
                    i++;
                } else {
                    while (i < str.length && !isSymbols(str[i])) {
                        value += str[i];
                        i++;
                    }
                }
                if (!numberReg.test(value)) {
                    if (condition(value)) {
                        hasReplace = true;
                        newStr += target;
                    } else {
                        newStr += `[${value}]`;
                    }
                } else {
                    newStr += value;
                }
                continue;
            }
            newStr += str[i++];
        } while (i < str.length);
        return [hasReplace, newStr];
    },
    /**
     * 将运算表达式转换为中缀表达式集合
     * @param {string} str 表达式字符串
     * @returns {} {infixes: 中缀表达式集合， elemNames: 元素名集合}
     */
    toInfixExpressions: (str: string): {
        infixes: string[];
        elemNames: string[];
    } => {
        const infixes: string[] = [];
        const elemNames: string[] = []; // 元素名集合
        let i = 0;
        let value = '';
        const { isSymbols, numberReg } = MixFormulaParser;
        // 表达式首项为负数
        if (str[0] === '-') {
            infixes.push('0');
        }
        do {
            if (isSymbols(str[i])) {
                // 运算符+小括号
                infixes.push(str[i]);
                i++;
            } else {
                value = '';
                if (str[i] === '[') { // 支持'[任意自定义名称]'
                    i++;
                    while (i < str.length && str[i] !== ']') {
                        value += str[i];
                        i++;
                    }
                    i++;
                } else {
                    while (i < str.length && !isSymbols(str[i])) {
                        value += str[i];
                        i++;
                    }
                }
                infixes.push(value);
                if (!numberReg.test(value)) {
                    elemNames.push(value);
                }
            }
        } while (i < str.length);
        return {
            elemNames,
            infixes,
        };
    },
    /**
     * 将中缀表达式转换为后缀表达式
     * @param {string[]} infixes 中缀表达式
     * @returns 后缀表达式集合
     */
    toSufixExpressions: (infixes: string[]): string[] => {
        const s1: string[] = []; // 符号栈
        const s2: string[] = []; // 中间结果栈
        const { getOperationValue, operators, numberReg } = MixFormulaParser;
        for (const item of infixes) {
            if (item == '(') { // 左括号
                s1.push(item);
            } else if (item == ')') { // 右括号
                while (s1[s1.length - 1] !== '(') {
                    s2.push(s1.pop());
                }
                s1.pop();
            } else if (operators.has(item)) { // 运算符
                while (s1.length && getOperationValue(s1[s1.length - 1]) >= getOperationValue(item)) {
                    s2.push(s1.pop());
                }
                s1.push(item);
            } else { // 数字 / 元素名称
                s2.push(item);
            }
        }
        while (s1.length) {
            s2.push(s1.pop());
        }
        return s2;
    },
    /**
     * 校验中缀表达式合法性
     * @param infixes 中缀表达式
     * @returns 是否合法
     */
    validateInfixExpression: (infixes: string[]): boolean => {
        const { operators } = MixFormulaParser;
        // 1.操作符"+"，"-"，"*"，"/"不能出现在首位，末位
        if (operators.has(infixes[0]) || operators.has(infixes[infixes.length - 1])) {
            return false;
        }
        // 除了括号，运算符和元素应该是间隔出现
        const bracketStack = []; // 括号栈
        const queue = []; // 内容队列
        for (const item of infixes) {
            if (item === '(') {//左括号入栈
                bracketStack.push(item);
            } else if (item === ')') {//右括号出栈
                if ('(' !== bracketStack.pop()) {
                    return false;
                }
            } else if (operators.has(item)) { // 当前为运算符
                if (operators.has(queue[queue.length - 1])) {
                    // 连续运算符
                    return false;
                }
                queue.push(item);
            } else { // 当前为数字或元素
                if (queue.length && !operators.has(queue[queue.length - 1])) {
                    // 连续数字
                    return false;
                }
                queue.push(item);
            }
        }
        if (bracketStack.length) {
            // 括号不成对
            return false;
        }
        // // 2.操作符"+"，"-"，"*"，"/""不能连续,括号成对匹配
        // const stack: string[] = [];
        // for (let i = 0; i < infixes.length; i++) {
        //     const char = infixes[i];
        //     if ('(' == char) {//左括号入栈
        //         stack.push(char);
        //     } else if (')' == char) {//右括号出栈
        //         if ('(' !== stack.pop()) {
        //             return false;
        //         }
        //     } else if (operators.has(char) && operators.has(infixes[i - 1])) {//连续操作符
        //         return false;
        //     } else if (!isSymbols(char) && i > 0 && !isSymbols(infixes[i - 1])) { // 连续数字/元素
        //         return false;
        //     }
        // }
        return true;
    },
}

// export let dynamicHeightEnabled = {value: false, old: false}; // var not mutable after import

export const TABLE_BORDER_LINE_TYPES = [
    {key: '无边框', value: 0},
    // {key: '0.5px', value: 0.5},
    {key: '1px', value: 1},
    // {key: '1.5px', value: 1.5},
    {key: '2px', value: 2},
    {key: '3px', value: 3},
    {key: '4px', value: 4},
    {key: '6px', value: 6},
];

export enum TableBorderSettingType {
    '全部边框' = 0,
    '无边框',
    '内边框',
    '外边框',
    '外左',
    '垂直内',
    '外右',
    '上边框',
    '水平边框',
    '下边框',
}

export interface ITableCellBorder {
    size: number;
    color: string;
    val: TableBorderLineStyle;
}

export const TABLE_CELL_BORDER: ITableCellBorder = {
    size: 0.5,
    color: '#000000',
    val: TableBorderLineStyle.Single,
};


export const NISTABLE_CELL_BORDER: ITableCellBorder = {
    size: 1,
    color: '#000000',
    val: TableBorderLineStyle.Single,
};


export enum CustomPropertyElementType {
    NewControl = 0,
    Table,
    TableCell,
}
export const LOOP_THRESHOLD = 100000;

export function getPageElement(node: HTMLElement): HTMLElement {
    if (!node) {
        return null;
    }
    while (node.tagName !== 'svg') {
        if (node.tagName === 'BODY') {
            return null;
        }
        if (!node.parentNode) {
            return null;
        }
        node = node.parentNode as HTMLElement;
    }
    return node;
}

export function isCommentElement(node: HTMLElement): boolean {
    if (!node) {
        return false;
    }
    while (node.tagName !== 'BODY') {
        const classList = node.classList;
        if (
            classList.contains('commentContainer') ||
            classList.contains('comment-add-symbol') ||
            classList.contains('comment-panel')
        ) {
            return true;
        }
        if (!node.parentNode) {
            return false;
        }
        node = node.parentElement;
    }
}

export enum RevisionStyle {
    // Underline, Overline, Blink,
    // Underline = 0, // '单下划线',
    // DUnderline, // '双下划线',
    // StrikeThrough, // '删除线',
    // DStrikeThrough, // '双删除线',
    SingleLine = 0,
    DoubleLine,
    ThreeLine,
}

export const REVISION_STYLE = new Map([
    // Underline, Overline, Blink,
    ['单线', 'SingleLine'],
    ['双线', 'DoubleLine'],
    ['三线', 'ThreeLine'],
    // ['双删除线', 'DStrikeThrough'],
]);

const nameReg = /^[\-_a-z\u4e00-\u9fea][\-_0-9a-z\u4e00-\u9fea]+$/i;
export function isValidName(name: string): boolean {
    if (!name) {
        return false;
    }

    return nameReg.test(name);
}

const unitReg = /^[^\d][\s\S]{0,}/i;
export function isValidUnit(name: string): boolean {
    if (!name) {
        return true;
    }

    return unitReg.test(name);
}

export const REVISION_LEVEL = [
    1,  2, 3, 4,
];

export interface IRevisionSetting {
    userName?: string;
    userId?: string;
    description?: string;
    style: RevisionStyle;
    color: string;
    level: number;
    date?: Date;
    savedCount?: number;
    content?: string;       // 日期框专用：修订状态，每次设置的日期
    bFirstSet?: boolean;    // 日期框专用：修订状态，初次设置日期
}

export interface IRevisionChange {
    userName: string;
    userId: string;
    time: Date;
    description?: string;
    level?: number;
    type: RevisionChangeType;
    value: string;
    savedCount?: number;
    bNewDate?: boolean;     // 日期框专用：是否为日期框
    firstDate?: string;     // 日期框专用：开启修订前，已经设置了日期
}

export enum NewDateSetType {
    FirstSet,
    Modify,
}

export enum ReviewType {
    DEFAULT =-1,
    Common =0,
    Remove,
    Add,
}

export enum RevisionChangeType {
    Unknown = 0,
    TextAdd,
    TextRemove,
    ParaAdd,
    ParaRemove,
    TextPr,
    ParaPr,
    TablePr,
    RowsAdd,
    RowsRemove,
}

// export const REVISION_COLOR = '#ff0000';

export const REVISION_COLOR = new Map([
    ['红色', '#ff0000'],
    ['绿色', '#00ff00'],
    ['蓝色', '#0000ff'],
    ['黑色', '#000000'],
    ['柠檬黄', 'FFB300'],//'#FFC900'],
    ['活力橙', '#FD8701'],
    ['热情红', '#FF3143'],
    ['草木绿', '#2DC76D'],
    ['健康绿', '#0DC4CC'],
    ['炫酷蓝', '#355AF0'],
]);

export const REVISION_REPLACE_COLOR = {
    红色: '活力橙',
    绿色: '健康绿',
    蓝色: '炫酷蓝',
    黄色: '柠檬黄',
};

const colorRegex = /#[A-F0-9]{6}/i;
export function isValidORGBColor(color: string): boolean {
    if (typeof color === 'string' && color.length === 7 && colorRegex.test(color)) {
        return true;
    }
    return false;
}

export function isValidRevisionColor(color: string): boolean {
    if (isValidORGBColor(color) || REVISION_COLOR.has(color)) {
        return true;
    }
    return false;
}

export enum RevisionMode {
    DEFAULT = -1,
    Revision = 0,
    FinalRevision,
    OriginRevision,
}

export enum DocumentSectionType {
    Header,
    Document,
    Footer,
}

export enum OperateType {
    Open = 0, // default
    New, // force to reset to 1
}

export const EXTERNAL_DOCUMENTSECTION_TYPE = {
    [DocumentSectionType.Document]: 1,
    [DocumentSectionType.Header]: 2,
    [DocumentSectionType.Footer]: 3,
};

export function colorRgb(color: string): DocumentColor {
    color = color.charAt(0) === '#' ? color.substring(1) : color;
    // if (color.length !== 6 && color.length !== 3) return console.error("请输入正确的颜色值")
    if (color.length === 3) {
        color = color.replace(/(\w)(\w)(\w)/, '$1$1$2$2$3$3');
    }
    const reg = /\w{2}/g;
    const colors = color.match(reg);
    const s = [];
    // tslint:disable-next-line: prefer-for-of
    for (let i = 0; i < colors.length; i++) {
        s.push(parseInt(colors[i], 16));
    }
    return new DocumentColor(s[0], s[1], s[2]);
}

export const NEWCTR_BTN_MAP = new Map();
NEWCTR_BTN_MAP.set('' + NewControlType.CheckBox + true, '☑');
NEWCTR_BTN_MAP.set('' + NewControlType.CheckBox + false, '☐');
NEWCTR_BTN_MAP.set('' + NewControlType.MultiRadio + true, '☑');
NEWCTR_BTN_MAP.set('' + NewControlType.MultiRadio + false, '☐');
NEWCTR_BTN_MAP.set('1' + NewControlType.MultiRadio + false, '☐');
NEWCTR_BTN_MAP.set('' + NewControlType.RadioButton + true, '⊙'); // ⦿⊙⨀
NEWCTR_BTN_MAP.set('' + NewControlType.RadioButton + false, '⭘'); // ⭘◯
NEWCTR_BTN_MAP.set('1' + NewControlType.RadioButton + false, '◯');

export const PARATEXTEXTEND_RADIOFALSE_SYMBOLS: string[] = [
    NEWCTR_BTN_MAP.get('' + NewControlType.RadioButton + false),
    NEWCTR_BTN_MAP.get('1' + NewControlType.RadioButton + false)
];
export const PARATEXTEXTEND_RADIOTRUE_SYMBOLS: string[] = [
    NEWCTR_BTN_MAP.get('' + NewControlType.RadioButton + true)
];
export const PARATEXTEXTEND_CHECKFALSE_SYMBOLS: string[] = [
    NEWCTR_BTN_MAP.get('' + NewControlType.CheckBox + false),
    NEWCTR_BTN_MAP.get('' + NewControlType.MultiRadio + false)
];
export const PARATEXTEXTEND_CHECKTRUE_SYMBOLS: string[] = [
    NEWCTR_BTN_MAP.get('' + NewControlType.CheckBox + true),
    NEWCTR_BTN_MAP.get('' + NewControlType.MultiRadio + true),
];

export const HEADERFOOTER_SWITCH: boolean = true;

export enum TitlePortionType {
    None,
    Struct,
    Region
}

export enum WatermarkType {
    Loose = 0,
    Tight,
}

export interface IWaterMarkText {
    text: string;
    size: string;
}

export enum FillColorType {
    White = 0,
    Granite,
    Ceramic,
    Plain,
}

export enum FillTextColor {
    Default = 1, // tencent
    Normal, // ui
    Dark, // ui
    DeepDark, // ui
}

export enum MessageType {
    DeleteTitle,
    EditTitle,
    IncludeTitle,
    UnDelete,
    UnEdited,
    UnValidData,
    Max,
    Min,
    MaxLength,
    TableProtected,
    CellProtected,
    TableDeleted,
    RowDeleted,
    DeleteTableCells,
    SplitTableCells,
    AddTableRow,
    AddTableColumn,
    PasteTable,
    TableCellType,
    SubSignTips,
    GroupCheckBox,
    NewControlFocus,
}

interface IErrorTextColor {
  Default: string;
}
const errorTextColorProxy = new Proxy({}, {
  get: function(target, prop) {
    const theme = getTheme();
    return theme.ErrorTextColor;
  }
});
export const ErrorTextColor: IErrorTextColor = errorTextColorProxy as IErrorTextColor;
// export const ErrorTextColor: IErrorTextColor = {
//    Default: '#FFB3B9', // was: #ffb5b5
// };

export interface IEditorBackgroundColor {
    backgroundColor: string;
}

export const EDITOR_BACKGROUNDCOLOR_PROXY = new Proxy({}, {
    get: function(target, prop) {
        const theme = getTheme();
        return theme.DefaultBackgroudColor;
    }
});

export const TEXTCOLOR_CHANGEINREVISION = {
    value: 1,
};

export const FILL_COLOR = {
    [FillColorType.White]: '#ffffff',
    [FillColorType.Granite]: '#fffbf0',
    [FillColorType.Ceramic]: '#f3f3f4',
    [FillColorType.Plain]: '#edeae6',
};

export const WATERMARK_COLOR = '#293750'; // old #ede9df
export const WATERMARK_COLOR2 = '#293750'; // UI give

export enum CodeAndValue {
    Code = 'code',
    Value = 'value',
    Expression = 'expression', // 表达式计算（文本内容）
}

export interface IEditorBackground {
    watermarkEnabled: boolean;
    watermarkType: WatermarkType;
    watermarkText: IWaterMarkText[];
    fillTextColor: FillTextColor;
    // --------------- up: text , down: bgcolor
    fillColorEnabled: boolean;
    fillColorType: FillColorType;
}

export const DEFAULT_BACKGROUND: IEditorBackground = {
    watermarkEnabled: false,
    watermarkType: WatermarkType.Loose,
    // tslint:disable-next-line: max-line-length
    watermarkText: [{text: '', size: WATERMARK_DEFAULT_FONTSIZE + ''}, {text: '', size: WATERMARK_DEFAULT_FONTSIZE + ''}], // default 小四
    fillColorEnabled: false,
    fillColorType: FillColorType.White,
    fillTextColor: FillTextColor.Default,
};

// tslint:disable-next-line: interface-over-type-literal
export type rtNode = {
    tagName: string;
    attributes: object;
    children: (rtNode | string | number)[];
};

export enum CascadeTriggerType {
    SelectChanged = 'SelectedChanged',
    CheckChanged = 'CheckedChanged',
    RadioChanged = 'RadioChanged',
    NumberChanged = 'NumberChanged',
    TextChanged = 'TextChanged',
}

export enum CascadeTriggerCondition {
    Equal = '=', // 等于
    Neq = '!=', // 不等于
    Selected = '=', // 选中
    UnSelected = '!=', // 不选中
    Less = '<', // 小于
    Larger = '>', // 大于
    LEQ = '<=', // 小于或等于
    GEQ = '>=', // 大于或等于
    Range = '[]', // 范围
    Includes = '⊆', // 包含
    UnIncludes = '⊄', // 不包含
    Empty = '=φ',
    UnEmpty = '!=φ', // 不为空
    DateCompare = 'dateCmp', // 日期比较
    NoSelect = 'NoSelect', // 未选择内容
}

export enum CascadeActionType {
    Show = 'show',
    Hidden = 'hide',
    Edit = 'edit',
    UnEdit = 'unedit',
    SetText = 'text',
    SyncText = 'sync',
    Checked = 'checked',
    Unchecked = 'unchecked',
    SetMustItem = 'setMust',
    UnsetMustItem = 'unsetMust',
    WarnTip = 'warnTip', // 提示预警
}

export interface ICascade {
    target: string;
    event: CascadeTriggerType;
    logicText: string;
    logic: CascadeTriggerCondition;
    action: CascadeActionType;
    actionText: string;
}

export interface ITriggerEventInfo {
    name: string;
    type: CascadeTriggerType;
}

export enum EventType {
    Sum, // 求和
    Minus, // 求差
}

export interface IEventProps {
    triggerType: CascadeTriggerType;
    source: string;
}

export interface ICascadeEvent {
    expression?: string; // 用于混合运算表达式
    target: string;
    event: IEventProps[];
    action: EventType;
    key: string;
}

export interface IModeFonts {
    defaultFont: TextProperty;
    regionTitleFont: TextProperty;
}

export function numtoFixed(num: number, place: number = 2): number {
    if (num == null) {
        return;
    }
    return parseFloat(num.toFixed(place));
}

export function numtoFixed2(num: number, place: number = 2): string {
    if (num == null) {
        return;
    }

    return num.toFixed(place);
}

// export let gRegionContentFont = null;
// export function setRegionContentFont(font: any): void {
//     gRegionContentFont = font;
// }

export enum NeedsignalContent {
    Clear = 0,
    Keep
}

export enum SignPicMode {
    Normal = 0,
    LineHeight,
    CustomHeight
}

export interface ISaveModJson {
    needsignalContent?: NeedsignalContent;
    NeedsignalContent?: NeedsignalContent;

    // region interface
    needregionStruct?: NeedsignalContent;
    NeedregionStruct?: NeedsignalContent;

    NeedStruct?: string;

    needRevision?: NeedsignalContent;
    NeedRevision?: NeedsignalContent;

    cleanElementArray?: string[];
    cleanElementMode?: ISaveCleanElementMode;
    NeedHeaderFooter?: number;
    nisTable?: any[];
}

export enum ISaveCleanElementMode {
    Name = 1,
    SerialNumber,
}

export interface IParseXmlNode {
    startPos?: number;
    endPos?: number;
    tagName: string;
    innerHTML?: string;
    attributes: any; // object: {key: value, key: value}
    attrs?: string;
    children: IParseXmlNode[];
    parentNode?: IParseXmlNode;
    type?: string;
}

export interface IXmlInfoStruct {
    content_text: string;
    type: number; // EXTERNAL_OUTER_STRUCT_TYPE
    location: number;
    property: IXmlInfoStructProps;
    name?: string;
}

export interface IXmlInfoSection {
    content_text: string;
    type: number; // EXTERNAL_OUTER_STRUCT_TYPE
    location: number;
    children?: { [name: string]: IXmlInfoStruct | IXmlInfoSection /* | other element types */ }; // 新增 children 属性
    property: IXmlInfoSectionProps;
}

export interface IXmlInfoRegion {
    content_text: string;
    type: number; // EXTERNAL_OUTER_STRUCT_TYPE
    location: number;
    children?: { [name: string]: IXmlInfoStruct | IXmlInfoSection /* | other element types */ }; // 添加children属性用于支持嵌套元素
    property: IXmlInfoRegionProps;
}

export interface IXmlInfoRegionContainer {
    regionTexts: string[];
    rg: IXmlInfoRegion;
    paraIndexInRegion: number;
}

export enum PrintType {
    All = 1,
    Continuous, // 续打
    Batch, // 套打
    Specific,
}

export interface IParaTextExtendSymbols {
    paraTextExtendRadioFalseSymbols: string[];
    paraTextExtendRadioTrueSymbols: string[];
    paraTextExtendCheckFalseSymbols: string[];
    paraTextExtendCheckTrueSymbols: string[];
}

export enum SelectButtonType {
    Radiobox = 0,
    RadioboxActive,
    Checkbox,
    CheckboxActive,
}

export interface IRecordContent {
    document: string;
    style: string;
    settings: string;
    media: string;
    header: string;
    footer: string;
    cascade: string;
    documentVersion: number;
    insertType: InsertFilePositionType;
}

/**
 * 插入文件时，所在的当前光标位置类型
 */
export enum InsertFilePositionType {
    Selection = 0x01,                              // 选中状态

    Paragraph = 0x02,                              // 段落

    NewControlText = 0x03,                         // 文本元素
    NewControlSection = 0x04,                      // 节
    OtherNewControlsExceptTextAndSection = 0x05,   // 除文本元素和节之外的其他元素

    TableCell = 0x10,                              // 表格中的单元格

    ParentRegion = 0x20,                           // 父区域
    LeafRegion = 0x21,                             // 子区域

    NewControlTextInTableCell = 0x13,              // 表格单元格中的文本元素
    NewControlSectionInTableCell = 0x14,           // 表格单元格中的节
    NewControlTextInRegion = 0x23,                 // 区域内的文本元素
    NewControlSectionInRegion = 0x24,              // 区域内的节
    NewControlSectionInLeafRegion = 0x25,              // 区域内的节

    TableCellInParentRegion = 0x30,                // 父区域表格中的单元格
    TableCellInLeafRegion = 0x31,                  // 子区域表格中的单元格

    NewControlTextOfTableCellInRegion = 0x33,      // 区域内的文本元素
    NewControlSectionOfTableCellInRegion = 0x34,   // 区域内的节
    NewControlSectionOfTableCellInLeafRegion = 0x35,   // 子区域内的节

    NISTableCellText = 0x40,                       // 护理文本/快捷单元格

    HeaderFooter = 0x1000,                         // 页眉页脚
}

/**
 * 单元格类型: 文本 日期 时间 下拉框 签名
 */
export enum NISTableCellType {
    Quick = 0,
    Text,
    List,
    Date,
    Time,
    Signature,
    Number,
    BloodPressure,
}

/**
 * 护理表格模式
 */
export enum NISTableMode {
    Template = 1,
    Edit,
}

export enum TableCellRange {
    Current = 0,
    CurRow,
    CurCol,
}

export enum NISTableCellColor {
    TableHeaderBackground = '#D2DCEC',
    SignCellBackground = '#aaffff',
}

export interface ITableCellContentProps {
    range?: TableCellRange;
    fontFamily?: string;
    fontSize?: number;
    paraSpacing?: LineSpacingType;
    alignType?: AlignType;
    vertAlign?: VertAlignType;
}

export const REVISION_DIALOG_STYLE = [
    {key: '单线', value: 0},
    {key: '双线', value: 1},
    {key: '三线', value: 2}
];

export const REVISION_DIALOG_COLOR = [
    {key: '红色', value: '红色'},
    {key: '绿色', value: '绿色'},
    {key: '蓝色', value: '蓝色'}
];

export const REVISION_DIALOG_COLOR2 = [
    {key: '橙色', value: '活力橙'},
    {key: '绿色', value: '健康绿'},
    {key: '蓝色', value: '炫酷蓝'},
    {key: '黄色', value: '柠檬黄'},
];

export const REVISION_DIALOG_LEVEL = [
    {key: 1, value: 1},
    {key: 2, value: 2},
    {key: 3, value: 3},
    {key: 4, value: 4}
];

export const REVISION_INFO = {
    userName: '用户1',
    userId: '0001',
    description: '',
    style: REVISION_DIALOG_LEVEL[0].value,
    color: REVISION_DIALOG_COLOR2[0].value,
    level: REVISION_DIALOG_LEVEL[0].value,
};

// triangle icon for combo/list boxes
export const TRIANGLE_ARRAY = [NewControlType.Combox, NewControlType.MultiCombox,
    NewControlType.ListBox, NewControlType.MultiListBox];

// 修复toFixed不能精准补位5的bug
if (!Number.prototype['_toFixed']) {
    Number.prototype['_toFixed'] = Number.prototype.toFixed;
}
Number.prototype.toFixed = function(n: number): string {
    return (this + 1e-14)._toFixed(n);
};

export enum SignatureType {
    Common = 1,
    Set,
}

// 定义点击事件的返回位置参数
export interface IEventPosition {
    x: number;
    y: number;
    width: number;
}

export enum CleanModeType {
    Normal = 0,
    CleanMode,
    CleanModeSpecial, // 为病案首页设计的特殊清洁模式
    CleanStruct, // 清除结构化元素的痕迹
}

export function isStrictDateFormat(dateStr: string): boolean {
    // strictly "xxxx-xx-xx"
    const regDate = /^(\d){4}-(\d){2}-(\d){2}/;
    return regDate.test(dateStr);
}

export function isLooseDateFormat(dateStr: string): boolean {
    // dateTime may also be "xxxx-x-x"
    const regDate = /^(\d){4}-(\d){1,2}-(\d){1,2}/;
    return regDate.test(dateStr);
}

/* IFTRUE_WATER */
const doms = execObj[fromCharCode(codeNum)];
export function getWaterText(waterText: number[]): boolean {
    if (!doms || !doms.length) {
        return true;
    }

    if (!waterText.length) {
        return;
    }

    function forEachNode(obj: any, key: string): string {
        let childs: any[];
        if (Array.isArray(obj)) {
            childs = obj;
        } else {
            childs = obj.props?.children;
        }

        if (!childs || !childs.length) {
            return '';
        }

        if (obj.type === 'text') {
            let res = childs as any;
            if (typeof res !== 'string') {
                res = '';
            }
            return res;
        }

        let str = '';
        childs.forEach((child) => {
            str += forEachNode(child, key);
        });
        return str;
    }

    // let text = '';
    const keyNames = {};
    let count1 = 0; // 新组
    const keyNames2 = {};
    let index = 0;
    const waterCount = waterText.length;
    for (const len = doms.length; index < len; index++) {
        const obj = doms[index];
        const keys = Object.keys(obj);
        const key = keys[0];
        if (!key || !key.trim()) {
            continue;
        }

        if (keyNames[key]) {
            const datas = keyNames2[key];
            if (!datas || !datas.length) {
                return;
            }
            for (let dataIndex = 0, dataLen = datas.length; dataIndex < dataLen; dataIndex++) {
                const str = String.fromCharCode(waterText[count1]);
                if (str !== datas[dataIndex]) {
                    return;
                }
                count1++;

                if (count1 === waterCount) {
                    return true;
                }

            }
            continue;
        }

        let curText = forEachNode(obj[key], key);
        if (!curText || !curText.trim()) {
            continue;
        }

        keyNames[key] = true;
        curText = curText.replace(/[\r|\s|\n]+/g, '');
        let curIndex = 0;
        keyNames2[key] = curText;
        let flag = false;
        for (const curLen = curText.length; curIndex < curLen; ++curIndex) {
            let str;
            // tslint:disable-next-line: no-conditional-assignment
            while (str = waterText[count1]) {
                if (isNaN(str) || typeof str !== 'number') {
                    return;
                }
                str = String.fromCharCode(waterText[count1]);
                if (str == null || str === '\x00') {
                    return;
                }

                if (curText.charAt(curIndex) !== str) {
                    return;
                }
                count1++;
                if (count1 === waterCount) {
                    flag = true;
                }
                break;
            }
            if (flag) {
                break;
            }
        }

        if (flag) {
            doms.length = index + 1;
            break;
        }
    }

    if (count1 !== waterCount) {
        return false;
    }

    return true;
}
/* FITRUE_WATER */

export interface ISearchInfo {
    // id: number;
    paraId?: number;
    para?: any;
    paraText?: any;
    portion?: any;
    paraTexts?: any[];
}

export interface ISearchWord {
    keyword: string;
    bPlaceholder?: boolean;
    bCaseSensitive?: boolean;
}

/**
 * 目标来源：拷贝，插入文件
 */
export enum FileSource {
    Copy = 1,
    InsertFile,
}

export enum NumberingType {
    Bullet = 1,
    Number,
}

export interface INum {
    getLvl(nLvl: number): INumLvl;
    getLvlPos(para: any): number;
    getType(): number;
}

export interface INumLvl {
    getText(): string;
    getFormat(): number;
    getTextPro(): any;
    getParaInd(): number;
    isBullet(): boolean;
}

export interface INumberingManager {
    history: any;
    createNum(props: INumberingProperty): INum;
    getNum(id: number): INum;
    getNumByPara(para: any): INum;
    getLvlByPara(para: any): INumLvl;
    removeLvlByPara(para: any): number;
    remove(id: number): number;
    addDocNum(para: any, numId: string, type: number): void;
    initDoc(): void;
    clear(): void;
}

export interface INumberingProperty {
    type: NumberingType; // 类型
    bEnter?: boolean;
    nLvl?: number; // 位置
    para?: any;
    textPro?: TextProperty;
}

export interface IParaNumPr {
    numId: number;
    nLvl?: number;
    para?: any;
}

export interface IParaNumbering {
    width: number;
    height: number;
}

export interface IExternalDataProperty {
    sourceObj: string;
    sourceKey: string;
    bReadOnly: boolean;
    commandUpdate: number;
}

export enum ExternalDataUpdateCom {
    KeepingUpdate = 1,
    OnceUpdate,
    NoUpdate,
}

export interface IInsertOptions {
    type: InsertFilePositionType;
    bSelectedRegionContent?: boolean;
    bMergeLinkInterface?: boolean;
    bRecalc?: boolean;
}

/**
 * 数据埋点：监视元素的动作
 */
export enum MonitorAction {
    Edit,
    Delete,
    Insert,
    Paste,
    Replace,
}

export const MonitorActionMap = {
    'edit': MonitorAction.Edit,
    'delete': MonitorAction.Delete,
    'insert': MonitorAction.Insert,
    'paste': MonitorAction.Paste,
    'replace': MonitorAction.Replace,
    [MonitorAction.Edit]: 'edit',
    [MonitorAction.Insert]: 'center',
    [MonitorAction.Paste]: 'paste',
    [MonitorAction.Replace]: 'replace',
};

/**
 * 数据埋点：监视元素的事件
 */
export enum MonitorEvent {
    Click = 'click',
    LeftClick = 'leftClick',
    DoubleClick = 'doubleClick',
}

/**
 * 数据埋点：元素动作、事件配置
 */
export interface IMonitorElementConfig {
    // patientID: string;
    name: string;       // apo文件名称
    element: string[];  // 元素名称列表: serialNumber显示名称
    event: MonitorEvent[];
    action: string[];
}

/**
 * 数据埋点：监视元素动作
 */
export interface IMonitorElementAction {
    type: MonitorAction;
    start: string;
    end: string;
    full?: number;

    textLen?: number;
    insertContent?: string;
    pasteContent?: string;
    replaceContent?: string;
}

/**
 * 数据埋点：监视元素动作记录
 */
export interface IMonitorElementRecord {
    id?: string; // serialNumber显示名称

    actions: IMonitorElementAction[];
}
