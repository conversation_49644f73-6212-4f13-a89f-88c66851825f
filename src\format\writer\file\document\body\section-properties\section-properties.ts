import { XmlComponent } from '../../../xml-components';
import { PageSize } from './page-size';
import { IPageSizeAttributes, PageOrientation } from './page-size/page-size-attributes';
import { IPageMarginAttributes, PageMargin } from './page-margin';
import { SECTION_PROPERTIES_DEFAULT } from '../../../../../../common/commonDefines';
import { IHeaderFooterAttributes, HeaderFooter } from './header-footer';

export type SectionPropertiesOptions =
    IPageSizeAttributes &
    IPageMarginAttributes &
    IHeaderFooterAttributes;

export class SectionProperties extends XmlComponent {

    private readonly options: SectionPropertiesOptions;

    constructor(options?: SectionPropertiesOptions) {
        super('w:sectPr');

        if (options) {
            this.options = options;
        } else {
            this.options = SECTION_PROPERTIES_DEFAULT;
        }

        const {width, height, orientation, top, right, bottom, left, header, footer, gutter,
            showHeader, showFooter, protectHeaderFooter, showHeaderBorder, showFooterBorder} = this.options;
        // console.log(width, height, orientation, top, right, bottom, left, header, footer, gutter)

        this.addPageSize(width, height, orientation);
        this.addPageMargin(top, right, bottom, left, header, footer, gutter);
        if (showHeader !== SECTION_PROPERTIES_DEFAULT.showHeader ||
            showFooter !== SECTION_PROPERTIES_DEFAULT.showFooter ||
            protectHeaderFooter !== SECTION_PROPERTIES_DEFAULT.protectHeaderFooter ||
            showHeaderBorder !== SECTION_PROPERTIES_DEFAULT.showHeaderBorder ||
            showFooterBorder !== SECTION_PROPERTIES_DEFAULT.showFooterBorder ) {
            this.addHeaderFooter(showHeader, showFooter, protectHeaderFooter, showHeaderBorder, showFooterBorder);
        }
    }

    public addPageSize(width: number = this.options.width, height: number = this.options.height,
                       orientation: PageOrientation = this.options.orientation): SectionProperties {

        this.root.push(new PageSize(width, height, orientation));
        return this;
    }

    public addPageMargin(top: number = this.options.top, right: number = this.options.right,
                         bottom: number = this.options.bottom, left: number = this.options.left,
                         header: number = this.options.header, footer: number = this.options.footer,
                         gutter: number = this.options.gutter): SectionProperties {

        this.root.push(new PageMargin(top, right, bottom, left, header, footer, gutter));
        return this;
    }

    public addHeaderFooter(showHeader: number = this.options.showHeader,
                           showFooter: number = this.options.showFooter,
                           protectHeaderFooter: number = this.options.protectHeaderFooter,
                           showHeaderBorder: number = this.options.showHeaderBorder,
                           showFooterBorder: number = this.options.showFooterBorder): SectionProperties {
        this.root.push(new HeaderFooter(showHeader, showFooter, protectHeaderFooter,
             showHeaderBorder, showFooterBorder));
        return this;
    }
}
