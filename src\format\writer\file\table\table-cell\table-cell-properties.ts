import { XmlComponent, XmlAttributeComponent } from '../../xml-components';

import {
    GridSpan,
    TableCellBorders,
    // TableCellWidth,
    VMerge,
    CellFormula,
    CellVertAlign,
    CellLeftSlash,
    CellRightSlash,
    // WidthType,
} from './table-cell-components';
import { ITableCellBorder, TABLE_CELL_BORDER_DEFAULT_COLOR } from '../../../../../common/commonDefines';
import { TableWidthType } from '../../../../../model/core/TableProperty';

export class TableCellProperties extends XmlComponent {
    private readonly cellBorder: TableCellBorders;

    constructor() {
        super('w:tcPr');
        this.cellBorder = new TableCellBorders();
        this.root.push(this.cellBorder);
    }

    public get Borders(): TableCellBorders {
        return this.cellBorder;
    }

    public addGridSpan(cellSpan: number): TableCellProperties {
        this.root.push(new GridSpan(cellSpan));

        return this;
    }

    public addVMerge(type: number): TableCellProperties {
        this.root.push(new VMerge(type));

        return this;
    }

    public addCellWidth(cellWidth: ICellWidthProperties): TableCellProperties {
        this.root.push(new CellWidth(cellWidth));
        return this;
    }

    // public setWidth(width: string | number, type: WidthType): TableCellProperties {
    //     this.root.push(new TableCellWidth(width, type));

    //     return this;
    // }

    public addTopBorder(border: ITableCellBorder): TableCellProperties {
        this.root.push(new TopBorder(border));
        return this;
    }

    public addRightBorder(border: ITableCellBorder): TableCellProperties {
        this.root.push(new RightBorder(border));
        return this;
    }

    public addBottomBorder(border: ITableCellBorder): TableCellProperties {
        this.root.push(new BottomBorder(border));
        return this;
    }

    public addLeftBorder(border: ITableCellBorder): TableCellProperties {
        this.root.push(new LeftBorder(border));
        return this;
    }

    public addCellProtected(val: string): TableCellProperties {
        this.root.push(new CellProtect(val));
        return this;
    }

    public addCellFormula(val: string): TableCellProperties {
        this.root.push(new CellFormula(val));
        return this;
    }

    public addCellVertAlign(val: number): TableCellProperties {
        this.root.push(new CellVertAlign(val));
        return this;
    }

    public addCellSlash(val: number): TableCellProperties {
        if (val === 1) {
            this.root.push(new CellLeftSlash(1));
        } else if (val === 2) {
            this.root.push(new CellRightSlash(1));
        }
        return this;
    }

}

export interface ICellWidthProperties {
    width: number;
    type: TableWidthType;
}

class CellWidthAttributes extends XmlAttributeComponent<ICellWidthProperties> {
    protected xmlKeys: any = {
        width: 'w:w',
        type: 'w:type',
    };
}

// tslint:disable-next-line: max-classes-per-file
export class CellWidth extends XmlComponent {
    constructor(attrs: ICellWidthProperties) {
        super('w:tcW');
        this.root.push(new CellWidthAttributes(attrs));
    }
}

// export interface IBorderProperties {
//     val: TableBorderLineStyle;
//     size: number;
//     color: string;
// }

// tslint:disable-next-line: max-classes-per-file
class BorderAttributes extends XmlAttributeComponent<ITableCellBorder> {
    protected xmlKeys: any = {
        val: 'w:val',
        size: 'w:sz',
        color: 'w:color',
    };
}

// tslint:disable-next-line: max-classes-per-file
class SimplefiedBorderAttributes extends XmlAttributeComponent<ITableCellBorder> {
    protected xmlKeys: any = {
        val: 'w:val',
        size: 'w:sz',
    };
}

// tslint:disable-next-line: max-classes-per-file
export class TopBorder extends XmlComponent {
    constructor(attrs: ITableCellBorder) {
        super('w:top');
        const borderAttrs = getBorderAttrs(attrs);
        this.root.push(borderAttrs);
    }
}

// tslint:disable-next-line: max-classes-per-file
export class RightBorder extends XmlComponent {
    constructor(attrs: ITableCellBorder) {
        super('w:right');
        const borderAttrs = getBorderAttrs(attrs);
        this.root.push(borderAttrs);
    }
}

// tslint:disable-next-line: max-classes-per-file
export class BottomBorder extends XmlComponent {
    constructor(attrs: ITableCellBorder) {
        super('w:bottom');
        const borderAttrs = getBorderAttrs(attrs);
        this.root.push(borderAttrs);
    }
}

// tslint:disable-next-line: max-classes-per-file
export class LeftBorder extends XmlComponent {
    constructor(attrs: ITableCellBorder) {
        super('w:left');
        const borderAttrs = getBorderAttrs(attrs);
        this.root.push(borderAttrs);
    }
}

// tslint:disable-next-line: max-classes-per-file
export class CellProtect extends XmlComponent {
    constructor(flag: string) {
        super('w:protected');
        this.root.push(flag);
    }
}

function getBorderAttrs(attrs: ITableCellBorder): BorderAttributes | SimplefiedBorderAttributes {
    let borderAttrs = null;
    if (attrs != null && attrs.color === TABLE_CELL_BORDER_DEFAULT_COLOR) {
        delete attrs.color;
        borderAttrs = new SimplefiedBorderAttributes(attrs);
    } else {
        borderAttrs = new BorderAttributes(attrs);
    }

    return borderAttrs;
}
