import { fabric } from 'fabric';
import { createRect, buildingRect, updateRectProps } from './Rect';
import { createCircle, buildingCircle, updateCircleProps } from './Circle';
import { createText, updateTextProps } from './Text';
import { createLine, buildingLine, updateLineProps } from './Line';
import { createArrow, buildingArrow, updateArrowProps, IArrow } from './Arrow';

export enum ActionType {
  Move,
  Pencil,
  Rect,
  Circle,
  Line,
  Text,
  Arrow,
}

interface State {
  actionType: ActionType;
  actionPayload?: {
    drawingObject?: fabric.Object;
    arrow?: IArrow;
    originX: number;
    originY: number;
  };
}


export class ImageEditorController {

  public onActionTypeChange: (action: ActionType) => void;

  public onAfterRender: () => void;

  private currentColor: string = '#000';

  private currentSize: number = 1;

  private currentText: string = 'xxxxxx';

  private currentPattern: fabric.Pattern | string = '';

  private readonly state: State;

  private canvas: fabric.Canvas;

  constructor(canvasElementId: string) {
    this.state = {
      actionType: ActionType.Move,
    };
    this.canvas = new fabric.Canvas(canvasElementId, {
      isDrawingMode: false
    });
    this.initEvents();
  }

  public get canvasWidth(): number {
    return this.canvas.getWidth();
  }

  public get canvasHeight(): number {
    return this.canvas.getHeight();
  }

  public setCurrentSize(size: number): void {
    this.currentSize = size;
    this.canvas.getActiveObjects().forEach(obj => this.updateProps(obj));
    this.canvas.renderAll();
  }

  public setCurrentColor(color: string): void {
    this.currentColor = color;
    this.canvas.getActiveObjects().forEach(obj => this.updateProps(obj));
    this.canvas.renderAll();
  }

  public setCurrentPattern(pattern: fabric.Pattern | string): void {
    this.currentPattern = pattern;
    this.canvas.getActiveObjects().forEach(obj => {
      if(obj instanceof fabric.Ellipse || obj instanceof fabric.Rect) {
        this.updateProps(obj);
      }
    });
    this.canvas.renderAll();
  }

  public updateProps(obj: fabric.Object): void {
    const props = {
      color: this.currentColor,
      size: this.currentSize,
      pattern: this.currentPattern
    };

    if(obj instanceof fabric.Rect) {
      updateRectProps(obj, props);
      this.afterRenderHandler();
      return;
    }

    if(obj instanceof fabric.Ellipse) {
      updateCircleProps(obj, props);
      this.afterRenderHandler();
      return;
    }

    if(obj instanceof fabric.Line) {
      updateLineProps(obj, props);
      this.afterRenderHandler();
      return;
    }
    if(obj instanceof fabric.Textbox) {
      updateTextProps(obj, props);
      this.afterRenderHandler();
      return;
    }

    //pencil
    if(obj.isType('path')) {
      obj.set({stroke: props.color, strokeWidth: props.size});
      this.afterRenderHandler();
      return;
    }

    //arrow
    if(obj instanceof fabric.Group) {
      let line: fabric.Line,
        triangle: fabric.Triangle;
      obj.forEachObject(innerObj => {
        if(innerObj instanceof fabric.Line) {
          line = innerObj;
        } else if(innerObj instanceof fabric.Triangle) {
          triangle = innerObj;
        }
      });
      updateArrowProps({line, triangle}, props);
      this.afterRenderHandler();
      return;
    }

  }
  
  public loadByJSON(json: string): void {
    this.canvas.loadFromJSON(json, (...args) => {
      this.canvas.getObjects().forEach(obj => {
        if(obj instanceof fabric.Image) {
          obj.selectable = false;
          let width = obj.width;
          let height = obj.height;
          const scale = this.computeScaleForImage(width, height);
          width = width*scale;
          height = height*scale;
          this.canvas.setWidth(width);
          this.canvas.setHeight(height);
          this.canvas.renderAll();
        }
        if(obj instanceof fabric.Textbox) {
          obj.setControlsVisibility({
            bl: false, br: false, tl: false, tr: false,
            mb: false, ml: false, mt: false, mr: false,
            mtr: false
          });
          obj.editingBorderColor = 'blue';
        }
      });
    });
  }

  public loadByBase64(base64: string): void {
    this.canvas.clear();
    const img = new Image();
    img.src = base64;
    img.onload = () => {
      let width = img.naturalWidth;
      let height = img.naturalHeight;
      const scale = this.computeScaleForImage(width, height);

      width = width*scale;
      height = height*scale;
      const image = new fabric.Image(img);
      image.selectable = false;
      image.scale(scale);
      this.canvas.setWidth(width);
      this.canvas.setHeight(height);
      this.canvas.add(image);
      this.canvas.renderAll();
    };
  }

  private computeScaleForImage(width: number, height: number): number {
      let scale = 1;
      const maxWidth = 800;
      const maxHeight = 450;
      if(width > maxWidth || height > maxHeight) {
        if(width/height >= maxWidth/maxHeight) {
          scale = maxWidth/width;
        } else {
          scale = maxHeight/height;
        }
      }
      return scale;
  }

  public startMove(): void {
    this.enableMoving();
    this.canvas.isDrawingMode = false;
    this.setCurrentActionType(ActionType.Move);
    this.canvas.getObjects().forEach(obj => {
      if(obj instanceof fabric.Path) {
        obj.setControlsVisibility({
          mb: false, ml: false, mt: false, mr: false
        });
      } 
      if(obj instanceof fabric.Textbox) {
        obj.setControlsVisibility({
          bl: false, br: false, tl: false, tr: false,
          mb: false, ml: false, mt: false, mr: false,
          mtr: false
        });
      }
    });
  }

  public startPencil(): void {
    this.disableMoving();
    this.canvas.isDrawingMode = true;
    this.canvas.freeDrawingBrush.color = this.currentColor;
    this.canvas.freeDrawingBrush.width = this.currentSize;
    this.setCurrentActionType(ActionType.Pencil);
  }

  public startRect(): void {
    this.disableMoving();
    this.canvas.isDrawingMode = false;
    this.setCurrentActionType(ActionType.Rect);
  }

  public startCicle(): void {
    this.disableMoving();
    this.canvas.isDrawingMode = false;
    this.setCurrentActionType(ActionType.Circle);
  }

  public startLine(): void {
    this.disableMoving();
    this.canvas.isDrawingMode = false;
    this.setCurrentActionType(ActionType.Line);
  }

  public startArrow(): void {
    this.disableMoving();
    this.canvas.isDrawingMode = false;
    this.setCurrentActionType(ActionType.Arrow);
  }

  public startText(text: string): void {
    this.disableMoving();
    this.canvas.isDrawingMode = false;
    this.setCurrentActionType(ActionType.Text);
    this.currentText = text;
  }

  public deleteActiveObjects(): void {
    this.canvas.getActiveObjects().forEach(obj => {
      this.canvas.remove(obj);
    });
    this.canvas.renderAll();
  }

  public exportAsSvg(): string {
    return this.canvas.toSVG();
  }

  public exportAsJSON(): string {
    return JSON.stringify(this.canvas.toDatalessJSON());
  }

  private initEvents(): void {
    this.canvas.on('mouse:down', (o) => {
      this.mouseDownEventHandler(o);
    });

    this.canvas.on('mouse:move', (o) => {
      this.mouseMoveEventHandler(o);
    });

    this.canvas.on('mouse:up', (o) => {
      this.mouseUpEventHanlder(o);
    });

    ['object:modified', 'object:added', 'object:removed'].forEach(eventName => {
      this.canvas.on(eventName, () => {
        this.afterRenderHandler();
      });
    });
  }

  private afterRenderHandler(): void {
    if(!!this.onAfterRender) {
      this.onAfterRender();
    }
  }

  private mouseDownEventHandler(o: fabric.IEvent): void {
      const {x, y} = this.canvas.getPointer(o.e);
      let drawingObject: fabric.Object;
      let props = {
        color: this.currentColor,
        size: this.currentSize,
        pattern: this.currentPattern
      };

      switch(this.state.actionType) {
        case ActionType.Rect: 
          drawingObject = createRect(x, y, props);
          break;
        case ActionType.Circle: 
          drawingObject = createCircle(x, y, props);
          break;
        case ActionType.Text:
          drawingObject = createText(this.currentText, x, y, props);
          break;
        case ActionType.Line:
          drawingObject = createLine(x, y, props);
          break;
        case ActionType.Arrow: 
          const arrow = createArrow(x, y, props);
          const { line, triangle } = arrow;
          this.canvas.add(line);
          this.canvas.add(triangle);
          this.state.actionPayload = {
            arrow,
            originX: x,
            originY: y
          };
          return;
        default:
          return;
      }

      this.canvas.add(drawingObject);
      this.state.actionPayload = {
        drawingObject,
        originX: x,
        originY: y
      };
  }

  private mouseMoveEventHandler(o: fabric.IEvent): void {
      if(!this.state.actionPayload) {
        return;
      }
      const {x, y}= this.canvas.getPointer(o.e);
      const { drawingObject, arrow, originX, originY } = this.state.actionPayload;

      switch(this.state.actionType) {
        case ActionType.Rect:
          const rect: fabric.Rect = drawingObject;
          buildingRect(rect, originX, originY, x, y);
          break;
        case ActionType.Circle:
          const ellipse: fabric.Ellipse = drawingObject as fabric.Ellipse;
          buildingCircle(ellipse, originX, originY, x, y);
          break;
        case ActionType.Line:
          const line: fabric.Line = drawingObject as fabric.Line;
          buildingLine(line, x, y);
          break;
        case ActionType.Arrow:
          buildingArrow(arrow, x, y);
          break;
        default:
          return;
      }

      this.canvas.renderAll();
  }

  private mouseUpEventHanlder(o: fabric.IEvent): void {
    if(this.state.actionType === ActionType.Arrow && !!this.state.actionPayload?.arrow) {
      const {x, y}= this.canvas.getPointer(o.e);
      const text = createText(this.currentText, x, y, {color: this.currentColor});
      const { line, triangle } = this.state.actionPayload.arrow;
      const group = new fabric.Group([line, triangle]);
      this.canvas.remove(line, triangle);
      this.canvas.add(group);
      this.canvas.add(text);
      this.canvas.setActiveObject(text);
    }
    if(!!this.state.actionPayload?.drawingObject) {
      this.canvas.setActiveObject(this.state.actionPayload.drawingObject);
    }

    //修复矩形和圆形画完无法触发afterRender事件
    if(this.state.actionType === ActionType.Circle || this.state.actionType === ActionType.Rect) {
      this.afterRenderHandler();
    }

    this.state.actionPayload = null;
    // this.startMove();
    this.startByActionType();
  }

  private startByActionType(): void {
    switch (this.state.actionType) {
        case ActionType.Circle: {
            this.startCicle();
            break;
        }
        case ActionType.Line: {
            this.startLine();
            break;
        }
        case ActionType.Move: {
            this.startMove();
            break;
        }
        case ActionType.Pencil: {
            this.startPencil();
            break;
        }
        case ActionType.Rect: {
            this.startRect();
            break;
        }
        default: {
            this.startMove();
        }
    }
  }

  private disableMoving(): void {
    this.canvas.selectionColor = 'rgba(100, 100, 255, 0)';
    this.canvas.selectionLineWidth = 0;
    this.canvas.forEachObject(obj => {
      if(obj.isType('image')) {
        return;
      }
      obj.selectable = false;
    });
    this.canvas.discardActiveObject();
    this.canvas.renderAll();
  }

  private enableMoving(): void {
    this.canvas.selectionColor = 'rgba(100, 100, 255, 0.3)';
    this.canvas.selectionLineWidth = 1;
    this.canvas.forEachObject(obj => {
      if(obj.isType('image')) {
        return;
      }
      obj.selectable = true;
    });
  }

  private setCurrentActionType(actionType: ActionType): void {
    this.state.actionType = actionType;
    if(this.onActionTypeChange) {
      this.onActionTypeChange(this.state.actionType);
    }
  }

}
