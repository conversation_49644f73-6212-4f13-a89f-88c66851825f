import React, { createRef } from 'react';
import '../../style/select.less';
import InputUI from '../Input';
import SelectList from './SelectList';
import ReactDOM from 'react-dom/client';

import { getEditorDomContainer } from '../../../../common/commonDefines';
import {SDKcontainer} from '../../module/SDKcontainer';

interface IState {
    bRefresh: boolean;
}

interface IPropKey {
    name: string;
    value: string;
    disabled: string;
}

interface IProps {
    onChange?: (value: any, name: string, item?: any) => void;
    data: object[];
    value: any;
    prop?: IPropKey;
    disabled?: boolean;
    readonly?: boolean;
    name?: string;
    unDeleted?: boolean;
    width?: number;
    bWrite?: boolean;
}

const selectObj: { 
    dom: HTMLDivElement | null, 
    reactVm: SelectList | null,
    root: ReactDOM.Root | null 
} = {
    dom: null,
    reactVm: null,
    root: null
};

const selectIds: number[] = [];
export default class Select extends React.Component<IProps, IState> {
    private _id: number;
    private _selectDOm: any;
    private reactVm: any;
    private value: any = '333';
    private prop: IPropKey;
    private label: string;
    constructor(props: any) {
        super(props);
        this._id = Math.random();
        selectIds.push(this._id);
        const prop: any = this.props.prop || {};
        this.prop = {
            name: prop.name || 'key',
            value: prop.value || 'value',
            disabled: prop.disabled || 'disabled',
        };
        this._selectDOm = React.createRef();
        this.state = {
            bRefresh: false,
        };
        this.value = this.props.value;
        this.label = this.getLabel();
    }

    public render(): any {
        let className = 'select-input';
        if (this.props.readonly === true) {
            className += ' readonly';
        }
        if (this.props.disabled === true) {
            className += ' disabled';
        }
        let bReadonly;
        if (this.props.bWrite === undefined) {
            bReadonly = true;
        } else {
            bReadonly = !this.props.bWrite;
        }
        return (
            <div className='editor-select'>
                <div className={className} ref={this._selectDOm}>
                    <InputUI
                        onChange={this.onChangeByInput}
                        disabled={this.props.disabled}
                        unDeleted={this.props.unDeleted}
                        readonly={bReadonly}
                        value={this.label}
                        placeholder='请选择'
                        addonAfter={this.renderSelectBtn()}
                    />
                </div>
            </div>
        );
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        if (nextProps.value !== this.value) {
            this.value = nextProps.value;
            this.initLabelByValue(nextProps.data);
        }
    }

    public componentDidMount(): void {
        this.createListDom();
        this.addEvent();
        this.initLabelByValue();
    }

    public componentWillUnmount(): void {
        this.deleteEvent();
        this.removeListDom();
    }

    private initLabelByValue(datas?: any[]): void {
        this.label = this.getLabel(datas);
    }

    private getLabel(items?: any[]): string {
        const value = this.value;
        if (value === undefined) {
            return '';
        }
        const datas = items || this.props.data;
        if (!datas || datas.length === 0) {
            return '';
        }

        const prop = this.prop;
        const lableKey = prop.name;
        const valueKey = prop.value;
        const data = datas.find((item) => item[valueKey] === value);
        let label: string = '';
        if (data) {
            label = data[lableKey];
        } else if (this.props.bWrite && this.props.value) {
            return this.props.value;
        }

        return label;
    }

    private onClose = (id: number): void => {
        //
    }

    private onChangeByInput = (value: string): void => {
        this.value = value;
        this.reactVm.setValue(value);
        if (this.props.bWrite) {
            this.label = value;
            const obj = {};
            obj[this.props.name] = value;
            obj[this.props.value] = value;
            this.updateComponentValue(obj);
            return
        }

        this.updateComponentValue(null);
    }

    private onChange = (value: any, item: any): void => {
        this.value = value;
        this.updateComponentValue(item);
        this.label = item[this.prop.name];
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private updateComponentValue(item: any): void {
        if (typeof this.props.onChange === 'function') {
            this.props.onChange(this.value, this.props.name, item);
        }
    }

    private getWidth(target: HTMLDivElement): number {
        let parentNode: HTMLDivElement = target;
        while (parentNode && parentNode.className.indexOf('inputWrapper') === -1) {
            parentNode = parentNode.parentNode as HTMLDivElement;
        }

        if (parentNode) {
            return parentNode.clientWidth;
        }
    }

    private selectHandleClick = (e: any): void => {
        if (this.props.readonly === true || this.props.disabled === true) {
            return;
        }

        const reactVm = this.reactVm;
        if (reactVm !=null && reactVm.isDisplay(this._id)) {
            return;
        }

        const target = e.target;
        const className = target.className;
        if (className === 'closeBtn') {
            return;
        }

        let width: number = this.props.width;
        if (width === undefined) {
            width = this.getWidth(target);
            if (width === undefined) {
                width = target.parentNode.clientWidth;
            }
            if (width < 100) {
                width = 100;
            }
        }
        // if (className.indexOf('addonAfterGroupWrapper') === -1) {
        //     width = target.offsetParent.clientWidth;
        // }

        const offsetLeft = target.offsetLeft;
        // const offsetTop = target.offsetTop;
        const x = e.clientX - e.offsetX  - offsetLeft;
        if (x < 2) {
            return;
        }
        const y = e.clientY - e.offsetY + target.clientHeight + 3;
        // console.log(x, y, offsetLeft, offsetTop)
        const props = this.props;
        this.reactVm.setData(props.data, this.value, props.disabled, props.readonly, {
            x,
            y,
            width,
            prop: this.prop,
            id: this._id,
            onChange: this.onChange,
            onClose: this.onClose,
        });
        // reactVm.setId(this._id);
        reactVm.setActive(true);
    }

    private addEvent(): void {
        const dom = this._selectDOm.current;
        dom.addEventListener('click', this.selectHandleClick, false);
        dom.addEventListener('keydown', this.preventDefault);
    }

    private deleteEvent = (): void => {
        const dom = this._selectDOm.current;
        if (!dom) {
            console.log('undelete event select');
            return;
        }
        dom.removeEventListener('click', this.selectHandleClick, false);
        dom.removeEventListener('keydown', this.preventDefault);
    }

    private preventDefault = (e: any): void => {
        if (e.keyCode ===  8) {
            e.preventDefault();
        }
    }

    private renderSelectBtn(): any {
        return (<span className='select-btn' />);
    }

    private async createListDom(): Promise<void> {
        const sdk = SDKcontainer.getSDK();
        if (sdk) {
            let obj = sdk.getSelectVm();
            if (obj) {
                this.reactVm = obj.reactVm;
                return;
            }
            obj = await sdk.setSelectList(SelectList);
            if (obj) {
                this.reactVm = obj.reactVm;
            }
            return;
        }
    
        if (selectObj.reactVm !== null) {
            this.reactVm = selectObj.reactVm;
            return;
        }
    
        const div = document.createElement('div');
        div.className = 'hz-editor-select-container';
        getEditorDomContainer().appendChild(div);
        selectObj.dom = div;
    
        const selectRef = createRef<SelectList>(); // 创建 ref
        
        // 只创建一次root并保存
        selectObj.root = ReactDOM.createRoot(div);
        
        selectObj.root.render(
            <SelectList ref={selectRef} />
        );
    
        // 使用 requestAnimationFrame 确保在下一个重绘周期中访问 ref
        requestAnimationFrame(() => {
            if (selectRef.current) {
                selectObj.reactVm = this.reactVm = selectRef.current;
            }
        });
    }

    private removeListDom(): void {
        const activeId = this._id;
        const index = selectIds.findIndex((id) => activeId === id);
        selectIds.splice(index, 1);
        if (selectIds.length === 0 && selectObj.dom) {
            // 使用setTimeout延迟unmount操作到下一个事件循环
            // 这样可以避免在React渲染过程中同步卸载根节点
            setTimeout(() => {
                if (selectObj.root) {
                    selectObj.root.unmount();
                    selectObj.root = null;
                }
                selectObj.dom = null;
                selectObj.reactVm = null;
            }, 0);
        }
    }
}
