# Default values for chart.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.
replicaCount: 1
image:
  repository: gitlab.example.com/group/project
  tag: stable
  pullPolicy: IfNotPresent
  secrets:
    - name: gitlab-registry
application:
  track: stable
  tier: web
service:
  enabled: true
  name: web
  type: ClusterIP
  url: http://my.host.com/
  externalPort: 5000
  internalPort: 5000
postgresql:
  enabled: true
resources:
  limits:
    cpu: 2
    memory: 8Gi
  requests:
    cpu: 10m
    memory: 32Mi
#  limits:
#    cpu: 100m
#    memory: 128Mi
#  requests:
#    cpu: 100m
#    memory: 128Mi
annotations:
  gitlabApp: ********************
  gitlabEnv: CI_ENVIRONMENT_SLUG
