import { createRoot } from 'react-dom/client';
import * as React from 'react';
import EditorInterface, {IExternalEvent} from './common/external/IExternalInterface';
import { EmrEditor } from './components/editor/Main';

export default class Editor {
  private _editor: any;
  private _options: any;
  private root: any;
  constructor(options: any) {
    // this._options = options || {};
    // // options.bShowMenu = true;
    // // options.bShowToolbar = true;
    // const dom = options.dom;
    // if (dom) {
    //   if (options.height === undefined) {
    //     // tslint:disable-next-line: no-console
    //     console.error('请传入容器高度');
    //   }
    //   const id = options.id || 'text-id-1';
    //   const editor: EmrEditor = ReactDOM.render(
    //     // tslint:disable-next-line: jsx-wrap-multiline
    //     <EmrEditor
    //         id={id}
    //         height={options.height}
    //         isTest={options.isTest}
    //         bShowMenu={options.bShowMenu}
    //         bShowToolbar={options.bShowToolbar}
    //     />,
    //     dom,
    //   ) as any;

    //   this._editor = {
    //     getEditor: () => {
    //       return editor.getEditor();
    //     },
    //     setEvent: (events: IExternalEvent): void => {
    //       editor.setEditEvent(events);
    //     },
    //     removeEvent(sNames?: string[]): boolean {
    //       return editor.removeEvent(sNames);
    //     },
    //   };
    // }
    this._options = options || {};
    const dom = options.dom;
    if (dom) {
      if (options.height === undefined) {
        console.error('请传入容器高度');
        return;
      }

      const id = options.id || 'text-id-1';
      const editorRef = React.createRef<EmrEditor>();

      this.root = createRoot(dom); // 存储根实例
      this.root.render(
        <EmrEditor
          ref={editorRef}
          id={id}
          height={options.height}
          isTest={options.isTest}
          bShowMenu={options.bShowMenu}
          bShowToolbar={options.bShowToolbar}
        />
      );

      const editor = editorRef.current;

      if (editor) {
        this._editor = {
          getEditor: () => {
            return editor.getEditor();
          },
          setEvent: (events: IExternalEvent): void => {
            editor.setEditEvent(events);
          },
          removeEvent(sNames?: string[]): boolean {
            return editor.removeEvent(sNames);
          },
        };
      }
    }
  }

  public getEditor(): EditorInterface {
    const result = this._editor.getEditor();
    return result;
  }

  public setEvent(events: IExternalEvent): any  {
    this._editor.setEvent(events);
    return this;
  }

  public removeEvent(sNames?: string[]): boolean  {
    return this._editor.removeEvent(sNames);
  }
}
