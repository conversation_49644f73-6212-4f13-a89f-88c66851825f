import { getImageEditor } from '@hz-editor/plugins';
import { getImageSelectionName } from 'src/components/editor/module/document/Image';

interface IDocumentCoreAction {
  timestamp?: number;
  isImageEditor?: boolean;
  external?: boolean;
  method: string;
  args: any[];
}

enum StateType {
  Init,
  Recording,
  RecordFinished,
  Replaying,
  ReplayFinished,
  ReplayError,
}

class RecordReplayState {
  private state = StateType.Init;

  public get isInit(): boolean {
    return this.state === StateType.Init;
  }

  public get isRecording(): boolean {
    return this.state === StateType.Recording;
  }

  public get isRecordFinished(): boolean {
    return this.state === StateType.RecordFinished;
  }

  public get isReplaying(): boolean {
    return this.state === StateType.Replaying;
  }

  public get isReplayFinished(): boolean {
    return this.state === StateType.ReplayFinished;
  }

  public startRecord(): void {
    this.state = StateType.Recording;
  }

  public stopRecord(): void {
    this.state = StateType.RecordFinished;
  }

  public startReplay(): void {
    this.state = StateType.Replaying;
  }

  public stopReplay(): void {
    this.state = StateType.ReplayFinished;
  }

  public error(): void {
    this.state = StateType.ReplayError;
  }

}


class DocumentCoreRecorder {

  private readonly ations: IDocumentCoreAction[] = [];

  private state: RecordReplayState;

  constructor(state: RecordReplayState) {
    this.state = state;
  }

  public start(): void {
    this.state.startRecord();
    this.ations.length = 0;
  }

  public stop(): void {
    this.state.stopRecord();
  }

  public isRecording(): boolean {
    return this.state.isRecording;
  }

  public record(action: IDocumentCoreAction): void {
    if(this.state.isRecording) {
      this.ations.push(this.cloneAction(action));
    }
  }

  public exportAtions(): Blob {
    const json = JSON.stringify(this.ations);
    return new Blob([json], {type: 'text/plain'});
  }

  public getAtionsToString(): string {
    return JSON.stringify(this.ations);
  }

  private cloneAction(action: IDocumentCoreAction): IDocumentCoreAction {
    return JSON.parse(JSON.stringify(action)) as IDocumentCoreAction;
  }

}

// tslint:disable-next-line: max-classes-per-file
class DocumentCoreReplayer {

  private documentCore: any;

  private customCallbacks: any = {};

  private actions: IDocumentCoreAction[] = [];

  private playingCallbacks: Function[] = [];

  private finishedCallbacks: Function[] = [];

  private canceledCallbacks: Function[] = [];

  private errorCallbacks: Function[] = [];

  private state: RecordReplayState;

  constructor(state: RecordReplayState) {
    this.state = state;
  }


  public setDocumentCore(documentCore: Object) {
    this.documentCore = documentCore;
  }

  public addCustomMethod(name: string, method: Function): void {
    this.customCallbacks[name] = method;
  }

  public async importActions(file: Blob): Promise<void> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        let json = JSON.parse(reader.result as string);
        let actions = <IDocumentCoreAction[]>json;
        this.actions = actions;
        resolve();
      };
      reader.onabort = reject;
      reader.onerror = reject;
      reader.readAsText(file);
    });
  }

  public importActionsByJson(sJson: string): void {
    let json = JSON.parse(sJson);
    let actions = <IDocumentCoreAction[]>json;
    this.actions = actions;
  }

  public start(): void {
    let { actions , documentCore } = this;
    let thisRef = this;
    this.state.startReplay();
    playFrame();
    async function playFrame(): Promise <void> {
      if(actions.length > 0 && thisRef.state.isReplaying) {
        let action = actions.shift();
        let isFinished = actions.length === 0;
        try {
          if (action.external === true) {
            if (thisRef.customCallbacks[action.method]) {
              await thisRef.customCallbacks[action.method].apply(documentCore, action.args);
            }
          } else {

            if ( 'setImageSelectionInfo' === action.method && action && action.args && 0 < action.args.length ) {
              action.args[0] = getImageSelectionName(action.args[0]);
            //   await thisRef.customCallbacks['handerFresh'].apply(documentCore, action.args);
            }

            // 图片编辑器的回放
            if (action.isImageEditor) {
              let imageEditorReplayer = window['__IMAGE_EDITOR_REPLAYER__'];
              if (!imageEditorReplayer) { // 如果图片编辑器插件未加载，则加载该插件
                await getImageEditor();
                imageEditorReplayer = window['__IMAGE_EDITOR_REPLAYER__'];
              }
              let method: Function = imageEditorReplayer[action.method];
              method.apply(imageEditorReplayer, action.args);

            // emr编辑器的回放
            } else {
              let method: Function = documentCore[action.method];
              await method.apply(documentCore, action.args);
            }
          }
        } catch (error) {
          console.log(error);
          thisRef.emitError(error);
          // return;
        }

        thisRef.emitFramePlay();
        if(isFinished) {
          thisRef.emitFinished();
        }

        // 判断下一帧是否为mousemove事件，是则快速播放帧，否则20ms播放一帧
        // const nextAction = actions[0];
        // if(!!nextAction && nextAction.method === 'mouseButtonMove') {
        //   requestAnimationFrame(()=> playFrame());
        // } else {
        //   setTimeout(() => playFrame(), 0);
        // }
        playFrame();
      }
    }
  }

  public cancel(): void {
    this.emitCanceled();
  }

  public onFramePlay(callback: Function) {
    this.playingCallbacks.push(callback);
  }

  public onCanceled(callback: Function): void {
    this.canceledCallbacks.push(callback);
  }

  public onFinished(callback: Function): void {
    this.finishedCallbacks.push(callback);
  }

  public onError(callback: Function): void {
    this.errorCallbacks.push(callback);
  }

  private emitFramePlay(): void {
    this.playingCallbacks.forEach(ck => ck.apply(null, []));
  }

  private emitCanceled(): void {
    this.canceledCallbacks.forEach(ck => ck.apply(null, []));
    this.state.stopReplay();
    this.clearCallbacks();
  }

  private emitFinished(): void {
    this.finishedCallbacks.forEach(ck => ck.apply(null, []));
    this.state.stopReplay();
    this.clearCallbacks();
  }

  private emitError(error: any): void {
    this.errorCallbacks.forEach((ck) => ck.apply(null, [error]));
    this.state.stopReplay();
    this.clearCallbacks();
  }

  private clearCallbacks(): void {
    this.playingCallbacks = [];
    this.canceledCallbacks = [];
    this.finishedCallbacks = [];
    this.errorCallbacks = [];
    this.customCallbacks = {};
  }

}

const recordreplayState = new RecordReplayState();
const recorder = new DocumentCoreRecorder(recordreplayState);
const replayer = new DocumentCoreReplayer(recordreplayState);

export function getDocumentCoreRecorder(): DocumentCoreRecorder {
  return recorder;
}

export function getDocumentCoreReplayer(): DocumentCoreReplayer {
  return replayer;
}

export function getDocumentCoreRecordReplayState(): RecordReplayState {
  return recordreplayState;
}
