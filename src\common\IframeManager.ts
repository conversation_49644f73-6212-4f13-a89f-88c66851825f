import { getEditorDomContainer } from './commonDefines';

class IframeManager {
    private _iframes: any;
    // private _textNodes: any;
    private _activeId: number;
    private _callbacks: any[];
    private _textNode: any;
    private _uiTextNode: any;
    private _divReader: any;

    private _catchMeasureLog: any[];


    constructor() {
        this._iframes = {};
        // this._textNodes = {};
        this._callbacks = [];
    }

    public deleteCallback(callback: (id: number) => void): void {
        this._callbacks.push(callback);
    }

    public setDocId(docId: number): void {
        this._activeId = docId;
    }

    public getDocId(): number {
        return this._activeId;
    }

    public addIframe(id: number, win: any): void {
        if (!win) {
            return;
        }
        this._iframes[id] = win;
    }

    public deleteIframe(id: number): void {
        delete this._iframes[id];
        // delete this._textNodes[id];
        const callbacks = this._callbacks;
        if (callbacks.length > 0) {
            callbacks.forEach((callback) => callback(id));
        }
    }

    public getWindow(id: number): Window {
        let win: Window;
        const iframe = this._iframes[id];
        if (iframe) {
            win = iframe.contentWindow;
        } else {
            win = window;
        }
        return win;
    }

    public getIframe(id: number): any {
        return this._iframes[id];
    }

    public getTextElement(): any {
        let element: any = this._textNode; // s[this._activeId];
        if (!element) {
            element = this.createTextNode();
        }
        return element;
    }

    public getLabelElement(): any {
        if (!this._uiTextNode) {
            const doc = document;
            const div = doc.createElement('div');
            const lable = doc.createElement('label');
            div.appendChild(lable);
            document.body.appendChild(div);

            this._divReader = div;
            this._uiTextNode = lable;
        }

        return this._uiTextNode;
    }

    public getReaderDiv(): any {
        if (!this._divReader) {
            const doc = document;
            const div = doc.createElement('div');
            const lable = doc.createElement('label');
            div.appendChild(lable);
            document.body.appendChild(div);

            this._divReader = div;
            this._uiTextNode = lable;
        }

        return this._divReader;
    }

    public getCatchMeasureLog(): any[] {
        if (!this._catchMeasureLog) {
            this._catchMeasureLog = [];
        }
        return this._catchMeasureLog;
    }


    private createTextNode(): any {
        const doc = document;
        const measureSVGContainer = doc.createElementNS('http://www.w3.org/2000/svg', 'svg');
        measureSVGContainer.id = 'svg-measure-container';
        measureSVGContainer.setAttribute('style', 'position: absolute; width: 0; height: 0');
        const textElement = doc.createElementNS('http://www.w3.org/2000/svg', 'text');
        const textNode = doc.createTextNode('');
        textElement.appendChild(textNode);
        measureSVGContainer.appendChild(textElement);
        getEditorDomContainer()
            .appendChild(measureSVGContainer);

        this._textNode = textElement;

        return textElement;
    }

    // private createTextNode(): any {
    //     const win = this.getWindow(this._activeId);
    //     if (win === window) {
    //         const values = Object.values(this._textNodes);
    //         if (values.length > 0) {
    //             this._textNodes[this._activeId] = values[0];
    //             return values[0];
    //         }
    //     }
    //     const doc = win.document;
    //     const measureSVGContainer = doc.createElementNS('http://www.w3.org/2000/svg', 'svg');
    //     measureSVGContainer.id = 'svg-measure-container';
    //     measureSVGContainer.setAttribute('style', 'position: absolute; width: 0; height: 0');
    //     const textElement = doc.createElementNS('http://www.w3.org/2000/svg', 'text');
    //     const textNode = doc.createTextNode('');
    //     textElement.appendChild(textNode);
    //     measureSVGContainer.appendChild(textElement);
    //     if (win === window) {
    //         getEditorDomContainer()
    //         .appendChild(measureSVGContainer);
    //     } else {
    //         doc.body.firstChild.appendChild(measureSVGContainer);
    //     }

    //     this._textNodes[this._activeId] = textElement;

    //     return textElement;
    // }
}

export const IFRAME_MANAGER = new IframeManager();
