import { XmlComponent } from '../../../../xml-components';
import { PageMarginAttributes } from './page-margin-attributes';

export class PageMargin extends XmlComponent {
    constructor(top: number, right: number, bottom: number, left: number, header: number,
                footer: number, gutter: number, mirror?: boolean) {
        super('w:pgMar');
        this.root.push(
            new PageMarginAttributes({
                top,
                right,
                bottom,
                left,
                header,
                footer,
                gutter,
                // mirror: mirror,
            }),
        );
    }
}
