import { IDocumentTable } from '../../../model/TableProperty';
import { ICanvasProps } from './common';

interface IProps {
    content: IDocumentTable;
    className?: string;
    scale?: number;
    pageIndex?: number;
    host?: any;
    cellId?: number;
    bFromHeaderFooter?: boolean;
}

export class TableUI {
    private props: IProps;
    private ctx: CanvasRenderingContext2D;
    constructor(props: ICanvasProps) {
        this.props = props.content;
        this.props.pageIndex = props.pageIndex;
        this.ctx = props.ctx;
        this.render(this.props.content.tableBorderLines);
    }

    private render(lines: any[]): void {
        if (!lines || lines.length === 0) {
            return;
        }
        const actLines = this.parseLine(lines);
        const ctx = this.ctx;
        actLines.forEach((line) => {
            if (!line) {
                return;
            }
            ctx.strokeStyle = line.stroke;
            ctx.lineWidth = line.strokeWidth;
            ctx.beginPath();
            ctx.moveTo(line.x1, line.y1);
            ctx.lineTo(line.x2, line.y2);
            ctx.closePath(); // 结束路径
            ctx.stroke();
        });
    }

    private parseLine(tableBorderLines: any[]): any[] {
        let lines = [];
        const rows = {};
        const cols = {};

        tableBorderLines.forEach((line: any) => {
            // 先转换成字符串，后面好比较
            const x1 = line.X1 = line.x1 + '';
            const x2 = line.X2 = line.x2 + '';
            const y1 = line.Y1 = line.y1 + '';
            const y2 = line.Y2 = line.y2 + '';
            line.StrokeWidth = line.strokeWidth + '';

            // 行数据放在一起
            if (y1 === y2) {
                const xx: string = y1 + y2;
                if (rows[xx]) {
                    rows[xx].push(line);
                } else {
                    rows[xx] = [line];
                }
            }

            // 列数据放在一起
            if (x1 === x2) {
                const yy: string = x1 + x2;
                if (cols[yy]) {
                    cols[yy].push(line);
                } else {
                    cols[yy] = [line];
                }
            }
        });
        const rowLines = this.getRowLines(rows);
        const colLines = this.getColLines(cols);
        lines = lines.concat(rowLines);
        lines = lines.concat(colLines);
        return lines;
    }

    private getRowLines(rows: any): any[] {
        const keys = Object.keys(rows);
        if (!keys.length) {
            return;
        }

        const lines = [];
        keys.forEach((key) => {
            const cells = rows[key].sort((a, b) => a.x1 - b.x1);
            // console.log(cells);
            let line: any;
            const length = cells.length - 1;
            cells.forEach((cell, index) => {
                if (index === length) {
                    if (index === 0 && line === undefined) {
                        line = {...cell};
                    }
                    lines.push(line);
                    return;
                }

                if (index === 0) {
                    line = {...cell};
                }

                const nextLine = cells[index + 1];
                // 所有条件相同才会平成 同一条直线
                if (cell.X2 === nextLine.X1 && cell.stroke === nextLine.stroke && cell.StrokeWidth ===
                    nextLine.StrokeWidth) {
                    line.x2 = nextLine.x2;
                } else {
                    lines.push(line);
                    // 重新构建新的直线
                    line = {...nextLine};
                }
            });
        });
        // console.log(lines);
        return lines;
    }

    private getColLines(cols: any): any[] {
        const keys = Object.keys(cols);
        if (!keys.length) {
            return;
        }

        const lines = [];
        keys.forEach((key) => {
            const datas = cols[key].sort((a, b) => a.x1 - b.x1);
            const length = datas.length - 1;
            let line: any;
            datas.forEach((data, index) => {
                if (index === length) {
                    if (index === 0 && line === undefined) {
                        line = {...data};
                    }
                    lines.push(line);
                    return;
                }

                if (index === 0) {
                    line = {...data};
                }

                const nextLine = datas[index + 1];
                if (data.Y2 === nextLine.Y1 && data.stroke === nextLine.stroke && data.StrokeWidth ===
                nextLine.StrokeWidth) {
                    line.y2 = nextLine.y2;
                } else {
                    lines.push(line);
                    // 重新构建新的直线
                    line = {...nextLine};
                }
            });
        });

        return lines;
    }
}
