import {ParaElementType} from './ParagraphContent';
import TextProperty, { TextVertAlign } from '../TextProperty';
import {measure} from '../util';
import {ParaElementBase, RepalceText} from './ParaElementBase';
import {TEXT_SCARE_NUM} from '../../StyleProperty';

/**
 * 文本内容：中英文字符，标点，数字，空格
 */
export default class ParaText extends ParaElementBase {
    public bTextFlag: boolean;

    constructor(text: string) {
        super();
        this.content = text;
        this.bTextFlag = false;
        this.type = ParaElementType.ParaText;

        this.setSpaceAfter(this.canSpaceAfter());
    }

    /**
     * 字符可放在行首
     */
    public canBeAtBeginOfLine(): boolean {
        // if ( /[!%),.:;>?}¢¨°·ˇˉ―‖’”…‰′″›℃∶、。〃〉》」』】〕〗〞︶︺︾﹀﹄﹚﹜﹞！＂％＇），．：；？］｀｜｝～￠\]]/.test(this.content)) {
        if ( -1 !== ('!%),.:;>?}¢¨°·ˇˉ―‖’”…‰′″›℃∶、。〃〉》」』】〕〗〞︶︺︾﹀﹄﹚﹜﹞！＂％＇），．：；？］｀｜｝～￠\]').indexOf(this.content) ) {
            return false;
        }

        return true;
    }

    /**
     * 字符可放在行末
     */
    public canBeAtEndOfLine(): boolean {
        // if ( /[$(\[{£¥·‘“〈《「『【〔〖〝﹙﹛﹝＄（．［｛￡￥]/.test(this.content)) {
        if ( -1 !== ('$(\[{£¥·‘“〈《「『【〔〖〝﹙﹛﹝＄（．［｛￡￥').indexOf(this.content) ) {
            return false;
        }

        return true;
    }

    /**
     * 是否为东亚字符，包括中文标点符号
     */
    public isEastAsianScript(): boolean {
        if (this.isChinese() || this.isChineseSymbol()) {
            return true;
        }

        return false;
    }

    /**
     * 是否为中文字符
     */
    public isChinese(): boolean {
        return (/[\u4e00-\u9fea]/.test(this.content));
    }

    /**
     * 是否为中文标点
     */
    public isChineseSymbol(): boolean {
        return (/[\uff00-\uffef]/.test(this.content) || /[\u3000-\u303f]/.test(this.content));
    }

    /**
     * 文本内容是否是空格
     */
    public isSpace(): boolean {
        // return RepalceText.Space === this.content;
        return (/[\u0020\u00a0\u202F\u0009]/.test(this.content));
    }

    public isEscapeString(): boolean {
        if (/[\b\f\v\0\r\n\t]/.test(this.content)) {
            this.content = '';
            return true;
        }

        return false;
    }

    /**
     * 宽度为0的空格
     */
    public isNBSP(): boolean {
        return String.fromCharCode(0xFEFF) === this.content;
    }

    /**
     * 是否为段落结束符
     */
    public isParaEnd(): boolean {
        return ParaElementType.ParaEnd === this.type;
    }

    public isSpaceAfter(): boolean {
        return this.bTextFlag;
    }

    public setSpaceAfter(bFlag: boolean): void {
        this.bTextFlag = bFlag;
    }

    /**
     * 拷贝函数，默认深拷贝，
     * UI显示拷贝时，只需要拷贝content，widthVisible
     * @param bForUI
     */
    public copy(bForUI: boolean = false): ParaText {
        const paraText = new ParaText(this.content);

        paraText.positionX = this.positionX;
        paraText.positionY = this.positionY;
        paraText.bVisible = this.bVisible;
        paraText.dy = this.dy;
        paraText.type = this.type;
        paraText.bViewSecret = this.bViewSecret;
        if ( true === bForUI) {
            paraText.width = this.width;
            paraText.widthVisible = this.widthVisible;
            paraText.bViewSecret = this.bViewSecret;
            if ( RepalceText.Space === paraText.content ) {
                paraText.content = RepalceText.SpaceForUI;
            }
        }

        return paraText;
    }

    public measure( textPr: TextProperty ): number {
        let text = this.content;
        // 字符对象为空格，则使用“我”替代
        if ( this.isSpace()) {
            if (/[\t]/.test(this.content)) {
                this.content = ' ';
            }
            text = '我';
            this.type = ParaElementType.ParaSpace;
        } else if ( this.isEastAsianScript() ) {
            text = '我';
        } else if (this.isEscapeString()) {
            return 0;
        }

        const m = measure(text, textPr)[0];

        // 字符对象为段落结束符，强制指定其宽度 = 0
        // if ( this.isParaEnd() ) {
        //     m.width = 0;
        // }
        if (!(null == m || ParaElementType.Unkown === m.type)) {
            this.width = m.width;
        } else {
            this.content = '';
            this.width = 0;
            this.widthVisible = this.width;
            return 0;
        }

        // 字符对象为空格，强制指定其宽度 / 2
        if ( this.isSpace()) {
            this.width /= 2;
        }

        const vertAlign = textPr.vertAlign;
        if (this.type === ParaElementType.ParaText && (vertAlign === TextVertAlign.Sub
            || vertAlign === TextVertAlign.Super)) {
            this.width = this.width / 2 * (TEXT_SCARE_NUM + 0.05);
        }

        this.widthVisible = this.width;
        return m.height;
    }

    /**
     * 判断当前字符是中文还是英文或空格，英文返回false，中文返回true
     */
    private canSpaceAfter(): boolean {
        // 连字符
        // if ( /[\u002D]/.test(this.content) || /[\u2014]/.test(this.content) ) {
        if ( RepalceText.Hyphen1 === this.content || RepalceText.Hyphen2 === this.content ) {
            return true;
        }

        // 空格
        if ( this.isSpace() || this.isNBSP() ) {
            return true;
        }

        if ( this.isEastAsianScript() && true === this.canBeAtEndOfLine()) {
            return true;
        }

        return false;
    }
}
