import * as React from 'react';
import {
    GlobalEvent as gEvent,
    GlobalEventName as gEventName,
} from '../../../../common/GlobalEvent';
import { DocumentCore } from '../../../../model/DocumentCore';
import {
    NewControlType,
    ViewModeType,
} from '../../../../common/commonDefines';
import '../../style/newBoxList.less';
import { NewCombox } from './NewCombox';
import { SDKcontainer } from '../SDKcontainer';
import { NewAddressbox } from './NewAddressbox';
import { consoleLog, isGlobalTestData } from '../../../../common/GlobalTest';
import { NewDateBox2 } from '../../NewDateBox2';
import { getPagePadding } from '../../../../common/commonMethods';

interface IDocumentProps {
    host: any;
    pageIndex: number;
}

interface IState {
    bRefresh: boolean;
}

export class NewControlLayer extends React.Component<IDocumentProps, IState> {
    private host: any;
    private documentCore: DocumentCore;
    private isShowComboxList: boolean;
    private isShowDateList: boolean;
    private isShowAddressList: boolean;
    private isShowComboxDropButton: boolean;
    private isShowDateDropButton: boolean;
    private isShowAddressDropButton: boolean;
    private pageIndex: number;
    private bVisible: boolean;
    private id: number;
    private myRef: any;
    private scale: number;
    // private tips: INewControlTips;
    private boxInfo: any;
    private spanRef: any;
    private bFromEnter: boolean; // it means trigger enterEvent(), also tab jump
    private newControlName: string;

    constructor(props: IDocumentProps) {
        super(props);
        this.host = props.host;
        this.documentCore = props.host.documentCore;
        this.myRef = React.createRef();
        this.spanRef = React.createRef();
        this.state = {
            bRefresh: false,
        };
    }

    public render(): any {
        if (this.props.host.bPrint === true) {
            return (
                <div ref={this.myRef}></div>
            );
        }
        this.scale = this.props.host.getScale();
        return (
            <div ref={this.myRef}>
                {this.renderComboxList()}
                {this.renderComboxDropButton()}
                {this.renderDateDropButton()}
                {this.renderDateList()}
                {this.renderAddressList()}
                {/* {this.renderTips()} */}
            </div>
        );
    }

    public componentDidMount(): void {
        gEvent.addEvent(this.host.docId, gEventName.NewControlToShow, this.showNewControlLayer);

        // for closing expanded structs only?
        gEvent.addEvent(this.host.docId, gEventName.Selection, this.showNewControlLayer);

        const dom = this.myRef.current as HTMLElement;
        dom.addEventListener('mousedown', this.preventDefault);
        // dom.addEventListener('mousemove', this.preventDefault);
        dom.addEventListener('mouseup', this.mouseUp);
        document.addEventListener('click', this.docClick);
        // if (iframe) {
        //   iframe.contentDocument.addEventListener('wheel', this.docScroll, false);
        // }
        // console.dir(dom)
        gEvent.addEvent(this.host.docId, gEventName.OpenCompleted, this.close);
        try {
            dom.parentNode.parentNode.parentNode.addEventListener('scroll', this.docScroll, false);
        } catch (error) {
            // todo
        }
    }

    public componentWillUnmount = (): void => {
        // this.close();
        gEvent.deleteEvent(this.host.docId, gEventName.NewControlToShow, this.showNewControlLayer);
        gEvent.deleteEvent(this.host.docId, gEventName.Selection, this.showNewControlLayer);
        const dom = this.myRef.current as HTMLElement;
        if (!dom) {
            console.log('undelete event newControlyaler');
            return;
        }
        dom.removeEventListener('mousedown', this.preventDefault);
        // dom.removeEventListener('mousemove', this.preventDefault);
        dom.removeEventListener('mouseup', this.mouseUp);
        document?.removeEventListener('click', this.docClick);
        gEvent.deleteEvent(this.host.docId, gEventName.OpenCompleted, this.close);
        // if (iframe) {
        //   iframe.contentDocument.removeEventListener('wheel', this.docScroll, false);
        // }

        try {
            dom.parentNode?.parentNode?.parentNode?.removeEventListener('scroll', this.docScroll, false);
        } catch (error) {
            // todo
        }
    }

    public refresh2(): void {
        this.setState({});
    }

    private docClick = () => {
        if (this.bVisible !== true) {
            return;
        }
        if (isGlobalTestData()) {
            consoleLog('window click close');
        }
        this.close();
    }

    private docScroll = (e: any) => {
        if (this.bVisible !== true) {
            return;
        }

        if (this.isShowComboxList === true) {
            const dom: HTMLDivElement = this.myRef.current;
            if (dom.contains(e.target)) {
                return;
            }
        }

        this.close();
    }

    // private renderTips(): any {
    //     if (!this.tips || this.tips.pageIndex !== this.props.pageIndex || !this.tips.content) {
    //         return null;
    //     }
    //     const tips = this.tips;
    //     return (
    //         <div
    //             className='newControl-tips2'
    //             style={{left: tips.left, top: tips.top}}
    //         >
    //             <i/>
    //             <span>{tips.content}</span>
    //         </div>
    //     );
    // }

    // private showTipsLayer = (tips: INewControlTips) => {
    //     this.tips = tips;
    //     this.setState({});
    // }

    private mouseUp = (e: any): void => {
        const target = e.target;
        const className = target.className || '';
        if (className.search(/drop-button(-triangle)?/) > -1) {
            this.handleDropButton(parseInt(target.getAttribute('data-value'), 10));
        // } else if (className === 'newControl-tips' || target.parentNode.className === 'newControl-tips') {
        //     this.selectNewControl();
        }

        e.stopPropagation();
    }

    private renderPlaceholderDom(): any {
        this.boxInfo = undefined;
        const documentCore = this.documentCore;
        const newControlPropety = documentCore.getCursorInNewControlProperty();

        if (!newControlPropety) {
            return null;
        }

        const bounds = documentCore.getNewControlsFocusBounds();
        // 将定位转移到首行
        const fixLine = bounds.bounds[0];
        if (fixLine.pageIndex !== this.props.pageIndex) {
            return null;
        }
        let subHeight = 0;
        let subLeft = 0;
        const viewMode = documentCore.getViewMode();
        if (viewMode === ViewModeType.WebView) {
            subHeight -= getPagePadding(documentCore).top;
        } else if (viewMode === ViewModeType.CompactView) {
            subLeft = getPagePadding(documentCore).left + getPagePadding.PaddingLeft;
        }
        if (this.isShowComboxList) {
            this.newControlName = bounds.newControlName;
        }

        const scale = this.scale;
        let width = fixLine.width;
        const left: number = (fixLine.x - subLeft) * scale;
        const height = fixLine.height;
        const top: number = (fixLine.y + height + subHeight) * scale;
        let minWidth = 150;
        if (newControlPropety.bShowCodeAndValue === true) {
            minWidth = 200;
        }
        width = width < minWidth ? minWidth : width;
        this.boxInfo = {
            property: newControlPropety,
            left,
            width,
            lineHeight: height * scale,
        };

        return (<span className='newcontrol-box-placeholder' ref={this.spanRef} style={{left, top}}/>);
    }

    // private selectNewControl = () => {
    //     const name = this.tips.newControlName;
    //     if (!name) {
    //         return;
    //     }

    //     this.documentCore.selectNewControl(name);
    //     this.host.refresh();
    // }

    private preventDefault = (e: any): void => {
        e.stopPropagation();
    }

    // event: 2: 双击事件
    private showNewControlLayer = (type: NewControlType, pageIndex: number, id: number, bFromEnter?: boolean,
                                   event?: number): void => {
        this.bFromEnter = bFromEnter;
        if (type === undefined) {
            if (this.bVisible === true) {
                this.close(true);
            }
            const curControl = this.documentCore.getCurrentNewControl();
            if (curControl != null && (curControl.isNewDateBox() || curControl.isAmongCombox())) {
                // if from gEventName.Selection, type must be undefined.
                // TODO: would affect entering from gEventName.NewControlToShow and type undefined?
            } else {
                this.id = undefined;
            }

            return;
        }
        // console.log(id);
        if (this.props.pageIndex !== pageIndex) {
            if (this.bVisible === true) {
                this.close(true);
            }
            return;
        }

        switch (type) {
            case NewControlType.ListBox:
            case NewControlType.MultiListBox: {
                if (id === this.id && this.bVisible === true) {
                    this.close();
                    return;
                } else if (this.bVisible === true) {
                    this.close(false);
                }
                this.isShowComboxList = true;
                this.isShowComboxDropButton = true;
                break;
            }
            case NewControlType.MultiCombox:
            case NewControlType.Combox: {
                if (this.id === id && bFromEnter !== true) {
                    if (event === 2) {
                        // this.close(false);
                        this.isShowComboxList = true;
                        this.isShowComboxDropButton = true;
                        break;
                    }
                    if (this.isShowComboxList) {
                        this.close(false);
                    }
                    this.showBtn(type);
                    return;
                }
                this.close(false);
                this.isShowComboxList = true;
                this.isShowComboxDropButton = true;
                break;
            }
            case NewControlType.DateTimeBox: {
                // if (event === 2) {
                // }
                if (isGlobalTestData()) {
                    consoleLog(id, event);
                }
                // if (this.id === id) {
                //     // const prevShowDateList = this.isShowDateList;
                //     if (event === 2) {
                //         this.close(false);
                //         this.isShowDateList = true;
                //         this.isShowDateDropButton = true;
                //         break;
                //     }
                //     if (this.isShowDateList === true) {
                //         this.close(false);
                //     }
                //     this.showBtn(type);
                //     // // if prev state is closed, show up again after mousedown
                //     // if (prevShowDateList === true) {
                //     //     return;
                //     // }
                //     return ;
                // }
                // this.close(false);
                // this.isShowDateList = true;
                // this.isShowDateDropButton = true;
                // break;
                if (id === this.id && this.bVisible === true) {
                    this.close();
                    return;
                } else if (this.bVisible === true) {
                    this.close(false);
                }
                this.isShowDateList = true;
                this.isShowDateDropButton = true;
                break;
            }
            case NewControlType.AddressBox: {
                if (this.id === id) {
                    const prevShowAddressList = this.isShowAddressList;
                    if (this.bVisible === true) {
                        this.close(false);
                    }
                    this.showBtn(type);
                    // if prev state is closed, show up again after mousedown
                    if (prevShowAddressList === true) {
                        return;
                    }
                }
                this.close(false);
                this.isShowAddressList = true;
                this.isShowAddressDropButton = true;
                break;
            }
            default: {
                if (this.bVisible === true) {
                    this.close();
                }
                this.id = id;
                return;
            }
        }
        this.id = id;
        this.bVisible = true;
        this.pageIndex = pageIndex;
        setTimeout(() => {
            this.setState({bRefresh: !this.state.bRefresh}, () => {
                this.setSDKInfo(type);
            });
        }, 5);
    }

    private setSDKInfo(type: NewControlType): any {
        const boxInfo = this.boxInfo;
        if (!boxInfo) {
            return;
        }
        const span = this.spanRef.current;
        if (!span) {
            return;
        }
        const sdk = SDKcontainer.getSDK();
        const position = span.getBoundingClientRect();
        boxInfo.left = position.left;
        boxInfo.top = position.top;
        boxInfo.type = type;
        boxInfo.bFromEnter = this.bFromEnter;
        sdk.showNewControlBox(this.host.docId, boxInfo, this.close);
    }

    private showBtn(type: NewControlType): void {
        switch (type) {
            case NewControlType.ListBox:
            case NewControlType.MultiListBox:
            case NewControlType.MultiCombox:
            case NewControlType.Combox: {
                if (this.isShowComboxList === false && this.isShowComboxDropButton === true) {
                    return;
                }
                this.isShowComboxList = false;
                this.isShowComboxDropButton = true;
                break;
            }
            case NewControlType.DateTimeBox: {
                if (this.isShowDateList === false && this.isShowDateDropButton === true) {
                    return;
                }
                this.isShowDateList = false;
                this.isShowDateDropButton = true;
                break;
            }
            case NewControlType.AddressBox: {
                if (this.isShowAddressList === false && this.isShowAddressDropButton === true) {
                    return;
                }
                this.isShowAddressList = false;
                this.isShowAddressDropButton = true;
                break;
            }
            default:
                if (this.bVisible === true) {
                    this.close();
                }
                return;
        }

        this.bVisible = true;
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private renderComboxList = () => {
        if (true !== this.isShowComboxList) {
            return null;
        }
        if (SDKcontainer.isActiveSDK()) {
            return this.renderPlaceholderDom();
        }
        const documentCore = this.documentCore;
        if ( true === documentCore.isCursorInNewControl() ) {
            const bounds = documentCore.getNewControlsFocusBounds();
            if (bounds && bounds.bounds) {
                this.newControlName = bounds.newControlName;
                const pageIndex: number = this.props.pageIndex;
                const line = bounds.bounds[0]; // [bounds.bounds.length - 1];  定位首行
                if (pageIndex === line.pageIndex) {
                    return (
                        // <div className='new-control-combox-list' style={{top, left}}>
                        <NewCombox
                            lineHeight={line.height * this.scale}
                            documentCore={documentCore}
                            scale={this.scale}
                            host={this.props.host}
                            key={this.id} // critical
                            bFromEnter={this.bFromEnter}
                            newControlPropety={documentCore.getCursorInNewControlProperty()}
                            closeNewComboxList={this.close}
                            refresh={this.refresh}
                        />
                        // </div>
                    );
                }
            }

        }

        return null;
    }

    private refresh = (bClose: boolean = true) => {
        bClose && this.close(false);
        this.host.setCursorVisible(true);
        this.host.refresh();
    }

    private close = (bRefresh: boolean = true, bFrame?: boolean): void => {
        let bChaged: boolean = false;
        if (this.isShowDateList === true) {
            this.isShowDateList = false;
            bChaged = true;
            if (bFrame !== true) {
                const sdk = SDKcontainer.getSDK();
                if (sdk) {
                    sdk.closeBox();
                }
            }
        }
        if (this.isShowComboxList === true) {
            this.isShowComboxList = false;
            bChaged = true;
            if (bFrame !== true) {
                const sdk = SDKcontainer.getSDK();
                if (sdk) {
                    sdk.closeBox();
                }
            }
            if (this.newControlName) {
                const host = this.props.host;
                if (host.externalEvent?.nsoStructItemSelected) {
                    host.externalEvent.nsoStructItemSelected(this.newControlName);
                    this.newControlName = null;
                }
            }
        }
        if (this.isShowAddressList === true) {
            this.isShowAddressList = false;
            bChaged = true;
            if (bFrame !== true) {
                const sdk = SDKcontainer.getSDK();
                if (sdk) {
                    sdk.closeBox();
                }
            }
        }
        if (this.isShowDateDropButton === true) {
            this.isShowDateDropButton = false;
            bChaged = true;
        }
        if (this.isShowComboxDropButton === true) {
            this.isShowComboxDropButton = false;
            bChaged = true;
        }
        if (this.isShowAddressDropButton === true) {
            this.isShowAddressDropButton = false;
            bChaged = true;
        }
        this.bVisible = false;
        if (bChaged === true && bRefresh === true) {
            this.setState({bRefresh: !this.state.bRefresh});
        }
    }

    private renderDateList = () => {
        if (true !== this.isShowDateList) {
            return null;
        }
        if (SDKcontainer.isActiveSDK()) {
            return this.renderPlaceholderDom();
        }
        const documentCore = this.documentCore;
        // console.log(this.isShowDateList, documentCore.isCursorInNewControl())
        if ( true === documentCore.isCursorInNewControl() ) {
            const bounds = documentCore.getNewControlsFocusBounds();
            if ( bounds && bounds.bounds ) {
                const pageIndex: number = this.props.pageIndex;
                const line = bounds.bounds[bounds.bounds.length - 1];
                if (pageIndex === line.pageIndex) {
                    return (
                        <NewDateBox2
                            lineHeight={line.height * this.scale}
                            documentCore={documentCore}
                            newControlPropety={documentCore.getCursorInNewControlProperty()}
                            scale={this.scale}
                            closeNewBoxList={this.close}
                            refresh={this.refresh}
                            host={this.host}
                            bFromEnter={this.bFromEnter}
                            key={this.id} // why not need key? constructor only called once(then handled in willrcvP)
                        />
                    );
                }
            }
        }

        return null;
    }

    private renderAddressList = () => {
        if (true !== this.isShowAddressList) {
            return null;
        }
        if (SDKcontainer.isActiveSDK()) {
            return this.renderPlaceholderDom();
        }
        const documentCore = this.documentCore;
        // console.log(this.isShowDateList, documentCore.isCursorInNewControl())
        if ( true === documentCore.isCursorInNewControl() ) {
            const bounds = documentCore.getNewControlsFocusBounds();
            if ( bounds && bounds.bounds ) {
                const pageIndex: number = this.props.pageIndex;
                const line = bounds.bounds[bounds.bounds.length - 1];
                if (pageIndex === line.pageIndex) {
                    return (
                        <NewAddressbox
                            lineHeight={line.height * this.scale}
                            documentCore={documentCore}
                            newControlPropety={documentCore.getCursorInNewControlProperty()}
                            scale={this.scale}
                            closeNewBoxList={this.close}
                            refresh={this.refresh}
                            key={this.id} // critical
                            bFromEnter={this.bFromEnter}
                            host={this.host}
                        />
                    );
                }
            }
        }

        return null;
    }

    private renderComboxDropButton = () => {
        const documentCore = this.documentCore;
        if (
            true === this.isShowComboxDropButton &&
            true === documentCore.isCursorInNewControl()
        ) {
            const scale = this.scale;
            const bounds = documentCore.getNewControlsFocusBounds();
            const pageIndex: number = this.props.pageIndex;
            if (
                bounds &&
                bounds.bounds &&
                pageIndex === bounds.bounds[bounds.bounds.length - 1].pageIndex
            ) {
                let subHeight = 0;
                let subLeft = 0;
                const viewMode = documentCore.getViewMode();
                if (viewMode === ViewModeType.WebView) {
                    subHeight -= getPagePadding(documentCore).top;
                } else if (viewMode === ViewModeType.CompactView) {
                    subLeft = getPagePadding(documentCore).left + getPagePadding.PaddingLeft;
                }
                const lastLine = bounds.bounds[bounds.bounds.length - 1];
                const left = (lastLine.x + lastLine.width - subLeft) * scale;
                const top = (lastLine.y + /*3 +*/ subHeight) * scale;
                const width = 10;

                return (
                    <div
                        className='new-control-combox-drop-button drop-button'
                        data-value={NewControlType.Combox}
                        style={{ top, left, width, height: lastLine.height * scale }}
                    >
                        <div className='drop-button-triangle' data-value={NewControlType.Combox} />
                    </div>
                    //   <NewComboxDropButton
                    //     documentCore={documentCore}
                    //     newControlPropety={documentCore.getCursorInNewControlProperty()}
                    //     showNewComboxList={this.props.host.state.showNewComboxList}
                    //     closeNewComboxList={this.props.host.state.closeNewComboxList}
                    //     refresh={this.props.handleRefresh}
                    //   />
                    // </div>
                );
            }
        }

        return null;
    }

    private handleDropButton(type: NewControlType): void {
        let bShow: boolean;
        switch (type) {
            case NewControlType.Combox: {
                bShow = this.isShowComboxList = !this.isShowComboxList;
                break;
            }
            case NewControlType.DateTimeBox: {
                bShow = this.isShowDateList = !this.isShowDateList;
                break;
            }
            default:
                return;
        }
        this.setState({bRefresh: !this.state.bRefresh}, () => {
            if (bShow === false) {
                const sdk = SDKcontainer.getSDK();
                if (sdk) {
                    sdk.closeBox();
                }
            } else {
                this.setSDKInfo(type);
            }
        });
    }

    private renderDateDropButton = () => {
        const documentCore = this.documentCore;
        if (
            true === this.isShowDateDropButton &&
            true === documentCore.isCursorInNewControl()
        ) {
            const scale = this.scale;
            const bounds = documentCore.getNewControlsFocusBounds();
            const pageIndex: number = this.props.pageIndex;
            if (
                bounds &&
                bounds.bounds &&
                pageIndex === bounds.bounds[bounds.bounds.length - 1].pageIndex
            ) {
                let subHeight = 0;
                let subLeft = 0;
                const viewMode = documentCore.getViewMode();
                if (viewMode === ViewModeType.WebView) {
                    subHeight -= getPagePadding(documentCore).top;
                } else if (viewMode === ViewModeType.CompactView) {
                    subLeft = getPagePadding(documentCore).left + getPagePadding.PaddingLeft;
                }
                const lastLine = bounds.bounds[bounds.bounds.length - 1];
                const left = (lastLine.x + lastLine.width - subLeft) * scale;
                const top = (lastLine.y + /*3 +*/ subHeight) * scale;
                const width = 10;

                return (
                    <div
                        className='new-control-drop-button'
                        data-value={NewControlType.DateTimeBox}
                        style={{ top, left, width, height: lastLine.height * scale }}
                    >
                        <div className='drop-button-triangle' data-value={NewControlType.DateTimeBox} />
                    </div>
                );
            }
        }

        return null;
    }
}
