@import './global.less';
@import './document.less';

.hz-editor-container .editor-menu {
    position: relative;
    z-index: 3;
    width: 100%;
    padding: 8px 0;
    font-size: @fontSize;
    font-family: @fontFamily;
    color: @color;
    clear: both;
    background-color: #fff;
    .first-menu {
        display: block;
        height: 24px;
        background-color: #fff;
        li {
            float: left;
            width: 56px;
            height:  24px;
            margin-left: 8px;
            line-height: 22px;
            font-size: @fontSize;
            // border: 1px solid @borderColor;
            cursor: pointer;

            &.active, &:hover {
                color: #fff;
                background-color: @activeColor;
                border-radius: 2px;
            }
        }
    }
    .second-menu {
        position: absolute;
        top: 40px;
        left: 0px;
        height: 82px;
        padding: 0 8px;
        background-color: #fff;
        li {
            float: left;

            &.autoFormat {
                position: relative;

                &:hover::after {
                    content: '删除文档末尾的空字符以及空段落';
                    position: absolute;
                    top: 105%;
                    left: 0;
                    border: 1px solid #ACB4C1;
                    display: block;
                    padding: 2px 4px;
                    z-index: 100;
                    font-size: 12px;
                    line-height: 1.5em;
                    color: #737B80;
                    background-color: white;
                    white-space: nowrap;
                }
            }

            &.toggleClose {
                i {
                    color: #ddd;
                } 
            }
            &.toggleOpen {
                i {
                    color: blue;
                }
            }


            &::before {
                content: '';
            }
            div {
                display: flex;
                padding: 0px 12px;
                text-align: center;
                border-radius: 4px;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                cursor: pointer;
                .image-icon {
                    display: block;
                    width: 30px;
                    height: 32px;
                    margin: 6px 0 4px 0;
                    font-style: normal;
                    font-size: 28px;
                    text-align: center;
                    // border: 1px dotted @borderColor;
                    border-radius: 2px;
                }
                span {
                    line-height: 24px;
                    &.node-children {
                        position: relative;
                        padding-right: 12px;
                        &::after {
                            position: absolute;
                            top: 10px;
                            right: 0;
                            content: ' ';
                            width: 0;
                            height: 0;
                            border: 5px solid transparent;
                            border-top: 5px solid #000;
                        }
                    }
                }

                &:hover {
                    background: rgba(115, 123, 128, 0.1);
                }
                &.active {
                    background: rgba(115, 123, 128, 0.2);
                }

                .close {
                    line-height: 68px;
                }
            }

            &.split-line {
                padding-right: 2px;
                margin-right: 2px;
                border-right: 2px solid @borderColor;
            }
            &.disabled {
                div {
                    cursor: not-allowed;
                    &:hover {
                        background: none;
                    }
                    &.active {
                        background: none;
                    }
                }
            }
        }
    }
    .three-menu {
        position: absolute;
        top: 126px;
        width: 124px;
        padding: 2px 0;
        box-shadow: 0px 2px 20px 0px rgba(39, 53, 70, 0.2);
        border-radius: 4px;
        border: 1px solid @borderColor;
        background-color: #fff;
        li {
            position: relative;
            width: 100%;
            height: 22px;
            text-align: left;
            cursor: pointer;
            overflow: hidden;
            label {
                padding-left: 20px;
                line-height: 20px;
                cursor: inherit;
            }
            span.split-line {
                display: block;
                height: 1px;
                margin: 1px 0;
                background: @borderColor;
            }
            &:hover {
                background-color: @activeBgColor;
            }
            &.disabled {
                &:hover {background: none;}
                cursor: not-allowed;
            }
        }
    }

    .checked-menu-flag {
        position: absolute;
        top: 4px;
        left: 4px;
        width: 14px;
        height: 14px;
        &::before, &::after {
            content: ' ';
            pointer-events: none;
            position: absolute;
            color: @activeColor;
            border: 1px solid;
            background-color: @activeColor;
        }

        &::before {
            width: 2px;
            height: 1px;
            left: 25%;
            top: 45%;
            border: none;
            border-bottom: 5px solid @activeColor;
            transform: rotate(-44deg);
        }

        &::after {
            width: 0px;
            height: 9px;
            left: 54%;
            top: 21%;
            border-bottom: 1px solid @activeColor;
            transform: rotate(44deg);
        }
    }
}
