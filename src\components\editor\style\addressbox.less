@import './global.less';
.hz-editor-container .new-address-list {

   position: absolute;
   // max-width: 160px;
   // max-width: 510px; // 170 * 3 max -> still need to consider ratio
   max-height: 192px;
   text-align: left;
   z-index: 1800;
   font-size: @fontSize;
   font-family: @fontFamily;
   color: @color;
   background:#fff;
   box-shadow: 0px 2px 8px 0px rgba(71,74,91,0.12);
   border-radius: 2px;
   // border: 1px solid rgba(221,221,227,1);
   border: 1px solid #DDDDE3;
   @padding: 8px 16px 0px 12px;
   outline: none;
   color: #293750;
   font-size: 14px;

   .address-item-list {
      // max-height: 200px;
      max-height: 160px;
      width: 100%;
      padding-top: 8px;
      padding-bottom: 8px;
      overflow: auto;
      vertical-align: top;

      li {
         // height: 24px;
         height: auto;
         padding: 0px 0 0px 12px;
         line-height: 30px;
         overflow: hidden;
         position: relative;

         &:hover {
               background-color: @activeBgColor;
               cursor: pointer;
         }
         &.active {
               color: @activeColor;
         }

         // &:focus {
         //       outline: none;
         //       background-color: @activeBgColor;
         // }

         &.focusedItem {
            outline: none;
            background-color: @activeBgColor;
            font-weight: bold;
         }

         // & > div {
         //     width: 100%;
         //     height: 100%;
         //     line-height: 0.9;
         // }

         // label {
         //     width: auto !important;
         //     max-width: calc(100% - 26px);
         //     #less.overflow();
         //     white-space: nowrap !important;
         // }
         // .radiobox-item i {

         // }
         i {
               position: absolute !important;
               top: 8px;
         }
         label {
               margin-left: 20px;
               max-width: 106px;
               word-break: break-word;
         }
      }

      .expand-triangle {
         display: inline-block;
         width: 0;
         height: 0;
         border-left: 8px solid darkgray;
         border-top: 4px solid transparent;
         border-bottom: 4px solid transparent;
         position: absolute;
         top: 10px;
         right: 5px;
         // position: relative;
         // top: 40%;
         // left: 1px;
      }
   }

   .list-button {
      display: table;
      height: 32px;
      width: 100%;
      padding: 8px 8px 0px 8px;
      text-align: center;
      // border-top: 1px solid @borderColor;
      border-top: 1px solid #DDDDE3;
      background-color: #F6F7F9;
      color: #54627B;
      label {
          // display: table-cell;
          display: inline-block;
          width: 33%;
          cursor: pointer;
          &:last-child {
              color: @activeColor;
          }
      }
      &.align-left {
          text-align: left;
          label:last-child {
              padding-left: 3px;
              color: @color;
          }
      }
      &.align-left {
          text-align: left;
          label:last-child {
              color: @color;
          }
      }
   }

   .divide-half {
      display: inline-block;
      width: 50%;
   }

   .divide-thrice {
      display: inline-block;
      width: 33%;
   }
}