import * as React from 'react';
import '../style/dialog.less';
import {GlobalEvent as gEvent, GlobalEventName as gEventName} from '../../../common/GlobalEvent';
import {IFRAME_MANAGER} from '../../../common/IframeManager';

interface IDialogProps {
    title: string;
    scale?: boolean;
    bCloseIcon?: boolean; // 右上角删除按钮
    noModal?: boolean; // 没有遮罩层
    width?: number | string;
    height?: number | string;
    top?: number | string;
    left?: number | string;
    id?: number | string;
    visible: boolean;
    className?: string;
    preventDefault?: boolean;
    footer?: any;
    footerHeight?: number;
    beforeOpen?: () => void;
    open?: () => void;
    close?: (id?: number | string | boolean) => void;
    confirm?: (id?: number | string) => void;
    right?: number | string;  // 添加right属性支持
    children?: React.ReactNode; // 添加children属性
}

// interface IDialogStates {
//     bReflash: boolean;
// }
interface IPosition {
    x: number;
    y: number;
    width: number;
    height: number;
}

export default class DialogAI extends React.Component<IDialogProps, {}> {
    private _width: number | string;
    private _height: number | string;
    private _left: number | string;
    private _right: number | string;
    private _top: number | string;
    private _visible: boolean;
    private _className: string = '';
    private _bMove: boolean;
    private _bStart: boolean;
    private _subLeft: number;
    private _subTop: number;
    private _sourceLeft: number;
    private _sourceTop: number;
    private _sourceRight: number | string;
    private _ref: any;
    private _dialogBox: HTMLDivElement;
    private _maxHeight: number;
    private _movePosition: IPosition;
    private _noModalClass: string = 'no-modal';
    private _docId: number;
    private _bChangePosition: boolean;

    constructor(props: any) {
        super(props);
        this._docId = gEvent.getActiveDocId();
        let width: number | string = props.width;
        let height: number | string = props.height;
        const left: number | string = props.left;
        const right: number | string = props.right; 
        
         // 初始化尺寸
        this._width = props.width || 500;
        this._height = props.height || '';
        
        // 初始化位置
        this._top = props.top;
        
        // 优先处理right
        if (props.right !== undefined) {
            this._right = props.right;
            this._left = 'auto';
        } else {
            this._right = undefined;
            this._left = props.left;
        }

        this._ref = React.createRef();
        this._movePosition = {} as any;
    }

    // public componentDimMount(): void {
    //     if (this.props.visible === true) {
    //         this.props.open();
    //         this._visible = true;
    //     }
    // }

    public componentDidMount(): void {
        this.initDialog();
        // this._ref.current.addEventListener('keydown', this.keyDown);
    }

    public componentWillUnmount(): void {
        const dom = this._ref.current;
        if (!dom) {
            console.log('undelete event dialog');
            return;
        }
        dom.removeEventListener('mousedown', this.mousedown);
        // dom.removeEventListener('click', this.click);
        dom.removeEventListener('mousemove', this.mousemove);
        const doc = dom.ownerDocument;
        if (doc) {
            doc.removeEventListener('mouseup', this.docMouseUp);
        }
        // dom.removeEventListener('keydown', this.keyDown);
    }

    public componentWillReceiveProps(nextProps: any): void {
        const visible = nextProps.visible;
        // 添加width更新处理
        if (nextProps.width !== this._width) {
            this._width = nextProps.width;
            if (this._dialogBox) {
                this._dialogBox.style.width = this.getCell(this._width);
            }
        }
        if (visible === true && visible !== this._visible) {
            const docId = gEvent.getActiveDocId();
            this._bChangePosition = this._docId !== docId;
            this._docId = docId;
        }
        this.init(visible);
    }

    public setWidth(width: number): void {
        this._width = width;
    }

    public render(): any {
        const props = this.props;
        const bodyContent = this.props.children;

        const style = {
            width: null,
            height: null,
        };

        style.width = this.getCell(this._width);
        style.height = this.getCell(this._height);
        let className = 'editor-dialog';
        if (props.className) {
            className += ' ' + props.className;
        }
        if (this._bStart !== true) {
            className += ' opacity0';
        } else if (this.props.visible) {
            className += ' visible';
        }
        if (!props.footer) {
            className += ' no-footer';
        }
        if (props.noModal === true) {
            className += ' ' + this._noModalClass;
        }

        let close: any;
        if (props.bCloseIcon === true) {
            close = (<div className={'dialog-box-close'}>×</div>);
        }

        return (
            <div
                className={className}
                ref={this._ref}
            >
                <div className={'dialog-box'} style={style}>
                    <div className={'dialog-box-header'}>{this.props.title}</div>
                    {close}
                    <div className={'dialog-box-body'}>
                        {bodyContent}
                    </div>
                    <div className='dialog-box-place' />
                    {this.renderFooter()}
                    {this.renderScaleButton()}
                </div>
            </div>
        );
    }

    private renderScaleButton(): any {
        if (this.props.scale !== true) {
            return null;
        }

        return (<div className='dialog-box-scale'/>);
    }

    private keyDown = (e: any) => {
        const target = e.target;
        if (target.tagName !== 'INPUT') {
            e.preventDefault();
            e.stopPropagation();
        }
    }

    private renderFooter(): any {
        if (!this.props.footer) {
            return null;
        }

        return (
            <div className={'dialog-box-footer'}>
                <div className={'dialog-box-footer-btns'}>
                    {this.props.footer}
                </div>
            </div>
        );
    }

    private scaleMounseDown(e: any): void {
        const className = this._className || '';
    
        if (this.props.scale !== true || typeof className !== 'string' || className.indexOf('dialog-box-scale') === -1) {
            return;
        }
        
        this._bMove = true;
        this.removeModalClass();
        const dom = this._dialogBox;
        const width = dom.clientWidth;
        const height = dom.clientHeight;
        this._movePosition = {
            x: e.clientX,
            y: e.clientY,
            width,
            height,
        };
    }

    private scaleMounseMove(e: any): void {
        const className = this._className || '';
    
    if (typeof className !== 'string' || className.indexOf('dialog-box-scale') === -1) {
        return;
    }
    
    const dom = this._dialogBox;
    const oldPosition = this._movePosition;
    let width = oldPosition.width;
    let height = oldPosition.height;
    const x = e.clientX;
    const y = e.clientY;
    const subWidth = x - oldPosition.x;
    const subHeight = y - oldPosition.y;
    width += subWidth;
    height += subHeight;
    if (width < 200 || height < 100) {
        return;
    }
    dom.style.width = width + 'px';
    dom.style.height = height + 'px';
    }

    // private scaleMouseUp(e: any): void {
    //     if (this.props.scale) {
    //         this.addModalClass();
    //     }
    // }

    private addModalClass(): void {
        if (!this.props.noModal === true) {
            return;
        }
        const dialogDom = this._ref.current;
        const className = ' ' + this._noModalClass;
        const dialogClassName = dialogDom.className;
        if (dialogClassName.indexOf(className) === -1) {
            dialogDom.className = dialogClassName + className;
        }
    }

    private removeModalClass(): void {
        if (this.props.noModal === true) {
            const dialogDom = this._ref.current;
            const className = ' ' + this._noModalClass;
            const dialogClassName = dialogDom.className;
            if (dialogClassName.indexOf(className) > -1) {
                dialogDom.className = dialogClassName.replace(className, '');
            }
        }
    }

    private init(visible: boolean): void {
        if (visible === this._visible) {
            return;
        }
        this._visible = visible;
        if (visible === true) {
            if (this._bChangePosition === true) {
                this.initPosition();
                this.reset();
            }
            if (this.props.beforeOpen) {
                this.props.beforeOpen();
            }

            if (typeof this.props.open !== 'function') {
                return;
            }
            
            setTimeout(() => {
                this.props.open();
            }, 0);
        } else if (visible === false) {
            this.reset();
        }
    }

    private reset(): void {
        if (this._sourceRight !== undefined) {
            this._right = this._sourceRight;
            this._left = 'auto';
        } else {
            this._left = this._sourceLeft;
        }
        this._top = this._sourceTop;
        this.setPosition();
    }

    private initDialog(): void {
        const dom = this._ref.current;
        // console.dir(this);
        const doc = dom.ownerDocument;
        const props = this.props;
        dom.addEventListener('mousedown', this.mousedown);
        // dom.addEventListener('click', this.click);
        dom.addEventListener('mousemove', this.mousemove);
        doc.addEventListener('mouseup', this.docMouseUp);
        // if (this.props.scale === true) {
        //     doc.addEventListener('mousemove', this.scaleMounseMove);
        // }
        if (props.visible === true) {
            this.init(true);
        }
        this._bStart = true;
        this.setHeight();
        this.setPosition();
        if (props.visible === true) {
            this.setState({});
        }
    }

    private setHeight(): void {
        const dom = this._ref.current;
        // console.dir(this);
        const doc = dom.ownerDocument;
        const props = this.props;
        const body = doc.firstElementChild;
        const bodyHeight = body.clientHeight;
        this.initPosition();
        let subHeight: number = 81;
        if (props.footer) {
            subHeight += props.footerHeight || 75;
        }
        this._maxHeight = bodyHeight - this._sourceTop - subHeight;
        this.setMaxHeight();
    }

    private initPosition(): void {
        const dom = this._ref.current;
        const doc = dom.ownerDocument;
        const body = doc.firstElementChild;
        const iframe = IFRAME_MANAGER.getIframe(this._docId);
        let iframeDoc;
        if (iframe) {
            iframeDoc = iframe.contentWindow.document;
        }
        const dialogBox = this._dialogBox = dom.firstChild;
        const height = dialogBox.clientHeight;
        const bodyHeight = body.clientHeight;
        let bodyWidth = body.clientWidth;
        const props = this.props;
        let sumLeft: number = 0;
        if (iframeDoc && iframeDoc !== doc) {
            bodyWidth = iframeDoc.firstElementChild.clientWidth;
            const position = iframe.getBoundingClientRect();
            sumLeft = position.left;
        }
    
        // 处理top
        if (this._top === undefined) {
            if (props.top === undefined) {
                this._top = (bodyHeight - height) / 2;
                if (this._top < 0) {
                    this._top = 20;
                }
            } else if (typeof props.top === 'string') {
                if (props.top.indexOf('%') > -1) {
                    this._top = parseInt(props.top, 10) * bodyHeight / 100 || 20;
                } else {
                    this._top = parseInt(props.top, 10) || 20;
                }
            } else if (typeof props.top === 'number') {
                this._top = props.top;
            }
        }
    
        // 优先处理right属性
        if (props.right !== undefined) {
            this._right = props.right;
            this._left = 'auto';
            // 立即设置位置，确保right生效
            if (this._dialogBox) {
                this._dialogBox.style.right = this.getCell(this._right);
                this._dialogBox.style.left = 'auto';
            }
        } else {
            this._right = undefined;  // 清除right值
            // 处理left
            if (typeof props.left === 'string') {
                if (props.left.indexOf('%') > -1) {
                    this._left = parseInt(props.left, 10) * bodyWidth / 100 || 0;
                } else {
                    this._left = parseInt(props.left, 10) || 0;
                }
            } else if (this._left === undefined) {
                this._left = (bodyWidth - (this._width as number)) / 2 + sumLeft;
            }
        }
    
        this._sourceLeft = this._left as number;
        this._sourceTop = this._top as number;
        this._sourceRight = this._right;
    }

    private setMaxHeight(): void {
        (this._dialogBox.querySelector('.dialog-box-body') as HTMLElement).style.maxHeight = this._maxHeight + 'px';
    }

    private setPosition(): void {
        const dom = this._dialogBox;
        if (!dom) return;  // 增加空值检查
    
        // 重置样式
        dom.style.left = '';
        dom.style.right = '';
    
        if (this._right !== undefined) {
            dom.style.right = this.getCell(this._right);
            dom.style.left = 'auto';
        } else {
            dom.style.right = 'auto';
            dom.style.left = this.getCell(this._left);
        }
        dom.style.top = this.getCell(this._top);
    }

    // private setPosition(): void {
    //     todo
    // }

    private getCell(val: number | string): string {
        if (typeof val === 'number') {
            return val + 'px';
        }

        return val;
    }

    // private _close = (e?: any): void => {
    //     this.props.close(this.props.id);
    //     this.reset();
    // }

    // private _confirm = (e: any): void => {
    //     this.props.confirm(this.props.id);
    // }

    // private click = (e: any): void => {
    //     const target = e.target;
    //     if (target.className === 'dialog-box-cancel-btn') {
    //         this._close(e);
    //     } else if (target.className === 'dialog-box-confirm-btn') {
    //         this._confirm(e);
    //     }
    // }

    private mousedown = (e: any): void => {
        const target = e.target;
        // 确保className是字符串类型
        const className = this._className = (target.className || '').toString();
        
        // 使用字符串方法前先确保className是字符串
        if (typeof className === 'string') {
            if (className.indexOf('dialog-box-header') > -1) {
                this.removeModalClass();
                this._bMove = true;
                this._subLeft = e.clientX - (this._left as number);
                this._subTop = e.clientY - (this._top as number);
            } else if (className.indexOf('dialog-box-close') > -1) {
                if (this.props.close) {
                    this.props.close(this.props.id);
                }
            } else {
                this.scaleMounseDown(e);
            }
        }
    
        if (this.props.preventDefault === false) {
            return;
        }
    }

    // private mouseup = (e: any): void => {
    //     this._bMove = true;
    // }

    private mousemove = (e: any): void => {
        if (this._bMove !== true) {
            return;
        }

        if (this._className.indexOf('dialog-box-header') > -1) {
            const x = e.clientX;
            const y = e.clientY;
            let left = this._left as number;
            let top = this._top as number;
            left = x - this._subLeft;
            top = y - this._subTop;
            this._left = left;
            this._top = top;
            this.setPosition();
            e.stopPropagation();
        } else {
            this.scaleMounseMove(e);
        }
    }

    private docMouseUp = (e: any): void => {
        if (this._bMove === false) {
            return;
        }
        this._bMove = false;
        this.addModalClass();
    }
}
