import { XmlComponent, XmlAttributeComponent } from '../../xml-components';
// tslint:disable-next-line: max-line-length
import { Placeholder, IsMustFill, DeleteProtect, ShowBorder, BorderString, EditProtect, CopyProtect, BackgroundColorHidden,
  EditReverse, HelpTip, SecretType, FixedLength, MaxLength, MinValue, MaxValue, Precision, Unit, Retrieve,
  SelectPrefixContent, PrefixContent, Separator, DateType, Title, CustomDateFormat, TabJump, Checked,
  PrintSelected,
  ShowRight,
  Label,
  NewControlHidden,
  ShowType,
  SpaceNum,
  SerialNumber,
  StartDate,
  EndDate,
  SignatureCount,
  PreText,
  SignatureSeparator,
  PostText,
  SignaturePlaceholder,
  SignatureRatio,
  RowHeightRestriction,
  ShowPlaceholder,
  ShowValue,
  LogicEvent,
  TextBorder,
  Hierarchy,
  Province,
  City,
  County,
  Group,
  ShowCodeAndValue,
  ShowSignBorder,
  AlwaysShow,
  SignType,
  DateTime,
  HideHasTitle, Identifier, LabelCode, Alignments, EventInfo, ForceValidate, CodeLabel, ValueLabel, SupportMultLines} from './contentControlElements';
import { CustomProperty } from './customProperty';
import { ICustomProps, CodeValueItem, INewControlProperty, NewControlType } from '../../../../../common/commonDefines';
import { ListItems } from './listItems';
import { RunProperties } from '../run/properties';
import { SdtContent } from './sdtContent';
import { customEncodeURIComponent } from '../../../../../common/commonMethods';
import { WriteStruct } from '@/common/struct/write';

export interface IContentControlAttributesProperties {
  name: string;
  type?: number;
}

export class ContentControlAttributes extends XmlAttributeComponent<IContentControlAttributesProperties> {
  protected xmlKeys: any = {
    name: 'name',
    type: 'type',
  };
}

export class ContentControlStart extends XmlComponent {

  private readonly customProperty: CustomProperty;
  private readonly listItems: ListItems;
  private readonly textProperty: RunProperties;

  constructor(attrs: IContentControlAttributesProperties, textProp: RunProperties) {
    super('sdtStart');
    this.root.push(new ContentControlAttributes(attrs));

    this.customProperty = new CustomProperty();
    this.listItems = new ListItems();
    this.textProperty = ( null != textProp ? textProp : new RunProperties() );
    this.root.push(this.textProperty);
  }

  public addSerialNumber(text: string): ContentControlStart {
    this.root.push(new SerialNumber(text));
    return this;
  }

  public addIdentifier(text: string): ContentControlStart {
    this.root.push(new Identifier(text));
    return this;
  }

  public addPlaceHolder(text: string): ContentControlStart {
    this.root.push(new Placeholder(text));
    return this;
  }

  public addHelpTip(text: string): ContentControlStart {
    this.root.push(new HelpTip(text));
    return this;
  }

  public addIsMustFill(flag: string): ContentControlStart {
    this.root.push(new IsMustFill(flag));
    return this;
  }
  public addDeleteProtect(flag: string): ContentControlStart {
    this.root.push(new DeleteProtect(flag));
    return this;
  }
  public addEditProtect(flag: string): ContentControlStart {
    this.root.push(new EditProtect(flag));
    return this;
  }
  public addTextBorder(flag: string): ContentControlStart {
    this.root.push(new TextBorder(flag));
    return this;
  }
  public addCopyProtect(flag: string): ContentControlStart {
    this.root.push(new CopyProtect(flag));
    return this;
  }
  public addLogicEvent(props: string): ContentControlStart {
    this.root.push(new LogicEvent(props));
    return this;
  }
  public addShowBorder(flag: string): ContentControlStart {
    this.root.push(new ShowBorder(flag));
    return this;
  }
  public addBorderString(borderString: string): ContentControlStart {
    this.root.push(new BorderString(borderString));
    return this;
  }
  public addEditReverse(flag: string): ContentControlStart {
    this.root.push(new EditReverse(flag));
    return this;
  }
  public addBackgroundColorHidden(flag: string): ContentControlStart {
    this.root.push(new BackgroundColorHidden(flag));
    return this;
  }
  public addCustomProperty(properties: Map<string, ICustomProps>): ContentControlStart {
    this.root.push(this.customProperty);

    // add customControlElements as well
    this.customProperty.addCustomProperties(properties);
    return this;
  }
  public addTabJump(flag: string): ContentControlStart {
    this.root.push(new TabJump(flag));
    return this;
  }
  public addNewControlHidden(flag: string): ContentControlStart {
    this.root.push(new NewControlHidden(flag));
    return this;
  }
  public addShowPlaceholder(flag: string): ContentControlStart {
    this.root.push(new ShowPlaceholder(flag));
    return this;
  }

  // text struct
  public addSecretType(flag: string): ContentControlStart {
    this.root.push(new SecretType(flag));
    return this;
  }
  public addFixedLength(text: string): ContentControlStart {
    this.root.push(new FixedLength(text));
    return this;
  }
  public addMaxLength(text: string): ContentControlStart {
    this.root.push(new MaxLength(text));
    return this;
  }
  public addTitle(text: string): ContentControlStart {
    this.root.push(new Title(text));
    return this;
  }
  public addHideHasTitle(text: string): ContentControlStart {
    this.root.push(new HideHasTitle(text));
    return this;
  }

  // value struct
  public addMinValue(text: string): ContentControlStart {
    this.root.push(new MinValue(text));
    return this;
  }
  public addMaxValue(text: string): ContentControlStart {
    this.root.push(new MaxValue(text));
    return this;
  }
  public addPrecision(text: string): ContentControlStart {
    this.root.push(new Precision(text));
    return this;
  }
  public addUnit(text: string): ContentControlStart {
    this.root.push(new Unit(text));
    return this;
  }

  public addForceValidate(text: string): ContentControlStart {
    this.root.push(new ForceValidate(text));
    return this;
  }

  // multi struct
  public addRetrieve(flag: string): ContentControlStart {
    this.root.push(new Retrieve(flag));
    return this;
  }
  public addSelectPrefixContent(text: string): ContentControlStart {
    this.root.push(new SelectPrefixContent(text));
    return this;
  }
  public addPrefixContent(text: string): ContentControlStart {
    this.root.push(new PrefixContent(text));
    return this;
  }
  public addSeparator(text: string): ContentControlStart {
    this.root.push(new Separator(text));
    return this;
  }

  public addItemList(itemList: CodeValueItem[]): ContentControlStart {
    this.root.push(this.listItems);

    // add list items
    this.listItems.addListItems(itemList);
    return this;
  }

  // datebox struct
  public addDateBoxFormat(flag: string): ContentControlStart {
    this.root.push(new DateType(flag));
    return this;
  }

  public addCustomFormat(flag: string): ContentControlStart {
    this.root.push(new CustomDateFormat(flag));
    return this;
  }

  public addStartDate(flag: string): ContentControlStart {
    this.root.push(new StartDate(flag));
    return this;
  }

  public addEndDate(flag: string): ContentControlStart {
    this.root.push(new EndDate(flag));
    return this;
  }

  public addShowRight(flag: string): ContentControlStart {
    this.root.push(new ShowRight(flag));
    return this;
  }

  public addChecked(flag: string): ContentControlStart {
    this.root.push(new Checked(flag));
    return this;
  }

  public addPrintSelected(flag: string): ContentControlStart {
    this.root.push(new PrintSelected(flag));
    return this;
  }

  public addLabel(text: string): ContentControlStart {
    this.root.push(new Label(text));
    return this;
  }

  public addLabelCode(text: string): ContentControlStart {
    this.root.push(new LabelCode(text));
    return this;
  }

  public addGroup(text: string): ContentControlStart {
    this.root.push(new Group(text));
    return this;
  }

  // radiobutton
  public addShowType(flag: string): ContentControlStart {
    this.root.push(new ShowType(flag));
    return this;
  }

  public addSpaceNum(text: string): ContentControlStart {
    this.root.push(new SpaceNum(text));
    return this;
  }

  public addSupportMultLines(text: string): ContentControlStart {
    this.root.push(new SupportMultLines(text));
    return this;
  }

  // signature box
  public addSignatureCount(flag: string): ContentControlStart {
    this.root.push(new SignatureCount(flag));
    return this;
  }

  public addPreText(text: string): ContentControlStart {
    this.root.push(new PreText(text));
    return this;
  }

  public addSignatureSeparator(text: string): ContentControlStart {
    this.root.push(new SignatureSeparator(text));
    return this;
  }

  public addPostText(text: string): ContentControlStart {
    this.root.push(new PostText(text));
    return this;
  }

  public addSignaturePlaceholder(text: string): ContentControlStart {
    this.root.push(new SignaturePlaceholder(text));
    return this;
  }

  public addSignatureRatio(text: string): ContentControlStart {
    this.root.push(new SignatureRatio(text));
    return this;
  }

  public addRowHeightRestriction(flag: string): ContentControlStart {
    this.root.push(new RowHeightRestriction(flag));
    return this;
  }

  public addShowValue(text: string): ContentControlStart {
    this.root.push(new ShowValue(text));
    return this;
  }

}

// tslint:disable-next-line: max-classes-per-file
export class ContentControlEnd extends XmlComponent {
  private readonly textProperty: RunProperties;

  constructor(attrs: IContentControlAttributesProperties, textProp: RunProperties) {
    super('sdtEnd');
    this.root.push(new ContentControlAttributes(attrs));
    this.textProperty = ( null != textProp ? textProp : new RunProperties() );
    this.root.push(this.textProperty);
  }

  public addShowBorder(flag: string): ContentControlEnd {
    this.root.push(new ShowBorder(flag));
    return this;
  }
  public addBorderString(borderString: string): ContentControlEnd {
    this.root.push(new BorderString(borderString));
    return this;
  }
}

// tslint:disable-next-line: max-classes-per-file
export class Sdt extends XmlComponent {
  private readonly customProperty: CustomProperty;
  private readonly listItems: ListItems;
  private readonly textProperty: RunProperties;
  private readonly sdtContent: SdtContent;

  constructor(attrs: IContentControlAttributesProperties, textProp: RunProperties) {
    super('sdt');
    this.root.push(new ContentControlAttributes(attrs));

    this.customProperty = new CustomProperty();
    this.listItems = new ListItems();
    this.textProperty = ( null != textProp ? textProp : new RunProperties() );
    this.sdtContent = new SdtContent('w:sdtcontent');
    this.root.push(this.textProperty);
  }

  public addSerialNumber(text: string): Sdt {
    this.root.push(new SerialNumber(text));
    return this;
  }

  public addIdentifier(text: string): Sdt {
    this.root.push(new Identifier(text));
    return this;
  }

  public addPlaceHolder(text: string): Sdt {
    this.root.push(new Placeholder(text));
    return this;
  }

  public addCodeLabel(text: string): Sdt {
    this.root.push(new CodeLabel(text));
    return this;
  }

  public addValueLabel(text: string): Sdt {
    this.root.push(new ValueLabel(text));
    return this;
  }

  public addHelpTip(text: string): Sdt {
    this.root.push(new HelpTip(text));
    return this;
  }

  public addIsMustFill(flag: string): Sdt {
    this.root.push(new IsMustFill(flag));
    return this;
  }
  public addDeleteProtect(flag: string): Sdt {
    this.root.push(new DeleteProtect(flag));
    return this;
  }
  public addShowCodeAndValue(flag: string): Sdt {
    this.root.push(new ShowCodeAndValue(flag));
    return this;
  }
  public addEditProtect(flag: string): Sdt {
    this.root.push(new EditProtect(flag));
    return this;
  }
  public addTextBorder(flag: string): Sdt {
    this.root.push(new TextBorder(flag));
    return this;
  }
  public addCopyProtect(flag: string): Sdt {
    this.root.push(new CopyProtect(flag));
    return this;
  }
  public addLogicEvent(props: string): Sdt {
    this.root.push(new LogicEvent(props));
    return this;
  }
  public addShowBorder(flag: string): Sdt {
    this.root.push(new ShowBorder(flag));
    return this;
  }
  public addBorderString(borderString: string): Sdt {
    this.root.push(new BorderString(borderString));
    return this;
  }
  public addEditReverse(flag: string): Sdt {
    this.root.push(new EditReverse(flag));
    return this;
  }
  public addBackgroundColorHidden(flag: string): Sdt {
    this.root.push(new BackgroundColorHidden(flag));
    return this;
  }
  public addCustomProperty(properties: Map<string, ICustomProps>): Sdt {
    this.root.push(this.customProperty);

    // add customControlElements as well
    this.customProperty.addCustomProperties(properties);
    return this;
  }
  public addSdtContent(): Sdt {
    this.root.push(this.sdtContent);
    return this;
  }
  public getSdtContent(): SdtContent {
    return this.sdtContent;
  }
  public addTabJump(flag: string): Sdt {
    this.root.push(new TabJump(flag));
    return this;
  }
  public addNewControlHidden(flag: string): Sdt {
    this.root.push(new NewControlHidden(flag));
    return this;
  }
  public addShowPlaceholder(flag: string): Sdt {
    this.root.push(new ShowPlaceholder(flag));
    return this;
  }

  // text struct
  public addSecretType(flag: string): Sdt {
    this.root.push(new SecretType(flag));
    return this;
  }
  public addFixedLength(text: string): Sdt {
    this.root.push(new FixedLength(text));
    return this;
  }
  public addMaxLength(text: string): Sdt {
    this.root.push(new MaxLength(text));
    return this;
  }
  public addTitle(text: string): Sdt {
    this.root.push(new Title(text));
    return this;
  }
  public addHideHasTitle(text: string): Sdt {
    this.root.push(new HideHasTitle(text));
    return this;
  }

  public addAlignments(text: string): Sdt {
    this.root.push(new Alignments(text));
    return this;
  }

  public addEventInfo(text: string): Sdt {
    this.root.push(new EventInfo(text));
    return this;
  }

  // value struct
  public addMinValue(text: string): Sdt {
    this.root.push(new MinValue(text));
    return this;
  }
  public addMaxValue(text: string): Sdt {
    this.root.push(new MaxValue(text));
    return this;
  }
  public addPrecision(text: string): Sdt {
    this.root.push(new Precision(text));
    return this;
  }
  public addUnit(text: string): Sdt {
    this.root.push(new Unit(text));
    return this;
  }
  public addForceValidate(text: string): Sdt {
    this.root.push(new ForceValidate(text));
    return this;
  }

  // multi struct
  public addRetrieve(flag: string): Sdt {
    this.root.push(new Retrieve(flag));
    return this;
  }
  public addSelectPrefixContent(text: string): Sdt {
    this.root.push(new SelectPrefixContent(text));
    return this;
  }
  public addPrefixContent(text: string): Sdt {
    this.root.push(new PrefixContent(text));
    return this;
  }
  public addSeparator(text: string): Sdt {
    this.root.push(new Separator(text));
    return this;
  }

  public addItemList(itemList: CodeValueItem[]): Sdt {
    this.root.push(this.listItems);

    // add list items
    this.listItems.addListItems(itemList);
    return this;
  }

  // datebox struct
  public addDateBoxFormat(flag: string): Sdt {
    this.root.push(new DateType(flag));
    return this;
  }

  public addCustomFormat(flag: string): Sdt {
    this.root.push(new CustomDateFormat(flag));
    return this;
  }

  public addStartDate(flag: string): Sdt {
    this.root.push(new StartDate(flag));
    return this;
  }

  public addEndDate(flag: string): Sdt {
    this.root.push(new EndDate(flag));
    return this;
  }

  public addDateTime(flag: string): Sdt {
    this.root.push(new DateTime(flag));
    return this;
  }

  public addShowRight(flag: string): Sdt {
    this.root.push(new ShowRight(flag));
    return this;
  }

  public addChecked(flag: string): Sdt {
    this.root.push(new Checked(flag));
    return this;
  }

  public addPrintSelected(flag: string): Sdt {
    this.root.push(new PrintSelected(flag));
    return this;
  }

  public addLabel(text: string): Sdt {
    this.root.push(new Label(text));
    return this;
  }

  public addLabelCode(text: string): Sdt {
    this.root.push(new LabelCode(text));
    return this;
  }

  public addGroup(text: string): Sdt {
    this.root.push(new Group(text));
    return this;
  }

  // radiobutton
  public addShowType(flag: string): Sdt {
    this.root.push(new ShowType(flag));
    return this;
  }

  public addSpaceNum(text: string): Sdt {
    this.root.push(new SpaceNum(text));
    return this;
  }

  public addSupportMultLines(text: string): Sdt {
    this.root.push(new SupportMultLines(text));
    return this;
  }

  // signature box
  public addSignatureCount(flag: string): Sdt {
    this.root.push(new SignatureCount(flag));
    return this;
  }

  public addPreText(text: string): Sdt {
    this.root.push(new PreText(text));
    return this;
  }

  public addSignatureSeparator(text: string): Sdt {
    this.root.push(new SignatureSeparator(customEncodeURIComponent(text)));
    return this;
  }

  public addPostText(text: string): Sdt {
    this.root.push(new PostText(text));
    return this;
  }

  public addSignaturePlaceholder(text: string): Sdt {
    this.root.push(new SignaturePlaceholder(text));
    return this;
  }

  public addSignatureRatio(text: string): Sdt {
    this.root.push(new SignatureRatio(text));
    return this;
  }

  public addRowHeightRestriction(flag: string): Sdt {
    this.root.push(new RowHeightRestriction(flag));
    return this;
  }

  public addShowValue(text: string): Sdt {
    this.root.push(new ShowValue(text));
    return this;
  }

  public addHierarchy(text: string): Sdt {
    this.root.push(new Hierarchy(text));
    return this;
  }

  public addProvince(text: string): Sdt {
    this.root.push(new Province(text));
    return this;
  }

  public addCity(text: string): Sdt {
    this.root.push(new City(text));
    return this;
  }

  public addCounty(text: string): Sdt {
    this.root.push(new County(text));
    return this;
  }

  public addSignType(text: string): Sdt {
    this.root.push(new SignType(text));
    return this;
  }

  public addAlwaysShow(text: string): Sdt {
    this.root.push(new AlwaysShow(text));
    return this;
  }

  public addShowSignBorder(text: string): Sdt {
    this.root.push(new ShowSignBorder(text));
    return this;
  }

}

// tslint:disable-next-line: max-classes-per-file
export class SectionStart extends XmlComponent {
  private readonly customProperty: CustomProperty;
  private readonly textProperty: RunProperties;

  constructor(attrs: IContentControlAttributesProperties, textProp: RunProperties) {
    super('sectionStart');
    this.root.push(new ContentControlAttributes(attrs));

    this.customProperty = new CustomProperty();
    this.textProperty = ( null != textProp ? textProp : new RunProperties() );
    this.root.push(this.textProperty);
  }

  public addSerialNumber(text: string): SectionStart {
    this.root.push(new SerialNumber(text));
    return this;
  }

  public addIdentifier(text: string): SectionStart {
    this.root.push(new Identifier(text));
    return this;
  }

  public addPlaceHolder(text: string): SectionStart {
    this.root.push(new Placeholder(text));
    return this;
  }

  public addHelpTip(text: string): SectionStart {
    this.root.push(new HelpTip(text));
    return this;
  }

  public addIsMustFill(flag: string): SectionStart {
    this.root.push(new IsMustFill(flag));
    return this;
  }
  public addDeleteProtect(flag: string): SectionStart {
    this.root.push(new DeleteProtect(flag));
    return this;
  }
  public addEditProtect(flag: string): SectionStart {
    this.root.push(new EditProtect(flag));
    return this;
  }
  public addTextBorder(flag: string): SectionStart {
    this.root.push(new TextBorder(flag));
    return this;
  }
  public addCopyProtect(flag: string): SectionStart {
    this.root.push(new CopyProtect(flag));
    return this;
  }
  public addShowBorder(flag: string): SectionStart {
    this.root.push(new ShowBorder(flag));
    return this;
  }
  public addBorderString(borderString: string): SectionStart {
    this.root.push(new BorderString(borderString));
    return this;
  }
  public addEditReverse(flag: string): SectionStart {
    this.root.push(new EditReverse(flag));
    return this;
  }
  public addBackgroundColorHidden(flag: string): SectionStart {
    this.root.push(new BackgroundColorHidden(flag));
    return this;
  }
  public addCustomProperty(properties: Map<string, ICustomProps>): SectionStart {
    this.root.push(this.customProperty);

    // add customControlElements as well
    this.customProperty.addCustomProperties(properties);
    return this;
  }
  public addTabJump(flag: string): SectionStart {
    this.root.push(new TabJump(flag));
    return this;
  }
  public addNewControlHidden(flag: string): SectionStart {
    this.root.push(new NewControlHidden(flag));
    return this;
  }
  public addShowPlaceholder(flag: string): SectionStart {
    this.root.push(new ShowPlaceholder(flag));
    return this;
  }

  // text struct
  public addSecretType(flag: string): SectionStart {
    this.root.push(new SecretType(flag));
    return this;
  }
  public addFixedLength(text: string): SectionStart {
    this.root.push(new FixedLength(text));
    return this;
  }
  public addMaxLength(text: string): SectionStart {
    this.root.push(new MaxLength(text));
    return this;
  }
  public addTitle(text: string): SectionStart {
    this.root.push(new Title(text));
    return this;
  }

}

// tslint:disable-next-line: max-classes-per-file
export class SectionEnd extends XmlComponent {
  private readonly textProperty: RunProperties;

  constructor(attrs: IContentControlAttributesProperties, textProp: RunProperties) {
    super('sectionEnd');
    this.root.push(new ContentControlAttributes(attrs));
    this.textProperty = ( null != textProp ? textProp : new RunProperties() );
    this.root.push(this.textProperty);
  }

  public addShowBorder(flag: string): SectionEnd {
    this.root.push(new ShowBorder(flag));
    return this;
  }
  public addBorderString(borderString: string): SectionEnd {
    this.root.push(new BorderString(borderString));
    return this;
  }
}

// tslint:disable-next-line: max-classes-per-file
export class Section extends XmlComponent {
  private readonly customProperty: CustomProperty;
  private readonly textProperty: RunProperties;
  private readonly secContent: SdtContent;

  constructor(attrs: IContentControlAttributesProperties, textProp: RunProperties) {
    super('section');
    this.root.push(new ContentControlAttributes(attrs));

    this.customProperty = new CustomProperty();
    this.textProperty = ( null != textProp ? textProp : new RunProperties() );
    this.secContent = new SdtContent('w:secontent');
    this.root.push(this.textProperty);
  }

  public addSerialNumber(text: string): Section {
    this.root.push(new SerialNumber(text));
    return this;
  }

  public addIdentifier(text: string): Section {
    this.root.push(new Identifier(text));
    return this;
  }

  public addPlaceHolder(text: string): Section {
    this.root.push(new Placeholder(text));
    return this;
  }

  public addHelpTip(text: string): Section {
    this.root.push(new HelpTip(text));
    return this;
  }

  public addIsMustFill(flag: string): Section {
    this.root.push(new IsMustFill(flag));
    return this;
  }
  public addDeleteProtect(flag: string): Section {
    this.root.push(new DeleteProtect(flag));
    return this;
  }
  public addEditProtect(flag: string): Section {
    this.root.push(new EditProtect(flag));
    return this;
  }
  // public addTextBorder(flag: string): Section {
  //   this.root.push(new TextBorder(flag));
  //   return this;
  // }
  public addCopyProtect(flag: string): Section {
    this.root.push(new CopyProtect(flag));
    return this;
  }
  public addShowBorder(flag: string): Section {
    this.root.push(new ShowBorder(flag));
    return this;
  }
  // public addBorderString(borderString: string): Section {
  //   this.root.push(new BorderString(borderString));
  //   return this;
  // }
  public addEditReverse(flag: string): Section {
    this.root.push(new EditReverse(flag));
    return this;
  }
  public addBackgroundColorHidden(flag: string): Section {
    this.root.push(new BackgroundColorHidden(flag));
    return this;
  }
  public addCustomProperty(properties: Map<string, ICustomProps>): Section {
    this.root.push(this.customProperty);

    // add customControlElements as well
    this.customProperty.addCustomProperties(properties);
    return this;
  }
  // public addTabJump(flag: string): Section {
  //   this.root.push(new TabJump(flag));
  //   return this;
  // }
  public addNewControlHidden(flag: string): Section {
    this.root.push(new NewControlHidden(flag));
    return this;
  }
  public addShowPlaceholder(flag: string): Section {
    this.root.push(new ShowPlaceholder(flag));
    return this;
  }

  // text struct
  // public addSecretType(flag: string): Section {
  //   this.root.push(new SecretType(flag));
  //   return this;
  // }
  // public addFixedLength(text: string): Section {
  //   this.root.push(new FixedLength(text));
  //   return this;
  // }
  // public addMaxLength(text: string): Section {
  //   this.root.push(new MaxLength(text));
  //   return this;
  // }
  public addTitle(text: string): Section {
    this.root.push(new Title(text));
    return this;
  }

  public addSectionContent(): Section {
    this.root.push(this.secContent);
    return this;
  }

  public getSectionContent(): SdtContent {
    return this.secContent;
  }

  public addSdt2(attrs: IContentControlAttributesProperties, portionProp: RunProperties,
                 contentControlElementVals: INewControlProperty): Sdt | Section {
    const sdt = (NewControlType.Section !== contentControlElementVals.newControlType ?
                    new Sdt(attrs, portionProp) : new Section(attrs, portionProp));
    const struct = new WriteStruct(contentControlElementVals, sdt);

    if (sdt instanceof Sdt) {
      sdt.addSdtContent();
    } else {
      sdt.addSectionContent();
    }

    this.secContent.addSdtContent(sdt);
    return sdt;
  }

}
