import Toolbar from './Toolbar';
import '../style/common.less';
import Menu from './Menu';
import RightMenu from './RightMenu';
import Modal from './modals';
import * as React from 'react';
import { EmrEditor } from '../Main';
import { SDKcontainer } from './SDKcontainer';
import { AsyncLoad } from './AsyncLoad';
import {GlobalEvent as gEvent, GlobalEventName as gEventName} from '../../../common/GlobalEvent';

interface IProps {
    host: EmrEditor;
    bHideMenu?: boolean;
    bHideToolbar?: boolean;
    bIframe?: boolean;
    isResetMenuDatas?: () => boolean;
}
interface IState {
    bRefresh: boolean;
}

const components = {
    searchUI: () => {
        return import('./modals/Search');
    }
};

export default class PageContainer extends React.Component<IProps, IState> {
    private host: EmrEditor;
    private _bRightMenu: boolean;
    private _rightOption: any;
    private searchVisible: boolean;
    private searchRef: any;

    constructor(props: IProps) {
        super(props);
        this.host = props.host;
        this.state = {
            bRefresh: false,
        };
        this.searchRef = React.createRef();
    }

    public render(): any {
        // const host = this.props.host;
        let className = 'editor-page-header';
        if (this.props.bHideToolbar === true) {
            className += ' no-toolbar';
        }

        if (this.props.bHideMenu === true) {
            className += ' no-menu';
        }

        return (
            <div className={className}>
                {this.renderMenu()}
                {this.renderToolbar()}
                {this.renderRightMenu()}
                {this.renderModal()}
                {this.renderSearchUI()}
            </div>
        );
    }

    public componentDidMount(): void {
        const bPrint = (this.host as any).bPrint;
        gEvent.addEvent(this.host.docId, gEventName.Search, this.search);
        if (bPrint) {
            return;
        }
        setTimeout(() => {
            if (this.props.bIframe === true) {
                const id = this.host.docId;
                const sdk = SDKcontainer.init(id, this.host);
                sdk.addRightMenu(id, RightMenu);
            } else {
                this._bRightMenu = true;
                this.refresh();
            }
        }, 200);
    }
    public componentWillUnmount(): void {
        gEvent.deleteEvent(this.host.docId, gEventName.Search, this.search);
    }

    public refresh(): void {
        this.setState({ bRefresh: !this.state.bRefresh});
    }

    private renderToolbar(): any {
        if (this.props.bHideToolbar === true) {
            return null;
        }

        return (<Toolbar host={this.host} />);
    }

    private renderModal(): any {
        if (this.props.bIframe === true) {
            return null;
        }

        return (
            <Modal host={this.host} />
        );
    }

    private renderMenu(): any {
        if (this.props.bHideMenu === true) {
            return null;
        }

        return (<Menu host={this.host} isResetMenuDatas={this.props.isResetMenuDatas}/>);
    }

    private renderRightMenu(): any {
        if (this._bRightMenu === undefined) {
            return null;
        }

        return (<RightMenu host={this.host}  />);
    }

    private renderSearchUI(): any {
        if (this.searchVisible === undefined) {
            return null;
        }
        const props = {close: this.closeSearch, documentCore: this.host.state.documentCore,
            host: this.host, refresh: this.refreshSearch, ref: this.searchRef};
        return (
        <AsyncLoad
            host={this}
            name='searchVisible'
            component={components.searchUI}
            props={props}
        />
        );
    }

    private search = (option: any): void => {
        if (option && option.visible !== undefined) {
            if (option.visible === this.searchVisible || this.searchVisible === undefined) {
                return;
            }
            this.searchRef.current?.close();
            return;
        }
        this.searchVisible = true;
        this.setState({});
    }

    private closeSearch = (id: string, bRefresh?: boolean): void => {
        this.searchVisible = false;
        this.refreshSearch(bRefresh);
    }

    private refreshSearch = (bRefresh: boolean): void => {
        if (bRefresh) {
            this.host.handleRefresh();
        }
    }

    // private init(): void {
    //     const listDom = this.host['myRef'].current.querySelector('.ReactVirtualized__Grid');
    //     this._rightOption = {
    //         dom: listDom,
    //     };
    //     listDom.addEventListener('contextmenu', this.contextmenu, false);
    // }

    // private contextmenu = (e: any) => {
    //     if (this._rightOption && this._rightOption.event) {
    //         return;
    //     }

    //     this._rightOption.event = e;
    //     this._bRightMenu = true;
    //     this.setState({bRefresh: !this.state.bRefresh});
    //     setTimeout(() => {
    //         this._rightOption.dom.removeEventListener('contextmenu', this.contextmenu);
    //         this._rightOption = null;
    //     }, 50);
    // }
}
