interface IRetrieveResult {
    result: string;
    bSuccess: boolean;
}
class Retrieve {
    private _datas: Map<string, string>;

    public async search(query: string, keyword: string, value?: string): Promise<IRetrieveResult> {
        if (!query || !keyword) {
            return {result: '', bSuccess: false};
        }

        if (query.length < keyword.length) {
            if (!value || value.length < keyword.length) {
                return {result: '', bSuccess: false};
            }
        }

        // const reg = new RegExp(keyword, 'i');
        // let bSuccess: boolean = query.search(reg) !== -1;
        // if (bSuccess) {
        //     return {result: query, bSuccess};
        // }
        let bSuccess: boolean;
        await this._init();
        // keyword = keyword.replace(/[\u4E00-\u9FA5]/g, (str) => {
        //     return this._getFirstWordToPY(str);
        // });

        // let result: string = query.replace(/[\u4E00-\u9FA5]/g, (str) => {
        //     return this._getFirstWordToPY(str);
        // });

        // bSuccess = result.search(reg) > -1;

        bSuccess = this._search(query, keyword);
        if (!bSuccess && value && typeof value === 'string') {
            // result = value.replace(/[\u4E00-\u9FA5]/g, (str) => {
            //     return this._getFirstWordToPY(str);
            // });
            bSuccess = this._search(value, keyword);
        }

        return {result: undefined, bSuccess};
    }

    /**
     * 中文拼音混合搜索
     * @param query 被搜索的字段
     * @param keyword 搜索的字段
     * @returns true/false
     */
    private _search(query: string, keyword: string): boolean {
        // const reg = /[\u4E00-\u9FA5]/;
        const reg2 = /[a-z]/i;
        let count = -1;
        query = query.toLowerCase();
        const len2 = query.length;
        const len = keyword.length;
        let queryStr: string;
        for (let index = 0; index < len; index++) {
            let word = keyword[index];
            let actionIndex: number;
            let queryWord: string;
            // 字母搜索
            if (reg2.test(word)) {
                word = word.toLowerCase();
                if (queryStr === undefined) {
                    queryStr = query.replace(/[\u4E00-\u9FA5]/g, (str) => {
                        return this._getFirstWordToPY(str);
                    });
                }

                if (count === -1) {
                    actionIndex = queryStr.indexOf(word);
                    if (actionIndex === -1) {
                        actionIndex = query.indexOf(word);
                        if (actionIndex === -1) {
                            return false;
                        }
                        // 当搜索的字段大于被搜索的字段时直接失败
                        // if (len2 - actionIndex < len - index) {
                        //     return false;
                        // }
                        // count = actionIndex + 1;
                        // continue;
                    }
                    // 当搜索的字段大于被搜索的字段时直接失败
                    if (len2 - actionIndex < len - index) {
                        return false;
                    }
                    count = actionIndex + 1;
                    continue;
                } else {
                    queryWord = queryStr[count];
                    if (queryWord !== word) {
                        queryWord = query[count];
                    }
                    count++;
                }
            } else {
                if (count === -1) {
                    actionIndex = query.indexOf(word);
                    if (actionIndex === -1) {
                        return false;
                    }
                    // 当搜索的字段大于被搜索的字段时直接失败
                    if (len2 - actionIndex < len - index) {
                        return false;
                    }
                    count = actionIndex + 1;
                    continue;
                }
                queryWord = query[count++];
            }

            if (queryWord !== word) {
                return false;
            }
        }

        return true;
    }

    private _getFirstWordToPY(word: string): string {
        return this._datas.get(word) || word;
    }

    private async _init(): Promise<void> {
        if (this._datas) {
            return;
        }
        const obj  = await import('./resources/gbk');
        const words = obj.default;
        const datas = this._datas = new Map();
        words.forEach((word) => {
            if (word.Y) {
                datas.set(String.fromCharCode(+word.U), word.Y);
            }
        });
    }
}

export default new Retrieve();
