import { DocumentCore } from '../../model/DocumentCore';
import { EmrEditor } from '../../components/editor/Main';
import { ClearHeaderFooterType, isValidBlob, ResultType } from '../commonDefines';
import { ExternalAction } from './ExternalAction';
import { SectionProperty } from '../../model/core/SectionProperty';
import Paragraph from '../../model/core/Paragraph';
import { NewControlText } from '../../model/core/NewControl/NewControlText';
import { ParaElementType } from '../../model/core/Paragraph/ParagraphContent';
import { NewControl } from '../../model/core/NewControl/NewControl';
import { DocumentContent } from '../../model/core/DocumentContent';
import { TableCell } from '../../model/core/Table/TableCell';
import { Table } from '../../model/core/Table';
import { Reader } from '../../format/reader/reader';
import ParaPortion from '../../model/core/Paragraph/ParaPortion';
export default class HeaderFooter extends ExternalAction {
    private _documentCore: DocumentCore;
    private _host: EmrEditor;
    constructor(host: EmrEditor) {
        super(host.state.documentCore);
        this._host = host;
        this._documentCore = host.state.documentCore;
    }

    /**
     * 设置页眉页脚是否只读
     * @param bReadOnly 是否只读
     */
    public setHeaderFooterReadOnly(bReadOnly: boolean): number {
        if (typeof bReadOnly !== 'boolean') {
            return ResultType.ParamError;
        }

        const res = this._documentCore.setProtectHeaderFooter(bReadOnly);
        if (res === ResultType.Success) {
            this._host.handleRefresh();
        }

        return res;
    }

    public hasHeader(): boolean {
        return this._documentCore.getShowHeader();
    }

    /**
     * 用传递进来的文件页眉替换当前文件的页眉。
     * @param fileContent 传递进来的文件内容
     * @param sRev 预览参数（暂时无用）
     */
    public async replaceHeader(content: Blob, sRev: string): Promise<number> {
        if (!content || isValidBlob(content) === false) {
            this.resetAdminMode();
            return ResultType.ParamError;
        }

        const reader = new Reader(this._documentCore.getDocument());
        let res;
        try {
            res = await reader.replaceHeader(content);
        } catch (error) {
            this.resetAdminMode();
            return ResultType.Failure;
        }

        if (res === ResultType.Success) {
            this._host.handleRefresh();
        }

        return res;
    }

    /**
     * 用传递进来的文件页脚替换当前文件的页脚。
     * @param fileContent 传递进来的文件内容
     * @param sRev 预览参数（暂时无用）
     */
    public async replaceFooter(content: Blob, sRev: string): Promise<number> {
        if (!content || isValidBlob(content) === false) {
            this.resetAdminMode();
            return ResultType.ParamError;
        }

        const reader = new Reader(this._documentCore.getDocument());
        let res;
        try {
            res = await reader.replaceFooter(content);
        } catch (error) {
            this.resetAdminMode();
            return ResultType.Failure;
        }

        if (res === ResultType.Success) {
            this._host.handleRefresh();
        }

        return res;
    }

    /**
     * 获取页眉的文本内容
     * @returns 页眉的文本内容，如果没有页眉返回空字符串
     */
    public getHeaderText(): string {
        if (!this._documentCore.getShowHeader()) {
            return ResultType.StringEmpty;
        }

        const document = this._documentCore.getDocument();
        if (!document || !document.sectionProperty || !document.sectionProperty.headerDefault) {
            return ResultType.StringEmpty;
        }

        // 获取页眉的内容
        let headerText = '';
        try {
            // 使用 DocumentCore 的 getAllText 方法获取文本
            const headerContent = document.sectionProperty.headerDefault.content;
            if (headerContent && headerContent.content) {
                // 遵循文档结构，先遍历所有段落
                for (let i = 0; i < headerContent.content.length; i++) {
                    const para = headerContent.content[i];
                    if (para && para instanceof Paragraph) {
                        // 遍历段落中的所有文本部分
                        for (let j = 0; j < para.content.length; j++) {
                            const portion = para.content[j];
                            if (portion && portion instanceof ParaPortion) {
                                // 获取文本内容
                                headerText += portion.getText();
                            }
                        }
                        if (i < headerContent.content.length - 1) {
                            headerText += '\n';
                        }
                    }
                }
            }
        } catch (error) {
            console.error('Error getting header text:', error);
        }

        return headerText || ResultType.StringEmpty;
    }

    /**
     * 获取页脚的文本内容
     * @returns 页脚的文本内容，如果没有页脚返回空字符串
     */
    public getFooterText(): string {
        if (!this._documentCore.getShowFooter()) {
            return ResultType.StringEmpty;
        }

        const document = this._documentCore.getDocument();
        if (!document || !document.sectionProperty || !document.sectionProperty.footerDefault) {
            return ResultType.StringEmpty;
        }

        // 获取页脚的内容
        let footerText = '';
        try {
            // 获取页脚的内容
            const footerContent = document.sectionProperty.footerDefault.content;
            if (footerContent && footerContent.content) {
                // 遍历所有段落
                for (let i = 0; i < footerContent.content.length; i++) {
                    const para = footerContent.content[i];
                    if (para && para instanceof Paragraph) {
                        // 遍历段落中的所有文本部分
                        for (let j = 0; j < para.content.length; j++) {
                            const portion = para.content[j];
                            if (portion && portion instanceof ParaPortion) {
                                // 获取文本内容
                                footerText += portion.getText();
                            }
                        }
                        if (i < footerContent.content.length - 1) {
                            footerText += '\n';
                        }
                    }
                }
            }
        } catch (error) {
            console.error('Error getting footer text:', error);
        }

        return footerText || ResultType.StringEmpty;
    }

    public deleteHeader(): number {
        const res = this._documentCore.deleteHeader(true);
        if (res === ResultType.Success) {
            this._host.handleRefresh();
        }

        return res;
    }

    /**
     * 删除当前文档的页眉内容
     */
    public deleteHeaderContent(sRev?: string): number {
        const res = this._documentCore.clearHeaderFooterContent(ClearHeaderFooterType.Header);
        if (res === ResultType.Success) {
            this._host.handleRefresh();
        }

        return res;
    }

    /**
     * 删除当前文档的页脚内容
     */
     public deleteFooterContent(sRev?: string): number {
        const res = this._documentCore.clearHeaderFooterContent(ClearHeaderFooterType.Footer);
        if (res === ResultType.Success) {
            this._host.handleRefresh();
        }

        return res;
    }

    public hasFooter(): boolean {
        return this._documentCore.getShowFooter();
    }

    public deleteFooter(): number {
        const res = this._documentCore.deleteFooter(true);
        if (res === ResultType.Success) {
            this._host.handleRefresh();
        }

        return res;
    }

    /**
     * 设置页脚的文本内容，如果没有页脚不会自动插入页脚
     * @param strText 页脚内容 支持通配符$ 用来代表页码
     * @param nParaStyle 页脚内容的对齐方式
     * @param sRev1 预留参数
     */
    public setFooterTextEx(strText: string, nParaStyle: number, sRev1?: string): number {
        if (strText == null || ![1, 2, 3, 4].includes(nParaStyle)) {
            return ResultType.ParamError;
        }

        const res = this._documentCore.addPageNumByText(strText, nParaStyle);
        if (res === ResultType.Success) {
            this._host.handleRefresh();
        }

        return res;
    }

    /**
     * 光标跳到当前页的页眉开始位置。
     */
    public jumpToHeader(): number {
        const res = this._documentCore.jumpToHeader();
        if (res === ResultType.Success) {
            this._host.handleRefresh();
        }
        return res;
    }

    public setHeadersTextByJson(sJson: string, sRev1: string): number {
        if ( null == sJson || '' === sJson ) {
            return ResultType.ParamError;
        }

        const jsonText = JSON.parse(sJson);

        // console.log(sJson)
        const pageIndexes = [];
        // header of such pages that will be changed
        for (const pageIndex in jsonText) {
            if (pageIndex) {
                // console.log(pageIndex)
                pageIndexes.push(+pageIndex);
            }
        }
        // console.log(pageIndexes)

        const logicDocument = this._documentCore.getDocument();
        const sectionsInfo = logicDocument.sectionsInfo;

        // get 'index' from pageindex of json
        const pages = logicDocument.pages;
        // the new controls in page 0 default header
        let oldNewControls = [];
        const theFirstHeader = logicDocument.sectionProperty.headerDefault;
        if (theFirstHeader != null) {
            // all structs of original header
            oldNewControls = theFirstHeader.getContent()
                .getAllNewControls();
        }

        for (const pageIndex of pageIndexes) {
            // page 1 -> 0, 2 -> 1 ...
            const curPage = pages[pageIndex - 1]; // documentPage
            // the 'index'
            if (curPage != null) {

                if (pageIndex > 1) {
                    // not page 0 or even < 0
                    const theIndex = curPage.pos;

                    let infoIndex = sectionsInfo.getIndex2(theIndex, pageIndex - 1);
                    const curSecPro = sectionsInfo.elements[infoIndex];
                    if (pageIndex - 1 !== curSecPro?.id) {
                        sectionsInfo.add( new SectionProperty(logicDocument), theIndex, pageIndex - 1);
                        logicDocument.createSectionHeaderFromDefaultHeader(pageIndex - 1);
                        infoIndex = sectionsInfo.getIndex2(theIndex, pageIndex - 1);
                        const newControlManager = logicDocument.getNewControlManager();

                        // get headerfooter instance, must be the last one
                        const theSectProperty = sectionsInfo.elements[infoIndex].sectProperty;
                        const defaultPageProps = logicDocument.sectionProperty.getPageProperty();
                        // set page margins
                        const pageMargins = (defaultPageProps != null) ? defaultPageProps['margins'] : null;
                        // console.log(pageMargins)
                        if (pageMargins != null) {
                            theSectProperty.setPageMargins(pageMargins.left, pageMargins.top,
                                pageMargins.right, pageMargins.bottom);
                        }
                        if (theSectProperty != null) {
                            // the new header
                            const theHeader = theSectProperty.headerDefault;
                            const allNewControls = oldNewControls;
                            // console.log(allNewControls); // they must all be repetitive controls at the moment
                            if (allNewControls.length > 0) {
                                // deal with 2 issues:
                                // 1. outside: structs' ui are copied, thus need to remove first,
                                // then 'newcontrol.addtoparagraph' later
                                // 2. inside: structs are not added to map & array

                                // get the struct index that would be inserted afterwards
                                const prevPage = pages[pageIndex - 1 - 1];
                                let prevPageLastPara = null;
                                if (prevPage != null) {
                                    const prevPageLastParaIndex = prevPage.endPos;
                                    prevPageLastPara = logicDocument.content[prevPageLastParaIndex];
                                }

                                let lastNewControlIndex = -1;
                                if (prevPageLastPara instanceof Paragraph) {
                                    prevPageLastPara.setApplyToAll(true); // temporary
                                    const curContentPos =  prevPageLastPara.getCurContentPosInDoc();
                                    prevPageLastPara.setApplyToAll(false); // temporary
                                    const lastNewControl = newControlManager.getPosNewControl(curContentPos, true);
                                    // console.log(lastNewControl)

                                    if (lastNewControl != null) {
                                        const lastNewControlName = lastNewControl.getNewControlName();
                                        // get index of this control in newControls
                                        const newControls = newControlManager.getAllNewControls();
                                        for (let i = 0, len = newControls.length; i < len; i++) {
                                            if (newControls[i].getNewControlName() === lastNewControlName) {
                                                lastNewControlIndex = i;
                                                break;
                                            }
                                        }
                                        // console.log(lastNewControlIndex);
                                    }
                                }

                                // new controls array in sectProperty
                                const renewedNewControls = [];
                                for (let j = 0, len = allNewControls.length; j < len; j++) {
                                    const headerNewControl = allNewControls[j];
                                    // create a newcontrol that just have diff name
                                    if (headerNewControl instanceof NewControlText) {

                                        // outside
                                        // get index of para which contains several controls
                                        const shadowPara = headerNewControl.getStartBorderInParagraph();
                                        if (shadowPara == null) {
                                            // tslint:disable-next-line: no-console
                                            console.warn('startPortion\'s para is null');
                                            return ResultType.Failure;
                                        }
                                        const documentContent = shadowPara.parent;
                                        let para = null;
                                        if (documentContent instanceof DocumentContent) {
                                            if (documentContent.isTableCellContent() === true) {
                                                // table
                                                const tableCell = documentContent.getParent();
                                                if (tableCell instanceof TableCell) {
                                                    // get paraindex, cellindex and rowindex
                                                    const paraIndex = shadowPara.index;
                                                    const cellIndex = tableCell.getIndex();
                                                    const tableRow = tableCell.getRow();
                                                    const rowIndex = tableRow.getIndex();
                                                    const theTable = tableRow.getTable();
                                                    const tableIndex = theTable.index;
                                                    // console.log(paraIndex, cellIndex, rowIndex, tableIndex)

                                                    // retrieve the para that contains control
                                                    const currentTable = theHeader.content.content[tableIndex];
                                                    if (currentTable instanceof Table) {
                                                        // tslint:disable-next-line: max-line-length
                                                        para = currentTable.content[rowIndex].content[cellIndex].getContent()[paraIndex];
                                                    }
                                                }
                                            } else if (documentContent.isHeaderFooter(false) === true) {
                                                // normal
                                                const paraIndex = shadowPara.index;
                                                para = theHeader.content.content[paraIndex];
                                            }
                                        }

                                        const textProperty = headerNewControl.getProperty();
                                        // tslint:disable-next-line: max-line-length
                                        const textUniqueName = newControlManager.makeUniqueName(textProperty.newControlType, textProperty.newControlName);
                                        // console.log(textUniqueName)
                                        // tslint:disable-next-line: max-line-length
                                        // critical!
                                        const newDocumentParent = para.parent;
                                        // tslint:disable-next-line: max-line-length
                                        const newControlText = new NewControlText(newDocumentParent, textUniqueName, textProperty);
                                        const newControlNames = newControlManager.getNameMap();
                                        // console.log(newControlText)
                                        // console.log(lastNewControlIndex)

                                        // inside
                                        if (lastNewControlIndex !== -1) {
                                            // array
                                            const newControls = newControlManager.getAllNewControls();
                                            newControls.splice(lastNewControlIndex + 1 + j, 0, newControlText);

                                            // map
                                            newControlNames.set(textUniqueName, newControlText);
                                            // console.log(newControls)
                                            // console.log(newControlNames)
                                        }

                                        // outside cont
                                        if (para instanceof Paragraph) {
                                            let preservedPortionProp = null;
                                            // delete existing wrong portions
                                            // add the new start/end portions that created when instantializing structs
                                            const oldName = textProperty.newControlName;
                                            // console.log(oldName)
                                            let startBorderIndex = -1;
                                            let endBorderIndex = -1;
                                            for (let i = 0, contentLen = para.content.length; i < contentLen; i++) {
                                                const curPortion = para.content[i];
                                                const paraBorder = curPortion.content[0];
                                                // tslint:disable-next-line: max-line-length
                                                if (paraBorder != null && paraBorder.type === ParaElementType.ParaNewControlBorder) {
                                                    if (paraBorder.isNewControlStartBoder() &&
                                                        paraBorder.getNewControlName() === oldName) {

                                                        startBorderIndex = i;
                                                    }
                                                    if (paraBorder.isNewControlEndBoder() &&
                                                        paraBorder.getNewControlName() === oldName) {

                                                        endBorderIndex = i;
                                                        break;
                                                    }
                                                }
                                            }
                                            // console.log(startBorderIndex, endBorderIndex)
                                            if (startBorderIndex !== -1 && endBorderIndex !== -1) {
                                                const paraContent = para.content;
                                                // const preservedPropPortion = paraContent[startBorderIndex + 1];
                                                // if (preservedPropPortion != null) {
                                                //     preservedPortionProp = preservedPropPortion.getDirectTextProperty();
                                                // }
                                                preservedPortionProp = this.getNewControlContentProp(paraContent, startBorderIndex);
                                                paraContent.splice(startBorderIndex, endBorderIndex - startBorderIndex + 1);
                                            } else {
                                                // tslint:disable-next-line: no-console
                                                console.warn('startBorderIndex/endBorderIndex is invalid');
                                                return ResultType.Failure;
                                            }
                                            // console.log(para)
                                            newControlText.addToParagragh(para, startBorderIndex - 1);

                                            // apply text prop
                                            if (!para.content[startBorderIndex].textProperty.isEqual(preservedPortionProp)) {
                                                const placeHoldePrortion = newControlText.getPlaceHolder();
                                                placeHoldePrortion.setTextProperty(preservedPortionProp);
                                            }
                                            // const newControlContent = newControlText.getNewControlContent();
                                            // if (newControlContent != null && preservedPortionProp != null) {
                                            //     newControlContent.applyTextProperty(preservedPortionProp);
                                            // }
                                        }

                                        renewedNewControls.push(newControlText);

                                    }
                                }

                                // set text content
                                // const renewedNewControls = theHeader.getContent()
                                //     .getAllNewControls();
                                const textContent = jsonText[pageIndex];
                                // console.log(renewedNewControls)
                                // console.log(textContent);

                                this.setControlTextInHeader(renewedNewControls, textContent);

                            }
                        }
                    } else {
                        oldNewControls = curSecPro.sectProperty.headerDefault.getContent()
                                .getAllNewControls();
                        if (oldNewControls.length > 0) {
                            const textContent = jsonText[pageIndex];
                            this.setControlTextInHeader(oldNewControls, textContent);
                        }
                    }

                    logicDocument.setHeaderFooterInterface(true);

                } else {
                    if (pageIndex < 1) {
                        return ResultType.Failure;
                    }
                    // the first DocumentSectionsInfoElement always exist, just modify it
                    // pageindex is 1
                    // set text content
                    if (oldNewControls.length > 0) {
                        const textContent = jsonText[pageIndex];
                        // like this:
                        // [
                        //     {
                        //         "SerialNumber": "页眉医院名称",
                        //         "text": "鞍山市肿瘤医院"
                        //     },
                        //     {
                        //         "SerialNumber": "标题",
                        //         "text": "主治医师查房记录"
                        //     }
                        // ]
                        // console.log(newControls)
                        // console.log(textContent);

                        this.setControlTextInHeader(oldNewControls, textContent, true);

                    }
                }

            }
        }

        logicDocument.recalculateAllForce();

        this._host.handleRefresh();
        return ResultType.Success;
    }

    public deleteRedundantInHeader(sText: string): number {
        let result = ResultType.Failure2;
        const logicDocument = this._documentCore.getDocument();
        const sectPr = logicDocument ? logicDocument.sectionProperty : null;
        if (sectPr) {
            const headerFirst = sectPr.getHeaderFirst();
            const headerDefault = sectPr.getHeaderDefault();

            if (headerFirst || headerDefault) {
                if (headerFirst && headerFirst.getDocumentContent()) {
                    const contents = headerFirst.getDocumentContent();
                    result = this.deleteLastEmptyParagragh(contents);
                    if (ResultType.Success === result) {
                        contents.curPos.contentPos = (contents.getElementsCount() <= contents.curPos.contentPos) ?
                                contents.getElementsCount() - 1 : contents.curPos.contentPos;
                    }
                }

                if (headerDefault) {
                    const contents = headerDefault.getDocumentContent();
                    const res = this.deleteLastEmptyParagragh(contents);
                    if (ResultType.Success === res) {
                        contents.curPos.contentPos = (contents.getElementsCount() <= contents.curPos.contentPos) ?
                                contents.getElementsCount() - 1 : contents.curPos.contentPos;
                    }
                    result = (ResultType.Failure2 === result ? res : result && res);
                }

                if (ResultType.Success === result) {
                    logicDocument.recalculate();
                    logicDocument.updateCursorXY();
                    this._host.handleRefresh();
                }
            }
        }

        return result;
    }

    // helper function
    private setControlTextInHeader(newControls: NewControl[], textContent: any[], bFirst?: boolean): void {

        // textContent like this:
        // [
        //     {
        //         "SerialNumber": "页眉医院名称",
        //         "text": "xx市xx医院"
        //     },
        //     {
        //         "SerialNumber": "标题",
        //         "text": "主治医师查房记录"
        //     }
        // ]
        if (!newControls || !textContent || textContent.length === 0) {
            return;
        }

        const keyValuePair = Object.keys(textContent[0]);
        const keyString = keyValuePair[0]; // eg: SerialNumber
        const valueString = keyValuePair[1]; // eg: text
        // console.log(keyString)
        // console.log(valueString)

        for (const newControl of newControls) {
            // 检查newControl是否为undefined或null
            if (!newControl) {
                continue;
            }

            const newControlCustomKey = newControl.getCustomByPropName(keyString);
            const serialNumber = newControl.getSerialNumber();
            let serialNumContent = null;
            for (const item of textContent) {
                if (serialNumber != null && serialNumber === item[keyString]) {
                    // check either custom props or serialnumber
                    serialNumContent = item[valueString];
                    // console.log(structText);

                    this.setNewControlTextWithProperty(newControl, serialNumContent, bFirst);

                } else if (newControlCustomKey != null && newControlCustomKey === item[keyString]) {
                    // double check if already set by serial number
                    const controlText = newControl.getNewControlText();
                    if (serialNumContent !== controlText) {
                        const structText = item[valueString];

                        this.setNewControlTextWithProperty(newControl, structText, bFirst);
                    }
                }
            }
        }
    }


    private setNewControlTextWithProperty(newControl: NewControl, value: string, bFirst?: boolean): void {

        const startBorderPortion = newControl.getStartBorderPortion();
        const startPara = startBorderPortion.paragraph;
        const startPortionIndex = startPara.getPortionIndexById(startBorderPortion.id);
        const contentPortion = startPara.content[startPortionIndex + 1];
        let textProperty = null;


        if (bFirst) {
            textProperty = this.getNewControlContentProp(startPara.content, startPortionIndex);
        } else if (contentPortion != null) {
            textProperty = contentPortion.textProperty.copy();
        }
        // const textProperty = this.getNewControlContentProp(startPara.content, startPortionIndex);

        // console.log(textProperty)

        newControl.setNewControlText(value);

        if (textProperty != null) {
            startPara.content[startPortionIndex + 1].setTextProperty(textProperty);
        }
    }

    private deleteLastEmptyParagragh(contents: DocumentContent): number {
        let result = ResultType.Failure2;
        if (contents && contents.content) {
            result = ResultType.UnEdited;

            if (2 <= contents.content.length) {
                // const elements = contents.content[contents.content.length - 1];
                for (let index = contents.content.length - 1; index >= 0; index--) {
                    const element = contents.content[index];
                    if (element && element.isParagraph() && element.isEmpty()) {
                        contents.logicDocument.removeSelection();
                        contents.content.splice(contents.content.length - 1, 1);
                        result = ResultType.Success;
                    } else {
                        break;
                    }
                }
            }
        }

        return result;
    }


    private getNewControlContentProp(contents: any, pos: number): any {
        const firstPortion = contents[pos + 1];
        if (!firstPortion) {
            return contents[pos].textProperty.copy();
        }

        const nextPortion = contents[pos + 2];
        if (firstPortion.isEmpty(false) && nextPortion && !nextPortion.isNewControlEnd()) {
            if (!nextPortion.isEmpty(false)) {
                return nextPortion.textProperty.copy();
            }
        }

        return firstPortion.textProperty.copy();
    }

}
