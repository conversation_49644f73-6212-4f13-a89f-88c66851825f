// import {GlobalEvent as gEvent, GlobalEventName as gEventName} from '../GlobalEvent';

import { IIndexDB, LoggerType } from './LoggerType';

export interface ILoggerProperty {
    id?: number;
    startTime?: Date;
    code?: string;
    description?: string;
    result?: any;
    name?: string;
    args?: any;
}

enum LoggerMode {
    Dev,
    Production,
}

interface ILoggerNode {
    type: LoggerType;
    time: Date;
    options: ILoggerProperty;
}

interface IOutputNode {
    time: string;
    editorID: string;
    wasted?: number;
    result?: any;
    description: string;
}

class OutPutLog {
    private _type: LoggerType;
    private _options: ILoggerProperty;
    private _time: Date;
    private _id: string;
    constructor(node: ILoggerNode, id: string) {
        this._type = node.type;
        this._options = node.options || {};
        this._time = node.time;
        this._id = id;
    }

    public getDBNode(): IOutputNode {
        const wasted = this._getWasted();
        const result = this._getResult();
        const item = {
            time: this._getTimeString(),
            editorID: this._id + '-' + this._options.id,
            wasted,
            result,
            description: this._getMsg(),
        };
        this.getIP(item);
        return item;
    }

    public testDBInsert(length: number): IOutputNode[] {
        const datas: IOutputNode[] = [];
        const wasted = this._getWasted();
        const result = this._getResult();
        const id = this._id + '-' + this._options.id;
        for (let i = 0; i < length; i++) {
            const item: IOutputNode = {
                time: this._getTimeString(),
                editorID: id,
                wasted,
                result,
                description: this._getMsg(),
            };
            datas.push(item);
        }

        return datas;
    }

    public getOptions(): object {
        return {
            time: this._getTimeString(),
            level: this._type,
            editorID: '1001',
            package: {
                wasted: this._getWasted(),
                ip: '127.0.0.1',
                creator: '李四',
                result: this._getResult(),
                description: this._getMsg(),
            },
        };
    }

    private _getTimeString(): string {
        const time = this._time;
        const date = `${time.getFullYear()}-${time.getMonth() + 1}-${time.getDate()}`;

        return `${date} ${time.getHours()}:${time.getMinutes()}:${time.getSeconds()}:${time.getMilliseconds()}`;
    }

    private _getWasted(): number {
        if (!this._options.startTime) {
            return undefined;
        }

        return this._time.getTime() - this._options.startTime.getTime();
    }

    private _getResult(): any {
        const result = this._options.result;
        if (result === undefined) {
            return '';
        }

        if (typeof result === 'number') {
            return result;
        } else if (typeof result === 'string') {
            return this._subString(result);
        } else if (result) {
            return 0;
        }

        return 1;
    }

    private _getMsg(): string {
        const options = this._options;
        if (options.description) {
            return options.description;
        }

        let result = '';
        if (options.name) {
            result = `name: ${options.name},`;
        }

        const args = options.args;
        if (args) {
            result += 'arguments: ';
            if (Array.isArray(args)) {
                args.forEach((arg, index) => {
                    const val = this._subString(arg, 1000);
                    result += `${index}: ${val},`;
                });
            } else {
                const keys = Object.keys(args);

                keys.forEach((key) => {
                    const val = this._subString(args[key]);
                    result += `${key}: ${val},`;
                });
            }
        }

        if (options.code) {
            result += `code: ${options.code},`;
        }

        result = result ? result.slice(0, -1) : result;

        return result;
    }

    private _subString(val: string, len: number = 400): string {
        if (typeof val === 'string' && val.length > len) {
            return 'string is too long';
        }

        return val;
    }

    private getIP(item: IOutputNode): void {
        // ip.setIp(item);
    }
}

class LoggerBase {
    private ids: object;
    private _mode: LoggerMode;
    private _logs: {
        [LoggerType.Interface]: ILoggerNode[],
        [LoggerType.Error]: ILoggerNode[],
        // [LoggerType.Debug]: ILoggerNode[],
        // [LoggerType.Info]: ILoggerNode[],
        [LoggerType.DevLog]: ILoggerNode[],
    };
    private _time: number;
    private _timeout: any;
    private _logDB: IIndexDB;
    private _openLog: object;
    private _keys: string[];
    private _id: number;
    private _uploadCallback: (content: string) => void;
    private _logExists: object;
    constructor() {
        this._keys = [LoggerType.Interface, LoggerType.Error, LoggerType.DevLog];
        this._time = 1000;
        this._logs = {
            [LoggerType.Interface]: [],
            [LoggerType.Error]: [],
            // [LoggerType.Debug]: [],
            // [LoggerType.Info]: [],
            [LoggerType.DevLog]: [],
        };
        this._openLog = {};
        this._mode = LoggerMode.Production;
        this.ids = {};
        this._logExists = {};
    }

    public isAddInterfaceLog(name: string): boolean {
        const logs = this._logs[LoggerType.Interface];
        const log = logs[logs.length - 1];
        if (log && log.options && log.options.name === name) {
            return false;
        }

        return true;
    }

    // public warn( options: ILoggerProperty): void {
    //     this._logs._push({msg: message, options, type: LoggerType.Warning});
    //     this._writeLog();
    // }

    public setId(id: string, docId: number): void {
        this.ids[docId] = id;
    }

    public setUploadFunc(fun: (content: string) => void): void {
        if (!fun || typeof fun !== 'function') {
            return;
        }
        this._uploadCallback = fun;
    }

    public devlog(options: ILoggerProperty): void {
        this._push(options, LoggerType.DevLog);
    }

    public error(options: ILoggerProperty): void {
        this._push(options, LoggerType.Error);
    }

    public info(options: ILoggerProperty): void {
        // this._push(options, LoggerType.Info);
    }

    public debug(options: ILoggerProperty): void {
        // this._push(options, LoggerType.Debug);
    }

    public interface(options: ILoggerProperty): void {
        this._push(options, LoggerType.Interface);
    }

    public open(id: string): void {
        this._openLog[id] = true;
        // this.initWindowObj();
    }

    public close(id: string): void {
        this._openLog[id] = false;
    }

    public async getItem(): Promise<any> {
        // let db: LoggerSQL = this._logDB;
        // if ( db ) {
        //     const datas = await db.query(LoggerType.Info, '');
        //     if ( datas && 0 < datas.length ) {
        //         let data = null;
        //         for (let index = datas.length - 1; 0 <= index ;index--) {
        //             const item = datas[index];
        //             if ( item && 'IncrementSave' === item.description ) {
        //                 data = datas[index].result;

        //                 return data;
        //             }
        //         }
        //     }
        // }

        return null;
    }

    private _push(options: ILoggerProperty, type: LoggerType): void {
        if (!options || type !== LoggerType.Error && this._openLog[this.ids[options.id]] !== true) {
            return;
        }

        this._id = options.id;
        this._logs[type].push({options, type, time: new Date()});
        this._writeLog();
        // if (this._uploadCallback) {
        //     this._writeLog2();
        // } else if (window['openDatabase']) {
        //     this._writeLog1();
        // } else {
        //     this._writeLog();
        // }
        // if (this._uploadCallback) {
        //     this._writeLog2();
        // } else {
        //     this._writeLog1();
        // }
    }

    private _writeLog(): void {
        clearTimeout(this._timeout);
        this._timeout = setTimeout(() => {
            if (!this._logDB) {
                this._initDB();
                this._writeLog();
                return;
            }

            const currentLogs = this._logs;

            // 遍历不同类型的日志数据
            this._keys.forEach((key) => {
                const logs = [];
                const datas = currentLogs[key];
                if (datas.length === 0) {
                    return;
                }
                // 装载、解析所有的日志
                datas.forEach((log) => {
                    logs.push(new OutPutLog(log, this.ids[log.options.id]).getDBNode());
                });
                this._logDB.insert(key, logs);
                currentLogs[key] = [];
            });
        }, this._time);
    }

    private async _initDB(): Promise<boolean> {
        const dbobj = await import('./db');
        const db: IIndexDB = new dbobj.default();
        const res = await db.init();
        this._logDB = db;
        if (window['editorCallback']) {
            window['editorCallback'].getDB = () => {
                return db;
            };
        }

        setTimeout(() => {
            db['deleteMaxDatas'](LoggerType.Interface);
        }, 100000);
        return res;
    }

    // private _writeLog2(): void {
    //     clearTimeout(this._timeout);
    //     this._timeout = setTimeout(() => {
    //         // if (ip === undefined) {
    //         //     ip = new Ip();
    //         // }
    //         const currentLogs = this._logs;
    //         const logs = [];
    //         // 遍历不同类型的日志数据
    //         this._keys.forEach((key) => {
    //             const datas = currentLogs[key];
    //             if (datas.length === 0) {
    //                 return;
    //             }
    //             // 装载、解析所有的日志
    //             datas.forEach((log) => {
    //                 logs.push(new OutPutLog(log, this.ids[log.options.id]).getDBNode());
    //             });
    //             currentLogs[key] = [];
    //         });

    //         if (logs.length > 0) {
    //             this._uploadCallback(JSON.stringify(logs));
    //         }
    //     }, this._time);
    // }

    // private initWindowObj(): void {
    //     if (this._uploadCallback) {
    //         return;
    //     }
    //     let db: LoggerSQL = this._logDB;
    //     if (!db) {
    //         db = this._logDB = new LoggerSQL();
    //         // ip = new Ip();
    //         if (window['editorCallback']) {
    //             window['editorCallback'].getDB = () => {
    //                 return db;
    //             };
    //         }
    //     }
    // }

    // private _writeLog1(): void {
    //     let db: LoggerSQL = this._logDB;
    //     if (!db) {
    //         db = this._logDB = new LoggerSQL();
    //         // ip = new Ip();
    //         // if (window['editorCallback']) {
    //         //     window['editorCallback'].getDB = () => {
    //         //         return db;
    //         //     };
    //         // }
    //         setTimeout(() => {
    //             this._addEvent();
    //         }, 1000);
    //     }
    //     clearTimeout(this._timeout);
    //     this._timeout = setTimeout(() => {
    //         const currentLogs = this._logs;
    //         this._keys.forEach((key) => {
    //             const datas = currentLogs[key];
    //             if (datas.length === 0) {
    //                 return;
    //             }
    //             // const logs = [];
    //             datas.forEach((log) => {
    //                 db.insert(new OutPutLog(log, this.ids[log.options.id]).getDBNode());
    //                 // logs.push();
    //             });
    //             currentLogs[key] = [];
    //         });
    //     }, this._time);
    // }

    // private _writeLog(): void {
    //     let db = this._logDB;
    //     if (!db) {
    //         if (db !== null) {
    //             db = new LoggerDB((res) => {
    //                 this._logDB = res;
    //                 this._writeLog();
    //             });
    //             this._logDB = null;
    //             ip = new Ip();
    //         }
    //         setTimeout(() => {
    //             this._addEvent();
    //         }, 1000);
    //         return;
    //     }

    //     clearTimeout(this._timeout);
    //     this._timeout = setTimeout(() => {
    //         // let startDate: Date = new Date();
    //         // let endDate: Date;
    //         const currentLogs = this._logs;
    //         let id: number;
    //         this._keys.forEach((key) => {
    //             const datas = currentLogs[key];
    //             if (datas.length === 0) {
    //                 return;
    //             }
    //             const logs = [];
    //             datas.forEach((log) => {
    //                 if (log.id === undefined) {
    //                     log.id = id;
    //                 } else {
    //                     id = log.id;
    //                 }
    //                 logs.push(new OutPutLog(log, this.ids[log.options.id]).getDBNode());
    //             });
    //             db.setTableName(key);
    //             db.insert(logs);
    //             currentLogs[key] = [];

    //             // const outLog = new OutPutLog(log);
    //             // const datas = outLog.testDBInsert(10);
    //             // db.insert(datas)
    //             // .then((result) => {
    //             //     endDate = new Date();
    //             //     console.log(endDate.getTime() - startDate.getTime());
    //             //     startDate = new Date();
    //             //     db.getCount()
    //             //     .then((res) => {
    //             //         // console.log(res)
    //             //         endDate = new Date();
    //             //         console.log(endDate.getTime() - startDate.getTime());

    //             //         startDate = new Date();
    //             //         db.getCount()
    //             //         .then((res) => {
    //             //             // console.log(res)
    //             //             endDate = new Date();
    //             //             console.log(res)
    //             //             console.log(endDate.getTime() - startDate.getTime());
    //             //         })
    //             //     })
    //             // });

    //             // db.query((item) => {
    //             //     return true;
    //             // }, 10000)
    //             // .then((result) => {
    //             //     endDate = new Date();
    //             //     console.log(endDate.getTime() - startDate.getTime());
    //             //     console.log(result)
    //             // });

    //             // db.query((item) => {
    //             //     if (item.level === LoggerType.Error) {
    //             //         return true;
    //             //     }
    //             //     return false;
    //             // }, 1).then((result) => { console.log(result); });
    //         });
    //     }, this._time);
    // }

    private _addEvent(): void {
        // window.addEventListener('error', this._error);
        // gEvent.addEvent(0, gEventName.unMounted, () => {
        //     window.removeEventListener('error', this._error);
        // });
    }

    private _error = (err: any): any => {
        if (!err || !err.error) {
            return;
        }
        this.error({id: this._id, description: `message: ${err.error.message};stack: ${err.error.stack};`});
    }
}

export let logger = new LoggerBase();
