// import PrintDialogModel from '../../common/print/PrintDialog';
// import {EmrPrint} from './PrintContent';
// import * as ReactDOM from 'react-dom';
// import * as React from 'react';
// import {printStyle} from '../../common/css/style';
// import {GlobalEvent as gEvent, GlobalEventName as gEventName} from '../../common/GlobalEvent';
// export class PrintDialog extends PrintDialogModel {
//     // public src: string;
//     // private docId: number;
//     constructor(doc?: any) {
//         super(doc);
//         this.src = 'about:link';
//     }

//     public open(isPrint: boolean): void {
//         this.isPrint = isPrint;
//         this.addDialog();
//     }

//     private addDialog(): void {
//         const pagePro = this.getPagePro() as any;
//         if (this.print) {
//             this.intPrintDialog(pagePro);
//             return;
//         }
//         let className = '';
//         if (this.isPrint) {
//             className = ' print-hide';
//         }
//         const src = this.src + '?time=' + (new Date()).getTime();
//         const dom = document.createElement('div');
//         dom.id = 'print-dialog';
//         dom.className = 'print-dialog active' + className;
//         // 页眉页脚：<input class='header-box' type='checkbox' data-type='1' checked='checked'  value='1' />
//         dom.innerHTML = `<div class='print-dialog-box' style='width: ${pagePro.width + 18}px'>
//             <div class='print-dialog-box-header'>
//                 <div class='btns'>
//                     <button type='1'>打印</button>
//                     <button type='2'>选择续打</button>
//                     清洁模式：<input type='checkbox' disabled='true' />
//                     <select style='width: 120px;'>
//                         <option value='200'>200%</option>
//                         <option value='150'>150%</option>
//                         <option value='100' selected='selected'>100%</option>
//                         <option value='75'>75%</option>
//                         <option value='50'>50%</option>
//                         <option value='25'>25%</option>
//                     </select>
//                     <div class='print-dialog-box-header-clinc'>
//                         <p>
//                             <input type='checkbox' class='clinc-model'  />门诊模式
//                         </p>
//                         <p class='clinc-radio-group'>
//                             <input type='radio' value='top' disabled  name='clinc' class='clinc-radio'/>上页
//                             <input type='radio' value='bottom' disabled  name='clinc' class='clinc-radio'/>下页
//                         </p>
//                     </div>
//                 </div>
//                 <div class='close' style='position: absolute; top: 20px; right: 10px;'>X</div>
//             </div>
//             <div class='print-dialog-box-body'>
//                 <iframe src='${src}' style='width: 100%; height: 100%;' frameborder='0'></iframe>
//             </div>
//         </div>`;
//         document.body.appendChild(dom);
//         this.initIframe(dom);
//         this.addEvent();
//         this.updateContent();
//         // gEvent.addEvent(this.docId, gEventName.UnMounted, this.deleteEvent);
//         // console.log(date.getMinutes() + ':' + date.getSeconds() + '.' + date.getMilliseconds());
//     }

//     private initIframe(dom: HTMLElement): void {
//         const iframe = dom.querySelector('iframe');
//         const iframeContent = iframe.contentWindow;
//         this.print = dom;
//         const style = document.createElement('style');
//         style.type = 'text/css';
//         style.innerHTML = printStyle();
//         iframeContent.document.body.innerHTML = `<div id='hz-editor-app' class='hz-editor-print'></div>`;
//         iframeContent.document.head.appendChild(style);
//         const currentReact = ReactDOM.render(<EmrPrint />, iframeContent['hz-editor-app']);
//         this.reactVm = currentReact;
//     }

//     private addEvent(): void {
//         this.print.addEventListener('mousewheel', this.preventDefault);
//         this.print.querySelector('.btns')
//             .addEventListener('click', this.bntsClick, false);
//         this.print.querySelector('.close')
//             .addEventListener('click', this.close, false);
//         this.print.querySelector('select')
//             .addEventListener('change', this.selectChange, false);
//         const clincCheckDom = this.clincCheckDom = this.print
//         .querySelector('.print-dialog-box-header-clinc .clinc-model');
//         clincCheckDom.addEventListener('change', this.clincCheckChange);
//         this.clincRadioDom = this.print
//         .querySelector('.print-dialog-box-header-clinc .clinc-radio-group');
//         this.clincRadioDom.addEventListener('change', this.clincRadioChange);
//     }

//     // private deleteEvent = (): void => {
//     //     this.print.removeEventListener('mousewheel', this.preventDefault);
//     //     this.print.querySelector('.btns')
//     //         .removeEventListener('click', this.bntsClick);
//     //     this.print.querySelector('.close')
//     //         .removeEventListener('click', this.close);
//     //     this.print.querySelector('select')
//     //         .removeEventListener('change', this.selectChange);
//     //     this.print.outerHTML = '';
//     //     gEvent.deleteEvent(this.docId, gEventName.UnMounted, this.deleteEvent);
//     //     // this.print.querySelector('.header-box').removeEventListener('change', this.checkboxChange);
//     // }
// }
