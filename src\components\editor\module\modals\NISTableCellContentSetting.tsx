import * as React from 'react';
import Dialog from '../../ui/Dialog';
import Button from '../../ui/Button';
import Radio from '../../ui/Radio';
import SelectUI from '../../ui/select/Select';
import '../../style/nistableCellSetting.less';
import { IFRAME_MANAGER } from '../../../../common/IframeManager';
import { DocumentCore } from '../../../../model/DocumentCore';
import { LineSpacingType } from '../../../../common/commonDefines';
import { fonts } from '../../text/font';

interface IProps {
    documentCore: any;
    visible: boolean;
    id: string;
    close: (name: string | number, bRefresh?: boolean) => void;
}

interface IState {
    bRefresh: boolean;
}

interface IPropNameKey {
    key: string;
    value: any;
    bDisabled?: boolean;
    disabled?: boolean;
}

export class NISTableCellContentSetting extends React.Component<IProps, IState> {
    private visible: boolean;
    private cellContentProps: any;
    private ranges: IPropNameKey[];
    private fontFamilyDatas: IPropNameKey[];
    private fontSizeDatas: IPropNameKey[];
    private paraSpacingDatas: IPropNameKey[];
    private alignDatas: IPropNameKey[];
    private cellVertAlignTypeDatas: IPropNameKey[];
    constructor(props: any) {
        super(props);
        this.state = {
            bRefresh: false,
        };
        this.ranges = [
            {key: '单个单元格', value: 0},
            {key: '当前行', value: 1},
            {key: '当前列', value: 2},
        ];
        this.paraSpacingDatas = [
            {key: '单倍行距', value: LineSpacingType.Single},
            {key: '1.5倍行距', value: LineSpacingType.SingeHalf},
            {key: '2倍行距', value: LineSpacingType.Double},
        ];

        this.fontFamilyDatas = [];
        fonts[0].options.forEach((item) => {
            this.fontFamilyDatas.push({key: item.name, value: item.value});
        });

        this.fontSizeDatas = [];
        fonts[1].options.forEach((item) => {
            this.fontSizeDatas.push({key: item.name, value: item.value});
        });

        this.alignDatas = [
            {key: '左对齐', value: 0},
            {key: '居中对齐', value: 1},
            {key: '右对齐', value: 2},
            {key: '两端对齐', value: 3},
        ];
        this.visible = this.props.visible;

        // const cellContentProps = this.props.documentCore.getNISTableCellContentProps();
        this.cellContentProps = {} as any;
        // {
        //     range: cellContentProps ? cellContentProps.range : 0,
        //     fontFamily: cellContentProps ? cellContentProps.fontFamily : 0,
        //     fontSize: cellContentProps ? cellContentProps.fontSize : 0,
        //     paraSpacing: cellContentProps ? cellContentProps.paraSpacing : 0,
        //     alignType: cellContentProps ? cellContentProps.alignType : 0,
        //     cellVertAlignValue: cellContentProps ? cellContentProps.cellVertAlignValue : 0,
        // };
        this.cellVertAlignTypeDatas = [
            {key: '顶部', value: 0},
            {key: '居中', value: 1},
            {key: '底部', value: 2},
        ];
    }

    public render(): any {
        return (
            <Dialog
                visible={this.visible}
                width={380}
                open={this.open}
                title='单元格格式设置'
                footer={this.renderFooter()}
                id={this.props.id}
            >
                <div className='nistableCellSetting'>
                    <div className='editor-line'>
                        <span className='title'>格式设置范围（不包含表头）</span>
                    </div>
                    <div className='editor-line'>
                        <Radio
                            value={this.cellContentProps.range}
                            data={this.ranges}
                            name='range'
                            onChange={this.onChange}
                        />
                    </div>
                    <div className='editor-line'>
                        <span className='title'>文字</span>
                    </div>
                    <div className='editor-line'>
                        <span className='w-70'>字体：</span>
                        <div className='right-auto'>
                            <SelectUI
                                name='fontFamily'
                                onChange={this.onChange}
                                data={this.fontFamilyDatas}
                                value={this.cellContentProps.fontFamily}
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <span className='w-70'>字号：</span>
                        <div className='right-auto'>
                            <SelectUI
                                name='fontSize'
                                onChange={this.onChange}
                                data={this.fontSizeDatas}
                                value={this.cellContentProps.fontSize}
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <span className='title'>段落</span>
                    </div>
                    <div className='editor-line'>
                        <span className='w-70'>段落行距：</span>
                        <div className='right-auto'>
                            <SelectUI
                                name='paraSpacing'
                                onChange={this.onChange}
                                data={this.paraSpacingDatas}
                                value={this.cellContentProps.paraSpacing}
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <span className='w-70'>段落对齐：</span>
                        <div className='right-auto'>
                            <SelectUI
                                name='alignType'
                                onChange={this.onChange}
                                data={this.alignDatas}
                                value={this.cellContentProps.alignType}
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <span className='title'>单元格对齐方式</span>
                    </div>
                    <div className='editor-line'>
                        <Radio
                            value={this.cellContentProps.vertAlign}
                            data={this.cellVertAlignTypeDatas}
                            name='vertAlign'
                            onChange={this.onChange}
                        />
                    </div>
                </div>
            </Dialog>
        );
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        this.visible = nextProps.visible;
    }

    private renderFooter(): any {
        return (
            <span>
                <Button onClick={this.close}>取消</Button>
                <Button type='primary' onClick={this.confirm}>
                    确认
                </Button>
            </span>
        );
    }

    private open = (): void => {
        const cellContentProps = this.props.documentCore.getNISTableCellContentProps() || {};
        this.cellContentProps = {
            range: cellContentProps ? cellContentProps.range : 0,
            fontFamily: cellContentProps ? cellContentProps.fontFamily : 0,
            fontSize: cellContentProps ? cellContentProps.fontSize : 0,
            paraSpacing: cellContentProps ? cellContentProps.paraSpacing : 0,
            alignType: cellContentProps ? cellContentProps.alignType : 0,
            vertAlign: cellContentProps.vertAlign != null ? cellContentProps.vertAlign : 0,
        };

        this.setState({bRefresh: !this.state.bRefresh});
    }

    private onChange = (value: any, name: string): void => {
        this.cellContentProps[name] = value;
    }

    private confirm = (id?: any): void => {
        const documentCore: DocumentCore = this.props.documentCore;
        IFRAME_MANAGER.setDocId(documentCore.getCurrentId());

        documentCore.setNISTableCellContentProps(this.cellContentProps);

        this.close(true);
    }

    private close = (bRefresh?: boolean): void => {
        this.props.close(this.props.id, bRefresh);
        this.visible = false;
        this.setState({bRefresh: !this.state.bRefresh});
    }
}
