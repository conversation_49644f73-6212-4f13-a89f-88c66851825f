/*
 * xml编码
 * @param string unsafe 编码前原始值
 * @return string 编码后xml值
 */
export function escapeXML(unsafe: string): string {
  return unsafe.replace(/[<>&'"]/g, function (c) {
    switch (c) {
      case "'": 
        return '&apos;';
      case '"': 
        return '&quot;';
      case '>': 
        return '&gt;';
      case '<': 
        return '&lt;';
      case '&': 
        return '&amp;';
    }
  });
}

/*
 * xml反编码
 * @param string xml 编码后xml值
 * @return string 原始值
 */
export function unescapeXML(xml: string): string {
  return xml
    .replace(/&apos;/g, "'")
    .replace(/&quot;/g, '"')
    .replace(/&gt;/g, '>')
    .replace(/&lt;/g, '<')
    .replace(/&amp;/g, '&');
}
