import * as React from 'react';
import Dialog from '../dialog/dialog';
import { INewControlProperty, NewControlType, NewControlContentSecretType,
    NewControlDefaultSetting } from '../../../common/commonDefines';
import {message} from '../../../common/Message';

interface IDialogProps {
    documentCore: any;
    visible: boolean;
    property?: INewControlProperty;
    close: (name: string | number, bReflash?: boolean) => void;
    children?: React.ReactNode;
}

interface IState {
    bReflash: boolean;
}

export default class NewControlNumDialog extends React.Component<IDialogProps, IState> {
    private newControl: INewControlProperty;
    private displayType: boolean;
    private isValidData: boolean;
    private _currentTarget: any;
    constructor(props: any) {
        super(props);
        this.state = {
            bReflash: false,
        };
        this.newControl = {newControlName: undefined};
    }

    public render(): any {

        // from reading file
        if (this.newControl.newControlDisplayType === NewControlContentSecretType.AllSecret) {
            this.displayType = true;
        } else {
            this.displayType = false;
        }

        return (
            <Dialog
                visible={this.props.visible}
                top='middle'
                width={350}
                height={450}
                close={this.close}
                open={this.open}
                preventDefault={true}
                title='数值框'
                confirm={this.confirm}
                id='newControlNum'
            >
                <div>
                    <div>
                        <p className='common-title'>常规：</p>
                        <div className='full-line'>
                            <label className='common-label'>名称</label>
                            <input
                                value={this.newControl.newControlName || ''}
                                onChange={this.textChange.bind(this, 'newControlName')}
                            />
                        </div>
                        <div className='full-line'>
                            <label className='common-label'>提示信息：</label>
                            <input
                                value={this.newControl.newControlInfo || ''}
                                onChange={this.textChange.bind(this, 'newControlInfo')}
                            />
                        </div>
                        <div className='full-line'>
                            <label className='common-label'>占位符：</label>
                            <input
                                value={this.newControl.newControlPlaceHolder || ''}
                                onChange={this.textChange.bind(this, 'newControlPlaceHolder')}
                            />
                        </div>
                        <p className='common-title'>属性：</p>
                        <div className='full-line'>
                            <input
                                type='checkbox'
                                id='isNewControlHidden'
                                checked={this.newControl.isNewControlHidden === true}
                                onChange={this.checkChange.bind(this, 'isNewControlHidden')}
                            />
                            <label htmlFor='isNewControlHidden'>隐藏</label>
                            <input
                                type='checkbox'
                                id='isNewControlCanntDelete'
                                checked={this.newControl.isNewControlCanntDelete === true}
                                onChange={this.checkChange.bind(this, 'isNewControlCanntDelete')}
                            />
                            <label htmlFor='isNewControlCanntDelete'>禁止删除</label>
                        </div>
                        <div className='full-line'>
                            <input
                                type='checkbox'
                                id='isNewControlCanntEdit'
                                checked={this.newControl.isNewControlCanntEdit === true}
                                onChange={this.checkChange.bind(this, 'isNewControlCanntEdit')}
                            />
                            <label htmlFor='isNewControlCanntEdit'>禁止编辑</label>
                            <input
                                type='checkbox'
                                id='isNewControlMustInput'
                                checked={this.newControl.isNewControlMustInput === true}
                                onChange={this.checkChange.bind(this, 'isNewControlMustInput')}
                            />
                            <label htmlFor='isNewControlMustInput'>必填项</label>
                        </div>
                        <div className='full-line'>
                            <input
                                type='checkbox'
                                id='isNewControlShowBorder'
                                checked={this.newControl.isNewControlShowBorder === false}
                                onChange={this.checkChange.bind(this, 'isNewControlShowBorder')}
                            />
                            <label htmlFor='isNewControlShowBorder'>边框隐藏</label>
                            <input
                                type='checkbox'
                                id='isNewControlReverseEdit'
                                checked={this.newControl.isNewControlReverseEdit === true}
                                onChange={this.checkChange.bind(this, 'isNewControlReverseEdit')}
                            />
                            <label htmlFor='isNewControlReverseEdit'>反向编辑</label>
                        </div>
                        <div className='full-line'>
                            <input
                                type='checkbox'
                                id='isNewControlHiddenBackground'
                                checked={this.newControl.isNewControlHiddenBackground === true}
                                onChange={this.checkChange.bind(this, 'isNewControlHiddenBackground')}
                            />
                            <label htmlFor='isNewControlHiddenBackground'>隐藏背景色</label>
                        </div>
                        <p className='common-title'>固有属性：</p>
                        <div className='full-line'>
                            <input
                                type='checkbox'
                                id='newControlDisplayType'
                                checked={this.displayType === true}
                                onChange={this.displayTypeChange}
                            />
                            <label htmlFor='newControlDisplayType'>隐私显示</label>
                        </div>

                        <div className='full-line'>
                            <div className='inline-block'>
                                <label>最小值</label>
                                <input
                                    value={this.newControl.minValue === undefined ? '' : this.newControl.minValue}
                                    onChange={this.numChange.bind(this, 'minValue')}
                                    name='minValue'
                                    onBlur={this.onBlur}
                                />
                            </div>
                            <div className='inline-block'>
                                <label>最大值</label>
                                <input
                                    value={this.newControl.maxValue === undefined ? '' : this.newControl.maxValue}
                                    onChange={this.numChange.bind(this, 'maxValue')}
                                    name='maxValue'
                                    onBlur={this.onBlur}
                                />
                            </div>
                        </div>
                        <div className='full-line'>
                            <div className='inline-block'>
                                <label>精度</label>
                                <input
                                    value={this.newControl.precision === undefined ? '' : this.newControl.precision}
                                    onChange={this.num2Change.bind(this, 'precision')}
                                />
                            </div>
                            <div className='inline-block'>
                                <label>单位</label>
                                <input
                                    disabled={true}
                                    value={this.newControl.unit || ''}
                                    style={{width: 'unit'}}
                                    onBlur={this.onBlur}
                                    name='unit'
                                    onChange={this.textChange.bind(this, 'unit')}
                                />
                            </div>
                        </div>
                    </div>
                </div>
            </Dialog>
        );
    }

    private open = (): void => {
        const props = this.props.property;
        const newControl: INewControlProperty = this.newControl;
        if (props === undefined) {
            this.init();
            newControl.newControlName = this.props.documentCore.makeUniqueNewControlName(NewControlType.NumberBox);
        } else {
            for (const key in props) {
                if (!props.hasOwnProperty(key)) {
                    continue;
                }
                const val = props[key];
                newControl[key] = val;
            }
        }
        this.setState({bReflash: !this.state.bReflash});
    }

    private close = (id?: any): void => {
        this.props.close(id);
    }

    private confirm = (id?: any): void => {
        let flag = this.unitBlur();
        if (flag === false) {
            return;
        }
        flag = this.blurValidNum();
        if (flag === false) {
            return;
        }
        // if (this.isValidData === false) {
        //     return;
        // }
        this._currentTarget = null;
        const documentCore = this.props.documentCore;
        if (this.props.property === undefined) {
            documentCore.addNewControl(this.newControl);
        } else {
            documentCore.setNewControlProperty(this.newControl, this.newControl.newControlName);
        }

        this.props.close(id, true);
    }

    private init(): void {
        const newControl = this.newControl;
        newControl.newControlName = undefined;
        newControl.newControlInfo = undefined;
        newControl.newControlPlaceHolder = NewControlDefaultSetting.DefaultPlaceHolderContent;
        newControl.newControlType = NewControlType.NumberBox;
        newControl.isNewControlHidden = false;
        newControl.isNewControlCanntDelete = false;
        newControl.isNewControlCanntEdit = false;
        newControl.isNewControlMustInput = false;
        newControl.isNewControlShowBorder = true;
        newControl.isNewControlReverseEdit = false;
        newControl.isNewControlHiddenBackground = true;
        newControl.newControlDisplayType = NewControlContentSecretType.DontSecret;
        newControl.minValue = undefined;
        newControl.maxValue = undefined;
        newControl.precision = undefined;
        newControl.unit = undefined;
    }

    private onBlur = (e: any): void => {
        this._currentTarget = e.target;
    }

    private unitBlur = (): boolean => {
        const target = this._currentTarget;
        if (!target) {
            return true;
        }

        if (target.name !== 'unit') {
            return;
        }
        const value = target.value;
        if (value && value.trim()
            .slice(0, 1)
            .search(/^[a-z\u4E00-\u9FA5]/i) === -1) {
            message.error('单位设置错误');
            return false;
        }

        return true;
    }

    private blurValidNum = (): boolean => {
        const target = this._currentTarget;
        if (!target) {
            return true;
        }
        const key = target.name;
        if (key === 'unit') {
            return;
        }
        // const oldFlag = this.isValidData;
        this.isValidData = false;
        const newControl = this.newControl;
        const val = target.value;

        // const numStr = value + '';
        if (isNaN(val)) {
            message.error('当前输入框中存在非法字符!');
            target.focus();
            return false;
        }

        let value = Number.parseFloat(val);
        if (key === 'minValue') {
            if (newControl.maxValue !== undefined) {
                if (newControl.maxValue < value) {
                    message.error('最小值不能大于最大值!');
                    target.focus();
                    return false;
                }
            }
        } else if (this.newControl.minValue !== undefined) {
            if (newControl.minValue > value) {
                message.error('最大值不能小于最小值!');
                target.focus();
                return false;
            }
        }
        // this.isValidData = oldFlag && true;

        if (val === '') {
            value = undefined;
        }
        newControl[key] = value;
        return true;
    }

    private textChange(name: string, e: any): void {
        let value = e.target.value;
        if (!value) {
            value = undefined;
        }
        this.newControl[name] = value;
        this.setState({bReflash: !this.state.bReflash});
    }

    private checkChange(name: string): void {
        this.newControl[name] = !this.newControl[name];
    }

    private num2Change(name: string, e: any): void {
        const target = e.target;
        const value = target.value;
        let num = parseInt(value, 10);
        if (Number.isNaN(num) || value === '') {
            num = undefined;
        }

        this.newControl[name] = num;

        this.setState({bReflash: !this.state.bReflash});
    }

    private numChange(name: string, e: any): void {
        const target = e.target;
        const value = target.value;
        // let num = parseFloat(value);
        // if (Number.isNaN(num) || value === '') {
        //     num = undefined;
        // } else if (value && (value.slice(-1)[0] === '.' || value.slice(0, 1)[0] === '-')) {
        //     num = value;
        // }

        this.newControl[name] = value;
        this.setState({bReflash: !this.state.bReflash});
    }

    private displayTypeChange = (): void => {
        this.displayType = !this.displayType;
        if (this.displayType) {
            this.newControl.newControlDisplayType = NewControlContentSecretType.AllSecret;
        } else {
            this.newControl.newControlDisplayType = NewControlContentSecretType.DontSecret;
        }
    }
}
