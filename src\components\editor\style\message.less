.editor-message {
    display: none;
    position: fixed;
    left: 0;
    top: 0;
    z-index: 9999;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.02);

    &.active {
        display: block;
    }

    .editor-message-box {
        position: absolute;
        left: 0;
        right: 0;
        top: 50%;
        z-index: 1;
        width: 300px;
        margin: 0 auto;
        padding: 5px 0;
        font-size: 13px;
        font-family: 'PingFang SC', 'Microsoft YaHei', 'Microsoft Jhenghei', sans-serif;
        box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.36);
        background-color: #fff;
        border-radius: 3px;
        border: 1px solid #eee;
        transform: translateY(-50%);
        &.show-iframe {
            margin: unset;
            transform: none;
            right: unset;
        }
    }

    .editor-message-title {
        // height: 22px;
        // height: 100%;
        // margin-bottom: 10px;
        text-align: left;
        font-size: 14px;
        line-height: 22px;
        color: #293750;
        border-bottom: 1px solid #DFE2E5;
        padding: 0 10px;
    }

    .editor-message-title > span {
        display: inline-block;
        margin-bottom: 10px;
    }

    .editor-message-title .editor-message-close {
        display: block;
        float: right;
        color: #ACB4C1;
        cursor: pointer;
    }

    .editor-message-body {
        padding: 10px 5px 0 5px;
        text-align: center;
        line-height: 24px;
    }

    .editor-message-btns {
        margin-top: 20px;
        padding: 5px 0;
        text-align: center;
        line-height: 22px;
        font-size: 14px;
    }

    .editor-message-btns > span {
        margin-right: 20px;
        padding: 2px 8px;
        cursor: pointer;
        border: 1px solid #d5d4dc;
        border-radius: 2px;
        display: inline-block;
        width: 100px;
    }

    .editor-message-btns > span:last-child {
        margin-right: 0;
    }

    .editor-message-body {
        font-size: 16px;
        color: #293750;
        font-weight: 600;
    }

    .editor-message-body .editor-message-warning {
        font-size: 40px;
        color: red;
        margin-top: 10px;
        i {
            display: inline-block;
            height: 40px;
            width: 40px;
            background-image: url('../../../common/resources/warningSolid.png');
            text-align: center;
        }
    }
    
    .editor-message-btns > span:last-child {
        color: #009fff;
    }

    .editor-message-btns .special-blessing {
        // width: 110px;
        // height: 28px;
        background: #009FFF;
        border-radius: 2px;
        color: #FFFFFF !important;
        position: relative;
        top: -5px;
    }

    &.editor-confirm.cancelDefault {
        .editor-message-btns {
            span:last-child {
                color: #000000;
            }
            span:first-child {
                color: #009fff;
            } 
        }
    }
}

