@import './global.less';
.hz-editor-container .date-box {
    .custom-date {
        .editor-checkbox {
            line-height: 1;
        }
    }

    .custom-format {
        .custom-table {
            display: inline-table;
            & > div {
                display: table-cell;
                &:first-child {
                    .editor-input .inputGroupWrapper, .editor-input .addonAfterGroupWrapper {
                        border-right: 0;
                        border-bottom-right-radius: 0;
                        border-top-right-radius: 0;
                    }
                }
                &:last-child {
                    width: 30px;
                    .editor-input .inputGroupWrapper {
                        border-top-left-radius: 0;
                        border-bottom-left-radius: 0;
                    }
                }
            }
        }

        .editor-line .w-050 {
            width: calc(50% - 2px);
            &:first-child {
                width: calc(50% - 3px);
                margin-right: 5px;
            }
        }
    }
}