import * as React from 'react';
import Dialog from '../../ui/Dialog';
import chars from '../../text/chars';
import Button from '../../ui/Button';
import '../../style/char.less';

interface IDialogProps {
    documentCore: any;
    visible: boolean;
    id: string;
    close: (name: string | number, bRefresh?: boolean) => void;
}

interface IState {
    bRefresh: boolean;
}
export default class CharDialog extends React.Component<IDialogProps, IState> {
    private _tabDatas: string[];
    private _tabIndex: number;
    private _chars: string[][];
    private _domRef: any;
    private visible: boolean;
    constructor(props: any) {
        super(props);
        this.state = {
            bRefresh: false,
        };
        this._tabDatas = ['特殊字符', '罗马字符', '数学字符', '医学字符'];
        this._tabIndex = 0;
        const actChars = [];
        actChars[0] = chars.common.slice(0);
        actChars[1] = chars.rome.slice(0);
        actChars[2] = chars.math.slice(0);
        actChars[3] = chars.medicine.slice(0);
        this._chars = actChars;
        this._domRef = React.createRef();
        this.visible = this.props.visible;
    }

    public render(): any {
        return (
            <Dialog
                id={this.props.id}
                preventDefault={true}
                visible={this.visible}
                footer={this.renderFooter()}
                width={510}
                title={'特殊符号'}
            >
                <div className='char-container' ref={this._domRef}>
                    <div className='title-container'>
                        <ul>
                            {this.renderTabs()}
                        </ul>
                    </div>

                    <div className='char-content-container'>{this.renderContent()}</div>
                </div>
            </Dialog>
        );
    }

    public componentDidMount(): void {
        this._domRef.current.addEventListener('click', this.clickHandler);
        this.setState({bRefresh: !this.state.bRefresh});
    }

    public componentWillUnmount(): void {
        this._domRef.current?.removeEventListener('click', this.clickHandler);
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        this.visible = nextProps.visible;
    }

    private clickHandler = (event: Event): void => {
        const target = event.target as HTMLElement;
        const index = target.getAttribute('data-index');
        if (!index) {
            return;
        }
        if (target.nodeName === 'LI') {
            this.tabClick(+index);
            return;
        }

        this.itemClick(this._chars[this._tabIndex][index]);
    }

    private renderFooter(): any {
        return (
            <span>
                <Button onClick={this.close}>取消</Button>
            </span>
        );
    }

    private close = (bRefresh?: boolean): void => {
        this.visible = false;
        this.setState({bRefresh: !this.state.bRefresh});
        this.props.close(this.props.id, bRefresh);
    }

    private renderTabs(): any {
        const activeIndex = this._tabIndex;
        return this._tabDatas.map((tab, index) => {
            return (
                <li
                    key={tab}
                    className={index === activeIndex ? 'active' : null}
                    data-index={index}
                >
                    {tab}
                </li>
            );
        });
    }

    private renderContent(): any {
        const index = this._tabIndex;
        const datas: string[] = this._chars[index];
        return datas.map((data, dataIndex) => {
            return (
                <span key={dataIndex} data-index={dataIndex}>{data}</span>
            );
        });
    }

    private tabClick(index: number): void {
        this._tabIndex = index;
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private itemClick(item: string): void {
        this.props.documentCore.insertText(item);
        this.close(true);
    }

    // private confirm = (id?: number | string): void => {
    //     this.close();
    // }
}
