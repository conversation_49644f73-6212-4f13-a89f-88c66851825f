import React from 'react';
import { NewControlDefaultSetting, numtoFixed2 } from '../../../../common/commonDefines';

interface IProps {
    documentCore: any;
    scale: number;
    pageIndex: number;
    bCascadeManager: boolean;
}

export class NewControlCascadeBackground extends React.Component<IProps, {}> {
    constructor(props: IProps) {
        super(props);
    }

    public render(): any {
        const {documentCore, pageIndex, bCascadeManager} = this.props;
        const newControls = documentCore.getNewControlsWithoutFocus(pageIndex);
        const cascadeManager = documentCore.getAllCascadeManagers();
        if (!cascadeManager) {
            return [];
        }
        const { cascades, events } = cascadeManager;

        return newControls.map((newControl) => {
            if (bCascadeManager === true) {
                const name = newControl.getNewControlName();
                const hasCascade = cascades && cascades.includes(name) ? true : false;
                const hasSumCascade = events && events.includes(name) ? true : false;
                const fillColor = hasCascade && hasSumCascade ? '#FFE0A3'
                                    : hasCascade ? '#E5F5FF'
                                    : hasSumCascade ? '#D9FAE3'
                                    : '';
                if (fillColor === '') {
                    return null;
                }
                const newControlsBounds = documentCore.getNewControlBounds(name);
                if (newControlsBounds && 0 < newControlsBounds.bounds.length) {
                    // const newControl = documentCore.getNewControlByName(name);
                    // const bPopWinNewControl = newControl.isPopWindowNewControl();
                    // let fillColor = true !== bPopWinNewControl ? NewControlDefaultSetting.DefaultBackgroundColor :
                    // tslint:disable-next-line: max-line-length
                    //                                                   NewControlDefaultSetting.DefaultBackgroundColor2;
                    // fillColor = newControl.getBgColorOfSignStructs() === 1 ?
                    //     fillColor : NewControlDefaultSetting.DefaultBackgroundColorCanntEdit;

                    const items = [];
                    const paths = newControlsBounds['path'];
                    newControlsBounds.bounds.forEach((item) => {
                        if (paths && 0 < paths.length) {
                            const path = paths.find((value) => {
                                return item.pageIndex === value.pageIndex;
                            });

                            if (path && path.y + path.height <= item.y) {
                                return ;
                            } else if (path && path.y + path.height < item.y + item.height) {
                                item.height = path.y + path.height - item.y;
                            }

                            items.push(item);
                        } else {
                            if (item && (pageIndex === item.pageIndex || newControl['bHeaderFooter'])) {
                                items.push(item);
                            }
                        }
                    });
                    if (items.length > 0) {
                        return this.getPolygonRect(items, fillColor, newControl.getNewControlName());
                    }
                }
            }
        });
    }

    public refresh(): void {
        this.setState({});
    }

    private getPolygonRect(items: any[], color: string, name: string): any {
        const length = items.length - 1;
        const lefts: any[] = [];
        const rights: any[] = [];
        items.forEach((item, index) => {
            const x = item.x;
            const y = item.y;
            const x2 = x + item.width;
            const y2 = y + item.height;
            if (index === 0) {
                rights.push({x, y: y2});
                rights.push({x, y});
                rights.push({x: x2, y});
                rights.push({x: x2, y: y2});
                if (length === index) {
                    return;
                }
            } else if (index === length) {
                rights.push({x: x2, y});
                rights.push({x: x2, y: y2});
                lefts.unshift({x, y});
                lefts.unshift({x, y: y2});
            } else {
                rights.push({x: x2, y});
                rights.push({x: x2, y: y2});
                lefts.unshift({x, y});
                lefts.unshift({x, y: y2});
            }
        });

        const paths = rights.concat(lefts);
        let path = '';
        paths.forEach((item) => {
            path += `${numtoFixed2(item.x)},${numtoFixed2(item.y)} `;
        });
        const style = {
            fill: color, strokeWidth: '0px',
        };
        return (<polygon key={name} points={path} style={style} />);
    }
}
