/**
 * Frame的坐标
 */
export default class DocumentFrameBounds {
    public left: number;
    public top: number;
    public right: number;
    public bottom: number;

    constructor(left: number, top: number, right: number, bottom: number) {
        this.left = left;
        this.top = top;
        this.right = right;
        this.bottom = bottom;
    }

    public compare( bounds: DocumentFrameBounds ): boolean {
        if ( this.left === bounds.left && this.top === bounds.top &&
             this.right === bounds.right && this.bottom === bounds.bottom ) {
            return true;
        }

        return false;
    }

    public copy(): DocumentFrameBounds {
        return new DocumentFrameBounds(this.left, this.top, this.right, this.bottom);
    }

    public shift(shiftDx: number, shiftDy: number): void {
        this.left += shiftDx;
        this.top += shiftDy;
        this.right += shiftDx;
        this.bottom += shiftDy;
    }
}
