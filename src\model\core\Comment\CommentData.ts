import { idCounter } from "../util";

export class CommentData {
    public replies: CommentData[];
    private _id: number;
    private content: string;
    private time: Date;
    private lastTime: Date;
    private showTime: string;
    private userId: string;
    private userName: string;
    private bSolved: boolean;
    constructor(bCopy?: boolean) {
        this.time = new Date();
        this._id = idCounter.getNewId();
        this.lastTime = this.time;
        this.userId = '';
        this.userName = '';
        this.bSolved = false;
        this.replies = [];
        this.content = '';
        if (bCopy !== true) {
            this.setShowTime();
            this.content += this.showTime;
        }
    }

    public addReply(content: string): CommentData {
        const reply = new CommentData();
        reply.setContent(content);
        reply.setLastTime(new Date());
        this.replies.push(reply);
        return reply;
    }

    public deleteReply(id: number): boolean {
        const index = this.replies.findIndex((reply) => reply.id === id);
        if (index !== -1) {
            this.replies.splice(index, 1);
            return true;
        }
        return false;
    }

    public get id(): number {
        return this._id;
    }

    public getUserId(): string {
        return this.userId;
    }

    public setUserId(userId: string): boolean {
        if (userId === this.userId) {
            return false;
        }
        this.userId = userId;
        return true;
    }

    public getUserName(): string {
        return this.userName;
    }

    public setUserName(userName: string): boolean {
        if (userName && userName !== this.userName) {
            this.userName = userName;
            return true;
        }
        return false;
    }

    public getContent(): string {
        return this.content;
    }

    public setContent(text: string): boolean {
        if (text !== this.content) {
            this.content = text;
            return true;
        }

        return false;
    }

    public updateContent(info: {
        content?: string;
        id?: number;
        isSolved?: boolean;
    }): boolean {
        const {content, id, isSolved} = info;
        if (content !== undefined) {
            if (typeof content !== 'string' || content === this.content) {
                return false;
            }
            if (typeof id === 'number' && id !== this.id) {
                const reply = this.replies.find((r) => r.id === id);
                if (!reply) {
                    return false;
                }
                return reply.updateContent({content});
            }
            this.content = content;
        }
        if (isSolved !== undefined) {
            if (isSolved === this.bSolved) {
                return false;
            }
            this.bSolved = isSolved;
        }
        return true;
    }

    /**
     * 强制更新目标属性
     * @param info 属性对象
     */
    public updateForce(info: {
        userName?: string;
        isSolved?: boolean;
        time?: Date;
        content?: string;
    }): boolean {
        const {userName, isSolved, time, content} = info;
        if (content) {
            this.setContent(content);
        }
        if (time) {
            this.setLastTime(time);
        }
        if (userName) {
            this.setUserName(userName);
        }
        if (isSolved !== undefined) {
            this.setSolved(isSolved);
        }
        return true;
    }

    public getLastTime(): Date {
        return this.lastTime;
    }

    public setLastTime(date?: Date): boolean {
        if (date === this.lastTime) {
            return false;
        }
        date = date || (new Date());
        this.lastTime = date;
        this.setShowTime();
        return true;
    }

    public getStringTime(): string {
        return this.showTime;
    }

    public isSolved(): boolean {
        return this.bSolved;
    }

    public setSolved(bSolved: boolean): boolean {
        if (bSolved === this.bSolved) {
            return false;
        }

        this.bSolved = bSolved;
        return true;
    }

    public copy(): CommentData {
        const data = new CommentData(true);
        data.lastTime = this.lastTime;
        data.bSolved = this.bSolved;
        data.userId = this.userId;
        data.userName = this.userName;
        data.content = this.content;
        data.showTime = this.showTime;

        return data;
    }

    private setShowTime(): void {
        if (!this.lastTime) {
            this.showTime = '';
            return;
        }
        const date = this.lastTime;
        let sTime: string;
        const hour: number = date.getHours();
        const minute: number = date.getMinutes();
        if (hour < 10) {
            sTime = '0' + hour;
        } else {
            sTime = hour + '';
        }
        if (minute < 10) {
            sTime += ':0' + minute;
        } else {
            sTime += ':' + minute;
        }
        this.showTime = `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()} ${sTime}`;
    }
}
