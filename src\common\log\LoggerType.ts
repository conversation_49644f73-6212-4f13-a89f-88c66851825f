export enum LoggerType {
    // Info = 'info',
    // Warning,
    // Debug = 'debug',
    Interface = 'interface',
    Error = 'error',
    DevLog = 'devlog',
}

export interface ITable {
    tableName: string;
    option: {keyPath: string, autoIncrement: boolean};
    indexs: {key: string, option?: {unique: boolean}}[];
}

export interface IIndexDB {
    init(): Promise<boolean>;

    insert(tableName: string, items: any[]): Promise<void>;
}

export interface IDataBase {
    dbName: string;
    version: number;
    tables: ITable[];
}
