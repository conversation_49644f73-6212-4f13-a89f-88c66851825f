import { ChangeBaseBoolProperty, ChangeBaseContent } from '../HistoryChange';
import { HistroyItemType } from '../HistoryDescription';
import { NumberingManager } from './Manager';
import { Numbered } from './Num';

export class ChangeNumberingAddNum extends ChangeBaseContent {
    constructor( changeClass: NumberingManager, pos: number, items: Numbered[], color?: boolean ) {
        super(changeClass, pos, items, true);

        this.type = HistroyItemType.NumberingAddNum;
    }

    /**
     * 删除加入的items
     */
    public undo(): void {
        const manager = this.changeClass as NumberingManager;
        if ( this.items ) {
            this.items.forEach((item) => {
                manager.nums.delete(item.getId());
            });
        }
    }

    /**
     * 重新加入items
     */
    public redo(): void {
        const manager = this.changeClass as NumberingManager;
        if ( this.items ) {
            this.items.forEach((item) => {
                manager.nums.set(item.getId(), item);
            });
        }
    }

    public createReverseChange(): ChangeNumberingRemoveNum {
        return this.createReverseChangeBase(ChangeNumberingRemoveNum);
    }
}

export class ChangeNumberingRemoveNum extends ChangeBaseContent {
    constructor( changeClass: NumberingManager, pos: number, items: Numbered[], color?: boolean ) {
        super(changeClass, pos, items, true);

        this.type = HistroyItemType.NumberingRemoveNum;
    }

    /**
     * 删除加入的items
     */
    public undo(): void {
        const manager = this.changeClass as NumberingManager;
        if ( this.items ) {
            this.items.forEach((item) => {
                manager.nums.set(item.getId(), item);
            });
        }
    }

    /**
     * 重新加入items
     */
    public redo(): void {
        const manager = this.changeClass as NumberingManager;
        if ( this.items ) {
            this.items.forEach((item) => {
                manager.nums.delete(item.getId());
            });
        }
    }

    public createReverseChange(): ChangeNumberingAddNum {
        return this.createReverseChangeBase(ChangeNumberingAddNum);
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeNumberingAddLvl extends ChangeBaseContent {
    constructor( changeClass: Numbered, pos: number, items: any[], color?: boolean ) {
        super(changeClass, pos, items, true);

        this.type = HistroyItemType.NumberingAddLvl;
    }

    /**
     * 删除加入的items
     */
    public undo(): void {
        const num = this.changeClass as Numbered;
        const lvls = num.getAllLvl();
        lvls.splice(this.position, this.items.length);
        num.updateLvlTexts(this.position);
    }

    /**
     * 重新加入items
     */
    public redo(): void {
        const num = this.changeClass as Numbered;
        const lvls = num.getAllLvl();
        lvls.splice(this.position, 0, ...this.items);
        num.updateLvlTexts(this.position + this.items.length);
    }

    public createReverseChange(): ChangeNumberingRemoveLvl {
        return this.createReverseChangeBase(ChangeNumberingRemoveLvl);
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeNumberingRemoveLvl extends ChangeBaseContent {
    constructor( changeClass: Numbered, pos: number, items: any[], color?: boolean ) {
        super(changeClass, pos, items, true);

        this.type = HistroyItemType.NumberingRemoveLvl;
    }

    /**
     * 删除加入的items
     */
    public undo(): void {
        const num = this.changeClass as Numbered;
        const lvls = num.getAllLvl();
        lvls.splice(this.position, 0, ...this.items);
        num.updateLvlTexts(this.position + this.items.length);
    }

    /**
     * 重新加入items
     */
    public redo(): void {
        const num = this.changeClass as Numbered;
        const lvls = num.getAllLvl();
        lvls.splice(this.position, this.items.length);
        num.updateLvlTexts(this.position);
    }

    public createReverseChange(): ChangeNumberingAddLvl {
        return this.createReverseChangeBase(ChangeNumberingAddLvl);
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeNumberingParaNumTr extends ChangeBaseBoolProperty {
    private type: HistroyItemType;
    constructor( changeClass: any, old: any, news: any, color?: boolean ) {
        super(changeClass, old, news, true);

        this.type = HistroyItemType.NumberingChangeParaNumTr;
    }

    public setValue( value: {numPr: any, numbering: any} ): void {
        const {numPr, numbering} = value;
        const para: any = this.getClass();
        if ( para ) {
            para.paraProperty.numPr = numPr;
            para.numbering = numbering;
        }
    }
}
