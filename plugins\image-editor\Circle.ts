import { fabric } from 'fabric';

interface IProps {
  color: string;
  size: number;
  pattern: fabric.Pattern | string;
};

export function createCircle(x: number, y: number, props: IProps): fabric.Ellipse {
  const { color, size, pattern } = props;
  const circle = new fabric.Ellipse({
    left: x,
    top: y,
    originX: 'left',
    originY: 'top',
    rx: 0,
    ry: 0,
    angle: 0,
    fill: pattern,
    stroke: color,
    strokeWidth: size,
    type : 'ellipse',
  });
  return circle;
}

export function buildingCircle(ellipse: fabric.Ellipse, originX: number, originY: number, x: number, y: number): void {
  let rx = Math.abs(originX - x)/2;
  let ry = Math.abs(originY - y)/2;
  if (rx > ellipse.strokeWidth) {
    rx -= ellipse.strokeWidth/2;
  }
  if (ry > ellipse.strokeWidth) {
    ry -= ellipse.strokeWidth/2;
  }
  ellipse.set({ rx: rx, ry: ry});

  if(originX > x){
    ellipse.set({originX: 'right' });
  } else {
    ellipse.set({originX: 'left' });
  }
  if(originY > y){
    ellipse.set({originY: 'bottom'  });
  } else {
    ellipse.set({originY: 'top'  });
  }
}

export function updateCircleProps(circle: fabric.Ellipse, props: IProps): void {
  const { color, size, pattern } = props;
  circle.set({
    stroke: color,
    strokeWidth: size,
    fill: pattern
  });
}
