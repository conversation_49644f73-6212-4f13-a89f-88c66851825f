@import './global.less';
.hz-editor-container  .editor-checkbox {
    display: inline-block;
}
.hz-editor-container  .editor-checkbox .checkbox-item{
    display: inline-block;
    position: relative;
    margin-right: 15px;
    font-family: @fontFamily;
    vertical-align: middle;
    box-sizing: border-box;
    width: 100%;
    padding: 5px 0;

    & > input[type='checkbox'] {
        position: absolute;
        top: 0;
        width: 100%;
        // height: 14px;
        height: 100%;
        padding: 0;
        margin: 0;
        z-index: 2;
        cursor: pointer;
        opacity: 0;
    }

    & > i {
        display: inline-block;
        position: relative;
        width: 14px;
        height: 14px;
        margin-right: 6px;
        background: #fff;
        border: 1px solid @borderColor;
        font-style: normal;
        vertical-align: middle;
    }

    & > label {
        display: inline-block;
        // line-height: 14px;
        // font-size: 14px;
        vertical-align: middle;
        cursor: pointer;
        white-space: normal;
        position: relative;
    }

    &.disabled  {
        & > input[type=checkbox] {
            display: none;
        }
        & > label {
            cursor: default;
        }

        & > i {
            background-color: @disabledColor;
        }
    }

    .checkbox-icon-checked {
        width: 14px;
        height: 14px;
        background-color: @activeColor;
        border: none;
    }

    .checkbox-icon-checked:before,
    .checkbox-icon-checked:after {
        content: '';
        pointer-events: none;
        position: absolute;
        color: white;
        border: 1px solid;
        background-color: white;
    }

    .checkbox-icon-checked:before {
        width: 2px;
        height: 1px;
        left: 25%;
        top: 45%;
        border: none;
        border-bottom: 5px solid #fff;
        transform: rotate(-44deg);
    }

    .checkbox-icon-checked:after {
        width: 0px;
        height: 9px;
        left: 54%;
        top: 21%;
        border-bottom: 1px solid #fff;
        transform: rotate(44deg);
    }
}
