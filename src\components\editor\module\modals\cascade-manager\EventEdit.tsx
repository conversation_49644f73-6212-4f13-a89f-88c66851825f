import React from 'react';
import Input from '../../../ui/Input';
import {CascadeTriggerType, CodeAndValue, EventType, MixFormulaParser,
} from '../../../../../common/commonDefines';
import Select from '../../../ui/select/Select';
import { message } from '../../../../../common/Message';
import {SearchIcon} from './SearchID';

interface IPropKey {
    key: string;
    value: any;
    disabled?: boolean;
}

interface IProps {
    hasMix?: boolean;
    onClick: (e: any, data: any) => void;
}

export class EventEdit extends React.Component<IProps, {}> {
    private visible: boolean;
    private event: any;
    private actions: any[];
    private logicTexts: any[];
    private disabled: boolean;
    private callback: (data: any, type: number) => void;

    constructor(props: any) {
        super(props);

        this.actions = [{key: 'SelectedChanged', value: CascadeTriggerType.SelectChanged},
            {key: 'CheckChanged', value: CascadeTriggerType.CheckChanged},
            {key: 'TextChanged', value: CascadeTriggerType.TextChanged},
        ];
        this.logicTexts = [{key: '属性值相加', value: CodeAndValue.Value}, {key: '文本内容相加', value: CodeAndValue.Code}];

        this.event = undefined;
    }

    public render(): any {
        const logicTexts = [...this.logicTexts];
        let isExpression = false;
        if (this.props.hasMix) {
            logicTexts.push({key: '表达式计算（文本内容）', value: CodeAndValue.Expression})
            isExpression = this.event?.key === CodeAndValue.Expression;
        }
        if (!this.event) {
            return (
            <div className='newcontrol-cascade cascade-manager'>
                <div className='no-data prop-add' onClick={this.add}>点击添加</div>
            </div>
            );
        }
        const target = this.event.target;
        return (
            <div className='cascade-event'>
                <div className='editor-line'>
                    触发源(根据触发动作自动汇总)  
                </div>
                <div className='editor-line cascade-event-target'>
                    <Input
                        value={target}
                        name='target'
                        onChange={this.onChange}
                        disabled={true}
                    />
                    <div className={target ? 'active' : ''}>{target}</div>
                </div>
                <div className='editor-line'>
                    <span className='w-150'>逻辑</span>
                    
                </div>
                <div className='editor-line'>    
                    <Select
                        data={logicTexts}
                        value={this.event.key}
                        name='key'
                        onChange={this.onChange}
                        disabled={this.disabled}
                    />
                </div>
                {
                    isExpression &&
                    <>
                        <div className="editor-line" style={{height: 0, margin: 0}}>
                            <span className='w-150'></span>
                            <div className='right-auto' style={{ width: '408px', position: 'relative' }}>
                                <div className="helptip" style={{transform: 'translateY(-14px)'}}>?</div>
                                <div className="message" style={{top: '14px'}}>
                                    <span>元素名引用:</span><span>"numbox1"/"[numbox1]"/"[numbox-one]"</span>
                                    <span>表达式示例:</span><span>10*numbox1/(1+[numbox-one]/2)</span>
                                </div>
                            </div>
                        </div>
                        <div className='editor-line'>
                            <span className='w-150'>计算表达式</span>
                            <div className='right-auto' style={{ width: '408px' }}>
                                <span style={{ width: '380px', display: 'inline-block' }}>
                                    <Input
                                        value={this.event.expression}
                                        name='expression'
                                        onChange={this.onChange}
                                        placeholder='点击右侧按钮，开始录入表达式计算公式'
                                        readonly={true}
                                    />
                                </span>
                                <div style={{ display: 'inline-block' }}>
                                    <SearchIcon onClick={this.searchClick.bind(this, -1)} />
                                </div>
                            </div>
                        </div>
                    </>
                }
                <div className='editor-line'>
                    触发动作
                </div>
                <div className='editor-line editor-multi-line'>
                    <ul>
                        {this.renderList(isExpression)}
                    </ul>
                </div>
            </div>
        );
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        this.visible = nextProps.visible;
    }

    public open = (props: any, disabled: boolean, callback: any, type?: number): void => {
        this.callback = callback;
        this.disabled = disabled;
        const options = props;
        if (!options) {
            this.event = undefined;
            this.setState({});
            return;
        }

        this.event = options;
        this.setState({});
    }

    public close = () => {
        this.event = {};
    }

    public validData(): boolean {
        if (this.disabled) {
            return true;
        }
        const options = this.event;
        if (!options.target) {
            message.error(`触发源不能为空`);
            return false;
        }

        const datas = this.event.event;
        if (datas.length === 0) {
            return true;
        }

        for (let index = datas.length - 1; index >= 0; index--) {
            const data = datas[index];
            if (!data.source) {
                message.error(`求和第${index + 1}行的触发动作不能为空，请重新填写`, {time: 10000});
                return false;
            }
        }
        return true;
    }

    /** 校验混合计算表达式 */
    public validateMixExpression(checkControl: (names: string[]) => string[]): boolean {
        const { expression } = this.event;
        if (!expression) {
            message.error('表达式不能为空');
            return false;
        }
        const { toInfixExpressions, validateInfixExpression } = MixFormulaParser;
        const { infixes, elemNames } = toInfixExpressions(expression);
        if (validateInfixExpression(infixes)) {
            const errNames = checkControl(elemNames);
            if (errNames.length) {
                message.error(`元素[${errNames.join(',')}]必须为数值框`);
                return false;
            }
            this.event.target = elemNames.join(',');
            return true;
        }
        message.error(`表达式"${expression}"格式不正确`);
        return false;
    }

    private add = (): void => {
        this.event = {
            action: EventType.Sum,
            key: 'value',
            event: [{triggerType: CascadeTriggerType.TextChanged, source: ''}],
        };
        this.callback(this.event, 1);
        this.setState({});
    }

    private renderList(isExpression: boolean): any {
        if (!this.event.event) {
            return;
        }

        if (isExpression) { // 混合运算
            return (
                <li>
                    <div>
                        <span>
                            <Input
                                value='*'
                                name='source'
                                disabled={true}
                            />
                        </span>
                    </div>
                    <div>
                        <div className='event-select'>
                            <Select
                                data={this.actions}
                                value={CascadeTriggerType.TextChanged}
                                name='triggerType'
                                disabled={true}
                            />
                        </div>
                        <div className='event-btn' style={{display: this.disabled ? 'none' : ''}}>
                            {this.renderIcon(0)}
                        </div>
                    </div>
                    
                </li>
            );
        }

        return this.event.event.map((event, index) => {
            return (
                <li key={index}>
                    <div>
                        <span>
                            <Input
                                value={event.source}
                                name='source'
                                onChange={this.listChange.bind(this, event)}
                                disabled={this.disabled}
                                readonly={true}
                                placeholder={''}
                            />
                        </span>
                        <div>
                            <SearchIcon onClick={this.searchClick.bind(this, index)} />
                        </div>
                    </div>
                    <div>
                        <div className='event-select'>
                            <Select
                                data={this.actions}
                                value={event.triggerType}
                                name='triggerType'
                                onChange={this.listChange.bind(this, event)}
                                disabled={this.disabled}
                            />
                        </div>
                        <div className='event-btn' style={{display: this.disabled ? 'none' : ''}}>
                            <i onClick={this.addData.bind(this, index)}>+</i>
                            {this.renderIcon(index)}
                        </div>
                    </div>
                    
                </li>
            );
        });
    }

    private renderIcon(index: number): any {
        // if (index === 0) {
        //     return;
        // }

        return (<i onClick={this.deleteData.bind(this, index)}>-</i>);
    }

    private listChange = (event: any, value: any, name: string): void => {
        event[name] = value;
        this.setState({});
    }

    private onChange = (value: any, name: string): void => {
        this.event[name] = value;
        if (name === 'key') {
            this.setState({});
        }
    }

    private addData = (index?: number): void => {
        const data = this.event.event[index];
        if (data) {
            if (data.source === '*') {
                return;
            }
        }
        this.event.event.splice(index + 1, 0, {triggerType: CascadeTriggerType.SelectChanged});
        this.setState({});
    }

    private deleteData = (index: number): void => {
        this.event.event.splice(index, 1);
        if (this.event.event.length === 0) {
            this.event = undefined;
            this.callback(null, 2);
        }
        this.setState({});
    }

    private searchClick = (index: number, e: any) => {
        if (this.props.onClick) {
            this.props.onClick(e, this.event.event[index]);
        }
    }
}
