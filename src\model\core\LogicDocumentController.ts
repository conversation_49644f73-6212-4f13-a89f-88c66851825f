import DocumentControllerBase from './DocumentControllerBase';
import Document from './Document';
import { ICurrentCursorPos } from './Paragraph/ParagraphSearchPosXY';
import { EquationType, ICommentProperty, ImageMediaType, INewControlProperty, IRevisionChange } from '../../common/commonDefines';
import { ParaElementBase } from './Paragraph/ParaElementBase';
import { ICursorProperty } from '../CursorProperty';
import TextProperty from './TextProperty';
import MouseEventHandler, { IMouseEvent } from '../../common/MouseEventHandler';
import { IDrawSelectionBounds } from '../DocumentCore';
import DocumentContentElementBase from './DocumentContentElementBase';
import Selection from './Selection';
import Paragraph from './Paragraph';
import { NewControl } from './NewControl/NewControl';
import { Region } from './Region';
import { DocumentElementState } from './HistoryState';

/**
 * Special class handler commands for the main part of the document
 * @param LogicDocument - Link to the main document
 */
export default class LogicDocumentController extends DocumentControllerBase {
  constructor(logicDocument: Document) {
    super(logicDocument);
  }

  public recalculateCurPos(): ICurrentCursorPos {
    return this.logicDocument.recalculateCurPosController();
  }

  public getCurPage(): number {
    return this.logicDocument.getCurPageController();
  }

  public addNewParagraph(): number {
    return this.logicDocument.addNewParagraphController();
  }

  public addSoftNewParagraph(): number {
    return this.logicDocument.addSoftNewParagraphController();
  }

  public canCopy(): boolean {
    return this.logicDocument.isCanCopy();
  }

  public isNotOverNewControl(): boolean {
    return this.logicDocument.unOverNewControl();
  }

  public addInlineImage(width: number, height: number, src: string,
                        name?: string, type?: EquationType, svgElem?: any,
                        mediaType?: ImageMediaType, mediaSrc?: string, datas?: any): string {
    return this.logicDocument.addInlineImageController(width, height, src, name, type,
                                svgElem, mediaType, mediaSrc, datas);
  }

  public addInlineTable(cols: number, rows: number, tableHeaderNum?: number,
                        tableName?: string, bRepeatHeader?: boolean): boolean {
    return this.logicDocument.addInlineTableController(cols, rows, tableHeaderNum, tableName, bRepeatHeader);
  }

  public getParagraphProperty(): any {
    return this.logicDocument.getParagraphPropertyController();
  }

  public getSelectText(): string {
    return this.logicDocument.getSelectTextController();
  }

  public getCurrentTextProps(): any {
    return this.logicDocument.getCurrentTextPropsController();
  }

  public addToParagraph(paraItem: ParaElementBase, bRecal?: boolean): number {
    return this.logicDocument.addToParagraphController(paraItem, bRecal);
  }

  public getParaContentByXY(mouseEvent: MouseEventHandler, pageIndex: number): any {
    return this.logicDocument.getParaContentByXYController(mouseEvent, pageIndex);
  }

  public remove(direction: number, bOnlyText: boolean, bOnlySelection?: boolean, bAddText?: boolean): any {
    return this.logicDocument.removeController(direction, bOnlyText, bOnlyText, bAddText);
  }

  public getCursorPosXY(): ICursorProperty {
    return this.logicDocument.getCursorPosXYController();
  }

  public moveCursorToStartPos(addToSelect: boolean = false): void {
    return this.logicDocument.moveCursorToStartPosController(addToSelect);
  }

  public moveCursorToEndPos(addToSelect: boolean = false): void {
    // todo
  }

  public moveCursorLeft(bShiftLey: boolean  = false): void {
    return this.logicDocument.moveCursorLeftController(bShiftLey);
  }

  public moveCursorRight(bShiftLey: boolean  = false): void {
    return this.logicDocument.moveCursorRightController(bShiftLey);
  }

  public getNewControlBySelectArea(): string {
    return this.logicDocument.getNewControlBySelectAreaBase();
  }

  public moveCursorUp(bShiftLey: boolean  = false): void {
    return this.logicDocument.moveCursorUpController(bShiftLey);
  }

  public moveCursorDown(bShiftLey: boolean  = false): void {
    return this.logicDocument.moveCursorDownController(bShiftLey);
  }

  public moveCursorToEndOfLine(bShiftLey: boolean  = false): boolean {
    // todo
    return false;
  }

  public moveCursorToStartOfLine(bShiftLey: boolean  = false): boolean {
    // todo
    return false;
  }

  public moveCursorToXY(pageIndex: number, pointX: number, pointY: number, bAddSelect: boolean = false): void {
    return this.logicDocument.moveCursorToXYController(pageIndex, pointX, pointY, bAddSelect);
  }

  public moveCursorToCell(): void {
    // todo
  }

  public setParagraphAlignment(alignment: number): number {
    return this.logicDocument.setParagraphAlignmentController(alignment);
  }

  public getDirectParaPr(): void {
    // todo
  }

  public getDirectTextProperty(): TextProperty {
    return this.logicDocument.getDirectTextPropertyController();
  }

  public removeSelection(): void {
    return this.logicDocument.removeSelectionController();
  }

  public isSelectionEmpty(): boolean {
    return this.logicDocument.isSelectionEmptyController();
  }

  public getSelectionBounds(mouseEvent?: IMouseEvent, pageIndex?: number): IDrawSelectionBounds {
    return this.logicDocument.getSelectionBoundsController(mouseEvent, pageIndex);
  }

  public checkPosInSelection( pageIndex: number, pointX: number, pointY: number ): boolean {
    return this.logicDocument.checkPosInSelectionController(pageIndex, pointX, pointY);
  }

  public selectAll(): void {
    return this.logicDocument.selectAllController();
  }

  public getSelectedContent(bKeepHalfStructBorder: boolean = false,
                            bSelectedContentToSave: boolean = false): DocumentContentElementBase[] {
    return this.logicDocument.getSelectedContentController(bKeepHalfStructBorder, bSelectedContentToSave);
  }

  public updateCursorType(pointX: number, pointY: number, pageIndex: number, mouseEvent?: IMouseEvent): void {
    return this.logicDocument.updateCursorTypeController(pointX, pointY, pageIndex, mouseEvent);
  }

  public isSelectionUse(): boolean {
    return this.logicDocument.isSelectionUseController();
  }

  public isTextSelectionUse(): boolean {
    // todo
    return false;
  }

  public getCurPosXY(): {x: number; y: number} {
    // todo
    return {x: 0, y: 0};
  }

  public getSelectedText(bClearText: boolean, oPr: any): string {
    // todo
    return '';
  }

  public getCurrentParagraph(): Paragraph {
    return this.logicDocument.getCurrentParagraphController();
  }

  public getSelectedElementsInfo(oInfo: any): void {
    // todo
  }

  public addTableRow(bBefore: boolean): boolean {
    return this.logicDocument.addTableRowController(bBefore);
  }

  public removeTableRow(rowIndex?: number): boolean {
    return this.logicDocument.removeTableRowController(rowIndex);
  }

  public removeTableColumn(): boolean {
    return this.logicDocument.removeTableColumnController();
  }

  public mergeTableCells(bClearMerge?: boolean): boolean {
    return this.logicDocument.mergeTableCellsController(bClearMerge);
  }

  public splitTableCells(cols: number, rows: number): void {
    // todo
  }

  public removeTable(): boolean {
    return this.logicDocument.removeTableController();
  }

  public canMergeTableCells(): boolean {
    // todo
    return false;
  }

  public updateSelectionState(): void {
    return this.logicDocument.updateSelectionStateController();
  }

  public getSelectionState(): any {
    // todo
    return [];
  }

  public setSelectionState(state: any, stateIndex: number): void {
    // todo
  }

  public addComment(props?: ICommentProperty): string {
    return this.logicDocument.addCommentController(props);
  }

  public canAddComment(): boolean {
    // todo
    return false;
  }

  // public isPopWinNewControl(): boolean {
  //   return this.logicDocument.isPopWinNewControlController();
  // }

  public canInput(): boolean {
      return this.logicDocument.canInputController();
  }

  public canDelete(direction?: number): boolean {
    return this.logicDocument.canDeleteController(direction);
  }

  public canInsertNewControl(): boolean {
      return this.logicDocument.canInsertNewControlController();
  }

  public startSelectionFromCurPos(): void {
    // todo
  }

  public getCurrentSectionPr(): void {
    // todo
    return null;
  }

  public addNewControl(property: INewControlProperty, sText?: string): number {
    return this.logicDocument.addNewControlController(property, sText);
  }

  public isCursorInNewControl(): boolean {
    return this.logicDocument.isCursorInNewControlController();
  }

  // BELOW ARE CUSTOM METHODS

  public getDocumentSelection(): Selection {
    return this.logicDocument.getDocumentSelectionController();
  }

  public getContentPosByXY(pageIndex: number, pointX: number, pointY: number): number {
    return this.logicDocument.getContentPosByXYController(pageIndex, pointX, pointY);
  }

  public isFocusInNewControl(mouseEvent?: MouseEventHandler, pageIndex?: number): boolean {
    return this.logicDocument.isFocusInNewControlController(mouseEvent, pageIndex);
  }

  public getFocusInNewControl(mouseEvent?: MouseEventHandler, pageIndex?: number): NewControl {
    return this.logicDocument.getFocusInNewControlController(mouseEvent, pageIndex);
  }

  public getFoucsInRegion(mouseEvent: MouseEventHandler, pageIndex: number): Region {
      return this.logicDocument.getFoucsInRegionController(mouseEvent, pageIndex);
  }

  public getCursorInNewControl(): NewControl {
    return this.logicDocument.getCursorInNewControlController();
  }

  public getDocumentElementState(): DocumentElementState[][] {
    return this.logicDocument.getDocumentElementStateController();
  }

  public setDocumentElementState( state: any, stateIndex: number ): void {
    return this.logicDocument.setDocumentElementStateController(state, stateIndex);
  }

  public getDocumentContent(): DocumentContentElementBase[] {
    return this.logicDocument.getDocumentContentController();
  }

  public getFocusRevision(mouseEvent: MouseEventHandler, pageIndex: number): IRevisionChange[] {
      return this.logicDocument.getFocusRevisionController(mouseEvent, pageIndex);
  }

  public getAllRevision(elemIds?: number[]): IRevisionChange[] { 
    return this.logicDocument.getAllRevisionController(elemIds);
  }

  public getTableCellByXY(pointX: number, pointY: number, pageIndex: number): any {
      return this.logicDocument.getTableCellByXYController(pointX, pointY, pageIndex);
  }

}
