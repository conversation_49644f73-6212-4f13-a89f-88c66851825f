import * as React from 'react';
import Dialog from '../../ui/Dialog';
import {message} from '../../../../common/Message';
import Input from '../../ui/Input';
import Checkbox from '../../ui/CheckboxItem';
import Button from '../../ui/Button';
import { ImageMediaType, isValidName } from '../../../../common/commonDefines';
import { IFRAME_MANAGER } from '../../../../common/IframeManager';

interface IProps {
    documentCore: any;
    visible: boolean;
    id: string;
    image?: any;
    close: (name: string | number, bRefresh?: boolean) => void;
}

interface IState {
    bRefresh: boolean;
}

export default class Image extends React.Component<IProps, IState> {
    private image: {imageName: string, imageSource: string, imageWidth: number, imageHeight: number,
        imageSizeLocked: boolean, imageDeleteLocked: boolean,
        imageCopyLocked: boolean, imagePreserveAspectRatio: boolean,
        title: string, alignment: number};

    private showImageError: string = '';
    private imageName: string;
    private imageSizeRatio: number;
    private timeout: any;
    private visible: boolean;
    constructor(props: any) {
        super(props);
        this.state = {
            bRefresh: false,
        };
        this.image = {
            imageName: '',
            imageSource: '',
            imageWidth: 0,
            imageHeight: 0,
            imageSizeLocked: false,
            imageDeleteLocked: false,
            imageCopyLocked: false,
            imagePreserveAspectRatio: false,
            title: '图片',
            alignment: 2,
        };
        this.imageSizeRatio = 1;
        this.visible = this.props.visible;
    }

    public render(): any {
        // TODO: bSignPic?

        return (
            <Dialog
                visible={this.visible}
                width={350}
                open={this.open}
                title={this.image.title + '设置'}
                footer={this.renderFooter()}
                id={this.props.id}
            >
                <div>
                    <div className='editor-line'>
                        <div className='w-70'>{this.image.title}名称</div>
                        <div className='right-auto'>
                            <Input
                                value={this.image.imageName}
                                onChange={this.onChange}
                                name='imageName'
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <div className='w-70'>{this.image.title}路径</div>
                        <div className='right-auto'>
                            <Input
                                disabled={true}
                                value={this.image.imageSource}
                                onChange={this.onChange}
                                name='imageSource'
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <div className='w-70'>宽度</div>
                        <div className='right-auto'>
                            <Input
                                type='number'
                                disabled={this.image.imageSizeLocked === true}
                                value={this.image.imageWidth}
                                onChange={this.onChange}
                                name='imageWidth'
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <div className='w-70'>高度</div>
                        <div className='right-auto'>
                            <Input
                                value={this.image.imageHeight}
                                type='number'
                                disabled={this.image.imageSizeLocked === true}
                                onChange={this.onChange}
                                name='imageHeight'
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <span className='title'>高级</span>
                    </div>
                    <div className='editor-line'>
                        <div className='w-050'>
                            <Checkbox
                                name='imagePreserveAspectRatio'
                                onChange={this.preserveAspectRatioChange}
                                value={this.image.imagePreserveAspectRatio}
                            >
                                保持比例
                            </Checkbox>
                        </div>
                        <div className='w-050'>
                            <Checkbox
                                name='imageSizeLocked'
                                onChange={this.onChange}
                                value={this.image.imageSizeLocked}
                            >
                                大小保护
                            </Checkbox>
                        </div>
                    </div>
                    <div className='editor-line'>
                        <div className='w-050'>
                            <Checkbox
                                name='imageDeleteLocked'
                                onChange={this.onChange}
                                value={this.image.imageDeleteLocked}
                            >
                                删除保护
                            </Checkbox>
                        </div>
                        <div className='w-050'>
                            <Checkbox
                                name='imageCopyLocked'
                                onChange={this.onChange}
                                value={this.image.imageCopyLocked}
                            >
                                拷贝保护
                            </Checkbox>
                        </div>
                    </div>
                </div>
            </Dialog>
        );
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        this.visible = nextProps.visible;
    }

    private renderFooter(): any {
        return (
            <span>
                <Button onClick={this.close}>取消</Button>
                <Button type='primary' onClick={this.confirm}>确认</Button>
            </span>
        );
    }

    private close = (bRefresh?: boolean): void => {
        this.visible = false;
        this.setState({bRefresh: !this.state.bRefresh});
        this.props.close(this.props.id, bRefresh);
    }

    private open = (): void => {
        let file = this.props.image;
        if (!file) {
            const documentCore = this.props.documentCore;
            file = documentCore.getSelectedDrawing();
        }

        if (!file) {
            return;
        }
        const image = this.image;
        this.imageName = image.imageName = file.name;
        image.imageSource = file.src;
        image.imageWidth = file.width;
        image.imageHeight = file.height;
        image.imagePreserveAspectRatio = file.preserveAspectRatio;
        image.imageSizeLocked = file.sizeLocked;
        image.imageDeleteLocked = file.deleteLocked;
        image.imageCopyLocked = file.copyProtect;
        this.imageSizeRatio = file.imageRatio || 1;
        image.title = '图片';
        image.alignment = file.vertAlign;
        if (file.mediaType) {
            image.imageSource = file.mediaSrc;
            const mtype = file.mediaType;
            image.title = mtype === ImageMediaType.Audio ? '音频'
                        : mtype === ImageMediaType.Video ? '视频'
                        : image.title;
        }

        this.setState({bRefresh: !this.state.bRefresh});
    }

    private onChange = (value: any, name: string): void => {
        this.image[name] = value;
        if (name === 'imageHeight' || name === 'imageWidth') {
            this.setImageSize(name);
        }
        if (name === 'imageSizeLocked') {
            this.setState({bRefresh: !this.state.bRefresh});
        }
    }

    private preserveAspectRatioChange = (value: any, name: string): void => {
        this.image[name] = value;
        this.setImageSize();
    }

    // private onBlur = (name: string): void => {
    //     this.setImageSize(name);
    // }

    private setImageSize(name?: string): void {
        if (this.image.imagePreserveAspectRatio !== true) {
            return;
        }
        clearTimeout(this.timeout);
        this.timeout = setTimeout(() => {
            const image = this.image;
            let result = 1;
            switch (name) {
                case 'imageHeight':
                    result = Math.round(image.imageHeight * this.imageSizeRatio);
                    if (result < 1) {
                        result = 1;
                    }
                    image.imageWidth = result;
                    break;
                default:
                    // imageWidth && imagePreserveAspectRatio
                    // imagePreserveAspectRatio: in case from false -> true,
                    // need to recalculate height based on current width
                    result = Math.round(image.imageWidth / this.imageSizeRatio);
                    if (result < 1) {
                        result = 1;
                    }
                    image.imageHeight = result;
                    break;
            }
            this.setState({bRefresh: !this.state.bRefresh});
        }, 100);
    }

    private confirm = (): void => {
        const documentCore = this.props.documentCore;
        if (documentCore.isProtectedMode()) {
            this.close(true);
            return;
        }
        IFRAME_MANAGER.setDocId(documentCore.getCurrentId());
        const image = this.image;
        if (!isValidName(image.imageName)) {
            message.error('名称不符合规范，请重新输入');
            return;
        }
        if (!documentCore.checkUniqueImageNameOtherThanSelectedImage(image.imageName)) {
            message.error('名称不符合规范，请重新命名');
            return null;
        // } else {
            // const curParaDrawing = documentCore.getSelectedDrawing();

            // if (curParaDrawing.name !== this.image.imageName) {
            //     const graphicObjects = documentCore.getDrawingObjects();
            //     // old image name object
            //     const paraDrawingFromMap = graphicObjects.getDrawingByName(curParaDrawing.name);
            //     // remove pair of old image name
            //     if (graphicObjects.deleteImage(curParaDrawing.name) && paraDrawingFromMap) {
            //         if (curParaDrawing === paraDrawingFromMap) { // same reference?
            //             curParaDrawing.setName(this.image.imageName);
            //             // add new image name pair
            //             graphicObjects.addGraphicObject(paraDrawingFromMap);
            //         } else {
            //             return null;
            //         }
            //     } else {
            //         // cannot find pair
            //         return null;
            //     }
            // }
        }

        if (!image.imageWidth || 0 > image.imageWidth) {
            message.error('宽度数值需大于0，请重新设置');
            return null;
        }

        if (!image.imageHeight || 0 > image.imageHeight) {
            message.error('数值数值需大于0，请重新设置');
            return null;
        }
        const curParaDrawing = documentCore.getSelectedDrawing();
        documentCore.setDrawingProp(curParaDrawing.getDrawingName(), {name: this.image.imageName,
            width: image.imageWidth, height: image.imageHeight, preserveAspectRatio: image.imagePreserveAspectRatio,
            sizeLocked: image.imageSizeLocked, deleteLocked: image.imageDeleteLocked,
            copyProtect: image.imageCopyLocked, vertAlign: image.alignment});

        this.close(true);
    }
}
