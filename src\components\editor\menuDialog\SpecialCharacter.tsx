import * as React from 'react';
import Dialog from '../dialog/dialog';
import chars from '../text/chars';

interface IDialogProps {
    documentCore: any;
    visible: boolean;
    close: (name: string | number, bReflash?: boolean) => void;
    children: React.ReactNode;
}

interface IState {
    bReflash: boolean;
}
export default class CharDialog extends React.Component<IDialogProps, IState> {
    private _tabDatas: string[];
    private _tabIndex: number;
    private _chars: string[][];
    private _domRef: any;
    constructor(props: any) {
        super(props);
        this.state = {
            bReflash: false,
        };
        this._tabDatas = ['特殊字符', '罗马字符', '数学字符', '医学字符'];
        this._tabIndex = 0;
        const actChars = [];
        actChars[0] = chars.common.slice(0);
        actChars[1] = chars.rome.slice(0);
        actChars[2] = chars.math.slice(0);
        actChars[3] = chars.medicine.slice(0);
        this._chars = actChars;
        this._domRef = React.createRef();
    }

    public render(): any {
        return (
            <Dialog
                id='chars'
                preventDefault={true}
                close={this.close}
                open={this.open}
                confirm={this.confirm}
                visible={this.props.visible}
                top='middle'
                width={510}
                height={408}
                title={'特殊符号'}
            >
                <div className='char-container' ref={this._domRef}>
                    <div className='char-title-container'>
                        <ul>
                            {this.renderTabs()}
                        </ul>
                    </div>

                    <div className='char-content-container'>{this.renderContent()}</div>
                </div>
            </Dialog>
        );
    }

    public componentDidMount(): void {
        if (this._domRef.current) {
            this._domRef.current.addEventListener('click', this.clickHandler);
        }
    }

    public componentWillUnmount(): void {
        if (this._domRef.current) {
            this._domRef.current.removeEventListener('click', this.clickHandler);
        }
    }

    public clickHandler = (event: Event): void => {
        const target = event.target as HTMLElement;
        const index = target.getAttribute('data-index');
        if (!index) {
            return;
        }
        if (target.nodeName === 'LI') {
            this.tabClick(+index);
            return;
        }

        this.itemClick(this._chars[this._tabIndex][index]);
    }
    private renderTabs(): any {
        const activeIndex = this._tabIndex;
        return this._tabDatas.map((tab, index) => {
            return (
                <li
                    key={tab}
                    className={index === activeIndex ? 'active' : null}
                    data-index={index}
                >
                    {tab}
                </li>
            );
        });
    }

    private renderContent(): any {
        const index = this._tabIndex;
        const datas: string[] = this._chars[index];
        return datas.map((data, dataIndex) => {
            return (
                <span key={dataIndex} data-index={dataIndex}>{data}</span>
            );
        });
    }

    private tabClick(index: number): void {
        this._tabIndex = index;
    }

    private itemClick(item: string): void {
        this.props.documentCore.insertText(item);
        this.close(null, true);
    }

    private close = (id?: any, bReflash?: boolean): void => {
        this.props.close(id, bReflash);
        // this.clearFont();
    }

    private open = (): void => {}

    private confirm = (id?: number | string): void => {
        this.close();
    }
}
