import ParaText from './ParaText';
import { ParaTextName } from './ParagraphContent';
import History from '../History';
import { ChangeParaTextReplaceText } from './PortionChange';

// 对ParaText进行一些字段扩招
export default class ParaTextExtend extends ParaText {
    private _name: ParaTextName;
    private _value: any;

    constructor(text: string, name?: ParaTextName, red?: boolean) {
        super(text);
        this._name = name;
    }

    public getTypeName(): ParaTextName {
        return this._name;
    }

    public setValue(value: any): void {
        this._value = value;
    }

    public getValue(): any {
        return this._value;
    }

    public repalceText(text: string, history?: History): void {
        if (history) {
            history.addChange(new ChangeParaTextReplaceText(this, this.content, text));
        }

        this.content = text;
    }
}
