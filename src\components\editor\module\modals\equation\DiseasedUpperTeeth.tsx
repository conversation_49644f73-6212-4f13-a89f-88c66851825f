import * as React from 'react';
import Dialog from '../../../ui/Dialog';
import Input from '../../../ui/Input';
import Button from '../../../ui/Button';
// import { message } from '../../../../../common/Message';
import { ParaEquation } from '../../../../../model/core/Paragraph/ParaDrawing';
import {IFRAME_MANAGER} from '../../../../../common/IframeManager';

interface IDialogProps {
    documentCore: any;
    visible: boolean;
    id: string;
    equation: ParaEquation;
    close: (name: string | number, bRefresh?: boolean) => void;
}

interface ITestContent {
    value1?: string;
    value2?: string;
    value3?: string;
}
interface IState {
    bRefresh: boolean;
}
export default class DiseasedUpperTeeth extends React.Component<IDialogProps, IState> {
    private visible: boolean;
    private data: ITestContent;
    private equationElemDom: HTMLElement;
    private oldData: ITestContent;
    constructor(props: any) {
        super(props);
        this.visible = this.props.visible;
        this.data = {};
        this.state = {
            bRefresh: false,
        };
    }

    public render(): any {
        return (
            <Dialog
                id={this.props.id}
                preventDefault={true}
                visible={this.visible}
                open={this.open}
                footer={this.renderFooter()}
                width={260}
                height={300}
                title={'病变上牙牙位图'}
            >
                <div className='diseased-upper-teeth'>
                    <div>
                        <div className='value1'>
                            <Input
                                name='value1'
                                min={0}
                                max={9}
                                type={'number'}
                                onChange={this.onChange}
                                value={this.data.value1}
                            />
                        </div>
                        <div className='value2'>
                            <Input
                                name='value2'
                                min={0}
                                max={9}
                                type={'number'}
                                onChange={this.onChange}
                                value={this.data.value2}
                            />
                        </div>
                        <div className='value3'>
                            <Input
                                name='value3'
                                min={0}
                                max={9}
                                type={'number'}
                                onChange={this.onChange}
                                value={this.data.value3}
                            />
                        </div>
                    </div>
                    <span className='line-v-1'/>
                    <span className='line-v-2'/>
                    <span className='line-h'/>
                </div>

            </Dialog>
        );
    }

    public componentDidMount(): void {
        //
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        this.visible = nextProps.visible;
    }

    private renderFooter(): any {
        return (
            <span>
                <Button onClick={this.close}>取消</Button>
                <Button type='primary' onClick={this.confirm}>确认</Button>
            </span>
        );
    }

    private open = (): void => {
        const equation = this.props.equation;
        // let equationElemDom: HTMLElement;
        const data = this.data = {};
        const oldData = this.oldData = {};
        const svg = equation.equationElem;
        if (svg) {
            const equationElemDom = this.equationElemDom = new DOMParser().parseFromString(
                svg,
                'text/xml',
            ).documentElement;
            const texts = equationElemDom.querySelectorAll('text');
            if (texts.length > 0) {
                texts.forEach((text, index) => {
                    const curText = text.innerHTML.trim();
                    data['value' + ++index] = curText;
                    oldData['value' + index] = curText;
                });
            }
        }

        this.setState({});
    }

    private onChange = (value: any, name: string): void => {
        this.data[name] = value;
        this.setState({});
    }

    private close = (bRefresh: boolean = false): void => {
        this.props.close(this.props.id, bRefresh);
        this.visible = false;
        this.setState({});
    }

    private isSameData(): boolean {
        const datas = this.data;
        const oldDatas = this.oldData;

        const keys = Object.keys(datas);
        for (let index = 0, length = keys.length; index < length; index++) {
            const key = keys[index];
            if (datas[key] !== oldDatas[key]) {
                return false;
            }
        }

        return true;
    }

    private confirm = (): void => {
        if (this.isSameData()) {
            this.close();
            return;
        }
        const documentCore = this.props.documentCore;
        const datas = this.data;
        const dom = this.equationElemDom;
        const texts = dom.querySelectorAll('text');
        IFRAME_MANAGER.setDocId(documentCore.getCurrentId());
        const textDom = IFRAME_MANAGER.getLabelElement(); // IFRAME_MANAGER.getTextElement();
        const oldData = this.oldData;
        Object.keys(datas)
        .forEach((key, index) => {
            const text = datas[key];
            const oldText = oldData[key];
            if (text !== oldData[key]) {
                texts[index].innerHTML = text;
            }

            let curWidth: number = 0;
            if (!text) {
                curWidth = 0;
            } else {
                textDom.innerHTML = text;
                curWidth = textDom.offsetWidth; // textDom.clientWidth;
            }
            // if (curWidth > obj['width' + curIndex]) {
            //     obj['width' + curIndex] = curWidth;
            // }
            if (!oldText) {
                curWidth = 0;
            } else {
                textDom.innerHTML = oldText;
                curWidth = textDom.offsetWidth; // textDom.clientWidth;
            }
            // if (curWidth > obj['oldWidth' + curIndex]) {
            //     obj['oldWidth' + curIndex] = curWidth;
            // }
        });

        // 修改属性
        const svgStr = dom.outerHTML;
        const drawObj = documentCore.getDrawingObjects();
        const svgConvertedURI = drawObj.convertSVGToImageString(svgStr);
        // drawObj.setDrawingProp(this.props.equation.name, {
        documentCore.setDrawingProp(this.props.equation.name, {
            // width: drawWidth,
            src: svgConvertedURI,
            equationElem: svgStr,
        });
        this.close(true);
    }
}
