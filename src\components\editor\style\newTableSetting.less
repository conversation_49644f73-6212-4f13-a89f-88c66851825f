
.new-full-line {
    display: flex;
    align-items: center;
    margin-bottom: 20px; /* 每行之间的间距 */
  }
  
  .table-label {
    margin-right: 15px; /* 标签和按钮之间的间距 */
    white-space: nowrap; /* 防止标签换行 */
  }
  
  .hz-table-button {
    width: 70px;
    height: 30px;
    background-color: #F0F0F0; /* 更浅的灰色背景 */
    color: #020202; /* 黑色色文字 */
    font-weight: bold; /* 文字加粗 */
    border: 1px solid #007BFF; /* 蓝色外边框 */
    border-radius: 5px;
    cursor: pointer;
    font-size: 11px;
    transition: background-color 0.3s, color 0.3s, border-color 0.3s;
    margin-left: 10px; /* 按钮之间的间距 */
  }
  
  .fixed-margin {
    margin-left: 8px; /* 固定的间距 */
  }

  .fixed-margin2 {
    margin-left: 0px; /* 固定的间距 */
  }
  
  .hz-table-button:first-child {
    margin-left: 30px; /* 第一个按钮左边距为0 */
  }
  
  /* 按钮悬停样式 */
  .hz-table-button:hover {
    background-color: #E0E0E0; /* 悬停时稍微深一些的灰色 */
    color: #0056b3; /* 悬停时更深的蓝色 */
    border-color: #0056b3; /* 悬停时更深的蓝色边框 */
  }
  
  /* 按钮点击选中样式 */
//   .hz-table-button:active {
//     background-color: #A9A9A9; /* 点击时更深的灰色 */
//     color: #FFF; /* 点击时的文字颜色 */
//   }
  