import { CommentOperatorType} from '@/common/commonDefines';
import {
    GlobalEvent as gEvent,
    GlobalEventName as gEventName
} from '@/common/GlobalEvent';
import { Comment } from '@/model/core/Comment/Comment';
import './commentPanel.less';
import React from 'react';
import CommentContent, { ICommentInfo } from './CommentContent';
import { CommentSelect } from '@/components/editor/ui/select/CommentSelect';
import { message } from '@/common/Message';

interface IProps {
    commentList: Comment[];
    documentCore: any;
    userName: string;
    height: number;
    bInsertNewComment?: boolean;
    activeComment: (name?: string) => void;
    refresh: (bRefreshUI: boolean) => void;
}

enum CommentStatusType {
    ALLCOMMENT,
    SOLVED,
    UNSOLVED,
}
interface SelectItem<T extends CommentStatusType | string> {
    value: T;
    key: string;
}
const commentStatusList: SelectItem<CommentStatusType>[] = [{
    value: CommentStatusType.ALLCOMMENT,
    key: '全部批注',
}, {
    value: CommentStatusType.SOLVED,
    key: '已处理',
}, {
    value: CommentStatusType.UNSOLVED,
    key: '未处理',
}];

interface IState {
    activeComment: string;
    bInsertNewComment: boolean;
    commentStatus: CommentStatusType;
    commentUser: string;
}
const defaultState: IState = {
    activeComment: '',
    bInsertNewComment: false,
    commentStatus: CommentStatusType.ALLCOMMENT,
    commentUser: '',
}
export default class ParaCommentLayer extends React.Component<IProps> {
    private _state: IState;

    private _listRef: any;

    constructor(props: IProps) {
        super(props);
        this._state = {...defaultState, };
        this._listRef = React.createRef();
    }

    public render(): any {
        const groupedRes = this.groupComments();
        return (
            <div className='comment-panel' tabIndex={-1} style={{height: this.props.height - 70 + 'px'}}>
                <div className="top-fixed">
                    {this.renderSummary(groupedRes)}
                    {this.renderFilter(groupedRes.users)}
                </div>
                <div className="comments-container" ref={this._listRef} >
                    {this.renderNewComment()}
                    {groupedRes.comments}
                </div>
            </div>
        );
    }
    public componentDidMount(): void {
        if (this.props.bInsertNewComment) {
            // 处理首次通过“插入批注”唤醒面板
            this.handleCommentChangeEvent(CommentOperatorType.New);
        }
        const docId = this.props.documentCore.getCurrentId();
        gEvent.addEvent(docId, gEventName.CommentChange, this.handleCommentChangeEvent);
        gEvent.addEvent(docId, gEventName.ContentChange, this.contentChange);
        gEvent.addEvent(docId, gEventName.OpenCompleted, this.fullyRefresh);
        gEvent.addEvent(docId, gEventName.MoveCursor, this.handleMoveCursor);
    }
    public componentDidUpdate(): void {
        this.scrollActiveComment();
    }

    public componentWillUnmount(): void {
        const docId = this.props.documentCore.getCurrentId();
        gEvent.deleteEvent(docId, gEventName.CommentChange, this.handleCommentChangeEvent);
        gEvent.deleteEvent(docId, gEventName.ContentChange, this.contentChange);
        gEvent.deleteEvent(docId, gEventName.OpenCompleted, this.fullyRefresh);
        gEvent.deleteEvent(docId, gEventName.MoveCursor, this.handleMoveCursor);

    }

    private groupComments() {
        const { commentList, userName } = this.props;
        const { commentUser, commentStatus } = this._state;
        const result = {
            sum: commentList.length,
            solved: 0,
            unSolved: 0,
            comments: [],
            users: [{
                value: '',
                key: '所有作者'
            }],
        }
        const userSet = new Set<string>([userName]);
        for (const comment of commentList) {
            const data = comment.getData();
            const isSolved = comment.getData()
                                    .isSolved();
            
            isSolved ? (result.solved++) : (result.unSolved++);
            const id = comment.getId();
            const info = {
                name: comment.getName(),
                id,
                data,
            };
            const commentUserName = data.getUserName();
            userSet.add(commentUserName);
            const isActived = comment.isActived();
            if (
                (isSolved && commentStatus === CommentStatusType.UNSOLVED) ||
                (!isSolved && commentStatus === CommentStatusType.SOLVED) ||
                (commentUser && commentUserName !== commentUser)
            ) {
                continue;
            }
            result.comments.push(
                <CommentContent
                    key={'com' + id}
                    index={id}
                    userName={userName}
                    info={info}
                    isActived={isActived}
                    isSolved={isSolved}
                    updateComment={this.updateComment}
                    activeComment={this.props.activeComment}
                />
            );
        }
        for (const name of userSet) {
            result.users.push({
                value: name, key: name,
            });
        }
        return result;

    }

    private renderSummary(groupedRes: {sum: number; solved: number; unSolved: number;}): React.ReactNode {

        return (
            <div className='summary'>
                <div className='btns'>
                    <button className='btn cancel' style={{boxSizing: 'content-box'}} onClick={() => this.handleCommentChangeEvent(CommentOperatorType.New)}>新增批注</button>
                </div>
                <div className="account">
                    <div className='title'>摘要：</div>
                    <div>未处理: <span style={{color: 'darkred'}}>{groupedRes.unSolved}</span> 处</div>
                    <div>已处理: <span style={{color: 'green'}}>{groupedRes.solved}</span> 处</div>
                </div>
            </div>
        );
    }

    private renderFilter(users: SelectItem<string>[]): React.ReactNode {
        const { commentStatus, commentUser } = this._state;
        return (
            <div className='filter'>
                <CommentSelect
                    datas={commentStatusList}
                    value={commentStatus}
                    name={'commentStatus'}
                    width='50%'
                    label='状态'
                    onChange={this.handleStatusChange}
                />
                <CommentSelect
                    datas={users}
                    value={commentUser}
                    name={'commentUser'}
                    width='50%'
                    label='作者'
                    onChange={this.handleStatusChange}
                />
            </div>
        );
    }

    private renderNewComment(): React.ReactNode {
        if (!this._state.bInsertNewComment) {
            return null;
        }
        const {userName} = this.props;
        return (
            <CommentContent
                key={-1}
                index={-1}
                userName={userName}
                isNew={true}
                isActived={true}
                onBlur={this.handleBlur}
                updateComment={this.updateComment}
                activeComment={this.props.activeComment}
            />
        );
    }

    private updateComment = (type: CommentOperatorType, info: Partial<ICommentInfo>) => {
        const { documentCore } = this.props;
        let refreshLevel = 0;
        switch (type) {
            case CommentOperatorType.New: {
                const commentName = documentCore.addCommentByCursor({
                    time: new Date(),
                    content: info.content,
                });
                if (commentName) {
                    this._state.activeComment = commentName;
                    this._state.bInsertNewComment = false;
                    refreshLevel = 2;
                } else {
                    message.error("当前位置不能插入批注");
                }
                break;
            }
            case CommentOperatorType.Reply: {
                if (documentCore.addCommentReply(info.name, info.content)) {
                    refreshLevel = 1;
                }
                break;
            }
            case CommentOperatorType.Delete: {
                const hasId = typeof info.id === 'number';
                if (documentCore.deleteCommentByName(info.name, info.id)) {
                    if (hasId) {
                        refreshLevel = 1;
                    } else {
                        refreshLevel = 2;
                    }
                }
                break;
            }
            case CommentOperatorType.Update: {
                if (documentCore.updateComment(info.name, {id: info.id, content: info.content})) {
                    // this._bActiveUpdate = false;
                }
                break;
            }
            case CommentOperatorType.Solve: {
                if (documentCore.updateComment(info.name, {isSolved: info.isSolved})) {
                    refreshLevel = 1;
                }
                break;
            }
        }
        this.handleRefreshByLevel(refreshLevel);
    }

    private handleBlur = (): void => {
        if (this._state.bInsertNewComment) {
            this._state.bInsertNewComment = false;
            this.handleRefreshByLevel(1);
        }
    }

    private handleMoveCursor = () => {
        this.props.activeComment();
    }

    private handleStatusChange = (value: any, name: string) => {
        if (name === 'commentUser') {
            if (value !== this._state.commentUser) {
                this._state.commentUser = value;
                this.setState({})
            }

        } else if (name === 'commentStatus') {
            value = +value;
            if (value !== this._state.commentStatus) {
                this._state.commentStatus = value;
                this.setState({});
            }
        }
    }

    private handleCommentChangeEvent = (type: CommentOperatorType, info?: any): void => {
        let refreshLevel = 0;
        switch (type) {
            case CommentOperatorType.Active: {
                const name = info || '';
                if (name !== this._state.activeComment) {
                    this._state.activeComment = name;
                    refreshLevel = 1;
                }
                break;
            }
            case CommentOperatorType.Update: {
                refreshLevel = 2;
                break;
            }
        }
        if (type === CommentOperatorType.New) {
            if (!this._state.bInsertNewComment) {
                this._state.bInsertNewComment = true;
                refreshLevel = 1;
            }
        } else {
            if (this._state.bInsertNewComment) {
                this._state.bInsertNewComment = false;
                refreshLevel = 1;
            }
        }
        this.handleRefreshByLevel(refreshLevel);
    }

    private handleRefreshByLevel(level: number) {
        if (level === 1) {
            this.setState({});
        } else if (level === 2) {
            this.props.refresh(true);
        } else if (level === 3) {
            this.props.refresh(false);
        }
    }

    public contentChange = () => {
        this.handleRefreshByLevel(3);
    }

    private fullyRefresh = () => {
        this._state = {...defaultState};
        this.handleRefreshByLevel(2);
    }

    private scrollActiveComment() {
        // 滚动激活条目
        const listDom = this._listRef.current as HTMLDivElement;
        const activeDom = listDom?.querySelector('div.active') as HTMLDivElement;
        if (!listDom || !activeDom) return;
        let offY = activeDom.offsetTop - listDom.scrollTop;
        if (offY < 0) {
            listDom.scrollTop = (listDom.scrollTop + offY - 5);
        } else {
            offY = listDom.offsetHeight + listDom.scrollTop - (activeDom.offsetTop + activeDom.offsetHeight);
            if (offY < 0) {
                listDom.scrollTop = (listDom.scrollTop - offY + 5);
            }
        }
    }

}
