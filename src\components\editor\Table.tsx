import * as React from 'react';
import { IDocumentTable } from "src/model/TableProperty";

export default class Table extends React.Component<IDocumentTable> {
    constructor(props) {
        super(props);
    }

    renderBorderLine() {
        const { tableBorderLines } = this.props;
        return tableBorderLines.map( (item, index) => {
           return <line key={index++} x1={item.x1} y1={item.y1} x2={item.x2} y2={item.y2}
           stroke={item.stroke} strokeWidth={item.strokeWidth} strokeDasharray={item.strokeDashArray}/> ;
        });
    }

    renderBackground() {
        const { tableBackground } = this.props;
        return tableBackground.map( (item, index) => {
            return <rect key={index} line-key={index++} width={item.width} height={item.height} x={item.x} y={item.y} fill={'#ffffff'} fillOpacity={item.fillOpacity}/> ;
         });
    }

    render() {
        return <g>
                {this.renderBackground()}
                <g className="tableBorderLine">{this.renderBorderLine()}</g>
               </g>
    }
}