import { getEditorDomContainer } from './commonDefines';
import {IFRAME_MANAGER} from './IframeManager';

export interface IMessageOption {
    title?: string;
    width?: string;
    height?: string;
    left?: string;
    right?: string;
    className?: string;
    time?: number;
    btns?: string[];
    autoClose?: boolean;
    onlyBtnClose?: boolean;
    cancelDefault?: boolean;
    specialBlessing?: boolean; // 用来应付emr ui
}

enum MessageType {
    Info,
    Warning,
    Error,
    Confirm,
}

// let prevCancelDefault: boolean = false;
class Message {
    protected _resolve: (index: number) => void;
    protected _reject: (index: number) => void;
    protected _type: number;
    private _msgDom: HTMLDivElement;
    private _msgTitleDom: HTMLSpanElement;
    private _msgBodyDom: HTMLSpanElement;
    private _msgBtnDom: HTMLDivElement;
    private _defaultTitle: string;
    private _defaultBtns: string[];
    private _bChangeTitle: boolean;
    private _bChangeBtn: boolean;
    private _defatultTime: number;
    private _clearTime: any;
    private _onlyBtnClose: boolean;
    private _className: string;

    public info(content: string, options?: IMessageOption): Promise<number> {
        this._type = MessageType.Info;
        this._init();
        this._setMessage(content, options);
        return new Promise((resolve, reject) => {
            this._resolve = resolve;
            this._reject = reject;
        });
    }

    public warning(content: string, options?: IMessageOption): Promise<number> {
        this._type = MessageType.Warning;
        this._init();
        this._setMessage(content, options);
        return new Promise((resolve, reject) => {
            this._resolve = resolve;
            this._reject = reject;
        });
    }

    public error(content: string, options?: IMessageOption): Promise<number> {
        this._type = MessageType.Error;
        this._init();
        this._setMessage(content, options);
        return new Promise((resolve, reject) => {
            this._resolve = resolve;
            this._reject = reject;
        });
    }

    public close(): void {
        if (!this._msgDom) {
            return;
        }
        this._close();
    }

    protected _init(className: string = '', cancelDefault: boolean = false): void {
        if (this._msgDom) { // } && prevCancelDefault === cancelDefault) {
            return;
        }
        // prevCancelDefault = cancelDefault;

        this._defaultBtns = ['确定'];
        this._defaultTitle = '提示信息';
        this._bChangeTitle = false;
        this._bChangeBtn = false;
        this._defatultTime = 5000;

        if (this._type === MessageType.Error) {
            this._defaultBtns = [];
            this._defaultTitle = '警示';
            this._defatultTime = 2000;
            this._bChangeTitle = true;
        } else if (this._type === MessageType.Confirm) {
            this._defaultTitle = '二次确认';
            this._bChangeTitle = true;
        }

        const additionalClass = cancelDefault === true ? 'cancelDefault' : '';
        const msgTemplate = `
            <div class="editor-message-box">
                <div class="editor-message-title">
                    <span>提示信息</span>
                    <div class="editor-message-close" data-index="0">&#x2715;</div>
                </div>
                <div class="editor-message-body">
                    <span></span>
                </div>
                <div class="editor-message-btns ${additionalClass}">
                    <span data-index="0">确定</span>
                </div>
            </div>
        `;

        const dom = document.createElement('div');
        dom.className = 'editor-message' + className;
        dom.innerHTML = msgTemplate;
        const container = getEditorDomContainer();
        // if (isHasMessageStyle === false) {
        //     isHasMessageStyle  = true;
        //     const style = document.createElement('style');
        //     style.type = 'text/css';
        //     style.innerHTML = getMessageStyle();
        //     container.appendChild(style);
        // }
        container.appendChild(dom);
        this._msgDom = dom;
        this._msgTitleDom = dom.querySelector('.editor-message-title>span');
        this._msgBtnDom = dom.querySelector('.editor-message-btns');
        this._msgBodyDom = dom.querySelector('.editor-message-body>span');

        this._addEvent();
    }

    protected _setMessage(content: string, options: IMessageOption): void {
        if (!content) {
            return;
        }
        const obj: IMessageOption = (options || {}) as IMessageOption;
        let title: string;
        let btns: string[];
        if (obj.title !== undefined) {
            this._bChangeTitle = true;
            title = obj.title;
        } else if (this._bChangeTitle === true) {
            this._bChangeTitle = false;
            title = this._defaultTitle;
        }

        if (obj.btns && obj.btns.length) {
            this._bChangeBtn = true;
            btns = obj.btns;
        } else if (this._bChangeBtn) {
            this._bChangeBtn = false;
            btns = this._defaultBtns;
        }

        if (title !== undefined) {
            this._msgTitleDom.innerHTML = title;
        }

        if (obj.className) {
            this._className = obj.className;
            this._msgDom.className += obj.className;
        }

        if (btns) {
            const specialBlessing = obj.specialBlessing;
            this._msgBtnDom.innerHTML = btns.map((btn, index) => {
                if (specialBlessing == null) {
                    return `<span data-index="${index}">${btn}</span>`;
                } else {
                    return `<span class='special-blessing' data-index="${index}">${btn}</span>`;
                }
            })
            .join('');
        } else {
            this._msgBtnDom.innerHTML = '';
        }

        if (this._type === MessageType.Error) {
            this._msgBodyDom.innerHTML =
            // <div class="editor-message-warning">&#x26A0;</div>
            `
                <div class="editor-message-warning"><i></i></div>
                <p class="editor-message-content">${content}</p>
            `;
        } else {
            this._msgBodyDom.innerHTML = content;
        }

        this._show();
        if (obj.autoClose === false) {
            return;
        }
        const iframe = IFRAME_MANAGER.getIframe(IFRAME_MANAGER.getDocId());
        if (iframe) {
            const position = iframe.getBoundingClientRect();
            const bodyWidth = position.width;
            const bodyHeight = position.height;
            const dom = this._msgDom.firstElementChild as HTMLDivElement;
            let left = position.left;
            let top = position.top;
            left = left + (bodyWidth - dom.clientWidth) / 2;
            top = top + (bodyHeight - dom.clientHeight) / 2;

            dom.style.left = left + 'px';
            dom.style.top = top + 'px';
            if (dom.className.indexOf('show-iframe') === -1) {
                dom.className += ' show-iframe';
            }
        }
        const time = obj.time !== undefined ? obj.time : this._defatultTime;
        clearTimeout(this._clearTime);
        this._clearTime = setTimeout(() => {
            this._hide();
            this._resolve(-1);
        }, time);
    }

    private _handleClick = (event: Event): void => {
        const target = event.target as HTMLElement;
        const index = target.getAttribute('data-index');
        if (index) {
            event.stopPropagation();
            return this._btnsClick(+index);
        }
        if (this._onlyBtnClose === true) {
            return;
        }
        if (this._type === MessageType.Confirm || this._type === MessageType.Error) {
            return;
        }
        event.stopPropagation();
        this._close();
        if (!index) {
            this._resolve(-1);
        }
    }

    private _btnsClick(index: number): void {
        // console.log(index)
        if (this._type === MessageType.Confirm) {
            if (index === 0) {
                this._reject(index);
            } else {
                this._resolve(index);
            }
        } else {
            this._resolve(index);
        }

        this._close();
    }

    private _addEvent(): void {
        this._msgDom.addEventListener('click', this._handleClick);
        // this._msgDom.addEventListener('mouseup', (e) => {
        //     e.stopPropagation();
        //     e.preventDefault();
        // });
        // this._msgDom.addEventListener('mousemove', (e) => {
        //     e.stopPropagation();
        //     e.preventDefault();
        // });
        // this._msgDom.addEventListener('mousedown', (e) => {
        //     e.stopPropagation();
        //     e.preventDefault();
        // });
    }

    private _close(): void {
        this._hide();
        clearTimeout(this._clearTime);
    }

    private _hide(): void {
        this._msgDom.className = this._msgDom.className.replace(/ active/, '');
        if (this._className) {
            this._msgDom.className = this._msgDom.className.replace(this._className, '');
            this._className = undefined;
        }
    }

    private _show(): void {
        const className = this._msgDom.className;
        if (className.indexOf(' active') > -1) {
            return;
        }
        this._msgDom.className = className + ' active';
    }
}

class MessageConfirm extends Message {
    public confirm(content: string, options?: IMessageOption): Promise<number> {
        this._type = MessageType.Confirm;
        // const cancelDefault = (options != null) ? options.cancelDefault : false;
        this._init(' editor-confirm'); // , cancelDefault);
        options = options || {};
        options.time = 10e10;
        options.btns = ['取消', '确认'];
        this._setMessage(content, options);
        return new Promise((resolve, reject) => {
            this._resolve = resolve;
            this._reject = reject;
        });
    }
}

export let message = new Message();

export let layer = new MessageConfirm();
