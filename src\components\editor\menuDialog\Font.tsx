import * as React from 'react';
import Dialog from '../dialog/dialog';
import SelectList from '../select/list';
import {fonts} from '../text/font';

interface IDialogProps {
    documentCore: any;
    visible: boolean;
    close: (name: string | number, bReflash?: boolean) => void;
    children?: React.ReactNode;
}

interface IState {
    bReflash: boolean;
}

export default class FontSetDialog extends React.Component<IDialogProps, IState> {
    // 存储结果属性
    private font: {fontSize: number, fontFamily: string, fontStyle: number, fontWeight: number, subscript: boolean,
        superscript: boolean, textDecorationLine: boolean};
    // 这里用来展示
    private fontFormat: {font: object, textDecoration: object};

    constructor(props: any) {
        super(props);
        this.state = {
            bReflash: false,
        };
        this.font =  {fontSize: undefined, fontFamily: undefined, fontStyle: undefined, subscript: undefined,
            fontWeight: undefined, superscript: undefined, textDecorationLine: undefined};
        this.fontFormat = {font: {}, textDecoration: {}};
    }

    public render(): any {
        return (
        <Dialog
            id={'font'}
            preventDefault={true}
            close={this.close}
            open={this.fontOpen}
            confirm={this.confirm}
            visible={this.props.visible}
            top='middle'
            width={400}
            title={'设置'}
        >
          <div className='font-set'>
            <div className={'font-top'}>
                <ul>
                    <li>
                        <p className='common-title'>字体</p>
                        <SelectList
                            height={150}
                            data={fonts[0].options}
                            props={{name: 'name', value: 'name'}}
                            value={this.font.fontFamily}
                            change={this.fontFamilyChang}
                        />
                    </li>
                    <li>
                    <p className='common-title'>字形</p>
                    <SelectList
                        height={150}
                        data={fonts[2].options}
                        props={{name: 'name', value: 'value'}}
                        value={this.font.fontStyle}
                        change={this.fontStyleChange}
                    />
                    </li>
                    <li>
                    <p className='common-title'>大小</p>
                    <SelectList
                        data={fonts[1].options}
                        height={150}
                        props={{name: 'name', value: 'value'}}
                        value={this.font.fontSize}
                        change={this.fontSizeChange}
                    />
                    </li>
                </ul>
            </div>
            <div className='font-middle'>
                <p className='common-title'>效果</p>
                <div>
                    <span>
                        <label htmlFor='textDecorationLine'>下划线</label>
                        <input
                            type='checkbox'
                            id='textDecorationLine'
                            checked={this.font.textDecorationLine === true}
                            onChange={this.fontCheckbox.bind(this, 'textDecorationLine')}
                        />
                    </span>
                    <span>
                        <label htmlFor='superscript'>上标</label>
                        <input
                            type='checkbox'
                            id='superscript'
                            checked={this.font.superscript === true}
                            onChange={this.fontCheckbox.bind(this, 'superscript')}
                        />
                    </span>
                    <span>
                        <label htmlFor='subscript'>下标</label>
                        <input
                            type='checkbox'
                            id='subscript'
                            checked={this.font.subscript === true}
                            onChange={this.fontCheckbox.bind(this, 'subscript')}
                        />
                    </span>
                </div>
                <div className='font-footer'>
                    <p className='common-title'>预览</p>
                    <div>
                        <p style={this.fontFormat.font}>
                            <span style={this.fontFormat.textDecoration}>文字效果示意</span>
                        </p>
                    </div>
                </div>
            </div>
          </div>
        </Dialog>
        );
    }

    public componentMount(): void {
        // if (this._visible === true) {
        //     this.props.open();
        // }
    }

    private close = (id?: any, bReflash?: boolean): void => {
        this.props.close(id, bReflash);
        this.clearFont();
    }

    private fontOpen = (): void => {
        const documentCore = this.props.documentCore;
        const attr = documentCore.getSelectedTextPro();
        const font = this.font;
        const style: any = this.font;
        style.textDecorationLine = attr.textDecorationLine === 1;
        style.subscript = attr.vertAlign === 1;
        style.superscript = attr.vertAlign === 2;
        font.fontFamily = attr.fontFamily;
        font.fontStyle = undefined;
        if (attr.fontStyle && attr.fontWeight) {
            font.fontStyle = 3;
        } else if (attr.fontStyle) {
            font.fontStyle = 1;
        } else if (attr.fontWeight) {
            font.fontStyle = 2;
        } else if (attr.fontStyle === 0 && attr.fontWeight === 0) {
            font.fontStyle = 0;
        }
        if (style.superscript && style.textDecorationLine || style.subscript && style.textDecorationLine) {
            style.superscript = false;
            style.textDecorationLine = false;
            style.subscript = false;
        }
        font.fontSize = attr.fontSize;
        this.setState({bReflash: !this.state.bReflash});
    }

    private confirm = (id?: number | string): void => {
        const font = this.getFont();
        const documentCore = this.props.documentCore;
        documentCore.setTextProperty(font);
        this.close(id, true);
    }

    private clearFont(): void {
        this.fontFormat = {
            font: {},
            textDecoration: {},
        };

        this.font =  {fontSize: null, fontFamily: null, fontStyle: null, subscript: null,
            fontWeight: null, superscript: null, textDecorationLine: null};
    }

    private getFont(): any {
        const font = this.font;
        let fontStyle: number = font.fontStyle;
        let fontWeight: number;
        switch (fontStyle) {
            case 0:
                fontWeight = 0;
                fontStyle = 0;
                break;
            case 1:
                fontStyle = 1;
                break;
            case 2:
                fontWeight = 1;
                fontStyle = 0;
                break;
            case 3:
                fontWeight = 1;
                fontStyle = 1;
                break;
            default:
                fontWeight = undefined;
                fontStyle = undefined;
        }

        font.fontStyle = fontStyle;
        font.fontWeight = fontWeight;

        return font;
    }

    private setPreviewFont(key?: string): void {
        const font = this.font;
        const style = {};
        const textDecorationStyle = {};
        this.fontFormat = {
            font: style,
            textDecoration: textDecorationStyle,
        };
        let flag: boolean = false;
        if (key !== undefined) {
            switch (key) {
                case 'superscript':
                    font.subscript = false;
                    break;
                case 'subscript':
                    font.superscript = false;
                    break;
                default:
                    flag = true;
            }
        }

        if (flag === true && font.superscript) {
            font.subscript = false;
            flag = false;
        }

        if (font.fontSize !== undefined) {
            style['fontSize'] = font.fontSize;
            flag = true;
        }
        if (font.fontFamily !== undefined) {
            style['fontFamily'] = font.fontFamily;
            flag = true;
        }

        if (font.subscript === true) {
            style['transform'] = 'translate(0, 5px) scale(.5)';
            flag = true;
        }
        if (font.superscript === true) {
            style['transform'] = 'translate(0, -5px) scale(.5)';
            flag = true;
        }
        if (font.textDecorationLine === true) {
            textDecorationStyle['borderBottom'] = '1px solid #000';
            flag = true;
        }

        if (font.fontStyle !== undefined) {
            switch (font.fontStyle) {
                case 1:
                    style['fontStyle'] = 'italic';
                    flag = true;
                    break;
                case 2:
                    style['fontWeight'] = 'bold';
                    flag = true;
                    break;
                case 3:
                    style['fontStyle'] = 'italic';
                    style['fontWeight'] = 'bold';
                    flag = true;
                    break;
                default:
            }
        }
        if (flag === true) {
            this.setState({bReflash: !this.state.bReflash});
        }
    }

    private fontCheckbox(key: string, e?: any): void {
        const value = e.target.checked;
        this.font[key] = value;
        this.setPreviewFont(key);
    }

    private fontFamilyChang = (item: any): void => {
        this.font.fontFamily = item.name;
        this.setPreviewFont();
        // this.fontFormat.fontFamily = item.value;
    }

    private fontStyleChange = (item: any): void => {
        this.font.fontStyle = item.value;
        this.setPreviewFont();
        // this.fontFormat.fontStyle = item.value;
    }

    private fontSizeChange = (item: any): void => {
        this.font.fontSize = item.value;
        this.setPreviewFont();
        // this.fontFormat.fontStyle = this.font.fontSize;
    }
}
