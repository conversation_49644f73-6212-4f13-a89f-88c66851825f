import { XmlComponent } from '../../xml-components';

export class Properties extends XmlComponent {

  constructor() {
      super('Properties');
  }

  public addCustomElements(properties: Map<string, any>): Properties {
    properties.forEach( (value, key, map) => {
      this.root.push(new CustomElement(key, value));
    });
    return this;
  }
}

export class CustomElement extends XmlComponent {
  constructor(key: string, value: any) {
      super(key);
      if (value != null) {
        this.root.push(value);
      }
  }
}
