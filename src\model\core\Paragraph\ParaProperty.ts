import ParaIndentation from './ParaIndentation';
import ParaSpacing from './ParaSpacing';
import { AlignType } from '../Style';
import { IParaNumPr } from '@/common/commonDefines';

/**
 * 段落属性
 */
export default class ParaProperty {
    public alignment: AlignType; // 对齐方式
    public bPageBreakBefore: boolean; // 段前分页符
    public bPageBreakAfter: boolean; // 段后分页符
    // todo: 缩进 段落间距 tabs
    public paraInd: ParaIndentation;  // 段落缩进
    public paraSpacing: ParaSpacing;  // 段落间距
    public bWordWrap: boolean;
    public numPr: IParaNumPr;
    // tabs: undefined;
    public nisSignAuthor: string;

    public propertyChange: ParaProperty = null;  // 属性发生改变

    constructor() {
        this.alignment = AlignType.Justify;
        this.bPageBreakBefore = false;
        this.bPageBreakAfter = false;
        this.paraInd = new ParaIndentation();
        this.paraSpacing = new ParaSpacing();
        this.bWordWrap = false;
        this.nisSignAuthor = undefined;
    }

    /**
     * 段落属性拷贝函数
     */
    public copy(): ParaProperty {
        const newPr = new ParaProperty();
        newPr.alignment = this.alignment;
        // 分页符不能复制
        // newPr.bPageBreakBefore = this.bPageBreakBefore;
        newPr.bPageBreakAfter = this.bPageBreakAfter;

        if ( this.paraInd ) {
            newPr.paraInd = this.paraInd.copy();
        }

        if ( this.paraSpacing ) {
            newPr.paraSpacing = this.paraSpacing.copy();
        }

        newPr.bWordWrap = this.bWordWrap;

        return newPr;
    }

    /**
     * 是否有属性发生改变
     */
    public hasPropertyChange(): boolean {
        if ( !this.propertyChange ) {
            return false;
        }

        return true;
    }

    /**
     * 属性发生改变
     */
    public addPropertyChange(): void {
        this.propertyChange = this.copy();
    }

    /**
     * 设置属性
     * @param propertyChange
     */
    public setPropertyChange( propertyChange: ParaProperty ): void {
        this.propertyChange = propertyChange;
    }

    /**
     * 删除属性变化
     */
    public removePropertyChange(): void {
        delete this.propertyChange;
    }
}
