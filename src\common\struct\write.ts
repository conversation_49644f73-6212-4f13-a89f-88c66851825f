import { XmlComponent } from '@/format/writer/file/xml-components';
import { IXmlResult } from '@/format/writer/file/xml-components/base';
import { ALIGN_TYPE, ICustomProps, INewControlProperty } from '../commonDefines';
import { IStructPropInfo, StructExportPrintType, StructXmlName, StructXmlProperty, STUCT_DEFAULT, writeStructXmlName } from './common';

export class WriteStruct {
    private _props: INewControlProperty;
    private _root: XmlComponent;
    constructor(props: any, root: XmlComponent) {
        this._props = props;
        this._root = root;
        this.init();
    }

    private init(): void {
        const props =  this._props;
        const names: string[] = [StructXmlProperty.NewControlName, StructXmlProperty.NewControlType];

        // tslint:disable-next-line:forin
        for (const propsName in props) {
            if (names.includes(propsName)) {
                continue;
            }

            const prop = props[propsName];
            const item: IStructPropInfo = writeStructXmlName[propsName];
            if (prop === undefined || item === undefined) {
                continue;
            }

            const key = item.propName || propsName;
            if (prop === STUCT_DEFAULT[key]) {
                continue;
            }

            switch (item.type) {
                case StructExportPrintType.CustomDate:
                case StructExportPrintType.County:
                case StructExportPrintType.Event: {
                    this.writeEvent(key, prop);
                    break;
                }
                case StructExportPrintType.Number: {
                    this.writeNumber(key, prop);
                    break;
                }
                case StructExportPrintType.Align: {
                    this.writeAlign(key, prop);
                    break;
                }
                case StructExportPrintType.Boolean: {
                    this.writeBoolean(key, prop);
                    break;
                }
                case StructExportPrintType.List: {
                    this.writeList(key, prop);
                    break;
                }
                case StructExportPrintType.Custom: {
                    this.writeCustom(key, prop);
                    break;
                }

                default: {
                    this.writeString(key, prop);
                    break;
                }
            }
        }
    }

    private writeString(key: string, value: string): void {
        if (typeof value !== 'string' || !value && key !== StructXmlName.Placeholder) {
            return;
        }

        this.addNode(key, value);
    }

    private writeBoolean(key: string, value: boolean): void {
        if (typeof value !== 'boolean') {
            return;
        }
        this.addNode(key, value ? '1' : '0');
    }

    private writeNumber(key: string, value: number): void {
        if (typeof value !== 'number') {
            return;
        }

        this.addNode(key, value + '');
    }

    private writeAlign(key: string, value: number): void {
        if (typeof value !== 'number') {
            return;
        }

        this.addNode(key, ALIGN_TYPE[value]);
    }

    private writeEvent(key: string, value: object): void {
        if (typeof value !== 'object' || !value || !Object.keys(value).length) {
            return;
        }

        this.addNode(key, JSON.stringify(value));
    }

    private writeList(key: string, list: any[]): void {
        if (!list || !list.length) {
            return;
        }

        const node = this.addNode(key, null);
        list.forEach((item) => {
            const listItem = this.addNode('listItem', null, node);
            this.addNode('name', item.code || '', listItem);
            this.addNode('value', item.value || '', listItem);
            this.addNode('select', item.bSelect === true ? '1' : '0', listItem);
        });
    }

    private writeCustom(key: string, customs: ICustomProps[]): void {
        if (!customs || !customs.length) {
            return;
        }

        const node = this.addNode(key, null);
        customs.forEach((custom) => {
            const child = this.addNode(custom.name, custom.value, node);
            this.addAttr({type: custom.type}, child);
        });
    }

    private addNode(key: string, text: string, root?: XmlComponent): XmlComponent {
        root = root || this._root;
        const xml = new XmlComponent(key);
        if (text) {
            xml.addChildElement(text);
        }

        root.addChildElement(xml);
        return xml;
    }

    private addAttr(obj: object, root: XmlComponent): void {
        const attr = new XmlAttribute(obj);
        root.addChildElement(attr);
    }
}

export function addNode(key: string, text: string, root: any): XmlComponent {
    const xml = new XmlComponent(key);
    if (text) {
        xml.addChildElement(text);
    }

    root.push(xml);
    return xml;
}

export function addAttr(obj: {[key: string]: any}, root: XmlComponent): void {
    const attr: XmlComponent = new XmlAttribute(obj);
    root.addChildElement(attr);
}

export class XmlAttribute extends XmlComponent {
    public root: any;
    constructor(properties: any) {
        super('_attr');
        this.root = properties;
    }

    public prepForXml(): IXmlResult {
        return super.prepForXml();
    }

    public set(properties: any): void {
        this.root = properties;
    }

    // helper function
    public getRoot(): any {
        return this.root;
    }
}
