import * as React from 'react';
import Dialog from '../../ui/Dialog';
import '../../style/signaturebox.less';
import Button from '../../ui/Button';
import Input from '../../ui/Input';
import Checkbox from '../../ui/CheckboxItem';
import CustomProperty from './CustomProperty';
import Select from '../../ui/select/Select';
// tslint:disable-next-line: max-line-length
import { INewControlProperty, NewControlType, CustomPropertyElementType, SignatureCountType, STD_START_DEFAULT, NewControlDefaultSetting, NewControlContentSecretType, SignatureType, isValidName } from '../../../../common/commonDefines';
import { IFRAME_MANAGER } from '../../../../common/IframeManager';
import { message } from 'src/common/Message';
import { ExternalDataBind } from './ExternalDataBind';

interface IDialogProps {
  documentCore: any;
  visible: boolean;
  property?: INewControlProperty;
  id: string;
  close: (name: string | number, bRefresh?: boolean) => void;
}

interface IState {
  bRefresh: boolean;
  disableSignatureRatio: boolean;
}

export default class NewSignatureBox extends React.Component<IDialogProps, IState> {
  private signatureProperty: INewControlProperty;
  private visible: any;
  private bCustomProperty: boolean;
  private signatureCountTypes1: any[];
  private signatureCountTypes2: any[];
  private signTypes: any[];
  private bDisableType: boolean;
  private alwaysShowCountTypes: any[];
  // private bDisableAlwaysShow: boolean;
  private bDisableSignatureCount: boolean;
  private resetSourceBind: boolean;
  private dataBind: any;

  constructor(props: any) {
    super(props);
    this.state = {
      bRefresh: false,
      disableSignatureRatio: true,
    };
    this.visible = this.props.visible;
    this.signatureProperty = {newControlName: undefined};
    this.signTypes = [
      {key: '常规', value: SignatureType.Common},
      {key: '集合', value: SignatureType.Set},
    ];
    this.alwaysShowCountTypes = [
      {key: '1', value: 1},
      {key: '2', value: 2},
      {key: '3', value: 3},
      {key: '4', value: 4},
      {key: '5', value: 5},
      {key: '6', value: 6},
      {key: '7', value: 7},
      {key: '8', value: 8},
      {key: '9', value: 9},
      {key: '10', value: 10},
    ];
    this.signatureCountTypes1 = [
      {key: '1', value: SignatureCountType.Single},
      {key: '2', value: SignatureCountType.Double},
      {key: '3', value: SignatureCountType.Triple},
    ];
    this.signatureCountTypes2 = [
      {key: '1', value: SignatureCountType.Single},
      {key: '2', value: SignatureCountType.Double},
      {key: '3', value: SignatureCountType.Triple},
      {key: '4', value: SignatureCountType.Fourth},
      {key: '5', value: SignatureCountType.Five},
      {key: '6', value: SignatureCountType.Sixth},
      {key: '7', value: SignatureCountType.Seven},
      {key: '8', value: SignatureCountType.Eight},
      {key: '9', value: SignatureCountType.Night},
      {key: '10', value: SignatureCountType.Ten},
    ];
  }

  public UNSAFE_componentWillReceiveProps(nextProps: any): void {
    this.visible = nextProps.visible;
  }

  public render(): any {
    return (
      <Dialog
        visible={this.visible}
        width={350}
        open={this.open}
        title='签名控件'
        footer={this.renderFooter()}
        id={this.props.id}
      >
        <div className='signature-box'>
            <div className='editor-line'>
                <span className='title'>常规</span>
            </div>
            <div className='editor-line'>
                <div className='w-70'>名称</div>
                <div className='right-auto'>
                    <Input
                        name='newControlName'
                        value={this.signatureProperty.newControlName}
                        onChange={this.onChange}
                    />
                </div>
            </div>
            <div className='editor-line'>
                <div className='w-70'>内部名称</div>
                <div className='right-auto'>
                    <Input
                        name='newControlSerialNumber'
                        value={this.signatureProperty.newControlSerialNumber}
                        onChange={this.onChange}
                        readonly={true}
                        placeholder={''}
                        disabled={true}
                    />
                </div>
            </div>
            <div className='editor-line'>
                <div className='w-70'>标识符</div>
                <div className='right-auto'>
                    <Input
                        name='identifier'
                        value={this.signatureProperty.identifier}
                        onChange={this.onChange}
                    />
                </div>
            </div>
            <div className='editor-line'>
                <div className='w-70'>提示符</div>
                <div className='right-auto'>
                    <Input
                        name='newControlInfo'
                        value={this.signatureProperty.newControlInfo}
                        onChange={this.onChange}
                    />
                </div>
            </div>
            <div className='editor-line'>
                <div className='w-70'>类型</div>
                <div className='right-auto'>
                  <Select
                      data={this.signTypes}
                      name='signType'
                      value={this.signatureProperty.signType}
                      onChange={this.onChange}
                      disabled={this.bDisableType}
                  />
                </div>
            </div>
            <div className='editor-line editor-multi-line'>
                <span className='title w-70'>属性</span>
                <div className='right-auto newcontrol-prop'>
                    <div className='editor-line'>
                        <div className='w-050'>
                            <Checkbox
                                name='isNewControlHidden'
                                disabled={false}
                                value={this.signatureProperty.isNewControlHidden}
                                onChange={this.onChange}
                            >
                                隐藏
                            </Checkbox>
                        </div>
                        <div className='w-050'>
                            <Checkbox
                                name='isNewControlCanntDelete'
                                value={this.signatureProperty.isNewControlCanntDelete}
                                onChange={this.onChange}
                            >
                                禁止删除
                            </Checkbox>
                        </div>
                    </div>

                    <div className='editor-line'>
                        <div className='w-050'>
                            <Checkbox
                                name='isNewControlCanntEdit'
                                value={this.signatureProperty.isNewControlCanntEdit}
                                onChange={this.onChange}
                                // disabled={true}
                            >
                                禁止编辑
                            </Checkbox>
                        </div>
                        <div className='w-050'>
                            <Checkbox
                                name='isNewControlShowBorder'
                                value={this.signatureProperty.isNewControlShowBorder}
                                onChange={this.onChange}
                                // disabled={true}
                            >
                                显示边框
                            </Checkbox>
                        </div>
                    </div>

                    <div className='editor-line'>
                        <div className='w-050'>
                            <Checkbox
                                name='isNewControlHiddenBackground'
                                value={this.signatureProperty.isNewControlHiddenBackground}
                                onChange={this.onChange}
                            >
                                隐藏背景色
                            </Checkbox>
                        </div>
                        <div className='w-050'>
                            <Checkbox
                                name='isNewControlCanntCopy'
                                value={this.signatureProperty.isNewControlCanntCopy}
                                onChange={this.onChange}
                            >
                                无法拷贝
                            </Checkbox>
                        </div>
                    </div>

                    <div className='editor-line'>
                        <div className='w-050'>
                            <Checkbox
                                name='showSignBorder'
                                value={this.signatureProperty.showSignBorder}
                                onChange={this.onChange}
                                disabled={(!this.isSignSet())}
                            >
                                子元素边框显示
                            </Checkbox>
                        </div>
                    </div>

                    <div className='editor-line'>
                        <CustomProperty
                            name='customProperty'
                            properties={this.signatureProperty.customProperty}
                            documentCore={this.props.documentCore}
                            onChange={this.onChange}
                            close={this.onClose}
                            id='bCustomProperty'
                            visible={this.bCustomProperty}
                            type={CustomPropertyElementType.NewControl}
                        />
                    </div>
                </div>
            </div>
            {this.renderExternal()}
        </div>
      </Dialog>
    );
  }

  private renderExternal(): any {
    if (this.signatureProperty.newControlType !== NewControlType.SignatureBox) {
      return null;
    }
    const signatureCount = this.signatureProperty.signatureCount;
    return (
      <React.Fragment>
        <div className='editor-line'>
            <span className='title'>扩展属性</span>
        </div>
        <div className='editor-line'>
            <div className='w-120'>签名个数</div>
            <div className='right-auto-120'>
                <div className={!this.isSignSet() ? '' : 'hidden'}>
                  <Select
                      data={this.signatureCountTypes1}
                      name='signatureCount'
                      value={this.signatureProperty.signatureCount}
                      onChange={this.onChange}
                  />
                </div>
                <div className={this.isSignSet() ? '' : 'hidden'}>
                  <Select
                      data={this.signatureCountTypes2}
                      name='signatureCount2'
                      value={this.signatureProperty.signatureCount}
                      onChange={this.onChange}
                      disabled={this.bDisableSignatureCount}
                  />
                </div>
            </div>
        </div>
        <div className='editor-line'>
            <div className='w-120'>常驻显示</div>
            <div className='right-auto-120'>
                <Select
                    data={this.alwaysShowCountTypes}
                    name='alwaysShow'
                    value={this.signatureProperty.alwaysShow}
                    onChange={this.onChange}
                    disabled={!this.isSignSet()}
                />
            </div>
        </div>
        <div className='editor-line'>
            <div className='w-120'>前字符</div>
            <div className='right-auto-120'>
                <Input
                    name='preText'
                    value={this.signatureProperty.preText}
                    onChange={this.onChange}
                />
            </div>
        </div>
        <div className='editor-line'>
            <div className='w-120'>间隔字符</div>
            <div className='right-auto-120'>
                <div className={signatureCount === SignatureCountType.Triple ? 'w-050' : ''}>
                    <Input
                        name='signatureSeparator'
                        value={this.getSignSeparatorVal(1)}
                        onChange={this.onChange}
                        disabled={signatureCount === SignatureCountType.Single || this.isSignSet()}
                    />
                </div>
                <div className={signatureCount === SignatureCountType.Triple ? 'w-050' : 'hidden'}>
                    <Input
                        name='signatureSeparator2'
                        value={this.getSignSeparatorVal(2)}
                        onChange={this.onChange}
                        disabled={this.isSignSet()}
                    />
                </div>
            </div>
        </div>
        <div className='editor-line'>
            <div className='w-120'>尾字符</div>
            <div className='right-auto-120'>
                <Input
                    name='postText'
                    value={this.signatureProperty.postText}
                    onChange={this.onChange}
                    disabled={this.isSignSet()}
                />
            </div>
        </div>
        <div className='editor-line'>
            <div className='w-120'>签名占位符</div>
            <div className='right-auto-120'>
                <Input
                    name='signaturePlaceholder'
                    value={this.signatureProperty.signaturePlaceholder}
                    onChange={this.onChange}
                />
            </div>
        </div>
        <ExternalDataBind
            name={this.signatureProperty.newControlName}
            id='externalDataBind'
            visible={this.visible}
            documentCore={this.props.documentCore}
            onChange={this.onChange}
            close={this.onClose}
            properties={this.dataBind}
            resetId={'resetSourceBind'}
        />
        {/* <div className='editor-line'>
            <div className='w-120'>签名缩放比例</div>
            <div className='right-auto-210'>
                <Input
                    name='signatureRatio'
                    value={this.signatureProperty.signatureRatio}
                    onChange={this.onChange}
                    type='number'
                    step={0.1}
                    min={0.1}
                    max={1.0}
                    disabled={this.state.disableSignatureRatio}
                />
            </div>
            <div className='w-90 row-height-restriction'>
                <Checkbox
                    name='rowHeightRestriction'
                    value={this.signatureProperty.rowHeightRestriction}
                    onChange={this.onChange}
                >
                    行高约束
                </Checkbox>
            </div>
        </div> */}
      </React.Fragment>
    );
  }

  private renderFooter(): any {
    return (
        <span>
            <Button onClick={this.close}>取消</Button>
            <Button type='primary' onClick={this.confirm}>确认</Button>
        </span>
    );
  }

  private onClose = (id: string, bRefresh: boolean): void => {
    this[id] = false;
    if (id === 'bCustomProperty' && bRefresh) {
        this.setState({bRefresh: !this.state.bRefresh});
    } else if ('externalDataBind' === id && bRefresh) {
      this.resetSourceBind = false;
      this.setState({bRefresh: !this.state.bRefresh});
    }
  }

  private open = (): void => {
    const props = this.props.property;
    const newControlProps: INewControlProperty = this.signatureProperty = {} as any;
    if (props === undefined) {
      this.init();
      newControlProps.newControlName = this.props.documentCore.makeUniqueNewControlName(NewControlType.SignatureBox);
    } else {
      const keys = Object.keys(props);
      keys.forEach((key) => {
          const val = props[key];
          newControlProps[key] = val;
      });
      this.bDisableType = true;
      // this.bDisableAlwaysShow = true;
      this.bDisableSignatureCount = true;
    }
    this.dataBind = newControlProps.externalDataBind;
    this.setState({bRefresh: !this.state.bRefresh});
  }

  private init(): void {
    const signatureProperty = this.signatureProperty;
    signatureProperty.newControlName = undefined;
    signatureProperty.newControlSerialNumber = undefined;
    signatureProperty.newControlInfo = undefined;
    signatureProperty.newControlPlaceHolder = ''; // no space at all
    signatureProperty.newControlType = NewControlType.SignatureBox;
    signatureProperty.isNewControlHidden = false;
    signatureProperty.isNewControlCanntDelete = false;
    signatureProperty.isNewControlCanntEdit = true;
    signatureProperty.isNewControlShowBorder = false;
    signatureProperty.isNewControlHiddenBackground = true;
    signatureProperty.isNewControlCanntCopy = false;
    signatureProperty.customProperty = undefined;

    // extension
    signatureProperty.signatureCount = STD_START_DEFAULT.signatureCount;
    signatureProperty.preText = STD_START_DEFAULT.preText;
    signatureProperty.signatureSeparator = STD_START_DEFAULT.signatureSeparator;
    signatureProperty.postText = STD_START_DEFAULT.postText;
    signatureProperty.signaturePlaceholder = STD_START_DEFAULT.signaturePlaceholder;
    signatureProperty.signatureRatio = STD_START_DEFAULT.signatureRatio;
    signatureProperty.rowHeightRestriction = STD_START_DEFAULT.rowHeightRestriction === 1 ? true : false;

    signatureProperty.signType = SignatureType.Common;
    signatureProperty.alwaysShow = signatureProperty.signatureCount;
    signatureProperty.showSignBorder = true;
    signatureProperty.identifier = undefined;
    signatureProperty.externalDataBind = undefined;

    this.resetSourceBind = false;
    this.bDisableType = false;
    // this.bDisableAlwaysShow = false;
    this.bDisableSignatureCount = false;
  }

  private onChange = (value: any, name: string): void => {

    if (name !== 'signatureSeparator' && name !== 'signatureSeparator2') {
      this.signatureProperty[name] = value;
    }

    if (name === 'signatureCount2') {
      this.signatureProperty.signatureCount = value;
    }

    const signSeparator = this.signatureProperty.signatureSeparator;
    if (name === 'rowHeightRestriction') {
      this.setState({disableSignatureRatio: !this.state.disableSignatureRatio});
    } else if (name === 'signatureSeparator') {
      // just check the first & for simplicity. PM suggested
      if (SignatureType.Common === this.signatureProperty.signType) {
        const indexOfAmpersand = signSeparator.indexOf('&');
        if (indexOfAmpersand === -1) {
          // TODO?
          this.signatureProperty[name] = value;
        } else {
          const secondHalf = signSeparator.slice(signSeparator.indexOf('&'));
          this.signatureProperty[name] = value + secondHalf; // still contains &
        }
      } else {
        this.signatureProperty[name] = value;
      }

    } else if (name === 'signatureSeparator2') {
      if (SignatureType.Common === this.signatureProperty.signType) {
        if (this.signatureProperty.signatureCount !== SignatureCountType.Triple) {
            // tslint:disable-next-line: no-console
            console.warn('Unreasonable access to signatureSeparator2 detected and stopped');
            return;
        } else {
          const firstHalf = signSeparator.slice(0, signSeparator.indexOf('&') + 1);
          this.signatureProperty.signatureSeparator = firstHalf + value; // keep &
        }
      }
    } else if (name === 'signatureCount') {
      if (SignatureType.Common === this.signatureProperty.signType) {
        if (this.signatureProperty.signatureCount === SignatureCountType.Triple) {
          if (signSeparator.indexOf('&') === -1) {
            this.signatureProperty.signatureSeparator += '&' + STD_START_DEFAULT.signatureSeparator;
          }
        }
      }
      this.setState({});
    } else if ('signType' === name) {
      if (SignatureType.Common === value) {
        this.signatureProperty.showSignBorder = true;
      }
      this.signatureProperty.signatureCount = 1;
      this.signatureProperty.alwaysShow = this.signatureProperty.signatureCount;

      this.setState({});
    } else if (name === 'alwaysShow') {
      if (SignatureType.Common === this.signatureProperty.signType) {
        this.signatureProperty[name] = this.signatureProperty.signatureCount;
      }
    } else if ('externalDataBind' === name) {
        this.dataBind = value;
    } else if ('resetSourceBind' === name) {
        this.resetSourceBind = true;
    }
  }

  private close = (bRefresh?: boolean): void => {
    this.props.close(this.props.id, bRefresh);
    this.visible = false;
    this.setState({bRefresh: !this.state.bRefresh});
  }

  private confirm = (): void => {
    const documentCore = this.props.documentCore;
    IFRAME_MANAGER.setDocId(documentCore.getCurrentId());
    const props = this.props.property;
    const signatureProperty = this.signatureProperty;
    // const textProps = this.prepareTextProperties();
    // console.log(props)

    if (!isValidName(signatureProperty.newControlName)) {
      message.error('名称不符合规范，请重新输入');
      return;
    }
    if (signatureProperty.identifier && !isValidName(signatureProperty.identifier)) {
        message.error('标识符不符合规范，请重新输入');
        return;
    }
    if ((!props || props.newControlName !== signatureProperty.newControlName)
        && !documentCore.checkNewControlName(signatureProperty.newControlName)) {
        message.error('已存在该名字，请重新命名');
        return;
    }

    if (this.isSignSet()) {
        if (this.signatureProperty.alwaysShow > this.signatureProperty.signatureCount) {
          message.error('常驻显示数目不能大于签名个数！');
          return ;
        }

        this.signatureProperty.signatureSeparator = '';
    }

    if (this.resetSourceBind) {
        signatureProperty.externalDataBind = {
            sourceObj: '',
            sourceKey: '',
            bReadOnly: false,
            commandUpdate: 1,
        }

        this.resetSourceBind = false;
    } else if (this.dataBind) {
        signatureProperty.externalDataBind = this.dataBind;
    }

    if (props === undefined) {
      documentCore.addNewControl(signatureProperty);
      // documentCore.addNewControl(this.textProperty);
      // documentCore.addNewControl(textProps[0]);
      // documentCore.addNewControl(textProps[1]);
    } else {
      documentCore.setNewControlProperty(signatureProperty, props.newControlName);
    }

    this.close(true);
  }

  /**
   * getSignSeparatorVal
   * @param index which signSeparator box: 1 // 2
   */
  private getSignSeparatorVal(index: number = 1): string {
    const {signatureSeparator, signatureCount} = this.signatureProperty;
    let result = signatureSeparator;

    // no matter what signCount is, first signSeparator should always be separated
    const indexofAmpersand = signatureSeparator.indexOf('&');
    if (indexofAmpersand !== -1) {
      // console.log(signatureSeparator)
      if (index === 1) {
          result = signatureSeparator.slice(0, indexofAmpersand);
      } else {
          result = signatureSeparator.slice(indexofAmpersand + 1);
      }
    }

    return result;
  }

  private isSignSet(): boolean {
    return (SignatureType.Set === this.signatureProperty.signType);
  }

}
