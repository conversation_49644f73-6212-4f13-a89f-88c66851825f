import * as React from 'react';

interface ISelectListProps {
    data: object[];
    value: any;
    height?: number | string;
    props: {name: string, value: string, disabled?: string};
    change: (item: any, index?: number) => void;
}

interface ISelectListStates {
    name: string;
}

export default class SelectList extends React.Component<ISelectListProps, ISelectListStates> {
    private _value: any;
    private _oldVal: any;
    private _activeIndex: number;
    private _ref: any;
    constructor(props: any) {
        super(props);
        this.state = {
            name: '',
        };
        this._ref = React.createRef();
    }

    public render(): any {
        let height: string;
        const actheight = this.props.height;
        if (actheight !== undefined) {
            height = typeof actheight === 'number' ? actheight + 'px' : actheight;
        }
        return (
            <div className={'select-list'} style={{height}}>
                <div className={'select-list-value'}>{this.state.name}</div>
                <ul ref={this._ref}>
                    {this.renderList(this.props.data)}
                </ul>
            </div>
        );
    }

    public UNSAFE_componentWillReceiveProps(nextProps: ISelectListProps): void {
        this.init(nextProps.value);
    }

    public componentDidMount(): void {
        this._ref.current.addEventListener('click', this.domClickHandler);
    }

    public componentWillUnmount(): void {
        this._ref.current?.removeEventListener('click', this.domClickHandler);
    }

    private init(value: any): void {
        if (value === null) {
            this._oldVal = value;
            this._value = value;
            return;
        }
        if (this._oldVal === value) {
            return;
        }

        this._oldVal = value;
        const props = this.props.props;
        let item: any;
        const datas = this.props.data;
        let actIndex: number;
        const key = props.value;
        for (let index = 0, len = datas.length; index < len; index++) {
            const data = datas[index];
            if (data[key] == value) {
                item = data;
                actIndex = index;
                break;
            }
        }

        if (item) {
            this.change(item, actIndex);
        } else if (this.state.name !== '') {
            this.setName('');
            this._activeIndex = -1;
        }
    }

    private setName(name: string): void {
        this.setState({name});
    }

    private renderList(datas: object[]): any {
        const props = this.props.props;
        const activeIndex = this._activeIndex;
        return datas.map((data, index) => {
            return (
                <li
                    key={index}
                    data-index={index}
                    className={activeIndex === index ? 'active' : ''}
                >
                    {data[props.name]}
                </li>
            );
        });
    }

    private domClickHandler = (event: any): void => {
        const target = event.target;
        if (target.tagName !== 'LI') {
            return;
        }

        const datas = this.props.data;
        const index = parseInt(target.getAttribute('data-index'), 10);
        if (isNaN(index)) {
            return;
        }

        this.handlerClick(datas[index], index);
    }

    private change(item: any, index: number): void {
        const props = this.props.props;
        this._activeIndex = index;
        this._value = item[props.value];
        this.setName(item[props.name]);
        this.props.change(item, index);
    }

    private handlerClick(item: any, index: number): void {
        const props = this.props.props;
        if (item[props.disabled] === true) {
            return;
        }
        const oldValue = this._value;
        const value = item[props.value];
        if (oldValue === value) {
            return;
        }
        this.change(item, index);
    }
}
