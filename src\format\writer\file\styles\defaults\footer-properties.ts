// import { Size, <PERSON>ze<PERSON>omplexScript } from "../../paragraph/run/formatting";
// import { RunProperties } from "../../paragraph/run/properties";
// import { RunFonts } from "../../paragraph/run/run-fonts";
import { XmlComponent } from '../../xml-components';

export class FooterPropertiesDefaults extends XmlComponent {
    // private readonly properties: RunProperties;

    constructor() {
        super('w:fdrDefault');
        // this.properties = new RunProperties();
        // this.root.push(this.properties);
    }

    // public size(size: number): RunPropertiesDefaults {
    //     this.properties.push(new Size(size));
    //     this.properties.push(new SizeComplexScript(size));
    //     return this;
    // }

    // public font(fontName: string): RunPropertiesDefaults {
    //     this.properties.push(new RunFonts(fontName));
    //     return this;
    // }
}
