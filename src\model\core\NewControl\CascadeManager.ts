import { CascadeTriggerType, CodeAndValue, ICascade, ICascadeEvent, IEventProps, ITriggerEventInfo, ResultType
} from '../../../common/commonDefines';
import { NewControlManager } from '../NewControlManager';
import { NewControl } from './NewControl';

interface ICascadeOption {
    cascades?: ICascade[]; // 级联
    eventInfos?: Map<string, CascadeTriggerType>; // 触发源，添加事件时，主动添加的信息
    events?: ICascadeEvent; // 最终触发时填充的元素
}

export class CascadeManager {
    private newControlManager: NewControlManager;
    private datas: Map<string, ICascadeOption>;
    private _caches: Map<string, ICascadeOption>;
    private bTrigger: boolean;
    constructor(manager: NewControlManager) {
        this.newControlManager = manager;
        this.datas = new Map();
    }

    /**
     * 随着结果化元素删除而清除
     * @param name 结构化名称
     */
    public deleteCascade(newControl: NewControl): void {
        this.deleteNewControls(newControl);
    }

    /**
     * 更新级联名称
     * @param old 旧的级联名称
     * @param newName 新的
     * @returns 无
     */
    public updateNewControlName(old: string, newName: string): void {
        const cascade = this.datas.get(old);
        if (!cascade) {
            return;
        }
        this.datas.set(newName, cascade);
        this.datas.delete(old);
    }

    public getTriggetEventInfo(newControl: NewControl): Map<string, CascadeTriggerType> {
        const cascade = this.datas.get(newControl.getNewControlName());
        if (!cascade) {
            return;
        }

        return cascade.eventInfos;
    }

    public getTriggetEventInfo2(newControl: NewControl): ITriggerEventInfo[] {
        const cascade = this.datas. get(newControl.getNewControlName());
        if (!cascade) {
            return;
        }

        const infos = cascade.eventInfos;
        if (!infos || infos.size === 0) {
            return;
        }
        const res = [];
        for (const [name, type] of infos) {
            res.push({name, type});
        }

        return res;
    }

    public setTriggetEventInfo(info: ITriggerEventInfo, newControl: NewControl): number {
        const name = newControl.getNewControlName();
        let cascade = this.datas.get(name);
        if (!cascade) {
            cascade = {eventInfos: undefined};
            this.datas.set(name, cascade);
        }
        let old = cascade.eventInfos;
        if (!old) {
            old = cascade.eventInfos = new Map();
        }

        old.set(info.name, info.type);
        return ResultType.Success;
    }

    public removeTriggerEventInfo(name: string, newControl: NewControl): void {
        const cascade = this.datas. get(newControl.getNewControlName());
        if (!cascade || !cascade.eventInfos) {
            return;
        }
        cascade.eventInfos.delete(name);
    }

    public setCascades(newControl: NewControl, cascades: ICascade[], bClearItems: boolean = true): number {
        if (!cascades) {
            return ResultType.UnEdited;
        }

        const name = newControl.getNewControlName();
        let cascade = this.datas.get(name);
        if (cascade && this.isSameCascade(cascades, cascade.cascades)) {
            return ResultType.UnEdited;
        }

        if (!cascade) {
            cascade = {cascades: undefined};
            this.datas.set(name, cascade);
        }

        if (bClearItems) {
            cascade.cascades = cascades;
        }

        return ResultType.Success;
    }

    public getCascades(newControl: NewControl): ICascade[] {
        const cascade = this.datas.get(newControl.getNewControlName());
        if (cascade) {
            return cascade.cascades;
        }
        return;
    }

    public getCascade(name: string): ICascadeOption {
        return this.datas.get(name);
    }

    public getCascadeMap(): Map<string, ICascadeOption> {
        return this.datas;
    }

    public setEvent(event: ICascadeEvent, newControl: NewControl, force = false): number {
        if (event === undefined) {
            return ResultType.UnEdited;
        }

        const name = newControl.getNewControlName();
        let eventInfo: ICascadeEvent;
        const cascade = this.datas.get(name);
        if (cascade) {
            if (cascade.events) {
                if (!event || !event.event || !event.event.length) {
                    cascade.events = undefined;
                    return ResultType.Success;
                }
                if (this.isSameEventInfo(event, cascade.events) && !force) {
                    return ResultType.UnEdited;
                }
                this.newControlManager.setEventInfo(newControl, event);
                eventInfo = cascade.events;
                eventInfo.action = event.action;
                eventInfo.target = event.target;
                eventInfo.key = event.key;
                eventInfo.expression = event.expression;
                eventInfo.event = (event.event || []).map((item) => {
                    return {...item};
                });
            } else {
                this.newControlManager.setEventInfo(newControl, event);
                cascade.events = event;
            }
        } else {
            this.newControlManager.setEventInfo(newControl, event);
            eventInfo = event;
            // eventInfo = {} as any;
            // eventInfo.action = event.action;
            // eventInfo.target = event.target;
            // eventInfo.key = event.key;
            // eventInfo.event = (event.event || []).map((item) => {
            //     return {...item};
            // });
            this.datas.set(name, {events: eventInfo});
        }

        return ResultType.Success;
    }

    public getEvent(newControl: NewControl): ICascadeEvent {
        const cascade = this.datas.get(newControl.getNewControlName());
        if (cascade) {
            return cascade.events;
        }
    }

    public isSameEventInfo(info: ICascadeEvent, old: ICascadeEvent): boolean {
        if (!old) {
            return false;
        }
        if (old.target !== info.target || old.action !== info.action || old.key !== info.key) {
            return false;
        }

        // equals between 'expression'
        if (info.key == CodeAndValue.Expression && old.expression !== info.key) {
            return false;
        }

        // equals between 'event'
        const oldEvent = old.event;
        const event = info.event;
        if (!oldEvent || !event || event.length !== oldEvent.length) {
            return false;
        }
        for (let index = 0, len = oldEvent.length; index < len; index++) {
            const oldItem = oldEvent[index];
            const current = event[index];
            if (oldItem.source !== current.source || oldItem.triggerType !== current.triggerType) {
                return false;
            }
        }
        return true;
    }

    public getAllCascades(bChecked: boolean = false): any[] {
        const datas = this.datas;
        if (datas.size === 0) {
            return;
        }

        const arrs = [];
        for (const [name, option] of  datas) {
            const infos = option.eventInfos;
            let eventInfos: any;
            if (infos) {
                eventInfos = {};
                for (const [actName, info] of infos) {
                    // if (bChecked && !this.checkEventInfo(name, actName)) {
                    //     continue;
                    // }
                    eventInfos[actName] = info;
                }
            }

            arrs.push({name, option: {events: option.events, eventInfos, cascades: option.cascades}});
        }

        return arrs;
    }

    public updateEventSource(newControl: NewControl): void {
        const name = newControl.getNewControlName();
        const cascade = this.datas.get(name);
        if (!cascade) {
            return;
        }
        const events = cascade.events;
        let source;
        const datas = events.event;
        const ids = {};
        datas.forEach((data) => {
            if (!data.source) {
                return;
            }
            if (data.source === '*') {
                source = events.target;
                return;
            }

            const arrs = data.source.split(',');
            arrs.forEach(((arr) => {
                if (!arr) {
                    return;
                }
                ids[arr] = true;
            }));
        });
        if (source) {
            source.split(',')
            .forEach((arr) => {
                ids[arr] = true;
            });
        }
        const keys = Object.keys(ids);
        events.target = keys.join(',');
    }

    public addCaches(caches: Map<string, ICascadeOption>): void {
        this._caches = caches;
    }

    public addCascadeByCaches(): void {
        if (!this._caches) {
            return;
        }
        const datas = this._caches;
        const newControls = this.newControlManager.getNameMap();
        const map = this.datas;
        const nameArrs: any = {};
        const sourceAll = {};
        for (const [name, option] of datas) {
            const events = option.events;
            if (events && events.event.length) {
                sourceAll[name] = events.target;
                nameArrs[name] = events.event;
            }

            map.set(name, option);
        };

        for (const name of Object.keys(nameArrs)) {
            const events = nameArrs[name];
            const data = map.get(name);
            const isExpression = data.events?.key === CodeAndValue.Expression;
            events.forEach((event) => {
                let source = event.source;
                if (isExpression) {
                    source = sourceAll[name];
                } else {
                    if (!source) {
                        return;
                    }
                    if (source === '*') {
                        source = sourceAll[name];
                    }
                }
                const ids = source.split(',');
                ids.forEach((id) => {
                    const newControl = newControls.get(id);
                    if (!newControl) {
                        return;
                    }
                    this.setTriggetEventInfo({name, type: event.triggerType}, newControl);
                });
            });
        }
        this._caches = null;
    }

    public initOpenDoc(datas: {name: string; option: ICascadeOption}[]): void {
        if (!datas || datas.length === 0) {
            return;
        }
        const newControls = this.newControlManager.getNameMap();
        const map = new Map<string, ICascadeOption>();
        const nameArrs: any = {};
        const sourceAll = {};
        datas.forEach((data) => {
            const option = data.option;
            const events = option.events;
            option.eventInfos = undefined;
            const name = data.name;
            if (events && events.event.length) {
                sourceAll[name] = events.target;
                nameArrs[name] = events.event;
            }

            map.set(name, data.option);
        });
        this.datas = map;
        for (const name of Object.keys(nameArrs)) {
            const events = nameArrs[name];
            const data = map.get(name);
            const isExpression = data.events?.key === CodeAndValue.Expression;
            events.forEach((event) => {
                let source = event.source;
                if (isExpression) {
                    source = sourceAll[name];
                } else {
                    if (!source) {
                        return;
                    }
                    if (source === '*') {
                        source = sourceAll[name];
                    }
                }
                const ids = source.split(',');
                ids.forEach((id) => {
                    const newControl = newControls.get(id);
                    if (!newControl) {
                        return;
                    }
                    this.setTriggetEventInfo({name, type: event.triggerType}, newControl);
                });
            });
        }
    }

    public triggerCascade(newControl: NewControl): boolean {
        if (this.bTrigger) {
            return;
        }
        const newControlManager = this.newControlManager;
        const name = newControl.getNewControlName();
        if (newControlManager.isOldCascade(name)) {
            return;
        }
        this.bTrigger = true;

        const bHidden = newControl.isHidden();
        newControlManager.addRunCascade(name);
        newControlManager.setTriggerTime(3);
        this.newControlManager.addCascadeMap(newControl, null);
        const res = this.newControlManager.triggerCascadeMap();
        this.newControlManager.clearCascadeMap();
        this.bTrigger = false;
        newControlManager.setTriggerTime(undefined);
        const newHidden = newControl.isHidden();
        if (newHidden === true && bHidden !== newHidden) {
            const startPara = newControl.getParagraph();
            const currentIndex = startPara.parent.curPos.contentPos;
            if (startPara.index <= currentIndex) {
                const endPortion = newControl.getEndBorderPortion();
                const endPara = endPortion.paragraph;
                if (endPara.index >= currentIndex) {
                    const endIndex = endPara.content.findIndex((item) => item === endPortion);
                    startPara.parent.curPos.contentPos = endPara.index;
                    endPara.curPos.contentPos = endIndex + 1;
                    endPortion.portionContentPos = 0;
                    this.newControlManager.getDoc()
                    .updateCursorXY();
                }
            }
        }
        return res === ResultType.Success;
    }

    public triggerEvent(newControl: NewControl): boolean {
        const name = newControl.getNewControlName();
        const cascade = this.datas.get(name);
        if (!cascade || !cascade.eventInfos) {
            return false;
        }

        return this.newControlManager.triggerEvent(name);
    }

    public clear(): void {
        this.datas.clear();
    }

    public deleteNewControls(newControl: NewControl): void {
        const list = newControl.getLeafList();
        this.deleteEachNewControl(list);
        this.datas.delete(newControl.getNewControlName());
    }

    public deleteEachNewControl(leafList: NewControl[]): void {
        if (!leafList || leafList.length === 0) {
            return;
        }

        leafList.forEach((item) => {
            const list = item.getLeafList();
            this.deleteEachNewControl(list);
            this.datas.delete(item.getNewControlName());
        });
    }

    private isSameCascade(cascades: ICascade[], oldCascades: ICascade[]): boolean {
        const len = (oldCascades ? oldCascades.length : 0);
        if (cascades.length !== len) {
            return false;
        }
        for (let index = 0; index < len; index++) {
            const oldCascade = oldCascades[index];
            const actCascade = cascades[index];
            if (oldCascade == null || actCascade == null) {
                return false;
            }
            for (const key in oldCascade) {
                if (oldCascade[key] !== actCascade[key]) {
                    return false;
                }
            }
        }

        return true;
    }

    private setTriggetEventInfo2(info: ITriggerEventInfo, newControl: NewControl, datas: Map<string, any>): number {
        const name = newControl.getNewControlName();
        let cascade = datas.get(name);
        if (!cascade) {
            cascade = {eventInfos: undefined};
            datas.set(name, cascade);
        }
        let old = cascade.eventInfos;
        if (!old) {
            old = cascade.eventInfos = new Map();
        }

        old.set(info.name, info.type);
        return ResultType.Success;
    }

    private checkEventInfo(name: string, actName: string): boolean {
        const cascade = this.datas.get(actName);
        if (!cascade) {
            return false;
        }
        const event = cascade.events;
        if (!event) {
            return false;
        }

        const eventInfos = event.event;
        if (!eventInfos || eventInfos.length === 0) {
            return false;
        }

        const index = eventInfos.findIndex((item) => {
            let source = item.source;
            if (!source) {
                return false;
            }
            if (source === '*') {
                source = event.target;
            }
            const arrs = source?.split(',');
            if (arrs && arrs.includes(name)) {
                return true;
            }

            return false;
        });

        return index !== -1;
    }
}
