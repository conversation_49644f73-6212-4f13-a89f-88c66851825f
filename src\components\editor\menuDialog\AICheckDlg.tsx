import * as React from 'react';
import DialogAI from '../ui/DialogAI';
import './AICheckDlg.less';
import { EmrEditor } from '../../../components/editor/Main';
import { skipEscapeString } from '../../../common/commonMethods';
import OperateDocument from '@/common/external/OperateDocument';


// 内联 SVG 图标组件
const Icons = {
    AI: () => (
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M2 17L12 22L22 17" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M2 12L12 17L22 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
    ),
    Check: () => (
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M20 6L9 17L4 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
    ),
    Dict: () => (
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M4 19.5V4.5C4 3.67157 4.67157 3 5.5 3H18.5C19.3284 3 20 3.67157 20 4.5V19.5C20 20.3284 19.3284 21 18.5 21H5.5C4.67157 21 4 20.3284 4 19.5Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M12 3V21" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
    ),
    Text: () => (
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M4 6H20" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M4 12H20" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M4 18H12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
    ),
    Settings: () => (
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 15C13.6569 15 15 13.6569 15 12C15 10.3431 13.6569 9 12 9C10.3431 9 9 10.3431 9 12C9 13.6569 10.3431 15 12 15Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M19.4 15C19.1277 15.6171 19.2583 16.3378 19.73 16.82L19.79 16.88C20.1659 17.2555 20.3791 17.7644 20.3791 18.295C20.3791 18.8256 20.1659 19.3345 19.79 19.71C19.4145 20.0859 18.9056 20.2991 18.375 20.2991C17.8444 20.2991 17.3355 20.0859 16.96 19.71L16.9 19.65C16.4178 19.1783 15.6971 19.0477 15.08 19.32C14.4715 19.5853 14.0871 20.1764 14.09 20.83V21C14.09 22.1046 13.1946 23 12.09 23C10.9854 23 10.09 22.1046 10.09 21V20.91C10.0853 20.2425 9.67618 19.6516 9.05 19.4C8.43292 19.1277 7.71224 19.2583 7.23 19.73L7.17 19.79C6.79452 20.1659 6.28559 20.3791 5.755 20.3791C5.22441 20.3791 4.71548 20.1659 4.34 19.79C3.96409 19.4145 3.75087 18.9056 3.75087 18.375C3.75087 17.8444 3.96409 17.3355 4.34 16.96L4.4 16.9C4.87167 16.4178 5.00231 15.6971 4.73 15.08C4.46469 14.4715 3.87359 14.0871 3.22 14.09H3C1.89543 14.09 1 13.1946 1 12.09C1 10.9854 1.89543 10.09 3 10.09H3.09C3.75753 10.0853 4.34844 9.67618 4.6 9.05C4.87231 8.43292 4.74167 7.71224 4.27 7.23L4.21 7.17C3.83409 6.79452 3.62087 6.28559 3.62087 5.755C3.62087 5.22441 3.83409 4.71548 4.21 4.34C4.58548 3.96409 5.09441 3.75087 5.625 3.75087C6.15559 3.75087 6.66452 3.96409 7.04 4.34L7.1 4.4C7.58224 4.87167 8.30292 5.00231 8.92 4.73H9C9.60845 4.46469 9.99285 3.87359 9.99 3.22V3C9.99 1.89543 10.8854 1 11.99 1C13.0946 1 13.99 1.89543 13.99 3V3.09C13.9929 3.74359 14.3772 4.33469 14.99 4.6C15.6071 4.87231 16.3278 4.74167 16.81 4.27L16.87 4.21C17.2455 3.83409 17.7544 3.62087 18.285 3.62087C18.8156 3.62087 19.3245 3.83409 19.7 4.21C20.0759 4.58548 20.2891 5.09441 20.2891 5.625C20.2891 6.15559 20.0759 6.66452 19.7 7.04L19.64 7.1C19.1683 7.58224 19.0377 8.30292 19.31 8.92V9C19.5753 9.60845 20.1664 9.99285 20.82 9.99H21C22.1046 9.99 23 10.8854 23 11.99C23 13.0946 22.1046 13.99 21 13.99H20.91C20.2564 13.9929 19.6653 14.3772 19.4 14.99V15Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
    ),
    ArrowRight: () => (
        <svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M9 18L15 12L9 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
    )
};

interface IProps {
    documentCore: any;
    host: any;
    visible?: boolean;
    id: string;
    close: (name: string | number, bRefresh?: boolean) => void;
}

interface IState {
    bRefresh: boolean;
    content: string;
    isCollapsed: boolean;
    activeTab: number;
    errors: ErrorItem[];
    correctedText: string;
    summary: string;
    expandedItems: Set<number>; 
    isReplaced: boolean;
}

interface ErrorItem {
    type: string;
    position: string;
    start: number;
    end: number;
    error: string;
    correction: string;
    explanation: string;
}

export default class AICheckDialog extends React.Component<IProps, IState> {
    private visible: boolean;
    private _host: EmrEditor;
    private _originalContent: string;
    private _simplifiedContent:string;
    private _sFatherName:string;
    private _nType:number;
    private _sAIRepos:string;
    private _operateDocument: OperateDocument;
    constructor(props: IProps) {
        super(props);
        this._host = props.host;
        this._originalContent = this._simplifiedContent  = this._sAIRepos =  "";
        this.state = {
            bRefresh: false,
            content: '',
            isCollapsed: false,
            activeTab: 0,
            errors: [],
            correctedText: '',
            summary: '',
            expandedItems: new Set<number>(),
            isReplaced: false,
        };
        this.visible = this.props.visible;
    }

    protected _getOperateDocument(): OperateDocument {
        if (!this._operateDocument) {
            this._operateDocument = new OperateDocument(this._host);
        }

        return this._operateDocument;
    }

    public render(): React.ReactNode {
        const { isCollapsed, activeTab } = this.state;
        
        const dialogClassName = `ai-check-dialog${isCollapsed ? ' collapsed' : ''}`;
        
        return (
            <DialogAI
                visible={this.visible}
                width={isCollapsed ? 24 : 340}
                close={this.handleDialogClose}
                open={this.open}
                noModal={true}
                className={dialogClassName}
                title=""
                left={20}
                right="auto"
                top={150}
            >
                <div className={`ai-dialog-container${isCollapsed ? ' collapsed' : ''}`}>
                    <div className="ai-dialog-nav">
                        <div className="nav-row">
                            <div className={`nav-item${activeTab === 0 ? ' active' : ''}`} onClick={() => this.handleTabClick(0)}>
                                <Icons.AI />
                                <span>AI检查</span>
                            </div>
                            <div className={`nav-item${activeTab === 1 ? ' active' : ''}`} onClick={() => this.handleTabClick(1)}>
                                <Icons.Check />
                                <span>指标校验</span>
                            </div>
                            <div className={`nav-item${activeTab === 2 ? ' active' : ''}`} onClick={() => this.handleTabClick(2)}>
                                <Icons.Dict />
                                <span>名词解释</span>
                            </div>
                        </div>
                        <div className="nav-row">
                            <div className={`nav-item${activeTab === 3 ? ' active' : ''}`} onClick={() => this.handleTabClick(3)}>
                                <Icons.Text />
                                <span>文本摘要</span>
                            </div>
                            <div className={`nav-item${activeTab === 4 ? ' active' : ''}`} onClick={() => this.handleTabClick(4)}>
                                <Icons.Settings />
                                <span>设置</span>
                            </div>
                        </div>
                    </div>
                    <div className="ai-dialog-content">
                        {this.renderContent()}
                    </div>
                    <div className="ai-dialog-close" onClick={this.handleCloseClick}>×</div>
                    <div className="ai-dialog-collapse" onClick={this.handleCollapse}>
                        <Icons.ArrowRight />
                    </div>
                </div>
            </DialogAI>
        );
    }

    public componentWillReceiveProps(nextProps: IProps): void {
        this.visible = nextProps.visible;
    }

    private open = (): void => {
        this.setState({bRefresh: !this.state.bRefresh});
    }

    // Add this new function to handle error item selection
    private handleErrorItemSelect = (error: ErrorItem, index: number): void => {
        // Toggle expansion first (keep existing functionality)
        this.toggleErrorItem(index);
        
    }

    private handleDialogClose = (bRefresh?: boolean): void => {
        this.visible = false;
        this.props.close(this.props.id, bRefresh);
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private handleCloseClick = (e: React.MouseEvent<HTMLDivElement>): void => {
        e.preventDefault();
        this.handleDialogClose();
    }

    private handleTabClick = (index: number): void => {
        this.setState({ activeTab: index }, () => {
            switch (index) {
                case 0:
                    this.handleAICheck();
                    break;
                case 1:
                    this.handleMetricsValidation();
                    break;
                case 2:
                    this.handleTermExplanation();
                    break;
                case 3:
                    this.handleTextSummary();
                    break;
                case 4:
                    this.handleSettings();
                    break;
            }
        });
    }

    //获取当前选中控件的文本内容
    private async getDocumentInfo(): Promise<{ originalXml: string; simplifiedXml: string; fatherName: string; nType:number }> {
        const sName = this.props.documentCore.getCurrentNewControlName();
        let sFatherName = "";
    
        if (sName) {
            // sName 不为空的情况
            const fatherRegion = this.props.documentCore.getFatherRegionNameOfOneStruct(sName);
            
            if (fatherRegion) {
                // 如果有父级区域，获取区域文本
                sFatherName = fatherRegion;
                const response = await this._getOperateDocument().extractContentForAICorrection(sFatherName, 1);
                const { originalXml = "", simplifiedXml = "" } = JSON.parse(response);
                return { originalXml, simplifiedXml, fatherName: sFatherName, nType: 1 };
            } else {
                sFatherName = this.props.documentCore.getFatherNewControlName(sName);
                if(!sFatherName) {
                    sFatherName = sName;
                }
                const response = await this._getOperateDocument().extractContentForAICorrection(sFatherName, 2);
                const { originalXml = "", simplifiedXml = "" } = JSON.parse(response);
                return { originalXml, simplifiedXml, fatherName: sFatherName, nType: 2 };
            }
        } else {
            // sName 为空的情况
            const region = this.props.documentCore.getCurrentRegion();
            if(!region) {
                return { originalXml: "", simplifiedXml: "", fatherName: "", nType: 0 };
            }
            const sRegion = region.getName();
    
            if (sRegion) {
                // sRegion 不为空，获取区域文本
                sFatherName = sRegion;
                const response = await this._getOperateDocument().extractContentForAICorrection(sFatherName, 1);
                const { originalXml = "", simplifiedXml = "" } = JSON.parse(response);
                return { originalXml, simplifiedXml, fatherName: sFatherName, nType: 1 };
            } else {
                // sRegion 为空，返回空
                return { originalXml: "", simplifiedXml: "", fatherName: "", nType: 0 };
            }
        }
    }
    
    // AI检查
    private handleAICheck = async (): Promise<void> => {
        const { originalXml, simplifiedXml, fatherName, nType } = await this.getDocumentInfo();
        this._originalContent = originalXml;
        this._simplifiedContent = simplifiedXml;
        this._sFatherName = fatherName;
        this._nType = nType;
   
        if (simplifiedXml.length < 2) {
            this.setState({
                errors: [],
                correctedText: "",
                summary: "文字长度过短 (<2)"
            });
            return; // 直接返回，不发送请求
        }
        
        fetch('http://localhost:7072/v1/text-correction', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                text: simplifiedXml,
                user: "张三"
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success && data.data) {
                // 提取 paragraphs 中所有 correctedText 并拼接
                const correctedText = data.data.paragraphs
                    ? data.data.paragraphs.map(p => p.correctedText).join('')
                    : "";
                this._sAIRepos = JSON.stringify(data.data.paragraphs || []);
        
                this.setState({
                    errors: data.data.errors || [],
                    correctedText: correctedText || "",
                    summary: data.data.summary || "",
                    isReplaced: false,
                });
            }
        })
        .catch(error => console.error('Error:', error));
    }

    // 指标校验
    private handleMetricsValidation = (): void => {
        console.log('执行指标校验');
        // TODO: 实现指标校验逻辑
    }

    // 名词解释
    private handleTermExplanation = (): void => {
        console.log('显示名词解释');
        // TODO: 实现名词解释逻辑
    }

    // 文本摘要
    private handleTextSummary = (): void => {
        console.log('生成文本摘要');
        // TODO: 实现文本摘要逻辑
    }

    // 设置
    private handleSettings = (): void => {
        console.log('打开设置');
        // TODO: 实现设置逻辑
    }

    private handleCollapse = (): void => {
        this.setState(prevState => ({ isCollapsed: !prevState.isCollapsed }));
    }

    private toggleErrorItem = (index: number): void => {
        const expandedItems = new Set(this.state.expandedItems);
        if (expandedItems.has(index)) {
            expandedItems.delete(index);
        } else {
            expandedItems.add(index);
        }
        this.setState({ expandedItems });
    }
    private formatSummary(summary: string): React.ReactNode {
        // 检查是否包含数字编号格式（如："1. ", "1、", "（1）"等）
        const hasNumbering = /^\d+[\.\、\）\)]/.test(summary.trim());
        
        if (hasNumbering) {
            // 包含编号时，按数字编号分割成数组
            const points = summary.split(/(?=\d+[\.\、\）\)])\s+/).filter(Boolean);
            return (
                <div className="summary-points">
                    {points.map((point, index) => (
                        <div key={index} className="summary-point">
                            {point.trim()}
                        </div>
                    ))}
                </div>
            );
        } else {
            // 没有编号时，直接返回原文本
            return <div className="summary-text">{summary}</div>;
        }
    }

    /**
     * 将大模型修改的内容映射回原始XML
     * @param originalXml 原始XML字符串
     * @param simplifiedXml 发送给大模型的简化JSON
     * @param modelResponse 大模型返回的JSON
     * @returns 更新后的XML字符串
     */
    private async mapBackToOriginalXml(
        originalXml: string,
        simplifiedXml: string,
        modelResponse: string
    ): Promise<string> {
        try {
            const xmlDoc = new DOMParser().parseFromString(originalXml, "text/xml");
            const simplifiedData = JSON.parse(simplifiedXml);
            const modelArray = JSON.parse(modelResponse) as { id: string; correctedText?: string };

            if (!Array.isArray(modelArray)) {
                throw new Error("Invalid model response format");
            }

            if (!simplifiedData.paragraphs || simplifiedData.paragraphs.length === 0) {
                console.warn("No paragraphs found in simplified data");
                return originalXml;
            }

            // 创建一个从 textUnit id 到 correctedText 的映射，方便查找
            const correctedTextMap: { [key: string]: string | undefined } = {};
            modelArray.forEach(item => {
                correctedTextMap[item.id] = item.correctedText;
            });

            // 遍历 simplifiedData 中的所有段落
            for (const paragraph of simplifiedData.paragraphs) {
                if (paragraph.textUnits && Array.isArray(paragraph.textUnits)) {
                    // 遍历当前段落的所有文本单元
                    for (const textUnit of paragraph.textUnits) {
                        const correctedText = correctedTextMap[textUnit.id];
                        if (correctedText !== undefined) {
                            this.updateTextNodeByXPath(xmlDoc, textUnit.xpath, correctedText);
                        }
                    }
                }
            }

            const serializer = new XMLSerializer();
            return serializer.serializeToString(xmlDoc);
        } catch (error) {
            console.error("Error mapping back to original XML:", error);
            throw new Error("Failed to map back to original XML");
        }
    }

    /**
     * 根据XPath更新文本节点
     * @param xmlDoc XML文档
     * @param xpath 节点的XPath
     * @param newText 新文本内容
     */
    private updateTextNodeByXPath(xmlDoc: Document, xpath: string, newText: string): void {
        const namespaceResolver = {
            lookupNamespaceURI: (prefix: string | null) => {
                if (prefix === 'w') {
                    return 'http://schemas.openxmlformats.org/wordprocessingml/2006/main';
                }
                return null;
            },
        };
    
        const result = xmlDoc.evaluate(xpath, xmlDoc, namespaceResolver, XPathResult.FIRST_ORDERED_NODE_TYPE, null);
        const node = result.singleNodeValue as Element;
    
        if (node) {
            node.textContent = newText;
        } else {
            console.warn(`Node not found for XPath: ${xpath}`);
        }
    }

    private handleReplaceText = async (): Promise<void> => {
        if (this.state.correctedText) {
            const sContent = await this.mapBackToOriginalXml(this._originalContent, this._simplifiedContent, this._sAIRepos);
            const modifications = this._getOperateDocument().replaceContentWithAICorrection(this._sFatherName, sContent, this._nType);
            console.log(modifications);
            
            this.setState({ isReplaced: true });
            // 刷新视图
            this._host.handleRefresh();
        }
    }

    private handleRestoreText = (): void => {
        //恢复原文
        this.setState({ isReplaced: false });
        // 刷新视图
        this._host.handleRefresh();
    }

    private handleCopyText = (): void => {
        //把文本复制到剪切板
        if (this.state.correctedText) {
            navigator.clipboard.writeText(this.state.correctedText);
            return;
        }
    }

    private renderContent(): React.ReactNode {
        const { errors, correctedText, summary, expandedItems } = this.state;
        
        return (
            <div className="ai-content-wrapper">
                <div className="error-list">
                    {errors.map((error, index) => (
                        <div key={index} className="error-item">
                            <div 
                                className="error-item-header"
                                onClick={() => this.handleErrorItemSelect(error, index)}
                            >
                                <span className="error-type">{error.type}</span>
                                <div className="error-content">
                                    <span className="error-text">{error.error}</span>
                                    <span className="arrow">→</span>
                                    <span className="correction-text">{error.correction}</span>
                                </div>
                                <span className={`expand-icon ${expandedItems.has(index) ? 'open' : ''}`}>▼</span>
                            </div>

                            {expandedItems.has(index) && (
                                <div className="explanation">
                                    <div className="explanation-title">详细说明：</div>
                                    <div className="explanation-content">{error.explanation}</div>
                                    <div className="error-position">
                                        <span>位置：</span>
                                        <span>{error.position}</span>
                                    </div>
                                </div>
                            )}
                        </div>
                    ))}
                </div>

                {correctedText && (
                <div className="corrected-text-section">
                    <div className="section-header">
                        <div className="section-title">修正文本：</div>
                        <button 
                            className="copy-button"
                            onClick={this.handleCopyText}
                        >
                            复制
                        </button>
                        <button 
                            className={`restore-button${!this.state.isReplaced ? ' disabled' : ''}`}
                            onClick={this.handleRestoreText}
                            disabled={!this.state.isReplaced}
                        >
                            恢复
                        </button>
                        <button 
                            className={`replace-button${this.state.isReplaced ? ' disabled' : ''}`}
                            onClick={this.handleReplaceText}
                        >
                            替换
                        </button>
                    </div>
                    <div className="section-content">{correctedText}</div>
                </div>
            )}

                {summary && (
                <div className="summary-section">
                    <div className="section-title">摘要：</div>
                    <div className="section-content">
                        {this.formatSummary(summary)}
                    </div>
                </div>
            )}
            </div>
        );
    }
}