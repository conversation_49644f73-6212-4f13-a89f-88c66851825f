import { SPELL_CHECK } from '@/common/Spellcheck';
import { DocumentCore } from '../../model/DocumentCore';
import { ResultType, FontPropName, ParagraphPropName, AlignType, FONST_SIZE_PX, filterChars2 } from '../commonDefines';
import { EmrEditor } from '../../components/editor/Main';
import { ExternalAction } from './ExternalAction';
export default class DocumentProperty extends ExternalAction {
    private _documentCore: DocumentCore;
    private _host: EmrEditor;
    constructor(host: EmrEditor) {
        super(host.state.documentCore);
        this._host = host;
        this._documentCore = host.state.documentCore;
    }


    public getFontProp():string{
       return this._documentCore.getFontProp();
    }

    public markTextWithWave(options?: {content?: string, type?: number}): string | null {
        const result = this._documentCore.markTextWithWave(options);
        if (result) {
            this._host.handleRefresh();
        }

        return result;
    }

    public setFontProp(propName: string, val: any): number {
        let result: number;
        switch (propName) {
            case FontPropName.Italic:
                result = this._setTextItalic(val);
                break;
            case FontPropName.Underline:
                result = this._setTextUnderline(val);
                break;
            case FontPropName.Bold:
                result = this._setTextBold(val);
                break;
            case FontPropName.Sub:
                result = this._setTextVertAlign(val, 1);
                break;
            case FontPropName.Super:
                result = this._setTextVertAlign(val, 2);
                break;
            case FontPropName.Background:
                result = this._setTextBackground(val);
                break;
            case FontPropName.Color:
                result = this._setTextColor(val);
                break;
            case FontPropName.FontSize:
                result = this._setTextFontSize(val);
                break;
            case FontPropName.FontFamily:
                result = this._setTextFontFamily(val);
                break;
            default:
                result = ResultType.ParamError;
        }
        if (result === ResultType.Success) {
            this._host.handleRefresh();
        }

        return result;
    }

    public setParagraphProp(propName: string, nVal: number): number {
        if (typeof nVal !== 'number') {
            return ResultType.ParamError;
        }
        let result: number;
        switch (propName) {
            case ParagraphPropName.FirstLineIndent: // 首行缩进
                result = this._setLineIndent(nVal, 1);
                break;
            case ParagraphPropName.HangingIndent: // 悬挂缩进
                result = this._setLineIndent(nVal, 2);
                break;
            case ParagraphPropName.LeftIndent:
                result = this._setLeftIndent(nVal);
                break;
            case ParagraphPropName.Alignment:
                result = this._setAlignment(nVal);
                break;
            case ParagraphPropName.WordWrap:
                result = this._setWordWrap(nVal);
                break;
            default:
                result = ResultType.ParamError;
        }
        if (result === ResultType.Success) {
            this._host.handleRefresh();
        }

        return result;
    }

    /**
     * 是否开启拼写检查
     * @param bEnable 是否开启
     * @param bRefresh 是否进行马上检查，默认检查
     */
     public enableSpellCheck(bEnable: boolean, bRefresh?: boolean): Promise<number> {
        return new Promise(async (resolve) => {
            if (typeof bEnable !== 'boolean') {
                return resolve(ResultType.ParamError);
            }

            if (bEnable === true) {
                if (SPELL_CHECK.isOpen()) {
                    return resolve(ResultType.UnEdited);
                }

                await SPELL_CHECK.open();
                if (bRefresh !== false) {
                    this._host.handleRefresh();
                }
                return resolve(ResultType.Success);
            }

            if (SPELL_CHECK.isOpen() === false) {
                return resolve(ResultType.UnEdited);
            }

            SPELL_CHECK.close();
            return resolve(ResultType.Success);
        });
    }

    public setParaLineSpace(nType: number, nHeight: number): number {
        let result: number;
        switch (nType) {
            case 0:
                result = this._setMultiLineSpace(nHeight);
                break;
            case 3:
                result = this._setFixedLineSpace(nHeight);
                break;
            default:
                result = ResultType.ParamError;
        }
        if (result === ResultType.Success) {
            this._host.handleRefresh();
        }

        return result;
    }

    public isCurrentLineEmpty(): boolean {
        const section = this._documentCore.getDocumentSelection();
        if (!section) {
            return false;
        }
        if (section.bUse) {
            return false;
        }
        const para = this._documentCore.getCurrentParagraph();
        if (!para) {
            return false;
        }

        return para.isEmpty();
    }

    public setWestCharBreakAttribute(nType: number, bFlag: boolean): number {
        if ( typeof nType !== 'number' || !(1 === nType || 2 === nType) ) {
            return ResultType.ParamError;
        }

        if ( typeof bFlag !== 'boolean') {
            return ResultType.ParamError;
        }

        let result: number;
        result = this._documentCore.setWestCharBreakAttribute(nType, bFlag);
        if (result === ResultType.Success) {
            this._host.handleRefresh();
        }

        return result;
    }

    public insertPageBreak(): number {
        
        const result = this._documentCore.addPageBreak();
        
        if (result === ResultType.Success) {
            this._host.handleRefresh();
        }
        return result;
    }

    public setFileProperty(itemName: string, content: string): number {
        if (!itemName || typeof content !== 'string' || typeof itemName !== 'string') {
            return ResultType.ParamError;
        }

        if (!this.checkXmlElementNameValidity(itemName)) {
            return ResultType.ParamError;
        }

        itemName = filterChars2(itemName);
        if (!itemName) {
            return ResultType.ParamError;
        }

        return this._documentCore.setFileProperty(itemName, content);
    }

    public getFileProperty(itemName: string): string {
        if (!itemName) {
            return ResultType.StringEmpty;
        }

        return this._documentCore.getFileProperty(itemName);
    }

    private _setFixedLineSpace(nVal: number): number {
        if (nVal < 0) {
            return ResultType.ParamError;
        }
        return this._documentCore.setParagraphProperty1({paraSpacing: nVal / 1000, paraSpacingType: 6});
    }

    private _setMultiLineSpace(nVal: number): number {
        if (nVal < 0) {
            return ResultType.ParamError;
        }

        const ratio = nVal / 100;
        // ratio = (ratio - 1) / 2 / 0.15;
        // ratio = Math.round(ratio * 100) / 100;
        let type = 4;

        switch (ratio) {
            case 1: {
                type = 1;
                break;
            }

            case 2: {
                type = 3;
                break;
            }
        }

        return this._documentCore.setParagraphProperty1({paraSpacing: ratio, paraSpacingType: type});
    }

    /**
     * 0 表示左对齐 1 表示右对齐 2 表示两端对齐 3 表示居中对齐
     * @param propName
     * @param nVal
     */
    private _setAlignment(nVal: number): number {
        if (nVal < 0 || nVal > 3) {
            return ResultType.ParamError;
        }

        let type: number = 0;
        switch (nVal) {
            case 0:
                type = AlignType.Left;
                break;
            case 1:
                type = AlignType.Right;
                break;
            case 2:
                type = AlignType.Justify;
                break;
            case 3:
                type = AlignType.Center;
                break;
            default:
                return ResultType.ParamError;
        }

        return this._documentCore.setParagraphProperty1({alignment: type});
    }

    private checkXmlElementNameValidity(name: string): boolean {
        let validity = !/^[xX][mM][lL].*/.test(name); // condition 3
        validity = validity && /^[a-zA-Z_].*/.test(name);  // condition 2
        validity = validity && /^[a-zA-Z0-9_\-\.]+$/.test(name); // condition 4
        return validity;
    }

    private _setLeftIndent(nVal: number): number {
        if (nVal < 0) {
            return ResultType.ParamError;
        }

        return this._documentCore.setParagraphProperty1({paraLeftInd: nVal * 10});
    }

    private _setLineIndent( nVal: number, type: number): number {
        if (nVal < 0) {
            return ResultType.ParamError;
        }

        const value = nVal * 10;

        return this._documentCore.setParagraphProperty1({
            paraInd: (2 === type ? -value : value), paraLeftInd: (2 === type ? value : undefined), paraIndType: type});
    }

    private _setTextItalic(nVal: number): number {
        if (nVal !== 0 && nVal !== 1) {
            return ResultType.ParamError;
        }

        return this._documentCore.setTextItalic(nVal);
    }

    private  _setTextUnderline = (nVal?: number): number => {
        if (nVal !== 0 && nVal !== 1) {
            return ResultType.ParamError;
        }
        return this._documentCore.setTextUnderline(nVal);
    }

    private _setTextBold = (nVal: number): number => {
        if (nVal !== 0 && nVal !== 1) {
            return ResultType.ParamError;
        }
        return this._documentCore.setTextBold(nVal);
    }

    private _setTextVertAlign(nVal: number, type: number): number {
        if (nVal !== 0 && nVal !== 1) {
            return ResultType.ParamError;
        }
        let result: number;
        switch (type) {
            case 1:
                result = this._documentCore.setTextSubscript(nVal === 1 ? 1 : 0);
                break;
            case 2:
                result = this._documentCore.setTextSuperscript(nVal === 1 ? 2 : 0);
                break;
        }

        return result;
    }

    private _setTextBackground(sVal: string): number {
        if (!sVal) {
            return ResultType.ParamError;
        }

        return this._documentCore.setTextBackgrounColor(sVal);
    }

    private _setTextColor(sVal: string): number {
        if (!sVal) {
            return ResultType.ParamError;
        }

        return this._documentCore.setTextColor(sVal);
    }

    private _setTextFontFamily(sVal: string): number {
        if (!sVal) {
            return ResultType.ParamError;
        }

        return this._documentCore.setTextFontFamily(sVal);
    }

    private _setTextFontSize(nVal: number): number {
        if (nVal < 0 || typeof nVal !== 'number') {
            return ResultType.ParamError;
        }
        nVal = FONST_SIZE_PX[nVal] || FONST_SIZE_PX.parse(nVal);
        return this._documentCore.setTextFontSize(nVal);
    }

    private _setWordWrap(nVal: number): number {
        if ( typeof nVal !== 'number' || !(0 === nVal || 1 === nVal) ) {
            return ResultType.ParamError;
        }

        return this._documentCore.setParagraphProperty1({bWordWrap: (0 === nVal ? false : true)});
    }
}
