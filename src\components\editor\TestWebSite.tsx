import {Editor} from 'hz-editor-sdk-new';
(async () => {
    const optionInit = {
                id: 123123,
                dom: document.getElementById('hz-editor-app'),
                src: 'index.html?time=' + new Date().getTime(),
                option: {
                        bShowMenu: true,
                        bShowToolbar: true,
                        isTest: true,
                        bNeedCustomFont:false,
                },
        };
    const res = await new Editor().init(optionInit);
    const editor = await res.getEditor();
})();
