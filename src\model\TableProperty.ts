import { INISCellGridLine } from './../common/commonDefines';
import { DocumentColor } from './core/Style';
import { IDrawSelectionsCell, IDocumentContent } from './DocumentCore';

/**
 * 渲染显示表格内容
 */
export interface IDocumentTable {
    id: number;
    index: number;
    x: number;
    y: number;
    width: number;
    height: number;
    scale?: number;
    startRow?: number;
    lastRow?: number;
    content?: IDocumentContent;  // 表格内容
    // paragraphs: IDocumentParagraph[],  // 段落
    tableBorderLines: IDocumentTableBorderLine[];   // 表格边框线
    tableBackground?: IDocumentTableBackground[];   // 表格/单元格背景填充
    tableCells: IDrawSelectionsCell[];
    fixedLeft?: number;
    fixedRight?: number;
    bFixedHeader?: boolean;
    bNISTable?: boolean;
    yEnd?: number;
    bFixedTable?: boolean;
    cellGirdLines?: {[key: number]: INISCellGridLine}
}

/**
 * 表格/单元格背景填充
 */
export interface IDocumentTableBackground {
    width?: number;
    height?: number;
    x?: number;
    y?: number;
    fill: DocumentColor;
    fillOpacity?: number;
}

/**
 * 表格边框线
 */
export interface IDocumentTableBorderLine {
    x1: number;
    y1: number;
    x2: number;
    y2: number;
    stroke: string; // DocumentColor;
    strokeWidth: number;
    // strokeLinecap?: number,
    strokeDashArray?: string; // number[]; // 虚线
    bSlash?: boolean;
    bDiagonalLine?: boolean;
    bFixed?: boolean;
    fixedType?: number; // 1: 左固定列，2：右固定列
    bHeader?: boolean;
}
