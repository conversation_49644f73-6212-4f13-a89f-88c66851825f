import DocumentFrameBounds from './FrameBounds';
import {PageProperty} from './Style';
import DocumentContentElementBase from './DocumentContentElementBase';
import { DocumentPageSection } from './DocumentPageSection';
import { idCounter } from './util';

export interface ILimits {
    x: number;
    y: number;
    xLimit: number;
    yLimit: number;
}

export interface ITableLimits extends ILimits {
    maxTopBorder: number;
}

/**
 * 文档页空白边距
 */
class PageMargins {
    public left: number = 0;
    public right: number = 0;
    public top: number = 0;
    public bottom: number = 0;
    public header?: number;
    public footer?: number;

    constructor() {
        //
    }
}

/**
 * 文档页类
 */
export default class DocumentPage {
     public id: number; // 页面id从0开始

     public width: number = 0; // 页面宽度
     public height: number = 0;
     public margins: PageMargins = new PageMargins();

     public x: number = 0;
     public y: number = 0;
     public xLimit: number = 0; // 页面可编辑最大宽度位置X
     public yLimit: number = 0; // 页面可编辑最大宽度位置Y

     public bounds: DocumentFrameBounds = new DocumentFrameBounds(0, 0, 0, 0);

     public pos: number = 0;
     public endPos: number = -1;
     public bResetStartElement: boolean = false;
     public endSectionParas: DocumentContentElementBase[] = [];

     // 文档区块
     public sections: DocumentPageSection[] = [];

    constructor(pageProperty?: PageProperty) {
        this.id = idCounter.getNewId();

        // 计算获取页面的大小信息
        /*this.x = pageProperty.paddingLeft;
        this.y = pageProperty.paddingTop;

        this.width = pageProperty.width;
        this.height = pageProperty.height;
        this.xLimit = pageProperty.width - this.x - pageProperty.paddingRight;
        this.yLimit = pageProperty.height - this.y - pageProperty.paddingBottom;

        this.margins = new PageMargins();
        this.margins.left = pageProperty.paddingLeft;
        this.margins.top = pageProperty.paddingTop;
        this.margins.right = pageProperty.paddingRight;
        this.margins.bottom = pageProperty.paddingBottom;
        this.bounds = new DocumentFrameBounds(0, 0, 0, 0);*/
    }

    public updateLimits(limits: ILimits): void {
        this.x = limits.x;
        this.xLimit = limits.xLimit;
        this.y = limits.y;
        this.yLimit = limits.yLimit;
    }

    public checkEndSectionPara(element: DocumentContentElementBase): boolean {
        const count = this.endSectionParas.length;
        for ( let index = 0; index < count; index++) {
            if (element === this.endSectionParas[index]) {
                return true;
            }
        }
        return false;
    }

    public shift(shiftDx: number, shiftDy: number): void {
        //
    }

    public copy(): DocumentPage {
        const pageProperty = new DocumentPage();
        pageProperty.width = this.width;
        pageProperty.height = this.height;
        const margins = this.margins;
        pageProperty.margins = new PageMargins();
        pageProperty.margins.left = margins.left;
        pageProperty.margins.top = margins.top;
        pageProperty.margins.right = margins.right;
        pageProperty.margins.bottom = margins.bottom;

        return pageProperty;
    }
}
