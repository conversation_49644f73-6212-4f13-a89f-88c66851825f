import { TextProperty } from './StyleProperty';
import {ParaElementType} from './core/Paragraph/ParagraphContent';
import {ParaElementBase} from './core/Paragraph/ParaElementBase';
import { DocumentCore } from './DocumentCore';

export interface IDocumentParaPortion {
    id: number;
    content: ParaElementBase[];
    textProperty: TextProperty;
    type: ParaElementType;
    positionX: number;
    positionY: number;
    startPos: number;
    endPos: number;
    pageIndex?: number;
    documentCore?: DocumentCore;
    textHeight?: number;
    cellId?: any;
    option?: any;
    nHeaderFooter?: number;
    bRadioBoxRed?: boolean;
    bNewLine?: boolean;     // for macOS
}

export interface IDcoumentPortionBackgroundColor {
    x: number;
    y: number;
    width: number;
    height: number;
    backgroundColor: string;
}

export interface IDcoumentPortionTextDecoration {
    x: number;
    y: number;
    width: number;
    color: string;
    style?: number;
}

export interface ICommentData {
    lineIndex: number;
    x: number;
    y: number;
    xLimit: number;
    time: string;
    userName: string;
    text: string;
    name: string;
}
