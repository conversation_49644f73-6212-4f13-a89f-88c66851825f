import * as React from 'react';
import Dialog from '../../ui/Dialog';
import Button from '../../ui/Button';
import Checkbox from '../../ui/CheckboxItem';
// tslint:disable-next-line: max-line-length
import { CodeValueItem, CustomPropertyElementType, ICustomProps, INISProperty, NISSelectType } from '../../../../common/commonDefines';
import CustomPropertyBtn from './CustomProperty';
import { IFRAME_MANAGER } from '../../../../common/IframeManager';
import NewComboBoxList from './NewComboBoxList2';
import Input from '../../ui/Input';
import { TableCell } from '../../../../model/core/Table/TableCell';
import { DocumentCore } from '../../../../model/DocumentCore';
import '../../style/NewNISList.less';
import { NISTableCellCommon } from './NISTableCellCommonProps';

interface IDialogProps {
    documentCore: DocumentCore;
    visible?: boolean;
    id?: string;
    close: (name: string | number, bRefresh?: boolean) => void;
    refresh: (bRefresh: boolean, timeout?: number) => void;
}

interface IState {
    bRefresh: boolean;
}

export default class NISTableCellList extends React.Component<IDialogProps, IState> {
    private cellProps: any; // revised cell props
    // private tableProps: any;
    private visible: any;
    private bCustomProperty: boolean;
    private docId: number;
    private nisProps: any;

    constructor(props: any) {
        super(props);
        this.cellProps = {
            bProtected: false,
            bRetrieve: false,
            bCheckMultiple: false,
            // bCanFormulaCacl: false,
            // formularType: false,
            // customProperty: undefined,
            nisProperty: {},
            selectType: NISSelectType.Dropdown,
            codeLabel: undefined,
            valueLabel: undefined,
            visible: undefined,
        };
        this.state = {
            bRefresh: false,
        };
        this.nisProps = {};
        this.visible = this.props.visible;
        // this.setDialogValue();
        this.docId = this.props.documentCore.getCurrentId();
    }

    public componentDidMount(): void {
        // this.boxRef.current.ownerDocument.addEventListener('click', this.docClick);
    }

    public componentWillUnmount(): void {
        // this.boxRef.current.ownerDocument.removeEventListener('click', this.docClick);
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        this.visible = nextProps.visible;
    }

    public render(): any {
        const nisProps = this.cellProps.nisProperty;
        return (
            <Dialog
                id={this.props.id}
                visible={this.visible}
                width={380}
                height={350}
                // close={this.close}
                open={this.open}
                preventDefault={false}
                title='下拉单元格'
                confirm={this.confirm}
                footer={this.renderFooter()}
                // id='table'
            >
                <div>
                    <div className='editor-line'>
                        <div className='w-70'>内部名称</div>
                        <div className='right-auto'>
                            <Input
                                name='nis-serialNumber'
                                value={nisProps.serialNumber}
                                onChange={this.onChange}
                                readonly={true}
                                placeholder={''}
                                disabled={true}
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <span className='table-label'>类型</span>
                    </div>
                    <div className='editor-line nis-radio-container'>
                        {/* <Radiobox
                            name='nis-selectType'
                            value={NISSelectType.Dropdown}
                            onChange={this.onChange}
                            // index={NISSelectType.Dropdown}
                        >下拉框
                        </Radiobox>
                        <Radiobox
                            name='nis-selectType'
                            value={NISSelectType.Combo}
                            onChange={this.onChange}
                            // index={NISSelectType.Combo}
                        >组合框
                        </Radiobox> */}
                        <input
                            type='radio'
                            name='nis-selectType'
                            className='nis-radio'
                            id='nis-radio-dropdown'
                            value={NISSelectType.Dropdown}
                            onChange={this.onChangeRadio}
                            checked={nisProps.selectType === NISSelectType.Dropdown}
                        /><label htmlFor='nis-radio-dropdown'>下拉框</label>
                        <input
                            type='radio'
                            name='nis-selectType'
                            className='nis-radio'
                            id='nis-radio-combo'
                            value={NISSelectType.Combo}
                            onChange={this.onChangeRadio}
                            checked={nisProps.selectType === NISSelectType.Combo}
                        /><label htmlFor='nis-radio-combo'>组合框</label>
                    </div>
                    <div className='editor-line'>
                        <span className='table-label'>属性</span>
                    </div>
                    <div className='editor-line editor-multi-line'>
                        <Checkbox
                            name='nis-bCheckMultiple'
                            value={nisProps.bCheckMultiple}
                            // disabled={true}
                            onChange={this.onChange}
                        >
                            支持多选
                        </Checkbox>
                        <Checkbox
                            name='nis-bRetrieve'
                            value={nisProps.bRetrieve}
                            disabled={false}
                            onChange={this.onChange}
                        >
                            索引检索
                        </Checkbox>
                        <Checkbox
                            name='bProtected'
                            value={this.cellProps.bProtected}
                            disabled={false}
                            onChange={this.onChange}
                        >
                            禁止编辑
                        </Checkbox>
                        <Checkbox
                            name='bShowCodeAndValue'
                            value={nisProps.bShowCodeAndValue}
                            onChange={this.checkChange}
                        >
                            展现属性值
                        </Checkbox>
                        <Checkbox
                            name='visible'
                            value={this.cellProps.visible}
                            onChange={this.onChange}
                        >
                            网格线
                        </Checkbox>
                        <CustomPropertyBtn
                            name='nis-customProperty'
                            properties={nisProps.customProperty}
                            documentCore={this.props.documentCore}
                            onChange={this.onChange}
                            close={this.onClose}
                            id='bCustomProperty'
                            visible={this.bCustomProperty}
                            type={CustomPropertyElementType.TableCell}
                        />
                        <div className='editor-line'>
                            <span className='title'>下拉选项</span>
                        </div>
                        <NewComboBoxList
                            value={nisProps.items}
                            docId={this.docId}
                            onChange={this.onChange}
                            name='nis-items'
                            codeLabel={nisProps.codeLabel}
                            valueLabel={nisProps.valueLabel}
                        />
                    </div>
                    <NISTableCellCommon prop={this.nisProps}/>
                    <div className='editor-line'>
                        <span className='title'>扩展属性</span>
                    </div>
                    <div className='editor-line'>
                        <label className='table-label'>内容分隔符: </label>
                        <div className='w-050'>
                            <Input
                                value={nisProps.separator}
                                name='nis-separator'
                                onChange={this.onChange}
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <Checkbox
                            name='nis-bShowValue'
                            value={nisProps.bShowValue}
                            disabled={false}
                            onChange={this.onChange}
                        >
                            Vaule值显示
                        </Checkbox>
                    </div>
                </div>
            </Dialog>
        );
    }

    private renderFooter(): any {
        return (
            <span>
                <Button onClick={this.close}>取消</Button>
                <Button type='primary' onClick={this.confirm}>确认</Button>
            </span>
        );
    }

    private init(): void {
        const curCellProps = this.props.documentCore.getTableCellProps();
        // console.log(curCellProps)
        if (curCellProps != null) {
            // copy object
            // deep copy, but lose TS type; function is said cannot be copied
            // const curCellPropsCopy = JSON.parse(JSON.stringify(curCellProps));

            // TODO: normal props
            this.cellProps.bProtected = curCellProps.bProtected;
            const nisProps = this.nisProps = curCellProps.nisProperty;
            if (nisProps != null) {
                const {items, separator, bRetrieve, bShowValue, customProperty, bCheckMultiple, selectType,
                    bShowCodeAndValue, serialNumber, codeLabel, valueLabel, gridLine} = nisProps;
                const thisNisProps = this.cellProps.nisProperty;
                // copy items
                const itemsCopy = [];
                for (const item of items) {
                    const curItem = new CodeValueItem(item.code, item.value);
                    Object.assign(curItem, item);
                    itemsCopy.push(curItem);
                }
                this.cellProps.visible = gridLine?.visible;
                thisNisProps.items = itemsCopy;
                // thisNisProps.prefixContent = prefixContent;
                // thisNisProps.selectPrefixContent = selectPrefixContent;
                thisNisProps.separator = separator;
                thisNisProps.bRetrieve = bRetrieve;
                thisNisProps.bShowValue = bShowValue;
                const customPropertyCopy = [];
                for (const item of customProperty) {
                    const curItem: ICustomProps = {type: item.type, value: item.value, name: item.name};
                    Object.assign(curItem, item);
                    customPropertyCopy.push(curItem);
                }
                thisNisProps.customProperty = customPropertyCopy;
                thisNisProps.bCheckMultiple = bCheckMultiple;
                thisNisProps.selectType = selectType;
                thisNisProps.bShowCodeAndValue = bShowCodeAndValue;
                thisNisProps.serialNumber = serialNumber;
                thisNisProps.codeLabel = codeLabel;
                thisNisProps.valueLabel = valueLabel;
            }
        }
    }

    private open = (): void => {
        const { documentCore } = this.props;
        const curNisCell = true; // documentCore.getCurNISCell();
        // console.log(curNisCell)
        if (curNisCell != null) {
            // init props here first
            this.init();
        }
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private close = (bRefresh?: boolean): void => {
        this.props.close(this.props.id, bRefresh);
        this.visible = false;
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private onClose = (id: string, bRefresh: boolean): void => {
        this[id] = false;
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private checkChange = (value: any, name: string): void => {
        this.cellProps.nisProperty[name] = value;
    }

    private onChange = (value: any, name: string): void => {
        if (name.indexOf('nis-') !== -1) {
            const trueName = name.slice('nis-'.length);
            this.cellProps.nisProperty[trueName] = value;
        } else if ('codeLabel' === name || 'valueLabel' === name) {
            this.cellProps.nisProperty[name] = value;
        } else {
            this.cellProps[name] = value;
        }
    }

    private onChangeRadio = (e: any): void => {
        // console.log(e.target)
        const name = e.target.name;
        const value = e.target.value;
        if (name.indexOf('nis-') !== -1) {
            const trueName = name.slice('nis-'.length);
            this.cellProps.nisProperty[trueName] = +value;
            this.setState({});
        }
        // console.log(this.cellProps.nisProperty)
    }

    private confirm = (id?: any): void => {
        const { documentCore } = this.props;
        IFRAME_MANAGER.setDocId(this.docId);
        // console.log(this.cellProps.nisProperty);

        const needUpdateText = this.needUpdateListText(this.cellProps.nisProperty);
        const nisProperty = this.cellProps.nisProperty;
        nisProperty.gridLine = {visible: this.cellProps.visible};
        documentCore.setTableCellProps({bProtected: this.cellProps.bProtected, nisProperty});

        if (needUpdateText === true) {
            // update cell text
            documentCore.setNISCellListItems(null);
        }

        this.close(true);
    }

    private needUpdateListText(nisProperty: INISProperty): boolean {
        const { documentCore } = this.props;
        const curNisCell = documentCore.getCurNISCell();
        // console.log(curNisCell)
        let needUpdate = false;
        if (curNisCell != null && curNisCell.property != null) {
            const curNISProps = curNisCell.property.nisProperty;
            const {items, separator, bShowValue} = curNISProps;
            if (nisProperty != null) {
                const {items: curItems, separator: curSeparator, bShowValue: curBShowValue} = nisProperty;
                if ( curSeparator !== separator || curBShowValue !== bShowValue ||
                    JSON.stringify(curItems) !== JSON.stringify(items)) {
                    needUpdate = true;
                }
                // console.log(JSON.stringify(curItems) !== JSON.stringify(items))
                // console.log(curSeparator !== separator)
                // console.log(curBShowValue !== bShowValue)
            }
        }

        return needUpdate;
    }

}
