import * as React from 'react';
import Dialog from '../../ui/Dialog';
import Input from '../../ui/Input';
import Button from '../../ui/Button';
import Select from '../../ui/select/Select';
import { PageProperty } from '../../../../model/StyleProperty';
import { PageFormat, PAGE_FORMAT } from '../../../../common/commonDefines';
import { getPxForMM, getMMFromPx } from '../../../../model/core/util';
import { message } from '../../../../common/Message';
import { IFRAME_MANAGER } from '../../../../common/IframeManager';

interface IDialogProps {
    documentCore: any;
    visible: boolean;
    id: string;
    close: (name: string | number, bRefresh?: boolean) => void;
}

interface IState {
    bRefresh: boolean;
}

interface IType {
    key: string;
    value: string;
}

export default class PageSetting extends React.Component<IDialogProps, IState> {
    private visible: any;
    private types: IType[];
    private pagePro: PageProperty;
    private pageType: PageFormat;
    private oldPro: any;
    constructor(props: any) {
        super(props);
        this.state = {
            bRefresh: false,
        };
        this.visible = this.props.visible;
        const types = ['A3', 'A4', 'A5', 'B4', 'B5', 'B6', '自定义'];
        this.types = types.map((type) => {
            return {
                key: type,
                value: type,
            };
        });

        this.pagePro = {};
        this.oldPro = {};
    }

    public render(): any {
        return (
            <Dialog
                visible={this.visible}
                width={350}
                title='页面设置'
                footer={this.renderFooter()}
                id={this.props.id}
            >
                <div>
                    <div className='editor-line'>
                        <span className='title'>纸张大小（cm）</span>
                    </div>
                    <div className='editor-line'>
                        <div className='w-70'>类型</div>
                        <div className='right-auto'>
                            <Select
                                value={this.pageType}
                                data={this.types}
                                onChange={this.typeChange}
                                name='pageType'
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <div className='w-70'>宽度</div>
                        <div className='right-auto'>
                            <Input
                                name='width'
                                disabled={this.pageType !== PageFormat.Custom}
                                type='number'
                                value={this.pagePro.width}
                                onChange={this.onChange}
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <div className='w-70'>高度</div>
                        <div className='right-auto'>
                            <Input
                                name='height'
                                disabled={this.pageType !== PageFormat.Custom}
                                type='number'
                                value={this.pagePro.height}
                                onChange={this.onChange}
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <span className='title'>页边距 （cm）</span>
                    </div>
                    <div className='editor-line'>
                        <div className='w-70'>左：</div>
                        <div className='right-auto'>
                            <Input
                                name='paddingLeft'
                                type='number'
                                value={this.pagePro.paddingLeft}
                                onChange={this.onChange}
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <div className='w-70'>右：</div>
                        <div className='right-auto'>
                            <Input
                                name='paddingRight'
                                type='number'
                                value={this.pagePro.paddingRight}
                                onChange={this.onChange}
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <div className='w-70'>顶端：</div>
                        <div className='right-auto'>
                            <Input
                                name='paddingTop'
                                type='number'
                                value={this.pagePro.paddingTop}
                                onChange={this.onChange}
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <div className='w-70'>底端：</div>
                        <div className='right-auto'>
                            <Input
                                name='paddingBottom'
                                type='number'
                                value={this.pagePro.paddingBottom}
                                onChange={this.onChange}
                            />
                        </div>
                    </div>
                </div>
            </Dialog>
        );
    }

    public componentDidMount(): void {
        // this.pageType = PageFormat.A4;
        // const prop = PAGE_FORMAT.A4;
        // const pagePro = this.pagePro;
        // pagePro.width = prop[0];
        // pagePro.height = prop[1];
        // pagePro.paddingLeft = 3.17;
        // pagePro.paddingRight = 3.17;
        // pagePro.paddingTop = 2.54;
        // pagePro.paddingBottom = 2.54;
        this.setDialogValue();
        this.setState({bRefresh: !this.state.bRefresh});
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        this.setDialogValue();
        this.visible = nextProps.visible;
    }

    private renderFooter(): any {
        return (
            <span>
                <Button onClick={this.close}>取消</Button>
                <Button type='primary' onClick={this.confirm}>确认</Button>
            </span>
        );
    }

    private close = (bRefresh?: boolean): void => {
        this.props.close(this.props.id, bRefresh);
        this.visible = false;
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private confirm = (): void => {
        const documentCore = this.props.documentCore;
        const pagePro = this.pagePro;
        IFRAME_MANAGER.setDocId(documentCore.getCurrentId());
        if ( false === this.checkDialogValue() ) {
            // this.close(false);
            return ;
        }

        const page: PageProperty = {
            width: getPxForMM(pagePro.width * 10),
            height: getPxForMM(pagePro.height * 10),
            paddingLeft: getPxForMM(pagePro.paddingLeft * 10),
            paddingRight: getPxForMM(pagePro.paddingRight * 10),
            paddingTop: getPxForMM(pagePro.paddingTop * 10),
            paddingBottom: getPxForMM(pagePro.paddingBottom * 10),
        };
        const res = documentCore.setPageProperty(page);
        /* IFTRUE_WATER */
        // 刷新水印
        documentCore.resetCorePosition(true, false);
        /* FITRUE_WATER */
        this.close(res === 0);
    }

    private typeChange = (value: PageFormat): void => {
        this.pageType = value;
        const pagePro = this.pagePro;
        switch (value) {
            case PageFormat.A3: {
                const prop = PAGE_FORMAT.A3;
                pagePro.width = prop[0];
                pagePro.height = prop[1];
                break;
            }
            case PageFormat.A4: {
                const prop = PAGE_FORMAT.A4;
                pagePro.width = prop[0];
                pagePro.height = prop[1];
                break;
            }
            case PageFormat.A5: {
                const prop = PAGE_FORMAT.A5;
                pagePro.width = prop[0];
                pagePro.height = prop[1];
                break;
            }
            case PageFormat.B4: {
                const prop = PAGE_FORMAT.B4;
                pagePro.width = prop[0];
                pagePro.height = prop[1];
                break;
            }
            case PageFormat.B5: {
                const prop = PAGE_FORMAT.B5;
                pagePro.width = prop[0];
                pagePro.height = prop[1];
                break;
            }
            case PageFormat.B6: {
                const prop = PAGE_FORMAT.B6;
                pagePro.width = prop[0];
                pagePro.height = prop[1];
                break;
            }
            default:
        }

        this.setState({bRefresh: !this.state.bRefresh});
    }

    private onChange = (value: any, name: string): void => {
        this.pagePro[name] = value;
    }

    private setDialogValue(): void {
        const pageProperty = this.props.documentCore.getPageProperty();
        // this.pageType = pageProperty.pageFormat;

        const pagePro = this.pagePro;
        pagePro.width = parseFloat((getMMFromPx(pageProperty.width) / 10).toFixed(2));
        pagePro.height = parseFloat((getMMFromPx(pageProperty.height) / 10).toFixed(2));
        pagePro.paddingLeft = parseFloat((getMMFromPx(pageProperty.paddingLeft) / 10).toFixed(2));
        pagePro.paddingRight = parseFloat((getMMFromPx(pageProperty.paddingRight) / 10).toFixed(2));
        pagePro.paddingTop = parseFloat((getMMFromPx(pageProperty.paddingTop) / 10).toFixed(2));
        pagePro.paddingBottom = parseFloat((getMMFromPx(pageProperty.paddingBottom) / 10).toFixed(2));

        if ( PAGE_FORMAT.A3[0] === pagePro.width && PAGE_FORMAT.A3[1] === pagePro.height ) {
            this.pageType = PageFormat.A3;
        } else if ( PAGE_FORMAT.A4[0] === pagePro.width && PAGE_FORMAT.A4[1] === pagePro.height ) {
            this.pageType = PageFormat.A4;
        } else if ( PAGE_FORMAT.A5[0] === pagePro.width && PAGE_FORMAT.A5[1] === pagePro.height ) {
            this.pageType = PageFormat.A5;
        } else if ( PAGE_FORMAT.B4[0] === pagePro.width && PAGE_FORMAT.B4[1] === pagePro.height ) {
            this.pageType = PageFormat.B4;
        } else if ( PAGE_FORMAT.B5[0] === pagePro.width && PAGE_FORMAT.B5[1] === pagePro.height ) {
            this.pageType = PageFormat.B5;
        } else if ( PAGE_FORMAT.B6[0] === pagePro.width && PAGE_FORMAT.B6[1] === pagePro.height ) {
            this.pageType = PageFormat.B6;
        } else if ( PAGE_FORMAT.C1[0] === pagePro.width && PAGE_FORMAT.C1[1] === pagePro.height ) {
            this.pageType = PageFormat.C1;
        } else {
            this.pageType = PageFormat.Custom;
        }

        this.oldPro.width = pagePro.width;
        this.oldPro.height = pagePro.height;
        this.oldPro.paddingLeft = pagePro.paddingLeft;
        this.oldPro.paddingRight = pagePro.paddingRight;
        this.oldPro.paddingTop = pagePro.paddingTop;
        this.oldPro.paddingBottom = pagePro.paddingBottom;
        this.oldPro.pageType = this.pageType;
    }

    private checkDialogValue(): boolean {
        const pagePro = this.pagePro;
        if ( 0 >= pagePro.width || 0 >= pagePro.height ) {
            message.error('页面高度、宽度必须大于0');
            return false;
        }

        if ( 0.42 > pagePro.paddingLeft ) {
            message.error('左边距位于页面的可打印区域之外，请设置 >= 0.42厘米');
            return false;
        }

        if ( 0.43 > pagePro.paddingRight ) {
            message.error('右边距位于页面的可打印区域之外，请设置 >= 0.43厘米');
            return false;
        }

        if ( 0.42 > pagePro.paddingTop ) {
            message.error('上边距位于页面的可打印区域之外，请设置 >= 0.42厘米');
            return false;
        }

        if ( 0.44 > pagePro.paddingBottom ) {
            message.error('下边距位于页面的可打印区域之外，请设置 >= 0.44厘米');
            return false;
        }

        if ( pagePro.width <= pagePro.paddingLeft + pagePro.paddingRight ) {
            message.error('页面宽度必须大于左边距和右边距之和');
            return false;
        }

        if ( pagePro.height <= pagePro.paddingTop + pagePro.paddingBottom ) {
            message.error('页面高度必须大于上边距和下边距之和');
            return false;
        }

        if ( 0.01 > Math.abs(this.oldPro.width - pagePro.width) &&
             0.01 > Math.abs(this.oldPro.height - pagePro.height) &&
             0.01 > Math.abs(this.oldPro.paddingLeft - pagePro.paddingLeft) &&
             0.01 > Math.abs(this.oldPro.paddingTop - pagePro.paddingTop) &&
             0.01 > Math.abs(this.oldPro.paddingRight - pagePro.paddingRight) &&
             0.01 > Math.abs(this.oldPro.paddingBottom - pagePro.paddingBottom) ) {
            return false;
        }

        return true;
    }

}
