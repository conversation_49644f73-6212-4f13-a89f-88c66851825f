import * as React from 'react';
import {Document} from './module/document/Document';
import ReactDOM from 'react-dom/client';
import PageContainer from './module/PageContainer';
import { DocumentCore } from '../../model/DocumentCore';
import ExternalEvent from '../../common/external/Event';
import IExternalInterface, { IExternalEvent } from '../../common/external/IExternalInterface';
import IAsyncExternalInterface from '../../common/external/IAsyncExternalInterface';
import { ExternalInterface } from '../../common/external';
import { logger } from '../../common/log/Logger';

import {
    GlobalEvent as gEvent,
    GlobalEventName as gEventName,
} from '../../common/GlobalEvent';
import { IFRAME_MANAGER } from '../../common/IframeManager';
import { getPxForMM, idCounter } from '../../model/core/util';
import './style/message.less';
import './style/warpper.less';
import { SDKcontainer } from './module/SDKcontainer';
import { CleanModeType, EditorHeader, ICopyProperty, RIGHT_MENU_CUSTOM_ITEM, ResultType, TEXTCOLOR_CHANGEINREVISION } from '../../common/commonDefines';
import { ExportHtml } from './module/ExportHtml';
import { ToolbarAction } from '../../common/menu/ToolbarAction2';
import { WasmInstance } from '../../common/WasmInstance';
import PrintDialog from './module/modals/PrintDialog';
import { getTheme } from '@hz-editor/theme';
import { consoleLog, isGlobalTestData } from '@/common/GlobalTest';
import { SPELL_CHECK } from '@/common/Spellcheck';
import { clearAllWavyUnderlinesFromDocument } from '@/model/core/WavyUnderline/WavyUnderlineUtils';
// import {EquationRefs} from '../../common/MedEquation';
// import { EquationType } from '../../common/commonDefines';

const writeClose = (): void => {
    // tslint:disable-next-line: no-console
    console.log('编辑器已经关闭');
};

interface IProps {
    id?: string;
    isTest?: boolean;
    bShowMenu?: boolean;
    bNeedCustomFont?: boolean;
    bShowToolbar?: boolean;
    win?: Window;
    height?: number;
    iframe?: any;
    bEnableScroll?: boolean;
    bScrollX?: boolean;
    bScrollY?: boolean;
    bSpellCheck?: boolean;
    close?: any;
    visible?: boolean; // 打印模式，不渲染前端UI
    _bCascadeManager?: boolean;
    textColorChangeInRevision?: number;
    toolbarAlignment?: number;
    contextMenu?: {id: string, text: string}[];
    disablePrintAction?: boolean; // 新增：禁用预览打印操作 
}
interface IInlineRectInfo {
    cursorX: number;
    cursorY: number;
    isInline: boolean;
    height: number;
    width: number;
}

const cod1 = 'ed';
const cod2 = 'ito';
const cod3 = 'rCa';
const cod4 = 'llb';
const cod5 = 'ack';
const cod6 = 'te';
const cod7 = 'xt';
interface IEmrState {
    bReflesh: boolean;
    documentCore: DocumentCore;
}
let resizeDom;
let editorIndex = 0;
export class EmrEditor extends React.Component<IProps, IEmrState> {
    public externalEvent: ExternalEvent;
    public id: string;
    public docId: number;
    public myRef: any;
    public bPrint: boolean;
    public bNeedCustomFont:boolean;
    public contentRef: any;
    public containerRef: any;
    public clientWidth: number;
    public copyOption: ICopyProperty;
    private documentCore: DocumentCore;
    private _external: IExternalInterface;
    private _asyncExternal: IAsyncExternalInterface;
    private height: number;
    private visible: boolean;
    private _document: any;
    private _height: number;
    //ofd pdf 服务器地址使用
    public odfSvrUrl:string;
    public pdfSvrUrl:string;
    // Inline模式使用
    private _inlineEvents: any[] = [];
    private _inlineOldRectInfo: { width: number, height: number }; // 记忆inline模式下的高度
    // 事件监听器
    private _fileEnabled: boolean;
    private _structEnabled: boolean;
    private _keyEnabled: boolean;
    private _unResizeCursor: boolean;
    private _bFirstVisible: boolean;
    private _runTest: any;
    private _bFocus: boolean;
    private _bFromInterfaces: boolean;
    private _rightMenus: any;
    private bHideMenu: boolean;
    private bHideToolbar: boolean;

    private menuAction: ToolbarAction;
    private bResetMenuDatas: boolean; // 是否重置所有菜单的勾选项（打开新文档）
    private _url: string;
    private _bCascadeManager: boolean;
    private toolbarAlign: number;

    constructor(props: IProps) {
        super(props);
        let bText = props.isTest;
        let bHideMenu = !props.bShowMenu;
        let bHideToolbar = !props.bShowToolbar;
        let height: number = EditorHeader.Total;

        TEXTCOLOR_CHANGEINREVISION.value = [1, 2].includes(props.textColorChangeInRevision) ?
                                        props.textColorChangeInRevision : 1;
        this.bNeedCustomFont = props.bNeedCustomFont;
        this.pdfSvrUrl = 'http://localhost:7075';
        this.odfSvrUrl = 'http://localhost:9090';
        this.id = props.id || 'editor-id';
        const docId = idCounter.getNewId();
        this._bCascadeManager = props._bCascadeManager;
        if (this._bCascadeManager === true) {
            this.bPrint = true;
        }
        if (props.isTest === undefined || props.isTest === false) {
            bText = false;
        }
        IFRAME_MANAGER.addIframe(docId, props.iframe);
        if (false) {
            if (props.iframe.offsetParent) {
                this._bFirstVisible = true;
            }

            if (props.bShowMenu !== true) {
                bHideMenu = true;
                height -= EditorHeader.Menu;
            }
            if (props.bShowToolbar !== true) {
                bHideToolbar = true;
                height -= EditorHeader.Toolbar;
            }
            // this._document = props.win.document;
            this._height = props.height || props.win.document.firstElementChild.clientHeight || 700;
            this.clientWidth = props.win.document.firstElementChild.clientWidth || 1000;
            // console.dir(props.win.document)
            // this.height = this._height - height;
            // console.dir(this._document)
            // console.log(this._document.firstElementChild.clientHeight)
            // console.log(props.win.document.firstElementChild.clientHeight,
            // props.win.document.firstElementChild.clientWidth)
        } else {
            this.toolbarAlign = null;
            // 服务端使用
            window['__EmrEditorComponent__'] = this;
            this._height = props.height || undefined;
            // this._document = document;
            // this._height = document.body.
            // this.height = document.firstElementChild.clientHeight - height;
            // this.clientWidth = document.body.clientWidth;
            if (props.bShowMenu !== true) {
                bHideMenu = true;
                height -= EditorHeader.Menu;
            }
            if (props.bShowToolbar !== true) {
                bHideToolbar = true;
                height -= EditorHeader.Toolbar;
            }
        }
        const win = window;
        this.toolbarAlign = props.toolbarAlignment;
        this.height = height;
        this.documentCore = new DocumentCore(undefined, bText, docId);
        this.state = {
            bReflesh: false,
            documentCore: this.documentCore,
        };
        this.bHideMenu = bHideMenu;
        this.bHideToolbar = bHideToolbar;
        this.docId = docId;
        this.myRef = React.createRef();
        this.contentRef = React.createRef();
        this.containerRef = React.createRef();
        logger.setId(this.id, this.docId);
        this._fileEnabled = true;
        this._structEnabled = true;
        this._keyEnabled = true;
        this.copyOption = {};
        if (props.contextMenu && props.contextMenu.length) {
            RIGHT_MENU_CUSTOM_ITEM.menus = props.contextMenu;
        }

        if (props.bSpellCheck === true) {
            SPELL_CHECK.open();
        }
        this._inlineOldRectInfo = {
            height: 0,
            width: 0,
        };
        // const text = win[cod1 + cod2 + cod3 + cod4 + cod5][cod6 + cod7];
        // console.log(text, window['editorCallback']['_text']);
        this.documentCore.setMarkText(win[cod1 + cod2 + cod3 + cod4 + cod5][cod6 + cod7]);
        win[cod1 + cod2 + cod3 + cod4 + cod5][cod6 + cod7] = null;
        // console.dir(new EquationRefs().getEquationByType(EquationType.LightPositioningMap));
    }

    public render(): any {
        if (this.visible === false || this.props.visible === false) {
            return null;
        }
        let className = 'hz-editor-container';
        const props = this.props;
        if (props.id !== undefined) {
            className += ' hz-editor-from-iframe';
        }
        if (props.bEnableScroll === false) {
            className += ' scroll-not';
        }
        if (props.bScrollX === false) {
            className += ' scroll-not-x';
        }
        if (props.bScrollY === false) {
            className += ' scroll-not-y';
        }
        const toolbars = [null, 2];
        if (toolbars.includes(this.toolbarAlign)) {
            className += ' toolbar-center';
        }
        if (props._bCascadeManager) {
            className += ' cascade-manager';
        }
        // const {pageProperty} = this.documentCore.render();
        // const width = this.id === 'editor-id' ? 'auto' : pageProperty.width;
        return (
            <div className={className} ref={this.myRef}>
                <PageContainer
                    host={this as any}
                    bHideToolbar={this.bHideToolbar}
                    bHideMenu={this.bHideMenu}
                    ref={this.containerRef}
                    isResetMenuDatas={this.isResetMenuDatas}
                />
                <Document
                    host={this}
                    ref={this.contentRef}
                    height={this._height}
                    headerHeight={this.height}
                    unResizeCursor={this._unResizeCursor}
                />
            </div>
        );
    }

    public showSectionBorder(): boolean {
        return getTheme()?.NewControl.ShowSectionBorder !== false;
    }

    public clearDatas(): void {
        this.contentRef.current?.clearDatas();
    }

    public async exportToHtml(cleanMode: CleanModeType, bWater?: boolean): Promise<string> {
        const document = new DocumentCore(undefined, false);

        const documentCore = this.documentCore;
        // const bTrackRevisions = documentCore.isFinalRevision();
        // if ( !bTrackRevisions ) {
        //     documentCore.changeRevisionState2(false);
            
        // }
        documentCore.changeRevisionState2(true);
        const content = documentCore.getDocContent(cleanMode);
        document.addDocContent(content, cleanMode, undefined);
        
        // if ( !bTrackRevisions ) {
        //     documentCore.changeRevisionState2(true);
        // }
        documentCore.removeSelection();
        const htmlVm = new ExportHtml(this, bWater);
        const html = await htmlVm.exportAsString(undefined, document);
        return html;
    }

    public openPrintDialog(options: any): void {
        const printDialog =  new PrintDialog(null, this);
        const document = new DocumentCore(undefined, false);
        printDialog.setDoc(document);
        // return document;

        const documentCore = this.documentCore; // host, core

        const bTrackRevisions = documentCore.isTrackRevisions();
        if ( bTrackRevisions ) {
            documentCore.setTrackRevisions(false);
        }
        const content = documentCore.getDocContent();
        const pageSize = this.getPageSize(options);
        let newOptions;
        if (pageSize) {
            newOptions = {pageSize};
            options.pageSize = pageSize;
        }
        // console.log(content);
        document.addDocContent(content, undefined, newOptions);
        
        // 清理打印文档中的波浪线
        clearAllWavyUnderlinesFromDocument(document.getDocument());
        
        document.changeRevisionState2(documentCore.isFinalRevision());

        // this.printDialog.addSideEffectsForPrintInstance(document, documentCore);
        document.recalculateAllForce();

        // this.printDialog.setContent(content);
        documentCore.removeSelection();
        printDialog.onClose(() => {
            printDialog.clearContent();
            printDialog.clearDom();
            // documentCore.recalculateAllForce();
            /// this.refresh(true);
        });
        printDialog.openCDialog(options);
    }

    public exportStructsNameValue(): string {
        const obj = this.documentCore.getNewControlsNameValue();
        return JSON.stringify(obj);
    }

    public isPrintModel(): boolean {
        return this.props.visible === false;
    }

    public componentDidMount(): void {
        if (process.env.NODE_ENV === 'development') {
            window['utilEditor'] = this;
        }

        let index: string = '';
        if (editorIndex > 0) {
            index = this.docId.toString();
        }
        window['editor' + index] = this.getEditor();
        if (this.props.visible === false) {
            return;
        }

        this.menuAction = new ToolbarAction(this);
        // this.addResizeDom();
        this.addWindowErrorEvent();
        editorIndex++;
        // window['that'] = this;
        gEvent.addEvent(this.docId, gEventName.MoveCursor, this.handleContentChange);
        gEvent.addEvent(this.docId, gEventName.Blur, this.blur);
        gEvent.addEvent(this.docId, gEventName.RightMenuVisible, this.setRightMenuVisible);
        
        // const water = new TestWaterText(this);
        // this['_water'] = water;
    }

    public componentWillUnmount(): void {
        if (this.props.visible === false) {
            return;
        }
        // resizeDom.removeEventListener('resize', this.onResize);
        IFRAME_MANAGER.deleteIframe(this.docId);
        gEvent.deleteEvent(this.docId, gEventName.MoveCursor, this.handleContentChange);
        gEvent.deleteEvent(this.docId, gEventName.Blur, this.blur);
        gEvent.deleteEvent(this.docId, gEventName.RightMenuVisible, this.setRightMenuVisible);
        if (this.props.id) {
            SDKcontainer.removeEditor(this.docId);
        }
        this.removeWindowErrorEvent();
        gEvent.setEvent(this.docId, gEventName.UnMounted);
    }

    public setCopyOptions(options: ICopyProperty): void {
        if (!options) {
            return;
        }

        const keys = Object.keys(options);
        const current = this.copyOption;
        let bChanged = false;
        keys.forEach((key) => {
            if (options[key] !== undefined && options[key] !== current[key]) {
                current[key] = options[key];
                bChanged = true;
            }
       });
    }

    public componentDidUpdate(): void {
        this.bResetMenuDatas = false;
    }

    /**
     * 重置Menu菜单的所有勾选项，恢复为默认的选项
     */
    public resetMenuDatas(): void {
        this.bResetMenuDatas = true;
    }

    public isResetMenuDatas = (): boolean => {
        return this.bResetMenuDatas;
    }

    public onFocus(flag: boolean): boolean {
        if (this._bFromInterfaces) {
            this._bFromInterfaces = false;
            return false;
        }

        if (!this.contentRef.current) {
            return false;
        }
        // if (flag === this._bFocus) {
        //     return true;
        // }
        // this._bFocus = flag;

        if (!flag) {
            this.contentRef.current.resetTextArea();
        }

        return this.contentRef.current.setCursorTrueVisible(flag);
    }

    public addTabindex(): void {
        this.contentRef.current.addTabindex();
    }

    public openCompleted(): void {
        gEvent.setEvent(this.docId, gEventName.OpenCompleted);
        if (this.externalEvent) {
            this.externalEvent.nsoFileOpenCompleted(null);
        }
    }

    public setStopCursorBlur(flag: boolean = true): void {
        this._bFromInterfaces = flag;
    }

    public setCursorVisible( bVisible: boolean): void {
        this.contentRef.current.setCursorVisible(bVisible);
    }

    public visibleChange(bVisible: boolean): void {
        const bFirstVisible = this._bFirstVisible;
        gEvent.setEvent(this.docId, gEventName.VisibleChange, bVisible, bFirstVisible === undefined);
        if (bFirstVisible === undefined && bVisible) {
            this._bFirstVisible = true;
        }
    }

    public mainRefresh(bResetHeight?: boolean): void {
        // 当前元素不存在时，不进行刷新否则容易出现异常
        if (!this.myRef.current) {
            return;
        }

        if (bResetHeight === true) {
            let doc = document;
            if (this.props && this.props.win) {
                doc = this.props.win.document;
            }
            this._height = doc.firstElementChild.clientHeight || 700;
        }
        this.setState({}, () => {
            const current = this.contentRef.current;
            current.clearContainer();

            current.refresh()
            .then(() => {
                current.clearTipFlag();
            });
        });
    }

    public handleRefresh(timeout?: number): void {
        // 当前元素不存在时，不进行刷新否则容易出现异常
        if (!this.myRef.current) {
            return;
        }
        if (this.contentRef.current.dynamicModeRefresh() === true) {
            return ;
        }

        if (!this.documentCore.isLockRefresh()) {
            return;
        }

        if (timeout !== undefined) {
            setTimeout(() => {
                this.contentRef.current.refresh();
            }, timeout);
        } else {
            if (this.contentRef.current != null) {

                this.contentRef.current.refresh()
                .then(() => {
                    this.contentRef.current.clearTipFlag();
                });
            } else {
                this.mainRefresh();
            }
        }

        // console.log(this.state.documentCore.getDocument());
    }

    public handleContainerRefresh(): void {
        // 当前元素不存在时，不进行刷新否则容易出现异常
        if (!this.myRef.current) {
            return;
        }
        this.containerRef.current.refresh();
    }

    public initCursorPos(): void {
        if (this.contentRef.current != null) {
            this.contentRef.current.initCursorPos();
        }
    }

    public hideNewControlLayer(): void {
        gEvent.setEvent(this.docId, gEventName.NewControlToShow);
        gEvent.setEvent(this.docId, gEventName.NewNISCellToShow);
    }

    public async convertToHTMLFromBuffer(buffer: ArrayBuffer, clearMode?: CleanModeType,
                                         needWaterMark?: number, bConnection?: boolean): Promise<string> {
        const input = document.createElement('input');
        this.documentCore.createNewDocument(true);
        this.clearDatas();
        await this.menuAction.readFromBuffer2(buffer, input);
        if (clearMode === CleanModeType.CleanStruct) {
            return this.exportToHtml(CleanModeType.CleanStruct, needWaterMark === 0);
        } else {
            await new Promise((resolve) => {
                this.setState({bReflesh: !this.state.bReflesh}, () => {
                    resolve(null);
                });
            });
        }
        const html = new ExportHtml(this, needWaterMark === 0);
        // update all cascades before export html
        if (bConnection !== false) {
            this.documentCore.updateAllConnections();
        }

        // legacy compat
        let cleanMode: any = clearMode;
        if (typeof clearMode === 'boolean') {
            cleanMode = (clearMode === true) ? CleanModeType.CleanMode : CleanModeType.Normal;
        }
        return await html.exportAsString(cleanMode);
    }

    public async exportHtml(): Promise< string > {
        const html = new ExportHtml(this);
        return await html.exportAsString(CleanModeType.CleanMode);
    }

    public async updateNISTableByJson(json: any): Promise<string> {
        const editor = this.getEditor();
        const res = editor.updateNISTableByJson(json);

        if (ResultType.Success !== res) {
            return res.toString();
        }

        const blob = await editor.saveToStream(undefined);
        return this.blobToBase64(blob);
    }

    public setCascadeModeFlag(flag: boolean): void {
        this._bCascadeManager = flag;
        this.contentRef.current._bCascadeManage = flag;
        this.contentRef.current.refresh();
    }

    public setLogsCallback(fn: any): void {
        logger.setUploadFunc(fn);
    }

    public getEditor(): IExternalInterface {
        if (this._external === undefined) {
            const external = new ExternalInterface(this);
            this._external = external.getEditor();

            // 后期会删除
            this._external['_addEditorEvent'] = () => {
                this.createExternalEvent();
            };

            // 后期会删除
            this._external['_runAutoTestScript'] = () => {
                this.createAutoTestEvent();
            };

            this._external['_getExtend'] = () => {
                const obj: any = this._external['_extend'] = {};
                obj.getPageLines = (pageIndex) => {
                    return this.documentCore.getPageLines(pageIndex);
                };
                return this._external;
            };
        }

        return this._external;
    }

    public getAsyncEditor(): IAsyncExternalInterface {
        if (this._asyncExternal === undefined) {
            const external = this.getEditor();
            const that = this;
            const _asyncExternal = new Proxy(external, {
                get: function (target, prop, receiver) {
                    if (target[prop] instanceof Function) {
                        return async function (...args) {
                            return await target[prop].apply(target, args);
                        };
                    } else {
                        const result = that.getEditorMethod(prop, target);
                        if (result && result.data instanceof Function) {
                            return async function (...args) {
                                return await result.data.apply(result.parent, args);
                            };
                        } else {
                            return Promise.resolve(target[prop]);
                        }
                    }
                }
            });
            Object.assign(this, {_asyncExternal});
        }
        return this._asyncExternal;
    }
    public setInlineEvent(func: any): void {
        if (typeof func === 'function' ) {
            this._inlineEvents.push(func);
        }
    }

    public inlineModeChange(flag: boolean): void {
        this.traiggerInlineEvent({isInline: flag});
    }

    public inlinePageProp(width: number, height: number): void {
        if (typeof width !== 'number' || typeof height !== 'number' || width <= 0 || height <= 0) {
            return;
        }
        if (this.documentCore.setInlinePageProp(width, height)) {
            this.handleRefresh();
        }
    }

    public setEditEvent(options: IExternalEvent): void {
        if (!options) {
            return;
        }
        if (this.externalEvent) {
            this.externalEvent.setEvents(options);
            return;
        }
        this.externalEvent = new ExternalEvent(this, options);
        if (this.contentRef.current) {
            this.contentRef.current.externalEvent = this.externalEvent;
        }
    }

    public removeEvent(sNames?: string[]): boolean {
        if (this.externalEvent) {
            return this.externalEvent.removeEvent(sNames);
        }
        return false;
    }

    public resetCursor(): void {
        this.contentRef.current.resetCursor();
    }

    // public clearDom(): void {
    //     const editor = this._external;
    //     const keys = Object.keys(editor);
    //     // 后期会删除
    //     delete this._external['_runAutoTestScript'];

    //     delete this._external['_getExtend'];
    //     delete this._external['_addEditorEvent'];
    //     delete this.createAutoTestEvent;
    //     keys.forEach((key) => {
    //         editor[key] = writeClose;
    //     });

    //     this._external = null;
    //     this._asyncExternal = null;
    //     this.externalEvent = null;
    //     delete window['__EmrEditorComponent__inWindow'];
    //     delete window['editor' + this.docId];
    //     delete window['utilEditor'];
    //     delete window['editor'];
    //     this.documentCore = null;
    //     this.menuAction = null;
    //     if (this.myRef.current && this.myRef.current.parentNode) {
    //         ReactDOM.unmountComponentAtNode(this.myRef.current.parentNode);
    //     }

    //     if (isGlobalTestData()) {
    //         consoleLog(gEvent.getAllEvents());
    //     } else {
    //         gEvent.deleteAllEventByName(this.docId, gEventName.UnMounted);
    //     }

    //     const iframe = this.props.iframe;
    //     if (iframe && iframe.parentNode) {
    //         iframe.outerHTML = '';
    //     }

    //     if (typeof this.props.close === 'function') {
    //         this.props.close();
    //     }
    // }

    public clearDom(): void {
        const editor = this._external;
        const keys = Object.keys(editor);
        // 后期会删除
        delete this._external['_runAutoTestScript'];
        delete this._external['_getExtend'];
        delete this._external['_addEditorEvent'];
        delete this.createAutoTestEvent;
        keys.forEach((key) => {
            editor[key] = writeClose;
        });
    
        this._external = null;
        this._asyncExternal = null;
        this.externalEvent = null;
        delete window['__EmrEditorComponent__'];
        delete window['editor' + this.docId];
        delete window['utilEditor'];
        delete window['editor'];
        delete window['__EMR_EDITOR_SET_THEME__'];
        this.documentCore = null;
        this.menuAction = null;
    
        // 卸载 React 组件
        const container = this.myRef.current?.parentNode as HTMLElement;
        if (container) {
            // 确保 container 是有效的 DOM 元素
            const root = ReactDOM.createRoot(container);
            root.unmount(); // 卸载 React 组件
        }
    
        if (isGlobalTestData()) {
            consoleLog(gEvent.getAllEvents());
        } else {
            gEvent.deleteAllEventByName(this.docId, gEventName.UnMounted);
        }
    
        const iframe = this.props.iframe;
        if (iframe && iframe.parentNode) {
            iframe.outerHTML = '';
        }
    
        if (typeof this.props.close === 'function') {
            this.props.close();
        }
    }

    public setSelections(): void {
        this.contentRef.current.setSelections();
    }

    public setScrollbarToPage(): void {
        //
    }

    public getRightMenus(): any {
        return this._rightMenus;
    }

    public testDocumentXml(timeout?: number, bInsertFile: boolean = false): void {
        // 当前元素不存在时，不进行刷新否则容易出现异常
        if (!this.myRef.current) {
            return;
        }
        // 文档打开，完全刷新水印
        /* IFTRUE_WATER */
        this.documentCore.resetCorePosition(true, false);
        /* FITRUE_WATER */
        if (timeout !== undefined) {
            setTimeout(() => {
                this.contentRef.current.testDocumentXml(bInsertFile);
            }, timeout);
        } else {
            if (this.contentRef.current != null) {
                this.contentRef.current.testDocumentXml(bInsertFile);
            } else {
                this.mainRefresh();
            }
        }
        // console.log(this.state.documentCore.getDocument());
    }

    public getDocumentCore(): DocumentCore {
        return this.documentCore;
    }

    public updateCursor(bComposition: boolean = false, bForceUpdate: boolean = false,
                        bResetCursor: boolean = false): boolean {
        // through ref, u can use anything from child component
        return this.contentRef.current.updateCursor(bComposition, bForceUpdate, bResetCursor);
    }

    public isSelected(): boolean {
        return this.contentRef.current.isSelected();
    }

    public getCopyPaste(): any {
        if (!this.contentRef.current) {
            return;
        }
        return this.contentRef.current.getCopyPaste();
    }

    public showToolbar(bVisible: boolean, name?: string): boolean {
        bVisible = !bVisible;
        if (bVisible === this.bHideToolbar) {
            return false;
        }
        this.bHideToolbar = bVisible;
        this.setHeight(this.bHideMenu, bVisible);
        this.mainRefresh();
        return true;
    }

    public showMenu(bVisible: boolean): boolean {
        bVisible = !bVisible;
        if (bVisible === this.bHideMenu) {
            return false;
        }
        this.bHideMenu = bVisible;
        this.setHeight(bVisible, this.bHideToolbar);
        this.mainRefresh();
        return true;
    }

    public isFileEnabled(): boolean {
        return this._fileEnabled;
    }

    public isKeyEnabled(): boolean {
        return this._keyEnabled;
    }

    public isStructEnabled(): boolean {
        return this._structEnabled;
    }

    public setFileEnabled(enabled: boolean): void {
        this._fileEnabled = enabled;
    }

    public setKeyEnabled(enabled: boolean): void {
        this._keyEnabled = enabled;
    }

    public setVisible(flag: boolean, iframe: HTMLIFrameElement): void {
        // if (iframe && flag === true) {
        //     IFRAME_WINDOW[this.docId] = iframe.contentWindow;
        // }
        if (flag === this.visible) {
            return;
        }
        this.visible = flag;
        if (flag === false) {
            gEvent.setEvent(this.docId, gEventName.UnMounted);
            if (this.props.id) {
                SDKcontainer.removeEditor(this.docId);
            }
        } else {
            this._unResizeCursor = true;
        }
        this.setState({}, () => {
            if (flag === true) {
                this.handleRefresh();
                if (this.externalEvent && this.contentRef.current) {
                    this.contentRef.current.externalEvent = this.externalEvent;
                }

                this._unResizeCursor = false;
                if (this.props.id) {
                    SDKcontainer.init(this.docId, this);
                }
            }
        });
    }

    public setStructEnabled(enabled: boolean): void {
        this._structEnabled = enabled;
    }

    public removeAllListen(): void {
        // this._fileEnabled = false;
        // this._structEnabled = false;
        // this._keyEnabled = false;
    }

    public async createAutoTestEvent(): Promise<any> {
        if (this._runTest) {
            return this._runTest;
        }
        const module = await import('../../common/AutoRunTest');
        const runTest = module.default;
        const test = new runTest(this);
        this._runTest = test;
        test.init(this._external);
        return this._runTest;
    }

    private setRightMenuVisible = (options: any): void => {
        let rightMenu = this._rightMenus;
        if (!rightMenu) {
            rightMenu = this._rightMenus = options;
            return;
        }

        if (!options.readonlyMode) {
            const readonlyMode = this._rightMenus.readonlyMode;
            this._rightMenus = options;
            if (readonlyMode) {
                this._rightMenus = readonlyMode;
            }
            return;
        }
        this._rightMenus = options;
    }

    private createExternalEvent(): void {
        const names: any = {};
        const keys = Object.keys(ExternalEvent.prototype);
        keys.forEach((key) => {
            names[key] = (...args) => {
                // tslint:disable-next-line: no-console
                console.log(key, ...args);
            };
        });

        this.setEditEvent(names);
    }

    private getPageSize(options: any): any {
        if (!options || !options.pageSize || typeof options.pageSize !== 'string') {
            return;
        }

        const pages = options.pageSize.split(',');
        const pageCols = ['width', 'height', 'top', 'bottom', 'left', 'right'];
        const obj = {};
        pages.forEach((item, index) => {
            const num = parseFloat(item);
            if (!item || isNaN(num) || isNaN(item) || num < 0) {
                obj[pageCols[index]] = undefined;
                return;
            }
            obj[pageCols[index]] = getPxForMM(num * 10);
        });
        return obj;
    }

    private setRemoteUrl(url: string): void {
        if (!url) {
            return;
        }

        if (this._url && this._url === url) {
            return;
        }

        this._url = url;
        WasmInstance.instance.ccall('SetRemoteUrl', 'null', ['string'], [url]);
    }

    private createAutoTest = () => {
        this.createAutoTestEvent();
    }

    private blur = (e: any): void => {
        this.onFocus(false);
    }

    private getEditorMethod(name: any, editor: any): any {
        if (!name) {
            return null;
        }

        const names = name.split('.');
        if (names.length === 0) {
            return null;
        }

        let parent: any;
        let data = editor;
        for (let index = 0, len = names.length; index < len; index++) {
            const prop = names[index];
            if (data[prop] === undefined) {
                return null;
            }
            parent = data;
            data = data[prop];
        }

        if (!data) {
            return null;
        }

        return {data, parent};
    }

    private setHeight(bHideMenu: boolean, bHideToolbar: boolean): void {
        let height: number = EditorHeader.Total;
        if (bHideMenu === true) {
            height -= EditorHeader.Menu;
        }

        if (bHideToolbar === true) {
            height -= EditorHeader.Toolbar;
        }
        this.height = height;
        this.handleContentChange();
    }

    private addResizeDom(): any {
        if (!resizeDom) {
            const dom = document.createElement('object');
            dom.setAttribute('style', `display: block; position: absolute; top: 0; left: 0; height: 100%;
                width: 100%; overflow: hidden;opacity: 0; pointer-events: none; z-index: -1;`);
            dom.type = 'text/html';
            dom.data = 'about:blank';
            document.body.appendChild(dom);
            if (!dom.contentDocument) {
                return;
            }
            resizeDom = dom.contentDocument.defaultView;
        }

        this.addResizeEvent();
    }

    private addResizeEvent(): void {
        resizeDom.addEventListener('resize', this.onResize);
    }

    private onResize = (e: any) => {
        gEvent.setEvent(this.docId, gEventName.Resize, e);
    }

    private addWindowErrorEvent = () => {
        if (!this.myRef.current) {
            return;
        }
        this.myRef.current.ownerDocument.defaultView.addEventListener('error', this.windowError, true);
    }

    private removeWindowErrorEvent = () => {
        const myRef = this.myRef.current;
        if (myRef && myRef.ownerDocument && myRef.ownerDocument.defaultView) {
            myRef.ownerDocument.defaultView.removeEventListener('error', this.windowError);
        }
    }

    private windowError = (err: any): void => {
        logger.error({id: this.docId, description: `message: ${err.error?.message};stack: ${err.error?.stack};`});
    }

    private handleContentChange = () => {
        if (!this.documentCore || !this.documentCore.isInlineMode() || !this.myRef.current) { // 非内联模式下不触发事件，也不计算高度
            this._inlineOldRectInfo = {
                height: 0,
                width: 0,
            };
            return;
        }
        // 修改：使用 requestAnimationFrame 确保在下一帧渲染前执行
        // 这样可以保证排版已完成
        requestAnimationFrame(() => {
            const pageProp = this.documentCore.render().pageProperty;
            // 修改：明确获取文档实际高度
            const docHeight = this.documentCore.isInlineMode() ? 
                (pageProp.paddingTop + pageProp.paddingBottom) : 0;
            const curHeight = pageProp.height + docHeight;
            const oldRect = this._inlineOldRectInfo;
            if (Math.abs(curHeight - oldRect.height) > 1 || Math.abs(pageProp.width - oldRect.width) > 1) {
                const cursorPos = this.documentCore.getCursorPosition();
                this.traiggerInlineEvent({
                    cursorX: cursorPos.x,
                    cursorY: cursorPos.y2,
                    height: curHeight,
                    isInline: true,
                    width: pageProp.width,
                });
                this._inlineOldRectInfo = {
                    height: Math.ceil(curHeight),
                    width: Math.ceil(pageProp.width),
                };
            }
        });
    }
    private traiggerInlineEvent = (rectInfo: Partial<IInlineRectInfo>): void => {
        // 调用内部注册的事件处理函数
        this._inlineEvents.forEach(func => func(rectInfo));
        
        // 触发外部事件
        if (this.externalEvent && rectInfo.height !== undefined && rectInfo.width !== undefined) {
            // 确保传递所有必需的属性
            this.externalEvent.inlineHeightChange({
                height: rectInfo.height,
                width: rectInfo.width,
                cursorX: rectInfo.cursorX,
                cursorY: rectInfo.cursorY,
                isInline: rectInfo.isInline
            });
        }
    }

    private blobToBase64(blob: Blob): Promise<string> {
        return new Promise(resolve => {
            const fileReader = new FileReader()
            fileReader.readAsDataURL(blob);
            fileReader.onload = () => {
                resolve(fileReader.result as string);
            }
        });
    }
}
