
import { STD_START_DEFAULT, STD_TYPE_DEFAULT, IParseXmlNode as INode, FILE_HEADER_VERSION2 } from '../commonDefines';
import { decodeTag } from '../copy/DataConver';
import Document from '../../model/core/Document';
import { IUniqueImageProps, Reader } from '../../format/reader/reader';
import DocumentContentElementBase from '../../model/core/DocumentContentElementBase';
import { ParagraphReader } from '../../format/reader/paragraph';
import { RegionReader } from '../../format/reader/region';
import { TableReader } from '../../format/reader/table';

export class ParseContent {
    private document: Document;
    private contentControlDescObj: any;
    private uniqueImagelist: Map<string, IUniqueImageProps>;
    private sectionEnds: string[];

    constructor(document: Document, mediaXML: string) {
        this.document = document;
        this.uniqueImagelist = new Map();
        this.contentControlDescObj = {
            newControlProperty: {
                newControlName: null,
                newControlInfo: '', // title?
                newControlSerialNumber: STD_START_DEFAULT.serialNumber,
                newControlPlaceHolder: STD_START_DEFAULT.placeholder,
                newControlType: STD_TYPE_DEFAULT,
                isNewControlHidden: false,
                // isNewControlCanntDelete: false,
                isNewControlCanntDelete: STD_START_DEFAULT.deleteProtect === 1 ? true : false,
                // isNewControlCanntEdit: false,
                isNewControlCanntEdit: STD_START_DEFAULT.editProtect === 1 ? true : false,
                isNewControlCanntCopy: STD_START_DEFAULT.copyProtect === 1 ? true : false,
                // isNewControlMustInput: false,
                isNewControlMustInput: STD_START_DEFAULT.isMustFill === 1 ? true : false,
                // isNewControlShowBorder: true,
                isNewControlShowBorder: STD_START_DEFAULT.showBorder === 1 ? true : false,
                // isNewControlReverseEdit: false,
                isNewControlReverseEdit: STD_START_DEFAULT.editReverse === 1 ? true : false,
                // isNewControlHiddenBackground: false,
                isNewControlHiddenBackground: STD_START_DEFAULT.backgroundColorHidden === 1 ? true : false,
                newControlDisplayType: STD_START_DEFAULT.secretType,
                newControlFixedLength: undefined,
                newControlMaxLength: STD_START_DEFAULT.maxLength,
                customProperty: null,
                tabJump: STD_START_DEFAULT.tabJump === 1 ? true : false,

                newControlItems: null, // todo
                prefixContent: STD_START_DEFAULT.prefixContent,
                selectPrefixContent: STD_START_DEFAULT.selectPrefixContent,
                separator: STD_START_DEFAULT.separator,
                minValue: STD_START_DEFAULT.minValue,
                maxValue: STD_START_DEFAULT.maxValue,
                precision: STD_START_DEFAULT.precision,
                unit: STD_START_DEFAULT.unit,
                forceValidate: STD_START_DEFAULT.forceValidate,
                retrieve: STD_START_DEFAULT.retrieve === 1 ? true : false,
                isShowValue: STD_START_DEFAULT.showValue === 1 ? true : false,
                dateBoxFormat: STD_START_DEFAULT.dateBoxFormat,
                startDate: STD_START_DEFAULT.startDate,
                endDate: STD_START_DEFAULT.endDate,
                // datetime?
                customFormat: STD_START_DEFAULT.customFormat,

                // checkbox/radiobutton
                showRight: STD_START_DEFAULT.showRight === 1 ? true : false,
                checked: STD_START_DEFAULT.checked === 1 ? true : false,
                printSelected: STD_START_DEFAULT.printSelected === 1 ? true : false,
                label: STD_START_DEFAULT.label,
                showType: STD_START_DEFAULT.showType,
                spaceNum: STD_START_DEFAULT.spaceNum,
                labelCode: STD_START_DEFAULT.labelCode,

                supportMultLines: STD_START_DEFAULT.supportMultLines === 1 ? true : false,

                // signature box
                signatureCount: STD_START_DEFAULT.signatureCount,
                preText: STD_START_DEFAULT.preText,
                signatureSeparator: STD_START_DEFAULT.signatureSeparator,
                postText: STD_START_DEFAULT.postText,
                signaturePlaceholder: STD_START_DEFAULT.signaturePlaceholder,
                signatureRatio: STD_START_DEFAULT.signatureRatio,
                rowHeightRestriction: STD_START_DEFAULT.rowHeightRestriction === 1 ? true : false,

                signType: STD_START_DEFAULT.signType,
                alwaysShow: STD_START_DEFAULT.alwaysShow,
                showSignBorder: STD_START_DEFAULT.showSignBorder === 1 ? true : false,

                // text
                hideHasTitle: STD_START_DEFAULT.hideHasTitle === 1 ? true : false,
            },
            newControlManager: document.getNewControlManager(),
            // lastNewControl: null, // pay attention to null. no ref passed. seems no way to init
            unclosedNewControls: [], // stack
            headerStructCount: 0, // header struct count
            strcuts: {
                start: {},
                end: {}
            }
        };
        const reader = new Reader();
        reader.readMedia(mediaXML, this.uniqueImagelist);
    }

    public parse(nodes: INode[][]): DocumentContentElementBase[][] {
        this.sectionEnds = [];
        const contents: DocumentContentElementBase[][] = [];
        nodes.forEach((items: INode[], index: number) => {
            const arrs = contents[index] = [];
            if (!items) {
                return;
            }

            this.findSectionEndNames(items);
            items.forEach((item: INode, itemIndex: number) => {
                this.create(items, item, itemIndex, arrs);
            });
        });
        // console.log(contents, this.contentControlDescObj);
        return contents;
    }

    private create(items: any[], item: any, itemIndex: number, arrs: any[]): void {
        switch (item.tagName) {
            case 'w:p': {

                if (FILE_HEADER_VERSION2 === this.document.getDocumentVersion()) {
                    ParagraphReader.tTraverseParagraphForSec(item, this.document, this.contentControlDescObj,
                        this.uniqueImagelist, true, arrs, null, null, null);
                } else {
                    ParagraphReader.tTraverseParagraph2(item, this.document, this.contentControlDescObj,
                        this.uniqueImagelist, true, arrs, null, null, null, this.sectionEnds);
                }

                break;
            }
            case 'rg': {
                RegionReader.tTraverseRegion2(item, this.document, this.contentControlDescObj,
                    this.uniqueImagelist, true, arrs, null, 1, null, null, this.sectionEnds);
                break;
            }
            case 'w:tbl': {
                TableReader.tTraverseTable2(item, this.document, this.contentControlDescObj,
                    this.uniqueImagelist, true, arrs, null, null, null, this.sectionEnds);
                break;
            }
        }
    }

    private findSectionEndNames(items: INode[]): void {
        let sectionEnds = [];
        items.forEach((item) => {
            switch (item.tagName) {
                case 'w:p':
                case 'rg':
                case 'w:tbl': {
                    const sections = this.getElementsByTagName(item, 'sectionEnd');
                    if (sections.length > 0) {
                        sectionEnds = sectionEnds.concat(sections);
                    }
                    break;
                }
            }
        });
        if (sectionEnds && sectionEnds.length > 0) {
            const sections = this.sectionEnds;
            for (const sectionEnd of sectionEnds) {
                const attrs = sectionEnd.attributes;
                const name = attrs['name'];
                if (name != null) {
                    sections.push(name);
                }
            }
        }
    }

    private getElementsByTagName(node: INode, tagName: string): INode[] {
        const children = node.children;
        let arrs: INode[] = [];
        for (let index = 0, len = children.length; index < len; index++) {
            const child = children[index];
            if (typeof child === 'string') {
                break;
            }
            if (child.tagName === tagName) {
                // res = child;
                arrs.push(child);
            }
            const res = this.getElementsByTagName(child, tagName);
            if (res && res.length > 0) {
                arrs = arrs.concat(res);
            }
        }

        return arrs;
    }
}

const regAttr: RegExp = /([\w\-:.]+)(?:(?:\s*=\s*(?:(?:"([^"]*)")|(?:'([^']*)')|([^\s>]+)))|(?=\s|$))/g;

export class ParseXml {
    private rootIndex: number;
    private oldXml: string;
    private xml: string;
    private node: INode;
    private result: any[];
    private customNodes: {index: number, node: INode}[];
    private customRegionNodes: {index: number, node: INode}[];
    private customReg: any[];
    private actParentNode: INode;
    private tagNames: string[];
    private nodeNames: string[];
    private maxValue: number;
    private bShowTitle: boolean;
    // tslint:disable-next-line: max-line-length
    private regHtml: RegExp = /<(?:(?:\/([^>]+)>)|(?:!--([\S|\s]*?)-->)|(?:([^\s\/<>]+)\s*((?:(?:"[^"]*")|(?:'[^']*')|[^"'<>])*)\/?>))/g;
    // private regAttr: RegExp = /([\w\-:.]+)(?:(?:\s*=\s*(?:(?:"([^"]*)")|(?:'([^']*)')|([^\s>]+)))|(?=\s|$))/g;
    constructor(xmlDoc: string, bShowTitle?: boolean) {
        this.bShowTitle = bShowTitle;
        this.parse(xmlDoc);
        // TODO:
    }

    public getAllNodesByTags(tagNames: string[]): any[][] {
        const resultNodes = [];
        // 实现遍历逻辑直接收集所有目标标签的节点
        this.traverseXml(this.node, tagNames, resultNodes);
        return resultNodes;
    }

    private traverseXml(node: any, tags: string[], result: any[]) {
        if (tags.includes(node.tagName)) {
            result.push(node);
        }
        if (node.children) {
            node.children.forEach(child => this.traverseXml(child, tags, result));
        }
    }

    public filterNodesByCustomProps(tagNames: string[] | string, json: any[]): INode[][] {
        const customNodes = this.customNodes;
        if (customNodes.length === 0 && this.customRegionNodes.length === 0) {
            if (!json.find((item) => item.name)) {
                return;
            }
        }
        // console.log(this.customNodes);
        // const node = this.node;
        // if (typeof tagNames === 'object') {
        //     this.findElement(node, tagNames);
        // } else {
        //     this.findElement2(node, tagNames);
        // }
        // const childs = node.children[0].children[0].children;
        let lastIndex: number = 0;
        let names: string[];
        if (typeof tagNames === 'string') {
            names = [tagNames];
        } else {
            names = tagNames;
        }
        this.tagNames = names;
        this.oldXml = this.xml;

        // 分开存储name匹配和customProp匹配
        const customJson: any[] = [];
        const customJsonIndex: number[] = [];
        const nameJson: any[] = [];
        const nameJsonIndex: number[] = [];
        json.forEach((obj, index) => {
            if (obj['name']) {
                nameJson.push(obj);
                nameJsonIndex.push(index);
            } else {
                customJson.push(obj);
                customJsonIndex.push(index);
            }
        });
        if (customJson.length) {
            // match node by customProperty
            for (let index = 0, len = customNodes.length; index < len; index++) {
                let curNode = customNodes[index].node.parentNode;
                if (curNode.tagName === 'rgPr') {
                    curNode = curNode.parentNode;
                }
                if (!names.includes(curNode.tagName)) {
                    continue;
                }
                this.xml = this.xml.slice(curNode.startPos - lastIndex);
                lastIndex = curNode.startPos;
                this.actParentNode = curNode;
                const innerHTML = curNode.innerHTML = this.getInnerHTML(curNode);
                this.getCustomReg(customJson);
                this.getNodesByCustomProp(innerHTML, curNode);
                if (this.customReg.length === 0) {
                    break;
                }
            }
        }
        const nameRegionResult = [];
        if (nameJson.length) {
            // 处理region node
            for (let index = 0, len = this.customRegionNodes.length; index < len; index++) {
                const curNode = this.customRegionNodes[index].node;
                if (!names.includes(curNode.tagName)) {
                    continue;
                }
                this.xml = this.xml.slice(curNode.startPos - lastIndex);
                lastIndex = curNode.startPos;
                this.actParentNode = curNode;
                curNode.innerHTML = this.getInnerHTML(curNode);
                this.getAttribute(curNode);
                if (!nameJson.find(obj => obj.name === curNode.attributes.name)) {
                    continue;
                }

                let text = curNode.innerHTML;
                let pos = this.actParentNode.startPos;
                if (!curNode.innerHTML) { // 顶级元素不需要切割字符串
                    const curPos = curNode.startPos;
                    text = text.substr(curPos - pos);
                    pos = curPos;
                    // text = text.slice(start);
                    curNode.innerHTML = this.getInnerHTML(curNode, text);
                }
                this.setEachNodeInnerHTML(curNode, text, pos);
                this.setTitleContentVisible(curNode);
                const fullyNode = this.setAllSectionContent(curNode);
                nameJson.forEach(obj => {
                    if (obj['name'] === curNode.attributes.name) {
                        nameRegionResult.push(fullyNode);
                    }
                });
            }
        }

        // for (let index = 0, len = childs.length; index < len; index++) {
        //     const result: INode[]  = [];
        //     this.getElementsByTagName(childs[index], names, result);
        //     if (!result.length) {
        //         continue;
        //     }

        //     for (let curIndex = 0, curLen = result.length; curIndex < curLen; curIndex++) {
        //         const curNode = result[curIndex];
        //         this.xml = this.xml.slice(curNode.startPos - lastIndex);
        //         lastIndex = curNode.startPos;
        //         this.actParentNode = curNode;
        //         const innerHTML = curNode.innerHTML = this.getInnerHTML(curNode);
        //         this.getCustomReg(json);
        //         this.getNodesByCustomProp(innerHTML, curNode);
        //         if (this.customReg.length === 0) {
        //             break;
        //         }
        //     }

        //     if (this.customReg.length === 0) {
        //         break;
        //     }
        // }
        // add result by index
        const allResult = [];
        for (let i = 0; i < json.length; i++) {
            allResult.push(null);
        }
        if (this.result) {
            const customResult = this.result.map((res) => {
                return this.setAllSectionContent(res.res);
            });
            customJsonIndex.forEach((target, index) => {
                allResult[target] = customResult[index];
            });
        }
        // add region node result match by name
        if (nameRegionResult.length) {
            nameJsonIndex.forEach((target, index) => {
                allResult[target] = nameRegionResult[index];
            });
        }

        return allResult;
    }

    private setAllSectionContent(node: INode): INode[] {
        if (!node) {
            return;
        }

        let result: INode[] = [];
        if (node.tagName === 'section') {
            const p = {
                tagName: 'w:p',
                children: this.getElementByTagName(node, 'w:secontent').children,
                attributes: {},
            };

            result.push(p);
        } else if (node.tagName === 'sectionStart') {
            const name: string = this.getAttribute(node).name;
            const parent = node.parentNode;
            // let children = parent.children;
            this.xml = this.oldXml;
            const firstIndex = parent.children.findIndex((item) => item === node) + 1;
            // let p: INode = {
            //     tagName: 'w:p',
            //     attributes: undefined,
            //     children: [],
            // };
            // this.xml = this.oldXml;
            // let lastIndex: number = 0;
            // let curNode: INode = children[index++];
            // let bEnd = this.getSectionNode(children, index, name, p, innerHTML);
            // if (bEnd) {
            //     return [p];
            // }
            // result.push(p);
            let lastIndex: number = 0;
            const children = parent.parentNode.children;
            const parentIndex = children.findIndex((item) => item === parent);
            for (let index = parentIndex, len = children.length; index < len; index++) {
                const child = children[index];
                const curNode = {...child};
                curNode.children = [];
                curNode.attributes = this.getAttribute(curNode);
                this.xml = this.xml.slice(child.startPos - lastIndex);
                lastIndex = child.startPos;
                this.actParentNode = child;
                const innerHTML = curNode.innerHTML = this.getInnerHTML(curNode);
                result.push(curNode);
                let childIndex: number;
                if (parentIndex === index) {
                    childIndex = firstIndex;
                } else {
                    childIndex = 0;
                }
                const bEnd = this.getSectionNode(child.children, childIndex, name, curNode, innerHTML);
                if (bEnd) {
                    break;
                }
            }
        } else if (node.tagName === 'rg') {
            result = node.children;
        } else {
            const p = {
                tagName: 'w:p',
                children: this.getElementByTagName(node, 'w:sdtcontent').children,
                attributes: {},
            };

            result.push(p);
        }

        return result;
    }

    private getSectionNode(children: INode[], index: number, name: string, p: INode, innerHTML: string): boolean {
        let lastIndex: number = p.startPos;
        let bChanged = false;
        for (const len = children.length; index < len; index++) {
            const child = children[index];
            if (child.tagName === 'sectionEnd') {
                this.getAttribute(child);
                if (child.attributes.name === name) {
                    bChanged = true;
                    break;
                }
            }
            innerHTML = innerHTML.slice(child.startPos - lastIndex);
            lastIndex = child.startPos;
            child.attributes = this.getAttribute(child);
            const text = child.innerHTML = this.getInnerHTML(child, innerHTML);
            this.setEachNodeInnerHTML(child, text, lastIndex);
            p.children.push(child);
        }
        return bChanged;
    }

    private setEachNodeInnerHTML(node: INode, text: string, pos: number): void {
        const children = node.children;
        if (children.length === 0) {
            let str: any = node.innerHTML;
            if (str && node.tagName === 'w:t') {
                str = decodeTag(str);
            }
            node.children = [str];
            return;
        }

        for (let index = 0, len = children.length; index < len; index++) {
            const child = children[index];
            if (typeof child === 'string') {
                break;
            }
            if (child.innerHTML) {
                continue;
            }

            this.getAttribute(child);
            text = text.substr(child.startPos - pos);
            pos = child.startPos;
            const innerHTML = child.innerHTML = this.getInnerHTML(child, text);
            this.setEachNodeInnerHTML(child, innerHTML, pos);
        }

    }

    private getNodesByCustomProp(innerHTML: string, node: INode): void {
        const customTextReg = /<(customProperty)>([\s\S]+?)<\/\1>/g;
        const matches = customTextReg.exec(innerHTML);
        const customRegs = this.customReg;
        let pos: number = this.actParentNode.startPos;
        let text: string = innerHTML;
        if (matches) {
            const customContent = matches[2];
            for (let index = customRegs.length - 1; index >= 0; index--) {
                const customObj = customRegs[index];
                const customReg = customObj.reg;
                let customMatch: RegExpExecArray;
                let maxLen: number = 0;
                // tslint:disable-next-line: no-conditional-assignment
                while (customMatch = customReg.exec(customContent)) {
                    maxLen++;
                }

                if (customObj.len === maxLen) {
                    // const node = this.getNodeByLastIndex(text.slice(0, matches.index));
                    // if (node) {
                    //     this.getAttribute(node);
                    //     if (!node.innerHTML) { // 顶级元素不需要切割字符串
                    //         const curPos = node.startPos;
                    //         text = text.substr(curPos - pos);
                    //         pos = curPos;
                    //         // text = text.slice(start);
                    //         node.innerHTML = this.getInnerHTML(node, text);
                    //     }
                    //     this.setEachNodeInnerHTML(node, text, pos);
                    // }
                    this.getAttribute(node);
                    if (!node.innerHTML) { // 顶级元素不需要切割字符串
                        const curPos = node.startPos;
                        text = text.substr(curPos - pos);
                        pos = curPos;
                        // text = text.slice(start);
                        node.innerHTML = this.getInnerHTML(node, text);
                    }
                    this.setEachNodeInnerHTML(node, text, pos);
                    this.setTitleContentVisible(node);
                    customObj.type = node.attributes.type;
                    customObj.res = node;
                    customRegs.splice(index, 1);
                    break;
                }
            }

            // if (customRegs.length === 0) {
            //     break;
            // }
            // matches = customTextReg.exec(innerHTML);
        }
        return;
    }

    private setTitleContentVisible(node: INode): void {
        // if (this.bShowTitle !== false) {
        //     return;
        // }
        let title: INode;
        let tNode: INode;
        if (node.tagName === 'sectionStart') {
            title = this.getElementByTagName(node, 'title');
            if (!title) {
                return;
            }
            const parent = node.parentNode;
            const index = parent.children.findIndex((item) => item === node);
            if (index === -1) {
                return;
            }
            tNode = this.getElementByTagName(parent.children[index + 1], 'w:t');
            if (tNode) {
                tNode.children = ['' as any];
                return;
            }
        } else if (node.tagName === 'rg') {
            title = this.getElementByTagName(node.children[0], 'title');
            if (!title) {
                return;
            }
            tNode = this.getElementByTagName(node.children[1], 'w:t');
        } else {
            if (!node.attributes || node.attributes.type !== '1') {
                return;
            }
            title = this.getElementByTagName(node, 'title');
            if (!title) {
                return;
            }

            tNode = this.getElementByTagName(node, 'w:t');
        }

        if (tNode && title.innerHTML === tNode.innerHTML) {
            // if (node.tagName === 'rg') {
            //     const wts = this.getElementsByTagName(node.children[1], 'w:t');
            //     if (wts.length === 1 && node.children.filter((child) => child.tagName === 'w:p').length !== 1) {
            //         node.children.splice(1, 1);
            //         return;
            //     }
            // }
            tNode.children = ['' as any];
        }
    }

    // private getNodeByLastIndex(text: string): INode {
    //     this.getNames();
    //     const maxLen = this.maxValue;
    //     const names = this.nodeNames;
    //     const nameReg = /name="([^"]+?)"/;
    //     for (let index = text.length; index >= maxLen; index--) {
    //         const start = index - maxLen;
    //         const name = text.substr(start, maxLen);
    //         const activeName = names.find((item) => name.indexOf(item) === 0);
    //         if (activeName) {
    //             const matches = nameReg.exec(text.slice(start));
    //             if (!matches) {
    //                 return;
    //             }
    //             return this.getNodeByName(matches[1], this.actParentNode);
    //         }
    //     }

    //     return this.actParentNode;
    // }

    // private getNodeByName(name: string, node: INode): INode {
    //     const children = node.children;
    //     const tagNames = this.tagNames;
    //     let res;
    //     for (let index = 0, len = children.length; index < len; index++) {
    //         const child = children[index];
    //         if (tagNames.includes(child.tagName)) {
    //             const attrs = this.getAttribute(child);
    //             if (attrs.name === name) {
    //                 return child;
    //             }
    //             res = this.getNodeByName(name, child);
    //             if (res) {
    //                 return res;
    //             }
    //         }
    //     }
    //     return res;
    // }

    private getAttribute(node: INode): any {
        if (node.attributes) {
            return node.attributes;
        }
        const htmlattr: string = node.attrs;
        if (htmlattr) {
            const attr = {};
            let match = regAttr.exec(htmlattr);
            while (match) {
                attr[match[1]] = match[2] || match[3] || match[4];
                match = regAttr.exec(htmlattr);
            }
            node.attributes = attr;
            return attr;
        }
        return node.attributes = {};
    }

    // private getNames(): void {
    //     if (this.nodeNames) {
    //         return;
    //     }
    //     let maxLen: number = 0;
    //     const names = this.tagNames.map((tagName) => {
    //         const name = `<${tagName} `;
    //         const len = name.length;
    //         if (len > maxLen) {
    //             maxLen = len;
    //         }
    //         return name;
    //     });
    //     this.maxValue = maxLen;
    //     this.nodeNames = names;
    // }

    private getCustomReg(json: any[]): any[] {
        if (this.customReg) {
            return this.customReg;
        }
        const matchRegs: any = [];
        json.forEach((obj) => {
            let regStr = '';
            let len = 0;
            // tslint:disable-next-line: forin
            for (const key in obj) {
                len++;
                if (regStr !== '') {
                    regStr += '|';
                }
                regStr += `(<${key}\\s+[^>]+?>(${obj[key]})<\\/${key}>)`;
            }
            matchRegs.push({reg: new RegExp(regStr, 'g'), len, res: null});
        });
        this.result = matchRegs.slice();

        return this.customReg = matchRegs;
    }

    private getElementByTagName(node: INode, tagName: string): INode {
        const children = node.children;
        // let res: INode;
        for (let index = 0, len = children.length; index < len; index++) {
            const child = children[index];
            if (typeof child === 'string') {
                break;
            }
            if (child.tagName === tagName) {
                // res = child;
                return child;
            }
            const res = this.getElementByTagName(child, tagName);
            if (res) {
                return res;
            }
        }
    }

    private getElementsByTagName(node: INode, tagName: string): INode[] {
        const children = node.children;
        let arrs: INode[] = [];
        for (let index = 0, len = children.length; index < len; index++) {
            const child = children[index];
            if (typeof child === 'string') {
                break;
            }
            if (child.tagName === tagName) {
                // res = child;
                arrs.push(child);
            }
            const res = this.getElementsByTagName(child, tagName);
            if (res && res.length > 0) {
                arrs = arrs.concat(res);
            }
        }

        return arrs;
    }

    // private getElementsByTagName(node: INode, tagNames: string[], result: INode[]): void {
    //     const children = node.children;
    //     // let res: INode;
    //     for (let index = 0, len = children.length; index < len; index++) {
    //         const child = children[index];
    //         if (tagNames.includes(child.tagName)) {
    //             // res = child;
    //             result.push(child);
    //         }
    //         this.getElementsByTagName(child, tagNames, result);
    //     }
    // }

    private parse(xmlDoc: string): void {
        this.xml = xmlDoc;
        // console.time('测试时间1')
        this.node = this.createNode(xmlDoc);
        // console.timeEnd('测试时间1');
    }

    private getInnerHTML(node: INode, xml?: string): string {
        return (xml || this.xml).substr(0, node.endPos - node.startPos);
    }

    // private filterCustomProps(node: INode): boolean {

    // }

    private findElement(node: INode, tagNames: string[]): void {
        const children = node.children;
        children.forEach((child) => {
            if (tagNames.includes(child.tagName)) {
                this.result.push(child);
            }
            this.findElement(child, tagNames);
        });
    }

    private findElement2(node: INode, tagName: string): void {
        const children = node.children;
        children.forEach((child) => {
            if (child.tagName === tagName) {
                this.result.push(child);
            }
            this.findElement2(child, tagName);
        });
    }

    // private getNodeByIndex(node: INode, tagNames: string[], index: number): INode {
        //
    // }

    // private forEachNodeByIndex(node: INode, tagNames: string[], pos: number): INode {
    //     const childs = node.children;
    //     for (let index: number = 0, len = childs.length; index < len; index++) {

    //     }
    // }

    private crateElement(parent: INode, tagName: string, htmlattr: string): INode {
        const elm: INode = {} as any;
        elm.parentNode = parent;
        elm.tagName = tagName;
        elm.type = 'element';
        elm.children = [];
        elm.attrs = htmlattr;
        // 如果属性存在，处理属性
        if (tagName === 'customProperty') {
            this.customNodes.push({index: 0, node: elm});
        } else if (tagName === 'rg') { // 存储区域节点
            this.customRegionNodes.push({index: 0, node: elm});
        }
        parent.children.push(elm);
        // 如果是自闭合节点返回父亲节点
        return  elm;
    }

    private createNode(html: string): INode {
        // this.rootIndex = 0;
        this.customNodes = [];
        this.customRegionNodes = [];
        const parent: INode = this.node = {} as any;
        parent.tagName = 'root';
        parent.children = [];
        parent.type = 'root';
        const reg = this.regHtml;
        let nextIndex: number = 0;
        let currentIndex: number = 0;
        let currentParent = parent;

        // const cdata = {script: 1, style: 1};
        let match = reg.exec(html);
        while (match) { // match 1/2/3/  tagName  4: attr
            currentIndex = match.index;
            try {
                let tagName;
                // if (currentIndex > nextIndex) {
                //     // text node
                //     this.createText(currentParent, html.slice(nextIndex, currentIndex));
                // }

                // tslint:disable-next-line: no-conditional-assignment
                if (tagName = match[3]) {
                    tagName = tagName;
                    if (tagName !== '?xml') {
                        // start tag
                        currentParent = this.crateElement(currentParent, tagName, match[4]);
                        currentParent.startPos = reg.lastIndex;

                        // 处理自闭合标签 add by tinyzhi
                        if (match[4] && match[4].endsWith('/')) {
                            currentParent.endPos = reg.lastIndex;
                            currentParent = currentParent.parentNode;
                        }
                    }
                } else if (match[1]) {
                    if (currentParent.type !== 'root') {
                        const tmpParent = currentParent;
                        while (currentParent.type === 'element' &&
                            currentParent.tagName !== match[1]) {
                            currentParent = currentParent.parentNode;
                            if (currentParent.type === 'root') {
                                currentParent = tmpParent;
                                throw new Error('break');
                            }
                        }
                        // end tag
                        currentParent.endPos = currentIndex;
                        currentParent = currentParent.parentNode;
                        // if (currentParent.tagName === 'w:body') {
                        //     this.rootIndex++;
                        // }
                    }
                }
            } catch (e) {
                //
            }
            nextIndex = reg.lastIndex;
            match = reg.exec(html);
        }

        return parent;
    }
}
