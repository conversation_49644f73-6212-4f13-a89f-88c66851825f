const fs = require('fs');
fs.readFile('./src/components/editor/style/iconfont/iconfont.css', function(err, content) {
    if (err || !content) {
        return;
    }
    var text = content.toString().replace(/\.\w+:before/g, function(res) {
        return '.iconfont' + res;
    });

    writeFile(text);
})

function writeFile(content) {
    fs.writeFile('./src/components/editor/style/iconfont/iconfont.css', content, function(err) {
        if (err) {
            return;
        }
        console.log('Saved success!');
    });
}

