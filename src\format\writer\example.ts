import { Document, Paragraph, TextRun } from '.';

export class FormatExamples {

  public static generateFromExamples(doc: Document): void {

    // this.documentBasic(doc);
    // this.stylesBasic(doc);
    this.headerFooter(doc);

  }

  // example 1: basic document.xml
  public static documentBasic(doc: Document): void {

    const paragraph = new Paragraph('Hello World  ');
    // const paragraph = new Paragraph();

    // const institutionText = new TextRun("Foo Bar");
    const institutionText = new TextRun('Foo Bar').bold()
                                                  .font('STSong');
    const dateText = new TextRun('Github is the best').bold()
                                                      .italic()
                                                      .underline('single');
    paragraph.addRun(institutionText);
    // paragraph.addRun(dateText);

    /**
     * w:t  w:tab  w:t  with same depth level
     */
    const tabTest = new TextRun();
    tabTest.addText('tabtab');
    tabTest.tab();
    tabTest.addText('tab22');
    paragraph.addRun(tabTest);

    doc.addParagraph(paragraph);
  }

  // example 2: styles
  public static stylesBasic(doc: Document): void {

    doc.createParagraph('Some simple content');
    // doc.createParagraph("Test heading2 with double red underline").heading2();

    doc
        .createParagraph()
        .createTextRun('Some monospaced content')
        .font('Monospace');

    // doc.createParagraph("An aside, in light gray italics and indented").style("aside");
    // doc.createParagraph("This is normal, but well-spaced text").style("wellSpaced");
    const para = doc.createParagraph();
    para.createTextRun('This is a bold run,')
        .bold();
    para.createTextRun(' switching to normal ');
    para.createTextRun('and then underlined ')
        .underline();
    para.createTextRun('and back to normal.');

  }

  // example 3: Add text to header and footer
  public static headerFooter(doc: Document): void {

    // doc.createParagraph("Hello World");

    // doc.Header.createParagraph("Header text");
    // doc.Footer.createParagraph("Footer text");

    const header1 = doc.createHeader();
    doc.setHeaderPageRange(1, 1, 4);
    header1.createParagraph('Header on another page');

    const header2 = doc.createHeader();
    header2.createParagraph('Header2 on another page');
    doc.setHeaderPageRange(2, 5, 9);

    // unavailable header, this should trigger error
    doc.setHeaderPageRange(3, 11, 99);

    const footer = doc.createFooter();
    const footerPara = footer.createParagraph('Footer on another page');
    footerPara.createTextRun('this is a text run in footer.')
              .addPageDomain(1, 10);

  }

}
