@import './global.less';
.hz-editor-container {
    .new-control-list, .new-cell-list {
        position: absolute;
        // max-width: 160px;
        min-width: 230px;
        // max-height: 192px;
        text-align: left;
        z-index: 1800;
        font-family: @fontFamily;
        color: @color;
        background:#fff;
        box-shadow: 0px 2px 8px 0px rgba(71,74,91,0.12);
        border-radius: 2px;
        // border: 1px solid rgba(221,221,227,1);
        border: 1px solid #DDDDE3;
        @padding: 8px 16px 0px 12px;
        outline: none;
        color: #293750;
        font-size: 12px;
        
        & * {
            box-sizing: border-box;
        }
        & .search-container .editor-input input {
            font-size: 12px;
        }

        .combox-item-panel {
            max-height: 160px;
            overflow-y: auto;
            overflow-x: hidden;
            margin: 8px;
            position: relative;
            

            &::-webkit-scrollbar {
                width: 7px;
                height: 1px;
                float: right;
            }
    
            &::-webkit-scrollbar-thumb {
                /*滚动条里面小方块*/
                border-radius: 1px;
                background-color: #ddd;
            }
            &::-webkit-scrollbar-track {
                /*滚动条里面轨道*/
                box-shadow   : inset 0 0 2px rgba(0, 0, 0, 0.2);
                background   : #f2f2f2;
                border-radius: 10px;
            }
        }

        .combox-item-list {
            // max-height: 200px;
            width: 100%;
            // margin-bottom: 8px;

    
    
            li {
                // height: 24px;
                // margin: 0px 8px 0px 8px;

                &:first-child {
                    border-top: 1px solid #DDDDE3;
                }

                &:hover {
                    background-color: @activeBgColor;
                    cursor: pointer;
                }
                &.active {
                    color: @activeColor;
                }

                &:focus {
                    outline: none;
                    background-color: @activeBgColor;
                }

                // & > div {
                //     width: 100%;
                //     height: 100%;
                //     line-height: 0.9;
                // }

                & > div, .checkbox-item, input {
                    width: 100%;
                    height: 100%;
                }

                .checkbox-item {
                    padding: 0px;
                    & > * {
                        vertical-align: middle;
                    }
                }
                i {
                //     position: absolute !important;
                //     top: 8px;
                    margin: 0;
                }
                
                label {
                    display: inline-flex;
                    align-self: stretch;
                    margin-left: 12px;
                    height: auto;
                    word-break: break-word;
                }
            }
        }

        .list-header, .combox-item-list { 
            & > li {
                position: relative;
                // height: 28px;
                border-left: 1px solid #DDDDE3;
                border-right: 1px solid #DDDDE3;
                border-bottom: 1px solid #DDDDE3;
                padding-left: 12px;
                margin-right: 1px;
            }

            .one-col {
                min-width: 160px;
            }

            .mul-col {
                min-width: 80px;
                margin: 0px auto 0px;
            }

            .one-col, .mul-col {
                display: inline-block;
                padding: 4px 8px;
                border-left: 1px solid #DDDDE3;
            }


        }
        .list-header {
            // margin: 8px 0px -8px 0px;
            width: 100%;
            line-height: 20px;
            position: relative;
            top: 0px;
            left: 0;
            z-index: 1;

            & > li {
                background: #F6F7F9;

                &:first-child {
                    border-top: 1px solid #DDDDE3;
                }

                & > * {
                    display: inline-block;
                    // overflow: hidden;
    
                    &:first-child {
                        width: 26px;
                    }
                }
            }

            & + .combox-item-list {
                padding-top: 0;
                label {
                    & > * {
                        display: inline-block;
                        vertical-align: middle;
                        // #less.overflow();
                    }
                }
            }
        }

        .search-container {
            padding: 8px 8px 0px 8px;

            // & + .combox-item-list {
            //     max-height: 124px;
            // }
        }

        & > div:last-child {
            padding-bottom: 8px;
        }

        .list-button {
            display: table;
            height: 32px;
            width: 100%;
            padding: 6px 0px 6px 12px;
            text-align: right;
            // border-top: 1px solid @borderColor;
            border-top: 1px solid #DDDDE3;
            background-color: #F6F7F9;
            color: #54627B;
            // box-shadow: inset 0px 1px 0px #DDDDE3;
            
            label {
                // display: table-cell;
                display: inline-block;
                width: 28px;
                height: 100%;
                line-height: 20px;
                margin-right: 12px;
                cursor: pointer;
                &.confirm-label {
                    color: @activeColor;
                }

                &:hover {
                    color: @activeColor;
                }
            }
            
            &.align-left {
                text-align: left;
                label:last-child {
                    padding-left: 3px;
                    color: @color;
                }
            }
            &.align-left {
                text-align: left;
                label:last-child {
                    color: @color;
                }
            }
        }
    }
}