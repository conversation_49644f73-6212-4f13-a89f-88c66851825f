import Document from '@/model/core/Document';
import DocxBuilder from './docx/DocxBuilder';
import SerialBuilder from '../serialize/serializer/SerialBuilder';
/** 
 * 通过document对象序列化对象，然后创建docx的Blob对象
 * TODO: 暂时为耦合行为（懒加载）， 若需兼容插件，则需要抽离一个中间层数据解析
 */
export function docxGenerator(document: Document): Promise<Blob> {
    const serializedObj = new SerialBuilder(document).serialized();
    return new DocxBuilder(serializedObj).generate().toBlob();
}
