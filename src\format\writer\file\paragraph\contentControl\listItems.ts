import { XmlComponent } from "../../xml-components";
import { CodeValueItem } from "../../../../../common/commonDefines";

export class ListItems extends XmlComponent {
  constructor() {
    super('listItems');
  }

  public addListItems(itemList: CodeValueItem[]): ListItems {
    for (const item of itemList) {
      this.root.push(new ListItem(item));
    }
    return this;
  }
}

// tslint:disable-next-line: max-classes-per-file
export class ListItem extends XmlComponent {
  constructor(item: CodeValueItem) {
    super('listItem');
    if (item != null) {
      this.root.push(new ListItemName(item.code));
      this.root.push(new ListItemValue(item.value));
      this.root.push(new ListItemSelect(item.bSelect === true ? '1' : '0'));
    }
  }
}

// tslint:disable-next-line: max-classes-per-file
export class ListItemName extends XmlComponent {
  constructor(text: string) {
      super('name');
      if (text) {
        this.root.push(text);
      }
  }
}

// tslint:disable-next-line: max-classes-per-file
export class ListItemValue extends XmlComponent {
  constructor(text: string) {
      super('value');
      if (text) {
        this.root.push(text);
      }
  }
}

// tslint:disable-next-line: max-classes-per-file
export class ListItemSelect extends XmlComponent {
  constructor(flag: string) {
      super('select');
      if (flag) {
        this.root.push(flag);
      }
  }
}
