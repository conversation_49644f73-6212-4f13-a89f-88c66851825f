import { HeightRule } from 'docx';
import { SerialObjType } from '../serialInterface';
import { ISerialTableRowObj } from '../serialInterface/ISerialTableObj';
import ISerial, { TableRowLineRule } from './ISerial';
import SerialTableCell from './SerialTableCell';
import { transPxToDocxLength } from './Utils';

export default class SerialTableRow implements ISerial {

    constructor(private readonly row: any) {}

    public serializedTo(collector: ISerialTableRowObj[]): ISerialTableRowObj[] {
        const prop = this.row.property;
        const rowObj: Required<ISerialTableRowObj> = {
            type: SerialObjType.TableRow,
            children: [],
            cantSplit: prop.bCanSplit,
            tableHeader: prop.bTableHeader,
            height: this.computeRowHeight(),
        };
        for (const cell of this.row.content) {
           new SerialTableCell(cell).serializedTo(rowObj.children);
        }
        rowObj.children.length && collector.push(rowObj);
        return collector;
    }

    /** 根据边距计算真实行高 */
    private computeRowHeight(): {
        value: number;
        rule: HeightRule;
    } {
        const height = {
            value: 0,
            rule: this.transHeightRule(this.row.property.height.hRule),
        };
        // const tableMargin = this.row.getTable().property.tableCellMargin;
        // const rowHeight = this.row.property.height.value + tableMargin.top.width + tableMargin.bottom.width;
        // height.value = transPxToDocxLength(rowHeight);
        height.value = transPxToDocxLength(this.row.getCurrentRowHeight());
        return height;
    }

    /** 获取行高类型 */
    private transHeightRule(rule: TableRowLineRule): HeightRule {
        switch (rule) {
            case TableRowLineRule.AtLeast: {
                return HeightRule.ATLEAST;
            }
            case TableRowLineRule.Auto: {
                return HeightRule.AUTO;
            }
            case TableRowLineRule.Exact:
            case TableRowLineRule.Fixed: {
                return HeightRule.EXACT;
            }
        }
    }
}
