import ParaPortion from './Paragraph/ParaPortion';

/**
 * 复合文本输入类
 */
export default class CompositeInput {
    public curPortion: ParaPortion;  // 输入时当前段落所在portion
    public curContentPos: number;  // 输入时当前portion内容的位置
    public length: number;  // 输入的文本长度
    public bUndo: boolean;  // 是否可以undo

    constructor() {
        this.curPortion = null;
        this.curContentPos = 0;
        this.length = 0;
        this.bUndo = true;
    }
}
