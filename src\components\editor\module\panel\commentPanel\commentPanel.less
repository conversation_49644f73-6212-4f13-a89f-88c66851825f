
@topHeight: 70px;
.hz-editor-container {
    .editor-morepage-view.comment-layer {
        & > .ReactVirtualized__Grid__innerScrollContainer  .more-page {
            left: 0;
            margin: 0;
        }

        .commentContainer {
            width: 100% !important;
            left: auto;
            margin: 0;
            & > .comment-panel {
                left: auto !important;
                right: 0;
            }
        }

        // .more-page > .page-wrapper {
        //     margin: 0 auto;
        // }
    }
    .comment-panel {
        width: 100%;
        display: grid;
        grid-template-rows: @topHeight 1fr;
        text-shadow: none;
    }

    .top-fixed {
        .summary,
        .filter {
            width: 100%;
            display: flex;
            font-size: 14px;
            line-height: 20px;
            justify-content: space-between;
            user-select: none;
        }
    
        .summary {
            padding: 4px 8px;
            border-bottom: 1px solid #D0D1D2;
            margin-bottom: 8px;
            user-select: text;

            .btns {
                .btn {
                    margin-left: 0;
                    margin-right: 8px;;
                }
            }

            .account {
                flex: 1;
                display: grid;
                grid-template-columns: 60px repeat(2, 1fr);
                .title {
                    width: 80px;
                    border-left: 1px solid #D0D1D2;
                    text-align: center;
                    font-weight: bold;
                }
                &>div>span {
                    font-weight: bold;;
                }
            }
        }
    
        .filter {
            align-items: center;
    
            & * {
                cursor: pointer;
            }
    
            .selector {
                padding: 2px 8px 2px 8px;
                width: 136px;
                height: 24px;
                background: #FFFFFF;
                border-radius: 4px;
                position: relative;
    
                option {
                    padding: 2px 0;
                    cursor: pointer;
                }
            }
    
            .editor-radio {
                padding: 0 8px;
                justify-content: space-between;
                flex-grow: 1;
                display: flex;
    
                .radio-item {
                    margin-right: 0;
                }
            }
        }
    }

    .comments-container {
        padding: 2px;
        width: 100%;
        overflow: auto;
        z-index: 100;
        position: relative;
        
        &::-webkit-scrollbar {
            width: 5px;
            height: 5px;
        }

        &::-webkit-scrollbar-thumb {
            /*滚动条里面小方块*/
            border-radius: 1px;
            background-color: #ddd;
        }
        &::-webkit-scrollbar-track {
            /*滚动条里面轨道*/
            box-shadow   : inset 0 0 2px rgba(0, 0, 0, 0.2);
            background   : #f2f2f2;
            border-radius: 10px;
        }
    }
    
    
    .btns {
        color: #4D4D4D;
        display: flex;
        align-items: center;
        justify-content: space-between;

        &>span:first-child {
            text-align: center;
            min-width: 110px;
            flex-grow: 2;
        }

        .btn {
            justify-content: center;
            align-items: center;
            padding: 4px 6px;
            cursor: pointer;
            float: right;
            margin-left: 8px;
            flex-grow: 1;
            font-size: 12px;
            line-height: 12px;

            width: 54px;
            background-color: #FFFFFF;
            border-radius: 4px;
            border: 1px solid #FFFFFF;
        }

        .cancel:hover {
            border: 1px solid #DEE7FF;
        }

        .primary {
            color: #FFFFFF;
            background: #3664D9;
            border: 1px solid #3664D9;

            &:hover {

                border: 1px solid #DEE7FF;
            }
        }

    }

    .comment {
        width: 100%;
        cursor: pointer;
        position: relative;

        * {
            box-sizing: border-box;
        }

        /* V2: 中性灰 - Neutral Grey/white */
        background: #FFFFFF;
        /* V2: 提醒 - Notice/Primary - 06 */

        /* 阴影 · Shadow/中等 · medium */
        border-top: 3px solid #FFFFFF;
        box-shadow: 0px 4px 10px rgba(77, 77, 77, 0.2);
        border-radius: 4px;

        font-size: 12px;
        line-height: 16px;
        font-weight: 400;
        padding-bottom: 12px;
        margin-bottom: 12px;

        &:last-child {
            margin-bottom: 0;
        }

        .solve {
            position: absolute;
            top: 8px;
            right: 0px;
            font-size: 16px;
            padding: 12px 12px 0 8px;

            &.checked {
                color: #3664D9;
            }

            &.check {
                color: #C4C4C4;
            }

            &.iconfont:hover {
                color: #099FFF;
            }
        }

        /* 悬停工具条 */
        .tools {
            display: none;
            position: absolute;
            top: 20px;
            right: 35px;
            width: 52px;
            font-size: 13px;
            justify-content: space-between;
            padding: 0;

            &.divide {
                border-right: 1px solid #D0D1D2;
            }

            .iconfont {
                font-size: 16px;
                display: flex;
                
                .reply {
                    margin-left: 4px;
                    font-size: 12px;
                    line-height: 16px;
                }
            }
            .iconfont:hover {
                color: #099FFF;
            }
            
        }

        &:hover {
            .tools {
                display: flex;
            }
        }

        &.active {
            border-top: 3px solid #00B050;
        }

        &>div {
            padding: 12px 12px 0 12px;
        }

        .item {
            width: 100%;
            text-align: left;

            .header {
                width: 100%;
                height: 32px;
                display: flex;
                position: relative;

                .user-img {
                    display: inline-block;
                    width: 28px;
                    height: 28px;
                    background: center url('../../../images/doctor.png');
                    border: 1px solid #FFFFFF;
                    border-radius: 10px;
                }

                .tools {
                    top: 8px; 
                    right: 22px;
                    width: 48px;
                    padding-right: 6px;
                }

                .title {
                    margin-left: 4px;
                    display: inline-flex;
                    flex-direction: column;
                    justify-content: center;
                    position: relative;
                    flex-grow: 1;

                    .username {
                        font-weight: 700;
                        color: #000000;
                    }

                    .label {
                        position: absolute;
                        left: 0;
                        top: 0;
                        max-width: 100%;
                        // white-space: wrap;
                        word-break: break-all;
                        display: none;
                        padding: 2px 4px;
                        z-index: 100;
                        font-size: 12px;
                        line-height: 1.5em;
                        color: #737B80;
                        background-color: white;
                        border: 1px solid #ACB4C1;
                    }

                    .username:hover+.label {
                        display: block;
                        transform: translateY(-102%);
                    }

                    .datetime {
                        color: #949494;
                    }
                }
            }

            .content {
                margin-top: 12px;
                line-height: 20px;
                font-size: 14px;
                font-weight: 400;
                word-break: break-all;
            }

        }

        .review-bar {
            width: 100%;
            padding-top: 0;
            margin-top: 12px;

            .review-input {
                width: 100%;
                position: relative;
                font-size: 14px;
                line-height: 20px;


                &>textarea:first-child {
                    resize: none;
                    display: flex;
                    width: 100%;
                    padding: 4px 8px;
                    font-size: 12px;
                    font-weight: 400;
                    line-height: 16px;
                    box-sizing: border-box;
                    align-items: center;
                    outline: none;

                    background: #F3F3F3;
                    border-radius: 4px;
                    border: 1px solid #FFFFFF;

                    &:focus {
                        border: 1px solid #3664D9;
                        box-shadow: 0px 0px 0px 2px #DEE7FF;
                    }
                }

                &>span:last-child {
                    position: absolute;
                    top: 16px;
                    right: 8px;
                    color: #949494;

                    line-height: 28px;
                }
            }
        }

    }

}
