import { XmlAttributeComponent } from '../../../../xml-components';

export interface IHeaderFooterAttributes {
    readonly showHeader?: number;
    readonly showFooter?: number;
    readonly protectHeaderFooter?: number;
    readonly showHeaderBorder?: number;
    readonly showFooterBorder?: number;
}

export class HeaderFooterAttributes extends XmlAttributeComponent<IHeaderFooterAttributes> {
    protected readonly xmlKeys: any = {
        showHeader: 'w:showHeader',
        showFooter: 'w:showFooter',
        protectHeaderFooter: 'w:protect',
        showHeaderBorder: 'w:showHeaderBorder',
        showFooterBorder: 'w:showFooterBorder',
    };
}
