import { embedData } from './DataType';

export class PastePermission {
    private accessId: string; // 外部设置的ID
    private access: boolean; // 是否允许外部访问
    private sourceId: string; // 原码的id
    private pageStr: string; // 是否同一个页过来
    private md5Id: string; // 加密ID
    private code: number;
    private tagetStr: string; // 解码ID
    private reg: RegExp = embedData.Reg;
    constructor() {
        this.access = true;
        this.accessId = '';
    }

    // 是否只允许版本编辑器复制粘贴?
    public setAccess(access: boolean): void {
        this.access = access;
    }

    // 编译后的ID
    public setSourceId(id: string): void {
        if (!id && (this.access === false || !this.accessId)) {
            return;
        }
        this.sourceId = id;
    }

    // 解码数字
    // public setCode(code: number): void {
    //     this.code = code;
    // }

    // public setTargetId(str: string): void {
    //     this.tagetStr = str;
    // }

    // 外部设置的ID
    public setAccessId(id: string): void {
        this.accessId = id;
    }

    // 编译后的ID
    // public setSourceId2(id: string): void {
    //     this.md5Id = id;
    // }

    // 本页的标识码
    public setPageStr(str: string): void {
        this.pageStr = str;
    }

    /**
     * 判断是否在当前编辑器进行复制的
     * @param accessId 粘贴内容中取出来的权限id
     * @param sourceStr 编辑器内部生成的id
     */
    public isOwnerContent(sourceStr: string, fromId: string): boolean {
        if (!fromId) {
            return false;
        }
        // if (this.sameMd5SId(accessId)) {
        //     return true;
        // }

        return fromId === sourceStr;
    }

    /**
     * 判断是否有粘贴权限
     * @param targetId 粘贴内容中取出来的权限解码id
     * @param isExternalContent 是否为外部内容粘贴
     */
    public getPermission(sourceId: string = null, isExternalContent: boolean = false): boolean {
        // if (!sourceId) {
        //     return false;
        // }
        // 不允许编辑器外部访问，accessId必须存在才能进行访问
        if (this.access === false) {
            if (sourceId === this.sourceId) {
                return true;
            }
            return false;
        }

        // 如果设置了accessId（author验证），需要进行权限检查
        if (this.accessId) {
            // 外部内容粘贴不进行author验证，直接允许
            if (isExternalContent) {
                return true;
            }

            // 跨编辑器复制需要进行author验证
            if (sourceId) {
                sourceId = sourceId.split('__')[0];
                if (sourceId.slice(this.pageStr.length) === this.accessId) {
                    return true;
                }
            }
            return false;
        }

        return true;
    }

    public isSamePage(sourceId: string): boolean {
        if (!sourceId) {
            return false;
        }

        if (this.access === false) {
            if (this.sourceId === sourceId) {
                return true;
            }
            return false;
        }

        if (this.accessId) {
            if (sourceId.indexOf(this.accessId) === this.pageStr.length) {
                return true;
            }
        } else if (sourceId.indexOf(this.pageStr) === 0) {
            return true;
        }

        return false;
    }

    // private sameMd5SId(accessId: string): boolean {
    //     if (!this.id || !accessId) {
    //         return false;
    //     }
    //     const sourceId = this.sourceId;
    //     const md5Id = accessId.slice(0, sourceId.length);
    //     if (md5Id === sourceId) {
    //         return true;
    //     }

    //     return false;
    // }
}
