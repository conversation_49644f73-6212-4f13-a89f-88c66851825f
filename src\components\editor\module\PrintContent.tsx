import * as React from 'react';
import { Document } from './document/Document';
//import * as ReactDOM from 'react-dom';
import '../style/common.less';
import { DocumentCore } from '../../../model/DocumentCore';
import Print from '../Print';
import {PrintOutpatient} from '../../../common/print/PrintOutpatient';
import ReactDOM from 'react-dom/client';
//import { ICPrintData } from './modals/PrintDialog';
/* IFTRUE_WATER */
import MarkFactory from '@/common/MarkFactory';
/* FITRUE_WATER */
import {GlobalEvent as gEvent, GlobalEventName as gEventName} from '../../../common/GlobalEvent';
import { ICPrintData } from '@/common/commonDefines';

interface IProps {
    id?: string;
}

interface IEmrState {
    bReflesh: boolean;
    documentCore: DocumentCore;
}

export class PrintContent extends React.Component<{}, IEmrState> {
    public docId: number;
    public myRef: any;
    public clientWidth: number;
    public contentRef: any;
    private documentCore: DocumentCore;
    private pagePro: any;
    private listDom: any;
    private printVm: any;
    private bPrint: boolean;
    private scaleStr: string;
    private printerServerUrl: string;

    private root: ReactDOM.Root | null = null; // 保存 React 根实例的引用

    constructor(props: IProps) {
        super(props);
        this.state = {
            bReflesh: false,
            documentCore: undefined,
        };
        this.myRef = React.createRef();
        this.contentRef = React.createRef();
        this.bPrint = true;
        this.docId = Math.ceil(Math.random() * 10000);
        this.clientWidth = document.body.clientWidth;
        this.scaleStr = '100';
        this.printerServerUrl = 'localhost:8888';
    }

    public render(): any {
        if (this.documentCore === undefined) {
            return <div>正在加载中</div>;
        }
        return (
            <div className='hz-editor-container' ref={this.myRef}>
                <Document host={this} ref={this.contentRef} height={undefined} />
            </div>
        );
    }

    // 在组件初始化时创建 React 根实例
    public componentDidMount(): void {
        const container = this.myRef.current?.parentNode as HTMLElement;

        if (container) {
            this.root = ReactDOM.createRoot(container);
            this.renderEditor();
        }
    }

    private renderEditor(): void {
        if (this.root) {
            this.root.render(<Document host={this} ref={this.contentRef} height={undefined} />);
        }
    }

    public refreshByAsync(): Promise<boolean> {
        return this.contentRef.current.refreshByAsync();
    }

    public handleRefresh(timeout?: number): void {
        if (timeout !== undefined) {
            setTimeout(() => {
                this.contentRef.current?.refresh();
            }, timeout);
        } else {
            this.contentRef.current?.refresh();
        }
    }

    // public clearDom(): void {
    //     if (this.myRef.current && this.myRef.current.parentNode) {
    //         ReactDOM.unmountComponentAtNode(this.myRef.current.parentNode);
    //         if (this.printVm && typeof this.printVm.close === 'function') {
    //             this.printVm.close();
    //         }
    //     }
    // }

    public clearDom(): void {
        // 使用 root 实例卸载组件
        if (this.root) {
            this.root.unmount();
            this.root = null; // 清理根实例引用
        }

        // 清理 printVm
        if (this.printVm && typeof this.printVm.close === 'function') {
            this.printVm.close();
        }

        // 清理 myRef 和 iframe
        if (this.myRef.current && this.myRef.current.parentNode) {
            const parentNode = this.myRef.current.parentNode as HTMLElement;          
        }
    }
    
    public setDocumentCore(documentCore: DocumentCore, bCprint: boolean): Promise<number> {
        return new Promise((resolve, reject) => {
            if (documentCore !== undefined && documentCore !== this.documentCore) {
                this.documentCore = documentCore;
                documentCore.setPrintStatus(true);
                // this.docId = documentCore.getCurrentId();
                this.setState({ documentCore }, () => {
                    if (bCprint !== true) {
                        this.scaleViewEvent(this.scaleStr);
                    }
                    resolve(1);
                });
                return;
            }
            resolve(0);
            this.handleRefresh();
        });
    }

    public scaleViewEvent(scale: string): void {
        this.scaleStr = scale;
        this.setLayouWidth(scale);
    }

    public openPrint(type: number, pagePosition?: string, option: any = {type: 0}): void {
        // console.log(this.printVm)
        if (!this.printVm) {
            if (option && option.type === 5) {
                this.printVm = new PrintOutpatient(this);
                this.printVm.getPrintOutpatientContent(option)
                .then((result) => {
                    if (option.close) {
                        option.close(result);
                    }
                });
                return;
            } else {
                this.printVm = new Print(this);
                this.printVm.setPrinterServerUrl(this.printerServerUrl);
                
                // 从传入的option中获取外部事件和打印行为配置
                if (option.externalEvent) {
                    this.printVm.setExternalEvent(option.externalEvent);
                }
                
                if (typeof option.disablePrintAction !== 'undefined') {
                    this.printVm.setDisablePrintAction(option.disablePrintAction);
                }
            }
        }
        // this.documentCore.removeAllPlaceholderNewControls();
        this.setState({}, () => {
            this.printVm.print(type, pagePosition, option)
            .then((result) => {
                /* IFTRUE_WATER */
                // 重置水印生成
                MarkFactory.getInstance().setBGenStart(true);
                /* FITRUE_WATER */
                this.handleRefresh();
                if (option.close) {
                    option.close(result);
                }
            });
        });
    }

    public openCPrint(type: number, data: ICPrintData): Promise<boolean> {
        return new Promise((resolve, reject) => {
            if (!this.printVm) {
                this.printVm = new Print(this);
                this.printVm.setPrinterServerUrl(this.printerServerUrl);
            }
            // this.documentCore.removeAllPlaceholderNewControls();
            this.setState({}, () => {
                // console.log(1)
                this.printVm.cPrint(type, data)
                .then((result) => {
                    /* IFTRUE_WATER */
                    // 重置水印生成
                    MarkFactory.getInstance().setBGenStart(true);
                    /* FITRUE_WATER */
                    this.handleRefresh();
                    resolve(result);
                });
            });
        });
    }

    public setPrinterServerUrl(str: string): void {
        this.printerServerUrl = str;
    }
    // public getTruePrintContent(): string {
    //     return this.printVm.truePrintContent;
    // }

    private setLayouWidth(scaleStr: string): void {
        return;
        const scale = parseInt(scaleStr, 10);
        const editorContainer = this.myRef.current;
        let dom: HTMLDivElement;
        if (this.listDom) {
            dom = this.listDom;
        } else {
            this.listDom = dom = editorContainer.querySelector(
                '.ReactVirtualized__Grid',
            );
        }
        if (!dom) {
            return;
        }
        this.getPagerPro();
        const pagePro = this.pagePro;

        const matchs = /\s+scale-\d+/.exec(dom.className);
        if (matchs) {
            dom.className = dom.className.replace(matchs[0], ' scale-' + scale);
        } else {
            dom.className += ' scale-' + scale;
        }
        if (scale > 100) {
            const width = pagePro.width * (scale / 100);
            dom.style.width = width + 'px';
        } else {
            let addNum = 0;
            switch (scale) {
                case 25:
                    addNum += 30;
                    break;
                case 50:
                    addNum += 10;
                    break;
            }
            dom.style.width = pagePro.width + addNum + 'px';
        }

        this.setLayouHeight(scale);
        // dom.style.width = width + 'px';
    }

    private getPagerPro(): void {
        this.pagePro = this.documentCore.getPageProperty();
    }

    // getTotal(): number {
    //   const { total } = this.state.documentCore.render();
    //   return total;
    // }

    private setLayouHeight(scale: number): void {
        const doms = this.listDom.querySelectorAll('.page-wrapper');
        if (!doms) {
            return;
        }
        const pagePro = this.pagePro;
        const num = scale / 100;
        const width = pagePro.width * num + 'px';
        const height = pagePro.height * num + 'px';
        Array.from(doms)
        .forEach((dom) => {
            const div = dom as HTMLDivElement;
            div.style.height = height;
            div.style.width = width;
        });

        let len = doms.length;

        switch (scale) {
            case 25:
                len = Math.ceil(len / 4);
                break;
            case 50:
                len = Math.ceil(len / 2);
                break;
        }

        const listHeight = pagePro.height * num * len + len * 20 + 'px';
        this.listDom.style.height = listHeight;
    }
}
