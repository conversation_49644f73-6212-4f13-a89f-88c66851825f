import * as React from 'react';
import Dialog from '../../ui/Dialog';
import Button from '../../ui/Button';
import { fonts } from '../../text/font';
// const requireObj = require('@/model/core/TextMeasureSize1');
// import { isMacOs } from '@/common/commonMethods';
import { FONT_MAPPING, isMacOs } from '@/common/commonDefines';
// import {CACHES} from '@/model/core/TextMeasureSize';

interface IDialogProps {
    documentCore: any;
    visible: boolean;
    id: string;
    close: (name: string | number, bRefresh?: boolean) => void;
}

export default class TestTextCache extends React.Component<IDialogProps> {
    private visible: boolean;
    private content: string;
    private fonts: any[];
    constructor(props) {
        super(props);
        this.fonts = fonts;
        this.visible = props.visible;
        this.content = '我 0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ,.;/?()[]=-+*<>，。；：:’“？、（）【】{}☑☐⊙⭘◯℃'; // ↵
    }

    public render(): any {
        return (
            <Dialog
                visible={this.visible}
                width={650}
                height={400}
                title='字体测试'
                footer={this.renderFooter()}
                id={this.props.id}
            >
                <div>
                    <div className='editor-line'>
                        <div className='w-70'>格式</div>
                        <div className='right-auto'>
                            <textarea
                                style={{width: '100%', height: '80px'}}
                                name='content'
                                value={this.content || ''}
                                onChange={this.textareaChange}
                            />
                        </div>
                    </div>
                </div>
            </Dialog>
        );
    }

    public componentDidMount(): void {
        ;
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        this.visible = nextProps.visible;
    }

    /**
     * 是否为东亚字符，包括中文标点符号
     */
     public isEastAsianScript(char: string): boolean {
        if (this.isChinese(char) || this.isChineseSymbol(char)) {
            return true;
        }

        return false;
    }

    /**
     * 是否为中文字符
     */
    public isChinese(char: string): boolean {
        return (/[\u4e00-\u9fea]/.test(char));
    }

    /**
     * 是否为中文标点
     */
    public isChineseSymbol(char: string): boolean {
        return (/[\uff00-\uffef]/.test(char) || /[\u3000-\u303f]/.test(char));
    }

    /**
     * 文本内容是否是空格
     */
     public isSpace(char: string): boolean {
        // return RepalceText.Space === this.content;
        return (/[\u0020\u00a0\u202F\u0009]/.test(char));
    }

    public isEscapeString(char: string): boolean {
        if (/[\b\f\v\0\r\n\t]/.test(char)) {
            return true;
        }

        return false;
    }

    private renderFooter(): any {
        return (
            <span>
                <Button onClick={this.close}>取消</Button>
                <Button type='primary' onClick={this.confirm}>确认</Button>
            </span>
        );
    }

    private confirm = (): void => {
        // if (!this.content) {
        //     return;
        // }
        // const fontFamilys = this.fonts[0];
        // const fontSizes = this.fonts[1];
        // requireObj.getCommon();
        // requireObj.initMeasureCaches();
        // let sources = requireObj.getCaches();
        // const errors = [];
        // const isOs = isMacOs;
        // if (isOs) {
        //     requireObj.clearCaches();
        //     requireObj.getCommon();
        //     requireObj.macOs();

        //     sources = requireObj.getCaches();
        //     // for ( const [key, item] of source1) {
        //     //     sources.set(key, item);
        //     // }
        // }

        // for (let index = 0, len = this.content.length; index < len; index++) {
        //     const char = this.content.charAt(index);
        //     fontFamilys.options.forEach((font) => {
        //         fontSizes.options.forEach((size) => {
        //             const fontFamily = font.value;
        //             const fontSize = size.value;
        //             if (fontSize < 10.7 || fontSize > 63.9) {
        //                 return;
        //             }
        //             const oFont = {fontFamily, fontSize};
        //             let actMeasure: any; //  = measure(char, oFont);
        //             // if (actMeasure) {
        //             //     actMeasure = actMeasure[0];
        //             // }
        //             let sourceMeasure;
        //             const textKey = this.getTextKey(char);
        //             const fontKey = this.getCacheKey(oFont);
        //             const texts = sources.get(fontKey);
        //             if (texts) {
        //                 sourceMeasure = texts.get(textKey);
        //             }
        //             const actTexts = CACHES.get(fontKey);
        //             if (actTexts) {
        //                 actMeasure = actTexts.get(textKey);
        //             }
        //             if (!actMeasure || !sourceMeasure || actMeasure.width - sourceMeasure.width > 0.1
        //                 || actMeasure.height - sourceMeasure.height > 0.1) {
        //                 errors.push({char, fontFamily, fontSize, target: actMeasure, source: sourceMeasure});
        //                 // console.log(textKey, fontKey, texts)
        //             }
        //         });
        //     });
        // }
        // if (errors.length > 0) {
        //     console.error('下面为字体缓存错误信息！！！');
        //     console.log(errors);
        // }

    }

    private getTextKey(char: string): string {
        let text = char;
        // 字符对象为空格，则使用“我”替代
        if ( this.isSpace(char)) {
            text = '我';
        } else if ( this.isEastAsianScript(char) ) {
            text = '我';
        } else if (this.isEscapeString(char)) {
            return '';
        }

        return text;
    }

    private getCacheKey(font: {fontSize: number, fontFamily?: string}): string {
        let tempFontFamily = font.fontFamily;
        if (isMacOs) {
            const fontsToBeTransferred = ['宋体', '黑体', '仿宋', '楷体', '幼圆'];
            if (fontsToBeTransferred.includes(tempFontFamily) === true) {
                tempFontFamily = FONT_MAPPING[tempFontFamily].mac;
                // console.log(tempFontFamily)
            }
        }

        const fontsArr = ['Times New Roman', 'STSong', 'STHeiti', 'FangSong_GB2312', 'STKaiti', 'Hiragino Sans GB'];
        // const fontsArr = ['Times New Roman'];
        // const text = (fontsArr.includes(font.fontFamily) === false) ?
        //         font.fontSize.toString() : font.fontFamily + font.fontSize;
        const text = (fontsArr.includes(tempFontFamily)) ? tempFontFamily + font.fontSize :
                font.fontSize.toString();
        // console.log(text)
        return text;
    }

    private textareaChange = (e): void => {
        this.content = e.target.value;
        this.setState({});
    }

    private close = (bRefresh?: boolean): void => {
        this.props.close(this.props.id, bRefresh);
        this.visible = false;
        this.setState({});
    }
}
