import * as React from 'react';
import Dialog from '../../ui/Dialog';
import Button from '../../ui/Button';
import Checkbox from '../../ui/CheckboxItem';
// tslint:disable-next-line: max-line-length
import { CustomPropertyElementType, isValidUnit } from '../../../../common/commonDefines';
import CustomPropertyBtn from './CustomProperty';
import { IFRAME_MANAGER } from '../../../../common/IframeManager';
import Input from '../../ui/Input';
import { DocumentCore } from '../../../../model/DocumentCore';
import '../../style/NewNISList.less';
import { message } from '../../../../common/Message';
import { NISTableCellCommon } from './NISTableCellCommonProps';

interface IDialogProps {
    documentCore: DocumentCore;
    visible: boolean;
    id?: string;
    close: (name: string | number, bRefresh?: boolean) => void;
    refresh: (bRefresh: boolean, timeout?: number) => void;
}

interface IState {
    bRefresh: boolean;
}

export default class NISTableCellNumber extends React.Component<IDialogProps, IState> {
    private cellProps: any; // revised cell props
    // private tableProps: any;
    private visible: any;
    private bCustomProperty: boolean;
    private docId: number;
    private nisProps: any;

    constructor(props: any) {
        super(props);
        this.cellProps = {
            bProtected: false,
            // minValue: undefined,
            // maxValue: undefined,
            // precision: undefined,
            // bCanFormulaCacl: false,
            // formularType: false,
            // customProperty: undefined,
            nisProperty: {},
        };
        this.state = {
            bRefresh: false,
        };
        this.nisProps = {};
        this.visible = this.props.visible;
        // this.setDialogValue();
        this.docId = this.props.documentCore.getCurrentId();
    }

    public componentDidMount(): void {
        // this.boxRef.current.ownerDocument.addEventListener('click', this.docClick);
    }

    public componentWillUnmount(): void {
        // this.boxRef.current.ownerDocument.removeEventListener('click', this.docClick);
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        this.visible = nextProps.visible;
    }

    public render(): any {
        const nisProps = this.cellProps.nisProperty;
        return (
            <Dialog
                id={this.props.id}
                visible={this.visible}
                width={350}
                // height={320}
                // close={this.close}
                open={this.open}
                preventDefault={false}
                title='数值单元格'
                confirm={this.confirm}
                footer={this.renderFooter()}
            >
                <div>
                    <div className='editor-line'>
                        <div className='w-70'>内部名称</div>
                        <div className='right-auto'>
                            <Input
                                name='nis-serialNumber'
                                value={nisProps.serialNumber}
                                onChange={this.onChange}
                                readonly={true}
                                placeholder={''}
                                disabled={true}
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <span className='title table-label'>属性</span>
                    </div>
                    <div className='editor-line'>
                        <span className='w-40'>
                            <Checkbox
                                name='bProtected'
                                value={this.cellProps.bProtected}
                                disabled={false}
                                onChange={this.onChange}
                            >
                                禁止编辑
                            </Checkbox>
                        </span>
                        <Checkbox
                            name='visible'
                            value={this.cellProps.visible}
                            disabled={false}
                            onChange={this.onChange}
                        >
                            网格线
                        </Checkbox>
                    </div>
                    <div className='editor-line'>
                        <label className='w-20'>最小值：</label>
                        <span className='w-20'>
                            <Input
                                type='number'
                                name='nis-minValue'
                                value={nisProps.minValue}
                                onChange={this.onChange}
                            />
                        </span>

                        <label className='w-20' style={{marginLeft: '15px'}}>最大值：</label>
                        <span className='w-20'>
                            <Input
                                type='number'
                                name='nis-maxValue'
                                value={nisProps.maxValue}
                                onChange={this.onChange}
                            />
                        </span>
                    </div>
                    <div className='editor-line'>
                        <label className='w-20'>精度：</label>
                        <span className='w-20'>
                            <Input
                                type='number'
                                name='nis-precision'
                                value={nisProps.precision}
                                onChange={this.onChange}
                            />
                        </span>

                        <label className='w-20' style={{marginLeft: '15px'}}>单位：</label>
                        <span className='w-20'>
                            <Input
                                name='nis-unit'
                                value={nisProps.unit}
                                onChange={this.onChange}
                            />
                        </span>
                    </div>
                    <div className='editor-line'>
                        <label className='w-90'>最小预警值：</label>
                        <div className='right-auto-90'>
                            <Input
                                type='number'
                                name='nis-minWarn'
                                value={nisProps.minWarn}
                                onChange={this.onChange}
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <label className='w-90'>最大预警值：</label>
                        <div className='right-auto-90'>
                            <Input
                                type='number'
                                name='nis-maxWarn'
                                value={nisProps.maxWarn}
                                onChange={this.onChange}
                            />
                        </div>
                    </div>
                    <NISTableCellCommon prop={this.nisProps}/>
                    <div style={{height: '1px'}} />
                    <div className='editor-line'>
                        <CustomPropertyBtn
                            name='nis-customProperty'
                            properties={nisProps.customProperty}
                            documentCore={this.props.documentCore}
                            onChange={this.onChange}
                            close={this.onClose}
                            id='bCustomProperty'
                            visible={this.bCustomProperty}
                            type={CustomPropertyElementType.TableCell}
                        />
                    </div>
                </div>
            </Dialog>
        );
    }

    private renderFooter(): any {
        return (
            <span>
                <Button onClick={this.close}>取消</Button>
                <Button type='primary' onClick={this.confirm}>确认</Button>
            </span>
        );
    }

    private setDialogValue(): void {
        const cellProps = this.props.documentCore.getTableCellProps();
        if ( cellProps ) {
            this.cellProps.bProtected = cellProps.bProtected;
            const nisProperty = this.nisProps = cellProps.nisProperty;
            const nisProps = this.cellProps.nisProperty;
            this.cellProps.visible = nisProperty.gridLine?.visible;
            nisProps.serialNumber = cellProps.nisProperty ? cellProps.nisProperty.serialNumber : undefined;
            nisProps.minValue = cellProps.nisProperty ? cellProps.nisProperty.minValue : undefined;
            nisProps.maxValue = cellProps.nisProperty ? cellProps.nisProperty.maxValue : undefined;
            nisProps.precision = cellProps.nisProperty ? cellProps.nisProperty.precision : undefined;
            nisProps.unit = cellProps.nisProperty ? cellProps.nisProperty.unit : '';
            nisProps.customProperty = cellProps.nisProperty ? cellProps.nisProperty.customProperty : undefined;
            if (nisProperty) {
                nisProps.minWarn = nisProperty.minWarn;
                nisProps.maxWarn = nisProperty.maxWarn;
            } else {
                nisProps.minWarn = undefined;
                nisProps.maxWarn = undefined;
            }
        }
    }

    private open = (): void => {
        // const table = this.table;
        this.setDialogValue();
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private close = (bRefresh?: boolean): void => {
        this.props.close(this.props.id, bRefresh);
        this.visible = false;
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private onClose = (id: string, bRefresh: boolean): void => {
        this[id] = false;
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private onChange = (value: any, name: string): void => {
        if (name.indexOf('nis-') !== -1) {
            const trueName = name.slice('nis-'.length);
            this.cellProps.nisProperty[trueName] = value;
        } else {
            this.cellProps[name] = value;
        }
    }

    private confirm = (id?: any): void => {
        if (!this.isValidNum()) {
            return ;
        }

        if (!this.unitBlur()) {
            return;
        }

        if (typeof this.cellProps.nisProperty.minValue !== 'number') {
            this.cellProps.nisProperty.minValue = undefined;
        }

        if (typeof this.cellProps.nisProperty.maxValue !== 'number') {
            this.cellProps.nisProperty.maxValue = undefined;
        }

        const cell = this.cellProps;
        cell.nisProperty.gridLine = {
            visible: cell.visible
        }

        const { documentCore } = this.props;
        IFRAME_MANAGER.setDocId(this.docId);
        // console.log(this.cellProps)
        documentCore.setTableCellProps(cell);

        this.close(true);
    }

    private isValidNum = (key: string = 'minValue'): boolean => {
        // const key = this._currentName;
        // if (key === 'unit') {
        //     return;
        // }
        // const oldFlag = this.isValidData;
        // this.isValidData = false;
        const nisProperty = this.cellProps.nisProperty;

        let minWarn = nisProperty.minWarn;
        let maxWarn = nisProperty.maxWarn;
        if (minWarn != null) {
            if (typeof minWarn !== 'number') {
                if (!minWarn) {
                    minWarn = nisProperty.minWarn = null;
                } else {
                    message.error('最小预警值不符合规范，请重新输入!');
                    return false;
                }
            } else {
                const minValue = nisProperty.minValue;
                if (typeof minValue === 'number' && minWarn < minValue) {
                    message.error('最小预警值不能小于最小值!');
                    return false;
                }
                const maxValue = nisProperty.maxValue;
                if (typeof maxValue === 'number' && minWarn > maxValue) {
                    message.error('最小预警值不能大于最大值!');
                    return false;
                }
            }
        }

        if (maxWarn != null) {
            if (typeof maxWarn !== 'number') {
                if (!maxWarn) {
                    maxWarn = nisProperty.maxWarn = null;
                } else {
                    message.error('最大预警值不符合规范，请重新输入!');
                    return false;
                }
            } else {
                const maxValue = nisProperty.maxValue;
                if (typeof maxValue === 'number' && maxWarn > maxValue) {
                    message.error('最大预警值不能大于最大值!');
                    return false;
                }
                if (minWarn != null && minWarn > maxWarn) {
                    message.error('最小预警值不能大于最大预警值!');
                    return false;
                }
            }
        }

        const val = nisProperty[key];
        if (val === undefined) {
            return true;
        }

        const value: any = Number.parseFloat(val);
        if (key === 'minValue') {
            if (typeof nisProperty.maxValue === 'number') {
                if (nisProperty.maxValue < value) {
                    message.error('最小值不能大于最大值!');
                    return false;
                }
            }
            if (maxWarn != null && maxWarn < value) {
                message.error('最大预警值不能小于最小值!');
                return false;
            }
        } else if (typeof nisProperty.minValue === 'number') {
            if (nisProperty.minValue > value) {
                message.error('最大值不能小于最小值!');
                return false;
            }
            if (maxWarn != null && maxWarn < nisProperty.minValue) {
                message.error('最大预警值不能小于最小值!');
                return false;
            }
        }

        if (typeof nisProperty.precision === 'number') {
            if ( (0 !== nisProperty.precision % 1) || nisProperty.precision < 0 || 8 < nisProperty.precision) {
                message.error('精度为不小于0且不大于8的整数!');
                return false;
            }
        }

        nisProperty[key] = value;
        return true;
    }

    private unitBlur = (): boolean => {
        const value = this.cellProps.nisProperty.unit;
        if (value && !isValidUnit(value)) {
            message.error('单位设置错误');
            return false;
        }

        return true;
    }
}
