import { CopyContent} from './Content';
import { CopySource, embedData } from './DataType';
import Apollo from './apollo';
import DocumentContentElementBase from '../../model/core/DocumentContentElementBase';
import Paragraph from '../../model/core/Paragraph';
import ParaPortion from '../../model/core/Paragraph/ParaPortion';
import { encodeTag } from './DataConver';
import {DocumentContentType} from '../../model/core/Style';
import { ParaElementType } from '../../model/core/Paragraph/ParagraphContent';
import { Table } from '../../model/core/Table';
import { TableRow } from '../../model/core/Table/TableRow';
import { TableCell } from '../../model/core/Table/TableCell';
import StructCore from './StructCore';
import { Region } from '../../model/core/Region';
import { RegionCore } from './RegionCore';
import { INewControlProperty, NewControlType, REGION_MAX_DEPTH } from '../commonDefines';
import { TableCore } from './TableCore';
import ParaDrawing from '../../model/core/Paragraph/ParaDrawing';
import TextProperty from '../../model/core/TextProperty';
import ParaText from 'src/model/core/Paragraph/ParaText';
import { clearWavyUnderlineFromPortion } from '../../model/core/WavyUnderline/WavyUnderlineUtils';

let newControlPropertys: any[];
let regionProps: any;
let apolloStr: any;
export default class CopyToClipboard {
    public copyContent: CopyContent[];
    public selections: DocumentContentElementBase [];
    public id: string;
    // public newControlPropertys: any[];
    // public regionProps: any[];
    private source: CopySource;
    private doc: any;
    private struct: StructCore;
    private structNames: string[];
    private regionCore: RegionCore;
    private tableCore: TableCore;
    private drawObj: any;
    private defaultTextProp: any;
    private defaultParaProps: any;

    private buttonManager: any;
    private buttons: Map<string, any>;


    public parseHtml(): string {
        this.initContent();
        const contents = this.copyContent;
        let html = '';
        const firstContent = contents[0]?.content;
        let firstWord: string;
        if (firstContent) {
            firstWord = firstContent.slice(0, 1);
            if (/\s/.test(firstWord)) {
                firstWord = '&nbsp;';
            }
            contents[0].content = firstContent.slice(1);
        }
        contents.forEach((item) => {
            const content = encodeTag(item.content);
            if (!content) {
                html += '<empty class="empty">&nbsp;</empty>';
            } else if (content.trim().length === 0 && content.length === 1) {
                html += `<span>&nbsp;</span>`;
            } else {
                if (html) {
                    html += '<empty class="empty"><br/></empty>';
                }
                html += `<span>${content}</span>`;
            }
        });
        // tslint:disable-next-line: no-invalid-template-strings
        const apollo = new Apollo(this.selections, this.doc);
        const text = apollo.getApollo();
        const roleIdDom = embedData.setNode(text, html, this.id);
        newControlPropertys = apollo.getNewControlProps();
        regionProps = apollo.getRegionProps();
        apolloStr = text;

        return `<div><span>${firstWord}</span>${roleIdDom}</div>`;
    }

    public setDocument(doc: any): void {
        this.doc = doc;
    }

    public setNewControlProps(props: any[]): void {
        newControlPropertys = props;
    }

    public setRegionProps(props: any[]): void {
        regionProps = props;
    }

    public getApollo(): string {
        return apolloStr;
    }

    public clear(): void {
        newControlPropertys = null;
        regionProps = null;
        apolloStr = null;
    }

    public getNewContents(selections: DocumentContentElementBase[], bFormat: boolean): DocumentContentElementBase[] {
        if (!selections || 0 === selections.length) {
            return [];
        }
        if (false === this.doc.canInput()) {
            return;
        }
        let maxDepth = this.doc.getSelectionStartRegionDepth();
        if (maxDepth > REGION_MAX_DEPTH) { // 暂时不能嵌套区域
            return;
        }
        const struct = this.struct = new StructCore(this.doc);
        if (struct.isCanntEdit()) {
            return;
        }

        if (bFormat === false) {
            const textProps = this.doc.getTextProps(struct.isPlaceholder());
            const paraProps = this.doc.getParagraphProperty();
            if (textProps) {
                const text = new TextProperty(textProps  as any);
                this.defaultTextProp = text;
            }
            if (paraProps) {
                this.defaultParaProps = paraProps;
            }
            return this.getNewContents2(selections);
        }

        this.buttonManager = null;
        this.buttons = null;

        this.structNames = [];
        let datas = [];
        const index = 0;
        this.drawObj = undefined;
        selections.forEach((item) => {
            const type = item.getType();
            if (type === DocumentContentType.Region) {
                datas.push(this.getRegion(item, this.doc, index));
            } else if (type === DocumentContentType.Table) {
                datas.push(this.getTable(item, this.doc));
            } else {
                datas.push(this.getParagraph(item, this.doc));
            }
        });
        if (struct.checkDatas(datas) === false) {
            this.resetPaste();
            return;
        }
        const bInfNewControl = struct.isFocusInNewControl();
        const bNewSection = struct.isNewSection();
        if (true || !bInfNewControl || bNewSection) {
            const regionManager: RegionCore = this.getRegionManager();
            const currentDepth = regionManager.getCurrentMaxDepth();
            if (currentDepth > 0 && bInfNewControl) {
                this.resetPaste();
                return;
            }
            maxDepth += currentDepth;
            if (maxDepth > REGION_MAX_DEPTH) {
                this.resetPaste();
                return;
            }
        }

        if (datas.length && bInfNewControl && !bNewSection) {
            const para = new Paragraph(undefined, this.doc);
            struct.getContentsToPara(datas, para);
            datas = [para];
            this.resetPasteStruct();
        }

        this.struct.addCascadeCaches();

        this.regionCore = null;
        this.struct = null;
        return datas;
    }

    public getNewContents2(selections: DocumentContentElementBase[]): any[] {
        let datas = [];
        selections.forEach((item) => {
            if (item['bHidden']) {
                return;
            }
            const type = item.getType();
            let paras: any;
            if (type === DocumentContentType.Region) {
                paras = this.getRegion2(item, this.doc);
                if (paras.length === 0) {
                    return;
                }
                datas.push(...paras);
            } else if (type === DocumentContentType.Table) {
                paras = this.getTable2(item, this.doc);
                if (paras.length === 0) {
                    return;
                }
                datas.push(...paras);
            } else {
                const curPara = this.getParagraph2(item, this.doc);
                if (curPara.getContent().length === 1) {
                    return;
                }
                datas.push(curPara);
            }
        });
        const struct = this.struct;
        if (datas.length && struct.isFocusInNewControl() && !struct.isNewSection()) {
            const para = new Paragraph(undefined, this.doc);
            struct.getContentsToPara(datas, para);
            datas = [para];
            this.resetPasteStruct();
        }
        this.struct = null;
        return datas;
    }

    private resetPaste(): void {
        this.regionCore?.resetPaste();
        this.regionCore = null;
        this.struct.resetPaste();
        this.struct = null;
        if (this.drawObj) {
            this.drawObj.resetPasteDrawing();
        }
    }

    private resetPasteStruct(): void {
        this.regionCore?.resetPaste();
        this.struct?.resetPaste();
    }

    private getRegionManager(): RegionCore {
        if (this.regionCore) {
            return this.regionCore;
        }

        return this.regionCore = new RegionCore(this);
    }

    private getRegion2(item: any, parent: any): any[] {
        const paras = [];
        item.getContent()
        .forEach((element) => {
            if (element['bHidden']) {
                return;
            }
            const type = element.getType();
            let paras2;
            switch (type) {
                case DocumentContentType.Region: {
                    paras2 = this.getRegion2(element, this.doc);
                    if (paras2.length === 0) {
                        return;
                    }
                    paras.push(...paras2);
                    break;
                }
                case DocumentContentType.Paragraph: {
                    const curPara = this.getParagraph2(element, this.doc);
                    // if (curPara.getContent().length === 1) {
                    //     return;
                    // }
                    paras.push(curPara);
                    break;
                }
                case DocumentContentType.Table: {
                    paras2 = this.getTable2(element, this.doc);
                    if (paras2.length === 0) {
                        return;
                    }
                    paras.push(...paras2);
                    // region.addToContent(len, this.getTable(element, parent));
                    break;
                }
            }
        });

        return paras;
    }

    private getRegion(item: any, root: any, index: number): DocumentContentElementBase {
        const name = item.getName();
        let props = regionProps.find((prop) => prop.newControlName === name);
        if (!props) {
            return;
        }
        props = {...props};
        const regionManager: RegionCore = this.getRegionManager();
        const newName = regionManager.makeUniqueName();
        props.newControlName = newName;
        const region = new Region(root, this.doc, props);
        const parent = region.getOperateContent();
        let parentRegion: Region;
        if (root !== this.doc) { // 没有父区域
            parentRegion = root.parent;
        }
        regionManager.addRegion(region, parentRegion);
        let currentIndex = 0;
        item.getContent()
        .forEach((element) => {
            const type = element.getType();
            const len = region.getContent().length;
            switch (type) {
                case DocumentContentType.Region: {
                    region.addToContent(len, this.getRegion(element, parent, currentIndex++));
                    break;
                }
                case DocumentContentType.Paragraph: {
                    region.addToContent(len, this.getParagraph(element, parent));
                    break;
                }
                case DocumentContentType.Table: {
                    region.addToContent(len, this.getTable(element, parent));
                    break;
                }
            }
        });
        if (props.showTitle) {
            region.addPasteTitlePortion();
        }
        return region;
    }

    private getTableCore(): TableCore {
        if (!this.tableCore) {
            this.tableCore = new TableCore(this);
        }

        return this.tableCore;
    }

    private getTable2(item: any, root: any): any[] {
        const paras = [];
        item.content.forEach((row) => {
            row.content.forEach((cell) => {
                cell.content.content.forEach((content) => {
                    const curPara = this.getParagraph2(content, this.doc);
                    // if (curPara.getContent().length === 1) {
                    //     return;
                    // }
                    paras.push(curPara);
                });
            });
        });

        return paras;
    }

    private getTable(item: DocumentContentElementBase, root: any): DocumentContentElementBase {
        const tableCore = this.getTableCore();
        const data = item as Table;
        const table = new Table(root, this.doc, 0, 0, []);
        let maxCol: number = 0;
        data.content.forEach((row, rowIndex) => {
            const tblRow: TableRow = table.addRow(rowIndex, 0, false);
            let cols: number = 0;
            row.content.forEach((cell, cellIndex) => {
                const paras = [];
                const tblCell = new TableCell(tblRow);
                cell.content.content.forEach((content) => {
                    paras.push(this.getParagraph(content, tblCell.content));
                });
                const property = cell.property.copy();
                if (property.gridSpan) {
                    tblCell.setGridSpan(property.gridSpan);
                    cols += property.gridSpan;
                }

                tblCell.content.addContentElements(paras);
                tableCore.setCellProps(tblCell, property, cell.borderInfo);
                tblRow.addCell(cellIndex, tblRow, tblCell, false);
            });
            tableCore.setRowProps(tblRow, row.property);
            if (cols > maxCol) {
                maxCol = cols;
            }
        });
        tableCore.setTableProps(table, data.property);
        const grid: number[] = (item as any).tableGrid;
        if (maxCol === grid.length) {
            tableCore.setTableGrid2(grid, table);
        } else {
            tableCore.setTableGrid(maxCol, table);
        }
        tableCore.setDefaultCell(table);
        const tableManager = this.doc.getTableManager();
        if ( tableManager ) {
            table.tableName = tableManager.getUniqueTableName();
            tableManager.add(table, table.tableName);
        }

        return table;
    }

    private getParagraph2(item: any, root: any): DocumentContentElementBase {
        const para = item as Paragraph;
        const newPara = new Paragraph(root, this.doc);
        // if (this.drawObj === undefined) {
        //     this.drawObj = this.doc.getDrawingObjects();
        // }

        let curIndex = 0;
        let bPlaceholder: boolean;
        para.content.forEach((por, porIndex) => {
            if (por['bHidden']) {
                return;
            }
            let contents = por.content;
            if (por.isNewControl()) {
                if (por.isNewControlStart()) {
                    if (por.isPlaceHolder()) {
                        bPlaceholder = true;
                    }
                    if (contents.length === 1) {
                        return;
                    }
                    contents = por.content.slice(1, contents.length);
                } else {
                    bPlaceholder = false;
                    return;
                }
            } else if (bPlaceholder === true) {
                return;
            }
            const newPor = new ParaPortion(newPara);
             
            newPor.setProperty(por.textProperty.copy());
            // 清除复制portion的波浪线属性
            clearWavyUnderlineFromPortion(newPor);
            
            let index = 0;
            contents.forEach((textElement) => {
                let base: any;
                if (textElement.isParaEnd()) {
                    return;
                }

                if (textElement.isImage()) {
                    return;
                    base = textElement.copy(true);
                    this.drawObj.addInsertGraphicObject(base);
                } else {
                    base = new ParaText(textElement.content);
                }
                newPor.addToContent(index++, base);
            });
            if (newPor.content.length === 0) {
                return;
            }
            newPor.setProperty(new TextProperty(this.defaultTextProp));
            
            // 清除复制portion的波浪线属性（在重新设置TextProperty后）
            clearWavyUnderlineFromPortion(newPor);
            
            newPara.addToContent(curIndex++, newPor);
        });
        newPara.paraProperty = this.defaultParaProps && this.defaultParaProps.copy();
        return newPara;
    }

    // 重新拷贝一份段落数据
    private getParagraph(item: DocumentContentElementBase, root: any): DocumentContentElementBase {
        const para = item as Paragraph;
        const newPara = new Paragraph(root, this.doc);
        newPara.paraProperty = para.paraProperty.copy();
        const len = para.content.length - 1;
        const structNames: string[] = this.structNames;
        if (this.drawObj === undefined) {
            this.drawObj = this.doc.getDrawingObjects();
        }
        let bPlaceholder = false;
        const newControl = this.struct.getCurrentNewControl();
        const bRemoveSoftLine = !(!newControl || NewControlType.TextBox === newControl.getType()
                        || NewControlType.Section === newControl.getType());
        para.content.forEach((por, porIndex) => {
            if (porIndex === len && por.content.length === 1) {
                return;
            }

            if (bRemoveSoftLine) {
                por.removeSoftLine();
            }
            let newPor = new ParaPortion(newPara);
            
            newPor.setProperty(por.textProperty.copy());

            // 清除复制portion的波浪线属性（在重新设置TextProperty后）
            clearWavyUnderlineFromPortion(newPor);
            
            let bBorderPortion: boolean = false;
            let bFirst: boolean;
            por.content.forEach((data, dataIndex) => {
                if (bBorderPortion || data.type === ParaElementType.ParaEnd) {
                    return;
                }

                if (data.type === ParaElementType.ParaNewControlBorder) {
                    this.addPortion(newPara, newPor);
                    const border = data as any;
                    bBorderPortion = true;
                    // const endBorder = structs[border.name];
                    // if (endBorder === null) {
                    //     // todo:
                    // } else if (endBorder !== undefined) {
                    //     newPara.addToContent(newPara.content.length - 1, endBorder);
                    // } else {
                    //     border.prevIndex = prevIndex;
                    //     structs[border.name] = border.createNewControlBorder(newPara, this.doc);
                    //     this.prevIndex = prevIndex = border.prevIndex;
                    // }
                    let props;
                    props = this.getNewControlProps(border.getNewControlName());
                    if (!props) {
                        return;
                    }
                    props.bPlaceholder = por.bPlaceHolder;
                    let name: string;
                    if (structNames.length > 0) {
                        name = structNames[structNames.length - 1];
                    }
                    const activeName = border.getNewControlName();
                    if (border.bStart === true) {
                        this.struct.setParentName(name);
                        structNames.push(activeName);
                    } else {
                        if (name === activeName) {
                            structNames.pop();
                        }
                    }
                    const res = this.struct.insertStruct(props, newPara, por);
                    if (border.bStart === true) {
                        if (res.isPlaceholder === true) {
                            bPlaceholder = true;
                        }
                    } else {
                        bPlaceholder = false;
                    }

                    newPor = new ParaPortion(newPara);
                    
                    newPor.setProperty(por.textProperty.copy());

                    // 清除复制portion的波浪线属性（在重新设置TextProperty后）
                    clearWavyUnderlineFromPortion(newPor);
                    return;
                }
                if (bPlaceholder === true) {
                    if (bFirst !== false) {
                        bFirst = false;
                        const name = structNames[structNames.length - 1];
                        this.struct.setPortionTextProp(name, 'placeholder', por.textProperty);

                        const border = data as any;
                        if (border.type === ParaElementType.ParaNewControlBorder &&
                            !border.bStart && name === data.getNewControlName()) {
                            structNames.pop();
                        }
                    }
                    return;
                }
                const base = (data as any).copy(false, this.doc);
                if (base instanceof ParaDrawing) {
                    this.drawObj.addInsertGraphicObject(base);
                }

                if (base.isButton()) {
                    let buttonManager = this.buttonManager;
                    if (!buttonManager) {
                        buttonManager = this.buttonManager = this.doc.getButtonManager();
                    }
                    base.name = buttonManager.makeUniqueName(base.name, this.buttons);
                    buttonManager.addCache(base);
                }

                newPor.addToContent(dataIndex, base, false, true);
                newPor.portionContentPos = newPor.content.length;
            });
            if (bFirst === false && bPlaceholder === true) {
                bPlaceholder = false;
            }
            this.addPortion(newPara, newPor);
            // newPara.addToContent(para.content.length - 1, newPor);
        });
        newPara.paraProperty = item.getParagraphProperty();
        return newPara;
    }

    private getNewControlProps(name: string): any {
        if (!name || !newControlPropertys || newControlPropertys.length === 0) {
            return;
        }

        const props = newControlPropertys.find((prop) => prop.newControlName === name);
        if (!props) {
            return;
        }

        let obj: INewControlProperty = {...props};
        delete obj.newControlItems;
        obj = JSON.parse(JSON.stringify(obj));
        if (props.newControlItems?.length) {
            obj.newControlItems = props.newControlItems.map((item) => item.copy());
        }
        return obj;

    }

    private addPortion(para: Paragraph, portion: ParaPortion): void {
        if (portion.content.length > 0) {
            para.addToContent(para.content.length - 1, portion);
        }
    }

    private getContents(): void {
        // let item = this.selection;
        // console.log(this.selections);
        const copyContents = [];
        // console.log(this.selections);
        this.selections.forEach((item) => {
            let text = '';
            const type = item.getType();
            if (type === DocumentContentType.Table) {
                text = 'table';
            } else if (type === DocumentContentType.Region) {
                text += this.getRegionText(item);
            } else {
                text += this.getParaText(item);
            }

            const copyContent = new CopyContent();
            copyContent.id = item.id;
            copyContent.content = text;
            copyContents.push(copyContent);
        });

        this.copyContent = copyContents;
    }

    private getRegionText(item: any): string {
        let text = '';
        const content = item.getContent();
        content.forEach((data, index) => {
            const type = data.getType();
            switch (type) {
                case DocumentContentType.Region: {
                    return text += this.getRegionText(data);
                }
                case DocumentContentType.Paragraph: {
                    return text += this.getParaText(data);
                }
            }
        });
        return text;
    }

    private getParaText(item: any): string {
        let text = '';
        const content = item.getContent();
        const lastIndex = content.length - 1;
        content.forEach((por, index) => {
            if (lastIndex === index) {
                return;
            }
            por.content.forEach((textData) => {
                text += textData.content;
            });
        });

        return text;
    }

    // svg 内容进行复制保存
    private saveSvgContent(): boolean {
        this.getContents();
        // console.log(copys)
        this.source = CopySource.CopyOwnerEditor;
        return true;
    }
    // html页面内容复制保存
    private saveHtmlContent(): boolean {
        const sel = window.getSelection()
                        .toString();
        if (!sel) {
            return false;
        }
        this.source = CopySource.CopySystem;
        const copy = new CopyContent();
        copy.content = sel;
        this.copyContent = [];
        this.copyContent.push(copy);
        return true;
    }

    private getSelectionContent(): boolean {
        if (this.saveSvgContent()) {
            return true;
        }

        if (this.saveHtmlContent()) {
            return true;
        }

        return false;
    }

    private initContent(): string {
        if (!this.getSelectionContent()) {
            return;
        }

        if (this.source === CopySource.CopySystem) {
            return;
        }
    }

}
