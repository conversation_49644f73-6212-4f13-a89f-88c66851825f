import { ParaElementBase } from './ParaElementBase';
import TextProperty from '../TextProperty';
import { ParaElementType, ParagraphContentPos } from './ParagraphContent';
import { measure, idCounterImage } from '../util';
import DocumentContentBase from '../DocumentContentBase';
import { NewControlDefaultSetting, DocumentSectionType } from '../../../common/commonDefines';
import { DocumentContent } from '../DocumentContent';

export class ParaNewControlBorder extends ParaElementBase {
    public id: number;
    public height: number;
    public name: string;
    public bStart: boolean;

    public paraIndex: number;
    // public paraId: number;
    // public tableIndex: number;
    public portionId: number;

    public bPopWindowNewControl: boolean;
    public remainWidth: number;

    constructor(newControlName: string, bStart: boolean, bPopWindowNewControl: boolean,
                bShowBorder: boolean, bDefaultBorder?: boolean) {
        super();

        this.id = idCounterImage.getNewId();
        this.bStart = bStart;

        const startBorder = bDefaultBorder ? NewControlDefaultSetting.DefaultNewControlStartBorder
                                        : NewControlDefaultSetting.NewControlSectionStartBorder;
        const endBorder = bDefaultBorder ? NewControlDefaultSetting.DefaultNewControlEndBorder
                                        : NewControlDefaultSetting.NewControlSectionEndBorder;
        this.content = true === bStart ? startBorder : endBorder; // '\u300E' : '\u300F'; // '\u259b' : '\u259F'; //
        this.name = true === bStart ? newControlName + '_start' : newControlName + '_end';
        this.type = ParaElementType.ParaNewControlBorder;

        this.paraIndex = -1;
        // this.tableIndex = -1;
        this.portionId = -1;
        // this.width = 2; // 2px
        this.bVisible = bShowBorder;
        this.bPopWindowNewControl = bPopWindowNewControl;
        this.remainWidth = 0;
    }

    public measure(textPr: TextProperty): number {
        const m = measure(NewControlDefaultSetting.DefaultNewControlStartBorder, textPr)[0];

        if ( true === this.bVisible ) {
            this.width = m.width;
        } else {
            this.width = 1;
        }
        this.widthVisible = this.width;
        this.height = m.height;

        return this.height;
    }

    /**
     * 拷贝函数，默认深拷贝，
     * UI显示拷贝时，只需要拷贝content，widthVisible
     * @param bForUI
     */
    public copy(bFlag: boolean = false): ParaElementBase {
        const newControlBorder = new ParaNewControlBorder(this.getNewControlName(), this.bStart,
                                    this.bPopWindowNewControl, this.bVisible);
        newControlBorder.content = this.content;

        if ( true === bFlag) {
            newControlBorder.name = this.name;
            newControlBorder.positionX = this.positionX;
            newControlBorder.positionY = this.positionY;
            newControlBorder.bVisible = this.bVisible;
        }

        newControlBorder.width = this.width;
        newControlBorder.widthVisible = this.widthVisible;
        newControlBorder.remainWidth = this.remainWidth;
        newControlBorder.bPopWindowNewControl = this.bPopWindowNewControl;
        // 打印专用
        if (this['bTextBorder'] === true) {
            newControlBorder['bTextBorder'] = true;
        }

        return newControlBorder;
    }

    public getBoderName(): string {
        return this.name;
    }

    public getNewControlName(): string {
        const pos = true === this.bStart ? this.name.lastIndexOf('_start') : this.name.lastIndexOf('_end');
        const name = this.name.slice(0, pos);
        return name;
    }

    public setNewControlName( newControlName: string ): void {
        const name = true === this.bStart ? newControlName + '_start' : newControlName + '_end';

        if ( name === this.name ) {
            return ;
        }

        this.name = name;
    }

    /**
     * 获取边框所在位置
     * @param parent 边框所在段落
     * @param bAbsolute 是否为绝对位置。true：获取在从Document---->Paragraph---->Portion----->text
     *                         Document---->Table---->Row--->Cell--->Paragraph---->Portion----->text的索引位置
     *      false: Paragraph---->Portion----->text
     */
    public getBorderPos( parent: DocumentContentBase, bAbsolute: boolean): ParagraphContentPos {
        const pos = new ParagraphContentPos();
        // const paraIndex = doc.getParaIndexById(this.paraId);
        // if ( -1 === paraIndex ) {
        //     return undefined;
        // }

        if ( true === bAbsolute ) {
            const topParent = parent.getTopDocument();

            switch (topParent.getDocumentSectionType()) {
                case DocumentSectionType.Header:
                    pos.add(0);
                    break;
                case DocumentSectionType.Document:
                    pos.add(1);
                    break;
                case DocumentSectionType.Footer:
                    pos.add(2);
                    break;
            }
        }

        const para = parent.content[this.paraIndex];
        if ( null == para ) {
            return undefined;
        }

        const portionIndex = para.getPortionIndexById(this.portionId);

        if ( -1 === portionIndex ) {
            return undefined;
        }

        if ( true === bAbsolute ) {
            if (para.parent instanceof DocumentContent) {
                para.parent.getParentIndexs(pos);
            }
            pos.add(this.paraIndex);
        }

        pos.add(portionIndex);
        true === this.bStart ? pos.add(0) : pos.add(para.getContent()[portionIndex].content.length - 1);

        return pos;
    }

    public isNewControlStartBoder(): boolean {
        return this.bStart;
    }

    public isNewControlEndBoder(): boolean {
        return !this.bStart;
    }

    public setParaIndex( paraIndex: number ): void {
        this.paraIndex = paraIndex;
    }

    public setPortionId( portionId: number ): void {
        this.portionId = portionId;
    }

    public canBeAtBeginOfLine(): boolean {
        return ( 1 === this.width && false === this.bStart ) ? false : true;
    }

    public isPopWinNewControl(): boolean {
        return this.bPopWindowNewControl;
    }

    public getRemainWidth(): number {
        return this.remainWidth;
    }

    public setRemainWidth(width: number): void {
        this.remainWidth = width;
    }

    public isSpaceAfter(): boolean {
        return true;
    }
}
