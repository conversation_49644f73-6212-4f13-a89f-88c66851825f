#!/bin/bash

set -x

sed '/PATH/d' .ci_env | grep -E "CI_COMMIT_REF_SLUG|CI_MAJOR_VERISON|CI_JOB_ID|CI_COMMIT_SHA|CI_REGISTRY_USER|CI_REGISTRY_PASSWORD|HTTPS_PROXY" > .ci_env_ex
cat .ci_env_ex
source .ci_env_ex

sed -i -e "s@http://[^/]*@http://mirrors.aliyun.com/debian@@" /etc/apt/sources.list

apt update && apt install -y gettext jq

npm config set registry https://registry.npmmirror.com
npm config set unsafe-perm true
npm install --save-dev