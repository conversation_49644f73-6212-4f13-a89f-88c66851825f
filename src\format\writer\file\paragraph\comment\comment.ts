import { XmlAttributeComponent, XmlComponent } from '../../xml-components';
import { XmlCommentData } from './commentData';

export interface ICommentAttributesProperties {
    id: string;
    start: string;
}

class CommentAttributes extends XmlAttributeComponent<ICommentAttributesProperties> {
    protected xmlKeys: any = {
        id: 'id',
        start: 'start',
    };
}

export class XmlComment extends XmlComponent {

    constructor(attrs: ICommentAttributesProperties) {
        super('w:comment');
        this.root.push(new CommentAttributes(attrs));
    }


}
