import * as React from 'react';

// import { fabric } from 'fabric';
import { ImageEditorController, ActionType } from './imageEditorController';
import { getPatterns, IPattern } from './Patterns';
// import './ImageEditor.less';

import { getDocumentCoreRecorder } from '../../src/model/DocumentCoreRecordReplay';
import { SelectColor } from './SelectColor';

interface ICanvasProps {
  style?: IPencilStyle;
  originImageBase64?: string;
  json?: string;
  onSave: (json: string, svg: string, dimension: {width: number; height: number}) => void;
  onCancel: () => void;
  onAfterRender?: (json: string) => void;
}

interface ICanvasState {
  color?: string;
  patterns: IPattern[];
  thicknessSizes: number[];
  quickTexts: string[];
  currentPatternIndex?: number;
  currentThicknessSizeIndex?: number;
  currentQuickTextsIndex?: number;
  actionType: ActionType;
}

class ImageEditorCanvas extends React.Component<ICanvasProps, ICanvasState> {

  public state: ICanvasState = {
    color: '#000000',
    patterns: getPatterns(),
    quickTexts: ['我是快捷文本', '我是快捷文本二'],
    thicknessSizes: [1, 2, 3, 4, 5, 6],
    actionType: ActionType.Move
  };

  private controller: ImageEditorController;
  private colorRef: any = React.createRef();

  public boxRef = React.createRef<HTMLDivElement>();

  public async componentDidMount(): Promise<void> {

    draggable(this.boxRef.current);

    this.controller = new ImageEditorController('emr-image-editor-canvas');
    this.controller.onActionTypeChange = (actionType) => {
      this.setState({actionType});
    };
    this.controller.onAfterRender = () => {
      if (!!this.props.onAfterRender) {
        const json = this.controller.exportAsJSON();
        this.props.onAfterRender(json);
      }
    };

    setTimeout(() => {
        const {
            color, size,
        } = this.props.style || {};
        let hasChange = false;
        const style: any = {};
        if (color) {
            this.controller.setCurrentColor(color);
            hasChange = true;
            style.color = color;
        }
        const sizeIndex = this.state.thicknessSizes.indexOf(size);
        if (sizeIndex !== -1) {
            this.controller.setCurrentSize(this.state.thicknessSizes[sizeIndex]);
            style.currentThicknessSizeIndex = sizeIndex;
            hasChange = true;
        }
        if (hasChange) {
            this.setState(style);
        }
    }, 0);

    if (!!this.props.json) {
      this.controller.loadByJSON(this.props.json);
      return;
    }

    if (!!this.props.originImageBase64) {
      this.controller.loadByBase64(this.props.originImageBase64);
      return;
    }

  }

  public componentDidUpdate(prevProps: ICanvasProps): void {

    if (!!this.props.json && this.props.json !== prevProps.json) {
      this.controller.loadByJSON(this.props.json);
      return;
    }
    if (!!this.props.originImageBase64 && this.props.originImageBase64 !== prevProps.originImageBase64) {
      this.controller.loadByBase64(this.props.originImageBase64);
      return;
    }
  }

  public move(): void {
    this.controller.startMove();
  }

  public pencil(): void {
    this.controller.startPencil();
  }

  public rect(): void {
    this.controller.startRect();
  }

  public cicle(): void {
    this.controller.startCicle();
  }

  public line(): void {
    this.controller.startLine();
  }

  public arrow(): void {
    this.controller.startArrow();
  }

  public text(): void {
    this.controller.startText('xxxxx');
  }

  public quickText(index: number): void {
    const text = this.state.quickTexts[index];
    this.controller.startText(text);
    this.setState({currentQuickTextsIndex: index});
  }

  public remove(): void {
    this.controller.deleteActiveObjects();
  }

  public save(): void {
    if (this.props.onSave) {
      const svg = this.controller.exportAsSvg();
      const json = this.controller.exportAsJSON();
      const width = this.controller.canvasWidth;
      const height = this.controller.canvasHeight;
      this.props.onSave(json, svg, {width, height});
    }
  }

  public cancel(): void {
    if (this.props.onCancel) {
      this.props.onCancel();
    }
  }

  public setColor(color: string): void {
    this.controller.setCurrentColor(color);
    this.setState({color});
  }

  public setSize(index: number): void {
    const size = this.state.thicknessSizes[index];
    this.controller.setCurrentSize(size);
    this.setState({currentThicknessSizeIndex: index});
  }

  public setPattern(index: number): void {
    const pattern = this.state.patterns[index];
    this.controller.setCurrentPattern(pattern.pattern);
    this.setState({currentPatternIndex: index});

  }

  public render(): any {
    const patternOptions = this.state.patterns.map((pattern) => {
      return (<span key={pattern.name}>{<img style={{marginRight: '4px', transform: 'translate(0, 2px)'}} src={pattern.source} />}{pattern.name}</span>);
    });
    const quickTextOptions = this.state.quickTexts.map((text, key) => {
      return (<span key={key}>{text}</span>);
    });
    const lineThicknessOptions = this.state.thicknessSizes.map((thickness, key) => {
      return (<span key={key}><span style={{width: '40px', height: `${key+1}px`, marginRight: '8px', display: 'inline-block', background: 'currentColor'}}></span>{thickness}px</span>);
    });

    const isMove = this.state.actionType === ActionType.Move;
    const isPencil = this.state.actionType === ActionType.Pencil;
    const isLine = this.state.actionType === ActionType.Line;
    const isArrow = this.state.actionType === ActionType.Arrow;
    const isCircle = this.state.actionType === ActionType.Circle;
    const isRect = this.state.actionType === ActionType.Rect;
    const isText = this.state.actionType === ActionType.Text;

    const moveClassName = 'move-icon-hz0525' + (isMove ? ' current-action' : '');
    const pencilClassName = 'pencil-icon-hz0525' + (isPencil ? ' current-action' : '');
    const lineClassName = 'line-icon-hz0525' + (isLine ? ' current-action' : '');
    const arrowClassName = 'arrow-icon-hz0525' + (isArrow ? ' current-action' : '');
    const circleClassName = 'circle-icon-hz0525' + (isCircle ? ' current-action' : '');
    const rectClassName = 'rect-icon-hz0525' + (isRect ? ' current-action' : '');
    const textClassName = 'text-icon-hz0525' + (isText ? ' current-action' : '');
    const deleteClassName = 'delete-icon-hz0525';

    return (
      <div className='image-editor-wrap-hz0525'>
        <div className='image-editor-box-hz0525 hz0525-draggable' ref={this.boxRef}>
          <div className='image-editor-header-hz0525 hz0525-draggable'>
            <span>矢量图片编辑</span>
            <span className='close-icon-hz0525' onClick={()=> this.cancel()}/>
          </div>
          <div className='image-editor-tools-hz0525'>
            <Tooltip title='选择' description='选择对象，移动'>
              <button className={moveClassName} onClick={()=> this.move()}/>
            </Tooltip>
            <span className='icon-divider-hz0525'/>
            <Tooltip title='直线' description='绘制直线'>
              <button className={lineClassName} onClick={()=> this.line()}/>
            </Tooltip>
            <Tooltip title='画笔' description='快速自由的绘制'>
              <button className={pencilClassName} onClick={()=> this.pencil()}>
                <span className='pencil-color-point-hz0525' style={{backgroundColor: this.state.color}}/>
              </button>
            </Tooltip>
            <span className='icon-divider-hz0525'/>
            <Tooltip title='矩形' description='矩形框'>
              <button className={rectClassName} onClick={()=> this.rect()}/>
            </Tooltip>
            <Tooltip title='椭圆形' description='椭圆框或圆框'>
              <button className={circleClassName} onClick={()=> this.cicle()}/>
            </Tooltip>
            <span className='icon-divider-hz0525'/>
            <Tooltip title='文本' description='编辑文本'>
              <button className={textClassName} onClick={()=> this.text()}/>
            </Tooltip>
            <Tooltip title='箭头文本' description='带有线条的文本'>
              <button className={arrowClassName} onClick={()=> this.arrow()}/>
            </Tooltip>
            <Tooltip title='删除' description=''>
              <button className={deleteClassName} onClick={()=> this.remove()}/>
            </Tooltip>
            <span className='icon-divider-hz0525'/>
            <Select icon={<Tooltip title='大小' description='改变直线/画笔/矩形/椭圆形的粗细大小'><span className='line-thickness-icon-hz0525'/></Tooltip>} placeholder={'粗细'} currentIndex={this.state.currentThicknessSizeIndex} onIndexChange={(index) => this.setSize(index)} options={lineThicknessOptions} />
            <Tooltip title='颜色' description='改变直线/画笔/矩形/椭圆形/文本的颜色'>
              <div className='image-color'>
                <input className='color-icon-hz0525' readOnly type='color' value={this.state.color} onClick={this.clickColor.bind(this)}/>
                <SelectColor ref={this.colorRef} color={this.state.color} onChange={this.handlerChangeColor}  />
              </div>
              
            </Tooltip>
            <span className='icon-divider-hz0525'/>
            <Select icon={<Tooltip title='填充' description='对封闭型图形进行图样填充'><span className='fill-icon-hz0525'/></Tooltip>} placeholder={'填充'} options={patternOptions} currentIndex={this.state.currentPatternIndex} onIndexChange={(index) => this.setPattern(index)}/>
            <Select icon={<Tooltip title='快捷文本' description='内置常用术语'><span className='quicktext-icon-hz0525'/></Tooltip>} placeholder={'快捷文本'} options={quickTextOptions} currentIndex={this.state.currentQuickTextsIndex} onIndexChange={(index) => this.quickText(index)}/>
          </div>
          <div className='image-editor-canvas-hz0525'>
            <canvas width='800' height='450' id='emr-image-editor-canvas'/>
          </div>
          <div className='image-editor-footer-hz0525'>
            <button className='cancel-button-hz0525' onClick={()=> this.cancel()}>取消</button>
            <button className='submit-button-hz0525' onClick={()=> this.save()}>保存</button>
          </div>
        </div>
      </div>
    );
  }

  private handlerChangeColor = (color: {hex: string, rgb?: object, hsv?: any}): void => {
    this.setColor(color.hex);
  }

  private clickColor = (e: any) => {
    e.preventDefault();
    e.stopPropagation();
    setTimeout(() => {
      this.colorRef.current.setColorVisible(true);
    }, 0);
  }
}

function getImageBase64FromBroswer(): Promise<string> {
  return new Promise((resolve, reject) => {
    const input: HTMLInputElement = document.createElement('input');
    input.style.display = 'none';
    input.type = 'file';
    input.onchange = (e) => {
      const file = input.files[0];
      const reader = new FileReader();
      reader.onerror = reject;
      reader.onloadend = () => resolve(reader.result as string);
      reader.readAsDataURL(file);
      document.body.removeChild(input);
    }
    document.body.appendChild(input);
    input.click();
  });
}
interface IPencilStyle {
    color?: string;
    size?: number;
}

interface Props {
  style?: IPencilStyle;
  editorRef: (editorManger: ImageEditorManger) => void;
}

interface State {
  json: string;
  originImageBase64: string;
  isShow: boolean;
}


export class ImageEditorManger {

  public onsave: (json: string, base64: string, dimension: {width: number; height: number}) => void;

  public onclose: () => void;

  constructor(private readonly editor: ImageEditor) {
  }

  public async create(): Promise<void> {
    const image = await getImageBase64FromBroswer();
    this.editor.openByOriginImage(image);
  }

  public setStyle(style: IPencilStyle): void {
    this.editor.setStyle(style);
  }

  public open(json: string): void {
    this.editor.openByJSON(json);
  }

  public openByOriginImage(base64: string): void {
    this.editor.openByOriginImage(base64);
  }

  public close(): void {
    this.editor.close();
  }

  public __save(json: string, svg: string, dimension: {width: number, height: number}): void {
    if (!!this.onsave) {
      const encodedData = window.btoa(unescape(encodeURIComponent(svg)));
      const base64 = 'data:image/svg+xml;base64,' + encodedData;
      this.onsave(json, base64, dimension);
    }
  }

}

// tslint:disable-next-line: max-classes-per-file
export class ImageEditor extends React.Component<Props, State> {

  public state: State = {
    isShow: false,
    originImageBase64: null,
    json: null
  };

  private manger: ImageEditorManger;

  public componentDidMount(): void {
    const manger = new ImageEditorManger(this);
    this.manger = manger;
    this.props.editorRef(this.manger);

    // 全局变量__IMAGE_EDITOR_REPLAYER__, 用于图片编辑器的回放功能动作实现
    window['__IMAGE_EDITOR_REPLAYER__'] = {
      create: (originImageBase64: string) => {
        this.setState({isShow: true, originImageBase64, json: null});
      },

      open: (json: string) => {
        this.setState({isShow: true, originImageBase64: null, json});
      },

      close: () => {
        this.setState({isShow: false, json: null, originImageBase64: null});
      }

    };
  }

  public componentDidUpdate(prevProps: Props): void {
    if (!!this.props.editorRef && this.props.editorRef !== prevProps.editorRef) {
      this.props.editorRef(this.manger);
    }
  }

  public setStyle(style: IPencilStyle): void {
    
  }

  public openByOriginImage(originImageBase64: string): void {
    this.setState({isShow: true, originImageBase64, json: null});

    // 录播功能：记录, 插入可编辑图片
    const recorder = getDocumentCoreRecorder();
    const action = {
      isImageEditor: true,
      method: 'create',
      timestamp: Date.now(),
      args: [originImageBase64]
    };
    recorder.record(action);
  }

  public openByJSON(json: string): void {
    this.setState({isShow: true, originImageBase64: null, json});

    // 录播功能：记录,打开图片编辑器
    const recorder = getDocumentCoreRecorder();
    const action = {
      isImageEditor: true,
      method: 'open',
      timestamp: Date.now(),
      args: [json]
    };
    recorder.record(action);
  }

  public close(): void {
    this.setState({isShow: false, json: null, originImageBase64: null}, () => {
      if (!!this.manger.onclose) {
        this.manger.onclose();
      }
    });

    // 录播功能：记录,关闭图片编辑器
    const recorder = getDocumentCoreRecorder();
    const action = {
      isImageEditor: true,
      method: 'close',
      timestamp: Date.now(),
      args: []
    };
    recorder.record(action);
  }

  public save(json: string, svg: string, dimension: {height: number; width: number}): void {
    this.manger.__save(json, svg, dimension);
    this.close();
  }

  public onAfterRender(json: string): void {
    // 录播功能：记录,图片编辑器绘图过程
    const recorder = getDocumentCoreRecorder();
    const action = {
      isImageEditor: true,
      method: 'open',
      timestamp: Date.now(),
      args: [json]
    };
    recorder.record(action);
  }

  public render(): any {
    const content = this.state.isShow ?
      <ImageEditorCanvas style={this.props.style} json={this.state.json} onAfterRender={(json) => this.onAfterRender(json)}
        originImageBase64={this.state.originImageBase64} onSave={(json, svg, dimension) => this.save(json, svg, dimension)} onCancel={()=> this.close()}/> : null;
    return (
      <div>
        {content}
      </div>
    );
  }
}

interface ISelectProps {
  icon?: any;
  placeholder?: string;
  options: any[];
  onIndexChange?: (index: number) => void;
  currentIndex?: number;
}

interface ISelectState {
  isOpen: boolean;
}

// tslint:disable-next-line: max-classes-per-file
class Select extends React.Component<ISelectProps, ISelectState> {

  private hostRef = React.createRef<HTMLDivElement>();

  public state: ISelectState = {
    isOpen: false
  };

  public componentDidMount(): void {
    document.addEventListener('click', this.outterClickHandler, false);
  }

  public componentWillUnmount(): void {
    document?.removeEventListener('click', this.outterClickHandler, false);
  }

  public toggleOpen(): void {
    this.setState({isOpen: !this.state.isOpen});
  }

  public changeIndex(index: number): void {
    if (!!this.props.onIndexChange) {
      this.props.onIndexChange(index);
    }
  }

  public render(): any {
    const  { options, icon, placeholder, currentIndex } = this.props;
    const { isOpen } = this.state;
    let content = <span className='placeholder-hz0525'>{placeholder}</span>;
    if (typeof currentIndex === 'number') {
      content = options[currentIndex];
    }
    const optionElements = options.map((option, key) => (<li className={'select-option-hz0525' + (key === currentIndex ? ' current-option' : '')} key={key} onClick={() => this.changeIndex(key)}>{option}</li>));
    return (
    <div className='select-hz0525' onClick={() => this.toggleOpen()} ref={this.hostRef}>
      <span className='select-icon-hz0525'>{icon}</span>
      <span className='select-content-hz0525'>
        <span className='select-text-hz0525'>{content}</span>
        <ul className='select-list-hz0525' style={{display: isOpen ? 'block': 'none'}}>{optionElements}</ul>
      </span>
    </div>
    );
  }

  private outterClickHandler = (e: MouseEvent) => {
    if (!this.hostRef.current.contains(e.target as HTMLElement)) {
      this.setState({isOpen: false});
    }
  }
}

interface ITooltipProps {
  title: string;
  description: string;
  children: any;
}

function Tooltip(props: ITooltipProps) {
  const { title, children, description } = props;
  return (
    <div className='tooltip-hz0525'>
      {children}
      <div className='tooltip-box-hz0525'>
        <p className='tooltip-title-hz0525'>{title}</p>
        <p className='tooltip-description-hz0525'>{description}</p>
      </div>
    </div>
  );
}

interface IDragPaylaod {
  left: number;
  top: number;
  x: number;
  y: number;
}
function draggable(ele: HTMLElement): void {
  let payload: IDragPaylaod;
  const draggableSection = ele.querySelector('.hz0525-draggable');
  const dragStart = (e: MouseEvent) => {
    if (e.target !== draggableSection) {
      return;
    }
    const {left, top} = ele.getBoundingClientRect();
    // e.clientX
    payload = {
      left, top, x: e.clientX, y: e.clientY
    };
  };

  const dragMove = (e: MouseEvent) => {
    const isDragging = !!payload;
    if (isDragging) {
      const { left, top, x, y} = payload;
      const moveX = e.clientX - x;
      const moveY = e.clientY - y;
      const currentLeft = left + moveX;
      const currentTop = top + moveY;
      ele.style.left = `${currentLeft}px`;
      ele.style.top = `${currentTop}px`;
    }
  };

  const dragEnd = (e: MouseEvent) => {
    payload = null;
  };

  // 初始居中
  {
    const {height, width} = ele.getBoundingClientRect();
    const clientWidth = window.innerWidth || document.documentElement.clientWidth;
    const clientHeight = window.innerHeight || document.documentElement.clientHeight;
    const left = (clientWidth - width) / 2;
    const top = (clientHeight - height) / 2;
    ele.style.position = 'absolute';
    ele.style.left = `${left}px`;
    ele.style.top = `${top}px`;
  }

  ele.addEventListener('mousedown', dragStart, false);
  ele.addEventListener('mousemove', dragMove, false);
  ele.addEventListener('mouseup', dragEnd, false);
}
