import * as React from 'react';
import '../style/radiobox.less';

interface IState {
    bRefresh: boolean;
}

interface IProps {
    onChange?: (value: any, name: string, e?: any, bRadioIndex?: number) => void;
    value: boolean;
    disabled?: boolean;
    readonly?: boolean;
    name?: string;
    index?: number; // static index when traversing map
    children?: React.ReactNode; // 添加 children 属性
    // curIndex?: number; // currently selected index
}

export default class RadioboxItem extends React.Component<IProps, IState> {
    private value: boolean;
    private input: any;
    constructor(props: any) {
        super(props);
        this.state = {
            bRefresh: false,
        };
        this.value = this.props.value;
        this.input = React.createRef();
    }

    public render(): any {
        const props = this.props;
        // console.log(props.index, props.curIndex)
        let className: string = '';
        // if (props.index !== props.curIndex) {
        //     this.value = false;
        // }
        // console.log(this.value)
        if (this.value === true) {
            className = 'radiobox-icon-checked';
        }

        let className1 = 'radiobox-item';
        if (props.disabled === true) {
            className1 += ' disabled';
        }
        return (
            <div className='editor-radiobox'>
                <span className={className1}>
                    <input type='radio' name='radio-listbox' ref={this.input} tabIndex={-1} />
                    <i className={className} />
                    <label className='radio-label'>{props.children}</label>
                </span>
            </div>
        );
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        // console.log(nextProps.value)
        this.value = nextProps.value;
    }

    public componentDidMount(): void {
        // console.log('didmount')
        this.input.current.addEventListener('change', this.onChange);
    }

    public componentWillUnmount(): void {
        // console.log('unmount')
        this.input.current?.removeEventListener('change', this.onChange);
    }

    private onChange = (e: any): void => {
        this.value = !this.value;
        // console.log(this.value)
        // console.log(this.props.name)
        // console.log(this.props.index)
        const onChange = this.props.onChange;
        if (typeof onChange === 'function') {
            onChange(this.value, this.props.name, e, this.props.index);
        }

        if (this.input.current) {
            this.setState({bRefresh: !this.state.bRefresh});
        }
    }
}
