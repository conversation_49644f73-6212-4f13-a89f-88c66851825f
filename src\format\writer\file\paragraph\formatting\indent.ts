// http://officeopenxml.com/WPindentation.php
import { XmlAttributeComponent, XmlComponent } from '../../xml-components';

export interface IIndentAttributesProperties {
    readonly left?: number;
    readonly right?: number;
    readonly hanging?: number;
    readonly firstLine?: number;
    // readonly start?: number;
    // readonly end?: number;
}

class IndentAttributes extends XmlAttributeComponent<IIndentAttributesProperties> {
    protected readonly xmlKeys: any = {
        left: 'w:left',
        right: 'w:right',
        hanging: 'w:hanging',
        firstLine: 'w:firstLine',
    };
}

export class Indent extends XmlComponent {
    constructor(attrs: IIndentAttributesProperties) {
        super('w:ind');
        this.root.push(new IndentAttributes(attrs));
    }
}
