import { measure } from 'src/model/core/util';
import DocumentFrameBounds from './FrameBounds';
import Document, { RecalcResultType } from './Document';
import ParaLine, { ParaLineInfo, IRanges } from './Paragraph/ParaLine';
import DocumentContentElementBase from './DocumentContentElementBase';
import ParaPortion from './Paragraph/ParaPortion';
import {DocumentContentType, AlignType, CursorType} from './Style';
import {SectionProperty} from './SectionProperty';
import ParaPage from './Paragraph/ParaPage';
import {IParagraphSearchPosXY, ICurrentCursorPos, IParagraphSearchPos} from './Paragraph/ParagraphSearchPosXY';
import {ParagraphContentPos, IParaPos, ParaElementType, ParaTextName} from './Paragraph/ParagraphContent';
import TextProperty, { ITextProperty } from './TextProperty';
import ParaProperty from './Paragraph/ParaProperty';
import { MouseEventType, IMouseEvent } from '../../common/MouseEventHandler';
import { ISelectionsPara , IDrawSelectionsLine } from '../DocumentCore';
import {ParaTextProperty} from './Paragraph/ParaTextProperty';
import ParagraphRecalculate from './Paragraph/ParagraphRecalculate';
import {ParaElementBase} from './Paragraph/ParaElementBase';
import {getMMFromPx, getAcitveNodeIndex, getPxForMM} from './util';
import ContentChanges, { ContentChangesElement } from './ContentChanges';
import { ChangeParagraphAlign, ChangeParagraphPropertyChange, ChangeParagraphLineSpacing,
    ChangeParagraphLineSpacingRule, ChangeParagraphIndFisrt, ChangeParagraphIndLeft,
    ChangeParagraphIndRight, ChangeParagraphRemoveItem, ChangeParagraphAddItem,
    ChangeParagraphPageBreak,
    ChangeParagraphWordWrap,
    ChangeParagraphHidden} from './Paragraph/ParagraphChange';
import ParaIndentation from './Paragraph/ParaIndentation';
import { ParagraphState } from './HistoryState';
import { ICheckSelectionNewControls, ParagraphSelection, SelectionFlagType } from './Selection';
// import {parse as circularParse, stringify as circularStringify} from 'flatted/esm';
import DocumentContentBase from './DocumentContentBase';
import { INewControlProperty, NewControlContentSecretType,
    LineSpacingType, LineSpacingRatio, ResultType, ReviewType,
    RevisionChangeType, IRevisionChange,
    DocumentSectionType, NewControlType, NEWCTR_BTN_MAP,
    NeedStructType,
    CleanModeType,
    NewControlDefaultSetting,
    IParaNumPr,
    VertAlignType} from '../../common/commonDefines';
import ParaDrawing from './Paragraph/ParaDrawing';
import { NewControl } from './NewControl/NewControl';
import { NewControlSignature } from './NewControl/NewControlSignature';
import { NewControlText, NewControlSection } from './NewControl/NewControlText';
import { ChangeContent } from './HistoryChange';
import { IParaProperty } from '../ParaProperty';
import {Comment} from './Comment/Comment';
// import { HistoryDescriptionType } from './HistoryDescription';
import History, { IOperateResult } from './History';
import { ParagraphRevisionChangesChecker } from './Paragraph/ParagraphRevisionChangesChecker';
import PortionRecalculateObject from './Paragraph/PortionRecalculateObject';
import { Region } from './Region';
import { DocumentContent } from './DocumentContent';
// import { WasmInstance } from '../../common/WasmInstance';
import { ReviewInfo } from './Revision';
import { message } from '../../common/Message';
import { WasmInstance } from '../../common/WasmInstance';
import { ParaNewControlBorder } from './Paragraph/ParaNewControlBorder';
import { getDefaultFont } from '../../common/commonMethods';
import { ParaNumbering } from './Paragraph/ParaNumbering';
import { getTheme } from '@hz-editor/theme';
import { HistroyItemType } from './HistoryDescription';

/**
 * 当前光标
 */
export class ParaCurPos {
    public x: number;
    public y: number;
    public contentPos: number;
    public line: number;
    public range: number;
    public pagePos: number; // ParaPage
    public realX: number;
    public realY: number;

    constructor() {
        this.x = 0;
        this.y = 0;
        this.contentPos = 0;
        this.line = 0;
        this.range = 0;
        this.pagePos = 0;
        this.realX = 0;
        this.realY = 0;
    }
}

/**
 * 段落
 */
export default class Paragraph extends DocumentContentElementBase {
    public content: ParaPortion[] = [];
    public pages: ParaPage[] = [];
    public bounds: DocumentFrameBounds = new DocumentFrameBounds(0, 0, 0, 0);
    public lines: ParaLine[] = [];
    public curPos: ParaCurPos;

    public paraEnd: any  =
        {
            Line  : 0,
            Range : 0,
        };

    // 段落末尾文本属性，用于属性继承
    public textPr: ParaTextProperty = null;

    public paraProperty: ParaProperty = new ParaProperty();
    public sectProperty: SectionProperty = undefined;

    public selection: ParagraphSelection; // 段落选择

    public paraRecal: ParagraphRecalculate; // 段落排版实现
    // history: History;

    public contentChanges: ContentChanges;

    public bReadFile: boolean;

    // public logicDocument: Document;
    private bHidden: boolean;
    private numbering: ParaNumbering;

    // todo 将来是包含了属性的文档树，暂时用纯文本代替
    constructor( parent: DocumentContentBase, document: Document, bFromTable?: boolean) {
        super(parent);
        this.curPos = new ParaCurPos();
        this.selection = new ParagraphSelection();
        this.paraRecal = new ParagraphRecalculate(this);
        // gHistory = parent.getHistory();
        this.contentChanges = new ContentChanges();
        const doc = (document || ( parent ? parent.getDocument() : null));
        this.textPr = new ParaTextProperty(getDefaultFont(doc));

        // 用户新建段落：插入空的portion，用于存入内容
        // this.content[0] = new ParaPortion(this);

        const endPortion = new ParaPortion(this, null, null, bFromTable);
        endPortion.addEndInternal();
        this.content[0] = endPortion;

        // ParaEnd：始终在一个单独的portion，使其portion的属性不受段落内文本属性改变的影响
        // this.addParaEnd(this.content.length);

        this.logicDocument = document;
        this.bHidden = false;
        this.bReadFile = false;
    }

    public addChildrenToContent(items: ParaPortion[], curPos?: number, addAfter?: boolean): number {
        if (!items || !items.length) {
            return ResultType.UnEdited;
        }
        if (curPos === undefined) {
            curPos = this.curPos.contentPos;
        }
        if (addAfter) {
            curPos++;
        }
        for (const item of items) {
            this.addToContent(curPos++, item);
        }
        this.curPos.contentPos = curPos - 1;
        // 首部
        const portion = this.content[curPos];
        const pos = new ParagraphContentPos();
        pos.update2(portion.content.length, 0);
        portion.setContentPos(pos);
    }

    /**
     * 读取
     * @param nPos
     * @param item
     */
    public addToContent( nPos: number, item: ParaPortion ): number {
        if ( null == item ) {
            return ResultType.UnEdited;
        }

        const history = this.getHistory();
        if ( history && history.canAdd() ) {
            history.addChange(new ChangeParagraphAddItem(this, nPos, [item]));
        }

        this.content.splice(nPos, 0, item);
        this.updateTrackRevisions();

        const length = this.content.length;
        // 移动当前光标
        if (this.curPos.contentPos >= nPos) {
            this.curPos.contentPos++;

            if (this.curPos.contentPos >= length) {
                this.curPos.contentPos = length - 1;
            }
        }

        if (this.selection.bUse) {
            if ( this.selection.startPos >= nPos ) {
                this.selection.startPos++;

                if ( this.selection.startPos >= length ) {
                    this.selection.startPos = length - 1;
                }
            }

            if ( this.selection.endPos >= nPos ) {
                this.selection.endPos++;

                if ( this.selection.endPos >= length ) {
                    this.selection.endPos = length - 1;
                }
            }
        }

        // 将portion关联至此para
        item.setParagragh(this);

        return ResultType.Success;
    }

    /**
     * 当前单元格内容进行填充
     * @param text 需要填充的内容
     * @param textProp 样式
     * @returns 是否需要进行排版
     */
    public setText(text: string, textProp?: TextProperty): boolean {
        this.content.splice(0, this.content.length - 1);
        const portion = new ParaPortion(this);
        if (textProp != null) {
            portion.textProperty = textProp;
        }
        portion.addText(text);
        this.addToContent(0, portion);
        if (this.pages.length && this.isVisibleAtCurCell(portion, text)) {
            // 只排版当前段落
            this.recalculatePage(0);
            return false;
        }

        return true;
    }

    public canRemoveAtCurCell(option : {width: number, height: number, bMerge: boolean, bLast: boolean, lineHeight: number}, direction?: number): {width: number, height: number, lineHeight: number} {
        const selection = this.selection;
        const contents = this.content;
        let maxHeight: number = 0;
        let width: number = 0;
        let lineHeight = 0;
        const maxWidth = this.xLimit - this.x;
        const myObj: any = {width, lineHeight, maxHeight, maxWidth};
        if (selection.bUse) {
            let start = selection.startPos;
            let end = selection.endPos;
            if (start > end) {
                const tem = start;
                start = end;
                end = tem;
            }

            if (option.lineHeight) {
                maxHeight = option.height;
                width = option.width;
                lineHeight = option.lineHeight;
            }

            let lastPor: ParaPortion;
            // const obj: any = {width, lineHeight, maxHeight, maxWidth};
            
            // let startIndex: number = 0;
            // for (let len = contents.length; startIndex )
            contents.forEach((portion, index) => {
                if (index > start && index < end || portion.isHidden()) {
                    return;
                }
                const selection = portion.selection;
                let startPor = selection.startPos;
                let endPor = selection.endPos;
                if (startPor > endPor) {
                    const pos = startPor;
                    startPor = endPor;
                    endPor = pos;
                }
                const myCallback = (textVm, textIndex) => {if (textIndex >= startPor && textIndex < endPor) {
                    return true;
                }};
                myObj.portion = portion;
                if (this.measureEachTextToCell(myObj, myCallback)) {
                    lastPor = portion;
                }
                // let curTextIndex = 0;
                // const texts = portion.content;
                // for (let curTextLen = texts.length; curTextIndex < curTextLen; curTextIndex++) {

                // }
                // portion.content.forEach((textVm, textIndex) => {
                //     if (textIndex >= startPor && textIndex < endPor) {
                //         return;
                //     }
                //     if (textVm.getType() === ParaElementType.ParaNewLine) {
                //         portion.recalculateMeasureContent(lineHeight, this.paraProperty);
                //         maxHeight += portion.ascent + lineHeight + portion.descent;
                //         lineHeight = 0;
                //         width = 0;
                //     }

                //     if (!textVm.content || !textVm.isVisible() || !textVm.widthVisible) {
                //         return;
                //     }

                //     lastPor = portion;
                //     const rect = measure(textVm.content, portion.textProperty);
                //     if (width + textVm.widthVisible > maxWidth) {
                //         portion.recalculateMeasureContent(lineHeight, this.paraProperty);
                //         maxHeight += portion.ascent + lineHeight + portion.descent;
                //         width = textVm.widthVisible;
                //         lineHeight = rect[0].height;
                //     } else {
                //         const height = rect[0].height;
                //         width += textVm.widthVisible;
                //         if (lineHeight < height) {
                //             lineHeight = height;
                //         }
                //     }
                    
                // });

            });
    
            lineHeight = myObj.lineHeight;
            maxHeight = myObj.maxHeight;
            if ((!option.bMerge || option.bLast) && lineHeight > 0 && lastPor) {
                lastPor.recalculateMeasureContent(lineHeight, this.paraProperty);
                maxHeight += lastPor.ascent + lineHeight + lastPor.descent;
            }

            return {height: maxHeight, width, lineHeight};
        }

        const elem = this.getRemoveElement(direction);
        if (!elem) {
            return;
        }
        let curPortion: ParaPortion;
        
        const myCallback = (textVm, index) => {if (textVm === elem) {return true;}};
        for (let curIndex = 0, len = contents.length; curIndex < len; curIndex++) {
            const portion = contents[curIndex];
            if (portion.isHidden()) {
                continue;
            }

            myObj.portion = portion;
            if (this.measureEachTextToCell(myObj, myCallback)) {
                curPortion = portion;
            }
        }
        lineHeight = myObj.lineHeight;
        maxHeight = myObj.maxHeight;
        if (!curPortion) {
            curPortion = new ParaPortion(this);
            curPortion.textProperty = contents[0].textProperty;
            curPortion.addText(' ');
            const rect = measure(' ', curPortion.textProperty);
            lineHeight = rect[0].height;
        }

        if (lineHeight > 0) {
            curPortion.recalculateMeasureContent(lineHeight, this.paraProperty);
            maxHeight += curPortion.ascent + lineHeight + curPortion.descent;
        }

        return {height: maxHeight, width, lineHeight};
    }

    /**
     * 测量当前段落高度
     * @param maxWidth 
     * @returns 高度
     */
    public measureParaHeight(maxWidth: number = 0): number {
        let width = 0;
        let maxHeight = 0;
        // 新增行不一定有最大宽度
        maxWidth = (this.xLimit - this.x) || maxWidth;
        let lineHeight = 0;
        const obj: any = {width, maxHeight, lineHeight, maxWidth};
        let lastPor: ParaPortion;
        this.content.forEach((portion) => {
            if (portion.isHidden()) {
                return;
            }
            obj.portion = portion;
            if (this.measureEachTextToCell(obj, null)) {
                lastPor = portion;
            }
        });
        if (lastPor) {
            lineHeight = obj.lineHeight;
            maxHeight = obj.maxHeight;
        } else { // 假如为一个空段落，比如新增行进来的
            lastPor = new ParaPortion(this);
            lastPor.textProperty = this.content[0].textProperty;
            lastPor.addText(' ');
            const rect = measure(' ', lastPor.textProperty);
            lineHeight = rect[0].height;
        }
        
        lastPor.recalculateMeasureContent(lineHeight, this.paraProperty);
        maxHeight += lastPor.ascent + lineHeight + lastPor.descent;

        return maxHeight;
    }

    

    /**
     * 当前输入，是否只需要刷新当前单元格或者段落即可
     * @param portion 当前输入的portion
     * @param text 输入的文本字符串
     * @returns 单元格、段落、undefined
     */
    public canRecalcCurCell(portion: ParaPortion, text: string, callback?: (para: any) => number, bPara?: boolean): any {
        // 当前段落在表格内
        if (!this.isTableCellContent()) {
            return;
        }
        const parent = this.parent as any;
        // 当前单元格不跨页
        if (parent.pages.length !== 1) {
            return;
        }
        const cell: any = parent.parent;
        // 只在护理表格起作用
        if (!cell.row.table.isNISTable()) {
            return;
        }

        // let width: number = 0;
        // const maxWidth = this.xLimit - this.x;
        // let maxHeight = 0;
        
        // // const pos = portion.portionContentPos - text.length;
        // let lineHeight: number = 0;
        // let curPor: ParaPortion;

        // const obj: any = {width, maxHeight, lineHeight, maxWidth};
        // // 下面获取所有行高度
        // this.content.forEach((por) => {
        //     if (por.isHidden()) {
        //         return;
        //     }
        //     obj.portion = por;
        //     if (this.measureEachTextToCell(obj, null)) {
        //         curPor = por;
        //     }
        //     // curPor = por;
        //     // por.content.forEach((textVm) => {
        //     //     const rects = measure(textVm.content, portion.textProperty);
        //     //     const curText = rects[0];
        //     //     const height: number = curText.height;
        //     //     if (maxHeight < height) {
        //     //         maxHeight = height;
        //     //     }
        //     //     const curX: number = textVm.widthVisible || curText.width;
        //     //     x += curX;
        //     //     if (x >= maxX) { // 满一行后，换下一行
        //     //         por.recalculateMeasureContent(maxHeight, this.paraProperty);
        //     //         curHeight += por.ascent + maxHeight + por.descent;
        //     //         x = curX;
        //     //         maxHeight = height;
        //     //     }
        //     // });
        // });
        // if (!curPor) {
        //     return;
        // }

        // lineHeight = obj.lineHeight;
        // maxHeight = obj.maxHeight;
        // curPor.recalculateMeasureContent(lineHeight, this.paraProperty);
        // maxHeight += curPor.ascent + lineHeight + curPor.descent;
        let maxHeight = this.measureParaHeight();
        const curLines = this.lines;
        if (bPara !== true && curLines.length) {
            const startLine = curLines[0];
            const endLine = curLines[curLines.length - 1];
            const curParaHeight = endLine.bottom - startLine.top;
            if (curParaHeight + 0.01 > maxHeight) { // 当前段落内容高度没有任何变化那么只需要排版当前段落即可
                return this;
            }
        }
        
        const paras = cell.getContent();
        paras.forEach((content: Paragraph) => {
            if (content === this) {
                return;
            }
            if (callback) {
                const res = callback(content);
                if (res !== undefined) {
                    maxHeight += res;
                    return;
                }
            }
            const lines = content.lines;
            if (!lines.length) {
                return;
            }
            const startLine = lines[0];
            const endline = lines[lines.length - 1];
            maxHeight += endline.bottom - startLine.top;
        });

        const bounds = cell.oldSelectionBounds;
        if (bounds?.length === 1) {
            const line = bounds[0];
            if (maxHeight > line.height) {
                return;
            }
            // 内容高度比原来的小
            if (line.contentHeight > maxHeight) {
                const maxContentHeight = cell.getMaxHeight();
                if (maxContentHeight > maxHeight + 0.01) {
                    return cell;
                }
            } else if (line.height > maxHeight) { // 内容比以前大，但是还在当前边框可视范围内
                return cell;
            }
        }

        return;
    }

    public add( paraItem: ParaElementBase ): number {
        let result: number = ResultType.Success;
        switch ( paraItem.type ) {
            case ParaElementType.ParaText:
            case ParaElementType.ParaSpace:
            case ParaElementType.ParaTab:
            case ParaElementType.ParaDrawing:
            case ParaElementType.ParaMedEquation:
            case ParaElementType.ParaBarcode:
            case ParaElementType.ParaQRCode:
            case ParaElementType.ParaPageNum:
            case ParaElementType.ParaNewLine: {
                const curPortion = this.content[this.curPos.contentPos];
                curPortion.add(curPortion.portionContentPos, paraItem, true);
                // curPortion.addToContent(curPortion.portionContentPos, paraItem, true);
                break;
            }

            case ParaElementType.ParaTextPr: {
                const paraTextProperty = paraItem as ParaTextProperty;
                const textPr = paraTextProperty.textProperty;
                const wavyUnderlineId = paraTextProperty.wavyUnderlineId;
                const bEmptyPara = this.isEmpty();

                // 如果段落为空，即：只有一个段落结束符，则改变段落属性和当前portion的属性
                // key-wasm by tinyzhi
                // if ( true === bEmptyPara) {
                //     // ？？？段落结束符设置文本属性会在UI上展示，容易被指示为bug
                //     if ( 1 <= this.content.length ) {
                //         result = this.content[this.curPos.contentPos].applyProperty(textPr);
                //         this.paraProperty.addPropertyChange();
                //     }
                //     break;
                // }
                const nResult = WasmInstance.instance._Paragraph_add_Judge(Number(bEmptyPara), this.content.length);
                if ( nResult === 2 ) {
                    result = this.content[this.curPos.contentPos].applyProperty(textPr);
                    // 设置wavyUnderlineId
                    if (wavyUnderlineId) {
                        this.content[this.curPos.contentPos].wavyUnderlineId = wavyUnderlineId;
                    }
                    this.paraProperty.addPropertyChange();
                    break;
                }
                if ( nResult === 1 ) {
                    break;
                }
                if ( nResult === 3) {
                    return;
                }
                // end by tinyzhi

                if (true === this.bApplyToAll) {
                    // let curRes: number = ResultType.UnEdited;
                    // for (let index = 0, length = this.content.length; index < length; index++) {
                    //     const item = this.content[index];
                    //     if (length === 1) {
                    //         const texts = item.content;
                    //         const endTextIndex = texts.length - 1;
                    //         if (endTextIndex > 0 && texts[endTextIndex].type === ParaElementType.ParaEnd) {
                    //             const addPortion = this.splitPortion1(item, index, endTextIndex);
                    //             curRes = this.addToContent(1, addPortion) && curRes;
                    //         }
                    //     }
                    //     const option: {result: number} = {
                    //         result: ResultType.UnEdited as number,
                    //     };
                    //     item.applyTextPr(textPr, true, option);
                    //     curRes = curRes && option.result;
                    // }

                    const curRes = this.applyAllTextProp(textPr);
                    result = this.textPr.applyTextPr(textPr, this.getHistory()) && curRes;
                    
                    // 为所有相关的portion设置wavyUnderlineId
                    if (wavyUnderlineId) {
                        this.setWavyUnderlineIdToAllPortions(wavyUnderlineId);
                    }

                } else {
                    result = this.applyTextPr(textPr);
                    
                    // 为相关的portion设置wavyUnderlineId
                    if (wavyUnderlineId) {
                        this.setWavyUnderlineIdToSelectedPortions(wavyUnderlineId);
                    }
                }
                break;
            }

            case ParaElementType.ParaPortion: {
                break;
            }

            default:
                break;
        }

        this.parent.setDirty();
        return result;
    }

    /**
     * 护理表格设置内容专用
     * @param paraItem
     * @returns
     */
    public add2(paraItem: ParaElementBase, bDirty?: boolean): number {
        let result: number = ResultType.Failure;
        switch ( paraItem.type ) {
            case ParaElementType.ParaText:
            case ParaElementType.ParaSpace:
            case ParaElementType.ParaTab:
            case ParaElementType.ParaDrawing:
            case ParaElementType.ParaMedEquation:
            case ParaElementType.ParaBarcode:
            case ParaElementType.ParaQRCode:
            case ParaElementType.ParaPageNum: {
                const curPortion = this.content[this.curPos.contentPos];
                curPortion.add(curPortion.portionContentPos, paraItem, true);

                result = ResultType.Success;
                break;
            }

            case ParaElementType.ParaTextPr: {
                const paraTextProperty = paraItem as ParaTextProperty;
                const textPr = paraTextProperty.textProperty;
                const wavyUnderlineId = paraTextProperty.wavyUnderlineId;
                const bEmptyPara = (this.isEmpty() && 1 === this.content.length);

                // 如果段落为空，即：只有一个段落结束符，则改变段落属性和当前portion的属性
                // key-wasm by tinyzhi
                // if ( true === bEmptyPara) {
                //     // ？？？段落结束符设置文本属性会在UI上展示，容易被指示为bug
                //     if ( 1 <= this.content.length ) {
                //         result = this.content[this.curPos.contentPos].applyProperty(textPr);
                //         this.paraProperty.addPropertyChange();
                //     }
                //     break;
                // }
                const nResult = WasmInstance.instance._Paragraph_add_Judge(Number(bEmptyPara), this.content.length);
                if ( nResult === 2 ) {
                    result = this.content[this.curPos.contentPos].applyProperty(textPr);
                    // 设置wavyUnderlineId
                    if (wavyUnderlineId) {
                        this.content[this.curPos.contentPos].wavyUnderlineId = wavyUnderlineId;
                    }
                    this.paraProperty.addPropertyChange();
                    break;
                }
                if ( nResult === 1 ) {
                    break;
                }
                if ( nResult === 3) {
                    return;
                }
                // end by tinyzhi

                if (true === this.bApplyToAll) {
                    const curRes = this.applyAllTextProp(textPr);
                    result = this.textPr.applyTextPr(textPr, this.getHistory()) && curRes;
                    
                    // 为所有相关的portion设置wavyUnderlineId
                    if (wavyUnderlineId) {
                        this.setWavyUnderlineIdToAllPortions(wavyUnderlineId);
                    }

                } else {
                    result = this.applyTextPr(textPr);
                    
                    // 为相关的portion设置wavyUnderlineId
                    if (wavyUnderlineId) {
                        this.setWavyUnderlineIdToSelectedPortions(wavyUnderlineId);
                    }
                }
                break;
            }

            case ParaElementType.ParaPortion: {
                break;
            }

            default:
                break;
        }

        if (bDirty && ResultType.Success === result) {
            this.parent.setDirty();
        }
        return result;
    }

    public addPageBreak(flag: boolean = true): void {
        if (flag === this.paraProperty.bPageBreakBefore) {
            return;
        }

        const history = this.getHistory();
        if ( history && history.canAdd() ) {
            history.addChange(new ChangeParagraphPageBreak(this, this.paraProperty.bPageBreakBefore, flag));
        }
        this.paraProperty.bPageBreakBefore = flag;

        // if (pos === undefined) {
        //     return;
        // }
        // const portion = para.content[pos];
        // this.content[0].setTextProperty(portion.textProperty.copy());
    }

    /**
     * 插入结构化元素
     * @param newControl
     * @param property
     */
    public addNewControl(newControl: NewControl, parentNewControl: NewControl,
                         property: INewControlProperty): number {
        let insertPos = -1;
        // const history = this.parent.getHistory();
        // history.createNewHistoryPoint(HistoryDescriptionType.DocumentAddNewControl);

        if ( parentNewControl && true === parentNewControl.isPlaceHolderContent() ) {
            parentNewControl.removePlaceHolderContent();
            insertPos = this.getPortionIndexById(parentNewControl.getStartBorderPortion().id);
        }

        if ( true === this.selection.bUse ) {
           // Key-wasm by tinyhi
           // const endPos = -1 === insertPos ? this.selection.endPos : insertPos;
           const wasm = WasmInstance.instance;
           const endPos = wasm._Paragraph_addNewControl_endPos(-1, insertPos, this.selection.endPos, insertPos);
           // end by tinyzhi
           const endPortion = this.content[endPos];

           if ( -1 === insertPos ) {
                const newPortion = endPortion.splitPortion(endPortion.selection.endPos);
                this.addToContent(endPos + 1, newPortion);
            }

           // 对于签名控件，使用新方法以支持字体继承
           if (newControl instanceof NewControlSignature) {
               newControl.addToParagraphWithFontInheritance(this, endPos);
           } else {
               newControl.addToParagragh(this, endPos);
           }

            // this.curPos.contentPos = endPos + 3;
           this.content[this.curPos.contentPos].moveCursorToEndPos();

           if ( true === this.selection.bUse ) {
                this.removeSelection();
            }
        } else {
            const contentPos = this.getParaContentPos(false, false);

            if ( -1 === insertPos ) {
                this.getElementByPos(contentPos, true);
            }

           // key-wasm by tinyzhi
           // const curPos = -1 === insertPos ? contentPos.get(0) : insertPos;
            const curPos = WasmInstance.instance._Paragraph_addNewControl_CurPos(
                                -1, insertPos, contentPos.get(0), insertPos);
            const curPortion = this.content[curPos];
            // end by tinyzhi
            // key-wasm by tinyzhi
            // if ( ParaElementType.ParaPortion === curPortion.type ) {
            //     if ( -1 === insertPos && false === curPortion.isEmpty(false) ) {
            //         const newPortion = curPortion.split(contentPos);
            //         this.addToContent(curPos + 1, newPortion);
            //     }

            //     newControl.addToParagragh(this, curPos);

            //     // this.curPos.contentPos = curPos + 3;
            //     this.content[this.curPos.contentPos].moveCursorToEndPos();
            // }
            const nJudge = WasmInstance.instance._Paragraph_addNewControl_Judge(ParaElementType.ParaPortion,
                curPortion.type, insertPos, Number(curPortion.isEmpty(false) || curPortion.isRegionTitle(false)));
            if (nJudge === 0) { return ResultType.Failure; }
            if (nJudge === 2) {
                 const newPortion = curPortion.split(contentPos);
                 this.addToContent(curPos + 1, newPortion);
             }
            if ( nJudge === 1 || nJudge === 2) {
                // 对于签名控件，使用新方法以支持字体继承
                if (newControl instanceof NewControlSignature) {
                    newControl.addToParagraphWithFontInheritance(this, curPos);
                } else {
                    newControl.addToParagragh(this, curPos);
                }
                this.content[this.curPos.contentPos].moveCursorToEndPos();
             }
            // end by tinyzhi
        }

        this.parent.setDirty();

        return ResultType.Success;
    }

    public getParaContentByXY(mouseEvent: any, pageIndex: number): ParaPortion {
        const contentPos = this.getParaContentPosByXY(pageIndex, mouseEvent.pointX, mouseEvent.pointY);
        if ( contentPos === undefined || contentPos.pos == null) {
            return null;
        }

        return this.content[contentPos.pos.get(0)];
    }

    public isReadonly(): boolean {
        return this.parent.isReadonly();
    }

    public addNewControlBySelectedPos(newControl: NewControl, pos: number, direction: number = 1): number {
        let startPos = this.selection.startPos;
        let endPos = this.selection.endPos;
        // const direction = this.getSelectionDirection();
        const startParaPos = this.getParaContentPos(true, true);
        const endParaPos = this.getParaContentPos(true, false);
        if ( -1 === direction ) {
            pos = (2 === pos ? 1 : ( 1 === pos ? 2 : pos));
        }

        if ( startPos > endPos ) {
            startPos = this.selection.endPos;
            endPos = this.selection.startPos;
        }

        if ( 0 === pos || 2 === pos ) {
            const endPortion = this.content[endPos];
            const endContentPos = (1 === direction ? endParaPos : startParaPos);
            const endBorderPortion = newControl.getEndBorderPortion();
            endBorderPortion.textProperty = endPortion.textProperty.copy();

            if ( this.isCursorAtBegin(endContentPos) ) {
                this.addToContent(endPos, endBorderPortion);
            } else {
                const endContentPortion = endPortion.split(endContentPos);
                this.addToContent(endPos + 1, endContentPortion);
                this.addToContent(endPos + 1, endBorderPortion);
            }
        }

        if ( 0 === pos || 1 === pos ) {
            const startPortion = this.content[startPos];
            const bNewControlBorder = startPortion.isNewControlStart();
            const startContentPos = (1 === direction ? startParaPos : endParaPos);
            const startBorderPortion = newControl.getStartBorderPortion();
            startBorderPortion.textProperty = startPortion.textProperty.copy();

            if ( this.isCursorAtBegin(startContentPos) ) {
                this.addToContent(startPos, startBorderPortion);
            } else {
                if ( !bNewControlBorder ) {
                    const startContentPortion = startPortion.split(startContentPos);
                    this.addToContent(startPos + 1, startContentPortion);
                    this.addToContent(startPos + 1, startBorderPortion);
                } else {
                    this.addToContent(startPos + 1, startBorderPortion);
                }
            }
        }

        newControl.setPlaceHolder(false);
        return 0;
    }

    // public setProDefault(): void {
    //     this.paraProperty = new ParaProperty();
    //     this.content[0].textProperty = new TextProperty();
    // }

    public removePageBreak(): void {
        this.paraProperty.bPageBreakBefore = false;
        this.paraProperty.addPropertyChange();
    }

    public hasPageBreak(): boolean {
        return this.paraProperty.bPageBreakBefore;
    }

    public checkNewControlHidden(newControl?: NewControl): void {
        let portion: ParaPortion;
        const bHiddenPara: boolean = this.getHidden();
        if (!newControl) {
            let index = this.curPos.contentPos;
            const contents = this.content;
            if (bHiddenPara) {
                for (; index > -1; index--) {
                    portion = contents[index];
                    if (portion.isNewControlEnd()) {
                        break;
                    }
                }
                if (portion) {
                    if (this.isHideEndNewcontrol(portion)) {
                        return;
                    }
                }
            } else {
                const len = contents.length;
                for (; index < len; index++) {
                    portion = contents[index];
                    if (portion.isNewControlStart()) {
                        break;
                    }
                }
                if (index === len) {
                    return;
                }
            }

            if (!portion) {
                return;
            }
            if (portion.getHidden()) {
                return;
            }
            const name = portion.getStartNewControlName();
            if (!name) {
                return;
            }
            newControl = this.getDocument()
            .getNewControlByName(name);
            if (!newControl) {
                return;
            }
        }

        if (!newControl.isHidden() || !newControl.isNewSection()) {
            return;
        }

        portion = newControl.getStartBorderPortion();
        if (portion.getHidden() || portion.paragraph.getHidden()) {
            return;
        }
        newControl.setNewContentHidden();
        if (bHiddenPara) {
            this.bHidden = false;
        }
    }

    /**
     * 检查当前newControl内容是否为空：为空，则插入占位符
     */
    public checkNewControlPlaceContent( newControl?: NewControl, textPro?: TextProperty ): NewControl {
        if ( null == newControl ) {
            newControl = this.getCursorInNewControl();
        }

        // if ( newControl && newControl.isPlaceHolderContent() ) {
        //     return;
        // }

        if (!newControl || newControl.isHidden()) {
            return newControl;
        }

        if ( null != newControl && true === newControl.isViewSecret() && !newControl.isPlaceHolderContent()) {
            newControl.getNewControlContent()
                        .setViewSecret(newControl.getViewSecretType());
        }

        if ( null != newControl && true === newControl.checkPlaceHolderContent() ) {
            const curPos = this.curPos.contentPos;
            const pos = this.content[curPos].portionContentPos;

            newControl.addPlaceHolderContent();
            if (textPro && newControl.getTitle()
                && (newControl.isNewTextBox || newControl.isNewSection())
                ) {
                const placeHoldePrortion = newControl.getPlaceHolder();
                const color = placeHoldePrortion.textProperty.color;
                placeHoldePrortion.textProperty = textPro.copy();
                placeHoldePrortion.textProperty.color = color;
            }

            const portion = this.content[curPos];
            portion.portionContentPos = pos;
        }

        return newControl;
    }

    public getCurrentPortion(): ParaPortion {
        const selection = this.selection;
        let portion: ParaPortion;
        if (selection.bUse) {
            let start = selection.startPos;
            let end = selection.endPos;
            if (start > end) {
                const pos = start;
                start = end;
                end = pos;
            }
            portion = this.content[start];
        } else {
            portion = this.content[this.curPos.contentPos];
        }

        return portion;
    }

    public getCurrentVisiblePortion(): ParaPortion {
        const selection = this.selection;
        let start;
        let end;
        const maxLen = this.content.length - 1;
        if (selection.bUse) {
            start = selection.startPos;
            end = selection.endPos;
            if (start > end) {
                const pos = start;
                start = end;
                end = pos;
            }

        } else {
            start = this.curPos.contentPos;
            end = maxLen;
        }

        let index: number = start;
        const contents = this.content;
        for (; index <= end; index++) {
            if (index > maxLen) {
                index = maxLen;
            } else if (contents[index].isHidden() || contents[index].content.length === 0) {
                continue;
            }
            break;
        }

        return contents[index];
    }

    public getParentPos(pos: ParagraphContentPos): void {
        pos.splice(0, 0, [this.index]);
        const parent = this.parent;
        if (parent instanceof DocumentContent) {
            parent.getParentPos(pos);
        }
    }

    public getCurParaPortion(): ParaPortion {
        return this.getCurrentPortion();
    }

    public checkNewControlPlaceContent2( name: string ): void {
        const newControl = this.getParent() ? this.getParent()
                                    .getNewControlByName(name) : null;
        if ( null == newControl ) {
            return;
        }

        // if ( newControl && newControl.isPlaceHolderContent() ) {
        //     return;
        // }

        if ( null != newControl && true === newControl.checkPlaceHolderContent() ) {
            newControl.addPlaceHolderContent();
        }

        if ( null != newControl && !newControl.isPlaceHolderContent() && true === newControl.isViewSecret()) {
            newControl.getNewControlContent()
                        .setViewSecret(newControl.getViewSecretType());
        }
    }

    public getPageBounds(curPage: number): DocumentFrameBounds {
        if (this.pages[curPage] == null) {
            return null;
        }
        return this.pages[curPage].bounds;
    }

    public recalculatePage(curPage: number = 0, bHidden?: boolean): RecalcResultType {
        // console.trace();
        return this.paraRecal.recalculateParagraphPage(curPage, bHidden);
    }

    /**
     * 获取段落文本字符数
     */
    public getTextLength(bSkipSomeCh: boolean = false): number {
        let textLength = 0;

        if (!bSkipSomeCh) {
            for (let index = 0, length = this.content.length; index < length; index++) {
                const portion = this.content[index];
                textLength += ( portion ? portion.content.length : 0);
            }

            return textLength - 1;
        } else {
            let bPlaceHolder = false;

            for (let index = 0, length = this.content.length; index < length; index++) {
                const portion = this.content[index];
                if ( portion ) {
                    if ( portion.isNewControlStart() ) {
                        bPlaceHolder = portion.bPlaceHolder;
                        if ( 1 >= portion.content.length ) {
                            continue;
                        }
                    } else if ( portion.isNewControlEnd() || portion.isRegionTitle() ) {
                        bPlaceHolder = false;
                        continue;
                    } else if ( bPlaceHolder ) {
                        bPlaceHolder = false;
                        continue;
                    }

                    textLength += portion.getTextLength(bSkipSomeCh);
                }
            }

            return textLength;
        }
    }

    public getLineCount(): number {
        return this.lines.length;
    }

    public getFrameBound(): DocumentFrameBounds {
        return this.bounds;
    }

    public getPages(): ParaPage[] {
        return this.pages;
    }

    public getDocument(): Document {
        let doc = this.logicDocument;
        if (!doc && this.parent) {
            if (this.parent instanceof Document) {
                doc = this.parent;
            } else {
                doc = this.parent.getDocument();
            }
            this.logicDocument = doc;
        }

        return doc;
    }

    /**
     * 获取当前光标所处的Portion
     */
    public getCursorPortion(): ParaPortion {
        return this.content[this.curPos.contentPos];
    }

    /**
     * 获取当前段落的位置信息
     */
    public getCurContentPos(): ParagraphContentPos {
        const contentPos = new ParagraphContentPos();
        contentPos.clear();
        const topParent = this.parent.getTopDocument();
        switch (topParent.getDocumentSectionType()) {
            case DocumentSectionType.Header:
                contentPos.add(0);
                break;
            case DocumentSectionType.Document:
                contentPos.add(1);
                break;
            case DocumentSectionType.Footer:
                contentPos.add(2);
                break;
        }

        if (this.parent instanceof DocumentContent) {
            this.parent.getParentIndexs(contentPos);
        }

        contentPos.add(this.index);

        return contentPos;
    }

    public isEmptyPara(): boolean {
        const contents = this.content;
        const names: string[] = [];
        let bPlaceHolder: boolean;
        let sSigStructName: string;
        const newControlManager = this.parent.getNewControlManager();
        for (let index = 0, len = contents.length; index < len; index++) {
            const portion = contents[index];
            if (portion.isEmpty(true) || portion.isHidden() || portion.isComment()) {
                continue;
            }

            if (portion.isNewControlStart()) {
                bPlaceHolder = portion.isPlaceHolder();
                const startName: string = portion.getStartNewControlName();
                names.push(startName);
                const newControl = newControlManager.getNewControlByName(startName);
                if (newControl?.isSignatureBox()) {
                    if ((newControl as any).getSignedElementNames().length > 0) {
                        return false;
                    }
                    sSigStructName = startName;
                }
                continue;
            }
            if (portion.isNewControlEnd()) {
                const endName: string = portion.getEndNewControlName();
                const name = names.pop();
                if (name !== endName) {
                    return false;
                }
                if (sSigStructName === endName) {
                    sSigStructName = null;
                }

                bPlaceHolder = false;
                continue;
            }

            if (bPlaceHolder || sSigStructName) {
                continue;
            }

            return false;
        }

        return true;
    }

    // /**
    //  * 新增批注（没被选中的情况下）
    //  * @param props 批注的信息
    //  */
    // public addComment1(props: ICommentProperty): boolean {
    //     const index = this.curPos.contentPos;
    //     const doc = this.getDocument();
    //     const commentManager = doc.getCommentManager();
    //     if (!this.isEmpty()) {
    //         const pos = this.getNextTextPos();
    //         if (pos === undefined) {
    //             return false;
    //         }
    //         if (pos) {
    //             const contents = this.content;
    //             const textIndex = contents[index].portionContentPos;
    //             const actPos = new ParagraphContentPos();
    //             actPos.add(index);
    //             actPos.add(textIndex);
    //             // 模拟选中信息
    //             const bBefore = this.setSelectionByPos(actPos, pos);
    //             const commentId1 = this.addComment(true, undefined, []);
    //             if (commentId1 !== -1) {
    //                 this.addComment(true, commentId1, []);
    //             }
    //             const comment1 = commentManager.getLastComment();
    //             props.name = commentManager.makeUniqueName(props.name);
    //             commentManager.setName(comment1.getName(), props.name);
    //             const commentData1 = comment1.getData();
    //             commentData1.setLastTime(props.time);
    //             commentData1.setContent(props.content);
    //             commentData1.setUserName(props.userName);
    //             commentManager.activeComment(comment1.getName());
    //             this.removeSelection();
    //             return true;
    //         }
    //     }

    //     const comment = new Comment();
    //     const commentId = comment.getId();

    //     commentManager.addComment(comment);
    //     props.name = commentManager.makeUniqueName(props.name);
    //     comment.setName(props.name);
    //     const commentData = comment.getData();
    //     commentData.setLastTime(props.time);
    //     commentData.setContent(props.content);
    //     commentData.setUserName(props.userName);
    //     commentManager.activeComment(comment.getName());

    //     const portion = new ParaPortion(this);
    //     // portion.addText('');

    //     this.addCommentPortion(false, commentId, index);
    //     this.addToContent(index, portion);
    //     this.addCommentPortion(true, commentId, index);
    // }

    // /**
    //  * 新增批注（选中的情况下）
    //  * @param bSamePara 是否同一个段落
    //  * @param commentId 当前批注的id
    //  * @param comments 追加的批注
    //  */
    // public addComment(bSamePara: boolean, commentId: number, comments: Comment[]): number {
    //     const bStart = commentId !== undefined;
    //     if (commentId === undefined) {
    //         const comment = new Comment();
    //         commentId = comment.getId();
    //         const doc = this.getDocument();
    //         doc.getCommentManager()
    //             .addComment(comment);
    //     }

    //     if (this.bApplyToAll) {
    //         let index: number;
    //         if (bStart) {
    //             index = 0;
    //         } else {
    //             index = this.content.length - 1;
    //         }

    //         this.addCommentPortion(bStart, commentId, index, comments[bStart ? 0 : 1]);
    //     } else {
    //         const contents = this.content;
    //         const selection = this.getStartAndEndPos(this.selection);
    //         const startPos = this.getValidPortionIndex(selection[0], 1);
    //         let end = selection[1];
    //         end = this.getValidPortionIndex(end, -1);
    //         if (bSamePara && startPos === end) { // 同一个段落
    //             if (contents[startPos].content.length === 0) {
    //                 return -1;
    //             }

    //             let curPortion = contents[end];
    //             const portionPos = this.getStartAndEndPos(curPortion.selection);
    //             const startIndex = portionPos[0];
    //             let commentIndex: number = end;
    //             let newPortion;
    //             const endIndex = portionPos[1];
    //             let length = curPortion.content.length - 1;
    //             if (length > -1 && (endIndex !== length || length === 0 && !curPortion.isParaEndPortion())) {
    //                 newPortion = curPortion.splitPortion(endIndex);
    //                 // this.addToContent(end, curPortion);
    //                 this.addToContent(end + 1, newPortion);
    //                 commentIndex++;
    //             }

    //             this.addCommentPortion(false, commentId, commentIndex, comments[1]);
    //             curPortion = contents[end];
    //             length = curPortion.content.length - 1;
    //             commentIndex = end;
    //             if (startIndex !== 0 && (length > 0 || length === 0 && !curPortion.isParaEndPortion())) {
    //                 newPortion = curPortion.splitPortion(startIndex);
    //                 // this.addToContent(end, curPortion);
    //                 this.addToContent(end + 1, newPortion);
    //                 commentIndex++;
    //             }

    //             this.addCommentPortion(true, commentId, commentIndex, comments[0]);
    //             return -1;

    //         } else if (bStart) { // 开始
    //             const curPortion = contents[startPos];
    //             let commentIndex = startPos;
    //             const len = curPortion.content.length;
    //             if (curPortion.isNewControl() === false && len > 0 && !curPortion.isParaEndPortion()
    //             || len > 1 && curPortion.isParaEndPortion()) {
    //                 const portionPos = this.getStartAndEndPos(curPortion.selection);
    //                 const newPortion = curPortion.splitPortion(portionPos[0]);
    //                 this.addToContent(startPos + 1, newPortion);
    //                 commentIndex = startPos + 1;
    //             }

    //             this.addCommentPortion(bStart, commentId, commentIndex, comments[0]);
    //         } else { // 结束comment标志
    //             const curPortion = contents[end];
    //             // if (curPortion.content.length === 1 && curPortion.isParaEndPortion()) {
    //             //     curPortion = contents[--end];
    //             // }
    //             let commentIndex = end;
    //             const len = curPortion.content.length;
    //             if (curPortion.isNewControl() === false && len > 0 && !curPortion.isParaEndPortion()
    //             || len > 1 && curPortion.isParaEndPortion()) {
    //                 const portionPos = this.getStartAndEndPos(curPortion.selection);
    //                 const newPortion = curPortion.splitPortion(portionPos[1]);
    //                 // this.addToContent(end, curPortion);
    //                 this.addToContent(end + 1, newPortion);
    //                 commentIndex++;
    //             }
    //             this.addCommentPortion(bStart, commentId, commentIndex, comments[1]);
    //         }
    //     }

    //     return commentId;
    // }

    /**
     * 插入批注的Portion
     * @param comment 批注对象
     * @param pos 插入位置
     * @param bStart 是否是起点
     */
    public insertCommentPortion(comment: Comment, pos: ParagraphContentPos, bStart?: boolean): ParagraphContentPos { 
        let portion: ParaPortion = bStart ? comment.getStartPortion() : comment.getEndPortion();
        if (!portion) {
            portion = new ParaPortion(this);
            portion.addComment(bStart, comment.getId());
            if (bStart) {
                comment.setStartPortion(portion);
            } else {
                comment.setEndPortion(portion);
            }
        }
        let curPosIndex = pos.get(0);
        let curPortionContentPos = pos.get(1);
        const content = this.content[curPosIndex];
        // ContentPos修正
        if (bStart) {
            let curPortion = content;
            let condi = this.getCommentCondi(curPortion, curPosIndex);
            while (
                condi !== '0000'
            ) {
                const {startPos, endPos} = curPortion.selection;
                if (
                    condi[0] === '1' ||
                    (condi[2] === '1' || condi[3] === '1') &&
                    curPortion.content.length === curPortionContentPos //(startPos > endPos ? endPos : startPos)
                ) {
                    // not start of start border
                    curPortion = this.content[++curPosIndex];
                    condi = this.getCommentCondi(curPortion, curPosIndex);
                    curPortionContentPos = 0;
                    continue;
                }
                let leftPortion = this.content[curPosIndex - 1];
                if (!leftPortion) {
                    leftPortion = new ParaPortion(this);
                    this.addToContent(curPosIndex, leftPortion);
                    curPortionContentPos = 0;
                    break;
                }
                const leftCondi = this.getCommentCondi(leftPortion, curPosIndex - 1);
                if (leftCondi !== '0000' && condi !== '0000') {
                    leftPortion = new ParaPortion(this);
                    this.addToContent(curPosIndex, leftPortion);
                    curPortionContentPos = 0;
                    break;
                }
                curPortion = leftPortion;
                curPosIndex--;
                curPortionContentPos = curPortion.content.length;
                condi = leftCondi;
            }
        } else {
            // move to right
            let curPortion = content;
            let condi = this.getCommentCondi(curPortion, curPosIndex);
            while (
                condi !== '0000'
            ) {
                const {startPos, endPos} = curPortion.selection;
                if (
                    (condi[2] === '1' || condi[3] === '1') &&
                    0 === curPortionContentPos // (startPos > endPos ? startPos : endPos)
                ) {
                    // not start of start border
                    curPortion = this.content[--curPosIndex];
                    if (!curPortion) {
                        curPosIndex = 0;
                        curPortion = new ParaPortion(this);
                        this.addToContent(curPosIndex, curPortion);
                        curPortionContentPos = 0;
                        break;
                    }
                    curPortionContentPos = curPortion.content.length;
                    condi = this.getCommentCondi(curPortion, curPosIndex);
                    continue;
                }
                let rightPortion = this.content[curPosIndex + 1];
                if (!rightPortion) {
                    rightPortion = new ParaPortion(this);
                    this.addToContent(curPosIndex, rightPortion);
                    curPortionContentPos = 0;
                    break;
                }
                const rightCondi = this.getCommentCondi(rightPortion, curPosIndex + 1);
                if (rightCondi !== '0000' && condi !== '0000') {
                    rightPortion = new ParaPortion(this);
                    this.addToContent(++curPosIndex, rightPortion);
                    curPortionContentPos = 0;
                    break;
                }
                curPortion = rightPortion;
                curPosIndex++;
                curPortionContentPos = 0;
                condi = rightCondi;
            }
        }
        const newPos = new ParagraphContentPos();
        newPos.add(curPosIndex);
        newPos.add(curPortionContentPos);
        return this.insertPortion(portion, newPos);
     }

    /**
     * 删除批注
     * @param pos ParaComment的位置信息
     * @param bDeleteComment 是否删除批注列表的里对应的批注(暂时没用)
     */
    public deleteCommentPortion(pos: ParagraphContentPos, bDeleteComment: boolean = false): void {
        const portionIndex = pos.shift();
        // let commentId: number;
        const portion = this.content[portionIndex];
        if (!portion || portion.isComment() === false) {
            return;
        }
        this.removePortion(portionIndex, 1);
        if (!this.bApplyToAll) {
            const selection = this.selection;
            if (selection.startPos > selection.endPos && selection.startPos > 0) {
                selection.startPos--;
            } else if (selection.endPos > 0) {
                selection.endPos--;
            }
        }
        const contentPos = this.curPos.contentPos;
        if (contentPos >= portionIndex) {
            this.curPos.contentPos = contentPos <= 1 ? 0 : contentPos - 1;
        }
    }

    /**
     * 获取当前段落所有行
     */
    public getLines(): ParaLine[] {
        return this.lines;
    }

    /**
     * 获取当前段落所有内容
     */
    public getContent(): ParaPortion[] {
        return this.content;
    }

    /**
     * 获取当前光标
     */
    public getCurPos(): ParaCurPos {
        return this.curPos;
    }

    /**
     * 通过id获取所在行
     * @param id
     */
    public getLineById(id: number): ParaLine {
        if (0 > id || id > this.lines.length) {
            return null;
        }

        return this.lines[id - 1];
    }

    /**
     * 段落拷贝函数
     * 待完善
     */
    public copy(parent: DocumentContentBase, option?: any): DocumentContentElementBase {
        const para = new Paragraph(parent, this.logicDocument);
        // para.setParagraphProperty(this.paraProperty.copy());
        para.paraProperty = this.paraProperty;
        if (option?.bPrint) {
            const numPr = para.paraProperty.numPr;
            if (numPr) {
                para.paraProperty.numPr = {...numPr};
                para.paraProperty.numPr.para = this;
                para.numbering = this.numbering;
            }
        }

        para.contentRemove2(0, para.content.length);
        let bPlaceHolder: boolean = false;
        const contents = this.content;
        for (let index = 0, count = contents.length; index < count; index++) {
            const element = contents[index];
            const copyElement = element.copy(para, undefined, undefined, option);
            if (copyElement === null) {
                continue;
            }
            if (bPlaceHolder === true) {
                const struct = option.structs[option.structNames[option.structNames.length - 1]];
                let bUse = true;
                // 两个边框内的占位符不仅仅一个portion
                if (copyElement.content.length === 0) {
                    if (!contents[index + 1].isNewControlEnd()) {
                        bUse = false;
                    }
                }
                if (bUse) {
                    struct.content.placeHolder = copyElement;
                    option.bPlaceHolder = null;
                }
            }
            bPlaceHolder = option?.bPlaceHolder;
            para.addToContent(para.content.length, copyElement);
        }
        // 根据需求，假如整个段落都是被标志为修订删除，那么则不返回当前段落
        if (para.content.length === 0) {
            return null;
        }
        para.bHidden = this.isHidden();
        // para.logicDocument = undefined;
        para.removeSelection();
        para.moveCursorToStartPos();

        return para;
    }

    /**
     * 复制portion
     * @param para 复制的段落
     * @param startId 开始的portionId
     * @param endId 结束的portionId
     */
    public copyPortions(para: Paragraph, startId: number, endId: number): void {
        this.paraProperty = para.paraProperty.copy();
        const contents = para.content;
        let startIndex: number;
        let endIndex: number;
        if (startId === undefined) {
            startIndex = 0;
        } else {
            startIndex = contents.findIndex((portion) => portion.id === startId);
        }
        if (endId === undefined) {
            endIndex = contents.length;
        } else {
            endIndex = contents.findIndex((portion) => portion.id === endId) + 1;
        }

        let num = 0;
        for (let index = startIndex; index < endIndex; index++) {
            this.addToContent(num++, contents[index].copy(this, true));
        }
    }

    public splitPortion(): ParaPortion {
        const curPor = this.content[this.curPos.contentPos];
        const contentPos = this.getParaContentPos();
        return curPor.split(contentPos);
    }

    /**
     * 根据位置切割portion
     * @param curPor 需要切割的portion
     * @param porPos 当前portion的位置
     * @param textPos portion的内容切割位置
     */
    public splitPortion1(curPor: ParaPortion, porPos: number, textPos: number): ParaPortion {
        const contentPos = new ParagraphContentPos();
        contentPos.add(porPos);
        contentPos.add(textPos);

        return curPor.split(contentPos);
    }

    /**
     * @param pors: ParaPortion集合
     * @param nPos: 插入位置
     */
    public addToChildren(pors: ParaPortion [], nPos: number, bContainOtherPortions: boolean = false): void {
        const lastIndex = pors.length - 1;
        for (let index = 0; index < lastIndex; index++) {
            const portion = pors[index];
            const contentPos = this.curPos.contentPos;
            this.addToContent(nPos++, portion);
            if (contentPos === this.curPos.contentPos) {
                this.curPos.contentPos++;
                if (this.curPos.contentPos >= this.content.length) {
                    this.curPos.contentPos = this.content.length - 1;
                }
            }
        }

        this.curPos.contentPos = (bContainOtherPortions ? Math.max(0, this.curPos.contentPos - 1) :
                                    this.curPos.contentPos);
    }

    /**
     * 新增完portions后，对游标进行调整
     */
    public updatePortionCursor(): void {
        this.curPos.contentPos = Math.max(0, Math.min(this.curPos.contentPos, this.content.length - 2));
        this.moveCursorToPortion(this.curPos.contentPos);
    }

    // 移动光标到某个portion的某个字符后面
    public moveCursorToPortion(porPos: number, contentPos?: number): void {
        const portion = this.content[Math.max(0, porPos)];
        if (portion.type === ParaElementType.ParaEnd) {
            // this.curPos.contentPos++;
            contentPos = 0;
        } else if (contentPos === undefined) {
            contentPos = portion.content.length;
        }
        const content = this.getParaContentPos();
        content.update(Math.max(contentPos, 0), 1);
        portion.setParaContentPos(content);
    }

    /**
     * 删除portion
     * @param nPos
     * @param length
     */
    public removePortion(nPos: number, length: number): void {
        // gHistory.addChange(new ChangeParagraphAddItem(this, nPos, [item]));

        this.content.splice(nPos, length);

        if ( this.curPos.contentPos >= nPos && 0 !== this.curPos.contentPos) {
            this.curPos.contentPos = this.content.length > 2 ? this.content.length - 2 : 0;
        }
    }

    public reset( x: number, y: number, xLimit: number, yLimit: number, pageAbs: number ): void {
        this.x            = x;
        this.y            = y;
        this.xLimit       = xLimit;
        this.yLimit       = yLimit;
        this.pageNum      = pageAbs;
    }

    public getType(): number {
        return DocumentContentType.Paragraph;
    }

    public getSectPr(): SectionProperty {
        return this.sectProperty;
    }

    public getParagraphProperty(): ParaProperty {
        return this.paraProperty;
    }

    /**
     * 段落是否为空：段落结束符不算内容
     */
    public isEmpty(): boolean {
        const contentLen = this.content.length;
        for (let curPos = 0; curPos < contentLen; curPos++) {

            if (false === this.content[curPos].isEmpty(true)) {
                return false;
            }
        }

        return true;
    }

    public isEmptyRange( curLine: number, curRange: number = 0 ): boolean {
        const line = this.lines[curLine];
        const range = line.ranges[curRange];

        for (let index = range.startPos; index <= range.endPos; index++) {
            if ( false === this.content[index].isEmptyRange(curLine, curRange) ) {
                return false;
            }
        }

        return true;
    }

    /**
     * 检查当前行的range是否可用？
     * @param curLine
     * @param curRange
     */
    public checkRanges(curLine: number, curRange: number): boolean {
        const ranges = this.lines[curLine].ranges;
        const rangesCount = ranges.length;

        if ( 1 >= rangesCount ) {
            return true;
        } else if ( 2 === rangesCount ) {

            const range0 = ranges[0];
            const range1 = ranges[1];

            if (range0.xEnd - range0.x < 0.001 && 1 === curRange && range1.xEnd - range1.x >= 0.001) {
                return true;
            } else if ( range1.xEnd - range1.x < 0.001 && 0 === curRange && range0.xEnd - range0.x >= 0.001) {
                return true;
            } else {
                return false;
            }
        } else if ( 3 === rangesCount && 1 === curRange ) {

            const range0 = ranges[0];
            const range2 = ranges[2];

            if (range0.xEnd - range0.x < 0.001 && range2.xEnd - range2.x < 0.001) {
                return true;
            } else {
                return false;
            }
        } else {
            return false;
        }
    }

    public isInline(): boolean {
        return true;
    }

    /**
     * 检查当前ParaPage是否有内容
     * @param curPage
     * @param bSkip
     */
    public isEmptyPage(curPage: number, bSkipEmptyLinesWithBreak: boolean = true): boolean {
        if (!this.pages[curPage] || this.pages[curPage].endLine < this.pages[curPage].startLine) {
            return true;
        }

        if (true === bSkipEmptyLinesWithBreak
            && this.pages[curPage].endLine === this.pages[curPage].startLine
            && this.lines[this.pages[curPage].endLine]
            && this.lines[this.pages[curPage].endLine].info & ParaLineInfo.Empty // ) {
            && this.lines[this.pages[curPage].endLine].info & ParaLineInfo.BreakPage ) {
            return true;
        }

        return false;
    }

    /**
     * 检查ParaPage是否有内容：ParaPage为 0 --- curPage
     * @param curPage
     * @param bSkip
     */
    public checkEmptyPages(curPage: number, bSkipEmptyLinesWithBreak: boolean = true): boolean {
        for ( let i = curPage; i >= 0; --i ) {
            if ( true !== this.isEmptyPage(i, bSkipEmptyLinesWithBreak) ) {
                return false;
            }
        }
        return true;
    }

    public checkFirstPage(curPage: number, bSkipEmptyLinesWithBreak: boolean = true): boolean {
        if (true === this.isEmptyPage(curPage, bSkipEmptyLinesWithBreak)) {
            return false;
        }

        return this.checkEmptyPages(curPage - 1, bSkipEmptyLinesWithBreak);
    }

    public useWrap(): boolean {
        if ( true !== this.isInline() ) {
            return false;
        }

        return true;
    }

    public useYLimit(): boolean {
        return true;
    }

    /**
     * 通过行号获取所在ParaPage
     * @param line
     */
    public getPageByLine(lineIndex: number): number {
        for (let curPage = 0, length = this.pages.length; curPage < length; curPage++) {
            const page = this.pages[curPage];

            if ( lineIndex >= page.startLine && lineIndex <= page.endLine ) {
                return curPage;
            }
        }

        return 0;
    }

    /**
     * 判断是否要分页
     * @param item
     */
    public checkBreakPageEnd(item: string): boolean {
        if ( this.parent instanceof Document && null == this.getDocumentNext()) {
            return false;
        }

        /*let ContentLen = this.content.length;
        for (let CurPos = 0; CurPos < ContentLen; CurPos++)
        {
            let Element = this.content[CurPos];

            if (true !== Element.Check_BreakPageEnd(PBChecker))
                return false;
        }*/
        return true;
    }

    /**
     * 插入段落结束符
     * todo：
     */
    public addEnd( textPr?: TextProperty ): void {
        const length = this.content.length;
        // 判断是否为空段落
        if ( 0 === length ) {
            const endPortion = new ParaPortion(this);
            endPortion.addEnd(textPr);

            this.addToContent(length, endPortion);
        // } else {
        //     const lastPortion = this.content[length - 2];
        //     if (!lastPortion) {
        //         return;
        //     }
        //     if ( 0 === lastPortion.content.length
        //         || ParaElementType.ParaEnd !== lastPortion.content[lastPortion.content.length - 1].type ) {
        //         lastPortion.addEnd(textPr);
        //     }
        }
    }

    /**
     * 删除段落结束符
     */
    public removeParaEnd(): void {
        const length = this.content.length;

        for (let index = length - 1; index >= 0; index++) {
            const element = this.content[index];

            if ( true === element.removeParaEnd() ) {
                return;
            }
        }
    }

    /**
     * 检查段落是否有段落结束符，如果没有，则插入结束符
     */
    public checkParaEnd(): void {
        const length = this.content.length;

        if ( 0 >= length || null == this.content[length - 1].getParaEnd() ) {
            this.addParaEnd(length);
        }
    }

    /**
     * 检查当前段落是否在页面真正跨页
     */
    public isStartFromNewPage(): boolean {
        if ( ( 1 < this.pages.length && this.pages[0].firstLine === this.pages[1].firstLine )
            || (!this.getDocumentPrev())
            || ( 1 === this.pages.length && -1 === this.pages[0].endLine ) ) {
            return true;
        }

        return false;
    }

    /**
     * 计算光标的当前位置
     * @param bUpdateX
     * @param bUpdateY
     */
    public recalculateCurPos(bUpdateX: boolean = true, bUpdateY: boolean = true): ICurrentCursorPos {
        const result = this.recalcCurPos(this.curPos.contentPos, true);

        if (result == null) {
            return null;
        }

        if ( bUpdateX ) {
            this.curPos.x = result.x;
        }

        if ( bUpdateY ) {
            this.curPos.y = result.y;
        }

        return result;
    }

    public getSelectedParaPros(paraPro: IParaProperty): boolean {
        const keys = Object.keys(paraPro)
            .filter((key) => paraPro[key] !== null);
        if (keys.length === 0) {
            return true;
        }

        const pro = this.paraProperty;
        for (let index =  keys.length - 1; index > -1; index--) {
            const key = keys[index];
            switch (key) {
                case 'alignment': {
                    if (paraPro.alignment === undefined) {
                        paraPro.alignment = pro.alignment;
                    } else if (pro.alignment !== paraPro.alignment) {
                        paraPro.alignment = null;
                        keys.splice(index, 1);
                    }
                    break;
                }
                case 'bPageBreakBefore': {
                    if (paraPro.bPageBreakBefore === undefined) {
                        paraPro.bPageBreakBefore = pro.bPageBreakBefore;
                    } else if (pro.bPageBreakBefore !== paraPro.bPageBreakBefore) {
                        paraPro.bPageBreakBefore = null;
                        keys.splice(index, 1);
                    }
                    break;
                }
                case 'paraLeftInd': {
                    if (paraPro.paraLeftInd === undefined) {
                        paraPro.paraLeftInd = pro.paraInd.left;
                    } else if (pro.paraInd.left !== paraPro.paraInd) {
                        paraPro.paraInd = null;
                        keys.splice(index, 1);
                    }
                    break;
                }
                case 'paraInd': {
                    const ind = pro.paraInd;
                    let num: number = 0;
                    let type: number = 0;
                    // 悬挂缩进
                    if (ind.firstLine !== undefined && ind.firstLine < 0) {
                        type = 2;
                        num = ind.firstLine;
                    } else { // 首行缩进
                        type = 1;
                        num = ind.firstLine;
                    }

                    if (paraPro.paraInd === undefined) {
                        paraPro.paraInd = num;
                        paraPro.paraIndType = type;
                    } else if (paraPro.paraInd !== num || paraPro.paraIndType !== type) {
                        paraPro.paraInd = null;
                        paraPro.paraIndType = null;
                        keys.splice(index, 1);
                    }

                    break;
                }
                case 'paraSpacing': {
                    const spacing = pro.paraSpacing;
                    if (paraPro.paraSpacing === undefined) {
                        paraPro.paraSpacing = spacing.lineSpacing;
                        paraPro.paraSpacingType = spacing.lineSpacingType;

                        if ( LineSpacingType.Multi === paraPro.paraSpacingType ) {
                            paraPro.paraSpacing = (paraPro.paraSpacing - 1) / 0.473;
                            paraPro.paraSpacing = Math.round(paraPro.paraSpacing * 100) / 100;
                        }
                    } else if (spacing.lineSpacing !== paraPro.paraSpacing
                            || spacing.lineSpacingType !== paraPro.paraSpacingType) {
                        paraPro.paraSpacing = null;
                        paraPro.paraSpacingType = null;
                        keys.splice(index, 1);
                    }
                    break;
                }
            }
        }

        if ( undefined === paraPro.bWordWrap ) {
            paraPro.bWordWrap = pro.bWordWrap;
        } else if ( pro.bWordWrap !== paraPro.bWordWrap ) {
            paraPro.bWordWrap = false;
        }

        return keys.filter((key) => paraPro[key] !== null).length === 0;
    }

    /**
     * 获取光标所在行，ParaPage
     * @param paraPos
     */
    public getCurParaPos(paraPos: IParaPos): void {
        this.content[this.curPos.contentPos].getCurParaPos(paraPos);

        if ( -1 !== this.curPos.line) {
            paraPos.line = this.curPos.line;
            paraPos.range = this.curPos.range;
        }

        paraPos.page = this.getPageByLine(paraPos.line);
    }

    /**
     * 保存当前光标所在行和portion
     * @param contentPos
     * @param line
     * @param bCorrectEndLinePos : 主要用于键盘的光标移动时为true
     */
    public setParaContentPos(contentPos: ParagraphContentPos, line: number, bCorrectEndLinePos: boolean = false): void {
        // console.log(contentPos)
        let portionPos = contentPos.get(0);

        if ( 0 > portionPos) {
            portionPos = 0;
        }

        if (portionPos >= this.content.length) {
            portionPos = this.content.length - 1;
        }

        this.curPos.contentPos = portionPos;
        this.content[portionPos].setParaContentPos(contentPos);
        // console.log(circularParse(circularStringify(this)));

        if ( false !== bCorrectEndLinePos ) {
            this.correctContentPos(bCorrectEndLinePos);
            this.correctContentPos2();
        }

        this.curPos.line = line;
        this.curPos.range = 0;
    }

    /**
     * 获取光标位置所在段落的行和portion，
     * bYLine = true：pointY为行号，直接在该行定位portion
     * @param pageIndex parapage
     * @param pointX
     * @param pointY
     * @param bYLine
     * @param bParaEnd
     */
    public getParaContentPosByXY(pageIndex: number, pointX: number, pointY: number,
                                 bYLine?: boolean, bParaEnd?: boolean ): IParagraphSearchPosXY {
        const searchPos = { pos : new ParagraphContentPos(), textPos : new ParagraphContentPos(),
            x: 0,
            y: 0,
            curX : 0,
            curY : 0,
            line : 0,
            diffX : 100000,
            bInText : false,
        };

        if ( this.lines.length <= 0) {
            return searchPos;
        }

        let pageNum = ( -1 === pageIndex || !pageIndex ) ? 0 : pageIndex;
        // console.log(pageNum);  // parapage number

        // pageNum不在页面范围，pointY的值为行号
        if ( pageNum >= this.pages.length ) {
            bYLine = true;
            pageNum = this.pages.length - 1;
            pointY = this.lines.length - 1;
        } else if ( pageNum < 0 ) {
            bYLine = true;
            pageNum = 0;
            pointY = 0;
        }

        let curLine = 0;
        if ( true === bYLine ) {
            curLine = pointY;
        } else {
            curLine = this.pages[pageNum].firstLine;

            const lastLine = (pageNum >= this.pages.length - 1) ?
                                this.lines.length - 1 : this.pages[pageNum + 1].firstLine - 1;
            let curLineY = this.lines[curLine].bottom;

            while (true) {
                if (pointY <= curLineY || curLine >= lastLine) {
                    break;
                }
                curLine++;
                curLineY = this.lines[curLine].bottom;
            }
        }

        const range = this.lines[curLine].ranges[0];
        const startPortionPos = range.startPos;
        const endPortionPos = range.endPos;

        searchPos.x = pointX;
        searchPos.y = pointY;
        searchPos.curX = range.xVisible;
        searchPos.line = curLine;

        for (let curPos = startPortionPos; curPos <= endPortionPos; curPos++) {
            const portion = this.content[curPos];

            if ( false === searchPos.bInText) {
                searchPos.textPos.update2(curPos, 0);
            }

            if ( portion.getParaContentPosByXY(searchPos, 1, bParaEnd)) {
                searchPos.pos.update2(curPos, 0);
                if (searchPos.pos['moveNextPortion']) {
                    searchPos.pos.reset();
                    searchPos.pos.update2(curPos + 1, 0);
                    searchPos.textPos.reset();
                    searchPos.textPos.update2(curPos + 1, 0);
                }
            }
        }

        // 空portion???
        if ( searchPos.diffX > 100000 - 1) {
            searchPos.line = -1;
        } else {
            searchPos.line = curLine;
        }

        return searchPos;
    }

    public getSearchInfos(search: any): void {
        search.search(this);
    }

    public selectAreaByPos(startPos: ParagraphContentPos, endPos: ParagraphContentPos, dir?: number): boolean {
        const startProIndex = startPos.shift();
        const endProIndex = endPos.shift();
        const startNum = startPos.get(0) || 0;
        if  (startProIndex === undefined || endProIndex === undefined) {
            return false;
        }
        if (startProIndex === endProIndex) {
            this.content[startProIndex].selectAreaByPos(startPos, endPos, dir);
        } else {
            const contents = this.content;
            const pos = new ParagraphContentPos();
            pos.clear();
            const startElem = contents[startProIndex];
            if (!startElem) {
                return false;
            }
            pos.add(startElem.content.length);
            let flag = startElem.selectAreaByPos(startPos, pos, dir);
            if (flag === false) {
                return false;
            }
            pos.clear();
            pos.add(0);
            flag = contents[endProIndex].selectAreaByPos(pos, endPos, dir);
            if (flag === false) {
                return false;
            }
            for (let index = startProIndex + 1; index < endProIndex; index++) {
                contents[index].selectAll(1);
            }
        }
        const selection = this.selection;
        selection.bUse = true;
        selection.bStart = false;
        selection.startPos = startProIndex;
        selection.endPos = endProIndex;
        this.curPos.contentPos = startProIndex;
        this.curPos.line = this.content[endProIndex].getLineByPos(startNum);
        return true;
    }

    /**
     * 获取段落第一个portion的信息
     */
    public getStartPos(pos?: ParagraphContentPos): ParagraphContentPos {
        let contentPos = pos;
        if (contentPos === undefined) {
            contentPos = new ParagraphContentPos();
            contentPos.clear();
        }
        contentPos.add(0);
        this.content[0].getStartPos(contentPos);
        // const contentPos = new ParagraphContentPos();
        // contentPos.update(0, 0);

        // this.content[0].getStartPos(contentPos);
        return contentPos;
    }

    /**
     * 获取段落最后一个portion的信息
     * @param bParaEnd
     */
    public getEndPos(bParaEnd: boolean, pos?: ParagraphContentPos ): ParagraphContentPos {
        let contentPos = pos;
        if (contentPos === undefined) {
            contentPos = new ParagraphContentPos();
            contentPos.clear();
        }
        contentPos.add(this.content.length - 1);

        this.content[this.content.length - 1].getEndPos(contentPos);
        // const contentPos = new ParagraphContentPos();
        // contentPos.update(this.content.length - 1, 0);

        return contentPos;
    }

    public getRegion(bInTable: boolean = true): Region {
        const parent = this.parent;
        if (parent instanceof DocumentContent) {
            return parent.getRegion(bInTable);
        }

        return;
    }

    public setParentPos(pos: ParagraphContentPos): void {
        pos.unShift(this.index);
        if (this.parent && 'setParentPos' in this.parent) {
            (this.parent as any).setParentPos(pos);
        }
    }

    /**
     * 将光标移动到制定point位置
     * @param pageIndex
     * @param pointX
     * @param pointY
     * @param bLine
     * @param bDontChangeRealPos
     */
    public moveCursorToXY(pageIndex: number, pointX: number, pointY: number,
                          bLine: boolean = false, bDontChangeRealPos: boolean = true): void {
        const searchPos = this.getParaContentPosByXY(pageIndex, pointX, pointY, bLine);

        this.setParaContentPos(searchPos.pos, searchPos.line);
        this.recalcCurPos(-1, false);

        if ( true !== bDontChangeRealPos ) {
            this.curPos.x = this.curPos.realX;
            this.curPos.y = this.curPos.realY;
        }

        // if ( !bLine ){
        //     this.curPos.realX = pointX;
        //     this.curPos.realY = pointY;
        // }
    }

    /**
     * 将光标移动到段首位置
     * @param bAddToSelect
     */
    public moveCursorToStartPos( bAddToSelect: boolean = false, bSelectFromStart: boolean = false ): void {
        // selection
        if ( true === bAddToSelect ) {
            let startPos;

            if ( true === this.selection.bUse ) {
                startPos = this.getParaContentPos(true, true);
            } else if ( true === bSelectFromStart ) {
                startPos = this.getStartPos();
            } else {
                startPos = this.getParaContentPos(false, false);
            }

            const endPos = this.getStartPos();

            this.selection.bUse = true;
            this.selection.bStart = false;

            this.setSelectionContentPos(startPos, endPos);
            this.setParaContentPos(startPos, 0);
        } else {
            if ( true === bSelectFromStart ) {
                this.selection.bUse = true;
                this.selection.bStart = false;
                this.selection.startPos = 0;
                this.selection.endPos = 0;
                this.curPos.contentPos = 0;
                this.content[this.curPos.contentPos].moveCursorToStartPos(bSelectFromStart);
            } else {
                this.removeSelection();

                this.curPos.contentPos = 0;
                this.curPos.line = -1;
                this.curPos.range = -1;
                this.content[0].moveCursorToStartPos();
                this.correctContentPos(false);
                this.correctContentPos2();
            }
        }
    }

    /**
     * 将光标移动到段尾位置
     * @param bAddToSelect
     * @param bSelectFromEnd
     */
    public moveCursorToEndPos( bAddToSelect: boolean = false, bSelectFromEnd: boolean = false ): void {
        // selection
        if ( true === bAddToSelect ) {
            let startPos;

            if ( true === this.selection.bUse ) {
                startPos = this.getParaContentPos(true, true);
            } else if ( true === bSelectFromEnd ) {
                startPos = this.getEndPos(true);
            } else {
                startPos = this.getParaContentPos(false, false);
            }

            const endPos = this.getEndPos(true);

            this.selection.bUse = true;
            this.selection.bStart = false;

            this.setSelectionContentPos(startPos, endPos);

            // 设置当前的光标位置，在段落结束符之前
            this.setParaContentPos(endPos, this.lines.length - 1);
        } else {
            if ( true === bSelectFromEnd ) {
                this.selection.bUse = true;
                this.selection.bStart = false;
                this.selection.startPos = this.content.length - 1;
                this.selection.endPos = this.content.length - 1;
                this.curPos.contentPos = this.content.length - 1;
                this.content[this.curPos.contentPos].moveCursorToEndPos(bSelectFromEnd);
            } else {
                this.removeSelection();

                this.curPos.contentPos = this.content.length - 1;
                this.curPos.line = -1;
                this.curPos.range = -1;
                this.content[this.curPos.contentPos].moveCursorToEndPos();
                this.correctContentPos(false);
                this.correctContentPos2();
            }
        }
    }

    /**
     * 获取光标左边left位置
     * @param searchPos
     * @param contentPos
     */
    public getLeftPos( searchPos: IParagraphSearchPos, contentPos: ParagraphContentPos ): boolean {
        let curPos = contentPos.get();
        this.content[curPos].getLeftPos(searchPos, contentPos);
        searchPos.pos.update(curPos, 0);

        // if founded
        if ( true === searchPos.bFound ) {
            return true;
        }

        // 上一个portion的尾部
        curPos--;

        while ( 0 <= curPos ) {
            this.content[curPos].getLeftPos(searchPos, null);
            searchPos.pos.update(curPos, 0);

            if ( false !== searchPos.bFound ) {
                return true;
            }

            curPos--;
        }

        return false;
    }

    // public getLeftPosItem( contentPos?: ParagraphContentPos ): ParaElementBase {
    //     const searchPos = {
    //         pos: new ParagraphContentPos(),
    //         bFound: false,
    //         bSelection: true,
    //       };

    //     if ( null == contentPos ) {
    //         contentPos = this.getParaContentPos();
    //     }

    //     this.getLeftPos(searchPos, contentPos);

    //     if ( true === searchPos.bFound ) {
    //         return this.content[searchPos.pos.get(0)].content[searchPos.pos.get(1)];
    //     } else {
    //         return null;
    //     }
    // }

    /**
     * 获取当前光标所在位置信息：行，ParaPage
     */
    public getCurrentParaPos(): IParaPos {
        // 获取当前光标所在行，不一定需要？当前光标所在行已经被记录了！！this.curPos.line！！
        const paraPos = this.content[this.curPos.contentPos].getCurrentParaPos();

        if ( -1 !== this.curPos.line ) {
           paraPos.line = this.curPos.line;
           paraPos.range = this.curPos.range;
       }

        paraPos.page = this.getPageByLine(paraPos.line);

        return paraPos;
    }

    public setContentPos(pos: ParagraphContentPos): boolean {
        const index = pos.shift();
        if (index === undefined || this.content.length <= index) {
            return false;
        }
        const portion = this.content[index];
        this.curPos.contentPos = index;
        this.curPos.line = portion.getLineByPos(pos.get());
        return portion.setContentPos(pos);
    }

    /**
     * 获取光标右边right位置
     * @param searchPos
     * @param contentPos
     * @param bStep
     */
    public getRightPos( searchPos: IParagraphSearchPos, contentPos: ParagraphContentPos, bStep?: boolean ): boolean {
        let curPos = contentPos.get();
        this.content[curPos].getRightPos(searchPos, contentPos);
        searchPos.pos.update(curPos, 0);

        // if founded
        if ( true === searchPos.bFound ) {
            return true;
        }

        curPos++;
        const count = this.content.length;

        while ( count > curPos ) {
            this.content[curPos].getRightPos(searchPos, null);
            searchPos.pos.update(curPos, 0);

            if ( false !== searchPos.bFound ) {
                return true;
            }

            curPos++;
        }

        return false;
    }

    // public getRightPosItem( contentPos?: ParagraphContentPos ): ParaElementBase {
    //     const searchPos = {
    //         pos: new ParagraphContentPos(),
    //         bFound: false,
    //         bSelection: true,
    //     };

    //     if ( null == contentPos ) {
    //         contentPos = this.getParaContentPos();
    //     }

    //     this.getRightPos(searchPos, contentPos);

    //     if ( false === searchPos.bFound || ( false === this.content[contentPos.get(0)].isEmpty(true)
    //       && null != this.content[contentPos.get(0)].content[contentPos.get(1)]
    //         && true === searchPos.bFound && searchPos.pos.get(1) !== contentPos.get(1) ) ) {
    //         return this.content[contentPos.get(0)].content[contentPos.get(1)];
    //     } else if ( true === searchPos.bFound ) {
    //         return this.content[searchPos.pos.get(0)].content[searchPos.pos.get(1) - 1];
    //     } else {
    //         return null;
    //     }
    // }

    /**
     * 光标左移
     * @param bShiftLey shift键
     */
    public moveCursorLeft( bShiftLey: boolean  = false ): boolean {
        if ( true === this.selection.bUse ) {
            const startSelectionPos = this.getParaContentPos(true, true);
            const endSelectionPos = this.getParaContentPos(true, false);

            if ( true !== bShiftLey ) {
                let startPos = startSelectionPos;
                if ( 0 < startSelectionPos.compare(endSelectionPos) ) {
                    startPos = endSelectionPos;
                }

                this.removeSelection();
                this.setParaContentPos(startPos, -1);
            } else {
              const searchPos = {
                pos: new ParagraphContentPos(),
                bFound: false,
                bSelection: true,
              };

              this.getLeftPos(searchPos, endSelectionPos);

              if ( true === searchPos.bFound ) {
                    this.selection.bUse = true;
                    this.setSelectionContentPos(startSelectionPos, searchPos.pos);
                } else {
                  return false;
                }
            }
        } else {
            const searchPos = {
              pos: new ParagraphContentPos(),
              bFound: false,
              bSelection: false,
            };
            const contentPos = this.getParaContentPos(false, false);

            if ( true === bShiftLey ) {
                searchPos.bSelection = true;
            }

            this.getLeftPos(searchPos, contentPos);

            if ( true === bShiftLey ) {
                this.selection.bUse = true;

                if ( true === searchPos.bFound ) {
                    this.setSelectionContentPos(contentPos, searchPos.pos);
                }  else {
                    // 在段尾时，默认也是被选中
                    searchPos.pos = this.getStartPos();
                    this.setSelectionContentPos(contentPos, searchPos.pos);
                    return false;
                }
            } else {
                if ( true === searchPos.bFound ) {
                    this.setParaContentPos(searchPos.pos, -1, true);
                } else {
                    return false;
                }
            }
        }

        if ( true === this.selection.bUse ) {
            const selectionEndPos = this.getParaContentPos(true, false);
            this.setParaContentPos(selectionEndPos, -1);
        }

        this.recalculateCurPos();
        return true;
    }

    /**
     * 从下一段开始位置开始向右移动光标，从上一段落末尾开始选择，并选中一个字符
     */
    public moveCursorLeftWithSelectionFromEnd(): void {
        // 重置当前段落状态
        this.moveCursorToEndPos(true, true);

        // 选中第一个字符
        // this.moveCursorLeft(true);
        this.recalculateCurPos();
    }

    /**
     * 键盘光标右移
     * @param bShiftLey
     */
    public moveCursorRight( bShiftLey: boolean = false ): boolean {
        if ( true === this.selection.bUse ) {
            const startSelectionPos = this.getParaContentPos(true, true);
            const endSelectionPos = this.getParaContentPos(true, false);

            if ( true !== bShiftLey ) {
                // 检查段落的结尾是否在选择中
                if ( true === this.isSelectionParaEnd(false)) {
                    this.removeSelection();
                    this.moveCursorToEndPos();
                } else {
                    let endPos = endSelectionPos;
                    if ( 0 < startSelectionPos.compare(endSelectionPos) ) {
                        endPos = startSelectionPos;
                    }

                    this.removeSelection();
                    this.setParaContentPos(endPos, -1);
                }
            } else {
                const searchPos = {
                    pos: new ParagraphContentPos(),
                    bFound: false,
                    bSelection: true,
                };

                this.getRightPos(searchPos, endSelectionPos);

                if ( true === searchPos.bFound ) {
                    this.setSelectionContentPos(startSelectionPos, searchPos.pos);
                } else {
                    return false;
                }
            }
        } else {
            const searchPos = {
                pos: new ParagraphContentPos(),
                bFound: false,
                bSelection: false,
            };
            const contentPos = this.getParaContentPos(false, false);

            if ( true === bShiftLey ) {
                searchPos.bSelection = true;
            }

            this.getRightPos(searchPos, contentPos);

            if ( true === bShiftLey ) {
                this.selection.bUse = true;

                if ( true === searchPos.bFound ) {
                    // 选择尚未添加，从当前位置添加
                    this.setSelectionContentPos(contentPos, searchPos.pos);
                }  else {
                    searchPos.pos = this.getEndPos(true);
                    this.setSelectionContentPos(contentPos, searchPos.pos);
                    return false;
                }
            } else {
                if ( true === searchPos.bFound ) {
                    this.setParaContentPos(searchPos.pos, -1, true);
                } else {
                    return false;
                }
            }
        }

        if ( true === this.selection.bUse ) {
            const selectionEndPos = this.getParaContentPos(true, false);
            this.setParaContentPos(selectionEndPos, -1);
        }

        this.recalculateCurPos();
        return true;
    }

    /**
     * 从上一段落末尾开始向右移动光标，从下一段开始位置开始选择，并选中一个字符
     */
    public moveCursorRightWithSelectionFromStart(): void {
        // 重置当前段落状态
        this.moveCursorToStartPos(true, true);

        // 选中第一个字符
        // this.moveCursorRight(true);
        this.recalculateCurPos();
    }

    /**
     * 键盘光标上移到段落的最后一行
     * @param pointX
     * @param pointY
     * @param bShiftLey
     */
    public moveCursorUpToLastRow( pointX: number, pointY: number, bShiftLey: boolean ): void {
        this.curPos.x = pointX;
        this.curPos.y = pointY;
        this.curPos.realX = pointX;
        this.curPos.realY = pointY;

        this.moveCursorToXY(this.pages.length - 1, pointX, this.lines.length - 1, true);

      // selection
        if ( true === bShiftLey ) {
          if ( false === this.selection.bUse ) {
                this.selection.bUse = true;

                const startSelectionPos = this.getEndPos(true);
                const endSelectionPos = this.getParaContentPos(false, false);
                this.setSelectionContentPos(startSelectionPos, endSelectionPos);
            } else {
                const startSelectionPos = this.getParaContentPos(true, true);
                const endSelectionPos = this.getParaContentPos(false, false);
                this.setSelectionContentPos(startSelectionPos, endSelectionPos);
            }
        }
    }

    /**
     * 键盘光标下移到段落的第一行
     * @param pointX
     * @param pointY
     * @param bShiftLey
     */
    public moveCursorDownToFirstRow( pointX: number, pointY: number, bShiftLey: boolean ): void {
        this.curPos.x = pointX;
        this.curPos.y = pointY;
        this.curPos.realX = pointX;
        this.curPos.realY = pointY;

        this.moveCursorToXY(0, pointX, 0, true);

        // selection
        if ( true === bShiftLey ) {
            if ( false === this.selection.bUse ) {
                this.selection.bUse = true;

                const startSelectionPos = this.getStartPos();
                const endSelectionPos = this.getParaContentPos(false, false);
                this.setSelectionContentPos(startSelectionPos, endSelectionPos);
            } else {
                const startSelectionPos = this.getParaContentPos(true, true);
                const endSelectionPos = this.getParaContentPos(false, false);
                this.setSelectionContentPos(startSelectionPos, endSelectionPos);
            }
        }
    }

    /**
     * 键盘光标上移
     * @param bShiftLey
     */
    public moveCursorUp( bShiftLey: boolean  = false ): boolean {
        let result = true;

        if (true === this.selection.bUse) {
            const startSelectionPos = this.getParaContentPos(true, true);
            let endSelectionPos = this.getParaContentPos(true, false);

            // 是否要进行选择
            if (true !== bShiftLey) {
                let startPos = startSelectionPos;
                if (0 < startPos.compare(endSelectionPos)) {
                    startPos = endSelectionPos;
                }

                const linePos = this.getParaPosByContentPos(startPos);
                this.setParaContentPos(startPos, linePos.line);
                this.recalculateCurPos();

                this.removeSelection();
            } else {
                const linePos = this.getCurrentParaPos();

                // 当前光标所在行是否在段落第一行
                if (0 === linePos.line) {
                    endSelectionPos = this.getStartPos();
                    result = false;
                } else {
                    this.moveCursorToXY(linePos.page, this.curPos.x, linePos.line - 1, true);
                    endSelectionPos = this.getParaContentPos(false, false);
                }

                this.setSelectionContentPos(startSelectionPos, endSelectionPos);
                return result;
            }
        } else {
            const linePos = this.getCurrentParaPos();

            // 是否要进行选择
            if (true === bShiftLey) {
                const startSelectionPos = this.getParaContentPos(false, false);
                let endSelectionPos = null;

                // 当前光标所在行是否在段落第一行
                if (0 === linePos.line) {
                    endSelectionPos = this.getStartPos();
                    result = false;
                } else {
                    this.moveCursorToXY(linePos.page, this.curPos.x, linePos.line - 1, true);
                    endSelectionPos = this.getParaContentPos(false, false);
                }

                this.selection.bUse = true;
                this.setSelectionContentPos(startSelectionPos, endSelectionPos);

                return result;
            } else {

                // 当前光标所在行是否在段落第一行
                if (0 === linePos.line) {
                    return false;
                } else {
                    this.moveCursorToXY(linePos.page, this.curPos.x, linePos.line - 1, true);
                }
            }
        }

        return result;
    }

    /**
     * 键盘光标下移
     * @param bShiftLey
     */
    public moveCursorDown( bShiftLey: boolean  = false ): boolean {
        let result = true;

        if ( true === this.selection.bUse ) {
            const startSelectionPos = this.getParaContentPos(true, true);
            let endSelectionPos = this.getParaContentPos(true, false);

            if ( true !== bShiftLey ) {
                        let endPos = endSelectionPos;
                        if ( 0 < startSelectionPos.compare(endSelectionPos) ) {
                            endPos = startSelectionPos;
                        }

                        const linePos = this.getParaPosByContentPos(endPos);
                        this.setParaContentPos(endPos, linePos.line);
                        this.recalculateCurPos();

                        this.removeSelection();
            } else {
                const linePos = this.getCurrentParaPos();

                // 当前光标所在行是否在段落最后一行
                if ( this.lines.length - 1 <= linePos.line ) {
                            endSelectionPos = this.getEndPos(true);
                            result = false;
                }  else {
                this.moveCursorToXY(linePos.page, this.curPos.x, linePos.line + 1, true);
                endSelectionPos = this.getParaContentPos(false, false);
                        }

                this.setSelectionContentPos(startSelectionPos, endSelectionPos);
                return result;
            }
        } else {
            const linePos = this.getCurrentParaPos();

            if ( true === bShiftLey ) {
                const startSelectionPos = this.getParaContentPos(false, false);
                let endSelectionPos = null;

                // 当前光标所在行是否在段落最后一行
                if ( this.lines.length - 1 <= linePos.line ) {
                            endSelectionPos = this.getEndPos(true);
                            result = false;
                }  else {
                this.moveCursorToXY(linePos.page, this.curPos.x, linePos.line + 1, true);
                endSelectionPos = this.getParaContentPos(false, false);
                        }

                this.selection.bUse = true;
                this.setSelectionContentPos(startSelectionPos, endSelectionPos);
                return result;
            } else {
                // 当前光标所在行是否在段落最后一行
                if ( this.lines.length - 1 <= linePos.line ) {
                    return false;
                } else {
                    this.moveCursorToXY(linePos.page, this.curPos.x, linePos.line + 1, true);
                }
            }
        }
        return result;
    }

    /**
     * 获取指定位置所在的range和line
     * @param paraPos
     */
    public getRangesByPos( paraPos: ParagraphContentPos ): IRanges[] {
        const portion = this.getElementByPos(paraPos);

        if ( null == portion || ParaElementType.ParaPortion !== portion.getType() ) {
            return [];
        }

        return portion.getRangesByPos(paraPos.get(paraPos.depth - 1));
    }

    /**
     * 获取当前光标位置坐标
     */
    public getCursorPosXY(): {pageNum: number, x: number, y: number } {
        // console.log("      ("+ this.curPos.x + ", " + this.curPos.y + ")");
        // console.log("real: ("+ this.curPos.realX + ", " + this.curPos.realY + ")");
        // console.log(this.curPos.pagePos)
        // console.log(this.getAbsolutePage(this.curPos.pagePos))
        const res = {
          pageNum: this.getAbsolutePage(this.curPos.pagePos), // 绝对页码
            x: this.curPos.realX,
            y: this.curPos.realY,
        };

        const linePos = this.curPos.line;
        const portion = this.getCurrentPortion();
        let line = this.lines[linePos];
        let portionContentPos = portion.portionContentPos;

        if (-1 === linePos) {
            const lineIndex = portion.getLineByPos(portionContentPos);
            line = this.lines[lineIndex];
        }

        if (VertAlignType.Bottom !== line.vertAlign) {
            portionContentPos = (portionContentPos >= portion.getRangeEndPos(linePos) ?
                                portion.getRangeEndPos(linePos) - 1 : portionContentPos);
            portionContentPos = portionContentPos >= portion.content.length ?
                                    Math.max(portion.content.length - 1, 0) : portionContentPos;
            const element = portion.content[portionContentPos];
            if (element) {
                if (!element.isImage() && !element.isMedEquation()) {
                    res.y = portion.content[portionContentPos].positionY;
                } else if (line.metrics.textHeight > (element as any).height) {
                    res.y = portion.content[portionContentPos].positionY + (element as any).height;
                }
            }
        }

        return res;
    }

    /**
     * 获取给定位置处的文本属性
     * @param contentPos
     */
    public getTextProperty( contentPos?: ParagraphContentPos ): TextProperty {
        contentPos = ( undefined === contentPos ? this.getParaContentPos(false, false) : contentPos );
        const curPos = contentPos.get(0);

        return this.content[curPos].getTextProperty(contentPos);
    }

    public getDirectTextProperty(): TextProperty {
        let textPr = null;

        if ( true === this.selection.bUse ) {
            let startPos = this.selection.startPos;
            let endPos = this.selection.endPos;

            if ( startPos > endPos ) {
                startPos = this.selection.endPos;
                endPos = this.selection.startPos;
            }

            while ( true === this.content[startPos].isSelectionEmpty(false) && startPos < endPos ) {
                startPos++;
            }

            textPr = this.content[startPos].getDirectTextProperty();
        } else {
            textPr = this.content[this.curPos.contentPos].getDirectTextProperty();
        }

        return textPr;
    }

    /**
     * 根据属性集合进行数据收集
     * @param textPros 需要查询的属性集合
     * @return 是否结束进行查询
     */
    public getSelectedTextProperty(textPros: ITextProperty): boolean {
        const keys = Object.keys(textPros)
            .filter((key) => textPros[key] !== null);

        if (keys.length === 0) {
            return true;
        }

        let startPos: number;
        let endPos: number;
        if (this.bApplyToAll === true) {
            startPos = 0;
            endPos = this.content.length - 1;
        } else {
            const selection = this.selection;
            startPos = selection.startPos;
            endPos = selection.endPos;
            if (startPos > endPos) {
                const pos = startPos;
                startPos = endPos;
                endPos = pos;
            }
        }

        const contents = this.content;
        for (let index = startPos; index <= endPos; index++) {
            const item = contents[index];
            const len = item.content.length;
            if (len === 0 || len === 1 && item.content[0].type === ParaElementType.ParaEnd) {
                continue;
            }
            // let start: number;
            // let end: number;
            if (this.bApplyToAll !== true) {
                const selection = item.selection;
                if (selection.startPos === selection.endPos) { // 对选中进行检查
                    continue;
                }
            }

            // 遍历属性进行对比
            const textProperty = item.textProperty;
            for (let keyIndex = keys.length - 1; keyIndex > -1; keyIndex--) {
                const key = keys[keyIndex];
                const value = textPros[key];
                if (value === undefined ) {
                    textPros[key] = textProperty[key];
                } else if (value !== textProperty[key]) {
                    textPros[key] = null;
                    keys.splice(keyIndex, 1);
                }
            }

            if (keys.length === 0) {
                return true;
            }
        }

        return false;
    }

    /**
     * 设置段落缩进
     * @param paraIndentation
     */
    public setIndentation( paraIndentation: ParaIndentation ): boolean {
        let bChange = false;

        // if ( this.parent && this.parent.isTableCellContent() ) {
        //     return false;
        // }

        if ( !this.paraProperty && !paraIndentation ) {
            bChange = false;
            return false;
        } else if ( !paraIndentation ) {
            bChange = true;
            this.paraProperty.paraInd = null;
            return true;
        }

        const ind = this.paraProperty.paraInd;
        if ( paraIndentation.firstLine !== undefined && !isNaN(paraIndentation.firstLine)
            && paraIndentation.left !== undefined && !isNaN(paraIndentation.left) ) {
            const lines = this.lines;
            const diff = paraIndentation.left - this.paraProperty.paraInd.left;
            if ( lines && lines.length && 0 < diff
                && lines[0].ranges[0].x + getPxForMM(diff) > lines[0].ranges[0].xEnd ) {
                return false;
            }
        }

        const history = this.getHistory();

        if ( paraIndentation.firstLine !== undefined && !isNaN(paraIndentation.firstLine)
            && this.paraProperty.paraInd.firstLine !== paraIndentation.firstLine) {
            const old = this.paraProperty.paraInd.firstLine;
            const lines = this.lines;

            if (ind.left &&
                getPxForMM(ind.left + paraIndentation.firstLine) + lines[0].ranges[0].x >= lines[0].ranges[0].xEnd) {
                return false;
            }
            // this.addPropertyChange();
            if ( history ) {
                history.addChange(new ChangeParagraphIndFisrt(this, old, paraIndentation.firstLine));
            }
            this.paraProperty.paraInd.firstLine = paraIndentation.firstLine;
            bChange = true;
        }

        if (paraIndentation.left !== undefined && !isNaN(paraIndentation.left)
            && this.paraProperty.paraInd.left !== paraIndentation.left ) {
            const old = this.paraProperty.paraInd.left;
            const lines = this.lines;
            if (lines.length) {
                const diff = paraIndentation.left - old;
                if ( lines && lines.length && 0 < diff
                    && lines[0].ranges[0].x + getPxForMM(diff) > lines[0].ranges[0].xEnd ) {
                    return false;
                }

                if (ind.firstLine &&
                    getPxForMM(ind.firstLine + paraIndentation.left) +
                                lines[0].ranges[0].x >= lines[0].ranges[0].xEnd) {
                    return false;
                }
            }

            // this.addPropertyChange();
            if ( history ) {
                history.addChange(new ChangeParagraphIndLeft(this, old, paraIndentation.left));
            }
            this.paraProperty.paraInd.left = paraIndentation.left;
            bChange = true;
        }

        if (paraIndentation.right !== undefined && !isNaN(paraIndentation.right)
            && this.paraProperty.paraInd.right !== paraIndentation.right ) {
            const old = this.paraProperty.paraInd.right;

            // this.addPropertyChange();
            if ( history ) {
                history.addChange(new ChangeParagraphIndRight(this, old, paraIndentation.right));
            }
            this.paraProperty.paraInd.right = paraIndentation.right;
            bChange = true;
        }

        return bChange;
    }

    /**
     * 设置段落属性(缩进，行间距)
     * @param paragraphProperty
     */
    public setParagraphProperty( paragraphProperty: any ): boolean {
        let bChange = false;
        const paraIndentation = paragraphProperty.indentation;
        const paraLineSpace = paragraphProperty.lineSpace;

        const paraProperty = this.paraProperty;

        // indent
        bChange = this.setIndentation(paraIndentation);

        // line spacing and line spacing type
        const preparedParaSpacing = this.perpareParaSpacing(paraLineSpace);

        const history = this.getHistory();

        if (paraLineSpace.type !== undefined &&
            this.paraProperty.paraSpacing.lineSpacing !== preparedParaSpacing.lineSpacing) {
            const old = this.paraProperty.paraSpacing.lineSpacing;

            // this.addPropertyChange();
            if ( history ) {
                history.addChange(new ChangeParagraphLineSpacing(this, old, preparedParaSpacing.lineSpacing));
            }
            this.paraProperty.paraSpacing.lineSpacing = preparedParaSpacing.lineSpacing;
            bChange = true;
            for (let i = 0, count = this.content.length; i < count; i++) {
                this.content[i].recalcInfo.bMeasure = true;
            }
        }

        if (paraLineSpace.type !== undefined &&
            this.paraProperty.paraSpacing.lineSpacingType !== preparedParaSpacing.lineSpacingType) {
            const old = this.paraProperty.paraSpacing.lineSpacingType;

            // this.addPropertyChange();
            if ( history ) {
                history.addChange(new ChangeParagraphLineSpacingRule(this, old, preparedParaSpacing.lineSpacingType));
            }
            this.paraProperty.paraSpacing.lineSpacingType = preparedParaSpacing.lineSpacingType;

            // loop through all portions to set recalcInfo.bMeasure = true
            for (let i = 0, count = this.content.length; i < count; i++) {
                this.content[i].recalcInfo.bMeasure = true;
            }
            bChange = true;
        }

        if (paragraphProperty.alignment !== undefined && paragraphProperty.alignment !== paraProperty.alignment) {
            this.setParagraphAlignment(paragraphProperty.alignment);
            bChange = true;
        }

        if (paragraphProperty.bPageBreakBefore !== undefined && paragraphProperty.bPageBreakBefore
                !== paraProperty.bPageBreakBefore) {
            this.addPageBreak(paragraphProperty.bPageBreakBefore);
            bChange = true;
        }

        if ( null != paragraphProperty.bWordWrap &&
            paragraphProperty.bWordWrap !== paraProperty.bWordWrap ) {
            this.setWestCharBreakAttribute(paragraphProperty.bWordWrap);
            bChange = true;
        }

        if ( bChange ) {
            this.parent.setDirty();
            // this.parent.docRecalState.reset();
            // this.parent.recalculate();
        }
        return bChange;
    }

    public setParagraphProperty2(para: Paragraph): void {
        const paragraphProperty = para.paraProperty.copy();
        this.setIndentation(paragraphProperty.paraInd);
        this.setParagraphAlignment(paragraphProperty.alignment);
        this.setWestCharBreakAttribute(paragraphProperty.bWordWrap);
        this.setParagraphSpacing(paragraphProperty);
    }

    public setParagraphSpacing(paragraphProperty: ParaProperty): void {
        const spacing = paragraphProperty.paraSpacing;
        const oldSpacing = this.paraProperty.paraSpacing;
        const history = this.getHistory();

        if (spacing.lineSpacing !== oldSpacing.lineSpacing) {

            if ( history ) {
                history.addChange(new ChangeParagraphLineSpacing(this, oldSpacing.lineSpacing, spacing.lineSpacing));
            }
            oldSpacing.lineSpacing = spacing.lineSpacing;
        }

        if (spacing.lineSpacingType !== oldSpacing.lineSpacingType) {

            if ( history ) {
                history.addChange(new ChangeParagraphLineSpacingRule(
                    this, oldSpacing.lineSpacingType, spacing.lineSpacingType));
            }

            oldSpacing.lineSpacingType = spacing.lineSpacingType;
        }
    }

    /**
     * 计算单倍行距的cm
     * @param textHeight
     * @param singleLinespacing
     */
    public calculateSingleLineSpacing(textHeight: number, singleLinespacing: number): number {
        const pXsingleLinespacing = singleLinespacing * textHeight;

        // convert px to cm
        return getMMFromPx(pXsingleLinespacing) / 10;
    }

    /**
     * 设置段落对齐方式
     * @param alignment
     */
    public setParagraphAlignment( alignment: number ): boolean {
        if ( alignment === this.paraProperty.alignment ) {
            return false;
        }

        // this.addPropertyChange();

        const history = this.getHistory();
        if ( history ) {
            history.addChange(new ChangeParagraphAlign(this, this.paraProperty.alignment, alignment));
        }

        this.paraProperty.alignment = alignment;
        this.parent.setDirty();
        return true;
    }

    public getSelection(): ParagraphSelection {
        return this.selection;
    }

    /**
     * 根据起始pos对pos进行截取
     * @param startPos 开始portion位置
     * @param endPos 结束portion位置
     */
    public getPortions(startPos: number, endPos: number): ParaPortion[] {
        return this.content.slice(startPos, endPos + 1);
    }

    /**
     * @param item: 新增的段落
     */
    public getSelectedContent(item: Paragraph, names: string[], bKeepHalfStructBorder: boolean = false,
                              option?: any): void {
        let startPos: number;
        let endPos: number;
        if (this.bApplyToAll === true) {
            startPos = 0;
            endPos = this.content.length - 1;
        } else {
            startPos = this.getParaContentPos(true, true)
                                                        .get(0);
            endPos = this.getParaContentPos(true, false)
                                                        .get(0);
            if (startPos > endPos) {
                const pos = startPos;
                startPos = endPos;
                endPos = pos;
            }
        }
        // 在前面进行判断，不需要里面进行判断
        // const doc = this.getDocument();
        // const manager = doc.getNewControlManager();
        // const options = { name: null, names: manager.getUnCopyNames(this)};
        const options: any = {bCopy: option && option.bCopy};
        item.bHidden = this.isHidden();

        // item.content = []; // 清空段落标志
        // portion contents
        for (let i = startPos; i <= endPos; i++) {
            const por = this.content[i];
            if (por.type === ParaElementType.ParaEnd) {
                // item.addToContent(item.content.length - 1, newPor);
                continue;
            }
            const newPor = por.getSelectdPortionContent(item, names, bKeepHalfStructBorder, options);
            if (!newPor || !newPor.content.length) {
                continue;
            }
            const len = item.content.length - 1;
            item.addToContent(len, newPor);
        }
        this.copyProperty(item);
    }

    /**
     * 获取选择区域的边界
     */
    public getSelectionBounds(bStart: boolean, bMultis: boolean): ISelectionsPara  {
        const result = {
            startLine: -1,
            startLineX: 0,
            startLineWidth: 0,
            endLine: -1,
            endLineX: 0,
            endLineWidth: 0,
        };

        const direction = this.getSelectionDirection();
        const bSelectedStart = ( true === bStart && true === bMultis );
        const bPrintNumbering = (this.logicDocument.getPrintStatus() && this.numbering
                                && (this.isSelectedAll(false) || (!bStart && bMultis)));

        // get start portion
        // const startPortion = this.content[this.selection.startPos];
        // const endPortion = this.content[this.selection.endPos];

        let startLine = -1;
        let endLine = -1;

        let startPos = this.selection.startPos;
        let endPos   = this.selection.endPos;

        if (startPos > endPos) {
            startPos = this.selection.endPos;
            endPos   = this.selection.startPos;
        }

        const startPortion = this.content[startPos];
        const endPortion = this.content[endPos];

        // get选中位置的portion所在行号
        startLine = startPortion.getLineByPos(-1 === direction ?
            startPortion.selection.endPos : startPortion.selection.startPos);
        endLine = endPortion.getLineByPos(-1 === direction ?
            endPortion.selection.startPos : endPortion.selection.endPos);

        // if ( -1 === direction ) {
        //     const temp = startLine;
        //     startLine = endLine;
        //     endLine = temp;
        // }

        if ( startLine > endLine ) {
            const temp = startLine;
            startLine = endLine;
            endLine = temp;
        }

        // 记录选中的首尾行号
        result.startLine = startLine;
        result.endLine = endLine;

        // 选择的首行
        const index = startLine;

        const drawSelelction = {
            startPos: -1,
            width: 0,
            bFindStart: true,
        };

        let startLineX = 0;
        let startLineWidth = 0;

        // 是否选中多行，如果选中多行，则首先记录选中的第一行的相关位置信息
        if ( index !== endLine ) {
            const curLine = this.lines[index];

            drawSelelction.startPos = curLine.ranges[0].xVisible;
            drawSelelction.width = 0;

            const curRangeStartPos = curLine.getStartPos();
            const curRangeEndPos = curLine.getEndPos();

            // if ( startPos > rangeEndPos || endPos < rangeStartPos )
            //     continue;

            for (let curPos = curRangeStartPos; curPos <= curRangeEndPos; curPos++) {
                const item = this.content[curPos];
                item.getSelectionBounds(index, drawSelelction, direction);
            }

            startLineX = drawSelelction.startPos;
            startLineWidth = curLine.ranges[0].xEnd - startLineX;
        }

        if ( -1 !== endLine ) {
            // todo: 光标拖动选择时，移动到页面外，光标的定位会发生错误
        }
        const line = this.lines[endLine];
        if (!line) {
            return result;
        }

        drawSelelction.startPos = line.ranges[0].xVisible;
        drawSelelction.width = 0;

        const rangeStartPos = line.getStartPos();
        const rangeEndPos = line.getEndPos();

        for (let curPos = rangeStartPos; curPos <= rangeEndPos; curPos++) {
            const item = this.content[curPos];
            item.getSelectionBounds(endLine, drawSelelction, direction);
        }

        // if ( 0.001 < drawSelelction.width) {
        //     result2.bounds.push(drawSelelction);
        // }

        let endLineX = 0;
        let endLineWidth = 0;

        drawSelelction.width = (bSelectedStart ? line.ranges[0].xEnd - drawSelelction.startPos : drawSelelction.width);

        // 如果只选中一行，则 result.endLineX = 0， result.endLineWidth = 0
        if ( index === endLine ) {
            startLineX = drawSelelction.startPos;
            startLineWidth = drawSelelction.width;
        } else {
            endLineX = drawSelelction.startPos;
            endLineWidth = drawSelelction.width;
        }

        result.startLineX = !bPrintNumbering ? startLineX : this.x;
        result.startLineWidth = !bPrintNumbering ? startLineWidth : startLineWidth + (startLineX - this.x);
        result.endLineX = endLineX;
        result.endLineWidth = endLineWidth;

        // console.log(result);
        // console.log(startLine, endLine)
        return result;
    }

    public setSectionPr(sectProperty: SectionProperty): void {
        this.sectProperty = sectProperty;
    }

    /**
     * 设置选择区域的开始位置
     * @param curPage
     * @param pointX
     * @param pointY
     */
    public setSelectionStart( pointX: number, pointY: number, curPage: number, mouseEvent?: IMouseEvent,
                              bTableBorder?: boolean ): void {
        // 删除旧的选择
        if ( true === this.selection.bUse ) {
            this.removeSelection();
        }

        const searchPos = this.getParaContentPosByXY(curPage, pointX, pointY, false, true);
        const searchPos2 = this.getParaContentPosByXY(curPage, pointX, pointY, false, false);

        this.selection.bUse = true;

        const imageFlags = this.logicDocument.getImageFlags();
        if (!imageFlags.isImageOnClick) {
            this.selection.bStart = true;
            this.selection.flag = SelectionFlagType.Common;
        } else {
            this.selection.flag = SelectionFlagType.Drawing;
        }

        // console.log(searchPos2)
        // 设置当前光标位置
        this.setParaContentPos(searchPos2.pos, searchPos2.line);
        // console.log(circularParse(circularStringify(this)));

        // 设置选择位置
        this.setSelectionContentPos(searchPos.pos, searchPos.pos);
    }

    /**
     * 设置选择区域的结束位置
     * @param curPage
     * @param pointX
     * @param pointY
     * @param mouseEvent
     */
    public setSelectionEnd( pointX: number, pointY: number, curPage: number, mouseEvent?: IMouseEvent,
                            bTableBorder?: boolean ): void {
        this.curPos.realX = pointX;
        this.curPos.realY = pointY;

        const searchPos = this.getParaContentPosByXY(curPage, pointX, pointY, false, true);
        const searchPos2 = this.getParaContentPosByXY(curPage, pointX, pointY, false, false);

        // 设置当前光标位置
        this.setParaContentPos(searchPos2.pos, searchPos2.line);

        const startPos = this.getParaContentPos(true, true);
        const endPos = this.getParaContentPos(true, false);

        // 设置选择位置
        this.setSelectionContentPos(startPos, searchPos.pos);

        const selectionStartPos = this.getParaContentPos(true, true);
        const selectionEndPos = this.getParaContentPos(true, false);

        // todo：当前段落，在相同位置多次单击鼠标
        if ( 0 === selectionStartPos.compare(selectionEndPos) && mouseEvent
            && MouseEventType.MouseButtonUp === mouseEvent.type ) {
            if (  1 <= mouseEvent.clickCount ) {
                this.removeSelection();
            }
        }
    }

    public setSelectionStart2(): void {
        const contentPos = this.getParaContentPos(true, true);
        const searchPos: IParagraphSearchPos = {
            bFound: false,
            bSelection: false,
            pos: new ParagraphContentPos(),
        };

        this.getLeftPos(searchPos, contentPos);
        if ( true === searchPos.bFound && this.content[searchPos.pos.get(0)].isNewControlStart() ) {
            // 设置当前光标位置
            this.setParaContentPos(searchPos.pos, 0);

            // 设置选择位置
            this.setSelectionContentPos(searchPos.pos, this.getParaContentPos(true, false));
        }

    }

    public setSelectionStart3(): void {
        const contentPos = this.getParaContentPos(true, false);
        const searchPos: IParagraphSearchPos = {
            bFound: false,
            bSelection: false,
            pos: new ParagraphContentPos(),
        };

        this.getRightPos(searchPos, contentPos);
        if ( true === searchPos.bFound && this.content[searchPos.pos.get(0)].isNewControlEnd() ) {
            // 设置当前光标位置
            this.setParaContentPos(searchPos.pos, 0);

            // 设置选择位置
            this.setSelectionContentPos(this.getParaContentPos(true, true), searchPos.pos);
        }

    }

    /**
     * 删除paragraph的选择信息并调用portion的选择删除函数
     */
    public removeSelection(): void {
        if (true === this.selection.bUse) {
            let startPos = this.selection.startPos;
            let endPos   = this.selection.endPos;

            if (startPos > endPos) {
                startPos = this.selection.endPos;
                endPos   = this.selection.startPos;
            }

            startPos = Math.max(0, startPos);
            endPos   = Math.min(this.content.length - 1, endPos);

            for (let curPos = startPos; curPos <= endPos; curPos++) {
                this.content[curPos].removeSelection();
            }
        }

        this.selection.bUse      = false;
        this.selection.bStart    = false;
        this.selection.startPos = 0;
        this.selection.endPos   = 0;
    }

    /**
     * 获取段落的被选择的行数组
     * @param selections
     * @param bParaEnd
     */
    public getSelectedLines( selections: ISelectionsPara , bParaEnd?: boolean ): IDrawSelectionsLine[] {

        const result: IDrawSelectionsLine[] = [];
        const bHeaderFooter = (DocumentSectionType.Document !== this.parent.getDocumentSectionType());
        const curPage = this.logicDocument.getCurPage();

        if ( null != selections) {
            const start = selections.startLine;
            const end = selections.endLine;

            // 当前为段落最后一行，或者段落只有一行
            let width = selections.startLineWidth;
            if ( start === this.lines.length - 1 ) { // ( 0.001 < selections.startLineWidth ) {
                width = ( true === bParaEnd ? ( this.lines[start].ranges[0].xEnd - selections.startLineX )
                                                : width );
            }
            let pageIndex = bHeaderFooter ? curPage : this.getAbsolutePageByLineId(start);

            result.push({line: this.lines[start], x: selections.startLineX, y: this.lines[start].top,
                        width: Math.abs(width),
                        height: this.lines[start].bottom - this.lines[start].top,
                        paragraph: this.index, pageIndex});

            if ( start !== end ) {
                for (let index = start + 1; index < end; index++) {
                    // let curPage = this.getPageByLine(index);
                    const line = this.lines[index];
                    const x = line.ranges[0].xVisible;
                    const curWidth = line.ranges[0].xEnd - x;

                    pageIndex = bHeaderFooter ? curPage : this.getAbsolutePageByLineId(index);
                    result.push({line, x, y: line.top, width: Math.abs(curWidth),
                        height: line.bottom - line.top, paragraph: this.index,
                        pageIndex: this.getAbsolutePageByLineId(index)});
                }

                // 选中的最后一行是否有内容被选中：排除光标在行首或行尾，但是没有选中任何内容的情况
                if ( 0.001 < selections.endLineWidth ) {
                    // 当前行为段落末行，且下一段落也被选中
                    const line = this.lines[end];
                    const range = this.lines[end].ranges[0];
                    const curWidth = ( true === bParaEnd ? ( range.xEnd - range.xVisible ) : selections.endLineWidth );
                    pageIndex = bHeaderFooter ? curPage : this.getAbsolutePageByLineId(end);
                    result.push({line: this.lines[end], x: selections.endLineX, y: line.top,
                                width: Math.abs(curWidth), height: line.bottom - line.top,
                                paragraph: this.index, pageIndex: this.getAbsolutePageByLineId(end)});
                }
            }
        } else {
            const bPrintNumbering = (this.logicDocument.getPrintStatus() && this.numbering);

            // 此段落被全部选中
            for (let index = 0, linesCount = this.lines.length; index < linesCount; index++) {
                const curParaPage = this.getPageByLine(index);
                const line = this.lines[index];
                const x = !(0 === index && bPrintNumbering) ? line.ranges[0].xVisible : this.pages[curParaPage].x;
                const width = this.pages[curParaPage].xLimit - x; // line.ranges[0].width;
                const pageIndex = bHeaderFooter ? curPage : this.getAbsolutePageByLineId(index);

                result.push({line, x, y: line.top, width: Math.abs(width), height: line.bottom - line.top,
                    paragraph: this.index, pageIndex});
            }
        }

        return result;
    }

    /**
     *  获取开始选择位置所在page id
     */
    public getPageIndexBySelectionStart(): number {

        const startPortion = this.content[this.selection.startPos];
        const lineSelectionStart = startPortion.getLineByPos(startPortion.selection.startPos);
        const paraPages = this.pages;

        for (let i = 0; i < paraPages.length; i++) {
            if (lineSelectionStart >= paraPages[i].startLine && lineSelectionStart <= paraPages[i].endLine) {
                return this.pageNum + i;
            }
        }

        return -1;
    }

    /**
     * 获取选择区域的开始元素的坐标
     */
    // public getSeletionStartPos( direction: number ) {
    //     const portion = this.content[this.selection.startPos];
    //     let portionContentPos = portion.selection.startPos;
    //     const length = portion.content.length;

    //     // 当光标开始位置在两个portion之间时，startPos = portion.content.length
    //     // 需要进行额外处理
    //     if ( portionContentPos >= length ) {
    //       return {
    //         x: portion.content[length - 1].positionX + portion.content[length - 1].widthVisible,
    //         y: portion.content[length - 1].positionY,
    //       };
    //     } else {
    //         if ( 1 === direction ) {
    //           return {
    //             x: portion.content[portionContentPos].positionX,
    //             y: portion.content[portionContentPos].positionY,
    //           };
    //         } else {
    //           portionContentPos = 0 === portionContentPos ? 0 : portionContentPos - 1;
    //           return {
    //             x: portion.content[portionContentPos].positionX + portion.content[portionContentPos].widthVisible,
    //             y: portion.content[portionContentPos].positionY,
    //           };
    //         }
    //     }
    // }

    /**
     * 选择区域是否为空
     * @param bContainParaEnd 是否包含段落结束符
     */
    public isSelectionEmpty( bContainParaEnd?: boolean ): boolean {
        if ( undefined === bContainParaEnd ) {
            bContainParaEnd = true;
        }

        if ( true === this.selection.bUse ) {
            let startPos = this.selection.startPos;
            let endPos = this.selection.endPos;

            if ( startPos > endPos ) {
                startPos = this.selection.endPos;
                endPos = this.selection.startPos;
            }

            for ( let curPos = startPos; curPos <= endPos; curPos++ ) {
                if ( true !== this.content[curPos].isSelectionEmpty(bContainParaEnd) ) {
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * 是否为选择状态
     */
    public isSelectionUse(): boolean {
        return this.selection.bUse;
    }

    /**
     * 设置选择状态
     * @param bUse 是否在选择
     */
    public setSelectionUse( bUse: boolean ): void {
        if ( true === bUse ) {
            this.selection.bUse = bUse;
        } else {
            this.removeSelection();
        }
    }

    /**
     * 停止选择
     */
    public stopSelection(): void {
        this.selection.bStart = false;
    }

    /**
     * 是否从段落开始位置选择
     */
    public isSelectionFromStart(): boolean {
        if ( true === this.selection.bUse ) {
            let startPos = this.getParaContentPos(true, true);
            const endPos = this.getParaContentPos(true, false);

            if ( startPos.compare(endPos) > 0 ) {
                startPos = endPos;
            }

            if ( true !== this.isCursorAtBegin(startPos) ) {
                return false;
            }

            return true;
        }

        return false;
    }

    /**
     * 检查段落是否选中段尾结束符
     * 在段尾的空portion不会被标记，只包含段落结束符的portion也不会被标记，但是portion会指向结束符
     * 正向选择：只选中结束符，portion是结束符左边不为空的portion
     * 反向选择：只选中结束符，portion为包含结束符的portion
     * @param bContainParaEnd
     */
    public isSelectionParaEnd( bContainParaEnd: boolean ): boolean {
        if ( true === bContainParaEnd ) {
            return true;
        }

        if ( true !== this.selection.bUse ) {
            return false;
        }

        const startPos = this.getParaContentPos(true, true);
        let endPos = this.getParaContentPos(true, false);

        if ( startPos.compare(endPos) > 0 ) {
            endPos = startPos;
        }

        const searchPos: IParagraphSearchPos = {
            bFound: false,
            bSelection: false,
            pos: new ParagraphContentPos(),
        };

        return false === this.getRightPos(searchPos, endPos);
    }

    /**
     * 是否选中整个段落
     * @param bContainParaEnd
     */
    public isSelectedAll( bContainParaEnd: boolean ): boolean {

        const bStart = this.isSelectionFromStart();
        const bEnd = ( true === bContainParaEnd ? true : this.isSelectionParaEnd(bContainParaEnd) );

        return ( true === bStart && true === bEnd );
    }

    /**
     * 在当前光标位置开始选择
     */
    public startSelectionByCurPos(): void {
        const contentPos = this.getParaContentPos(false, false);
        this.selection.bStart = false;
        this.selection.bUse = true;

        this.setSelectionContentPos(contentPos, contentPos);
    }

    /**
     * 将select的开头/结尾设置为段落的开头/结尾。
     * @param bSelectStart 是否是设置select的开头
     * @param bEnd 是否为select区域的结尾
     */
    public setSelectionBeginEnd( bSelectStart: boolean, bEnd: boolean ): void {
        const contentPos = ( true === bEnd ? this.getStartPos() : this.getEndPos(true) );

        if ( true === bSelectStart ) {
            this.setSelectionContentPos(contentPos, this.getParaContentPos(true, false));
        } else {
            this.setSelectionContentPos(this.getParaContentPos(true, true), contentPos);
        }
    }

    /**
     * 选择整个段落
     * @param direction 选择方向
     */
    public selectAll( direction: number ): void {
        this.selection.bUse = true;

        let startPos = null;
        let endPos = null;

        if ( -1 === direction ) {
            startPos = this.getEndPos(true);
            endPos = this.getStartPos();
        } else {
            startPos = this.getStartPos();
            endPos = this.getEndPos(true);
        }

        this.setSelectionContentPos(startPos, endPos);
    }

    public getHidden(): boolean {
        return this.bHidden;
    }

    public isHidden(): boolean {
        return this.bHidden && !this.getDocument()
            .isDesignModel() || this.isRevisionHide();
    }

    public setHidden(bHidden: boolean): void {
        const history = this.getHistory();
        if ( history ) {
            history.addChange(new ChangeParagraphHidden(this, this.bHidden, bHidden));
        }

        this.bHidden = bHidden;
    }

    public setPortionsHidden(bHidden: boolean): void {
        if (bHidden === true) {
            return;
        }
        const contents = this.content;
        if (contents[0] && contents[0].isHidden() === bHidden) {
            return;
        }
        for (let index = 0, len = contents.length - 1; index < len; index++) {
            contents[index].setHidden(bHidden);
        }
    }

    public isHideEndNewcontrol(portion: ParaPortion): boolean {
        const contents = this.content;
        const len = contents.length - 1;
        for (let index = len; index >= 0; index--) {
            const content = contents[index];
            if (index === len) {
                if (content.content.length > 1) {
                    return false;
                }
            } else if (content === portion) {
                return true;
            } else if (content.content.length > 0) {
                break;
            }
        }

        return false;
    }

    /*
     * 判断portion是否为段落第一个非空portion
     */
    public isHideStartNewcontrol(portion: ParaPortion): boolean {
      const firstNotEmpty = this.content.find((content) => !(content instanceof ParaPortion) || !content.isEmpty(true));
      return firstNotEmpty && firstNotEmpty === portion;
    }

    public checkPosInSelection( pageIndex: number, pointX: number, pointY: number ): boolean {
        if ( 0 > pageIndex || this.pages.length <= pageIndex || true !== this.selection.bUse ) {
            return false;
        }

        const searchPos = this.getParaContentPosByXY(pageIndex, pointX, pointY, false, false);
        return false;
    }

    /**
     * 判断当前选择区域的方向
     */
    public getSelectionDirection(): number {
        if ( true !== this.selection.bUse ) {
            return 0;
        }

        if ( this.selection.startPos < this.selection.endPos ) {
            return 1;
        } else if ( this.selection.startPos > this.selection.endPos ) {
            return -1;
        }

        return this.content[this.selection.startPos].getSelectionDirection();
    }

    /**
     * 获取当前光标所在段落的portion
     * @param bSelection
     * @param bStart
     */
    public getParaContentPos(bSelection?: boolean, bStart?: boolean): ParagraphContentPos {
        const contentPos = new ParagraphContentPos();
        // console.log(this.curPos.contentPos)
        // 判断是否是内容选择状态
        const pos = true !== bSelection ? this.curPos.contentPos :
                    ( false !== bStart ? this.selection.startPos : this.selection.endPos);
        contentPos.add(pos);

        if ( 0 > pos || this.content.length <= pos ) {
            return contentPos;
        }

        // 获取光标所在portion的内容位置
        this.content[pos].getParaContentPos(contentPos, bSelection, bStart);
        return contentPos;
    }

    /**
     *  获取当前光标所在段落的portion(内部会产生新增portion的动作, 针对批注锚点Portion)
     * @param contentPos
     */
    public getElementByPos( contentPos: ParagraphContentPos, bCompositeInput: boolean = false ): ParaPortion {
        if ( 1 > contentPos.depth ) {
            return undefined;
        }

        let curPortionPos = contentPos.get(0);
        let curPortion = this.content[curPortionPos];

        if ( true === bCompositeInput && 0 !== curPortionPos && curPortion.isSingleParaEndPortion() ) {
            const curContentPos = new ParagraphContentPos();
            curContentPos.clear();
            curContentPos.update(curPortionPos, 0);
            curContentPos.update(0, 1);

            const searchPos = {
                pos: new ParagraphContentPos(),
                bFound: false,
                bSelection: false,
            };

            if ( this.getLeftPos(searchPos, curContentPos) && this.content[searchPos.pos.get(0)] ) {
                curPortionPos = searchPos.pos.get(0);
                curPortion = this.content[curPortionPos];
                curPortion.portionContentPos = curPortion.content.length;
            }
        }

        if ( true === bCompositeInput
            && ( curPortion.isRegionTitle(false) && curPortion.isEnd() ||
            true === this.isBetweenTwoNewControls(contentPos)
                    || true === curPortion.isAfterNewControlEndBorder() ) ) {
            const newPortion = new ParaPortion(this);
            if ( false === curPortion.isRegionTitle(false) ) {
                newPortion.textProperty = curPortion.textProperty.copy();
            } else if ( this.logicDocument && this.logicDocument.getRegionContentDefaultFont() ) {
                newPortion.textProperty = this.logicDocument.getRegionContentDefaultFont()
                                                                .copy();
            }

            const nextPortion = curPortion.getNextPortion();
            if (nextPortion && nextPortion.isComment() && !nextPortion.isStartComment()) {
                curPortionPos++;
            }

            this.addToContent(curPortionPos + 1, newPortion);
            curPortionPos++;

            const curContentPos = new ParagraphContentPos();
            curContentPos.add(curPortionPos);
            curContentPos.add(0);
            this.setParaContentPos(curContentPos, 0);
        } else if ( true === bCompositeInput ) {
            const newControl = this.getCursorInNewControl();
            const bStartBorder = curPortion.isNewControlStart();

            // text is section leaf at beginning paragragh (section contains paragraghs more than one) 
            const bSameNewControl = (bStartBorder &&
                (!newControl || newControl.getNewControlName() === curPortion.getStartNewControlName()));
            if ( newControl && ( true === bStartBorder
                                || true === newControl.getStartBorderPortion().bPlaceHolder
                                || ( true === this.isCursorAtBegin() && true === curPortion.isNewControlEnd())) ) {
                const startBorderPortion = newControl.getStartBorderPortion();
                const endBorderPortion = newControl.getEndBorderPortion();
                const placeHolder = newControl.getPlaceHolder();
                const bTextBox = (newControl.isNewTextBox() || newControl.isNewSection());
                const enableDefaultStyleAfterTitle = getTheme()?.NewControl?.EnableDefaultStyleAfterTitle;

                if ( bStartBorder || endBorderPortion === curPortion ) {
                    curPortionPos = (bStartBorder && bSameNewControl ? curPortionPos + 1 : curPortionPos);

                    const portion = new ParaPortion(this);
                    portion.bHidden = startBorderPortion.bHidden;

                    if (bStartBorder && bTextBox && enableDefaultStyleAfterTitle && newControl.getTitle()) {
                        portion.textProperty = new TextProperty(getDefaultFont(this.getDocument()));
                    } else {
                        portion.textProperty = startBorderPortion.textProperty.copy();
                    }
                    this.addToContent(curPortionPos, portion);
                } else if (bTextBox && startBorderPortion.bPlaceHolder) {
                    const portion = new ParaPortion(this);
                    const textProperty = placeHolder.textProperty.copy();
                    textProperty.color = NewControlDefaultSetting.InputContentColor;
                    portion.textProperty = textProperty;
                    this.addToContent(curPortionPos, portion);
                    curPortionPos++;
                }

                if ( true === startBorderPortion.bPlaceHolder ) {
                    startBorderPortion.setPlaceHolder(false);
                    endBorderPortion.setPlaceHolder(false);

                    const textProperty = placeHolder.textProperty.copy();
                    textProperty.color = NewControlDefaultSetting.InputContentColor;
                    // if (bStartBorder && bTextBox) {
                    //     this.content[curPortionPos].textProperty = textProperty.copy();
                    // }
                    placeHolder.removeFromContent(0, placeHolder.content.length);
                    placeHolder.setTextProperty(textProperty);
                }
                curPortionPos = this.fixCurPosJumpingComment(curPortionPos);

                const curContentPos = new ParagraphContentPos();
                curContentPos.add(curPortionPos);
                curContentPos.add(0);
                this.setParaContentPos(curContentPos, 0);

                return this.content[curPortionPos];
            } else if ( bStartBorder && null == newControl && this.isCursorAtBegin() || curPortion.isStartComment() ) {
                const newPortion = new ParaPortion(this);
                newPortion.textProperty = curPortion.textProperty.copy();

                this.addToContent(curPortionPos, newPortion);

                const curContentPos = new ParagraphContentPos();
                curContentPos.add(curPortionPos);
                curContentPos.add(0);
                this.setParaContentPos(curContentPos, 0);
            } else if ( newControl && curPortion.isNewControlEnd() ) {
                curPortionPos--;
                const beforePortion = this.content[curPortionPos];

                if ( beforePortion ) {
                    // if ( beforePortion.isNewControlStart() || beforePortion.isNewControlEnd() ) {
                    //     const newPortion = new ParaPortion(this);
                    //     newPortion.textProperty = beforePortion.textProperty.copy();

                    //     this.addToContent(curPortionPos, newPortion);

                    //     const curContentPos = new ParagraphContentPos();
                    //     curContentPos.add(curPortionPos);
                    //     curContentPos.add(0);
                    //     this.setParaContentPos(curContentPos, 0);
                    // } else {
                    const curContentPos = new ParagraphContentPos();
                    curContentPos.add(curPortionPos);
                    curContentPos.add(this.content[curPortionPos].content.length);
                    this.setParaContentPos(curContentPos, 0);
                    // }
                }
            } else if ( curPortion.isSingleParaEndPortion() ) {
                const newPortion = new ParaPortion(this);
                newPortion.textProperty = curPortion.textProperty.copy();
                this.addToContent(curPortionPos, newPortion);

                const curContentPos = new ParagraphContentPos();
                curContentPos.add(curPortionPos);
                curContentPos.add(0);
                this.setParaContentPos(curContentPos, 0);
            } else if (curPortion.paragraph.isTimeCell()) {
                const para = curPortion.paragraph;
                const contents = para.content;
                const index = contents.findIndex((por) => por === curPortion);
                if (index % 2 === 1) {
                    if (curPortion.portionContentPos === 0) {
                        curPortionPos--;
                    } else {
                        curPortionPos++;
                    }
                    this.curPos.contentPos = curPortionPos;
                }
            }
            // 遇到ParaComment时调整位置
            curPortionPos = this.fixCurPosJumpingComment(curPortionPos);
        }

        if (curPortionPos >= this.content.length) {
            return null;
        }
        return this.content[curPortionPos];
    }

    public getContentByPos(pos: any): any {
        const index = pos.shift();

        return (null == index ? this : this.content[index]);
    }

    public write(bAdd: boolean): any {
        const pos = this.getCurContentPosInDoc();
        pos.splice(pos.getDepth() - 1, 2);
        const newControl = (false === bAdd ? this.getCursorInNewControl() : null);

        return {
            pos: pos.data.toString(),
            newControlName: newControl ? newControl.getNewControlName() : null,
            // lines: this.lines.toString(),
            // content: this.getText(),
            // contentType: this.content[0] ? this.content[0].getType() : -1,
            // selection: {
            //     bUse: this.selection.bUse,
            //     startPos: this.selection.startPos,
            //     endPos: this.selection.endPos,
            // },
            // newControl: {
            //     name: name,
            //     bPlaceHolder: this.bPlaceHolder,
            //     type:
            // },
        };
    }

    /**
     * 在指定位置插入Portion
     * @param portion 需要插入的Portion
     * @param pos 插入的位置
     */
    public insertPortion(portion: ParaPortion, pos: ParagraphContentPos): ParagraphContentPos {
        const contentPos = pos.shift();
        const curPortion = this.content[contentPos];
        let newContentPos = contentPos;
        // if (curPortion.isNewControlStart() || curPortion.isParaEndPortion()) {
        //     // before: newControl start, paraEnd
        //     this.addToContent(newContentPos, portion);
        //     if (newContentPos === 0) {
        //         // 用于段首占位
        //         this.addToContent(newContentPos, new ParaPortion(this));
        //     }
        // } else if (curPortion.isNewControlEnd()) {
        //     // after: newControl end
        //     this.addToContent(++ newContentPos, portion);
        // } else 
        if (curPortion.content.length) {
            const splitedPortion = curPortion.splitPortion(pos.shift());
            this.addToContent(++newContentPos, portion);
            if (!splitedPortion.isEmpty(true)) {
                this.addToContent(++ newContentPos, splitedPortion);
                const bRegionTitle = splitedPortion.isRegionTitle(false);
                let region = bRegionTitle && (this.parent as any)?.parent;
                if (region) {
                    region.updateSplitTitlePortion(splitedPortion);
                }
            }
        } else {
            this.addToContent(++ newContentPos, portion);
        }
        if (newContentPos >= this.content.length) {
            newContentPos = this.content.length - 1;
        }
        if (this.curPos.contentPos >= contentPos) {
            this.curPos.contentPos = newContentPos;
        }
        const newPos = new ParagraphContentPos();
        newPos.add(newContentPos);
        newPos.add(0);
        return newPos;
    }

    public insertText(text: string): void {
        const pos = this.getCurContentPosInDoc();
        const depth = pos.getDepth();
        const portionPos = new ParagraphContentPos();
        const portionIndex = pos.get(depth - 1);
        const contentIndex = pos.get(depth);
        portionPos.add(portionIndex);
        portionPos.add(contentIndex);
        const oldPortion = this.content[portionIndex];
        let portion = this.getElementByPos(portionPos, true);

        const trackPortion = portion.checkTrackRevisionsBeforeAdd();
        if (trackPortion) {
            portion = trackPortion;
            portion.makeElementCurrentPos();
        } else if ( this.logicDocument.isTrackRevisions() && !trackPortion && !this.isHeaderFooter(false) ) {
            message.info('当前权限较低！');
            // this.bCanDirty = true;
            return ;
        }

        let index: number = 0;
        if (oldPortion === portion) {
            index = contentIndex;
        }
        portion.addText(text, null, index);
        index = Math.min(index + text.length, portion.content.length);
        portion.portionContentPos = index;
    }

    public addText(text: string): void {
        const portion = new ParaPortion(this);
        portion.addText(text);

        this.addToContent(0, portion);
    }

    /**
     * 通过元素索引获得坐标位置
     * @param pos
     */
    public getXYByContentPos( pos: number ): ICurrentCursorPos {
      const searchPos = this.getParaContentPos();

      // this.setParaContentPos(pos, searchPos.line);
      return this.recalcCurPos(-1, false);
    }

    /**
     * 获取ParaPos
     * @param contentPos
     */
    public getParaPosByContentPos( contentPos: ParagraphContentPos ): IParaPos {
        const paraPos = this.content[contentPos.get(0)].getParaPosByContentPos(contentPos, 1);
        const curLine = paraPos.line;

        for (let index = this.pages.length - 1; index >= 0; index--) {
            const page = this.pages[index];

            if ( curLine >= page.startLine && curLine <= page.endLine ) {
                paraPos.page = index;
                return paraPos;
            }
        }

        return paraPos;
    }

    public getCurContentPosInDoc( bSelection?: boolean, bStart?: boolean ): ParagraphContentPos {
        const contentPos = new ParagraphContentPos();
        const topParent = this.parent.getTopDocument();

        switch (topParent.getDocumentSectionType()) {
            case DocumentSectionType.Header:
                contentPos.add(0);
                break;
            case DocumentSectionType.Document:
                contentPos.add(1);
                break;
            case DocumentSectionType.Footer:
                contentPos.add(2);
                break;
        }

        if (this.parent instanceof DocumentContent) {
            this.parent.getParentIndexs(contentPos);
        }

        contentPos.add(this.index);

        if ( true === this.bApplyToAll ) {
            if ( true === bStart ) {
                contentPos.add(0);
                contentPos.add(0);
            } else {
                const length = this.content.length - 1;
                contentPos.add(length);
                contentPos.add(this.content[length].content.length - 1);
            }
        } else if ( true === bSelection ) {
            let startPos = this.selection.startPos;
            let endPos = this.selection.endPos;
            const direction = this.getSelectionDirection();

            if ( -1 === direction ) {
                startPos = this.selection.endPos;
                endPos = this.selection.startPos;
            }

            if ( true === bStart ) {
                const portionContentPos = -1 === direction ?
                        this.content[startPos].selection.endPos : this.content[startPos].selection.startPos;
                contentPos.add(startPos);
                contentPos.add(portionContentPos);
            } else {
                const portionContentPos = -1 === direction ?
                        this.content[endPos].selection.startPos : this.content[endPos].selection.endPos;
                contentPos.add(endPos);
                contentPos.add(portionContentPos);
            }
        } else {
            contentPos.add(this.curPos.contentPos);
            contentPos.add(this.content[this.curPos.contentPos]?.portionContentPos);
        }

        return contentPos;
    }

    public selectTopPos(): void {
        const parent = this.parent;
        const selection = parent.selection;
        selection.bUse = true;
        selection.bStart = false;
        parent.curPos.contentPos = selection.endPos = selection.startPos = this.index;
        if (parent instanceof DocumentContent) {
            parent.selectTopPos();
        }
    }

    public setContentCurPos(): void {
        const parent = this.parent;
        parent.curPos.contentPos = this.index;

        if (parent instanceof DocumentContent) {
            parent.setContentCurPos();
        }
    }

    public addDocContentChild(options: any, cleanMode: CleanModeType): void {
        // console.log(options)
        // console.log(cleanMode)
        // this.parent = options.parent;
        // options.parent = this;
        let hasSectionWithPrintHide = false;
        if (CleanModeType.Normal === cleanMode) {
            return this.addDocContentChild2(options, cleanMode);
        }

        const structs = options.structProps;
        // const structProps = options.structProps;
        // const doc = this.getDocument();
        // const manager = doc.getNewControlManager();
        // const newContents = options.newCont
        let bPlaceHolder: boolean = false;
        let newControlProp: INewControlProperty;
        const hasOneNewControl = {
            start: 0,
            end: 0
        };
        let hasContent = false;

        // the authentic newControlManager
        let originalNewControlManager = null;
        if (this.logicDocument != null) {
            originalNewControlManager = this.logicDocument.getNewControlManager();
        }
        
        this.setLogicDocument(options.doc);
        let bSignPlaceHolder = false;
        this.content.forEach((portion, portionIndex) => {
            let titleVisible = false;
            portion.paragraph = this;
            if (portion.isNewControlStart()) {
                hasOneNewControl.start += 1;
                const name = portion.getStartNewControlName();
                newControlProp = structs.find((struct) => struct.newControlName === name);
                // console.log(newControlProp)
                if (portion.isPlaceHolder()) {
                    // portion.setHidden(true);
                    bPlaceHolder = true;
                }
                // console.log(portion)
                let originalNewControl: NewControl = null;
                if (originalNewControlManager != null && newControlProp) {
                    originalNewControl = originalNewControlManager.getNewControlByName(newControlProp.newControlName);
                }

                if (!bPlaceHolder && originalNewControl && originalNewControl.isSignatureBox()
                    && 0 < originalNewControl.getSignatureCount()) {
                    const leafControls = originalNewControl.getLeafList();
                    bSignPlaceHolder = true;
                    for (let index = 0, count = leafControls ? leafControls.length : 0; index < count; index++) {
                        const element = leafControls[index];
                        bSignPlaceHolder = (element.isPlaceHolderContent() && bSignPlaceHolder);
                    }

                    if (bPlaceHolder !== bSignPlaceHolder &&
                        originalNewControl.getSignatureCount() !== leafControls.length) {
                        bSignPlaceHolder = bPlaceHolder;
                    }

                    bPlaceHolder = bSignPlaceHolder;
                }

                if ((bPlaceHolder && this.isNewControlTextTitleOnly(originalNewControl))
                    || this.isNewControlPrintSelected(originalNewControl)) {
                    portion.setHidden(true);
                } else {
                    portion.setBorderHide();
                    if (!portion.isHidden() && portion.content.length) {
                        const len = portion.content.length;
                        titleVisible = portion.content[len - 1].bVisible;
                    }
                }

                // 如果是节控件且设置了打印隐藏属性，标记它
                if (newControlProp && newControlProp.newControlType === NewControlType.Section && newControlProp.printSelected) {
                    hasSectionWithPrintHide = true;
                }

                if (newControlProp) {
                    const bTextBorder = newControlProp.bTextBorder;
                    if (bTextBorder // && (bPlaceHolder !== true || cleanMode === CleanModeType.CleanModeSpecial)
                        && newControlProp.isNewControlHidden !== true && !newControlProp.printSelected
                        && !(CleanModeType.CleanMode === cleanMode && bPlaceHolder === true)) {
                        portion['bTextBorder'] = bTextBorder;
                        portion.content[0]['bTextBorder'] = bTextBorder;
                    }
                    if (0 < newControlProp.newControlFixedLength) {
                        portion['newControlFixedLength'] = newControlProp.newControlFixedLength;
                        portion['alignment'] = newControlProp.alignments;
                    }
                }
                // const name = portion.getStartNewControlName();
                // const index = structProps.findIndex((item) => item.newControlName === name);
                // if (index === -1) {
                //     return;
                // }
                // const prop = structProps[index];
                // const parent = structs[structs.length - 1] || {};
                // this.createNewControl(prop, parent.name, manager);
                // structs.push({name, start: portion, parentName: parent.name});
                // structProps.splice(index, 1);
            } else if (portion.isNewControlEnd()) {
                hasOneNewControl.end += 1;

                const bCurPlaceHoder = bPlaceHolder;
                if (bPlaceHolder) {
                    portion.setHidden(true);
                    bPlaceHolder = false;
                } else {
                    portion.setBorderHide();
                    const name = portion.getEndNewControlName();
                    newControlProp = structs.find((struct) => struct.newControlName === name);
                }

                if (newControlProp && (cleanMode === CleanModeType.CleanModeSpecial || bPlaceHolder !== true)
                    && !(CleanModeType.CleanMode === cleanMode && bCurPlaceHoder === true)) {
                    const bTextBorder = newControlProp.bTextBorder;
                    if (bTextBorder && newControlProp.isNewControlHidden !== true && !newControlProp.printSelected) {
                        portion['bTextBorder'] = bTextBorder;
                        portion.content[0]['bTextBorder'] = bTextBorder;
                    }
                }
                if (newControlProp && NewControlType.SignatureBox === newControlProp.newControlType) {
                    bSignPlaceHolder = false;
                }
                newControlProp = null;
                // const name = portion.getEndNewControlName();
                // const newControl = manager.getNewControlByName(name);
                // const struct = structs.pop();
                // if (newControl) {
                //     newControl.updateBorderPortion(struct.start, portion);
                // }
            } else {
                const flag = this.setPrintPortionHide(bPlaceHolder || bSignPlaceHolder,
                                    portion, newControlProp, portionIndex, cleanMode, options.clearStruct);
                if (flag) {
                    newControlProp = null;
                }
            }
            // 记录portion是否包含有效内容
            if ((titleVisible ||
                !(portion.isHidden() || portion.isPlaceHolder() || portion.isEmpty(true)))
                && !newControlProp?.printSelected && !portion.isNewControlEnd()) {
                hasContent = true;
            }
        });
        if (cleanMode === CleanModeType.CleanMode && 
            hasSectionWithPrintHide && 
            hasOneNewControl.start === hasOneNewControl.end && 
            hasOneNewControl.start === 1 && 
            !hasContent) {
            const parent = this.getParent();
            if (!(parent.isTableCellContent() && parent.content.length === 1)) {
                this.bHidden = true;
            }
        }
    }

    public addDocContentChild2(options: any, cleanMode: CleanModeType): void {
        const structs = options.structProps;
        let newControlProp: INewControlProperty;

        this.setLogicDocument(options.doc);
        this.content.forEach((portion, portionIndex) => {
            portion.paragraph = this;
            if (portion.isNewControlStart()) {
                const name = portion.getStartNewControlName();
                newControlProp = structs.find((struct) => struct.newControlName === name);

                if (newControlProp && 0 < newControlProp.newControlFixedLength) {
                    portion['newControlFixedLength'] = newControlProp.newControlFixedLength;
                    portion['alignment'] = newControlProp.alignments;
                }
            }
        });
    }

    // public createNewControl(newControlProps: any, parentName: string, manager: any): void {
    //     const newControl = manager.createNewControl(this, newControlProps);
    //     if (!newControl) {
    //         return;
    //     }
    //     if (parentName) {
    //         const parentNewControl = manager.getNewControlByName(parentName);
    //         if (!parentNewControl) {
    //             return;
    //         }
    //         parentNewControl.addLeaf1(newControl);
    //         manager.setNameToNameMap(newControl.getNewControlName(), newControl);
    //     } else {
    //         manager.addNewControl1(newControl);
    //     }
    // }

    public parentHidden(): boolean {
        const parent = this.parent;
        if (parent instanceof DocumentContent) {
            return parent.parentHidden();
        }
        return false;
    }

    /**
     * 段落分割
     * @param para
     */
    public split(para: Paragraph): void {
        // 获取当前光标所在portion和具体文本位置
        const contentPos = this.getParaContentPos();
        const curPos = contentPos.get(0);
        const curPortion = this.content[curPos];

        const newControl = this.getCursorInNewControl();
        const bPlaceHolderContent = null != newControl && true === newControl.isPlaceHolderContent();

        let newPortion: ParaPortion = null;
        let newContent: ParaPortion[] = null;
        if ( false === bPlaceHolderContent ) {
            // 分割光标所在portion，拷贝当前光标所在portion之后的所有portion：curPos + 1
            if ( false === curPortion.isSingleParaEndPortion() ) {
                newPortion = curPortion.split(contentPos);
                newContent = this.content.slice(curPos + 1);
            }

            // let startIndex = curPos;
            // let endIndex = curPos - 1;
            // if (!curPortion.isEmpty(true) && curPortion.isNewControlStart() !== true) {
            //     startIndex++;
            //     endIndex++;
            //     newPortion = curPortion.split(contentPos);
            // }

            // newContent = this.content.slice(startIndex);
            const textProperty = this.getTextProperty(contentPos);
            if ( null === newPortion ) {
                newPortion = new ParaPortion(para);
                newPortion.setProperty(textProperty);
            }

            // 删除被分割的portion数组
            this.contentRemove2(curPos + 1, this.content.length - curPos - 1, true);
            // this.contentRemove2(startIndex, this.content.length - endIndex - 1, true);
        } else {
            const startPortionIndex = this.getPortionIndexById(newControl.getStartBorder().portionId);
            const endPortionIndex = this.getPortionIndexById(newControl.getEndBorder().portionId);

            if ( -1 !== startPortionIndex && -1 !== endPortionIndex ) {
                // get content after endBorder, add new endBorder
                // newContent = this.content.slice(endPortionIndex + 1);
                // const endBorderPortion = newControl.createNewControlBorder(para, false);
                // endBorderPortion.setTextColor(NewControlDefaultSetting.InputContentColor);
                // newContent.splice(0, 0, endBorderPortion);

                // // delete newControl, add startBorder
                // this.contentRemove2(startPortionIndex, this.content.length - startPortionIndex, true);
                // const startBorderPortion = newControl.createNewControlBorder(this, true);
                // this.addToContent(startPortionIndex, startBorderPortion);

                // startBorderPortion.setPlaceHolder(false);
                // newControl.setBorder(startBorderPortion, true);
                // newControl.setBorder(endBorderPortion, false);

                newContent = this.content.slice(endPortionIndex);
                // para.addToChildren(newContent, 0);

                this.content[startPortionIndex].setPlaceHolder(false);
                this.content[endPortionIndex].setPlaceHolder(false);
                this.contentRemove2(startPortionIndex + 1, this.content.length - startPortionIndex - 1, true);
            } else {
                return ;
            }
        }

        // let endPortion = new ParaPortion(this);
        // endPortion.addEnd();

        // 插入段落结束符
        this.addParaEnd(this.content.length); // .addToContent(this.content.length, endPortion);

            // 清除新段落，将分割得到的portion插入新段落
        para.contentRemove2(0, para.content.length);
        para.contentConcat(newContent);
        // 假如把空portion先加进去，会被去掉，那么前一个段落的继承将会消失，因此得先修正再新增空的继承用的portion
        para.correctContent();
        para.addToContent(0, newPortion);

            // 为新段落设置段落相关属性
        this.copyProperty(para);

        this.moveCursorToEndPos();
        para.moveCursorToStartPos();
    }

    public getAllImagesName(startId?: number, endId?: number): string[] {
        const images: string[] = [];
        const drawings = this.logicDocument
                            ?.getDrawingObjects()
                            ?.getGraphicObject();
        if (drawings) {
            if (startId != null || endId != null) {
                let flag = startId == null;
                for (const curPort of this.content) {
                    const portId = curPort.getId();
                    if (startId === portId) {
                        flag = true;
                    }
                    if (flag) {
                        for (const [name, draw] of drawings) {
                            if (draw.portion?.getId() === portId) {
                                images.push(draw.getDrawingName());
                            }
                        }
                    }
                    if (endId === portId) {
                        flag = false;
                        break;
                    }
                }
            } else {
                for (const [name, draw] of drawings) {
                    if (draw.paraId === this.id) {
                        images.push(draw.getDrawingName());
                    }
                }
            }
        }
        return images;
    }

    /**
     * 删除段落内容：多个portion
     * @param pos
     * @param count
     */
    public contentRemove2(pos: number, count: number, bFromSplit?: boolean): boolean {
        for (let index = pos; index < pos + count; index++) {
            if ( this.content[index].preDelete ) {
                this.content[index].preDelete();
            }
        }

        const delItems = this.content.slice(pos, pos + count);

        if ( !delItems || 0 === delItems.length ) {
            return false;
        }

        const history = this.getHistory();
        if ( history ) {
            history.addChange(new ChangeParagraphRemoveItem(this, pos, delItems));
        }

        this.updateTrackRevisions();

        // all deleted half comment portions
        const commentPortions = delItems.filter((item) => item.isComment());
        const halfCommentPortions = [];
        if (commentPortions.length) {
            const leftEnd = commentPortions[0];
            leftEnd.isStartComment() && halfCommentPortions.push(leftEnd);
            const rightStart = commentPortions[commentPortions.length - 1];
            !rightStart.isStartComment() && halfCommentPortions.push(rightStart);
        }
        const halfCount = halfCommentPortions.length;

        if ( this.selection.startPos > pos + count ) {
            this.selection.startPos -= count - halfCount;
        } else if ( this.selection.startPos > pos ) {
            this.selection.startPos = pos;
        }

        if ( this.selection.endPos > pos + count ) {
            this.selection.endPos -= count - halfCount;
        } else if ( this.selection.endPos > pos ) {
            this.selection.endPos = pos;
        }

        if ( this.curPos.contentPos >= pos + count ) {
            this.curPos.contentPos -= count - halfCount;
        } else if (this.curPos.contentPos > pos ) {
            this.curPos.contentPos = pos;
        }

        this.content.splice(pos, count, ...halfCommentPortions);

        // this.parent.getDrawingObjects().deleteImage(this.index);
        // check if contains image
        if (!bFromSplit) {
            for (const delItem of delItems) {
                delItem.removeImageFromPortion(delItem.content);
            }
        }

        return true;
    }

    /**
     * 插入portion数组
     * @param newContent
     */
    public contentConcat(newContent: ParaPortion[]): void {
        if ( !newContent || 0 === newContent.length ) {
            return;
        }

        const pos = this.content.length;
        const portion = this.content[pos - 1];
        if ( portion && portion.isParaEndPortion()) {
            if ( portion.isSingleParaEndPortion()) {
                this.contentRemove(pos - 1);
            } else {
                portion.removeFromContent(portion.content.length - 1, 1);
            }
        }
        this.content = this.content.concat(newContent);

        // console.log(newContent)
        // console.log(circularParse(circularStringify(this)));

        for (let index = 0, length = newContent.length; index < length; index++) {
            const elements = newContent[index].content;
            for (let index2 = 0, length2 = elements.length; index2 < length2; index2++) {
                const element = elements[index2];
                if ( element.type === ParaElementType.ParaDrawing ||
                     element.type === ParaElementType.ParaMedEquation ||
                     element.type === ParaElementType.ParaBarcode ||
                     ParaElementType.ParaQRCode === element.type) {
                    (element as ParaDrawing).paraId = this.id;
                }
            }
        }

        const history = this.getHistory();
        if ( history ) {
            history.addChange(new ChangeParagraphAddItem(this, pos, newContent));
        }

        // 重置portion所在段落索引
        for ( let curPos = pos, length = this.content.length; curPos < length; curPos++ ) {
            this.content[curPos].setParagragh(this);
        }
    }

    public getParaLines(pageIndex: number, result: any): void {
        const curPage = pageIndex - this.parent.getParentSumPageIndex();
        const lines = this.getLinesByPageId(curPage);
        if (!lines || lines.length === 0) {
            return;
        }
        const res = result.paras;
        lines.forEach((line) => {
            result.paras.push(true);
        });
    }

    public isTimeCell(): boolean {
        const parent = this.parent;
        if (parent) {
            return parent.isTimeCell();
        }
        return false;
    }

    public initOpenDefalutContent(splitTag: string, cellText: string): boolean {
        if (!splitTag) {
            return;
        }

        const contents = this.content;
        if (contents.length === 4) {
            return true;
        }

        const text = this.getSelectText(true);
        if (!text) {
            return true;
        }
        if (cellText && cellText !== text) {
            return true;
        }

        const portions = this.content.splice(0, contents.length - 1);
        const arrs = text.split(splitTag);
        if (arrs.length !== 2) {
            return false;
        }
        let textProp: TextProperty = portions[0]?.textProperty;

        let index = 0;
        const portion1 = new ParaPortion(this);
        portion1.addText(arrs[0]);
        portion1.textProperty = textProp;
        this.content.splice(index++, 0, portion1);

        const portion2 = new ParaPortion(this);
        portion2.addText(splitTag);
        if (portions[1]) {
            const content = portions[1].getSelectText(true);
            if (content.indexOf(splitTag) === 0) {
                portion2.textProperty = portions[1].textProperty;
            } else {
                portion2.textProperty = textProp;
            }
            textProp = portions[1].textProperty;
        } else {
            portion2.textProperty = textProp;
        }
        this.content.splice(index++, 0, portion2);
        const portion3 = new ParaPortion(this);
        portion3.addText(arrs[1] || '');
        this.content.splice(index++, 0, portion3);
        portion3.textProperty = textProp;
        return true;
    }

    public selectCellContent(): boolean {
        if (this.selection.bUse) {
            this.removeSelection();
        }

        let pos = this.curPos.contentPos;
        if (pos % 2 === 1) {
            const portion = this.content[pos];
            if (portion.portionContentPos === 0) {
                pos--;
            } else {
                pos++;
            }
        }
        return this.selectOnePortion(pos);
    }

    public selectOnePortion(index: number): boolean {
        const portion = this.content[index];
        if (!portion) {
            return false;
        }

        portion.selectAll(1);
        portion.selectTopPos();

        return true;
    }

    public getCurrentPageLastLine(pageIndex: number, result?: any): any {
        const curPage = pageIndex - this.parent.getParentSumPageIndex() - this.pageNum;
        const page = this.pages[curPage];
        if (!page) {
            return;
        }
        const endLine = page.endLine;
        if (endLine === -1) {
            return null;
        }

        return this.lines[endLine];
    }

    public removeHiddeNewControl(direction: number, bOnlyText: boolean, bOnlySelection: boolean,
                                 flag: boolean): boolean {
        if (this.getDocument()
        .isDesignModel()) {
            return;
        }
        const pos = this.curPos.contentPos;
        let contents: ParaPortion[] = this.content;
        let lastPortion: ParaPortion;
        const curPortion = contents[pos];
        if (!curPortion) {
            return;
        }
        const bHiddenPortion = curPortion.isHidden();
        if (!bHiddenPortion && direction === -1 && pos === 0) {
            if (this.index === 0) {
                return;
            }
            const para = this.parent.content[this.index - 1] as Paragraph;
            if (para.getType() !== DocumentContentType.Paragraph) {
                return;
            }
            contents = para.getContent();
            // let lastPortion = contents[contents.length - 2];
            for (let index = contents.length - 2; index >= 0; index--) {
                const item = contents[index];
                if (item.isNewControlEnd()) {
                    lastPortion = item;
                    break;
                }
            }
            if (!lastPortion || !(lastPortion.isHidden() || para.isHidden())) {
                return;
            }
        } else {
            if (!bHiddenPortion) {
                return;
            }

            lastPortion = curPortion;
            if (!curPortion.isNewControlEnd()) {
                let index = contents.findIndex((data) => data === curPortion) + 1;
                for (const len = contents.length; index < len; index++) {
                    lastPortion = contents[index];
                    if (!lastPortion.isHidden()) {
                        lastPortion = contents[index - 1];
                        break;
                    } else if (lastPortion.isNewControlEnd()) {
                        break;
                    }
                }

                if (!lastPortion) {
                    lastPortion = curPortion;
                }
            }
        }

        const border = lastPortion.content[0] as ParaNewControlBorder;
        if (!border || typeof border.getNewControlName !== 'function') {
            return;
        }
        const parent = this.parent;
        const contentPos = parent.curPos.contentPos;
        const name = border.getNewControlName();
        const doc = this.getDocument();
        // const newControl = doc.getNewControlByName(name);
        // if (!newControl) {
        //     return false;
        // }
        const res = doc.getNewControlManager()
            .deleteNewControl(name);
        if (res === ResultType.Success) {
            const newContentPos = parent.curPos.contentPos;
            if (contentPos === newContentPos) {
                let result = {res: true, bNeedRecal: true};
                while (true) {
                    result = this.remove(direction, bOnlyText, bOnlySelection, flag);
                    if (result.res === true) {
                        break;
                    }
                    const bRemove = this.removeHiddeNewControl(direction, bOnlyText, bOnlySelection, flag);
                    if (!bRemove) {
                        break;
                    }
                }
                if (result.res === false) {
                    doc.recalculate();
                }
                return result.res;
            } else {
                const baseElement = parent.content[newContentPos];
                const baseCurPos = baseElement.getCurPos().contentPos;
                if (baseCurPos === 0 && baseElement.getContent()[0].portionContentPos === 0) {
                    return false;
                }
                doc.recalculate();
                return true;
            }
            // doc.recalculate();
            // return true;
        }
        return false;
    }

    /**
     * 删除段落内容，主要用于外部删除：键盘等
     * @param direction
     * @param bOnlyText
     * @param bOnlySelection
     * @param bSelectParaEnd 是否选中了段尾结束符
     */
    public remove( direction: number, bOnlyText: boolean, bOnlySelection: boolean, bAddText: boolean,
                   bSelectParaEnd?: boolean, checkNewControls?: ICheckSelectionNewControls):
                   IOperateResult {
        const result = {res: true, bNeedRecal: true};

        let bRemoveEmptyPortion = (-1 < direction);
        const bTrackRevisions = ( this.parent && this.parent.isTrackRevisions() );
        if ( true === this.selection.bUse ) {
            let startPos = this.selection.startPos; // portion startpos
            let endPos = this.selection.endPos; // portion endpos

            if ( startPos > endPos ) {
                const temp = startPos;
                startPos = endPos;
                endPos = temp;
            }

            // 检查是否需要删除段落结束符？
            // if ( ( endPos === this.content.length - 1 || this.isSelectionParaEnd(true) )
            //  && true === bSelectParaEnd ) {
            //     ;
            // }

            // console.log(startPos, endPos)

            if ( true === bOnlySelection ) {
                const forwardOverNewControls = this.parent.getSelectForwardOverNewControls(this.parent);
                const behindOverNewControls = this.parent.getSelectBehindOverNewControls(this.parent);

                if ( forwardOverNewControls && 1 <= forwardOverNewControls.length ) {
                    const newControl = forwardOverNewControls[0];
                    if ( newControl && this === newControl.getStartBorderInParagraph()) {
                        if (newControl.isPlaceHolderContent()) {
                            this.content[endPos].removeSelection();
                            bRemoveEmptyPortion = false;
                            endPos -= 1;
                        } else if (this.content[startPos].isEmpty(false)) {
                            bRemoveEmptyPortion = false;
                        }
                    }
                }

                if ( behindOverNewControls && 1 <= behindOverNewControls.length ) {
                    const newControl = behindOverNewControls[0];
                    if ( newControl && newControl.isPlaceHolderContent()
                        && this === newControl.getEndBorderPortion().paragraph ) {
                        this.content[startPos].removeSelection();
                        if ( !bTrackRevisions ) {
                            // 非修订状态下，前后边框被删除后，会自动补全
                            startPos += 1;
                        } else {
                            // 开始位置在后边框
                            if ( this.content[startPos].isNewControlEnd() ) {
                                startPos += 1;
                            } else {
                                // 开始位置在占位符
                                this.content[startPos + 1].removeSelection();
                                startPos += 2;
                            }
                        }
                    }
                }

                if ( startPos > endPos ) {
                    return result;
                }
            }

            if ( startPos === endPos ) {
                if (checkNewControls && this.isCursorInNewControl()) {
                    const pos = (this.content[startPos].isNewControlStart() ? startPos + 1 : startPos);
                    checkNewControls['textPro'] = this.content[pos].textProperty.copy();
                }
                this.content[startPos].remove(direction, bSelectParaEnd);
                this.curPos.contentPos = startPos;
            } else {
                const endPortion = this.content[endPos];
                if (!bTrackRevisions || !endPortion.isNewControl()) {
                    endPortion.remove(direction, bSelectParaEnd);
                }

                let bChecked = false;
                // portion的内容被全部删除，此时portion为空
                if ( endPos < this.content.length - 1 && true === this.content[endPos].isEmpty(false) ) {
                    if (checkNewControls) {
                        bChecked = true;
                        const pos = (this.content[startPos].isNewControlStart() ? startPos + 1 : startPos);
                        checkNewControls['textPro'] = this.content[pos].textProperty.copy();
                    }
                    this.contentRemove(endPos);
                    this.curPos.contentPos = endPos;
                    this.content[endPos].moveCursorToStartPos();
                }

                if ( bTrackRevisions ) {
                    for (let pos = endPos - 1; pos >= startPos + 1; pos--) {
                        const portion = this.content[pos];

                        if ( ParaElementType.ParaPortion === portion.getType() ) {
                            if ( portion.canDeleteInReviewMode() ) {
                                if ( !portion.isNewControl() ) {
                                    if (checkNewControls) {
                                        checkNewControls.para = this;
                                        checkNewControls['textPro'] = portion.textProperty.copy();
                                    }
                                    this.contentRemove2(pos, 1);
                                }
                            } else {
                                portion.setReviewType(ReviewType.Remove, true);
                            }
                        } else {
                            this.content[pos].remove(direction);
                            if ( this.content[pos].isEmpty(false)) {
                                this.removePortion(pos, 1);
                            }
                        }
                    }
                } else {
                    if (!bChecked && checkNewControls) {
                        const pos = (this.content[startPos].isNewControlStart() ? startPos + 1 : startPos);
                        checkNewControls['textPro'] = this.content[pos].textProperty.copy();
                    }
                    this.contentRemove2(startPos + 1, endPos - startPos - 1);
                }

                this.content[startPos].remove(direction);

                if ( bTrackRevisions ) {
                    const startPos2 = Math.max(0, startPos);
                    const endPos2 = Math.min(this.content.length - 1, endPos);

                    for (let pos = startPos2; pos <= endPos2; pos++) {
                        this.content[pos].removeSelection();
                    }

                    this.curPos.contentPos = startPos;
                }

                // 在正选情况下，如果没有输入文本，删除内容后，需要修正光标位置
                if ( -1 < direction && true !== bAddText ) {
                    this.correctContentPos2();
                } else {
                    this.curPos.contentPos = startPos;
                    this.selection.startPos = startPos;
                    this.selection.endPos = startPos;
                }

                const curPortion = this.content[this.curPos.contentPos];
                if (curPortion && curPortion.isEmpty(false)) {
                    if (curPortion.isPlaceHolder()) {
                        curPortion.setPlaceHolder(false);
                    }

                    if (curPortion.getHidden()) {
                        curPortion.setHidden(false);
                    }
                }
            }

            // 删除内容后，重置选择状态，同时修正光标位置
            if ( true !== this.content[this.curPos.contentPos].selection.bUse ) {
                this.removeSelection();

                if ( -1 < direction && true !== bAddText && bRemoveEmptyPortion ) {
                    this.correctContent();
                } else {
                    this.correctContentPos2();
                }
            } else {
                this.selection.bUse = true;
                this.selection.bStart = false;
                this.selection.startPos = startPos;
                this.selection.endPos = startPos;

                if ( -1 < direction && true !== bAddText ) {
                    this.correctContent();
                }
            }

            if ( bTrackRevisions ) {
                this.checkNewControlPlaceContent(null, checkNewControls && checkNewControls['textPro']);
            }
        } else {
            let contentPos = this.curPos.contentPos;
            let curPortion: ParaPortion = this.content[contentPos];
            let bContinueDelete = true;
            const firstLen = curPortion.content.length;
            // let bUpdate = true;
            let bRemove = curPortion.remove(direction);
            const firstRemove = bRemove;

            while ( false === bRemove ) {
                if ( 0 > direction ) {
                    contentPos--;
                } else {
                    contentPos++;
                }

                if ( 0 > contentPos || this.content.length <= contentPos ) {
                    break;
                }

                if (this.content[contentPos].isHidden()) {
                    this.curPos.contentPos = contentPos;
                    if (!(ReviewType.Remove === this.content[contentPos].getReviewType()
                        && this.logicDocument.isFinalRevision())) {
                        return {res: false, bNeedRecal: false};
                    } else {
                        continue;
                    }
                }

                const bNewControlBorder = curPortion.isNewControlStart() || curPortion.isNewControlEnd();

                curPortion = this.content[contentPos];
                // commentObj = this.getDeleteComment(curPortion, contentPos);
                if ( 0 > direction ) {
                    curPortion.moveCursorToEndPos(false);
                } else {
                    curPortion.moveCursorToStartPos(false);
                }

                if ( bNewControlBorder ) {
                    this.curPos.contentPos = contentPos;

                    // if ( -1 < direction ) {
                    bRemove = curPortion.remove(direction);
                    continue;
                    // }

                    // bUpdate = false;
                    // break;
                } else if ( ( curPortion.isNewControlStart() || curPortion.isNewControlEnd() ) && -1 < direction ) {
                    this.curPos.contentPos = contentPos;
                    bContinueDelete = false;
                    // bUpdate = false;
                } else {
                    this.curPos.contentPos = contentPos;
                }

                bRemove = curPortion.remove(direction);
            }

            if ( bRemove ) {
                bContinueDelete = false;
            }

            if ( 0 > contentPos || this.content.length <= contentPos ) {
                result.res = false;
                result.bNeedRecal = true;
            } else {
                if ( 0 < direction && true === this.isCursorAtEnd() && bContinueDelete ) {
                    result.res = false;
                    result.bNeedRecal = true;
                }
            }

            if ( 0 > direction && false === result.res ) {
                // 段落开头，删除左侧的元素。
                // 1. 第一行的缩进非零，则：
                //      1.1 如果是正数，则将其设为0；
                //      1.2 如果是负数，即：悬挂缩进，则将左缩进设为第一行的缩进值，而第一行的缩进设为0；
                // 2. 如果左缩进非零（非第一行），则将其设为0；
                // 3. 如果啥事没有，则删除未完成。
                result.res = true;

                if ( AlignType.Right === this.paraProperty.alignment ) {
                    this.setParagraphAlignment(AlignType.Center);
                } else if ( AlignType.Center === this.paraProperty.alignment ) {
                    this.setParagraphAlignment(AlignType.Left);
                } else if ( 0.001 < Math.abs(this.paraProperty.paraInd.firstLine) ) {

                    const paraInd = this.paraProperty.paraInd.copy();
                    paraInd.firstLine = 0;

                    if ( 0 < this.paraProperty.paraInd.firstLine ) {
                        // 首行缩进
                        // firstLine = 0;
                    } else {
                        // 首行悬挂缩进
                        paraInd.left += this.paraProperty.paraInd.firstLine;
                    }

                    this.setIndentation(paraInd);
                } else if ( 0.001 < Math.abs(this.paraProperty.paraInd.left) ) {
                    this.getDocument()
                    .getNumManager()
                    .removeLvlByPara(this);
                    const paraInd = this.paraProperty.paraInd.copy();
                    paraInd.left = 0;
                    this.setIndentation(paraInd);
                } else {
                    result.res = false;
                }
            }

            if ( true === result.res ) {
                const id = this.getDeleteCommentId();
                if (id > -1) {
                    this.getTopDocument()
                        .deleteCommentById(id);
                }
                this.checkNewControlPlaceContent();
            }
        }

        return result;
    }

    /**
     * 将Para段的内容连接到当前段落
     * @param para
     */
    public concat( para: Paragraph, bDelFromKeyDown?: boolean ): void {
        // 删除本段中段落结束符
        this.removeParaEnd();

        // 将Para段的内容连接到当前段落
        const newContent = para.content.slice(0);
        this.contentConcat(newContent);

        // 删除Para段中的内容
        para.contentRemove2(0, para.content.length, bDelFromKeyDown);
    }

    /**
     * 设置段落相关属性
     * @param para
     */
    public copyProperty(para: Paragraph): void {
      para.x = this.x;
      para.xLimit = this.xLimit;

      para.paraProperty = this.paraProperty.copy();
    }

    /**
     * 当前光标在段落开始位置
     * @param contentPos
     */
    public isCursorAtBegin( contentPos?: ParagraphContentPos ): boolean {
        if ( (0 === this.curPos.contentPos && 0 === this.content[this.curPos.contentPos].portionContentPos)
            && null == contentPos ) {
            return true;
        }

        contentPos = ( undefined === contentPos ? this.getParaContentPos(false, false) : contentPos );
        const searchPos: IParagraphSearchPos = {
                bFound: false,
                bSelection: false,
                pos: new ParagraphContentPos(),
            };

        this.getLeftPos(searchPos, contentPos);

        if ( true === searchPos.bFound ) {
            return false;
        } else {
            return true;
        }
    }

    /**
     * 当前光标在段落结束位置
     * @param contentPos
     */
    public isCursorAtEnd( contentPos?: ParagraphContentPos ): boolean {

        contentPos = ( undefined === contentPos ? this.getParaContentPos(false, false) : contentPos );
        const searchPos: IParagraphSearchPos = {
            bFound: false,
            bSelection: false,
            pos: new ParagraphContentPos(),
        };

        this.getRightPos(searchPos, contentPos);

        if ( true === searchPos.bFound ) {
            return false;
        } else {
            return true;
        }
    }

    public addContentChanges( changes: ContentChangesElement ): void {
        this.contentChanges.add(changes);
    }

    public filterContentNodes(type: number): any[] {
        if (type === NeedStructType.All) {
            return [this];
        }
        const contents = this.content;
        const content = [];
        // let structNames = [];
        let bPlaceHolder: boolean;
        contents.forEach((portion) => {
            if (bPlaceHolder === true) {
                if (portion.isNewControlEnd()) {
                    portion.bPlaceHolder = false;
                    bPlaceHolder = false;
                }
                return;
            }
            if (portion.isNewControlStart()) {
                if (portion.bPlaceHolder) {
                    bPlaceHolder = true;
                    portion.bPlaceHolder = false;
                }
                const portionTexts = portion.content;
                if (portionTexts.length > 1) {
                    portion.removeFromContent(0, 1);
                } else {
                    return;
                }
            } else if (portion.isNewControlEnd()) {
                return;
            }

            content.push(portion);
        });
        this.content = [];
        content.forEach((portion, index) => {
            this.addToContent(index, portion);
        });
        return [this];
    }

    public refreshContentChanges(): void {
        this.contentChanges.refresh();
    }

    /**
     * 重新计算排版指定的数据：undo/redo
     * @param pageIndex
     */
    public refreshRecalData2( pageIndex: number ): void {
        if ( !pageIndex ) {
            pageIndex = 0;
        }

        if ( 0 <= this.index ) {
            this.parent.refreshRecalData2(this.index, this.getRelativePageIndex(pageIndex));
        }
    }

    public refreshRecalData(data: any): void {
        let bNeedRecalc = false;
        let curPage = 0;
        const type = data.type;
        
        switch (type) {
            case HistroyItemType.ParagraphAddItem:
            case HistroyItemType.ParagraphRemoveItem: {
                let dataPos = 0;
                if (data instanceof ChangeParagraphAddItem || data instanceof ChangeParagraphRemoveItem) {
                    dataPos = data.getMinPos();
                } else if (null != data.position) {
                    dataPos = data.position;
                }

                for (curPage = this.pages.length - 1; 0 < curPage; curPage--) {
                    if (dataPos > this.lines[this.pages[curPage].startLine].getStartPos()) {
                        break;
                    }
                }
                bNeedRecalc = true;
                break;
            }
        
            default:
                break;
        };

        if (bNeedRecalc) {
            return this.refreshRecalData2(curPage);
        }
    }

    /**
     * 获取当前文档元素（文档中的段落）状态：用于undo/redo
     */
    public getDocumentElementState(): ParagraphState[] {
        const state = new ParagraphState(this.curPos, this.selection);
        state.contentPos = this.getParaContentPos(false, false);

        if ( true === this.selection.bUse ) {
            state.startPos = this.getParaContentPos(true, true);
            state.endPos = this.getParaContentPos(true, false);
            // console.log(this.content.length, state.endPos);
        }

        return [state];
    }

    /**
     * 获取当前元素处在哪个页面
     * @param pos 位置信息
     */
    public getCurrentPageByPos(pos: ParagraphContentPos): number {
        const parent = this.parent;
        let pageIndex: number;
        const len = pos.getDepth();
        const portionIndex = pos.get(len - 1);
        const textIndex = pos.get(len);
        const portion = this.content[portionIndex];
        const line = portion.getLineByPos(textIndex);
        const pageNum = this.pages.findIndex((page) => page.startLine <= line && line <= page.endLine);
        if (parent.isTableCellContent()) {
            pageIndex = parent.getAbsolutePage(pageNum);
        } else {
            pageIndex = this.getAbsolutePage(pageNum);
        }

        return pageIndex;
    }

    /**
     * 设置当前文档元素（文档中的段落）状态：用于undo/redo
     * @param state
     * @param index
     */
    public setDocumentElementState( state: ParagraphState[], index: number ): void {
        if ( !state || 0 >= state.length) {
            return ;
        }

        const paraState = state[index];

        this.curPos.x = paraState.curPos.x;
        this.curPos.y = paraState.curPos.y;
        this.curPos.realX = paraState.curPos.realX;
        this.curPos.realY = paraState.curPos.realY;
        this.curPos.line = paraState.curPos.line;
        this.curPos.pagePos = paraState.curPos.pagePos;

        this.setParaContentPos(paraState.contentPos, -1, true);

        this.removeSelection();

        this.selection.bStart = paraState.selection.bStart;
        this.selection.bUse = paraState.selection.bUse;
        this.selection.startPos = paraState.selection.startPos;
        this.selection.endPos = paraState.selection.endPos;

        // console.log(this.content.length, this.selection.endPos);

        if ( true === this.selection.bUse ) {
           this.setSelectionContentPos(paraState.startPos, paraState.endPos);
        }
    }

    public getNumberingPr(): IParaNumPr {
        return this.paraProperty.numPr;
    }

    public removeNumbering(): void {
        this.paraProperty.numPr = undefined;
        this.numbering = undefined;
    }

    public addNumbering(numId: number): void {
        this.paraProperty.numPr = {
            numId
        };
        this.numbering = new ParaNumbering();
    }

    public getNumbering(): ParaNumbering {
        return this.numbering;
    }

    /**
     * 设置当前文档元素（文档中的段落）选择状态：用于undo/redo
     * @param startPos
     * @param endPos
     */
    public setSelectionContentPos( startContentPos: ParagraphContentPos,  endContentPos: ParagraphContentPos ): void {
        const depth = 0;
        let direction = 1;

        // 判断正向选择，或反向选择
        if ( startContentPos.compare(endContentPos) > 0 ) {
            direction = -1;
        }

        // 旧的选择区域
        let oldStartPos = Math.max(0, Math.min(this.selection.startPos, this.content.length - 1));
        let oldEndPos = Math.max(0, Math.min(this.selection.endPos, this.content.length - 1));

        if ( oldStartPos > oldEndPos ) {
            oldStartPos = this.selection.endPos;
            oldEndPos = this.selection.startPos;
        }

        // 获取指定的选择位置
        const startPos = startContentPos.get(depth);
        const endPos = endContentPos.get(depth);

        this.selection.startPos = startPos;
        this.selection.endPos = endPos;

        // 删除旧的选择区域：不在当前portion位置的都删除
        if ( oldStartPos < startPos && oldStartPos < endPos ) {
            const temp = Math.min(startPos, endPos);

            for (let index = oldStartPos; index < temp; index++) {
                this.content[index].removeSelection();
            }
        }

        if ( oldEndPos > startPos && oldEndPos > endPos ) {
            const temp = Math.max(startPos, endPos);

            for (let index = temp + 1; index <= oldEndPos; index++) {
                this.content[index].removeSelection();
            }
        }

        // 设置指定的选择位置
        if ( startPos === endPos ) {
            this.content[startPos].setSelectionContentPos(startContentPos, endContentPos, depth + 1, 0, 0);
        } else {
            // 反向选择
            if ( startPos > endPos ) {
                this.content[startPos].setSelectionContentPos(startContentPos, null, depth + 1, 0, 1);
                this.content[endPos].setSelectionContentPos(null, endContentPos, depth + 1, -1, 0);

                for (let curPos = endPos + 1; curPos < startPos; curPos++) {
                    this.content[curPos].selectAll(-1);
                }
            } else {
                this.content[startPos].setSelectionContentPos(startContentPos, null, depth + 1, 0, -1);
                this.content[endPos].setSelectionContentPos(null, endContentPos, depth + 1, 1, 0);

                for (let curPos = startPos + 1; curPos < endPos; curPos++) {
                    this.content[curPos].selectAll(1);
                }
            }
        }
    }

    public getLastVisibleLine(clientY: number): {x: number, y: number, xLimit: number} {
        const lines = this.lines;
        if (lines.length === 0) {
            return;
        }
        for (let index = this.pages[0].endLine; index > -1; index--) {
            const curLine = lines[index];
            if (curLine.bottom < clientY) {
                const position = curLine.ranges[0];
                return {x: curLine.ranges[0].x, y: curLine.bottom, xLimit: position.xEnd};
            }
        }
    }

    public setImageSelection(startPos: ParagraphContentPos): void {
      // 删除旧的选择
      if ( true === this.selection.bUse ) {
        this.removeSelection();
      }

      this.selection.bUse = true;

      // console.log(this.getParaContentPos());
      // console.log(this.getParaContentPos(true, false));
      // console.log(this.getParaContentPos(true, true));

      // let endPos = JSON.parse(JSON.stringify(startPos)) as ParagraphContentPos;
      // let endPos = Object.assign({}, startPos);
      // endPos.data[1] = startPos.data[1] + 1;

      // endPos.data[1] should be startPos.data[1] + 1, since a paradrawing just inserted
      // when clicking on image, this should remain the same. acceptable
      const endPos = this.getParaContentPos();

      // console.log(startPos, endPos)
      this.setSelectionContentPos(startPos, endPos);
    }

    /**
     * 设置段落属性
     * @param change
     */
    public setPropertyChange( change: ParaProperty ): void {
        const history = this.getHistory();
        if ( history ) {
            history.addChange(new ChangeParagraphPropertyChange(this, this.paraProperty.propertyChange, change));
        }
        this.paraProperty.setPropertyChange(change);
    }

    /**
     * 是否有段落属性发生改变
     */
    public hasPropertyChange(): boolean {
        return this.paraProperty.hasPropertyChange();
    }

    /**
     * 当前光标不在空的portion，则在排版计算时，将其删除，
     * 同时还需要删除history里面的记录
     * 当portion内容被删除为空时，此portion需要被删除？需要考虑情况：
     * 1. 当undo/redo时，删除内容后，portion为空，此时portion就不应该被删除！无论此时光标是否在此portion内
     */
    public hasUnusePortion(): boolean {
        const count = this.content.length;

        for (let index = 0; index < count; index++) {
            const element = this.content[index];

            if ( 0 === element.content.length ) {
                return true;
            }
        }

        return false;
    }

    /**
     * 删除空portion：主要是在改变文本属性时产生的
     */
    public removeUnusePortion(): void {
        const count = this.content.length;
        const removeItems = [];

        for (let index = 0; index < count; index++) {
            const element = this.content[index];

            if ( 0 === element.content.length ) {
                removeItems.push(index);
            }
        }

        for (let index = removeItems.length - 1; index >= 0; index--) {
            const pos = removeItems[index];
            this.content.splice(pos, 1);

            // 更新光标所在portion位置
            if ( pos <= this.curPos.contentPos ) {
                this.curPos.contentPos--;
            }
            // gHistory.removeLastPoint();
        }

        if ( 0 < removeItems.length ) {
            this.recalculatePage();
        }
    }

    public getAbsolutePageByLineId(id: number): number {
        const pageIndex = this.getAbsolutePage();
        const paraPage = this.getPageByLine(id);

        return pageIndex + paraPage;
    }

    /**
     * 获取段落在指定页面的所有行
     * @param pageIndex
     */
    public getLinesByPageId( pageIndex: number ): ParaLine[] {

        if ( pageIndex < this.pageNum || pageIndex > this.pageNum + this.pages.length - 1 ) {
            return [];
        }

        const result: ParaLine[] = [];
        let startLine = this.pages[pageIndex - this.pageNum].startLine;
        const endLine = this.pages[pageIndex - this.pageNum].endLine;

        for (; startLine <= endLine; startLine++) {
            result.push(this.lines[startLine]);
        }

        return result;
    }

    /**
     * 获取段落在指定页面的开始行号
     * @param pageIndex
     */
    public getElementStartLineByPageId( pageIndex: number ): number {

        if ( pageIndex < this.pageNum || pageIndex > this.pageNum + this.pages.length - 1 ) {
            return -1;
        }

        return this.pages[pageIndex - this.pageNum].startLine;
    }

    /**
     * 获取段落在指定页面的结束行号
     * @param pageIndex
     */
    public getElementEndLineByPageId( pageIndex: number ): number {

        if ( pageIndex < this.pageNum || pageIndex > this.pageNum + this.pages.length - 1 ) {
            return -1;
        }

        return this.pages[pageIndex - this.pageNum].endLine;
    }

    /**
     * 重新计算段落的第一页，此段落在新页面开始
     */
    public startFromNewPage(): void {
        this.pages.length = 1;
        this.pages[0] = new ParaPage(this.x, this.y, this.xLimit, this.yLimit, 0);

        this.pages[0].setEndLine(-1);
        this.lines[-1] = new ParaLine();
    }

    /**
     * 判断当前段落是否在第一页
     */
    public isContentOnFirstPage(): boolean {
        if ( 0 > this.pages[0].endLine ) {
            return false;
        }

        return true;
    }

    public getCurrentParagraph(): DocumentContentElementBase {
        return this;
    }

    /**
     * 删除此元素前需要调用此函数：需要先删除引用了此元素的所有对象，比如注释、批注等
     */
    public preDelete(): void {
        for (let index = 0, count = this.content.length; index < count; index++) {
            const element = this.content[index];

            element.preDelete();

            if ( ParaElementType.ParaComment === element.type && this.parent instanceof Document ) {
                // todo;
            }

            // if ( true !== element.isEmpty(true) && true === element.isNewControlStart() ) {
            //     const newControlName = element.getStartNewControlName();
            //     // this.getParent().getNewControlManager().removeNewControlByName(newControlName);
            // }
        }

        // const startPos = this.getCurContentPosInDoc(true, true);
        // const endPos = this.getCurContentPosInDoc(true, false);
        // // const newControlNames = this.getNewControlNamesByPos(startPos, endPos);
        // const newControlManager = this.getParent().getNewControlManager();

        // if ( newControlManager ) {
        //     newControlManager.remove(startPos, endPos);
        // }
    }

    public getEmptyHeight(): number {
        const count = this.content.length;
        if ( 0 >= count ) {
            return 0;
        }
        return this.content[0].textHeight;
    }

    public recalculateMinmaxContentWidth(bRotated: boolean): { Min: number, Max: number } {
        const min = 0;
        const max = 0;
        const length = this.content.length;

        for (let index = 0; index < length; index++) {
            const element = this.content[index];
            element.recalculateMinmaxContentWidth(bRotated);
        }

        return { Min: 0, Max: 0 };
    }

    public shift(curPage: number, shiftDx: number, shiftDy: number): void {
        if ( 0 === curPage ) {
            this.x += shiftDx;
            this.y += shiftDy;
            this.xLimit += shiftDx;
            this.yLimit += shiftDy;
        }

        this.pages[curPage].shift(shiftDx, shiftDy);

        const startLine = this.pages[curPage].startLine;
        const endLine = this.pages[curPage].endLine;

        for (let curLine = startLine; curLine <= endLine; curLine++) {
            this.lines[curLine].shift(shiftDx, shiftDy);
        }

        for (let curLine = startLine; curLine <= endLine; curLine++) {
            const line = this.lines[curLine];
            const rangesCount = line.ranges.length;

            for (let curRange = 0; curRange < rangesCount; curRange++) {
                const range = line.ranges[curRange];
                const startPos = range.startPos;
                const endPos = range.endPos;

                for (let pos = startPos; pos <= endPos; pos++) {
                    const element = this.content[pos];
                    element.shift(shiftDx, shiftDy, curLine, curRange);
                }
            }
        }
    }

    /**
     * 获取当前光标前的结构化元素的左边框位置
     */
    // public getNewControlLeftBorderPos(): number {
    //     return 0;
    // }

    // /**
    //  * 获取当前光标前的结构化元素的右边框位置
    //  */
    // public getNewControlRightBorderPos(): number {
    //     return 0;
    // }

    /**
     * 当前光标是否在newControl内
     * @param newControlName
     * @param bStart
     */
    public isCursorInNewControl( newControlName?: string, bStart?: boolean ): boolean {
        const contentPos = this.getCurContentPosInDoc();
        // console.log(contentPos)
        const newControlManager = this.parent.getNewControlManager();

        if ( newControlManager ) {
            // console.log(newControlManager.isCursorInNewControl(contentPos))
            return newControlManager.isCursorInNewControl(contentPos);
        }

        return false;
    }

    /**
     * 获取当前光标所在的newControl
     */
    public getCursorInNewControl(): NewControl {
        if ( true === this.isCursorInNewControl() ) {
            const contentPos = this.getCurContentPosInDoc();

            return this.parent.getNewControlManager()
                                    .getPosNewControl(contentPos);
        }

        return null;
    }

    public deleteLastSpaces(bSpace: boolean, bTag: boolean): number {
        const contents = this.content;
        let startIndex = -1;
        // let paraEndIndex = -1;
        const length = contents.length;
        let num: number = -1;
        for (let index = length - 1; index >= 0; index--) {
            const portion = contents[index];
            // if (portion.isParaEndPortion()) {
            //     paraEndIndex = index;
            //     continue;
            // }
            num = portion.deleteLastSpaces(bSpace, bTag);
            if (num !== 0) {
                break;
            }
            startIndex = index;
        }

        if (startIndex === -1 && num === 2) {
            return ResultType.Success; // 删一部分，说明这个段落不能删除
        } else if (startIndex === -1) {
            return ResultType.UnEdited;
        } else if (length > 0 && contents.length === length) {
            return ResultType.UnEdited;
        }

        const endIndex = length;
        // if (paraEndIndex !== -1) {
        //     endIndex = paraEndIndex;
        // }
        this.curPos.contentPos = Math.max(0, startIndex - 1);
        this.contentRemove2(startIndex, endIndex - startIndex);
        this.addParaEnd(this.content.length);

        return ResultType.Success;
    }

    /**
     * 当前鼠标的焦点是否newControl内
     * @param pointX
     * @param pointY
     * @param pageIndex
     */
    public isFocusInNewControl(pointX: number, pointY: number, pageIndex?: number): boolean {
        if ( 0 > pageIndex || null == pageIndex ) {
            return false;
        }

        const contentPos = this.getContentPosByXY(pageIndex, pointX, pointY);
        const newControlManager = this.parent.getNewControlManager();
        if ( newControlManager ) {
            return newControlManager.isCursorInNewControl(contentPos);
        }

        return false;
    }


    public getNewControlTextAI(startPortionIndex: number, endPortionIndex: number,
                            bParaEnd: boolean = false, bSection: boolean = false): string {
        let text = '';

        if ( null != startPortionIndex ) {
            startPortionIndex = Math.max(startPortionIndex, 0);
        } else {
            startPortionIndex = 0;
        }

        if ( null != endPortionIndex ) {
            endPortionIndex = Math.min(endPortionIndex, this.content.length - 1);
        } else {
            endPortionIndex = this.content.length - 1;
        }

        for (let index = startPortionIndex; index <= endPortionIndex; index++) {
            const portion = this.getContent()[index];
            const reviewInfo = (!!portion && portion.getReviewInfo());
            const bDelReview = (!!reviewInfo && ReviewType.Remove === portion.getReviewType())
                    || (!!reviewInfo && reviewInfo.isSecondInfo());

            if (portion && !bDelReview) {
                text += portion.getTextAI(bParaEnd, bSection);
            }
        }

        return text;
    }

    public getNewControlText(startPortionIndex: number, endPortionIndex: number,
                             bParaEnd: boolean = false, bSection: boolean = false): string {
        let text = '';

        if ( null != startPortionIndex ) {
            startPortionIndex = Math.max(startPortionIndex, 0);
        } else {
            startPortionIndex = 0;
        }

        if ( null != endPortionIndex ) {
            endPortionIndex = Math.min(endPortionIndex, this.content.length - 1);
        } else {
            endPortionIndex = this.content.length - 1;
        }

        let bSkipToEnd = false;
        for (let index = startPortionIndex; index <= endPortionIndex; index++) {
            const portion = this.getContent()[index];
            const reviewInfo = (!!portion && portion.getReviewInfo());
            const bDelReview = (!!reviewInfo && ReviewType.Remove === portion.getReviewType())
                                || (!!reviewInfo && reviewInfo.isSecondInfo());

            // 检查是否需要跳过占位符NewControl的内容
            if (bSkipToEnd) {
                if (portion && portion.isNewControlEnd()) {
                    bSkipToEnd = false;
                }
                continue;
            }
            if (portion && portion.isPlaceHolder() && portion.isNewControlStart()) {
                bSkipToEnd = true;
                continue;
            }

            // 添加隐藏状态检查，跳过隐藏的portion（如ListBox后的占位符元素）
            if (portion && !bDelReview && !portion.isHidden()) {
                text += portion.getText(bParaEnd, bSection);
            }
        }

        return text;
    }

    public getAllNewControls(): NewControl[] {
        const result = [];
        const content = this.getContent();
        for (const portion of content) {
            const paraItem = portion.content[0];
            if (paraItem instanceof ParaNewControlBorder) {
                if (paraItem.bStart === true) {
                    const name = paraItem.getNewControlName();
                    result.push(this.logicDocument.getNewControlByName(name));
                }
            }
        }
        return result;
    }

    public removeNewControlText( startPortionIndex: number, endPortionIndex: number ): void {
        if ( null != startPortionIndex ) {
            startPortionIndex = Math.max(startPortionIndex, 0);
        } else {
            startPortionIndex = 0;
        }

        if ( null != endPortionIndex ) {
            endPortionIndex = Math.min(endPortionIndex, this.content.length - 1);
        } else {
            endPortionIndex = this.content.length - 1;
        }

        // const content = this.getContent();
        this.removePortion(startPortionIndex, endPortionIndex - startPortionIndex + 1);
    }

    public getSelectText(bApplyToAll?: boolean): string {
        let text = '';
        let startPos: number;
        let end: number;
        if (bApplyToAll === true) {
            startPos = 0;
            end = this.content.length;
        } else {
            const selection = this.selection;
            startPos = selection.startPos;
            end = selection.endPos;
            if (startPos > end) {
                const pos = startPos;
                startPos = end;
                end = pos;
            }
            end += 1;
        }

        const contents = this.content.slice(startPos, end);
        let bEnd = false;
        contents.forEach((portion) => {
            if (bEnd) {
                if (portion.isNewControlEnd()) {
                    bEnd = false;
                }
                return;
            }
            if (portion.isPlaceHolder() && portion.isNewControlStart()) {
                bEnd = true;
            }
            text += portion.getSelectText(bApplyToAll);
        });

        return text;
    }

    //AI 用途 获取选择文本，包括结构化元素边框
    public getSelectTextAI(bApplyToAll?: boolean): string {
        let text = '';
        let startPos: number;
        let end: number;
        if (bApplyToAll === true) {
            startPos = 0;
            end = this.content.length;
        } else {
            const selection = this.selection;
            startPos = selection.startPos;
            end = selection.endPos;
            if (startPos > end) {
                const pos = startPos;
                startPos = end;
                end = pos;
            }
            end += 1;
        }

        const contents = this.content.slice(startPos, end);
        let bEnd = false;
        contents.forEach((portion) => {
            if (bEnd) {
                if (portion.isNewControlEnd()) {
                    bEnd = false;
                    text += ']';
                }
                return;
            }
            if (portion.isPlaceHolder() && portion.isNewControlStart()) {
                bEnd = true;
                text +='['   
            }
            text += portion.getSelectTextAI(bApplyToAll);
        });

        return text;
    }

    public getSelectTextNoHidden(bApplyToAll?: boolean): string {
        if (this.isHidden()) {
            return '';
        }
        let text = '';
        let startPos: number;
        let end: number;
        if (bApplyToAll === true) {
            startPos = 0;
            end = this.content.length;
        } else {
            const selection = this.selection;
            startPos = selection.startPos;
            end = selection.endPos;
            if (startPos > end) {
                const pos = startPos;
                startPos = end;
                end = pos;
            }
            end += 1;
        }

        const contents = this.content.slice(startPos, end);
        let bEnd = false;
        contents.forEach((portion) => {
            if (bEnd) {
                if (portion.isNewControlEnd()) {
                    bEnd = false;
                }
                return;
            }
            if (portion.isPlaceHolder() && portion.isNewControlStart()) {
                bEnd = true;
            }
            if (!portion.isHidden()) {
                text += portion.getSelectText(bApplyToAll);
            }
        });

        return text;
    }

    public getSelectionNodePos(pos: ParagraphContentPos, bStart: boolean = true, bApplyToAll: boolean): void {
        const contents = this.content;
        // const  = this.bApplyToAll;
        const activeIndex = getAcitveNodeIndex(this.selection, bStart, bApplyToAll, contents.length - 2);
        pos.add(activeIndex);
        contents[activeIndex].getSelectionNodePos(pos, bStart, bApplyToAll);
    }

    /**
     * 获取鼠标的焦点所在的newControl
     * @param pointX
     * @param pointY
     * @param pageIndex
     */
    public getFocusInNewControl(pointX: number, pointY: number, pageIndex: number): NewControl {
        if ( true === this.isFocusInNewControl(pointX, pointY, pageIndex) ) {
            const contentPos = this.getContentPosByXY(pageIndex, pointX, pointY);

            return this.parent.getNewControlManager()
                                .getPosNewControl(contentPos);
        }

        return null;
    }

    /**
     * 获取鼠标的焦点所在的newControl的显示区域
     * @param newControlName
     * @param startPos
     * @param endPos
     */
    public getNewControlParaBounds(newControlName: string, startPos: number,
                                   endPos: number ): IDrawSelectionsLine[] {
        const selections = this.getNewControlLinesBounds(newControlName, startPos, endPos);

        return this.getSelectedLines(selections);
    }

    public getNewControlLinesBounds(newControlName: string, startPos?: number, endPos?: number ): ISelectionsPara  {
        if ( null == startPos && null == endPos ) {
            return null;
        }

        // const newControl = this.isCursorInNewControl(newControlName, true);
        const result = {
            startLine: -1,
            startLineX: 0,
            startLineWidth: 0,
            endLine: -1,
            endLineX: 0,
            endLineWidth: 0,
        };
        const bMultiParas = ( null == endPos ? true : false );

        if ( null != newControlName) {
            // let result = [];
            let startBorderPos = null;
            let endBorderPos = null;

            let startLine = 0;
            let endLine = this.getLineCount() - 1;

            let startPortion = this.content[startPos];
            let endPortion = this.content[endPos];
            // const newControlName = newControl.getNewControlName();
            if ( null != startPos && null != endPos ) {
                startBorderPos = startPortion.getNewControlStartBoder(newControlName);
                endBorderPos = endPortion.getNewControlEndBoder(newControlName);

                // get选中位置的portion所在行号
                startLine = startPortion.getLineByPos(startBorderPos);
                endLine = endPortion.getLineByPos(endBorderPos);
            } else if ( null != startPos ) {
                // startPos = this.getPortionIndexById(startPos);
                startBorderPos = startPortion.getNewControlStartBoder(newControlName);
                startLine = startPortion.getLineByPos(startBorderPos);
                endPos = this.lines[endLine].getEndPos();
                endPortion = this.content[endPos];
            } else if ( null != endPos ) {
                startPos = 0;
                startPortion = this.content[startPos];
                endBorderPos = endPortion.getNewControlEndBoder(newControlName);
                endLine = endPortion.getLineByPos(endBorderPos);
            }

            // 记录选中的首尾行号
            result.startLine = startLine;
            result.endLine = endLine;

            let startLineX = 0;
            let startLineWidth = 0;
            let endLineX = 0;
            let endLineWidth = 0;

            if ( null != startBorderPos && null != endBorderPos ) {
                startLineX = startPortion.content[0].positionX;
                endLineX = endPortion.content[endPortion.content.length - 1].positionX;
            } else if ( null != startBorderPos ) {
                startLineX = startPortion.content[0].positionX;
            } else if ( null != endBorderPos ) {
                startLineX = this.lines[0].ranges[0].xVisible;
            }

            startLineWidth = this.getNewControlStartLineWidth(startLine, startPos, endPos);

            if ( startLine === endLine ) {
                endLineX = 0;
                endLineWidth = 0;
            } else {
                const lastLine = this.lines[endLine];
                endLineX = lastLine.ranges[0].xVisible;
                endLineWidth = bMultiParas ? lastLine.ranges[0].xEnd - endLineX :
                                this.getNewControlEndLineWidth(startPos, endPos);
            }

            result.startLineX = startLineX;
            result.startLineWidth = startLineWidth;
            result.endLineX = endLineX;
            result.endLineWidth = endLineWidth;

            // console.log(result);
            // console.log(startLine, endLine)
            return result;
        }

        return null;
    }

    public getPortionIndexById( portionId: number ): number {
        for (let index = 0, length = this.content.length; index < length; index++) {
            const element = this.content[index];
            if ( portionId === element.id ) {
                return index;
            }
        }

        return -1;
    }

    /**
     * 获取newControl所在行的开始宽度
     * @param startPos 开始位置的portion索引
     * @param endPos
     */
    public getNewControlStartLineWidth( startLine: number, startPortionPos: number, endPortionPos: number ): number {
        const startPortion = this.content[startPortionPos];
        let endPortion = this.content[endPortionPos];
        // const startLine = startPortion.startLine;
        const endLine = endPortion.startLine;
        const startLinesCount = startPortion.getLinesCount();

        const startPos = startPortion.getRangeEndPos(0);
        const startPosition = 0 < startPos ? startPortion.content[0].positionX : startPortion.positionX;

        if ( startLine !== endLine ) {
            // if ( 1 < startLinesCount ) {
            //     return startPortion.content[startPos - 1].positionX +
            //     startPortion.content[startPos - 1].widthVisible - startPosition;
            // } else {
                const width = this.lines[startLine].ranges[0].xEnd - startPosition;
                return Math.abs(width); // 0 < width ? width : 0;
            // }
        } else {
            let endPos = endPortion.getRangeEndPos(0);
            let endPosition = 0;
            // endPortion在startLine上没有放下文本内容，而是在startLine + 1行才开始真正排版
            // 因此endPortionPos - 1
            if ( 0 === endPos && 0 < endPortion.getLinesCount() ) {
                endPortionPos -= 1;
                endPos = this.content[endPortionPos].getRangeEndPos(0);
                endPortion = this.content[endPortionPos];
            }

            const item = endPortion.content[endPos - 1];
            endPosition = (0 < endPos ? item.positionX + item.widthVisible - item.getRemainWidth()
                                        : endPortion.positionX);

            return endPosition - startPosition;
        }
    }

    public getNewControlEndLineWidth( startPortionPos: number, endPortionPos: number ): number {
        const endPortion = this.content[endPortionPos];
        const endStartLine = endPortion.startLine;
        const endLinesCount = endPortion.getLinesCount();

        const endPosition = this.lines[endStartLine + endLinesCount - 1].ranges[0].xVisible;

        const endPos = endPortion.getRangeEndPos(endLinesCount - 1) - 1;
        const item = endPortion.content[endPos];
        return (item.positionX + item.widthVisible - item.getRemainWidth() - endPosition);
    }

    /**
     * 判断当前位置pos是否在两个newControl之间，且两个newControl之间无任何内容
     * @param pos
     */
    public isBetweenTwoNewControls( pos?: ParagraphContentPos ): boolean {
        const searchPos = {
            pos: new ParagraphContentPos(),
            bFound: false,
            bSelection: false,
        };

        const contentPos = null != pos ? pos : this.getParaContentPos(false, false);

        this.getRightPos(searchPos, contentPos);

        if ( true !== searchPos.bFound ) {
            return false;
        } else {
            const curPos = searchPos.pos;
            const item = this.content[curPos.get(0)].content[curPos.get(1) - 1];
            if ( null == item || ParaElementType.ParaNewControlBorder !== item.type
                || true !== item.isNewControlStartBoder() ) {
                return false;
            }
        }

        this.getLeftPos(searchPos, contentPos);

        if ( true !== searchPos.bFound ) {
            return false;
        } else {
            const curPos = searchPos.pos;
            const item = this.content[curPos.get(0)].content[curPos.get(1)];
            if ( null == item || ParaElementType.ParaNewControlBorder !== item.type
                || true !== item.isNewControlEndBoder() ) {
                return false;
            }
        }

        return true;
    }

    /**
     * 选择区域中存在newControl，进行删除时，需要特定操作
     */
    public remove2(): void {
        // ;
    }

    /**
     * 设置NewControl内文本是否为隐私显示
     * @param viewSecretType
     * @param startPortionIndex
     * @param endPortionIndex
     * @param bAllText
     * @param bStart
     */
    public setNewControlViewSecret( viewSecretType: NewControlContentSecretType,
                                    startPortionIndex: number, endPortionIndex: number,
                                    bAllText: boolean = false, bStart: boolean = false ): number {
        let result = 0;

        if ( null != startPortionIndex ) {
            startPortionIndex = Math.max(startPortionIndex, 0);
        } else {
            startPortionIndex = 0;
        }

        if ( null != endPortionIndex ) {
            endPortionIndex = Math.min(endPortionIndex, this.content.length - 1);
        } else {
            endPortionIndex = this.content.length - 1;
        }

        switch (viewSecretType) {
            case NewControlContentSecretType.DontSecret:
            case NewControlContentSecretType.AllSecret: {
                for (let index = startPortionIndex; index <= endPortionIndex; index++) {
                    const portion = this.content[index];
                    result += portion.setNewControlViewSecret(NewControlContentSecretType.AllSecret === viewSecretType);
                }
                break;
            }

            case NewControlContentSecretType.PartSecret: {
                if ( true === bAllText ) {
                    const textLength = this.getTextLengthByPos(startPortionIndex, endPortionIndex, true);

                    if ( 1 === textLength ) {
                        for (let index = startPortionIndex; index <= endPortionIndex; index++) {
                            const portion = this.content[index];
                            portion.setNewControlViewSecret(true);
                        }
                    } else if ( 2 <= textLength ) {
                        let count = 0;
                        let pos = 1;
                        for (let index = startPortionIndex; index <= endPortionIndex; index++) {
                            const portion = this.content[index];
                            count += portion.setNewControlViewSecret(true, pos);

                            if ( 1 <= count ) {
                                pos = 0;
                            }
                        }

                        if ( 2 < textLength ) {
                            count = 0;
                            for (let index = endPortionIndex; index >= startPortionIndex; index--) {
                                const portion = this.content[index];
                                count += portion.setNewControlViewSecret(false, -1);
                                if ( 1 <= count ) {
                                    break;
                                }
                            }
                        }
                    }
                } else {
                    if ( true === bStart ) {
                        let count = 0;
                        let pos = 1;
                        for (let index = startPortionIndex; index <= endPortionIndex; index++) {
                            const portion = this.content[index];
                            count += portion.setNewControlViewSecret(true, pos);

                            if ( 1 <= count ) {
                                pos = 0;
                            }
                        }

                        result = count;
                    } else {
                        for (let index = startPortionIndex; index <= endPortionIndex; index++) {
                            const portion = this.content[index];
                            portion.setNewControlViewSecret(true);
                        }

                        let count = 0;
                        for (let index = endPortionIndex; index >= startPortionIndex; index--) {
                            const portion = this.content[index];
                            count += portion.setNewControlViewSecret(false, -1);
                            if ( 1 <= count ) {
                                break;
                            }
                        }
                    }
                }
                break;
            }

            default:
                break;
        }

        return result;
    }

    /**
     * 设置NewControl内文本是否显示
     * @param bHidden
     * @param startPortionIndex
     * @param endPortionIndex
     */
    public setNewControlTextHidden( bHidden: boolean, startPortionIndex: number, endPortionIndex: number ): void {
        if ( null != startPortionIndex ) {
            startPortionIndex = Math.max(startPortionIndex, 0);
        } else {
            startPortionIndex = 0;
        }

        if ( null != endPortionIndex ) {
            endPortionIndex = Math.min(endPortionIndex, this.content.length - 1);
        } else {
            endPortionIndex = this.content.length - 2;
        }

        for (let index = startPortionIndex; index <= endPortionIndex; index++) {
            const portion = this.content[index];
            portion.setNewControlTextHidden(bHidden);
        }
    }

    public getTextLengthByPos( startPortionIndex: number, endPortionIndex: number, bNewControl?: boolean ): number {
        let length = 0;

        if ( null != startPortionIndex ) {
            startPortionIndex = Math.max(startPortionIndex, 0);
        } else {
            startPortionIndex = 0;
        }

        if ( null != endPortionIndex ) {
            endPortionIndex = Math.min(endPortionIndex, this.content.length - 1);
        } else {
            endPortionIndex = this.content.length - 1;
        }

        for (let pos = startPortionIndex; pos <= endPortionIndex; pos++) {
            length += this.content[pos].getTextLength(bNewControl);
        }

        return length;
    }

    public isParaSimpleChanges( changes: ChangeContent[] ): boolean {
        const count = changes.length;

        for (let index = 0; index < count; index++) {
            const element = changes[index].data;

            if ( false === element.isParagraphSimpleChanges() ) {
                return false;
            }
        }

        return true;
    }

    public getHistory(): History {
        return this.parent ? this.parent.getHistory() : null;
    }

    public updateCursorType(pointX: number, pointY: number, curPage: number): void {
        return this.setCursorType(CursorType.Text);
    }

    public setCursorType(type: CursorType): void {
        this.getParent()
                .setCursorType(type);
    }

    // public getCursorType(): CursorType {
    //     return this.getParent().getCursorType();
    // }
    public isSelectedOrCursorInNewControlTitle(): boolean {
        let result = false;

        if ( this.selection.bUse ) {
            const startPos = this.selection.startPos;
            const endPos = this.selection.endPos;

            result = ( this.content[startPos].isSelectedOrCursorInNewControlTitle()
                    || this.content[endPos].isSelectedOrCursorInNewControlTitle() );
        } else {
            const portion = this.content[this.curPos.contentPos];
            result = ( false === this.isCursorAtBegin() &&
            portion.isSelectedOrCursorInNewControlTitle());
        }

        return result;
    }

    // public removeImageFromParagraph() {
    //   for (let i = 0; i < this.content.length; i++) {
    //     let portion = this.content[i];
    //     portion.removeImageFromPortion(portion.content);
    //   }
    // }

    public forceInitPara(): void {
        this.content = [];
        const portion = new ParaPortion(this);

        // const item = new ParaText(String.fromCharCode(0x00B6));
        // item.type = ParaElementType.ParaEnd;
        // portion.content[0] = item;
        portion.addEndInternal();

        this.content[0] = portion;
    }

    public isRevisionHide(): boolean {
        const doc = this.getDocument();
        if (!doc) {
            return false;
        }
        if (!doc.isFinalRevision()) {
            return false;
        }
        // if (!doc.isInRevision(this.id)) {
        //     return false;
        // }
        const contents = this.content;
        for (let index = contents.length - 1; index >= 0; index--) {
            if (contents[index].getReviewType() !== ReviewType.Remove) {
                return false;
            }
        }
        return true;
    }

    public getAllRevision() {
        const revisionManager = this.getRevisionsManager();
        if (!revisionManager.hasElement(this.id)) return [];
        const res: any[] = [];
        function buildEmptyInfo() {
            return {
                info: null,
                type: null,
                rev: null,
            };
        }
        let prevInfo = buildEmptyInfo();
        let prevDelInfo = buildEmptyInfo();
        for (const portion of this.content) {
            if (portion.isEmpty(true)) continue;
            if (!portion.isReviewType()) {
                prevInfo = buildEmptyInfo();
                prevDelInfo  = buildEmptyInfo();
                continue;
            }
            // 获取portion pos
            const pagePos = this.getCurContentPos();
            const portionIndex = portion.getCurInParaIndex();
            pagePos.add(portionIndex);
            portion.getEndPos(pagePos);

            const pos = new ParagraphContentPos();
            pos.add(portionIndex)
            const reviewInfo = portion.getReviewInfo();
            const reviewType = portion.getReviewType();
            const records = reviewInfo?.getSavedRecord();
            const bSecondRev = (ReviewType.Remove === reviewType && !!reviewInfo && !!reviewInfo.getDeleteInfo());
            const content = portion.getRevisionContent();

            if (bSecondRev) {
                const secondRevInfo = reviewInfo.getDeleteInfo();
                if (!secondRevInfo.isEqual(prevDelInfo.info)) {
                    const secondRev = {
                        ...this.getRevisionInfos(secondRevInfo, reviewType, content, pos, bSecondRev),
                        ids: [portion.id],
                        pagePos,
                        count: 1,
                    };
                    res.push(secondRev);
                    prevDelInfo.rev = secondRev;
                    prevDelInfo.info = secondRevInfo;
                } else {
                    const rev = prevDelInfo.rev;
                    rev.pagePos = pagePos;
                    rev.ids.push(portion.id);
                    rev.count++;
                }
            }

            if (ReviewType.Add === reviewType && 0 < records?.length) {
                records.forEach((record, index) => {
                    if (record) {
                        let firstDate;
                        if (0 === index && reviewInfo['firstDate']) {
                            firstDate = reviewInfo['firstDate'];
                        }
                        res.push({
                            userName: record.userName,
                            userId: record.userId,
                            time: record.date,
                            description: record.description,
                            level: record.level,
                            type: RevisionChangeType.TextRemove,
                            value: record.content,
                            bNewDate: true,
                            firstDate,
                            ids: [portion.id],
                            count: 1,
                            pagePos,
                        });
                    }
                });
            }

            if (!reviewInfo.isEqual(prevInfo.info) || prevInfo.type !== reviewType) {
                if (prevInfo.type !== reviewType) {
                    const tmp = this.getRevisionInfos(reviewInfo, reviewType, content, pos);
                    const rev = {
                        ...tmp,
                        type: bSecondRev ? RevisionChangeType.TextAdd : tmp.type,
                        ids: [portion.id],
                        pagePos,
                        count: 1,
                    };
                    res.push(rev);
                    prevInfo.type = reviewType;
                    prevInfo.rev = rev;
                }
                prevInfo.info = reviewInfo;
            } else {
                const rev = prevInfo.rev;
                rev.ids.push(portion.id);
                rev.pagePos = pagePos;
                rev.count++;
            }
        }
        return res;
    }

    public getFocusRevision(pointX: number, pointY: number, pageIndex: number): IRevisionChange[] {
        const searchPos = this.getParaContentPosByXY(pageIndex, pointX, pointY, false);
        const revisionManager = this.getRevisionsManager();
        let contentPos = searchPos.pos;
        let portion = this.content[contentPos.get(0)];
        let bRevision = (portion && portion.isReviewType());
        const result: IRevisionChange[] = [];

        if ( !bRevision && revisionManager && revisionManager.hasElement(this.id) ) {
            const revisionPos = {
                pos: new ParagraphContentPos(),
                bFound: false,
                bSelection: false,
            };
            this.getRightPos(revisionPos, contentPos);

            if ( revisionPos.bFound ) {
                contentPos = revisionPos.pos;
                portion = this.content[contentPos.get(0)];
                bRevision = (portion && portion.isReviewType());
            }
        }

        if ( bRevision ) {
            const startPos = portion.getRangeStartPos(searchPos.line - portion.startLine);
            const endPos = portion.getRangeEndPos(searchPos.line - portion.startLine) - 1;
            const contents = portion.content;

            // 光标是否落在当前修订上
            if ( 0 <= startPos && startPos <= endPos && contents[startPos] && contents[endPos] ) {
                const startLeftPos = contents[startPos].positionX;
                const endRightPos = contents[endPos].positionX + contents[endPos].width;

                bRevision = (startLeftPos <= pointX && pointX <= endRightPos) ? true : false;
            } else {
                bRevision = false;
            }
        }

        if ( bRevision ) {
            const reviewInfo = portion.getReviewInfo();
            const reviewType = portion.getReviewType();
            const records = reviewInfo?.getSavedRecord();
            const bSecondRev = (ReviewType.Remove === reviewType && !!reviewInfo && !!reviewInfo.getDeleteInfo());
            const content = portion.getRevisionContent();

            if (bSecondRev) {
                const secondRevInfo = reviewInfo.getDeleteInfo();
                const secondRev = this.getRevisionInfos(secondRevInfo, reviewType, content, contentPos, bSecondRev);
                if (secondRev) {
                    result.push(secondRev);
                }
            }

            if (ReviewType.Add === reviewType && 0 < records?.length) {
                records.forEach((record, index) => {
                    if (record) {
                        let firstDate;
                        if (0 === index && reviewInfo['firstDate']) {
                            firstDate = reviewInfo['firstDate'];
                        }
                        result.push({userName: record.userName,
                            userId: record.userId,
                            time: record.date,
                            description: record.description,
                            level: record.level,
                            type: RevisionChangeType.TextRemove,
                            value: record.content,
                            bNewDate: true,
                            firstDate,
                        });
                    }
                });
            }

            const rev = this.getRevisionInfos(reviewInfo, reviewType, content, contentPos);
            if (rev) {
                rev.type = bSecondRev ? RevisionChangeType.TextAdd : rev.type;
                result.push(rev);
            }
        }

        return result;
    }

    public acceptRevisionChanges(bAll: boolean, type?: RevisionChangeType): void {
        // const trackManager = this.logicDocument ? this.logicDocument.getRevisionsManager() : null;

        if ( this.selection.bUse || bAll ) {
            let startPos = this.selection.startPos;
            let endPos = this.selection.endPos;
            const length = this.content.length;

            if ( startPos < endPos ) {
                startPos = this.selection.endPos;
                endPos = this.selection.startPos;
            }

            if ( bAll ) {
                startPos = 0;
                endPos = length - 1;
            }

            if ( length - 1 <= endPos) {
                endPos = length - 2;
                if ( bAll || RevisionChangeType.TextPr === type ) {
                    this.content[length - 1].acceptTextPrChange();
                }
            }

            // for (let pos = endPos; pos >= startPos; pos--) {
            //     const portion = this.content[pos];
            //     if ( portion.acceptRevisionChanges ) {
            //         portion.acceptRevisionChanges(bAll, type);
            //     }
            // }

            let bModify = false;
            for (let pos = length - 1; pos >= 0; pos--) {
                const portion = this.content[pos];
                if ( portion && portion.acceptRevisionChanges ) {
                    if (portion.acceptRevisionChanges(bAll, type)) {
                        bModify = true;
                    } else {
                        const newControlName = portion.getStartNewControlName();
                        if (bModify && newControlName) {
                            this.checkNewControlPlaceContent2(newControlName);
                        }
                        bModify = false;
                    }
                }
            }

            this.correctContent();
            this.correctContentPos(false);
            this.updateTrackRevisions();
        }
    }

    public rejectRevisionChanges(bAll: boolean, type?: RevisionChangeType): void {
        // const trackManager = this.logicDocument ? this.logicDocument.getRevisionsManager() : null;

        if ( this.selection.bUse || bAll ) {
            // let startPos = this.selection.startPos;
            // let endPos = this.selection.endPos;
            const length = this.content.length - 1;

            // if ( startPos < endPos ) {
            //     startPos = this.selection.endPos;
            //     endPos = this.selection.startPos;
            // }

            // if ( bAll ) {
            //     startPos = 0;
            //     endPos = length - 1;
            // }

            // if ( length - 1 <= endPos) {
            //     endPos = length - 2;
            //     if ( bAll || RevisionChangeType.TextPr === type ) {
            //         this.content[length - 1].rejectTextPrChange();
            //     }
            // }

            // for (let pos = endPos; pos >= startPos; pos--) {
            //     const portion = this.content[pos];
            //     if ( portion.rejectRevisionChanges ) {
            //         portion.rejectRevisionChanges(bAll, type);
            //     }
            // }

            for (let pos = length - 1; pos >= 0; pos--) {
                const portion = this.content[pos];
                if ( portion && portion.rejectRevisionChanges ) {
                    portion.rejectRevisionChanges(bAll, type);
                }
            }

            this.correctContent();
            this.correctContentPos(false);
            this.updateTrackRevisions();
        }
    }

    public checkRevisionsChanges(): void {
        const revisionManager = this.getRevisionsManager();
        const checker = new ParagraphRevisionChangesChecker(this, revisionManager);
        const contentPos = new ParagraphContentPos();

        for (let index = 0, length = this.content.length; index < length; index++) {
            const element = this.content[index];
            // if ( index === length - 1 ) {
            //     checker.setParaEndPortion()
            // }

            contentPos.update(index, 0);
            element.checkRevisionsChanges(checker, contentPos, 1);
        }

        checker.flushAddRemoveChange();
        checker.flushTextPrChange();

        // if ( ReviewType.Common !== this.getReviewType() ) {
            // const startPos = this.getEndPos(false);
            // const endPos = this.getEndPos(true);
            // const revision = new Revision(revisionManager.getCurrentUserName(), revisionManager.getCurrentUserId());
            // revisionManager.addRevision(this.id, revision);
        // }
    }

    public removePrChange(): void {
        if ( this.hasPropertyChange() ) {
            this.paraProperty.removePropertyChange();
            this.updateTrackRevisions();
        }
    }

    public acceptPrChanges(): void {
        this.removePrChange();
    }

    public rejectPrChanges(): void {
        if ( this.hasPropertyChange() ) {
            this.setPropertyChange(this.paraProperty.propertyChange);
        }
    }

    public getReviewType(): ReviewType {
        const portion = this.getParaEndPortion();

        if ( portion ) {
            return portion.getReviewType();
        }

        return ReviewType.Common;
    }

    public setReviewType(type: ReviewType): void {
        const portion = this.getParaEndPortion();

        if ( portion ) {
            if ( !portion.isSingleParaEndPortion() ) {
                const endPortion = portion.splitPortion(portion.content.length - 1);
                endPortion.setReviewType(type);
                this.addToContent(this.content.length, endPortion);
            } else {
                portion.setReviewType(type);
            }
        }
    }

    public getReviewInfo(): ReviewInfo {
        const portion = this.getParaEndPortion();

        if ( portion ) {
            return portion.getReviewInfo();
        }

        return new ReviewInfo(this.logicDocument);
    }

    public getParaEndPortion(): ParaPortion {
        return this.content[this.content.length - 1];
    }

    public setReviewTypeWithInfo(type: ReviewType, info: ReviewInfo): void {
        const portion = this.getParaEndPortion();

        if ( portion ) {
            if ( !portion.isSingleParaEndPortion() ) {
                const endPortion = portion.splitPortion(portion.content.length - 1);
                endPortion.setReviewTypeWithInfo(type, info);
                this.addToContent(this.content.length, endPortion);
            } else {
                portion.setReviewTypeWithInfo(type, info);
            }
        }
    }

    public getCurrentTextBase(): ParaElementBase {
        const portion = this.getCurParaPortion();
        if (!portion) {
            return;
        }

        return portion.getCurrentText();
    }

    public getTopElement(): DocumentContentElementBase {
        const parent = this.parent;
        if (parent.isTableCellContent() || parent.isRegionContent()) {
            return parent.getTopElement();
        }

        return this;
    }

    public getPosition(type: any): {x: number, y: number, pageNum: number} {
        const portion: ParaPortion = this.getCurrentVisiblePortion();
        // if (portion.content.length === 0) {
        //     const paraContents = this.content;
        //     let index = this.getPortionIndexById(portion.id) + 1;
        //     portion = paraContents[index++];
        //     while (portion.content.length === 0) {
        //         portion = paraContents[index++];
        //         if (!portion) {
        //             return;
        //         }
        //     }
        // }
        const text: ParaElementBase = portion.getCurrentVisibleText();
        if (!text) {
            return;
        }

        const startPos = new ParagraphContentPos();
        startPos.clear();
        portion.getParentPos(startPos);
        const textIndex = portion.content.findIndex((item) => item === text);
        startPos.add(textIndex);
        const topIndex = this.parent.getDocumentSectionType();
        startPos.splice(0, 0, [topIndex]);
        const pageNum = portion.paragraph.getCurrentPageByPos(startPos);

        return {pageNum, x: text.positionX, y: text.positionY - portion.textHeight};
    }

    public getElementById(elementId: number): DocumentContentElementBase {
        return this.id === elementId ? this : null;
    }

    public canDelete(): boolean {
        let startPos = this.bApplyToAll ? 0 : this.selection.startPos;
        let endPos = this.bApplyToAll ? this.content.length - 1 : this.selection.endPos;
        if ( startPos > endPos ) {
            const temp = startPos;
            startPos = endPos;
            endPos = temp;
        }

        for (let i = startPos; i <= endPos; i++) {
            const portion = this.content[i];
            if ( this.bApplyToAll ) {
                portion.selectAll(1);
            }

            const result = portion.canDelete();

            if ( this.bApplyToAll ) {
                portion.removeSelection();
            }

            if (result === false) {
                return false;
            }
        }

        if ( this.logicDocument.isTrackRevisions() ) {
            // let startPos = this.bApplyToAll ? 0 : this.selection.startPos;
            // let endPos = this.bApplyToAll ? this.content.length - 1 : this.selection.endPos;

            // if ( startPos > endPos ) {
            //     const temp = startPos;
            //     startPos = endPos;
            //     endPos = temp;
            // }

            const revisionManager = this.getRevisionsManager();
            const curUserId = revisionManager.getCurrentUserId();
            const curLevel = revisionManager.getCurrentLevel();
            const bEquMode = this.logicDocument.getRevisionLevelEquMode();

            for (let index = startPos; index <= endPos; index++) {
                const element = this.content[index];
                if ( element ) {
                    const reviewType = element.getReviewType();
                    const reviewInfo = element.getReviewInfo();

                    if ( ReviewType.Common !== reviewType && !element.isEmpty(true) ) {
                        if ( curLevel < reviewInfo.getLevel() ) {
                            message.error('权限较低！');
                            return false;
                        } else if ( curLevel === reviewInfo.getLevel() ) {
                            if ( curUserId === reviewInfo.getUserId() ) {
                                continue;
                            } else if ( !bEquMode ) {
                                return false;
                            }
                        }
                    }
                }
            }
        }

        return true;
    }

    public getPosByElement(portion: ParaPortion): ParagraphContentPos {
        const contentPos = new ParagraphContentPos();
        const length = this.content.length;

        for (let pos = 0; pos < length; pos++) {
            const element = this.content[pos];
            contentPos.update(pos, 0);

            if ( element === portion ) {
                return contentPos;
            }
        }

        return null;
    }

    public setElementCurrentPos(bUpdateStates: boolean): void {
        this.parent.updateContentIndexing();
        this.parent.setCurrentElement(this.index, bUpdateStates);
    }

    public saveRecalculateObject(): ParagraphRecalcObject {
        const recalcObj = new ParagraphRecalcObject();
        recalcObj.save(this);
        return recalcObj;
    }

    public loadRecalculateObject(recalcObj: ParagraphRecalcObject): void {
        // console.log(recalcObj)
        recalcObj.load(this);
    }

    public getContentText(): string {
        let result = '';
        for (let index = 0, length = this.content.length; index < length; index++) {
            const portion = this.content[index];
            if ( portion && !portion.isSingleParaEndPortion() ) {
                result += portion.getTextContent();
            }
        }

        return result;
    }

    public getFirstNewControl(bSection: boolean): NewControl {
        let newControl;
        for (let index = 0, length = this.content.length; index < length; index++) {
            const portion = this.content[index];
            if ( portion && portion.isNewControlStart() ) {
                const control = this.logicDocument.getNewControlByName(portion.getStartNewControlName());
                if ( null != bSection ) {
                    if ( control && (control.isNewSection() === bSection) ) {
                        newControl = control;
                        break;
                    }
                } else {
                    if ( control ) {
                        newControl = control;
                        break;
                    }
                }
            }
        }

        return newControl;
    }

    public setParaEndProperty(): void {
        const length = this.content.length;
        if ( this.isEmpty() && 1 < length ) {
            this.content[length - 1].textProperty = this.content[length - 2].textProperty.copy();
        }
    }

    public setWestCharBreakAttribute(b: boolean): number {
        if ( b === this.paraProperty.bWordWrap || null == b ) {
            return ResultType.UnEdited;
        }

        const history = this.getHistory();
        if ( history ) {
            history.addChange(new ChangeParagraphWordWrap(this, this.paraProperty.bWordWrap, b));
        }

        this.paraProperty.bWordWrap = b;
        return ResultType.Success;
    }

    public getWestCharBreakAttribute(): boolean {
        return this.paraProperty.bWordWrap;
    }

    public isTableCellContent(): boolean {
        return (this.parent && this.parent.isTableCellContent());
    }

    public getRevisionsManager(): any {
        return (this.parent && this.parent.getRevisionsManager());
    }

    public resetParagraphCursorPos(): void {
        if (this.isEmpty() && this.bApplyToAll) {
            this.curPos.contentPos = 0;
        }
    }

    /**
     * 插入软回车
     * @returns
     */
    public addSoftNewParagraph(pos: number): number {
        if (0 > pos || pos >= this.content.length) {
            return -1;
        }
        const portion = new ParaPortion(this);
        portion.addParaItem(0, ParaElementType.ParaNewLine);
        const result = this.addToContent(pos, portion);
        if (ResultType.Success === result) {
            this.content[++this.curPos.contentPos].portionContentPos = 0;
        }

        return result;
    }

    /*
     * 清除修订信息
     */
    public resetRevisions(): void {
        this.content?.forEach((portion) => {
            portion?.resetRevisions();
        });
    }

    /**
     * 删除一个portion
     * @param pos 删除portion的索引
     */
    public contentRemove( pos: number ): boolean {
        if ( 0 > pos || this.content.length < pos ) {
            return false;
        }

        const delItem = this.content[pos];

        if ( delItem.preDelete ) {
            delItem.preDelete();
        }

        const history = this.getHistory();
        if ( history ) {
            history.addChange(new ChangeParagraphRemoveItem(this, pos, [delItem]));
        }

        this.content.splice(pos, 1);

        // this.parent.getDrawingObjects().deleteImage(this.index);
        delItem.removeImageFromPortion(delItem.content);

        if ( this.selection.startPos > pos ) {
            this.selection.startPos--;

            if ( this.selection.startPos < 0 ) {
                this.selection.startPos = 0;
            }
        }

        if ( this.selection.endPos > pos  ) {
            this.selection.endPos--;

            if ( this.selection.endPos < 0 ) {
                this.selection.endPos = 0;
            }
        }

        if ( this.curPos.contentPos > pos ) {
            this.curPos.contentPos--;

            if (this.curPos.contentPos < 0 ) {
                this.curPos.contentPos = 0;
            }
        }

        return true;
    }

    private isNewControlTextTitleOnly(newControl: NewControl): boolean {
        // pay attention the origin of newControlManager!
        // const newControlManager = this.parent.getNewControlManager();
        // const newControl = newControlManager.getNewControlByName(props.newControlName);
        // console.log(newControl)
        // console.log(newControlManager)

        if (newControl && newControl.isNewTextBox() && newControl.getHideHasTitle() === true) {
            const text = newControl.getNewControlText();
            if ( text === '' || text == null) {
                // should be true since already checked in placeholdercontent
                const title = newControl.getTitle();
                if (title != null && title !== '') {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 插入段落结束符
     * @param pos
     */
    private addParaEnd( pos: number ): void {
        const endPortion = new ParaPortion(this);
        if ( 1 <= this.content.length ) {
            endPortion.textProperty = this.content[this.content.length - 1].textProperty.copy();
        }
        endPortion.addEnd();
        this.addToContent(pos, endPortion);
    }

    /**
     * 计算当前光标所在位置的坐标等信息
     * todo: portion.recalculateCurPos的参数positionX, positionY需要修改，因为并没有被修改
     * @param contentPos
     * @param bUpdateCurPos
     */
    private recalcCurPos( contentPos: number, bUpdateCurPos: boolean ): ICurrentCursorPos {
        if (this.index === -1) {
            return null;
        }
        const paraPos = {
            line: -1,
            page: -1,
            pos: -1,
        };

        this.getCurParaPos(paraPos);
        let curLine = paraPos.line;
        const curPage = paraPos.page;

        if ( -1 !== this.curPos.line) {
            curLine = this.curPos.line;
        }

        if (this.lines.length === 0) {
            // header.recal(), but still get to last para of footer, why? cuz in controller, curHeaderFooter
            // tslint:disable-next-line: no-console
            console.warn('this.lines is empty');
            // tslint:disable-next-line: no-console
            console.trace();
            return null;
        }

        // 检查 curLine 是否有效
        if (curLine < 0 || curLine >= this.lines.length || !this.lines[curLine]) {
            // tslint:disable-next-line: no-console
            console.warn(`Invalid curLine: ${curLine}, lines length: ${this.lines.length}`);
            return null;
        }

        // 检查 ranges 数组是否存在且不为空
        if (!this.lines[curLine].ranges || this.lines[curLine].ranges.length === 0) {
            // tslint:disable-next-line: no-console
            console.warn('this.lines[curLine].ranges is empty');
            return null;
        }

        let positionX = this.lines[curLine].ranges[0].xVisible;
        const positionY = this.lines[curLine].top + this.lines[curLine].metrics.baseline;

        const startPortionPos = this.lines[curLine].ranges[0].startPos;
        const endPortionPos = this.lines[curLine].ranges[0].endPos;

        for ( let curPos = startPortionPos; curPos <= endPortionPos; curPos++) {
            const portion = this.content[curPos];
            positionX = portion.recalculateCurPos(positionX, positionY,
                            (curPos === this.curPos.contentPos ? true : false), curLine, curPage);

            if ( curPos === this.curPos.contentPos) {
                if ( true === portion.isEmpty(false) ) {
                    this.curPos.realX = positionX;
                    this.curPos.realY = positionY;
                }
                break;
            }
        }

        this.curPos.line = curLine;
        return {
            x: this.curPos.realX,
            y: this.curPos.realY,
            pageNum: this.getAbsolutePage(curPage),
            line: curLine,
            page: curPage,
        };
    }

    /**
     * 调整段落内容
     * @param startPos
     * @param endPos
     * @param bNotDeleteEmptyPortions 是否不删除空的portion
     */
    private correctContent( startPos?: number, endPos?: number, bNotDeleteEmptyPortions?: boolean ): void {
        const start = ( undefined === startPos ? 0 : Math.max(startPos - 1, 0));
        const end = ( undefined === endPos ? this.content.length - 1 : Math.min(endPos + 1, this.content.length - 1));
        const portionContentPos = this.content[this.curPos.contentPos].portionContentPos;

        for (let index = end; index >= start; index--) {
            const element = this.content[index];

            if ( true !== bNotDeleteEmptyPortions ) {
                if ( true === element.isEmpty(false) ) {
                    this.contentRemove(index);
                }
            }
        }

        this.correctContentPos2();
    }

    /**
     * 设置光标处文本属性时，对光标位置进行修正
     * @param bCorrectEndLinePos
     */
    private correctContentPos( bCorrectEndLinePos: boolean ): void {
        const count = this.content.length;
        let curPos = this.curPos.contentPos;

        // 如果光标落在行的末尾，那么将它移动到下一行的开头
        if ( true === bCorrectEndLinePos && true === this.content[curPos].isCusorInEnd() ) {
            const tempPos = curPos + 1;

            if ( tempPos < count && true === this.content[tempPos].isStartFromNewLine() ) {
                curPos = tempPos;
                this.content[curPos].moveCursorToStartPos();
            }
        }

        while ( curPos > 0 && true === this.content[curPos].isCusorNeedCorrectPos() ) {
            curPos--;
            this.content[curPos].moveCursorToEndPos(false);
        }

        this.curPos.contentPos = curPos;
    }

    /**
     * 修正光标位置
     */
    private correctContentPos2(): void {
        if (true === this.selection.bUse) {
            return ;
        }

        const count = this.content.length;
        const curPos = Math.min(Math.max(0, this.curPos.contentPos), count - 1);

        // while ( 0 < curPos ) {
        //     curPos--;
        //     this.content[curPos].moveCursorToEndPos();
        // }

        // while ( count > curPos ) {
        //     curPos++;
        //     this.content[curPos].moveCursorToStartPos();
        // }

        this.correctCurPosRangeLine();
        this.curPos.contentPos = curPos;
    }

    /**
     * 修正: 当选择内容在一个portion内时，非全部选中，且选择方向为正选：direction = 1，
     * 需要将endPos - 1：即将选择的末尾位置调整到当前portion的末尾
     */
    private correctContentPos3(): void {
        if (true === this.selection.bUse && this.selection.startPos < this.selection.endPos) {
            this.selection.endPos--;
        }
    }

    /**
     * 修正当前光标所在的位置信息
     */
    private correctCurPosRangeLine(): void {
        if ( -1 === this.curPos.line ) {
            return;
        }

        const curParaPos = this.getParaContentPos(false, false);
        const ranges = this.getRangesByPos(curParaPos);

        this.curPos.line = -1;
        this.curPos.range = -1;

        for (let index = 0, rangesCount = ranges.length ; index < rangesCount; index++) {
            const rangeIndex = ranges[index].range;
            const lineIndex = ranges[index].line;

            if ( undefined !== this.lines[lineIndex] && undefined !== this.lines[lineIndex].ranges[rangeIndex] ) {
                const range = this.lines[lineIndex].ranges[rangeIndex];

                if ( 0 < range.width ) {
                    this.curPos.line = lineIndex;
                    this.curPos.range = rangeIndex;
                    break;
                }
            }
        }
    }

    /**
     * 计算可存储的line spacing和line spacing type
     * @param paraLineSpace
     */
    private perpareParaSpacing(paraLineSpace: {lineSpacing: number; ratio: number; type: LineSpacingType}):
                {lineSpacing: number; lineSpacingType: LineSpacingType} {
        // console.log(paraLineSpace)
        const sINGLELINESPACE = LineSpacingRatio.Single;
        const tEXTHEIGHT = 16;

        let rawLineSpacing = 1;
        let linespacingType = LineSpacingType.Single;
        const preparedParaSpacing = {
            lineSpacing: LineSpacingRatio.Single,
            lineSpacingType: LineSpacingType.Single,
        };

        switch (paraLineSpace.type) {
            case LineSpacingType.Single:
            case LineSpacingType.Min:
                rawLineSpacing = 1;
                linespacingType = LineSpacingType.Single;
                break;

            case LineSpacingType.SingeHalf:
                rawLineSpacing = 1.5;
                linespacingType = LineSpacingType.SingeHalf;
                break;

            case LineSpacingType.Double:
                rawLineSpacing = 2;
                linespacingType = LineSpacingType.Double;
                break;

            case LineSpacingType.Multi:
                if (!rawLineSpacing || rawLineSpacing < 1) {
                    rawLineSpacing = 3;
                } else {
                    rawLineSpacing = paraLineSpace.ratio;
                }
                linespacingType = LineSpacingType.Multi;
                break;

            case LineSpacingType.Fixed:

                if (!rawLineSpacing) {
                    // Fixed type saved as cm.  Usu. should not reach here
                    rawLineSpacing = this.calculateSingleLineSpacing(tEXTHEIGHT, sINGLELINESPACE);
                } else {
                    // Format is CM here
                    rawLineSpacing = paraLineSpace.ratio;
                }

                linespacingType = LineSpacingType.Fixed;
                break;

            default:
                break;
        }

        if ( paraLineSpace.type !== LineSpacingType.Fixed ) {
            // convert raw ratio to real ratio
            // key-wasm by tinyzhi
            // const ascDesSingeSpace = (sINGLELINESPACE - 1) / 2;
            // preparedParaSpacing.lineSpacing = ascDesSingeSpace * rawLineSpacing * 2 + 1;
            preparedParaSpacing.lineSpacing = WasmInstance.instance._Paragraph_perpareParaSpacing_Count(
                                                sINGLELINESPACE, rawLineSpacing);
            // end by tinyzhi
            preparedParaSpacing.lineSpacingType = linespacingType;
        } else {
            preparedParaSpacing.lineSpacing = rawLineSpacing;
            preparedParaSpacing.lineSpacingType = linespacingType;
        }

        return preparedParaSpacing;
    }

    /**
     * 应用当前文本属性
     * @param textProperty
     */
    private applyTextPr( textProperty: TextProperty ): number {
        const option = {
            result: ResultType.UnEdited,
        };

        if ( this.isSelectedAll(false) ) {
            return this.applyAllTextProp(textProperty);
        } else if (this.selection.bUse) {
            let startPos = this.selection.startPos;
            let endPos = this.selection.endPos;
            let direction = 1; // 1: 向下选择，-1：向上选择
            let bCorrectSelectEndPos = false;

            // 段落结束符属性处理，分两种情况：
            // 1. 最后一个portion的内容包含段落结束符
            // 2. 最后一个portion的内容只有一个段落结束符
            // 段落结束符属性不再进行单独处理，其属性不再起作用
            // let length = this.content.length;
            // //if ( ( endPos === length - 1 && this.content[endPos].isSelectionParaEnd() ) ||
            //   if ( endPos === length - 2 && this.content[length - 1].isSingleParaEndPortion() )  {
            //         bSelectionParaEnd = true;
            // }

            const newControlManager = this.parent.getNewControlManager();
            let startNewControl: NewControl = null;
            let endNewControl: NewControl = null;
            if ( newControlManager ) {
                let startContentPos = this.parent.getStartRemoveSelectionPos();
                let endContentPos = this.parent.getEndRemoveSelectionPos();

                if ( -1 === startContentPos.compare(this.getCurContentPosInDoc(true, true)) ) {
                    startContentPos = this.getCurContentPosInDoc(true, true);
                }

                if ( 1 === endContentPos.compare(this.getCurContentPosInDoc(true, false)) ) {
                    endContentPos = this.getCurContentPosInDoc(true, false);
                }

                const forwardOverNewControl = newControlManager.getPosNewControl(startContentPos);
                const behindOverNewControl = newControlManager.getPosNewControl(endContentPos);

                if ( forwardOverNewControl && true === forwardOverNewControl.isPlaceHolderContent() ) {
                    startNewControl = forwardOverNewControl;
                }

                if ( behindOverNewControl && true === behindOverNewControl.isPlaceHolderContent() ) {
                    endNewControl = behindOverNewControl;
                }
            }
            // 选中区域是否是同一个portion
            if ( startPos === endPos ) {
                if ( startNewControl && startNewControl === endNewControl ) {
                    option.result = (startNewControl.getTitle() ? this.applyNewControlTextProps(textProperty) :
                                        startNewControl.applyTextProperty(textProperty));
                } else {

                    direction = this.parent.getSelectionDirection();
                    const curPortion = this.content[endPos];

                    if ( true === curPortion.bPlaceHolder ) {
                        const num = curPortion.applyProperty(textProperty);
                        return num;
                    }

                    if ( 1 === direction ) {
                        bCorrectSelectEndPos = (true !== curPortion.isEndPos(curPortion.selection.endPos));
                    }

                    const portions = curPortion.applyTextPr(textProperty, false, option);
                    if (portions) {
                        this.replaceCurPortion(startPos, portions, direction);
                    }
                   
                    if (-1 === direction) {
                        if (this.selection.startPos < this.selection.endPos ) {
                            const temp = this.selection.endPos;
                            this.selection.endPos = this.selection.startPos;
                            this.selection.startPos = temp;
                        }
                        
                    } else if (bCorrectSelectEndPos) {
                        this.correctContentPos3();
                    }
                }
                // console.log(this.content[startPos], ",  ------" + this.content[startPos].portionContentPos);
            } else {

                if ( startPos > endPos ) {
                    const temp = endPos;
                    endPos = startPos;
                    startPos = temp;
                    direction = -1;
                }

                const startPortion = this.content[startPos];
                const endPortion = this.content[endPos];
                if ( 1 === direction ) {
                    bCorrectSelectEndPos = // ( true !== endPortion.bPlaceHolder &&
                                    true !== endPortion.isEndPos(endPortion.selection.endPos);
                }

                // 中间全部选中的portion
                for ( let curPos = startPos + 1; curPos < endPos; curPos++ ) {
                    this.content[curPos].applyTextPr(textProperty, false, option);
                }

                let bCorrectContent = false;

                if ( endNewControl ) {
                    option.result = (endNewControl.getTitle() ? this.applyNewControlTextProps(textProperty) :
                                    endNewControl.applyTextProperty(textProperty)) && option.result;
                } else {
                    // 必须先设置endPos，如果先设置startPos，那么endPos就不准确
                    const endPortions = endPortion.applyTextPr(textProperty, false, option);
                    if (endPortions !== undefined && ParaElementType.ParaPortion === endPortion.getType() ) {
                        this.replaceCurPortion(endPos, endPortions, direction);
                        bCorrectContent = true;
                    }
                }

                if ( startNewControl ) {
                    option.result = (startNewControl.getTitle() ? this.applyNewControlTextProps(textProperty) :
                                    startNewControl.applyTextProperty(textProperty)) && option.result;
                } else {
                    const startPortions = startPortion.applyTextPr(textProperty, false, option);
                    if (startPortions !== undefined && ParaElementType.ParaPortion === startPortion.getType() ) {
                        this.replaceCurPortion(startPos, startPortions);
                        bCorrectContent = true;
                    }
                }

                // 删除多余空portion
                if ( true === bCorrectContent ) {
                    this.correctContent();
                }

                if (bCorrectSelectEndPos) {
                    this.correctContentPos3();
                }
            }

            // todo : 段落尾部属性处理，需要在段落结束符是一个单独的portion，其他情况不需要单独处理
            // if ( true === bSelectionParaEnd ) {
            //     this.content[this.content.length - 1].applyTextPr(textProperty);
            //     this.textPr.applyTextPr(textProperty);
            // }
        } else {
            // add a portion is like splitting a portion into 3(or 2) parts
            // console.log(circularParse(circularStringify(this)));
            const curPos = this.curPos.contentPos;
            const curPortion = this.content[curPos];
            const newControl = this.getCursorInNewControl();

            if ( newControl && true === newControl.isPlaceHolderContent() ) {
                const num = (newControl.getTitle() ? ResultType.UnEdited :
                                newControl.applyTextProperty(textProperty));
                return num;
            }

            if ( newControl && newControl.getTitle() && 1 <= newControl.getTitle().length ) {
                const length = curPortion.content.length;
                if ( curPortion.isNewControlStart() && 1 < length && curPortion.portionContentPos < length ) {
                    return ;
                }
            }

            const portions = curPortion.applyTextPr(textProperty, false, option);

            if (portions !== undefined && ParaElementType.ParaPortion === curPortion.getType() ) {
                this.replaceCurPortion(curPos, portions);
            }

            if ( true === this.isCursorAtEnd() ) {
                // nothing
            }
            // console.log(circularParse(circularStringify(this)));
        }

        return option.result as number;
    }

    /**
     * 更新段落portion，设置当前光标为新插入的portion
     * @param pos
     * @param portions
     * @param direction
     */
    private replaceCurPortion( pos: number, portions: ParaPortion[], direction?: number ): void {
        const leftPortion = portions[0];
        const centerPortion = portions[1];
        const rightPortion = portions[2];

        let centerPortionPos = pos;
        const oldCurPos = this.curPos.contentPos;
        const oldSelectionStartPos = this.selection.startPos;
        const oldSelectionEndPos = this.selection.endPos;

        // 光标不在portion开始位置: portion不为空，且不在段首
        if ( null != leftPortion ) {
            this.addToContent(pos + 1, centerPortion);
            centerPortionPos += 1;
        }

        // 光标不在portion结束位置
        if ( null != rightPortion ) {
            this.addToContent(centerPortionPos + 1, rightPortion);
        }

        if ( oldCurPos === pos ) {
            this.curPos.contentPos = centerPortionPos;
        } else if ( oldCurPos > pos ) { // 选中多个portion
            // console.log(oldCurPos, ", CurPos   " + pos);
            if ( null != leftPortion && null != rightPortion ) {
                this.curPos.contentPos = oldCurPos + 2;
            } else if ( null != leftPortion || null != rightPortion ) {
                this.curPos.contentPos = oldCurPos + 1;
            }
        }
        this.curPos.line = -1;

            // 光标选择portion
        if ( true === this.selection.bUse ) {
            // 设置选择开始位置portion
            if ( oldSelectionStartPos === pos ) {
                if ( oldSelectionStartPos > oldSelectionEndPos ) {
                    // 反选，且选中多个portion
                    if ( null != leftPortion && null != rightPortion ) {
                        this.selection.startPos = pos + 2;
                    } else if ( null != leftPortion || null != rightPortion ) {
                        this.selection.startPos = pos + 1;
                    } else {
                        this.selection.startPos = pos;
                    }
                } else if ( oldSelectionStartPos < oldSelectionEndPos) {
                    // 正选多个portion
                    this.selection.startPos = pos;
                } else {
                    // 只选中一个portion
                    this.selection.startPos = pos;

                    if ( null != leftPortion && null != rightPortion ) {
                        this.selection.endPos = pos + 2;
                    } else if ( null != leftPortion || null != rightPortion ) {
                        this.selection.endPos = pos + 1;
                    } else {
                        this.selection.endPos = pos;
                    }
                }
            } else if ( oldSelectionStartPos > pos ) {
                // console.log(oldSelectionStartPos, ", Selection " + pos);
                if ( null != leftPortion && null != rightPortion ) {
                    this.selection.startPos = oldSelectionStartPos + 2;
                } else if ( null != leftPortion || null != rightPortion ) {
                    this.selection.startPos = oldSelectionStartPos + 1;
                }
            }

            // 设置选择结束portion
            if ( oldSelectionEndPos === pos ) {
                if ( oldSelectionStartPos < oldSelectionEndPos ) {

                    if ( null != leftPortion && null != rightPortion ) {
                        this.selection.endPos = pos + 2;
                    } else if ( null != leftPortion || null != rightPortion ) {
                        this.selection.endPos = pos + 1;
                    } else {
                        this.selection.endPos = pos;
                    }
                } else if ( oldSelectionStartPos > oldSelectionEndPos) {
                    this.selection.endPos = pos;
                }
            } else if ( oldSelectionEndPos > pos ) {
                // console.log(oldSelectionStartPos, ", Selection " + pos);
                if ( null != leftPortion && null != rightPortion ) {
                    this.selection.endPos = oldSelectionEndPos + 2;
                } else if ( null != leftPortion || null != rightPortion ) {
                    this.selection.endPos = oldSelectionEndPos + 1;
                }
            }
        }
    }

    /**
     * 段落属性发生变化
     */
    private addPropertyChange(): void {
        if ( this.hasPropertyChange() ) {
            this.paraProperty.addPropertyChange();

            const history = this.getHistory();
            if ( history ) {
                history.addChange(new ChangeParagraphPropertyChange(this, null, this.paraProperty.propertyChange));
            }
        }
    }

    /**
     * 简单设置选中
     * @param startPos
     * @param endPos
     */
    private setSelectionByPos(endPos: ParagraphContentPos, startPos: ParagraphContentPos): boolean {
        let startPorIndex = startPos.get(0);
        let endPorIndex = endPos.get(0);
        let startTextIndex = startPos.get(1);
        let endTextIndex = endPos.get(1);
        let bBefore: boolean = true;
        if (endPos.compare(startPos) === -1) {
            bBefore = false;
            let temp = startPorIndex;
            startPorIndex = endPorIndex;
            endPorIndex = temp;
            temp = startTextIndex;
            startTextIndex = endTextIndex;
            endTextIndex = temp;
        }
        const selection = this.selection;
        selection.bUse = true;
        selection.bStart = true;
        selection.startPos = startPorIndex;
        selection.endPos = endPorIndex;
        this.curPos.contentPos = endPorIndex;
        const contents = this.content;
        let actPor = contents[startPorIndex];
        let porSelect = actPor.selection;
        porSelect.bUse = true;
        porSelect.startPos = startTextIndex;
        if (startPorIndex === endPorIndex) {
            porSelect.endPos = endTextIndex;
        } else {
            porSelect.endPos = actPor.content.length - 1;
            if (porSelect.endPos < 0) {
                porSelect.endPos = 0;
            }
            actPor = contents[endPorIndex];
            porSelect = actPor.selection;
            porSelect.bUse = true;
            porSelect.startPos = 0;
            porSelect.endPos = endTextIndex;
        }

        actPor.portionContentPos = endTextIndex;
        return bBefore;
    }

    private getNextTextPos(): ParagraphContentPos {
        const pos = new ParagraphContentPos();
        const types = [ParaElementType.ParaText, ParaElementType.ParaDrawing, ParaElementType.ParaBarcode,
            ParaElementType.ParaMedEquation, ParaElementType.ParaTab, ParaElementType.ParaSpace];
        let flag = this.getRightTextPos(pos, types);
        if (flag) {
            return pos;
        } else if (flag === undefined) {
            return;
        }
        flag = this.getLeftTextPos(pos, types);
        if (flag) {
            return pos;
        } else if (flag === undefined) {
            return;
        }

        return null;
    }

    private getLeftTextPos(pos: ParagraphContentPos, types: number[]): boolean {
        const contents = this.content;
        const posIndex = this.curPos.contentPos;
        let portionIndex: number = posIndex;
        // const types = [ParaElementType.ParaText, ParaElementType.ParaDrawing,
        //     ParaElementType.ParaMedEquation, ParaElementType.ParaTab];
        // const len = contents.length - 2;
        while (true) {
            const curPortion = contents[portionIndex];
            if (curPortion.isComment()) {
                return;
            }
            let texts: ParaElementBase[];
            if (posIndex === portionIndex) {
                texts = curPortion.content.slice(0, curPortion.portionContentPos);
            } else {
                texts = curPortion.content.slice(0);
            }

            if (texts && texts.length > 0) {
                let textIndex: number;
                textIndex = texts.reverse()
                    .findIndex((base) => types.includes(base.type));

                if (textIndex !== -1) {
                    let textLen: number = curPortion.portionContentPos - 1;
                    if (posIndex !== portionIndex) {
                        textLen = texts.length - 1;
                    }
                    textIndex = textLen - textIndex;
                    if (textIndex < 0) {
                        textIndex = 0;
                    }
                    pos.add(portionIndex);
                    pos.add(textIndex);
                    return true;
                }
            }
            if (--portionIndex < 0) {
                break;
            }
        }

        return false;
    }

    private getRightTextPos(pos: ParagraphContentPos, types: number[]): boolean {
        const contents = this.content;
        const posIndex = this.curPos.contentPos;
        let portionIndex: number = posIndex;
        // const types = [ParaElementType.ParaText, ParaElementType.ParaDrawing,
        //     ParaElementType.ParaMedEquation, ParaElementType.ParaTab];
        const len = contents.length - 2;
        while (true) {
            const curPortion = contents[portionIndex];
            if (curPortion.isComment()) {
                return;
            }
            let texts: ParaElementBase[];
            if (posIndex === portionIndex) {
                texts = curPortion.content.slice(curPortion.portionContentPos + 1);
            } else {
                texts = curPortion.content;
            }

            if (texts && texts.length > 0) {
                let textIndex: number = texts.findIndex((base) => types.includes(base.type));

                if (textIndex !== -1) {
                    if (posIndex === portionIndex) {
                        textIndex = curPortion.portionContentPos + 1 + textIndex;
                    } else {
                        textIndex++;
                    }
                    pos.add(portionIndex);
                    pos.add(textIndex);
                    return true;
                }
            }
            if (++portionIndex > len) {
                break;
            }
        }

        return false;
    }

    private setPrintPortionHide(bPlaceHolder: boolean, portion: ParaPortion,
                                newControlProp: INewControlProperty, porIndex: number,
                                cleanMode: CleanModeType, bClearStruct?: boolean): boolean {
        if (bPlaceHolder) {
            if (cleanMode != null && cleanMode === CleanModeType.CleanModeSpecial) {
                //
            } else {
                // hide placeholder
                portion.setHidden(true);
            }
            return;
        }

        if (!newControlProp) {
            return;
        }
        // 处理节控件和其他特殊控件
        const types2 = [NewControlType.SignatureBox, NewControlType.Button,
            NewControlType.AddressBox, NewControlType.MultiRadio, NewControlType.RadioButton];
            
        // 特殊处理节控件
        if (newControlProp.newControlType === NewControlType.Section) {
            // 如果设置了打印隐藏属性，则隐藏该节控件及其内容
            if (newControlProp.printSelected) {
                const contents = portion.paragraph.content;
                for (let index = porIndex, len = contents.length; index < len; index++) {
                    const curPortion = contents[index];
                    if (curPortion.isNewControlEnd() && curPortion.getEndNewControlName() === newControlProp.newControlName) {
                        break;
                    }
                    curPortion.setHidden(true);
                }
            }
            return true;
        }

        // 处理特殊控件
        if (types2.includes(newControlProp.newControlType)) {
            if (newControlProp.printSelected) {
                // 隐藏开始边框
                portion.setHidden(true);
                
                // 隐藏内容和结束边框
                const contents = portion.paragraph.content;
                for (let index = porIndex, len = contents.length; index < len; index++) {
                    const curPortion = contents[index];
                    if (curPortion.isNewControlEnd()) {
                        curPortion.setHidden(true);
                        break;
                    }
                    curPortion.setHidden(true);
                }
            }
            return true;
        }
        
        // 处理其他非特殊控件
        if (!types2.includes(newControlProp.newControlType)) {
            if ((newControlProp.printSelected || bClearStruct) && !newControlProp.checked) {
                const contents = portion.paragraph.content;
                for (let index = porIndex, len = contents.length; index < len; index++) {
                    const curPortion = contents[index];
                    if (curPortion.isNewControlEnd()) {
                        break;
                    }
                    curPortion.setHidden(true);
                }
            } else if (bClearStruct) {
                const contents = portion.paragraph.content;
                const type = ParaTextName.ParaCheckbox;
                for (let index = porIndex, len = contents.length; index < len; index++) {
                    const curPortion = contents[index];
                    if (curPortion.isNewControlEnd()) {
                        break;
                    }
                    const text = curPortion.content.find((node) => node['_name'] === type);
                    if (text) {
                        curPortion.setHidden(true);
                        break;
                    }
                }
            }
            return true;
        }
        const types = [NewControlType.RadioButton, NewControlType.MultiRadio];
        if (types.includes(newControlProp.newControlType)) {
            const type = ParaTextName.ParaCheckbox;
            if (newControlProp.printSelected || bClearStruct) {
                const contents = portion.paragraph.content;
                const items = newControlProp.newControlItems.map((data) => {
                    return {...data};
                });
                const actItem = items.find((current) => current.bSelect);
                if (!actItem) {
                    for (let index = porIndex, len = contents.length; index < len; index++) {
                        const curPortion = contents[index];
                        if (curPortion.isNewControlEnd()) {
                            break;
                        }
                        curPortion.setHidden(true);
                    }
                    return;
                }

                let item;
                const trueRadio = NEWCTR_BTN_MAP.get(newControlProp.showType + 'true');
                const falseRadio = NEWCTR_BTN_MAP.get(newControlProp.showType + 'false');
                const showRight = newControlProp.showRight;
                let bSelect;
                let bChange;

                if ( !showRight ) {
                    item = items.shift();
                    bSelect = item.bSelect;
                    for (let index = porIndex, len = contents.length; index < len; index++) {
                        const curPortion = contents[index];
                        if (curPortion.isNewControlEnd()) {
                            break;
                        }
                        if (bChange) {
                            curPortion.setHidden(true);
                        }
                        const res = curPortion.getFlagByStr(trueRadio, falseRadio);
                        // if (showRight) {
                        //     if (bSelect) {
                        //         if (res === 2) {
                        //             item = items.shift();
                        //             bSelect = item.bSelect;
                        //             bChange = true;
                        //         }
                        //     } else {
                        //         if (res === 1) {
                        //             item = items.shift();
                        //             bSelect = item.bSelect;
                        //         }
                        //         curPortion.setHidden(true);
                        //     }
                        // } else {
                        if (bSelect) {
                            if (bChange !== undefined && res === 2) {
                                item = items.shift();
                                bSelect = item.bSelect;
                                curPortion.setHidden(true);
                                bChange = true;
                                continue;
                            }
                        } else {
                            if (bChange !== undefined && res === 1) {
                                item = items.shift();
                                bSelect = item.bSelect;
                            } else {
                                if (bChange !== undefined && res === 2) {
                                    item = items.shift();
                                    bSelect = item.bSelect;
                                }
                                curPortion.setHidden(true);
                            }
                        }
                        bChange = false;
                        if (bClearStruct) {
                            const text = curPortion.content.find((node) => node['_name'] === type);
                            if (text) {
                                curPortion.setHidden(true);
                            }
                        }
                    }
                } else {
                    item = items.pop();
                    bSelect = item.bSelect;
                    let bStart = false;
                    let bSpaceNum = false; // 下一个portion是否是spaceNum
                    const spaceNum = newControlProp.spaceNum;
                    let bOldVer = false;
                    let bNextPrint = false;
                    for (let index = contents.length - 1; index >= porIndex; index--) {
                        const curPortion = contents[index];
                        if (curPortion.isNewControlStart() && bStart) {
                            break;
                        }
                        if ( curPortion.isNewControlEnd()
                            && curPortion.getEndNewControlName() === newControlProp.newControlName) {
                            bStart = true;
                        }

                        if ( !bStart || curPortion.isEmpty(false) ) {
                            continue;
                        }

                        const res = curPortion.getFlagByStr(trueRadio, falseRadio);
                        const curItemCode = item?.code;
                        const portionText = curPortion.getTextContent();
                        if (1 !== res && 2 !== res && spaceNum && portionText === curItemCode) {
                            bOldVer = true;
                        }

                        if (bSelect) {
                            if (res === 2) {
                                item = items.pop();
                                bSelect = item.bSelect;
                            } else if ( 0 === res ) {
                                item = items.pop();
                                bSelect = false;
                                if (bOldVer) {
                                    bNextPrint = true;
                                }
                            } else if ( null == res ) {
                                curPortion.setHidden(true);
                            }
                        } else {
                            if (res === 1) {
                                bSelect = item.bSelect;
                                if (bClearStruct) {
                                    const text = curPortion.content.find((node) => node['_name'] === type);
                                    if (text) {
                                        curPortion.setHidden(true);
                                    }
                                }
                                continue;
                            } else if ( 0 === res && (!bOldVer || (bOldVer &&!bSpaceNum)) ) {
                                bSelect = item.bSelect;
                                item = items.pop();
                            }

                            if (!(bNextPrint && bSpaceNum)) {
                                curPortion.setHidden(true);
                            } else {
                                bNextPrint = true;
                            }
                        }

                        if (1 !== res && 2 !== res && spaceNum && portionText === curItemCode) {
                            bSpaceNum = true;
                        } else if (bSpaceNum && spaceNum && portionText.length === spaceNum) {
                            bSpaceNum = false;
                        }
                        if (bClearStruct && res != null) {
                            const text = curPortion.content.find((node) => node['_name'] === type);
                            if (text) {
                                curPortion.setHidden(true);
                            }
                        }
                    }
                }
            } else if (bClearStruct) {
                const contents = portion.paragraph.content;
                for (let index = porIndex, len = contents.length; index < len; index++) {
                    const curPortion = contents[index];
                    if (curPortion.isNewControlEnd()) {
                        break;
                    }
                    const text = curPortion.content.find((node) => node['_name'] === type);
                    if (text) {
                        curPortion.setHidden(true);
                    }
                }
            }
            return true;
        }

        return false;
    }

    private getStartAndEndPos(selection: any): number[] {
        let startPos = selection.startPos;
        let end = selection.endPos;
        if (startPos > end) {
            const temp = startPos;
            startPos = end;
            end = temp;
        }

        return [startPos, end];
    }

    /** 获取portion的批注条件 */
    private getCommentCondi(portion: ParaPortion, posIndex: number): string {
        // 结构化元素占位符
        let bPlaceholder = false;
        while (posIndex--) {
            const tmpPortion = this.content[posIndex];
            if (!tmpPortion || tmpPortion.isNewControlEnd()) {
                break;
            }
            if (tmpPortion.isNewControlStart()) {
                bPlaceholder = tmpPortion.isPlaceHolder();
                break;
            }
        }
        return '' + +portion.isComment() + +portion.isParaEndPortion() +
        +(portion.isNewControlStart() || portion.isRegionTitle() || bPlaceholder) +
        +portion.isNewControlEnd();
    }

    // private addCommentPortion(bStart: boolean, commentId: number, index: number, comment?: Comment): void {
    //     const portion = new ParaPortion(this);
    //     portion.addComment(bStart, commentId);
    //     this.addToContent(index, portion);
    //     if (comment) {
    //         const portion1 = new ParaPortion(this);
    //         portion1.addComment(!bStart, comment.getId());
    //         if (bStart === false) {
    //             index += 1;
    //         }
    //         this.addToContent(index, portion1);
    //     }
    // }

    // private getDeleteComment(portion: ParaPortion, index: number): {bStart: boolean, id: number, both?: boolean} {
    //     if (portion.isComment()) {
    //         if (portion.content.length === 0) {
    //             return;
    //         }
    //         return {bStart: portion.isStartComment(), id: portion.getCommentId(), both: true};
    //     } else if (portion.content.length === 0) {
    //         const actPortion = this.content[index - 1];
    //         if (actPortion && actPortion.isComment()) {
    //             return {bStart: actPortion.isStartComment(), id: actPortion.getCommentId(), both: true};
    //         }
    //     }

    //     return;
    // }


    private updateTrackRevisions(): void {
        const revisionManager = this.getRevisionsManager();
        if ( revisionManager && this.parent.isTrackRevisions() ) {
            revisionManager.checkElement(this);
        }
    }

    private getContentPosByXY(pageIndex: number, pointX: number, pointY: number): ParagraphContentPos {
        const searchPos = this.getParaContentPosByXY(pageIndex, pointX, pointY, false);
        const contentPos = new ParagraphContentPos();
        const topParent = this.parent.getTopDocument();

        switch (topParent.getDocumentSectionType()) {
            case DocumentSectionType.Header:
                contentPos.add(0);
                break;
            case DocumentSectionType.Document:
                contentPos.add(1);
                break;
            case DocumentSectionType.Footer:
                contentPos.add(2);
                break;
        }

        if ( this.parent instanceof DocumentContent ) {
            this.parent.getParentIndexs(contentPos);
        }
        contentPos.add(this.index);
        contentPos.add(searchPos.pos.get(0));
        contentPos.add(searchPos.pos.get(1));

        return contentPos;
    }

    private getDeleteCommentId(): number {
        const pos = this.curPos.contentPos;
        const contents = this.content;
        let bChange = false;
        for (let index = pos, len = contents.length; index < len; index++) {
            const position = contents[index];
            if (position.content.length === 0) {
                continue;
            }
            if (position.isComment() && !position.isStartComment()) {
                bChange = true;
                break;
            }
            return -1;
        }
        if (!bChange) {
            return -1;
        }
        bChange = false;
        for (let index = pos - 1; index >= 0 ; index--) {
            const position = contents[index];
            if (position.content.length === 0) {
                continue;
            }
            if (position.isComment() && position.isStartComment()) {
                return position.getCommentId();
            }
            return -1;
        }

        return -1;
    }

    // private getValidPortionIndex(pos: number, dir: number): number {
    //     const contents = this.content;
    //     // let protion = contents[pos];
    //     // if (portion.isNewControlEndBoder()) {

    //     // }
    //     // 向前遍历
    //     if (dir === 1) {
    //         for (let index = pos; index >= 0; index--) {
    //             const portion = contents[index];
    //             if (portion.isNewControl()) {
    //                 continue;
    //             }
    //             return index;
    //         }
    //         pos = 0;
    //     } else { // dir === -1 向后遍历
    //         for (let index = pos, len = contents.length; index < len; index++) {
    //             const portion = contents[index];
    //             if (portion.isNewControl()) {
    //                 continue;
    //             }
    //             return index;
    //         }
    //         pos = contents.length - 1;
    //     }

    //     return pos;
    // }

    private applyAllTextProp(textPr: TextProperty): number {
        let curRes: number = ResultType.UnEdited;
        for (let index = 0, length = this.content.length; index < length; index++) {
            const item = this.content[index];
            if (length === 1) {
                const texts = item.content;
                const endTextIndex = texts.length - 1;
                if (endTextIndex > 0 && texts[endTextIndex].type === ParaElementType.ParaEnd) {
                    const addPortion = this.splitPortion1(item, index, endTextIndex);
                    curRes = this.addToContent(1, addPortion) && curRes;
                }
            }
            const option: {result: number, bContainParaEnd?: boolean} = {
                result: ResultType.UnEdited as number,
                bContainParaEnd: true,
            };
            item.applyTextPr(textPr, true, option);
            curRes = curRes && option.result;
        }

        return curRes;
    }

    /**
     * 修正光标索引（跳过Comment）
     * @param curPos 当前索引
     * @returns 新索引
     */
    private fixCurPosJumpingComment(curPos: number): number {
        const curPortion = this.content[curPos];
        // 定位后判断批注位置
        let commentPortion: ParaPortion = null;
        if (curPortion.isComment()) {
            commentPortion = curPortion;
        } else {
            let siblingPortion: ParaPortion = null;
            let offset = 0;
            if (curPortion.isCusorInStart()) {
                siblingPortion = this.content[curPos - 1];
                offset = -1;
            } else if (curPortion.isCusorInEnd()) {
                siblingPortion = this.content[curPos + 1];
                offset = 1;
            }
            if (siblingPortion && siblingPortion.isComment()) {
                commentPortion = siblingPortion;
                curPos += offset;
            }
        }
        if (commentPortion) {
            // 紧邻批注尾时，需要移动至批注尾进行输入
            const bStart = commentPortion.isStartComment();
            const pos = new ParagraphContentPos();
            let line = 0;
            let siblingPortion: ParaPortion;
            if (bStart) {
                // to left
                const prevPortion = this.content[--curPos];
                if (!prevPortion) {
                    const newPortion = new ParaPortion(this);
                    newPortion.setTextProperty(curPortion.textProperty.copy());
                    this.addToContent(0, newPortion);
                    pos.add(0);
                    pos.add(0);
                    this.setParaContentPos(pos, 0);
                    return 0;
                }
                pos.add(curPos);
                pos.add(prevPortion.content.length);
                line = prevPortion.getLinesCount() - 1;
                siblingPortion = prevPortion;
            } else {
                // to right
                siblingPortion = this.content[++curPos];
                pos.add(curPos);
                pos.add(0);
                line = 0;
            }

            const isUnValidPortion = siblingPortion.isComment() || siblingPortion.isParaEnd() ||
                                    siblingPortion.isNewControlStart() || siblingPortion.isNewControlEnd();
            if (isUnValidPortion) {
                const newPortion = new ParaPortion(this);
                newPortion.textProperty = curPortion.textProperty.copy();
                this.addToContent(curPos, newPortion);
            }
            this.setParaContentPos(pos, line);
        }
        return curPos;
    }

    private getRevisionInfos(reviewInfo: ReviewInfo, reviewType: ReviewType, content: string,
                             contentPos: ParagraphContentPos, bSecondRev: boolean = false): IRevisionChange {
        const iRevisionChange: IRevisionChange = {
            userName: reviewInfo.getUserName(), // revisionManager.getCurrentUserName(),
            userId: reviewInfo.getUserId(), // revisionManager.getCurrentUserId(),
            time: reviewInfo.getTime(),
            description: reviewInfo.getDescription(),
            level: 1,
            type: ReviewType.Add === reviewType ?
                    RevisionChangeType.TextAdd : RevisionChangeType.TextRemove,
            value: content,
        };

        if (reviewInfo['bResetDate']) {
            iRevisionChange.value = '日期已清空';

            if (reviewInfo['firstDate']) {
                iRevisionChange.bNewDate = true;
                iRevisionChange.firstDate = reviewInfo['firstDate'];
            }
        } else if (reviewInfo['firstDate'] && !reviewInfo.getSavedRecord()) {
            iRevisionChange.bNewDate = true;
            iRevisionChange.firstDate = reviewInfo['firstDate'];
        } else if (reviewInfo['bFirstSet'] && !reviewInfo.getSavedRecord()) {
            iRevisionChange.bNewDate = true;
        }

        for (let index = contentPos.get(0) - 1; index >= 0; index--) {
            const element = this.content[index];
            if ( element && reviewType === element.getReviewType() && !element.isEmpty(true)) {
                const preReviewInfo = element.getReviewInfo();
                const secondRevInfo = bSecondRev ? preReviewInfo.getDeleteInfo() : null;
                if ( (!bSecondRev && preReviewInfo.isEqual(reviewInfo)) ||
                    (bSecondRev && (secondRevInfo && secondRevInfo.isEqual(reviewInfo)))
                    ) {
                    iRevisionChange.value = element.getRevisionContent() + iRevisionChange.value;
                } else {
                    break;
                }
            } else if (element.isEmpty(true)) {
                continue;
            } else {
                break;
            }
        }

        for (let index = contentPos.get(0) + 1; index < this.content.length; index++) {
            const element = this.content[index];
            if ( element && reviewType === element.getReviewType() && !element.isEmpty(true)) {
                const nextReviewInfo = element.getReviewInfo();
                const secondRevInfo = bSecondRev ? nextReviewInfo.getDeleteInfo() : null;
                if ( (!bSecondRev && nextReviewInfo.isEqual(reviewInfo)) ||
                    (bSecondRev && (secondRevInfo && secondRevInfo.isEqual(reviewInfo)))
                    ) {
                    iRevisionChange.value += element.getRevisionContent();
                } else {
                    break;
                }
            } else if (element.isEmpty(true)) {
                continue;
            } else {
                break;
            }
        }

        return iRevisionChange;
    }

    /**
     * 结构化元素只有占位符的时候，且存在标题
     * @param textPro
     */
    private applyNewControlTextProps(textPro: TextProperty): number {
        let result = ResultType.UnEdited;
        let startPos = this.selection.startPos;
        let endPos = this.selection.endPos;
        if ( startPos > endPos ) {
            const temp = endPos;
            endPos = startPos;
            startPos = temp;
        }

        for (let index = startPos; index <= endPos; index++) {
            const portion = this.content[index];
            if (portion && !portion.isSelectionEmpty(false)) {
                portion.applyProperty(textPro.copy());
                result = ResultType.Success;
            }
        }

        return result;
    }

    /**
     * 计算当前portion的高度
     * @param option 包括：width, lineHeight, maxHeight, portion, maxWidth
     * @param callback 执行时过滤条件，返回true进行过滤
     * @returns 
     */
    private measureEachTextToCell(option: any, callback: (item: any, index: number) => boolean): boolean {
        let {width, lineHeight, maxHeight, portion, maxWidth} = option;
        let bChanged: boolean;
        const contents = portion.content;
        contents.forEach((textVm: ParaElementBase, index) => {
            if (callback && callback(textVm, index)) {
                return;
            }

            if (textVm.getType() === ParaElementType.ParaNewLine) {
                portion.recalculateMeasureContent(lineHeight, this.paraProperty);
                maxHeight += portion.ascent + lineHeight + portion.descent;
                lineHeight = 0;
                width = 0;
                bChanged = true;
                return;
            }

            if (!textVm.content || !textVm.isVisible()) {
                return;
            }

            bChanged = true;
            const rect = measure(textVm.content, portion.textProperty);
            const widthVisible = textVm.widthVisible ||rect[0].width;
            const nextEle = contents[index + 1];
            let nextWidth: number = 0;
            if (nextEle && !nextEle.canBeAtBeginOfLine()) {
                const nextRect = measure(nextEle.content, portion.textProperty);
                nextWidth = nextRect[0].width;
            } else if (!textVm.isSpaceAfter()) { // 是否中文、符号、空格外的字符需要对整个单词进行检查
                const prevEle = contents[index - 1];
                if (prevEle && prevEle.isSpaceAfter()) {
                    nextWidth = this.getCurrentWordWidth(contents, index + 1, portion.textProperty);
                }
            }
            if (width + widthVisible + nextWidth > maxWidth) {
                portion.recalculateMeasureContent(lineHeight, this.paraProperty);
                maxHeight += portion.ascent + lineHeight + portion.descent;
                width = widthVisible;
                lineHeight = rect[0].height;
            } else {
                const height = rect[0].height;
                width += widthVisible;
                if (lineHeight < height) {
                    lineHeight = height;
                }
            }
        });

        option.width = width;
        option.lineHeight = lineHeight;
        option.maxHeight = maxHeight;

        return bChanged;
    }

    private getCurrentWordWidth(contents: ParaElementBase[], index: number, textProperty): number {
        let width: number = 0;
        for (let len = contents.length; index < len; index++) {
            const textVm = contents[index];
            if (textVm.isSpaceAfter()) {
                return width;
            }

            const rect = measure(textVm.content, textProperty);
            width += textVm.widthVisible || rect[0].width;
        }
        return width;
    }

    private getRemoveElement(direction: number): any {
        const contents = this.content;
        const curPos = this.curPos.contentPos;
        if (direction === -1) { // 往前删
            for (let index = curPos; index > -1; index--) {
                const portion = contents[index];
                if (!portion || !portion.content.length || portion.isEmpty(true) || portion.isHidden()) {
                    continue;
                }
                const texts = portion.content;
                for (let curIndex = portion.portionContentPos - 1; curIndex > -1; curIndex--) {
                    const text = texts[curIndex];
                    if (!text?.isVisible()) {
                        continue;
                    }
                    switch (text.type) {
                        case ParaElementType.ParaNewControlBorder: {
                            return;
                        }
                        default: {
                            if (!text.content || !text.width) {
                                continue;
                            }

                            return text;
                        }
                    }
                }
            }
            return;
        }
        // 往后删
        for (let curIndex = curPos, len = contents.length; curIndex < len; curIndex++) {
            const portion = contents[curIndex];
            if (!portion || !portion.content.length || portion.isEmpty(true) || portion.isHidden()) {
                continue;
            }
            const texts = portion.content;
            for (let index = portion.portionContentPos, len = texts.length; index < len; index++) {
                const text = texts[index];
                if (!text.isVisible()) {
                    continue;
                }
                switch (text.type) {
                    case ParaElementType.ParaEnd:
                    case ParaElementType.ParaNewControlBorder: {
                        return;
                    }
                    default: {
                        if (!text.content || !text.width) {
                            continue;
                        }

                        return text;
                    }
                }
            }
        }
    }

    /**
     * 当前段落在当前单元格内是否完全能正常显示
     * @param portion 当前portion
     * @param text 
     * @returns 
     */
    private isVisibleAtCurCell(portion: ParaPortion, text: string): boolean {
        // 当前段落在表格内
        if (!this.isTableCellContent()) {
            return false;
        }
        const parent = this.parent as any;
        // 当前单元格不跨页
        if (parent.pages.length !== 1) {
            return false;
        }
        const cell: any = parent.parent;
        if (!cell.row.table.isNISTable()) {
            return false;
        }

        const maxHeight = this.measureParaHeight();
        // let width = 0;
        // let maxHeight = 0;
        // const maxWidth = this.xLimit - this.x;
        // let lineHeight = 0;
        // const obj: any = {width, maxHeight, lineHeight, maxWidth};
        // this.content.forEach((portion) => {
        //     this.measureEachTextToCell(obj, null)
        // });

        // lineHeight = obj.lineHeight;
        // maxHeight = obj.maxHeight;
        // portion.recalculateMeasureContent(lineHeight, this.paraProperty);
        // maxHeight += portion.ascent + lineHeight + portion.descent;
        // this.getDocument().startAction(HistoryDescriptionType.DocumentParagrahText);

        const lines = cell.oldSelectionBounds;
        if (lines?.length === 1) {
            const line = lines[0];
            if (maxHeight > line.height) {
                return false;
            }
            // 内容高度比原来的小
            if (line.contentHeight > maxHeight) {
                const maxContentHeight = cell.getMaxHeight();
                if (maxContentHeight > maxHeight + 0.01) {
                    return true;
                }
            } else if (line.height > maxHeight) { // 内容比以前大，但是还在当前边框可视范围内
                return true;
            }
        }

        return false;
    }

    private isNewControlPrintSelected(newControl: NewControl): boolean {
        if (!newControl) {
            return false;
        }
        const types2 = [NewControlType.Section, NewControlType.SignatureBox, NewControlType.Button,
            NewControlType.AddressBox, NewControlType.MultiRadio, NewControlType.RadioButton];
        if (newControl.getPrintSelected() && !types2.includes(newControl.getType())) {
            return true;
        }

        return false;
    }

    /**
     * 为所有portions设置wavyUnderlineId
     */
    private setWavyUnderlineIdToAllPortions(wavyUnderlineId: string): void {
        for (const portion of this.content) {
            if (portion.textProperty.textDecorationLine === 4) { // WavyUnderline
                portion.wavyUnderlineId = wavyUnderlineId;
            }
        }
    }

    /**
     * 为选中的portions设置wavyUnderlineId
     */
    private setWavyUnderlineIdToSelectedPortions(wavyUnderlineId: string): void {
        if (this.selection.bUse) {
            let startPos = this.selection.startPos;
            let endPos = this.selection.endPos;
            
            if (startPos > endPos) {
                const temp = endPos;
                endPos = startPos;
                startPos = temp;
            }

            for (let i = startPos; i <= endPos; i++) {
                const portion = this.content[i];
                if (portion && portion.textProperty.textDecorationLine === 4) { // WavyUnderline
                    portion.wavyUnderlineId = wavyUnderlineId;
                }
            }
        } else {
            // 没有选择时，设置当前portion
            const curPortion = this.content[this.curPos.contentPos];
            if (curPortion && curPortion.textProperty.textDecorationLine === 4) { // WavyUnderline
                curPortion.wavyUnderlineId = wavyUnderlineId;
            }
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ParagraphRecalcObject {
    public x: number;
    public y: number;
    public xLimit: number;
    public yLimit: number;
    public startPage: number;
    public pages: ParaPage[];
    public lines: ParaLine[];
    public recalcContent: PortionRecalculateObject[];
    // public clipInfo: [];

    constructor(doc?: any) { // documentContent?
        this.x      = 0;
        this.y      = 0;
        this.xLimit = 0;
        this.yLimit = 0;

        this.pages = [];
        this.lines = [];
        this.recalcContent = [];
    }

    public save(para: Paragraph): void { // documentContent?
        this.x      = para.x;
        this.y      = para.y;
        this.xLimit = para.xLimit;
        this.yLimit = para.yLimit;

        this.pages = para.pages;
        this.lines = para.lines;

        const content = para.content;
        const count = content.length;

        for ( let index = 0; index < count; index++ ) {
            this.recalcContent[index] = content[index].saveRecalculateObject();
        }
    }

    public load(para: Paragraph): void {
        para.x      = this.x;
        para.y      = this.y;
        para.xLimit = this.xLimit;
        para.yLimit = this.yLimit;

        para.pages = this.pages;
        para.lines = this.lines;

        const count = para.content.length;
        for ( let index = 0; index < count; index++ ) {
            para.content[index].loadRecalculateObject( this.recalcContent[index] );
        }
    }
}
