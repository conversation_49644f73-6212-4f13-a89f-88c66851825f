import Document from './Document';
import { ChangeContent, ChangeBaseContent, ChangeBase } from './HistoryChange';
import ParaPortion from './Paragraph/ParaPortion';
import { HistroyItemType, ContentChangeType, HistoryDescriptionType, HistroyItemRecalType } from './HistoryDescription';
import Paragraph from './Paragraph';
import { ContentChangesElement } from './ContentChanges';
import { DocumentElementState } from './HistoryState';
import HeaderFooter from './HeaderFooter';
// import { NewControlContent } from './NewControl/NewControl';

export interface IRecalculateData {
    Inline?: {
        pos: number,
        pageNum: number,
    };
    // Flow         : [], 图片等
    headerFooters?: HeaderFooter[];
    // Drawings     : {
    //     All       : false,
    //     Map       : {},
    //     ThemeInfo : null
    // },
    tables?: boolean[];
    // NumPr        : [],
    // NotesEnd     : false,
    // NotesEndPage : 0,
    update?: boolean;
    type?: HistroyItemRecalType;
    data?: any;
}

export interface IOperateResult {
    res: boolean;           // 操作成功
    bNeedRecal: boolean;    // 是否需要排版
}

/**
 * 文档操作历史点
 */
class HistoryPoint {
    public state: DocumentElementState[][] = null; // 文档的当前状态（光标，选择）
    public items: ChangeContent[] = null;
    public time: number; // 当前时间
    public additional: any = null; // 其他信息
    public description: number = null; // 当前point的名称

    constructor(state: DocumentElementState[][], items: ChangeContent[], time: number, description: number) {
        this.state = state;
        this.items = items;
        this.time = time;
        this.description = description;
    }
}

/**
 * 修改记录类：redo，undo
 */
export default class History {
    public index: number = -1; // 文档修改index
    public savedIndex: number = null; // 文档保存点index
    public recalIndex: number = -1;  // 可撤销、重做的步数？
    public bForceSave: boolean = false; // 文档强制保存

    public userSaveMode: boolean = false; //  是否用户保存
    public userSavedIndex: number = null; // 用户保存点index
    public document: Document;
    public lastState: DocumentElementState[][] = null;

    public recalculateData: IRecalculateData =
        {
            Inline       : {
                pos     : -1,
                pageNum : 0,
            },
            // Flow         : [],
            headerFooters       : [],
            // Drawings     : {
            //     All       : false,
            //     Map       : {},
            //     ThemeInfo : null
            // },
            tables       : [],
            // NumPr        : [],
            // NotesEnd     : false,
            // NotesEndPage : 0,
            update       : true,
        };

    public points: HistoryPoint[]; // 记录当前位置的更改
    public storedData: any[]; // 记录多个位置的更改

    public bAddChanges: boolean = false; // 文档
    public bCollectChanges: boolean = false;
    public bMinorChanges: boolean = false; // 是否文档变化较小
    public bTurnOffHistory: boolean; // 是否关闭撤销、删除操作

    constructor(doc: Document) {
        this.points = [];
        this.document = doc;
        this.bTurnOffHistory = true;
    }

    /**
     * 是否可以进行撤销操作
     */
    public canUndo(): boolean {
        if ( 0 <= this.index ) {
            return true;
        }

        return false;
    }

    /**
     * 是否可以进行重做操作
     */
    public canRedo(): boolean {
        if ( 0 < this.points.length && this.points.length - 1 > this.index ) {
            return true;
        }

        return false;
    }

    /**
     * 撤销操作
     */
    public undo(): IRecalculateData {
        // this.checkUnionLastPoints();

        if ( true !== this.canUndo() ) {
            return null;
        }

        if ( this.index === this.points.length - 1 ) {
            this.lastState = this.document.getDocumentState();
        }

        this.clearRecaculateData();

        // console.log("undo:  ", this);

        const point = this.points[this.index--];
        let bDirty = false;

        for (let index = point.items.length - 1; index >= 0; index--) {
            const element = point.items[index];

            if ( element.data ) {
                element.data.undo();
                element.data.refreshRecalData();

                if ( !bDirty && element.data.isDirty() ) {
                    bDirty = true;
                }
            }

            this.updateContentChangesOnUndo(element);
        }

        if ( point ) {
            this.document.setDocumentState(point.state);
            if ( bDirty ) {
                this.document.setDirty();
            }
        }

        return this.recalculateData;
    }

    /**
     * 重做操作
     */
    public redo(): IRecalculateData {
        if ( true !== this.canRedo() ) {
            return null;
        }

        // console.log("redo:  ", this);

        const point = this.points[++this.index];

        this.clearRecaculateData();
        let bDirty = false;

        for (let index = 0, count = point.items.length - 1; index <= count; index++) {
            const element = point.items[index];

            if ( element.data ) {
                element.data.redo();
                element.data.refreshRecalData();

                if ( !bDirty && element.data.isDirty() ) {
                    bDirty = true;
                }
            }

            this.updateContentChangesOnRedo(element);
        }

        // 将光标位置、选择等状态恢复到下一个记录点
        let state = null;
        if ( this.index === this.points.length - 1 ) {
            state = this.lastState;
        } else {
            state = this.points[this.index + 1].state;
        }

        this.document.setDocumentState(state);

        if ( bDirty ) {
            this.document.setDirty();
        }

        return this.recalculateData;
    }

    /**
     * 生成新的记录点
     */
    public createNewHistoryPoint( description: number, seleState?: any ): boolean {
        if ( false === this.bTurnOffHistory ) {
            return false;
        }

        if ( this.document && this.document.onCreateNewHistoryPoint ) {
            this.document.onCreateNewHistoryPoint();
        }

        this.bAddChanges = false;
        this.bCollectChanges = false;

        this.checkUnionLastPoints();

        const state = seleState ? seleState : this.document.getDocumentState();

        this.points[++this.index] = new HistoryPoint(state, [], new Date().getTime(), description );

        this.points.length = this.index + 1;

        // if (6 < this.points.length) {
        //     this.points.splice(0, this.points.length - 6);
        //     this.index = 5;
        // }

        return true;
    }

    /**
     * 特殊point：创建新point捕捉发生的所有变化，使用后，必须通过removeLastPoint删除此history
     */
    public createNiewHistoryPointForChanges(): void {
        this.points[++this.index] = new HistoryPoint(null, [], null, -1 );

        this.points.length = this.index + 1;
        this.bCollectChanges = true;
    }

    /**
     * 删除最后一个point，和createNiewHistoryPointForChanges配套使用
     */
    public removeLastPoint(): void {
        if ( -1 < this.index ) {
            this.bCollectChanges = false;
            this.index--;
            this.points.length = this.index + 1;
        }
    }

    public isLastPointEmpty(): boolean {
        if ( !this.points[this.index] || 0 >= this.points[this.index].items.length ) {
            return true;
        }

        return false;
    }

    public clearRedo(): void {
        this.points.length = this.index + 1;
    }

    public clear(): void {
        this.index = -1;
        this.bAddChanges = false;
        this.points.length = 0;
        this.savedIndex = null;
        this.userSavedIndex = null;
        this.bTurnOffHistory = false;
        this.clearRecaculateData();
    }

    public clearData(): void {
        this.index = -1;
        this.bAddChanges = false;
        this.points.length = 0;
        this.savedIndex = null;
        this.userSavedIndex = null;
        this.clearRecaculateData();
    }

    public canAdd(): boolean {
        if ( 0 > this.index || false === this.bTurnOffHistory || this.document?.isLoadFile()) {
            return false;
        }

        return true;
    }

    // 添加change操作
    public addChange( changeClass: ChangeBase, data?: any ): void {
        if ( 0 > this.index || false === this.bTurnOffHistory ) {
            return;
        }

        if ( this.recalIndex >= this.index ) {
            this.recalIndex = this.index - 1;
        }

        let curClass;
        if ( changeClass ) {
            curClass = changeClass.getClass();
            data = changeClass;
        }

        const changeContent = new ChangeContent(curClass, data, !this.bMinorChanges);

        this.points[this.index].items.push(changeContent);

        // 修改的对象如果符合要求，在执行undo后，会删除特定的history，执行redo后，会重新加入特定的history
        if ( changeClass && changeClass.isContentChange() ) {
            const bAdd = changeClass.isAdd();
            const count = changeClass.getItemsCount();

            const contentChanges = new ContentChangesElement( ( true === bAdd ? ContentChangeType.ContentChangeAdd :
                ContentChangeType.ContentChangeRemove ), count, changeContent, data.position);
            curClass.addContentChanges(contentChanges);
        }
    }

    public recalculateDataAdd( recalData: IRecalculateData ): void {
        if ( true !== this.recalculateData.update ) {
            return;
        }

        if ( null == recalData || null == recalData.data) {
            return;
        }

        if ( 0 > this.recalculateData.Inline.pos || ( this.recalculateData.Inline.pos > recalData.data.pos )
            || ( this.recalculateData.Inline.pos === recalData.data.pos &&
                this.recalculateData.Inline.pageNum > recalData.data.pageNum )) {
                this.recalculateData.Inline.pos = recalData.data.pos;
                this.recalculateData.Inline.pageNum = recalData.data.pageNum;
        }
    }

    public recalculateTableGridAdd(id: number): void {
        this.recalculateData.tables[id] = true;
    }

    public getRecalData(recalData: IRecalculateData, arrChanges?: any[], startIndex?: number,
                        endIndex?: number): IRecalculateData {
        if ( recalData ) {
            this.recalculateData = recalData;
        } else if (arrChanges) {
            this.clearRecaculateData();

            const start = null != startIndex ? startIndex : 0;
            const end = null != endIndex ? endIndex : arrChanges.length;
            for (let index = start; index < end; index++) {
                const item = arrChanges[index];
                        
                if (item && item.bNeedRecal && item.changeClass.refreshRecalData) {
                    item.changeClass.refreshRecalData(item.data);
                }
            }
        } else {
            if (0 <= this.index) {
                this.clearRecaculateData();

                for (let index = this.recalIndex + 1; index <= this.index; index++) {
                    const point = this.points[index];
                    
                    for (let index = 0, length = point.items.length; index < length; index++) {
                        const item = point.items[index];
                        
                        if (item.bNeedRecal && item.changeClass.refreshRecalData) {
                            item.changeClass.refreshRecalData(item.data);
                        }
                    }
                }
            }
        }

        return this.recalculateData;
    }

    public resetRecalIndex(): void {
        this.recalIndex = this.index;
    }

    /**
     * 文档内容是否是小的改变：发生了简单的更改（输入或删除文本）
     */
    public isSimpleChanges(): any {
    //     let nCount;
    //     let items: ChangeContent[];

    //     if ( 1 !== this.index - this.recalIndex && -1 <= this.recalIndex ) {
    //         nCount = 0;
    //         items = [];

    //         for (let index = this.recalIndex + 1; index <= this.index; index++) {
    //             items = items.concat(this.points[index].items);
    //             nCount += this.points[index].items.length;
    //         }
    //     } else if ( 0 <= this.index ) {
    //         const point = this.points[this.index];
    //         nCount = point.items.length;
    //         items = point.items;
    //     } else {
    //         return [];
    //     }

    //     if ( 0 < items.length ) {
    //         const className = items[0].changeClass;

    //         for (let index = 0; index < nCount; index++) {
    //             const item = items[index];

    //             if ( className !== item.changeClass ) {
    //                 return [];
    //             }
    //         }

    //         if ( className instanceof ParaPortion && className.isSimpleChanges(items)) {
    //             return [items[0]];
    //         }
    //     }

        return [];
    }

    /**
     * 段落简单改变
     */
    public isParaSimpleChanges(): Paragraph {
        let nCount;
        let items;

        if ( 1 !== this.index - this.recalIndex && -1 <= this.recalIndex ) {
            nCount = 0;
            items = [];

            for (let index = this.recalIndex + 1; index <= this.index; index++) {
                items = items.concat(this.points[index].items);
                nCount += this.points[index].items.length;
            }
        } else if ( 0 <= this.index ) {
            const point = this.points[this.index];
            nCount = point.items.length;
            items = point.items;
        } else {
            return null;
        }

        if ( 0 < items.length ) {
            let para = null;

            for (let index = 0; index < nCount; index++) {
                const element = items[index].changeClass;

                if ( element instanceof Paragraph ) {
                    if ( null === para ) {
                        para = element;
                    } else if ( para !== element ) {
                        return null;
                    }
                } else if ( element.getParagraph ) {
                    if ( null === para ) {
                        para = element.getParagraph();
                    } else if ( para !== element.getParagraph() ) {
                        return null;
                    }
                // } else if ( element instanceof NewControlContent ) {
                //     if ( null === para ) {
                //         para = element.getStartBorderPortion().paragraph;
                //     } else if ( para !== element.getStartBorderPortion().paragraph ) {
                //         return null;
                //     }
                } else {
                    return null;
                }
            }

            for (let index = 0; index < nCount; index++) {
                const item = items[index];
                const element = item.changeClass;
                if (item.data && item.data.bParaSimpleChange === false) {
                    return null;
                }

                if ( !element.isParaSimpleChanges || false === element.isParaSimpleChanges(element) ) {
                    return null;
                }

                if ( item && item.data && HistroyItemType.ParagraphWordWrap === item.data.type) {
                    return null;
                }
            }

            return para;
        }

        return null;
    }

    /**
     * 是否有文档修改操作
     */
    public hasChanges(): boolean {
        if ( !this.document ) {
            return false;
        }

        if ( -1 === this.index && false === this.bForceSave ) {
            return false;
        }

        if ( this.index !== this.recalIndex || true === this.bForceSave ) {
            return true;
        }
        return false;
    }

    public saveRedoPoints(): void {
        const data = [];

        for (let index = this.index + 1, count = this.points.length; index < count; index++) {
            data.push(this.points[index]);
        }

        this.storedData.push(data);
    }

    public popRedoPoints(): void {
        if ( 0 >= this.storedData.length ) {
            return ;
        }

        const points = this.storedData[this.storedData.length - 1];
        this.points.length = this.index + 1;

        for (let index = 0, count = points.length; index < count; index++) {
            this.points[this.index + index + 1] = points[index];
        }

        this.storedData.length -= 1;
    }

    /**
     * 不使用撤销、删除操作
     */
    public turnOff(): void {
        this.bTurnOffHistory = false;
    }

    /**
     * 使用撤销、删除操作
     */
    public turnOn(): void {
        this.bTurnOffHistory = true;
    }

    public isTurnOn(): boolean {
        return this.bTurnOffHistory;
    }

    /**
     * 检查是否可以合并最后两个historyPoint
     */
    public checkUnionLastPoints(): boolean {
        // 禁用重新计算时，不会组合历史记录
        if ( true !== this.document.isOnRecalculate() ) {
            return false;
        }

        const count = this.points.length;
        if ( 2 > count ) {
            return false;
        }

        const point1 = this.points[count - 1];
        const point2 = this.points[count - 2];

        // 倒数第2个historyPoint的长度是否 > 63，且是文档新增的单词：不要组合超过63个元素的单词
        if ( point2.items.length > 63 && HistoryDescriptionType.DocumentAddLetterUnion === point2.description ) {
            return false;
        }

        let startIndex1 = 0;
        let startIndex2 = 0;

        if ( point1.items.length > 0 && point1.items[0].data
            && HistroyItemType.TableIdDescription === point1.items[0].data.type ) {
            startIndex1 = 1;
        }

        if ( point2.items.length > 0 && point2.items[0].data
            && HistroyItemType.TableIdDescription === point2.items[0].data.type ) {
            startIndex2 = 1;
        }

        let newDescription;
        if ( ( HistoryDescriptionType.DocumentCompositeInput === point2.description
            || HistoryDescriptionType.DocumentCompositeInputReplace === point2.description)
             && HistoryDescriptionType.DocumentCompositeInputReplace === point1.description ) {
                newDescription = HistoryDescriptionType.DocumentCompositeInput;
        } else {
            // todo:
            return;
        }

        if ( 0 !== startIndex1 ) {
            point1.items.splice(0, 1);
        }

        if ( 0 !== startIndex2 ) {
            point2.items.splice(0, 1);
        }

        // 合并最后两个HistoryPoint
        const newPoint = new HistoryPoint(point2.state, point2.items.concat(point1.items),
                                            new Date().getTime(), newDescription);
        this.points.splice(count - 2, 2, newPoint);

        if ( this.index >= this.points.length) {
            const diff = this.points.length - 1 - this.index;
            this.index += diff;
            this.recalIndex = Math.max(-1, this.recalIndex + diff);
        }

        return true;
    }

    /**
     * 是否可以删除最后一个point
     */
    public canRemoveLastPoint(): boolean {
        if ( 0 >= this.points.length
             || ( true !== this.userSaveMode && null !== this.savedIndex && this.savedIndex >= this.points.length - 1 )
             || ( true === this.userSaveMode && null !== this.userSavedIndex &&
                    this.userSavedIndex >= this.points.length - 1 ) ) {
            return false;
        }

        return true;
    }

    public createIncrePointData(index: number, startPoint: number, lastPoint: number, version: string): any {
        const point = this.points[index];
        if ( point ) {
            if ( 0 < point.items.length ) {
                const firstItem = point.items[0];
                if ( 'IncrePointData' === firstItem.changeClass
                    && HistroyItemType.TableIdDescription === firstItem.data.type ) {
                    point.items.splice(0, 1);
                }
            }

            const data = {
                type: HistroyItemType.TableIdDescription,
                des: point.description,
                length: point.items.length,
                index,
                startPoint,
                lastPoint,
                version,
            };

            const item = new ChangeContent('IncrePointData', data, false);
            point.items.splice(0, 0, item);
        }
    }

    public getHistoryCount(): number {
        return this.points.length;
    }

    public clearRecaculateData(index?: number): void {
        this.recalculateData = {
            Inline       : {
                pos     : -1,
                pageNum : 0,
            },
            // Flow         : [],
            headerFooters       : [],
            // Drawings     : {
            //     All       : false,
            //     Map       : {},
            //     ThemeInfo : null
            // },
            tables       : [],
            // NumPr        : [],
            // NotesEnd     : false,
            // NotesEndPage : 0,
            update       : true,
        };

        if (0 <= index) {
            this.executeExternalActions(index);
        }
    }

    public getNonRecalcChanges(): ChangeBaseContent[] {
        const changes = [];
        
        if (-1 !== this.recalIndex && 1 !== this.index - this.recalIndex) {
            for (let index = this.recalIndex + 1; index <= this.index; index++) {
                this.getChangesFromPoint(index, changes);
            }
        } else {
            this.getChangesFromPoint(this.index, changes);
        }

        return changes;
    }

    public getChangesFromPoint(index: number, changes: any[]): any[] {
        if (!changes) {
            changes = [];
        }

        const point = this.points[index];
        if (point) {
            for (let index = 0, count = point.items.length; index < count; index++) {
                changes.push(point.items[index].data);
            }
        }

        return changes;
    }

    public getCurRecalIndex(): number {
        return this.recalIndex;
    }

    /**
     * 当前point的索引值
     * @returns
     */
    public getCurPointIndex(): number {
        return this.index;
    }

    private isClear(): boolean {
        if ( 0 >= this.points.length) {
            return true;
        }

        return false;
    }

    /**
     * 文档内容是否被改变
     * @param changeClass
     * @param data
     */
    private isContentChange( changeClass: any, data: ChangeBaseContent ): boolean {
        if ( ( changeClass instanceof Document && ( HistroyItemType.DocumentAddItem === data.type ||
                                                    HistroyItemType.DocumentRemoveItem === data.type ) )
             || ( changeClass instanceof Paragraph && ( HistroyItemType.ParagraphAddItem === data.type ||
                                                        HistroyItemType.ParagraphRemoveItem === data.type ) )
             || ( changeClass instanceof ParaPortion && ( HistroyItemType.ParaPortionAddItem === data.type ||
                                                            HistroyItemType.ParaPortionRemoveItem === data.type ) ) ) {
            return true;
        }

        return false;
    }

    /**
     * 文档内容是否增加
     * @param changeClass
     * @param data
     */
    private isAddContentChange( changeClass: any, data: ChangeBaseContent ): boolean {
        if ( ( changeClass instanceof Document && HistroyItemType.DocumentAddItem === data.type )
             || ( changeClass instanceof Paragraph && HistroyItemType.ParagraphAddItem === data.type )
             || ( changeClass instanceof ParaPortion && HistroyItemType.ParaPortionAddItem === data.type ) ) {
            return true;
        }

        return false;
    }

    private updateContentChangesOnUndo( item: ChangeContent ): void {
        if ( this.isContentChange(item.changeClass, item.data)) {
            item.changeClass.contentChanges.removeByHistoryItem(item);
        }
    }

    private updateContentChangesOnRedo( item: ChangeContent ): void {
        if ( this.isContentChange(item.changeClass, item.data)) {
            const bAdd = this.isAddContentChange(item.changeClass, item.data);
            const count = this.getCountInContentChange(item.changeClass, item.data);

            const changes = new ContentChangesElement( ( true === bAdd ) ? ContentChangeType.ContentChangeAdd :
                                                ContentChangeType.ContentChangeRemove, count, item, item.data.position);
            item.changeClass.addContentChanges(changes);
        }
    }

    private getCountInContentChange( changeClass: any, data: ChangeBaseContent ): number {
        if ( ( changeClass instanceof Document && HistroyItemType.DocumentRemoveItem === data.type )
            || ( changeClass instanceof Paragraph || changeClass instanceof ParaPortion ) ) {
            return data.items.length;
        }

        return 1;
    }

    /**
     * 执行额外的动作：没有或者无法加入point节点，只能在执行完undo、redo之后再执行
     * @param index
     */
    private executeExternalActions(index: number): void {
        const point = this.points[index];
        if (HistoryDescriptionType.DocumentMergeTableCells === point?.description) {
            const newControlManager = this.document?.getNewControlManager();
            newControlManager?.updateNewControlPos();
        }
    }
}
