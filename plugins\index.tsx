interface IImageEditor {

    onsave: (json: string, base64: string, dimension: {width: number; height: number}) => void;
  
    onclose: () => void;
  
    create(): Promise<void>;
  
    open(json: string): void;

    openByOriginImage(base64: string): void;
  
    close(): void;
 
}
let editor: IImageEditor;
export async function getImageEditor(style?: {color?: string; size?: number;}): Promise<IImageEditor> {
  if(editor) {
    return editor;
  } else {
    const { createImageEditor } = await import('./image-editor');
    return editor = await createImageEditor({color: '#0000FF', size: 6,}); // 初始化画笔样式
  }
}


let docGen;
/**
 * 将文档转换为Docx
 * @param doc {Document} 文档对象
 * @returns Blob
 */
export async function generateDocx(doc: any): Promise<Blob> {
    if (!docGen) {
        const { docxGenerator } = await import('./lazy-docx');
        docGen = docxGenerator;
    }
    return docGen(doc);
}

let excelTableGen;
/**
 * 将表格集合转换为Excel
 * @param tables {Table[]} 表格对象数组
 * @returns Blob
 */
export async function generateExcelByTable(tables: any[]): Promise<Blob> {
    if (!excelTableGen) {
        const { excelTableGenerator } = await import('./lazy-excel');
        excelTableGen = excelTableGenerator;
    }
    return excelTableGen(tables);
}