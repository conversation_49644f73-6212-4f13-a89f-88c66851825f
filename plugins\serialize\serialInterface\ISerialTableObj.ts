import { ITableCellOptions, ITableOptions, ITableRowOptions } from 'docx';
import { ISerialBase, SerialObjType } from './ISerialBase';

export interface ISerialTableCellObj extends ISerialBase, Omit<ITableCellOptions, 'children'> {
    type: SerialObjType.TableCell;
    isVerticalMerged?: boolean;
}

export interface ISerialTableRowObj extends Omit<ISerialBase, 'children'>, Omit<ITableRowOptions, 'children'> {
    type: SerialObjType.TableRow;
    children: ISerialTableCellObj[];
}

export interface ISerialTableObj extends Omit<ISerialBase, 'children'>, Omit<ITableOptions, 'rows'> {
    name?: string;
    type: SerialObjType.Table;
    children: ISerialTableRowObj[];
}
