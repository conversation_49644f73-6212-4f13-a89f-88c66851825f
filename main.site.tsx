import * as React from 'react';
import { EmrEditor } from './src/components/editor/Main';
import { WasmInstance } from './src/common/WasmInstance';
import { isGlobalTestData, consoleLog, editorCallback } from './src/common/GlobalTest';
import ExternalEvent from './src/common/external/Event';
import {fetchWithSignature} from './src/common/ParseWater';

import { setTheme } from '@hz-editor/theme';
import { initCustomFont } from './src/model/core/util-custom-font'
import { createRoot } from 'react-dom/client';
import { IExternalEvent } from '@/common/external/IExternalInterface';
import ReactDOM from 'react-dom/client';


/* IFTRUE_WATER */
class EditorServerMessageExt2 {
  private _callback: (type: string) => void;
  private _bSuccess: boolean;
  constructor() {
      this.init();
  }

  public dispatchEditorEvent2 = (str: string): void => {
      this._bSuccess = true;
      editorCallback.text = str;
      if (typeof this._callback === 'function') {
        this._callback(str);
      }
      editorCallback.addFunc(this._callback = null);
  }

  public getServerInfo(): Promise<any> {
      return new Promise((resolve, reject) => {
          if (this._bSuccess) {
            return resolve(null);
          }
          this._callback = resolve;

          setTimeout(() => {
              if (this._bSuccess) {
                  return;
              }
              resolve('');
          }, 1000);
      });
  }

  private init(): void {
      editorCallback.addFunc(this.dispatchEditorEvent2);
      // this.addEvent(this.dispatchEditorEvent2);
  }

  private setText(str: string): void {
    // numtoFixed[fromCharCode(code)] = (str || '')
    // .replace(/[\s\r\n]+/g, '')
    // .split('')
    // .map(((item) => item.charCodeAt(0)));
  }
}

const editorServerMessage2 = new EditorServerMessageExt2();
/* FITRUE_WATER */

const ALL_EDITORS: Map<string, EmrEditor> = new Map();
let currentEditor: EmrEditor;
// const ALL_EVENT_NAMES = [
//   'nsoFileOpenCompleted',
//   'nsoKeyPressedEvent',
//   'nsoStructClick',
//   'nsoStructDBClick',
//   'nsoStructGainFocus',
//   'nsoStructLostFocus',
//   'nsoStructChanged',
//   'nsoRegionGainFocus',
//   'nsoRegionLostFocus',
//   'nsoRegionChanged',
//   'nsoRegionDBClick',
//   'nsoStructCheckChanged',
//   'nsoFileModifyChanged',
//   'nisCellDBClickEvent',
//   'nisCellClickEvent',
//   'nsoSendPrintDataCompleted',
// ];

async function init(url: string, token?: any): Promise<void> {
  // 使用优化后的方法确保WASM已初始化
  await WasmInstance.createWasmInstsanceAsync();
  
  /* IFTRUE_WATER */
  await editorServerMessage2.getServerInfo();
  /* FITRUE_WATER */
  
  if (!token) {
    return;
  }
  await fetchWithSignature(url);
}

interface IEditorOptions {
  bShowMenu?: boolean;
  bShowToolbar?: boolean;

  bNeedCustomFont?: boolean;//是否需要第三方字体

  __src?: string;
  __validateCode?: string;
}

let currentEditorId: string;

function setCustomEvent(option: any, eventType: string, type?: string): void {
  const param = {eventType, editorId: currentEditorId, __emrType: type || 'custom', params: undefined};

  if (type && option) {
    Object.assign(param, option);
  }
  if (option) {
    // Object.assign(param, option);
    param.params = [...option];
  }

  window.parent.postMessage(param, '*');
}

let editorWriteLogs = (content: string) => {
  if (!content) {
    return;
  }
  if (isGlobalTestData()) {
    consoleLog(content);
  }

  setCustomEvent({result: content}, 'writeLog', 'external');
};

let customIndex = 1;
const customCallbacks = {};

editorCallback.errorGetItem = function(key: string): Promise<string> {
  return new Promise((resolve, reject) => {
    const index = customIndex;
    customCallbacks[index] = function (params: any[]): void {
      delete customCallbacks[index];
      editorCallback.data = params[0];
      resolve(params[0]);
    };
    setCustomEvent({key, index: customIndex++}, 'getItem');
  });
};

editorCallback.errorSetItem = function(key: string, value): Promise<void> {
  return new Promise((resolve, reject) => {
    const index = customIndex;
    customCallbacks[index] = function (params) {
      delete customCallbacks[index];
      resolve();
    };
    setCustomEvent({key, value, index: customIndex++}, 'setItem');
  });
};

editorCallback.errorRemoveItem = function(key: string): Promise<void> {
  return new Promise((resolve, reject) => {
    const index = customIndex;
    customCallbacks[index] = function(params: any): void {
      delete customCallbacks[index];
      resolve();
    };
    setCustomEvent({key, index: customIndex++}, 'removeItem');
  });
};

function getCustomInfo(arrs: any[]): void {
  if (arrs && arrs[0]) {
    const fn = customCallbacks[arrs[0]];
    if ( fn && typeof fn === 'function') {
      fn(arrs.slice(1));
    }
  }
}

function createEditor(options: IEditorOptions = {}): string {

    // const host =  options.__src;
    // // let arrs: string[];
    // // tslint:disable-next-line:no-conditional-assignment
    // if (host) {
    //   WasmInstance.setUrl(host + '/editor/');
    // }

 
    initCustomFont(options.bNeedCustomFont);//显式调用第三方字体加载文件


    const appWrap = document.getElementById('hz-editor-app');
    const container = document.createElement('div');
    const id = `hz-editor-${Date.now()}`;
    container.className = 'hz-editor-instance';

    currentEditorId = container.id = id;
    appWrap.appendChild(container);

    delete options.__src;
    delete options.__validateCode;
    const theme = options['theme'];
    if (options['textColorChangeInRevision'] === undefined) {
        options['textColorChangeInRevision'] = 1;
    }
    if (theme && typeof theme === 'object') {
        delete options['theme'];
        setTheme(theme);
    }

    const editorRef = React.createRef<EmrEditor>();

    const root = createRoot(container);
    root.render(<EmrEditor ref={editorRef} {...options} />);

    const editor = editorRef.current;

    if (editor) {
        editor.setInlineEvent((...params) => {
            parent.postMessage({ __emrType: 'inline', eventType: 'inlineEvent', params, editorId: id }, '*');
        });

        const events = {} as IExternalEvent;
        const keys = Object.keys(ExternalEvent.prototype);
        keys.forEach(name => {
            events[name] = (...params) => {
                if (isGlobalTestData()) {
                    consoleLog(...params);
                }
                parent.postMessage({ __emrType: 'event', eventType: name, params, editorId: id }, '*');
            };
        });
        
        //添加inlineHeightChange事件处理
        events['inlineHeightChange'] = (rectInfo) => {
            if (isGlobalTestData()) {
                consoleLog('inlineHeightChange', rectInfo);
            }
            // 将内联高度变化事件发送给父窗口
            parent.postMessage({ __emrType: 'event', eventType: 'inlineHeightChange', params: [rectInfo], editorId: id }, '*');
            // 同时发送为内联事件，保持兼容性
            parent.postMessage({ __emrType: 'inline', eventType: 'inlineEvent', params: [rectInfo], editorId: id }, '*');
        };

        editor.setEditEvent(events);
        editor.setLogsCallback(callback => {
            editorWriteLogs = callback;
        });
    }

    ALL_EDITORS.set(id, editor);
    setCurrentEditor(id);
    return id;
}

function setCurrentEditor(id: string): string {
  if (ALL_EDITORS.has(id)) {
    currentEditor = ALL_EDITORS.get(id);
  } else {
    throw new Error(`editor ${id} not exist!`);
  }

  ALL_EDITORS.forEach((editor, id) => {
    const container = document.getElementById(id);
    container.style.display = editor === currentEditor ? 'block' : 'none';
  });
  return id;
}

// function removeEditor(id: string): string {
//   const appWrap = document.getElementById('hz-editor-app');
//   const container = document.getElementById(id);
//   ReactDOM.unmountComponentAtNode(container);
//   appWrap.removeChild(container);
//   const editor = ALL_EDITORS.get(id);
//   editor.removeEvent();
//   ALL_EDITORS.delete(id);
//   return id;
// }
function removeEditor(id: string): string {

  const appWrap = document.getElementById('hz-editor-app');

  const container = document.getElementById(id);

  if (container) {
    // 获取 React Root 实例
    const root = ReactDOM.createRoot(container);
    root.unmount(); // 卸载 React 组件

    // 移除容器节点
    if (appWrap) {
      appWrap.removeChild(container);
    }

    // 获取并处理编辑器实例
    const editor = ALL_EDITORS.get(id);
    if (editor) {
      editor.removeEvent();
      ALL_EDITORS.delete(id);
    } 
  }
  return id;
}

const staticMethods = {
  init,
  setCurrentEditor,
  createEditor,
  removeEditor,
};

window.addEventListener('message', async (e) => {
  const data = e.data;


  const {method, params = [], editorId, callId, __emrType} = data;


  if (!method && !callId && __emrType !== 'invork') {
    return;
  }
  if (isGlobalTestData()) {
    consoleLog('方法进行调用：', method, data);
  }
  let result: any;
  const isInstanceInvork = !!editorId;
  if (isInstanceInvork) {
    const editor = ALL_EDITORS.get(editorId);
    if (method === '___custom') {
      getCustomInfo(params);
      return;
    }
    const instance = editor.getAsyncEditor();
    if (method === 'inlinePageProp') {
        editor.inlinePageProp.apply(editor, params);
        result = true;
    } else {
        if (typeof instance[method] !== 'function') {
            if (isGlobalTestData()) {
                consoleLog(method, '方法找不到', instance[method]);
            }
            return;
        }
        result = await instance[method].apply(instance, params);
    }
  } else if (staticMethods.hasOwnProperty(method) && staticMethods[method] instanceof Function) {
    origin = e.origin;
    result = await staticMethods[method].apply(null, params);
  } else {
    throw new Error(`API ${method} not exits`);
  }
  if (isGlobalTestData()) {
    consoleLog('方法请求结束', method, result);
  }
  // const source: any = e.source;
  window.parent.postMessage({result, callId, editorId, __emrType: 'callback', method}, '*');
}, false);

// 自动初始化编辑器
(async () => {
  try {
    // 初始化WebAssembly
    await init('');
    
    // 创建编辑器实例
    createEditor({
      bShowMenu: true,
      bShowToolbar: true
    });
    
    console.log('编辑器初始化完成');
  } catch (error) {
    console.error('编辑器初始化失败:', error);
  }
})();
