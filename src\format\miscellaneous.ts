import { ICustomProps, DataType, rtNode } from '../common/commonDefines';
import { safeDecodeURIComponent } from '../common/commonMethods';

export function readCustomProperties(customProperties: any): ICustomProps[] {
  const customPropertyArr = [];
  for (const customPropertyItem of customProperties) {
    const properties: ICustomProps = {
      type: DataType.String,
      name: undefined,
      value: undefined, // xml.js Line 184-185, if 'null' will break zip compiling/xmlifying
    };

    const name = customPropertyItem.nodeName;
    if (name) { // must be string, ok
      properties.name = name;
    }

    // tslint:disable-next-line: max-line-length
    const value = customPropertyItem.childNodes[0] ? customPropertyItem.childNodes[0].nodeValue : null;
    if (value) {
      properties.value = value;
    }

    // tslint:disable-next-line: max-line-length
    const type = customPropertyItem.attributes.getNamedItem('type') ? +customPropertyItem.attributes.getNamedItem('type').nodeValue : null;
    if (type) {
      properties.type = type;
    }

    // console.log(properties)
    // customPropertyMap.set(name, properties);
    customPropertyArr.push(properties);
  }
  return customPropertyArr;
}

export function tReadCustomProperties(customProperties: (rtNode | string | number)[],
                                      documentVersion?: number): ICustomProps[] {
  const customPropertyArr = [];
  for (const customPropertyItem of customProperties) {
    const properties: ICustomProps = {
      type: DataType.String,
      name: undefined,
      // value: undefined, // xml.js Line 184-185, if 'null' will break zip compiling/xmlifying
      value: '',
    };

    if ( typeof customPropertyItem === 'object') {
      const name = customPropertyItem.tagName;
      if (name) { // must be string, ok
        properties.name = safeDecodeURIComponent(name, documentVersion);
      }

      // tslint:disable-next-line: max-line-length
      const value = customPropertyItem.children[0] ? customPropertyItem.children[0] : null;
      if (value && typeof value === 'string') {
        const val = safeDecodeURIComponent(value, documentVersion);

        // // may cause 'URIError: malformed URI sequence'
        // try {
        //   val = decodeURIComponent(value);
        // } catch (error) {
        //   // tslint:disable-next-line: no-console
        //   console.log(error);
        //   val = value;
        // }
        properties.value = val;
      }

      // tslint:disable-next-line: max-line-length
      const type = customPropertyItem.attributes['type'] ? +customPropertyItem.attributes['type'] : null;
      if (type) {
        properties.type = type;
        if (type === DataType.String && properties.value) {
          properties.value = properties.value.replace(/&quot;/g, '"');
        }
      }

      // console.log(properties)
      // customPropertyMap.set(name, properties);
      customPropertyArr.push(properties);
    }

  }
  return customPropertyArr;
}
