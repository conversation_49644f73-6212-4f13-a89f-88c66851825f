@import './global.less';
.hz-editor-container {
    .editor-equation {
        padding: 20px 0;
        font-size: @fontSize;
        font-family: @fontFamily;
        color: @color;
        text-align: center;

        & > div {
            width: 150px;
            margin: 0 auto;
            line-height: 30px;
            margin-bottom: 10px;
        }
    }

    .menstruation4 {
        display: flex;
        position: relative;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        align-items: center;
        

        & > div:first-child {
            flex: 1;
            width: 40%;
            padding-left: 70px;

            .line {
                position: absolute;
                left: 48%;
                top: 45%;
                height: 36px;
                width: 1px;
                background: #000 1px;
                transform: rotate(45deg);
            }
        }

        & > div:last-child {
            flex: 1;
            .line {
                width: 80px;
                height: 2px;
                margin: 6px 0;
                background: #000 1px;
            }
        }
    }

    .menstruation3 {
        position: relative;
        width: 100%;
        height: 200px;
        background: url(../images/xbg.png) center no-repeat;
        & > div {
            position: absolute;
            width: 100px;
        }
        .text1 {
            font-size: 12px;
            left: 165px;
            top: 0px;
        }
        .text2 {
            font-size: 12px;
            right: 45px;
            top: 65px;
        }
        .text3 {
            font-size: 12px;
            left: 165px;
            top: 140px;
        }
        .text4 {
            font-size: 12px;
            left: 60px;
            top: 65px;
        }
        .value1 {
            left: 160px;
            top: 20px;
        }
        .value2 {
            right: 65px;
            top: 85px;
        }

        .value3 {
            left: 160px;
            top: 160px;
        }
        .value4 {
            left: 60px;
            top: 85px;
        }
        
    }

    .menstruation2-map {
        position: relative;
        margin: 0 80px;
        .w-050:nth-of-type(1), .w-050:nth-of-type(3){
            .editor-input {
                padding-right: 8px;
            }
        }
        .w-050:nth-of-type(2), .w-050:nth-of-type(4){
            .editor-input {
                padding-left: 8px;
            }
        }

        .menstruation2-line {
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;

            & > span:first-child {
                position: absolute;
                left: 0;
                top: 50%;
                height: 1px;
                width: 100%;

                background: #000 1px;
            }

            & > span:last-child {
                position: absolute;
                left: 50%;
                top: 0;
                height: 100%;
                width: 1px;

                background: #000 1px;
            }
        }
    }

    .tooth-bit-map {
        position: relative;
        .w-33-10 {
            display: inline-block;
            width: 33%;
            padding: 10px 20px;
        }
        .line-h {
            position: absolute;
            left: 0;
            bottom: 50%;
            width: 100%;
            height: 1px;
            background-color: #000;
        }
        .line-v-1, .line-v-2 {
            position: absolute;
            left: 33%;
            top: 0;
            width: 1px;
            height: 100%;
            background-color: #000;
        }

        .line-v-2 {
            left: 66%;
        }

        .w-50 {
            display: inline-block;
            width: 50%;
            padding: 10px 20px;
        }

        .w-label {
            display: inline-block;
            width: 18px;
        }
        .w-input {
            display: inline-block;
            width: calc(100% - 22px);
        }

        .line-v-3 {
            position: absolute;
            left: 50%;
            top: 0;
            width: 1px;
            height: 100%;
            background-color: #000;
        }

        .line-h-2, .line-h-3, .line-h-4 {
            position: absolute;
            top: 49%;
            width: 0;
            height: 1px;
            background-color: #000;
        }

        .line-h-2 {
            left: 5px;
            width: 8px;
        }

        .line-h-3 {
            left: 44%;
            width: 34px;
        }

        .line-h-4 {
            right: 10px;
            width: 8px;
        }
    }

    .editor-edit-equation {
        .left-item {
            padding-right: 10px;
            border-right: 1px solid;
            padding-left: 20px;
        }

        .left-item.top-item {
            border-bottom: 1px solid;
        }

        .left-item.bottom-item {
            border-top: 1px solid;
        }

        .right-item {
            padding-left: 30px;
            border-left: 1px solid;
        }

        .right-item.top-item {
            border-bottom: 1px solid;
        }

        .right-item.bottom-item {
            border-top: 1px solid;
        }

        .top-item {
            padding-bottom: 10px;
        }

        .bottom-item {
            padding-top: 10px;
        }

        input {
            width: 120px;
        }

        .menstruation-date {
            width: 140px;
        }

        .hz-editor-menu .input-window {
            height: 400px;
            overflow-y: scroll;
        }

        .menstruation-checkbox {
            width: 15px;
            margin-bottom: 30px;
        }

        .menstruation-text,
        .menstruation-date {
            margin-bottom: 10px;
            display: block;
        }

        .menstruation-upper-block,
        .menstruation-bottom-block {
            position: relative;
            top: 10px;
            padding-left: 5px;
        }

        .menstruation-upper-block {
            border-bottom: 1px solid;
            padding-bottom: 5px;
        }

        .menstruation-block div {
            padding-left: 10px;
        }

        .menstruation-bottom-block {
            padding-top: 10px;
        }

        .button-area {
            position: relative;
            height: 160px;
            margin-top: 30px;
        }

        .input-area .del-button {
            margin-left: 20px;
        }

        .config-format-container,
        .config-spacing-container {
            width: 50%;
            float: left;
        }

        .button-container {
            position: absolute;
            bottom: 20px;
            right: 20px;
        }

        .button-container .button {
            margin-right: 20px;
            font-size: 13px;
        }

        .detail-block {
            margin: 10px;
        }

        .fraction-block {
            text-align: center;
            padding: 20px 0;
            margin: 0;
        }

        .fraction-block:nth-child(2) {
            border-bottom: 1px solid;
        }

        .menstruation-block {
            width: 33%;
            height: 200px;
            display: inline-block;
        }

        .menstruation-block:nth-child(4) {
            box-sizing: border-box;
            padding-left: 10px;
        }

        .equation-detail-block:nth-child(2) {
            margin-bottom: 0;
        }

        .equation-detail-block:nth-child(3) {
            margin-top: 0;
        }

        .image-detail-block {
            margin-top: 20px;
        }

        .detail-block .detail-item {
            width: 50%;
            display: inline-block;
        }

        .detail-block .detail-item {
            width: 46%;
            box-sizing: border-box;
        }
    }

    .diseased-upper-teeth {
        position: relative;
        .value1 {
            display: inline-block;
            width: 50px;
            margin-top: 20px;
            margin-left: 92px;
            // padding: 10px 10px;
            text-align: center;
        }
        .value2 {
            display: inline-block;
            width: 50px;
            margin-top: 54px;
            margin-left: 50px;
            // padding: 10px 10px;
            text-align: center;
        }
        .value3 {
            display: inline-block;
            width: 50px;
            // margin-top: 100px;
            margin-left: 35px;
            // padding: 10px 10px;
        }
        .line-h {
            position: absolute;
            left: 0;
            // margin-top: 4px;
            margin-left: 71px;
            width: 90px;
            height: 2px;
            background-color: #000;
            transform: rotate(90deg);
        }
        .line-v-1 {
            position: absolute;
            left: 80px;
            top: 0;
            width: 2px;
            height: 100px;
            transform: rotate(-45deg);
            background-color: #000;
        }

        .line-v-2 {
            position: absolute;
            top: 0;
            width: 2px;
            height: 100px;
            // margin-top: 5px;
            // margin-left: 76px;
            background-color: #000;
            transform: rotate(45deg);
            left: 150px;
        }
    }

    .diseased-lower-teeth {
        position: relative;
        .value1 {
            display: inline-block;
            width: 80px;
            margin-top: 15px;
            margin-left: 10px;
            // padding: 10px 10px;
            text-align: center;
        }
        .value2 {
            display: inline-block;
            width: 80px;
            margin-top: 10px;
            margin-left: 130px;
            // padding: 10px 10px;
            text-align: center;
        }
        .value3 {
            display: inline-block;
            width: 80px;
            margin-top: 10px;
            margin-left: 130px;
            // padding: 10px 10px;
        }
        .line-h {
            position: absolute;
            left: 0;
            // margin-top: 4px;
            margin-left: 110px;
            width: 120px;
            height: 2px;
            background-color: #000;
        }
        .contain {
            display: inline-block;
            left: 0;
            // margin-top: 4px;
            margin-left: 110px;
            height: 5px;
        }
    }
}
