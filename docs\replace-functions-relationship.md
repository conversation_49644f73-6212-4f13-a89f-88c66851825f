# replaceSpecificStructPropWithBackStream 函数关系文档

## 核心函数概览

### 1. replaceSpecificStructPropWithBackStream (主入口)
**位置**: `src/common/external/OperateDocument.ts`
**功能**: 替换文档中指定结构的内容的主要接口
**调用者**: 
- `src/common/external/index.ts` 的 `replaceSpecificStructPropWithBackStream()`
- `src/common/external/index.ts` 的 `replaceSpecificRegionPropWithBackStream()`

**核心逻辑**:
```typescript
if (type === 2) {
    // Region 替换
    res = await this.replaceRegionWithTraditionalMethod(targets, sources, content, type);
} else {
    // NewControl 替换  
    res = await this.replaceNewControlWithHybridMethod(targets, sources, content, type);
}
```

### 2. replaceSpecificStructPropWithBackString (废弃包装)
**功能**: Base64字符串版本的包装函数，内部调用Stream版本
**状态**: 不重要，仅做兼容性包装

## 分支函数详解

### 3. replaceRegionWithTraditionalMethod (Region处理)
**功能**: 处理Region类型(type=2)的替换
**调用路径**: `replaceSpecificStructPropWithBackStream` → `replaceRegionWithTraditionalMethod`
**处理对象**: 区域(section)结构

### 4. replaceNewControlWithHybridMethod (NewControl处理)
**功能**: 处理NewControl类型(type=1)的替换，使用混合方法
**调用路径**: `replaceSpecificStructPropWithBackStream` → `replaceNewControlWithHybridMethod`
**处理对象**: 结构化元素(sdt)

### 5. tryNewControlReplaceMethod (NewControl优化尝试)
**功能**: NewControl的优化替换尝试方法
**调用路径**: `replaceNewControlWithHybridMethod` → `tryNewControlReplaceMethod`
**特点**: 可能是性能优化版本，失败时会回退到传统方法

## AI相关函数

### 6. replaceContentWithAICorrection (AI修正)
**功能**: 使用AI修正后的内容替换文档结构
**调用者**: `src/common/external/index.ts` 的 `replaceContentWithAICorrection()`
**使用场景**: AI文本校正功能
**特点**: 
- 使用 `getSpecificStructContentWithBackStreamAI` 获取内容
- 支持Region和NewControl两种类型

### 7. extractContentForAICorrection (AI内容提取)
**功能**: 为AI处理提取结构化文本内容
**调用者**: `src/common/external/index.ts` 的 `extractContentForAICorrection()`
**配套使用**: 与 `replaceContentWithAICorrection` 配套使用

## 优化版本函数

### 8. replaceSpecificStructPropWithBackStreamOptimized (优化版)
**功能**: 使用优化XML解析算法的替换版本
**状态**: 在 `src/common/external/index.ts` 中被注释掉，未启用
**特点**: 性能优化版本，但可能还在测试阶段

## 函数调用关系图

```
外部调用
├── replaceSpecificStructPropWithBackStream (主入口)
│   ├── type=2 → replaceRegionWithTraditionalMethod
│   └── type=1 → replaceNewControlWithHybridMethod
│                └── tryNewControlReplaceMethod (优化尝试)
│
├── replaceContentWithAICorrection (AI修正)
│   └── getSpecificStructContentWithBackStreamAI
│
├── extractContentForAICorrection (AI提取)
│   ├── saveRegionContentToStreamAI (Region)
│   └── saveStructContentToStreamAI (NewControl)
│
└── replaceSpecificStructPropWithBackStreamOptimized (优化版-未启用)
```

## 使用场景分类

1. **常规替换**: `replaceSpecificStructPropWithBackStream`
2. **AI文本校正**: `extractContentForAICorrection` + `replaceContentWithAICorrection`
3. **性能优化**: `replaceSpecificStructPropWithBackStreamOptimized` (未启用)

## 重要说明

- `type=1`: NewControl(结构化元素/sdt)
- `type=2`: Region(区域/section)  
- AI相关函数是独立的处理流程，不依赖主替换流程
- 优化版本目前未启用，可能还在开发测试中