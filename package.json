{"name": "hz-editor", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"version": "node gen-version.js", "start": "webpack-dev-server", "start:water": "webpack serve --config webpack.config.water.js", "prepack:watersite": "node gen-version.js", "pack:watersite": "webpack --config webpack.config.watersite.prod.js", "prepack:watersite:debug": "node gen-version.js", "pack:watersite:debug": "webpack --config webpack.config.watersite.debug.js", "postpack:watersite:debug": "node hz-debug.js", "debug:static": "node hz-debug.js", "analyze": "cross-env ANALYZE=true npm run pack:watersite", "dev:site": "webpack serve --config webpack.config.site.js", "build:sdk": "webpack --config webpack.config.sdk.js", "prepack:watersite:minifile": "node gen-version.js", "pack:watersite:minifile": "webpack --config webpack.config.watersite.minifile.prod.js"}, "keywords": [], "author": "", "private": true, "license": "ISC", "devDependencies": {"@babel/runtime": "^7.12.5", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@types/fabric": "^5.3.8", "@types/jasmine": "^5.1.4", "@types/jquery": "^3.5.30", "@types/opentype.js": "^1.3.8", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/yargs": "^17.0.32", "apidoc": "^1.2.0", "clean-webpack-plugin": "^4.0.0", "compression-webpack-plugin": "^11.1.0", "copy-webpack-plugin": "^12.0.2", "cross-env": "^7.0.2", "css-loader": "^3.4.2", "css-minimizer-webpack-plugin": "^6.0.0", "file-loader": "^6.2.0", "file-saver": "^2.0.2", "flatted": "^2.0.1", "gulp": "^5.0.0", "gulp-base64": "^0.1.3", "gulp-concat": "^2.6.1", "gulp-csso": "^4.0.1", "gulp-less": "^5.0.0", "html-webpack-plugin": "^5.6.0", "increase-memory-limit": "^1.0.7", "jasmine-core": "^3.2.1", "js-conditional-compile-loader": "^1.0.15", "karma": "^6.4.3", "karma-chrome-launcher": "^3.2.0", "karma-jasmine": "^5.1.0", "karma-sourcemap-loader": "^0.4.0", "karma-webpack": "^5.0.1", "less": "^4.2.0", "less-loader": "^12.2.0", "memoize-one": "^5.1.1", "mini-css-extract-plugin": "^2.9.0", "perf_hooks": "0.0.1", "qrcode": "^1.5.3", "style-loader": "^4.0.0", "svg-url-loader": "^7.1.1", "terser-webpack-plugin": "^5.3.11", "ts-loader": "^9.5.0", "tsconfig-paths-webpack-plugin": "^4.1.0", "tslint": "^6.1.3", "typescript": "^5.4.5", "url-loader": "^4.1.0", "webpack": "^5.91.0", "webpack-bundle-analyzer": "^4.10.2", "webpack-cli": "^5.1.4", "webpack-dev-server": "^5.0.0", "webpack-obfuscator": "^3.5.1", "xml-js": "^1.6.11", "prop-types": "^15.8.1"}, "dependencies": {"@types/css-font-loading-module": "0.0.7", "@types/react-dom": "^18.3.0", "@uiw/react-color": "^1.4.3", "cross-env": "^7.0.2", "docx": "^6.0.3", "exceljs": "^4.3.0", "fabric": "^5.3.0", "hz-editor": "file:", "hz-editor-sdk-new": "^1.1.240304", "jquery": "^3.4.1", "js-md5": "^0.7.3", "jsbarcode": "^3.11.5", "opentype.js": "^1.3.4", "react": "18.3.1", "react-dom": "18.3.1", "react-dnd-html5-backend": "^16.0.1", "stream-browserify": "^3.0.0", "ts-indexdb": "^0.0.6", "txml": "^4.0.0", "typo-js": "^1.2.2", "rc-notification": "5.1.1"}}