import { ParagraphContentPos } from "../Paragraph/ParagraphContent";
import ParaPortion from "../Paragraph/ParaPortion";
import TextProperty, { TextDecorationLineType } from "../TextProperty";
import { idCounter } from "../util";

export enum WavyUnderlineType {
    SpellCheck = 0,    // 拼写检查错误
    Grammar = 1,       // 语法错误  
    Suggestion = 2,    // 建议修改
    Warning = 3,       // 警告信息
    Custom = 4,        // 自定义类型
}

export const WavyUnderlineTypeConfig = {
    [WavyUnderlineType.SpellCheck]: {
        color: '#ff0000',
        defaultContent: '可能的拼写错误'
    },
    [WavyUnderlineType.Grammar]: {
        color: '#00ff00', 
        defaultContent: '语法检查建议'
    },
    [WavyUnderlineType.Suggestion]: {
        color: '#0000ff',
        defaultContent: '修改建议'
    },
    [WavyUnderlineType.Warning]: {
        color: '#ff8800',
        defaultContent: '注意事项'
    },
    [WavyUnderlineType.Custom]: {
        color: '#8800ff',
        defaultContent: '自定义提示'
    }
};

export class WavyUnderline {
    private id: number;                    // 唯一标识
    private name: string;                  // 内部名称
    private content: string;               // 提示内容
    private type: WavyUnderlineType;       // 波浪线类型
    private startPortion: ParaPortion;     // 起始段落片段
    private endPortion: ParaPortion;       // 结束段落片段
    private startPos: ParagraphContentPos; // 起始位置
    private endPos: ParagraphContentPos;   // 结束位置
    private bActive: boolean;              // 激活状态
    private logicDocument: Document;       // 文档引用

    private detectionStartPos: ParagraphContentPos;
    private detectionEndPos: ParagraphContentPos;
    
    constructor() {
        this.id = idCounter.getNewId();
        this.bActive = false;
        this.content = '';
        this.type = WavyUnderlineType.SpellCheck;
    }
    
    // 基础属性访问器
    public getId(): number { return this.id; }
    public getName(): string { return this.name; }
    public setName(name: string): void { this.name = name; }
    
    // 内容管理
    public getContent(): string { return this.content; }
    public setContent(content: string): boolean {
        if (content !== this.content) {
            this.content = content;
            return true;
        }
        return false;
    }
    
    // 类型管理
    public getType(): WavyUnderlineType { return this.type; }
    public setType(type: WavyUnderlineType): void { this.type = type; }
    
    // 位置管理
    public getStartPos(): ParagraphContentPos { return this.startPos; }
    public setStartPos(pos: ParagraphContentPos): void { this.startPos = pos; }
    public getEndPos(): ParagraphContentPos { return this.endPos; }
    public setEndPos(pos: ParagraphContentPos): void { this.endPos = pos; }
    
    // 段落片段管理
    public getStartPortion(): ParaPortion { return this.startPortion; }
    public setStartPortion(portion: ParaPortion): void { this.startPortion = portion; }
    public getEndPortion(): ParaPortion { return this.endPortion; }
    public setEndPortion(portion: ParaPortion): void { this.endPortion = portion; }
    
    // 激活状态管理
    public setActive(bActive: boolean): void { this.bActive = bActive; }
    public isActive(): boolean { return this.bActive; }
    
    // 文档引用
    public setLogicDocument(doc: Document): void { this.logicDocument = doc; }
    public getLogicDocument(): Document { return this.logicDocument; }
    
    // 工具方法
    public isEmpty(): boolean {
        return !this.startPortion || !this.endPortion;
    }

    public setDetectionStartPos(pos: ParagraphContentPos): void {
        this.detectionStartPos = pos;
    }
    
    public setDetectionEndPos(pos: ParagraphContentPos): void {
        this.detectionEndPos = pos;
    }
    
    public getDetectionStartPos(): ParagraphContentPos {
        return this.detectionStartPos;
    }
    
    public getDetectionEndPos(): ParagraphContentPos {
        return this.detectionEndPos;
    }
    
    public isInRange(pos: ParagraphContentPos): boolean {
        return this.startPos.compare(pos) <= 0 && this.endPos.compare(pos) >= 0;
    }
}