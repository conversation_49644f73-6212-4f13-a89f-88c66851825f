// import { IStructJson, IStructContent } from '../commonDefines';

import { ICustomToolbarItem, IEventPosition } from '../commonDefines';
import { NISTableExternalInterface } from './NISTableExternalInterface';

// import { IStructJson, IStructContent } from "../commonDefines";

export default interface IExternalInterface {
    // NIS?: NISTableExternalInterface;
    CTT?: NISTableExternalInterface;

    /**
     * 新建一片文档  初始文档为A4 ，页边距均为默认值，无页眉页脚
     */
    createNew(): number;

    /**
     * 打开病历
     * @param path 可以为url 路径，也可以为base64的串值。
     * @param mode 2 为可编辑。 0 – 为 只读
     */
    openDocumentWithString(path: string, mode: string): Promise<number>;

    /**
     * 打开病历
     * @param content blob
     * @param mode 2 为可编辑。 0 – 为 只读
     */
    openDocumentWithStream(content: Blob, mode: string): Promise<number>;

    /**
     * 保存病历
     * @return base64编码的文档内容
     */
    saveToString(): Promise<string>;

    /**
     * 保存为docx
     */
    saveToDocx(): Promise<Blob>;

    /**
     * 保存病历
     * @return Blob
     */
    saveToStream(sModJson: string): Promise<Blob>;

    /** 保存指定结构为Blob数据 */
    saveStructContentToStream(name: string): Promise<Blob>;

    /**
     * 保存指定结构为字符流。
     * @param sNameJson 名称集合
     */
    saveStructContentToStreamByArray(sNameJson: string): Promise<Map<string, Blob>>;

    /**
     * 保存选中区域的内容为一个子文档（base64串值）
     * @return Base64bia编码的文档内容
     */
    saveSelectAreaToString(): Promise<string>;

    /**
     * 保存选中区域的内容为一个子文档（blob）
     * @return blob
     */
    saveSelectAreaToStream(sModJson: string): Promise<Blob>;

    /**
     * 设置服务端url
     * @param url 服务端url
     */
    setRemoteUrl(url: string): number;

      /**
     * 设置常规的http服务端url
     * @param url 服务端url
     */
      setHttpSvrUrl(url: string): Promise<number>;

     /**
     * 设置ofd的http服务端url
     * @param url 服务端url
     */
      setOfdSvrUrl(url: string): Promise<number>;


    /**
     * 把当前打开的文件,导出成PDF或HTML文件。
     * @param nFormatType 导出HTML或PDF文件的类型
     */
    exportToOtherFormatWithStream(nFormatType: number): Promise<Blob>;

    /**

     * 把当前打开的文件,导出成PDF或HTML文件
     * @param type 导出HTML或PDF文件的类型
     */
    exportToOtherFormat(type?: number): Promise<string>;

    /**
     * 销毁编辑器对象。
     * 调用该接口后，editor对象会被销毁。可以释放资源。
     */
    close(): void;

    /**
     * 关闭当前打开的病历。
     * 讨论点：关闭后病历显示空白，回收部分资源。
     */
    closeDoc(bRefresh?: boolean): void;

    /**
     * 开启日志模式
     * @param flag 是否开启日志模式： true: 开启；false: 关闭；
     */
    setDebugMode(flag: boolean): void;

    /**
     * 切换病历的编辑模式
     * @param nType 1 正常模式 2 只读模式 3 严格模式
     */
    setEditMode(nType: number): number;

    updateEditor(bAll?: boolean): void;

    /**
     * 切换病历的视图模式
     * @param nType 1 分页模式 2 Web模式
     */
    setViewMode(nType: number): number;

    /**
     * 开启管理员模式
     * @param bFlag 是否开启管理员模式： true: 开启；false: 关闭
     */
    enableAdministratorMode(bFlag: boolean): number;

    /**
     * 控制菜单是否显示
     * @param bVisible 显示菜单控制标志
     */
    setMenuBarVisible(bVisible: boolean): boolean;

    /**
     * 显示和隐藏指定的菜单项
     * @param sJson 右键菜单设置项集合
     */
    disableMenuItem(sJson: string): number;

    /**
     * 只读模式下控制某些视图属性
     * @param sJson 文档处于只读模式下，设置视图属性
     */
    setViewPropInReadonlyMode(sJson: string): number;

    setTextWaterMark(sText: string, nMode: number, colorType?: number): number;

    deleteTextWaterMark(): number;

    hasTextWaterMark(): boolean;

    /**
     * 抽取当前文档的四个验证json
     */
    generateAutoTestFiles(): Promise<string[]>;

    /**
     * 通过比较四个验证json来比较文档一致性
     * @param autoFiles1 四个验证json: html, structs, image, table
     * @param autoFiles2 四个验证json: html, structs, image, table
     */
    compareAutoTestFiles(autoFiles1: string[], autoFiles2: string[]): string | number;

    /**
     * 文档显示比例设置
     * @param nType 显示比例类型
     * @param nValue 显示比例的值
     */
    setViewProportion(nType: number, nValue?: number): number;

    /**
     * 获取文档显示比例
     */
     getViewProportion(): number;

    /**
     * 控制工具栏是否显示
     * @param bVisible 显示工具栏控制标志
     * @param toolBarName 工具栏名称：暂时没用
     */
    setSpecificToolBarVisible(bVisible: boolean, toolBarName?: string): boolean;

    /**

     * 关闭打印预览
     * 假设处于打印预览状态，则结束，如果不处于，则无响应
     */
    closePrintPreview(): void;

    /**
     * 打印文档
     * @param flag bool  控制开关 控制是否弹窗。 True – 弹框(默认值)   false – 不弹（此处需要商量）
     */
    printDoc(flag?: boolean): void;

    /**

     * 直接打印
     * @param sPrintJson 打印配置参数
     */
    directPrintDoc(sPrintJson: string): Promise<number>;

    /**
    * 继续打印
     *
     * @param sPrintJson
     */
    continuePrintDoc(sPrintJson: string): Promise<number>;

    /**
     * 门诊打印
     * @param sPrintName 打印机名称
     * @param printMode 1 – Web 打印   2 – C端打印(现在只支持1打印)
     * @param pageType 纸张模式：1 -上页打印 2-下页打印
     * @param firstPageDistance 首页留白距离 mm
     * @param pageMidDistance 门诊病历上页跟下页的留白距离 单位mm
     * @param lastPageDistance 首页留白距离 mm
     * @param pageWidth 门诊病历宽，单位mm
     * @param pageHeight 门诊病历高，单位mm
     */
    printOutpatientDoc(sPrintName: string, printMode: number, pageType: number, firstPageDistance: number,
                       pageMidDistance?: number, lastPageDistance?: number, pageWidth?: number, pageHeight?: number)
                       : Promise<boolean>;

    /**
     * 设置当前选中文本的Bold, Italic, UnderLine 属性。
     * @param propName 属性名(Bold, Italic, UnderLine)
     * @param val 属性值
     */
    setFontProp(propName: string, val: number | string): number;


        /**
     * 获取当前选中文本的Bold, Italic, UnderLine 属性。
     */
        getFontProp(): string;


    /**
     * 获得当前文档的页数
     */
    getPageCount(): number;

      /**
   * @api {function} none enableTrackRevisions
   * @apiName enableTrackRevisions
   * @apiGroup 85_revisionAPI
   * @apiVersion  1.0.0
   * @apiDescription enableTrackRevisions(flag: boolean): number
   * <p>开启或关闭修订功能</p>
   * @apiparam {boolean} bFlag true:开启 false:关闭
   * @apiSuccessExample {javascript} Success:
   *     let number = editor.enableTrackRevisions(true);
    */
    enableTrackRevisions(flag: boolean): number;

    /**
   * @api {function} none getTrackRevisions
   * @apiName getTrackRevisions
   * @apiGroup 85_revisionAPI
   * @apiVersion  1.0.0
   * @apiDescription getTrackRevisions(): boolean
   * <p>获取开启关闭修订状态</p>
   * @apiSuccessExample {javascript} Success:
   *     if( editor.getTrackRevisions()) //true:开启 false:关闭
   *     { editor.showRecension(2);
   * }
    */
    getTrackRevisions(): boolean;

    /**
   * @api {function} none showRecension
   * @apiName showRecension
   * @apiGroup 85_revisionAPI
   * @apiVersion  1.0.0
   * @apiDescription showRecension(flag: number): number
   * <p>控制显示修订的状态</p>
   * @apiparam {number} flag 1：显示修订状态 2：显示最终状态
   * @apiSuccessExample {javascript} Success:
   *     let nresult = editor.showRecension(2);
    */
    showRecension(flag: number): number;


    /**
   * @api {function} none showRevisionPanel
   * @apiName showRevisionPanel
   * @apiGroup 85_revisionAPI
   * @apiVersion  1.0.0
   * @apiDescription showRevisionPanel(flag: boolean): void
   * <p>控制修订面板的显示</p>
   * @apiparam {boolean} flag true：显示 false：隐藏
   * @apiSuccessExample {javascript} Success:
   *   editor.showRevisionPanel(true);
    */
    showRevisionPanel(flag: boolean): void;

    /**
     * 获取修订痕迹数量
     * @returns 修订痕迹数量
     */
    getRevisionCount(): {
        total: number;           // 总修订数
        additions: number;       // 新增修订数
        deletions: number;       // 删除修订数
        users: string[];         // 参与修订的用户列表
    };

     /**
   * @api {function} none setRecensionInfo
   * @apiName setRecensionInfo
   * @apiGroup 85_revisionAPI
   * @apiVersion  1.0.0
   * @apiDescription setRecensionInfo(strUserCode: string, strUserName: string, strMemo: string,
   *                  nMarkStyle: number, strMarkColor: string): number
   * <p>当开启修订功能时，设置修订信息</p>
    * @apiparam {string} strUserCode 修订者工号
    * @apiparam {string} strUserName 修订者姓名(必须保证姓名的唯一性,否则相同名字的人的修订会一样)
    * @apiparam {string} strMemo 备注
    * @apiparam {number} nMarkStyle 修订痕迹风格(1:单删除线、单下划线 2:双删除线、双下划线 3:三删除线、三下划线)
    * @apiparam {string} strMarkColor 修订颜色(可取以下颜色：红色,蓝色,绿色)
   * @apiSuccessExample {javascript} Success:
   *  let nresult = editor.setRecensionInfo("1101","医生A","骨科",1,"红色");
    */
    setRecensionInfo(strUserCode: string, strUserName: string, strMemo: string,
                     nMarkStyle: number, strMarkColor: string): number;

    /**
   * @api {function} none getRecensionShowState
   * @apiName getRecensionShowState
   * @apiGroup 85_revisionAPI
   * @apiVersion  1.0.0
   * @apiDescription getRecensionShowState(): number
   * <p>获取当前文档修订痕迹的状态</p>
   * @apiSuccessExample {javascript} Success:
   *   let nresult = editor.getRecensionShowState();// -1 失败； 1 显示修订痕迹状态； 2  最终状态
    */
    getRecensionShowState(): number;

    /**
     * 设置当前段落的 FirstLineIndent, Alignment 属性
     * @param propName FirstLineIndent 首行缩进 LeftIndent 左缩进 HangingIndent 悬挂缩进 Alignment 对齐
     * @param nVal “FirstLineIndent”： 1/1000 cm；"Alignment" ：  0 表示左对齐 1 表示右对齐 2 表示两端对齐 3 表示居中对齐
     */
    setParagraphProp(propName: string, nVal: number): number;

    /**
     * 调整选中段落的行间距为特定行距
     * @param nType 0 – 设置整倍数的行距 3 – 固定值
     * @param nHeight 当nType 不同的时候该值取值不一样 nType =0 100 表示1倍行距 200 表示2倍行距
     * nType = 3的时候，该值表示固定高度，单位1/1000cm (比如50 表示0.05cm)
     */
    setParaLineSpace(nType: number, nHeight: number): number;

       /**
   * @api {function} none isCurrentLineEmpty
   * @apiName isCurrentLineEmpty
   * @apiGroup 7_insertAPI
   * @apiVersion  1.0.0
   * @apiDescription isCurrentLineEmpty(): boolean
   * <p>合并文档 base64流</p>
   * @apiSuccessExample {boolean} Success:
   *     boolean true -- 空白行  false -- 非空白行
   */
    isCurrentLineEmpty(): boolean;

    /**
     * 是否显示超过高度的文本
     * @param bShow true: 显示， false: 不显示  默认不显示
     */
    showShelteredText(bShow: boolean): number;

    /**
     * 设置当前段落或者全文档的西文字体在单词中间断行的属性
     * @param nType 1 – 当前段落 2 – 全文档
     * @param bFlag True -- 允许西文换行。 False – 不允许
     */
    setWestCharBreakAttribute(nType: number, bFlag: boolean): number;

    /**
     * 设置文档属性，一般为自定义属性
     * @param itemName 属性类型名称
     * @param content 设置的属性内容
     */
    setFileProperty(itemName: string, content: string): number;

    /**
     * 获取文档指定属性的值
     * @param itemName 属性类型名称
     */
    getFileProperty(itemName: string): string;

    /**
     * 设置页面边距 设置页面页边距大小(单位cm)
     * @param fPageLeft 左页边距
     * @param fPageRight 右页边距
     * @param fPageTop 上页边距
     * @param fPageBottom 下页边距
     */
    setPageMargin(fPageLeft: number, fPageRight: number, fPageTop: number, fPageBottom: number): number;

    /**
     * 获取页面边距
     */
    getPageMargin(): { fWidth: number; fHeight: number; fPageLeft: number; fPageRight: number; fPageTop: number; fPageBottom: number };
    /**
     * 设置页面大小 为打印设置页面的纸张格式等属性
     * @param nPageFormat 纸张格式，例如: A3=3、A4=4、自定义=11 (目前只支持A3,A4,自定义)
     * @param fPageWidth 纸张的宽度 cm (只有纸张格式为自定义才有效)
     * @param fPageHeight 纸张的高度 cm (只有纸张格式为自定义才有效)
     * @param bHorOrVer 预留 (无用)
     * @param nPageLayOut 预留 (无用)
     */
    setPageFormat(nPageFormat: number, fPageWidth?: number, fPageHeight?: number,
                  bHorOrVer?: boolean, nPageLayOut?: number): number;

    /**
     * 在光标当前位置插入文本内容
     * @param sText 文本内容
     */
    insertTextAtCurrentCursor(sText: string): number;

    /**
     * 当前光标处插入字符串流文件
     * @param base64String 需要插入的文件流
     */
    insertFileWithString(base64String: string): Promise<number>;

    /**
     * 当前光标处插入Blob
     * @param base64String Blob
     */
    insertFileWithStream(content: Blob): Promise<number>;

    /**
     * 合并文档
     * @param base64String 待合并的文件字符串流
     * @param bDifferentHeader 页眉不一样
     * @param bFirstDifferentHeader 首页页眉不一样
     */
    mergeDocumentsWithString(base64String: string, bDifferentHeader?: boolean,
                             bFirstDifferentHeader?: number): Promise<number>;

    /**
     * 合并文档
     * @param content blob
     * @param options: bDifferentHeader 页眉不一样 , bFirstDifferentHeader 首页页眉不一样, retainCascade: 是否保留级联
     */
    mergeDocumentsWithStream(content: Blob, options?: string): Promise<number>;
        //bDifferentHeader?: boolean, bFirstDifferentHeader?: number): Promise<number>;

    /**
     * True -- 进入设计模式。 False – 离开设计模式 进入设计模式后，被隐藏的数据元将显示出来
     * @param bFlag 设计模式状态控制
     */
    designTemplet(bFlag: boolean): void;

    /**
     * 清洁浏览：不显示痕迹，不显示背景色；只有占位符的结构在浏览的时候会被隐藏
     * 正常模式：只有占位符的结构显示出来。
     * @param nType 1 -- 清洁模式 0 – 正常模式
     * @param nProtected 0 -- 不需要保护状态 1 -- 需要保护状态
     */
    browseTemplet(nType: number, nProtected: number): number;

    /**
     * 返回当前光标的页码
     * @return 当前光标的页码，失败为 0
     */
    getCurrentCursorPage(): number;

    /**
     * 判断文档打开后是否被修改
     * @return 文档状态，如果被修改，那么返回true，反之返回false.
     */
    isDocModified(): boolean;

    /**
     * 设置文档修改的状态，既可以设置为"修改"状态, 也可以设置为"非修改"状态。
     * @param bModify 修改状态 true：表示"修改"状态 false：表示"非修改"状态
     */
    setDocModified2(bModify: boolean): number;

    /**
     * 获取选中的文本
     * @return 选中的文本
     */
    getSelectText(): string;

    /**
     * 高亮选中整个文档（不包含页眉 页脚）
     */
    selectAllDoc(): void;

    /**
     * 对全文档进行只读保护或者解除只读保护
     * @param bProtect 是否只读模式 true 保护 false 解除保护
     */
    protectDoc(bProtect: boolean): number;

    /**
     * 文档是否处于只读保护状态。
     */
    isProtectedMode(): boolean;

    /**
     * 删除文档末尾的空白行、空格、Tab键，及控制是否删除文末分页符
     * @param bBlankLine 空白行
     * @param bSpace 空格(全角 半角)
     * @param bTag Tab 字符
     * @param bDelPageBreak 是否删除文末分页符
     */
    deleteRedundantEx(bBlankLine: boolean, bSpace: boolean, bTag: boolean, bDelPageBreak: boolean): number;

    /**
     * 在当前位置上插入新的一行
     */
    insertNewLine(): number;

     /**
     * 弹出插入特殊字符对话框
     */
    insertSpecialCharacter():number;


    /**
     * 复制当前选定的内容。
     */
    copy(): number;

    /**
     * 剪切当前选定的内容。
     */
    cut(): number;

    /**
     * 粘贴当前剪贴板的内容。
     */
    paste(): Promise<number>;

    /**
     * 删除当前光标选中的内容。
     */
    delete(): void;

    redo(): void;

    undo(): void;

    /**
     * 刷新文档里的所有级联。
     */
    updateAllConnections(): number;

    /**
     * 获取整个文档的Text数据。（需要打开文档）
     */
    getTextFromDocument(): string;

    /**
     * 返回当前打开文档的一串32位的MD5码。
     */
    getFileMd5(): Promise<string>;

    /**
     * 设置剪贴板格式，外部剪贴板与内部剪贴板是否带格式
     * @param bOut 外部粘贴内容
     * @param bIn Apollo格式内容
     */
    setClipboardFormat(bOut: boolean, bIn: boolean): number;

    /**
     * 设置拷贝的附加信息(如文档名称)
     * 此信息设置有值时，会触发拷贝控制，只有信息一致的情况下，才能允许互相拷贝。此信息为空的情况下，不会触发拷贝控制。
     * @param sCopyInformation 信息段设置
     */
    setExtraCopyInformation(sCopyInformation: string): number;

    /**
     * 设置是否可以从外部(比如IE，记事本，VS等等)拷贝到编辑器
     * @param bEnable 设置是否可以从外部(比如IE,记事本等等)拷贝到odt true – 可以false – 不可以
     */
    enableCopyFromExternal(bEnable: boolean): number;

    /**
     * 光标跳到指定的页面页首
     * @param pageIndex 指定页索引
     */
    jumpToPage(pageIndex: number): void;

    /**
     * 光标跳到文档的第一页页首。
     */
    jumpToFirstPage(): void;

    /**
     * 光标跳到文档的最后一页页首
     */
    jumpToLastPage(): void;

    /**
     * 光标跳到当前页的末尾。
     */
    jumpToEndOfPage(): void;

    /**
     * 光标跳到当前页的开始。
     */
    jumpToStartOfPage(): void;

    /**
     * 光标跳转到文件末尾
     */
    jumpToFileEnd(): void;

    /**
     * 光标跳转到指定位置
     * @param sPosition 位置
     */
    jumpToOnePosition(sPosition: string): number;

    /**
     * fucntionID = 100 将指定的下拉框改成comboBox
     * json: {"name":["aaa1","bbb1"]}
     * @param functionID 将指定的下拉框改成comboBox
     * @param param json: {"name":["aaa1","bbb1"]}
     */
    executeMethod(functionID: number, param: string): any;

    /**
     * 获取选中区域头位置
     * @return 空值 表示失败
     */
    getSelectionRangeStart(): string;

    /**
     * 获取选中区域尾位置
     * @return  空值 表示失败
     */
    getSelectionRangeEnd(): string;

    /**
     * 根据字符位置反亮选中指定的区域
     * @param sStartPos: 选中的开始位置
     * @param sEndPos: 选中的结束位置
     */
    selectOneArea2(sStartPos: string, sEndPos: string): void;

    /**
     * 在当前光标位置选中字符
     * @param nCharNumber 选中长度
     * @param direction 选择方向： 1-- 左，2 -- 右
     * @returns
     */
    selectOneArea(nCharNumber: number, direction: number): void;

    /**
     * 在当前光标处插入分页符
     */
    insertPageBreak(): number;

    /**
     * 插入条形码
     * @param sJson text string 条形码文本 width string 像素宽度px height string 像素高度px
     * name string 元素NAM showText boolean 是否显示文本
     * alignment string center,left,right (当显示文本为true的时候 才需要设置)
     * errorCL: 暂时无用
     */
    insertBarcode(sJson: string): string;

    /**
     * 插入二维码
     * @param sJson text string 二维码文本 name string 元素NAM
     */
    insertQRCode(sJson: string): Promise<string>;

    /**
     * 在光标所在位置插入字符串流的图片
     * @param base64String 图片路径
     * @return 插入图片的名称
     */
    addImageWithString(base64String: string): Promise<string>;
    /**
     * 在光标所在位置插入url指定的图片
     * @param url 图片url
     * @return 插入图片的名称
     */
     addImageWithUrl(url: string): Promise<string>;

    /**
     * 在光标所在位置插入字符串流的可编辑图片
     * @param base64String 图片路径
     * @return 插入图片的名称
     */
    addEditableImageWithString(base64String: string): Promise<string>;

    /**
     * 在指定位置插入签名图片
     * @param sStructName 结构化名称
     * @param nMark 左右边框的前后位置（1 – 后边框的后端 0 – 前边框的前端 2 – 后边框的前端 3 – 前边框的后端）
     * @param sBase64 签名图片的链接字符串
     * @param nWidht 图片宽度
     * @param nHeight 图片高度
     */
    addSignaturePicToSectionWithStream(sStructName: string, nMark: number,
                                       sBase64: string, nWidht: number, nHeight: number): Promise<string>;

    /**
     * 删除一个指定的图片
     * @param sName 图片名称
     */
    deleteImage(sName: string): number;

    /**
     * 对一个合法的图片对象重命名
     * @param sName 图片名称
     * @param sNewName 图片新名称
     * @return 重命名后的图片的名称
     */
    setImageName(sName: string, sNewName: string): number;

    /**
     * 对一个图片设置长宽
     * @param sName 图片名称
     * @param nWidth 指定图片宽
     * @param nHeight 指定图片高
     */
    setImageSize(sName: string, nWidth: number, nHeight: number): number;

    /**
     * 获取当前光标选中图片的名称
     */
    getCurrentImageName(): string;

    /**
     * 设置指定图片是否可以删除
     * @param sName 图片名称
     * @param bDeleteProtect 是否删除保护
     */
    setImageDeleteProtection(sName: string, bDeleteProtect: boolean): number;

    /**
     * 设置指定图片是否可以拷贝
     * @param sName 图片名称
     * @param bCopyProtect 是否拷贝保护
     */
    setImageCopyProtection(sName: string, bCopyProtect: boolean): number;

    /**
     * 设置图片的自定义属性
     * @param sImageName 图片名称
     * @param sPropName 自定义属性名
     * @param sPropValue 自定义属性值
     */
    setImageCustomProperty(sImageName: string, sPropName: string, sPropValue: string | number): number;

    /**
     * 获取图片的自定义属性
     * @param sImageName 图片名称
     * @param sPropName 自定义属性名
     */
    getImageCustomProperty(sImageName: string, sPropName: string): any;

    getAllImagesByCurrentDoc(): string;

    /**
     * 获取指定结构内容全部图片名称
     * @param sName 结构名称
     */
     getAllImagesInStruct(sName: string): string;

    /**
     * 在当前光标位置插入给定名称的医学公式
     * @param nType 医学公式类型: 1 － 牙齿 2 － 视野 3 － 月经 4 － 文本 13－ 瞳孔 14－ 光定位 15－ 胎动
     * @param sID 医学公式名称
     * @param sJson 医学公式内容
     */
    insertMedicalformula(nType: number, sID: string, sJson: string): number;

    /**
     * 设置指定医学公式的类型和内容
     * @param sID 医学公式名称
     * @param sJson 医学公式内容
     */
    setMedicalformulaText(sID: string, sJson: string): number;

    /**
     * 设置修订模式的类型
     * @param bFlag 模式的类型
     */
    setRecensionProtectMode(bFlag: boolean): number;

    clearUndoList(): void;

    /**
     * 在当前光标位置插入表格。
     * @param name 表格名称
     * @param col 列数
     * @param row 行数
     * @param hasTitlerow 是否有头行
     * @param nNumbers 头行数
     */
    insertTable(name: string, col: number , row: number, hasTitlerow: boolean, nNumbers: number ): number;

    /**
     * 获取修订痕迹数量
     * @returns 修订痕迹数量
     */
    getRevisionCount(): {
        total: number;           // 总修订数
        additions: number;       // 新增修订数
        deletions: number;       // 删除修订数
        users: string[];         // 参与修订的用户列表
    };

    /**
     * 获取修订痕迹详情
     * @param author 修订者姓名
     * @returns 修订痕迹详情
     */
    getRevisionDetails(author?: string): Array<{
        userName: string;        // 修订者姓名
        userId: string;          // 修订者ID
        time: string;            // 修订时间 (ISO格式)
        level: number;           // 修订级别
        type: string;            // 修订类型：'add' | 'remove'
        value: string;           // 修订内容
    }>;

    /**
     * 在当前光标位置插入表格。
     * @param name 表格名称
     * @param col 列数
     * @param row 行数
     * @param json headerNum：number  // 标题行数  bRepeatHeader：boolean // 跨页是否重复标题行
     */
    insertTableWithParament(name: string, col: number, row: number, sJson: string): number;

    /** 将光标移动至当前表格所选行的最后一个单元格 */
    moveCursorToTableRowEnd(): boolean;

    /**
     * 返回单元格内结构化元素名称集合
     * @param sTalbeName 表格名称
     * @param sCellName 单元格名称
     * @return 结构化元素name，以英文逗号隔开
     */
    getStructsNameByCell(sTalbeName: string, sCellName: string): string;

    /**
     * 批量设置指定表格单元格的文本（传数组）
     * @param sName 表格名称
     * @param sJsonContent 单元格内容的json数组
     */
    putCellContentByArray(sName: string, sJsonContent: string): number;

    /**
     * 获取当前文档中所有表格名字
     */
    getAllTableNamesByCurrentDoc(): string;

    /**
     * 返回当前光标位置的表格名
     */
    getTableNameByCurrentCursor(): string;

    /**
     * 对指定表格的某行之前（后）插入数行。
     * @param name 表格名称
     * @param nIndex 插入的参照位置在第几行//从1开始整数(1表示表格第一行上面插入行)
     * @param nCount 插入几行
     * @param nDirection 1 – 往下新增行 0 – 往上新增行
     */
    incMultiRows(name: string, nIndex: number , nCount: number, nDirection: number): number;

    /**
     * 删除指定行
     * @param sName 表格名称
     * @param index 删除行位置
     * @param nCount 删除行数据
     * @param nDirection 1 – 往下新增行 0 – 往上新增行
     */
    delMultiRows(sName: string, index: number, nCount: number, nDirection: number): number;

    /**
     * 设置表格属性（目前只支持自定义属性）
     * @param sName 表格名称
     * @param sProp 属性json
     */
    setTableProp(sName: string, sProp: string): number;

      /**
     * 设置表格单元格公式（目前只支持自定义属性）
     * @param sName 表格名称
     * @param sCell 单元格
     * @param sFormula 公式
     */
    setTableCellFormula(sName: string, sCell: string,sFormula:string): number;

    /**
     * 获取表格属性（目前只支持自定义属性）
     * @param sName 表格名称
     * @param sProp 属性json
     */
    getTableProp(sName: string, sProp: string): string;
    
    /**
     * 获取指定表格的行数
     * @param sTableName 表格名称
     * @returns 表格的行数，如果表格不存在则返回0
     */
    getTableRowCount(sTableName: string): number;
    
    /**
     * 获取指定表格的列数
     * @param sTableName 表格名称
     * @returns 表格的列数，如果表格不存在则返回0
     */
    getTableColumnCount(sTableName: string): number;

    /**
     * 获取表格内结构化元素的信息
     * @param sName 表格名称
     * @param sRev 预留参数
     */
    getTableXmlInfoByParament(sName: string, sRev: string): string;

    /**
     * 获取表格内单元格的内容
     * @param sName 表格名称
     * @param sJson 条件json
     */
    getTableContentByParament(sName: string, sJson: string): string;

    /**
     * 获取光标所在的表格的单元格名称
     */
    getTableCellNameByCurrentCursor(): string;

    /**
     * 合并目标表格的指定单元格
     * @param sTableName 表格名称
     * @param sCellNames 单元格名称（多个单元格以分号隔开）
     */
    mergeTableCell(sTableName: string, sCellNames: string): boolean;

    /**
     * 获取指定表格指定单元格的文本内容
     * @param sTableName 表格名称
     * @param sCellName 单元格名称
     */
    getCellContent(sTableName: string, sCellName: string): string;

    /**
     * 获取指定表格指定单元格中的结构化元素名称列表
     * @param sTableName 表格名称
     * @param sCellName 单元格名称
     * @return 结构化元素名称，多个名称以英文逗号分隔
     */
    getStructNamesByCell(sTableName: string, sCellName: string): string;

    /**
     * 获取指定表格指定单元格所在列的文本内容
     * @param sName 表格名称
     * @param sCol 单元格名称
     */
    getColContent(sName: string, sCol: string): string;

    protectTable(sTable: string , bEditorProtect: boolean): number;

    protectTableCell(sTable: string, sCellName: string, bEditorProtect: boolean): number;

    /**
     * 显示、隐藏缓存的区域
     * @param name 区域名称
     * @param flag 显示、隐藏
     */
    collapseRegion(name: string, flag: boolean): number;

    /**
     * 当前光标位置插入区域
     * @param sName 区域名称
     */
    insertRegionAtCurrentCursor(sName: string ): string;

    swapRegions(sRegionA:string,sRegionB:string):Promise<number>;

    sortRegions(sJson:string):Promise<number>;

    getIncompletedCtrlNameListInRegion( sRegion:string ):string;

    /**
     * 让编辑器失去或者拥有焦点
     * @param flag true: 拥有；false: 失去
     */
    setFocus(flag: boolean): number;

    /**
     * 删除指定区域内部末尾的空白,包括（空行 空格 Tab字符等，分页符可选）
     * @param strName 区域名称
     * @param bPageBreak True – 删除分页符 False – 不删除分页符
     */
    deleteRedundantByRegionName(strName: string, bPageBreak: boolean): number;

    /**
     * 在指定区域的后面插入一个区域，并且指定插入区域的名称
     * @param sRegion 插入的区域名称
     * @param sPrveRegion 指定插入区域的名称
     */
    insertRegionAfterOneRegion(sRegion: string, sPrveRegion: string): number;

    /*
    将指定区域保存为blob
     */
    saveRegionContentToStream(sRegion:string,sJson:string): Promise<Blob>;

    /**
     * 获得当前光标所在的region的名称
     */
    getCurrentRegionName(): string;

    /**
     * 获取指定一个区域的脏标记状态
     * @param sRegionName 区域名称
     */
    getRegionModifyFlag(sRegionName: string): number;

    getRegionText(name: string): string;

    /**
     * 获取整个文档区域的脏标记符合某个状态的名称集合/可以只获取最外层区域，或者所有层次的区域
     * @param nOnlyFirstRegions 1 – 只获取最外层 0 --- 获取所有区域
     * @param nModifyFlag 1 – 只获取修改过的 0 --- 只获取未修改过的
     */
    getAllRegionsByModifyFlag(nOnlyFirstRegions: number, nModifyFlag: number): string;

    /**
     * 将当前文档所有的区域记录脏标记置位为0
     */
    cleanAllRegionsModifyFlag(): number;

    /**
     * 获得区域属性
     * @param sName 区域名称
     * @param sProp 属性名
     */
    getRegionProp(sName: string, sProp: string): any;

    /**
     * 设置区域属性
     * @param sName 区域名称
     * @param sProp 属性名
     * @param sValue 属性值
     */
    setRegionProp(sName: string, sProp: string, sValue: any): number;

    /**
     * 设置区域属性
     * @param sName 区域名称
     * @param sJsons 区域属性集合
     */
    setRegionPropByArray(sName: string, sJsons: string): number;

    /**
     * 一次获取指定结构化元素的所有属性。
     * @param sName 区域名称
     */
    getRegionPropByArray(sName: string): string;

    setRegionBorderViewMode(nViewType: number): number;

    /** 设置指定区域是否常显边框 */
    setRegionBorderVisible(sRegion: string, bShow: boolean): number;

    /** 设置所有区域是否常显边框 */
    setAllRegionsBorderVisible(bShow: boolean): number;

    setRegionName(sName: string, sNewName: string): number;

    /**
     * 删除一个区域
     * @param sName 区域名称
     * @param lFlag 1：删除区域结构（内容不删除）2：删除区域内容 3：删除区域和内容
     */
    deleteRegion(sName: string, lFlag: number): number;

    /**
     * 获取当前光标所在区域的最外层区域名称
     */
    getOutestRegionNameByCurrentCursor(): string;

    /**
     * 获取指定结构（数据元或者数据组）名称的最近的父级区域的名称
     * @param sStructsName 元素，节，区域。
     */
    getFatherRegionNameOfOneStruct(sStructsName: string): string;

    /**
     * 按照某个属性名（可以同时带属性值）进行筛选，筛选出符合条件的结构名
     * @param sRegionName 区域名
     * @param sPropName 属性名
     * @param sPropValue 属性值
     * @return 在指定区域内，返回符合条件的所有的结构名称 区域跟结构以"|"隔开，批次之间以"," 隔开
     */
    filterStructsByPropInRegion(sRegionName: string, sPropName: string, sPropValue: any): string;

    /**
     * 依次返回当前文档中Region名称列表
     */
    getAllRegionNamesByCurrentDoc(): string;

    /**
     * 返回文档第一层Region名称列表
     */
    getFirstLevelRegionNames(): string;

    /**
     * 对指定的某个区域设置是否只读，其他剩余的区域设置是否只读
     * @param sCurrentRegionName 区域名称
     * @param bCurrent 指定区域只读标识
     * @param bLef 除了指定区域外，其他最外层区域的只读标识
     */
    setRegionsReadOnlyProp(sCurrentRegionName: string, bCurrent: boolean, bLef: boolean): number;

    /**
     * 获得指定区域尾位置索引
     * @param sName 区域名称
     */
    getRegionEnd(sName: string): string;

    /**
     * 获得指定区域头位置索引
     * @param sName 区域名称
     */
    getRegionBegin(sName: string): string;

    /**
     * 在指定区域的前端或者后端加入一个新行。
     * @param sRegion 区域名称
     * @param nPosType 1 – 区域前端 2 – 区域后端
     */
    addNewLineForRegion(sRegion: string, nPosType: number): number;

    /**
     * 将当前文档的所有内容变成一个区域
     * @param sRegionName 区域名称
     */
    changeAllFileToOneRegion(sRegionName: string): string;

    /**
     * 设置指定区域内容文本
     * @param sName 区域名称
     * @param sText 区域内容
     */
    setRegionText(sName: string, sText: string): number;

    /**
     * 光标跳转到某一个区域的前面或者后面
     * @param strName 区域名称
     * @param bBack true – 区域外的行开始处 false – 区域前的行结束处。
     */
    cursorJumpOutOfOneRegion(strName: string, bBack: boolean): number;

    /**
     * 选中指定名称的区域
     * @param sName 区域名称
     * @param bOnlyContent true － 只选中区域的内容 false － 选中整个区域结构（暂时没用
     */
    selectOneRegion(sName: string, bOnlyContent?: boolean): number;

    /**
     * 以XML格式依次返回指定区域的Region, Section 和NewControl的结构化元素信息
     * @param name 区域名称
     * @param sRev 保留参数 目前无用
     */
    getRegionXmlInfoByParament(name: string, sRev?: string): string;

    /**
     * 往指定区域的插入一个文件流
     * @param strName 区域名称
     * @param cContent 链接文件的base64 数据流
     * @param option 是否保留级联属性等
     */
    setRegionFileLinkWithStream(strName: string, cContent: Blob, option?: string): Promise<number>;


    /**
     * 获得指定名称结构化元素的最近的父级结构化元素名称
     * @param sName 结构化元素名称
     */
    getFatherStructName(sName: string): string;

    /**
     * 获得选中区域中结构化元素的名称列表
     * @return 选中区域中结构化元素的名称列表, 空则表示选中区域中无结构化元素. 多个结构化元素 采用逗号分隔，依此排列。节跟元素之间用分号隔开。
     */
    getStructBySelectArea(): string;

    /**
     * 获取范围内名称
     * @returns 区域、结构化名称
     */
    getStructsNameListFromSelectedArea(): string;

    /**
     * 一次获取指定结构化元素的所有属性。
     * @param sName 结构化名称
     */
    getStructPropByArray(sName: string): string;

    /**
     * 获取当前光标的结构化名称
     */
    getCurrentStructName(): string;

    /**
     * 根据名称获取结构的类型，结构包括节，元素及区域
     * @param name 结构化名称
     */
    getStructTypeByName(name: string): number;

    /**
     * 设置checkBox的勾选状态
     * @param name 结构化名称
     * @param bChecked 选中状态
     */
    setCheckboxStatus(name: string, bChecked: boolean): number;

    /**
     * 设置checkBox的文本
     * @param name 结构化名称
     * @param sCaption 展示文本
     */
    setCheckboxCaption(name: string, sCaption: string): number;

    /**
     * 读取checkBox的文本
     * @param name 获取文本
     */
    getCheckboxCaption(name: string): string;

    /**
     * 设置checkBox的 code 文本
     * @param name checkBox名称
     * @param sCode 对应的属性值
     */
    setCheckboxCode(name: string, sCode: string): number;

    /**
     * 获取checkBox的 code 文本
     * @param name checkBox名称
     */
    getCheckboxCode(name: string): string;

    /**
     * 返回所有设置过组的checkbox的组名
     */
    getAllGroupCheckboxName(): string;

    /**
     * 返回指定组的checkbox的check状态
     * @param sGroupName 组名
     */
    getGroupCheckboxStatus(sGroupName: string): number;

    /**
     * 返回指定checkbox的组名
     * @param sCheckBox 结构化名称
     */
    getCheckboxGroupName(sCheckBox: string): string;

    /**
     * 对指定组的checkbox的最后一个checkbox显示特定的信息
     * @param sGroupName 组名
     * @param sInfo 特定的信息
     */
    showInfoToGroupCheckbox(sGroupName: string, sInfo: string): number;

    /**
     * 设置指定名称RadioButton的Code和Value值
     * @param sName 结构化名称
     * @param sJson code和value的json
     */
    setRadioButtonCodeAndValueByArray(sName: string, sJson: string): number;

    addRadioButtonCodeAndValueByArray(sName: string, sJson: string): number;

    /**
     * 获取指定名称RadioButton当前选中项的Value值
     * @param sName 结构化名称
     */
    getRadioButtonSelectItemValue(sName: string): string;

    /**
     * 设置指定按钮元素目标项的文本颜色
     * @param sName 按钮元素名称
     * @param index 选择项位置索引（0-）,多个索引用逗号分隔
     * @param color 颜色值
     */
    setRadioButtonItemTextColor(sName: string, index: string, color: string): number;

    /**
     * 根据索引选中指定名称RadioButton的指定项
     * @param sName 按钮元素名称
     * @param index 选择索引：选择多项时，用逗号分隔
     */
    selectRadioButtonItemByIndex(sName: string, index: string): number;

    /**
     * 获取指定名称RadioButton当前选中项的索引值
     * @param sName 按钮元素名称
     */
    getRadioButtonSelectedIndexes(sName: string): string;

    /**
     * 选中指定名称的RadioButton的指定Value项
     * @param sName 结构化名称
     * @param sValue 选中的值
     */
    selectRadioButtonItemByValue(sName: string, sValue: string): number;

    /**
     * 删除指定名称RadioButton的所有项
     * @param sName 结构化名称
     */
    deleteAllRadioButtonItem(sName: string): number;

    /**
     * 获取指定名称控件的value数组
     * @param sName 结构化名称
     */
    getRadioButtonValueWithArray(sName: string): string;

    /**
     * 选中指定名称RadioButton的指定Code项
     * @param sName 结构化名称
     */
    getRadioButtonCodeWithArray(sName: string): string;

    /**
     * 选中指定名称RadioButton的指定Code项
     * @param sName 结构化名称
     * @param sCode code值
     */
    selectRadioButtonItemByCode(sName: string, sCode: string): number;

    /**
     * 将指定名称RadioButton 选项清空
     * @param sName 结构化名称
     */
    clearRadioButtonCheckItem(sName: string): number;

    /**
     * 在光标处插入文本框 或者 节。
     * @param sName Textbox名称
     * @param sText 内容值
     * @param nType 类型 0 元素 1 节
     */
    insertStructAtCurrentCursor(sName: string, sText: string, nType: number): number;

    /**
     * 设置指定结构化元素 的文本内容
     * @param sName 结构化名称
     * @param sText 文本内容
     */
    setStructText(sName: string, sText: string): number;

    /**
     * 用波浪线标记选中的文本
     * @param options 波浪线选项配置
     * @returns 返回波浪线ID，失败返回null
     */
    markTextWithWave(options?: {content?: string, type?: number}): string | null;

    /**
     * 将所有结构化元素（元素 节）边框设置成true falae
     * @param bFlag /
     */
     setStructsBorderVisible(bFlag: boolean): number;

     /**
      * 将所有的元素跟节的占位符设置为指定字符串，（是否只设置占位符为空）
      * @param onlyBank 是不是只有占位符是""才设置
      * @param sString 指定字符串
      */
     resetStructsPlacehold(onlyBank: boolean, sString: string): number;

    /**
     * 设置文本框，节的标题
     * @param sName 文本框或者节名称
     * @param sTitle 标题
     */
    setStructTitle(sName: string, sTitle: string): number;

    /**
     * 获取文本框，节的标题
     * @param sName 文本框或者节名称
     */
    getStructTitle(sName: string): string;

    /**
     * 获取结构化元素的文本
     * @param sName 结构化名称
     */
    getStructText(sName: string): string;

    /**
     * 返回结构化元素的 开始位置
     * @param sName 结构化名称
     * @return 位置； 空值 表示失败
     */
    getStructBegin(sName: string): string;

    /**
     * 返回结构化元素的结束位置
     * @param sName 结构化名称
     * @return 位置； 空值 表示失败
     */
    getStructEnd(sName: string): string;

    /**
     * 设置结构化元素的 属性
     * @param sName 结构化名称
     * @param sPropName 属性名
     * @param sValue 属性值
     */
    setStructProp(sName: string, sPropName: string, sValue: any): number;

    /**
     * 设置文本框可输入的最大长度
     * @param sName 结构化名称
     * @param nMaxLen 最大长度 大于等于0的整数 0 – 取消长度限制
     */
    setTextBoxMaxLen(sName: string, nMaxLen: number): number;

    /**
     * 获取文本框可输入的最大长度
     * @param sName 结构化名称
     */
    getTextBoxMaxLen(sName: string): number;

    /**
     * 批量对多个结构化元素的内容赋值。
     * @param sJson 多个结构化元素Json
     */
    setStructsTextByArray(sJson: string): number;

    /**
     * 获取指定名称日期框的值
     * @param name 结构化名称
     * @param sRev 扩展参数"AllDate" 假如年份月日格式里面不存在的话，也会返回
     */
    getDateTimeBoxValueEx(name: string, sRev?: string): string;

    /**
     * 设置指定名称日期框的值
     * @param sName 结构化名称
     * @param sValue 日期框的值
     */
    setDateTimeBoxValue(sName: string, sValue: string): number;

    /**
     * 获取指定名称日期框的默认显示格式
     * @param sName 结构化名称
     */
    getDateTimeFormat(sName: string): string;

    /**
     * 设置指定指定名称日期框的默认显示格式
     * @param sName 结构化名称
     * @param nType 1   YYY-MM-DD 2	YYYY-MM-DD	HH：MM：SS	3	YYYY-MM-DD	HH：MM 4  HH：MM：SS
     * @param setDateTimeFormat 自定义格式时，日期格式；
     */
    setDateTimeFormat(sName: string, nType: number, setDateTimeFormat?: string): number;

    /**
     * 批量对多个结构化元素的属性赋值。
     * @param sJson 多个结构化元素Json
     */
    setStructsPropByArray(sJson: string): number;

    /**
     * 设置Numbox的文本
     * @param sName 结构化名称
     * @param nText 数值框的文本
     */
    setNumboxText(sName: string, nText: number): number;

    setNumboxUnit(name: string, unit: string): number;

    getNumboxUnit(name: string): string;

    /**
     * 功能描述：获取包含非法值得Numbox的名称列表
     * @param bFlag 1 只包含强制校验得数值框 2 所有的数值框
     */
     getIllegalValueNumbox(bFlag: number): string;

    /**
     * 获取Numbox的文本
     * @param name 结构化名称
     */
    getNumboxText(name: string): number;

    /**
     * 设置Numbox的取值上限
     * @param name 结构化名称
     * @param maxValue 最大值
     */
    setNumboxMaxValue(name: string, maxValue: number): number;

    /**
     * 获取Numbox的取值上限
     * @param name 结构化名称
     */
    getNumboxMaxValue(name: string): number;

    /**
     * 设置Numbox的取值下限
     * @param name 结构化名称
     * @param minValue 最小值
     */
    setNumboxMinValue(name: string, minValue: number): number;

    /**
     * 获取Numbox的取值下限
     * @param name 结构化名称
     */
    getNumboxMinValue(name: string): number;

    /**
     * 设置Numbox的精度
     * @param name 结构化名称
     * @param precision 精度值
     */
    setNumboxPrecision(name: string, precision: number): number;

    /**
     * 获取Numbox的精度
     * @param name 结构化名称
     */
    getNumboxPrecision(name: string): number;

    /**
     * 设置数字框输入错误后的警告信息，比如输入英文字母
     * @param strName 结构化名称
     * @param strInfo 警告信息
     */
    setNumboxErrorInputInfo(strName: string, strInfo: string): number;

    /**
     * 设置数字框输入不在最小值到最大值范围的警告信息
     * @param strName 结构化名称
     * @param strInfo 警告信息
     */
    setNumboxOutRangeInfo(strName: string, strInfo: string): number;

    /**
     * 获取指定下拉框的当前Code值
     * @param sName 结构化名称
     * @param nType 中标预留值，暂时没用
     */
    getCompoundBoxCurrentCode(sName: string, nType?: number): string;

    /**
     * 获取指定下拉框的当前value值
     * @param sName 结构化名称
     * @param nType 中标预留值，暂时没用
     */
    getCompoundBoxCurrentValue(sName: string, nType?: number): string;

    /**
     * 获取指定名称控件的code数组
     * @param sName 结构化名称
     * @param nType 中标预留值，暂时没用
     */
    getCompoundBoxCodeWithArray(sName: string, nType?: number): string[];

    /**
     * 获取指定名称控件的value数组
     * @param sName 结构化名称
     * @param nType 中标预留值，暂时没用
     */
    getCompoundBoxValueWithArray(sName: string, nType?: number): string[];

    /**
     * 获取指定名称控件的Code对应的Value值
     * @param sName 结构化名称
     * @param sCode 控件列表的Code值
     * @param nType 中标预留值，暂时没用
     */
    getCompoundBoxValueByCode(sName: string, sCode: string, nType?: number): string;

    /**
     * 设置指定名称控件的Code和Value值；当已存在code值是，则修改value，当不存在时，则新增
     * @param sName 结构化名称
     * @param sCode 控件列表的Code值
     * @param sValue 控件列表的Value值
     * @param nType 中标预留值，暂时没用
     */
    setCompoundBoxCodeAndValue(sName: string, sCode: string, sValue: string, nType?: number): number;

    /**
     * 设置指定多选下拉控件的分隔符
     * @param sName 结构化名称
     * @param sSeparator 分隔符的信息
     */
    setMultiDropdownControlSeparator(sName: string, sSeparator: string): number;

    /**
     * 设置指定多选下拉控件的选中项及未选中项之间的分隔符
     * @param sName 结构化名称
     * @param sSeparator 分隔符的信息
     */
    setMultiDropdownControlGroupSeparator(sName: string, sSeparator: string): number;

    /**
     * 设置下拉框控件的下拉窗体弹出模式( 单击弹出还是双击弹出)
     * @param bEnable True – 双击弹出 False – 单击弹出
     */
    setCompoundBoxDropMode(bEnable: boolean): number;

    /**
     * 获取指定name的Struct的指定属性
     * @param sName 结构化名称
     * @param sPropName 属性名
     */
    getStructProp(sName: string, sPropName: string): string | boolean;

    /**
     * 删除一个结构化元素
     * @param sName 结构化名称
     */
    deleteStruct(sName: string): number;

    /**
     * 重新命名指定 结构化元素的name
     * @param sName 结构化名称
     * @param sNewName 新的名称
     */
    setStructName(sName: string, sNewName: string): number;

    /**
     * 光标跳转到某一个结构的边框前或者后
     * @param sName 结构化名称
     * @param nMark 前端或者后端  1 – 后边框的后端 0 – 前边框的前端 2 – 后边框的前端 3 – 前边框的后端
     */
    cursorJumpOutOfOneStruct(sName: string, nMark: number): number;

    /**
     * 光标选中某一个结构的所有内容,但是不包括边框(无论边框是否隐藏)
     * @param sName 结构化名称
     */
    selectOneStructContent(sName: string): number;

    /**
     * 光标选中某一个结构，包括边框
     * @param sName 结构化名称
     */
    selectOneStruct(sName: string): number;

    /**
     * 以Json格式依次返回文档的结构化信息. （不包含嵌套信息）
     */
    getStructsXmlInfoByParament(): string;

    /**
     * 批量对多个结构化元素的内容赋值。
     * @param josn 设置text json
     */
    setStructsTextByJson(json: string): number;

    /**
     * 以Json格式依次返回文档的结构化信息和表格信息. （不包含嵌套信息）
     */
    getStructsXmlInfoByParament2(sJson: string): string;

    /**
     * 以Json格式不打开文档依次返回文档的结构化和区域信息. （不包含嵌套信息）
     */
    getStructsXmlInfoByFile(content: Blob): Promise<string>;

    /**
     * 以Json格式不打开文档依次返回文档的结构化和区域信息. （不包含嵌套信息）
     */
    getStructsXmlInfoByFile2(content: Blob, sJson: string): Promise<string>;

    /**
     * 按照某个属性名（可以同时带属性值）进行筛选，筛选出符合条件的结构名
     * @param sJson 配对属性json格式对象
     * @param sRev1 预留参数
     * @return 返回为字符串，以英文,隔开
     */
    filterStructsByProp(sJson: string, sRev1?: string): string;

    /**
     * 获取当前文档所有必填项未填的新式控件的名称列表
     * @return 返回为字符串，以英文,隔开
     */
    getIncompletedCtrlNameList(): string;

    /**
     * 获取当前文档中所有包含错误的元素名称列表
     */
    getErrorCtrlNameList():string;

    /**
     * 保存指定结构为字符串数据。(base64编码)
     * @param sName 结构化名称
     * @return 返回一个字符串(base64编码)
     */
    saveStructContentToString(sName: string): string;

    /**
     * 设置指定名称控件的Code和Value值（传数组，一次设多条code，value）
     * @param sName 结构化名称
     * @param value 设置的值 json
     * @param nType 结构化类型
     */
    setCompoundBoxCodeAndValueByArray(sName: string, sValue: string, nType?: number): number;

    /**
     * 根据指定下拉框的value值设置当前显示的code值
     * @param sName 结构化名称
     * @param value 设置的值
     * @param nType 结构化类型
     */
    setCompoundBoxCurrentCodeByValue(sName: string, value: string, nType?: number): number;

    /**
     * 删除指定名称控件的所有条目
     * @param sName 结构化名称
     * @param nType 结构化类型
     */
    deleteAllCompoundBoxCodeAndValue(sName: string, nType?: number): number;

    /**
     * 判断当前文档是否含有页眉
     */
    hasHeader(): boolean;

    /**
     * 删除当前文档的页眉
     */
    deleteHeader(): number;

    /**
     * 删除当前文档的页眉内容
     */
    deleteHeaderContent(sRev?: string): number;

    /**
     * 删除当前文档的页脚内容
     */
    deleteFooterContent(sRev?: string): number;

    /**
     * 判断当前文档是否含有页脚
     */
    hasFooter(): boolean;

    /**
     * 删除当前文档的页脚
     */
    deleteFooter(): number;

    /**
   * @api {function} none replaceHeader
   * @apiName replaceHeader
   * @apiGroup 4_headerfooterAPI
   * @apiVersion  1.0.0
   * @apiDescription replaceHeader(fileContent: Blob, sRev: string): number
   * <p>用传递进来的文件页眉替换当前文件的页眉。</p>
   * @apiparam {string} fileContent 传递进来的文件内容
   * @apiparam {string} sRev 预览参数（暂时无用）
   * @apiSuccessExample {number} Success:
   *  number
   */
    replaceHeader(fileContent: Blob, sRev: string): Promise<number>;

    /**
   * @api {function} none replaceFooter
   * @apiName replaceFooter
   * @apiGroup 4_headerfooterAPI
   * @apiVersion  1.0.0
   * @apiDescription replaceFooter(fileContent: Blob, sRev: string): number
   * <p>用传递进来的文件页脚替换当前文件的页脚。</p>
   * @apiparam {string} fileContent 传递进来的文件内容
   * @apiparam {string} sRev 预览参数（暂时无用）
   * @apiSuccessExample {number} Success:
   *  number
   */
    replaceFooter(fileContent: Blob, sRev: string): Promise<number>;

    /**
     * 设置页脚的文本内容，如果没有页脚不会自动插入页脚
     * @param strText 页脚内容 支持通配符$ 用来代表页码
     * @param nParaStyle 页脚内容的对齐方式
     * @param sRev1 预留参数
     */
    setFooterTextEx(strText: string, nParaStyle: number, sRev1?: string): number;

    /**
     * 光标跳到当前页的页眉开始位置。
     */
    jumpToHeader(): number;

    /**
     * 设置页眉页脚是否只读
     * @param bReadOnly 是否只读
     */
    setHeaderFooterReadOnly(bReadOnly: boolean): number;

    /**
     * 获取当前页的页眉文本内容
     */
    getHeaderText(): string;

    /**
     * 设置批注用户名
     * @param sName 用户名
     */
    setCommentAuthor(sName: string): number;


    /**
     * 激活末页动态高度
     * @param bShow
     */
    setDynamicHeightMode(bShow: boolean): number;

    /**
     * 激活Inline模式
     * @param flag
     */
    setInlineMode(flag: boolean): number;

    /**
     * 根据指定条件，用指定源文件里的的结构内容替换当前目标文档中的结构的内容。
     * @param sCurStructJson 当前文档中结构的Json
     * @param sSourceBase64String 源文件的json
     * @param sSourceStructJson 源文件的结构json
     */
    replaceSpecificStructPropWithBackString(sCurStructJson: string, sSourceStructJson: string,
                                            sSourceBase64String: string, title?: string): Promise<string>;

    /**
     * 根据指定条件，用指定源文件里的的结构内容替换当前目标文档中的结构的内容。
     * @param sCurStructJson 当前文档中结构的Json
     * @param sSourceBase64String 源文件的json
     * @param content 源文件的Blob
     */
    replaceSpecificStructPropWithBackStream(sCurStructJson: string, sSourceStructJson: string,
                                            content: Blob, title?: string): Promise<string>;

    replaceSpecificRegionPropWithBackStream(sCurRegionJson: string, sSourceRegionJson: string,
                                            content: Blob, title?: string): Promise<string>;

    // getSpecificStructContentWithBackString(sSourceStructJson: IStructJson[],
    //                                        sSourceBase64String: string): Promise<IStructContent[]>;

    // getSpecificStructContentWithBackStream(sSourceStructJson: IStructJson[],
    //                                        content: Blob, type: number): Promise<IStructContent[]>;

    /**
     * 当前光标位置插入一个签名控件，可以指定属性。
     * @param sName 签名控件名称
     * @param sJson json串值
     */
    insertSignControlAtCurrentCursor(sName: string, sJson: string): string | number;

    /**
     * 前光标位置插入一个签名控件，可以指定属性
     * @param sName 签名控件名称
     */
    getElementCountByName(sName: string): number;

    /**
     * 在签名控件里的指定签名子元素中签名（文本）
     * @param sName 签名控件名称
     * @param index 签名子元素的index
     * @param sText 签名文本
     */
    addSignTextToControl(sName: string, index: number, sText: string): number;

    /**
     * 在签名控件里的指定签名子元素中签名(图形)
     * @param sName 签名控件名称
     * @param index 签名子元素的index
     * @param sPicData 签名图片的base64串值
     */
    addSignPicToControlWithString(sName: string, index: number, sPicData: string): Promise<number>;

    /**
     * 在签名控件里的指定签名子元素中签名(图形)
     * @param sName
     * @param index
     * @param sPicData
     * @param sJson
     */
    addSignPicToControlWithString2(sName: string, index: number, sPicData: string, sJson?: string): Promise<number>;

    /**
     * 整个签名控件将被删除
     * @param sName 签名控件名称
     */
    deleteSignControlByName(sName: string): number;

    /**
     * 删除指定name的签名控件的内容
     * @param sName 签名控件名称
     * @param index 签名控件里的签名元素索引. 1-3 -- 索引 0 – 全部子元素删除
     */
    deleteSignContentByName(sName: string, index: number): number;

    /**
     * 获取整个文档中签名控件的个数
     */
    getSignControlCount(): number;

    /**
     * 获取指定区域中的签名控件的个数
     * @param sName 区域名
     */
    getSignControlCountInRegion(sName: string): number;

    /**
     * 获取指定区域中的签名控件的名称
     * @param sName 区域名
     */
    getSignControlNamesInRegion(sName: string): string[];

    /**
     * 获取全文档中的签名控件的名称
     */
    getSignControlNames(): string[];

    /**
     * @api {function} none setHeadersTextByJson
     * @apiName setHeadersTextByJson
     * @apiGroup 4_headerfooterAPI
     * @apiVersion 1.0.0
     * @apiDescription setHeadersTextByJson(sJson: string, sRev1: string): number
     * <p>通过JSON格式数据设置文档页眉中结构化元素的内容。可以同时设置多个页眉和多个结构化元素。</p>
     * @apiparam {string} sJson 页眉的JSON格式数据，格式为：{页码索引: [{SerialNumber: "结构化元素序列号", text: "要设置的文本内容"}, ...], ...}
     * @apiparam {string} sRev1 预留参数，暂未使用
     * @apiparamExample {json} sJson示例
     * {
     *   "1": [
     *     {
     *       "SerialNumber": "页眉医院名称",
     *       "text": "XX市人民医院"
     *     },
     *     {
     *       "SerialNumber": "标题",
     *       "text": "主治医师查房记录"
     *     }
     *   ]
     * }
     * @apiSuccessExample {number} Success:
     *  0 - 成功
     */
    setHeadersTextByJson(sJson: string, sRev1: string): number;

    /**
     * 设置页眉距离顶部的距离
     * @param headerFromTop 页眉距离顶部的距离，单位：毫米(mm)
     */
    setHeaderFromTop(headerFromTop: number): number;

    /**
     * 设置页脚距离底部的距离
     * @param footerFromBottom 页脚距离底部的距离，单位：毫米(mm)
     */
    setFooterFromBottom(footerFromBottom: number): number;

    /**
     * 获取页眉距离顶部的距离
     * @returns 页眉距离顶部的距离，单位：毫米(mm)
     */
    getHeaderFromTop(): number;

    /**
     * 获取页脚距离底部的距离
     * @returns 页脚距离底部的距离，单位：毫米(mm)
     */
    getFooterFromBottom(): number;

    /**
     * 当前光标位置的是否可以编辑
     */
    canEditInCurrentCursor(): boolean;

    /**
     * 文件监听器，影响nsoFileOpenCompleted事件
     */
    addFileListen(): number;

    /**
     * 结构化元素监听器，影响nsoStructChanged nsoStructClick nsoStructDBClick nsoStructGainFocus  nsoStructLostFocus 事件
     * @param nRev 预留参数，暂时没用
     */
    addStructListen(nRev?: number): number;

    /**
     * 键盘监听器 影响nsoKeyPressedEvent 事件
     * @param nRev 预留参数，暂时没用
     */
    addKeyListen(nRev?: number): number;

    /**
     * 移除File监听器
     */
    removeFileListen(): number;

    /**
     * 移除Struct监听器
     */
    removeStructListen(): number;

    /**
     * 移除键盘监听器
     */
    removeKeyListen(): number;

    /**
     * 移除所有的监听器。
     */
    removeAllListen(): number;

    /**
     * 开启自动缓存
     */
    enableAutoSave(): void;

    /**
     * 关闭自动缓存
     */
    disableAutoSave(): void;

    /**
     * 设置c端打印服务地址
     * @param url server url
     */
    setPrinterServerUrl(url: string): number;

    /**
     * 设置地址栏内容
     * @param name
     * @param sText
     */
     setAddressControlText(name: string, sText: string): number;

     /**
      * 获取地址栏内容
      * @param name
      */
     getAddressControlText(name: string): string;

    /**
     * 锁定文档内所有表格，不允许拖动表格行列宽线
     * @param bFixed
     */
    forbidMoveTableBorder(bFixed: boolean): number;

    /**
     * 返回指定name的签名控件里的元素name
     * @param sName 签名控件名称
     */
    getSignElementNames(sName: string): string;

    /**
     * 返回指定name的签名控件里的已经签名的元素name
     * @param sName 签名控件名称
     */
    getSignedElementNames(sName: string): string;

    /**
     * 在签名控件里的指定签名子元素中签名(图形),可以控制签名的大小
     * @param sName 签名控件名称
     * @param sJson 控制参数
     */
    addSignContentToControl(sName: string, sJson: string): Promise<number>;

    /**
     * 删除指定name的签名控件的内容
     * @param sName 签名控件名称
     * @param sJson 控制参数
     */
    deleteSignContent(sName: string, sJson: string): number;

    /**
     * 删除当前文档的页眉内容末尾的空行
     * @param sText 预留参数 ''
     */
    deleteRedundantInHeader(sText: string): number;

    /**
     * 移除指定签名里特定的字符。
     * @param sName 签名元素名称
     * @param sRemoveChar 指定字符
     */
    removeCertainCharInSignControl(sName: string, sRemoveChar: string): number;

    /**
     * emr模版保存校验：当页眉有表格的时候，判断第一个单元格第一个portion里的字体高度，然后看行高。
     * 如果行高不足以显示全这个字，返回错误代码让emr提示。
     */
    checkFirstTableRowHeightInHeader(sTableName: string): number;

    /*
     * 接受所有修订
     */
    acceptRevisions(): void;

    /**
     * 光标跳转到指定表格，指定单元格内，如果单元格内有内容，光标在单元格末尾
     * @param sTable table name
     * @param sCellName cell name
     */
    moveCursorToTableCell(sTable: string, sCellName: string): number;

    /**
     * false: 此接口调用后，后续对编辑器的操作都不会启用刷新机制。
     * true: 此接口调用将强行刷新一次。且刷新机制被重置
     * @param bLock 启用刷新机制
     */
    lockRefresh(bLock: boolean): number;

    /**
     * 是否开启拼写检查
     * @param bEnable 是否开启
     * @param bRefresh 是否进行马上检查，默认检查
     */
    enableSpellCheck(bEnable: boolean, bRefresh?: boolean): Promise<number>;

    /**
     * 控制光标在不可编辑的结构化元素上，是否显示禁止光标
     * @param bFlag
     */
    enableNotAllowedCursorStyle(bFlag: boolean): number;

    /**
     * 结构化元素: 标题后的文字继承默认字体格式可配置
     * @param bEnable 控制是否在结构化元素标题后保留缺省样式
     */
    enableDefaultStyleAfterTitle(bEnable: boolean): number;

    /**
     * 设置动态网格线是否显示, 显示的颜色或者类别
     * @param bEnable
     * @param sParam
     */
    setDynamicGridLine(bEnable: boolean, sParam?: string): number;

    /**
     * 提供文档现有的外部数据源绑定的json，值为空则赋""
     */
    getSourceBindJson(): string;

    /**
     * 通过json一次性刷新外部源的值。如果设置了只刷新一次
     * @param sJson
     */
    setSourceBindJson(sJson: string): number;

    enableSouceBindInRegion(sRegion:string,nControl:number):number;

    saveNISTableFile(): Promise<any[]>;

    /**
     * 表格列信息抽取, 返回该列的结构化元素信息
        包括元素id checkbox勾选属性，自定义属性，元素内容
     * @param sTable
     * @param colID 列序列
     */
    getTableColInfo(sTable: string, colID: string): string;

    /**
     * 获取指定表格指定行的结构化元素信息
     * @param sTableName 表格名称
     * @param nRowIndex 行索引，从1开始
     * @returns 该行中的结构化元素信息，格式与 getTableXmlInfoByParament 相同
     */
    getTableRowStructInfo(sTableName: string, nRowIndex: number): string;

    /**
     * 获取当前光标所在表格行的索引
     * @returns 当前光标所在表格行的索引，从1开始；如果光标不在表格内，则返回-1
     */
    getCurrentTableRowIndex(): number;

    /** 设置自定义工具栏 */
    setCustomToolbar(items: ICustomToolbarItem[]): number;

    /**
     * 新行数据追加、修改
     * @param sJson
     */
    updateNISTableByJson(sJson: string): number;

    /**
   * @api {function} none insertComment
   * @apiName insertComment
   * @apiGroup 85_revisionAPI
   * @apiVersion  1.0.0
   * @apiDescription insertComment(sJson: string): number
   * <p>当前光标处插入批注，返回插入批注的名称</p>
   * @apiparam {string} sJson 批注信息
   * @apiparamExample {json} sJson
   * {
   * userName:"批注作者",content:"批注内容"
   *}
   * @apiSuccessExample {javascript} Success:
   *   const sJson = {userName:"医生A",content:"主诉字数不足"};
   *   let sName = editor.insertComment(JSON.stringify(sJson));
   *   console.log("批注name:",sName);
   */
    insertComment(sJson: string): string;

    /**
   * @api {function} none deleteComment
   * @apiName deleteComment
   * @apiGroup 85_revisionAPI
   * @apiVersion  1.0.0
   * @apiDescription deleteComment(name:string): number
   * <p>删除批注</p>
   * @apiparam {string} name 批注name
   * @apiSuccessExample {javascript} Success:
   * let nResult = editor.deleteComment(name);//0 成功； 非0 失败
   * @apiExample  {javascript} Example:
   * //删除文档中所有的批注
   * let jsonData = editor.getAllComments();
   * jsonData.forEach(item => {
   * const name = item.name;
   * editor.deleteComment(name);
   *});
   *
   * //删除文档中指定作者的所有批注
   * let targetUserName = "张医生";
   * jsonData.forEach(item => {
   *    if (item.userName === targetUserName) {
   *        const name = item.name;
   *        editor.deleteComment(name);
   *    }
   * });
   */
    deleteComment(name: string): number;

    /**
   * @api {function} modifyComment(name:string,sJson:string):number modifyComment
   * @apiName modifyComment
   * @apiGroup 85_revisionAPI
   * @apiVersion  1.0.0
   * @apiDescription modifyComment(name: string,sJson: string): number
   * <p>修改批注内容</p>
   * @apiparam {string} name 批注name
   * @apiparam {json} sjon 批注内容
   * @apiparamExample {json} sJson
   * {
   * content:"批注内容"
   *}
   * @apiSuccessExample {javascript} Success:
   *  const sjson = {content:"批注内容"};
   *  editor.modifyComment("批注1",JSON.stringify(sJson)); //0 成功； 非0 失败
   */
    modifyComment(name: string, sJson: string): number;

    /**
   * @api {function} none getCommentContent
   * @apiName getCommentContent
   * @apiGroup 85_revisionAPI
   * @apiVersion  1.0.0
   * @apiDescription getCommentContent(name:string): string
   * <p>获取批注内容</p>
   * @apiparam {string} name 批注name
   * @apiSuccessExample {javascript} Success:
   *   let sResult = editor.getCommentContent('批注1');
   *   console.log("comment content is ",sResult);
   */
    getCommentContent(name: string): string;

     /**
   * @api {function} none getAllComments
   * @apiName getAllComments
   * @apiGroup 85_revisionAPI
   * @apiVersion  1.0.0
   * @apiDescription getAllComments(): string
   * <p>获取文档中的所有批注信息</p>
   * @apiSuccessExample {json} Success:
   * [
   *    {"userName":"默认用户","content":"111","name":"批注1","time":"2024-8-10 09:08"},
   *    {"userName":"默认用户","content":"222","name":"批注2","time":"2024-8-10 09:08"}
   *  ]
   */
    getAllComments(): string;

     /**
   * @api {function} none jumpToOneCommentByName
   * @apiName jumpToOneCommentByName
   * @apiGroup 85_revisionAPI
   * @apiVersion  1.0.0
   * @apiDescription jumpToOneCommentByName(name: string): number
   * <p>光标跳转到指定批注名称处</p>
   * @apiparam {string} name 批注name
   * @apiSuccessExample {javascript} Success:
   *  let nResult = editor.jumpToOneCommentByName("批注1");  //0 成功； 非0 失败
   */
    jumpToOneCommentByName(name: string): number;

     /**
   * @api {function} none addCommentReply
   * @apiName addCommentReply
   * @apiGroup 85_revisionAPI
   * @apiVersion  1.0.0
   * @apiDescription addCommentReply(name: string,sJson: string): number
   * <p>给批注添加回复</p>
   * @apiparam {string} name 批注name
   * @apiparam {json} sjon 批注回复内容json
   * @apiparamExample {json} sJson
   * {
   * userName:"回复人",content:"回复内容"
   *}
   * @apiSuccessExample {javascript} Success:
   *  let sJson = {userName:"回复人",content:"回复内容"};
   *  const sCurrentjson = editor.getCurrentCommentInfo();
   *  if( sCurrentjson.name !== ""){
   *    editor.addCommentReply(sCurrentjson.name,sJson); //0 成功； 非0 失败
   * }
   */
    addCommentReply(name: string, sJson: string): number;

     /**
   * @api {function} none getCurrentCommentInfo
   * @apiName getCurrentCommentInfo
   * @apiGroup 85_revisionAPI
   * @apiVersion  1.0.0
   * @apiDescription getCurrentCommentInfo(): string
   * <p>获取当前光标处的批注信息，返回值为json，类似插入批注的json</p>
   * @apiSuccessExample {javascript} Success:
   *  const sjson = editor.getCurrentCommentInfo();
   *  console.log("name=",sjson.name);
   *  console.log("userName=", sjson.userName);
   *  console.log("content=", sjson.content);
   *  console.log("time=",sjson.time);
   */
    getCurrentCommentInfo(): string;

     /**
   * @api {function} none showCommentPanel
   * @apiName showCommentPanel
   * @apiGroup 85_revisionAPI
   * @apiVersion  1.0.0
   * @apiDescription showCommentPanel(bShow: boolean): number
   * <p>是否显示批注面板</p>
   * @apiparam {string} bShow 1 显示 0 隐藏
   * @apiSuccessExample {javascript} Success:
   * let number = editor.showCommentPanel(true); //0 成功； 非0 失败
   */
    showCommentPanel(bShow: boolean): number;
}

export interface IExternalEvent {
    /**
     * 内联模式下文档高度变化事件
     * @param rectInfo 包含高度、宽度和光标位置的信息
     */
    inlineHeightChange?(rectInfo: {height: number, width: number, cursorX?: number, cursorY?: number, isInline?: boolean}): void;
    /**
     * 编辑器里面加载一个文档结束后将向上层发送的事件
     * @param sPath 打开的文档全路径
     * @param sReverve 预留，目前没用
     */
    nsoFileOpenCompleted?(sPath: string, sReverve?: string): void;

    /**
     * 编辑器内键盘按键按下的时候激发的事件。
     * @param nKeyCode 键盘的KeyCode
     * @param altKey ATL的按下值
     * @param ctrlKey CTRL的按下值
     * @param shiftKey SHIFT的按下值
     * @return 事件拦截，当返回false时，当前输入无效
     */
    nsoKeyPressedEvent?(nKeyCode: number, ctrlKey: boolean, shiftKey: boolean, altKey: boolean): boolean;

    /**
     * 鼠标单击结构化元素时产生的事件。
     * @param sName 结构化元素名称
     * @param type 结构化元素类型
     */
    nsoStructClick?(sName: string, type: number, position: any): void;

    /**
     * 鼠标双击结构化元素时产生的事件。
     * @param sName 结构化元素名称
     * @param type 结构化元素类型
     */
    nsoStructDBClick?(sName: string, type: number, position: any): void;

    /**
     * 结构化元素 获得编辑焦点，即光标在结构化元素 内，可以通过光标左右移动进入，也可以通过鼠标单击进入，从而产生该事件。
     * 只针对本结构，父结构不会触发。
     * @param sName 结构化名称
     * @param type 结构化元素类型
     */
    nsoStructGainFocus?(sName: string, type: number): void;

    /**
     * 结构化元素失去编辑焦点，即光标不在结构化元素内，可以通过光标左右移动退出，也可以通过鼠标单击其他地方，从而产生该事件。
     * 只针对本结构，父结构不会触发。
     * @param sName 结构化名称
     * @param type 结构化元素类型
     */
    nsoStructLostFocus?(sName: string, type: number): void;

    /**
     * 发生变化的结构化元素
     * @param sName 结构化名称
     * @param type 结构化元素类型
     */
    nsoStructChanged?(sName: string, type: number): void;

    /**
     * 区域点击操作符
     * @param sRegionName 区域名称
     */
    nsoRegionCustomEvent(sRegionName: string): void;
    /**
     * 区域点击操作符
     * @param x 横坐标
     * @param y 纵坐标
     */
    nsoRegionOperate(x: number, y: number): void;

    /**
     * 区域 获得编辑焦点，即光标在区域 内，可以通过光标左右移动进入，也可以通过鼠标单击进入，从而产生该事件。
     * @param sName 区域名称
     * @param type 区域类型
     */
    nsoRegionGainFocus?(sName: string, type: number): void;

    /**
     * 区域 失去编辑焦点，即光标在从区域内移动区域外，可以通过光标左右移动移出，也可以通过鼠标单击，从而产生该事件。
     * @param sName 区域名称
     * @param type 区域类型
     */
    nsoRegionLostFocus?(sName: string, type: number): void;

    /**
     * 区域 内容发生改变后产生的事件。
     * @param sName 区域名称
     * @param type 区域类型
     */
    nsoRegionChanged?(sName: string, type: number): void;

    /**
     * 区域鼠标双击产生的事件。
     * @param sName 区域名称
     * @param type 区域类型
     */
    nsoRegionDBClick?(sName: string, type: number): void;

    /**
     * Checkbox的check状态改变产生的事件
     * @param sName 结构化名称
     * @param bChecked 选中状态
     */
    nsoStructCheckChanged?(sName: string, bChecked: boolean): void;

    /**
     * 文档脏标记变化后产生的事件
     * @param bModified True – 文档被修改 False – 文档未被修改
     */
    nsoFileModifyChanged?(bModified: boolean): void;

    nisCellClickEvent?(rowId: string, cellId: string, type: number, position: IEventPosition): void;

    nisCellDBClickEvent?(rowId: string, cellId: string, type: number, position: IEventPosition): void;

    /**
     * 当护理表格单元格第一次获取焦点的时候，产生的事件。
     * @param rowId 行id
     * @param cellId 单元格id
     * @param type 单元格类型
     */
    nisCellGainFocusEvent?(rowId: string, cellId: string, type: number): void;

    /**
     * 当护理表格单元格失去焦点的时候，产生的事件。
     * @param rowId 行id
     * @param cellId 单元格id
     * @param type 单元格类型
     */
    nisCellLostFocusEvent?(rowId: string, cellId: string, type: number): void;

    /**
     * 关闭页面后，发送事件
     * @param type 怎么触发打印事件的：type: 1 -- 点打印关闭； 2 -- 直接不打印关闭
     */
    nsoSendPrintDataCompleted?(type?: number): void;

    /** 表格新建行后产生的事件
     * @param sName 表格名称
     */
    nsoRowInsertedInTable?(sName: string): void;

    /** 文档滚动到最底部时产生的事件 */
    nsoScrollReachEnd?(): void;

    /** 文档滚动离开最底部时产生的事件 */
    nsoScrollLeaveEnd?(): void;

    /**
     * 编辑器的下拉类型的元素的选择弹框消失后发送事件
     * 只有下拉类型的结构化元素会触发该事件，事件触发是编辑器的选择弹框消失后触发，可以通过事件的返回参数，调用接口获取当前选择的项值。
     */
    nsoStructItemSelected(sName: string): void;

    /**
     * 表格操作符
     * @param structJson 按照单元格从左到右，返回里面的结构化元素的类型以及name。[{name,type}]
     */
    nsoRowInsertedByTableAction(structJson: string): void;

    /**
     * 点击自定义菜单项，发送异步事件
     * @param id 自定义菜单id
     */
    nsoContextMenuEvent(id: string): void;

    /**
     * 选项按钮选择项变更事件
     * @param name 选项元素名称
     * @param checkedIndexes 选中的选项集合
     */
    nsoRadioButtonCheckChanged(sName: string, checkedIndexes: string): void;

    /**
     * 按钮点击事件
     * @param sName 按钮名称
     * @param option 按钮其他属性
     */
    nsoButtonClick(sName: string, option?: any): void;
    nsoCustomToolbarEvent(item: ICustomToolbarItem): void;

    /**
     * 当预览打印时，准备好打印的 HTML 数据后触发此事件。
     * @param htmlContent 打印的 HTML 内容字符串。
     */
    nsoPrinterDataEvent?(htmlContent: string): void;
}
