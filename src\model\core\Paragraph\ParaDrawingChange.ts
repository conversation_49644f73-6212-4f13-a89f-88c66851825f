import { ChangeBaseStringProperty, ChangeBaseDoubleProperty, ChangeBaseBoolProperty } from '../HistoryChange';
import { HistroyItemType } from '../HistoryDescription';
import ParaDrawing from './ParaDrawing';

export class ChangeGraphicObjectName extends ChangeBaseStringProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: ParaDrawing, old: string, news: string, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.GraphicObjectName;
    }

    public setValue(value: string): void {
        const paraDrawing = this.getClass();
        if ( paraDrawing ) {
            paraDrawing.graphicObjects?.drawingObjects?.delete(paraDrawing.name);
            paraDrawing.graphicObjects?.drawingObjects?.set(value, paraDrawing);
            paraDrawing.name = value;
        }
    }
}

export class ChangeGraphicObjectSrc extends ChangeBaseStringProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: ParaDrawing, old: string, news: string, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.GraphicObjectSrc;
    }

    public setValue(value: string): void {
        const paraDrawing = this.getClass();
        if ( paraDrawing ) {
            paraDrawing.src = value;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeGraphicObjectWidth extends ChangeBaseDoubleProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: ParaDrawing, old: number, news: number, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.GraphicObjectWidth;
    }

    public setValue( value: number ): void {
        const paraDrawing = this.getClass();
        if ( paraDrawing ) {
            paraDrawing.width = value;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeGraphicObjectHeight extends ChangeBaseDoubleProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: ParaDrawing, old: number, news: number, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.GraphicObjectHeight;
    }

    public setValue( value: number ): void {
        const paraDrawing = this.getClass();
        if ( paraDrawing ) {
            paraDrawing.height = value;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeGraphicObjectSizeLocked extends ChangeBaseBoolProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: ParaDrawing, old: boolean, news: boolean, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.GraphicObjectSizeLocked;
    }

    public setValue( value: boolean ): void {
        const paraDrawing = this.getClass();
        if ( paraDrawing ) {
            paraDrawing.sizeLocked = value;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeGraphicObjectCopyProtect extends ChangeBaseBoolProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: ParaDrawing, old: boolean, news: boolean, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.GraphicObjectCopyProtect;
    }

    public setValue( value: boolean ): void {
        const paraDrawing = this.getClass();
        if ( paraDrawing ) {
            paraDrawing.sizeLocked = value;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeGraphicObjectDeleteLocked extends ChangeBaseBoolProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: ParaDrawing, old: boolean, news: boolean, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.GraphicObjectDeleteLocked;
    }

    public setValue( value: boolean ): void {
        const paraDrawing = this.getClass();
        if ( paraDrawing ) {
            paraDrawing.deleteLocked = value;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeGraphicObjectPreserveAspectRatio extends ChangeBaseBoolProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: ParaDrawing, old: boolean, news: boolean, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.GraphicObjectPreserveAspectRatio;
    }

    public setValue( value: boolean ): void {
        const paraDrawing = this.getClass();
        if ( paraDrawing ) {
            paraDrawing.preserveAspectRatio = value;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeGraphicObjectSvgElem extends ChangeBaseStringProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: ParaDrawing, old: string, news: string, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.GraphicObjectSvgElem;
    }

    public setValue(value: string): void {
        const paraDrawing = this.getClass();
        if ( paraDrawing ) {
            paraDrawing.equationElem = value;
        }
    }
}
