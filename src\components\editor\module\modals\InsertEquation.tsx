import * as React from 'react';
import Dialog from '../../ui/Dialog';
import Input from '../../ui/Input';
import Button from '../../ui/Button';
import Select from '../../ui/select/Select';

import {EquationType, isValidName } from '../../../../common/commonDefines';
import {message} from '../../../../common/Message';
import { EquationRefs } from '../../../../common/MedEquation';
import { IFRAME_MANAGER } from '../../../../common/IframeManager';

interface IDialogProps {
    documentCore: any;
    visible: boolean;
    id: string;
    close: (name: string | number, bRefresh?: boolean) => void;
    property?: any;
    isEditing?: boolean;
}

interface IState {
    bRefresh: boolean;
    equation?: IEquation;
}

interface IType {
    key: string;
    value: number;
    src: string;
}

interface IEquation {
    name: string;
    type: number;
    src: string;
}

export default class InsertEquation extends React.Component<IDialogProps, IState> {
    private visible: boolean;
    private types: IType[];
    private equation: IEquation;
    private equationRefs: EquationRefs;

    constructor(props: IDialogProps) {
        super(props);
        this.state = {
            bRefresh: false,
        };
        this.visible = this.props.visible;
        this.types = [
            {
                key: '月经史',
                value: EquationType.Menstruation,
                src: require('../../../../common/resources/men-rev.png').default,
            },
            {
                key: '月经史2',
                value: EquationType.Menstruation2,
                src: require('../../../../common/resources/menstruation2.png').default,
            },
            {
                key: '月经史3',
                value: EquationType.Menstruation3,
                src: require('../../../../common/resources/menstruation3.png').default,
            },
            {
                key: '月经史4',
                value: EquationType.Menstruation4,
                src: require('../../../../common/resources/menstruation4.png').default,
            },
            {
                key: 'PD牙位图',
                value: EquationType.ToothBitMap,
                src: require('../../../../common/resources/toothBitMap.png').default,
            },
            {
                key: '瞳孔图',
                value: EquationType.PupilMapping,
                src: require('../../../../common/resources/PupilMapping.png').default,
            },
            {
                key: '胎心图',
                value: EquationType.FetalHeartChart,
                src: require('../../../../common/resources/FetalHeartChart.png').default,
            },
            {
                key: '光定位图',
                value: EquationType.LightPositioningMap,
                src: require('../../../../common/resources/LightPositioningMap.png').default,
            },
            {
                key: '分式',
                value: EquationType.Fraction,
                src: require('../../../../common/resources/fraction-rev.png').default,
            },
            {
                key: 'Z/P 牙位记录法',
                value: EquationType.Ordinary,
                src: require('../../../../common/resources/dental.png').default,
            },
            {
                key: '标尺',
                value: EquationType.Rule,
                src: require('../../../../common/resources/rule.png').default,
            },
            {
                key: '病变上牙牙位图',
                value: EquationType.DiseasedUpperTeeth,
                src: require('../../../../common/resources/diseased-upper-teeth.png').default,
            },
            {
                key: '病变下牙牙位图',
                value: EquationType.DiseasedLowerTeeth,
                src: require('../../../../common/resources/diseased-lower-teeth.png').default,
            },
            {
                key: '恒牙牙位图',
                value: EquationType.PermanentToothBitmap,
                src: require('../../../../common/resources/permanent-tooth-bitmap.png').default,
            },
            {
                key: '乳牙牙位图',
                value: EquationType.DeciduousToothBitmap,
                src: require('../../../../common/resources/deciduous-tooth-bitmap.png').default,
            },
        ];
        const isEditingMode = this.props.isEditing;
        let initialEquation: IEquation;
        if (isEditingMode && this.props.property) {
            const editProp = this.props.property;
            const initialTypeData = this.types.find(t => t.value === editProp.type) || this.types[0];
            initialEquation = {
                name: editProp.name,
                type: editProp.type,
                src: initialTypeData.src
            };
        } else {
            const defaultTypeData = this.types.find(t => t.value === EquationType.Menstruation) || this.types[0];
            initialEquation = {
                name: '月经史',
                type: defaultTypeData.value,
                src: defaultTypeData.src
            };
        }
        this.equation = initialEquation;
    }

    public render(): any {
        const title = this.props.isEditing ? '编辑医学表达式' : '插入医学表达式';
        return (
            <Dialog
                visible={this.visible}
                width={400}
                title={title}
                open={this.open}
                footer={this.renderFooter()}
                id={this.props.id}
                close={this.close}
            >
                <div>
                    <div className='editor-line'>
                        <div className='w-70'>名称</div>
                        <div className='right-auto'>
                            <Input
                                name='name'
                                value={this.equation.name}
                                onChange={this.onChange}
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <div className='w-70'>种类</div>
                        <div className='right-auto'>
                            <Select
                                name='type'
                                data={this.types}
                                value={this.equation.type}
                                onChange={this.typeChange}
                                disabled={this.props.isEditing}
                            />
                        </div>
                    </div>
                    <div className='editor-line editor-multi-line'>
                        <div className='w-70'>预览</div>
                        <div className='right-auto'>
                            <img width='265'  src={this.equation.src} />
                        </div>
                    </div>
                </div>
            </Dialog>
        );
    }

    public componentDidMount(): void {
        this.setState({ bRefresh: !this.state.bRefresh });
        if (!this.props.isEditing && this.props.documentCore) {
            this.open();
        }
    }

    public UNSAFE_componentWillReceiveProps(nextProps: IDialogProps): void {
        let stateNeedsUpdate = false;

        if (this.props.visible !== nextProps.visible) {
            this.visible = nextProps.visible;
            stateNeedsUpdate = true;

            if (nextProps.visible && !this.props.visible) {
                const isEditingMode = nextProps.isEditing;
                if (isEditingMode && nextProps.property) {
                    const editProp = nextProps.property;
                    const initialTypeData = this.types.find(t => t.value === editProp.type) || this.types[0];
                    this.equation = {
                        name: editProp.name,
                        type: editProp.type,
                        src: initialTypeData.src
                    };
                } else {
                    const defaultTypeData = this.types.find(t => t.value === EquationType.Menstruation) || this.types[0];
                    this.equation = {
                        name: nextProps.documentCore ? nextProps.documentCore.makeUniqueImageName(12) : '公式',
                        type: defaultTypeData.value,
                        src: defaultTypeData.src
                    };
                }
            }
        }

        if ((this.props.property !== nextProps.property || this.props.isEditing !== nextProps.isEditing) && nextProps.visible) {
            const isEditingMode = nextProps.isEditing;
            if (isEditingMode && nextProps.property) {
                const editProp = nextProps.property;
                const initialTypeData = this.types.find(t => t.value === editProp.type) || this.types[0];
                if (this.equation.name !== editProp.name || this.equation.type !== editProp.type) {
                    this.equation = {
                        name: editProp.name,
                        type: editProp.type,
                        src: initialTypeData.src
                    };
                    stateNeedsUpdate = true;
                }
            } else if (!isEditingMode) {
                const defaultTypeData = this.types.find(t => t.value === EquationType.Menstruation) || this.types[0];
                if (!this.equation || this.props.isEditing) {
                    this.equation = {
                        name: nextProps.documentCore ? nextProps.documentCore.makeUniqueImageName(12) : '公式',
                        type: defaultTypeData.value,
                        src: defaultTypeData.src
                    };
                    stateNeedsUpdate = true;
                }
            }
        }

        if (stateNeedsUpdate) {
            this.setState({ bRefresh: !this.state.bRefresh });
        }
    }

    private renderFooter(): any {
        return (
            <span>
                <Button onClick={() => this.close(false)}>取消</Button>
                <Button type='primary' onClick={this.confirm}>确认</Button>
            </span>
        );
    }

    private open = (): void => {
        if (this.props.documentCore) {
            if (!this.props.isEditing || !this.equation?.name) {
                this.equation.name = this.props.documentCore.makeUniqueImageName(12);
                this.setState({ bRefresh: !this.state.bRefresh });
            }
        }
    }

    private close = (bRefresh?: boolean): void => {
        this.visible = false;
        this.props.close(this.props.id, bRefresh);
    }

    private confirm = (): void => {
        const documentCore = this.props.documentCore;
        const equation = this.equation;
        IFRAME_MANAGER.setDocId(documentCore.getCurrentId());
        if (!isValidName(equation.name)) {
            message.error('名称不符合规范，请重新输入');
            return;
        }
        const isEditing = this.props.isEditing;
        const originalName = isEditing && this.props.property ? this.props.property.name : null;

        if ((isEditing && equation.name !== originalName) || !isEditing) {
            if (!documentCore.checkUniqueImageName(equation.name)) {
                message.error('已存在该名字，请重新命名');
                return;
            }
        }

        if (!this.equationRefs) {
            this.equationRefs = new EquationRefs();
        }
        
        // 检查是否为月经公式，并获取日期选项
        let initialDateEnabled = false;
        let lastDateEnabled = false;
        
        if (equation.type === EquationType.Menstruation) {
            // 尝试从DOM中获取日期选项状态
            try {
                const menEquationForm = document.querySelector('form.menstruation-equation-form');
                if (menEquationForm) {
                    const initialDateCheckbox = menEquationForm.querySelector('#menstruation-date-initial') as HTMLInputElement;
                    const lastDateCheckbox = menEquationForm.querySelector('#menstruation-date-last') as HTMLInputElement;
                    
                    initialDateEnabled = initialDateCheckbox ? initialDateCheckbox.checked : false;
                    lastDateEnabled = lastDateCheckbox ? lastDateCheckbox.checked : false;
                    
                   
                }
            } catch (e) {
                
            }
        }
        
        // 传递日期选项参数
        const dateOptions = { 
            useDate: initialDateEnabled || lastDateEnabled, 
            initialDateEnabled, 
            lastDateEnabled 
        };
       
        // 获取公式尺寸和SVG元素，传递日期选项参数
        const size = equation.type === EquationType.Menstruation 
            ? this.equationRefs.getEquationSizeByType(equation.type, dateOptions)
            : this.equationRefs.getEquationSizeByType(equation.type);
            
       
        
        const svgDom = equation.type === EquationType.Menstruation 
            ? this.equationRefs.getEquationByType(equation.type, dateOptions)
            : this.equationRefs.getEquationByType(equation.type);
            
        const imageDom = svgDom.cloneNode(true) as any;
        
        // 如果是月经公式，确保hidden-values元素包含正确的类
        if (equation.type === EquationType.Menstruation) {
            try {
                const hiddenValues = imageDom.querySelector('.hidden-values');
                if (hiddenValues) {
                    if (initialDateEnabled) {
                        hiddenValues.classList.add('initialDateEnabled');
                    } else {
                        hiddenValues.classList.remove('initialDateEnabled');
                    }
                    
                    if (lastDateEnabled) {
                        hiddenValues.classList.add('lastDateEnabled');
                    } else {
                        hiddenValues.classList.remove('lastDateEnabled');
                    }
                    
                   
                }
            } catch (e) {
                
            }
        }
        const svgConvertedURI = documentCore.getDrawingObjects()
            .convertSVGToImageString(imageDom);

        this.checkEquationSizeLimit(size);

        let res = '';
        if (isEditing) {
            console.warn('Equation update logic requires implementation. Using placeholder.');
            res = equation.name;
        } else {
            res = documentCore.addInlineImage(size.width, size.height, svgConvertedURI,
                equation.name, equation.type, imageDom);
        }

        this.close(res !== '');
    }

    private typeChange = (value: number, name: string, item: IType): void => {
        this.equation.src = item.src;
        this.equation.type = value;
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private onChange = (value: any, name: string): void => {
        this.equation[name] = value;
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private checkEquationSizeLimit(size: {height: number, width: number}): void {
        if (size != null) {
            const documentCore = this.props.documentCore;
            const maxWidth = documentCore.getMaxWidth(true);
            const maxHeight = documentCore.getMaxHeight(true);

            if (size.width > maxWidth) {
                const wRatio = maxWidth / size.width;
                const hRatio = maxHeight / size.height;
                if (size.height * wRatio > maxHeight) {
                    size.height = maxHeight;
                    size.width *= hRatio;
                } else {
                    size.width = maxWidth;
                    size.height *= wRatio;
                }
            } else if (size.height > maxHeight) {
                const wRatio = maxWidth / size.width;
                const hRatio = maxHeight / size.height;
                if (size.width * hRatio > maxWidth) {
                    size.width = maxWidth;
                    size.height *= wRatio;
                } else {
                    size.height = maxHeight;
                    size.width *= wRatio;
                }
            }
        }
    }
}
