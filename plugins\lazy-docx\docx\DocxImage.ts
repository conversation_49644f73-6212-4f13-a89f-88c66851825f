import { ISerialImageObj, SerialObjType } from '../../serialize/serialInterface';
import { ImageRun, ParagraphChild } from 'docx';
import { IAbstractDocx, IDocx } from './IDocx';

export default class DocxImage extends IAbstractDocx implements IDocx {
    constructor(private readonly imgObj: ISerialImageObj) { super(); }

    public buildTo(collector: ParagraphChild[] = []): ParagraphChild[] {
        if (this.imgObj.type !== SerialObjType.Image) {
            return collector;
        }
        const imgOption: any = {};
        imgOption.data = this.transImageSrc();
        imgOption.transformation = this.imgObj.transformation;
        const image = new ImageRun(imgOption);
        collector.push(image);
        return collector;
    }

    /** 根据图片内容，将非图片元素转换为图片 */
    private transImageSrc(): string {
        const img = this.imgObj;
        // tslint:disable-next-line: one-variable-per-declaration
        const width = img.transformation.width,
              height = img.transformation.height;
        // svg 转换为图片内容
        if (/data:image\/svg\+xml/i.test(img.data) && window && window.document) {
            const docDom = window.document;
            // 设置画布大小，填充图片
            const canvas = docDom.createElement('canvas');
            canvas.height = height;
            canvas.width = width;
            const svgImg = new Image();
            svgImg.src = img.data;
            const canvasCtx = canvas.getContext('2d');
            canvasCtx.drawImage(svgImg, 0, 0, width, height);
            return canvas.toDataURL('image/png');
        }
        return img.data;
    }
}
