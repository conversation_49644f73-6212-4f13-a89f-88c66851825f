import { ToolbarIndex } from './commonDefines';
import {GlobalEvent as gEvent, GlobalEventName as gEventName} from './GlobalEvent';
import { IFRAME_MANAGER } from './IframeManager';

export default class EditorEvent {
    private editorContainer: HTMLElement;
    private _id: number;
    private _host: any;
    private _scale: number;
    private bFromWin: boolean;
    constructor(host: any) {
        this.editorContainer = host.myRef.current.firstChild;
        this._host = host;
        this._id = host.docId;
        this._scale = 1;
        this.addEvent();
    }

    private addEvent(): void {
        const editorContainerNode = this.editorContainer;
        editorContainerNode.addEventListener('mousedown', this.handleMouseDown, false);
        editorContainerNode.addEventListener('mousemove', this.handleMouseMove, false);
        editorContainerNode.addEventListener('mouseup', this.handleMouseUp, false);
        editorContainerNode.addEventListener('visibilitychange', this.handleWindowVisible);
        editorContainerNode.addEventListener('dblclick', this.handleDoubleClick);
        editorContainerNode.addEventListener('click', this.handleClick);
        // document.body.style.transformOrigin = '0px 0px 0px';
        // let scale = 1;
        // console.dir(editorContainerNode.ownerDocument.defaultView)
        editorContainerNode.addEventListener('mousewheel', this.handleMousewheel, false);
        editorContainerNode.ownerDocument.addEventListener('keydown', this.keydown);
        editorContainerNode.addEventListener('keyup', this.stopPropagation);
        editorContainerNode.addEventListener('input', this.stopPropagation);
        editorContainerNode.parentNode.parentNode.addEventListener('mousewheel', this.preventDefault);
        editorContainerNode.ownerDocument.defaultView.addEventListener('blur', this.onBlur);
        gEvent.addEvent(this._id, gEventName.UnMounted, this.removeEvent);
        const win = IFRAME_MANAGER.getWindow(this._id);
        if (win) {
            win.addEventListener('focus', this.onFocus);
        }
        editorContainerNode.ownerDocument.addEventListener('click', this.winClick);
    }

    private removeEvent = (): void => {
        const editorContainerNode = this.editorContainer;
        if (!editorContainerNode) {
            console.log('undelete event globalEvent');
            return;
        }
        editorContainerNode.removeEventListener('mousedown', this.handleMouseDown);
        editorContainerNode.removeEventListener('mousemove', this.handleMouseMove);
        editorContainerNode.removeEventListener('mouseup', this.handleMouseUp);
        editorContainerNode.removeEventListener('visibilitychange', this.handleWindowVisible);
        editorContainerNode.removeEventListener('dblclick', this.handleDoubleClick);
        editorContainerNode.removeEventListener('click', this.handleClick);
        editorContainerNode.ownerDocument?.removeEventListener('keydown', this.keydown);
        editorContainerNode.removeEventListener('keyup', this.stopPropagation);
        editorContainerNode.removeEventListener('input', this.stopPropagation);
        editorContainerNode.parentNode?.parentNode?.removeEventListener('mousewheel', this.preventDefault);
        editorContainerNode.ownerDocument?.defaultView?.removeEventListener('blur', this.onBlur);
        gEvent.deleteEvent(this._id, gEventName.UnMounted, this.removeEvent);
        editorContainerNode.ownerDocument?.removeEventListener('click', this.winClick);
        const win = IFRAME_MANAGER.getWindow(this._id);
        if (win) {
            win.removeEventListener('focus', this.onFocus);
        }
    }

    private onBlur = (e: any): void => {
        // console.log(222)
        gEvent.setEvent(this._id, gEventName.Blur, e);
    }

    private winClick = (e: any): void => {
        gEvent.setEvent(this._id, gEventName.WindowClick, e);
    }

    private onFocus = (e: any): void => {
        // console.log(this._id);
        const bHidden = 'hidden' in document ? 'hidden' : null;
        if (!document[bHidden] && this._host.host.canSetCursorVisible()) {
            this._host.host.setCursorTrueVisible(!this._host.host.getSelected());
        }
    }

    private handleMousewheel = (e: any) => {
        if (e.ctrlKey === true || e.metaKey === true) {
            const bUpScroll = e.deltaY < 0;
            if (bUpScroll === true) {
                this._scale = 0.1;
            } else {
                this._scale = -0.1;
            }

            this.handleScale();
            e.preventDefault();
        }
    }

    private handleScale = (): void => {
        this._host.setViewScale(this._scale);
        // e.preventDefault();
        this._host.host.refresh();
    }

    private handleMouseDown = (e: any): void => {
        gEvent.setEvent(this._id, gEventName.Mousedown, e);
        e.stopPropagation();
    }

    private handleMouseMove = (e: any): void => {
        gEvent.setEvent(this._id, gEventName.Mousemove, e);
        e.stopPropagation();
    }

    private handleMouseUp = (e: any): void => {
        gEvent.setEvent(this._id, gEventName.Mouseup, e);
        e.stopPropagation();
    }

    private handleWindowVisible = (e: any): void => {
        gEvent.setEvent(this._id, gEventName.VisibilityChange, e);
    }

    private handleDoubleClick = (e: any): void => {
        gEvent.setEvent(this._id, gEventName.Dblclick, e);
        e.stopPropagation();
    }

    private handleClick = (e: any): void => {
        gEvent.setEvent(this._id, gEventName.Click, e);
        e.stopPropagation();
    }

    private keydown = (event: any): void => {
        if (event.ctrlKey === true && event.keyCode === 70) {
            gEvent.setEvent(this._id, gEventName.Search);
            event.preventDefault();
            event.stopPropagation();
            return;
        }
        if (event.keyCode === 27) {
            gEvent.setEvent(this._id, gEventName.Search, {visible: false});
            event.preventDefault();
            event.stopPropagation();
            return;
        }
        event.stopPropagation();
    }

    private stopPropagation = (e: any): void => {
        e.stopPropagation();
    }

    private preventDefault = (e: any): void => {
        if (e.ctrlKey === true || e.metaKey === true) {
            e.preventDefault();
            e.stopPropagation();
        }
    }
}
