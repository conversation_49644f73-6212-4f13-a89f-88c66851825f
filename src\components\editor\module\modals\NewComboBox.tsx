import * as React from 'react';
import Dialog from '../../ui/Dialog';
import { INewControlProperty, NewControlType,
    NewControlDefaultSetting,
    CodeValueItem,
    CustomPropertyElementType, isValidName, AlignType} from '../../../../common/commonDefines';
import Input from '../../ui/Input';
import Checkbox from '../../ui/CheckboxItem';
import Button from '../../ui/Button';
import Select from '../../ui/select/Select';
import CustomProperty from './CustomProperty';
import NewComboBoxList from './NewComboBoxList2';
import { message } from '../../../../common/Message';
import { IFRAME_MANAGER } from '../../../../common/IframeManager';
import CascadeBtn from './NewCascade';
import { ExternalDataBind } from './ExternalDataBind';

interface IDialogProps {
    documentCore: any;
    visible: boolean;
    id: string;
    property?: INewControlProperty;
    close: (name: string | number, bRefresh?: boolean) => void;
    type?: number;
}

interface IState {
    bRefresh: boolean;
}

export default class NewComboxBox extends React.Component<IDialogProps, IState> {
    private newControl: INewControlProperty;
    private bCustomProperty: boolean;
    private visible: any;
    private types: any[];
    private multiData: any[];
    private isNewControlTypeDisable: boolean;
    private newControlType: NewControlType;
    private docId: number;
    private bCascade: boolean;
    private alignments: any[];
    private resetSourceBind: boolean;
    private dataBind: any;
    constructor(props: any) {
        super(props);
        this.state = {
            bRefresh: false,
        };
        this.visible = this.props.visible;
        this.newControl = {newControlName: undefined};
        this.isNewControlTypeDisable = false;
        this.newControlType = NewControlType.Combox;
        this.types = [
            {key: '组合框', value: NewControlType.Combox},
            {key: '列表框', value: NewControlType.ListBox},
        ];
        this.multiData = [
            {key: '单选', value: false},
            {key: '多选', value: true},
        ];
        this.docId = this.props.documentCore.getCurrentId();
        this.alignments = [
            {
                key: '默认',
                value: AlignType.Left,
            },
            // {
            //     key: '右对齐',
            //     value: AlignType.Right,
            // },
            {
                key: '居中对齐',
                value: AlignType.Center,
            },
        ];
    }

    public render(): any {
        return (
            <Dialog
                visible={this.visible}
                width={380}
                open={this.open}
                title={this.getTitle()} // '单选/多选框'
                footer={this.renderFooter()}
                id={this.props.id}
            >
                <div>
                    <div className='editor-line'>
                        <span className='title'>常规</span>
                    </div>
                    <div className='editor-line'>
                        <div className='w-70'>名称</div>
                        <div className='right-auto'>
                            <Input
                                name='newControlName'
                                value={this.newControl.newControlName}
                                onChange={this.onChange}
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <div className='w-70'>内部名称</div>
                        <div className='right-auto'>
                            <Input
                                name='newControlSerialNumber'
                                value={this.newControl.newControlSerialNumber}
                                onChange={this.onChange}
                                readonly={true}
                                placeholder={''}
                                disabled={true}
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <div className='w-70'>标识符</div>
                        <div className='right-auto'>
                            <Input
                                name='identifier'
                                value={this.newControl.identifier}
                                onChange={this.onChange}
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <div className='w-70'>提示符</div>
                        <div className='right-auto'>
                            <Input
                                name='newControlInfo'
                                value={this.newControl.newControlInfo}
                                onChange={this.onChange}
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <div className='w-70'>占位符</div>
                        <div className='right-auto'>
                            <Input
                                name='newControlPlaceHolder'
                                value={this.newControl.newControlPlaceHolder}
                                onChange={this.onChange}
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <div className='w-70'>标题</div>
                        <div className='right-auto'>
                            <Input
                                name='newControlTitle'
                                value={this.newControl.newControlTitle}
                                onChange={this.onChange}
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <div className='w-70'>类型</div>
                        <div className='right-auto '>
                            <div className='w-050'>
                                <Select
                                    data={this.types}
                                    value={this.newControlType}
                                    name='newControlType'
                                    disabled={true}
                                    onChange={this.typeChange}
                                />
                            </div>
                            <div className='w-050'>
                                <Select
                                    data={this.multiData}
                                    name='isNewControlTypeDisable'
                                    value={this.isNewControlTypeDisable}
                                    disabled={true}
                                    onChange={this.typeChange}
                                />
                            </div>
                        </div>
                    </div>
                    <div className='editor-line editor-multi-line'>
                        <span className='title w-70'>属性</span>
                        <div className='right-auto newcontrol-prop'>
                            <div className='editor-line'>
                                <div className='w-050'>
                                    <Checkbox
                                        name='isNewControlHidden'
                                        value={this.newControl.isNewControlHidden}
                                        onChange={this.onChange}
                                    >
                                        隐藏
                                    </Checkbox>
                                </div>
                                <div className='w-050'>
                                    <Checkbox
                                        name='isNewControlCanntDelete'
                                        value={this.newControl.isNewControlCanntDelete}
                                        onChange={this.onChange}
                                    >
                                        禁止删除
                                    </Checkbox>
                                </div>
                            </div>

                            <div className='editor-line'>
                                <div className='w-050'>
                                    <Checkbox
                                        name='isNewControlCanntEdit'
                                        value={this.newControl.isNewControlCanntEdit}
                                        onChange={this.onChange}
                                    >
                                        禁止编辑
                                    </Checkbox>
                                </div>
                                <div className='w-050'>
                                    <Checkbox
                                        name='isNewControlMustInput'
                                        value={this.newControl.isNewControlMustInput}
                                        onChange={this.onChange}
                                    >
                                        必填项
                                    </Checkbox>
                                </div>
                            </div>

                            <div className='editor-line'>
                                <div className='w-050'>
                                    <Checkbox
                                        name='isNewControlShowBorder'
                                        value={this.newControl.isNewControlShowBorder}
                                        onChange={this.onChange}
                                    >
                                        显示边框
                                    </Checkbox>
                                </div>

                                <div className='w-050'>
                                    <Checkbox
                                        name='isNewControlReverseEdit'
                                        value={this.newControl.isNewControlReverseEdit}
                                        onChange={this.onChange}
                                    >
                                        反向编辑
                                    </Checkbox>
                                </div>
                            </div>

                            <div className='editor-line'>
                                <div className='w-050'>
                                    <Checkbox
                                        name='isNewControlHiddenBackground'
                                        value={this.newControl.isNewControlHiddenBackground}
                                        onChange={this.onChange}
                                    >
                                        隐藏背景色
                                    </Checkbox>
                                </div>
                                <div className='w-050'>
                                    <Checkbox
                                        name='retrieve'
                                        value={this.newControl.retrieve}
                                        onChange={this.onChange}
                                    >
                                        索引检查
                                    </Checkbox>
                                </div>
                            </div>
                            <div className='editor-line'>
                                <div className='w-050'>
                                    <Checkbox
                                        name='tabJump'
                                        value={this.newControl.tabJump}
                                        onChange={this.onChange}
                                    >
                                        键盘跳转
                                    </Checkbox>
                                </div>
                                <div className='w-050'>
                                    <Checkbox
                                        name='bShowCodeAndValue'
                                        value={this.newControl.bShowCodeAndValue}
                                        onChange={this.onChange}
                                    >
                                        展现属性值
                                    </Checkbox>
                                </div>
                            </div>
                            <div className='editor-line'>
                                <div className='w-050'>
                                    <Checkbox
                                        name='bTextBorder'
                                        value={this.newControl.bTextBorder}
                                        onChange={this.onChange}
                                    >
                                        带框字符
                                    </Checkbox>
                                </div>
                                <div className='w-050'>
                                    <Checkbox
                                        name='printSelected'
                                        value={this.newControl.printSelected}
                                        onChange={this.onChange}
                                    >
                                        打印时隐藏
                                    </Checkbox>
                                </div>
                            </div>
                            <div className='editor-line'>
                                <CustomProperty
                                    name='customProperty'
                                    properties={this.newControl.customProperty}
                                    documentCore={this.props.documentCore}
                                    onChange={this.onChange}
                                    close={this.onClose}
                                    id='bCustomProperty'
                                    visible={this.bCustomProperty}
                                    type={CustomPropertyElementType.NewControl}
                                />
                            </div>
                        </div>

                        <div className='editor-line'>
                            <span className='title'>下拉选项</span>
                        </div>
                        <NewComboBoxList
                            value={this.newControl.newControlItems}
                            docId={this.docId}
                            onChange={this.onChange}
                            name='newControlItems'
                            codeLabel={this.newControl.codeLabel}
                            valueLabel={this.newControl.valueLabel}
                        />
                        <div className='editor-line'>
                            <span className='title'>固有属性</span>
                        </div>
                        <div className='editor-line'>
                            <span className='w-050'>选中项前缀字符：</span>
                            <div className='w-050'>
                                <Input
                                    value={this.newControl.selectPrefixContent}
                                    name='selectPrefixContent'
                                    onChange={this.onChange}
                                />
                            </div>
                        </div>
                        <div className='editor-line'>
                            <span className='w-050'>未选中项前缀字符：</span>
                            <div className='w-050'>
                                <Input
                                    value={this.newControl.prefixContent}
                                    name='prefixContent'
                                    onChange={this.onChange}
                                />
                            </div>
                        </div>
                        <div className='editor-line'>
                            <span className='w-050'>内容分隔符：</span>
                            <div className='w-050'>
                                <Input
                                    value={this.newControl.separator}
                                    name='separator'
                                    onChange={this.onChange}
                                />
                            </div>
                        </div>
                        <div className='editor-line'>
                            <Checkbox
                                name='isShowValue'
                                value={this.newControl.isShowValue}
                                onChange={this.onChange}
                            >
                                显示value值
                            </Checkbox>
                        </div>
                        <div className='editor-line'>
                            <div className='w-70'>
                                级联
                            </div>
                            <div className='right-auto'>
                                <CascadeBtn
                                    visible={this.bCascade}
                                    id='bCascade'
                                    controlName={this.newControl.newControlName}
                                    documentCore={this.props.documentCore}
                                    properties={this.newControl.cascade}
                                    name='cascade'
                                    list={this.getCasedeList}
                                    close={this.onClose}
                                    onChange={this.onChange}
                                    type={this.newControl.newControlType}
                                />
                            </div>
                        </div>
                        <div className='editor-line'>
                            <div className='w-70'>
                                内部对齐
                            </div>
                            <div className='right-auto'>
                                <Select
                                    name='alignments'
                                    onChange={this.onChange}
                                    data={this.alignments}
                                    value={this.newControl.alignments}
                                />
                            </div>
                        </div>
                        <div className='editor-line'>
                        <div className='w-70'>
                            固定长度
                        </div>
                        <div className='right-auto'>
                            <Input
                                type={'number'}
                                name='newControlFixedLength'
                                value={this.newControl.newControlFixedLength}
                                onChange={this.onChange}
                                renderAppend={this.renderCell}
                            />
                        </div>
                    </div>
                    </div>
                    <ExternalDataBind
                            name={this.newControl.newControlName}
                            id='externalDataBind'
                            visible={this.visible}
                            documentCore={this.props.documentCore}
                            onChange={this.onChange}
                            close={this.onClose}
                            properties={this.dataBind}
                            resetId={'resetSourceBind'}
                    />
                </div>
            </Dialog>
        );
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        this.visible = nextProps.visible;
    }

    // private renderCell(): any {
    //     return '字符';
    // }

    private renderFooter(): any {
        return (
            <span>
                <Button onClick={this.close}>取消</Button>
                <Button type='primary' onClick={this.confirm}>确认</Button>
            </span>
        );
    }

    private renderCell(): any {
        return '厘米';
    }

    private onClose = (id: string, bRefresh: boolean): void => {
        this[id] = false;
        if ((id === 'bCustomProperty' || id === 'bCascade') && bRefresh) {
            this.setState({bRefresh: !this.state.bRefresh});
        } else if ('externalDataBind' === id && bRefresh) {
            this.resetSourceBind = false;
            this.setState({bRefresh: !this.state.bRefresh});
        }
    }

    private getCasedeList = (): any[] => {
        return this.newControl.newControlItems;
    }

    private open = (): void => {
        const props = this.props.property;
        const newControl: INewControlProperty = this.newControl = {} as any;
        let type: number;
        if (props === undefined) {
            this.init();
            type = this.props.type;
            newControl.newControlType = type;
            newControl.newControlName = this.props.documentCore.makeUniqueNewControlName(type);
        } else {
            const keys = Object.keys(props);
            keys.push('cascade');
            keys.forEach((key) => {
                if (key === 'newControlItems') {
                    return;
                }
                const val = props[key];
                newControl[key] = val;
            });
            const items = props.newControlItems;
            if (items && items.length > 0) {
                newControl.newControlItems = items.map((item) => {
                    return new CodeValueItem(item.code, item.value, item.bSelect);
                });
            }
            if (newControl.alignments === undefined) {
                newControl.alignments = AlignType.Left;
            }
            type = newControl.newControlType;
        }
        switch (type) {
            case NewControlType.Combox: {
                this.isNewControlTypeDisable = false;
                this.newControlType = NewControlType.Combox;
                break;
            }
            case NewControlType.ListBox: {
                this.isNewControlTypeDisable = false;
                this.newControlType = NewControlType.ListBox;
                break;
            }
            case NewControlType.MultiCombox: {
                this.isNewControlTypeDisable = true;
                this.newControlType = NewControlType.Combox;
                break;
            }
            case NewControlType.MultiListBox: {
                this.isNewControlTypeDisable = true;
                this.newControlType = NewControlType.ListBox;
                break;
            }
        }
        this.dataBind = newControl.externalDataBind;
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private checkedData(): boolean {
        const listDatas = this.newControl.newControlItems;
        if (listDatas && listDatas.length > 0) {
            const obj = {};
            for (let index = 0, length = listDatas.length; index < length; index++) {
                const code = listDatas[index].code;
                if (obj[code] === true) {
                    message.error(`下拉选项第${index + 1}行，存在相同名称`);
                    return false;
                }
                obj[code] = true;
            }
            for (let index = listDatas.length - 1; index >= 0; index--) {
                const code = listDatas[index].code;
                if (!code) {
                    listDatas.splice(index, 1);
                    continue;
                }
            }
        }

        return true;
    }

    private close = (bRefresh?: boolean): void => {
        this.props.close(this.props.id, bRefresh);
        this.visible = false;
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private confirm = (): void => {
        IFRAME_MANAGER.setDocId(this.docId);
        if (!this.checkedData()) {
            return;
        }

        const documentCore = this.props.documentCore;
        const props = this.props.property;
        const newControl = this.newControl;
        if (!isValidName(newControl.newControlName)) {
            message.error('名称不符合规范，请重新输入');
            return;
        }
        if (newControl.identifier && !isValidName(newControl.identifier)) {
            message.error('标识符不符合规范，请重新输入');
            return;
        }
        if ((!props || props.newControlName !== newControl.newControlName)
            && !documentCore.checkNewControlName(newControl.newControlName)) {
            message.error('该名称不符合，请重新填写');
            return;
        }

        if (this.resetSourceBind) {
            this.newControl.externalDataBind = {
                sourceObj: '',
                sourceKey: '',
                bReadOnly: false,
                commandUpdate: 1,
            }

            this.resetSourceBind = false;
        } else if (this.dataBind) {
            this.newControl.externalDataBind = this.dataBind;
        }

        if (props === undefined) {
            documentCore.addNewControl(this.newControl);
        } else {
            documentCore.setNewControlProperty(this.newControl, props.newControlName);
        }

        this.close(true);
    }

    private init(): void {
        const newControl = this.newControl;
        newControl.newControlTitle = undefined;
        newControl.newControlName = undefined;
        newControl.newControlInfo = undefined;
        newControl.newControlPlaceHolder = NewControlDefaultSetting.DefaultPlaceHolderContent;
        newControl.newControlType = NewControlType.Combox;
        newControl.isNewControlHidden = false;
        newControl.isNewControlCanntDelete = false;
        newControl.isNewControlCanntEdit = false;
        newControl.isNewControlMustInput = false;
        newControl.isNewControlShowBorder = true;
        newControl.isNewControlReverseEdit = false;
        newControl.isNewControlHiddenBackground = true;
        newControl.customProperty = undefined;
        newControl.newControlItems = undefined;
        newControl.selectPrefixContent = '';
        newControl.prefixContent = '';
        newControl.retrieve = false;
        newControl.separator = NewControlDefaultSetting.DefaultItemSeparator;
        newControl.tabJump = true;
        newControl.isShowValue = false;
        newControl.cascade = undefined;
        newControl.bShowCodeAndValue = undefined;
        newControl.identifier = undefined;
        this.newControlType = newControl.newControlType;
        this.isNewControlTypeDisable = false;
        newControl.newControlFixedLength = undefined;
        newControl.bTextBorder = undefined;
        newControl.alignments = AlignType.Left;
        newControl.codeLabel = undefined;
        newControl.valueLabel = undefined;
        newControl.externalDataBind = undefined;
        newControl.printSelected = false;
        this.resetSourceBind = false;
    }

    private onChange = (value: any, name: string): void => {
        // this.newControl[name] = value;
        // if (newControl && newControl.hasOwnProperty('bSetTitle')) {
        //     this.newControl.valueLabel = newControl.valueLabel;
        //     this.newControl.codeLabel = newControl.codeLabel;
        // }
        if ('resetSourceBind' !== name) {
            if ('externalDataBind' !== name) {
                this.newControl[name] = value;
            } else {
                this.dataBind = value;
            }
        } else {
            this.resetSourceBind = true;
        }
    }

    private typeChange = (value: any, name: string): void => {
        let bChange = false;
        if ( 'newControlType' === name && value !== this.newControlType ) {
            if ( NewControlType.Combox === value || NewControlType.MultiCombox === value ) {
                this.newControl.newControlType = this.isNewControlTypeDisable ?
                                                    NewControlType.MultiCombox : NewControlType.Combox;
            } else if ( NewControlType.ListBox === value || NewControlType.MultiListBox === value) {
                this.newControl.newControlType = this.isNewControlTypeDisable ?
                                                NewControlType.MultiListBox : NewControlType.ListBox;
            }
            bChange = true;
        } else if ( 'isNewControlTypeDisable' === name && value !== this.isNewControlTypeDisable ) {
            if ( NewControlType.Combox === this.newControlType || NewControlType.MultiCombox === this.newControlType ) {
                this.newControl.newControlType = value ? NewControlType.MultiCombox : NewControlType.Combox;
            } else if ( NewControlType.ListBox === this.newControlType
                        || NewControlType.MultiListBox === this.newControlType ) {
                this.newControl.newControlType = value ? NewControlType.MultiListBox : NewControlType.ListBox;
            }
            bChange = true;
        }

        this[name] = value;
        if ( bChange ) {
            this.newControl.newControlName = this.props.documentCore
                                                        .makeUniqueNewControlName(this.newControl.newControlType);
            this.setState({bRefresh: !this.state.bRefresh});
        }
    }

    private getTitle(): string {
        let title = '';

        if ( NewControlType.Combox === this.newControlType ) {
            title = '组合框';
        } else if ( NewControlType.ListBox === this.newControlType ) {
            title = '列表框';
        }

        title += (this.isNewControlTypeDisable ? '（多选）' : '（单选）');

        return title;
    }
}
