import { consoleLog, isGlobalTestData } from '../common/GlobalTest';
import { IExternalEvent } from '../common/external/IAsyncExternalInterface';
import LoggerSQL from "../common/log/db"

const CALLBACKS: Map<string, Function> = new Map();

const LISTENERS: Map<string, IExternalEvent> = new Map();

let globalEditorId: string;
let db: any;
const editorWriteLogs = (content: string) => {
  if (!content) {
    return;
  }
  if (!db) {
    db = new LoggerSQL();
  }
  let logs: any[];
  if (isGlobalTestData()) {
    consoleLog(content);
  }
  try {
    logs = JSON.parse(content);
    logs.forEach((log) => {
      db.insert(log);
    });
  } catch (e) {
    console.error(e);
    return;
  }
};

window.addEventListener('message', (e) => {
  const data = e.data;
  if (isGlobalTestData()) {
    consoleLog(data, LISTENERS);
  }

  const { __emrType } = data;
  if (!__emrType) {
    return;
  }
  if (__emrType === 'external') {
    const { result } = data;
    editorWriteLogs(result);
  } else if (__emrType === 'callback') {
    const { error, result, callId } = data;
    if (!!callId && CALLBACKS.has(callId)) {
      const callback = CALLBACKS.get(callId);
      callback(error, result);
      CALLBACKS.delete(callId);
    }
  } else if (__emrType === 'event') {
    const { params, editorId, eventType } = data;
    if (LISTENERS.has(editorId)) {
      const listener = LISTENERS.get(editorId)[eventType];
      if (listener instanceof Function) {
        listener.apply(null, params);
      }
    }
  }
}, false);

let countCallId: number = 1;
function invork(iframe: HTMLIFrameElement, editorId: string, method: string, params: any[] = []): Promise<any> {
  const callId = `callId-${countCallId++}`;
  globalEditorId = editorId;
  iframe.contentWindow.postMessage({callId, method, params, editorId, __emrType: 'invork'}, '*');
  return new Promise((resolve, reject) => {
    CALLBACKS.set(callId, (err, result) => {
      if (err) {
        reject(err);
      } else {
        resolve(result);
      }
    });
  });
}

function setEditorEvent(editorId: string, events: IExternalEvent): void {
  const obj = LISTENERS.get(editorId);
  if (obj) {
    const keys = Object.keys(events);
    keys.forEach((key) => {
      obj[key] = events[key];
    });
    return;
  }
  LISTENERS.set(editorId, events);
}

export function removeEditorEvent(editorId: string, sNames?: string[]): boolean {
  if (!LISTENERS.has(editorId)) {
    return false;
  }

  if (!sNames) {
    LISTENERS.delete(editorId);
  } else {
    const events = LISTENERS.get(editorId);
    sNames.forEach((name) => {
      events[name] = undefined;
    });
  }
  return true;
}

export default  {
  invork, setEditorEvent, removeEditorEvent
};
