// import * as React from 'react';
// import Document from './Document';
// import { DocumentCore, IDrawSelectionBounds } from '../../model/DocumentCore';
// import { ICursorProperty } from '../../model/CursorProperty';
// import {GlobalEvent as gEvent, GlobalEventName as gEventName} from '../../common/GlobalEvent';
// import {TextArea} from './TextArea';
// import Print from './Print';
// import MouseEventHandler, {KeyBoardEvent, MouseEventType} from '../../common/MouseEventHandler';

// /**
//  * 根据节点获得pageid 如果不存在 返回null
//  * @param node
//  */
// // tslint:disable-next-line: sy-global-const-name
// export const getPageElement = (node: any): any => {
//     if (!node) {
//       return null;
//     }
//     while (node.tagName !== 'svg'
//      || node.getAttribute('type') !== 'page') {
//        if (node.tagName === 'BODY') {
//          return null;
//        }
//        if (!node.parentNode) { return null; }
//        node = node.parentNode;
//     }
//     return node;
// };

// interface IEmrState {
//   currentIndex: number;
//   isScroll: boolean;
//   clickOutsideDropdown: boolean;
//   bInput: boolean;  // 文本输入
//   isCacheEnabled: boolean;
// }

// export class EmrPrint extends React.Component<{}, IEmrState> {

//   /*
//    * 判断当前输入是否为：上下左右，pageUp，pageDown
// 	 * @param keyCode
// 	 */
//   private static isLRUDKey( keyCode: number ): boolean {
//     return (keyCode === 33 || keyCode === 34 || keyCode === 37 || keyCode === 38 || keyCode === 39 ||
//           keyCode === 40);
//   }

//   public isPrint: boolean = true;

//   /**
//    * 光标闪动的定时器
//    */
//   private timer: any = null;
//   /**
//    * 光标的dom
//    */
//   private cursor: SVGElement | null = null;
//   /**
//    * textArea
//    */
//   private textArea: any = null;

//   private startPoint: any = null;
//   /**
//    * 光标点
//    */
//   private endPoint: any = null;

//   // mouse operation
//   private globalMouseEvent: MouseEventHandler = null;

//   private globalKeyEvent: KeyBoardEvent = null;

//   private myRef: any;

//   private selections: IDrawSelectionBounds = null;  // 选中区域

//   private bSelected: boolean = false; // 当前是否选中状态

//   private pageIndex: number;  // 当前光标所在页面
//   private bJump: boolean;  // 是否需要跳转页面，todo： 判断当前页面是否已经生成，如果没有生成，先刷新页面；如果已经生成，则滚动页面

//   // cacheThreshold = null;
//   private cacheSaveTimer: any = null;

//   // 10 mins
//   private cacheEnabledCountDown: number = 600000;

//   // 10s
//   private cacheMonitorCountDown: number = 10000;

//   private pagePro: any = null;

//   private listDom: HTMLDivElement;
//   private hasContent: boolean = false;
//   private documentCore: any;
//   private printVm: any;
//   private docId: number;

//   constructor(props: any) {
//     super(props);
//     // this.documentCore = new DocumentCore(undefined, false);
//     // this.documentCore = new DocumentCore();
//     this.pageIndex = 0;
//     this.bJump = false;
//     this.globalMouseEvent = new MouseEventHandler();
//     this.globalKeyEvent = new KeyBoardEvent();
//     this.myRef = React.createRef();

//     this.state = {
//        currentIndex: 0,
//        isScroll: false,
//        clickOutsideDropdown: false,
//        bInput: false,
//        isCacheEnabled: false,
//     };

//     //  console.log('EmrPrint---------------constructor----------------')
//   }

//   public componentDidMount(): void {
//     // document.body.className = (document.body.className || '') + ' print';
//     setTimeout(() => {
//       this.addEventListener();
//       this.textArea = new TextArea(this, true);
//     }, 550);
//   }

//   public componentWillUnmount(): void {
//     // gEvent.setEvent(gEventName.UnMounted);
//     this.removeEventListener();
//     if ( this.timer ) {
//         this.cursorBlur();
//     }
//     // console.log('EmrPrint---------------componentWillUnmount----------------')
//   }

//   public componentDidUpdate(): void {
//     // console.log("didUpdate");
//     // console.log(this.state.isCacheEnabled); // after didMount(), will be true
//   }

//   // componentWillUpdate() {
//   public getSnapshotBeforeUpdate(): void {
//     if (this.cursor && !this.state.isScroll && true !== this.isSelected() ) {
//         this.updateCursor(true, false);
//     }
//     return null;
//   }

//   public render(): any {
//     if (!this.documentCore) {
//       return (<div>正在加载中</div>);
//     }
//     return (
//       <div className='hz-editor-container' style={{paddingTop: 0}} ref={this.myRef}>
//          <textarea id='textarea_input' />
//          {this.renderPages()}
//       </div>
//     );
//   }

//   public setDocumentCore(documentCore: DocumentCore): void {
//     if (documentCore !== undefined) {
//       this.documentCore = documentCore;
//       this.docId = documentCore.getCurrentId();
//     }
//     this.setState({bInput: !this.state.bInput});
//   }

//   public scaleViewEvent(scale: string): void {
//     this.setLayouWidth(scale);
//   }

//   public openPrint(type: number, pagePosition?: string): void {
//     if (!this.printVm) {
//       this.printVm = new Print(this);
//     }

//     this.printVm.print(type, pagePosition);
//   }

//   public getCursor(): SVGElement {
//     return this.cursor;
//   }

//     /**
//      * 初始化textArea，光标位置
//      */

//   private addEventListener(): void {
//     const dom = this.myRef.current;
//     // document.addEventListener("scroll", this.handleScroll);
//     // document.addEventListener("keydown", this.handleKeyDown, true);
//     // document.addEventListener("keyup",this.handleKeyUp);
//     dom.addEventListener('mousedown', this.handleMouseDown);
//     dom.addEventListener('mousemove', this.handleMouseMove);
//     dom.addEventListener('mouseup', this.handleMouseUp);
//     dom.addEventListener('visibilitychange', this.handleWindowVisible);
//   }

//   private removeEventListener(): void {
//       const dom = this.myRef.current;
//       // document.removeEventListener("scroll", this.handleScroll);
//       // document.removeEventListener("keydown", this.handleKeyDown);
//       // document.addEventListener("keyup",this.handleKeyUp);
//       dom.removeEventListener('mousedown', this.handleMouseDown);
//       dom.removeEventListener('mousemove', this.handleMouseMove);
//       dom.removeEventListener('mouseup', this.handleMouseUp);
//       dom.removeEventListener('visibilitychange', this.handleWindowVisible);
//   }

//   /**
//    * 设置光标位置
//    * @param position
//    */
//   private setCursorPosition(position: ICursorProperty): void {
//     const { y1, y2, pageNum } = position;
//     let x = position.x;
//     //  console.log(position.x);
//     const cursor = this.cursor;

//     // cursor stroke width is cut half at 0
//     if (x === 0) {
//       x = 1;
//     }

//     cursor.setAttribute('x1', `${x}`);
//     cursor.setAttribute('x2', `${x}`);
//     cursor.setAttribute('y1', `${y1}`);
//     cursor.setAttribute('y2', `${y2}`);
//     this.textArea.setTextAreaPosition(x, y1);
//   }

//     /**
//      * 打开文档时，初始化光标位置：文档第一页第一段段首
//      */
//   private initCursorPos(): void {
//       const documentCore = this.documentCore;

//       const position = documentCore.getCursorPositionBySetPoint(0, 0, 0);

//       const newPageNode = this.getNewPageNode(position);

//       if ( null !== newPageNode ) {
//         this.insertCursor(newPageNode);
//         this.setCursorPosition(position);
//         this.cursorFocused();
//         this.textArea.focus();
//         // this.setCursorVisible(true);
//       }
//   }

//   /**
//    * 光标开始闪呀
//    */

//   private cursorFocused(): void {
//     clearInterval(this.timer);
//     this.timer = setInterval(() => {
//       if (!this.cursor) {
//         return;
//       }
//       const isHidden = this.cursor.style.visibility === 'hidden';
//       this.cursor.style.visibility = isHidden ? 'initial' : 'hidden';
//     }, 550);
//   }

//   /*
//    * 光标移动时：需要一直保持显示状态，不能为hidden
// 	 */
//   private setCursorVisible( bVisible: boolean ): void {
//     if ( 'hidden' === this.cursor.style.visibility ) {
//       if ( true === bVisible ) {
//         this.cursor.style.visibility = 'initial';
//       }
//     } else {
//       if ( false === bVisible ) {
//         this.cursor.style.visibility = 'hidden';
//       }
//     }
//   }

//   private cursorBlur(): void {
//     clearInterval(this.timer);
//   }

//   /*
//    * keyUp
// 	 */
//   private onKeyUp(): void {
//       // 上，下，左，右键不需要重刷页面，只有在跨页等需要重刷的情况下才重刷
//       // redo/undo时不需要重刷页面，react会自动重刷一次页面，这样减少一次页面刷新
//       // const bForceUpdate = (false === EmrPrint.isLRUDKey(this.globalKeyEvent.keyCode));

//       // // update paraelem's position x, position y here
//       // this.updateCursor(true, bForceUpdate);
//       // this.globalKeyEvent.keyCode = 0;
//     }

//   /**
//    * 在model层设置cursor并渲染
//    * @param pageNode
//    */
//   private renderSetCursor( pageNode: SVGElement ): void {
//     if (pageNode) {
//       // 右键粘贴时，光标变化暂时代替内容变化
//       // gEvent.setEvent(this.docId, gEventName.ContentChange);
//       const position = this.documentCore.getCursorPosition();
//       this.endPoint = position;
//       this.insertCursor(pageNode);
//       this.setCursorPosition(position);
//       this.cursorFocused();
//       this.textArea.focus();
//       this.setCursorVisible(true);
//     }
//   }

//   /**
//    * 获取当前事件的page id
//    * @param pageNode
//    * @param event
//    */
//   private getPageId(pageNode: SVGElement, event: any): number {
//     let pageId;
//     // when clicking outside svg
//     if (!pageNode) {
//       const page = this.documentCore.getDocument().pages[0];
//       if (!page) {
//           return;
//       }
//       // assume each page's height is the same
//       const pageHeight = page.height;

//       // when clicking parapage margin area
//       if (event.offsetY / pageHeight >= 1 ) {
//         pageId = Math.floor(event.offsetY / pageHeight) - 1;
//       }

//       // TODO: click left/right of current page

//     } else {
//       pageId = Number.parseInt(pageNode.getAttribute('page-id'), 10);
//     }
//     return pageId;
//   }

//   /**
//    * 更新光标位置
//    * @bComposition 是否有键盘输入
//    * @bForceUpdate 是否需要强制更新：一般只有在对文档内容进行编辑的时候，才需要强制更新
//    * @bResetCursor 是否需要重新插入光标，使当前页面重新获取焦点：在操作工具栏后或其他操作，页面焦点会失去
//    */
//   private updateCursor( bComposition: boolean = false, bForceUpdate: boolean = false,
//                         bResetCursor: boolean = false ): void {
//     const documentCore = this.documentCore;
//     const position = documentCore.getCursorPosition();
//     // console.log(position)
//     // console.log(documentCore.document.content[0]);
//     // console.log(circularParse(circularStringify(documentCore.document.content[0]))); // no position yet

//     // 键盘移动光标时，判断光标是否需要跨页
//     if ( ( this.cursor && null !== this.cursor.ownerSVGElement && Number.parseInt(this.cursor.ownerSVGElement
//         .getAttribute('page-id'), 10) !== position.pageNum ) || ( true === bResetCursor ) ) {
//       let newPageNode = this.getNewPageNode(position);

//       // 当光标移动到页面临界处（换页），当光标所在页面还没有被刷新出来，则需要先刷新所需的页面
//       if ( !newPageNode && position.pageNum !== this.pageIndex ) {
//         this.pageIndex = position.pageNum;
//         this.bJump = true;
//         this.setState({currentIndex: this.pageIndex});
//       }

//       newPageNode = this.getNewPageNode(position);
//       if ( null !== newPageNode ) {
//         this.endPoint = position;
//         this.insertCursor(newPageNode);
//         this.setCursorPosition(position);
//         this.cursorFocused();
//         this.textArea.focus();
//       }
//     } else {
//       this.setCursorPosition(position);

//       if ( true === bForceUpdate ) {
//         // this.forceUpdate();
//       } else {
//         this.cursorFocused();
//       }
//         // console.log(circularParse(circularStringify(documentCore.document.content[0]))); // have position
//     }
//     // 强制光标可见
//     this.setCursorVisible(true);
//   }

//   /**
//    * 设置当前是否是选择状态
//    * @param bSelect
//    */
//   private setSelect( bSelect: boolean ): void {
//     this.bSelected = bSelect;
//   }

//   private renderPages(): any {
//      const documentCore = this.documentCore;
//      const { currentIndex }  = this.state;
//      const getContentByPageId = documentCore.getContentByPageId;
//      const { total, textProperty, pageProperty } = documentCore.render();
//      return (
//       <Document
//         onScroll={this.handleScroll}
//         total={total}
//         height={total * pageProperty.height}
//         currentIndex={( true === this.bJump ? currentIndex : undefined )}
//         textProperty={textProperty}
//         pageProperty={pageProperty}
//         getContentByPageId={getContentByPageId}
//         editorContainer={this.myRef.current}
//         documentCore={documentCore}
//         handleRefresh={this.handleRefresh}
//         host={this}
//         cursorType={documentCore.getCursorTypeForPrint()}
//       />
//      );
//   }

//   private setLayouWidth(scaleStr: string): void {
//     const scale = parseInt(scaleStr, 10);
//     const editorContainer = this.myRef.current;
//     let dom: HTMLDivElement;
//     if (this.listDom) {
//       dom = this.listDom;
//     } else {
//       this.listDom = dom = editorContainer.querySelector('.ReactVirtualized__Grid');
//     }
//     if (!dom) {
//       return;
//     }
//     this.getPagerPro();
//     const pagePro = this.pagePro;

//     const matchs = /\s+scale-\d+/.exec(dom.className);
//     if (matchs) {
//       dom.className = dom.className.replace(matchs[0], ' scale-' + scale);
//     } else {
//       dom.className += ' scale-' + scale;
//     }
//     if (scale > 100) {
//       const width = pagePro.width * (scale / 100);
//       dom.style.width = width + 'px';
//     } else {
//       let addNum = 0;
//       switch (scale) {
//         case 25:
//             addNum += 30;
//             break;
//         case 50:
//             addNum += 10;
//             break;
//       }
//       dom.style.width = pagePro.width + addNum + 'px';
//     }

//     this.setLayouHeight(scale);
//     // dom.style.width = width + 'px';
//   }

//   private getPagerPro(): void {
//       this.pagePro = this.documentCore.getPageProperty();
//   }

//   // getTotal(): number {
//   //   const { total } = this.state.documentCore.render();
//   //   return total;
//   // }

//   private setLayouHeight(scale: number): void {
//     const doms = this.listDom.querySelectorAll('.page-wrapper');
//     if (!doms) {
//       return;
//     }
//     const pagePro = this.pagePro;
//     const num = scale / 100;
//     const width = pagePro.width * num  + 'px';
//     const height = pagePro.height * num + 'px';
//     Array.from(doms)
//     .forEach((dom) => {
//       const div = dom as HTMLDivElement;
//       div.style.height = height;
//       div.style.width = width;
//     });

//     let len = doms.length;

//     switch (scale) {
//       case 25:
//           len = Math.ceil(len / 4);
//           break;
//       case 50:
//           len = Math.ceil(len / 2);
//           break;
//     }

//     const listHeight = pagePro.height * num * len + len * 20 + 'px';
//     this.listDom.style.height = listHeight;
//   }

//   private isCacheEnabled = () => {
//     return this.state.isCacheEnabled;
//   }

//   private clearCacheSaveTimer = () => {
//     clearTimeout(this.cacheSaveTimer);
//     this.cacheSaveTimer = null;
//     // console.log("clr")
//   }

//   /**
//    * 获得当前滚动到第几页,强制刷新页面
//    */
//   private handleScroll = (event: any) => {
//     // console.log()
//   }

//   /**
//    * 光标元素插入
//    */
//   private insertCursor = (pageNode: SVGElement, pageId?: number) => {

//     if (this.cursor) {
//       let newPageId;
//       const oldPageId = this.cursor.ownerSVGElement.getAttribute('page-id');
//       if (pageNode) {
//         newPageId = pageNode.getAttribute('page-id');
//       } else if (pageId) { // cross page
//         newPageId = pageId;
//       }

//       if ( pageNode && ( newPageId !== oldPageId || 0 === pageNode.getElementsByTagName('line').length ) ) {
//         // move the same cursor to new page
//         pageNode.appendChild(this.cursor);
//         pageNode.appendChild(this.textArea.wrapper());
//       }
//     } else {
//       const cursor = document.createElementNS('http://www.w3.org/2000/svg', 'line');
//       cursor.setAttribute('id', 'cursor');
//       cursor.setAttribute('stroke', 'black');
//       cursor.setAttribute('stroke-width', '2');
//       cursor.setAttribute('style', 'visibility:hidden');
//       pageNode.appendChild(cursor);
//       this.cursor = cursor;
//       this.textArea.insertHiddenTextArea(pageNode);
//     }

//   }

//   /*
//    * textArea和document都会回调此函数，在页面滚动时，textArea可能会被销毁，
//    * 此时按键操作，则只有document回调此函数进行处理
//    * keyDown
// 	 * @param event
// 	 */
//   private onKeyDown(event: any): void {
//       // if ( this instanceof EmrPrint && this.textArea ) {
//       //     const documentCore = this.documentCore;
//       //     this.globalKeyEvent.keyCode = event.keyCode;
//       //     this.globalKeyEvent.bCtrlKey = event.ctrlKey;
//       //     this.globalKeyEvent.bAltKey = event.altKey;
//       //     this.globalKeyEvent.bShiftKey = event.shiftKey;

//       //     // 当前按键为功能键，不做任何处理
//       //     // if ( true === EmrPrint.isFuntionKey(this.globalKeyEvent) )
//       //     //     return ;

//       //   //   if (!this.globalKeyEvent.bShiftKey) {
//       //   //       this.globalKeyEvent.keyCode = 113;
//       //   //       return
//       //   //   }

//       //     const oldSelection = documentCore.getDocumentSelection();
//       //     const bOldSelection = oldSelection.bUse;

//       //     this.pageIndex = documentCore.getCursorPosition().pageNum;
//       //     this.bJump = false;

//       //     // documentCore.onKeyDown(this.globalKeyEvent);

//       //     const newSelection = documentCore.getDocumentSelection();
//       //     // console.log(newSelection);

//       //     // 选择：
//       //     // 1. shift键 + left/right/up/down
//       //     // 2. ctrl + a: 全选
//       //     // 3. undo/redo : 有选择操作
//       //     // 只要当前有内容选中，就ok
//       //     if ( true === newSelection.bUse ) {

//       //         // update globalMouseEvent class
//       //         const point = documentCore.getCursorPosition();
//       //         this.globalMouseEvent.pointX = point.x;
//       //         this.globalMouseEvent.pointY = point.y1;

//       //         // always clear selection before render (before selections[] is cleared)
//       //         this.clearSection();

//       //         const pageNode = getPageElement(event.target);
//       //         const pageId = this.getPageId(pageNode, event);

//       //         this.selections = documentCore.getSelectionBounds(this.globalMouseEvent, pageId);

//       //         // 只有在有内容选中，并且当前是选中状态，才进行选中区域的渲染
//       //         if ( this.selections ) {
//       //             this.renderSetCursor(pageNode);
//       //             this.cursorBlur();
//       //             this.setCursorVisible(false);
//       //             this.renderSection();
//       //         }
//       //     } else if ( ( true === bOldSelection ) && ( true === event.shiftKey || true === event.ctrlKey ) ) {
//       //         // 选择状态下，单击shift、ctrl键没有效果，不需要进行光标等刷新
//       //         return;
//       //     } else {
//       //         this.clearSection();

//       //         // 之前选择的状态被清除，确保光标开始闪烁
//       //         if ( true === bOldSelection && false === newSelection.bUse ) {
//       //             // 当前光标所在页面
//       //             const curIndex = documentCore.getCursorPosition().pageNum;

//       //             if ( curIndex !== this.state.currentIndex || this.pageIndex !== this.state.currentIndex ) {
//       //                 this.bJump = true;
//       //                 this.pageIndex = curIndex;
//       //                 this.setState({currentIndex: curIndex});
//       //             }

//       //             this.handleWindowVisible();
//       //         }
//       //     }
//       // }
//   }

//   /**
//    * 处理选区
//    *
//    */
//   private handleMouseDown = (event: any) => {

//     // console.log("editor mousedown")
//     // console.log(event.pageX, event.pageY)
//     // console.log(event.target.tagName)

//     const documentCore = this.documentCore;
//     // console.log(documentCore.getDocument())

//     const pageNode = getPageElement(event.target);
//     const pointX = event.offsetX;
//     const pointY = event.offsetY;

//     // check if click on image
//     // IMAGE_FLAGS.isImageOnClick = false;
//     // IMAGE_FLAGS.isHandlerOnClick = false;
//     // if ( event.target.tagName === "image" ) {
//     //   // this.cursorBlur();
//     //   // this.setCursorVisible(false);
//     //   IMAGE_FLAGS.isImageOnClick = true;
//     //   documentCore.setImageSelectionInfo(event.target);
//     // } else if (event.target.tagName === "circle" && event.target.className.baseVal.includes("image-processor")) {
//     //   // this.cursorBlur();
//     //   IMAGE_FLAGS.isImageOnClick = true;
//     //   IMAGE_FLAGS.isHandlerOnClick = true;
//     //   documentCore.setImageSelectionInfo(event.target);
//     // } else {
//     //   // this.cursorFocused();
//     //   documentCore.setImageSelectionInfo(null);
//     // }

//     // set mouseEvent
//     this.globalMouseEvent.button = event.button;
//     this.globalMouseEvent.bShiftKey = event.shiftKey;
//     this.globalMouseEvent.type = MouseEventType.MouseButtonDown;
//     this.globalMouseEvent.pointX = pointX;
//     this.globalMouseEvent.pointY = pointY;

//     const pageId = this.getPageId(pageNode, event);

//     let selection = documentCore.getDocumentSelection();
//     // console.log(circularParse(circularStringify(selection)));

//     // If selected area exists
//     if ( !( true === selection.bUse && true === event.shiftKey ) ) {
//       this.clearSection();
//       documentCore.removeSelection();
//     }

//     // console.log(circularParse(circularStringify(documentCore.getDocument())));
//     documentCore.mouseButtonDown(this.globalMouseEvent, pageId, pointX, pointY);
//     // console.log(circularParse(circularStringify(documentCore.getDocument())));
//     selection = documentCore.getDocumentSelection();

//     this.pageIndex = documentCore.getCursorPosition().pageNum;
//     this.bJump = false;

//     // mousedown on image polygons should not focus on 1px textarea so as not to jump wildly
//     // if (!IMAGE_FLAGS.isHandlerOnClick) {
//     //   this.renderSetCursor(pageNode);
//     // }
//     this.renderSetCursor(pageNode);

//     // shift键选择
//     if ( true === selection.bUse && true === this.globalMouseEvent.bShiftKey ) {
//       // 隐藏光标
//       this.cursorBlur();
//       this.setCursorVisible(false);

//       // always clear selection before render (before selections[] is cleared)
//       this.clearSection();

//       this.selections = documentCore.getSelectionBounds(this.globalMouseEvent, pageId);
//        // console.log(documentCore.getDocumentSelection())
//        // console.log(selections)

//       // 只有在有内容选中，并且当前是选中状态，才进行选中区域的渲染
//       if ( this.selections && true === documentCore.getDocumentSelection().bUse ) {
//         this.renderSection();
//       }
//         // console.log(documentCore.document.selection)
//     }
//   }

//   /**
//    * 记录选区开始
//    * @param event
//    */
//   private handleMouseMove = (event: MouseEvent) => {
//     // console.log("editor mousemove")
//     const documentCore = this.documentCore;

//     this.globalMouseEvent.type = MouseEventType.MouseButtonMove;

//     const pageNode = getPageElement(event.target);

//     const pageId = this.getPageId(pageNode, event);

//     // update globalMouseEvent class
//     this.globalMouseEvent.pointX = event.offsetX;
//     this.globalMouseEvent.pointY = event.offsetY;
//     // console.log(event.offsetX, event.offsetY); // when crossing page, y might be < 0

//     // if (!IMAGE_FLAGS.isHandlerOnClick) {
//     //   documentCore.mouseButtonMove(this.globalMouseEvent, pageId, event.offsetX, event.offsetY);
//     // }
//     documentCore.mouseButtonMove(this.globalMouseEvent, pageId, event.offsetX, event.offsetY);

//     // --- all selections{} should be ready at this point ---
//     this.pageIndex = documentCore.getCursorPosition().pageNum;
//     this.bJump = false;

//     const selection = documentCore.getDocumentSelection();

//     if ( true === selection.bUse && true === selection.bStart) {
//       // console.log(documentCore.getDocument())
//       // always clear selection before render (before selections[] is cleared)
//       this.clearSection();

//       this.selections = documentCore.getSelectionBounds(this.globalMouseEvent, pageId);

//       // 只有在有内容选中，并且当前是选中状态，才进行选中区域的渲染
//       if ( this.selections ) {
//           // 隐藏光标
//           this.renderSetCursor(pageNode);
//           this.cursorBlur();
//           this.setCursorVisible(false);
//           this.renderSection();
//       }
//     }
//   }

//   /**
//    * 鼠标点击-放开
//    * 判断光标位置 重置位置或者移除光标
//    * @param event
//    */
//   private handleMouseUp = (event: any) => {

//     // console.log("editor mouseup")
//     // console.log(event.target.tagName)

//     // smooth clicking in modal or mousedown event not expected to influence editor
//     // const isClickOnNonSVGDOM = this.isCursorOnNonSVGDOM(event);
//     // if (isClickOnNonSVGDOM) {
//     //   return null;
//     // }

//     // let callbackOption = {isStop: false};
//     // gEvent.setEvent('editorMouseUpStopUnSelection', callbackOption, event);
//     // if (callbackOption.isStop === true) {
//     //   return null;
//     // }

//     // During mouseup of LEFT CLICK, If contextMenu opens, close it
//     // if (this.state.showContextMenu && event.button === MouseButtonType.MouseButtonLeft &&
//     // "contextMenu-item" !== event.target.className ) {
//     //   this.setState({showContextMenu: false});
//     // }

//     // // If mousedown on image and drag, shouldn't scroll page and rerender cursor
//     // if (IMAGE_FLAGS.isImageOnClick) {
//     //   this.textArea.focus(); // events in textArea should be preserved
//     //   return null;
//     // }

//     this.globalMouseEvent.type = MouseEventType.MouseButtonUp;

//     const pageNode = getPageElement(event.target);
//     const pageId = this.getPageId(pageNode, event);

//     const documentCore = this.documentCore;

//     documentCore.mouseButtonUp(this.globalMouseEvent, pageId, event.offsetX, event.offsetY);

//     // const selection = documentCore.getDocumentSelection();

//     this.renderSetCursor(pageNode);

//     // if ( true === this.isSelected() || IMAGE_FLAGS.isImageOnClick || IMAGE_FLAGS.isHandlerOnClick ) {
//     if ( true === this.isSelected() ) {
//         // console.log("hide cursor")
//         this.cursorBlur();
//         this.setCursorVisible(false);
//     }
//   }

//   /**
//    * 清除选区
//    * @param selected
//    */
//   private clearSection = () => {
//     const dom = this.myRef.current;
//     if ( this.selections ) {
//       const lines = this.selections.lines;
//       if ( lines ) {
//         lines.map((item) => {
//           const selectionCollection = dom.getElementsByClassName(`section-item-${item.line.id}`);

//           // If parapage, there would exist two (or more, consider parapage > 2) such nodes
//           for (let i = 0, len = selectionCollection.length; i < len; i++) {
//               const elem = selectionCollection[i];
//               if (elem) {
//                 elem.classList.remove('selection-selected');
//               }
//           }
//         });
//       }

//       const drawCells = this.selections.cells;
//       if ( drawCells ) {
//           drawCells.map((cell) => {
//           const selectionCollection = dom.getElementsByClassName(`section-cellItem-${cell.id}-${cell.pageIndex}`);

//           // If parapage, there would exist two (or more, consider parapage > 2) such nodes
//           for (let i = 0, len = selectionCollection.length; i < len; i++) {
//               const elem = selectionCollection[i];
//               if (elem) {
//                 elem.classList.remove('selection-selected');
//               }
//           }
//         });
//       }
//     }

//     this.selections = null;
//     this.setSelect(false);
//   }

//   /**
//    * 渲染选区
//    * @param e
//    * @param isUp 选区方向
//    */
//   private renderSection = (e?: MouseEvent, selections?: IDrawSelectionBounds ) => {

//     if ( null === this.selections ) {
//       this.setSelect(false);
//       return;
//     }

//     const drawLines = this.selections.lines;
//     const drawCells = this.selections.cells;

//     if ( ( !drawLines || 0 === drawLines.length ) && ( !drawCells || 0 === drawCells.length ) ) {
//         return;
//     }

//     const dom = this.myRef.current;
//     if ( drawLines && 0 < drawLines.length ) {
//         drawLines.map((item, index) => {
//           const tarLines = dom.getElementsByClassName(`section-item-${item.line.id}`);
//           const tarLineIndex = 0;

//           const tarLine: any = tarLines[tarLineIndex];

//           // 选中多页，选中的页面会被替换，tarLine已经不在
//           if ( undefined !== tarLine && null !== tarLine ) {
//             tarLine.classList.add('selection-selected');

//             tarLine.setAttribute('x', `${item.x}`);
//             tarLine.setAttribute('width', `${item.width}`);
//           }
//       });
//     }

//     if ( drawCells && 0 < drawCells.length ) {

//         drawCells.map((item) => {
//             const tarCells = dom.getElementsByClassName(`section-cellItem-${item.id}-${item.pageIndex}`);
//             const tarCell: any = tarCells[0];

//               // 选中多页，选中的页面会被替换，tarLine已经不在
//             if ( undefined !== tarCell && null !== tarCell ) {
//                   tarCell.classList.add('selection-selected');

//                   tarCell.setAttribute('x', `${item.x}`);
//                   tarCell.setAttribute('width', `${item.width}`);
//               }
//         });

//     }

//     this.setSelect(true);
//   }

//   /**
//    * 页面失去或获得焦点
//    */
//   private handleWindowVisible = () => {
//       const isHidden = document.hidden;
//       if (isHidden) {
//           clearInterval(this.timer);
//       } else {
//           this.cursorFocused();
//       }
//   }

//   /**
//    *
//    */
//   private handleClickOutsideDropdown = () => {
//     this.setSelect(false);
//     this.setState({clickOutsideDropdown: false});
//   }

//   /**
//    * 强制页面重刷
//    */
//   private handleRefresh = () => {
//     // this.state.documentCore.render();

//     // 重新使页面获取焦点
//     this.updateCursor(false, false, true);

//     // this.forceUpdate();
//     // this.setState( {bInput: !this.state.bInput} );
//     this.render();
//   }

//   private testDocument = (data: any): void => {
//     // console.log(data);
//     this.documentCore.testDocument(data);
//     this.initCursorPos();
//     this.forceUpdate();
//   }

//   private testDocumentXml = () => {
//     // console.log(data);
//     // this.state.documentCore.testDocumentXml(data);
//     this.initCursorPos();
//     this.forceUpdate();
//   }

//   private addInlineImage = (width: number, height: number, src: string) => {
//     // console.log(src)
//     this.documentCore.addInlineImage(width, height, src);
//     // this.initCursorPos();
//     // this.forceUpdate();

//     // look more closely
//     this.updateCursor(true, true);
//   }

//   /*
//    * 获取文档页的node数组
// 	 */
//   private getDocumentNode(): SVGElement {
//       if ( null === this.myRef || undefined === this.myRef ) {
//         return null;
//       }

//       const editorContainerNode = this.myRef.current;

//       if ( null === editorContainerNode ) {
//       return null;
//     }

//       let documentNode = null;
//       for ( let i = 0; i < editorContainerNode.childElementCount; i++ ) {
//       // 获取当前页面的页码
//       if ( 'grid' === (editorContainerNode.childNodes.item(i) as SVGElement).getAttribute('role') ) {
//         // 获取当前页SVG
//         documentNode = editorContainerNode.childNodes.item(i) ;
//         break;
//       }
//     }

//       return documentNode.childNodes.item(0);
//   }

//   /*
//    * 重新设置光标pageNode
// 	 * @param position
// 	 */
//   private getNewPageNode(position: ICursorProperty): SVGElement {
//       const documentNode = this.getDocumentNode();

//       if ( null === documentNode ) {
//         return null;
//       }

//       let curPage = null;
//       for ( let i = 0; i < documentNode.childElementCount; i++ ) {
//           // 获取当前页面的页码
//           if ( Number.parseInt((documentNode.childNodes.item(i) as SVGElement)
//             .getAttribute('page-index'), 10) === position.pageNum ) {
//               // 获取当前页SVG
//               curPage = documentNode.childNodes.item(i).childNodes
//                 .item(0) ;
//               break;
//           }
//       }

//       return curPage;
//   }

//   /**
//    * 是否选中内容
//    */
//   private isSelected(): boolean  {
//     return this.bSelected;
//   }
// }
