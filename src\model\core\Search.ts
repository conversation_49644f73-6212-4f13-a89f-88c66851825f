import { ISearchInfo, ISearchWord, ResultType } from '@/common/commonDefines';
import Paragraph from './Paragraph';
import Document from './Document';
import { ParaElementType } from './Paragraph/ParagraphContent';
import { getRegWord } from '@/common/commonMethods';

export class Search {
    private logicDocument: Document;
    private searchDatas: Map<number, ISearchInfo[]>;
    private currentSearch: ISearchInfo;
    private searchWord: ISearchWord;
    private oldSearchWord: ISearchWord;
    private current: any;
    private para: Paragraph;
    private searchCount: number;
    private searchIndex: number;
    private pageIndex: number;
    private portionTexts: any[];
    private currentIndex: number;
    constructor(doc: Document) {
        this.logicDocument = doc;
    }

    public setSearchWord(searchWord: ISearchWord): number {
        const oldSearchWord = this.searchWord;
        this.searchCount = 0;
        this.searchDatas = new Map();
        this.searchIndex = 0;
        if (oldSearchWord && oldSearchWord.bCaseSensitive === searchWord.bCaseSensitive && searchWord.bPlaceholder ===
            oldSearchWord.bPlaceholder && searchWord.keyword === oldSearchWord.keyword) {
            return ResultType.UnEdited;
        }

        this.searchWord = {...searchWord};
        if (!searchWord.keyword) {
            return ResultType.Failure;
        }
        return ResultType.Success;
    }

    public getSearchInfos(paraId: number): ISearchInfo[] {
        return this.searchDatas.get(paraId);
    }

    public getSearchCount(): number {
        // let count: number = 0;

        // const datas = this.searchDatas;
        // for (const [id, data] of datas) {
        //     count += data.length;
        // }

        return this.searchCount;
    }

    public search(para: Paragraph): boolean {
        if (para.isHidden()) {
            return false;
        }
        this.para = para;
        const contents = para.content;
        const portions = [];
        const {bPlaceholder, bCaseSensitive, keyword} = this.searchWord;
        if (!keyword) {
            return false;
        }
        const wordReg = getRegWord(keyword);

        let bEnd: boolean;
        let text: string = '';
        let searchReg: RegExp;
        if (bCaseSensitive) {
            searchReg = new RegExp(wordReg, 'ig');
        } else {
            searchReg = new RegExp(wordReg, 'g');
        }

        const paraId = para.id;
        const maxLength = keyword.length;
        const current: any = this.current = {
            paraId,
            maxLength,
            searchReg,
            lastIndex: 0,
        };
        this.pageIndex = undefined;
        this.portionTexts = [];
        const portionTexts = {};
        for (let index = 0, len = contents.length; index < len; index++) {
            const portion = contents[index];
            if (bEnd) {
                if (portion.isNewControlEnd()) {
                    bEnd = false;
                }
                continue;
            }

            if (bPlaceholder === true && portion.isPlaceHolder() && portion.isNewControlStart()) {
                bEnd = true;
            }

            if (!portion.isHidden()) {
                const content = this.getContentText(portion.content);
                if (content) {
                    portionTexts[index] = this.portionTexts;
                    current.content = content;
                    current.text = text;
                    current.portion = portion;
                    this.addSearchData();
                    this.portionTexts = [];
                    text += content;
                }
            }
        }

        if (!text) {
            return false;
        }

        // if (bChanged === false) {
        //     return false;
        // }

        return true;
    }

    public jumpToOneSearch(index: number): number {
        const datas = Array.from(this.searchDatas);
        if (datas.length === 0) {
            return;
        }

        let current: ISearchInfo[] = [];
        let dataIndex = 0;
        const maxLength = this.searchWord.keyword.length - 1;
        for (const len = datas.length; dataIndex < len; dataIndex++) {
            const info = datas[dataIndex][1];
            const all = [].concat(current, info);
            if (all.length > index) {
                const currentInfo = all[index];
                if (!currentInfo) {
                    continue;
                }
                const portion = currentInfo.portion;
                const para: Paragraph = currentInfo.para;
                if  (portion.isHidden()) {
                    this.deleteOneSearch(para.id, currentInfo);
                    dataIndex--;
                    continue;
                }

                const texts = this.getAllSelectTexts(currentInfo);
                if (!texts ) {
                    this.deleteOneSearch(para.id, currentInfo);
                    dataIndex--;
                    continue;
                }

                // 下面设置选区
                // this.logicDocument.removeSelection();
                // let startIndex: number;
                // let endIndex: number;
                // let startPortion: any;
                // const text = currentInfo.paraText;
                // endIndex = portion.content.findIndex((item) => item === text);
                // const endPortionIndex = para.content.findIndex((item) => item === portion);
                // let startPortionIndex: number;
                // if (texts[0].portion !== portion) {
                //     const startObj = texts[0];
                //     startPortion = startObj.portion;
                //     const startText = startObj.text;
                //     startIndex = startPortion.content.findIndex((item) => item === startText);
                //     const startSelection = startPortion.selection;
                //     startSelection.bUse = true;
                //     startSelection.startPos = startIndex;
                //     startSelection.portionContentPos = startIndex;
                //     startSelection.endPos = startPortion.content.length;
                //     startIndex = 0;
                //     startPortionIndex = para.content.findIndex((item) => item === startPortion);
                //     for (let selIndex = startPortionIndex + 1; selIndex < endPortionIndex; selIndex++) {
                //         para.content[selIndex].selectAll(1);
                //     }
                // } else {
                //     // const portionContents = portion.content;
                //     startIndex = endIndex - maxLength;
                //     startPortionIndex = endPortionIndex;
                // }

                // const selection = portion.selection;
                // selection.bUse = true;
                // selection.startPos = startIndex;
                // selection.portionContentPos = portion.selection.endPos = endIndex + 1;
                // para.selectTopPos();
                // const paraSection = para.selection;
                // paraSection.bUse = true;
                // paraSection.startPos = startPortionIndex;
                // para.curPos.line = portion.getLineByPos(selection.portionContentPos);
                // paraSection.endPos  = para.curPos.contentPos = endPortionIndex;
                // // this.logicDocument.getSelectionBounds();
                // this.logicDocument.selection.bUse = false;
                // this.logicDocument.updateCursorXY();
                // this.logicDocument.selection.bUse = true;

                const startPos = para.getCurContentPos();
                const endPos = startPos.copy();

                const startObj = texts[0];
                const startPortion = startObj.portion;
                const startText = startObj.text;
                const startIndex: number = startPortion.content.findIndex((item) => item === startText);
                const text = currentInfo.paraText;
                const endIndex = portion.content.findIndex((item) => item === text);
                const endPortionIndex = para.content.findIndex((item) => item === portion);
                let startPortionIndex: number;
                if (texts[0].portion !== portion) {
                    startPortionIndex = para.content.findIndex((item) => item === startPortion);
                } else {
                    startPortionIndex = endPortionIndex;
                }

                startPos.add(startPortionIndex);
                startPos.add(startIndex);
                endPos.add(endPortionIndex);
                endPos.add(endIndex + 1);
                this.logicDocument.jumpToOnePosition(startPos.copy());
                this.logicDocument.selectAreaByPos(startPos, endPos);
                return ResultType.Success;
            }
            current = all;
        }

        return ResultType.Failure;
    }

    private getAllSelectTexts(info: ISearchInfo): any[] {
        const keyword = this.searchWord.keyword;
        const maxLength = keyword.length - 1;
        const portion = info.portion;
        const portions = (info.para as Paragraph).content;
        if (maxLength === 0) {
            // 当前portion可能被删除
            if (portions.findIndex((item) => item === portion) === -1) {
                return;
            }
            const oldText = info.paraText;
            // 当前text可能已经被删除
            if (portion.content.findIndex((item) => item === oldText) === -1) {
                return;
            }
            return [{text: info.paraText, portion: info.portion}];
        }

        const texts = [];
        let count = -1;
        const bPlaceholder = this.searchWord.bPlaceholder;
        let bEnd = false;
        const toLowerCase = this.searchWord.bCaseSensitive;
        for (let index = portions.length - 1; index >= 0; index--) {
            const curPortion = portions[index];
            if (curPortion.isHidden()) {
                continue;
            }
            let contents;
            if (portion === curPortion) {
                count = 1;
                const oldText = info.paraText;
                texts.unshift({text: oldText, portion});
                const oldIndex = portion.content.findIndex((item) => oldText === item);
                // 找不到缓存的text，已经被删除，说明这个info没用了
                if (oldIndex === -1) {
                    return;
                }
                contents = portion.content.slice(0, oldIndex);
            } else if (count !== -1) {
                if (bPlaceholder && curPortion.isNewControl()) {
                    if (curPortion.isNewControlStart()) {
                        bEnd = false;
                    } else if (curPortion.isPlaceHolder()) {
                        bEnd = true;
                    }
                }
                if (bEnd) {
                    continue;
                }
                contents = curPortion.content;
            } else {
                if (count === -1) {
                    continue;
                }
                return;
            }
            let curText = null;
            // const contentTexts = ;
            for (let textIndex = contents.length - 1; textIndex >= 0; textIndex--) {
                const textEle = contents[textIndex];
                const contentText = this.getContentText([textEle]);
                if (!contentText) {
                    continue;
                }

                let text1 = contentText;
                let text2 = keyword[maxLength - count];
                if (toLowerCase) {
                    text1 = text1.toLowerCase();
                    text2 = text2.toLowerCase();
                }

                if (text1 === text2) {
                    count++;
                    texts.unshift({text: textEle, portion: curPortion});
                } else {
                    return;
                }

                curText = keyword[maxLength - count];
                if (curText === undefined) {
                    break;
                }
            }

            if (curText === undefined) {
                break;
            }
        }

        if (texts.length - 1 !== maxLength) {
            return;
        }

        return texts;
    }

    private deleteOneSearch(paraId: number, info: ISearchInfo): void {
        const infos = this.searchDatas.get(paraId);
        if (infos) {
            const index = infos.findIndex((item) => item === info);
            if (index > -1) {
                infos.splice(index, 1);
                this.searchCount--;
            }
        }
    }

    private isExist(portion: any): boolean {
        if (portion.isHidden()) {
            return false;
        }

        return !!portion.paragraph.content.find((item) => item === portion);
    }

    private addSearchData(): void {
        const {text, content, portion, paraId, maxLength, searchReg, lastIndex} = this.current;
        // 有一部分是前面的portion加入的
        const lastText = text.slice(Math.max(lastIndex, 0));
        const textLength = lastText.length;
        const searchContent = lastText + content;
        let match = searchReg.exec(searchContent);
        if (!match) {
            this.setLastIndex(text + content);
            return;
        }
        const para = this.para;
        const datas: ISearchInfo[] = [];
        // let contentIndex = 0;
        // const contents = portion.content;
        let actLastIndex;
        const portionTexts = this.portionTexts;
        const subLength = maxLength - 1 - textLength;
        const toLowerCase = this.searchWord.bCaseSensitive;
        while (match) {
            actLastIndex = searchReg.lastIndex;
            // let subTextCount: number = 0;
            // if (textLength > 0 && contentIndex === 0) {
            //     subTextCount = textLength;
            // }
            // const startIndex = match.index + subTextCount;
            // 需要匹配的文本
            // const actText = searchContent.slice(startIndex, maxLength - subTextCount);
            // if (!actText) {
            //     break;
            // }

            const endIndex = match.index;
            const contentText = portionTexts[endIndex + subLength];
            if (!contentText) {
                break;
            }

            let text1 = match[0].slice(-1);
            let text2 = contentText.content;
            if (toLowerCase) {
                text1 = text1.toLowerCase();
                text2 = text2.toLowerCase();
            }
            if (text1 !== text2) {
                break;
            }

            // 获取遍历内容
            // const actContents = contents.slice(contentIndex);
            // if (!actContents) {
            //     break;
            // }

            // let actTextIndex = 0;
            // let selectedContent: any;
            // const actLen = actText.length;
            // let firstIndex: number;
            // // 在portion中获取相对应的text元素
            // for (let index = 0, len = actContents.length; index < len; index++) {
            //     const actContent = actContents[index];
            //     const actContentText = this.getContentText(actContent);
            //     if (!actContentText) {
            //         continue;
            //     }

            //     if (actContentText === actText[actTextIndex]) {
            //         actTextIndex++;
            //         if (firstIndex === undefined) {
            //             firstIndex = index;
            //         }
            //     } else {
            //         actTextIndex = 0;
            //         index = firstIndex + 1;
            //         firstIndex = undefined;
            //         continue;
            //     }

            //     // 这里找完了进行组装数据
            //     if (actTextIndex === actLen) {
            //         selectedContent = actContent;
            //         contentIndex += index;
            //         datas.push({
            //             portionId: portion.id,
            //             id: actContent.id,
            //             paraId,
            //         });
            //         this.searchCount++;
            //         break;
            //     }
            // }

            const data = {
                // id: portionTexts[endIndex].id,
                paraId,
                para,
                paraText: contentText,
                portion,
                paraTexts: [],
            };

            data.paraTexts = this.getAllSelectTexts(data);
            datas.push(data);
            // datas.push({
            //     // id: portionTexts[endIndex].id,
            //     paraId,
            //     para,
            //     paraText: contentText,
            //     portion,
            // });
            // datas['paraTexts'] = this.getAllSelectTexts(datas[datas.length - 1]);
            this.searchCount++;

            match = searchReg.exec(searchContent);
        }
        if (datas.length > 0) {
            const oldDatas = this.searchDatas.get(paraId);
            if (!oldDatas) {
                this.searchDatas.set(paraId, datas);
            } else {
                oldDatas.push(...datas);
            }
        }

        // 这里进行设置上次搜索的指针坐标
        this.setLastIndex(text + content, lastIndex + text.length - textLength);
    }

    private setLastIndex(text: string, lastIndex?: number): void {
        const {maxLength} = this.current;
        let subLength;
        if (lastIndex === undefined) {
            subLength = maxLength - 1;
        } else {
            subLength = Math.min(text.length - lastIndex, maxLength - 1);
        }

        this.current.lastIndex = text.length - subLength;
    }

    private getContentText(contents: any[]): string {
        let actContentText = '';
        let pageIndex = this.pageIndex;
        contents.forEach((content) => {
            let text: string;
            if (!content.isVisible()) {
                return;
            }
            switch (content.type) {
                case ParaElementType.ParaPageNum: {
                    if (pageIndex === undefined) {
                        this.pageIndex = pageIndex = this.para.getDocument().curPage + 1;
                    }
                    text = content.getRenderedText(true, pageIndex);
                    break;
                }
                case ParaElementType.ParaText:
                case ParaElementType.ParaTab:
                case ParaElementType.ParaSpace:
                    text = content.content;
                    break;
                case ParaElementType.ParaDrawing:
                case ParaElementType.ParaMedEquation:
                case ParaElementType.ParaBarcode:
                case ParaElementType.ParaQRCode:
                case ParaElementType.ParaNewControlBorder:
                    text = '\n';
                    break;
                default:
            }
            if (text) {
                this.portionTexts.push(content);
                actContentText += text;
            }
        });

        return actContentText;
    }
}
