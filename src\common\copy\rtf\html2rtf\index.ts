
// const customTagMap = require('./customTagMap');
// const translateStyle = require('./translateStyle');
export default class HtmlToRtf {
    // public cnUnicodes: string[];
    // public styles: Map<HTMLElement, string>;
    // public fontFamilys: Array<{name: string, isCN: boolean, associated: string, node: HTMLElement}>;
    // public fontColors: Array<{color: string, node: HTMLElement}>;
    // \\u2013-\\u2026\\u3001-\\u300F\\u3400-\\u9FBB\\uFF01-\\uFF1F
    // public cnReg: RegExp;
    // public blockTags = ['div', 'p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6'];
    // public markTags = ['b', 'i', 'u', 'sub'];
    // public customTags = ['a', 'ol', 'ul', 'li', 'center', 'table', 'td', 'th', 'tr', 'sup'];
    // constructor() {
    //     this.fontFamilys = [];
    //     this.fontColors = [];
    //     this.cnUnicodes = ['\\u0391-\\uFFE5'];
    //     // this.cnUnicodes.unshift('^\\x00-\\x80');
    //     this.cnReg = new RegExp(`[${this.cnUnicodes.join('')}]`, 'g');
    //     // console.log(this.cnReg)
    //     this.styles = new Map();
    // }

    // public matchStyle(node: HTMLElement): void {
    //     if (!node.getAttribute) {
    //         return;
    //     }
    //     const style = node.getAttribute('style');
    //     if (!style) {
    //         return;
    //     }
    //     this.getStyleRTF(style, node);
    // }

    // public getTagRTF = (node: HTMLElement, start?: string, end?: string): string => {
    //     let str = '';
    //     start = start || '';
    //     end = end || '';
    //     this.matchStyle(node);
    //     if (!node.childNodes || node.childNodes.length === 0) {
    //         const content = node.textContent || '';
    //         if (content) {

    //             const style = this.styles.get(node.parentNode as HTMLElement) || '';
    //             // console.log(style)
    //             str += (start || '{\\rtlch\\ltrch') + style + ' ' ;
    //             const text = this.encodeText(content.replace(/(\\)|(\{)|(\})/g, (str) => {
    //                 return '\\' + str;
    //             })).replace(/\s+/g, (str) => {
    //                 return new Array(str.length).join('\\~');
    //             });
    //             str += `${text}${end || '}'}`;
    //         } else if (node.tagName === 'BR') {
    //             str += '\\line';
    //         }
    //     } else {
    //         str += start;
    //         const childNodeStr = this.mapNode(node.childNodes);
    //         if (childNodeStr) {
    //             str = str + childNodeStr + end;
    //         } else {
    //             str = '';
    //         }
    //     }
    //     return str;
    // }

    // public getParentColor(node: HTMLElement): number {
    //     return this.fontColors.findIndex((color) => color.node === node);
    // }

    // public getParentFamily(node: HTMLElement): number {
    //     return this.fontFamilys.findIndex((family) => family.node === node);
    // }

    // public mapParent(node, funcName = 'getParentColor') {
    //     const index = this[funcName](node);
    //     if (index > -1) {
    //         return index;
    //     }
    //     if (node.parentNode) {
    //         return this.mapParent(node.parentNode);
    //     } else {
    //         return -1;
    //     }
    // }

    // public getStyleRTF(style: string, node: HTMLElement): void {
    //     const styleObj = this.parseStyle(style);
    //     let str = '';
    //     const fontSize = styleObj['font-size'],
    //         lineHeight = styleObj['line-height'],
    //         marginTop = styleObj['margin-top'],
    //         textIndent = styleObj['text-indent'],
    //         fontFamily = styleObj['font-family'],
    //         color = styleObj['color'];
    //     str += fontSize ? translateStyle.getFontSizeRTF(fontSize) : '';
    //     str += lineHeight ? translateStyle.getLineHeightRTF(lineHeight) : '';
    //     str += marginTop ? translateStyle.getMarginTopRTF(marginTop) : '';
    //     str += textIndent ? translateStyle.getTextIndentRTF(textIndent) : '';
    //     const sColor = this.addColor(color, node);
    //     str += sColor;

    //     const sfamily = this.addFamily(fontFamily, node);
    //     str += sfamily;

    //     this.styles.set(node, str);
    // }

    // public parseStyle(style: string): object {
    //     const styles = style.split(';');
    //     const styleObj = {};
    //     for (const stringStyle of styles) {
    //         if (stringStyle) {
    //             const splitStyle = stringStyle.split(':');
    //             styleObj[splitStyle[0].trim().toLowerCase()] = splitStyle[1].trim();
    //         }
    //     }
    //     return styleObj;
    // }

    // public mapNode(nodes: NodeListOf<any>) {
    //     let str = '';
    //     Array.from(nodes).forEach((node) => {
    //         if (node.nodeType === 8) {
    //             return;
    //         }
    //         if (!node.tagName && !node.textContent) {
    //             return;
    //         }
    //         str += this.matchTag(node);
    //     });

    //     return str;
    // }

    // public initColor(): string {
    //     const colors = this.fontColors;
    //     if (colors.length === 0) {
    //         return '';
    //     }
    //     let color = '';
    //     for (let i = 0, ii = colors.length; i < ii; i++) {
    //         color += colors[i].color + ';';
    //     }

    //     return color;
    // }

    // public initFamily(): string {
    //     const familys = this.fontFamilys;
    //     if (familys.length === 0) {
    //         return '';
    //     }
    //     let sFamily = '';
    //     for (let i = 0, ii = familys.length; i < ii; i++) {
    //         const family = familys[i];
    //         let charset = '';
    //         if (family.isCN) {
    //             charset = '\\fcharset134';
    //         }
    //         sFamily += `{\\f${i + 1}\\fbidi \\fnil${charset}\\fprq2 ${family.name}}`;
    //     }

    //     return sFamily;
    // }

    // public parse(html) {
    //     const div = document.createElement('div');
    //     div.innerHTML = html.replace(/\r|\n|&#13;/g, '');
    //     const result = this.mapNode(div.childNodes);
    //     let rtf_str = `{\\rtf1\\ansi\\deff0\\ansicpg936\\adeflang1025\n{
    // \\fonttbl{\\f0\\fbidi \\froman\\fcharset0\\fprq2{\\*\\panose 02020603050405020304}
    // Times New Roman;}${this.initFamily()}}\n{\\colortbl;${this.initColor()}}`;
    //     rtf_str = `${rtf_str}\n${result}\n\\par}`;
    //     return rtf_str;
    // }

    // private encodeText(text: string, isFont: boolean = false): string {
    //     // text = text.replace(/&emsp;&emsp;/g, '');
    //     const appendStr = isFont ? ' ?' : '\\\'3f';
    //     text = text.replace(this.cnReg, (str) => {
    //         return `\\u${str.charCodeAt(0)}${appendStr}`;
    //     });

    //     return text;
    // }

    // private matchTag(node: HTMLElement): string {
    //     let tag = (node.tagName || '').toLowerCase();
    //     let str = '';
    //     switch (tag) {
    //         case 'em':
    //             tag = 'i';
    //             break;
    //         case 'strong':
    //             tag = 'b';
    //             break;
    //         default:
    //     }
    //     if (this.blockTags.includes(tag)) {
    //         str = this.getTagRTF(node, '{\\pard ', '\\sb70\\par}');
    //     } else if (this.markTags.includes(tag)) {
    //         str = this.getTagRTF(node, `{\\${tag}`, '}');
    //     } else if (this.customTags.includes(tag)) {
    //         const custom = customTagMap[tag];
    //         custom.init && custom.init(node);
    //         str = this.getTagRTF(node, custom.start, custom.end);
    //     } else {
    //         str = this.getTagRTF(node);
    //     }
    //     return str;
    // }

    // private addColor(color: string, node: HTMLElement): string {
    //     let sColor = '';
    //     let curIndex: number;
    //     if (color && (color = translateStyle.getColorRTF(color))) {
    //         const index = this.fontColors.findIndex((item) => item.color === color);
    //         if (index === -1) {
    //             this.fontColors.push({color, node});
    //             curIndex = this.fontColors.length;
    //         } else {
    //             curIndex = index + 1;
    //         }
    //     } else if (this.fontColors.length > 0) {
    //         const index = this.mapParent(node.parentNode);
    //         if (index > -1) {
    //             curIndex = index + 1;
    //         }
    //     }
    //     if (curIndex !== undefined) {
    //         sColor = '\\cf' + curIndex;
    //     }

    //     return sColor;
    // }

    // private addFamily(fontFamily: string, node: HTMLElement): string {
    //     let sfamily = '';
    //     if (fontFamily) {
    //         const familys = fontFamily.replace(/'|"/g, '').split(/,\s*/);
    //         let firstIndex: number;
    //         let associated = '';
    //         // 暂时把关联字体去掉
    //         const len = familys.length > 0 ? 1 : 0;
    //         for (let i = 0; i < len; i++) {
    //             const name = familys[i];
    //             let index = this.fontFamilys.findIndex((font) => font.name === name);
    //             if (index === -1) {
    //                 const text =  this.encodeText(name, true);
    //                 const obj = {
    //                     name: text,
    //                     node,
    //                     associated: '',
    //                     isCN: name.length !== text.length,
    //                 };

    //                 this.fontFamilys.push(obj);
    //                 index = this.fontFamilys.length;
    //                 if (i === 0) {
    //                     firstIndex = index - 1;
    //                 }
    //             } else {
    //                 index++;
    //             }
    //             if (i !== 0) {
    //                 associated = '\\af' + index + associated; // 关联字体
    //             } else {
    //                 sfamily = '\\f' + index;
    //             }
    //         }
    //         if (associated) {
    //             if (firstIndex !== undefined) {
    //                 this.fontFamilys[firstIndex].associated = associated;
    //             }
    //             sfamily += associated;
    //         }
    //     } else if (this.fontFamilys.length > 0) {
    //         const index = this.mapParent(node.parentNode, 'getParentFamily');
    //         if (index > -1) {
    //             sfamily = '\\f' + (index + 1);
    //             const font = this.fontFamilys[index];
    //             sfamily += font.associated;
    //         }
    //     }
    //     return sfamily;
    // }
}
