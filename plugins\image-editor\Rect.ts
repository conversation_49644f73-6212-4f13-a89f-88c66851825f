import { fabric } from 'fabric';

interface IProps {
  color: string;
  size: number;
  pattern: fabric.Pattern | string;
};

export function createRect(x: number, y: number, props: IProps): fabric.Rect {
  const { color, size, pattern } = props;
  const rect = new fabric.Rect({
    left: x,
    top: y,
    width: 0,
    height: 0,
    fill: pattern,
    stroke : color,
    type : 'rect',
    strokeWidth: size,
  });
  return rect;
}

export function buildingRect(rect: fabric.Rect,originX: number, originY: number, x: number, y: number): void {
  if(originX > x) {
    rect.set({ left: Math.abs(x) });
  }
  if(originY > y){
    rect.set({ top: Math.abs(y) });
  }

  rect.set({width: Math.abs(originX - x)});
  rect.set({height: Math.abs(originY - y)});
}

export function updateRectProps(rect: fabric.Rect, props: IProps): void {
  const { color, size, pattern } = props;
  rect.set({
    stroke : color,
    fill: pattern,
    strokeWidth: size,
  });
}
