import { INum, INumberingManager, INumberingProperty, INumLvl, ResultType } from '@/common/commonDefines';
import ContentChanges, { ContentChangesElement } from '../ContentChanges';
import { DocCurPosType } from '../Document';
import DocumentContentElementBase from '../DocumentContentElementBase';import { HistroyItemType } from '../HistoryDescription';
;
import Paragraph from '../Paragraph';
import { Numbered } from './Num';
import { ChangeNumberingAddNum, ChangeNumberingRemoveNum } from './NumChanges';

// interface IDocNumbered {
//     num: Numbered;
//     paras: DocumentContentElementBase[];
// }
export class NumberingManager implements INumberingManager {
    private _doc: any;
    private _nums: Map<number, Numbered>;
    private _crateProps: INumberingProperty;
    private _docNums: Map<string, Numbered>;
    private _bStartAction: boolean;
    private contentChanges: ContentChanges;
    constructor(doc: any) {
        this._doc = doc;
        this._nums = new Map();
        this.contentChanges = new ContentChanges();
    }

    public getNum(id: number): INum {
        return this._nums.get(id);
    }

    public getLvlByPara(para: Paragraph): INumLvl {
        const numId = para.paraProperty.numPr?.numId;
        const num = this._nums.get(numId);
        if (!num) {
            return;
        }
        return num.getLvlByPara(para);
    }

    public addContentChanges( changes: ContentChangesElement ): void {
        this.contentChanges.add(changes);
    }

    public getNumByPara(para: Paragraph): INum {
        if (!para) {
            return;
        }

        const numId = para.paraProperty.numPr?.numId;
        const num = this._nums.get(numId);

        return num;
    }

    public clear(): void {
        this._nums.clear();
    }

    public get nums(): Map<number, Numbered> {
        return this._nums;
    }

    public get history(): any {
        return this._doc.getHistory();
    }

    /**
     * 打开或者插入文档时，先添加到缓存中，在初始化文档再进行
     * @param para 添加的当前段落
     * @param numId 编号id
     * @param type 编号的类型
     */
    public addDocNum(para: DocumentContentElementBase, numId: string, type: number): void {
        if (!this._docNums) {
            this._docNums = new Map();
        }
        let num = this._docNums.get(numId);
        let pos: number;
        if (!num) {
            num = new Numbered(type, this);
            this._docNums.set(numId, num);
            pos = 0;
        } else {
            pos = num.getAllLvl().length;
        }
        num.addLvl({nLvl: pos, para, type, bEnter: true});
    }

    /**
     * 文档初始化完再一次从缓存新增到正式环境中
     */
    public initDoc(): void {
        if (!this._docNums) {
            return;
        }
        const nums = this._nums;
        for (const [numId, num] of this._docNums) {
            nums.set(num.getId(), num);
        }
        this._docNums = null;
    }

    public removeLvlByPara(para: Paragraph): number {
        const pr = para.getNumberingPr();
        if (!pr) {
            return ResultType.UnEdited;
        }

        const num = this._nums.get(pr.numId);
        if (!num) {
            return ResultType.UnEdited;
        }
        this.startAction(HistroyItemType.NumberingRemoveLvl);
        const res = num.removeLvlByPara(para);
        const lvls = num.getAllLvl();
        if (lvls.length === 0) {
            this.remove(num.getId());
        }
        this.endAction();
        return res;
    }

    public createNum(props: INumberingProperty): INum {
        if (!props || !props.type) {
            return;
        }

        // 页眉页脚不能新增
        if (this._doc.curPos.type !== DocCurPosType.Content) {
            return;
        }

        // 包含表格，区域标题新增失败
        const selection = this._doc.getSelection();
        if (this._doc.isContainerTableOrTitle()) {
            return;
        }

        this.startAction(HistroyItemType.NumberingAddNum);
        const {type, bEnter} = props;
        this._crateProps = props;
        let para: DocumentContentElementBase;
        let paras: DocumentContentElementBase[];
        let paras2: DocumentContentElementBase[];
        let res: number;
        if (selection.bUse) {
            paras = this._doc.getSelectParas();
            para = paras[0];
            if (!para) {
                this.endAction();
                return;
            }
            res = this.removeNumsByEle(paras, type);
            paras2 = paras;
        } else {
            para = this._doc.getCurrentParagraph();
            if (!para || para.parent.isTableCellContent()) {
                this.endAction();
                return;
            }
            // 先删除以前项目编号组再新增
            res = this.removeNumsByEle([para], type);
            // 对行首插入新段落进行特殊处理
            if (bEnter && para.getNumberingPr() && para.isCursorAtBegin()) {
                para = para.prev || para;
            }
            paras2 = [para];
        }

        if (res === 1) { // 进行取反即可，即取消项目符号
            this._crateProps = null;
            this.endAction();
            return null;
        }

        const prev = this.getPrevPara(para);
        let nextPara = para;
        if (paras) {
            nextPara = paras[paras.length - 1];
        }

        // let numId: number;
        let sum = 0;
        let num: Numbered;
        // 优先继承前面的
        if (prev) {
            num = this.getNumByParaAndType(prev, type);
            if (num) {
                para = prev;
                sum = 1;
            } else {
                para = null;
            }
        } else {
            para = null;
        }

        // 前面的找不到再继承后面的
        if (!para) {
            const next = this.getNextPara(nextPara);
            if (next) {
                num = this.getNumByParaAndType(next, type);
                if (num) {
                    para = next;
                }
            }
        }

        let nLvl: number;
        if (num) {
            nLvl = num.getLvlPos(para) + sum;
        } else {
            num = this.addNum(type);
            nLvl = 0;
        }

        this.addLvls(num, nLvl, paras2);
        this._crateProps = null;
        this.endAction();
        return num;
    }

    public remove(numId: number): number {
        const num = this._nums.get(numId);
        if (!num) {
            return ResultType.UnEdited;
        }
        this.history.addChange(new ChangeNumberingRemoveNum(this, undefined, [num]));
        num.remove();
        this._nums.delete(numId);

        return ResultType.Success;
    }

    private getNumByParaAndType(para: DocumentContentElementBase, type: number): Numbered {
        const numId = para.getNumberingPr().numId;
        const num = this._nums.get(numId);
        if (num.getType() === type) {
            return num;
        }
        return;
    }

    private addNum(type: number): Numbered {
        const num = new Numbered(type, this);
        this._nums.set(num.getId(), num);
        this.history.addChange(new ChangeNumberingAddNum(this, undefined, [num]));
        return num;
    }

    private addLvls(num: Numbered, nLvl: number, paras: DocumentContentElementBase[]): void {
        const lvl = num.getLvl(nLvl);
        const textPro = lvl?.getTextPro()
        .copy();
        const bEnter = this._crateProps.bEnter;
        paras.forEach((item) => {
            num.addLvl({nLvl: nLvl++, type: undefined, para: item, textPro, bEnter});
        });
        num.updateLvlTexts(nLvl);
    }

    private getPrevPara(para: DocumentContentElementBase, obj?: any): DocumentContentElementBase {
        let prev = para.prev;
        if (!prev) {
            const parent = para.getParent();
            if (parent.isRegionContent()) {
                prev = parent['parent'].prev;
            }
        }
        const options = obj || {count: 0};
        while (prev) {
            if (prev.isParagraph()) {
                const pro = prev.getNumberingPr();
                if (pro) {
                    return prev;
                }
                options.count++;
            } else if (prev.isRegion()) {
                const contents = prev.getContent();
                const res = this.forEachPrevNodes(contents, options);
                if (res !== undefined) {
                    return res;
                }
            } else {
                options.count++;
            }
            if (options.count > 0) {
                return;
            }
            prev = prev.prev;
        }

        return;
    }

    private forEachPrevNodes(contents: DocumentContentElementBase[], options: any): DocumentContentElementBase {
        const element = contents[contents.length - 1];
        let res;
        if (element.isRegion()) {
            res = this.forEachPrevNodes(element.getContent(), options);
            if (res !== undefined) {
                return res;
            }
        } else if (element.isTable()) {
            options.count++;
        } else {
            const pro = element.getNumberingPr();
            if (pro) {
                return element;
            }
            options.count++;
        }
        if (options.count > 0) {
            return;
        }

        return this.getPrevPara(element, options);
    }

    private forEachNextNodes(contents: DocumentContentElementBase[], options: any): DocumentContentElementBase {
        const element = contents[0];
        let res;
        if (element.isRegion()) {
            res = this.forEachNextNodes(element.getContent(), options);
            if (res !== undefined) {
                return res;
            }
        } else if (element.isTable()) {
            options.count++;
        } else {
            const pro = element.getNumberingPr();
            if (pro) {
                return element;
            }
            options.count++;
        }

        if (options.count > 0) {
            return;
        }

        return this.getNextPara(element, options);
    }

    private getNextPara(para: DocumentContentElementBase, obj?: any): DocumentContentElementBase {
        let next = para.next;
        if (!next) {
            const parent = para.getParent();
            if (parent.isRegionContent()) {
                next = parent['parent'].next;
            }
        }
        const options = obj || {count: 0};
        while (next) {
            if (next.isParagraph()) {
                const pro = next.getNumberingPr();
                if (pro) {
                    return next;
                }
                options.count++;
            } else if (next.isRegion()) {
                const contents = next.getContent();
                const res = this.forEachNextNodes(contents, options);
                if (res !== undefined) {
                    return res;
                }
            } else {
                options.count++;
            }
            if (options.count > 0) {
                return;
            }
            next = next.next;
        }

        return;
    }

    /**
     * 根据选中的段落去掉编号
     * @param paras 段落集合
     * @return 返回删除类型 1: all编号（全部为同一个type类型，全部为编号） 2：补全
     */
    private removeNumsByEle(paras: DocumentContentElementBase[], type: number): number {
        if (this._crateProps.bEnter === true) {
            return 2;
        }

        let nums = 0;
        const numIds = {};
        let bSameType: boolean = true;
        paras.forEach((item) => {
            const pr = item.getNumberingPr();
            if (pr) {
                const id = pr.numId;
                // if (numIds[id]) {
                //     return;
                // }
                numIds[id] = true;
                const num = this._nums.get(id);
                if (!num) {
                    return;
                }
                if (num.getType() !== type) {
                    bSameType = false;
                }
                this.removeLvlByPara(item as Paragraph);
            } else {
                nums++;
            }
        });
        const keys = Object.keys(numIds);
        // keys.forEach((id) => {
        //     this.remove(+id);
        // });
        if (keys.length !== 1 || nums > 0 || bSameType !== true) {
            return 2;
        }
        return 1;
    }

    private startAction(type: number, sel?: any): void {
        this._bStartAction = this._doc.isStartAction();
        if (this._bStartAction === true) {
            return;
        }
        this._doc.startAction(type, sel);
    }

    private endAction(): void {
        if (this._bStartAction === true) {
            this._bStartAction = undefined;
            return;
        }
        return this._doc.endAction();
    }

}
