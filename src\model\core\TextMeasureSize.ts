import { ParaElementType } from './Paragraph/ParagraphContent';

// 字符测量缓存
export const CACHES = new Map<string/*字体及字号*/, Map<string/*字符*/, IRect>>();

// 常用等宽中文字体列表
export const FIXED_WIDTH_FONTS = [
    '宋体', '仿宋', '黑体', '楷体', 'STSong', 'STHeiti', 'STKaiti', 'FangSong_GB2312', 'SimSun', 'Sim<PERSON><PERSON>', 'KaiTi', 'FangSong'
];

// 检查是否是等宽中文字体
export function isFixedWidthFont(fontFamily: string): boolean {
    if (!fontFamily) return false;
    const lowerFont = fontFamily.toLowerCase();
    return FIXED_WIDTH_FONTS.some(font => lowerFont.includes(font.toLowerCase()));
}

// 字符类型判断函数
export function isChineseChar(char: string): boolean {
    return /[\u4e00-\u9fa5]/.test(char);
}

export function isChinesePunctuation(char: string): boolean {
    return /[：，。？！；、（）【】「」『』''""《》]/.test(char);
}

export function isLatinChar(char: string): boolean {
    return /[a-zA-Z]/.test(char);
}

export function isDigit(char: string): boolean {
    return /[0-9]/.test(char);
}

export function isSpace(char: string): boolean {
    return /\s/.test(char);
}

/**
 * 接口：测量字符的高宽，类型
 */
export interface IRect {
    width: number;
    height: number;
    type?: ParaElementType;
}

// 根据字体和字号获取预设值的键
export function getPresetKey(fontSize: number): string {
    // 支持的字号列表
    const fontSizes = [58.7, 48, 34.7, 32, 29.3, 24, 21.3, 20, 18.7, 16, 15, 14.7, 14, 13.3, 12, 11, 10.7];
    
    // 找到最接近的字号
    let closestSize = fontSizes[0];
    let minDiff = Math.abs(fontSize - closestSize);
    
    for (let i = 1; i < fontSizes.length; i++) {
        const diff = Math.abs(fontSize - fontSizes[i]);
        if (diff < minDiff) {
            minDiff = diff;
            closestSize = fontSizes[i];
        }
    }
    
    return closestSize.toString();
}

// 获取预设的字符测量值
export function getPresetMeasurement(char: string, fontSize: number): IRect | null {
    const key = getPresetKey(fontSize);
    const cache = PRESET_CACHES.get(key);
    
    if (cache && cache.has(char)) {
        return cache.get(char);
    }
    
    // 对于预设中没有的字符，根据类型返回合理的默认值
    if (isChineseChar(char)) {
        return {
            width: fontSize,
            height: fontSize * 1.1,
            type: ParaElementType.ParaText
        };
    } else if (isChinesePunctuation(char)) {
        return {
            width: fontSize * 0.5,
            height: fontSize * 1.1,
            type: ParaElementType.ParaText
        };
    } else if (isLatinChar(char)) {
        return {
            width: fontSize * 0.6,
            height: fontSize * 1.1,
            type: ParaElementType.ParaText
        };
    } else if (isDigit(char)) {
        return {
            width: fontSize * 0.5,
            height: fontSize * 1.1,
            type: ParaElementType.ParaText
        };
    } else if (isSpace(char)) {
        return {
            width: fontSize * 0.4,
            height: fontSize * 1.1,
            type: ParaElementType.ParaText
        };
    } else {
        return {
            width: fontSize * 0.7,
            height: fontSize * 1.1,
            type: ParaElementType.ParaText
        };
    }
}

// 预设的字符测量缓存
type MeasureCache = Map<string, IRect>;
export const PRESET_CACHES = new Map<string, MeasureCache>();

// 初始化预设缓存
export function initPresetCaches(): void {
    _initCaches();
}

// 为了兼容性提供的别名函数
export function initMeasureCaches(): void {
    _initCaches();
}

// 实际初始化函数
function _initCaches(): void {
    if (PRESET_CACHES.size > 0) {
        return;
    }

    const paraEnd = String.fromCharCode(0x21B5);
    
    // 初号 58.7
    PRESET_CACHES.set(
        '58.7', new Map([
            ['我', {width: 58.7, height: 58, type: ParaElementType.ParaText}],
            [' ', {width: 29.35, height: 58, type: ParaElementType.ParaText}],
            ['0', {width: 29.35, height: 58, type: ParaElementType.ParaText}],
            ['1', {width: 29.35, height: 58, type: ParaElementType.ParaText}],
            ['2', {width: 29.35, height: 58, type: ParaElementType.ParaText}],
            ['3', {width: 29.35, height: 58, type: ParaElementType.ParaText}],
            ['4', {width: 29.35, height: 58, type: ParaElementType.ParaText}],
            ['5', {width: 29.35, height: 58, type: ParaElementType.ParaText}],
            ['6', {width: 29.35, height: 58, type: ParaElementType.ParaText}],
            ['7', {width: 29.35, height: 58, type: ParaElementType.ParaText}],
            ['8', {width: 29.35, height: 58, type: ParaElementType.ParaText}],
            ['9', {width: 29.35, height: 58, type: ParaElementType.ParaText}],
            ['a', {width: 29.35, height: 58, type: ParaElementType.ParaText}],
            ['b', {width: 29.35, height: 58, type: ParaElementType.ParaText}],
            ['c', {width: 29.35, height: 58, type: ParaElementType.ParaText}],
            ['，', {width: 58.7, height: 58, type: ParaElementType.ParaText}],
            ['。', {width: 58.7, height: 58, type: ParaElementType.ParaText}],
            ['：', {width: 58.7, height: 58, type: ParaElementType.ParaText}],
            ['；', {width: 58.7, height: 58, type: ParaElementType.ParaText}],
            ['、', {width: 58.7, height: 58, type: ParaElementType.ParaText}],
            ['？', {width: 58.7, height: 58, type: ParaElementType.ParaText}],
            ['！', {width: 58.7, height: 58, type: ParaElementType.ParaText}],
            ['（', {width: 58.7, height: 58, type: ParaElementType.ParaText}],
            ['）', {width: 58.7, height: 58, type: ParaElementType.ParaText}],
            ['【', {width: 58.7, height: 58, type: ParaElementType.ParaText}],
            ['】', {width: 58.7, height: 58, type: ParaElementType.ParaText}],
            ['"', {width: 58.7, height: 58, type: ParaElementType.ParaText}],
            ["'", {width: 58.7, height: 58, type: ParaElementType.ParaText}],
            ['《', {width: 58.7, height: 58, type: ParaElementType.ParaText}],
            ['》', {width: 58.7, height: 58, type: ParaElementType.ParaText}],
            ['_end_', {width: 0, height: 58, type: ParaElementType.ParaText}]
        ])
    );
    

    // 小初 48
    PRESET_CACHES.set(
        '48', new Map([
            ['我', {width: 48, height: 48, type: ParaElementType.ParaText}],
            [' ', {width: 24, height: 48, type: ParaElementType.ParaText}],
            ['0', {width: 24, height: 48, type: ParaElementType.ParaText}],
            ['1', {width: 24, height: 48, type: ParaElementType.ParaText}],
            ['，', {width: 48, height: 48, type: ParaElementType.ParaText}],
            ['。', {width: 48, height: 48, type: ParaElementType.ParaText}],
            ['：', {width: 48, height: 48, type: ParaElementType.ParaText}],
            [paraEnd, {width: 0, height: 48, type: ParaElementType.ParaText}]
        ])
    );

    // 一号 34.7
    PRESET_CACHES.set(
        '34.7', new Map([
            ['我', {width: 34.7, height: 35, type: ParaElementType.ParaText}],
            [' ', {width: 17.35, height: 35, type: ParaElementType.ParaText}],
            ['0', {width: 17.35, height: 35, type: ParaElementType.ParaText}],
            ['1', {width: 17.35, height: 35, type: ParaElementType.ParaText}],
            ['，', {width: 34.7, height: 35, type: ParaElementType.ParaText}],
            ['。', {width: 34.7, height: 35, type: ParaElementType.ParaText}],
            ['：', {width: 34.7, height: 35, type: ParaElementType.ParaText}],
            [paraEnd, {width: 0, height: 35, type: ParaElementType.ParaText}]
        ])
    );

    // 小一 32
    PRESET_CACHES.set(
        '32', new Map([
            ['我', {width: 32, height: 32, type: ParaElementType.ParaText}],
            [' ', {width: 16, height: 32, type: ParaElementType.ParaText}],
            ['0', {width: 16, height: 32, type: ParaElementType.ParaText}],
            ['1', {width: 16, height: 32, type: ParaElementType.ParaText}],
            ['，', {width: 32, height: 32, type: ParaElementType.ParaText}],
            ['。', {width: 32, height: 32, type: ParaElementType.ParaText}],
            ['：', {width: 32, height: 32, type: ParaElementType.ParaText}],
            [paraEnd, {width: 0, height: 32, type: ParaElementType.ParaText}]
        ])
    );
    
    // 二号 29.3
    PRESET_CACHES.set(
        '29.3', new Map([
            ['我', {width: 29.3, height: 29, type: ParaElementType.ParaText}],
            [' ', {width: 14.65, height: 29, type: ParaElementType.ParaText}],
            ['0', {width: 14.65, height: 29, type: ParaElementType.ParaText}],
            ['1', {width: 14.65, height: 29, type: ParaElementType.ParaText}],
            ['，', {width: 29.3, height: 29, type: ParaElementType.ParaText}],
            ['。', {width: 29.3, height: 29, type: ParaElementType.ParaText}],
            ['：', {width: 29.3, height: 29, type: ParaElementType.ParaText}],
            [paraEnd, {width: 0, height: 29, type: ParaElementType.ParaText}]
        ])
    );
    
    // 小二 24
    PRESET_CACHES.set(
        '24', new Map([
            ['我', {width: 24, height: 24, type: ParaElementType.ParaText}],
            [' ', {width: 12, height: 24, type: ParaElementType.ParaText}],
            ['0', {width: 12, height: 24, type: ParaElementType.ParaText}],
            ['1', {width: 12, height: 24, type: ParaElementType.ParaText}],
            ['，', {width: 24, height: 24, type: ParaElementType.ParaText}],
            ['。', {width: 24, height: 24, type: ParaElementType.ParaText}],
            ['：', {width: 24, height: 24, type: ParaElementType.ParaText}],
            [paraEnd, {width: 0, height: 24, type: ParaElementType.ParaText}]
        ])
    );
    
    // 三号 21.3
    PRESET_CACHES.set(
        '21.3', new Map([
            ['我', {width: 21.3, height: 21, type: ParaElementType.ParaText}],
            [' ', {width: 10.65, height: 21, type: ParaElementType.ParaText}],
            ['0', {width: 10.65, height: 21, type: ParaElementType.ParaText}],
            ['1', {width: 10.65, height: 21, type: ParaElementType.ParaText}],
            ['，', {width: 21.3, height: 21, type: ParaElementType.ParaText}],
            ['。', {width: 21.3, height: 21, type: ParaElementType.ParaText}],
            ['：', {width: 21.3, height: 21, type: ParaElementType.ParaText}],
            [paraEnd, {width: 0, height: 21, type: ParaElementType.ParaText}]
        ])
    );
    
    // 小三 20
    PRESET_CACHES.set(
        '20', new Map([
            ['我', {width: 20, height: 20, type: ParaElementType.ParaText}],
            [' ', {width: 10, height: 20, type: ParaElementType.ParaText}],
            ['0', {width: 10, height: 20, type: ParaElementType.ParaText}],
            ['1', {width: 10, height: 20, type: ParaElementType.ParaText}],
            ['，', {width: 20, height: 20, type: ParaElementType.ParaText}],
            ['。', {width: 20, height: 20, type: ParaElementType.ParaText}],
            ['：', {width: 20, height: 20, type: ParaElementType.ParaText}],
            [paraEnd, {width: 0, height: 20, type: ParaElementType.ParaText}]
        ])
    );
    
    // 四号 18.7
    PRESET_CACHES.set(
        '18.7', new Map([
            ['我', {width: 18.7, height: 19, type: ParaElementType.ParaText}],
            [' ', {width: 9.35, height: 19, type: ParaElementType.ParaText}],
            ['0', {width: 9.35, height: 19, type: ParaElementType.ParaText}],
            ['1', {width: 9.35, height: 19, type: ParaElementType.ParaText}],
            ['，', {width: 18.7, height: 19, type: ParaElementType.ParaText}],
            ['。', {width: 18.7, height: 19, type: ParaElementType.ParaText}],
            ['：', {width: 18.7, height: 19, type: ParaElementType.ParaText}],
            [paraEnd, {width: 0, height: 19, type: ParaElementType.ParaText}]
        ])
    );
    
    // 小四 16
    PRESET_CACHES.set(
        '16', new Map([
            ['我', {width: 16, height: 16, type: ParaElementType.ParaText}],
            [' ', {width: 8, height: 16, type: ParaElementType.ParaText}],
            ['0', {width: 8, height: 16, type: ParaElementType.ParaText}],
            ['1', {width: 8, height: 16, type: ParaElementType.ParaText}],
            ['，', {width: 16, height: 16, type: ParaElementType.ParaText}],
            ['。', {width: 16, height: 16, type: ParaElementType.ParaText}],
            ['：', {width: 16, height: 16, type: ParaElementType.ParaText}],
            [paraEnd, {width: 0, height: 16, type: ParaElementType.ParaText}]
        ])
    );
    
    // 五号 14
    PRESET_CACHES.set(
        '14', new Map([
            ['我', {width: 14, height: 14, type: ParaElementType.ParaText}],
            [' ', {width: 7, height: 14, type: ParaElementType.ParaText}],
            ['0', {width: 7, height: 14, type: ParaElementType.ParaText}],
            ['1', {width: 7, height: 14, type: ParaElementType.ParaText}],
            ['，', {width: 14, height: 14, type: ParaElementType.ParaText}],
            ['。', {width: 14, height: 14, type: ParaElementType.ParaText}],
            ['：', {width: 14, height: 14, type: ParaElementType.ParaText}],
            [paraEnd, {width: 0, height: 14, type: ParaElementType.ParaText}]
        ])
    );
    
    // 小五 12
    PRESET_CACHES.set(
        '12', new Map([
            ['我', {width: 12, height: 12, type: ParaElementType.ParaText}],
            [' ', {width: 6, height: 12, type: ParaElementType.ParaText}],
            ['0', {width: 6, height: 12, type: ParaElementType.ParaText}],
            ['1', {width: 6, height: 12, type: ParaElementType.ParaText}],
            ['，', {width: 12, height: 12, type: ParaElementType.ParaText}],
            ['。', {width: 12, height: 12, type: ParaElementType.ParaText}],
            ['：', {width: 12, height: 12, type: ParaElementType.ParaText}],
            [paraEnd, {width: 0, height: 12, type: ParaElementType.ParaText}]
        ])
    );
    
    // 八号 10.7
    PRESET_CACHES.set(
        '10.7', new Map([
            ['我', {width: 10.7, height: 11, type: ParaElementType.ParaText}],
            [' ', {width: 5.35, height: 11, type: ParaElementType.ParaText}],
            ['0', {width: 5.35, height: 11, type: ParaElementType.ParaText}],
            ['1', {width: 5.35, height: 11, type: ParaElementType.ParaText}],
            ['，', {width: 10.7, height: 11, type: ParaElementType.ParaText}],
            ['。', {width: 10.7, height: 11, type: ParaElementType.ParaText}],
            ['：', {width: 10.7, height: 11, type: ParaElementType.ParaText}],
            [paraEnd, {width: 0, height: 11, type: ParaElementType.ParaText}]
        ])
    );
}