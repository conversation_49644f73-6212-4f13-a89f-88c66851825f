module.exports = {
    a: {
        init: function(node) {
            let link = node.getAttribute('href') || '';
            this.start = this.start.replace('${link}',link);
        },
        start: '{\\field{\\*\\fldinst{HYPERLINK "${link}"}}{\\fldrslt',
        end: '}}'
    },
    ol: {
        start:'{{\\*\\pn\\pnlvlbody\\pnf0\\pnindent0\\pnstart1\\pndec{\\pntxta.}}\\fi-360\\li720\\sa200\\sl276\\slmult1',
        end: '}'
    },
    ul: {
        start: '{{\\*\\pn\\pnlvlblt\\pnf1\\pnindent0{\\pntxtb\\\'B7}}\\fi-360\\li720\\sa200\\sl276\\slmult1\\lang22\\f0\\fs22',
        end: '}'
    },
    li: {
        start: '{\\pntext\\tab}',
        end: '\\par'
    },
    center: {
        start: '{\\pard\\qr',
        end: '\\par}'
    },
    table: {
        start: '{',
        // init() {

        // },
        end: '}'
    },
    td: {
        start: '{\\pard\\intbl\\qc',
        end: '\\cell}'
    },
    th: {
        start: '{\\pard\\intbl\\qc',
        end: '\\cell}'
    },
    tr: {
        start: '{\\trowd\\trgaph10',
        end: '\\row}'
    },
    sup: {
        start: '{\\super',
        end: '}'
    }
};