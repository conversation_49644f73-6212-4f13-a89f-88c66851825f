import { EmrEditor } from '../../components/editor/Main';
import { ResultType } from '../commonDefines';
export default class ListenEvent {
    private _host: EmrEditor;
    constructor(host: EmrEditor) {
        this._host = host;
    }

    /**
     * 文件监听器，影响nsoFileOpenCompleted事件
     */
    public addFileListen(): number {
        if (this._host.isFileEnabled() === true) {
            return ResultType.UnEdited;
        }
        this._host.setFileEnabled(true);
        return ResultType.Success;
    }

    /**
     * 结构化元素监听器，影响nsoStructChanged nsoStructClick nsoStructDBClick nsoStructGainFocus  nsoStructLostFocus 事件
     * @param nRev 预留参数，暂时没用
     */
    public addStructListen(nRev?: number): number {
        if (this._host.isStructEnabled() === true) {
            return ResultType.UnEdited;
        }
        this._host.setStructEnabled(true);
        return ResultType.Success;
    }

    /**
     * 键盘监听器 影响nsoKeyPressedEvent 事件
     * @param nRev 预留参数，暂时没用
     */
    public addKeyListen(nRev?: number): number {
        if (this._host.isKeyEnabled() === true) {
            return ResultType.UnEdited;
        }
        this._host.setKeyEnabled(true);
        return ResultType.Success;
    }

    /**
     * 移除File监听器
     */
    public removeFileListen(): number {
        if (this._host.isFileEnabled() === false) {
            return ResultType.UnEdited;
        }
        this._host.setFileEnabled(false);
        return ResultType.Success;
    }

    /**
     * 移除Struct监听器
     */
    public removeStructListen(): number {
        if (this._host.isStructEnabled() === false) {
            return ResultType.UnEdited;
        }
        this._host.setStructEnabled(false);
        return ResultType.Success;
    }

    /**
     * 移除键盘监听器
     */
    public removeKeyListen(): number {
        if (this._host.isKeyEnabled() === false) {
            return ResultType.UnEdited;
        }
        this._host.setKeyEnabled(false);
        return ResultType.Success;
    }

    /**
     * 移除所有的监听器。
     */
    public removeAllListen(): number {
        this._host.removeAllListen();
        return ResultType.Success;
    }
}
