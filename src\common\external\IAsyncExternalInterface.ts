
/**
 * @apiDefine 1_documentAPI 文档操作
 */
/**
 * @apiDefine 2_layoutAPI 布局设置
 */
/**
 * @apiDefine 3_printAPI 打印设置
 */
/**
 * @apiDefine 4_headerfooterAPI 页眉页脚
 */
/**
 * @apiDefine 5_menuAPI 菜单工具栏
 */
/**
 * @apiDefine 6_PropertAPI 属性操作
 */
/**
 * @apiDefine 7_insertAPI 插入合并操作
 */
/**
 * @apiDefine 8_editAPI 编辑类操作
 */
/**
 * @apiDefine 81_copyAPI 拷贝粘贴
 */
/**
 * @apiDefine 82_cursorAPI 光标位置
 */
/**
 * @apiDefine 83_imageAPI 图片
 */
/**
 * @apiDefine 84_formulaAPI 医学公式
 */
/**
 * @apiDefine 85_revisionAPI 修订与批注
 */
/**
 * @apiDefine 86_tableAPI 表格
 */
/**
 * @apiDefine 90_regionAPI 区域
 */
/**
 * @apiDefine 91_structAPI 结构化元素
 */
/**
 * @apiDefine 92_checkAPI 勾选框
 */
/**
 * @apiDefine 93_textboxAPI 文本框与节
 */
/**
 * @apiDefine 94_dateAPI 日期框
 */
/**
 * @apiDefine 95_numberAPI 数字框
 */
/**
 * @apiDefine 96_droplistAPI 下拉框
 */
/**
 * @apiDefine 97_signAPI 签名控件
 */
/**
 * @apiDefine 9999_eventAPI 事件
 */
/**
 * @apiDefine A_httpAPI 编辑器后台服务
 */
/**
 * @apiDefine B_SomethingAbout 附录
 */
export default interface IAsyncExternalInterface {
  /**
       * @api {function} none 1.createNew
       * @apiName createNew
       * @apiGroup 1_documentAPI
       * @apiVersion  1.0.0
       *@apiDescription createNew(): number
       * <p>新建一片文档  初始文档为A4 ，页边距均为默认值，无页眉页脚。无参数。</p>
       @apiExample  {javascript} Example:
       * editorFunction.createNew();
       *@apiSuccessExample {number} Success:
       *      0
      *@apiErrorExample {number} Error( 所有接口的错误返回值都遵循 ):
      *1	新增、修改、设置错误
      *2	设置的值相同，不进行修改
      *NaN 返回的值undefine
      *''	返回字符串类型时，空字符串表示错误
      *3	参数错误
      *4	废弃
      *5	打开文件失败，编辑器此时会下载文档
      *6	非法操作 （比如在文本框中插入文本框）
      *-1	失败
       */
  createNew(): Promise<number>;

  /**
   * @api {function} none 2.openDocumentWithString
   * @apiName openDocumentWithString
   * @apiGroup 1_documentAPI
   * @apiVersion  1.0.0
   * @apiDescription openDocumentWithString(path: string, mode: string):number
   * <p>基于base64编码的string，打开病历</p>
   * @apiparam {string} path 文件的base64的串值。
   * @apiparam {json} mode json格式的选项
   * @apiParamExample {json} mode
   * {
   * operate:'new',//'new':打开文书(重置计数统计); 'open':打开文书（不重置计数). 默认:'open' 。
   *               //每次save的时候， 文件头的计数次数会加+1.
   *               //建议基于模板新建文书的时候使用new，后面打开使用open.
   * mode:'normal',//'normal':正常模式; 'readonly':全文只读;'strict':严格模式; 默认:'normal'
   * page:'A4',   //'A4','A5' ... 固定格式；
   *              //自定义格式:"custom,width=11.1,height=17.2",单位cm
   *              //不指定,则为默认值，按照原文默认页面样式打开
   *              //建议不要指定该参数，制作模板的时确定文档格式。
   * view:'page', //'web':web模式; 'page':分页模式; 默认为'page'
   * defaultFont:'Font=宋体,Size=11', //'Font=宋体,Size=11' or 'Font=宋体，Size=小五'
   *                                  //设置缺省字体样式. 当Size为数字的时候，单位为pt
   *                                  //注意：只能修改默认的字体样式。设置过字体的样式的依旧采用原设置
   * regionTitleFont:'Font=宋体,Size=11',//'Font=宋体,Size=11'
   *                                   //统一设置域标题的字体样式，
   *                                   //文档中所有的区域的标题的字体样式采用传递进来的格式进行进行设置。
   * linespace:1.5,   //1，2，3 等, 设置默认的行距. 修改过的行距依旧采用原设置。
   * disableSaveDialog:true,  //true:屏蔽关闭文档的时候的二次确认对话框 false:保持现状.
   * cleanMode：true, //true:清洁浏览; false:不切换
   * fileFormat:'apo',    //'apo':默认格式；'txt': txt格式
   * viewProportion:100,  //视图比例参数，设置范围为50-200
   * bLoadCache:false,//true:启用区域缓存加载；false:默认，不启用
   * printCellBgColor:false //true:打印表格单元格的背景色，包括模态表格的表头颜色。false:为不打印。默认为true
   * }
   * @apiExample  {javascript} Example:
   * {
   *    var sContent =base64Str;
   *    var sModeJson = {mode:'normal'};
   *    editorFunction.openDocumentWithString(base64Str,sModeJson);
   * }
   * *@apiSuccessExample {number} Success:
   *      0
   *@apiErrorExample {number} Error:
   *       非0
  */
  openDocumentWithString(path: string, mode: string): Promise<number>;

  /**
       * @api {function} none 3.openDocumentWithStream
       * @apiName openDocumentWithStream
       * @apiGroup 1_documentAPI
       * @apiVersion  1.0.0
       * @apiDescription openDocumentWithStream(content: Blob, mode: string): number
       * <p>基于blob数据，打开病历。</p>
       * @apiparam {blob} content 文件的blob。
       * @apiparam {json} mode json格式的选项
       * @apiParamExample {json} mode
       * {
       * operate:'new',//'new':打开文书(重置计数统计); 'open':打开文书（不重置计数). 默认:'open' 。
       *               //每次save的时候， 文件头的计数次数会加+1.
       *               //建议基于模板新建文书的时候使用new，后面打开使用open.
       * mode:'normal',//'normal':正常模式; 'readonly':全文只读;'strict':严格模式; 默认:'normal'
       * page:'A4',   //'A4','A5' ... 固定格式；
       *              //自定义格式:"custom,width=11.1,height=17.2",单位cm
       *              //不指定,则为默认值，按照原文默认页面样式打开
       *              //建议不要指定该参数，制作模板的时确定文档格式。
       * view:'page', //'web':web模式; 'page':分页模式; 默认为'page'
       * defaultFont:'Font=宋体,Size=11', //'Font=宋体,Size=11' or 'Font=宋体，Size=小五'
       *                                  //设置缺省字体样式. 当Size为数字的时候，单位为pt
       *                                  //注意：只能修改默认的字体样式。设置过字体的样式的依旧采用原设置
       * regionTitleFont:'Font=宋体,Size=11',//'Font=宋体,Size=11'
       *                                   //统一设置域标题的字体样式，
       *                                   //文档中所有的区域的标题的字体样式采用传递进来的格式进行进行设置。
       * linespace:1.5,   //1，2，3 等, 设置默认的行距. 修改过的行距依旧采用原设置。
       * disableSaveDialog:true,  //true:屏蔽关闭文档的时候的二次确认对话框 false:保持现状.
       * cleanMode：true, //true:清洁浏览; false:不切换
       * fileFormat:'apo',    //'apo':默认格式；'txt': txt格式
       * viewProportion:100,  //视图比例参数，设置范围为50-200
       * bLoadCache:false,//true:启用区域缓存加载；false:默认，不启用
       * printCellBgColor:false //true:打印表格单元格的背景色，包括模态表格的表头颜色。false:为不打印。默认为true
       * }
       * @apiExample  {javascript} Example:
       * {
       *   getfilecontentFromServer(getIntValueFromString(name)) //从数据库抽取content
       *   .then(blob => {
       *     // 在这里处理blob对象
       *     //const blob2 = new Blob([blob], { type: 'application/apollo-zstd' });//如果content里没有MIME-type 需要追加
       *     // 这里你可以进一步处理这个Blob对象，比如编辑器直接打开
       *     editorFunction.openDocumentWithStream(blob);
       *   })
       *   .catch(error => {
       *     console.error('Error fetching blob:', error);
       *   });
       * }
       * *@apiSuccessExample {number} Success:
       *      0
       *@apiErrorExample {number} Error:
       *       非0
      */
  openDocumentWithStream(content: Blob, mode: string): Promise<number>;

  /**
   * @api {function} none 4.saveToString
   * @apiName saveToString
   * @apiGroup 1_documentAPI
   * @apiVersion  1.0.0
   * @apiDescription saveToString():string
   * <p>保存文件为base64串值。</p>
  * @apiExample  {javascript} Example:
  * {
  *   editorFunction.saveToString().then(base64String => {
  *    // 在这里处理返回的base64数据
  *   const contentType = "application/apollo-zstd";
  *   const blob = base64ToBlob(base64String, contentType);
  *   console.log(blob); // 输出 Blob 对象
  *  }).catch(error => {
  *    // 如果出现错误，可以在这里处理错误
  *    console.error("error:", error);
  *  });
  *
  *   // 将 base64 字符串转换为 Blob 对象
  *   function base64ToBlob(base64String, contentType) {
  *       const binaryString = atob(base64String); // 解码 base64 字符串为二进制数据
  *       const byteArray = new Uint8Array(binaryString.length);
  *       for (let i = 0; i < binaryString.length; i++) {
  *           byteArray[i] = binaryString.charCodeAt(i);
  *   }
  *   return new Blob([byteArray], { type: contentType });
  *}
  * @apiSuccessExample {string} Success:
  *      Promise<string>
*/
  saveToString(): Promise<string>;

  /**
      * @api {function} none 5.saveToStream
       * @apiName saveToStream
       * @apiGroup 1_documentAPI
       * @apiVersion  1.0.0
       * @apiDescription saveToStream(sModJson: string):Blob
       * <p>保存文件为Blob数据。</p>
       * @apiparam {json} sModeJson json格式的选项
       * @apiParamExample {json} sModeJson
       * {
       * NeedsignalContent:1, // 0:不需要; 1:需要. 默认是1
       * NeedRevision:0,      //0 :不带修订; 1:带修订. 默认是1
       * cleanElementMode:1, //1：元素name；2：元素的serialNumber  如果不需要该功能，则不要传递。保存的时候是否需要清除特定元素的内容。
       * cleanElementArray:['aaa','bbb'] //字符串数组， 与cleanElementMode 配合使用。
       * }
       * @apiExample  {javascript} Example:
       * {
      * editorFunction.saveToStream(sJson).then(blob => {
      *    // 在这里处理返回的 Blob 数据 创建一个下载链接并模拟点击下载
      *    const blobUrl = URL.createObjectURL(blob);
      *    const link = document.createElement('a');
      *    link.href = blobUrl;
      *    link.download = 'file.apo';
      *    link.click();
      *  }).catch(error => {
      *    // 如果出现错误，可以在这里处理错误
      *    console.error("error:", error);
      *  });
      *
      * // 将 Blob 对象转换为 base64 格式
      *function blobToBase64(blob) {
      *return new Promise((resolve, reject) => {
      *    const reader = new FileReader();
      *    reader.onload = () => {
      *        const base64Data = reader.result.split(',')[1];
      *        resolve(base64Data);
      *    };
      *    reader.onerror = reject;
      *    reader.readAsDataURL(blob);
      *});
  }
       * }
       * *@apiSuccessExample {blob} Success:
       *      Promise<Blob>
      */
  saveToStream(sModJson: string): Promise<Blob>;

  /**
  * @api {function} none 6.saveStructContentToStream
   * @apiName saveStructContentToStream
   * @apiGroup 1_documentAPI
   * @apiVersion  1.0.0
   * @apiDescription saveStructContentToStream(name: string):Blob
   * <p>保存指定结构为Blob数据 包含结构本身</p>
   * @apiparam {string} name 结构元素name
   * @apiSuccessExample {blob} Success:
   *      Promise<Blob>
   */
  saveStructContentToStream(name: string): Promise<Blob>;

  /**
  * @api {function} none 7.saveStructContentToStreamByArray
   * @apiName saveStructContentToStreamByArray
   * @apiGroup 1_documentAPI
   * @apiVersion  1.0.0
   * @apiDescription saveStructContentToStreamByArray(sNameJson: string): 'Map<string, Blob>'
   * <p>保存指定多个结构保存为Blob数据</p>
   * @apiparam {json} sNameJson 多个结构元素name的串值
   * @apiParamExample {json} sNameJson
   * [
   *  'textedit1','textedit2'
   * ]
   * @apiSuccessExample {blob} Success:
   *      Promise<Map<string, Blob>>
   */
  saveStructContentToStreamByArray(sNameJson: string): Promise<Map<string, Blob>>;

  /**
  * @api {function} none 8.saveSelectAreaToString
   * @apiName saveSelectAreaToString
   * @apiGroup 1_documentAPI
   * @apiVersion  1.0.0
   * @apiDescription saveSelectAreaToString():'string'
   * <p>保存选中区域的内容为一个子文档（base64串值)</p>
   * @apiSuccessExample {string} Success:
   *     Promise<string> Base64编码的文档内容
   */
  saveSelectAreaToString(): Promise<string>;

  /**
   * @api {function} none 9.saveSelectAreaToStream
   * @apiName saveSelectAreaToStream
   * @apiGroup 1_documentAPI
   * @apiVersion  1.0.0
   * @apiDescription saveSelectAreaToStream(sModJson: string): Blob
   * <p>保存选中区域的内容为一个子文档（blob）</p>
   * @apiParam {String} [sModJson] JSON格式的选项配置，可选参数。如果不传递则使用默认值
   * @apiParamExample {json} sModeJson参数示例:
   * {
   *   "NeedsignalContent": 1,     // 0:不保留签名内容; 1:保留签名内容(默认)
   *   "NeedRevision": 0,          // 0:不保留修订信息(默认); 1:保留修订信息
   *   "NeedStruct": 1,            // 0:不保留结构化元素，只保留纯文本; 1:保留所有结构化元素(默认); 2:保留结构化元素但不保留区域
   *   "NeedHeaderFooter": 0,      // 0:不保留页眉页脚(默认); 1:保留页眉页脚
   *   "cleanElementMode": 1,      // 可选，清除元素的模式: 1:按元素name清除; 2:按元素serialNumber清除
   *   "cleanElementArray": ["aaa", "bbb"]  // 可选，字符串数组，与cleanElementMode配合使用，指定要清除的元素
   * }
   * @apiSuccessExample {Blob} Success:
   *     Promise<Blob> 返回包含选中区域内容的Blob对象
   */
  saveSelectAreaToStream(sModJson: string): Promise<Blob>;

  /**
   * 设置服务端url
   * @param url 服务端url
   */
  setRemoteUrl(url: string): Promise<number>;

  /**
   * @api {function} none setHttpSvrUrl
   * @apiName setHttpSvrUrl
   * @apiGroup 1_documentAPI
   * @apiVersion  1.0.0
   * @apiDescription setHttpSvrUrl(url: string):'number'
   * <p>设置常规http服务url</p>
   * @apiparam {string} url 常规服务端的url 比如:http://********:8080
   * @apiSuccessExample {number} Success:
   *     无
   */
  setHttpSvrUrl(url: string): Promise<number>;

    /**
   * @api {function} none setOfdSvrUrl
   * @apiName setOfdSvrUrl
   * @apiGroup 1_documentAPI
   * @apiVersion  1.0.0
   * @apiDescription setOfdSvrUrl(url: string):'number'
   * <p>设置ofd的http服务url</p>
   * @apiparam {string} url ofd服务端的url 比如:http://********:9090
   * @apiSuccessExample {number} Success:
   *     无
   */
    setOfdSvrUrl(url: string): Promise<number>;

  /**
   * @api {function} none exportToOtherFormatWithStream
   * @apiName exportToOtherFormatWithStream
   * @apiGroup 1_documentAPI
   * @apiVersion  1.0.0
   * @apiDescription exportToOtherFormatWithStream(nFormatType: number): Blob
   * <p>把当前打开的文件,导出成其他格式的文件，比如HTML.</p>
   * @apiparam {number} nFormatType 导出文件的类型
   * @apiParamExample {string} nFormatType
   * 2 html
   * 3 text
   * 4 docx
   * 5 excel
   * 6 清洁模式html
   * 该接口不能直接导出pdf，需要借助后台服务。
   * @apiExample  {javascript} Example:
   * {
   * editorFunction.exportToOtherFormatWithStream(2).then(blob => {
   *   // 在这里处理返回的 Blob 数据 创建一个下载链接并模拟点击下载
   *   const blobUrl = URL.createObjectURL(blob);
   *   const link = document.createElement('a');
   *   link.href = blobUrl;
   *   link.download = 'file.html';
   *   link.click();
   * }).catch(error => {
   *   // 如果出现错误，可以在这里处理错误
   *   console.error("convert html error:", error);
   * });
   * }
   * @apiSuccessExample {blob} Success:
   *     Promise<Blob>
   */
  exportToOtherFormatWithStream(nFormatType: number): Promise<Blob>;

  /**
   * @api {function} none close
   * @apiName close
   * @apiGroup 1_documentAPI
   * @apiVersion  1.0.0
   * @apiDescription close()
   * <p>关闭当前打开的病历</p>
 */
  close(): Promise<void>;

  /**
   * @api {function} none setDebugMode
   * @apiName setDebugMode
   * @apiGroup 1_documentAPI
   * @apiVersion  1.0.0
   * @apiDescription setDebugMode(flag: boolean)
   * <p>开启日志模式</p>
   * @apiparam {boolean} flag 是否开启日志模式：true: 开启；false: 关闭；
   */
  setDebugMode(flag: boolean): Promise<void>;

  /**
   * @api {function} none setEditMode
   * @apiName setEditMode
   * @apiGroup 2_layoutAPI
   * @apiVersion  1.0.0
   * @apiDescription setEditMode(nType: number):number
   * <p>切换编辑模式</p>
   * @apiparam {number} nType 1：正常模式 2：只读模式 3：严格模式
   * @apiSuccessExample {number} Success:
   *     number
 */
  setEditMode(nType: number): Promise<number>;

  //该接口已废弃，不对外暴露
  setViewMode(nType: number): Promise<number>;

  /**
   * @api {function} none enableAdministratorMode
   * @apiName enableAdministratorMode
   * @apiGroup 1_documentAPI
   * @apiVersion  1.0.0
   * @apiDescription enableAdministratorMode(bFlag: boolean):number
   * <p>开启管理员模式</p>
   * @apiparam {boolean} bFlag 是否开启管理员模式： true: 开启；false: 关闭
 */
  enableAdministratorMode(bFlag: boolean): Promise<number>;

  /**
   * @api {function} none setMenuBarVisible
   * @apiName setMenuBarVisible
   * @apiGroup 5_menuAPI
   * @apiVersion  1.0.0
   * @apiDescription setMenuBarVisible(bVisible: boolean):number
   * <p>显示隐藏菜单栏</p>
   * @apiparam {boolean} bVisible 显示菜单控制标志 true: 显示；false: 隐藏
   *  @apiSuccessExample {boolean} Success:
   *     boolean
 */
  setMenuBarVisible(bVisible: boolean): Promise<boolean>;

  /**
   * @api {function} none disableMenuItem
   * @apiName disableMenuItem
   * @apiGroup 5_menuAPI
   * @apiVersion  1.0.0
   * @apiDescription disableMenuItem(sJson: string):number
   * <p>显示和隐藏指定的菜单项</p>
   * @apiparam {json} sJson 右键菜单设置项集合
   * @apiParamExample {json} sJson
   * {
   *   "0": true,//true 显示 false 隐藏
   *   "1": false,
   *   "2": false
   * }
   * @apiExample {string} 菜单项对应的值
   * 剪切 (cut)	0
  *拷贝 (copy)	1
  *粘贴 (paste)	2
  *删除（delete）	4
  *字体（font）	5
  *段落（paragraph）	6
  *图片属性（image）	7
  *医学公式（formula）	8
  *合并单元格（merge）	9
  *拆分单元格（split）	10
  *插入（insertRow）	11
  *删除行（deleteRow）	12
  *删除列（deleteCol）	13
  *表格属性（table）	14
  *单元格（cell）	15
  *编辑图片（editImage）	32
  *结构化元素(structs)	30
  *区域（region）	31
  *撤销（undo）	33
  *重做（redo）	34
  *删除区域（deleteRegion）	35
  *插入-上行（insertTopRow）	16
  *插入–下行（insertBottomRow）	17
  *插入-左列（insertLeftCol）	18
  *插入-右列（insertRightCol）	19
  *刷新（refresh）	100
  *帮助 (helper)
   *  @apiSuccessExample {string} Success:
   *     返回 number 0 成功 1 失败。 失败的原因是：没有找到对应的菜单项。
   *  editor.disableMenuItem(JSON.stringify({
   * "helper": true  // true -- 屏蔽 false -- 显示
   * }));
 */
  disableMenuItem(sJson: string): Promise<number>;


  /**
 * @api {function} none showSystemDialog
 * @apiName showSystemDialog
 * @apiGroup 5_menuAPI
 * @apiVersion 1.0.0
 * @apiDescription 显示编辑器系统对话框 需要手工关闭
 *
 * @apiparam {number} nType 对话框类型，可选值：
 *   - 1: 字体对话框
 *   - 2: 段落对话框
 *   - 3: 表格属性对话框
 *   - 4: 医学表达式对话框
 *   - 5: 特殊字符对话框
 *
 *
 * @apiExample {javascript} 示例:
 *   // 显示医学表达式对话框
 *   editor.showSystemDialog(4);
 *  //返回操作结果，0表示成功，非0表示失败
 *
 */
showSystemDialog(nType: number): Promise<number>;

  /**
   * @api {function} none showToolBarItem
   * @apiName showToolBarItem
   * @apiGroup 5_menuAPI
   * @apiVersion  1.0.0
   * @apiDescription showToolBarItem(sJson: string):number
   * <p>显示和隐藏指定的工具栏项</p>
   * @apiparam {json} sJson 工具栏设置项
   * @apiParamExample {json} sJson
   * {
   *   "cut": true,//true 显示 false 隐藏
   *   "paste": false,
   * }
   * @apiExample {string} 菜单项对应的值
   * 功能 与 disableMenuItem 类似 传递参数写法不一样。
   *撤销	"undo"
  *回退	"redo"
  *剪切	"cut"
  *复制	"copy"
  *粘贴	"paste"
  *段落对齐	"align"
  *段落左缩进	"leftIndent"
  *段落右缩进	"rightIndent"
  *段落间距	"lineHeight"
  *字体	"fontFamily"
  *字号	"fontSize"
  *字体颜色	"color"
  *字体背景	"backgroundColor"
  *粗体	"bold"
  *下划线	"underline"
  *斜体	"italic"
  *下标	"subScript"
  *上标	"superScript"
  *视图比例	"scale"
  *插入图片	"image"
  *插入分页符" "breakPage"
  *插入特殊符号 "specialCharacter"
  *打印	"doPrint"
   *  @apiSuccessExample {number} Success:
   *     number
 */
  showToolBarItem(sJson: string): Promise<number>;


  /**
   * @api {function} none showToolBarItem
   * @apiName showToolBarItem
   * @apiGroup 5_menuAPI
   * @apiVersion  1.0.0
   * @apiDescription showToolBarItem(sJson: string):number
   * <p>显示和隐藏指定的菜单项</p>
   * @apiparam {json} sJson 菜单设置项
   * @apiParamExample {json} sJson
   * {
   *   "cut": true,//true 显示 false 隐藏
   *   "paste": false,
   * }
   * @apiExample {string} 菜单项对应的值
   * 功能 与 disableMenuItem 类似 传递参数写法不一样。
   *撤销	“undo”
*回退	“redo”
*剪切	“cut”
*复制	“copy”
*粘贴	“paste”
*段落对齐	“align”
*段落左缩进	“leftIndent”
*段落右缩进	“rightIndent”
*段落间距	“lineHeight”
*字体	“fontFamily”
*字号	“fontSize”
*字体颜色	“color”
*字体背景	“backgroundColor”
*粗体	“bold”
*下划线	“underline”
*斜体	“italic”
*下标	“subScript”
*上标	“superScript”
*视图比例	“scale”
*插入图片	“image”
*打印	“doPrint”
   *  @apiSuccessExample {number} Success:
   *     number
 */
  showToolBarItem(sJson: string): Promise<number>;

  /**
   * @api {function} none setViewProportion
   * @apiName setViewProportion
   * @apiGroup 2_layoutAPI
   * @apiVersion  1.0.0
   * @apiDescription setViewProportion(nType: number，nValue: number):number
   * <p>设置文档显示比例</p>
   * @apiparam {number} nType 显示比例类型 1:适合宽度 4：按照百分比显示
   * @apiparam {number} nValue 显示比例的值 只有nType=4有效 100表示100% 110表示110%
   * @apiSuccessExample {number} Success:
   *     number
   */
  setViewProportion(nType: number, nValue?: number): Promise<number>;

  /**
   * @api {function} none getViewProportion
   * @apiName getViewProportion
   * @apiGroup 2_layoutAPI
   * @apiVersion  1.0.0
   * @apiDescription getViewProportion():number
   * <p>获取文档显示比例</p>
   * * @apiSuccessExample {number} Success:
   *     number 文档的显示百分比
   */
  getViewProportion(): number;

  /**
  * @api {function} none setSpecificToolBarVisible
  * @apiName setSpecificToolBarVisible
  * @apiGroup 5_menuAPI
  * @apiVersion  1.0.0
  * @apiDescription setSpecificToolBarVisible(bVisible: boolean, toolBarName?: string):boolean
  * <p>控制工具栏是否显示</p>
  * @apiparam {boolean} bVisible 显示控制 true: 显示；false: 隐藏
  * @apiparam {string} toolBarName 工具栏名称，可选参数，可以不传。
  *  @apiSuccessExample {boolean} Success:
  *     boolean
*/
  setSpecificToolBarVisible(bVisible: boolean, toolBarName?: string): Promise<boolean>;

  /**
   * @api {function} none printDoc
   * @apiName printDoc
   * @apiGroup 3_printAPI
   * @apiVersion  1.0.0
   * @apiDescription printDoc(flag?: boolean);
   * <p>通过浏览器的打印框进行文档打印</p>
   * @apiparam {bool} flag 控制开关 可选参数，控制是否编辑器的打印预览框。true:弹框 false:不弹
   */
  printDoc(flag?: boolean): Promise<void>;

  /**
   * 直接打印
   * @param sPrintJson 打印配置参数
   */
  directPrintDoc(sPrintJson: string): Promise<number>;


  /**
   * @api {function} none printOutpatientDoc
   * @apiName printOutpatientDoc
   * @apiGroup 3_printAPI
   * @apiVersion  1.0.0
   * @apiDescription printOutpatientDoc(sPrintName: string, printMode: number, pageType: number, firstPageDistance: number,
   *                  pageMidDistance?: number, lastPageDistance?: number, pageWidth?: number, pageHeight?: number)
   *                  : boolean;
   * <p>门诊打印</p>
   * @apiparam {string} sPrintName 打印机名称
   * @apiparam {number} printMode 1 – Web 打印
   * @apiparam {number} pageType 纸张模式：1 -上页打印 2-下页打印
   * @apiparam {number} firstPageDistance 首页留白距离 mm
   * @apiparam {number} pageMidDistance 门诊病历上页跟下页的留白距离 单位mm
   * @apiparam {number} lastPageDistance 首页留白距离 mm
   * @apiparam {number} pageWidth 门诊病历宽，单位mm
   * @apiparam {number} pageHeight 门诊病历高，单位mm
  *  @apiSuccessExample {boolean} Success:
   *     boolean
   * 理论上最多能打两页，一页分为上下部分（两个pageHeight）
   * 每次打印时都需要重新加载文档内容，否则会导致打印的内容布局错乱
   * @apiExample {string} Example
   * editor.printOutpatientDoc('hp-printer', 1, 1, 30, 40, 20, 183, 132)
   */
  printOutpatientDoc(sPrintName: string, printMode: number, pageType: number, firstPageDistance: number,
    pageMidDistance?: number, lastPageDistance?: number, pageWidth?: number, pageHeight?: number)
    : Promise<boolean>;

  /**
   * @api {function} none setFontProp
   * @apiName setFontProp
   * @apiGroup 6_PropertAPI
   * @apiVersion  1.0.0
   * @apiDescription setFontProp(propName: string, val: number|string): number
   * <p>设置当前选中文本的Bold, Italic, Underline 属性</p>
   * @apiparam {string} propName 属性名 (Bold, Italic, Underline)
   * @apiparam {string} val 属性值 (可以是number或者string)
   * @apiParamExample {string} propName 属性名
   * {
   *  Bold, Italic, Underline, Sub, Super, Background, Color, FontSize, FontFamily
   * }
   * @apiParamExample {string} val 属性值
   * {
   * Bold: number(0, 1)
   * Italic: number(0, 1)
   * Underline: number(0, 1)
   * Sub: number(0, 1)
   * Super: number(0, 1)
   * FontSize: number(pt 只能为数字)
   * Background: string (RGB)
   * Color: string（RGB值，类似'#FF0000'）
   * FontFamily: string (宋体...)
   * }
   */
  setFontProp(propName: string, val: number | string): Promise<number>;

    /**
   * @api {function} none getFontProp
   * @apiName getFontProp
   * @apiGroup 6_PropertAPI
   * @apiVersion  1.0.0
   * @apiDescription getFontProp(): string
   * <p>获取光标位置的字体属性，包括Bold, Italic, Underline 等属性</p>
  * @apiSuccessExample {json} Success:
  * {
   * Bold: number(0, 1)
   * Italic: number(0, 1)
   * Underline: number(0, 1)
   * Sub: number(0, 1)
   * Super: number(0, 1)
   * FontSize: number(pt 只能为数字)
   * Background: string (RGB)
   * Color: string（RGB值，类似'#FF0000'）
   * FontFamily: string (宋体...)
   * }
   */
    getFontProp(): Promise<string>;

  /**
   * @api {function} none getPageCount
   * @apiName getPageCount
   * @apiGroup 2_layoutAPI
   * @apiVersion  1.0.0
   * @apiDescription getPageCount(): number
   * <p>获得当前文档的页数</p>
   * @apiSuccessExample {number} Success:
   *     number
   */
  getPageCount(): Promise<number>;

  /**
   * @api {function} none setParagraphProp
   * @apiName setParagraphProp
   * @apiGroup 6_PropertAPI
   * @apiVersion  1.0.0
   * @apiDescription setParagraphProp(propName: string, nVal: number): number
   * <p>设置当前段落的缩进, 对齐属性</p>
   * @apiparam {string} propName 属性名
   * @apiparam {string} nVal 属性值
   * @apiParamExample {string} propName 属性名
   * {
   *  FirstLineIndent:首行缩进
   *  LeftIndent:左缩进
   *  HangingIndent:悬挂缩进
   *  Alignment:对齐
   * }
   * @apiParamExample {number} nVal 属性值
   * {
   * FirstLineIndent： 1/1000 cm
   * HangingIndent: 1/1000 cm
   * LeftIndent: 1/1000 cm
   * Alignment：  0:左对齐 1:右对齐 2:两端对齐 3:居中对齐
   * }
   * @apiSuccessExample {number} Success:
   *     number
   */
  setParagraphProp(propName: string, nVal: number): Promise<number>;

  /**
   * @api {function} none setParaLineSpace
   * @apiName setParaLineSpace
   * @apiGroup 6_PropertAPI
   * @apiVersion  1.0.0
   * @apiDescription setParaLineSpace(nType: number, nHeight: number): number
   * <p>调整选中段落的行间距为特定行距</p>
   * @apiparam {number} nType 0：设置整倍数的行距 3：固定值
   * @apiparam {number} nHeight 行距
   * @apiParamExample {string} nHeight 属性值
   * 当nType 不同的时候该值取值不一样：
   * 当nType=0: height= 100 表示1倍行距； height= 200 表示2倍行距。
   * 当nType=3：该值表示固定高度，单位1/1000cm (比如50 表示0.05cm)
   * @apiSuccessExample {number} Success:
   *     number
   */
  setParaLineSpace(nType: number, nHeight: number): Promise<number>;

  /**
   * @api {function} none setWestCharBreakAttribute
   * @apiName setWestCharBreakAttribute
   * @apiGroup 6_PropertAPI
   * @apiVersion  1.0.0
   * @apiDescription setWestCharBreakAttribute(nType: number, bFlag: boolean): number
   * <p>设置当前段落或者全文档的西文字体在单词中间断行的属性</p>
   * @apiparam {number} nType 1：当前段落； 2：全文档
   * @apiparam {boolean} bFlag True：允许西文换行。 False：不允许
   * @apiSuccessExample {number} Success:
   *     number
   */
  setWestCharBreakAttribute(nType: number, bFlag: boolean): Promise<number>;

  /**
   * @api {function} none setFileProperty
   * @apiName setFileProperty
   * @apiGroup 6_PropertAPI
   * @apiVersion  1.0.0
   * @apiDescription setFileProperty(itemName: string, content: string): number
   * <p>设置文档属性，一般为自定义属性</p>
   * @apiparam {string} itemName 属性名称
   * @apiparam {string} content 设置的属性内容
   * @apiSuccessExample {number} Success:
   *     number
   * 可以用来设置跟随文档的一些业务属性：
   * setFileProperty('lastEditor','张三')
   * 下次打开文档后 getFileProperty('lastEditor") 即可获取到'张三'
   */
  setFileProperty(itemName: string, content: string): Promise<number>;

  /**
   * @api {function} none getFileProperty
   * @apiName getFileProperty
   * @apiGroup 6_PropertAPI
   * @apiVersion  1.0.0
   * @apiDescription getFileProperty(itemName: string): string
   * <p>获取文档指定属性的值</p>
   * @apiparam {string} itemName 属性名称
   * @apiSuccessExample {string} Success:
   *     Promise<string>
   */
  getFileProperty(itemName: string): Promise<string>;

  /**
   * @api {function} none setPageMargin
   * @apiName setPageMargin
   * @apiGroup 2_layoutAPI
   * @apiVersion  1.0.0
   * @apiDescription setPageMargin(fPageLeft: number, fPageRight: number, fPageTop: number, fPageBottom: number): number
   * <p>设置页面边距 设置页面页边距大小(单位cm)</p>
   * @apiparam {number} fPageLeft 左页边距
   * @apiparam {number} fPageRight 右页边距
   * @apiparam {number} fPageTop 上页边距
   * @apiparam {number} fPageBottom 下页边距
   * @apiSuccessExample {number} Success:
   *     number
   * 此接口可以调整纸张的页边距。推荐制作模板的时，将页边距固定好。
   * 调用该接口会改变文档的排版。
   */
  setPageMargin(fPageLeft: number, fPageRight: number, fPageTop: number, fPageBottom: number): Promise<number>;

  /**
   * @api {function} none getPageMargin
   * @apiName getPageMargin
   * @apiGroup 2_layoutAPI
   * @apiVersion  1.0.0
   * @apiDescription getPageMargin(): { fWidth: number; fHeight: number; fPageLeft: number; fPageRight: number; fPageTop: number; fPageBottom: number }
   * <p>获取页面边距,单位cm</p>
   * @apiSuccessExample {string} Success:
   *     { fWidth: number; fHeight: number; fPageLeft: number; fPageRight: number; fPageTop: number; fPageBottom: number }
   */
  getPageMargin(): Promise<{ fWidth: number; fHeight: number; fPageLeft: number; fPageRight: number; fPageTop: number; fPageBottom: number }>;
  
  /**
   * @api {function} none setPageFormat
   * @apiName setPageFormat
   * @apiGroup 2_layoutAPI
   * @apiVersion  1.0.0
   * @apiDescription setPageFormat(nPageFormat: number, fPageWidth?: number, fPageHeight?: number,
   *             bHorOrVer?: boolean, nPageLayOut?: number): number
   * <p>设置页面的纸张格式</p>
   * @apiparam {number} nPageFormat 纸张格式，例如: A3=3、A4=4、自定义=11 (目前只支持A3,A4,自定义)
   * @apiparam {number} fPageWidth 纸张的宽度 cm (只有纸张格式为自定义才有效)
   * @apiparam {number} fPageHeight 纸张的高度 cm (只有纸张格式为自定义才有效)
   * @apiparam {number} bHorOrVer 预留 (无用)
   * @apiparam {number} nPageLayOut 预留 (无用)
   * @apiSuccessExample {number} Success:
   *     number
   * 此接口可以调整纸张的尺寸。推荐制作模板的时，将尺寸固定好。
   * 调用该接口会显著改变文档的排版。
   */
  setPageFormat(nPageFormat: number, fPageWidth?: number, fPageHeight?: number,
    bHorOrVer?: boolean, nPageLayOut?: number): Promise<number>;

  /**
   * @api {function} none insertTextAtCurrentCursor
   * @apiName insertTextAtCurrentCursor
   * @apiGroup 7_insertAPI
   * @apiVersion  1.0.0
   * @apiDescription insertTextAtCurrentCursor(sText: string): number
   * <p>在光标当前位置插入文本内容</p>
   * @apiparam {string} sText 文本内容
  * @apiParamExample {string} sText
   *
   *  sText可以是普通的string,如果需要回车换行，录入\r\n
   *  sText 可支持json格式 用来录入上下标文本
   * {
   *   'normal':"正常文本",
   *   'superscript';"上标文本",
   *   'subscript':"下标文本"
   *  }
   *  @apiExample  {javascript} Example:
   * editor.insertTextAtCurrentCursor("hello world");//普通string
   * editor.insertTextAtCurrentCursor(JSON.stringify({"normal":"aaa","superscript":"bbb","subscript":"ccc"}))；//json format
   * @apiSuccessExample {number} Success:
   *     number
   *  编辑器会自动区分json参数或者普通string
   */
  insertTextAtCurrentCursor(sText: string): Promise<number>;

  /**
  * @api {function} none insertFileWithString
  * @apiName insertFileWithString
  * @apiGroup 7_insertAPI
  * @apiVersion  1.0.0
  * @apiDescription insertFileWithString(base64String: string): number
  * <p>当前光标处插入字符串流文件</p>
  * @apiparam {string} base64String 需要插入的文件base64字符串流
  * @apiSuccessExample {number} Success:
  *     number
  * 注意：A文件中插入B文件，插入完毕后，B文件中的级联会被舍弃
  */
  insertFileWithString(base64String: string): Promise<number>;

  /**
   * @api {function} none showAISuggestionAtCursor
   * @apiName showAISuggestionAtCursor
   * @apiGroup 82_cursorAPI
   * @apiVersion  1.0.0
   * @apiDescription showAISuggestionAtCursor(suggestions: string[]): Promise<boolean>
   * <p>在当前光标位置显示 AI 建议弹窗，传递光标坐标和测试文本</p>
   * @apiparam {string[]} suggestions 需要显示的建议文本数组
   * @apiSuccessExample {boolean} Success:
   *     true: 显示成功, false: 显示失败
   * @apiExample  {javascript} Example:
   * editor.showAISuggestionAtCursor(["这是第一条建议", "这是第二条建议"]);
   */
  showAISuggestionAtCursor(suggestions: string[]): Promise<boolean>;

  /**
   * @api {function} none insertFileWithStream
   * @apiName insertFileWithStream
   * @apiGroup 7_insertAPI
   * @apiVersion  1.0.0
   * @apiDescription insertFileWithStream(content: Blob): number
   * <p>当前光标处插入字符串流文件</p>
   * @apiparam {Blob} content 需要插入的文件blob
   * @apiSuccessExample {number} Success:
   *     number
   * 注意：A文件中插入B文件，插入完毕后，B文件中的级联会被舍弃
   */
  insertFileWithStream(content: Blob): Promise<number>;

  /**
   * @api {function} none mergeDocumentsWithString
   * @apiName mergeDocumentsWithString
   * @apiGroup 7_insertAPI
   * @apiVersion  1.0.0
   * @apiDescription mergeDocumentsWithString(base64String: string, options?: string): number
   * <p>合并文档 base64流</p>
   * @apiparam {string} base64String 待合并的文件字符串流
   * @apiparam {string} options 合并选项，JSON字符串格式，包含以下可选属性：
   *   - retainCascade: boolean - 是否保留级联关系，默认为false
   * @apiSuccessExample {number} Success:
   *     number
   * 参考 mergeDocumentsWithStream
   */
  mergeDocumentsWithString(base64String: string, options?: string): Promise<number>;

  /**
   * @api {function} none mergeDocumentsWithStream
   * @apiName mergeDocumentsWithStream
   * @apiGroup 7_insertAPI
   * @apiVersion  1.0.0
   * @apiDescription mergeDocumentsWithStream(content: Blob, options?: string): number
   * <p>使用blob 合并文档</p>
   * @apiparam {blob} content 待合并的文件blob
   * @apiparam {string} options 合并选项，JSON字符串格式，包含以下可选属性：
   *   - retainCascade: boolean - 是否保留级联关系，默认为false
   * @apiSuccessExample {number} Success:
   *     number
   * 注意：
   * 1. A文件中合并B文件，默认情况下B文件中的级联会被舍弃，除非设置retainCascade为true
   * 2. Merge接口必须保证调用的时候，光标处于空段落的首位置，否则调用失败。
   * 推荐流程：
   * 1. jumpToFileEnd() //先将光标跳转到文档末尾
   * 2. insertNewLine() // 产生一个空行 保证光标处于空段落的首位置
   * 3. mergeDocumentsWithStream //合并
  *  @apiExample  {javascript} Example:
  * {
  *  try{
	* const blob = await getfilecontentFromServer(i);
	*		await editorFunction.jumpToFileEnd();
	*		var bFlag = await editorFunction.isCurrentLineEmpty();
	*		if( !bFlag){//不在行首
	*			await editorFunction.insertNewLine();
	*			await editorFunction.setParagraphProp('Alignment',0);//可以不要
	*		}
   *    // 默认不保留级联关系
   *    await editorFunction.mergeDocumentsWithStream(blob);
   *    
   *    // 或者指定保留级联关系
   *    // const options = JSON.stringify({ retainCascade: true });
   *    // await editorFunction.mergeDocumentsWithStream(blob, options);
   *     }catch(error) {
    *        console.error('Error fetching blob:', error);
    *    };
  * }
   */
  mergeDocumentsWithStream(content: Blob, options?: string): Promise<number>;

  /**
   * True -- 进入设计模式。 False – 离开设计模式 进入设计模式后，被隐藏的数据元将显示出来
   * @param bFlag 设计模式状态控制
   */
  designTemplet(bFlag: boolean): Promise<void>;

  /**
   * @api {function} none browseTemplet
   * @apiName browseTemplet
   * @apiGroup 2_layoutAPI
   * @apiVersion  1.0.0
   * @apiDescription browseTemplet(nType: number, nProtected: number): number
   * <p>切换浏览模式</p>
   * @apiparam {number} nType 1:清洁模式 0:正常模式
   * @apiparam {number} nProtected 0:不需要保护状态 1:需要保护状态
   * @apiSuccessExample {number} Success:
   *     number
   * 清洁浏览：不显示痕迹，不显示背景色；只有占位符的结构在浏览的时候会被隐藏
   * 正常模式：只有占位符的结构显示出来。
   */
  browseTemplet(nType: number, nProtected: number): Promise<number>;

  /**
   * @api {function} none getCurrentCursorPage
   * @apiName getCurrentCursorPage
   * @apiGroup 2_layoutAPI
   * @apiVersion  1.0.0
   * @apiDescription getCurrentCursorPage(): number
   * <p>返回当前光标的页码</p>
   * @apiSuccessExample {number} Success:
   *     当前光标的页码，失败为 0
  */
  getCurrentCursorPage(): Promise<number>;

  /**
   * @api {function} none isDocModified
   * @apiName isDocModified
   * @apiGroup 8_editAPI
   * @apiVersion  1.0.0
   * @apiDescription isDocModified(): boolean
   * <p>判断文档打开后是否被修改</p>
   * @apiSuccessExample {number} Success:
   *     文档状态，如果被修改，那么返回true，反之返回false.影响文档脏标记的因素非常多，可能会出现打开文档后
   * 未修改，但是isDocModified返回true的情况。因此，可以借助getFileMd5接口共同判断。
  */
  isDocModified(): Promise<boolean>;

  /**
   * @api {function} none setDocModified2
   * @apiName setDocModified2
   * @apiGroup 8_editAPI
   * @apiVersion  1.0.0
   * @apiDescription setDocModified2(bModify: boolean): number
   * <p>设置文档修改的状态，既可以设置为'修改'状态, 也可以设置为'非修改'状态。</p>
   * @apiparam {boolean} bModify true: 修改状态; false: 非修改状态
   * @apiSuccessExample {number} Success:
   *     number
  */
  setDocModified2(bModify: boolean): Promise<number>;

  /**
   * @api {function} none getSelectText
   * @apiName getSelectText
   * @apiGroup 8_editAPI
   * @apiVersion  1.0.0
   * @apiDescription getSelectText(): string
   * <p>获取选中的文本</p>
   * @apiSuccessExample {string} Success:
   *     string 选中的文本
  */
  getSelectText(): Promise<string>;

  /**
   * @api {function} none selectAllDoc
   * @apiName selectAllDoc
   * @apiGroup 8_editAPI
   * @apiVersion  1.0.0
   * @apiDescription selectAllDoc()
   * <p>高亮选中整个文档（不包含页眉 页脚）</p>
   * @apiSuccessExample {string} Success:
   *     null
  */
  selectAllDoc(): Promise<void>;

  /**
   * @api {function} none protectDoc
   * @apiName protectDoc
   * @apiGroup 8_editAPI
   * @apiVersion  1.0.0
   * @apiDescription protectDoc(bProtect: boolean): number
   * <p>对全文档进行只读保护或者解除只读保护</p>
   * @apiparam {boolean} bProtect 是否只读模式； true：只读； false：解除只读
   * @apiSuccessExample {number} Success:
   *     number
  */
  protectDoc(bProtect: boolean): Promise<number>;

  /**
   * @api {function} none isProtectedMode
   * @apiName isProtectedMode
   * @apiGroup 8_editAPI
   * @apiVersion  1.0.0
   * @apiDescription isProtectedMode(): boolean
   * <p>文档是否处于只读保护状态</p>
   * @apiSuccessExample {boolean} Success:
   *     true：只读保护  false：非只读保护
  */
  isProtectedMode(): Promise<boolean>;

  /**
   * @api {function} none deleteRedundantEx
   * @apiName deleteRedundantEx
   * @apiGroup 8_editAPI
   * @apiVersion  1.0.0
   * @apiDescription deleteRedundantEx(bBlankLine: boolean, bSpace: boolean,
   *                    bTag: boolean, bDelPageBreak: boolean): number
   * <p>删除文档末尾的空白行、空格、Tab键，及控制是否删除文末分页符</p>
   * @apiparam {boolean} bBlankLine 是否删除空白行
   * @apiparam {boolean} bSpace 是否删除空格(全角 半角)
   * @apiparam {boolean} bTag 是否删除Tab字符
   * @apiparam {boolean} bDelPageBreak 是否删除文末分页符
   * @apiSuccessExample {number} Success:
   *     number
  */
  deleteRedundantEx(bBlankLine: boolean, bSpace: boolean, bTag: boolean, bDelPageBreak: boolean): Promise<number>;

  /**
  * @api {function} none insertNewLine
  * @apiName insertNewLine
  * @apiGroup 8_editAPI
  * @apiVersion  1.0.0
  * @apiDescription insertNewLine():number
  * <p>在当前位置上插入新的一行</p>
  * @apiSuccessExample {number} Success:
  *     number
 */
  insertNewLine(): Promise<number>;

    /**
  * @api {function} none insertSpecialCharacter
  * @apiName insertSpecialCharacter
  * @apiGroup 8_editAPI
  * @apiVersion  1.0.0
  * @apiDescription insertSpecialCharacter():number
  * <p>弹出插入特殊字符对话框</p>
  * @apiSuccessExample {number} Success:
  *     number
 */
    insertSpecialCharacter(): Promise<number>;

  /**
   * @api {function} none copy
   * @apiName copy
   * @apiGroup 8_editAPI
   * @apiVersion  1.0.0
   * @apiDescription copy():number
   * <p>复制当前选定的内容。</p>
   * @apiSuccessExample {number} Success:
   *     number
  */
  copy(): Promise<number>;

  /**
   * @api {function} none cut
   * @apiName cut
   * @apiGroup 8_editAPI
   * @apiVersion  1.0.0
   * @apiDescription cut():number
   * <p>剪切当前选定的内容。</p>
   * @apiSuccessExample {number} Success:
   *     number
  */
  cut(): Promise<number>;

  /**
   * @api {function} none paste
   * @apiName paste
   * @apiGroup 8_editAPI
   * @apiVersion  1.0.0
   * @apiDescription paste():number
   * <p>粘贴当前剪贴板的内容。</p>
   * @apiSuccessExample {number} Success:
   *     number
  */
  paste(): Promise<number>;

  /**
   * @api {function} none delete
   * @apiName delete
   * @apiGroup 8_editAPI
   * @apiVersion  1.0.0
   * @apiDescription delete()
   * <p>删除当前光标选中的内容。</p>
   * @apiSuccessExample {number} Success:
   *     null
  */
  delete(): Promise<void>;

  /**
   * @api {function} none redo
   * @apiName redo
   * @apiGroup 8_editAPI
   * @apiVersion  1.0.0
   * @apiDescription redo()
   * <p>删除当前光标选中的内容。</p>
   * @apiSuccessExample {number} Success:
   *     null
  */
  redo(): Promise<void>;

  /**
   * @api {function} none undo
   * @apiName undo
   * @apiGroup 8_editAPI
   * @apiVersion  1.0.0
   * @apiDescription undo()
   * <p>删除当前光标选中的内容。</p>
   * @apiSuccessExample {number} Success:
   *     null
  */
  undo(): Promise<void>;

  /**
   * @api {function} none getTextFromDocument
   * @apiName getTextFromDocument
   * @apiGroup 8_editAPI
   * @apiVersion  1.0.0
   * @apiDescription getTextFromDocument():string
   * <p>获取整个文档的Text数据。</p>
   * @apiSuccessExample {string} Success:
   *     string
   *     需要打开文档。不打开文档获取需要借助后台服务
  */
  getTextFromDocument(): Promise<string>;

  /**
   * @api {function} none getFileMd5
   * @apiName getFileMd5
   * @apiGroup 8_editAPI
   * @apiVersion  1.0.0
   * @apiDescription getFileMd5():string
   * <p>返回当前打开文档的一串32位的MD5码。</p>
   * @apiSuccessExample {string} Success:
   *     string
   *  只有可见的信息，比如文字，文字的字体颜色，参与MD5生成。
  */
  getFileMd5(): Promise<string>;

  /**
   * @api {function} none getVersion
   * @apiName getVersion
   * @apiGroup 1_documentAPI
   * @apiVersion  1.0.0
   * @apiDescription getVersion(): string
   * <p>获取当前编辑器的版本号</p>
   * @apiSuccessExample {string} Success:
   *     返回编辑器的版本号，例如 "1.1.1"
   */
  getVersion(): Promise<string>;

  /**
   * @api {function} none setExtraCopyInformation
   * @apiName setExtraCopyInformation
   * @apiGroup 81_copyAPI
   * @apiVersion  1.0.0
   * @apiDescription setExtraCopyInformation(sCopyInformation: string): number
   * <p>设置拷贝的附加信息</p>
   * @apiparam {string} sCopyInformation 需要设置的附件信息
   * @apiSuccessExample {number} Success:
   *     number
   * 1.此信息设置有值时，会触发拷贝控制，只有信息一致的情况下，才能允许互相拷贝。
   * 2.此信息为空的情况下，不会触发拷贝控制。
   * 3.比如A文档打开后调用此接口设置附件信息"张三",B文档打开后设置附加信息"王二"。
   * 4.因为 "张三" 不等于 "王二",所以不能从A文档中拷贝内容到B文档。
  */
  setExtraCopyInformation(sCopyInformation: string): Promise<number>;

  /**
   * @api {function} none enableCopyFromExternal
   * @apiName enableCopyFromExternal
   * @apiGroup 81_copyAPI
   * @apiVersion  1.0.0
   * @apiDescription enableCopyFromExternal(bEnable: boolean): number
   * <p>设置是否可以从外部(比如浏览器，记事本等)拷贝到编辑器</p>
   * @apiparam {boolean} bEnable 是否可以从外部(比如IE,记事本等等)拷贝到文档中。 true:可以;false:不可以
   * @apiSuccessExample {number} Success:
   *     number
  */
  enableCopyFromExternal(bEnable: boolean): Promise<number>;

    /**
   * @api {function} none setClipboardFormat
   * @apiName setClipboardFormat
   * @apiGroup 81_copyAPI
   * @apiVersion  1.0.0
   * @apiDescription setClipboardFormat(bOut: boolean, bIn: boolean): number
   * <p>设置剪贴板格式，外部剪贴板与内部剪贴板是否带格式</p>
   * @apiparam {boolean} bOut 外部粘贴内容  true:带格式;false:不带格式
   * @apiparam {boolean} bIn  内部粘贴内容  true:带格式;false:不带格式
   * @apiSuccessExample {number} Success:
   *     number
  */
  setClipboardFormat(bOut: boolean, bIn: boolean): Promise<number>;

  /**
   * @api {function} none jumpToPage
   * @apiName jumpToPage
   * @apiGroup 82_cursorAPI
   * @apiVersion  1.0.0
   * @apiDescription jumpToPage(pageIndex: number)
   * <p>光标跳到指定的页面页首</p>
   * @apiparam {number} pageIndex 指定页的索引
   * @apiSuccessExample {string} Success:
   *     null
  */
  jumpToPage(pageIndex: number): Promise<void>;

  /**
   * @api {function} none jumpToFirstPage
   * @apiName jumpToFirstPage
   * @apiGroup 82_cursorAPI
   * @apiVersion  1.0.0
   * @apiDescription jumpToFirstPage()
   * <p>光标跳到文档的第一页页首。</p>
   * @apiSuccessExample {string} Success:
   *     null
  */
  jumpToFirstPage(): Promise<void>;

  /**
   * @api {function} none jumpToLastPage
   * @apiName jumpToLastPage
   * @apiGroup 82_cursorAPI
   * @apiVersion  1.0.0
   * @apiDescription jumpToLastPage()
   * <p>光标跳到文档的最后一页页首</p>
   * @apiSuccessExample {string} Success:
   *     null
  */
  jumpToLastPage(): Promise<void>;

  /**
   * @api {function} none jumpToEndOfPage
   * @apiName jumpToEndOfPage
   * @apiGroup 82_cursorAPI
   * @apiVersion  1.0.0
   * @apiDescription jumpToEndOfPage()
   * <p>光标跳到当前页的末尾。</p>
   * @apiSuccessExample {string} Success:
   *     null
  */
  jumpToEndOfPage(): Promise<void>;

  /**
   * @api {function} none jumpToStartOfPage
   * @apiName jumpToStartOfPage
   * @apiGroup 82_cursorAPI
   * @apiVersion  1.0.0
   * @apiDescription jumpToStartOfPage()
   * <p>光标跳到当前页的开始。</p>
   * @apiSuccessExample {string} Success:
   *     null
  */
  jumpToStartOfPage(): Promise<void>;

  /**
   * @api {function} none jumpToFileEnd
   * @apiName jumpToFileEnd
   * @apiGroup 82_cursorAPI
   * @apiVersion  1.0.0
   * @apiDescription jumpToFileEnd()
   * <p>光标跳转到文件末尾</p>
   * @apiSuccessExample {string} Success:
   *     null
  */
  jumpToFileEnd(): Promise<void>;

  /**
   * @api {function} none jumpToOnePosition
   * @apiName jumpToOnePosition
   * @apiGroup 82_cursorAPI
   * @apiVersion  1.0.0
   * @apiDescription jumpToOnePosition(sPosition: string): number
   * <p>光标跳转到指定位置</p>
   * @apiparam {string} sPosition 位置
   * @apiSuccessExample {number} Success:
   *     number
   * sPosition 由getSelectionRangeStart类似的接口返回
  */
  jumpToOnePosition(sPosition: string): Promise<number>;

  /**
   * @api {function} none getSelectionRangeStart
   * @apiName getSelectionRangeStart
   * @apiGroup 82_cursorAPI
   * @apiVersion  1.0.0
   * @apiDescription getSelectionRangeStart(): string
   * <p>获取选中区域头位置</p>
   * @apiSuccessExample {string} Success:
   *     位置string。 空值表示失败
  */
  getSelectionRangeStart(): Promise<string>;

  /**
   * @api {function} none getSelectionRangeEnd
   * @apiName getSelectionRangeEnd
   * @apiGroup 82_cursorAPI
   * @apiVersion  1.0.0
   * @apiDescription getSelectionRangeEnd(): string
   * <p>获取选中区域尾位置</p>
   * @apiSuccessExample {string} Success:
   *     位置string。 空值表示失败
  */
  getSelectionRangeEnd(): Promise<string>;

  /**
   * @api {function} none selectOneArea2
   * @apiName selectOneArea2
   * @apiGroup 82_cursorAPI
   * @apiVersion  1.0.0
   * @apiDescription selectOneArea2(sStartPos: string, sEndPos: string)
   * <p>根据字符位置反亮选中指定的区域</p>
   * @apiparam {string} sStartPos 选中的开始位置
   * @apiparam {string} sEndPos 选中的结束位置
   * @apiSuccessExample {number} Success:
   *     null
   *     注意：位置值由getSelectionRangeStart类似的接口返回
  */
  selectOneArea2(sStartPos: string, sEndPos: string): Promise<void>;

  /**
   * @api {function} none insertPageBreak
   * @apiName insertPageBreak
   * @apiGroup 8_editAPI
   * @apiVersion  1.0.0
   * @apiDescription insertPageBreak(): number
   * <p>在当前光标处插入分页符</p>
   * @apiSuccessExample {number} Success:
   *     number
  */
  insertPageBreak(): Promise<number>;

  /**
   * @api {function} none addImageWithString
   * @apiName addImageWithString
   * @apiGroup 83_imageAPI
   * @apiVersion  1.0.0
   * @apiDescription addImageWithString(base64String: string): string
   * <p>在光标所在位置插入字符串流的图片</p>
   * @apiparam {string} base64String 图片base64串值
   * @apiSuccessExample {string} Success:
   * base64String 格式如下：data:${mimeType};base64,${base64Data}
   *     string
   *  返回插入图片的name
  */
  addImageWithString(base64String: string): Promise<string>;

  /**
   * 在光标所在位置插入网络url指定的图片
   * @param url 图片url
   * @return 插入图片的名称
   */
  addImageWithUrl(url: string): Promise<string>;

  /**
   * @api {function} none addEditableImageWithString
   * @apiName addEditableImageWithString
   * @apiGroup 83_imageAPI
   * @apiVersion  1.0.0
   * @apiDescription addEditableImageWithString(base64String: string): string
   * <p>在光标所在位置插入字符串流的矢量图片</p>
   * @apiparam {string} base64String 矢量图片base64串值
   * @apiSuccessExample {string} Success:
   *     string
   *  返回插入矢量图片的name
  */
  addEditableImageWithString(base64String: string): Promise<string>;

  /**
   * @api {function} none addSignaturePicToSectionWithStream
   * @apiName addSignaturePicToSectionWithStream
   * @apiGroup 83_imageAPI
   * @apiVersion  1.0.0
   * @apiDescription addSignaturePicToSectionWithStream(sStructName: string, nMark: number,
   *                                 sBase64: string, nWidht: number, nHeight: number): string
   * <p>在指定位置插入签名图片</p>
   * @apiparam {string} sStructName 元素名称.只支持节跟文本框，如果为空，则在当前位置插入.
   * @apiparam {number} nMark 左右边框的前后位置（1 – 后边框的后端 0 – 前边框的前端 2 – 后边框的前端 3 – 前边框的后端）
   * @apiparam {string} sBase64 签名图片的base64串值
   * @apiparam {number} nWidht 图片宽度px 如果为0，则用图片的实际宽度.
   * @apiparam {number} nHeight 图片高度px 如果为0，则用图片的实际高度。
   * @apiSuccessExample {string} Success:
   *     string
   * 1.返回插入签名图片的name
   * 2.支持的图形格式包含但并不限于：BMP、JPEG、PNG、GIF。
   * 3.插入的签名图片默认不能拷贝。可以删除，如果要设置图片不能被删除 请调用SetImageDeleteProtection.
   * 4.注意图片base数据需要包含头信息，完整的string的格式如下： "data:image/png;base64,xxxx" (xxx为图片的base64串值).
   * 5.长跟宽只要有一个值为0，图片就会保持实际长宽。
  */
  addSignaturePicToSectionWithStream(sStructName: string, nMark: number,
    sBase64: string, nWidht: number, nHeight: number): Promise<string>;

  /**
  * @api {function} none deleteImage
  * @apiName deleteImage
  * @apiGroup 83_imageAPI
  * @apiVersion  1.0.0
  * @apiDescription deleteImage(sName: string): number
  * <p>删除一个指定的图片</p>
  * @apiparam {string} sName 图片的name
  * @apiSuccessExample {number} Success:
  *     number
 */
  deleteImage(sName: string): Promise<number>;

  /**
   * @api {function} none setImageName
   * @apiName setImageName
   * @apiGroup 83_imageAPI
   * @apiVersion  1.0.0
   * @apiDescription setImageName(sName: string, sNewName: string): number
   * <p>对一个合法的图片对象重命名</p>
   * @apiparam {string} sName 图片的name
   * @apiparam {string} sNewName 图片新name
   * @apiSuccessExample {number} Success:
   *     number
  */
  setImageName(sName: string, sNewName: string): Promise<number>;

  /**
   * @api {function} none setImageSize
   * @apiName setImageSize
   * @apiGroup 83_imageAPI
   * @apiVersion  1.0.0
   * @apiDescription setImageSize(sName: string, nWidth: number, nHeight: number): number
   * <p>对一个图片设置长宽</p>
   * @apiparam {string} sName 图片的name
   * @apiparam {number} nWidth 指定图片宽 单位px
   * @apiparam {number} nHeight 指定图片高 单位px
   * @apiSuccessExample {number} Success:
   *     number
  */
  setImageSize(sName: string, nWidth: number, nHeight: number): Promise<number>;

  /**
   * @api {function} none getCurrentImageName
   * @apiName getCurrentImageName
   * @apiGroup 83_imageAPI
   * @apiVersion  1.0.0
   * @apiDescription getCurrentImageName(): string
   * <p>获取当前光标选中图片的名称</p>
   * @apiSuccessExample {string} Success:
   *     string
  */
  getCurrentImageName(): Promise<string>;

  /**
   * @api {function} none setImageDeleteProtection
   * @apiName setImageDeleteProtection
   * @apiGroup 83_imageAPI
   * @apiVersion  1.0.0
   * @apiDescription setImageDeleteProtection(sName: string, bDeleteProtect: boolean): number
   * <p>设置指定图片是否可以删除</p>
   * @apiparam {string} sName 图片的name
   * @apiparam {boolean} bDeleteProtect 是否删除保护. true:不可删除 false:可以删除
   * @apiSuccessExample {number} Success:
   *     number
  */
  setImageDeleteProtection(sName: string, bDeleteProtect: boolean): Promise<number>;

  /**
   * @api {function} none setImageCopyProtection
   * @apiName setImageCopyProtection
   * @apiGroup 83_imageAPI
   * @apiVersion  1.0.0
   * @apiDescription setImageCopyProtection(sName: string, bCopyProtect: boolean): number
   * <p>设置指定图片是否可以拷贝</p>
   * @apiparam {string} sName 图片的name
   * @apiparam {boolean} bDeleteProtect 是否拷贝保护. true:不可拷贝 false:可以拷贝
   * @apiSuccessExample {number} Success:
   *     number
  */
  setImageCopyProtection(sName: string, bCopyProtect: boolean): Promise<number>;

  /**
   * @api {function} none setImageCustomProperty
   * @apiName setImageCustomProperty
   * @apiGroup 83_imageAPI
   * @apiVersion  1.0.0
   * @apiDescription setImageCustomProperty(sImageName:string, sPropName:string, sPropValue:string|number): number
   * <p>设置图片的自定义属性</p>
   * @apiparam {string} sImageName 图片名称
   * @apiparam {string} sPropName 自定义属性名
   * @apiparam {string} sPropValue 自定义属性值 可以是string类型 也可以是number类型
   * @apiSuccessExample {number} Success:
   *     number
  */
  setImageCustomProperty(sImageName: string, sPropName: string, sPropValue: string | number): Promise<number>;

  /**
   * @api {function} none getImageCustomProperty
   * @apiName getImageCustomProperty
   * @apiGroup 83_imageAPI
   * @apiVersion  1.0.0
   * @apiDescription getImageCustomProperty(sImageName: string, sPropName: string): any
   * <p>获取图片的自定义属性</p>
   * @apiparam {string} sImageName 图片名称
   * @apiparam {string} sPropName 自定义属性名
   * @apiSuccessExample {any} Success:
   *     返回any类型
  */
  getImageCustomProperty(sImageName: string, sPropName: string): Promise<any>;

  /**
   * @api {function} none getAllImagesByCurrentDoc
   * @apiName getAllImagesByCurrentDoc
   * @apiGroup 83_imageAPI
   * @apiVersion  1.0.0
   * @apiDescription getAllImagesByCurrentDoc():string
   * <p>获取文档所有的图片,以逗号隔开</p>
   * @apiSuccessExample {string} Success:
   *     返回类似的string："image1,image2,image3"
  */
  getAllImagesByCurrentDoc(): string;

  /**
   * @api {function} none getAllImagesInStruct
   * @apiName getAllImagesInStruct
   * @apiGroup 83_imageAPI
   * @apiVersion  1.0.0
   * @apiDescription getAllImagesInStruct(sName: string):string
   * <p>获取指定结构里的图片name</p>
   * @apiparam {string} sName 指定元素的name
   * @apiSuccessExample {string} Success:
   *     string
  */
  getAllImagesInStruct(sName: string): string;

  /**
   * @api {function} none insertMedicalformula
   * @apiName insertMedicalformula
   * @apiGroup 84_formulaAPI
   * @apiVersion  1.0.0
   * @apiDescription insertMedicalformula(nType: number, sID: string, sJson: string): number
   * <p>在当前光标位置插入给定名称的医学公式</p>
   * @apiparam {number} nType 医学公式类型:
   * <p>0:普通分式 1：Z/P牙位图; 2:月经史; 3:瞳孔图; 4:胎心; 5:光定位; 6:PD牙位图; 8:标尺;</p>
   * <p>9-11：月经史 12：病变下牙 13:病变上牙 14：恒齿牙位图 15：乳牙牙位图 </p>
   * <p>注意！恒齿牙位图以及乳牙牙位图 暂不支持接口插入。 </p>
   * @apiparam {string} sID 医学公式名称
   * @apiparam {string} sJson 医学公式内容，json格式
   * @apiParamExample {json} sJson
   * {
      * "value1":3,
      * "value2":15,
      * "value3":50,
      * "value4":30
   * }
   * 1.数据排列方式为（从上往下，从左往右排列)
   * 2.比如月经史：上边是value1，左边：value2，右边：value3，下边：value4）
   * @apiSuccessExample {number} Success:
   *     number
  */
  insertMedicalformula(nType: number, sID: string, sJson: string): Promise<number>;

  /**
   * @api {function} none setMedicalformulaText
   * @apiName setMedicalformulaText
   * @apiGroup 84_formulaAPI
   * @apiVersion  1.0.0
   * @apiDescription setMedicalformulaText(sID: string, sJson: string): number
   * <p>设置指定医学公式的类型和内容</p>
   * @apiparam {string} sID 医学公式名称
   * @apiparam {string} sJson 医学公式内容，json格式
   * @apiSuccessExample {number} Success:
   *     number
   *  sJson 同 insertMedicalformula
   *  注意！恒齿牙位图以及乳牙牙位图 暂不支持接口修改。 </p>
  */
  setMedicalformulaText(sID: string, sJson: string): Promise<number>;

  /**
   * @api {function} none setRecensionProtectMode
   * @apiName setRecensionProtectMode
   * @apiGroup 85_revisionAPI
   * @apiVersion  1.0.0
   * @apiDescription setRecensionProtectMode(bFlag: boolean): Promise<number>
   * <p>设置修订模式的类型</p>
   * @apiparam {boolean} bFlag true:保护模式 false:可编辑模式
   * @apiSuccessExample {javascript} Success:
   *     let nresult = editor.setRecensionProtectMode(true);//启用修订保护模式后，修订无法被其他人修改
  */
  setRecensionProtectMode(bFlag: boolean): Promise<number>;

   /**
     * @api {function} none getRevisionCount
     * @apiName getRevisionCount
     * @apiGroup 85_revisionAPI
     * @apiVersion  1.0.0
     * @apiDescription getRevisionCount()
     * <p>获取文档中修订的统计信息，包括总修订数、新增修订数、删除修订数和参与修订的用户列表。</p>
     * @apiExample {javascript} 调用示例:
     * editor.getRevisionCount().then(result => {
     *   console.log('修订统计:', result);
     *   console.log('总修订数:', result.total);
     *   console.log('新增修订数:', result.additions);
     *   console.log('删除修订数:', result.deletions);
     *   console.log('参与用户:', result.users);
     * });
     * @apiSuccessExample {string} 返回值格式:
     * {
     *   "total": 15,              // 总修订数
     *   "additions": 10,          // 新增修订数
     *   "deletions": 5,           // 删除修订数
     *   "users": ["张三", "李四"]  // 参与修订的用户列表
     * }
     * @apiErrorExample {string} 错误情况:
     * {
     *   "total": 0,
     *   "additions": 0,
     *   "deletions": 0,
     *   "users": []
     * }
     */
   getRevisionCount(): Promise<{
       total: number;           // 总修订数
       additions: number;       // 新增修订数
       deletions: number;       // 删除修订数
       users: string[];         // 参与修订的用户列表
   }>;


   /**
    * @api {function} none getRevisionDetails
    * @apiName getRevisionDetails
    * @apiGroup 85_revisionAPI
    * @apiVersion  1.0.0
    * @apiDescription getRevisionDetails(author?: string): string
    * <p>获取文档中所有修订的详细信息，包括修订者、修订时间、修订类型、修订内容等。可以按指定作者进行过滤查询。</p>
    * @apiparam {string} author 可选参数，指定修订者姓名进行过滤。如果不传或传空字符串，则返回所有修订信息
    * @apiExample {javascript} 调用示例:
    * // 获取所有修订信息
    * editor.getRevisionDetails().then(result => {
    *   const revisions = JSON.parse(result);
    *   console.log('所有修订信息:', revisions);
    *   revisions.forEach(rev => {
    *     console.log('修订者:', rev.userName);
    *     console.log('修订时间:', rev.time);
    *     console.log('修订类型:', rev.type);
    *     console.log('修订内容:', rev.value);
    *   });
    * });
    * 
    * // 获取指定作者的修订信息
    * editor.getRevisionDetails('张三').then(result => {
    *   const revisions = JSON.parse(result);
    *   console.log('张三的修订信息:', revisions);
    * });
    * 
    * // 获取所有修订信息（不传参数）
    * editor.getRevisionDetails('').then(result => {
    *   const revisions = JSON.parse(result);
    *   console.log('所有修订信息:', revisions);
    * });
         * @apiSuccessExample {string} 返回值格式:
     * "[{\"userName\":\"张三\",\"userId\":\"user001\",\"time\":\"2024-01-15T10:30:00.000Z\",\"level\":1,\"type\":\"add\",\"value\":\"新增的文本内容\"},{\"userName\":\"李四\",\"userId\":\"user002\",\"time\":\"2024-01-15T11:20:00.000Z\",\"level\":1,\"type\":\"remove\",\"value\":\"删除的文本内容\"}]"
    * @apiErrorExample {string} 错误情况:
    * ""  // 空字符串表示没有修订信息或查询失败
    */
   getRevisionDetails(author?: string): Promise<string>;

  /**
   * @api {function} none clearUndoList
   * @apiName clearUndoList
   * @apiGroup 8_editAPI
   * @apiVersion  1.0.0
   * @apiDescription clearUndoList()
   * <p>清空撤销列表。</p>
   * @apiSuccessExample {number} Success:
   *     null
  */
  clearUndoList(): void;

  /**
   * @api {function} none insertTable
   * @apiName insertTable
   * @apiGroup 86_tableAPI
   * @apiVersion  1.0.0
   * @apiDescription insertTable(name: string, col: number , row: number, hasTitlerow: boolean, nNumbers: number ): number
   * <p>在当前光标位置插入表格。</p>
   * @apiparam {string} name 表格名称
   * @apiparam {number} col 列数
   * @apiparam {number} row 行数
   * @apiparam {boolean} hasTitlerow 是否有头行
   * @apiparam {number} nNumbers 头行数
   * @apiSuccessExample {number} Success:
   *     number
  */
  insertTable(name: string, col: number, row: number, hasTitlerow: boolean, nNumbers: number): Promise<number>;

  /**
   * @api {function} none moveCursorToTableRowEnd
   * @apiName moveCursorToTableRowEnd
   * @apiGroup 86_tableAPI
   * @apiVersion  1.0.0
   * @apiDescription moveCursorToTableRowEnd(): boolean
   * <p>将光标移动至当前表格所选行的最后一个单元格</p>
   * @apiSuccessExample {boolean} Success:
   *     boolean
  */
  moveCursorToTableRowEnd(): boolean;

  /**
   * @api {function} none putCellContentByArray
   * @apiName putCellContentByArray
   * @apiGroup 86_tableAPI
   * @apiVersion  1.0.0
   * @apiDescription putCellContentByArray(sName: string, sJsonContent: string): number
   * <p>批量设置指定表格单元格的文本</p>
   * @apiparam {string} sName 表格名称
   * @apiparam {string} sJsonContent 单元格内容的json数组
   * @apiparamExample {json} sJsonContent
   * {
   * "A1": "内容1",
   * "A2": "内容2",
   * "A3": "内容3"
   * }
   * @apiSuccessExample {number} Success:
   *     number
  */
  putCellContentByArray(sName: string, sJsonContent: string): Promise<number>;

  /**
   * @api {function} none getAllTableNamesByCurrentDoc
   * @apiName getAllTableNamesByCurrentDoc
   * @apiGroup 86_tableAPI
   * @apiVersion  1.0.0
   * @apiDescription getAllTableNamesByCurrentDoc(): string
   * <p>获取当前文档中所有表格名字</p>
   * @apiSuccessExample {string} Success:
   *     string
   * "talbe1","table2","table3"
  */
  getAllTableNamesByCurrentDoc(): Promise<string>;

  /**
   * @api {function} none getTableNameByCurrentCursor
   * @apiName getTableNameByCurrentCursor
   * @apiGroup 86_tableAPI
   * @apiVersion  1.0.0
   * @apiDescription getTableNameByCurrentCursor(): string
   * <p>返回当前光标位置的表格名</p>
   * @apiSuccessExample {string} Success:
   *     string
  */
  getTableNameByCurrentCursor(): Promise<string>;

  /**
   * @api {function} none incMultiRows
   * @apiName incMultiRows
   * @apiGroup 86_tableAPI
   * @apiVersion  1.0.0
   * @apiDescription incMultiRows(name: string, nIndex: number , nCount: number, nDirection: number): number
   * <p>对指定表格的某行之前（后）插入数行</p>
   * @apiparam {string} name 表格名称
   * @apiparam {number} nIndex 从1开始整数(1表示表格第一行上面插入行)
   * @apiparam {number} nCount 插入几行
   * @apiparam {number} nDirection 1:往下新增行; 0:往上新增行
   * @apiSuccessExample {number} Success:
   *     number
  */
  incMultiRows(name: string, nIndex: number, nCount: number, nDirection: number): Promise<number>;

  /**
   * @api {function} none delMultiRows
   * @apiName delMultiRows
   * @apiGroup 86_tableAPI
   * @apiVersion  1.0.0
   * @apiDescription delMultiRows(sName: string, index: number, nCount: number, nDirection: number): number
   * <p>删除指定行</p>
   * @apiparam {string} name 表格名称
   * @apiparam {number} nIndex 删除行位置
   * @apiparam {number} nCount 删除多少行
   * @apiparam {number} nDirection 1:往下删除; 0:往上删除
   * @apiSuccessExample {number} Success:
   *     number
   * 如果指定行下面不够足够数量的行删除，比如删除3行，只有2行，则只删除2行，接口依旧返回正确。
  */
  delMultiRows(sName: string, index: number, nCount: number, nDirection: number): Promise<number>;

  /**
   * @api {function} none setTableProp
   * @apiName setTableProp
   * @apiGroup 86_tableAPI
   * @apiVersion  1.0.0
   * @apiDescription setTableProp(sName: string, sProp: string): number
   * <p>设置表格的自定义属性</p>
   * @apiparam {string} sName 表格名称
   * @apiparam {string} sProp 属性 json格式
   * @apiparamExample {json} sProp
   * {
   *   [
  *      {"name":"属性1","value":1},
  *      {"name":"属性2","value":"值2"}
   *   ]
   * }
   * @apiSuccessExample {number} Success:
   *     number
  */
  setTableProp(sName: string, sProp: string): Promise<number>;

    /**
   * @api {function} none setTableCellFormula
   * @apiName setTableCellFormula
   * @apiGroup 86_tableAPI
   * @apiVersion  1.0.0
   * @apiDescription setTableCellFormula(sName: string, sCell:string,sFormula:string): number
   * <p>设置表格单元格的公式</p>
   * @apiparam {string} sName 表格名称
   * @apiparam {string} sCell 单元格名称
   * @apiparam {string} sFormula 公式
   * @apiparamExample {json} sFormula
   * 举例说明：
   * 求和： SUM(A1,D1)
   * 相加:  ADD(A1+B1+C1)
   * 相乘:  MUL(A1,B1)  (只能计算2个)
   * 四则混合运算:MIX((A1-1)+2*B1)
   * @apiSuccessExample {number} Success:
   *     number
  */
    setTableCellFormula(sName: string, sCell: string,sFormula:string): Promise<number>;

  /**
   * @api {function} none getTableProp
   * @apiName getTableProp
   * @apiGroup 86_tableAPI
   * @apiVersion  1.0.0
   * @apiDescription getTableProp(sName: string, sProp: string): string
   * <p>获取表格的自定义属性</p>
   * @apiparam {string} sName 表格名称
   * @apiparam {string} sProp 属性名 json格式
   * @apiparamExample {json} sProp
   * {
   *   [
  *      {"name":"属性1"},
  *      {"name":"属性2"}
   *   ]
   * }
   * @apiSuccessExample {string} Success:
   *     string
   * 返回json格式如下：
   *   [
   *      {"name":"属性1","value":1},
   *      {"name":"属性2","value":"值2"}
   *   ]

  */
  getTableProp(sName: string, sProp: string): Promise<string>;

  /**
   * @api {function} none getTableXmlInfoByParament
   * @apiName getTableXmlInfoByParament
   * @apiGroup 86_tableAPI
   * @apiVersion  1.0.0
   * @apiDescription getTableXmlInfoByParament(sName: string, sRev: string): string
   * <p>获取表格内结构化元素的信息</p>
   * @apiparam {string} sName 表格名称
   * @apiparam {string} sRev 预留参数 无用
   * @apiSuccessExample {json} Success:
   * //string 返回json格式如下：
   *  {
  *   "textbox1": {
  *     "type": "1",
  *     "content_text": "第一个文本元素",
  *     "property": {
  *        "Placeholder": "占位符",
  *        "IsMustInput": true,
  *        "DeleteProtect": true,
  *        "EditProtect": false,
  *        "ShowBorder": true,
  *        "HiddenBackground": false,
  *        "ReverseEdit": false,
  *        "Hidden": false,
  *        "CustomProperty": {
  *                            "自定义属性1": "11111",
  *                            "自定义属性2": "22222",
  *                           },
    *	     "SelectedItem": //下拉类型的元素的选中项
  *        [
  *            {
  *                "code":"code1",
  *                "value":"value1"
  *            },
  *            {
  *                "code":"code2",
  *                "value":"value2"
  *            }
  *        ]
  *       }
  *     }
  *   }
  */
  getTableXmlInfoByParament(sName: string, sRev: string): Promise<string>;

  /**
   * @api {function} none getTableCellNameByCurrentCursor
   * @apiName getTableCellNameByCurrentCursor
   * @apiGroup 86_tableAPI
   * @apiVersion  1.0.0
   * @apiDescription getTableCellNameByCurrentCursor(): string
   * <p>获取光标所在的表格的单元格名称</p>
   * @apiSuccessExample {string} Success:
   *     string
  */
  getTableCellNameByCurrentCursor(): Promise<string>;

  /**
   * @api {function} none getCellContent
   * @apiName getCellContent
   * @apiGroup 86_tableAPI
   * @apiVersion  1.0.0
   * @apiDescription getCellContent(sTableName: string, sCellName: string): string
   * <p>获取指定表格指定单元格的文本内容</p>
   * @apiparam {string} sTableName 表格名称
   * @apiparam {string} sCellName 单元格名称，类似"A1","B2"之类
   * @apiSuccessExample {string} Success:
   *     string
   * 返回指定单元格里的内容。
   * 假设存在结构化元素，会过滤占位符，不过滤标题。
  */
  getCellContent(sTableName: string, sCellName: string): Promise<string>;

  /**
   * @api {function} none getStructNamesByCell
   * @apiName getStructNamesByCell
   * @apiGroup 86_tableAPI
   * @apiVersion  1.0.0
   * @apiDescription getStructNamesByCell(sTableName: string, sCellName: string): string
   * <p>获取指定表格指定单元格中的结构化元素名称列表</p>
   * @apiparam {string} sTableName 表格名称
   * @apiparam {string} sCellName 单元格名称，类似"A1","B2"之类
   * @apiSuccessExample {string} Success:
   *     string
   * 返回指定单元格中的结构化元素名称，多个名称以英文逗号分隔。
   * 如果单元格中没有结构化元素，则返回空字符串。
  */
  
  /**
   * @api {function} none getTableRowCount
   * @apiName getTableRowCount
   * @apiGroup 86_tableAPI
   * @apiVersion  1.0.0
   * @apiDescription getTableRowCount(sTableName: string): number
   * <p>获取指定表格的行数</p>
   * @apiparam {string} sTableName 表格名称
   * @apiSuccessExample {number} Success:
   *     number
   * 返回指定表格的行数。
   * 如果表格不存在，则返回0。
  */
  getTableRowCount(sTableName: string): Promise<number>;
  
  /**
   * @api {function} none getTableColumnCount
   * @apiName getTableColumnCount
   * @apiGroup 86_tableAPI
   * @apiVersion  1.0.0
   * @apiDescription getTableColumnCount(sTableName: string): number
   * <p>获取指定表格的列数</p>
   * @apiparam {string} sTableName 表格名称
   * @apiSuccessExample {number} Success:
   *     number
   * 返回指定表格的列数。
   * 如果表格不存在，则返回0。
  */
  getTableColumnCount(sTableName: string): Promise<number>;
  getStructNamesByCell(sTableName: string, sCellName: string): Promise<string>;

  /**
   * @api {function} none mergeTableCell
   * @apiName mergeTableCell
   * @apiGroup 86_tableAPI
   * @apiVersion  1.0.0
   * @apiDescription mergeTableCell(sTableName: string, sCellNames: string): boolean
   * <p>合并目标表格的指定单元格</p>
   * @apiparam {string} sTableName 表格名称
   * @apiparam {string} sCellName 单元格名称（多个单元格以逗号隔开）类似"A1,B2"
   * @apiSuccessExample {boolean} Success:
   *     boolean
   * 假设光标不在表格内，则调用该接口无任何效果。
  */
  mergeTableCell(sTableName: string, sCellNames: string): boolean;

  /**
   * @api {function} none getColContent
   * @apiName getColContent
   * @apiGroup 86_tableAPI
   * @apiVersion  1.0.0
   * @apiDescription getColContent(sName: string, sCol: string): string
   * <p>获取指定表格指定单元格所在列的文本内容</p>
   * @apiparam {string} sName 表格名称
   * @apiparam {string} sCol 单元格名称
   * @apiSuccessExample {string} Success:
   *     string
  */
  getColContent(sName: string, sCol: string): string;

   /**
   * @api {function} none protectTable
   * @apiName protectTable
   * @apiGroup 86_tableAPI
   * @apiVersion  1.0.0
   * @apiDescription protectTable(sTable: string, bEditorProtect: boolean): number
   * <p>对指定表格设置 不可编辑不可删除 保护</p>
   * @apiparam {string} sTable 表格名称
   * @apiparam {boolean} bEditorProtect true:启用保护 false:解除保护
   * @apiSuccessExample {number} Success:
   *     number
  */
  protectTable(sTable: string, bEditorProtect: boolean): Promise<number>;

  /**
   * @api {function} none protectTableCell
   * @apiName protectTableCell
   * @apiGroup 86_tableAPI
   * @apiVersion  1.0.0
   * @apiDescription protectTableCell(sTable: string, sCellName: string, bEditorProtect: boolean): Promise<number>
   * <p>对指定表格的指定单元格设置 不可编辑不可删除 保护</p>
   * @apiparam {string} sTable 表格名称
   * @apiparam {string} sCellName 单元格名称
   * @apiparam {boolean} bEditorProtect true:启用保护 false:解除保护
   * @apiSuccessExample {number} Success:
   *     number
  */
  protectTableCell(sTable: string, sCellName: string, bEditorProtect: boolean): Promise<number>;

  /**
   * @api {function} none insertRegionAtCurrentCursor
   * @apiName insertRegionAtCurrentCursor
   * @apiGroup 90_regionAPI
   * @apiVersion  1.0.0
   * @apiDescription insertRegionAtCurrentCursor(sName: string): string
   * <p>当前光标位置插入区域</p>
   * @apiparam {string} sName 区域名称
   * @apiSuccessExample {string} Success:
   * 1. 返回插入区域的name
   * 2. 选择了一片文本，然后调用该接口，则这些文本作为区域的内容。
   * 3. 假设选择区域（当前光标位置）非法，调用该接口会失败。
  */
  insertRegionAtCurrentCursor(sName: string): Promise<string>;

  /**
   * @api {function} none setRegionSubElementsReverseEdit
   * @apiName setRegionSubElementsReverseEdit
   * @apiGroup 90_regionAPI
   * @apiVersion  1.0.0
   * @apiDescription setRegionSubElementsReverseEdit(sRegionName: string, bReverseEdit: boolean): Promise<number>
   * <p>设置区域的子元素是否逆序编辑</p>
   * @apiparam {string} sRegionName 区域名称
   * @apiparam {boolean} bReverseEdit 是否逆序编辑
   * @apiSuccessExample {number} Success:
   *     number
   */
  setRegionSubElementsReverseEdit(sRegionName: string, bReverseEdit: boolean): Promise<number>;


  /**
   * @api {function} none swapRegions
   * @apiName swapRegions
   * @apiGroup 90_regionAPI
   * @apiVersion  1.0.0
   * @apiDescription swapRegions(sRegionA:string,sRegionB:String): number
   * <p>交换2个区域的位置</p>
   * @apiparam {string} sRegionA 区域名称
   * @apiparam {string} sRegionB 区域名称
   * @apiSuccessExample {number} Success:
   * number 交换的区域仅适用于未嵌套
  */
  swapRegions(sRegionA:string,sRegionB:string):Promise<number>;

    /**
   * @api {function} none sortRegions
   * @apiName sortRegions
   * @apiGroup 90_regionAPI
   * @apiVersion  1.0.0
   * @apiDescription sortRegions(sJson:string): number
   * <p>按照json给出的排列顺序 对文档中的区域进行排序</p>
   * @apiparam {string} sJson json
  * @apiparamExample {json} sProp
   *   [
  *     "region1",
  *     "region2",
  *     "region4",
  *     "region3"
   *   ]
  * @apiExample  {javascript} Example:
  * 病历排序例子
   * this.getFileContentFromServerByName("病历排序", "businessFile")
  *.then(async (blob) => {
  *  await this.editorFunction.openDocumentWithStream(blob);
  *  const strName = await this.editorFunction.getFirstLevelRegionNames();
  *  const regionNames = strName.split(',');
  *
  * // 存放日期与区域名称的映射
  *  const regionDateMap = [];
*
  *  for (const region of regionNames) {
  *      const strControlNames = await this.editorFunction.filterStructsByPropInRegion(region, 'ControlName', '记录日期');
  *      // strControlNames 格式类似 'region1|struct1,struct2'
  *      const parts = strControlNames.split('|');
  *      const firstSubstring = parts[1].split(',')[0]; // 获取符合条件的第一个 struct 的 name
  *      const dateValue = await this.editorFunction.getDateTimeBoxValueEx(firstSubstring, 'AllDate');//此时返回的日期格式为：Date=2015-04-22 Time=19:04:00
*
  *      if (typeof dateValue === 'string' && dateValue) {
  *          // 提取日期和时间并组合成 ISO 格式的字符串
  *          const dateMatch = dateValue.match(/Date=(\d{4}-\d{2}-\d{2}) Time=(\d{2}:\d{2}:\d{2})/);
  *          if (dateMatch) {
  *              const fullDateTime = `${dateMatch[1]}T${dateMatch[2]}`; // 组合为 ISO 格式
  *              regionDateMap.push({ region, dateValue: new Date(fullDateTime) });
  *          }
  *      }
  * }
*
  *  // 按照日期值排序区域
  *  regionDateMap.sort((a, b) => a.dateValue.getTime() - b.dateValue.getTime());// 比较时间戳
*
  *  // 获取排序后的区域名称列表
  *  const sortedRegions = regionDateMap.map(item => item.region);
*
  *  //假设 首次病程必须保证在第一个，不管日期是多少。
  *  const targetRegion = regionNames[0]; // 假设regionNames[0] 为 首次病程记录的region 名
  *  const sortedRegions2 = [
  *    targetRegion, // 将目标region放在第一位
  *    ...regionDateMap
  *      .map(item => item.region)
  *      .filter(region => region !== targetRegion)
  *  ];
  *  // 格式化排序后的区域为JSON结构
  *  const sortedJson = JSON.stringify(sortedRegions2);
  *  const result = await this.editorFunction.sortRegions(sortedJson);
*
  *  console.log("sort result is ",result);
  *})
  *.catch((error) => {
  *  console.error("Error fetching blob:", error);
  *});
   * @apiSuccessExample {number} Success:
   * number 适用与大病程的区域排序
  */
  sortRegions(sJson:string):Promise<number>;


  /**
   * @api {function} none getIncompletedCtrlNameListInRegion
   * @apiName getIncompletedCtrlNameListInRegion
   * @apiGroup 90_regionAPI
   * @apiVersion  1.0.0
   * @apiDescription getIncompletedCtrlNameListInRegion(sRegionA:string): string
   * <p>获取指定区域所有必填项未填的结构的名称列表</p>
   * @apiparam {string} sRegion 区域名称
   * @apiSuccessExample {string} Success:
   * string 为空则表示没有未填的必选项；
   * 指定区域所有必填项未填的结构名称列表，以,隔开
   * 如："数据元1,数据元2,数据元3,节1"
  */
  getIncompletedCtrlNameListInRegion( sRegion:string ):Promise<string>;

  /**
   * @api {function} none setFocus
   * @apiName setFocus
   * @apiGroup 8_editAPI
   * @apiVersion  1.0.0
   * @apiDescription setFocus(flag: boolean): number
   * <p>让编辑器失去或者拥有焦点</p>
   * @apiparam {boolean} flag true: 拥有；false: 失去
   * @apiSuccessExample {number} Success:
   *     number
  */
  setFocus(flag: boolean): Promise<number>;

  /**
   * @api {function} none deleteRedundantByRegionName
   * @apiName deleteRedundantByRegionName
   * @apiGroup 90_regionAPI
   * @apiVersion  1.0.0
   * @apiDescription deleteRedundantByRegionName(strName: string, bPageBreak: boolean): number
   * <p>删除指定区域内部末尾的空白,包括：空行，空格，Tab字符等，分页符可选</p>
   * @apiparam {string} sName 区域名称
   * @apiparam {boolean} bPageBreak true:删除分页符 false:不删除分页符
   * @apiSuccessExample {string} Success:
   *  number
  */
  deleteRedundantByRegionName(strName: string, bPageBreak: boolean): Promise<number>;

  /**
   * @api {function} none insertRegionAfterOneRegion
   * @apiName insertRegionAfterOneRegion
   * @apiGroup 90_regionAPI
   * @apiVersion  1.0.0
   * @apiDescription insertRegionAfterOneRegion(sRegion: string, sPrveRegion: string): number
   * <p>在指定区域的后面插入一个区域</p>
   * @apiparam {string} sRegion 新插入的区域名称
   * @apiparam {string} sPrveRegion 指定位置的区域名称
   * @apiSuccessExample {number} Success:
   *  number
  */
  insertRegionAfterOneRegion(sRegion: string, sPrveRegion: string): Promise<number>;

  /**
   * @api {function} none saveRegionContentToStream
   * @apiName saveRegionContentToStream
   * @apiGroup 1_documentAPI
   * @apiVersion  1.0.0
   * @apiDescription saveRegionContentToStream(sRegion: string, sJson: string): Blob
   * <p>将指定区域根据参数保存为blob</p>
   * @apiparam {string} sRegion 区域名称
   * @apiparam {string} sJson 参数json
   * @apiparamExample {json} sJsons
   *{
   * "NeedsignalContent":0, //是否需要保留签名控件的内容
   * "NeedregionStruct":0   //是否需要保留指定区域的结构
   *}
   * @apiSuccessExample {blob} Success:
   *  Promise<Blob>
  */
  saveRegionContentToStream(sRegion:string,sJson:string): Promise<Blob>;

  /**
   * @api {function} none getCurrentRegionName
   * @apiName getCurrentRegionName
   * @apiGroup 90_regionAPI
   * @apiVersion  1.0.0
   * @apiDescription getCurrentRegionName(): string
   * <p>获得当前光标所在的region的名称</p>
   * @apiSuccessExample {string} Success:
   *  string
  */
  getCurrentRegionName(): Promise<string>;

  /**
   * @api {function} none getRegionModifyFlag
   * @apiName getRegionModifyFlag
   * @apiGroup 90_regionAPI
   * @apiVersion  1.0.0
   * @apiDescription getRegionModifyFlag(sRegionName: string): number
   * <p>获取指定一个区域的脏标记状态</p>
   * @apiparam {string} sRegionName 区域名称
   * @apiSuccessExample {number} Success:
   *  number
   * 1：已被修改
   * 0：没有修改
  */
  getRegionModifyFlag(sRegionName: string): Promise<number>;

  /**
   * @api {function} none getRegionText
   * @apiName getRegionText
   * @apiGroup 90_regionAPI
   * @apiVersion  1.0.0
   * @apiDescription getRegionText(name: string): string
   * <p>获取指定一个区域的文本内容</p>
   * @apiparam {string} name 区域名称
   * @apiSuccessExample {string} Success:
   *  string
  */
  getRegionText(name: string): Promise<string>;

  /**
   * @api {function} none getAllRegionsByModifyFlag
   * @apiName getAllRegionsByModifyFlag
   * @apiGroup 90_regionAPI
   * @apiVersion  1.0.0
   * @apiDescription getAllRegionsByModifyFlag(nOnlyFirstRegions: number, nModifyFlag: number): string
   * <p>获取整个文档区域的脏标记符合某个状态的集合，可以获取最外层区域，或者所有层次的区域</p>
   * @apiparam {number} nOnlyFirstRegions 1：只获取最外层 0：获取所有区域
   * @apiparam {number} nModifyFlag 1：只获取修改过的 0：只获取未修改过的
   * @apiExample  {javascript} Example:
   * //区域脏标记功能:  区域里面的内容如果发生了改变，该脏标记会自动设置为1.
   * //该脏标记可以被接口置位为0.
   * //用区域设计大病历，有些业务逻辑需要将某天修改的病程单独抽流保存出去
   * //保存完毕后，调用接口cleanAllRegionsModifyFlag 清理掉区域的脏标记。
   * //如果需要设计此类功能，可以用如下代码实现：
   * {  const regionsString = editor.getAllRegionsByModifyFlag(1,1);//抽取最外层脏标记为1的区域id 返回分号拼接的 string
   *   // 拆分出子 string
   *   const regionsArray = regionsString.split(';');
   *   // 循环遍历每个子 string
   *   regionsArray.forEach(region => {
   *     // 调用接口进行保存
   *     const promise = saveRegionContentToStream(region, modeJson);//modeJson提前设置好
   *         .then(asyncBlob => {
   *             // 返回 Promise(blob)
   *             // 可以在这里进行任何额外的处理，比如上传数据库或者转换base64
   *             return asyncBlob;
   *         });
   * });
   * // 完成后清除状态
   * editor.cleanAllRegionsModifyFlag();
   * };
   * @apiSuccessExample {string} Success:
   *  string
   * 符合条件的所有区域名称，用分号隔开";"
  */
  getAllRegionsByModifyFlag(nOnlyFirstRegions: number, nModifyFlag: number): Promise<string>;

  /**
   * @api {function} none cleanAllRegionsModifyFlag
   * @apiName cleanAllRegionsModifyFlag
   * @apiGroup 90_regionAPI
   * @apiVersion  1.0.0
   * @apiDescription cleanAllRegionsModifyFlag(): number
   * <p>将当前文档所有的区域记录脏标记置位为0</p>
   * @apiSuccessExample {number} Success:
   *  number
  */
  cleanAllRegionsModifyFlag(): Promise<number>;

  /**
   * @api {function} none getRegionProp
   * @apiName getRegionProp
   * @apiGroup 90_regionAPI
   * @apiVersion  1.0.0
   * @apiDescription getRegionProp(sName: string, sProp: string): any
   * <p>获得区域属性</p>
   * @apiparam {string} sName 区域名称
   * @apiparam {string} sProp 属性名
   * @apiSuccessExample {number} Success:
   *  any
  */
  getRegionProp(sName: string, sProp: string): Promise<any>;

  /**
   * @api {function} none setRegionProp
   * @apiName setRegionProp
   * @apiGroup 90_regionAPI
   * @apiVersion  1.0.0
   * @apiDescription setRegionProp(sName: string, sProp: string,sValue: any): number
   * <p>设置区域属性</p>
   * @apiparam {string} sName 区域名称
   * @apiparam {string} sProp 属性名
   * @apiparam {any} sValue 属性值
   * @apiSuccessExample {number} Success:
   *  number
  */
  setRegionProp(sName: string, sProp: string, sValue: any): Promise<number>;

  /**
   * @api {function} none setRegionPropByArray
   * @apiName setRegionPropByArray
   * @apiGroup 90_regionAPI
   * @apiVersion  1.0.0
   * @apiDescription setRegionPropByArray(sName: string, sJsons: string): number
   * <p>批量设置区域的多个属性</p>
   * @apiparam {string} sName 区域名称
   * @apiparam {string} sJsons 区域属性集合
   * @apiparamExample {json} sJsons
   * {
   * "property": {
   *     "DeleteProtect": true,
   *     "EditProtect": false,
   *     "CustomProperty": {
   *         "自定义属性1": "11111",
   *         "自定义属性2": "22222",
   *     }
   *   }
   * }
   * @apiSuccessExample {number} Success:
   *  number
  */
  setRegionPropByArray(sName: string, sJsons: string): Promise<number>;

  /**
   * @api {function} none getRegionPropByArray
   * @apiName getRegionPropByArray
   * @apiGroup 90_regionAPI
   * @apiVersion  1.0.0
   * @apiDescription getRegionPropByArray(sName: string): string
   * <p>获取指定区域的所有属性</p>
   * @apiparam {string} sName 区域名称
   * @apiSuccessExample {string} Success:
   *  string
   * 以json格式返回属性集合
  */
  getRegionPropByArray(sName: string): Promise<string>;

  /**
   * @api {function} none setRegionBorderViewMode
   * @apiName setRegionBorderViewMode
   * @apiGroup 90_regionAPI
   * @apiVersion  1.0.0
   * @apiDescription setRegionBorderViewMode(nViewType: number): number
   * <p>设置区域的边框的显示模式</p>
   * @apiparam {number} nViewType 边框的显示模式 1:模式1 ;2:正常模式;3:无边框 默认为2
   * @apiSuccessExample {number} Success:
   *  number
   * 模式1：当光标不在任何一个区域内时，区域边框都常显灰色，当光标在某一个区域内时，该区域边框显示以前的彩色。
  */
  setRegionBorderViewMode(nViewType: number): Promise<number>;

  /**
   * @api {function} none setRegionName
   * @apiName setRegionName
   * @apiGroup 90_regionAPI
   * @apiVersion  1.0.0
   * @apiDescription setRegionName(sName: string, sNewName: string): number
   * <p>重新命名指定区域</p>
   * @apiparam {string} sName 区域名
   * @apiparam {string} sNewName 区域新的名称
   * @apiSuccessExample {number} Success:
   *  number
  */
  setRegionName(sName: string, sNewName: string): Promise<number>;

  /**
   * @api {function} none deleteRegion
   * @apiName deleteRegion
   * @apiGroup 90_regionAPI
   * @apiVersion  1.0.0
   * @apiDescription deleteRegion(sName: string, lFlag: number): number
   * <p>删除一个区域</p>
   * @apiparam {string} sName 区域名称
   * @apiparam {number} lFlag 1：删除区域结构（内容不删除）2：删除区域内容 3：删除区域和内容
   * @apiSuccessExample {number} Success:
   *  number
  */
  deleteRegion(sName: string, lFlag: number): Promise<number>;

  /**
   * @api {function} none getOutestRegionNameByCurrentCursor
   * @apiName getOutestRegionNameByCurrentCursor
   * @apiGroup 90_regionAPI
   * @apiVersion  1.0.0
   * @apiDescription getOutestRegionNameByCurrentCursor(): string
   * <p>获取当前光标所在区域的最外层区域名称</p>
   * @apiSuccessExample {string} Success:
   *  string
   * 1. 如果当前光标不在区域内，则返回空字符串.
   * 2. 如果当前光标已经在最外层区域，那直接返回当前区域名称.
  */
  getOutestRegionNameByCurrentCursor(): Promise<string>;

  /**
   * @api {function} none getFatherRegionNameOfOneStruct
   * @apiName getFatherRegionNameOfOneStruct
   * @apiGroup 90_regionAPI
   * @apiVersion  1.0.0
   * @apiDescription getFatherRegionNameOfOneStruct(sStructsName: string): string
   * <p>获取指定结构的最近的父级区域的名称</p>
   * @apiparam {string} sStructsName 元素，节，区域。
   * @apiSuccessExample {string} Success:
   *  string
   * 1. 如果当前光标不在区域内，则返回空字符串.
   * 2. 如果当前光标已经在最外层区域，那直接返回当前区域名称.
  */
  getFatherRegionNameOfOneStruct(sStructsName: string): Promise<string>;

  /**
   * @api {function} none filterStructsByPropInRegion
   * @apiName filterStructsByPropInRegion
   * @apiGroup 90_regionAPI
   * @apiVersion  1.0.0
   * @apiDescription filterStructsByPropInRegion(sRegionName: string, sPropName: string, sPropValue: any): string
   * <p>按照某个属性名（可以同时带属性值）进行筛选，筛选出符合条件的结构名</p>
   * @apiparam {string} sRegionName 区域名。
   * @apiparam {string} sPropName 属性名
   * @apiparam {any} sPropValue 属性值
   * @apiSuccessExample {string} Success:
   *  string
   * 1. 在指定区域内，返回符合条件的所有的结构名称 区域跟结构以"|"隔开，批次之间以"," 隔开
  */
  filterStructsByPropInRegion(sRegionName: string, sPropName: string, sPropValue: any): Promise<string>;

   /**
   * @api {function} none getAllRegionNamesByCurrentDoc
   * @apiName getAllRegionNamesByCurrentDoc
   * @apiGroup 90_regionAPI
   * @apiVersion  1.0.0
   * @apiDescription getAllRegionNamesByCurrentDoc(): string
   * <p>依次返回当前文档中Region名称列表</p>
   * @apiSuccessExample {string} Success:
   *  string 以,隔开的字符串
  */
  getAllRegionNamesByCurrentDoc(): Promise<string>;

  /**
   * @api {function} none getFirstLevelRegionNames
   * @apiName getFirstLevelRegionNames
   * @apiGroup 90_regionAPI
   * @apiVersion  1.0.0
   * @apiDescription getFirstLevelRegionNames(): string
   * <p>返回文档第一层Region名称列表</p>
   * @apiSuccessExample {string} Success:
   *  string 以英文逗号隔开的string "aaa,bbb"
  */
  getFirstLevelRegionNames(): Promise<string>;

  /**
   * @api {function} none setRegionsReadOnlyProp
   * @apiName setRegionsReadOnlyProp
   * @apiGroup 90_regionAPI
   * @apiVersion  1.0.0
   * @apiDescription setRegionsReadOnlyProp(sCurrentRegionName: string, bCurrent: boolean, bLef: boolean): number
   * <p>对指定的某个区域跟其他剩余的区域设置只读状态</p>
   * @apiparam {string} sCurrentRegionName 区域名称
   * @apiparam {boolean} bCurrent 指定区域只读标识。 true：只读  false：可编辑
   * @apiparam {boolean} bLef 除了指定区域外，其他最外层区域的只读标识。 true：只读 false：可编辑
   * @apiSuccessExample {number} Success:
   *  number
  */
  setRegionsReadOnlyProp(sCurrentRegionName: string, bCurrent: boolean, bLef: boolean): Promise<number>;

   /**
   * @api {function} none getRegionEnd
   * @apiName getRegionEnd
   * @apiGroup 90_regionAPI
   * @apiVersion  1.0.0
   * @apiDescription getRegionEnd(sName: string): string
   * <p>获得指定区域尾位置索引</p>
   * @apiparam {string} sName 区域名称
   * @apiSuccessExample {string} Success:
   *  string
  */
  getRegionEnd(sName: string): Promise<string>;

  /**
   * @api {function} none getRegionBegin
   * @apiName getRegionBegin
   * @apiGroup 90_regionAPI
   * @apiVersion  1.0.0
   * @apiDescription getRegionBegin(sName: string): string
   * <p>获得指定区域头位置索引</p>
   * @apiparam {string} sName 区域名称
   * @apiSuccessExample {string} Success:
   *  string
  */
  getRegionBegin(sName: string): Promise<string>;

  /**
   * @api {function} none addNewLineForRegion
   * @apiName addNewLineForRegion
   * @apiGroup 90_regionAPI
   * @apiVersion  1.0.0
   * @apiDescription addNewLineForRegion(sRegion: string，nPosType: number): number
   * <p>在指定区域的前端或者后端加入一个新行。</p>
   * @apiparam {string} sRegion 区域名称
   * @apiparam {number} nPosType 1 – 区域前端 2 – 区域后端
   * @apiSuccessExample {number} Success:
   *  number
  */
  addNewLineForRegion(sRegion: string, nPosType: number): Promise<number>;

  /**
   * @api {function} none changeAllFileToOneRegion
   * @apiName changeAllFileToOneRegion
   * @apiGroup 90_regionAPI
   * @apiVersion  1.0.0
   * @apiDescription changeAllFileToOneRegion(sRegionName: string): string
   * <p>将当前文档的所有内容变成一个区域</p>
   * @apiparam {string} sRegionName 区域名称
   * @apiSuccessExample {string} Success:
   *  string
   * 1. 返回插入区域的名称。
   * 2. 为空表示失败。
   * 3. 假如文档中已经存在2层区域，则接口会失败。
  */
  changeAllFileToOneRegion(sRegionName: string): Promise<string>;

  /**
   * @api {function} none setRegionText
   * @apiName setRegionText
   * @apiGroup 90_regionAPI
   * @apiVersion  1.0.0
   * @apiDescription setRegionText(sName: string, sText: string): number
   * <p>设置指定区域内容文本</p>
   * @apiparam {string} sName 区域名称
   * @apiparam {string} sText 区域内容
   * @apiSuccessExample {number} Success:
   *  number
  */
  setRegionText(sName: string, sText: string): Promise<number>;

  /**
   * @api {function} none cursorJumpOutOfOneRegion
   * @apiName cursorJumpOutOfOneRegion
   * @apiGroup 90_regionAPI
   * @apiVersion  1.0.0
   * @apiDescription cursorJumpOutOfOneRegion(strName: string, bBack: boolean): number
   * <p>光标跳转到某一个区域的前面或者后面</p>
   * @apiparam {string} strName 区域名称
   * @apiparam {boolean} bBack true：区域外的行开始处； false：区域前的行结束处。
   * @apiSuccessExample {number} Success:
   *  number
  */
  cursorJumpOutOfOneRegion(strName: string, bBack: boolean): Promise<number>;

  /**
   * @api {function} none selectOneRegion
   * @apiName selectOneRegion
   * @apiGroup 90_regionAPI
   * @apiVersion  1.0.0
   * @apiDescription selectOneRegion(sName: string, bOnlyContent?: boolean): number
   * <p>选中指定名称的区域</p>
   * @apiparam {string} sName 区域名称
   * @apiparam {boolean} bOnlyContent true：只选中区域的内容。 false：暂时没用
   * @apiSuccessExample {number} Success:
   *  number
  */
  selectOneRegion(sName: string, bOnlyContent?: boolean): Promise<number>;

  /**
   * @api {function} none getRegionXmlInfoByParament
   * @apiName getRegionXmlInfoByParament
   * @apiGroup 90_regionAPI
   * @apiVersion  1.0.0
   * @apiDescription getRegionXmlInfoByParament(name: string, sRev?: string): string
   * <p>以json格式依次返回指定区域的Region, Section 和NewControl的结构化元素信息。不包含嵌套关系。</p>
   * @apiparam {string} name 区域名称
   * @apiparam {string} sRev 当值为 "onlyStruct" 时，只返回简化的结构信息（区域名称列表和NewControl名称列表）
   * @apiSuccessExample {json} Success-默认:
   *  {
   * {
   * "textbox1": {
   *     "type": "1",
   *     "content_text": "第一个文本元素",
   *     "property": {
   *         "Placeholder": "占位符",
   *         "IsMustInput": true,
   *         "DeleteProtect": true,
   *         "EditProtect": false,
   *         "ShowBorder": true,
   *         "HiddenBackground": false,
   *         "ReverseEdit": false,
   *         "Hidden": false,
   *         "CustomProperty": {
   *                                  "自定义属性1": "11111",
   *                                  "自定义属性2": "22222",
   *},
 	*		"SelectedItem":   //下拉类型的元素的选中项
  *          [
  *              {
  *                  "code":"code1",
  *                  "value":"value1"
  *              },
  *              {
  *                  "code":"code2",
  *                  "value":"value2"
  *              }
  *          ]
  *      }
  *  },
  *}
   * }
   * @apiSuccessExample {json} Success-onlyStruct:
   * {
   *   "regions": ["region1", "region2"],
   *   "newcontrols": ["textbox1", "textbox2"]
   * }
   */
  getRegionXmlInfoByParament(name: string, sRev?: string): Promise<string>;

  /**
   * @api {function} none setRegionFileLinkWithStream
   * @apiName setRegionFileLinkWithStream
   * @apiGroup 90_regionAPI
   * @apiVersion  1.0.0
   * @apiDescription setRegionFileLinkWithStream(strName: string, cContent: Blob, option?: string): Promise<number>
   * <p>往指定区域的插入一个文件流</p>
   * @apiparam {string} strName 区域名称
   * @apiparam {Blob} cContent 文件内容
   * @apiparam {string} option 可选参数，JSON字符串格式。可以设置 retainCascade 参数以保留级联关系
   * @apiExample {示例} 使用示例
  * {
  * getfilecontentFromServer()
  *    .then(blob => {
  *      // 在这里处理blob对象
  *      //const blob2 = new Blob([blob], { type: 'application/apollo-zstd' });
  *      // 插入到区域中，并设置保留级联关系
  *      editorFunction.setRegionFileLinkWithStream('region1', blob, JSON.stringify({ retainCascade: true }));
  *    })
  *    .catch(error => {
  *      console.error('Error fetching blob:', error);
  *    });
  * }
   * @apiSuccessExample {number} Success:
   *  number
  */
  setRegionFileLinkWithStream(strName: string, cContent: Blob, option?: string): Promise<number>;

  /**
   * @api {function} none getFatherStructName
   * @apiName getFatherStructName
   * @apiGroup 91_structAPI
   * @apiVersion  1.0.0
   * @apiDescription getFatherStructName(sName: string): string
   * <p>获得指定名称结构化元素的最近的父级结构化元素名称</p>
   * @apiparam {string} sName 结构化元素名称
   * @apiSuccessExample {string} Success:
   *  string
   * 返回最近的父级结构化元素名称
  */
  getFatherStructName(sName: string): Promise<string>;

  /**
   * @api {function} none getStructBySelectArea
   * @apiName getStructBySelectArea
   * @apiGroup 91_structAPI
   * @apiVersion  1.0.0
   * @apiDescription getStructBySelectArea(): string
   * <p>获得选中范围中的结构化元素的名称列表</p>
   * @apiparam {string} sName 结构化元素名称
   * @apiSuccessExample {string} Success:
   *  string
   * 1.选中范围中的结构化元素的名称列表,
   * 2.空则表示选中区域中无结构化元素.
   * 3.多个结构化元素 采用逗号分隔，依此排列。节跟元素之间用分号隔开
  */
  getStructBySelectArea(): Promise<string>;

  /**
   * @api {function} none getStructPropByArray
   * @apiName getStructPropByArray
   * @apiGroup 91_structAPI
   * @apiVersion  1.0.0
   * @apiDescription getStructPropByArray(sName: string): string
   * <p>一次获取指定结构化元素的所有属性。</p>
   * @apiparam {string} sName 结构化名称
   * @apiSuccessExample {json} Success:
   *  json格式的返回值
  */
  getStructPropByArray(sName: string): Promise<string>;

  /**
   * @api {function} none getCurrentStructName
   * @apiName getCurrentStructName
   * @apiGroup 91_structAPI
   * @apiVersion  1.0.0
   * @apiDescription getCurrentStructName(): string
   * <p>获取当前光标的结构化名称</p>
   * @apiSuccessExample {string} Success:
   *  string
  */
  getCurrentStructName(): Promise<string>;

  /**
   * @api {function} none getStructTypeByName
   * @apiName getStructTypeByName
   * @apiGroup 91_structAPI
   * @apiVersion  1.0.0
   * @apiDescription getStructTypeByName(name: string): string
   * <p>根据名称获取结构的类型，结构包括节，元素及区域</p>
   * @apiparam {number} name 结构化名称
   * @apiSuccessExample {number} Success:
   *  number
   * [NewControlType.Combox]: 1,
   * [NewControlType.ListBox]: 2,
   * [NewControlType.TextBox]: 3,
   * [NewControlType.CheckBox]: 4,
   * [NewControlType.NumberBox]: 5,
   * [NewControlType.MultiListBox]: 6,
   * [NewControlType.MultiCombox]: 7,
   * [NewControlType.DateTimeBox]: 8,
   * [NewControlType.RadioButton]: 9,
   * [NewControlType.MultiCheckBox]: 10,
   * [NewControlType.Section]: 13,
   * [NewControlType.Region]: 14,
   * [NewControlType.SignatureBox]: 15,
   * [NewControlType.MultiRadio]: 16,
   * [NewControlType.AddressBox]: 17,
   * [NewControlType.Button]: 18
  */
  getStructTypeByName(name: string): Promise<number>;

  /**
   * @api {function} none setCheckboxStatus
   * @apiName setCheckboxStatus
   * @apiGroup 92_checkAPI
   * @apiVersion  1.0.0
   * @apiDescription setCheckboxStatus(name: string,bChecked: boolean): number
   * <p>设置checkBox的勾选状态</p>
   * @apiparam {string} name checkBox名称
   * @apiparam {boolean} bChecked 选中状态
   * @apiSuccessExample {number} Success:
   *  number
  */
  setCheckboxStatus(name: string, bChecked: boolean): Promise<number>;

  /**
   * @api {function} none setCheckboxCaption
   * @apiName setCheckboxCaption
   * @apiGroup 92_checkAPI
   * @apiVersion  1.0.0
   * @apiDescription setCheckboxCaption(name: string,sCaption: string): number
   * <p>设置checkBox的文本</p>
   * @apiparam {string} name checkBox名称
   * @apiparam {string} sCaption 展示文本
   * @apiSuccessExample {number} Success:
   *  number
  */
  setCheckboxCaption(name: string, sCaption: string): Promise<number>;

  /**
   * @api {function} none getCheckboxCaption
   * @apiName getCheckboxCaption
   * @apiGroup 92_checkAPI
   * @apiVersion  1.0.0
   * @apiDescription getCheckboxCaption(name: string): string
   * <p>读取checkBox的文本</p>
   * @apiparam {string} name checkBox名称
   * @apiSuccessExample {string} Success:
   *  string
  */
  getCheckboxCaption(name: string): Promise<string>;

  /**
   * @api {function} none setCheckBoxCode
   * @apiName setCheckBoxCode
   * @apiGroup 92_checkAPI
   * @apiVersion  1.0.0
   * @apiDescription setCheckBoxCode(name: string,sCode: string): number
   * <p>设置checkBox的 code 文本</p>
   * @apiparam {string} name checkBox名称
   * @apiparam {string} sCode 对应的属性值
   * @apiSuccessExample {number} Success:
   *  number
  */
  setCheckBoxCode(name: string, sCode: string): number;

  /**
   * @api {function} none getCheckBoxCode
   * @apiName getCheckBoxCode
   * @apiGroup 92_checkAPI
   * @apiVersion  1.0.0
   * @apiDescription getCheckBoxCode(name: string): string
   * <p>获取checkBox的 code 文本</p>
   * @apiparam {string} name checkBox名称
   * @apiSuccessExample {string} Success:
   *  string
  */
  getCheckBoxCode(name: string): string;

  /**
   * @api {function} none getAllGroupCheckboxName
   * @apiName getAllGroupCheckboxName
   * @apiGroup 92_checkAPI
   * @apiVersion  1.0.0
   * @apiDescription getAllGroupCheckboxName(): string
   * <p>返回所有设置过组的checkbox的组名</p>
   * @apiSuccessExample {string} Success:
   *  string
   * 1.返回字符串以','隔开
   * 2.如果没有，则返回''
  */
  getAllGroupCheckboxName(): Promise<string>;

  /**
   * @api {function} none getGroupCheckboxStatus
   * @apiName getGroupCheckboxStatus
   * @apiGroup 92_checkAPI
   * @apiVersion  1.0.0
   * @apiDescription getGroupCheckboxStatus(sGroupName: string): number
   * <p>返回指定组的checkbox的check状态</p>
   * @apiparam {string} sGroupName 组名
   * @apiSuccessExample {number} Success:
   *  number
   * 1:checked   0:未check  -1:失败
  */
  getGroupCheckboxStatus(sGroupName: string): Promise<number>;

  /**
   * @api {function} none getCheckboxGroupName
   * @apiName getCheckboxGroupName
   * @apiGroup 92_checkAPI
   * @apiVersion  1.0.0
   * @apiDescription getCheckboxGroupName(sCheckBox: string): string
   * <p>返回指定checkbox的组名</p>
   * @apiparam {string} sCheckBox checkbox名称
   * @apiSuccessExample {number} Success:
   *  number
  */
  getCheckboxGroupName(sCheckBox: string): Promise<string>;

  /**
   * @api {function} none showInfoToGroupCheckbox
   * @apiName showInfoToGroupCheckbox
   * @apiGroup 92_checkAPI
   * @apiVersion  1.0.0
   * @apiDescription showInfoToGroupCheckbox(sGroupName: string, sInfo: string): number
   * <p>返回指定checkbox的组名</p>
   * @apiparam {string} sGroupName 组名
   * @apiparam {string} sInfo 特定的信息
   * @apiExample {javascript} Example
   * {
   *   //注意，显示弹框出来后，3秒自动消失，或者点击编辑器任意区域，也消失。
   *   //可能会同时调用该接口多次，点击任意区域，所有的弹框都要消失。
   *   editor.showInfoToGroupCheckbox("组1","必须勾选一项");
   * }
   * @apiSuccessExample {number} Success:
   *  number
  */
  showInfoToGroupCheckbox(sGroupName: string, sInfo: string): Promise<number>;

  /**
   * @api {function} none setRadioButtonCodeAndValueByArray
   * @apiName setRadioButtonCodeAndValueByArray
   * @apiGroup 92_checkAPI
   * @apiVersion  1.0.0
   * @apiDescription setRadioButtonCodeAndValueByArray(sName: string, sJson: string): number
   * <p>设置指定名称RadioButton的Code和Value值</p>
   * @apiparam {string} sName 组名
   * @apiparam {string} sJson code和value的json
   * @apiparamExample {javascript} sJson
    *{
    *   "code": ["code1",
    *       "code2",
    *       "code3"],
    *   "value":["value1",
    *       "vaule2",
    *       "vualue3"]
    *}
   * @apiSuccessExample {number} Success:
   *  number
   * 1. 原来的选项值会被删除。
   * 2. 如果新增项值，使用addRadioButtonCodeAndValueByArray。
  */
  setRadioButtonCodeAndValueByArray(sName: string, sJson: string): Promise<number>;

  /**
   * @api {function} none getRadioButtonSelectItemValue
   * @apiName getRadioButtonSelectItemValue
   * @apiGroup 92_checkAPI
   * @apiVersion  1.0.0
   * @apiDescription getRadioButtonSelectItemValue(sName: string): string
   * <p>获取指定名称RadioButton当前选中项的Value值</p>
   * @apiparam {string} sName 结构化名称
   * @apiSuccessExample {string} Success:
   *  string
  */
  getRadioButtonSelectItemValue(sName: string): Promise<string>;

   /**
   * @api {function} none selectOneRadioButtonItemByValue
   * @apiName selectOneRadioButtonItemByValue
   * @apiGroup 92_checkAPI
   * @apiVersion  1.0.0
   * @apiDescription selectOneRadioButtonItemByValue(sName: string,sValue: string): number
   * <p>选中指定名称的RadioButton的指定Value项</p>
   * @apiparam {string} sName 结构化名称
   * @apiparam {string} sValue 选中的值
   * @apiSuccessExample {number} Success:
   *  number
  */
  selectOneRadioButtonItemByValue(sName: string, sValue: string): Promise<number>;

  /**
   * @api {function} none deleteAllRadioButtonItem
   * @apiName deleteAllRadioButtonItem
   * @apiGroup 92_checkAPI
   * @apiVersion  1.0.0
   * @apiDescription deleteAllRadioButtonItem(sName: string): number
   * <p>删除指定名称RadioButton的所有项</p>
   * @apiparam {string} sName 结构化名称
   * @apiSuccessExample {number} Success:
   *  number
  */
  deleteAllRadioButtonItem(sName: string): Promise<number>;

  /**
   * @api {function} none getRadioButtonValueWithArray
   * @apiName getRadioButtonValueWithArray
   * @apiGroup 92_checkAPI
   * @apiVersion  1.0.0
   * @apiDescription getRadioButtonValueWithArray(sName: string): string
   * <p>获取指定名称控件的value数组</p>
   * @apiparam {string} sName 结构化名称
   * @apiSuccessExample {json} Success:
   *  //json string    code:对应编辑器里面的显示名称，value:对应值
   * [
   * "1",
   * "2",
   * "3"
  *]
  */
  getRadioButtonValueWithArray(sName: string): Promise<string>;

  /**
   * @api {function} none getRadioButtonCodeWithArray
   * @apiName getRadioButtonCodeWithArray
   * @apiGroup 92_checkAPI
   * @apiVersion  1.0.0
   * @apiDescription getRadioButtonCodeWithArray(sName: string): string
   * <p>选中指定名称RadioButton的指定Code项</p>
   * @apiparam {string} sName 结构化名称
   * @apiSuccessExample {json} Success:
   *  //json string    code:对应编辑器里面的显示名称，value:对应值
   * [
   * "1",
   * "2",
   * "3"
  *]
  */
  getRadioButtonCodeWithArray(sName: string): Promise<string>;

  /**
   * @api {function} none selectRadioButtonItemByCode
   * @apiName selectRadioButtonItemByCode
   * @apiGroup 92_checkAPI
   * @apiVersion  1.0.0
   * @apiDescription selectRadioButtonItemByCode(sName: string,sCode: string): number
   * <p>选中指定名称RadioButton的指定Code项</p>
   * @apiparam {string} sName 结构化名称
   * @apiparam {string} sCode code值（对应界面的显示名)
   * @apiSuccessExample {number} Success:
   *  selectRadioButtonItemByValue('radioButton1','a') //选中radioButton1的a项
  */
  selectRadioButtonItemByCode(sName: string, sCode: string): Promise<number>;


  /**
   * @api {function} none selectRadioButtonItemByValue
   * @apiName selectRadioButtonItemByValue
   * @apiGroup 92_checkAPI
   * @apiVersion  1.0.0
   * @apiDescription selectRadioButtonItemByValue(sName: string,sValue: string): number
   * <p>选中指定名称RadioButton的指定Value项</p>
   * @apiparam {string} sName 结构化名称
   * @apiparam {string} sValue value值（对应界面的属性值)
   * @apiSuccessExample {number} Success:
   *  selectRadioButtonItemByValue('radioButton1','1') //选中radioButton1的属性值为1的项
  */
  selectRadioButtonItemByValue(sName: string, sValue: string): Promise<number>;

  /**
   * @api {function} none clearRadioButtonCheckItem
   * @apiName clearRadioButtonCheckItem
   * @apiGroup 92_checkAPI
   * @apiVersion  1.0.0
   * @apiDescription clearRadioButtonCheckItem(sName: string): number
   * <p>将指定名称RadioButton选项清空</p>
   * @apiparam {string} sName 结构化名称
   * @apiSuccessExample {number} Success:
   *  number
  */
  clearRadioButtonCheckItem(sName: string): number;

  /**
   * @api {function} none insertStructAtCurrentCursor
   * @apiName insertStructAtCurrentCursor
   * @apiGroup 91_structAPI
   * @apiVersion  1.0.0
   * @apiDescription insertStructAtCurrentCursor(sName: string, sText: string, nType: number): number
   * <p>在光标处插入结构化元素，支持文本框，节，数值框，多种下拉框。</p>
   * @apiparam {string} sName 结构化名称
   * @apiparam {string} sText 内容值
   * @apiparam {number} nType 类型
   * @apiparamExample {string} nType
   * {
    * //不支持插入签名元素，需要通过专门的插入签名元素接口插入
    * 1 -- Combox
    * 2 -- ListBox
    * 3 -- TextBox
    * 4 – CheckBox
    * 5 -- NumberBox
    * 6 -- MultiListBox
    * 7 -- MultiCombox
    * 8 -- DateTimeBox
    * 9 –RadioButton
    * 10 –MultiCheckbox
    * 11--- 预留
    * 12 – 预留
    * 13- 节（section）
    * 14-- 区域(Region)
    * 16 – 多选radiobox
    * 17 – 地址控件
    * 18 – 按钮
   * }
   * @apiSuccessExample {number} Success:
   *  number
  */
  insertStructAtCurrentCursor(sName: string, sText: string, nType: number): Promise<number>;

  /**
   * @api {function} none setStructText
   * @apiName setStructText
   * @apiGroup 91_structAPI
   * @apiVersion  1.0.0
   * @apiDescription setStructText(sName: string, sText: string): number
   * <p>设置指定结构化元素 的文本内容.某些特殊类型的结构化元素无法使用该接口赋值。(比如多选randiobox 多选checkbox)</p>
   * @apiparam {string} sName 结构化名称
   * @apiparam {string} sText 内容值 可选json格式
   * @apiparamExample {json} sText
   * {
   * //json格式可以支持上下标 也可以直接传递text文本
   * "normal":"xxxxx",
   * "superscript";"xxxx",
   * "subscript":"xxxx"
   * }
   * @apiSuccessExample {number} Success:
   *  number
  */
  setStructText(sName: string, sText: string): Promise<number>;

  markTextWithWave(options?: {content?: string, type?: number}): Promise<string> | Promise<null>;

  /**
   * @api {function} none setStructsBorderVisible
   * @apiName setStructsBorderVisible
   * @apiGroup 91_structAPI
   * @apiVersion  1.0.0
   * @apiDescription setStructsBorderVisible(bFlag: boolean): number
   * <p>统一设置结构化元素边框是否显示隐藏</p>
   * @apiparam {boolean} bFlag true：显示 false：隐藏
   * @apiSuccessExample {number} Success:
   *  number
  */
  setStructsBorderVisible(bFlag: boolean): Promise<number>;

  /**
   * @api {function} none resetStructsPlacehold
   * @apiName resetStructsPlacehold
   * @apiGroup 91_structAPI
   * @apiVersion  1.0.0
   * @apiDescription resetStructsPlacehold(onlyBank: boolean, sString: string): number
   * <p>将所有的元素跟节的占位符设置为指定字符串，可以只设置占位符为空的）</p>
   * @apiparam {boolean} onlyBank 是不是只有占位符是""才设置 true:是 false：不是
   * @apiparam {string} sString 指定字符串
   * @apiSuccessExample {number} Success:
   *  number
  */
  resetStructsPlacehold(onlyBank: boolean, sString: string): Promise<number>;

  /**
   * @api {function} none setStructTitle
   * @apiName setStructTitle
   * @apiGroup 91_structAPI
   * @apiVersion  1.0.0
   * @apiDescription setStructTitle(sName: string, sTitle: string): number
   * <p>设置文本框，节的标题</p>
   * @apiparam {string} sName 文本框或者节名称
   * @apiparam {string} sTitle 标题
   * @apiSuccessExample {number} Success:
   *  number
  */
  setStructTitle(sName: string, sTitle: string): Promise<number>;

  /**
   * @api {function} none getStructTitle
   * @apiName getStructTitle
   * @apiGroup 91_structAPI
   * @apiVersion  1.0.0
   * @apiDescription getStructTitle(sName: string): string
   * <p>获取文本框，节的标题</p>
   * @apiparam {string} sName 文本框或者节名称
   * @apiSuccessExample {string} Success:
   *  string
  */
  getStructTitle(sName: string): Promise<string>;

   /**
   * @api {function} none getStructText
   * @apiName getStructText
   * @apiGroup 91_structAPI
   * @apiVersion  1.0.0
   * @apiDescription getStructText(sName: string): string
   * <p>获取结构化元素的文本</p>
   * @apiparam {string} sName 结构化名称
   * @apiSuccessExample {string} Success:
   *  string
  */
  getStructText(sName: string): Promise<string>;

   /**
   * @api {function} none getStructBegin
   * @apiName getStructBegin
   * @apiGroup 91_structAPI
   * @apiVersion  1.0.0
   * @apiDescription getStructBegin(sName: string): string
   * <p>返回结构化元素的开始位置</p>
   * @apiparam {string} sName 结构化名称
   * @apiSuccessExample {string} Success:
   *  string
   *  返回位置值； 空值表示失败
  */
  getStructBegin(sName: string): Promise<string>;

  /**
   * @api {function} none getStructEnd
   * @apiName getStructEnd
   * @apiGroup 91_structAPI
   * @apiVersion  1.0.0
   * @apiDescription getStructEnd(sName: string): string
   * <p>返回结构化元素的结束位置</p>
   * @apiparam {string} sName 结构化名称
   * @apiSuccessExample {string} Success:
   *  string
   *  返回位置值； 空值表示失败
  */
  getStructEnd(sName: string): Promise<string>;

  /**
   * @api {function} none setStructProp
   * @apiName setStructProp
   * @apiGroup 91_structAPI
   * @apiVersion  1.0.0
   * @apiDescription setStructProp(sName: string, sPropName: string, sValue: any): number
   * <p>设置结构化元素的属性</p>
   * @apiparam {string} sName 结构化名称
   * @apiparam {string} sPropName 属性名
   * @apiparam {any} sValue 属性值
   * @apiSuccessExample {number} Success:
   *  number
   * 各种元素的结构化属性名参考附录。
  */
  setStructProp(sName: string, sPropName: string, sValue: any): Promise<number>;

  /**
   * @api {function} none setTextBoxMaxLen
   * @apiName setTextBoxMaxLen
   * @apiGroup 93_textboxAPI
   * @apiVersion  1.0.0
   * @apiDescription setTextBoxMaxLen(sName: string, nMaxLen: number): number
   * <p>设置文本框可输入的最大长度</p>
   * @apiparam {string} sName 结构化名称
   * @apiparam {string} nMaxLen 最大长度,>0的整数 ;等于0表示取消长度限制
   * @apiSuccessExample {number} Success:
   *  number
  */
  setTextBoxMaxLen(sName: string, nMaxLen: number): Promise<number>;

  /**
   * @api {function} none getTextBoxMaxLen
   * @apiName getTextBoxMaxLen
   * @apiGroup 93_textboxAPI
   * @apiVersion  1.0.0
   * @apiDescription getTextBoxMaxLen(sName: string): number
   * <p>获取文本框可输入的最大长度</p>
   * @apiparam {string} sName 结构化名称
   * @apiSuccessExample {number} Success:
   *  number
   * =0 表示不限制  NaN 表示失败
  */
  getTextBoxMaxLen(sName: string): Promise<number>;

  /**
   * @api {function} none setStructProp
   * @apiName setStructProp
   * @apiGroup 91_structAPI
   * @apiVersion  1.0.0
   * @apiDescription setStructsTextByArray(sJson: string): number
   * <p>批量对多个结构化元素的内容赋值。</p>
   * @apiparam {string} sJson 多个结构化元素的内容Json
   * @apiparamExample {json} sJson
   * {
      * "textbox1": {
      *    "content_text": "第一个文本元素"
      *},
      *"textbox2": {
      *    "content_text": "第二个文本元素"
      *}
   * }
   * @apiSuccessExample {number} Success:
   *  number
  */
  setStructsTextByArray(sJson: string): Promise<number>;

  /**
   * @api {function} none getDateTimeBoxValueEx
   * @apiName getDateTimeBoxValueEx
   * @apiGroup 94_dateAPI
   * @apiVersion  1.0.0
   * @apiDescription getDateTimeBoxValueEx(sName: string): string
   * <p>获取指定名称日期框的值</p>
   * @apiparam {string} name 日期框名称
   * @apiparam {string} sRev 扩展参数"AllDate" 假如年份月日格式里面不存在的话，也会返回
   * @apiSuccessExample {string} Success:
   *  string
  */
  getDateTimeBoxValueEx(name: string, sRev?: string): Promise<string>;

  /**
   * @api {function} none setDateTimeBoxValue
   * @apiName setDateTimeBoxValue
   * @apiGroup 94_dateAPI
   * @apiVersion  1.0.0
   * @apiDescription setDateTimeBoxValue(sName: string,sValue: string): number
   * <p>设置指定名称日期框的值</p>
   * @apiparam {string} name 日期框名称
   * @apiparam {string} sValue 日期框的值 必须遵循如下格式:Date=2010-12-02 Time=12:23
   * @apiSuccessExample {number} Success:
   *  number
  */
  setDateTimeBoxValue(sName: string, sValue: string): Promise<number>;

  /**
   * @api {function} none getDateTimeFormat
   * @apiName getDateTimeFormat
   * @apiGroup 94_dateAPI
   * @apiVersion  1.0.0
   * @apiDescription getDateTimeFormat(sName: string): string
   * <p>获取指定名称日期框的默认显示格式</p>
   * @apiparam {string} name 日期框名称
   * @apiSuccessExample {string} Success:
   *  当为固定格式时，返回如下格式：
   *  1.   yyyy-MM-dd
   *  2.	 yyyy-MM-dd	HH：mm：ss
   *  3.	 yyyy-MM-dd	HH：mm
    * 4.   HH：mm：ss
    * 当自定义日期格式的时候，将返回json的string
    *{
    *    "Year":"yyyy",
    *    "YearSeparat":"-",
    *    "Month":"MM",
    *    "MonthSeparat":"-",
    *    "Day":"dd",
    *    "DaySeparat":" ",
    *    "Hour":"HH",
    *    "HourSeparat":":",
    *    "Minute":"mm",
    *    "MinuteSeparat":":",
    *    "Second":"ss",
    *    "SecondSeparat":".",
    *    "Millisecond":"fff",
    *    "MillisecondSeparat":""
    *}
  */
  getDateTimeFormat(sName: string): Promise<string>;

  /**
   * @api {function} none setDateTimeFormat
   * @apiName setDateTimeFormat
   * @apiGroup 94_dateAPI
   * @apiVersion  1.0.0
   * @apiDescription setDateTimeFormat(sName: string, nType: number, sFormat?: string): number
   * <p>设置指定指定名称日期框的默认显示格式</p>
   * @apiparam {string} sName 日期框名称
   * @apiparam {number} nType 格式类型
   * @apiparam {string} sFormat 自定义格式json；
   * @apiparamExample {number} nType
   * {
   * 1  yyyy-MM-dd
    *2	yyyy-MM-dd	HH：mm：ss
    *3	yyyy-MM-dd	HH：mm
    *4  HH：mm：ss
    *0  自定义
   * }
   * @apiparamExample {json} sFormat
   * {
   * //nType = 0 时
    *    "Year":"yyyy",
    *    "YearSeparat":"-",
    *    "Month":"MM",
    *    "MonthSeparat":"-",
    *    "Day":"dd",
    *    "DaySeparat":" ",
    *    "Hour":"HH",
    *    "HourSeparat":":",
    *    "Minute":"mm",
    *    "MinuteSeparat":":",
    *    "Second":"ss",
    *    "SecondSeparat":".",
    *    "Millisecond":"fff",
    *    "MillisecondSeparat":""
   * }
   * @apiSuccessExample {number} Success:
   *  number
  */
  setDateTimeFormat(sName: string, nType: number, sFormat?: string): Promise<number>;

   /**
   * @api {function} none setStructsPropByArray
   * @apiName setStructsPropByArray
   * @apiGroup 91_structAPI
   * @apiVersion  1.0.0
   * @apiDescription setStructsPropByArray(sJson: string): number
   * <p>批量对多个结构化元素的属性赋值。</p>
   * @apiparam {string} sJson 多个结构化元素Json
   * @apiparamExample {json} sJson
   *{
   * "textbox1": {
   *     "property": {
   *         "Title": "标题",
   *         "Placeholder": "占位符",
   *         "IsMustInput": true,
   *         "DeleteProtect": true,
   *         "EditProtect": false,
   *         "ShowBorder": true,
   *         "HiddenBackground": false,
   *         "ReverseEdit": false,
   *         "DisplayType": true,
   *         "Hidden": false,
   *         "code": [
   *             "code1",
   *             "code2",
   *             "code3"
   *         ],
   *         "value": [
   *             "value1",
   *             "vaule2",
   *             "vualue3"
   *         ],
   *         "CustomProperty": {
   *             "自定义属性1": "11111",
   *             "自定义属性2": "22222"
   *         }
   *     }
  * },
  *  "textbox2": {
  *      "property": {
  *          "Placeholder": "占位符",
  *          "IsMustInput": true,
  *          "DeleteProtect": true,
  *          "EditProtect": false,
  *          "ShowBorder": true,
  *          "HiddenBackground": false,
  *          "ReverseEdit": false,
  *          "DisplayType": false,
  *          "Hidden": false,
  *          "CustomProperty": {
  *              "自定义属性1": true,
  *              "自定义属性2": "22222"
  *          }
  *      }
  *  }
  *}
   * @apiSuccessExample {number} Success:
   *  number
  */
  setStructsPropByArray(sJson: string): Promise<number>;

   /**
   * @api {function} none setNumboxText
   * @apiName setNumboxText
   * @apiGroup 95_numberAPI
   * @apiVersion  1.0.0
   * @apiDescription setNumboxText(sName: string, nText: number): number
   * <p>设置Numbox的文本</p>
   * @apiparam {string} sName 数字框名称
   * @apiparam {number} nText 数字框的值
   * @apiSuccessExample {number} Success:
   *  number
  */
  setNumboxText(sName: string, nText: number): Promise<number>;

  /**
   * @api {function} none getNumboxText
   * @apiName getNumboxText
   * @apiGroup 95_numberAPI
   * @apiVersion  1.0.0
   * @apiDescription getNumboxText(name: string): number
   * <p>获取Numbox的文本</p>
   * @apiparam {string} name 数字框名称
   * @apiSuccessExample {number} Success:
   *  number
  */
  getNumboxText(name: string): Promise<number>;

  /**
   * @api {function} none setNumboxMaxValue
   * @apiName setNumboxMaxValue
   * @apiGroup 95_numberAPI
   * @apiVersion  1.0.0
   * @apiDescription setNumboxMaxValue(name: string,maxValue: number): number
   * <p>设置Numbox的取值上限</p>
   * @apiparam {string} name 数字框名称
   * @apiparam {number} maxValue 最大值
   * @apiSuccessExample {number} Success:
   *  number
  */
  setNumboxMaxValue(name: string, maxValue: number): Promise<number>;

  /**
   * @api {function} none getNumboxMaxValue
   * @apiName getNumboxMaxValue
   * @apiGroup 95_numberAPI
   * @apiVersion  1.0.0
   * @apiDescription getNumboxMaxValue(name: string): number
   * <p>获取Numbox的取值上限</p>
   * @apiparam {string} name 数字框名称
   * @apiSuccessExample {number} Success:
   *  number
  */
  getNumboxMaxValue(name: string): Promise<number>;

  /**
   * @api {function} none setNumboxMinValue
   * @apiName setNumboxMinValue
   * @apiGroup 95_numberAPI
   * @apiVersion  1.0.0
   * @apiDescription setNumboxMinValue(name: string,minValue: number): number
   * <p>设置Numbox的取值下限</p>
   * @apiparam {string} name 数字框名称
   * @apiparam {number} minValue 最小值
   * @apiSuccessExample {number} Success:
   *  number
  */
  setNumboxMinValue(name: string, minValue: number): Promise<number>;

  /**
   * @api {function} none getNumboxMinValue
   * @apiName getNumboxMinValue
   * @apiGroup 95_numberAPI
   * @apiVersion  1.0.0
   * @apiDescription getNumboxMinValue(name: string): number
   * <p>获取Numbox的取值下限</p>
   * @apiparam {string} name 数字框名称
   * @apiSuccessExample {number} Success:
   *  number
  */
  getNumboxMinValue(name: string): Promise<number>;

  /**
   * @api {function} none setNumboxPrecision
   * @apiName setNumboxPrecision
   * @apiGroup 95_numberAPI
   * @apiVersion  1.0.0
   * @apiDescription setNumboxPrecision(name: string,precision: number): number
   * <p>设置Numbox的精度</p>
   * @apiparam {string} name 数字框名称
   * @apiparam {number} precision 精度值
   * @apiSuccessExample {number} Success:
   *  number
  */
  setNumboxPrecision(name: string, precision: number): Promise<number>;

  /**
   * @api {function} none getNumboxPrecision
   * @apiName getNumboxPrecision
   * @apiGroup 95_numberAPI
   * @apiVersion  1.0.0
   * @apiDescription getNumboxPrecision(name: string): number
   * <p>获取Numbox的精度</p>
   * @apiparam {string} name 数字框名称
   * @apiSuccessExample {number} Success:
   *  number
  */
  getNumboxPrecision(name: string): Promise<number>;

  /**
   * @api {function} none setNumboxErrorInputInfo
   * @apiName setNumboxErrorInputInfo
   * @apiGroup 95_numberAPI
   * @apiVersion  1.0.0
   * @apiDescription setNumboxErrorInputInfo(strName: string,strInfo: string): number
   * <p>设置数字框输入错误后的警告信息，比如输入英文字母</p>
   * @apiparam {string} strName 数字框名称
   * @apiparam {string} strInfo 警告信息
   * @apiSuccessExample {number} Success:
   *  number
  */
  setNumboxErrorInputInfo(strName: string, strInfo: string): Promise<number>;

  /**
   * @api {function} none setNumboxOutRangeInfo
   * @apiName setNumboxOutRangeInfo
   * @apiGroup 95_numberAPI
   * @apiVersion  1.0.0
   * @apiDescription setNumboxOutRangeInfo(strName: string,strInfo: string): number
   * <p>设置数字框输入不在最小值到最大值范围的警告信息</p>
   * @apiparam {string} strName 数字框名称
   * @apiparam {string} strInfo 警告信息
   * @apiSuccessExample {number} Success:
   *  number
  */
  setNumboxOutRangeInfo(strName: string, strInfo: string): Promise<number>;

  /**
   * @api {function} none getCompoundBoxCurrentCode
   * @apiName getCompoundBoxCurrentCode
   * @apiGroup 96_droplistAPI
   * @apiVersion  1.0.0
   * @apiDescription getCompoundBoxCurrentCode(sName: string,nType?: number): string
   * <p>获取指定下拉框的当前Code值</p>
   * @apiparam {string} sName 结构化名称
   * @apiparam {number} nType 预留值，可不填写
   * @apiSuccessExample {string} Success:
   *  string
  */
  getCompoundBoxCurrentCode(sName: string, nType?: number): Promise<string>;

  /**
   * @api {function} none getCompoundBoxCurrentValue
   * @apiName getCompoundBoxCurrentValue
   * @apiGroup 96_droplistAPI
   * @apiVersion  1.0.0
   * @apiDescription getCompoundBoxCurrentValue(sName: string,nType?: number): string
   * <p>获取指定下拉框的当前value值</p>
   * @apiparam {string} sName 结构化名称
   * @apiparam {number} nType 预留值，可不填写
   * @apiSuccessExample {string} Success:
   *  string
  */
  getCompoundBoxCurrentValue(sName: string, nType?: number): Promise<string>;

  /**
   * @api {function} none getCompoundBoxCodeWithArray
   * @apiName getCompoundBoxCodeWithArray
   * @apiGroup 96_droplistAPI
   * @apiVersion  1.0.0
   * @apiDescription getCompoundBoxCodeWithArray(sName: string,nType?: number): string[]
   * <p>获取指定名称控件的code数组</p>
   * @apiparam {string} sName 结构化名称
   * @apiparam {number} nType 预留值，可不填写
   * @apiSuccessExample {string} Success:
   *  string[]
  */
  getCompoundBoxCodeWithArray(sName: string, nType?: number): Promise<string[]>;

  /**
   * @api {function} none getCompoundBoxValueWithArray
   * @apiName getCompoundBoxValueWithArray
   * @apiGroup 96_droplistAPI
   * @apiVersion  1.0.0
   * @apiDescription getCompoundBoxValueWithArray(sName: string,nType?: number): string[]
   * <p>获取指定名称控件的value数组</p>
   * @apiparam {string} sName 结构化名称
   * @apiparam {number} nType 预留值，可不填写
   * @apiSuccessExample {string} Success:
   *  string[]
  */
  getCompoundBoxValueWithArray(sName: string, nType?: number): Promise<string[]>;

  /**
   * @api {function} none getCompoundBoxValueByCode
   * @apiName getCompoundBoxValueByCode
   * @apiGroup 96_droplistAPI
   * @apiVersion  1.0.0
   * @apiDescription getCompoundBoxValueByCode(sName: string,sCode: string,nType?: number): string
   * <p>获取指定名称控件的Code对应的Value值</p>
   * @apiparam {string} sName 结构化名称
   * @apiparam {string} sCode 控件列表的Code值
   * @apiparam {number} nType 预留值，可不填写
   * @apiSuccessExample {string} Success:
   *  string
  */
  getCompoundBoxValueByCode(sName: string, sCode: string, nType?: number): Promise<string>;

  /**
   * @api {function} none setCompoundBoxCodeAndValue
   * @apiName setCompoundBoxCodeAndValue
   * @apiGroup 96_droplistAPI
   * @apiVersion  1.0.0
   * @apiDescription setCompoundBoxCodeAndValue(sName: string,sCode: string,sValue: string,nType?: number): number
   * <p>设置指定名称控件的Code和Value值；当已存在code值是，则修改value，当不存在时，则新增</p>
   * @apiparam {string} sName 结构化名称
   * @apiparam {string} sCode 控件列表的Code值
   * @apiparam {string} sValue 控件列表的Value值
   * @apiparam {number} nType 预留值，可不填写
   * @apiSuccessExample {number} Success:
   *  number
  */
  setCompoundBoxCodeAndValue(sName: string, sCode: string, sValue: string, nType?: number): Promise<number>;

  /**
   * @api {function} none setMultiDropdownControlSeparator
   * @apiName setMultiDropdownControlSeparator
   * @apiGroup 96_droplistAPI
   * @apiVersion  1.0.0
   * @apiDescription setMultiDropdownControlSeparator(sName: string,sSeparator: string): number
   * <p>设置指定多选下拉控件的分隔符</p>
   * @apiparam {string} sName 结构化名称
   * @apiparam {string} sCode 分隔符的信息
   * @apiSuccessExample {number} Success:
   *  number
  */
  setMultiDropdownControlSeparator(sName: string, sSeparator: string): Promise<number>;

  /**
   * @api {function} none setMultiDropdownControlGroupSeparator
   * @apiName setMultiDropdownControlGroupSeparator
   * @apiGroup 96_droplistAPI
   * @apiVersion  1.0.0
   * @apiDescription setMultiDropdownControlGroupSeparator(sName: string,sSeparator: string): number
   * <p>设置指定多选下拉控件的选中项及未选中项之间的分隔符</p>
   * @apiparam {string} sName 结构化名称
   * @apiparam {string} sCode 分隔符的信息
   * @apiSuccessExample {number} Success:
   *  number
  */
  setMultiDropdownControlGroupSeparator(sName: string, sSeparator: string): Promise<number>;

  /**
   * @api {function} none setCompoundBoxDropMode
   * @apiName setCompoundBoxDropMode
   * @apiGroup 96_droplistAPI
   * @apiVersion  1.0.0
   * @apiDescription setCompoundBoxDropMode(bEnable: boolean): number
   * <p>设置下拉框控件的下拉窗体弹出模式(单击弹出还是双击弹出)</p>
   * @apiparam {boolean} bEnable true:双击弹出 false:单击弹出
   * @apiSuccessExample {number} Success:
   *  number
  */
  setCompoundBoxDropMode(bEnable: boolean): Promise<number>;

  /**
   * @api {function} none getStructProp
   * @apiName getStructProp
   * @apiGroup 91_structAPI
   * @apiVersion  1.0.0
   * @apiDescription getStructProp(sName: string, sPropName: string): string|boolean
   * <p>获取指定name的Struct的指定属性</p>
   * @apiparam {string} sName 结构化名称
   * @apiparam {string} sPropName 属性名
   * @apiSuccessExample {string} Success:
   *  string|boolean
  */
  getStructProp(sName: string, sPropName: string): Promise<string | boolean>;

  /**
   * @api {function} none deleteStruct
   * @apiName deleteStruct
   * @apiGroup 91_structAPI
   * @apiVersion  1.0.0
   * @apiDescription deleteStruct(sName: string): number
   * <p>删除一个结构化元素</p>
   * @apiparam {string} sName 结构化名称
   * @apiSuccessExample {number} Success:
   *  number
  */
  deleteStruct(sName: string): Promise<number>;

  /**
   * @api {function} none setStructName
   * @apiName setStructName
   * @apiGroup 91_structAPI
   * @apiVersion  1.0.0
   * @apiDescription setStructName(sName: string,sNewName: string): number
   * <p>重新命名指定 结构化元素的name</p>
   * @apiparam {string} sName 结构化名称
   * @apiparam {string} sNewName 新的名称
   * @apiSuccessExample {number} Success:
   *  number
  */
  setStructName(sName: string, sNewName: string): Promise<number>;

  /**
   * @api {function} none cursorJumpOutOfOneStruct
   * @apiName cursorJumpOutOfOneStruct
   * @apiGroup 91_structAPI
   * @apiVersion  1.0.0
   * @apiDescription cursorJumpOutOfOneStruct(sName: string,nMark: number): number
   * <p>光标跳转到某一个结构的边框前或者后</p>
   * @apiparam {string} sName 结构化名称
   * @apiparam {number} nMark 1 – 后边框的后端 0 – 前边框的前端 2 – 后边框的前端 3 – 前边框的后端
   * @apiSuccessExample {number} Success:
   *  number
  */
  cursorJumpOutOfOneStruct(sName: string, nMark: number): Promise<number>;

  /**
   * @api {function} none selectOneStructContent
   * @apiName selectOneStructContent
   * @apiGroup 91_structAPI
   * @apiVersion  1.0.0
   * @apiDescription selectOneStructContent(sName: string): number
   * <p>光标选中某一个结构的所有内容,但是不包括边框(无论边框是否隐藏)</p>
   * @apiparam {string} sName 结构化名称
   * @apiSuccessExample {number} Success:
   *  number
  */
  selectOneStructContent(sName: string): Promise<number>;

  /**
   * @api {function} none selectOneStruct
   * @apiName selectOneStruct
   * @apiGroup 91_structAPI
   * @apiVersion  1.0.0
   * @apiDescription selectOneStruct(sName: string): number
   * <p>光标选中某一个结构，包括边框</p>
   * @apiparam {string} sName 结构化名称
   * @apiSuccessExample {number} Success:
   *  number
  */
  selectOneStruct(sName: string): Promise<number>;

  /**
   * @api {function} none getStructsXmlInfoByParament
   * @apiName getStructsXmlInfoByParament
   * @apiGroup 91_structAPI
   * @apiVersion  1.0.0
   * @apiDescription getStructsXmlInfoByParament(): string
   * <p>以Json格式依次返回文档的结构化信息,不包含嵌套信息。</p>
   * @apiDeprecated 请使用getStructsXmlInfoByParament2.
   * @apiSuccessExample {json} Success:
   *  json
  */
  getStructsXmlInfoByParament(): Promise<string>;

  /**
   * @api {function} none getStructsXmlInfoByParament2
   * @apiName getStructsXmlInfoByParament2
   * @apiGroup 91_structAPI
   * @apiVersion  1.0.0
   * @apiDescription getStructsXmlInfoByParament2(sJson: string): string
   * <p>根据配置参数，以Json格式依次返回文档的结构化信息</p>
   * @apiparam {string} sJson 配置参数 json格式
   * @apiparamExample {json} sJson
   * {
    * "needNest" :1, //是否生成嵌套关系 默认为0
    * "needTable" :1 //是否包含表格信息 默认为0
    * "needCascade" :1 //是否导出级联信息 默认为0 级联属性名：LogicEvent
    * }
    * @apiExample {json} Example
    * {
      * // 下面这段代码使用了迭代机制来从返回的json中查找id为'主诉'的text和这个元素的name
      * function findTextByIdIterative(obj, id) {
      * const stack = [{ obj, key: null }];
      * let text = null;
      * let parent = null;
      * while (stack.length > 0) {
      *   const { obj: currentObj, key: currentKey } = stack.pop();
      *   for (const key in currentObj) {
      *     const value = currentObj[key];
      *     if (key === id && typeof value === 'object') {
      *       text = value.text;
      *       parent = currentKey;
      *       break;
      *     }
      *     if (typeof value === 'object') {
      *       stack.push({ obj: value, key: key });
      *     }
      *   }
      *
      *   if (text !== null) break;
        *}
        *return { text, parent };
      *}
      * // 调用函数并打印结果
      *const resultIterative = findTextByIdIterative(json, '主诉');
      * console.log("text:", resultIterative.text);
      * console.log("parent:", resultIterative.parent);
    * }
   * @apiSuccessExample {json} Success:
   *  {
   * //返回json串
   * //这个json包含了编辑器核心的结构化数据
   * //json中可以抽取需要的业务数据
   * //也可以作为数据交换格式。
   * "textbox01": { //这是元素的name
   *     "type": 1,
   *     "id": '主诉', //定义了元素的id
   *     "text": "主诉内容",//这是元素的内容
   *     "location":1,//1 – 正文 2 – 页眉  3- 页脚
   *     "CustomPrperty": {
   *         "aaa": "bbb",
   *         "ccc": "dddd"
   *     },
   *     "children": [
   *         {
   *             "textbox02": {
   *                 "type": 2,
   *                 "id": '现病史',
   *                 "text": "现病史内容",
   *                 "CustomPrperty": {
   *                     "aaa": "bbb",
   *                     "ccc": "dddd"
   *                 },
   *                 "xxxx3": {
   *                     "type": 1,
   *                     "xxx属性": true,
   *                     "text": "xxxx",
   *                     "CustomPrperty": {
   *                         "aaa": "bbb",
   *                         "ccc": "dddd"
   *                     }
   *                 }
   *             }
   *         }
   *     ]
   * },
   * "tablexxxx2": {
   *     "CustomPrperty": {
   *         "aaa": "bbb",
   *         "ccc": "dddd"
   *     },
   *     "children": [
   *         {
   *             "xxx5": {
   *                 "type": 4,
   *                 "xxx属性": true,
   *                 "text": "xxxx",
   *                 "CustomPrperty": {
   *                     "aaa": "bbb",
   *                     "ccc": "dddd"
   *                 }
   *             }
   *         }
   *     ]
   * }
  *}
*/
  getStructsXmlInfoByParament2(sJson: string): Promise<string>;

    /**
   * @api {function} none getStructsXmlInfoByFile
   * @apiName getStructsXmlInfoByFile
   * @apiGroup 91_structAPI
   * @apiVersion  1.0.0
   * @apiDescription getStructsXmlInfoByFile(content: Blob): string
   * <p>不打开文档依次返回文档的结构化和区域json信息</p>
   * @apiDeprecated getStructsXmlInfoByFile2.
   * @apiparam {blob} content 文档blob流数据
   * @apiSuccessExample {json} Success:
   *  json
  */
  getStructsXmlInfoByFile(content: Blob): Promise<string>;

  /**
   * @api {function} none getStructsXmlInfoByFile2
   * @apiName getStructsXmlInfoByFile2
   * @apiGroup 91_structAPI
   * @apiVersion  1.0.0
   * @apiDescription getStructsXmlInfoByFile2(content: Blob,sJson: string): string
   * <p>不打开文档依次返回文档的结构化和区域信息</p>
   * @apiparam {blob} content 文档blob流数据
   * @apiparam {string} sJson json格式控制参数 参考getStructsXmlInfoByParament2
   * @apiSuccessExample {json} Success:
   *  json 参考参考getStructsXmlInfoByParament2
  */
  getStructsXmlInfoByFile2(content: Blob, sJson: string): Promise<string>;

  /**
   * @api {function} none filterStructsByProp
   * @apiName filterStructsByProp
   * @apiGroup 91_structAPI
   * @apiVersion  1.0.0
   * @apiDescription filterStructsByProp(sJson: string,sRev1?: string): string
   * <p>按照某个属性名（可以同时带属性值）进行筛选，筛选出符合条件的结构名</p>
   * @apiparam {string} sJson json格式参数
   * @apiparam {string} sRev1 预留参数 可以不填
   * @apiparamExample {json} sValue
   * 指定属性名 指定属性值
   *{
   *   "controlName": "病人姓名"
   *   };
   * 指定属性名 任意属性值
   * {
   *     "oneProp": null
   * }
   * @apiSuccessExample {string} Success:
   *  string
   *1. 当不启用sRev的时候，, 返回为筛选结构的name字符串，以,隔开。
   *2. 当启用sRev的时候，返回为符合条件的name字符串的json串值，会带回属性的值。比如筛选存在recoderTime属性的元素，返回json
   *3. 此接口查询性能不如直接从文档的json中搜索快,适合少量的筛选。
   */
  filterStructsByProp(sJson: string, sRev1?: string): Promise<string>;

  /**
   * @api {function} none getIncompletedCtrlNameList
   * @apiName getIncompletedCtrlNameList
   * @apiGroup 8_editAPI
   * @apiVersion  1.0.0
   * @apiDescription getIncompletedCtrlNameList(): string
   * <p>获取当前文档所有必填项未填的元素名称列表</p>
   * @apiSuccessExample {string} Success:
   *  string 以,隔开
   */
  getIncompletedCtrlNameList(): Promise<string>;

   /**
   * @api {function} none getErrorCtrlNameList
   * @apiName getErrorCtrlNameList
   * @apiGroup 8_editAPI
   * @apiVersion  1.0.0
   * @apiDescription getErrorCtrlNameList(): string
   * <p>获取当前文档所有包含错误的元素详细信息</p>
   * @apiSuccessExample {json} Success:
   *  json
   * [
   * {name:'textedit1',type:1},
   * {name:'numberbox2',type:2},
   * ]
   * type 1 表示正则校准失败 可能包含违禁词
   * type 2 表示数值框非法 可能超过最大最小值 或者不是数字
   * type 3 表示文本框内容超过最大字符数
   * type 4 表示日期框日期非法
   */
   getErrorCtrlNameList(): Promise<string>;

  /**
   * @api {function} none saveStructContentToString
   * @apiName saveStructContentToString
   * @apiGroup 1_documentAPI
   * @apiVersion  1.0.0
   * @apiDescription saveStructContentToString(sName: string): string
   * <p>保存指定结构为字符串数据。(base64编码)</p>
   * @apiparam {string} sName 结构化名称
   * @apiSuccessExample {string} Success:
   *  string base64编码
   */
  saveStructContentToString(sName: string): Promise<string>;

  /**
   * @api {function} none setCompoundBoxCodeAndValueByArray
   * @apiName setCompoundBoxCodeAndValueByArray
   * @apiGroup 96_droplistAPI
   * @apiVersion  1.0.0
   * @apiDescription setCompoundBoxCodeAndValueByArray(sName: string, sValue: string, nType?: number): number
   * <p>设置指定名称控件的Code和Value值（传数组，一次设多条code，value</p>
   * @apiparam {string} sName 结构化名称
   * @apiparam {string} sValue 设置的值 json
   * @apiparam {number} nType 结构化类型 可不传递
   * @apiparamExample {json} sValue
   * {
   * "code": [ "code1",
   *     "code2",
   *     "code3"],
   * "value":[  "value1",
   *     "vaule2",
   *     "vualue3"]
   *}
   * @apiSuccessExample {number} Success:
   *  number
   */
  setCompoundBoxCodeAndValueByArray(sName: string, sValue: string, nType?: number): Promise<number>;

  /**
   * @api {function} none setCompoundBoxCurrentCodeByValue
   * @apiName setCompoundBoxCurrentCodeByValue
   * @apiGroup 96_droplistAPI
   * @apiVersion  1.0.0
   * @apiDescription setCompoundBoxCurrentCodeByValue(sName: string, value: string, nType?: number): number
   * <p>根据指定下拉框的value值设置当前显示的code值</p>
   * @apiparam {string} sName 结构化名称
   * @apiparam {string} value 设置的值
   * @apiparam {number} nType 结构化类型 可不传递
   * @apiSuccessExample {number} Success:
   *  number
   */
  setCompoundBoxCurrentCodeByValue(sName: string, value: string, nType?: number): Promise<number>;

   /**
   * @api {function} none deleteAllCompoundBoxCodeAndValue
   * @apiName deleteAllCompoundBoxCodeAndValue
   * @apiGroup 96_droplistAPI
   * @apiVersion  1.0.0
   * @apiDescription deleteAllCompoundBoxCodeAndValue(sName: string, nType?: number): number
   * <p>删除指定名称控件的所有条目</p>
   * @apiparam {string} sName 结构化名称
   * @apiparam {number} nType 结构化类型 可不传递
   * @apiSuccessExample {number} Success:
   *  number
   */
  deleteAllCompoundBoxCodeAndValue(sName: string, nType?: number): Promise<number>;

  /**
   * @api {function} none hasHeader
   * @apiName hasHeader
   * @apiGroup 4_headerfooterAPI
   * @apiVersion  1.0.0
   * @apiDescription hasHeader(): boolean
   * <p>判断当前文档是否含有页眉</p>
   * @apiSuccessExample {boolean} Success:
   *  boolean
   */
  hasHeader(): Promise<boolean>;

  /**
   * @api {function} none deleteHeader
   * @apiName deleteHeader
   * @apiGroup 4_headerfooterAPI
   * @apiVersion  1.0.0
   * @apiDescription deleteHeader(): boolean
   * <p>删除当前文档的页眉</p>
   * @apiSuccessExample {number} Success:
   *  number
   */
  deleteHeader(): Promise<number>;

  /**
   * @api {function} none deleteHeaderContent
   * @apiName deleteHeaderContent
   * @apiGroup 4_headerfooterAPI
   * @apiVersion  1.0.0
   * @apiDescription deleteHeaderContent(sRev?: string): number
   * <p>删除当前文档的页眉内容</p>
   * @apiparam {string} sRev 预留参数 可不传递
   * @apiSuccessExample {number} Success:
   *  number
   */
  deleteHeaderContent(sRev?: string): Promise<number>;

  /**
   * @api {function} none deleteFooterContent
   * @apiName deleteFooterContent
   * @apiGroup 4_headerfooterAPI
   * @apiVersion  1.0.0
   * @apiDescription deleteFooterContent(sRev?: string): number
   * <p>删除当前文档的页脚内容</p>
   * @apiparam {string} sRev 预留参数 可不传递
   * @apiSuccessExample {number} Success:
   *  number
   */
  deleteFooterContent(sRev?: string): Promise<number>;

  /**
   * @api {function} none hasFooter
   * @apiName hasFooter
   * @apiGroup 4_headerfooterAPI
   * @apiVersion  1.0.0
   * @apiDescription hasFooter(): boolean
   * <p>判断当前文档是否含有页脚</p>
   * @apiSuccessExample {boolean} Success:
   *  boolean
   */
  hasFooter(): Promise<boolean>;

   /**
   * @api {function} none deleteFooter
   * @apiName deleteFooter
   * @apiGroup 4_headerfooterAPI
   * @apiVersion  1.0.0
   * @apiDescription deleteFooter(): boolean
   * <p>删除当前文档的页脚</p>
   * @apiSuccessExample {number} Success:
   *  number
   */
  deleteFooter(): Promise<number>;

  /**
   * @api {function} none setFooterTextEx
   * @apiName setFooterTextEx
   * @apiGroup 4_headerfooterAPI
   * @apiVersion  1.0.0
   * @apiDescription setFooterTextEx(strText: string, nParaStyle: number, sRev1?: string): number>
   * <p>设置页脚的文本内容，如果没有页脚不会自动插入页脚</p>
   * @apiparam {string} strText 页脚内容 支持通配符$ 用来代表页码
   * @apiparam {string} nParaStyle 页脚内容的对齐方式 1:左对齐 2:居中 3:右对齐 4:两端对齐
   * @apiparam {string} sRev1 预留参数 可以不传
   * @apiExample {javascript} Example
   * {
   * editor.setFooterTextEx("第$页",2)
   * }
   * @apiSuccessExample {number} Success:
   *  number
   */
  setFooterTextEx(strText: string, nParaStyle: number, sRev1?: string): Promise<number>;

  /**
   * @api {function} none jumpToHeader
   * @apiName jumpToHeader
   * @apiGroup 4_headerfooterAPI
   * @apiVersion  1.0.0
   * @apiDescription jumpToHeader(): number
   * <p>光标跳到当前页的页眉开始位置。</p>
   * @apiSuccessExample {number} Success:
   *  number
   */
  jumpToHeader(): Promise<number>;

  /**
   * @api {function} none getHeaderText
   * @apiName getHeaderText
   * @apiGroup 4_headerfooterAPI
   * @apiVersion  1.0.0
   * @apiDescription getHeaderText(): string
   * <p>获取当前页的页眉文本内容。</p>
   * @apiSuccessExample {string} Success:
   *  string
   */
  getHeaderText(): Promise<string>;
  /**
   * @api {function} none setHeaderFooterReadOnly
   * @apiName setHeaderFooterReadOnly
   * @apiGroup 4_headerfooterAPI
   * @apiVersion  1.0.0
   * @apiDescription setHeaderFooterReadOnly(bReadOnly: boolean): number
   * <p>设置页眉页脚是否只读</p>
   * @apiparam {boolean} bReadOnly 是否只读 true:只读 false：可编辑
   * @apiSuccessExample {number} Success:
   *  number
   */
  setHeaderFooterReadOnly(bReadOnly: boolean): Promise<number>;

  /**
   * @api {function} none setCommentAuthor
   * @apiName setCommentAuthor
   * @apiGroup 85_revisionAPI
   * @apiVersion  1.0.0
   * @apiDescription setCommentAuthor(sName: string): number
   * <p>设置批注用户名</p>
   * @apiparam {string} sName 用户名
   * @apiSuccessExample {javascript} Success:
   *  let nresult = editor.setCommentAuthor("张医生");//设置完毕后，插入的批注作者就是张医生
   */
  setCommentAuthor(sName: string): Promise<number>;

  /**
   * 激活末页动态高度
   * @param bShow
   */
  setDynamicHeightMode(bShow: boolean): Promise<number>;

  /**
   * 激活Inline模式
   * @param flag
   */
  setInlineMode(flag: boolean): Promise<number>;

  /**
   * @api {function} none replaceSpecificStructPropWithBackString
   * @apiName replaceSpecificStructPropWithBackString
   * @apiGroup 91_structAPI
   * @apiVersion  1.0.0
   * @apiDescription replaceSpecificStructPropWithBackString(sCurStructJson: string, sSourceStructJson: string,
   *                     sSourceBase64String: string): string
   * <p>根据指定条件，用指定源文件里的的结构内容替换当前目标文档中的结构的内容。</p>
  *  @apiparam {string} sCurStructJson 当前文档中结构的Json,只支持自定义属性
   * @apiparam {string} sSourceStructJson 源文件的json，只支持自定义属性
   * @apiparam {string} sSourceBase64String 源文件的base64串值
   * @apiSuccessExample {string} Success:
   *  string
   * 1. 不支持区域
   * 2. 被替换掉的元素id，按照筛选条件返回
   * 3. json格式
   * 4. 详细调用例子参考replaceSpecificStructPropWithBackStream
   */
  replaceSpecificStructPropWithBackString(sCurStructJson: string, sSourceStructJson: string,
    sSourceBase64String: string): Promise<string>;

  /**
   * @api {function} none replaceSpecificStructPropWithBackStream
   * @apiName replaceSpecificStructPropWithBackStream
   * @apiGroup 91_structAPI
   * @apiVersion  1.0.0
   * @apiDescription replaceSpecificStructPropWithBackStream(sCurStructJson: string, sSourceStructJson: string,
   *                     content: Blob): string
   * <p>根据指定条件，用指定源文件里的的结构内容替换当前目标文档中的结构的内容。</p>
  *  @apiparam {string} sCurStructJson 当前文档中结构的Json 支持自定义属性 name 属性
   * @apiparam {string} sSourceStructJson 源文件的结构json 参考例子 只支持自定义属性
   * @apiparam {blob} content 源文件的Blob
   * @apiExample {javascript} Example
   * {
   * //当前打开入院记录,想把首次病程里的'主诉'的内容引用过来.
   * //首先确保模板维护的时候，入院记录中的'主诉'跟首次病程中的'主诉'id都设置过，比如都为"主诉"
   * let v1 = [{"id":"主诉"}]; //入院记录中的主诉id 必须是自定义属性
   * let v2 = [{"id":"主诉2"}]; //首次病程中的主诉id 假设不一样。 必须是自定义属性
   * let v3 = [{"name":"texteditor2"}]; //当前文档中需要被替换的元素的name
  * getfilecontentFromServer('首次病程')
  *    .then(blob => {
  *      // 在这里处理blob对象
  *      //const blob2 = new Blob([blob], { type: 'application/apollo-zstd' });
  *      // 替换主诉
  *      editorFunction.replaceSpecificStructPropWithBackStream(JSON.stringify(v1),JSON.stringify(v2),blob);
  *    })
  *    .catch(error => {
  *      console.error('Error fetching blob:', error);
  *    });
  *
   * }
   * @apiSuccessExample {string} Success:
   *  string
   * 1. 不支持区域
   * 2. 被替换掉的元素id，按照筛选条件返回
   * 3. 假设当前文档符合自定义属性的有多个元素，都将被替换
   * 4. json格式
   */
  replaceSpecificStructPropWithBackStream(sCurStructJson: string, sSourceStructJson: string,
    content: Blob): Promise<string>;

  /**
   * @api {function} none replaceContentWithAICorrection
   * @apiName replaceContentWithAICorrection
   * @apiGroup 99_aiAPI
   * @apiVersion  1.0.0
   * @apiDescription replaceContentWithAICorrection(sName:string,sContent:string,sJson:string): Promise<number>
   * <p>AI场景类接口，用特定的结构内容替换当前目标文档中的结构的内容。</p>
  *  @apiparam {string} sName 结构名称
  *  @apiparam {string} sOriginalContent 文档原始内容
  *  @apiparam {string} sAIContent  文档AI内容
  *  @apiparam {string} sJson 控制json
  *  @apiSuccessExample {number} Success:    
  *  number 
  */
  replaceContentWithAICorrection(sName:string,sOriginalContent:string,sSimpleContent:string,sAIContent:string,sJson?:string): Promise<number>;
  
  /**
   * @api {function} none extractContentForAICorrection
   * @apiName extractContentForAICorrection
   * @apiGroup 99_aiAPI
   * @apiVersion  1.0.0
   * @apiDescription extractContentForAICorrection(sName:string,sJson:string): Promise<string>
   * <p>AI场景类接口，提取结构内容。</p>
  *  @apiparam {string} sName 结构名称
  *  @apiparam {string} sJson 控制json 
  *  @apiSuccessExample {string} Success:    
  *  string 包含内容xml跟精简xml的json
  */
  extractContentForAICorrection(sName:string, sJson?: string): Promise<string>;

  /**
   * @api {function} none replaceSpecificRegionPropWithBackStream
   * @apiName replaceSpecificRegionPropWithBackStream
   * @apiGroup 91_structAPI
   * @apiVersion  1.0.0
   * @apiDescription replaceSpecificRegionPropWithBackStream(sCurRegionJson: string, sSourceRegionJson: string,
   *                     content: Blob): string
   * <p>根据指定条件，用指定源文件里的的区域内容替换当前目标文档中的区域的内容。</p>
  *  @apiparam {string} sCurRegionJson 当前文档中结构的Json 只支持自定义属性
   * @apiparam {string} sSourceRegionJson 源文件的json 只支持自定义属性
   * @apiparam {blob} content 源文件的Blob
   * @apiSuccessExample {string} Success:
   *  string
   * 1. 只支持区域
   * 2. 被替换掉的元素id，按照筛选条件返回
   * 3. json格式
   * 4. 详细调用例子参考replaceSpecificStructPropWithBackStream
   */
  replaceSpecificRegionPropWithBackStream(sCurRegionJson: string, sSourceRegionJson: string,
    content: Blob): Promise<string>;

  // getSpecificStructContentWithBackString(sSourceStructJson: IStructJson[],
  //                                        sSourceBase64String: string): Promise<IStructContent[]>;

  // getSpecificStructContentWithBackStream(sSourceStructJson: IStructJson[],
  //                                        content: Blob, type: number): Promise<IStructContent[]>;

  /**
   * @api {function} none insertSignControlAtCurrentCursor
   * @apiName insertSignControlAtCurrentCursor
   * @apiGroup 97_signAPI
   * @apiVersion  1.0.0
   * @apiDescription insertSignControlAtCurrentCursor(sName: string,sJson: string): number
   * <p>当前光标位置插入一个签名控件，可以指定属性。</p>
   * @apiparam {string} sName 签名控件name
   * @apiparam {string} sJson json串值 为空则取默认值
   * @apiparamExample {json} sJson
   * {
   * "SignNumber":2, //签名元素个数
   * "FrontChar":"", //前字符
   * "MidChar":"", //中字符
   * "EndChar":"",//尾字符
   * "SignPlaceholder":"",//签名子元素占位符
   * "type":1,           //1:常规类型   2:集合类型
   * "alwaysShow":2,    //仅当type=2 时生效
   * "showSignBorder":1, //是否显示子签名元素的边框
   * "SignRatio":1,//比例 默认为1
   * }
   * @apiSuccessExample {number} Success:
   *  number
   */
  insertSignControlAtCurrentCursor(sName: string, sJson: string): Promise<number>;

  /**
   * @api {function} none getElementCountByName
   * @apiName getElementCountByName
   * @apiGroup 97_signAPI
   * @apiVersion  1.0.0
   * @apiDescription getElementCountByName(sName: string): number
   * <p>返回指定name的签名控件里的签名元素个数</p>
   * @apiparam {string} sName 签名控件名称
   * @apiSuccessExample {number} Success:
   *  number
   */
  getElementCountByName(sName: string): Promise<number>;

  /**
   * @api {function} none addSignTextToControl
   * @apiName addSignTextToControl
   * @apiGroup 97_signAPI
   * @apiVersion  1.0.0
   * @apiDescription addSignTextToControl(sName: string, index: number, sText: string): number
   * <p>在签名控件里的指定签名子元素中签名（文本）</p>
   * @apiparam {string} sName 签名控件名称
   * @apiparam {number} index 签名子元素的index
   * @apiparam {string} sText 签名文本
   * @apiSuccessExample {number} Success:
   *  number
   */
  addSignTextToControl(sName: string, index: number, sText: string): Promise<number>;

   /**
   * @api {function} none addSignPicToControlWithString
   * @apiName addSignPicToControlWithString
   * @apiGroup 97_signAPI
   * @apiVersion  1.0.0
   * @apiDescription addSignPicToControlWithString(sName: string, index: number, sPicData: string): number
   * <p>在签名控件里的指定签名子元素中签名(图形)</p>
   * @apiparam {string} sName 签名控件名称
   * @apiparam {number} index 签名子元素的index
   * @apiparam {string} sPicData 签名图片的base64串值
   * @apiSuccessExample {number} Success:
   *  number
   */
  addSignPicToControlWithString(sName: string, index: number, sPicData: string): Promise<number>;

  /**
   * @api {function} none addSignPicToControlWithString2
   * @apiName addSignPicToControlWithString2
   * @apiGroup 97_signAPI
   * @apiVersion  1.0.0
   * @apiDescription addSignPicToControlWithString2(sName: string, index: number, sPicData: string,sJson?: string): number
   * <p>在签名控件里的指定签名子元素中签名(图形)</p>
   * @apiparam {string} sName 签名控件名称
   * @apiparam {number} index 签名子元素的index
   * @apiparam {string} sPicData 签名图片的base64串值
   * @apiparam {string} sJson 控制json 可选参数
   * @apiparamExample {json} sJson
   * {
   * "mode":1,//mode=0 图片保留原始大小 （widht height参数不需要传递）
   *           //mode=1 图片受行高约束，插入的签名不会导致行高发生变化（widht height 参数不需要传递）
   *           //mode=2 自定义高宽。图片以指定高宽插入。
   * "width":xxx,//px
   * "height":xxx //px
   * }
   * @apiSuccessExample {number} Success:
   *  number
   */
  addSignPicToControlWithString2(sName: string, index: number, sPicData: string, sJson?: string): Promise<number>;

  /**
   * @api {function} none deleteSignControlByName
   * @apiName deleteSignControlByName
   * @apiGroup 97_signAPI
   * @apiVersion  1.0.0
   * @apiDescription deleteSignControlByName(sName: string): number
   * <p>整个签名控件将被删除</p>
   * @apiparam {string} sName 签名控件名称
   * @apiSuccessExample {number} Success:
   *  number
   */
  deleteSignControlByName(sName: string): Promise<number>;

  /**
   * @api {function} none deleteSignContentByName
   * @apiName deleteSignContentByName
   * @apiGroup 97_signAPI
   * @apiVersion  1.0.0
   * @apiDescription deleteSignContentByName(sName: string,index: number): number
   * <p>删除指定name的签名控件的内容</p>
   * @apiparam {string} sName 签名控件名称
   * @apiparam {number} index 签名控件里的签名元素索引 >0 -- 索引  0 – 全部子元素删除
   * @apiSuccessExample {number} Success:
   *  number
   */
  deleteSignContentByName(sName: string, index: number): Promise<number>;

  /**
   * @api {function} none getSignControlCount
   * @apiName getSignControlCount
   * @apiGroup 97_signAPI
   * @apiVersion  1.0.0
   * @apiDescription getSignControlCount(): number
   * <p>获取整个文档中签名控件的个数</p>
   * @apiSuccessExample {number} Success:
   *  number
   */
  getSignControlCount(): Promise<number>;

  /**
   * @api {function} none getSignControlCountInRegion
   * @apiName getSignControlCountInRegion
   * @apiGroup 97_signAPI
   * @apiVersion  1.0.0
   * @apiDescription getSignControlCountInRegion(sName: string): number
   * <p>获取指定区域中的签名控件的个数</p>
   * @apiparam {string} sName 区域名
   * @apiSuccessExample {number} Success:
   *  number
   */
  getSignControlCountInRegion(sName: string): Promise<number>;

  /**
   * @api {function} none getSignControlNamesInRegion
   * @apiName getSignControlNamesInRegion
   * @apiGroup 97_signAPI
   * @apiVersion  1.0.0
   * @apiDescription getSignControlNamesInRegion(sName: string): string[]
   * <p>获取指定区域中的签名控件的名称</p>
   * @apiparam {string} sName 区域名
   * @apiSuccessExample {string} Success:
   *  string[]
   */
  getSignControlNamesInRegion(sName: string): Promise<string[]>;

  /**
   * @api {function} none getSignControlNames
   * @apiName getSignControlNames
   * @apiGroup 97_signAPI
   * @apiVersion  1.0.0
   * @apiDescription getSignControlNames(): string[]
   * <p>获取全文档中的签名控件的名称</p>
   * @apiSuccessExample {string} Success:
   *  string[]
   */
  getSignControlNames(): Promise<string[]>;

  /**
   * @api {function} none setHeadersTextByJson
   * @apiName setHeadersTextByJson
   * @apiGroup 4_headerfooterAPI
   * @apiVersion 1.0.0
   * @apiDescription setHeadersTextByJson(sJson: string, sRev1: string): number
   * <p>通过JSON格式数据设置文档页眉中结构化元素的内容。可以同时设置多个页眉和多个结构化元素。</p>
   * @apiparam {string} sJson 页眉的JSON格式数据，格式为：{页码索引: [{SerialNumber: "结构化元素序列号", text: "要设置的文本内容"}, ...], ...}
   * @apiparam {string} sRev1 预留参数，暂未使用
   * @apiparamExample {json} sJson示例
   * {
   *   "1": [
   *     {
   *       "SerialNumber": "页眉医院名称",
   *       "text": "XX市人民医院"
   *     },
   *     {
   *       "SerialNumber": "标题",
   *       "text": "主治医师查房记录"
   *     }
   *   ]
   * }
   * @apiSuccessExample {number} Success:
   *  0 - 成功
   */
  setHeadersTextByJson(sJson: string, sRev1: string): Promise<number>;

  /**
   * @api {function} none setHeaderFromTop
   * @apiName setHeaderFromTop
   * @apiGroup 4_headerfooterAPI
   * @apiVersion 1.0.0  
   * @apiDescription setHeaderFromTop(headerFromTop: number): Promise<number>;
   * <p>设置页眉距离顶部的距离 单位mm</p>
   * @apiparam {number} headerFromTop 页眉距离顶部的距离，单位：毫米(mm)
   * @apiSuccessExample {number} Success:
   *  0 - 成功
   */
  setHeaderFromTop(headerFromTop: number): Promise<number>;

  /**
   * @api {function} none setFooterFromBottom
   * @apiName setFooterFromBottom
   * @apiGroup 4_headerfooterAPI
   * @apiVersion 1.0.0
   * @apiDescription setFooterFromBottom(footerFromBottom: number): Promise<number>;
   * <p>设置页脚距离底部的距离 单位mm</p>
   * @apiparam {number} footerFromBottom 页脚距离底部的距离，单位：毫米(mm)
   * @apiSuccessExample {number} Success:
   *  0 - 成功
   */
  setFooterFromBottom(footerFromBottom: number): Promise<number>;

  /**
   * @api {function} none getHeaderFromTop
   * @apiName getHeaderFromTop
   * @apiGroup 4_headerfooterAPI
   * @apiVersion 1.0.0
   * @apiDescription getHeaderFromTop(): Promise<number>;
   * <p>获取页眉距离顶部的距离 单位mm</p>
   * @apiSuccessExample {number} Success:
   *  页眉距离顶部的距离，单位：毫米(mm)
   */
  getHeaderFromTop(): Promise<number>;

  /**
   * @api {function} none getFooterFromBottom
   * @apiName getFooterFromBottom
   * @apiGroup 4_headerfooterAPI
   * @apiVersion 1.0.0
   * @apiDescription getFooterFromBottom(): Promise<number>;
   * <p>获取页脚距离底部的距离 单位mm</p>  
   * @apiSuccessExample {number} Success:
   *  页脚距离底部的距离，单位：毫米(mm)
   */
  getFooterFromBottom(): Promise<number>;

  /**
   * @api {function} none canEditInCurrentCursor
   * @apiName canEditInCurrentCursor
   * @apiGroup 8_editAPI
   * @apiVersion  1.0.0
   * @apiDescription canEditInCurrentCursor(): boolean
   * <p>当前光标位置的是否可以编辑</p>
   * @apiSuccessExample {boolean} Success:
   *  boolean
   */
  canEditInCurrentCursor(): Promise<boolean>;

  /**
   * 文件监听器，影响nsoFileOpenCompleted事件
   */
  addFileListen(): Promise<number>;

  /**
   * 结构化元素监听器，影响nsoStructChanged nsoStructClick nsoStructDBClick nsoStructGainFocus  nsoStructLostFocus 事件
   * @param nRev 预留参数，暂时没用
   */
  addStructListen(nRev?: number): Promise<number>;

  /**
   * 键盘监听器 影响nsoKeyPressedEvent 事件
   * @param nRev 预留参数，暂时没用
   */
  addKeyListen(nRev?: number): Promise<number>;

  /**
   * 移除File监听器
   */
  removeFileListen(): Promise<number>;

  /**
   * 移除Struct监听器
   */
  removeStructListen(): Promise<number>;

  /**
   * 移除键盘监听器
   */
  removeKeyListen(): Promise<number>;

  /**
   * 移除所有的监听器。
   */
  removeAllListen(): Promise<number>;

  /**
   * 开启自动缓存
   */
  enableAutoSave(): Promise<void>;

  /**
   * 关闭自动缓存
   */
  disableAutoSave(): Promise<void>;

  /**
   * 设置c端打印服务地址
   * @param url server url
   */
  setPrinterServerUrl(url: string): number;

  /**
   * 设置地址栏内容
   * @param name
   * @param sText
   */
  setAddressControlText(name: string, sText: string): number;

  /**
   * 获取地址栏内容
   * @param name
   */
  getAddressControlText(name: string): string;

  /**
   * @api {function} none forbidMoveTableBorder
   * @apiName forbidMoveTableBorder
   * @apiGroup 8_editAPI
   * @apiVersion  1.0.0
   * @apiDescription forbidMoveTableBorder(bFixed: boolean): number
   * <p>锁定文档内所有表格，包括页眉页脚的列宽，不让拖动列宽线调整列宽</p>
   * @apiparam {boolean} bFixed true:锁定 false：不锁定
   * @apiSuccessExample {number} Success:
   *  number
   */
  forbidMoveTableBorder(bFixed: boolean): Promise<number>;

  /**
   * @api {function} none getSignElementNames
   * @apiName getSignElementNames
   * @apiGroup 97_signAPI
   * @apiVersion  1.0.0
   * @apiDescription getSignElementNames(sName: string): string
   * <p>返回指定name的签名控件里的元素name</p>
   * @apiparam {string} sName 签名控件名称
   * @apiSuccessExample {string} Success:
   *  string
   */
  getSignElementNames(sName: string): Promise<string>;

  /**
   * @api {function} none getSignedElementNames
   * @apiName getSignedElementNames
   * @apiGroup 97_signAPI
   * @apiVersion  1.0.0
   * @apiDescription getSignedElementNames(sName: string): string
   * <p>返回指定name的签名控件里的已经签名的元素name</p>
   * @apiparam {string} sName 签名控件名称
   * @apiSuccessExample {string} Success:
   *  string
   */
  getSignedElementNames(sName: string): Promise<string>;

 /**
 * @api {function} none addSignContentToControl
 * @apiName addSignContentToControl
 * @apiGroup 97_signAPI
 * @apiVersion 1.0.0
 * @apiDescription addSignContentToControl(sName: string, sJson: string): number
 * <p>在签名控件里的指定签名子元素中添加签名内容（图形/文字/图文混排），支持多种类型及签名大小控制</p>
 * @apiparam {string} sName 签名控件名称
 * @apiparam {string} sJson 控制参数，支持单个对象或数组对象（多个子签名内容设置）
 * mode 说明
 * - 1: `LineHeight`模式，图片将根据签名段落行高自动等比缩放（忽略 width/height)
 * - 2: `CustomHeight`模式，使用用户指定的 `width` 和 `height` 值渲染图片
 * - 若不指定 mode，则默认按原图比例，优先使用 width/height 设置（可部分设置
 * @apiparamExample {string} string 示例
 *
 * // === 纯文字签名 ===
 * {
 *   "name": "sign1",
 *   "content": {
 *     "type": 1,
 *     "data": "张三签署"
 *   },
 *   "helpTips": "签名说明提示"
 * }
 * // === 纯图片签名（支持 mode）===
 * {
 *   "name": "sign2",
 *   "content": {
 *     "type": 2,
 *     "data": {
 *       "source": "data:image/png;base64,...",
 *       "mode": 2,
 *       "width": 100,
 *       "height": 40
 *     }
 *   },
 *   "helpTips": "签名图像"
 * }
 *
 * @apiSuccessExample {javascript} Success
 * const sName = "signBox01";
 * const sJsonArr = JSON.stringify([...]);
 * const result = docAPI.addSignContentToControl(sName, sJsonArr);
 * if (result === 0) {
 *   console.log("批量签名插入成功");
 * } else {
 *   console.warn("签名失败，错误码：", result);
 * }
 */
  addSignContentToControl(sName: string, sJson: string): Promise<number>;

  /**
   * @api {function} none deleteSignContent
   * @apiName deleteSignContent
   * @apiGroup 97_signAPI
   * @apiVersion  1.0.0
   * @apiDescription deleteSignContent(sName: string, sJson: string): number
   * <p>删除指定name的签名控件的内容</p>
   * @apiparam {string} sName 签名控件名称
   * @apiparam {string} sJson 控制参数
   * @apiSuccessExample {number} Success:
   *  number
   */
  deleteSignContent(sName: string, sJson: string): Promise<number>;

    /**
   * @api {function} none deleteRedundantInHeader
   * @apiName deleteRedundantInHeader
   * @apiGroup 4_headerfooterAPI
   * @apiVersion  1.0.0
   * @apiDescription deleteRedundantInHeader(sText: string): boolean
   * <p>删除当前文档的页眉内容末尾的空行</p>
   * @apiparam {string} sText 预留参数 ''
   * @apiSuccessExample {number} Success:
   *  number
   */
  deleteRedundantInHeader(sText: string): Promise<number>;

  /**
   * 移除指定签名里特定的字符。
   * @param sName 签名元素名称
   * @param sRemoveChar 指定字符
   */
  removeCertainCharInSignControl(sName: string, sRemoveChar: string): Promise<number>;

  /**
   * emr模版保存校验：当页眉有表格的时候，判断第一个单元格第一个portion里的字体高度，然后看行高。
   * 如果行高不足以显示全这个字，返回错误代码让emr提示。
   */
  checkFirstTableRowHeightInHeader(sTableName: string): Promise<number>;

  /**
   * @api {function} none acceptRevisions
   * @apiName acceptRevisions
   * @apiGroup 85_revisionAPI
   * @apiVersion  1.0.0
   * @apiDescription acceptRevisions()
   * <p>接受所有修订</p>
   * @apiparam {string} sText 预留参数 ''
   * @apiSuccessExample {javascript} Success:
   *  editor.acceptRevisions();
   */
  acceptRevisions(): void;

  /**
   * @api {function} none moveCursorToTableCell
   * @apiName moveCursorToTableCell
   * @apiGroup 86_tableAPI
   * @apiVersion  1.0.0
   * @apiDescription moveCursorToTableCell(sTable: string, sCellName: string): number
   * <p>光标跳转到指定表格，指定单元格内，如果单元格内有内容，光标在单元格末尾</p>
   * @apiparam {string} sTable table name
   * @apiparam {string} sCellName cell name
   * @apiSuccessExample {number} Success:
   *  number
   */
  moveCursorToTableCell(sTable: string, sCellName: string): Promise<number>;

  /**
   * 此接口调用后，后续对编辑器的操作都不会启用刷新机制。
   * 此接口调用将强行刷新一次
   * @param bLock 启用刷新机制
   */
  lockRefresh(bLock: boolean): Promise<number>;

  /**
   * 控制光标在不可编辑的结构化元素上，是否显示禁止光标
   * @param bFlag
   */
  enableNotAllowedCursorStyle(bFlag: boolean): Promise<number>;

  /**
   * 结构化元素: 标题后的文字继承默认字体格式可配置
   * @param bEnable 控制是否在结构化元素标题后保留缺省样式
   */
  enableDefaultStyleAfterTitle(bEnable: boolean): Promise<number>;

  /**
   * @api {function} none setDynamicGridLine
   * @apiName setDynamicGridLine
   * @apiGroup 2_layoutAPI
   * @apiVersion  1.0.0
   * @apiDescription setDynamicGridLine(bEnable: boolean, sParam?: string): number
   * <p>设置动态网格线是否显示, 显示的颜色或者类别</p>
   * @apiparam {boolean} bEnable true：启用 false：关闭
   * @apiparam {string} sParam cell json {color:"#123456"} 可选
   * @apiSuccessExample {number} Success:
   *  number
   */
  setDynamicGridLine(bEnable: boolean, sParam?: string): Promise<number>;

   /**
   * @api {function} none enableSouceBindInRegion
   * @apiName enableSouceBindInRegion
   * @apiGroup 90_regionAPI
   * @apiVersion  1.0.0
   * @apiDescription enableSouceBindInRegion(sRegion:string,nControl:number): number
   * <p>控制指定区域里的设置过souceBind的元素是否刷新数据/p>
   * @apiparam {string} sRegion 区域名
   * @apiparam {number} nControl 1 - 总刷新  0 -- 不刷新
   * @apiSuccessExample {number} Success:
   *  number
   */
  enableSouceBindInRegion(sRegion:string,nControl:number):Promise<number>;

  /**
   * @api {function} none getSourceBindJson
   * @apiName getSourceBindJson
   * @apiGroup 91_structAPI
   * @apiVersion  1.0.0
   * @apiDescription getSourceBindJson(): string
   * <p>返回文档现有的外部数据源绑定的json</p>
   * @apiSuccessExample {string} Success:
   *  string 为空则表示没有设置
   */
  getSourceBindJson(): Promise<string>;

  /**
   * @api {function} none setSourceBindJson
   * @apiName setSourceBindJson
   * @apiGroup 91_structAPI
   * @apiVersion  1.0.0
   * @apiDescription setSourceBindJson(sJson: string): number
   * <p> 通过json一次性刷新外部源的值</p>
   * @apiparam {json} sJson json串值
   * @apiparamExample {json} sJson
   * {
        * "Patient":
        * {
        *   name:"张三",
        *   age:"12",
        *   dept:"骨科"
        *}
    *}
   * @apiSuccessExample {number} Success:
   *  number
   */
  setSourceBindJson(sJson: string): Promise<number>;

  /**
   * @api {function} none getTableRowStructInfo
   * @apiName getTableRowStructInfo
   * @apiGroup 86_tableAPI
   * @apiVersion  1.0.0
   * @apiDescription getTableRowStructInfo(sTableName: string, nRowIndex: number): string
   * <p>获取指定表格指定行的结构化元素信息</p>
   * @apiparam {string} sTableName 表格名称
   * @apiparam {number} nRowIndex 行索引，从1开始
   * @apiSuccessExample {string} Success:
   *  该行中的结构化元素信息，格式与 getTableXmlInfoByParament 相同
   */
  getTableRowStructInfo(sTableName: string, nRowIndex: number): Promise<string>;

  /**
   * @api {function} none getCurrentTableRowIndex
   * @apiName getCurrentTableRowIndex
   * @apiGroup 86_tableAPI
   * @apiVersion  1.0.0
   * @apiDescription getCurrentTableRowIndex(): number
   * <p>获取当前光标所在表格行的索引</p>
   * @apiSuccessExample {number} Success:
   *  当前光标所在表格行的索引，从1开始；如果光标不在表格内，则返回-1
   */
  getCurrentTableRowIndex(): Promise<number>;
}

export interface IExternalEvent {
  /**
   * @api {Event} none nsoFileOpenCompleted
   * @apiName nsoFileOpenCompleted
   * @apiGroup 9999_eventAPI
   * @apiVersion  1.0.0
   * @apiDescription nsoFileOpenCompleted(sPath: string, sReverve?: string)
   * <p> 编辑器里面加载一个文档结束后将向上层发送的事件</p>
   * @apiparam {string} sPath 打开的文档全路径
   * @apiparam {string} sReverve 预留，目前没用
   */
  nsoFileOpenCompleted?(sPath: string, sReverve?: string): void;

  /**
   * @api {Event} none nsoKeyPressedEvent
   * @apiName nsoKeyPressedEvent
   * @apiGroup 9999_eventAPI
   * @apiVersion  1.0.0
   * @apiDescription nsoKeyPressedEvent(nKeyCode: number, ctrlKey: boolean, shiftKey: boolean, altKey: boolean): boolean
   * <p> 编辑器内键盘按键按下的时候激发的事件。</p>
   * @apiparam {number} nKeyCode 键盘的KeyCode
   * @apiparam {boolean} altKey ATL的按下值
   * @apiparam {boolean} ctrlKey CTRL的按下值
   * @apiparam {boolean} shiftKey SHIFT的按下值
   * @apiSuccessExample {boolean} Success:
   *  boolean 事件拦截，当返回false时，当前输入无效
   */
  nsoKeyPressedEvent?(nKeyCode: number, ctrlKey: boolean, shiftKey: boolean, altKey: boolean): boolean;

  /**
   * @api {Event} none nsoStructClick
   * @apiName nsoStructClick
   * @apiGroup 9999_eventAPI
   * @apiVersion  1.0.0
   * @apiDescription nsoStructClick(sName: string, type: number)
   * <p> 鼠标单击结构化元素时产生的事件。</p>
   * @apiparam {string} sName 结构化元素名称
   * @apiparam {number} type 结构化元素类型
   */
  nsoStructClick?(sName: string, type: number): void;

  /**
   * @api {Event} none nsoStructDBClick
   * @apiName nsoStructDBClick
   * @apiGroup 9999_eventAPI
   * @apiVersion  1.0.0
   * @apiDescription nsoStructDBClick(sName: string, type: number)
   * <p> 鼠标双击结构化元素时产生的事件。</p>
   * @apiparam {string} sName 结构化元素名称
   * @apiparam {number} type 结构化元素类型
   */
  nsoStructDBClick?(sName: string, type: number): void;

  /**
   * @api {Event} none nsoStructGainFocus
   * @apiName nsoStructGainFocus
   * @apiGroup 9999_eventAPI
   * @apiVersion  1.0.0
   * @apiDescription nsoStructGainFocus(sName: string, type: number)
   * <p>结构化元素 获得编辑焦点，即光标在结构化元素 内，可以通过光标左右移动进入，也可以通过鼠标单击进入，从而产生该事件。
   * 只针对本结构，父结构不会触发</p>
   * @apiparam {string} sName 结构化元素名称
   * @apiparam {number} type 结构化元素类型
   */
  nsoStructGainFocus?(sName: string, type: number): void;

   /**
   * @api {Event} none nsoStructLostFocus
   * @apiName nsoStructLostFocus
   * @apiGroup 9999_eventAPI
   * @apiVersion  1.0.0
   * @apiDescription nsoStructLostFocus(sName: string, type: number)
   * <p> 结构化元素失去编辑焦点，即光标不在结构化元素内，可以通过光标左右移动退出，也可以通过鼠标单击其他地方，从而产生该事件。
   * 只针对本结构，父结构不会触发。</p>
   * @apiparam {string} sName 结构化元素名称
   * @apiparam {number} type 结构化元素类型
   */
  nsoStructLostFocus?(sName: string, type: number): void;

  /**
   * @api {Event} none nsoStructChanged
   * @apiName nsoStructChanged
   * @apiGroup 9999_eventAPI
   * @apiVersion  1.0.0
   * @apiDescription nsoStructChanged(sName: string, type: number)
   * <p> 结构化元素内容发生变化时产生的事件</p>
   * @apiparam {string} sName 结构化元素名称
   * @apiparam {number} type 结构化元素类型
   */
  nsoStructChanged?(sName: string, type: number): void;

  /**
   * 区域点击操作符
   * @param sRegionName 区域名称
   */
  nsoRegionCustomEvent(sRegionName: string): void;
  /**
   * 区域点击操作符
   * @param x 横坐标
   * @param y 纵坐标
   */
  nsoRegionOperate(x: number, y: number): void;

  /**
   * @api {Event} none nsoRegionGainFocus
   * @apiName nsoRegionGainFocus
   * @apiGroup 9999_eventAPI
   * @apiVersion  1.0.0
   * @apiDescription nsoRegionGainFocus(sName: string, type: number)
   * <p> 区域 获得编辑焦点，即光标在区域 内，可以通过光标左右移动进入，也可以通过鼠标单击进入，从而产生该事件</p>
   * @apiparam {string} sName 区域名称
   * @apiparam {number} type 预留参数 无意义
   */
  nsoRegionGainFocus?(sName: string, type: number): void;

  /**
   * @api {Event} none nsoRegionLostFocus
   * @apiName nsoRegionLostFocus
   * @apiGroup 9999_eventAPI
   * @apiVersion  1.0.0
   * @apiDescription nsoRegionLostFocus(sName: string, type: number)
   * <p>失去编辑焦点，即光标在从区域内移动区域外，可以通过光标左右移动移出，也可以通过鼠标单击，从而产生该事件。</p>
   * @apiparam {string} sName 区域名称
   * @apiparam {number} type 预留参数 无意义
   */
  nsoRegionLostFocus?(sName: string, type: number): void;

  /**
   * @api {Event} none nsoRegionChanged
   * @apiName nsoRegionChanged
   * @apiGroup 9999_eventAPI
   * @apiVersion  1.0.0
   * @apiDescription nsoRegionChanged(sName: string, type: number)
   * <p>区域内容发生改变后产生的事件</p>
   * <p>注意：区域内的子元素内容发生改变后，会触发最外层的区域的该事件，</p>
   * @apiparam {string} sName 区域名称
   * @apiparam {number} type 预留参数 无意义
   */
  nsoRegionChanged?(sName: string, type: number): void;

  /**
   * @api {Event} none nsoRegionDBClick
   * @apiName nsoRegionDBClick
   * @apiGroup 9999_eventAPI
   * @apiVersion  1.0.0
   * @apiDescription nsoRegionDBClick(sName: string, type: number)
   * <p>区域鼠标双击产生的事件。</p>
   * @apiparam {string} sName 区域名称
   * @apiparam {number} type 预留参数 无意义
   */
  nsoRegionDBClick?(sName: string, type: number): void;

   /**
   * @api {Event} none nsoStructCheckChanged
   * @apiName nsoStructCheckChanged
   * @apiGroup 9999_eventAPI
   * @apiVersion  1.0.0
   * @apiDescription nsoStructCheckChanged(sName: string, bChecked: boolean)
   * <p>Checkbox的check状态改变产生的事件</p>
   * @apiparam {string} sName 结构化名称
   * @apiparam {boolean} bChecked 选中状态
   */
  nsoStructCheckChanged?(sName: string, bChecked: boolean): void;

  /**
   * @api {Event} none nsoFileModifyChanged
   * @apiName nsoFileModifyChanged
   * @apiGroup 9999_eventAPI
   * @apiVersion  1.0.0
   * @apiDescription nsoFileModifyChanged(bModified: boolean)
   * <p>文档脏标记变化后产生的事件</p>
   * @apiparam {boolean} bModified True：文档被修改 False：文档未被修改
   */
  nsoFileModifyChanged?(bModified: boolean): void;

  /**
   * @api {Event} none nsoPrinterDataEvent
   * @apiName nsoPrinterDataEvent
   * @apiGroup 9999_eventAPI
   * @apiVersion  1.0.0
   * @apiDescription nsoPrinterDataEvent(htmlContent: string)
   * <p>当预览打印时，准备好打印的 HTML 数据后触发此事件。</p>
   * @apiparam {string} htmlContent 打印的 HTML 内容字符串。
   */
  nsoPrinterDataEvent?(htmlContent: string): void;
}

/**
 * @apiDefine A_httpAPI 编辑器的后台服务
 */
/**
 * @api {function} 1.后台服务介绍
 * @apiDescription hz编辑提供的标准容器镜像启动后，会附带http服务，用在离线文档操作以及格式转换上。
 * <p>不需要额外部署。</p>
 * <p>如果频繁访问编辑器的后台服务，则需要多部署几个实例 启用负载均衡</p>
 * <p>每个实例建议分配2核cpu 8G内存</p>
 * @apiGroup A_httpAPI
 */
  /**
   * @api {Event} none 抽取hz文件的结构化信息
   * @apiName getStructsXmlInfoByFile
   * @apiGroup A_httpAPI
   * @apiVersion  1.0.0
   * @apiDescription getStructsXmlInfoByFile
   * <p> post: [ /editorSvr/v1/getStructsXmlInfoByFile ] </p>
   * @apiparam {blob} file 包含于body，文件的blob数据
   * @apiSuccessExample {string} Success:
   *  string
   */
    /**
   * @api {Event} none html文件转换为pdf文件
   * @apiName printToPdf
   * @apiGroup A_httpAPI
   * @apiVersion  1.0.0
   * @apiDescription printToPdf
   * <p> post: [ /editorSvr/v1/printToPdf ] </p>
   * @apiparam {blob} file 包含于body，html文件的blob数据
   * @apiSuccessExample {blob} Success:
   *  blob
   */
    /**
   * @api {Event} none hz文件转换为html
   * @apiName convertToHtml
   * @apiGroup A_httpAPI
   * @apiVersion  1.0.0
   * @apiDescription convertToHtml
   * <p> post: [ /editorSvr/v1/convertToHtml ] </p>
   * @apiparam {blob} file 包含于body，hz文件的blob数据
   * @apiparam {number} cleanMode 包含于body，清洁模式 0 – 非清洁，1 – 清洁
   * @apiparam {number} needWaterMark  包含于body，水印 0 – 移除，1 – 保留
   * @apiSuccessExample {blob} Success:
   *  blob
   */
    /**
   * @api {Event} none pdf文件转换为ofd文件
   * @apiName pdf2ofd
   * @apiGroup A_httpAPI
   * @apiVersion  1.0.0
   * @apiDescription pdf2ofd
   * <p> post: [ /editorSvr/v1/pdf2ofd ] </p>
   * <p>该服务需要单独部署。一般为9090端口</p>
   * @apiparam {blob} file 包含于body，pdf文件的blob数据
   * @apiSuccessExample {blob} Success:
   *  blob
   */
     /**
   * @api {Event} none ofd文件转换为html文件
   * @apiName odf2html
   * @apiGroup A_httpAPI
   * @apiVersion  1.0.0
   * @apiDescription odf2html
   * <p> post: [ /editorSvr/v1/odf2html ] </p>
   * <p>该服务需要单独部署，默认为9090</p>
   * @apiparam {blob} file 包含于body，odf文件的blob数据
   * @apiSuccessExample {blob} Success:
   *  blob
   * @apiExample {javascript} Example
   * function downloadFileFromBlob(blob, fileName) {
   * // 创建一个指向blob的URL
   * const url = window.URL.createObjectURL(blob);
  *
  *  // 创建一个a标签用于下载
  *  const a = document.createElement('a');
  *  a.style.display = 'none';
  *  a.href = url;
  *  a.download = fileName;
  *
  *  // 添加到document中（可选），然后触发点击
  *  document.body.appendChild(a);
  *  a.click();
  *
  *  // 清理工作
  *  window.URL.revokeObjectURL(url);
  *  document.body.removeChild(a);
  * }
  *
  *function postFile(fileBlob, url) {
  *  // 设置POST请求的头部，包含表单数据
  *  const formData = new FormData();
  *  formData.append('file', fileBlob);
  *
  *  // 发送POST请求
  *  fetch(url, {
  *      method: 'POST',
  *      body: formData,
  *  })
  *  .then(response => {
  *      if (!response.ok) {
  *          throw new Error('Network response was not ok');
  *      }
  *      return response.blob(); // 假设服务器返回的是Blob或文件流
  *  })
  *  .then(blob => {
  *      // 调用下载函数
  *      downloadFileFromBlob(blob, 'a.html');
  *  })
  *  .catch(error => {
  *      console.error('There was a problem with your fetch operation:', error);
  *  });
  *}

  * // 假设你已经有了一个odf文件的content
  *  const blob = new Blob([content], { type: 'application/octet-stream' });

  * // 调用函数，传入Blob和URL
  * postFile(fileBlob, 'http://localhost:9090/editorSvr/v1/odf2html');
  */
