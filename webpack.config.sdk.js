const CleanWebpackPlugin = require('clean-webpack-plugin');
const TsconfigPathsPlugin = require('tsconfig-paths-webpack-plugin');
const path = require('path');
const webpack = require('webpack');
const features = require('./features.json');
const conditionalCompiler = {
    loader: 'js-conditional-compile-loader',
    options: {
        WATER: false, // 去除水印代码
        NOWATER: true, // 无水印版
    }
}
module.exports = {
  entry: { 'sdk': './src/sdk/index.ts' },
  module: {
    rules: [
      {
        test: /\.tsx?$/,
        use: ['ts-loader', conditionalCompiler],
        exclude: /node_modules/
      }
    ]
  },
  node: {
    fs: 'empty'
  },
  resolve: {
    plugins: [new TsconfigPathsPlugin({})],
    extensions: ['.tsx', '.ts', '.js'],
    alias: {
      '@': path.resolve(__dirname, './src')
    }
  },
  output: {
    libraryTarget: 'umd',
    library: 'EmrEditorSDK',
    filename: 'index.js',
    path: path.resolve(__dirname, 'dist/sdk')
  },
  plugins: [
    new CleanWebpackPlugin(['dist/sdk']),
    new webpack.DefinePlugin(features),
  ],
  mode: 'production'
};
