import {xml2json} from 'xml-js';
// import _ from 'underscore';
// import xml2js from 'xml2js';

const isObject = (obj: any): boolean => {
    return obj === Object(obj);
};

const compareObjects = (a: object, b: object, schema: object, keyPrefix: string, options: any): any => {
    let differences = [];
    const    ak          = Object.keys(a);
    const    bk          = Object.keys(b);
    const    allKeys     = [].concat(ak, bk);
    const lhs = options.left || 'lhs';
    const rhs = options.right || 'rhs';

    allKeys.forEach((key: string): any => {
        const formattedKey = (keyPrefix || '') + key;
        const fieldOptions = schema[key] || {};

        if (options.filter && options.filter(a[key], b[key])) {
            return;
        }

        if (!ak.includes(key)) {
            if (fieldOptions.skipKey) {
                return;
            } else {
                return differences.push({
                    path: formattedKey,
                    type: 'missing field',
                    message: 'field ' + formattedKey + ' not present in ' + lhs,
                });
            }
        }

        if (!bk.includes(key)) {
            if (fieldOptions.skipKey) {
                return;
            } else {
                return differences.push({
                    path: formattedKey,
                    type: 'missing field',
                    message: 'field ' + formattedKey + ' not present in ' + rhs
                });
            }
        }

        if ((fieldOptions.compareTypes === undefined || fieldOptions.compareTypes === true)
            && (typeof a[key] !== typeof b[key])) {
            return differences.push({
                path: formattedKey,
                type: 'type equality',
                message: lhs + ' field type (' + (typeof a[key]) + ') does not match ' + rhs + ' field type ('
                    + (typeof b[key] + ')'),
                [lhs]: a[key],
                [rhs]: b[key]
            });
        }

        if (Array.isArray(a[key])) {
            for (let i = 0; i < a[key].length; i++) {
                const objA = a[key][i];
                const objB = b[key][i];

                if (objA === '' && objB === '') {
                    return;
                }

                if (objA === 'false' && objB === 'false') {
                    return;
                }
                if (objA === 0 && objB === 0) {
                    return;
                }
                if (!objB) {
                    return differences.push({
                        path: formattedKey,
                        type: 'missing field',
                        message: 'field ' + formattedKey + '[' + i + ']' + ' not present in ' + rhs
                    });
                }
                if (!objA) {
                    return differences.push({
                        path: formattedKey,
                        type: 'missing field',
                        message: 'field ' + formattedKey + '[' + i + ']' + ' not present in ' + lhs
                    });
                }
                if (isObject(a[key][i]) && isObject(b[key][i])) {
                        differences = differences.concat(compareObjects(a[key][i], b[key][i],
                            fieldOptions, formattedKey + '[' + i + '].', options));
                    } else {
                        if ((fieldOptions.compareValues === undefined || fieldOptions.compareValues === true) &&
                            ((a[key][i] !== b[key][i]) && (fieldOptions.equalityFunction  ?
                                !fieldOptions.equalityFunction(a[key][i], b[key][i]) : true) )) {
                            return differences.push({
                                path: formattedKey,
                                type: 'value equality',
                                [lhs]: a[key][i],
                                [rhs]: b[key][i]
                            });
                        }
                    }
            }
        } else if (isObject(a[key])) {
            differences = differences.concat(compareObjects(a[key], b[key], fieldOptions, formattedKey + '.', options));
        } else  if (typeof a[key] === 'string' && typeof b[key] === 'string' && options.stringCaseInsensitive) {
            if ((a[key].toLowerCase() === b[key].toLowerCase())) {
                return;
            }
        } else if ((fieldOptions.compareValues === undefined || fieldOptions.compareValues === true)
        &&  ((a[key] !== b[key]) && (fieldOptions.equalityFunction  ?
            !fieldOptions.equalityFunction(a[key], b[key]) : true) )) {
                return differences.push({
                    path: formattedKey,
                    type: 'value equality',
                    [lhs]: a[key],
                    [rhs]: b[key]
                });

        }
    });

    return differences;
};

export class DiffXml {
    public diff(lhs: string, rhs: string, schema?: object, options?: any): any {
        if (!lhs || !rhs) {
            return;
        }
        const obj = {compact: true, ignoreComment: true, spaces: 4};
        const lhObj = JSON.parse(xml2json(lhs, obj));
        const rhObj = JSON.parse(xml2json(rhs, obj));
        return compareObjects(lhObj, rhObj, schema || {}, null, options || {});
    }
}

// const abx =  {
//     diff(lhs: string, rhs: string, schema: object, options: any, next: any): any {
//       next(compareObjects(lhs, rhs, schema || {}, null, options || {}));
//     },
//    diffAsXml(lhs, rhs, schema, options, next) {
//         xml2js.parseString(lhs, function(err, lhsp) {
//             xml2js.parseString(rhs, function(err, rhsp) {
//                 next(compareObjects(lhsp, rhsp, schema || {}, null, options || {}));
//             });
//         });
//     }
// };
