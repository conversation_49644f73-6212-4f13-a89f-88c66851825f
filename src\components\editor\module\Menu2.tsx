import * as React from 'react';
import '../style/menu.less';
import {GlobalEvent as gEvent, GlobalEventName as gEventName} from '../../../common/GlobalEvent';
import { getDocumentCoreRecordReplayState } from '../../../model/DocumentCoreRecordReplay';
import { ResultType, ToolbarIndex, ViewModeType, EditModeType, EquationType } from '../../../common/commonDefines';
import { EmrEditor } from '../Main';
import { ToolbarAction } from '../../../common/menu/ToolbarAction';
import { ExportHtml } from './ExportHtml';
import { getImageEditor } from '@hz-editor/plugins';
import { message } from '../../../common/Message';
import { IFRAME_MANAGER } from '../../../common/IframeManager';
import { logger } from '../../../common/log/Logger';
// import { incrementDB } from '../../../common/indexDB/IncrementDB';

interface IBar {
    name: string;
    index: number;
    hide?: boolean;
    className?: string;
    children?: IBar[];
    disabled?: boolean;
    checked?: boolean;
    initChildCheck?: boolean;
}

let toolbars: IBar[];

interface IProps {
    host: EmrEditor;
}

interface IState {
    activeIndex: number;
    bRefresh: boolean;
}

export default class Toolbar extends React.Component<IProps, IState> { // 'Menu' actually
    private datas: any[];
    private checkedDatas: number[];
    private ulRef: any;
    private bItemClick: boolean;
    private host: EmrEditor;
    private timeout: any;
    private insertFileRef: any;
    private documentCore: any;
    private openFileRef: React.RefObject<HTMLInputElement>;
    private insertImageRef: React.RefObject<HTMLInputElement>;
    private zipToApoRef: React.RefObject<HTMLInputElement>;
    private apoToZipRef: React.RefObject<HTMLInputElement>;
    private viewModeObj: any;
    private editModeObj: any;

    private menuAction: ToolbarAction;

    constructor(props: IProps) {
        super(props);
        this.host = this.props.host;
        this.datas = toolbars;
        this.state = {
            activeIndex: -1,
            bRefresh: false,
        };
        this.documentCore = this.host.getDocumentCore();
        this.checkedDatas = [];
        this.ulRef = React.createRef();
        this.insertFileRef = React.createRef();
        this.insertImageRef = React.createRef();
        this.openFileRef = React.createRef();
        this.zipToApoRef = React.createRef();
        this.apoToZipRef = React.createRef();
        const viewModeObj = this.viewModeObj = {};
        viewModeObj[ViewModeType.BreakPageView] = ToolbarIndex.BreakPageView;
        viewModeObj[ViewModeType.WebView] = ToolbarIndex.WebView;
        this.editModeObj = {};
        this.editModeObj[EditModeType.Normal] = ToolbarIndex.Normal;
        this.editModeObj[EditModeType.Protected] = ToolbarIndex.Protected;
        this.editModeObj[EditModeType.StrictMode] = ToolbarIndex.StrictMode;
    }

    public render(): any {
        const datas = this.datas;
        if (!datas || datas.length === 0) {
            return null;
        }
        this.setRecordReplayMenuItemsStatus();

        return (
            <div className='editor-menu'>
                <div className='box'>
                    <div className='logo'>HONGZHI EDITOR</div>
                    <nav className='nav'>
                        <ul className='list1' ref={this.ulRef} onMouseLeave={this.onMouseLeave}>
                            {this.renderFirstMenu(datas)}
                        </ul>
                    </nav>
                </div>
                <input
                    id='insertImage'
                    className='insert-image'
                    type='file'
                    ref={this.insertImageRef}
                    accept='image/*'
                    onChange={this.fileChange}
                />
                <input
                    id='insertFile'
                    className='insert-file'
                    type='file'
                    ref={this.insertFileRef}
                    onChange={this.fileChange}
                />
                <input
                    id='openFile'
                    className='open-file'
                    type='file'
                    ref={this.openFileRef}
                    onChange={this.fileChange}
                />
                <input
                    id='apoToZip'
                    className='apo-to-zip'
                    type='file'
                    ref={this.apoToZipRef}
                    onChange={this.fileChange}
                />
                <input
                    id='zipToApo'
                    className='zip-to-apo'
                    type='file'
                    ref={this.zipToApoRef}
                    onChange={this.fileChange}
                />
            </div>
        );
    }

    public componentDidMount(): void {
        this.menuAction = new ToolbarAction(this.host);
        this.addEvent();
    }

    public componentWillUnmount(): void {
        this.deleteEvent();
    }

    private renderFirstMenu(datas: IBar[]): any {
        const activeIndex = this.state.activeIndex;
        return datas.filter((data: IBar) => data.hide !== true)
        .map((data: IBar, index) => {
            let className: string = 'first';
            if (activeIndex === index) {
                className += ' active';
            }
            if (data.disabled === true) {
                className += ' disabled';
            }
            return (
                <li
                    key={data.index}
                    data-index={index}
                    className={className}
                    onMouseEnter={this.onMouseEnter.bind(this, index)}
                >
                    <span>{data.name}</span>
                    {this.renderSecondMenu(data.children, index)}
                </li>
            );
        });
    }

    private renderSecondMenu(datas: IBar[], parentIndex: number): any {
        if (!datas) {
            return null;
        }

        const documentCore = this.host.getDocumentCore();
        const bIntable = documentCore.isInTableCell();
        const bSelection = documentCore.isSelectionUse();

        const items = datas.filter((data: IBar) => data.hide !== true)
        .map((data: IBar, index) => {
            let className: string = null;

            if ( ToolbarIndex.InsertFile === data.index || ToolbarIndex.InsertTable === data.index ) {
                if ( bIntable || bSelection ) {
                    data.disabled = true;
                } else {
                    data.disabled = false;
                }
            }

            if (data.disabled === true) {
                className = ' disabled';
            } else {
                className = '';
            }

            const dataIndex = parentIndex + ',' + index;
            return (
                <li
                    key={data.index}
                    data-index={dataIndex}
                    className={className}
                    onMouseEnter={this.onMouseEnter2.bind(this, data)}
                >
                    {this.renderCheckedFlag(data)}
                    {this.renderNextMenuFlag(data.children)}
                    <span>{data.name}</span>
                    {this.renderThreeMenu(data.children, dataIndex)}
                </li>
            );
        });

        if (!items || items.length === 0) {
            return null;
        }

        return (<ul className='list2'>{items}</ul>);
    }

    private renderCheckedFlag(item: IBar): any {
        if (item.checked === undefined) {
            return null;
        }

        if (!this.checkedDatas.includes(item.index)) {
            return null;
        }

        return (<span className='checked-menu-flag' />);
    }

    private renderNextMenuFlag(datas: IBar[]): any {
        if (!datas) {
            return null;
        }

        return (<span className='next-menu-flag' />);
    }

    private renderThreeMenu(datas: IBar[], parentIndex: string): any {
        if (!datas) {
            return null;
        }
        const documentCore = this.host.getDocumentCore();
        const bInHeadFooter = documentCore.isInHeaderFooter();

        const items = datas.filter((data: IBar) => data.hide !== true)
        .map((data: IBar, index) => {
            let className: string = null;

            if ( ToolbarIndex.Region === data.index ) {
                if ( bInHeadFooter ) {
                    data.disabled = true;
                } else {
                    data.disabled = false;
                }
            }

            if (data.disabled === true) {
                className = ' disabled';
            }
            return (
                <li key={data.index} data-index={parentIndex + ',' + index} className={className}>
                    {this.renderCheckedFlag(data)}
                    <span>{data.name}</span>
                </li>
            );
        });

        if (!items || items.length === 0) {
            return null;
        }

        return (<ul className='list3'>{items}</ul>);
    }

    private fileChange = (e: any) => {
        const inputTarget = e.target as HTMLInputElement;
        // tslint:disable-next-line: newline-per-chained-call
        this.menuAction.fileChange(inputTarget).then((res) => {
            if (res === true) {
                this.clearCheckedDatas();
                // this.refresh();
            }
        });

        // trick input field to "onChange" every time
        inputTarget.value = '';
    }

    private refresh(): void {
        this.host.handleRefresh();
    }

    private getLiDom(target: any): HTMLLIElement {
        let node: HTMLLIElement;
        if (target.tagName === 'LI') {
            node = target;
        } else {
            const parentNode = target.parentNode;
            if (parentNode.tagName === 'LI') {
                node = parentNode;
            }
        }

        if (!node) {
            return;
        }

        return node;
    }

    private getActiveItem(indexs: string[]): IBar {
        const datas = this.datas;
        let index = 0;
        let content = datas[indexs[index++]];
        while (content) {
            if (!content.children) {
                return content;
            }
            const data = content.children[indexs[index++]];
            if (!data) {
                return content;
            }

            content = data;
        }
    }

    private setItemEvent(item: IBar): void {
        const documentCore = this.documentCore;
        switch (item.index) {
            case ToolbarIndex.NewFile: {
                this.menuAction.createNew()
                .then((res) => {
                    if (res) {
                        this.clearCheckedDatas();
                        this.host.removeAllListen();
                        this.refresh();
                    }
                });
                break;
            }
            case ToolbarIndex.Open: {
                this.menuAction.openFile()
                .then((res) => {
                    if (res) {
                        this.host.removeAllListen();
                        this.openFileRef.current.click();
                    }
                });

                break;
            }
            case ToolbarIndex.Image: {
                this.insertImageRef.current.click();

                break;
            }
            case ToolbarIndex.InsertEditableImage: {
              getImageEditor().then(editor => {
                editor.create();
                editor.onsave = (json, url, {width, height}) => {

                  const computeScale = (width: number, height: number): number => {
                      const maxWidth = this.host.state.documentCore.getMaxWidth(true);
                      const maxHeight = this.host.state.documentCore.getMaxHeight(true);
                      let scale = 1;
                      if(width > maxWidth || height > maxHeight) {
                        if(width/height >= maxWidth/maxHeight) {
                          scale = maxWidth/width;
                        } else {
                          scale = maxHeight/height;
                        }
                      }
                      return scale;
                  }
                  const scale = computeScale(width, height);

                  this.host.state.documentCore.addInlineImage(width*scale, height*scale, url, '', EquationType.EditableSvg, json);
                  this.refresh();
                };
              });
              break;
            }

            case ToolbarIndex.DesignMode: {
                documentCore.designTemplet(!documentCore.isDesignModel());
                this.refresh();
                break;
            }
            case ToolbarIndex.Normal: {
                const res = documentCore.setEditMode(EditModeType.Normal);
                if (res === ResultType.Success) {
                    this.exitFinalRevision();
                    this.refresh();
                }
                return;
            }
            case ToolbarIndex.Protected: {
                // const activeIndex = this.checkedDatas.findIndex((index) => index === ToolbarIndex.Protected);
                // this.menuAction.setProtected(activeIndex === -1);
                // this.refresh();
                const res = this.documentCore.setEditMode(EditModeType.Protected);
                if (res === ResultType.Success) {
                    this.refresh();
                }
                return;
            }

            case ToolbarIndex.Save: {
                this.menuAction.generate();

                break;
            }

            case ToolbarIndex.InsertFile: {
                // tslint:disable-next-line: newline-per-chained-call
                if (!this.host.getDocumentCore().isProtectedMode()) {
                    this.menuAction.openFile(true)
                    .then((res) => {
                        if (res) {
                            this.insertFileRef.current.click();
                        }
                    });
                }

                break;
            }

            case ToolbarIndex.BreakPage: {
                this.menuAction.addPageBreak()
                .then((res) => {
                    if (res) {
                        // TODO： 这里要改，执行了三次刷新，暂时使用
                        // setTimeout(() => {
                        //     const dom = this.pageListDom || document.querySelector('.ReactVirtualized__Grid.ReactVirtualized__List');
                        //     if (dom) {
                        //         this.pageListDom = dom;
                        //         dom.scrollTop = (this.host.pageIndex) * 1000;
                        //     }
                        //     this.host.handleRefresh(10);
                        // }, 0);
                    }
                });

                break;
            }

            case ToolbarIndex.ApoToZip: {
                this.apoToZipRef.current.click();
                break;
            }

            case ToolbarIndex.ZipToApo: {
                this.zipToApoRef.current.click();
                break;
            }

            case ToolbarIndex.FormatFlag: {
                this.menuAction.setShowParaEnd();
                this.refresh();
                return;
            }

            case ToolbarIndex.ExportHtml: {
                const html = new ExportHtml(this.host);
                html.export();
                return;
            }

            case ToolbarIndex.StartTrackRevisions: {
                const activeIndex = this.checkedDatas.findIndex((index) => ToolbarIndex.StartTrackRevisions === index);
                this.menuAction.setTrackRevsions(-1 < activeIndex);
                this.refresh();
                break;
            }

            case ToolbarIndex.FinalRevisions: {
                const activeIndex = this.checkedDatas.findIndex((index) => ToolbarIndex.FinalRevisions === index);
                this.menuAction.setFinalRevisions(-1 < activeIndex);
                this.refresh();
                break;
            }

            case ToolbarIndex.StartRecord: {
              this.menuAction.startRecord();
              this.setRecordReplayMenuItemsStatus();
              this.refresh();
              break;
            }

            case ToolbarIndex.StopRecord: {
              this.menuAction.stopRecord();
              this.setRecordReplayMenuItemsStatus();
              this.refresh();
              break;
            }

            case ToolbarIndex.ExportRecord: {
              this.menuAction.exportRecord();
              break;
            }

            case ToolbarIndex.StartReplay: {
              const onFramePlay = () => this.refresh();
              const onFinished = () => console.log('finished');
              const onCanceled = () => console.log('cancel');
              this.menuAction.startReplay(onFramePlay, onFinished, onCanceled);
              this.setRecordReplayMenuItemsStatus();
              break;
            }

            case ToolbarIndex.StopReplay: {
              this.menuAction.stopRecord();
              this.setRecordReplayMenuItemsStatus();
              this.refresh();
              break;
            }

            case ToolbarIndex.StatusCode: {
                //   alert(this.host.getEditor().getFileMd5());
                this.host.getEditor()
                .getFileMd5()
                .then((file) => {
                    message.error(file);
                });
                break;
            }

            case ToolbarIndex.BreakPageView: {
                const res = this.documentCore.setViewMode(ViewModeType.BreakPageView);
                if (res === ResultType.Success) {
                    this.refresh();
                }
                return;
            }

            case ToolbarIndex.WebView: {
                const res = this.documentCore.setViewMode(ViewModeType.WebView);
                if (res === ResultType.Success) {
                    this.refresh();
                }
                return;
            }

            case ToolbarIndex.StrictMode: {
                // const activeIndex = this.checkedDatas.findIndex((index) => ToolbarIndex.StrictMode === index);
                // this.menuAction.setStrictMode(-1 < activeIndex);
                // this.refresh();
                this.exitFinalRevision();
                const res = this.documentCore.setEditMode(EditModeType.StrictMode);
                if (res === ResultType.Success) {
                    this.refresh();
                }
                return;
            }

            case ToolbarIndex.TableCellName: {
                const activeIndex = this.checkedDatas.findIndex((index) => ToolbarIndex.TableCellName === index);
                this.menuAction.setShowTableCellNames(-1 < activeIndex);
                this.refresh();
                return;
            }

            case ToolbarIndex.IncrementSave: {
                // const data = this.documentCore.getIncrementDatas();
                // if ( data ) {
                //     // logger.open(this.host.id);
                //     // logger.info({id: this.documentCore.getCurrentId(),
                //     //     description: 'IncrementSave', result: data});
                //     incrementDB.insertItem(101, data);
                // }
                return;
            }

            case ToolbarIndex.IncrementLoad: {
                // logger.open(this.host.id);
                // const items = incrementDB.getItem(101); // logger.getItem();

                // if ( items ) {
                //     let data;
                //     items.then((item1) => {
                //         data = item1.value;

                //         if ( this.documentCore.loadIncrementDatas(JSON.parse(data)) ) {
                //             this.refresh();
                //         }
                //     });
                // }
                return;
            }

            default: {
                this.menuAction.handleEditMenu(item.index);
                break;
            }

        }
        gEvent.setEvent(this.host.docId, gEventName.DialogEvent, item.index);
    }

    private setRecordReplayMenuItemsStatus(): void {
      const rrState = getDocumentCoreRecordReplayState();
      const rrMenuItems = this.datas.find((item) => item.index === ToolbarIndex.Tool)
        .children.find((item) => item.index === ToolbarIndex.Auto).children
        .filter((data: IBar) => data.index !== ToolbarIndex.AutoTestPlay);

      if (rrState.isInit || rrState.isReplayFinished) {
        rrMenuItems.forEach((item) => {
          if ([ToolbarIndex.StartRecord, ToolbarIndex.StartReplay, ToolbarIndex.StatusCode].includes(item.index)) {
            item.disabled = false;
          } else {
            item.disabled = true;
          }
        });
      }

      if (rrState.isRecording) {
        rrMenuItems.forEach((item) => {
          if ([ToolbarIndex.StopRecord].includes(item.index)) {
            item.disabled = false;
          } else {
            item.disabled = true;
          }
        });
      }

      if (rrState.isRecordFinished) {
        rrMenuItems.forEach((item) => {
          if ([ToolbarIndex.StartRecord, ToolbarIndex.ExportRecord,
                ToolbarIndex.StartReplay, ToolbarIndex.StatusCode].includes(item.index)) {
            item.disabled = false;
          } else {
            item.disabled = true;
          }
        });
      }

      if (rrState.isReplaying) {
        rrMenuItems.forEach((item) => {
          if ([ToolbarIndex.StopReplay].includes(item.index)) {
            item.disabled = false;
          } else {
            item.disabled = true;
          }
        });
      }

    }

    private onMouseLeave = (): void => {
        clearTimeout(this.timeout);
        this.timeout = setTimeout(() => {
            this.setState({activeIndex: -1});
        }, 300);
    }

    private onMouseEnter2 = (item: any, e: any): void => {
        const index = item.index;
        const data: IBar = this.datas[index];
        switch (index) {
            case ToolbarIndex.Cut:
            case ToolbarIndex.Copy: {
                item.disabled = !this.menuAction.isActiveCopy();
                break;
            }
            case ToolbarIndex.Redo: {
                item.disabled = !this.menuAction.isActiveRedo();
                break;
            }
            case ToolbarIndex.Undo: {
                item.disabled = !this.menuAction.isActiveUndo();
                break;
            }
            case ToolbarIndex.EditMode: {
                const editMode = this.menuAction.getEditMode();
                const arr = [ToolbarIndex.Normal, ToolbarIndex.Protected, ToolbarIndex.StrictMode];
                const checkedDatas = this.checkedDatas;
                const curIndex = checkedDatas.findIndex((myIndex) => arr.includes(myIndex));
                const activeIndex = this.editModeObj[editMode];
                if (curIndex > -1) {
                    checkedDatas[curIndex] = activeIndex;
                } else {
                    checkedDatas.push(activeIndex);
                }
                break;
            }
            case ToolbarIndex.ViewMode: {
                const viewMode = this.menuAction.getViewMode();
                const arrrs = [ToolbarIndex.BreakPageView, ToolbarIndex.WebView];
                const checkedDatas = this.checkedDatas;
                const curIndex = checkedDatas.findIndex((myIndex) => arrrs.includes(myIndex));
                const activeIndex = this.viewModeObj[viewMode];
                if (curIndex > -1) {
                    checkedDatas[curIndex] = activeIndex;
                } else {
                    checkedDatas.push(activeIndex);
                }
                break;
            }
            case ToolbarIndex.TableProps: {
                item.disabled = !this.menuAction.isInTable();
                break;
            }
            // case ToolbarIndex.Revision: {
            //     const bTrackRevision = this.menuAction.isTrackRevisions();
            //     // const activeIndex = this.checkedDatas.findIndex((index) => ToolbarIndex.StartTrackRevisions === index);
            //     this.menuAction.setTrackRevsions(bTrackRevision);
            //     const bFinalRevision = this.menuAction.isFinalRevision();
            //     this.menuAction.setFinalRevisions(bFinalRevision);
            //     break;
            // }
            default:
                return;
        }

        this.setState({});
    }

    private onMouseEnter = (index: number, e: any): void => {
        clearTimeout(this.timeout);
        const data: IBar = this.datas[index];
        if (data.index === ToolbarIndex.Edit) {
        //     const flag = this.menuAction.getProtected();
        //     const activeIndex = this.checkedDatas.findIndex((item) => item === ToolbarIndex.Protected);
        //     if (flag) {
        //         if (activeIndex === -1) {
        //             this.checkedDatas.push(ToolbarIndex.Protected);
        //         }
        //     } else {
        //         if (activeIndex > -1) {
        //             this.checkedDatas.splice(activeIndex, 1);
        //         }
        //     }
        // } else if (data.index === ToolbarIndex.Edit) {
            const active = data.children.find((child) => child.index === ToolbarIndex.Paste);
            this.menuAction.isActivePaste((flag) => {
                active.disabled = !flag;
                this.setState({});
            });
        }
        this.setState({activeIndex: index});
    }

    private itemClick = (e: any): void => {
        const res = this.getLiDom(e.target);
        if (!res) {
            return;
        }

        const dataIndex = res.getAttribute('data-index');
        if (!dataIndex) {
            return;
        }
        const arrs = dataIndex.split(',');
        if (arrs.length === 1) {
            return;
        }
        // const index = +arrs[arrs.length - 1];
        // if (index === this.state.activeIndex) {
        //     return;
        // }
        const data = this.getActiveItem(arrs);
        if (!data) {
            return;
        }

        if (data.disabled === true) {
            return;
        }

        if (data.children) {
            return;
        }

        if ( ToolbarIndex.StartTrackRevisions === data.index ) {
            const flag = this.menuAction.isTrackRevisions();
            const activeIndex = this.checkedDatas.findIndex((item) => (ToolbarIndex.StartTrackRevisions === item));
            if (flag) {
                this.checkedDatas.splice(activeIndex, 1);
            } else {
                this.checkedDatas.push(data.index);
            }
        } else if ( ToolbarIndex.FinalRevisions === data.index ) {
            const flag = this.menuAction.isFinalRevision();
            const activeIndex = this.checkedDatas.findIndex((item) => (ToolbarIndex.FinalRevisions === item));
            if (flag) {
                this.checkedDatas.splice(activeIndex, 1);
            } else {
                this.checkedDatas.push(data.index);
            }
        } else if ( ToolbarIndex.TableCellName === data.index ) {
            const flag = this.menuAction.isShowTableCellNames();
            const activeIndex = this.checkedDatas.findIndex((item) => (ToolbarIndex.TableCellName === item));
            if (flag) {
                this.checkedDatas.splice(activeIndex, 1);
            } else {
                this.checkedDatas.push(data.index);
            }
        }

        // if (res.className.indexOf('first') > -1) {
        //     this.setState({activeIndex: +arrs[0]});
        // }
        IFRAME_MANAGER.setDocId(this.host.docId);
        this.setItemEvent(data);
        this.setState({activeIndex: -1});
    }

    // private docClickEvent = (): void => {
    //     if (this.bItemClick === true) {
    //         this.bItemClick = false;
    //         return;
    //     }

    //     if (this.state.activeIndex !== -1) {
    //         this.setState({activeIndex: -1});
    //     }
    // }

    private addEvent(): void {
        if (!this.ulRef.current) {
            return;
        }

        this.ulRef.current.addEventListener('click', this.itemClick);
        // document.addEventListener('click', this.docClickEvent);
    }

    private deleteEvent(): void {
        if (!this.ulRef.current) {
            return;
        }

        this.ulRef.current.removeEventListener('click', this.itemClick);
        // document.removeEventListener('click', this.docClickEvent);
    }

    private clearCheckedDatas = (): void => {
        this.checkedDatas = [];
    }

    private exitFinalRevision(): void {
        const activeIndex = (this.checkedDatas ?
            this.checkedDatas.findIndex((pos) => ToolbarIndex.FinalRevisions === pos) : -1);
        if ( -1 < activeIndex ) {
            this.checkedDatas.splice(activeIndex, 1);
            this.menuAction.setFinalRevisions(false);
        }
    }
}

toolbars = [
    {
        name: '文件',
        index: ToolbarIndex.File,
        children: [
            {
                name: '新建',
                index: ToolbarIndex.NewFile,
            },
            {
                name: '打开',
                index: ToolbarIndex.Open,
            },
            {
                name: '保存',
                index: ToolbarIndex.Save,
            },
            {
                name: '页面设置',
                index: ToolbarIndex.PageSet,
            },
            {
                name: '打印预览',
                index: ToolbarIndex.ReviewPrint,
            },
            {
                name: '打印',
                index: ToolbarIndex.Print,
            },
            {
                name: '导出html',
                index: ToolbarIndex.ExportHtml,
            },
            // {
            //     name: 'C端打印',
            //     index: ToolbarIndex.CPrint,
            // },
        ],
    },
    {
        name: '编辑',
        index: ToolbarIndex.Edit,
        initChildCheck: true,
        children: [
            {
                name: '撤销',
                index: ToolbarIndex.Undo,
                disabled: true,
            },
            {
                name: '重做',
                index: ToolbarIndex.Redo,
                disabled: true,
            },
            {
                name: '复制',
                index: ToolbarIndex.Copy,
                disabled: true,
            },
            {
                name: '粘贴',
                index: ToolbarIndex.Paste,
            },
            {
                name: '剪切',
                index: ToolbarIndex.Cut,
                disabled: true,
            },
            {
                name: '全选',
                index: ToolbarIndex.SelectAll,
            },
            {
                name: '编辑模式',
                index: ToolbarIndex.EditMode,
                children: [
                    {
                        name: '正常',
                        index: ToolbarIndex.Normal,
                        checked: true,
                    },
                    {
                        name: '只读',
                        index: ToolbarIndex.Protected,
                        checked: false,
                    },
                    {
                        name: '仅元素可编辑',
                        index: ToolbarIndex.StrictMode,
                        checked: false,
                    },
                ]
            },
        ],
    },
    {
        name: '视图',
        index: ToolbarIndex.View,
        children: [
            {
                name: '缩放',
                index: ToolbarIndex.ViewScale,
            },
            {
                name: '显示格式标记',
                index: ToolbarIndex.FormatFlag,
            },
            {
                name: '页眉页脚',
                index: ToolbarIndex.HeaderFooter,
            },
            {
                name: '导航面板',
                index: ToolbarIndex.NavMenu,
            },
            // {
            //     name: '属性面板',
            //     index: ToolbarIndex.AttributePanel,
            // },
            {
                name: '视图模式',
                index: ToolbarIndex.ViewMode,
                initChildCheck: true,
                children: [
                    {
                        name: '分页视图',
                        index: ToolbarIndex.BreakPageView,
                        checked: true,
                    },
                    {
                        name: 'Web视图',
                        index: ToolbarIndex.WebView,
                        checked: false,
                    }
                ]
            },
        ],
    },
    {
        name: '插入',
        index: ToolbarIndex.Insert,
        children: [
            {
                name: '结构化元素',
                index: ToolbarIndex.Struct,
                children: [
                    {
                        name: '文本框',
                        index: ToolbarIndex.TextBox,
                    },
                    {
                        name: '数值框',
                        index: ToolbarIndex.NumberBox,
                    },
                    {
                        name: '复选框',
                        index: ToolbarIndex.Checkbox,
                        disabled: false,
                    },
                    {
                        name: '单选按钮',
                        index: ToolbarIndex.Radio,
                        disabled: false,
                    },
                    {
                        name: '单选/多选框',
                        index: ToolbarIndex.SingleOrMultipleBox,
                    },
                    {
                        name: '日期框',
                        index: ToolbarIndex.DateBox,
                    },
                    {
                        name: '区域',
                        index: ToolbarIndex.Region,
                    },
                    {
                        name: '签名控件',
                        index: ToolbarIndex.SignatureBox,
                    },
                ],
            },
            {
                name: '分页符',
                index: ToolbarIndex.BreakPage,
            },
            {
                name: '图片',
                index: ToolbarIndex.Image,
            },
            {
               name: '可编辑图片',
               index: ToolbarIndex.InsertEditableImage,

            },
            {
                name: '医学公式',
                index: ToolbarIndex.MedEquation,
            },
            {
                name: '特殊字符',
                index: ToolbarIndex.SpecialCharacter,
            },
            {
                name: '文件',
                index: ToolbarIndex.InsertFile,
            },
            {
                name: '页码',
                index: ToolbarIndex.PageNum,
            },
            {
                name: '水印',
                index: ToolbarIndex.WaterMark,
            }
        ],
    },
    {
        name: '格式',
        index: ToolbarIndex.Format,
        children: [
            {
                name: '字符',
                index: ToolbarIndex.Char,
            },
            {
                name: '段落',
                index: ToolbarIndex.Paragraph,
            },
        ],
    },
    {
        name: '工具',
        index: ToolbarIndex.Tool,
        children: [
            {
                name: '修订',
                index: ToolbarIndex.Revision,
                children: [
                    {
                        name: '修订设置',
                        index: ToolbarIndex.RevisionSetting,
                    },
                    {
                        name: '开启修订',
                        index: ToolbarIndex.StartTrackRevisions,
                        checked: false,
                        disabled: false,
                    },
                    {
                        name: '接受拒绝修订',
                        index: ToolbarIndex.RevisionAcceptReject,
                        disabled: false,
                    },
                    {
                        name: '最终修订状态',
                        index: ToolbarIndex.FinalRevisions,
                        checked: false,
                    },
                    {
                        name: '审阅窗格',
                        index: ToolbarIndex.ReviewPanel,
                        disabled: false,
                    },
                ],
            },
            {
                name: '设计模式',
                index: ToolbarIndex.DesignMode,
                // disabled: true,
            },
            {
              name: '自动化',
              index: ToolbarIndex.Auto,
              children: [
                {
                  name: '开始录制',
                  index: ToolbarIndex.StartRecord,
                },
                {
                  name: '结束录制',
                  index: ToolbarIndex.StopRecord,
                },
                {
                  name: '导出',
                  index: ToolbarIndex.ExportRecord,
                },
                {
                  name: '开始回放',
                  index: ToolbarIndex.StartReplay,
                },
                {
                  name: '结束回放',
                  index: ToolbarIndex.StopReplay,
                },
                {
                  name: '状态码',
                  index: ToolbarIndex.StatusCode,
                },
                {
                    name: 'ID回放',
                    index: ToolbarIndex.AutoTestPlay,
                },
              ],
            },
            {
                name: '增量保存',
                index: ToolbarIndex.IncrementSave,
                // disabled: true,
            },
            {
                name: '增量Load',
                index: ToolbarIndex.IncrementLoad,
                // disabled: true,
            },
        ],
    },
    {
        name: '表格',
        index: ToolbarIndex.Table,
        children: [
            {
                name: '插入表格',
                index: ToolbarIndex.InsertTable,
            },
            {
                name: '表格属性',
                index: ToolbarIndex.TableProps,
                disabled: false,
            },
            {
                name: '显示单元格名称',
                index: ToolbarIndex.TableCellName,
                checked: false,
                disabled: false,
            },
        ],
    },
    {
        name: '帮助',
        index: ToolbarIndex.Helper,
        children: [
            {
                name: '文字输入',
                index: ToolbarIndex.InputChart,
            },
            {
                name: 'apo转为zip',
                index: ToolbarIndex.ApoToZip,
            },
            {
                name: 'zip转为apo',
                index: ToolbarIndex.ZipToApo,
            },
        ],
    },
];
