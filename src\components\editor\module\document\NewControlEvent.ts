import {Document} from './Document';
import '../../style/newControl.less';
import {GlobalEvent as gEvent, GlobalEventName as gEventName} from '../../../../common/GlobalEvent';
import { DocumentCore } from '../../../../model/DocumentCore';
import MouseEventHandler from '../../../../common/MouseEventHandler';
import { RenderSectionBackgroundType, NewControlErrorInfo,
    getPageElement,
    INewControlTips,
    ViewModeType,
    MessageType,
    NewControlType,
    ResultType,
    numtoFixed2,
    MonitorEvent,
    getDateRangeError,
    } from '../../../../common/commonDefines';
import { message } from '../../../../common/Message';
import { IFRAME_MANAGER } from '../../../../common/IframeManager';
import { MessageTip } from '../../../../common/MessageTip';
import { getTheme } from '@hz-editor/theme';
import { NewControlDate } from '../../../../model/core/NewControl/NewControlDate';
import { consoleLog, isGlobalTestData } from '../../../../common/GlobalTest';
import { getPagePadding } from '../../../../common/commonMethods';
import { Region } from '@/model/core/Region';

enum CursorClass {
    Pointer = 'pointer',
    NotAllowed = 'not-allowed',
    Auto = 'auto',
}

interface IRegionBorderLine {
    x: number;
    y: number;
    pageIndex: number;
    bStart?: boolean;
    bEnd?: boolean;
    width?: number;
}

// let date1: Date;
export default class NewControlEvent {
    private host: Document;
    private docId: number;
    private gMouseEvent: MouseEventHandler;
    private documentCore: DocumentCore;
    private newControlsCursorInBounds: any;
    private newControlsMouseFocusBounds: any;
    private curFocusNewControlName: any;
    // private newControlTips: any;
    private viewCursorPoint: number[] = [];
    private isShowNewControlTips: boolean;
    // private bShowNewControlTip: boolean;
    // private curInNewControlName: string;
    // private curNewcontrolName: string;
    private newControl: any;

    // private tipDom: HTMLDivElement;
    // private bCursorChange: boolean;
    // private activeBgContainer: object;
    // private bgContainer: object;
    private bFromMouseDown: boolean;
    // private timer: any;
    // private timerout: any;
    // private timer: any;
    private timerout: any;
    private bCursorHidden: boolean;
    // private container: HTMLDivElement;
    private bActiveClass: boolean;
    private region: any;
    private tips: INewControlTips;
    private prevCursorInName: string;
    private regionMaxLengthDom: HTMLDivElement;
    private startTimeout: any;
    private clearTimeout: any;
    private messageTip: MessageTip;
    private messageTimeout: any;
    private messageTimeout2: any;
    private messageTimeout3: any;
    private oldRegionBorderLines: IRegionBorderLine[];
    private newTips: INewControlTips;
    private bNewControlTip: boolean;
    private bOldNewControlTip: boolean;
    private regionContentChangeTimeout: any;
    private regionMessageOption: any;
    private moveNewControlName: string;
    private downNewControl: any;

    private subSignTips: INewControlTips;

    // 绑定只读模式属性
    private showSignHelptips: boolean;
    private bTableMoveing: boolean;
    private _btnDom: any;

    // private viewMode: ViewModeType;
    constructor(host: Document) {
        this.host = host;
        this.docId = host.docId;
        this.documentCore = host.documentCore;
        this.addEvents();
        this.gMouseEvent = new MouseEventHandler();
        // this.bCursorChange = true;
        // this.bgContainer = {};
        // this.activeBgContainer = {};
        this.regionMaxLengthDom = null;
    }

    public clearDatas(): void {
        this.region = null;
        this.regionMaxLengthDom = null;
        this.messageTip = null;
        this.regionMessageOption = null;
        this.newControl = null;
        this.downNewControl = null;
        if (this._btnDom) {
            this._btnDom.removeEventListener('click', this.addRegionClick);
            this._btnDom = null;
        }
    }

    public renderBackground(position: any): void {
        if (!position) {
            return;
        }
        this.cursorChange(position);
        this.renderRegionBorder(this.region);
    }

    public isCursorHidden(): boolean {
        return this.bCursorHidden;
    }

    public onScroll(): void {
        this.showTipContent();
        this.renderRegionBorder(this.region, true);
    }

    public setRegionDirty(): void {
        if (!this.region) {
            return;
        }

        this.region.setDirty();
    }

    public clearTipFlag(): void {
        if (!this.newTips) {
            return;
        }

        const downNewControl = this.downNewControl;
        if (!downNewControl) {
            this.hideTipContent();
            return;
        }

        const newControl = this.documentCore.getCurrentNewControl();
        if (!newControl) {
            this.hideTipContent();
            return;
        }
        if (newControl.getNewControlName() !== downNewControl.getNewControlName()) {
            this.hideTipContent();
        }
    }

    public setMouseDown(bFromMouseDown: boolean): void {
        this.bFromMouseDown = bFromMouseDown;
    }

    private handleMouseDown = (e: any): void => {
        // this.bFromMouseDown = true;
        const pageNode = getPageElement(e.target);
        if (!pageNode || (this.documentCore.isProtectedMode() && !this.showSignHelptips)) {
            this.newControl = undefined;
            this.clearHightLightNewControl();
            return;
        }
        const documentCore = this.documentCore;
        const scale = this.host.getScale();
        const position = getPagePadding(this.documentCore);
        let pointX = e.offsetX / scale + position.left;
        const x = pointX;
        const pointY = e.offsetY / scale + position.top;
        // this.viewMode = undefined;

        pointX = documentCore.getCursorStateInDocument(pointX, pointY, this.host.getPageIndex()).pointX;

        this.gMouseEvent.pointX = pointX;
        this.gMouseEvent.pointY = pointY;
        this.gMouseEvent.x = e.clientX;
        this.gMouseEvent.y = e.clientY;

        const newControl = documentCore.getMouseDownControl(this.gMouseEvent, this.host.getPageIndex());
        if (newControl) {
            this.curFocusNewControlName = newControl.getNewControlName();
            this.renderFocusHightLightNewControl();
            if (!newControl.isCheckBox() || !newControl.isMultiAndRadio()) {
                this.setCursorHidden(false);
            }
            // const tips: any = this.tips;
            // if (tips) {
            //     tips.content = newControl.getTipsContent();
            // }
        } else {
            this.curFocusNewControlName = null;
            this.hideNewControlTipContent();
            this.setCursorHidden(false);
        }
        this.downNewControl = newControl;

        this.hideSubSignTipContent();
        // setTimeout(() => {
        //     this.showNewControlOuterLayer(newControl, e);
        // }, 0);
    }

    private handleMouseMove = (event: any): void => {
        const pageNode = getPageElement(event.target);
        if (!pageNode || (this.documentCore.isProtectedMode() && !this.showSignHelptips)) {
            this.clearFocusNewControl();
            return;
        }

        // date1 = new Date();
        const pageId = this.host.getCurPageIndex();
        const scale = this.host.getScale();
        const position = getPagePadding(this.documentCore);
        const offsetY = event.offsetY / scale + position.top;
        this.viewCursorPoint[0] = event.pageX + position.left;
        this.viewCursorPoint[1] = offsetY;

        // update gMouseEvent class
        this.gMouseEvent.pointX = event.offsetX / scale + position.left;
        this.gMouseEvent.pointY = offsetY;
        const documentCore = this.documentCore;

        const bPrint = this.host.isPrint();
        const cursorState = !bPrint ? documentCore.getCursorStateInDocument(this.gMouseEvent.pointX , offsetY, pageId)
                            : null;
        this.gMouseEvent.pointX = cursorState ? cursorState.pointX : this.gMouseEvent.pointX;

        // 非文本选择中，光标在同一位置持续3秒
        const newControlName: string = this.moveNewControlName
        = documentCore.getFocusNewControlName(this.gMouseEvent, pageId);
        // tslint:disable-next-line: no-conditional-assignment
        if (newControlName) {
            const cursorInNewControlName = documentCore.getCursorInNewControlName();
            if (newControlName !== cursorInNewControlName) {
                const newControl = documentCore.getNewControlByName(newControlName);
                const parent = newControl ? newControl.getParent() : null;

                if (newControl && newControl.isNewTextBox() && newControl.getTipsContent()
                    && parent && parent.isSignatureBox()) {
                    const newPosition = newControl.getPosition(MessageType.SubSignTips);
                    this.curFocusNewControlName = newControlName;
                    this.subSignTips = {left: newPosition.x,
                        top: newPosition.y,
                        content: newControl.getTipsContent(),
                        pageIndex: pageId};
                    if (this.documentCore.isProtectedMode()) {
                        // 只读模式下控制签名控件提示信息
                        if (this.showSignHelptips) {
                            this.showSubSignTipContent();
                        }
                    } else {
                        this.showSubSignTipContent();
                    }
                } else {
                    
                    this.hideSubSignTipContent();
                }
            }
            this.showRegExpText(newControlName, this.gMouseEvent, 900);
        } else {
            this.hideSubSignTipContent();
        }

        // const isFocusOnNewControl = documentCore.isFocusOnNewControl(this.gMouseEvent, pageId);
        if (newControlName) { // } || true === documentCore.isCursorInNewControl()) {
            this.gMouseEvent.x = event.clientX;
            this.gMouseEvent.y = event.clientY;
            if (!this.documentCore.isProtectedMode()) {
                this.renderFocusHightLightNewControl(this.gMouseEvent, pageId);
            }
        } else {
            this.clearFocusNewControl();
            if ( !documentCore.isCursorInNewControl()) {
                this.clearCursorInNewControlPath();
            }
            clearTimeout(this.messageTimeout);
            // this.clearHightLightNewControl();
        }

        this.setNewControlCursor(event, newControlName, (cursorState && cursorState.result));
        if (!newControlName) {
            const res = this.documentCore.getReadonlyOptionByXY(this.gMouseEvent, pageId);
            if (res && res.isReadonly === true) {
                if (this.bActiveClass !== true) {
                    const dom = this.getContainer();
                    // 光标样式
                    switch (getTheme()?.NewControl?.MouseStyleInUneditableStruct) {
                        case 1: {
                            dom.className = ' cursor-' + CursorClass.Pointer;
                            break;
                        }
                        case 2: {
                            dom.className = ' cursor-' + CursorClass.Auto;
                            break;
                        }
                        default: {
                            if (false !== getTheme()?.NewControl?.EnableNotAllowedCursorStyle) {
                                dom.className = ' cursor-' + CursorClass.NotAllowed;
                            }
                            break;
                        }
                    }
                    this.bActiveClass = true;
                }
            } else {
                if (this.bActiveClass !== false) {
                    this.removeCursorClass();
                }
            }
        }
    }

    private handleMouseUp = (e: any): void => {
        const documentCore = this.documentCore;
        this.bTableMoveing = documentCore.isTableBorder2();
        const bSelected = documentCore.isSelectionUse();

        if (this.bTableMoveing !== true) {
            this.newControlChange(this.downNewControl);
            this.checkBoxCursorHidden();
            this.getCurrentRegion();
        }

        if ( !bSelected && this.bTableMoveing !== true) {
            this.showTips();
            this.showNewControlOuterLayer(this.newControl, e);
        } else if (!bSelected) {
            this.showTips();
        } else if (bSelected) {
            this.hideTipContent();
        }
        documentCore.resetTableBorder2();
    }

    /**
     * 设置结构化元素的光标
     * @param e 当前事件
     */
    private setNewControlCursor(e: any, newControlName: string, bShowRegionMes: boolean): void {
        let className: string;
        const region = this.documentCore.getFoucsInRegion(this.gMouseEvent, this.host.getPageIndex());

        if ( bShowRegionMes && region) {
            // this.showRegionMaxLengthDom(e, region);
            const scale = this.host.getScale();
            const postion: any = this.regionMessageOption = {};
            postion.x = e.clientX;
            postion.y = e.clientY;
            postion.newControl = region;
            if (!this.moveNewControlName) {
                this.showErrorMessageInfo(undefined, postion);
            }
        }

        if (!newControlName && !this.documentCore.isStrictMode2()) {
            // const region = this.documentCore.getFoucsInRegion(this.gMouseEvent, this.host.getPageIndex());
            if (!region || region.isReadOnly() !== true) {
                this.removeCursorClass();
                return;
            }

            // 光标样式
            switch (getTheme()?.NewControl?.MouseStyleInUneditableStruct) {
                case 1: {
                    className = ' cursor-' + CursorClass.Pointer;
                    break;
                }
                case 2: {
                    className = ' cursor-' + CursorClass.Auto;
                    break;
                }
                default: {
                    if (false !== getTheme()?.NewControl?.EnableNotAllowedCursorStyle) {
                        className = ' cursor-' + CursorClass.NotAllowed;
                    }

                    break;
                }
            }
        }
        this.removeCursorClass();

        if (!className) {
            className = this.getCursorClass(newControlName);
            if (!className) {
                return;
            }
        }

        const dom = this.getContainer();
        if (dom.className.indexOf(className) > -1) {
            return;
        }
        dom.className += className;
        this.bActiveClass = true;
    }

    private getCursorClass(newControlName: string): any {
        if (false === getTheme()?.NewControl?.EnableNotAllowedCursorStyle) {
            return;
        }

        const className = ' cursor-';
        const bProtect = this.documentCore.isProtectedMode();
        if (bProtect) {
            return className + CursorClass.NotAllowed;
        }
        const newControl = this.documentCore.getNewControlByName(newControlName);
        if (newControl && newControl.isReadOnly()) {
            switch (getTheme()?.NewControl?.MouseStyleInUneditableStruct) {
                case 1:
                    return className + CursorClass.Pointer;
                case 2:
                    return className + CursorClass.Auto;
                default:
                    return className + CursorClass.NotAllowed;
            }
            // return className + CursorClass.NotAllowed;
        }

        if ( this.documentCore.isStrictMode2() ) {
            if ( newControl ) {
                return className + CursorClass.Auto;
            }
            const region = this.documentCore.getFoucsInRegion(this.gMouseEvent, this.host.getPageIndex());
            if ( region && !region.isReadOnly() ) {
                return className + CursorClass.Auto;
            }
            switch (getTheme()?.NewControl?.MouseStyleInUneditableStruct) {
                case 1:
                    return className + CursorClass.Pointer;
                case 2:
                    return className + CursorClass.Auto;
                default:
                    return className + CursorClass.NotAllowed;
            }

            // return className + CursorClass.NotAllowed;
        }

        if (newControl && newControl.isPopWindowNewControl()) {
            return className + CursorClass.Pointer;
        }

        return;
    }

    private getContainer(): HTMLDivElement {
        return this.host.getContainer();
    }

    private removeCursorClass(): void {
        if (this.bActiveClass !== true) {
            return;
        }
        const container = this.getContainer();
        container.className = container.className.replace(/\s+cursor-[a-z]+(-[a-z]+)?/g, '');
        this.bActiveClass = false;
    }

    private getCurrentRegion = (): void => {
        const documentCore = this.documentCore;
        const region = documentCore.getCursorInRegion();
        if (region && region === this.region ) {
            const selection = documentCore.getDocumentSelection();
            if (selection && selection.bUse !== true && !this.host.isCusorVisible()) {
                this.host.setCursorVisible(true);
            }
            if (this.bOldNewControlTip) {
                this.bOldNewControlTip = false;
                this.showRegionTipContent(region);
            }
            this.renderRegionBorder(region);
            return;
        }
        this.regionChange(region);
        let name: string;
        if (region) {
            name = region.getName();
            this.region = region;
            this.showRegionTipContent(region);
        } else if (!this.region) {
            this.hideRegionTipContent();
            return;
        } else {
            this.region = undefined;
            this.hideRegionTipContent();
        }

        // documentCore.setRegionActive(name);
        // if (documentCore.getRegionBorderViewType() !== RegionBorderViewType.None) {
        //     this.host.changeDocument();
        // }
    }

    private showRegionTipContent(region: any): void {
        if (this.bNewControlTip !== true) {
            const oldLines = this.oldRegionBorderLines;
            const tipContent = region.getTipsContent();
            if (tipContent && oldLines && oldLines.length > 0) {
                const line = this.oldRegionBorderLines[0];
                this.newTips = {
                    left: line.x,
                    top: line.y - 6,
                    pageIndex: line.pageIndex,
                    content: tipContent,
                    bRegion: true,
                };
                this.showTipContent();
            } else {
                this.hideRegionTipContent();
            }
        }
    }

    private hideRegionTipContent(): void {
        if (this.bNewControlTip === true) {
            return;
        }
        this.hideTipContent();
    }

    private regionChange(newRegion: any): void {
        const oldRegion = this.region;
        if (oldRegion === newRegion) {
            if (this.bOldNewControlTip) {
                this.renderRegionBorder(newRegion);
                this.bOldNewControlTip = false;
            }
            return;
        }

        this.removeRegionBorder();
        this.bOldNewControlTip = false;
        if (newRegion) {
            gEvent.setEvent(this.docId, gEventName.RegionChange, newRegion);
            this.renderRegionBorder(newRegion);
        // } else {
        //     this.removeRegionBorder();
            // regionBorderDom.innerHTML = '';
        }

        if (!newRegion) {
            if (!oldRegion) {
                return;
            }
            this.documentCore.endMonitorElement(oldRegion);
        } else {
            this.documentCore.startMonitorElement(newRegion);
        }

        if (this.host.externalEvent) {
            if (!newRegion) {
                if (!oldRegion) {
                    return;
                }
                this.host.externalEvent.nsoRegionLostFocus(oldRegion.name, oldRegion.getNewControlType());
            } else {
                this.host.externalEvent.nsoRegionGainFocus(newRegion.name, newRegion.getNewControlType());
            }
        }
    }

    private isSameRegionBorder(lines: IRegionBorderLine[], region: any): boolean {
        const oldLines: IRegionBorderLine[] = this.oldRegionBorderLines;
        if (oldLines === undefined) {
            return false;
        }

        if (this.region !== region) {
            return false;
        }

        const length = lines.length;
        if (length !== oldLines.length) {
            return false;
        }

        for (let index = 0; index < length; index++) {
            const line = lines[index];
            const oldLine = oldLines[index];
            if (line.x !== oldLine.x || line.y !== oldLine.y || line.width !== oldLine.width
                || line.pageIndex !== oldLine.pageIndex) {
                return false;
            }
        }

        return true;
    }

    private hideTipContent(): void {
        this.isShowNewControlTips = false;
        if (!this.newTips) {
            return;
        }
        this.newTips = undefined;
        this.bNewControlTip = false;
        const tipDom = this.getContainer()
        .querySelector('.newControl-tips');
        if (!tipDom) {
            return;
        }
        tipDom.outerHTML = '';
    }

    private showTipContent(): void {
        if (this.newTips) {
            this.createTipsDom();
        }
    }

    private createTipsDom(bNewTips: boolean = true): void {
        const tips = (bNewTips ? this.newTips : this.subSignTips);
        const pageDom = this.getContainer()
            .querySelector(`div[page-index="${tips.pageIndex}"]`);

        this.hiddenOtherTips(tips.pageIndex);
        if (!pageDom) {
            return;
        }

        let subTop: number = 0;
        const obj = getPagePadding(this.documentCore);
        const paddingTop = obj.top;
        if (tips.pageIndex === 0 && tips.top - paddingTop < 18) {
            const position = this.host.getCursorPosition();
            if (position) {
                subTop =  20;
                if (tips.bRegion !== true) {
                    subTop += position.y2 - position.y1;
                }
            }
        }

        const scale = this.host.getScale();
        let subLeft = obj.left;
        if (subLeft > 0) {
            subLeft += getPagePadding.PaddingLeft;
        }
        const left = (tips.left - subLeft) * scale  + 'px';
        const top = (tips.top + subTop - paddingTop) * scale  + 'px';
        const className = (bNewTips ? 'newControl-tips' : 'subSign-tips');
        const tipDom = pageDom.querySelector('.' + className) as HTMLDivElement;
        if (!tipDom) {
            const div = document.createElement('div');
            div.className = className;
            div.setAttribute('style', `left: ${left}; top: ${top}`);
            div.innerHTML = `<i></i><span>${tips.content}</span>`;
            pageDom.appendChild(div);
        } else {
            tipDom.style.left = left;
            tipDom.style.top = top;
            tipDom.querySelector('span').innerHTML = tips.content;
            if (tipDom.className.indexOf('active')) {
                tipDom.className = tipDom.className.replace(' active', '');
            }
        }
    }

    private removeRegionBorder(): void {
        if (this.oldRegionBorderLines === undefined) {
            return;
        }
        const dom: HTMLDivElement = this.getContainer();
        if (!dom) {
            return;
        }
        const regionBorders = dom.querySelectorAll('.region-border');
        for (let index = 0, len = regionBorders.length; index < len; index++) {
            const border = regionBorders[index];
            if (border.innerHTML) {
                border.innerHTML = '';
            }
            // border.classList.remove('active');
        }
        this.oldRegionBorderLines = undefined;
        this.removerRegionButton();
    }

    private removerRegionButton() {
        const dom: HTMLDivElement = this.getContainer();
        const regionBtns = dom.querySelectorAll('.region-btn');
        for (let index = 0, len = regionBtns.length; index < len; index++) {
          const btn = regionBtns[index];
          btn.removeEventListener('click', this.addRegionClick);
          btn.classList.remove('actived');
        }
    }

    private renderRegionBorder(newRegion: any, bRefresh?: boolean): void {
        const dom: HTMLDivElement = this.getContainer();
        if (!dom) {
            return;
        }

        if (!newRegion || newRegion.isHidden()) {
            this.removeRegionBorder();
            return;
        }

        const lines: IRegionBorderLine[] = this.documentCore.getRegionBoundLines(newRegion) as any;
        if (!lines || lines.length === 0) {
            if (lines === undefined) { // 区域被删除了
                this.hideTipContent();
            }
            this.removeRegionBorder();
            return;
        }

        if (bRefresh !== true && this.isSameRegionBorder(lines, newRegion)) {
            this.host.keepFocusForJumpTab();
            return;
        }

        // 过滤当前常显区域
        if (!newRegion.getAlwaysShow() && !newRegion.isLoadCache()) {
            const style = `fill: transparent; stroke-width: 1`;
            for (let index = 0, length = lines.length; index < length; index += 2) {
                const line = lines[index];
                const borderDom = dom.querySelector(`div[page-index="${line.pageIndex}"] .region-border`);
                if (!borderDom) {
                    continue;
                }

                let html: string = '';
                const line2 = lines[index + 1];
                const x1 = numtoFixed2(line.x);
                const x2 = numtoFixed2(line2.width);
                const y1 = numtoFixed2(line.y);
                const y2 = numtoFixed2(line2.y);
                html += `<line x1=${x1} y1=${y1} x2=${x1} y2=${y2} style='${style}'/>`;
                html += `<line x1=${x2} y1=${y1} x2=${x2} y2=${y2} style='${style}'/>`;
                if (line.bStart) {
                    html += `<line x1=${x1} y1=${y1} x2=${x2} y2=${y1} style='${style}'/>`;
                }
                if (line2.bEnd) {
                    html += `<line x1=${x1} y1=${y2} x2=${x2} y2=${y2} style='${style}'/>`;
                }
                borderDom.innerHTML = html;
                if (borderDom.classList.contains('active')) {
                    borderDom.classList.remove('active');
                }
            }
        }
        this.oldRegionBorderLines = lines;
        // 渲染区域操作入口
        this.renderRegionButton(newRegion);
    }

    private renderRegionButton(newRegion: Region): void {
        const lines = this.oldRegionBorderLines;
        if (!lines || lines.length === 0) {
          return;
        }
        const dom: HTMLDivElement = this.getContainer();

        // 渲染区域操作入口
        const firstLine = lines[0];
        if (!!firstLine) {
          const btnDom: HTMLElement = this._btnDom = dom.querySelector(`div[page-index="${firstLine.pageIndex}"] .region-btn`);
          if (!!btnDom) {
            const { x, y, width } = firstLine;
            const scale = this.host.getScale();
            const position = getPagePadding(this.documentCore);
            const left = (width * scale - position.left) + 'px';
            const top = (y - position.top) * scale  + 'px';
            btnDom.style.top = top;
            btnDom.style.left = left;
            btnDom.style.transform = `translate(${10 * scale}px, ${10 * (scale - 1)}px) scale(${scale})`;
            btnDom.classList.add('actived');
            btnDom.addEventListener('click', this.addRegionClick);
            this.host.keepFocusForJumpTab();
          }
        }
    }

    private addRegionClick = (): void => {
        if (this.host.externalEvent && this.oldRegionBorderLines.length) {
            const { x, y } = this.oldRegionBorderLines[0];
            this.host.externalEvent?.nsoRegionOperate(x, y);
            this.host.externalEvent?.nsoRegionCustomEvent(this.region.getNewControlName());
        }
    }

    private showNewControlOuterLayer(newControl: any, e: any, event?: number): void {
        if (!newControl || e.button === 2) {
            if (isGlobalTestData()) {
                consoleLog(newControl, event, '右键，结构化不存在触发隐藏');
            }
            gEvent.setEvent(this.docId, gEventName.NewControlToShow);
            return;
        }

        // if readonly, not send type, thus no dropdown
        if ( null != newControl && newControl.isReadOnly()) {
            if (isGlobalTestData()) {
                consoleLog(newControl, event, '只读触发隐藏');
            }
            gEvent.setEvent(this.docId, gEventName.NewControlToShow);
            return;
        }

        const pageNode = getPageElement(e.target);
        if (!pageNode) {
            gEvent.setEvent(this.docId, gEventName.NewControlToShow);
            return;
        }

        // const type = newControl.getType();

        // if (this.curInNewControlName && this.curInNewControlName === newControl.name) {
        //     gEvent.setEvent(this.docId, gEventName.NewControlToShow, type, 0, true);
        //     return;
        // }
        // this.curInNewControlName = newControl.name;
        if (isGlobalTestData()) {
            consoleLog(newControl, event, '正常触发显示');
        }
        gEvent.setEvent(this.docId, gEventName.NewControlToShow, newControl.getType(),
            this.host.getPageIndex(), newControl.getId(), false, event);
    }

    private renderFocusHightLightNewControl = (mouseEvent?: MouseEventHandler, pageIndex?: number,
                                               bRenderTips: boolean = true) => {
        const documentCore = this.documentCore;

        const cursorInNewControlBounds = documentCore.getNewControlsFocusBounds();
        const mouseFocusNewControlBounds = ( null != mouseEvent ?
                                documentCore.getNewControlsFocusBounds(mouseEvent, pageIndex) : null );

        // 同一行有多个newControl，且有newControl跨行，先选中跨行的newControl， 然后再选中不跨行的newControl
        if (this.newControlsCursorInBounds && 0 < this.newControlsCursorInBounds.bounds.length) {
            this.clearSectionBackground(this.newControlsCursorInBounds.bounds,
                RenderSectionBackgroundType.NewControlCursorIn);
        }

        if (this.newControlsMouseFocusBounds
            && 0 < this.newControlsMouseFocusBounds.bounds.length) {
            this.clearSectionBackground(this.newControlsMouseFocusBounds.bounds,
                RenderSectionBackgroundType.NewControlFocus);
        }

        if (mouseFocusNewControlBounds
            && 0 < mouseFocusNewControlBounds.bounds.length) {
            // if (true === this.bShowNewControlTip && true === bRenderTips) {
            //     // this.showNewControlTips(mouseFocusNewControlBounds.newControlName);
            //     // this.curFocusNewControlName = mouseFocusNewControlBounds.newControlName;
            // } else {
            //     // this.curFocusNewControlName = null;
            // }
            // console.log(mouseFocusNewControlBounds)
            // this.curFocusNewControlName = mouseFocusNewControlBounds.newControlName;
        }

        this.newControlsCursorInBounds = cursorInNewControlBounds;
        this.newControlsMouseFocusBounds = mouseFocusNewControlBounds;

        if ((!cursorInNewControlBounds || null === cursorInNewControlBounds.newControlName)
            && (!mouseFocusNewControlBounds || null === mouseFocusNewControlBounds.newControlName)) {
            this.curFocusNewControlName = undefined;
            return;
        }

        if (cursorInNewControlBounds && 0 < cursorInNewControlBounds.bounds.length) {
            this.curFocusNewControlName = cursorInNewControlBounds.newControlName;
            const firstBounds = cursorInNewControlBounds.bounds[0];
            const tips: any = this.tips = {} as any;
            // const scale = this.host.getScale();
            tips.left = firstBounds.x;
            tips.top = firstBounds.y;
            tips.pageIndex = firstBounds.pageIndex;
            tips.newControlName = this.curFocusNewControlName;
            if (mouseFocusNewControlBounds &&
                cursorInNewControlBounds.newControlName === mouseFocusNewControlBounds.newControlName) {
                this.clearSectionBackground(mouseFocusNewControlBounds.bounds,
                    RenderSectionBackgroundType.NewControlFocus);
                this.newControlsMouseFocusBounds = null;
            }

            const newControl = this.documentCore.getNewControlByName(cursorInNewControlBounds.newControlName);
            const bShowSectionBorder = this.host.showSectionBorder();
            if ( newControl && newControl.isNewSection() && bShowSectionBorder) {
                this.renderSectionBackground(cursorInNewControlBounds.bounds,
                    RenderSectionBackgroundType.NewControlCursorIn, cursorInNewControlBounds.color);
            }
        } else {
            this.tips = null;
        }

        if (mouseFocusNewControlBounds && 0 < mouseFocusNewControlBounds.bounds.length) {
            this.showErrorMessageInfo(mouseFocusNewControlBounds.newControlName, mouseEvent);
            if (cursorInNewControlBounds &&
                cursorInNewControlBounds.newControlName === mouseFocusNewControlBounds.newControlName) {
                return;
            }

            const newControl = this.documentCore.getNewControlByName(mouseFocusNewControlBounds.newControlName);
            if (newControl && newControl.isErrorStatus()) {
                return null;
            }

            this.renderSectionBackground(mouseFocusNewControlBounds.bounds,
                RenderSectionBackgroundType.NewControlFocus, mouseFocusNewControlBounds.color);
        }
    }

    private clearHightLightNewControl = () => {
        if (this.newControlsCursorInBounds) {
            const lines = this.newControlsCursorInBounds.bounds;
            if (lines) {
                this.clearSectionBackground(lines, RenderSectionBackgroundType.NewControlCursorIn);
            }
        }

        this.clearFocusNewControl();
        this.newControlsCursorInBounds = null;
    }

    private clearFocusNewControl = () => {
        if (this.newControlsMouseFocusBounds) {
            const lines = this.newControlsMouseFocusBounds.bounds;
            if (lines) {
                this.clearSectionBackground(lines, RenderSectionBackgroundType.NewControlFocus);
            }
        }

        this.newControlsMouseFocusBounds = null;
    }

    private renderSectionBackground(renderLine: any[], type: RenderSectionBackgroundType, color?: string): void {
        if (true) {
            this.renderSectionBackground2(renderLine, type, color);
            return;
        }
        const container = this.host.getContainer();
        if (!container) {
            return;
        }
        const scale = 1; // this.host.getScale();
        const points = [];
        const pageNums = [];

        for (let index = 0, length = renderLine.length; index < length; index++) {
            const item = renderLine[index];
            if (null != item) {
                if ( RenderSectionBackgroundType.NewControlCursorIn === type ) {
                    const i = {x: Math.round(item.x * scale), y: Math.round(item.y * scale), pageIndex: item.pageIndex,
                        height: Math.round(item.height * scale), width: Math.round(item.width * scale)};
                    points.push(i);
                    if ( !pageNums.includes(item.pageIndex) ) {
                        pageNums.push(item.pageIndex);
                    }
                    continue;
                }

                const className = (type === RenderSectionBackgroundType.TableCellSelection) ?
                    `${type}-${item.id}-${item.pageIndex}` : `${type}-${item.line.id}`;
                let tarLine = container.querySelector('.' + className);

                if ( this.documentCore.isInHeaderFooter() ) {
                    tarLine = container.querySelectorAll('.' + className)[item.pageIndex];
                }

                // 选中多页，选中的页面会被替换，tarLine已经不在
                if (undefined !== tarLine && null !== tarLine && RenderSectionBackgroundType.NewControlFocus === type) {
                    tarLine.classList.add('newcontrol-focus');

                    tarLine.setAttribute('x', `${numtoFixed2(item.x * scale)}`);
                    tarLine.setAttribute('width', `${numtoFixed2(item.width * scale)}`);

                    if ( null != color ) {
                      tarLine.setAttribute('style', `fill:${color}`);
                    }
                }
            }
        }

        if ( 0 < points.length && RenderSectionBackgroundType.NewControlCursorIn === type ) {
            this.renderCursorInNewControlPath(points, pageNums, container);
        }
    }

    private clearSectionBackground(renderLine: any[], type: RenderSectionBackgroundType): void {
        if (true) {
            this.clearSectionBackground2(renderLine, type);
            return;
        }
        const container = this.host.getContainer();
        if (!container) {
            return;
        }
        // const pages = container.getElementsByClassName('page-container');
        if ( RenderSectionBackgroundType.NewControlCursorIn === type ) {
            this.clearCursorInNewControlPath(container);
            // tslint:disable-next-line: prefer-for-of
            // for (let index = 0; index < pages.length; index++) {
            //     const page = pages[index];
            //     const paths = page.getElementsByClassName('newcontrol-cursorin-container')[0];
            //     if ( paths ) {
            //         // tslint:disable-next-line: prefer-for-of
            //         for (let index2 = paths.childNodes.length - 1; index2 >= 0; index2--) {
            //             const path = paths.childNodes[index2];
            //             paths.removeChild(path);
            //             // path.removeAttribute('d');
            //         }
            //     }
            // }
        }

        for (let index = 0, length = renderLine.length; index < length; index++) {
            const item = renderLine[index];
            if (null != item) {
                const className = (type === RenderSectionBackgroundType.TableCellSelection) ?
                    `${type}-${item.id}-${item.pageIndex}` : `${type}-${item.line.id}`;
                const selectionCollection = container.querySelectorAll('.' + className);
                // let selectionCollection: any = bgContainer[className];
                // if (!selectionCollection) {
                //     selectionCollection = bgContainer[className] = container.querySelectorAll('.' + className);
                // }

                // If parapage, there would exist two (or more, consider parapage > 2) such nodes
                for (let i = 0, length2 = selectionCollection.length; i < length2; i++) {
                    const elem = selectionCollection[i];
                    if (elem) {
                        if (RenderSectionBackgroundType.ParagragphLineSelection === type
                            || RenderSectionBackgroundType.TableCellSelection === type) {
                            elem.classList.remove('selection-selected');
                        } else if ( RenderSectionBackgroundType.NewControlCursorIn === type ) {
                            elem.classList.remove('newcontrol-cursor');
                        } else if ( RenderSectionBackgroundType.NewControlFocus === type ) {
                            elem.classList.remove('newcontrol-focus');
                        }
                    }
                }
            }
        }
    }

    private renderCursorInNewControlPath(points: any[], pageNums: number[], container: any): void {
        if ( points && 0 < points.length ) {
            pageNums.forEach((pageNum) => {
                const page = container.querySelector(
                    `.page-container[page-id='${pageNum}']`);
                if (!page) {
                    return;
                }

                let pathContainer = page.getElementsByClassName('newcontrol-cursorin-container')[0];
                if (!pathContainer) {
                    pathContainer = document.createElementNS('http://www.w3.org/2000/svg', 'g') as any;
                    pathContainer.setAttribute('class', 'newcontrol-cursorin-container');
                    page.appendChild(pathContainer);
                }

                let pathElement: HTMLElement = pathContainer.childNodes[0] as any;
                if ( !pathElement ) {
                    pathElement = document.createElementNS('http://www.w3.org/2000/svg', 'polygon') as any;
                    pathElement.setAttribute('fill', 'transparent');
                    pathElement.setAttribute('fillOpacity', '1');
                    // pathElement.setAttribute('stroke', '#0000ff');
                    pathElement.setAttribute('stroke', getTheme()
                            .NewControl.DefaultNewControlSectionRangeBorderColor);
                    pathElement.setAttribute('strokeWidth', '1');
                    pathContainer.appendChild(pathElement);
                } else {
                    pathElement.style.display = '';
                }
                // if (!getTheme().NewControl.ShowNewControlSectionRangeBorder) {
                //   pathElement.style.display = 'none';
                // }

                let path = '';
                // tslint:disable-next-line: prefer-for-of
                for (let index2 = 0; index2 < points.length; index2++) {
                    const item = points[index2];
                    if ( item && item.pageIndex === pageNum ) {
                        path += (numtoFixed2(item.x) + ' ');
                        path += (numtoFixed2(item.y) + ' ');

                        path += (numtoFixed2(item.x) + ' ');
                        path += numtoFixed2(item.y + item.height) + ' ';
                    }
                }

                for (let index2 = points.length - 1; index2 >= 0; index2--) {
                    const item = points[index2];
                    if ( item && item.pageIndex === pageNum ) {
                        const x = numtoFixed2(item.x + item.width);

                        path += (x + ' ');
                        path += numtoFixed2(item.y + item.height) + ' ';

                        path += (x + ' ');
                        path += numtoFixed2(item.y) + ' ';
                    }
                }

                pathElement.setAttribute('points', path);
            });
        }
    }

    private renderCursorInNewControlPath2(pageNums: object, container: any): void {
        const keys = Object.keys(pageNums);
        if ( keys && 0 < keys.length ) {
            keys.forEach((pageNum) => {
                const points = pageNums[pageNum];
                if (!points || points.length === 0) {
                    return;
                }
                const page = container.querySelector(
                    `.page-container[page-id='${pageNum}']`);
                if (!page) {
                    return;
                }

                let pathContainer = page.getElementsByClassName('newcontrol-cursorin-container')[0];
                if (!pathContainer) {
                    pathContainer = document.createElementNS('http://www.w3.org/2000/svg', 'g') as any;
                    pathContainer.setAttribute('class', 'newcontrol-cursorin-container');
                    page.appendChild(pathContainer);
                }

                let pathElement: HTMLElement = pathContainer.childNodes[0] as any;
                if ( !pathElement ) {
                    pathElement = document.createElementNS('http://www.w3.org/2000/svg', 'polygon') as any;
                    pathElement.setAttribute('fill', 'transparent');
                    pathElement.setAttribute('fillOpacity', '1');
                    // pathElement.setAttribute('stroke', '#0000ff');
                    pathElement.setAttribute('stroke', getTheme()
                            .NewControl.DefaultNewControlSectionRangeBorderColor);
                    pathElement.setAttribute('strokeWidth', '1');
                    pathContainer.appendChild(pathElement);
                } else {
                    pathElement.style.display = '';
                }
                // if (!getTheme().NewControl.ShowNewControlSectionRangeBorder) {
                //   pathElement.style.display = 'none';
                // }

                let path = '';
                // tslint:disable-next-line: prefer-for-of
                for (let index2 = 0; index2 < points.length; index2++) {
                    const item = points[index2];
                    const x = numtoFixed2(item.x);
                    const y = numtoFixed2(item.y);
                    path += (x + ' ');
                    path += (y + ' ');

                    path += (x + ' ');
                    path += numtoFixed2(item.y + item.height) + ' ';
                }

                for (let index2 = points.length - 1; index2 >= 0; index2--) {
                    const item = points[index2];
                    const x = numtoFixed2(item.x + item.width);

                    path += (x + ' ');
                    path += numtoFixed2(item.y + item.height) + ' ';

                    path += (x + ' ');
                    path += numtoFixed2(item.y) + ' ';
                }

                pathElement.setAttribute('points', path);
            });
        }
    }

    private clearCursorInNewControlPath(container?: any): void {
        container = container ? container : this.host.getContainer();
        if (!container) {
            return;
        }
        const paths = container.querySelectorAll('.page-wrapper .newcontrol-cursorin-container > polygon');
        Array.from(paths)
        .forEach((path: HTMLElement) => {
            path.style.display = 'none';
        });
        // const pages = container.getElementsByClassName('page-container');
        // if ( pages ) {
        //     // tslint:disable-next-line: prefer-for-of
        //     for (let index = 0; index < pages.length; index++) {
        //         const page = pages[index];
        //         const paths = page.getElementsByClassName('newcontrol-cursorin-container')[0];
        //         if ( paths ) {
        //             // tslint:disable-next-line: prefer-for-of
        //             for (let index2 = paths.childNodes.length - 1; index2 >= 0; index2--) {
        //                 const path = paths.childNodes[index2];
        //                 paths.removeChild(path);
        //                 // path.removeAttribute('d');
        //             }
        //         }
        //     }
        // }
    }

    private renderSectionBackground2(renderLine: any[], type: RenderSectionBackgroundType, color?: string): void {
        const container = this.host.getContainer();
        if (!container) {
            return;
        }
        // const points = [];
        const pageNums: any = {};

        for (let index = 0, length = renderLine.length; index < length; index++) {
            const item = renderLine[index];
            if (null != item) {
                const i = {x: Math.round(item.x), y: Math.round(item.y), pageIndex: item.pageIndex,
                    height: Math.round(item.height), width: Math.round(item.width)};
                // points.push(i);
                // if ( !pageNums.includes(item.pageIndex) ) {
                //     pageNums.push(item.pageIndex);
                // }
                const pageIndex = item.pageIndex;
                if (pageNums[pageIndex]) {
                    pageNums[pageIndex].push(i);
                } else {
                    pageNums[pageIndex] = [i];
                }
            }
        }
        const keys = Object.keys(pageNums);
        if ( 0 < keys.length && RenderSectionBackgroundType.NewControlFocus === type ) {
            keys.forEach((pageNum) => {
                const points = pageNums[pageNum];
                if (!points || points.length === 0) {
                    return;
                }
                const page = container.querySelector(
                    `.page-container[page-id='${pageNum}']`);
                if (!page) {
                    return;
                }

                let pathContainer = page.querySelector('.newcontrol-focus');
                if (!pathContainer) {
                    pathContainer = document.createElementNS('http://www.w3.org/2000/svg', 'g');
                    pathContainer.setAttribute('class', 'newcontrol-focus');
                    page.appendChild(pathContainer);
                }

                // let pathElement = pathContainer.querySelector('path');
                // if ( !pathElement ) {
                //     pathElement = document.createElementNS('http://www.w3.org/2000/svg', 'path') as any;
                //     // pathElement.setAttribute('fill', 'transparent');
                //     pathElement.setAttribute('fill', '#ABC8F2');
                //     pathElement.setAttribute('fill-opacity', '0.8');
                //     pathElement.setAttribute('stroke', 'none');
                //     // pathElement.setAttribute('stroke', getTheme()
                //     //         .NewControl.DefaultNewControlSectionRangeBorderColor);
                //     // pathElement.setAttribute('strokeWidth', '1');
                //     pathContainer.appendChild(pathElement);
                // // } else {
                // //     pathElement.style.display = '';
                // }

                // let path = 'M ';
                // // tslint:disable-next-line: prefer-for-of
                // for (let index2 = 0; index2 < arrs.length; index2++) {
                //     const item = arrs[index2];
                //     if ( item && item.pageIndex === pageNum ) {
                //         path += ('M ' === path) ? (item.x + ' ') : ('L ' + item.x + ' ');
                //         path += (item.y + ' ');

                //         path += ('L ' + item.x + ' ');
                //         path += (item.y + item.height) + ' ';
                //     }
                // }

                // for (let index2 = arrs.length - 1; index2 >= 0; index2--) {
                //     const item = arrs[index2];
                //     if ( item && item.pageIndex === pageNum ) {
                //         const x = item.x + item.width;

                //         path += ('L ' + x + ' ');
                //         path += (item.y + item.height) + ' ';

                //         path += ('L ' + x + ' ');
                //         path += (item.y) + ' ';
                //     }
                // }

                // pathElement.setAttribute('d', path + ' Z');
                // let rects = '';
                // for (let index2 = 0, length = points.length; index2 < length; index2++) {
                //     const item = points[index2];
                //     rects += `<rect class='newcontrol-rect' x='${item.x}' y='${item.y}'
                //         width='${item.width}' height='${item.height}'></rect>`;
                // }
                pathContainer.innerHTML = this.getPolygonRect(points);
            });
        } else if ( 0 < keys.length && RenderSectionBackgroundType.NewControlCursorIn === type ) {
            this.renderCursorInNewControlPath2(pageNums, container);
        }
    }

    private getPolygonRect(items: any[], color: string = 'rgba(0, 241, 253, 0.3)'): string {
        const length = items.length - 1;
        const lefts: any[] = [];
        const rights: any[] = [];
        items.forEach((item, index) => {
            const x = numtoFixed2(item.x);
            const y = numtoFixed2(item.y);
            const x2 = numtoFixed2(item.x + item.width);
            const y2 = numtoFixed2(item.y + item.height);
            if (index === 0) {
                rights.push({x, y: y2});
                rights.push({x, y});
                rights.push({x: x2, y});
                rights.push({x: x2, y: y2});
                if (length === index) {
                    return;
                }
            } else if (index === length) {
                rights.push({x: x2, y});
                rights.push({x: x2, y: y2});
                lefts.unshift({x, y});
                lefts.unshift({x, y: y2});
            } else {
                rights.push({x: x2, y: y2});
                lefts.unshift({x, y});
                lefts.unshift({x, y: y2});
            }
        });

        const paths = rights.concat(lefts);
        let path = '';
        paths.forEach((item) => {
            path += `${item.x},${item.y} `;
        });
        const style = `fill: ${color}; stroke-width: 0px; pointer-events: none`;
        return `<polygon points='${path}' style='${style}' />`;
    }

    private clearSectionBackground2(renderLine: any[], type: RenderSectionBackgroundType): void {
        const container = this.host.getContainer();
        if (!container) {
            return;
        }
        const pages = container.getElementsByClassName('page-container');
        if ( RenderSectionBackgroundType.NewControlCursorIn === type ) {
            this.clearCursorInNewControlPath(container);
        } else if (RenderSectionBackgroundType.NewControlFocus === type) {
            const pagesContainer = this.getContainer();
            const paths = pagesContainer.getElementsByClassName('newcontrol-focus');
            Array.from(paths)
            .forEach((path: HTMLElement) => {
                // path.style.display = 'none';
                path.innerHTML = '';
            });
        }
    }

    private getViewMode(): ViewModeType {
        // if (this.viewMode === undefined) {
        //     this.viewMode = this.documentCore.getViewMode();
        // }
        return this.documentCore.getViewMode();
    }

    private getPaddingtTop(): {top: number, left?: number} {
        const obj = {
            top: 0,
            left: 0,
        };

        const viewMode = this.getViewMode();
        if (viewMode === ViewModeType.WebView) {
            const margin = this.documentCore.getPagePositionInfo(0);
            obj.top = margin.y;
        } else if (viewMode === ViewModeType.CompactView) {
            const page = this.documentCore.getPageProperty();
            obj.top = 0; // page.paddingTop;
            obj.left = page.paddingLeft;
        }

        return obj;
    }

    // private hideTips(): void {
    //     if (this.isShowNewControlTips !== true) {
    //         return;
    //     }
    //     // console.log(this.curFocusNewControlName)
    //     this.tips = null;
    //     this.prevCursorInName = null;
    //     this.isShowNewControlTips = false;
    //     gEvent.setEvent(this.host.docId, gEventName.NewControlTips);
    // }

    private showTips(bCursorChange: boolean = false): void {
        if (this.host.bPrint) {
            return;
        }

        // let newControlName: string = this.curFocusNewControlName;
        const newControl = this.newControl;
        if (!newControl) {
            this.prevCursorInName = undefined;
            return;
        }

        let newControlName: string;
        if (newControl) {
            const parent = newControl.getParent();
            if (parent && parent.isSignatureBox() && newControl.isNewTextBox()) {
                return;
            }

            newControlName = newControl.getNewControlName();
        }

        if (this.prevCursorInName === newControlName) {
            return;
        }

        const tips = this.tips;
        if (!tips) {
            return;
        }

        tips.content = newControl.getTipsContent();
        if (!tips.content) {
            return;
        }

        this.tips.newControlName = newControlName;
        this.prevCursorInName = newControlName;
        this.isShowNewControlTips = true;
        if (!this.getContainer()
                .querySelector('.newControl-tips')) {
            this.newTips = this.tips;
            this.showTipContent();
        }
        gEvent.setEvent(this.docId, gEventName.NewControlTips, this.tips);
        // clearTimeout(this.timer);
        // this.timer = setTimeout(() => {
        //     if (this.curFocusNewControlName === newControlName && this.isShowNewControlTips === true) {
        //         return;
        //     }

        //     if (e.target.nodeName === 'DIV') {
        //         return;
        //     }

        //     const content = this.documentCore.getNewControlTips(newControlName);
        //     if (!content) {
        //         this.hideTips();
        //         return;
        //     }
        //     this.curFocusNewControlName = newControlName;
        //     let dom = this.tipDom;
        //     if (!dom) {
        //         dom = document.createElement('div');
        //         dom.className = 'editor-newcontrol-tips';
        //         dom.innerHTML = `<div></div><i/>`;
        //         this.host.getContainer()
        //         .appendChild(dom);
        //         this.tipDom = dom;
        //     }
        //     (dom.firstChild as HTMLElement).innerText = content;
        //     const page = this.getPagePro();
        //     dom.className += ' visible';
        //     const scale = this.host.getScale();
        //     dom.style.maxWidth = page.width * scale - 40 + 'px';
        //     // console.log(e)
        //     this.showDom(dom, e);
        // }, 30);

    }

    // private getPagePro(): PageProperty {
    //     return this.pagePro = this.documentCore.getPageProperty();
    // }

    // private showDom(dom: HTMLElement, e: any): void {
    //     // setTimeout(() => {
    //         // this.getPagePro();
    //         const page = this.pagePro;
    //         const pageDom = dom.parentNode.querySelector('.page-wrapper') as HTMLDivElement;
    //         const scale = this.host.getScale();
    //         const width = page.width * scale;
    //         const x = e.offsetX;
    //         const y = e.offsetY;
    //         const xLimit = width;
    //         const left = page.paddingLeft * scale;
    //         const right = xLimit - page.paddingRight * scale;
    //         // const clientX = e.clientX;
    //         // console.dir(dom)
    //         // console.dir(e)
    //         if (x < left || x > right) {
    //             this.hideTips();
    //             return;
    //         }

    //         const clientWidth = dom.clientWidth;
    //         const tipWidth = clientWidth / 2;

    //         let nLeft = x - tipWidth;
    //         if (clientWidth + 42 > width) {
    //             nLeft = 15;
    //         } else if (nLeft < 0) {
    //             nLeft = x + nLeft;
    //         } else {
    //             const subWidth = x + clientWidth - xLimit;
    //             if (subWidth > 0) {
    //                 nLeft = xLimit - clientWidth - 15;
    //             }
    //         }
    //         const last = dom.lastChild as any;
    //         const clientHeight = dom.clientHeight + 15;

    //         dom.className = dom.className.replace(/\s+(active|visible)/g, ' active');
    //         dom.style.left =  pageDom.offsetLeft + nLeft + 'px';
    //         dom.style.top = y - clientHeight + this.getOffsetTop(e.target) + 'px';
    //         last.style.left = x - nLeft + 'px';
    //         this.isShowNewControlTips = true;
    //     // }, 0);
    // }

    // private getOffsetTop(target: any): number {
    //     let parent = target.parentNode;
    //     while (parent) {
    //         if (parent.tagName === 'DIV') {
    //             return parent.offsetTop;
    //         }
    //         parent = parent.parentNode;
    //     }

    //     return 0;
    // }

    private newControlChange(newControl: any): void {
        const oldNewControl = this.newControl;
        if (!this.documentCore.isProtectedMode()) {
            // this.NewControl用于后续click事件的使用
            this.newControl = newControl;
        }
        // 从结构化移动到结构化外面
        if (!newControl) {
            // 一直在结构化外溜达 <- 666
            if (!oldNewControl) {
                return;
            }
            this.hideNewControlTipContent();
            this.showNumberErrorTips(oldNewControl);
            this.checkDateBoxRange(oldNewControl);
            this.newControlOnBlur(oldNewControl);

            if ( oldNewControl ) {
                this.clearHightLightNewControl();
            }
            this.downNewControl = null;
            // gEvent.setEvent(this.docId, gEventName.NewControlChange, newControl);
            return;
        }

        if ( this.documentCore.isStrictMode2() && this.host.getCursor2()) {
            const cursor = this.host.getCursor2();
            if ( cursor ) {
                cursor.setNodeVisible(false);
            }
        }

        // 在结构化内移动
        if (newControl === oldNewControl) {
            return;
        }

        const tipContent = newControl.getTipsContent();
        if (tipContent) {
            const parent = newControl ? newControl.getParent() : null;
            const bSubSign = (parent && newControl.isNewTextBox() && parent.isSignatureBox());
            if (!bSubSign) {
                let tips;
                const newControlName = newControl.getNewControlName();
                if (!this.tips) {
                    const cursorInNewControlBounds = this.documentCore.getNewControlsFocusBounds();
                    const firstBounds = cursorInNewControlBounds?.bounds[0];
                    this.newTips = tips = this.tips = {} as any;
                    if (firstBounds) {
                        // const scale = this.host.getScale();
                        tips.left = firstBounds.x;
                        tips.top = firstBounds.y;
                        tips.pageIndex = firstBounds.pageIndex;
                    }
                } else {
                    tips = this.newTips = this.tips;
                    if (newControlName !== tips.newControlName) {
                        const newPosition = newControl.getNewPosition2();
                        if (newPosition) {
                            tips.left = newPosition.x;
                            tips.top = newPosition.y + 2;
                        }
                    }
                }

                tips.newControlName = newControlName;
                this.bNewControlTip = true;
                tips.content = tipContent;
                if (this.documentCore.isProtectedMode()) {
                    // 只读模式下控制签名控件提示信息
                    if (this.showSignHelptips && newControl.isSignatureBox()) {
                        this.showTipContent();
                    }
                } else {
                    this.showTipContent();
                }
            } else {
                this.hideTipContent();
            }
        } else {
            this.hideNewControlTipContent();
            // this.newTips = undefined;
        }

        // 从一个结构化跳到另外一个内
        if (oldNewControl) {
            this.newControlOnBlur(oldNewControl);
        }
        // 跳到新的结构化内
        this.showNumberErrorTips(oldNewControl);
        this.checkDateBoxRange(oldNewControl);
        this.newControlOnFocus(newControl);
        this.downNewControl = newControl;
        gEvent.setEvent(this.docId, gEventName.NewControlChange, newControl);
    }

    private hideNewControlTipContent(): void {
        const bNewControlTip = this.bNewControlTip;
        if (bNewControlTip !== true) {
            return;
        }
        this.hideTipContent();
        this.bOldNewControlTip = true;
    }

    private getNewControlByName(name: string): any {
        if (!name) {
            return;
        }
        const newControl = this.newControl;
        if (newControl && newControl.name === name) {
            return newControl;
        }

        return this.documentCore.getNewControlByName(name);
    }

    private showErrorMessageInfo(name: string, mouseEvent?: MouseEventHandler): void {
        mouseEvent = mouseEvent;
        if (!mouseEvent || mouseEvent.x === undefined) {
            return;
        }

        clearTimeout(this.messageTimeout);
        this.messageTimeout = setTimeout(() => {
            let newControl: any;
            if (name) {
                newControl = this.documentCore.getNewControlByName(name);
            }

            if (!newControl) {
                this.showRegionMessageTips(mouseEvent);
                return;
            }

            const x = mouseEvent.x - 10;
            const y = mouseEvent.y - 45;
            if (newControl.isNewTextBox()) {
                if ((newControl as any).isMoreThanMaxLength1()) {
                    const messageTip = this.getMessageTip();
                    const len: any = newControl.getMaxLength() || '';
                    messageTip.open('提醒：该元素的最大长度不能超过' + len, x, y);
                    return;
                }
            } else if (newControl.isNumberBox()) {
                const res = (newControl as any).isValidNumber(false);
                if (res !== NewControlErrorInfo.Success) {
                    const messageTip = this.getMessageTip();
                    messageTip.open(res, x, y);
                    return;
                }
            } else if (newControl.isNewDateBox()) {
                if (newControl instanceof NewControlDate) {
                    const res = newControl.isValidDate();
                    if (res !== NewControlErrorInfo.Success) {
                        const messageTip = this.getMessageTip();
                        messageTip.open(res, x, y);
                        return;
                    }
                }
            }
            
            // this.showRegExpText(name, mouseEvent);

            this.showRegionMessageTips(mouseEvent);
        }, 900);

    }

    private showRegExpText(name: any, mouseEvent?: MouseEventHandler, timeout: number = 0): void {
        mouseEvent = mouseEvent;
        if (!mouseEvent || mouseEvent.x === undefined) {
            return;
        }

        clearTimeout(this.messageTimeout3);
        this.messageTimeout3 = setTimeout(() => {
            let newControl: any;
            const curName = this.documentCore.getFocusNewControlName(mouseEvent, this.host.getCurPageIndex())
            newControl = this.documentCore.getNewControlByName(curName);

            if (!newControl) {
                return;
            }

            const x = mouseEvent.x - 10;
            const y = mouseEvent.y - 45;

            if ('matchRegExp2' in newControl) {
                const text = newControl.matchRegExp2();
                if (text) {
                    const messageTip = this.getMessageTip();
                    messageTip.open(`内容校验失败，${text}`, x, y);
                    return;
                }
            }
        }, timeout);
    }

    private showRegExpText2(newControl: any, mouseEvent?: MouseEventHandler, timeout: number = 0): void {
        mouseEvent = mouseEvent;
        if (!mouseEvent || mouseEvent.x === undefined) {
            return;
        }

        clearTimeout(this.messageTimeout2);
        this.messageTimeout2 = setTimeout(() => {
            if (!newControl) {
                return;
            }

            const x = mouseEvent.x - 10;
            const y = mouseEvent.y - 45;

            if ('matchRegExp2' in newControl) {
                const text = newControl.matchRegExp2();
                if (text) {
                    const messageTip = this.getMessageTip();
                    messageTip.open(`内容校验失败，${text}`, x, y);
                    return;
                }
            }
        }, timeout);
    }

    private showRegionMessageTips(mouseEvent: MouseEventHandler): void {
        const option = this.regionMessageOption;
        if (!option) {
            if (mouseEvent.newControl && 'isRegion' in mouseEvent.newControl) {
                this.showRegExpText2(mouseEvent.newControl, mouseEvent, 1);
            }
            return;
        }

        const length = option.newControl.getMaxLength();
        if (length === 0 || length == null || length > option.newControl.getTextLength(true)) {
            this.regionMessageOption = null;
            if (mouseEvent.newControl && 'isRegion' in mouseEvent.newControl) {
                this.showRegExpText2(mouseEvent.newControl, mouseEvent, 1);
            }
            // const region = this.documentCore.getFoucsInRegion({pointX: mouseEvent.x, pointY: mouseEvent.y, ...mouseEvent}, this.host.getCurPageIndex());
            
            return;
        }
        // const region = this.documentCore.getFoucsInRegion(mouseEvent, this.host.getCurPageIndex());
        

        const x = option.x - 10;
        const y = option.y - 45;
        const messageTip = this.getMessageTip();
        const len: any = length || '';
        messageTip.open('提醒：该区域的最大长度不能超过' + len, x, y);
        this.regionMessageOption = null;
    }

    private getMessageTip(): MessageTip {
        let messageTip = this.messageTip;
        if (!messageTip) {
            messageTip = this.messageTip = new MessageTip(this.host);
        }
        return messageTip;
    }

    private showMessageTips = (newControl: any, type: MessageType, sInfo?: string): void => {
        const messageTip = type === MessageType.NewControlFocus ? new MessageTip(this.host) : this.getMessageTip();
        const position: any = newControl.getPosition(type);
        if (!position) {
            return;
        }
        let x: number = position.x;
        let y: number = position.y;
        const scale = this.host.getScale();
        const obj = getPagePadding(this.documentCore);
        x -= obj.left;
        x *= scale;
        y = (y - obj.top) * scale;
        const newPosition = messageTip.getActivePosition(position.pageNum, x, y);
        if (!newPosition) {
            return;
        }
        // if (position.bRefresh === true) {
        //     this.host.changeDocument();
        // }
        const text = this.getMessageText(type, sInfo);
        messageTip.open(text, newPosition.x, newPosition.y, false, type !== MessageType.NewControlFocus);
    }

    private getMessageText = (type: MessageType, sInfo?: string): string => {
        let text: string;
        switch (type) {
            case MessageType.UnDelete: {
                text = '设置了删除保护，无法编辑内容';
                break;
            }
            case MessageType.IncludeTitle: {
                text = '当前包含了标题，无法编辑内容';
                break;
            }
            case MessageType.UnEdited: {
                text = '设置了保护属性，无法编辑内容';
                break;
            }
            case MessageType.EditTitle: {
                text = '当前光标在标题中，无法编辑内容';
                break;
            }
            case MessageType.DeleteTitle: {
                text = '当前光标在标题中，无法编辑内容';
                break;
            }
            case MessageType.TableProtected: {
                text = '表格设置了只读保护，无法编辑内容';
                break;
            }
            case MessageType.CellProtected: {
                text = '单元格设置了保护，无法编辑内容';
                break;
            }
            case MessageType.TableDeleted: {
                text = '表格设置了删除保护，无法编辑';
                break;
            }
            case MessageType.RowDeleted: {
                text = '表格设置了行删除保护，禁止删除';
                break;
            }
            case MessageType.UnValidData: {
                text = '';
                break;
            }
            case MessageType.Max: {
                text = '';
                break;
            }
            case MessageType.Min: {
                text = '';
                break;
            }
            case MessageType.MaxLength: {
                text = '录入内容已超过最大限制长度';
                break;
            }
            case MessageType.TableCellType: {
                text = '所选单元格的类型不一样，不允许合并';
                break;
            }
            case MessageType.NewControlFocus:
            case MessageType.GroupCheckBox: {
                text = sInfo;
                break;
            }
        }
        return text;
    }

    private showNumberErrorTips(newControl: any): void {
        return;
        if (!newControl || true !== newControl.isNumberBox()) {
            return;
        }

        const res = newControl.updateValidInput();
        const name = newControl.getNewControlName();
        switch (res) {
            case NewControlErrorInfo.Success: {
                return;
            }
            case NewControlErrorInfo.Refresh: {
                this.host.refresh();
                return;
            }
            default: {
                setTimeout(() => {
                    IFRAME_MANAGER.setDocId(this.host.docId);
                    message.error(res, {time: 100000000000})
                    .then(() => {
                        this.documentCore.selectNewControlContent(name);
                        this.host.refresh();
                    });
                }, 220);
                return;
            }
        }
    }

    private checkDateBoxRange(newControl: any): void {
        if (!newControl || true !== newControl.isNewDateBox()) {
            return;
        }
        if ( newControl.isNewDateBox()) {
            const startDate = Date.parse(newControl.getStartDate());
            const endDate = Date.parse(newControl.getEndDate());
            if (Number.isNaN(startDate) && Number.isNaN(endDate)) {
                // both range not set, quit check
                return;
            } else {
                const dateTime = newControl.getDateTime();
                if (dateTime == null) {
                    // newly created date struct
                    return;
                }
                const dateTimeParsed = Date.parse(dateTime);
                if (!Number.isNaN(dateTime)) {
                    let errorMsg = '';
                    if (!Number.isNaN(startDate)) {
                        if (dateTimeParsed < startDate) {
                            //errorMsg = NewControlErrorInfo.DateRangeError;
                            errorMsg = getDateRangeError(newControl.getStartDate(), newControl.getEndDate());
                        }
                    }
                    if ((errorMsg != null && errorMsg.length <= 0) && !Number.isNaN(endDate)) {
                        if (dateTimeParsed > endDate) {
                            //errorMsg = NewControlErrorInfo.DateRangeError;
                            errorMsg = getDateRangeError(newControl.getStartDate(), newControl.getEndDate());
                        }
                    }
                    if (errorMsg != null && errorMsg.length > 0) {
                        const name = newControl.getNewControlName();
                        setTimeout(() => {
                            IFRAME_MANAGER.setDocId(this.docId);
                            message.error(errorMsg, {time: 100000000000})
                            .then(() => {
                                this.documentCore.selectNewControlContent(name);
                                this.host.refresh();
                                
                            });
                        }, 220);
                    }

                } else {
                    // tslint:disable-next-line: no-console
                    console.warn('DateTime is not of legal format');
                    return;
                }
            }
        } else {
            // tslint:disable-next-line: no-console
            console.warn('You have reached Aperture Science lab area. Want a cake?');
        }
    }

    private cursorChange = (position: {x: number, y1: number, y2: number, pageNum: number}): void => {
        // this.bCursorChange = true;
        const bProtectMode = this.documentCore.isProtectedMode();
        if (this.bFromMouseDown === false && true !== bProtectMode ) {
            // this.newControl = this.documentCore.getMouseDownControl(this.gMouseEvent, position.pageNum);
            clearTimeout(this.timerout);
            this.timerout = setTimeout(() => {
                if (!this.documentCore.getDocument()) {
                    return;
                }

                if (position) {
                    const scale = this.host.getScale();
                    this.gMouseEvent.pointX = position.x / scale;
                    this.gMouseEvent.pointY = (position.y2 + position.y1) / 2 / scale;
                    // this.curFocusNewControlName = null;
                    this.renderFocusHightLightNewControl(); // this.gMouseEvent, position.pageNum);
                    // const newControl = this.getNewControlByName(this.curFocusNewControlName);
                    // this.newControlChange(newControl);
                    if (this.bCursorHidden === true) {
                        this.checkBoxCursorHidden();
                        if (!this.bCursorHidden) {
                            this.host.updateCursor();
                        }
                    }
                }

                this.getCurrentRegion();
                // this.showTips(true);
                // this.setErrorTextColor(newControl, 2);
            }, 10);
            const newControl2 = this.documentCore.getCurrentNewControl();
            this.newControlChange(newControl2);
            if (this.region) {
                this.refreshRegionBorder(this.region, true);
            }
            // this.hideTips();
            this.checkBoxCursorHidden();
        } else if ( bProtectMode ) {
            // this.hideTips();
        }

        this.bFromMouseDown = false;
    }

    // private mouseMove = (e: any): void => {
    //     clearTimeout(this.timer);
    //     this.timer = setTimeout(() => {
    //         this.handleMouseMove(e);
    //     }, 30);
    // }

    private newControlOnFocus = (newControl: any): void => {
        if (newControl) {
            this.documentCore.startMonitorElement(newControl);
            if (this.host.externalEvent) {
                this.host.externalEvent.nsoStructGainFocus(newControl.name, newControl.getType());
            }
        }
        // if (newControl.isNewTextBox()) {
        //     const res = newControl.setTextErrorBgColor(2);
        //     if (res === true) {
        //         this.host.changeDocument();
        //     }
        // }
    }

    private newControlOnBlur = (newControl: any): void => {
        this.documentCore.endMonitorElement(newControl);
        if (this.host.externalEvent) {
            this.host.externalEvent.nsoStructLostFocus(newControl.name, newControl.getType());
        }

        // 需要判断this.newControl是否为非法的数值框，若同时存在两个非法数值框，会进入jump死循环
        if (!(this.newControl?.isNumberBox() && (NewControlErrorInfo.Success !== this.newControl.isValidNumber(false)))) {
            this.setErrorTextColor(newControl, 2);
        }
        const type = newControl.getType();
        if (type === NewControlType.Combox || type === NewControlType.MultiCombox) {
            newControl.setNewContentHidden();
        }
    }

    private setErrorTextColor(newControl: any, type: number): void {
        if (newControl) {
            if (newControl.isNewTextBox() || newControl.isNumberBox() || newControl.isNewDateBox() || newControl.isNewSection()) {
                let bChanged: boolean = false;
                const newControlName = newControl.getNewControlName();
                if (newControl.isNumberBox()) {
                    // const result: string = newControl.updateValidInput();
                    const result: string = this.documentCore.updateNewControlValidInput(newControlName);
                    if (result === NewControlErrorInfo.Refresh) {
                        bChanged = true;
                    }
                }
                // const res = newControl.setTextErrorBgColor(type);
                const curNewControlName = this.documentCore.getCursorInNewControlName();
                const res = this.documentCore.setNewControlTextErrorBgColor(newControlName, type);
                if (res === true || bChanged) {
                    this.host.changeDocument();
                    if (newControlName !== curNewControlName && newControl.isNumberBox()) {
                        this.host.updateCursor();
                    }
                }
            }
        }
    }

    private contentOnClick = (e: any): void => {
        const target = e.target;
        if (target?.classList[0] === 'button-rect') {
            this.host.setCursorVisible(false);
            if (this.host.externalEvent?.nsoButtonClick) {
                const name = target.getAttribute('name');
                const button = this.documentCore.getParaButtonByName(name);
                if (button) {
                    this.host.externalEvent.nsoStructClick(name, button.getStructType(), {x: e.clientX, y: e.clientY, height: button.height});
                }
            }
            return;
        }
        if (this.host.externalEvent && target && target.nodeName === 'text') {

            let newControl = this.newControl;
            if (!newControl) {
                return;
            }
            if (newControl.getParent instanceof Function) {
                const parentControl =  newControl.getParent();
                if (parentControl && parentControl.getType() === NewControlType.SignatureBox) {
                    newControl = parentControl;
                }
            }
            const position = this.getNewControlPosition();
            const obj = getPagePadding(this.documentCore);
            let subLeft = 0;
            if (obj.left > 0) {
                subLeft = (obj.left + getPagePadding.PaddingLeft) * this.host.getScale();
            }
            this.host.externalEvent.nsoStructClick(newControl.name, newControl.getType(),
            {x: position.x - subLeft, y: position.y - obj.top, height: position.height});
        }

        if ( this.newControl && !this.newControl.isReadOnly() ) {
            this.checkBoxSelect();
            this.radioSelect(e);
        }

        this.documentCore.addMonitorElementEvent(this.newControl, MonitorEvent.Click);

        const className = target.className;
        if (typeof className === 'string' && className.indexOf('newControl-tips') > -1) {
            this.tipContentClick(target, className);
        }
        const dataIndex = target.getAttribute('data-index');
        if (dataIndex) {
            const arrs = dataIndex.split('?');
            if (arrs.length === 2) {
                if (this.documentCore.changeRegionLoaded(arrs[0], arrs[1]) === ResultType.Success) {
                    let region = this.region;
                    if (region && arrs[1] === '2') {
                        const name = region.getName();
                        if (name === arrs[0]) {
                            if (arrs[1] === '2') {
                                this.removerRegionButton();
                            }
                        }
                    }

                    this.host.refresh();
                    if (!region && arrs[1] === '1') {
                        region = this.documentCore.getRegionByName(arrs[0]);
                        this.regionChange(region);
                    }
                }
            }
        }
    }

    private tipContentClick(dom: HTMLDivElement, className: string): void {
        if (className.indexOf(' active') === -1) {
            dom.className = className + ' active';
        }
        if (this.bNewControlTip) {
            this.documentCore.selectNewControl(this.newControl.getNewControlName());
        } else {
            this.documentCore.selectRegionByName(this.region.getName());
            const container = this.getContainer();
            if (!container) {
                return;
            }
            const borders = container.querySelectorAll('.region-border');
            Array.from(borders)
            .forEach((border) => {
                if (!border.classList.contains('active')) {
                    border.classList.add('active');
                }
            });
        }
        this.host.refresh();
    }

    private setCursorHidden(flag: boolean = true): void {
        this.bCursorHidden = flag;
    }

    private isCursorHiddBox(): boolean {
        const newControl = this.newControl;
        if (!newControl) {
            return false;
        }

        if (newControl.isCheckBox() || newControl.isMultiAndRadio()) {
            return true;
        }

        return false;
    }

    private checkBoxCursorHidden(): void {
        this.bCursorHidden = false;
        if (!this.isCursorHiddBox()) {
            return;
        }
        this.setCursorHidden();
    }

    private isCheckBox(): boolean {
        const newControl = this.newControl;
        if (!newControl) {
            return;
        }
        if (!newControl.isCheckBox()) {
            return;
        }

        return true;
    }

    private isRadioButton(): boolean {
        const newControl = this.newControl;
        if (!newControl) {
            return;
        }
        if (!newControl.isMultiAndRadio()) {
            return;
        }

        return true;
    }

    private radioSelect(e: any): void {
        // const newControl = ;
        if (this.documentCore.isProtectedMode() || !this.isRadioButton()) {
            return;
        }
        this.host.updateRefreshFlag(false);
        const flag = this.documentCore.setRadioSelectedValueByXY(this.newControl.getNewControlName(),
                                    e.offsetX, e.offsetY);
        if (flag) {
            this.setCursorHidden();
            this.host.refresh(true);
        } else if (flag === false) {
            this.setCursorHidden(false);
            this.host.setCursorVisible(true);
        } else {
            this.setCursorHidden();
            this.host.setCursorVisible(true);
        }
        this.host.updateRefreshFlag(true);
    }

    private checkBoxSelect(): void {
        // const newControl = this.newControl;
        if (!this.isCheckBox()) {
            return;
        }
        this.host.updateRefreshFlag(false);
        // newControl.setChecked();
        this.documentCore.setCheckboxStatus(this.newControl.getNewControlName(), !this.newControl.isSelected());
        this.host.refresh(true);
    }

    private contentOnDBClick = (e: any): void => {
        let newControl = this.newControl;
        const type = newControl?.getType();

        if (isGlobalTestData()) {
            consoleLog(newControl, type);
        }

        const target = e.target;
        if (target?.classList[0] === 'button-rect') {
            this.host.setCursorVisible(false);
            if (this.host.externalEvent?.nsoButtonClick) {
                const name = target.getAttribute('name');
                const button = this.documentCore.getParaButtonByName(name);
                if (button) {
                    this.host.externalEvent.nsoStructDBClick(name, button.getStructType(), {x: e.clientX, y: e.clientY, height: button.height});
                }
            }
            return;
        }

        if (newControl && (type === NewControlType.Combox
            || type === NewControlType.MultiCombox)) {
            this.showNewControlOuterLayer(newControl, e, 2);
        }

        if (this.host.externalEvent) {
            if (newControl) {
                if (newControl.getParent instanceof Function) {
                    const parentControl =  newControl.getParent();
                    if (parentControl && parentControl.getType() === NewControlType.SignatureBox) {
                        newControl = parentControl;
                    }
                }

                const newPostion = this.getNewControlPosition();
                const position = getPagePadding(this.documentCore);
                let subLeft = 0;
                if (position.left > 0) {
                    subLeft = (position.left + getPagePadding.PaddingLeft) * this.host.getScale();
                }
                this.host.externalEvent.nsoStructDBClick(newControl.name, newControl.getType(),
                {x: newPostion.x - subLeft, y: newPostion.y - position.top, height: newPostion.height});
                return;
            }
            const region = this.region;
            if (region) {
                this.host.externalEvent.nsoRegionDBClick(region.name, region.getNewControlType());
            }
        }

        if (this.documentCore.dbClickChangeRegionLoaded()) {
            this.host.refresh();
        }
    }

    private getNewControlPosition(): {x?: number, y?: number, height?: number} {
        const dom = this.host.getCurrentPageNode();
        if (!dom) {
            return {};
        }
        const scale = this.host.getScale();
        const documentCore = this.documentCore;
        const bounds = documentCore.getNewControlsFocusBounds();
        const lastLine = (bounds && bounds.bounds) ? bounds.bounds[bounds.bounds.length - 1] : null;
        if (!lastLine) {
            return {};
        }
        const viewMode = documentCore.getViewMode();
        let subHeight = 0;
        if (viewMode === ViewModeType.WebView) {
            const page = documentCore.getPagePositionInfo(0);
            subHeight -= page.y;
        }

        const left = lastLine.x * scale;
        const height = lastLine.height * scale;
        const top = (lastLine.y + lastLine.height + subHeight) * scale; // + 3;

        const parent = dom.parentNode;
        const span = parent.ownerDocument.createElement('span');
        span.className = 'newcontrol-box-placeholder';
        span.style.left = left + 'px';
        span.style.top = top + 'px';
        span.style.height = height + 'px';
        parent.appendChild(span);
        const position = span.getBoundingClientRect();
        span.outerHTML = '';
        return position;
    }

    private contentChange = (bText: boolean, bFlagChange: boolean): void => {
        const region = this.region;
        const newControl = this.newControl;
        if (this.host.externalEvent) {
            if (bText === true) {
                if (newControl) {
                    this.host.externalEvent.nsoStructChanged(newControl.name, newControl.getType());
                    if (newControl.isMultiAndRadio()) {
                        this.host.externalEvent.nsoRadioButtonCheckChanged(newControl.getNewControlName(), newControl.getSelectedIndexes());
                    }
                    //增加逻辑 如果结构化元素在区域内，则通知区域改变
                    if (newControl) {
                        const para = newControl.getStartBorderPortion().paragraph;
                        if (para) {
                            const currentRegion = para.getRegion();
                            if (currentRegion) {
                                //继续寻找一次父级区域
                                if (currentRegion.parent && currentRegion.parent.isRegionContent()) {
                                    const subRegion = (currentRegion.parent as any).parent;
                                    const regionName = subRegion.getName();
                                    const regionType = subRegion.getNewControlType();
                                    //修改区域脏标记
                                    subRegion.setDirty(true); 
                                    this.host.externalEvent.nsoRegionChanged(regionName, regionType); 
                                }
                                else{
                                    const regionName = currentRegion.getName();
                                    const regionType = currentRegion.getNewControlType();
                                    //修改区域脏标记
                                    currentRegion.setDirty(true);   
                                    this.host.externalEvent.nsoRegionChanged(regionName, regionType); 
                                }         
                            }
                        }
                    }
                } else if (region) {
                    //修改区域脏标记
                    region.setDirty(true); 
                    this.host.externalEvent.nsoRegionChanged(region.name, region.getNewControlType());
                }      
            }
            if (bFlagChange) {
                this.host.externalEvent.nsoFileModifyChanged(bText);
            }
        }

        //在区域内改变文字需要修改脏标记以及上一级区域的脏标记
        if(bText && region){
            //修改区域脏标记
            region.setDirty(true);
            const parent = region.getParent();
            if(parent && parent.isRegionContent()){
                parent.setDirty(true);
            }
        }

        if (bText && newControl) {
            if (this.documentCore.triggerNewControlCascade(newControl.getNewControlName())) {
                if (this.host.isNoRefresh()) {
                    this.host.updateRefreshFlag2(true);
                } else {
                    this.host.refresh();
                }
            }
            let res: string;
            // tslint:disable-next-line:no-conditional-assignment
            if (newControl.isNewDateBox() && (res = newControl.getWarnTip())) {
                message.info(res);
            }
        }

        this.refreshRegionBorder(region);
    }

    private refreshRegionBorder(region: any, bRegion?: boolean): void {
        clearTimeout(this.regionContentChangeTimeout);
        this.regionContentChangeTimeout = setTimeout(() => {
            const actRegion = this.region;
            this.renderRegionBorder(actRegion);
            const actNewControl = this.newControl;
            if (this.newTips && bRegion !== true && (actNewControl && !actNewControl.getTipsContent()
            || region && !actRegion.getTipsContent())) {
                this.hideTipContent();
            }
        }, 100);
    }

    private checkedChange = (name: string, flag: boolean): void => {
        if (this.host.externalEvent) {
            this.host.externalEvent.nsoStructCheckChanged(name, flag);
        }
    }

    private showRegionMaxLengthDom(e: any, region: any): void {
        clearTimeout(this.startTimeout);
        clearTimeout(this.clearTimeout);
        if ( region && 0 < region.getMaxLength() ) {
            const length = region.getMaxLength();
            if ( length < region.getTextLength(true) ) {
                this.startTimeout = setTimeout(() => {
                    if (e.target.nodeName === 'DIV') {
                        return;
                    }

                    let dom = this.regionMaxLengthDom;
                    if (!dom) {
                        dom = document.createElement('div');
                        dom.className = 'editor-region-max-length editor-revision-tips';
                        dom.innerHTML = `<div></div><i/>`;
                        const container = this.host.getContainer();
                        container.appendChild(dom);
                        this.regionMaxLengthDom = dom;
                        // dom.addEventListener(gEventName.Click, this.handleTips);
                        // dom.addEventListener(gEventName.Mousemove, this.handleTips);
                    }
                    const serialNumber = region.getSerialNumber();
                    const name = (null == serialNumber || '' === serialNumber) ? '该区域' : serialNumber;
                    (dom.firstChild as HTMLElement).innerText = '提醒：' + name + '不得超过' + length + '个字符！';
                    const page = this.documentCore.getPageProperty();
                    dom.className += ' visible';
                    const scale = this.host.getScale();
                    dom.style.maxWidth = page.width * scale - 40 + 'px';
                    this.showDom(dom, e);
                }, 100);

                this.clearTimeout = setTimeout(() => {
                    this.hideRegionMaxLengthDom();
                }, 2000);
            }
        }
        this.hideRegionMaxLengthDom();
    }

    private hideRegionMaxLengthDom(): void {
        if (!this.regionMaxLengthDom) {
            return;
        }

        this.regionMaxLengthDom.className = 'editor-region-max-length editor-revision-tips';
    }

    private showDom(dom: HTMLElement, e: any): void {
        const page = this.documentCore.getPageProperty();
        const pageDom = dom.parentNode.querySelector('.page-wrapper') as HTMLDivElement;
        const scale = this.host.getScale();
        const width = page.width * scale;
        const x = e.offsetX;
        const y = e.offsetY;
        const xLimit = width;
        const left = page.paddingLeft * scale;
        const right = xLimit - page.paddingRight * scale;
        // const clientX = e.clientX;
        // console.dir(dom)
        // console.dir(e)
        if (x < left || x > right) {
            // this.hideTips();
            return;
        }

        const clientWidth = dom.clientWidth;
        const tipWidth = clientWidth / 2;

        let nLeft = x - tipWidth;
        if (clientWidth + 42 > width) {
            nLeft = 15;
        } else if (nLeft < 0) {
            nLeft = x + nLeft;
        } else {
            const subWidth = x + clientWidth - xLimit;
            if (subWidth > 0) {
                nLeft = xLimit - clientWidth - 15;
            }
        }
        const last = dom.lastChild as any;
        const clientHeight = dom.clientHeight + 15;

        dom.className = dom.className.replace(/\s+(active|visible)/g, ' active');
        dom.style.left =  pageDom.offsetLeft + nLeft + 'px';
        dom.style.top = y - clientHeight + this.getOffsetTop(e.target) + 'px';
        last.style.left = x - nLeft + 'px';
    }

    private getOffsetTop(target: any): number {
        let parent = target.parentNode;
        while (parent) {
            if (parent.tagName === 'DIV') {
                return parent.offsetTop;
            }
            parent = parent.parentNode;
        }

        return 0;
    }

    private viewScale = (): void => {
        this.showTipContent();
        this.renderRegionButton(this.region);
        this.refreshRegionBorder(this.region);
        this.showNewControlOuterLayer(undefined, undefined);
    }

    private isReadOnly = (flag: boolean): void => {
        if (flag) {
            this.removeRegionBorder();
        }
    }

    private viewPropChange = (option: any): void => {
        if (option.hasOwnProperty('showSignHelptips') && this.showSignHelptips !== option.showSignHelptips) {
            this.showSignHelptips = option.showSignHelptips;
            option.result = ResultType.Success;
        }
        if (option.hasOwnProperty('showBgColor')) {
            const newControls = this.host.documentCore.getAllNewControls();
            newControls.forEach((newControl) => {
                newControl.setShowBgColor(option.showBgColor);
            });
            option.result = ResultType.Success;
        }
    }

    private addEvents(): void {
        gEvent.addEvent(this.docId, gEventName.Mousedown, this.handleMouseDown);
        gEvent.addEvent(this.docId, gEventName.Mousemove, this.handleMouseMove);
        gEvent.addEvent(this.docId, gEventName.Mouseup, this.handleMouseUp);
        gEvent.addEvent(this.docId, gEventName.UnMounted, this.removeEvents);
        gEvent.addEvent(this.docId, gEventName.MoveCursor, this.cursorChange);
        gEvent.addEvent(this.docId, gEventName.Dblclick, this.contentOnDBClick);
        gEvent.addEvent(this.docId, gEventName.Click, this.contentOnClick);
        setTimeout(() => {
            gEvent.addEvent(this.docId, gEventName.ContentChange, this.contentChange);
        }, 0);
        gEvent.addEvent(this.docId, gEventName.RegionActive, this.getCurrentRegion);
        gEvent.addEvent(this.docId, gEventName.CheckedChange, this.checkedChange);
        gEvent.addEvent(this.docId, gEventName.MessageEvent, this.showMessageTips);
        gEvent.addEvent(this.docId, gEventName.ViewScale, this.viewScale);
        gEvent.addEvent(this.docId, gEventName.ViewMode, this.viewScale);
        gEvent.addEvent(this.docId, gEventName.Readonly, this.isReadOnly);
        gEvent.addEvent(this.docId, gEventName.ViewPropChange, this.viewPropChange);
    }

    private removeEvents = (): void => {
        gEvent.deleteEvent(this.docId, gEventName.Mousedown, this.handleMouseDown);
        gEvent.deleteEvent(this.docId, gEventName.Mousemove, this.handleMouseMove);
        gEvent.deleteEvent(this.docId, gEventName.Mouseup, this.handleMouseUp);
        gEvent.deleteEvent(this.docId, gEventName.MoveCursor, this.cursorChange);
        gEvent.deleteEvent(this.docId, gEventName.Dblclick, this.contentOnDBClick);
        gEvent.deleteEvent(this.docId, gEventName.Click, this.contentOnClick);
        gEvent.deleteEvent(this.docId, gEventName.ContentChange, this.contentChange);
        gEvent.deleteEvent(this.docId, gEventName.RegionActive, this.getCurrentRegion);
        gEvent.deleteEvent(this.docId, gEventName.CheckedChange, this.checkedChange);
        gEvent.deleteEvent(this.docId, gEventName.MessageEvent, this.showMessageTips);
        gEvent.deleteEvent(this.docId, gEventName.ViewScale, this.viewScale);
        gEvent.deleteEvent(this.docId, gEventName.ViewMode, this.viewScale);
        gEvent.deleteEvent(this.docId, gEventName.Readonly, this.isReadOnly);
        gEvent.deleteEvent(this.docId, gEventName.ViewPropChange, this.viewPropChange);
        this.clearDatas();
    }
    
    private hideSubSignTipContent(): void {
        if (this.subSignTips) {
            this.subSignTips = undefined;
            // this.bNewControlTip = false;
            const tipDom = this.getContainer()
            .querySelector('.subSign-tips');
            if (!tipDom) {
                return;
            }
            tipDom.outerHTML = '';
        }
    }

    private showSubSignTipContent(): void {
        if (this.subSignTips) {
            this.createTipsDom(false);
        }
    }

    /**
     * 去除非当前页的tipDom
     * @param pageIndex 页码
     */
    private hiddenOtherTips(pageIndex: number): void {
        const tipDoms = this.getContainer()
                        .querySelectorAll('.newControl-tips');
        tipDoms?.forEach((dom) => {
            if (dom && dom.parentElement &&
                +dom.parentElement.getAttribute('page-index') !== pageIndex) {
                dom.outerHTML = '';
            }
        });
    }
}
