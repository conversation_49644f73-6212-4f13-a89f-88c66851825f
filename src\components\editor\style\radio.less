@import './global.less';
.hz-editor-container .editor-radio {
    display: inline-block;
    font-family: @fontFamily;

    .radio-item {
        display: inline-block;
        position: relative;
        margin-right: 15px;
        line-height: 1;
        vertical-align: middle;
        box-sizing: border-box;

        & > i {
            display: inline-block;
            width: 14px;
            height: 14px;
            margin-right: 4px;
            border: 1px solid @borderColor;
            border-radius: 50%;
            background: #fff;
            font-style: normal;
            vertical-align: top;
        }

        & > input[type='radio'] {
            position: absolute;
            width: 100%;
            height: 14px;
            padding: 0;
            margin: 0;
            z-index: 2;
            cursor: pointer;
            opacity: 0;
        }

        & > label {
            display: inline-block;
            // line-height: 15px;
            font-size: 14px;
            vertical-align: top;
            cursor: pointer;
        }

        &.disabled  {
            & > input[type=radio] {
                display: none;
            }
            & > label {
                cursor: default;
            }

            & > i {
                background-color: #fff;
                border-color: @disabledColor;
                &::before {
                    background-color: @disabledColor;
                }
            }
        }

        .radio-icon-checked {
            position: relative;
            border: 1px solid @activeColor;

            &::before {
                display: inline-block;
                position: absolute;
                left: calc(50% - 3px);
                top: calc(50% - 3px);
                width: 6px;
                height: 6px;
                border-radius: 50%;
                content: '';
                background: @activeColor;
            }
        }
    }
}
