import { ISerialTableRowObj } from '../serialize/serialInterface';
import { ICellSpanInfo, IExcel } from './IExcel';
import ExcelJS from 'exceljs';
import ExcelTableCell from './ExcelTableCell';
import { getPxForMM, pxToPt, transDocxLengthToMM } from '../serialize/serializer';

export default class ExcelTableRow implements IExcel {
    private spanInfos: ICellSpanInfo[];

    /**
     * 表格行构建对象
     * @param tableRow 表格行json信息
     * @param prevSpans 从上一行继承下来的纵向合并单元格信息
     */
    constructor(private tableRow: ISerialTableRowObj, private prevSpans?: ICellSpanInfo[]) {
        this.spanInfos = [];
    }

    public get CellSpanInfo(): ICellSpanInfo[] {
        return this.spanInfos;
    }

    public buildTo(row: ExcelJS.Row): ExcelTableRow {
        let cellIndex = 1;
        for (const tableCell of this.tableRow.children) {
            if (tableCell.isVerticalMerged) {
                cellIndex += tableCell.columnSpan || 1;
                continue;
            }
            const cell = row.getCell(cellIndex);
            const cellBuilder = new ExcelTableCell(tableCell).buildTo(cell);
            const spanInfo = cellBuilder.cellSpanInfo;
            if (spanInfo) {
                const newSpan = {
                    ...spanInfo,
                    startCol: cellIndex,
                };
                if (!this.prevSpans.find(span => span.startCol === cellIndex)) {
                    this.CellSpanInfo.push(newSpan);
                }
                cellIndex += spanInfo.colSpan;
                continue;
            }
            cellIndex++;
        }
        if (this.tableRow.height) {
            // excel行高单位为磅
            row.height = pxToPt(getPxForMM(transDocxLengthToMM(this.tableRow.height.value))) * 1.25; // 行高有折扣
        }
        return this;
    }

}
