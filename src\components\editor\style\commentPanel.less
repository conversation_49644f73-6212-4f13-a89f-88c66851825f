@import './global.less';

.hz-editor-container {

    .comment-panel {
        position: absolute;
        top: 10px;
        left: 10px;
        width: 186px; /* 将宽度缩小1/3: 280px / 3 * 2 = 186px */
        display: flex;
        flex-direction: column;
        text-shadow: none;
    }

    .summary,
    .filter {
        width: 100%;
        display: flex;
        font-size: 10px; /* 调整字体大小以适应宽度缩小 */
        line-height: 18px; /* 调整行高以适应字体大小 */
        justify-content: space-between;
        user-select: none;
    }

    .summary {
        padding: 6px 8px; /* 调整内边距以适应宽度缩小 */
        border-bottom: 1px solid #D0D1D2;
        margin-bottom: 8px; /* 调整外边距以适应宽度缩小 */

        .left,
        .right {
            cursor: pointer;
            font-size: 12px; /* 调整字体大小 */
        }

        .center {
            font-weight: 700;
            font-size: 12px; /* 调整字体大小 */
        }
    }

    .filter {
        align-items: center;
        margin-bottom: 8px; /* 调整外边距以适应宽度缩小 */
        font-size: 10px;
        & * {
            cursor: pointer;
            font-size: 10px; /* 调整字体大小 */
        }

        .selector {
            padding: 2px 6px; /* 调整内边距以适应宽度缩小 */
            width: 91px; /* 调整宽度以适应宽度缩小 */
            height: 24px;
            background: #FFFFFF;
            border-radius: 4px;
            position: relative;

            option {
                padding: 2px 0;
                cursor: pointer;
                font-size: 10px; /* 调整字体大小 */
            }
        }

        .editor-radio {
            padding: 0 6px; /* 调整内边距以适应宽度缩小 */
            justify-content: space-between;
            flex-grow: 1;
            display: flex;
            font-size: 8px; /* 调整字体大小 */
            .radio-item {
                margin-right: 0;
                font-size: 8px; /* 调整字体大小 */
            }
        }
    }

    .sum-symbols {
        position: absolute;
        left: -30px; /* 调整位置以适应宽度缩小 */
        top: 0;
        width: 20px; /* 调整宽度以适应宽度缩小 */
        height: 20px; /* 调整高度以适应宽度缩小 */
        box-sizing: border-box;
        background: #F3F3F3;
        color: #4D4D4D;
        border-radius: 4px;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 10px; /* 调整字体大小 */
        cursor: pointer;

        &.active, &:hover {
            color: #FFFFFF;
            background-color: #00B050;
        }
    }

    .comment-add-symbol {
        position: absolute;
        cursor: pointer;
        width: 17px; /* 调整宽度以适应宽度缩小 */
        height: 17px; /* 调整高度以适应宽度缩小 */
        display: flex;
        box-sizing: border-box;
        justify-content: center;
        align-items: center;
        background-color: #FFFFFF;

        &:hover {
            background-color: #f2f2f2;
        }
    }

    .comment {
        width: 100%;
        cursor: pointer;
        position: relative;

        * {
            box-sizing: border-box;
        }

        background: #FFFFFF;
        border-top: 2px solid #FFFFFF; /* 调整边框粗细以适应宽度缩小 */
        box-shadow: 0px 3px 15px rgba(77, 77, 77, 0.2); /* 调整阴影以适应宽度缩小 */
        border-radius: 4px;

        font-size: 10px; /* 调整字体大小 */
        line-height: 10px; /* 调整行高以适应字体大小 */
        font-weight: 700;
        padding-bottom: 10px; /* 调整内边距以适应宽度缩小 */
        margin-bottom: 10px; /* 调整外边距以适应宽度缩小 */

        .solve {
            position: absolute;
            top: 6px; /* 调整位置以适应宽度缩小 */
            right: 0px;
            font-size: 10px; /* 调整字体大小 */
            padding: 10px 10px 0 6px; /* 调整内边距以适应宽度缩小 */

            &.checked {
                color: #3664D9;
            }

            &.check {
                color: #C4C4C4;
            }

            &.iconfont:hover {
                color: #099FFF;
            }
        }

        .tools {
            display: none;
            position: absolute;
            top: 10px; /* 调整位置以适应宽度缩小 */
            right: 10px; /* 调整位置以适应宽度缩小 */
            width: 34px; /* 调整宽度以适应宽度缩小 */
            font-size: 12px; /* 调整字体大小 */
            justify-content: space-between;
            padding: 0;

            &.divide {
                border-right: 1px solid #D0D1D2;
            }

            .iconfont {
                font-size: 10px; /* 调整字体大小 */
                display: flex;
                
                .reply {
                    margin-left: 2px; /* 调整外边距以适应宽度缩小 */
                    font-size: 10px; /* 调整字体大小 */
                    line-height: 10px; /* 调整行高以适应字体大小 */
                }
            }
            .iconfont:hover {
                color: #099FFF;
            }
            
        }

        &:hover {
            .tools {
                display: flex;
            }
        }

        &.active {
            border-top: 2px solid #00B050; /* 调整边框粗细 */
        }

        &>div {
            padding: 10px 10px 0 10px; /* 调整内边距以适应宽度缩小 */
        }

        .item {
            width: 100%;
            text-align: left;

            .header {
                width: 100%;
                height: 28px; /* 调整高度以适应宽度缩小 */
                display: flex;
                position: relative;

                .user-img {
                    display: inline-block;
                    width: 28px; /* 调整宽度以适应宽度缩小 */
                    height: 28px; /* 调整高度以适应宽度缩小 */
                    background: center url('../images/doctor.png');
                    border: 1px solid #FFFFFF;
                    border-radius: 32px; /* 调整边框圆角 */
                }

                .tools {
                    top: 6px; /* 调整位置以适应宽度缩小 */
                    right: 18px; /* 调整位置以适应宽度缩小 */
                    width: 40px; /* 调整宽度以适应宽度缩小 */
                    padding-right: 4px; /* 调整内边距以适应宽度缩小 */
                }

                .title {
                    margin-left: 4px;
                    display: inline-flex;
                    flex-direction: column;
                    justify-content: center;
                    position: relative;
                    flex-grow: 1;
                        
                    .username {
                        font-weight: 700;
                        color:#00B050;
                        font-size: 12px; /* 调整字体大小 */
                        margin-bottom: 8px;
                    }

                    .label {
                        position: absolute;
                        left: 0;
                        top: 0;
                        max-width: 100%;
                        word-break: break-all;
                        display: none;
                        padding: 2px 4px;
                        z-index: 100;
                        font-size: 12px; /* 调整字体大小 */
                        line-height: 1.5em;
                        color: #737B80;
                        background-color: white;
                        border: 1px solid #ACB4C1;
                    }

                    .username:hover+.label {
                        display: block;
                        transform: translateY(-100%);
                    }

                    .datetime {
                        color: #949494;
                        font-size: 10px; /* 调整字体大小 */
                    }
                    .time {
                        color:#949494;
                        font-size: 10px; /* 调整字体大小 */
                    }
                }
            }

            .content {
                margin-top: 10px; /* 调整外边距以适应宽度缩小 */
                line-height: 18px; /* 调整行高以适应字体大小 */
                font-size: 12px; /* 调整字体大小 */
                font-weight: 700; /* 设置字体为粗体 */
                word-break: break-all;
            }

        }

        .review-bar {
            width: 100%;
            padding-top: 0;
            margin-top: 10px; /* 调整外边距以适应宽度缩小 */

            .review-input {
                width: 100%;
                position: relative;
                font-size: 12px; /* 调整字体大小 */
                line-height: 18px; /* 调整行高以适应字体大小 */

                &>textarea:first-child {
                    resize: none;
                    display: flex;
                    width: 100%;
                    padding: 4px 6px; /* 调整内边距以适应宽度缩小 */
                    font-size: 10px; /* 调整字体大小 */
                    font-weight: 700;
                    line-height: 10px; /* 调整行高以适应字体大小 */
                    box-sizing: border-box;
                    align-items: center;
                    outline: none;

                    background: #F3F3F3;
                    border-radius: 4px;
                    border: 1px solid #FFFFFF;

                    &:focus {
                        border: 1px solid #3664D9;
                        box-shadow: 0px 0px 0px 2px #DEE7FF;
                    }
                }

                &>span:last-child {
                    position: absolute;
                    top: 10px; /* 调整位置以适应宽度缩小 */
                    right: 6px; /* 调整位置以适应宽度缩小 */
                    color: #949494;

                    line-height: 24px; /* 调整行高以适应字体大小 */
                }
            }
        }

        .btns {
            color: #4D4D4D;
            display: flex;
            align-items: center;
            justify-content: space-between;

            &>span:first-child {
                text-align: center;
                min-width: 90px; /* 调整最小宽度以适应宽度缩小 */
                flex-grow: 2;
            }

            .btn {
                justify-content: center;
                align-items: center;
                padding: 4px 6px;
                cursor: pointer;
                float: right;
                margin-left: 6px; /* 调整外边距以适应宽度缩小 */
                flex-grow: 1;
                font-size: 9px; /* 调整字体大小 */
                line-height: 10px; /* 调整行高以适应字体大小 */

                width: 48px; /* 调整宽度以适应宽度缩小 */
                background-color: #FFFFFF;
                border-radius: 4px;
                border: 1px solid #FFFFFF;
            }

            .cancel:hover {
                border: 1px solid #DEE7FF;
            }

            .primary {
                color: #FFFFFF;
                background: #3664D9;
                border: 1px solid #3664D9;

                &:hover {
                    border: 1px solid #DEE7FF;
                }
            }

        }
    }

}
