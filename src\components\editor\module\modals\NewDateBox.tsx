import * as React from 'react';
import Dialog from '../../ui/Dialog';
import '../../style/datebox.less';
import { INewControlProperty, NewControlType, NewControlContentSecretType,
    NewControlDefaultSetting,
    DATE_FORMAT_STRING,
    DateBoxFormat,
    CustomPropertyElementType,
    ICustomFormatDateProps,
    dateFormat, isValidName, DateBoxMode} from '../../../../common/commonDefines';
import Input from '../../ui/Input';
import Checkbox from '../../ui/CheckboxItem';
import Button from '../../ui/Button';
import CustomProperty from './CustomProperty';
import Select from '../../ui/select/Select';
import {message} from '../../../../common/Message';
import { NewDateBox as NewDateBoxModal} from '../../NewDateBox';
import { IFRAME_MANAGER } from '../../../../common/IframeManager';
import CascadeBtn from './NewCascade';
import { ExternalDataBind } from './ExternalDataBind';

interface IDialogProps {
    documentCore: any;
    visible: boolean;
    id: string;
    property?: INewControlProperty;
    close: (name: string | number, bRefresh?: boolean) => void;
}

interface IState {
    bRefresh: boolean;
    bShowStartDateBox: boolean;
    bShowEndDateBox: boolean;
}

interface IType {
    key: string;
    value: DateBoxFormat;
}

const years = [{key: 'yyyy', value: 'yyyy'}, {key: 'yy', value: 'yy'}, {key: '无', value: ''}];
const months = [{key: 'MM', value: 'MM'}, {key: 'M', value: 'M'}, {key: '无', value: ''}];
const days = [{key: 'dd', value: 'dd'}, {key: 'd', value: 'd'}, {key: '无', value: ''}];
const hours = [{key: 'HH', value: 'HH'}, {key: '无', value: ''}];
const minutes = [{key: 'mm', value: 'mm'}, {key: '无', value: ''}];
const seconds = [{key: 'ss', value: 'ss'}, {key: '无', value: ''}];
const milliseconds = [{key: 'fff', value: 'fff'}, {key: '无', value: ''}];

export default class NewDateBox extends React.Component<IDialogProps, IState> {
    private dateBoxProperty: INewControlProperty;
    private bCustomProperty: boolean;
    private visible: any;
    private types: IType[];
    private customFormat: ICustomFormatDateProps;
    private showTime: string;
    private timeout: any;
    private customChecked: boolean;
    private bCascade: boolean;
    private resetSourceBind: boolean;
    private dataBind: any;
    constructor(props: any) {
        super(props);
        this.state = {
            bRefresh: false,
            bShowStartDateBox: false,
            bShowEndDateBox: false,
        };
        this.visible = this.props.visible;
        this.dateBoxProperty = {newControlName: undefined};

        this.types = [
            {key: DATE_FORMAT_STRING[DateBoxFormat.Date], value: DateBoxFormat.Date},
            {key: DATE_FORMAT_STRING[DateBoxFormat.DateAndHMS], value: DateBoxFormat.DateAndHMS},
            {key: DATE_FORMAT_STRING[DateBoxFormat.DateAndHM], value: DateBoxFormat.DateAndHM},
            {key: DATE_FORMAT_STRING[DateBoxFormat.HMS], value: DateBoxFormat.HMS},
        ];
        this.customFormat = {} as any;
    }

    public render(): any {
        const documentCore = this.props.documentCore;
        const startDateBox = this.state.bShowStartDateBox === true ?
        (
            <NewDateBoxModal
                documentCore={documentCore}
                newControlPropety={this.dateBoxProperty}
                scale={null}
                closeNewBoxList={this.closeDateBox.bind(this, 'startDate')}
                refresh={this.refresh}
                host={null}
                mode={DateBoxMode.StartDate}
                setStartEndDate={this.setStartEndDate}
                // className={'start-end-datebox'}
            />
        ) : null;
        const endDateBox = this.state.bShowEndDateBox === true ?
        (
            <NewDateBoxModal
                documentCore={documentCore}
                newControlPropety={this.dateBoxProperty}
                scale={null}
                closeNewBoxList={this.closeDateBox.bind(this, 'endDate')}
                refresh={this.refresh}
                host={null}
                mode={DateBoxMode.EndDate}
                setStartEndDate={this.setStartEndDate}
                // className={'start-end-datebox'}
            />
        ) : null;
        return (
            <Dialog
                visible={this.visible}
                width={350}
                open={this.open}
                title='日期框'
                footer={this.renderFooter()}
                id={this.props.id}
            >
                <div className='date-box'>
                    <div className='editor-line'>
                        <span className='title'>常规</span>
                    </div>
                    <div className={'editor-line' + (this.isNoEvent() ? ' noEvent' : '')}>
                        <div className='w-70'>名称</div>
                        <div className='right-auto'>
                            <Input
                                name='newControlName'
                                value={this.dateBoxProperty.newControlName}
                                onChange={this.onChange}
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <div className='w-70'>内部名称</div>
                        <div className='right-auto'>
                            <Input
                                name='newControlSerialNumber'
                                value={this.dateBoxProperty.newControlSerialNumber}
                                onChange={this.onChange}
                                readonly={true}
                                placeholder={''}
                                disabled={true}
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <div className='w-70'>标识符</div>
                        <div className='right-auto'>
                            <Input
                                name='identifier'
                                value={this.dateBoxProperty.identifier}
                                onChange={this.onChange}
                            />
                        </div>
                    </div>
                    <div className={'editor-line' + (this.isNoEvent() ? ' noEvent' : '')}>
                        <div className='w-70'>提示符</div>
                        <div className='right-auto'>
                            <Input
                                name='newControlInfo'
                                value={this.dateBoxProperty.newControlInfo}
                                onChange={this.onChange}
                            />
                        </div>
                    </div>
                    <div className={'editor-line' + (this.isNoEvent() ? ' noEvent' : '')}>
                        <div className='w-70'>占位符</div>
                        <div className='right-auto'>
                            <Input
                                name='newControlPlaceHolder'
                                value={this.dateBoxProperty.newControlPlaceHolder}
                                onChange={this.onChange}
                            />
                        </div>
                    </div>
                    <div className={'editor-line datebox-wrapper' + (this.isStartDateNoEvent() ? ' noEvent' : '')}>
                        <div className='w-70'>起始时间</div>
                        <div className='right-auto'>
                            <Input
                                name='startDate'
                                value={this.dateBoxProperty.startDate}
                                onChange={this.onChange}
                                readonly={true}
                                placeholder={'请选取起始时间'}
                                clickcallback={this.toggleDateBox}
                                addonAfter={this.renderSelectBtn()}
                            />
                            {startDateBox}
                        </div>
                    </div>
                    <div className={'editor-line datebox-wrapper' + (this.isEndDateNoEvent() ? ' noEvent' : '')}>
                        <div className='w-70'>截止时间</div>
                        <div className='right-auto'>
                            <Input
                                name='endDate'
                                value={this.dateBoxProperty.endDate}
                                onChange={this.onChange}
                                readonly={true}
                                placeholder={'请选取截止时间'}
                                clickcallback={this.toggleDateBox}
                                addonAfter={this.renderSelectBtn()}
                            />
                            {endDateBox}
                        </div>
                    </div>
                    <div className={'editor-line editor-multi-line' + (this.isNoEvent() ? ' noEvent' : '')}>
                        <span className='title w-70'>属性</span>
                        <div className='right-auto newcontrol-prop'>
                            <div className='editor-line'>
                                <div className='w-050'>
                                    <Checkbox
                                        name='isNewControlHidden'
                                        value={this.dateBoxProperty.isNewControlHidden}
                                        onChange={this.onChange}
                                    >
                                        隐藏
                                    </Checkbox>
                                </div>
                                <div className='w-050'>
                                    <Checkbox
                                        name='isNewControlCanntDelete'
                                        value={this.dateBoxProperty.isNewControlCanntDelete}
                                        onChange={this.onChange}
                                    >
                                        禁止删除
                                    </Checkbox>
                                </div>
                            </div>

                            <div className='editor-line'>
                                <div className='w-050'>
                                    <Checkbox
                                        name='isNewControlCanntEdit'
                                        value={this.dateBoxProperty.isNewControlCanntEdit}
                                        onChange={this.onChange}
                                    >
                                        禁止编辑
                                    </Checkbox>
                                </div>
                                <div className='w-050'>
                                    <Checkbox
                                        name='isNewControlMustInput'
                                        value={this.dateBoxProperty.isNewControlMustInput}
                                        onChange={this.onChange}
                                    >
                                        必填项
                                    </Checkbox>
                                </div>
                            </div>

                            <div className='editor-line'>
                                <div className='w-050'>
                                    <Checkbox
                                        name='isNewControlShowBorder'
                                        value={this.dateBoxProperty.isNewControlShowBorder}
                                        onChange={this.onChange}
                                    >
                                        显示边框
                                    </Checkbox>
                                </div>

                                <div className='w-050'>
                                    <Checkbox
                                        name='isNewControlReverseEdit'
                                        value={this.dateBoxProperty.isNewControlReverseEdit}
                                        onChange={this.onChange}
                                    >
                                        反向编辑
                                    </Checkbox>
                                </div>
                            </div>

                            <div className='editor-line'>
                                <div className='w-050'>
                                    <Checkbox
                                        name='isNewControlHiddenBackground'
                                        value={this.dateBoxProperty.isNewControlHiddenBackground}
                                        onChange={this.onChange}
                                    >
                                        隐藏背景色
                                    </Checkbox>
                                </div>
                                <div className='w-050'>
                                    <Checkbox
                                        name='tabJump'
                                        value={this.dateBoxProperty.tabJump}
                                        onChange={this.onChange}
                                    >
                                        键盘跳转
                                    </Checkbox>
                                </div>
                            </div>
                            <div className='editor-line'>
                                <div className='w-050'>
                                    <Checkbox
                                        name='printSelected'
                                        value={this.dateBoxProperty.printSelected}
                                        onChange={this.onChange}
                                    >
                                        打印时隐藏
                                    </Checkbox>
                                </div>
                            </div>

                            <div className='editor-line'>
                                <CustomProperty
                                    name='customProperty'
                                    properties={this.dateBoxProperty.customProperty}
                                    documentCore={this.props.documentCore}
                                    onChange={this.onChange}
                                    close={this.onClose}
                                    id='bCustomProperty'
                                    visible={this.bCustomProperty}
                                    type={CustomPropertyElementType.NewControl}
                                />
                            </div>
                        </div>
                    </div>
                    <div className={'editor-line editor-multi-line' + (this.isNoEvent() ? ' noEvent' : '')}>
                        <span className='title w-70'>日期格式</span>
                        <div className='right-auto'>
                            {this.renderFormatList()}
                        </div>
                    </div>
                    <div className={'editor-line editor-multi-line' + (this.isNoEvent() ? ' noEvent' : '')}>
                        <span className='w-70' />
                        <div className='right-auto'>
                            <div className='editor-line custom-date'>
                                <Checkbox
                                    name='isNewControlHiddenBackground'
                                    value={this.customChecked}
                                    onChange={this.openCustomChange}
                                >
                                    自定义日期格式
                                </Checkbox>
                            </div>
                            {this.renderCustomFormat()}
                            <div className='editor-line'>示例：{this.showTime}</div>
                        </div>
                    </div>
                    <div className='editor-line'>
                        <div className='w-70'>
                            级联
                        </div>
                        <div className='right-auto'>
                            <CascadeBtn
                                visible={this.bCascade}
                                id='bCascade'
                                controlName={this.dateBoxProperty.newControlName}
                                documentCore={this.props.documentCore}
                                properties={this.dateBoxProperty.cascade}
                                name='cascade'
                                close={this.onClose}
                                onChange={this.onChange}
                                type={this.dateBoxProperty.newControlType}
                            />
                        </div>
                    </div>
                    <ExternalDataBind
                        name={this.dateBoxProperty.newControlName}
                        id='externalDataBind'
                        visible={this.visible}
                        documentCore={this.props.documentCore}
                        onChange={this.onChange}
                        close={this.onClose}
                        properties={this.dataBind}
                        resetId={'resetSourceBind'}
                    />
                </div>
            </Dialog>
        );
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        this.visible = nextProps.visible;
    }

    private renderCustomFormat(): any {
        let className = 'custom-format';
        const disabled = this.dateBoxProperty.dateBoxFormat !== DateBoxFormat.AutoCustom;
        if (disabled) {
            className += ' un-active';
        }

        return (
            <div className={className}>
                <div className='editor-line  custom-table'>
                    <div>
                        <Select
                            data={years}
                            value={this.customFormat.year}
                            name='year'
                            disabled={disabled}
                            onChange={this.customChange}
                        />
                    </div>
                    <div>
                        <Input
                            value={this.customFormat.yearAndMonth}
                            disabled={disabled}
                            name='yearAndMonth'
                            unDeleted={true}
                            onChange={this.customChange}
                        />
                    </div>
                </div>
                <div className='editor-line'>
                    <div className='w-050  custom-table'>
                        <div>
                            <Select
                                data={months}
                                disabled={disabled}
                                value={this.customFormat.month}
                                name='month'
                                onChange={this.customChange}
                            />
                        </div>
                        <div>
                            <Input
                                value={this.customFormat.monthAnyDay}
                                disabled={disabled}
                                name='monthAnyDay'
                                unDeleted={true}
                                onChange={this.customChange}
                            />
                        </div>
                    </div>
                    <div className='w-050  custom-table'>
                        <div>
                            <Select
                                data={days}
                                disabled={disabled}
                                value={this.customFormat.day}
                                name='day'
                                onChange={this.customChange}
                            />
                        </div>
                        <div>
                            <Input
                                value={this.customFormat.dayAfter}
                                disabled={disabled}
                                name='dayAfter'
                                unDeleted={true}
                                onChange={this.customChange}
                            />
                        </div>
                    </div>
                </div>
                <div className='editor-line'>
                    <div className='w-050  custom-table'>
                        <div>
                            <Select
                                data={hours}
                                disabled={disabled}
                                value={this.customFormat.hour}
                                name='hour'
                                onChange={this.customChange}
                            />
                        </div>
                        <div>
                            <Input
                                value={this.customFormat.hourAndMinute}
                                disabled={disabled}
                                name='hourAndMinute'
                                unDeleted={true}
                                onChange={this.customChange}
                            />
                        </div>
                    </div>
                    <div className='w-050 custom-table'>
                        <div>
                            <Select
                                data={minutes}
                                disabled={disabled}
                                value={this.customFormat.minute}
                                name='minute'
                                onChange={this.customChange}
                            />
                        </div>
                        <div>
                            <Input
                                value={this.customFormat.minuteAndSecond}
                                disabled={disabled}
                                name='minuteAndSecond'
                                unDeleted={true}
                                onChange={this.customChange}
                            />
                        </div>
                    </div>
                </div>
                <div className='editor-line'>
                    <div className='w-050  custom-table'>
                        <div>
                            <Select
                                data={seconds}
                                disabled={disabled}
                                value={this.customFormat.second}
                                name='second'
                                onChange={this.customChange}
                            />
                        </div>
                        <div>
                            <Input
                                value={this.customFormat.secondAfter}
                                disabled={disabled}
                                name='secondAfter'
                                unDeleted={true}
                                onChange={this.customChange}
                            />
                        </div>
                    </div>
                    <div className='w-050  custom-table'>
                        <div>
                            <Select
                                data={milliseconds}
                                disabled={disabled}
                                value={this.customFormat.millisecond}
                                name='millisecond'
                                onChange={this.customChange}
                            />
                        </div>
                        <div>
                            <Input
                                value={this.customFormat.millisecondAfter}
                                disabled={disabled}
                                name='millisecondAfter'
                                unDeleted={true}
                                onChange={this.customChange}
                            />
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    private renderFormatList(): any {
        const activeValue = this.dateBoxProperty.dateBoxFormat;
        const list = this.types.map((item) => {
            let className = null;
            if (item.value === activeValue) {
                className = 'active';
            }
            return (
                <li className={className} key={item.key} onClick={this.onClick.bind(this, item)}>{item.key}</li>
            );
        });
        return (
            <ul className='format-list'>
                {list}
            </ul>
        );
    }

    private renderFooter(): any {
        return (
            <span>
                <Button onClick={this.close}>取消</Button>
                <Button type='primary' onClick={this.confirm}>确认</Button>
            </span>
        );
    }

    private onClose = (id: string, bRefresh: boolean): void => {
        this[id] = false;
        if ((id === 'bCustomProperty' || 'bCascade' === id) && bRefresh) {
            this.setState({bRefresh: !this.state.bRefresh});
        } else if ('externalDataBind' === id && bRefresh) {
            this.resetSourceBind = false;
            this.setState({bRefresh: !this.state.bRefresh});
        }
    }

    private onClick = (item: IType): void => {
        this.dateBoxProperty.dateBoxFormat = item.value;
        this.customChecked = false;
        this.setShowTime();
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private openCustomChange = (value: any, name: string): void => {
        this.customChecked = value;
        this.dateBoxProperty.dateBoxFormat = DateBoxFormat.AutoCustom;
        this.setShowTime();
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private open = (): void => {
        const props = this.props.property;
        // connection btw existing props and current props
        const newControl: INewControlProperty = this.dateBoxProperty = {} as any;
        if (props === undefined) {
            // init props here first
            this.init();
            newControl.newControlName = this.props.documentCore.makeUniqueNewControlName(NewControlType.DateTimeBox);
            this.customChecked = false;
        } else {
            const keys = Object.keys(props);
            keys.push('cascade');
            keys.forEach((key) => {
                const val = props[key];
                newControl[key] = val;
            });
            // const actNewControl = this.props.documentCore.getNewControlByName(newControl.newControlName);
            const customFormat = props.customFormat;
            if (!customFormat || Object.keys(customFormat).length === 0) {
                this.initCustomProps();
                this.customChecked = false;
            } else {
                this.customFormat = {...customFormat};
                this.customChecked = true;
            }
        }
        this.setShowTime();
        this.dataBind = newControl.externalDataBind;
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private close = (bRefresh?: boolean): void => {
        this.props.close(this.props.id, bRefresh);
        this.visible = false;
        this.closeAllDateBox();
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private confirm = (): void => {
        const documentCore = this.props.documentCore;
        IFRAME_MANAGER.setDocId(documentCore.getCurrentId());
        const props = this.props.property;
        const dateBoxProperty = this.dateBoxProperty;
        if (!isValidName(dateBoxProperty.newControlName)) {
            message.error('名称不符合规范，请重新输入');
            return;
        }
        if (dateBoxProperty.identifier && !isValidName(dateBoxProperty.identifier)) {
            message.error('标识符不符合规范，请重新输入');
            return;
        }
        if ((!props || props.newControlName !== dateBoxProperty.newControlName)
            && !documentCore.checkNewControlName(dateBoxProperty.newControlName)) {
            message.error('已存在该名字，请重新命名');
            return;
        }
        if (dateBoxProperty.dateBoxFormat === DateBoxFormat.AutoCustom) {
            dateBoxProperty.customFormat = this.customFormat;
        } else {
            dateBoxProperty.customFormat = null;
        }

        if (this.resetSourceBind) {
            dateBoxProperty.externalDataBind = {
                sourceObj: '',
                sourceKey: '',
                bReadOnly: false,
                commandUpdate: 1,
            }

            this.resetSourceBind = false;
        } else if (this.dataBind) {
            dateBoxProperty.externalDataBind = this.dataBind;
        }

        if (props === undefined) {
            documentCore.addNewControl(dateBoxProperty);
        } else {
            // update datebox text due to potential date format change
            // this.updateDateBoxText();

            documentCore.setNewControlProperty(dateBoxProperty, props.newControlName);
        }

        this.close(true);
    }

    private setShowTime(): void {
        let dateStr: string = DATE_FORMAT_STRING[this.dateBoxProperty.dateBoxFormat];
        if (!dateStr) {
            const custom = this.customFormat;
            dateStr = this.getYear() + this.getMonth() + this.getDay() + this.getHour() + this.getMinute()
                + this.getSecond() + this.getMillisecond();
        }
        this.showTime = dateFormat(new Date(), dateStr);
    }

    private init(): void {
        const dateBoxProperty = this.dateBoxProperty;
        dateBoxProperty.newControlName = undefined;
        dateBoxProperty.newControlInfo = undefined;
        dateBoxProperty.newControlPlaceHolder = NewControlDefaultSetting.DefaultPlaceHolderContent;
        dateBoxProperty.newControlType = NewControlType.DateTimeBox;
        dateBoxProperty.isNewControlHidden = false;
        dateBoxProperty.isNewControlCanntDelete = false;
        dateBoxProperty.isNewControlCanntEdit = false;
        dateBoxProperty.isNewControlMustInput = false;
        dateBoxProperty.isNewControlShowBorder = true;
        dateBoxProperty.isNewControlReverseEdit = false;
        dateBoxProperty.isNewControlHiddenBackground = true;
        dateBoxProperty.newControlDisplayType = NewControlContentSecretType.DontSecret;
        dateBoxProperty.customProperty = undefined;
        dateBoxProperty.dateBoxFormat = DateBoxFormat.DateAndHMS;
        dateBoxProperty.startDate = '无';
        dateBoxProperty.endDate = '无';
        dateBoxProperty.tabJump = true;
        dateBoxProperty.cascade = undefined;
        dateBoxProperty.identifier = undefined;
        dateBoxProperty.externalDataBind = undefined;
        dateBoxProperty.printSelected = false;
        this.resetSourceBind = false;
        this.initCustomProps();
    }

    private initCustomProps(): void {
        this.customFormat = {
            year: years[0].value,
            month: months[0].value,
            day: days[0].value,
            hour: hours[0].value,
            minute: minutes[0].value,
            second: seconds[0].value,
            millisecond: '',
            yearAndMonth: '-',
            monthAnyDay: '-',
            dayAfter: ' ',
            hourAndMinute: ':',
            minuteAndSecond: ':',
            secondAfter: '',
        };
    }

    private onChange = (value: any, name: string): void => {
        if ('resetSourceBind' !== name) {
            if ('externalDataBind' !== name) {
                this.dateBoxProperty[name] = value;
            } else {
                this.dataBind = value;
            }
        } else {
            this.resetSourceBind = true;
        }
    }

    private toggleDateBox = (name?: string): void => {
        if (name === 'startDate') {
            this.setState({bShowStartDateBox: !this.state.bShowStartDateBox});
        } else if (name === 'endDate') {
            this.setState({bShowEndDateBox: !this.state.bShowEndDateBox});
        }
    }

    private closeDateBox = (name?: string): void => {
        if (name === 'startDate') {
            this.setState({bShowStartDateBox: false});
        } else if (name === 'endDate') {
            this.setState({bShowEndDateBox: false});
        }
    }

    private closeAllDateBox(): void {
        if (this.state.bShowStartDateBox === true) {
            this.setState({bShowStartDateBox: false});
        }
        if (this.state.bShowEndDateBox === true) {
            this.setState({bShowEndDateBox: false});
        }
    }

    private setStartEndDate = (mode?: DateBoxMode, selectedDate?: string): void => {
        if (mode === DateBoxMode.StartDate) {
            const startDateParsed = Date.parse(selectedDate);
            const endDateParsed = Date.parse(this.dateBoxProperty.endDate);
            if (!Number.isNaN(endDateParsed)) {
                if (startDateParsed >= endDateParsed) {
                    message.error('截止时间需要大于起始时间');
                    this.dateBoxProperty.startDate = '无';
                } else {
                    this.dateBoxProperty.startDate = selectedDate;
                }
            } else {
                // enddate not set yet
                this.dateBoxProperty.startDate = selectedDate;
            }
        } else if (mode === DateBoxMode.EndDate) {
            const startDateParsed = Date.parse(this.dateBoxProperty.startDate);
            const endDateParsed = Date.parse(selectedDate);
            if (!Number.isNaN(startDateParsed)) {
                if (startDateParsed >= endDateParsed) {
                    message.error('截止时间需要大于起始时间');
                    this.dateBoxProperty.endDate = '无';
                } else {
                    this.dateBoxProperty.endDate = selectedDate;
                }
            } else {
                // startdate not set yet
                this.dateBoxProperty.endDate = selectedDate;
            }
        }
    }

    private refresh = (): void => {
        this.setState({});
    }

    private customChange = (value: any, name: string): void => {
        this.customFormat[name] = value;
        clearTimeout(this.timeout);
        this.timeout = setTimeout(() => {
            this.setShowTime();
            this.setState({bRefresh: !this.state.bRefresh});
        }, 300);
    }

    private updateDateBoxText(): void {
        // current control text change
        const newControl = this.props.documentCore.getNewControlByName(this.dateBoxProperty.newControlName);
        if (newControl) {
          let dateTime = newControl.getDateTime();
          if (dateTime != null) {
            const dateBoxFormat = this.dateBoxProperty.dateBoxFormat; // yet to be changed
            if (dateBoxFormat === DateBoxFormat.Date) {
              dateTime = dateTime.slice(0, 10);
            } else if (dateBoxFormat === DateBoxFormat.DateAndHM) {
              dateTime = dateTime.slice(0, 16);
            } else if (dateBoxFormat === DateBoxFormat.DateAndHMS) {
              // do nothing
            } else if (dateBoxFormat === DateBoxFormat.HMS) {
              dateTime = dateTime.slice(11);
            } else {
              // what else? lol
            }
            newControl.setNewControlText(dateTime);
          }
        }
    }

    private getYear(): string {
        const custom = this.customFormat;
        if (custom.year) {
            return custom.year + (custom.yearAndMonth || '');
        }
        return '';
    }

    private getMonth(): string {
        const custom = this.customFormat;
        if (custom.month) {
            return custom.month + (custom.monthAnyDay || '');
        }
        return '';
    }

    private getDay(): string {
        const custom = this.customFormat;
        if (custom.day) {
            return custom.day + (custom.dayAfter || '');
        }
        return '';
    }

    private getHour(): string {
        const custom = this.customFormat;
        if (custom.hour) {
            return custom.hour + (custom.hourAndMinute || '');
        }
        return '';
    }

    private getMinute(): string {
        const custom = this.customFormat;
        if (custom.minute) {
            return custom.minute + (custom.minuteAndSecond || '');
        }
        return '';
    }

    private getSecond(): string {
        const custom = this.customFormat;
        if (custom.second) {
            return custom.second + (custom.secondAfter || '');
        }
        return '';
    }

    private getMillisecond(): string {
        const custom = this.customFormat;
        if (custom.millisecond) {
            return custom.millisecond + (custom.millisecondAfter || '');
        }
        return '';
    }

    private isNoEvent(): boolean {
        return this.state.bShowStartDateBox === true || this.state.bShowEndDateBox === true;
    }

    private isStartDateNoEvent(): boolean {
        return this.state.bShowEndDateBox === true;
    }

    private isEndDateNoEvent(): boolean {
        return this.state.bShowStartDateBox === true;
    }

    private renderSelectBtn(): any {
        return (<span className='select-btn' />);
    }
}
