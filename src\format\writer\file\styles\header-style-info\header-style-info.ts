import { XmlComponent } from '../../xml-components';
import { HeaderStyleInfoAttributes } from './header-style-info-attributes';
import { HeaderPage } from './header-page';
import { HeaderStyleInfoType } from './header-style-info-type';

export class HeaderStyleInfo extends XmlComponent {

    private headerPage: HeaderPage;
    private headerStyleInfoAttributes: HeaderStyleInfoAttributes;

    constructor(type: string) {

        super('w:headerStyleInfo');

        this.headerStyleInfoAttributes = new HeaderStyleInfoAttributes({type});

        this.root.push(this.headerStyleInfoAttributes);

    }

    public setType(type: HeaderStyleInfoType): void {
        // 0 - attr
        // this.root[0]['root'].type = type;
        this.headerStyleInfoAttributes.setType(type);
    }

    public addHeaderPage(index: number, type?: HeaderStyleInfoType, startPage?: number, endPage?: number): void {
        this.headerPage = new HeaderPage(index, type, startPage, endPage);
        this.root.push(this.headerPage);
    }

    public setHeaderPage(index: number, startPage: number, endPage: number): void {
        // console.log(this.root);

        // [0] is headerStyleInfoAttributes, others should be HeaderPage
        if (this.root[index] instanceof HeaderPage) {
            this.headerPage = this.root[index] as HeaderPage;
            this.headerPage.setHeaderPage(startPage, endPage);
        } else {
            // console.log(`Error: "header${index}" of which you'd like to set page range is not available`);
        }
    }

}
