import { DocumentCore, IDrawSelectionBounds } from '../../../../model/DocumentCore';
import MouseEventHandler, { MouseEventType } from '../../../../common/MouseEventHandler';
import { RenderSectionBackgroundType, VIEW_SCALE, getPageElement,
    ViewModeType,
    HEADERFOOTER_SWITCH,
    numtoFixed2,
    IFixedCellType,
    ResultType,
    isCommentElement,
    CommentOperatorType} from '../../../../common/commonDefines';
import {GlobalEvent as gEvent, GlobalEventName as gEventName} from '../../../../common/GlobalEvent';
import EditorEvent from '../../../../common/GlobalEditorEvent'; // connect gEventName.Mousedown with mousedown event
import {Document} from './Document';
import { getImageSelectionName } from './Image';
import { getPagePadding } from '../../../../common/commonMethods';

export default class MouseEvent {
    public myRef: any;
    public docId: number;
    private documentCore: DocumentCore;
    private host: Document;
    private gMouseEvent: MouseEventHandler;
    // private viewCursorPoint: number[] = [];
    private pageIndex: number;
    private selections: IDrawSelectionBounds = null;
    private bSelected: boolean = false;
    private bMouseDown: boolean;
    // private timeout: any;
    // private dom: HTMLDivElement;
    private pageIndex1: number;
    private viewMode: ViewModeType;
    // curposType before dblClick
    // private oldCurposType: number;
    private bInHeaderFooterPrev: boolean;
    private _mousedownY: number;
    private _imageName: string;

    private cursorType: any;
    private bMouseUp: boolean;
    private bMoveSelectionToEmr: boolean;   // 是否拖选到EMR中了
    private _clickedCommentSymbol: boolean;
    constructor(host: Document) {
        this.host = host;
        this.myRef = host.currentRef;
        this.docId = host.docId;
        this.documentCore = host.documentCore;
        this.gMouseEvent = new MouseEventHandler();
        this.addEvents();
        const evet = new EditorEvent(this);
        this.pageIndex = 0;
        this.pageIndex1 = 0;
        this.bInHeaderFooterPrev = false;
        this._clickedCommentSymbol = false;
    }

    public getPageIndex(): number {
        return this.pageIndex1;
    }

    public getCurPageIndex(): number {
        return this.pageIndex;
    }

    public setPageIndex(pageIndex: number): void {
        this.pageIndex1 = pageIndex;
    }

    public isSelected(): boolean {
        return this.bSelected;
    }

    public setSelections(): void {
        const documentCore = this.documentCore;
        this.selections = documentCore.getSelectionBounds();
        if (true) {
            this.renderSection2();
        } else {
            this.renderSection();
        }
        this.bMouseDown = true;
        setTimeout(() => {
            this.bMouseDown = false;
        }, 0);
    }

    /**
     * 清除选区
     * @param selected
     */
    public clearSection = () => {
        if (true) {
            this.clearSection2();
            return;
        }
        if (this.selections) {
            const lines = this.selections.lines;
            if (lines) {
                this.clearSectionBackground(
                    lines,
                    RenderSectionBackgroundType.ParagragphLineSelection,
                );
            }

            const drawCells = this.selections.cells;
            if (drawCells) {
                this.clearSectionBackground(
                    drawCells,
                    RenderSectionBackgroundType.TableCellSelection,
                );
            }
        }

        this.selections = null;
        this.setSelect(false);
    }

    public setViewScale(scale: number): void {
        const documentCore = this.documentCore;
        let prevScale = documentCore.getViewScale() + scale;
        const max = Math.round(VIEW_SCALE.max / 100);
        if (prevScale > max) {
            if (scale > 0) {
                return;
            }
            prevScale = max;
        }

        const min = VIEW_SCALE.min / 100;
        if (prevScale < min) {
            if (scale < 0) {
                return;
            }

            prevScale = min;
        }
    }

    public isMousdown(): boolean {
        return this._mousedownY !== undefined;
    }

    public setCursorByXY(x: number, y: number, event: any): void {
        const pageNode = getPageElement(event.target);
        if (!pageNode) {
            return;
        }

        const pageId = this.getPageId(pageNode);
        const scale = this.host.getScale();
        const option = getPagePadding(this.documentCore);
        const pointX = x / scale + option.left;
        const pointY = y / scale + option.top;
        this.documentCore.moveCursorToXY(pageId, pointX, pointY);
    }

    // public getMousedownY(): number {
    //     return this._mousedownY;
    // }

    private setViewScale1 = (prevScale: number, option?: object): void => {
        if (this.documentCore.isInlineMode()) {
            // inline模式禁用视图比例缩放
            return;
        }
        const res = this.documentCore.setViewScale(prevScale);
        if (res === 0) {
            this.host.refresh()
            .then(() => {
                this.setPageMove();
            });
            const bVisible = this.host.isCusorVisible();
            if (bVisible === true) {
                const cursor = this.host.getCursor2();
                cursor.setCursorVisible2(false);
                setTimeout(() => {
                    cursor.setCursorVisible2(false);
                }, 0);
            }
        }
        if (option) {
            option['result'] = res;
        }
    }

    private setPageMove(): void {
        const container = this.host.getContainer();
        const parentNode = container.parentNode as HTMLElement;
        const firstChild = container.firstChild as HTMLElement;
        const boxWidth = parentNode.clientWidth;
        const childWidth = firstChild.clientWidth;
        const subWidth = childWidth - boxWidth;
        if (subWidth > 0) {
            // container.style.sc = -subWidth / 2 + 'px';
            // container.style.width = boxWidth  + subWidth + 'px';
            parentNode.scrollLeft = subWidth / 2;
        } else {
            parentNode.scrollLeft = 0;
            // container.style.width = '';
        }
    }

    private getViewMode(): ViewModeType {
        if (this.viewMode === undefined) {
            this.viewMode = this.documentCore.getViewMode();
        }
        return this.viewMode;
    }

    // public componentDidMount(): void {
    //     const evet = new EditorEvent(this);
    // }

    private addEvents(): void {
        // gEvent.addEvent(this.docId, gEventName.Blur, this.blur);
        gEvent.addEvent(this.docId, gEventName.Mousedown, this.handleMouseDown);
        gEvent.addEvent(this.docId, gEventName.Mousemove, this.handleMouseMove);
        gEvent.addEvent(this.docId, gEventName.Mouseup, this.handleMouseUp);
        gEvent.addEvent(this.docId, gEventName.UnMounted, this.removeEvents);
        gEvent.addEvent(this.docId, gEventName.CursorVisible, this.cursorVisible);
        gEvent.addEvent(this.docId, gEventName.ViewScale, this.setViewScale1);
        gEvent.addEvent(this.docId, gEventName.Dblclick, this.handleDoubleClick);
        // table
        // gEvent.addEvent(this.docId, gEventName.TableEvent, this.handleTableEvent);
        // gEvent.addEvent(this.docId, 'deleteTableCells', this.handleTableEvent);
        // gEvent.addEvent(this.docId, 'splitTableCells', this.handleTableEvent);
        const dom = this.getContainer();
        const doc = dom.ownerDocument;
        dom.ownerDocument.addEventListener('mouseup', this.mouseTrueUp);
        dom.ownerDocument.addEventListener('keydown', this.resetDocumentCursor);
        if (doc !== document) {
            document.addEventListener('mouseup', this.mouseTrueUp);
            document.addEventListener('keydown', this.resetDocumentCursor);
        }
        // gEvent.addEvent(this.docId, gEventName.Click, this.handleClick);
    }

    private removeEvents = (): void => {
        gEvent.deleteEvent(this.docId, gEventName.Mousedown, this.handleMouseDown);
        gEvent.deleteEvent(this.docId, gEventName.Mousemove, this.handleMouseMove);
        gEvent.deleteEvent(this.docId, gEventName.Mouseup, this.handleMouseUp);
        gEvent.deleteEvent(this.docId, gEventName.CursorVisible, this.cursorVisible);
        gEvent.deleteEvent(this.docId, gEventName.ViewScale, this.setViewScale1);
        gEvent.deleteEvent(this.docId, gEventName.Dblclick, this.handleDoubleClick);
        // table
        // gEvent.deleteEvent(this.docId, gEventName.TableEvent, this.handleTableEvent);
        // gEvent.deleteEvent(this.docId, 'deleteTableCells', this.handleTableEvent);
        // gEvent.deleteEvent(this.docId, 'splitTableCells', this.handleTableEvent);
        const dom = this.getContainer();
        const doc = dom.ownerDocument;
        if (!dom || !doc) {
            console.log('undelete event mouseEvent');
            return;
        }
        dom.ownerDocument.removeEventListener('mouseup', this.mouseTrueUp);
        dom.ownerDocument.removeEventListener('keydown', this.resetDocumentCursor);
        if (doc !== document) {
            document.removeEventListener('mouseup', this.mouseTrueUp);
            document.removeEventListener('keydown', this.resetDocumentCursor);
        }
        // gEvent.deleteEvent(this.docId, gEventName.Click, this.handleClick);
    }

    private mouseTrueUp = (e: any) => {
        if (this.bMouseUp === false) {
            const gMouseEvent = this.gMouseEvent;
            this.documentCore.mouseButtonUp(
                gMouseEvent,
                this.pageIndex,
                gMouseEvent.pointX,
                gMouseEvent.pointY,
            );
            this.bMouseUp = true;
            this.bMoveSelectionToEmr = this.documentCore.isSelectionUse();
        }
    }

    private cursorVisible = (bShow: boolean, bVisible: boolean): void => {
        if (bShow === false || this.bMouseDown === true) {
            return;
        }
        if (this.bSelected !== true) {
            return;
        }

        this.clearSection();
    }

    /**
     * 将EMR中的光标重新设置回editor，仅对backspace、delete有效
     * @param event
     */
    private resetDocumentCursor = (event: any): void => {
        if (this.bMoveSelectionToEmr && (8 === event.keyCode || 46 === event.keyCode)) {
            this.bMoveSelectionToEmr = false;
            this.host.setCursorTrueVisible(false);
            if (ResultType.Success === this.documentCore.removeSelectedContent()) {
                this.clearSection2();
                this.host.handleRefresh();
            } else {
                this.host.focus();
            }
        }
    }

    private viewModeTag = (e: any) => {
        /* IFTRUE_WATER return FITRUE_WATER */
        const target = e.target;
        const className = target.className;
        if (typeof className === 'string' && className.indexOf('view-mode-span') > -1) {
            const documentCore = this.documentCore;
            if (documentCore.isProtectedMode()) {
                // 只读模式不允许切换视图
                return;
            }
            const viewMode = documentCore.getViewMode();
            if (viewMode === ViewModeType.WebView) {
                documentCore.setViewMode(ViewModeType.BreakPageView);
            } else {
                documentCore.setViewMode(ViewModeType.WebView);
            }
            this.host.handleRefresh();
            if (this.host.isSelected() !== true) {
                this.host.setCursorVisible(true);
            }
            gEvent.setEvent(this.docId, gEventName.ViewMode);
        }
    }

    /**
     * 处理mouse down
     */
    private handleMouseDown = (event: any) => {
        if (isCommentElement(event.target)) {
            this._clickedCommentSymbol = true;
            return;
        }
        this._clickedCommentSymbol = false;

        this.host.clearTaskList();
        this.bMouseUp = false;
        this.host.setMouseDown(true);
        this.bMouseDown = true;
        this._mousedownY = event.layerY;
        const bRightBtn = event.buttons === 2;
        // this._bMousedown = true;
        // console.log('editor mousedown')
        // console.log(event.pageX, event.pageY)
        // tslint:disable-next-line: no-console
        // console.log(event, new Date().toLocaleString());
        const documentCore = this.documentCore;
        // console.log(event.detail);
        // console.log(documentCore.getDocument());
        // console.log(documentCore.getDocument().hdrFtr);
        // console.log(documentCore.getDocument().getDrawingObjects()) // console would not show static obj

        // smooth clicking in modal or mousedown event not expected to influence editor
        // const isClickOnNonSVGDOM = this.isCursorOnNonSVGDOM(event);
        // if (isClickOnNonSVGDOM) {
        //     return null;
        // }

        // if (true === this.isCursorOnToolBar(event)) {
        //     return null;
        // }

        // if (TableMenuModalType.None !== this.state.tablePopMenuModalType
        //     && 'contextMenu-item' !== event.target.className) {
        //     this.setState({ tablePopMenuModalType: TableMenuModalType.None });
        // }

        // const callbackOption = { isStop: false };
        // gEvent.setEvent(
        //     this.docId,
        //     'editorStopUnSelection',
        //     callbackOption,
        //     event,
        // );
        // if (callbackOption.isStop === true) {
        //     return null;
        // }

        if ( documentCore.isMovingTableBorder() ) {
            documentCore.resetMovingTableNewBorder();
            this.host.updateCursor2();
        }

        // this.scrollTool.mousedown(true);
        this.viewMode = undefined;
        const pageNode = getPageElement(event.target);
        if (!pageNode) {
            this.documentCore.removeSelection();
            this.clearSection();
            this.host.setCursorVisible(false);
            return;
        }
        let layerY = event.layerY;
        const bPrint = this.host.isPrint();
        if ( bPrint ) {
            layerY = event.offsetY;
        }
        const scale = this.host.getScale();
        const position = getPagePadding(this.documentCore);
        let pointX = event.layerX / scale + position.left;
        let pointY = layerY / scale + position.top;

        if (bRightBtn && this.selections) {
            const {cells, lines} = this.selections;
            let arrs = [];
            if (cells && cells.length > 0) {
                arrs = arrs.concat(cells);
            }
            if (lines && lines.length > 0) {
                arrs = arrs.concat(lines);
            }
            if (arrs.length) {
                for (let index = 0, length2 = arrs.length; index < length2; index++) {
                    const line = arrs[index];
                    if (line.x > pointX) {
                        continue;
                    }
                    if (line.y > pointY) {
                        continue;
                    }
                    const x2 = line.x + line.width;
                    if (x2 < pointX) {
                        continue;
                    }
                    const y2 = line.y + line.height;
                    if (y2 < pointY) {
                        continue;
                    }
                    return;
                }
            }
        }

        const logicDocument = documentCore.getDocument();
        const bInHeaderFooter = documentCore.isInHeaderFooter();
        // check if click on image
        // IMAGE_FLAGS.isImageOnClick = false;
        // IMAGE_FLAGS.isHandlerOnClick = false;
        // logicDocument.setImageOnClick(false);
        // logicDocument.setHandlerOnClick(false);
        this._imageName = null;
        if (event.target.tagName === 'image' &&
            event.target.className.baseVal.includes('select-button') === false) { // select button, not real image
            // this._imageName = event.target.getAttribute('name');
            // this.host.cursorBlur();
            this.host.setCursorVisible(false);
            documentCore.removeSelection();
            // IMAGE_FLAGS.isImageOnClick = true;
            // logicDocument.setImageOnClick(true);
            logicDocument.setHandlerOnClick(false);

            // const id = event.target && event.target.id;
            if (event.target.getAttribute('id')
                    .includes('-hdrFtr-') === bInHeaderFooter) {
                const selectionInfo = getImageSelectionName(event.target);
                this._imageName = selectionInfo;
                documentCore.setImageSelectionInfo(selectionInfo);
            }
        } else if (
            event.target.tagName === 'circle' &&
            event.target.className.baseVal.includes('image-processor')
        ) {
            // this.cursorBlur();
            // IMAGE_FLAGS.isImageOnClick = true;
            // logicDocument.setImageOnClick(true);
            // IMAGE_FLAGS.isHandlerOnClick = true;
            logicDocument.setHandlerOnClick(true);

            // const id = event.target && event.target.id;
            const selectionInfo = getImageSelectionName(event.target);
            this._imageName = selectionInfo;
            documentCore.setImageSelectionInfo(selectionInfo);
            return;
        } else {
            // this.cursorFocused();
            // documentCore.removeSelection();
            if ( documentCore.getImageSelectionInfo() ) {
                this.clearSection();
                documentCore.removeSelection();
            }
            documentCore.setImageSelectionInfo(null);
        }

        // set mouseEvent
        // this.gMouseEvent.button = event.button;
        // this.gMouseEvent.bShiftKey = event.shiftKey;
        // this.gMouseEvent.type = MouseEventType.MouseButtonDown;
        // this.gMouseEvent.pointX = pointX / scale;
        // this.gMouseEvent.pointY = pointY;
        // this.gMouseEvent.clickCount = event.detail;

        // store curposType before dblClick
        // if (event.detail === 1) {
        //     this.oldCurposType = documentCore.getDocument().curPos.type;
        // }
        if (event.detail === 2) {
            this.bInHeaderFooterPrev = bInHeaderFooter;
        }

        const pageId = this.getPageId(pageNode);

        let selection = documentCore.getDocumentSelection();
        // console.log(circularParse(circularStringify(selection)));

        // If selected area exists
        if (!(true === selection.bUse && true === event.shiftKey)) { // bRightBtn !== true &&
            this.clearSection();
            documentCore.removeSelection();
        } else if ( bRightBtn && documentCore.getImageSelectionInfo() ) {
            this.clearSection();
        }

        const cursorState = !bPrint ? documentCore.getCursorStateInDocument(pointX, pointY, pageId) : null;
        pointX = cursorState ? cursorState.pointX : pointX;
        pointY = cursorState && event.detail !== 2 ? cursorState.pointY : pointY;
        // if ( !bPrint ) {
        //     pointX = documentCore.getCursorStateInDocument(pointX, pointY, pageId).pointX;
        // }

        this.gMouseEvent.button = event.button;
        this.gMouseEvent.bShiftKey = event.shiftKey;
        this.gMouseEvent.type = MouseEventType.MouseButtonDown;
        this.gMouseEvent.pointX = pointX;
        this.gMouseEvent.pointY = pointY;
        this.gMouseEvent.clickCount = event.detail;

        const oldSelection = documentCore.isSelectionUse();

        // console.log(circularParse(circularStringify(documentCore.getDocument())));
        documentCore.mouseButtonDown(this.gMouseEvent, pageId, pointX, pointY);
        // console.log(circularParse(circularStringify(documentCore.getDocument())));
        selection = documentCore.getDocumentSelection();

        this.pageIndex1 = this.pageIndex = documentCore.getCursorPosition().pageNum;
        // this.bJump = false;

        const bStrictMode2 = documentCore.isStrictMode2();
        if ( bStrictMode2 &&
            false === documentCore.isCursorInNewControl() && false === documentCore.isCursorInRegion() ) {
            // this.documentCore.removeSelection();
            // this.clearSection2();
            this.host.setCursorVisible(false);
            // return ;
        }

        if ( !bStrictMode2 && documentCore.isMovingTableBorder() ) {
            this.host.updateCursor2();
            return;
        }

        // shift键选择
        if (true === selection.bUse && true === this.gMouseEvent.bShiftKey) {
            // 隐藏光标
            // this.host.cursorBlur();
            this.host.setCursorVisible(false);

            // always clear selection before render (before selections[] is cleared)
            this.clearSection();

            this.selections = documentCore.getSelectionBounds(
                this.gMouseEvent,
                pageId,
            );
            // console.log(documentCore.getDocumentSelection())
            // console.log(selections)

            // 只有在有内容选中，并且当前是选中状态，才进行选中区域的渲染
            if (
                this.selections &&
                true === documentCore.getDocumentSelection().bUse
            ) {
                this.renderSection();
            }
            // console.log(documentCore.document.selection)
        } else {
            if (this.shouldUpdateCursor(event)) {
                this.host.renderSetCursor(pageNode);
            }
        }

        if ( NURSING_FEATURE && oldSelection && !selection.bUse ) {
            this.clearSection();
            this.host.setCursorVisible(true);
        }

        // focus newControl
        // if (true === documentCore.isCursorInNewControl()) {
        //     this.renderFocusHightLightNewControl();
        //     this.showNewBoxList();
        //     // this.showNewComboxDropButton();
        //     this.showNewDropButton();
        // } else {
        //     this.curInNewControlName = null;
        //     this.closeNewDropButton();
        //     this.clearHightLightNewControl();
        // }
    }

    /**
     * 处理mouse move
     * @param event
     */
    private handleMouseMove = (event: any) => {
        if (this._clickedCommentSymbol) {
            return;
        }
        // console.log(event)
        // if (this._bMousedown !== true) {
        //   return;
        // }
        // console.log('editor mousemove')
        const documentCore = this.documentCore;
        // const isClickOnNonSVGDOM = this.isCursorOnNonSVGDOM(event);
        // if (isClickOnNonSVGDOM) {
        //     return null;
        // }

        this.gMouseEvent.type = MouseEventType.MouseButtonMove;

        const pageNode = getPageElement(event.target);
        if (!pageNode) {
            return;
        }

        const pageId = this.getPageId(pageNode);
        const scale = this.host.getScale();

        // this.viewCursorPoint[0] = event.pageX;
        // this.viewCursorPoint[1] = event.pageY;

        // update gMouseEvent class
        let layerY = event.layerY;
        const bPrint = this.host.isPrint();
        if ( bPrint ) {
            layerY = event.offsetY;
        }
        const position = getPagePadding(this.documentCore);
        let offsetX = event.layerX / scale + position.left;
        let offsetY = layerY / scale + position.top;
        // console.log(event.offsetX, event.offsetY); // when crossing page, y might be < 0

        const cursorState = !bPrint ? documentCore.getCursorStateInDocument(offsetX, offsetY, pageId) : null;
        offsetX = cursorState ? cursorState.pointX : offsetX;
        offsetY = cursorState && event.detail !== 2 ? cursorState.pointY : offsetY;
        // if ( !bPrint ) {
        //     offsetX = documentCore.getCursorStateInDocument(offsetX, offsetY, pageId).pointX;
        // }

        this.gMouseEvent.pointX = offsetX;
        this.gMouseEvent.pointY = offsetY;

        const oldSelection = documentCore.isSelectionUse();

        const imageFlags = documentCore.getImageFlags();
        if (!imageFlags.isHandlerOnClick) {
            documentCore.mouseButtonMove(
                this.gMouseEvent,
                pageId,
                offsetX,
                offsetY,
            );
        }

        // --- all selections{} should be ready at this point ---
        // this.pageIndex = documentCore.getCursorPosition().pageNum;
        // this.bJump = false;

        if ( this.cursorType !== documentCore.getCursorType() ) {
            this.cursorType = documentCore.getCursorType();
            const svgs = this.getContainer()
                    .getElementsByClassName('page-container');

            for (let index = 0; svgs && index < svgs.length; index++) {
                const element = svgs[index];
                element.setAttribute('cursor', this.cursorType);
            }
        } else if ( documentCore.isMovingTableBorder() ) {
            this.host.updateCursor2();
            return ;
          }

        const selection = documentCore.getDocumentSelection();

        if (
            true === selection.bUse &&
            true === selection.bStart &&
            !imageFlags.isHandlerOnClick
        ) {
            // console.log(documentCore.getDocument())
            // always clear selection before render (before selections[] is cleared)
            this.clearSection();

            this.selections = documentCore.getSelectionBounds(
                this.gMouseEvent,
                pageId,
            );

            // 只有在有内容选中，并且当前是选中状态，才进行选中区域的渲染
            if (
                this.selections &&
                (this.selections.cells.length > 0 ||
                    this.selections.lines != null)
            ) {
                // this.closeNewDropButton();
                // 隐藏光标
                // mousemove on image polygons should not focus on 1px textarea so as not to jump wildly
                if (!imageFlags.isHandlerOnClick) {
                    // this.renderSetCursor(pageNode);
                }
                // console.log('hide cursor')
                // this.host.cursorBlur();
                this.host.setCursorVisible(false);
                if (!imageFlags.isImageOnClick) {
                    this.renderSection();
                }
            }
        } else if ( NURSING_FEATURE && oldSelection && !selection.bUse ) {
            this.clearSection();
            this.host.setCursorVisible(true);
        }

        // 非文本选择中，光标在同一位置持续3秒
        // const newControlName = documentCore.getFocusNewControlName(
        //     this.gMouseEvent,
        //     pageId,
        // );
        // if (null != newControlName && false === selection.bStart) {
        //     if (newControlName !== this.curFocusNewControlName) {
        //         this.closeNewControlTips();
        //     }

        //     this.bShowNewControlTip = true;
        // } else {
        //     this.closeNewControlTips();
        // }

        // newControl background
        // if (
        //     true === documentCore.isCursorInNewControl() ||
        //     true === documentCore.isFocusOnNewControl(this.gMouseEvent, pageId)
        // ) {
        //     this.renderFocusHightLightNewControl(this.gMouseEvent, pageId);
        // } else {
        //     this.clearHightLightNewControl();
        // }

        gEvent.setEvent(this.docId, gEventName.TableMousemove, {pageId, offsetX, offsetY, selection}, event);
    }

    /**
     * 鼠标点击-放开
     * 判断光标位置 重置位置或者移除光标
     * @param event
     */
    private handleMouseUp = (event: any) => {
        if (this._clickedCommentSymbol) {
            return;
        }
        this.bMouseUp = true;
        this.host.setMouseDown(false);
        // console.log('up')
        // this._bMousedown = false;
        // console.log('editor mouseup')
        // console.log(event.target.tagName)

        // smooth clicking in modal or mousedown event not expected to influence editor
        // const isClickOnNonSVGDOM = this.isCursorOnNonSVGDOM(event);
        // if (isClickOnNonSVGDOM) {
        //     return null;
        // }
        this.host.setScrollPageVisible();
        this._mousedownY = undefined;
        // const callbackOption = { isStop: false };
        // gEvent.setEvent(
        //     this.docId,
        //     'editorMouseUpStopUnSelection',
        //     callbackOption,
        //     event,
        // );
        // if (callbackOption.isStop === true) {
        //     return null;
        // }

        // During mouseup of LEFT CLICK, If contextMenu opens, close it
        // if (
        //     this.state.showContextMenu &&
        //     event.button === MouseButtonType.MouseButtonLeft &&
        //     'contextMenu-item' !== event.target.className
        // ) {
        //     this.setState({ showContextMenu: false });
        // }

        // const logicDocument = this.documentCore.getDocument();
        const imageFlags = this.documentCore.getImageFlags();
        // if (event.button === 2) {
        //     if (event.target.tagName === 'image' &&
        //     event.target.className.baseVal.includes('select-button') === false) { // select button, not real image
        //         this._imageName = event.target.getAttribute('name');
        //         // this.host.cursorBlur();
        //         this.host.setCursorVisible(false);
        //         // IMAGE_FLAGS.isImageOnClick = true;
        //         // logicDocument.setImageOnClick(true);

        //         const id = event.target && event.target.id;
        //         const selectionInfo = getImageSelectionName(event.target);
        //         this.documentCore.setImageSelectionInfo(selectionInfo);

        //         if (this._imageName) {
        //             this.documentCore.selectedImageByName2(this._imageName);
        //             this.host.focus(); // events in textArea should be preserved
        //             return null;
        //         }
        //     }
        // } else
        const bPrint = this.host.isPrint();
        if (imageFlags.isImageOnClick && !bPrint) {
            if (this._imageName) {
                this.documentCore.selectedImageByName2(this._imageName);
            }
            const image = this.documentCore.getSelectedImage();
            const portion = image.portion;
            const paragraph = portion.getParagraph();
            const line = paragraph.getLineById(portion.getLineByPos(0) + 1);
            gEvent.setEvent(
                this.docId,
                gEventName.CommentChange,
                CommentOperatorType.AddNewSymbol,
                {
                    pageIndex: image.pageIndex,
                    top: line.top,
                    bShow: true,
                }
            );
            this.host.focus(); // events in textArea should be preserved
            return null;
        }

        // this.scrollTool.mousedown(false, event.clientY);

        this.gMouseEvent.type = MouseEventType.MouseButtonUp;

        const pageNode = getPageElement(event.target);
        const pageId = this.getPageId(pageNode);

        if (!pageNode) {
            return;
        }
        this.pageIndex1 = pageId;
        const documentCore = this.documentCore;

        const bMovingTableBorder = documentCore.isMovingTableBorder();
        const preCursorPageIndex = documentCore.getCursorPosition().pageNum;
        const scale = this.host.getScale();
        const position = getPagePadding(this.documentCore);
        let offsetX = event.layerX / scale + position.left;
        let offsetY = event.layerY / scale  + position.top;

        if (!this.host.isCascadeManager() && bPrint ) {
            offsetY = event.offsetY;
        }

        const cursorState = !bPrint ? documentCore.getCursorStateInDocument(offsetX, offsetY, pageId) : null;
        offsetX = cursorState ? cursorState.pointX : offsetX;
        offsetY = cursorState && event.detail !== 2 ? cursorState.pointY : offsetY;

        documentCore.mouseButtonUp(
            this.gMouseEvent,
            pageId,
            offsetX,
            offsetY,
        );

        const bStrictMode2 = documentCore.isStrictMode2();
        if ( bStrictMode2 &&
            false === documentCore.isCursorInNewControl() && false === documentCore.isCursorInRegion() ) {
            // this.documentCore.removeSelection();
            // this.clearSection2();
            this.host.setCursorVisible(false);
            // return ;
        }

        if ( !bStrictMode2 && this.cursorType !== documentCore.getCursorType() && bMovingTableBorder ) {
            this.cursorType = documentCore.getCursorType();
        }

        if (this.shouldUpdateCursor(event)) {
            // this.newControlHandler();

            // if (!this.isControlLoseFocus()) {
            //     this.renderSetCursor(pageNode);
            // }

            this.host.renderSetCursor(pageNode);

            // this.textArea.focus(); // this alone might be enough
        }

        // this.closeNewControlTips();

        const selection = documentCore.getDocumentSelection();

        // if in headerfooter, scene is diff
        let curBUse = selection.bUse;
        if (documentCore.isInHeaderFooter()) {
            // tslint:disable-next-line: newline-per-chained-call
            curBUse = !documentCore.getHdrFtr().getCurHeaderFooter().content.isSelectionEmpty();
        }
        if ( false === this.bSelected && curBUse ) {
            this.setSelect(curBUse);
            this.selections = documentCore.getSelectionBounds();
        }

        // console.log(this.bSelected)
        // if ( true === this.isSelected() || IMAGE_FLAGS.isImageOnClick || IMAGE_FLAGS.isHandlerOnClick ) {
        if (true === this.bSelected) {
            this.host.setCursorVisible(false);
            if (!imageFlags.isImageOnClick
                && !(this.bInHeaderFooterPrev && 2 === event.detail && !(cursorState && cursorState.result))) {
                this.selections = documentCore.getSelectionBounds(
                    this.gMouseEvent,
                    pageId,
                );
                // this.renderSection2();
            }
        }
        this.bMouseDown = false;

        if ( !bStrictMode2 && bMovingTableBorder ) {
            this.pageIndex = preCursorPageIndex;
            this.host.updateCursor(false, true);
            this.pageIndex = pageId;
        }
        //屏蔽表格单元格跨页重复提示，存在bug
        //gEvent.setEvent(this.docId, gEventName.TableShowtips, {pageId, offsetX, offsetY}, event);
        gEvent.setEvent(this.docId, gEventName.TableMouseup, {pageId, offsetX, offsetY}, event);
    }

    private handleDoubleClick = (e: any) => {
        // not actually big influence on header footer
        // this method is entered really late. Event triggered far after handlemousedown()
        if (this.enterOrExitHeaderFooter(e)) {
            // console.log('dblclick refreshed')

            // if headerfooter has only empty para, remove it
            const documentCore = this.documentCore;
            const logicDocument = documentCore.getDocument();
            const hdrFtr = logicDocument.hdrFtr;
            const curHeader = hdrFtr.pages[hdrFtr.curPage].header;
            if (curHeader != null) {
                const bEmptyHeaderFooter = curHeader.isEmptyHeaderFooter();
                const sectionProperty = documentCore.getSectionProperty();
                sectionProperty.setIsCurHeaderFooterEmpty(bEmptyHeaderFooter);
            }

            documentCore.removeSelection();
            this.selections = null;
            this.host.handleRefresh();
            // this.handleRefresh();
            // this.global_mouseEvent.clickCount = 2;
        }

        this.viewModeTag(e);
        // console.log(event.target)
    }

    private enterOrExitHeaderFooter(event: any): boolean {
        if (HEADERFOOTER_SWITCH === false) {
            return false;
        }

        const documentCore = this.documentCore;
        this.viewMode = documentCore.getViewMode();
        if ( ViewModeType.WebView === this.viewMode ) {
            return false;
        }

        if ( documentCore.isInHeaderFooter() !== this.bInHeaderFooterPrev ) {
            return true;
        }
        // const logicDocument = documentCore.getDocument();
        // const pageMetrics = logicDocument.getPageContentStartPos(this.pageIndex);
        // const y = event.layerY;

        // const curPosType = logicDocument.curPos.type;

        // // console.log(curPosType)
        // // console.log(this.oldCurposType);
        // // console.log(event.target)

        // if (curPosType !== this.oldCurposType) {
        //     enter headerfooter
        //     if ((y <= pageMetrics.y || y >= pageMetrics.yLimit) &&
        //         (this.oldCurposType === DocCurPosType.Content) &&
        //         (curPosType === DocCurPosType.HdrFtr)) {
        //         return DocCurPosType.HdrFtr;
        //     }

        //     // exit headerfooter
        //     if ((y > pageMetrics.y && y < pageMetrics.yLimit) &&
        //         (this.oldCurposType === DocCurPosType.HdrFtr) &&
        //         (curPosType === DocCurPosType.Content)) {
        //         return DocCurPosType.Content;
        //     }
        // }
        // return null;
    }

    private renderSectionBackground(renderLine: any[], type: RenderSectionBackgroundType, color?: string): void {
        const scale = 1; // this.host.getScale();
        const arrs = [];
        const pagesContainer = this.getContainer();
        const bInHeaderFooter = this.documentCore.isInHeaderFooter();
        for (let index = 0, length = renderLine.length; index < length; index++) {
            const item = renderLine[index];
            if (null != item) {
                arrs.push(index);
                const className = (type === RenderSectionBackgroundType.TableCellSelection) ?
                    `${type}-${item.id}-${item.pageIndex}` : `${type}-${item.line.id}`;

                let tarLines = pagesContainer
                    .getElementsByClassName(className);

                // renderSection can be in headerfooter
                // const docPosType = this.documentCore.getDocument()
                //     .getDocPosType();
                // if (docPosType === DocCurPosType.HdrFtr) {

                    // several hdrftrs may have the same id paraline
                if ( bInHeaderFooter && tarLines.length > 1) {
                    const curPageIndex = this.documentCore.getCurPageIndex();
                    const pageNodes = pagesContainer.children; // ignore text or comment nodes

                    for (const pageNode of (pageNodes as any)) {
                        if (+pageNode.getAttribute('page-index') === curPageIndex) {
                            // there exists only one selection area for a paraline
                            tarLines = pageNode.getElementsByClassName(className);
                            break;
                        }
                    }
                }

                const tarLineIndex = 0;

                const tarLine: any = tarLines[tarLineIndex];

                // 选中多页，选中的页面会被替换，tarLine已经不在
                if (undefined !== tarLine && null !== tarLine) {
                    if (RenderSectionBackgroundType.ParagragphLineSelection === type
                        || RenderSectionBackgroundType.TableCellSelection === type) {
                        tarLine.classList.add('selection-selected');
                    } else {
                        tarLine.classList.add('newcontrol-focus');
                    }

                    tarLine.setAttribute('x', `${item.x * scale}`);
                    tarLine.setAttribute('width', `${item.width * scale}`);
                }
            }
        }
    }

    private clearSectionBackground(renderLine: any[], type: RenderSectionBackgroundType): void {
        // for (let index = 0, length = renderLine.length; index < length; index++) {
        //     const item = renderLine[index];
        //     if (null != item) {
        //         const className = (type === RenderSectionBackgroundType.TableCellSelection) ?
        //             `${type}-${item.id}-${item.pageIndex}` : `${type}-${item.line.id}`;
        //         const selectionCollection = this.getContainer()
        //         .getElementsByClassName(className);

        //         // If parapage, there would exist two (or more, consider parapage > 2) such nodes
        //         for (let i = 0, length2 = selectionCollection.length; i < length2; i++) {
        //             const elem = selectionCollection[i];
        //             if (elem) {
        //                 if (RenderSectionBackgroundType.ParagragphLineSelection === type
        //                     || RenderSectionBackgroundType.TableCellSelection === type) {
        //                     elem.classList.remove('selection-selected');
        //                 } else {
        //                     elem.classList.remove('newcontrol-focus');
        //                 }
        //             }
        //         }
        //     }
        // }

        if (RenderSectionBackgroundType.ParagragphLineSelection === type
            || RenderSectionBackgroundType.TableCellSelection === type) {
                const elems = this.getContainer()
                .querySelectorAll('.selection.selection-selected');
                if (elems.length > 0) {
                    Array.from(elems)
                    .forEach((elem) => {
                        elem.classList.remove('selection-selected');
                    });
                }
        } else {
            const elems = this.getContainer()
            .querySelectorAll('.selection.newcontrol-focus');
            if (elems.length > 0) {
                Array.from(elems)
                .forEach((elem) => {
                    elem.classList.remove('newcontrol-focus');
                });
            }
        }
    }

    /**
     * 渲染选区
     * @param e
     * @param isUp 选区方向
     */
    private renderSection = (
        e?: MouseEvent,
        selections?: IDrawSelectionBounds,
    ) => {
        if (true) {
            this.renderSection2(e, selections);
            return;
        }
        if (null == this.selections) {
            this.setSelect(false);
            return;
        }

        const drawLines = this.selections.lines;
        const drawCells = this.selections.cells;

        if (
            (!drawLines || 0 === drawLines.length) &&
            (!drawCells || 0 === drawCells.length)
        ) {
            return;
        }

        let bSelected = false;
        if (drawLines && 0 < drawLines.length) {
            bSelected = true;
            this.renderSectionBackground(
                drawLines,
                RenderSectionBackgroundType.ParagragphLineSelection,
            );
        }

        if (drawCells && 0 < drawCells.length) {
            bSelected = true;
            this.renderSectionBackground(
                drawCells,
                RenderSectionBackgroundType.TableCellSelection,
            );
        }

        this.setSelect(bSelected);
        if ( bSelected ) {
            gEvent.setEvent(this.docId, gEventName.Selection);
        }
    }

    private renderSectionBackground2(
        event: any, renderLine: any[], type: RenderSectionBackgroundType, color?: string, append?: boolean): void {
        // const arrs = [];

        if (type === RenderSectionBackgroundType.TableCellSelection) {
            return this.renderTableSectionBackground(renderLine);
        }

        const pageNums: any = {};
        const pagesContainer = this.getContainer();
        // const bInHeaderFooter = this.documentCore.isInHeaderFooter();
        for (let index = 0, length = renderLine.length; index < length; index++) {
            const item = renderLine[index];
            if (item) {
                const pageIndex = item.pageIndex;
                if (pageNums[pageIndex]) {
                    pageNums[pageIndex].push(item);
                } else {
                    pageNums[pageIndex] = [item];
                }
            }

        }

        const keys = Object.keys(pageNums);
        const fixedType = this.selections.fixedType;
        const name: string = this.getSvgName(fixedType) || '';
        if (keys.length) {
            keys.forEach((pageNum) => {
                const arrs = pageNums[pageNum];
                if (!arrs || arrs.length === 0) {
                    return;
                }

                const page = pagesContainer.querySelector(
                    `.page-wrapper[page-index='${pageNum}']>svg` + name);
                if (!page) {
                    return;
                }

                let pathContainer = page.querySelector('.selections');
                if (!pathContainer) {
                    pathContainer = document.createElementNS('http://www.w3.org/2000/svg', 'g');
                    pathContainer.setAttribute('class', 'selections');
                    page.appendChild(pathContainer);
                }

                // let pathElement = pathContainer.querySelector('path');
                // if ( !pathElement ) {
                //     pathElement = document.createElementNS('http://www.w3.org/2000/svg', 'path') as any;
                //     // pathElement.setAttribute('fill', 'transparent');
                //     pathElement.setAttribute('fill', '#ABC8F2');
                //     pathElement.setAttribute('fill-opacity', '0.8');
                //     pathElement.setAttribute('stroke', 'none');
                //     // pathElement.setAttribute('stroke', getTheme()
                //     //         .NewControl.DefaultNewControlSectionRangeBorderColor);
                //     // pathElement.setAttribute('strokeWidth', '1');
                //     pathContainer.appendChild(pathElement);
                // // } else {
                // //     pathElement.style.display = '';
                // }

                // let path = 'M ';
                // // tslint:disable-next-line: prefer-for-of
                // for (let index2 = 0; index2 < arrs.length; index2++) {
                //     const item = arrs[index2];
                //     if ( item && item.pageIndex === pageNum ) {
                //         path += ('M ' === path) ? (item.x + ' ') : ('L ' + item.x + ' ');
                //         path += (item.y + ' ');

                //         path += ('L ' + item.x + ' ');
                //         path += (item.y + item.height) + ' ';
                //     }
                // }

                // for (let index2 = arrs.length - 1; index2 >= 0; index2--) {
                //     const item = arrs[index2];
                //     if ( item && item.pageIndex === pageNum ) {
                //         const x = item.x + item.width;

                //         path += ('L ' + x + ' ');
                //         path += (item.y + item.height) + ' ';

                //         path += ('L ' + x + ' ');
                //         path += (item.y) + ' ';
                //     }
                // }

                // let rects: string = '';
                // for (let index2 = 0, length = arrs.length; index2 < length; index2++) {
                //     const item = arrs[index2];
                //     rects += `<rect class='selections-rect' x='${item.x}' y='${item.y}'
                //         width='${item.width}' height='${item.height}'></rect>`;
                // }
                // pathContainer.innerHTML = pathContainer.innerHTML + rects;
                const innerHTML = this.getPolygonRect(arrs, undefined, append);
                if (append) {
                    pathContainer.innerHTML += innerHTML;
                } else {
                    pathContainer.innerHTML = innerHTML;
                }
            });
        }
    }

    private renderTableSectionBackground(cells: any[]): void {
        const pageNums: any = {};
        for (let index = 0, length = cells.length; index < length; index++) {
            const item = cells[index];
            if (item) {
                const pageIndex = item.pageIndex;
                const fixedType = item.fixedType;
                if (!pageNums[pageIndex]) {
                    pageNums[pageIndex] = {[fixedType]: [item]};
                } else {
                    const obj = pageNums[pageIndex];
                    if (obj[fixedType]) {
                        obj[fixedType].push(item);
                    } else {
                        obj[fixedType] = [item];
                    }
                }
            }
        }

        const pageIndexs = Object.keys(pageNums);
        if (!pageIndexs.length) {
            return;
        }

        const dom = this.getContainer();
        pageIndexs.forEach((pageNum) => {
            const svgs = dom.querySelectorAll(`.page-wrapper[page-index='${pageNum}']>svg`);
            if (!svgs.length) {
                return;
            }

            const page = pageNums[pageNum];
            for (let index = 0, len = svgs.length; index < len; index++) {
                const svg = svgs[index];
                const name = svg.getAttribute('name');
                let type: IFixedCellType;
                switch (name) {
                    case 'left': {
                        type = IFixedCellType.Left;
                        break;
                    }
                    case 'right': {
                        type = IFixedCellType.Right;
                        break;
                    }
                    case 'header': {
                        type = IFixedCellType.Header;
                        break;
                    }
                    case 'leftHeader': {
                        type = IFixedCellType.LeftHeader;
                        break;
                    }
                    case 'rightHeader': {
                        type = IFixedCellType.RightHeader;
                        break;
                    }
                }
                const datas = page[type];
                if (!datas || !datas.length) {
                    continue;
                }
                const rects = this.getPolygonRect(datas);
                if (rects) {
                    let pathContainer = svg.querySelector('.selections');
                    if (!pathContainer) {
                        pathContainer = document.createElementNS('http://www.w3.org/2000/svg', 'g');
                        pathContainer.setAttribute('class', 'selections');
                        svg.appendChild(pathContainer);
                    }
                    pathContainer.innerHTML = rects;
                }
            }
        });
    }

    private getSvgName(type: IFixedCellType): string {
        let svgName: string;
        switch (type) {
            case IFixedCellType.Left: {
                svgName = 'left';
                break;
            }
            case IFixedCellType.Right: {
                svgName = 'right';
                break;
            }
            case IFixedCellType.Header: {
                svgName = 'header';
                break;
            }
            case IFixedCellType.LeftHeader: {
                svgName = 'leftHeader';
                break;
            }
            case IFixedCellType.RightHeader: {
                svgName = 'rightHeader';
                break;
            }
        }

        if (svgName) {
            svgName = `[name="${svgName}"]`;
        }

        return svgName;
    }

    private getPolygonRect(items: any[], color: string = 'rgba(0, 119, 255, 0.3)', append: boolean = false): string {
        const length = items.length - 1;
        const style = `fill: ${color}; stroke-width: 0px; pointer-events: none`;
        if (append || items[length].table != null || null != items[length].region) {
            let rects = '';
            for (let index2 = 0; index2 <= length; index2++) {
                const item = items[index2];
                rects += `<rect x='${numtoFixed2(item.x)}' y='${numtoFixed2(item.y)}'
                    width='${numtoFixed2(item.width)}' height='${numtoFixed2(item.height)}' style='${style}'></rect>`;
            }

            return rects;
        }

        const lefts: any[] = [];
        const rights: any[] = [];
        items.forEach((item, index) => {
            const x = item.x;
            const y = item.y;
            const x2 = x + item.width;
            const y2 = y + item.height;
            if (index === 0) {
                rights.push({x, y: y2});
                rights.push({x, y});
                rights.push({x: x2, y});
                rights.push({x: x2, y: y2});
                if (length === index) {
                    return;
                }
            } else if (index === length) {
                rights.push({x: x2, y});
                rights.push({x: x2, y: y2});
                lefts.unshift({x, y});
                lefts.unshift({x, y: y2});
            } else {
                rights.push({x: x2, y: y2});
                lefts.unshift({x, y});
                lefts.unshift({x, y: y2});
            }
        });

        const paths = rights.concat(lefts);
        let path = '';
        paths.forEach((item) => {
            path += `${numtoFixed2(item.x)},${numtoFixed2(item.y)} `;
        });

        return `<polygon points='${path}' style='${style}' />`;
    }

    private clearSectionBackground2(): void {
        if (this.bSelected !== true) {
            return;
        }
        const pagesContainer = this.getContainer();
        if (!pagesContainer) {
            return;
        }
        const paths = pagesContainer.querySelectorAll('.selections');
        Array.from(paths)
        .forEach((path: HTMLElement) => {
            // path.style.display = 'none';
            path.innerHTML = '';
        });
    }

    private renderSection2 = (
        e?: MouseEvent,
        selections?: IDrawSelectionBounds,
    ) => {
        if (null == this.selections) {
            this.clearSectionBackground2();
            this.setSelect(false);
            return;
        }

        // search render for break page
        this.clearSectionBackground2();

        const drawLines = this.selections.lines;
        const drawCells = this.selections.cells;

        if (
            (!drawLines || 0 === drawLines.length) &&
            (!drawCells || 0 === drawCells.length)
        ) {
            return;
        }

        let bSelected = false;
        let lines: any[] = [];
        if (drawLines && 0 < drawLines.length) {
            bSelected = true;
            lines = drawLines;
        }
        let appendTable: boolean = false;
        if (drawCells && 0 < drawCells.length) {
            bSelected = true;
            this.renderSectionBackground2(e,
                drawCells, RenderSectionBackgroundType.TableCellSelection
            );
            appendTable = true;
            // lines = lines.concat(drawCells);
        }

        if (bSelected && lines.length) {
            this.renderSectionBackground2(e,
                lines, null, undefined, appendTable
            );
        }

        this.setSelect(bSelected);
        if ( bSelected ) {
            gEvent.setEvent(this.docId, gEventName.Selection);
        }
    }

    private clearSection2 = () => {
        if (this.selections) {
            this.clearSectionBackground2();
        }

        this.selections = null;
        this.setSelect(false);
    }

    private setSelect(flag: boolean): void {
        this.bSelected = flag;
    }

    private getPageId(pageNode: HTMLElement): number {
        if (!pageNode) {
            return 0;
        }

        const page = +pageNode.getAttribute('page-id');
        this.pageIndex = page; // this.host.getPageByPageId(page);

        return this.pageIndex;
    }

    private shouldUpdateCursor(event: any): boolean {
        const documentCore = this.documentCore;
        const logicDocument = documentCore.getDocument();
        const pageMetrics = logicDocument.getPageContentStartPos(this.pageIndex);
        const scale = this.host.getScale();
        const option = getPagePadding(this.documentCore);
        const y = event.layerY / scale  + option.top;

        // const curPosType = logicDocument.curPos.type;
        const hdrFtr = logicDocument.hdrFtr;

        const imageFlags = this.documentCore.getImageFlags();
        // 1. in editing header footer mode, click on doc content
        if ( documentCore.isInHeaderFooter() && (y > pageMetrics.y && y < pageMetrics.yLimit)) {
            return false;
        } else if (imageFlags.isHandlerOnClick) {
            // 2. mousedown on image polygons should not focus on 1px textarea so as not to jump wildly
            return false;
        } else if (hdrFtr.curHeaderFooter != null && hdrFtr.getJustLeaveHeaderFooter() === true) {
            // 3. exiting headerfooter, in mousedown no refresh, in mouseup refresh
            hdrFtr.setJustLeaveHeaderFooter(false);
            return false;
        } else if (!documentCore.isInHeaderFooter() && y >= pageMetrics.yLimit) {
            // 4. when doc content is short and want to enable footer
        }
        return true;
    }

    private getContainer(): HTMLDivElement {
        // if (!this.dom) {
        //     this.dom = this.host.getContainer();
        // }

        return this.host.getContainer();
    }
}
