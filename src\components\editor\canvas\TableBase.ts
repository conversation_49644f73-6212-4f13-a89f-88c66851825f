import { IDocumentTable } from '../../../model/TableProperty';
import { ICanvasProps } from './common';
import { TableUI } from './Table';
import { TableCellContentUI } from './TableCellContent';

interface IProps {
    content: IDocumentTable;
    className?: string;
    scale?: number;
    pageIndex?: number;
    host?: any;
    cellId?: number;
    bFromHeaderFooter?: boolean;
}

export class TableBaseUI {
    private props: IProps;
    private documentCore: any;
    private host: any;
    private ctx: any;
    constructor(props: ICanvasProps) {
        this.props = props.content;
        this.host = props.host;
        this.props.pageIndex = props.pageIndex;
        this.documentCore = props.host.documentCore;
        this.ctx = props.ctx;
        this.render();
    }

    private render(): void {
        this.renderTable(this.props.content);
        this.renderTableCells(this.props.content);
    }

    private renderTable(table: IDocumentTable): void {
        const {bFromHeaderFooter, pageIndex} = this.props;
        const host = this.host;
        const ctx = this.ctx;
        const obj = new TableUI({content: {content: table, bFromHeaderFooter}, pageIndex, host, ctx });
    }

    private renderTableCells = (table: IDocumentTable) => {
        const scale = 1; // this.scale;
        const {bFromHeaderFooter, pageIndex} = this.props;
        const host = this.host;
        const ctx = this.ctx;
        // const pageIndex = this.props.pageIndex;
        table.tableCells.forEach((cell) => {
            // const { id, height, width, positionX, positionY } = items;
            const {id, cellName, bMask} = cell;
            let {height, width, x, y} = cell;
            height = height * scale;
            width = width * scale;
            x = x * scale;
            y = y * scale;
            const props = {
                height,
                width, //  would be overridden
                x, // would be overridden
                y,
            };

            const props2 = {
                height,
                width, //  would be overridden
                x, // would be overridden
                y,
                cellName,
            };
            const obj = {content: {content: cell.content, cellId: id, bFromHeaderFooter}, pageIndex, host, ctx}
            const aaa = new TableCellContentUI(obj);

            // const url = 'url(#' + cell.id + ')';
            // if (cell.cellBackground && cell.cellBackground !== '#fff') {
            //     result.tableCellBackground.push(
            //         <rect
            //             key={id}
            //             width={width}
            //             height={height}
            //             x={x}
            //             y={y}
            //             data-key={id}
            //             fill={cell.cellBackground || '#fff'}
            //         />
            //     );
            // }

            // result.tablecell.push(
            //     <rect key={id} className={className} cell-key={id} {...props}>
            //         {id}
            //     </rect>
            // );


            // const host = this.props.host;
            // result.cellContent.push(

            //     <TableCellContent
            //         host={host}
            //         key={id}
            //         ref={this._cellRefs[cell.cellId]}
            //         paras={cell.content.paragraphs}
            //         cellId={(bMask ? id : undefined)}
            //         pageIndex={pageIndex}
            //         scale={scale}
            //         bFromHeaderFooter={bFromHeaderFooter}
            //     />
            // );

            // if ( bMask ) {
            //     const mask = (
            //     <clipPath id={'mask' + id} key={'mask' + id}>
            //         <rect key={id + '-mask'} x={x} y={y} width={width} height={height} fill='#666' />
            //     </clipPath>
            //     );
            //     result.mask.push(mask);
            // }
            // const cellNames = this.renderTableCellNames(props2, id);
            // if (cellNames) {
            //     result.cellName.push(cellNames);
            // }
        });
    }
}
