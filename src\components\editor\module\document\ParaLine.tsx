import * as React from 'react';
import { isMacOs, numtoFixed } from '../../../../common/commonDefines';
import { IDocumentParaLine } from '../../../../model/ParaLineProperty';
import ParaPortion from './ParaPortion';

export default class ParaLine extends React.Component<IDocumentParaLine, {}> {
    constructor(props: IDocumentParaLine) {
        super(props);
    }

    public render(): any {
        // const { id, index } = this.props;
        return this.renderPortion();
    }

    public shouldComponentUpdate(nextProps: any, nextState: any): any {
        const { id, content, startPos, endPos } = this.props;

        // console.log(this, nextProps)
        // if ( content !== nextProps.content )
        // return true;
        // console.log('ParaLine--------shouldComponentUpdate---------------')
        try {
            if (startPos === nextProps.startPos && endPos === nextProps.endPos &&
                content[startPos].getRangeStartPos(id) === nextProps.content[nextProps.startPos].getRangeStartPos(id) &&
                content[startPos].getRangeEndPos(id) === nextProps.content[nextProps.startPos].getRangeEndPos(id) &&
                content[endPos].getRangeStartPos(id) === nextProps.content[nextProps.endPos].getRangeStartPos(id) &&
                content[endPos].getRangeEndPos(id) === nextProps.content[nextProps.endPos].getRangeEndPos(id)) {

                if (this.props.content.length === nextProps.content.length) {
                    for (let index = 0, count = this.props.content.length; index < count; index++) {
                        const element = this.props.content[index];
                        if (element.content.length !== nextProps.content[index].content.length
                            || element.getTextContent() !== nextProps.content[index].getTextContent()) {
                            return true;
                        }
                    }
                } else {
                    return false;
                }
            }
        } catch (e) {
            console.error(e);
        }

        // console.log(this, nextProps)
        // console.log('llllllllllllllllllllllllllllllllllllllllllllllllllll')
        return true;
    }

    private renderPortion(): any {
        const { content, cellId } = this.props;
        const curLine = this.props.id;
        let bMacCellMask;
        if (cellId !== undefined && isMacOs) {
            bMacCellMask = true;
        }

        return content.map((item) => {
            if (item.content.length === 0) {
                return;
            }

            // const bNewLine = bMacCell;
            // bMacCell = false;
            if (
                curLine >= item.startLine &&
                item.startLine + item.getLinesCount() > curLine
            ) {
                const startPos = item.getRangeStartPos(curLine - item.startLine);
                const endPos = item.getRangeEndPos(curLine - item.startLine);
                // console.log(item)
                return (
                        <ParaPortion
                            key={item.id}
                            id={item.id}
                            cellId={cellId}
                            content={item.content}
                            type={item.type}
                            textProperty={item.textProperty}
                            positionX={numtoFixed(item.positionX)}
                            positionY={numtoFixed(item.positionY)}
                            startPos={startPos}
                            endPos={endPos}
                            pageIndex={this.props.pageIndex}
                            documentCore={this.props.documentCore}
                            textHeight={numtoFixed(item.textHeight)}
                            nHeaderFooter={this.props.nHeaderFooter}
                            bRadioBoxRed={item['bRadioBoxRed']}
                            bNewLine={bMacCellMask}
                        />
                );
            }
        });
    }
}
