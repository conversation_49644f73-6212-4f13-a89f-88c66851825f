import { CommentOperatorType, ResultType, ViewModeType} from '@/common/commonDefines';
import {
    GlobalEvent as gEvent,
    GlobalEventName as gEventName
} from '@/common/GlobalEvent';
import { Comment } from '@/model/core/Comment/Comment';
import { DocumentCore } from '@/model/DocumentCore';
import '@/components/editor/style/commentPanel.less';
import React from 'react';
import CommentContent, { ICommentInfo } from './CommentContent';
import RadioCommet from '../../ui/RadioCommet';
import { CommentSelect } from '../../ui/select/CommentSelect';

interface IProps {
    documentCore: DocumentCore;
    userName: string;
    bShowPanel?: boolean; // 批注列表
    pageInfo: any;
    top: number;
    bShowNewComment: boolean;
    refresh: () => void;
}

interface IState {
    bShowList: boolean; // 是否显示批注列
    bShowNewComment: boolean; // 控制新增批注UI的显示
    commentStatus: CommentStatusType;
    commentUser: CommentUserType;
    bShowFilter: boolean; // 是否筛选批注
}

enum CommentStatusType {
    ALLCOMMENT,
    SOLVED,
    UNSOLVED,
}
const commentStatusList = [{
    value: CommentStatusType.ALLCOMMENT,
    key: '全部',
}, {
    value: CommentStatusType.SOLVED,
    key: '已处理',
}, {
    value: CommentStatusType.UNSOLVED,
    key: '未处理',
}];

enum CommentUserType {
    ALLUSER,
    SELF,
}

const commentUserList = [{
    value: CommentUserType.ALLUSER,
    key: '全部',
    default: true,
}, {
    value: CommentUserType.SELF,
    key: '自己'
}];
export default class ParaCommentLayer extends React.Component<IProps, IState> {
    private _documentCore: DocumentCore;
    // 被激活的批注在显示列表中的位置
    private _activeIndex: number = -1;
    /** 被激活的批注id */
    private _activeId: number = -1;
    /** 更新后是否已经重排批注列表 */
    // private _bActiveUpdate: boolean = false;
    private _activedCommentName: string = '';

    private _hoverCommentDom: any;

    private _listRef: any;
    private commentList: Comment[];
    private bShowedComments: {
        comment: Comment;
        top: number;
    }[];
    private newCommentPos: any;
    private lineCache: {[key: number]: Comment[]};

    constructor(props: IProps) {
        super(props);
        const {documentCore} = props;
        this._documentCore = documentCore;
        this.newCommentPos = {
            pageIndex: 0,
            top: props.top || 0,
            bShow: false,
        };
        this.state = {
            bShowList: false,
            bShowNewComment: props.bShowNewComment,
            bShowFilter: false,
            commentStatus: CommentStatusType.ALLCOMMENT,
            commentUser: CommentUserType.ALLUSER,
        };
        this._listRef = React.createRef();
    }

    public render(): any {
        const {bShowPanel, pageInfo: {width, scale}, documentCore} = this.props;
        this.commentList = documentCore.getAllComments();
        const style: any = {
            left: width * scale + 12 + 'px', // containes padding(12px)
        };
        if (!bShowPanel) {
            style.display = 'none';
        }
        return (
            <div className='comment-panel' tabIndex={-1} ref={this._listRef} style={style}>
                {this.renderSummary()}
                {this.renderFilter()}
                {this.prepareAllComments()}
                {this.renderNewComment()}
            </div>
        );
    }

    public componentDidMount(): void {
        const docId = this._documentCore.getCurrentId();
        gEvent.addEvent(docId, gEventName.CommentChange, this.handleCommentChange);
        gEvent.addEvent(docId, gEventName.ContentChange, this.handleRefreshChange);

        const listDom = this._listRef.current;
        if (listDom) {
            listDom.addEventListener('click', this.handleClick);
            // listDom.addEventListener('mousedown', this.handleMouseDown);
            listDom.addEventListener('mousemove', this.handleMouseMove);
            listDom.addEventListener('blur', this.handleBlur);
        }
        window.requestAnimationFrame(this.refreshCommentsLayout);
    }

    public componentWillUnmount(): void {
        const docId = this._documentCore.getCurrentId();
        gEvent.deleteEvent(docId, gEventName.CommentChange, this.handleCommentChange);
        gEvent.deleteEvent(docId, gEventName.ContentChange, this.handleRefreshChange);

        const listDom = this._listRef.current;
        if (listDom) {
            listDom.removeEventListener('click', this.handleClick);
            // listDom.removeEventListener('mousedown', this.handleMouseDown);
            listDom.removeEventListener('mousemove', this.handleMouseMove);
            listDom.removeEventListener('blur', this.handleBlur);
        }
    }

    public componentDidUpdate(prevProps: Readonly<IProps>, prevState: Readonly<IState>, snapshot?: any): void {
        window.requestAnimationFrame(this.refreshCommentsLayout);
    }

    private prepareAllComments(): any {
        const {pageInfo, userName} = this.props;
        const commentList = this.commentList;
        const bShowedComments = this.bShowedComments = [];
        const lines = this.lineCache = {};
        const scaledHeight = pageInfo.height * pageInfo.scale + 20;
        const { commentStatus, commentUser, bShowFilter } = this.state;
        const viewMode = this._documentCore.getViewMode();
        const pageHeightInfo = {
            scaledHeight,
            firstPageHeight: pageInfo.firstPageHeight,
            firstPageHeader: viewMode !== ViewModeType.WebView ? 0 : pageInfo.firstPageHeader,
            otherPageHeight: pageInfo.otherPageHeight || pageInfo.firstPageHeight,
            otherPageHeader: viewMode !== ViewModeType.WebView ? 0 :
                            (pageInfo.otherPageHeader || pageInfo.firstPageHeader),
        };
        const commentChildren: any[] = [];
        let activedTop = 0;

        for (let idx = 0, len = commentList.length; idx < len; idx++) {
            const comment = commentList[idx];
            const position = this.getCommentPosition(comment, pageHeightInfo, pageInfo.scale);
            const y = position.top;
            !lines[y] && (lines[y] = []);
            lines[y].push(comment);

            const data = comment.getData();
            const isSolved = comment.getData()
                                    .isSolved();
            if (
                (isSolved && commentStatus === CommentStatusType.UNSOLVED) ||
                (!isSolved && commentStatus === CommentStatusType.SOLVED) ||
                (data.getUserName() !== userName && commentUser === CommentUserType.SELF)
            ) {
                continue;
            }
            const id = comment.getId();
            const info = {
                name: comment.getName(),
                id,
                data,
            };
            const isActived = comment.isActived();
            let pos: any;
            if (!bShowFilter) {
                pos = {
                    left: 0,
                    top: y,
                };
            }
            commentChildren.push(
                <CommentContent
                    key={'com' + id}
                    index={id}
                    userName={userName}
                    position={pos}
                    info={info}
                    isActived={isActived}
                    isSolved={isSolved}
                    updateComment={this.updateComment}
                    activeComment={this.activeComment}
                    refreshList={this.refreshCommentsLayout}
                />
            );

            if (isActived) {
                if (this._activeIndex !== bShowedComments.length) {
                    this._activeIndex = bShowedComments.length;
                    // this._bActiveUpdate = false;
                }
                this._activeId = info.id;
                activedTop = y;
            }
            bShowedComments.push({
                comment,
                top: y,
            });
        }

        // 计算右侧定位边距
        const { paddingRight } = pageInfo;
        const symbolStyle: any = {
            // left: '-45px', // 最大-30
            // width: '25px', // 最小17
            // height: '25px' // 最小17
        };
        if (viewMode === ViewModeType.CompactView) {
            symbolStyle.left = '-30px';
            symbolStyle.width = '17px';
            symbolStyle.height = '17px';
        } else if (paddingRight < 42) {
            const release = Math.floor(42 - paddingRight);
            let left = -45;
            let width = 25;
            if (release < 8) {
                left += release;
            } else {
                left += 8;
                const sub = Math.ceil((release - 8) / 2);
                left += sub;
                width -= sub;
                left = left > -30 ? -30 : left;
                width = width < 17 ? 17 : width;
            }
            symbolStyle.left = left + 'px';
            symbolStyle.width = width + 'px';
            symbolStyle.height = width + 'px';
        }
        const sumSymbols: any[] = [];
        for (const key of Object.keys(lines)) {
            const value = +key;
            sumSymbols.push(
                <div
                    key={'symbol' + key}
                    data-index={key}
                    className={`sum-symbols${activedTop === value ? ' active' : ''}`}
                    style={{top: value - 5 + 'px', ...symbolStyle}}
                >
                    {lines[key].length}
                </div>
            );
        }
        return sumSymbols.concat(commentChildren);
    }

    private renderSummary(): React.ReactNode {
        const rightDom = !this.state.bShowFilter ?
                    (<div className='right all'>查看</div>) :
                    (<div className='right return-all'>返回</div>);

        return (
            <div className='summary'>
                <div className='iconfont doubleLeftArrowLine left'>
                    隐藏
                </div>
                <div className='center'>全部({this.commentList.length})</div>
                {rightDom}
            </div>
        );
    }

    private renderFilter(): React.ReactNode {
        const { commentStatus, commentUser, bShowFilter } = this.state;
        const style: any = {};
        if (!bShowFilter) {
            style.display = 'none';
        }
        return (
            <div className='filter' style={style}>
                <CommentSelect
                    datas={commentStatusList}
                    value={commentStatus}
                    name={'commentStatus'}
                    width={90}
                    label=''
                    onChange={this.handleStatusChange}
                />
                <RadioCommet 
                    data={commentUserList}
                    value={commentUser}
                    name={'commentUser'}
                    onChange={this.handleUserChange}
                />
            </div>
        );
    }

    private renderNewComment(): React.ReactNode {
        if (!this.state.bShowNewComment) {
            return null;
        }
        const {pageInfo, userName} = this.props;
        const {pageIndex, top} = this.newCommentPos;
        const pos = {
            left: 0,
            top: pageIndex * pageInfo.height + top,
        };
        return (
            <CommentContent
                key={-1}
                index={-1}
                userName={userName}
                position={pos}
                isNew={true}
                isActived={true}
                onBlur={this.handleBlur}
                updateComment={this.updateComment}
                activeComment={this.activeComment}
                refreshList={this.refreshCommentsLayout}
            />
        );
    }

    private activeComment = (name: string): void => {
        this.props.documentCore.activeComment(name);
    }

    private getCommentPosition = (
        comment: Comment,
        pageHeight: {
            scaledHeight: number;
            firstPageHeight: number;
            firstPageHeader: number;
            otherPageHeight: number;
            otherPageHeader: number;
        },
        scale: number
    ): {
        top: number;
        bottom: number;
    } => {
        const portion = comment.getStartPortion();
        const paragraph = portion.getParagraph();
        const pageIndex = paragraph.getCurrentPageByPos(comment.getStartPos());
        const line = paragraph.getLineById(portion.getLineByPos(0) + 1);

        const {
            scaledHeight: height,
            firstPageHeight: first,
            firstPageHeader: firstHeader,
            otherPageHeight: other,
            otherPageHeader: otherHeader,
        } = pageHeight;
        let offsetHeader = firstHeader;
        let offset = pageIndex * first;
        if (pageIndex > 0) {
            offsetHeader = otherHeader;
            offset = first + (pageIndex - 1) * other;
        }

        const top = line.top * scale;
        const bottom = line.bottom * scale;
        return {
            top: Math.floor(top + offset - offsetHeader),
            bottom: Math.floor(bottom + offset - offsetHeader),
        };
    }
    private updateComment = (type: CommentOperatorType, info: Partial<ICommentInfo>) => {
        const { documentCore, refresh } = this.props;
        switch (type) {
            case CommentOperatorType.New: {
                const commentName = documentCore.addCommentByCursor({
                    time: new Date(),
                    content: info.content,
                });
                if (commentName) {
                    // this._bActiveUpdate = false;
                    this._activedCommentName = commentName;
                    this.setState({bShowNewComment: false});
                    refresh();
                }
                break;
            }
            case CommentOperatorType.Reply: {
                if (documentCore.addCommentReply(info.name, info.content)) {
                    // this._bActiveUpdate = false;
                    this.setState({});
                }
                break;
            }
            case CommentOperatorType.Delete: {
                const hasId = typeof info.id === 'number';
                if (documentCore.deleteCommentByName(info.name, info.id)) {
                    this._activeIndex = -1;
                    if (hasId) {
                        this.setState({});
                    } else {
                        refresh();
                    }
                }
                break;
            }
            case CommentOperatorType.Update: {
                if (documentCore.updateComment(info.name, {id: info.id, content: info.content})) {
                    // this._bActiveUpdate = false;
                }
                break;
            }
            case CommentOperatorType.Solve: {
                if (documentCore.updateComment(info.name, {isSolved: info.isSolved})) {
                    this.setState({});
                }
                break;
            }
        }
    }

    private handleBlur = (isNew: boolean): void => {
        if (isNew) {
            this.setState({bShowNewComment: false});
        }
    }

    private handleClick = (event: any) => {
        event.stopPropagation();
        const dom = event.target;
        if (!dom) {
            return;
        }
        const classList = dom.classList;
        if (classList.contains('right')) {
            // this._bActiveUpdate = false;
            if (this.state.bShowFilter) {
                this.setState({
                    bShowFilter: false,
                    commentStatus: CommentStatusType.ALLCOMMENT,
                    commentUser: CommentUserType.ALLUSER,
                });
            } else {
                this.setState({bShowFilter: true});
            }
        } else if (classList.contains('left')) {
            if (this._documentCore.showCommentPanel(false) === ResultType.Success) {
                this.props.refresh();
            }
        } else if (classList.contains('sum-symbols')) {
            const key = dom.dataset['index'];
            const line = this.lineCache[key];
            if (line && line.length) {
            this.props.documentCore.activeComment(line[line.length - 1].getName());
            }
        }
        gEvent.setEvent(
            this._documentCore.getCurrentId(),
            gEventName.CommentChange,
            CommentOperatorType.AddNewSymbol
        );
    }
    private handleStatusChange = (value: any, name: string) => {
        value = +value;
        if (value !== this.state.commentStatus) {
            this.setState({commentStatus: value});
        }
    }

    private handleUserChange = (value: any, name: string, item?: any) => {
        value = +value;
        if (value !== +this.state.commentUser) {
            this.setState({commentUser: value});
        }
    }

    private handleCommentChange = (type: CommentOperatorType, info?: any): void => {
        switch (type) {
            case CommentOperatorType.New: {
                if (info) {
                    this.newCommentPos = {
                        pageIndex: info.pageIndex,
                        top: info.top,
                    };
                }
                if (!this.state.bShowNewComment) {
                    this.setState({bShowNewComment: true});
                }
                break;
            }
            case CommentOperatorType.AddNewSymbol: {
                if (info) {
                    this.newCommentPos = {
                        pageIndex: info.pageIndex,
                        top: info.top,
                    };
                    if (
                        (this.state.bShowNewComment)
                    ) {
                        this.setState({bShowNewComment: false});
                    }
                }
                break;
            }
            case CommentOperatorType.Active: {
                const name = info || '';
                if (name !== this._activedCommentName) {
                    this._activedCommentName = name;
                    this.props.refresh();
                }
                break;
            }
            case CommentOperatorType.Update: {
                this.handleRefreshChange();
                break;
            }
        }
    }

    private handleRefreshChange = () => {
        // this._bActiveUpdate = false;
    }

    private handleMouseMove = (event: MouseEvent) => {
        if (this._hoverCommentDom) {
            if (+this._hoverCommentDom.dataset['index'] !== this._activeId) {
                this._hoverCommentDom.classList.remove('active');
            }
            this._hoverCommentDom = null;
        }
        const dom = event.target as HTMLDivElement;
        if (dom) {
            if (dom.classList.contains('sum-symbols')) {
                event.stopPropagation();
                const key = dom.dataset['index'];
                const line = this.lineCache[key];
                if (line && line.length) {
                    const listDom = this._listRef.current as HTMLDivElement;
                    const id = line[line.length - 1].getId();
                    const commentDom = listDom.querySelector(`div.comment[data-index='${id}']`);
                    if (commentDom) {
                        this._hoverCommentDom = commentDom;
                        commentDom.classList.add('active');
                    }
                }
            }
        }
    }

    /** 重排定位模式下，批注的位置 */
    private refreshCommentsLayout = (): void => {
        if (this._hoverCommentDom) {
            if (+this._hoverCommentDom.dataset['index'] !== this._activeId) {
                this._hoverCommentDom.classList.remove('active');
            }
            this._hoverCommentDom = null;
        }
        const listDom = this._listRef.current as HTMLDivElement;
        if (!this.props.bShowPanel || !listDom) {
            return;
        }
        // 顺序是一致的
        const comments = this.bShowedComments;
        const len = comments.length;
        const commentDoms = listDom.querySelectorAll('div.comment');
        if (!len || comments.length !== commentDoms.length) {
            return;
        }
        if (this.state.bShowFilter) {
            for (let i = 0, len = comments.length; i < len; i++) {
                const dom = commentDoms[i] as HTMLDivElement;
                dom.style.display && (dom.style.display = '');
            }
            return;
        }
        // this._bActiveUpdate = true;
        const index = this._activeIndex < 0 || this._activeIndex >= commentDoms.length ? 0 : this._activeIndex;
        const activeDom = commentDoms[index] as HTMLDivElement;
        activeDom.style.display && (activeDom.style.display = '');
        const offsetY = 49; // 顶部菜单条的高度
        const {height: tHeight} = activeDom.getBoundingClientRect();
        // padding: 12px
        let minY = comments[index].top;
        if (minY < 37) { // summary's height
            minY = 47;
        }
        activeDom.style.top = minY + 'px';
        let maxY = minY + tHeight + 12;
        minY -= 12;
        let prev = index - 1;
        let prevHidden = false;
        while (prev >= 0) {
            const dom = commentDoms[prev] as HTMLDivElement;
            if (prevHidden || minY < offsetY) {
                dom.style.display = 'none';
            } else {
                dom.style.display && (dom.style.display = '');
            }
            const {height} = dom.getBoundingClientRect();
            const top = comments[prev].top;
            const bottom = top + height;
            if (bottom > minY) {
                minY = minY - height;
                if (minY < offsetY) {
                    prevHidden = true;
                    dom.style.display = 'none';
                } else {
                    dom.style.top = minY + 'px';
                }
            } else {
                dom.style.top = top + 'px';
                minY = top;
            }
            minY -= 12;
            prev--;
        }

        let next = index + 1;
        while (next < len) {
            const dom = commentDoms[next] as HTMLDivElement;
            dom.style.display && (dom.style.display = '');
            const {height} = dom.getBoundingClientRect();
            const top = comments[next].top;
            const bottom = top + height;
            if (top < maxY) {
                dom.style.top = maxY + 'px';
                maxY += height;
            } else {
                dom.style.top = top + 'px';
                maxY = bottom;
            }
            maxY += 12;
            next++;
        }
    }
}
