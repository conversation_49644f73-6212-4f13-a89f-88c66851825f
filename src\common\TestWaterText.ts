// import { loopWater } from '@/components/editor/module/RightMenu';
// import { numtoFixed } from './commonDefines';
// import { MarkFactory } from './MarkFactory';

// export class TestWaterText {
//     private _timeout: number;
//     private _text: string;
//     private _host: any;
//     private _text2: string;
//     constructor(host: any) {
//         this._host = host;
//         this._text = MarkFactory.getInstance()['content'];
//         this.setText(this._text);
//         // this.autoRun();
//     }

//     public set timeout(value: number) {
//         loopWater(this._host.contentRef.current, value, true);
//         this._timeout = value;
//     }

//     public get timeout(): number {
//         return this._timeout;
//     }

//     public get waterText(): string {
//         return this._text;
//     }

//     public set waterText(value: string) {
//         this._text = value;
//         this.setText(value);
//         MarkFactory.getInstance()
//         .setStr(value);
//         this._host.handleRefresh();
//     }

//     public set waterText2(value: string) {
//         this._text2 = value;
//         // this.setText(value);
//         MarkFactory.getInstance()
//         .setStr(value);
//         this._host.handleRefresh();
//     }

//     private setText(value: string): void {
//         numtoFixed['a'] = (value || '')
//         .replace(/[\s\r\n]+/g, '')
//         .split('')
//         .map(((item) => item.charCodeAt(0)));
//     }

//     private autoRun(): void {
//         this.timeout = 100 * 1000;
//         this.waterText = `wfwefwef~！@￥%……&*（）——+《》';\\{}@&     wef342!@#$wefg`;
//     }
// }
