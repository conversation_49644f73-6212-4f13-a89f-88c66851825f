import * as React from 'react';
import Dialog from '../../ui/Dialog';
import {message} from '../../../../common/Message';
import Input from '../../ui/Input';
import Button from '../../ui/Button';
import { ImageMediaType, isValidName, NewControlType, videoIconStr } from '../../../../common/commonDefines';
import { IFRAME_MANAGER } from '../../../../common/IframeManager';
import { ParaElementType } from '../../../../model/core/Paragraph/ParagraphContent';

interface IProps {
    documentCore: any;
    host: any;
    visible: boolean;
    id: string;
    isVideoUrl?: boolean;
    close: (name: string | number, bRefresh?: boolean) => void;
}

interface IState {
    bRefresh: boolean;
}

export default class VideoUrlPanel extends React.Component<IProps, IState> {
    private video: {
        videoName: string, 
        videoSource: string, 
        videoUrl: string
    };
    private timeout: any;
    private visible: boolean;
    private domRef: any; // 用于设置遮罩
    constructor(props: any) {
        super(props);
        this.state = {
            bRefresh: false,
        };
        this.video = {
            videoName: '',
            videoSource: videoIconStr,
            videoUrl: ''
        };
        this.visible = this.props.visible;
        this.domRef = React.createRef();
    }

    public render(): any {
        // TODO: bSignPic?

        return (
            <Dialog
                visible={this.visible}
                width={350}
                open={this.open}
                title='网络视频'
                footer={this.renderFooter()}
                id={this.props.id}
            >
                <div ref={this.domRef} style={{position: 'relative'}}>
                    <div className='editor-line'>
                        <div className='w-70'>URL</div>
                        <div className='right-auto'>
                            <Input
                                value={this.video.videoUrl}
                                onChange={this.onChange}
                                name='videoUrl'
                            />
                        </div>
                    </div>
                </div>
            </Dialog>
        );
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        this.visible = nextProps.visible;
    }

    private renderFooter(): any {
        return (
            <span>
                <Button onClick={this.close}>取消</Button>
                <Button type='primary' onClick={this.confirm}>确认</Button>
            </span>
        );
    }

    private close = (bRefresh?: boolean): void => {
        this.clearUrl();
        this.visible = false;
        if (this.timeout) {
            clearTimeout(this.timeout);
        }
        this.setState({bRefresh: !this.state.bRefresh});
        this.props.close(this.props.id, bRefresh);
    }

    private open = (): void => {
        const documentCore = this.props.documentCore;
        this.video.videoName = documentCore.makeUniqueImageName(ParaElementType.ParaDrawing);

        this.setState({bRefresh: !this.state.bRefresh});
    }

    private onChange = (value: any, name: string): void => {
        this.video[name] = value;
    }

    private confirm = (): void => {
        const documentCore = this.props.documentCore;
        if (documentCore.isProtectedMode()) {
            this.close(true);
            return;
        }
        IFRAME_MANAGER.setDocId(documentCore.getCurrentId());
        const video = this.video;
        if (!isValidName(video.videoName)) {
            message.error('名称不符合规范，请重新输入');
            return;
        }
        if (!video.videoUrl || !this.isValidUrl()) {
            message.error('视频支持mp4、ogg、webm，请输入正确视频url');
            return;
        }
        if (!documentCore.checkUniqueImageNameOtherThanSelectedImage(video.videoName)) {
            message.error('名称不符合规范，请重新命名');
            return null;
        }

        // const videoDom = document.createElement('video');
        // videoDom.src = this.video.videoUrl;
        // // videoDom.onload =()=> {
        // //     alert('success');
        // // }
        // videoDom.onerror =()=> {
        //     message.error('视频url加载失败，请输入正确视频url');
        //     return null;
        // }

        fetch(this.video.videoUrl, {
            method: 'head',
        }).then(async res => {
            if (200 != res.status) {
                message.error('视频url预加载失败，请输入正确的url');
            } else {
                this.insertVideo();
                this.close(true);
            }
        }).catch(error => {
            message.error('视频url拉取失败，请输入正确的url');
        }).finally(() => {
            this.clearUrl();
        });
    }

    /** 清空url */
    private clearUrl = () => {
        this.video.videoUrl = '';
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private insertVideo(): void {
        const { documentCore, host } = this.props;
        const maxWidth = documentCore.getMaxWidth(true);
        const maxHeight = documentCore.getMaxHeight(true);
        // in signature box?
        let ratio = 1;
        const curControl = documentCore.getCurrentNewControl();
        if (curControl != null) {
            const parentControl = curControl.getParent();
            if (parentControl != null && parentControl.getType() === NewControlType.SignatureBox) {
                const tempRatio = parentControl.getSignatureRatio();
                if (isNaN(tempRatio) === false) {
                    ratio = parentControl.getSignatureRatio();
                }
            }
        }
        // let { width, height } = this.video;
        let width = 40;
        let height = 40;
        width *= ratio;
        height *= ratio;

        // if image width/height exceeds limits, shrink them to fit
        if (width > maxWidth) {
            const wRatio = maxWidth / width;
            const hRatio = maxHeight / height;
            if (height * wRatio > maxHeight) {
                height = maxHeight;
                width *= hRatio;
            } else {
                width = maxWidth;
                height *= wRatio;
            }
        } else if (height > maxHeight) {
            const wRatio = maxWidth / width;
            const hRatio = maxHeight / height;
            if (width * hRatio > maxWidth) {
                width = maxWidth;
                height *= wRatio;
            } else {
                height = maxHeight;
                width *= hRatio;
            }
        }
        if (this.props.isVideoUrl) {
            documentCore.addInlineImage(
                width, height, this.video.videoSource,
                null, null, null, ImageMediaType.Video, this.video.videoUrl
            );
        } else {
            documentCore.addInlineImage(width, height, this.video.videoUrl);
        }
        host.handleRefresh();
    }

    private isValidUrl = (): boolean => {
        const suffixIndex = this.video.videoUrl?.lastIndexOf('.');
        const suffix = this.video.videoUrl?.substring(suffixIndex);
        return /(.*)\.(mp4|ogg|webm)$/.test(suffix);
    }
}
