// import * as React from 'react'; 
// import * as ReactTestUtils from 'react-dom/test-utils'; // ES6
// import { configure, shallow , render, mount} from 'enzyme';
// import * as Adapter from 'enzyme-adapter-react-16';

// import {EmrEditor} from './Editor';


// configure({adapter: new Adapter()});


// describe('<EmrEditor />', () => {
//     let wrapper;

//     // enzyme
//     it('EmrEditor render without crash', () => {
//         shallow(<EmrEditor />);
//     });

//     it('EmrEditor\'s states should contain documentCore', () => {
//         wrapper = shallow(<EmrEditor />);
//         expect(wrapper.state('documentCore')).toBeTruthy();
//     });

//     it('EmrEditor should render some default content', () => {
//         wrapper = shallow(<EmrEditor />);
//         expect(wrapper.state('documentCore').document.content.length).toBeGreaterThan(0);
//     });

//     // react test utils
//     it('ReactTestUtils render without crash', () => {
//         expect(ReactTestUtils.isElement(<EmrEditor />)).toBe(true);
//     });

    
// });
