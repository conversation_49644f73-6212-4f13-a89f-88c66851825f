import { fabric } from 'fabric';

interface IProps {
  color: string;
}

export function createText(text: string, x: number, y: number, props: IProps): fabric.Textbox {
  const { color } = props;
  const textBox = new fabric.Textbox(text, {
    left: x,
    top: y,
    originX: 'left',
    originY: 'center',
    selected: true,
    fill: color,
    fontSize: 14,
    fontFamily: 'STSong'
  });
  textBox.editingBorderColor = 'blue';
  return textBox;
}

export function updateTextProps(text: fabric.Textbox, props: IProps): void {
  const { color } = props;
  text.set({fill: color});
}
