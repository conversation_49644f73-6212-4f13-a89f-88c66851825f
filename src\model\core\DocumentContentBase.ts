import {IDrawNewControlBounds} from '../DocumentCore';
import Document, { CurPos, DocCurPosType } from './Document';
import DocumentPage, { ILimits } from './DocumentPage';
import Selection, { SelectionFlagType, ICheckSelectionNewControls } from './Selection';
import { DocumentContentType, CursorType } from './Style';
import History, { IOperateResult } from './History';
import DocumentContentElementBase from './DocumentContentElementBase';
import Paragraph from './Paragraph';
import { GlobalEvent } from '../../common/GlobalEvent';
import { GraphicObjects } from './GraphicObjects/GraphicObjects';
import { INewControlProperty, DocumentSectionType, ResultType,
    REGION_MAX_DEPTH,
    ReviewType,
    MessageType,
    NewControlType} from '../../common/commonDefines';
import HeaderFooter from './HeaderFooter';
import { NewControlManager } from './NewControlManager';
import { NewControl } from './NewControl/NewControl';
import { ParagraphContentPos } from './Paragraph/ParagraphContent';
import { idCounter } from './util';
import { CommentManager } from './Comment/CommentManager';
import { RegionManager, Region, IRegionParam } from './Region';
import { DocumentContent } from './DocumentContent';
import {message} from '../../common/Message';
import { Table } from './Table';
import { HistoryDescriptionType } from './HistoryDescription';
import { GlobalEvent as gEvent , GlobalEventName as gEventName } from '../../common/GlobalEvent';
import ParaDrawing from './Paragraph/ParaDrawing';
import { TableBase } from './TableBase';
import { ReviewInfo } from './Revision';

/**
 * 此类用于处理文档内容（段落和表格等结构）的基类
 */
export default class DocumentContentBase {

    public id: number;     // 唯一标识
    public startPage: number;
    public curPage: number;
    public content: DocumentContentElementBase[]; // 文档内容

    public reindexStartPos: number;

    public curPos: CurPos; // 光标位置
    public selection: Selection; // 选择

    // public bReadOnly: boolean;  // 只读模式

    constructor() {
        this.id = idCounter.getNewId();
        this.startPage = 0;
        this.curPage   = 0;
        this.content = [];
        this.reindexStartPos = 0;
        // this.bReadOnly = false;
    }

    public getId(): number {
      return this.id;
    }

    public updateContentIndexing(): void {
        if (-1 !== this.reindexStartPos) {
            for (let index = this.reindexStartPos, count = this.content.length; index < count; ++index) {
                this.content[index].index = index;
            }

            this.reindexStartPos = -1;
        }
    }

    public getElementsCount(): number {
        return this.content.length;
    }

    public getPages(): DocumentPage[] {
        return [];
    }

    public getPageLimits(pageIndex: number): ILimits {
        return null;
    }

    public getPageFields(pageIndex: number): ILimits {
        return null;
    }

    public getColumnFields(index: number, column: number = 0): ILimits {
        return null;
    }

    public getInsertPosition(): InsertPosition {
        return ;
    }

    public isTimeCell(): boolean {
        return false;
    }

    public isReadonly(): boolean {
        return false;
    }

    public getDocument(): any {
        if ('logicDocument' in this) {
            return this['logicDocument'];
        }
        return this;
    }

    public getColorMap(): any {
        // todo
        return null;
        // return this.colorMap;
    }

    public getCurPos(): CurPos {
        return this.curPos;
    }

    public setDocPosType(type: DocCurPosType): void {
        this.curPos.type = type;
    }

    public getDocPosType(): DocCurPosType {
        return this.curPos.type;
    }

    public moveCursorToXY(curPage: number, pointX: number, pointY: number,
                          bAddToSelect: boolean = false, bDontChangeRealPos: boolean = true): void {
        return;
    }

    public getNewControlByName(name: string): NewControl {
        return this.getNewControlManager()
            .getNewControlByName(name);
    }

    public recalculateCurPos(): any {
        return;
    }

    public selectNewControlByNewControl(newControl: NewControl, includeContent: boolean = true): number {
        // this.removeSelection();

        // const startBorder = newControl.getStartBorder();
        // const endBorder = newControl.getEndBorder();
        this.removeSelection();
        const startPos = newControl.getStartPos();
        startPos.bStart = true;
        const endPos = newControl.getEndPos();
        endPos.bStart = false;
        const type = startPos.shift();
        endPos.shift();
        if (includeContent) {
            // const startBorder = newControl.getStartBorderPortion();
            let depth = startPos.getDepth();
            // 选择时不包括边框、标题
            // if (startBorder.content.length < 2 ) {
            depth -= 1;
            // }
            startPos.update2(startPos.get(depth) + 1, depth);
        } else {
            const depth = endPos.getDepth();
            endPos.update2(endPos.get(depth) + 1, depth);
        }
        // const parent = newControl.getDocumentParent();
        const doc = this.getDocument();
        let controler;
        if (this === doc) {
            controler = doc.setControlActiveByType(type);
            if (controler !== doc) {
                controler = controler.getContent();
            }
        } else {
            controler = this;
        }
        const res = controler.selectAreaByPosBase(startPos, endPos);

        // if ( null == startPos || null == endPos
        //     || null == startBorder || null == endBorder || null == parent ) {
        //     return ResultType.Failure;
        // }

        // let startX: number = startBorder.positionX;
        // let endX: number = endBorder.positionX;
        // if (startBorder.isVisible) {
        //     if (includeContent === true) {
        //         startX += startBorder.widthVisible;
        //     }
        //     // else {
        //     //     startX -= startBorder.widthVisible;
        //     // }
        // }

        // if (includeContent === false && endBorder.isVisible) {
        //     endX += endBorder.widthVisible;
        // }

        // const startPageIndex: number = parent.content[startBorder.paraIndex].getCurrentPageByPos(startPos);
        // const endPageIndex = parent.content[endBorder.paraIndex].getCurrentPageByPos(endPos);

        // this.moveCursorToXY(startPageIndex, startX, startBorder.positionY, false);
        // // this.selection.bUse = true;
        // this.moveCursorToXY(endPageIndex, endX, endBorder.positionY, true);
        this.recalculateCurPos();
        return res ? ResultType.Success : ResultType.Failure;
    }

    /**
     * 获取选择区域内：只包含前边框的所有newcontrols的名称
     */
    public getNewControlNamesWithLeftBorder(): string[] {
        const newControlManager = this.getNewControlManager();

        if ( null != newControlManager && true === this.selection.bUse ) {
            const startPos = this.getStartRemoveSelectionPos();
            const endPos = this.getEndRemoveSelectionPos();
            const list = newControlManager.getForwardOverNewControls(startPos, endPos);
            const result: string[] = [];

            for (const newControl of list) {
                result.push(newControl.getNewControlName());
            }

            return result;
        }

        return null;
    }

    /**
     * 是否包含表格元素/区域标题
     * @returns true/false
     */
    public isContainerTableOrTitle(): boolean {
        if (!this.selection.bUse) {
            return false;
        }
        let {startPos, endPos} = this.selection;
        if (startPos > endPos) {
            const temp = startPos;
            startPos = endPos;
            endPos = temp;
        }
        const contents = this.content;
        for (let index = startPos; index <= endPos; index++) {
            const item = contents[index];
            if (!item.isParagraph() && !item.isHidden() && item.isContainerTableOrTitle()) {
                return true;
            }
        }
        return false;
    }

    /**
     * 返回选中范围内的段落（不包括隐藏段落）
     * @returns 段落集合
     */
    public getSelectParas(): DocumentContentElementBase[] {
        if (!this.selection.bUse) {
            return;
        }
        let {startPos, endPos} = this.selection;
        if (startPos > endPos) {
            const temp = startPos;
            startPos = endPos;
            endPos = temp;
        }
        const contents = this.content;
        let datas: DocumentContentElementBase[] = [];
        for (let index = startPos; index <= endPos; index++) {
            const item = contents[index];
            if (item.isHidden()) {
                continue;
            }
            if (item.isParagraph()) {
                datas.push(item);
            } else {
                const items = item.getSelectParas();
                if (items && items.length > 0) {
                    datas = datas.concat(items);
                }
            }
        }

        return datas;
    }

    /**
     * 获取选择区域内：只包含后边框的所有newcontrols的名称
     */
    public getNewControlNamesWithRightBorder(): string[] {
        const newControlManager = this.getNewControlManager();
        if ( null != newControlManager && true === this.selection.bUse ) {
            const startPos = this.getStartRemoveSelectionPos();
            const endPos = this.getEndRemoveSelectionPos();
            const list = newControlManager.getBehindOverNewControls(startPos, endPos);
            const result: string[] = [];

            for (const newControl of list) {
                result.push(newControl.getNewControlName());
            }

            return result;
        }

        return null;
    }

    public addNewControlBySelectedPos(startPara: Paragraph, endPara: Paragraph, newControl: NewControl,
                                      direction: number = 1): boolean {
        if ( startPara === endPara ) {
            startPara.addNewControlBySelectedPos(newControl, 0, direction);
        } else {
            startPara.addNewControlBySelectedPos(newControl, 1, direction);
            endPara.addNewControlBySelectedPos(newControl, 2, direction);
        }

        return true;
    }

    public addNewControlInternal(property: INewControlProperty, sText?: string): number {
        const contentPos = this.curPos.contentPos;
        let startPos = contentPos;
        let endPos = contentPos;
        let startElement = this.content[startPos];
        let endElement = this.content[endPos];
        let bAddEndPos = true;
        let direction = 1;

        const bSigBox = (NewControlType.SignatureBox === property.newControlType);
        if ( true === this.selection.bUse ) {
            startPos = this.selection.startPos;
            endPos = this.selection.endPos;
            direction = this.getSelectionDirection();

            if ( startPos !== endPos && NewControlType.Section !== property.newControlType ) {
                return ResultType.Invalid;
            }

            startElement = this.content[startPos];
            endElement = this.content[endPos];

            startElement = bSigBox ? endElement : startElement;
            // if ( NewControlType.TextBox === property.newControlType
            //     || NewControlType.Section === property.newControlType ) {
            if ( startPos > endPos ) {
                startPos = this.selection.endPos;
                endPos = this.selection.startPos;
            }

            if ( startElement !== endElement
                && ((startElement.isTable() || endElement.isTable())
                        || (startElement.isRegion() || endElement.isRegion())) ) {
                return ResultType.Invalid;
            } else if ( startElement === endElement && startElement.isRegion() ) {
                // if ( !startElement.isValidAddNewControl() ) {
                //     return ResultType.Invalid;
                // }
            } else if ( startElement === endElement && startElement instanceof TableBase ) {
                if ( startElement.isSelectedAll() || startElement.isCellSelection() ) {
                    return ResultType.Invalid;
                }
            }

            for (let index = startPos + 1; index < endPos; index++) {
                const element = this.content[index];
                if ( element
                    /*&& ( (NewControlType.Section === property.newControlType && element.isRegion())
                        || (NewControlType.TextBox === property.newControlType*/
                                && (element.isTable() || element.isRegion()) /*) )*/ ) {
                    return ResultType.Invalid;
                }
            }

            if ( NewControlType.TextBox === property.newControlType
                || NewControlType.Section === property.newControlType) {
                bAddEndPos = false;
            }
        }

        // const element = this.content[contentPos];
        let result: number;
        if ( endElement.isParagraph() ) {
            const newControlManager = this.getNewControlManager();
            const endContentPos = (1 === direction ? endElement.getCurContentPosInDoc(!bAddEndPos, false) :
                                        startElement.getCurContentPosInDoc(!bAddEndPos, false));
            const startContentPos = (bAddEndPos || bSigBox) ? endContentPos : (1 === direction ?
                startElement.getCurContentPosInDoc(true, true) : endElement.getCurContentPosInDoc(true, true));

            const addParam = newControlManager.isValidAddNewControl(startContentPos,
                endContentPos, property.newControlType);
            if ( addParam && true === addParam.bValid ) {
                const newControl = newControlManager.createNewControl(endElement, property, sText);

                if ( null == newControl ) {
                    return ResultType.Failure;
                }
                // const parentNewControl = newControlManager.getPosNewControl(endContentPos);
                if ( bAddEndPos || bSigBox ||
                    (addParam.parentNewControl && addParam.parentNewControl.isPlaceHolderContent()) ) {
                    if ( (bAddEndPos || bSigBox) && this.selection.bUse ) {
                        this.removeSelection();
                    }

                    result = endElement.addNewControl(newControl, addParam.parentNewControl, property);

                    if (result === ResultType.Success) {
                        newControlManager.addNewControl(newControl, endContentPos);
                    }
                } else {
                    newControlManager.addNewControl(newControl, startContentPos, endContentPos);
                    const bsuccess = this.addNewControlBySelectedPos(startElement as Paragraph, endElement as Paragraph,
                                                    newControl, direction);

                    result = bsuccess ? ResultType.Success : ResultType.Failure;
                    if (bsuccess && true === this.selection.bUse && newControl.isHidden()) {
                        newControl['bHidden'] = undefined;
                        newControl.setHidden(true);
                    }
                }

                if (newControl && newControl.isSignatureBox() && (newControl as any).isSignSet()) {
                    this.recalculate();
                    (newControl as any).setAlwaysShow2();
                }
            } else {
                return ResultType.Invalid;
            }
        } else {
            result = endElement.addNewControl(null, null, property, sText);
            if ( result !== ResultType.Success  ) {
                return result;
            }
        }

        return result;
    }

    public selectAreaByPosBase(startPos: ParagraphContentPos, endPos: ParagraphContentPos, dir?: number): boolean {
        const start = startPos.shift();
        const end = endPos.shift();
        if ( end === undefined || start === undefined) {
            return false;
        }
        const contents = this.content;
        const startElement = contents[start];
        const endElement = contents[end];
        if (!endElement || !startElement) {
            return false;
        }
        const selection = this.selection;

        selection.bUse = true;
        selection.bStart = false;
        selection.startPos = start;
        selection.endPos = end;
        selection.flag = SelectionFlagType.Common;
        this.curPos.contentPos = end;
        let flag: boolean;
        if (startElement === endElement) {
            flag = startElement.selectAreaByPos(startPos, endPos, dir);
        } else {
            const newElementPos = new ParagraphContentPos();
            let startIndex = start;
            if (startPos.bStart === true) {
                newElementPos.clear();
                startElement.getEndPos(false, newElementPos);
                flag = startElement.selectAreaByPos(startPos, newElementPos, dir);
                if (flag === false) {
                    return false;
                }
                startIndex++;
            }

            let endIndex = end;

            if (endPos.bStart === false) {
                newElementPos.clear();
                // newElementPos.add(end);
                endElement.getStartPos(newElementPos);
                flag = endElement.selectAreaByPos(newElementPos, endPos, dir);
                if (flag === false) {
                    return false;
                }
                endIndex--;
            }

            for (let index = startIndex; index <= endIndex; index++) {
                const base = contents[index];
                base.selectAll(1);
            }
        }

        return flag;
    }

    // public selectAreaByStartPos(index: number, startPos: ParagraphContentPos, hasEnd: boolean, dir: number): number {
    //     const newElementPos = new ParagraphContentPos();
    //     newElementPos.clear();
    //     const startElement = this.content[index];
    //     startElement.getEndPos(false, newElementPos);
    //     const flag = startElement.selectAreaByPos(startPos, newElementPos, dir);
    //     if (!hasEnd) {

    //     }
    // }

    /**
     * 是否包含结构化元素的半个边框
     * @returns true/false
     */
    public unOverNewControl(): boolean {
        const selection = this.selection;
        if (!selection.bUse) {
            return false;
        }

        const doc = this.getDocument();
        let startIndex = selection.startPos;
        let endIndex = selection.endPos;
        if (startIndex > endIndex) {
            const temp = startIndex;
            startIndex = endIndex;
            endIndex = temp;
        }

        const contents = this.content;
        const startPos = contents[startIndex].getCurContentPosInDoc(true, true, true);
        const endPos = contents[endIndex].getCurContentPosInDoc(true, false, true);
        const manager = doc.getNewControlManager();
        let newControls = manager.getForwardOverNewControls(startPos, endPos);
        if (newControls && newControls.length > 0) {
            return true;
        }
        newControls = manager.getBehindOverNewControls(startPos, endPos);
        if (newControls && newControls.length > 0) {
            return true;
        }
        return false;
    }

    public isCanCopy(): boolean {
        const selection = this.selection;
        if (!selection.bUse) {
            return false;
        }

        const doc = this.getDocument();
        let startIndex = selection.startPos;
        let endIndex = selection.endPos;
        if (startIndex > endIndex) {
            const temp = startIndex;
            startIndex = endIndex;
            endIndex = temp;
        }

        const contents = this.content;
        const startPos = contents[startIndex].getCurContentPosInDoc(true, true, true);
        const endPos = contents[endIndex].getCurContentPosInDoc(true, false, true);
        const newControls: NewControl[] = doc.getNewControlManager()
        .getNewControls(startPos, endPos);
        if (newControls && newControls.length) {
            if (newControls.find((item) => item.isCopyProtect())) {
                return false;
            }
        }

        const images: ParaDrawing[] = doc.getDrawingObjects()
        .getAllImages()
        .filter((item) => item.copyProtect);
        for (const image of images) {
            const portion = image.portion;
            if (!portion) {
                continue;
            }
            const portionSelection = portion.selection;
            if (!portionSelection.bUse) {
                continue;
            }

            const currentIndex = portion.content.findIndex((item) => item === image);
            if (currentIndex === -1) {
                continue;
            }
            startIndex = portionSelection.startPos;
            endIndex = portionSelection.endPos;
            if (startIndex > endIndex) {
                const temp = startIndex;
                startIndex = endIndex;
                endIndex = temp;
            }
            if (endIndex > currentIndex && currentIndex >= startIndex) {
                return false;
            }

            // const element = portion.paragraph.getTopElement();
            // if (!element) {
            //     continue;
            // }

            // const curIndex = element.index;
            // if (curIndex < startIndex || curIndex > endIndex) {
            //     continue;
            // }

            // if (element === contents[curIndex]) {
            //     return false;
            // }
        }

        return true;
    }

    public getSelectedContentToCopy(bKeepHalfStructBorder: boolean = false,
                                    option?: any): DocumentContentElementBase[] {
        const selection = this.selection;
        if ( true !== selection.bUse ) {
            return;
        }

        let startParaPos = selection.startPos;
        let endParaPos = selection.endPos;

        if (startParaPos > endParaPos) {
            const pos = startParaPos;
            startParaPos = endParaPos;
            endParaPos = pos;
        }
        const contents = this.content;
        let paras = [];

        let names = [];
        const leftNames = this.getNewControlNamesWithLeftBorder();
        const rightNames = this.getNewControlNamesWithRightBorder();
        if (leftNames.length > 0) {
            names = names.concat(leftNames);
        }

        if (rightNames.length > 0) {
            names = names.concat(rightNames);
        }

        const doc = this.getDocument();
        const history = doc.getHistory();
        let turnOn = false;
        if ( history ) {
            turnOn = history.isTurnOn();
            history.turnOff();
        }
        for (let i = startParaPos; i <= endParaPos; i++) {
            let base: any;
            const curPar = contents[i];
            const type = curPar.getType();
            if (type === DocumentContentType.Region) {
                if ((curPar as any).isLoadCacheRegion()) {
                    continue;
                }
                if (curPar.isSelectedAll()) {
                    base = curPar.copy(this, option);
                } else {
                    base = new Region(this, doc, {newControlName: curPar.getName()});
                    const res = curPar.getSelectedContent(base, names, bKeepHalfStructBorder, option);
                    paras = paras.concat(res);
                    continue;
                }
            } else if (curPar.getType() === DocumentContentType.Table) {
                base = new Table(this, doc, 0, 0, []);
                const cellParas = curPar.getSelectedContent(base, names, undefined, option);
                // 这里会进行内容选中，但是没有选表格的单元格，所以不需要重新构建表格
                if (cellParas && cellParas.length > 0) {
                    paras = paras.concat(cellParas);
                    break;
                }
            } else {
                base = new Paragraph(this, doc);
                curPar.getSelectedContent(base, names, bKeepHalfStructBorder, option);
                if (base.content.length < 1) {
                    continue;
                }
            }
            paras.push(base);
        }

        if ( history && turnOn ) {
            history.turnOn();
        }
        return paras;
    }

    /**
     * 获取结构化名称，节跟元素之间用分号隔开
     */
    public getNewControlBySelectAreaBase(): string {
        const list = this.getNewControlNamesWithBothBordersBase();
        if (!list || list.length === 0) {
            return ResultType.StringEmpty;
        }
        const newControlManager = this.getNewControlManager();
        const arrs = [];
        const sections = [];
        list.forEach((name) => {
            const control = newControlManager.getNewControlByName(name);
            if (control.isNewSection()) {
                sections.push(name);
            } else {
                arrs.push(name);
            }
        });

        return sections.join(',') + ';' + arrs.join(',');
    }

    /**
     * 获取选择区域内：只包含整个元素的所有newcontrols的名称
     */
    public getNewControlNamesWithBothBordersBase(): string[] {
        const newControlManager = this.getNewControlManager();
        if ( null != newControlManager && true === this.selection.bUse ) {
            const startPos = this.getStartRemoveSelectionPos();
            const endPos = this.getEndRemoveSelectionPos();
            return newControlManager.getNewControlNamesByPos(startPos, endPos);
        }

        return [];
    }

    public getParentSumPageIndex(): number {
        let parent: DocumentContentElementBase;
        if (this.isRegionContent()) {
            parent = this['parent'];
        } else if (this.isTableCellContent()) {
            parent = this['parent'].row.table;
        }
        if (parent) {
            return parent.getParentSumPageIndex();
        }
        return 0;
    }

    /**
     * 获取上一个元素的索引(去除隐藏元素)
     * @param pos 当前元素的索引
     */
    public getPrevElementIndex(pos: number, items?: any[]): number {
        const contents = items || this.content;
        let index: number = pos - 1;
        if (index === -1) {
            return pos;
        }
        if (contents[index] && contents[index].isHidden() !== true) {
            return index;
        }
        index--;
        for (; index >= 0; index--) {
            const content = contents[index];
            if (content.isHidden() !== true) {
                break;
            }
        }
        if (index < 0) {
            index = pos;
        }

        return index;
    }

    /**
     * 获取下一个元素的坐标（去除隐藏元素的索引）
     * @param pos 当前元素的索引
     */
    public getNextElementIndex(pos: number, items?: any[]): number {
        const contents = items || this.content;
        let index: number = pos + 1;
        if (!contents[index]) {
            return pos;
        }
        if (contents[index].isHidden() !== true) {
            return index;
        }
        index++;
        const len = contents.length;
        for (; index < len; index++) {
            const content = contents[index];
            if (content.isHidden() !== true) {
                break;
            }
        }
        if (index === len) {
            index = pos;
        }

        return index;
    }

    /**
     * 根据索引获取元素，过滤掉隐藏元素
     * @param index 索引
     */
    public getElementByIndex(index: number): DocumentContentElementBase {
        const contents = this.content;
        const len = contents.length;
        for (; index < len; index++) {
            const content = contents[index];
            if (content.isHidden() !== true) {
                return content;
            }
        }
        return;
    }

    // tslint:disable-next-line: no-empty
    public resetInsertPosition(): void {}

    public getCurParaPortion(): any {}

    public addRegionInternal(props: INewControlProperty): number {
        const regionInfo = this.getSeletedRegionInfo();
        if (!regionInfo) {
            message.error('当前位置非法，无法插入区域！');
            return ResultType.Failure;
        }
        if (regionInfo.errorStr) {
            message.error(regionInfo.errorStr);
            return;
        }
        const indexs = regionInfo.indexs;
        if (indexs === undefined) {
            return ResultType.Failure;
        }

        const regionManager = this.getRegionManager();

        let parent: any;
        if (regionInfo.parent) {
            parent = regionInfo.parent;
            if (!regionManager.isValidDepth(parent.getName(), undefined, true)) {
                message.error('当前位置已经存在一个区域！');
                return;
            }
            parent = parent.content;
        } else {
            parent = this;
        }
        let start = indexs[0];
        const end = (indexs[1] || start);
        const contents = parent.content;
        const maxDepth = REGION_MAX_DEPTH;
        if (contents.slice(start, end + 1)
        .find((item) => {
                return item.getType() === DocumentContentType.Region &&
                    !regionManager.isValidDepth(item.getName(), maxDepth);
            },
        )) {
            message.error('当前位置已经存在一个区域！');
            return;
        }

        let doc = this.getDocument();
        if (doc instanceof DocumentContent) {
            doc = doc.logicDocument;
        }

        // const history = doc.getHistory();
        // history.createNewHistoryPoint(HistoryDescriptionType.DocumentAddRegion);
        doc.startAction(HistoryDescriptionType.DocumentAddRegion);
        const region = new Region(parent, doc, props);
        // const items = [];
        const childNames: string[] = regionInfo.childNames = [];

        for (let removeIndex = end; removeIndex >= start; removeIndex--) {
            const actItem = contents[removeIndex];
            if (actItem.getType() === DocumentContentType.Region) {
                childNames.unshift(actItem.getName());
            }
            region.addToContent(0, actItem, false);
            // items.unshift(contents[removeIndex]);
            parent.contentRemove(removeIndex, 1, undefined, true);
            actItem.selectAll(true);
        }
        if (region.isShowTitle2()) {
            region.addTitlePortion(props.newControlTitle);
        }
        // region.getOperateContent().selection.startPos = 0;
        // region.getOperateContent().selection.endPos = region.getOperateContent().content.length - 1;
        // region.getOperateContent().selection.bUse = true;

        parent.addToContent(start, region, false);
        // parent.selection.startPos = start;
        // parent.selection.endPos = start;
        // parent.selection.bUse = true;
        parent.curPos.contentPos = start;

        // const emptyPara: boolean = items.length !== 1;
        // items.forEach((item, index) => {
        //     region.addToContent(index, item, emptyPara);
        // });

        if (regionInfo.newControlNames && regionInfo.newControlNames.length) {
            this.getNewControlManager()
                .setParentByNames(regionInfo.newControlNames, region.getOperateContent(), doc.getHistory());
        }
        // region.addTitlePortion(props.newControlTitle);
        region.getContent()
        .forEach((item) => {
            item.removeSelection();
        });
        this.removeSelection();
        if (regionInfo.parent) {
            regionManager.addLeaf2(region, regionInfo);
            parent.curPos.contentPos = start;
            region.moveCursorToStartPos();
        } else {
            regionManager.addRegion(region, undefined, regionInfo.childNames);
            this.curPos.contentPos = start;
            this.content[start].moveCursorToStartPos();
        }

        regionManager.setActiveName(region.getName());
        if (region.isHidden()) {
            start = this.getNextElementIndex(start);
        }

        this.recalculate();
        this.updateCursorXY();
        doc.endAction();
        return ResultType.Success;
    }

    /**
     * 获取顶级节点的pos；Header 、Document、Footer
     * @param elem 顶级节点内容元素
     */
    public getTopElementPos(elem: DocumentContentElementBase): number {
        const type = elem.parent.getDocumentSectionType();
        let posIndex: number;
        switch (type) {
            case DocumentSectionType.Header: {
                posIndex = 0;
                break;
            }
            case DocumentSectionType.Document: {
                posIndex = 1;
                break;
            }
            case DocumentSectionType.Footer: {
                posIndex = 2;
                break;
            }
        }
        return posIndex;
    }

    /**
     * 选区内是否完全包含结构化元素
     * @param startPos 开始位置
     * @param endPos 结束位置
     */
    public isOverNewControl(startPos: ParagraphContentPos, endPos: ParagraphContentPos, param: IRegionParam): boolean {
        const newControlManager = this.getNewControlManager();
        // 后边框集合
        const behindNewControls = newControlManager.getBehindOverNewControls(startPos, endPos);
        // 前边框集合
        const forwardNewControls = newControlManager.getForwardOverNewControls(startPos, endPos);
        if (forwardNewControls.length !== behindNewControls.length) {
            return false;
        }

        for (let index = 0, count = behindNewControls.length; index < count; index++) {
            const name = behindNewControls[index].getNewControlName();
            if (!forwardNewControls.find((newControl) => newControl.getNewControlName() === name)) {
                return false;
            }
        }

        param.newControlNames = newControlManager.getNewControlNamesByPos(startPos, endPos);

        return true;
    }

    /**
     * 用于计算带分节符的空段落。
     * @param element
     * @param prevElement
     * @param pageIndex
     */
    public recalculateEmptySectionParagraph(element: DocumentContentElementBase,
                                            prevElement: DocumentContentElementBase, pageIndex: number): void {
        //
    }

    /**
     * 停止选择
     */
    public stopSelection( bShiftKey: boolean = false ): void {
        if ( true !== this.selection.bUse ) {
            return;
        }

        this.selection.bStart = false;

        if ( this.content[this.selection.startPos] ) {
            this.content[this.selection.startPos].stopSelection();
        }

        // 处理shift
        if ( true === bShiftKey ) {
            //
        }
    }

    /**
     * 元素内容删除
     * @param direction 删除方向：Backspace是删除光标前的元素：-1，Delete键是删除光标后面的元素：1
     * @param bOnlyText
     * @param bOnlySelection
     */
    public privateRemove(direction: number, bOnlyText: boolean, bOnlySelection: boolean,
                         bAddText: boolean, checkNewControls?: ICheckSelectionNewControls): IOperateResult {
        if ( this.curPos.contentPos < 0 ) {
            return {res: false, bNeedRecal: false};
        }

        // console.log(circularParse(circularStringify(this)));
        checkNewControls.direction = direction;
        let result = {res: true, bNeedRecal: true};
        let bSelectTop: boolean = false;
        const curDoc = this.getDocument();
        const commentManager: CommentManager = curDoc.getCommentManager();
        if ( true === this.selection.bUse ) {
            let startPos = this.selection.startPos; // 段落开始
            let endPos = this.selection.endPos; // 段落结束

            if ( startPos > endPos ) {
                const temp = startPos;
                startPos = endPos;
                endPos = temp;
            }

            // const newControlNames: string[] = [];
            const startContentPos = this.getStartRemoveSelectionPos();
            const endContentPos = this.getEndRemoveSelectionPos();
            const bTrackRevisions = this.isTrackRevisions();
            const bSelectedDocument = this.getSelectedContentFlag();
            let newControlList: NewControl[] = [];
            // console.log(bSelectedDocument)

            // if ( bTrackRevisions ) {
            //     const revisionsManager = this.getRevisionsManager();
            //     if ( false === revisionsManager.canDelete() ) {
            //         return false;
            //     }
            // }

            let portionIndex = -1;
            let bInsertNewControlsBorder = false;
            if ( this instanceof Document || this.isHeaderFooter(false) ) {
                bSelectTop = true;
                commentManager.prepareDeletedComments(startContentPos, endContentPos);
                const element = this.content[startPos];
                curDoc.forwardOverNewControls = this.getForwardOverNewControls(startContentPos, endContentPos);
                curDoc.behindOverNewControls = this.getBehindOverNewControls(startContentPos, endContentPos);

                if ( !(startPos === endPos && element.isTable()
                    && true === element.isCellSelection() && false === element.isSelectedAll() ) ) {
                    const newControlManager = this.getNewControlManager();
                    if ( bTrackRevisions ) {
                        newControlList = newControlManager.getNewControls(startContentPos, endContentPos);
                    }
                    newControlManager.remove(startContentPos, endContentPos);

                    if ( bTrackRevisions && 0 < newControlList.length ) {
                        for (let index = 0, length = newControlList.length; index < length; index++) {
                            const newControl = newControlList[index];
                            newControl.replaceTextToPara();
                        }
                    }
                }
                this.getNewControlManager()
                    .setTriggerCascadeStatus(true);
            }

            const forwardOverNewControls = this.getSelectForwardOverNewControls(this);
            const behindOverNewControls = this.getSelectBehindOverNewControls(this);

            bInsertNewControlsBorder = ( (forwardOverNewControls && 0 < forwardOverNewControls.length)
                                    || (behindOverNewControls && 0 < behindOverNewControls.length) );

            checkNewControls.bInsertNewControlsBorder = bInsertNewControlsBorder;
            if ( true === bInsertNewControlsBorder ) {
                checkNewControls.forwardOverNewControls = forwardOverNewControls;
                checkNewControls.behindOverNewControls = behindOverNewControls;
            }

            // 是否选中多个段落
            // let bSelectMultiPara = ( startPos !== endPos ) ? true : false;

            // 重置选择标记
            this.selection.bUse = false;
            this.selection.startPos = 0;
            this.selection.endPos = 0;

            // let bStartEmpty = false, bEndEmpty = false;
            // 检查开始段落是否被全部选中，或者为空段落
            const bStartEmpty = this.content[startPos].isEmpty() || this.content[startPos].isSelectedAll(true);
            const bEndEmpty = this.content[endPos].isEmpty() || this.content[endPos].isSelectedAll(false);
            const startElementType = this.content[startPos].getType();
            const endElementType = this.content[endPos].getType();

            if ( bTrackRevisions && bSelectedDocument === false ) {
                let tempEndPos = endPos;
                if ( this.content[endPos].isParagraph() && !bEndEmpty ) {
                    tempEndPos = endPos - 1;
                }

                let startParaPr = null;
                const startElem = this.content[startPos];
                if ( startElem.isParagraph && !bStartEmpty ) {
                    startParaPr = startElem.getParagraphProperty();
                }

                if ( startParaPr ) {
                    ;
                }

                result = {res: false, bNeedRecal: false};
                const removeRegionList: Region[] = [];
                for (let index = endPos; index >= startPos; index--) {
                    const element = this.content[index];
                    if ( element.isTable() ) {
                        if ( element.isSelectedAll() ) {
                            // element.removeTableRow();
                            result.res = this.contentRemove(index, 1) || result.res;
                            result.bNeedRecal = result.res;
                        } else if ( element.isCellSelection() ) {
                            result.res = element.removeTableRow2() || result.res;
                            result.bNeedRecal = result.res;
                        } else {
                            const temp = element.remove(1, true, bOnlySelection, bAddText, false);
                            result.res = temp.res || result.res;
                            result.bNeedRecal = temp.bNeedRecal || result.bNeedRecal;
                        }
                    } else {
                        if ( element && element.isRegion() && element.isSelectedAll() ) {
                            removeRegionList.push(element as Region);
                        }

                        const temp = element.remove(1, true, bOnlySelection, bAddText, false, checkNewControls);
                        result.res = temp.res || result.res;
                        result.bNeedRecal = temp.bNeedRecal || result.bNeedRecal;
                    }
                }

                this.removeSelection();

                const revisionsManager = this.getRevisionsManager();
                const curUserId = revisionsManager.getCurrentUserId();
                const curLevel = revisionsManager.getCurrentLevel();

                for (let index = tempEndPos; index >= startPos; index--) {
                    const element = this.content[index];
                    if ( !element ) {
                        continue;
                    }

                    const reviewType = element.getReviewType();
                    const reviewInfo = element.getReviewInfo();
                    const bDelLevel = ( curUserId === reviewInfo.getUserId() || curLevel > reviewInfo.getLevel() );

                    if ( element.isParagraph() && (endPos !== startPos && index !== endPos) ) {
                        if ( ReviewType.Add === reviewType && bDelLevel ) {
                            if ( element.isEmpty() ) {
                                this.contentRemove(index, 1);
                            } else if ( index < this.content.length - 1 && this.content[index + 1].isParagraph() ) {
                                element.concat(this.content[index + 1]);
                                this.contentRemove(index + 1, 1);
                            }
                        } else {
                            if ( ReviewType.Add === reviewType) {
                                const newReviewInfo = reviewInfo.copy();
                                newReviewInfo.savePrev(ReviewType.Add);
                                newReviewInfo.update();

                                element.setReviewTypeWithInfo(ReviewType.Remove, newReviewInfo);
                            } else {
                                element.setReviewType(ReviewType.Remove);
                            }
                        }
                    } else if ( element.isTable() ) {
                        if ( element instanceof TableBase && 0 >= element.getRowsCount() ) {
                            this.contentRemove(index, 1);
                        }
                    } else if ( element.isRegion() && element.isEmpty() ) {
                        this.contentRemove(index, 1);
                    }

                    this.curPos.contentPos = startPos;
                }

                for (let index = removeRegionList.length; index >= 0; index--) {
                    const region = removeRegionList[index];
                    if ( region ) {
                        this.removeRegionForTrackRevisions(region);
                    }
                }

                if ( bInsertNewControlsBorder ) {
                    for (const newControl of forwardOverNewControls) {
                        if ( newControl && newControl.getStartBorderPortion() ) {
                            const startBorderPortion = newControl.getStartBorderPortion();
                            startBorderPortion.setReviewTypeWithInfo(ReviewType.Common, new ReviewInfo(null));
                        }
                    }

                    for (const newControl of behindOverNewControls) {
                        if ( newControl && newControl.getEndBorderPortion() ) {
                            const endBorderPortion = newControl.getEndBorderPortion();
                            endBorderPortion.setReviewTypeWithInfo(ReviewType.Common, new ReviewInfo(null));
                        }
                    }
                }

                this.checkNewControlPlaceHolder(checkNewControls);
            } else {
                // startPos !== endPos: 光标选中至少两个段落，具体情况：
                // 1. startPos：选中段落尾部结束符，endPos：选中段落内容。
                // 2. startPos：选中段落尾部结束符，endPos：不选中段落任何内容。
                // 3. startPos：选中包括段落尾部结束符的内容，endPos：选中段落内容。
                // 4. startPos：选中包括段落尾部结束符的内容，endPos：不选中段落任何内容。
                // 然后，还需要考虑startPos，endPos段落是否为空段落，
                // 1. 如果为空，直接删除此段落,
                // 2. 如果选中的内容删除后为空段落，即选中整个段落，则直接删除此段落,
                let element = this.content[startPos];
                if ( startPos !== endPos ) {
                    direction = -1;

                    element = this.content[startPos];
                    // const elementType = element.getType();
                    if ( DocumentContentType.Paragraph === startElementType ) {
                        // portionIndex = element.getSelection().startPos + 1;
                        const dir = element.getSelectionDirection();
                        portionIndex = -1 === dir ? element.getSelection().endPos + 1
                                        : element.getSelection().startPos + 1;
                    } else if ( DocumentContentType.Table === startElementType ) {
                        if ( DocumentContentType.Paragraph === endElementType ) {
                            element = this.content[endPos];
                        }
                        portionIndex = 0;
                    }

                    let bRegionSelectAll = false;

                    // 选择尾部不会空，则先删除选中内容，删除选中内容后，该段落也不会为空段落
                    if ( DocumentContentType.Table !== endElementType ) {
                        if (endElementType === DocumentContentType.Region) {
                            bRegionSelectAll = this.content[endPos].isSelectedAll();
                        }
                        if (bRegionSelectAll === false) {
                            result = this.content[endPos].remove(direction, true, bOnlySelection,
                                                        bAddText, false, checkNewControls);
                        }
                    }

                    if ( bStartEmpty ) {
                        let deleteCount = 0;

                        if ( DocumentContentType.Paragraph === endElementType ) {
                            deleteCount = endPos - startPos;
                            element = this.content[endPos];
                            portionIndex = 0;
                        } else if ( DocumentContentType.Table === endElementType ) {
                            // endPos：选中table
                            // 1. 选中整个table，直接删除 [startPos，endPos]
                            // 2. 选中table的几行，则删除选中的rows

                            if ( true === this.content[endPos].isSelectedAll() ) {
                                deleteCount = endPos - startPos + 1;
                            } else {
                                deleteCount = endPos - startPos;
                                if ( false === this.content[endPos].removeTableRow2() ) {
                                    deleteCount += 1;
                                }
                            }
                        } else if (DocumentContentType.Region === endElementType) {
                            if ( true === bRegionSelectAll) {
                                deleteCount = endPos - startPos + 1;
                            } else {
                                deleteCount = endPos - startPos;
                            }
                        }

                        this.contentRemove(startPos, deleteCount);
                        this.curPos.contentPos = startPos;
                        this.content[startPos].moveCursorToStartPos();

                        if (0 < deleteCount) {
                            result.res = true;
                            result.bNeedRecal = true;
                        }
                    } else if ( !bStartEmpty ) {
                        // startPos:
                        //  1. 选择第一段不是空段落，且没有选中全部内容，
                        //  2. 选中table的几行，则删除选中的rows
                        if ( DocumentContentType.Paragraph === startElementType ) {
                            result = this.content[startPos].remove(direction, true, bOnlySelection,
                                                        bAddText, true, checkNewControls);
                        } else if ( DocumentContentType.Table === startElementType ) {
                            if ( false === this.content[startPos].removeTableRow2() ) {
                                this.contentRemove(startPos, 1);
                                result.res = true;
                                result.bNeedRecal = true;
                            }
                        } else if ( DocumentContentType.Region === startElementType ) {
                            result = this.content[startPos].remove(direction, true, bOnlySelection, bAddText, true);
                        }

                        let deleteCount = result ? endPos - startPos : endPos - startPos + 1;
                        const deleteStartPos = result ? startPos + 1 : startPos;
                        if ( DocumentContentType.Paragraph === endElementType ) {
                            // 开始位置为table，不需要进行此操作
                            if ( DocumentContentType.Paragraph === startElementType) {
                                if ( !bEndEmpty ) {

                                    // 将endPos段的内容连接到startPos段落
                                    const newContent = this.content[endPos].getContent()
                                                                                    .slice(0);
                                    this.content[startPos].contentConcat(newContent);
                                }
                            } else {
                                element = this.content[endPos];
                                portionIndex = 0;
                                deleteCount = endPos - startPos - 1;
                            }

                        } else if ( DocumentContentType.Table === endElementType ) {
                            if ( true === this.content[endPos].isSelectedAll() ) {
                                deleteCount = endPos - startPos;
                            } else {
                                deleteCount = endPos - startPos - 1;
                                if ( false === this.content[endPos].removeTableRow2() ) {
                                    // this.contentRemove(endPos, 1);
                                    deleteCount += 1;
                                }
                            }
                        } else if (DocumentContentType.Region === endElementType) {
                            if ( true === bRegionSelectAll) {
                                deleteCount = endPos - startPos;
                            } else {
                                deleteCount = endPos - startPos - 1;
                                // this.content[endPos].removeTableRow2();
                            }
                        }

                        if ( DocumentContentType.Paragraph === startElementType ) {
                            this.content[startPos].checkParaEnd(startPos);
                        }

                        // 删除的段落区间：(startPos，endPos]
                        this.contentRemove(startPos + 1, deleteCount);

                        this.curPos.contentPos = (DocumentContentType.Table === startElementType && result ?
                                                    startPos + 1 : startPos);

                        if ( DocumentContentType.Table === startElementType
                            && DocumentContentType.Table === endElementType ) {
                            this.content[this.curPos.contentPos].moveCursorToStartPos();
                        }

                        if (0 < deleteCount) {
                            result.res = true;
                            result.bNeedRecal = true;
                        }
                    }

                    if ( startPos >= this.content.length ) {
                        this.curPos.contentPos = this.content.length - 1;
                        this.content[this.curPos.contentPos].moveCursorToEndPos();
                    }
                    this.setDirty();
                } else {
                    this.curPos.contentPos = startPos;
                    element = this.content[startPos];
                    const elementType = element.getType();
                    if ( DocumentContentType.Paragraph === elementType ) {
                        const dir = element.getSelectionDirection();
                        portionIndex = -1 === dir ? element.getSelection().endPos + 1
                                        : element.getSelection().startPos + 1;
                    }

                    if ( DocumentContentType.Table === elementType && true !== bAddText ) {
                        if ( 0 > direction && true === element.isSelectedAll() ) {
                            this.removeTable();
                            this.setDirty();
                        } else if ( true === element.isCellSelection() ) {
                            if ( 0 > direction ) {
                                GlobalEvent.setEvent(curDoc.id, gEventName.TableEvent, MessageType.DeleteTableCells);

                                return {res: false, bNeedRecal: false};
                            } else if ( 0 < direction ) {
                                result = element.remove(direction, bOnlyText, bOnlySelection, bAddText);

                                if ( result ) {
                                    this.setDirty();
                                }

                                return result;
                            }
                        } else if ( true === element.isSelectionUse() ) {
                            result = element.remove(-1, true, bOnlySelection, bAddText, false);
                            // bOnlySelectTableCellContent = false;
                            this.setDirty();
                        }
                    } else {
                        result = element.remove(direction, true, bOnlySelection, bAddText, false, checkNewControls);
                        if ( result.res ) {
                                // && true === bInsertNewControlsBorder ) {
                            this.setDirty();
                        }
                    }
                }
                //  null != element && DocumentContentType.Paragraph === element.getType()
                // && true === bOnlySelectTableCellContent
                if (null != element && element.isParagraph() ) {
                    if ( bInsertNewControlsBorder ) {
                        let bFlag = false;
                        for (const newControl of forwardOverNewControls) {
                            if ( element.getParent() === newControl.getDocumentParent() ) {
                                bFlag = true;
                                const border = newControl.createNewControlBorder(element as Paragraph, true);
                                newControl.setBorder(border, true);
                                element.addToContent(portionIndex, border);
                            }
                        }

                        if (portionIndex >= element.getContent().length ) {
                            portionIndex = element.getContent().length - 1;
                        }

                        for (let index = behindOverNewControls.length - 1; index >= 0; index--) {
                            const newControl = behindOverNewControls[index];
                            if ( element.getParent() === newControl.getDocumentParent() ) {
                                bFlag = true;
                                const border = newControl.createNewControlBorder(element as Paragraph, false);
                                newControl.setBorder(border, false);
                                element.addToContent(portionIndex, border);
                            }
                        }

                        if (bFlag) {
                            result.bNeedRecal = true;
                        }
                    }
                    checkNewControls.para = element as Paragraph;
                }

                // if (comments && comments.length > 0) {
                //     this.getDocument()
                //         .deleteComments(comments.map((comment) => comment.data));
                // }
            }

        } else {

            let contentPos = this.curPos.contentPos;
            const elementType = this.content[contentPos].getType();

            // 段落内容删除
            if ( DocumentContentType.Paragraph === elementType ) {
                // 删除内容
                result = this.content[contentPos].remove(direction, bOnlyText, bOnlySelection, false);
                if ( false === result.res ) {
                    const para = this.content[contentPos] as Paragraph;
                    const removeNodeFlag: boolean =
                        para.removeHiddeNewControl(direction, bOnlyText, bOnlySelection, false);
                    if (removeNodeFlag === true) {
                        return result;
                        // contentPos = this.curPos.contentPos;
                    } else if (removeNodeFlag === false) {
                        contentPos = this.curPos.contentPos;
                    }
                    // Backspace删除
                    if ( 0 > direction ) {
                        if ( 0 < contentPos) { // } &&
                            const prevElement = this.content[contentPos - 1];
                            if ( prevElement.isParagraph() ) {
                                if ( true === prevElement.isEmpty() ) {
                                    // 上一个段落是空段落，只需删除上一个段落，光标移动到当前段落开头
                                    // const prevPara = this.content[contentPos - 1];
                                    const bPageBreakBefore = prevElement.hasPageBreak();
                                    this.contentRemove(contentPos - 1, 1);
                                    contentPos--;

                                    this.curPos.contentPos = contentPos;
                                    // 当遇到上一个空段落有分页符时，不能直接删除，得把分页符复制过来
                                    const actPara = this.content[contentPos] as Paragraph;
                                    if (bPageBreakBefore) {
                                        actPara.addPageBreak();
                                    } else if (actPara.hasPageBreak() === true) {
                                        // 当上一个段落是空行，当前段落有分页符
                                        actPara.removePageBreak();
                                    }
                                    this.content[contentPos].moveCursorToStartPos(false);
                                } else {
                                    // 上一个段落不是空段落，只需将当前段落和上一个段落连接为一个段落，连接后删除当前段落
                                    // 1. 先将光标移动到上一段段尾
                                    // const prevItem = this.content[contentPos - 1];

                                    prevElement.moveCursorToEndPos(false);

                                    // 2. 将当前段落连接到上一段末尾，然后删除当前段落
                                    prevElement.concat(this.content[contentPos], true);
                                    this.contentRemove(contentPos, 1);
                                    this.curPos.contentPos--;
                                }
                                result.res = true;
                                result.bNeedRecal = true;
                            } else if ( prevElement.isRegion() || prevElement.isTable() ) {
                                const region: any = prevElement;
                                const parent = region.getParent();
                                if (region.isRegion() && region.deleteCurrentHideRegion()) {
                                    // if (!) {
                                    //     return false;
                                    // }
                                    // contentPos--;
                                    this.curPos.contentPos = contentPos - 1;
                                    this.content[contentPos].moveCursorToStartPos(false);
                                    return {res: true, bNeedRecal: true};
                                }
                                if ( this.content[contentPos].isEmpty() // &&
                                    // ( (contentPos !== this.content.length - 1 && !parent.isRegionContent()))
                                    //   || ( contentPos === this.content.length - 1 && parent.isRegionContent())
                                    ) {
                                    if ( contentPos !== this.content.length - 1 ) {
                                        this.contentRemove(contentPos, 1, false);
                                    } else if ( contentPos === this.content.length - 1 && parent.isRegionContent() ) {
                                        if ( 2 !== this.content.length ) {
                                            this.contentRemove(contentPos, 1, false);
                                        } else {
                                            return {res: true, bNeedRecal: true};
                                        }
                                    }
                                }
                                contentPos--;
                                this.curPos.contentPos = contentPos;
                                this.content[contentPos].moveCursorToEndPos(false);
                            }
                        } else if (contentPos > 0) {
                            let pos = contentPos - 1;
                            pos = this.content[pos].jumpOrDeleteTheHidden(direction);
                            let bChange = false;
                            if (pos === undefined) {
                                pos = contentPos - 1;
                            } else {
                                pos = contentPos - pos;
                                bChange = true;
                            }
                            this.curPos.contentPos = pos;
                            this.content[pos].moveCursorToEndPos(false, false);
                            return {res: true, bNeedRecal: true};
                        } else if ( 0 === contentPos ) {
                            // 文档首页的首行位置，即文档打开初始化位置，光标位置不需要改变
                            return {res: false, bNeedRecal: false};
                        }
                    } else if ( 0 < direction ) { // Delete删除
                        const length = this.content.length - 1;
                        if ( length > contentPos ) { // }
                        //    && this.content[contentPos + 1].isParagraph() ) {
                        //     if ( true === this.content[contentPos].isEmpty() ) {
                        //         // 当前段落为空，只需删除当前段落，光标移动到下一个元素开头
                        //         this.contentRemove(contentPos, 1);
                        //         this.content[contentPos].moveCursorToStartPos(false);
                        //     } else {
                        //         // 当前段落不为空，只需将当前段落和下一段落连接为一个段落
                        //         const element = this.content[contentPos];
                        //         element.concat(this.content[contentPos + 1], true);
                        //         this.contentRemove(contentPos + 1, 1);
                        //     }
                        // }
                            if ( this.content[contentPos].isEmpty() ) {
                                if ( this.isRegionContent() && 2 === this.content.length && this.content[contentPos + 1]
                                    && this.content[contentPos + 1].isRegion()) {
                                        return {res: true, bNeedRecal: true};
                                }

                                this.contentRemove(contentPos, 1);
                                this.content[contentPos].moveCursorToStartPos(false);
                            } else if ( this.content[contentPos + 1].isParagraph() ) {
                                // 当前段落不为空，只需将当前段落和下一段落连接为一个段落
                                const element = this.content[contentPos];
                                element.concat(this.content[contentPos + 1], true);
                                this.contentRemove(contentPos + 1, 1);
                            } else {
                                const region = this.content[contentPos + 1];
                                if (region.isRegion() && region.isHidden()) {
                                    if ((region as any).deleteCurrentHideRegion()) {
                                        // this.curPos.contentPos = contentPos + 1;
                                        // this.content[contentPos].moveCursorToStartPos(false);
                                        return {res: true, bNeedRecal: true};
                                    }
                                    return {res: false, bNeedRecal: false};
                                }
                                contentPos++;
                                this.curPos.contentPos = contentPos;
                                this.content[contentPos].moveCursorToStartPos(false);
                            }
                        } else if ( true === this.content[contentPos].isEmpty()
                                    && 0 !== contentPos
                                    && length === contentPos
                                    &&  DocumentContentType.Paragraph === this.content[contentPos - 1].getType() ) {
                            // 文档最后一段是一个空段落，而且上一个元素不是table
                            this.contentRemove(contentPos, 1);
                            this.curPos.contentPos--;
                            this.content[this.curPos.contentPos].moveCursorToEndPos(false);
                        }  else if (length !== contentPos) {
                            let pos = contentPos + 1;
                            pos = this.content[pos].jumpOrDeleteTheHidden(direction);
                            let bChange = false;
                            if (pos === undefined) {
                                pos = contentPos + 1;
                            } else {
                                pos = contentPos + pos;
                                bChange = true;
                            }
                            this.curPos.contentPos = pos;
                            this.content[pos].moveCursorToStartPos(false, false);
                            return {res: bChange, bNeedRecal: bChange};
                        } else if ( length === contentPos ) {
                            this.content[contentPos].moveCursorToEndPos(false);
                        } else if ( length === contentPos ) {
                            // 文档末尾位置，光标位置不需要改变
                            return {res: false, bNeedRecal: false};
                        }
                    }

                    if ( this.content[contentPos] instanceof Paragraph) {
                        checkNewControls.para = this.content[contentPos] as Paragraph;
                    }
                }

                // 更新光标位置
                const item = this.content[contentPos];
                if ( item && DocumentContentType.Paragraph === item.getType() ) {
                    const curPos = item.getCurPos();
                    curPos.realX = curPos.x;
                    curPos.realY = curPos.y;
                }

                // this.setDirty();
            } else if ( DocumentContentType.Table === elementType ) {
                result = this.content[contentPos].remove(direction, bOnlyText, bOnlySelection, bAddText);
                // if ( this.content[contentPos].remove(direction, bOnlyText, bOnlySelection, bAddText) ) {
                //     // this.setDirty();
                // }
            } else if ( DocumentContentType.Region === elementType ) {
                result = this.content[contentPos].remove(direction, bOnlyText, bOnlySelection, bAddText);
                // if ( this.content[contentPos].remove(direction, bOnlyText, bOnlySelection, bAddText) ) {
                //     // this.setDirty();
                // }
            }
        }

        if (result.res === true && bSelectTop) { // TODO: 批注删除
            const portions = commentManager.clearDeletedComments();
            if (portions.length) {
                const content = this.content[this.curPos.contentPos];
                content.addChildrenToContent(portions, undefined, true);
                // this.content[this.curPos.contentPos].appendCommentPortion(position);
            }
            commentManager.clearUnValidComments(true);
            this.getNewControlManager()
                .setTriggerCascadeStatus(false)
                .then((res) => {
                    if (typeof res === 'function') {
                        res();
                    }
                });
        }

        return result;
    }

    public removeFromContent(): void {
        //
    }

    public getPageContentStartPos(startPageIndex: number, elementPageIndex: number, elementIndex: number = 0): ILimits {
        return null;
    }

    public getPageContentStartPos2( startPageIndex: number, elementPageIndex: number,
                                    elementIndex: number = 0): ILimits {
        return null;
    }

    public addToContent(curPos: number, newPara: DocumentContentElementBase, unEmptyPara?: boolean): void {
        // throw new Error("Method not implemented.");
    }

    /**
     * 在指定位置插入内容集合
     * @param items 插入项集合
     * @param curPos 插入位置
     * @param addAfter 是否插入在当前位置后面
     */
    public addChildrenToContent(items: DocumentContentElementBase[], curPos?: number, addAfter?: boolean): void {}

    public getCommentManager(): CommentManager {
        return this.getDocument()
        .getCommentManager();
    }

    public deleteCommentById(id: number): boolean {
        const manager = this.getCommentManager();
        const comment = manager.getCommentById(id);
        if (comment) {
            manager.deleteComments([comment]);
            return true;
        }
        return false;
    }

    public getAbsolutePage(curPage: number): number {
        return curPage;
    }

    public refreshRecalData2( index: number, pageIndex: number ): void {
        //
    }

    public getHistory(): History {
        return null;
    }

    /**
     * 插入table
     * @param cols 列
     * @param rows 行
     */
    public addInlineTableController(cols: number, rows: number, tableHeaderNum?: number,
                                    tableName?: string, bRepeatHeader?: boolean): boolean {
        if ( 0 >= cols || 0 >= rows ) {
            return ;
        }

        if ( 0 > this.curPos.contentPos ) {
            return;
        }

        const doc = this.getDocument();

        if ( null == doc ) {
            return false;
        }

        if ( false === this.canAddTable() ) {
            return ;
        }

        // 删除选中内容
        if ( true === this.selection.bUse ) {
            this.removeSelection();
            // this.remove(1, true);
            return;
        }

        const tableManager = doc.getTableManager();
        if ( null != tableName && ( null == tableManager || false === tableManager.checkTableName(tableName)) ) {
            return false;
        }

        // const history = doc.getHistory();
        // history.createNewHistoryPoint(HistoryDescriptionType.DocumentAddNewTable);
        let curPos = this.curPos.contentPos;
        let element = this.content[curPos];
        const type = element.getType();
        // 在段落中插入时，将段落分割成两端，在分割处插入表格
        // TODO：在table中插入table，即表中表
        if ( DocumentContentType.Paragraph === type || DocumentContentType.Region === type ) {
            doc.startAction(HistoryDescriptionType.DocumentAddNewTable);

            let parent = this;
            if (type === DocumentContentType.Region) {
                element = this.getCurrentParagraph();
                parent = (element as  Paragraph).getRegion()
                    .getOperateContent() as any;

                curPos = element.index;
            }
            // const sectPr = this.sectionsInfo.getSectPr(this.curPos.contentPos).sectProperty;
            const pageFields = this.getPageFields(this.curPage);
            let tableWidth = pageFields.xLimit - pageFields.x; // + 2 * 1.9; // mm
            const grid: number[] = [];

            tableWidth = Math.max(tableWidth, cols * 2); // * 1.9);

            // 均分表格：每列的宽度
            for (let index = 0; index < cols; index++) {
                grid[index] = tableWidth / cols;
            }

            const newTable = new Table(parent, doc, rows, cols, grid, tableHeaderNum, tableName);
            tableManager.add(newTable, tableName);

            if (false === bRepeatHeader) {
                newTable.property.setRepeatHeader(false);
            }

            if ( true === element.isCursorAtBegin() ) {
                newTable.moveCursorToStartPos();
                parent.addToContent(curPos, newTable, false);

                // 文档首页首行
                if ( null != newTable.getDocumentPrev() && this === element.getParent() ) {
                    this.curPos.contentPos++;
                }
            } else if ( true === element.isCursorAtEnd() && undefined === element.getSectPr() ) {
                newTable.moveCursorToStartPos();
                parent.addToContent(curPos + 1, newTable, false);
                parent.curPos.contentPos++;
            } else {
                const newPara = new Paragraph(parent, doc);
                element.split(newPara);

                parent.addToContent(curPos + 1, newPara);

                // 将光标放在表格的开头。
                newTable.moveCursorToStartPos();
                parent.addToContent(curPos + 1, newTable, false);

                parent.curPos.contentPos++;
            }

            doc.endAction();

            return true;
        }
    }

    /**
     * 是否为表格单元格内容
     */
    public isTableCellContent(): boolean {
        return false;
    }

    public getTableId(): number {
        return -1;
    }

    public getTableIndex(): number {
        return -1;
    }

    public getTableRowIndex(): number {
        return -1;
    }

    public getTableRow(): any {
        return null;
    }

    public getTableCellIndex(): number {
        return -1;
    }

    public getTableCell(): any {
        return null;
    }

    /**
     * 是否可以在当前光标处插入table
     */
    public canAddTable(): boolean {
        if ( false === this.canInput() ) {
            return false;
        }

        const newControl = this.getCursorInNewControl();
        if (newControl /*&& ( newControl.isNewTextBox() || newControl.isSpecialNewControl()
                            || newControl.isNewTextBox() )*/ ) {
            return false;
        }

        return true;
    }

    // public getTable(): any { return undefined; }

    public getSelectionDirection(): number {
        return 0;
    }

    /**
     * 是否只选中单个元素：即一个单元格内容
     */
    public isSelectedSingleElement(): boolean {
        return (
            true === this.selection.bUse && (DocCurPosType.Content === this.getDocPosType()
                                                || DocCurPosType.DrawingObjects === this.getDocPosType())
            && this.selection.startPos === this.selection.endPos
            && (SelectionFlagType.Common === this.selection.flag || SelectionFlagType.Drawing === this.selection.flag)
        );
    }

    /**
     * 是否只选中了表格的若干单元格
     */
    public isSelectedTableCells(): boolean {
        const element = this.getCurrentTable();
        if ( element && element.isTable() && true === element.isCellSelection() ) {
            return true;
        }

        return false;
    }

    public isSelectedTable(): boolean {
        const element = this.getCurrentTable();
        if ( element && element.isTable() && true === element.isSelectedAll() ) {
            return true;
        }

        return false;
    }

    /**
     * 判断当前光标是否在单元格内，或者只选中了单元格里面的内容
     */
    public isInTable(): boolean {
        const element = this.getCurrentTable();
        if ( element && element.isTable() ) {
            if ( element.isSelectionUse() ) {
                return ( false === element.isCellSelection() );
            } else {
                return true;
            }
        }

        return false;
    }

    /**
     * 判断当前光标，或当前选择区域是否在单元格内
     */
    public isInTableCell(): boolean {
        const element = this.getCurrentTable();
        if ( element && element.isTable() ) {
            return true;
        }

        return false;
    }

    public getCurrentTable(tableName?: string): TableBase {
        if ( this.selection.bUse ) {
            if ( this.selection.startPos === this.selection.endPos &&
                this.content[this.selection.startPos] ) {
                return this.content[this.selection.startPos].getCurrentTable();
            }
        } else {
            return this.content[this.curPos.contentPos] ?
                this.content[this.curPos.contentPos].getCurrentTable() : null;
        }

        return null;
    }

    public getTableByName(name: string): TableBase {
        let element = null;
        const doc = this.getDocument();

        if ( doc && doc.getTableManager() ) {
            const tableManager = doc.getTableManager();
            element = tableManager.getTableByName(name);
        }

        return element;
    }

    /**
     * 是否可以合并选中单元格
     */
    public canMergeTableCells(): boolean {
        const element = this.getCurrentTable();

        if ( null != element && element.isTable() && true === element.isCellSelection() ) {
            return element.canMergeTableCells();
        }

        return false;
    }

    public splitTableCells(rows: number, cols: number): boolean {
        const element = this.getCurrentTable();
        if ( null != element && element.isTable() ) {
            let doc = this.getDocument();
            if (doc instanceof DocumentContent) {
                doc = doc.logicDocument;
            }

            doc.startAction(HistoryDescriptionType.DocumentSplitTableCells);
            const result = element.splitTableCells(rows, cols);

            if ( true === result ) {
                this.updateSelectionState();
                this.updateCursorXY();
            }

            doc.endAction();

            return result;
        }

        return false;
    }

    public mergeTableCellsController(bClearMerge?: boolean): boolean {
        const element = this.getCurrentTable();

        if ( null != element && element.isTable() && element.isSelectionUse() ) {
            return element.mergeTableCells(bClearMerge);
        }

        return false;
    }

    public addTableRowController(bBefore: boolean): boolean {
        let result = false;

        if ((this.selection.bUse && this.selection.startPos === this.selection.endPos
                    && !this.content[this.selection.startPos].isParagraph())
            || (!this.selection.bUse && !this.content[this.curPos.contentPos].isParagraph())) {

            let pos;
            if (this.selection.bUse) {
                pos = this.selection.startPos;
            } else {
                pos = this.curPos.contentPos;
            }

            result = this.content[pos].addTableRow(bBefore);

            if ( result && false === this.selection.bUse && true === this.content[pos].isSelectionUse() ) {
                this.selection.bUse = true;
                this.selection.startPos = pos;
                this.selection.endPos = pos;
            }
        }

        return result;
    }

    public removeTableRowController(rowIndex?: number): boolean {
        if ( ( true === this.selection.bUse && this.selection.startPos === this.selection.endPos &&
                DocumentContentType.Paragraph !== this.content[this.selection.startPos].getType()  )
            || ( false === this.selection.bUse &&
                DocumentContentType.Paragraph !== this.content[this.curPos.contentPos].getType() ) ) {
            let pos = this.curPos.contentPos;

            if ( true === this.selection.bUse ) {
                pos = this.selection.startPos;
            }

            if ( false === this.content[pos].removeTableRow(rowIndex) ) {
                this.removeTable();
            }

            // todo: check newcontrols in delete tablerows
            return true;
        }

        return false;
    }

    public removeTableColumnController(): boolean {
        if ( ( true === this.selection.bUse && this.selection.startPos === this.selection.endPos
               && DocumentContentType.Paragraph !== this.content[this.selection.startPos].getType())
            || ( false === this.selection.bUse
                && DocumentContentType.Paragraph !== this.content[this.curPos.contentPos].getType() ) ) {
            let pos = this.curPos.contentPos;

            if ( true === this.selection.bUse ) {
                pos = this.selection.startPos;
            }

            if ( false === this.content[pos].removeTableColumn() ) {
                this.removeTable();
            }

            // todo: check newcontrols in delete tablecols
            return true;
        }

        return false;
    }

    public removeTableController(): boolean {
        if ( ( true === this.selection.bUse && this.selection.startPos === this.selection.endPos &&
                DocumentContentType.Table === this.content[this.selection.startPos].getType() )
            || ( false === this.selection.bUse
                && DocumentContentType.Paragraph !== this.content[this.curPos.contentPos].getType() ) ) {
            let pos = this.curPos.contentPos;

            if ( true === this.selection.bUse ) {
                pos = this.selection.startPos;
            }

            const table = this.content[pos];

            if ( table && table.isTable() ) {
                if ( true !== table.isInnerTable() ) {
                    this.removeSelection();
                    table.preDelete();

                    this.contentRemove(pos, 1);

                    if ( this.content.length - 1 <= pos ) {
                        pos--;
                    }

                    if ( pos <= 0 ) {
                        pos = 0;
                    }

                    this.curPos.contentPos = pos;
                    this.setDocPosType(DocCurPosType.Content);
                    this.content[pos].moveCursorToStartPos();
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 内容只读
     */
    // public isReadOnly(): boolean {
    //     return this.bReadOnly;
    // }

    // public setReadOnly(bReadOnly: boolean): void {
    //     this.bReadOnly = bReadOnly;
    // }

    /**
     * 光标处是否为只读状态
     */
    // public isCursorReadOnly(): boolean {
    //     let result = false;
    //     // const table = this.getCurrentTable();

    //     // if ( table ) {
    //     //     result = ( table.isReadOnly() || ( table.isSelectionUse() && table.isCanntDelete() ));
    //     // }

    //     // if ( true === result ) {
    //     //     return true;
    //     // }

    //     // const region = this.getCurrentRegion();
    //     // if ( region ) {
    //     //     result = region.isReadOnly();
    //     // }

    //     return result;
    // }

    public isCursorInNewControl(): boolean {
    //     return this.controller.isCursorInNewControl();
    // }

    // public isCursorInNewControlController(): boolean {
        let contentPos = this.curPos.contentPos;

        if ( true === this.selection.bUse ) {
            contentPos = this.selection.endPos;
        }

        if ( null != this.content[contentPos] ) {
            return this.content[contentPos].isCursorInNewControl();
        }

        return false;
    }

    public getCursorInNewControl(): NewControl {
    //     return this.controller.getCursorInNewControl();
    // }

    // public getCursorInNewControlController(): NewControl {
        if ( true === this.isCursorInNewControl() ) {
            let contentPos = this.curPos.contentPos;

            if ( true === this.selection.bUse ) {
                contentPos = this.selection.endPos;
            }

            return this.content[contentPos].getCursorInNewControl();
        }

        return null;
    }

    // public isPopWinNewControlController(): boolean {
    //     const newControl = this.getCursorInNewControl();
    //     let bPopWindow = false;

    //     if ( null != newControl ) {
    //         bPopWindow = newControl.isPopWindowNewControl();

    //         if ( newControl.isReadOnly() ) {
    //             return false;
    //         } else if ( newControl.isReverseEdit() && bPopWindow ) {
    //             return true;
    //         }
    //     }

    //     const table = this.getCurrentTable();
    //     if ( table && false === table.isPopWinNewControl() ) {
    //         return false;
    //     }

    //     const region = this.getCurrentRegion();
    //     if ( region && false === region.isPopWinNewControl() ) {
    //         return false;
    //     }

    //     return bPopWindow;
    // }

    public canInput(): boolean {
        return true;
    }

    public canDelete(direction?: number): boolean {
        return true;
    }

    public canInsertNewControl(): boolean {
        return true;
    }

    /**
     * 当前是否可进行编辑
     */
    public canInputController(): boolean {
        const newControl = this.getCursorInNewControl();
        const doc = this.getDocument();
        doc.setActiveNewControl(newControl);
        if ( null != newControl ) {
            if (newControl.isCheckBox() || newControl.isMultiAndRadio()) {
                return false;
            }

            const id: number = doc.id;
            if ( newControl.isReadOnly() ) {
                // alert('元素设置了只读保护，无法输入');
                // message.error('元素设置了只读保护，无法输入');
                gEvent.setEvent(id, gEventName.MessageEvent, newControl, MessageType.UnEdited);

                return false;
            } else if ( newControl.isSpecialNewControl() ) {
                return false;
            } else if ( newControl.isReverseEdit() ) {
                return true;
            }

            if ( newControl.isMoreThanMaxLength() ) {
                // alert('录入内容已超过最大限制长度');
                // newControl.setErrorTextBgColor(2);
                gEvent.setEvent(id, gEventName.MessageEvent, newControl, MessageType.MaxLength);
                // message.error('录入内容已超过最大限制长度');
                // return false;
            } else {
                newControl.setErrorTextBgColor();
            }

            if ( this.isSelectedOrCursorInNewControlTitle() ) {
                // alert('当前光标在元素标题中，无法输入');
                // message.error('当前光标在元素标题中，无法输入');
                gEvent.setEvent(id, gEventName.MessageEvent, newControl, MessageType.EditTitle);
                return false;
            }
        } else {
            const region = this.getCurrentRegion();
            if ( region && false === region.canInput() ) {
                return false;
            }

            if (region && this.isStrictMode2()) {
                return true;
            }
        }

        if ( this.selection.bUse ) {
            return this.canDelete();
        } else {
            const table = this.getCurrentTable();
            if ( table && false === table.canInput() ) {
                return false;
            }

            if ( this.isStrictMode2() ) {
                if ( newControl ) {
                    return true;
                } else if ( false === this.isAdminMode() ) {
                    const curRegion = this.getCurrentRegion();
                    if ( curRegion ) {
                        return !curRegion.isReadOnly();
                    }
                }

                return false;
            }
        }

        return true;
    }

    public filterContentNodes(type: number): any[] {
        return [];
    }

    /**
     * 当前是否可删除
     */
    public canDeleteController(direction?: number): boolean {
        const newControl = this.getCursorInNewControl();
        // if ( this.selection.bUse && null != newControl ) {
        //     if ( newControl.isDeleteProtect() ) {
        //         alert('元素设置了删除保护，无法删除');
        //         return false;
        //     }
        // }
        // if (!newControl && this.isStrictMode2()) {
        //     return false;
        // }
        const doc = this.getDocument();
        const id: number = doc.id;
        doc.setActiveNewControl(newControl);

        const bInTitle = this.isSelectedOrCursorInNewControlTitle();

        if ( newControl && bInTitle ) {
            gEvent.setEvent(id, gEventName.MessageEvent, newControl, MessageType.DeleteTitle);
            return false;
        } else if ( bInTitle ) {
            let start: number;
            const selection = this.selection;
            if ( selection.bUse) {
                start = selection.startPos;
                const end = selection.endPos;
                if ( start > end) {
                    start = end;
                }
            } else {
                start = this.curPos.contentPos;
            }
            // alert('当前光标在元素标题中，无法删除');
            // message.error('当前光标在元素标题中，无法删除');
            gEvent.setEvent(id, gEventName.MessageEvent, this.content[start], MessageType.DeleteTitle);
            return false;
        }

        const bStrictMode = this.isStrictMode2();
        if ( this.selection.bUse ) {
            const startContentPos = this.getStartRemoveSelectionPos();
            const endContentPos = this.getEndRemoveSelectionPos();
            if ( false === this.isValidDelete(startContentPos, endContentPos) ) {
                let start: number;
                const selection = this.selection;
                if ( selection.bUse) {
                    start = selection.startPos;
                    const end = selection.endPos;
                    if ( start > end) {
                        start = end;
                    }
                }
                // let id: number;
                // if (this instanceof DocumentContent) {
                //     id = this.logicDocument.id;
                // } else {
                //     id = this.id;
                // }
                // alert('当前选中的结构化元素无法删除');
                gEvent.setEvent(id, gEventName.MessageEvent, this.content[start], MessageType.UnEdited);
                // message.error('当前选中的结构化元素无法删除');
                return false;
            }

            const newControl2 = this.getNewControlManager()
                .getSelectionInNewControl(startContentPos, endContentPos);
            const bContain = newControl2?.isLayOverNewControl(startContentPos, endContentPos);
            if (newControl2 &&
                (bContain ? !newControl2.isDeleteProtect() : newControl2.isReverseEdit2())
                ) {
                // if ( !this.isTrackRevisions() ) {
                //     return true;
                // } else {
                return newControl2.canDelete();
                // }
            }

            if ( bStrictMode ) {
                if ( newControl2) {
                    return !newControl2.isReadOnly();
                }
                // else if ( false === this.isAdminMode() ) {
                //     const curRegion = this.getCurrentRegion();
                //     if ( curRegion ) {
                //         if (curRegion.isReadOnly()) {
                //             gEvent.setEvent(id, gEventName.MessageEvent, this.content[start], MessageType.UnEdited);
                //             return false;
                //         }
                //     }
                // }
            }

            let startPos = this.selection.startPos; // 段落开始
            let endPos = this.selection.endPos; // 段落结束

            if ( startPos > endPos ) {
                const temp = startPos;
                startPos = endPos;
                endPos = temp;
            }
            // const bAdminMode = this.isAdminMode();
            for (let index = startPos; index <= endPos; index++) {
                const item = this.content[index];
                // if (!bAdminMode && item.isRegion() && item.isReadOnly()) {
                //     gEvent.setEvent(id, gEventName.MessageEvent, item, MessageType.UnEdited);
                //     return false;
                // }
                if ( item && false === item.canDelete() ) {
                    return false;
                }
            }

            const bRegionContent = this.isRegionContent();

            if ( bStrictMode && !bRegionContent ) {
                if ( startPos === endPos && this.content[startPos].isRegion() ) {
                    return true;
                }

                return false;
            }
        } else {
            // const newControl2 = this.getCursorInNewControl();
            if (newControl) {
                const para = newControl.getParagraph();
                if (newControl.isSpecialNewControl() || newControl.isReadOnly() ) {
                    return false;
                } else if ( newControl.isReverseEdit2() ) {
                    return newControl.canDelete();
                } else if ( bStrictMode ) {
                    return true;
                } else if (newControl.isCursorInNewControlTitle(para.getCurContentPosInDoc(), direction)) {
                    return false;
                }
                // return !newControl.isReadOnly();
            }

            const table = this.getCurrentTable();
            if ( table && false === table.canDelete() ) {
                return false;
            }

            const region = this.getCurrentRegion();
            if ( region && false === region.canDelete() ) {
                return false;
            }

            if (bStrictMode && null == region) {
                return false;
            }
        }

        return true;
    }

    public canInsertNewControlController(): boolean {
        if ( this.isSelectedOrCursorInNewControlTitle() ) {
            return false;
        }

        const bStrictMode = this.isStrictMode2();
        if ( this.selection.bUse ) {
            const bAdminMode = this.isAdminMode();
            let startPos = this.selection.startPos;
            let endPos = this.selection.endPos;

            if ( startPos > endPos ) {
                startPos = this.selection.endPos;
                endPos = this.selection.startPos;
            }
            // const startContentPos = this.getStartRemoveSelectionPos();
            // const endContentPos = this.getEndRemoveSelectionPos();
            // const newControlManager = this.getNewControlManager();
            // if ( newControlManager && newControlManager.isSelectionInNewControl() ) {
            //     const
            // }
        } else {
            const newControl = this.getCursorInNewControl();
            if (newControl) {
                if ( newControl.isNewSection() ) {
                    return !newControl.isReadOnly();
                } else {
                    return false;
                }
            }

            const table = this.getCurrentTable();
            if ( table && false === table.canInsertNewControl() ) {
                return false;
            }

            const region = this.getCurrentRegion();
            if ( region && false === region.canInsertNewControl() ) {
                return false;
            }

            if (bStrictMode && null == region) {
                return false;
            }
        }

        return true;
    }

    public isTableCellProtected(): boolean {
        const element = this.getCurrentTable();
        if ( element && element.isTable() ) {
            return element.isTableCellProtected();
        }

        return false;
    }

    public setTableCellProtected(bProtected: boolean): void {
        const element = this.getCurrentTable();
        if ( element && element.isTable() ) {
            return element.setTableCellProtected(bProtected);
        }
    }

    public getTableCellName(): string {
        const element = this.getCurrentTable();
        if ( false === this.selection.bUse &&
            null != element && element.isTable() ) {
            return element.getTableCellName();
        }

        return undefined;
    }

    public addTableRow(bBefore: boolean): boolean {
      return false;
    }

    public removeTableRow(rowIndex?: number): boolean { return false; }

    public removeTable(): boolean { return false; }

    public removeTableColumn(): boolean { return false; }

    public recalculate(): void  { return ; }

    // 局部排版：针对一次操作中，需要进行多次排版的情况，不需要每次都对changes的所有改变进行排版
    public recalculateByChanges(arrChanges: any[], startIndex?: number, endIndex?: number): void { return ; }

    public isTableFirstRowOnNewPage(): boolean {
        return true;
    }

    public isRegionFirstOnNewPage(): boolean {
        return true;
    }

    public updateCursorXY(): void { return ; }

    public updateSelectionState(): void { return ; }

    public getCurrentParagraph(): DocumentContentElementBase {
      return null;
    }

    public getSelectionBounds(mouseEvent?: any, pageIndex?: any): any {
        return null;
    }

    public getSelection(): Selection { return this.selection; }

    public getDrawingObjects(): GraphicObjects {
        return null;
    }

    public setDrawingObjects(drawingObjects: GraphicObjects): void {
        return null;
        // TODO: if exist reference to graphicObject's props, would still preserve. may be unexpected
    }

    public getNewControlManager(): NewControlManager { return ; }

    public addNewControl(property: INewControlProperty): number {
        return 0;
    }

    public getRegionManager(): RegionManager {
        return;
    }

    // public isCursorInNewControl(): boolean {
    //     return false;
    // }

    public removeSelection(): void { return ; }

    public getNewControlBounds(newControl: NewControl): IDrawNewControlBounds {
        return null;
    }

    /**
     * 获取删除的选择区域的开始位置
     */
    public getStartRemoveSelectionPos(): ParagraphContentPos {
        let startPos = this.selection.startPos;

        if ( this.selection.startPos > this.selection.endPos ) {
            startPos = this.selection.endPos;
        }

        return this.content[startPos].getCurContentPosInDoc(this.selection.bUse, true, true);
    }

    /**
     * 获取删除的选择区域的结束位置
     */
    public getEndRemoveSelectionPos(): ParagraphContentPos {
        let endPos = this.selection.endPos;

        if ( this.selection.startPos > this.selection.endPos ) {
            endPos = this.selection.startPos;
        }

        return this.content[endPos].getCurContentPosInDoc(this.selection.bUse, false, true);
    }

    public getForwardOverNewControls( startPos: ParagraphContentPos, endPos: ParagraphContentPos ): NewControl[] {
        const newControlManager = this.getNewControlManager();
        if ( null != newControlManager ) {
            return newControlManager.getForwardOverNewControls(startPos, endPos);
        }

        return null;
    }

    public getBehindOverNewControls( startPos: ParagraphContentPos, endPos: ParagraphContentPos ): NewControl[] {
        const newControlManager = this.getNewControlManager();
        if ( null != newControlManager ) {
            return newControlManager.getBehindOverNewControls(startPos, endPos);
        }

        return null;
    }

    public getLayOverNewControls( startPos: ParagraphContentPos, endPos: ParagraphContentPos ): string[] {
        const newControlManager = this.getNewControlManager();
        if ( null != newControlManager ) {
            return newControlManager.getNewControlNamesByPos(startPos, endPos, false);
        }

        return null;
    }

    public isContainNewControls(startContentPos: ParagraphContentPos, endContentPos: ParagraphContentPos): boolean {
        return false;
    }

    /**
     * 当前选择区域是否有不可删除内容
     * 或者当前光标处不可删除
     * @param startPos
     * @param endPos
     * @param newControlNames : 不可删除newControls名称
     */
    public isValidDelete( startPos: ParagraphContentPos, endPos: ParagraphContentPos,
                          newControlNames?: string[] ): boolean {
        let result = true;
        // const regionManager = this.getRegionManager();
        // if (regionManager && !regionManager.isValidDelete( startPos, endPos)) {
        //     return false;
        // }
        const newControlManager = this.getNewControlManager();
        if ( null != newControlManager ) {
            result = !this.isSelectedOrCursorInNewControlTitle();
            result = ( result && newControlManager.isValidDelete(startPos, endPos, newControlNames));
            result = ( result && newControlManager.isValidDeleteSpecialNewCtrls(startPos, endPos, newControlNames));
        }

        return result;
    }

    /**
     * 检查newControl的占位符内容
     * @param checkNewControls
     */
    public checkNewControlPlaceHolder( checkNewControls: ICheckSelectionNewControls ): boolean {
        let bChanged = false;
        if ( null != checkNewControls && null != checkNewControls.para ) {
            let res: NewControl;

            // 记录当前记录点的已有的子记录数
            const history = this.getHistory();
            const curRecalIndex = history.getCurRecalIndex();
            const startIndex = -1 !== curRecalIndex ? history.points[curRecalIndex]?.items.length : -1;

            let para;
            if ( true === checkNewControls.bInsertNewControlsBorder ) {
                para = checkNewControls.para;
                for (const newControl of checkNewControls.behindOverNewControls) {
                    para.checkNewControlPlaceContent(newControl, checkNewControls['textPro']);
                    if (newControl.isHidden()) {
                        bChanged = newControl.getEndBorderPortion()
                                    .setHidden(true) || bChanged;
                    }
                }

                for (const newControl of checkNewControls.forwardOverNewControls) {
                    res = para.checkNewControlPlaceContent(newControl, checkNewControls['textPro']);
                }
            } else {
                res = checkNewControls.para.checkNewControlPlaceContent(null, checkNewControls['textPro']);
            }

            // let para: any;
            if (checkNewControls.direction === 1) {
                para = checkNewControls.para;
            } else {
                para = checkNewControls.para.parent.content[checkNewControls.para.index - 1];
            }

            if (para && para.isParagraph()) {
                bChanged = para.checkNewControlHidden(res) || bChanged;
            }

            const endIndex = -1 !== curRecalIndex ? history.points[curRecalIndex]?.items.length : -1;
            if (startIndex < endIndex) {
                // 对此次的文档变化进行局部排版
                const arrChanges = history.getChangesFromPoint(curRecalIndex, null);

                this.getDocument()
                        .recalculateByChanges(arrChanges, startIndex, endIndex);
                this.updateCursorXY();
            }
        }

        return bChanged;
    }

    public getSelectBehindOverNewControls(parent: DocumentContentBase): NewControl[] {
        return null;
    }

    public getSelectForwardOverNewControls(parent: DocumentContentBase): NewControl[] {
        return null;
    }

    public isHeaderFooter(bReturnHeaderFooter: boolean): boolean | HeaderFooter {
        if (bReturnHeaderFooter === true) {
            return null;
        }
        return false;
    }

    /**
     * 通过段落id获取段落index
     * @param paraId 段落id
     */
    public getParaIndexById( paraId: number ): number {
        if ( -1 === paraId || null == paraId ) {
            return null;
        }

        for (const element of this.content) {
            if ( paraId === element.id && element.isParagraph() ) {
                return element.index;
            }
        }

        return -1;
    }

    /**
     * 文档是否被修改
     */
    public isDirty(): boolean {
        return false;
    }

    /**
     * 设置文档脏标记
     * @param bDirty 默认为true：已被修改
     */
    public setDirty( bDirty: boolean = true ): void {
        // this.bDirty = bDirty;
    }

    public isStrictMode2(): boolean {
        const sectionType = this.getDocumentSectionType();

        if ( DocumentSectionType.Document === sectionType ) {
            const doc = this.getDocument();
            if ( doc ) {
                return doc.isStrictMode2();
            }
        }

        return false;
    }

    public isAdminMode(): boolean {
        const doc = this.getDocument();
        if ( doc ) {
            return doc.isAdminMode();
        }

        return false;
    }

    public contentRemove(nPos: number, count: number, bCheckLastElement?: boolean, bNotDeleteGrf?: boolean): boolean {
        return false;
    }

    public setCursorType(type: CursorType): void {
        return ;
    }

    public isSelectedOrCursorInNewControlTitle(): boolean {
        if ( this.selection.bUse ) {
            const startPos = this.selection.startPos;
            const endPos = this.selection.endPos;

            return (this.content[startPos].isSelectedOrCursorInNewControlTitle()
                    || this.content[endPos].isSelectedOrCursorInNewControlTitle());
        } else {
            return this.content[this.curPos.contentPos].isSelectedOrCursorInNewControlTitle();
        }

        // if (  DocumentContentType.Paragraph === this.content[startPos].getType() ) {
        //     result = result && this.content[startPos].isSelectedInNewControlTitle();
        // } else {
        //     ;
        // };

        // return result;
    }

    public isTrackRevisions(): boolean {
        return false;
    }

    public getSelectedContentFlag(): boolean {
        return false;
    }

    public getRevisionsManager(): any {
        return null;
    }

    // public getFocusRevision(pointX: number, pointY: number, pageIndex: number): any { return null; }

    public acceptRevisions(bAll: boolean = true): void {
        const trackManager = this.getRevisionsManager();
        if ( null == trackManager ) {
            return ;
        }

        if ( this.selection.bUse || bAll ) {
            let startPos = this.selection.startPos;
            let endPos = this.selection.endPos;
            const length = this.content.length;

            if ( startPos < endPos ) {
                startPos = this.selection.endPos;
                endPos = this.selection.startPos;
            }

            if ( bAll ) {
                startPos = 0;
                endPos = length - 1;
            }

            for (let pos = startPos; pos <= endPos; pos++) {
                const element = this.content[pos];
                element.acceptRevisionChanges(bAll);

                if ( element.isParagraph() ) {
                    element.acceptPrChanges();
                }
            }

            for (let pos = endPos; pos >= startPos; pos--) {
                const element = this.content[pos];
                if ( element.isParagraph() ) {
                    const reviewType = element.getReviewType();
                    const reviewInfo = element.getReviewInfo();

                    if ( ReviewType.Add === reviewType ) {
                        element.setReviewType(ReviewType.Common);
                    } else if ( ReviewType.Remove === reviewType ) {
                        element.setReviewType(ReviewType.Common);
                        this.concatParagraphs(pos, true);
                    }
                // } else if ( element.isTable() || element.isRegion() ) {
                //     element.acceptRevisionChanges(bAll);
                }
            }
        }
    }

    public rejectRevisions(bAll: boolean = true): void {
        const trackManager = this.getRevisionsManager();
        if ( null == trackManager ) {
            return ;
        }

        if ( this.selection.bUse || bAll ) {
            let startPos = this.selection.startPos;
            let endPos = this.selection.endPos;
            const length = this.content.length;

            if ( startPos < endPos ) {
                startPos = this.selection.endPos;
                endPos = this.selection.startPos;
            }

            if ( bAll ) {
                startPos = 0;
                endPos = length - 1;
            }

            for (let pos = startPos; pos <= endPos; pos++) {
                const element = this.content[pos];
                element.rejectRevisionChanges(bAll);

                if ( element.isParagraph() ) {
                    element.rejectPrChanges();
                }
            }

            for (let pos = endPos; pos >= startPos; pos--) {
                const element = this.content[pos];
                if ( element.isParagraph() ) {
                    const reviewType = element.getReviewType();

                    if ( ReviewType.Add === reviewType ) {
                        element.setReviewType(ReviewType.Common);
                        this.concatParagraphs(pos, true);
                    } else if ( ReviewType.Remove === reviewType ) {
                        element.setReviewType(ReviewType.Common);
                    }
                // } else if ( element.isTable() || element.isRegion() ) {
                //     element.rejectRevisionChanges(bAll);
                }
            }
        }
    }

    public setCurrentElement(index: number, bUpdateStates: boolean): void { return ; }

    public setHdrFtrFirstPage(value: boolean): void {
        return null;
    }

    public getTopElement(): DocumentContentElementBase {
        return;
    }

    public isRegionContent(): boolean {
        return false;
    }

    public isInRegion(): boolean {
        if ( this.selection.bUse ) {
            if ( this.selection.startPos === this.selection.endPos ) {
                return ( this.content[this.selection.startPos].isRegion());
            }
        } else {
            return ( this.content[this.curPos.contentPos].isRegion());
        }
    }

    public getCurrentRegion(): Region {
        if ( this.selection.bUse ) {
            if ( this.selection.startPos === this.selection.endPos ) {
                return this.content[this.selection.startPos].getCurrentRegion();
            }
        } else {
            if ( this.content[this.curPos.contentPos] ) {
                return this.content[this.curPos.contentPos].getCurrentRegion();
            }
        }

        return null;
    }

    public getRegionByName(regionName: string): Region {
        let element = null;
        const doc = this.getDocument();

        if ( doc && doc.getRegionManager() ) {
            const regionManager = doc.getRegionManager();
            element = regionManager.getRegionByName(regionName);
        }

        return element;
    }

    // public getRegion(bInTable: boolean = true): Region { return undefined; }

    public getTopDocument(): DocumentContentBase {
        return this;
    }

    public getDocumentSectionType(): DocumentSectionType {
        return DocumentSectionType.Document;
    }

    public canAddNewParagraphByEnter(): boolean {
        // const element = this.content[this.curPos.contentPos];
        const para = this.getCurrentParagraph();
        const parent = para ? para.parent : null;

        if ( parent && parent instanceof DocumentContent ) {
            if ( parent.isTableCellContent() ) {
                const table = ( parent as DocumentContent).getTable();

                if (table.isCursorAtBegin() || table.isCursorAtEnd()) {
                    return true;
                }
            } else if ( parent.isRegionContent() ) {
                // if ( parent.isCursorAtBegin() || parent.isCursorAtEnd() ) {
                return true;
                // }
            }
        }

        return false;
    }

    public addNewParagraphByEnter(): void {
        const para = this.getCurrentParagraph();
        const parent = para ? para.parent : null;

        if ( parent && parent instanceof DocumentContent ) {
            if ( parent.isTableCellContent() || parent.isRegionContent() ) {
                const element = parent.isTableCellContent() ? (parent as DocumentContent).getTable()
                                        : ( parent as DocumentContent).getRegion();
                const elementParent = element.parent;
                const newPara = new Paragraph(elementParent, this.getDocument());

                const pos = (element.isCursorAtBegin() ? element.index : element.index + 1);
                elementParent.addToContent(pos, newPara);

                elementParent.curPos.contentPos = pos;
                elementParent.recalculate();
                elementParent.updateCursorXY();
            }
        }
    }

    public isSelectedAll(): boolean {
        return false;
    }

    public getElementStartPos(element: DocumentContentElementBase): ParagraphContentPos {
        const posIndex = this.getTopElementPos(element);
        const contentPos = new ParagraphContentPos();
        contentPos.clear();
        element.getStartPos(contentPos);
        element.getParentPos(contentPos);
        contentPos.splice(0, 0, [posIndex]);

        return contentPos;
    }

    public getElementEndPos(element: DocumentContentElementBase): ParagraphContentPos {
        const posIndex = this.getTopElementPos(element);
        const contentPos = new ParagraphContentPos();
        contentPos.clear();
        element.getEndPos(true, contentPos);
        element.getParentPos(contentPos);
        contentPos.splice(0, 0, [posIndex]);

        return contentPos;
    }

    public getApplyToAll(): boolean {
        return false;
    }

    public isHeaderFooterContent(): boolean {
        return false;
    }

    public getContentByPos(pos: any): any { return null; }

    public isFirstElementOnPage(curPage: number, index: number): boolean {
        if (!this.getPages[curPage]) {
            return false;
        }

        return (this.getPages[curPage].pos === index);
    }

    /**
     * 清除修订信息
     */
    public resetRevisions(): void {
        this.content?.forEach((element) => {
            element?.resetRevisions();
        });
    }

    protected createNewParagraph(): DocumentContentElementBase {
        const oPara = new Paragraph(this, this.getDocument());
        oPara.moveCursorToStartPos();
        return oPara;
    }

    /**
     * 在修订状态下，删除被整个选中的region
     * @param region 被整个选中的region
     */
    protected removeRegionForTrackRevisions(region: Region): void {
        const index = region.index;
        const regionManager = this.getRegionManager();
        const regionContent = region.getContent();

        regionContent.forEach((element) => {
            this.addToContent(index + 1, element);
        });
        this.contentRemove(index, 1);
        regionManager.delete(region.getName());

        this.curPos.contentPos = index;
        this.content[this.curPos.contentPos].moveCursorToStartPos();
    }

    /**
     * 获取选中区域索引，表格，结构化元素，区域，不能交叉只能被包含或者包含
     */
    private getSeletedRegionInfo(): IRegionParam {
        const selection = this.selection;
        if (selection.bUse !== true) {
            return this.getRegionInfo1();
        }

        let start = selection.startPos;
        let end = selection.endPos;
        if (start > end) {
            const temp = start;
            start = end;
            end = temp;
        }

        const contents = this.content;

        if (start === end) {
            const content = contents[start];
            const eleType = content.getType();
            if (eleType === DocumentContentType.Table) {
                if (content.isSelectedAll() === false) {
                    return;
                }
                return {
                    indexs: [start],
                    newControlNames: [],
                };

            } else if (eleType === DocumentContentType.Region) {
                if (content.isReadOnly() || (content as any).isContainerTitle()) {
                    return;
                }
                return this.getRegionInfo2(content);
            }
        } else {
            const startElem = contents[start];
            if (startElem.isRegion()) {
                if (startElem.isSelectedAll() !== true) {
                    return;
                }
            }
            const endElem = contents[end];
            if (endElem.isRegion()) {
                if (endElem.isSelectedAll() !== true) {
                    return;
                }
            }
        }

        const param: IRegionParam = {
            indexs: [start, end],
            newControlNames: [],
        };
        const flag = this.getSelectedOverNewControl(contents[start], contents[end], param);
        if (flag === false) {
            return;
        }
        return param;
    }

    private getRegionInfo2(content: DocumentContentElementBase): IRegionParam {
        let doc: Document;
        if (this instanceof DocumentContent) {
            doc = this.logicDocument;
        } else {
            doc = this as any;
        }

        // const dir = doc.getSelectionDirection();
        const startPos: ParagraphContentPos = doc.getSelectionNodePos();
        let depth = startPos.getDepth() - 1;
        startPos.splice(depth, 2);
        const endPos = doc.getSelectionNodePos(false);
        const para = doc.getElementByPos(startPos) as Paragraph;
        if (!para) {
            return;
        }

        endPos.splice(endPos.getDepth() - 1, 2);
        const para2 = doc.getElementByPos(endPos) as Paragraph;
        // if (para2 === para) {
        //     return;
        // }
        const parent1: DocumentContent = para.parent as any;
        const parent2: DocumentContent = para2.parent as any;
        if (parent1 !== parent2) {
            return this.getRegionInfo3(startPos, endPos, doc);
        } else {
            if (!(parent1 instanceof DocumentContent) || !parent1.isRegionContent()) {
                return;
            }
        }

        if ((parent1.parent as Region).isReadOnly()) {
            return;
        }

        depth = startPos.getDepth();
        let start = startPos.get(depth);
        let end = endPos.get(depth);
        if (start > end) {
            const temp = start;
            start = end;
            end = temp;
        }
        const contents = parent1.content;
        if (start === 0 && end === contents.length - 1) {
            return {errorStr: '当前位置已经存在一个区域！'};
        }
        const startElem = contents[start];
        let type = startElem.getType();
        if (type === DocumentContentType.Region || type === DocumentContentType.Table) {
            if (!startElem.isSelectedAll()) {
                return;
            }
        }
        if (start !== end) {
            const endElem = contents[end];
            type = endElem.getType();
            if (type === DocumentContentType.Region || type === DocumentContentType.Table) {
                if (!startElem.isSelectedAll()) {
                    return;
                }
            }
        }

        const param: IRegionParam = {
            parent: parent1.parent as any,
            indexs: [start, end],
            newControlNames: [],
        };

        const actFlag = this.getSelectedOverNewControl(para, para2, param);
        if (actFlag === false) {
            return;
        }

        return param;
    }

    /**
     * 嵌套在区域内
     * @param startPos 开始位置
     * @param endPos 结束位置
     * @param doc document
     */
    private getRegionInfo3(startPos: ParagraphContentPos, endPos: ParagraphContentPos, doc: Document): IRegionParam {
        const sameParent = this.getSameParent(startPos, endPos, doc);
        if (!sameParent) {
            return;
        }
        const depth = startPos.getDepth();
        let start = startPos.get(depth);
        let end = endPos.get(depth);
        const contents = sameParent.content;
        if (start === 0 && end === contents.length - 1) {
            return {errorStr: '当前位置已经存在一个区域！'};
        }
        if (start > end) {
            const temp = start;
            start = end;
            end = temp;
        }
        const param: IRegionParam = {
            parent: sameParent.parent as any,
            indexs: [start, end],
            newControlNames: [],
        };
        const actFlag = this.getSelectedOverNewControl(contents[start], contents[end], param);
        if (actFlag === false) {
            return;
        }

        return param;
    }

    private getSameParent(startPos: ParagraphContentPos, endPos: ParagraphContentPos, doc: Document):
        DocumentContent {
        const data1 = startPos.data;
        const data2 = endPos.data;
        let index: number = 0;
        for (length = data1.length; index < length; index++) {
            if (data1[index] !== data2[index]) {
                break;
            }
        }

        // const copyPos = startPos.copy();
        const arrs = startPos.splice(index, data1.length - index);
        const parent = doc.getElementByPos(startPos);
        // if (parent.isTableCellContent()) {
        //     return;
        // }
        // if (parent instanceof TableRow) {
        //     return;
        // }
        // if (parent instanceof Table) {
        //     return;
        // }
        if (!('getType' in parent)) {
            return;
        }
        if (parent.getType() === DocumentContentType.Region) {
            startPos.add(arrs[0]);
            return parent.content;
        }

        if (parent instanceof DocumentContent && parent.isRegionContent()) {
            startPos.add(arrs[0]);
            return parent;
        }

        return;
    }

    private getRegionInfo1(): IRegionParam {
        let pos = this.curPos.contentPos;
        let elem = this.content[pos];
        const type = elem.getType();
        const actParam: IRegionParam = {
            indexs: [pos],
        };
        if (type === DocumentContentType.Table) {
            return;
        } else if (type === DocumentContentType.Region) {
            // return; // 暂时不能嵌套

            const para = this.getCurrentParagraph() as Paragraph;
            // 表格内的段落不允许插入
            const region = para.getRegion(false);
            if (!region || region.getContent().length === 1) {
                return;
            } else if (region?.isShowTitle3() && region.getContent()[0] === para) {
                return ;
            }

            // if (!this.getRegionManager()
            //     .isValidDepth(region.getName())) {
            //     return;
            // }
            pos = para.index;
            elem = para;
            actParam.parent = region;
            actParam.indexs = [pos];
        }

        const actFlag = this.getSelectedOverNewControl(elem, elem, actParam);
        if (actFlag === false) {
            return;
        }

        return actParam;
    }

    private getSelectedOverNewControl(startElem: DocumentContentElementBase, endElem: DocumentContentElementBase,
                                      param: IRegionParam): boolean {
        const posIndex = this.getTopElementPos(startElem);
        const startPos = new ParagraphContentPos();
        startPos.clear();
        startElem.getStartPos(startPos);
        startElem.getParentPos(startPos);
        startPos.splice(0, 0, [posIndex]);
        const endPos = new ParagraphContentPos();
        endPos.clear();
        endElem.getEndPos(true, endPos);
        endElem.getParentPos(endPos);
        endPos.splice(0, 0, [posIndex]);
        if (this.isOverNewControl(startPos, endPos, param) === false) {
            return false;
        }
        return true;
    }

    private concatParagraphs(pos: number, bUseConcatStyle: boolean): boolean {
        if ( this.content.length - 1 > pos && this.content[pos].isParagraph() && this.content[pos + 1].isParagraph()) {
            this.content[pos].concat(this.content[pos + 1], bUseConcatStyle);
            this.contentRemove(pos + 1, 1);
            // this.content[pos]
            return true;
        }

        return false;
    }
}
