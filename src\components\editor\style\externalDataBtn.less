@import './global.less';
.hz-editor-container {
    .external-data-btn {
        display: inline-block;
        // line-height: 1;
        white-space: nowrap;
        cursor: pointer;
        background: #fff;
        border: 0px;
        // color: #606266;
        -webkit-appearance: none;
        text-align: center;
        box-sizing: border-box;
        outline: none;
        // margin-left: 5px;
        // transition: .1s;
        // font-weight: 500;
        // // font-family: @fontFamily;
        // -moz-user-select: none;
        // -webkit-user-select: none;
        // -ms-user-select: none;
        // padding: 6px 8px;
        // border-radius: 2px;

        // left: 233px;
        // top: 434px;

        /* 14-Body/regular */

        font-family: 'PingFang SC';
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        /* identical to box height, or 143% */


        /* 蓝色 - Blue-VI【废弃】/Primary */

        color: #0040ff;
        border-color: #FFF;
    }

    
    .input-content {
        display: inline-block;
        width: 180px;
        margin-left: 2px;
        text-align: left;
        // line-height: 28px;
        margin-bottom: 8px;
    }
}