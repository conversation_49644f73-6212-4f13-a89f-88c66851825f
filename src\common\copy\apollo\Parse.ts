import {VNode} from '../ParseNode';
import {SaveFileType} from '../DataType';
import {ImagePro, NodeType, NodeStyle, StructurePro, TablePro, ColPro} from './NodeType';
import { decodeTag } from '../DataConver';
import { unescapeXML } from '@/utils/xml';


export default class Apollo {
    private parseStyle: NodeStyle;
    private regAttr: RegExp = new RegExp(`<(w:(\\w+))\\s*(w:(\\w+)="([^"]+)"\\s*)*?\\s*><\\/\\1>`, 'ig');
    private region: VNode;
    private portion: VNode;
    private para: VNode;
    private root: VNode;
    private table: VNode;
    private row: VNode;
    private col: VNode;
    private _disabled: boolean;

    constructor() {
        this._disabled = false;
    }

    public parse(str: string): VNode {
        let html = decodeURIComponent(atob(str.replace(SaveFileType.Apollo, '')));
        if (!html) {
            return;
        }
        html = /<(w:body)>([\S\s]*?)<\/\1>/.exec(html)[2];
        const root = new VNode();
        root.children = [];
        root.type = 'root';
        root.tagName = 'root';
        this.root = root;
        this.filter(html, root);
        return root;
    }

    private getParseStyle(): NodeStyle {
        if (!this.parseStyle) {
            this.parseStyle = new NodeStyle();
        }

        return this.parseStyle;
    }

    private setpPrStyle(node: VNode): string {
        const attrs = node.attrs;
        if (!attrs) {
            return;
        }

        let style = '';
        const parseStyle = this.getParseStyle();
        // tslint:disable-next-line: forin
        for (const key in attrs) {
            const attr = parseStyle[key];
            if (parseStyle[key] === undefined) {
                style += key + ':' + attrs[key].value + ';';
                continue;
            }
            style +=  (typeof attr === 'string' ? attr : parseStyle[key](attrs[key].value,
                'w:' + attrs[key].key.toLowerCase()));
        }
        return style;
    }

    private getAttrs(str: string): any {
        if (!str) {
            return {};
        }

        const attrs = {};
        const reg = /([\w\-:]+)="([^"]+)"/g;
        let matchs = reg.exec(str);
        while (matchs) {
            attrs[matchs[1]] = matchs[2];
            matchs = reg.exec(str);
        }
        return attrs;
    }

    private getAttrs2(str: string): any {
        const attrs = {};
        if (!str) {
            return attrs;
        }

       // const reg = /<([\w:]+)>([^\1]+?)<\/\1>/g;
       const reg = /<([\w:]+)>([\s\S]*?)<\/\1>/g;

        let match: RegExpExecArray = reg.exec(str);
        while (match) {
            attrs[match[1]] = match[2];
            match = reg.exec(str);
        }
        return attrs;
    }

    private getAttrs3(str: string): any {
        const attrs = {};
        if (!str) {
            return attrs;
        }

        //const reg = /<(\w+)>([^\1]+?)<\/\1>/g;
        const reg = /<(\w+)>([\s\S]*?)<\/\1>/g;

        let match: RegExpExecArray = reg.exec(str);
        while (match) {
            attrs[match[1]] = match[2];
            match = reg.exec(str);
        }
        return attrs;
    }

    /**
     * 获取grid 的信息
     * @param str grid信息字符串
     */
    private getAttrs4(str: string): any {
        if (!str) {
            return {};
        }
        const reg = /<([A-Za-z0-9]+)([\s\S]+?)><\/\1>/g;
        let match: RegExpExecArray = reg.exec(str);
        const attrs = {};
        while (match) {
            attrs[match[1]] = this.getAttrs(match[2]);
            match = reg.exec(str);
        }
        return attrs;
    }

    private getHiddenProp(str: string): string {
        if (!str) {
            return;
        }
        const reg = /<w:bHidden bHidden="(\w+)"><\/w:bHidden>/;
        const match = reg.exec(str);
        if (match) {
            return match[1];
        }
        return;
    }

    private getTableBorder(str: string): any {
        if (!str) {
            return {};
        }
        const reg = /<([A-Za-z0-9]+)([\s\S]+?)><\/\1>/g;
        let match: RegExpExecArray = reg.exec(str);
        const attrs = {};
        while (match) {
            const name = match[1];
            const val = this.getAttrs(match[2]);
            if (!attrs[name]) {
                attrs[name] = [val];
            } else {
                attrs[name].push(val);
            }
            match = reg.exec(str);
        }
        return attrs;
    }

    private getPro(str: string): any {
        if (!str) {
            return {};
        }

        const attrs = {};
        const reg = this.regAttr;
        let matchs = reg.exec(str);
        while (matchs) {
            attrs[matchs[2].toLowerCase()] = {
                key: matchs[4],
                value: matchs[5],
            };
            matchs = reg.exec(str);
        }
        return attrs;
    }

    private tblPro(str: string): void {
        const colStr = [];
        const reg = new RegExp(`<(${NodeType.GridCol}|${ColPro.Border})>([\\s\\S]+?)<\\/\\1>`, 'g');
        str = str.replace(reg, (res, a1, a2) => {
            colStr.push(a2);
            return '';
        });

        const attrs = this.getAttrs(str);
        const obj = this.getAttrs3(str);
        const col = this.getAttrs4(colStr[0]);
        const border = this.getTableBorder(colStr[1]);
        this.table.attrs = {...attrs, ...obj, ...col, ...border};
    }

    private rowPro(str: string): void {
        const attrs = this.getAttrs(str);
        const obj = this.getAttrs3(str);
        this.row.attrs = {...attrs, ...obj};
    }

    private colPro(str: string): void {
        let borderStr = '';
        const reg = new RegExp(`<(${ColPro.Border})>([\\s\\S]+?)<\\/\\1>`, 'g');
        str = str.replace(reg, (res, a1, a2) => {
            borderStr += a2;
            return '';
        });
        const attrs = this.getAttrs(str);
        const obj = this.getAttrs3(str);
        const border = this.getTableBorder(borderStr);
        this.col.attrs = {...attrs, ...obj, ...border};
    }

    private paraPro(str: string): void {
        const attrs = this.getPro(str);
        if (Object.keys(attrs).length === 0) {
            return;
        }

        this.para.attrs = attrs;
        const style = this.setpPrStyle(this.para);
        this.para.attrs = {style, bHidden: this.getHiddenProp(str)};
    }

    private porPro(str: string): void {
        const attrs = this.getPro(str);
        if (Object.keys(attrs).length === 0) {
            return;
        }
        this.portion.attrs = attrs;
        const style = this.setpPrStyle(this.portion);
        this.portion.attrs = {style, bHidden: this.getHiddenProp(str)};
    }

    private createBaseNode(str: string): void {
        if (!str || this._disabled === true) {
            return;
        }
        const text = str || '';
        const node = new VNode();
        node.type = 'text';
        node.tagName = 'text';
        node.content = decodeTag(text);
        node.parentNode = this.portion;
        this.portion.children.push(node);
    }

    private createParaNode(name: string, root: VNode): void {
        const curNode = root || this.root;
        const para = new VNode();
        para.tagName = 'para';
        para.type = 'element';
        para.isBlock = true;
        para.children = [];
        para.parentNode = curNode;
        curNode.children.push(para);
        this.para = para;
    }

    private createPorNode(name: string): void {
        const node = new VNode();
        node.type = 'element';
        node.tagName = name;
        node.children = [];
        node.parentNode = this.para;
        this.portion = node;
        this.para.children.push(node);
    }

    private createImageNode(str: string): void {
        const attrs = this.getAttrs(str);
        if (!attrs[ImagePro.Src]) {
            return;
        }
        const node = new VNode();
        node.type = 'element';
        node.tagName = 'img';
        node.parentNode = this.portion;
        node.attrs = attrs;
        this.portion.children.push(node);
    }

    private createEquationNode(str: string): void {
        const attrs = this.getAttrs(str);
        if (!attrs[ImagePro.Src]) {
            return;
        }
        Object.assign(attrs, this.getAttrs2(str));

        const node = new VNode();
        node.type = 'element';
        node.tagName = 'img';
        node.parentNode = this.portion;
        node.attrs = attrs;
        this.portion.children.push(node);
    }

    private createRegionPr(str: string, root: VNode): void {
        const attrs = this.getAttrs3(str);
        if (!attrs) {
            return;
        }

        root.attrs = attrs;
    }

    private createStruct(str: string, root: VNode, name: string): void {
        const attrs = this.getAttrs(str);
        if (!attrs[StructurePro.Name]) {
            return;
        }

        Object.assign(attrs, this.getAttrs2(str));

        const node = new VNode();
        node.type = 'attribute';
        node.tagName = 'struct';
        node.parentNode = this.portion;
        node.attrs = attrs;
        if (name === NodeType.SdtEnd) {
            attrs.bStart = false;
            this._disabled = false;
        } else {
            attrs.bStart = true;
            // if (attrs[StructurePro.IsPlaceholder] === '1') {
            //     this._disabled = true;
            // }
        }
        this.portion.children.push(node);
    }

    private createRegion(parentNode: VNode): void {
        parentNode = parentNode || this.root;
        const node = new VNode();
        node.tagName = 'region';
        node.type = 'element';
        node.isBlock = true;
        node.children = [];
        node.parentNode = parentNode;
        this.region = node;
        parentNode.children.push(node);
    }

    private createTable(parentNode: VNode): void {
        parentNode = parentNode || this.root;
        const node = new VNode();
        node.tagName = 'tbl';
        node.type = 'element';
        node.isBlock = true;
        node.children = [];
        node.parentNode = parentNode;
        this.table = node;
        parentNode.children.push(node);
    }

    private createRow(): void {
        const node = new VNode();
        node.tagName = 'row';
        node.type = 'element';
        node.children = [];
        node.parentNode = this.table;
        this.row = node;
        this.table.children.push(node);
    }

    private createCol(): void {
        const node = new VNode();
        node.tagName = 'col';
        node.type = 'element';
        node.children = [];
        node.parentNode = this.row;
        this.col = node;
        this.row.children.push(node);
    }


    private createButton(str: string): void {
        const attrs = this.getAttrs(str);
        if (!attrs[StructurePro.Name]) {
            return;
        }
        const reg = /<[^>]*?>([\s\S]*?)<\/[^>]*?>/i;
        const match = reg.exec(str);
        if (match) {
            attrs.content = unescapeXML(match[1]);
        }
        const node = new VNode();
        node.type = 'element';
        node.tagName = 'button';
        node.parentNode = this.portion;
        node.attrs = attrs;
        this.portion.children.push(node);
    }


    private filterTable(str: string, root: VNode): void {
        const reg = /<(w:(\w+))([^>]*?)>([\s\S]*?)<\/\1>/gi;
        let matchs = reg.exec(str);
        while (matchs) {
            switch (matchs[2]) {
                case NodeType.Row:
                    this.createRow();
                    this.filterTable(matchs[4], root);
                    break;
                case NodeType.Col:
                    this.createCol();
                    this.filterTable(matchs[4], root);
                    break;
                case NodeType.TblPr:
                    this.tblPro(matchs[4]);
                    break;
                case NodeType.RowPr:
                    this.rowPro(matchs[4]);
                    break;
                case NodeType.ColPr:
                    this.colPro(matchs[4]);
                    break;
                case NodeType.P:
                    this.createParaNode(matchs[2], this.col);
                    this.filterP(matchs[4], this.col);
                    break;
                default:
                    break;
            }
            matchs = reg.exec(str);
        }
    }

    private filterP(str: string, root: VNode): void {
        const reg = /<(w:(\w+))([^>]*?)>([\s\S]*?)<\/\1>/gi;
        let matchs = reg.exec(str);
        while (matchs) {
            switch (matchs[2]) {
                case NodeType.PPr:
                    this.paraPro(matchs[4]);
                    break;
                case NodeType.R:
                    this.createPorNode(matchs[2]);
                    this.filterP(matchs[4], root);
                    break;
                case NodeType.RPr:
                    this.porPro(matchs[4]);
                    break;
                case NodeType.T:
                    this.createBaseNode(matchs[4]);
                    break;
                case NodeType.Drawing:
                    this.createImageNode(matchs[0]);
                    break;

                case NodeType.Button:
                    this.createButton(matchs[0]);
                    break;

                case NodeType.OMediaMath:
                    this.createEquationNode(matchs[0]);
                    break;
                case NodeType.SdtStart:
                case NodeType.SdtEnd:
                    this.createStruct(matchs[0], root, matchs[2]);
                    break;
                default:
                    break;
            }
            matchs = reg.exec(str);
        }
    }

    private filterRegion(str: string, root: VNode): void {
        const reg = /<(w:(\w+))(\s+[^>]*?)?>([\s\S]*?)<\/\1\3>/gi;
        let matchs = reg.exec(str);
        const parent = this.region;
        while (matchs) {
            switch (matchs[2]) {
                case NodeType.RegionPr: {
                    this.createRegionPr(matchs[0], parent);
                    break;
                }
                case NodeType.Region:
                    this.createRegion(parent);
                    this.filterRegion(matchs[4], parent);
                    break;
                case NodeType.P:
                    this.createParaNode(matchs[2], parent);
                    this.filterP(matchs[4], parent);
                    break;
                case NodeType.Tbl:
                    this.createTable(parent);
                    this.filterTable(matchs[4], parent);
                    break;
                case NodeType.Region:
                    this.createRegion(parent);
                    this.filterRegion(matchs[4], parent);
                    break;
                default:
                    break;
            }
            matchs = reg.exec(str);
        }
    }

    private filter(str: string, root?: VNode): void {
        const reg = /<(w:(\w+))(\s+[^>]*?)?>([\s\S]*?)<\/\1\3>/gi;
        let matchs = reg.exec(str);
        while (matchs) {
            switch (matchs[2]) {
                case NodeType.Region:
                    this.createRegion(root);
                    this.filterRegion(matchs[4], root);
                    break;
                case NodeType.P:
                    this.createParaNode(matchs[2], root);
                    this.filterP(matchs[4], root);
                    break;
                case NodeType.Tbl:
                    this.createTable(root);
                    this.filterTable(matchs[4], root);
                    break;
                default:
                    break;
            }
            matchs = reg.exec(str);
        }
    }

}
