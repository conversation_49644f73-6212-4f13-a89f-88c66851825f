import { unescapeX<PERSON> } from '@/utils/xml';
import { ALIGN_TYPE, CodeValueItem, DataType, FILE_HEADER_VERSION, FILE_HEADER_VERSION2, ICustomProps, INewControlProperty, rtNode } from '../commonDefines';
import { parseFromString } from '../commonMethods';
import { IReadStruct, StructExportPrintType, readStructXmlName, IStructPropInfo } from './common';

export class ReadStruct implements IReadStruct {
    private _props: INewControlProperty;
    private _name: string;
    private _text: string;
    private _version: number;
    private _node: any;
    constructor(props: INewControlProperty) {
        this._props = props;
    }

    public readProp(text: string, version?: number, node?: any): any {
        const name = node.tagName;
        const item: IStructPropInfo = readStructXmlName[name];
        if (item === undefined) {
            // tslint:disable-next-line:no-console
            console.warn('当前属性未定义：' + name);
            return;
        }

        this._version = version;
        this._text = text;
        this._name = item.propName || name;

        switch (item.type) {
            case StructExportPrintType.Boolean: {
                this._readBoolean();
                break;
            }
            case StructExportPrintType.Number: {
                this._readNumber();
                break;
            }
            case StructExportPrintType.List: {
                this._node = node;
                this._readList();
                this._node = null;
                break;
            }
            case StructExportPrintType.Custom: {
                this._node = node;
                this._readCustormProps();
                this._node = null;
                break;
            }
            case StructExportPrintType.Event: {
                if (this._props._bRetainCascade === false) {
                    break;
                }
                this._readEvent();
                break;
            }
            case StructExportPrintType.Align: {
                this._readAlign();
                break;
            }
            case StructExportPrintType.CustomDate: {
                this._readCustomDate();
                break;
            }
            case StructExportPrintType.County: {
                this._readCountry();
                break;
            }
            default: {
                this._readString();
            }
        }
    }

    private _readBoolean(): void {
        if (!this._text) {
            return;
        }
        this._props[this._name] = this._text === '1';
    }

    private _readString(): void {
        this._props[this._name] = safeDecodeURIComponent(this._text || '', this._version);
    }

    private _readNumber(): void {
        if (!this._text) {
            return;
        }
        this._props[this._name] = +this._text;
    }

    private _readList(): void {
        if (!this._text) {
            return;
        }
        const runChild = this._node;
        const listItems = runChild.children;
        if (listItems.length > 0) {
            const listItemsArr = [];
            for (const listItem of listItems) {
                if (typeof listItem === 'object') {
                    let nameNode: rtNode = null;
                    let valueNode: rtNode = null;
                    let selectNode: rtNode = null;
                    for (const listItemNode of listItem.children) {
                        if (typeof listItemNode === 'object') {
                            if (listItemNode.tagName === 'name') {
                                nameNode = listItemNode;
                            } else if (listItemNode.tagName === 'value') {
                                valueNode = listItemNode;
                            } else if (listItemNode.tagName === 'select') {
                                selectNode = listItemNode;
                            }
                        }
                    }

                    if (null == nameNode || 0 === nameNode.children.length ) {
                        continue;
                    }

                    listItemsArr.push(new CodeValueItem(
                        safeDecodeURIComponent(nameNode.children[0] as string, this._version),
                        safeDecodeURIComponent(valueNode.children[0] as string, this._version),
                        (selectNode.children[0] as string) === '1' ? true : false)
                    );

                }
            }

            this._props[this._name] = listItemsArr;
        }
    }

    private _readCustormProps(): void {
        if (!this._text) {
            return;
        }
        const customProperties = this._node.children;
        if (customProperties.length > 0) {
            const customPropertyArr = tReadCustomProperties(customProperties, this._version);
            this._props[this._name] = customPropertyArr;
        }
    }

    private _readEvent(): void {
        if (!this._text) {
            return;
        }
        try {
            this._props[this._name] = JSON.parse(
                this._text
                    .replace(/&amp;/g, '&')
                    .replace(/&lt;/g, '<')
                    .replace(/&gt;/g, '>')
                    .replace(/&quot;/g, '"')
                    .replace(/&apos;/g, '\'') as string
            );

        } catch (error) {
            //
        }
    }

    private _readAlign(): void {
        const align = ALIGN_TYPE[this._text];
        if (align === undefined) {
            return;
        }
        this._props[this._name] = align;
    }

    private _readCustomDate(): void {
        if (!this._text) {
            return;
        }
        let nodeValue = this._text;
        if (nodeValue === '{}') {
            return;
        }
        // const domParser = new DOMParser();
        // // handle unescaped string
        // const doc = domParser.parseFromString(nodeValue, 'text/html');
        // if (doc != null) {
        //     nodeValue = doc.documentElement.textContent;
        // }

        // const div = IFRAME_MANAGER.getReaderDiv();
        // div.innerHTML = this._text;

        this._props[this._name] = JSON.parse(parseFromString(this._text));
    }

    private _readCountry(): void {
        if (!this._text) {
            return;
        }
        try {
            this._props[this._name] = JSON.parse(safeDecodeURIComponent(this._text, this._version));
        } catch (error) {
            //
        }
    }
}

function safeDecodeURIComponent(value: string, documentVersion?: number): string {
    if (!value || typeof value !== 'string') {
        return '';
    }
    let val = value;

    if (documentVersion === FILE_HEADER_VERSION || documentVersion === FILE_HEADER_VERSION2) {
        val = unescapeXML(val);
    } else {
        try {
            val = decodeURIComponent(val);
        } catch (error) {
            val = value;
        }
    }

    return val;
}

function tReadCustomProperties(customProperties: any[], documentVersion?: number): ICustomProps[] {
    const customPropertyArr = [];
    for (const customPropertyItem of customProperties) {
        const properties: ICustomProps = {
            type: DataType.String,
            name: undefined,
            value: '',
        };

        if ( typeof customPropertyItem === 'object') {
            const name = customPropertyItem.tagName;
            if (name) { // must be string, ok
                properties.name = safeDecodeURIComponent(name, documentVersion);
            }

            const value = customPropertyItem.children[0] ? customPropertyItem.children[0] : null;
            if (value && typeof value === 'string') {
                const val = safeDecodeURIComponent(value, documentVersion);
                properties.value = val;
            }

            // tslint:disable-next-line: max-line-length
            const type = customPropertyItem.attributes['type'] ? +customPropertyItem.attributes['type'] : null;
            if (type) {
                properties.type = type;
                if (type === DataType.String && properties.value) {
                    properties.value = properties.value.replace(/&quot;/g, '"');
                }
            }

            customPropertyArr.push(properties);
        }

    }

    return customPropertyArr;
}
