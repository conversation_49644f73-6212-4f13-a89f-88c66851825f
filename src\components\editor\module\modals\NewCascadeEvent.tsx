import * as React from 'react';
import '../../style/cascade.less';
import Dialog from '../../ui/Dialog';
import {CascadeTriggerType, ICascadeEvent, EventType, CodeAndValue, MixFormulaParser} from '../../../../common/commonDefines';
import {message} from '../../../../common/Message';
import Input from '../../ui/Input';
import Select from '../../ui/select/Select';
import Button from '../../ui/Button';
import { IFRAME_MANAGER } from '../../../../common/IframeManager';

interface IDialogProps {
    documentCore: any;
    visible: boolean;
    name?: string;
    properties: ICascadeEvent;
    controlName: string;
    onChange: (value: any, name: string) => void;
    close: (name: string | number, bRefresh?: boolean) => void;
    id: string;
    type?: EventType;
    disable?: boolean;
    hasMix?: boolean; // 是否含有混合运算
}

interface IState {
    bRefresh: boolean;
}

export default class CascadeEventBtn extends React.PureComponent<IDialogProps, IState> {
    private visible: boolean;
    constructor(props: IDialogProps) {
        super(props);
        this.state = {
            bRefresh: false,
        };
    }

    public render(): any {
        const className = (this.props.disable ? 'cascade-prop-btn disable' : 'cascade-prop-btn');
        return (
            <React.Fragment>
                <div className={className} onClick={this.onClick}>
                    <i>+</i>
                    <span>添加{this.props.hasMix ? '计算级联' : '求和级联'}</span>
                </div>
                {this.renderDialog()}
            </React.Fragment>
        );
    }

    private onClick = (e: any): void => {
        if ( this.props.disable ) {
            return;
        }

        this.visible = true;
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private onClose = (name: string, bRefresh: boolean): void => {
        this.visible = false;
        if (bRefresh) {
            this.props.close(this.props.id, true);
        } else {
            // this.setState({bRefresh: !this.state.bRefresh});
        }
    }

    private renderDialog(): any {
        if (this.visible !== true) {
            return null;
        }
        const props = this.props;
        return (
            <CascadeDialog
                visible={this.visible}
                id={props.id}
                name={props.name}
                controlName={props.controlName}
                onChange={props.onChange}
                close={this.onClose}
                properties={props.properties}
                documentCore={props.documentCore}
                type={this.props.type}
                hasMix={this.props.hasMix}
            />
        );
    }
}

interface IPropKey {
    key: string;
    value: any;
    disabled?: boolean;
}

class CascadeDialog extends React.Component<IDialogProps, IState> {
    private visible: boolean;
    private event: any;
    private actions: any[];
    private logicTexts: any[];

    constructor(props: any) {
        super(props);
        this.state = {
            bRefresh: false,
        };

        this.actions = [{key: 'SelectedChanged', value: CascadeTriggerType.SelectChanged},
            {key: 'CheckChanged', value: CascadeTriggerType.CheckChanged},
            {key: 'TextChanged', value: CascadeTriggerType.TextChanged},
        ];
        this.logicTexts = [{key: '属性值相加', value: CodeAndValue.Value}, {key: '文本内容相加', value: CodeAndValue.Code}];

        this.visible = this.props.visible;
        this.event = {};
    }

    public render(): any {
        const logicTexts = [...this.logicTexts];
        let isExpression = false;
        if (this.props.hasMix) {
            logicTexts.push({key: '表达式计算（文本内容）', value: CodeAndValue.Expression})
            isExpression = this.event.key === CodeAndValue.Expression;
        }
        return (
            <Dialog
                visible={this.visible}
                width={610}
                top='25%'
                open={this.open}
                title={this.props.hasMix ? '计算级联' : '求和级联'}
                footer={this.renderFooter()}
                id={this.props.id}
            >
                <div className='cascade-event'>
                    <div className='editor-line'>
                        <span className='w-150'>触发源(有多个源，以,隔开)</span>
                        <div className='right-auto'>
                            <Input
                                value={this.event.target}
                                name='target'
                                onChange={this.onChange}
                                disabled={isExpression}
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <span className='w-150'>逻辑</span>
                        <div className='right-auto'>
                            <Select
                                data={logicTexts}
                                value={this.event.key}
                                name='key'
                                onChange={this.onChange}
                            />
                        </div>
                    </div>
                    {
                        isExpression &&
                        <div className='editor-line'>
                            <span className='w-150'>
                                计算表达式
                            </span>
                            <div className='right-auto' style={{position: 'relative'}}>
                                <Input
                                    value={this.event.expression}
                                    name='expression'
                                    onChange={this.onChange}
                                    placeholder='请录入表达式计算公式'
                                />
                                <div className="helptip">?</div>
                                <div className="message">
                                    <span>元素名引用:</span><span>"numbox1"/"[numbox1]"/"[numbox-one]"</span>
                                    <span>表达式示例:</span><span>10*numbox1/(1+[numbox-one]/2)</span>
                                </div>
                            </div>
                        </div>
                    }
                    <div className='editor-line editor-multi-line'>
                        <span className='w-150 align-top'>触发动作：(所有的源采用同一个动作，请写*)</span>
                        <div className='right-auto'>
                            <ul>
                                {this.renderList(isExpression)}
                            </ul>
                        </div>
                    </div>
                </div>
            </Dialog>
        );
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        this.visible = nextProps.visible;
    }

    private renderList(isExpression: boolean): any {
        if (!this.event.event) {
            return;
        }

        if (isExpression) {
            // 数值框的计算级联
            if (!this.event.event.length) {
                return;
            }
            return (
                <li>
                    <span>
                        <Input
                            value='*'
                            name='source'
                            disabled={true}
                        />
                    </span>
                    <div className='event-select'>
                        <Select
                            data={this.actions}
                            value={CascadeTriggerType.TextChanged}
                            name='triggerType'
                            disabled={true}
                        />
                    </div>
                    <div className='event-btn'>
                        {this.renderIcon(0)}
                    </div>
                </li>
            );
        }

        return this.event.event.map((event, index) => {
            return (
                <li key={index}>
                    <span>
                        <Input
                            value={event.source}
                            name='source'
                            onChange={this.listChange.bind(this, event)}
                        />
                    </span>
                    <div className='event-select'>
                        <Select
                            data={this.actions}
                            value={event.triggerType}
                            name='triggerType'
                            onChange={this.listChange.bind(this, event)}
                        />
                    </div>
                    <div className='event-btn'>
                        <i onClick={this.addData.bind(this, index)}>+</i>
                        {this.renderIcon(index)}
                    </div>
                </li>
            );
        });
    }

    private renderIcon(index: number): any {
        // if (index === 0) {
        //     return;
        // }

        return (<i onClick={this.deleteData.bind(this, index)}>-</i>);
    }

    private renderFooter(): any {
        return (
            <span>
                <Button onClick={this.close}>取消</Button>
                <Button type='primary' onClick={this.confirm}>确认</Button>
            </span>
        );
    }

    private listChange = (event: any, value: any, name: string): void => {
        event[name] = value;
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private onChange = (value: any, name: string): void => {
        this.event[name] = value;
        if (name === 'key' && this.props.hasMix) {
            this.setState({});
        }
    }

    private addData = (index?: number): void => {
        const data = this.event.event[index];
        if (data) {
            if (data.source === '*') {
                return;
            }
        }
        this.event.event.splice(index + 1, 0, {triggerType: CascadeTriggerType.SelectChanged});
        this.setState({});
    }

    private deleteData = (index: number): void => {
        this.event.event.splice(index, 1);
        this.setState({});
    }

    private open = (): void => {
        const props = this.props;
        const options = props.properties;
        if (!options) {
            this.event = {
                action: props.type,
                key: 'value',
                event: [{triggerType: CascadeTriggerType.SelectChanged}],
            };
        } else {
            this.event = {
                target: options.target,
                action: options.action,
                key: options.key,
                expression: options.expression,
            };
            if (options.event && options.event.length > 0) {
                this.event.event = options.event.map((event) => {
                    return {...event};
                });
            } else {
                this.event.event = [{source: '*', triggerType: CascadeTriggerType.SelectChanged}];
            }
        }
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private validData(): boolean {
        const options = this.event;
        if (!options.target) {
            message.error(`触发源不能为空`);
            return false;
        }
        if (options.target.split(',').includes(this.props.controlName)) {
            message.error(`触发源不能包含自身`);
            return false;
        }

        const datas = this.event.event;
        if (datas.length === 0) {
            return true;
        }

        for (let index = datas.length - 1; index >= 0; index--) {
            const data = datas[index];
            if (!data.source) {
                message.error(`第${index + 1}行的触发动作不能为空，请重新填写`, {time: 10000});
                return false;
            }
        }

        return true;
    }

    /** 校验混合计算表达式 */
    private validateMixExpression(): boolean {
        const { expression } = this.event;
        if (!expression) {
            message.error('表达式不能为空');
            return false;
        }
        const { toInfixExpressions, validateInfixExpression } = MixFormulaParser;
        const { infixes, elemNames } = toInfixExpressions(expression);
        if (validateInfixExpression(infixes)) {
            if (elemNames.includes(this.props.controlName)) {
                message.error(`表达式中不能包含自身`);
                return false;
            }
            const errNames: string[] = this.props.documentCore.checkNewControlsType(elemNames);
            if (errNames.length) {
                message.error(`元素[${errNames.join(',')}]必须为数值框`);
                return false;
            }
            this.event.target = elemNames.join(',');
            return true;
        }
        message.error(`表达式"${expression}"格式不正确`);
        return false;
    }

    private close = (id?: any): void => {
        this.props.close(this.props.id, false);
        this.visible = false;

        this.setState({bRefresh: !this.state.bRefresh});
    }

    private confirm = (id?: any): void => {
        IFRAME_MANAGER.setDocId(this.props.documentCore.getCurrentId());
        
        if (this.props.hasMix && this.event.key === CodeAndValue.Expression) {
            if (this.event.event.length) {
                if (!this.validateMixExpression()) {
                    return;
                }
                this.event.event = [{source: '*', triggerType: CascadeTriggerType.TextChanged}];
            }
        } else {
            // 删除表达式
            delete this.event.expression;
            if (this.validData() === false) {
                return;
            }
    
            let event = this.event;
            if (event.event.length === 0) {
                event = null;
            } else {
                const res = this.props.documentCore.checkNewControlEvent(event);
                if (res) {
                    message.error(res, {time: 10000});
                    return;
                }
            }
        }
        // event = JSON.parse(JSON.stringify(this.event));
        this.props.onChange(this.event, this.props.name);
        this.event = null;
        this.props.close(id, true);
    }
}
