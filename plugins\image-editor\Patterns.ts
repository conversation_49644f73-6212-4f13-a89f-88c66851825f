import { fabric } from 'fabric';

const name1 = '疼痛消失';
const s1 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAQCAYAAABk1z2tAAAAt0lEQVRIS+2WwQ3CMAxFXweoxEacqx44wWyIEXrIDizQPWAGDshQR5blExVJDsnR+lJeYv/8DHzXEViAC3DfaifgCszAWks3bBt7yEMAp1J7mL/rLKBCJuAFTObmzDnyjRfReUBp6w2Q+tm02wMW01lAO3NjMJMKWVSngE0YIjKiAEZwVQwRGVYAH60Ywg26vBZJAJ+tGMIBfowogNEjXcUQBjCPnZqkJ8mvydSTZG8y9STZ+1V7AyJLxViRYrnhAAAAAElFTkSuQmCC';

const name2 = '位置觉减退或消失';
const s2 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAQCAYAAABk1z2tAAAAoUlEQVRIS+3UMQ4BQRhH8d8W6BU0TiAKN3EGx1DoJK7hCI4iGjeQUOiRoPkm2Wyj2OIj2en+85pXzLzKj5+q5rfEBi+ssWu4p/C64B3zkDpg0BBM4UWwhwfKfqOPZ0im8SK0xRSLENrjhFXsNF4Eb5jhHEITHDGMncaL4AWjxpu7Yhx3abz+SX4yOH8lmNK5bx3uOti2o10H23a062DbuH4Arp+gERrVvY0AAAAASUVORK5CYII='

const name3 = '深Ⅱ度';
const s3 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAQCAYAAABk1z2tAAAAt0lEQVRIS+2WwQ3CMAxFXweoxEacqx44wWyIEXrIDizQPWAGDshQR5blExVJDsnR+lJeYv/8DHzXEViAC3DfaifgCszAWks3bBt7yEMAp1J7mL/rLKBCJuAFTObmzDnyjRfReUBp6w2Q+tm02wMW01lAO3NjMJMKWVSngE0YIjKiAEZwVQwRGVYAH60Ywg26vBZJAJ+tGMIBfowogNEjXcUQBjCPnZqkJ8mvydSTZG8y9STZ+1V7AyJLxViRYrnhAAAAAElFTkSuQmCC';

const name4 = '震动觉减退或消失';
const s4 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAQCAYAAABk1z2tAAAAqElEQVRIS+2VQQqDQAxFn96nBfEc7ZXaXsl7FMTzdFE+nZQQ6kIXkwrjZsx8Bx4/Tn7H53kCD2Aq9RW4AWO23v0bUDTEAL2LevfulTNfl6vqHtDaKgDfbgNM0T2guajV/j2D8y5W1SPgCdDeEslKXV2PgCtceduHADwDPTAHnwbgVVqepsvBC3D/cTE0vDVu9E2abi1uSbI3OluSuIu3K4lakoTRtTmJ3tM+jIoXKiWCAAAAAElFTkSuQmCC';

const name5 = 'Ⅲ三度';
const s5 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAQCAYAAABk1z2tAAAAPElEQVRIS2NkGOSAEeq+/4PUnYyjDqQwZkZDkMIAZBgNwdEQpDQEKNU/mgZHQ5DSEKBU/9BJg5T6lGb6AcKeDhFCvv3MAAAAAElFTkSuQmCC';

const name6 = '深浅感觉全部消失';
const s6 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAQCAYAAABk1z2tAAAAU0lEQVRIS2NkGOSAEeq+eijdiMe9A6JmSDjw/2CO5SERgqAAHJD0RUx6Hw1BLKFEUmyNhuBoCCKFAElph5gcSowaUBocLajRQoqkmBgyuXjQVscAHFc+D8OmuCcAAAAASUVORK5CYII=';

const name7 = '浅感觉全部消失';
const s7 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAQCAYAAABk1z2tAAAATUlEQVRIS2NkGOSAEcl99QwMDA1Q3EjA3XRTO+pABgYGikJ7NARHQxBHbqYoXeEpITDMHU2Do2lwNA2ihsBoJsGWIigqkpCLmUHZ8AIAdeZwETRMynoAAAAASUVORK5CYII=';

const name8 = '触觉减退';
const s8 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAQCAYAAABk1z2tAAAAVUlEQVRIS2NkGOSAEYf7/qOJD5i6AbOY2IAZdSA0pMhOMrhCcNBkHZADyfYdsemIEnWjDmRgYKAoIw6JEBw0GQKbQygKfnpksFEH0qqgHjRl46CvSQCCxjARB9Kl9gAAAABJRU5ErkJggg==';

const name9 = 'Ⅰ度';
const s9 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAQCAYAAABk1z2tAAAAT0lEQVRIS2NkGOSAEeq+w1DaFod7B0x+yDhw0EY0LASHnAMHLM2h5wlcITjoHThoonzIpMFBE6XoUTdaDlKamIdMGkT36KBJk6PlIKVpEABzHygRUAi0LAAAAABJRU5ErkJggg==';

const name10 = '触觉过敏或异样';
const s10 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAQCAYAAABk1z2tAAAAfElEQVRIS+2WwQmAMAxFX8ElxFE8uIXDOIDDuIUHV3ELqQRaKD32kl9oTyn/8kjaRwLiJyS+BziAO9034ARW7zwDGtAFfEAEJmCvgF3yDGiNeoE5dczqpZq+S94NoPyI5T+JrGzKNygJOTxYiL/Jo91oRl7Uw4Oty4i8Zn6oULQRUwINZgAAAABJRU5ErkJggg==';

const name11 = 'Ⅱ度';
const s11 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAQCAYAAABk1z2tAAAAj0lEQVRIS83V0Q2AIAxF0ctKrmHU9dShXMM1TD9ICKFAAkj5LnBI4eHQxwGcwAo8mboduEbVOWVjEzixpYBmcCmgKVwMNIcLgSZxHmgW54HvqIjoEU3yipeZOVc6hJaDft7QEC7hpLM54HScdFYDmsBpP4kZXMtPUhtNzXVhi5sXiy59l/U8cAPuijz8ve4D3xx/eHgD5wgAAAAASUVORK5CYII=';

const name12 = '触觉消失';
const s12 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAQCAYAAABk1z2tAAAAn0lEQVRIS+3WMQ5BQRRA0fMbC1CKJaC0HhVLoFKoWAKV9SixBFFagEokb5pJFEjMSP5U83Kbm0zezTQqP034dbDCBM/7DkvcS/MkuMEAsxDa4oRFzMV4ErxijEsI9XFEN+ZiPAneMMoED+iFYDGeBNcYZk98xjwEi/F8SaaxGPsXS/JzngSrjc3fCLYd/LSzbQe/7Wjbweyz8XZHq8/MA3vPvhFDvzFAAAAAAElFTkSuQmCC';

const name13 = '痛觉过敏或异样';
const s13 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAQCAYAAABk1z2tAAAARklEQVRIS2NkGOSAEeq+/4PUnYxDxoGDNAAZGGAhOOpAckNgNATJDTmYvtEQHA1BSkOAUv1DJg2O1sVkRjW8sUCmftprAwCgcwQRheYSmgAAAABJRU5ErkJggg==';

const name14 = '痛觉减退';
const s14 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAQCAYAAABk1z2tAAAAdElEQVRIS2NkGOSAEYf77BkYGNoYGBisB1oemwNBjlvFwMAQxsDAcBCLA+kqj+5AulpOjOeRHTjoHAfyAMyBg9JxyA48ysDAUIUjzYHUDZg8rlw8aAqfIevA0XKQ2HJ2tBxEym1kFWWj5SA0BMkuRwd9MQMApqBuEbhug8cAAAAASUVORK5CYII=';

const sources = [s1,s2,s3,s4,s5,s6,s7,s8,s9,s10,s11,s12,s13, s14];
const names = [name1, name2, name3, name4, name5, name6, name7, name8, name9, name10, name11, name12, name13, name14];

export interface IPattern {
  name: string;
  pattern: fabric.Pattern | string;
  source?: string;
}


export function getPatterns(): IPattern[] {
  const patterns = sources.map((source, index) => {
    const pattern = new fabric.Pattern({source, repeat: 'repeat'});
    const name = names[index];
    return {
      pattern, name, source
    };
  });
  return [
    {
      name: '无',
      pattern: ''
    },
    ...patterns
  ];
}
