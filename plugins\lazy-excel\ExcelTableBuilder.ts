import ExcelJS from 'exceljs';
import { ISerialTableObj } from '../serialize/serialInterface';
import ExcelTable from './ExcelTable';

export default class ExcelTableBuilder {
    private workbook: ExcelJS.Workbook;

    constructor(private tables: ISerialTableObj[]) {
        this.workbook = new ExcelJS.Workbook();
        this.workbook.calcProperties.fullCalcOnLoad = true;
    }

    /** 生成文档对象 */
    public generate(): ExcelTableBuilder {
        for (const table of this.tables) {
            const sheet = this.workbook.addWorksheet(table.name);
            new ExcelTable(table).buildTo(sheet);
        }
        return this;
    }

    /**  将文档转换为Blob */
    public async toBlob(): Promise<Blob> {
        const buf = await this.workbook.xlsx.writeBuffer();
        return new Blob([buf], {type: 'Application/vnd.ms-excel.sheet.macroEnabled.12'});
    }
}
