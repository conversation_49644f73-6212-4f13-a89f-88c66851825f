{"version": 3, "file": "index-dev.umd.js", "sources": ["../node_modules/@babel/runtime/helpers/esm/extends.js", "../node_modules/@babel/runtime/helpers/esm/inheritsLoose.js", "../node_modules/@babel/runtime/helpers/esm/assertThisInitialized.js", "../node_modules/memoize-one/dist/memoize-one.esm.js", "../src/timer.js", "../src/domHelpers.js", "../src/createGridComponent.js", "../src/VariableSizeGrid.js", "../src/createListComponent.js", "../src/VariableSizeList.js", "../src/FixedSizeGrid.js", "../src/FixedSizeList.js", "../node_modules/@babel/runtime/helpers/esm/objectWithoutPropertiesLoose.js", "../src/shallowDiffers.js", "../src/areEqual.js", "../src/shouldComponentUpdate.js"], "sourcesContent": ["export default function _extends() {\n  _extends = Object.assign || function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n\n    return target;\n  };\n\n  return _extends.apply(this, arguments);\n}", "export default function _inheritsLoose(subClass, superClass) {\n  subClass.prototype = Object.create(superClass.prototype);\n  subClass.prototype.constructor = subClass;\n  subClass.__proto__ = superClass;\n}", "export default function _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n\n  return self;\n}", "var shallowEqual = function shallowEqual(newValue, oldValue) {\n  return newValue === oldValue;\n};\n\nvar simpleIsEqual = function simpleIsEqual(newArgs, lastArgs) {\n  return newArgs.length === lastArgs.length && newArgs.every(function (newArg, index) {\n    return shallowEqual(newArg, lastArgs[index]);\n  });\n};\n\nfunction index (resultFn, isEqual) {\n  if (isEqual === void 0) {\n    isEqual = simpleIsEqual;\n  }\n\n  var lastThis;\n  var lastArgs = [];\n  var lastResult;\n  var calledOnce = false;\n\n  var result = function result() {\n    for (var _len = arguments.length, newArgs = new Array(_len), _key = 0; _key < _len; _key++) {\n      newArgs[_key] = arguments[_key];\n    }\n\n    if (calledOnce && lastThis === this && isEqual(newArgs, lastArgs)) {\n      return lastResult;\n    }\n\n    lastResult = resultFn.apply(this, newArgs);\n    calledOnce = true;\n    lastThis = this;\n    lastArgs = newArgs;\n    return lastResult;\n  };\n\n  return result;\n}\n\nexport default index;\n", "// @flow\n\n// Animation frame based implementation of setTimeout.\n// Inspired by <PERSON>, https://gist.github.com/joelambert/1002116#file-requesttimeout-js\n\nconst hasNativePerformanceNow =\n  typeof performance === 'object' && typeof performance.now === 'function';\n\nconst now = hasNativePerformanceNow\n  ? () => performance.now()\n  : () => Date.now();\n\nexport type TimeoutID = {|\n  id: AnimationFrameID,\n|};\n\nexport function cancelTimeout(timeoutID: TimeoutID) {\n  cancelAnimationFrame(timeoutID.id);\n}\n\nexport function requestTimeout(callback: Function, delay: number): TimeoutID {\n  const start = now();\n\n  function tick() {\n    if (now() - start >= delay) {\n      callback.call(null);\n    } else {\n      timeoutID.id = requestAnimationFrame(tick);\n    }\n  }\n\n  const timeoutID: TimeoutID = {\n    id: requestAnimationFrame(tick),\n  };\n\n  return timeoutID;\n}\n", "// @flow\n\nlet size: number = -1;\n\n// This utility copied from \"dom-helpers\" package.\nexport function getScrollbarSize(recalculate?: boolean = false): number {\n  if (size === -1 || recalculate) {\n    const div = document.createElement('div');\n    const style = div.style;\n    style.width = '50px';\n    style.height = '50px';\n    style.overflow = 'scroll';\n\n    ((document.body: any): HTMLBodyElement).appendChild(div);\n\n    size = div.offsetWidth - div.clientWidth;\n\n    ((document.body: any): HTMLBodyElement).removeChild(div);\n  }\n\n  return size;\n}\n\nexport type RTLOffsetType =\n  | 'negative'\n  | 'positive-descending'\n  | 'positive-ascending';\n\nlet cachedRTLResult: RTLOffsetType | null = null;\n\n// TRICKY According to the spec, scrollLeft should be negative for RTL aligned elements.\n// Chrome does not seem to adhere; its scrollLeft values are positive (measured relative to the left).\n// <PERSON><PERSON>'s elastic bounce makes detecting this even more complicated wrt potential false positives.\n// The safest way to check this is to intentionally set a negative offset,\n// and then verify that the subsequent \"scroll\" event matches the negative offset.\n// If it does not match, then we can assume a non-standard RTL scroll implementation.\nexport function getRTLOffsetType(recalculate?: boolean = false): RTLOffsetType {\n  if (cachedRTLResult === null || recalculate) {\n    const outerDiv = document.createElement('div');\n    const outerStyle = outerDiv.style;\n    outerStyle.width = '50px';\n    outerStyle.height = '50px';\n    outerStyle.overflow = 'scroll';\n    outerStyle.direction = 'rtl';\n\n    const innerDiv = document.createElement('div');\n    const innerStyle = innerDiv.style;\n    innerStyle.width = '100px';\n    innerStyle.height = '100px';\n\n    outerDiv.appendChild(innerDiv);\n\n    ((document.body: any): HTMLBodyElement).appendChild(outerDiv);\n\n    if (outerDiv.scrollLeft > 0) {\n      cachedRTLResult = 'positive-descending';\n    } else {\n      outerDiv.scrollLeft = 1;\n      if (outerDiv.scrollLeft === 0) {\n        cachedRTLResult = 'negative';\n      } else {\n        cachedRTLResult = 'positive-ascending';\n      }\n    }\n\n    ((document.body: any): HTMLBodyElement).removeChild(outerDiv);\n\n    return cachedRTLResult;\n  }\n\n  return cachedRTLResult;\n}\n", "// @flow\n\nimport memoizeOne from 'memoize-one';\nimport { createElement, PureComponent } from 'react';\nimport { cancelTimeout, requestTimeout } from './timer';\nimport { getScrollbarSize, getRTLOffsetType } from './domHelpers';\n\nimport type { TimeoutID } from './timer';\n\ntype Direction = 'ltr' | 'rtl';\nexport type ScrollToAlign = 'auto' | 'smart' | 'center' | 'start' | 'end';\n\ntype itemSize = number | ((index: number) => number);\n\ntype RenderComponentProps<T> = {|\n  columnIndex: number,\n  data: T,\n  isScrolling?: boolean,\n  rowIndex: number,\n  style: Object,\n|};\nexport type RenderComponent<T> = React$ComponentType<\n  $Shape<RenderComponentProps<T>>\n>;\n\ntype ScrollDirection = 'forward' | 'backward';\n\ntype OnItemsRenderedCallback = ({\n  overscanColumnStartIndex: number,\n  overscanColumnStopIndex: number,\n  overscanRowStartIndex: number,\n  overscanRowStopIndex: number,\n  visibleColumnStartIndex: number,\n  visibleColumnStopIndex: number,\n  visibleRowStartIndex: number,\n  visibleRowStopIndex: number,\n}) => void;\ntype OnScrollCallback = ({\n  horizontalScrollDirection: ScrollDirection,\n  scrollLeft: number,\n  scrollTop: number,\n  scrollUpdateWasRequested: boolean,\n  verticalScrollDirection: ScrollDirection,\n}) => void;\n\ntype ScrollEvent = SyntheticEvent<HTMLDivElement>;\ntype ItemStyleCache = { [key: string]: Object };\n\ntype OuterProps = {|\n  children: React$Node,\n  className: string | void,\n  onScroll: ScrollEvent => void,\n  style: {\n    [string]: mixed,\n  },\n|};\n\ntype InnerProps = {|\n  children: React$Node,\n  style: {\n    [string]: mixed,\n  },\n|};\n\nexport type Props<T> = {|\n  children: RenderComponent<T>,\n  className?: string,\n  columnCount: number,\n  columnWidth: itemSize,\n  direction: Direction,\n  height: number,\n  initialScrollLeft?: number,\n  initialScrollTop?: number,\n  innerRef?: any,\n  innerElementType?: string | React$AbstractComponent<InnerProps, any>,\n  innerTagName?: string, // deprecated\n  itemData: T,\n  itemKey?: (params: {|\n    columnIndex: number,\n    data: T,\n    rowIndex: number,\n  |}) => any,\n  onItemsRendered?: OnItemsRenderedCallback,\n  onScroll?: OnScrollCallback,\n  outerRef?: any,\n  outerElementType?: string | React$AbstractComponent<OuterProps, any>,\n  outerTagName?: string, // deprecated\n  overscanColumnCount?: number,\n  overscanColumnsCount?: number, // deprecated\n  overscanCount?: number, // deprecated\n  overscanRowCount?: number,\n  overscanRowsCount?: number, // deprecated\n  rowCount: number,\n  rowHeight: itemSize,\n  style?: Object,\n  useIsScrolling: boolean,\n  width: number,\n|};\n\ntype State = {|\n  instance: any,\n  isScrolling: boolean,\n  horizontalScrollDirection: ScrollDirection,\n  scrollLeft: number,\n  scrollTop: number,\n  scrollUpdateWasRequested: boolean,\n  verticalScrollDirection: ScrollDirection,\n|};\n\ntype getItemOffset = (\n  props: Props<any>,\n  index: number,\n  instanceProps: any\n) => number;\ntype getItemSize = (\n  props: Props<any>,\n  index: number,\n  instanceProps: any\n) => number;\ntype getEstimatedTotalSize = (props: Props<any>, instanceProps: any) => number;\ntype GetOffsetForItemAndAlignment = (\n  props: Props<any>,\n  index: number,\n  align: ScrollToAlign,\n  scrollOffset: number,\n  instanceProps: any,\n  scrollbarSize: number\n) => number;\ntype GetStartIndexForOffset = (\n  props: Props<any>,\n  offset: number,\n  instanceProps: any\n) => number;\ntype GetStopIndexForStartIndex = (\n  props: Props<any>,\n  startIndex: number,\n  scrollOffset: number,\n  instanceProps: any\n) => number;\ntype InitInstanceProps = (props: Props<any>, instance: any) => any;\ntype ValidateProps = (props: Props<any>) => void;\n\nconst IS_SCROLLING_DEBOUNCE_INTERVAL = 150;\n\nconst defaultItemKey = ({ columnIndex, data, rowIndex }) =>\n  `${rowIndex}:${columnIndex}`;\n\n// In DEV mode, this Set helps us only log a warning once per component instance.\n// This avoids spamming the console every time a render happens.\nlet devWarningsOverscanCount = null;\nlet devWarningsOverscanRowsColumnsCount = null;\nlet devWarningsTagName = null;\nif (process.env.NODE_ENV !== 'production') {\n  if (typeof window !== 'undefined' && typeof window.WeakSet !== 'undefined') {\n    devWarningsOverscanCount = new WeakSet();\n    devWarningsOverscanRowsColumnsCount = new WeakSet();\n    devWarningsTagName = new WeakSet();\n  }\n}\n\nexport default function createGridComponent({\n  getColumnOffset,\n  getColumnStartIndexForOffset,\n  getColumnStopIndexForStartIndex,\n  getColumnWidth,\n  getEstimatedTotalHeight,\n  getEstimatedTotalWidth,\n  getOffsetForColumnAndAlignment,\n  getOffsetForRowAndAlignment,\n  getRowHeight,\n  getRowOffset,\n  getRowStartIndexForOffset,\n  getRowStopIndexForStartIndex,\n  initInstanceProps,\n  shouldResetStyleCacheOnItemSizeChange,\n  validateProps,\n}: {|\n  getColumnOffset: getItemOffset,\n  getColumnStartIndexForOffset: GetStartIndexForOffset,\n  getColumnStopIndexForStartIndex: GetStopIndexForStartIndex,\n  getColumnWidth: getItemSize,\n  getEstimatedTotalHeight: getEstimatedTotalSize,\n  getEstimatedTotalWidth: getEstimatedTotalSize,\n  getOffsetForColumnAndAlignment: GetOffsetForItemAndAlignment,\n  getOffsetForRowAndAlignment: GetOffsetForItemAndAlignment,\n  getRowOffset: getItemOffset,\n  getRowHeight: getItemSize,\n  getRowStartIndexForOffset: GetStartIndexForOffset,\n  getRowStopIndexForStartIndex: GetStopIndexForStartIndex,\n  initInstanceProps: InitInstanceProps,\n  shouldResetStyleCacheOnItemSizeChange: boolean,\n  validateProps: ValidateProps,\n|}) {\n  return class Grid<T> extends PureComponent<Props<T>, State> {\n    _instanceProps: any = initInstanceProps(this.props, this);\n    _resetIsScrollingTimeoutId: TimeoutID | null = null;\n    _outerRef: ?HTMLDivElement;\n\n    static defaultProps = {\n      direction: 'ltr',\n      itemData: undefined,\n      useIsScrolling: false,\n    };\n\n    state: State = {\n      instance: this,\n      isScrolling: false,\n      horizontalScrollDirection: 'forward',\n      scrollLeft:\n        typeof this.props.initialScrollLeft === 'number'\n          ? this.props.initialScrollLeft\n          : 0,\n      scrollTop:\n        typeof this.props.initialScrollTop === 'number'\n          ? this.props.initialScrollTop\n          : 0,\n      scrollUpdateWasRequested: false,\n      verticalScrollDirection: 'forward',\n    };\n\n    // Always use explicit constructor for React components.\n    // It produces less code after transpilation. (#26)\n    // eslint-disable-next-line no-useless-constructor\n    constructor(props: Props<T>) {\n      super(props);\n    }\n\n    static getDerivedStateFromProps(\n      nextProps: Props<T>,\n      prevState: State\n    ): $Shape<State> | null {\n      validateSharedProps(nextProps, prevState);\n      validateProps(nextProps);\n      return null;\n    }\n\n    scrollTo({\n      scrollLeft,\n      scrollTop,\n    }: {\n      scrollLeft: number,\n      scrollTop: number,\n    }): void {\n      if (scrollLeft !== undefined) {\n        scrollLeft = Math.max(0, scrollLeft);\n      }\n      if (scrollTop !== undefined) {\n        scrollTop = Math.max(0, scrollTop);\n      }\n\n      this.setState(prevState => {\n        if (scrollLeft === undefined) {\n          scrollLeft = prevState.scrollLeft;\n        }\n        if (scrollTop === undefined) {\n          scrollTop = prevState.scrollTop;\n        }\n\n        if (\n          prevState.scrollLeft === scrollLeft &&\n          prevState.scrollTop === scrollTop\n        ) {\n          return null;\n        }\n\n        return {\n          horizontalScrollDirection:\n            prevState.scrollLeft < scrollLeft ? 'forward' : 'backward',\n          scrollLeft: scrollLeft,\n          scrollTop: scrollTop,\n          scrollUpdateWasRequested: true,\n          verticalScrollDirection:\n            prevState.scrollTop < scrollTop ? 'forward' : 'backward',\n        };\n      }, this._resetIsScrollingDebounced);\n    }\n\n    scrollToItem({\n      align = 'auto',\n      columnIndex,\n      rowIndex,\n    }: {\n      align: ScrollToAlign,\n      columnIndex?: number,\n      rowIndex?: number,\n    }): void {\n      const { columnCount, height, rowCount, width } = this.props;\n      const { scrollLeft, scrollTop } = this.state;\n      const scrollbarSize = getScrollbarSize();\n\n      if (columnIndex !== undefined) {\n        columnIndex = Math.max(0, Math.min(columnIndex, columnCount - 1));\n      }\n      if (rowIndex !== undefined) {\n        rowIndex = Math.max(0, Math.min(rowIndex, rowCount - 1));\n      }\n\n      const estimatedTotalHeight = getEstimatedTotalHeight(\n        this.props,\n        this._instanceProps\n      );\n      const estimatedTotalWidth = getEstimatedTotalWidth(\n        this.props,\n        this._instanceProps\n      );\n\n      // The scrollbar size should be considered when scrolling an item into view,\n      // to ensure it's fully visible.\n      // But we only need to account for its size when it's actually visible.\n      const horizontalScrollbarSize =\n        estimatedTotalWidth > width ? scrollbarSize : 0;\n      const verticalScrollbarSize =\n        estimatedTotalHeight > height ? scrollbarSize : 0;\n\n      this.scrollTo({\n        scrollLeft:\n          columnIndex !== undefined\n            ? getOffsetForColumnAndAlignment(\n                this.props,\n                columnIndex,\n                align,\n                scrollLeft,\n                this._instanceProps,\n                verticalScrollbarSize\n              )\n            : scrollLeft,\n        scrollTop:\n          rowIndex !== undefined\n            ? getOffsetForRowAndAlignment(\n                this.props,\n                rowIndex,\n                align,\n                scrollTop,\n                this._instanceProps,\n                horizontalScrollbarSize\n              )\n            : scrollTop,\n      });\n    }\n\n    componentDidMount() {\n      const { initialScrollLeft, initialScrollTop } = this.props;\n\n      if (this._outerRef != null) {\n        const outerRef = ((this._outerRef: any): HTMLElement);\n        if (typeof initialScrollLeft === 'number') {\n          outerRef.scrollLeft = initialScrollLeft;\n        }\n        if (typeof initialScrollTop === 'number') {\n          outerRef.scrollTop = initialScrollTop;\n        }\n      }\n\n      this._callPropsCallbacks();\n    }\n\n    componentDidUpdate() {\n      const { direction } = this.props;\n      const { scrollLeft, scrollTop, scrollUpdateWasRequested } = this.state;\n\n      if (scrollUpdateWasRequested && this._outerRef != null) {\n        // TRICKY According to the spec, scrollLeft should be negative for RTL aligned elements.\n        // This is not the case for all browsers though (e.g. Chrome reports values as positive, measured relative to the left).\n        // So we need to determine which browser behavior we're dealing with, and mimic it.\n        const outerRef = ((this._outerRef: any): HTMLElement);\n        if (direction === 'rtl') {\n          switch (getRTLOffsetType()) {\n            case 'negative':\n              outerRef.scrollLeft = -scrollLeft;\n              break;\n            case 'positive-ascending':\n              outerRef.scrollLeft = scrollLeft;\n              break;\n            default:\n              const { clientWidth, scrollWidth } = outerRef;\n              outerRef.scrollLeft = scrollWidth - clientWidth - scrollLeft;\n              break;\n          }\n        } else {\n          outerRef.scrollLeft = Math.max(0, scrollLeft);\n        }\n\n        outerRef.scrollTop = Math.max(0, scrollTop);\n      }\n\n      this._callPropsCallbacks();\n    }\n\n    componentWillUnmount() {\n      if (this._resetIsScrollingTimeoutId !== null) {\n        cancelTimeout(this._resetIsScrollingTimeoutId);\n      }\n    }\n\n    render() {\n      const {\n        children,\n        className,\n        columnCount,\n        direction,\n        height,\n        innerRef,\n        innerElementType,\n        innerTagName,\n        itemData,\n        itemKey = defaultItemKey,\n        outerElementType,\n        outerTagName,\n        rowCount,\n        style,\n        useIsScrolling,\n        width,\n      } = this.props;\n      const { isScrolling } = this.state;\n\n      const [\n        columnStartIndex,\n        columnStopIndex,\n      ] = this._getHorizontalRangeToRender();\n      const [rowStartIndex, rowStopIndex] = this._getVerticalRangeToRender();\n\n      const items = [];\n      if (columnCount > 0 && rowCount) {\n        for (\n          let rowIndex = rowStartIndex;\n          rowIndex <= rowStopIndex;\n          rowIndex++\n        ) {\n          for (\n            let columnIndex = columnStartIndex;\n            columnIndex <= columnStopIndex;\n            columnIndex++\n          ) {\n            items.push(\n              createElement(children, {\n                columnIndex,\n                data: itemData,\n                isScrolling: useIsScrolling ? isScrolling : undefined,\n                key: itemKey({ columnIndex, data: itemData, rowIndex }),\n                rowIndex,\n                style: this._getItemStyle(rowIndex, columnIndex),\n              })\n            );\n          }\n        }\n      }\n\n      // Read this value AFTER items have been created,\n      // So their actual sizes (if variable) are taken into consideration.\n      const estimatedTotalHeight = getEstimatedTotalHeight(\n        this.props,\n        this._instanceProps\n      );\n      const estimatedTotalWidth = getEstimatedTotalWidth(\n        this.props,\n        this._instanceProps\n      );\n\n      return createElement(\n        outerElementType || outerTagName || 'div',\n        {\n          className,\n          onScroll: this._onScroll,\n          ref: this._outerRefSetter,\n          style: {\n            position: 'relative',\n            height,\n            width,\n            overflow: 'auto',\n            WebkitOverflowScrolling: 'touch',\n            willChange: 'transform',\n            direction,\n            ...style,\n          },\n        },\n        createElement(innerElementType || innerTagName || 'div', {\n          children: items,\n          ref: innerRef,\n          style: {\n            height: estimatedTotalHeight,\n            pointerEvents: isScrolling ? 'none' : undefined,\n            width: estimatedTotalWidth,\n          },\n        })\n      );\n    }\n\n    _callOnItemsRendered: (\n      overscanColumnStartIndex: number,\n      overscanColumnStopIndex: number,\n      overscanRowStartIndex: number,\n      overscanRowStopIndex: number,\n      visibleColumnStartIndex: number,\n      visibleColumnStopIndex: number,\n      visibleRowStartIndex: number,\n      visibleRowStopIndex: number\n    ) => void;\n    _callOnItemsRendered = memoizeOne(\n      (\n        overscanColumnStartIndex: number,\n        overscanColumnStopIndex: number,\n        overscanRowStartIndex: number,\n        overscanRowStopIndex: number,\n        visibleColumnStartIndex: number,\n        visibleColumnStopIndex: number,\n        visibleRowStartIndex: number,\n        visibleRowStopIndex: number\n      ) =>\n        ((this.props.onItemsRendered: any): OnItemsRenderedCallback)({\n          overscanColumnStartIndex,\n          overscanColumnStopIndex,\n          overscanRowStartIndex,\n          overscanRowStopIndex,\n          visibleColumnStartIndex,\n          visibleColumnStopIndex,\n          visibleRowStartIndex,\n          visibleRowStopIndex,\n        })\n    );\n\n    _callOnScroll: (\n      scrollLeft: number,\n      scrollTop: number,\n      horizontalScrollDirection: ScrollDirection,\n      verticalScrollDirection: ScrollDirection,\n      scrollUpdateWasRequested: boolean\n    ) => void;\n    _callOnScroll = memoizeOne(\n      (\n        scrollLeft: number,\n        scrollTop: number,\n        horizontalScrollDirection: ScrollDirection,\n        verticalScrollDirection: ScrollDirection,\n        scrollUpdateWasRequested: boolean\n      ) =>\n        ((this.props.onScroll: any): OnScrollCallback)({\n          horizontalScrollDirection,\n          scrollLeft,\n          scrollTop,\n          verticalScrollDirection,\n          scrollUpdateWasRequested,\n        })\n    );\n\n    _callPropsCallbacks() {\n      const { columnCount, onItemsRendered, onScroll, rowCount } = this.props;\n\n      if (typeof onItemsRendered === 'function') {\n        if (columnCount > 0 && rowCount > 0) {\n          const [\n            overscanColumnStartIndex,\n            overscanColumnStopIndex,\n            visibleColumnStartIndex,\n            visibleColumnStopIndex,\n          ] = this._getHorizontalRangeToRender();\n          const [\n            overscanRowStartIndex,\n            overscanRowStopIndex,\n            visibleRowStartIndex,\n            visibleRowStopIndex,\n          ] = this._getVerticalRangeToRender();\n          this._callOnItemsRendered(\n            overscanColumnStartIndex,\n            overscanColumnStopIndex,\n            overscanRowStartIndex,\n            overscanRowStopIndex,\n            visibleColumnStartIndex,\n            visibleColumnStopIndex,\n            visibleRowStartIndex,\n            visibleRowStopIndex\n          );\n        }\n      }\n\n      if (typeof onScroll === 'function') {\n        const {\n          horizontalScrollDirection,\n          scrollLeft,\n          scrollTop,\n          scrollUpdateWasRequested,\n          verticalScrollDirection,\n        } = this.state;\n        this._callOnScroll(\n          scrollLeft,\n          scrollTop,\n          horizontalScrollDirection,\n          verticalScrollDirection,\n          scrollUpdateWasRequested\n        );\n      }\n    }\n\n    // Lazily create and cache item styles while scrolling,\n    // So that pure component sCU will prevent re-renders.\n    // We maintain this cache, and pass a style prop rather than index,\n    // So that List can clear cached styles and force item re-render if necessary.\n    _getItemStyle: (rowIndex: number, columnIndex: number) => Object;\n    _getItemStyle = (rowIndex: number, columnIndex: number): Object => {\n      const { columnWidth, direction, rowHeight } = this.props;\n\n      const itemStyleCache = this._getItemStyleCache(\n        shouldResetStyleCacheOnItemSizeChange && columnWidth,\n        shouldResetStyleCacheOnItemSizeChange && direction,\n        shouldResetStyleCacheOnItemSizeChange && rowHeight\n      );\n\n      const key = `${rowIndex}:${columnIndex}`;\n\n      let style;\n      if (itemStyleCache.hasOwnProperty(key)) {\n        style = itemStyleCache[key];\n      } else {\n        const offset = getColumnOffset(\n          this.props,\n          columnIndex,\n          this._instanceProps\n        );\n        const isRtl = direction === 'rtl';\n        itemStyleCache[key] = style = {\n          position: 'absolute',\n          left: isRtl ? undefined : offset,\n          right: isRtl ? offset : undefined,\n          top: getRowOffset(this.props, rowIndex, this._instanceProps),\n          height: getRowHeight(this.props, rowIndex, this._instanceProps),\n          width: getColumnWidth(this.props, columnIndex, this._instanceProps),\n        };\n      }\n\n      return style;\n    };\n\n    _getItemStyleCache: (_: any, __: any, ___: any) => ItemStyleCache;\n    _getItemStyleCache = memoizeOne((_: any, __: any, ___: any) => ({}));\n\n    _getHorizontalRangeToRender(): [number, number, number, number] {\n      const {\n        columnCount,\n        overscanColumnCount,\n        overscanColumnsCount,\n        overscanCount,\n        rowCount,\n      } = this.props;\n      const { horizontalScrollDirection, isScrolling, scrollLeft } = this.state;\n\n      const overscanCountResolved: number =\n        overscanColumnCount || overscanColumnsCount || overscanCount || 1;\n\n      if (columnCount === 0 || rowCount === 0) {\n        return [0, 0, 0, 0];\n      }\n\n      const startIndex = getColumnStartIndexForOffset(\n        this.props,\n        scrollLeft,\n        this._instanceProps\n      );\n      const stopIndex = getColumnStopIndexForStartIndex(\n        this.props,\n        startIndex,\n        scrollLeft,\n        this._instanceProps\n      );\n\n      // Overscan by one item in each direction so that tab/focus works.\n      // If there isn't at least one extra item, tab loops back around.\n      const overscanBackward =\n        !isScrolling || horizontalScrollDirection === 'backward'\n          ? Math.max(1, overscanCountResolved)\n          : 1;\n      const overscanForward =\n        !isScrolling || horizontalScrollDirection === 'forward'\n          ? Math.max(1, overscanCountResolved)\n          : 1;\n\n      return [\n        Math.max(0, startIndex - overscanBackward),\n        Math.max(0, Math.min(columnCount - 1, stopIndex + overscanForward)),\n        startIndex,\n        stopIndex,\n      ];\n    }\n\n    _getVerticalRangeToRender(): [number, number, number, number] {\n      const {\n        columnCount,\n        overscanCount,\n        overscanRowCount,\n        overscanRowsCount,\n        rowCount,\n      } = this.props;\n      const { isScrolling, verticalScrollDirection, scrollTop } = this.state;\n\n      const overscanCountResolved: number =\n        overscanRowCount || overscanRowsCount || overscanCount || 1;\n\n      if (columnCount === 0 || rowCount === 0) {\n        return [0, 0, 0, 0];\n      }\n\n      const startIndex = getRowStartIndexForOffset(\n        this.props,\n        scrollTop,\n        this._instanceProps\n      );\n      const stopIndex = getRowStopIndexForStartIndex(\n        this.props,\n        startIndex,\n        scrollTop,\n        this._instanceProps\n      );\n\n      // Overscan by one item in each direction so that tab/focus works.\n      // If there isn't at least one extra item, tab loops back around.\n      const overscanBackward =\n        !isScrolling || verticalScrollDirection === 'backward'\n          ? Math.max(1, overscanCountResolved)\n          : 1;\n      const overscanForward =\n        !isScrolling || verticalScrollDirection === 'forward'\n          ? Math.max(1, overscanCountResolved)\n          : 1;\n\n      return [\n        Math.max(0, startIndex - overscanBackward),\n        Math.max(0, Math.min(rowCount - 1, stopIndex + overscanForward)),\n        startIndex,\n        stopIndex,\n      ];\n    }\n\n    _onScroll = (event: ScrollEvent): void => {\n      const {\n        clientHeight,\n        clientWidth,\n        scrollLeft,\n        scrollTop,\n        scrollHeight,\n        scrollWidth,\n      } = event.currentTarget;\n      this.setState(prevState => {\n        if (\n          prevState.scrollLeft === scrollLeft &&\n          prevState.scrollTop === scrollTop\n        ) {\n          // Scroll position may have been updated by cDM/cDU,\n          // In which case we don't need to trigger another render,\n          // And we don't want to update state.isScrolling.\n          return null;\n        }\n\n        const { direction } = this.props;\n\n        // TRICKY According to the spec, scrollLeft should be negative for RTL aligned elements.\n        // This is not the case for all browsers though (e.g. Chrome reports values as positive, measured relative to the left).\n        // It's also easier for this component if we convert offsets to the same format as they would be in for ltr.\n        // So the simplest solution is to determine which browser behavior we're dealing with, and convert based on it.\n        let calculatedScrollLeft = scrollLeft;\n        if (direction === 'rtl') {\n          switch (getRTLOffsetType()) {\n            case 'negative':\n              calculatedScrollLeft = -scrollLeft;\n              break;\n            case 'positive-descending':\n              calculatedScrollLeft = scrollWidth - clientWidth - scrollLeft;\n              break;\n          }\n        }\n\n        // Prevent Safari's elastic scrolling from causing visual shaking when scrolling past bounds.\n        calculatedScrollLeft = Math.max(\n          0,\n          Math.min(calculatedScrollLeft, scrollWidth - clientWidth)\n        );\n        const calculatedScrollTop = Math.max(\n          0,\n          Math.min(scrollTop, scrollHeight - clientHeight)\n        );\n\n        return {\n          isScrolling: true,\n          horizontalScrollDirection:\n            prevState.scrollLeft < scrollLeft ? 'forward' : 'backward',\n          scrollLeft: calculatedScrollLeft,\n          scrollTop: calculatedScrollTop,\n          verticalScrollDirection:\n            prevState.scrollTop < scrollTop ? 'forward' : 'backward',\n          scrollUpdateWasRequested: false,\n        };\n      }, this._resetIsScrollingDebounced);\n    };\n\n    _outerRefSetter = (ref: any): void => {\n      const { outerRef } = this.props;\n\n      this._outerRef = ((ref: any): HTMLDivElement);\n\n      if (typeof outerRef === 'function') {\n        outerRef(ref);\n      } else if (\n        outerRef != null &&\n        typeof outerRef === 'object' &&\n        outerRef.hasOwnProperty('current')\n      ) {\n        outerRef.current = ref;\n      }\n    };\n\n    _resetIsScrollingDebounced = () => {\n      if (this._resetIsScrollingTimeoutId !== null) {\n        cancelTimeout(this._resetIsScrollingTimeoutId);\n      }\n\n      this._resetIsScrollingTimeoutId = requestTimeout(\n        this._resetIsScrolling,\n        IS_SCROLLING_DEBOUNCE_INTERVAL\n      );\n    };\n\n    _resetIsScrolling = () => {\n      this._resetIsScrollingTimeoutId = null;\n\n      this.setState({ isScrolling: false }, () => {\n        // Clear style cache after state update has been committed.\n        // This way we don't break pure sCU for items that don't use isScrolling param.\n        this._getItemStyleCache(-1);\n      });\n    };\n  };\n}\n\nconst validateSharedProps = (\n  {\n    children,\n    direction,\n    height,\n    innerTagName,\n    outerTagName,\n    overscanColumnsCount,\n    overscanCount,\n    overscanRowsCount,\n    width,\n  }: Props<any>,\n  { instance }: State\n): void => {\n  if (process.env.NODE_ENV !== 'production') {\n    if (typeof overscanCount === 'number') {\n      if (devWarningsOverscanCount && !devWarningsOverscanCount.has(instance)) {\n        devWarningsOverscanCount.add(instance);\n        console.warn(\n          'The overscanCount prop has been deprecated. ' +\n            'Please use the overscanColumnCount and overscanRowCount props instead.'\n        );\n      }\n    }\n\n    if (\n      typeof overscanColumnsCount === 'number' ||\n      typeof overscanRowsCount === 'number'\n    ) {\n      if (\n        devWarningsOverscanRowsColumnsCount &&\n        !devWarningsOverscanRowsColumnsCount.has(instance)\n      ) {\n        devWarningsOverscanRowsColumnsCount.add(instance);\n        console.warn(\n          'The overscanColumnsCount and overscanRowsCount props have been deprecated. ' +\n            'Please use the overscanColumnCount and overscanRowCount props instead.'\n        );\n      }\n    }\n\n    if (innerTagName != null || outerTagName != null) {\n      if (devWarningsTagName && !devWarningsTagName.has(instance)) {\n        devWarningsTagName.add(instance);\n        console.warn(\n          'The innerTagName and outerTagName props have been deprecated. ' +\n            'Please use the innerElementType and outerElementType props instead.'\n        );\n      }\n    }\n\n    if (children == null) {\n      throw Error(\n        'An invalid \"children\" prop has been specified. ' +\n          'Value should be a React component. ' +\n          `\"${children === null ? 'null' : typeof children}\" was specified.`\n      );\n    }\n\n    switch (direction) {\n      case 'ltr':\n      case 'rtl':\n        // Valid values\n        break;\n      default:\n        throw Error(\n          'An invalid \"direction\" prop has been specified. ' +\n            'Value should be either \"ltr\" or \"rtl\". ' +\n            `\"${direction}\" was specified.`\n        );\n    }\n\n    if (typeof width !== 'number') {\n      throw Error(\n        'An invalid \"width\" prop has been specified. ' +\n          'Grids must specify a number for width. ' +\n          `\"${width === null ? 'null' : typeof width}\" was specified.`\n      );\n    }\n\n    if (typeof height !== 'number') {\n      throw Error(\n        'An invalid \"height\" prop has been specified. ' +\n          'Grids must specify a number for height. ' +\n          `\"${height === null ? 'null' : typeof height}\" was specified.`\n      );\n    }\n  }\n};\n", "// @flow\n\nimport createGridComponent from './createGridComponent';\n\nimport type { Pro<PERSON>, ScrollToAlign } from './createGridComponent';\n\nconst DEFAULT_ESTIMATED_ITEM_SIZE = 50;\n\ntype VariableSizeProps = {|\n  estimatedColumnWidth: number,\n  estimatedRowHeight: number,\n  ...Props<any>,\n|};\n\ntype itemSizeGetter = (index: number) => number;\ntype ItemType = 'column' | 'row';\n\ntype ItemMetadata = {|\n  offset: number,\n  size: number,\n|};\ntype ItemMetadataMap = { [index: number]: ItemMetadata };\ntype InstanceProps = {|\n  columnMetadataMap: ItemMetadataMap,\n  estimatedColumnWidth: number,\n  estimatedRowHeight: number,\n  lastMeasuredColumnIndex: number,\n  lastMeasuredRowIndex: number,\n  rowMetadataMap: ItemMetadataMap,\n|};\n\nconst getEstimatedTotalHeight = (\n  { rowCount }: Props<any>,\n  { rowMetadataMap, estimatedRowHeight, lastMeasuredRowIndex }: InstanceProps\n) => {\n  let totalSizeOfMeasuredRows = 0;\n\n  // Edge case check for when the number of items decreases while a scroll is in progress.\n  // https://github.com/bvaughn/react-window/pull/138\n  if (lastMeasuredRowIndex >= rowCount) {\n    lastMeasuredRowIndex = rowCount - 1;\n  }\n\n  if (lastMeasuredRowIndex >= 0) {\n    const itemMetadata = rowMetadataMap[lastMeasuredRowIndex];\n    totalSizeOfMeasuredRows = itemMetadata.offset + itemMetadata.size;\n  }\n\n  const numUnmeasuredItems = rowCount - lastMeasuredRowIndex - 1;\n  const totalSizeOfUnmeasuredItems = numUnmeasuredItems * estimatedRowHeight;\n\n  return totalSizeOfMeasuredRows + totalSizeOfUnmeasuredItems;\n};\n\nconst getEstimatedTotalWidth = (\n  { columnCount }: Props<any>,\n  {\n    columnMetadataMap,\n    estimatedColumnWidth,\n    lastMeasuredColumnIndex,\n  }: InstanceProps\n) => {\n  let totalSizeOfMeasuredRows = 0;\n\n  // Edge case check for when the number of items decreases while a scroll is in progress.\n  // https://github.com/bvaughn/react-window/pull/138\n  if (lastMeasuredColumnIndex >= columnCount) {\n    lastMeasuredColumnIndex = columnCount - 1;\n  }\n\n  if (lastMeasuredColumnIndex >= 0) {\n    const itemMetadata = columnMetadataMap[lastMeasuredColumnIndex];\n    totalSizeOfMeasuredRows = itemMetadata.offset + itemMetadata.size;\n  }\n\n  const numUnmeasuredItems = columnCount - lastMeasuredColumnIndex - 1;\n  const totalSizeOfUnmeasuredItems = numUnmeasuredItems * estimatedColumnWidth;\n\n  return totalSizeOfMeasuredRows + totalSizeOfUnmeasuredItems;\n};\n\nconst getItemMetadata = (\n  itemType: ItemType,\n  props: Props<any>,\n  index: number,\n  instanceProps: InstanceProps\n): ItemMetadata => {\n  let itemMetadataMap, itemSize, lastMeasuredIndex;\n  if (itemType === 'column') {\n    itemMetadataMap = instanceProps.columnMetadataMap;\n    itemSize = ((props.columnWidth: any): itemSizeGetter);\n    lastMeasuredIndex = instanceProps.lastMeasuredColumnIndex;\n  } else {\n    itemMetadataMap = instanceProps.rowMetadataMap;\n    itemSize = ((props.rowHeight: any): itemSizeGetter);\n    lastMeasuredIndex = instanceProps.lastMeasuredRowIndex;\n  }\n\n  if (index > lastMeasuredIndex) {\n    let offset = 0;\n    if (lastMeasuredIndex >= 0) {\n      const itemMetadata = itemMetadataMap[lastMeasuredIndex];\n      offset = itemMetadata.offset + itemMetadata.size;\n    }\n\n    for (let i = lastMeasuredIndex + 1; i <= index; i++) {\n      let size = itemSize(i);\n\n      itemMetadataMap[i] = {\n        offset,\n        size,\n      };\n\n      offset += size;\n    }\n\n    if (itemType === 'column') {\n      instanceProps.lastMeasuredColumnIndex = index;\n    } else {\n      instanceProps.lastMeasuredRowIndex = index;\n    }\n  }\n\n  return itemMetadataMap[index];\n};\n\nconst findNearestItem = (\n  itemType: ItemType,\n  props: Props<any>,\n  instanceProps: InstanceProps,\n  offset: number\n) => {\n  let itemMetadataMap, lastMeasuredIndex;\n  if (itemType === 'column') {\n    itemMetadataMap = instanceProps.columnMetadataMap;\n    lastMeasuredIndex = instanceProps.lastMeasuredColumnIndex;\n  } else {\n    itemMetadataMap = instanceProps.rowMetadataMap;\n    lastMeasuredIndex = instanceProps.lastMeasuredRowIndex;\n  }\n\n  const lastMeasuredItemOffset =\n    lastMeasuredIndex > 0 ? itemMetadataMap[lastMeasuredIndex].offset : 0;\n\n  if (lastMeasuredItemOffset >= offset) {\n    // If we've already measured items within this range just use a binary search as it's faster.\n    return findNearestItemBinarySearch(\n      itemType,\n      props,\n      instanceProps,\n      lastMeasuredIndex,\n      0,\n      offset\n    );\n  } else {\n    // If we haven't yet measured this high, fallback to an exponential search with an inner binary search.\n    // The exponential search avoids pre-computing sizes for the full set of items as a binary search would.\n    // The overall complexity for this approach is O(log n).\n    return findNearestItemExponentialSearch(\n      itemType,\n      props,\n      instanceProps,\n      Math.max(0, lastMeasuredIndex),\n      offset\n    );\n  }\n};\n\nconst findNearestItemBinarySearch = (\n  itemType: ItemType,\n  props: Props<any>,\n  instanceProps: InstanceProps,\n  high: number,\n  low: number,\n  offset: number\n): number => {\n  while (low <= high) {\n    const middle = low + Math.floor((high - low) / 2);\n    const currentOffset = getItemMetadata(\n      itemType,\n      props,\n      middle,\n      instanceProps\n    ).offset;\n\n    if (currentOffset === offset) {\n      return middle;\n    } else if (currentOffset < offset) {\n      low = middle + 1;\n    } else if (currentOffset > offset) {\n      high = middle - 1;\n    }\n  }\n\n  if (low > 0) {\n    return low - 1;\n  } else {\n    return 0;\n  }\n};\n\nconst findNearestItemExponentialSearch = (\n  itemType: ItemType,\n  props: Props<any>,\n  instanceProps: InstanceProps,\n  index: number,\n  offset: number\n): number => {\n  const itemCount = itemType === 'column' ? props.columnCount : props.rowCount;\n  let interval = 1;\n\n  while (\n    index < itemCount &&\n    getItemMetadata(itemType, props, index, instanceProps).offset < offset\n  ) {\n    index += interval;\n    interval *= 2;\n  }\n\n  return findNearestItemBinarySearch(\n    itemType,\n    props,\n    instanceProps,\n    Math.min(index, itemCount - 1),\n    Math.floor(index / 2),\n    offset\n  );\n};\n\nconst getOffsetForIndexAndAlignment = (\n  itemType: ItemType,\n  props: Props<any>,\n  index: number,\n  align: ScrollToAlign,\n  scrollOffset: number,\n  instanceProps: InstanceProps,\n  scrollbarSize: number\n): number => {\n  const size = itemType === 'column' ? props.width : props.height;\n  const itemMetadata = getItemMetadata(itemType, props, index, instanceProps);\n\n  // Get estimated total size after ItemMetadata is computed,\n  // To ensure it reflects actual measurements instead of just estimates.\n  const estimatedTotalSize =\n    itemType === 'column'\n      ? getEstimatedTotalWidth(props, instanceProps)\n      : getEstimatedTotalHeight(props, instanceProps);\n\n  const maxOffset = Math.max(\n    0,\n    Math.min(estimatedTotalSize - size, itemMetadata.offset)\n  );\n  const minOffset = Math.max(\n    0,\n    itemMetadata.offset - size + scrollbarSize + itemMetadata.size\n  );\n\n  if (align === 'smart') {\n    if (scrollOffset >= minOffset - size && scrollOffset <= maxOffset + size) {\n      align = 'auto';\n    } else {\n      align = 'center';\n    }\n  }\n\n  switch (align) {\n    case 'start':\n      return maxOffset;\n    case 'end':\n      return minOffset;\n    case 'center':\n      return Math.round(minOffset + (maxOffset - minOffset) / 2);\n    case 'auto':\n    default:\n      if (scrollOffset >= minOffset && scrollOffset <= maxOffset) {\n        return scrollOffset;\n      } else if (minOffset > maxOffset) {\n        // Because we only take into account the scrollbar size when calculating minOffset\n        // this value can be larger than maxOffset when at the end of the list\n        return minOffset;\n      } else if (scrollOffset < minOffset) {\n        return minOffset;\n      } else {\n        return maxOffset;\n      }\n  }\n};\n\nconst VariableSizeGrid = createGridComponent({\n  getColumnOffset: (\n    props: Props<any>,\n    index: number,\n    instanceProps: InstanceProps\n  ): number => getItemMetadata('column', props, index, instanceProps).offset,\n\n  getColumnStartIndexForOffset: (\n    props: Props<any>,\n    scrollLeft: number,\n    instanceProps: InstanceProps\n  ): number => findNearestItem('column', props, instanceProps, scrollLeft),\n\n  getColumnStopIndexForStartIndex: (\n    props: Props<any>,\n    startIndex: number,\n    scrollLeft: number,\n    instanceProps: InstanceProps\n  ): number => {\n    const { columnCount, width } = props;\n\n    const itemMetadata = getItemMetadata(\n      'column',\n      props,\n      startIndex,\n      instanceProps\n    );\n    const maxOffset = scrollLeft + width;\n\n    let offset = itemMetadata.offset + itemMetadata.size;\n    let stopIndex = startIndex;\n\n    while (stopIndex < columnCount - 1 && offset < maxOffset) {\n      stopIndex++;\n      offset += getItemMetadata('column', props, stopIndex, instanceProps).size;\n    }\n\n    return stopIndex;\n  },\n\n  getColumnWidth: (\n    props: Props<any>,\n    index: number,\n    instanceProps: InstanceProps\n  ): number => instanceProps.columnMetadataMap[index].size,\n\n  getEstimatedTotalHeight,\n  getEstimatedTotalWidth,\n\n  getOffsetForColumnAndAlignment: (\n    props: Props<any>,\n    index: number,\n    align: ScrollToAlign,\n    scrollOffset: number,\n    instanceProps: InstanceProps,\n    scrollbarSize: number\n  ): number =>\n    getOffsetForIndexAndAlignment(\n      'column',\n      props,\n      index,\n      align,\n      scrollOffset,\n      instanceProps,\n      scrollbarSize\n    ),\n\n  getOffsetForRowAndAlignment: (\n    props: Props<any>,\n    index: number,\n    align: ScrollToAlign,\n    scrollOffset: number,\n    instanceProps: InstanceProps,\n    scrollbarSize: number\n  ): number =>\n    getOffsetForIndexAndAlignment(\n      'row',\n      props,\n      index,\n      align,\n      scrollOffset,\n      instanceProps,\n      scrollbarSize\n    ),\n\n  getRowOffset: (\n    props: Props<any>,\n    index: number,\n    instanceProps: InstanceProps\n  ): number => getItemMetadata('row', props, index, instanceProps).offset,\n\n  getRowHeight: (\n    props: Props<any>,\n    index: number,\n    instanceProps: InstanceProps\n  ): number => instanceProps.rowMetadataMap[index].size,\n\n  getRowStartIndexForOffset: (\n    props: Props<any>,\n    scrollTop: number,\n    instanceProps: InstanceProps\n  ): number => findNearestItem('row', props, instanceProps, scrollTop),\n\n  getRowStopIndexForStartIndex: (\n    props: Props<any>,\n    startIndex: number,\n    scrollTop: number,\n    instanceProps: InstanceProps\n  ): number => {\n    const { rowCount, height } = props;\n\n    const itemMetadata = getItemMetadata(\n      'row',\n      props,\n      startIndex,\n      instanceProps\n    );\n    const maxOffset = scrollTop + height;\n\n    let offset = itemMetadata.offset + itemMetadata.size;\n    let stopIndex = startIndex;\n\n    while (stopIndex < rowCount - 1 && offset < maxOffset) {\n      stopIndex++;\n      offset += getItemMetadata('row', props, stopIndex, instanceProps).size;\n    }\n\n    return stopIndex;\n  },\n\n  initInstanceProps(props: Props<any>, instance: any): InstanceProps {\n    const {\n      estimatedColumnWidth,\n      estimatedRowHeight,\n    } = ((props: any): VariableSizeProps);\n\n    const instanceProps = {\n      columnMetadataMap: {},\n      estimatedColumnWidth: estimatedColumnWidth || DEFAULT_ESTIMATED_ITEM_SIZE,\n      estimatedRowHeight: estimatedRowHeight || DEFAULT_ESTIMATED_ITEM_SIZE,\n      lastMeasuredColumnIndex: -1,\n      lastMeasuredRowIndex: -1,\n      rowMetadataMap: {},\n    };\n\n    instance.resetAfterColumnIndex = (\n      columnIndex: number,\n      shouldForceUpdate?: boolean = true\n    ) => {\n      instance.resetAfterIndices({ columnIndex, shouldForceUpdate });\n    };\n\n    instance.resetAfterRowIndex = (\n      rowIndex: number,\n      shouldForceUpdate?: boolean = true\n    ) => {\n      instance.resetAfterIndices({ rowIndex, shouldForceUpdate });\n    };\n\n    instance.resetAfterIndices = ({\n      columnIndex,\n      rowIndex,\n      shouldForceUpdate = true,\n    }: {\n      columnIndex?: number,\n      rowIndex?: number,\n      shouldForceUpdate: boolean,\n    }) => {\n      if (typeof columnIndex === 'number') {\n        instanceProps.lastMeasuredColumnIndex = Math.min(\n          instanceProps.lastMeasuredColumnIndex,\n          columnIndex - 1\n        );\n      }\n      if (typeof rowIndex === 'number') {\n        instanceProps.lastMeasuredRowIndex = Math.min(\n          instanceProps.lastMeasuredRowIndex,\n          rowIndex - 1\n        );\n      }\n\n      // We could potentially optimize further by only evicting styles after this index,\n      // But since styles are only cached while scrolling is in progress-\n      // It seems an unnecessary optimization.\n      // It's unlikely that resetAfterIndex() will be called while a user is scrolling.\n      instance._getItemStyleCache(-1);\n\n      if (shouldForceUpdate) {\n        instance.forceUpdate();\n      }\n    };\n\n    return instanceProps;\n  },\n\n  shouldResetStyleCacheOnItemSizeChange: false,\n\n  validateProps: ({ columnWidth, rowHeight }: Props<any>): void => {\n    if (process.env.NODE_ENV !== 'production') {\n      if (typeof columnWidth !== 'function') {\n        throw Error(\n          'An invalid \"columnWidth\" prop has been specified. ' +\n            'Value should be a function. ' +\n            `\"${\n              columnWidth === null ? 'null' : typeof columnWidth\n            }\" was specified.`\n        );\n      } else if (typeof rowHeight !== 'function') {\n        throw Error(\n          'An invalid \"rowHeight\" prop has been specified. ' +\n            'Value should be a function. ' +\n            `\"${rowHeight === null ? 'null' : typeof rowHeight}\" was specified.`\n        );\n      }\n    }\n  },\n});\n\nexport default VariableSizeGrid;\n", "// @flow\n\nimport memoizeOne from 'memoize-one';\nimport { createElement, PureComponent } from 'react';\nimport { cancelTimeout, requestTimeout } from './timer';\nimport { getRTLOffsetType } from './domHelpers';\n\nimport type { TimeoutID } from './timer';\n\nexport type ScrollToAlign = 'auto' | 'smart' | 'center' | 'start' | 'end';\n\ntype itemSize = number | ((index: number) => number);\n// TODO Deprecate directions \"horizontal\" and \"vertical\"\ntype Direction = 'ltr' | 'rtl' | 'horizontal' | 'vertical';\ntype Layout = 'horizontal' | 'vertical';\n\ntype RenderComponentProps<T> = {|\n  data: T,\n  index: number,\n  isScrolling?: boolean,\n  style: Object,\n|};\ntype RenderComponent<T> = React$ComponentType<$Shape<RenderComponentProps<T>>>;\n\ntype ScrollDirection = 'forward' | 'backward';\n\ntype onItemsRenderedCallback = ({\n  overscanStartIndex: number,\n  overscanStopIndex: number,\n  visibleStartIndex: number,\n  visibleStopIndex: number,\n}) => void;\ntype onScrollCallback = ({\n  scrollDirection: ScrollDirection,\n  scrollOffset: number,\n  scrollUpdateWasRequested: boolean,\n}) => void;\n\ntype ScrollEvent = SyntheticEvent<HTMLDivElement>;\ntype ItemStyleCache = { [index: number]: Object };\n\ntype OuterProps = {|\n  children: React$Node,\n  className: string | void,\n  onScroll: ScrollEvent => void,\n  style: {\n    [string]: mixed,\n  },\n|};\n\ntype InnerProps = {|\n  children: React$Node,\n  style: {\n    [string]: mixed,\n  },\n|};\n\nexport type Props<T> = {|\n  children: RenderComponent<T>,\n  className?: string,\n  direction: Direction,\n  height: number | string,\n  initialScrollOffset?: number,\n  innerRef?: any,\n  innerElementType?: string | React$AbstractComponent<InnerProps, any>,\n  innerTagName?: string, // deprecated\n  itemCount: number,\n  itemData: T,\n  itemKey?: (index: number, data: T) => any,\n  itemSize: itemSize,\n  layout: Layout,\n  onItemsRendered?: onItemsRenderedCallback,\n  onScroll?: onScrollCallback,\n  outerRef?: any,\n  outerElementType?: string | React$AbstractComponent<OuterProps, any>,\n  outerTagName?: string, // deprecated\n  overscanCount: number,\n  style?: Object,\n  useIsScrolling: boolean,\n  width: number | string,\n|};\n\ntype State = {|\n  instance: any,\n  isScrolling: boolean,\n  scrollDirection: ScrollDirection,\n  scrollOffset: number,\n  scrollUpdateWasRequested: boolean,\n|};\n\ntype GetItemOffset = (\n  props: Props<any>,\n  index: number,\n  instanceProps: any\n) => number;\ntype GetItemSize = (\n  props: Props<any>,\n  index: number,\n  instanceProps: any\n) => number;\ntype GetEstimatedTotalSize = (props: Props<any>, instanceProps: any) => number;\ntype GetOffsetForIndexAndAlignment = (\n  props: Props<any>,\n  index: number,\n  align: ScrollToAlign,\n  scrollOffset: number,\n  instanceProps: any\n) => number;\ntype GetStartIndexForOffset = (\n  props: Props<any>,\n  offset: number,\n  instanceProps: any\n) => number;\ntype GetStopIndexForStartIndex = (\n  props: Props<any>,\n  startIndex: number,\n  scrollOffset: number,\n  instanceProps: any\n) => number;\ntype InitInstanceProps = (props: Props<any>, instance: any) => any;\ntype ValidateProps = (props: Props<any>) => void;\n\nconst IS_SCROLLING_DEBOUNCE_INTERVAL = 150;\n\nconst defaultItemKey = (index: number, data: any) => index;\n\n// In DEV mode, this Set helps us only log a warning once per component instance.\n// This avoids spamming the console every time a render happens.\nlet devWarningsDirection = null;\nlet devWarningsTagName = null;\nif (process.env.NODE_ENV !== 'production') {\n  if (typeof window !== 'undefined' && typeof window.WeakSet !== 'undefined') {\n    devWarningsDirection = new WeakSet();\n    devWarningsTagName = new WeakSet();\n  }\n}\n\nexport default function createListComponent({\n  getItemOffset,\n  getEstimatedTotalSize,\n  getItemSize,\n  getOffsetForIndexAndAlignment,\n  getStartIndexForOffset,\n  getStopIndexForStartIndex,\n  initInstanceProps,\n  shouldResetStyleCacheOnItemSizeChange,\n  validateProps,\n}: {|\n  getItemOffset: GetItemOffset,\n  getEstimatedTotalSize: GetEstimatedTotalSize,\n  getItemSize: GetItemSize,\n  getOffsetForIndexAndAlignment: GetOffsetForIndexAndAlignment,\n  getStartIndexForOffset: GetStartIndexForOffset,\n  getStopIndexForStartIndex: GetStopIndexForStartIndex,\n  initInstanceProps: InitInstanceProps,\n  shouldResetStyleCacheOnItemSizeChange: boolean,\n  validateProps: ValidateProps,\n|}) {\n  return class List<T> extends PureComponent<Props<T>, State> {\n    _instanceProps: any = initInstanceProps(this.props, this);\n    _outerRef: ?HTMLDivElement;\n    _resetIsScrollingTimeoutId: TimeoutID | null = null;\n\n    static defaultProps = {\n      direction: 'ltr',\n      itemData: undefined,\n      layout: 'vertical',\n      overscanCount: 2,\n      useIsScrolling: false,\n    };\n\n    state: State = {\n      instance: this,\n      isScrolling: false,\n      scrollDirection: 'forward',\n      scrollOffset:\n        typeof this.props.initialScrollOffset === 'number'\n          ? this.props.initialScrollOffset\n          : 0,\n      scrollUpdateWasRequested: false,\n    };\n\n    // Always use explicit constructor for React components.\n    // It produces less code after transpilation. (#26)\n    // eslint-disable-next-line no-useless-constructor\n    constructor(props: Props<T>) {\n      super(props);\n    }\n\n    static getDerivedStateFromProps(\n      nextProps: Props<T>,\n      prevState: State\n    ): $Shape<State> | null {\n      validateSharedProps(nextProps, prevState);\n      validateProps(nextProps);\n      return null;\n    }\n\n    scrollTo(scrollOffset: number): void {\n      scrollOffset = Math.max(0, scrollOffset);\n\n      this.setState(prevState => {\n        if (prevState.scrollOffset === scrollOffset) {\n          return null;\n        }\n        return {\n          scrollDirection:\n            prevState.scrollOffset < scrollOffset ? 'forward' : 'backward',\n          scrollOffset: scrollOffset,\n          scrollUpdateWasRequested: true,\n        };\n      }, this._resetIsScrollingDebounced);\n    }\n\n    scrollToItem(index: number, align: ScrollToAlign = 'auto'): void {\n      const { itemCount } = this.props;\n      const { scrollOffset } = this.state;\n\n      index = Math.max(0, Math.min(index, itemCount - 1));\n\n      this.scrollTo(\n        getOffsetForIndexAndAlignment(\n          this.props,\n          index,\n          align,\n          scrollOffset,\n          this._instanceProps\n        )\n      );\n    }\n\n    componentDidMount() {\n      const { direction, initialScrollOffset, layout } = this.props;\n\n      if (typeof initialScrollOffset === 'number' && this._outerRef != null) {\n        const outerRef = ((this._outerRef: any): HTMLElement);\n        // TODO Deprecate direction \"horizontal\"\n        if (direction === 'horizontal' || layout === 'horizontal') {\n          outerRef.scrollLeft = initialScrollOffset;\n        } else {\n          outerRef.scrollTop = initialScrollOffset;\n        }\n      }\n\n      this._callPropsCallbacks();\n    }\n\n    componentDidUpdate() {\n      const { direction, layout } = this.props;\n      const { scrollOffset, scrollUpdateWasRequested } = this.state;\n\n      if (scrollUpdateWasRequested && this._outerRef != null) {\n        const outerRef = ((this._outerRef: any): HTMLElement);\n\n        // TODO Deprecate direction \"horizontal\"\n        if (direction === 'horizontal' || layout === 'horizontal') {\n          if (direction === 'rtl') {\n            // TRICKY According to the spec, scrollLeft should be negative for RTL aligned elements.\n            // This is not the case for all browsers though (e.g. Chrome reports values as positive, measured relative to the left).\n            // So we need to determine which browser behavior we're dealing with, and mimic it.\n            switch (getRTLOffsetType()) {\n              case 'negative':\n                outerRef.scrollLeft = -scrollOffset;\n                break;\n              case 'positive-ascending':\n                outerRef.scrollLeft = scrollOffset;\n                break;\n              default:\n                const { clientWidth, scrollWidth } = outerRef;\n                outerRef.scrollLeft = scrollWidth - clientWidth - scrollOffset;\n                break;\n            }\n          } else {\n            outerRef.scrollLeft = scrollOffset;\n          }\n        } else {\n          outerRef.scrollTop = scrollOffset;\n        }\n      }\n\n      this._callPropsCallbacks();\n    }\n\n    componentWillUnmount() {\n      if (this._resetIsScrollingTimeoutId !== null) {\n        cancelTimeout(this._resetIsScrollingTimeoutId);\n      }\n    }\n\n    render() {\n      const {\n        children,\n        className,\n        direction,\n        height,\n        innerRef,\n        innerElementType,\n        innerTagName,\n        itemCount,\n        itemData,\n        itemKey = defaultItemKey,\n        layout,\n        outerElementType,\n        outerTagName,\n        style,\n        useIsScrolling,\n        width,\n      } = this.props;\n      const { isScrolling } = this.state;\n\n      // TODO Deprecate direction \"horizontal\"\n      const isHorizontal =\n        direction === 'horizontal' || layout === 'horizontal';\n\n      const onScroll = isHorizontal\n        ? this._onScrollHorizontal\n        : this._onScrollVertical;\n\n      const [startIndex, stopIndex] = this._getRangeToRender();\n\n      const items = [];\n      if (itemCount > 0) {\n        for (let index = startIndex; index <= stopIndex; index++) {\n          items.push(\n            createElement(children, {\n              data: itemData,\n              key: itemKey(index, itemData),\n              index,\n              isScrolling: useIsScrolling ? isScrolling : undefined,\n              style: this._getItemStyle(index),\n            })\n          );\n        }\n      }\n\n      // Read this value AFTER items have been created,\n      // So their actual sizes (if variable) are taken into consideration.\n      const estimatedTotalSize = getEstimatedTotalSize(\n        this.props,\n        this._instanceProps\n      );\n\n      return createElement(\n        outerElementType || outerTagName || 'div',\n        {\n          className,\n          onScroll,\n          ref: this._outerRefSetter,\n          style: {\n            position: 'relative',\n            height,\n            width,\n            overflow: 'auto',\n            WebkitOverflowScrolling: 'touch',\n            willChange: 'transform',\n            direction,\n            ...style,\n          },\n        },\n        createElement(innerElementType || innerTagName || 'div', {\n          children: items,\n          ref: innerRef,\n          style: {\n            height: isHorizontal ? '100%' : estimatedTotalSize,\n            pointerEvents: isScrolling ? 'none' : undefined,\n            width: isHorizontal ? estimatedTotalSize : '100%',\n          },\n        })\n      );\n    }\n\n    _callOnItemsRendered: (\n      overscanStartIndex: number,\n      overscanStopIndex: number,\n      visibleStartIndex: number,\n      visibleStopIndex: number\n    ) => void;\n    _callOnItemsRendered = memoizeOne(\n      (\n        overscanStartIndex: number,\n        overscanStopIndex: number,\n        visibleStartIndex: number,\n        visibleStopIndex: number\n      ) =>\n        ((this.props.onItemsRendered: any): onItemsRenderedCallback)({\n          overscanStartIndex,\n          overscanStopIndex,\n          visibleStartIndex,\n          visibleStopIndex,\n        })\n    );\n\n    _callOnScroll: (\n      scrollDirection: ScrollDirection,\n      scrollOffset: number,\n      scrollUpdateWasRequested: boolean\n    ) => void;\n    _callOnScroll = memoizeOne(\n      (\n        scrollDirection: ScrollDirection,\n        scrollOffset: number,\n        scrollUpdateWasRequested: boolean\n      ) =>\n        ((this.props.onScroll: any): onScrollCallback)({\n          scrollDirection,\n          scrollOffset,\n          scrollUpdateWasRequested,\n        })\n    );\n\n    _callPropsCallbacks() {\n      if (typeof this.props.onItemsRendered === 'function') {\n        const { itemCount } = this.props;\n        if (itemCount > 0) {\n          const [\n            overscanStartIndex,\n            overscanStopIndex,\n            visibleStartIndex,\n            visibleStopIndex,\n          ] = this._getRangeToRender();\n          this._callOnItemsRendered(\n            overscanStartIndex,\n            overscanStopIndex,\n            visibleStartIndex,\n            visibleStopIndex\n          );\n        }\n      }\n\n      if (typeof this.props.onScroll === 'function') {\n        const {\n          scrollDirection,\n          scrollOffset,\n          scrollUpdateWasRequested,\n        } = this.state;\n        this._callOnScroll(\n          scrollDirection,\n          scrollOffset,\n          scrollUpdateWasRequested\n        );\n      }\n    }\n\n    // Lazily create and cache item styles while scrolling,\n    // So that pure component sCU will prevent re-renders.\n    // We maintain this cache, and pass a style prop rather than index,\n    // So that List can clear cached styles and force item re-render if necessary.\n    _getItemStyle: (index: number) => Object;\n    _getItemStyle = (index: number): Object => {\n      const { direction, itemSize, layout } = this.props;\n\n      const itemStyleCache = this._getItemStyleCache(\n        shouldResetStyleCacheOnItemSizeChange && itemSize,\n        shouldResetStyleCacheOnItemSizeChange && layout,\n        shouldResetStyleCacheOnItemSizeChange && direction\n      );\n\n      let style;\n      if (itemStyleCache.hasOwnProperty(index)) {\n        style = itemStyleCache[index];\n      } else {\n        const offset = getItemOffset(this.props, index, this._instanceProps);\n        const size = getItemSize(this.props, index, this._instanceProps);\n\n        // TODO Deprecate direction \"horizontal\"\n        const isHorizontal =\n          direction === 'horizontal' || layout === 'horizontal';\n\n        const isRtl = direction === 'rtl';\n        const offsetHorizontal = isHorizontal ? offset : 0;\n        itemStyleCache[index] = style = {\n          position: 'absolute',\n          left: isRtl ? undefined : offsetHorizontal,\n          right: isRtl ? offsetHorizontal : undefined,\n          top: !isHorizontal ? offset : 0,\n          height: !isHorizontal ? size : '100%',\n          width: isHorizontal ? size : '100%',\n        };\n      }\n\n      return style;\n    };\n\n    _getItemStyleCache: (_: any, __: any, ___: any) => ItemStyleCache;\n    _getItemStyleCache = memoizeOne((_: any, __: any, ___: any) => ({}));\n\n    _getRangeToRender(): [number, number, number, number] {\n      const { itemCount, overscanCount } = this.props;\n      const { isScrolling, scrollDirection, scrollOffset } = this.state;\n\n      if (itemCount === 0) {\n        return [0, 0, 0, 0];\n      }\n\n      const startIndex = getStartIndexForOffset(\n        this.props,\n        scrollOffset,\n        this._instanceProps\n      );\n      const stopIndex = getStopIndexForStartIndex(\n        this.props,\n        startIndex,\n        scrollOffset,\n        this._instanceProps\n      );\n\n      // Overscan by one item in each direction so that tab/focus works.\n      // If there isn't at least one extra item, tab loops back around.\n      const overscanBackward =\n        !isScrolling || scrollDirection === 'backward'\n          ? Math.max(1, overscanCount)\n          : 1;\n      const overscanForward =\n        !isScrolling || scrollDirection === 'forward'\n          ? Math.max(1, overscanCount)\n          : 1;\n\n      return [\n        Math.max(0, startIndex - overscanBackward),\n        Math.max(0, Math.min(itemCount - 1, stopIndex + overscanForward)),\n        startIndex,\n        stopIndex,\n      ];\n    }\n\n    _onScrollHorizontal = (event: ScrollEvent): void => {\n      const { clientWidth, scrollLeft, scrollWidth } = event.currentTarget;\n      this.setState(prevState => {\n        if (prevState.scrollOffset === scrollLeft) {\n          // Scroll position may have been updated by cDM/cDU,\n          // In which case we don't need to trigger another render,\n          // And we don't want to update state.isScrolling.\n          return null;\n        }\n\n        const { direction } = this.props;\n\n        let scrollOffset = scrollLeft;\n        if (direction === 'rtl') {\n          // TRICKY According to the spec, scrollLeft should be negative for RTL aligned elements.\n          // This is not the case for all browsers though (e.g. Chrome reports values as positive, measured relative to the left).\n          // It's also easier for this component if we convert offsets to the same format as they would be in for ltr.\n          // So the simplest solution is to determine which browser behavior we're dealing with, and convert based on it.\n          switch (getRTLOffsetType()) {\n            case 'negative':\n              scrollOffset = -scrollLeft;\n              break;\n            case 'positive-descending':\n              scrollOffset = scrollWidth - clientWidth - scrollLeft;\n              break;\n          }\n        }\n\n        // Prevent Safari's elastic scrolling from causing visual shaking when scrolling past bounds.\n        scrollOffset = Math.max(\n          0,\n          Math.min(scrollOffset, scrollWidth - clientWidth)\n        );\n\n        return {\n          isScrolling: true,\n          scrollDirection:\n            prevState.scrollOffset < scrollLeft ? 'forward' : 'backward',\n          scrollOffset,\n          scrollUpdateWasRequested: false,\n        };\n      }, this._resetIsScrollingDebounced);\n    };\n\n    _onScrollVertical = (event: ScrollEvent): void => {\n      const { clientHeight, scrollHeight, scrollTop } = event.currentTarget;\n      this.setState(prevState => {\n        if (prevState.scrollOffset === scrollTop) {\n          // Scroll position may have been updated by cDM/cDU,\n          // In which case we don't need to trigger another render,\n          // And we don't want to update state.isScrolling.\n          return null;\n        }\n\n        // Prevent Safari's elastic scrolling from causing visual shaking when scrolling past bounds.\n        const scrollOffset = Math.max(\n          0,\n          Math.min(scrollTop, scrollHeight - clientHeight)\n        );\n\n        return {\n          isScrolling: true,\n          scrollDirection:\n            prevState.scrollOffset < scrollOffset ? 'forward' : 'backward',\n          scrollOffset,\n          scrollUpdateWasRequested: false,\n        };\n      }, this._resetIsScrollingDebounced);\n    };\n\n    _outerRefSetter = (ref: any): void => {\n      const { outerRef } = this.props;\n\n      this._outerRef = ((ref: any): HTMLDivElement);\n\n      if (typeof outerRef === 'function') {\n        outerRef(ref);\n      } else if (\n        outerRef != null &&\n        typeof outerRef === 'object' &&\n        outerRef.hasOwnProperty('current')\n      ) {\n        outerRef.current = ref;\n      }\n    };\n\n    _resetIsScrollingDebounced = () => {\n      if (this._resetIsScrollingTimeoutId !== null) {\n        cancelTimeout(this._resetIsScrollingTimeoutId);\n      }\n\n      this._resetIsScrollingTimeoutId = requestTimeout(\n        this._resetIsScrolling,\n        IS_SCROLLING_DEBOUNCE_INTERVAL\n      );\n    };\n\n    _resetIsScrolling = () => {\n      this._resetIsScrollingTimeoutId = null;\n\n      this.setState({ isScrolling: false }, () => {\n        // Clear style cache after state update has been committed.\n        // This way we don't break pure sCU for items that don't use isScrolling param.\n        this._getItemStyleCache(-1, null);\n      });\n    };\n  };\n}\n\n// NOTE: I considered further wrapping individual items with a pure ListItem component.\n// This would avoid ever calling the render function for the same index more than once,\n// But it would also add the overhead of a lot of components/fibers.\n// I assume people already do this (render function returning a class component),\n// So my doing it would just unnecessarily double the wrappers.\n\nconst validateSharedProps = (\n  {\n    children,\n    direction,\n    height,\n    layout,\n    innerTagName,\n    outerTagName,\n    width,\n  }: Props<any>,\n  { instance }: State\n): void => {\n  if (process.env.NODE_ENV !== 'production') {\n    if (innerTagName != null || outerTagName != null) {\n      if (devWarningsTagName && !devWarningsTagName.has(instance)) {\n        devWarningsTagName.add(instance);\n        console.warn(\n          'The innerTagName and outerTagName props have been deprecated. ' +\n            'Please use the innerElementType and outerElementType props instead.'\n        );\n      }\n    }\n\n    // TODO Deprecate direction \"horizontal\"\n    const isHorizontal = direction === 'horizontal' || layout === 'horizontal';\n\n    switch (direction) {\n      case 'horizontal':\n      case 'vertical':\n        if (devWarningsDirection && !devWarningsDirection.has(instance)) {\n          devWarningsDirection.add(instance);\n          console.warn(\n            'The direction prop should be either \"ltr\" (default) or \"rtl\". ' +\n              'Please use the layout prop to specify \"vertical\" (default) or \"horizontal\" orientation.'\n          );\n        }\n        break;\n      case 'ltr':\n      case 'rtl':\n        // Valid values\n        break;\n      default:\n        throw Error(\n          'An invalid \"direction\" prop has been specified. ' +\n            'Value should be either \"ltr\" or \"rtl\". ' +\n            `\"${direction}\" was specified.`\n        );\n    }\n\n    switch (layout) {\n      case 'horizontal':\n      case 'vertical':\n        // Valid values\n        break;\n      default:\n        throw Error(\n          'An invalid \"layout\" prop has been specified. ' +\n            'Value should be either \"horizontal\" or \"vertical\". ' +\n            `\"${layout}\" was specified.`\n        );\n    }\n\n    if (children == null) {\n      throw Error(\n        'An invalid \"children\" prop has been specified. ' +\n          'Value should be a React component. ' +\n          `\"${children === null ? 'null' : typeof children}\" was specified.`\n      );\n    }\n\n    if (isHorizontal && typeof width !== 'number') {\n      throw Error(\n        'An invalid \"width\" prop has been specified. ' +\n          'Horizontal lists must specify a number for width. ' +\n          `\"${width === null ? 'null' : typeof width}\" was specified.`\n      );\n    } else if (!isHorizontal && typeof height !== 'number') {\n      throw Error(\n        'An invalid \"height\" prop has been specified. ' +\n          'Vertical lists must specify a number for height. ' +\n          `\"${height === null ? 'null' : typeof height}\" was specified.`\n      );\n    }\n  }\n};\n", "// @flow\n\nimport createListComponent from './createListComponent';\n\nimport type { Pro<PERSON>, ScrollToAlign } from './createListComponent';\n\nconst DEFAULT_ESTIMATED_ITEM_SIZE = 50;\n\ntype VariableSizeProps = {|\n  estimatedItemSize: number,\n  ...Props<any>,\n|};\n\ntype itemSizeGetter = (index: number) => number;\n\ntype ItemMetadata = {|\n  offset: number,\n  size: number,\n|};\ntype InstanceProps = {|\n  itemMetadataMap: { [index: number]: ItemMetadata },\n  estimatedItemSize: number,\n  lastMeasuredIndex: number,\n|};\n\nconst getItemMetadata = (\n  props: Props<any>,\n  index: number,\n  instanceProps: InstanceProps\n): ItemMetadata => {\n  const { itemSize } = ((props: any): VariableSizeProps);\n  const { itemMetadataMap, lastMeasuredIndex } = instanceProps;\n\n  if (index > lastMeasuredIndex) {\n    let offset = 0;\n    if (lastMeasuredIndex >= 0) {\n      const itemMetadata = itemMetadataMap[lastMeasuredIndex];\n      offset = itemMetadata.offset + itemMetadata.size;\n    }\n\n    for (let i = lastMeasuredIndex + 1; i <= index; i++) {\n      let size = ((itemSize: any): itemSizeGetter)(i);\n\n      itemMetadataMap[i] = {\n        offset,\n        size,\n      };\n\n      offset += size;\n    }\n\n    instanceProps.lastMeasuredIndex = index;\n  }\n\n  return itemMetadataMap[index];\n};\n\nconst findNearestItem = (\n  props: Props<any>,\n  instanceProps: InstanceProps,\n  offset: number\n) => {\n  const { itemMetadataMap, lastMeasuredIndex } = instanceProps;\n\n  const lastMeasuredItemOffset =\n    lastMeasuredIndex > 0 ? itemMetadataMap[lastMeasuredIndex].offset : 0;\n\n  if (lastMeasuredItemOffset >= offset) {\n    // If we've already measured items within this range just use a binary search as it's faster.\n    return findNearestItemBinarySearch(\n      props,\n      instanceProps,\n      lastMeasuredIndex,\n      0,\n      offset\n    );\n  } else {\n    // If we haven't yet measured this high, fallback to an exponential search with an inner binary search.\n    // The exponential search avoids pre-computing sizes for the full set of items as a binary search would.\n    // The overall complexity for this approach is O(log n).\n    return findNearestItemExponentialSearch(\n      props,\n      instanceProps,\n      Math.max(0, lastMeasuredIndex),\n      offset\n    );\n  }\n};\n\nconst findNearestItemBinarySearch = (\n  props: Props<any>,\n  instanceProps: InstanceProps,\n  high: number,\n  low: number,\n  offset: number\n): number => {\n  while (low <= high) {\n    const middle = low + Math.floor((high - low) / 2);\n    const currentOffset = getItemMetadata(props, middle, instanceProps).offset;\n\n    if (currentOffset === offset) {\n      return middle;\n    } else if (currentOffset < offset) {\n      low = middle + 1;\n    } else if (currentOffset > offset) {\n      high = middle - 1;\n    }\n  }\n\n  if (low > 0) {\n    return low - 1;\n  } else {\n    return 0;\n  }\n};\n\nconst findNearestItemExponentialSearch = (\n  props: Props<any>,\n  instanceProps: InstanceProps,\n  index: number,\n  offset: number\n): number => {\n  const { itemCount } = props;\n  let interval = 1;\n\n  while (\n    index < itemCount &&\n    getItemMetadata(props, index, instanceProps).offset < offset\n  ) {\n    index += interval;\n    interval *= 2;\n  }\n\n  return findNearestItemBinarySearch(\n    props,\n    instanceProps,\n    Math.min(index, itemCount - 1),\n    Math.floor(index / 2),\n    offset\n  );\n};\n\nconst getEstimatedTotalSize = (\n  { itemCount }: Props<any>,\n  { itemMetadataMap, estimatedItemSize, lastMeasuredIndex }: InstanceProps\n) => {\n  let totalSizeOfMeasuredItems = 0;\n\n  // Edge case check for when the number of items decreases while a scroll is in progress.\n  // https://github.com/bvaughn/react-window/pull/138\n  if (lastMeasuredIndex >= itemCount) {\n    lastMeasuredIndex = itemCount - 1;\n  }\n\n  if (lastMeasuredIndex >= 0) {\n    const itemMetadata = itemMetadataMap[lastMeasuredIndex];\n    totalSizeOfMeasuredItems = itemMetadata.offset + itemMetadata.size;\n  }\n\n  const numUnmeasuredItems = itemCount - lastMeasuredIndex - 1;\n  const totalSizeOfUnmeasuredItems = numUnmeasuredItems * estimatedItemSize;\n\n  return totalSizeOfMeasuredItems + totalSizeOfUnmeasuredItems;\n};\n\nconst VariableSizeList = createListComponent({\n  getItemOffset: (\n    props: Props<any>,\n    index: number,\n    instanceProps: InstanceProps\n  ): number => getItemMetadata(props, index, instanceProps).offset,\n\n  getItemSize: (\n    props: Props<any>,\n    index: number,\n    instanceProps: InstanceProps\n  ): number => instanceProps.itemMetadataMap[index].size,\n\n  getEstimatedTotalSize,\n\n  getOffsetForIndexAndAlignment: (\n    props: Props<any>,\n    index: number,\n    align: ScrollToAlign,\n    scrollOffset: number,\n    instanceProps: InstanceProps\n  ): number => {\n    const { direction, height, layout, width } = props;\n\n    // TODO Deprecate direction \"horizontal\"\n    const isHorizontal = direction === 'horizontal' || layout === 'horizontal';\n    const size = (((isHorizontal ? width : height): any): number);\n    const itemMetadata = getItemMetadata(props, index, instanceProps);\n\n    // Get estimated total size after ItemMetadata is computed,\n    // To ensure it reflects actual measurements instead of just estimates.\n    const estimatedTotalSize = getEstimatedTotalSize(props, instanceProps);\n\n    const maxOffset = Math.max(\n      0,\n      Math.min(estimatedTotalSize - size, itemMetadata.offset)\n    );\n    const minOffset = Math.max(\n      0,\n      itemMetadata.offset - size + itemMetadata.size\n    );\n\n    if (align === 'smart') {\n      if (\n        scrollOffset >= minOffset - size &&\n        scrollOffset <= maxOffset + size\n      ) {\n        align = 'auto';\n      } else {\n        align = 'center';\n      }\n    }\n\n    switch (align) {\n      case 'start':\n        return maxOffset;\n      case 'end':\n        return minOffset;\n      case 'center':\n        return Math.round(minOffset + (maxOffset - minOffset) / 2);\n      case 'auto':\n      default:\n        if (scrollOffset >= minOffset && scrollOffset <= maxOffset) {\n          return scrollOffset;\n        } else if (scrollOffset < minOffset) {\n          return minOffset;\n        } else {\n          return maxOffset;\n        }\n    }\n  },\n\n  getStartIndexForOffset: (\n    props: Props<any>,\n    offset: number,\n    instanceProps: InstanceProps\n  ): number => findNearestItem(props, instanceProps, offset),\n\n  getStopIndexForStartIndex: (\n    props: Props<any>,\n    startIndex: number,\n    scrollOffset: number,\n    instanceProps: InstanceProps\n  ): number => {\n    const { direction, height, itemCount, layout, width } = props;\n\n    // TODO Deprecate direction \"horizontal\"\n    const isHorizontal = direction === 'horizontal' || layout === 'horizontal';\n    const size = (((isHorizontal ? width : height): any): number);\n    const itemMetadata = getItemMetadata(props, startIndex, instanceProps);\n    const maxOffset = scrollOffset + size;\n\n    let offset = itemMetadata.offset + itemMetadata.size;\n    let stopIndex = startIndex;\n\n    while (stopIndex < itemCount - 1 && offset < maxOffset) {\n      stopIndex++;\n      offset += getItemMetadata(props, stopIndex, instanceProps).size;\n    }\n\n    return stopIndex;\n  },\n\n  initInstanceProps(props: Props<any>, instance: any): InstanceProps {\n    const { estimatedItemSize } = ((props: any): VariableSizeProps);\n\n    const instanceProps = {\n      itemMetadataMap: {},\n      estimatedItemSize: estimatedItemSize || DEFAULT_ESTIMATED_ITEM_SIZE,\n      lastMeasuredIndex: -1,\n    };\n\n    instance.resetAfterIndex = (\n      index: number,\n      shouldForceUpdate?: boolean = true\n    ) => {\n      instanceProps.lastMeasuredIndex = Math.min(\n        instanceProps.lastMeasuredIndex,\n        index - 1\n      );\n\n      // We could potentially optimize further by only evicting styles after this index,\n      // But since styles are only cached while scrolling is in progress-\n      // It seems an unnecessary optimization.\n      // It's unlikely that resetAfterIndex() will be called while a user is scrolling.\n      instance._getItemStyleCache(-1);\n\n      if (shouldForceUpdate) {\n        instance.forceUpdate();\n      }\n    };\n\n    return instanceProps;\n  },\n\n  shouldResetStyleCacheOnItemSizeChange: false,\n\n  validateProps: ({ itemSize }: Props<any>): void => {\n    if (process.env.NODE_ENV !== 'production') {\n      if (typeof itemSize !== 'function') {\n        throw Error(\n          'An invalid \"itemSize\" prop has been specified. ' +\n            'Value should be a function. ' +\n            `\"${itemSize === null ? 'null' : typeof itemSize}\" was specified.`\n        );\n      }\n    }\n  },\n});\n\nexport default VariableSizeList;\n", "// @flow\n\nimport createGridComponent from './createGridComponent';\n\nimport type { Pro<PERSON>, ScrollToAlign } from './createGridComponent';\n\nconst FixedSizeGrid = createGridComponent({\n  getColumnOffset: ({ columnWidth }: Props<any>, index: number): number =>\n    index * ((columnWidth: any): number),\n\n  getColumnWidth: ({ columnWidth }: Props<any>, index: number): number =>\n    ((columnWidth: any): number),\n\n  getRowOffset: ({ rowHeight }: Props<any>, index: number): number =>\n    index * ((rowHeight: any): number),\n\n  getRowHeight: ({ rowHeight }: Props<any>, index: number): number =>\n    ((rowHeight: any): number),\n\n  getEstimatedTotalHeight: ({ rowCount, rowHeight }: Props<any>) =>\n    ((rowHeight: any): number) * rowCount,\n\n  getEstimatedTotalWidth: ({ columnCount, columnWidth }: Props<any>) =>\n    ((columnWidth: any): number) * columnCount,\n\n  getOffsetForColumnAndAlignment: (\n    { columnCount, columnWidth, width }: Props<any>,\n    columnIndex: number,\n    align: ScrollToAlign,\n    scrollLeft: number,\n    instanceProps: typeof undefined,\n    scrollbarSize: number\n  ): number => {\n    const lastColumnOffset = Math.max(\n      0,\n      columnCount * ((columnWidth: any): number) - width\n    );\n    const maxOffset = Math.min(\n      lastColumnOffset,\n      columnIndex * ((columnWidth: any): number)\n    );\n    const minOffset = Math.max(\n      0,\n      columnIndex * ((columnWidth: any): number) -\n        width +\n        scrollbarSize +\n        ((columnWidth: any): number)\n    );\n\n    if (align === 'smart') {\n      if (scrollLeft >= minOffset - width && scrollLeft <= maxOffset + width) {\n        align = 'auto';\n      } else {\n        align = 'center';\n      }\n    }\n\n    switch (align) {\n      case 'start':\n        return maxOffset;\n      case 'end':\n        return minOffset;\n      case 'center':\n        // \"Centered\" offset is usually the average of the min and max.\n        // But near the edges of the list, this doesn't hold true.\n        const middleOffset = Math.round(\n          minOffset + (maxOffset - minOffset) / 2\n        );\n        if (middleOffset < Math.ceil(width / 2)) {\n          return 0; // near the beginning\n        } else if (middleOffset > lastColumnOffset + Math.floor(width / 2)) {\n          return lastColumnOffset; // near the end\n        } else {\n          return middleOffset;\n        }\n      case 'auto':\n      default:\n        if (scrollLeft >= minOffset && scrollLeft <= maxOffset) {\n          return scrollLeft;\n        } else if (minOffset > maxOffset) {\n          // Because we only take into account the scrollbar size when calculating minOffset\n          // this value can be larger than maxOffset when at the end of the list\n          return minOffset;\n        } else if (scrollLeft < minOffset) {\n          return minOffset;\n        } else {\n          return maxOffset;\n        }\n    }\n  },\n\n  getOffsetForRowAndAlignment: (\n    { rowHeight, height, rowCount }: Props<any>,\n    rowIndex: number,\n    align: ScrollToAlign,\n    scrollTop: number,\n    instanceProps: typeof undefined,\n    scrollbarSize: number\n  ): number => {\n    const lastRowOffset = Math.max(\n      0,\n      rowCount * ((rowHeight: any): number) - height\n    );\n    const maxOffset = Math.min(\n      lastRowOffset,\n      rowIndex * ((rowHeight: any): number)\n    );\n    const minOffset = Math.max(\n      0,\n      rowIndex * ((rowHeight: any): number) -\n        height +\n        scrollbarSize +\n        ((rowHeight: any): number)\n    );\n\n    if (align === 'smart') {\n      if (scrollTop >= minOffset - height && scrollTop <= maxOffset + height) {\n        align = 'auto';\n      } else {\n        align = 'center';\n      }\n    }\n\n    switch (align) {\n      case 'start':\n        return maxOffset;\n      case 'end':\n        return minOffset;\n      case 'center':\n        // \"Centered\" offset is usually the average of the min and max.\n        // But near the edges of the list, this doesn't hold true.\n        const middleOffset = Math.round(\n          minOffset + (maxOffset - minOffset) / 2\n        );\n        if (middleOffset < Math.ceil(height / 2)) {\n          return 0; // near the beginning\n        } else if (middleOffset > lastRowOffset + Math.floor(height / 2)) {\n          return lastRowOffset; // near the end\n        } else {\n          return middleOffset;\n        }\n      case 'auto':\n      default:\n        if (scrollTop >= minOffset && scrollTop <= maxOffset) {\n          return scrollTop;\n        } else if (minOffset > maxOffset) {\n          // Because we only take into account the scrollbar size when calculating minOffset\n          // this value can be larger than maxOffset when at the end of the list\n          return minOffset;\n        } else if (scrollTop < minOffset) {\n          return minOffset;\n        } else {\n          return maxOffset;\n        }\n    }\n  },\n\n  getColumnStartIndexForOffset: (\n    { columnWidth, columnCount }: Props<any>,\n    scrollLeft: number\n  ): number =>\n    Math.max(\n      0,\n      Math.min(\n        columnCount - 1,\n        Math.floor(scrollLeft / ((columnWidth: any): number))\n      )\n    ),\n\n  getColumnStopIndexForStartIndex: (\n    { columnWidth, columnCount, width }: Props<any>,\n    startIndex: number,\n    scrollLeft: number\n  ): number => {\n    const left = startIndex * ((columnWidth: any): number);\n    const numVisibleColumns = Math.ceil(\n      (width + scrollLeft - left) / ((columnWidth: any): number)\n    );\n    return Math.max(\n      0,\n      Math.min(\n        columnCount - 1,\n        startIndex + numVisibleColumns - 1 // -1 is because stop index is inclusive\n      )\n    );\n  },\n\n  getRowStartIndexForOffset: (\n    { rowHeight, rowCount }: Props<any>,\n    scrollTop: number\n  ): number =>\n    Math.max(\n      0,\n      Math.min(rowCount - 1, Math.floor(scrollTop / ((rowHeight: any): number)))\n    ),\n\n  getRowStopIndexForStartIndex: (\n    { rowHeight, rowCount, height }: Props<any>,\n    startIndex: number,\n    scrollTop: number\n  ): number => {\n    const top = startIndex * ((rowHeight: any): number);\n    const numVisibleRows = Math.ceil(\n      (height + scrollTop - top) / ((rowHeight: any): number)\n    );\n    return Math.max(\n      0,\n      Math.min(\n        rowCount - 1,\n        startIndex + numVisibleRows - 1 // -1 is because stop index is inclusive\n      )\n    );\n  },\n\n  initInstanceProps(props: Props<any>): any {\n    // Noop\n  },\n\n  shouldResetStyleCacheOnItemSizeChange: true,\n\n  validateProps: ({ columnWidth, rowHeight }: Props<any>): void => {\n    if (process.env.NODE_ENV !== 'production') {\n      if (typeof columnWidth !== 'number') {\n        throw Error(\n          'An invalid \"columnWidth\" prop has been specified. ' +\n            'Value should be a number. ' +\n            `\"${\n              columnWidth === null ? 'null' : typeof columnWidth\n            }\" was specified.`\n        );\n      }\n\n      if (typeof rowHeight !== 'number') {\n        throw Error(\n          'An invalid \"rowHeight\" prop has been specified. ' +\n            'Value should be a number. ' +\n            `\"${rowHeight === null ? 'null' : typeof rowHeight}\" was specified.`\n        );\n      }\n    }\n  },\n});\n\nexport default FixedSizeGrid;\n", "// @flow\n\nimport createListComponent from './createListComponent';\n\nimport type { Props, ScrollToAlign } from './createListComponent';\n\nconst FixedSizeList = createListComponent({\n  getItemOffset: ({ itemSize }: Props<any>, index: number): number =>\n    index * ((itemSize: any): number),\n\n  getItemSize: ({ itemSize }: Props<any>, index: number): number =>\n    ((itemSize: any): number),\n\n  getEstimatedTotalSize: ({ itemCount, itemSize }: Props<any>) =>\n    ((itemSize: any): number) * itemCount,\n\n  getOffsetForIndexAndAlignment: (\n    { direction, height, itemCount, itemSize, layout, width }: Props<any>,\n    index: number,\n    align: ScrollToAlign,\n    scrollOffset: number\n  ): number => {\n    // TODO Deprecate direction \"horizontal\"\n    const isHorizontal = direction === 'horizontal' || layout === 'horizontal';\n    const size = (((isHorizontal ? width : height): any): number);\n    const lastItemOffset = Math.max(\n      0,\n      itemCount * ((itemSize: any): number) - size\n    );\n    const maxOffset = Math.min(\n      lastItemOffset,\n      index * ((itemSize: any): number)\n    );\n    const minOffset = Math.max(\n      0,\n      index * ((itemSize: any): number) - size + ((itemSize: any): number)\n    );\n\n    if (align === 'smart') {\n      if (\n        scrollOffset >= minOffset - size &&\n        scrollOffset <= maxOffset + size\n      ) {\n        align = 'auto';\n      } else {\n        align = 'center';\n      }\n    }\n\n    switch (align) {\n      case 'start':\n        return maxOffset;\n      case 'end':\n        return minOffset;\n      case 'center': {\n        // \"Centered\" offset is usually the average of the min and max.\n        // But near the edges of the list, this doesn't hold true.\n        const middleOffset = Math.round(\n          minOffset + (maxOffset - minOffset) / 2\n        );\n        if (middleOffset < Math.ceil(size / 2)) {\n          return 0; // near the beginning\n        } else if (middleOffset > lastItemOffset + Math.floor(size / 2)) {\n          return lastItemOffset; // near the end\n        } else {\n          return middleOffset;\n        }\n      }\n      case 'auto':\n      default:\n        if (scrollOffset >= minOffset && scrollOffset <= maxOffset) {\n          return scrollOffset;\n        } else if (scrollOffset < minOffset) {\n          return minOffset;\n        } else {\n          return maxOffset;\n        }\n    }\n  },\n\n  getStartIndexForOffset: (\n    { itemCount, itemSize }: Props<any>,\n    offset: number\n  ): number =>\n    Math.max(\n      0,\n      Math.min(itemCount - 1, Math.floor(offset / ((itemSize: any): number)))\n    ),\n\n  getStopIndexForStartIndex: (\n    { direction, height, itemCount, itemSize, layout, width }: Props<any>,\n    startIndex: number,\n    scrollOffset: number\n  ): number => {\n    // TODO Deprecate direction \"horizontal\"\n    const isHorizontal = direction === 'horizontal' || layout === 'horizontal';\n    const offset = startIndex * ((itemSize: any): number);\n    const size = (((isHorizontal ? width : height): any): number);\n    const numVisibleItems = Math.ceil(\n      (size + scrollOffset - offset) / ((itemSize: any): number)\n    );\n    return Math.max(\n      0,\n      Math.min(\n        itemCount - 1,\n        startIndex + numVisibleItems - 1 // -1 is because stop index is inclusive\n      )\n    );\n  },\n\n  initInstanceProps(props: Props<any>): any {\n    // Noop\n  },\n\n  shouldResetStyleCacheOnItemSizeChange: true,\n\n  validateProps: ({ itemSize }: Props<any>): void => {\n    if (process.env.NODE_ENV !== 'production') {\n      if (typeof itemSize !== 'number') {\n        throw Error(\n          'An invalid \"itemSize\" prop has been specified. ' +\n            'Value should be a number. ' +\n            `\"${itemSize === null ? 'null' : typeof itemSize}\" was specified.`\n        );\n      }\n    }\n  },\n});\n\nexport default FixedSizeList;\n", "export default function _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n\n  return target;\n}", "// @flow\n\n// Pulled from react-compat\n// https://github.com/developit/preact-compat/blob/7c5de00e7c85e2ffd011bf3af02899b63f699d3a/src/index.js#L349\nexport default function shallowDiffers(prev: Object, next: Object): boolean {\n  for (let attribute in prev) {\n    if (!(attribute in next)) {\n      return true;\n    }\n  }\n  for (let attribute in next) {\n    if (prev[attribute] !== next[attribute]) {\n      return true;\n    }\n  }\n  return false;\n}\n", "// @flow\n\nimport shallowDiffers from './shallowDiffers';\n\n// Custom comparison function for React.memo().\n// It knows to compare individual style props and ignore the wrapper object.\n// See https://reactjs.org/docs/react-api.html#reactmemo\nexport default function areEqual(\n  prevProps: Object,\n  nextProps: Object\n): boolean {\n  const { style: prevStyle, ...prevRest } = prevProps;\n  const { style: nextStyle, ...nextRest } = nextProps;\n\n  return (\n    !shallowDiffers(prevStyle, nextStyle) && !shallowDiffers(prevRest, nextRest)\n  );\n}\n", "// @flow\n\nimport areEqual from './areEqual';\nimport shallowDiffers from './shallowDiffers';\n\n// Custom shouldComponentUpdate for class components.\n// It knows to compare individual style props and ignore the wrapper object.\n// See https://reactjs.org/docs/react-component.html#shouldcomponentupdate\nexport default function shouldComponentUpdate(\n  nextProps: Object,\n  nextState: Object\n): boolean {\n  return (\n    !areEqual(this.props, nextProps) || shallowDiffers(this.state, nextState)\n  );\n}\n"], "names": ["_extends", "Object", "assign", "target", "i", "arguments", "length", "source", "key", "prototype", "hasOwnProperty", "call", "apply", "this", "_inherits<PERSON><PERSON>e", "subClass", "superClass", "create", "constructor", "__proto__", "_assertThisInitialized", "self", "ReferenceError", "simpleIsEqual", "newArgs", "lastArgs", "every", "newArg", "index", "newValue", "oldValue", "resultFn", "isEqual", "lastThis", "lastResult", "calledOnce", "_len", "Array", "_key", "now", "performance", "Date", "cancelTimeout", "timeoutID", "cancelAnimationFrame", "id", "requestTimeout", "callback", "delay", "start", "requestAnimationFrame", "tick", "size", "cachedRTLResult", "getRTLOffsetType", "recalculate", "outerDiv", "document", "createElement", "outerStyle", "style", "width", "height", "overflow", "direction", "innerDiv", "innerStyle", "append<PERSON><PERSON><PERSON>", "body", "scrollLeft", "<PERSON><PERSON><PERSON><PERSON>", "IS_SCROLLING_DEBOUNCE_INTERVAL", "defaultItemKey", "columnIndex", "data", "rowIndex", "devWarningsOverscanCount", "devWarningsOverscanRowsColumnsCount", "devWarningsTagName", "createGridComponent", "getColumnOffset", "getColumnStartIndexForOffset", "getColumnStopIndexForStartIndex", "getColumnWidth", "getEstimatedTotalHeight", "getEstimatedTotalWidth", "getOffsetForColumnAndAlignment", "getOffsetForRowAndAlignment", "getRowHeight", "getRowOffset", "getRowStartIndexForOffset", "getRowStopIndexForStartIndex", "initInstanceProps", "shouldResetStyleCacheOnItemSizeChange", "validateProps", "props", "_instanceProps", "_this", "_resetIsScrollingTimeoutId", "_outerRef", "state", "instance", "isScrolling", "horizontalScrollDirection", "initialScrollLeft", "scrollTop", "initialScrollTop", "scrollUpdateWasRequested", "verticalScrollDirection", "_callOnItemsRendered", "memoizeOne", "overscanColumnStartIndex", "overscanColumnStopIndex", "overscanRowStartIndex", "overscanRowStopIndex", "visibleColumnStartIndex", "visibleColumnStopIndex", "visibleRowStartIndex", "visibleRowStopIndex", "onItemsRendered", "_callOnScroll", "onScroll", "_getItemStyle", "columnWidth", "rowHeight", "itemStyleCache", "_getItemStyleCache", "offset", "isRtl", "position", "left", "undefined", "right", "top", "_", "__", "___", "_onScroll", "event", "currentTarget", "clientHeight", "clientWidth", "scrollHeight", "scrollWidth", "setState", "prevState", "calculatedScrollLeft", "Math", "max", "min", "calculatedScrollTop", "_resetIsScrollingDebounced", "_outerRefSetter", "ref", "outerRef", "current", "_resetIsScrolling", "getDerivedStateFromProps", "nextProps", "validateSharedProps", "scrollTo", "scrollToItem", "align", "columnCount", "rowCount", "scrollbarSize", "div", "offsetWidth", "getScrollbarSize", "estimatedTotalHeight", "horizontalScrollbarSize", "verticalScrollbarSize", "componentDidMount", "_callPropsCallbacks", "componentDidUpdate", "componentWillUnmount", "render", "children", "className", "innerRef", "innerElementType", "innerTagName", "itemData", "itemKey", "outerElementType", "outerTagName", "useIsScrolling", "_getHorizontalRangeToRender", "columnStartIndex", "columnStopIndex", "_getVerticalRangeToRender", "rowStartIndex", "rowStopIndex", "items", "push", "estimatedTotalWidth", "WebkitOverflowScrolling", "<PERSON><PERSON><PERSON><PERSON>", "pointerEvents", "overscanColumnCount", "overscanColumnsCount", "overscanCount", "overscanCountResolved", "startIndex", "stopIndex", "overscanBackward", "overscanForward", "overscanRowCount", "overscanRowsCount", "PureComponent", "defaultProps", "window", "WeakSet", "has", "add", "console", "warn", "Error", "rowMetadataMap", "estimatedRowHeight", "lastMeasuredRowIndex", "totalSizeOfMeasuredRows", "itemMetadata", "columnMetadataMap", "estimatedColumnWidth", "lastMeasuredColumnIndex", "getItemMetadata", "itemType", "instanceProps", "itemMetadataMap", "itemSize", "lastMeasuredIndex", "findNearestItem", "findNearestItemBinarySearch", "findNearestItemExponentialSearch", "high", "low", "middle", "floor", "currentOffset", "itemCount", "interval", "getOffsetForIndexAndAlignment", "scrollOffset", "estimatedTotalSize", "maxOffset", "minOffset", "round", "VariableSizeGrid", "resetAfterColumnIndex", "shouldForceUpdate", "resetAfterIndices", "resetAfterRowIndex", "forceUpdate", "devWarningsDirection", "createListComponent", "getItemOffset", "getEstimatedTotalSize", "getItemSize", "getStartIndexForOffset", "getStopIndexForStartIndex", "scrollDirection", "initialScrollOffset", "overscanStartIndex", "overscanStopIndex", "visibleStartIndex", "visibleStopIndex", "layout", "isHorizontal", "offsetHorizontal", "_onScrollHorizontal", "_onScrollVertical", "_getRangeToRender", "estimatedItemSize", "totalSizeOfMeasuredItems", "VariableSizeList", "resetAfterIndex", "FixedSizeGrid", "lastColumnOffset", "middleOffset", "ceil", "lastRowOffset", "numVisibleColumns", "numVisibleRows", "FixedSizeList", "lastItemOffset", "numVisibleItems", "_objectWithoutPropertiesLoose", "excluded", "sourceKeys", "keys", "indexOf", "shallow<PERSON>iffers", "prev", "next", "attribute", "areEqual", "prevProps", "prevStyle", "prevRest", "nextStyle", "nextRest", "nextState"], "mappings": "2OAAe,SAASA,WACtBA,EAAWC,OAAOC,QAAU,SAAUC,OAC/B,IAAIC,EAAI,EAAGA,EAAIC,UAAUC,OAAQF,IAAK,KACrCG,EAASF,UAAUD,OAElB,IAAII,KAAOD,EACVN,OAAOQ,UAAUC,eAAeC,KAAKJ,EAAQC,KAC/CL,EAAOK,GAAOD,EAAOC,WAKpBL,IAGOS,MAAMC,KAAMR,WCff,SAASS,EAAeC,EAAUC,GAC/CD,EAASN,UAAYR,OAAOgB,OAAOD,EAAWP,WAC9CM,EAASN,UAAUS,YAAcH,EACjCA,EAASI,UAAYH,ECHR,SAASI,EAAuBC,WAChC,IAATA,QACI,IAAIC,eAAe,oEAGpBD,ECLT,IAIIE,EAAgB,SAAuBC,EAASC,UAC3CD,EAAQlB,SAAWmB,EAASnB,QAAUkB,EAAQE,MAAM,SAAUC,EAAQC,UALtCC,EAMjBF,EAN2BG,EAMnBL,EAASG,GALhCC,IAAaC,EADH,IAAsBD,EAAUC,KAUnD,SAASF,EAAOG,EAAUC,OAKpBC,OAJY,IAAZD,IACFA,EAAUT,OAKRW,EADAT,EAAW,GAEXU,GAAa,SAEJ,eACN,IAAIC,EAAO/B,UAAUC,OAAQkB,EAAU,IAAIa,MAAMD,GAAOE,EAAO,EAAGA,EAAOF,EAAME,IAClFd,EAAQc,GAAQjC,UAAUiC,UAGxBH,GAAcF,IAAapB,MAAQmB,EAAQR,EAASC,GAC/CS,GAGTA,EAAaH,EAASnB,MAAMC,KAAMW,GAClCW,GAAa,EACbF,EAAWpB,KACXY,EAAWD,EACJU,IC5BX,IAGMK,EAFmB,iBAAhBC,aAAuD,mBAApBA,YAAYD,IAGpD,kBAAMC,YAAYD,OAClB,kBAAME,KAAKF,OAMR,SAASG,EAAcC,GAC5BC,qBAAqBD,EAAUE,IAG1B,SAASC,EAAeC,EAAoBC,OAC3CC,EAAQV,QAURI,EAAuB,CAC3BE,GAAIK,+BATGC,IACHZ,IAAQU,GAASD,EACnBD,EAASpC,KAAK,MAEdgC,EAAUE,GAAKK,sBAAsBC,aAQlCR,ECjCT,IAAIS,GAAgB,EA0BpB,IAAIC,EAAwC,KAQrC,SAASC,EAAiBC,eAAAA,IAAAA,GAAwB,GAC/B,OAApBF,GAA4BE,EAAa,KACrCC,EAAWC,SAASC,cAAc,OAClCC,EAAaH,EAASI,MAC5BD,EAAWE,MAAQ,OACnBF,EAAWG,OAAS,OACpBH,EAAWI,SAAW,SACtBJ,EAAWK,UAAY,UAEjBC,EAAWR,SAASC,cAAc,OAClCQ,EAAaD,EAASL,aAC5BM,EAAWL,MAAQ,QACnBK,EAAWJ,OAAS,QAEpBN,EAASW,YAAYF,GAEnBR,SAASW,KAA6BD,YAAYX,GAEhDA,EAASa,WAAa,EACxBhB,EAAkB,uBAElBG,EAASa,WAAa,EAEpBhB,EAD0B,IAAxBG,EAASa,WACO,WAEA,sBAIpBZ,SAASW,KAA6BE,YAAYd,GAE7CH,SAGFA,ECwET,IAAMkB,EAAiC,IAEjCC,EAAiB,gBAAGC,IAAAA,cAAaC,cAAMC,aAC5BF,GAIbG,EAA2B,KAC3BC,EAAsC,KACtCC,EAAqB,KASV,SAASC,aACtBC,IAAAA,gBACAC,IAAAA,6BACAC,IAAAA,gCACAC,IAAAA,eACAC,IAAAA,wBACAC,IAAAA,uBACAC,IAAAA,+BACAC,IAAAA,4BACAC,IAAAA,aACAC,IAAAA,aACAC,IAAAA,0BACAC,IAAAA,6BACAC,IAAAA,kBACAC,IAAAA,sCACAC,IAAAA,gDAgDcC,8BACJA,UA9BRC,eAAsBJ,EAAkBK,EAAKF,iBAC7CG,2BAA+C,OAC/CC,mBAQAC,MAAe,CACbC,iBACAC,aAAa,EACbC,0BAA2B,UAC3BlC,WAC0C,iBAAjC4B,EAAKF,MAAMS,kBACdP,EAAKF,MAAMS,kBACX,EACNC,UACyC,iBAAhCR,EAAKF,MAAMW,iBACdT,EAAKF,MAAMW,iBACX,EACNC,0BAA0B,EAC1BC,wBAAyB,aA8Q3BC,8BAUAA,qBAAuBC,EACrB,SACEC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,UAEErB,EAAKF,MAAMwB,gBAAgD,CAC3DR,yBAAAA,EACAC,wBAAAA,EACAC,sBAAAA,EACAC,qBAAAA,EACAC,wBAAAA,EACAC,uBAAAA,EACAC,qBAAAA,EACAC,oBAAAA,QAINE,uBAOAA,cAAgBV,EACd,SACEzC,EACAoC,EACAF,EACAK,EACAD,UAEEV,EAAKF,MAAM0B,SAAkC,CAC7ClB,0BAAAA,EACAlC,WAAAA,EACAoC,UAAAA,EACAG,wBAAAA,EACAD,yBAAAA,QAwDNe,uBACAA,cAAgB,SAAC/C,EAAkBF,OAW7Bb,IAV0CqC,EAAKF,MAA3C4B,IAAAA,YAAa3D,IAAAA,UAAW4D,IAAAA,UAE1BC,EAAiB5B,EAAK6B,mBAC1BjC,GAAyC8B,EACzC9B,GAAyC7B,EACzC6B,GAAyC+B,GAGrCpH,EAASmE,MAAYF,KAGvBoD,EAAenH,eAAeF,GAChCoD,EAAQiE,EAAerH,OAClB,KACCuH,EAAS/C,EACbiB,EAAKF,MACLtB,EACAwB,EAAKD,gBAEDgC,EAAsB,QAAdhE,EACd6D,EAAerH,GAAOoD,EAAQ,CAC5BqE,SAAU,WACVC,KAAMF,OAAQG,EAAYJ,EAC1BK,MAAOJ,EAAQD,OAASI,EACxBE,IAAK5C,EAAaQ,EAAKF,MAAOpB,EAAUsB,EAAKD,gBAC7ClC,OAAQ0B,EAAaS,EAAKF,MAAOpB,EAAUsB,EAAKD,gBAChDnC,MAAOsB,EAAec,EAAKF,MAAOtB,EAAawB,EAAKD,wBAIjDpC,KAGTkE,4BACAA,mBAAqBhB,EAAW,SAACwB,EAAQC,EAASC,SAAc,OAkGhEC,UAAY,SAACC,SAQPA,EAAMC,cANRC,IAAAA,aACAC,IAAAA,YACAxE,IAAAA,WACAoC,IAAAA,UACAqC,IAAAA,aACAC,IAAAA,cAEGC,SAAS,SAAAC,MAEVA,EAAU5E,aAAeA,GACzB4E,EAAUxC,YAAcA,SAKjB,SAGDzC,EAAciC,EAAKF,MAAnB/B,UAMJkF,EAAuB7E,KACT,QAAdL,SACMV,SACD,WACH4F,GAAwB7E,YAErB,sBACH6E,EAAuBH,EAAcF,EAAcxE,EAMzD6E,EAAuBC,KAAKC,IAC1B,EACAD,KAAKE,IAAIH,EAAsBH,EAAcF,QAEzCS,EAAsBH,KAAKC,IAC/B,EACAD,KAAKE,IAAI5C,EAAWqC,EAAeF,UAG9B,CACLtC,aAAa,EACbC,0BACE0C,EAAU5E,WAAaA,EAAa,UAAY,WAClDA,WAAY6E,EACZzC,UAAW6C,EACX1C,wBACEqC,EAAUxC,UAAYA,EAAY,UAAY,WAChDE,0BAA0B,IAE3BV,EAAKsD,+BAGVC,gBAAkB,SAACC,OACTC,EAAazD,EAAKF,MAAlB2D,WAEHvD,UAAcsD,EAEK,mBAAbC,EACTA,EAASD,GAEG,MAAZC,GACoB,iBAAbA,GACPA,EAAShJ,eAAe,aAExBgJ,EAASC,QAAUF,MAIvBF,2BAA6B,WACa,OAApCtD,EAAKC,4BACPxD,EAAcuD,EAAKC,8BAGhBA,2BAA6BpD,EAChCmD,EAAK2D,kBACLrF,MAIJqF,kBAAoB,aACb1D,2BAA6B,OAE7B8C,SAAS,CAAE1C,aAAa,GAAS,aAG/BwB,oBAAoB,iBArlBtB+B,kCACLC,EACAb,UAEAc,EAAoBD,EAAWb,GAC/BnD,EAAcgE,GACP,iCAGTE,yBACE3F,IAAAA,WACAoC,IAAAA,eAKmB0B,IAAf9D,IACFA,EAAa8E,KAAKC,IAAI,EAAG/E,SAET8D,IAAd1B,IACFA,EAAY0C,KAAKC,IAAI,EAAG3C,SAGrBuC,SAAS,SAAAC,eACOd,IAAf9D,IACFA,EAAa4E,EAAU5E,iBAEP8D,IAAd1B,IACFA,EAAYwC,EAAUxC,WAItBwC,EAAU5E,aAAeA,GACzB4E,EAAUxC,YAAcA,EAEjB,KAGF,CACLF,0BACE0C,EAAU5E,WAAaA,EAAa,UAAY,WAClDA,WAAYA,EACZoC,UAAWA,EACXE,0BAA0B,EAC1BC,wBACEqC,EAAUxC,UAAYA,EAAY,UAAY,aAEjD5F,KAAK0I,+BAGVU,iCACEC,MAAAA,aAAQ,SACRzF,IAAAA,YACAE,IAAAA,WAMiD9D,KAAKkF,MAA9CoE,IAAAA,YAAarG,IAAAA,OAAQsG,IAAAA,SAAUvG,IAAAA,QACLhD,KAAKuF,MAA/B/B,IAAAA,WAAYoC,IAAAA,UACd4D,ED3RL,SAA0B9G,eAAAA,IAAAA,GAAwB,IACzC,IAAVH,GAAeG,EAAa,KACxB+G,EAAM7G,SAASC,cAAc,OAC7BE,EAAQ0G,EAAI1G,MAClBA,EAAMC,MAAQ,OACdD,EAAME,OAAS,OACfF,EAAMG,SAAW,SAEfN,SAASW,KAA6BD,YAAYmG,GAEpDlH,EAAOkH,EAAIC,YAAcD,EAAIzB,YAE3BpF,SAASW,KAA6BE,YAAYgG,UAG/ClH,EC4QmBoH,QAEFrC,IAAhB1D,IACFA,EAAc0E,KAAKC,IAAI,EAAGD,KAAKE,IAAI5E,EAAa0F,EAAc,UAE/ChC,IAAbxD,IACFA,EAAWwE,KAAKC,IAAI,EAAGD,KAAKE,IAAI1E,EAAUyF,EAAW,SAGjDK,EAAuBrF,EAC3BvE,KAAKkF,MACLlF,KAAKmF,gBAUD0E,EARsBrF,EAC1BxE,KAAKkF,MACLlF,KAAKmF,gBAOiBnC,EAAQwG,EAAgB,EAC1CM,EACJF,EAAuB3G,EAASuG,EAAgB,OAE7CL,SAAS,CACZ3F,gBACkB8D,IAAhB1D,EACIa,EACEzE,KAAKkF,MACLtB,EACAyF,EACA7F,EACAxD,KAAKmF,eACL2E,GAEFtG,EACNoC,eACe0B,IAAbxD,EACIY,EACE1E,KAAKkF,MACLpB,EACAuF,EACAzD,EACA5F,KAAKmF,eACL0E,GAEFjE,OAIVmE,mCACkD/J,KAAKkF,MAA7CS,IAAAA,kBAAmBE,IAAAA,oBAEL,MAAlB7F,KAAKsF,UAAmB,KACpBuD,EAAa7I,KAAKsF,UACS,iBAAtBK,IACTkD,EAASrF,WAAamC,GAEQ,iBAArBE,IACTgD,EAASjD,UAAYC,QAIpBmE,yBAGPC,kCACU9G,EAAcnD,KAAKkF,MAAnB/B,YACoDnD,KAAKuF,MAAzD/B,IAAAA,WAAYoC,IAAAA,eAAWE,0BAEmB,MAAlB9F,KAAKsF,UAAmB,KAIhDuD,EAAa7I,KAAKsF,aACN,QAAdnC,SACMV,SACD,WACHoG,EAASrF,YAAcA,YAEpB,qBACHqF,EAASrF,WAAaA,oBAGdwE,EAA6Ba,EAA7Bb,YAAaE,EAAgBW,EAAhBX,YACrBW,EAASrF,WAAa0E,EAAcF,EAAcxE,OAItDqF,EAASrF,WAAa8E,KAAKC,IAAI,EAAG/E,GAGpCqF,EAASjD,UAAY0C,KAAKC,IAAI,EAAG3C,QAG9BoE,yBAGPE,gCAC0C,OAApClK,KAAKqF,4BACPxD,EAAc7B,KAAKqF,+BAIvB8E,wBAkBMnK,KAAKkF,MAhBPkF,IAAAA,SACAC,IAAAA,UACAf,IAAAA,YACAnG,IAAAA,UACAF,IAAAA,OACAqH,IAAAA,SACAC,IAAAA,iBACAC,IAAAA,aACAC,IAAAA,aACAC,QAAAA,aAAU/G,IACVgH,IAAAA,iBACAC,IAAAA,aACArB,IAAAA,SACAxG,IAAAA,MACA8H,IAAAA,eACA7H,IAAAA,MAEMyC,EAAgBzF,KAAKuF,MAArBE,cAKJzF,KAAK8K,8BAFPC,OACAC,SAEoChL,KAAKiL,4BAApCC,OAAeC,OAEhBC,EAAQ,MACV9B,EAAc,GAAKC,MAEnB,IAAIzF,EAAWoH,EACfpH,GAAYqH,EACZrH,QAGE,IAAIF,EAAcmH,EAClBnH,GAAeoH,EACfpH,IAEAwH,EAAMC,KACJxI,gBAAcuH,EAAU,CACtBxG,YAAAA,EACAC,KAAM4G,EACNhF,YAAaoF,EAAiBpF,OAAc6B,EAC5C3H,IAAK+K,EAAQ,CAAE9G,YAAAA,EAAaC,KAAM4G,EAAU3G,SAAAA,IAC5CA,SAAAA,EACAf,MAAO/C,KAAK6G,cAAc/C,EAAUF,UASxCgG,EAAuBrF,EAC3BvE,KAAKkF,MACLlF,KAAKmF,gBAEDmG,EAAsB9G,EAC1BxE,KAAKkF,MACLlF,KAAKmF,uBAGAtC,gBACL8H,GAAoBC,GAAgB,MACpC,CACEP,UAAAA,EACAzD,SAAU5G,KAAK4H,UACfgB,IAAK5I,KAAK2I,gBACV5F,SACEqE,SAAU,WACVnE,OAAAA,EACAD,MAAAA,EACAE,SAAU,OACVqI,wBAAyB,QACzBC,WAAY,YACZrI,UAAAA,GACGJ,IAGPF,gBAAc0H,GAAoBC,GAAgB,MAAO,CACvDJ,SAAUgB,EACVxC,IAAK0B,EACLvH,MAAO,CACLE,OAAQ2G,EACR6B,cAAehG,EAAc,YAAS6B,EACtCtE,MAAOsI,SA+DftB,qCAC+DhK,KAAKkF,MAA1DoE,IAAAA,YAAa5C,IAAAA,gBAAiBE,IAAAA,SAAU2C,IAAAA,YAEjB,mBAApB7C,GACL4C,EAAc,GAAKC,EAAW,EAAG,OAM/BvJ,KAAK8K,8BAJP5E,OACAC,OACAG,OACAC,SAOEvG,KAAKiL,4BAJP7E,OACAC,OACAG,OACAC,YAEGT,qBACHE,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,MAKkB,mBAAbG,EAAyB,OAO9B5G,KAAKuF,MALPG,IAAAA,0BACAlC,IAAAA,WACAoC,IAAAA,UACAE,IAAAA,yBACAC,IAAAA,6BAEGY,cACHnD,EACAoC,EACAF,EACAK,EACAD,OA+CNgF,6CAOM9K,KAAKkF,MALPoE,IAAAA,YACAoC,IAAAA,oBACAC,IAAAA,qBACAC,IAAAA,cACArC,IAAAA,WAE6DvJ,KAAKuF,MAA5DG,IAAAA,0BAA2BD,IAAAA,YAAajC,IAAAA,WAE1CqI,EACJH,GAAuBC,GAAwBC,GAAiB,KAE9C,IAAhBtC,GAAkC,IAAbC,QAChB,CAAC,EAAG,EAAG,EAAG,OAGbuC,EAAa1H,EACjBpE,KAAKkF,MACL1B,EACAxD,KAAKmF,gBAED4G,EAAY1H,EAChBrE,KAAKkF,MACL4G,EACAtI,EACAxD,KAAKmF,gBAKD6G,EACHvG,GAA6C,aAA9BC,EAEZ,EADA4C,KAAKC,IAAI,EAAGsD,GAEZI,EACHxG,GAA6C,YAA9BC,EAEZ,EADA4C,KAAKC,IAAI,EAAGsD,SAGX,CACLvD,KAAKC,IAAI,EAAGuD,EAAaE,GACzB1D,KAAKC,IAAI,EAAGD,KAAKE,IAAIc,EAAc,EAAGyC,EAAYE,IAClDH,EACAC,MAIJd,2CAOMjL,KAAKkF,MALPoE,IAAAA,YACAsC,IAAAA,cACAM,IAAAA,iBACAC,IAAAA,kBACA5C,IAAAA,WAE0DvJ,KAAKuF,MAAzDE,IAAAA,YAAaM,IAAAA,wBAAyBH,IAAAA,UAExCiG,EACJK,GAAoBC,GAAqBP,GAAiB,KAExC,IAAhBtC,GAAkC,IAAbC,QAChB,CAAC,EAAG,EAAG,EAAG,OAGbuC,EAAajH,EACjB7E,KAAKkF,MACLU,EACA5F,KAAKmF,gBAED4G,EAAYjH,EAChB9E,KAAKkF,MACL4G,EACAlG,EACA5F,KAAKmF,gBAKD6G,EACHvG,GAA2C,aAA5BM,EAEZ,EADAuC,KAAKC,IAAI,EAAGsD,GAEZI,EACHxG,GAA2C,YAA5BM,EAEZ,EADAuC,KAAKC,IAAI,EAAGsD,SAGX,CACLvD,KAAKC,IAAI,EAAGuD,EAAaE,GACzB1D,KAAKC,IAAI,EAAGD,KAAKE,IAAIe,EAAW,EAAGwC,EAAYE,IAC/CH,EACAC,OArhBuBK,mBAKpBC,aAAe,CACpBlJ,UAAW,MACXsH,cAAUnD,EACVuD,gBAAgB,KAhDE,oBAAXyB,aAAoD,IAAnBA,OAAOC,UACjDxI,MAA+BwI,QAC/BvI,MAA0CuI,QAC1CtI,MAAyBsI,SAkqB7B,IAAMrD,EAAsB,kBAExBkB,IAAAA,SACAjH,IAAAA,UACAF,IAAAA,OACAuH,IAAAA,aACAI,IAAAA,aACAe,IAAAA,qBACAC,IAAAA,cACAO,IAAAA,kBACAnJ,IAAAA,MAEAwC,IAAAA,YAG6B,iBAAlBoG,GACL7H,IAA6BA,EAAyByI,IAAIhH,KAC5DzB,EAAyB0I,IAAIjH,GAC7BkH,QAAQC,KACN,uHAO4B,iBAAzBhB,GACsB,iBAAtBQ,GAGLnI,IACCA,EAAoCwI,IAAIhH,KAEzCxB,EAAoCyI,IAAIjH,GACxCkH,QAAQC,KACN,sJAMc,MAAhBnC,GAAwC,MAAhBI,GACtB3G,IAAuBA,EAAmBuI,IAAIhH,KAChDvB,EAAmBwI,IAAIjH,GACvBkH,QAAQC,KACN,sIAMU,MAAZvC,QACIwC,MACJ,uFAEmB,OAAbxC,EAAoB,cAAgBA,8BAItCjH,OACD,UACA,0BAIGyJ,MACJ,2FAEMzJ,yBAIS,iBAAVH,QACH4J,MACJ,wFAEgB,OAAV5J,EAAiB,cAAgBA,0BAIrB,iBAAXC,QACH2J,MACJ,0FAEiB,OAAX3J,EAAkB,cAAgBA,wBCn3B1CsB,EAA0B,kBAC5BgF,IAAAA,SACAsD,IAAAA,eAAgBC,IAAAA,mBAAoBC,IAAAA,qBAElCC,EAA0B,KAI1BD,GAAwBxD,IAC1BwD,EAAuBxD,EAAW,GAGhCwD,GAAwB,EAAG,KACvBE,EAAeJ,EAAeE,GACpCC,EAA0BC,EAAa/F,OAAS+F,EAAa1K,YAMxDyK,GAHoBzD,EAAWwD,EAAuB,GACLD,GAKpDtI,EAAyB,kBAC3B8E,IAAAA,YAEA4D,IAAAA,kBACAC,IAAAA,qBACAC,IAAAA,wBAGEJ,EAA0B,KAI1BI,GAA2B9D,IAC7B8D,EAA0B9D,EAAc,GAGtC8D,GAA2B,EAAG,KAC1BH,EAAeC,EAAkBE,GACvCJ,EAA0BC,EAAa/F,OAAS+F,EAAa1K,YAMxDyK,GAHoB1D,EAAc8D,EAA0B,GACXD,GAKpDE,EAAkB,SACtBC,EACApI,EACAnE,EACAwM,OAEIC,EAAiBC,EAAUC,KACd,WAAbJ,GACFE,EAAkBD,EAAcL,kBAChCO,EAAavI,EAAM4B,YACnB4G,EAAoBH,EAAcH,0BAElCI,EAAkBD,EAAcV,eAChCY,EAAavI,EAAM6B,UACnB2G,EAAoBH,EAAcR,sBAGhChM,EAAQ2M,EAAmB,KACzBxG,EAAS,KACTwG,GAAqB,EAAG,KACpBT,EAAeO,EAAgBE,GACrCxG,EAAS+F,EAAa/F,OAAS+F,EAAa1K,SAGzC,IAAIhD,EAAImO,EAAoB,EAAGnO,GAAKwB,EAAOxB,IAAK,KAC/CgD,EAAOkL,EAASlO,GAEpBiO,EAAgBjO,GAAK,CACnB2H,OAAAA,EACA3E,KAAAA,GAGF2E,GAAU3E,EAGK,WAAb+K,EACFC,EAAcH,wBAA0BrM,EAExCwM,EAAcR,qBAAuBhM,SAIlCyM,EAAgBzM,IAGnB4M,EAAkB,SACtBL,EACApI,EACAqI,EACArG,OAEIsG,EAAiBE,QACJ,WAAbJ,GACFE,EAAkBD,EAAcL,kBAChCQ,EAAoBH,EAAcH,0BAElCI,EAAkBD,EAAcV,eAChCa,EAAoBH,EAAcR,uBAIlCW,EAAoB,EAAIF,EAAgBE,GAAmBxG,OAAS,IAExCA,EAErB0G,EACLN,EACApI,EACAqI,EACAG,EACA,EACAxG,GAMK2G,EACLP,EACApI,EACAqI,EACAjF,KAAKC,IAAI,EAAGmF,GACZxG,IAKA0G,EAA8B,SAClCN,EACApI,EACAqI,EACAO,EACAC,EACA7G,QAEO6G,GAAOD,GAAM,KACZE,EAASD,EAAMzF,KAAK2F,OAAOH,EAAOC,GAAO,GACzCG,EAAgBb,EACpBC,EACApI,EACA8I,EACAT,GACArG,UAEEgH,IAAkBhH,SACb8G,EACEE,EAAgBhH,EACzB6G,EAAMC,EAAS,EACNE,EAAgBhH,IACzB4G,EAAOE,EAAS,UAIhBD,EAAM,EACDA,EAAM,EAEN,GAILF,EAAmC,SACvCP,EACApI,EACAqI,EACAxM,EACAmG,WAEMiH,EAAyB,WAAbb,EAAwBpI,EAAMoE,YAAcpE,EAAMqE,SAChE6E,EAAW,EAGbrN,EAAQoN,GACRd,EAAgBC,EAAUpI,EAAOnE,EAAOwM,GAAerG,OAASA,GAEhEnG,GAASqN,EACTA,GAAY,SAGPR,EACLN,EACApI,EACAqI,EACAjF,KAAKE,IAAIzH,EAAOoN,EAAY,GAC5B7F,KAAK2F,MAAMlN,EAAQ,GACnBmG,IAIEmH,EAAgC,SACpCf,EACApI,EACAnE,EACAsI,EACAiF,EACAf,EACA/D,OAEMjH,EAAoB,WAAb+K,EAAwBpI,EAAMlC,MAAQkC,EAAMjC,OACnDgK,EAAeI,EAAgBC,EAAUpI,EAAOnE,EAAOwM,GAIvDgB,EACS,WAAbjB,EACI9I,EAAuBU,EAAOqI,GAC9BhJ,EAAwBW,EAAOqI,GAE/BiB,EAAYlG,KAAKC,IACrB,EACAD,KAAKE,IAAI+F,EAAqBhM,EAAM0K,EAAa/F,SAE7CuH,EAAYnG,KAAKC,IACrB,EACA0E,EAAa/F,OAAS3E,EAAOiH,EAAgByD,EAAa1K,aAG9C,UAAV8G,IAEAA,EADEiF,GAAgBG,EAAYlM,GAAQ+L,GAAgBE,EAAYjM,EAC1D,OAEA,UAIJ8G,OACD,eACImF,MACJ,aACIC,MACJ,gBACInG,KAAKoG,MAAMD,GAAaD,EAAYC,GAAa,OACrD,sBAECH,GAAgBG,GAAaH,GAAgBE,EACxCF,EACEG,EAAYD,EAGdC,EACEH,EAAeG,EACjBA,EAEAD,IAKTG,EAAmBzK,EAAoB,CAC3CC,gBAAiB,SACfe,EACAnE,EACAwM,UACWF,EAAgB,SAAUnI,EAAOnE,EAAOwM,GAAerG,QAEpE9C,6BAA8B,SAC5Bc,EACA1B,EACA+J,UACWI,EAAgB,SAAUzI,EAAOqI,EAAe/J,IAE7Da,gCAAiC,SAC/Ba,EACA4G,EACAtI,EACA+J,WAEQjE,EAAuBpE,EAAvBoE,YAAatG,EAAUkC,EAAVlC,MAEfiK,EAAeI,EACnB,SACAnI,EACA4G,EACAyB,GAEIiB,EAAYhL,EAAaR,EAE3BkE,EAAS+F,EAAa/F,OAAS+F,EAAa1K,KAC5CwJ,EAAYD,EAETC,EAAYzC,EAAc,GAAKpC,EAASsH,GAE7CtH,GAAUmG,EAAgB,SAAUnI,IADpC6G,EACsDwB,GAAehL,YAGhEwJ,GAGTzH,eAAgB,SACdY,EACAnE,EACAwM,UACWA,EAAcL,kBAAkBnM,GAAOwB,MAEpDgC,wBAAAA,EACAC,uBAAAA,EAEAC,+BAAgC,SAC9BS,EACAnE,EACAsI,EACAiF,EACAf,EACA/D,UAEA6E,EACE,SACAnJ,EACAnE,EACAsI,EACAiF,EACAf,EACA/D,IAGJ9E,4BAA6B,SAC3BQ,EACAnE,EACAsI,EACAiF,EACAf,EACA/D,UAEA6E,EACE,MACAnJ,EACAnE,EACAsI,EACAiF,EACAf,EACA/D,IAGJ5E,aAAc,SACZM,EACAnE,EACAwM,UACWF,EAAgB,MAAOnI,EAAOnE,EAAOwM,GAAerG,QAEjEvC,aAAc,SACZO,EACAnE,EACAwM,UACWA,EAAcV,eAAe9L,GAAOwB,MAEjDsC,0BAA2B,SACzBK,EACAU,EACA2H,UACWI,EAAgB,MAAOzI,EAAOqI,EAAe3H,IAE1Dd,6BAA8B,SAC5BI,EACA4G,EACAlG,EACA2H,WAEQhE,EAAqBrE,EAArBqE,SAAUtG,EAAWiC,EAAXjC,OAEZgK,EAAeI,EACnB,MACAnI,EACA4G,EACAyB,GAEIiB,EAAY5I,EAAY3C,EAE1BiE,EAAS+F,EAAa/F,OAAS+F,EAAa1K,KAC5CwJ,EAAYD,EAETC,EAAYxC,EAAW,GAAKrC,EAASsH,GAE1CtH,GAAUmG,EAAgB,MAAOnI,IADjC6G,EACmDwB,GAAehL,YAG7DwJ,GAGThH,2BAAkBG,EAAmBM,SAI7BN,EAEAqI,EAAgB,CACpBL,kBAAmB,GACnBC,uBANAA,sBA9Z8B,GAqa9BL,qBANAA,oBA/Z8B,GAsa9BM,yBAA0B,EAC1BL,sBAAuB,EACvBF,eAAgB,WAGlBrH,EAASoJ,sBAAwB,SAC/BhL,EACAiL,YAAAA,IAAAA,GAA8B,GAE9BrJ,EAASsJ,kBAAkB,CAAElL,YAAAA,EAAaiL,kBAAAA,KAG5CrJ,EAASuJ,mBAAqB,SAC5BjL,EACA+K,YAAAA,IAAAA,GAA8B,GAE9BrJ,EAASsJ,kBAAkB,CAAEhL,SAAAA,EAAU+K,kBAAAA,KAGzCrJ,EAASsJ,kBAAoB,gBAC3BlL,IAAAA,YACAE,IAAAA,aACA+K,kBAAAA,gBAM2B,iBAAhBjL,IACT2J,EAAcH,wBAA0B9E,KAAKE,IAC3C+E,EAAcH,wBACdxJ,EAAc,IAGM,iBAAbE,IACTyJ,EAAcR,qBAAuBzE,KAAKE,IACxC+E,EAAcR,qBACdjJ,EAAW,IAQf0B,EAASyB,oBAAoB,GAEzB4H,GACFrJ,EAASwJ,eAINzB,GAGTvI,uCAAuC,EAEvCC,cAAe,gBAAG6B,IAAAA,YAAaC,IAAAA,aAEA,mBAAhBD,QACH8F,MACJ,mFAGoB,OAAhB9F,EAAuB,cAAgBA,uBAGxC,GAAyB,mBAAdC,QACV6F,MACJ,iFAEoB,OAAd7F,EAAqB,cAAgBA,0BCzX/CrD,EAAiC,IAEjCC,EAAiB,SAAC5C,EAAe8C,UAAc9C,GAIjDkO,EAAuB,KACvBhL,EAAqB,KAQV,SAASiL,aACtBC,IAAAA,cACAC,IAAAA,sBACAC,IAAAA,YACAhB,IAAAA,8BACAiB,IAAAA,uBACAC,IAAAA,0BACAxK,IAAAA,kBACAC,IAAAA,sCACAC,IAAAA,gDAuCcC,8BACJA,UA3BRC,eAAsBJ,EAAkBK,EAAKF,iBAC7CI,mBACAD,2BAA+C,OAU/CE,MAAe,CACbC,iBACAC,aAAa,EACb+J,gBAAiB,UACjBlB,aAC4C,iBAAnClJ,EAAKF,MAAMuK,oBACdrK,EAAKF,MAAMuK,oBACX,EACN3J,0BAA0B,KAgM5BE,8BAMAA,qBAAuBC,EACrB,SACEyJ,EACAC,EACAC,EACAC,UAEEzK,EAAKF,MAAMwB,gBAAgD,CAC3DgJ,mBAAAA,EACAC,kBAAAA,EACAC,kBAAAA,EACAC,iBAAAA,QAINlJ,uBAKAA,cAAgBV,EACd,SACEuJ,EACAlB,EACAxI,UAEEV,EAAKF,MAAM0B,SAAkC,CAC7C4I,gBAAAA,EACAlB,aAAAA,EACAxI,yBAAAA,QAyCNe,uBACAA,cAAgB,SAAC9F,OASXgC,IARoCqC,EAAKF,MAArC/B,IAAAA,UAAWsK,IAAAA,SAAUqC,IAAAA,OAEvB9I,EAAiB5B,EAAK6B,mBAC1BjC,GAAyCyI,EACzCzI,GAAyC8K,EACzC9K,GAAyC7B,MAIvC6D,EAAenH,eAAekB,GAChCgC,EAAQiE,EAAejG,OAClB,KACCmG,EAASiI,EAAc/J,EAAKF,MAAOnE,EAAOqE,EAAKD,gBAC/C5C,EAAO8M,EAAYjK,EAAKF,MAAOnE,EAAOqE,EAAKD,gBAG3C4K,EACU,eAAd5M,GAAyC,eAAX2M,EAE1B3I,EAAsB,QAAdhE,EACR6M,EAAmBD,EAAe7I,EAAS,EACjDF,EAAejG,GAASgC,EAAQ,CAC9BqE,SAAU,WACVC,KAAMF,OAAQG,EAAY0I,EAC1BzI,MAAOJ,EAAQ6I,OAAmB1I,EAClCE,IAAMuI,EAAwB,EAAT7I,EACrBjE,OAAS8M,EAAsB,OAAPxN,EACxBS,MAAO+M,EAAexN,EAAO,eAI1BQ,KAGTkE,4BACAA,mBAAqBhB,EAAW,SAACwB,EAAQC,EAASC,SAAc,OAyChEsI,oBAAsB,SAACpI,SAC4BA,EAAMC,cAA/CE,IAAAA,YAAaxE,IAAAA,WAAY0E,IAAAA,cAC5BC,SAAS,SAAAC,MACRA,EAAUkG,eAAiB9K,SAItB,SAGDL,EAAciC,EAAKF,MAAnB/B,UAEJmL,EAAe9K,KACD,QAAdL,SAKMV,SACD,WACH6L,GAAgB9K,YAEb,sBACH8K,EAAepG,EAAcF,EAAcxE,SAMjD8K,EAAehG,KAAKC,IAClB,EACAD,KAAKE,IAAI8F,EAAcpG,EAAcF,IAGhC,CACLvC,aAAa,EACb+J,gBACEpH,EAAUkG,aAAe9K,EAAa,UAAY,WACpD8K,aAAAA,EACAxI,0BAA0B,IAE3BV,EAAKsD,+BAGVwH,kBAAoB,SAACrI,SAC+BA,EAAMC,cAAhDC,IAAAA,aAAcE,IAAAA,aAAcrC,IAAAA,YAC/BuC,SAAS,SAAAC,MACRA,EAAUkG,eAAiB1I,SAItB,SAIH0I,EAAehG,KAAKC,IACxB,EACAD,KAAKE,IAAI5C,EAAWqC,EAAeF,UAG9B,CACLtC,aAAa,EACb+J,gBACEpH,EAAUkG,aAAeA,EAAe,UAAY,WACtDA,aAAAA,EACAxI,0BAA0B,IAE3BV,EAAKsD,+BAGVC,gBAAkB,SAACC,OACTC,EAAazD,EAAKF,MAAlB2D,WAEHvD,UAAcsD,EAEK,mBAAbC,EACTA,EAASD,GAEG,MAAZC,GACoB,iBAAbA,GACPA,EAAShJ,eAAe,aAExBgJ,EAASC,QAAUF,MAIvBF,2BAA6B,WACa,OAApCtD,EAAKC,4BACPxD,EAAcuD,EAAKC,8BAGhBA,2BAA6BpD,EAChCmD,EAAK2D,kBACLrF,MAIJqF,kBAAoB,aACb1D,2BAA6B,OAE7B8C,SAAS,CAAE1C,aAAa,GAAS,aAG/BwB,oBAAoB,EAAG,oBAvbzB+B,kCACLC,EACAb,UAEAc,EAAoBD,EAAWb,GAC/BnD,EAAcgE,GACP,iCAGTE,kBAASmF,GACPA,EAAehG,KAAKC,IAAI,EAAG+F,QAEtBnG,SAAS,SAAAC,UACRA,EAAUkG,eAAiBA,EACtB,KAEF,CACLkB,gBACEpH,EAAUkG,aAAeA,EAAe,UAAY,WACtDA,aAAcA,EACdxI,0BAA0B,IAE3B9F,KAAK0I,+BAGVU,sBAAarI,EAAesI,YAAAA,IAAAA,EAAuB,YACzC8E,EAAcnO,KAAKkF,MAAnBiJ,UACAG,EAAiBtO,KAAKuF,MAAtB+I,aAERvN,EAAQuH,KAAKC,IAAI,EAAGD,KAAKE,IAAIzH,EAAOoN,EAAY,SAE3ChF,SACHkF,EACErO,KAAKkF,MACLnE,EACAsI,EACAiF,EACAtO,KAAKmF,oBAKX4E,mCACqD/J,KAAKkF,MAAhD/B,IAAAA,UAAWsM,IAAAA,oBAAqBK,IAAAA,UAEL,iBAAxBL,GAAsD,MAAlBzP,KAAKsF,UAAmB,KAC/DuD,EAAa7I,KAAKsF,UAEN,eAAdnC,GAAyC,eAAX2M,EAChCjH,EAASrF,WAAaiM,EAEtB5G,EAASjD,UAAY6J,OAIpBzF,yBAGPC,oCACgCjK,KAAKkF,MAA3B/B,IAAAA,UAAW2M,IAAAA,SACgC9P,KAAKuF,MAAhD+I,IAAAA,kBAAcxI,0BAE4B,MAAlB9F,KAAKsF,UAAmB,KAChDuD,EAAa7I,KAAKsF,aAGN,eAAdnC,GAAyC,eAAX2M,KACd,QAAd3M,SAIMV,SACD,WACHoG,EAASrF,YAAc8K,YAEpB,qBACHzF,EAASrF,WAAa8K,oBAGdtG,EAA6Ba,EAA7Bb,YAAaE,EAAgBW,EAAhBX,YACrBW,EAASrF,WAAa0E,EAAcF,EAAcsG,OAItDzF,EAASrF,WAAa8K,OAGxBzF,EAASjD,UAAY0I,OAIpBtE,yBAGPE,gCAC0C,OAApClK,KAAKqF,4BACPxD,EAAc7B,KAAKqF,+BAIvB8E,wBAkBMnK,KAAKkF,MAhBPkF,IAAAA,SACAC,IAAAA,UACAlH,IAAAA,UACAF,IAAAA,OACAqH,IAAAA,SACAC,IAAAA,iBACAC,IAAAA,aACA2D,IAAAA,UACA1D,IAAAA,aACAC,QAAAA,aAAU/G,IACVmM,IAAAA,OACAnF,IAAAA,iBACAC,IAAAA,aACA7H,IAAAA,MACA8H,IAAAA,eACA7H,IAAAA,MAEMyC,EAAgBzF,KAAKuF,MAArBE,YAGFsK,EACU,eAAd5M,GAAyC,eAAX2M,EAE1BlJ,EAAWmJ,EACb/P,KAAKiQ,oBACLjQ,KAAKkQ,oBAEuBlQ,KAAKmQ,oBAA9BrE,OAAYC,OAEbX,EAAQ,MACV+C,EAAY,MACT,IAAIpN,EAAQ+K,EAAY/K,GAASgL,EAAWhL,IAC/CqK,EAAMC,KACJxI,gBAAcuH,EAAU,CACtBvG,KAAM4G,EACN9K,IAAK+K,EAAQ3J,EAAO0J,GACpB1J,MAAAA,EACA0E,YAAaoF,EAAiBpF,OAAc6B,EAC5CvE,MAAO/C,KAAK6G,cAAc9F,UAQ5BwN,EAAqBa,EACzBpP,KAAKkF,MACLlF,KAAKmF,uBAGAtC,gBACL8H,GAAoBC,GAAgB,MACpC,CACEP,UAAAA,EACAzD,SAAAA,EACAgC,IAAK5I,KAAK2I,gBACV5F,SACEqE,SAAU,WACVnE,OAAAA,EACAD,MAAAA,EACAE,SAAU,OACVqI,wBAAyB,QACzBC,WAAY,YACZrI,UAAAA,GACGJ,IAGPF,gBAAc0H,GAAoBC,GAAgB,MAAO,CACvDJ,SAAUgB,EACVxC,IAAK0B,EACLvH,MAAO,CACLE,OAAQ8M,EAAe,OAASxB,EAChC9C,cAAehG,EAAc,YAAS6B,EACtCtE,MAAO+M,EAAexB,EAAqB,cA6CnDvE,kCAC4C,mBAA/BhK,KAAKkF,MAAMwB,iBACE1G,KAAKkF,MAAnBiJ,UACQ,EAAG,OAMbnO,KAAKmQ,oBAJPT,OACAC,OACAC,OACAC,YAEG7J,qBACH0J,EACAC,EACAC,EACAC,MAK6B,mBAAxB7P,KAAKkF,MAAM0B,SAAyB,OAKzC5G,KAAKuF,MAHPiK,IAAAA,gBACAlB,IAAAA,aACAxI,IAAAA,8BAEGa,cACH6I,EACAlB,EACAxI,OAgDNqK,mCACuCnQ,KAAKkF,MAAlCiJ,IAAAA,UAAWvC,IAAAA,gBACoC5L,KAAKuF,MAApDE,IAAAA,YAAa+J,IAAAA,gBAAiBlB,IAAAA,gBAEpB,IAAdH,QACK,CAAC,EAAG,EAAG,EAAG,OAGbrC,EAAawD,EACjBtP,KAAKkF,MACLoJ,EACAtO,KAAKmF,gBAED4G,EAAYwD,EAChBvP,KAAKkF,MACL4G,EACAwC,EACAtO,KAAKmF,gBAKD6G,EACHvG,GAAmC,aAApB+J,EAEZ,EADAlH,KAAKC,IAAI,EAAGqD,GAEZK,EACHxG,GAAmC,YAApB+J,EAEZ,EADAlH,KAAKC,IAAI,EAAGqD,SAGX,CACLtD,KAAKC,IAAI,EAAGuD,EAAaE,GACzB1D,KAAKC,IAAI,EAAGD,KAAKE,IAAI2F,EAAY,EAAGpC,EAAYE,IAChDH,EACAC,OA3WuBK,mBAKpBC,aAAe,CACpBlJ,UAAW,MACXsH,cAAUnD,EACVwI,OAAQ,WACRlE,cAAe,EACff,gBAAgB,KArCE,oBAAXyB,aAAoD,IAAnBA,OAAOC,UACjD0C,MAA2B1C,QAC3BtI,MAAyBsI,SA2f7B,IAAMrD,EAAsB,kBAExBkB,IAAAA,SACAjH,IAAAA,UACAF,IAAAA,OACA6M,IAAAA,OACAtF,IAAAA,aACAI,IAAAA,aACA5H,IAAAA,MAEAwC,IAAAA,SAGoB,MAAhBgF,GAAwC,MAAhBI,GACtB3G,IAAuBA,EAAmBuI,IAAIhH,KAChDvB,EAAmBwI,IAAIjH,GACvBkH,QAAQC,KACN,0IAOAoD,EAA6B,eAAd5M,GAAyC,eAAX2M,SAE3C3M,OACD,iBACA,WACC8L,IAAyBA,EAAqBzC,IAAIhH,KACpDyJ,EAAqBxC,IAAIjH,GACzBkH,QAAQC,KACN,oKAKD,UACA,0BAIGC,MACJ,2FAEMzJ,6BAIJ2M,OACD,iBACA,+BAIGlD,MACJ,oGAEMkD,yBAII,MAAZ1F,QACIwC,MACJ,uFAEmB,OAAbxC,EAAoB,cAAgBA,0BAI1C2F,GAAiC,iBAAV/M,QACnB4J,MACJ,mGAEgB,OAAV5J,EAAiB,cAAgBA,uBAEpC,IAAK+M,GAAkC,iBAAX9M,QAC3B2J,MACJ,mGAEiB,OAAX3J,EAAkB,cAAgBA,wBCvrB1CoK,EAAkB,SACtBnI,EACAnE,EACAwM,OAEQE,EAAevI,EAAfuI,SACAD,EAAuCD,EAAvCC,gBAAiBE,EAAsBH,EAAtBG,qBAErB3M,EAAQ2M,EAAmB,KACzBxG,EAAS,KACTwG,GAAqB,EAAG,KACpBT,EAAeO,EAAgBE,GACrCxG,EAAS+F,EAAa/F,OAAS+F,EAAa1K,SAGzC,IAAIhD,EAAImO,EAAoB,EAAGnO,GAAKwB,EAAOxB,IAAK,KAC/CgD,EAASkL,EAAgClO,GAE7CiO,EAAgBjO,GAAK,CACnB2H,OAAAA,EACA3E,KAAAA,GAGF2E,GAAU3E,EAGZgL,EAAcG,kBAAoB3M,SAG7ByM,EAAgBzM,IAmCnB6M,EAA8B,SAClC1I,EACAqI,EACAO,EACAC,EACA7G,QAEO6G,GAAOD,GAAM,KACZE,EAASD,EAAMzF,KAAK2F,OAAOH,EAAOC,GAAO,GACzCG,EAAgBb,EAAgBnI,EAAO8I,EAAQT,GAAerG,UAEhEgH,IAAkBhH,SACb8G,EACEE,EAAgBhH,EACzB6G,EAAMC,EAAS,EACNE,EAAgBhH,IACzB4G,EAAOE,EAAS,UAIhBD,EAAM,EACDA,EAAM,EAEN,GAILF,EAAmC,SACvC3I,EACAqI,EACAxM,EACAmG,WAEQiH,EAAcjJ,EAAdiJ,UACJC,EAAW,EAGbrN,EAAQoN,GACRd,EAAgBnI,EAAOnE,EAAOwM,GAAerG,OAASA,GAEtDnG,GAASqN,EACTA,GAAY,SAGPR,EACL1I,EACAqI,EACAjF,KAAKE,IAAIzH,EAAOoN,EAAY,GAC5B7F,KAAK2F,MAAMlN,EAAQ,GACnBmG,IAIEkI,EAAwB,kBAC1BjB,IAAAA,UACAX,IAAAA,gBAAiB4C,IAAAA,kBAAmB1C,IAAAA,kBAElC2C,EAA2B,KAI3B3C,GAAqBS,IACvBT,EAAoBS,EAAY,GAG9BT,GAAqB,EAAG,KACpBT,EAAeO,EAAgBE,GACrC2C,EAA2BpD,EAAa/F,OAAS+F,EAAa1K,YAMzD8N,GAHoBlC,EAAYT,EAAoB,GACH0C,GAKpDE,EAAmBpB,EAAoB,CAC3CC,cAAe,SACbjK,EACAnE,EACAwM,UACWF,EAAgBnI,EAAOnE,EAAOwM,GAAerG,QAE1DmI,YAAa,SACXnK,EACAnE,EACAwM,UACWA,EAAcC,gBAAgBzM,GAAOwB,MAElD6M,sBAAAA,EAEAf,8BAA+B,SAC7BnJ,EACAnE,EACAsI,EACAiF,EACAf,OAEQpK,EAAqC+B,EAArC/B,UAAWF,EAA0BiC,EAA1BjC,OAAQ6M,EAAkB5K,EAAlB4K,OAAQ9M,EAAUkC,EAAVlC,MAI7BT,EAD6B,eAAdY,GAAyC,eAAX2M,EACpB9M,EAAQC,EACjCgK,EAAeI,EAAgBnI,EAAOnE,EAAOwM,GAI7CgB,EAAqBa,EAAsBlK,EAAOqI,GAElDiB,EAAYlG,KAAKC,IACrB,EACAD,KAAKE,IAAI+F,EAAqBhM,EAAM0K,EAAa/F,SAE7CuH,EAAYnG,KAAKC,IACrB,EACA0E,EAAa/F,OAAS3E,EAAO0K,EAAa1K,aAG9B,UAAV8G,IAKAA,EAHAiF,GAAgBG,EAAYlM,GAC5B+L,GAAgBE,EAAYjM,EAEpB,OAEA,UAIJ8G,OACD,eACImF,MACJ,aACIC,MACJ,gBACInG,KAAKoG,MAAMD,GAAaD,EAAYC,GAAa,OACrD,sBAECH,GAAgBG,GAAaH,GAAgBE,EACxCF,EACEA,EAAeG,EACjBA,EAEAD,IAKfc,uBAAwB,SACtBpK,EACAgC,EACAqG,UAvLoB,SACtBrI,EACAqI,EACArG,OAEQsG,EAAuCD,EAAvCC,gBAAiBE,EAAsBH,EAAtBG,yBAGvBA,EAAoB,EAAIF,EAAgBE,GAAmBxG,OAAS,IAExCA,EAErB0G,EACL1I,EACAqI,EACAG,EACA,EACAxG,GAMK2G,EACL3I,EACAqI,EACAjF,KAAKC,IAAI,EAAGmF,GACZxG,GA6JSyG,CAAgBzI,EAAOqI,EAAerG,IAEnDqI,0BAA2B,SACzBrK,EACA4G,EACAwC,EACAf,WAEQpK,EAAgD+B,EAAhD/B,UAAWF,EAAqCiC,EAArCjC,OAAQkL,EAA6BjJ,EAA7BiJ,UAAW2B,EAAkB5K,EAAlB4K,OAAQ9M,EAAUkC,EAAVlC,MAIxCT,EAD6B,eAAdY,GAAyC,eAAX2M,EACpB9M,EAAQC,EACjCgK,EAAeI,EAAgBnI,EAAO4G,EAAYyB,GAClDiB,EAAYF,EAAe/L,EAE7B2E,EAAS+F,EAAa/F,OAAS+F,EAAa1K,KAC5CwJ,EAAYD,EAETC,EAAYoC,EAAY,GAAKjH,EAASsH,GAE3CtH,GAAUmG,EAAgBnI,IAD1B6G,EAC4CwB,GAAehL,YAGtDwJ,GAGThH,2BAAkBG,EAAmBM,OAG7B+H,EAAgB,CACpBC,gBAAiB,GACjB4C,kBAJ8BlL,EAAxBkL,mBAvQwB,GA4Q9B1C,mBAAoB,UAGtBlI,EAAS+K,gBAAkB,SACzBxP,EACA8N,YAAAA,IAAAA,GAA8B,GAE9BtB,EAAcG,kBAAoBpF,KAAKE,IACrC+E,EAAcG,kBACd3M,EAAQ,GAOVyE,EAASyB,oBAAoB,GAEzB4H,GACFrJ,EAASwJ,eAINzB,GAGTvI,uCAAuC,EAEvCC,cAAe,gBAAGwI,IAAAA,YAEU,mBAAbA,QACHb,MACJ,gFAEmB,OAAba,EAAoB,cAAgBA,0BC9S9C+C,EAAgBtM,EAAoB,CACxCC,gBAAiB,WAA8BpD,UAC7CA,IADkB+F,aAGpBxC,eAAgB,WAA8BvD,YAA3B+F,aAGnBlC,aAAc,WAA4B7D,UACxCA,IADegG,WAGjBpC,aAAc,WAA4B5D,YAAzBgG,WAGjBxC,wBAAyB,gBAAGgF,IAAAA,kBAAUxC,UACPwC,GAE/B/E,uBAAwB,gBAAG8E,IAAAA,qBAAaxC,YACPwC,GAEjC7E,+BAAgC,WAE9Bb,EACAyF,EACA7F,EACA+J,EACA/D,OALEF,IAAAA,YAAaxC,IAAAA,YAAa9D,IAAAA,MAOtByN,EAAmBnI,KAAKC,IAC5B,EACAe,EAAgBxC,EAA6B9D,GAEzCwL,EAAYlG,KAAKE,IACrBiI,EACA7M,EAAgBkD,GAEZ2H,EAAYnG,KAAKC,IACrB,EACA3E,EAAgBkD,EACd9D,EACAwG,EACE1C,UAGQ,UAAVuC,IAEAA,EADE7F,GAAciL,EAAYzL,GAASQ,GAAcgL,EAAYxL,EACvD,OAEA,UAIJqG,OACD,eACImF,MACJ,aACIC,MACJ,aAGGiC,EAAepI,KAAKoG,MACxBD,GAAaD,EAAYC,GAAa,UAEpCiC,EAAepI,KAAKqI,KAAK3N,EAAQ,GAC5B,EACE0N,EAAeD,EAAmBnI,KAAK2F,MAAMjL,EAAQ,GACvDyN,EAEAC,MAEN,sBAEClN,GAAciL,GAAajL,GAAcgL,EACpChL,EACEiL,EAAYD,EAGdC,EACEjL,EAAaiL,EACfA,EAEAD,IAKf9J,4BAA6B,WAE3BZ,EACAuF,EACAzD,EACA2H,EACA/D,OALEzC,IAAAA,UAAW9D,IAAAA,OAAQsG,IAAAA,SAOfqH,EAAgBtI,KAAKC,IACzB,EACAgB,EAAaxC,EAA2B9D,GAEpCuL,EAAYlG,KAAKE,IACrBoI,EACA9M,EAAaiD,GAET0H,EAAYnG,KAAKC,IACrB,EACAzE,EAAaiD,EACX9D,EACAuG,EACEzC,UAGQ,UAAVsC,IAEAA,EADEzD,GAAa6I,EAAYxL,GAAU2C,GAAa4I,EAAYvL,EACtD,OAEA,UAIJoG,OACD,eACImF,MACJ,aACIC,MACJ,aAGGiC,EAAepI,KAAKoG,MACxBD,GAAaD,EAAYC,GAAa,UAEpCiC,EAAepI,KAAKqI,KAAK1N,EAAS,GAC7B,EACEyN,EAAeE,EAAgBtI,KAAK2F,MAAMhL,EAAS,GACrD2N,EAEAF,MAEN,sBAEC9K,GAAa6I,GAAa7I,GAAa4I,EAClC5I,EACE6I,EAAYD,EAGdC,EACE7I,EAAY6I,EACdA,EAEAD,IAKfpK,6BAA8B,WAE5BZ,OADEsD,IAAAA,YAAawC,IAAAA,mBAGfhB,KAAKC,IACH,EACAD,KAAKE,IACHc,EAAc,EACdhB,KAAK2F,MAAMzK,EAAesD,MAIhCzC,gCAAiC,WAE/ByH,EACAtI,OAFEsD,IAAAA,YAAawC,IAAAA,YAAatG,IAAAA,MAItBqE,EAAOyE,EAAehF,EACtB+J,EAAoBvI,KAAKqI,MAC5B3N,EAAQQ,EAAa6D,GAAUP,UAE3BwB,KAAKC,IACV,EACAD,KAAKE,IACHc,EAAc,EACdwC,EAAa+E,EAAoB,KAKvChM,0BAA2B,WAEzBe,OADEmB,IAAAA,UAAWwC,IAAAA,gBAGbjB,KAAKC,IACH,EACAD,KAAKE,IAAIe,EAAW,EAAGjB,KAAK2F,MAAMrI,EAAcmB,MAGpDjC,6BAA8B,WAE5BgH,EACAlG,OAFEmB,IAAAA,UAAWwC,IAAAA,SAAUtG,IAAAA,OAIjBuE,EAAMsE,EAAe/E,EACrB+J,EAAiBxI,KAAKqI,MACzB1N,EAAS2C,EAAY4B,GAAST,UAE1BuB,KAAKC,IACV,EACAD,KAAKE,IACHe,EAAW,EACXuC,EAAagF,EAAiB,KAKpC/L,2BAAkBG,KAIlBF,uCAAuC,EAEvCC,cAAe,gBAAG6B,IAAAA,YAAaC,IAAAA,aAEA,iBAAhBD,QACH8F,MACJ,iFAGoB,OAAhB9F,EAAuB,cAAgBA,0BAKtB,iBAAdC,QACH6F,MACJ,+EAEoB,OAAd7F,EAAqB,cAAgBA,0BCtO/CgK,EAAgB7B,EAAoB,CACxCC,cAAe,WAA2BpO,UACxCA,IADgB0M,UAGlB4B,YAAa,WAA2BtO,YAAxB0M,UAGhB2B,sBAAuB,gBAAGjB,IAAAA,mBAAWV,SACPU,GAE9BE,8BAA+B,WAE7BtN,EACAsI,EACAiF,OAHEnL,IAAAA,UAAWF,IAAAA,OAAQkL,IAAAA,UAAWV,IAAAA,SAAUqC,IAAAA,OAAQ9M,IAAAA,MAO5CT,EAD6B,eAAdY,GAAyC,eAAX2M,EACpB9M,EAAQC,EACjC+N,EAAiB1I,KAAKC,IAC1B,EACA4F,EAAcV,EAA0BlL,GAEpCiM,EAAYlG,KAAKE,IACrBwI,EACAjQ,EAAU0M,GAENgB,EAAYnG,KAAKC,IACrB,EACAxH,EAAU0M,EAA0BlL,EAASkL,UAGjC,UAAVpE,IAKAA,EAHAiF,GAAgBG,EAAYlM,GAC5B+L,GAAgBE,EAAYjM,EAEpB,OAEA,UAIJ8G,OACD,eACImF,MACJ,aACIC,MACJ,aAGGiC,EAAepI,KAAKoG,MACxBD,GAAaD,EAAYC,GAAa,UAEpCiC,EAAepI,KAAKqI,KAAKpO,EAAO,GAC3B,EACEmO,EAAeM,EAAiB1I,KAAK2F,MAAM1L,EAAO,GACpDyO,EAEAN,MAGN,sBAECpC,GAAgBG,GAAaH,GAAgBE,EACxCF,EACEA,EAAeG,EACjBA,EAEAD,IAKfc,uBAAwB,WAEtBpI,OADEiH,IAAAA,UAAWV,IAAAA,gBAGbnF,KAAKC,IACH,EACAD,KAAKE,IAAI2F,EAAY,EAAG7F,KAAK2F,MAAM/G,EAAWuG,MAGlD8B,0BAA2B,WAEzBzD,EACAwC,OAFEnL,IAAAA,UAAWF,IAAAA,OAAQkL,IAAAA,UAAWV,IAAAA,SAAUqC,IAAAA,OAAQ9M,IAAAA,MAM5CkE,EAAS4E,EAAe2B,EACxBlL,EAF6B,eAAdY,GAAyC,eAAX2M,EAEpB9M,EAAQC,EACjCgO,EAAkB3I,KAAKqI,MAC1BpO,EAAO+L,EAAepH,GAAYuG,UAE9BnF,KAAKC,IACV,EACAD,KAAKE,IACH2F,EAAY,EACZrC,EAAamF,EAAkB,KAKrClM,2BAAkBG,KAIlBF,uCAAuC,EAEvCC,cAAe,gBAAGwI,IAAAA,YAEU,iBAAbA,QACHb,MACJ,8EAEmB,OAAba,EAAoB,cAAgBA,0BC1HrC,SAASyD,EAA8BxR,EAAQyR,MAC9C,MAAVzR,EAAgB,MAAO,OAGvBC,EAAKJ,EAFLD,EAAS,GACT8R,EAAahS,OAAOiS,KAAK3R,OAGxBH,EAAI,EAAGA,EAAI6R,EAAW3R,OAAQF,IACjCI,EAAMyR,EAAW7R,GACb4R,EAASG,QAAQ3R,IAAQ,IAC7BL,EAAOK,GAAOD,EAAOC,WAGhBL,ECRM,SAASiS,EAAeC,EAAcC,OAC9C,IAAIC,KAAaF,OACdE,KAAaD,UACV,MAGN,IAAIC,KAAaD,KAChBD,EAAKE,KAAeD,EAAKC,UACpB,SAGJ,ECRM,SAASC,EACtBC,EACA3I,OAEe4I,EAA2BD,EAAlC7O,MAAqB+O,IAAaF,aAC3BG,EAA2B9I,EAAlClG,MAAqBiP,IAAa/I,oBAGvCsI,EAAeM,EAAWE,KAAeR,EAAeO,EAAUE,sHCPxD,SACb/I,EACAgJ,UAGGN,EAAS3R,KAAKkF,MAAO+D,IAAcsI,EAAevR,KAAKuF,MAAO0M"}