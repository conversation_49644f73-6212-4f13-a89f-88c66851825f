import Paragraph from '../Paragraph';
import {ParaLineRange} from './ParaLine';

/**
 * 统计在对齐方式时各变量和状态
 */
export class ParagraphRecalculateStateCounter {
    public paragraph: Paragraph = undefined;
    public range: ParaLineRange = undefined;
    public bWord: boolean = false;
    public spaceLen: number = 0; // 连续空格长度
    public spacesCount: number = 0; // 连续空格数

    public words: number = 0;    // 单词个数
    public spaces: number = 0;  // 空格数统计
    public letters: number = 0;  // 字母个数
    public spacesSkip: number = 0;
    public lettersSkip: number = 0;

    constructor() {
        //
    }

    public reset(para: Paragraph, range: any): void {

        this.paragraph = para;
        this.range = range;
        this.bWord = false;
        this.spaceLen = 0;
        this.spacesCount = 0;

        this.words       = 0;
        this.spaces      = 0;
        this.letters     = 0;
        this.spacesSkip  = 0;
        this.lettersSkip = 0;
    }
}
