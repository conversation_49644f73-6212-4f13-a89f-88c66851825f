import { DocumentCore } from '../../model/DocumentCore';
import { EmrEditor } from '../../components/editor/Main';
import { ResultType, isValidName, NewControlPropName, DataType,
    INewControlProperty, ICustomProps, checkXmlElementNameValidity, NewControlType,
    RegionDeleteType,
    EXTERNAL_STRUCT_TYPE,
    filterChars2} from '../commonDefines';
import { ExternalAction } from './ExternalAction';
import { Region } from '../../model/core/Region';
import { NewControl } from '../../model/core/NewControl/NewControl';
import { NewControlRadio } from '../../model/core/NewControl/NewControlRadio';
export default class Table extends ExternalAction {
    private _documentCore: DocumentCore;
    private _host: EmrEditor;
    constructor(host: EmrEditor) {
        super(host.state.documentCore);
        this._host = host;
        this._documentCore = host.state.documentCore;
    }

    public insertRegionAtCurrentCursor(sName: string ): string {
        if (!sName || !isValidName(sName) || !this._documentCore.checkRegionName(sName)) {
            return ResultType.StringEmpty;
        }

        const res = this._documentCore.addRegion({newControlName: sName});
        if (res === ResultType.Success) {
            this._host.handleRefresh();
            return sName;
        }

        return ResultType.StringEmpty;
    }

    public collapseRegion(name: string, flag: boolean): number {
        if (!name || typeof flag !== 'boolean') {
            return ResultType.ParamError;
        }

        const res = this._documentCore.changeRegionLoaded(name, flag ? '1' : '2');
        if (res === ResultType.Success) {
            this._host.handleRefresh();
        }
        return res;
    }

    public deleteRedundantByRegionName(strName: string, bPageBreak: boolean): number {
        if (!strName || typeof bPageBreak !== 'boolean') {
            return ResultType.ParamError;
        }

        const result = this._documentCore.deleteRedundantByRegionName(strName, bPageBreak);
        if (result === ResultType.Success) {
            this._host.handleRefresh();
        }
        return result;
    }

    public insertRegionAfterOneRegion(sRegion: string, sPrveRegion: string): number {
        if (!sRegion || !sPrveRegion || !isValidName(sRegion) || !this._documentCore.checkRegionName(sRegion)) {
            return ResultType.ParamError;
        }

        const res = this._documentCore.insertRegionAfterOneRegion(sRegion, sPrveRegion);
        if (res === ResultType.Success) {
            this._host.handleRefresh();
        }
        return res;
    }

    public getCurrentRegionName(): string {
        const region = this._documentCore.getCursorInRegion();
        if (region) {
            return region.getName();
        }

        return ResultType.StringEmpty;
    }

    /**
     * 获取指定一个区域的脏标记状态
     * @param sRegionName 区域名称
     */
    public getRegionModifyFlag(sRegionName: string): number {
        return this._documentCore.getRegionModifyFlag(sRegionName) ? 1 : 0;
    }

    /**
     * 获取整个文档区域的脏标记符合某个状态的名称集合/可以只获取最外层区域，或者所有层次的区域
     * @param nOnlyFirstRegions 1 – 只获取最外层 0 --- 获取所有区域
     * @param nModifyFlag 1 – 只获取修改过的 0 --- 只获取未修改过的
     */
    public getAllRegionsByModifyFlag(nOnlyFirstRegions: number, nModifyFlag: number): string {
        const arrs = [1, 0];
        if (!arrs.includes(nOnlyFirstRegions) || !arrs.includes(nModifyFlag)) {
            return ResultType.StringEmpty;
        }

        return this._documentCore.getAllRegionsByModifyFlag(nOnlyFirstRegions, nModifyFlag);
    }

    /**
     * 将当前文档所有的区域记录脏标记置位为0
     */
    public cleanAllRegionsModifyFlag(): number {
        return this._documentCore.cleanAllRegionsModifyFlag();
    }

    public getRegionProp(sName: string, sProp: string): any {
        if (!sName || !sProp) {
            return ResultType.StringEmpty;
        }
        const region = this._documentCore.getRegionByName(sName);
        if (!region) {
            return ResultType.StringEmpty;
        }

        return this._getValueByPropName(sProp, region);
    }

    public setRegionProp(sName: string, sProp: string, sValue: any): number {
        if (!sName || !sProp) {
            return ResultType.ParamError;
        }
        const propsNames: string[] = [NewControlPropName.ReverseEdit, NewControlPropName.DeleteProtect,
            NewControlPropName.EditProtect, NewControlPropName.Hidden];
        let value: any;
        let type: any;
        const stringProps: string[] = [NewControlPropName.Title, NewControlPropName.SerialNumber,
            NewControlPropName.Helptip, NewControlPropName.Identifier, NewControlPropName.Placeholder];
        if (propsNames.includes(sProp)) {
            if (typeof sValue !== 'boolean') {
                return ResultType.ParamError;
            }
        } else if (stringProps.includes(sProp)) {
            //修复设置Title时，Title为空字符串 允许设置
            if (typeof sValue !== 'string' || !sValue && sProp !== 'Title') {
                return ResultType.ParamError;
            }
        } else {
            if (!checkXmlElementNameValidity(sProp)) {
                return ResultType.ParamError;
            }
            type = this._getDataType(sValue);
            if (type === undefined) {
                return ResultType.ParamError;
            }
        }
        value = sValue;

        
        const option: INewControlProperty = {newControlName: undefined, newControlSerialNumber: undefined};
        switch (sProp) {
            case NewControlPropName.ReverseEdit:
                option.isNewControlReverseEdit = value;
                break;
            case NewControlPropName.DeleteProtect:
                option.isNewControlCanntDelete = value;
                break;
            case NewControlPropName.EditProtect:
                option.isNewControlCanntEdit = value;
                break;
            case NewControlPropName.Placeholder:
                option.newControlPlaceHolder = value;
                break;
            case NewControlPropName.Hidden:
                option.isNewControlHidden = value;
                break;
            case NewControlPropName.Identifier:
                if (value && !isValidName(value)) {
                    return ResultType.ParamError;
                }
                option.identifier = value;
                break;
            case NewControlPropName.Title: {
                option.newControlTitle = value;
                option.showTitle = true;
                break;
            }
            case NewControlPropName.Helptip: {
                option.newControlInfo = value;
                break;
            }
            case NewControlPropName.SerialNumber: {
                option.newControlSerialNumber = value;
                break;
            }
            case NewControlPropName.MaxLength: {
                option.newControlMaxLength = value;
                break;
            }
            case NewControlPropName.ShowBackgroundColor: {
                option.showBackgroundColor = value;
                break;
            }
            // case NewControlPropName.Placeholder: {
            //     option.newControlPlaceHolder = value;
            //     break;
            // }
            default: {
                const newControl: Region = this._documentCore.getRegionByName(sName);
                if (!newControl) {
                    return ResultType.Failure;
                }
                this.resetAdminMode();
                return newControl.addCustomPropItem(sProp, value + '', type, value);
            }
        }
        const result = this._documentCore.setRegionProperty(option, sName);
        this.resetAdminMode();
        if (result === ResultType.Success) {
            this._host.handleRefresh();
        }

        return result;
    }

    public getRegionText(name: string): string {
        if (!name) {
            return ResultType.StringEmpty;
        }

        return this._documentCore.getRegionText(name);
    }

    public setRegionPropByArray(sName: string, sJsons: string): number {
        if (!sName || !sJsons) {
            return ResultType.ParamError;
        }
        if (this._documentCore.checkRegionName(sName)) {
            return ResultType.ParamError;
        }
        let obj: any;
        try {
            obj = JSON.parse(sJsons);
        } catch {
            return ResultType.ParamError;
        }
        if (!obj.property || typeof obj.property !== 'object') {
            return ResultType.ParamError;
        }
        const props = this._setProperty(obj.property);
        if (Object.keys(props).length === 1) {
            return ResultType.ParamError;
        }
        const res = this._documentCore.setRegionProperty(props, sName);
        if (res === ResultType.Success) {
            this._host.handleRefresh();
        }
        return res;
    }

    public setRegionBorderViewMode(nViewType: number): number {
        if (![1, 2, 3].includes(nViewType)) {
            return ResultType.ParamError;
        }
        const res = this._documentCore.setRegionBorderViewMode(nViewType);
        if (res === ResultType.Success) {
            this._host.handleRefresh();
        }
        return res;
    }

    /** 设置指定区域是否常显边框 */
    public setRegionBorderVisible(sRegion: string, bShow: boolean): number {
        if (!sRegion || typeof sRegion !== 'string' || typeof bShow !== 'boolean') {
            return ResultType.ParamError;
        }
        const res = this._documentCore.setRegionBorderVisible(sRegion, bShow);
        if (res === ResultType.Success) {
            this._host.handleRefresh();
        }
        return res;
    }

    /** 设置所有区域是否常显边框 */
    public setAllRegionsBorderVisible(bShow: boolean): number {
        if (typeof bShow !== 'boolean') {
            return ResultType.ParamError;
        }
        const res = this._documentCore.setAllRegionsBorderVisible(bShow);
        if (res === ResultType.Success) {
            this._host.handleRefresh();
        }
        return res;
    }

    /**
     * 设置指定区域内所有子元素的反向编辑属性为true或者false
     * @param sRegionName 区域名称
     * @param bReverseEdit 反向编辑属性值
     * @returns 操作结果
     */
    public setRegionSubElementsReverseEdit(sRegionName: string, bReverseEdit: boolean): number {
        if (!sRegionName || typeof bReverseEdit !== 'boolean') {
            return ResultType.ParamError;
        }

        // 获取区域内所有结构化元素名称数组
        const structNames = this._documentCore.getDocument().getNewcontrolNamesInOneRegion(sRegionName);
        if (structNames.length === 0) {
            return ResultType.Success; // 区域内没有结构化元素，直接返回成功
        }

        // 为每个结构化元素设置反向编辑属性
        let result = ResultType.Success;
        for (const structName of structNames) {
            const newControl = this._documentCore.getNewControlByName(structName);
            if (!newControl) {
                return ResultType.ParamError;
            }
            const res = newControl.setReverseEdit(bReverseEdit);
            if (res !== ResultType.Success) {
                result = res; // 如果有一个失败，记录失败结果，但继续处理其他元素
            }
        }

        if (result === ResultType.Success) {
            this._host.handleRefresh();
        }

        return result;
    }

    public setRegionName(sName: string, sNewName: string): number {
        if (!sName || !sNewName || !isValidName(sNewName) || !this._documentCore.checkRegionName(sNewName)) {
            return ResultType.ParamError;
        }

        return this._documentCore.setRegionProperty({newControlName: sNewName}, sName);
    }

    /**
     * 删除一个区域
     * @param sName 区域名称
     * @param lFlag 1：删除区域结构（内容不删除）2：删除区域内容 3：删除区域和内容
     */
    public deleteRegion(sName: string, lFlag: number): number {
        if (!sName || ![RegionDeleteType.OnlyStruct, RegionDeleteType.OnlyContent, RegionDeleteType.StructAndContent]
            .includes(lFlag)) {
            return ResultType.ParamError;
        }

        const res = this._documentCore.removeRegion(sName, lFlag);
        if (res === ResultType.Success) {
            this._host.handleRefresh();
        }

        return res;
    }

    public getOutestRegionNameByCurrentCursor(): string {
        return this._documentCore.getTopRegionNameAtCursor();
    }

    public getFatherRegionNameOfOneStruct(sStructsName: string): string {
        if (!sStructsName) {
            return ResultType.StringEmpty;
        }

        return this._documentCore.getFatherRegionNameOfOneStruct(sStructsName);
    }

    /**
     * 按照某个属性名（可以同时带属性值）进行筛选，筛选出符合条件的结构名
     * @param sRegionName 区域名
     * @param sPropName 属性名
     * @param sPropValue 属性值
     * @return 在指定区域内，返回符合条件的所有的结构名称 区域跟结构以”|”隔开，批次之间以”,” 隔开
     */
    public filterStructsByPropInRegion(sRegionName: string, sPropName: string, propValue: any): string {
        if (!sRegionName || !sPropName || propValue == null) {
            return ResultType.StringEmpty;
        }

        return this._documentCore.filterStructsByPropInRegion(sRegionName, sPropName, propValue);
    }

    /**
     * 依次返回当前文档中Region名称列表
     */
    public getAllRegionNamesByCurrentDoc(): string {
        return this._documentCore.getAllRegionNames()
            .join(',');
    }

    /**
     * 返回文档第一层Region名称列表
     */
    public getFirstLevelRegionNames(): string {
        return this._documentCore.getFirstLevelRegionNames()
            .join(',');
    }

    /**
     * 对指定的某个区域设置是否只读，其他剩余的区域设置是否只读
     * @param sCurrentRegionName 区域名称
     * @param bCurrent 指定区域只读标识
     * @param bLef 除了指定区域外，其他最外层区域的只读标识
     */
    public setRegionsReadOnlyProp(sName: string, bCurrent: boolean, bLef: boolean): number {
        if (!sName || typeof bCurrent !== 'boolean' || typeof bLef !== 'boolean') {
            return ResultType.ParamError;
        }

        return this._documentCore.setRegionsReadOnlyProp(sName, bCurrent, bLef);
    }

    public getRegionEnd(sName: string): string {
        if (!sName) {
            return ResultType.StringEmpty;
        }

        return this._documentCore.getRegionEnd(sName);
    }

    public getRegionBegin(sName: string): string {
        if (!sName) {
            return ResultType.StringEmpty;
        }

        return this._documentCore.getRegionBegin(sName);
    }

    /**
     * 在指定区域的前端或者后端加入一个新行。
     * @param sRegion 区域名称
     * @param nPosType 1 – 区域前端 2 – 区域后端
     */
    public addNewLineForRegion(sRegion: string, nPosType: number): number {
        if (!sRegion || ![1, 2].includes(nPosType)) {
            return ResultType.ParamError;
        }

        const res = this._documentCore.addNewLineForRegion(sRegion, nPosType);
        if (res === ResultType.Success) {
            this._host.handleRefresh();
        }
        return res;
    }

    /**
     * 设置指定区域内容文本
     * @param sName 区域名称
     * @param sText 区域内容
     */
    public setRegionText(sName: string, sText: string): number {
        if (!sName || sText == null) {
            return ResultType.ParamError;
        }
        const res = this._documentCore.setRegionText(sName, sText);
        if (res === ResultType.Success) {
            this._host.handleRefresh();
        }
        return res;
    }

    /**
     * 光标跳转到某一个区域的前面或者后面
     * @param strName 区域名称
     * @param bBack true – 区域外的行开始处 false – 区域前的行结束处。
     */
    public cursorJumpOutOfOneRegion(strName: string, bBack: boolean): number {
        if (!strName || typeof bBack !== 'boolean') {
            return ResultType.ParamError;
        }
        const res = this._documentCore.jumpToRegionBeforeOrAfter(strName, bBack);
        if (res === ResultType.Success) {
            this._host.handleRefresh();
        }
        return res;
    }

    /**
     * 选中指定名称的区域
     * @param sName 区域名称
     * @param bOnlyContent true － 只选中区域的内容 false － 选中整个区域结构（暂时没用
     */
    public selectOneRegion(sName: string, bOnlyContent?: boolean): number {
        if (!sName || bOnlyContent !== undefined && typeof bOnlyContent !== 'boolean') {
            return ResultType.ParamError;
        }

        const res = this._documentCore.selectOneRegion(sName, bOnlyContent);
        if (res === ResultType.Success) {
            this._host.handleRefresh();
        }

        return res;
    }

    /**
     * 以XML格式依次返回指定区域的Region, Section 和NewControl的结构化元素信息
     * @param name 区域名称
     * @param sRev 保留参数 目前无用
     */
    public getRegionXmlInfoByParament(name: string, sRev?: string): string {

        if (sRev !== 'Include Child') {
            const newControls = this._documentCore.getRegionXmlInfoByParament(name, sRev);
            if ( newControls ) {
                // const result: any = this._documentCore.getDocument()
                //                             .getNewControlsPropToCustom(newControls);
                const keys = Object.keys(newControls);

                keys.forEach((key) => {
                    newControls[key].type = EXTERNAL_STRUCT_TYPE[newControls[key].type];
                });

                return JSON.stringify(newControls);
            }

            return ResultType.StringEmpty;

        } else {
            const region = this._documentCore.getRegionByName(name);
            if (!region) {
                return ResultType.StringEmpty;
            }

            const logicDocument = this._documentCore.getDocument();
            return logicDocument.getRegionNextContentProps(region);
        }

    }

    /**
     * 将当前文档的所有内容变成一个区域
     * @param sRegionName 区域名称
     */
    public changeAllFileToOneRegion(sName: string): string {
        if (!sName) {
            return ResultType.StringEmpty;
        }

        const res = this._documentCore.changeAllFileToOneRegion(sName);
        if (res) {
            this._host.handleRefresh();
        }

        return res;
    }

    public getRegionPropByArray(sName: string): string {
        if (!sName) {
            return ResultType.StringEmpty;
        }

        return this._documentCore.getRegionPropByArray(sName);
    }

    public getSignControlCountInRegion(sName: string): number {
        if (!sName) {
            return ResultType.Failure2;
        }
        const curRegion: Region = this._documentCore.getRegionByName(sName);
        const logicDocument = this._documentCore.getDocument();
        const newControlManager = logicDocument.getNewControlManager();
        let result = 0;
        if (curRegion != null) {
            const startPos = logicDocument.getElementStartPos(curRegion);
            const endPos = logicDocument.getElementEndPos(curRegion);
            const newControls = newControlManager.getNewControls(startPos, endPos);
            // console.log(newControls);

            for (const newControl of newControls) {
                if (newControl.getType() === NewControlType.SignatureBox) {
                    result++;
                }
            }
            return result;

        }
        return ResultType.Failure2;
    }

    public getSignControlNamesInRegion(sName: string): string[] {
        if (!sName) {
            return [ResultType.ParamError + ''];
        }
        const curRegion: Region = this._documentCore.getRegionByName(sName);
        const logicDocument = this._documentCore.getDocument();
        const newControlManager = logicDocument.getNewControlManager();
        const result = [];
        if (curRegion != null) {
            const startPos = logicDocument.getElementStartPos(curRegion);
            const endPos = logicDocument.getElementEndPos(curRegion);
            const newControls = newControlManager.getNewControls(startPos, endPos);
            // console.log(newControls);

            for (const newControl of newControls) {
                if (newControl.getType() === NewControlType.SignatureBox) {
                    result.push(newControl.getNewControlName());
                }
            }
            return result;

        }
        return [ResultType.Failure + ''];
    }

    public getIncompletedCtrlNameListInRegion(sRegion: string): string {
        if (!sRegion) {
            return ResultType.ParamError + '';
        }
        const curRegion = this._documentCore.getRegionByName(sRegion);
        if (curRegion == null) {
            return '';
        }
        const logicDocument = this._documentCore.getDocument();
        const newControlManager = logicDocument.getNewControlManager();

        let result = '';
        if (curRegion != null) {
            const startPos = logicDocument.getElementStartPos(curRegion);
            const endPos = logicDocument.getElementEndPos(curRegion);
            const newControls: NewControl[] = newControlManager.getNewControls(startPos, endPos);
            // console.log(newControls);

            for (const newControl of newControls) {
                const bMustInput = newControl.isMustInput();
                if (bMustInput === true) {
                    const curText = newControl.getNewControlText();
                    const curName = newControl.getNewControlName();
                    if (newControl instanceof NewControlRadio) {
                        const bSelected = newControl.getSelected();
                        if (bSelected == null || (bSelected != null && bSelected.length === 0)) {
                            result += curName + ', ';
                        }
                    } else if (curText != null && curText === '' && curText.length === 0) {
                        result += curName + ', ';
                    }
                }
            }
        }
        result = result.trim();
        const len = result.length;
        if (result[len - 1] === ',') {
            result = result.slice(0, len - 1);
        }

        return result;
    }

    private _getValueByPropName(sPropName: string, newControl: Region): any {
        let result: any;
        switch (sPropName) {
            case NewControlPropName.ReverseEdit:
                result = newControl.isReverseEdit();
                break;
            case NewControlPropName.DeleteProtect:
                result = newControl.isDeleteProtect();
                break;
            case NewControlPropName.EditProtect:
                result = newControl.isEditProtect();
                break;
            case NewControlPropName.Hidden:
                result = newControl.isSourceHide();
                break;
            case NewControlPropName.Title:
                result = newControl.getTitle();
                break;
            case NewControlPropName.Helptip:
                result = newControl.getTipsContent();
                break;
            case NewControlPropName.Identifier:
                result = newControl.getIdentifier();
                break;
            case NewControlPropName.SerialNumber:
                result = newControl.getSerialNumber();
                break;
            case NewControlPropName.Placeholder:
                result = newControl.getPlaceholder();
                break;
            case NewControlPropName.MaxLength:
                result = newControl.getMaxLength();
                break;
            default:
                result = newControl.getCustomByPropName(sPropName);
        }

        return result;
    }

    private _setProperty(obj: any): INewControlProperty {
        const keys = Object.keys(obj);
        if (keys.length === 0) {
            return;
        }
        const option: INewControlProperty = {newControlName: undefined, bClearItems: false};
        keys.forEach((key) => {
            const value: any = obj[key];
            switch (key) {
                case NewControlPropName.ReverseEdit:
                    if (typeof value !== 'boolean') {
                        return;
                    }
                    option.isNewControlReverseEdit = value;
                    break;
                case NewControlPropName.DeleteProtect:
                    if (typeof value !== 'boolean') {
                        return;
                    }
                    option.isNewControlCanntDelete = value;
                    break;
                case NewControlPropName.Identifier:
                    if (typeof value !== 'string') {
                        return;
                    }
                    option.identifier = value;
                    break;
                case NewControlPropName.Placeholder:
                    if (typeof value !== 'string') {
                        return;
                    }
                    option.newControlPlaceHolder = value;
                    break;
                case NewControlPropName.EditProtect:
                    if (typeof value !== 'boolean') {
                        return;
                    }
                    option.isNewControlCanntEdit = value;
                    break;
                case NewControlPropName.Hidden:
                    if (typeof value !== 'boolean') {
                        return;
                    }
                    option.isNewControlHidden = value;
                    break;
                case NewControlPropName.Title: {
                    if (typeof value !== 'string') {
                        return;
                    }
                    option.newControlTitle = filterChars2(value);
                    option.showTitle = true;
                    break;
                }
                case NewControlPropName.Helptip: {
                    if (typeof value !== 'string') {
                        return;
                    }
                    option.newControlInfo = value;
                    break;
                }
                case NewControlPropName.SerialNumber: {
                    if (typeof value !== 'string') {
                        return;
                    }
                    option.newControlSerialNumber = value;
                    break;
                }
                case NewControlPropName.MaxLength: {
                    if (typeof value !== 'number') {
                        return;
                    }
                    option.newControlMaxLength = value;
                    break;
                }
                case NewControlPropName.CustomProperty: {
                    option.customProperty = this._getCustomProps(value);
                    break;
                }
                default:
            }
        });
        return option;
    }

    private _getCustomProps(obj: any): any {
        const keys = Object.keys(obj);
        if (keys.length === 0) {
            return;
        }
        const arrs: ICustomProps[] = [];
        keys.forEach((key) => {
            if (!checkXmlElementNameValidity(key)) {
                return;
            }
            const value = obj[key];
            const type = this._getDataType(value);
            arrs.push({name: key, value: value + '', type});
        });
        return arrs;
    }

    private _getDataType(str: any): DataType {
        let type: DataType;
        switch (typeof str) {
            case 'string': {
                type = DataType.String;
                break;
            }
            case 'number': {
                type = DataType.Number;
                break;
            }
            case 'boolean': {
                type = DataType.Boolean;
                break;
            }
            default: {
                return;
            }
        }
        return type;
    }
}
