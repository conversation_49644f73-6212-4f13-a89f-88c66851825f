import { XmlComponent, XmlAttributeComponent } from '../../xml-components';

interface IComponentAttributes {
    readonly val: string;
}

class ComponentAttributes extends XmlAttributeComponent<IComponentAttributes> {
    protected readonly xmlKeys: any = { val: 'w:val' };
}

export class Name extends XmlComponent {
    constructor(value: string) {
        super('w:name');
        this.root.push(new ComponentAttributes({ val: value }));
    }
}
