import {  getPxForMM  } from './util';
import { ShadowFillType } from '../../common/commonDefines';
import { TableBorderLineStyle } from './TableProperty';

/**
 * 文档对齐
 */
export enum AlignType {
    Left, Center, Right, Justify,
}

export enum FontStyleType {
    Normal, Italic,
}

/**
 * 页面属性
 */
export class PageProperty {
    public pageFormat?: string = 'A4';
    public width?: number;  // 210mm
    public height?: number; // 297mm
    public paddingTop?: number; // 25.4mm
    public paddingBottom?: number; // 25.4mm
    public paddingRight?: number; // 31.7mm
    public paddingLeft?: number; // 31.7mm

    constructor(config: PageProperty = {} as PageProperty) {
        this.pageFormat = config.pageFormat || 'A4';
        this.width = config.width || getPxForMM(210);
        this.height = config.height || getPxForMM(297);
        this.paddingTop = config.paddingTop || getPxForMM(25.4);
        this.paddingLeft = config.paddingLeft || getPxForMM(31.7);
        this.paddingRight = config.paddingRight || getPxForMM(31.7);
        this.paddingBottom = config.paddingBottom || getPxForMM(25.4);
    }

}

/**
 * 文档样式类
 */
// export class Styles {
//    private paraProperty: ParaProperty;
// }

export class DocumentColor {
    public r: number = 0;
    public g: number = 0;
    public b: number = 0;

    public bAuto: boolean;

    constructor(r?: number, g?: number, b?: number, bAuto: boolean = false) {
        this.r = r;
        this.g = g;
        this.b = b;

        this.bAuto = bAuto;
    }

    public copy(): DocumentColor {
        return new DocumentColor(this.r, this.g, this.b, this.bAuto);
    }

    public toHex(): string {
        // var rgb = color.split(',');
        // var r = parseInt(rgb[0].split('(')[1]);
        // var g = parseInt(rgb[1]);
        // var b = parseInt(rgb[2].split(')')[0]);
        const hex = '#' + ((1 << 24) + (this.r << 16) + (this.g << 8) + this.b).toString(16).slice(1);
        return hex;
     }

    public toString(): string {
        // return this.colorRGBtoHex();
        return 'rgb(' + this.r + ',' + this.g + ',' + this.b + ')';
    }
}

/**
 * 边框
 */
// tslint:disable-next-line: max-classes-per-file
export class DocumentBorder {
    public color: DocumentColor; // 边框颜色
    public space: number; // 边框的间距
    public size: number; // 边框的宽度
    public value: TableBorderLineStyle; // 边框的样式
    public dashArray: string; // number[];
    public bPrint: boolean;
    public bVisible: boolean;

    constructor(obj?: DocumentBorder) {
        if ( null == obj) {
            this.color = new DocumentColor(0, 0, 0); // default to be black
            this.space = 0;
            this.size = 1;
            this.value = TableBorderLineStyle.Single;
            this.dashArray = null; // '5,5'; //
            this.bPrint = true;
            this.bVisible = true;
        } else {
            this.color = obj.color;
            this.space = obj.space;
            this.size = obj.size;
            this.value = obj.value;
            this.dashArray = obj.dashArray;
            this.bPrint = obj.bPrint;
            this.bVisible = obj.bVisible;
        }
    }

    public copy(): DocumentBorder {
        const newBorder = new DocumentBorder();
        newBorder.color = new DocumentColor(this.color.r, this.color.g, this.color.b, this.color.bAuto);
        newBorder.space = this.space;
        newBorder.size = this.size;
        newBorder.value = this.value;
        newBorder.bPrint = this.bPrint;
        newBorder.bVisible = this.bVisible;

        return newBorder;
    }
}

export enum DocumentContentType {
  Unknown = 0x000,
    Paragraph = 0x0001,
    Table = 0x0002,
    Region = 0x0003,
}

// tslint:disable-next-line: max-classes-per-file
export class DocumentShadow {
    public value: ShadowFillType;  // 是否填充
    public color: DocumentColor;  // 背景填充颜色
    // unifill:

    constructor() {
        this.value = ShadowFillType.None;
        this.color = null;
    }

    public copy(): DocumentShadow {
        const shadow = new DocumentShadow();
        shadow.value = this.value;
        shadow.color = new DocumentColor(this.color.r, this.color.g, this.color.b, this.color.bAuto);

        return shadow;
    }
}

export enum CursorType {
    Text = 'text',
    Point = 'point',
    RowResize = 'row-resize',
    ColResize = 'col-resize',
    EResize = 'e-resize',
    NResize = 'n-resize',
    SeResize = 'se-resize',
    SwResize = 'sw-resize',
    Wait = 'wait',
    Move = 'move',
    ReadOnly = 'not-allowed',
}
