import DocumentFrameBounds from '../FrameBounds';

/**
 * 段落所在页面结构
 * 段落在一个页面就只有一个ParaPage，在多个页面就有多个ParaPage
 */
export default class ParaPage {
    public x: number;
    public y: number;
    public xLimit: number;
    public yLimit: number;
    public startLine: number; // 首行
    public endLine: number; // 末行
    public endInfo: undefined; // todo ：批注信息，结构化元素
    public firstLine: number = 0; // 第一行

    public bounds: DocumentFrameBounds;

    constructor(x: number, y: number, xLimit: number, yLimit: number, firstLine: number) {
        this.x = x;
        this.y = y;
        this.xLimit = xLimit;
        this.yLimit = yLimit;
        this.firstLine = firstLine;
        this.startLine = firstLine;
        this.endLine = firstLine;
        this.bounds = new DocumentFrameBounds(x, y, xLimit, y);
    }

    public setEndLine(endLine: number): void {
        this.endLine = endLine;
    }

    public shift(shiftDx: number, shiftDy: number): void {
        this.x += shiftDx;
        this.y += shiftDy;
        this.xLimit += shiftDx;
        this.yLimit += shiftDy;

        this.bounds.shift(shiftDx, shiftDy);
    }
}
