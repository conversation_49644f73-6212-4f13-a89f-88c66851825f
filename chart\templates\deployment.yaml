apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    app.gitlab.com/app: {{ .Values.annotations.gitlabApp }}
    app.gitlab.com/env: {{ .Values.annotations.gitlabEnv }}
  name: {{ template "trackableappname" . }}
  labels:
    app: {{ template "appname" . }}
    track: "{{ .Values.application.track }}"
    tier: "{{ .Values.application.tier }}"
    chart: "{{ .Chart.Name }}-{{ .Chart.Version | replace "+" "_" }}"
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      app: {{ template "appname" . }}
      track: "{{ .Values.application.track }}"
      tier: "{{ .Values.application.tier }}"
      release: {{ .Release.Name }}
  template:
    metadata:
      annotations:
        app.gitlab.com/app: {{ .Values.annotations.gitlabApp }}
        app.gitlab.com/env: {{ .Values.annotations.gitlabEnv }}
      labels:
        app: {{ template "appname" . }}
        track: "{{ .Values.application.track }}"
        tier: "{{ .Values.application.tier }}"
        release: {{ .Release.Name }}
    spec:
      imagePullSecrets:
{{ toYaml .Values.image.secrets | indent 10 }}
      containers:
      - name: {{ .Chart.Name }}
        image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
        imagePullPolicy: {{ .Values.image.pullPolicy }}
        ports:
        - name: "{{ .Values.service.name }}"
          containerPort: {{ .Values.service.internalPort }}
        resources:
{{ toYaml .Values.resources | indent 12 }}
