

import { XmlAttributeComponent, XmlComponent } from '../../xml-components';
import { Text } from '../run/run-components/text';

export interface ICommentDataAttributesProperties {
    userName: string;
    time: string;
    solved: string;
    content: string;
}

class CommentDataAttributes extends XmlAttributeComponent<ICommentDataAttributesProperties> {
    protected xmlKeys: any = {
        userName: 'userName',
        time: 'time',
        solved: 'solved',
        content: 'content',
    };
}

export class XmlCommentData extends XmlComponent {

    constructor(attrs: ICommentDataAttributesProperties) {
        super('w:reply');
        this.root.push(new CommentDataAttributes(attrs));
    }
}
