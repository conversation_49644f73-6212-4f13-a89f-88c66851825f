export class SizeL {
    public cx: number;
    public cy: number;

    constructor(reader: any, cx?: number, cy?: number) {
        if (reader != null) {
            this.cx = reader.readUint32();
            this.cy = reader.readUint32();
        } else {
            this.cx = cx;
            this.cy = cy;
        }
    }

    public clone(): SizeL {
        return new SizeL(null, this.cx, this.cy);
    }

    public toString(): string {
        return '{cx: ' + this.cx + ', cy: ' + this.cy + '}';
    }
}
