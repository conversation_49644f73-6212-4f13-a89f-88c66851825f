// import * as React from 'react';
// import {PageProperty} from '../../model/StyleProperty';
// import { getPxForMM, getMMFromPx, getPageFormatFromSize } from '../../model/core/util';
// import { DocumentCore } from '../../model/DocumentCore';
// import { Reader } from '../../format/reader/reader';
// import { FormatWriter } from '../../format/writer/writer';
// import { SPACING_CONFIG, COLOR_CONFIG, ImageConfigModalType,
//   EquationType, IMAGE_FLAGS, NewControlType,
//   MENU_LIST, MenuItemIndex, LineSpacingType, LineSpacingRatio, CodeValueItem,
//   NewControlDefaultSetting, INewControlProperty, PAGE_FORMAT, DATE_FORMAT_STRING,
//   ErrorMessages, NewControlContentSecretType, EDITOR_VERSION, DateBoxFormat,
//   ICustomProps, FileSaveType, IParaPropertyRender, FILE_HEADER_LENGTH  } from '../../common/commonDefines';
// import ParaDrawing from '../../model/core/Paragraph/ParaDrawing';
// import MedEquationEditModal from './modals/medEquationEditModal';
// import MedEquationModal from './modals/medEquationModal';
// import { PrintDialog } from './PrintDialog';
// import {GlobalEvent as gEvent } from '../../common/GlobalEvent';
// import { IXmlProps } from './Editor';
// import MedEquation from './medEquation';
// import { ParaElementType } from '../../model/core/Paragraph/ParagraphContent';
// import FontSetDialog from './menuDialog/Font';
// import ParagraphSetDialog from './menuDialog/Paragraph';
// import ImageSetDialog from './menuDialog/Image';
// import SplitCellDialog from './menuDialog/SplitCell';
// import { TableMenuModalType } from '../../common/commonDefines';
// import TableSettingDialog from './menuDialog/Table';
// import * as JSZip from 'jszip';
// import * as zstdCodecPack from 'zstd-codec';
// import { saveAs } from 'file-saver';
// import CharDialog from './menuDialog/SpecialCharacter';
// import NewControlNumDialog from './menuDialog/NewControlNum';
// import ParaCommentDialog from './menuDialog/ParaComment';
// import CustomPropertyDialog from './menuDialog/NewControlCustomProp';
// import { NewControlNumer } from '../../model/core/NewControl/NewControlNum';

// // import {parse as circularParse, stringify as circularStringify} from 'flatted/esm';

// interface IPageMenuProps {
//   pageProperty?: PageProperty;
//   handleClickOutsideDropdown?: any;
//   clickOutsideDropdown?: boolean;
//   changePagePro?: (pageProperty: PageProperty) => void;
//   changeParagraphPro?: (paragraphProperty: any) => void;
//   getParagraphPro?: () => any;
//   refresh: (timeout?: number) => void;
//   testDocument?: any;
//   testDocumentXml?: any;
//   documentCore?: DocumentCore;
//   imageConfigModalType?: number;
//   handleModalState: (type: string) => void;
//   showEquationEditModal?: boolean;
//   equationType: EquationType;
//   host?: any;
//   xmlProps: IXmlProps;
// }

// interface IPageMenuState {
//   showFile?: boolean;
//   showView?: boolean;
//   showInsert?: boolean;
//   showFormat?: boolean;
//   showTools?: boolean;
//   showFormatConfigModal?: boolean;
//   showFileConfigModal?: boolean;
//   showInsertTable?: boolean;
//   showInsertTableConfigModal?: boolean;
//   showHelp?: boolean;
//   showHelpConfigModal?: boolean;
//   showInsertNewControl?: boolean;
//   showInsertNewControlModal?: boolean;
//   showNewControlModal?: boolean;
//   showNewControlExtend?: boolean;
//   pageProperty?: PageProperty;
//   isDisabled?: boolean;
//   pagePropertyForRender?: any;
//   paraProperty?: IParaPropertyRender;
//   leftIndentForRender?: number;
//   isFirstIndent?: boolean;
//   lineSpaceDisabled?: boolean;
//   isFixedLineSpacing?: boolean;
//   inputRepeatedTimes?: number;
//   tableName?: string;
//   tableColsForRender?: number;
//   tableRowsForRender?: number;
//   tableHeadersForRender?: number;
//   isRepeatTableHeader?: boolean;
//   newControlName?: string;
//   newControlInfo?: string;
//   newControlPlaceHolder?: string;
//   newControlType?: number;
//   // isNewControlTypeDisable?: boolean;
//   isNewControlHidden?: boolean;
//   isNewControlCanntDelete?: boolean;
//   isNewControlCanntEdit?: boolean;
//   isNewControlMustInput?: boolean;
//   isNewControlShowBorder?: boolean;
//   isNewControlReverseEdit?: boolean;
//   isNewControlHiddenBackground?: boolean;
//   newControlDisplayType?: number;
//   newControlFixedLength?: number;
//   newControlMaxLength?: number;
//   isMultiSelect?: boolean;
//   // newControlItems?: CodeValueItem[];
//   prefixContent?: string;
//   selectPrefixContent?: string;
//   separator?: string;
//   isNewControlAddItem?: boolean;
//   isNewControlModifyItem?: boolean;
//   newControlItemName?: string;
//   newControlItemValue?: string;
//   dateBoxFormat?: DateBoxFormat;

//   imageWidth?: number;
//   imageHeight?: number;
//   imageSource?: string;
//   imageName?: string;
//   imagePreserveAspectRatio?: boolean;
//   imageRatio?: number;
//   imageSizeLocked?: boolean;
//   imageDeleteLocked?: boolean;

//   showEquationModal?: boolean;
//   showImageError?: boolean;
//   bReflash?: boolean;
//   retrieve?: boolean;
// }

// const pageFormat = [
//   {
//     label: '格式',
//     key: 'pageFormat',
//     options: [ 'A3', 'A4', 'A5', 'B4', 'B5', 'B6', '自定义'],
//   },
// ];

// export default class PageMenu extends React.Component<IPageMenuProps, IPageMenuState> {

//   private openFileRef: React.RefObject<HTMLInputElement>;
//   private insertFileRef: React.RefObject<HTMLInputElement>;
//   private apoToZipRef: React.RefObject<HTMLInputElement>;
//   private zipToApoRef: React.RefObject<HTMLInputElement>;
//   private insertImageRef: React.RefObject<HTMLInputElement>;
//   // equation svg ref
//   private ordEquationRef: React.RefObject<HTMLInputElement>;
//   private fractionEquationRef: React.RefObject<HTMLInputElement>;
//   private menEquationRef: React.RefObject<HTMLInputElement>;

//   private host: any;
//   private newControlItemRef: any;
//   private fontVisible: boolean = false;
//   private paragraphVisible: boolean = false;
//   private imageVisible: boolean = false;
//   private splitCellVisible: boolean = false;
//   private tableVisible: boolean = false;
//   private rightMenuDom: React.RefObject<HTMLDivElement>;
//   private listDemo: any;
//   private stopDocClick: boolean;
//   private isShowRightMenu: boolean;
//   private pageListDom: Element;
//   private rightMenuList: Array<{name: string, hide: boolean, disabled: boolean, className: string, index: number,
//      childs?: any[]}>;
//   private rightMenuOption: {name: string, hide: boolean, disabled: boolean, className: string, index: number};
//   private imageConfigModalType: number;
//   private imageRef: any;
//   private bCursorInNewControl: boolean;
//   private isNewControlTypeDisable: boolean;
//   private curNewControlType: NewControlType;  // 当前newControl的类型
//   private newControlItems: CodeValueItem[];
//   private curNewControlItemPos: number;
//   private newControlProps: INewControlProperty;
//   private printVm: PrintDialog;
//   private chartDialog: CharDialog;
//   private charVisible: boolean;
//   private newControlNumVisible: boolean;
//   private paraCommentVisible: boolean;
//   private docId: number;
//   private newControlCustomPropVisible: boolean;
//   private newControlCustomPropName: string;
//   private newControlCustomProps: ICustomProps[];

//   constructor(props: IPageMenuProps) {
//     super(props);
//     this.openFileRef = React.createRef();
//     this.insertFileRef = React.createRef();
//     this.insertImageRef = React.createRef();
//     this.ordEquationRef = React.createRef();
//     this.fractionEquationRef = React.createRef();
//     this.menEquationRef = React.createRef();
//     this.apoToZipRef = React.createRef();
//     this.zipToApoRef = React.createRef();
//     this.newControlItemRef = React.createRef();
//     this.host = this.props.host;
//     this.docId = this.host.docId;

//     // pagePropertyForRender is just for render
//     const pagePropertyForRender = this.constructPagePropertyForRender(this.props.pageProperty);

//     this.state = {
//       showFile: false,
//       showView: false,
//       showInsert: false,
//       showFormat: false,
//       showTools: false,
//       showFormatConfigModal: false,
//       showFileConfigModal: false,
//       showInsertTable: false,
//       showInsertTableConfigModal: false,
//       showHelp: false,
//       showHelpConfigModal: false,
//       showInsertNewControl: false,
//       showInsertNewControlModal: false,
//       showNewControlModal: false,
//       showNewControlExtend: true,
//       pageProperty: this.props.pageProperty,
//       isDisabled: true,
//       pagePropertyForRender,
//       paraProperty: {
//         indentation: {firstLine: 0, left: 0},
//         lineSpace: {type: LineSpacingType.Single, ratio: null},
//       },
//       leftIndentForRender: 0,
//       isFirstIndent: true,  // default to be true
//       lineSpaceDisabled: true,
//       isFixedLineSpacing: false,
//       inputRepeatedTimes: 1,
//       tableName: '',
//       tableColsForRender: 2,
//       tableRowsForRender: 2,
//       tableHeadersForRender: 0,
//       isRepeatTableHeader: false,
//       imageWidth: 0,
//       imageHeight: 0,
//       imageSource: '',
//       imageName: '',
//       imagePreserveAspectRatio: false,
//       imageRatio: 1,
//       imageDeleteLocked: false,
//       imageSizeLocked: false,

//       showEquationModal: false,
//       showImageError: false,

//       newControlName: '',
//       newControlInfo: '',
//       newControlPlaceHolder: '',
//       newControlType: 1,
//       // isNewControlTypeDisable: false,
//       isNewControlHidden: false,
//       isNewControlCanntDelete: false,
//       isNewControlCanntEdit: false,
//       isNewControlMustInput: false,
//       isNewControlShowBorder: true,
//       isNewControlReverseEdit: false,
//       isNewControlHiddenBackground: false,
//       newControlDisplayType: 1,
//       newControlFixedLength: 0,
//       newControlMaxLength: 0,
//       isMultiSelect: false,
//       prefixContent: '',
//       selectPrefixContent: '',
//       separator: '',
//       isNewControlAddItem: false,
//       isNewControlModifyItem: false,
//       newControlItemName: '',
//       newControlItemValue: '',
//       dateBoxFormat: DateBoxFormat.DateAndHMS,
//       bReflash: false,
//       retrieve: false,
//     };
//     this.bCursorInNewControl = false;
//     this.isNewControlTypeDisable = false;
//     this.curNewControlType = NewControlType.Empty;
//     this.newControlItems = undefined;
//     this.curNewControlItemPos = 0;

//     this.imageConfigModalType = 0;
//     this.rightMenuDom = React.createRef();
//     this.initRightMenu();
//   }

//   public UNSAFE_componentWillReceiveProps(nextProps: IPageMenuProps): void {

//     // console.log("componentWillReceiveProps")
//     if (nextProps.imageConfigModalType !== ImageConfigModalType.None) {
//       const documentCore = nextProps.documentCore;
//       const paraDrawing = documentCore.getSelectedImage() as ParaDrawing;
//       // console.log(paraDrawing);
//       const imageRatio = paraDrawing.width / paraDrawing.height;
//       this.setState({imageWidth: paraDrawing.width, imageHeight: paraDrawing.height,
//                      imageSource: paraDrawing.src, imageName: paraDrawing.name,
//                      imagePreserveAspectRatio: paraDrawing.preserveAspectRatio,
//                      imageSizeLocked: paraDrawing.sizeLocked,
//                      imageDeleteLocked: paraDrawing.deleteLocked,
//                      imageRatio});
//     }

//     // console.log(JSON.stringify(this.props.pageProperty) )
//     // console.log(JSON.stringify(nextProps.pageProperty) )
//     // console.log(JSON.stringify(this.state.pageProperty))

//     if (this.documentGeneratedFromLoading(nextProps)) {

//       // modal should be correct at this step. Take care of modal ui
//       const pagePropertyForRender = this.constructPagePropertyForRender(nextProps.pageProperty);

//       // make pageFormat correct
//       // tslint:disable-next-line: max-line-length
//       pagePropertyForRender.pageFormat = getPageFormatFromSize(pagePropertyForRender.width, pagePropertyForRender.height);
//       // console.log(pagePropertyForRender)

//       this.setState({pageProperty: nextProps.pageProperty, pagePropertyForRender});
//     }

//     if (nextProps.clickOutsideDropdown) {
//       this.setState({showFile: false, showInsert: false, showFormat: false,
//                     showTools: false, showInsertTable: false, showHelp: false}, () => {
//         this.props.handleClickOutsideDropdown();
//       });
//     }

//   }

//   public componentWillMount(): void {
//     // should trigger only once

//     // treat hanging indent case and assign leftIndentForRender val
//     // set current paraProperty according to current paragraph
//     if (this.props.getParagraphPro) {
//       this.setParaPropertyState();
//     }
//   }

//   public componentWillUnmount(): void {
//     document.removeEventListener('keydown', this.docPrint);
//     // this.listDemo.removeEventListener('contextmenu', this.contextmenu);
//     document.removeEventListener('click', this.docClickEvent);
//     this.listDemo.removeEventListener('scroll', this.scrollHandler);
//     this.rightMenuDom.current.addEventListener('mousedown', this.handerRightMenu);
//   }

//   public componentDidMount(): void {
//     document.addEventListener('keydown', this.docPrint, false);
//     this.listDemo = document.querySelector('.ReactVirtualized__Grid');
//     // this.listDemo.addEventListener('contextmenu', this.contextmenu, false);

//     document.addEventListener('click', this.docClickEvent);
//     gEvent.addEvent(this.docId, 'setToRightMenu', this.setToRightMenu);
//     this.listDemo.addEventListener('scroll', this.scrollHandler);
//     this.rightMenuDom.current.addEventListener('mousedown', this.handerRightMenu);

//     window.onresize = this.scrollHandler;
//     window.onblur = this.scrollHandler;
//   }

//   public render(): JSX.Element {
//     // if (this.props.imageConfigModalType !== ImageConfigModalType.None) {
//     //   let documentCore = this.props.documentCore;
//     //   console.log(documentCore.getSelectedImage());
//     // }

//     const {dropdown, menu} = COLOR_CONFIG;
//     const {menuHeight} = SPACING_CONFIG;
//     // const { pageProperty } = this.state;
//     // console.log(this.props.imageConfigModalType)
//     const equationRefs = {fractionEquationRef: this.fractionEquationRef, ordEquationRef: this.ordEquationRef,
//                           menEquationRef: this.menEquationRef};
//     const attrCheckboxes = (
//       // <React.Fragment>
//       <div className='detail-block'>
//         <div className='detail-item'>
//             <input
//               id='hidden-new-control'
//               type='checkbox'
//               className='newcontrol'
//               checked={this.state.isNewControlHidden}
//               onChange={this.handleNewControlPropValueChange}
//               disabled={true}
//             />
//             <span className='text-disabled'>隐藏</span>
//         </div>

//         <div className='detail-item'>
//             <input
//               id='cannt-delete'
//               type='checkbox'
//               className='newcontrol'
//               checked={this.state.isNewControlCanntDelete}
//               onChange={this.handleNewControlPropValueChange}
//               disabled={false}
//             />
//             <span className='text-disabled'>无法删除</span>
//           </div>

//         <div className='detail-item'>
//             <input
//               id='cannt-edit'
//               type='checkbox'
//               className='newcontrol'
//               checked={this.state.isNewControlCanntEdit}
//               onChange={this.handleNewControlPropValueChange}
//               disabled={false}
//             />
//             <span className='cannt-edit'>不可编辑</span>
//           </div>

//         <div className='detail-item'>
//             <input
//               id='must-input'
//               type='checkbox'
//               className='newcontrol'
//               checked={this.state.isNewControlMustInput}
//               onChange={this.handleNewControlPropValueChange}
//               disabled={false}
//             />
//             <span className='text-disabled'>必填项</span>
//           </div>

//         <div className='detail-item'>
//             <input
//               id='hidden-border'
//               type='checkbox'
//               className='newcontrol'
//               checked={!this.state.isNewControlShowBorder}
//               onChange={this.handleNewControlPropValueChange}
//               disabled={false}
//             />
//             <span className='hidden-border'>边框隐藏</span>
//           </div>

//         <div className='detail-item'>
//             <input
//               id='revert-edit'
//               type='checkbox'
//               className='newcontrol'
//               checked={this.state.isNewControlReverseEdit}
//               onChange={this.handleNewControlPropValueChange}
//               disabled={false}
//             />
//             <span className='revert-edit'>反向编辑</span>
//           </div>

//         <div className='detail-item'>
//             <input
//               id='hidden-background'
//               type='checkbox'
//               className='newcontrol'
//               checked={this.state.isNewControlHiddenBackground}
//               onChange={this.handleNewControlPropValueChange}
//               disabled={false}
//             />
//             <span className='hidden-background'>背景色隐藏</span>
//         </div>

//         <div className='detail-item'>
//           <button className='detail-item-custom-prop' onClick={this.addCustomProps.bind(this)}>自定义属性</button>
//         </div>
//       </div>
//     );

//     return (
//       <div className='emr-editor-menu' onClick={this.toggleShow} style={{backgroundColor: menu, height: menuHeight}}>
//         <div className='menu-tab'>
//           <div className='dropdown-cell first-menu' onClick={this.toggleTab}>文件</div>
//           <div
//             className='dropdown'
//             style={{display: this.state.showFile ? 'inline-block' : 'none', backgroundColor: dropdown}}
//           >
//             <div onClick={this.handleChildClick} className='dropdown-cell'>新建</div>
//             <div onClick={this.handleChildClick} className='dropdown-cell'>打开</div>
//             <div onClick={this.handleChildClick} className='dropdown-cell'>保存</div>
//             <div onClick={this.handleChildClick} className='dropdown-cell'>页面设置</div>
//             <div onClick={this.handleChildClick} className='dropdown-cell'>批注列表</div>
//             <div onClick={this.handlePrint.bind(this, false)} className='dropdown-cell'>打印预览</div>
//             <div onClick={this.handlePrint.bind(this, true)} className='dropdown-cell'>打印</div>
//             <input
//               id='openFile'
//               type='file'
//               style={{visibility: 'hidden'}}
//               ref={this.openFileRef}
//               onChange={this.handleChangeFile}
//             />
//           </div>
//         </div>
//         <div className='menu-tab'>编辑</div>
//         <div className='menu-tab'>
//           <div className='dropdown-cell first-menu' onClick={this.toggleTab}>视图</div>
//           <div
//             className='dropdown'
//             style={{display: this.state.showView ? 'inline-block' : 'none', backgroundColor: dropdown}}
//           >
//             <div onClick={this.handleChildClick} className='dropdown-cell'>显示格式标记</div>
//           </div>
//         </div>
//         <div className='menu-tab'>
//           <div className='dropdown-cell first-menu' onClick={this.toggleTab}>插入</div>
//           <div
//             className='dropdown'
//             style={{display: this.state.showInsert ? 'inline-block' : 'none', backgroundColor: dropdown}}
//           >
//             <div onClick={this.handleChildClick} className='dropdown-cell newcontrol'>结构化元素</div>
//             {/* <div onMouseEnter={this.handleChildFocus} className='dropdown-cell'>结构化元素</div> */}
//               <div className='dropdown' style={{display: this.state.showInsertNewControl ? 'inline-block' : 'none', backgroundColor: dropdown}}
//               >
//                     <div onClick={this.handleChildClick} className='dropdown-cell'>文本框</div>
//                     <div onClick={this.handleChildClick} className='dropdown-cell'>数值框</div>
//                     <div onClick={this.handleChildClick} className='dropdown-cell'>复选框</div>
//                     <div onClick={this.handleChildClick} className='dropdown-cell'>单选按钮</div>
//                     <div onClick={this.handleChildClick} className='dropdown-cell'>单选框/多选框</div>
//                     <div onClick={this.handleChildClick} className='dropdown-cell'>日期框</div>
//               </div>
//             <div onClick={this.handleChildClick} className='dropdown-cell'>分页符</div>
//             <div
//               onClick={this.handleChildClick}
//               className={IMAGE_FLAGS.isImageOnClick ? 'dropdown-cell opacity' : 'dropdown-cell'}
//             >图片
//             </div>
//             <div
//               onClick={this.handleChildClick}
//               className={IMAGE_FLAGS.isImageOnClick ? 'dropdown-cell opacity' : 'dropdown-cell'}
//             >医学公式
//             </div>
//             <div onClick={this.handleChildClick} className='dropdown-cell'>批注</div>
//             <div onClick={this.handleChildClick} className='dropdown-cell'>特殊字符</div>
//             <div onClick={this.handleChildClick} className='dropdown-cell'>文件</div>
//             <input
//               id='insertImage'
//               type='file'
//               style={{visibility: 'hidden'}}
//               ref={this.insertImageRef}
//               accept='image/*'
//               onChange={this.handleChangeFile}
//             />
//             <input
//               id='insertFile'
//               type='file'
//               style={{display: 'none'}}
//               ref={this.insertFileRef}
//               onChange={this.handleChangeFile}
//             />
//           </div>
//         </div>
//         <div className='menu-tab'>
//           <div className='dropdown-cell first-menu' onClick={this.toggleTab}>格式</div>
//           <div
//             className='dropdown'
//             style={{display: this.state.showFormat ? 'inline-block' : 'none', backgroundColor: dropdown}}
//           >
//             <div onClick={this.handleChildClick} className='dropdown-cell'>字符</div>
//             <div onClick={this.handleChildClick} className='dropdown-cell'>段落</div>
//           </div>
//         </div>
//         <div className='menu-tab'>
//           <div className='dropdown-cell first-menu' onClick={this.toggleTab}>工具</div>
//           <div
//             className='dropdown'
//             style={{display: this.state.showTools ? 'inline-block' : 'none', backgroundColor: dropdown}}
//           >
//             <div onClick={this.handleChildClick} className='dropdown-cell'>设计模式</div>
//           </div>
//         </div>
//         <div className='menu-tab'>
//           <div className='dropdown-cell first-menu' onClick={this.toggleTab}>表格</div>
//           <div
//             className='dropdown'
//             style={{display: this.state.showInsertTable ? 'inline-block' : 'none', backgroundColor: dropdown}}
//           >
//             <div onClick={this.handleChildClick} className='dropdown-cell'>插入表格</div>
//             <div onClick={this.handleChildClick} className='dropdown-cell'>表格属性</div>
//           </div>
//         </div>
//         <div className='menu-tab'>
//           <div className='dropdown-cell first-menu' onClick={this.toggleTab}>帮助</div>
//           <div
//             className='dropdown'
//             style={{display: this.state.showHelp ? 'inline-block' : 'none', backgroundColor: dropdown}}
//           >
//             <div onClick={this.handleChildClick} className='dropdown-cell'>文字输入</div>
//             <div onClick={this.handleChildClick} className='dropdown-cell'>apo转为zip</div>
//             <input
//               id='apoToZip'
//               type='file'
//               style={{visibility: 'hidden'}}
//               ref={this.apoToZipRef}
//               onChange={this.handleChangeFile}
//             />
//             <div onClick={this.handleChildClick} className='dropdown-cell'>zip转为apo</div>
//             <input
//               id='zipToApo'
//               type='file'
//               style={{visibility: 'hidden'}}
//               ref={this.zipToApoRef}
//               onChange={this.handleChangeFile}
//             />
//           </div>
//         </div>

//         {/* page config window */}
//         <div className='config-window-container' style={{display: this.state.showFileConfigModal ? 'block' : 'none'}}>
//           <div className='config-window'>

//             <div className='config-format-container'>
//               <div className='config-descriptor'>纸张格式(厘米)</div>
//               {this.renderPagefomtConfig(pageFormat, true)}
//               <div className='detail-block'>
//                   <label htmlFor='width'>宽度：</label>
//                   <input
//                     type='number'
//                     min={10}
//                     id={'width'}
//                     value={this.state.pagePropertyForRender.width}
//                     onChange={this.handleChangePageInfo}
//                     disabled={this.state.isDisabled}
//                   />
//               </div>
//               <div className='detail-block'>
//                   <label htmlFor='height'>高度：</label>
//                   <input
//                     id={'height'}
//                     type='number'
//                     min={10}
//                     value={this.state.pagePropertyForRender.height}
//                     onChange={this.handleChangePageInfo}
//                     disabled={this.state.isDisabled}
//                   />
//               </div>
//             </div>

//             <div className='config-spacing-container'>
//               {this.renderPageBorder()}
//             </div>

//             <div className='button-container'>
//               <button className='button' onClick={this.handleButtonClick.bind(this, 'pageConfig')}>取消</button>
//               <button className='button' onClick={this.handleButtonClick.bind(this, 'pageConfig')}>确定</button>
//             </div>

//           </div>

//         </div>

//         {/* paragraph window */}
//         <div
//           className='config-window-container'
//           style={{display: this.state.showFormatConfigModal ? 'block' : 'none'}}
//         >
//           <div className='config-window paragraph-window'>

//             <div className='config-descriptor'>缩进</div>
//             <div className='detail-block'>
//                 <label htmlFor='left-indent' className='left-cell'>左侧缩进：</label>
//                 <input
//                   type='number'
//                   step={0.5}
//                   id={'left-indent'}
//                   className='right-cell'
//                   value={this.state.leftIndentForRender}
//                   onChange={this.handleIndentationValueChange}
//                 /> cm
//             </div>
//             <div className='detail-block'>
//                 <select
//                   name={'special-indent-select'}
//                   className='left-cell'
//                   value={this.state.isFirstIndent ? 'first' : 'hanging'}
//                   onChange={this.switchSpecialIndentType}
//                 >
//                   <option value='first'>首行缩进</option>
//                   <option value='hanging'>悬挂缩进</option>
//                 </select>
//                 <input
//                   id={'special-indent'}
//                   type='number'
//                   min={0}
//                   step={1}
//                   className='right-cell'
//                   value={Math.abs(this.state.paraProperty.indentation.firstLine)}
//                   onChange={this.handleIndentationValueChange}
//                 /> cm
//             </div>

//             <div className='config-descriptor'>行距</div>
//             <div className='detail-block'>
//                 <select
//                   name={'line-space-select'}
//                   className='left-cell'
//                   value={this.state.paraProperty.lineSpace.type}
//                   onChange={this.switchLineSpaceType}
//                 >
//                   <option value={LineSpacingType.Single}>单倍行距</option>
//                   <option value={LineSpacingType.SingeHalf}>1.5倍行距</option>
//                   <option value={LineSpacingType.Double}>2倍行距</option>
//                   <option value={LineSpacingType.Multi}>多倍行距</option>
//                   <option value={LineSpacingType.Min}>最小值</option>
//                   <option value={LineSpacingType.Fixed}>固定值</option>
//                 </select>
//                 <input
//                   id={'line-space'}
//                   type='number'
//                   disabled={this.state.lineSpaceDisabled}
//                   min={this.state.isFixedLineSpacing ? 0 : 1}
//                   step={this.state.isFixedLineSpacing ? 0.05 : 0.5}
//                   className='right-cell'
//                   value={this.state.paraProperty.lineSpace.ratio}
//                   onChange={this.handleLineSpaceValueChange}
//                 />{this.state.isFixedLineSpacing ? ' cm' : '倍'}
//             </div>

//             <div className='button-container'>
//               <button className='button' onClick={this.handleButtonClick.bind(this, 'formatParagraph')}>取消</button>
//               <button className='button' onClick={this.handleButtonClick.bind(this, 'formatParagraph')}>确定</button>
//             </div>

//           </div>

//         </div>

//         {/* insert new table */}
//         <div
//           className='config-window-container'
//           style={{display: this.state.showInsertTableConfigModal ? 'block' : 'none'}}
//         >
//           <div className='config-window table-window'>

//             <div className='config-descriptor'>插入表格</div>
//             <div className='detail-block'>
//                 <label htmlFor='table-name' className='table-name'>名称(A)：</label>
//                 <input
//                   type='string'
//                   id={'table-name'}
//                   className='table'
//                   value={this.state.tableName}
//                   onChange={this.handleTableValueChange}
//                 />
//             </div>
//             <div className='detail-block'>
//                 <label htmlFor='table-cols' className='cols-cell'>列数(C)：</label>
//                 <input
//                   type='number'
//                   min={1}
//                   step={1}
//                   id={'table-cols'}
//                   className='table'
//                   value={this.state.tableColsForRender}
//                   onChange={this.handleTableValueChange}
//                 />
//             </div>
//             <div className='detail-block'>
//                 <label htmlFor='table-rows' className='rows-cell'>行数(R)：</label>
//                 <input
//                   type='number'
//                   min={1}
//                   step={1}
//                   id={'table-rows'}
//                   className='table'
//                   value={this.state.tableRowsForRender}
//                   onChange={this.handleTableValueChange}
//                 />
//             </div>
//             <div className='detail-block'>
//                 <div className='detail-item'>
//                 <label htmlFor='table-header' className='header-num'>表头：</label>
//                 <input
//                   id='repeat-table-header'
//                   type='checkbox'
//                   className='table'
//                   checked={this.state.isRepeatTableHeader}
//                   onChange={this.handleTableValueChange}
//                   disabled={false}
//                 />
//                 <span className='text-disabled'>重复标题行</span>
//                 </div>
//                 <div className='detail-item'>
//                 <span className='header-num'>前</span>
//                 <input
//                   type='number'
//                   min={0}
//                   step={1}
//                   id={'table-header'}
//                   className='table'
//                   value={this.state.tableHeadersForRender}
//                   onChange={this.handleTableValueChange}
//                 />
//                 <span className='text-disabled'>行</span>
//                 </div>
//             </div>

//             <div className='button-container'>
//               <button className='button' onClick={this.handleButtonClick.bind(this, 'insertNewTable')}>取消</button>
//               <button className='button' onClick={this.handleButtonClick.bind(this, 'insertNewTable')}>确定</button>
//             </div>
//           </div>
//         </div>

//         {/* input test window */}
//         <div className='config-window-container' style={{display: this.state.showHelpConfigModal ? 'block' : 'none'}}>
//           <div className='config-window input-window'>
//             <div className='input-area'>
//               {/* <p>input example: ["主诉：请输入主诉20字以内空格     在这里有看见了吗？"]</p> */}
//               <p>请输入段落内容:</p>
//               {/* <input className="test-text"/> */}
//               <div>["<textarea rows={5} className='test-text' />"]</div>
//             </div>

//             <div className='button-area'>
//               <p>重复次数:</p>
//               <input
//                 type='number'
//                 min={1}
//                 step={1}
//                 className='test-paraCount'
//                 value={this.state.inputRepeatedTimes}
//                 onChange={this.handleRepeatedTimesChange}
//               />

//               <div className='button-container'>
//                 <button className='button add' onClick={this.handleButtonClick.bind(this, 'helpInput')}>添加</button>
//                 <button className='button' onClick={this.handleButtonClick.bind(this, 'helpInput')}>取消</button>
//                 <button className='button' onClick={this.handleButtonClick.bind(this, 'helpInput')}>确定</button>
//               </div>

//             </div>

//           </div>
//         </div>

//         {/* image config modal*/}
//         <div
//           className='config-window-container'
//           style={{display: this.imageConfigModalType === ImageConfigModalType.Basic ? 'block' : 'none'}}
//         >
//           <div className='config-window'>

//             <div className='config-descriptor'>
//               图片属性
//             </div>
//             <div className='detail-block image-detail-block'>
//               <div className='detail-item'>
//                 <label htmlFor='image-name'>图片名称：</label>
//                 <input id='image-name' value={this.state.imageName} onChange={this.handleImagePropertyChange}/>
//               </div>
//               <div className='detail-item'>
//                 <label htmlFor='image-path'>图片路径：</label>
//                 {/* <input id='image-path' value={this.state.imageSource} /> */}
//               </div>
//             </div>

//             <div className='detail-block image-detail-block'>
//               <div className='detail-item'>
//                 <label htmlFor='image-width' className={this.state.imageSizeLocked ? 'text-disabled' : ''}>宽度：</label>
//                 <input
//                   type='number'
//                   min={1}
//                   id='image-width'
//                   value={this.state.imageWidth}
//                   onChange={this.handleImagePropertyChange}
//                   disabled={this.state.imageSizeLocked ? true : false}
//                 />
//               </div>
//               <div className='detail-item'>
//                 <label htmlFor='image-height' className={this.state.imageSizeLocked ? 'text-disabled' : ''}>高度：</label>
//                 <input
//                   type='number'
//                   min={1}
//                   id='image-height'
//                   value={this.state.imageHeight}
//                   onChange={this.handleImagePropertyChange}
//                   disabled={this.state.imageSizeLocked ? true : false}
//                 />
//               </div>
//             </div>

//             <div className='detail-block image-detail-block'>
//               <input
//                 id='image-ratio'
//                 type='checkbox'
//                 name='ratio'
//                 className='image-checkbox'
//                 checked={this.state.imagePreserveAspectRatio}
//                 onChange={this.handleImagePropertyChange}
//                 disabled={this.state.imageSizeLocked ? true : false}
//               />
//               <span className={this.state.imageSizeLocked ? 'text-disabled' : ''}>保持比例</span>
//             </div>

//             <div
//               className='error-popup'
//               style={{display: (this.state.showImageError) ? 'block' : 'none'}}
//             >"{this.state.imageName}" 名称已经存在.
//             </div>

//             <div className='button-container'>
//               <button className='button' onClick={this.handleButtonClick.bind(this, 'imageBasic')}>取消</button>
//               <button className='button' onClick={this.handleButtonClick.bind(this, 'imageBasic')}>确定</button>
//             </div>

//           </div>
//         </div>

//         <div
//           className='config-window-container'
//           style={{display: this.props.imageConfigModalType === ImageConfigModalType.Advanced ? 'block' : 'none'}}
//         >
//           <div className='config-window'>
//             <div className='config-descriptor'>高级</div>
//             <div className='detail-block image-detail-block'>
//               <div className='detail-item'>
//                 <input
//                   id='position-lock'
//                   type='checkbox'
//                   name='position-lock'
//                   className='image-checkbox'
//                   checked={false}
//                   onChange={this.handleImagePropertyChange}
//                   disabled={true}
//                 />
//                 <span className='text-disabled'>位置保护</span>
//               </div>
//               <div className='detail-item'>
//                 <input
//                   id='size-lock'
//                   type='checkbox'
//                   name='size-lock'
//                   className='image-checkbox'
//                   checked={this.state.imageSizeLocked}
//                   onChange={this.handleImagePropertyChange}
//                 />大小保护
//               </div>
//             </div>

//             <div className='detail-block image-detail-block'>
//               <div className='detail-item'>
//                 <input
//                   id='delete-lock'
//                   type='checkbox'
//                   name='delete-lock'
//                   className='image-checkbox'
//                   checked={this.state.imageDeleteLocked}
//                   onChange={this.handleImagePropertyChange}
//                   disabled={true}
//                 />
//                 <span className='text-disabled'>删除保护</span>
//               </div>
//               <div className='detail-item'>
//                 <input
//                   id='copy-lock'
//                   type='checkbox'
//                   name='position-lock'
//                   className='image-checkbox'
//                   checked={false}
//                   onChange={this.handleImagePropertyChange}
//                   disabled={true}
//                 />
//                 <span className='text-disabled'>拷贝保护</span>
//               </div>
//             </div>

//             <div className='button-container'>
//               <button className='button' onClick={this.handleButtonClick.bind(this, 'imageAdvanced')}>取消</button>
//               <button className='button' onClick={this.handleButtonClick.bind(this, 'imageAdvanced')}>确定</button>
//             </div>

//           </div>
//         </div>

//         {/* equation template */}
//         <MedEquation refs={equationRefs} />

//         {/* equation modal*/}
//         <div className='config-window-container' style={{display: this.state.showEquationModal ? 'block' : 'none'}}>
//           <MedEquationModal
//             documentCore={this.props.documentCore}
//             closeModal={this.closeModal}
//             addInlineImage={this.addInlineImage}
//             showEquationModal={this.state.showEquationModal}
//             equationRefs={equationRefs}
//           />
//         </div>

//         {/* equation edit modal */}
//         <div className='config-window-container' style={{display: this.props.showEquationEditModal ? 'block' : 'none'}}>
//           <MedEquationEditModal
//             equationType={this.props.equationType}
//             refresh={this.props.refresh}
//             documentCore={this.props.documentCore}
//             handleModalState={this.props.handleModalState}
//             showEquationEditModal={this.props.showEquationEditModal}
//             equationRefs={equationRefs}
//           />
//         </div>

//         {/* insert NewTextControl */}
//         <div
//           className='new-control'
//           style={{display: (this.state.showInsertNewControlModal || this.state.showNewControlModal) &&
//             (NewControlType.TextBox === this.curNewControlType || NewControlType.Section === this.curNewControlType) ? 'block' : 'none'}}
//         >
//           <div className='config-window'>
//             <div className='config-descriptor'>文本框</div>
//               <label htmlFor='new-control-common' className='new-control-common'>常规：</label>
//               <div className='detail-block'>
//                   <label htmlFor='new-control-name' className='new-control-name'>名称：</label>
//                   <input
//                     type='string'
//                     id={'new-control-name'}
//                     className='newcontrol'
//                     value={this.state.newControlName}
//                     onChange={this.handleNewControlPropValueChange}
//                   />
//               </div>
//               <div className='detail-block'>
//                   <label htmlFor='new-control-info' className='new-control-info'>提示信息：</label>
//                   <input
//                     type='string'
//                     id={'new-control-info'}
//                     className='newcontrol'
//                     value={this.state.newControlInfo}
//                     onChange={this.handleNewControlPropValueChange}
//                   />
//               </div>
//               <div className='detail-block'>
//                   <label htmlFor='place-holder' className='place-holder'>占位符：</label>
//                   <input
//                     type='string'
//                     id={'place-holder'}
//                     className='newcontrol'
//                     value={this.state.newControlPlaceHolder}
//                     onChange={this.handleNewControlPropValueChange}
//                   />
//               </div>
//               <div className='detail-block'>
//                   <label htmlFor='new-control-type' className='new-control-type'>类型：</label>
//                   <select
//                     name={'new-control-type'}
//                     className='new-control-type'
//                     value={this.state.newControlType}
//                     onChange={this.handleNewControlPropValueChange}
//                     disabled={this.isNewControlTypeDisable}
//                   >
//                     <option value={NewControlType.TextBox}>简单元素</option>
//                     <option value={NewControlType.Section}>节</option>
//                   </select>
//               </div>

//               <label htmlFor='new-control-porperty' className='new-control-porperty'>属性：</label>
//               {attrCheckboxes}

//               <div
//                 className='new-control-extend-property'
//                 style={{display: this.state.showNewControlExtend ? 'block' : 'none'}}
//               >
//                 <label htmlFor='new-control-extend' className='new-control-extend'>扩展：</label>
//                 <div className='detail-block'>
//                     <label htmlFor='display-type' className='display-type'>保密显示：</label>
//                     <select
//                       name={'display-type'}
//                       className='display-type'
//                       value={this.state.newControlDisplayType}
//                       onChange={this.handleNewControlPropValueChange}
//                     >
//                       <option value={NewControlContentSecretType.DontSecret}>不保密</option>
//                       <option value={NewControlContentSecretType.AllSecret}>全部保密</option>
//                       <option value={NewControlContentSecretType.PartSecret}>部分保密</option>
//                     </select>
//                 </div>
//                 <div className='detail-block'>
//                     <label htmlFor='fixed-length' className='fixed-length'>固定长度：</label>
//                     <input
//                       type='number'
//                       min={1}
//                       step={1}
//                       id={'fixed-length'}
//                       className='newcontrol'
//                       value={this.state.newControlFixedLength}
//                       onChange={this.handleNewControlPropValueChange}
//                       disabled={true}
//                     />
//                     <span className='text-disabled'>字符</span>
//                 </div>
//                 <div className='detail-block'>
//                     <label htmlFor='max-length' className='max-length'>最大长度：</label>
//                     <input
//                       type='number'
//                       min={0}
//                       step={1}
//                       id={'max-length'}
//                       className='newcontrol'
//                       value={this.state.newControlMaxLength}
//                       onChange={this.handleNewControlPropValueChange}
//                     />
//                     <span className='text-disabled'>字符</span>
//                 </div>
//               </div>

//             <div className='button-container'>
//               <button className='button' onClick={this.handleButtonClick.bind(this, 'insertNewControl')}>取消</button>
//               <button className='button' onClick={this.handleButtonClick.bind(this, 'insertNewControl')}>确定</button>
//             </div>
//           </div>
//         </div>

//         <div
//           className='new-control-combox'
//           style={{display: (this.state.showInsertNewControlModal || this.state.showNewControlModal) &&
//             (NewControlType.Combox === this.curNewControlType || NewControlType.MultiCombox === this.curNewControlType
//             || NewControlType.ListBox === this.curNewControlType || NewControlType.MultiListBox === this.curNewControlType) ? 'block' : 'none'}}
//         >
//           <div className='config-window'>
//             <div className='config-descriptor'>多选框</div>
//               <label htmlFor='new-control-common' className='new-control-common'>常规：</label>
//               <div className='detail-block'>
//                   <label htmlFor='new-control-name' className='new-control-name'>名称：</label>
//                   <input
//                     type='string'
//                     id={'new-control-name'}
//                     className='right-cell'
//                     value={this.state.newControlName}
//                     onChange={this.handleNewControlPropValueChange}
//                   />
//                   <label htmlFor='new-control-info' className='new-control-info' style={{marginLeft: 15}}>提示信息：</label>
//                   <input
//                     type='string'
//                     id={'new-control-info'}
//                     className='right-cell'
//                     value={this.state.newControlInfo}
//                     onChange={this.handleNewControlPropValueChange}
//                   />
//               </div>

//               <div className='detail-block'>
//                   <label htmlFor='place-holder' className='place-holder'>占位符：</label>
//                   <input
//                     type='string'
//                     id={'place-holder'}
//                     className='right-cell'
//                     value={this.state.newControlPlaceHolder}
//                     onChange={this.handleNewControlPropValueChange}
//                   />

//                   <label htmlFor='new-control-type' className='new-control-type'  style={{marginLeft: 10}}>类型：</label>
//                   <div className='detail-item'>
//                     <select
//                       name={'new-control-type'}
//                       className='new-control-type'
//                       value={this.state.newControlType}
//                       onChange={this.handleNewControlPropValueChange}
//                       disabled={this.isNewControlTypeDisable}
//                       style={{marginLeft: 30}}
//                     >
//                       <option value={NewControlType.ListBox}>下拉框</option>
//                       <option value={this.getNewComboxType()}>组合框</option>
//                     </select>
//                   </div>
//                   <div className='detail-item' style={{marginLeft: 220}}>
//                     <input
//                       id='is-multi-combo-box'
//                       type='checkbox'
//                       className='newcontrol'
//                       checked={this.state.isMultiSelect}
//                       onChange={this.handleNewControlPropValueChange}
//                       disabled={this.isNewControlTypeDisable}
//                     />
//                     <span className='text-disabled'>支持多选</span>
//                   </div>
//               </div>

//               <label htmlFor='new-control-porperty' className='new-control-porperty'>属性：</label>
//               {attrCheckboxes}
//               <div className='detail-block'>
//                   <div className='detail-item'>
//                     <input
//                       id='is-retrieve-combo-box'
//                       type='checkbox'
//                       className='newcontrol'
//                       checked={this.state.retrieve}
//                       onChange={this.handleNewControlPropValueChange}
//                     />
//                     <span className='text-disabled'>索引检索</span>
//                   </div>
//               </div>

//               <label htmlFor='new-control-porperty' className='new-control-list-porperty'>下拉选项</label>
//               <div className='detail-block'>
//                 <fieldset className='table-container'>
//                   <table className='new-control-list-items' id={'new-control-list-items'}
//                     onMouseDown={this.handleMouseDown.bind(this, 'select-new-control-item')}>
//                     <thead>
//                     <tr>
//                       <th className='table-th' scope='col'>显示名称</th>
//                       <th className='table-th' scope='col'>值</th>
//                     </tr>
//                     </thead>
//                   </table>
//                 </fieldset>
//                 <button className='button' onClick={this.handleButtonClick.bind(this, 'insertNewItem')}>添加</button>
//                 <button className='button' disabled={this.isNewControlItemsEmpty()} onClick={this.handleButtonClick.bind(this, 'modifyNewItem')}>修改</button>
//                 <button className='button' disabled={this.isNewControlItemsEmpty()} onClick={this.handleButtonClick.bind(this, 'deleteNewItem')}>删除</button>
//                 <button className='button' disabled={this.isNewControlItemsEmpty()} onClick={this.handleButtonClick.bind(this, 'upNewItem')}>上移</button>
//                 <button className='button' disabled={this.isNewControlItemsEmpty()} onClick={this.handleButtonClick.bind(this, 'downNewItem')}>下移</button>
//               </div>
//               <div
//                 className='new-control-extend-property'
//                 style={{display: this.state.showNewControlExtend ? 'block' : 'none'}}
//               >
//                 <label htmlFor='new-control-extend' className='new-control-extend'>固有属性：</label>
//                 <div className='detail-block'>
//                     <label htmlFor='select-prefix' className='fixed-length'>选中项前缀字符：</label>
//                     <input
//                       type='string'
//                       id={'select-prefix-content'}
//                       className='newcontrol'
//                       value={this.state.selectPrefixContent}
//                       onChange={this.handleNewControlPropValueChange}
//                     />
//                 </div>
//                 <div className='detail-block'>
//                     <label htmlFor='max-length' className='max-length'>未选中项前缀字符：</label>
//                     <input
//                       type='string'
//                       id={'prefix-content'}
//                       className='newcontrol'
//                       value={this.state.prefixContent}
//                       onChange={this.handleNewControlPropValueChange}
//                     />
//                 </div>
//                 <div className='detail-block'>
//                     <label htmlFor='max-length' className='max-length'>内容分隔符：</label>
//                     <input
//                       type='string'
//                       id={'separator'}
//                       className='newcontrol'
//                       value={this.state.separator}
//                       onChange={this.handleNewControlPropValueChange}
//                     />
//                 </div>
//               </div>

//             <div className='button-container'>
//               <button className='button' onClick={this.handleButtonClick.bind(this, 'insertNewControl')}>取消</button>
//               <button className='button' onClick={this.handleButtonClick.bind(this, 'insertNewControl')}>确定</button>
//             </div>
//           </div>
//         </div>

//         <div
//           className='new-control config-window-container'
//           style={{display: (this.state.showInsertNewControlModal || this.state.showNewControlModal) &&
//             NewControlType.DateTimeBox === this.curNewControlType ? 'block' : 'none'}}
//         >
//           <div className='config-window'>
//             <div className='config-descriptor'>日期框</div>

//             <div className='detail-block-container'>
//               <label htmlFor='new-control-common' className='new-control-common new-control-label'>常规：</label>
//               <div className='detail-block'>
//                 <label htmlFor='new-control-name' className='new-control-name new-control-descriptor'>名称：</label>
//                 <input
//                   type='string'
//                   id={'new-control-name'}
//                   className='newcontrol'
//                   value={this.state.newControlName}
//                   onChange={this.handleNewControlPropValueChange}
//                 />
//               </div>
//               <div className='detail-block'>
//                 <label htmlFor='new-control-info' className='new-control-info new-control-descriptor'>提示信息：</label>
//                 <input
//                   type='string'
//                   id={'new-control-info'}
//                   className='newcontrol'
//                   value={this.state.newControlInfo}
//                   onChange={this.handleNewControlPropValueChange}
//                 />
//               </div>
//               <div className='detail-block'>
//                 <label htmlFor='place-holder' className='place-holder new-control-descriptor'>占位符：</label>
//                 <input
//                   type='string'
//                   id={'place-holder'}
//                   className='newcontrol'
//                   value={this.state.newControlPlaceHolder}
//                   onChange={this.handleNewControlPropValueChange}
//                 />
//               </div>
//             </div>

//             <div className='detail-block-container'>
//               <label htmlFor='new-control-porperty' className='new-control-porperty new-control-label'>属性：</label>
//               {attrCheckboxes}
//             </div>

//             <div className='detail-block-container'>
//               <label htmlFor='new-control-date' className='new-control-date new-control-label'>日期格式：</label>
//               <div className='new-control-date-format'>
//                 <div onClick={this.handleDateClick.bind(this, 'newControlDate')} className={'date-format-item ' + (this.state.dateBoxFormat === DateBoxFormat.Date ? 'date-format-selected' : '')} data-value={DateBoxFormat.Date}>{DATE_FORMAT_STRING[DateBoxFormat.Date]}</div>
//                 <div onClick={this.handleDateClick.bind(this, 'newControlDate')} className={'date-format-item ' + (this.state.dateBoxFormat === DateBoxFormat.DateAndHMS ? 'date-format-selected' : '')} data-value={DateBoxFormat.DateAndHMS}>{DATE_FORMAT_STRING[DateBoxFormat.DateAndHMS]}</div>
//                 <div onClick={this.handleDateClick.bind(this, 'newControlDate')} className={'date-format-item ' + (this.state.dateBoxFormat === DateBoxFormat.DateAndHM ? 'date-format-selected' : '')} data-value={DateBoxFormat.DateAndHM}>{DATE_FORMAT_STRING[DateBoxFormat.DateAndHM]}</div>
//                 <div onClick={this.handleDateClick.bind(this, 'newControlDate')} className={'date-format-item ' + (this.state.dateBoxFormat === DateBoxFormat.HMS ? 'date-format-selected' : '')} data-value={DateBoxFormat.HMS}>{DATE_FORMAT_STRING[DateBoxFormat.HMS]}</div>
//               </div>
//             </div>

//             <div className='button-container'>
//               <button className='button' onClick={this.handleButtonClick.bind(this, 'insertNewControl')}>取消</button>
//               <button className='button' onClick={this.handleButtonClick.bind(this, 'insertNewControl')}>确定</button>
//             </div>
//           </div>
//         </div>

//         <div className='add-new-control-item'
//           style={{display: true === this.state.isNewControlAddItem ? 'block' : 'none'}}
//         >
//           <div className='add-new-control-item config-window' >
//             <div className='config-descriptor'>
//                 添加选项
//             </div>

//             <div className='detail-block'>
//                 <label className='new-control-item-name'>显示名称：</label>
//                 <input
//                   ref={this.newControlItemRef}
//                   type='string'
//                   id={'new-control-item-name'}
//                   className='newcontrol'
//                   value={this.state.newControlItemName}
//                   onChange={this.handleNewControlPropValueChange}
//                 />
//             </div>
//             <div className='detail-block'>
//                 <label className='new-control-item-name'>值：</label>
//                 <input
//                   type='string'
//                   id={'new-control-item-value'}
//                   className='newcontrol'
//                   value={this.state.newControlItemValue}
//                   onChange={this.handleNewControlPropValueChange}
//                 />
//             </div>
//             <div className='button-container'>
//                 <button className='button' onClick={this.handleButtonClick.bind(this, 'addNewControlItem')}>取消</button>
//                 <button className='button' onClick={this.handleButtonClick.bind(this, 'addNewControlItem')}>确定</button>
//             </div>
//           </div>
//         </div>
//         <div className='add-new-control-item'
//           style={{display: true === this.state.isNewControlModifyItem ? 'block' : 'none'}}
//         >
//           <div className='add-new-control-item config-window' >
//             <div className='config-descriptor'>
//                 修改选项
//             </div>

//             <div className='detail-block'>
//                 <label className='new-control-item-name'>显示名称：</label>
//                 <input
//                   type='string'
//                   id={'new-control-item-name'}
//                   className='newcontrol'
//                   value={this.state.newControlItemName}
//                   onChange={this.handleNewControlPropValueChange}
//                 />
//             </div>
//             <div className='detail-block'>
//                 <label className='new-control-item-name'>值：</label>
//                 <input
//                   type='string'
//                   id={'new-control-item-value'}
//                   className='newcontrol'
//                   value={this.state.newControlItemValue}
//                   onChange={this.handleNewControlPropValueChange}
//                 />
//             </div>
//             <div className='button-container'>
//                 <button className='button' onClick={this.handleButtonClick.bind(this, 'modifyNewControlItem')}>取消</button>
//                 <button className='button' onClick={this.handleButtonClick.bind(this, 'modifyNewControlItem')}>确定</button>
//             </div>
//           </div>
//         </div>
//         <ParagraphSetDialog
//           documentCore={this.props.documentCore}
//           visible={this.paragraphVisible}
//           close={this.close}
//         />

//         <FontSetDialog
//           documentCore={this.props.documentCore}
//           visible={this.fontVisible}
//           close={this.close}
//         />

//         {this.renderChar()}

//         {this.renderNewControlNum()}
//         {this.renderParaCommentDialog()}

//         {this.renderNewControlCustomProp()}

//         <ImageSetDialog
//           documentCore={this.props.documentCore}
//           visible={this.imageVisible}
//           close={this.close}
//         />

//         <SplitCellDialog
//           documentCore={this.props.documentCore}
//           visible={this.splitCellVisible}
//           close={this.close}
//         />

//         <TableSettingDialog
//           documentCore={this.props.documentCore}
//           // tableProps={this.props.documentCore.getTableProperty()}
//           visible={this.tableVisible}
//           close={this.close}
//         />

//       <div className='right-menu'>
//           <div className='right-menu-box' ref={this.rightMenuDom}>
//               <ul>
//                 {this.rightMenuRender()}
//               </ul>
//           </div>
//           <div className='right-menu-redialog'>
//             <div className='right-menu-redialog-box'>
//               <div className='text-dec'>
//                   <p>要粘贴带格式的数据，请用键盘快捷方式</p>
//                   <p><span className='left'>粘贴</span><span className='right'>Ctrl + v</span></p>
//                   <p>或者</p>
//                   <p>在下方的区域点击右键，选择粘贴</p>
//               </div>
//               <div className='editor-dialog-content dialog-content' contentEditable={true} />
//               <div className='dialog-btn'><button>好</button></div>
//             </div>
//           </div>
//         </div>
//       </div>
//     );
//   }

//   /**
//    * save .apo to string
//    */
//   public saveToString(): Promise<ArrayBuffer> {
//     const formatWriter = new FormatWriter();

//     return formatWriter.generate(this.props.xmlProps, true);
//   }

//   private renderChar(): any {
//     if (this.charVisible === undefined) {
//       return null;
//     }

//     return (
//       <CharDialog
//         documentCore={this.props.documentCore}
//         visible={this.charVisible}
//         close={this.charHide}
//       />
//     );
//   }

//   private renderParaCommentDialog(): any {
//     if (this.paraCommentVisible === undefined) {
//       return null;
//     }

//     return (
//       <ParaCommentDialog
//         documentCore={this.props.documentCore}
//         visible={this.paraCommentVisible}
//         close={this.close}
//       />
//     )
//   }

//   private renderNewControlCustomProp(): any {
//     if (this.newControlCustomPropVisible === undefined) {
//       return null;
//     }

//     return (
//       <CustomPropertyDialog
//         documentCore={this.props.documentCore}
//         visible={this.newControlCustomPropVisible}
//         close={this.close}
//         callback={this.setCustomProps}
//         properties={this.newControlCustomProps}
//         name={this.newControlCustomPropName}
//       />
//     );
//   }

//   private renderNewControlNum(): any {
//     if (this.newControlNumVisible === undefined) {
//       return null;
//     }

//     return (
//       <NewControlNumDialog
//         documentCore={this.props.documentCore}
//         visible={this.newControlNumVisible}
//         close={this.close}
//         property={this.newControlProps}
//       />
//     );
//   }

//   private setCustomProps = (datas: ICustomProps[]): void => {
//     this.newControlCustomProps = datas;
//   }

//   private scrollHandler = (e: Event): any => {
//     if (this.isShowRightMenu === true) {
//       this.isShowRightMenu = false;
//       this.hideRightMenu();
//     }
//   }

//   private charShow(e: Event): void {
//     this.charVisible = true;
//   }

//   private charHide = (id: string, bReflash: boolean): void => {
//     this.charVisible = false;
//     if (bReflash) {
//       this.host.handleRefresh();
//     }
//   }

//   private resetRigthMenu(type: number, option: any): void {
//     switch (type) {
//       case 1: // 图片
//         this.hideTableMenu(true);
//         this.hideEquationImageMenu(true);
//         this.hideImageMenu(false);
//         this.hideParagraphMenu(true);
//         this.hideStructMenu(true);
//         this.setBaseMenu(option);
//         break;
//       case 2: // 医学公式
//         this.hideTableMenu(true);
//         this.hideEquationImageMenu(false);
//         this.hideImageMenu(true);
//         this.hideParagraphMenu(true);
//         this.hideStructMenu(true);
//         this.setBaseMenu(option);
//         break;
//       case 3: // 表格内容
//       case 4: // 表格单元格
//         this.hideEquationImageMenu(true);
//         this.hideImageMenu(true);
//         this.hideTableMenu(false);
//         this.hideStructMenu(true);
//         this.hideParagraphMenu(false);
//         if (type === 3) {
//           this.rightMenuOption[MenuItemIndex.MergeCell].disabled = true;
//         } else {
//           this.rightMenuOption[MenuItemIndex.SplitCell].disabled = true;
//         }
//         break;
//       case 5:
//       case 6: // 表格内的医学公式和图片
//         this.hideTableMenu(false);
//         this.hideParagraphMenu(false);
//         this.hideStructMenu(true);
//         if (type === 5) {
//           this.hideImageMenu(false);
//           this.hideEquationImageMenu(true);
//         } else {
//           this.hideImageMenu(true);
//           this.hideEquationImageMenu(false);
//         }
//         this.rightMenuOption[MenuItemIndex.MergeCell].disabled = true;
//         break;
//       case 7: // 结构化元素
//         this.hideStructMenu(false);
//         this.hideTableMenu(true);
//         this.hideEquationImageMenu(true);
//         this.hideImageMenu(true);
//         this.hideParagraphMenu(false);
//         // this.setBaseMenu(option);
//         break;
//       case 8: // 表格内的结构化元素
//         this.hideTableMenu(false);
//         this.hideParagraphMenu(false);
//         this.hideEquationImageMenu(true);
//         this.hideImageMenu(true);
//         this.hideStructMenu(false);
//         // this.setBaseMenu(option);
//         this.rightMenuOption[MenuItemIndex.MergeCell].disabled = true;
//         break;
//       default:
//         this.hideEquationImageMenu(true);
//         this.hideImageMenu(true);
//         this.hideTableMenu(true);
//         this.hideStructMenu(true);
//         this.hideParagraphMenu(false);
//     }
//   }

//   private toggleCellPro(index?: number): void {
//     const childs = this.rightMenuOption[MenuItemIndex.CellProperty].childs;
//     if (index !== undefined) {
//       childs.forEach((child) => {
//         if (child.index === index) {
//           child.hide = false;
//         } else {
//           child.hide = true;
//         }
//       });
//     } else {
//       childs.forEach((child) => {
//         child.hide = !child.hide;
//       });
//     }
//   }

//   private hideStructMenu(bHide: boolean): void {
//     const obj = this.rightMenuOption[MenuItemIndex.Struct];
//     obj.hide = bHide;
//     obj.disabled = bHide;
//   }

//   private hideImageMenu(bHide: boolean): void {
//     const obj = this.rightMenuOption[MenuItemIndex.Image];
//     obj.hide = bHide;
//     obj.disabled = bHide;
//   }

//   private hideEquationImageMenu(bHide: boolean): void {
//     const obj = this.rightMenuOption[MenuItemIndex.Formula];
//     obj.hide = bHide;
//     obj.disabled = bHide;
//   }

//   private hideTableMenu(bHide: boolean): void {
//     const option = this.rightMenuOption;
//     for (let i = MenuItemIndex.MergeCell; i <= MenuItemIndex.InsertRightCol; i++) {
//       option[i].hide = bHide;
//       option[i].disabled = bHide;
//       // todo: 暂时暂停对表格属性的启用
//       // if (i === MenuItemIndex.Table) {
//       //   option[i].disabled = true;
//       // }
//     }
//   }

//   private setBaseMenu(option: {copy: boolean}): void {
//     option.copy = false;
//   }

//   private hideParagraphMenu(bHide: boolean): void {
//     const option = this.rightMenuOption;
//     option[MenuItemIndex.Font].hide = bHide;
//     option[MenuItemIndex.Paragraph].hide = bHide;
//   }

//   private setToRightMenu = (datas: Array<{index: number, key: string, value: string}>): void => {
//     const option = this.rightMenuOption;
//     datas.forEach((item) => {
//       const current = option[item.index];
//       if (current) {
//         current[item.key] = item.value;
//       }
//     });
//     this.setState({bReflash: !this.state.bReflash});
//   }

//   private hideRightMenu(): void {
//     const dom = this.rightMenuDom.current;
//     dom.className = dom.className.replace(/\s+right-menu-visible/g, '');
//     this.isShowRightMenu = false;
//   }

//   private docClickEvent = () => {
//     if (this.isShowRightMenu !== true) {
//       return;
//     }
//     if (this.stopDocClick) {
//         this.stopDocClick = false;
//         return;
//     }
//     this.hideRightMenu();
//   }

//   private contextmenu = (e: any) => {
//     // if (true === EmrEditor.isClickOnImage(e) || this.host.isPopTableMenu()) {
//     //   return;
//     // }
//     const option = {isStop: false, copy: true};
//     let dom = this.rightMenuDom.current;
//     gEvent.setEvent(this.docId, 'contextmenu', e, dom, option);
//     if (option.isStop === true) {
//       return;
//     }
//     e.preventDefault();
//     const type = this.getContextMenuType(e);
//     this.resetRigthMenu(type, option);
//     const top = 16 + e.clientY;

//     dom.style.left = 12 + e.clientX + 'px';
//     dom.style.top = top + 'px';
//     dom.className += ' right-menu-visible';
//     this.isShowRightMenu = true;
//     this.imageRef = e;
//     setTimeout(() => {
//       const height = dom.clientHeight;
//       const maxHeight = document.body.clientHeight;
//       const nextTop = maxHeight - height - 80;
//       if (top > nextTop) {
//         dom.style.top = nextTop + 'px';
//         dom = null;
//       }
//     }, 10);
//   }

//   private rightMenuRender(): any {
//     const documentCore = this.props.documentCore;
//     if (documentCore.isProtectedMode()) {
//       return null;
//     }
//     return this.rightMenuList
//     .map((item) => {
//       let className = item.className;
//       if (item.disabled) {
//         className += ' disabled';
//       }
//       if (item.hide === true) {
//         className += ' hide';
//       }
//       return (
//         <li
//           key={item.index}
//           data-index={item.index}
//           className={className}
//         >
//           {item.name}
//           {item.childs ? (<ul className='inner-ul'>{this.rightMenuChildRender(item.childs, item.index)}</ul>) : null}
//         </li>
//       );
//     });
//   }

//   private rightMenuChildRender(datas: any[], parentIndex: number): any {
//     return datas.map((item, index) => {
//       let className = '';
//       if (item.disabled) {
//         className += ' disabled';
//       }
//       if (item.hide === true) {
//         className += ' hide';
//       }
//       return (
//         <li
//           key={item.index}
//           data-index={index}
//           data-parent-index={parentIndex}
//           data-child='1'
//           className={className}
//         >
//           {item.name}
//         </li>
//       );
//     });
//   }

//   private handerRightMenu = (e: any): void => {
//     const target = e.target;
//     let index = target.getAttribute('data-index');
//     if (!index) {
//       this.stopDocClick = true;
//       return;
//     }

//     index = parseInt(index, 10);

//     let item: any;

//     const isChild: boolean = target.getAttribute('data-child') === '1';
//     const parentIndex: string = target.getAttribute('data-parent-index');
//     if (parentIndex) {
//       item = this.rightMenuOption[parentIndex].childs[index];
//       index = item.index;
//     } else {
//       item = this.rightMenuOption[index];
//     }

//     // if (isChild === true) {
//     //   e.stopPropagation();
//     // }
//     e.stopPropagation();

//     if (item.disabled === true || item.childs !== undefined) {
//       this.stopDocClick = true;
//       // e.stopPropagation();
//       return;
//     }
//     // e.stopPropagation();
//     const option = {isStop: false};

//     gEvent.setEvent(this.docId, 'rightMenuClick', e, option);
//     this.stopDocClick = option.isStop;
//     switch (index) {
//       case MenuItemIndex.Font:
//         this.open('font');
//         break;
//       case MenuItemIndex.Paragraph:
//         this.open('paragraph');
//         break;
//       case MenuItemIndex.Image:
//         this.open('image');
//         // const paraDrawing = this.props.documentCore.getSelectedImage() as ParaDrawing;
//         // this.host.setState({imageConfigModalType: ImageConfigModalType.Advanced});
//         // // this.imageConfigModalType = ImageConfigModalType.Advanced;
//         // // this.imageConfigModalType = ImageConfigModalType.Basic;
//         // const imageRatio = paraDrawing.width / paraDrawing.height;
//         // this.setState({imageWidth: paraDrawing.width, imageHeight: paraDrawing.height,
//         //              imageSource: paraDrawing.src, imageName: paraDrawing.name,
//         //              imagePreserveAspectRatio: paraDrawing.preserveAspectRatio,
//         //              imageSizeLocked: paraDrawing.sizeLocked,
//         //              imageDeleteLocked: paraDrawing.deleteLocked,
//         //              imageRatio});
//         break;
//       case MenuItemIndex.Formula:
//         if (this.imageRef) {
//           this.host.handleDoubleClick(this.imageRef);
//         }
//         break;
//       case MenuItemIndex.MergeCell:
//         this.mergeTableCells();
//         break;
//       case MenuItemIndex.SplitCell:
//         this.open('splitCell');
//         break;
//       case MenuItemIndex.DeleteRow:
//       case MenuItemIndex.DeleteCol:
//         this.host.clearSection();
//         setTimeout(() => {
//           this.host.setCursorVisible(true);
//         });
//         this.host.setState({tablePopMenuModalType: TableMenuModalType.DeleteTableCells});
//         break;
//       case MenuItemIndex.Table:
//         this.open('table');
//         break;
//       case MenuItemIndex.InsertTopRow:
//         this.props.documentCore.insertTableRow(true);
//         this.host.setSelections();
//         this.props.refresh();
//         break;
//       case MenuItemIndex.InsertBottomRow:
//         this.props.documentCore.insertTableRow(false);
//         this.host.setSelections();
//         this.props.refresh();
//         break;
//       case MenuItemIndex.InsertLeftCol:
//         this.props.documentCore.insertTableColumn(true);
//         this.host.setSelections();
//         this.props.refresh();
//         break;
//       case MenuItemIndex.InsertRightCol:
//         this.props.documentCore.insertTableColumn(false);
//         this.host.setSelections();
//         this.props.refresh();
//         break;
//       case MenuItemIndex.Protected:
//       case MenuItemIndex.UnProtected:
//         this.toggleCellPro();
//         break;
//       case MenuItemIndex.Struct:
//         // const newControl = this.currentMoveNode;
//         // console.log(struct) // 结构化元素
//         if ( true === this.bCursorInNewControl ) {
//           this.popNewControlPropertyWindow();
//         }
//         break;
//     }
//   }

//   /**
//    * 合并单元格
//    */
//   private mergeTableCells(): void {
//     this.props.documentCore.mergeTableCells();
//     this.props.refresh(10);
//   }

//   private isImage(e: any): boolean {
//       return (e.target.tagName === 'image' || (e.target.tagName === 'circle'
//       && e.target.className.baseVal.includes('image-handler')));
//   }

//   private getContextMenuType(e: Event): number {
//     let type: number = 0;
//     const documentCore = this.props.documentCore;

//     if (true === this.isImage(e)) {
//       const imageType = documentCore.getImageType();
//       switch (imageType) {
//         case ParaElementType.ParaDrawing:
//           documentCore.setImageSelectionInfo(e.target);
//           type = 1;
//           break;
//         case ParaElementType.ParaMedEquation:
//           type = 2;
//           break;
//         default:
//       }
//     } else {
//       // const struct = documentCore.getCurrentNewControl();
//       this.bCursorInNewControl = documentCore.isCursorInNewControl();
//       if ( true === this.bCursorInNewControl ) {
//         type = 7;
//         // this.currentMoveNode = struct;
//       }
//     }

//     let type1: number = 0;
//     if (true === documentCore.isInTableCell()) { // 在单元格内
//       type1 = 3;
//     } else if (true === documentCore.isSelectedTableCells()) { // 选中单元格
//       type1 = 4;
//     }
//     switch (type.toString() + type1) {
//       case '03':
//         type = 3;
//         break;
//       case '04':
//         type = 4;
//         break;
//       case '13':
//         type = 5; // 表格里面选中图片
//         break;
//       case '23':
//         type = 6; // 表格里面选中医学公式
//         break;
//       case '73':
//         type = 8;
//         break;
//       default:
//         break;
//     }

//     return type;
//   }

//   private docPrint = (e: any) => {
//     if (e.ctrlKey === true && e.keyCode === 80) {
//       e.preventDefault();
//       e.stopPropagation();
//       this.handlePrint(true);
//     }
//   }
//   private toggleShow = (e: any) => {
//     const className = e.target.className;
//     if (className.indexOf('first-menu') === -1 && -1 === className.indexOf('newcontrol')) {
//       this.setState({showFile: false, showInsert: false, showFormat: false,
//             showTools: false, showInsertTable: false, showHelp: false});
//     }
//   }

//   private open(id: string): void {
//     this[id + 'Visible'] = true;
//   }

//   private close = (id?: any, bReflash?: boolean): void => {
//     this[id + 'Visible'] = false;
//     if (bReflash === true) {
//       this.props.refresh(10);
//     }
//   }

//   private constructPagePropertyForRender(pageProperty: PageProperty): any {
//     const pagePropertyForRender = JSON.parse(JSON.stringify(pageProperty));
//     pagePropertyForRender.height = (getMMFromPx(pagePropertyForRender.height) / 10).toFixed(2);
//     pagePropertyForRender.width = (getMMFromPx(pagePropertyForRender.width) / 10).toFixed(2);
//     pagePropertyForRender.paddingBottom = (getMMFromPx(pagePropertyForRender.paddingBottom) / 10).toFixed(2);
//     pagePropertyForRender.paddingLeft = (getMMFromPx(pagePropertyForRender.paddingLeft) / 10).toFixed(2);
//     pagePropertyForRender.paddingRight = (getMMFromPx(pagePropertyForRender.paddingRight) / 10).toFixed(2);
//     pagePropertyForRender.paddingTop = (getMMFromPx(pagePropertyForRender.paddingTop) / 10).toFixed(2);
//     // console.log(pagePropertyForRender);
//     return pagePropertyForRender;
//   }

//   /**
//    * 文档是否由读取生成
//    */
//   private documentGeneratedFromLoading(nextProps: IPageMenuProps): boolean {
//     // since pageFormat is not saved in modal, compare traditionally to decide
//     let result = false;
//     if (JSON.stringify(nextProps.pageProperty) !==  JSON.stringify(this.props.pageProperty)) {
//       // tslint:disable-next-line: max-line-length
//       const {width: pWidth, height: pHeight, paddingBottom: pBottom, paddingLeft: pLeft, paddingTop: pTop, paddingRight: pRight} = nextProps.pageProperty;
//       // tslint:disable-next-line: max-line-length
//       const {width: sWidth, height: sHeight, paddingBottom: sBottom, paddingLeft: sLeft, paddingTop: sTop, paddingRight: sRight} = this.state.pageProperty;
//       // tslint:disable-next-line: max-line-length
//       if (pWidth === sWidth && pHeight === sHeight && pBottom === sBottom && pLeft === sLeft && pTop === sTop && pRight === sRight) {
//         result = false;
//       } else {
//         result = true;
//       }
//     }
//     return result;
//   }

//   /**
//    * 获取当前段落的属性并更新state
//    */
//   private setParaPropertyState(): void {
//     const curParaPro = this.props.getParagraphPro();

//     // console.log(curParaPro)
//     const paraProperty = {
//       indentation: {firstLine: 0, left: 0},
//       lineSpace: {type: LineSpacingType.Single, ratio: null},
//     };
//     // let paraProperty = JSON.parse(JSON.stringify(this.state.paraProperty));
//     // mm to cm
//     paraProperty.indentation.left = curParaPro.paraInd.left / 10;
//     paraProperty.indentation.firstLine = curParaPro.paraInd.firstLine / 10;

//     // consider leftIndentForRender
//     let leftIndentForRender = paraProperty.indentation.left;
//     let isFirstIndent = true;
//     if (curParaPro.paraInd.firstLine < 0) {
//       // hanging indent
//       leftIndentForRender += paraProperty.indentation.firstLine;
//       isFirstIndent = false;
//     }

//     // set line space
//     let lineSpaceDisabled = true;
//     let isFixedLineSpacing = false;
//     if (curParaPro.paraSpacing.lineSpacingType === LineSpacingType.Multi ||
//       curParaPro.paraSpacing.lineSpacingType === LineSpacingType.Fixed) {
//       lineSpaceDisabled = false;
//       if (curParaPro.paraSpacing.lineSpacingType === LineSpacingType.Fixed) {
//         isFixedLineSpacing = true;
//       }
//     }

//     paraProperty.lineSpace.type = curParaPro.paraSpacing.lineSpacingType;

//     // initialize ratio value according to type
//     switch (paraProperty.lineSpace.type) {
//       case LineSpacingType.Single:
//       case LineSpacingType.Min:
//           paraProperty.lineSpace.ratio = 1;
//           break;

//       case LineSpacingType.SingeHalf:
//           paraProperty.lineSpace.ratio = 1.5;
//           break;

//       case LineSpacingType.Double:
//           paraProperty.lineSpace.ratio = 2;
//           break;

//       case LineSpacingType.Multi:
//           lineSpaceDisabled = false;
//           // need to convert to times
//           let ratio = curParaPro.paraSpacing.lineSpacing;
//           ratio = (ratio - 1) / 2 / 0.15;
//           paraProperty.lineSpace.ratio = Math.round(ratio * 100) / 100;
//           break;

//       case LineSpacingType.Fixed:
//           lineSpaceDisabled = false;
//           isFixedLineSpacing = true;

//           paraProperty.lineSpace.ratio = curParaPro.paraSpacing.lineSpacing;
//           break;

//       default:
//           break;
//     }

//     this.setState({paraProperty, leftIndentForRender, isFirstIndent, isFixedLineSpacing});
//     // return paraProperty;
//   }

//   private renderPagefomtConfig(array: any, disabled?: boolean): any {
//     const { pagePropertyForRender } = this.state;
//     return array.map(({label, key, options}, index) => {
//       return (
//         <div key={index} className='detail-block'>
//           <label htmlFor={key}>{label}：</label>
//           <select name={label} id={key} value={pagePropertyForRender && pagePropertyForRender[key]} onChange={this.handleChangePageInfo}>
//             {options.map((item) =>  <option value={item} key={item}>{item}</option>)}
//           </select>
//         </div>
//       );
//     });
//   }

//   private renderPageBorder(): JSX.Element {
//     const { pagePropertyForRender } = this.state;
//     return (
//       <div className='config-page-border'>
//         <div className='config-descriptor'>页边距(厘米) </div>
//         <div className='detail-block'>
//             <label htmlFor='width'>左对齐: </label>
//             <input
//               type='number'
//               min={1}
//               id={'paddingLeft'}
//               value={pagePropertyForRender.paddingLeft}
//               onChange={this.handleChangePageInfo}
//             />
//         </div>
//         <div className='detail-block'>
//             <label htmlFor='height'>右对齐: </label>
//             <input
//               id={'paddingRight'}
//               type='number'
//               min={1}
//               value={pagePropertyForRender.paddingRight}
//               onChange={this.handleChangePageInfo}
//             />
//         </div>
//         <div className='detail-block'>
//             <label htmlFor='height'>顶端对齐: </label>
//             <input
//               id={'paddingTop'}
//               type='number'
//               min={1}
//               value={pagePropertyForRender.paddingTop}
//               onChange={this.handleChangePageInfo}
//             />
//         </div>
//         <div className='detail-block'>
//             <label htmlFor='height'>底端对齐: </label>
//             <input
//               id={'paddingBottom'}
//               type='number'
//               min={1}
//               value={pagePropertyForRender.paddingBottom}
//               onChange={this.handleChangePageInfo}
//             />
//         </div>
//       </div>
//     );
//   }

//   private handleIndentationValueChange = (e: any) => {

//     const type = e.target.id;
//     const val = Number(e.target.value);
//     const paraProperty = JSON.parse(JSON.stringify(this.state.paraProperty));
//     let leftIndentForRender = JSON.parse(JSON.stringify(this.state.leftIndentForRender));

//     if (type === 'left-indent') {
//       paraProperty.indentation.left = val;

//       // if hanging indent, need to add up the hidden "left"
//       if (!this.state.isFirstIndent) {
//         paraProperty.indentation.left += -paraProperty.indentation.firstLine;
//       }

//       leftIndentForRender = val;

//     } else if (type === 'special-indent') {

//       if (this.state.isFirstIndent) {
//         paraProperty.indentation.firstLine = val;
//         paraProperty.indentation.left = this.state.leftIndentForRender;
//       } else { // hanging indent
//         paraProperty.indentation.left = val + this.state.leftIndentForRender;
//         paraProperty.indentation.firstLine = -val;
//       }

//     }
//     this.setState({paraProperty, leftIndentForRender});

//   }

//   private handleLineSpaceValueChange = (e: any) => {
//     const paraProperty = JSON.parse(JSON.stringify(this.state.paraProperty));
//     paraProperty.lineSpace.ratio = Number(e.target.value);
//     this.setState({paraProperty});
//   }

//   private switchSpecialIndentType = (e: any) => {
//     // console.log(e.target.value)

//     // need to reset left and first vals based on indent type switch
//     const paraProperty = JSON.parse(JSON.stringify(this.state.paraProperty));

//     let isFirstIndent = true;
//     if (e.target.value === 'first') {

//       isFirstIndent = true;
//       paraProperty.indentation.firstLine = Math.abs(paraProperty.indentation.firstLine);
//       paraProperty.indentation.left = this.state.leftIndentForRender;

//     } else if (e.target.value === 'hanging') {

//       isFirstIndent = false;
//       paraProperty.indentation.firstLine = -Math.abs(paraProperty.indentation.firstLine);
//       paraProperty.indentation.left = this.state.leftIndentForRender - paraProperty.indentation.firstLine;

//     }
//     this.setState({isFirstIndent, paraProperty});

//   }

//   private switchLineSpaceType = (e: any) => {

//     const paraProperty = JSON.parse(JSON.stringify(this.state.paraProperty));
//     let lineSpaceDisabled = JSON.parse(JSON.stringify(this.state.lineSpaceDisabled));
//     let isFixedLineSpacing = false;

//     paraProperty.lineSpace.type = Number(e.target.value);

//     lineSpaceDisabled = true;
//     paraProperty.lineSpace.ratio = '';

//     switch (paraProperty.lineSpace.type) {
//       case LineSpacingType.Single:
//       case LineSpacingType.Min:
//           paraProperty.lineSpace.ratio = 1;
//           break;

//       case LineSpacingType.SingeHalf:
//           paraProperty.lineSpace.ratio = 1.5;
//           break;

//       case LineSpacingType.Double:
//           paraProperty.lineSpace.ratio = 2;
//           break;

//       case LineSpacingType.Multi:
//           lineSpaceDisabled = false;
//           paraProperty.lineSpace.ratio = 3;
//           break;

//       case LineSpacingType.Fixed:
//           lineSpaceDisabled = false;
//           isFixedLineSpacing = true;

//           // assume default textHeight is 16 and single line space is 1.3X
//           // console.log(typeof getMMFromPx(16 * LineSpacingRatio.Single))
//           paraProperty.lineSpace.ratio = Number((getMMFromPx(16 * LineSpacingRatio.Single) / 10).toFixed(2));
//           break;

//       default:
//           break;
//     }

//     this.setState({paraProperty, lineSpaceDisabled, isFixedLineSpacing});

//   }

//   private handlePrint(flag: boolean): void {
//     if (!this.printVm) {
//       this.printVm = new PrintDialog(this.props.documentCore);
//     }

//     this.printVm.open(flag);

//     this.props.documentCore.removeSelection();
//     this.host.handleRefresh();
//     clearInterval(this.host.timer);
//     this.host.setCursorVisible(false);
//   }

//   private handleChangePageInfo = (e: any) => {
//     const key = e.target.id;
//     let value = e.target.value;
//     // console.log(key, value);
//     let pageProperty: any = '';
//     let pagePropertyForRender: any = '';

//     if (key === 'height' || key === 'width') {
//       value = Number.parseFloat(value);
//       const pxValue = getPxForMM(value.toFixed(2) * 10);
//       // value = Number.parseInt(value);
//       pageProperty = { ...this.state.pageProperty, [key]: pxValue };
//       pagePropertyForRender = {...this.state.pagePropertyForRender, [key]: value};

//     } else if (key === 'pageFormat' && value === '自定义') {
//       this.setState({ isDisabled: false });
//       pageProperty = { ...this.state.pageProperty, [key]: value };
//       pagePropertyForRender = {...this.state.pagePropertyForRender, [key]: value};

//     } else if (key === 'pageFormat' && value === 'A3') {
//       this.setState({ isDisabled: true});
//       const A3 = PAGE_FORMAT.A3;
//       const width = getPxForMM(297);
//       const height = getPxForMM(420);
//       pageProperty = { ...this.state.pageProperty, [key]: value, height, width };
//       pagePropertyForRender = {...this.state.pagePropertyForRender, [key]: value, height: A3[1], width: A3[0]};

//     } else if (key === 'pageFormat' && value === 'A4') {
//       this.setState({ isDisabled: true});
//       const A4 = PAGE_FORMAT.A4;
//       const width = getPxForMM(210);
//       const height = getPxForMM(297);
//       pageProperty = { ...this.state.pageProperty, [key]: value, height, width };
//       pagePropertyForRender = {...this.state.pagePropertyForRender, [key]: value, height: A4[1], width: A4[0]};

//     } else if (key === 'pageFormat' && value === 'A5') {
//       this.setState({ isDisabled: true });
//       const A5 = PAGE_FORMAT.A5;
//       const width = getPxForMM(148);
//       const height = getPxForMM(210);
//       pageProperty = { ...this.state.pageProperty, [key]: value, height, width };
//       pagePropertyForRender = {...this.state.pagePropertyForRender, [key]: value, height: A5[1], width: A5[0]};

//     } else if (key === 'pageFormat' && value === 'B4') {
//       this.setState({ isDisabled: true});
//       const B4 = PAGE_FORMAT.B4;
//       const width = getPxForMM(250);
//       const height = getPxForMM(353);
//       pageProperty = { ...this.state.pageProperty, [key]: value, height, width };
//       pagePropertyForRender = {...this.state.pagePropertyForRender, [key]: value, height: B4[1], width: B4[0]};

//     } else if (key === 'pageFormat' && value === 'B5') {
//       this.setState({ isDisabled: true});
//       const B5 = PAGE_FORMAT.B5;
//       const width = getPxForMM(176);
//       const height = getPxForMM(250);
//       pageProperty = { ...this.state.pageProperty, [key]: value, height, width };
//       pagePropertyForRender = {...this.state.pagePropertyForRender, [key]: value, height: B5[1], width: B5[0]};

//     } else if (key === 'pageFormat' && value === 'B6') {
//       this.setState({ isDisabled: true});
//       const B6 = PAGE_FORMAT.B6;
//       const width = getPxForMM(125);
//       const height = getPxForMM(176);
//       pageProperty = { ...this.state.pageProperty, [key]: value, height, width };
//       pagePropertyForRender = {...this.state.pagePropertyForRender, [key]: value, height: B6[1], width: B6[0]};
//     } else if (key === 'paddingLeft' || key === 'paddingRight' || key === 'paddingTop' || key === 'paddingBottom') {
//       // const pxValue = getPxForMM(value * 10);
//       // console.log(value * 10)
//       value = Number.parseFloat(value);
//       pageProperty = { ...this.state.pageProperty, [key]: getPxForMM(value.toFixed(2) * 10) };
//       pagePropertyForRender = {...this.state.pagePropertyForRender, [key]: value};
//     }

//     // console.time('test');
//     this.setState({
//       pageProperty,
//       pagePropertyForRender,
//     });
//     // console.timeEnd('test');
//     // this.props.changePagePro(pageProperty);
//     // this.props.refresh();
//   }

//   private formatPageProperty(): void {
//     const { pagePropertyForRender } = this.state;
//     if ( '自定义' === pagePropertyForRender.pageFormat ) {
//       pagePropertyForRender.height = (parseFloat(pagePropertyForRender.height)).toFixed(2);
//       pagePropertyForRender.width = (parseFloat(pagePropertyForRender.width)).toFixed(2);
//     }

//     pagePropertyForRender.paddingBottom = (parseFloat(pagePropertyForRender.paddingBottom)).toFixed(2);
//     pagePropertyForRender.paddingLeft = (parseFloat(pagePropertyForRender.paddingLeft)).toFixed(2);
//     pagePropertyForRender.paddingRight = (parseFloat(pagePropertyForRender.paddingRight)).toFixed(2);
//     pagePropertyForRender.paddingTop = (parseFloat(pagePropertyForRender.paddingTop)).toFixed(2);

//     this.setState({pagePropertyForRender});
//   }

//   private toggleTab = (e: any) => {
//     // console.log(e);
//     // e.stopPropagation();
//     switch (e.target.innerHTML) {
//       case '文件':
//         this.setState({showFile: !this.state.showFile, showInsert: false, showFormat: false, showHelp: false,
//           showView: false, showTools: false, showInsertTable: false});
//         break;

//       case '视图':
//         this.setState({showView: !this.state.showView, showFile: false, showFormat: false, showHelp: false,
//           showInsert: false, showTools: false, showInsertTable: false, showInsertNewControl: false,
//                         showNewControlExtend: true});
//         break;

//       case '插入':
//         this.setState({showInsert: !this.state.showInsert, showFile: false, showFormat: false, showHelp: false,
//           showView: false, showTools: false, showInsertTable: false, showInsertNewControl: false,
//                         showNewControlExtend: true});
//         break;

//       case '格式':
//         this.setState({showFormat: !this.state.showFormat, showFile: false, showInsert: false, showHelp: false,
//           showView: false, showTools: false, showInsertTable: false});
//         break;

//       case '工具':
//         this.setState({showTools: !this.state.showTools, showFile: false, showInsert: false,
//           showView: false, showFormat: false, showHelp: false, showInsertTable: false});
//         break;

//       case '表格':
//         this.setState({showInsertTable: !this.state.showInsertTable, showFile: false, showInsert: false,
//           showView: false, showTools: false, showFormat: false, showHelp: false});
//         break;

//       case '帮助':
//         this.setState({showHelp: !this.state.showHelp, showFile: false, showInsert: false, showFormat: false,
//           showView: false, showTools: false, showInsertTable: false});
//         break;

//       default:
//         break;
//     }
//   }

//   private handleChildClick = (e: any) => {
//     // e.stopPropagation();
//     switch (e.target.innerHTML) {
//       case '新建': {

//         if (this.props.documentCore.isDirty()) {
//           setTimeout(() => {
//             const result = window.confirm('当前文档未保存。需要保存吗？');
//             if (result) {
//               this.generate();
//             } else {
//               this.props.documentCore.createNewDocument();
//               this.props.refresh();
//             }
//           }, 10);
//         } else {
//           this.props.documentCore.createNewDocument();
//           this.props.refresh();
//         }
//         break;
//       }
//       case '页面设置':
//         // console.log('page config');
//         this.setState({showFile: !this.state.showFile, showFileConfigModal: !this.state.showFileConfigModal});
//         break;

//       case '显示格式标记':
//         // console.log('page config');
//         this.setState({showView: !this.state.showView}, () => {
//           const bShow = this.props.documentCore.isShowParaEnd();
//           this.props.documentCore.setShowParaEnd(!bShow);
//           this.props.refresh();
//         });
//         break;

//       case '文字输入':
//         // console.log('input');
//         this.setState({showHelp: !this.state.showHelp, showHelpConfigModal: !this.state.showHelpConfigModal});
//         break;

//       case 'apo转为zip':
//         this.setState({showHelp: !this.state.showHelp}, () => {
//           // trigger the hidden input file
//           this.apoToZipRef.current.click();
//         });
//         break;

//       case 'zip转为apo':
//         this.setState({showHelp: !this.state.showHelp}, () => {
//           // trigger the hidden input file
//           this.zipToApoRef.current.click();
//         });
//         break;

//       case '段落':
//         // console.log("paragraph");
//         this.setState({showFormat: !this.state.showFormat, showFormatConfigModal: !this.state.showFormatConfigModal},
//           () => {
//           this.setParaPropertyState();
//         });
//         break;

//       case '打印':
//         this.setState({showFile: !this.state.showFile}, () => {
//           window.print();
//         });
//         break;
//       case '结构化元素':
//         this.setState({showInsertNewControl: !this.state.showInsertNewControl});
//         break;
//       case '批注':
//         this.props.documentCore.addComment();
//         this.props.refresh(0);
//         // console.log(this.props.documentCore.getDocument())
//           // console.log('input');
//           // this.setState({showHelp: !this.state.showHelp, showHelpConfigModal: !this.state.showHelpConfigModal});
//           break;
//       case '批注列表':
//         this.paraCommentVisible = true;
//         this.setState({bReflash: !this.state.bReflash});
//         break;
//       case '数值框':
//         this.newControlProps = undefined;
//         this.newControlNumVisible = true;
//         this.setState({bReflash: !this.state.bReflash});
//         break;
//       case '文本框':
//         this.isNewControlTypeDisable = false;
//         this.curNewControlType = NewControlType.TextBox;
//         this.setState({showInsertNewControl: !this.state.showInsertNewControl,
//                       newControlName: this.props.documentCore.makeUniqueNewControlName(NewControlType.TextBox),
//                       newControlInfo: '', newControlPlaceHolder: NewControlDefaultSetting.DefaultPlaceHolderContent,
//                       newControlType: NewControlType.TextBox, // isNewControlTypeDisable: false,
//                       isNewControlHidden: false, isNewControlCanntDelete: false, isNewControlCanntEdit: false,
//                       isNewControlMustInput: false, isNewControlShowBorder: true,
//                       isNewControlReverseEdit: false, isNewControlHiddenBackground: false,
//                       newControlDisplayType: NewControlContentSecretType.DontSecret, newControlFixedLength: 0,
//                       newControlMaxLength: 0, showInsertNewControlModal: true,
//                     });
//         break;
//       case '单选框/多选框':
//         this.isNewControlTypeDisable = false;
//         this.curNewControlType = NewControlType.Combox;
//         this.setState({showInsertNewControl: !this.state.showInsertNewControl,
//                       newControlName: this.props.documentCore.makeUniqueNewControlName(NewControlType.Combox),
//                       newControlInfo: '', newControlPlaceHolder: NewControlDefaultSetting.DefaultPlaceHolderContent,
//                       newControlType: NewControlType.Combox, // isNewControlTypeDisable: false,
//                       isNewControlHidden: false, isNewControlCanntDelete: false, isNewControlCanntEdit: false,
//                       isNewControlMustInput: false, isNewControlShowBorder: true,
//                       isNewControlReverseEdit: false, isNewControlHiddenBackground: false,
//                       isMultiSelect: false, prefixContent: '', selectPrefixContent: '',
//                       separator: NewControlDefaultSetting.DefaultItemSeparator, showInsertNewControlModal: true,
//                       retrieve: false,
//                     });
//         break;
//       case '日期框':
//         this.isNewControlTypeDisable = false;
//         this.curNewControlType = NewControlType.DateTimeBox;
//         this.setState({showInsertNewControl: !this.state.showInsertNewControl,
//                       newControlName: this.props.documentCore.makeUniqueNewControlName(NewControlType.DateTimeBox),
//                       newControlInfo: '', newControlPlaceHolder: NewControlDefaultSetting.DefaultPlaceHolderContent,
//                       newControlType: NewControlType.DateTimeBox, // isNewControlTypeDisable: false,
//                       isNewControlHidden: false, isNewControlCanntDelete: false, isNewControlCanntEdit: false,
//                       isNewControlMustInput: false, isNewControlShowBorder: true,
//                       isNewControlReverseEdit: false, isNewControlHiddenBackground: false,
//                       dateBoxFormat: DateBoxFormat.DateAndHMS,
//                       showInsertNewControlModal: true,
//                     });
//         break;
//       case '分页符':
//           this.props.documentCore.addPageBreak();
//           this.props.refresh();
//           // TODO： 这里要改，执行了三次刷新，暂时使用
//           setTimeout(() => {
//             const dom = this.pageListDom || document.querySelector('.ReactVirtualized__Grid.ReactVirtualized__List');
//             if (dom) {
//               this.pageListDom = dom;
//               dom.scrollTop = (this.host.pageIndex) * 1000;
//             }
//             this.props.refresh(10);
//           }, 0);

//           // this.props.refresh();
//           // this.props.refresh(10);
//           break;
//       case '保存':
//         this.setState({showFile: !this.state.showFile}, () => {
//           this.generate();
//           // this.saveToString();
//         });
//         break;
//       case '特殊字符':
//         this.charShow(null);
//         this.host.handleRefresh(0);
//         break;

//       case '打开':
//         this.setState({showFile: !this.state.showFile}, () => {

//           if (this.props.documentCore.isDirty()) {
//             setTimeout(() => {
//               const result = window.confirm('当前文档未保存。需要保存吗？');
//               if (result) {
//                 this.generate();
//               } else {
//                 // trigger the hidden input file
//                 this.openFileRef.current.click();
//               }
//             }, 10);
//           } else {
//             // trigger the hidden input file
//             this.openFileRef.current.click();
//           }
//         });
//         break;

//       case '图片':
//         // console.log("image");
//         if (!IMAGE_FLAGS.isImageOnClick) {
//           this.setState({showInsert: !this.state.showInsert}, () => {
//             this.insertImageRef.current.click();
//           });
//         }
//         break;

//       case '插入表格': {
//         this.setState({showInsertTable: !this.state.showInsertTable,
//                        tableName: this.props.documentCore.getUniqueTableName(),
//                        showInsertTableConfigModal: !this.state.showInsertTableConfigModal});
//         break;
//       }

//       case '表格属性': {
//         this.open('table');
//         break;
//       }

//       case '医学公式': {
//         if (!IMAGE_FLAGS.isImageOnClick) {
//           this.setState({showInsert: !this.state.showInsert, showEquationModal: !this.state.showEquationModal});
//         }
//         break;
//       }

//       case '设计模式': {
//         this.setState({showTools: !this.state.showTools});
//         break;
//       }

//       case '文件': {
//         this.setState({showInsert: !this.state.showInsert}, () => {
//           if (this.props.documentCore.isDirty()) {
//             setTimeout(() => {
//               const result = window.confirm('当前文档未保存。需要保存吗？');
//               if (result) {
//                 // save
//                 this.generate();
//               } else {
//                 // trigger the hidden input file
//                 this.insertFileRef.current.click();
//               }
//             }, 10);
//           } else {
//             // trigger the hidden input file
//             this.insertFileRef.current.click();
//           }
//         });
//       }

//       default:
//         break;
//     }
//   }

//   /**
//    * 设置table的行/列参数
//    * @param e
//    */
//   private handleTableValueChange = (e: any): void => {
//     const type = e.target.id;
//     const tableColsForRender = Number(e.target.value);
//     const tableRowsForRender = Number(e.target.value);
//     const tableHeadersForRender = Number(e.target.value);

//     if (type === 'table-cols') {
//       this.setState({tableColsForRender});
//     }

//     if (type === 'table-rows') {
//       this.setState({tableRowsForRender});
//     }

//     if (type === 'table-header') {
//       this.setState({tableHeadersForRender});
//     }

//     if (type === 'repeat-table-header') {
//       this.setState({isRepeatTableHeader: !this.state.isRepeatTableHeader});
//     }

//     // if (type === 'cell-margin-left') {
//     //   const tableCellMarginLeft = Number(e.target.value);
//     //   this.setState({tableCellMarginLeft});
//     // }

//     // if (type === 'cell-margin-right') {
//     //   const tableCellMarginRight = Number(e.target.value);
//     //   this.setState({tableCellMarginRight});
//     // }

//     // if (type === 'cell-margin-top') {
//     //   const tableCellMarginTop = Number(e.target.value);
//     //   this.setState({tableCellMarginTop});
//     // }

//     // if (type === 'cell-margin-bottom') {
//     //   const tableCellMarginBottom = Number(e.target.value);
//     //   this.setState({tableCellMarginBottom});
//     // }

//     // if (type === 'table-row-height') {
//     //   const tableRowHeight = Number(e.target.value);
//     //   this.setState({tableRowHeight});
//     // }

//     // if ( type === 'table-row-height-auto' ) {
//     //   this.setState({bTableRowHeightAuto: !this.state.bTableRowHeightAuto});
//     // }

//     // if ( type === 'table-column-width' ) {
//     //   const tableColumnWidth = Number(e.target.value);
//     //   this.setState({tableColumnWidth});
//     // }
//   }

//   /**
//    * when user finishes selecting a file during "open file process"
//    */
//   private handleChangeFile = (e: any) => {
//     // console.log(e.target.id);
//     const {documentCore} = this.props;
//     // console.log(e.target.files)
//     const inputTarget = e.target as HTMLInputElement;
//     const file = e.target.files[0];
//     const id = e.target.id;

//     const fileReader = new FileReader();
//     const formatWriter = new FormatWriter();
//     // console.log(file)
//     if (id === 'openFile') {
//       const fileType = file.name ? file.name.slice(file.name.lastIndexOf('.')) : null;
//       const newZip = new JSZip();

//       switch (fileType) {
//         // case '.xml':

//         //   // accepted xml file?
//         //   // let fileRealName = file.name.slice(0, file.name.lastIndexOf('.'));
//         //   // if (fileRealName === 'Document') {

//         //   // create new document first
//         //   this.props.documentCore.createNewDocument(true);

//         //   // tslint:disable-next-line: no-console
//         //   console.time('read time');
//         //   this.readFile(fileReader, file, inputTarget);

//         //   // } else {
//         //   //   alert('Not accepted xml file');
//         //   // }

//         //   break;

//         case '.txt':
//           // create new document first
//           this.props.documentCore.createNewDocument(true);

//           // tslint:disable-next-line: no-console
//           console.time('read time');
//           this.readTxtFile(fileReader, file, inputTarget);

//           break;

//         // case '.zip':

//         //   // create new document first
//         //   this.props.documentCore.createNewDocument(true);

//         //   // tslint:disable-next-line: newline-per-chained-call
//         //   newZip.loadAsync(file).then((zip) => {

//         //     let documentZippedFile = null;
//         //     let stylesZippedFile = null;
//         //     let settingsZippedFile = null;
//         //     try {
//         //       documentZippedFile = newZip.file('Document.xml');
//         //       if (!documentZippedFile) {
//         //         throw new Error('Document.xml missing');
//         //       }
//         //     } catch (error) {
//         //       console.log(error)
//         //       this.errorProcess(inputTarget, ErrorMessages.MainXmlLoadFailure, error);
//         //       return ;
//         //     }

//         //     try {
//         //       stylesZippedFile = newZip.file('Styles.xml');
//         //       if (!stylesZippedFile) {
//         //         throw new Error('Styles.xml missing');
//         //       }
//         //     } catch (error) {
//         //       console.log(error)
//         //       this.errorProcess(inputTarget, ErrorMessages.MinorXmlLoadFailure, error);

//         //       // continue
//         //     }

//         //     try {
//         //       settingsZippedFile = newZip.file('Settings.xml');
//         //       if (!settingsZippedFile) {
//         //         throw new Error('Settings.xml missing');
//         //       }
//         //     } catch (error) {
//         //       console.log(error)
//         //       this.errorProcess(inputTarget, ErrorMessages.MinorXmlLoadFailure, error);

//         //       // continue
//         //     }

//         //     // tslint:disable-next-line: no-console
//         //     console.time('read time');

//         //     // tslint:disable-next-line: newline-per-chained-call
//         //     documentZippedFile.async('blob').then((unZippedDocumentFile) => {
//         //       // console.log(unZippedDocumentFile);
//         //       this.readFile(fileReader, unZippedDocumentFile, inputTarget);

//         //     });

//         //   })
//         //   .catch((error) => {
//         //     console.log(error)
//         //     this.errorProcess(inputTarget, ErrorMessages.UnzipFailure, error);
//         //   });
//         //   break;

//         case '.apo':
//           this.props.documentCore.createNewDocument(true);

//           this.readZstFile(fileReader, file, inputTarget, newZip);

//           break;

//         default:
//           alert(ErrorMessages.UnSupportedFileType);
//           break;
//       }
//     } else if (id === 'insertImage') {
//       fileReader.readAsDataURL(file);
//       // fileReader.readAsArrayBuffer(file);

//       fileReader.onload = () => {

//         // console.log(fileReader.result);
//         const imgSrc = fileReader.result;

//         // console.time("create image elem");
//         const img = new Image();
//         img.src = imgSrc as string;
//         // console.timeEnd("create image elem");  // negligible

//         const maxWidth = documentCore.getMaxWidth();
//         const maxHeight = documentCore.getMaxHeight();
//         // console.log(maxWidth, maxHeight);

//         img.onload = () => {

//           // if image width/height exceeds limits, shrink them to fit
//           if (img.width > maxWidth) {
//             const wRatio = maxWidth / img.width;
//             const hRatio = maxHeight / img.height;
//             if (img.height * wRatio > maxHeight) {
//               img.height = maxHeight;
//               img.width *= hRatio;
//             } else {
//               img.width = maxWidth;
//               img.height *= wRatio;
//             }
//           } else if (img.height > maxHeight) {
//             const wRatio = maxWidth / img.width;
//             const hRatio = maxHeight / img.height;
//             if (img.width * hRatio > maxWidth) {
//               img.width = maxWidth;
//               img.height *= wRatio;
//             } else {
//               img.height = maxHeight;
//               img.width *= hRatio;
//             }
//           }

//           this.addInlineImage(img.width, img.height, img.src);

//           // trick input field to "onChange" every time
//           inputTarget.value = '';

//         };

//         // console.log(this.props.documentCore.document)

//       };
//     } else if (id === 'apoToZip') {

//       const zstdCodec = zstdCodecPack.ZstdCodec;
//       fileReader.readAsArrayBuffer(file);

//       fileReader.onloadend = () => {
//         const reader = new Reader(this.props.documentCore.getDocument());
//         const rawBuffer = fileReader.result as ArrayBuffer;

//         // separate rawBuffer to two parts
//         const {contentBuffer}: {contentBuffer: Uint8Array} = reader.separateRawBuffer(new Uint8Array(rawBuffer));

//         zstdCodec.run( (zstd) => {
//           const streaming = new zstd.Streaming();
//           const newZip = new JSZip();
//           let newBlob = null;

//           const deCompressed = streaming.decompress(contentBuffer);
//           newBlob = new Blob([deCompressed.buffer], {type: 'application/apollo-zip'});
//           saveAs(newBlob, file.name.slice(0, file.name.lastIndexOf('.')) + '.zip');

//         });
//       };
//     } else if (id === 'zipToApo') {

//       const zstdCodec = zstdCodecPack.ZstdCodec;
//       fileReader.readAsArrayBuffer(file);

//       fileReader.onloadend = () => {
//         const buffer = fileReader.result as ArrayBuffer;
//         zstdCodec.run( (zstd) => {
//           const streaming = new zstd.Streaming();
//           const compressionLevel = 3; // default 3. 1 - 22. https://facebook.github.io/zstd/zstd_manual.html
//           // tslint:disable-next-line: no-console
//           const chunks = [];
//           const threshold = 5897510;
//           // const threshold = 1024000;
//           const arrayBufferLength = buffer.byteLength;
//           const times = Math.ceil(arrayBufferLength / threshold);
//           let start = 0;
//           for (let i = 1; i <= times; i++) {
//             start = (i - 1) * threshold;
//             if (i === times) {
//               chunks.push(new Uint8Array(buffer.slice(start)));
//             } else {
//               chunks.push(new Uint8Array(buffer.slice(start, start + threshold)));
//             }
//           }
//           // console.log(chunks)
//           // tslint:disable-next-line: newline-per-chained-call
//           const sizeHint = chunks.map((ar) => ar.length).reduce((p, c) => p + c);
//           const compressed = streaming.compressChunks(chunks, sizeHint, compressionLevel);

//           const revisedBuffer = formatWriter.addFileHeader(compressed.buffer);

//           // const newBlob: any = new Blob([compressed.buffer], {type: 'application/apollo-zstd'});
//           const fileType: string = FileSaveType.APO + ';version=' + EDITOR_VERSION;
//           const newBlob = new Blob([revisedBuffer], {type: fileType});

//           saveAs(newBlob, file.name.slice(0, file.name.lastIndexOf('.')) + '.apo');

//         });
//       };
//     } else if (id === 'insertFile') {
//       const fileType = file.name ? file.name.slice(file.name.lastIndexOf('.')) : null;
//       const newZip = new JSZip();

//       switch (fileType) {
//         case '.txt':

//           // tslint:disable-next-line: no-console
//           console.time('read time');
//           this.readTxtFile(fileReader, file, inputTarget, true);

//           break;

//         case '.apo':
//           if ( false === documentCore.canInput() ||
//             documentCore.getCurrentNewControl() instanceof NewControlNumer) {
//             window.alert('插入位置点非法，插入文件失败');
//           } else {
//             this.readZstFile(fileReader, file, inputTarget, newZip, true);
//           }

//           break;

//         default:
//           break;
//       }

//     }
//   }

//   private addInlineImage = (width: number, height: number, src: string,
//                             name?: string, type?: EquationType, svgElem?: any) => {
//     const {documentCore} = this.props;
//     documentCore.addInlineImage(width, height, src, name, type, svgElem);

//     // this.textArea.focus();
//     this.props.refresh();
//   }

//   private handleRepeatedTimesChange = (e: any) => {
//     this.setState({inputRepeatedTimes : Number(e.target.value)});
//   }

//   private handleImagePropertyChange = (e: any) => {
//     // console.log(e.target.id)
//     const {imageWidth, imageHeight, imageRatio, imagePreserveAspectRatio} = this.state;
//     let height = imageHeight;
//     let width = imageWidth;
//     let ratio = imageRatio;
//     switch (e.target.id) {
//       case 'image-height':
//         // console.log(e.target.value);
//         height = e.target.value * 1;
//         if (imagePreserveAspectRatio) {
//           width = ratio * height;
//           this.setState({imageHeight: height, imageWidth: width});
//         } else {
//           this.setState({imageHeight: height});
//         }
//         break;

//       case 'image-width':
//         width = e.target.value * 1;
//         if (imagePreserveAspectRatio) {
//           height = width / ratio;
//           this.setState({imageHeight: height, imageWidth: width});
//         } else {
//           this.setState({imageWidth: width});
//         }
//         break;

//       case 'image-ratio':
//         // calculate new ratio if checkbox WILL BE checked
//         if (!imagePreserveAspectRatio) {
//           ratio = width / height;
//           this.setState({imagePreserveAspectRatio: !this.state.imagePreserveAspectRatio, imageRatio: ratio});
//         } else {

//           // new href?
//           this.setState({imagePreserveAspectRatio: !this.state.imagePreserveAspectRatio});
//         }
//         break;

//       case 'image-name':
//         const showImageError = false;
//         this.setState({imageName: e.target.value, showImageError});
//         break;

//       case 'size-lock':
//         this.setState({imageSizeLocked: !this.state.imageSizeLocked});
//         break;

//       case 'delete-lock':
//         this.setState({imageDeleteLocked: !this.state.imageDeleteLocked});
//         break;

//       default:
//         break;
//     }
//   }

//   private readFile(fileReader: FileReader, file: any, inputTarget: HTMLInputElement): void {

//     fileReader.readAsText(file);
//     fileReader.onloadend = () => {

//       /** parse the result */
//       const reader = new Reader(this.props.documentCore.getDocument());
//       // reader.load(fileReader.result as string);
//       // console.log(fileReader.result) // the .xml opened
//       const result = reader.read(fileReader.result as string);
//       if (!result) { // fail safe
//         this.props.documentCore.createNewDocument();
//       }

//       console.timeEnd('read time');

//       // rerender
//       this.props.testDocumentXml(); // cursor is at 1st para's start
//       // this.props.refresh();

//       // trick input field to "onChange" every time
//       inputTarget.value = '';
//     };

//     // loading animation?
//     fileReader.onprogress = () => {
//       // console.log('loading...');
//     };
//   }

//   private readZstFile(fileReader: FileReader, zstfile: any, inputTarget: HTMLInputElement, newZip: JSZip, bInsertFile: boolean = false): void {
//     // don't forget to sync up with reader - readFromString() method
//     // tslint:disable-next-line: no-console
//     console.time('read time');
//     const zstdCodec = zstdCodecPack.ZstdCodec;

//     fileReader.readAsArrayBuffer(zstfile);
//     fileReader.onloadend = () => {
//       const reader = new Reader(this.props.documentCore.getDocument());
//       const rawBuffer = fileReader.result as ArrayBuffer;

//       // separate rawBuffer to two parts
//       const {headerBuffer, contentBuffer }: {headerBuffer: Uint8Array, contentBuffer: Uint8Array}
//       = reader.separateRawBuffer(new Uint8Array(rawBuffer));

//       // print out file header
//       const headerArray = Array.from(headerBuffer);
//       let output = '';
//       for (let i = 0; i < headerArray.length; i++) {
//         // 0-2: desc
//         // 3-4: length
//         // 5: version
//         // 6-23: preserved space
//         if (i < 3 || i > 5) {
//           output += String.fromCharCode(parseInt(headerArray[i] + '', 8));
//         } else {
//           // console.log(parseInt(headerArray[i] + '', 8).toString(2))
//           output += parseInt(headerArray[i] + '', 8);
//         }
//       }
//       console.log(output);

//       zstdCodec.run( (zstd) => {
//         const streaming = new zstd.Streaming();
//         // tslint:disable-next-line: no-console

//         let newBlob = null;
//         try {
//           console.time('decompression');
//           const deCompressed = streaming.decompress(contentBuffer);
//           // const deCompressed = streaming.decompress(new Uint8Array(buffer as ArrayBuffer));
//           console.timeEnd('decompression');

//           // After buffer separation, type can be original. No need version number
//           newBlob = new Blob([deCompressed.buffer], {type: 'application/apollo-zip'});
//         } catch (error) {
//           console.timeEnd('read time');

//           this.errorProcess(inputTarget, ErrorMessages.DecompressFailure, error, bInsertFile);
//           return;
//           // console.log(error.name)
//           // console.log(error.message)
//           // const e = new ErrorEvent('error', {message: 'my error', error: error})
//           // window.dispatchEvent(error)
//         }

//         // unzip the zipped blob
//         // tslint:disable-next-line: newline-per-chained-call
//         newZip.loadAsync(newBlob).then((zip) => {

//           let documentZippedFile = null;
//           let stylesZippedFile = null;
//           let settingsZippedFile = null;
//           let mediaZippedFile = null;

//           try {
//             mediaZippedFile = newZip.file('Media.xml');
//             if (!mediaZippedFile) {
//               throw new Error('Media.xml missing');
//             }
//           } catch (error) {
//             console.log(error);
//             this.errorProcess(inputTarget, ErrorMessages.MainXmlLoadFailure, error, bInsertFile);
//             return ;
//           }

//           try {
//             documentZippedFile = newZip.file('Document.xml');
//             if (!documentZippedFile) {
//               throw new Error('Document.xml missing');
//             }
//           } catch (error) {
//             console.log(error)
//             this.errorProcess(inputTarget, ErrorMessages.MainXmlLoadFailure, error, bInsertFile);
//             return ;
//           }

//           try {
//             stylesZippedFile = newZip.file('Styles.xml');
//             if (!stylesZippedFile) {
//               throw new Error('Styles.xml missing');
//             }
//           } catch (error) {
//             console.log(error);
//             this.errorProcess(inputTarget, ErrorMessages.MinorXmlLoadFailure, error, bInsertFile);

//             // continue
//           }

//           try {
//             settingsZippedFile = newZip.file('Settings.xml');
//             if (!settingsZippedFile) {
//               throw new Error('Settings.xml missing');
//             }
//           } catch (error) {
//             console.log(error);
//             this.errorProcess(inputTarget, ErrorMessages.MinorXmlLoadFailure, error, bInsertFile);

//             // continue
//           }

//           /** read media.xml first */
//           // tslint:disable-next-line: newline-per-chained-call
//           mediaZippedFile.async('blob').then((unZippedMediaFile: any) => {
//             // tslint:disable-next-line: newline-per-chained-call
//             fileReader.readAsText(unZippedMediaFile);
//             fileReader.onloadend = () => {
//               const mediaFile: string = fileReader.result as string;
//               if (!reader.readMedia(mediaFile) && !bInsertFile) { // fail safe
//                 this.props.documentCore.createNewDocument();
//               }

//               /** then read document.xml */
//               // tslint:disable-next-line: newline-per-chained-call
//               documentZippedFile.async('blob').then((unZippedDocumentFile: any) => {
//                 fileReader.readAsText(unZippedDocumentFile);
//                 fileReader.onloadend = () => {
//                   const documentFile: string = fileReader.result as string;
//                   // console.log(documentFile) // plain txt of .xml file
//                   if (!reader.read(documentFile as string, bInsertFile) && !bInsertFile) { // fail safe
//                     this.props.documentCore.createNewDocument();
//                   }
//                   // tslint:disable-next-line: no-console
//                   console.timeEnd('read time');

//                   /** read settings.xml */
//                   // tslint:disable-next-line: newline-per-chained-call
//                   settingsZippedFile.async('blob').then((unZippedSettingsFile: any) => {
//                     fileReader.readAsText(unZippedSettingsFile);
//                     fileReader.onloadend = () => {
//                       const settingsFile: string = fileReader.result as string;
//                       if (!reader.readSettings(settingsFile as string, bInsertFile) && !bInsertFile) { // fail safe
//                         this.props.documentCore.createNewDocument();
//                       }
//                       // rerender
//                       this.props.testDocumentXml(bInsertFile); // cursor is at 1st para's start
//                       // this.props.refresh();

//                       // trick input field to "onChange" every time
//                       inputTarget.value = '';
//                     };
//                   })
//                   .catch((error) => {
//                     console.log(error);
//                     this.errorProcess(inputTarget, ErrorMessages.XmlError, error, bInsertFile);
//                     return;
//                   });

//                 };

//               })
//               .catch((error) => {
//                 console.log(error);
//                 this.errorProcess(inputTarget, ErrorMessages.FetchBlobFailure, error, bInsertFile);
//                 return;
//               });
//             };

//           })
//           .catch((error) => {
//             console.log(error)
//             this.errorProcess(inputTarget, ErrorMessages.FetchBlobFailure, error, bInsertFile);
//             return;
//           });

//         }) // newzip loadasync
//         .catch((error) => {
//           console.log(error)
//           this.errorProcess(inputTarget, ErrorMessages.UnzipFailure, error, bInsertFile);
//           return;
//         });

//       }); // zstdCodec.run
//     }; // onloadend

//     // loading animation?
//     fileReader.onprogress = () => {
//       // console.log('loading...');
//     };
//   }

//   private readTxtFile(fileReader: FileReader, file: any, inputTarget: HTMLInputElement, bInsertFile: boolean = false): void {
//     fileReader.readAsText(file);
//     fileReader.onloadend = () => {

//       const reader = new Reader(this.props.documentCore.getDocument());
//       const result = reader.readFromTxt(fileReader.result as string, bInsertFile);
//       if (!result && !bInsertFile) { // fail safe
//         this.props.documentCore.createNewDocument();
//       }

//       console.timeEnd('read time');

//       // rerender
//       this.props.testDocumentXml(bInsertFile); // cursor is at 1st para's start or current pos(insert file)

//       // trick input field to "onChange" every time
//       inputTarget.value = '';
//     };

//     // loading animation?
//     fileReader.onprogress = () => {
//       // console.log('loading...');
//     };
//   }

//   private handleButtonClick(type: string, e: any): void {
//     // console.log(type);
//     if (type === 'pageConfig') {
//       this.setState({showFileConfigModal: !this.state.showFileConfigModal});
//     } else if (type === 'formatParagraph') {
//       this.setState({showFormatConfigModal: !this.state.showFormatConfigModal});
//     } else if ( 'insertNewTable' === type ) {
//       this.setState({showInsertTableConfigModal: !this.state.showInsertTableConfigModal});
//     } else if ( 'insertNewItem' === type ) {
//       this.setState({isNewControlAddItem: !this.state.isNewControlAddItem,
//         newControlItemName: '', newControlItemValue: ''});
//       setTimeout(() => {
//         this.newControlItemRef.current.focus();
//       }, 50);
//       return;
//     } else if ( 'modifyNewItem' === type ) {
//       const item = this.newControlItems[this.curNewControlItemPos - 1];
//       this.setState({isNewControlModifyItem: !this.state.isNewControlModifyItem,
//           newControlItemName: item.code, newControlItemValue: item.value});
//       return ;
//     } else if ( 'deleteNewItem' === type ) {
//       this.handleDeleteNewControlItem(e);
//       return ;
//     } else if ( 'upNewItem' === type ) {
//       this.handleShiftNewControlItem(e, true);
//       return ;
//     } else if ( 'downNewItem' === type ) {
//       this.handleShiftNewControlItem(e, false);
//       return ;
//     }
//     // put image menu later since need name validation
//     // else if (type === 'imageBasic' || type === 'imageAdvanced') {
//     //   this.props.handleModalState();
//     // }

//     const { documentCore } = this.props;
//     switch (e.target.innerHTML) {
//       case '确定':
//         if (type === 'pageConfig') {
//           this.formatPageProperty();
//           this.props.changePagePro(this.state.pageProperty);
//           this.props.refresh();
//         } else if (type === 'formatParagraph') {

//           // cm to mm
//           const paraProperty = JSON.parse(JSON.stringify(this.state.paraProperty));
//           paraProperty.indentation.firstLine *= 10;
//           paraProperty.indentation.left *= 10;
//           // this.setState({paraProperty}, function(){
//           this.props.changeParagraphPro(paraProperty);
//           this.props.refresh(0);
//           // });

//         } else if (type === 'helpInput') {

//           const testTexts = document.getElementsByClassName('test-text');
//           const testTextVals = [];
//           for (const testText of (testTexts as any)) {
//             testTextVals.push((testText as HTMLInputElement).value);
//           }
//           // console.log(testTextVals)
//           // let value = (document.getElementsByClassName("test-text")[0] as HTMLInputElement).value;

//           /** Repeat times */
//           const count = (document.getElementsByClassName('test-paraCount')[0] as HTMLInputElement).value;
//           if (testTextVals.length > 0 && count.length > 0) {
//             this.setState({showHelpConfigModal: !this.state.showHelpConfigModal}, () => {
//               this.props.testDocument([testTextVals, count]);
//             });
//           } else {
//             alert('please enter content.');
//           }

//         } else if (type === 'insertNewTable') {
//           const headerNum = (true === this.state.isRepeatTableHeader ? this.state.tableHeadersForRender : undefined );
//           documentCore.insertNewTable(this.state.tableColsForRender, this.state.tableRowsForRender,
//             headerNum, this.state.tableName);
//           this.props.refresh();
//         } else if (type === 'imageBasic') {
//           this.imageConfigModalType = 0;
//           // console.log('imageBasic');
//           const {imageWidth, imageHeight, imagePreserveAspectRatio, imageName} = this.state;

//           // check if name already exists
//           if (!documentCore.checkUniqueImageNameOtherThanSelectedImage(imageName)) {
//             this.setState({showImageError: true});
//             return null;
//           }

//           // remove SVG's fixed ratio if want to set manually
//           const curImage = documentCore.getImageSelectionInfo();
//           // console.log(curImage);
//           if (curImage) {
//             if (curImage.getAttribute('preserveAspectRatio') !== 'none') {
//               curImage.setAttribute('preserveAspectRatio', 'none');
//             }
//           }

//           // console.log(imageWidth, imageHeight, imagePreserveAspectRatio, imageName);
//           documentCore.applyDrawingProps(imageWidth, imageHeight, imagePreserveAspectRatio, imageName);

//           if (this.state.showImageError) {
//             this.setState({showImageError: false});
//           }
//           this.props.handleModalState('image');
//           this.props.refresh();

//         } else if (type === 'imageAdvanced') {
//           // console.log('imageAdvanced');

//           const {imageSizeLocked, imageDeleteLocked} = this.state;

//           documentCore.applyDrawingLocks(imageSizeLocked, imageDeleteLocked);

//           this.props.handleModalState('image');
//           this.props.refresh();
//         } else if ( 'insertNewControl' === type ) {
//           this.insertNewControl();
//           this.setNewComboxListItems();
//         } else if ( 'addNewControlItem' === type ) {
//           this.handleInsertNewControlItem(e);
//         } else if ( 'modifyNewControlItem' === type ) {
//           this.handleModifyNewControlItem(e);
//         }

//         break;

//       case '添加':
//         // console.log("add");
//         const container = document.createElement('div');
//         const textarea = document.createElement('textarea');
//         const parent = document.getElementsByClassName('input-area')[0];
//         const desc1 = document.createElement('span');
//         const desc2 = document.createElement('span');
//         const text1 = document.createTextNode('["');
//         const text2 = document.createTextNode('"]');
//         const delBtn = document.createElement('button');
//         const delText = document.createTextNode('删除');

//         desc1.appendChild(text1);
//         desc2.appendChild(text2);

//         textarea.className = 'test-text';
//         textarea.setAttribute('rows', '5');
//         delBtn.className = 'del-button';
//         delBtn.appendChild(delText);
//         delBtn.onclick = (event) => {
//           const curContainer = (event.target as HTMLInputElement).parentNode;
//           curContainer.parentNode.removeChild(container);
//           // console.log( (e.target as HTMLInputElement).parentNode )
//         };

//         container.appendChild(desc1);
//         container.appendChild(textarea);
//         container.appendChild(desc2);
//         container.appendChild(delBtn);

//         parent.appendChild(container);
//         break;

//       default:
//         this.newControlCustomProps = undefined;
//         if (type === 'helpInput') {
//           this.setState({showHelpConfigModal: !this.state.showHelpConfigModal});
//         } else if (type === 'imageBasic' || type === 'imageAdvanced') {
//           this.imageConfigModalType = 0;
//           if (this.state.showImageError) {
//             this.setState({showImageError: false});
//           }
//           this.props.handleModalState('image');
//         } else if ( 'insertNewControl' === type ) {
//           if ( true === this.state.showInsertNewControlModal ) {
//             this.setState({showInsertNewControlModal: false});
//           } else if ( true === this.state.showNewControlModal ) {
//             this.setState({showNewControlModal: false});
//           }
//           this.setNewComboxListItems();
//         } else if ( 'addNewControlItem' === type ) {
//           this.setState({isNewControlAddItem: false});
//         } else if ( 'modifyNewControlItem' === type ) {
//           this.setState({isNewControlModifyItem: false});
//         }
//         break;
//     }
//   }

//   private handleDateClick(type: string, e: any): void {
//     switch (type) {
//       case 'newControlDate':
//         const dateBoxFormat = +e.target.getAttribute('data-value');
//         this.setState({dateBoxFormat});
//         break;

//       default:
//         break;
//     }
//   }

//   private addCustomProps(e: any): void {
//     this.newControlCustomPropVisible = true;
//     this.renderNewControlCustomProp();
//     this.host.handleRefresh(0);
//   }

//   /**
//    * insert NewControl
//    */
//   private insertNewControl(): void {
//     const property: INewControlProperty = {
//       newControlName: this.state.newControlName,
//       newControlInfo: this.state.newControlInfo,
//       newControlPlaceHolder: this.state.newControlPlaceHolder,
//       newControlType: this.state.newControlType,
//       isNewControlHidden: this.state.isNewControlHidden,
//       isNewControlCanntDelete: this.state.isNewControlCanntDelete,
//       isNewControlCanntEdit: this.state.isNewControlCanntEdit,
//       isNewControlMustInput: this.state.isNewControlMustInput,
//       isNewControlShowBorder: this.state.isNewControlShowBorder,
//       isNewControlReverseEdit: this.state.isNewControlReverseEdit,
//       isNewControlHiddenBackground: this.state.isNewControlHiddenBackground,
//       customProperty: this.newControlCustomProps,
//       retrieve: this.state.retrieve,
//     };

//     switch (property.newControlType) {
//       case NewControlType.TextBox:
//         property.newControlDisplayType = this.state.newControlDisplayType;
//         property.newControlFixedLength = this.state.newControlFixedLength;
//         property.newControlMaxLength = this.state.newControlMaxLength;
//         break;

//       case NewControlType.Combox:
//       case NewControlType.MultiCombox:
//       case NewControlType.ListBox:
//       case NewControlType.MultiListBox:
//         property.newControlItems = this.newControlItems;
//         property.prefixContent = this.state.prefixContent;
//         property.selectPrefixContent = this.state.selectPrefixContent;
//         property.separator = this.state.separator;
//         // this.newControlItems = [];
//         // this.curNewControlItemPos = 0;
//         break;

//       case NewControlType.DateTimeBox:
//         property.dateBoxFormat = this.state.dateBoxFormat;

//         // current control text change
//         const newControl = this.props.documentCore.getNewControlByName(property.newControlName);
//         if (newControl) {
//           let dateTime = newControl.getDateTime();
//           if (dateTime != null) {
//             // const dateBoxFormat = newControl.getDateBoxFormat();
//             const dateBoxFormat = property.dateBoxFormat; // yet to be changed
//             if (dateBoxFormat === DateBoxFormat.Date) {
//               dateTime = dateTime.slice(0, 10);
//             } else if (dateBoxFormat === DateBoxFormat.DateAndHM) {
//               dateTime = dateTime.slice(0, 16);
//             } else if (dateBoxFormat === DateBoxFormat.DateAndHMS) {
//               // do nothing
//             } else if (dateBoxFormat === DateBoxFormat.HMS) {
//               dateTime = dateTime.slice(11);
//             } else {
//               // what else? lol
//             }
//             newControl.setNewControlText(dateTime);
//           }
//         }

//         break;

//       default:
//         break;
//     }

//     if ( true === this.state.showInsertNewControlModal ) { // first time insert struct
//       // this.setState({showInsertNewControlModal: !this.state.showInsertNewControlModal});
//       // const bSuccess = this.props.documentCore.addNewControl(property);
//       // // console.log(bSuccess)
//       // if ( true === bSuccess ) {
//       //   this.props.refresh();
//       // } else {
//       //   window.alert('插入位置点非法，插入元素失败');
//       // }
//     } else if ( true === this.state.showNewControlModal ) { // from right menu
//       // this.setState({showNewControlModal: !this.state.showNewControlModal});
//       // this.host.setNewControlDropButton(property);
//       // this.props.documentCore.setNewControlProperty(property);
//       // this.host.handleRefresh();
//     }

//     this.isNewControlTypeDisable = false;
//     this.curNewControlType = NewControlType.Empty;
//     this.newControlItems = [];
//     this.curNewControlItemPos = 0;
//     this.newControlCustomProps = undefined;
//     this.newControlCustomPropName = undefined;
//   }

//   private handleInsertNewControlItem(e: any): void {
//     if ( null == this.state.newControlItemName || '' === this.state.newControlItemName ) {
//       this.setState({isNewControlAddItem: false});
//       alert('列表项名称不能为空');
//       return ;
//     }

//     let bSuccess = true;
//     if ( null == this.newControlItems ) {
//       this.newControlItems = [];
//     }

//     for (let index = 0, length = this.newControlItems.length; index < length; index++) {
//         const item = this.newControlItems[index];

//         if ( item.code === this.state.newControlItemName ) {
//           bSuccess = false;
//           break;
//         }
//     }

//     if ( true === bSuccess ) {
//       this.insertNewControlItem(e); // 0 , this.state.newControlItemName, this.state.newControlItemValue);

//       const cursorInNewControlName = this.props.documentCore.getCursorInNewControlName();
//       if ( true === this.bCursorInNewControl && cursorInNewControlName !== this.state.newControlName ) {
//         this.props.documentCore.insertNewControlItem(this.state.newControlName,
//           this.curNewControlItemPos, this.state.newControlItemName, this.state.newControlItemValue);
//       }
//     } else {
//       alert('禁止插入相同名称选项');
//     }

//     this.setState({isNewControlAddItem: false});
//   }

//   private insertNewControlItem( e: any ): void { // pos: number , code: string, value: string ): void {
//     const table = document.getElementById('new-control-list-items') as HTMLTableElement;
//     if ( table ) {
//       if ( 1 === table.rows.length ) {
//         this.curNewControlItemPos = 1;
//       } else {
//         table.rows[this.curNewControlItemPos].bgColor = '';
//       }

//       const item = new CodeValueItem(this.state.newControlItemName, this.state.newControlItemValue);
//       this.insertItem(table, item);
//       return ;
//     }
//   }

//   private handleModifyNewControlItem(e: any): void {
//     const table = document.getElementById('new-control-list-items') as HTMLTableElement;
//     if ( table ) {
//       if ( 1 >= table.rows.length ) {
//         return ;
//       } else {
//         if ( null == this.state.newControlItemName || '' === this.state.newControlItemName ) {
//           alert('列表项名称不能为空');
//         } else {
//           let bSame = false;
//           for (let index = 0, length = this.newControlItems.length; index < length; index++) {
//             const item = this.newControlItems[index];

//             if ( item.code === this.state.newControlItemName && index !== this.curNewControlItemPos - 1 ) {
//               bSame = true;
//               break;
//             }
//           }

//           if ( false === bSame ) {
//             const newRow = table.rows[this.curNewControlItemPos];
//             newRow.cells[0].innerText = this.state.newControlItemName;
//             newRow.cells[1].innerText = this.state.newControlItemValue;
//             this.newControlItems[this.curNewControlItemPos - 1].code = this.state.newControlItemName;
//             this.newControlItems[this.curNewControlItemPos - 1].value = this.state.newControlItemValue;
//           } else {
//             alert('列表项名称不能相同');
//           }
//         }
//       }
//     }

//     this.setState({isNewControlModifyItem: false});
//   }

//   private handleDeleteNewControlItem(e: any): void {
//     const table = document.getElementById('new-control-list-items') as HTMLTableElement;
//     if ( table ) {
//       if ( 1 === table.rows.length ) {
//         return ;
//       } else {
//         table.deleteRow(this.curNewControlItemPos);
//         this.newControlItems.splice(this.curNewControlItemPos - 1, 1);

//         const length = table.rows.length;
//         if ( 1 < length ) {
//           if ( length === this.curNewControlItemPos ) {
//             this.curNewControlItemPos--;
//           }

//           table.rows[this.curNewControlItemPos].bgColor = NewControlDefaultSetting.DefaultFocusHighLightColor;
//         } else {
//           return ;
//         }
//         return ;
//       }
//     }
//   }

//   private handleShiftNewControlItem(e: any, bUp: boolean): void {
//     const table = document.getElementById('new-control-list-items') as HTMLTableElement;
//     if ( table ) {
//       const length = table.rows.length;
//       if ( 2 >= length ) {
//         return ;
//       } else {
//         if ( true === bUp ) {
//           if ( 1 < this.curNewControlItemPos ) {
//             const curRow = table.rows[this.curNewControlItemPos];
//             const code = curRow.cells[0].innerText;
//             const value = curRow.cells[1].innerText;
//             table.deleteRow(this.curNewControlItemPos);

//             this.curNewControlItemPos--;
//             this.newControlItems.splice(this.curNewControlItemPos, 1);

//             const item = new CodeValueItem(code, value);
//             this.insertItem(table, item);
//           }
//         } else {
//           if ( length - 1 !== this.curNewControlItemPos ) {
//             const curRow = table.rows[this.curNewControlItemPos];
//             const code = curRow.cells[0].innerText;
//             const value = curRow.cells[1].innerText;
//             table.deleteRow(this.curNewControlItemPos);

//             this.newControlItems.splice(this.curNewControlItemPos - 1, 1);
//             this.curNewControlItemPos++;

//             const item = new CodeValueItem(code, value);
//             this.insertItem(table, item);
//           }
//         }
//       }
//     }
//   }

//   private handleMouseDown(type: string, e: any): void {
//     if ( 'select-new-control-item' === type ) {
//       const table = document.getElementById('new-control-list-items') as HTMLTableElement;
//       if ( null != table && 'TD' === e.target.tagName ) {
//         const curRowIndex = e.target.parentNode.rowIndex;

//         if ( null != curRowIndex && this.curNewControlItemPos !== curRowIndex ) {
//           table.rows[this.curNewControlItemPos].bgColor = '';

//           table.rows[curRowIndex].bgColor = NewControlDefaultSetting.DefaultFocusHighLightColor;
//           this.curNewControlItemPos = curRowIndex;
//         }
//       }
//     }
//   }

//   private setNewComboxListItems(bCreateNew: boolean = true): void {
//     if ( NewControlType.Combox === this.state.newControlType
//       || NewControlType.MultiCombox === this.state.newControlType
//       || NewControlType.ListBox === this.state.newControlType
//       || NewControlType.MultiListBox === this.state.newControlType ) {
//       const table = document.getElementById('new-control-list-items') as HTMLTableElement;

//       if ( true === bCreateNew ) {
//         this.newControlItems = [];

//         if ( table && 1 < table.rows.length ) {
//           for (let index = table.rows.length - 1; index > 0; ) {
//             table.deleteRow(index);
//             index = table.rows.length - 1;
//           }
//         }
//       } else if ( null != this.newControlItems ) {
//         this.curNewControlItemPos = 0;
//         for (let index = 0; index < this.newControlItems.length; index++) {
//           const item =  this.newControlItems[index];

//           const newRow = table.insertRow(index + 1);
//           if ( 0 === index ) {
//             newRow.bgColor = NewControlDefaultSetting.DefaultFocusHighLightColor;
//             this.curNewControlItemPos = 1;
//           }

//           const newCell1 = newRow.insertCell(0);
//           const newText1 = document.createTextNode(item.code);
//           newCell1.appendChild(newText1);
//           const newCell2 = newRow.insertCell(1);
//           const newText2 = document.createTextNode(item.value);
//           newCell2.appendChild(newText2);
//         }
//       }
//     }
//   }

//   private insertItem(table: HTMLTableElement, item: CodeValueItem): void {
//     const newRow = table.insertRow(this.curNewControlItemPos);
//     newRow.bgColor = NewControlDefaultSetting.DefaultFocusHighLightColor;
//     const newCell1 = newRow.insertCell(0);
//     const newText1 = document.createTextNode(item.code);
//     newCell1.appendChild(newText1);
//     const newCell2 = newRow.insertCell(1);
//     const newText2 = document.createTextNode(item.value);
//     newCell2.appendChild(newText2);

//     this.newControlItems.splice(this.curNewControlItemPos - 1, 0, item);
//   }

//   private errorProcess(inputTarget: HTMLInputElement, errorMessage: ErrorMessages, error: any, bInsertFile: boolean = false): void {

//     if (!bInsertFile) {
//       this.props.documentCore.createNewDocument();
//     }

//     // rerender
//     this.props.testDocumentXml(); // cursor is at 1st para's start

//     // trick input field to "onChange" every time
//     inputTarget.value = '';

//     alert(errorMessage);

//     window.dispatchEvent(new ErrorEvent('error', {message: errorMessage, error}));
//   }

//   private closeModal = (type: string) => {
//     if (type === 'equation') {
//       this.setState({showEquationModal: !this.state.showEquationModal});
//     }
//   }

//   private generate(): void {
//     const formatWriter = new FormatWriter();

//     const {documentCore, xmlProps} = this.props;
//     // tslint:disable-next-line: newline-per-chained-call
//     formatWriter.generate(xmlProps).then(() => {
//       documentCore.setDirty(false);
//     // tslint:disable-next-line: newline-per-chained-call
//     }).catch((error) => {
//       // no need to deal with error here, just coerce console is fine
//       // console.log(error)
//       documentCore.setDirty(false);
//     });
//   }

//   private handleNewControlPropValueChange = (e: any): void => {
//     const id = e.target.id;
//     const name = e.target.name;

//     if ( 'new-control-name' === id ) {
//       this.setState({newControlName: String(e.target.value)});
//     } else if ( 'new-control-info' === id ) {
//       this.setState({newControlInfo: String(e.target.value)});
//     } else if ( 'place-holder' === id ) {
//       this.setState({newControlPlaceHolder: String(e.target.value)});
//     } else if ( 'new-control-type' === name ) {
//       let type = Number(e.target.value);
//       if ( this.state.isMultiSelect ) {
//         if ( NewControlType.Combox === type ) {
//           type = NewControlType.MultiCombox;
//         } else if ( NewControlType.ListBox === type ) {
//           type = NewControlType.MultiListBox;
//         }
//       }

//       this.setState({newControlName: this.props.documentCore.makeUniqueNewControlName(type), newControlType: type});
//     } else if ( 'hidden-new-control' === id ) {
//       this.setState({isNewControlHidden: !this.state.isNewControlHidden});
//     } else if ( 'cannt-delete' === id ) {
//       this.setState({isNewControlCanntDelete: !this.state.isNewControlCanntDelete});
//     } else if ( 'cannt-edit' === id ) {
//       this.setState({isNewControlCanntEdit: !this.state.isNewControlCanntEdit});
//     } else if ( 'must-input' === id ) {
//       this.setState({isNewControlMustInput: !this.state.isNewControlMustInput});
//     } else if ( 'hidden-border' === id ) {
//       this.setState({isNewControlShowBorder: !this.state.isNewControlShowBorder});
//     } else if ( 'revert-edit' === id ) {
//       this.setState({isNewControlReverseEdit: !this.state.isNewControlReverseEdit});
//     } else if ( 'hidden-background' === id ) {
//       this.setState({isNewControlHiddenBackground: !this.state.isNewControlHiddenBackground});
//     } else if ('display-type' === name ) {
//       this.setState({newControlDisplayType: Number(e.target.value)});
//     } else if ( 'fixed-length' === id ) {
//       this.setState({newControlFixedLength: Number(e.target.value)});
//     } else if ( 'max-length' === id ) {
//       this.setState({newControlMaxLength: Number(e.target.value)});
//     } else if ( 'is-multi-combo-box' === id ) {
//       let type = this.state.newControlType;
//       if ( !this.state.isMultiSelect ) {
//         if ( NewControlType.Combox === type ) {
//           type = NewControlType.MultiCombox;
//         } else if ( NewControlType.ListBox === type ) {
//           type = NewControlType.MultiListBox;
//         }
//       } else {
//         if ( NewControlType.MultiCombox === type ) {
//           type = NewControlType.Combox;
//         } else if ( NewControlType.MultiListBox === type ) {
//           type = NewControlType.ListBox;
//         }
//       }
//       // const type = true === this.state.isMultiSelect ? NewControlType.Combox : NewControlType.MultiCombox;
//       this.setState({isMultiSelect: !this.state.isMultiSelect,
//                     newControlName: this.props.documentCore.makeUniqueNewControlName(type), newControlType: type});
//     } else if ( 'new-control-item-name' === id ) {
//       this.setState({newControlItemName: String(e.target.value)});
//     } else if ( 'new-control-item-value' === id ) {
//       this.setState({newControlItemValue: String(e.target.value)});
//     } else if ( 'select-prefix-content' === id ) {
//       this.setState({selectPrefixContent: String(e.target.value)});
//     } else if ( 'prefix-content' === id ) {
//       this.setState({prefixContent: String(e.target.value)});
//     } else if ( 'separator' === id ) {
//       this.setState({separator: String(e.target.value)});
//     } else if ( 'is-retrieve-combo-box' === id) {
//       this.setState({retrieve: !this.state.retrieve});
//     }

//     if ( 'new-control-type' === name ) {
//       if ( NewControlType.Section !== Number(e.target.value) ) {
//         if ( true !== this.state.showNewControlExtend) {
//           this.setState({showNewControlExtend: !this.state.showNewControlExtend});
//         }

//         if ( 'display-type' === name ) {
//           this.setState({newControlDisplayType: Number(e.target.value)});
//         } else if ( 'fixed-length' === id ) {
//           this.setState({newControlFixedLength: Number(e.target.value)});
//         } else if ( 'max-length' === id ) {
//           this.setState({newControlMaxLength: Number(e.target.value)});
//         }
//       } else {
//         this.setState({showNewControlExtend: !this.state.showNewControlExtend});
//       }
//     }
//   }

//   private popNewControlPropertyWindow(): void {
//     // if ( true === this.props.documentCore.isCursorInNewControl() ) {
//       const property = this.props.documentCore.getCursorInNewControlProperty();
//       this.isNewControlTypeDisable = true;
//       this.curNewControlType = property.newControlType;
//       if (NewControlType.NumberBox === property.newControlType) {
//         this.newControlProps = property;
//         this.newControlNumVisible = true;
//         return;
//       }

//       this.newControlCustomPropName = property.newControlName;

//       const isMultiSelect = NewControlType.MultiCombox === property.newControlType
//                             || NewControlType.MultiListBox === property.newControlType;
//       this.setState({showNewControlModal: true, // isNewControlTypeDisable: true,
//                   showNewControlExtend: NewControlType.Section !== property.newControlType,
//                   newControlName: property.newControlName,
//                   newControlInfo: property.newControlInfo || '', newControlPlaceHolder: property.newControlPlaceHolder || '',
//                   newControlType: property.newControlType, isNewControlHidden: property.isNewControlHidden,
//                   isNewControlCanntDelete: property.isNewControlCanntDelete,
//                   isNewControlCanntEdit: property.isNewControlCanntEdit,
//                   isNewControlMustInput: property.isNewControlMustInput,
//                   isNewControlShowBorder: property.isNewControlShowBorder,
//                   isNewControlReverseEdit: property.isNewControlReverseEdit,
//                   isNewControlHiddenBackground: property.isNewControlHiddenBackground,
//                   newControlDisplayType: property.newControlDisplayType,
//                   newControlFixedLength: property.newControlFixedLength || 0,
//                   newControlMaxLength: property.newControlMaxLength || 0,
//                   isMultiSelect, prefixContent: property.prefixContent || '',
//                   selectPrefixContent: property.selectPrefixContent || '', separator: property.separator || '',
//                   retrieve: property.retrieve || false, dateBoxFormat: property.dateBoxFormat || 0,
//         }, () => {
//           if ( NewControlType.Combox === property.newControlType
//             || NewControlType.MultiCombox === property.newControlType
//             || NewControlType.ListBox === property.newControlType
//             || NewControlType.MultiListBox === property.newControlType) {
//               this.newControlItems = property.newControlItems;
//               this.setNewComboxListItems(false);
//           } else {
//             this.newControlItems = [];
//           }
//         });
//   }

//   private getNewComboxType(): NewControlType {
//     if ( NewControlType.ListBox === this.state.newControlType
//       || NewControlType.MultiListBox === this.state.newControlType) {
//       return NewControlType.Combox;
//     } else {
//       return this.state.newControlType;
//     }
//   }

//   private initRightMenu(): void {
//     const rightMenuList = JSON.parse(JSON.stringify(MENU_LIST));

//     this.rightMenuList = rightMenuList;
//     const rightMenuOption: any = {};
//     rightMenuList.forEach((item) => {
//       rightMenuOption[item['index']] = item;

//       if ( item.childs ) {
//         item.childs.forEach((child) => {
//           rightMenuOption[child['index']] = child;
//         });
//       }
//     });
//     this.rightMenuOption = rightMenuOption;
//   }

//   private isNewControlItemsEmpty(): boolean {
//     if ( null == this.newControlItems || 0 === this.newControlItems.length ) {
//       return true;
//     }

//     return false;
//   }

// }
