@import './global.less';
.hz-editor-container .editor-select {
    display: inline-block;
    width: 100%;
    height: 28px;

    &  * {
        box-sizing: border-box;
    }

    .select-input {
        cursor: pointer;
        input {
            cursor: inherit;
        }

        &.readonly, &.disabled {
            cursor: default;
            .addonAfterGroupWrapper {
                display: none;
            }
        }
    }

    .editor-input.readonly span.inputWrapper > span {
        &:first-child {
            border-right: none;
        }
        &.addonAfterGroupWrapper {
            border-left: none;
            background-color: #fff;
        }
    }

    .editor-input .inputGroupWrapper {
        border-right: none;
    }

    .inputGroupWrapper:focus, .inputGroupWrapper:hover {
        & + .addonAfterGroupWrapper {
            border-color: #099FFF;
        }
    }

    .select-btn {
        display: inline-block;
        width: 12px;
        height: 26px;
        margin-right: 5px;
        margin-left: 2px;
        line-height: 22px;
        vertical-align: top;
        pointer-events: none;

        &::before {
            display: inline-block;
            width: 0;
            height: 0;
            margin-top: 10px;
            border-top: 5px solid  #000000;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            content: " ";
            pointer-events: none;
        }

        // &::after {
        //     position: absolute;
        //     bottom: 0;
        //     width: 0;
        //     height: 0;
        //     left: 50%;
        //     margin-left: -10px;
        //     content: " ";
        //     border-bottom: 10px solid  #000000 ;
        //     border-left: 10px solid transparent ;
        //     border-right: 10px solid transparent ;
        // }
    }
}

.hz-editor-select-container {
    .editor-select-list {
        position: fixed;
        z-index: 9999;
        left: -200%;
        font-size: @fontSize;
        max-height: 200px;
        font-family: @fontFamily;
        overflow: auto;

        ul {
            padding: 8px 0;
            margin: 0;
            border: 1px solid @borderColor;
            background: #fff;
            border-radius: 3px;

            li {
                height: 30px;
                padding: 0 15px;
                line-height: 30px;
                color: @color;
                text-align: left;
                list-style: none;

                label {
                    display: inline-block;
                    width: 100%;
                    line-height: 30px;
                    overflow:hidden;
                    text-overflow:ellipsis;
                    white-space:nowrap;
                    cursor: pointer;
                }

                &.active {
                    background: @activeBgColor;
                }

                &:hover:not(.disabled) {
                    color: @activeColor;
                }

                &.disabled > * {
                    cursor: default;
                    color: @color4;
                    background: transparent;
                }

                &.no-data:hover, &.no-data {
                    color: @color4;
                }
            }
        }
    }
}

.hz-editor-container .c-select-container {
    display: flex;
    align-items: center;
    padding: 0 3px;
    .c-label {
        padding: 2px;
    }
    .c-selector {
        padding: 2px 8px 2px 8px;
        background-color: #FFFFFF;
        border-radius: 4px;
        border: 1px solid #FFFFFF;
        cursor: pointer;
        position: relative;
        z-index: 200;
        flex: 1;

        font-size: 14px;
        line-height: 20px;
        font-weight: 400;
        text-align: left;

        &:hover {
            border-color: #DEE7FF;
        }

        .select {
            display: flex;
            width: 100%;
            align-items: center;

            .content {
                flex-grow: 1;
            }

            .radio-down {
                border-top: 7px solid #949494;
                border-left: 7px solid #FFFFFF;
                border-right: 7px solid #FFFFFF;
                border-bottom: 0;
            }

            .radio-up {
                border-bottom: 7px solid #949494;
                border-left: 7px solid #FFFFFF;
                border-right: 7px solid #FFFFFF;
                border-top: 0;
            }

        }

        .items {
            position: absolute;
            left: 0;
            top: calc(100% + 2px);
            width: 100%;
            background: #FFFFFF;
            border: 1px solid #DDDDE3;
            box-shadow: 0px 2px 20px rgba(39, 53, 70, 0.2);
            border-radius: 4px;

            .item {
                padding: 2px 8px 2px 8px;

                &:hover {
                    background-color: #DEE7FF;
                }
            }
        }
    }
}

.hz-editor-container .c-selector {
    width: 120px;
    padding: 2px 8px 2px 12px;
    background-color: #FFFFFF;
    border-radius: 4px;
    border: 1px solid #FFFFFF;
    cursor: pointer;
    position: relative;
    z-index: 200;

    font-size: 14px;
    line-height: 20px;
    font-weight: 400;
    text-align: left;

    &:hover {
        border-color: #DEE7FF;
    }

    .select {
        display: flex;
        width: 100%;
        align-items: center;

        .content {
            flex-grow: 1;
        }

        .radio-down {
            border-top: 7px solid #949494;
            border-left: 7px solid #FFFFFF;
            border-right: 7px solid #FFFFFF;
            border-bottom: 0;
        }

        .radio-up {
            border-bottom: 7px solid #949494;
            border-left: 7px solid #FFFFFF;
            border-right: 7px solid #FFFFFF;
            border-top: 0;
        }

    }
    .items {
        position: absolute;
        left: 0;
        top: calc(100% + 2px);
        width: 100%;
        background: #FFFFFF;
        border: 1px solid #DDDDE3;
        box-shadow: 0px 2px 20px rgba(39, 53, 70, 0.2);
        border-radius: 4px;

        .item {
            padding: 2px 8px 2px 12px;
            &:hover {
                background-color: #DEE7FF;
            }
        }
    }
}
