import {RecalcResultType} from '../Document';
import Paragraph from '../Paragraph';
import {SectionProperty} from '../SectionProperty';
import {ParaLineRange} from './ParaLine';
import {ParagraphContentPos} from './ParagraphContent';
import DocumentContentBase from '../DocumentContentBase';
import { getPxForMM } from '../util';
import { AlignType } from '../../../common/commonDefines';

/**
 * 排版中段落所要用到的各种变量和状态值
 */
export class ParagraphRecalculateStateWrap {
    public paragraph: Paragraph;
    public parent: DocumentContentBase = null;
    public pageAbs: number = 0;
    public bInTable: boolean = false;
    public sectPr: SectionProperty = null;

    public bFast: boolean = false; // 快速重新计算

    public page: number = 0;
    public line: number = 0;
    public range: number = 0;

    public ranges: ParaLineRange[] = [];
    public rangesCount: number = 0;

    public bEmptyLine: boolean = true;
    public bStartWord: boolean = false;
    public bWord: boolean = false;

    public wordLen: number = 0; // 当前字符宽度
    public spaceLen: number = 0; // 空格长度
    public spacesCount: number = 0; // 空格数

    public textHeight: number = 0; // 行中最大的字符高度
    public lineAscent: number = 0; // 字符上行距
    public lineDescent: number = 0; // 字符下行距

    public baseLineOffset: number = 0;
    public lineTop: number = 0;
    public lineBottom: number = 0;
    public lineTop2: number = 0;
    public lineBottom2: number = 0;
    public linePrevBottom: number = 0;

    public xRange: number = 0;  // 给定段的初始水平位置
    public x: number = 0; // 当前水平位置
    public xEnd: number = 0; // 当前段的水平限制

    public y: number = 0; // 当前垂直位置
    public xStart: number = 0; // 此页面上X的初始值
    public yStart: number = 0; // 此页面上Y的初始值
    public xLimit: number = 0; // 限制此页面上的X值
    public yLimit: number = 0; // 限制此页面上的Y值

    public bBreakPageLineEmpty: boolean = false;
    public bBreakPageLine: boolean = false; // 此行是否为需要分页的行
    public bForceNewPage: boolean = false; // 转到新页面
    public bNewPage: boolean = false; // 转到新页面
    public bNewLine: boolean = false; // 转到行
    public bNewRange: boolean = false; // 转到新Range
    public bEnd: boolean = false; // 结束行
    // bRangeY         : boolean = false; // 由于包装，当前行包裹在Y周围。
    public bUseFirstLine: boolean = false; // 首行缩进
    public bFirstItemOnLine: boolean = true; // 行上第一个item
    public bTextOnLine: boolean = false; //

    public curPos: ParagraphContentPos = new ParagraphContentPos(); // 当前portion的相关数据

    public bMoveToLBP: boolean = false; // 断行时此行是否排满：未排满时，两端对齐方式下会插入空格，用于字符在行首或行末的判断
    public bLineBreakFirst: boolean = true;  // 一行的断行是否在第一个portion：即一行只有一个portion
    public lineBreakPos: ParagraphContentPos = new ParagraphContentPos(); // 断行时portion要被分割
    public fixedLength: number = 0;
    public newControlName: string = null;
    public newControlAlign: AlignType;
    public startPortionIndex: number;
    public newControlEndBorderBreak: boolean;
    public newControlContentBreak: boolean;
    public startXDiff: number;  // 居中对齐不需要换行时，排版的 x 需要加上fixedLength / 2
    public bWordWrap: boolean;
    public bHidden: boolean;

    public lastItem: any        =  // 记录行最后一个非空白元素，
        {
            portionPos     : -1,     // portion的索引
            portionContentPos : -1,  // 内容的索引
        };

    public recalcResult: RecalcResultType = RecalcResultType.RecalResultNextElement;

    public runRecalcInfoLast: undefined;
    public runRecalcInfoBreak: undefined;
    public bSoftLine: boolean;

    public restartPageRecalcInfo: any =     // 重新计算当前段落页面的信息
        {
            line   : 0,           // 需要计算的行号
            object : null,         // 导致重新计算的对象
        };

    constructor(para: Paragraph) {
        this.paragraph = para;
        this.bHidden = false;
        this.bSoftLine = false;
    }

    public resetPage(para: Paragraph, curPage: number): void {
        this.paragraph = para;
        this.parent = para.getParent();
        this.pageAbs = para.getAbsolutePage(curPage);
        this.page = curPage;
        this.bInTable = para.parent.isTableCellContent();

        // this.runRecalcInfoLast = ( 0 === curPage ) ? undefined : para.pages[curPage - 1].endInfo.;
        this.runRecalcInfoBreak = this.runRecalcInfoLast;
        this.bWordWrap = para.getWestCharBreakAttribute();
    }

    public resetLine(): void {

        this.bEmptyLine = true;
        this.bBreakPageLine = false;
        this.bEnd = false;

        this.lineTop = 0;
        this.lineBottom = 0;
        this.lineAscent = 0;
        this.lineDescent = 0;
        this.textHeight = 0;

        this.bNewPage = false;
        this.bNewLine = false;
        this.bFast = false;
        this.recalcResult = RecalcResultType.RecalResultNextLine;
        this.bSoftLine = false;
    }

    public resetRange(x: number, xEnd: number): void {

        this.spaceLen = 0;
        this.wordLen = 0;
        this.spacesCount = 0;
        this.bWord = false;
        this.bFirstItemOnLine = true;
        this.bNewRange = false;
        this.bStartWord = false;
        this.x = x;
        this.xEnd = xEnd;
        this.xRange = x;

        this.bLineBreakFirst = true; // ++++++++++wen.luo key: bug31906 20190710++++++++++++
        this.bMoveToLBP = false;
        this.lineBreakPos = new ParagraphContentPos();
        this.lastItem.portionContentPos = -1;
        this.lastItem.portionPos = -1;
        this.bSoftLine = false;
    }

    public resetRanges(): void {

        this.ranges = [];
        this.rangesCount = 0;
    }

    public resetRestartPageRecalcInfo(): void {

        this.restartPageRecalcInfo.line = 0;
        this.restartPageRecalcInfo.object = null;
    }

    public setRestartPageRecalcInfo(line: number, obj: any): void {

        this.restartPageRecalcInfo.line = line;
        this.restartPageRecalcInfo.object = obj;
    }

    /**
     * 设置此行断行处在portion.content中的位置，
     * @param curPortionContentPos 此行断行在portion.content中的位置
     * @param bFirstItemOnLine 是否为第一个portion
     */
    public setLineBreakPos( curPortionContentPos: number, bFirstItemOnLine: boolean ): void {

        this.lineBreakPos.set(this.curPos);
        this.lineBreakPos.add(curPortionContentPos);
        this.bLineBreakFirst = bFirstItemOnLine;
    }

    public updateCurPos( portionPos: number, depth: number ): void {
        this.curPos.update(portionPos, depth);
    }

    public setFast( b: boolean ): void {
        this.bFast = b;
    }

    public isFastRecalculate(): boolean {
        return this.bFast;
    }

    public isInTable(): boolean {
        return this.bInTable;
    }

    public setNewControlFixedLength(obj: any): void {
        if ( obj ) {
            const titleLength = obj.titleLength ? obj.titleLength : 0;
            this.fixedLength = getPxForMM(obj.fixedLength * 10) - titleLength;
            this.newControlName = obj.newControlName;
            this.newControlAlign = obj.align;
            this.startPortionIndex = obj.startPortionIndex;
            this.newControlEndBorderBreak = false;
            this.newControlContentBreak = false;
            this.startXDiff = 0;
        }
    }

    public resetNewControlFixedLength(): void {
        this.fixedLength = 0;
        this.newControlName = null;
        this.newControlAlign = null;
        this.startPortionIndex = -1;
        this.newControlEndBorderBreak = false;
        this.newControlContentBreak = false;
    }

    public isNewControlFixedBreak(): boolean {
        return (this.newControlEndBorderBreak || this.newControlContentBreak);
    }
}
