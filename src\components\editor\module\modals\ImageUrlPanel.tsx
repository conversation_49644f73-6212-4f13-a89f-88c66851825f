import * as React from 'react';
import Dialog from '../../ui/Dialog';
import {message} from '../../../../common/Message';
import Input from '../../ui/Input';
import Button from '../../ui/Button';
import { ImageMediaType, isValidName, NewControlType } from '../../../../common/commonDefines';
import { IFRAME_MANAGER } from '../../../../common/IframeManager';
import { ParaElementType } from '../../../../model/core/Paragraph/ParagraphContent';

interface IProps {
    documentCore: any;
    host: any;
    visible: boolean;
    id: string;
    isBigImage?: boolean;
    close: (name: string | number, bRefresh?: boolean) => void;
}

interface IState {
    bRefresh: boolean;
}

interface ImgInfo {
    base64: string;
    height: number;
    mimeType: string;
    width: number;
    _dom: HTMLImageElement;
}

export default class ImageUrlPanel extends React.Component<IProps, IState> {
    private image: {
        imageName: string, 
        imageSource: string, 
        imageUrl: string
    };
    private _canvas: HTMLCanvasElement;
    private _ctx: CanvasRenderingContext2D;
    private timeout: any;
    private visible: boolean;
    private domRef: any; // 用于设置遮罩
    private maskDom: HTMLElement;
    private controllerAbort: any;
    constructor(props: any) {
        super(props);
        this.state = {
            bRefresh: false,
        };
        this.image = {
            imageName: '',
            imageSource: '',
            imageUrl: ''
        };
        this.visible = this.props.visible;
        this.domRef = React.createRef();
    }

    public render(): any {
        // TODO: bSignPic?

        return (
            <Dialog
                visible={this.visible}
                width={350}
                open={this.open}
                title='网络图片'
                footer={this.renderFooter()}
                id={this.props.id}
            >
                <div ref={this.domRef} style={{position: 'relative'}}>
                    <div className='editor-line'>
                        <div className='w-70'>URL</div>
                        <div className='right-auto'>
                            <Input
                                value={this.image.imageUrl}
                                onChange={this.onChange}
                                name='imageUrl'
                            />
                        </div>
                    </div>
                </div>
            </Dialog>
        );
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        this.visible = nextProps.visible;
    }

    private renderFooter(): any {
        return (
            <span>
                <Button onClick={this.close}>取消</Button>
                <Button type='primary' onClick={this.confirm}>确认</Button>
            </span>
        );
    }

    private close = (bRefresh?: boolean): void => {
        if (this.controllerAbort != null) {
            this.controllerAbort.abort();
        }
        this.clearUrl();
        this.visible = false;
        if (this.timeout) {
            clearTimeout(this.timeout);
        }
        this.setState({bRefresh: !this.state.bRefresh});
        this.props.close(this.props.id, bRefresh);
    }

    private getCanvasContext(width: number, height: number): {
        canvas: HTMLCanvasElement;
        ctx: CanvasRenderingContext2D;
     } {
        if (!this._canvas) {
            this._canvas = document.createElement('canvas');
            this._ctx = this._canvas.getContext('2d');
        }
        this._canvas.width = width;
        this._canvas.height = height;
        return {
            canvas: this._canvas,
            ctx: this._ctx
        };
    }

    private open = (): void => {
        const documentCore = this.props.documentCore;
        this.image.imageName = documentCore.makeUniqueImageName(ParaElementType.ParaDrawing);

        this.setState({bRefresh: !this.state.bRefresh});
    }

    private onChange = (value: any, name: string): void => {
        this.image[name] = value;
    }

    private confirm = (): void => {
        const documentCore = this.props.documentCore;
        if (documentCore.isProtectedMode()) {
            this.close(true);
            return;
        }
        IFRAME_MANAGER.setDocId(documentCore.getCurrentId());
        const image = this.image;
        if (!isValidName(image.imageName)) {
            message.error('名称不符合规范，请重新输入');
            return;
        }
        if (!image.imageUrl) {
            message.error('请输入图片url');
            return;
        }
        if (!documentCore.checkUniqueImageNameOtherThanSelectedImage(image.imageName)) {
            message.error('名称不符合规范，请重新命名');
            return null;
        }
        this.addMask();
        this.controllerAbort = new AbortController();
        fetch(image.imageUrl, {
            method: 'get',
            signal: this.controllerAbort.signal
        }).then(async res => {
            const imgBlob = await res.blob();
            if (!this.isValidBlob(imgBlob)) {
                message.error('图片文件不正确，请输入正确的url');
                return;
            }
            await this.handleImageBlob(imgBlob);
            this.close(true);
        }).catch(error => {
            if (error.name !== 'AbortError') {
                message.error('图片拉取失败，请输入正确的url');
            }
        }).finally(() => {
            this.removeMask();
            this.clearUrl();
            this.controllerAbort = null;
        });
    }
    /** 清空url */
    private clearUrl = () => {
        this.image.imageUrl = '';
        this.setState({bRefresh: !this.state.bRefresh});
    }

    /** 添加加载遮罩 */
    private addMask = (): void => {
        const dom = this.domRef.current;
        if (dom) {
            const rect = dom.getBoundingClientRect();
            this.maskDom = document.createElement("div");
            const height = rect.height + 15;
            this.maskDom.style.height = `${height}px`;
            this.maskDom.style.width = `${rect.width + 32}px`;
            this.maskDom.style.position = 'absolute';
            this.maskDom.style.background = '#A9A9A9';
            this.maskDom.style.opacity = '0.5';
            this.maskDom.style.top = '0';
            this.maskDom.style.left = '0';
            this.maskDom.style.margin = '-10px -16px -5px -16px';
            this.maskDom.innerHTML = `<div class="loading" style="width: 120px; height: 50%; margin: ${height/4}px auto 0; ">
                <span></span>
                <span></span>
                <span></span>
                <span></span>
                <span></span>
            </div>`;
            dom.append(this.maskDom);
        }
    }
    /** 去除加载遮罩 */
    private removeMask = (): void => {
        const dom = this.domRef.current;
        if (dom && this.maskDom) {
            this.maskDom.remove();
            this.maskDom = null;
        }
    }

    /**  处理图片Blob */
    private handleImageBlob = async (blob: Blob) => {
        const origBase = await this.tBlobToBase64(blob, this.props.isBigImage);
        const imgInfo = await this.tBase64ToImgInfo(origBase);
        const finalInfo = await this.compressImage(imgInfo, this.props.isBigImage);
        this.insertImage(finalInfo);
        return;
    }

    private insertImage(imgInfo: ImgInfo): void {
        const { documentCore, host } = this.props;
        const maxWidth = documentCore.getMaxWidth(true);
        const maxHeight = documentCore.getMaxHeight(true);
        // in signature box?
        let ratio = 1;
        const curControl = documentCore.getCurrentNewControl();
        if (curControl != null) {
            const parentControl = curControl.getParent();
            if (parentControl != null && parentControl.getType() === NewControlType.SignatureBox) {
                const tempRatio = parentControl.getSignatureRatio();
                if (isNaN(tempRatio) === false) {
                    ratio = parentControl.getSignatureRatio();
                }
            }
        }
        let { width, height } = imgInfo;
        width *= ratio;
        height *= ratio;

        // if image width/height exceeds limits, shrink them to fit
        if (width > maxWidth) {
            const wRatio = maxWidth / width;
            const hRatio = maxHeight / height;
            if (height * wRatio > maxHeight) {
                height = maxHeight;
                width *= hRatio;
            } else {
                width = maxWidth;
                height *= wRatio;
            }
        } else if (height > maxHeight) {
            const wRatio = maxWidth / width;
            const hRatio = maxHeight / height;
            if (width * hRatio > maxWidth) {
                width = maxWidth;
                height *= wRatio;
            } else {
                height = maxHeight;
                width *= hRatio;
            }
        }
        if (this.props.isBigImage) {
            documentCore.addInlineImage(
                width, height, imgInfo.base64,
                null, null, null, ImageMediaType.Image,
                null, {isBigImage: this.props.isBigImage, sourceUrl: this.image.imageUrl}
            );
        } else {
            documentCore.addInlineImage(width, height, imgInfo.base64);
        }
        host.handleRefresh();
    }

    private tBlobToBase64(blob: Blob, isBigImage = false): Promise<string> {
        return new Promise(resolve => {
            const fileReader = new FileReader()
            fileReader.readAsDataURL(blob);
            fileReader.onload = () => {
                resolve(fileReader.result as string);
            }
        });
    }

    private tBase64ToImgInfo(base64: string): Promise<ImgInfo> {
        return new Promise(resolve => {
            const img = new Image();
            img.src = base64;
            img.onload = () => {
                resolve({
                    _dom: img,
                    base64,
                    width: img.width,
                    height: img.height,
                    mimeType: base64.match(/:(.*?);/)[1]
                });
            }
        });
    }

    private compressImage = (imgInfo: ImgInfo, isBigImage = false): ImgInfo => {
        if (!isBigImage) return imgInfo;
        // 确定最大边界值
        const maxSize = 1000 * 1000; // 98dpi 下 100kb
        const curSize = imgInfo.width * imgInfo.height;
        if (curSize <= maxSize) return imgInfo;
        const ratio = maxSize / curSize;
        const width = +(imgInfo.width * ratio).toFixed(0);
        const height  = +(imgInfo.height * ratio).toFixed(0);

        const {canvas, ctx} = this.getCanvasContext(width, height);
        ctx.drawImage(imgInfo._dom, 0, 0, width, height);
        const base64 = canvas.toDataURL(imgInfo.mimeType);
        return {
            ...imgInfo,
            base64,
            height: height,
            width: width,
        };
    }

    /** 检查图片blob */
    private isValidBlob = (blob: Blob): boolean => {
        if (!blob || !blob.size || !blob.type) {
            return false;
        }
        return /^image\/(jpeg|gif|jpg|png)$/.test(blob.type.toLowerCase());
    }
}
