import { SpaceType } from '../../../space-type';
import { XmlAttributeComponent, XmlComponent } from '../../../xml-components';
import { customEncodeURIComponent } from '../../../../../../common/commonMethods';

class TextAttributes extends XmlAttributeComponent<{ readonly space: SpaceType }> {
    protected readonly xmlKeys: any = { space: 'xml:space' };
}

export class Text extends XmlComponent {
    constructor(text: string) {
        super('w:t');
        // this.root.push(new TextAttributes({ space: SpaceType.PRESERVE }));
        let curText: any = text;
        if (curText) {
            if (typeof curText === 'object') {
                // tslint:disable-next-line: no-console
                console.warn('object passed to w:t');
                curText = '';
            }
            this.root.push(customEncodeURIComponent(curText));
        }
    }
}
