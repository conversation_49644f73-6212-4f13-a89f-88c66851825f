import DocumentContentElementBase from './DocumentContentElementBase';
import { TableRow } from './Table/TableRow';
import DocumentContentBase from './DocumentContentBase';
import { TablePage } from './TablePage';
import { TableBorderLineStyle, TableMeasurement,
        TableWidthType, IRowInfoOfNewGrid } from './TableProperty';
import { DocumentBorder, DocumentColor } from './Style';
import Document from './Document';
import { TableRecalculate } from './Table/TableRecalculate';
import { VerticalMergeType } from './Table/TableCellProperty';
// import DocumentFrameBounds from './FrameBounds';
import { TableCell } from './Table/TableCell';
// import { ITableLimits } from './DocumentPage';
import { TableRowLineRule } from './Table/TableRowProperty';
// import { TableAnchorPosition } from './TableAnchorPosition';
import MouseEventHandler, { MouseEventType, IMouseEvent } from '../../common/MouseEventHandler';
import { TableSelection, TableSelectionType, ITableSelectionData, ITableCellPos } from './Selection';
// import { ParaElementBase } from './Paragraph/ParaElementBase';
import { IDrawNewControlBounds, IDrawSelectionsLine } from '../DocumentCore';
import { ResultType, ITableProperty, TableBorderSettingType, DocumentSectionType,
    // RevisionChangeType,
    MessageType,
    ITableCellFormulaPar,
    // VertAlignType,
    CellFormulaCalc,
    // DataType,
    CleanModeType,
    filterChars2,
    TABLE_ROW_COUNT,
    EnableRowActionType,
    EXTERNAL_STRUCT_TYPE,
    MixFormulaParser} from '../../common/commonDefines';
import { ITextProperty } from './TextProperty';
import { ParaElementType, ParagraphContentPos } from './Paragraph/ParagraphContent';
import { GlobalEvent } from '../../common/GlobalEvent';
// import { ICurrentCursorPos } from './Paragraph/ParagraphSearchPosXY';
import { NewControl } from './NewControl/NewControl';
// import ParaProperty from './Paragraph/ParaProperty';
// import { ICursorProperty } from '../CursorProperty';
// import { IParaProperty } from '../ParaProperty';
import { getPxForMM, getMMFromPx } from './util';
// import { GraphicObjects } from './GraphicObjects/GraphicObjects';
// import { Comment } from './Comment/Comment';
// import { TableState, DocumentElementState } from './HistoryState';
// import History from './History';
import { ChangeTableDefaultCellMargins } from './Table/TableChange';
// import ContentChanges, { ContentChangesElement } from './ContentChanges';
// import { WasmInstance } from '../../common/WasmInstance';
import { DocumentContent } from './DocumentContent';
import { Region } from './Region';
import ParaPortion from './Paragraph/ParaPortion';
import Paragraph from './Paragraph';
// import HeaderFooter from './HeaderFooter';
// import { TableManager } from './Table/TableManager';
import { HistoryDescriptionType } from './HistoryDescription';
import { WasmInstance } from '../../common/WasmInstance';
import { GlobalEvent as gEvent , GlobalEventName as gEventName } from '../../common/GlobalEvent';
// import { FormulaCellManager } from './Table/FormulaCellManager';
import { TableBase } from './TableBase';
// import { ReviewInfo } from './Revision';

/**
 * 表格
 */
const gMaxRowNum = TABLE_ROW_COUNT;
const gMaxColNum = 50;
export class Table extends TableBase {
    // public tableName: string;
    // public rows: number;
    // public cols: number;

    // public content: TableRow[];
    // public pages: TablePage[];

    // public maxTopBorder: number[];
    // public maxBottomBorder: number[];  // bottom border?
    // public maxBottomMargin: number[];  // bottom margin?
    // public property: TableProperty;
    // // public logicDocument: Document;

    // public xOrigin: number;

    // public tableSumGrid: number[];
    // public tableRecal: TableRecalculate;
    // public tableGrid: number[];  // 每列单元格的宽度

    // public tableGridCalc: number[];

    // public rowsInfo: TableRowInfo[];  // 行信息记录
    // public tableRowsBottom: number[][];

    // public headerInfo: TableHeaderInfo;

    // public bTurnOffRecalc: boolean;
    // public recalcInfo: TableRecalcInfo;
    // public selection: TableSelection;

    // // public anchorPosition: TableAnchorPosition;

    // public curCell: TableCell;  // 光标当前所在单元格

    // public formulaCellManager: FormulaCellManager;

    // // private customProps: Map<string, string>;

    // private contentChanges: ContentChanges;

    // private bReadFile: boolean;     // 读取文档标记：读取文档时，不需要触发公式计算等

    // private reviewType: ReviewType;
    // private reviewInfo: ReviewInfo;

    // private markup: any;

    constructor( parent: DocumentContentBase, logicDocument: Document, rows?: number, cols?: number,
                 tableGrid?: number[], tableHeaderNum?: number, tableName?: string ) {
        super(parent, logicDocument, rows, cols, tableGrid, tableHeaderNum, tableName);

        // this.pages = [];
        // this.pages[0] = new TablePage(0, 0, 0, 0, 0, 0);

        // this.property = new TableProperty();

        // this.logicDocument = ( !logicDocument ? null : logicDocument );

        // this.rows = rows;
        // this.cols = cols;

        // this.content = [];
        // for (let index = 0; index < rows; index++) {
        //     this.content[index] = new TableRow(this, cols);
        // }

        // if ( null != tableHeaderNum ) {
        //     for (let index = 0; index < tableHeaderNum; index++) {
        //         this.content[index].setTableHeader(true);
        //     }
        // }

        // this.reIndexContent(0);
        // this.xOrigin = 0;

        this.tableRecal = new TableRecalculate(this);
        // this.tableGrid = tableGrid ? tableGrid : [];
        // this.tableSumGrid = [];
        // this.tableGridCalc = this.copyTableGrid();

        // this.bTurnOffRecalc = false;
        // this.recalcInfo = new TableRecalcInfo();
        // this.rowsInfo = [];
        // this.tableRowsBottom = [];
        // this.headerInfo = new TableHeaderInfo();
        // this.maxTopBorder = [];
        // this.maxBottomBorder = [];
        // this.maxBottomMargin = [];
        // // this.anchorPosition = new TableAnchorPosition();
        // this.curCell = ( 0 < this.content.length ) ? this.content[0].getCell(0) : null;
        // this.selection = new TableSelection();
        // // this.customProps = null;
        // this.contentChanges = new ContentChanges();
        // this.markup = {
        //     internal: {},
        //     cols: [],
        // };
        // const tableManager = this.getTableManager();
        // if ( tableManager ) {
        //     if ( null == tableName ) {
        //         tableName = tableManager.getUniqueTableName();
        //     }
        //     tableManager.add(this, tableName);
        // }
        // this.tableName = tableName;
        // this.formulaCellManager = new FormulaCellManager(this);
        // this.reviewInfo = new ReviewInfo(this.logicDocument);
        // this.reviewType = ReviewType.Common;
        // this.bReadFile = false;
    }

    // public getName(): string {
    //     return this.tableName;
    // }

    // public getType(): DocumentContentType {
    //     return DocumentContentType.Table;
    // }

    // public getColorMap(): any {
    //     return this.parent.getColorMap();
    // }

    // public getShadow(): DocumentShadow {
    //     return this.property.shadow;
    // }

    // public getTableGrid(): number[] {
    //     return this.tableGrid;
    // }

    // public reset( x: number, y: number, xLimit: number, yLimit: number, pageNum: number ): void {
    //     this.xOrigin = x;
    //     this.x            = x;
    //     this.y            = y;
    //     this.xLimit       = xLimit;
    //     this.yLimit       = yLimit;
    //     this.pageNum      = pageNum;

    //     this.pages.length = 1;
    //     this.pages[0] = new TablePage(x, y, xLimit, yLimit, 0, 0);
    // }

    // public getPageBounds(curPage: number): DocumentFrameBounds {
    //     return this.pages[curPage].bounds;
    // }

    // public addComment(bSamePara: boolean, commentId: number, comments: Comment[]): number {
    //     const selection = this.selection;
    //     const contents = this.content;
    //     const datas = selection.data;
    //     // let flag: boolean = true;
    //     if (!datas) { // 没有单元格被选中，只是单元格中某部分被选中
    //         // flag = false;
    //         const pos = selection.startPos.pos;
    //         // datas = [{rowIndex: pos.rowIndex, cellIndex: pos.cellIndex}];
    //         return contents[pos.rowIndex].content[pos.cellIndex].content.addComment(bSamePara, commentId, comments);
    //     }
    //     const bStart = commentId !== undefined;
    //     let dataIndex: number;
    //     if (bStart) {
    //         dataIndex = 0;
    //     } else {
    //         dataIndex = datas.length - 1;
    //     }
    //     const data = datas[dataIndex];
    //     return contents[data.rowIndex].content[data.cellIndex].content.addComment(bSamePara, commentId, comments, true);
    // }

    // public deleteComment(pos: ParagraphContentPos, bDeleteComment: boolean = false): void {
    //     const rowIndex = pos.get(1);
    //     const cellIndex = pos.get(2);
    //     const datas = new ParagraphContentPos();
    //     const len = pos.getDepth() + 1;
    //     for (let index = 3; index < len; index++) {
    //         datas.add(pos.get(index));
    //     }
    //     this.content[rowIndex].content[cellIndex].content.deleteComment(datas, bDeleteComment);
    // }

    // public getCurrentPageLastLine(pageIndex: number, result?: any): any {
    //     const curPage = pageIndex - this.getParentSumPageIndex();
    //     const page = this.pages[curPage];
    //     if (!page) {
    //         return;
    //     }

    //     // for (let index = page.pos, length = page.endPos; index <= length; index++) {
    //     //     contents[index].getParaLines(pageIndex, result);
    //     // }
    //     const cells = this.content[page.lastRow].content;
    //     return cells[cells.length - 1].content.getCurrentPageLastLine(pageIndex, result);
    // }

    // public getParaLines(pageIndex: number, result: {paras: any[], tables: any[]}): any {
    //     const curPage = pageIndex - this.getParentSumPageIndex();
    //     const page = this.pages[curPage];
    //     if (!page) {
    //         return;
    //     }
    //     const contents = this.content;
    //     const tableInfo = [];
    //     for (let index = page.firstRow, length = page.lastRow; index <= length; index++) {
    //         const row = contents[index];
    //         if (!row) {
    //             continue;
    //         }
    //         const cells = row.content;
    //         const rowInfos = [];
    //         for (let cellIndex = 0, cellLength = cells.length; cellIndex < cellLength; cellIndex++) {
    //             const lines = cells[cellIndex].content.getParaLines(pageIndex);
    //             const cellInfos = [];
    //             if (lines && lines.length > 0) {
    //                 cellInfos.push(...lines);
    //             }
    //             rowInfos.push(cellInfos);
    //         }
    //         tableInfo.push(rowInfos);
    //     }
    //     result.tables.push(tableInfo);
    // }

    // public selectTopPos(): void {
    //     const parent = this.parent as any;
    //     const selection = parent.selection;
    //     selection.bUse = true;
    //     selection.bStart = false;
    //     parent.curPos.contentPos = selection.endPos = selection.startPos = this.index;

    //     if (parent.isRegionContent()) {
    //         parent.parent.selectTopPos();
    //     }
    // }

    // public setContentCurPos(): void {
    //     const parent = this.parent as any;
    //     const selection = parent.selection;
    //     parent.curPos.contentPos = this.index;

    //     if (parent.isRegionContent()) {
    //         parent.parent.setContentCurPos();
    //     }
    // }

    // public getRegion(): Region {
    //     const parent = this.parent;
    //     if (parent instanceof DocumentContent) {
    //         return parent.getRegion();
    //     }
    // }

    // public isStartFromNewPage(): boolean {
    //     if ( ( 1 < this.pages.length && true === this.isEmptyPage(0) ) || ( null === this.getDocumentPrev() )) {
    //         return true;
    //     }

    //     return false;
    // }

    // /**
    //  * 是否包含表格元素
    //  * @returns true/false
    //  */
    // public isContainerTableOrTitle(): boolean {
    //     return true;
    // }

    // public getLastVisibleLine(clientY: number, bEnd: boolean): {x: number, y: number, xLimit: number} {
    //     const currentY = this.y;
    //     if (currentY > clientY) {
    //         return;
    //     }
    //     const rows = this.content;
    //     // let sumHeight: number = currentY;
    //     const rowsBottom = this.tableRowsBottom;
    //     for (let rowIndex = 0, len = rows.length; rowIndex < len; rowIndex++) {
    //         let lastVisitedY: number;
    //         if (rowIndex + 1 !== len) {
    //             lastVisitedY = rowsBottom[rowIndex + 1][0];
    //             if (lastVisitedY - clientY < 0) {
    //                 continue;
    //             }
    //         }
    //         const row = rows[rowIndex];
    //         const currentRow = this.getVerticalMergeCell(rows, row, rowIndex);
    //         let cellIndex: number = 0;
    //         if (bEnd === true) {
    //             cellIndex = currentRow.content.length - 1;
    //         }
    //         const position = currentRow.content[cellIndex].getLastVisibleLine(clientY);
    //         if (position) {
    //             return position;
    //         }
    //     }
    // }

    // public startFromNewPage(): void {
    //     this.pages.length = 1;
    //     this.pages[0] = new TablePage(0, 0, 0, 0, 0, 0);

    //     this.headerInfo.pages[0] = new TableHeaderPage();
    //     this.headerInfo.pages[0].bDraw = false;

    //     this.rowsInfo[0] = new TableRowInfo();
    //     this.rowsInfo[0].height = [];
    //     this.rowsInfo[0].y = [];
    //     this.rowsInfo[0].pages = 1;
    //     this.rowsInfo[0].topDy = [];
    //     this.rowsInfo[0].maxTopBorder = [];
    //     this.rowsInfo[0].bFirstPage = false;
    //     this.rowsInfo[0].startPage = 0;

    //     this.rowsInfo[0].xMin = 0;
    //     this.rowsInfo[0].xMax = 0;
    //     this.rowsInfo[0].maxBottomBorder = 0;

    //     this.rowsInfo[0].y[0] = 0.0;
    //     this.rowsInfo[0].height[0] = 0.0;
    //     this.rowsInfo[0].topDy[0] = 0.0;
    //     this.rowsInfo[0].maxTopBorder[0] = 0.0;

    //     for (let index = 0, count = this.content.length; index < count; index++) {
    //         this.tableRowsBottom[index] = [];
    //         this.tableRowsBottom[index][0] = 0;
    //     }

    //     this.pages[0].maxBottomBorder = 0;
    //     this.pages[0].bottomBorders = [];

    //     if ( 0 < this.content.length ) {
    //         for (let index = 0, length = this.content[0].getCellsCount(); index < length; index++) {
    //             const cell = this.content[0].getCell(index);

    //             cell.content.startFromNewPage();
    //             cell.pagesCount = 2;
    //         }
    //     }
    // }

    // public getTableCellByXY(pointX: number, pointY: number, pageIndex: number): TableCell {
    //     if (typeof pointX !== 'number' || typeof pointY !== 'number' || typeof pageIndex !== 'number') {
    //         return null;
    //     }
    //     let cell = null;
    //     const cellPos = this.getCellByXY(pointX, pointY, pageIndex);

    //     if ( cellPos ) {
    //         cell = this.content[cellPos.rowIndex].getCell(cellPos.cellIndex);
    //     }

    //     return cell;
    // }

    // public getPageContentStartPos(curPage: number, rowIndex: number, cellIndex: number): ITableLimits {
    //     let row = this.content[rowIndex];
    //     const cell = row.getCell(cellIndex);
    //     const cellMargins = cell.getMargins();
    //     const cellInfo = row.getCellInfo(cellIndex);

    //     const vMergeCount = this.getVerticalMergeCount(rowIndex, cellInfo.startGridCol, cell.getGridSpan());

    //     const pos = this.parent.getPageContentStartPos2(this.pageNum, curPage, this.index);

    //     rowIndex = rowIndex + vMergeCount - 1;
    //     row = this.content[rowIndex];

    //     let bHeader = false;
    //     let y = pos.y;
    //     if ( true !== this.headerInfo.bHeaderRecalculate
    //         && -1 !== this.headerInfo.pageIndex && 0 < this.headerInfo.count
    //         && this.headerInfo.pageIndex < curPage && true === this.headerInfo.pages[curPage].bDraw ) {
    //         y = this.headerInfo.pages[curPage].rowsInfo[this.headerInfo.count - 1].tableRowsBottom;
    //         bHeader = true;
    //     }

    //     const cellSpacing = row.getCellSpacing();
    //     if ( !cellSpacing ) {
    //         // todo
    //     }

    //     const cellsCount = row.getCellsCount();
    //     const tableBorders = this.getTableBorders();
    //     let maxTopBorder = 0;

    //     for (let curCell = 0; curCell < cellsCount; curCell++) {
    //         let cellNow = row.getCell(curCell);
    //         const vMerge = cellNow.getVMerge();

    //         if ( VerticalMergeType.Continue === vMerge ) {
    //             cellNow = this.getStartMergedCell(rowIndex,
    //                 row.getCellInfo(curCell).startGridCol, cellNow.getGridSpan());
    //         }

    //         const borderInfoTop = cellNow.getBorderInfo().top;
    //         if ( !borderInfoTop ) {
    //             continue;
    //         }

    //         for (const curBorder of borderInfoTop) {
    //             const border = this.compareBorders(curBorder, tableBorders.top, false, true);

    //             if ( TableBorderLineStyle.Single === border.value && maxTopBorder < border.size ) {
    //                 maxTopBorder = border.size;
    //             }
    //         }
    //     }

    //     pos.x = this.pages[curPage].x;
    //     y += maxTopBorder;

    //     y += cellMargins.top.width; // cell顶部区域

    //     return {
    //         x: pos.x + cellInfo.xContentStart,
    //         xLimit: pos.x + cellInfo.xContentEnd,
    //         y,
    //         yLimit: pos.yLimit,
    //         maxTopBorder,
    //     };
    // }

    // public getMaxTopBorder(rowIndex: number): number {
    //     const row = this.content[rowIndex];
    //     const cellsCount = row.getCellsCount();
    //     const tableBorders = this.getTableBorders();

    //     let maxTopBorder = 0;

    //     for (let curCell = 0; curCell < cellsCount; curCell++) {
    //         const cell = row.getCell(curCell);
    //         // const vMerge = cell.getVMerge();

    //         // if ( VerizonMerge.Continue === vMerge )
    //         //     cell = this.getStartMergedCell(rowIndex, row.getCellInfo(curCell));

    //         const borderInfoTop = cell.getBorderInfo().top;

    //         if ( null === borderInfoTop ) {
    //             continue;
    //         }

    //         for (const curBorder of borderInfoTop) {
    //             const border = this.compareBorders(curBorder, tableBorders.top, false, true);

    //             if ( TableBorderLineStyle.Single === border.value && maxTopBorder < border.size ) {
    //                 maxTopBorder = border.size;
    //             }
    //         }
    //     }

    //     return maxTopBorder;
    // }

    // public recalculatePage(curPage: number = 0, bHidden: boolean = false): RecalcResultType {
    //     this.resetReadFile();
    //     return this.tableRecal.recalculatePage(curPage, bHidden);
    // }

    // public recalculateCurPos(bUpdateX: boolean = true, bUpdateY: boolean = true): ICurrentCursorPos {
    //     if ( this.curCell ) {
    //         return this.curCell.recalculateCurPosForContent(bUpdateX, bUpdateY);
    //     }

    //     return null;
    // }

    // public getCursorPosXY(): ICursorProperty {
    //     if ( true === this.selection.bUse && TableSelectionType.TableCell === this.selection.type ) {
    //         if ( null === this.selection.data ) {
    //             // return { pageNum: 0, x: 0, y1: 0, y2: 0};
    //             return this.curCell.content.getCursorPosXY();
    //         }

    //         // const pos = this.selection.data[0];
    //         // const cell = this.content[pos.row.index].getCell(pos.cellIndex);
    //         // const pos = this.selection.endPos.pos;
    //         // let curPos = this.content[pos.rowIndex].getCell(pos.cellIndex).content
    //         //                                                         .getCursorPosXY();
    //         // if ( 0 >= curPos.x || 0 >= curPos.y1 ) {
    //             // curPos = this.curCell.content.getCursorPosXY();
    //         // }

    //         return this.curCell.content.getCursorPosXY();
    //     } else {
    //         if ( this.curCell ) {
    //             return this.curCell.content.getCursorPosXY();
    //         } else {
    //             return this.content[0].content[0].getCurPosXY();
    //         }
    //     }
    // }

    // public moveCursorToXY(pageIndex: number, pointX: number, pointY: number,
    //                       bLine: boolean = false, bDontChangeRealPos: boolean = true): void {
    //     const pos = this.getCellByXY(pointX, pointY, pageIndex);
    //     const row = this.content[pos.rowIndex];
    //     const cell = row.getCell(pos.cellIndex);

    //     this.curCell = cell;
    //     this.curCell.moveCursorToXYForContent(pageIndex - this.curCell.content.getRelativeStartPage(),
    //                                             pointX, pointY, bLine, bDontChangeRealPos);
    // }

    // public moveCursorToStartPos(bAddToSelect: boolean = false, bSelectFromStart: boolean = false): void {
    //     if ( true === bAddToSelect ) {
    //         // todo:
    //     } else {
    //         this.curCell = this.content[0].getCell(0);

    //         this.selection.bUse = false;
    //         this.selection.bStart = false;
    //         this.selection.startPos.pos = {rowIndex: 0, cellIndex: 0};
    //         this.selection.endPos.pos = {rowIndex: 0, cellIndex: 0};
    //         this.selection.curRow = 0;

    //         this.curCell.moveCursorToStartPosContent();
    //     }
    // }

    // public moveCursorToEndPos(bAddToSelect: boolean = false, bSelectFromStart: boolean = false): void {
    //     if ( true === bAddToSelect ) {
    //         // todo:
    //     } else {
    //         const rows = this.content;
    //         const rowIndex = rows.length - 1;
    //         const cells = rows[rowIndex].content;
    //         const cellIndex = cells.length - 1;
    //         this.curCell = cells[cellIndex];

    //         this.selection.bUse = false;
    //         this.selection.bStart = false;
    //         this.selection.startPos.pos = {rowIndex, cellIndex};
    //         this.selection.endPos.pos = {rowIndex, cellIndex};
    //         this.selection.curRow = rowIndex;

    //         this.curCell.moveCursorToEndPos(bAddToSelect, bSelectFromStart);
    //     }
    // }

    // /** 移动光标至选择行的最后一个单元格 */
    // public moveCursorToSelectionRowEnd(): void {
    //     let cell = null, rowIndex = 0, cellIndex = 0;
    //     if (true === this.selection.bUse && this.selection.type === TableSelectionType.TableCell) {
    //         const cellInfo = this.selection.data[this.selection.data.length - 1];
    //         if (cellInfo) {
    //             rowIndex = cellInfo.rowIndex;
    //             const cells = this.content[cellInfo.rowIndex].content;
    //             cellIndex = cells.length - 1;
    //             cell = cells[cellIndex];
    //         }
    //     } else {
    //         if (this.curCell) {
    //             const row = this.curCell.getRow();
    //             const cells = row.content;
    //             rowIndex = row.index;
    //             cellIndex = cells.length - 1;
    //             cell = cells[cellIndex];
    //         }
    //     }
    //     if (cell) {
    //         this.selection.bUse = false;
    //         this.selection.bStart = false;
    //         this.selection.startPos.pos = {rowIndex, cellIndex};
    //         this.selection.endPos.pos = {rowIndex, cellIndex};
    //         this.selection.curRow = rowIndex;
    //         this.curCell = cell;
    //         this.curCell.moveCursorToEndPos();
    //     }
    // }

    // public setContentPos(pos: ParagraphContentPos): boolean {
    //     const rowIndex = pos.shift();
    //     if (rowIndex === undefined || rowIndex >= this.getRowsCount()) {
    //         return false;
    //     }
    //     const row = this.getRow(rowIndex);
    //     const cellIndex = pos.shift();
    //     if (cellIndex === undefined || cellIndex >= row.getCellsCount()) {
    //         return false;
    //     }
    //     const curCell = row.getCell(cellIndex);
    //     this.curCell = curCell;
    //     return curCell.content
    //         .setContentPos(pos);
    // }

    // public filterContentNodes(type: number): DocumentContentElementBase[] {
    //     const contents = this.content;
    //     contents.forEach((row) => {
    //         const cells = row.content;
    //         cells.forEach((cell) => {
    //             cell.content.filterContentNodes(type);
    //         });
    //     });
    //     return [this];
    // }

    // public getSelectParas(): any[] {
    //     // let pros: ParaProperty[] = [];
    //     const selection = this.selection;
    //     const contents = this.content;
    //     let datas = selection.data;
    //     let flag: boolean = true;
    //     if (!datas) { // 没有单元格被选中，只是单元格中某部分被选中
    //         flag = false;
    //         const pos = selection.startPos.pos;
    //         datas = [{rowIndex: pos.rowIndex, cellIndex: pos.cellIndex}];
    //     }
    //     let items = [];
    //     for (let index = 0, len = datas.length; index < len; index++) {
    //         const item = datas[index];
    //         const cell = contents[item.rowIndex].content[item.cellIndex];
    //         const actDatas = cell?.content.getSelectParas();
    //         if (actDatas && actDatas.length > 0) {
    //             items = items.concat(actDatas);
    //         }
    //     }

    //     return items;
    // }

    // public getSelectedParaPros(paraPro: IParaProperty): boolean {
    //     // let pros: ParaProperty[] = [];
    //     const selection = this.selection;
    //     const contents = this.content;
    //     let datas = selection.data;
    //     let flag: boolean = true;
    //     if (!datas) { // 没有单元格被选中，只是单元格中某部分被选中
    //         flag = false;
    //         const pos = selection.startPos.pos;
    //         datas = [{rowIndex: pos.rowIndex, cellIndex: pos.cellIndex}];
    //     }
    //     for (let index = 0, len = datas.length; index < len; index++) {
    //         const item = datas[index];
    //         const cell = contents[item.rowIndex].content[item.cellIndex];
    //         if (cell && cell.content.getSelectedParaPros(paraPro, flag)) {
    //             return true;
    //         }
    //     }

    //     return false;

    //     // datas.forEach((item) => {
    //     //     const cell = contents[item.rowIndex].content[item.cellIndex];
    //     //     if (cell.getSelectedParaPros(paraPro, flag)) {
    //     //         return
    //     //     }
    //     // });

    //     // return pros;
    // }

    public getSelectText(bSelectAll?: boolean, needPara: boolean = false): string {
        if (bSelectAll === true) {
            return this.getAllTableText(needPara);
        }
        const selection = this.selection;
        const contents = this.content;
        const datas = selection.data;
        let flag: boolean = true;
        if (!datas) { // 没有单元格被选中，只是单元格中某部分被选中
            flag = false;
            const item = selection.startPos.pos;
            const cell = contents[item.rowIndex].content[item.cellIndex];
            return cell.content.getSelectText(flag);
        }
        let text = '';
        datas.forEach((item) => {
            const cell = contents[item.rowIndex].content[item.cellIndex];
            if ( cell ) {
                text += cell.content.getSelectText(flag);
            }
        });

        return text;
    }

    /**
     * description: 获取选中字体的文本格式
     */
    public getSelectedTextProperty(textPros: ITextProperty): boolean {
        const selection = this.selection;
        const contents = this.content;
        let datas = selection.data;
        let flag: boolean = true;
        if (!datas) { // 没有单元格被选中，只是单元格中某部分被选中
            flag = false;
            const pos = selection.startPos.pos;
            datas = [{rowIndex: pos.rowIndex, cellIndex: pos.cellIndex}];
        }

        for (let index = 0, len = datas.length; index < len; index++) {
            const item = datas[index];
            const cell = contents[item.rowIndex].content[item.cellIndex];
            if (cell && cell.content.getSelectedTextProperty(flag, textPros)) {
                return true;
            }
        }

        return false;
    }

    public getPosition(type: any): {x: number, y: number, pageNum: number} {
        const selection = this.selection;
        const contents = this.content;
        const datas = selection.data;
        // let rowIndex: number;
        // let cellIndex: number;
        let pos: any;
        if (!datas || datas.length === 0) { // 没有单元格被选中，只是单元格中某部分被选中
            // pos = selection.startPos.pos;
            return this.curCell.content.getPosition(type);
        } else {
            pos = datas[0];
        }
        // rowIndex = pos.rowIndex;
        // cellIndex = pos.cellIndex;
        return contents[pos.rowIndex].content[pos.cellIndex].content.getPosition(type);
    }

    public getTopElement(): DocumentContentElementBase {
        const parent = this.parent;
        if (parent.isRegionContent()) {
            return parent.getTopElement();
        }
        return this;
    }

    public getParentPos(pos: ParagraphContentPos): void {
        pos.splice(0, 0, [this.index]);
        const parent = this.parent;
        if (parent instanceof DocumentContent) {
            parent.getParentPos(pos);
        }
    }

    public getCurParaPortion(): ParaPortion {
        const selection = this.selection;
        const contents = this.content;
        const datas = selection.data;
        // let rowIndex: number;
        // let cellIndex: number;
        let pos: any;
        if (!datas || datas.length === 0) { // 没有单元格被选中，只是单元格中某部分被选中
            // pos = selection.startPos.pos;
            return this.curCell.content.getCurParaPortion();
        } else {
            pos = datas[0];
        }
        // rowIndex = pos.rowIndex;
        // cellIndex = pos.cellIndex;
        return contents[pos.rowIndex].content[pos.cellIndex].content.getCurParaPortion();
    }

    // public getSelectedContent(tbl: Table, names: string[], bKeepHalfStructBorder?: boolean, option?: any): any {
    //     const selection = this.selection;
    //     const contents = this.content;
    //     const datas = selection.data;
    //     if (!datas) { // 没有单元格被选中，只是单元格中某部分被选中
    //         const pos = selection.startPos.pos;
    //         const cell = contents[pos.rowIndex].content[pos.cellIndex];
    //         return cell.content.getSelectedCellContent(names, option);
    //     }
    //     let rowIndex: number;

    //     const rowMap = new Map<number, TableCell[]>();
    //     let cells: TableCell[] = [];

    //     // let maxCellIndex: number = 0;
    //     const maxRowIndex: number = contents.length - 1;
    //     const rowSpans: {rowIndex: number, colSpan: number, cell: TableCell}[] = [];
    //     datas.forEach((item, index) => {
    //         const row = contents[item.rowIndex];
    //         const cell = row.content[item.cellIndex];
    //         const currentCellIndex = cell.metrics.startGridCol;
    //         if (item.rowIndex !== rowIndex) {
    //             if (cells.length > 0) {
    //                 rowMap.set(rowIndex, cells);
    //             }
    //             cells = [];
    //             rowIndex = item.rowIndex;
    //         }

    //         let currentRowIndex: number = rowIndex;
    //         let colSpan: number = 1;
    //         // 统计纵向合并项
    //         while (currentRowIndex++ < maxRowIndex) {
    //             const nextRowCell = contents[currentRowIndex].content[currentCellIndex];
    //             if (nextRowCell && nextRowCell.property.verticalMerge === VerticalMergeType.Continue) {
    //                 colSpan++;
    //             } else {
    //                 break;
    //             }
    //         }
    //         if (colSpan > 1) {
    //             rowSpans.push({rowIndex, colSpan, cell});
    //         }
    //         cells.push(cell);
    //     });

    //     if (cells.length > 0) {
    //         rowMap.set(rowIndex, cells);
    //     }

    //     // 对纵向合并列进行补全
    //     this.setRowSpan(rowIndex, rowSpans, rowMap);

    //     let currentIndex = 0;
    //     for (const [num, curRow] of rowMap) {
    //         const row: TableRow = tbl.addRow(currentIndex++, 0, false);
    //         const copyRow = curRow[0].row;
    //         row.property = copyRow.property.copy();
    //         row.height = copyRow.height;
    //         // const maxLen = curRow.length;
    //         curRow.forEach((item, index) => {
    //             const newCell = item.copy(row, option);
    //             newCell.borderInfo = item.borderInfoCopy();
    //             row.addCell(index, row, newCell, false);
    //         });
    //     }

    //     tbl.property = this.property.copy();
    //     tbl.tableGrid = this.tableGrid.slice();

    //     this.setColSpan(tbl);
    // }

    // /**
    //  * 光标左移
    //  * @param bShiftLey
    //  */
    // public moveCursorLeft(bShiftLey: boolean = false): boolean {
    //     let result = true;

    //     if ( true === this.selection.bUse && TableSelectionType.TableCell === this.selection.type ) {
    //         if ( true === bShiftLey ) {
    //             const startPos = this.selection.startPos.pos;
    //             const endPos = this.selection.endPos.pos;

    //             if ( startPos.rowIndex === endPos.rowIndex && startPos.cellIndex === endPos.cellIndex
    //                 && this.parent.isSelectedSingleElement() ) {
    //                 this.selection.type = TableSelectionType.Text;
    //             } else {

    //                 if ( ( 0 === endPos.cellIndex && 0 === endPos.rowIndex )
    //                     || ( true !== this.parent.isSelectedSingleElement()
    //                             && 0 === startPos.rowIndex && 0 === endPos.rowIndex) ) {

    //                     // 选中：最后一行的最后一个cell 或者 最有一行的多个cell
    //                     this.selection.endPos.pos = { rowIndex: 0, cellIndex: 0 };
    //                     result = false;
    //                 } else if ( 0 < endPos.cellIndex && this.parent.isSelectedSingleElement() ) {

    //                     // 只选中当前行的一个cell
    //                     this.selection.endPos.pos = { rowIndex: endPos.rowIndex, cellIndex: endPos.cellIndex - 1};
    //                 } else {
    //                     // 选中多个cell
    //                     this.selection.endPos.pos = { rowIndex: endPos.rowIndex - 1, cellIndex: 0 };
    //                 }

    //                 let bForceSelectByLines = false;
    //                 if ( false === result && this.isInline() ) {
    //                     bForceSelectByLines = true;
    //                 }

    //                 this.updateSelectedCellsArray(bForceSelectByLines);
    //             }
    //         } else {
    //             this.selection.bUse = false;
    //             const pos = this.selection.data[0];
    //             this.curCell = this.content[pos.rowIndex].getCell(pos.cellIndex);
    //             this.curCell.moveCursorToStartPos();
    //         }
    //     } else {
    //         if ( false === this.curCell.content.moveCursorLeft(bShiftLey) ) {
    //             // 选中单元格
    //             if ( true === bShiftLey ) {

    //                 if ( 0 === this.curCell.index && 0 === this.curCell.row.index
    //                     && null === this.getDocumentPrev() && this.parent instanceof Document ) {
    //                     return false;
    //                 }

    //                 this.selection.bUse = true;
    //                 this.selection.type = TableSelectionType.TableCell;
    //                 this.selection.startPos.pos = { rowIndex: this.curCell.row.index, cellIndex: this.curCell.index };

    //                 const curRow = this.curCell.row;

    //                 if ( 0 === this.curCell.index && 0 === curRow.index ) {

    //                     // 选中：第一行的所有cell
    //                     this.selection.endPos.pos = { rowIndex: 0, cellIndex: curRow.getCellsCount() - 1 };
    //                     result = false;
    //                 } else if ( 0 < this.curCell.index ) {

    //                     // 只选中当前行的2个cell
    //                     this.selection.endPos.pos = { rowIndex: curRow.index, cellIndex: this.curCell.index - 1};
    //                 } else {
    //                     // 选中2行的所有cell
    //                     this.selection.endPos.pos = { rowIndex: curRow.index - 1, cellIndex: 0 };
    //                 }

    //                 this.updateSelectedCellsArray();
    //             } else {
    //                 const curCell = this.curCell;

    //                 if ( 0 !== curCell.row.index || 0 !== curCell.index ) {
    //                     // 光标移动到当前row下一个cell
    //                     if ( 0 !== curCell.index ) {
    //                         this.curCell = this.getStartMergedCell2(curCell.index - 1, this.selection.curRow);
    //                     } else {
    //                         // 光标移动到下一row
    //                         this.selection.curRow = Math.max(0, this.selection.curRow - 1);
    //                         this.curCell = this.getStartMergedCell2(
    //                             this.content[this.selection.curRow].getCellsCount() - 1, this.selection.curRow);
    //                     }

    //                     this.curCell.content.moveCursorToEndPos();
    //                 } else {
    //                     result = false;
    //                 }
    //             }
    //         } else {
    //             if ( true === bShiftLey ) {
    //                 this.selection.bUse = true;
    //                 this.selection.type = TableSelectionType.Text;
    //                 this.selection.startPos.pos = { rowIndex: this.curCell.row.index, cellIndex: this.curCell.index };
    //                 this.selection.endPos.pos = { rowIndex: this.curCell.row.index, cellIndex: this.curCell.index };
    //             }
    //         }
    //     }

    //     return result;
    // }

    // /**
    //  * 键盘光标右移
    //  * @param bShiftLey
    //  */
    // public moveCursorRight(bShiftLey: boolean = false): boolean {
    //     let result = true;

    //     if ( true === this.selection.bUse && TableSelectionType.TableCell === this.selection.type ) {
    //         if ( true === bShiftLey ) {
    //             const startPos = this.selection.startPos.pos;
    //             const endPos = this.selection.endPos.pos;

    //             if ( startPos.rowIndex === endPos.rowIndex && startPos.cellIndex === endPos.cellIndex
    //                 && this.parent.isSelectedSingleElement() ) {
    //                 this.selection.type = TableSelectionType.Text;
    //             } else {
    //                 const lastRow = this.content[this.content.length - 1];
    //                 const endRow = this.content[endPos.rowIndex];

    //                 if ( ( lastRow.getCellsCount() - 1 === endPos.cellIndex
    //                         && this.content.length - 1 === endPos.rowIndex )
    //                     || ( true !== this.parent.isSelectedSingleElement()
    //                           && this.content.length - 1 === startPos.rowIndex
    //                           && this.content.length - 1 === endPos.rowIndex) ) {

    //                     // 选中：最后一行的最后一个cell 或者 最有一行的多个cell
    //                     this.selection.endPos.pos = { rowIndex: lastRow.index, cellIndex: lastRow.getCellsCount() - 1 };
    //                     result = false;
    //                 } else if ( endRow.getCellsCount() - 1 > endPos.cellIndex
    //                             && this.parent.isSelectedSingleElement() ) {

    //                     // 只选中当前行的一个cell
    //                     this.selection.endPos.pos = { rowIndex: endPos.rowIndex, cellIndex: endPos.cellIndex + 1};
    //                 } else {
    //                     // 选中多个cell
    //                     this.selection.endPos.pos = { rowIndex: endPos.rowIndex + 1,
    //                                                  cellIndex: this.content[endPos.rowIndex + 1].getCellsCount() - 1 };
    //                 }

    //                 let bForceSelectByLines = false;
    //                 if ( false === result && this.isInline() ) {
    //                     bForceSelectByLines = true;
    //                 }

    //                 this.updateSelectedCellsArray(bForceSelectByLines);
    //             }
    //         } else {
    //             this.selection.bUse = false;
    //             const pos = this.selection.data[this.selection.data.length - 1];
    //             this.curCell = this.content[pos.rowIndex].getCell(pos.cellIndex);
    //             this.curCell.moveCursorToEndPos();
    //         }
    //     } else {
    //         if ( false === this.curCell.content.moveCursorRight(bShiftLey) ) {
    //             // 选中单元格
    //             if ( true === bShiftLey ) {
    //                 this.selection.bUse = true;
    //                 this.selection.type = TableSelectionType.TableCell;
    //                 this.selection.startPos.pos = { rowIndex: this.curCell.row.index, cellIndex: this.curCell.index };

    //                 const lastRow = this.content[this.content.length - 1];
    //                 const curRow = this.curCell.row;

    //                 if ( lastRow.getCellsCount() - 1 === this.curCell.index
    //                         && this.content.length - 1 === curRow.index ) {

    //                     // 选中：最后一行的最后一个cell
    //                     this.selection.endPos.pos = { rowIndex: lastRow.index, cellIndex: lastRow.getCellsCount() - 1 };
    //                     result = false;
    //                 } else if ( curRow.getCellsCount() - 1 > this.curCell.index ) {

    //                     // 只选中当前行的一个cell
    //                     this.selection.endPos.pos = { rowIndex: curRow.index, cellIndex: this.curCell.index + 1};
    //                 } else {
    //                     // 选中多个cell
    //                     this.selection.endPos.pos = { rowIndex: curRow.index + 1,
    //                                                     cellIndex: this.content[curRow.index + 1].getCellsCount() - 1 };
    //                 }

    //                 let bForceSelectByLines = false;
    //                 if ( false === result && this.isInline() ) {
    //                     bForceSelectByLines = true;
    //                 }

    //                 this.updateSelectedCellsArray(bForceSelectByLines);
    //             } else {
    //                 const curCell = this.curCell;

    //                 if ( this.content.length - 1 > curCell.row.index ||
    //                     this.content[curCell.row.index].getCellsCount() - 1 > curCell.index ) {
    //                     // 光标移动到当前row下一个cell
    //                     if ( this.content[curCell.row.index].getCellsCount() - 1 > curCell.index ) {
    //                         this.curCell = this.getStartMergedCell2(curCell.index + 1, this.selection.curRow);
    //                     } else {
    //                         // 光标移动到下一row
    //                         this.selection.curRow = Math.min(this.content.length - 1, this.selection.curRow + 1);
    //                         this.curCell = this.getStartMergedCell2(0, this.selection.curRow);
    //                     }

    //                     this.curCell.content.moveCursorToStartPos();
    //                 } else {
    //                     result = false;
    //                 }
    //             }
    //         } else {
    //             if ( true === bShiftLey ) {
    //                 this.selection.bUse = true;
    //                 this.selection.type = TableSelectionType.Text;
    //                 this.selection.startPos.pos = { rowIndex: this.curCell.row.index, cellIndex: this.curCell.index };
    //                 this.selection.endPos.pos = { rowIndex: this.curCell.row.index, cellIndex: this.curCell.index };
    //             }
    //         }
    //     }

    //     return result;
    // }

    // /**
    //  * 键盘光标上移
    //  * @param bShiftLey
    //  */
    // public moveCursorUp(bShiftLey: boolean = false): boolean {
    //     let result = true;

    //     if ( true === this.selection.bUse && TableSelectionType.TableCell === this.selection.type ) {
    //         // todo
    //     } else {
    //         if ( false === this.curCell.content.moveCursorUp(bShiftLey) ) {
    //             if ( true === bShiftLey ) {
    //                 // todo
    //             } else {
    //                 if ( 0 !== this.curCell.row.index ) {
    //                     const x = this.curCell.getCurPosXY().x;
    //                     const y = this.curCell.getCurPosXY().y1;
    //                     const prevRow = this.content[this.curCell.row.index - 1];
    //                     let cell: TableCell = null;

    //                     for (let index = 0, count = prevRow.getCellsCount(); index < count; index++) {
    //                         cell = prevRow.getCell(index);
    //                         const cellInfo = prevRow.getCellInfo(index);

    //                         if ( x - this.x <= cellInfo.xGridEnd ) {
    //                             break;
    //                         }
    //                     }

    //                     if ( null !== cell ) {
    //                         cell = this.getStartMergedCell2(cell.index, cell.row.index);
    //                         cell.moveCursorUpToLastRow(x, y, false);
    //                         this.curCell = cell;
    //                         this.selection.endPos.pos = { rowIndex: cell.row.index, cellIndex: cell.index };
    //                         this.selection.curRow = cell.row.index;
    //                     }
    //                 } else {
    //                     result = false;
    //                 }
    //             }
    //         } else {
    //             if ( true === bShiftLey ) {
    //                 this.selection.bUse = true;
    //                 this.selection.type = TableSelectionType.Text;
    //                 this.selection.startPos.pos = { rowIndex: this.curCell.row.index, cellIndex: this.curCell.index };
    //                 this.selection.endPos.pos = { rowIndex: this.curCell.row.index, cellIndex: this.curCell.index };
    //             }
    //         }
    //     }

    //     return result;
    // }

    // /**
    //  * 键盘光标下移
    //  * @param bShiftLey
    //  */
    // public moveCursorDown(bShiftLey: boolean = false): boolean {
    //     let result = true;

    //     if ( true === this.selection.bUse && TableSelectionType.TableCell === this.selection.type ) {
    //         // todo
    //     } else {
    //         if ( false === this.curCell.content.moveCursorDown(bShiftLey) ) {
    //             if ( true === bShiftLey ) {
    //                 // todo
    //             } else {
    //                 const row = this.curCell.row;
    //                 const vMergeCount = this.getVerticalMergeCount(row.index,
    //                                             row.getCellInfo(this.curCell.index).startGridCol,
    //                                             row.getCell(this.curCell.index)
    //                                                                         .getGridSpan());

    //                 if ( this.content.length - 1 !== row.index + vMergeCount - 1 ) {
    //                     const x = this.curCell.getCurPosXY().x;
    //                     const y = this.curCell.getCurPosXY().y1;
    //                     const lastRow = this.content[row.index + vMergeCount];
    //                     let cell: TableCell = null;

    //                     for (let index = 0, count = lastRow.getCellsCount(); index < count; index++) {
    //                         cell = lastRow.getCell(index);
    //                         const cellInfo = lastRow.getCellInfo(index);

    //                         if ( x - this.x <= cellInfo.xGridEnd ) {
    //                             break;
    //                         }
    //                     }

    //                     if ( null !== cell ) {
    //                         cell.moveCursorDownToFirstRow(x, y, false);
    //                         this.curCell = cell;
    //                         this.selection.endPos.pos = { rowIndex: cell.row.index, cellIndex: cell.index };
    //                         this.selection.curRow = cell.row.index;
    //                     }
    //                 } else {
    //                     result = false;
    //                 }
    //             }
    //         } else {
    //             if ( true === bShiftLey ) {
    //                 this.selection.bUse = true;
    //                 this.selection.type = TableSelectionType.Text;
    //                 this.selection.startPos.pos = { rowIndex: this.curCell.row.index, cellIndex: this.curCell.index };
    //                 this.selection.endPos.pos = { rowIndex: this.curCell.row.index, cellIndex: this.curCell.index };
    //             }
    //         }
    //     }

    //     return result;
    // }

    // /**
    //  * 光标上移进入表格最后一行
    //  * @param pointX
    //  * @param pointY
    //  * @param bShiftLey
    //  */
    // public moveCursorUpToLastRow(pointX: number, pointY: number, bShiftLey: boolean = false): void {
    //     if ( true === bShiftLey ) {
    //         // todo
    //     } else {
    //         const row = this.content[this.content.length - 1];
    //         let cell: TableCell = null;

    //         for (let index = 0, count = row.getCellsCount(); index < count; index++) {
    //             cell = row.getCell(index);
    //             const cellInfo = row.getCellInfo(index);

    //             if ( pointX - this.x <= cellInfo.xGridEnd ) {
    //                 break;
    //             }
    //         }

    //         if ( null !== cell ) {
    //             cell = this.getStartMergedCell2(cell.index, cell.row.index);
    //             cell.moveCursorUpToLastRow(pointX, pointY, false);
    //             this.curCell = cell;
    //             // this.selection.endPos.pos = { rowIndex: cell.row.index, cellIndex: cell.index };
    //             this.selection.curRow = cell.row.index;
    //         }
    //     }
    // }

    // /**
    //  * 光标下移进入表格第一行
    //  * @param pointX
    //  * @param pointY
    //  * @param bShiftLey
    //  */
    // public moveCursorDownToFirstRow(pointX: number, pointY: number, bShiftLey: boolean = false): void {
    //     if ( true === bShiftLey ) {
    //         // todo
    //     } else {
    //         const row = this.content[0];
    //         let cell: TableCell = null;

    //         for (let index = 0, count = row.getCellsCount(); index < count; index++) {
    //             cell = row.getCell(index);
    //             const cellInfo = row.getCellInfo(index);

    //             if ( pointX - this.x <= cellInfo.xGridEnd ) {
    //                 break;
    //             }
    //         }

    //         if ( null !== cell ) {
    //             cell.moveCursorDownToFirstRow(pointX, pointY, false);
    //             this.curCell = cell;
    //             // this.selection.endPos.pos = { rowIndex: cell.row.index, cellIndex: cell.index };
    //             this.selection.curRow = cell.row.index;
    //         }
    //     }
    // }

    // public getSelectionNodePos(pos: ParagraphContentPos, bStart: boolean): void {
    //     const selection = this.selection;
    //     const contents = this.content;
    //     const datas = selection.data;
    //     let rowIndex: number;
    //     let cellIndex: number;
    //     let data: {rowIndex: number, cellIndex: number};
    //     if (!datas || datas.length === 0) { // 没有单元格被选中，只是单元格中某部分被选中
    //         data = selection.startPos.pos;
    //         rowIndex = data.rowIndex;
    //         cellIndex = data.cellIndex;
    //         pos.add(rowIndex);
    //         pos.add(cellIndex);
    //         const cell = contents[rowIndex].content[cellIndex];
    //         return cell.content.getSelectionNodePos(pos, bStart, false);
    //     }

    //     if (bStart) {
    //         data = datas[0];
    //     } else {
    //         data = datas[datas.length - 1];
    //     }

    //     const row = contents[data.rowIndex];
    //     if (data.cellIndex >= row?.getCellsCount()) {
    //         data.cellIndex = row?.getCellsCount() - 1;
    //     }

    //     rowIndex = data.rowIndex;
    //     cellIndex = data.cellIndex;
    //     pos.add(rowIndex);
    //     pos.add(cellIndex);

    //     row.content[cellIndex].content.getSelectionNodePos(pos, bStart, true);
    // }

    // /**
    //  * 获取表格边框属性
    //  */
    // public getTableBorders(): any {
    //     return this.property.tableBorders;
    // }

    /**
     *
     * @param border1
     * @param border2
     * @param bTableBorder1 是否为table默认边框，true：table默认边框；false：cell单独设置的边框
     * @param bTableBorder2
     */
    public compareBorders(border1: DocumentBorder, border2: DocumentBorder,
                          bTableBorder1?: boolean, bTableBorder2?: boolean): DocumentBorder {
        if ( undefined === bTableBorder1 ) {
            bTableBorder1 = false;
        }

        if ( undefined === bTableBorder2 ) {
            bTableBorder2 = false;
        }

        if ( true === bTableBorder1 ) {
            return border2;
        }

        if ( true === bTableBorder2 ) {
            return border1;
        }

        if ( TableBorderLineStyle.None === border1.value ) {
            return border2;
        }

        if ( TableBorderLineStyle.None === border2.value ) {
            return border1;
        }

        if ( border1.size > border2.size ) {
            return border1;
        } else if ( border2.size > border1.size ) {
            return border2;
        }

        return border1;
    }

    /**
     * 在当前页面是否是空
     * @param curPage
     */
    public isEmptyPage(curPage: number = 0): boolean {
        // key-wasm by tinyzhi
        // if ( !this.pages[curPage]
        //      || ( this.pages[curPage].lastRow < this.pages[curPage].firstRow )
        //      || ( 0 === curPage && true !== this.rowsInfo[0].bFirstPage ) ) {
        //      return true;
        // }

        // return false;
        if ( !this.pages[curPage] ) {
            return true;
        }

        const nResult = WasmInstance.instance._Table_isEmptyPage(
            this.pages[curPage] ? 1 : 0,
            this.pages[curPage].lastRow,
            this.pages[curPage].firstRow,
            curPage,
            (this.rowsInfo[0] && this.rowsInfo[0].bFirstPage) ? 1 : 0);
        return Boolean(nResult);
        // end by tinyzhi
    }

    public getPages(): TablePage[] {
        return this.pages;
    }

    /**
     * 检查当前页之前的页面是否为空
     * @param curPage
     */
    public checkEmptyPages(curPage: number = 0): boolean {
        // key-wasm by tinyzhi
        // for (let index = curPage; index >= 0; index--) {
        //     if ( true !== this.isEmptyPage(index) ) {
        //         return false;
        //     }
        // }
        // return true;

        let emptyPageTemp = true;
        for (let index = curPage; index >= 0; index--) {
            if ( true !== this.isEmptyPage(index)) {
                emptyPageTemp = false;
                break;
            }
        }
        // const tempArray = WasmInstance.instance._arrayToHeap(emptyPageTemp as any);
        const nResult = WasmInstance.instance._Table_checkEmptyPages(curPage, (true === emptyPageTemp ? 1 : 0) );
        // WasmInstance.instance._freeArray(tempArray);
        return Boolean(nResult);
        // end by tinyzhi
    }

    public isSelectionUse(): boolean {
        return this.selection.bUse;
    }

    /**
     * 获取指定列中指定行的单元格编号
     * 如果没有找到单元格，则返回-1
     * @param curRow
     * @param startGridCol
     * @param bAllowOverlap true -- 寻找此列开始的单元格，false -- 寻找一个严格从给定列开始的单元格
     */
    public getCellIndexByStartGridCol(curRow: number, startGridCol: number, bAllowOverlap: boolean): number {
        const row = this.content[curRow];

        if ( !row ) {
            return -1;
        }

        let curGridCol = row.getBefore().gridBefore;
        const cellsCount = row.getCellsCount();

        if ( bAllowOverlap ) {
            for (let index = 0; index < cellsCount; index++) {
                if ( startGridCol === curGridCol ) {
                    return index;
                } else if ( startGridCol < curGridCol ) {
                    return index - 1;
                }

                const cell = row.getCell(index);
                curGridCol += cell.getGridSpan();
            }

            return cellsCount - 1;
        } else {
            for (let index = 0; index < cellsCount; index++) {
                if ( startGridCol === curGridCol ) {
                    return index;
                } else if ( startGridCol < curGridCol ) {
                    return -1;
                }

                const cell = row.getCell(index);
                curGridCol += cell.getGridSpan();
            }
        }

        return -1;
    }

    /**
     * 获取当前行中存在的合并单元格
     * @param startRow
     * @param startGridCol
     * @param gridSpan
     */
    public getStartMergedCell(startRow: number, startGridCol: number, gridSpan: number): TableCell {
        let result: TableCell = null;

        for (let index = startRow; index >= 0; index--) {
            const row = this.content[index];
            const cellsCount = row.getCellsCount();
            const beforeInfo = row.getBefore();
            let curGridCol = beforeInfo.gridBefore;

            let curCell = 0;
            let bWasMerged = false;

            while ( curGridCol <= startGridCol && curCell < cellsCount ) {
                const cell = row.getCell(curCell);
                const cellGridSpan = cell.getGridSpan();
                const vMerge = cell.getVMerge();

                if ( curGridCol === startGridCol && gridSpan === cellGridSpan
                    && VerticalMergeType.Continue === vMerge ) {
                    bWasMerged = true;
                    result = cell;
                    break;
                } else if ( curGridCol === startGridCol && gridSpan === cellGridSpan
                            && VerticalMergeType.Continue !== vMerge ) {
                    bWasMerged = true;
                    result = cell;
                    return result;
                } else if ( curGridCol <= startGridCol + gridSpan - 1
                            && curGridCol + cellGridSpan - 1 >= startGridCol ) {
                    break;
                }

                curGridCol += cellGridSpan;
                curCell++;
            }

            if ( false === bWasMerged ) {
                break;
            }
        }

        return result;
    }

    public getEndMergeCell(startRow: number, startGridCol: number, gridSpan: number): TableCell {
        let result: TableCell = null;

        for (let index = startRow, count = this.content.length; index < count; index++) {
            const row = this.content[index];
            const cellsCount = row.getCellsCount();
            const beforeInfo = row.getBefore();
            let curGridCol = beforeInfo.gridBefore;

            let curCell = 0;
            let bWasMerged = false;

            while ( curGridCol <= startGridCol && curCell < cellsCount ) {
                const cell = row.getCell(curCell);
                const cellGridSpan = cell.getGridSpan();
                const vMerge = cell.getVMerge();

                if ( curGridCol === startGridCol && gridSpan === cellGridSpan ) {
                    if ( VerticalMergeType.Continue === vMerge || index === startRow ) {
                        bWasMerged = true;
                        result = cell;
                        break;
                    } else {
                        return result;
                    }
                } else if ( curGridCol <= startGridCol + gridSpan - 1
                            && curGridCol + cellGridSpan - 1 >= startGridCol ) {
                    break;
                }

                curGridCol += cellGridSpan;
                curCell++;
            }

            if ( false === bWasMerged ) {
                break;
            }
        }

        return result;
    }

    /**
     * 获取单元格垂直合并数量
     * @param startRow
     * @param startGridCol
     * @param gridSpan
     */
    public getVerticalMergeCount(startRow: number, startGridCol: number, gridSpan: number): number {
        let vMergeCount = 1;

        for (let index = startRow + 1, length = this.content.length; index < length; index++) {
            const row = this.content[index];
            const cellsCount = row.getCellsCount();
            const beforeInfo = row.getBefore();
            let curGridCol = beforeInfo.gridBefore;

            let curCell = 0;
            let bWasMerged = false;

            while ( curGridCol <= startGridCol && curCell < cellsCount ) {
                const cell = row.getCell(curCell);
                const cellGridSpan = cell.getGridSpan();
                const vMerge = cell.getVMerge();

                // 当前列，行合并，合并信息
                if ( curGridCol === startGridCol && gridSpan === cellGridSpan
                        && VerticalMergeType.Continue === vMerge ) {
                    bWasMerged = true;
                    vMergeCount++;
                } else if ( curGridCol === startGridCol && gridSpan === cellGridSpan
                            && VerticalMergeType.Continue !== vMerge ) {
                    bWasMerged = true;
                    return vMergeCount;
                } else if ( curGridCol <= startGridCol + gridSpan - 1
                            && curGridCol + cellGridSpan - 1 >= startGridCol ) {
                    break;
                }

                curGridCol += cellGridSpan;
                curCell++;
            }

            if ( false === bWasMerged ) {
                break;
            }
        }

        return vMergeCount;
    }

    public getSearchInfos(search: any): void {
        const rows = this.content;
        rows.forEach((row) => {
            for (let index = 0, cellsCount = row.getCellsCount(); index < cellsCount; index++) {
                const cell = row.getCell(index);
                cell.content.getSearchInfos(search);
            }
        });
    }

    public getVerticalMergeCount2(startRow: number, startGridCol: number, gridSpan: number): number {
        let vMergeCount = 1;

        const sRow = this.content[startRow];
        let startVMerge = VerticalMergeType.Restart;

        for (let index = 0, cellsCount = sRow.getCellsCount(); index < cellsCount; index++) {
            const tempStartGrid = sRow.getCellInfo(index).startGridCol;
            if ( tempStartGrid === startGridCol ) {
                startVMerge = sRow.getCell(index)
                                    .getVMerge();
                break;
            }
        }

        if ( VerticalMergeType.Restart === startVMerge ) {
            return vMergeCount;
        }

        for (let index = startRow - 1; index >= 0; index--) {
            const row = this.content[index];
            const cellsCount = row.getCellsCount();
            const beforeInfo = row.getBefore();
            let curGridCol = beforeInfo.gridBefore;

            let curCell = 0;
            let bWasMerged = false;

            while ( curGridCol <= startGridCol && curCell < cellsCount ) {
                const cell = row.getCell(curCell);
                const cellGridSpan = cell.getGridSpan();
                const vMerge = cell.getVMerge();

                // 当前列，行合并，合并信息
                if ( curGridCol === startGridCol && gridSpan === cellGridSpan
                        && VerticalMergeType.Continue === vMerge ) {
                    bWasMerged = true;
                    vMergeCount++;
                } else if ( curGridCol === startGridCol && gridSpan === cellGridSpan
                            && VerticalMergeType.Continue !== vMerge ) {
                    bWasMerged = true;
                    return vMergeCount;
                } else if ( curGridCol <= startGridCol + gridSpan - 1
                            && curGridCol + cellGridSpan - 1 >= startGridCol ) {
                    break;
                }

                curGridCol += cellGridSpan;
                curCell++;
            }

            if ( false === bWasMerged ) {
                break;
            }
        }

        return vMergeCount;
    }

    public getVMergeCount(cellIndex: number, rowIndex: number): number {
        const row = this.content[rowIndex];
        if ( !row ) {
            return 1;
        }

        const cell = row.getCell(cellIndex);
        if ( !cell ) {
            return 1;
        }

        const cellInfo = row.getCellInfo(cellIndex);
        if ( !cellInfo ) {
            return 1;
        }

        return this.getVerticalMergeCount(rowIndex, cellInfo.startGridCol, cell.getGridSpan());
    }

    /**
     * 计算指定页面的行的垂直连接的单元格的数量
     * @param pageIndex
     * @param curRow
     * @param startGridCol
     * @param gridSpan
     */
    public getVerticalMergeCountOnPage(pageIndex: number, curRow: number,
                                       startGridCol: number, gridSpan: number): number {
        let vMergeCount = this.getVerticalMergeCount(curRow, startGridCol, gridSpan);

        if ( true !== this.isEmptyPage(pageIndex) && curRow + vMergeCount - 1 >= this.pages[pageIndex].lastRow ) {
            vMergeCount = this.pages[pageIndex].lastRow + 1 - curRow;

            if ( false === this.rowsInfo[curRow + vMergeCount - 1].bFirstPage
                 && pageIndex === this.rowsInfo[curRow + vMergeCount - 1].startPage ) {
                vMergeCount--;
            }
        }

        return vMergeCount;
    }

    /**
     * 获取表格的最小宽度
     */
    public getTableMinWidth(): number {
        let minWidth = 0;
        const length = this.content.length;

        for (let rowIndex = 0; rowIndex < length; rowIndex++) {
            const row = this.content[rowIndex];
            const cellsCount = row.getCellsCount();

            const cellSpacing = 0;
            let rowWidth = cellSpacing * ( cellsCount + 1 );

            // 每行的最小宽度：单元格左边距 + 右边距
            for (let cellIndex = 0; cellIndex < cellsCount; cellIndex++) {
                const cell = row.getCell(cellIndex);
                const cellMargins = cell.getMargins();

                rowWidth += cellMargins.left.width + cellMargins.right.width;
            }

            if ( minWidth < rowWidth ) {
                minWidth = rowWidth;
            }
        }

        return minWidth;
    }

    /**
     * 获取每行的最小宽度
     */
    public getMinSumGrid(): number[] {
        const sumGrid: number[] = [];

        for (let index = 0, colsCount = this.tableGridCalc.length; index < colsCount; index++) {
            sumGrid[index] = 0;
        }

        for (let index = 0, rowsCount = this.content.length; index < rowsCount; index++) {
            const row = this.content[index];
            const cellsCount = row.getCellsCount();

            const cellSpacing = 0;
            let curGridCol = 0;

            for (let cellIndex = 0; cellIndex < cellsCount; cellIndex++) {
                const cell = row.getCell(cellIndex);
                const cellMargins = cell.getMargins();
                const gridSpan = cell.getGridSpan();

                let cellMinWidth = cellMargins.left.width + cellMargins.right.width;

                if ( 0 === cellIndex || cellIndex === cellsCount - 1 ) {
                    cellMinWidth += cellSpacing * 1.5;
                } else {
                    cellMinWidth += cellSpacing;
                }

                if ( sumGrid[curGridCol + gridSpan - 1] < sumGrid[curGridCol - 1] + cellMinWidth ) {
                    sumGrid[curGridCol + gridSpan - 1] = sumGrid[curGridCol - 1] + cellMinWidth;
                }

                curGridCol += gridSpan;
            }
        }

        return sumGrid;
    }

    /**
     * 按比例减少所有网格列，以使总宽度等于指定的table宽度。
     * @param sumGrid
     * @param tableW table宽度
     */
    // public scaleTableWidth(sumGrid: number[], tableW: number): number[] {
    //     const minSumGrid = this.getMinSumGrid();

    //     const gridsToScale: boolean[] = [];
    //     for (let index = 0, length = sumGrid.length; index < length; index++) {
    //         gridsToScale[index] = true;
    //     }

    //     const tableGrid: number[] = [];
    //     tableGrid[0] = sumGrid[0];

    //     for (let index = 1, length = sumGrid.length; index < length; index++) {
    //         tableGrid[index] = sumGrid[index] - sumGrid[index - 1];
    //     }

    //     const tableGridMin: number[] = [];
    //     tableGridMin[0] = minSumGrid[0];
    //     for (let index = 1, length = minSumGrid.length; index < length; index++) {
    //         tableGridMin[index] = minSumGrid[index] - minSumGrid[index - 1];
    //     }

    //     let currentW = sumGrid[sumGrid.length - 1];
    //     let gridsToScaleCount = gridsToScale.length;
    //     while ( 0 < gridsToScaleCount && 1 < currentW ) {
    //         const koef = tableW / currentW;

    //         const tableGridTemp: number[] = [];
    //         for (let index = 0, length1 = tableGrid.length; index < length1; index++) {
    //             tableGridTemp[index] = tableGrid[index];
    //         }

    //         const length = tableGridTemp.length;
    //         for (let index = 0 ; index < length; index++) {
    //             if ( true === gridsToScale[index] ) {
    //                 tableGridTemp[index] *= koef;
    //             }
    //         }

    //         let bBreak = true;

    //         for (let index = 0; index < length; index++) {
    //             if ( true === gridsToScale[index] && 1 > tableGridTemp[index] - tableGridMin[index] ) {
    //                 bBreak = false;
    //                 gridsToScale[index] = false;
    //                 gridsToScaleCount--;

    //                 currentW -= tableGrid[index];
    //                 tableW -= tableGridMin[index];

    //                 tableGrid[index] = tableGridMin[index];
    //             }
    //         }

    //         if ( true === bBreak ) {
    //             for (let index = 0 ; index < length; index++) {
    //                 if ( true === gridsToScale[index] ) {
    //                     tableGrid[index] = tableGridTemp[index];
    //                 }
    //             }

    //             break;
    //         }
    //     }

    //     const sumGridNew: number[] = [];
    //     sumGridNew[-1] = 0;
    //     for (let index = 0, length = tableGrid.length; index < length; index++) {
    //         sumGridNew[index] = tableGrid[index] + sumGridNew[index - 1];
    //     }

    //     return sumGridNew;
    // }

    // public setTableGrid(tableGrid: number[], bInitCurPos: boolean = false): void {
    //     let bChanged = false;
    //     if (bInitCurPos && this.content.length) {
    //         this.bReadFile = bInitCurPos;
    //         this.curCell = this.content[0].getCell(0);
    //     }

    //     if ( tableGrid.length !== this.tableGrid.length ) {
    //         bChanged = true;
    //     } else {
    //         for (let index = 0, count = tableGrid.length; index < count; index++) {
    //             if ( 1 < Math.abs(tableGrid[index] - this.tableGrid[index]) ) {
    //                 bChanged = true;
    //                 break;
    //             }
    //         }
    //     }

    //     if ( false === bChanged ) {
    //         return ;
    //     }

    //     const history = this.getHistory();
    //     if ( history ) {
    //         history.addChange(new ChangeTableTableGrid(this, this.tableGrid, tableGrid));
    //     }

    //     this.tableGrid = tableGrid.slice(0);
    //     this.tableGridCalc = this.copyTableGrid();
    // }

    // public copyTableGrid(): number[] {
    //     const grid: number[] = this.tableGrid.slice(0);

    //     // for (let index = 0, count = this.tableGrid.length; index < count; index++) {
    //     //     grid[index] = this.tableGrid[index];
    //     // }

    //     return grid;
    // }

    // public getTableCellMargins(): GenericBox<TableMeasurement> {
    //     return this.property.tableCellMargin;
    // }

    /**
     * 是否击中表格边框
     * @param pointX
     * @param pointY
     * @param curPage
     */
    // public isTableBorder(pointX: number, pointY: number, curPage: number, options?: any): Table {
    //     curPage = Math.max(0, Math.min(curPage, this.pages.length - 1));

    //     if ( true === this.isEmptyPage(curPage) ) {
    //         return null;
    //     }

    //     const bFixedRowHeight = this.isFixedRowHeight();
    //     const bFixedColWidth = this.isFixedColWidth();

    //     if ( bFixedRowHeight && bFixedColWidth ) {
    //         return null;
    //     }

    //     const result = this.checkHitInBorder(pointX, pointY, curPage);
    //     if ( -1 !== result.border) {
    //         if (options) {
    //             options.bTableBorder = true;
    //         }
    //         if ( ( ( 0 === result.border || 2 === result.border ) && true === bFixedRowHeight )
    //             || ( ( 1 === result.border || 3 === result.border ) && true === bFixedColWidth ) ) {
    //             this.selection.bStart = false;
    //             this.selection.type2 = TableSelectionType.Common;
    //             return null;
    //         }

    //         if ( 3 === result.border && 0 === result.pos.cellIndex ) {
    //             this.selection.bStart = false;
    //             this.selection.type2 = TableSelectionType.Common;
    //             return null;
    //         }

    //         const row = this.content[result.pos.rowIndex];
    //         if ( 1 === result.border && row && row.getCellsCount() === result.pos.cellIndex + 1 ) {
    //             this.selection.bStart = false;
    //             this.selection.type2 = TableSelectionType.Common;
    //             return null;
    //         }

    //         return this;
    //     } else {
    //         const cell = this.content[result.pos.rowIndex].getCell(result.pos.cellIndex);
    //         return cell.isTableBorder(pointX, pointY, curPage - cell.content.getRelativeStartPage(), options);
    //     }
    // }

    /**
     * 单元格跨页：检查当前光标所在单元格是否有内容
     * @param rowIndex
     * @param cellIndex
     * @param pageIndex
     */
    public checkCurCellContent(rowIndex: number, cellIndex: number, pageIndex: number): boolean {
        const row = this.content[rowIndex];
        const cell = row.getCell(cellIndex);
        const cellContent = cell.content;
        const cellContentPageIndex = this.getRelativeStartPage() + pageIndex - cellContent.getAbsoluteStartPage();
        const page = cellContent.pages[cellContentPageIndex];

        return page ? true : false;
    }

    public selectAreaByPos(startPos: ParagraphContentPos, endPos: ParagraphContentPos, direction?: number): boolean {
        const startRowIndex = startPos.shift();
        let startCellIndex = startPos.shift();
        if (startRowIndex === undefined || startCellIndex === undefined) {
            return false;
        }
        const endRowIndex = endPos.shift();
        let endCellIndex = endPos.shift();
        if (endRowIndex === undefined || endCellIndex === undefined) {
            return false;
        }
        const selection = this.selection;
        selection.bStart = false;
        selection.bUse = true;
        selection.curRow = endRowIndex;
        let data: any[];

        if (startRowIndex === endRowIndex) {
            const row = this.content[startRowIndex];
            if (startCellIndex === endCellIndex) {
                data = null;
                selection.type = TableSelectionType.Text;
                const cell = row.content[startCellIndex];
                this.curCell = cell;
                cell.content.selectAreaByPosBase(startPos, endPos);
            } else {
                data = [];
                selection.type = TableSelectionType.TableCell;
                for (let index = startCellIndex; index <= endCellIndex; index++) {
                    row.content[index].content.setApplyToAll(true);
                    data.push({rowIndex: startRowIndex, cellIndex: index});
                }
            }
        } else {
            selection.type = TableSelectionType.TableCell;
            startCellIndex = 0;
            const contents = this.content;
            let cellCount: number;
            for (let rowIndex = startRowIndex; rowIndex <= endRowIndex; rowIndex++) {
                const row = contents[rowIndex];
                cellCount = row.getCellsCount();
                for (let index = startCellIndex; index < cellCount; index++) {
                    row.content[index].content.setApplyToAll(true);
                    data.push({rowIndex, cellIndex: index});
                }
            }
            endCellIndex = cellCount - 1;
        }
        selection.data = data;
        selection.startPos.pos = { rowIndex: startRowIndex, cellIndex: startCellIndex };
        selection.endPos.pos = { rowIndex: endRowIndex, cellIndex: endCellIndex };
        return true;
    }

    /**
     * 设置选择开始位置
     * @param pointX
     * @param pointY
     * @param curPage
     * @param mouseEvent
     */
    public setSelectionStart(pointX: number, pointY: number, curPage: number, mouseEvent: IMouseEvent): void {
        if ( this.pages.length <= curPage || 0 > curPage ) {
            curPage = 0;
        }

        const hitInfo = this.checkHitInBorder(pointX, pointY, curPage);
        const pos = hitInfo.pos;

        // if (true !== this.checkCurCellContent(pos.rowIndex, pos.cellIndex, curPage)) {
        //     this.selection.bStart = false;
        //     return;
        // }

        // 是否选中row/col/cell
        if ( true === hitInfo.bRowSelection || true === hitInfo.bColumnSelection || true === hitInfo.bCellSelection ) {
            this.removeSelection();

            this.curCell = this.content[pos.rowIndex].getCell(pos.cellIndex);
            this.curCell.setSelectionStart(pointX, pointY,
                                            curPage - this.curCell.content.getRelativeStartPage(), mouseEvent);

            this.selection.bUse = true;
            this.selection.bStart = true;
            this.selection.type = TableSelectionType.TableCell;
            this.selection.type2 = TableSelectionType.TableCells;
            this.selection.data2 = null;

            this.selection.startPos.pos = pos;
            this.selection.startPos.x = pointX;
            this.selection.startPos.y = pointY;
            this.selection.startPos.pageIndex = curPage;
            this.selection.startPos.mouseEvent = {
                clickCount: mouseEvent.clickCount,
                type: mouseEvent.type,
                bCtrlKey: mouseEvent.bCtrlKey,
            };

            const endPos = {
                rowIndex: pos.rowIndex,
                cellIndex: pos.cellIndex,
            };

            if ( true === hitInfo.bRowSelection ) {
                endPos.cellIndex = this.content[pos.rowIndex].getCellsCount() - 1;
                this.selection.type2 = TableSelectionType.TableRows;
            } else if ( true === hitInfo.bColumnSelection ) {
                const row = this.content[pos.rowIndex];
                const endRow = this.content.length - 1;
                const endCell = this.getCellIndexByStartGridCol(endRow,
                                        row.getCellInfo(pos.cellIndex).startGridCol, true);

                if ( -1 !== endCell ) {
                    endPos.rowIndex = endRow;
                    endPos.cellIndex = endCell;
                    this.selection.type2 = TableSelectionType.TableColumns;
                }
            }

            this.selection.endPos.pos = endPos;
            this.selection.endPos.x = pointX;
            this.selection.endPos.y = pointY;
            this.selection.endPos.pageIndex = curPage;
            this.selection.endPos.mouseEvent = {
                clickCount: mouseEvent.clickCount,
                type: mouseEvent.type,
                bCtrlKey: mouseEvent.bCtrlKey,
            };
        } else if ( -1 === hitInfo.border ) {
            // 选择单元格内容
            const bInnerTableBorder = ( this.isTableBorder(pointX, pointY, curPage) ? true : false );

            if ( true === bInnerTableBorder ) {
                const cell = this.content[pos.rowIndex].getCell(pos.cellIndex);
                cell.setSelectionStart(pointX, pointY,
                                curPage - this.curCell.content.getRelativeStartPage(), mouseEvent);

                this.selection.type2 = TableSelectionType.TableBorderInnerTable;
                this.selection.data2 = cell;
            } else {
                this.removeSelection();

                this.curCell = this.content[pos.rowIndex].getCell(pos.cellIndex);
                this.curCell.setSelectionStart(pointX, pointY,
                                curPage - this.curCell.content.getRelativeStartPage(), mouseEvent);

                this.selection.bUse = true;
                this.selection.bStart = true;
                this.selection.type = TableSelectionType.Text;
                this.selection.type2 = TableSelectionType.Common;
                this.selection.data2 = null;

                this.selection.startPos.pos = pos;
                this.selection.startPos.x = pointX;
                this.selection.startPos.y = pointY;
                this.selection.startPos.pageIndex = curPage;
                this.selection.startPos.mouseEvent = {
                    clickCount: mouseEvent.clickCount,
                    type: mouseEvent.type,
                    bCtrlKey: mouseEvent.bCtrlKey,
                };
            }
        } else {
            // if ( 0 === hitInfo.border || 2 === hitInfo.border ) {
            //     if ( this.isFixedRowHeight() ) {
            //         return ;
            //     }
            // } else if ( this.isFixedColWidth() ||
            //     ( 0 === pos.cellIndex || this.content[pos.rowIndex].getCellsCount() === pos.cellIndex + 1 )) {
            //     return ;
            // }

            // 选中表格边框
            this.selection.type2 = TableSelectionType.TableBorder;
            this.selection.data2 = {};
            this.selection.data2.pageNum = curPage;

            let x = pointX;
            let y = pointY;
            const row = this.content[pos.rowIndex];

            // 选中行边框：水平边框线
            if ( 0 === hitInfo.border || 2 === hitInfo.border ) {
                this.selection.data2.bColumn = false;

                let yMin = 0;
                const yMax = this.logicDocument.getPageLimits(this.getAbsoluteStartPage()).yLimit;
                const rowStart = this.pages[curPage].firstRow;
                const rowEnd = this.pages[curPage].lastRow;

                if ( 0 === hitInfo.border ) {
                    this.selection.data2.index = pos.rowIndex - rowStart;
                } else {
                    this.selection.data2.index = hitInfo.rowIndex - rowStart + 1;
                }

                if ( 0 !== this.selection.data2.index ) {
                    const tempRow = this.selection.data2.index + rowStart - 1;
                    yMin = this.rowsInfo[tempRow].y[curPage];
                }

                if ( this.selection.data2.index !== rowEnd - rowStart + 1 ) {
                    y = this.rowsInfo[this.selection.data2.index + rowStart].y[curPage];
                } else {
                    y = this.rowsInfo[this.selection.data2.index + rowStart - 1].y[curPage] +
                        this.rowsInfo[this.selection.data2.index + rowStart - 1].height[curPage];
                }

                this.selection.data2.min = yMin;
                this.selection.data2.max = yMax;

                if ( null != this.selection.data2.min ) {
                    y = Math.max(y, this.selection.data2.min);
                }

                if ( null != this.selection.data2.max ) {
                    y = Math.min(y, this.selection.data2.max);
                }

                // this.setCursorType(CursorType.RowResize);
            } else {
                this.selection.data2.bColumn = true;

                let xMin = null;
                let xMax = null;
                const cellsCount = row.getCellsCount();
                const cellSpacing = ( null == row.getCellSpacing() ? 0 : row.getCellSpacing() );
                const page = this.pages[curPage];

                if ( 3 === hitInfo.border ) {
                    this.selection.data2.index = pos.cellIndex;
                } else {
                    this.selection.data2.index = pos.cellIndex + 1;
                }

                // const cell = row.getCell(this.selection.data2.index - 1);
                if ( 0 !== this.selection.data2.index ) {
                    const margins = row.getCell(this.selection.data2.index - 1)
                                            .getMargins();
                    if ( 0 !== this.selection.data2.index - 1 && this.selection.data2.index !== cellsCount ) {
                        xMin = page.x + row.getCellInfo(this.selection.data2.index - 1).xGridStart +
                                margins.left.width + margins.right.width + cellSpacing;
                    } else {
                        xMin = page.x + row.getCellInfo(this.selection.data2.index - 1).xGridStart +
                                margins.left.width + margins.right.width + 1.5 * cellSpacing;
                    }
                }

                if ( cellsCount !== this.selection.data2.index ) {
                    const margins = row.getCell(this.selection.data2.index)
                                            .getMargins();
                    if ( cellsCount - 1 !== this.selection.data2.index ) {
                        xMax = page.x + row.getCellInfo(this.selection.data2.index).xGridEnd -
                                ( margins.left.width + margins.right.width + cellSpacing );
                    } else {
                        xMax = page.x + row.getCellInfo(this.selection.data2.index).xGridEnd -
                                ( margins.left.width + margins.right.width + 1.5 * cellSpacing );
                    }
                }

                if ( cellsCount !== this.selection.data2.index ) {
                    x = page.x + row.getCellInfo(this.selection.data2.index).xGridStart;
                } else {
                    x = page.x + row.getCellInfo(this.selection.data2.index - 1).xGridEnd;
                }

                this.selection.data2.min = xMin;
                this.selection.data2.max = xMax;

                if ( null != this.selection.data2.min ) {
                    x = Math.max(x, this.selection.data2.min);
                }

                if ( null != this.selection.data2.max ) {
                    x = Math.min(x, this.selection.data2.max);
                }

                // this.setCursorType(CursorType.ColResize);
            }

            this.selection.data2.pos = {
                rowIndex: pos.rowIndex,
                cellIndex: pos.cellIndex,
            };

            this.selection.data2.x = x;
            this.selection.data2.y = y;

            this.selection.data2.startX = pointX;
            this.selection.data2.startY = pointY;
            this.selection.data2.startColumnX = x;
            this.selection.data2.startColumnY = y;
            this.selection.data2.bStart = true;
        }
    }

    /**
     * 设置选择结束位置
     * @param pointX
     * @param pointY
     * @param curPage
     * @param mouseEvent
     */
    public setSelectionEnd(pointX: number, pointY: number, curPage: number, mouseEvent: IMouseEvent): void {
        if ( curPage >= this.pages.length || 0 > curPage ) {
            curPage = 0;
        }

        if ( TableSelectionType.TableBorder === this.selection.type2 ) {
            const data = this.selection.data2 as ITableSelectionData;
            const minWidth = 1;

            if ( this.logicDocument.isProtectedMode() || data.pageNum !== curPage ) {
                return ;
            }

            // if ( true === data.bColumn && this.isFixedColWidth() ) {
            //     return ;
            // }

            // if ( false === data.bColumn && this.isFixedRowHeight() ) {
            //     return ;
            // }

            let x = pointX;
            let y = pointY;

            if ( true !== data.bStart || minWidth < Math.abs(x - data.startX)
                || minWidth < Math.abs(y - data.startY) ) {
                data.bStart = false;
            } else {
                x = data.x;
                y = data.y;
            }

            if ( true === data.bColumn ) {
                x = this.updateForMovingBorder(x);
            } else {
                y = this.updateForMovingBorder(y);
            }

            data.x = x;
            data.y = y;

            if ( MouseEventType.MouseButtonUp === mouseEvent.type ) {
                if ( minWidth > Math.abs(x - data.startColumnX) && minWidth > Math.abs(y - data.startColumnY) ) {
                    this.selection.type2 = TableSelectionType.Common;
                    this.selection.data2 = null;

                    return;
                } else if ( this.isInHeader() ) {
                    const docPage = this.logicDocument.pages[this.getAbsolutePage()];
                    const yLimit = docPage ? ( docPage.yLimit >> 1 ) : this.yLimit;

                    if ( yLimit <= data.y ) {
                        return ;
                    }
                }

                this.logicDocument.startAction(HistoryDescriptionType.DocumentMoveTableBorder);
                // const history = this.getHistory();
                // if ( history ) {
                //     history.createNewHistoryPoint(HistoryDescriptionType.DocumentMoveTableBorder);
                // }

                // const page = this.pages[curPage];
                if ( true === data.bColumn ) {
                    // const rowIndex = data.pos.rowIndex;
                    const row = this.content[data.pos.rowIndex];

                    if ( !( 0 === data.pos.cellIndex && data.pos.cellIndex === data.index )
                        && row.getCellsCount() !== data.index ) {

                        // if ( data.index !== this.markup.cols.length ) {
                        const col = row.getCellInfo(data.index).startGridCol;
                        // } else {
                        //     col = row.getCellInfo(data.index - 1).startGridCol
                        //                      + row.getCell(data.index - 1).getGridSpan();
                        // }

                        const page = this.pages[curPage];
                        const dx = data.x - (page.x + this.tableSumGrid[col - 1]);
                        // const dx = data.x - data.startX;
                        // const rowsInfo: [] = [];
                        // this.setColumnWidth(dx + this.tableSumGrid[row.getStartGridCol(data.pos.cellIndex)]);
                        const rowsInfo: IRowInfoOfNewGrid[][] = [];
                        // const curCellCounts = row.getCellsCount();

                        if ( this.selection.bUse && TableSelectionType.TableCell === this.selection.type
                            && 0 < this.selection.data.length ) {
                            // ;
                        } else {
                            // let beforeFlag = false;
                            // let beforeSpace2 = null;
                            // let beforeSpace = null;
                            // if ( 0 === data.index && 0 !== col && data.x < page.x ) {
                            //     beforeSpace = page.x - data.x;
                            //     page.x -= beforeSpace;
                            // }

                            const xMax = page.xLimit - page.x;
                            for (let rowIndex = 0, rowsCount = this.content.length; rowIndex < rowsCount; rowIndex++) {
                                rowsInfo[rowIndex] = [];
                                const curRow = this.content[rowIndex];

                                let bFindLeft = true;
                                let bFindRight = false;
                                let tempDx = dx;
                                const cellsCount = curRow.getCellsCount();
                                // if ( cellsCount !== curCellCounts && rowIndex < data.index ) {
                                //     bFindLeft = false;
                                //     bFindRight = false;
                                // }

                                let sumCellsWidth = 0;
                                for (let curCell = 0; curCell < cellsCount; curCell++) {
                                    const cell = curRow.getCell(curCell);
                                    const cellMargins = cell.getMargins();
                                    const curGridStart = curRow.getCellInfo(curCell).startGridCol;
                                    const curGridEnd = curGridStart + cell.getGridSpan() - 1;

                                    let cellWidth = 0;

                                    if ( bFindLeft ) {
                                        if ( curGridStart === col ) {
                                            bFindLeft = false;
                                            bFindRight = false;
                                            cellWidth = this.tableSumGrid[curGridEnd] - this.tableSumGrid[col - 1] - dx;
                                        } else {
                                            if ( (curGridEnd + 1 === col)
                                            || (curGridEnd + 1 < col &&
                                        minWidth /*24*/ > (this.tableSumGrid[col - 1] - this.tableSumGrid[curGridEnd]))
                                            || (curGridEnd + 1 > col &&
                                        minWidth/*24*/ > (this.tableSumGrid[curGridEnd] - this.tableSumGrid[col - 1]))) {
                                                bFindLeft = false;
                                                cellWidth = this.tableSumGrid[col - 1]
                                                                - this.tableSumGrid[curGridStart - 1] + dx;
                                            }

                                            if ( bFindLeft ) {
                                                cellWidth = this.tableSumGrid[curGridEnd]
                                                                    - this.tableSumGrid[curGridStart - 1];
                                            }

                                            const tempWidth = Math.max(minWidth/*36*/, Math.max(cellWidth,
                                                cellMargins.left.width + cellMargins.right.width));

                                            if ( !bFindLeft ) {
                                                bFindRight = true;
                                                tempDx = tempWidth - (this.tableSumGrid[col - 1]
                                                                - this.tableSumGrid[curGridStart - 1]);
                                            }
                                        }
                                    } else if ( bFindRight ) {
                                        bFindRight = false;
                                        cellWidth = this.tableSumGrid[curGridEnd]
                                                        - this.tableSumGrid[curGridStart - 1] - tempDx;
                                    } else {
                                        cellWidth = this.tableSumGrid[curGridEnd] - this.tableSumGrid[curGridStart - 1];
                                    }

                                    cellWidth = Math.max(minWidth/*36*/, Math.max(cellWidth,
                                                                cellMargins.left.width + cellMargins.right.width));

                                    // console.log(cellWidth)
                                    sumCellsWidth += cellWidth;
                                    if ( cellsCount - 1 === curCell && minWidth <= Math.abs(sumCellsWidth - xMax) ) {
                                        cellWidth += Math.abs(sumCellsWidth - xMax);
                                    }
                                    rowsInfo[rowIndex].push({width: cellWidth, type: 0, gridSpan: 1});
                                }
                            }
                        }

                        this.createNewGrid(rowsInfo);
                        this.parent.recalculate();
                        this.parent.updateCursorXY();
                        // GlobalEvent.setEvent(this.logicDocument.id, 'updateCursorType');

                        // if ( true === this.selection.bUse && TableSelectionType.TableCell === this.selection.type ) {
                        //     ;
                        // }
                    } else {
                        // ;
                    }
                } else {
                    const rowIndex = this.pages[data.pageNum].firstRow + data.index;

                    if ( 0 !== rowIndex ) {
                        if ( !(0 < data.pageNum && 0 === data.index) ) {
                            const row = this.content[rowIndex - 1];
                            const oldHeight = this.rowsInfo[rowIndex - 1].height[curPage];
                            const newHeight = oldHeight + data.y - data.startY;

                            if ( 0 < newHeight ) {
                                row.setHeight(newHeight, TableRowLineRule.AtLeast);
                                this.parent.recalculate();
                                this.parent.updateCursorXY();
                                // GlobalEvent.setEvent(this.logicDocument.id, 'updateCursorType');
                            }
                        }
                    } else {
                        // ;
                    }
                }

                this.logicDocument.endAction();
                this.selection.type2 = TableSelectionType.Common;
                this.selection.data2 = null;
            }

            return ;
        } else if ( TableSelectionType.TableBorderInnerTable === this.selection.type2 ) {
            return ;
        }

        const tempPos = this.getCellByXY(pointX, pointY, curPage);
        const pos = {
            rowIndex: tempPos.rowIndex,
            cellIndex: tempPos.cellIndex,
        };

        // if (true !== this.checkCurCellContent(pos.rowIndex, pos.cellIndex, curPage)) {
        //     this.selection.bStart = false;
        //     return;
        // }

        // 按照选择方式重新设置当前选中的区域
        if ( TableSelectionType.TableRows === this.selection.type2 ) {
            pos.cellIndex = this.content[pos.rowIndex].getCellsCount() - 1;
        } else if ( TableSelectionType.TableColumns === this.selection.type2 ) {
            const row = this.content[pos.rowIndex];
            const endCell = this.getCellIndexByStartGridCol(this.content.length - 1,
                                    row.getCellInfo(pos.cellIndex).startGridCol, true);

            if ( -1 !== endCell ) {
                pos.rowIndex = this.content.length - 1;
                pos.cellIndex = endCell;
            }
        }

        this.content[pos.rowIndex].getCell(pos.cellIndex)
                                                .setCurPosXY(pointX, pointY);
        this.selection.data = null;
        this.selection.endPos.pos = pos;
        this.selection.endPos.x = pointX;
        this.selection.endPos.y = pointY;
        this.selection.endPos.pageIndex = curPage;
        this.selection.endPos.mouseEvent = mouseEvent;
        this.selection.curRow = pos.rowIndex;

        const startPos = this.selection.startPos;
        const endPos = this.selection.endPos;
        if ( TableSelectionType.Common === this.selection.type2 && this.parent.isSelectedSingleElement()
            && startPos.pos.rowIndex === endPos.pos.rowIndex && startPos.pos.cellIndex === endPos.pos.cellIndex) {
            this.curCell.setSelectionStart(startPos.x, startPos.y,
                    startPos.pageIndex - this.curCell.content.getRelativeStartPage(), startPos.mouseEvent);

            this.selection.type = TableSelectionType.Text;

            this.curCell.setSelectionEnd(pointX, pointY,
                    curPage - this.curCell.content.getRelativeStartPage(), mouseEvent);

            if ( MouseEventType.MouseButtonUp === mouseEvent.type ) {
                this.selection.bStart = false;
            }

            if ( false === this.curCell.content.selection.bUse) {
                this.selection.bStart = false;
                this.selection.bUse = false;
                this.moveCursorToXY(curPage, pointX, pointY);
            }
        } else {
            if ( MouseEventType.MouseButtonUp === mouseEvent.type ) {
                this.selection.bStart = false;
                this.curCell = this.content[pos.rowIndex].getCell(pos.cellIndex);
            }

            this.selection.type = TableSelectionType.TableCell;
            this.updateSelectedCellsArray(TableSelectionType.TableRows === this.selection.type2 ? true : false);
        }
    }

    public resetMovingTableNewBorder(): void {
        if ( TableSelectionType.TableBorder === this.selection.type2 ) {
            this.selection.type2 = TableSelectionType.Common;
            this.selection.data2 = null;
            this.selection.bStart = false;
            this.selection.bUse = false;
        }
    }

    /**
     * 在当前光标位置开始选择
     */
    public startSelectionByCurPos(): void {
        this.selection.bUse = true;
        this.selection.startPos.pos = {rowIndex: this.curCell.row.index, cellIndex: this.curCell.index};
        this.selection.endPos.pos = {rowIndex: this.curCell.row.index, cellIndex: this.curCell.index};

        this.updateSelectedCellsArray();

        if ( this.logicDocument ) {
            const curPos = this.logicDocument.getCurPos();
            this.selection.startPos.x = curPos.x;
            this.selection.startPos.y = curPos.y;
        }
        this.selection.type = TableSelectionType.Text;
        this.selection.curRow = this.curCell.row.index;

        this.curCell.content.startSelectionByCurPos();
    }

    public getCurrentPageByPos(pos: ParagraphContentPos): number {
        const content = this.content[pos.shift()].content[pos.shift()].content;
        return content.getCurrentPageByPos(pos);
    }

    public removeSelection(): void {
        if ( false === this.selection.bUse ) {
            return ;
        }

        if ( 0 >= this.content.length ) {
            this.curCell = null;
        } else {
            if ( TableSelectionType.Text === this.selection.type ) {
                this.curCell = this.content[this.selection.startPos.pos.rowIndex]
                                                    .getCell(this.selection.startPos.pos.cellIndex);
                this.curCell.content.removeSelection();
            } else if ( 0 < this.content.length && this.content[0].getCellsCount() > 0 ) {
                this.curCell = this.content[0].getCell(0);
                this.curCell.content.removeSelection();
            }
        }

        this.selection.bStart = false;
        this.selection.bUse = false;
        this.selection.data = null;
        this.selection.type = TableSelectionType.Common;

        this.selection.startPos.pos = { rowIndex: 0, cellIndex: 0 };
        this.selection.endPos.pos = { rowIndex: 0, cellIndex: 0 };
    }

    /**
     * 选择区域是否为空
     * @param bCheckHidden
     */
    public isSelectionEmpty(bCheckHidden?: boolean): boolean {
        // key-wasm by tinyzhi
        // if ( true === this.selection.bUse ) {
        //     if ( TableSelectionType.TableCell === this.selection.type ) {
        //         return false;
        //     } else {
        //         return this.curCell.content.isSelectionEmpty(bCheckHidden);
        //     }
        // }

        // return true;
        const nResult = WasmInstance.instance._Table_isSelectionEmpty(Number(this.selection.bUse),
                                            this.selection.type, TableSelectionType.TableCell);
        if (nResult === 1) { return false; }
        if (nResult === 2) { return this.curCell.content.isSelectionEmpty(bCheckHidden); }
        if (nResult === 3) { return true; }
         // end by tinyzhi
    }

    /**
     * 设置是否选择
     * @param bUse
     */
    public setSelectionUse(bUse: boolean): void {
        if ( true === bUse ) {
            this.selection.bUse = true;
        } else {
            this.removeSelection();
        }
    }

    /**
     * 设置选择的起始位置
     * @param bSelectStart
     * @param bEnd
     */
    public setSelectionBeginEnd(bSelectStart: boolean, bEnd: boolean): void {
        let pos;

        if ( true === bEnd ) {
            pos = { rowIndex: 0, cellIndex: 0 };
        } else {
            const rowIndex = this.content.length - 1;
            const cellCount = this.content[rowIndex].getCellsCount();

            pos = { rowIndex, cellIndex: cellCount - 1 };
        }

        if ( true === bSelectStart ) {
            this.selection.startPos.pos = pos;
        } else {
            this.selection.endPos.pos = pos;
        }

        this.updateSelectedCellsArray();
    }

    /**
     * 表格全选
     * @param direction 选择方向
     */
    // public selectAll(direction: number): void {
    //     if ( !this.curCell ) {
    //         this.curCell = this.content[0].getCell(0);
    //     }
    //     this.selection.bUse = true;
    //     this.selection.bStart = false;
    //     this.selection.type = TableSelectionType.TableCell;
    //     this.selection.type2 = TableSelectionType.Common;
    //     this.selection.data = null;

    //     if ( 0 > direction ) {
    //         this.selection.endPos.pos = { rowIndex: 0, cellIndex: 0 };
    //         this.selection.endPos.pageIndex = 0;

    //         this.selection.startPos.pos = { rowIndex: this.content.length - 1,
    //                                         cellIndex: this.content[this.content.length - 1].getCellsCount() - 1 };
    //         this.selection.startPos.pageIndex = this.pages.length - 1;
    //     } else {
    //         this.selection.startPos.pos = { rowIndex: 0, cellIndex: 0 };
    //         this.selection.startPos.pageIndex = 0;

    //         this.selection.endPos.pos = { rowIndex: this.content.length - 1,
    //                                         cellIndex: this.content[this.content.length - 1].getCellsCount() - 1 };
    //         this.selection.endPos.pageIndex = this.pages.length - 1;
    //     }

    //     this.updateSelectedCellsArray();
    // }

    // public getStructsNameByCell(sCellName: string): string {
    //     const cell = this.getCellByName(sCellName);
    //     if (!cell) {
    //         return ResultType.StringEmpty;
    //     }

    //     const rowIndex = cell.row.index;
    //     const cellIndex = cell.index;
    //     const content = this.content[rowIndex]?.content[cellIndex]?.content;
    //     if (!content) {
    //         return ResultType.StringEmpty;
    //     }

    //     const posIndex = this.parent.getTopElementPos(this);
    //     const startPos = new ParagraphContentPos();
    //     startPos.clear();
    //     this.getParentPos(startPos);
    //     startPos.splice(0, 0, [posIndex]);
    //     startPos.add(rowIndex);
    //     startPos.add(cellIndex);
    //     const endPos = startPos.copy();
    //     content.getStartPos(startPos);
    //     content.getEndPos(true, endPos);
    //     const newControlManager = this.logicDocument.getNewControlManager();
    //     const names = newControlManager.getNewControlNamesByPos(startPos, endPos);
    //     if (names && names.length > 0) {
    //         return names.join(',');
    //     }
    //     return ResultType.StringEmpty;
    // }

    // public getStartPos(pos: ParagraphContentPos): void {
    //     pos.add(this.index);
    //     pos.add(0);
    //     pos.add(0);
    //     this.content[0].content[0].content.getStartPos(pos);
    // }

    // public getEndPos(bParaEnd: boolean, pos: ParagraphContentPos): void {
    //     pos.add(this.index);
    //     const rows = this.content;
    //     const rowIndex = rows.length - 1;
    //     const cells = rows[rowIndex].content;
    //     const cellIndex = cells.length - 1;
    //     pos.add(rowIndex);
    //     pos.add(cellIndex);
    //     cells[cellIndex].content.getEndPos(bParaEnd, pos);
    // }

    // /**
    //  * 是否选中整个表格
    //  * @param bEnd
    //  */
    // public isSelectedAll(bEnd?: boolean): boolean {
    //     if ( true !== this.selection.bUse || null == this.selection.data ) {
    //         return false;
    //     }

    //     if ( true === this.bApplyToAll ) {
    //         return true;
    //     }

    //     let arrayPos = 0;
    //     const selectionArray = this.selection.data;
    //     for (let curRow = 0, rowsCount = this.content.length; curRow < rowsCount; curRow++) {
    //         const row = this.content[curRow];

    //         for (let curCell = 0, cellsCount = row.getCellsCount(); curCell < cellsCount; curCell++, arrayPos++) {
    //             if ( selectionArray.length <= arrayPos ) {
    //                 return false;
    //             }

    //             const pos = selectionArray[arrayPos];
    //             if ( pos.rowIndex !== curRow || pos.cellIndex !== curCell ) {
    //                 const cell = row.getCell(curCell);
    //                 if ( cell && VerticalMergeType.Continue === cell.getVMerge() ) {
    //                     const startCell = this.getStartMergedCell2(curCell, curRow);
    //                     if ( startCell && startCell.content.isSelectionUse() ) {
    //                         arrayPos--;
    //                         continue;
    //                     }
    //                 }

    //                 return false;
    //             }
    //         }
    //     }

    //     return true;
    // }

    // /**
    //  * 获取选择区域
    //  */
    // public getSelectionBounds(bStart: boolean, bMultis: boolean): IDrawSelectionBounds {
    //     let result = {
    //         direction: -1,
    //         lines: [],
    //         cells: [],
    //     };

    //     if ( true === this.bApplyToAll || ( true === this.selection.bUse
    //                                         && TableSelectionType.TableCell === this.selection.type
    //                                         && 0 < this.selection.data.length ) ) {

    //         const selectedCellsArray = this.getSelectionArray();
    //         // console.log(selectedCellsArray)
    //         // console.log(this.selection)

    //         if ( selectedCellsArray && 0 !== selectedCellsArray.length ) {
    //             const bInHeaderFooter = this.isHeaderFooter(false);
    //             for (const pos of selectedCellsArray) {
    //                 const row = this.content[pos.rowIndex];
    //                 const cell = row.getCell(pos.cellIndex);

    //                 if ( cell ) {
    //                     const bound = (!bInHeaderFooter ? cell.getSelectionBounds2(null) :
    //                                     cell.getSelectionBounds());
    //                     if ( 0 < result.cells.length ) {
    //                         result.cells = result.cells.concat(bound);
    //                     } else {
    //                         result.cells = bound;
    //                     }
    //                 }
    //             }
    //         }

    //     } else {
    //         result = this.curCell.content.getSelectionBounds(bStart, bMultis);
    //     }

    //     return result;
    // }

    // /**
    //  * 获取当前光标处段落
    //  */
    // public getCurrentParagraph(): DocumentContentElementBase {
    //     const selectionArray = this.getSelectionArray();
    //     if ( null != selectionArray && 0 < selectionArray.length ) {
    //         const curRow = selectionArray[0].rowIndex;
    //         const curCell = selectionArray[0].cellIndex;

    //         return this.content[curRow].getCell(curCell).content
    //                                                         .getCurrentParagraph();
    //     }

    //     return (this.curCell ? this.curCell.content.getCurrentParagraph() : null);
    // }

    // public getSelectionDirection(): number {
    //     if ( true !== this.selection.bUse ) {
    //         return 0;
    //     }

    //     if ( true === this.bApplyToAll ) {
    //         return 1;
    //     }

    //     const selectionArray = this.getSelectionArray();
    //     if ( null != selectionArray && 0 < selectionArray.length ) {
    //         const startPos = this.selection.startPos.pos;
    //         const endPos = this.selection.endPos.pos;

    //         if ( startPos.rowIndex < endPos.rowIndex ) {
    //             return 1;
    //         } else if ( startPos.rowIndex > endPos.rowIndex ) {
    //             return -1;
    //         } else {
    //             if ( startPos.cellIndex < endPos.cellIndex ) {
    //                 return 1;
    //             } else if ( startPos.cellIndex > endPos.cellIndex ) {
    //                 return -1;
    //             }
    //         }

    //         // return 0;
    //     }
    //     return (this.curCell ? this.curCell.content.getSelectionDirection() : 0);
    // }

    // /**
    //  * 获取当前选中的所有单元格，或当前光标所在单元格
    //  */
    // public getSelectionArray(): ITableCellPos[] {
    //     let selectionArray = [];

    //     if ( true === this.bApplyToAll ) {
    //         // selectionArray = [];

    //         for (let curRow = 0, count = this.content.length; curRow < count; curRow++) {
    //             const row = this.content[curRow];
    //             const cellsCount = row.getCellsCount();

    //             for (let curCell = 0; curCell < cellsCount; curCell++) {
    //                 const cell = row.getCell(curCell);
    //                 const vMerge = cell.getVMerge();

    //                 if ( VerticalMergeType.Continue === vMerge ) {
    //                     continue;
    //                 }

    //                 selectionArray.push({rowIndex: curRow, cellIndex: curCell});
    //             }
    //         }
    //     } else if ( true === this.selection.bUse && TableSelectionType.TableCell === this.selection.type ) {
    //         selectionArray = this.selection.data;
    //     } else {
    //         if ( this.curCell ) {
    //             selectionArray = [{rowIndex: this.curCell.row.index, cellIndex: this.curCell.index}];
    //         // } else {
    //         //     selectionArray = [];
    //         }
    //     }

    //     return selectionArray;
    // }

    // /**
    //  * 是否有单元格被选中
    //  */
    // public isCellSelection(): boolean {
    //     // key-wasm by tinyzhi
    //     // if ( true === this.bApplyToAll ||
    //     //     ( true === this.selection.bUse &&
    //     //         TableSelectionType.TableCell === this.selection.type && 0 < this.selection.data.length) ) {
    //     //     return true;
    //     // }

    //     // return false;
    //     const nResult = WasmInstance.instance._Table_isCellSelection(Number(this.bApplyToAll),
    //                     Number(this.selection.bUse), TableSelectionType.TableCell, this.selection.type,
    //                     this.selection.data && this.selection.data.length);
    //     return Boolean(nResult);
    //      // end by tinyzhi
    // }

    // public getDocumentElementState(): DocumentElementState[][] {
    //     const curPos: ITableCellPos = {
    //         cellIndex: this.curCell.index,
    //         rowIndex: this.curCell.row.index,
    //     };

    //     const selection = new TableSelection();

    //     selection.bStart = this.selection.bStart;
    //     selection.bUse = this.selection.bUse;
    //     selection.startPos = this.selection.startPos.copy();
    //     selection.endPos = this.selection.endPos.copy();
    //     selection.data = [];
    //     selection.type = this.selection.type;
    //     selection.type2 = this.selection.type;
    //     selection.curRow = this.selection.curRow;

    //     if ( true === this.selection.bUse && TableSelectionType.TableCell === this.selection.type ) {
    //        for (let index = 0; index < this.selection.data.length; index++) {
    //            selection.data[index] = {
    //                 cellIndex: this.selection.data[index].cellIndex,
    //                 rowIndex: this.selection.data[index].rowIndex,
    //            };
    //        }
    //     }

    //     const state = this.curCell.content.getDocumentElementState();
    //     const tableState = new TableState(curPos, selection);
    //     state.push([tableState]);

    //     return state;
    // }

    // public setDocumentElementState( state: TableState[], stateIndex: number ): void {
    //     if ( 0 >= state.length) {
    //         return ;
    //     }

    //     stateIndex = state.length - 1;
    //     const tableState = state[stateIndex][0];

    //     this.selection.bStart = tableState.selection.bStart;
    //     this.selection.bUse = tableState.selection.bUse;
    //     this.selection.startPos = tableState.selection.startPos.copy();
    //     this.selection.endPos = tableState.selection.endPos.copy();
    //     this.selection.data = [];
    //     this.selection.type = tableState.selection.type;
    //     this.selection.type2 = tableState.selection.type2;
    //     this.selection.curRow = tableState.selection.curRow;

    //     if ( true === this.selection.bUse && TableSelectionType.TableCell === this.selection.type ) {
    //         for (let index = 0; index < tableState.selection.data.length; index++) {
    //             this.selection.data[index] = {
    //                 cellIndex: tableState.selection.data[index].cellIndex,
    //                 rowIndex: tableState.selection.data[index].rowIndex,
    //             };
    //         }
    //      }

    //     this.curCell = this.content[tableState.curCell.rowIndex].getCell(tableState.curCell.cellIndex);
    //     this.curCell.content.setDocumentState(state, stateIndex - 1);
    // }

    // public addNewParagraph(): void {
    //     this.curCell.content.addNewParagraph();
    // }

    public addSoftNewParagraph(): number {
        return this.curCell?.content.addSoftNewParagraph();
    }

    // public add(paraItem: ParaElementBase, bRecal?: boolean): number {
    //     return this.addToParagraph(paraItem, bRecal);
    // }

    // public addToParagraph(paraItem: ParaElementBase, bRecal?: boolean): number {
    //     let result: number = ResultType.Success;
    //     if (ParaElementType.ParaTextPr === paraItem.type && ( true === this.bApplyToAll ||
    //             ( true === this.selection.bUse && TableSelectionType.TableCell === this.selection.type
    //                 && 0 < this.selection.data.length ) )) {
    //         const selectedCellsArray = this.getSelectionArray();
    //         let res: number = ResultType.UnEdited;
    //         for (let index = 0, length = selectedCellsArray.length; index < length; index++) {
    //             const pos = selectedCellsArray[index];
    //             const row = this.content[pos.rowIndex];
    //             const cell = row.getCell(pos.cellIndex);
    //             const cellContent = cell.content;

    //             cellContent.setApplyToAll(true);
    //             res = cellContent.addToParagraph(paraItem, bRecal) && res;
    //             cellContent.setApplyToAll(false);
    //         }
    //         result = res;
    //     } else {
    //         return this.curCell.content.addToParagraph(paraItem, bRecal);
    //     }

    //     return result;
    // }

    // public addInlineImage(width: number, height: number, src: string,
    //                       name: string, type: EquationType, svgElem?: any): string {
    //     this.selection.bUse = true;
    //     this.selection.type = TableSelectionType.Text;
    //     return this.curCell.content.addInlineImage(width, height, src, name, type, svgElem);
    // }

    // public getAllImagesName(): string[] {
    //     const images: string[] = [];
    //     const drawings = this.logicDocument
    //                         ?.getDrawingObjects()
    //                         ?.getGraphicObject();
    //     if (drawings) {
    //         for (const [ name, draw] of drawings) {
    //             if (draw.tableId === this.getId()) {
    //                 images.push(draw.getDrawingName());
    //             }
    //         }
    //     }
    //     return images;
    // }

    // public addNewControl(newControl: NewControl, parentNewControl: NewControl,
    //                      property: INewControlProperty, sText?: string): number {
    //     if ( true === this.bApplyToAll ) {
    //         return ResultType.Failure;
    //     }

    //     let result: number = ResultType.Failure;
    //     if ( true === this.selection.bUse ) {
    //         if (TableSelectionType.TableCell === this.selection.type && 0 < this.selection.data.length) {
    //             return ResultType.Failure;
    //         } else if (TableSelectionType.Text === this.selection.type) {
    //             result = this.curCell.content.addNewControl(property, sText);
    //             if ( result !==  ResultType.Success) {
    //                 return result;
    //             }
    //         }
    //     } else {
    //         return this.curCell.content.addNewControl(property, sText);
    //     }

    //     if ( true === this.selection.bUse ) {
    //         this.removeSelection();
    //     }

    //     return result;
    // }

    /**
     * 获取当前光标前的结构化元素的左边框位置
     */
    // public getNewControlLeftBorderPos(): number {
    //     if ( true === this.bApplyToAll ) {
    //         return -1;
    //     }

    //     if ( true === this.selection.bUse ) {
    //         if (TableSelectionType.TableCell === this.selection.type && 0 < this.selection.data.length) {
    //             return -1;
    //         } else if (TableSelectionType.Text === this.selection.type) {
    //             return 0;
    //         }
    //     } else {
    //         return 0;
    //         // return this.curCell.content.addNewControl(property);
    //     }

    //     return -1;
    // }

    // /**
    //  * 获取当前光标前的结构化元素的右边框位置
    //  */
    // public getNewControlRightBorderPos(): number {
    //     return 0;
    // }

    public isCursorInNewControl(): boolean {
        if ( null != this.curCell ) {
            return this.curCell.content.isCursorInNewControl();
        }

        return false;
    }

    public getCursorInNewControl(): NewControl {
        if ( null != this.curCell ) {
            return this.curCell.content.getCursorInNewControl();
        }

        return null;
    }

    public isFocusInNewControl(pointX: number, pointY: number, pageIndex: number): boolean {
        if ( undefined === pointX || undefined === pointY || undefined === pageIndex ) {
            return false;
        }

        const cellPos = this.getCellByXY(pointX, pointY, pageIndex);
        const cell = this.content[cellPos.rowIndex].getCell(cellPos.cellIndex);

        return cell.content.isFocusInNewControl(pointX, pointY, pageIndex - cell.content.getRelativeStartPage());
    }

    public getFocusInNewControl(pointX: number, pointY: number, pageIndex: number): NewControl {
        const cellPos = this.getCellByXY(pointX, pointY, pageIndex);
        const cell = this.content[cellPos.rowIndex].getCell(cellPos.cellIndex);

        return cell.content.getFocusInNewControl(pointX, pointY, pageIndex - cell.content.getRelativeStartPage());
    }

    public getDocumentSectionType(): DocumentSectionType {
        return this.parent.getDocumentSectionType();
    }

    /**
     * 获取当前选中表格单元格的位置
     * @param bSelection
     * @param bStart
     */
    public getCurContentPosInDoc( bSelection?: boolean, bStart?: boolean, bDeep?: boolean ): ParagraphContentPos {
        const contentPos = new ParagraphContentPos();

        if ( true === bSelection ) {
            if (bDeep === true) {
                return this.getDeepPosInTable(bStart);
            }

            const topParent = this.getTopDocument();

            switch (topParent.getDocumentSectionType()) {
                case DocumentSectionType.Header:
                    contentPos.add(0);
                    break;
                case DocumentSectionType.Document:
                    contentPos.add(1);
                    break;
                case DocumentSectionType.Footer:
                    contentPos.add(2);
                    break;
            }

            if (this.parent instanceof DocumentContent) {
                this.parent.getParentIndexs(contentPos);
            }

            if ( true === this.bApplyToAll ) {
                if ( true === bStart ) {
                    contentPos.add(this.index);
                    contentPos.add(0);
                    contentPos.add(-1);
                } else {
                    contentPos.add(this.index);
                    contentPos.add(this.rowsInfo.length);
                    contentPos.add(this.getRow(this.rowsInfo.length - 1).content.length);
                }
            } else {
                const selectionArray = this.getSelectionArray();
                if ( null != selectionArray && 0 < selectionArray.length ) {
                    const direction = this.getSelectionDirection();
                    const startPos = -1 === direction ? this.selection.endPos.pos : this.selection.startPos.pos;
                    const endPos = -1 === direction ? this.selection.startPos.pos : this.selection.endPos.pos;

                    if ( true === bStart ) {
                        contentPos.add(this.index);
                        contentPos.add(startPos.rowIndex);
                        contentPos.add(-1); // contentPos.add(startPos.cellIndex);
                    } else {
                        contentPos.add(this.index);
                        contentPos.add(endPos.rowIndex);
                        // contentPos.add(endPos.cellIndex + 1);
                        contentPos.add(this.getRow(endPos.rowIndex).content.length);
                    }
                }
            }

        } else {
            const startPos = this.selection.startPos.pos;
            const rowIndex = startPos.rowIndex;
            const cellIndex = startPos.cellIndex;
            const cellContent = this.content[rowIndex].content[cellIndex].content;
            cellContent.setApplyToAll(false);
            const pos = cellContent.getCurContentPosInDoc(false, bStart);
            // console.log(this)
            return pos;
        }

        return contentPos;
    }

    public getDeepPosInTable(bStart: boolean): ParagraphContentPos {
        let rowIndex: number;
        let cellIndex: number;
        let bApplyToAll: boolean = true;
        const datas = this.selection.data;
        if ( datas && 1 <= datas.length ) {
            let index = 0;
            if (bStart === false) {
                index = datas.length - 1;
            }
            const data = datas[index];
            rowIndex = data.rowIndex;
            cellIndex = data.cellIndex;
        } else {
            const direction = this.getSelectionDirection();
            if (bStart) {
                const startPos = -1 === direction ? this.selection.endPos.pos : this.selection.startPos.pos;
                rowIndex = startPos.rowIndex;
                cellIndex = startPos.cellIndex;
            } else {
                const endPos = -1 === direction ? this.selection.startPos.pos : this.selection.endPos.pos;
                rowIndex = endPos.rowIndex;
                cellIndex = endPos.cellIndex;
            }
            bApplyToAll = false;
        }
        const cellContent = this.content[rowIndex].content[cellIndex].content;
        cellContent.setApplyToAll(bApplyToAll);
        const pos = cellContent.getCurContentPosInDoc(this.selection.bUse, bStart);
        cellContent.setApplyToAll(false);
        // const contentPos = new ParagraphContentPos();
        // contentPos.add(this.index);
        // contentPos.add(rowIndex);
        // contentPos.add(cellIndex);
        // const depth = pos.getDepth() + 1;
        // for (let index = 0; index < depth; index++) {
        //     contentPos.add(pos.get(index));
        // }

        return pos;
    }

    public getNewControlBounds( newControl: NewControl, mouseEvent?: MouseEventHandler,
                                pageIndex?: number ): IDrawNewControlBounds {
        if ( null != newControl ) {
            if ( null == mouseEvent ) {
                return this.curCell.content.getNewControlBounds(newControl);
            } else {
                const cellPos = this.getCellByXY(mouseEvent.pointX, mouseEvent.pointY, pageIndex);
                const cell = this.content[cellPos.rowIndex].getCell(cellPos.cellIndex);

                return cell.content.getNewControlBounds(newControl);
            }
        }
        return null;
    }

    public setSelectionStart2(): void {
        const datas = this.selection.data;
        if ( datas && datas.length > 0 ) {
            return;
        }

        const startPos = this.selection.startPos.pos;
        const doc = this.content[startPos.rowIndex].content[startPos.cellIndex].content;
        doc.content[doc.selection.startPos].setSelectionStart2();
    }

    public setSelectionStart3(): void {
        const datas = this.selection.data;
        // 多列选中
        if ( datas && datas.length > 0 ) {
            return;
        }

        const endPos = this.selection.endPos.pos;
        const doc = this.content[endPos.rowIndex].content[endPos.cellIndex].content;
        doc.content[doc.selection.endPos].setSelectionStart3();
    }

    // public getCurPageLastNode(): ParaElementBase {
    //     const pages = this.pages;
    //     if (pages.length > 0) {

    //     }

    //     return;
    // }

    public getNewControlParaBounds( newControlName: string, startPos: number,
                                    endPos: number ): IDrawSelectionsLine[] {
        if ( null != newControlName ) {
            if ( null == startPos && null == endPos ) {
                let lines: IDrawSelectionsLine[] = [];

                for (let curRow = 0, count = this.content.length; curRow < count; curRow++) {
                    const row = this.content[curRow];
                    const cellsCount = row.getCellsCount();

                    for (let curCell = 0; curCell < cellsCount; curCell++) {
                        const cell = row.getCell(curCell);

                        if ( cell ) {
                            const content = cell.content;
                            content.setApplyToAll(true);

                            const bounds = content.getNewControlParaBounds(newControlName);
                            if ( null != bounds && null != bounds.lines && 0 < bounds.lines.length ) {
                                lines = lines.concat(bounds.lines);
                            }

                            content.setApplyToAll(false);
                        }
                    }
                }

                return lines;
            }
        }
        return null;
    }

    // public getNewControlText( startPortionIndex: number, endPortionIndex: number ): string {
    //     let text = '';

    //     for (let curRow = 0, length = this.content.length; curRow < length; curRow++) {
    //         const row = this.content[curRow];
    //         const cellsCount = row.getCellsCount();

    //         for (let cellIndex = 0; cellIndex < cellsCount; cellIndex++) {
    //             const cell = row.getCell(cellIndex);
    //             const vMerge = cell.getVMerge();
    //             const vMergeCount = this.getVerticalMergeCount(curRow,
    //                 cell.metrics.startGridCol, cell.getGridSpan());

    //             if ( VerticalMergeType.Continue === vMerge || 1 < vMergeCount ) {
    //                 continue;
    //             }

    //             text += cell.content.getNewControlText();
    //         }
    //     }

    //     return text;
    // }

    // public isSelectedOrCursorInNewControlTitle(): boolean {
    //     // let result = true;
    //     // const selectedCellsArray = this.getSelectionArray();

    //     if ( this.bApplyToAll || ( true === this.selection.bUse
    //                 && TableSelectionType.TableCell === this.selection.type
    //                 && 0 < this.selection.data.length ) ) {
    //         return false;
    //     } else {
    //         return this.curCell.content.isSelectedOrCursorInNewControlTitle();
    //     }

    //     // return result;
    // }

    // public getTextLength(bSkipSomeCh: boolean = false): number {
    //     let length = 0;
    //     // let bHeader = false;

    //     for (let rowIndex = 0, rowsCount = this.content.length; rowIndex < rowsCount; rowIndex++) {
    //         const row = this.content[rowIndex];
    //         // if ( true === row.isTableHeader() && false === bHeader ) {
    //         //     bHeader = true;
    //         //     continue;
    //         // }

    //         for (let cellIndex = 0, cellsCount = row.getCellsCount(); cellIndex < cellsCount; cellIndex++) {
    //             const cell = row.getCell(cellIndex);
    //             const vMerge = cell.getVMerge();

    //             if ( cell && VerticalMergeType.Restart === vMerge ) {
    //                 length += cell.content.getTextLength(bSkipSomeCh);
    //             }
    //         }
    //     }

    //     return length;
    // }

    // public remove(direction: number, bOnlyText: boolean, bOnlySelection: boolean,
    //               bAddText: boolean, bSelectParaEnd?: boolean): boolean {
    //     if ( true === this.bApplyToAll || ( true === this.selection.bUse
    //                                         && TableSelectionType.TableCell === this.selection.type
    //                                         && 0 < this.selection.data.length ) ) {
    //         const selectedCellsArray = this.getSelectionArray();
    //         const count = selectedCellsArray.length;

    //         if ( true === bAddText && 0 < count ) {
    //             const pos = selectedCellsArray[0];
    //             const cell = this.content[pos.rowIndex].getCell(pos.cellIndex);

    //             cell.content.selectAll();
    //             cell.content.remove(direction, bOnlyText, bOnlySelection, bAddText, bSelectParaEnd);

    //             this.curCell = cell;
    //             this.selection.bUse = false;
    //             this.selection.bStart = false;
    //             this.selection.startPos.pos = { rowIndex: pos.rowIndex, cellIndex: pos.cellIndex };
    //             this.selection.endPos.pos = { rowIndex: pos.rowIndex, cellIndex: pos.cellIndex };
    //         } else {
    //             if ( this.isReadOnlyProtect() ) {
    //                 // alert('表格设置了只读保护，无法删除内容');
    //                 // message.error('表格设置了只读保护，无法删除内容');
    //                 gEvent.setEvent(this.logicDocument.id, gEventName.MessageEvent, this, MessageType.TableProtected);
    //                 return false;
    //             }

    //             for (let index = 0; index < count; index++) {
    //                 const pos1 = selectedCellsArray[index];
    //                 const row = this.content[pos1.rowIndex];
    //                 const curCell = row ? row.getCell(pos1.cellIndex) : null;

    //                 if ( curCell && curCell.isCellProtected() ) {
    //                     // alert('单元格设置了保护，无法删除内容');
    //                     // message.error('单元格设置了保护，无法删除内容');
    //                     // tslint:disable-next-line: max-line-length
    //                     gEvent.setEvent(this.logicDocument.id, gEventName.MessageEvent, this, MessageType.CellProtected);
    //                     return false;
    //                 }
    //             }

    //             for (let index = 0; index < count; index++) {
    //                 const pos1 = selectedCellsArray[index];
    //                 const row = this.content[pos1.rowIndex];
    //                 const curCell = row ? row.getCell(pos1.cellIndex) : null;

    //                 if ( curCell ) {
    //                     curCell.content.setApplyToAll(true);
    //                     curCell.content.remove(direction, bOnlyText, bOnlySelection, bAddText, bSelectParaEnd);
    //                     curCell.content.setApplyToAll(false);
    //                 }
    //             }

    //             const pos = selectedCellsArray[0];
    //             const cell = this.content[pos.rowIndex].getCell(pos.cellIndex);

    //             this.curCell = cell;
    //             this.selection.bUse = false;
    //             this.selection.bStart = false;
    //             this.selection.startPos.pos = { rowIndex: pos.rowIndex, cellIndex: pos.cellIndex };
    //             this.selection.endPos.pos = { rowIndex: pos.rowIndex, cellIndex: pos.cellIndex };
    //         }
    //     } else {
    //         this.curCell.content.remove(direction, bOnlyText, bOnlySelection, bAddText, bSelectParaEnd);

    //         if ( false === this.curCell.content.isSelectionUse() ) {
    //             const cell = this.curCell;

    //             this.selection.bUse = false;
    //             this.selection.bStart = false;
    //             this.selection.startPos.pos = { rowIndex: cell.row.index, cellIndex: cell.index };
    //             this.selection.endPos.pos = { rowIndex: cell.row.index, cellIndex: cell.index };
    //         }
    //     }

    //     return true;
    // }

    // public canMergeTableCells(selection: TableSelection = this.selection): boolean {
    //     // key-wasm by tinyzhi
    //     // if ( true !== this.selection.bUse || 0 >= this.selection.data.length
    //     //     || TableSelectionType.TableCell !== this.selection.type ) {
    //     //     return false;
    //     // }
    //     const nResult = WasmInstance.instance._Table_canMergeTableCells(Number(selection.bUse),
    //                         selection.data && selection.data.length,
    //                         TableSelectionType.TableCell, selection.type);
    //     if (nResult === 0) { return false; }
    //      // end by tinyzhi

    //     // 合并选中的单元格之前，需要检查每行单元格的开始和结束列是否相同
    //     const tempMerge = this.checkMerge(selection);
    //     return tempMerge.bCanMerge;
    // }

    /**
     * 合并单元格
     * @param cellNames 指定单元格名称
     */
    public mergeTableCellByCellNames(cellNames: string[]): boolean {
        const distinctCellNames = new Set(cellNames);
        // 单元格是否连续（需要呈矩形）
        let maxRow = -1,
            maxCell = -1,
            minRow = Number.MAX_SAFE_INTEGER - 2,
            minCell = Number.MAX_SAFE_INTEGER - 2;
        const selectedCells = [];
        for (const name of distinctCellNames) {
            const cell = this.getTableCellByName(name);
            if (!cell) {
                continue;
            }
            const rowIndex = cell.row.getIndex(),
                  cellIndex = cell.getIndex();
            (minRow > rowIndex) && (minRow = rowIndex);
            (minCell > cellIndex) && (minCell = cellIndex);
            (maxRow < rowIndex) && (maxRow = rowIndex);
            (maxCell < cellIndex) && (maxCell = cellIndex);
            selectedCells.push({rowIndex, cellIndex});
        }
        if (selectedCells.length !== distinctCellNames.size) {
            return false;
        }
        const selection = new TableSelection();
        selection.bUse = true;
        selection.type = TableSelectionType.TableCell;
        selection.type2 = TableSelectionType.Common;
        selection.startPos.pos = { rowIndex: minRow, cellIndex: minCell };
        selection.startPos.pageIndex = 0;
        selection.endPos.pos = { rowIndex: maxRow, cellIndex: maxCell };
        selection.endPos.pageIndex = this.pages.length - 1;

        // push cells to selection
        selection.data = selectedCells;

        if (this.canMergeTableCells(selection)) {
            return this.mergeTableCells(undefined, selection);
        }
        return false;
    }

    /**
     * merge table cells
     */
    public mergeTableCells(bClearMerge?: boolean, selection: TableSelection = this.selection): boolean {
        if ( true !== selection.bUse || 0 >= selection.data.length
            || TableSelectionType.TableCell !== selection.type ) {
            return false;
        }

        // 合并选中的单元格之前，需要检查每行单元格的开始和结束列是否相同
        const tempMerge = this.checkMerge(selection);
        const bCanMerge = tempMerge.bCanMerge;
        const startGrid = tempMerge.startGrid;
        const endGrid = tempMerge.endGrid;
        const rowsInfo = tempMerge.rowsInfo;

        if ( true !== bCanMerge ) {
            return false;
        }

        const topLeftPos = selection.data[0];
        const topLeftCell = this.content[topLeftPos.rowIndex].getCell(topLeftPos.cellIndex);

        for (let index = 0, count = selection.data.length; index < count; index++) {
            const pos = selection.data[index];
            const row = this.content[pos.rowIndex];
            const cell = row.getCell(pos.cellIndex);

            // 将此单元格的内容添加到左上角单元格的内容中
            if ( 0 !== index ) {
                topLeftCell.contentMerge(cell.content);
                cell.content.clearContent();
            }
        }

        // 重新设置合并后单元格的宽度
        if ( true !== bClearMerge ) {
            let sumWidth = 0;
            for (let index = startGrid; index <= endGrid; index++) {
                sumWidth += this.tableGridCalc[index];
            }

            topLeftCell.setCellWidth(new TableMeasurement(TableWidthType.Mm, sumWidth));
        }

        // 删除额外的单元格，并添加垂直合并的单元格
        // for (let rowIndex = 0;  rowIndex < rowsInfo.length; rowIndex++) {
        // 必须从合并的当前行开始，不能直接从0，或者非合并行开始
        // tslint:disable-next-line: forin
        for (const rowIndex in rowsInfo ) {
        // for (const rowIndex of rowsInfo) {
            const row = this.content[rowIndex];

            // row.getCellsCount()会发生变化，即：合并删除，不能count =  row.getCellsCount() 
            for (let cellIndex = 0; cellIndex < row.getCellsCount(); cellIndex++) {
                const cellStartGrid = row.getCellInfo(cellIndex).startGridCol;

                if ( startGrid === cellStartGrid ) {
                    if ( row.index !== topLeftPos.rowIndex ) {
                        const cell = row.getCell(cellIndex);
                        cell.setGridSpan(endGrid - startGrid + 1);
                        cell.setVMerge(VerticalMergeType.Continue);
                    } else {
                        topLeftCell.setGridSpan(endGrid - startGrid + 1);
                    }
                } else if ( startGrid < cellStartGrid && cellStartGrid <= endGrid ) {
                    row.removeCell(cellIndex, false);
                    cellIndex--;
                } else if ( cellStartGrid > endGrid ) {
                    break;
                }
            }
        }

        // 删除多余的行
        this.checkTableRows(false);

        selection.bUse = true;
        selection.startPos.pos = topLeftPos;
        selection.endPos.pos = topLeftPos;
        selection.type = TableSelectionType.TableCell;
        selection.data = [topLeftPos];

        this.curCell = topLeftCell;
        this.curCell.content.selectAll();
        // clear table cell slash
        this.curCell.setTableCellSlash(-1);

        this.recalcInfo.bTableBorders = true;
        // this.recalculatePage();
        this.parent.recalculate();
        this.parent.setDirty();

        const newControlManager = this.parent.getNewControlManager();
        if ( newControlManager ) {
            newControlManager.updateNewControlPos(/*this.parent.getStartRemoveSelectionPos(),
                        this.parent.getEndRemoveSelectionPos()*/);
        }
        return true;
    }

    /**
     * 拆分单元格
     * @param rows 拆分行数
     * @param cols 拆分列数
     */
    public splitTableCells(rows: number, cols: number): boolean {
        // key-wasm by tinyzhi
        // if ( ! ( false === this.selection.bUse
        //     || ( true === this.selection.bUse && ( TableSelectionType.Text === this.selection.type
        //                                             || (TableSelectionType.TableCell === this.selection.type
        //                                                     && 1 === this.selection.data.length)))) ) {
        //     return false;
        // }
        const nResult = WasmInstance.instance._Table_splitTableCells(Number(this.selection.bUse),
                                TableSelectionType.Text, this.selection.type,
                                TableSelectionType.TableCell, this.selection.data && this.selection.data.length);
        if ( nResult === 0 ) { return false; }
        // end by tinyzhi
        let cellPos = null;
        let cell = null;
        // key-wasm by tinyzhi
        //  if ( false === this.selection.bUse ||
       //     ( true === this.selection.bUse && TableSelectionType.Text === this.selection.type ) ) {
        const nResult2 = WasmInstance.instance._Table_CellsJudge(Number(this.selection.bUse),
                                         TableSelectionType.Text, this.selection.type);
        if (Boolean(nResult2)) {
        // end by tinyzhi
            cell = this.curCell;
            cellPos = { rowIndex: cell.row.index, cellIndex: cell.index };
        } else {
            cellPos = this.selection.data[0];
            cell = this.content[cellPos.rowIndex].getCell(cellPos.cellIndex);
        }

        const row = this.content[cellPos.rowIndex];
        const startGrid = row.getCellInfo(cellPos.cellIndex).startGridCol;
        const gridSpan = cell.getGridSpan();
        const vMergeCount = this.getVerticalMergeCount(cellPos.rowIndex, startGrid, gridSpan);

        // 合并过的单元格，在拆分时，不能行数超过合并时单元格的行数，且拆分的行数必须是合并时行数的整数倍
        if ( 1 < vMergeCount ) {
            const warning = '拆分的行数必须是能被' + vMergeCount + '整除，且不大于' + vMergeCount;
            if ( vMergeCount < rows ) {
                GlobalEvent.setEvent(this.parent.id, gEventName.TableEvent, MessageType.SplitTableCells, warning);
                return false;
            } else if ( 0 !== vMergeCount % rows ) {
                GlobalEvent.setEvent(this.parent.id, gEventName.TableEvent, MessageType.SplitTableCells, warning);
                return false;
            }
        }

        if ( gMaxRowNum < this.content.length + rows - 1 ) {
            const warning = '表格总行数不允许超过' + gMaxRowNum;
            GlobalEvent.setEvent(this.parent.id, gEventName.TableEvent, MessageType.AddTableRow, warning);
            return false;
        }

        if ( gMaxColNum < row.getCellsCount() + cols - 1 ) {
            const warning = '表格总列数不允许超过' + gMaxColNum;
            GlobalEvent.setEvent(this.parent.id, gEventName.TableEvent, MessageType.AddTableColumn, warning);
            return false;
        }

        if ( 1 < cols ) {
            const beforeSum = this.tableSumGrid[startGrid - 1];
            const widthSum = this.tableSumGrid[startGrid + gridSpan - 1];
            const spanWidth = widthSum - beforeSum;
            const gridWidth = spanWidth / cols;
            const cellMargins = cell.getMargins();
            const minWidth = cellMargins.left.width + cellMargins.right.width;

            if ( gridWidth < minWidth ) {
                const maxCols = Math.floor(spanWidth / minWidth);
                if ( maxCols < cols ) {
                    const warning = '拆分的列数超出了范围，请重新输入： 1---' + maxCols;
                    GlobalEvent.setEvent(this.parent.id, gEventName.TableEvent, MessageType.SplitTableCells, warning);
                    return false;
                }
            }
        }

        const splitCells: TableCell[] = [];
        const splitCellsPos = [];
        const splitRows: TableRow[] = [];

        // 获取当前拆分的单元格及其位置信息
        if ( 1 >= rows ) {
            for (let index = 0; index < vMergeCount; index++) {
                const curRow = this.content[cellPos.rowIndex + index];

                splitRows[index] = curRow;
                splitCells[index] = null;
                splitCellsPos[index] = null;

                for (let curCell = 0, cellsCount = curRow.getCellsCount(); curCell < cellsCount; curCell++) {
                    const startGridCol = curRow.getCellInfo(curCell).startGridCol;

                    if ( startGridCol === startGrid ) {
                        splitCells[index] = curRow.getCell(curCell);
                        splitCellsPos[index] = { rowIndex: cellPos.rowIndex + index, cellIndex: curCell };
                    }
                }
            }
        } else {
            if ( 1 < vMergeCount ) {
                const newVMergeCount = vMergeCount / rows;

                for (let index = 0; index < vMergeCount; index++) {
                    const curRow = this.content[cellPos.rowIndex + index];

                    splitRows[index] = curRow;
                    splitCells[index] = null;
                    splitCellsPos[index] = null;

                    for (let curCell = 0, cellsCount = curRow.getCellsCount(); curCell < cellsCount; curCell++) {
                        const startGridCol = curRow.getCellInfo(curCell).startGridCol;

                        if ( startGridCol === startGrid ) {
                            const cell1 = curRow.getCell(curCell);
                            splitCells[index] = cell1;
                            splitCellsPos[index] = { rowIndex: cellPos.rowIndex + index, cellIndex: curCell };

                            if ( 0 === index % newVMergeCount ) {
                                cell1.setVMerge(VerticalMergeType.Restart);
                            } else {
                                cell1.setVMerge(VerticalMergeType.Continue);
                            }
                        }
                    }
                }
            } else {
                splitCells[0] = cell;
                splitCellsPos[0] = cellPos;
                splitRows[0] = row;

                for (let index = 1, cellsCount = row.getCellsCount(); index < rows; index++) {
                    const newRow = this.addRow(cellPos.rowIndex + index, cellsCount, false);
                    newRow.copyProperty(row.property);

                    splitRows[index] = newRow;
                    splitCells[index] = null;
                    splitCellsPos[index] = null;

                    for (let curCell = 0; curCell < cellsCount; curCell++) {
                        const newCell = newRow.getCell(curCell);
                        const oldCell = row.getCell(curCell);

                        newCell.copyProperty(oldCell.property);

                        if ( cellPos.cellIndex === curCell ) {
                            splitCells[index] = newCell;
                            splitCellsPos[index] = { rowIndex: cellPos.rowIndex + index, cellIndex: curCell };
                        } else {
                            newCell.setVMerge(VerticalMergeType.Continue);
                        }
                    }
                }
            }
        }

        if ( 1 < cols ) {
            // 获取当前单元格开始和结束位置，计算出分割后每个单元格的宽度
            const beforeSum = this.tableSumGrid[startGrid - 1];
            const widthSum = this.tableSumGrid[startGrid + gridSpan - 1];
            const spanWidth = widthSum - beforeSum;
            const gridWidth = spanWidth / cols;

            // 此数组包含有关在第i列之后添加了多少新列的信息
            const gridInfo: number[] = [];
            for (let index = 0; index < this.tableGridCalc.length; index++) {
                gridInfo[index] = 0;
            }

            // 该数组包含有关新单元格中有多少间隙的信息
            const newGridInfo: number[] = [];
            for (let index = 0; index < cols; index++) {
                newGridInfo[index] = 1;
            }

            const startGridInfo: number[] = [];
            for (let index = 0; index < this.tableGridCalc.length; index++) {
                startGridInfo[index] = this.tableGridCalc[index];
            }

            let newColIndex = 0;
            let curWidth = beforeSum + gridWidth;
            const gridCount = startGrid + gridSpan;

            for (let gridIndex = startGrid; gridIndex < gridCount; gridIndex++) {
                let bNewCol = true;

                if ( 1 > Math.abs(curWidth - this.tableSumGrid[gridIndex]) ) {
                    newColIndex++;
                    curWidth += gridWidth;
                    bNewCol = false;
                    continue;
                }

                while ( curWidth < this.tableSumGrid[gridIndex] ) {
                    if ( 0 === gridInfo[gridIndex] ) {
                        startGridInfo[gridIndex] = curWidth - this.tableSumGrid[gridIndex - 1];
                    }
                    gridInfo[gridIndex] += 1;

                    newColIndex++;
                    curWidth += gridWidth;

                    if ( 1 > Math.abs(curWidth - this.tableSumGrid[gridIndex]) ) {
                        newColIndex++;
                        curWidth += gridWidth;
                        bNewCol = false;
                        break;
                    }
                }

                if ( true === bNewCol ) {
                    newGridInfo[newColIndex] += 1;
                }
            }

            // 当前单元格合并后再拆分，且合并的数量大于拆分的数量
            if ( cols < gridSpan && -1 !== newGridInfo.findIndex((value) => isNaN(value)) ) {
                // const diff = gridCount - cols;
                let count = 0;
                for (let index = 0; index < gridSpan; index++) {
                    if ( isNaN(newGridInfo[index]) ) {
                        newGridInfo[index] = 1;
                    }

                    count += newGridInfo[index];
                }

                if ( count < gridInfo.length ) {
                    newGridInfo[cellPos.cellIndex + cols - 1] += (gridInfo.length - count);
                }
            }

            // 添加此行（Cols  -  1）单元格，使用与原始单元格相同的设置。 GridSpan值取自newGridInfo数组。
            for (let index = 0; index < splitRows.length; index++) {
                if ( null != splitCells[index] && null != splitCellsPos[index]) {
                    const curRow = splitRows[index];
                    const curCell = splitCells[index];
                    const cellPos1 = splitCellsPos[index];

                    curCell.setGridSpan(newGridInfo[0]);
                    curCell.setCellWidth(new TableMeasurement(TableWidthType.Mm, gridWidth));

                    for (let index2 = 1; index2 < cols; index2++) {
                        const newCell = curRow.addCell(cellPos1.cellIndex + index2, curRow, null, false);
                        newCell.copyProperty(curCell.property);
                        newCell.setGridSpan(newGridInfo[index2]);
                        newCell.setCellWidth(new TableMeasurement(TableWidthType.Mm, gridWidth));
                    }
                }
            }

            const oldTableGridLen = this.tableGridCalc.length;
            const tableGrid = this.copyTableGrid();

            // 向TableGrid添加新列，从最后开始，以免重新计算数字
            for (let index = oldTableGridLen - 1; index >= 0; index--) {
                let sum = this.tableGridCalc[index];

                if ( 0 < gridInfo[index] ) {
                    tableGrid[index] = startGridInfo[index];
                    sum -= ( startGridInfo[index] - gridWidth );

                    for (let newIndex = 0; newIndex < gridInfo[index]; newIndex++) {
                        sum -= gridWidth;

                        if ( newIndex !== gridInfo[index] - 1 ) {
                            tableGrid.splice(index + newIndex + 1, 0, gridWidth);
                        } else {
                            tableGrid.splice(index + newIndex + 1, 0, sum);
                        }
                    }
                }
            }

            this.setTableGrid(tableGrid);

            // 遍历所有行, 并根据gridInfo数组的值更改GridSpan单元格
            for (let curRow = 0, rowsCount = this.content.length; curRow < rowsCount; curRow++) {
                if ( splitCellsPos[0].rowIndex <= curRow
                     && curRow <= splitCellsPos[splitCellsPos.length - 1].rowIndex ) {
                    continue;
                }

                const tempRow = this.content[curRow];

                const gridBefore = tempRow.getBefore().gridBefore;
                const gridAfter = tempRow.getAfter().gridAfter;

                if ( 0 < gridBefore ) {
                    let sumGridSpan = gridBefore;
                    for (let curGrid = 0; curGrid < gridBefore; curGrid++) {
                        sumGridSpan += gridInfo[curGrid];
                    }

                    tempRow.setBefore(sumGridSpan);
                }

                let lastGrid = 0;

                for (let curCell = 0, cellsCount = tempRow.getCellsCount(); curCell < cellsCount; curCell++) {
                    const tempCell = tempRow.getCell(curCell);
                    const curGridSpan = tempCell.getGridSpan();
                    const startGridCol = tempRow.getCellInfo(curCell).startGridCol;

                    let sumGridSpan = curGridSpan;

                    lastGrid = startGridCol + curGridSpan;

                    for (let curGrid = startGridCol; curGrid < lastGrid; curGrid++) {
                        sumGridSpan += gridInfo[curGrid];
                    }

                    tempCell.setGridSpan(sumGridSpan);
                }

                if ( 0 < gridAfter ) {
                    let sumGridSpan = gridAfter;
                    for (let curGrid = lastGrid; curGrid < oldTableGridLen; curGrid++) {
                        sumGridSpan += gridInfo[curGrid];
                    }

                    tempRow.setAfter(sumGridSpan);
                }
            }
        }
        // clear table cell slash
        this.curCell.setTableCellSlash(-1);
        this.recalcInfo.bTableBorders = true;
        this.reIndex();
        this.tableRecal.recalculateGrid();
        this.parent.recalculate();
        this.parent.setDirty();
        // this.recalculatePage();

        return true;
    }

    /**
     * 检查是否需要从表中删除不必要的行。 这可以在合并单元格之后或在更改表格的网格之后发生
     * @param bSaveHeight true : 表格发生了改变；false: 表格没有任何变化
     */
    // public checkTableRows(bSaveHeight: boolean): boolean {
    //     const deleteRows = [];
    //     const calcRowsHeight = [];
    //     const calcRowsHeight2 = [];

    //     // 首先，找出需要删除的行。
    //     for (let curRow = 0, rowsCount = this.content.length; curRow < rowsCount; curRow++) {
    //         const row = this.content[curRow];

    //         let bVMergeRestart = false;
    //         let bVMergeContinue = false;
    //         let bNeedDeleteRow = true;
    //         let bNeedCalcHeight = false;

    //         if ( 0 < row.getBefore().gridBefore || 0 < row.getAfter().gridAfter ) {
    //             bNeedCalcHeight = true;
    //         }

    //         for (let curCell = 0, cellsCount = row.getCellsCount(); curCell < cellsCount; curCell++) {
    //             const cell = row.getCell(curCell);
    //             const vMerge = cell.getVMerge();

    //             if ( VerticalMergeType.Continue !== vMerge ) {
    //                 const vMergeCount = this.getVerticalMergeCount(curRow,
    //                                                 row.getCellInfo(curCell).startGridCol, cell.getGridSpan());
    //                 if ( 1 < vMergeCount ) {
    //                     bVMergeRestart = true;
    //                 }

    //                 bNeedDeleteRow = false;

    //                 if ( true === bNeedCalcHeight ) {
    //                     bNeedCalcHeight = ( 1 === vMergeCount ? false : bNeedCalcHeight);
    //                 }
    //             } else {
    //                 bVMergeContinue = true;
    //             }
    //         }

    //         if ( true === bVMergeContinue && true === bVMergeRestart ) {
    //             calcRowsHeight2.push(curRow);
    //         } else if ( true === bNeedCalcHeight ) {
    //             calcRowsHeight.push(curRow);
    //         }

    //         if ( true === bNeedDeleteRow ) {
    //             deleteRows.push(curRow);
    //         }
    //     }

    //     // for (let index = 0, count = calcRowsHeight2.length; index < count; index++) {
    //     //     const rowIndex = calcRowsHeight2[index];
    //     //     const row = this.content[rowIndex];
    //     //     let minHeight = -1;

    //     //     for (let curCell = 0, cellsCount = row.getCellsCount(); curCell < cellsCount; curCell++) {
    //     //         const cell = row.getCell(curCell);
    //     //         const vMerge = cell.getVMerge();

    //     //         if ( VerticalMergeType.Restart === vMerge ) {
    //     //             const curCellHeight = cell.content.getEmptyHeight();
    //     //             if ( curCellHeight < minHeight || -1 === minHeight ) {
    //     //                 minHeight = curCellHeight;
    //     //             }
    //     //         }
    //     //     }

    //     //     const oldHeight = row.getHeight();

    //     //     if ( !oldHeight || TableRowLineRule.Auto === oldHeight.hRule || minHeight > oldHeight.value ) {
    //     //         row.setHeight(minHeight, TableRowLineRule.AtLeast);
    //     //     }
    //     // }

    //     if ( 0 >= deleteRows.length ) {
    //         return false;
    //     }

    //     if ( true === bSaveHeight ) {
    //         for (let index = 0, count = calcRowsHeight.length; index < count; index++) {
    //             const rowIndex = calcRowsHeight[index];
    //             let heightValue = null;
    //             const rowsInfo = this.rowsInfo[rowIndex];

    //             rowsInfo.height.forEach((item) => {
    //                 if ( null === heightValue ) {
    //                     heightValue = item;
    //                 } else {
    //                     heightValue = null;
    //                     return;
    //                 }
    //             });
    //             // for (let curPage = 0; curPage < rowsInfo.height.length; curPage++) {
    //             // }
    //             this.content[rowIndex].setHeight(heightValue, TableRowLineRule.AtLeast);
    //         }

    //         for (let index = 0, count = deleteRows.length; index < count;) {
    //             let curRowSpan = 1;
    //             const startRow = deleteRows[index];

    //             while ( count > index + curRowSpan &&
    //                     deleteRows[index] + curRowSpan === deleteRows[index + curRowSpan] ) {
    //                 curRowSpan++;
    //             }

    //             if ( this.rowsInfo[startRow - 1].startPage === this.rowsInfo[startRow - 1 + curRowSpan].startPage ) {
    //                 const startPage = this.rowsInfo[startRow - 1 + curRowSpan].startPage;
    //                 const sumHeight = this.rowsInfo[startRow - 1 + curRowSpan].height[startPage]
    //                                     + this.rowsInfo[startRow - 1 + curRowSpan].y[startPage];
    //                 this.content[startRow - 1].setHeight(sumHeight, TableRowLineRule.AtLeast);
    //             }
    //             index += curRowSpan;
    //         }
    //     }

    //     // 从最后一行开始删除，以免重新计算行号
    //     for (let index = deleteRows.length - 1; index >= 0; index--) {
    //         const deleteRowIndex = deleteRows[index];
    //         this.removeRow(deleteRowIndex);
    //     }

    //     return true;
    // }

    /**
     * 只选中表格
     */
    // public removeTableRow(rowIndex?: number): boolean {
    //     const deleteRows = [];

    //     if ( null == rowIndex ) {
    //         // 获取需要删除的行
    //         if ( true === this.selection.bUse && TableSelectionType.TableCell === this.selection.type ) {
    //             let num = 0;
    //             let prevRow = -1;
    //             for (let index = 0, count = this.selection.data.length; index < count; index++) {
    //                 const pos = this.selection.data[index];

    //                 if ( pos.rowIndex !== prevRow ) {
    //                     deleteRows[num++] = pos.rowIndex;
    //                 }

    //                 prevRow = pos.rowIndex;
    //             }
    //         } else if ( this.curCell ) {
    //             deleteRows[0] = this.curCell.row.index;
    //         }
    //     } else {
    //         deleteRows[0] = rowIndex;
    //     }

    //     if ( 0 >= deleteRows.length || rowIndex >= this.content.length ) {
    //         return ;
    //     } else if ( this.property.isDeleteProtect() && deleteRows.length === this.content.length ) {
    //         gEvent.setEvent(this.logicDocument.id, gEventName.MessageEvent, this, MessageType.TableDeleted);
    //         return ;
    //     } else if ( true !== this.property.isTableCanDeleteRow() ) {
    //         gEvent.setEvent(this.logicDocument.id, gEventName.MessageEvent, this, MessageType.RowDeleted);
    //         return ;
    //     }

    //     const firstRow = deleteRows[0];
    //     rowIndex = deleteRows[deleteRows.length - 1] + 1;

    //     if ( rowIndex < this.content.length ) {
    //         const curRow = this.content[rowIndex];
    //         const cellsCount = curRow.getCellsCount();

    //         for (let curCell = 0; curCell < cellsCount; curCell++) {
    //             const cell = curRow.getCell(curCell);
    //             const vMerge = cell.getVMerge();

    //             if ( VerticalMergeType.Continue !== vMerge ) {
    //                 continue;
    //             }

    //             const vMergeCount = this.getVerticalMergeCount2(rowIndex,
    //                                     curRow.getCellInfo(curCell).startGridCol, cell.getGridSpan());
    //             if ( rowIndex - ( vMergeCount - 1 ) >= firstRow ) {
    //                 cell.setVMerge(VerticalMergeType.Restart);
    //             }
    //         }
    //     }

    //     this.removeSelection();

    //     this.removeNewControl(deleteRows);

    //     for (let index = deleteRows.length - 1; index >= 0; index--) {
    //         this.removeRow(deleteRows[index]);
    //     }

    //     this.parent.setDirty();

    //     if ( 0 >= this.content.length ) {
    //         return false;
    //     }

    //     // key-wasm by tinyzhi
    //      // rowIndex = Math.min(firstRow, this.content.length - 1);
    //     rowIndex = WasmInstance.instance._MMI(firstRow, this.content.length - 1);
    //      // end by tinyzhi
    //     // const row = this.content[rowIndex];

    //     // this.curCell = row.getCell(0);
    //     this.checkCurCell();
    //     this.curCell.content.moveCursorToStartPos();

    //     return true;
    // }

    // public parentHidden(): boolean {
    //     const parent: any = this.parent;
    //     if (parent.isRegionContent()) {
    //         return parent.parentHidden();
    //     }
    //     return false;
    // }

    /**
     * 同时选择多个元素：包含表格，段落
     */
    // public removeTableRow2(): boolean {
    //     // if ( false === this.selection.bUse || TableSelectionType.Text === this.selection.type ) {
    //     //     return true;
    //     // }
    //     const nResult = WasmInstance.instance._Table_CellsJudge(Number(this.selection.bUse),
    //                             TableSelectionType.Text, this.selection.type);
    //     if (Boolean(nResult)) {
    //         return true;
    //     }
    //      // end by tinyzhi

    //     const deleteRows = [];
    //     for (let index = 0, count = this.content.length; index < count; index++) {
    //         deleteRows[index] = 0;
    //     }

    //     let length = 0;
    //     for (let index = 0, count = this.selection.data.length; index < count; index++) {
    //         const pos = this.selection.data[index];

    //         if ( 0 === deleteRows[pos.rowIndex] ) {
    //             deleteRows[pos.rowIndex] = 1;
    //             length++;
    //         }
    //     }

    //     if ( this.content.length === length ) {
    //         return false;
    //     }

    //     for (let index = this.content.length - 1; index >= 0; index--) {
    //         if ( 1 === deleteRows[index] ) {
    //             this.removeRow(index);
    //         }
    //     }

    //     if ( 0 >= this.content.length ) {
    //         return false;
    //     }

    //     if ( this.curCell.row.index >= this.content.length ) {
    //         this.curCell = this.content[this.content.length - 1].getCell(0);
    //     }

    //     this.removeSelection();

    //     return true;
    // }

    // public removeTableRow3(): void {
    //     let bEmpty = true;
    //     const length = this.content.length;
    //     for (let index = 0; index < length; index++) {
    //         const row = this.content[index];
    //         const count = row.getCellsCount();
    //         for (let cellIndex = 0; cellIndex < count; cellIndex++) {
    //             const cell = row.getCell(cellIndex);
    //             if ( cell ) {
    //                 bEmpty = cell.content.isEmpty();

    //                 if ( !bEmpty ) {
    //                     break;
    //                 }
    //             }
    //         }

    //         if ( !bEmpty ) {
    //             break;
    //         }
    //     }

    //     if ( ReviewType.Common === this.reviewType ) {
    //         this.setReviewType(ReviewType.Remove);
    //     } else {
    //         ;
    //     }

    //     if ( bEmpty ) {
    //         for (let index = length - 1; index >= 0; index--) {
    //             this.removeRow(index);
    //         }
    //     } else {
    //         this.remove(1, true, true, false, false);
    //     }
    // }

    /**
     * 删除列
     */
    // public removeTableColumn(): boolean {
    //     let selectedCellsArray = [];

    //     // 找到所选单元格的右边框和左边框
    //     if ( true === this.selection.bUse && TableSelectionType.TableCell === this.selection.type ) {
    //         selectedCellsArray = this.selection.data;
    //     } else {
    //         selectedCellsArray[0] = { rowIndex: this.curCell.row.index, cellIndex: this.curCell.index };
    //     }

    //     if ( 0 >= selectedCellsArray.length ) {
    //         return ;
    //     }

    //     let startGrid = -1;
    //     let endGrid = -1;

    //     // for (let index = 0, cellsCount = selectedCellsArray.length; index < cellsCount; index++) {
    //     for (const pos of selectedCellsArray) {
    //         // const pos = selectedCellsArray[index];
    //         const tempRow = this.content[pos.rowIndex];
    //         const cell = tempRow.getCell(pos.cellIndex);

    //         const curGridStart = tempRow.getCellInfo(pos.cellIndex).startGridCol;
    //         const curGridEnd = curGridStart + cell.getGridSpan() - 1;

    //         if ( -1 === startGrid || ( -1 !== startGrid && startGrid > curGridStart) ) {
    //             startGrid = curGridStart;
    //         }

    //         if ( -1 === endGrid || ( -1 !== endGrid && endGrid < curGridEnd) ) {
    //             endGrid = curGridStart;
    //         }
    //     }

    //     let tableCellsCount = 0;
    //     let deleteCellsCount = 0;
    //     const deleteInfo = [];
    //     const rowsInfo: IRowInfoOfNewGrid[][] = [];

    //     // 遍历所有行，并查看是否有任何单元格与[startGrid，endGrid]的交集，然后删除此单元格。
    //     for (let rowIndex = 0, count = this.content.length; rowIndex < count; rowIndex++) {
    //         deleteInfo[rowIndex] = [];
    //         rowsInfo[rowIndex] = [];

    //         const tempRow = this.content[rowIndex];
    //         const beforeInfo = tempRow.getBefore();
    //         if ( 0 < beforeInfo.gridBefore ) {
    //             rowsInfo[rowIndex].push({ width: this.tableSumGrid[beforeInfo.gridBefore - 1], type: -1, gridSpan: 1} );
    //         }

    //         for (let curCell2 = 0, length = tempRow.getCellsCount(); curCell2 < length; curCell2++) {
    //             tableCellsCount++;
    //             const cell = tempRow.getCell(curCell2);
    //             const curGridStart = tempRow.getCellInfo(curCell2).startGridCol;
    //             const curGridEnd = curGridStart + cell.getGridSpan() - 1;

    //             if ( curGridStart <= endGrid && curGridEnd >= startGrid ) {
    //                 deleteCellsCount++;
    //                 deleteInfo[rowIndex].push(curCell2);
    //             } else {
    //                 const width = this.tableSumGrid[curGridEnd] - this.tableSumGrid[curGridStart - 1];
    //                 rowsInfo[rowIndex].push({ width, type: 0, gridSpan: 1} );
    //             }
    //         }
    //     }

    //     if ( this.property.isDeleteProtect() && tableCellsCount === deleteCellsCount ) {
    //         gEvent.setEvent(this.logicDocument.id, gEventName.MessageEvent, this, MessageType.TableDeleted);
    //         return ;
    //     }

    //     // 删除所有单元格
    //     for (let rowIndex = 0; rowIndex < this.content.length; rowIndex++) {
    //         const row1 = this.content[rowIndex];

    //         for (let index = deleteInfo[rowIndex].length - 1; index >= 0; index--) {
    //             const cell = deleteInfo[rowIndex][index];
    //             row1.removeCell(cell);
    //         }
    //     }

    //     // 删除列时，可能会删除整行
    //     for (let rowIndex = this.content.length - 1; rowIndex >= 0; rowIndex--) {
    //         let bRemove = true;

    //         // 如果rowsInfo中没有单元格条目（即，类型等于0），则完全删除该行
    //         for (let index = 0, count = rowsInfo[rowIndex].length; index < count; index++) {
    //             if ( 0 === rowsInfo[rowIndex][index].type ) {
    //                 bRemove = false;
    //                 break;
    //             }
    //         }

    //         if ( true === bRemove ) {
    //             this.removeRow(rowIndex);
    //             rowsInfo.splice(rowIndex, 1);
    //         }
    //     }

    //     if ( 0 >= this.content.length ) {
    //         return false;
    //     }

    //     // 删除列时，必须记住有关垂直合并单元格的信息，并且在新网格中，仅在最初合并时才合并单元格。
    //   // 现在，如果已从任何单元格合并单元格，则在删除列后，它可以与完全不同的单元格合并。
    //     this.createNewGrid(rowsInfo);

    //     // 遍历所有单元格并查看它们的垂直关联，是否已被破坏
    //     this.correctVerticelMerge();

    //     // 仍有可能完全由垂直连接的cell组成的row。
    //     for (let rowIndex = this.content.length - 1; rowIndex >= 0; rowIndex--) {
    //         let bRemove = true;
    //         const row1 = this.content[rowIndex];

    //         for (let cellIndex = 0, count = row1.getCellsCount(); cellIndex < count; cellIndex++) {
    //             const cell = row1.getCell(cellIndex);
    //             if ( VerticalMergeType.Continue !== cell.getVMerge() ) {
    //                 bRemove = false;
    //                 break;
    //             }
    //         }

    //         if ( true === bRemove ) {
    //             this.removeRow(rowIndex);
    //         }
    //     }

    //     // 将光标移动到下一列的开头
    //     const curRow = 0;
    //     const row = this.content[curRow];
    //     const cellsCount = row.getCellsCount();
    //     const curCell = ( undefined === deleteInfo[0][0] ? cellsCount - 1 : Math.min(deleteInfo[0][0], cellsCount - 1));

    //     this.curCell = row.getCell(curCell);
    //     this.curCell.content.moveCursorToStartPos();

    //     this.selection.curRow = curRow;
    //     this.selection.bUse = false;
    //     this.selection.bStart = false;
    //     this.selection.startPos.pos = { rowIndex: curRow, cellIndex: curCell };
    //     this.selection.endPos.pos = { rowIndex: curRow, cellIndex: curCell };

    //     this.recalcInfo.bTableBorders = true;
    //     this.recalculatePage();
    //     this.parent.recalculate();

    //     if (this.checkGirdValid()) {
    //         this.recalculatePage();
    //         this.parent.recalculate();
    //     }
    //     this.parent.setDirty();

    //     return true;
    // }

    /**
     * 删除表格row
     * @param rowIndex
     */
    // public removeRow(rowIndex: number): void {
    //     if ( 0 > rowIndex || rowIndex >= this.content.length ) {
    //         return ;
    //     }
    //     const enableRowNum = this.getEnableRowNum();
    //     const contents = this.content;
    //     const curRow = contents[enableRowNum];
    //     if (curRow && (contents.length - 2 === enableRowNum || rowIndex < enableRowNum)) {
    //         this.hideFirstRow(false);
    //         this.setEnableRowAction2(EnableRowActionType.Unable);
    //     }
    //     this.content[rowIndex].preDelete();

    //     const history = this.getHistory();
    //     if ( history ) {
    //         history.addChange(new ChangeTableRemoveRow(this, rowIndex, [this.content[rowIndex]]));
    //     }

    //     this.rows--;
    //     this.content.splice(rowIndex, 1);
    //     this.tableRowsBottom.splice(rowIndex, 1);
    //     this.rowsInfo.splice(rowIndex, 1);
    //     this.reIndexContent(rowIndex);
    //     this.checkCurCell();

    //     this.recalcInfo.bTableBorders = true;
    // }

    // public addTableColumn(bBefore: boolean): boolean {
    //     if ( null == bBefore ) {
    //         bBefore = true;
    //     }

    //     let cellsPos: ITableCellPos[] = [];
    //     // let insertRowIndex = -1;
    //     let count = 1;

    //     if ( true === this.selection.bUse && TableSelectionType.TableCell === this.selection.type ) {
    //         cellsPos = this.selection.data;
    //         count = 0;

    //         let prevRow = -1;
    //         for (const cellPos of cellsPos ) {
    //             if ( -1 !== prevRow ) {
    //                 if ( prevRow === cellPos.rowIndex ) {
    //                     count++;
    //                     prevRow = cellPos.rowIndex;
    //                 }
    //             } else {
    //                 count++;
    //                 prevRow = cellPos.rowIndex;
    //             }
    //         }
    //     } else {
    //         count = 1;
    //         cellsPos[0] = {rowIndex: this.curCell.row.index, cellIndex: this.curCell.index};
    //     }

    //     if ( 0 >= cellsPos.length ) {
    //         return false;
    //     }

    //     if ( this.content[cellsPos[0].rowIndex] &&
    //         gMaxColNum < this.content[cellsPos[0].rowIndex].getCellsCount() + count ) {
    //         const warning = '表格总列数不允许超过' + gMaxColNum;
    //         GlobalEvent.setEvent(this.parent.id, gEventName.TableEvent, MessageType.AddTableColumn, warning);
    //         return false;
    //     }

    //     let width = 0;
    //     if ( true === bBefore ) {
    //         const row = this.content[cellsPos[0].rowIndex];
    //         const startGrid = row.getCellInfo(cellsPos[0].cellIndex).startGridCol;
    //         const endGrid = startGrid + row.getCell(cellsPos[0].cellIndex)
    //                             .getGridSpan() - 1;
    //         width = this.tableSumGrid[endGrid] - this.tableSumGrid[startGrid - 1];
    //     } else {
    //         const row = this.content[cellsPos[cellsPos.length - 1].rowIndex];
    //         const startGrid = row.getCellInfo(cellsPos[cellsPos.length - 1].cellIndex).startGridCol;
    //         const endGrid = startGrid + row.getCell(cellsPos[cellsPos.length - 1].cellIndex)
    //                             .getGridSpan() - 1;
    //         width = this.tableSumGrid[endGrid] - this.tableSumGrid[startGrid - 1];
    //     }

    //     const rowsInfo: IRowInfoOfNewGrid[][] = [];
    //     const addInfo = [];
    //     if ( true === bBefore ) {
    //         let startGrid = -1;

    //         for (const cellPos of cellsPos) {
    //             const row = this.content[cellPos.rowIndex];
    //             // const cell = row.getCell(cellPos.cellIndex);
    //             const curGridStart = row.getCellInfo(cellPos.cellIndex).startGridCol;

    //             if ( -1 === startGrid || ( -1 !== startGrid && startGrid > curGridStart ) ) {
    //                 startGrid = curGridStart;
    //             }
    //         }

    //         for (const row of this.content) {
    //             rowsInfo[row.index] = [];
    //             addInfo[row.index] = 0;

    //             const beforeInfo = row.getBefore();
    //             if ( 0 < beforeInfo.gridBefore ) {
    //                 rowsInfo[row.index].push(
    //                     {width: this.tableSumGrid[beforeInfo.gridBefore - 1], type: -1, gridSpan: 1});
    //             }

    //             const cellsCount = row.getCellsCount();
    //             for (let cellIndex = 0; cellIndex < cellsCount; cellIndex++) {
    //                 const cell = row.getCell(cellIndex);
    //                 const curGridStart = row.getCellInfo(cellIndex).startGridCol;
    //                 const curGridEnd = curGridStart + cell.getGridSpan() - 1;

    //                 if ( startGrid >= curGridStart ) {
    //                     addInfo[row.index] = cellIndex;
    //                 }

    //                 const curWidth = this.tableSumGrid[curGridEnd] - this.tableSumGrid[curGridStart - 1];
    //                 rowsInfo[row.index].push({width: curWidth, type: 0, gridSpan: 1});
    //             }

    //             const afterInfo = row.getAfter();
    //             if ( 0 < afterInfo.gridAfter ) {
    //                 if ( startGrid >= row.getCellInfo(cellsCount - 1).startGridCol
    //                         + row.getCell(cellsCount - 1)
    //                                 .getGridSpan() ) {
    //                     addInfo[row.index] = cellsCount;
    //                 }
    //             }
    //         }

    //         for (const row of this.content) {
    //             let bBefore2 = false;
    //             if ( 0 < rowsInfo.length && -1 === rowsInfo[row.index][0].type ) {
    //                 bBefore2 = true;
    //             }

    //             for (let index = 0; index < count; index++) {
    //                 const newCell = row.addCell(addInfo[row.index], row, null, false);
    //                 const nextCell = ( addInfo[row.index] >= row.getCellsCount() - 1 ) ?
    //                     row.getCell(addInfo[row.index] - 1) : row.getCell(addInfo[row.index] + 1);

    //                 newCell.copyProperty(nextCell.property);

    //                 if ( false === bBefore2 ) {
    //                     rowsInfo[row.index].splice(addInfo[row.index], 0, {width, type: 0, gridSpan: 1});
    //                 } else {
    //                     rowsInfo[row.index].splice(addInfo[row.index] + 1, 0, {width, type: 0, gridSpan: 1});
    //                 }
    //             }
    //         }
    //     } else {
    //         let endGrid = -1;

    //         for (const cellPos of cellsPos) {
    //             const row = this.content[cellPos.rowIndex];
    //             const cell = row.getCell(cellPos.cellIndex);
    //             const curGridEnd = row.getCellInfo(cellPos.cellIndex).startGridCol + cell.getGridSpan() - 1;

    //             if ( -1 === endGrid || ( -1 !== endGrid && endGrid < curGridEnd ) ) {
    //                 endGrid = curGridEnd;
    //             }
    //         }

    //         for (const row of this.content) {
    //             rowsInfo[row.index] = [];
    //             addInfo[row.index] = 0;

    //             const beforeInfo = row.getBefore();
    //             if ( 0 < beforeInfo.gridBefore ) {
    //                 rowsInfo[row.index].push(
    //                     {width: this.tableSumGrid[beforeInfo.gridBefore - 1], type: -1, gridSpan: 1});
    //             }

    //             const cellsCount = row.getCellsCount();
    //             for (let cellIndex = 0; cellIndex < cellsCount; cellIndex++) {
    //                 const cell = row.getCell(cellIndex);
    //                 const curGridStart = row.getCellInfo(cellIndex).startGridCol;
    //                 const curGridEnd = curGridStart + cell.getGridSpan() - 1;

    //                 if ( endGrid >= curGridEnd ) {
    //                     addInfo[row.index] = cellIndex;
    //                 }

    //                 const curWidth = this.tableSumGrid[curGridEnd] - this.tableSumGrid[curGridStart - 1];
    //                 rowsInfo[row.index].push({width: curWidth, type: 0, gridSpan: 1});
    //             }
    //         }

    //         for (const row of this.content) {
    //             let bBefore2 = false;
    //             if ( 0 < rowsInfo.length && -1 === rowsInfo[row.index][0].type ) {
    //                 bBefore2 = true;
    //             }

    //             for (let index = 0; index < count; index++) {
    //                 const newCell = row.addCell(addInfo[row.index] + 1, row, null, false);
    //                 const nextCell = ( addInfo[row.index] + 1 >= row.getCellsCount() - 1 ) ?
    //                     row.getCell(addInfo[row.index]) : row.getCell(addInfo[row.index] + 2);

    //                 newCell.copyProperty(nextCell.property);

    //                 if ( false === bBefore2 ) {
    //                     rowsInfo[row.index].splice(addInfo[row.index] + 1, 0, {width, type: 0, gridSpan: 1});
    //                 } else {
    //                     rowsInfo[row.index].splice(addInfo[row.index] + 2, 0, {width, type: 0, gridSpan: 1});
    //                 }
    //             }
    //         }
    //     }

    //     this.createNewGrid(rowsInfo);

    //     if ( null != this.selection.data ) {
    //         this.selection.data.length = 0;
    //     } else {
    //         this.selection.data = [];
    //     }

    //     this.selection.bUse = true;
    //     this.selection.type = TableSelectionType.TableCell;

    //     for (let index = 0; index < this.content.length; index++) {
    //         const startCell = ( true === bBefore ) ? addInfo[index] : addInfo[index] + 1;

    //         for (let cellIndex = 0; cellIndex < count; cellIndex++) {
    //             this.selection.data.push({rowIndex: index, cellIndex: startCell + cellIndex});
    //         }
    //     }

    //     this.curCell = this.content[this.selection.data[0].rowIndex].getCell(this.selection.data[0].cellIndex);
    //     this.parent.recalculate();
    //     this.parent.updateCursorXY();
    //     return true;
    // }

    // public addTableRow(bBefore: boolean, rowInfo?: any): boolean {
    //     if ( null == bBefore ) {
    //         bBefore = true;
    //     }

    //     let cellsPos: ITableCellPos[] = [];
    //     let insertRowIndex = -1;
    //     let count = 1;

    //     if ( true === this.selection.bUse && TableSelectionType.TableCell === this.selection.type ) {
    //         cellsPos = this.selection.data;
    //         count = 0;

    //         let prevRow = -1;
    //         for (const cellPos of cellsPos ) {
    //             if ( prevRow !== cellPos.rowIndex ) {
    //                 count++;
    //                 prevRow = cellPos.rowIndex;
    //             }
    //         }
    //     } else if (rowInfo) {
    //         count = rowInfo.count;
    //         insertRowIndex = rowInfo.insertIndex;
    //         cellsPos[0] = {rowIndex: rowInfo.rowIndex, cellIndex: rowInfo.cellIndex};
    //     } else {
    //         count = 1;
    //         cellsPos[0] = {rowIndex: this.curCell.row.index, cellIndex: this.curCell.index};
    //     }

    //     if ( 0 >= cellsPos.length ) {
    //         return false;
    //     }

    //     count = 1;
    //     if ( this.content && gMaxRowNum < this.content.length + count ) {
    //         const warning = '表格总行数不允许超过' + gMaxRowNum;
    //         GlobalEvent.setEvent(this.parent.id, gEventName.TableEvent, MessageType.AddTableRow, warning);
    //         return false;
    //     }

    //     if ( true === bBefore ) {
    //         insertRowIndex = cellsPos[0].rowIndex;
    //     } else {
    //         insertRowIndex = cellsPos[cellsPos.length - 1].rowIndex;
    //     }

    //     // const history = this.getHistory();
    //     // if ( history ) {
    //     //     history.createNewHistoryPoint(HistoryDescriptionType.DocumentAddTableRow);
    //     // }

    //     const row = this.content[insertRowIndex];
    //     const cellsInfo = [];

    //     for (let index = 0, cellsCount = row.getCellsCount(); index < cellsCount; index++) {
    //         const cell = row.getCell(index);
    //         const cellInfo = row.getCellInfo(index);
    //         const cellStartGrid = cellInfo.startGridCol;
    //         const cellGridSpan = cell.getGridSpan();
    //         const vMergeCountBefore = this.getVerticalMergeCount2(insertRowIndex, cellStartGrid, cellGridSpan);
    //         const vMergeCountAfter = this.getVerticalMergeCount(insertRowIndex, cellStartGrid, cellGridSpan);

    //         cellsInfo[index] = { vMergeCountBefore, vMergeCountAfter};
    //     }

    //     for (let index = 0, cellsCount = row.getCellsCount(); index < count; index++) {
    //         let newRow;

    //         if ( true === bBefore ) {
    //             newRow = this.addRow(insertRowIndex, cellsCount, true);
    //         } else {
    //             newRow = this.addRow(insertRowIndex + 1, cellsCount, true);
    //         }

    //         newRow.copyProperty(row.property);

    //         for (let cellIndex = 0; cellIndex < cellsCount; cellIndex++) {
    //             const newCell = newRow.getCell(cellIndex);
    //             const oldCell = row.getCell(cellIndex);

    //             newCell.copyProperty(oldCell.property);

    //             if ( true === bBefore ) {
    //                 if ( 1 < cellsInfo[cellIndex].vMergeCountBefore ) {
    //                     newCell.setVMerge(VerticalMergeType.Continue);
    //                 } else {
    //                     newCell.setVMerge(VerticalMergeType.Restart);
    //                 }
    //             } else {
    //                 if ( 1 < cellsInfo[cellIndex].vMergeCountAfter ) {
    //                     newCell.setVMerge(VerticalMergeType.Continue);
    //                 } else {
    //                     newCell.setVMerge(VerticalMergeType.Restart);
    //                 }
    //             }
    //         }
    //     }

    //     if ( null != this.selection.data ) {
    //         this.selection.data.length = 0;
    //     } else {
    //         this.selection.data = [];
    //     }

    //     const startRow = ( true === bBefore ) ? insertRowIndex : insertRowIndex + 1;

    //     this.selection.bUse = true;
    //     this.selection.type = TableSelectionType.TableCell;
    //     this.selection.startPos.pos = { rowIndex: startRow, cellIndex: 0 };
    //     this.selection.endPos.pos = { rowIndex: startRow + count - 1,
    //         cellIndex: this.content[startRow + count - 1].getCellsCount() - 1 };

    //     const arr = [];
    //     for (let index = 0; index < count; index++) {
    //         const curRow = this.content[index];
    //         const cellsCount = curRow.getCellsCount();

    //         for (let cellIndex = 0; cellIndex < cellsCount; cellIndex++) {
    //             const cell = curRow.getCell(cellIndex);
    //             if ( VerticalMergeType.Continue === cell.getVMerge() ) {
    //                 continue;
    //             }

    //             if (VerticalMergeType.Continue !== this.content[startRow + index]?.getCell(cellIndex)
    //                             ?.getVMerge()) {
    //                 arr.push({rowIndex: startRow + index, cellIndex});
    //             }
    //         }
    //     }

    //     // this.curCell = this.content[startRow].getCell(0);
    //     this.setSelectionData(arr);
    //     // this.parent.recalculate();
    //     // this.parent.updateCursorXY();
    //     return true;
    // }

    public reIndex(): void {
        this.reIndexContent(0);

        for (let index = 0, count = this.content.length; index < count; index++) {
            this.content[index].reIndex(0);
        }
    }

    public getIndex(): number {
        if ( !this.parent ) {
            return -1;
        }

        this.parent.updateContentIndexing();
        return this.index;
    }

    public getRepeatOnBreak(): boolean {
        const bRepeatOnBreak = this.property.isRepeatOnBreak();
        return bRepeatOnBreak;
    }

    public isTableFirstRowOnNewPage(curRow: number): boolean {
        for (let curPage = 0, count = this.pages.length; curPage < count; curPage++) {
            const page = this.pages[curPage];

            if ( curRow === page.firstRow && curRow <= page.lastRow ) {
                if ( 0 === curPage
                    && ( null !== this.getDocumentPrev()
                        || ( this.parent.isTableCellContent() && true !== this.parent.isTableFirstRowOnNewPage() )
                        || ( this.parent.isRegionContent() && true !== this.parent.isRegionFirstOnNewPage())) ) {
                    return false;
                }

                return true;
            }
        }

        return false;
    }

    public copy(parent: DocumentContentBase, option?: any): DocumentContentElementBase {
        const table = new Table(parent, this.logicDocument);

        table.rows = this.rows;
        table.cols = this.cols;
        // table.setParagraphProperty(this.property.copy());
        table.property = this.property.copy();
        // not alert error
        table.rowsInfo = this.rowsInfo;
        const contents = this.content;
        const count = contents.length;
        const bPrintEmptyRow = this.property.isPrintEmptyRow();
        
        let curIndex = 0;
        let lastRow;
        for (let index = 0; index < count; index++) {
            const row = contents[index];
            // 打印状态下，过滤空行
            if (bPrintEmptyRow === false && option?.bPrint  && row.isEmptyRow()) {
                table.rowsInfo.splice(curIndex, 1);
                if (index + 1 === count) {
                    lastRow = row;
                }
                continue;
            }
            curIndex++;
            table.content.push(row.copy(table, option));
            // gHistory.addChange
        }

        if (!table.content.length) {
            return;
        }

        // 打印过滤掉最后的空行，重新对底部边框进行赋值
        if (lastRow) {
            const curRow = table.content[table.content.length - 1];
            curRow.content.forEach((cell) => {
                if (cell.borderInfo.bottom.length) {
                    if (cell.borderInfo.bottom[0].size === 0) {
                        cell.borderInfo.bottom[0].size = 1;
                    }
                } else if (cell.property.borders.bottom) {
                    cell.property.borders.bottom.size = 1;
                }
            });
        }

        table.reIndexContent(0);
        table.tableGrid = this.tableGrid.slice();

        if ( 0 < count && 0 < table.content[0].getCellsCount() ) {
            table.curCell = table.content[0].getCell(0);
        }

        return table;
    }

    public getRow(index: number): TableRow {
        if ( null == index || 0 > index || this.content.length <= index ) {
            return null;
        }

        return this.content[index];
    }

    /**
     * 检查当前光标所在单元格的合法性
     */
    // public checkCurCell(): void {
    //     if ( this.curCell ) {
    //         const row = this.curCell.getRow();

    //         if (!row || row.getTable() !== this || this.getRow(row.getIndex()) !== row
    //             || this.curCell !== row.getCell(this.curCell.getIndex())) {
    //             this.curCell = null;
    //         }
    //     }

    //     if ( !this.curCell ) {
    //         for (let curRow = 0, rowsCount = this.content.length; curRow < rowsCount; curRow++) {
    //             const row = this.content[curRow];
    //             if ( 0 < row.getCellsCount() && VerticalMergeType.Continue !== row.getCell(0)
    //                                             .getVMerge() ) {
    //                 this.curCell = row.getCell(0);
    //                 break;
    //             }
    //         }
    //     }
    // }

    // public recalculateGridCols(): void {
    //     this.tableRecal.recalculateGridCols();
    // }

    // public setParagraphAlignment(alignment: number): boolean {
    //     if ( true === this.bApplyToAll || ( true === this.selection.bUse
    //             && TableSelectionType.TableCell === this.selection.type && 0 < this.selection.data.length ) ) {
    //         const selectedCellsArray = this.getSelectionArray();

    //         if ( selectedCellsArray && 0 !== selectedCellsArray.length ) {
    //             for (const pos of selectedCellsArray) {
    //                 const row = this.content[pos.rowIndex];
    //                 const cell = row.getCell(pos.cellIndex);
    //                 const cellContent = cell.content;

    //                 cellContent.setApplyToAll(true);
    //                 cellContent.setParagraphAlignment(alignment);
    //                 cellContent.setApplyToAll(false);
    //             }
    //         }

    //     } else {
    //         this.curCell.content.setParagraphAlignment(alignment);
    //     }
    //     return true;
    // }

    // public setParagraphProperty(paraProperty: any): boolean {
    //     // key-wasm by tinyzhi
    //     // if ( true === this.bApplyToAll || ( true === this.selection.bUse
    //     //         && TableSelectionType.TableCell === this.selection.type && 0 < this.selection.data.length ) ) {
    //     let result = false;
    //     const nResult = WasmInstance.instance._Table_setParagraphProperty(Number(this.bApplyToAll),
    //                             Number(this.selection.bUse), TableSelectionType.TableCell,
    //                             this.selection.type, this.selection.data && this.selection.data.length);
    //     if (Boolean(nResult)) {
    //     // end by tinyzhi
    //         const selectedCellsArray = this.getSelectionArray();

    //         if ( selectedCellsArray && 0 !== selectedCellsArray.length ) {
    //             for (const pos of selectedCellsArray) {
    //                 const row = this.content[pos.rowIndex];
    //                 const cell = row.getCell(pos.cellIndex);
    //                 const cellContent = cell.content;

    //                 cellContent.setApplyToAll(true);
    //                 result = cellContent.setParagraphProperty(paraProperty) || result;
    //                 cellContent.setApplyToAll(false);
    //             }
    //         }

    //     } else if (this.curCell) {
    //         result = this.curCell.content.setParagraphProperty(paraProperty);
    //     }

    //     return result;
    // }

    // public getParagraphProperty(): ParaProperty {
    //     // if ( true === this.bApplyToAll || ( true === this.selection.bUse
    //     //     && TableSelectionType.TableCell === this.selection.type && 0 < this.selection.data.length ) ) {
    //     //     const selectedCellsArray = this.getSelectionArray();

    //     //     for (const pos of selectedCellsArray) {
    //     //         const row = this.content[pos.rowIndex];
    //     //         const cell = row.getCell(pos.cellIndex);
    //     //         const cellContent = cell.content;

    //     //         cellContent.setApplyToAll(true);
    //     //         cellContent.setParagraphProperty(paraProperty);
    //     //         cellContent.setApplyToAll(false);
    //     //     }

    //     // } else
    //     if (this.curCell) {
    //         return this.curCell.content.getParagraphProperty();
    //     }
    // }

    // /**
    //  * add table row
    //  * @param rowIndex start from 0
    //  * @param cellsCount start from 1. Abs count
    //  * @param bReIndex whether to recalc all row linkedlist
    //  */
    // public addRow(rowIndex: number, cellsCount: number, bReIndex: boolean, newRow?: TableRow): TableRow {
    //     if ( 0 > rowIndex ) {
    //         rowIndex = 0;
    //     }

    //     if ( this.content.length <= rowIndex ) {
    //         rowIndex = this.content.length;
    //     }

    //     this.rows++;

    //     newRow = ( null == newRow ? new TableRow(this, cellsCount) : newRow );

    //     const history = this.getHistory();
    //     if ( history ) {
    //         history.addChange(new ChangeTableAddRow(this, rowIndex, [newRow]));
    //     }

    //     this.content.splice(rowIndex, 0, newRow);
    //     this.tableRowsBottom.splice(rowIndex, 0, []);

    //     const rowInfo = new TableRowInfo();
    //     rowInfo.pages = 1;
    //     rowInfo.bFirstPage = true;
    //     this.rowsInfo.splice(rowIndex, 0, rowInfo);

    //     if ( true !== bReIndex ) {
    //         if ( 0 < rowIndex ) {
    //             this.content[rowIndex - 1].next = newRow;
    //             newRow.prev = this.content[rowIndex - 1];
    //         } else {
    //             newRow.prev = null;
    //         }

    //         if ( this.content.length - 1 > rowIndex ) {
    //             this.content[rowIndex + 1].prev = newRow;
    //             newRow.next = this.content[rowIndex + 1];
    //         } else {
    //             newRow.next = null;
    //         }

    //     } else {
    //         this.reIndexContent(rowIndex);
    //     }

    //     newRow.table = this;

    //     return newRow;
    // }

    public setTableCellMargin(left: number, right: number, top: number, bottom: number): void {
        const margins = this.property.tableCellMargin;
        const oldLeft   = margins.left;
        const oldRight  = margins.right;
        const oldTop    = margins.top;
        const oldBottom = margins.bottom;

        const leftNew = null != left ? new TableMeasurement(TableWidthType.Mm, getPxForMM(left)) : null;
        const rightNew = null != right ? new TableMeasurement(TableWidthType.Mm, getPxForMM(right)) : null;
        const topNew = null != top ? new TableMeasurement(TableWidthType.Mm, getPxForMM(top)) : null;
        const bottomNew = null != bottom ? new TableMeasurement(TableWidthType.Mm, getPxForMM(bottom)) : null;

        const history = this.getHistory();
        if ( history && history.canAdd()) {
            history.addChange(new ChangeTableDefaultCellMargins(this,
                {
                    left   : oldLeft,
                    right  : oldRight,
                    top    : oldTop,
                    bottom : oldBottom
                },
                {
                    left   : leftNew,
                    right  : rightNew,
                    top    : topNew,
                    bottom : bottomNew
                }));
        }

        margins.left = leftNew;
        margins.right = rightNew;
        margins.top = topNew;
        margins.bottom = bottomNew;

        // this.parent.recalculate();
        // this.parent.updateCursorXY();
    }

    // public getRowHeight(rowIndex: number): number {
    //     if ( this.content[rowIndex] ) {
    //         return this.content[rowIndex].getCurrentRowHeight();
    //     }
    //     return undefined;
    // }

    // public setRowHeight(height: number, bAuto: boolean, rowIndex?: number): void {
    //     if ( true === this.isFixedRowHeight() || null == bAuto ) {
    //         return ;
    //     }

    //     if ( true !== bAuto || !this.isFixedRowHeight() ) {
    //         const margins = this.property.tableCellMargin;
    //         height = getPxForMM(height) - margins.top.width - margins.bottom.width;
    //     }
    //     const rowsRange = (null == rowIndex ? this.getSelectedRowsRange() :
    //                         { start: rowIndex, end: rowIndex } );

    //     // let bContinue = false;
    //     for (let index = rowsRange.start; index <= rowsRange.end; index++) {
    //         const row = this.getRow(index);
    //         // const cellsCount = row.getCellsCount();

    //         // for (let cellIndex = 0; cellIndex < cellsCount; cellIndex++) {
    //         //     const cell = row.getCell(cellIndex);
    //         //     const vm = cell.getVMerge();

    //         //     if ( VerticalMergeType.Continue === vm ) {
    //         //         // const count = this.getVMergeCount(cellIndex, row.index);
    //         //         bContinue = true;
    //         //     }
    //         // }

    //         // if ( bContinue ) {
    //         //     bContinue = false;
    //         //     continue;
    //         // }

    //         if ( true === bAuto ) {
    //             row.setHeight(0, TableRowLineRule.Auto);
    //         } else {
    //             row.setHeight(height, TableRowLineRule.Exact);
    //         }
    //     }

    //     // this.parent.recalculate();
    //     // this.parent.updateCursorXY();
    // }

    public setRowHeight2(height: number, bAuto: boolean, rowIndex: number): boolean {
        let result = false;
        const row = this.content[rowIndex];

        if ( row ) {
            if ( (true !== bAuto || !this.isFixedRowHeight()) && null != height && 0 <= height ) {
                const margins = this.property.tableCellMargin;
                height = getPxForMM(height) - margins.top.width - margins.bottom.width;
            }

            const rowHeight = row.getHeight();
            if ( rowHeight.isAuto() ) {
                if ( !bAuto ) {
                    result = row.setHeight(height, TableRowLineRule.Exact);
                }
            } else {
                if ( bAuto ) {
                    result = row.setHeight(0, TableRowLineRule.Auto);
                } else {
                    result = row.setHeight(height, TableRowLineRule.Exact);
                }
            }
        }

        return result;
    }

    // public getCurrentRowHeight(): number {
    //     let height = 0;
    //     if ( this.selection && TableSelectionType.TableCell === this.selection.type ) {
    //         return height;
    //     }

    //     const row = this.curCell.getRow();
    //     if ( row ) {
    //        height = row.getCurrentRowHeight();

    //        if ( this.isFixedRowHeight() || row.isFixed() ) {
    //            height -= ( row.getTopMargin() + row.getBottomMargin() );
    //        }
    //     }

    //     return height;
    // }

    // public getRowHeight(): number {
    //     let height: number = null;

    //     if ( true === this.selection.bUse && TableSelectionType.TableCell === this.selection.type ) {
    //         for (let index = 0, count = this.selection.data.length; index < count; index++) {
    //             const pos = this.selection.data[index];
    //             const row = this.content[pos.rowIndex];
    //             const cell = row.getCell(pos.cellIndex);

    //             const rowHeight = row.getHeight();
    //             let curRowHeight = 0;
    //             if ( rowHeight.isAuto() ) {
    //                 let rowH = 0;
    //                 const curRow = row.getIndex();

    //                 if ( this.rowsInfo[curRow] ) {
    //                     for ( const curPage of this.rowsInfo[curRow].height ) {
    //                         rowH += curPage;
    //                     }

    //                     if ( this.rowsInfo[curRow].topDy[0] ) {
    //                         rowH -= this.rowsInfo[curRow].topDy[0];
    //                     }

    //                     rowH -= ( row.getTopMargin() + row.getBottomMargin() );
    //                 }

    //                 height = rowH;
    //             } else {
    //                 height = rowHeight.value;
    //             }

    //             if ( null === height ) {
    //                 height = curRowHeight;
    //             } else if ( undefined !== curRowHeight && TABLE_RECALC_TOL < Math.abs(curRowHeight - height)) {
    //                 curRowHeight = undefined;
    //             }
    //         }
    //     } else {
    //         const row = this.curCell.getRow();
    //         const rowHeight = row.getHeight();
    //         if ( rowHeight.isAuto() ) {
    //             let rowH = 0;
    //             const curRow = row.getIndex();

    //             if ( this.rowsInfo[curRow] ) {
    //                 for ( const curPage of this.rowsInfo[curRow].height ) {
    //                     rowH += curPage;
    //                 }

    //                 if ( this.rowsInfo[curRow].topDy[0] ) {
    //                     rowH -= this.rowsInfo[curRow].topDy[0];
    //                 }

    //                 rowH -= ( row.getTopMargin() + row.getBottomMargin() );
    //             }

    //             height = rowH;
    //         } else {
    //             height = rowHeight.value;
    //         }
    //     }

    //     return height;
    // }

    public setColumnWidth(width: number): boolean {
        if ( true === this.isFixedColWidth() ) {
            return ;
        }

        let bChange = false;
        width = getPxForMM(width);
        // const rowsRange = this.getSelectedRowsRange();
        const selectedCellsArray = this.getSelectionArray();

        const cells: number[] = [];
        const rowsInfo = this.getRowsInfo();
        for (const cell of selectedCellsArray) {
            cells[cell.cellIndex] = 1;
        }

        for (const row of rowsInfo ) {
            const nAdd = -1 === row[0].type ? 1 : 0;

            for (const curCell in cells) {
                if (cells.hasOwnProperty(curCell)) {
                    const cellIndex = parseInt(curCell, 0) | 0;
                    if ( row[cellIndex + nAdd] && (1 <= Math.abs(width - row[cellIndex + nAdd].width))  ) {
                        row[cellIndex + nAdd].width = width;
                        bChange = true;
                    }
                }
            }
        }

        if ( bChange ) {
            this.createNewGrid(rowsInfo);
        }

        return bChange;
        // this.parent.recalculate();
        // this.parent.updateCursorXY();
    }

    public setColumnsWidth(width: number[], rowIndex?: number): boolean {
        if ( true === this.isFixedColWidth() ) {
            return ;
        }

        const rowsInfo = this.getRowsInfo();
        let bChange = false;
        let curRow = (null == rowIndex ? 0 : rowIndex);
        const rowsCount = (null == rowIndex ? this.content.length : rowIndex + 1);

        for (; curRow < rowsCount; curRow++) {
            // rowsInfo[curRow] = [];
            const row = this.content[curRow];
            const beforeInfo = row.getBefore();

            for (let curCell = 0, cellsCount = row.getCellsCount();
                curCell < cellsCount && width.length === cellsCount; curCell++) {
                // const cell = row.getCell(curCell);
                // const curGridStart = row.getCellInfo(curCell).startGridCol;
                // const curGridEnd = curGridStart + cell.getGridSpan() - 1;

                // rowsInfo[curRow].push({
                //     width: this.tableSumGrid[curGridEnd] - this.tableSumGrid[curGridStart - 1],
                //     type: 0,
                //     gridSpan: 1,
                // });
                const curWidth = getPxForMM(width[curCell] * 10);

                if ( 1 <= Math.abs(curWidth - rowsInfo[curRow][curCell].width) ) {
                    rowsInfo[curRow][curCell].width = curWidth;
                    bChange = true;
                }
            }
        }

        if ( bChange ) {
            this.createNewGrid(rowsInfo);
        }

        return bChange;
        // this.parent.recalculate();
        // this.parent.updateCursorXY();
    }

    /**
     * 获取选中单元格、光标所在单元格的列宽
     */
    public getColumnWidth(): number {
        let width: number = null;
        const selectedCellsArray = this.getSelectionArray();

        const cells: number[] = [];
        const rowsInfo = this.getRowsInfo();
        for (const cell of selectedCellsArray) {
            cells[cell.cellIndex] = 1;
        }

        for (const row of rowsInfo ) {
            const nAdd = -1 === row[0].type ? 1 : 0;

            // for (let curCell = 0, count = cells.length; curCell < count; curCell++) {
            for (const curCell in cells) {
                if (cells.hasOwnProperty(curCell)) {
                    const cellIndex = parseInt(curCell, 0) | 0;
                    if ( row[cellIndex + nAdd] ) {
                        if ( null === width ) {
                            width = (row[cellIndex + nAdd].width);
                        } else if ( 1 < Math.abs(width - row[cellIndex + nAdd].width)) {
                            width = undefined;
                            break;
                        }
                    }
                }
            }

            if ( undefined === width ) {
                break;
            }
        }

        return width;
    }

    // public getMinWidth(): number {
    //     ;
    // }

    /**
     * 获取所有列的宽度
     */
    public getAllColumnsWidth(): number[] {
        const widths: number[] = [];
        const rowsInfo = this.getRowsInfo();
        // const tableCellMargin = this.property.tableCellMargin;

        if (this.curCell == null || rowsInfo == null) {
            return widths;
        }
        for (let index = 0, count = rowsInfo[this.curCell.row.index].length; index < count; index++) {
            const width = rowsInfo[this.curCell.row.index][index].width;
            widths[index] = getMMFromPx(width) / 10;
            widths[index] = parseFloat(widths[index].toFixed(2));
        }
        return widths;
    }

    public addDocContentChild(options: any, cleanMode: CleanModeType): void {
        // this.parent = options.parent;
        options = {...options};
        // options.parent = this;
        this.setLogicDocument(options.doc);
        for (let curRow = 0, rowsCount = this.content.length; curRow < rowsCount; curRow++) {
            const row = this.content[curRow];
            for (let curCell = 0, cellsCount = row.getCellsCount(); curCell < cellsCount; curCell++) {
                const cell = row.getCell(curCell);
                cell.content.addDocContentChild(options, cleanMode);
            }
        }
    }

    public putCellContentByArray(texts: any): number {
        const keys = Object.keys(texts);
        if (keys.length === 0) {
            return ResultType.Failure;
        }
        let res: number = ResultType.UnEdited;
        keys.forEach((key) => {
            const tabeCell = this.getTableCellByName(key);
            if (!tabeCell) {
                return;
            }

            let para: Paragraph = (tabeCell.getContent() ? tabeCell.getContent()[0] as Paragraph : null);
            const textProp = (para && para.content[0] ? para.content[0].textProperty : null);
            const paraProperty = (para && para.getParagraphProperty()
                                            .copy());

            if (!tabeCell.content.isEmpty()) {
                tabeCell.content.clearContent(true);
            }

            para = tabeCell.getContent()[0] as Paragraph;
            if (paraProperty) {
                para.paraProperty = paraProperty;
                // para.content[0].textProperty = textProp.copy();
            }

            const value = filterChars2(texts[key]);
            if (value) {
                const portion = new ParaPortion(para);
                if ( textProp ) {
                    portion.textProperty = textProp.copy();
                }
                portion.addText(value);
                para.addToContent(0, portion);
            } else {
                para.content[0].textProperty = textProp.copy();
            }
            res = ResultType.Success;
        });

        return res;
    }

    // public setTableCellWidth(width: number): void {
    //     const rowsRange = this.getSelectedRowsRange();

    //     for (let index = rowsRange.start; index <= rowsRange.end; index++) {
    //         const row = this.getRow(index);
    //         const rowHeight = row.getHeight();
    //     }
    //     // this.setTableGrid()
    // }

    public setPropertys(props: ITableProperty): boolean {
        const properties = this.property;
        const margins = props.tableCellMargins;
        const targetMargins = properties.tableCellMargin;
        targetMargins.left = new TableMeasurement(TableWidthType.Mm, margins.left);
        targetMargins.right = new TableMeasurement(TableWidthType.Mm, margins.right);
        targetMargins.bottom = new TableMeasurement(TableWidthType.Mm, margins.bottom);
        targetMargins.top = new TableMeasurement(TableWidthType.Mm, margins.top);
        this.setCanAddRow(props.bCanAddRow);
        this.setCanDeleteRow(props.bCanDeleteRow);
        this.setDeleteProtect(props.bDeleteProtect);
        this.setFixedColWidth(props.bFixedColWidth);
        properties.setPrintEmptyRow(props.bPrintEmptyRow);
        properties.setRepeatOnBreak(props.bRepeatOnBreak);
        // this.setFixedRowHeight(props.bFixedRowHeight);
        properties['bFixedRowHeight'] = props.bFixedRowHeight;
        this.setTableReadOnly(props.bReadOnly);
        properties.addCustomProps(props.customProperty);
        this.setTableName(props.tableName);
        if (props['borders']) {
            properties.tableBorders = props['borders'];
        }
        // this.setTableBorders(props.tableBorders);

        return true;
    }

    public setTableProps( props: ITableProperty ): boolean {
        let bApplyToInnerTable = false;
        if ( false === this.selection.bUse ) {
            bApplyToInnerTable = this.curCell.content.setTableProps(props);
        }

        if ( true === bApplyToInnerTable ) {
            return true;
        }

        this.logicDocument.startAction(HistoryDescriptionType.DocumentSetTableProperty);

        if ( null != props.tableName && this.tableName !== props.tableName ) {
            this.setTableName(props.tableName);
        }

        let bNeedChange = false;
        if ( null != props.tableDefaultMargins ) {
            // let bUsingDefaultMargin = false;

            const oldMargins = this.property.tableCellMargin;
            const margins = props.tableDefaultMargins;
            const leftNew = null != margins.left ? margins.left : oldMargins.left.width;
            const rightNew = null != margins.right ? margins.right : oldMargins.right.width;
            const topNew = null != margins.top ? margins.top : oldMargins.top.width;
            const bottomNew = null != margins.bottom ? margins.bottom : oldMargins.bottom.width;

            if ( leftNew !== oldMargins.left.width || rightNew !== oldMargins.right.width ||
                topNew !== oldMargins.top.width || bottomNew !== oldMargins.bottom.width ) {
                bNeedChange = true;
            }

            if ( true === bNeedChange ) {
                this.setTableCellMargin(leftNew, rightNew, topNew, bottomNew);
            }
        }

        if ( null != props.rowHeights ) {
            if ( true === this.isFixedRowHeight() ) {
                bNeedChange = false;
                // this.setRowHeight(props.rowHeights);
            }
        }

        // if ( null != props.rowHeight && !this.isFixedRowHeight()) {
        //     bNeedChange = true;
        //     this.setRowHeight(props.rowHeight, props.bRowAuto, props.rowIndex);
        // }

        if ( null != props.columnWidth ) {
            // bNeedChange = true;
            bNeedChange = this.setColumnWidth(props.columnWidth) || bNeedChange;
        }

        // if ( null != props.tableCellWidth ) {
        //     this.setTableCellWidth(props.tableCellWidth);
        // }

        if ( null != props.columnWidths ) {
            // if ( true === this.isFixedColWidth() ) {
                // this.setColumnWidth(props.columnWidth);
                // bNeedChange = true;
                bNeedChange = this.setColumnsWidth(props.columnWidths, this.curCell.row.index) || bNeedChange;
            // }
        }

        if ( null != props.tableBorders ) {
            const bAllNullBorder = this.checkAllNullBorder();
            if ( 0 === props.tableBorders.type && bAllNullBorder ) {
                bNeedChange = this.setTableBorders(props.tableBorders) || bNeedChange;
            } else if ( 1 === props.tableBorders.type || !bAllNullBorder ) {
                props.cellBorders = props.tableBorders;
            }
        }

        if ( null != props.cellBorders ) {
            bNeedChange = this.setCellBorders(props.cellBorders, 0 === props.tableBorders.type) || bNeedChange;
        }

        if (props.bReadOnly != null && this.isReadOnlyProtect() !== props.bReadOnly ) {
            this.setTableReadOnly(props.bReadOnly);
        }

        if (props.bDeleteProtect != null && this.isDeleteProtect() !== props.bDeleteProtect ) {
            this.setDeleteProtect(props.bDeleteProtect);
        }

        if (props.bCanDeleteRow != null && this.canDeleteRow() !== props.bCanDeleteRow ) {
            this.setCanDeleteRow(props.bCanDeleteRow);
        }

        if (props.bCanAddRow != null && this.canAddRow() !== props.bCanAddRow ) {
            this.setCanAddRow(props.bCanAddRow);
        }

        if (props.bFixedRowHeight != null && this.isFixedRowHeight() !== props.bFixedRowHeight ) {
            bNeedChange = this.setFixedRowHeight(props.bFixedRowHeight) || bNeedChange;

            props.bRowAuto = props.bFixedRowHeight ? false : true;
            props.rowHeight = props.bFixedRowHeight ? props.rowHeight : undefined;

        }

        if (props.bPrintEmptyRow != null && this.property.isPrintEmptyRow() !== props.bPrintEmptyRow) {
            this.property.setPrintEmptyRow(props.bPrintEmptyRow);
        }

        if (props.bRepeatOnBreak != null && this.property.isRepeatOnBreak() !== props.bRepeatOnBreak) {
            this.property.setRepeatOnBreak(props.bRepeatOnBreak);
        }

        if (props.bFixedColWidth != null && this.isFixedColWidth() !== props.bFixedColWidth ) {
            this.setFixedColWidth(props.bFixedColWidth);
        }

        if (props.repeatHeaderNum != null && this.getTableRowsHeaderCount() !== props.repeatHeaderNum) {
            bNeedChange = this.setTableRowsHeaderCount(props.repeatHeaderNum) || bNeedChange;
        }

        bNeedChange = this.setRowHeight2(props.rowHeight, props.bRowAuto, props.rowIndex) || bNeedChange;

        if ( null != props.customProperty ) {
            this.property.setCustomProps(props.customProperty);
        }

        if (this.setEnableRowAction(props.enableRowAction) === ResultType.Success) {
            bNeedChange = true;
        }

        if (props.bRepeatHeader != null && this.isRepeatHeader() !== props.bRepeatHeader ) {
            bNeedChange = (this.setRepeatHeader(props.bRepeatHeader) && 2 <= this.pages.length) || bNeedChange;
        }

        if ( bNeedChange ) {
            this.parent.recalculate();
            this.parent.updateCursorXY();
            this.parent.setDirty(true);
        }

        this.logicDocument.endAction();
        return true;
    }

    /**
     * 是否允许设置enableRowAction属性, 0: 允许，1：只有一行有效行不允许，2：行有合并拆分不允许
     */
    public getRowActionType(): number {
        const length = this.content.length;
        let index = 0;
        for (; index < length; index++) {
            const curRow = this.content[index];
            if ( curRow && !curRow.isTableHeader() ) {
                if (curRow.isSplitOrMerge()) {
                    return 2;
                }
                return index !== length - 1 ? 0 : 1;
            }
        }

        return 1;
    }

    public isRowSettingVisible(): boolean {
        if (this.property.getEnableRowAction() !== EnableRowActionType.Enable) {
            return false;
        }

        if (this.selection.bUse || this.property.isTableReadOnlyProtect()) {
            return false;
        }

        if (this.logicDocument.isProtectedMode() || this.logicDocument.isStrictMode2()) {
            return false;
        }

        const curCell = this.curCell;
        if (!curCell) {
            return false;
        }

        const row = curCell.row;
        if (row.isTableHeader()) {
            return false;
        }

        // 光标在当前表格的最后一行
        const contents = this.content;
        const lastRow = contents[contents.length - 1];
        if (row === lastRow) {
            const enableRowActionRow = contents[this.getEnableRowNum()];
            if (!enableRowActionRow) {
                return false;
            }

            // 光标所在的行的列数与模板行的列数相同
            if (row.getCellsCount() ===  enableRowActionRow.getCellsCount()) {
                return true;
            }
        }

        if (curCell.getGridSpan() > 1 || row.isSplitOrMerge()) {
            return false;
        }

        const res = this.getRowActionType();
        if (res !== 0) {
            return false;
        }

        return true;
    }

    public addActionRow(): string {
        const cell = this.curCell;
        if (!cell) {
            return;
        }

        const contents = this.content;
        const index = this.getEnableRowNum();
        const curRow: TableRow = contents[index];
        if (!curRow) {
            return;
        }

        const newControlManager = this.logicDocument.getNewControlManager();
        newControlManager.clearPasteControls();
        let actPara: any;
        // let bSignatureBox: boolean;
        const resNames: any[] = [];
        let count = 0;
        // 需要进行初始化的元素
        const boxs = [];
        const option = {imgs: {}, structs: {}, structNames: [], create(name: string, para: any): any {
            const newControl = newControlManager.copyNewControl(name, para);
            if (!newControl) {
                return;
            }
            const newName = newControl.getNewControlName();
            if (count < 1) {
                resNames.push({name: newName, type: EXTERNAL_STRUCT_TYPE[newControl.getType()]});
            } else {
                count--;
            }
            if (newControl.isSignatureBox()) {
                count = newControl.leafList.length;
                newControl.leafList = [];
            } else if (newControl.isCheckBox() || newControl.isMultiAndRadio()) {
                boxs.push(newControl);
            }

            const parentName = this.structNames[this.structNames.length - 1];
            const structs = this.structs;
            structs[newName] = newControl;
            const obj: any = {control: newControl};
            if (parentName) {
                obj.parentName = parentName;
            }

            newControlManager.addPasteControl(obj);
            this.structNames.push(newName);
            if (!actPara) {
                actPara = para;
            }
            return newControl;
        }};
        let res = '';
        const newRow = curRow.copy(this, option);
        newRow.setHeight(10, TableRowLineRule.Auto);
        newRow.content.forEach((item) => item.setBottomAndTopBorderSize(1));
        const row = cell.row;
       
        this.addRow(row.index + 1, 0, true, newRow);

        boxs.forEach((box) => {
            box.addContent();
        });
        this.logicDocument.recalculate();
        if (resNames.length) {
            const pos = actPara.getCurContentPosInDoc();
            newControlManager.insertAllPasteNewControl(actPara.parent, pos);
            const additionalData = { index: newRow.index, counts: newRow.getCellsCount() };
            res = JSON.stringify({ resNames, ...additionalData });
        }

        const images = option.imgs;
        const drawObj = this.logicDocument.getDrawingObjects();
        // tslint:disable-next-line: forin
        for (const key in images) {
            const img = images[key];
            const oldImg = img.old;
            const newImg = img.new;
            newImg.name = drawObj.makeUniqueImageName(ParaElementType.ParaDrawing);
            newImg.src = oldImg.src;
            drawObj.addGraphicObject(newImg);
        }

        return res;
    }

    public getTableProps(): ITableProperty {
        const props: ITableProperty = {
            tableName: this.tableName,
            tableDefaultMargins: undefined,
            tableCellMargins: undefined,
            rowHeights: [],
            rowsAuto: [],
            tableCellWidth: 0,
            columnWidths: undefined,
            bRepeatHeader: this.isRepeatHeader(),
            repeatHeaderNum: this.getTableRowsHeaderCount(),
            rows: this.content.length,
            cols: this.tableGrid.length,
            bFixedRowHeight: this.isFixedRowHeight(),
            bFixedColWidth: this.isFixedColWidth(),
            bCanAddRow: this.canAddRow(),
            bCanDeleteRow: this.canDeleteRow(),
            bReadOnly: this.isReadOnlyProtect(),
            bDeleteProtect: this.isDeleteProtect(),
            customProperty: this.getCustomProps(),
            borderLineSize: this.getBorderLineSize(),
            borderColor: this.getBorderLineColor(),
            cellsVertAlign: this.getCellsVertAlign(),
            enableRowAction: this.property.getEnableRowAction(),
            bPrintEmptyRow: this.property.isPrintEmptyRow(),
            bRepeatOnBreak: this.property.isRepeatOnBreak(),
        };

        const margins = this.property.tableCellMargin;
        let top = getMMFromPx(margins.top.width) / 10;
        let left = getMMFromPx(margins.left.width) / 10;
        let bottom = getMMFromPx(margins.bottom.width) / 10;
        let right = getMMFromPx(margins.right.width) / 10;

        top = parseFloat(top.toFixed(2));
        left = parseFloat(left.toFixed(2));
        bottom = parseFloat(bottom.toFixed(2));
        right = parseFloat(right.toFixed(2));
        props.tableDefaultMargins = { top, bottom, left, right};

        // props.rowHeight = getMMFromPx(this.getRowHeight()) / 10;
        // props.rowHeight = parseFloat(props.rowHeight.toFixed(2));
        props.columnWidths = this.getAllColumnsWidth(); // getMMFromPx(this.getColumnWidth()) / 10;
        // props.repeatHeaderNum = this.getTableRowsHeaderCount();

        if ( true === this.bApplyToAll ) {
            const curPos = { rowIndex: [0, this.content.length - 1], cellIndex: [0, this.tableGrid.length - 1]};
            props.curPos = curPos;
        } else if ( true === this.selection.bUse && TableSelectionType.TableCell === this.selection.type
            && 0 < this.selection.data.length ) {
            const startPos = this.selection.startPos.pos;
            const endPos = this.selection.endPos.pos;
            const startRowIndex = startPos.rowIndex > endPos.rowIndex ? endPos.rowIndex : startPos.rowIndex;
            const endRowIndex = startPos.rowIndex > endPos.rowIndex ? startPos.rowIndex : endPos.rowIndex;
            const startColIndex = startPos.cellIndex > endPos.cellIndex ? endPos.cellIndex : startPos.cellIndex;
            const endColIndex = startPos.cellIndex > endPos.cellIndex ? startPos.cellIndex : endPos.cellIndex;
            const curPos = { rowIndex: [startRowIndex, endRowIndex], cellIndex: [startColIndex, endColIndex]};
            props.curPos = curPos;
        } else {
            if (this.curCell != null) {
                const curPos = { rowIndex: [this.curCell.row.index], cellIndex: [this.curCell.index] };
                props.curPos = curPos;
            }
        }

        this.content.forEach((row) => {
            let height = row.getCurrentRowHeight() + margins.top.width + margins.bottom.width;
            height = getMMFromPx(height) / 10;
            props.rowHeights.push(parseFloat(height.toFixed(2)));

            const rowHeight = row.getHeight();
            props.rowsAuto.push(!rowHeight.isFixed());
        });

        return props;
    }

    public getEnableRowNum(): number {
        return this.property.getEnableRowNum();
    }

    public hideFirstRow(bHide: boolean, bReflash?: boolean): number {
        let row: TableRow;
        let result = ResultType.Failure;
        const length = this.content.length;
        let index = 0;
        if (bHide !== true) {
            index = this.getEnableRowNum();
            row = this.content[index];
            this.property.setEnableRowNum(null);
        } else {
            for (; index < length; index++) {
                const curRow = this.content[index];
                if ( curRow && !curRow.isTableHeader() ) {
                    row = curRow;
                    // if (row.index !== index) {
                    //     row.index = index;
                    //     row.content.forEach((cell, cellIndex) => {
                    //         cell.index = cellIndex;
                    //     });
                    // }
                    break;
                }
            }
            if (index === length - 1) {
                return ResultType.Failure;
            }
            this.property.setEnableRowNum(index);
        }

        if ( row ) {
            // const height = row.getCurrentRowHeight();
            const margins = this.property.tableCellMargin;
            const diff = margins.top.width + margins.bottom.width;
            // if ( (bHide && 0 >= height - diff) || (!bHide && 0 <= height - diff) ) {
            //     if ( 0 >= height - diff ) {
            //         return ResultType.UnEdited;
            //     }
            //     // result = ResultType.Success;
            // }

            const newHeight = (bHide ? -diff : getPxForMM(10));
            const size = bHide ? 0 : 1;
            const cellBorders = {
                top: {
                    size,
                    color: new DocumentColor(0, 0, 0),
                    value: 1,
                },
                type: 1,
            };
            // let top: DocumentBorder;
            // const tops = this.curCell.borderInfo.top;
            // if (tops && tops.length > 0) {
            //     top = tops[0].copy();
            //     top.size = size;
            // } else {
            //     top = new DocumentBorder();
            //     top.size = size;
            // }
            // // const vBorder: DocumentBorder = new DocumentBorder();
            // // vBorder.size = 0;
            // const borders = {
            //     top,
            //     type: 1
            // };
            this.setCellBorders(cellBorders, false, row);
            // row.content.forEach((item) => item.setBottomAndTopBorderSize(size));
            const rule = bHide ? TableRowLineRule.Exact : TableRowLineRule.Auto;
            row.setHeight(newHeight, rule);

            // this.setCellBorders(cellBorders, false, row);
            result = ResultType.Success;
            if (bHide && row.next && this.curCell?.row === row) {
                const nextCell = row.next.content[0];
                const rowIndex = row.next.index;
                const cellIndex = nextCell.index;
                this.selection.bUse = false;
                this.selection.bStart = false;
                this.selection.startPos.pos = {rowIndex, cellIndex};
                this.selection.endPos.pos = {rowIndex, cellIndex};
                this.selection.curRow = rowIndex;
                this.curCell = nextCell;
                nextCell.moveCursorToStartPos();
            }
        }

        if (bReflash === true && ResultType.Success === result) {
            this.parent.recalculate();
            this.parent.updateCursorXY();
        }

        return result;
    }

    // public getTableProperty2(sJson: any): string {
    //     const customProperty = this.getCustomProps();
    //     if (customProperty && customProperty.length) {
    //         const props = [];
    //         for (const item of sJson) {
    //             const prop = customProperty.find((prop1) => (prop1.name === item.name) );
    //             let value;

    //             if ( prop ) {
    //                 value = prop.value;
    //                 if ( DataType.Boolean === prop.type && null != value ) {
    //                     value = Boolean(value);
    //                 }
    //             }

    //             props.push({
    //                 name: item.name,
    //                 value,
    //             });
    //         }

    //         if ( props && props.length ) {
    //             return JSON.stringify(props);
    //         }
    //     }

    //     return ResultType.StringEmpty;
    // }

    // public setTableCustomProps(sJson: any): boolean {
    //     const props = [];
    //     for (let index = 0, length = sJson.length; index < length; index++) {
    //         const prop = sJson[index];
    //         if ( prop ) {
    //             let value = prop.value;
    //             const typeV = (null != value ? typeof value : undefined);
    //             let type;

    //             switch (typeV) {
    //                 case 'number':
    //                     type = DataType.Number;
    //                     break;

    //                 case 'boolean':
    //                     type = DataType.Boolean;
    //                     value = value.toString();
    //                     break;

    //                 case 'string':
    //                 default:
    //                     type = DataType.String;
    //                     break;
    //             }
    //             props.push({
    //                 name: prop.name,
    //                 value,
    //                 type,
    //             });
    //         }
    //     }

    //     if ( props.length ) {
    //         this.property.addCustomProps(props);
    //         return true;
    //     }

    //     return false;
    // }

    // public setTableName(name: string): void {
    //     const tableManager = this.getTableManager();

    //     if ( tableManager && tableManager.checkTableName(name)) {
    //         // tableManager.removeTableName(this.tableName);
    //         tableManager.setTableName(this, name);
    //         this.tableName = name;
    //     }
    // }

    // public getTableName(): string {
    //     return this.tableName;
    // }

    // public getTableManager(): TableManager {
    //     if ( this.logicDocument ) {
    //         return this.logicDocument.getTableManager();
    //     }

    //     return null;
    // }

    // public isReadOnly(bOnlySelectCells?: boolean): boolean {
    //     if ( this.isAdminMode() ) {
    //         return false;
    //     }

    //     return ( this.property.isTableReadOnlyProtect() || this.isCanntDelete()
    //             || ( !this.isSelectedAll() && false === this.canDeleteRow(bOnlySelectCells)) );
    // }

    // public isNewControlReadOnly(): boolean {
    //     if ( this.isAdminMode() ) {
    //         return false;
    //     }

    //     if ( this.property.isTableReadOnlyProtect() ) {
    //         return true;
    //     }

    //     if ( this.isTableCellProtected() ) {
    //         return true;
    //     }

    //     return false;
    // }

    // public canInput(): boolean {
    //     if ( this.isAdminMode() ) {
    //         return true;
    //     }

    //     if ( this.property.isTableReadOnlyProtect() ) {
    //         // alert('表格设置了只读保护，无法输入');
    //         gEvent.setEvent(this.logicDocument.id, gEventName.MessageEvent, this, MessageType.TableProtected);
    //         // message.error('表格设置了只读保护，无法输入');
    //         return false;
    //     }

    //     if ( this.isTableCellProtected() ) {
    //         // alert('表格单元格设置了保护，无法输入');
    //         gEvent.setEvent(this.logicDocument.id, gEventName.MessageEvent, this, MessageType.CellProtected);
    //         // message.error('表格单元格设置了保护，无法输入');
    //         return false;
    //     }

    //     return true;
    // }

    // public canDeleteSelf(): boolean {
    //     if (this.isAdminMode()) {
    //         return true;
    //     }
    //     if (true === this.isDeleteProtect() ||
    //         false === this.property.isTableCanDeleteRow()) {
    //         return false;
    //     }
    //     // cell can delete
    //     for (const row of this.content) {
    //         for (const col of row.content) {
    //             if (true === col.isCellProtected()) {
    //                 return false;
    //             }
    //             // let result = true;
    //             // const content = col.content;
    //             // content.setApplyToAll(true);
    //             // const startContentPos = content.getCurContentPosInDoc(true, true, true);
    //             // const endContentPos = content.getCurContentPosInDoc(true, false, true);
    //             // if (false === this.isValidDelete(startContentPos, endContentPos)) {
    //             //     result = false;
    //             // }
    //             // content.setApplyToAll(false);
    //             // if (!result) {
    //             //     return false;
    //             // }
    //         }
    //     }
    //     return true;
    // }

    // public canDelete(): boolean {
    //     if ( this.isAdminMode() ) {
    //         return true;
    //     }

    //     if ( true === this.isSelectedAll() && true === this.isDeleteProtect() ) {
    //         // alert('表格设置了删除保护，无法删除');
    //         gEvent.setEvent(this.logicDocument.id, gEventName.MessageEvent, this, MessageType.TableDeleted);
    //         // message.error('表格设置了删除保护，无法删除');
    //         return false;
    //     }

    //     if ( true === this.isTableCellProtected() ) {
    //         // alert('表格单元格设置了保护，无法删除');
    //         gEvent.setEvent(this.logicDocument.id, gEventName.MessageEvent, this, MessageType.CellProtected);
    //         // message.error('表格单元格设置了保护，无法删除');
    //         return false;
    //     }

    //     if ( true === this.isSelectionUse() && TableSelectionType.TableCell === this.selection.type
    //         && false === this.isSelectedAll() && false === this.canDeleteRow() ) {
    //         // alert('表格设置了行删除保护，无法删除');
    //         gEvent.setEvent(this.logicDocument.id, gEventName.MessageEvent, this, MessageType.RowDeleted);
    //         // message.error('表格设置了行删除保护，无法删除');
    //         return false;
    //     }

    //     if ( this.isReadOnlyProtect() && ( false === this.isSelectionUse() ||
    //             ( this.isSelectionUse() && TableSelectionType.Text === this.selection.type)) ) {
    //         // alert('表格设置了只读保护，无法删除');
    //         // message.error('表格设置了只读保护，无法删除');
    //         gEvent.setEvent(this.logicDocument.id, gEventName.MessageEvent, this, MessageType.CellProtected);
    //         return false;
    //     }

    //     // if ( this.logicDocument.isTrackRevisions() && this.isSelectionUse() ) {
    //         // if ( this.bApplyToAll || TableSelectionType.TableCell === this.selection.type ) {
    //     if ( ( this.isSelectionUse() && TableSelectionType.TableCell === this.selection.type)
    //         || this.bApplyToAll ) {
    //             const selectedCellsArray = this.getSelectionArray();

    //             for (const pos of selectedCellsArray) {
    //                 const row = this.content[pos.rowIndex];
    //                 const cell = row ? row.getCell(pos.cellIndex) : null;
    //                 if ( cell ) {
    //                     cell.content.setApplyToAll(true);
    //                     if ( !cell.content.canDelete() ) {
    //                         cell.content.setApplyToAll(false);
    //                         return false;
    //                     }
    //                     cell.content.setApplyToAll(false);
    //                 }
    //             }
    //     } else if ( this.isSelectionUse() ) {
    //         if ( this.curCell && !this.curCell.content.canDelete() ) {
    //             return false;
    //         }
    //     }

    //     return true;
    // }

    // public canInsertNewControl(): boolean {
    //     if ( this.isAdminMode() ) {
    //         return true;
    //     }

    //     return true;
    // }

    // public isAdminMode(): boolean {
    //     if ( this.logicDocument ) {
    //         return this.logicDocument.isAdminMode();
    //     }

    //     return false;
    // }

    // public isReadOnlyProtect(): boolean {
    //     return this.property.isTableReadOnlyProtect();
    // }

    // public setTableReadOnly(bReadOnly: boolean): void {
    //     this.property.setTableReadOnly(bReadOnly, this.getHistory());
    // }

    public isInRegionReadOnly(): boolean {
        const parent = this.parent;

        while ( parent && parent instanceof DocumentContent ) {
            if ( parent.parent && parent.parent instanceof Region ) {
                return parent.parent.isReadOnly();
            }
            break;
        }

        return false;
    }

    // public getCustomProps(): Map<string, string> {
    //     return this.customProps;
    // }

    // public setCustomProp(key: string, value: string): void {
    //     if ( null == this.customProps ) {
    //         this.customProps = new Map<string, string>();
    //     }

    //     this.customProps.set(key, value);
    // }

    // public addCustomPropItem(key: string, value: string): number {
    //     return this.property.addCustomPropItem(key, value);
    // }

    // public addCustomProps(props: ICustomProps[]): void {
    //     this.property.addCustomProps(props);
    // }

    // public getCustomByPropName(name: string): string {
    //     return this.property.getCustomByPropName(name);
    // }

    // public getCustomProps(): ICustomProps[] {
    //     return this.property.getCustomProps();
    // }

    // public isDeleteProtect(): boolean {
    //     return this.property.isDeleteProtect();
    // }

    // public isCanntDelete(): boolean {
    //     return ((this.isSelectedAll() && this.property.isDeleteProtect())
    //             || this.isTableCellProtected());
    // }

    // public setDeleteProtect(bDeleteProtect: boolean): void {
    //     this.property.setDeleteProtect(bDeleteProtect, this.getHistory());
    // }

    // public setCanAddRow(bCanAddRow: boolean): void {
    //     this.property.setTableCanAddRow(bCanAddRow, this.getHistory());
    // }

    // public canAddRow(): boolean {
    //     return this.property.isTableCanAddRow();
    // }

    // public isTableCanDeleteRow(): boolean {
    //     return this.property.isTableCanDeleteRow();
    // }

    // public setCanDeleteRow(bCanAddColumn: boolean): void {
    //     this.property.setTableCanDeleteRow(bCanAddColumn, this.getHistory());
    // }

    // public canDeleteRow(bOnlySelectCells?: boolean): boolean {
    //     if ( !bOnlySelectCells ) {
    //         if ( false === this.property.isTableCanDeleteRow() ) {
    //             return false;
    //         }

    //         if ( this.selection.bUse && TableSelectionType.TableCell === this.selection.type ) {
    //             const deleteRows: TableRow[] = [];

    //             if ( this.selection.data ) {
    //                 this.selection.data.forEach((pos) => {
    //                     const row = this.content[pos.rowIndex];
    //                     if ( -1 === deleteRows.indexOf(row) ) {
    //                         deleteRows.push(row);
    //                     }
    //                 });
    //             }

    //             if ( this.property.isDeleteProtect() && deleteRows.length === this.content.length ) {
    //                 return false;
    //             }

    //             let result = true;
    //             for (let index = 0; index < deleteRows.length; index++) {
    //                 const row = deleteRows[index];

    //                 for (let cellIndex = 0, count = row.getCellsCount(); cellIndex < count; cellIndex++) {
    //                     const cell = row.getCell(index);
    //                     if ( cell && cell.isCellProtected() ) {
    //                         return false;
    //                     }

    //                     if ( cell && cell.content ) {
    //                         const content = cell.content;
    //                         content.setApplyToAll(true);
    //                         const startContentPos = content.getCurContentPosInDoc(true, true, true);
    //                         const endContentPos = content.getCurContentPosInDoc(true, false, true);
    //                         if ( false === this.isValidDelete(startContentPos, endContentPos) ) {
    //                             result = false;
    //                         }
    //                         content.setApplyToAll(false);

    //                         if ( false === result ) {
    //                             return result;
    //                         }
    //                     }
    //                 }
    //             }
    //         } else {
    //             if (this.curCell == null) {
    //                 return true;
    //             }

    //             const row = this.curCell.row;

    //             let result = true;
    //             for (let index = 0, count = row.getCellsCount(); index < count; index++) {
    //                 const cell = row.getCell(index);
    //                 if ( cell && cell.isCellProtected() ) {
    //                     return false;
    //                 }

    //                 if ( cell && cell.content ) {
    //                     const content = cell.content;
    //                     content.setApplyToAll(true);
    //                     const startContentPos = content.getCurContentPosInDoc(true, true, true);
    //                     const endContentPos = content.getCurContentPosInDoc(true, false, true);
    //                     if ( false === this.isValidDelete(startContentPos, endContentPos) ) {
    //                         result = false;
    //                     }
    //                     content.setApplyToAll(false);

    //                     if ( false === result ) {
    //                         return result;
    //                     }
    //                 }
    //             }
    //         }

    //         return true;
    //     } else {
    //         return !this.isTableCellProtected();
    //     }
    // }

    // public canDeleteCol(): boolean {
    //     if ( this.selection.bUse && TableSelectionType.TableCell === this.selection.type ) {
    //         const deleteCols: number[] = [];

    //         if ( this.selection.data ) {
    //             this.selection.data.forEach((pos) => {
    //                 deleteCols.push(pos.cellIndex);
    //             });
    //         }

    //         let result = true;
    //         for (let index = 0, count = this.content.length; index < count; index++) {
    //             const row = this.content[index];

    //             // tslint:disable-next-line: prefer-for-of
    //             for (let cellIndex = 0; cellIndex < deleteCols.length; cellIndex++) {
    //                 const cell = row.getCell(deleteCols[cellIndex]);

    //                 if ( cell && cell.isCellProtected() ) {
    //                     return false;
    //                 }

    //                 if ( cell && cell.content ) {
    //                     const content = cell.content;
    //                     content.setApplyToAll(true);
    //                     const startContentPos = content.getCurContentPosInDoc(true, true, true);
    //                     const endContentPos = content.getCurContentPosInDoc(true, false, true);
    //                     if ( false === this.isValidDelete(startContentPos, endContentPos) ) {
    //                         result = false;
    //                     }
    //                     content.setApplyToAll(false);

    //                     if ( false === result ) {
    //                         return result;
    //                     }
    //                 }
    //             }
    //         }

    //         return result;
    //     } else {
    //         let result = true;
    //         for (let index = 0, count = this.content.length; index < count; index++) {
    //             const row = this.content[index];
    //             const cell = row.getCell(this.curCell.index);

    //             if ( cell && cell.isCellProtected() ) {
    //                 return false;
    //             }

    //             if ( cell && cell.content ) {
    //                 const content = cell.content;
    //                 content.setApplyToAll(true);
    //                 const startContentPos = content.getCurContentPosInDoc(true, true, true);
    //                 const endContentPos = content.getCurContentPosInDoc(true, false, true);
    //                 if ( false === this.isValidDelete(startContentPos, endContentPos) ) {
    //                     result = false;
    //                 }
    //                 content.setApplyToAll(false);

    //                 if ( false === result ) {
    //                     return result;
    //                 }
    //             }
    //         }

    //         return true;
    //     }
    // }

    // public setFixedRowHeight(bFixedRowHeight: boolean): boolean {
    //     let result = false;
    //     this.content.forEach((row) => {
    //         const rowHeight = row.getHeight();
    //         if ( bFixedRowHeight ) {
    //             if ( rowHeight.isFixed() ) {
    //                 return;
    //             }
    //             result = row.setHeight(row.getCurrentRowHeight(), TableRowLineRule.Exact) || result;
    //         } else {
    //             result = row.setHeight(0, TableRowLineRule.Auto) || result;
    //         }
    //     });

    //     this.property.setFixedRowHeight(bFixedRowHeight, this.getHistory());
    //     return result;
    // }

    // public isFixedRowHeight(): boolean {
    //     return this.property.isFixedRowHeight();
    // }

    // public setFixedColWidth(bFixedColWidth: boolean): void {
    //     this.property.setFixedColWidth(bFixedColWidth, this.getHistory());
    // }

    public setEnableRowAction(type: EnableRowActionType): number {
        if (type != null) {
            if (this.property.getEnableRowAction() !== type) {
                // 存在多行时才能进行设置
                const res = this.hideFirstRow(type === EnableRowActionType.Enable);
                if (res === ResultType.Success) {
                    return this.property.setEnableRowAction(type);
                }
            }
        }
        return ResultType.UnEdited;
    }

    public setEnableRowAction2(type: EnableRowActionType): void {
        if (![EnableRowActionType.Enable, EnableRowActionType.Unable].includes(type)) {
            return;
        }
        this.property.setEnableRowAction(type);
    }

    public setEnableRowNum(): void {
        if (this.property.getEnableRowAction() !== EnableRowActionType.Enable) {
            return;
        }
        let index = 0;
        const contents = this.content;
        const len = contents.length;
        for (; index < len; index++) {
            const row = contents[index];
            if (row.isTableHeader()) {
                continue;
            }
            break;
        }

        this.property.setEnableRowNum(index);
    }

    public getEnableRowAction(): EnableRowActionType {
        return this.property.getEnableRowAction();
    }

    // public isFixedColWidth(): boolean {
    //     return this.property.isFixedColWidth();
    // }

    // public isInFixedHeightRow(): boolean {
    //     if ( (this.selection && TableSelectionType.TableCell === this.selection.type) ||
    //         this.bApplyToAll ) {
    //         return false;
    //     }

    //     if ( this.curCell && this.curCell.row ) {
    //         const row = this.curCell.row;
    //         return row.isFixed();
    //     }

    //     return false;
    // }

    // public getBorderLineSize(): number {
    //     const row = this.content[0];
    //     const cell = row ? row.getCell(0) : null;

    //     if ( cell ) {
    //         const cellBodersInfo = cell.getBorderInfo();
    //         const borderInfoLeft = cellBodersInfo.left;
    //         const borderInfoRight = cellBodersInfo.right;
    //         const borderInfoTop = cellBodersInfo.top;
    //         const borderInfoBottom = cellBodersInfo.bottom;
    //         if ( borderInfoLeft && borderInfoLeft[0] ) {
    //             return borderInfoLeft[0].size;
    //         } else if ( borderInfoRight && borderInfoRight[0]) {
    //             return borderInfoRight[0].size;
    //         } else if ( borderInfoTop && borderInfoTop[0] ) {
    //             return borderInfoTop[0].size;
    //         } else if ( borderInfoBottom && borderInfoBottom[0]) {
    //             return borderInfoBottom[0].size;
    //         }
    //     }

    //     return 0;
    // }

    // public getBorderLineColor(): string {
    //     const row = this.content[0];
    //     const cell = row ? row.getCell(0) : null;

    //     if ( cell ) {
    //         const cellBodersInfo = cell.getBorderInfo();
    //         const borderInfoLeft = cellBodersInfo.left;
    //         const borderInfoRight = cellBodersInfo.right;
    //         const borderInfoTop = cellBodersInfo.top;
    //         const borderInfoBottom = cellBodersInfo.bottom;
    //         if ( borderInfoLeft && borderInfoLeft[0] ) {
    //             return borderInfoLeft[0].color.toHex();
    //         } else if ( borderInfoRight && borderInfoRight[0]) {
    //             return borderInfoRight[0].color.toHex();
    //         } else if ( borderInfoTop && borderInfoTop[0] ) {
    //             return borderInfoTop[0].color.toHex();
    //         } else if ( borderInfoBottom && borderInfoBottom[0]) {
    //             return borderInfoBottom[0].color.toHex();
    //         }
    //     }

    //     return '#000000';
    // }

    // public getLastRowBorderSize(): number {
    //     let size = 0;
    //     const row = this.content[this.content.length - 1];
    //     for (let index = 0, count = row.getCellsCount(); index < count; index++) {
    //         const cell = row.getCell(index);
    //         const border = cell.getBorder(2);

    //         if ( border && border.size > size) {
    //             size = border.size;
    //         }
    //     }

    //     return size;
    // }

    // public getDrawingObjects(): GraphicObjects {
    //     return this.parent ? this.parent.getDrawingObjects() : null;
    // }

    // public setDrawingObjects(drawingObjects: GraphicObjects): void {
    //     if ( this.parent ) {
    //         this.parent.setDrawingObjects(drawingObjects);
    //     }
    // }

    // /**
    //  * 获取table行数
    //  */
    // public getRowsCount(): number {
    //     return this.content.length;
    // }

    // public getTableRowsHeaderCount(): number {
    //     let nHeaderCount = 0;

    //     for (let index = 0, count = this.content.length; index < count; index++) {
    //         if ( true === this.content[index].isTableHeader()) {
    //             nHeaderCount++;
    //         } else {
    //             break;
    //         }
    //     }

    //     return nHeaderCount;
    // }

    // public setTableRowsHeaderCount(nHeaderCount: number): boolean {
    //     if ( 0 > nHeaderCount ) {
    //         return false;
    //     }

    //     for (let index = 0; index < nHeaderCount; index++) {
    //         this.content[index].setTableHeader(true);
    //     }

    //     for (let index = nHeaderCount; index < this.content.length; index++) {
    //         this.content[index].setTableHeader(false);
    //     }
    //     return true;
    // }

    // public updateCursorType(pointX: number, pointY: number, curPage: number): void {
    //     if ( 0 > curPage || curPage >= this.pages.length ) {
    //         curPage = 0;
    //     }

    //     if ( true === this.selection.bStart || TableSelectionType.TableBorder === this.selection.type2 ) {
    //         return ;
    //     }

    //     const bFixedRowHeight = this.isFixedRowHeight();
    //     const bFixedColWidth = this.isFixedColWidth();

    //     if ( bFixedRowHeight && bFixedColWidth ) {
    //         return ;
    //     }

    //     const hitInfo = this.checkHitInBorder(pointX, pointY, curPage);
    //     const pos = hitInfo.pos;

    //     if ( ( 3 === hitInfo.border && 0 === pos.cellIndex )
    //         || ( 1 === hitInfo.border && this.content[pos.rowIndex].getCellsCount() === pos.cellIndex + 1 ) ) {
    //         if ( CursorType.ColResize === this.getCursorType() || CursorType.RowResize === this.getCursorType() ) {
    //             this.setCursorType(CursorType.Text);
    //             this.selection.bStart = false;
    //             this.selection.type2 = TableSelectionType.Common;
    //         }
    //         return ;
    //     }

    //     if ( -1 !== hitInfo.border ) {
    //         switch (hitInfo.border) {
    //             case 0:
    //             case 2:
    //                 if ( false === bFixedRowHeight ) {
    //                     this.setCursorType(CursorType.RowResize);
    //                 }
    //                 return;

    //             case 1:
    //             case 3:
    //                 if ( false === bFixedColWidth ) {
    //                     this.setCursorType(CursorType.ColResize);
    //                 }
    //                 return;

    //             default:
    //                 break;
    //         }
    //     }

    //     const cellPos = this.getCellByXY(pointX, pointY, curPage);
    //     const cell = this.content[cellPos.rowIndex].getCell(cellPos.cellIndex);
    //     cell.updateCursorType(pointX, pointY, curPage - cell.content.getRelativeStartPage());
    // }

    // public setCursorType(type: CursorType): void {
    //     this.logicDocument.setCursorType(type);
    // }

    // public getCursorType(): CursorType {
    //     return this.logicDocument.getCursorType();
    // }

    // public getHistory(): History {
    //     if ( this.getParent() ) {
    //         return this.getParent()
    //                     .getHistory();
    //     }

    //     return null;
    // }

    // public addContentChanges( changes: ContentChangesElement ): void {
    //     this.contentChanges.add(changes);
    // }

    // public getTableCellName(rowIndex?: number, colIndex?: number): string {
    //     if ( (this.selection.bUse && TableSelectionType.TableCell === this.selection.type)
    //         && (null == rowIndex || null == colIndex) ) {
    //         return undefined;
    //     }

    //     rowIndex = (null == rowIndex ? this.curCell.row.index : rowIndex);
    //     colIndex = (null == colIndex ? this.curCell.index : colIndex);
    //     return this.getCellName(colIndex, rowIndex);

    //     // if ( this.getCellByName(name) ) {
    //     //     const cell = this.getCellByName(name);
    //     //     console.log(this.getCellName(cell.index, cell.row.index))
    //     // }
    //     // return name;
    // }

    // public getCurrentCell(rowIndex?: number, colIndex?: number): TableCell {
    //     return this.curCell;
    // }

    // public getTableCellByName(name: string): TableCell {
    //     if ( null == name || 1 >= name.length ) {
    //         return null;
    //     }

    //     return this.getCellByName(name);
    // }

    // public getCellByName(name: string): TableCell {
    //     // const sRow = name.replace(/[^0-9]+/g, '');
    //     // const sCol = name.replace(/[^A-Z]+/g, '');

    //     const rowIndex = this.getRowIndexByName(name); // parseInt(sRow, 0);

    //     if ( isNaN(rowIndex) || 0 > rowIndex || this.content.length <= rowIndex ) {
    //         return null;
    //     }

    //     const row = this.content[rowIndex];

    //     if ( null == row ) {
    //         return null;
    //     }

    //     const colIndex = this.getColIndexByName(name);

    //     if ( 0 > colIndex ) {
    //         return null;
    //     }

    //     return row.getCell(colIndex);
    // }

    // /**
    //  * 获取指定列的所有单元格
    //  * @param colIndex 表格列索引
    //  * @returns 单元格集合
    //  */
    // public getCellsByColIndex(colIndex: number): TableCell[] {
    //     const cells: TableCell[] = [];
    //     for (const row of this.content) {
    //         cells.push(row.getCell(colIndex));
    //     }
    //     return cells;
    // }

    // public isTableCellProtected(): boolean {
    //     const selectedCellsArray = this.getSelectionArray();

    //     if ( selectedCellsArray && 0 !== selectedCellsArray.length ) {
    //         for (const pos of selectedCellsArray) {
    //             const row = this.content[pos.rowIndex];
    //             const cell = row ? row.getCell(pos.cellIndex) : null;
    //             if ( cell && cell.isCellProtected() ) {
    //                 return true;
    //             }
    //         }
    //     }

    //     return false;
    // }

    // public setTableCellProtected(bProtected: boolean): void {
    //     const selectedCellsArray = this.getSelectionArray();
    //     if ( selectedCellsArray && 0 !== selectedCellsArray.length ) {
    //         const history = this.getHistory();
    //         // history.createNewHistoryPoint(HistoryDescriptionType.TableCellProtected);
    //         this.logicDocument.startAction(HistoryDescriptionType.TableCellProtected);

    //         for (const pos of selectedCellsArray) {
    //             const row = this.content[pos.rowIndex];
    //             const cell = row ? row.getCell(pos.cellIndex) : null;
    //             if ( cell ) {
    //                 cell.setCellProtected(bProtected, history);
    //             }
    //         }

    //         this.logicDocument.endAction();
    //     }
    // }

    // public setTableCellsVertAlign(type: VertAlignType): boolean {
    //     if ( null == type || type > VertAlignType.Bottom || type < VertAlignType.Top ) {
    //         return false;
    //     }

    //     let result = false;
    //     const cells = this.getSelectionArray();
    //     cells.forEach((pos) => {
    //         const row = this.content[pos.rowIndex];
    //         const cell = row.getCell(pos.cellIndex);

    //         if ( cell ) {
    //             result = cell.setVertAlign(type) || result;
    //         }
    //     });

    //     if ( result ) {
    //         for (let index = 0, length = this.pages.length; index < length; index++) {
    //             this.recalculatePage(index);
    //         }
    //     }

    //     return result;
    // }

    // public getTableCellsVertAlign(): VertAlignType {
    //     let type;
    //     const cells = this.getSelectionArray();

    //     for (let index = 0, length = cells.length; index < length; index++) {
    //         const pos = cells[0];
    //         const row = this.content[pos.rowIndex];
    //         const cell = row.getCell(pos.cellIndex);

    //         if ( cell ) {
    //             type = cell.getVertAlign();
    //             break;
    //         }
    //     }

    //     return type;
    // }

    public checkSelectTableCellBorder(borderType: string): boolean {
        if ( TableBorderSettingType[0] === borderType || TableBorderSettingType[1] === borderType ) {
            return true;
        }

        const bSelect = this.isSelectionUse() || this.bApplyToAll;
        const selectedCellsArray = this.getSelectionArray();

        if ( ( false === bSelect || 1 === selectedCellsArray.length )
            && ( TableBorderSettingType[2] === borderType || TableBorderSettingType[5] === borderType
                || TableBorderSettingType[8] === borderType ) ) {
                return true;
        }

        switch (borderType) {
            // 内
            case TableBorderSettingType[2]:
                return this.checkTableBorderInner(selectedCellsArray);

            // 垂直
            case TableBorderSettingType[5]:
                return this.checkTableBorderV(selectedCellsArray);

            // 水平
            case TableBorderSettingType[8]:
                return this.checkTableBorderH(selectedCellsArray);

            // 外左
            case TableBorderSettingType[4]:
                return this.checkTableBorderLeft(selectedCellsArray);

            // 外右
            case TableBorderSettingType[6]:
                return this.checkTableBorderRight(selectedCellsArray);

            // 上
            case TableBorderSettingType[7]:
                return this.checkTableBorderTop(selectedCellsArray);

            // 下
            case TableBorderSettingType[9]:
                return this.checkTableBorderBottom(selectedCellsArray);

            // 外
            case TableBorderSettingType[3]:
                return this.checkTableBorderOutter(selectedCellsArray);
            default:
                break;
        }

        return true;
    }

    // public getTopDocument(): DocumentContentBase {
    //     return this.parent ? this.parent.getTopDocument() : null;
    // }

    // public getFocusRevision(pointX: number, pointY: number, pageIndex: number): IRevisionChange[] {
    //     const pos = this.getCellByXY(pointX, pointY, pageIndex);
    //     const row = this.content[pos.rowIndex];
    //     const cell = row.getCell(pos.cellIndex);
    //     return cell.content.getFocusRevision(pointX, pointY, pageIndex);
    // }

    // public moveCursorToCell(): void {
    //     if ( this.selection.bUse && TableSelectionType.TableCell === this.selection.type ) {
    //         const pos = this.selection.data[0];

    //         this.selection.type = TableSelectionType.Common;
    //         this.curCell = this.content[pos.rowIndex].getCell(pos.cellIndex);
    //         this.curCell.content.removeSelection();
    //     } else {
    //         const curCell = this.curCell;
    //         const pos = { rowIndex: curCell.row.index, cellIndex: curCell.index };
    //         let nextCell = this.getNextCell(pos);
    //         while ( null != nextCell && VerticalMergeType.Restart !== nextCell.getVMerge() ) {
    //             nextCell = this.getNextCell(pos);
    //         }

    //         if ( null != nextCell ) {
    //             this.curCell.content.removeSelection();

    //             this.curCell = nextCell;
    //         } else {
    //             if ( !( curCell.isCellProtected() || false === this.canAddRow() )) {
    //                 this.logicDocument.startAction(HistoryDescriptionType.DocumentAddTableRow);
    //                 this.addTableRow(false);

    //                 this.parent.recalculate();
    //                 this.logicDocument.endAction();

    //                 let nextTempCell = this.getNextCell(pos);
    //                 while ( null != nextTempCell && VerticalMergeType.Restart !== nextTempCell.getVMerge() ) {
    //                     nextTempCell = this.getNextCell(pos);
    //                 }

    //                 if ( null != nextTempCell ) {
    //                     this.curCell = nextTempCell;
    //                 }
    //             }
    //         }

    //         this.curCell.content.moveCursorToEndPos();
    //         this.selection.bUse = false;
    //         this.selection.type = TableSelectionType.Text;
    //         this.selection.curRow = this.curCell.row.index;
    //         this.selection.data = [];
    //     }
    // }

    // public isCursorAtBegin(): boolean {
    //     const curCell = this.curCell;
    //     if ( false === this.selection.bUse && ( 0 === curCell.row.index && 0 === curCell.index) ) {
    //         return curCell.content.isCursorAtBegin();
    //     }

    //     return false;
    // }

    // public isCursorAtEnd(): boolean {
    //     const curCell = this.curCell;
    //     const lastRow = this.content[this.content.length - 1];
    //     if ( false === this.selection.bUse
    //         && ( lastRow.index === curCell.row.index && lastRow.getCellsCount() - 1 === curCell.index) ) {
    //         return curCell.content.isCursorAtEnd();
    //     }

    //     return false;
    // }

    // public getCurrentTable(): Table { return this; }

    // public isInHeader(): boolean {
    //     const topDoc = this.getTopDocument();

    //     if ( topDoc && topDoc instanceof DocumentContent ) {
    //         const parent = topDoc.parent;
    //         if ( parent && parent instanceof HeaderFooter && parent.isHeader() ) {
    //             return true;
    //         }
    //     }

    //     return false;
    // }

    // public isInFooter(): boolean {
    //     const topDoc = this.getTopDocument();

    //     if ( topDoc && topDoc instanceof DocumentContent ) {
    //         const parent = topDoc.parent;
    //         if ( parent && parent instanceof HeaderFooter && parent.isFooter() ) {
    //             return true;
    //         }
    //     }

    //     return false;
    // }

    // public getReviewType(): ReviewType {
    //     return this.reviewType;
    // }

    // public setReviewType(type: ReviewType): void {
    //     if ( type !== this.reviewType) {
    //         const oldReviewType = this.reviewType;
    //         const oldReviewInfo = this.reviewInfo.copy();

    //         this.reviewType = type;
    //         this.reviewInfo.update();

    //         const history = this.getHistory();
    //         history.addChange(new ChangeTableReviewType(this,
    //             { reviewType: oldReviewType,
    //               reviewInfo: oldReviewInfo},
    //             { reviewType: this.reviewType,
    //               reviewInfo: this.reviewInfo.copy()}));

    //         this.updateTrackRevisions();
    //     }
    // }

    // public getReviewInfo(): ReviewInfo {
    //     return this.reviewInfo;
    // }

    // public setReviewTypeWithInfo(reviewType: ReviewType, reviewInfo: ReviewInfo): void {
    //     const history = this.getHistory();
    //     history.addChange(new ChangeTableReviewType(this,
    //         { reviewType: this.reviewType,
    //           reviewInfo: this.reviewInfo ? this.reviewInfo.copy() : undefined},
    //         { reviewType,
    //           reviewInfo: reviewInfo ? reviewInfo.copy() : undefined}));

    //     this.reviewInfo = reviewInfo;
    //     this.reviewType = reviewType;

    //     this.updateTrackRevisions();
    // }

    // public updateTrackRevisions(): void {
    //     if ( this.parent && this.parent.getRevisionsManager() && this.parent.isTrackRevisions() ) {
    //         const revisionManager = this.parent.getRevisionsManager();
    //         revisionManager.checkElement(this);
    //     }
    // }

    // public checkRevisionsChanges(): void {
    //     const revisionManager = this.logicDocument.getRevisionsManager();

    //     if ( revisionManager && ReviewType.Common !== this.getReviewType() ) {
    //         if ( ReviewType.Add === this.getReviewType()) {
    //             this.content.content.forEach((item) => {
    //                 item.checkRevisionsChanges();
    //             });
    //         }
    //     }
    // }

    // public acceptRevisionChanges(bAll: boolean, type?: RevisionChangeType): void {
    //     if ( bAll ) {
    //         for (let index = 0, count = this.content.length; index < count; index++) {
    //             const row = this.content[index];
    //             const cellsCount = row ? row.getCellsCount() : 0;

    //             for (let cellIndex = 0; cellIndex < cellsCount; cellIndex++) {
    //                 const cell = row.getCell(cellIndex);

    //                 if ( cell && cell.content ) {
    //                     cell.content.acceptRevisions(bAll);
    //                 }
    //             }
    //         }
    //     }
    // }

    // public rejectRevisionChanges(bAll: boolean, type?: RevisionChangeType): void {
    //     if ( bAll ) {
    //         for (let index = 0, count = this.content.length; index < count; index++) {
    //             const row = this.content[index];
    //             const cellsCount = row ? row.getCellsCount() : 0;

    //             for (let cellIndex = 0; cellIndex < cellsCount; cellIndex++) {
    //                 const cell = row.getCell(cellIndex);

    //                 if ( cell && cell.content ) {
    //                     cell.content.rejectRevisions(bAll);
    //                 }
    //             }
    //         }
    //     }
    // }

    // /**
    //  * 清除修订信息
    //  */
    // public resetRevisions(): void {
    //     this.content?.forEach((row) => {
    //         row?.content.forEach((cell) => {
    //             cell?.content?.resetRevisions();
    //         });
    //     });
    // }

    // public getElementById(elementId: number): DocumentContentElementBase {
    //     let element = null;
    //     for (let index = 0, length = this.content.length; index < length; index++) {
    //         const row = this.content[index];
    //         const cellsCount = row.getCellsCount();

    //         for (let cellIndex = 0; cellIndex < cellsCount; cellIndex++) {
    //             const cell = row.getCell(cellIndex);

    //             if ( cell && cell.content ) {
    //                 element = cell.content.getElementById(elementId);

    //                 if ( element ) {
    //                     return element;
    //                 }
    //             }
    //         }
    //     }

    //     return element;
    // }

    // public getParaContentByXY(mouseEvent: MouseEventHandler, pageIndex: number): ParaPortion {
    //     const cellObj = this.getCellByXY(mouseEvent.pointX, mouseEvent.pointY, pageIndex);
    //     if (!cellObj || cellObj.cellIndex === undefined) {
    //         return;
    //     }

    //     return this.content[cellObj.rowIndex]?.content[cellObj.cellIndex]?.content
    //     .getParaContentByXY(mouseEvent, pageIndex);
    // }

    // public getAllNewControls(): NewControl[] {
    //     const newControls = [];
    //     this.setApplyToAll(true); // temporary
    //     const startPos = this.getCurContentPosInDoc(true, true);
    //     const endPos = this.getCurContentPosInDoc(true);
    //     this.setApplyToAll(false);
    //     const newControlManager = this.logicDocument.getNewControlManager();
    //     if ( newControlManager ) {
    //         const controlNames = this.getNewControlNamesByPos(startPos, endPos);
    //         for (const controlName of controlNames) {
    //             newControls.push(newControlManager.getNewControlByName(controlName));
    //         }
    //     }
    //     // console.log(newControls)

    //     return newControls;
    // }

    // public getContentByPos(pos: any): any {
    //     let index = pos.shift();
    //     const row = this.content[index];
    //     if ( row ) {
    //         index = pos.shift();
    //         const cell = row.getCell(index);

    //         if ( cell ) {
    //             return cell.content.getContentByPos(pos);
    //         }
    //     }

    //     return null;
    // }

    // public getCellsVertAlign(): any {
    //     let cellsVertAlign = null;
    //     if ( true === this.selection.bUse && TableSelectionType.TableCell === this.selection.type
    //         && 0 < this.selection.data.length ) {
    //         for (let index = 0, length = this.selection.data.length; index < length; index++) {
    //             const pos = this.selection.data[index];
    //             const row = this.content[pos.rowIndex];
    //             const cell = row ? row.getCell(pos.cellIndex) : null;

    //             if ( cell ) {
    //                 if ( 0 === index ) {
    //                     cellsVertAlign = cell.getVertAlign();
    //                 } else {
    //                     if ( cell.getVertAlign() !== cellsVertAlign ) {
    //                         cellsVertAlign = null;

    //                         return cellsVertAlign;
    //                     }
    //                 }
    //             }
    //         }
    //     } else {
    //         if ( this.curCell ) {
    //             return this.curCell.getVertAlign();
    //         }
    //     }

    //     return cellsVertAlign;
    // }

    // public getColIndexByName(name: string): number {
    //     const sCol = name.replace(/[^A-Z]+/g, '');

    //     const length = sCol.length;
    //     const sLetters = 'ZABCDEFGHIJKLMNOPQRSTUVWXY';
    //     let colIndex = sLetters.indexOf(sCol[length - 1]) - 1;
    //     // colIndex += sLetters.indexOf(sCol[1]);

    //     for (let index = length - 2; index >= 0; index--) {
    //         const element = sCol[index];
    //         colIndex += sLetters.indexOf(element) * 26;
    //     }

    //     return colIndex;
    // }

    // public isValidTableCell(cellName?: string): boolean {
    //     return true;
    // }

    public isCellsInRowOrCol(names: string[]): boolean {
        if ( names && 2 === names.length ) {
            const rowIndex1 = this.getRowIndexByName(names[0]);
            const colIndex1 = this.getColIndexByName(names[0]);
            const rowIndex2 = this.getRowIndexByName(names[1]);
            const colIndex2 = this.getColIndexByName(names[1]);

            if ( (null != rowIndex1 && rowIndex1 === rowIndex2)
                || (null != colIndex1 && colIndex1 === colIndex2) ) {
                return true;
            }

            // if ( rowIndex1 !== rowIndex2 && colIndex1 !== colIndex2 ) {
            //     const cell1 = this.getCellByName(names[0]);
            //     const cell2 = this.getCellByName(names[1]);

            //     let cell = cell1.row.getCell(colIndex2);
            //     let vMergeCount = cell.getVMerge();
            //     if ( cell && cell2 === cell ) {
            //         return true;
            //     }

            //     cell = cell2.row.getCell(colIndex1);
            //     if ( cell && cell1 === cell ) {
            //         return true;
            //     }

            //     if ( 1 < cell1.getGridSpan() && 1 < cell2.getGridSpan() ) {
            //         return false;
            //     }

            //     // cell = cell2.row.getCell(colIndex1);
            //     const startGridCol1 = cell1.row.getStartGridCol(colIndex1);
            //     const startGridCol2 = cell2.row.getStartGridCol(colIndex2);

            //     if ( startGridCol1 + cell1.getGridSpan() >= colIndex2 && colIndex2 >= startGridCol1 ) {
            //         return true;
            //     }

            //     if ( startGridCol2 + cell2.getGridSpan() >= colIndex1 && colIndex1 >= startGridCol2 ) {
            //         return true;
            //     }
            // }
        }

        return false;
    }

    // public setCellFormula(par: ITableCellFormulaPar, cellName?: string): number {
    //     let result = ResultType.Failure;
    //     const cell = ( (null != cellName && this.getCellByName(cellName)) ?
    //                 this.getCellByName(cellName) : this.curCell);
    //     if ( cell && cell.setFormula(par) ) {
    //         result = this.setFormulaManager(par, cell, cellName);
    //         if ( ResultType.Success === result && true !== par.bClear ) {
    //             this.calcFormula(cell);
    //             this.parent.recalculate();
    //             this.parent.updateCursorXY();
    //         }
    //     }

    //     return result;
    // }

    // public getCellFormula(cellName?: string): ITableCellFormulaPar {
    //     const cell = ( (null != cellName && this.getCellByName(cellName)) ?
    //                 this.getCellByName(cellName) : this.curCell);
    //     if ( cell ) {
    //         return cell.getFormula();
    //     }

    //     return null;
    // }

    public calcFormula(cell: TableCell): boolean {
        let result = false;
        if ( cell && cell.property.formula ) {
            const par = cell.property.formula;
            const history = this.getHistory();
            let turnOn = false;
            if ( history ) {
                turnOn = history.isTurnOn();
                history.turnOff();
            }

            switch (par.formulaType) {
                case CellFormulaCalc.SUM:
                    result = this.calcSumFormula(par.startCell, par.endCell, cell);
                    break;
                case CellFormulaCalc.ADD:
                    result = this.calcAddFormula(par.addFormula, cell);
                    break;
                case CellFormulaCalc.MUL:
                    result = this.calcMulFormula(par.multi1, par.multi2, cell);
                    break;
                case CellFormulaCalc.MIX:
                    result = this.calcMixFormula(par.mixFormula, cell);
                    break;
            }

            if ( history && turnOn ) {
                history.turnOn();
            }
        }

        return result;
    }

    public execFormulaCalc(): boolean {
        let result = false;
        if ( this.curCell && !this.isReadFile() ) {
            const row = this.curCell.getRow();
            const cellName = this.getCellName(this.curCell.index, row.index);
            // this.curCell.getGridSpan() + row.getCellInfo(this.curCell.index).startGridCol - 1, row.index);
            if ( this.formulaCellManager.hasFormula(cellName) ) {
                const history = this.getHistory();
                let turnOn = false;
                if ( history ) {
                    turnOn = history.isTurnOn();
                    history.turnOff();
                }
                result = this.formulaCellManager.execFormulaCalc(cellName);

                if ( result ) {
                    this.parent.recalculate();
                    this.parent.updateCursorXY();
                }
                if ( history && turnOn ) {
                    history.turnOn();
                }
            }
        }

        return result;
    }

    public calcFormulaByCellName(cellName: string): boolean {
        return this.calcFormula(this.getCellByName(cellName));
    }

    public setFormulaManager(par: ITableCellFormulaPar, cell?: TableCell, cellName?: string | string[]): number {
        if ( !cell) {
            cell = ( (null != cellName && typeof cellName == 'string'  && this.getCellByName(cellName)) ?
                this.getCellByName(cellName) : this.curCell);
        }

        const cells: string[] = [];
        switch (par.formulaType) {
            case CellFormulaCalc.SUM:
                const startRowIndex = this.getRowIndexByName(par.startCell);
                const startColIndex = this.getColIndexByName(par.startCell);
                const endRowIndex = this.getRowIndexByName(par.endCell);
                const endColIndex = this.getColIndexByName(par.endCell);
                const rowCount = this.content.length;

                if ( null != startRowIndex && startRowIndex === endRowIndex && startRowIndex < rowCount ) {
                    const row = this.content[startRowIndex];
                    if ( row ) {
                        for (let cellIndex = startColIndex; cellIndex <= endColIndex; cellIndex++) {
                            const curCell = row ? row.getCell(cellIndex) : null;
                            if ( curCell && curCell !== cell ) {
                                cells.push(this.getCellName(cellIndex, startRowIndex));
                            }
                        }
                    }
                } else if ( null != startColIndex && startColIndex === endColIndex
                    && startRowIndex < rowCount && endRowIndex < rowCount ) {
                    for (let index = startRowIndex; index <= endRowIndex; index++) {
                        const row = this.content[index];
                        const curCell = row ? row.getCell(startColIndex) : null;
                        if ( curCell && curCell !== cell ) {
                            cells.push(this.getCellName(startColIndex, index));
                        }
                    }
                }
                break;
            case CellFormulaCalc.MUL:
                cells.push(par.multi1, par.multi2);
                break;
            case CellFormulaCalc.ADD: {
                const names = par.addFormula.match(RegExp(/[^\+]+/g));

                if ( names && 1 < names.length ) {
                    names.forEach((value) => {
                        cells.push(value);
                    });
                }

                break;
            }
            case CellFormulaCalc.MIX: {
                if (Array.isArray(cellName)) {
                    cells.push(...cellName);
                } else {
                    const { elemNames } = MixFormulaParser.toInfixExpressions(par.mixFormula);
                    if (elemNames.length) {
                        cells.push(...elemNames);
                    }
                }
                break;
            }
        }

        if ( cells && 0 < cells.length ) {
            const row = cell.getRow();
            cellName = (null != cellName && typeof cellName == 'string' ? cellName : this.getCellName(cell.index, row.index));
            return this.formulaCellManager.setFormulaCells(cellName as string, cells, par.bClear);
        } else if ( par.bClear ) {
            return ResultType.UnEdited;
        }

        return ResultType.Failure;
    }

    // public getRowsInfo(): IRowInfoOfNewGrid[][] {
    //     const rowsInfo: IRowInfoOfNewGrid[][] = [];

    //     for (let curRow = 0, rowsCount = this.content.length; curRow < rowsCount; curRow++) {
    //         rowsInfo[curRow] = [];
    //         const row = this.content[curRow];
    //         const beforeInfo = row.getBefore();

    //         for (let curCell = 0, cellsCount = row.getCellsCount(); curCell < cellsCount; curCell++) {
    //             const cell = row.getCell(curCell);
    //             const cellInfo = row.getCellInfo(curCell);

    //             if ( cellInfo) {
    //                 const curGridStart = cellInfo.startGridCol;
    //                 const curGridEnd = curGridStart + cell.getGridSpan() - 1;

    //                 if ( null == this.tableSumGrid[curGridEnd] || null == this.tableSumGrid[curGridStart - 1]) {
    //                     rowsInfo[curRow].push({
    //                         width: 0,
    //                         type: 0,
    //                         gridSpan: 1,
    //                     });
    //                 } else {
    //                     rowsInfo[curRow].push({
    //                         width: this.tableSumGrid[curGridEnd] - this.tableSumGrid[curGridStart - 1],
    //                         type: 0,
    //                         gridSpan: 1,
    //                     });
    //                 }
    //             } else {
    //                 rowsInfo[curRow].push({
    //                     width: 0,
    //                     type: 0,
    //                     gridSpan: 1,
    //                 });
    //             }
    //         }
    //     }
    //     return rowsInfo;
    // }

    // public checkGirdValid(): boolean {
    //     let sumCellsWidth = 0;
    //     let bFlag = false;
    //     const minWidth = 1;
    //     const rowsInfo: IRowInfoOfNewGrid[][] = [];
    //     const xMax = this.xLimit - this.x;

    //     for (let curRow = 0, rowsCount = this.content.length; curRow < rowsCount; curRow++) {
    //         rowsInfo[curRow] = [];
    //         const row = this.content[curRow];
    //         const beforeInfo = row.getBefore();
    //         sumCellsWidth = 0;

    //         for (let curCell = 0, cellsCount = row.getCellsCount(); curCell < cellsCount; curCell++) {
    //             const cell = row.getCell(curCell);
    //             const cellInfo = row.getCellInfo(curCell);

    //             if ( cellInfo) {
    //                 const curGridStart = cellInfo.startGridCol;
    //                 const curGridEnd = curGridStart + cell.getGridSpan() - 1;

    //                 if ( null == this.tableSumGrid[curGridEnd] || null == this.tableSumGrid[curGridStart - 1]) {
    //                     rowsInfo[curRow].push({
    //                         width: 0,
    //                         type: 0,
    //                         gridSpan: 1,
    //                     });
    //                 } else {
    //                     let cellWidth = this.tableSumGrid[curGridEnd] - this.tableSumGrid[curGridStart - 1];
    //                     sumCellsWidth += cellWidth;
    //                     if ( cellsCount - 1 === curCell && minWidth <= Math.abs(sumCellsWidth - xMax) ) {
    //                         cellWidth += Math.abs(sumCellsWidth - xMax);
    //                         bFlag = true;
    //                     }

    //                     rowsInfo[curRow].push({
    //                         width: cellWidth,
    //                         type: 0,
    //                         gridSpan: 1,
    //                     });
    //                 }
    //             } else {
    //                 rowsInfo[curRow].push({
    //                     width: 0,
    //                     type: 0,
    //                     gridSpan: 1,
    //                 });
    //             }
    //         }
    //     }

    //     if ( bFlag ) {
    //         this.createNewGrid(rowsInfo);
    //     }

    //     return bFlag;
    // }

    // public checkFirstTableRowHeight(): number {
    //     const firstRow = this.content[0];
    //     if (firstRow && firstRow.content) {
    //         const rowHeight = firstRow.getCurrentRowHeight();
    //         const firstCell = firstRow.getCell(0);
    //         if (firstCell && firstCell.content && firstCell.content.content) {
    //             // first portion
    //             const firstTextHeight = firstCell.content.content[0].getEmptyHeight();
    //             if (firstTextHeight > rowHeight) {
    //                 return ResultType.Invalid;
    //             }

    //             // first line
    //             // const firstLine = firstCell.content.content[0].lines[0];
    //             // const firstLineHeight = firstLine.bottom - firstLine.top;
    //             // if (firstLineHeight > rowHeight) {
    //             //     return ResultType.Invalid;
    //             // }
    //         }
    //     }

    //     return ResultType.Success;
    // }

    // public resetReadFile(): void {
    //     this.bReadFile = false;
    // }

    private checkTableBorderH(selectedCellsArray: ITableCellPos[]): boolean {
        let num = 0;
        let prevRow = -1;
        const rowIndexs: number[] = [];
        for (const pos of selectedCellsArray) {
            const row = this.content[pos.rowIndex];
            if ( pos.rowIndex !== prevRow ) {
                rowIndexs[num++] = pos.rowIndex;
            }

            prevRow = pos.rowIndex;
        }

        if ( 1 >= rowIndexs.length ) {
            return true;
        }

        let result = true;
        let borderBottom = null;
        for (const pos of selectedCellsArray) {
            const row = this.content[pos.rowIndex];

            if ( row.index === rowIndexs[rowIndexs.length - 1] ) {
                break;
            }

            const cell = row.getCell(pos.cellIndex);
            const borders = cell.getBorders();

            if ( null !== borderBottom ) {
                if ( null == borders.bottom || borders.bottom.size !== borderBottom.size ) {
                    result = false;
                    break;
                }
            } else {
                borderBottom = borders.bottom;
            }
        }

        if ( result && borderBottom ) {
            result = 0 === borderBottom.size ? true : false;
        }

        return result;
    }

    private checkTableBorderV(selectedCellsArray: ITableCellPos[]): boolean {
        let num = 0;
        let prevCol = -1;
        const colIndexs: number[] = [];
        for (const pos of selectedCellsArray) {
            if ( pos.cellIndex !== prevCol ) {
                colIndexs[num++] = pos.cellIndex;
            }

            prevCol = pos.cellIndex;
        }

        if ( 1 >= colIndexs.length ) {
            return true;
        }

        let result = true;
        let borderRight = null;
        const lastColIndex = selectedCellsArray[selectedCellsArray.length - 1].cellIndex;
        for (const pos of selectedCellsArray) {
            const row = this.content[pos.rowIndex];
            const cell = row.getCell(pos.cellIndex);

            if ( pos.cellIndex === lastColIndex ) {
                continue;
            }

            const borders = cell.getBorders();

            if ( null !== borderRight ) {
                if ( null == borders.right || borders.right.size !== borderRight.size ) {
                    result = false;
                    break;
                }
            } else {
                borderRight = borders.right;
            }
        }

        if ( result && borderRight ) {
            result = 0 === borderRight.size ? true : false;
        }

        return result;
    }

    private checkTableBorderTop(selectedCellsArray: ITableCellPos[]): boolean {
        let result = true;
        let borderTop = null;
        const firstRowIndex = selectedCellsArray[0].rowIndex;
        for (const pos of selectedCellsArray) {
            if ( firstRowIndex !== pos.rowIndex ) {
                break;
            }

            const row = this.content[pos.rowIndex];
            const cell = row.getCell(pos.cellIndex);
            const borders = cell.getBorders();

            if ( null !== borderTop ) {
                if ( null == borders.top || borders.top.size !== borderTop.size ) {
                    result = false;
                    break;
                }
            } else {
                borderTop = borders.top;
            }
        }

        if ( result && borderTop ) {
            result = 0 === borderTop.size ? true : false;
        }

        return result;
    }

    private checkTableBorderLeft(selectedCellsArray: ITableCellPos[]): boolean {
        let result = true;
        let borderLeft = null;
        const firstColIndex = selectedCellsArray[0].cellIndex;
        for (const pos of selectedCellsArray) {
            if ( firstColIndex !== pos.cellIndex ) {
                continue;
            }

            const row = this.content[pos.rowIndex];
            const cell = row.getCell(pos.cellIndex);
            const borders = cell.getBorders();

            if ( null !== borderLeft ) {
                if ( null == borders.left || borders.left.size !== borderLeft.size ) {
                    result = false;
                    break;
                }
            } else {
                borderLeft = borders.left;
            }
        }

        if ( result && borderLeft ) {
            result = 0 === borderLeft.size ? true : false;
        }

        return result;
    }

    private checkTableBorderRight(selectedCellsArray: ITableCellPos[]): boolean {
        // let num = 0;
        // let prevRow = -1;
        // const rowIndexs: number[] = [];
        // for (const pos of selectedCellsArray) {
        //     const row = this.content[pos.rowIndex];
        //     if ( pos.rowIndex !== prevRow ) {
        //         rowIndexs[num++] = pos.rowIndex;
        //     }

        //     prevRow = pos.rowIndex;
        // }
        // let num = 0;
        // let prevCol = -1;
        // const colIndexs: number[] = [];
        // for (const pos of selectedCellsArray) {
        //     if ( pos.cellIndex !== prevCol ) {
        //         colIndexs[num++] = pos.cellIndex;
        //     }

        //     prevCol = pos.cellIndex;
        // }

        let result = true;
        let borderRight = null;
        const lastColIndex = selectedCellsArray[selectedCellsArray.length - 1].cellIndex;
        for (const pos of selectedCellsArray) {
            if ( lastColIndex !== pos.cellIndex ) {
                continue;
            }

            const row = this.content[pos.rowIndex];
            const cell = row.getCell(pos.cellIndex);
            const borders = cell.getBorders();

            if ( null !== borderRight ) {
                if ( null == borders.right || borders.right.size !== borderRight.size ) {
                    result = false;
                    break;
                }
            } else {
                borderRight = borders.right;
            }
        }

        if ( result && borderRight ) {
            result = 0 === borderRight.size ? true : false;
        }

        return result;
    }

    private checkTableBorderBottom(selectedCellsArray: ITableCellPos[]): boolean {
        // let num = 0;
        // let prevCol = -1;
        // const colIndexs: number[] = [];
        // for (const pos of selectedCellsArray) {
        //     if ( pos.cellIndex !== prevCol ) {
        //         colIndexs[num++] = pos.cellIndex;
        //     }

        //     prevCol = pos.cellIndex;
        // }

        let result = true;
        let borderBottom = null;
        const lastRowIndex = selectedCellsArray[selectedCellsArray.length - 1].rowIndex;
        for (const pos of selectedCellsArray) {
            if ( lastRowIndex !== pos.rowIndex ) {
                continue;
            }

            const row = this.content[pos.rowIndex];
            const cell = row.getCell(pos.cellIndex);
            const borders = cell.getBorders();

            if ( null !== borderBottom ) {
                if ( null == borders.bottom || borders.bottom.size !== borderBottom.size ) {
                    result = false;
                    break;
                }
            } else {
                borderBottom = borders.bottom;
            }
        }

        if ( result && borderBottom ) {
            result = 0 === borderBottom.size ? true : false;
        }

        return result;
    }

    private checkTableBorderInner(selectedCellsArray: ITableCellPos[]): boolean {
        // if ( false === this.checkTableBorderH(selectedCellsArray)
        //     || false === this.checkTableBorderV(selectedCellsArray) ) {
        //     return false;
        // }

        return this.checkTableBorderH(selectedCellsArray) && this.checkTableBorderV(selectedCellsArray);
    }

    private checkTableBorderOutter(selectedCellsArray: ITableCellPos[]): boolean {
        // if ( false === this.checkTableBorderTop(selectedCellsArray)
        //     || false === this.checkTableBorderLeft(selectedCellsArray)
        //     || false === this.checkTableBorderRight(selectedCellsArray)
        //     || false === this.checkTableBorderBottom(selectedCellsArray) ) {
        //     return false;
        // }

        return this.checkTableBorderTop(selectedCellsArray) && this.checkTableBorderLeft(selectedCellsArray)
            && this.checkTableBorderRight(selectedCellsArray) && this.checkTableBorderBottom(selectedCellsArray);
    }

    /**
     * 横向列合并项进行调整
     * @param leftIndex 左边最大索引
     * @param rightIndex 右边最小索引
     * @param table 表格数据源
     */
    // private setColSpan(table: Table): void {
    //     if (table.content.length === 1) {
    //         return;
    //     }
    //     let leftIndex: number = 0;
    //     let rightIndex: number = 9999;
    //     table.content.forEach((row, rowIndex) => {
    //         const cells = row.content;
    //         const firstCell = cells[0];
    //         const startGridCol = firstCell.metrics.startGridCol;
    //         if (startGridCol > leftIndex) {
    //             leftIndex = startGridCol;
    //         }

    //         const lastCell = cells[cells.length - 1];
    //         const endGridCol = lastCell.metrics.startGridCol + lastCell.property.gridSpan;
    //         if (endGridCol < rightIndex) {
    //             rightIndex = endGridCol;
    //         }
    //     });

    //     table.content.forEach((row, index) => {
    //         const cells = row.content;
    //         const len = cells.length;

    //         // 左边的列
    //         const firstCell = cells[0];
    //         const startGridCol = firstCell.metrics.startGridCol;
    //         const subIndex: number = leftIndex - startGridCol;
    //         if (subIndex !== 0) {
    //             firstCell.setGridSpan(firstCell.property.gridSpan - subIndex);
    //         }

    //         // 右边列
    //         const lastCell = cells[len - 1];
    //         const endGridSpan = lastCell.property.gridSpan;
    //         const endGridCol = lastCell.metrics.startGridCol + endGridSpan;
    //         const subEndIndex = endGridCol - rightIndex;
    //         if (subEndIndex !== 0) {
    //             lastCell.setGridSpan(endGridSpan - subEndIndex);
    //         }
    //     });
    // }

    // /**
    //  * 对纵向合并列进行补全
    //  * @param rowIndex 最大行数
    //  * @param rowSpans 需要补全的列
    //  * @param rowMap 所有选中的行列
    //  */
    // private setRowSpan(rowIndex: number, rowSpans: {rowIndex: number, colSpan: number, cell: TableCell}[],
    //                    rowMap: Map<number, TableCell[]>): void {
    //     const contents = this.content;
    //     for (const item of rowSpans) {
    //         const cell = item.cell;
    //         let currentRowIndex = item.rowIndex;
    //         let colSpan = item.colSpan;
    //         const cellIndex = cell.metrics.startGridCol;
    //         while (colSpan-- > 1 && currentRowIndex++ < rowIndex) {
    //             const row: TableCell[] = rowMap.get(currentRowIndex);
    //             let isExist: boolean = false;
    //             for (let index = row.length - 1; index >= 0; index--) {
    //                 if (row[index].metrics.startGridCol < cellIndex) {
    //                     isExist = true;
    //                     row.splice(index + 1, 0, contents[currentRowIndex].content[cellIndex]);
    //                     break;
    //                 }
    //             }
    //             if (isExist === false) {
    //                 row.splice(0, 0, contents[currentRowIndex].content[cellIndex]);
    //             }
    //             // const lastIndex = row.content.lastIndexOf(data => );
    //         }
    //     }
    // }

    // private getStartMergedCell2(cellIndex: number, rowIndex: number): TableCell {
    //     const row = this.content[rowIndex];
    //     const cell = row.getCell(cellIndex);
    //     const cellInfo = row.getCellInfo(cellIndex);

    //     return this.getStartMergedCell(rowIndex, cellInfo.startGridCol, cell.getGridSpan());
    // }

    /**
     * 通过坐标位置，定位所在单元格
     * @param pageIndex
     * @param pointX
     * @param pointY
     */
    // private getCellByXY(pointX: number, pointY: number, pageIndex: number): { rowIndex: number, cellIndex: number} {

    //     let curGrid = 0;
    //     // key-wasm by tinyzhi
    //     // const curPage = Math.min(this.pages.length - 1, Math.max(0, pageIndex));
    //     const nResult1 = WasmInstance.instance._MMX(0, pageIndex);
    //     const curPage = WasmInstance.instance._MMI(this.pages.length - 1, nResult1);
    //     // end by tinyzhi
    //     const page = this.pages[curPage];
    //     const colsCount = this.tableGridCalc.length;

    //     // 1. 获取当前位置所在表格列col
    //     if ( pointX >= page.x ) {
    //         for (; curGrid < colsCount; curGrid++) {
    //             if ( pointX >= page.x + this.tableSumGrid[curGrid - 1]
    //                 && pointX <= page.x + this.tableSumGrid[curGrid] ) {
    //                 break;
    //             }
    //         }
    //     }

    //     if ( curGrid >= colsCount ) {
    //         curGrid = colsCount - 1;
    //     }

    //     // const pageNum = pageIndex;
    //     let rowStart = 0;
    //     let rowEnd = 0;

    //     // 2. 确定坐标所在页面的开始行，结束行
    //     if ( 0 <= pageIndex && pageIndex < this.pages.length ) {
    //         rowStart = this.pages[pageIndex].firstRow;
    //         rowEnd = this.pages[pageIndex].lastRow;
    //     } else if ( this.pages.length <= pageIndex ) {
    //         rowStart = this.content.length - 1;
    //         rowEnd = rowStart;
    //     }

    //     const result = {
    //         rowIndex: 0,
    //         cellIndex: 0,
    //     };

    //     if ( rowEnd < rowStart ) {
    //         return result;
    //     }

    //     // 3. 确定row，cell
    //     for (let curRow = rowStart; curRow <= rowEnd; curRow++) {
    //         const row = this.content[curRow];
    //         const cellsCount = row.getCellsCount();
    //         const beforeInfo = row.getBefore();
    //         let curGridCol = beforeInfo.gridBefore;

    //         for (let curCell = 0; curCell < cellsCount; curCell++) {
    //             const cell = row.getCell(curCell);
    //             const gridSpan = cell.getGridSpan();
    //             const vMerge = cell.getVMerge();

    //             // 当前单元格是否垂直合并
    //             if ( VerticalMergeType.Continue === vMerge && rowStart !== curRow ) {
    //                 curGridCol += gridSpan;
    //                 continue;
    //             }

    //             const vMergeCount = this.getVerticalMergeCountOnPage(pageIndex, curRow, curGridCol, gridSpan);
    //             if ( 0 >= vMergeCount ) {
    //                 curGridCol += gridSpan;
    //                 continue;
    //             }

    //             // 检查行，即x坐标
    //             if ( curGrid >= curGridCol && curGrid < curGridCol + gridSpan ) {

    //                 const rowInfo = this.rowsInfo[curRow + vMergeCount - 1];
    //                 if ( rowInfo.height[pageIndex] && rowInfo.y[pageIndex]
    //                     && ( pointY <= ( rowInfo.height[pageIndex] + rowInfo.y[pageIndex])
    //                         || curRow + vMergeCount - 1 >= rowEnd ) ) {

    //                     if ( VerticalMergeType.Continue === vMerge && rowStart === curRow ) {
    //                         const cell1 = this.getStartMergedCell(curRow, curGridCol, gridSpan);
    //                         if ( cell1 ) {
    //                             result.rowIndex = cell1.row.index;
    //                             result.cellIndex = cell1.index;
    //                             return result;
    //                         } else {
    //                             return result;
    //                         }
    //                     } else {
    //                         result.rowIndex = curRow;
    //                         result.cellIndex = curCell;
    //                         return result;
    //                     }
    //                 }
    //             }

    //             curGridCol += gridSpan;
    //         }
    //     }

    //     return result;
    // }

    /**
     * 检测当前光标是否选中表格边框
     * @param pointX
     * @param pointY
     * @param curPage
     */
    // private checkHitInBorder(pointX: number, pointY: number, curPage: number): any {
    //     const cellPos = this.getCellByXY(pointX, pointY, curPage);
    //     const result = {
    //         pos: cellPos,
    //         border: -1,
    //         rowIndex: cellPos.rowIndex,
    //         bRowSelection: false,
    //         bColumnSelection: false,
    //         bCellSelection: false,
    //     };

    //     if ( this.logicDocument.isForbidMoveTableBoder() ) {
    //         return result;
    //     }

    //     const curRowIndex = cellPos.rowIndex;
    //     const curCellIndex = cellPos.cellIndex;
    //     const row = this.content[curRowIndex];
    //     const cell = row.getCell(curCellIndex);
    //     const cellInfo = row.getCellInfo(curCellIndex);
    //     const vMergeCount = this.getVMergeCount(curCellIndex, curRowIndex);
    //     const vMergeCountOnPage = this.getVerticalMergeCountOnPage(curPage,
    //                                     curRowIndex, cellInfo.startGridCol, cell.getGridSpan());

    //     if ( 0 >= vMergeCountOnPage ) {
    //         return result;
    //     }

    //     const page = this.pages[curPage];
    //     const xCellStart = page.x + cellInfo.xGridStart;
    //     const xCellEnd = page.x + cellInfo.xGridEnd;
    //     const yCellStart = this.rowsInfo[curRowIndex].y[curPage];
    //     const yCellEnd = this.rowsInfo[curRowIndex + vMergeCountOnPage - 1].y[curPage]
    //                     + this.rowsInfo[curRowIndex + vMergeCountOnPage - 1].height[curPage];

    //     const tolerance = 3;

    //     if ( yCellStart - tolerance <= pointY && pointY <= yCellStart + tolerance ) {
    //         result.border = 0;
    //     } else if ( yCellEnd - tolerance <= pointY && pointY <= yCellEnd + tolerance ) {
    //         if ( vMergeCountOnPage !== vMergeCount ) {
    //             result.border = -1;
    //         } else {
    //             result.border = 2;
    //             result.rowIndex = curRowIndex + vMergeCount - 1;
    //         }
    //     } else if ( xCellStart - tolerance <= pointX && pointX <= xCellStart + tolerance ) {
    //         result.border = 3;
    //     } else if ( xCellEnd - tolerance <= pointX && pointX <= xCellEnd + tolerance ) {
    //         result.border = 1;
    //     }

    //     if ( 0 === curRowIndex && pointY <= yCellStart + tolerance ) {
    //         result.border = -1;
    //     }

    //     // console.log(result.border)
    //     return result;
    // }

    /**
     * 获取选中的cells
     * @param bForceSelectByLines
     */
    // private updateSelectedCellsArray(bForceSelectByLines: boolean = false): void {
    //     this.selection.type = TableSelectionType.TableCell;
    //     this.selection.data = [];

    //     if ( true !== bForceSelectByLines && this.parent.isSelectedSingleElement() ) {
    //         let startRow = this.selection.startPos.pos.rowIndex;
    //         let startCell = this.selection.startPos.pos.cellIndex;
    //         let endRow = this.selection.endPos.pos.rowIndex;
    //         let endCell = this.selection.endPos.pos.cellIndex;

    //         if ( endRow < startRow ) {
    //             const tempRow = startRow;
    //             startRow = endRow;
    //             endRow = tempRow;

    //             const tempCell = startCell;
    //             startCell = endCell;
    //             endCell = tempCell;
    //         }

    //         if ( startRow === endRow ) {
    //             if ( endCell < startCell ) {
    //                 const tempCell = startCell;
    //                 startCell = endCell;
    //                 endCell = tempCell;
    //             }

    //             const row = this.content[startRow];

    //             for (let curCell = startCell; curCell <= endCell; curCell++) {
    //                 const cell = row.getCell(curCell);
    //                 const vMerge = cell.getVMerge();

    //                 if ( VerticalMergeType.Continue === vMerge ) {
    //                     continue;
    //                 }

    //                 this.selection.data.push({rowIndex: startRow, cellIndex: curCell});
    //             }
    //         } else {
    //             const cellStart = this.content[startRow].getCell(startCell);
    //             const cellEnd = this.content[endRow].getCell(endCell);

    //             const gridColStart1 = this.content[startRow].getStartGridCol(startCell);
    //             const gridColEnd1 = gridColStart1 + cellStart.getGridSpan() - 1;
    //             const gridColStart2 = this.content[endRow].getStartGridCol(endCell);
    //             const gridColEnd2 = gridColStart2 + cellEnd.getGridSpan() - 1;

    //             const gridColStart = ( gridColStart1 > gridColStart2 ? gridColStart2 : gridColStart1 );
    //             const gridColEnd = ( gridColEnd1 > gridColEnd2 ? gridColEnd1 : gridColEnd2 );

    //             for (let curRow = startRow; curRow <= endRow; curRow++) {
    //                 const row = this.content[curRow];
    //                 const beforeInfo = row.getBefore();
    //                 const cellsCount = row.getCellsCount();
    //                 let curGridCol = beforeInfo.gridBefore;

    //                 for (let curCell = 0; curCell < cellsCount; curCell++) {
    //                     const cell = row.getCell(curCell);
    //                     const gridSpan = cell.getGridSpan();
    //                     const vMerge = cell.getVMerge();

    //                     if ( VerticalMergeType.Continue === vMerge ) {
    //                         curGridCol += gridSpan;
    //                         continue;
    //                     }

    //                     if ( startRow === curRow || endRow === curRow || ( curRow > startRow && curRow < endRow )) {
    //                         if ( ( gridColStart <= curGridCol && curGridCol <= gridColEnd )
    //                             || ( gridColStart <= curGridCol + gridSpan - 1
    //                                     && curGridCol + gridSpan - 1 <= gridColEnd ) ) {
    //                             this.selection.data.push({rowIndex: curRow, cellIndex: curCell});
    //                         }
    //                     }

    //                     curGridCol += gridSpan;
    //                 }
    //             }
    //         }
    //     } else {
    //         // 选中每行的所有cell
    //         const rowCount = this.content.length;
    //         let startRow = Math.min(rowCount - 1, Math.max(0, this.selection.startPos.pos.rowIndex));
    //         let endRow = Math.min(rowCount - 1, Math.max(0, this.selection.endPos.pos.rowIndex));

    //         if ( startRow > endRow ) {
    //             const tempRow = startRow;
    //             startRow = endRow;
    //             endRow = tempRow;
    //         }

    //         for (let curRow = startRow; curRow <= endRow; curRow++) {
    //             const row = this.content[curRow];
    //             const cellsCount = row.getCellsCount();

    //             for (let curCell = 0; curCell < cellsCount; curCell++) {
    //                 const cell = row.getCell(curCell);
    //                 const vMerge = cell.getVMerge();

    //                 if ( VerticalMergeType.Continue === vMerge ) {
    //                     continue;
    //                 }

    //                 this.selection.data.push({rowIndex: curRow, cellIndex: curCell});
    //             }
    //         }
    //     }

    //     if ( 1 < this.selection.data.length ) {
    //         this.selection.curRow = this.selection.data[this.selection.data.length - 1].rowIndex;
    //     }
    // }

    /**
     * 检查选中单元格是否能合并
     * @ return  true：可以合并，返回合并的区间，行信息；  false：不能合并
     */
    // private checkMerge(selection: TableSelection = this.selection): any {
    //     const rowsInfo = [];
    //     let nRowMin = -1;
    //     let nRowMax = -1;

    //     for (let index = 0, count = selection.data.length; index < count; index++) {
    //         const pos = selection.data[index];
    //         const row = this.content[pos.rowIndex];
    //         const cell = row.getCell(pos.cellIndex);

    //         const startGridCol = row.getCellInfo(pos.cellIndex).startGridCol;
    //         const endGridCol = startGridCol + cell.getGridSpan() - 1;
    //         const vMergeCount = this.getVerticalMergeCount(pos.rowIndex, startGridCol, cell.getGridSpan());

    //         for (let rowIndex = pos.rowIndex; rowIndex <= pos.rowIndex + vMergeCount - 1; rowIndex++) {
    //             if ( undefined !== rowsInfo[rowIndex] ) {
    //                 if ( startGridCol < rowsInfo[rowIndex].startGrid ) {
    //                     rowsInfo[rowIndex].startGrid = startGridCol;
    //                 }

    //                 if ( endGridCol > rowsInfo[rowIndex].endGrid ) {
    //                     rowsInfo[rowIndex].endGrid = endGridCol;
    //                 }
    //             } else {
    //                 rowsInfo[rowIndex] = { startGrid: startGridCol, endGrid: endGridCol };

    //                 nRowMin = ( -1 === nRowMin || nRowMin > rowIndex ) ? rowIndex : nRowMin;
    //                 nRowMax = ( -1 === nRowMax || nRowMax < rowIndex ) ? rowIndex : nRowMax;
    //             }
    //         }
    //     }

    //     let bCanMerge = true;
    //     let startGrid = -1;
    //     let endGrid = -1;

    //     // 检查一下选中的行没有间隙：单元格没有被选中，选中区域不连续
    //     for (let index = nRowMin; index <= nRowMax; index++) {
    //         if ( !rowsInfo[index] ) {
    //             bCanMerge = false;
    //             break;
    //         }
    //     }

    //     // 检查选中的单元格是否开始和结束所在列都一致
    //     // tslint:disable-next-line: forin
    //     for (const index in rowsInfo) {
    //         if ( -1 === startGrid ) {
    //             startGrid = rowsInfo[index].startGrid;
    //         } else if ( startGrid !== rowsInfo[index].startGrid ) {
    //             bCanMerge = false;
    //             break;
    //         }

    //         if ( -1 === endGrid ) {
    //             endGrid = rowsInfo[index].endGrid;
    //         } else if ( endGrid !== rowsInfo[index].endGrid ) {
    //             bCanMerge = false;
    //             break;
    //         }
    //     }

    //     if ( true === bCanMerge ) {
    //         let topRow = -1;
    //         let bottomRow = -1;

    //         // 确保水平单元具有均匀的上下边缘（即没有突起）。
    //         // 为此，对于落入[Grid_start，Grid_end]段的每个列，找到顶部和底部单元格，分别查看单元格数据的顶部和底部行
    //         for (let gridIndex = startGrid; gridIndex <= endGrid; gridIndex++) {
    //             let topPos = null;
    //             let bottomPos = null;

    //             for (let index = 0, count = selection.data.length; index < count; index++) {
    //                 const pos = selection.data[index];
    //                 const curRow = this.content[pos.rowIndex];
    //                 const curCell = curRow.getCell(pos.cellIndex);

    //                 const startGridCol = curRow.getCellInfo(pos.cellIndex).startGridCol;
    //                 const endGridCol = startGridCol + curCell.getGridSpan() - 1;

    //                 if ( gridIndex >= startGridCol && gridIndex <= endGridCol ) {
    //                     topPos = ( null === topPos || topPos.rowIndex > pos.rowIndex ) ? pos : topPos;
    //                     bottomPos = ( null === bottomPos || bottomPos.rowIndex < pos.rowIndex ) ? pos : bottomPos;
    //                 }
    //             }

    //             if ( null === topPos || null === bottomPos ) {
    //                 bCanMerge = false;
    //                 break;
    //             }

    //             if ( -1 === topRow ) {
    //                 topRow = topPos.rowIndex;
    //             } else if ( topRow !== topPos.rowIndex ) {
    //                 bCanMerge = false;
    //                 break;
    //             }

    //             const row = this.content[bottomPos.rowIndex];
    //             const cell = row.getCell(bottomPos.cellIndex);
    //             const vMergeCount = this.getVerticalMergeCount(bottomPos.rowIndex,
    //                                     row.getCellInfo(bottomPos.cellIndex).startGridCol, cell.getGridSpan());
    //             const curBottomRow = bottomPos.rowIndex + vMergeCount - 1;

    //             if ( -1 === bottomRow ) {
    //                 bottomRow = curBottomRow;
    //             } else if ( bottomRow !== curBottomRow ) {
    //                 bCanMerge = false;
    //                 break;
    //             }
    //         }

    //         if ( true === bCanMerge ) {
    //             for (let rowIndex = topRow; rowIndex <= bottomRow; rowIndex++) {
    //                 const row = this.content[rowIndex];
    //                 const gridBefore = row.getBefore().gridBefore;
    //                 const gridAfter = row.getAfter().gridAfter;

    //                 if ( 0 >= gridBefore && 0 >= gridAfter ) {
    //                     continue;
    //                 }

    //                 if ( startGrid < gridBefore ) {
    //                     bCanMerge = false;
    //                     break;
    //                 }

    //                 const cellsCount = row.getCellsCount();
    //                 const cell = row.getCell(cellsCount - 1);
    //                 const rowEndGrid = cell.getGridSpan() - 1 + row.getCellInfo(cellsCount - 1).startGridCol;

    //                 if ( endGrid > rowEndGrid ) {
    //                     bCanMerge = false;
    //                     break;
    //                 }
    //             }
    //         }
    //     }

    //     return { startGrid, endGrid, rowsInfo, bCanMerge };
    // }

    // private getVerticalMergeCell(rows: TableRow[], currentRow: TableRow, rowIndex: number): TableRow {
    //     let prevRow = currentRow;
    //     while (prevRow) {
    //         if (prevRow.content[0].property.verticalMerge === 0) {
    //             return prevRow;
    //         }
    //         prevRow = rows[--rowIndex];
    //     }

    //     return currentRow;
    // }

    /**
     * 重新计算rows的上下关系
     * @param startIndex
     */
    // private reIndexContent(startIndex: number): void {
    //     if ( null === startIndex ) {
    //         startIndex = 0;
    //     }

    //     for (let index = startIndex, rowsCount = this.content.length; index < rowsCount; index++) {
    //         const row = this.content[index];
    //         row.setIndex(index);
    //         row.prev = ( index > 0 ? this.content[index - 1] : null );
    //         row.next = ( index < rowsCount - 1 ? this.content[index + 1] : null );
    //     }
    // }

    /**
     * 基于RowsInfo数组再次对表的网格进行返工。
     * 在此数组中，为每一行设置所有单元格的宽度（在GridBefore / GridAfter行之前或之后的间隙）。
     * 在输出中，给出一个新的网格TableGrid和一个RowsInfo数组，其中为每个单元格（skip）指定了GridSpan。
     * @param rowsInfo
     */
    // private createNewGrid(rowsInfo: IRowInfoOfNewGrid[][]): number[] {
    //     const curPos = [];
    //     const curX = [];

    //     // 设置每一行所有单元格的宽度
    //     for (let index = 0, count = rowsInfo.length; index < count; index++) {
    //         curPos[index] = 0;
    //         curX[index] = rowsInfo[index][0].width;

    //         for (const iterator of rowsInfo[index]) {
    //             iterator.gridSpan = 1;

    //             if ( 1 !== rowsInfo[index][rowsInfo[index].length - 1].type ) {
    //                 rowsInfo[index].push({ width: 0, type: 1, gridSpan: 0 });
    //             } else {
    //                 rowsInfo[index][rowsInfo[index].length - 1] = { width: 0, type: 1, gridSpan: 0 };
    //             }
    //         }
    //         // for (let index2 = 0; index2 < rowsInfo[index].length; index2++) {
    //         //     rowsInfo[index][index2].gridSpan = 1;

    //         //     if ( 1 !== rowsInfo[index][rowsInfo[index].length - 1].type ) {
    //         //         rowsInfo[index].push({ width: 0, type: 1, gridSpan: 0 });
    //         //     } else {
    //         //         rowsInfo[index][rowsInfo[index].length - 1] = { width: 0, type: 1, gridSpan: 0 };
    //         //     }
    //         // }
    //     }

    //     const tableGrid: number[] = [];
    //     let prevX = 0;
    //     let bEnd = false;

    //     while ( true !== bEnd ) {
    //         let minX = -1;
    //         const count = rowsInfo.length;

    //         for (let index = 0; index < count; index++) {
    //             // key-wasm by tinyzhi
    //             // if ( ( -1 === minX || curX[index] < minX )
    //             //     && !( rowsInfo[index].length - 1 === curPos[index]
    //             //             && 1 === rowsInfo[index][curPos[index]].type ) ) {
    //             if (Boolean(WasmInstance.instance._Table_createNewGrid_Judge(minX, curX[index], rowsInfo[index].length,
    //                     curPos[index], rowsInfo[index][curPos[index]].type))) {
    //             // end by tinyzhi
    //                 minX = curX[index];
    //             }
    //         }

    //         for (let index = 0; index < count; index++) {
    //             if ( rowsInfo[index].length - 1 === curPos[index] && 1 === rowsInfo[index][curPos[index]].type ) {
    //                 rowsInfo[index][curPos[index]].gridSpan++;
    //             } else {
    //                 // key-wasm by tinyzhi
    //                 if ( 1 > Math.abs(curX[index] - minX) ) {
    //                 // if (Boolean(WasmInstance.instance._FLEq(curX[index], minX)) ) {
    //                 // end by tinyzhi
    //                     curPos[index]++;
    //                     curX[index] += rowsInfo[index][curPos[index]].width;
    //                 } else {
    //                     rowsInfo[index][curPos[index]].gridSpan++;
    //                 }
    //             }
    //         }

    //         tableGrid.push(minX - prevX);
    //         prevX = minX;

    //         bEnd = true;

    //         for (let index = 0; index < count; index++) {
    //             if ( rowsInfo[index].length - 1 !== curPos[index] ) {
    //                 bEnd = false;
    //                 break;
    //             }
    //         }
    //     }

    //     for (let curRow = 0, count = rowsInfo.length; curRow < count; curRow++) {
    //         const rowInfo = rowsInfo[curRow];
    //         const row = this.content[curRow];

    //         let curIndex = 0;
    //         if ( -1 === rowInfo[0].type ) {
    //             if ( 0 < rowInfo[0].gridSpan ) {
    //                 row.setBefore(rowInfo[0].gridSpan);
    //             }
    //             curIndex++;
    //         } else {
    //             row.setBefore(0);
    //         }

    //         for (let curCell = 0; curCell < rowInfo.length; curIndex++, curCell++) {
    //             if ( 1 === rowInfo[curIndex].type ) {
    //                 break;
    //             }

    //             const cell = row.getCell(curCell);
    //             cell.setGridSpan(rowInfo[curIndex].gridSpan);

    //             const widthType = cell.getCellWidth().type;
    //             if ( TableWidthType.Auto !== widthType ) {
    //                 const width = rowInfo[curIndex].width;
    //                 cell.setCellWidth(new TableMeasurement(TableWidthType.Mm, width));
    //             }
    //         }

    //         curIndex = rowInfo.length - 1;

    //         if ( 1 === rowInfo[curIndex].type ) {
    //             row.setAfter(rowInfo[curIndex].gridSpan);
    //         } else {
    //             row.setAfter(0);
    //         }
    //     }

    //     this.setTableGrid(tableGrid);
    //     return tableGrid;
    // }

    // // private setTableGrid(tableGrid: number[]): void {
    // //     // gHistory.addChange();

    // //     this.tableGrid = tableGrid;
    // // }

    // private correctVerticelMerge(): void {
    //     for (let curRow = 0, rowsCount = this.content.length; curRow < rowsCount; curRow++) {
    //         const row = this.content[curRow];
    //         let gridCol = row.getBefore().gridBefore;

    //         for (let curCell = 0, cellsCount = row.getCellsCount(); curCell < cellsCount; curCell++) {
    //             const cell = row.getCell(curCell);
    //             const vMerge = cell.getVMerge();
    //             const gridSpan = cell.getGridSpan();

    //             if ( VerticalMergeType.Continue === vMerge ) {
    //                 let bNeedReset = true;

    //                 if ( 0 !== curRow ) {
    //                     const prevRow = this.content[curRow - 1];
    //                     let prevGridCol = prevRow.getBefore().gridBefore;

    //                     for (let prevCell = 0, prevCellsCount = prevRow.getCellsCount();
    //                                 prevCell < prevCellsCount; prevCell++) {
    //                         const oPrevCell = prevRow.getCell(prevCell);
    //                         const prevGridSpan = oPrevCell.getGridSpan();

    //                         if ( prevGridCol === gridCol ) {
    //                             if ( prevGridSpan === gridSpan ) {
    //                                 bNeedReset = false;
    //                             }

    //                             break;
    //                         } else if ( prevGridCol > gridCol ) {
    //                             break;
    //                         }

    //                         prevGridCol += prevGridSpan;
    //                     }
    //                 }

    //                 if ( true === bNeedReset ) {
    //                     cell.setVMerge(VerticalMergeType.Restart);
    //                 }
    //             }

    //             gridCol += gridSpan;
    //         }
    //     }
    // }

    // private getSelectedRowsRange(): any {
    //     let startRow = -1;
    //     let endRow = -2;

    //     const selectedCellsArray = this.getSelectionArray();
    //     if ( 0 !== selectedCellsArray.length ) {
    //         for (let index = 0, count = selectedCellsArray.length; index < count; index++) {
    //             const rowIndex = selectedCellsArray[index].rowIndex;

    //             if ( -1 === startRow || startRow > rowIndex ) {
    //                 startRow = rowIndex;
    //             }

    //             if ( -1 === endRow || endRow < rowIndex ) {
    //                 endRow = rowIndex;
    //             }
    //         }
    //     } else {
    //         startRow = this.curCell.getRow().index;
    //         endRow = startRow;
    //     }

    //     return { start: startRow, end: endRow };
    // }

    // private updateForMovingBorder(pos: number): number {
    //     const data = this.selection.data2 as ITableSelectionData;

    //     if ( null != data.min ) {
    //         pos = Math.max(pos, data.min);
    //     }

    //     if ( null != data.max ) {
    //         pos = Math.min(pos, data.max);
    //     }

    //     // this.updateTableMarkup(data.pos.rowIndex, data.pos.cellIndex, data.pageNum);

    //     return pos;
    // }

    // private updateTableMarkup(rowIndex: number, cellIndex: number, curPage: number): void {
    //     // this.markup.internal = {
    //     //     rowIndex,
    //     //     cellIndex,
    //     //     pageNum: curPage,
    //     // };

    //     const page = this.pages[curPage];
    //     if ( null == page ) {
    //         return;
    //     }
    // }

    private updateNewBorderProps(name: string, border: DocumentBorder): void {
        const curBorder = this.property.tableBorders[name];
        border.bPrint = curBorder.bPrint;
        border.bVisible  = curBorder.bVisible;
    }

    private setTableBorders(tableBorders: any): boolean {
        let result = false;
        if ( false === this.checkNullBorder(tableBorders.top) &&
            false === this.compareBorders3(tableBorders.top, this.property.tableBorders.top)) {
            this.checkBorderColor(tableBorders.top);
            this.updateNewBorderProps('top', tableBorders.top);
            this.setTableBordersTop(tableBorders.top);

            const row = this.content[0];
            for (let index = 0; index < row.getCellsCount(); index++) {
                const cell = row.getCell(index);
                result = cell.setBorder(null, 0) || result;
            }
        }

        if ( false === this.checkNullBorder(tableBorders.bottom) &&
            false === this.compareBorders3(tableBorders.bottom, this.property.tableBorders.bottom)) {
            this.checkBorderColor(tableBorders.bottom);
            this.updateNewBorderProps('bottom', tableBorders.bottom);
            this.setTableBordersBottom(tableBorders.bottom);

            const row = this.content[this.content.length - 1];
            for (let index = 0; index < row.getCellsCount(); index++) {
                const cell = row.getCell(index);
                result = cell.setBorder(null, 2) || result;
            }
        }

        if ( false === this.checkNullBorder(tableBorders.left) &&
            false === this.compareBorders3(tableBorders.left, this.property.tableBorders.left)) {
            this.checkBorderColor(tableBorders.left);
            this.updateNewBorderProps('left', tableBorders.left);
            this.setTableBordersLeft(tableBorders.left);

            for (const row of this.content) {
                const cell = row.getCell(0);
                result = cell.setBorder(null, 3) || result;
            }
        }

        if ( false === this.checkNullBorder(tableBorders.right) &&
            false === this.compareBorders3(tableBorders.right, this.property.tableBorders.right)) {
            this.checkBorderColor(tableBorders.right);
            this.updateNewBorderProps('right', tableBorders.right);
            this.setTableBordersRight(tableBorders.right);

            for (const row of this.content) {
                const cellsCount = row.getCellsCount();
                const cell = row.getCell(cellsCount - 1);
                result = cell.setBorder(null, 1) || result;
            }
        }

        if ( false === this.checkNullBorder(tableBorders.insideH) &&
            false === this.compareBorders3(tableBorders.insideH, this.property.tableBorders.insideH)) {
            this.checkBorderColor(tableBorders.insideH);
            this.updateNewBorderProps('insideH', tableBorders.insideH);
            this.setTableBordersH(tableBorders.insideH);

            for (let curRow = 0; curRow < this.content.length; curRow++) {
                const row = this.content[curRow];
                const cellsCount = row.getCellsCount();

                for (let curCell = 0; curCell < cellsCount; curCell++) {
                    const cell = row.getCell(cellsCount - 1);

                    if ( 0 !== curRow ) {
                        result = cell.setBorder(null, 0) || result;
                    }

                    if ( this.content.length - 1 !== curRow ) {
                        result = cell.setBorder(null, 2) || result;
                    }
                }
            }
        }

        if ( false === this.checkNullBorder(tableBorders.insideV) &&
            false === this.compareBorders3(tableBorders.insideV, this.property.tableBorders.insideV)) {
            this.checkBorderColor(tableBorders.insideV);
            this.updateNewBorderProps('insideV', tableBorders.insideV);
            this.setTableBordersV(tableBorders.insideV);

            for (const row of this.content) {
                const cellsCount = row.getCellsCount();

                for (let curCell = 0; curCell < cellsCount; curCell++) {
                    const cell = row.getCell(cellsCount - 1);

                    if ( 0 !== curCell ) {
                        result = cell.setBorder(null, 3) || result;
                    }

                    if ( cellsCount - 1 !== curCell ) {
                        result = cell.setBorder(null, 1) || result;
                    }
                }
            }
        }

        return result;
    }

    // private setCellBorders(cellBorders: any, bAll?: boolean, curRow?: TableRow): boolean {
    //     let result = false;
    //     let cellsArray: ITableCellPos[] = null;
    //     const bSpacing = ( null != this.content[0].getCellSpacing() ) ? true : false;

    //     if ( true === bAll ) {
    //         this.setApplyToAll(true);
    //         cellsArray = this.getSelectionArray();
    //         this.setApplyToAll(false);
    //     } else if ( curRow ) {
    //         cellsArray = [];
    //         const rowIndex = curRow.index;
    //         for (let index = 0, count = curRow.getCellsCount(); index < count; index++) {
    //             const startGridCol = curRow.getCellInfo(index).startGridCol;
    //             const gridSpan = curRow.getCell(index)
    //                                     .getGridSpan();
    //             const tempArray = this.getCellsPosArrayByCellsArray(
    //                                         this.getMergedCells(rowIndex, startGridCol, gridSpan));
    //             cellsArray = cellsArray.concat(tempArray);
    //         }
    //     } else if ( true === this.selection.bUse && TableSelectionType.TableCell === this.selection.type ) {
    //         cellsArray = [];
    //         for (let index = 0, count = this.selection.data.length; index < count; index++) {
    //             const rowIndex = this.selection.data[index].rowIndex;
    //             const cellIndex = this.selection.data[index].cellIndex;
    //             const startGridCol = this.content[rowIndex].getCellInfo(cellIndex).startGridCol;
    //             const gridSpan = this.content[rowIndex].getCell(cellIndex)
    //                                     .getGridSpan();
    //             const tempArray = this.getCellsPosArrayByCellsArray(
    //                                         this.getMergedCells(rowIndex, startGridCol, gridSpan));
    //             cellsArray = cellsArray.concat(tempArray);
    //         }
    //     } else {
    //         const rowIndex = this.curCell.row.index;
    //         const cellIndex = this.curCell.index;
    //         const startGridCol = this.content[rowIndex].getCellInfo(cellIndex).startGridCol;
    //         const gridSpan = this.content[rowIndex].getCell(cellIndex)
    //                                                     .getGridSpan();
    //         cellsArray = this.getCellsPosArrayByCellsArray(this.getMergedCells(rowIndex, startGridCol, gridSpan));
    //     }

    //     if ( cellsArray && 0 < cellsArray.length ) {
    //         const firstPos = cellsArray[0];
    //         const lastPos = cellsArray[cellsArray.length - 1];
    //         const firstRow = firstPos.rowIndex;
    //         const lastRow = lastPos.rowIndex;

    //         const bBorderTop = this.checkNullBorder(cellBorders.top) ? false : true;
    //         const bBorderBottom = this.checkNullBorder(cellBorders.bottom) ? false : true;
    //         const bBorderLeft = this.checkNullBorder(cellBorders.left) ? false : true;
    //         const bBorderRight = this.checkNullBorder(cellBorders.right) ? false : true;
    //         const bBorderH = this.checkNullBorder(cellBorders.insideH) ? false : true;
    //         const bBorderV = this.checkNullBorder(cellBorders.insideV) ? false : true;

    //         this.checkBorderColor(cellBorders.top);
    //         this.checkBorderColor(cellBorders.bottom);
    //         this.checkBorderColor(cellBorders.left);
    //         this.checkBorderColor(cellBorders.right);
    //         this.checkBorderColor(cellBorders.insideH);
    //         this.checkBorderColor(cellBorders.insideV);

    //         if ( false === bSpacing && !curRow) {
    //             let pos = { rowIndex: 0, cellIndex: 0};
    //             // const curRow = firstRow;
    //             let index = 0;
    //             const gridRowFirstStart = this.content[firstPos.rowIndex].getCellInfo(firstPos.cellIndex).startGridCol;
    //             let gridRowFirstEnd = 0;
    //             let gridRowLastStart = 0;
    //             let gridRowLastEnd = 0;

    //             while ( index < cellsArray.length ) {
    //                 pos = cellsArray[index];
    //                 if ( pos.rowIndex !== firstRow ) {
    //                     break;
    //                 }

    //                 const row = this.content[pos.rowIndex];
    //                 const cell = row.getCell(pos.cellIndex);

    //                 gridRowFirstEnd = row.getCellInfo(pos.cellIndex).startGridCol + cell.getGridSpan() - 1;
    //                 index++;
    //             }

    //             index = 0;

    //             while ( index < cellsArray.length ) {
    //                 pos = cellsArray[index];
    //                 if ( pos.rowIndex === lastRow ) {
    //                     break;
    //                 }

    //                 index++;
    //             }

    //             gridRowLastStart = this.content[pos.rowIndex].getCellInfo(pos.cellIndex).startGridCol;
    //             gridRowLastEnd = this.content[lastPos.rowIndex].getCellInfo(lastPos.cellIndex).startGridCol +
    //                                 this.content[lastPos.rowIndex].getCell(lastPos.cellIndex)
    //                                     .getGridSpan() - 1;

    //             if ( 0 < firstRow && bBorderTop ) {
    //                 // tslint:disable-next-line: no-shadowed-variable
    //                 let startCell = 0;
    //                 // tslint:disable-next-line: no-shadowed-variable
    //                 let endCell = 0;
    //                 let bStart = false;
    //                 let bEnd = false;

    //                 const row = this.content[firstRow - 1];
    //                 for (let curCell = 0; curCell < row.getCellsCount(); curCell++) {
    //                     const cell = row.getCell(curCell);
    //                     const startGridCol = row.getCellInfo(curCell).startGridCol;
    //                     const endGridCol = startGridCol + cell.getGridSpan() - 1;

    //                     if ( false === bStart ) {
    //                         if ( startGridCol < gridRowFirstStart ) {
    //                             continue;
    //                         } else if ( startGridCol > gridRowFirstStart ) {
    //                             break;
    //                         } else {
    //                             startCell = curCell;
    //                             bStart = true;

    //                             if ( endGridCol < gridRowFirstEnd ) {
    //                                 continue;
    //                             } else if ( endGridCol > gridRowFirstEnd ) {
    //                                 break;
    //                             } else {
    //                                 endCell = curCell;
    //                                 bEnd = true;
    //                                 break;
    //                             }
    //                         }
    //                     }

    //                     if ( false === bEnd ) {
    //                         if ( endGridCol < gridRowFirstEnd ) {
    //                             continue;
    //                         } else if ( endGridCol > gridRowFirstEnd ) {
    //                             break;
    //                         } else {
    //                             endCell = curCell;
    //                             bEnd = true;
    //                             break;
    //                         }
    //                     }
    //                 }

    //                 if ( bStart && bEnd ) {
    //                     for (let curCell = startCell; curCell <= endCell; curCell++) {
    //                         const cell = row.getCell(curCell);
    //                         result = cell.setBorder(cellBorders.top, 2) || result;
    //                     }
    //                 }
    //             }

    //             if ( lastRow < this.content.length - 1 && bBorderBottom ) {
    //                 // tslint:disable-next-line: no-shadowed-variable
    //                 let startCell = 0;
    //                 // tslint:disable-next-line: no-shadowed-variable
    //                 let endCell = 0;
    //                 let bStart = false;
    //                 let bEnd = false;

    //                 const row = this.content[lastRow + 1];
    //                 for (let curCell = 0; curCell < row.getCellsCount(); curCell++) {
    //                     const cell = row.getCell(curCell);
    //                     const startGridCol = row.getCellInfo(curCell).startGridCol;
    //                     const endGridCol = startGridCol + cell.getGridSpan() - 1;

    //                     if ( false === bStart ) {
    //                         if ( startGridCol < gridRowLastStart ) {
    //                             continue;
    //                         } else if ( startGridCol > gridRowLastStart ) {
    //                             break;
    //                         } else {
    //                             startCell = curCell;
    //                             bStart = true;

    //                             if ( endGridCol < gridRowLastEnd ) {
    //                                 continue;
    //                             } else if ( endGridCol > gridRowLastEnd ) {
    //                                 break;
    //                             } else {
    //                                 endCell = curCell;
    //                                 bEnd = true;
    //                                 break;
    //                             }
    //                         }
    //                     }

    //                     if ( false === bEnd ) {
    //                         if ( endGridCol < gridRowLastEnd ) {
    //                             continue;
    //                         } else if ( endGridCol > gridRowLastEnd ) {
    //                             break;
    //                         } else {
    //                             endCell = curCell;
    //                             bEnd = true;
    //                             break;
    //                         }
    //                     }
    //                 }

    //                 if ( bStart && bEnd ) {
    //                     for (let curCell = startCell; curCell <= endCell; curCell++) {
    //                         const cell = row.getCell(curCell);
    //                         result = cell.setBorder(cellBorders.bottom, 0) || result;
    //                     }
    //                 }
    //             }
    //         }

    //         let prevRow = firstRow;
    //         let startCell = firstPos.cellIndex;
    //         let endCell = startCell;

    //         for (let index = 0; index < cellsArray.length; index++) {
    //             const pos = cellsArray[index];
    //             // const row = this.content[pos.rowIndex];
    //             // const cell = row.getCell(pos.cellIndex);

    //             if ( prevRow !== pos.rowIndex ) {
    //                 const tempRow = this.content[prevRow];

    //                 if ( 0 < startCell && bBorderLeft ) {
    //                     result = tempRow.getCell(startCell - 1)
    //                                 .setBorder(cellBorders.left, 1) || result;
    //                 }

    //                 if ( endCell < tempRow.getCellsCount() - 1 && bBorderRight ) {
    //                     result = tempRow.getCell(endCell + 1)
    //                                 .setBorder(cellBorders.right, 3) || result;
    //                 }

    //                 for (let curCell = startCell; curCell <= endCell; curCell++) {
    //                     const tempCell = tempRow.getCell(curCell);

    //                     if ( firstRow === prevRow && bBorderTop ) {
    //                         result = tempCell.setBorder(cellBorders.top, 0) || result;
    //                     } else if ( firstRow !== prevRow && bBorderH ) {
    //                         result = tempCell.setBorder(cellBorders.insideH, 0) || result;
    //                     }

    //                     if ( lastRow === prevRow && bBorderBottom ) {
    //                         result = tempCell.setBorder(cellBorders.bottom, 2) || result;
    //                     } else if ( lastRow !== prevRow && bBorderH ) {
    //                         result = tempCell.setBorder(cellBorders.insideH, 2) || result;
    //                     }

    //                     if ( curCell === startCell && bBorderLeft ) {
    //                         result = tempCell.setBorder(cellBorders.left, 3) || result;
    //                     } else if ( curCell !== startCell && bBorderV ) {
    //                         result = tempCell.setBorder(cellBorders.insideV, 3) || result;
    //                     }

    //                     if ( curCell === endCell && bBorderRight ) {
    //                         result = tempCell.setBorder(cellBorders.right, 1) || result;
    //                     } else if ( curCell !== endCell && bBorderV ) {
    //                         result = tempCell.setBorder(cellBorders.insideV, 1) || result;
    //                     }
    //                 }

    //                 startCell = pos.cellIndex;
    //                 endCell = pos.cellIndex;
    //                 prevRow = pos.rowIndex;
    //             } else {
    //                 endCell = pos.cellIndex;
    //             }

    //             if ( cellsArray.length - 1 === index ) {
    //                 const tempRow = this.content[prevRow];

    //                 if ( 0 < startCell && bBorderLeft ) {
    //                     result = tempRow.getCell(startCell - 1)
    //                                 .setBorder(cellBorders.left, 1) || result;
    //                 }

    //                 if ( endCell < tempRow.getCellsCount() - 1 && bBorderRight ) {
    //                     result = tempRow.getCell(endCell + 1)
    //                                 .setBorder(cellBorders.right, 3) || result;
    //                 }

    //                 for (let curCell = startCell; curCell <= endCell; curCell++) {
    //                     const tempCell = tempRow.getCell(curCell);

    //                     if ( firstRow === pos.rowIndex && bBorderTop ) {
    //                         result = tempCell.setBorder(cellBorders.top, 0) || result;
    //                     } else if ( firstRow !== pos.rowIndex && bBorderH ) {
    //                         result = tempCell.setBorder(cellBorders.insideH, 0) || result;
    //                     }

    //                     if ( lastRow === pos.rowIndex && bBorderBottom ) {
    //                         result = tempCell.setBorder(cellBorders.bottom, 2) || result;
    //                     } else if ( lastRow !== pos.rowIndex && bBorderH ) {
    //                         result = tempCell.setBorder(cellBorders.insideH, 2) || result;
    //                     }

    //                     if ( curCell === startCell && bBorderLeft ) {
    //                         result = tempCell.setBorder(cellBorders.left, 3) || result;
    //                     } else if ( curCell !== startCell && bBorderV ) {
    //                         result = tempCell.setBorder(cellBorders.insideV, 3) || result;
    //                     }

    //                     if ( curCell === endCell && bBorderRight ) {
    //                         result = tempCell.setBorder(cellBorders.right, 1) || result;
    //                     } else if ( curCell !== endCell && bBorderV ) {
    //                         result = tempCell.setBorder(cellBorders.insideV, 1) || result;
    //                     }
    //                 }
    //             }
    //         }
    //     }

    //     return result;
    // }

    // private checkNullBorder(border: DocumentBorder): boolean {
    //     if ( null == border ) {
    //         return true;
    //     }

    //     if ( null != border.value || null != border.size ) {
    //         return false;
    //     }

    //     if ( null != border.color &&
    //         ( null != border.color.r || null != border.color.g || null != border.color.b)) {
    //         return false;
    //     }

    //     return true;
    // }

    // private checkAllNullBorder(): boolean {
    //     for (let curRow = 0, rowCount = this.content.length; curRow < rowCount; curRow++) {
    //         const row = this.content[curRow];
    //         for (let curCell = 0, cellCounts = row.getCellsCount(); curCell < cellCounts; curCell++) {
    //             const cell = row.getCell(curCell);
    //             if (!cell.checkAllNullBorder()) {
    //                 return false;
    //             }
    //         }
    //     }
    //     return true;
    // }

    // private checkBorderColor(border: any): void {
    //     if ( border && border.color
    //         && !(border.color instanceof DocumentColor) ) {
    //         const color = border.color;
    //         border.color = new DocumentColor(color.r, color.g, color.b);
    //     }
    // }

    // private compareBorders3(border1: DocumentBorder, border2: DocumentBorder): boolean {
    //     if ( border1.value !== border2.value || border1.size !== border2.size ) {
    //         return false;
    //     }

    //     if ( border1.color.r !== border2.color.r ||
    //         border1.color.g !== border2.color.g ||
    //         border1.color.b !== border2.color.b ) {
    //         return false;
    //     }

    //     return true;
    // }

    // private setTableBordersTop(border: DocumentBorder): void {
    //     if ( null == border && null == this.property.tableBorders.top ) {
    //         return ;
    //     }

    //     const b = ( null != border ) ? new DocumentBorder(border) : border;

    //     const history = this.getHistory();
    //     if ( history ) {
    //         history.addChange(new ChangeTableBorderTop(this, this.property.tableBorders.top, b));
    //     }

    //     this.property.tableBorders.top = b;
    // }

    // private setTableBordersBottom(border: DocumentBorder): void {
    //     if ( null == border && null == this.property.tableBorders.bottom ) {
    //         return ;
    //     }

    //     const b = ( null != border ) ? new DocumentBorder(border) : border;

    //     const history = this.getHistory();
    //     if ( history ) {
    //         history.addChange(new ChangeTableBorderBottom(this, this.property.tableBorders.bottom, b));
    //     }

    //     this.property.tableBorders.bottom = b;
    // }

    // private setTableBordersLeft(border: DocumentBorder): void {
    //     if ( null == border && null == this.property.tableBorders.left ) {
    //         return ;
    //     }

    //     const b = ( null != border ) ? new DocumentBorder(border) : border;

    //     const history = this.getHistory();
    //     if ( history ) {
    //         history.addChange(new ChangeTableBorderLeft(this, this.property.tableBorders.left, b));
    //     }

    //     this.property.tableBorders.left = b;
    // }

    // private setTableBordersRight(border: DocumentBorder): void {
    //     if ( null == border && null == this.property.tableBorders.right ) {
    //         return ;
    //     }

    //     const b = ( null != border ) ? new DocumentBorder(border) : border;

    //     const history = this.getHistory();
    //     if ( history ) {
    //         history.addChange(new ChangeTableBorderRight(this, this.property.tableBorders.right, b));
    //     }

    //     this.property.tableBorders.right = b;
    // }

    // private setTableBordersH(border: DocumentBorder): void {
    //     if ( null == border && null == this.property.tableBorders.insideH ) {
    //         return ;
    //     }

    //     const b = ( null != border ) ? new DocumentBorder(border) : border;

    //     const history = this.getHistory();
    //     if ( history ) {
    //         history.addChange(new ChangeTableBorderInsideH(this, this.property.tableBorders.insideH, b));
    //     }

    //     this.property.tableBorders.insideH = b;
    // }

    // private setTableBordersV(border: DocumentBorder): void {
    //     if ( null == border && null == this.property.tableBorders.insideV ) {
    //         return ;
    //     }

    //     const b = ( null != border ) ? new DocumentBorder(border) : border;

    //     const history = this.getHistory();
    //     if ( history ) {
    //         history.addChange(new ChangeTableBorderInsideV(this, this.property.tableBorders.insideV, b));
    //     }

    //     this.property.tableBorders.insideV = b;
    // }

    // private getCellsPosArrayByCellsArray(cellsArray: TableCell[]): ITableCellPos[] {
    //     const result: ITableCellPos[] = [];

    //     cellsArray.forEach((cell) => {
    //         result.push({ rowIndex: cell.getRow().index, cellIndex: cell.index });
    //     });

    //     return result;
    // }

    // private getMergedCells(rowIndex: number, startGridCol: number, gridSpan: number): TableCell[] {
    //     const cellsArray: TableCell[] = [];
    //     const row = this.content[rowIndex];
    //     const cellIndex = this.getCellIndexByStartGridCol(rowIndex, startGridCol, false);

    //     if ( -1 === cellIndex ) {
    //         return [];
    //     }

    //     const cell = row.getCell(cellIndex);
    //     if ( gridSpan !== cell.getGridSpan() ) {
    //         return [];
    //     }

    //     cellsArray.push(cell);

    //     for (let index = rowIndex - 1; index >= 0; index--) {
    //         const curCellIndex = this.getCellIndexByStartGridCol(index, startGridCol, false);

    //         if ( -1 === curCellIndex ) {
    //             break;
    //         }

    //         const curCell = this.content[index].getCell(curCellIndex);
    //         if ( gridSpan !== curCell.getGridSpan() ) {
    //             break;
    //         }

    //         if ( VerticalMergeType.Continue !== curCell.getVMerge() ) {
    //             break;
    //         }

    //         cellsArray.splice(0, 0, curCell);
    //     }

    //     for (let index = rowIndex + 1; index < this.content.length; index++) {
    //         const curCellIndex = this.getCellIndexByStartGridCol(index, startGridCol, false);

    //         if ( -1 === curCellIndex ) {
    //             break;
    //         }

    //         const curCell = this.content[index].getCell(curCellIndex);
    //         if ( gridSpan !== curCell.getGridSpan() ) {
    //             break;
    //         }

    //         if ( VerticalMergeType.Continue !== curCell.getVMerge() ) {
    //             break;
    //         }

    //         cellsArray.push(curCell);
    //     }

    //     return cellsArray;
    // }

    // private getNextCell(pos: any): TableCell {
    //     const row = this.content[pos.rowIndex];
    //     const rowCount = this.content.length;
    //     const cellsCount = row.getCellsCount();

    //     if ( pos.cellIndex < cellsCount - 1 ) {
    //         pos.cellIndex += 1;
    //         return row.getCell(pos.cellIndex);
    //     } else if ( pos.rowIndex < rowCount - 1 ) {
    //         pos.rowIndex += 1;
    //         pos.cellIndex = 0;
    //         return this.content[pos.rowIndex].getCell(0);
    //     }

    //     return null;
    // }

    // private getAllTableText(needPara: boolean = false): string {
    //     const contents = this.content;
    //     let text = '';
    //     contents.forEach((row) => {
    //         row.content.forEach((cell) => {
    //             text += cell.content.getSelectText(true, needPara);
    //         });
    //     });

    //     return text;
    // }

    // private getCellName(colIndex: number, rowIndex: number): string {
    //     const sLetters = 'ZABCDEFGHIJKLMNOPQRSTUVWXY';
    //     // const sDigits = '0123456789';

    //     let colName = colIndex + 1 ;
    //     rowIndex += 1;

    //     let sColName = sLetters[(colName % 26)];
    //     colName = ((colName / 26) >> 0);
    //     while (colName !== 0) {
    //         sColName = sLetters[(colName % 26)] + sColName;
    //         colName = ((colName / 26) >> 0);
    //     }

    //     // let sRowName = sDigits[(rowName % 10)];
    //     // rowName = ((rowName / 10) >> 0);

    //     // while (rowName !== 0) {
    //     //     sRowName = sDigits[(rowName % 10)] + sRowName;
    //     //     rowName = ((rowName / 10) >> 0);
    //     // }
    //     return sColName + rowIndex.toString();
    // }

    // private removeNewControl(deleteRows: number[]): void {
    //     const newControlManager = this.logicDocument.getNewControlManager();
    //     if ( newControlManager ) {
    //         const oldData = this.selection.data;

    //         this.selection.bUse = true;
    //         this.selection.data = [];
    //         let row = this.content[deleteRows[0]];
    //         this.selection.data[0] = {rowIndex: row.index, cellIndex: 0};

    //         row = this.content[deleteRows[deleteRows.length - 1]];
    //         this.selection.data[1] = {rowIndex: row.index, cellIndex: row.getCellsCount() - 1};

    //         const startPos = this.getCurContentPosInDoc(true, true, true);
    //         const endPos = this.getCurContentPosInDoc(true, false, true);

    //         newControlManager.remove(startPos, endPos);

    //         this.selection.bUse = false;
    //         this.selection.data = oldData;
    //     }
    // }

    // private getRowIndexByName(name: string): number {
    //     const sRow = name.replace(/[^0-9]+/g, '');
    //     return parseInt(sRow, 0) - 1;
    // }

    // private calcSumFormula(startCellName: string, endCellName: string, cell: TableCell): boolean {
    //     if ( cell ) {
    //         const startRowIndex = this.getRowIndexByName(startCellName);
    //         const startColIndex = this.getColIndexByName(startCellName);
    //         const endRowIndex = this.getRowIndexByName(endCellName);
    //         const endColIndex = this.getColIndexByName(endCellName);

    //         const rowCount = this.content.length;
    //         let result = 0;
    //         if ( null != startRowIndex && startRowIndex === endRowIndex && startRowIndex < rowCount ) {
    //             const row = this.content[startRowIndex];
    //             if ( row ) {
    //                 for (let cellIndex = startColIndex; cellIndex <= endColIndex; cellIndex++) {
    //                     const curCell = row ? row.getCell(cellIndex) : null;
    //                     if ( curCell && curCell !== cell ) {
    //                         const temp = curCell.content.getContentForFormula();
    //                         if ( isNaN(temp) ) {
    //                             continue;
    //                         }

    //                         result += temp;
    //                     }
    //                 }

    //                 cell.content.setContentForFormula(result.toString());
    //                 return true;
    //             }
    //         } else if ( null != startColIndex && startColIndex === endColIndex
    //             && startRowIndex < rowCount && endRowIndex < rowCount ) {
    //             for (let index = startRowIndex; index <= endRowIndex; index++) {
    //                 const row = this.content[index];
    //                 const curCell = row ? row.getCell(startColIndex) : null;
    //                 if ( curCell && curCell !== cell ) {
    //                     const temp = curCell.content.getContentForFormula();
    //                     if ( isNaN(temp) ) {
    //                         continue;
    //                     }

    //                     result += temp;
    //                 }
    //             }

    //             cell.content.setContentForFormula(result.toString());
    //             return true;
    //         } else if ( null != startColIndex && null != endColIndex) {
    //             // const startCell = this.content[startRowIndex].getCell(startColIndex);
    //             // const endCell = this.content[endRowIndex].getCell(endColIndex);

    //             // let cell1 = startCell.row.getCell(endColIndex);
    //             // if ( cell1 && cell1 === endCell ) {
    //             //     return true;
    //             // }

    //             // cell1 = endCell.row.getCell(startColIndex);
    //             // if ( cell1 && cell1 === startCell ) {
    //             //     return true;
    //             // }

    //             // const startGridCol1 = startCell.row.getStartGridCol(startColIndex);
    //             // const startGridCol2 = endCell.row.getStartGridCol(endColIndex);
    //         }
    //     }

    //     return false;
    // }

    // private calcAddFormula(formula: string, cell: TableCell): boolean {
    //     if ( cell ) {
    //         const names = formula.match(RegExp(/[^-\/\+\*]+/g));
    //         const chars = formula.match(RegExp(/[-\/\+\*]/g));

    //         if ( 1 > names.length || 1 > chars.length || (names.length - 1 !== chars.length) ) {
    //             return false;
    //         }

    //         let result = 0;
    //         names.forEach((name) => {
    //             const curCell = this.getCellByName(name);
    //             if ( curCell && curCell !== cell ) {
    //                 const temp = curCell.content.getContentForFormula();

    //                 if ( isNaN(temp) ) {
    //                     return;
    //                 }

    //                 result += temp;
    //             }
    //         });

    //         cell.content.setContentForFormula(result.toString());
    //         return true;
    //     }

    //     return false;
    // }

    // private calcMulFormula(multi1: string, multi2: string, cell: TableCell): boolean {
    //     if ( cell ) {
    //         const cell1 = this.getCellByName(multi1);
    //         const cell2 = this.getCellByName(multi2);

    //         let result = 0;
    //         if ( cell1 && cell2 && cell !== cell1 && cell !== cell2 ) {
    //             const content1 = cell1.content.getContentForFormula();
    //             const content2 = cell2.content.getContentForFormula();
    //             result = content1 * content2;
    //             result = isNaN(result) ? 0 : result;
    //         }

    //         cell.content.setContentForFormula(result.toString());
    //         return true;
    //     }

    //     return false;
    // }

    // private calcMixFormula(formula: string, cell: TableCell): boolean {
    //     if ( cell ) {
    //         const names = formula.match(RegExp(/[^-\/\+\*]+/g));
    //         const chars = formula.match(RegExp(/[-\/\+\*]/g));

    //         if ( 1 > names.length || 0 > chars.length || (names.length - 1 !== chars.length) ) {
    //             return false;
    //         }

    //         let index = 0;
    //         const cell1 = this.getCellByName(names[index++]);
    //         let temp = cell1.content.getContentForFormula();
    //         let result = isNaN(temp) ? 0 : temp;

    //         chars.forEach((char) => {
    //             const curCell = this.getCellByName(names[index++]);
    //             if ( curCell && curCell !== cell ) {
    //                 temp = curCell.content.getContentForFormula();

    //                 if ( isNaN(temp) ) {
    //                     return;
    //                 }

    //                 switch (char) {
    //                     case '+':
    //                         result += temp;
    //                         break;
    //                     case '-':
    //                         result -= temp;
    //                         break;
    //                     case '*':
    //                         result *= temp;
    //                         break;
    //                     case '/':
    //                         result /= temp;
    //                         break;
    //                 }
    //             }
    //         });

    //         result = isNaN(result) ? 0 : result;
    //         cell.content.setContentForFormula(result.toString());
    //         return true;
    //     }

    //     return false;
    // }

    // private setSelectionData(oData: any[]): void {
    //     if (!this.selection.data && !oData) {
    //         this.selection.data = null;
    //         return;
    //     }

    //     if (!this.selection.data) {
    //         this.selection.data = oData;
    //         return ;
    //         // return this.private_UpdateSelectionInCells();
    //     }

    //     if (!oData) {
    //         this.selection.data = null;
    //         return ;
    //         // return this.private_RemoveSelectionInCells();
    //     }

    //     this.selection.data = oData;

    //     for (let nIndex = 0, nCount = this.selection.data.length; nIndex < nCount; ++nIndex) {
    //         const oPos = this.selection.data[nIndex];
    //         const row = this.content[oPos.rowIndex];
    //         const cell = row.getCell(oPos.cellIndex);

    //         if (cell && VerticalMergeType.Continue !== cell.getVMerge()) {
    //             this.curCell = cell;
    //             break;
    //         } else {
    //             this.selection.data.splice(nIndex, 1);
    //         }
    //     }
    // }
}
