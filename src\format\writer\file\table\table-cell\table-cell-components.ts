import { BorderStyle } from '../../styles';
import { XmlAttributeComponent, XmlComponent } from '../../xml-components';
import { IXmlResult } from '../../xml-components/base';

interface ICellBorder {
    readonly style: BorderStyle;
    readonly size: number;
    readonly color: string;
}

class CellBorderAttributes extends XmlAttributeComponent<ICellBorder> {
    protected readonly xmlKeys: any = { style: 'w:val', size: 'w:sz', color: 'w:color' };
}

class BaseTableCellBorder extends XmlComponent {
    public setProperties(style: BorderStyle, size: number, color: string): BaseTableCellBorder {
        const attrs = new CellBorderAttributes({
            style,
            size,
            color,
        });
        this.root.push(attrs);

        return this;
    }
}

// tslint:disable-next-line: max-classes-per-file
export class TableCellBorders extends XmlComponent {
    constructor() {
        super('w:tcBorders');
    }

    public prepForXml(): IXmlResult {
        if (this.root.length > 0) {
            return super.prepForXml();
        } else {
            this.deleted = true;
        }
    }

    public addTopBorder(style: BorderStyle, size: number, color: string): TableCellBorders {
        const top = new BaseTableCellBorder('w:top');
        top.setProperties(style, size, color);
        this.root.push(top);

        return this;
    }

    public addStartBorder(style: BorderStyle, size: number, color: string): TableCellBorders {
        const start = new BaseTableCellBorder('w:start');
        start.setProperties(style, size, color);
        this.root.push(start);

        return this;
    }

    public addBottomBorder(style: BorderStyle, size: number, color: string): TableCellBorders {
        const bottom = new BaseTableCellBorder('w:bottom');
        bottom.setProperties(style, size, color);
        this.root.push(bottom);

        return this;
    }

    public addEndBorder(style: BorderStyle, size: number, color: string): TableCellBorders {
        const end = new BaseTableCellBorder('w:end');
        end.setProperties(style, size, color);
        this.root.push(end);

        return this;
    }
}

/**
 * Attributes fot the GridSpan element.
 */
// tslint:disable-next-line: max-classes-per-file
class GridSpanAttributes extends XmlAttributeComponent<{ readonly val: number }> {
    protected readonly xmlKeys: any = { val: 'w:val' };
}

/**
 * GridSpan element. Should be used in a table cell. Pass the number of columns that this cell need to span.
 */
// tslint:disable-next-line: max-classes-per-file
export class GridSpan extends XmlComponent {
    constructor(value: number) {
        super('w:gridSpan');

        this.root.push(
            new GridSpanAttributes({
                val: value,
            }),
        );
    }
}

/**
 * Vertical merge types.
 */
// export enum VMergeType {
//     /**
//      * Cell that is merged with upper one.
//      */
//     CONTINUE = 'continue',
//     /**
//      * Cell that is starting the vertical merge.
//      */
//     RESTART = 'restart',
// }

// tslint:disable-next-line: max-classes-per-file
class VMergeAttributes extends XmlAttributeComponent<{ readonly val: number }> {
    protected readonly xmlKeys: any = { val: 'w:val' };
}

/**
 * Vertical merge element. Should be used in a table cell.
 */
// tslint:disable-next-line: max-classes-per-file
export class VMerge extends XmlComponent {
    constructor(value: number) {
        super('w:vMerge');

        this.root.push(
            new VMergeAttributes({
                val: value,
            }),
        );
    }
}

// tslint:disable-next-line: max-classes-per-file
class CellFormulaAttributes extends XmlAttributeComponent<{ readonly val: string }> {
    protected readonly xmlKeys: any = { val: 'w:val' };
}

// tslint:disable-next-line: max-classes-per-file
export class CellFormula extends XmlComponent {
    constructor(value: string) {
        super('w:cellFormula');

        this.root.push(
            new CellFormulaAttributes({
                val: value,
            }),
        );
    }
}

// tslint:disable-next-line: max-classes-per-file
class CellVertAlignAttributes extends XmlAttributeComponent<{ readonly val: number }> {
    protected readonly xmlKeys: any = { val: 'w:val' };
}

// tslint:disable-next-line: max-classes-per-file
export class CellVertAlign extends XmlComponent {
    constructor(value: number) {
        super('w:cellVertAlign');

        this.root.push(
            new CellVertAlignAttributes({
                val: value,
            }),
        );
    }
}
// tslint:disable-next-line: max-classes-per-file
class CellLeftSlashAttributes extends XmlAttributeComponent<{ readonly val: number }> {
    protected readonly xmlKeys: any = { val: 'w:val' };
}
// tslint:disable-next-line: max-classes-per-file
export class CellLeftSlash extends XmlComponent {
    constructor(value: number) {
        super('w:bCellLeftSlash');
        this.root.push(
            new CellLeftSlashAttributes({
                val: value
            })
        );
    }
}

// tslint:disable-next-line: max-classes-per-file
class CellRightSlashAttributes extends XmlAttributeComponent<{ readonly val: number }> {
    protected readonly xmlKeys: any = { val: 'w:val' };
}
// tslint:disable-next-line: max-classes-per-file
export class CellRightSlash extends XmlComponent {
    constructor(value: number) {
        super('w:bCellRightSlash');
        this.root.push(
            new CellRightSlashAttributes({
                val: value
            })
        );
    }
}

// export enum WidthType {
//     /** Auto. */
//     AUTO = 'auto',
//     /** Value is in twentieths of a point */
//     DXA = 'dxa',
//     /** No (empty) value. */
//     NIL = 'nil',
//     /** Value is in percentage. */
//     PERCENTAGE = 'pct',
// }

// // tslint:disable-next-line: max-classes-per-file
// class TableCellWidthAttributes extends XmlAttributeComponent<{ readonly type: WidthType;
//     readonly width: string | number }> {
//     protected readonly xmlKeys: any = { width: 'w:w', type: 'w:type' };
// }

// /**
//  * Table cell width element.
//  */
// // tslint:disable-next-line: max-classes-per-file
// export class TableCellWidth extends XmlComponent {
//     constructor(value: string | number, type: WidthType) {
//         super('w:tcW');

//         this.root.push(
//             new TableCellWidthAttributes({
//                 width: value,
//                 type,
//             }),
//         );
//     }
// }
