/**
 * @apiDefine 0_intro 编辑器的对接
 */
/**
 * @api {} 1.编辑器介绍
 * @apiDescription hz编辑支持Web端对接，编辑器将以独立站点的形式部署，提供API给开发者。
 * <p>不支持XP系统。</p>
 * <p>hz编辑器会提供私有化部署包，部署到内网后的ip地址，引入改地址，即可直接实例化编辑器。</p>
 * <p> 需要的sdk.js 可以本地引入，也可以引用部署地址中的。</p>
 * <p> sdk地址：http://localhost:8080/sdk.js (使用实际地址替换）</p>
 * <p> 演示系统地址：http://localhost:8080/demo2.html?port=8080 (实际地址+端口号) </p>
 * <p> 开发手册：http://localhost:8080/helper/help.html (实际地址+端口号)</p>
 * @apiGroup 0_intro
 * */
/**
 * @api {function} init-step 2.编辑器初始化
 * @apiGroup 0_intro
 * @apiDescription 接入步骤：
 * <p>1) 引入编辑器的站点地址</p>
 * <p>2) 引入iframe沟通sdk的sdk文件（即sdk.js 文件，可以拉取站点或者保存到本地）</p>
 * <p>3) 需要设置编辑器容器的宽高度（编辑器包含在iframe），然后根据自己的需求修改option内容，即可初始化编辑器。</p>
 * <p>4) 调用编辑器接口实现各种业务.</p>
 * @apiParamExample  {javascript} 初始化编辑例子:
  *<script>
*   var Editor = EmrEditorSDK.Editor;
*	var editorFunction = null;
*   async function init(options) {
*       const vm = await new Editor().init(options);
*       const editor = await vm.getEditor();
*        editor.setMenuBarVisible(true);
*        // 设置事件
*       vm.setEvent({
*            nsoStructClick: (name, type, position) => {
*               console.log(name, type, position);
*           }
*       });
*       return editor; // 返回编辑器实例
*   }
*
*    // 确保init函数完成后再使用editorFunction
*   (async () => {
*		const editorDiv = document.getElementById('div-editor'); //替换成自己的div id
*       try {
*           const editorInstance = await init({
*               dom: editorDiv,// DOM元素
*               src: 'http://localhost:8080/',//替换成自己的url
*               option: {
*                   bShowMenu: true,
*                   bShowToolbar: true,
*                   isTest: false,
*                   theme: {
*                      NewControl: {
*                          ShowRegionOperator: false,
*                      }
*                  }
*              }
*           });
*           editorFunction =  editorInstance;
*       } catch (error) {
*           console.error('初始化编辑器时出错:', error);
*       }
*    })();
* </script>
*/
/**
 * @api {function} init-option 3.初始化参数
 * @apiDescription 编辑器初始化参数option介绍
 * <p>option为json结构，包含编辑的界面UI,全局性的开关，以及结构化组件的RGB值</p>
 * <p>参数都有默认值，只需要调整需要的部分</p>
* @apiParamExample  {json} option-param:
* {
    *bShowMenu: false, //！！boolean 是否显示菜单。默认为false
    *bNeedCustomFont:false,// 是否需要引入第三方字体 默认为false
    *isTest: false, //！！ 编辑器是否处于测试模式 默认为true 。业务系统中建议指定为false
    *bShowToolbar: true, //！！boolean 是否显示工具栏 默认为false
    *toolbarAlignment: 1, //number 工具栏上的按钮对齐方式 1 —居左 2 – 居中。默认为1
    *textColorChangeInRevision:1, //number	修订的时候，文字颜色是否跟随修订一起变化.1 – 变化  2 – 不变。 默认为1
    *height: 700,//编辑器高度，默认为700px
    *bEnableScroll:true,//boolean 是否开启滚动条 默认true
    *bScrollX: false, //boolean 是否显示X轴滚动条 仅当bEnableScroll = true 时候生效
    *bScrollY: true //boolean 是否显示Y轴滚动 仅当bEnableScroll = true 时候生效
    *disablePrintAction: false, //boolean 是否禁用打印操作 默认为false
    *contextMenu: //自定义右键菜单
    *[
    *    {
    *    id: '01', //菜单id 事件返回需要这个id
    *    text: '自定义菜单1'
    *    },
    *    {
    *    id: '02', //菜单id 事件返回需要这个id
    *    text: '自定义菜单2'
    *    }
    *],
    *theme: //UI配置 对象。
    *{
    *  NewControl: {
    *    DefaultBackgroundColor: '#FF00FF', //背景色 RGB值
    *    DefaultNewControlBorderColor: "#ccff00", //默认边框颜色
    *    DefaultTextColor : '',   // 默认占位符颜色
    *    DefaultMustInputTextColor : '',  // 必填项占位符颜色
    *    DefaultMustInputBorderColor : '',  // 必填项边框颜色
    *    DefaultBackgroundColorCanntEdit : '' //不可编辑背景色
    *    DefaultFocusHighLightColor : '', //悬停高亮色
    *    DefaultRegionBorderColor: '' //默认区域边框颜色
    *    ShowRegionOperator: false, //！！区域是否显示操作符 业务中建议使用false 默认为true
    *    DefaultNewControlSectionRangeBorderColor: '', //节范围边框颜色
    *    ShowSectionBorder: false, //是否显示节范围边框
    *    DefaultMessageTipColor: '', //提示符颜色
    *    DefaultMessageTipBackgroundColor: '', //提示符背景颜色
    *    MouseStyleInUneditableStruct:1 //鼠标移动到不可编辑的元素的时候，鼠标的样式。0(样式：X),1（样式：手）,2（样式：普通光标），默认是0。
    *    ConfirmStyle: 1 //下拉类型弹框显示文字  1 默认 2 确认
    *  },
    *  ErrorTextColor: '' //元素存在校验时，检测出错误后，文本的颜色,默认为红色
    *  DisableColorDiffInHeaderFooter:true //页眉页脚文字区别 默认为false
    *  DefaultBackgroundColor:'', //设置编辑器的页面外的背景色
    *  HeaderFooterOpacity:1 //页眉页脚是否需要遮罩层1 – 需要 ，0 – 不需要 。默认为1
    *}
*}
* @apiGroup 0_intro
 */

import IExternalInterface from './IExternalInterface';
import { ExternalActionProperty } from './ExternalActionProperty';
import {logger} from '../log/Logger';
import { NISTableExternalInterface } from './NISTableExternalInterface';
import Table from './Table';
import { getTheme } from '@hz-editor/theme';
// import { getTheme } from '@hz-editor/theme';
import { ErrorMessages, FunctionID, ICustomToolbarItem, ResultType } from '../commonDefines';
import { VERSION } from '../../version';
import { getMMFromPx, getPxForMM } from '../../model/core/util';


function proxy(key: string, ...arrs: any[]): any  {
    const fn = this[key];
    if (typeof fn === 'function' && key.search(/^_/) === -1) {
        return fn.apply(this, arrs);
    }
}

export class ExternalInterface extends ExternalActionProperty implements IExternalInterface  {
    // tslint:disable-next-line: sy-property-name
    public CTT: NISTableExternalInterface;
    private _docId: number;
    private _names: string[];
    constructor(host: any) {
        super(host);
        this._host = host;
        this._docId = host.docId;
        this._names = ['saveToStream', 'openDocumentWithStream'];

        if ( NURSING_FEATURE ) {
            this.CTT = new NISTableExternalInterface(this);
        }
    }

    public getEditor(): IExternalInterface {
        const props = ExternalInterface.prototype;
        // console.log(props)
        const editor: any = {};

        for (const key in props) {
            if (props.hasOwnProperty(key) && key.search(/^_/) === -1) {
                editor[key] = (...arrs: any[]) => {
                    const date: Date = new Date();
                    this._documentCore.setAdminModeTrue();
                    // this._host.setStopCursorBlur();
                    const result = proxy.call(this, key, ...arrs);
                    // console.log(typeof result)
                    // if (!this._names.includes(key)) {
                    // }
                    if (!(result instanceof Promise)) {
                        try {
                            logger.interface({startTime: date, name: key, args: arrs, result, id: this._docId});
                            return result;
                        } finally {
                            // 确保无论如何都会调用resetAdminMode
                            this._documentCore?.resetAdminMode();
                        }
                    } else {
                        return result
                            .then((res) => {
                                if (logger.isAddInterfaceLog(key) && !this._names.includes(key)) {
                                    logger.interface({startTime: date, name: key, args: arrs,
                                        result: res, id: this._docId});
                                }
                                return res;
                            })
                            .catch((err) => {
                                if (logger.isAddInterfaceLog(key)) {
                                    logger.interface({startTime: date, name: key, args: arrs,
                                        result: 1, id: this._docId});
                                }
                                if (err.message !== ErrorMessages.ThrowCustomErrorMsg) {
                                    logger.error({id: this._docId, description:
                                        `name: ${key};message: ${err.message};stack: ${err.stack};`});
                                }
                                throw err;
                            })
                            .finally(() => {
                                // 确保无论Promise成功还是失败，都会调用resetAdminMode
                                this._documentCore?.resetAdminMode();
                            });
                    }
                };
            }
        }

        if ( NURSING_FEATURE ) {
            let funs: any = this.getCTTTableOperate();
            if (funs) {
                editor.NIS = {};
                funs = funs.__proto__;
                for ( const fn in funs ) {
                    if (fn.search(/^_/) === -1) {
                        editor.NIS[fn] = (...arrs: any[]) => {
                            const date: Date = new Date();
                            this._documentCore.setAdminModeTrue();
                            // this._host.setStopCursorBlur();
                            const result = this.CTT[fn](...arrs);
                            // console.log(typeof result)
                            // if (!this._names.includes(key)) {
                            // }

                            return this._writeLog(result, date, fn, arrs);
                        };
                    }
                }
            }

            // editor.NIS =
        }

        // if ( NURSING_FEATURE ) {
        //     let funs: any = this.getCTTTableOperate();
        //     if (funs) {
        //         editor.CTT = {};
        //         funs = funs.__proto__;
        //         for ( const fn in funs ) {
        //             if (fn.search(/^_/) === -1) {
        //                 editor.CTT[fn] = (...arrs: any[]) => {
        //                     const date: Date = new Date();
        //                     this._documentCore.setAdminModeTrue();
        //                     // this._host.setStopCursorBlur();
        //                     const result = this.CTT[fn](...arrs);
        //                     // console.log(typeof result)
        //                     // if (!this._names.includes(key)) {
        //                     // }

        //                     return this._writeLog(result, date, fn, arrs);
        //                 };
        //             }
        //         }
        //     }
        // }

        delete editor['getEditor'];
        delete editor['constructor'];

        return editor as IExternalInterface;
    }

    /**
     * 新建一片文档  初始文档为A4 ，页边距均为默认值，无页眉页脚
     */
    public createNew(): number {
        return this._getOperateDocument()
            .createNew();
    }

    /**
     * 打开病历
     * @param path 可以为url 路径，也可以为base64的串值。
     * @param mode 2 为可编辑。 0 – 为 只读
     */
    public openDocumentWithString(path: string, mode: string): Promise<number> {
        return this._getOperateDocument()
            .openDocumentWithString(path, mode);
    }

    /**
     * 打开病历
     * @param path 可以为url 路径，也可以为base64的串值。
     * @param mode 2 为可编辑。 0 – 为 只读
     */
    public openDocumentWithStream(content: Blob, mode: string): Promise<number> {
        return this._getOperateDocument()
            .openDocumentWithStream(content, mode);
    }

    /**
     * 保存病历
     * @return base64编码的文档内容
     */
    public saveToString(): Promise<string> {
        return this._getOperateDocument()
            .saveToString();
    }

    /**
     * 保存病历
     * @return base64编码的文档内容
     */
    public saveToStream(sModJson: string): Promise<Blob> {
        return this._getOperateDocument()
            .saveToStream(sModJson);
    }

    /**
     * 保存为docx
     */
    public saveToDocx(): Promise<Blob> {
        return this._getOperateDocument()
            .saveToDocx();
    }

    /** 保存指定结构为Blob数据 */
    public saveStructContentToStream(name: string): Promise<Blob> {
        return this._getOperateDocument()
            .saveStructContentToStream(name);
    }

    /**
     * 保存指定结构为字符流。
     * @param sNameJson 名称集合
     */
    public saveStructContentToStreamByArray(sNameJson: string): Promise<Map<string, Blob>> {
        return this._getOperateDocument()
            .saveStructContentToStreamByArray(sNameJson);
    }

    /**
     * 保存选中区域的内容为一个子文档（base64串值）
     * @return Base64bia编码的文档内容
     */
    public saveSelectAreaToString(): Promise<string> {
        return this._getOperateDocument()
            .saveSelectAreaToString();
    }

    /**
     * 保存选中区域的内容为一个子文档（base64串值）
     * @return Base64bia编码的文档内容
     */
    public saveSelectAreaToStream(sModJson: string): Promise<Blob> {
        return this._getOperateDocument()
            .saveSelectAreaToStream(sModJson);
    }

    /**
     * 把当前打开的文件,导出成PDF或HTML文件。
     * @param nFormatType 导出HTML或PDF文件的类型
     */
    public exportToOtherFormatWithStream(nFormatType: number): Promise<Blob> {
        return this._getOperateDocument()
            .exportToOtherFormatWithStream(nFormatType);
    }


    public  exportToOtherFormat(type?: number): Promise<string> {
        return this._getOperateDocument()
            .exportToOtherFormat(type);
    }

    /**
     * 关闭当前打开的病历。
     * 讨论点：关闭后病历显示空白，回收部分资源。
     */
    public close(): void {
        this._getOperateDocument()
            .close();
        this._close();
    }

    /**
     * 关闭当前打开的病历。
     * 讨论点：关闭后病历显示空白，回收部分资源。
     */
    public  closeDoc(bRefresh?: boolean): void {
        this._getOperateDocument()
        .closeDoc(bRefresh);
    }

    /**
     * 开启管理员模式
     * @param bFlag 是否开启管理员模式： true: 开启；false: 关闭
     */
    public enableAdministratorMode(bFlag: boolean): number {
        return this._getEditDocument()
            .enableAdministratorMode(bFlag);
    }

    /**
     * 根据指定条件，用指定源文件里的的结构内容替换当前目标文档中的结构的内容。
     * @param sCurStructJson 当前文档中结构的Json
     * @param sSourceBase64String 源文件的json
     * @param sSourceStructJson 源文件的结构json
     */
    public replaceSpecificStructPropWithBackString(sCurStructJson: string, sSourceStructJson: string,
                                                   sSourceBase64String: string, title?: string): Promise<string> {
        return this._getOperateDocument()
            .replaceSpecificStructPropWithBackString(sCurStructJson, sSourceStructJson, sSourceBase64String);
    }

    /**
     * 根据指定条件，用指定源文件里的的结构内容替换当前目标文档中的结构的内容。
     * @param sCurStructJson 当前文档中结构的Json
     * @param sSourceBase64String 源文件的json
     * @param content 源文件的Blob
     */
    public replaceSpecificStructPropWithBackStream(sCurStructJson: string, sSourceStructJson: string,
                                                content: Blob, title?: string): Promise<string> {
        
        //优化版本                                            //const result = this._getOperateDocument()
        //    .replaceSpecificStructPropWithBackStreamOptimized(sCurStructJson, sSourceStructJson, content, 1);
       // this._host.handleRefresh();
        //return result;
        return this._getOperateDocument()
            .replaceSpecificStructPropWithBackStream(sCurStructJson, sSourceStructJson, content, 1);
    }

    public replaceSpecificRegionPropWithBackStream(sCurRegionJson: string, sSourceRegionJson: string,
                                                   content: Blob, title?: string): Promise<string> {
        
        //优化版本                                            //const result = this._getOperateDocument()
        //    .replaceSpecificStructPropWithBackStreamOptimized(sCurRegionJson, sSourceRegionJson, content, 2);
        //this._host.handleRefresh();
       // return result;
        const result = this._getOperateDocument()
            .replaceSpecificStructPropWithBackStream(sCurRegionJson, sSourceRegionJson, content, 2);
        this._host.handleRefresh();
        return result;
    }

    public replaceContentWithAICorrection(sName:string,sOriginalContent:string): Promise<number> {
        const nType = this._getNewControl()
            .getStructTypeByName(sName);
        let type = 2;
        if(nType === 14){
            type=1;
        }
        return this._getOperateDocument()
            .replaceContentWithAICorrection(sName,sOriginalContent,type);
    }

    public extractContentForAICorrection(sName:string, sJson?: string): Promise<string> {
        const nType = this._getNewControl()
            .getStructTypeByName(sName);
         let type = 1;//1--region 2--struct
         if(nType===14){//region
            // this._getRegion()
             //    .setRegionProp(sName, "AI-id", sName);
         }else{
            // this._getNewControl()
             //    .setNewControlProp(sName, "AI-id", sName);
             type = 2;
         }
        return this._getOperateDocument()
            .extractContentForAICorrection(sName,type);
    }

    /**
     * 开启日志模式
     * @param flag 是否开启日志模式： true: 开启；false: 关闭；
     */
    public setDebugMode(flag: boolean): void {
        this._getOperateDocument()
            .setDebugMode(flag);
    }

    /**
     * 控制菜单是否显示
     * @param bVisible 显示菜单控制标志
     */
    public setMenuBarVisible(bVisible: boolean): boolean {
        return this._getOperateDocument()
            .setMenuBarVisible(bVisible);
    }

    /** 设置自定义工具栏 */
    public setCustomToolbar(items: ICustomToolbarItem[]): number {
        return this._getOperateDocument()
            .setCustomToolbar(items);
    }


    /**
     * 设置服务端url
     * @param url 服务端url
     */
    public setRemoteUrl(url: string): number {
        return this._getOperateDocument()
            .setRemoteUrl(url);
    }


    /**设置常规编辑器的http服务端url
     * @param url 服务端url
     */
    public setHttpSvrUrl(url: string): Promise<number> {
        let tempN = this._getOperateDocument()
            .setHttpSvrUrl(url);
        return tempN;
    }

      /**
     * 设置ofd的http服务端url
     * @param url 服务端url
     */
      public setOfdSvrUrl(url: string): Promise<number> {
        let tempN = this._getOperateDocument()
            .setHttpSvrUrl(url);
        return tempN;
    }


    /**
     * 显示和隐藏指定的菜单项
     * @param sJson 右键菜单设置项集合
     */
    public disableMenuItem(sJson: string): number {
        return this._getOperateDocument()
            .disableMenuItem(sJson);
    }

    public showSystemDialog(nType: number):number{
        return this._getOperateDocument().showSystemDialog(nType);
    }

    /**
     * 显示和隐藏指定的菜单项
     * @param sJson 工具栏设置项集合
     */
    public showToolBarItem(sJson: string): boolean {
        return this._getOperateDocument()
                   .showToolBarItem(sJson);
    }

    /**
     * 是否显示超过高度的文本
     * @param bShow true: 显示， false: 不显示  默认不显示
     */
    public showShelteredText(bShow: boolean): number {
        return this._getOperateDocument()
            .showShelteredText(bShow);
    }

    /**
     * 只读模式下控制某些视图属性
     */
    public setViewPropInReadonlyMode(sJson: string): number {
        return this._getOperateDocument()
            .setViewPropInReadonlyMode(sJson);
    }

    public generateAutoTestFiles(): Promise<string[]> {
        return this._getOperateDocument()
            .generateAutoTestFiles();
    }

    public compareAutoTestFiles(autoFiles1: string[], autoFiles2: string[]): string | number {
        return this._getOperateDocument()
            .compareAutoTestFiles(autoFiles1, autoFiles2);
    }

    /**
     * 让编辑器失去或者拥有焦点
     * @param flag true: 拥有；false: 失去
     */
    public setFocus(flag: boolean): number {
        return this._getCursorPosition()
            .setFocus(flag);
    }

    /**
     * 控制工具栏是否显示
     * @param bVisible 显示工具栏控制标志
     * @param toolBarName 工具栏名称：暂时没用
     */
    public setSpecificToolBarVisible(bVisible: boolean, toolBarName?: string): boolean {
        return this._getOperateDocument()
            .setSpecificToolBarVisible(bVisible, toolBarName);
    }

    /**

     * 关闭打印预览
     * 假设处于打印预览状态，则结束，如果不处于，则无响应
     */
    public closePrintPreview(): void{
        this._getOperateDocument()
            .closePrintPreview();
    }

    /**
     * 打印文档
     * @param flag bool  控制开关 控制是否弹窗。 True – 弹框(默认值)   false – 不弹（此处需要商量）
     */
    public printDoc(flag: boolean = true): void {
        this._getOperateDocument()
            .printDoc(flag);
    }

    /**
     * 直接打印
     * @param sPrintJson 打印配置参数
     */
    public directPrintDoc(sPrintJson: string): Promise<number> {
        return this._getOperateDocument()
            .directPrintDoc(sPrintJson);
    }

    /**

     * 继续打印
     * @param sPrintJson 打印配置参数
     */
    public continuePrintDoc(sPrintJson: string): Promise<number> {
        return this._getOperateDocument()
            .continuePrintDoc(sPrintJson);
    }

    /**
     * 门诊打印
     * @param sPrintName 打印机名称
     * @param printMode 1 – Web 打印   2 – C端打印(现在只支持1打印)
     * @param pageType 纸张模式：1 -上页打印 2-下页打印
     * @param firstPageDistance 首页留白距离 mm
     * @param pageMidDistance 门诊病历上页跟下页的留白距离 单位mm
     * @param pageMidDistance 门诊病历上页跟下页的留白距离 单位mm
     * @param pageWidth 门诊病历宽，单位mm
     * @param pageHeight 门诊病历高，单位mm
     */
    public printOutpatientDoc(sPrintName: string, printMode: number, pageType: number, firstPageDistance: number,
                              pageMidDistance?: number, lastPageDistance?: number, pageWidth?: number,
                              pageHeight?: number): Promise<boolean> {
        return this._getOperateDocument()
            .printOutpatientDoc(sPrintName, printMode, pageType, firstPageDistance, pageMidDistance, lastPageDistance,
                pageWidth, pageHeight);
    }

    /**
     * 设置当前选中文本的Bold, Italic, UnderLine 属性。
     * @param propName 属性名(Bold, Italic, UnderLine)
     * @param Val 属性值
     */
    public setFontProp(propName: string, val: number | string): number {
        return this._getDocmentProperty()
            .setFontProp(propName, val);
    }

     /**
     * 获取当前选中文本的Bold, Italic, UnderLine 属性。
     */
     public getFontProp(): string {
        return this._getDocmentProperty()
            .getFontProp();
    }


    /**
     * 获得当前文档的页数
     */
    public getPageCount(): number {
        return this._getDocumentPage()
            .getPageCount();
    }

    /**
     * 设置文字水印
     * @param sText 文字水印
     * @param nMode 1 – 宽松型   2 – 紧凑型
     * @param colorType 1 - default, tencent  2 - ui normal  3 - ui dark
     */
    public setTextWaterMark(sText: string, nMode: number, colorType?: number): number {
        return this._getDocumentPage()
        .setTextWaterMark(sText, nMode, colorType);
    }

    /**
     * 删除文档的文字水印
     */
    public deleteTextWaterMark(): number {
        return this._getDocumentPage()
        .deleteTextWaterMark();
    }

    /**
     * 判断当前文档是否有水印
     */
    public hasTextWaterMark(): boolean {
        return this._getDocumentPage()
        .hasTextWaterMark();
    }

    /**
     * 设置当前段落的 FirstLineIndent, Alignment 属性
     * @param propName FirstLineIndent 首行缩进 LeftIndent 左缩进 HangingIndent 悬挂缩进 Alignment 对齐
     * @param nVal “FirstLineIndent”： 1/1000 cm；“Alignment” ：  0 表示左对齐 1 表示右对齐 2 表示两端对齐 3 表示居中对齐
     */
    public setParagraphProp(propName: string, nVal: number): number {
        return this._getDocmentProperty()
            .setParagraphProp(propName, nVal);
    }

    /**
     * 开启或关闭修订功能
     * @param flag 是否开启修订的状态值
     */
    public enableTrackRevisions(flag: boolean): number {
        return this._getRecension()
            .enableTrackRevisions(flag);
    }

    /**
     * 获取开启关闭修订状态
     */
    public getTrackRevisions(): boolean {
        return this._getRecension()
            .getTrackRevisions();
    }

    /**
     * 控制显示修订的状态。
     * @param flag 1：显示修订状态 2：显示最终状态
     */
    public showRecension(flag: number): number {
        const res = this._getRecension()
                        .showRecension(flag);
        if (res === ResultType.Success) {
            this._documentCore.resetAdminMode();
            this._host.handleRefresh();
        }
        return res;
    }



    /** 控制修订面板的显示 */
    public showRevisionPanel(flag: boolean): void {
        this._getOperateDocument().showRevisionPanel(flag);
    }

    /**
     * 当开启修订功能时，设置修订信息
     * @param strUserCode 修订者工号
     * @param strUserName 修订者姓名(必须保证姓名的唯一性,否则相同名字的人的修订会一样)
     * @param strMemo 备注
     * @param nMarkStyle 修订痕迹风格(1:单删除线、单下划线 2:双删除线、双下划线 3:三删除线、三下划线)
     * @param strMarkColor 修订颜色(可取以下颜色：红色,蓝色,绿色)
     */
    public setRecensionInfo(strUserCode: string, strUserName: string, strMemo: string,
                            nMarkStyle: number, strMarkColor: string): number {
        return this._getRecension()
            .setRecensionInfo(strUserCode, strUserName, strMemo, nMarkStyle, strMarkColor);
    }

    public getRevisionCount(): {
        total: number;           // 总修订数
        additions: number;       // 新增修订数
        deletions: number;       // 删除修订数
        users: string[];         // 参与修订的用户列表
    } {
        return this._getRecension()
            .getRevisionCount();
    }

    public getRevisionDetails(author?: string): Array<{
        userName: string;        // 修订者姓名
        userId: string;          // 修订者ID
        time: string;            // 修订时间 (ISO格式)
        level: number;           // 修订级别
        type: string;            // 修订类型：'add' | 'remove'
        value: string;           // 修订内容
    }> {
        return this._getRecension()
            .getRevisionDetails(author);
    }

    /**
     * 获取当前文档修订痕迹的状态
     * @return -1   失败 1 显示修订痕迹状态 2  最终状态
     */
    public getRecensionShowState(): number {
        return this._getRecension()
            .getRecensionShowState();
    }

    /**
     * 调整选中段落的行间距为特定行距
     * @param nType 0 – 设置整倍数的行距 3 – 固定值
     * @param nHeight 当nType 不同的时候该值取值不一样 nType =0 100 表示1倍行距 200 表示2倍行距
     * nType = 3的时候，该值表示固定高度，单位1/1000cm (比如50 表示0.05cm)
     */
    public setParaLineSpace(nType: number, nHeight: number): number {
        return this._getDocmentProperty()
            .setParaLineSpace(nType, nHeight);
    }

    public isCurrentLineEmpty(): boolean {
        return this._getDocmentProperty()
            .isCurrentLineEmpty();
    }

    /**
     * @param nType 1 – 当前段落 2 – 全文档
     * @param bFlag True -- 允许西文换行。 False – 不允许
     */
    public setWestCharBreakAttribute(nType: number, bFlag: boolean): number {
        return this._getDocmentProperty()
            .setWestCharBreakAttribute(nType, bFlag);
    }

    /**
     * 设置文档属性，一般为自定义属性
     * @param itemName 属性类型名称
     * @param content 设置的属性内容
     */
    public setFileProperty(itemName: string, content: string): number {
        return this._getDocmentProperty()
            .setFileProperty(itemName, content);
    }

    /**
     * 获取文档指定属性的值
     * @param itemName 属性类型名称
     */
    public getFileProperty(itemName: string): string {
        return this._getDocmentProperty()
            .getFileProperty(itemName);
    }

    /**
     * 设置页面边距 设置页面页边距大小(单位cm)
     * @param fPageLeft 左页边距
     * @param fPageRight 右页边距
     * @param fPageTop 上页边距
     * @param fPageBottom 下页边距
     */
    public setPageMargin(fPageLeft: number, fPageRight: number, fPageTop: number, fPageBottom: number): number {
        return this._getDocumentPage()
            .setPageMargin(fPageLeft, fPageRight, fPageTop, fPageBottom);
    }

    /**
     * 获取页面边距
     */
    public getPageMargin(): { fWidth: number; fHeight: number; fPageLeft: number; fPageRight: number; fPageTop: number; fPageBottom: number } {
        return this._getDocumentPage()
            .getPageMargin();
    }

    /**
     * 设置页面大小 为打印设置页面的纸张格式等属性
     * @param nPageFormat 纸张格式，例如: A3=3、A4=4、自定义=11 (目前只支持A3,A4,自定义)
     * @param fPageWidth 纸张的宽度 cm (只有纸张格式为自定义才有效)
     * @param fPageHeight 纸张的高度 cm (只有纸张格式为自定义才有效)
     * @param bHorOrVer 预留 (无用)
     * @param nPageLayOut 预留 (无用)
     */
    public setPageFormat(nPageFormat: number, fPageWidth?: number, fPageHeight?: number,
                         bHorOrVer?: boolean, nPageLayOut?: number): number {
        return this._getDocumentPage()
            .setPageFormat(nPageFormat, fPageWidth, fPageHeight, bHorOrVer, nPageLayOut);
    }

    /**
     * 文档显示比例设置
     * @param nType 显示比例类型
     * @param nValue 显示比例的值
     */
    public setViewProportion(nType: number, nValue?: number): number {
        return this._getDocumentPage()
            .setViewProportion(nType, nValue);
    }

    /**
     * 获取文档显示比例
     */
    public getViewProportion(): number {
        return this._getDocumentPage()
            .getViewProportion();
    }

    /**
     * 在光标当前位置插入文本内容
     * @param sText 文本内容
     */
    public insertTextAtCurrentCursor(sText: string): number {
        return this._getOperateDocument()
            .insertTextAtCurrentCursor(sText);
    }

    /**
     * 当前光标处插入字符串流文件
     * @param base64String 需要插入的文件流
     */
    public insertFileWithString(base64String: string): Promise<number> {
        return this._getOperateDocument()
            .insertFileWithString(base64String);
    }

    /**
     * 当前光标处插入字符串流文件
     * @param base64String 需要插入的文件流
     * @param options 插入文件的控制参数
     */
    public insertFileWithStream(content: Blob, options?: any | string): Promise<number> {
        return this._getOperateDocument()
            .insertFileWithStream(content, options);
    }

    /**
     * 合并文档
     * @param base64String 待合并的文件字符串流
     * @param bDifferentHeader 页眉不一样
     * @param bFirstDifferentHeader 首页页眉不一样
     */
    public mergeDocumentsWithString(base64String: string, bDifferentHeader?: boolean,
                                    bFirstDifferentHeader?: number): Promise<number> {
        return this._getOperateDocument()
            .mergeDocumentsWithString(base64String);
    }
    /**
     * 合并目标表格的指定单元格
     * @param sTableName 表格名称
     * @param sCellNames 单元格名称（多个单元格以分号隔开）
     */
    public mergeTableCell(sTableName: string, sCellNames: string): boolean {
        return this._getTable()
            .mergeTableCell(sTableName, sCellNames);
    }

    /**
     * 合并文档
     * @param content Blob
     * @param bDifferentHeader 页眉不一样
     * @param bFirstDifferentHeader 首页页眉不一样
     */

    public mergeDocumentsWithStream(content: Blob, options?: string): Promise<number> {
        // , bDifferentHeader?: boolean, bFirstDifferentHeader?: number): Promise<number> {
        return this._getOperateDocument()
            .mergeDocumentsWithStream(content, options);
    }

    /**
     * True -- 进入设计模式。 False – 离开设计模式 进入设计模式后，被隐藏的数据元将显示出来
     * @param bFlag 设计模式状态控制
     */
    public designTemplet(bFlag: boolean): number {
        return this._getEditDocument()
            .designTemplet(bFlag);
    }

    /**
     * 清洁浏览：不显示痕迹，不显示背景色；只有占位符的结构在浏览的时候会被隐藏
     * 正常模式：只有占位符的结构显示出来。
     * @param nType 1 -- 清洁模式 0 – 正常模式
     * @param nProtected 0 -- 不需要保护状态 1 -- 需要保护状态
     */
    public browseTemplet(nType: number, nProtected: number): number {
        return this._getEditDocument()
            .browseTemplet(nType, nProtected);
    }

    /**
     * 返回当前光标的页码
     * @return 当前光标的页码，失败为 0
     */
    public getCurrentCursorPage(): number {
        return this._getDocumentPage()
            .getCurrentCursorPage();
    }

    /**
     * 切换病历的编辑模式
     * @param nType 1 正常模式 2 只读模式 3 严格模式 4 只读模式且移动到修订处可弹出修订信息对话框。
     */
    public setEditMode(nType: number): number {
        return this._getEditDocument()
            .setEditMode(nType);
    }

    /**
     * 刷新编辑器UI
     * @param bAll 是否全部刷新，默认否
     * @returns 空
     */
    public updateEditor(bAll?: boolean): void {
        return this._getEditDocument()
            .updateEditor(bAll);
    }

    /**
     * 切换病历的视图模式
     * @param nType 1 分页模式 2 Web模式
     */
    public setViewMode(nType: number): number {
        return this._getEditDocument()
            .setViewMode(nType);
    }

    /**
     * 判断文档打开后是否被修改
     * @return 文档状态，如果被修改，那么返回true，反之返回false.
     */
    public isDocModified(): boolean {
        return this._getEditDocument()
            .isDocModified();
    }

    /**
     * 设置文档修改的状态，既可以设置为”修改”状态, 也可以设置为”非修改”状态。
     * @param bModify 修改状态 true：表示”修改”状态 false：表示”非修改”状态
     */
    public setDocModified2(bModify: boolean): number {
        return this._getEditDocument()
            .setDocModified2(bModify);
    }

    /**
     * 获取选中的文本
     * @return 选中的文本
     */
    public getSelectText(): string {
        return this._getEditDocument()
            .getSelectText();
    }

    /**
     * 高亮选中整个文档（不包含页眉 页脚）
     */
    public selectAllDoc(): void {
        this._getCursorPosition()
            .selectAllDoc();
    }

    /**
     * 对全文档进行只读保护或者解除只读保护
     * @param bProtect 是否只读模式 true 保护 false 解除保护
     */
    public protectDoc(bProtect: boolean): number {
        return this._getEditDocument()
        .protectDoc(bProtect);
    }

    /**
     * 文档是否处于只读保护状态。
     */
    public isProtectedMode(): boolean {
        return this._getEditDocument()
        .isProtectedMode();
    }

    /**
     * 删除文档末尾的空白行、空格、Tab键，及控制是否删除文末分页符
     * @param bBlankLine 空白行
     * @param bSpace 空格(全角 半角)
     * @param bTag Tab 字符
     * @param bDelPageBreak 是否删除文末分页符
     */
    public deleteRedundantEx(bBlankLine: boolean, bSpace: boolean, bTag: boolean, bDelPageBreak: boolean): number {
        return this._getEditDocument()
            .deleteRedundantEx(bBlankLine, bSpace, bTag, bDelPageBreak);
    }

    /**
     * fucntionID = 100 将指定的下拉框改成comboBox
     * json: {"name":["aaa1","bbb1"]}
     * @param functionID 将指定的下拉框改成comboBox
     * @param param json: {"name":["aaa1","bbb1"]}
     */
    public executeMethod(functionID: number, param: string): any {
        switch (functionID) {
            case FunctionID.ChangeComboBox: {
                return this._getNewControl()
                .changeStrcutType(param);
            }
            default: {
                return ResultType.ParamError;
            }
        }
    }

    /**
     * 在当前位置上插入新的一行
     */
    public insertNewLine(): number {
        return this._getEditDocument()
            .insertNewLine();
    }

    /**
     * 弹出插入特殊字符对话框
     */
    public insertSpecialCharacter(): number {
        return this._getEditDocument()
            .insertSpecialCharacter();
    }

    /**

     * 复制当前选定的内容。
     */
    public copy(): number {
        return this._getEditDocument()
            .copy();
    }

    /**
     * 剪切当前选定的内容。
     */
    public cut(): number {
        return this._getEditDocument()
            .cut();
    }

    /**
     * 粘贴当前剪贴板的内容。
     */
    public paste(): Promise<number> {
        return this._getEditDocument()
            .paste();
    }

    /**
     * 删除当前光标选中的内容。
     */
    public delete(): void {
        this._getEditDocument()
            .delete();
    }

    public redo(): void {
        this._getEditDocument()
            .redo();
    }

    public undo(): void {
        this._getEditDocument()
            .undo();
    }

    /**
     * 刷新文档里的所有级联。
     */
    public updateAllConnections(): number {
        return this._getNewControl()
            .updateAllConnections();
    }

    /**
     * 获取整个文档的Text数据。（需要打开文档）
     */
    public getTextFromDocument(): string {
        return this._getEditDocument()
            .getTextFromDocument();
    }

    /**
     * 返回当前打开文档的一串32位的MD5码。
     */
    public getFileMd5(): Promise<string> {
        return this._getEditDocument()
            .getFileMd5();
    }

    /**
     * 获取当前编辑器的版本号
     * @returns 编辑器版本号
     */
    public getVersion(): Promise<string> {
        return Promise.resolve(VERSION);
    }

    /**
     * 设置剪贴板格式，外部剪贴板与内部剪贴板是否带格式
     * @param bOut 外部粘贴内容
     * @param bIn Apollo格式内容
     */
    public setClipboardFormat(bOut: boolean, bIn: boolean): number {
        return this._getEditDocument()
            .setClipboardFormat(bOut, bIn);
    }

    /**
     * 设置拷贝的附加信息(如文档名称)
     * 此信息设置有值时，会触发拷贝控制，只有信息一致的情况下，才能允许互相拷贝。此信息为空的情况下，不会触发拷贝控制。
     * @param sCopyInformation 信息段设置
     */
    public setExtraCopyInformation(sCopyInformation: string): number {
        return this._getEditDocument()
            .setExtraCopyInformation(sCopyInformation);
    }

    /**
     * 设置是否可以从外部(比如IE，记事本，VS等等)拷贝到编辑器
     * @param bEnable 设置是否可以从外部(比如IE,记事本等等)拷贝到odt true – 可以false – 不可以
     */
    public enableCopyFromExternal(bEnable: boolean): number {
        return this._getEditDocument()
            .enableCopyFromExternal(bEnable);
    }

    /**
     * 光标跳到指定的页面页首
     * @param pageIndex 指定页索引
     */
    public jumpToPage(pageIndex: number): void {
        this._getCursorPosition()
            .jumpToPage(pageIndex);
    }

    /**
     * 光标跳到文档的第一页页首。
     */
    public jumpToFirstPage(): void {
        this._getCursorPosition()
            .jumpToFirstPage();
    }

    /**
     * 光标跳到文档的最后一页页首
     */
    public jumpToLastPage(): void {
        this._getCursorPosition()
            .jumpToLastPage();
    }

    /**
     * 光标跳到当前页的末尾。
     */
    public jumpToEndOfPage(): void {
        this._getCursorPosition()
            .jumpToEndOfPage();
    }

    /**
     * 光标跳到当前页的开始。
     */
    public jumpToStartOfPage(): void {
        this._getCursorPosition()
            .jumpToStartOfPage();
    }

    /**
     * 光标跳转到文件末尾
     */
    public jumpToFileEnd(): void {
        this._getCursorPosition()
            .jumpToFileEnd();
    }

    /**
     * 光标跳转到指定位置
     * @param sPosition 位置
     */
    public jumpToOnePosition(sPosition: string): number {
        return this._getCursorPosition()
            .jumpToOnePosition(sPosition);
    }

    /**
     * 获取选中区域头位置
     * @return 空值 表示失败
     */
    public getSelectionRangeStart(): string {
        return this._getCursorPosition()
            .getSelectionRangeStart();
    }

    /**
     * 获取选中区域尾位置
     * @return  空值 表示失败
     */
    public getSelectionRangeEnd(): string {
        return this._getCursorPosition()
            .getSelectionRangeEnd();
    }

    /**
     * 根据字符位置反亮选中指定的区域
     * @param sStartPos: 选中的开始位置
     * @param sEndPos: 选中的结束位置
     */
    public  selectOneArea2(sStartPos: string, sEndPos: string): void {
        this._getCursorPosition()
            .selectOneArea2(sStartPos, sEndPos);
    }

    /**

     * 在当前光标位置选中字符
     * @param nCharNumber 选中长度
     * @param direction 选择方向： 1-- 左，2 -- 右
     * @returns
     */
    public selectOneArea(nCharNumber: number, direction: number): void {
        this._getCursorPosition()
            .selectOneArea(nCharNumber, direction);
    }

    /**
     * 在当前光标处插入分页符
     */
    public insertPageBreak(): number {
        return this._getDocmentProperty()
            .insertPageBreak();
    }

    /**
     * 插入条形码
     * @param sJson text string 条形码文本 width string 像素宽度px height string 像素高度px
     * name string 元素NAM showText boolean 是否显示文本
     * alignment string center,left,right (当显示文本为true的时候 才需要设置)
     * errorCL: 暂时无用
     */
    public insertBarcode(sJson: string): string {
        return this._getDrawing()
            .insertBarcode(sJson);
    }

    /**
     * 插入二维码
     * @param sJson text string 二维码文本 name string 元素NAM
     */
    public insertQRCode(sJson: string): Promise<string> {
        return this._getDrawing()
            .insertQRCode(sJson);
    }

    /**
     * 在光标所在位置插入字符串流的图片
     * @param base64String 图片路径
     * @return 插入图片的名称
     */
    public addImageWithString(base64String: string): Promise<string> {
        return this._getDrawing()
            .addImageWithString(base64String);
    }
    public addImageWithUrl(url: string): Promise<string> {
        return this._getDrawing()
            .addImageWidthUrl(url);
    }

    /**
     * 在光标所在位置插入字符串流的可编辑图片
     * @param base64String 图片路径
     * @return 插入图片的名称
     */
    public addEditableImageWithString(base64String: string): Promise<string> {
        const isEditable = true;
        return this._getDrawing()
            .addImageWithString(base64String, isEditable);
    }

    /**
     * 在指定位置插入签名图片
     * @param sStructName 结构化名称
     * @param nMark 左右边框的前后位置（1 – 后边框的后端 0 – 前边框的前端 2 – 后边框的前端 3 – 前边框的后端）
     * @param sBase64 签名图片的链接字符串
     * @param nWidht 图片宽度
     * @param nHeight 图片高度
     */
    public addSignaturePicToSectionWithStream(sStructName: string, nMark: number,
                                              sBase64: string, nWidht: number, nHeight: number): Promise<string> {
        return this._getDrawing()
            .addSignaturePicToSectionWithStream(sStructName, nMark, sBase64, nWidht, nHeight);
    }

    /**
     * 删除一个指定的图片
     * @param sName 图片名称
     */
    public deleteImage(sName: string): number {
        return this._getDrawing()
            .deleteImage(sName);
    }

    /**
     * 对一个合法的图片对象重命名
     * @param sName 图片名称
     * @param sNewName 图片新名称
     * @return 重命名后的图片的名称
     */
    public setImageName(sName: string, sNewName: string): number {
        return this._getDrawing()
            .setImageName(sName, sNewName);
    }

    /**
     * 对一个图片设置长宽
     * @param sName 图片名称
     * @param nWidth 指定图片宽
     * @param nHeight 指定图片高
     */
    public setImageSize(sName: string, nWidth: number, nHeight: number): number {
        return this._getDrawing()
            .setImageSize(sName, nWidth, nHeight);
    }

    /**
     * 获取当前光标选中图片的名称
     */
    public getCurrentImageName(): string {
        return this._getDrawing()
            .getCurrentImageName();
    }

    /**
     * 设置指定图片是否可以删除
     * @param sName 图片名称
     * @param bDeleteProtect 是否删除保护
     */
    public setImageDeleteProtection(sName: string, bDeleteProtect: boolean): number {
        return this._getDrawing()
            .setImageDeleteProtection(sName, bDeleteProtect);
    }

    /**
     * 设置指定图片是否可以拷贝
     * @param sName 图片名称
     * @param bCopyProtect 是否拷贝保护
     */
    public setImageCopyProtection(sName: string, bCopyProtect: boolean): number {
        return this._getDrawing()
            .setImageCopyProtection(sName, bCopyProtect);
    }

    /**
     * 设置图片的自定义属性
     * @param sImageName 图片名称
     * @param sPropName 自定义属性名
     * @param sPropValue 自定义属性值
     */
    public setImageCustomProperty(sImageName: string, sPropName: string, sPropValue: string | number): number {
        return this._getDrawing()
            .setImageCustomProperty(sImageName, sPropName, sPropValue);
    }

    /**
     * 获取图片的自定义属性
     * @param sImageName 图片名称
     * @param sPropName 自定义属性名
     */
    public getImageCustomProperty(sImageName: string, sPropName: string): any {
        return this._getDrawing()
            .getImageCustomProperty(sImageName, sPropName);
    }

    /**
     * 获取文档所有的图片名称,以逗号隔开
     */
    public getAllImagesByCurrentDoc(): string {
        return this._getDrawing()
            .getAllImagesByCurrentDoc();
    }

    /**
     * 获取指定结构内容全部图片名称
     * @param sName 结构名称
     */
    public getAllImagesInStruct(sName: string): string {
        return this._documentCore
                .getAllImagesInStruct(sName);
    }

    /**
     * 在当前光标位置插入给定名称的医学公式
     * @param nType 医学公式类型: 1 － 牙齿 2 － 视野 3 － 月经 4 － 文本 13－ 瞳孔 14－ 光定位 15－ 胎动
     * @param sID 医学公式名称
     * @param sJson 医学公式内容
     */
    public insertMedicalformula(nType: number, sID: string, sJson: string): number {
        return this._getDrawing()
            .insertMedicalformula(nType, sID, sJson);
    }

    /**
     * 设置指定医学公式的类型和内容
     * @param sID 医学公式名称
     * @param sJson 医学公式内容
     */
    public setMedicalformulaText(sID: string, sJson: string): number {
        return this._getDrawing()
            .setMedicalformulaText(sID, sJson);
    }

    public setRecensionProtectMode(bFlag: boolean): number {
        return this._getRecension()
            .setRecensionProtectMode(bFlag);
    }

    public clearUndoList(): void {
        this._getOperateDocument()
            .clearUndoList();
    }

    /**
     * 在当前光标位置插入表格。
     * @param name 表格名称
     * @param col 列数
     * @param row 行数
     * @param hasTitlerow 是否有头行
     * @param nNumbers 头行数
     */
    public insertTable(name: string, col: number , row: number, hasTitlerow?: boolean, nNumbers?: number ): number {
        return this._getTable()
            .insertTable(name, col , row, hasTitlerow, nNumbers);
    }

    public insertTableWithParament(name: string, col: number, row: number, sJson: string): number {
        return this._getTable()
            .insertTableWithParament(name, col, row, sJson);
    }

    /** 将光标移动至当前表格所选行的最后一个单元格 */
    public moveCursorToTableRowEnd(): boolean {
        return this._getTable()
            .moveCursorToTableRowEnd();
    }

    /**
     * 返回单元格内结构化元素名称集合
     * @param sTalbeName 表格名称
     * @param sCellName 单元格名称
     * @return 结构化元素name，以英文逗号隔开
     */
    public getStructsNameByCell(sTalbeName: string, sCellName: string): string {
        return this._getTable()
            .getStructsNameByCell(sTalbeName, sCellName);
    }

    /**
     * 批量设置指定表格单元格的文本（传数组）
     * @param sName 表格名称
     * @param sJsonContent 单元格内容的json数组
     */
    public putCellContentByArray(sName: string, sJsonContent: string): number {
        return this._getTable()
            .putCellContentByArray(sName, sJsonContent);
    }

    /**
     * 对指定表格的某行之前（后）插入数行。
     * @param name 表格名称
     * @param nIndex 插入的参照位置在第几行//从1开始整数(1表示表格第一行上面插入行)
     * @param ncount 插入几行
     * @param nDirection 1 – 往下新增行 0 – 往上新增行
     */
    public incMultiRows(name: string, nIndex: number , nCount: number, nDirection: number): number {
        return this._getTable()
            .incMultiRows(name, nIndex, nCount, nDirection);
    }

    /**
     * 删除指定行
     * @param sName 表格名称
     * @param index 删除行位置
     * @param nCount 删除行数据
     * @param nDirection 1 – 往下新增行 0 – 往上新增行
     */
    public delMultiRows(name: string, index: number, nCount: number, nDirection: number): number {
        return this._getTable()
            .delMultiRows(name, index, nCount, nDirection);
    }

    /**
     * 设置表格属性（目前只支持自定义属性）
     * @param sName 表格名称
     * @param sProp 属性json
     */
    public setTableProp(sName: string, sProp: string): number {
        return this._getTable()
            .setTableProp(sName, sProp);
    }

    /**
     * 设置表格单元格公式
     * @param sName 表格名称
     * @param sCell 单元格
     * @param sFormula 公式
     */
    public setTableCellFormula(sName: string, sCell: string,sFormula:string): number{
        return this._getTable()
            .setTableCellFormula(sName, sCell,sFormula);
    }

    /**
     * 获取表格属性（目前只支持自定义属性）
     * @param sName 表格名称
     * @param sProp 属性json
     */
    public getTableProp(sName: string, sProp: string): string {
        return this._getTable()
            .getTableProp(sName, sProp);
    }

    /**
     * 获取表格内结构化元素的信息
     * @param sName 表格名称
     * @param sRev 预留参数
     */
    public getTableXmlInfoByParament(sName: string, sRev: string): string {
        return this._getTable()
            .getTableXmlInfoByParament(sName, sRev);
    }

    /**
     * 获取表格内单元格的内容
     * @param sName 表格名称
     * @param sJson 条件json
     */
    public getTableContentByParament(sName: string, sJson: string): string {
        return this._getTable()
            .getTableContentByParament(sName, sJson);
    }

    /**
     * 获取当前文档中所有表格名字
     */
    public getAllTableNamesByCurrentDoc(): string {
        return this._getTable()
            .getAllTableNamesByCurrentDoc();
    }

    public protectTable(sTable: string , bEditorProtect: boolean): number {
        return this._getTable()
            .protectTable(sTable, bEditorProtect);
    }

    public protectTableCell(sTable: string, sCellName: string, bEditorProtect: boolean): number {
        return this._getTable()
            .protectTableCell(sTable, sCellName, bEditorProtect);
    }


    public getTableColInfo(sTable: string, colID: string): string {
        return this._getTable()
                    .getTableColInfo(sTable, colID);
    }

    /**
     * 获取指定表格指定行的结构化元素信息
     * @param sTableName 表格名称
     * @param nRowIndex 行索引，从1开始
     * @returns 该行中的结构化元素信息，格式与 getTableXmlInfoByParament 相同
     */
    public getTableRowStructInfo(sTableName: string, nRowIndex: number): string {
        return this._getTable()
            .getTableRowStructInfo(sTableName, nRowIndex);
    }

    /**
     * 获取当前光标所在表格行的索引
     * @returns 当前光标所在表格行的索引，从1开始；如果光标不在表格内，则返回-1
     */
    public getCurrentTableRowIndex(): number {
        return this._getTable()
            .getCurrentTableRowIndex();
    }

    /**
     * 获取指定表格的行数
     * @param sTableName 表格名称
     * @returns 表格的行数，如果表格不存在则返回0
     */
    public getTableRowCount(sTableName: string): number {
        return this._getTable()
            .getTableRowCount(sTableName);
    }
    
    /**
     * 获取指定表格的列数
     * @param sTableName 表格名称
     * @returns 表格的列数，如果表格不存在则返回0
     */
    public getTableColumnCount(sTableName: string): number {
        return this._getTable()
            .getTableColumnCount(sTableName);
    }


    /**
     * 当前光标位置插入区域
     * @param sName 区域名称
     */
    public insertRegionAtCurrentCursor(sName: string ): string {
        return this._getRegion()
            .insertRegionAtCurrentCursor(sName);
    }

    public async  swapRegions(sRegionA:string,sRegionB:string): Promise<number>{

        const regionA = this._documentCore.getRegionByName(sRegionA);
        const regionB = this._documentCore.getRegionByName(sRegionB);
        if( !regionA || !regionB)
            return (ResultType.ParamError);

        function isValidJson(obj: { property: {} | {}; }) {
            return typeof obj === 'object' && obj !== null;
        }

       const json = {
        "NeedregionStruct":0
       };
       const contentA =  await this._getOperateDocument()
        .saveRegionContentToStream(sRegionA,JSON.stringify(json));

       const contentB = await this._getOperateDocument()
        .saveRegionContentToStream(sRegionB,JSON.stringify(json));

        const resA = await this._getOperateDocument()
            .setRegionFileLinkWithStreamForSort(sRegionA, contentB);

        if(resA !== 0 )
            return resA;

        const resB = await this._getOperateDocument()
            .setRegionFileLinkWithStreamForSort(sRegionB, contentA);

        if(resB !== 0 )
            return resB;

        const proAString = this._getRegion().getRegionPropByArray(sRegionA);
        const proBString = this._getRegion().getRegionPropByArray(sRegionB);

        const proA = JSON.parse(proAString);
        const proB = JSON.parse(proBString);

        // 处理 proA
        const firstRegionAKey = Object.keys(proA)[0]; // 获取第一个区域的键
        const propertyA = proA[firstRegionAKey]?.property; // 使用可选链操作符安全提取
        const transformedJsonA = {
            property: {}
        };

        // 确保 propertyA 存在
        if (propertyA) {
            for (const key in propertyA) {
                transformedJsonA.property[key] = propertyA[key];
            }
        }

        // 处理 proB
        const firstRegionBKey = Object.keys(proB)[0]; // 获取第一个区域的键
        const propertyB = proB[firstRegionBKey]?.property; // 使用可选链操作符安全提取
        const transformedJsonB = {
            property: {}
        };

        // 确保 propertyB 存在
        if (propertyB) {
            for (const key in propertyB) {
                transformedJsonB.property[key] = propertyB[key];
            }
        }
        if (isValidJson(transformedJsonB) && isValidJson(transformedJsonA)){
            this._getRegion().setRegionPropByArray(sRegionA, JSON.stringify(transformedJsonB));
            this._getRegion().setRegionPropByArray(sRegionB, JSON.stringify(transformedJsonA));
            this._getRegion().setRegionName(sRegionA, "hzeditor-2024");
            this._getRegion().setRegionName(sRegionB, sRegionA);
            this._getRegion().setRegionName("hzeditor-2024", sRegionB);
        }
        return resA;
    }

    // public async sortRegions(sJson:string):Promise<number>{
    //     const regionNames = await this._getRegion().getFirstLevelRegionNames().split(',');
    //     const order = JSON.parse(sJson); // 解析传入的排序 JSON

    //     // 创建一个索引映射以便于排序
    //     const indexMap: { [key: string]: number } = {};
    //     order.forEach((name: string, index: number) => {
    //         indexMap[name] = index;
    //     });

    //     let lastSwapResult = 0;
    //     // 排序
    //     for (let i = 0; i < regionNames.length; i++) {
    //         for (let j = i + 1; j < regionNames.length; j++) {
    //             const indexI = indexMap[regionNames[i]] ?? Infinity; // 默认到最大值
    //             const indexJ = indexMap[regionNames[j]] ?? Infinity; // 默认到最大值
    //             if (indexI > indexJ) {
    //                 lastSwapResult = await this.swapRegions(regionNames[i], regionNames[j]);
    //             }
    //         }
    //     }
    //     return lastSwapResult;
    // }

    public async sortRegions(sJson: string): Promise<number> {
        const regionNames = await this._getRegion().getFirstLevelRegionNames().split(',');
        const order = JSON.parse(sJson); // 解析传入的排序 JSON

        // 创建一个索引映射以便于排序
        const indexMap: { [key: string]: number } = {};
        order.forEach((name: string, index: number) => {
            indexMap[name] = index;
        });

        // 记录需要交换的区域及其目标位置
        const targetPositions = regionNames.map(name => indexMap[name] ?? Infinity);

        // 进行位置交换
        let lastSwapResult = 0;
        for (let i = 0; i < regionNames.length; i++) {
            while (targetPositions[i] !== i) {
                const targetIndex = targetPositions[i];
                // 进行交换
                lastSwapResult = await this.swapRegions(regionNames[i], regionNames[targetIndex]);

                // 更新目标位置
                const temp = targetPositions[i];
                targetPositions[i] = targetPositions[targetIndex];
                targetPositions[targetIndex] = temp;

                // 更新 regionNames 数组
                const tempName = regionNames[i];
                regionNames[i] = regionNames[targetIndex];
                regionNames[targetIndex] = tempName;
            }
        }

        return lastSwapResult;
    }

    /**
     * 往指定区域的插入一个文件流
     * @param strName 区域名称
     * @param cContent 链接文件的base64 数据流
     */

    public setRegionFileLinkWithStream(strName: string, cContent: Blob, option?: string): Promise<number> {
        return this._getOperateDocument()
            .setRegionFileLinkWithStream(strName, cContent, option);

    }

    /**
     * 删除指定区域内部末尾的空白,包括（空行 空格 Tab字符等，分页符可选）
     * @param strName 区域名称
     * @param bPageBreak True – 删除分页符 False – 不删除分页符
     */
    public deleteRedundantByRegionName(strName: string, bPageBreak: boolean): number {
        return this._getRegion()
            .deleteRedundantByRegionName(strName, bPageBreak);
    }

    /**
     * 在指定区域的后面插入一个区域，并且指定插入区域的名称
     * @param sRegion 插入的区域名称
     * @param sPrveRegion 指定插入区域的名称
     */
    public insertRegionAfterOneRegion(sRegion: string, sPrveRegion: string): number {
        return this._getRegion()
            .insertRegionAfterOneRegion(sRegion, sPrveRegion);
    }

    /**
     * 获得当前光标所在的region的名称
     */
    public getCurrentRegionName(): string {
        return this._getRegion()
            .getCurrentRegionName();
    }

    /**
     * 获取指定一个区域的脏标记状态
     * @param sRegionName 区域名称
     */
    public getRegionModifyFlag(sRegionName: string): number {
        return this._getRegion()
            .getRegionModifyFlag(sRegionName);
    }

    public getRegionText(name: string): string {
        return this._getRegion()
            .getRegionText(name);
    }

    /**
     * 获取整个文档区域的脏标记符合某个状态的名称集合/可以只获取最外层区域，或者所有层次的区域
     * @param nOnlyFirstRegions 1 – 只获取最外层 0 --- 获取所有区域
     * @param nModifyFlag 1 – 只获取修改过的 0 --- 只获取未修改过的
     */
    public getAllRegionsByModifyFlag(nOnlyFirstRegions: number, nModifyFlag: number): string {
        return this._getRegion()
            .getAllRegionsByModifyFlag(nOnlyFirstRegions, nModifyFlag);
    }

    /**
     * 将当前文档所有的区域记录脏标记置位为0
     */
    public cleanAllRegionsModifyFlag(): number {
        return this._getRegion()
            .cleanAllRegionsModifyFlag();
    }

    /**
     * 获得区域属性
     * @param sName 区域名称
     * @param sProp 属性名
     */
    public getRegionProp(sName: string, sProp: string): any {
        return this._getRegion()
            .getRegionProp(sName, sProp);
    }

    /**
     * 设置区域属性
     * @param sName 区域名称
     * @param sProp 属性名
     * @param sValue 属性值
     */
    public setRegionProp(sName: string, sProp: string, sValue: any): number {
        return this._getRegion()
            .setRegionProp(sName, sProp, sValue);
    }

    /**
     * 设置区域属性
     * @param sName 区域名称
     * @param sJsons 区域属性集合
     */
    public setRegionPropByArray(sName: string, sJsons: string): number {
        return this._getRegion()
            .setRegionPropByArray(sName, sJsons);
    }

    /**
     * 一次获取指定结构化元素的所有属性。
     * @param sName 区域名称
     */
    public getRegionPropByArray(sName: string): string {
        return this._getRegion()
            .getRegionPropByArray(sName);
    }

    /**
     * 设置区域的边框的显示模式
     * @param nViewType 模式1：当光标不在任何一个区域内时，区域边框都常显灰色，当光标在某一个区域内时，该区域边框显示以前的彩色。
     */
    public setRegionBorderViewMode(nViewType: number): number {
        return this._getRegion()
            .setRegionBorderViewMode(nViewType);
    }

    /** 设置指定区域是否常显边框 */
    public setRegionBorderVisible(sRegion: string, bShow: boolean): number {
        return this._getRegion()
            .setRegionBorderVisible(sRegion, bShow);
    }

    /** 设置所有区域是否常显边框 */
    public setAllRegionsBorderVisible(bShow: boolean): number {
        return this._getRegion()
            .setAllRegionsBorderVisible(bShow);
    }

    public setRegionName(sName: string, sNewName: string): number {
        return this._getRegion()
            .setRegionName(sName, sNewName);
    }

    /**
     * 删除一个区域
     * @param sName 区域名称
     * @param lFlag 1：删除区域结构（内容不删除）2：删除区域内容 3：删除区域和内容
     */
    public deleteRegion(sName: string, lFlag: number): number {
        return this._getRegion()
            .deleteRegion(sName, lFlag);
    }

    /**
     * 获取当前光标所在区域的最外层区域名称
     */
    public getOutestRegionNameByCurrentCursor(): string {
        return this._getRegion()
            .getOutestRegionNameByCurrentCursor();
    }

    /**
     * 获取指定结构（数据元或者数据组）名称的最近的父级区域的名称
     * @param sStructsName 元素，节，区域。
     */
    public getFatherRegionNameOfOneStruct(sStructsName: string): string {
        return this._getRegion()
            .getFatherRegionNameOfOneStruct(sStructsName);
    }

    /**
     * 按照某个属性名（可以同时带属性值）进行筛选，筛选出符合条件的结构名
     * @param sRegionName 区域名
     * @param sPropName 属性名
     * @param sPropValue 属性值
     * @return 在指定区域内，返回符合条件的所有的结构名称 区域跟结构以”|”隔开，批次之间以”,” 隔开
     */
    public filterStructsByPropInRegion(sRegionName: string, sPropName: string, propValue: any): string {
        return this._getRegion()
            .filterStructsByPropInRegion(sRegionName, sPropName, propValue);
    }

    /**
     * 依次返回当前文档中Region名称列表
     */
    public getAllRegionNamesByCurrentDoc(): string {
        return this._getRegion()
            .getAllRegionNamesByCurrentDoc();
    }

    /**
     * 返回文档第一层Region名称列表
     */
    public getFirstLevelRegionNames(): string {
        return this._getRegion()
            .getFirstLevelRegionNames();
    }

    /**
     * 对指定的某个区域设置是否只读，其他剩余的区域设置是否只读
     * @param sCurrentRegionName 区域名称
     * @param bCurrent 指定区域只读标识
     * @param bLef 除了指定区域外，其他最外层区域的只读标识
     */
    public setRegionsReadOnlyProp(sCurrentRegionName: string, bCurrent: boolean, bLef: boolean): number {
        return this._getRegion()
            .setRegionsReadOnlyProp(sCurrentRegionName, bCurrent, bLef);
    }

    /**
     * 获得指定区域尾位置索引
     * @param sName 区域名称
     */
    public getRegionEnd(sName: string): string {
        return this._getRegion()
            .getRegionEnd(sName);
    }

    /**
     * 获得指定区域头位置索引
     * @param sName 区域名称
     */
    public getRegionBegin(sName: string): string {
        return this._getRegion()
            .getRegionBegin(sName);
    }

    /**
     * 在指定区域的前端或者后端加入一个新行。
     * @param sRegion 区域名称
     * @param nPosType 1 – 区域前端 2 – 区域后端
     */
    public addNewLineForRegion(sRegion: string, nPosType: number): number {
        return this._getRegion()
            .addNewLineForRegion(sRegion, nPosType);
    }

    /**
     * 将当前文档的所有内容变成一个区域
     * @param sRegionName 区域名称
     */
    public changeAllFileToOneRegion(sRegionName: string): string {
        return this._getRegion()
            .changeAllFileToOneRegion(sRegionName);
    }

    /**
     * 设置指定区域内容文本
     * @param sName 区域名称
     * @param sText 区域内容
     */
    public setRegionText(sName: string, sText: string): number {
        return this._getRegion()
            .setRegionText(sName, sText);
    }

    /**
     * 设置指定区域内所有子元素的反向编辑属性为true或者false
     * @param sRegionName 区域名称
     * @param bReverseEdit 反向编辑属性值
     * @returns 操作结果
     */
    public setRegionSubElementsReverseEdit(sRegionName: string, bReverseEdit: boolean): number {
        return this._getRegion()
            .setRegionSubElementsReverseEdit(sRegionName, bReverseEdit);
    }

    /**
     * 光标跳转到某一个区域的前面或者后面
     * @param strName 区域名称
     * @param bBack true – 区域外的行开始处 false – 区域前的行结束处。
     */
    public cursorJumpOutOfOneRegion(strName: string, bBack: boolean): number {
        return this._getRegion()
            .cursorJumpOutOfOneRegion(strName, bBack);
    }

    /**
     * 显示、隐藏缓存的区域
     * @param name 区域名称
     * @param flag 显示、隐藏
     */
    public collapseRegion(name: string, flag: boolean): number {
        return this._getRegion()
            .collapseRegion(name, flag);
    }

    /**
     * 选中指定名称的区域
     * @param sName 区域名称
     * @param bOnlyContent true － 只选中区域的内容 false － 选中整个区域结构（暂时没用
     */
    public selectOneRegion(sName: string, bOnlyContent?: boolean): number {
        return this._getRegion()
            .selectOneRegion(sName, bOnlyContent);
    }

    /**
     * 以XML格式依次返回指定区域的Region, Section 和NewControl的结构化元素信息
     * @param name 区域名称
     * @param sRev 保留参数 目前无用
     */
    public getRegionXmlInfoByParament(name: string, sRev?: string): string {
        return this._getRegion()
            .getRegionXmlInfoByParament(name, sRev);
    }

    /**
     * 返回当前光标位置的表格名
     */
    public getTableNameByCurrentCursor(): string {
        return this._getTable()
            .getTableNameByCurrentCursor();
    }

    /**
     * 获取光标所在的表格的单元格名称
     */
    public getTableCellNameByCurrentCursor(): string {
        return this._getTable()
            .getTableCellNameByCurrentCursor();
    }

    /**
     * 获取指定表格指定单元格的文本内容
     * @param sTableName 表格名称
     * @param sCellName 单元格名称
     */
    public getCellContent(sTableName: string, sCellName: string): string {
        return this._getTable()
            .getCellContent(sTableName, sCellName);
    }

    /**
     * 获取指定表格指定单元格中的结构化元素名称列表
     * @param sTableName 表格名称
     * @param sCellName 单元格名称
     * @return 结构化元素名称，多个名称以英文逗号分隔
     */
    public getStructNamesByCell(sTableName: string, sCellName: string): string {
        return this._getTable()
            .getStructsNameByCell(sTableName, sCellName);
    }

    /**
     * 获取指定表格指定单元格所在列的文本内容
     * @param sTableName 表格名称
     * @param sCellName 单元格名称
     */
    public getColContent(sTableName: string, sCellName: string): string {
        return this._getTable()
            .getColContent(sTableName, sCellName);
    }

    /**
     * 获得指定名称结构化元素的最近的父级结构化元素名称
     * @param sName 结构化元素名称
     */
    public getFatherStructName(sName: string): string {
        return this._getNewControl()
            .getFatherStructName(sName);
    }

    /**
     * 获得选中区域中结构化元素的名称列表
     * @return 选中区域中结构化元素的名称列表, 空则表示选中区域中无结构化元素. 多个结构化元素 采用逗号分隔，依此排列。节跟元素之间用分号隔开。
     */
    public getStructBySelectArea(): string {
        return this._getNewControl()
            .getStructBySelectArea();
    }

    /**
     * 获取范围内名称
     * @returns 区域、结构化名称
     */
     public getStructsNameListFromSelectedArea(): string {
        return this._getNewControl()
            .getStructsNameListFromSelectedArea();
    }

    /**
     * 一次获取指定结构化元素的所有属性。
     * @param sName 结构化名称
     */
    public getStructPropByArray(sName: string): string {
        return this._getNewControl()
            .getStructPropByArray(sName);
    }

    /**
     * 获取当前光标的结构化名称
     */
    public getCurrentStructName(): string {
        return this._getNewControl()
            .getCurrentStructName();
    }

    /**
     * 根据名称获取结构的类型，结构包括节，元素及区域
     * @param name 结构化名称
     */
    public getStructTypeByName(name: string): number {
        return this._getNewControl()
            .getStructTypeByName(name);
    }

    /**
     * 设置checkBox的勾选状态
     * @param name 结构化名称
     * @param bChecked 选中状态
     */
    public setCheckboxStatus(name: string, bChecked: boolean): number {
        return this._getNewControl()
            .setCheckboxStatus(name, bChecked);
    }

    /**
     * 设置checkBox的文本
     * @param name 结构化名称
     * @param sCaption 展示文本
     */
    public setCheckboxCaption(name: string, sCaption: string): number {
        return this._getNewControl()
            .setCheckboxCaption(name, sCaption);
    }

    /**
     * 读取checkBox的文本
     * @param name 获取文本
     */
    public getCheckboxCaption(name: string): string {
        return this._getNewControl()
            .getCheckboxCaption(name);
    }

    /**
     * 设置checkBox的 code 文本
     * @param name checkBox名称
     * @param sCode 对应的属性值
     */
    public setCheckboxCode(name: string, sCode: string): number {
        return this._getNewControl()
                   .setCheckboxCode(name, sCode);
    }

    /**
     * 获取checkBox的 code 文本
     * @param name checkBox名称
     */
    public getCheckboxCode(name: string): string {
        return this._getNewControl()
                   .getCheckboxCode(name);
    }

    /**
     * 返回所有设置过组的checkbox的组名
     */
    public getAllGroupCheckboxName(): string {
        return this._getNewControl()
            .getAllGroupCheckboxName();
    }

    /**
     * 返回指定组的checkbox的check状态
     * @param sGroupName 组名
     */
    public getGroupCheckboxStatus(sGroupName: string): number {
        return this._getNewControl()
            .getGroupCheckboxStatus(sGroupName);
    }

    /**
     * 返回指定checkbox的组名
     * @param sCheckBox 结构化名称
     */
    public getCheckboxGroupName(sCheckBox: string): string {
        return this._getNewControl()
            .getCheckboxGroupName(sCheckBox);
    }

    /**
     * 对指定组的checkbox的最后一个checkbox显示特定的信息
     * @param sGroupName 组名
     * @param sInfo 特定的信息
     */
    public showInfoToGroupCheckbox(sGroupName: string, sInfo: string): number {
        return this._getNewControl()
            .showInfoToGroupCheckbox(sGroupName, sInfo);
    }

    public showInfoToCheckBoxs(sCheckBoxName: string, sInfo: string): number {
        return this._getNewControl()
            .showInfoToCheckBoxs(sCheckBoxName, sInfo);
    }

    /**
     * 设置指定名称RadioButton的Code和Value值(清空原有值)
     * @param sName 结构化名称
     * @param sJson code和value的json
     */
    public setRadioButtonCodeAndValueByArray(sName: string, sJson: string): number {
        return this._getNewControl()
            .setRadioButtonCodeAndValueByArray(sName, sJson);
    }

    /**
     * 新增 指定名称RadioButton的Code和Value值(保留原有值)
     * @param sName 结构化名称
     * @param sJson code和value的json
     */
    public addRadioButtonCodeAndValueByArray(sName: string, sJson: string): number {
        return this._getNewControl()
            .setRadioButtonCodeAndValueByArray(sName, sJson, false);
    }

    /**
     * 获取指定名称RadioButton当前选中项的Value值
     * @param sName 结构化名称
     */
    public getRadioButtonSelectItemValue(sName: string): string {
        return this._getNewControl()
            .getRadioButtonSelectItemValue(sName);
    }

     /**
     * 设置指定按钮元素目标项的文本颜色
     * @param sName 按钮元素名称
     * @param index 选择项位置索引（0-）
     * @param color 颜色值
     */
    public setRadioButtonItemTextColor(sName: string, index: string, color: string): number {
        return this._getNewControl()
            .setRadioButtonItemTextColor(sName, index, color);
    }

     /**
      * 根据索引选中指定名称RadioButton的指定项
      * @param sName 按钮元素名称
      * @param index 选择索引：选择多项时，用逗号分隔
      */
    public selectRadioButtonItemByIndex(sName: string, index: string): number {
        return this._getNewControl()
            .selectRadioButtonItemByIndex(sName, index);
    }

     /**
      * 获取指定名称RadioButton当前选中项的索引值
      * @param sName 按钮元素名称
      */
    public getRadioButtonSelectedIndexes(sName: string): string {
        return this._getNewControl()
            .getRadioButtonSelectedIndexes(sName);
    }

    /**
     * 选中指定名称的RadioButton的指定Value项
     * @param sName 结构化名称
     * @param sValue 选中的值
     */
    public selectRadioButtonItemByValue(sName: string, sValue: string): number {
        return this._getNewControl()
            .selectRadioButtonItemByValue(sName, sValue);
    }

    /**
     * 删除指定名称RadioButton的所有项
     * @param sName 结构化名称
     */
    public deleteAllRadioButtonItem(sName: string): number {
        return this._getNewControl()
            .deleteAllRadioButtonItem(sName);
    }

    /**
     * 获取指定名称控件的value数组
     * @param sName 结构化名称
     */
    public getRadioButtonValueWithArray(sName: string): string {
        return this._getNewControl()
            .getRadioButtonValueWithArray(sName);
    }

    /**
     * 选中指定名称RadioButton的指定Code项
     * @param sName 结构化名称
     */
    public getRadioButtonCodeWithArray(sName: string): string {
        return this._getNewControl()
            .getRadioButtonCodeWithArray(sName);
    }

    /**
     * 选中指定名称RadioButton的指定Code项
     * @param sName 结构化名称
     * @param sCode code值
     */
    public selectRadioButtonItemByCode(sName: string, sCode: string): number {
        return this._getNewControl()
            .selectRadioButtonItemByCode(sName, sCode);
    }

    /**
     * 将指定名称RadioButton 选项清空
     * @param sName 结构化名称
     */
    public clearRadioButtonCheckItem(sName: string): number {
        return this._getNewControl()
            .clearRadioButtonCheckItem(sName);
    }

    /**
     * 在光标处插入文本框 或者 节。
     * @param sName Textbox名称
     * @param sText 内容值
     * @param nType 类型 根据EXTERNAL_OUTER_STRUCT_TYPE
     */
    public insertStructAtCurrentCursor(sName: string, sText: string, nType: number): number {
        return this._getNewControl()
            .insertStructAtCurrentCursor(sName, sText, nType);
    }

    /**
     * 设置指定结构化元素 的文本内容
     * @param sName 结构化名称
     * @param sText 文本内容
     */
    public setStructText(sName: string, sText: string): number {
        return this._getNewControl()
            .setNewControlText(sName, sText);
    }

    /**
     * 用波浪线标记选中的文本
     * @param options 波浪线选项配置
     * @returns 返回波浪线ID，失败返回null
     */
    public markTextWithWave(options?: {content?: string, type?: number}): string | null {
        return this._getDocmentProperty()
            .markTextWithWave(options);
    }

    /**
     * 将所有结构化元素（元素 节）边框设置成true falae
     * @param bFlag /
     */
    public setStructsBorderVisible(bFlag: boolean): number {
        return this._getNewControl()
            .setStructsBorderVisible(bFlag);
    }

    /**
     * 将所有的元素跟节的占位符设置为指定字符串，（是否只设置占位符为空）
     * @param onlyBank 是不是只有占位符是“”才设置
     * @param sString 指定字符串
     */
    public resetStructsPlacehold(onlyBank: boolean, sString: string): number {
        return this._getNewControl()
            .resetStructsPlacehold(onlyBank, sString);
    }

    /**
     * 设置文本框，节的标题
     * @param sName 文本框或者节名称
     * @param sTitle 标题
     */
    public setStructTitle(sName: string, sTitle: string): number {
        return this._getNewControl()
            .setStructTitle(sName, sTitle);
    }

    /**
     * 获取文本框，节的标题
     * @param sName 文本框或者节名称
     */
    public getStructTitle(sName: string): string {
        return this._getNewControl()
            .getStructTitle(sName);
    }

    /**
     * 获取结构化元素的文本
     * @param sName 结构化名称
     */
    public getStructText(sName: string): string {
        return this._getNewControl()
            .getNewControlText(sName);
    }

    /**
     * 返回结构化元素的 开始位置
     * @param sName 结构化名称
     * @return 位置； -1 表示失败
     */
    public getStructBegin(sName: string): string {
        return this._getNewControl()
            .getNewControlBegin(sName);
    }

    /**
     * 返回结构化元素的结束位置
     * @param sName 结构化名称
     * @return 位置； -1 表示失败
     */
    public getStructEnd(sName: string): string {
        return this._getNewControl()
            .getNewControlEnd(sName);
    }

    /**
     * 设置结构化元素的 属性
     * @param sName 结构化名称
     * @param sPropName 属性名
     * @param sValue 属性值
     */
    public setStructProp(sName: string, sPropName: string, sValue: any): number {
        return this._getNewControl()
            .setNewControlProp(sName, sPropName, sValue);
    }

    /**
     * 批量对多个结构化元素的内容赋值。
     * @param sJson 多个结构化元素Json
     */
    public setStructsTextByArray(sJson: string): number {
        return this._getNewControl()
            .setStructsTextByArray(sJson);
    }

    /**
     * 批量对多个结构化元素的属性赋值。
     * @param sJson 多个结构化元素Json
     */
    public setStructsPropByArray(sJson: string): number {
        return this._getNewControl()
            .setStructsPropByArray(sJson);
    }

    /**
     * 获取指定name的NewControl的指定属性
     * @param sName 结构化名称
     * @param sPropName 属性名
     */
    public getStructProp(sName: string, sPropName: string): string | boolean {
        return this._getNewControl()
            .getNewControlProp(sName, sPropName);
    }

    /**
     * 设置文本框可输入的最大长度
     * @param sName 结构化名称
     * @param nMaxLen 最大长度 大于等于0的整数 0 – 取消长度限制
     */
    public setTextBoxMaxLen(sName: string, nMaxLen: number): number {
        return this._getNewControl()
            .setTextBoxMaxLen(sName, nMaxLen);
    }

    /**
     * 获取文本框可输入的最大长度
     * @param sName 结构化名称
     */
    public getTextBoxMaxLen(sName: string): number {
        return this._getNewControl()
            .getTextBoxMaxLen(sName);
    }

    /**
     * 获取指定名称日期框的值
     * @param name 结构化名称
     * @param sRev 扩展参数”AllDate” 假如年份月日格式里面不存在的话，也会返回
     */
    public getDateTimeBoxValueEx(name: string, sRev?: string): string {
        return this._getNewControl()
            .getDateTimeBoxValueEx(name, sRev);
    }

    /**
     * 设置指定名称日期框的值
     * @param sName 结构化名称
     * @param sValue 日期框的值
     */
    public setDateTimeBoxValue(sName: string, sValue: string): number {
        return this._getNewControl()
            .setDateTimeBoxValue(sName, sValue);
    }

    /**
     * 获取指定名称日期框的默认显示格式
     * @param sName 结构化名称
     */
    public getDateTimeFormat(sName: string): string {
        return this._getNewControl()
            .getDateTimeFormat(sName);
    }

    /**
     * 设置指定指定名称日期框的默认显示格式
     * @param sName 结构化名称
     * @param nType 1   YYY-MM-DD 2	YYYY-MM-DD	HH：MM：SS	3	YYYY-MM-DD	HH：MM 4  HH：MM：SS
     * @param setDateTimeFormat 自定义格式时，日期格式；
     */
    public setDateTimeFormat(sName: string, nType: number, setDateTimeFormat?: string): number {
        return this._getNewControl()
            .setDateTimeFormat(sName, nType, setDateTimeFormat);
    }

    /**
     * 设置Numbox的文本
     * @param sName 结构化名称
     * @param nText 数值框的文本
     */
    public setNumboxText(sName: string, nText: number): number {
        return this._getNewControl()
            .setNumboxText(sName, nText);
    }

    public setNumboxUnit(name: string, unit: string): number {
        return this._getNewControl()
            .setNumboxUnit(name, unit);
    }

    public getNumboxUnit(name: string): string {
        return this._getNewControl()
            .getNumboxUnit(name);
    }

    /**
     * 功能描述：获取包含非法值得Numbox的名称列表
     * @param bFlag 1 只包含强制校验得数值框 2 所有的数值框
     */
     public getIllegalValueNumbox(bFlag: number): string {
        return this._getNewControl()
            .getIllegalValueNumbox(bFlag);
    }

    /**
     * 获取Numbox的文本
     * @param name 结构化名称
     */
    public getNumboxText(name: string): number {
        return this._getNewControl()
            .getNumboxText(name);
    }

    /**
     * 设置Numbox的取值上限
     * @param name 结构化名称
     * @param maxValue 最大值
     */
    public setNumboxMaxValue(name: string, maxValue: number): number {
        return this._getNewControl()
            .setNumboxMaxValue(name, maxValue);
    }

    /**
     * 获取Numbox的取值上限
     * @param name 结构化名称
     */
    public getNumboxMaxValue(name: string): number {
        return this._getNewControl()
            .getNumboxMaxValue(name);
    }

    /**
     * 设置Numbox的取值下限
     * @param name 结构化名称
     * @param minValue 最小值
     */
    public setNumboxMinValue(name: string, minValue: number): number {
        return this._getNewControl()
            .setNumboxMinValue(name, minValue);
    }

    /**
     * 获取Numbox的取值下限
     * @param name 结构化名称
     */
    public getNumboxMinValue(name: string): number {
        return this._getNewControl()
            .getNumboxMinValue(name);
    }

    /**
     * 设置Numbox的精度
     * @param name 结构化名称
     * @param precision 精度值
     */
    public setNumboxPrecision(name: string, precision: number): number {
        return this._getNewControl()
            .setNumboxPrecision(name, precision);
    }

    /**
     * 获取Numbox的精度
     * @param name 结构化名称
     */
    public getNumboxPrecision(name: string): number {
        return this._getNewControl()
            .getNumboxPrecision(name);
    }

    /**
     * 设置数字框输入错误后的警告信息，比如输入英文字母
     * @param strName 结构化名称
     * @param strInfo 警告信息
     */
    public setNumboxErrorInputInfo(strName: string, strInfo: string): number {
        return this._getNewControl()
            .setNumboxErrorInputInfo(strName, strInfo);
    }

    /**
     * 设置数字框输入不在最小值到最大值范围的警告信息
     * @param strName 结构化名称
     * @param strInfo 警告信息
     */
    public setNumboxOutRangeInfo(strName: string, strInfo: string): number {
        return this._getNewControl()
            .setNumboxOutRangeInfo(strName, strInfo);
    }

    /**
     * 获取指定下拉框的当前Code值
     * @param sName 结构化名称
     * @param nType 中标预留值，暂时没用
     */
    public getCompoundBoxCurrentCode(sName: string, nType?: number): string {
        return this._getNewControl()
            .getCompoundBoxCurrentCode(sName, nType);
    }

    /**
     * 获取指定下拉框的当前value值
     * @param sName 结构化名称
     * @param nType 中标预留值，暂时没用
     */
    public getCompoundBoxCurrentValue(sName: string, nType?: number): string {
        return this._getNewControl()
            .getCompoundBoxCurrentValue(sName, nType);
    }

    /**
     * 获取指定名称控件的code数组
     * @param sName 结构化名称
     * @param nType 中标预留值，暂时没用
     */
    public getCompoundBoxCodeWithArray(sName: string, nType?: number): string[] {
        return this._getNewControl()
            .getCompoundBoxCodeWithArray(sName, nType);
    }

    /**
     * 获取指定名称控件的value数组
     * @param sName 结构化名称
     * @param nType 中标预留值，暂时没用
     */
    public getCompoundBoxValueWithArray(sName: string, nType?: number): string[] {
        return this._getNewControl()
            .getCompoundBoxValueWithArray(sName, nType);
    }

    /**
     * 获取指定名称控件的Code对应的Value值
     * @param sName 结构化名称
     * @param sCode 控件列表的Code值
     * @param nType 中标预留值，暂时没用
     */
    public getCompoundBoxValueByCode(sName: string, sCode: string, nType?: number): string {
        return this._getNewControl()
            .getCompoundBoxValueByCode(sName, sCode, nType);
    }

    /**
     * 设置指定名称控件的Code和Value值；当已存在code值是，则修改value，当不存在时，则新增
     * @param sName 结构化名称
     * @param sCode 控件列表的Code值
     * @param sValue 控件列表的Value值
     * @param nType 中标预留值，暂时没用
     */
    public setCompoundBoxCodeAndValue(sName: string, sCode: string, sValue: string, nType?: number): number {
        return this._getNewControl()
            .setCompoundBoxCodeAndValue(sName, sCode, sValue, nType);
    }

    /**
     * 设置指定多选下拉控件的分隔符
     * @param sName 结构化名称
     * @param sSeparator 分隔符的信息
     */
    public setMultiDropdownControlSeparator(sName: string, sSeparator: string): number {
        return this._getNewControl()
            .setMultiDropdownControlSeparator(sName, sSeparator);
    }

    /**
     * 设置指定多选下拉控件的选中项及未选中项之间的分隔符
     * @param sName 结构化名称
     * @param sSeparator 分隔符的信息
     */
    public setMultiDropdownControlGroupSeparator(sName: string, sSeparator: string): number {
        // todo: 这个接口暂时不会实现，主要没用这种场景
        return;
    }

    /**
     * 设置下拉框控件的下拉窗体弹出模式( 单击弹出还是双击弹出)
     * @param bEnable True – 双击弹出 False – 单击弹出
     */
    public setCompoundBoxDropMode(bEnable: boolean): number {
        return;
    }

    /**
     * 删除一个结构化元素
     * @param sName 结构化名称
     */
    public deleteStruct(sName: string): number {
        return this._getNewControl()
            .deleteNewControl(sName);
    }

    /**
     * 重新命名指定 结构化元素的name
     * @param sName 结构化名称
     */
    public setStructName(sName: string, sNewName: string): number {
        return this._getNewControl()
            .setNewControlName(sName, sNewName);
    }

    /**
     * 光标跳转到某一个结构的边框前或者后
     * @param sName 结构化名称
     * @param nMark 前端或者后端  1 – 后边框的后端 0 – 前边框的前端 2 – 后边框的前端 3 – 前边框的后端
     */
    public cursorJumpOutOfOneStruct(sName: string, nMark: number): number {
        return this._getNewControl()
            .cursorJumpOutOfOneStruct(sName, nMark);
    }

    /**
     * 光标选中某一个结构的所有内容,但是不包括边框(无论边框是否隐藏)
     * @param sName 结构化名称
     */
    public selectOneStructContent(sName: string): number {
        return this._getNewControl()
            .selectOneStructContent(sName);
    }

    public selectOneStruct(sName: string): number {
        return this._getNewControl()
            .selectOneStruct(sName);
    }

    /**
     * 以Json格式依次返回文档的结构化信息. （不包含嵌套信息）
     */
    public getStructsXmlInfoByParament(): string {
        return this._getNewControl()
            .getStructsXmlInfoByParament();
    }

    /**
     * 批量对多个结构化元素的内容赋值。
     * @param json 设置text json
     */
    public setStructsTextByJson(json: string): number {
        return this._getNewControl()
        .setStructsTextByJson(json);
    }

    /**
     * 以Json格式依次返回文档的结构化信息和表格信息. （不包含嵌套信息）
     */
    public getStructsXmlInfoByParament2(sJson: string): string {

        if (!sJson) {

            return this._documentCore.getAllNestContentProps({needNest: 0, needTable: 0, needCascade: 0});

        }
        let param;
        try {
            param = JSON.parse(sJson);
        } catch (err) {
            return ResultType.StringEmpty;
        }


        const res = this._documentCore.getAllNestContentProps({
            needNest: Number.parseInt(param.needNest),
            needTable: Number.parseInt(param.needTable),
            needCascade: Number.parseInt(param.needCascade),
            needHidden: Number.parseInt(param.needHidden)});

        return res;
        // if (param && param.hasOwnProperty('needNest') && +param.needNest === 1) {
        //     // nest
        // } else {
        //     // no nest
        //     const structsJson = this.getStructsXmlInfoByParament();
        //     const tableObj = this._getTable()
        //         .getStructsXmlInfoByParament2(sJson);
        //     return JSON.stringify(Object.assign(JSON.parse(structsJson), tableObj));
        // }
    }

    public getStructsXmlInfoByFile(content: Blob): Promise<string> {
        return this._getNewControl()
            .getStructsXmlInfoByFile(content);
    }

    public getStructsXmlInfoByFile2(content: Blob, sJson: string): Promise<string> {
        return this._getNewControl()
            .getStructsXmlInfoByFile(content, sJson);
    }

    /**
     * 按照某个属性名（可以同时带属性值）进行筛选，筛选出符合条件的结构名
     * @param sJson 配对属性json格式字符串
     * @param sRev1 预留参数
     * @return 返回为字符串，以英文,隔开
     */
    public filterStructsByProp(sJson: string, sRev1?: string): string {
        return this._getNewControl()
            .filterStructsByProp(sJson, sRev1);
    }

    /**
     * 获取当前文档所有必填项未填的新式控件的名称列表
     * @return 返回为字符串，以英文,隔开
     */
    public getIncompletedCtrlNameList(): string {
        return this._getNewControl()
            .getIncompletedCtrlNameList();
    }

    /**
     * 获取当前文档中包含错误的控件名称列表
     */
    public getErrorCtrlNameList(): string {
        return this._getNewControl()
                .getErrorCtrlNameList();
    }

    /**
     * 保存指定结构为字符串数据。(base64编码)
     * @param sName 结构化名称
     * @return 返回一个字符串(base64编码)
     */
    public saveStructContentToString(sName: string): string {
        return this._getNewControl()
            .saveStructContentToString(sName);
    }

    /**
     * 设置指定名称控件的Code和Value值（传数组，一次设多条code，value）
     * @param sName 结构化名称
     * @param value 设置的值 json
     * @param nType 结构化类型
     */
    public setCompoundBoxCodeAndValueByArray(sName: string, sValue: string, nType?: number): number {
        return this._getNewControl()
            .setCompoundBoxCodeAndValueByArray(sName, sValue, nType);
    }

    /**
     * 根据指定下拉框的value值设置当前显示的code值
     * @param sName 结构化名称
     * @param value 设置的值
     * @param nType 结构化类型
     */
    public setCompoundBoxCurrentCodeByValue(sName: string, value: string, nType?: number): number {
        return this._getNewControl()
            .setCompoundBoxCurrentCodeByValue(sName, value, nType);
    }

    /**
     * 删除指定名称控件的所有条目
     * @param sName 结构化名称
     * @param nType 结构化类型
     */
    public deleteAllCompoundBoxCodeAndValue(sName: string, nType?: number): number {
        return this._getNewControl()
            .deleteAllCompoundBoxCodeAndValue(sName, nType);
    }

    /**
     * 设置页眉页脚是否只读
     * @param bReadOnly 是否只读
     */
    public setHeaderFooterReadOnly(bReadOnly: boolean): number {
        return this._getHeader()
            .setHeaderFooterReadOnly(bReadOnly);
    }

    /**
     * 获取当前页的页眉文本内容
     */
    public getHeaderText(): string {
        return this._getHeader()
            .getHeaderText();
    }

    /**
     * 用传递进来的文件页眉替换当前文件的页眉。
     * @param fileContent 传递进来的文件内容
     * @param sRev 预览参数（暂时无用）
     */
    public replaceHeader(fileContent: Blob, sRev: string): Promise<number> {
        return this._getHeader()
            .replaceHeader(fileContent, sRev);
    }

     /**
      * 用传递进来的文件页脚替换当前文件的页脚。
      * @param fileContent 传递进来的文件内容
      * @param sRev 预览参数（暂时无用）
      */
    public replaceFooter(fileContent: Blob, sRev: string): Promise<number> {
        return this._getHeader()
            .replaceFooter(fileContent, sRev);
    }

    /**
     * 判断当前文档是否含有页眉
     */
    public hasHeader(): boolean {
        return this._getHeader()
            .hasHeader();
    }

    /**
     * 删除当前文档的页眉
     */
    public deleteHeader(): number {
        return this._getHeader()
            .deleteHeader();
    }

    /**
     * 删除当前文档的页眉内容
     */
     public deleteHeaderContent(sRev?: string): number {
        return this._getHeader()
            .deleteHeaderContent(sRev);
    }

    /**
     * 删除当前文档的页脚内容
     */
     public deleteFooterContent(sRev?: string): number {
        return this._getHeader()
            .deleteFooterContent(sRev);
    }

    /**
     * 判断当前文档是否含有页脚
     */
    public hasFooter(): boolean {
        return this._getHeader()
            .hasFooter();
    }

    /**
     * 删除当前文档的页脚
     */
    public deleteFooter(): number {
        return this._getHeader()
            .deleteFooter();
    }

    /**
     * 设置页脚的文本内容，如果没有页脚不会自动插入页脚
     * @param strText 页脚内容 支持通配符$ 用来代表页码
     * @param nParaStyle 页脚内容的对齐方式
     * @param sRev1 预留参数
     */
    public setFooterTextEx(strText: string, nParaStyle: number, sRev1?: string): number {
        return this._getHeader()
            .setFooterTextEx(strText, nParaStyle, sRev1);
    }

    /**
     * 是否开启拼写检查
     * @param bEnable 是否开启
     * @param bRefresh 是否进行马上检查，默认检查
     */
    public enableSpellCheck(bEnable: boolean, bRefresh?: boolean): Promise<number> {
        return this._getDocmentProperty()
            .enableSpellCheck(bEnable, bRefresh);
    }

    /**
     * 光标跳到当前页的页眉开始位置。
     */
    public jumpToHeader(): number {
        return this._getHeader()
            .jumpToHeader();
    }

    /**
     * 设置批注用户名
     * @param sName 用户名
     */
    public setCommentAuthor(sName: string): number {
        return this._getComment()
                   .setCommentAuthor(sName);

    }

    /**
     * 设置动态高度模式
     * @param bShow
     */
    public setDynamicHeightMode(bShow: boolean): number {
        return this._getOperateDocument()
            .setDynamicHeightMode(bShow);
    }

    /**
     * 变更Inline模式
     * @param flag 是否启用内联模式
     * @param containerWidth 容器宽度（可选）
     */
    public setInlineMode(flag: boolean, containerWidth?: number): number {
        return this._getOperateDocument().setInlineMode(flag, containerWidth);
    }

    /**
     * 当前光标位置插入一个签名控件，可以指定属性
     * @param sName
     * @param sJson
     */
    public insertSignControlAtCurrentCursor(sName: string, sJson: string): string | number {
        return this._getNewControl()
            .insertSignControlAtCurrentCursor(sName, sJson);
    }

    public getSignElementNames(sName: string): string {
        return this._getNewControl()
            .getSignElementNames(sName);
    }

    /**
     * 返回指定name的签名控件里的已经签名的元素name
     * @param sName 签名控件名称
     */
    public getSignedElementNames(sName: string): string {
        return this._getNewControl()
            .getSignedElementNames(sName);
    }

    /**
     * 在签名控件里的指定签名子元素中签名(图形),可以控制签名的大小
     * @param sName 签名控件名称
     * @param sJson 控制参数
     */
    public addSignContentToControl(sName: string, sJson: string): Promise<number> {
        return this._getDrawing()
            .addSignContentToControl(sName, sJson);
    }

    /**
     * 删除指定name的签名控件的内容
     * @param sName 签名控件名称
     * @param sJson 控制参数
     */
     public deleteSignContent(sName: string, sJson: string): number {
        return this._getNewControl()
            .deleteSignContent(sName, sJson);
    }

    /**
     * 返回指定name的签名控件里的签名元素个数
     * @param sName
     */
    public getElementCountByName(sName: string): number {
        return this._getNewControl()
            .getElementCountByName(sName);
    }

    /**
     * 在签名控件里的指定签名子元素中签名（文本）
     * @param sName
     * @param index
     * @param sText
     */
    public addSignTextToControl(sName: string, index: number, sText: string): number {
        return this._getNewControl()
            .addSignTextToControl(sName, index, sText);
    }

    /**
     * 在签名控件里的指定签名子元素中签名(图形)
     * @param sName
     * @param index
     * @param sPicData
     */
    public addSignPicToControlWithString(sName: string, index: number, sPicData: string): Promise<number> {
        return this._getDrawing()
            .addSignPicToControlWithString(sName, index, sPicData);
    }

    /**
     * 在签名控件里的指定签名子元素中签名(图形)
     * @param sName
     * @param index
     * @param sPicData
     */
     public addSignPicToControlWithString2(sName: string, index: number, sPicData: string,
                                           sJson?: string): Promise<number> {
        return this._getDrawing()
            .addSignPicToControlWithString2(sName, index, sPicData, sJson);
    }

    /**
     * 删除指定name的签名控件的内容
     * @param sName
     */
    public deleteSignControlByName(sName: string): number {
        return this._getNewControl()
            .deleteSignControlByName(sName);
    }

    /**
     * 删除指定name的签名控件的内容
     * @param sName
     * @param index
     */
    public deleteSignContentByName(sName: string, index: number): number {
        return this._getNewControl()
            .deleteSignContentByName(sName, index);
    }

    /**
     * 获取整个文档中签名控件的个数
     */
    public getSignControlCount(): number {
        return this._getNewControl()
            .getSignControlCount();
    }

    /**
     * 获取指定区域中的签名控件的个数
     * @param sName
     */
    public getSignControlCountInRegion(sName: string): number {
        return this._getRegion()
            .getSignControlCountInRegion(sName);
    }

    /**
     * 获取指定区域中的签名控件的名称
     * @param sName
     */
    public getSignControlNamesInRegion(sName: string): string[] {
        return this._getRegion()
            .getSignControlNamesInRegion(sName);
    }

    /**
     * 获取全文档中的签名控件的名称
     */
    public getSignControlNames(): string[] {
        return this._getNewControl()
            .getSignControlNames();
    }

    /**
     * 传递进来页眉的json信息，统一设置全文档的页眉
     * @param sJson /
     * @param sRev1 /
     */
    public setHeadersTextByJson(sJson: string, sRev1: string): number {
        return this._getHeader()
            .setHeadersTextByJson(sJson, sRev1);
    }

    /**
     * 设置页眉距离顶部的距离
     * @param headerFromTop 页眉距离顶部的距离，单位：毫米(mm)
     */
    public setHeaderFromTop(headerFromTop: number): number {
        let nResult = ResultType.Success;
        // 将毫米转换为像素后传递给底层
        const headerFromTopPx = getPxForMM(headerFromTop);
        this._documentCore
            .setPageMarginsHeader(headerFromTopPx);
        return nResult;
    }

    /**
     * 设置页脚距离底部的距离
     * @param footerFromBottom 页脚距离底部的距离，单位：毫米(mm)
     */
    public setFooterFromBottom(footerFromBottom: number): number {
        let nResult = ResultType.Success;
        // 将毫米转换为像素后传递给底层
        const footerFromBottomPx = getPxForMM(footerFromBottom);
        this._documentCore
            .setPageMarginsFooter(footerFromBottomPx);
        return nResult;
    }

    /**
     * 获取页眉距离顶部的距离
     * @returns 页眉距离顶部的距离，单位：毫米(mm)
     */
    public getHeaderFromTop(): number {
        // 底层返回像素值，转换为毫米返回
        const headerFromTopPx = this._documentCore.getPageMarginsHeader();
        return getMMFromPx(headerFromTopPx);
    }

    /**
     * 获取页脚距离底部的距离
     * @returns 页脚距离底部的距离，单位：毫米(mm)
     */
    public getFooterFromBottom(): number {
        // 底层返回像素值，转换为毫米返回
        const footerFromBottomPx = this._documentCore.getPageMarginsFooter();
        return getMMFromPx(footerFromBottomPx);
    }

    /**
     * 删除当前文档的页眉内容末尾的空行
     * @param sText 预留参数 ''
     */
    public deleteRedundantInHeader(sText: string): number {
        return this._getHeader()
            .deleteRedundantInHeader(sText);
    }

    /**
     * 移除指定签名里特定的字符。
     * @param sName 签名元素名称
     * @param sRemoveChar 指定字符
     */
    public removeCertainCharInSignControl(sName: string, sRemoveChar: string = '/'): number {
        return this._getNewControl()
                .removeCertainCharInSignControl(sName, sRemoveChar);
    }

    public checkFirstTableRowHeightInHeader(sTableName: string): number {
        return this._getOperateDocument()
                .checkFirstTableRowHeightInHeader(sTableName);
    }

    /**
     * 当前光标位置的是否可以编辑
     */
    public canEditInCurrentCursor(): boolean {
        return this._getEditDocument()
                .canEditInCurrentCursor();
    }

    public lockRefresh(bLock: boolean): number {
        return this._getEditDocument()
                    .lockRefresh(bLock);
    }

    public enableNotAllowedCursorStyle(bFlag: boolean): number {
        if (null == bFlag || typeof bFlag !== 'boolean') {
            return ResultType.ParamError;
        }

        const theme = getTheme();
        if (theme && theme.NewControl) {
            if (bFlag === theme.NewControl.EnableNotAllowedCursorStyle) {
                return ResultType.UnEdited;
            } else {
                theme.NewControl.EnableNotAllowedCursorStyle = bFlag;
                return ResultType.Success;
            }
        }

        return ResultType.Failure;
    }

    /**
     * 文件监听器，影响nsoFileOpenCompleted事件
     */
    public addFileListen(): number {
        return this._getListenEvents()
            .addFileListen();
    }

    /**
     * 结构化元素监听器，影响nsoStructChanged nsoStructClick nsoStructDBClick nsoStructGainFocus  nsoStructLostFocus 事件
     * @param nRev 预留参数，暂时没用
     */
    public addStructListen(nRev?: number): number {
        return this._getListenEvents()
            .addStructListen(nRev);
    }

    /**
     * 键盘监听器 影响nsoKeyPressedEvent 事件
     * @param nRev 预留参数，暂时没用
     */
    public addKeyListen(nRev?: number): number {
        return this._getListenEvents()
            .addKeyListen(nRev);
    }

    /**
     * 移除File监听器
     */
    public removeFileListen(): number {
        return this._getListenEvents()
            .removeFileListen();
    }

    /**
     * 移除Struct监听器
     */
    public removeStructListen(): number {
        return this._getListenEvents()
            .removeStructListen();
    }

    /**
     * 移除键盘监听器
     */
    public removeKeyListen(): number {
        return this._getListenEvents()
            .removeStructListen();
    }

    /**
     * 移除所有的监听器。
     */
    public removeAllListen(): number {
        return this._getListenEvents()
            .removeAllListen();
    }

    /**
     * 开启自动缓存
     */
    public enableAutoSave(): void {
        return this._getEditDocument()
            .enableAutoSave();
    }

    /**
     * 关闭自动缓存
     */
    public disableAutoSave(): void {
        return this._getEditDocument()
            .disableAutoSave();
    }

    /**
     * 保存指定Region内容为一个子文档
     */
    public saveRegionContentToStream(sName: string, sModJson: string): Promise<Blob> {
        return this._getOperateDocument()
            .saveRegionContentToStream(sName, sModJson);
    }

    public getIncompletedCtrlNameListInRegion(sRegion: string): string {
        return this._getRegion()
            .getIncompletedCtrlNameListInRegion(sRegion);
    }

    /**
     * 设置c端打印服务地址
     * @param url server url
     */
    public setPrinterServerUrl(url: string): number {
        return this._getOperateDocument()
            .setPrinterServerUrl(url);
    }

    public getTable(): Table {
        return this._getTable();
    }

    // public getNISTableOperate(): NISTableExternalInterface {
    //     if ( NURSING_FEATURE ) {
    //         return (this.NIS ? this.NIS :
    //             this.NIS = new NISTableExternalInterface(this));
    //     }

    //     return null;
    // }

    public getCTTTableOperate(): NISTableExternalInterface {
        if ( NURSING_FEATURE ) {
            return (this.CTT ? this.CTT :
                    this.CTT = new NISTableExternalInterface(this));
        }
    }

    /**
     * 获取地址栏内容
     * @param name
     * @returns
     */
    public getAddressControlText(name: string): string {
        return this._getNewControl()
            .getAddressControlText(name);
    }

    public setAddressControlText(name: string, sText: string): number {
        return this._getNewControl()
            .setAddressControlText(name, sText);
    }

    public forbidMoveTableBorder(bFixed: boolean): number {
        return this._getOperateDocument()
            .forbidMoveTableBorder(bFixed);
    }

    public acceptRevisions(): void {
        return this._getRecension()
                .acceptRevisions();
    }

    public moveCursorToTableCell(sTable: string, sCellName: string): number {
        return this._getTable()
                        .moveCursorToTableCell(sTable, sCellName);
    }

    public enableDefaultStyleAfterTitle(bEnable: boolean): number {
        if (null == bEnable || typeof bEnable !== 'boolean') {
            return ResultType.ParamError;
        }

        const theme = getTheme();
        if (theme && theme.NewControl) {
            if (bEnable === theme.NewControl.EnableDefaultStyleAfterTitle) {
                return ResultType.UnEdited;
            } else {
                theme.NewControl.EnableDefaultStyleAfterTitle = bEnable;
                return ResultType.Success;
            }
        }

        return ResultType.Failure;
    }

    public setDynamicGridLine(bEnable: boolean, sParam?: string): number {
        if (null == bEnable || typeof bEnable !== 'boolean' ||
            (null != sParam && typeof sParam !== 'string')) {
            return ResultType.ParamError;
        }

        return this._getOperateDocument()
                    .setDynamicGridLine(bEnable, sParam);
    }

    public getSourceBindJson(): string {
        return this._getOperateDocument()
                    .getSourceBindJson();
    }

    public setSourceBindJson(sJson: string): number {
        return this._getOperateDocument()
                    .setSourceBindJson(sJson);
    }

    public enableSouceBindInRegion(sRegion:string,nControl:number):number{
        return this._getOperateDocument()
        .enableSouceBindInRegion(sRegion,nControl);
    }

    public saveNISTableFile(): Promise<any[]> {
        return this._getOperateDocument()
                    .saveNISTableFile();
    }

    public updateNISTableByJson(sJson: string): number {
        return this._getTable()
                    .updateNISTableByJson(sJson);
    }

    public insertComment(sJson: string): string {
        return this._getComment()
                    .insertComment(sJson);
    }

    public deleteComment(name: string): number {
        return this._getComment()
                    .deleteComment(name);
    }

    public modifyComment(name: string, sJson: string): number {
        return this._getComment()
                    .modifyComment(name, sJson);
    }

    public getCommentContent(name: string): string {
        return this._getComment()
                    .getCommentContent(name);
    }

    public get comment(): any {
        return this._getComment();
    }

    public get aiSuggestion(): any {
        return this._getAISuggestion();
    }

    /**
     * 在当前光标位置显示 AI 建议弹窗
     * @param suggestions 建议文本数组
     * @returns 是否成功显示
     */
    public showAISuggestionAtCursor(suggestions: string[]): boolean {
        return this.aiSuggestion.showAISuggestionAtCursor_interface(suggestions);
    }

    public getAllComments(): string {
        return this._getComment()
                    .getAllComments();
    }

    public jumpToOneCommentByName(sName: string): number {
        return this._getComment()
                    .jumpToOneCommentByName(sName);
    }

    public addCommentReply(name: string, sJson: string): number {
        return this._getComment()
                    .addCommentReply(name, sJson);
    }

    public getCurrentCommentInfo(): string {
        return this._getComment()
                    .getCurrentCommentInfo();
    }

    public showCommentPanel(bShow: boolean): number {
        return this._getComment()
                    .showCommentPanel(bShow);
    }

    // tslint:disable-next-line: max-line-length
    // public getSpecificStructContentWithBackString(sSourceStructJson: IStructJson[], sSourceBase64String: string, type: number = 1): Promise<IStructContent[]> {
    //     return this._getOperateDocument()
    //         .getSpecificStructContentWithBackString(sSourceStructJson, sSourceBase64String, type);
    // }

    // tslint:disable-next-line: max-line-length
    // public getSpecificStructContentWithBackStream(sSourceStructJson: IStructJson[], content: Blob, type: number = 1): Promise<IStructContent[]> {
    //     return this._getOperateDocument()
    //         .getSpecificStructContentWithBackStream(sSourceStructJson, content, type);
    // }

    private _writeLog(result: any, date: Date, key: string, arrs: any[]): any {
        if (!(result instanceof Promise)) {
            logger.interface({startTime: date, name: key, args: arrs, result, id: this._docId});
            this._documentCore.resetAdminMode();
        } else {
            return new Promise((resolve, reject) => {
                const currentKey = key;
                result.then((res) => {
                    if (logger.isAddInterfaceLog(currentKey)) {
                        logger.interface({startTime: date, name: key, args: arrs,
                            result: res, id: this._docId});
                    }
                    this._documentCore.resetAdminMode();
                    resolve(res);
                })
                .catch((err) => {
                    if (logger.isAddInterfaceLog(currentKey)) {
                        logger.interface({startTime: date, name: key, args: arrs,
                            result: 1, id: this._docId});
                    }
                    if (err.message !== ErrorMessages.ThrowCustomErrorMsg) {
                        logger.error({id: this._docId, description:
                            `name: ${key};message: ${err.message};stack: ${err.stack};`});
                    }

                    throw err;
                    // console.error(err)
                });
            });
        }

        return result;
    }

}
