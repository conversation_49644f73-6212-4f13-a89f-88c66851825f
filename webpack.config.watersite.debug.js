const HtmlWebpackPlugin = require('html-webpack-plugin');
const { CleanWebpackPlugin } = require('clean-webpack-plugin');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const TsconfigPathsPlugin = require('tsconfig-paths-webpack-plugin');
const TerserPlugin = require('terser-webpack-plugin');
const CssMinimizerPlugin = require('css-minimizer-webpack-plugin');
const CompressionPlugin = require('compression-webpack-plugin');
const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer');
const path = require('path');
const webpack = require('webpack');
const features = require('./features.json');
const JavaScriptObfuscator = require('webpack-obfuscator');

const conditionalCompiler = {
  loader: 'js-conditional-compile-loader',
  options: {
    WATER: true, // 保留水印代码
    NOWATER: false, // 无水印版
  }
};

module.exports = {
  // 添加 source map 支持
  devtool: 'source-map',
  
  entry: {
    'hz-editor': './site.tsx',
    'test-web': './src/components/editor/TestWebSite.tsx'
  },
  stats: {
    warnings: true, // 启用警告，帮助调试
  },
  module: {
    rules: [
      {
        test: /\.tsx?$/,
        use: ['ts-loader', conditionalCompiler],
        exclude: /node_modules/
      },
      {
        test: /\.css$/,
        use: [
          'style-loader',
          'css-loader'
        ]
      },
      {
        test: /\.less$/,
        use: [
          {
            loader: 'style-loader', // creates style nodes from JS strings
          },
          {
            loader: 'css-loader', // translates CSS into CommonJS
          },
          {
            loader: 'less-loader', // compiles Less to CSS
          },
        ],
      },
      {
        test: /\.(jpe?g|png|gif)$/i,
        use: [
          {
            loader: 'url-loader',
            options: {
              limit: 5000,
          }
        }
        ]
      },
      {
        test: /\.svg$/,
        use: [
          {
            loader: 'svg-url-loader',
            options: {
              limit: 5000,
              encoding: "base64"
        }
      },
        ],
      },
      {
        test: /\.(eot|ttf|woff|woff2)$/,
        use: [
          {
            loader: 'file-loader',
            options: {
              outputPath: 'fonts/'
        }
      }
    ]
  },
    ]
  },
  node: {
    __dirname: true,
    __filename: true,
  },
  resolve: {
    extensions: ['.tsx', '.ts', '.js'],
    plugins: [new TsconfigPathsPlugin({})],
    alias: {
      '@': path.resolve(__dirname, './src')
    },
    fallback: {
      stream: require.resolve('stream-browserify')
    }
  },
  output: {
    libraryTarget: 'umd',
    library: 'HongzhiEditor',
    filename: '[name].umd.[contenthash].js',
    chunkFilename: "[name].[contenthash:10].chunk.js",
    path: path.resolve(__dirname, 'dist/iframe-debug'),
  },
  plugins: [
    new webpack.ProvidePlugin({
      process: 'process/browser',
    }),
    new webpack.DefinePlugin(features),
    new HtmlWebpackPlugin({
      title: 'hz-editor-site',
      filename: 'index.html',
      template: 'index.site.html',
      chunks: ['hz-editor', 'vendors-hz-editor', 'style'],
      minify: false // 禁用HTML压缩，便于调试
    }),
    new HtmlWebpackPlugin({
      title: 'test-site',
      filename: 'test.html',
      template: 'iframe.html',
      chunks: ['test-web', 'vendors-hz-editor']
    }),
    new CopyWebpackPlugin({
      patterns: [
        {
          from: path.resolve(__dirname, 'node_modules/hz-editor-sdk-new/index.js'),
          to: path.resolve(__dirname, 'dist/iframe-debug/sdk.js'),
          globOptions: {
            ignore: ['.*']
          }
        },
        {
          from: path.resolve(__dirname, 'src/static/water'),
          to: '',
          globOptions: {
            ignore: ['.*']
          }
        },
        {
          from: path.resolve(__dirname, 'src/components/editor/TestSdk.js'),
          to: 'test-sdk.js'
        },
        {
          from: path.resolve(__dirname, 'node_modules/typo-js/dictionaries'),
          to: 'dictionaries'
        },
      ]
    }),
    // 移除代码混淆，便于调试
    // new JavaScriptObfuscator({...}),
    
    // 保留gzip压缩，不影响调试
    new CompressionPlugin({
      algorithm: 'gzip',
      test: /\.(js|css|html|svg)$/,
      threshold: 10240,
      minRatio: 0.8
    }),
    process.env.ANALYZE && new BundleAnalyzerPlugin({
      analyzerMode: 'server',
      analyzerHost: '127.0.0.1',
      analyzerPort: 8888,
      reportFilename: 'report.html',
      defaultSizes: 'gzip',
      openAnalyzer: true,
      generateStatsFile: true,
      statsFilename: 'stats.json',
    })
  ].filter(Boolean),
  optimization: {
    minimize: true,
    minimizer: [
      new TerserPlugin({
        parallel: true,
        terserOptions: {
          compress: {
            drop_console: false, // 保留console输出，便于调试
            drop_debugger: false // 保留debugger语句，便于调试
          },
          format: {
            comments: true // 保留注释，便于调试
          },
          sourceMap: true // 确保生成source map
        },
        extractComments: false
      }),
      new CssMinimizerPlugin({
        minimizerOptions: {
          sourceMap: true // 为CSS启用source map
        }
      })
    ],
    splitChunks: {
      chunks: 'all',
      minSize: 100000, // 增大最小体积到 100KB，减少分包数量
      maxSize: 500000, // 最大 500KB
      minChunks: 1,
      maxAsyncRequests: 30,
      maxInitialRequests: 30,
      automaticNameDelimiter: '-',
      cacheGroups: {
        vendors: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors-hz-editor',
          chunks: 'all',
          priority: 20
        },
        commons: {
          test: /[\\/]node_modules[\\/](fabric)/,
          name: 'fabric',
          chunks: 'all',
          priority: 10
        },
        styles: {
          name: 'style',
          test: /\.(less|css)$/,
          chunks: 'all',
          enforce: true
        }
      }
    },
    runtimeChunk: 'single'
  },
  mode: 'production'
}; 