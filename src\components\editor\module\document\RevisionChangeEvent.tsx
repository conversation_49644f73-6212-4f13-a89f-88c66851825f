import {Document} from './Document';
import {GlobalEvent as gEvent, GlobalEventName as gEventName} from '../../../../common/GlobalEvent';
import { DocumentCore } from '../../../../model/DocumentCore';
import MouseEventHandler from '../../../../common/MouseEventHandler';
import { dateFormat, getPageElement, IRevisionChange, RevisionChangeType } from '../../../../common/commonDefines';
import { getPagePadding } from '../../../../common/commonMethods';

export default class RevisionChangeEvent {
    private host: Document;
    private docId: number;
    private gMouseEvent: MouseEventHandler;
    private documentCore: DocumentCore;
    private curFocusRevision: any;
    private isShowRevisionTips: boolean;
    private showReadonlyRevisionTips: boolean;

    private tipDom: HTMLDivElement;
    private timer: any;
    constructor(host: Document) {
        this.host = host;
        this.docId = host.docId;
        this.documentCore = host.documentCore;
        this.addEvents();
        this.gMouseEvent = new MouseEventHandler();
    }

    public hideTips(): void {
        clearTimeout(this.timer);
        if (this.isShowRevisionTips !== true || !this.tipDom) {
            return;
        }

        const dom = this.tipDom;
        if (!!dom && dom.className.indexOf('active') > -1) {
            dom.className = dom.className.replace(/\s+active/g, '');
        }
        this.isShowRevisionTips = false;
    }

    private handleMouseDown = (e: any): void => {
        const pageNode = getPageElement(e.target);
        if (!pageNode || (this.documentCore.isProtectedMode() && this.host.isPrint()) ) {
            return;
        }

        this.hideTips();
    }

    private handleMouseMove = (event: any): void => {
        const documentCore = this.documentCore;
        const pageNode = getPageElement(event.target);
        if (!pageNode) {
            return;
        }
        const bProtect = documentCore.isProtectedMode();
        if (bProtect && this.host.isPrint() || documentCore.isFinalRevision()) {
            return;
        }

        if (!this.showReadonlyRevisionTips && documentCore.isCanReadRevisionInfo() === false) {
            return;
        }

        if ( documentCore.isSelecting() ) {
            this.hideTips();
            return;
        }

        const pageId = this.host.getCurPageIndex(); // this.host.getPageIndex();
        const scale = this.host.getScale();

        // update gMouseEvent class
        const position = getPagePadding(this.documentCore);
        let offsetX = event.layerX / scale + position.left;
        const offsetY = event.layerY / scale + position.top;

        const cursorState = documentCore.getCursorStateInDocument(offsetX, offsetY, pageId);

        offsetX = cursorState ? cursorState.pointX : offsetX;
        this.gMouseEvent.pointX = offsetX;
        this.gMouseEvent.pointY = offsetY;

        // 非文本选择中，光标在同一位置持续3秒
        if ( cursorState.result ) {
            const revisionChanges = documentCore.getFocusRevision(this.gMouseEvent, pageId);
            if (revisionChanges) {
                this.showTips(revisionChanges, event, pageId);
                return ;
            }
        }

        this.hideTips();
        this.curFocusRevision = null;
    }

    private showTips(revisionChanges: IRevisionChange[], e: any, pageId: number): void {
        clearTimeout(this.timer);
        if (e.target.nodeName === 'DIV') {
            return;
        }

        const content = this.getRevisionContent(revisionChanges);
        if (!content) {
            this.hideTips();
            return;
        }

        if (this.curFocusRevision === (content + 'pageIndex' + pageId)
            && this.isShowRevisionTips === true) {
            return;
        }

        this.timer = setTimeout(() => {

            let dom = this.tipDom;
            if (!dom) {
                dom = document.createElement('div');
                dom.className = 'revision-tips';
                // dom.innerHTML = `<div></div>`;
                this.host.getContainer()
                .appendChild(dom);
                this.tipDom = dom;
                dom.addEventListener(gEventName.Click, this.handleTips);
                dom.addEventListener(gEventName.Mousemove, this.handleTips);
            }
            (dom as HTMLElement).innerHTML = content;
            const page = this.documentCore.getPageProperty();
            dom.className += ' visible';
            const scale = this.host.getScale();
            dom.style.maxWidth = page.width * scale - 40 + 'px';
            // console.log(e)
            this.showDom(dom, e, pageId);
            this.curFocusRevision = content + 'pageIndex' + pageId;
        }, 300);
    }

    private getRevisionContent(revisionChanges: IRevisionChange[]): string {
        let content = '';
        if (revisionChanges && revisionChanges.length) {

            let prevContent;
            let bNewDate;
            let label;
            revisionChanges.forEach((change, index) => {
                let value = change.value;

                if (0 === index) {
                    bNewDate = change.bNewDate;
                    prevContent = (bNewDate ? value : '');

                    if (change.firstDate) {
                        label = (bNewDate ? '日期变更' : (RevisionChangeType.TextAdd === change.type ? '已添加' : '已删除'));

                        if (change.firstDate) {
                            value = (change.firstDate + ' --> ' + value);
                        }
                    } else {
                        label = (bNewDate ? '日期初始值' : (RevisionChangeType.TextAdd === change.type ? '已添加' : '已删除'));
                    }
                }

                if (0 !== index) {
                    content += `<div></div>`;
                    label = (bNewDate ? '日期变更' : (RevisionChangeType.TextAdd === change.type ? '已添加' : '已删除'));

                    if (bNewDate) {
                        value = (prevContent + ' --> ' + value);
                        prevContent = change.value;
                    }
                }

                if (change) {
                    const time = dateFormat(change.time, 'yyyy-MM-dd HH:mm:ss');

                    let valueHtml =
                        `<div>
                            <label>${change.userName + '(' + change.userId})</label>
                        </div>
                        <div>
                        <label>${time}</label>
                        </div>
                        <div>
                            <label>${label}：</label>
                            <span>${value}</span>
                        </div>`;

                    valueHtml += `<div`;
                    if (change.description) {
                        valueHtml += `><label>备注：</label><span>${change.description}</span></div>`;
                    } else {
                        valueHtml += ` style="display: none;"></div>`;
                    }

                    content += valueHtml;
                }
            });
        }

        return content;
    }

    private showDom(dom: HTMLElement, e: any, pageId: number): void {
        // setTimeout(() => {
            // this.getPagePro();
            const page = this.documentCore.getPageProperty();
            let topDom;
            // const pageDom = dom.parentNode.querySelector('.page-wrapper') as HTMLDivElement;
            let pageDom: HTMLDivElement = this.host.getContainer()
                                .querySelector(`.page-wrapper[page-index='${pageId}']`) as HTMLDivElement;
            const scale = this.host.getScale();
            if (this.host.isMorePage()) {
                topDom = pageDom.parentNode as any;
                if (this.host.totalPage - 1 === pageId) {
                    pageDom = topDom;
                }
            } else {
                topDom = pageDom;
            }

            const width = page.width * scale;
            const position = getPagePadding(this.documentCore);
            const x = e.offsetX + position.left * scale;
            const y = e.offsetY;
            const xLimit = width;
            const left = page.paddingLeft * scale;
            const right = xLimit - page.paddingRight * scale;
            // const clientX = e.clientX;
            // console.dir(dom)
            // console.dir(e)
            if (x < left || x > right) {
                this.hideTips();
                return;
            }

            const clientWidth = dom.clientWidth;
            const tipWidth = clientWidth / 2;

            let nLeft = x - tipWidth;
            if (clientWidth + 42 > width) {
                nLeft = 15;
            } else if (nLeft < 0) {
                nLeft = x + nLeft;
            } else {
                const subWidth = x + clientWidth - xLimit;
                if (subWidth > 0) {
                    nLeft = xLimit - clientWidth - 15;
                }
            }
            // const last = dom.lastChild as any;
            // const clientHeight = 0; // dom.clientHeight + 15;

            dom.className = dom.className.replace(/\s+(active|visible)/g, ' active');
            const left2 = (!!pageDom && 0 <= pageDom.offsetLeft + nLeft) ? pageDom.offsetLeft + nLeft : 0;
            dom.style.left = left2 - position.left + 'px';

            const prePageHeight = !!topDom ? topDom.offsetTop : 0;
            const top = y + 15 + prePageHeight;
            dom.style.top = top + 'px';
            // nLeft = (0 > x - nLeft) ? 0 : x - nLeft;
            // last.style.left = nLeft + 'px';
            this.isShowRevisionTips = true;
        // }, 0);
    }

    private getOffsetTop(target: any): number {
        let parent = target.parentNode;
        while (parent) {
            if (parent.tagName === 'DIV') {
                return parent.offsetTop;
            }
            parent = parent.parentNode;
        }

        return 0;
    }

    private addEvents(): void {
        gEvent.addEvent(this.docId, gEventName.Mousedown, this.handleMouseDown);
        gEvent.addEvent(this.docId, gEventName.Mousemove, this.handleMouseMove);
        gEvent.addEvent(this.docId, gEventName.UnMounted, this.removeEvents);
        gEvent.addEvent(this.docId, gEventName.ViewPropChange, this.onViewPropChange);
    }

    private removeEvents = (): void => {
        gEvent.deleteEvent(this.docId, gEventName.Mousedown, this.handleMouseDown);
        gEvent.deleteEvent(this.docId, gEventName.Mousemove, this.handleMouseMove);
        gEvent.deleteEvent(this.docId, gEventName.ViewPropChange, this.onViewPropChange);
    }

    private handleTips(event: any): void {
        if (event.currentTarget) {
            event.currentTarget.className = 'revision-tips';
        }
    }

    private onViewPropChange = (option: {showRevision: boolean, result: number}): void => {
        if (typeof option.showRevision === 'boolean') {
            this.showReadonlyRevisionTips = option.showRevision;
            option.result = 0;
        }
    }

    private getPaddingtTop(): number {
        if (this.documentCore.isWebView()) {
            const margin = this.documentCore.getPagePositionInfo(0);
            return margin.y;
        }

        return 0;
    }
}
