import React from 'react';

export interface IFacingItem {
    pos: string | number;
    active?: boolean;
}
export interface IToothFacing {
    /** 是否是下颌牙 */
    isMandible?: boolean;
    /** 牙齿标记二维数组 [ len(tags) * <maxlen(titles)> ] */
    flags: {
        left: number[][],
        right: number[][],
    };
}

interface IProps {
    colLength: number;
    title: string;
    tags: string[]
}

interface ITableProps extends IProps {
    facings: IToothFacing;
    onClick: (title: string, row: number, col: number, isRight: boolean) => void;
}

export class ToothTable extends React.Component<ITableProps> {
    
    public render() {
        return (
            <table className="op-tbl">
                <tbody onClick={this.handleClick}>
                {this.renderRows()}
                </tbody>
            </table>
        );
    }

    public shouldComponentUpdate(nextProps: Readonly<ITableProps>, nextState: Readonly<{}>, nextContext: any): boolean {
        return nextProps.facings !== this.props.facings;
    }

    private renderRows() {
        const {isMandible} = this.props.facings;
        const tags = this.props.tags;
        const len = tags.length - 1;
        const newTags = [...tags];
        if (isMandible) {
            newTags.reverse();
        }
        return newTags.map((tag, index) => {
            return (
                <tr className='op-tbl-row' key={index}>
                    {this.renderCells(tag, isMandible ? len - index : index)}
                </tr>
            );
        });
    }

    private renderCells(tag: string, rowIndex: number) {
        const { flags } = this.props.facings;
        const len = this.props.colLength;
        const children  = [];
        // key 说明：  rowIndex - colIndex - [0: right, 1: left]
        const right = new Set(flags.right[rowIndex]);
        let i = 0;
        while (i < len) {
            const col = i++;
            children.push(
                <td
                    key={'0'  + col}
                    className={`op-tbl-cell ${col % 2 !== 0 ? 'even' : ''} ${right.has(col) ? 'active' : ''}`}
                    data-key={`${rowIndex}-${col}-0`}
                >{tag}</td>
            );
        }
        const left = new Set(flags.left[rowIndex]);
        while (i > 0) {
            const col = --i;
            children.push(
                <td
                    key={'1' + col}
                    className={`op-tbl-cell ${col % 2 !== 0 ? 'even' : ''} ${left.has(col) ? 'active' : ''}`}
                    data-key={`${rowIndex}-${col}-1`}
                >{tag}</td>
            );
        }
        return children;
    }

    private handleClick = (e: any) => {
        const dom = e.target as HTMLTableCellElement;
        if (!dom || dom.tagName !== 'TD' || !dom.dataset['key']) return;
        const key = dom.dataset['key'];
        const items = key.split('-').map(x => +x);
        if (items.length === 3) {
            this.props.onClick(this.props.title, items[0], items[1], items[2] === 0)
        }
    }
}

export interface IToothControl {
    /** 维度1：列数， 维度2： 列对应的选中项 */
    left: number[][];
    leftActive: number[];
    right: number[][];
    rightActive: number[];
}

interface IControlProp extends IProps {
    control: IToothControl;
    controlTags: string[];
    onClick: (title: string, pos: number, isRight: boolean) => void;
}

export class ToothControl extends React.Component<IControlProp> {
    
    public render() {
        return (
            <table className="op-tbl">
                <tbody onClick={this.handleClick}>
                    <tr className='op-tbl-row'>
                        {this.renderCells()}
                    </tr>
                </tbody>
            </table>
        );
    }

    public shouldComponentUpdate(nextProps: Readonly<IControlProp>, nextState: Readonly<{}>, nextContext: any): boolean {
        return nextProps.control !== this.props.control;
    }

    private renderCells() {
        const {colLength: len, control, controlTags: tags} = this.props;

        const children  = [];
        // key 说明：  rowIndex - colIndex - [0: right, 1: left]
        let i = 0;
        const rightSet = new Set(control.rightActive);
        while (i < len) {
            const col = i++;
            const isActive = rightSet.has(col) || control.right[col].length;
            children.push(
                <td
                    key={'1' + col}
                    className={`op-tbl-cell ${col % 2 !== 0 ? 'even' : ''} ${isActive ? 'active' : ''}`}
                    data-key={`${col}-0`}
                >{tags[col]}</td>
            );
        }
        i = len - 1;
        const leftSet = new Set(control.leftActive);
        while (i >= 0) {
            const col = i--;
            const isActive = leftSet.has(col) || control.left[col].length;
            children.push(
                <td
                    key={'0'  + col}
                    className={`op-tbl-cell ${col % 2 !== 0 ? 'even' : ''} ${isActive ? 'active' : ''}`}
                    data-key={`${col}-1`}
                >{tags[col]}</td>
            );
        }
        return children;
    }

    private handleClick = (e: any) => {
        const dom = e.target as HTMLTableCellElement;
        if (!dom || dom.tagName !== 'TD' || !dom.dataset['key']) return;
        const key = dom.dataset['key'];
        const items = key.split('-').map(x => +x);
        if (items.length === 2) {
            this.props.onClick(this.props.title, items[0], items[1] === 0)
        }
    }
}