export class BitmapInfoHeader {
    public width: number;
    public height: number;
    public planes: number;
    public bitcount: number;
    public compression: number;
    public sizeimage: number;
    public xpelspermeter: number;
    public ypelspermeter: number;
    public clrused: number;
    public clrimportant: number;

    constructor(reader: any, skipsize: boolean) {
        if (skipsize) {
            reader.skip(4);
        }
        this.width = reader.readInt32();
        this.height = reader.readInt32();
        this.planes = reader.readUint16();
        this.bitcount = reader.readUint16();
        this.compression = reader.readUint32();
        this.sizeimage = reader.readUint32();
        this.xpelspermeter = reader.readInt32();
        this.ypelspermeter = reader.readInt32();
        this.clrused = reader.readUint32();
        this.clrimportant = reader.readUint32();
    }

    public colors(): number {
        if (this.clrused !== 0) {
            return this.clrused < 256 ? this.clrused : 256;
        } else {
            // tslint:disable-next-line: no-bitwise
            return this.bitcount > 8 ? 0 : 1 << this.bitcount;
        }
    }
}
