/*

The MIT License (MIT)

Copyright (c) 2015 Thomas <PERSON>

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.

*/

import Codepage from '../Codepage';
import { Document } from '../Document';
import { Helper, RTFJSError } from '../Helper';
import { RenderChp } from '../renderer/RenderChp';
import { Renderer } from '../renderer/Renderer';
import { RenderPap } from '../renderer/RenderPap';
import { SymbolTable } from '../Symboltable';
import { GlobalState, State } from './Containers';
import { DestinationFactory } from './destinations/DestinationBase';
import { Destinations } from './destinations/Destinations';

export class Parser {
    private inst: Document;
    private parser: GlobalState;
    private row: string[][];
    private cell: string[];
    private tableId: number;
    private isTable: boolean;
    private widths: string[];
    private isNouicompat: boolean;
    private cptable: Codepage;

    constructor(document: Document, blob: ArrayBuffer, renderer: Renderer) {
        this.inst = document;
        this.parser = new GlobalState(blob, renderer);
        this.row = [];
        this.isTable = false;
        this.widths = [];
        this.isNouicompat = false;
        this.cptable = new Codepage();
    }

    public parse(): Promise<void> {
        if (this.parser.data.length > 1 && String.fromCharCode(this.parser.data[0]) === '{') {
            this.parseLoop(false, true);
            return Promise.all(this.parser._asyncTasks)
                .then(() => { return; });
        }
        if (this.parser.version == null) {
            throw new RTFJSError('Not a valid rtf document');
        }
        if (this.parser.state != null) {
            throw new RTFJSError('File truncated');
        }
    }

    public handleKeyword(keyword: string, param: number, first: boolean): void {
        if (!this.parser.state.skipdestination) {
            if (first) {
                if (!this.changeDestination(keyword, param)) {
                    let handled = false;
                    const dest = this.parser.state.destination;
                    if (dest != null) {
                        if (dest.handleKeyword != null) {
                            handled = dest.handleKeyword(keyword, param) || false;
                        }
                    }
                    if (!handled && this.parser.state.skipunknowndestination) {
                        this.parser.state.skipdestination = true;
                    }
                }
            } else {
                this.applyText();
                const dest = this.parser.state.destination;
                if (dest != null) {
                    if (dest.handleKeyword != null) {
                        dest.handleKeyword(keyword, param);
                    }
                } else {
                    Helper.log('Unhandled keyword: ' + keyword + ' param: ' + param);
                }
            }
        }
    }

    private eof(): boolean {
        return this.parser.pos >= this.parser.data.length;
    }

    private readChar(): string {
        if (this.parser.pos < this.parser.data.length) {
            this.parser.column++;
            return String.fromCharCode(this.parser.data[this.parser.pos++]);
        }

        throw new RTFJSError('Unexpected end of file');
    }

    private unreadChar(): void {
        if (this.parser.pos > 0) {
            this.parser.column--;
            this.parser.pos--;
        } else {
            throw new RTFJSError('Already at beginning of file');
        }
    }

    private readBlob(cnt: number): ArrayBuffer {
        if (this.parser.pos + cnt > this.parser.data.length) {
            throw new RTFJSError('Cannot read binary data: too long');
        }
        const buf = new ArrayBuffer(cnt);
        const view = new Uint8Array(buf);
        for (let i = 0; i < cnt; i++) {
            view[i] = this.parser.data[this.parser.pos + i];
        }
        return buf;
    }

    private applyDestination(always: boolean): void {
        const dest = this.parser.state.destination;
        if (dest != null) {
            if (always || this.parser.state.parent == null
                || this.parser.state.parent.destination !== this.parser.state.destination) {
                if (dest.apply != null) {
                    dest.apply();
                }
                this.parser.state.destination = null;
            }
        }
    }

    private applyText(): void {
        if (this.parser.text.length > 0) {
            const state = this.parser.state;
            const dest = state.destination;
            if (dest == null) {
                throw new RTFJSError('Cannot route text to destination');
            }
            if (state.parent && state.parent.skipdestination) {
                this.parser.text = '';
                return;
            }
            if (dest != null && dest.appendText != null && !state.skipdestination) {
                const text = this.parser.text.replace(/\t/g, '');
                dest.appendText(text);
                if (this.isTable) {
                    this.cell.push(text);
                    if (this.tableId === null) {
                        this.tableId = dest.getInsLastIndex() - 1;
                    }
                }
            }
            // console.log(dest)
            this.parser.text = '';
        }
    }

    private pushState(forceSkip: boolean): void {
        this.parser.state = new State(this.parser.state);
        if (forceSkip) {
            this.parser.state.skipdestination = true;
        }

        const dest = this.parser.state.destination;
        if (dest != null && !this.parser.state.skipdestination) {
            if (dest.sub != null) {
                const sub = dest.sub();
                if (sub != null) {
                    this.parser.state.destination = sub;
                }
            }
        }
    }

    private popState(): any {
        const state = this.parser.state;
        if (state == null) {
            throw new RTFJSError('Unexpected end of state');
        }

        this.applyText();
        if (state.parent == null || state.destination !== state.parent.destination) {
            this.applyDestination(true);
        }
        this.parser.state = state.parent;

        if (this.parser.state !== null) {
            const currentState = this.parser.state;
            this.inst._ins.push((renderer) => {
                renderer.setChp(new RenderChp(currentState.chp));
            });
            this.inst._ins.push((renderer) => {
                renderer.setPap(new RenderPap(currentState.pap));
            });
        }
        return this.parser.state;
    }

    private changeDestination(name: string, param: number): boolean {
        this.applyText();
        const handler = Destinations[name];
        if (handler != null) {
            this.applyDestination(false);
            if (handler instanceof DestinationFactory) {
                this.parser.state.destination = handler.newDestination(this.parser, this.inst, name, param);
            } else {
                this.parser.state.destination = new handler(this.parser, this.inst, name, param);
            }
            return true;
        }
        return false;
    }

    private processKeyword(keyword: string, param: number): string {
        const first = this.parser.state.first;
        if (first) {
            if (keyword === '*') {
                this.parser.state.skipunknowndestination = true;
                // this.parser.state.skipdestination = false;
                return;
            } else if (keyword === 'fldinst') {
                this.parser.state.skipunknowndestination = false;
            }

            this.parser.state.first = false;
        }

        if (this.parser.state.bindata > 0) {
            throw new RTFJSError('Keyword encountered within binary data');
        }

        // Reset if we unexpectedly encounter a keyword
        this.parser.state.skipchars = 0;
        switch (keyword) {
            case '\n':
                return '\n';
            case '\r':
                return '\r';
            case 'tab':
                return '\t';
            case 'ldblquote':
                return '“';
            case 'rdblquote':
                return '”';
            case '{':
            case '}':
            case '\\':
                return keyword;
            case 'ansicpg':
                let char: string;
                let chars = '';
                let index = 0;
                let splitIndex = 0;
                // tslint:disable-next-line: no-conditional-assignment
                while (char = this.readChar()) {
                    index++;
                    if (char === '\\' && splitIndex++ === 2) {
                        break;
                    }
                    chars += char;
                }
                while (index--) {
                    this.unreadChar();
                }
                if (chars.indexOf('\\nouicompat') > -1) {
                    this.isNouicompat = true;
                }
                this.handleKeyword(keyword, param, first);
                return;
            case 'uc':
                if (param != null && param >= 0) {
                    this.parser.state.ucn = param;
                }
                break;
            case 'u':
                if (param != null) {
                    if (param < 0) {
                        param += 65536;
                    }
                    if (param < 0 || param > 65535) {
                        throw new RTFJSError('Invalid unicode character encountered');
                    }

                    const symbol = SymbolTable[param.toString(16)
                        .substring(2)];
                    this.appendText(symbol !== undefined ? symbol : String.fromCharCode(param));
                    this.parser.state.skipchars = this.parser.state.ucn;
                }
                return;

            case 'bin':
                if (param == null) {
                    throw new RTFJSError('Binary data is missing length');
                }
                if (param < 0) {
                    throw new RTFJSError('Binary data with invalid length');
                }
                this.parser.state.bindata = param;
                return;

            case 'upr':
                this.parseLoop(true, false); // skip the first sub destination (ansi)
                // this will be followed by a \ud sub destination
                return;
            case 'ud':
                return;
            case 'irowband':
                // table row 会出现两个0
                if (this.tableId !== null && this.row.length > 0) {
                    this.inst._table.push({key: this.tableId, value: [].concat(this.row)});
                    // this.isTable = false;
                    // console.log(this.inst._table)
                    if (this.widths.length > 0) {
                        this.inst._tableArea.push(this.widths);
                        this.widths = [];
                    }
                }

                const chart = String.fromCharCode(this.parser.data[this.parser.pos - 1]);
                if (chart === '0' && this.row.length === 0) {
                    this.tableId = null;
                }
                this.isTable = true;
                this.row = [];
                this.cell = [];
                this.handleKeyword(keyword, param, first);
                return;
            case 'cell':
                this.handleKeyword(keyword, param, first);
                this.row.push([].concat(this.cell));
                this.cell = [];
                return;
            case 'clwWidth':
                // 只会在第一行的时候获取td宽度
                if (this.isTable && this.tableId === null && this.row.length === 0) {
                    const pos = this.parser.pos;
                    const data = this.parser.data;
                    const num = String.fromCharCode(data[pos - 5], data[pos - 4],
                            data[pos - 3], data[pos - 2], data[pos - 1]);
                    this.widths.push(((parseFloat(num.replace(/[^\d]/g, '')) || 0) / 11.85).toFixed(0));
                }
            default:
                this.handleKeyword(keyword, param, first);
                return;
        }

        this.parser.state.skipdestination = false;
    }

    private appendText(text: string): void {
        // Handle characters not found in codepage
        text = text ? text : '';
        const state = this.parser.state;
        state.first = false;
        if (state.skipchars > 0) {
            const len = text.length;
            if (state.skipchars >= len) {
                state.skipchars -= len;
                return;
            }

            if (state.destination == null || !state.skipdestination) {
                this.parser.text += text.slice(state.skipchars);
            }
            state.skipchars = 0;
        } else if (state.destination == null || !state.skipdestination) {
            this.parser.text += text;
        }
    }

    private applyBlob(blob: ArrayBuffer): void {
        this.parser.state.first = false;
        this.applyText();
        if (this.parser.state.skipchars > 0) {
            // \bin and all its data is considered one character for skipping purposes
            this.parser.state.skipchars--;
        } else {
            const dest = this.parser.state.destination;
            if (dest == null) {
                throw new RTFJSError('Cannot route binary to destination');
            }
            if (dest != null && dest.handleBlob != null && !this.parser.state.skipdestination) {
                dest.handleBlob(blob);
            }
        }
    }

    private parseKeyword(process: boolean): void {
        if (this.parser.state == null) {
            throw new RTFJSError('No state');
        }

        let param: number;
        let ch = this.readChar();
        if (!Helper._isalpha(ch)) {
            // tslint:disable-next-line: quotemark
            if (ch === "\'") {
                let hex = this.readChar() + this.readChar();
                let hexPic1: string;
                let hexPic2: string;

                if (this.parser.state.pap.charactertype === Helper.CHARACTER_TYPE.DOUBLE || this.isNouicompat
                         // tslint:disable-next-line: no-conditional-assignment
                         === true && (hexPic1 = this.readChar()) + (hexPic2 = this.readChar()) === '\\\'') {
                    if (!hexPic1) {
                        hexPic1 = this.readChar();
                        hexPic2 = this.readChar();
                    }
                    hex += this.readChar() + this.readChar();
                } else if (this.isNouicompat) {
                    this.unreadChar();
                    this.unreadChar();
                }

                // 这里对图片相关路径进行处理 (在中文路径解析时报错)
                if (hex === '5cer' && hexPic1 + hexPic2 === 'Us') {
                    let char: string;
                    // tslint:disable-next-line: no-conditional-assignment
                    while (char = this.readChar()) {
                        if (char === '}') {
                            this.unreadChar();
                            return;
                        }
                    }
                }

                param = Helper._parseHex(hex);
                if (isNaN(param)) {
                    if (hexPic1) {
                        this.unreadChar();
                        this.unreadChar();
                        this.unreadChar();
                        this.unreadChar();
                    }
                    return;
                    // throw new RTFJSError("Could not parse hexadecimal number");
                }

                if (process) {
                    // Looking for current fonttbl charset
                    let codepage = this.parser.codepage;
                    if (this.parser.state.chp.hasOwnProperty('fontfamily')) {
                        // 中文字体获取
                        if (codepage === 936 && this.parser.state.destination.index !== undefined &&
                            this.parser.state.destination.fontfamily === undefined) {
                            // let dest = this.parser.state.destination
                            let char: string;
                            let text = '';
                            let chars = hex;
                            if (chars.length === 4) {
                                const num = Helper._parseHex(chars);
                                if (!isNaN(num)) { // cptable[codepage].dec [num];
                                    text += String.fromCharCode(this.cptable.getCodepage(chars) || num);
                                    chars = '';
                                }
                            }
                            // tslint:disable-next-line: no-conditional-assignment
                            while (char = this.readChar() + this.readChar()) {
                                // tslint:disable-next-line: quotemark
                                if (char !== "\\\'") {
                                    this.unreadChar();
                                    this.unreadChar();
                                    break;
                                }
                                chars += this.readChar() + this.readChar();
                                if (chars.length === 4) {
                                    const num = Helper._parseHex(chars);
                                    if (isNaN(num)) {
                                        break;
                                    }
                                    text += String.fromCharCode(this.cptable.getCodepage(chars) || num);
                                    chars = '';
                                }
                            }
                            if (text) {
                                this.appendText(text);
                                return;
                            }
                        }
                        const idx = this.parser.state.chp.fontfamily;
                        if (this.inst._fonts !== undefined && this.inst._fonts[idx] != null
                            && this.inst._fonts[idx].charset !== undefined && this.inst._fonts[idx].charset != null) {
                            codepage = this.inst._fonts[idx].charset;
                        }
                    }

                    if (!hexPic1) {
                        return;
                    }

                    this.appendText(String.fromCharCode(this.cptable.getCodepage(hex) || param));
                }
            } else if (process) {
                const text = this.processKeyword(ch, param);
                if (text != null) {
                    this.appendText(text);
                }
            }
        } else {
            let keyword = ch;
            ch = this.readChar();
            while (keyword.length < 30 && Helper._isalpha(ch)) {
                keyword += ch;
                ch = this.readChar();
            }

            let num;
            if (ch === '-') {
                num = '-';
                ch = this.readChar();
            } else {
                num = '';
            }

            if (Helper._isdigit(ch)) {
                do {
                    num += ch;
                    ch = this.readChar();
                } while (num.length < 20 && Helper._isdigit(ch));

                if (num.length >= 20) {
                    throw new RTFJSError('Param for keyword ' + keyword + ' too long');
                }

                param = parseInt(num, 10);
                if (isNaN(param)) {
                    throw new RTFJSError('Invalid keyword ' + keyword + ' param');
                }
            }

            if (ch !== ' ') {
                this.unreadChar();
            }

            if (process) {
                const text = this.processKeyword(keyword, param);
                if (text != null) {
                    this.appendText(text);
                }
            }
        }
    }

    private parseLoop(skip: boolean, process: boolean): void {
        try {
            const initialState = this.parser.state;
            main_loop: while (!this.eof()) {
                if (this.parser.state != null && this.parser.state.bindata > 0) {
                    const blob = this.readBlob(this.parser.state.bindata);
                    this.parser.state.bindata = 0;
                    this.applyBlob(blob);
                } else {
                    // if(this.parser.pos > 2871) {
                    //     let i = this.parser.pos
                    // }
                    const ch = this.readChar();
                    switch (ch) {
                        case '\r':
                            continue;
                        case '\n':
                            this.parser.line++;
                            this.parser.column = 0;
                            continue;
                        case '{':
                            this.pushState(skip);
                            break;
                        case '}':
                            // if (this.parser.state.destination.index === 0) {
                            //     console.log(111)
                            // }
                            if (initialState === this.parser.state) {
                                this.unreadChar();
                                break main_loop;
                            } else if (this.popState() === initialState) {
                                break main_loop;
                                 }
                            break;
                        case '\\':
                            this.parseKeyword(!skip ? process : null);
                            break;
                        default:
                            if (!skip) {
                                this.appendText(ch);
                            }
                            break;
                    }
                }
            }
        } catch (error) {
            if (error instanceof RTFJSError) {
                error.message += ' (line: ' + this.parser.line + '; column: ' + this.parser.column + ')';
            }
            throw error;
        }
    }
}
