function __assert(t,...e){if(!t)throw 2===e.length?new __AssertionError(e[0],e[1]):new Error(e[0])}function __assertFunction(t){__assert(__isFunction(t),"function",t)}function __assertArray(t){__assert(__isArray(t),"array",t)}function __assertNotEmpty(t){__assert(!__isEmpty(t),"Sequence is empty!")}function __assertIterable(t){__assert(__isIterable(t),"iterable",t)}function __assertCollection(t){__assert(__isCollection(t),"collection",t)}function __assertNumeric(t){__assert(__isNumeric(t),"numeric value",t)}function __assertNumberBetween(t,e,r=1/0){__assertNumeric(t),__assert(t>=e&&t<=r,`Number must be between ${e} and ${r}!`)}function __assertIndexInRange(t,e){__assertCollection(t),__assert(__isNumeric(e),"number",e),__assert(e>=0&&e<t.count(),"Index is out of bounds")}function __defaultEqualityCompareFn(t,e){return t===e}function __isArray(t){return t instanceof Array}function __isFunction(t){return"function"==typeof t}function __isNumeric(t){return!isNaN(parseFloat(t))}function __isEmpty(t){return t[Symbol.iterator]().next().done}function __isIterable(t){return Symbol.iterator in Object(t)}function __isString(t){return"string"==typeof t}function __isCollection(t){return t instanceof __Collection}function __isGenerator(t){return t instanceof function*(){}.constructor}function __isUndefined(t){return void 0===t}function __isPredicate(t){return!__isNative(t)&&__isFunction(t)&&1==__getParameterCount(t)}function __isNative(t){return/native code/.test(Object(t).toString())||!!~__nativeConstructors.indexOf(t)}function __aggregateCollection(t,e,r,n){__assertFunction(r),__assertFunction(n);let o=e;for(let e of t)o=r(o,e);return n(o)}function __removeDuplicates(t,e=__defaultEqualityCompareFn){__assertIterable(t),__assertFunction(e);const r=[];return new __Collection(function*(){t:for(let n of t){for(let t of r)if(e(n,t))continue t;r.push(n),yield n}})}function __removeFromArray(t,e){__assertArray(t);let r,n,o=[];for(;(n=t.shift())&&!(r=__defaultEqualityCompareFn(n,e));)o.push(n);return t.unshift(...o),r}function __getDefault(t=Object){if(t&&__isNative(t)&&"function"==typeof t){let e=t();return e instanceof Object||t===Date?null:e}return t}function __getParameterCount(t){return __assertFunction(t),t.length}function __getComparatorFromKeySelector(t,e=defaultComparator){if(__isFunction(t))return new Function("comparator","keySelectorFn","a","b","return comparator(keySelectorFn(a), keySelectorFn(b))").bind(null,e,t);if(__isString(t))return t.startsWith("[")||t.startsWith(".")||(t=`.${t}`),new Function("comparator","a","b",`return comparator(a${t}, b${t})`).bind(null,e);throw new __AssertionError("string or function",t)}class __AssertionError extends Error{constructor(t,e){super(`Expected ${t}, got ${e}!`)}}export function defaultComparator(t,e){return t<e?-1:e<t?1:0};const __nativeConstructors=[Object,Number,Boolean,String,Symbol];class __Collection{constructor(t){this.__iterable=null,__assert(__isIterable(t)||__isGenerator(t),"iterable or generator",t),this.__iterable=t}[Symbol.iterator](){const t=this.__iterable;return __isGenerator(t)?t():function*(){yield*t}()}__resultOrDefault(t,e=(t=>!0),r=Object){let n;__isPredicate(e)?n=e:(n=(t=>!0),r=e),__assertFunction(n);const o=__getDefault(r);if(__isEmpty(this))return o;let i=t.call(this,n);return i||o}elementAt(t){return __assertIndexInRange(this,t),this.skip(t).first()}take(t=0){if(__assertNumeric(t),t<=0)return __Collection.empty;const e=this;return new __Collection(function*(){let r=0;for(let n of e)if(yield n,++r===t)break})}skip(t=0){return __assertNumeric(t),t<=0?this:this.skipWhile((e,r)=>r<t)}takeWhile(t=((t,e)=>!0)){__assertFunction(t);const e=this;return new __Collection(function*(){let r=0;for(let n of e){if(!t(n,r++))break;yield n}})}takeUntil(t=((t,e)=>!1)){return this.takeWhile((e,r)=>!t(e,r))}skipWhile(t=((t,e)=>!0)){__assertFunction(t);const e=this;return new __Collection(function*(){let r=0,n=!1;for(let o of e)!n&&t(o,r++)||(n=!0,yield o)})}skipUntil(t=((t,e)=>!1)){return this.skipWhile((e,r)=>!t(e,r))}first(t=(t=>!0)){return __assertFunction(t),__assertNotEmpty(this),this.skipWhile(e=>!t(e))[Symbol.iterator]().next().value}firstOrDefault(t=(t=>!0),e=Object){return this.__resultOrDefault(this.first,t,e)}last(t=(t=>!0)){return __assertFunction(t),__assertNotEmpty(this),this.reverse().first(t)}lastOrDefault(t=(t=>!0),e=Object){return this.__resultOrDefault(this.last,t,e)}single(t=(t=>!0)){__assertFunction(t),__assertNotEmpty(this);let e;for(let r of this){if(t(r)){e=r;break}0}if(this.first(r=>t(r)&&!__defaultEqualityCompareFn(r,e)))throw new Error("Sequence contains more than one element");return e}singleOrDefault(t=(t=>!0),e=Object){return this.__resultOrDefault(this.single,t,e)}defaultIfEmpty(t){return __isEmpty(this)?new __Collection([__getDefault(t)]):this}concat(t){__assertIterable(t);const e=this;return new __Collection(function*(){yield*e,yield*t})}union(t,e=__defaultEqualityCompareFn){return __assertIterable(t),this.concat(t).distinct(e)}join(t,e,r,n,o=__defaultEqualityCompareFn){__assertIterable(t),__assertFunction(e),__assertFunction(r),__assertFunction(n),__assertFunction(o);const i=this;return new __Collection(function*(){for(let s of i){const i=e(s);for(let e of t){const t=r(e);o(i,t)&&(yield n(s,e))}}})}except(t){__assertIterable(t),__isCollection(t)||(t=new __Collection(t));const e=this;return new __Collection(function*(){for(let r of e)t.contains(r)||(yield r)})}zip(t,e){__assertIterable(t),__assertFunction(e);const r=this;return new __Collection(function*(){const n=t[Symbol.iterator]();for(let t of r){const r=n.next();if(r.done)break;yield e(t,r.value)}})}intersect(t,e=__defaultEqualityCompareFn){__assertIterable(t),__assertFunction(e);const r=this;return new __Collection(function*(){const n=__Collection.from(t);for(let t of r)n.any(r=>e(t,r))&&(yield t)})}sequenceEqual(t,e=__defaultEqualityCompareFn){if(!__isIterable(t))return!1;const r=this[Symbol.iterator](),n=t[Symbol.iterator]();let o,i;do{if(o=r.next(),i=n.next(),o.done!=i.done)return!1;if(!o.done&&!e(o.value,i.value))return!1}while(!o.done);return!0}static __getEqualKey(t,e,r){for(let n of t.keys())if(r(n,e))return n;return e}groupBy(t,...e){function r(e){let r=2===__getParameterCount(e);const n=_.first();try{const o=t(n);r=r&&e(o,o)&&!e(o,{})}catch(t){r=!1}return r}function n(t){return s(t,t=>t,void 0,__defaultEqualityCompareFn)}function o(t,e){let n,o;return r(e)?(n=e,o=(t=>t)):(n=__defaultEqualityCompareFn,o=e),i(t,o,n)}function i(t,e,n){let o,i,_;return r(n)?o=n:_=n,2===__getParameterCount(e)?_=e:i=e,o||(o=__defaultEqualityCompareFn),i||(i=(t=>t)),s(t,i,_,o)}function s(t,e,r,n){__assertFunction(t),__assertFunction(e),__assert(__isUndefined(r)||__isFunction(r),"resultSelector must be undefined or function!"),__assertFunction(n);let o,i=new Map;for(let r of _){const o=__Collection.__getEqualKey(i,t(r),n),s=e(r);i.has(o)?i.get(o).push(s):i.set(o,[s])}return o=r?__Collection.from(i).select(t=>r(...t)):i}const _=this;let a;switch(e.length){case 0:a=n;break;case 1:a=o;break;case 2:a=i;break;case 3:a=s;break;default:throw new Error("GroupBy parameter count can not be greater than 4!")}return a(t,...e)}groupJoin(t,e,r,n,o=__defaultEqualityCompareFn){__assertIterable(t),__assertFunction(e),__assertFunction(r),__assertFunction(n);let i=new Map;for(let n of this){const s=e(n);i.set(n,new __Collection(function*(){for(let e of t)o(s,r(e))&&(yield e)}))}return new __Collection(function*(){for(let[t,e]of i)yield n(t,e)})}add(t){this.insert(t,this.count())}insert(t,e){const r=this.toArray();__assert(e>=0&&e<=r.length,"Index is out of bounds!"),this.__iterable=function*(){yield*r.slice(0,e),yield t,yield*r.slice(e,r.length)}}remove(t){let e=this.toArray();return!!__removeFromArray(e,t)&&(this.__iterable=function*(){yield*e},!0)}min(t=(t=>t)){return __assertFunction(t),__assertNotEmpty(this),this.select(t).aggregate((t,e)=>t<e?t:e)}max(t=(t=>t)){return __assertFunction(t),__assertNotEmpty(this),this.select(t).aggregate((t,e)=>t>e?t:e)}sum(t=(t=>t)){return __assertNotEmpty(this),this.select(t).aggregate(0,(t,e)=>t+e)}average(t=(t=>t)){return __assertNotEmpty(this),this.sum(t)/this.count()}order(t=defaultComparator){return this.orderBy(t=>t,t)}orderDescending(t=defaultComparator){return this.orderByDescending(t=>t,t)}orderBy(t,e=defaultComparator){return __assertFunction(e),new __OrderedCollection(this,__getComparatorFromKeySelector(t,e))}orderByDescending(t,e=defaultComparator){return new __OrderedCollection(this,__getComparatorFromKeySelector(t,(t,r)=>e(r,t)))}shuffle(){return this.orderBy(()=>Math.floor(3*Math.random())-1)}indexOf(t,e=__defaultEqualityCompareFn){__assertFunction(e);let r=0;for(let n of this){if(e(n,t))return r;r++}return-1}lastIndexOf(t,e=__defaultEqualityCompareFn){__assertFunction(e);let r=0,n=-1;for(let o of this)e(o,t)&&(n=r),r++;return n}contains(t,e=__defaultEqualityCompareFn){return!!~this.indexOf(t,e)}where(t=((t,e)=>!0)){__assertFunction(t);const e=this;return new __Collection(function*(){let r=0;for(let n of e)t(n,r)&&(yield n),r++})}conditionalWhere(t,e){return t?this.where(e):this}count(t=(t=>!0)){let e=0,r=this.where(t)[Symbol.iterator]();for(;!r.next().done;)e++;return e}any(t=null){return!__isEmpty(this)&&(!t||!this.where(t)[Symbol.iterator]().next().done)}all(t=(t=>!0)){return __assertFunction(t),!this.any(e=>!t(e))}aggregate(t,e=null,r=null){return!__isFunction(t)||e||r?__isFunction(t)||!__isFunction(e)||r?__aggregateCollection(this,t,e,r):__aggregateCollection(this,t,e,t=>t):__aggregateCollection(this.skip(1),this.first(),t,t=>t)}select(t=(t=>t)){const e=this;let r=0;return new __Collection(function*(){for(let n of e)yield t(n,r),r++})}flatten(){return this.selectMany(t=>t)}selectMany(t,e=((t,e)=>e)){__assertFunction(t),__assertFunction(e);const r=this;return new __Collection(function*(){let n=0;for(let o of r){let r=t(o,n),i=r;i=__isIterable(r)?r:[r];for(let t of i[Symbol.iterator]())yield e(o,t);n++}})}distinct(t=__defaultEqualityCompareFn){return __assertFunction(t),__removeDuplicates(this,t)}toArray(){return[...this]}toDictionary(t,e=null,r=null){if(__assertFunction(t),!e&&!r)return this.toDictionary(t,t=>t,__defaultEqualityCompareFn);if(!r&&1===__getParameterCount(e))return this.toDictionary(t,e,__defaultEqualityCompareFn);if(!r&&2===__getParameterCount(e))return this.toDictionary(t,t=>t,e);__assertFunction(r),__assertFunction(e);let n=[],o=new Map;for(let i of this){let s=t(i),_=e(i);__assert(null!=s,"Key is not allowed to be null!"),__assert(!__Collection.from(n).any(t=>r(t,s)),`Key '${s}' is already in use!`),n.push(s),o.set(s,_)}return o}toLookup(t,e=null,r=null){return __assertFunction(t),e||r?(r||1!==__getParameterCount(e))&&(r||2!==__getParameterCount(e))?(__assertFunction(r),__assertFunction(e),this.groupBy(t,e,r)):this.groupBy(t,e):this.groupBy(t)}reverse(){const t=this.toArray();return new __Collection(function*(){for(let e=t.length-1;e>=0;e--)yield t[e]})}forEach(t){__assertFunction(t);for(let e of this)t(e)}static from(t){return new __Collection(t)}static range(t,e){return __assertNumberBetween(e,0,1/0),new __Collection(function*(){let r=t;for(;r!=e+t;)yield r++})}static repeat(t,e){return __assertNumberBetween(e,0,1/0),new __Collection(function*(){for(let r=0;r<e;r++)yield t})}static get empty(){return new __Collection([])}}class __HeapElement{constructor(t,e){this.__index=t,this.__value=e}static __createHeapElement(t,e){return void 0===e||e instanceof __HeapElement?e:new __HeapElement(t,e)}}export class _MinHeap{constructor(t,e=defaultComparator){__assertArray(t),__assertFunction(e),this.__elements=t,this.__comparator=((t,r)=>{let n=e(t.__value,r.__value);return 0!==n?n:defaultComparator(t.__index,r.__index)}),this.__createHeap(this.__elements,this.__comparator)}__heapify(t,e,r){let n=2*(r+1),o=n-1,i=r;if(t[i]=__HeapElement.__createHeapElement(i,t[i]),o<t.length&&(t[o]=__HeapElement.__createHeapElement(o,t[o]),e(t[o],t[i])<0&&(i=o)),n<t.length&&(t[n]=__HeapElement.__createHeapElement(n,t[n]),e(t[n],t[i])<0&&(i=n)),i!==r){let n=t[r];t[r]=t[i],t[i]=n,this.__heapify(t,e,i)}}__createHeap(t,e){if(0!==t.length)for(let r=Math.floor(t.length/2);r>=0;r--)this.__heapify(t,e,r)}__hasTopElement(){return this.__elements.length>0}__getTopElement(){if(1===this.__elements.length)return this.__elements.pop().__value;let t=this.__elements[0];return this.__elements[0]=this.__elements.pop(),this.__heapify(this.__elements,this.__comparator,0),t.__value}[Symbol.iterator](){let t=this;return{next:function(){return t.__hasTopElement()?{done:!1,value:t.__getTopElement()}:{done:!0,value:void 0}}}}};class __OrderedCollection extends __Collection{constructor(t,e){__assertFunction(e),super(t),this.__comparator=e}thenBy(t,e=defaultComparator){const r=this.__comparator,n=__getComparatorFromKeySelector(t,e);return new __OrderedCollection(this.__iterable,(t,e)=>{const o=r(t,e);return 0!==o?o:n(t,e)})}thenByDescending(t,e=defaultComparator){return this.thenBy(t,(t,r)=>e(r,t))}[Symbol.iterator](){let t=this,e=super[Symbol.iterator].bind(this);return function*(){yield*new _MinHeap([...{[Symbol.iterator]:e}],t.__comparator)}()}}export const Collection=__Collection;export default Collection;export function extendIterablePrototype(t,e=[]){e.push("constructor");let r=Collection.from(e),n=[];for(let e of Object.getOwnPropertyNames(Object.getPrototypeOf(Collection.empty)))if(!e.startsWith("_")&&!r.contains(e)&&__isFunction(Collection.empty[e])){if(e in t)throw new Error(`The method "${e}" already exists on the "${t.constructor&&t.constructor.name}" prototype. `+"Use the exclude parameter to patch without this method.");n.push(e)}for(let e of n)t[e]=function(...t){let r=Collection.from(this);return r[e].call(r,...t)}};export function extendNativeTypes(){extendIterablePrototype(Array.prototype,["concat","forEach","indexOf","join","lastIndexOf","reverse"]);const t=Array.prototype.join;Array.prototype.join=function(...e){if(4==e.length||5==e.length){let t=Collection.from(this);return t.join.call(t,...e)}return t.call(this,...e)};const e=Array.prototype.indexOf;Array.prototype.indexOf=function(...t){if(2==t.length&&__isFunction(t[1])){let e=Collection.from(this);return e.indexOf.call(e,...t)}return e.call(this,...t)};const r=Array.prototype.lastIndexOf;Array.prototype.lastIndexOf=function(...t){if(2==t.length&&__isFunction(t[1])){let e=Collection.from(this);return e.lastIndexOf.call(e,...t)}return r.call(this,...t)},extendIterablePrototype(Map.prototype,["add","forEach"]),extendIterablePrototype(Set.prototype,["add","forEach"])};