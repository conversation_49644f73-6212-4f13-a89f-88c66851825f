// import { ParagraphProperties } from "../../paragraph/properties";
import { XmlComponent } from '../../xml-components';
import { ParagraphProperties } from '../../paragraph';
import { AlignType, LineSpacingType, LineSpacingRatio } from '../../../../../common/commonDefines';
import { Alignment, AlignmentOptions } from '../../paragraph/formatting/alignment';
import { Spacing } from '../../paragraph/formatting/spacing';

export class ParagraphPropertiesDefaults extends XmlComponent {

  private properties: ParagraphProperties;

  constructor() {
    super('w:pPrDefault');
    // this.root.push(new ParagraphProperties());
  }

  public alignment(alignType: number): ParagraphPropertiesDefaults {
    this.initializeProperties();

    // default to be justfied
    let alignmentOption = AlignmentOptions.BOTH;
    switch (alignType) {
      case AlignType.Left:
        alignmentOption = AlignmentOptions.LEFT;
        break;
      case AlignType.Right:
        alignmentOption = AlignmentOptions.RIGHT;
        break;
      case AlignType.Center:
        alignmentOption = AlignmentOptions.CENTER;
        break;
      case AlignType.Justify:
        alignmentOption = AlignmentOptions.BOTH;
        break;
      default:
        break;
    }
    this.properties.push(new Alignment(alignmentOption));
    return this;
  }

  // paraspacing
  public paraSpacing(paraSpacingType: number): ParagraphPropertiesDefaults {
    this.initializeProperties();

    const lineMapping = {
      lineType: LineSpacingType.Single,
      line: 1,
    };
    switch (paraSpacingType) {
      case LineSpacingType.Single:
        lineMapping.lineType = LineSpacingType.Single;
        lineMapping.line = LineSpacingRatio.Single;
        break;
      case LineSpacingType.SingeHalf:
        lineMapping.lineType = LineSpacingType.SingeHalf;
        lineMapping.line = LineSpacingRatio.SingeHalf;
        break;
      case LineSpacingType.Double:
        lineMapping.lineType = LineSpacingType.Double;
        lineMapping.line = LineSpacingRatio.Double;
        break;
      case LineSpacingType.Min:
        lineMapping.lineType = LineSpacingType.Min;
        lineMapping.line = LineSpacingRatio.Single;
        break;
      case LineSpacingType.Fixed:
        lineMapping.lineType = LineSpacingType.Fixed;
        // placeholder, shouldn't be here usually
        lineMapping.line = 0.55;
        break;
      case LineSpacingType.Multi:
        lineMapping.lineType = LineSpacingType.Multi;
        // placeholder, shouldn't be here usually
        lineMapping.line = (LineSpacingRatio.Single - 1) * 3 + 1;
        break;
      default:
        break;
    }

    this.properties.push(new Spacing(lineMapping));
    return this;
  }

  /**
   * initialize run properties if not
   */
  private initializeProperties(): void {
    if (!this.properties) {
      this.properties = new ParagraphProperties();

      // should be the first child element
      // this.root.push(this.properties);
      this.root.unshift(this.properties);
    }
  }
}
