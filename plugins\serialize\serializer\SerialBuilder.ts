import { ISerialDocObj, ISerialHFObj, SerialObjType } from '../serialInterface';
import { ISectionPropertiesOptions, PageOrientation } from 'docx';
import { buildBorderOption, transPxToDocxLength } from './Utils';
import SerialDocument from './SerialDocument';

/** 将现有模型转换为简单的可序列化模型 （去除section） */
export default class SerialBuilder {

    constructor(private readonly doc: any) {}

    public serialized(): ISerialDocObj {
        const prop = this.doc.sectionProperty;

        const docObj: ISerialDocObj = {
            type: SerialObjType.Document,
            children: [],
            property: this.buildSectionProperty(prop),
        };
        // fill children
        new SerialDocument(this.doc.content).serializedTo(docObj.children);
        // build header
        const headerDoc = this.doc.getHeaderFooter(true);
        headerDoc && (docObj.header = this.buildHeaderFooterObj(headerDoc));
        // build footer
        const footerDoc = this.doc.getHeaderFooter(false);
        footerDoc && (docObj.footer = this.buildHeaderFooterObj(footerDoc));

        return docObj;
    }

    /** 创建 */
    private buildHeaderFooterObj(hfDoc: any): ISerialHFObj {
        const obj: ISerialHFObj = {
            type: SerialObjType.Header,
            children: [],
            default: undefined,
            even: undefined,
            odd: undefined
        };
        if (hfDoc.isHeader()) {
            obj.type = SerialObjType.Header;
        } else if (hfDoc.isFooter()) {
            obj.type = SerialObjType.Footer;
        } else {
            return undefined;
        }
        obj.default = new SerialDocument(hfDoc.content).serializedTo();
        // TODO: 暂时不支持基偶页

        return obj;
    }

    /** 创建section属性 */
    private buildSectionProperty(prop: any): ISectionPropertiesOptions {
        const sectionProperty: ISectionPropertiesOptions = {
            page: {
                size: prop.pageSize && {
                    width: transPxToDocxLength(prop.pageSize.width),
                    height: transPxToDocxLength(prop.pageSize.height),
                    orientation: prop.pageSize.orient === 'landscape' ?
                                PageOrientation.LANDSCAPE :
                                PageOrientation.PORTRAIT,
                } || undefined,
                margin: prop.pageMargins && {
                    top: transPxToDocxLength(prop.pageMargins.paddingTop),
                    right: transPxToDocxLength(prop.pageMargins.paddingRight),
                    bottom: transPxToDocxLength(prop.pageMargins.paddingBottom),
                    left: transPxToDocxLength(prop.pageMargins.paddingLeft),
                    header: transPxToDocxLength(prop.pageMargins.header),
                    footer: transPxToDocxLength(prop.pageMargins.footer),
                } || undefined,
                pageNumbers: undefined,
                borders: false && { //  暂时不保存边框格式
                    pageBorderTop: buildBorderOption(prop.borders.top),
                    pageBorderRight: buildBorderOption(prop.borders.right),
                    pageBorderBottom: buildBorderOption(prop.borders.bottom),
                    pageBorderLeft: buildBorderOption(prop.borders.left),
                } || undefined,
                // textDirection: PageTextDirectionType.LEFT_TO_RIGHT_TOP_TO_BOTTOM
            },
            grid: undefined,
            headerWrapperGroup: undefined,
            footerWrapperGroup: undefined,
            lineNumbers: undefined,
            titlePage: prop.titlePage,
            // verticalAlign: VerticalAlign.BOTH,
            column: undefined,
            // type: SectionType.NEXT_PAGE
        };
        return sectionProperty;
    }

}
