import { ISerialParaObj, ISerialTableCellObj, ISerialTableObj, SerialObjType } from '../../serialize/serialInterface';
import { TableCell } from 'docx';
import DocxParagraph from './DocxParagraph';
import DocxTable from './DocxTable';
import { IAbstractDocx, IDocx } from './IDocx';

export default class DocxTableCell extends IAbstractDocx implements IDocx{
    constructor(private readonly tableCellObj: ISerialTableCellObj) { super(); }

    public buildTo(collector: TableCell[]): TableCell[] {
        if (!this.tableCellObj || this.tableCellObj.isVerticalMerged) {
            return collector;
        }
        const option: any = Object.assign({}, this.tableCellObj);
        this.deleteUnUsefulProp(option, 'type', 'children');
        const children: any[] = [];
        for (const content of this.tableCellObj.children) {
            if (content.type === SerialObjType.Paragraph) {
                new DocxParagraph(content as ISerialParaObj).buildTo(children);
            } else if (content.type === SerialObjType.Table) {
                new DocxTable(content as ISerialTableObj).buildTo(children);
            }
        }
        option.children = children;
        const cell = new TableCell(option);
        collector.push(cell);
        return collector;
    }
}
