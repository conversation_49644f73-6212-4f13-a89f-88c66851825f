<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>编辑器SDK测试页面</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 0;
      background-color: #f5f5f5;
    }
    
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    
    .header {
      background-color: #fff;
      padding: 15px 20px;
      border-radius: 5px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      margin-bottom: 20px;
    }
    
    .content {
      display: flex;
      gap: 20px;
    }
    
    .sidebar {
      width: 250px;
      background-color: #fff;
      padding: 15px;
      border-radius: 5px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .editor-container {
      flex: 1;
      min-height: 700px;
      background-color: #fff;
      border-radius: 5px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      overflow: hidden;
    }
    
    button {
      padding: 8px 15px;
      margin: 5px 0;
      width: 100%;
      background-color: #4CAF50;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      transition: background-color 0.3s;
    }
    
    button:hover {
      background-color: #45a049;
    }
    
    h1, h2 {
      margin-top: 0;
      color: #333;
    }
    
    .control-group {
      margin-bottom: 15px;
      padding-bottom: 15px;
      border-bottom: 1px solid #eee;
    }
    
    .status {
      margin-top: 15px;
      padding: 10px;
      background-color: #f0f0f0;
      border-radius: 4px;
      font-family: monospace;
      font-size: 12px;
      height: 100px;
      overflow-y: auto;
    }
    
    /* 编辑器样式 */
    #editor-container {
      width: 100%;
      height: 700px;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>编辑器SDK测试页面</h1>
      <p>此页面演示了如何使用编辑器SDK初始化和控制编辑器</p>
    </div>
    
    <div class="content">
      <div class="sidebar">
        <h2>控制面板</h2>
        
        <div class="control-group">
          <h3>基本操作</h3>
          <button id="btn-init">初始化编辑器</button>
          <button id="btn-destroy">销毁编辑器</button>
        </div>
        
        <div class="control-group">
          <h3>编辑器操作</h3>
          <button id="btn-undo" disabled>撤销</button>
          <button id="btn-redo" disabled>重做</button>
          <button id="btn-insert-text" disabled>插入文本</button>
          <button id="btn-get-text" disabled>获取文本</button>
        </div>
        
        <div class="status" id="status-panel">
          状态: 未初始化
        </div>
      </div>
      
      <div class="editor-container">
        <div id="editor-container"></div>
      </div>
    </div>
  </div>
  
  <!-- 加载SDK (从本地) -->
  <script src="/test-sdk.js"></script>
  
  <!-- 备选SDK加载路径 -->
  <script>
    // 如果主SDK加载失败，尝试从其他位置加载
    window.addEventListener('error', function(e) {
      const src = e.target.src || '';
      if (src.includes('test-sdk.js')) {
        console.warn('SDK包装器加载失败，尝试备选路径...');
        
        // 创建新的脚本元素
        const script = document.createElement('script');
        script.src = 'test-sdk.js'; // 尝试相对路径
        document.head.appendChild(script);
      }
    }, true);
  </script>
  
  <script>
    // 状态记录
    let editor = null;
    let editorInitialized = false;
    
    // 获取DOM元素
    const editorContainer = document.getElementById('editor-container');
    const statusPanel = document.getElementById('status-panel');
    const btnInit = document.getElementById('btn-init');
    const btnDestroy = document.getElementById('btn-destroy');
    const btnUndo = document.getElementById('btn-undo');
    const btnRedo = document.getElementById('btn-redo');
    const btnInsertText = document.getElementById('btn-insert-text');
    const btnGetText = document.getElementById('btn-get-text');
    
    // 状态更新函数
    function updateStatus(message) {
      const timestamp = new Date().toLocaleTimeString();
      statusPanel.innerHTML += `<div>[${timestamp}] ${message}</div>`;
      statusPanel.scrollTop = statusPanel.scrollHeight;
    }
    
    // 更新按钮状态
    function updateButtons(initialized) {
      btnInit.disabled = initialized;
      btnDestroy.disabled = !initialized;
      btnUndo.disabled = !initialized;
      btnRedo.disabled = !initialized;
      btnInsertText.disabled = !initialized;
      btnGetText.disabled = !initialized;
    }
    
    // 初始化编辑器
    async function initEditor() {
      try {
        updateStatus("开始初始化编辑器...");
        
        // 检查SDK是否可用
        if (typeof Editor === 'undefined') {
          throw new Error('SDK未正确加载，Editor类不可用。请确保SDK脚本已正确加载。');
        }
        
        // 创建SDK选项
        const optionInit = {
          id: Date.now(),  // 使用时间戳作为唯一ID
          dom: editorContainer,
          src: '/index.html?time=' + new Date().getTime(),
          option: {
            bShowMenu: true,
            bShowToolbar: true,
            isTest: true,
            bNeedCustomFont: false,
          },
        };
        
        // 使用Editor SDK初始化
        updateStatus("调用Editor SDK...");
        const res = await new Editor().init(optionInit);
        editor = await res.getEditor();
        
        editorInitialized = true;
        updateStatus("编辑器初始化成功！");
        updateButtons(true);
      } catch (error) {
        updateStatus(`初始化失败: ${error.message || error}`);
        console.error('初始化错误:', error);
      }
    }
    
    // 销毁编辑器
    function destroyEditor() {
      try {
        if (!editorInitialized || !editor) {
          updateStatus("没有活动的编辑器实例");
          return;
        }
        
        // 清空容器
        editorContainer.innerHTML = '';
        editor = null;
        editorInitialized = false;
        
        updateStatus("编辑器已销毁");
        updateButtons(false);
      } catch (error) {
        updateStatus(`销毁失败: ${error.message || error}`);
        console.error('销毁错误:', error);
      }
    }
    
    // 绑定按钮事件
    btnInit.addEventListener('click', initEditor);
    btnDestroy.addEventListener('click', destroyEditor);
    
    // 撤销操作
    btnUndo.addEventListener('click', async () => {
      if (!editorInitialized || !editor) return;
      try {
        await editor.undo();
        updateStatus("执行撤销操作");
      } catch (error) {
        updateStatus(`撤销失败: ${error.message || error}`);
      }
    });
    
    // 重做操作
    btnRedo.addEventListener('click', async () => {
      if (!editorInitialized || !editor) return;
      try {
        await editor.redo();
        updateStatus("执行重做操作");
      } catch (error) {
        updateStatus(`重做失败: ${error.message || error}`);
      }
    });
    
    // 插入文本
    btnInsertText.addEventListener('click', async () => {
      if (!editorInitialized || !editor) return;
      try {
        const text = "这是通过SDK插入的文本 - " + new Date().toLocaleString();
        await editor.insertText(text);
        updateStatus(`插入文本: "${text}"`);
      } catch (error) {
        updateStatus(`插入文本失败: ${error.message || error}`);
      }
    });
    
    // 获取文本
    btnGetText.addEventListener('click', async () => {
      if (!editorInitialized || !editor) return;
      try {
        const text = await editor.getText();
        updateStatus(`获取文本: 长度=${text.length}字符`);
        console.log('编辑器文本:', text);
      } catch (error) {
        updateStatus(`获取文本失败: ${error.message || error}`);
      }
    });
    
    // 页面加载完成后显示初始状态
    window.onload = function() {
      updateStatus('页面加载完成，请点击"初始化编辑器"按钮');
      updateButtons(false);
    };
    
    // 检查SDK是否正确加载
    window.addEventListener('DOMContentLoaded', function() {
      if (typeof Editor === 'undefined') {
        const statusPanel = document.getElementById('status-panel');
        statusPanel.innerHTML = `<div style="color: red; font-weight: bold">
          错误: SDK加载失败! "Editor"类未定义。<br><br>
          请确保：<br>
          1. dev:site服务正在运行 (npm run dev:site)<br>
          2. 服务运行在http://127.0.0.1:5001<br>
          3. test-sdk.js文件存在于服务根目录<br><br>
          您也可以尝试在控制台中查看网络请求，检查test-sdk.js加载是否有错误。
        </div>`;
        
        // 禁用所有按钮
        document.querySelectorAll('button').forEach(btn => {
          btn.disabled = true;
        });
      }
    });
  </script>
</body>
</html> 