import { INumLvl, IParaNumPr, NumberingType, ResultType } from '@/common/commonDefines';
import Paragraph from '../Paragraph';
import ParaIndentation from '../Paragraph/ParaIndentation';
import TextProperty from '../TextProperty';
import { getMMFromPx, getPxForMM, measure } from '../util';

export class NumLvl implements INumLvl {
    private para: Paragraph;
    private textPr: TextProperty;
    private lvlText: any[];
    private format: NumberingType;
    private paraInd: number; // mm数值存储，用来还原当前段落缩进
    private paraInd2: number; // pxValue

    // private start: number;
    // private restart: number;
    // private suff: string;
    // private jc: number;
    // private pStyle: any; // 特殊样式
    constructor(format: NumberingType, para: Paragraph, textPr?: TextProperty) {
        this.textPr = textPr || new TextProperty();
        this.para = para;
        this.format = format;
        this.lvlText = [];
        if (this.isBullet()) {
            this.textPr.fontSize = 9;
        }
    }

    public getFormat(): NumberingType {
        return this.format;
    }

    public getText(): string {
        return this.lvlText.join('');
    }

    public setText(nPos: number): number {
        if (this.isNumbered()) {
            this.lvlText = [nPos, '.'];
        } else {
            this.lvlText = [String.fromCharCode(11044)];
        }

        return ResultType.Success;
    }

    public setTextPr(textPr: TextProperty): number {
        if (!textPr) {
            return ResultType.UnEdited;
        }

        this.textPr = textPr.copy();
        return ResultType.Success;
    }

    public getTextPro(): TextProperty {
        return this.textPr;
    }

    public getParaPr(): IParaNumPr {
        return this.para.getNumberingPr();
    }

    public setPara(para: Paragraph): number {
        this.para = para;
        return ResultType.Success;
    }

    public getPara(): Paragraph {
        return this.para;
    }

    public getParaId(): number {
        return this.para.getId();
    }

    public setNumValue(num: number): void {
        this.lvlText[0] = num;
    }

    public isNumbered(): boolean {
        return this.format === NumberingType.Number;
    }

    public isBullet(): boolean {
        return this.format === NumberingType.Bullet;
    }

    public remove(): void {
        this.para.removeNumbering();
        this.removeParaInd();
    }

    public addParaNumbering(paraId: number, ind: number): void {
        this.para.addNumbering(paraId);
        this.setExtendParaInd(ind);
    }

    public getParaInd(): number {
        return this.paraInd2;
    }

    public setParaInd(): void {
        let ind = this.getTextWidth();
        let sub: number = ind;
        if (this.paraInd2 !== undefined) {
           sub = ind - this.paraInd2;
        }

        // 这里多次进行设置
        if (Math.abs(sub) < 0.2) {
            return;
        }
        this.paraInd2 = ind;
        ind = sub;

        ind = getMMFromPx(ind) + this.para.paraProperty.paraInd.left;
        this.paraInd = ind;
        this.updateParaInd(ind);
    }

    private setExtendParaInd(oldInd?: number): void {
        let ind = this.getTextWidth();
        let sub: number = ind;
        const newInd = this.paraInd2 = ind;
        let left = this.para.paraProperty.paraInd.left;
        if (oldInd !== undefined) {
            if (left > 0.01) {
                sub = newInd - oldInd;
            }
        } else {
            sub = newInd;
            left = 0;
        }

        ind = getMMFromPx(sub) + left;
        this.paraInd = ind;
        if (Math.abs(sub) < 0.2) {
            return;
        }
        this.updateParaInd(ind);
    }

    private getTextWidth(): number {
        // const text = this.getText();
        // const ms = measure(text, this.textPr);
        let ind: number = 18.9; // 5mm
        // if (ms) {
        //     let width: number = 0;
        //     ms.forEach((m) => {
        //         width += m.width;
        //     });
        //     ind += width;
        // }
        ind += 9; // 先固定圈的大小
        return ind;
    }

    private removeParaInd(): void {
        const paraIndentation = new ParaIndentation();
        paraIndentation.firstLine = undefined;
        paraIndentation.right = undefined;
        paraIndentation.left = Math.max(this.para.paraProperty.paraInd.left - this.paraInd, 0);
        this.para.setIndentation(paraIndentation);
    }

    private updateParaInd(ind: number): void {
        const paraIndentation = new ParaIndentation();
        paraIndentation.left = ind;
        paraIndentation.firstLine = undefined;
        paraIndentation.right = undefined;
        this.para.setIndentation(paraIndentation);
    }
}
