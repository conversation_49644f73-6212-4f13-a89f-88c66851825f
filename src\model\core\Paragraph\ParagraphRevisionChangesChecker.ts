import Paragraph from '../Paragraph';
import { RevisionsManager } from '../RevisionsManager';
import { ParagraphContentPos, ParaElementType } from './ParagraphContent';
import { ReviewInfo, Revision } from '../Revision';
import TextProperty from '../TextProperty';
import { ReviewType, RevisionChangeType } from '../../../common/commonDefines';

export interface IRevisionAddRemove {
    changeType: ReviewType;
    startPos: ParagraphContentPos;
    endPos: ParagraphContentPos;
    value: string[];
    userId: string;
    userName: string;
    dataTime: Date;
}

export interface IRevisionTextPr {
    property: TextProperty;
    startPos: ParagraphContentPos;
    endPos: ParagraphContentPos;
    userId: string;
    userName: string;
    dataTime: Date;
}

export enum RevisionObjectType {
    Drawing = '0',
    MedEquation = '1',
}

export class ParagraphRevisionChangesChecker {
    private paragraph: Paragraph;
    private paraId: number;
    private revisionsManager: RevisionsManager;
    private addRemove: IRevisionAddRemove;
    private textPr: IRevisionTextPr;

    constructor(para: Paragraph, manager: RevisionsManager) {
        this.paragraph = para;
        this.paraId = para.id;
        this.revisionsManager = manager;
        this.addRemove = {
            changeType: null,
            startPos: null,
            endPos: null,
            value: [],
            userId: '',
            userName: '',
            dataTime: new Date(),
        };

        this.textPr = {
            property: null,
            startPos: null,
            endPos: null,
            userId: '',
            userName: '',
            dataTime: null,
        };
    }

    public flushAddRemoveChange(): void {
        const addRemove = this.addRemove;

        if ( ReviewType.Add === addRemove.changeType || ReviewType.Remove === addRemove.changeType ) {
            const revision = new Revision(addRemove.userName, addRemove.userId);
            revision.setElement(this.paragraph);
            revision.setType((ReviewType.Add === addRemove.changeType ?
                        RevisionChangeType.TextAdd : RevisionChangeType.TextRemove));
            revision.setValue(addRemove.value);
            revision.setStartPos(addRemove.startPos);
            revision.setEndPos(addRemove.endPos);
            revision.setTime(addRemove.dataTime);

            this.revisionsManager.addRevision(this.paraId, revision);
        }

        addRemove.changeType = null;
        addRemove.startPos = null;
        addRemove.startPos = null;
        addRemove.value = [];
        addRemove.userId = '';
        addRemove.userName = '';
        addRemove.dataTime = new Date();
    }

    public flushTextPrChange(): void {
        const textPr = this.textPr;

        if ( null != textPr.property ) {
            const revision = new Revision(textPr.userName, textPr.userId);
            revision.setElement(this.paragraph);
            revision.setType(RevisionChangeType.TextPr);
            revision.setValue(textPr.property);
            revision.setStartPos(textPr.startPos);
            revision.setEndPos(textPr.endPos);
            revision.setTime(textPr.dataTime);

            this.revisionsManager.addRevision(this.paraId, revision);
        }

        textPr.property = null;
        textPr.startPos = null;
        textPr.startPos = null;
        textPr.userId = '';
        textPr.userName = '';
        textPr.dataTime = new Date();
    }

    public addText(text: string): void {
        if ( null == text || '' === text ) {
            return ;
        }

        const value = this.addRemove.value;
        const length = value.length;

        if ( 0 >= length ) {
            value.push('' + text);
        } else {
            value[length - 1] += text;
        }
    }

    public addDrawing(item: any): void {
        if ( item ) {
            const type = item.type;

            switch (type) {
                case ParaElementType.ParaMedEquation:
                    this.addRemove.value.push(RevisionObjectType.MedEquation);
                    break;

                case ParaElementType.ParaDrawing:
                    this.addRemove.value.push(RevisionObjectType.Drawing);
                    break;
            }
        }
    }

    public startAddRemove(reviewType: ReviewType, contentPos: ParagraphContentPos): void {
        const addRemove = this.addRemove;
        addRemove.changeType = reviewType;
        addRemove.startPos = contentPos.copy();
        addRemove.endPos = contentPos.copy();
        addRemove.value = [];
    }

    public setAddRemoveEndPos(contentPos: ParagraphContentPos): void {
        this.addRemove.endPos = contentPos.copy();
    }

    public updateAddRemoveReviewInfo(reviewInfo: ReviewInfo): void {
        if ( reviewInfo && reviewInfo.getTime().getTime() >= this.addRemove.dataTime.getTime() ) {
            this.addRemove.userId = reviewInfo.getUserId();
            this.addRemove.userName = reviewInfo.getUserName();
            this.addRemove.dataTime = reviewInfo.getTime();
        }
    }

    public haveTextPrChange(): boolean {
        return ( null == this.textPr.property ? false : true );
    }

    public startTextPrChange(pr: TextProperty, contentPos: ParagraphContentPos): void {
        const textPr = this.textPr;
        textPr.property = pr.copy();
        textPr.startPos = contentPos.copy();
        textPr.endPos = contentPos.copy();
    }

    public setTextPrChangeEndPos(contentPos: ParagraphContentPos): void {
        this.textPr.endPos = contentPos.copy();
    }

    public updateTextPrChangeReviewInfo(reviewInfo: ReviewInfo): void {
        if ( reviewInfo && reviewInfo.getTime() >= this.textPr.dataTime ) {
            this.textPr.userId = reviewInfo.getUserId();
            this.textPr.userName = reviewInfo.getUserName();
            this.textPr.dataTime = reviewInfo.getTime();
        }
    }

    public comparePrChange(prChange: TextProperty): boolean {
        if ( null == this.textPr.property ) {
            return false;
        }

        return this.textPr.property.isEqual(prChange);
    }

    public getReviewType(): ReviewType {
        return this.addRemove.changeType;
    }

    public getReviewUserId(): string {
        return this.addRemove.userId;
    }
}
