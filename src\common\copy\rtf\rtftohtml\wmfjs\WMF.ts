import { WMFRecords } from './WMFRecords';
import { WMFRect16 } from './Renderer';
import { Blob } from './Blob';
import { Helper } from './Helper';
import { GDIContext } from './GDIContext';

export class WMFPlacable {
    private boundingBox: WMFRect16;
    private unitsPerInch: number;

    constructor(reader: Blob) {
        reader.skip(2);
        this.boundingBox = new WMFRect16(reader);
        this.unitsPerInch = reader.readInt16();
        reader.skip(4);
        reader.skip(2); // TODO: checksum
        Helper.log('Got bounding box ' + this.boundingBox + ' and ' + this.unitsPerInch + ' units/inch');
    }
}

export class WMF {
    // tslint:disable-next-line: variable-name
    private _version: number;
    // tslint:disable-next-line: variable-name
    private _hdrsize: number;
    // tslint:disable-next-line: variable-name
    private _placable: WMFPlacable;
    // tslint:disable-next-line: variable-name
    private _records: WMFRecords;

    constructor(reader: Blob, placable: WMFPlacable, version: number, hdrsize: number) {
        this._version = version;
        this._hdrsize = hdrsize;
        this._placable = placable;
        this._records = new WMFRecords(reader, this._hdrsize);
    }

    public render(gdi: GDIContext): void {
        this._records.play(gdi);
    }
}
