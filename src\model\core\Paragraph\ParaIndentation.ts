/**
 * 段落缩进
 */
export default class ParaIndentation {
    public left: number;  // 左缩进
    public right?: number;  // 右缩进
    public firstLine: number; // 首行缩进

    constructor() {
        this.left = 0;
        this.right = 0;
        this.firstLine = 0;
    }

    /**
     * 是否设置了段落缩进
     */
    public isEmpty(): boolean {
        if ( 0 !== this.left || 0 !== this.right || 0 !== this.firstLine ) {
            return false;
        }

        return true;
    }

    public copy(): ParaIndentation {
        const newInd = new ParaIndentation();
        newInd.left = this.left;
        newInd.right = this.right;
        newInd.firstLine = this.firstLine;

        return newInd;
    }

}
