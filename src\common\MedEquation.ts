import { EquationType, getEditorDomContainer } from './commonDefines';
const rulePng = require('./resources/rule.png');

const domObj = {};
export class EquationRefs {
    public getEquationByType(type: EquationType, options?: {useDate?: boolean, initialDateEnabled?: boolean, lastDateEnabled?: boolean}): SVGElement {
        const dom = domObj[type];
        if (!dom) {
            return this.getEquation(type, options);
        }

        return dom;
    }

    public getEquationSizeByType(type?: EquationType, options?: {useDate?: boolean, initialDateEnabled?: boolean, lastDateEnabled?: boolean}): {height: number, width: number} {
        
        let width: number;
        let height: number = 50;
        switch (type) {
            case EquationType.Menstruation4: {
                width = 101.77;
                height = 54;
                break;
            }
            case EquationType.Menstruation3: {
                width = 86;
                height = 76;
                break;
            }
            case EquationType.Menstruation2: {
                width = 106;
                height = 60;
                break;
            }
            case EquationType.Menstruation: {
                // 如果是月经公式并且提供了选项，检查是否使用日期
                let usesDate = false;
                if (options) {
                    usesDate = options.useDate || !!options.initialDateEnabled || !!options.lastDateEnabled;
                }
                
                // 如果使用日期，宽度扩大50%
                const baseWidth = 106;
                const widthMultiplier = usesDate ? 1.5 : 1; // 增加到 1.5 倍
                width = usesDate ? Math.round(baseWidth * widthMultiplier) : baseWidth;
                break;
            }
            case EquationType.ToothBitMap: {
                width = 87;
                height = 50;
                break;
            }
            case EquationType.Rule: {
                width = 300;
                height = 50;
                break;
            }
            case EquationType.PupilMapping:
            case EquationType.LightPositioningMap: {
                width = 66;
                height = 60;
                break;
            }
            // case EquationType.PupilMapping: {
            //     width = 87;
            //     height = 60;
            //     break;
            // }
            case EquationType.FetalHeartChart: {
                width = 50;
                height = 60;
                break;
            }
            case EquationType.DiseasedUpperTeeth: {
                width = 45;
                break;
            }
            case EquationType.DiseasedLowerTeeth: {
                width = 80;
                height = 40;
                break;
            }
            case EquationType.DeciduousToothBitmap:
            case EquationType.PermanentToothBitmap: {
                width = 120;
                height = 60;
                break;
            }
            default:
                width = 50;
        }

        return {width, height};
    }

    public getEquationStringByType(type: EquationType, options?: {useDate?: boolean, initialDateEnabled?: boolean, lastDateEnabled?: boolean}): string {
        let str: string;
        switch (type) {
            case EquationType.Fraction: {
                str = this.getFraEquation();
                break;
            }
            case EquationType.Menstruation: {
                str = this.getMenEquation(undefined, options);
                break;
            }
            case EquationType.Menstruation2: {
                str = this.getMenstruation2(type);
                break;
            }
            case EquationType.Menstruation3: {
                str = this.getMenstruation3(type);
                break;
            }
            case EquationType.Menstruation4: {
                str = this.getMenstruation4(type);
                break;
            }
            case EquationType.Ordinary: {
                str = this.getOrdEquation();
                break;
            }
            case EquationType.ToothBitMap: {
                str = this.getToothBitMap(type);
                break;
            }
            case EquationType.PupilMapping: {
                str = this.getPupilMapping(type);
                break;
            }
            case EquationType.FetalHeartChart: {
                str = this.getFetalHeartChart(type);
                break;
            }
            case EquationType.LightPositioningMap: {
                str = this.getLightPositioningMap(type);
                break;
            }
            case EquationType.Rule: {
                str = this.getRule(type);
                break;
            }
            case EquationType.DiseasedUpperTeeth: {
                str = this.getDiseasedUpperTeeth(type);
                break;
            }
            case EquationType.DiseasedLowerTeeth: {
                str = this.getDiseasedLowerTeeth(type);
                break;
            }
            case EquationType.PermanentToothBitmap: {
                str = this.getPermanentToothBitmap(type);
                break;
            }
            case EquationType.DeciduousToothBitmap: {
                str = this.getDeciduousToothBitmap(type);
                break;
            }
            default:
                return;
        }

        return str;
    }

    // 光定位图
    private getLightPositioningMap(type: EquationType): string {
        const size = this.getEquationSizeByType(type);
        return `
        <svg
            class="medical-equation-wrapper"
            width="${size.width}"
            height="${size.height}"
            xmlns="http://www.w3.org/2000/svg"
        >
            <g>
                <rect width="100%" height="100%" fill="white"/>
            </g>
            <g>
                <text x="10" y="14" font-size="14" font-family="Arial" fill="#000">1</text>
                <text x="29" y="14" font-size="14" font-family="Arial" fill="#000">1</text>
                <text x="48" y="14" font-size="14" font-family="Arial" fill="#000">1</text>
                <text x="10" y="34" font-size="14" font-family="Arial" fill="#000">1</text>
                <text x="29" y="34" font-size="14" font-family="Arial" fill="#000">1</text>
                <text x="48" y="34" font-size="14" font-family="Arial" fill="#000">1</text>
                <text x="10" y="55" font-size="14" font-family="Arial" fill="#000">1</text>
                <text x="29" y="55" font-size="14" font-family="Arial" fill="#000">1</text>
                <text x="48" y="55" font-size="14" font-family="Arial" fill="#000">1</text>
            </g>
        </svg>
        `;
    }

    private getMenstruation4(type: EquationType): any {
        const size = this.getEquationSizeByType(type);
        return `
        <svg
            class="medical-equation-wrapper"
            width="${size.width}"
            height="${size.height}"
            xmlns="http://www.w3.org/2000/svg"
        >
            <rect width="100%" height="100%" fill="white"/>
            <g>
                <text font-size="14" stroke-width="0" id="svg_9" y="33.24875" x="3.24867" stroke="#000" fill="#000000">1</text>
                <path d="m38.74866,20.74875l-6.00002,16.25002" stroke="#000" fill="none"/>
                <text font-size="14" stroke-width="0" y="17.74877" x="70.99859" stroke="#000" fill="#000000">1</text>
                <line y2="28.24876" x2="105.01318" y1="28.24876" x1="51.49863" stroke="#000" fill="none"/>
                <text font-size="14" stroke-width="0" y="52.74874" x="71.24859" stroke="#000" fill="#000000">1</text>
            </g>
        </svg>
        `;
    }

    private getMenstruation3(type: EquationType): any {
        const size = this.getEquationSizeByType(type);
        return `
        <svg
            class="medical-equation-wrapper"
            width="${size.width}"
            height="${size.height}"
            xmlns="http://www.w3.org/2000/svg"
        >
            <rect width="100%" height="100%" fill="white"/>
            <g>
                <line stroke="#000" y2="70" x2="0" y1="0" x1="82" fill="none"/>
                <line stroke="#000" y2="70.5" x2="81.5" y1="1" x1="0" fill="none"/>
                <text font-size="14" stroke-width="0" y="13" x="34" stroke="#000" fill="#000000">1</text>
                <text font-size="14" stroke-width="0" y="43.5" x="70" stroke="#000" fill="#000000">1</text>
                <text font-size="14" stroke-width="0" y="70.5" x="34" stroke="#000" fill="#000000">1</text>
                <text font-size="14" stroke-width="0" y="41" x="2" stroke="#000" fill="#000000">1</text>
                
            </g>
        </svg>
        `;
    }

    private getMenstruation2(type?: EquationType): string {
        const size = this.getEquationSizeByType(type);
        return `
        <svg
            class="medical-equation-wrapper"
            width="${size.width}"
            height="${size.height}"
            viewBox="150 50 150 300"
            xmlns="http://www.w3.org/2000/svg"
        >
            <rect width="100%" height="100%" fill="white"/>
            <g class="medical-equation">
                <line x1="0" y1="200" x2="400" y2="200" class="liner" style="stroke: black; stroke-width: 5;"/>
                <line x1="190" y1="90" x2="190" y2="300" class="liner" style="stroke: black; stroke-width: 5;"/>
                <text x="75" y="160" text-anchor="middle" style="font-size: 80px;">
                    <tspan class="equation-text">1</tspan>
                </text>
                <text x="325" y="160" text-anchor="middle" style="font-size: 80px;">
                    <tspan class="equation-text">1</tspan>
                </text>
                <text x="75" y="290" text-anchor="middle" style="font-size: 80px;">
                    <tspan class="equation-text">1</tspan>
                </text>
                <text x="325" y="290" text-anchor="middle" style="font-size: 80px;">
                    <tspan class="equation-text">1</tspan>
                </text>
            </g>
        </svg>
        `;
    }

    private getRule(type: EquationType): string {
        const size = this.getEquationSizeByType(type);
        return `
        <svg
            class="medical-equation-wrapper"
            width="${size.width}"
            height="${size.height}"
            xmlns="http://www.w3.org/2000/svg"
        >
            <rect width="100%" height="100%" fill="white"/>
            <g>
                <image href="${rulePng.default}" width="100%" height="100%"/>
                <polygon points="5,16 0,24 10,24" style="fill:#000;transform: translate(-3.4px, 0px);stroke:#000;stroke-width:1;"/>
                <text>0</text>
            </g>
        </svg>
        `;
    }

    // 瞳孔图
    private getPupilMapping(type: EquationType): string {
        const size = this.getEquationSizeByType(type);
        return `
        <svg
            class="medical-equation-wrapper"
            width="${size.width}"
            height="${size.height}"
            xmlns="http://www.w3.org/2000/svg"
        >
            <g>
                <rect width="100%" height="100%" fill="white"/>
            </g>
            <g>
                <text x="10" y="14" font-size="14" font-family="Arial" fill="#000">1</text>
                <text x="48" y="14" font-size="14" font-family="Arial" fill="#000">1</text>
                <text x="10" y="34" font-size="14" font-family="Arial" fill="#000">1</text>
                <text x="29" y="34" font-size="14" font-family="Arial" fill="#000">1</text>
                <text x="48" y="34" font-size="14" font-family="Arial" fill="#000">1</text>
                <text x="10" y="55" font-size="14" font-family="Arial" fill="#000">1</text>
                <text x="48" y="55" font-size="14" font-family="Arial" fill="#000">1</text>
            </g>
        </svg>
        `;
    }

    // 胎心图
    private getFetalHeartChart(type: EquationType): string {
        const size = this.getEquationSizeByType(type);
        return `
        <svg
            class="medical-equation-wrapper"
            width="${size.width}"
            height="${size.height}"
            xmlns="http://www.w3.org/2000/svg"
        >
            <g>
                <rect width="100%" height="100%" fill="white"/>
            </g>
            <g>
                <line x1="3" x2="7" y1="30" y2="30" stroke="#000" stroke-width="0.5"></line>
                <line x1="22" x2="31" y1="30" y2="30" stroke="#000" stroke-width="0.5"></line>
                <line x1="26" x2="26" y1="0" y2="60" stroke="#000" stroke-width="0.5"></line>
                <line x1="44" x2="48" y1="30" y2="30" stroke="#000" stroke-width="0.5"></line>
                <text x="10" y="14" font-size="14" font-family="Arial" fill="#000">1</text>
                <text x="33" y="14" font-size="14" font-family="Arial" fill="#000">1</text>
                <text x="10" y="34" font-size="14" font-family="Arial" fill="#000">1</text>
                <text x="33" y="34" font-size="14" font-family="Arial" fill="#000">1</text>
                <text x="10" y="55" font-size="14" font-family="Arial" fill="#000">1</text>
                <text x="33" y="55" font-size="14" font-family="Arial" fill="#000">1</text>
            </g>
        </svg>
        `;
    }

    // PD牙位图
    private getToothBitMap(type: EquationType): string {
        const size = this.getEquationSizeByType(type);
        return `
        <svg
            class="medical-equation-wrapper"
            width="${size.width}"
            height="${size.height}"
            xmlns="http://www.w3.org/2000/svg"
        >
            <g>
                <rect width="100%" height="100%" fill="white"/>
            </g>
            <g>
                <line x1="0" x2="87" y1="27.786461" y2="27.786461" stroke="#000" stroke-width="0.5"/>
                <line x1="29" x2="29" y1="0" y2="50" stroke="#000" stroke-width="0.5"/>
                <line x1="58" x2="58" y1="0" y2="50" stroke="#000" stroke-width="0.5"/>
                <text x="10"  y="19.632474" font-size="14" font-family="Arial" fill="#000">1</text>
                <text x="39"  y="19.632474" font-size="14" font-family="Arial" fill="#000">1</text>
                <text x="68"  y="19.632474" font-size="14" font-family="Arial" fill="#000">1</text>
                <text x="10"  y="44.632475" font-size="14" font-family="Arial" fill="#000">1</text>
                <text x="39"  y="44.632475" font-size="14" font-family="Arial" fill="#000">1</text>
                <text x="68" y="44.632475" font-size="14" font-family="Arial" fill="#000">1</text>
            </g>
        </svg>
        `;
    }

    private getEquation(type: EquationType, options?: {useDate?: boolean, initialDateEnabled?: boolean, lastDateEnabled?: boolean}): any {
 
        let str: string;
        
        // 如果是月经公式并且提供了选项，直接调用getMenEquation
        if (type === EquationType.Menstruation && options) {
            str = this.getMenEquation(undefined, options);
        } else {
            str = this.getEquationStringByType(type);
        }
        
        if (!str) {
            return;
        }

        let dom = window['editor-equation-container'];
        if (!dom) {
            dom = document.createElement('div');
            dom.id = 'editor-equation-container';
            getEditorDomContainer()
            .appendChild(dom);
        }

        const div = document.createElement('div');
        div.innerHTML = str.trim();
        const svg = div.firstChild;
        dom.appendChild(svg);
        domObj[type] = svg;
        return svg;
    }

    private getOrdEquation(type?: EquationType): string {
        const size = this.getEquationSizeByType(type);
        return `
        <svg
            class="medical-equation-wrapper"
            width="${size.width}"
            height="${size.height}"
            viewBox="0 0 300 300"
            xmlns="http://www.w3.org/2000/svg"
        >
            <rect width="100%" height="100%" fill="white"/>
            <g class="medical-equation">
                <line x1="0" y1="200" x2="300" y2="200" class="liner" style="stroke: black; stroke-width: 5;"/>
                <line
                    x1="150"
                    y1="90"
                    x2="150"
                    y2="300"
                    class="liner"
                    style="stroke: black; stroke-width: 5;"
                />
                <text x="75" y="160" text-anchor="middle" style="font-size: 80px;">
                    <tspan class="equation-text">1</tspan>
                </text>
                <text x="225" y="160" text-anchor="middle" style="font-size: 80px;">
                    <tspan class="equation-text">1</tspan>
                </text>
                <text x="75" y="290" text-anchor="middle" style="font-size: 80px;">
                    <tspan class="equation-text">1</tspan>
                </text>
                <text x="225" y="290" text-anchor="middle" style="font-size: 80px;">
                    <tspan class="equation-text">1</tspan>
                </text>
            </g>
        </svg>
        `;
    }

    private getFraEquation(): string {
        const size = this.getEquationSizeByType();
        return `
        <svg
            class="medical-equation-wrapper"
            width="${size.width}"
            height="${size.height}"
            viewBox="0 0 300 300"
            xmlns="http://www.w3.org/2000/svg"
        >
            <rect width="100%" height="100%" fill="white"/>
            <g class="medical-equation">
                <line x1="0" y1="200" x2="300" y2="200" class="liner" style="stroke: black; stroke-width: 5"/>
                <text x="150" y="160" text-anchor="middle" style="font-size: 80px;">
                    <tspan class="equation-text">1</tspan>
                </text>
                <text x="150" y="290" text-anchor="middle" style="font-size: 80px;">
                    <tspan class="equation-text">1</tspan>
                </text>
            </g>
        </svg>
        `;
    }

    private getMenEquation(svgElement?: SVGElement, options?: {useDate?: boolean, initialDateEnabled?: boolean, lastDateEnabled?: boolean}): string {
        // 检查是否使用日期
        let usesDate = false;
        let initialDateEnabled = false;
        let lastDateEnabled = false;
        
        
        
        // 优先使用传入的选项参数
        if (options) {
            initialDateEnabled = !!options.initialDateEnabled;
            lastDateEnabled = !!options.lastDateEnabled;
            usesDate = options.useDate || initialDateEnabled || lastDateEnabled;        
        }
        // 如果没有传入选项参数，则从SVG元素中获取
        else if (svgElement) {
            try {
                const hiddenValues = svgElement.querySelector('.hidden-values');
                console.log('[getMenEquation] 从SVG元素中获取hidden-values:', hiddenValues);
                if (hiddenValues) {
                    initialDateEnabled = hiddenValues.classList.contains('initialDateEnabled');
                    lastDateEnabled = hiddenValues.classList.contains('lastDateEnabled');
                    usesDate = initialDateEnabled || lastDateEnabled;
                   ;
                }
            } catch (e) {
                
            }
        }
        
        // 计算尺寸
        const baseWidth = 106; // 默认宽度
        const height = 50; // 默认高度
        
       
        
        // 强制设置 usesDate，确保它正确反映日期选项状态
        usesDate = initialDateEnabled || lastDateEnabled;
        
        // 如果使用日期，宽度扩大50%
        const widthMultiplier = usesDate ? 1.5 : 1; // 增加到 1.5 倍
        const width = Math.round(baseWidth * widthMultiplier);
       
        // 字体大小，如果使用日期则缩小
        const fontSize = usesDate ? 40 : 80;
        
       
        
        // 构建 hidden-values 元素的类名，确保正确反映日期选项状态
        let hiddenValuesClass = "hidden-values";
        if (initialDateEnabled) {
            hiddenValuesClass += " initialDateEnabled";
        }
        if (lastDateEnabled) {
            hiddenValuesClass += " lastDateEnabled";
        }
        
       
        // 返回SVG字符串，确保宽度和字体大小正确应用
        return `
        <svg
            class="medical-equation-wrapper"
            width="${width}"
            height="${height}"
            viewBox="0 0 636 300"
            xmlns="http://www.w3.org/2000/svg"
            data-uses-date="${usesDate}"
            data-font-size="${fontSize}"
            data-width="${width}"
        >
            <rect width="100%" height="100%" fill="white"/>
            <g class="medical-equation">
                <g class="${hiddenValuesClass}" />
                <text x="192" y="220" text-anchor="end" style="font-size: ${fontSize}px;">
                    <tspan class="equation-text">15岁</tspan>
                </text>

                <line x1="212" y1="200" x2="408" y2="200" class="liner" style="stroke: black; stroke-width: 5px;"/>
                <text x="310" y="160" text-anchor="middle" style="font-size: ${fontSize}px;">
                    <tspan class="equation-text">3天</tspan>
                </text>
                <text x="310" y="290" text-anchor="middle" style="font-size: ${fontSize}px;">
                    <tspan class="equation-text">30天</tspan>
                </text>

                <text x="428" y="220" text-anchor="start" style="font-size: ${fontSize}px;">
                    <tspan class="equation-text">50岁</tspan>
                </text>
            </g>
        </svg>
        `;
    }

    // 光定位图
    private getDiseasedUpperTeeth(type: EquationType): string {
        const size = this.getEquationSizeByType(type);
        return `<svg class="medical-equation-wrapper"
                    width="${size.width}"
                    height="${size.height}"
                    viewBox="0 0 190 210"
                    xmlns="http://www.w3.org/2000/svg"
                    >
                <g>
                    <rect width="100%" height="100%" fill="white"/>
                </g>
                <g>
                <line stroke-width="3" y2="96.39165" x2="96.39165" y1="3" x1="3" stroke="#000" fill="none"/>
                <line stroke="#000" stroke-width="3" y2="2.49859" x2="189.50141" y1="95" x1="97" fill="none"/>
                <line stroke="#000" stroke-width="3" y2="209.01709" x2="96" y1="95" x1="96" fill="none"/>
                <text stroke="#000" font-size="60px" stroke-width="0" id="svg_1" y="45" x="78" fill="#000000">1</text>
                <text stroke="#000" font-size="60px" stroke-width="0" id="svg_2" y="140" x="36" fill="#000000">2</text>
                <text stroke="#000" font-size="60px" stroke-width="0" id="svg_3" y="140" x="125" fill="#000000">3</text>
                </g>
            </svg>
        `;
    }

    private getDiseasedLowerTeeth(type: EquationType): string {
        const size = this.getEquationSizeByType(type);
        return `
            <svg
                class="medical-equation-wrapper"
                width="${size.width}"
                height="${size.height}"
                xmlns="http://www.w3.org/2000/svg"
            >
                <g>
                    <rect width="100%" height="100%" fill="white"/>
                </g>
                <g>
                <line id="line1" y2="20" x2="80" y1="20" x1="30" stroke="#000" fill="none"/>
                <text stroke="#000" font-size="14px" font-family="Arial" stroke-width="0" id="value1" y="25" x="5" fill="#000000">FI</text>
                <text stroke="#000" font-size="14px" font-family="Arial" stroke-width="0" id="value2" y="12" x="40" fill="#000000">123</text>
                <text stroke="#000" font-size="14px" font-family="Arial" stroke-width="0" id="value3" y="38" x="40" fill="#000000">123</text>
                </g>
            </svg>
        `;
    }

    private getPermanentToothBitmap(type: EquationType): string {
        return `<svg data-source="{'controlTags':['8','7','6','5','4','3','2','1'],'titles':['第三磨牙','第二磨牙','第一磨牙','第二前磨牙','第一前磨牙','尖牙','侧切牙','中切牙'],'mandible':{'isMandible':true,'flags':{'left':[[],[],[],[],[],[],[],[],[],[],[],[],[],[],[],[]],'right':[[],[],[],[],[],[],[],[],[],[],[],[],[],[],[],[]]}},'maxilla':{'isMandible':false,'flags':{'left':[[],[],[],[],[],[],[],[],[],[],[],[],[],[],[],[]],'right':[[],[],[],[],[],[],[],[],[],[],[],[],[],[],[],[]]}},'control':{'mandible':{'left':[[],[],[],[],[],[],[],[]],'right':[[],[],[],[],[],[],[],[]],'leftActive':[],'rightActive':[]},'maxilla':{'left':[[],[],[],[],[],[],[],[]],'right':[[],[],[],[],[],[],[],[]],'leftActive':[],'rightActive':[]}},'tags':['P','L','B','D','O','M']}" width="111" height="63" xmlns="http://www.w3.org/2000/svg/" version="1.1"><g transform="translate(10, 10)"><g transform="translate(20.00, 0)"><text x="" y="" 
        font-family="宋体" font-size="16" font-style="normal">
        
    </text><text x="" y="" 
        font-family="宋体" font-size="8" font-style="italic">
        
    </text></g><g transform="translate(71.00, 0)"><text x="" y="" 
        font-family="宋体" font-size="16" font-style="normal">
        
    </text><text x="" y="" 
        font-family="宋体" font-size="8" font-style="italic">
        
    </text></g><g transform="translate(20.00, 24.5)"><text x="" y="" 
        font-family="宋体" font-size="16" font-style="normal">
        
    </text><text x="" y="" 
        font-family="宋体" font-size="8" font-style="italic">
        
    </text></g><g transform="translate(71.00, 24.5)"><text x="" y="" 
        font-family="宋体" font-size="16" font-style="normal">
        
    </text><text x="" y="" 
        font-family="宋体" font-size="8" font-style="italic">
        
    </text></g><g style="stroke: black; stroke-width: 1;">
            <line x1="0" y1="22" x2="91" y2="22" />
            <line x1="46" y1="0" x2="46" y2="43" />
        </g>
        </g></svg>`;
    }

    private getDeciduousToothBitmap(type: EquationType) {
        return `<svg data-source="{'controlTags':['Ⅴ','Ⅳ','Ⅲ','Ⅱ','Ⅰ'],'titles':['第二乳磨牙','第一乳磨牙','乳尖牙','乳侧尖牙','乳中切牙'],'mandible':{'isMandible':true,'flags':{'left':[[],[],[],[],[],[],[],[],[],[]],'right':[[],[],[],[],[],[],[],[],[],[]]}},'maxilla':{'isMandible':false,'flags':{'left':[[],[],[],[],[],[],[],[],[],[]],'right':[[],[],[],[],[],[],[],[],[],[]]}},'control':{'mandible':{'left':[[],[],[],[],[]],'right':[[],[],[],[],[]],'leftActive':[],'rightActive':[]},'maxilla':{'left':[[],[],[],[],[]],'right':[[],[],[],[],[]],'leftActive':[],'rightActive':[]}},'tags':['P','L','B','D','O','M']}" width="111" height="63" xmlns="http://www.w3.org/2000/svg/" version="1.1"><g transform="translate(10, 10)"><g transform="translate(20.00, 0)"><text x="" y="" 
        font-family="宋体" font-size="16" font-style="normal">
        
    </text><text x="" y="" 
        font-family="宋体" font-size="8" font-style="italic">
        
    </text></g><g transform="translate(71.00, 0)"><text x="" y="" 
        font-family="宋体" font-size="16" font-style="normal">
        
    </text><text x="" y="" 
        font-family="宋体" font-size="8" font-style="italic">
        
    </text></g><g transform="translate(20.00, 24.5)"><text x="" y="" 
        font-family="宋体" font-size="16" font-style="normal">
        
    </text><text x="" y="" 
        font-family="宋体" font-size="8" font-style="italic">
        
    </text></g><g transform="translate(71.00, 24.5)"><text x="" y="" 
        font-family="宋体" font-size="16" font-style="normal">
        
    </text><text x="" y="" 
        font-family="宋体" font-size="8" font-style="italic">
        
    </text></g><g style="stroke: black; stroke-width: 1;">
            <line x1="0" y1="22" x2="91" y2="22" />
            <line x1="46" y1="0" x2="46" y2="43" />
        </g>
        </g></svg>`;
    }
}
