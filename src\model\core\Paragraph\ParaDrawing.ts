import { ParaElementType } from './ParagraphContent';
import { ParaElementBase, RepalceText } from './ParaElementBase';
import DocumentContentBase from '../DocumentContentBase';
import { audioIconStr, DocumentSectionType, EquationType, IExternalDataProperty, ImageMediaType, ResultType, VertAlignType, videoIconStr } from '../../../common/commonDefines';
import { idCounterImage } from '../util';
import ParaPortion from './ParaPortion';
import { ChangeGraphicObjectName, ChangeGraphicObjectSrc, ChangeGraphicObjectWidth,
  ChangeGraphicObjectHeight, ChangeGraphicObjectSizeLocked, ChangeGraphicObjectCopyProtect,
  ChangeGraphicObjectDeleteLocked, ChangeGraphicObjectPreserveAspectRatio, ChangeGraphicObjectSvgElem,
 } from './ParaDrawingChange';
import { GraphicObjects } from '../GraphicObjects/GraphicObjects';
import { DocumentContent } from '../DocumentContent';
import History from '../History';
import { genarateBarcode, genarateQRCode } from '@/common/commonMethods';

export interface IParaDrawingProp {
  name?: string;
  sizeLocked?: boolean;
  deleteLocked?: boolean;
  preserveAspectRatio?: boolean;
  copyProtect?: boolean;
  width?: number;
  height?: number;
  type?: number;
  src?: string;
  equationElem?: string;
  equationType?: EquationType;
  mediaSrc?: string;
  mediaType?: ImageMediaType;

  // barcode
  content?: string;
  bUse?: boolean;
  textAlign?: string;

  // qrcode
  errorCL?: string;

  externalDataBind?: IExternalDataProperty;
  vertAlign?: VertAlignType;
}

export default class ParaDrawing extends ParaElementBase {
  public id: number;
  public height: number;
  public src: string;
  public name: string;
  public sizeLocked: boolean;
  public deleteLocked: boolean;
  public preserveAspectRatio: boolean;
  public copyProtect: boolean;
  public pageIndex: number;
  public paraId: number;
  public tableId: number;
  public preload: boolean;
  public portion: ParaPortion;
  public imageRatio: number;
  public graphicObjects: GraphicObjects;
  public vertAlign: VertAlignType;
  // private _timeout: any;

  constructor(document: DocumentContentBase = null, width: number = 0, height: number = 0,
              src: string = null, isFromCopy: boolean = false,
              dName: string = null, imageRatio: number = 0,
              align: number = VertAlignType.Bottom) { // dName: drawing name exists
    super();
    // id is new every time new paradrawing is CREATED
    this.id = idCounterImage.getNewId();
    this.content = RepalceText.Space;
    this.type = ParaElementType.ParaDrawing;
    this.height = height;
    this.width = width;
    this.src = src;
    this.sizeLocked = false;
    this.deleteLocked = false;
    this.name = '图片' + this.id;
    // this.name = '图片' + idCounterImage.getNewId();
    this.preserveAspectRatio = true;
    this.pageIndex = -1;
    this.paraId = -1;
    this.tableId = -1;
    this.portion = null;
    this.preload = false;
    this.imageRatio = (imageRatio === 0) ? width / height : imageRatio;
    this.vertAlign = align;

    if (document) {
      const drawingObjects = document.getDrawingObjects();
      if (drawingObjects) {
        if (!drawingObjects.isParaEquation(this)) {
          if (!dName) {
            this.name = drawingObjects.makeUniqueImageName(ParaElementType.ParaDrawing);
          } else {
            this.name = drawingObjects.makeUniqueImageName(ParaElementType.ParaDrawing, dName);
          }

          drawingObjects.addGraphicObject(this);
        }

        this.graphicObjects = drawingObjects;
      }

      // let images = DocumentContentBase.drawingObjects.getGraphicObject();
      // if (images.length === 0) {
      // 	this.name += 1;
      // } else {
      // 	let lastImage = images[images.length - 1];
      // 	this.name += lastImage.name.slice(lastImage.name.indexOf("片") + 1) * 1 + 1;
      // }

      // console.log(images.length);
    }
  }

  /** 是否是多媒体资源 */
  public isMediaImage(): Boolean {
      return false;
  }

  public getId(): number {
    return this.id;
  }

  public measure(): number {

    // just retrieve height, width from front
    this.widthVisible = this.width;
    // console.log(this.height);
    return this.height;
  }

  public getDocumentSectionType(): DocumentSectionType {
    if (!this.portion) {
      return DocumentSectionType.Document;
    }

    return this.portion.paragraph.parent.getDocumentSectionType();
  }

  public hasBind() {
    return false;
  }

  public getBind() {
    return null;
  }

  /**
   * 拷贝
   * @param bForUI：true UI显示时所需宽度等信息
   */
  public copy(bForUI: boolean = false, document: DocumentContentBase = null): ParaDrawing {
    const newDrawing = new ParaDrawing(document, 0, 0, null, true);

    newDrawing.positionX = this.positionX;
    newDrawing.positionY = this.positionY;

    if (true === bForUI) {
      newDrawing.id = this.id;
      newDrawing.name = this.name;
      newDrawing.content = RepalceText.SpaceForUI;
    }

    newDrawing.type = this.type;
    newDrawing.width = this.width;
    newDrawing.widthVisible = this.width;
    newDrawing.height = this.height;
    newDrawing.src = this.src;
    newDrawing.sizeLocked = this.sizeLocked;
    newDrawing.deleteLocked = this.deleteLocked;
    newDrawing.preserveAspectRatio = this.preserveAspectRatio;
    newDrawing.pageIndex = this.pageIndex;
    newDrawing.preload = this.preload;
    newDrawing.imageRatio = this.imageRatio;
    newDrawing.vertAlign = this.vertAlign;
    newDrawing.copyProtect = this.copyProtect;

    return newDrawing;
  }

  public setName(name: string): boolean {
    if ( null == name || name === this.name ) {
      return false;
    }

    const history = this.getHistory();
    if ( history ) {
      history.addChange(new ChangeGraphicObjectName(this, this.name, name));
    }
    this.name = name;
    this.setDirty();
    return true;
  }

  public getDrawingName(): string {
    return this.name;
  }

  public setDirty(): void {
    if (this.portion == null) {
      return;
    }

    const para = this.portion.paragraph;
    if (!para) {
      return;
    }
    const doc = para.getDocument();
    doc.setRegionDirty(para);
  }

  public getRatio(): number {
    return this.imageRatio;
  }

  public selectTopPos(): void {
    const parent = this.portion;
    const doc = parent.paragraph.getDocument();
    if (doc) {
      doc.removeSelection();
    }
    const selection = parent.selection;
    selection.bUse = true;
    const index = parent.content.findIndex((item) => item === this);
    selection.startPos = index;
    parent.portionContentPos = selection.endPos = index + 1;
    parent.selectTopPos();
  }

  public setContentCurPos(): void {
    const parent = this.portion;
    const index = parent.content.findIndex((item) => item === this);
    parent.portionContentPos = index + 1;
    parent.setContentCurPos();
    // if (doc) {
    //   doc.removeSelection();
    // }
  }

  /**
   * Set absolute page number index
   */
  public setPageIndex(pageIndex: number): void {
    this.pageIndex = pageIndex;
  }

  public setPortion( portion: ParaPortion): void {
    this.portion = portion;
  }

  public setParaId(paraId: number): void {
    this.paraId = paraId;
  }

  public setTableId(tableId: number): void {
    this.tableId = tableId;
  }

  public setPreload(preload: boolean): void {
    this.preload = preload;
  }

  public setAdvancedProps(preserveAspectRatio?: boolean, sizeLocked?: boolean,
                          deleteLocked?: boolean, copyProtect?: boolean): void {
    let bChange = false;
    if (preserveAspectRatio !== null) {
      bChange = true;
      this.preserveAspectRatio = preserveAspectRatio;
    }
    if (sizeLocked !== null) {
      bChange = true;
      this.sizeLocked = sizeLocked;
    }
    if (deleteLocked !== null) {
      bChange = true;
      this.deleteLocked = deleteLocked;
    }
    if (copyProtect != null) {
      this.copyProtect = copyProtect;
    }
  }

  // updatePosition() {

  // }
  public setSrc(src: string): boolean {
    if ( null != src && src !== this.src ) {
      const history = this.getHistory();
      if ( history ) {
        history.addChange(new ChangeGraphicObjectSrc(this, this.src, src));
      }

      this.src = src;
      this.setDirty();
      return true;
    }

    return false;
  }

  public setWidth(width: number): boolean {
    if ( null != width && width !== this.width ) {
      const history = this.getHistory();
      if ( history ) {
        history.addChange(new ChangeGraphicObjectWidth(this, this.width, width));
      }

      this.width = width;
      this.widthVisible = width;
      this.setDirty();
      return true;
    }

    return false;
  }

  public setHeight(height: number): boolean {
    if ( null != height && height !== this.height ) {
      const history = this.getHistory();
      if ( history ) {
        history.addChange(new ChangeGraphicObjectHeight(this, this.height, height));
      }

      this.height = height;
      this.setDirty();
      return true;
    }

    return false;
  }

  // canChangeWrapPolygon() {

  // }

  public setSizeLocked(sizeLocked: boolean): boolean {
    if (sizeLocked != null && sizeLocked !== this.sizeLocked ) {
      const history = this.getHistory();
      if ( history ) {
        history.addChange(new ChangeGraphicObjectSizeLocked(this, this.sizeLocked, sizeLocked));
      }

      this.sizeLocked = sizeLocked;
      this.setDirty();
      return true;
    }

    return false;
  }

  public setCopyProtect(copyProtect: boolean): boolean {
    if (copyProtect != null && copyProtect !== this.copyProtect ) {
      const history = this.getHistory();
      if ( history ) {
        history.addChange(new ChangeGraphicObjectCopyProtect(this, this.copyProtect, copyProtect));
      }

      this.copyProtect = copyProtect;
      this.setDirty();
      return true;
    }

    return false;
  }

  public setDeleteLocked(deleteLocked: boolean): boolean {
    if (deleteLocked != null && deleteLocked !== this.deleteLocked ) {
      const history = this.getHistory();
      if ( history ) {
        history.addChange(new ChangeGraphicObjectDeleteLocked(this, this.deleteLocked, deleteLocked));
      }

      this.deleteLocked = deleteLocked;
      this.setDirty();
      return true;
    }

    return false;
  }

  public isDeleteLocked(): boolean {
    return this.deleteLocked;
  }

  public setPreserveAspectRatio(preserveAsRatio: boolean): boolean {
    if (preserveAsRatio != null && preserveAsRatio !== this.preserveAspectRatio ) {
      const history = this.getHistory();
      if ( history ) {
        history.addChange(new ChangeGraphicObjectPreserveAspectRatio(this, this.preserveAspectRatio, preserveAsRatio));
      }

      this.preserveAspectRatio = preserveAsRatio;
      this.setDirty();
      return true;
    }

    return false;
  }
  // setAllowOverlap() {

  // }

  public getAbsolutePageIndex(): number {
    if ( this.portion && this.portion.paragraph && this.portion.paragraph.parent ) {
      return this.portion.paragraph.parent.getAbsolutePage(this.pageIndex);
    }

    return -1;
  }

  public isImage(): boolean {
    return true;
  }

  public setDrawingProp(props: IParaDrawingProp): number {
    let result = false;

    result = this.setName(props.name) || result;
    result = this.setWidth(props.width) || result;
    result = this.setHeight(props.height) || result;

    result = this.setSrc(props.src) || result;
    result = this.setSizeLocked(props.sizeLocked) || result;
    result = this.setCopyProtect(props.copyProtect) || result;
    result = this.setDeleteLocked(props.deleteLocked) || result;
    result = this.setPreserveAspectRatio(props.preserveAspectRatio) || result;
    result = (ResultType.Success === this.setVertAlign(props.vertAlign)) || result;

    // if (props.sizeLocked !== undefined && props.sizeLocked !== this.sizeLocked) {
    //     this.setSizeLocked(props.sizeLocked);
    // }

    // if (props.copyProtect !== undefined && props.copyProtect !== this.copyProtect) {
    //     bChange = true;
    //     this.setCopyProtect(props.copyProtect);
    // }

    // if (props.deleteLocked !== undefined && props.deleteLocked !== this.deleteLocked) {
    //     bChange = true;
    //     this.setDeleteLocked(props.deleteLocked);
    // }

    // if (props.preserveAspectRatio !== undefined && props.preserveAspectRatio !== this.preserveAspectRatio) {
    //     bChange = true;
    //     this.setPreserveAspectRatio(props.preserveAspectRatio);
    // }

    return (true === result ? ResultType.Success : ResultType.UnEdited);
  }

  public getProps(): IParaDrawingProp {
    const drawingProps: IParaDrawingProp = {
      name: this.name,
      sizeLocked: this.sizeLocked,
      deleteLocked: this.deleteLocked,
      preserveAspectRatio: this.preserveAspectRatio,
      copyProtect: this.copyProtect,
      width: this.width,
      height: this.height,
      type: this.type,
      src: this.src,
    };

    return drawingProps;
  }

  public isReadOnly(): boolean {
    const parent = this.portion ? this.portion.paragraph.parent : null;
    if (parent && parent instanceof DocumentContent) {
      const table = parent.getTable();
      if ( table && table.isNewControlReadOnly() ) {
          return true;
      }

      const region = parent.getRegion();
      if (region) {
          return region.isReadOnly();
      }
    }

    return false;
  }

  public getHistory(): History {
    return this.graphicObjects ? this.graphicObjects.getHistory() : null;
  }

  public setVertAlign(vertAlign: VertAlignType): number {
    if (vertAlign === this.vertAlign || null == vertAlign) {
      return ResultType.UnEdited;
    }

    this.vertAlign = vertAlign;
    return ResultType.Success;
  }

  public getVertAlign(): number {
    return this.vertAlign;
  }

}

/**
 * 医学公式
 */
export class ParaEquation extends ParaDrawing {

  public equationType: EquationType;
  public equationElem: string; // original svg element

  constructor(document: DocumentContentBase, width: number, height: number, src: string,
              name: string, equationType: EquationType, svgElem: string,
              isFromCopy: boolean = false, align: number = VertAlignType.Bottom) {
    super(document, width, height, src, isFromCopy);

    // console.log(equationProperties);
    // overwrite existing props

    this.type = (EquationType.EditableSvg !== equationType ?
      ParaElementType.ParaMedEquation : ParaElementType.ParaDrawing);

    if (document) {
      const drawingObjects = document.getDrawingObjects();
      if (drawingObjects) {
        this.name = drawingObjects.makeUniqueImageName(this.type, name);
        drawingObjects.addGraphicObject(this);
      }
    }

    this.equationType = equationType;
    this.equationElem = svgElem;
    this.vertAlign = align;

    // show 8 polygons
    this.preserveAspectRatio = false;

    // editable drawing is always aspect-ratio-preserved
    if (EquationType.EditableSvg === equationType) {
      this.preserveAspectRatio = true;
    }

  }

  // public addGraphicObject(document: DocumentContentBase): void {
  //   const drawingObjects = document.getDrawingObjects();
  //   if (drawingObjects) {
  //     drawingObjects.addGraphicObject(this);
  //   }
  // }

  public copy(bForUI: boolean = false, document: DocumentContentBase = null): ParaEquation {
    const newEquation = new ParaEquation(document, 0, 0, null, null, this.equationType, null, true);

    newEquation.positionX = this.positionX;
    newEquation.positionY = this.positionY;

    if (true === bForUI) {
      newEquation.id = this.id;
      // when copy, id, name should not matter.
      // Only when paste should them be changed
      // but when render, id, name should be kept the same.
      newEquation.name = this.name;
      newEquation.content = RepalceText.SpaceForUI;
    }

    newEquation.type = this.type;
    newEquation.width = this.width;
    newEquation.widthVisible = this.width;
    newEquation.height = this.height;
    newEquation.src = this.src;
    newEquation.sizeLocked = this.sizeLocked;
    newEquation.deleteLocked = this.deleteLocked;
    newEquation.preserveAspectRatio = this.preserveAspectRatio;

    newEquation.pageIndex = this.pageIndex;
    newEquation.preload = this.preload;

    newEquation.equationType = this.equationType;
    newEquation.equationElem = this.equationElem;
    newEquation.copyProtect = this.copyProtect;
    newEquation.vertAlign = this.vertAlign;

    return newEquation;
  }

  public setSvgElemStr(svgElemStr: string): boolean {
    if (svgElemStr != null && svgElemStr !== this.equationElem ) {
      const history = this.getHistory();
      // history.addChange(new ChangeGraphicObjectSrc(this, this.src, src));
      if ( history ) {
        history.addChange(new ChangeGraphicObjectSvgElem(this, this.equationElem, svgElemStr));
      }

      // this.src = src;
      this.equationElem = svgElemStr;
      this.setDirty();
      return true;
    }

    return false;
  }

  public setDrawingProp(props: IParaDrawingProp): number {
    let res = super.setDrawingProp(props);
    if ( this.setSvgElemStr(props.equationElem) ) {
      res = ResultType.Success;
    }

    return res;
  }

  public getProps(): IParaDrawingProp {
    const drawingProps: IParaDrawingProp = {
      name: this.name,
      sizeLocked: this.sizeLocked,
      deleteLocked: this.deleteLocked,
      preserveAspectRatio: this.preserveAspectRatio,
      copyProtect: this.copyProtect,
      width: this.width,
      height: this.height,
      type: this.type,
      src: this.src,
      equationElem: this.equationElem,
      equationType: this.equationType,
    };

    return drawingProps;
  }

  public canEdit(): boolean {
    return EquationType.EditableSvg === this.equationType;
  }

  public getJsonSrc(): string {
    return this.equationElem;
  }

  public setJsonSrc(src: string): void {
    this.setSvgElemStr(src);
  }

  public isMedEquation(): boolean {
    return ParaElementType.ParaMedEquation === this.type;
  }

  public isSvgDrawing(): boolean {
    return EquationType.EditableSvg === this.equationType;
  }

  public isImage(): boolean {
    if (EquationType.EditableSvg === this.equationType) {
      return true;
    }

    return false;
  }
}


// tslint:disable-next-line: max-classes-per-file
export class ParaMediaDrawing extends ParaDrawing {
    public mediaType: ImageMediaType;
    public mediaSrc: string;

    constructor(document: DocumentContentBase = null, width: number = 0, height: number = 0,
                src: string = null, isFromCopy: boolean = false,
                dName: string = null, imageRatio: number = 0, mediaType: ImageMediaType = ImageMediaType.Image,
                mediaSrc: string = null, align: number = VertAlignType.Bottom) { // dName: drawing name exists
        if (!src) {
            src = mediaType === ImageMediaType.Audio ? audioIconStr : videoIconStr;
        }
        super(null, width, height, src, isFromCopy, dName, imageRatio, align);
        // this.type 依旧保持图片类型，复用排版；
        // mediaType用于判断具体的多媒体类型
        this.mediaType = mediaType;
        this.mediaSrc = mediaSrc;
        this.name = (mediaType === ImageMediaType.Audio ? '音频' : '视频') + this.id;

        if (document) {
            this.addToDrawingObjects(document, dName);
        }
    }

    public isMediaImage(): Boolean {
        return true;
    }

    /**
     * 拷贝
     * @param bForUI：true UI显示时所需宽度等信息
     */
    public copy(bForUI: boolean = false, document: DocumentContentBase = null): ParaMediaDrawing {
        const newDrawing = new ParaMediaDrawing(document, 0, 0, null, false, this.name, this.imageRatio, this.mediaType);

        newDrawing.positionX = this.positionX;
        newDrawing.positionY = this.positionY;

        if (true === bForUI) {
            newDrawing.id = this.id;
            newDrawing.name = this.name;
            newDrawing.content = RepalceText.SpaceForUI;
        }

        newDrawing.type = this.type;
        newDrawing.width = this.width;
        newDrawing.widthVisible = this.width;
        newDrawing.height = this.height;
        newDrawing.src = this.src;
        newDrawing.sizeLocked = this.sizeLocked;
        newDrawing.deleteLocked = this.deleteLocked;
        newDrawing.preserveAspectRatio = this.preserveAspectRatio;
        newDrawing.pageIndex = this.pageIndex;
        newDrawing.preload = this.preload;
        // newDrawing.imageRatio = this.imageRatio;
        newDrawing.mediaSrc = this.mediaSrc;
        // newDrawing.mediaType = this.mediaType;
        newDrawing.copyProtect = this.copyProtect;
        newDrawing.vertAlign = this.vertAlign;

        return newDrawing;
    }

    public setSrc(src: string): boolean {
        if (null != src && src !== this.src) {
            const history = this.getHistory();
            if (history) {
                history.addChange(new ChangeGraphicObjectSrc(this, this.src, src));
            }

            this.src = src;
            this.setDirty();
            return true;
        }

        return false;
    }

    /**
     * 设置多媒体链接
     * @param src 多媒体元素链接
     * @returns 是否设置成功
     */
    public setMediaSrc(src: string): boolean {
        if (null != src && src !== this.mediaSrc) {
            const history = this.getHistory();
            if (history) {
                history.addChange(new ChangeGraphicObjectSrc(this, this.mediaSrc, src));
            }

            this.src = src;
            this.setDirty();
            return true;
        }

        return false;
    }

    /**
     * 获取多媒体链接
     */
    public getMediaSrc(): string {
        return !!this.mediaSrc ? this.mediaSrc : '';
    }

    /**
     * 返回多媒体类型
     * @returns 多媒体类型
     */
    public getMediaType(): ImageMediaType {
        return this.mediaType;
    }

    public isImage(): boolean {
        return true;
    }

    public setDrawingProp(props: IParaDrawingProp): number {
        let result = false;

        result = this.setName(props.name) || result;
        result = this.setWidth(props.width) || result;
        result = this.setHeight(props.height) || result;

        result = this.setSrc(props.src) || result;
        result = this.setSizeLocked(props.sizeLocked) || result;
        result = this.setCopyProtect(props.copyProtect) || result;
        result = this.setDeleteLocked(props.deleteLocked) || result;
        result = this.setPreserveAspectRatio(props.preserveAspectRatio) || result;

        return (true === result ? ResultType.Success : ResultType.UnEdited);
    }

    public getProps(): IParaDrawingProp {
        const drawingProps: IParaDrawingProp = {
            name: this.name,
            sizeLocked: this.sizeLocked,
            deleteLocked: this.deleteLocked,
            preserveAspectRatio: this.preserveAspectRatio,
            copyProtect: this.copyProtect,
            width: this.width,
            height: this.height,
            type: this.type,
            src: this.src,
            mediaSrc: this.mediaSrc,
            mediaType: this.mediaType,
        };

        return drawingProps;
    }

    /** （可重写） 添加到DrawingObject中 */
    public addToDrawingObjects(document: DocumentContentBase, dName: string): void {
        const drawingObjects = document.getDrawingObjects();
        if (drawingObjects) {
            if (!drawingObjects.isParaEquation(this)) {
                let mtype = ParaElementType.ParaDrawing;
                switch (this.mediaType) {
                    case ImageMediaType.Video: {
                        mtype = ParaElementType.ParaVideoDrawing;
                        break;
                    }
                    case ImageMediaType.Audio: {
                        mtype = ParaElementType.ParaAudioDrawing;
                        break;
                    }
                }
                if (!dName) {
                    this.name = drawingObjects.makeUniqueImageName(mtype);
                } else {
                    this.name = drawingObjects.makeUniqueImageName(mtype, dName);
                }

                drawingObjects.addGraphicObject(this);
            }

            this.graphicObjects = drawingObjects;
        }
    }

}

/**
 * ParaBarcode：条形码、二维码共用
 */
// tslint:disable-next-line: max-classes-per-file
export class ParaBarcode extends ParaDrawing {
  // 条形码专有属性
  public bUse: boolean;
  public textAlign: string;

  // 共有属性
  public externalDataBind: IExternalDataProperty; // 外部数据源绑定
  // public equationElem: string; // original svg element

  // 二维码专有属性
  private errorCL: string;

  constructor(document: DocumentContentBase = null, width: number = 0, height: number = 0,
              src: string = null, isFromCopy: boolean = false,
              dName: string = null, datas: any = null) { // dName: drawing name exists
      super(null, width, height, src, isFromCopy, dName);

      this.content = datas?.content;
      // this.equationElem = svgElemStr;
      this.name = dName;
      this.type = ParaElementType.ParaBarcode;
      // this.height = 60;
      // this.width = 150;
      this.preserveAspectRatio = false;

      if (document) {
          this.addToDrawingObjects(document, this.name);
      }

      if (datas) {
        this.bUse = datas.bUse;
        this.textAlign = datas.textAlign;
        this.errorCL = datas.errorCL;

        if (this.errorCL) {
          this.type = ParaElementType.ParaQRCode;
        }

        if (datas.externalDataBind) {
          this.setSourceDataBind(datas.externalDataBind);
        }
      }
  }

  public addToDrawingObjects(document: DocumentContentBase, dName: string): void {
    const drawingObjects = document.getDrawingObjects();
    if (drawingObjects) {
        if (!dName) {
            this.name = drawingObjects.makeUniqueImageName(this.type);
        } else {
            this.name = drawingObjects.makeUniqueImageName(this.type, dName);
        }

        drawingObjects.addGraphicObject(this);
    }

    this.graphicObjects = drawingObjects;
  }

  public setSourceDataBind(props: IExternalDataProperty): number {
      let result = ResultType.UnEdited;
      if (!props || (!props.sourceObj && !this.externalDataBind?.sourceObj)) {
          return result;
      }

      if (!this.externalDataBind) {
          this.externalDataBind = {
              sourceObj: '',
              sourceKey: '',
              bReadOnly: undefined,
              commandUpdate: undefined,
          };
      }

      const sourceObj = this.externalDataBind.sourceObj;
      if (props.sourceObj !== this.externalDataBind.sourceObj) {
          this.externalDataBind.sourceObj = props.sourceObj;
          result = ResultType.Success;
      }

      if (props.sourceKey !== this.externalDataBind.sourceKey) {
          this.externalDataBind.sourceKey = props.sourceKey;
          result = ResultType.Success;
      }

      if (props.bReadOnly !== this.externalDataBind.bReadOnly) {
          this.externalDataBind.bReadOnly = props.bReadOnly;
          result = ResultType.Success;
      }

      if (props.commandUpdate !== this.externalDataBind.commandUpdate) {
          this.externalDataBind.commandUpdate = props.commandUpdate;
          result = ResultType.Success;
      }

      if (ResultType.Success === result) {
          const document = this.graphicObjects.getDocument();
          if (this.externalDataBind.sourceObj) {
            document.addSourceData(this.name, this.externalDataBind);
          } else {
            document.removeSourceData(this.name, sourceObj);
          }
      }

      return result;
  }

  public getSourceDataBind(): IExternalDataProperty {
      const datas = this.externalDataBind;
      if (datas) {
          return {
              sourceObj: datas.sourceObj,
              sourceKey: datas.sourceKey,
              bReadOnly: datas.bReadOnly,
              commandUpdate: datas.commandUpdate,
          };
      }

      return undefined;
  }

  public setDrawingProp(props: IParaDrawingProp): number {
      let result = false;

      result = this.setName(props.name) || result;
      result = this.setWidth(props.width) || result;
      result = this.setHeight(props.height) || result;
      this.bUse = props.bUse;
      this.textAlign = props.textAlign;
      this.errorCL = props.errorCL;

      this.content = props.content;
      result = this.setSrc(props.src);
      result = (ResultType.Success === this.setSourceDataBind(props.externalDataBind))
                || result;

      return (true === result ? ResultType.Success : ResultType.UnEdited);
  }

  public getProps(): any {
    const drawingProps: any = {
      name: this.name,
      width: this.width,
      height: this.height,
      type: this.type,
      src: this.src,
      bUse: this.bUse,
      errorCL: this.errorCL,
      textAlign: this.textAlign,
      content: this.content,
    };

    return drawingProps;
  }

  public copy(bForUI: boolean = false, document: DocumentContentBase = null): ParaBarcode {
    const newEquation = new ParaBarcode(document, 0, 0, null, null, null, null);

    newEquation.positionX = this.positionX;
    newEquation.positionY = this.positionY;

    if (true === bForUI) {
      newEquation.id = this.id;
      // when copy, id, name should not matter.
      // Only when paste should them be changed
      // but when render, id, name should be kept the same.
      newEquation.name = this.name;
      newEquation.content = RepalceText.SpaceForUI;
    } else {
      newEquation.content = this.content;
      newEquation.bUse = this.bUse;
      newEquation.textAlign = this.textAlign;
  
      newEquation.errorCL = this.errorCL;
    }

    newEquation.type = this.type;
    newEquation.width = this.width;
    newEquation.widthVisible = this.width;
    newEquation.height = this.height;
    newEquation.src = this.src;

    newEquation.pageIndex = this.pageIndex;
    newEquation.preload = this.preload;

    // newEquation.equationElem = this.equationElem;

    return newEquation;
  }

  public isBarCode(): boolean {
    return ParaElementType.ParaBarcode === this.type;
  }

  public isQRCode(): boolean {
    return ParaElementType.ParaQRCode === this.type;
  }

  public updateBarcode(): void {
    if (this.isBarCode()) {
      const src = genarateBarcode({
        content: this.content,
        height: this.height,
        textAlign: this.textAlign,
        bUse: this.bUse,
        drawingObjects: this.graphicObjects,
      });
  
      this.setSrc(src);
    } else if (this.isQRCode()){
      if (this.content) {
        const src = genarateQRCode({
          content: this.content,
          errorCL: this.errorCL,
        });

        this.setSrc(src);
      } else {
        this.setSrc(undefined);
      }
    }
  }

  public getErrorCL(): string {
    return this.errorCL;
  }

  public setSrc(src: string): boolean {
    if (src !== this.src) {
      const history = this.getHistory();
      if ( history ) {
        history.addChange(new ChangeGraphicObjectSrc(this, this.src, src));
      }

      this.src = src;
      this.setDirty();
      return true;
    }

    return false;
  }
}

// tslint:disable-next-line: max-classes-per-file
export class ParaBigDrawing extends ParaDrawing {
    public sourceUrl: string;
  
    constructor(document: DocumentContentBase = null, width: number = 0, height: number = 0,
            src: string = null, isFromCopy: boolean = false,
            dName: string = null, imageRatio: number = 0,
            align: number = VertAlignType.Bottom, datas: any = null) { // dName: drawing name exists
        super(null, width, height, src, isFromCopy, dName, imageRatio, align);
        this.preserveAspectRatio = false;
  
        if (document) {
            this.addToDrawingObjects(document, this.name);
        }

        if (datas) {
            this.sourceUrl = datas.sourceUrl || '';
        }
    }
  
    public addToDrawingObjects(document: DocumentContentBase, dName: string): void {
      const drawingObjects = document.getDrawingObjects();
      if (drawingObjects) {
          if (!dName) {
              this.name = drawingObjects.makeUniqueImageName(this.type);
          } else {
              this.name = drawingObjects.makeUniqueImageName(this.type, dName);
          }
  
          drawingObjects.addGraphicObject(this);
      }
      this.graphicObjects = drawingObjects;
    }
  
  
    public setDrawingProp(props: IParaDrawingProp): number {
        const result = super.setDrawingProp(props);
        return result;
    }
  
    public getProps(): any {
      const drawingProps: any = super.getProps();
  
      return drawingProps;
    }
  
    public copy(bForUI: boolean = false, document: DocumentContentBase = null): ParaBigDrawing {
      const newDraw = new ParaBigDrawing(document, 0, 0, null, null);
  
      newDraw.positionX = this.positionX;
      newDraw.positionY = this.positionY;
  
      if (true === bForUI) {
        newDraw.id = this.id;
        // when copy, id, name should not matter.
        // Only when paste should them be changed
        // but when render, id, name should be kept the same.
        newDraw.name = this.name;
        newDraw.content = RepalceText.SpaceForUI;
      } else {
        newDraw.content = this.content;
      }
  
      newDraw.type = this.type;
      newDraw.width = this.width;
      newDraw.widthVisible = this.width;
      newDraw.height = this.height;
      newDraw.src = this.src;
      newDraw.sizeLocked = this.sizeLocked;
      newDraw.deleteLocked = this.deleteLocked;
      newDraw.preserveAspectRatio = this.preserveAspectRatio;
      newDraw.pageIndex = this.pageIndex;
      newDraw.preload = this.preload;
      newDraw.imageRatio = this.imageRatio;
      newDraw.vertAlign = this.vertAlign;
      newDraw.copyProtect = this.copyProtect;

      newDraw.sourceUrl = this.sourceUrl;
  
      return newDraw;
    }

    public hasBind(): boolean {
        return true;
    }

    public getBind() {
        return {
            isBig: true,
            sourceUrl: this.sourceUrl,
        };
    }
  
  }
