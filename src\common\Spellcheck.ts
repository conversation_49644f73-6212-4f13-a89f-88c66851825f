import { IDocumentParagraph } from '@/model/ParaProperty';

interface ISpellData {
    content: IDocumentParagraph;
    ref: any;
    pageIndex: number;
    option?: {cellBoundX: number, cellBoundY: number};
}

interface ISpellMethods {
    check: (word: string) => boolean;
    checkExact: (word: string) => boolean;
    suggest: (word: string, limit?: number) => string[];
    correct: (word: string) => string[];
}

interface ILangWord {
    text: string;
    items: any[];
}

interface IRenderWord {
    [id: string]: {
        words: ILangWord[],
        ref: any;
    };
}

interface IRenderDom {
    [id: string]: HTMLDivElement;
}

interface IRenderCaches {
    [pageIndex: number]: {
        doms: HTMLDivElement,
        words: IRenderWord;
        oldDoms: string[];
    };
    [pageIndex: string]: {
        doms: HTMLDivElement,
        words: IRenderWord;
        oldDoms: string[];
    };
}

interface IParaData {
    [id: number]: ISpellData;
    [id: string]: ISpellData;
}

interface IPageData {
    [pageIndex: number]: IParaData;
    [pageIndex: string]: IParaData;
}

class SpellcheckMode {
    private _spellCaches: Map<string, boolean>;
    // private _host: any;
    private _spell: ISpellMethods;
    private _renderCaches: IRenderCaches;
    private _oldRenderChaches: IRenderCaches;
    private _activePages: number[];
    private _pageDatas: IPageData;
    private _timeout: any;
    private _localDatas: IPageData;
    private _bOpen: boolean;
    private _host: any;
    private _oldPages: number[];
    private _curListDom: any;
    private _observe: MutationObserver;

    public add(data: ISpellData): void {
        if (this._bOpen !== true) {
            return;
        }

        const pageIndex = data.pageIndex;
        // 局部刷新或者全部刷新数据
        const datas = this._localDatas || this._pageDatas;
        const page = datas[pageIndex];
        if (page) {
            page[data.content.id] = data;
        } else {
            this._pageDatas[pageIndex] = {
                [data.content.id]: data
            };
        }
    }

    public onScroll(pageIndexs: number[]) {
        if (this._bOpen !== true) {
            return;
        }
        this._activePages = pageIndexs;
        // clearTimeout(this._timeout);
        // this._timeout = setTimeout(() => {
        //     const oldActivePages = this._activePages || [999999, 1000000];
        //     const min = oldActivePages[0];
        //     const max = oldActivePages[1];
        //     const pageCaches = this._renderCaches;
        //     // const checkDatas = [];
        //     // const ids: number[] = [];
        //     for (let index = pageIndexs[0], len = pageIndexs[1] + 1; index < len; index++) {
        //         if (index >= min && index <= max) {
        //             continue;
        //         }
        //         // 当前页不渲染，所以dom会被销毁
        //         if (pageCaches[index]) {
        //             (pageCaches[index] as any).doms = null;
        //         }
        //         const datas = this._pageDatas[index];
        //         if (datas) {
        //             // ids.push(index);
        //             this.checking(datas, index);
        //         }
        //     }
        //     // this.checkedRender(id);
        //     // this.checkedRender(id, checkDatas);
        //     this._activePages = pageIndexs;
        //     // this.render();
        // }, 500);
    }

    public render(): void {
        if (this._bOpen !== true || !this._spell) {
            return;
        }
        const pageDatas = this._pageDatas;
        const keys: any[] = Object.keys(pageDatas);
        if (!keys.length) {
            return;
        }
        let activePages = this._activePages;
        if (!Array.isArray(activePages)) {
            const min = Math.min(...keys);
            const max = Math.max(...keys);
            activePages = [min, max];
        }
        // const pages = this._activePages;
        const start = activePages[0];
        const end = activePages[1];

        const oldPageCaches = this._renderCaches;
        this._oldRenderChaches = {};
        this._renderCaches = {};
        this._oldPages = null;
        for (let index = start; index <= end; index++) {
            const datas = pageDatas[index];
            if (!datas) {
                continue;
            }
            const caches = oldPageCaches[index];
            if (caches && caches.doms) {
                this.removeDoms(caches.doms);
            }
            this.checking(datas, index);
        }
        this.createObserver(this._host?.listDom);
        // this.checking(this._datas);
    }

    public renderPara(pageIndex: number): void {
        this.renderCell(pageIndex);
    }

    public renderCell(pageIndex: number): void {
        if (this._bOpen !== true || !this._localDatas) {
            return;
        }

        const page: any = this._renderCaches[pageIndex] || {};
        const words: IRenderWord = page.words;
        const datas: IParaData = this._localDatas[pageIndex];
        if (words) {
            const keys = Object.keys(datas);
            keys.forEach((key) => {
                if (words[key]) {
                    words[key] = null;
                }
            });
        }

        this.checking(datas, pageIndex);
        this._localDatas = null;
    }

    public renderPage(pageIndex: number) {
        if (this._bOpen !== true || !this._localDatas) {
            return;
        }
        const datas = this._localDatas[pageIndex];
        const caches = this._renderCaches;
        const keys: any[] = Object.keys(caches);
        const max = Math.max(...keys);
        for (let index = pageIndex; index <= max; index++) {
            const cache = caches[index];
            if (cache) {
                const doms = cache.doms;
                if (doms) {
                    this.removeDoms(doms);
                }
                this._renderCaches[index] = null;
            }
            this._oldRenderChaches[index] = null;
        }

        this.checking(datas, pageIndex);
        this._localDatas = null;
    }

    public setLocalDatas(pageIndex: number): void {
        if (this._bOpen !== true) {
            return;
        }
        this._localDatas = {[pageIndex]: {}};
    }

    public clearAll(): void {
        if (this._bOpen !== true) {
            return;
        }

        this._pageDatas = {};
    }

    public isOpen(): boolean {
        return this._bOpen === true;
    }

    public close(): void {
        if (this._bOpen !== true) {
            return;
        }
        this._bOpen = false;
        const caches = this._renderCaches;
        if (caches) {
            const keys = Object.keys(caches);
            keys.forEach((key) => {
                const cache = caches[key];
                if (!cache || !cache.doms) {
                    return;
                }
                const doms = cache.doms;
                if (doms) {
                    this.removeDoms(doms);
                }
            });
        }
        this._host = null;
        this._pageDatas = undefined;
        this._renderCaches = null;
        this._oldRenderChaches = null;
        this.mutationObserver()
        .disconnect();
        this._observe = null;
        this._curListDom = null;
    }

    public open(): Promise<boolean> {
        this._bOpen = true;
        this._pageDatas = {};
        this._renderCaches = {};
        this._oldRenderChaches = {};
        this._activePages = undefined;
        return new Promise(async (resolve, reject) => {
            if (this._spell) {
                this.render();
                return resolve(true);
            }
            await this.init();
            resolve(true);
        });
    }

    public initUnSpellWords(words: string[]): void {
        if (this._bOpen !== true) {
            return;
        }

        if (!words || !Array.isArray(words)) {
            return;
        }
        const map = this._spellCaches;
        words.forEach((word) => {
            map.set(word, true);
        });
    }

    private checkedRender = (): void => {
        clearTimeout(this._timeout);
        this._timeout = setTimeout(() => {
            // const pages = this._activePages;
            // if (!pages || !pages.length) {
            //     return;
            // }
            // const pageCaches = this._renderCaches;
            // for (let index = 0, len = pages.length; index < len; index++) {
            //     const pageIndex = pages[index];
            //     const page = pageCaches[pageIndex];
            //     if (!page) {
            //         continue;
            //     }
            //     if (page.doms && !page.doms.parentNode) {
            //         const dom = window['s' + pageIndex];
            //         if (dom) {
            //             // const g: any = document.createElementNS('http://www.w3.org/2000/svg', 'g');
            //             // g.setAttribute('class', 'spell-name');
            //             // g.innerHTML = html;
            //             dom.appendChild(page.doms.innerHTML);
            //             // window['s' + pageIndex] = page.doms;
            //         }
            //     }
            // }
            // const pageIndexs = this._activePages;
            const activePages = this._activePages || [999999, 1000000];
            const oldPages = this._oldPages || [999999, 1000000];
            const min = oldPages[0];
            const max = oldPages[1];
            const pageCaches = this._renderCaches;
            // const checkDatas = [];
            // const ids: number[] = [];
            // console.log(111)
            for (let index = activePages[0], len = activePages[1] + 1; index < len; index++) {
                if (index >= min && index <= max) {
                    continue;
                }
                // 当前页不渲染，所以dom会被销毁
                if (pageCaches[index]) {
                    const doms = (pageCaches[index] as any).doms;
                    if (doms) {
                        this.removeDoms(doms);
                    }
                    (pageCaches[index] as any).doms = null;
                }
                const datas = this._pageDatas[index];
                if (datas) {
                    // ids.push(index);
                    this.checking(datas, index);
                }
            }
            this._oldPages = activePages;
        }, 50);
    }

    private createObserver(dom: HTMLDivElement) {
        if (!dom) {
            return;
        }
        if (this._curListDom === dom) {
            return;
        }
        this._curListDom = dom;
        this.mutationObserver()
        .observe(dom, {childList: true});
    }

    private mutationObserver() {
        if (this._observe) {
            return this._observe;
        }
        return this._observe = new MutationObserver((e) => {
            this.checkedRender();
        });
    }

    private checking(datas: IParaData, pageIndex: number): void {
        if (!this._spell) {
            return;
        }

        const keys = Object.keys(datas);
        if (!keys.length) {
            return;
        }

        const res: IRenderWord = {};
        const pageCaches: any = this._renderCaches[pageIndex] || {};
        const wordCaches: IRenderWord = (pageCaches.words || {} as any);
        // let oldDoms;
        let oldDomKeys: string[] = (pageCaches.oldDomKeys || [])   as any;
        const oldPage = this._oldRenderChaches[pageIndex];
        const hasKeys = [];
        const oldDoms: HTMLDivElement = pageCaches.doms;

        keys.forEach((key) => {
            // delete oldDoms[key];
            // 存在缓存，不需要重新检测是否合法
            if (wordCaches[key]) {
                hasKeys.push(key);
                wordCaches[key].ref = datas[key].ref;
                res[key] = wordCaches[key];
                return;
            }

            const data: ISpellData = datas[key];
            if (!this._host) {
                this._host = data.ref.props.host;
            }

            const words = this.getData(data);
            if (words.length) {
                hasKeys.push(key);
                wordCaches[key] = res[key] = {words, ref: data.ref};
            }
        });

        if (oldDoms) {
            if (oldDomKeys.length) {
                keys.forEach((key) => {
                    const index = oldDomKeys.findIndex((item) => item === key);
                    if (hasKeys.includes(key)) {
                        if (index === -1) {
                            oldDomKeys.push(key);
                        }
                        return;
                    }
                    if (index === -1) {
                        return;
                    }
                    const dom: any = oldDoms.querySelector(`[p="${key}"]`);
                    if (dom) {
                        this.removeDoms(dom);
                    }
                    oldDomKeys.splice(index, 1);
                });
            } else {
                oldDomKeys = hasKeys;
            }
        } else {
            oldDomKeys = hasKeys;
        }

        if (!Object.keys(res).length) {
            return;
        }

        pageCaches.words = wordCaches;
        pageCaches.oldDomKeys = oldDomKeys;
        this._renderCaches[pageIndex] = pageCaches;
        this._oldRenderChaches[pageIndex] = pageCaches;
        this.renderWords(res, pageIndex);
    }

    private renderWords(datas: IRenderWord, pageIndex: number): void {
        const keys = Object.keys(datas);
        const pageCaches: any = this._renderCaches[pageIndex] || {};
        const doms: HTMLDivElement = pageCaches.doms;
        const oldDomKeys = pageCaches.oldDomKeys;
        let html = '';
        keys.forEach((key) => {
            const data = datas[key];
            const words: ILangWord[] = data.words;
            let p = '';
            const option = data.ref.props.option;
            let maxWidth;
            let maxHeight;
            if (option) {
                maxWidth = option.cellBoundX;
                maxHeight = option.cellBoundY;
            }
            // const ref = data.ref;
            // 渲染每个段落的单词
            words.forEach((word) => {
                const firstItem = word.items[0];
                const x = firstItem.positionX;
                const y = firstItem.positionY + 2;
                if ( maxHeight !== undefined && y > maxHeight) {
                    return;
                }
                if (maxWidth !== undefined && x > maxWidth) {
                    return;
                }
                let width: number = 0;
                word.items.forEach((item) => {
                    width += item.widthVisible;
                });
                if (maxWidth !== undefined) {
                    width = Math.min(maxWidth, x + width);
                } else {
                    width += x;
                }
                p += `<line p='${key}' x1='${x}' y1='${y}' x2='${width}' y2='${y}' />`;
            });
            if (!p) {
                const curIndex = oldDomKeys.find((item) => item === key);
                if (curIndex !== -1) {
                    const dom: any = doms.querySelector(`[p="${key}"]`);
                    if (dom) {
                        this.removeDoms(dom);
                    }
                }
                return;
            }
            if (doms) {
                const dom = doms.querySelector(`[p="${key}"]`);
                if (dom) {
                    dom.innerHTML = p;
                } else {
                    const g: any = document.createElementNS('http://www.w3.org/2000/svg', 'g');
                    g.innerHTML = p;
                    g.setAttribute('p', key);
                    doms.appendChild(g);
                }
            } else {
                html += `<g p="${key}">${p}</g>`;
            }
        });
        if (!doms) {
            const svg: HTMLDivElement = window['s' + pageIndex] as any;
            if (svg) {
                const g: any = document.createElementNS('http://www.w3.org/2000/svg', 'g');
                g.setAttribute('class', 'spell-name');
                g.innerHTML = html;
                svg.appendChild(g);
                pageCaches.doms = g;
            }
        }
        this._renderCaches[pageIndex] = pageCaches;
    }

    private removeDoms(dom: HTMLDivElement): void {
        if (dom.parentNode) {
            dom.outerHTML = '';
        }
    }

    /**
     * 对一个段落数据进行查询
     * @param data 当前段落集合
     * @returns 拼写错误集合
     */
    private getData(data: ISpellData): ILangWord[] {
        if (!data) {
            return [];
        }
        const content: IDocumentParagraph = data.content;
        const contents = content.content;
        const len = contents.length;
        if (len === 1 && contents[0].isEmpty(false)) {
            return [];
        }
        // const lines = content.lines;
        let portionIndex: number = 0;
        const {startLine, endLine} = content;
        let portions: any = [];
        let lineWords: ILangWord[] = [];
        for (let line = startLine; line <= endLine; line++) {
            let start = 0;
            let startPos = 0;
            let endItem;
            for (let cIndex = portionIndex; cIndex < len; cIndex++) {
                const item = contents[cIndex];
                if (!item) {
                    portionIndex = cIndex;
                    break;
                }
                if (line >= item.startLine) {
                    portionIndex = cIndex;
                    if (line >= item.startLine + item.getLinesCount()) {
                        continue;
                    }
                    if (start === 0) {
                        startPos = item.getRangeStartPos(line - item.startLine);
                    }
                    endItem = item;
                    portions.push(item);
                    start++;
                } else {
                    break;
                }
            }
            // let endPos;
            if (!endItem) {
                continue;
            }
            const endPos = endItem.getRangeEndPos(line - endItem.startLine);
            const words = this.getLangWord(portions, startPos, endPos);
            if (words.length) {
                lineWords = lineWords.concat(words);
            }
            portions = [];
        }

        return lineWords;
    }

    /**
     * 获取当前行所有异常的集合
     * @param datas portions集合
     * @param start 开始位置
     * @param end 结束位置
     * @returns 拼写错误的单词
     */
    private getLangWord(datas: any[], start: number, end: number): ILangWord[] {
        const res: ILangWord[] = [];
        const reg = /^[a-z]$/i;
        // 多个portion进行联合查询
        const len = datas.length - 1;
        let word: ILangWord;
        const wordCaces = this._spellCaches;
        datas.forEach((data, index) => {
            let startPos: number;
            let endPos: number;
            if (index === 0) {
                startPos = start;
            } else {
                startPos = 0;
            }
            if (index === len) {
                endPos = end;
            }
            const contents = data.content.slice(startPos, endPos);
            for (let cIndex = 0, cLen = contents.length; cIndex < cLen; cIndex++) {
                const content = contents[cIndex];
                if (!content) {
                    break;
                }
                const text = content.content;
                if (!text) {
                    continue;
                }
                if (!reg.test(text)) {
                    // 两个以下不进行检查
                    if (word && word.items.length > 1) {
                        // 从缓存获取
                        const cache: boolean = wordCaces.get(word.text);
                        if (cache === undefined) {
                            // 缓存不存在才进行查询
                            if (this._spell.check(word.text)) {
                                wordCaces.set(word.text, true);
                            } else {
                                res.push(word);
                                wordCaces.set(word.text, false);
                            }
                        } else if (cache === false) { // 检测不通过才进行添加
                            res.push(word);
                        }
                    }
                    word = null;
                    continue;
                }
                if (!word) {
                    word = {
                        text: '',
                        items: []
                    };
                }
                word.text += text;
                word.items.push(content);
            }
        });

        if (word && word.items.length > 1 && !this._spell.check(word.text)) {
            res.push(word);
        }

        return res;
    }

    private init(): Promise<void> {
        // if (this._host) {
        //     this._host = host;
        //     return;
        // }

        // this._host = host;
        return new Promise(async (reso, reject) => {
            this._spellCaches = new Map();
            // this._pageDatas = {};
            const module = await import('typo-js');
            const typo = module.default;
            // this._renderCaches = {};
            // this._oldRenderChaches = {};
            // this._activePages = [0, 0];
            const spell = new typo('en_US', null, null, {dictionaryPath: './dictionaries',
            asyncLoad: true, loadedCallback: () => {
                this._spell = spell;
                this.render();
                reso();
            }});
        });
    }
}

export const SPELL_CHECK = new SpellcheckMode();
