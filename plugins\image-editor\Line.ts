import { fabric } from 'fabric';

interface IProps {
  color: string;
  size: number;
}

export function createLine(x: number, y: number, props: IProps): fabric.Line {
  const { color, size } = props;
  const line = new fabric.Line([x, y, x, y],{
    originX: 'center',
    originY: 'center',
    stroke: color,
    strokeWidth: size,
    fill: '',
    type: 'line'
  });
  return line;
}

export function buildingLine(line: fabric.Line, x: number, y: number): void {
  line.set({x2: x, y2: y});
}

export function updateLineProps(line: fabric.Line, props: IProps): void {
  const { color, size } = props;
  line.set({
    stroke: color,
    strokeWidth: size,
  });
}
