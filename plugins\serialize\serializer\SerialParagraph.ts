import { IIndentAttributesProperties, ISpacingProperties, LineRuleType } from 'docx';
import { ISerialParaObj, ISerialPortionObj, SerialObjType } from '../serialInterface';
import ISerial from './ISerial';
import SerialPortion from './SerialPortion';
import { convertAlignment, mmToDocxIndentLength } from './Utils';

/**
 * 行间距类型
 */
export enum LineSpacingType {
    Single = 1,
    SingeHalf,
    Double,
    Multi,
    Min,
    Fixed,
}
export enum ReviewType {
    Common,
    Remove,
    Add,
}
export default class SerialParagraph implements ISerial {
    private paraObj: ISerialParaObj; // 用于暂存软回车时的当前段落对象
    private tmpCollector: ISerialParaObj[] = [];

    constructor(private readonly para: any) { }

    public serializedTo(collector: ISerialParaObj[]): ISerialParaObj[] {
        this.tmpCollector = collector;
        this.paraObj = this.buildParagraphObj();
        const contents = this.para.getContent();
        let fullDelete = contents[0] && contents[0].getReviewType() === ReviewType.Remove; // 标记当前段落是否全是删除修订
        for (const content of contents) {
            const revisionType = content.getReviewType();
            if (revisionType === ReviewType.Remove || content.isParaEnd()) {
                // 6版本直接移除被删除的修订
                continue;
            }
            fullDelete = false;
            new SerialPortion(content).serializedTo(this.paraObj.children as any[], this);
        }
        if (!fullDelete) {
            collector.push(this.paraObj);
        }
        return collector;
    }

    /** 将当前段落对象推送到容器中，创建新的段落对象 */
    public reBuildCollector(): ISerialPortionObj[] {
        this.tmpCollector.push(this.paraObj);
        this.paraObj = this.buildParagraphObj();
        return this.paraObj.children as any[];
    }

    /** 创建段落序列化对象 */
    private buildParagraphObj(): ISerialParaObj {
        const prop = this.para.paraProperty;
        return {
            type: SerialObjType.Paragraph,
            children: [],
            border: undefined,
            // heading: HeadingLevel.HEADING_1, // doc的标题等级
            bidirectional: false,
            pageBreakBefore: prop.bPageBreakBefore,
            tabStops: [],
            style: '',
            // bullet: {
            //     level: 0
            // },
            shading: undefined,
            widowControl: false,
            frame: undefined,
            // suppressLineNumbers: false,
            // numbering: {
            //     reference: '',
            //     level: 0,
            //     instance: 0,
            //     custom: false
            // },
            alignment: convertAlignment(prop.alignment),
            thematicBreak: false,
            contextualSpacing: prop.bWordWrap,
            rightTabStop: undefined,
            leftTabStop: undefined,
            indent: this.getIndent(prop.paraInd),
            spacing: this.getSpacing(prop.paraSpacing),
            keepNext: false,
            keepLines: false,
            outlineLevel: undefined,
            wordWrap: prop.bWordWrap,
            overflowPunct: false,
        };
    }

    /** 获取段落间距 */
    private getSpacing(spacing: any): ISpacingProperties {
        if (!spacing) {
            return undefined;
        }
        const prop: ISpacingProperties = {
            after: spacing.paraSpacingAfter || 0,
            before: spacing.paraSpacingBefore || 0,
            line: spacing.lineSpacing,
            lineRule: LineRuleType.AUTO,
            beforeAutoSpacing: false,
            afterAutoSpacing: false
        };
        this.fixedLineRuleType(spacing.lineSpacingType, prop);
        return prop;
    }

    /** 修正间距类型（将倍数转换为docx内部单位） */
    private fixedLineRuleType(type: LineSpacingType, prop: any): void {
        const lineHeight = 240;
        prop.after *= lineHeight;
        prop.before *= lineHeight;
        switch (type) {
            case LineSpacingType.Single: {
                prop.line = lineHeight;
                prop.lineRule = LineRuleType.AUTO;
                break;
            }
            case LineSpacingType.SingeHalf: {
                prop.line = lineHeight * 1.5;
                prop.lineRule = LineRuleType.AUTO;
                break;
            }
            case LineSpacingType.Double: {
                prop.line = lineHeight * 2;
                prop.lineRule = LineRuleType.AUTO;
                break;
            }
            case LineSpacingType.Multi: {
                prop.line *= lineHeight;
                prop.lineRule = LineRuleType.AUTO;
                break;
            }
            case LineSpacingType.Min: {
                prop.line = lineHeight;
                prop.lineRule = LineRuleType.AT_LEAST;
                break;
            }
            case LineSpacingType.Fixed: {
                prop.line *= lineHeight;
                prop.lineRule = LineRuleType.EXACTLY;
                break;
            }
        }
    }

    /** 获取段落缩进内容 */
    private getIndent(indent: any): IIndentAttributesProperties {
        const prop: IIndentAttributesProperties = {
            left: mmToDocxIndentLength(indent.left) || undefined,
            hanging: indent.firstLine < 0 ? mmToDocxIndentLength(-indent.firstLine) : undefined, // 悬挂
            firstLine: indent.firstLine > 0 ? mmToDocxIndentLength(indent.firstLine) : undefined, // 首行
        };
        return prop;
    }

}
