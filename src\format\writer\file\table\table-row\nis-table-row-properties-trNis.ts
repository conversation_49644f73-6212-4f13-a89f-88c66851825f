// tslint:disable-next-line: max-line-length
import { DataType, ICustomProps, INISRowProperty, NISRowType, parseBoolean } from '../../../../../common/commonDefines';
import { customEncodeURIComponent } from '../../../../../common/commonMethods';
import { CustomProperty } from '../../paragraph/contentControl/customProperty';
import { XmlComponent, XmlAttributeComponent } from '../../xml-components';

export interface ITrNisProperties {
    type: number;
}

// tslint:disable-next-line: max-classes-per-file
class ITrNisAttributes extends XmlAttributeComponent<ITrNisProperties> {
    protected xmlKeys: any = {
        type: 'type',
    };
}

// tslint:disable-next-line: max-classes-per-file
export class TrNis extends XmlComponent {

    private readonly customProperty: CustomProperty;
    constructor(attrs: ITrNisProperties) {
        super('w:trNis');
        if (attrs && NISRowType.Normal !== attrs.type) {
            this.root.push(new ITrNisAttributes(attrs));
        }

        this.customProperty = new CustomProperty();
    }

    public addContent(nisProperty: INISRowProperty): TrNis {
        if (nisProperty == null) {
            return this;
        }
        // const type = nisProperty.type;
        // common props
        // fontFamily?: string;
        // fontSize?: number;
        // paraSpacing?: LineSpacingType;
        // alignType?: AlignType;
        const {type, rowID, signStatus, bDeleteProtect, customProperty, creator, sumStatus, sumKey} = nisProperty;
        if (rowID != null) {
            this.root.push(new RowID(rowID));
        }
        if (signStatus) {
            this.root.push(new SignStatus('' + signStatus));
        }
        if (bDeleteProtect != null && bDeleteProtect === true) {
            const result = bDeleteProtect === true ? '1' : '0';
            this.root.push(new BDeleteProtect(result));
        }

        // if (customProperty instanceof Map && customProperty.size > 0) { // default empty map
        if (customProperty != null) {
            // this.root.push(new CustomProperty(JSON.stringify(customProperty)));

            // change customProperty to Map()
            const customPropertyMap = this.addCustomProps(customProperty); //

            // add customControlElements as well
            this.customProperty.addCustomProperties(customPropertyMap);
            if (customPropertyMap.size > 0) {
                this.root.push(this.customProperty);
            }
        }

        if (creator != null) {
            this.root.push(new Creator(creator));
        }

        // if (type != null) {
        //     this.root.push(new Type('' + type));
        // }

        if (sumKey) {
            this.root.push(new SumKey(sumKey));
        }
        if (sumStatus != null && sumStatus !== 0) {
            const result = sumStatus;
            this.root.push(new SumStatus('' + result));
        }

        return this;
    }

    private addCustomProps(props: ICustomProps[], bClearItems: boolean = true): Map<string, ICustomProps> {
        const customProperty: Map<string, ICustomProps> = new Map();
        if (!props) {
            return customProperty;
        }
        if (bClearItems) {
            customProperty.clear();
        }
        props.forEach((prop) => {
            if (prop.value === undefined) {
              prop.value = '';
            }
            customProperty.set(prop.name, prop);
            switch (prop.type) {
                case DataType.Boolean:
                    // prop.targetValue = prop.value ? parseBoolean(prop.value) : undefined;
                    prop.targetValue = prop.value ? parseBoolean(prop.value) : '';
                    break;
                case DataType.Number:
                    // prop.targetValue = prop.value ? Number(prop.value) : undefined;
                    prop.targetValue = prop.value ? Number(prop.value) : '';
                    break;
                default:
                    prop.targetValue = prop.value;
            }
        });
        return customProperty;
    }

}

// tslint:disable-next-line: max-classes-per-file
export class RowID extends XmlComponent {
    constructor(val: string) {
        super('rowID');
        this.root.push(customEncodeURIComponent(val));
    }
}

// tslint:disable-next-line: max-classes-per-file
export class SignStatus extends XmlComponent {
    constructor(val: string) {
        super('signStatus');
        this.root.push(val);
    }
}

// tslint:disable-next-line: max-classes-per-file
export class BDeleteProtect extends XmlComponent {
    constructor(val: string) {
        super('bDeleteProtect');
        this.root.push(val);
    }
}

// tslint:disable-next-line: max-classes-per-file
export class Creator extends XmlComponent {
    constructor(val: string) {
        super('creator');
        this.root.push(customEncodeURIComponent(val));
    }
}

// tslint:disable-next-line: max-classes-per-file
export class Type extends XmlComponent {
    constructor(val: string) {
        super('type');
        this.root.push(val);
    }
}

// tslint:disable-next-line: max-classes-per-file
export class SumStatus extends XmlComponent {
    constructor(val: string) {
        super('sumStatus');
        this.root.push(val);
    }
}

// tslint:disable-next-line: max-classes-per-file
export class SumKey extends XmlComponent {
    constructor(val: string) {
        super('sumKey');
        this.root.push(customEncodeURIComponent(val));
    }
}
