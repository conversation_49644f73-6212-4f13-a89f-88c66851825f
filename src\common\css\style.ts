export function printStyle(): string {
    return `
            body {
                background: #ccc;
                text-align: center;
                margin: 0;
                padding: 0;
                overflow: hidden;
            }
            input {
                padding: 2px;
                border: 1px solid #bbb;
            }
            svg {
                display: block;
                margin: 0px auto;
                cursor: text;
            }
            svg+svg {
                margin-top: 20px;
            }

            .button-rect {
                stroke-width: 0.5;
                shape-rendering: crispedges;
                stroke: #666;
                fill: #ddd;
                pointer-events: none;
            }
            .table-container line {
                shape-rendering: crispedges;
            }

            #textarea_input {
                position: absolute;
                z-index: 0;
                width: 0.5em;
                height: 1em;
                opacity: 0;
                background: transparent;
                -moz-appearance: none;
                appearance: none;
                border: none;
                resize: none;
                outline: none;
                overflow: hidden;
                font: inherit;
                padding: 0 1px;
                margin: 0 -1px;
                contain: strict;
                -ms-user-select: text;
                -moz-user-select: text;
                -webkit-user-select: text;
                user-select: text;
                /* white-space: pre !important; */
                pointer-events: none;
            }
            tspan,
            text {
                -moz-user-select: none; /*火狐*/
                -webkit-user-select: none; /*webkit浏览器*/
                -ms-user-select: none; /*IE10*/
                user-select: none;
            }
            .selection {
                fill: white;
                opacity: 0;
            }
            .selection-selected {
                /* fill: cornflowerblue; */
                fill: #009fff;
                opacity: 0.5;
            }

            image {
                cursor: all-scroll;
            }
            image.select-button {
                cursor: pointer;
            }
            .opacity {
                opacity: 0.5;
                pointer-events: none;
            }
            .header-footer-enabled {
                opacity: 0.5;
                pointer-events: none;
            }
            .hide, .hidden {
                display: none;
            }

            .hz-editor-print {
                width: 100%;
            }
            .hz-editor-print .ReactVirtualized__Grid {
                max-width: auto !important;
                overflow: hidden !important;
            }
            .hz-editor-print .ReactVirtualized__Grid__innerScrollContainer {

                width: 100% !important;
                height: 100% !important;
                max-width: 100% !important;
                max-height: 100% !important;
                margin-left: 0 !important;
                margin-right: 0 !important;
            }

            .hz-editor-print .page-wrapper {

                position: static !important;
                margin-top: 0;
                margin-bottom: 20px;
                background: none;
            }

            .hz-editor-print .page-wrapper svg {
                transform-origin: 0 0;
            }
            .hz-editor-print .scale-200 .page-wrapper svg {
                float: left;
                transform: scale(2, 2);
            }
            .hz-editor-print .scale-150 .page-wrapper svg {
                float: left;
                transform: scale(1.5, 1.5);
            }
            .hz-editor-print .scale-75 .page-wrapper {

                float: none;
                margin-left: auto;
                margin-right: auto;
            }

            .hz-editor-print .scale-75 .page-wrapper svg {
                transform: scale(0.75, 0.75);
            }
            .hz-editor-print .scale-50 .page-wrapper, .hz-editor-print .scale-25 .page-wrapper {
                float: left;
            }
            .hz-editor-print .scale-50 .page-wrapper:nth-child(2n) {
                margin-left: 10px;
            }
            .hz-editor-print .scale-50 .page-wrapper svg {
                transform: scale(0.50, 0.50);
            }
            .hz-editor-print .scale-25 .page-wrapper {
                margin-left: 10px;
            }
            .hz-editor-print .scale-25 .page-wrapper:nth-child(4n + 1) {
                margin-left: 0;
            }
            .hz-editor-print .scale-25 .page-wrapper svg {
                transform: scale(0.25, 0.25);
            }
            .hz-editor-print .region-btn {
              display: none !important;
            }
            .image-processor {
                display: none;
            }
        `;
}

export function doPrintStyle(): string {
    return `
        * {
            margin: 0;
            padding: 0;
        }
        @media print {
            @page {
                margin: 0;
                padding: 0;
            }
            @page rotated {
                size: A4 landscape;
                margin-bottom: -8.8cm; /* > 29.7 - 21 */
            }
        }

        .print-container-landscape {
            page: rotated;
        }

        .print-interface .header-bg, .print-interface .footer-bg {
            display: none;
        }


        .button-rect {
            stroke-width: 0.5;
            shape-rendering: crispedges;
            stroke: #666;
            fill: #ddd;
            pointer-events: none;
        }

        .table-container line {
            shape-rendering: crispedges;
        }


        .pager {
            position: relative;
            left: 0;
            top: 0;
            text-align: left;
            page-break-after: always;
            overflow: hidden;
        }

        .print-hide {
            display: none;
        }

        .pager .transparent {
            opacity: 0;
        }

        img {
            border: none;
        }

        .header-bg {
            position: absolute;
            top: 0;
            left: 0;
            z-index: -1;
            width: 100%;
            margin: 0 auto;
            height: 60px;
            background: #fff;
            display: none;
        }

        .footer-bg {
            position: absolute;
            bottom: 0;
            right: 0px;
            z-index: -1;
            width: 100%;
            height: 50px;
            background: #fff;
            display: none;
        }

        .hide, .batch .nistable > line, .batch line.nisgrid {
            display: none;
        }
    `;
}
// .header {
//     position: absolute;
//     top: 20px;
//     left: 0;
//     z-index: 2;
//     width: 100%;
//     margin: 0 auto;
//     text-align: center;
//     color: #ccc;
// }
// .footer {
//     position: absolute;
//     bottom: -30px;
//     right: 30px;
//     z-index: 9;
//     color: #ccc;
//     text-align: right;
// }

export function getMessageStyle(): string {
    const style = `
      #editor-dom-container .editor-message {
        display: none;
        position: fixed;
        left: 0;
        top: 0;
        z-index: 9999;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.02);
      }
      #editor-dom-container .editor-message.active {
        display: block;
      }
      #editor-dom-container .editor-message .editor-message-box {
        position: relative;
        left: 0;
        right: 0;
        top: 50%;
        z-index: 1;
        width: 300px;
        margin: 0 auto;
        padding: 5px 0;
        font-size: 13px;
        font-family: 'PingFang SC', 'Microsoft YaHei', 'Microsoft Jhenghei', sans-serif;
        box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.36);
        background-color: #fff;
        border-radius: 3px;
        border: 1px solid #eee;
        transform: translateY(-50%);
      }
      #editor-dom-container .editor-message .editor-message-title {
        // height: 22px;
        height: 100%;
        // margin-bottom: 10px;
        text-align: left;
        font-size: 14px;
        line-height: 22px;
        color: #293750;
        border-bottom: 1px solid #DFE2E5;
        padding: 0 10px;
      }
      #editor-dom-container .editor-message .editor-message-title > span {
        display: inline-block;
        margin-bottom: 10px;
      }
      #editor-dom-container .editor-message .editor-message-title .editor-message-close {
        display: inline-block;
        float: right;
        color: #ACB4C1;
        cursor: pointer;
      }
      #editor-dom-container .editor-message .editor-message-body {
        padding: 10px 5px 0 5px;
        text-align: center;
        line-height: 24px;
      }
      #editor-dom-container .editor-message .editor-message-btns {
        margin-top: 20px;
        padding: 5px 0;
        text-align: center;
        line-height: 22px;
        font-size: 14px;
      }
      #editor-dom-container .editor-message .editor-message-btns > span {
        margin-right: 20px;
        padding: 2px 8px;
        cursor: pointer;
        border: 1px solid #d5d4dc;
        border-radius: 2px;
        display: inline-block;
        width: 100px;
      }
      #editor-dom-container .editor-message .editor-message-btns > span:last-child {
        margin-right: 0;
      }

      #editor-dom-container .editor-message .editor-message-body {
        font-size: 16px;
        color: #293750;
        font-weight: 600;
      }

      #editor-dom-container .editor-message .editor-message-body .editor-message-warning {
        font-size: 40px;
        color: red;
        margin-top: 10px;
      }

      #editor-dom-container .editor-message.editor-confirm .editor-message-btns > span:last-child {
        color: #009fff;
      }

      #editor-dom-container .editor-message.editor-confirm .editor-message-btns.cancelDefault > span:last-child {
        color: #000000;
      }

      #editor-dom-container .editor-message.editor-confirm .editor-message-btns.cancelDefault > span:first-child {
        color: #009fff;
      }
    `;

    return style;
}
/*#editor-dom-container .printFrame-box {
        position: fixed;
        left: -3000%;
        top: 85px;
        width: 21cm;
        height: 29.7cm;
        opacity: 0;
        background: #fff;
        z-index: -1;
    } */
export function getPrintReviewStyle(): string {
    return `
    #editor-dom-container .print-dialog {
        display: none;
        position: fixed;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        z-index: 99;
        background: #EBEFF2;
    }

    .button-rect {
        stroke-width: 0.5;
        shape-rendering: crispedges;
        stroke: #666;
        fill: #ddd;
        pointer-events: none;
    }
    .table-container line {
        shape-rendering: crispedges;
    }

    #editor-dom-container .print-dialog-header {
        width: 100%;
        background: #FFFFFF;
        position: relative;
        padding: 11px;
    }
    #editor-dom-container .print-dialog .hidden {
        display: none !important;
    }
    #editor-dom-container .print-dialog.visible {
        display: block;
        opacity: 0;
        }
    #editor-dom-container .print-dialog.active {
        display: flex;
        flex-direction: column;
    }
    #editor-dom-container .print-dialog.print-hide {
        left: -500%;
        opacity: 0;
    }
    #editor-dom-container .print-dialog .print-dialog-box {
        flex: 99;
        width: 100%;
        margin: 0 auto;
        background: #fff;
    }
    #editor-dom-container .print-dialog .print-dialog-box-header {
        height: 50px;
        background: #EBEFF2;
        line-height: 50px;

        font-size: 13px;

        flex: 1;
    }
    #editor-dom-container .print-dialog .print-dialog-box-header > div:first-child {
        line-height: 50px;
        margin-left: 10px;
        text-align: left;
    }
    #editor-dom-container .print-dialog .print-dialog-box-header-clinc {
        display: inline-block;
        line-height: 20px;
        font-size: 12px;
    }
    #editor-dom-container .print-dialog .print-dialog-box-body {
        height: calc(100% - 50px);
        border: 1px dashed #999;
        border-radius: 1px;
    }
    #editor-dom-container .print-dialog p {
        margin: 0;
        padding: 0;
    }
    #editor-dom-container .print-dialog .print-dialog-box .ratio-block {
        display: inline-block;
        border-left: 2px solid #ADB1B3;
        margin-left: 10px;
        padding-left: 10px;
        height: 30px;
        line-height: 30px;
    }

    .print-dialog .print-dialog-box .continue-block {
        display: none;
        height: 30px;
        line-height: 30px;
    }
    .print-dialog .print-dialog-box #cont-checked + .continue-block {
        display: inline-block;
    }

    #editor-dom-container .print-dialog .print-dialog-box .ratio-block select {
        width: 120px;
        height: 28px;
        position: relative;
        bottom: 1px;
    }
    #editor-dom-container .print-dialog .btns {
        float: right;
        margin-right: 3px;
    }
    #editor-dom-container .print-dialog .btns button {
        border: 1px solid #DDDDE3;
        background: #FFFFFF;
    }
    `;
}

export function getCPrintStyle(): string {
    return `
    #editor-dom-container .print-dialog-c {
        display: none;
        position: fixed;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        z-index: 99;
        background: rgba(0, 0, 0, 0.1);
        transition: height 0.5s;

        overflow: auto;
    }
    .button-rect {
        stroke-width: 0.5;
        shape-rendering: crispedges;
        stroke: #666;
        fill: #ddd;
        pointer-events: none;
    }
    .table-container line {
        shape-rendering: crispedges;

    }
    #editor-dom-container .print-dialog-c.visible {
        display: block;
        opacity: 0;
        }
    #editor-dom-container .print-dialog-c.active {
        display: block;
    }
    #editor-dom-container .print-dialog-c.print-hide {
        left: -500%;
        opacity: 0;
    }
    #editor-dom-container .print-dialog-c .print-dialog-box-c {
        position: absolute;
        left: 0;
        right: 0;
        top: 5%;
        height: 92%;
        margin: auto;
        background: #fff;
        border: 1px solid #999;
        border-radius: 4px;
    }
    #editor-dom-container .print-dialog-c .print-dialog-box-body-c {
        height: 100%;
        display: inline-block;
        width: calc(100% - 320px);
    }
    .print-dialog-box-body-c #curPageBar .jumpPage-side {
        display: inline-block;
        width: 60px;
        min-width: 60px;
        border-radius: 4px;
        padding: 0px 6px;
        font-size: 21px;
        color: #ACB4C1;
        background: #DDDDE3;
        vertical-align: middle;
        text-align: center;
    }
    .print-dialog-box-body-c #curPageBar * {
        margin: 0;
        display: inline-block;
    }
    .print-dialog-box-body-c #curPageBar .jumpPage-side svg  {
        user-select: none;
        width: 16px;
        height: 16px;
        margin: 0;
    }
    .print-dialog-box-body-c #curPageBar .jumpPage-side svg path  {
        pointer-event: none;
    }
    .print-dialog-box-body-c #curPageBar .jumpPage-side svg.activeLabel:hover  {
        cursor: pointer;
        filter: saturate(70);
    }
    .print-dialog-box-body-c #curPageBar .jumpPage-center {
        margin: 0px auto;
        font-size: 14px;
        display: inline-block;
        width: calc(100% - 154px);
        min-width: 200px;
        text-align: center;
        font-size: 14px;
        color: #54627B;
        line-height: 19.6px;
    }
    .print-dialog-box-body-c input#singlePage {
        width: 30px;
        height: 24px;
        background: #FFFFFF;
        align-items: center;
        border: 1px solid #DDDDE3;
        box-sizing: border-box;
        margin-right: 4px;
    }
    #editor-dom-container .print-dialog-c p {
        margin: 0;
        padding: 0;
    }
    #editor-dom-container .print-dialog-c .print-dialog-box-side {
        display: inline-block;
        width: 300px;
        vertical-align: top;
        padding: 10px 0 0 0;
        border: 1px solid #e7e9eb;
        height: 100%;
        position: relative;
        overflow: hidden;
    }
    #editor-dom-container .print-dialog-c .print-dialog-box-side p {
        margin-bottom: 10px;
    }
    #editor-dom-container .print-dialog-c .print-dialog-box-side .cprint-header{
        text-align: left;
        padding: 10px;
        background-color: #F6F7F9;
        margin: 15px 10px !important;
    }
    #editor-dom-container .print-dialog-c .print-dialog-box-side #printer-select{
        width: 179px;
        height: 32px;
        margin-left: 25px;
        position: relative;
        bottom: 1px;
    }
    #editor-dom-container .print-dialog-c .left-part {
        margin: 0 0 0 20px;
    }
    #editor-dom-container .print-dialog-c .left-part, #editor-dom-container .print-dialog-c .right-part {
        display: inline-block;
    }
    #editor-dom-container .print-dialog-c .left-part {
        vertical-align: top;
        width: 72px;
    }
    #editor-dom-container .print-dialog-c .split-container {
        // margin: 10px 0;
    }
    #editor-dom-container .print-dialog-c .right-part {
        width: 200px;
    }
    #editor-dom-container .print-dialog-c .right-part-more-configs {
        padding-left: 7px;
    }
    #editor-dom-container .print-dialog-c .right-part #specific-page {
        width: 116px;
        height: 32px;
        margin-left: 10px;
    }
    #editor-dom-container .print-dialog-c .print-dialog-box-side #print-count-number {
        width: 100px;
        height: 32px;
        margin-left: 9px;
    }
    #editor-dom-container .print-dialog-c .print-dialog-box-side .btns {
        text-align: center;
        padding: 10px;
        position: absolute;
        border-top: 1px solid #e7e9eb;
        bottom: 0;
        width: 100%;
    }
    #editor-dom-container .print-dialog-c .print-dialog-box-side .btns .c-button {
        display: inline-block;
        line-height: 1;
        white-space: nowrap;
        cursor: pointer;
        background: #fff;
        border: 1px solid #d5d4dc;
        color: #606266;
        -webkit-appearance: none;
        text-align: center;
        box-sizing: border-box;
        outline: none;
        margin: 0;
        transition: 0.1s;
        font-weight: 600;
        font-family: 'PingFang SC', 'Microsoft YaHei', 'Microsoft Jhenghei', sans-serif;
        -moz-user-select: none;
        -webkit-user-select: none;
        -ms-user-select: none;
        padding: 8px 25px;
        font-size: 14px;
        border-radius: 2px;
    }

    #editor-dom-container .print-dialog-c .print-dialog-box-side .btns .print {
        color: #fff;
        background-color: #009fff;
        border-color: #009fff;
    }
    #editor-dom-container .print-dialog-c .print-dialog-box-side .c-checkbox {
        margin-left: 15px;
    }
    #editor-dom-container .print-dialog-c .print-dialog-box-side .cont-startpage-hf-text {
        font-size: 12px;
    }
    #editor-dom-container .print-dialog-c .print-dialog-box-side .cont-startpage-hf-text-2 {
        position: relative;
        bottom: 2px;
    }
    .print-dialog-box-side .hidden {
        display: none;
    }
    #editor-dom-container .print-dialog-c .print-dialog-box-side .print-box {
        border-bottom: 1px solid #e7e9eb;
        padding-bottom: 10px;
        padding-left: 16px;
        position: relative;
    }

    #editor-dom-container .print-dialog-c .print-dialog-box-side .print-box ~ p {
        margin: 15px 10px 15px 20px;
    }
    #editor-dom-container .print-dialog-c .print-dialog-box-side .dim-text {
        color: #54627B;
    }
    `;
}
