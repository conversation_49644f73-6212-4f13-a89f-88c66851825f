const fs = require('fs');
const path = require('path');
const curPath = './src/model/core/';
const obj = require(path.resolve(__dirname, curPath + 'TextMeasureSize1.js'));
obj.getCommon()
obj.initMeasureCaches();
let caches = obj.getCaches();
let texts = {};
let items = {};

function forEach() {
    let count = 0;
    let datas = {};
    for (const [size, cache] of caches) {
        const obj = items[size] = [];
        const keys = {};
        for (const [mykey, item] of cache) {
            let key = mykey;
            // if (['\'', '"', '\\'].includes(key)) {
            //     key = '\\' + key;
            // }
            const actKey = item.width + '' + item.height;
            let data = datas[actKey];
            let pushKey;
            if (!data) {
                let textKey = count++;
                texts[textKey] = [item.width, item.height, item.type];
                datas[actKey] = textKey;
                data = textKey;
                pushKey = data;
            } else {
                pushKey = data;
            }
            if (!keys[pushKey]) {
                keys[pushKey] = [];
            }

            keys[pushKey].push(key);
        }
        for (const arrKey in keys) {
            obj.push(`[%%${keys[arrKey].join("%%,%%")}%%].map(i=> [i,${texts[arrKey]}])`);
        }
    }
}
forEach();
let content = '// 请不要直接修改TextMeasureSize.js， 修改完请在终端运行node setTextCache.js即可\n';
content += 'export const CACHES = new Map(); \n';
content += `export function initMeasureCaches() {
    const items = ${JSON.stringify(items).replace(/"\[/g,'[').replace(/%%([\s\S]{1,2})%%/g, (a, b) => {
        let key = b;
        if (['\''].includes(b)) {
            key = '\\' + b;
        }
        return `'${key}'`;
    }).replace(/\)"/g,')')};

    for (const size in items) {
        const arrs = [];
        items[size].forEach((item) => {
            const arr = item[0];
            const obj = {width: arr[1], height: arr[2], type: arr[3]};
            item.forEach((i) => {
                arrs.push([i[0], obj]);
            });
        });
        CACHES.set(size, new Map(arrs));
    }
}
`;

// console.log(JSON.stringify(items));
// console.log(JSON.stringify(texts));
fs.writeFile(curPath + 'TextMeasureSize.js', content, function(err) {
    if (err) {
        console.log(err);
        return;
    }
    console.log('save success!!')
});

// obj.clearCaches();
// obj.getCommon();
// obj.macOs();
// caches = obj.getCaches();
// texts = {};
// items = {};

// forEach();

// content = '// 请不要直接修改TextMeasureSize.js， 修改完请在终端运行node setTextCache.js即可\n';
// content += `import {CACHES} from './TextMeasureSize';\n`;
// content += `export function initIOSCaches() {
//     CACHES.clear();

//     const items = ${JSON.stringify(items).replace(/"\[/g,'[').replace(/%%([\s\S]{1,2})%%/g, (a, b) => {
//         let key = b;
//         if (['\''].includes(b)) {
//             key = '\\' + b;
//         }
//         return `'${key}'`;
//     }).replace(/\)"/g,')')};

//     for (const size in items) {
//         const arrs = [];
//         items[size].forEach((item) => {
//             const arr = item[0];
//             const obj = {width: arr[1], height: arr[2], type: arr[3]};
//             item.forEach((i) => {
//                 arrs.push([i[0], obj]);
//             });
//         });
//         CACHES.set(size, new Map(arrs));
//     }
// }
// `;

// fs.writeFile(curPath + 'TextMeasureSizeForIOS.js', content, function(err) {
//     if (err) {
//         console.log(err);
//         return;
//     }
//     console.log('save ios success!!')
// });
