import { INISCellGridLine, IParaButtonProp } from './../common/commonDefines';
import { SPELL_CHECK } from '@/common/Spellcheck';
// import { isMacOs } from '@/common/commonMethods';
import type { IDocumentProperty } from './DocumentProperty';
import Document, { IWavyUnderlineOptions } from './core/Document';
import { PageProperty } from './StyleProperty';
import type { IDocumentParagraph, IParaProperty } from './ParaProperty';
import type {ICursorProperty} from './CursorProperty';
import MouseEventHandler, {KeyBoardEvent} from '../common/MouseEventHandler';
import TextProperty, { TextVertAlign, FontStyleType, TextDecorationLineType,
    FontWeightType } from './core/TextProperty';
import Selection from './core/Selection';
import ParaLine from './core/Paragraph/ParaLine';
import { DEFAULT_TEXT_PROPERTY, DEFAULT_PARAGRAPH_PROPERTY,
  EquationType, type INewControlProperty, SETTINGS_DEFAULT, ResultType,
  type ICommentProperty, type ICustomProps, type IRevisionSetting,
  type IRevisionChange, type ITableProperty, HeaderFooterType, type IPageNumProperty, ViewModeType,
  type IEditorBackground,
  EditModeType,
  type ITableCellFormulaPar,
  type ITableCellProperty,
  type ICustomFormatDateProps,
  type ITableCellContentProps,
  NISDateBoxFormat,
  type INISRowProperty,
  type IImageFlags,
  CleanModeType,
  ClearHeaderFooterType,
  type INumberingProperty,
  type INum,
  NISTableCellType,
  NISSelectType,
  type IFixedCellType,
  type ImageMediaType,
  type IExternalDataProperty
} from '../../src/common/commonDefines';
import type { IDocumentTable } from './TableProperty';
import DocumentContentElementBase from './core/DocumentContentElementBase';
import ParaDrawing, { type IParaDrawingProp } from './core/Paragraph/ParaDrawing';
import type { ILimits } from './core/DocumentPage';
import { GraphicObjects } from './core/GraphicObjects/GraphicObjects';
import { CHeaderFooterController } from './core/HeaderFooter';
import { SectionPageMargins, SectionPageSize, SectionIndex } from './core/Sections';
import { NewControl } from './core/NewControl/NewControl';
import { ParaElementType, ParagraphContentPos } from './core/Paragraph/ParagraphContent';
import { idCounter } from './core/util';
import {logger} from '../common/log/Logger';
import {Comment} from './core/Comment/Comment';
import type { IDocumentRegion } from './RegionProperty';
import { Region } from './core/Region';
import { SectionProperty } from './core/SectionProperty';
import { getDocumentCoreRecorder, getDocumentCoreRecordReplayState } from './DocumentCoreRecordReplay';
import type { IDocContent } from '../common/DocContent';
import { initMeasureCaches } from './core/TextMeasureSize';
import { TableCell } from './core/Table/TableCell';
import { TableBase } from './core/TableBase';
/* IFTRUE_WATER */
import type { IMarkRect, IMarkBounding } from '@/common/MarkFactory';
/* FITRUE_WATER */
import { generateDocx, generateExcelByTable } from '@hz-editor/plugins';
import { ICommentStatusInfo } from './core/Comment/CommentManager';
import ParaButton from './core/Paragraph/ParaButton';
import { WavyUnderlineManager } from './core/WavyUnderline/WavyUnderlineManager';
// import {parse as circularParse, stringify as circularStringify} from 'flatted/esm';
// let iosCall;
// (function () {
//   if (!isMacOs()) {
//     return;
//   }
//   import('./core/TextMeasureSizeForIOS').then((res) => {
//     iosCall = true;
//     res.initIOSCaches();
//   });
// })();

export interface IRefreshPageData {
  bPageContent?: boolean;
  ids?: number[];
  content?: any; // IDocumentTable、IDocumentParagraph、IDrawSelectionsCell
  type?: number; // 1：paragraph；2：table
  bNextPage?: boolean;
  bPrevPage?: boolean;
  table?: any;
  bRefreshAll?: boolean;
  bFixedContent?: boolean;
}

export interface IDocumentData {
  pageProperty: PageProperty;
  textProperty: TextProperty;
}

/**
 * 选择区域: 段落
 */
export interface ISelectionsPara {
    startLine: number;
    startLineX: number;
    startLineWidth: number;
    endLine: number;
    endLineX: number;
    endLineWidth: number;
    // para: IDrawSelectionsPara,
    // cell: IDrawSelectionsCell,
}

/**
 * 选择区域边界
 */
export interface IDrawSelectionBounds {
    [x: string]: any;
    // start: {startLine: number, endLine: number},
    // end: {startLine: number, endLine: number},
    direction: number;
    lines: IDrawSelectionsLine[];
    cells: IDrawSelectionsCell[];
    fixedType?: IFixedCellType;
    // startPageIndex: number
}

/**
 * 选择区域绘制的行
 */
export interface IDrawSelectionsLine {
    line?: ParaLine;
    x: number;
    y?: number;
    width: number;
    height?: number;
    paragraph?: number;
    pageIndex?: number;
    bStart?: boolean;
    bEnd?: boolean;
}

/**
 * 选择区域绘制的cells
 */
export interface IDrawSelectionsCell {
    bHeader?: boolean;
    contentHeight?: number;
    id: number;
    x: number;
    y: number;
    height: number;
    width: number;
    table: number;
    pageIndex: number;
    content?: IDocumentContent;  // 表格内容
    cellName?: string;
    cellBackground?: string;   // 表格/单元格背景填充
    bFixedHeader?: boolean;
    yEnd?: number;
    cellId?: number;
    cellBounds?: any;
    bMask?: boolean; // 是否需要遮罩层
    bFixed?: boolean;
    fixedType?: number;
    bValidData?: boolean; // 是否符合校验
    bWarnBorder?: boolean;
    index?: number; // 单元格索引
    gridLine?: INISCellGridLine;
}

/**
 * 文本属性
 */
export interface IDefaultTextProperty {
  'fontSize': number;
  'fontFamily': string;
  'fontWeight': string;
  'fontStyle': string;
  'textDecorationLine': string;
  'color': string;
  'backgroundColor': string;
}

/**
 * 段落属性
 */
export interface IDefaultParaProperty {
  'alignment': number;
  'paraSpacing': number;
}

export interface ISettingsInfoDefaults {
  'application': string;
  'version': number;
  'protectMode': number;
}

export interface ISettingsSavedInfo {
  createdTime: string; // 首次创建时间
  createdBy: string; // 首次创建于~
  lastButOne: string; // 倒数第二次保存时间
  lastSavedT: string; // 最后一次保存时间
}

/**
 * 光标位置：UI显示
 */
// export interface ICursorPos {
//     pageNum: number;
//     x: number; // + item.widthVisible,
//     y1: number; //
//     y2: number;
// }

export interface IDrawNewControlBounds {
  bounds: IDrawSelectionsLine[];
  newControlName: string;
  color: string;
}

export interface IDrawTableNewBorder {
  bColumn: boolean;
  x: number;
  y: number;
  height: number;
  width: number;
  pageIndex: number;
}
// interface Command {
// }

/**
 * 文档渲染显示内容
 */
export interface IDocumentContent {
    regions?: IDocumentRegion[];
    paragraphs: IDocumentParagraph[];  // 段落
    tables: IDocumentTable[];  // 表格
    images?: ParaDrawing[];
    bTop?: boolean;
    x?: number;
    y?: number;
    width?: number;
    height?: number;
    rects?: {lines: IDrawSelectionsLine[], name: string}[]
}

const recorder = getDocumentCoreRecorder();
function DocumentCoreMethodRecorder() {
  return function documentCoreMethodRecorderDecorator(target: Object, propKey: string,
                                                      descriptor: PropertyDescriptor): void {
    const method: Function = descriptor.value;
    descriptor.value = function(...args: any[]) {
      const action = {
        method: propKey,
        timestamp: Date.now(),
        args
      };
      recorder.record(action);
      return method.apply(this, args);
    };
  };
}

export class DocumentCore {

  public static create(data: IDocumentData): DocumentCore {
    // todo

    return ({}) as DocumentCore;
  }
  private title: string;
  private id: number;
  private document: Document;
  private _prevAdminModel: boolean;
  private _prevProtectModel: boolean;

  constructor(data?: IDocumentData, bAddTestText: boolean = true, id?: number) {
    this.id = id === undefined ? idCounter.getNewId() : id;
    // initCaches();
    // if (iosCall !== true) {
    initMeasureCaches();
    // }
    this.document = new Document(data);
    this.document.id = this.id;
    this.document.testDocument(undefined, bAddTestText);
  }

  public setShowCascadeHiddenBackground(bHiddenBackground: boolean): boolean {
    return this.document.setShowCascadeHiddenBackground(bHiddenBackground);
  }

    /**
     * 设置页面属性
     * @param pageProperty
     */
  @DocumentCoreMethodRecorder()
  public setPageProperty(pageProperty?: PageProperty): number {
    if (this.document.isProtectedMode()) {
      return ResultType.ProtectedMode;
    }
    const date = new Date();
    const result = this.document.setPageProperty(pageProperty);
    logger.devlog({id: this.id, result,
      startTime: date, args: pageProperty, name: 'setPageProperty'});
    return result;
  }

  public getCurrentId(): number {
    return this.id;
  }

    /**
     * 设置批注用户名
     * @param sName 用户名
     */
    public setCommentAuthor(sName: string): number {
        return this.document.setCommentAuthor(sName);
    }

    public getCommentStatusInfo(): ICommentStatusInfo {
        return this.document.getCommentStatusInfo();
    }

    // 在 DocumentCore.ts 中添加方法：
  public getPositionByXY(x: number, y: number): ParagraphContentPos | null {
    //return this.document.getPositionByXY(x, y);
    return null;
  }

  @DocumentCoreMethodRecorder()
  public showCommentByUserName(bShow: boolean, userName: string): number {
    if (this.document.isProtectedMode()) {
      return ResultType.ProtectedMode;
    }
    return this.document.showCommentByUserName(bShow, userName);
  }

  @DocumentCoreMethodRecorder()
  public showCommentByName(name: string, bValue: boolean): number {
    if (this.document.isProtectedMode()) {
      return ResultType.ProtectedMode;
    }
    return this.document.showCommentByName(name, bValue);
  }

  @DocumentCoreMethodRecorder()
  public deleteCommentByName(name: string, id?: number): boolean {
    if (this.document.isProtectedMode() ||
          (this.document.isStrictMode2())) {
      return false;
    }
    return this.document.deleteCommentByName(name, id);
  }

  @DocumentCoreMethodRecorder()
  public addCommentByCursor(props: ICommentProperty): string {
    if (this.document.isProtectedMode() ||
          (this.document.isStrictMode2())) {
      return ResultType.StringEmpty;
    }
    return this.document.addCommentByCursor(props);
  }

  @DocumentCoreMethodRecorder()
  public addCommentReply(name: string, content: string, userName?: string): boolean {
    return this.document.addCommentReply(name, content, userName);
  }

  public isCommentInUnInsertNewControl(): boolean {
    return this.document.isCommentInUnInsertNewControl();
  }

  /**
   * 是否显示批注面板
   * @param bShow 是否显示
   */
  public showCommentPanel(bShow: boolean): number {
      return this.document.showCommentPanel(bShow);
  }

  @DocumentCoreMethodRecorder()
  public updateComment(
    name: string,
    info: {
        content?: string;
        id?: number;
        isSolved?: boolean;
    }
  ): boolean {
    return this.document.updateComment(name, info);
  }

  public getCommentByCursor(): Comment {
    return this.document.getCommentByCursor();
  }

  public activeComment(name?: string): number {
    return this.document.activeComment(name);
  }

  public getAllComments(pageIndex?: number): Comment[] {
    return this.document.getAllComments(pageIndex);
  }

  public getAllCommentCount(): number {
    return this.document.getAllCommentCount();
  }

  @DocumentCoreMethodRecorder()
  public setCommentModify(propName: string, bValue: boolean, name?: string): number {
    if (this.document.isProtectedMode() ||
        (this.document.isStrictMode2())) {
      return ResultType.ProtectedMode;
    }
    return this.document.setCommentModify(propName, bValue, name);
  }

  @DocumentCoreMethodRecorder()
  public setCommentModifyByUserName(propName: string, bValue: boolean, userName: string): number {
    if (this.document.isProtectedMode() ||
          (this.document.isStrictMode2())) {
      return ResultType.ProtectedMode;
    }
    return this.document.setCommentModifyByUserName(propName, bValue, userName);
  }

  @DocumentCoreMethodRecorder()
  public moveCursorInComment(name: string): boolean {
    return this.document.jumpInComment(name);
  }

  @DocumentCoreMethodRecorder()
  public jumpInRegion(name: string): number {
    return this.document.jumpInRegion(name);
  }

  public changeRegionLoaded(name: string, type: string): number {
    return this.document.changeRegionLoaded(name, type);
  }

  public dbClickChangeRegionLoaded(): boolean {
    return this.document.dbClickChangeRegionLoaded();
  }

  public getRegionPropsByName(name: string): any {
    return this.document.getRegionPropsByName(name);
  }

  @DocumentCoreMethodRecorder()
  public deleteRegion(name: string): number {
    return this.document.deleteRegion(name);
  }

  @DocumentCoreMethodRecorder()
  public getStructsNameListFromSelectedArea(): string {
    return this.document.getStructsNameListFromSelectedArea();
  }

  public getCommentByName(name: string): Comment {
    return this.document.getCommentByName(name);
  }

  @DocumentCoreMethodRecorder()
  public jumpInStruct(name: string, nMark: number): number {
    return this.document.jumpInStruct(name, nMark);
  }

  /**
   * 获取页面属性
   */
  public getPageProperty(): PageProperty {
    return this.document.getPageProperty();
  }

  @DocumentCoreMethodRecorder()
  public browseTemplet(nType: number, nProtected: number): number {
    const result = this.document.browseTemplet(nType, nProtected);
    
    // 当nProtected为1时，额外设置文档级别保护，等同于protectDoc(true)
    if (nProtected !== undefined) {
      if (nProtected === 1) {
        this.protectDoc(true);
      }else if(nProtected === 0){
        this.protectDoc(false);
      }
    }
    
    return result;
  }

  /**
   * 设置段落属性
   */
  @DocumentCoreMethodRecorder()
  public setParagraphProperty(paragraphProperty: IParaProperty): number {
      if (this.document.isProtectedMode() ||
            (this.document.isStrictMode2())) {
        return ResultType.ProtectedMode;
      }
      return this.document.setParagraphProperty(paragraphProperty);
  }

  @DocumentCoreMethodRecorder()
  public selectOneRegion(sName: string, bOnlyContent?: boolean): number {
    return this.document.selectOneRegion(sName, bOnlyContent);
  }

  @DocumentCoreMethodRecorder()
  public setParagraphProperty1(paragraphProperty: IParaProperty): number {
    if (this.document.isProtectedMode() ||
          (this.document.isStrictMode2())) {
      return ResultType.ProtectedMode;
    }
    const date = new Date();
    const result = this.document.setParagraphProperty1(paragraphProperty);
    logger.devlog({id: this.id, result,
      startTime: date, args: paragraphProperty, name: 'setParagraphProperty1'});
    return result;
  }

  public setWestCharBreakAttribute(nType: number, bWordWrap: boolean): number {
    const bSelectAll = (1 === nType ? false : true);
    if ( bSelectAll) {
      this.selectAll();
    }

    const result = this.setParagraphProperty1({bWordWrap});

    if ( bSelectAll) {
      this.removeSelection();
    }
    return result;
  }

  public getRefreshPageContent(pageIndex: number, content: IDocumentContent, options?: any): IRefreshPageData {
    return this.document.getRefreshPageContent(pageIndex, content, options);
  }

  public getNextRefreshPageContent(pageIndex: number, option: IRefreshPageData): IRefreshPageData {
    return this.document.getNextRefreshPageContent(pageIndex, option);
  }

  /**
   * 获取段落属性
   */
  public getParagraphProperty = () => {
      return this.document.getParagraphProperty();
  }

  public getCurLineHeight(): number {
    return this.document.getCurLineHeight();
  }
  /**
   * 是否在级联管理视图下显示背景色
   */
  public isShowCascadeBackground(): boolean {
    return this.document.isShowCascadeBackground();
  }
  /**
   * 设置 是否在级联管理视图下显示背景色
   */
  public setShowCascadeBackground(bShowCascadeBackground: boolean): boolean {
    return this.document.setShowCascadeBackground(bShowCascadeBackground);
  }
  /**
   * 获取当前段落
   */
  // getCurrentParagraph = () => {
  //   return this.document.getCurrentParagraph();
  // }

  /**
   *  获取光标位置
   */
  // getCurPos = (): CursorPos => {
  //   return this.document.getCursorPosXY(true);
  // }

  /**
   * 鼠标down
   */
  @DocumentCoreMethodRecorder()
  public mouseButtonDown(mouseEvent: MouseEventHandler, pageIndex: number, pointX: number, pointY: number): void {
    return this.document.mouseButtonDown(mouseEvent, pageIndex, pointX, pointY);
  }

  /**
   * 鼠标move
   */
  @DocumentCoreMethodRecorder()
  public mouseButtonMove(mouseEvent: MouseEventHandler, pageIndex: number, pointX: number, pointY: number): void {
    return this.document.mouseButtonMove(mouseEvent, pageIndex, pointX, pointY);
  }

  /**
   * 鼠标up
   */
  @DocumentCoreMethodRecorder()
  public mouseButtonUp(mouseEvent: MouseEventHandler, pageIndex: number, pointX: number, pointY: number): void {
    return this.document.mouseButtonUp(mouseEvent, pageIndex, pointX, pointY);
  }

  public getSelectedParaPro(): IParaProperty {
    const date = new Date();
    const result = this.document.getSelectedParaPro();
    logger.devlog({id: this.id, result: result ? ResultType.Success : ResultType.Failure,
      startTime: date, args: arguments, name: 'getSelectedParaPro'});
    return result;
  }

  /**
   * 清除选区
   */
  @DocumentCoreMethodRecorder()
  public clearSelection(): void {
    return this.document.removeSelection();
  }

  public getSelectedContent(): DocumentContentElementBase[] {
    // const date = new Date();
    const result = this.document.getSelectedContent();
    // logger.devlog({id: this.id, result: result ? ResultType.Success : ResultType.Failure,
    //   startTime: date, args: arguments, name: 'getSelectedContent'});
    return result;
  }

  public canCopy(): boolean {
    return this.document.canCopy();
  }

  /**
   * 获取选择区域将保存的内容(与getSelectedContent()类似，但结构化元素选择时处理有不同)
   */
  public getSelectedContentToSave(type?: number): DocumentContentElementBase[] {
    // const date = new Date();
    this.document.setSelectedContentFlag(true);
    const result = this.document.getSelectedContentToSave(type);
    this.document.setSelectedContentFlag(false);
    // logger.devlog({id: this.id, result: result ? ResultType.Success : ResultType.Failure,
    //   startTime: date, args: arguments, name: 'getSelectedContent'});
    return result;
  }

  public getRegionModifyFlag(sRegionName: string): boolean {
    return this.document.getRegionModifyFlag(sRegionName);
  }

  public getAllRegionsByModifyFlag(nOnlyFirstRegions: number, nModifyFlag: number): string {
    return this.document.getAllRegionsByModifyFlag(nOnlyFirstRegions, nModifyFlag);
  }

  public cleanAllRegionsModifyFlag(): number {
    return this.document.cleanAllRegionsModifyFlag();
  }

  /**
   * 返回所有内容
   */
  public getContent(): DocumentContentElementBase[] {
    return this.document.getContent();
  }

  @DocumentCoreMethodRecorder()
  public selectAll(): number {
    return this.document.selectAll();
  }

  @DocumentCoreMethodRecorder()
  public getAllContents(): DocumentContentElementBase[] {
    return this.document.getAllContents();
  }

  public getCurPageIndex(): number {
    return this.document.getCurPage();
  }

  @DocumentCoreMethodRecorder()
  public setProtectHeaderFooter(bProtect: boolean): number {
    return this.document.setProtectHeaderFooter(bProtect);
  }

  public getShowHeader(): boolean {
    return this.document.getShowHeader();
  }

  public getShowFooter(): boolean {
    return this.document.getShowFooter();
  }

  @DocumentCoreMethodRecorder()
  public setShowHeader(value: boolean): number {
    return this.document.setShowHeader(value);
  }

  @DocumentCoreMethodRecorder()
  public addPageNum(pageNumProperty?: IPageNumProperty): void {
    return this.document.addPageNum(pageNumProperty);
  }

  @DocumentCoreMethodRecorder()
  public setEditorBackground(watermarkProperty?: IEditorBackground): void {
    return this.document.setEditorBackground(watermarkProperty);
  }

  public getEditorBackground(): IEditorBackground {
    return this.document.getEditorBackground();
  }

  @DocumentCoreMethodRecorder()
  public addPageNumByText(text: string, nParaStyle: number): number {
    return this.document.addPageNumByText(text, nParaStyle);
  }

  @DocumentCoreMethodRecorder()
  public setShowFooter(value: boolean): number {
    return this.document.setShowFooter(value);
  }

  @DocumentCoreMethodRecorder()
  public deleteFooter(bDelete: boolean): number {
    return this.document.toggleShowHeaderFooter(HeaderFooterType.Footer, !bDelete);
  }

  @DocumentCoreMethodRecorder()
  public jumpToHeader(): number {
    return this.document.jumpToHeader();
  }

  @DocumentCoreMethodRecorder()
  public deleteHeader(bDelete: boolean): number {
    return this.document.toggleShowHeaderFooter(HeaderFooterType.Header, !bDelete);
  }

  @DocumentCoreMethodRecorder()
  public clearHeaderFooterContent(clearType: ClearHeaderFooterType): number {
    return this.document.clearHeaderFooterContent(clearType);
  }

  public getTableCellIdsByIndex(index: number): number[] {
    return this.document.getTableCellIdsByIndex(index);
  }

  public getPageCount(): number {
    const pages = this.document.getPages();
    if (!pages) {
      return 0;
    }

    return pages.length;
  }

  @DocumentCoreMethodRecorder()
  public putCellContentByArray(sName: string, texts: any): number {
    return this.document.putCellContentByArray(sName, texts);
  }

  /**
   *  获取光标选择区域
   */
  public getSelectionBounds(mouseEvent?: MouseEventHandler, pageIndex?: number): IDrawSelectionBounds {
    return this.document.getSelectionBounds(mouseEvent, pageIndex);
  }

  @DocumentCoreMethodRecorder()
  public selectArea(sStartPos: string, sEndPos: string): number {
    return this.document.selectArea(sStartPos, sEndPos);
  }

  @DocumentCoreMethodRecorder()
  public selectAreaByDirection(nCharNumber: number, direction: number): number {
    return this.document.selectAreaByDirection(nCharNumber, direction);
  }

    /**
     * 设置文本属性
     * @param prop
     */
  @DocumentCoreMethodRecorder()
  public setTextProperty(prop: TextProperty): number {
    if (this.document.isProtectedMode()) {
      return ResultType.ProtectedMode;
    }
    const date = new Date();
    const result = this.document.setTextProperty(prop);
    logger.devlog({id: this.id, result,
      startTime: date, args: {prop}, name: 'setTextProperty'});
    return result;
  }

  /**
   * 设置文本属性: 字体
   */
  @DocumentCoreMethodRecorder()
  public setTextFontFamily( prop: string ): number {
    if (this.document.isProtectedMode()) {
      return ResultType.ProtectedMode;
    }
    const date = new Date();
    const result = this.document.setTextFontFamily(prop);
    logger.devlog({id: this.id, result,
      startTime: date, args: {prop}, name: 'setTextFontFamily'});
    return result;
  }

  /**
   * 设置文本斜体
   */
  @DocumentCoreMethodRecorder()
  public setTextItalic(nVal?: FontStyleType): number {
    if (this.document.isProtectedMode()) {
      return ResultType.ProtectedMode;
    }
    const date = new Date();
    const result = this.document.setTextItalic(nVal);
    logger.devlog({id: this.id, result,
      startTime: date, args: {prop: nVal}, name: 'setTextItalic'});
    return result;
  }

   /**
   * 获取文本属性
   */
   @DocumentCoreMethodRecorder()
   public getFontProp(): string {
     // 获取当前的粗体状态
     let Boldnum: number;

     const textPr = this.document.getTextProps(false);
     // 创建一个对象来存储成员变量的值
     const jsonObject: { [key: string]: any } = {};

     // 通过访问类的成员变量，将它们的值赋给 jsonObject
     jsonObject.FontSize = textPr.fontSize;
     jsonObject.FontFamily = textPr.fontFamily;
     jsonObject.Color = textPr.color;
     jsonObject.Background = textPr.backgroundColor;
     jsonObject.Bold = textPr.fontWeight;
     jsonObject.Underline = textPr.textDecorationLine;
     jsonObject.Italic = textPr.fontStyle;
     jsonObject.Sub = 0;
     jsonObject.Super = 0;
     if( textPr.vertAlign === 1)
       jsonObject.Sub = 1;
     if( textPr.vertAlign === 2)
       jsonObject.Super = 1;
    
     // 转换为 JSON 字符串（如果需要）
     const jsonString = JSON.stringify(jsonObject);
     return jsonString;
   }

  /**
   * 设置文本斜体
   */
  @DocumentCoreMethodRecorder()
  public setTextUnderline(nVal?: TextDecorationLineType): number {
    if (this.document.isProtectedMode()) {
      return ResultType.ProtectedMode;
    }
    const date = new Date();
    const result = this.document.setTextUnderline(nVal);
    logger.devlog({id: this.id, result,
      startTime: date, args: {prop: nVal}, name: 'setTextUnderline'});
    return result;
  }

  @DocumentCoreMethodRecorder()
  public setTextColor(prop: string): number {
    if (this.document.isProtectedMode()) {
      return ResultType.ProtectedMode;
    }
    const date = new Date();
    const result = this.document.setTextColor(prop);
    logger.devlog({id: this.id, result,
      startTime: date, args: {prop}, name: 'setTextColor'});
    return result;
  }

  @DocumentCoreMethodRecorder()
  public setTextBackgrounColor(prop: string): number {
    // console.log(prop)
    if (this.document.isProtectedMode()) {
      return ResultType.ProtectedMode;
    }
    const date = new Date();
    const result = this.document.setTextBackgrounColor(prop);
    logger.devlog({id: this.id, result,
      startTime: date, args: {prop}, name: 'setTextBackgrounColor'});
    return result;
  }

  @DocumentCoreMethodRecorder()
  public setTextSubscript(value?: TextVertAlign): number {
    if (this.document.isProtectedMode()) {
      return ResultType.ProtectedMode;
    }
    const date = new Date();
    const result = this.document.setTextVertAlign(TextVertAlign.Sub, value);
    logger.devlog({id: this.id, result,
      startTime: date, args: {prop: value}, name: 'setTextSubscript'});
    return result;
  }

  @DocumentCoreMethodRecorder()
  public addFixedHeightPara(height: number, nPos?: number, action?: string): void {
    this.document.addFixedHeightPara(height, nPos, action);
  }

  @DocumentCoreMethodRecorder()
  public setTextSuperscript(value?: TextVertAlign): number {
    if (this.document.isProtectedMode()) {
      return ResultType.ProtectedMode;
    }
    const date = new Date();
    const result = this.document.setTextVertAlign(TextVertAlign.Super, value);
    logger.devlog({id: this.id, result,
      startTime: date, args: {prop: value}, name: 'setTextSuperscript'});
    return result;
  }

  /**
   * 设置文本粗体
   */
  // public setTextBold =(): void =>{
  //   return this.document.setTextBold();
  // }

  /**
   * 设置文本属性: 字号
   */
  @DocumentCoreMethodRecorder()
  public setTextFontSize( prop: number ): number {
    if (this.document.isProtectedMode()) {
      return ResultType.ProtectedMode;
    }
    const date = new Date();
    const result = this.document.setTextFontSize(prop);
    logger.devlog({id: this.id, result,
      startTime: date, args: {prop}, name: 'setTextFontSize'});
    return result;
  }

  @DocumentCoreMethodRecorder()
  public setTextBold(value?: FontWeightType): number {
    if (this.document.isProtectedMode()) {
      return ResultType.ProtectedMode;
    }
    const date = new Date();
    const result = this.document.setTextBold(value);
    logger.devlog({id: this.id, result,
      startTime: date, args: {prop: value}, name: 'setTextBold'});
    return result;
  }

  public getTextPropertyByCursor(position: number): TextProperty {
    // todo
    return ({}) as TextProperty;
  }

  public getTextPropertyBySelection(start: number, end: number): TextProperty {
    // todo
    return ({}) as TextProperty;
  }

  /**
   * 设置文本对齐方式
   */
  @DocumentCoreMethodRecorder()
  public setParagraphAlignment(alignment: number): number {
    if (this.document.isProtectedMode() ||
          (this.document.isStrictMode2())) {
      return ResultType.ProtectedMode;
    }
    const date = new Date();
    const result = this.document.setParagraphAlignment(alignment);
    logger.devlog({id: this.id, result,
      startTime: date, args: {prop: alignment}, name: 'setParagraphAlignment'});
    return result;
  }
  /**
   *
   * @param id
   */
  public getContentByPageId(id: number, sectionIndex: SectionIndex = 0): IDocumentContent {
    // todo 返回类型处理
    return this.document.getContentByPageId(id, sectionIndex);
  }

  public getLinesByPageId( pageIndex: number, element: number ): ParaLine[] {
      return this.document.getLinesByPageId(pageIndex, element);
  }

  public getElementStartLineByPageId( pageIndex: number, element: number ): number {
      return this.document.getElementStartLineByPageId(pageIndex, element);
  }

  public getElementEndLineByPageId( pageIndex: number, element: number ): number {
    return this.document.getElementEndLineByPageId(pageIndex, element);
  }

  @DocumentCoreMethodRecorder()
  public setRegionText(name: string, text: string): number {
    return this.document.setRegionText(name, text);
  }

  // public exec(command: Command): void {
  //   // todo
  // }

  @DocumentCoreMethodRecorder()
  public render(): IDocumentProperty {
    const pageProperty = this.document.getPageProperty();
    if (this.isInlineMode()) {
        pageProperty.height = this.document.getLastPageContentBoundY();
    }
    // todo
    return {
      total: this.document.pages.length,
      textProperty: this.document.textProperty,
      pageProperty,
    };
  }

  @DocumentCoreMethodRecorder()
  public renderByRange(start: number, end: number): IDocumentProperty {
    // todo
    return ({}) as IDocumentProperty;
  }

  public toData(): IDocumentData {
    // todo
    return ({}) as IDocumentData;
  }

  @DocumentCoreMethodRecorder()
  public testDocument(data: any): void {
    this.document.testDocument(data);
  }

  /**
   * 获得鼠标正确的位置
   *
   * 首先找到正确的当前页， 如果找不到，说明鼠标点击位置不在page上，则返回null
   * 能找到，则向core请求正确的相对 page位置
   */
  public getCursorPositionBySetPoint(pageId: number, pointX: number, pointY: number): ICursorProperty {
    this.document.moveCursorToXY(pageId, pointX, pointY);
    return this.document.getCursorPosXY();
  }

  public moveCursorToXY(pageId: number, pointX: number, pointY: number): void {
    this.document.moveCursorToXY(pageId, pointX, pointY);
  }

  /**
   * 刷新文档里的所有级联。
   */
  public updateAllConnections(): number {
    return this.document.updateAllConnections();
  }

  public getAllNewControls(): NewControl[] {
    return this.document.getAllNewControls();
  }

  public getAllNewControls2(): NewControl[] {
    return this.document.getAllNewControls2();
  }

  public getAllNewControlNames(): string[] {
    if (this.document.getNewControlManager()) {
      return this.document.getNewControlManager()
                      .getAllNewControlsName();
    }
    return [];
  }

  /**
   * 获取所有嵌套的结构化信息对象（保留物理位置）
   */
  public getAllNestContentProps(params: any): string {
    return this.document.getAllNestContentProps(params);
  }

  /**
   * 设置文字水印
   * @param sText 文字水印
   * @param nMode 1 – 宽松型   2 – 紧凑型
   */
  public setTextWaterMark(sText: string, nMode: number, colorType?: number): number {
    return this.document.setTextWaterMark(sText, nMode, colorType);
  }

  /**
   * 删除文档的文字水印
   */
  public deleteTextWaterMark(): number {
    return this.document.deleteTextWaterMark();
  }

  /**
   * 判断当前文档是否有水印
   */
  public hasTextWaterMark(): boolean {
    return this.document.hasTextWaterMark();
  }

  public getAllCascadeManagers(): any {
    return this.document.getAllCascadeManagers();
  }

  public getElementsByNewControl(sName: string): DocumentContentElementBase[] {
    return this.document.getElementsByNewControl(sName);
  }

    /**
     * 得到当前光标位置
     */
  public getCursorPosition(): ICursorProperty {
    return this.document.getCursorPosXY();
  }

  public getCursorType(): string {
    return this.document.getCursorType();
  }

  public getCursorTypeForPrint(): string {
    return this.document.getCursorType();
  }

  @DocumentCoreMethodRecorder()
  public moveCursorToNewPage(code: number, pageIndex: number, clientY: number): void {
    this.document.moveCursorToNewPage(code, pageIndex, clientY);
  }

  @DocumentCoreMethodRecorder()
  public initCursorPos(): ICursorProperty {
    this.document.moveCursorToStartPos();
    return this.getCursorPosition();
  }

  /**
   * 根据配置是否生成一个新portion 插入其中文本
   */
  @DocumentCoreMethodRecorder()
  public onCompositionStart(): boolean {
    if (this.document.isProtectedMode()) {
      return false;
    }
    return this.document.beginCompositeInput();
  }

  public checkNewControlEvent(eventInfo: any): string {
    return this.document.checkNewControlEvent(eventInfo);
  }

  /**
   * 检查目标元素集合是否均为指定类型，
   * @param names 元素名称集合
   * @param type 指定类型
   * @returns 错误的类型名称集合
   */
  public checkNewControlsType(names: string, type?: number): string[] {
    return this.document.checkNewControlsType(names, type);
  }

  @DocumentCoreMethodRecorder()
  public insertCompositionInput(content: string, bComposition: boolean): boolean {
        if (this.document.isProtectedMode()) {
          return false;
        }
        return this.document.insertCompositionInput(content, bComposition);
  }

  // @DocumentCoreMethodRecorder()
  public insertContent(paras: DocumentContentElementBase []): boolean {
    if (this.document.isProtectedMode()) {
      this.document.getDrawingObjects()
        .resetPasteDrawing();
      this.document.getRegionManager()
        .resetPaste();
      this.document.getButtonManager()
        .clearCaches();
      return false;
    }
    const res = this.document.addPasteContent(paras);
    if (res !== true) {
      this.document.getDrawingObjects()
        .resetPasteDrawing();
      this.document.getRegionManager()
        .resetPaste();
      this.document.getButtonManager()
        .clearCaches();
    }
    return true;
  }

  @DocumentCoreMethodRecorder()
  public insertText(text: string): number {
    if (this.document.isProtectedMode()) {
      return ResultType.ProtectedMode;
    }
    return this.document.insertText(text);
  }

  public getAllCascades(bChecked?: boolean): any {
    return this.document.getAllCascades(bChecked);
  }

  /**
   * 新增分页符
   */
  @DocumentCoreMethodRecorder()
  public addPageBreak(): number {
    if (this.document.isProtectedMode()) {
      return ResultType.ProtectedMode;
    }
    const date = new Date();
    const result = this.document.addPageBreak();
    logger.devlog({id: this.id, result,
      startTime: date, args: arguments, name: 'addPageBreak'});
    return result;
  }

  @DocumentCoreMethodRecorder()
  public onCompositionEnd(): void {
        if (this.document.isProtectedMode()) {
          return;
        }
        this.document.endCompositionInput();
    }

  @DocumentCoreMethodRecorder()
  public onKeyDown( keyEvent: KeyBoardEvent ): void {
        this.document.onKeyDown(keyEvent);
  }

  public initOpenCascades(datas: any[]): void {
    this.document.initOpenCascades(datas);
  }

  public isNotOverNewControl(): boolean {
    return this.document.isNotOverNewControl();
  }

    /**
     * 获取document.selection
     */
  public getDocumentSelection(): Selection {
        return this.document.getDocumentSelection();
    }

  public isSelectionUse(): boolean {
    return this.document.isSelectionUse();
  }

  public isSelecting(): boolean {
    return this.document.isSelecting();
  }

  public isShelteredText(): boolean {
    return this.document.isShelteredText();
  }

  public showShelteredText(bFlag: boolean): number {
    return this.document.showShelteredText(bFlag);
  }

    /**
     * 选择的方向
     * @param bounds
     */
  public getSelectionDirection(): boolean {
        return -1 === this.document.getSelectionDirection() ? true : false;
    }

  public getSelectText(): string {
    return this.document.getSelectText();
  }

    /**
     * 返回所有文本
     * @param needPara 是否需要段落（回车符）
     */
  public getAllText(needPara: boolean = false): string {
    return this.document.getAllText(needPara);
  }

  /**
   * 获取页眉文本内容
   * @returns 页眉的文本内容，如果没有页眉返回空字符串
   */
  public getHeader(): string {
    try {
      // 获取文档对象
      if (!this.document || !this.document.sectionProperty || !this.document.sectionProperty.headerDefault) {
        return '';
      }
      
      // 获取页眉的内容
      const headerContent = this.document.sectionProperty.headerDefault.content;
      if (!headerContent || !headerContent.content) {
        return '';
      }
      
      // 遍历所有段落，获取文本内容
      let headerText = '';
      for (let i = 0; i < headerContent.content.length; i++) {
        const para = headerContent.content[i];
        // 判断是否是 Paragraph 类型
        if (para && para.isParagraph && para.isParagraph()) {
          // 将段落转换为 Paragraph 类型
          const paragraph = para as any; // 使用 any 类型绕过类型检查
          if (paragraph.content && paragraph.content.length) {
            // 遍历段落中的所有文本部分
            for (let j = 0; j < paragraph.content.length; j++) {
              const portion = paragraph.content[j];
              if (portion && typeof portion.getText === 'function') {
                headerText += portion.getText();
              }
            }
            if (i < headerContent.content.length - 1) {
              headerText += '\n';
            }
          }
        }
      }
      
      return headerText;
    } catch (error) {
      console.error('Error getting header text:', error);
      return '';
    }
  }

  /**
   * 获取页脚文本内容
   * @returns 页脚的文本内容，如果没有页脚返回空字符串
   */
  public getFooter(): string {
    try {
      // 获取文档对象
      if (!this.document || !this.document.sectionProperty || !this.document.sectionProperty.footerDefault) {
        return '';
      }
      
      // 获取页脚的内容
      const footerContent = this.document.sectionProperty.footerDefault.content;
      if (!footerContent || !footerContent.content) {
        return '';
      }
      
      // 遍历所有段落，获取文本内容
      let footerText = '';
      for (let i = 0; i < footerContent.content.length; i++) {
        const para = footerContent.content[i];
        // 判断是否是 Paragraph 类型
        if (para && para.isParagraph && para.isParagraph()) {
          // 将段落转换为 Paragraph 类型
          const paragraph = para as any; // 使用 any 类型绕过类型检查
          if (paragraph.content && paragraph.content.length) {
            // 遍历段落中的所有文本部分
            for (let j = 0; j < paragraph.content.length; j++) {
              const portion = paragraph.content[j];
              if (portion && typeof portion.getText === 'function') {
                footerText += portion.getText();
              }
            }
            if (i < footerContent.content.length - 1) {
              footerText += '\n';
            }
          }
        }
      }
      
      return footerText;
    } catch (error) {
      console.error('Error getting footer text:', error);
      return '';
    }
  }

  public changeNewControlType(names: string[], type?: number): number {
    return this.document.changeNewControlType(names, type);
  }

  public getNewControlBegin(sName: string): string {
    return this.document.getNewControlBegin(sName);
  }

  public getNewControlEnd(sName: string): string {
    return this.document.getNewControlEnd(sName);
  }

  @DocumentCoreMethodRecorder()
  public cursorJumpOutOfOneStruct(sName: string, nMark: number): number {
    return this.document.cursorJumpOutOfOneStruct(sName, nMark);
  }

  public setNewControlControllerActive(newControl: NewControl): any {
    return this.document.setNewControlControllerActive(newControl);
  }

  @DocumentCoreMethodRecorder()
  public jumpToOnePosition(sPos: string | ParagraphContentPos): number {
    return this.document.jumpToOnePosition(sPos);
  }

  @DocumentCoreMethodRecorder()
  public clearUndoList(): void {
    if (this.document.isProtectedMode()) {
      return;
    }
    this.document.clearUndoList();
  }

    /**
     * 获取选择区域的开始元素的坐标
     */
    // public getSeletionStartPos() {
    //     return this.document.getSelectionStartPos();
    // }

    /**
     * 清除所有内容
     */
    @DocumentCoreMethodRecorder()
    public clearContent(): void {
      if (this.document.isProtectedMode() ||
            (this.document.isStrictMode2())) {
        return;
      }
      this.document.clearContent();
    }

    public close(): void {
      this.document.clearContent();
      this.document = null;
    }

    /**
     * 删除选择区域
     */
    @DocumentCoreMethodRecorder()
    public removeSelection(): void {
        this.document.removeSelection();
    }

    /**
     * 获取当前新控件名称
     */
    public getCurrentNewControlName(): string {
      return this.document.getCurrentNewControlName();
    }

    public getCurrentNewControl(): NewControl {
      return this.document.getCurrentNewControl();
    }

    @DocumentCoreMethodRecorder()
    public removeSelectedContent(): number {
      return this.document.removeSelectedContent();
    }

    @DocumentCoreMethodRecorder()
    public undo(): void {
      if (this.document.isProtectedMode()) {
        return;
      }
      const date = new Date();
      this.document.undo();
      logger.devlog({id: this.id,
        startTime: date, args: arguments, name: 'undo'});
    }

    @DocumentCoreMethodRecorder()
    public redo(): void {
      const date = new Date();
      this.document.redo();
      logger.devlog({id: this.id,
        startTime: date, args: arguments, name: 'redo'});
    }

    /**
     * 获取文档默认文本属性
     */
    public getDefaultTextProperty(): IDefaultTextProperty {
      const logicDocument = this.getDocument();
      const defaultFont = logicDocument.getDefaultFont();
      return {
        // fontSize: DEFAULT_TEXT_PROPERTY.fontSize,
        // fontFamily: DEFAULT_TEXT_PROPERTY.fontFamily,
        // fontSize: DEFAULT_FONT.fontSize,
        // fontFamily: DEFAULT_FONT.fontFamily,
        fontSize: defaultFont.fontSize,
        fontFamily: defaultFont.fontFamily,
        fontWeight: DEFAULT_TEXT_PROPERTY.fontWeight,
        fontStyle: DEFAULT_TEXT_PROPERTY.fontStyle,
        textDecorationLine: DEFAULT_TEXT_PROPERTY.textDecorationLine,
        color: DEFAULT_TEXT_PROPERTY.color,
        backgroundColor: DEFAULT_TEXT_PROPERTY.backgroundColor,
      };
    }

    /**
     * 获取文档默认段落属性
     */
    public getDefaultParaProperty(): IDefaultParaProperty {
      return {
        alignment: DEFAULT_PARAGRAPH_PROPERTY.alignment,
        paraSpacing: DEFAULT_PARAGRAPH_PROPERTY.paraSpacing,
      };
    }

    /**
     * 获取Settings info 预存储属性集
     */
    public getSettingsInfoDefaults(): ISettingsInfoDefaults {
      return {
        application: SETTINGS_DEFAULT.info.application,
        version: SETTINGS_DEFAULT.info.version,
        protectMode: SETTINGS_DEFAULT.info.protectMode,
      };
    }

    /**
     * 返回当前文档的首次创建属性
     */
    public getDocSavedInfo(): ISettingsSavedInfo {
        return this.document.getDocSavedInfo();
    }

    /**
     * 获取document实例
     */
    public getDocument(): Document {
      return this.document;
    }

    /**
     * 获取排版方法
     */
    @DocumentCoreMethodRecorder()
    public recalculate(): void {
      this.document.recalculate();
    }

    @DocumentCoreMethodRecorder()
    public recalculateAllForce(): void {
      this.document.recalculateAllForce();
    }

    public updateCursorXY(): void {
      this.document.updateCursorXY();
    }

    /**
     * 获取页面x y limits
     */
    public getPageLimits(pageIndex: number): ILimits {
      return this.document.getPageLimits(pageIndex);
    }

    /**
     * 获取当前页的首个元素的可编辑区域
     */
    public getPageFields(pageIndex: number): ILimits {
      return this.document.getPageFields(pageIndex);
    }

    /**
     * 获取当前最大宽度
     */
    public getMaxWidth(bInsertFile: boolean): number {
      return this.document.getMaxWidth(bInsertFile);
    }

    public getAllNewControlProps(): any[] {
      return this.document.getAllNewControlProps();
    }

    public getAllRegionProps(): any[] {
      return this.document.getAllRegionProps();
    }

    /**
     * 获取当前最大高度
     */
    public getMaxHeight(bInsertFile: boolean): number {
      return this.document.getMaxHeight(bInsertFile);
    }

    public getDrawingByName(sName: string): ParaDrawing {
      return this.document.getDrawingByName(sName);
    }

    @DocumentCoreMethodRecorder()
    public setDrawingName(sName: string, sNewName: string): number {
      if (this.document.isProtectedMode()) {
        return ResultType.ProtectedMode;
      }
      return this.document.setDrawingName(sName, sNewName);
    }

    @DocumentCoreMethodRecorder()
    public deleteDrawing(sName: string, bActiveControl?: boolean): number {
      if (this.document.isProtectedMode()) {
        return ResultType.ProtectedMode;
      }
      return this.document.deleteDrawing(sName, bActiveControl);
    }

    @DocumentCoreMethodRecorder()
    public insertSpecialCharacter():number{
      
       return this.document.insertSpecialCharacter();
    }

    @DocumentCoreMethodRecorder()
    public insertNewLine(): number {
      if (!this.document.canInput()) {
        return ResultType.Failure;
      }
      return this.document.addNewParagraph();
    }

    @DocumentCoreMethodRecorder()
    public addNewLineForRegion(sRegion: string, nPosType: number): number {
      return this.document.addNewLineForRegion(sRegion, nPosType);
    }

    @DocumentCoreMethodRecorder()
    public changeAllFileToOneRegion(sName: string): string {
      return this.document.changeAllFileToOneRegion(sName);
    }

    @DocumentCoreMethodRecorder()
    public setDrawingProp(sName: string, props: IParaDrawingProp, bActiveControl?: boolean): number {
      return this.document.setDrawingProp(sName, props, bActiveControl);
    }

    public isDocModified(): boolean {
      return this.document.isDirty();
    }

    @DocumentCoreMethodRecorder()
    public setDocModified2(bModify: boolean): void {
      this.document.setDirty(bModify);
    }

    public getPagePositionInfo(pageIndex: number): any {
      return this.document.getPagePositionInfo(pageIndex);
    }

    public isStartScale(): boolean {
      return this.document.isStartScale();
    }

    @DocumentCoreMethodRecorder()
    public jumpToPage(pageIndex: number, bStart: boolean = true): void {
      this.document.jumpToPage(pageIndex, bStart);
    }

    public getSelectedDrawing(): ParaDrawing {
      return this.document.getSelectedDrawing();
    }

    public getTableHeaderContent(tableId: number): any {
      return this.document.getTableHeaderContent(tableId);
    }

    @DocumentCoreMethodRecorder()
    public addRegion(props: INewControlProperty): number {
      if (this.document.isProtectedMode() ||
            (this.document.isStrictMode2())) {
        return ResultType.ProtectedMode;
      }
      return this.document.addRegion(props);
    }

    public isCursorInRegion(): boolean {
      return this.document.isCursorInRegion();
    }

    public getCursorInRegion(): Region {
      return this.document.getCursorInRegion();
    }

    public getCurrentRegionProps(): INewControlProperty {
      return this.document.getCurrentRegionProps();
    }

    public getCurrentRegion(): Region {
        return this.document.getCurrentRegion();
    }

    /* IFTRUE_WATER */
    /**
     * 获取随机水印内容
     * @param release 是否返回剩余水印
     * @param reset 是否重置
     * @param startPos 水印的起始坐标（在reset为true时生效）
     */
     public getCorePosition(release: boolean = false, reset: boolean = false,
                            startPos?: IMarkBounding): IMarkRect {
        return this.document.getCorePosition(release, reset, startPos);
    }
    /**
     * 重置随机水印的样式、起始坐标
     * @param reset 是否重置样式name
     * @param keepPos 是否保持坐标位置
     */
    public resetCorePosition(reset: boolean = false, keepPos: boolean = true): void {
        this.document.resetCorePosition(reset, keepPos);
    }
    public getCoreBGen(): boolean {
        return this.document.getCoreBGen();
    }
    /** 获取水印的变更状态 */
    public corePositionChange(): number {
        return this.document.corePositionChange();
    }
    /* FITRUE_WATER */

    @DocumentCoreMethodRecorder()
    public setRegionActive(name?: string): void {
      this.document.setRegionActive(name);
    }

    @DocumentCoreMethodRecorder()
    public jumpToRegionBeforeOrAfter(name: string, bBack: boolean): number {
      return this.document.jumpToRegionBeforeOrAfter(name, bBack);
    }

    @DocumentCoreMethodRecorder()
    public deleteTableRows(name: string, rowIndex: number, nCount: number, nDirection: number): number {
      return this.document.deleteTableRows(name, rowIndex, nCount, nDirection);
    }

    public addActionRow(name: string): string {
      return this.document.addActionRow(name);
    }

    @DocumentCoreMethodRecorder()
    public makeUniqueRegionName(): string {
      return this.document.makeUniqueRegionName();
    }

    @DocumentCoreMethodRecorder()
    public checkRegionName(name: string): boolean {
      return this.document.checkRegionName(name);
    }
    public getRegionPropByArray(sName: string): string {
      return this.document.getRegionPropByArray(sName);
    }

    @DocumentCoreMethodRecorder()
    public deleteRedundantByRegionName(strName: string, bPageBreak: boolean): number {
      return this.document.deleteRedundantByRegionName(strName, bPageBreak);
    }

    @DocumentCoreMethodRecorder()
    public setRegionBorderViewMode(nViewType: number): number {
      return this.document.setRegionBorderViewMode(nViewType);
    }

    public setRegionBorderVisible(sRegion: string, bShow: boolean): number {
        return this.document.setRegionBorderVisible(sRegion, bShow);
    }

    public setAllRegionsBorderVisible(bShow: boolean): number {
        return this.document.setAllRegionsBorderVisible(bShow);
    }

    @DocumentCoreMethodRecorder()
    public removeRegion(sName: string, flag: number, unRefresh: boolean = false): number {
      return this.document.removeRegion(sName, flag, unRefresh);
    }

    public selectRegionByName(name: string): number {
      return this.document.selectRegionByName(name);
    }

    public getTopRegionNameAtCursor(): string {
      return this.document.getTopRegionNameAtCursor();
    }

    public getFatherRegionNameOfOneStruct(sStructsName: string): string {
      return this.document.getFatherRegionNameOfOneStruct(sStructsName);
    }
    @DocumentCoreMethodRecorder()
    public filterStructsByPropInRegion(sRegionName: string, sPropName: string, propValue: any): string {
      return this.document.filterStructsByPropInRegion(sRegionName, sPropName, propValue);
    }

    public getAllRegionNames(): string[] {
      return this.document.getAllRegionNames();
    }

    @DocumentCoreMethodRecorder()
    public setRegionsReadOnlyProp(sName: string, bCurrent: boolean, bLef: boolean): number {
      return this.document.setRegionsReadOnlyProp(sName, bCurrent, bLef);
    }

    public getRegionEnd(sName: string): string {
      return this.document.getRegionEnd(sName);
    }

    public getRegionBegin(sName: string): string {
      return this.document.getRegionBegin(sName);
    }

    public getFirstLevelRegionNames(): string[] {
      return this.document.getFirstLevelRegionNames();
    }

    @DocumentCoreMethodRecorder()
    public insertRegionAfterOneRegion(sRegion: string, sPrveRegion: string): number {
      return this.document.insertRegionAfterOneRegion(sRegion, sPrveRegion);
    }
    public getRegionByName(name: string): Region {
      return this.document.getRegionByName(name);
    }

    @DocumentCoreMethodRecorder()
    public setRegionProperty(props: INewControlProperty, name: string): number {
      return this.document.setRegionProperty(props, name);
    }

    /**
     * 插入图片
     */
    public addInlineImage(width: number, height: number, src: string, name?: string,
                          type?: EquationType, svgElem?: any, mediaType?: ImageMediaType,
                          mediaSrc?: string, datas?: any): string {
      if (this.document.isProtectedMode()) {
        return ResultType.StringEmpty;
      }
      const date = new Date();
      const result = this.document.addInlineImage(width, height, src, name, type, svgElem, mediaType, mediaSrc, datas);
      logger.devlog({id: this.id, result: result ? ResultType.Success : ResultType.Failure,
        startTime: date, args: arguments, name: 'addInlineImage'});

      // 手动录播记录：修复svgElem为DOM无法序列化
      if (svgElem instanceof Element) {
        svgElem = new XMLSerializer().serializeToString(svgElem);
      }
      const action = {
        method: 'addInlineImage',
        timestamp: Date.now(),
        args: [width, height, src, name, type, svgElem, mediaType, mediaSrc]
      };
      recorder.record(action);

      // TODO: may be problematic
      const logicDocument = this.getDocument();
      if ( getDocumentCoreRecordReplayState().isReplaying) {
        // IMAGE_FLAGS.isImageOnClick = false;
        logicDocument.setImageOnClick(false);
      }
      return result;
    }

    @DocumentCoreMethodRecorder()
    public deleteNewControl(sName: string): number {
      return this.document.deleteNewControl(sName);
    }

    public getImageNames(): string[] {
      return this.document.getImageNames();
    }

    public getNewControlsProps(): object {
      return this.document.getNewControlsProps();
    }

    public getNewControlProps(name: string): object {
      return this.document.getNewControlProps(name);
    }

    public getNewControlPropsWithParam(name: string,needExtraDateProps?: boolean, needCascade?: number): object {
      return this.document.getNewControlPropsWithParam(name,needExtraDateProps,needCascade);
    }

    @DocumentCoreMethodRecorder()
    public replaceNewControlTexts(props: any[], texts: any[][]): string[][] {
      return this.document.replaceNewControlTexts(props, texts);
    }

    public getFatherNewControlName(sName: string): string {
      return this.document.getFatherNewControlName(sName);
    }

    public getRegionXmlInfoByParament(name: string, sRev?: string): any {
      return this.document.getRegionXmlInfoByParament(name, sRev);
    }

    @DocumentCoreMethodRecorder()
    public setCheckboxStatus(name: string, bChecked: boolean): number {
      return this.document.setCheckboxStatus(name, bChecked);
    }

    public setStructsTextByJson(json: any[]): number {
      return this.document.setStructsTextByJson(json);
    }

    @DocumentCoreMethodRecorder()
    public setCheckboxCaption(name: string, sCaption: string): number {
      return this.document.setCheckboxCaption(name, sCaption);
    }

    public getCheckboxCaption(name: string): string {
      return this.document.getCheckboxCaption(name);
    }

    public setCheckBoxCode(name: string, sCode: string): number {
        return this.document.setCheckBoxCode(name, sCode);
    }

    public getCheckBoxCode(name: string): string {
        return this.document.getCheckBoxCode(name);
    }

    public getAllGroupCheckboxName(): string {
        return this.document.getAllGroupCheckboxName();
    }

    public getGroupCheckboxStatus(sGroupName: string): number {
        return this.document.getGroupCheckboxStatus(sGroupName);
    }

    public getCheckboxGroupName(sCheckBox: string): string {
        return this.document.getCheckboxGroupName(sCheckBox);
    }

    public showInfoToGroupCheckbox(sGroupName: string, sInfo: string): number {
        return this.document.showInfoToGroupCheckbox(sGroupName, sInfo);
    }

    public showInfoToCheckBoxs(sGroupName: string, sInfo: string): number {
        return this.document.showInfoToCheckBoxs(sGroupName, sInfo);
    }

    @DocumentCoreMethodRecorder()
    public setRadioCodeAndValueByArray(sName: string, items: any[], bClear?: boolean): number {
      return this.document.setRadioCodeAndValueByArray(sName, items, bClear);
    }

    public getRadioSelectedValue(sName: string): string {
      return this.document.getRadioSelectedValue(sName);
    }
    public setRadioButtonItemTextColor(sName: string, index: string, color: string): number {
        return this.document.setRadioButtonItemTextColor(sName, index, color);
    }
    public setRadioButtonItemByIndex(sName: string, index: string): number {
        return this.document.setRadioButtonItemByIndex(sName, index);
    }
    public getRadioButtonSelectedIndexes(sName: string): string {
        return this.document.getRadioButtonSelectedIndexes(sName);
    }

    @DocumentCoreMethodRecorder()
    public setRadioSelectedValue(sName: string, sValue: string): number {
      return this.document.setRadioSelectedValue(sName, sValue);
    }

    @DocumentCoreMethodRecorder()
    public setRadioSelectedValueByXY(sName: string, pointX: number, pointY: number): boolean {
      return this.document.setRadioSelectedValueByXY(sName, pointX, pointY);
    }

    @DocumentCoreMethodRecorder()
    public setNewControlDateTime(sName: string, selectedDate: string): boolean {
      return this.document.setNewControlDateTime(sName, selectedDate);
    }

    @DocumentCoreMethodRecorder()
    public resetNewControlDateTime(sName: string): void {
      return this.document.resetNewControlDateTime(sName);
    }

    @DocumentCoreMethodRecorder()
    public setNISCellDateTime(selectedDate: string): void {
      return this.document.setNISCellDateTime(selectedDate);
    }

    @DocumentCoreMethodRecorder()
    public resetNISCellDateTime(): void {
      return this.document.resetNISCellDateTime();
    }

    @DocumentCoreMethodRecorder()
    public setNISDateBoxFormat(type: NISDateBoxFormat, customFormat?: ICustomFormatDateProps): boolean {
      return this.document.setNISDateBoxFormat(type, customFormat);
    }

    @DocumentCoreMethodRecorder()
    public deleteAllRadioItem(sName: string): number {
      return this.document.deleteAllRadioItem(sName);
    }

    public getDocContent(cleanMode?: CleanModeType): IDocContent {
      return this.document.getDocContent(cleanMode);
    }

    public addDocContent(docContent: IDocContent, cleanMode: CleanModeType = CleanModeType.CleanMode,
                         options?: any): number {
      return this.document.addDocContent(docContent, cleanMode, options);
    }

    public updateRegionTitle(name: string): number {
      return this.updateRegionTitle(name);
    }

    public getRadioButtonItemWithArray(sName: string, key: string): string {
      return this.document.getRadioButtonItemWithArray(sName, key);
    }

    @DocumentCoreMethodRecorder()
    public setRadioSelectedByCode(sName: string, sCode: string): number {
      return this.document.setRadioSelectedByCode(sName, sCode);
    }

    @DocumentCoreMethodRecorder()
    public clearRadioButtonCheckItem(sName: string): number {
        return this.document.clearRadioButtonCheckItem(sName);
    }

    public getNewControlBySelectArea(): string {
      return this.document.getNewControlBySelectArea();
      // const newControlNames = this.document.getNewControlNamesWithBothBorders();
      // const newControlManager = this.document.getNewControlManager();
      // let sectionNames = '';
      // let noSectionNames = '';
      // newControlNames.forEach((name) => {
      //   const newcontrol = newControlManager.getNewControlByName(name);
      //   if ( newcontrol && newcontrol.isNewSection() ) {
      //     if ( '' === sectionNames ) {
      //       sectionNames = name;
      //     } else {
      //       sectionNames += ( ',' + name );
      //     }
      //   } else {
      //     if ( '' === noSectionNames ) {
      //       noSectionNames = name;
      //     } else {
      //       noSectionNames += ( ',' + name );
      //     }
      //   }
      // });

      // if ( '' === sectionNames || '' === noSectionNames ) {
      //   return sectionNames + noSectionNames;
      // } else {
      //   return sectionNames + ';' + noSectionNames;
      // }
    }

    @DocumentCoreMethodRecorder()
    public deleteRedundantEx(bBlankLine: boolean, bSpace: boolean, bTag: boolean, bDelPageBreak: boolean): number {
      return this.document.deleteRedundantEx(bBlankLine, bSpace, bTag, bDelPageBreak);
    }

    public getNavMenus(): any {
      return this.document.getNavMenus();
    }

    @DocumentCoreMethodRecorder()
    public selectedImageByName2(name: string): number {
      return this.document.selectedImageByName2(name);
    }

    public clearAllNewcontrolCascade(): void {
      this.document.clearAllNewcontrolCascade();
    }

    @DocumentCoreMethodRecorder()
    public triggerNewControlCascade(name: string): boolean {
      return this.document.triggerNewControlCascade(name);
    }

    /**
     * set current image element
     */
    public setImageSelectionInfo(selectionInfo: string): void {
      this.document.setImageSelectionInfo(selectionInfo);

      // 录播：对于DOM元素仅记录id
      const action = {
        method: 'setImageSelectionInfo',
        timestamp: Date.now(),
        args: [selectionInfo],
        external: true,
      };
      recorder.record(action);
    }

    @DocumentCoreMethodRecorder()
    public selectedImageByName(name: string): number {
      return this.document.selectedImageByName(name);
    }

    public getRowActionType(name: string): number {
      return this.document.getRowActionType(name);
    }

    public isRowSettingVisible(name: string): boolean {
      return this.document.isRowSettingVisible(name);
    }

    @DocumentCoreMethodRecorder()
    public moveCursorToTableByName(name: string): number {
      return this.document.moveCursorToTableByName(name);
    }

    public getFileProperty(name: string): string {
      return this.document.getFileProperty(name);
    }

    public setFileProperty(name: string, value: string): number {
        return this.document.setFileProperty(name, value);
    }

    public getAllFileProperties(): Map<string, string> {
      return this.document.getAllFileProperties();
    }

    public setAllFileProperties(values: Map<string, string>): void {
        this.document.setAllFileProperties(values);
    }

    /**
     * get current image element
     */
    public getImageSelectionInfo(): any {
      return this.document.getImageSelectionInfo();
    }

    public getNewControlCustomPropsByName(name: string): ICustomProps[] {
      const date = new Date();
      const res = this.document.getNewControlCustomPropsByName(name);
      logger.devlog({id: this.id,
        startTime: date, args: arguments, name: 'getNewControlCustomPropsByName'});
      return res;
    }

    @DocumentCoreMethodRecorder()
    public resetNewControlSelectItem(name: string): void {
      this.document.resetNewControlSelectItem(name);
    }

    @DocumentCoreMethodRecorder()
    public addNewControlCustomProps(name: string, props: ICustomProps[]): number {
      const date = new Date();
      const res = this.document.addNewControlCustomProps(name, props);
      logger.devlog({id: this.id,
        startTime: date, args: arguments, name: 'addNewControlCustomProps'});
      return res;
    }

    @DocumentCoreMethodRecorder()
    public insertNewTable(cols: number, rows: number, tableHeaderNum?: number,
                          tableName?: string, bRepeatHeader?: boolean): boolean {
      const date = new Date();
      const result = this.document.addInlineTable(cols, rows, tableHeaderNum, tableName, bRepeatHeader);
      logger.devlog({id: this.id,
        startTime: date, args: arguments, name: 'insertNewTable'});
      return result;
    }

    public getAllTableNames(): string[] {
      return this.document.getAllTableNames();
    }

    /**
     * 插入表格行
     * @param bBefore 在此行之前还是之后
     */
    @DocumentCoreMethodRecorder()
    public insertTableRow(bBefore: boolean): boolean {
      return this.document.insertTableRow(bBefore);
    }

    @DocumentCoreMethodRecorder()
    public insertTableRows(name: string, rowIndex: number, count: number, direction: number): number {
      return this.document.insertTableRows(name, rowIndex, count, direction);
    }

    /**
     * 插入表格列
     * @param bBefore 在此列之前还是之后
     */
    @DocumentCoreMethodRecorder()
    public insertTableColumn(bBefore: boolean): boolean {
      return this.document.insertTableColumn(bBefore);
    }

    public getCurrentParagraph(): DocumentContentElementBase {
        return this.document.getCurrentParagraph();
    }

    @DocumentCoreMethodRecorder()
    public removeTableRow(rowIndex?: number): boolean {
      const date = new Date();
      const result = this.document.removeTableRow(rowIndex);
      logger.devlog({id: this.id,
        startTime: date, args: arguments, name: 'removeTableRow'});
      return result;
    }

    @DocumentCoreMethodRecorder()
    public removeTableColumn(): boolean {
      const date = new Date();
      const result = this.document.removeTableColumn();
      logger.devlog({id: this.id,
        startTime: date, args: arguments, name: 'removeTableColumn'});
      return result;
    }

    @DocumentCoreMethodRecorder()
    public removeTable(tableIndex?: number): boolean {
      const date = new Date();
      const result = this.document.removeTable();
      logger.devlog({id: this.id,
        startTime: date, args: arguments, name: 'removeTableColumn'});
      return result;
    }

    public deleteTableByName(name: string): number {
      return this.document.deleteTableByName(name);
    }

    @DocumentCoreMethodRecorder()
    public mergeTableCells(): void {
      const date = new Date();
      const result = this.document.mergeTableCells();
      logger.devlog({id: this.id, result: result ? ResultType.Success : ResultType.Failure,
        startTime: date, args: arguments, name: 'mergeTableCells'});
    }

    public canMergeTableCells(): boolean {
      // const date = new Date();
      const result = this.document.canMergeTableCells();
      // logger.devlog({id: this.id, result: result ? ResultType.Success : ResultType.Failure,
      //   startTime: date, args: arguments, name: 'canMergeTableCells'});
      return result;
    }

    /**
     * 拆分单元格
     * @param rows 拆分行数
     * @param cols 拆分列数
     */
    @DocumentCoreMethodRecorder()
    public splitTableCells(rows: number, cols: number): boolean {
      const date = new Date();
      const result = this.document.splitTableCells(rows, cols);
      logger.devlog({id: this.id, result: result ? ResultType.Success : ResultType.Failure,
        startTime: date, args: arguments, name: 'splitTableCells'});
      return result;
    }

    /**
     * 当前是否只选中了一个table的若干单元格
     */
    public isSelectedTableCells(): boolean {
       return this.document.isSelectedTableCells();
    }

    public isSelectedTable(): boolean {
      return this.document.isSelectedTable();
    }

    /**
     * 当前光标是否在单元格内
     */
    public isInTableCell(): boolean {
      return this.document.isInTableCell();
    }

    public isMovingTableBorder(): boolean {
      return this.document.isMovingTableBorder();
    }

    public isTableBorder2(): boolean {
      return this.document.isTableBorder2();
    }

    public resetTableBorder2(): void {
        this.document.resetTableBorder2();
    }

    public getTableNewBorder(): IDrawTableNewBorder {
      return this.document.getMovingTableNewBorder();
    }

    @DocumentCoreMethodRecorder()
    public insertNISTable(cols: number, rows: number, tableHeaderNum?: number, tableName?: string): boolean {
      const date = new Date();
      const result = this.document.addNISTable(cols, rows, tableHeaderNum, tableName);
      logger.devlog({id: this.id,
        startTime: date, args: arguments, name: 'insertNISTable'});
      return result;
    }

    public getSelectedTextPro(): any {
      const date = new Date();
      const result = this.document.getSelectedTextPro();
      logger.devlog({id: this.id, result: result ? ResultType.Success : ResultType.Failure,
        startTime: date, args: arguments, name: 'getSelectedTextPro'});
      return result;
    }

    @DocumentCoreMethodRecorder()
    public setTableProperty(props: ITableProperty): void {
      this.document.setTableProperty(props);
    }

    @DocumentCoreMethodRecorder()
    public setTableProperty1(name: string, props: ITableProperty): number {
      return this.document.setTableProperty1(name, props);
    }

    @DocumentCoreMethodRecorder()
    public setTableCellProtect(name: string, cellName: string, bReadOnly: boolean): number {
      return this.document.setTableCellProtect(name, cellName, bReadOnly);
    }

    @DocumentCoreMethodRecorder()
    public setTableProperty2(name: string, sJson: any): number {
      return this.document.setTableProperty2(name, sJson);
    }

    public getTableProperty(name?: string): ITableProperty {
      return this.document.getTableProperty(name);
    }

    public getTableProperty2(name: string, sJson: any): string {
      return this.document.getTableProperty2(name, sJson);
    }

    public getCurrentTableName(): string {
      return this.document.getCurrentTableName();
    }

    public getCurrentCellName(): string {
      return this.document.getCurrentCellName();
    }

    public getCurrentCell(): TableCell {
      return this.document.getCurrentCell();
    }

    public getUniqueTableName(name?: string): string {
      return this.document.getUniqueTableName(name);
    }

    public setSearchInfo(info: any): number {
      return this.document.setSearchInfo(info);
    }

    public jumpToOneSearch(index: number): number {
      return this.document.jumpToOneSearch(index);
    }

    public closeSearch(): void {
      this.document.closeSearch();
    }

    public getSearchCount(): number {
      return this.document.getSearchCount();
    }

    public createNum(props: INumberingProperty): INum {
      return this.document.createNum(props);
    }

    public getDrawingObjects(): GraphicObjects {
      return this.document.getDrawingObjects();
    }

    public getGraphicObject(): Map<string, ParaDrawing> {
      return this.document.getDrawingObjects()
                      .getGraphicObject();
    }

    public isImageInHeaderFooter(image: ParaDrawing): boolean {
      return this.document.getDrawingObjects()
        .isImageInHeaderFooter(image);
    }

    public setCellText(text: string): number {
      return this.document.setCellText(text);
    }

    @DocumentCoreMethodRecorder()
    public clearImages(): void {
      return this.document.getDrawingObjects()
                      .clearImages();
    }

    @DocumentCoreMethodRecorder()
    public protectDoc(bProtect: boolean): number {
      this.document.setDocProtect2(this._prevProtectModel);
      this._prevProtectModel = undefined;
      return this.document.setDocProtect(bProtect);
    }

    public protectDoc2(bProtect: boolean): void {
      this.document.setDocProtect2(bProtect);
    }

    public setMarkText(text: string): void {
      this.document.setMarkText(text);
    }

    public isProtectedMode(): boolean {
      return this.document.isProtectedMode();
    }

    public isCanReadRevisionInfo(): boolean {
      return this.document.isCanReadRevisionInfo();
    }

    public getStructsNameByCell(sTalbeName: string, sCellName: string): string {
      return this.document.getStructsNameByCell(sTalbeName, sCellName);
    }

    public deletePrevProtectedMode(): void {
      this._prevProtectModel = undefined;
    }

    /**
     * 接口进来的方法都会设置成false，这个是获取原始的只读属性
     * @returns true 或 false
     */
    public isProtectedMode2(): boolean {
      if (this._prevAdminModel !== undefined && this._prevProtectModel !== undefined) {
        return this._prevProtectModel;
      }
      return this.document.isProtectedMode();
    }

    @DocumentCoreMethodRecorder()
    public designTemplet(bDesignModel: boolean): number {
      return this.document.setDesignModel(bDesignModel);
    }

    public setAdminModeTrue(): void {
      if (this._prevAdminModel !== undefined) {
        return;
      }
      this._prevAdminModel = this.document.isAdminMode();
      this._prevProtectModel = this.document.isProtectedMode();
      this.document.setDocProtect2(false);
      this.document.setAdminMode(true);
    }

    public getNextNewControlByJson(res: any): any {
      return this.document.getNextNewControlByJson(res);
    }

    public resetAdminMode(): void {
      // set prev props back! not 'reset'

      if (this._prevAdminModel === undefined || !this.document) {
        return;
      }
      this.document.setDocProtect2(this._prevProtectModel);
      this.document.setAdminMode(this._prevAdminModel);
      this._prevAdminModel = undefined;
    }

    @DocumentCoreMethodRecorder()
    public setAdminMode(bAdminMode: boolean): number {
      this.resetAdminMode();
      return this.document.setAdminMode(bAdminMode);
    }

    public replaceRegionTexts(props: any[], texts: any[][]): string[][] {
      return this.document.replaceRegionTexts(props, texts);
    }

    public setViewMode(type: ViewModeType): number {
      return this.document.setViewMode(type);
    }

    public getViewMode(): ViewModeType {
      return this.document.getViewMode();
    }

    public isWebView(): boolean {
      return (ViewModeType.WebView === this.getViewMode());
    }

    public isAdminMode(): boolean {
      return this.document.isAdminMode();
    }

    public isDesignModel(): boolean {
      return this.document.isDesignModel();
    }

    public isStrictMode(): boolean {
        return this.document.isStrictMode();
    }

    /**
     * 严格模式和管理员模式
     */
    public isStrictMode2(): boolean {
        return this.document.isStrictMode2();
    }

    @DocumentCoreMethodRecorder()
    public setStrictMode(bStrictMode: boolean): number {
      return this.document.setStrictMode(bStrictMode);
    }

    public getEditMode(): EditModeType {
        return this.document.getEditMode();
    }

    @DocumentCoreMethodRecorder()
    public setEditMode(type: EditModeType): number {
        return this.document.setEditMode(type);
    }

    // public isEditMode(): boolean {
    //   return this.document.isEditMode();
    // }

    @DocumentCoreMethodRecorder()
    public setPropsToPrint(): void {
      return this.document.setPropsToPrint();
    }

    @DocumentCoreMethodRecorder()
    public setViewScale(scale: number): number {
      return this.document.setViewScale(scale);
    }

    public getViewScale(): number {
      return this.document.getViewScale();
    }

    public getSelectedImage(): ParaDrawing {
      return this.document.getDrawingObjects()
                      .getSelectedImage();
    }

    @DocumentCoreMethodRecorder()
    public applyDrawingProps(width: number, height: number, preserveAspectRatio?: boolean, name?: string): void {
      const date = new Date();
      this.document.getDrawingObjects()
                        .applyDrawingProps(width, height, preserveAspectRatio, name);
      logger.devlog({id: this.id,
                          startTime: date, args: arguments, name: 'applyDrawingProps'});
    }

    @DocumentCoreMethodRecorder()
    public applyDrawingSvgElemStr(svgEquationElemStr: string): void {
      this.getDrawingObjects()
        .applyDrawingSvgElemStr(svgEquationElemStr);
    }

    @DocumentCoreMethodRecorder()
    public applyDrawingHref(svgConvertedURI: string, newWidth: number, newHeight: number): void {
      this.getDrawingObjects()
        .applyDrawingHref(svgConvertedURI, newWidth, newHeight);
    }

    @DocumentCoreMethodRecorder()
    public applyDrawingLocks(sizeLocked: boolean, deleteLocked: boolean): void {
      const date = new Date();
      this.document.getDrawingObjects()
                        .applyDrawingLocks(sizeLocked, deleteLocked);
      logger.devlog({id: this.id,
                          startTime: date, args: arguments, name: 'applyDrawingLocks'});
    }

    @DocumentCoreMethodRecorder()
    public setAspectRatio(preserveAspectRatio: boolean): void {
      if (this.document.isProtectedMode() ||
          (this.document.isStrictMode2())) {
        return;
      }
      const date = new Date();
      this.document.getDrawingObjects()
        .setAspectRatio(preserveAspectRatio);
      logger.devlog({id: this.id,
        startTime: date, args: arguments, name: 'setAspectRatio'});
    }

    @DocumentCoreMethodRecorder()
    public removeAllPlaceholderNewControls(): void {
      this.document.removeAllPlaceholderNewControls();
    }

    @DocumentCoreMethodRecorder()
    public deleteImage(delImage: ParaDrawing): boolean {
      if (this.document.isProtectedMode()) {
        return false;
      }
      const date = new Date();
      const result = this.document.getDrawingObjects()
        .deleteImage(delImage);
      logger.devlog({id: this.id,
        startTime: date, args: {name: delImage.name}, name: 'deleteImage'});
      return result;
    }

    @DocumentCoreMethodRecorder()
    public deleteImageFromParaTableRemoval(elementId: number, bTable: boolean): void {
      if (this.document.isProtectedMode() ||
          (this.document.isStrictMode2())) {
        return;
      }
      const date = new Date();
      this.document.getDrawingObjects()
        .deleteImageFromParaTableRemoval(elementId, bTable);
      logger.devlog({id: this.id,
        startTime: date, args: arguments, name: 'deleteImageFromParaTableRemoval'});
    }

    public getImageType(): number {
      return this.document.getDrawingObjects()
                              .getImageType();
    }

    @DocumentCoreMethodRecorder()
    public checkUniqueImageName(name: string): boolean {
      return this.document.getDrawingObjects()
        .checkUniqueImageName(name);
    }

    @DocumentCoreMethodRecorder()
    public checkUniqueImageNameOtherThanSelectedImage(name: string): boolean {
      return this.document.getDrawingObjects()
        .checkUniqueImageNameOtherThanSelectedImage(name);
    }

    @DocumentCoreMethodRecorder()
    public addNewControl(property: INewControlProperty, sText?: string): number {
      if (this.document.isProtectedMode()) {
        return ResultType.ProtectedMode;
      }
      const date = new Date();
      const result = this.document.addNewControl(property, sText);
      if (sText !== undefined) {
        property['text'] = sText;
      }
      logger.devlog({id: this.id, result: result ? ResultType.Success : ResultType.Failure,
        startTime: date, args: property, name: 'addNewControl'});
      return result;
    }

    @DocumentCoreMethodRecorder()
    public checkNewControlName(name: string): boolean {
      return this.document.getNewControlManager()
        .checkNewControlName(name);
    }

    @DocumentCoreMethodRecorder()
    public makeUniqueNewControlName(type: number, name?: string): string {
      return this.document.getNewControlManager()
                              .makeUniqueName(type, name);
    }

    public addSelection(startEle: DocumentContentElementBase, endEle: DocumentContentElementBase): void {
      this.document.addSelection(startEle, endEle);
    }

    @DocumentCoreMethodRecorder()
    public setNewControlText(sName: string, sText: string): number {
      if (this.document.isProtectedMode()) {
        return ResultType.ProtectedMode;
      }
      const date = new Date();
      const result = this.document.setNewControlText(sName, sText);
      logger.devlog({id: this.id, result,
        startTime: date, args: arguments, name: 'setNewControlText'});
      return result;
    }
    
    @DocumentCoreMethodRecorder()
    public setNewControlImage(sName: string, image: ParaDrawing): number {
      if (this.document.isProtectedMode()) {
        return ResultType.ProtectedMode;
      }
      const date = new Date();
      const result = this.document.setNewControlImage(sName, image);
      logger.devlog({id: this.id, result,
        startTime: date, args: arguments, name: 'setNewControlImage'});
      return result;
    }

    @DocumentCoreMethodRecorder()
    public setNewControlMixData(sName: string, datas: any): number {
      if (this.document.isProtectedMode()) {
        return ResultType.ProtectedMode;
      }
      const date = new Date();
      const result = this.document.setNewControlMixData(sName, datas);
      logger.devlog({id: this.id, result,
        startTime: date, args: arguments, name: 'setNewControlImage'});
      return result;
    }

    @DocumentCoreMethodRecorder()
    public setNewControlListItems(sName: string, pos: any[]): number {
      if (this.document.isProtectedMode()) {
        return ResultType.ProtectedMode;
      }
      const date = new Date();
      const result = this.document.setNewControlListItems(sName, pos);
      logger.devlog({id: this.id, result,
        startTime: date, args: arguments, name: 'setNewControlListItems'});
      return result;
    }

    @DocumentCoreMethodRecorder()
    public setNISCellListItems(pos: any[]): number {
      if (this.document.isProtectedMode()) {
        return ResultType.ProtectedMode;
      }
      const date = new Date();
      const result = this.document.setNISCellListItems(pos);
      logger.devlog({id: this.id, result,
        startTime: date, args: arguments, name: 'setNISCellListItems'});
      return result;
    }

    @DocumentCoreMethodRecorder()
    public setNewControlRadioCheck(sName: string, pos: any[]): number {
      if (this.document.isProtectedMode()) {
        return ResultType.ProtectedMode;
      }
      const date = new Date();
      const result = this.document.setNewControlListItems(sName, pos);
      logger.devlog({id: this.id, result,
        startTime: date, args: arguments, name: 'setNewControlListItems'});
      return result;
    }

    @DocumentCoreMethodRecorder()
    public markTextWithWave(options?: IWavyUnderlineOptions): string | null {
        if (this.document.isProtectedMode()) {
            return null;
        }
        const date = new Date();
        const result = this.document.markTextWithWaveController(options);
        logger.devlog({id: this.id, result: result || 'null',
          startTime: date, args: options || {}, name: 'markTextWithWave'});
        return result;
    }

    public getSelectionRangeStart(): string {
      return this.document.getSelectionRangeStart();
    }

    public getSelectionRangeEnd(): string {
      return this.document.getSelectionRangeEnd();
    }

    public getCurrentTextProps(): any {
      return this.document?.getCurrentTextProps();
    }

    public getTextProps(): any {
      return this.document.getTextProps();
    }

    public setControlActiveByType(type?: number): void {
      this.document.setControlActiveByType(type);
    }

    @DocumentCoreMethodRecorder()
    public makeUniqueImageName(type: ParaElementType, name?: string): string {
      return this.document.getDrawingObjects()
        .makeUniqueImageName(type, name);
    }
    /**
     * 当前鼠标是否在newControl内
     */
    public isFocusOnNewControl(mouseEvent?: MouseEventHandler, pageIndex?: number): boolean {
      return this.document.isFocusInNewControl(mouseEvent, pageIndex);
    }

        public getWavyUnderlineManager(): WavyUnderlineManager {
      return this.document.getWavyUnderlineManager();
    }

    /**
     * 根据坐标获取段落内容位置 - 用于波浪线位置检测
     */
    public getParaContentPosByXY(pageId: number, pointX: number, pointY: number): {pos: ParagraphContentPos, bFound: boolean} | null {
      const contentPos = this.document.getContentPosByXY(pageId, pointX, pointY);
      if (contentPos === undefined) {
        return null;
      }

      const elementIndex = this.document.getElementPageIndexByXY(contentPos, pointX, pointY, pageId);
      const element = this.document.content[contentPos];
      
      if (element && element.isParagraph()) {
        return (element as any).getParaContentPosByXY(pageId, pointX, pointY);
      }
      
      return null;
    }

    /**
     * 当前光标是否在newControl内
     */
    public isCursorInNewControl(): boolean {
      return this.document.isCursorInNewControl();
    }

    public getNewControlsFocusBounds(mouseEvent?: MouseEventHandler, pageIndex?: number): IDrawNewControlBounds {
      return this.document?.getNewControlsFocusBounds(mouseEvent, pageIndex);
    }

    public getFoucsInRegion(mouseEvent: MouseEventHandler, pageIndex: number): Region {
      return this.document.getFoucsInRegion(mouseEvent, pageIndex);
    }

    public getRegionBoundLines(region: Region, jumpProtect?: boolean): IDrawSelectionsLine[] {
      return this.document.getRegionBoundLines(region, jumpProtect);
    }

    public getRegionText(name: string): string {
      return this.document.getRegionText(name);
    }

    public getRegionTextAI(name: string): string {
      let str = '';
      str += '{';
      str += this.document.getRegionTextAI(name);
      str += '}';
      return str;
    }

    public setPrintStatus(flag: boolean): void {
      this.document.setPrintStatus(flag);
    }

    public getPrintStatus(): boolean {
      return this.document.getPrintStatus();
    }

    /**
     * 获取指定newControl的提示信息
     * @param newControlName
     */
    public getNewControlTips( newControlName: string ): string {
      return this.document.getNewControlTips(newControlName);
    }

    /**
     * 获取当前光标所在newControl的属性
     */
    public getCursorInNewControlProperty(): INewControlProperty {
      return this.document.getCursorInNewControlProperty();
    }

    /**
     * 获取当前光标所在newControl的名称
     */
    public getCursorInNewControlName(): string {
      const newControl = this.document.getCursorInNewControl();
      if ( newControl ) {
        return newControl.getNewControlName();
      }

      return null;
    }

    public getMouseDownControl(mouseEvent?: MouseEventHandler, pageIndex?: number): NewControl {
      return this.document.getFocusInNewControl(mouseEvent, pageIndex);
    }

    /**
     * 获取当前鼠标所在newControl的名称
     */
    public getFocusNewControlName(mouseEvent?: MouseEventHandler, pageIndex?: number): string {
      const newControl = this.document.getFocusInNewControl(mouseEvent, pageIndex);
      if ( newControl ) {
        return newControl.getNewControlName();
      }

      return null;
    }

    public getUnValidNumberBox(flagType?: number): string {
      return this.document.getUnValidNumberBox(flagType);
    }

    public getReadonlyOptionByXY(mouseEvent: MouseEventHandler, pageIndex: number): any {
      return this.document.getReadonlyOptionByXY(mouseEvent, pageIndex);
    }

    public getRegionBorderViewType(): number {
      return this.document.getRegionBorderViewType();
    }

    /**
     * 设置指定newcontrol，或者光标处newcontrol的属性
     * @param property
     * @param newControlName
     */
    @DocumentCoreMethodRecorder()
    public setNewControlProperty(property: INewControlProperty, newControlName?: string): boolean {
      if (this.document.isProtectedMode()) {
        return false;
      }
      const date = new Date();
      const result = this.document.setNewControlProperty(newControlName, property)
        === ResultType.Success ? true : false;
      if (newControlName !== undefined) {
        property['name'] = newControlName;
      }
      logger.devlog({id: this.id, result: result ? ResultType.Success : ResultType.Failure,
        startTime: date, args: property, name: 'setNewControlProperty'});
      return result;
    }

    /**
     * 在同一步设置newcontrol的 property 和 text
     * @param property
     * @param sText
     */
    @DocumentCoreMethodRecorder()
    public setNewControlPropertyAndText(property: INewControlProperty, sText: string): boolean {
      if (this.document.isProtectedMode()) {
        return false;
      }
      const date = new Date();
      const result = this.document.setNewControlPropertyAndText(property, sText)
        === ResultType.Success ? true : false;

      logger.devlog({id: this.id, result: result ? ResultType.Success : ResultType.Failure,
        startTime: date, args: property, name: 'setNewControlPropertyAndText'});
      return result;
    }

    @DocumentCoreMethodRecorder()
    public setNewControlProperty1(property: INewControlProperty, newControlName?: string): number {
      const date = new Date();
      const result = this.document.setNewControlProperty(newControlName, property);
      if (newControlName !== undefined) {
        property['name'] = newControlName;
      }
      logger.devlog({id: this.id, result,
        startTime: date, args: property, name: 'setNewControlProperty'});
      return result;
    }

    /**
     * 获取所有newcontrol名称，除了光标和鼠标所在的newcontrol
     */
    public getNewControlsWithoutFocus(pageIndex?: number, newControlName?: string, needHidden?: boolean): NewControl[] {
      return this.document.getNewControlsWithoutFocus(pageIndex, newControlName, needHidden);
    }

    /**
     * 获取当前页的区域
     * @param pageIndex 当前页
     * @param regionName 要排除的区域名称
     * @param needHidden 是否需要隐藏区域
     * @param jumpPage 是否考虑跨页
     * @returns 
     */
    public getRegionsWithoutFocus(pageIndex?: number, regionName?: string,
                                needHidden?: boolean, jumpPage?: boolean, jumpProtect?: boolean): Region[] {
        return this.document.getRegionsWithoutFocus(pageIndex, regionName, needHidden, jumpPage, jumpProtect);
    }

    /**
     * 获取当前状态的工具栏
     */
    public getToolbarItemStatus(): any {
        return this.document.getToolbarItemStatus();
    }

    /**
     * 同步当前状态的工具栏
     */
    public setToolbarItemStatus(toolbar: any): void {
        this.document.setToolbarItemStatus(toolbar);
    }

    /**
     * 获取newcontrol的区域
     * @param newControlName
     */
    public getNewControlBounds(newControlName: string): IDrawNewControlBounds {
      const newControl = this.document.getNewControlByName(newControlName);
      return this.document.getNewControlBounds(newControl);
    }

    @DocumentCoreMethodRecorder()
    public selectNewControlContent(sName: string): number {
      return this.document.selectNewControlContent(sName);
    }

    @DocumentCoreMethodRecorder()
    public selectNewControl(sName: string): number {
      if (this.document.isProtectedMode()) {
        return ResultType.ProtectedMode;
      }
      return this.document.selectNewControl(sName);
    }

    public isNewControlHiddenBackground(newControlName: string): boolean {
      return this.document.isNewControlHiddenBackground(newControlName);
    }

    public jumpToNextNewControl(name: string): boolean {
      return this.document.jumpToNextNewControl(name);
    }

    /**
     * 获取选择区域内只包含左边框的所有newcontrols的名称
     */
    public getNewControlNamesWithLeftBorder(): string[] {
      return this.document.getNewControlNamesWithLeftBorder();
    }

    /**
     * 获取选择区域内只包含右边框的所有newcontrols的名称
     */
    public getNewControlNamesWithRightBorder(): string[] {
      return this.document.getNewControlNamesWithRightBorder();
    }

    /**
     * 获取选择区域内包含左右边框的所有newcontrols的名称
     */
    public getNewControlNamesWithBothBorders(): string[] {
      return this.document.getNewControlNamesWithBothBorders();
    }

    public getNewControlText( sName: string ): string {
      return this.document.getNewControlText(sName);
    }

    /*
    为AI设计的带边框的newcontrol的内容
     */
    public getNewControlTextAI( sName: string ): string {
      return this.document.getNewControlTextAI(sName);
    }

    public getTableCellText(): string {
      return this.document.getTableCellText();
    }

    

    public getNewControlByName( sName: string ): NewControl {
      return this.document.getNewControlByName(sName);
    }

    /**
     * 获取指定结构内容全部图片名称
     * @param sName 结构名称
     */
     public getAllImagesInStruct(sName: string): string {
        return this.document
                .getAllImagesInStruct(sName);
    }

    public getNewControlTitle(name: string): string {
      let newControl: any = this.getNewControlByName(name);
      if (newControl) {
        return newControl.getTitle();
      }
        return ''
    }

    public getRegionTitle(name: string): string {
        const region = this.getRegionByName(name);
        if (region) {
          return region.getTitle();
        }
        return ''
    
    }

    public getNewControlPropByName(name: string): INewControlProperty {
      return this.document.getNewControlPropByName(name);
    }

    public getNewControlsNameValue(): object[] {
      return this.document.getNewControlsNameValue();
    }

    /**
     * 批量设置newcontrol的内容
     * @param contents
     * @param bRefresh 是否排版，更新光标位置
     */
    @DocumentCoreMethodRecorder()
    public setStructsTextByArray( contents: Map<string, string[]> ): number {
      if (this.document.isProtectedMode()) {
        return ResultType.ProtectedMode;
      }
      return this.document.setNewControlsText(contents);
    }

    /**
     * 批量设置newcontrol的属性
     * @param propertys
     * @param bRefresh 是否排版，更新光标位置
     */
    @DocumentCoreMethodRecorder()
    public setStructsPropByArray( propertys: INewControlProperty[] ): number {
      if (this.document.isProtectedMode()) {
        return ResultType.ProtectedMode;
      }
      return this.document.setNewControlsProp(propertys);
    }

    // public getNewControlNamesByCustoms(): string {
    //   return this.document.getNewControlNamesByCustoms();
    // }

    /**
     * 单选框、多选框中下拉选项插入item
     * @param sName
     * @param pos 插入位置
     * @param code 属性名称
     * @param value 值
     */
    @DocumentCoreMethodRecorder()
    public insertNewControlItem( sName: string, pos: number, code: string, value: string ): boolean {
      if (this.document.isProtectedMode()) {
        return false;
      }
      const date = new Date();
      const newControl = this.getNewControlByName(sName);

      if ( newControl ) {
        const result = newControl.addItem(pos, code, value);
        logger.devlog({id: this.id, result: result ? ResultType.Success : ResultType.Failure,
          startTime: date, args: arguments, name: arguments['callee'].name});
        return result;
      }
    }

    /**
     * 光标处是否不可编辑
     */
    // public isCursorReadOnly(): boolean {
    //   return this.document.isCursorReadOnly();
    // }

    /**
     * 特殊的newControl元素，不允许用户进行输入、删除
     */
    public isInSpecialNewControl(): boolean {
      return this.document.isInSpecialNewControl();
    }

    // public isPopWinNewControl(): boolean {
    //   return this.document.isPopWinNewControl();
    // }

    /**
     * 光标处的newControl元素，是否不允许用户进行编辑
     */
    // public isCursorInCantEditNewControl(): boolean {
    //   return this.document.isCursorInCantEditNewControl();
    // }

    public canInput(): boolean {
      return this.document.canInput();
    }

    public canDelete(): boolean {
      return this.document.canDelete();
    }

    /**
     * 新建空白文档
     */
    @DocumentCoreMethodRecorder()
    public createNewDocument(loadDocument: boolean = false): void {
      const date = new Date();
      this.document.resetModeStatus();
      this.document.clearContent();
      this.document = new Document();
      this.document.id = this.id;
      SPELL_CHECK.close();
      // setRegionContentFont(null);
      // console.log(circularParse(circularStringify(this.document)));
      if (!loadDocument) {
        this.document.createNewDocument();
        this.document.moveCursorToXY(0, 0, 0);
      }
      this._prevAdminModel = undefined;
      this._prevProtectModel = undefined;
      logger.devlog({id: this.id,
        startTime: date, args: arguments, name: 'createNewDocument'});
    }

    /**
     * 文档是否被修改
     */
    public isDirty(): boolean {
      // const date = new Date();
      const result = this.document.isDirty();
      // logger.devlog({id: this.id, result: result ? ResultType.Success : ResultType.Failure,
      //   startTime: date, args: arguments, name: 'isDirty'});
      return result;
    }

    @DocumentCoreMethodRecorder()
    public isImageOnClick(): boolean {
      return this.document.isImageOnClick();
    }

    @DocumentCoreMethodRecorder()
    public changeRevisionState(bFinal: boolean): number {
      return this.document.changeRevisionState(bFinal);
    }

    @DocumentCoreMethodRecorder()
    public changeRevisionState2(bFinal: boolean): void {
      this.document.changeRevisionState2(bFinal);
    }

    public getPageLines(pageIndex: number): any {
      return this.document.getPageLines(pageIndex);
    }

    /**
     * 设置文档脏标记
     * @param bDirty 默认为true：已被修改
     */
    @DocumentCoreMethodRecorder()
    public setDirty( bDirty: boolean = true ): void {
      // const date = new Date();
      this.document.setDirty(bDirty);
      // logger.devlog({id: this.id,
      //   startTime: date, args: arguments, name: 'setDirty'});
    }

    @DocumentCoreMethodRecorder()
    public setShowParaEnd(bShow: boolean): void {
      if (this.document.isProtectedMode()) {
        return;
      }
      this.document.setShowParaEnd(bShow);
    }

    public isShowParaEnd(): boolean {
        return this.document.isShowParaEnd();
    }

    public getHdrFtr(): CHeaderFooterController {
      return this.document.hdrFtr;
    }

    public hasHeaderFooter(bHeader: boolean): boolean {
      return (null != this.document.getHeaderFooter(bHeader));
    }

    public getHeaderFooter(bHeader: boolean): any {
      return this.document.getHeaderFooter(bHeader);
    }

    public getSectionProperty(): SectionProperty {
      return this.document.sectionProperty;
    }

    public getPageMarginsHeader(): number {
      return this.document.sectionProperty.getPageMarginsHeader();
    }

    public getPageMarginsFooter(): number {
      return this.document.sectionProperty.getPageMarginsFooter();
    }

    public getSectionPageMargins(): SectionPageMargins {
      return this.document.sectionProperty.pageMargins;
    }

    public getSectionPageSize(): SectionPageSize {
      return this.document.sectionProperty.pageSize;
    }

    @DocumentCoreMethodRecorder()
    public setDynamicHeightMode(bShow: boolean): number {
      // dynamicHeightEnabled.old = dynamicHeightEnabled.value;
      // dynamicHeightEnabled.value = bShow;
      // return ResultType.Success;
      return this.document.setDynamicHeightMode(bShow);
    }

    @DocumentCoreMethodRecorder()
    public setInlineMode(flag: boolean, containerWidth?: number): number {
        return this.document.setInlineMode(flag, containerWidth);
    }
    public isInlineMode(): boolean {
        return this.document.isInlineMode();
    }
    public setInlinePageProp(width: number, height: number): boolean {
        return this.document.setInlinePageProp(width, height);
    }

    public getDynamicHeightMode(): boolean {
      return this.document.getDynamicHeightMode();
    }


    public canRedo(): boolean {
      return this.document.canRedo();
    }

    public canUndo(): boolean {
      return this.document.canUndo();
    }

    @DocumentCoreMethodRecorder()
    public setRevision(revisonSetting: IRevisionSetting): void {
      return this.document.setRevision(revisonSetting);
    }

    public isTrackRevisions(): boolean {
        return this.document.isTrackRevisions();
    }

    public isFinalRevision(): boolean {
        return this.document.isFinalRevision();
    }

    @DocumentCoreMethodRecorder()
    public startTrackRevisions(): void {
        return this.document.startTrackRevisions();
    }

    @DocumentCoreMethodRecorder()
    public setTrackRevisions(bTrack: boolean): number {
        return this.document.setTrackRevisions(bTrack);
    }

    @DocumentCoreMethodRecorder()
    public closeTrackRevisions(): void {
      return this.document.closeTrackRevisions();
    }

    @DocumentCoreMethodRecorder()
    public setFinalRevision(bFinal: boolean): void {
      return this.document.setFinalRevision(bFinal);
    }

    @DocumentCoreMethodRecorder()
    public acceptRevisionChanges(): void {
      return this.document.acceptRevisionChanges();
    }

    @DocumentCoreMethodRecorder()
    public rejectRevisionChanges(): void {
      return this.document.rejectRevisionChanges();
    }

    /**
     * 获取修订统计信息
     * @returns 修订统计信息
     */
    @DocumentCoreMethodRecorder()
    public getRevisionCount(): {
        total: number;           // 总修订数
        additions: number;       // 新增修订数
        deletions: number;       // 删除修订数
        users: string[];         // 参与修订的用户列表
    } {
        return this.document.getRevisionCount();
    }

    /**
     * 获取修订详细信息
     * @param author 可选，指定修订者姓名进行过滤
     * @returns 修订详细信息数组，包含userName、userId、time、level、type、value字段
     */
    @DocumentCoreMethodRecorder()
    public getRevisionDetails(author?: string): Array<{
        userName: string;        // 修订者姓名
        userId: string;          // 修订者ID
        time: string;            // 修订时间 (ISO格式)
        level: number;           // 修订级别
        type: string;            // 修订类型：'add' | 'remove'
        value: string;           // 修订内容
    }> {
        return this.document.getRevisionDetails(author);
    }

    public getFocusRevision(mouseEvent: MouseEventHandler, pageIndex: number): IRevisionChange[] {
      return this.document.getFocusRevision(mouseEvent, pageIndex);
    }

    public getAllRevision(elemIds?: number[]): IRevisionChange[] {
        return this.document.getAllRevision(elemIds);
    }

    public getCurRevisionSetting(): IRevisionSetting {
      return this.document.getCurRevisionSetting();
    }

    public getRevisionLevelEquMode(): boolean {
        return this.document.getRevisionLevelEquMode();
    }

    @DocumentCoreMethodRecorder()
    public setRevisionLevelEquMode(bLevelEquMode: boolean): number {
        return this.document.setRevisionLevelEquMode(bLevelEquMode);
    }

    public haveRevisionChanges(): boolean {
      return this.document.haveRevisionChanges();
    }

    public isInHeaderFooter(): boolean {
      return this.document.isInHeaderFooter();
    }

    @DocumentCoreMethodRecorder()
    public checkSelectTableCellBorder(borderType: string): boolean {
      return this.document.checkSelectTableCellBorder(borderType);
    }

    public isTableCellProtected(): boolean {
      return this.document.isTableCellProtected();
    }

    @DocumentCoreMethodRecorder()
    public setTableCellProtected(bProtected: boolean): void {
      return this.document.setTableCellProtected(bProtected);
    }

    public getTableCellName(): string {
      return this.document.getTableCellName();
    }

    @DocumentCoreMethodRecorder()
    public disableHeaderFooterForPrintReview(): void {
      return this.document.disableHeaderFooterForPrintReview();
    }

    @DocumentCoreMethodRecorder()
    public restoreHeaderFooterStateFromPrintReview(): void {
      return this.document.restoreHeaderFooterStateFromPrintReview();
    }

    public getCurrentTable(tableName?: string): any {
      return this.document.getCurrentTable(tableName);
    }

    /**
     * 插入表格时，检查重名
     * @param tableName
     */
    @DocumentCoreMethodRecorder()
    public checkTableName(tableName: string): boolean {
      return this.document.checkTableName(tableName);
    }

    /**
     * 检查已经插入的table，修改名称时是否重名
     * @param tableName new name
     */
    @DocumentCoreMethodRecorder()
    public checkTableName2(tableName: string): boolean {
      return this.document.checkTableName2(tableName);
    }

    public getIncrementDatas(): any {
      const date = new Date();
      const result = this.document.createIncreDatas();
      logger.devlog({id: this.id,
        startTime: date, args: arguments, name: 'createIncrementDatas'});

      // console.log("生成增量数据：", result);
      return result;
    }

    public loadIncrementDatas(data: any): boolean {
      const date = new Date();
      const result = this.document.loadIncrementDatas(data);
      logger.devlog({id: this.id,
        startTime: date, args: arguments, name: 'loadIncrementDatas'});

      return result;
    }

    public enableAutoSave(): void {
      return this.document.enableAutoSave();
    }

    public disableAutoSave(): void {
      return this.document.disableAutoSave();
    }

    public isAutoSaveInc(): boolean {
      return this.document.isAutoSaveInc();
    }

    // public isSaveInc(): boolean {
    //   return this.document.isSaveInc();
    // }

    public getHistoryCount(): number {
      return this.document.getHistoryCount();
    }

    public isInNewControlNotSection(): boolean {
      return this.document.isInNewControlNotSection();
    }

    @DocumentCoreMethodRecorder()
    public setCellFormula(par: ITableCellFormulaPar, cellName?: string | string[]): void {
      this.document.setCellFormula(par, cellName);
    }

    public getCellFormula(cellName?: string): ITableCellFormulaPar {
      return this.document.getCellFormula(cellName);
    }

    @DocumentCoreMethodRecorder()
    public setShowTableCellNames(b: boolean): void {
      this.document.setShowTableCellNames(b);
    }

    public isShowTableCellNames(): boolean {
      return this.document.isShowTableCellNames();
    }

    /**
     * 设置当前表格单元格斜线
     * @param slashType 1: 左斜线， 2： 右斜线， -1：清除斜线
     */
    public setTableCellSlash(slashType: number): boolean {
        return this.document.setTableCellSlash(slashType);
    }

    @DocumentCoreMethodRecorder()
    public setTableCellsVertAlign(type: number): boolean {
      return this.document.setTableCellsVertAlign(type);
    }

    public getTableCellsVertAlign(): any {
      return this.document.getTableCellsVertAlign();
    }

    public getCellByName(name: string): any {
      return this.document.getCellByName(name);
    }

    public isCellsInRowOrCol(names: string[], tableName?: string): boolean {
      return this.document.isCellsInRowOrCol(names, tableName);
    }

    public getCursorStateInDocument(pointX: number, pointY: number, pageId: number):
        { result: boolean, pointX: number, pointY: number } {
      return this.document.getCursorStateInDocument(pointX, pointY, pageId);
    }

    public isInNewControlReadOnly(): boolean {
      return this.document.isInNewControlReadOnly();
    }

    public isInNewControlSignature(): boolean {
      return this.document.isInNewControlSignature();
    }

    public getProtectHeaderFooter(): boolean {
      return this.document.getProtectHeaderFooter();
    }

    public getShowHeaderBorder(): boolean {
        return this.document.getShowHeaderBorder();
    }

    @DocumentCoreMethodRecorder()
    public setShowHeaderBorder(val: boolean): void {
        this.document.setShowHeaderBorder(val);
    }

    /**
     * 将当前文档另存为docx
     * @returns docx / undefined
     */
    public saveToDocxBlob(): Promise<Blob> {
        // 序列化对象
        return generateDocx(this.document);
    }

    /**
     * 将所有表格转换为excel
     * @returns xlsx / undefined
     */
    public saveToExcelByTables(): Promise<Blob | null> {
        const tables = this.document.getAllTables();
        if (tables.length) {
            return generateExcelByTable(this.document.getAllTables());
        }
        return Promise.resolve(null);
    }

    public getShowFooterBorder(): boolean {
        return this.document.getShowFooterBorder();
    }

    @DocumentCoreMethodRecorder()
    public setShowFooterBorder(val: boolean): void {
        this.document.setShowFooterBorder(val);
    }

    public getFirstPageDiff(): boolean {
      return this.document.sectionProperty.getFirstPageDiff();
    }

    public getPageHeight(): number {
      return this.document.sectionProperty.getPageHeight();
    }

    @DocumentCoreMethodRecorder()
    public setPageMarginsHeader(headerFromTop: number): void {
      return this.document.sectionProperty.setPageMarginsHeader(headerFromTop);
    }

    @DocumentCoreMethodRecorder()
    public setPageMarginsFooter(footerFromBottom: number): void {
      return this.document.sectionProperty.setPageMarginsFooter(footerFromBottom);
    }

    @DocumentCoreMethodRecorder()
    public toggleShowHeaderFooter(type: HeaderFooterType, isShowHeaderFooter: boolean ): number {
      return this.document.toggleShowHeaderFooter(type, isShowHeaderFooter);
    }

    @DocumentCoreMethodRecorder()
    public toggleHdrFtrFirstPage(isFirstPageDiff: boolean ): void {
      return this.document.toggleHdrFtrFirstPage(isFirstPageDiff);
    }

    @DocumentCoreMethodRecorder()
    public setBDisableSaveDialog(flag: boolean): void {
      return this.document.setBDisableSaveDialog(flag);
    }

    public getBDisableSaveDialog(): boolean {
      return this.document.getBDisableSaveDialog();
    }

    @DocumentCoreMethodRecorder()
    public setPrinterServerUrl(url: string): void {
      return this.document.setPrinterServerUrl(url);
    }

    public getPrinterServerUrl(): string {
      return this.document.getPrinterServerUrl();
    }

    public getInsertFilePositionType(): number {
      return this.document.getInsertFilePositionType();
    }

    public getFileVersion(): number {
      return this.document.getFileVersion();
    }

    public getDocumentVersion(): number {
      return this.document.getDocumentVersion();
    }

    @DocumentCoreMethodRecorder()
    public setRegionContentDefaultFont(textPr: TextProperty): void {
        this.document.setRegionContentDefaultFont(textPr);
    }

    @DocumentCoreMethodRecorder()
    public resetMovingTableNewBorder(): void {
      this.document.resetMovingTableNewBorder();
    }

    @DocumentCoreMethodRecorder()
    public setNewControlTextErrorBgColor(name: string, type: number): boolean {
      return this.document.setNewControlTextErrorBgColor(name, type);
    }

    @DocumentCoreMethodRecorder()
    public updateNewControlValidInput(name: string): string {
      return this.document.updateNewControlValidInput(name);
    }

    @DocumentCoreMethodRecorder()
    public deleteEmptyParaAfterTableAndRegion(): boolean {
      return this.document.deleteEmptyParaAfterTableAndRegion();
    }

    public isEmptyParagraphAfterTableAndRegion(): boolean {
      return this.document.isEmptyParagraphAfterTableAndRegion();
    }

    public getImageFlags(): IImageFlags {
      return this.document.getImageFlags();
    }

    public isCursorInEmptyParagraph(): boolean {
      return this.document.isCursorInEmptyParagraph();
    }

    @DocumentCoreMethodRecorder()
    public setDiagonalLineCell(): boolean {
      return this.document.setDiagonalLineCell();
    }

    @DocumentCoreMethodRecorder()
    public setTableRowReadOnly(): void {
      this.document.setTableRowReadOnly();
    }

    @DocumentCoreMethodRecorder()
    public setNISTableCellTypeProps(props: any): number {
      return this.document.setNISTableCellTypeProps(props);
    }

    @DocumentCoreMethodRecorder()
    public setNISTableCellContentProps(props: ITableCellContentProps): number {
      return this.document.setNISTableCellContentProps(props);
    }

    public getNISTableCellContentProps(): ITableCellContentProps {
      return this.document.getNISTableCellContentProps();
    }

    public getNISCellBounds(): IDrawSelectionsCell[] {
      return this.document.getNISCellBounds();
    }

    public getTableCellByXY(pointX: number, pointY: number, pageId: number): TableCell {
      return this.document.getTableCellByXY(pointX , pointY, pageId);
    }

    public getNISTableMode(): number {
        return this.document.getNISTableMode();
    }

    public isNISEditMode(): boolean {
        return this.document.isNISEditMode();
    }

    public isNISTemplateMode(): boolean {
        return this.document.isNISTemplateMode();
    }

    public isInNISTable(): boolean {
      return this.document.isInNISTable();
    }

    public getTableCellProps(): ITableCellProperty {
      return this.document.getTableCellProps();
    }

    public getNISTableCellType(): number {
      return this.document.getNISTableCellType();
    }

    @DocumentCoreMethodRecorder()
    public setTableCellProps(props: ITableCellProperty): number {
      return this.document.setTableCellProps(props);
    }

    @DocumentCoreMethodRecorder()
    public setNISTableMode(mode: number): number {
      return this.document.setNISTableMode(mode);
    }

    public getCurNISCell(): TableCell {
      return this.document.getCurNISCell();
    }

    public isTableBorder(pointX: number, pointY: number, pageIndex: number): any {
      return this.document.isTableBorder(pointX, pointY, pageIndex);
    }

    public isExistSignCellInCurRow(): boolean {
      return this.document.isExistSignCellInCurRow();
    }

    public getSignCellsInCurRow(): TableCell[] {
      return this.document.getSignCellsInCurRow();
    }

    @DocumentCoreMethodRecorder()
    public deleteNISTable(sName: string): number {
      const date = new Date();
      const result = this.document.deleteTableByName(sName);
      logger.devlog({id: this.id,
        startTime: date, args: arguments, name: 'deleteNISTable'});
      return result;
    }

    public getNISTableNames(): string[] {
      return this.document.getNISTableNames();
    }

    @DocumentCoreMethodRecorder()
    public setNISTableColID(table: TableBase, colInfo: string[]): boolean {
      const date = new Date();
      const res = this.document.setNISTableColID(table, colInfo);
      logger.devlog({id: this.id,
        startTime: date, args: arguments, name: 'setNISTableColInfo'});
      return res;
    }

    public getNISTableColID(table: TableBase): string {
      const date = new Date();
      const res = this.document.getNISTableColIDs(table);
      logger.devlog({id: this.id,
        startTime: date, args: arguments, name: 'getNISTableColNumber'});
      return res.toString();
    }

    public filterColID(sName: string, serialNumberJson: any): string {
      return this.document.filterColID(sName, serialNumberJson);
    }

    public isNewNISTable(sTableName: string): number {
        return this.document.isNewNISTable(sTableName);
    }

    @DocumentCoreMethodRecorder()
    public hideNISFirstRow(sName: string, bHide: boolean): number {
        return this.document.hideNISFirstRow(sName, bHide);
    }

    public getNISCurrentRowID(): string {
        return this.document.getNISCurrentRowID();
    }

    public getTableInfo(tableName: string, sJson: any): string {
      return this.document.getTableInfo(tableName, sJson);
    }

    public setRowInfo(sTableName: string, sInfo: any): number {
      return this.document.setRowInfo(sTableName, sInfo);
    }

    public getRowInfo(sTableName: string, rowID: string, sParam: any): string {
      return this.document.getRowInfo(sTableName, rowID, sParam);
    }

    public getNISAllRowsID(sName: string): string {
        return this.document.getNISAllRowsID(sName);
    }

    public getNISLastRowID(sName: string): string {
        return this.document.getNISLastRowID(sName);
    }

    public getNISFirstRowID(sName: string): string {
        return this.document.getNISFirstRowID(sName);
    }

    public moveRowToPosition(sName: string, movedRow: string, PositionRow: string, direct: number): number {
        return this.document.moveRowToPosition(sName, movedRow, PositionRow, direct);
    }

    public sortRow(sName: string, rowJson: any): number {
        return this.document.sortRow(sName, rowJson);
    }

    public sortRowInRange(sName: string, beginRow: string, rowJson: any): number {
        return this.document.sortRowInRange(sName, beginRow, rowJson);
    }

    @DocumentCoreMethodRecorder()
    public setNISRowProp(sName: string, rowID: string, prop: INISRowProperty): number {
      const date = new Date();
      const res = this.document.setNISRowProp(sName, rowID, prop);
      logger.devlog({id: this.id,
        startTime: date, args: arguments, name: 'setNISRowProp'});
      return res;
    }

    @DocumentCoreMethodRecorder()
    public setNISRowText(sName: string, rowID: string, sJson: any): number {
      const date = new Date();
      const res = this.document.setNISRowText(sName, rowID, sJson);
      logger.devlog({id: this.id,
        startTime: date, args: arguments, name: 'setNISRowText'});
      return res;
    }

    public getNISRowProp(sName: string, sJson: string): string {
        return this.document.getNISRowProp(sName, sJson);
    }

    public getNISRowCreator(sName: string, rowID: string): string {
        return this.document.getNISRowCreator(sName, rowID);
    }

    public setNISRowCreator(sName: string, rowID: string, creator: string): number {
      const date = new Date();
      const res = this.document.setNISRowCreator(sName, rowID, creator);
      logger.devlog({id: this.id,
        startTime: date, args: arguments, name: 'setNISRowCreator'});
      return res;
    }

    public getHeaderRowTextByColID(sTable: string, colIDs: string[]): string {
      return this.document.getHeaderRowTextByColID(sTable, colIDs);
    }

    public getNISRowText(sName: string, rowIDs: string[]): string {
        return this.document.getNISRowText(sName, rowIDs);
    }

    @DocumentCoreMethodRecorder()
    public protectNISRows(sName: string, rowIDs: string[], bFlag: boolean): boolean {
        return this.document.protectNISRows(sName, rowIDs, bFlag);
    }

    @DocumentCoreMethodRecorder()
    public protectNISCurrentRow(bFlag: boolean): boolean {
        return this.document.protectNISCurrentRow(bFlag);
    }

    @DocumentCoreMethodRecorder()
    public selectNISRow(sName: string, rowID: string): boolean {
        return this.document.selectNISRow(sName, rowID);
    }

    public getSelectedNISRowNumber(): number {
        return this.document.getSelectedNISRowNumber();
    }

    @DocumentCoreMethodRecorder()
    public cancelSelecteNISRow(): boolean {
        return this.document.cancelSelecteNISRow();
    }

    public isCurrentNISRowCanDelete(): boolean {
        return this.document.isCurrentNISRowCanDelete();
    }

    public isCurrentNISRowSigned(): number {
        return this.document.isCurrentNISRowSigned();
    }

    public isHiddenFirstNISRow(rowID: string): boolean {
      return this.document.isHiddenFirstNISRow(rowID);
  }

    @DocumentCoreMethodRecorder()
    public deleteNISCurrentRow(): boolean {
      const date = new Date();
      const res = this.document.deleteNISCurrentRow();
      logger.devlog({id: this.id,
        startTime: date, args: arguments, name: 'deleteNISCurrentRow'});
      return res;
    }

    @DocumentCoreMethodRecorder()
    public deleteNISRows(sName: string, rowIDs: string[]): boolean {
      const date = new Date();
      const res = this.document.deleteNISRows(sName, rowIDs);
      logger.devlog({id: this.id,
        startTime: date, args: arguments, name: 'deleteNISRows'});
      return res;
    }

    @DocumentCoreMethodRecorder()
    public setNISSignCellText(sName: string, sJson: string): boolean {
        const res = this.document.setNISSignCellText(sName, sJson);
        return res;
    }

    @DocumentCoreMethodRecorder()
    public deleteNISSignCellsText(sName: string, sJson: string): boolean {
      const date = new Date();
      const res = this.document.deleteNISSignCellsText(sName, sJson);
      logger.devlog({id: this.id,
        startTime: date, args: arguments, name: 'deleteNISSignCellsText'});
      return res;
    }

    @DocumentCoreMethodRecorder()
    public async signCurrentNISRow(sJson: string): Promise<boolean> {
      const date = new Date();
      const res = await this.document.signCurrentNISRow(sJson);
      // logger.devlog({id: this.id,
      //   startTime: date, args: arguments, name: 'signCurrentNISRow'});
      return res;
    }

    @DocumentCoreMethodRecorder()
    public async signNISRows(sName: string, sJson: string): Promise<boolean> {
      // const date = new Date();
      const res = await this.document.signNISRows(sName, sJson);
      return res;
    }

    @DocumentCoreMethodRecorder()
    public deleteCurrentNISRowSign(sJson: string): boolean {
      const date = new Date();
      const res = this.document.deleteCurrentNISRowSign(sJson);
      logger.devlog({id: this.id,
        startTime: date, args: arguments, name: 'deleteCurrentNISRowSign'});
      return res;
    }

    @DocumentCoreMethodRecorder()
    public deleteNISRowsSign(sName: string, sJson: any): boolean {
      const date = new Date();
      const res = this.document.deleteNISRowsSign(sName, sJson);
      logger.devlog({id: this.id,
        startTime: date, args: arguments, name: 'deleteNISRowsSign'});
      return res;
    }

    public getNISSignNumber(sName: string, sJson: string): number {
      return this.document.getNISSignNumber(sName, sJson);
    }

    public getNISSignStatus(sName: string, sJson: string): number {
      return this.document.getNISSignStatus(sName, sJson);
    }

    @DocumentCoreMethodRecorder()
    public setNISSignStatus(sName: string, sJson: any): number {
      const date = new Date();
      const res = this.document.setNISSignStatus(sName, sJson);
      logger.devlog({id: this.id,
        startTime: date, args: arguments, name: 'setNISSignStatus'});
      return res;
    }

    public filterNISSignRow(sName: string, sJson: any): string[] {
      const date = new Date();
      const res = this.document.filterNISSignRow(sName, sJson);
      logger.devlog({id: this.id,
        startTime: date, args: arguments, name: 'filterNISSignRow'});
      return res;
    }

    public getSignNameByRow(sName: string, rowID: string): string[] {
        const res = this.document.getSignNameByRow(sName, rowID);

        return res;
    }

    @DocumentCoreMethodRecorder()
    public insertSumRowAtCurrentRow(sJson: any): boolean {
        const res = this.document.insertSumRowAtCurrentRow(sJson);
        return res;
    }

    @DocumentCoreMethodRecorder()
    public insertSumRows(sName: string, sJson: any): string {
        const res = this.document.insertSumRows(sName, sJson);
        return res;
    }

    @DocumentCoreMethodRecorder()
    public deleteSumRows(sName: string, sJson: any): boolean {
      const res = this.document.deleteSumRows(sName, sJson);
      return res;
    }

    @DocumentCoreMethodRecorder()
    public setRowSumStatus(sName: string, sJson: any): boolean {
      const res = this.document.setRowSumStatus(sName, sJson);
      return res;
    }

    public getRowSumStatus(sName: string, sJson: any): string {
        return this.document.getRowSumStatus(sName, sJson);
    }

    public getCurrentRowSumStatus(): number {
        return this.document.getCurrentRowSumStatus();
    }

    @DocumentCoreMethodRecorder()
    public cleanRowSumStatus(sName: string, sJson: any): boolean {
      const res = this.document.cleanRowSumStatus(sName, sJson);
      return res;
    }

    public getLastSumRow(sName: string): string {
      return this.document.getLastSumRow(sName);
    }

    public filterSumRow(sName: string, sJson: any): string {
      return this.document.filterSumRow(sName, sJson);
    }

    public getNISRowsTextByJson(sName: string, sJson: any): string {
      return this.document.getNISRowsTextByJson(sName, sJson);
    }

    @DocumentCoreMethodRecorder()
    public insertNISRowAtCurrentRow(sJson: any): boolean {
      const date = new Date();
      const res = this.document.insertNISRowAtCurrentRow(sJson);
      logger.devlog({id: this.id,
        startTime: date, args: arguments, name: 'insertNISRowAtCurrentRow'});
      return res;
    }

    @DocumentCoreMethodRecorder()
    public insertNISRows(sName: string, sJson: any): string {
      const date = new Date();
      const res = this.document.insertNISRows(sName, sJson);
      logger.devlog({id: this.id,
        startTime: date, args: arguments, name: 'insertNISRows'});
      return res;
    }

    @DocumentCoreMethodRecorder()
    public setNISCellType(sName: string, rowID: string, colID: string, nType: number): boolean {
        const res = this.document.setNISCellType(sName, rowID, colID, nType);
        return res;
    }

    public setBPCellFormat(sName: string, rowID: string, colID: string, format: string): number {
      return this.document.setBPCellFormat(sName, rowID, colID, format);
    }

    public setBPCellValue(sName: string, rowID: string, colID: string, value: string): number {
      return this.document.setBPCellValue(sName, rowID, colID, value);
    }

    public judgeTimeAndBpCell(sTable: string, sRowID: string): string {
      return this.document.judgeTimeAndBpCell(sTable, sRowID);
    }

    public getNISCellType(sName: string, rowID: string, colID: string): number {
        return this.document.getNISCellType(sName, rowID, colID);
    }

    @DocumentCoreMethodRecorder()
    public setNISCellLayout(sName: string, rowID: string, colID: string, sJson: string): boolean {
        const res = this.document.setNISCellLayout(sName, rowID, colID, sJson);
        return res;
    }

    @DocumentCoreMethodRecorder()
    public setNISCellText(sName: string, sJson: string): boolean {
        const res = this.document.setNISCellText(sName, sJson);
        return res;
    }

    public getNISCellText(sName: string, sJson: string): string {
        return this.document.getNISCellText(sName, sJson);
    }

    @DocumentCoreMethodRecorder()
    public setNISCellProp(sName: string, rowID: string, colID: string, sJson: string): boolean {
        const res = this.document.setNISCellProp(sName, rowID, colID, sJson);
        return res;
    }

    public getNISCellProp(sName: string, rowID: string, colID: string, sPropName: string): string {
        return this.document.getNISCellProp(sName, rowID, colID, sPropName);
    }

    public getNISCellsPropOfFirstRow(sName: string, sPropName: string): string[] {
        return this.document.getNISCellsPropOfFirstRow(sName, sPropName);
    }

    @DocumentCoreMethodRecorder()
    public setNISDateCellFormat(
        sName: string, rowID: string, colID: string, nType: number, sFormat: string): boolean {
        const res = this.document.setNISDateCellFormat(sName, rowID, colID, nType, sFormat);
        return res;
    }

    @DocumentCoreMethodRecorder()
    public setNISDateCellProp(
        sName: string, rowID: string, colID: string, sJson: string): boolean {
        const res = this.document.setNISDateCellProp(sName, rowID, colID, sJson);
        return res;
    }

    @DocumentCoreMethodRecorder()
    public setNISDateCellValue(sName: string, rowID: string, colID: string, sValue: string): boolean {
        const res = this.document.setNISDateCellValue(sName, rowID, colID, sValue);
        return res;
    }

    @DocumentCoreMethodRecorder()
    public setNISTimeCellFormat(
        sName: string, rowID: string, colID: string, nType: number, sFormat: string): boolean {
        const res = this.document.setNISTimeCellFormat(sName, rowID, colID, nType, sFormat);
        return res;
    }

    @DocumentCoreMethodRecorder()
    public setNISTimeCellValue(sName: string, rowID: string, colID: string, sValue: string): boolean {
        const res = this.document.setNISTimeCellValue(sName, rowID, colID, sValue);
        return res;
    }

    @DocumentCoreMethodRecorder()
    public setNISCompoundCellCodeAndValueByArray(
        sName: string, rowID: string, colID: string, sJson: string, nType?: number): boolean {
        const res = this.document.setNISCompoundCellCodeAndValueByArray(sName, rowID, colID, sJson, nType);
        return res;
    }

    @DocumentCoreMethodRecorder()
    public setNISCompoundCellSeparator(sName: string, rowID: string, colID: string, sSeparator: string): boolean {
        const res = this.document.setNISCompoundCellSeparator(sName, rowID, colID, sSeparator);
        return res;
    }

    public getNISCompoundCelllCurrentValue(sName: string, rowID: string, colID: string, nType?: number): string {
        return this.document.getNISCompoundCelllCurrentValue(sName, rowID, colID, nType);
    }

    public getNISCompoundCelllCurrentCode(sName: string, rowID: string, colID: string, nType?: number): string {
        return this.document.getNISCompoundCelllCurrentCode(sName, rowID, colID, nType);
    }

    @DocumentCoreMethodRecorder()
    public setNISNumCellMaxValue(sName: string, rowID: string, colID: string, maxValue: number): number {
        return this.document.setNISNumCellMaxValue(sName, rowID, colID, maxValue);
    }

    public getNISNumCellMaxValue(sName: string, rowID: string, colID: string): number {
        return this.document.getNISNumCellMaxValue(sName, rowID, colID);
    }

    @DocumentCoreMethodRecorder()
    public setNISNumCellMinValue(sName: string, rowID: string, colID: string, minValue: number): number {
        return this.document.setNISNumCellMinValue(sName, rowID, colID, minValue);
    }

    public getNISNumCellMinValue(sName: string, rowID: string, colID: string): number {
        return this.document.getNISNumCellMinValue(sName, rowID, colID);
    }

    @DocumentCoreMethodRecorder()
    public setNISNumCellPrecision(sName: string, rowID: string, colID: string, precision: number): number {
        return this.document.setNISNumCellPrecision(sName, rowID, colID, precision);
    }

    public getNISNumCellPrecision(sName: string, rowID: string, colID: string): number {
        return this.document.getNISNumCellPrecision(sName, rowID, colID);
    }

    @DocumentCoreMethodRecorder()
    public setNISQuickCellValueByArray(sName: string, rowID: string, colID: string, sJson: any): boolean {
      const res = this.document.setNISQuickCellValueByArray(sName, rowID, colID, sJson);
      return res;
    }

    public isNISEditModeNotInTable(): boolean {
      return this.document.isNISEditModeNotInTable();
    }

    public isInNumberCellOfNISTable(): boolean {
      return this.document.isInNumberCellOfNISTable();
    }

    @DocumentCoreMethodRecorder()
    public setTableHeaderReadOnly(sName: string, nMode: boolean): number {
        return this.document.setTableHeaderReadOnly(sName, nMode);
    }

    public forbidMoveTableBorder(bFixed: boolean): boolean {
        this.document.setForbidMoveTableBoder(bFixed);
        return true;
    }

    public isForbidMoveTableBoder(): boolean {
        return this.document.isForbidMoveTableBoder();
    }

    public canInsertTextInNISTable(): boolean {
      const cell = this.document.getCurNISCell();
      const cellProps = cell ? cell.getNISProperty2() : null;
      if (cellProps && (NISTableCellType.Number === cellProps.type
              || NISTableCellType.Time === cellProps.type
              || NISTableCellType.Date === cellProps.type
              || NISTableCellType.BloodPressure === cellProps.type
              || NISTableCellType.Signature === cellProps.type
              || (NISTableCellType.List === cellProps.type && NISSelectType.Dropdown === cellProps.selectType) )
          ) {
        return false;
      }

      const table = this.document.getCurrentTable();
      if (table && table.isNISTable() && this.document.isSelectionUse() && table.isCellSelection()) {
        return false;
      }

      return true;
    }

    public getTableByName(name: string): any {
        return this.document.getTableByName(name);
    }

    public createNewEmptyDocumentForInteface(bDefaultText?: boolean): Document {
      const doc = new Document();
      if (bDefaultText) {
        doc.setDefaultFont(this.getDefaultTextProperty());
      }

      return doc;
    }

    public isLockRefresh(): boolean {
        return this.document.isLockRefresh();
    }

    /**
     * 获取指定表格的行数
     * @param sTableName 表格名称
     * @returns 表格的行数，如果表格不存在则返回0
     */
    public getTableRowCount(sTableName: string): number {
        const table = this.document.getTableByName(sTableName);
        if (!table) {
            return 0;
        }
        return table.content.length;
    }

    /**
     * 获取指定表格的列数
     * @param sTableName 表格名称
     * @returns 表格的列数，如果表格不存在则返回0
     */
    public getTableColumnCount(sTableName: string): number {
        const table = this.document.getTableByName(sTableName);
        if (!table) {
            return 0;
        }
        return table.cols;
    }

    public setLockRefresh(bFlag: boolean): number {
      return this.document.setLockRefresh(bFlag);
    }

    public isRecalcPages(): number {
      return this.document.isRecalcPages()
    }

    public resetRecalcPages(): void {
      this.document.resetRecalcPages();
    }

    public async recalculatePageForFileReader(): Promise<void> {
      const res = await this.document.recalculatePageForFileReader();
      return res;
    }
  
    public setImageVertAlign(type: number): number {
      return this.document.setImageVertAlign(type);
    }

    public setSourceDataBind(name: string, props: IExternalDataProperty): number {
      return this.document.setSourceDataBind(name, props);
    }

    public getSourceDataBind(name: string): IExternalDataProperty {
      return this.document.getSourceDataBind(name);
    }

    public enableSouceBindInRegion(sRegion:string,nControl:number):number{
      return this.document.enableSouceBindInRegion(sRegion,nControl);
    }

    public getSourceBindJson(): string {
        return this.document.getSourceBindJson();
    }

    public getBarcodeName(name?: string): string {
      return this.document.getDrawingObjects()
        .makeUniqueImageName(ParaElementType.ParaBarcode, name || 'barcode');
    }

    public setRelayRecalc(bRelay: boolean) : void {
      return this.document.setRelayRecalc(bRelay);
    }

    public initElementMonitor(config: any): void {
      this.document.initElementMonitor(config);
    }

    public getElementMonitor(): any {
      return this.document.getElementMonitor();
    }

    public startMonitorElement(element: any): void {
      this.document.startMonitorElement(element);
    }

    public endMonitorElement(element: any): void {
      this.document.endMonitorElement(element);
    }

    public sendMonitorData(bInterface?: boolean): void {
      this.document.sendMonitorData(bInterface);
    }

    public addMonitorElementEvent(element: any, type: any): void {
      ;
    }

    public getParaButtonName(name: string = 'structbutton1'): string {
      return this.document.getParaButtonName(name);
    }

    public checkParaButtonName(name: string): boolean {
      return this.document.checkParaButtonName(name);
    }

    public addParaButton(props: any): string {
      return this.document.addParaButton(props);
    }

    public setParaButtonProps(name: string, props: any): number {
      return this.document.setParaButtonProps(name, props);
    }

    public getParaButtonProps(name: string): IParaButtonProp {
      return this.document.getParaButtonProps(name);
    }

    public getParaButtonByName(name: string): ParaButton {
      return this.document.getParaButtonByName(name);
    }

    public isSetHeadFooterInterface(): boolean {
        return this.document.isSetHeadFooterInterface();
    }

    public getTableColInfo(sTable: string, colID: string): string {
        return this.document.getTableColInfo(sTable, colID);
    }
}
