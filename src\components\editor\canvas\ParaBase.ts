import { IDocumentParagraph } from '../../../model/ParaProperty';
import { ICanvasProps } from './common';
import {ParagraphUI} from './Paragraph';

interface IProps {
    content: IDocumentParagraph;
    className?: string;
    scale?: number;
    pageIndex?: number;
    host?: any;
    cellId?: number;
    bFromHeaderFooter?: boolean;
}

export class ParaBaseUI {
    private props: IProps;
    private documentCore: any;
    private host: any;
    private ctx: CanvasRenderingContext2D;
    constructor(props: ICanvasProps) {
        this.props = props.content;
        this.host = props.host;
        this.props.pageIndex = props.pageIndex;
        this.documentCore = props.host.documentCore;
        this.ctx = props.ctx;
        this.render();
    }

    private render(): void {
        const { content, cellId } = this.props;
        this.renderImage(content.images);
        this.renderParagraph(content, cellId);
    }

    private renderImage(images: any[]): any {
        if (!images || !images.length) {
            return null;
        }
        const { pageIndex, bFromHeaderFooter } = this.props;
        const host = this.host;
        const ctx = this.ctx;
        images.forEach((item, index) => {
            const img = new Image();   // 创建一个<img>元素
            img.src = item.src; // 设置图片源地址
            img.onload = () => {
                ctx.drawImage(img, item.positionX, item.positionY, item.width, item.height);
            };
            // return (
            //     <Image
            //         key={item.id}
            //         id={pageIndex + desc + item.id}
            //         item={item}
            //         editorContainer={editorContainer}
            //         documentCore={documentCore}
            //         handleRefresh={handleRefresh}
            //         host={host}
            //     />
            // );
        });
    }

    private renderParagraph(item: IDocumentParagraph, id?: any): any {
        if (!item) {
            return null;
        }
        const {pageIndex} = this.props;
        const content: any = {content: item.content, pageIndex, startLine: item.startLine, endLine: item.endLine};
        content.lines = item.lines;
        content.cellId = id;
        content.id = item.id;
        content.index = item.index;
        const props = {content, ctx: this.ctx, host: this.host, pageIndex};
        this.renderTextBorder(item.points, id);
        const obj = new ParagraphUI(props);

        // return (
        //     <React.Fragment>
        //         {this.renderTextBorder(item.points, item.id)}
        //         <Paragraph
        //             id={item.id}
        //             cellId={id}
        //             key={item.index + item.id}
        //             index={item.index}
        //             pageIndex={this.props.pageIndex}
        //             content={item.content}
        //             lines={item.lines}
        //             startLine={item.startLine}
        //             endLine={item.endLine}
        //             documentCore={this.props.host.documentCore}
        //         />
        //     </React.Fragment>
        // );
    }

    private renderTextBorder(points: any[], id: any): any {
        // const { points } = this.props;
        if (!points || !points.length) {
            return null;
        }
        const style = {
            fill: 'transparent',
            stroke: '#000',
            strokeWidth: '1px'
        };
        const ctx = this.ctx;
        points.forEach((point, index) => {
            let path: string = '';
            point.forEach((item) => {
                path += item.x + ',' + (item.y + 4) + ' ';
            });
            // return (
            //     <polygon key={index + id} points={path} style={style} />
            // );
        });
    }
}
