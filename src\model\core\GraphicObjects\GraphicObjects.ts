import { DrawingObjectsController } from '../DrawingObjectsController';
import { IMouseEvent } from '../../../common/MouseEventHandler';
import { DocumentSectionType, ResultType } from '../../../common/commonDefines';
import Paragraph from '../Paragraph';
import ParaPortion from '../Paragraph/ParaPortion';
// import {parse as circularParse, stringify as circularStringify} from 'flatted/esm';
import ParaDrawing, { ParaEquation } from '../Paragraph/ParaDrawing';
import { ParaElementType } from '../Paragraph/ParagraphContent';
import { GraphicPage } from './GraphicPage';
import { ParaElementBase } from '../Paragraph/ParaElementBase';
import { PortionSelection } from '../Selection';
import { ChangeGraphicObjectsAddParaImage, ChangeGraphicObjectsRemoveParaImage } from './GraphicObjectsChange';
import ContentChanges, { ContentChangesElement } from '../ContentChanges';
import History from '../History';
import Document from '../Document';
import { Region } from '../Region';
import { Table } from '../Table';

export class GraphicObjects {

  public drawingObjectsController: DrawingObjectsController;
  private document: Document;
  // whole image map
  private drawingObjects: Map<string, ParaDrawing>;

  // current image element
  private selectionInfo: string;
  private contentChanges: ContentChanges;

  private graphicPages: GraphicPage[];

  private graphicObjectCaches: ParaDrawing[];

  constructor(doc: Document) {
    this.document = doc;
    this.drawingObjects = new Map();
    this.selectionInfo = null;
    this.contentChanges = new ContentChanges();
    this.graphicObjectCaches = [];
  }

  /**
   * return paradrawing of current image
   */
  public getSelectedImage(): ParaDrawing {
    let selectedImage = null;

    // getCurrentParagraph() is both in paragraph and table
    const para = this.document.getCurrentParagraph() as Paragraph;
    // console.log(para)

    const portion = para.content[para.curPos.contentPos] as ParaPortion;
    selectedImage = portion.content[portion.selection.startPos] as ParaDrawing;
    if (selectedImage == null) { // editable image
      selectedImage = portion.content[portion.selection.startPos - 1];
      if (selectedImage instanceof ParaDrawing) {
        portion.selection.startPos--;
        portion.selection.endPos--;
      }
    }
    if (!this.selectionInfo) {

      return this.getClickImage(para, portion);
    } else if (this.selectionInfo !== selectedImage.name) {

// tslint:disable-next-line: no-console
        console.warn('Selected image is not correctly retrieved');
        return null;

        // let correctedRunContentPos = para.curPos.contentPos + 1;
        // let correctedPortion = para.content[correctedRunContentPos];
        // let correctedSelectedImage = correctedPortion.content[0] as ParaDrawing;

        // if (this.selectionInfo.getAttribute("name") === correctedSelectedImage.name) {

        //     console.log("weeeep")
        //     selectedImage = correctedSelectedImage;

        //     correctedPortion.selection.bUse = true;
        //     correctedPortion.selection.startPos = 1;
        //     correctedPortion.selection.endPos = 2;

        //     // correct incorrect props
        //     para.curPos.contentPos += 1;
        //     portion.selection.bUse = false;
        //     portion.selection.startPos = 0;
        //     portion.selection.endPos = 0;

        // } else {
        //     console.warn("Selected image is not correctly retrieved");
        //     return null;
        // }
    }

    return selectedImage;
  }

  /**
   * 根据名称获取绘图
   * @param name 对象名称
   */
  public getDrawingByName(name: string): ParaDrawing {
    return this.drawingObjects.get(name);
  }

  public getDrawingBySrc(src: string): ParaDrawing {
    const drawingObjects = this.drawingObjects;
    for (const drawingInstance of drawingObjects.values()) {
      const drawingSrc = drawingInstance.src;
      if (src === drawingSrc) {
        return drawingInstance;
      }
    }
  }

  public getImageType(): number {
    const curParaDrawing = this.getSelectedImage();
    let type: number = 0;
    if (curParaDrawing) {
        type = curParaDrawing.type;
    }
    return type;
  }

  public isParaEquation(image: ParaDrawing): boolean {
    return (image instanceof ParaEquation);
  }

  public setDrawPreload(actImage: ParaDrawing, flag: boolean): void {
    const flag1 = !flag;
    const images = this.drawingObjects;
    for (const [name, image] of images) {
      if (actImage !== image) {
        image.setPreload(flag1);
      } else {
        image.setPreload(flag);
      }
    }
  }

  public makeUniqueImageName(type: ParaElementType, name?: string): string {
    if (this.checkUniqueImageName(name) === true) {
        return name;
    } else {
      // generate image name
      let newImageName = NEW_IMAGE_NAME.get(type);

      for (let number = 1; number < Number.MAX_SAFE_INTEGER; number++) {
        const tempName = newImageName + number;
        if (this.drawingObjects.has(tempName) === false) {
          newImageName = tempName;
          break;
        }
      }
      return newImageName;
    }
  }

  /**
   * 图片属性页，检查(修改后的)图片名称 是否与"非当前选中图片"列表的其他图片重名
   */
  public checkUniqueImageNameOtherThanSelectedImage(name: string): boolean {
    if (null != name) {
      // if (!isValidName(name)) {
      //   return false;
      // }
      // const curParaDrawing = DocumentContentBase.drawingObjects.getSelectedImage() as ParaDrawing;
      const curParaDrawing =  this.getSelectedImage() as ParaDrawing;

      if (name === curParaDrawing?.name) {
          return true;
      } else {
          // whole image name map

          return !this.drawingObjects.has(name);
      }
    }

    return false;
  }

  public checkUniqueImageName(name: string): boolean {
    if (null != name && '' !== name) {
      return !this.drawingObjects.has(name);
    }

    return false;
  }

  public setEquationElem(name: string, elem: string): number {
    const drawing = this.drawingObjects.get(name) as ParaEquation;
    if (!drawing) {
      return ResultType.Failure;
    }
    if (drawing.equationElem === elem) {
      return ResultType.UnEdited;
    }
    const src = this.convertSVGToImageString(elem);
    drawing.src = src;
    drawing.equationElem = elem;

    return ResultType.Success;
  }

  public applyDrawingProps(width: number, height: number, preserveAspectRatio: boolean = true,
                           name: string = null): void {

    // this.drawingObjectsController.applyDrawingProps(item, width, height);

    // let document = documentCore.document;
    // let item = document.content[indexCollection[0]].content[indexCollection[1]].content[indexCollection[2]];
    // item.width = width;
    // item.widthVisible = item.width;
    // item.height = height;

    // console.log(document);

    // console.log(portion)
    // console.log(portion.selection.startPos)
    const curParaDrawing = this.getSelectedImage();
    let result = false;
    // console.log(curParaDrawing);
    if (curParaDrawing && (curParaDrawing.isImage() || curParaDrawing.isMedEquation()) ) {
      // curParaDrawing.width = width;
      // curParaDrawing.widthVisible = width;
      // curParaDrawing.height = height;
      // curParaDrawing.preserveAspectRatio = preserveAspectRatio;
      // if (name != null) {
      //     curParaDrawing.name = name;
      // }
      result = curParaDrawing.setWidth(width) || result;
      result = curParaDrawing.setHeight(height) || result;
      this.setAspectRatio(preserveAspectRatio);
      if (name != null) {
        this.setName(curParaDrawing.name, name);
      }
    } else {
      console.log('image is not selected!');
    }

    if ( true === result ) {
      this.document.setDirty();
      this.document.recalculate();
      this.document.updateCursorXY();
    }

    // predicament: if not recalculate(), {item.positionY + item.height} can never auto update correctly
    // document.recalculate();
  }

  /**
   * change image's src and width/height
   */
  public applyDrawingHref(src: string, newWidth?: number, newHeight?: number): void {
    const curParaDrawing = this.getSelectedImage();
    let result = false;
    if (curParaDrawing) {
        // curParaDrawing.src = src;
        result = curParaDrawing.setSrc(src) || result;
        result = curParaDrawing.setWidth(newWidth) || result;
        result = curParaDrawing.setHeight(newHeight) || result;
        // if (newWidth) {
        //     curParaDrawing.width = newWidth;
        //     curParaDrawing.widthVisible = newWidth;
        // }
        // if (newHeight) {
        //     curParaDrawing.height = newHeight;
        // }
    }

    if ( true === result ) {
      this.document.setDirty();
      this.document.recalculate();
      this.document.updateCursorXY();
    }
  }

  public applyDrawingSvgElemStr(svgElemStr: string): void {
    const curParaDrawing = this.getSelectedImage();
    if (curParaDrawing instanceof ParaEquation) {
      // curParaDrawing.equationElem = svgElemStr;
      curParaDrawing.setSvgElemStr(svgElemStr);

      this.document.setDirty();
      this.document.recalculate();
      this.document.updateCursorXY();
    }
  }

  public setDrawingProp(name: string, props: any): number {
    const drawing = this.getDrawingByName(name);
    if (!drawing) {
      return ResultType.Failure;
    }
    const result = drawing.setDrawingProp(props);
    if (props.name != null && name !== props.name) {
      this.drawingObjects.delete(name);
      this.drawingObjects.set(props.name, drawing);
    }

    if (result === ResultType.Success) {
      this.document.setDirty();
      this.document.recalculate();
      this.document.updateCursorXY();
    }
    return result;
  }

  public applyDrawingLocks(sizeLocked: boolean, deleteLocked: boolean): void {

    const curParaDrawing = this.getSelectedImage();
    if (curParaDrawing) {
        // curParaDrawing.sizeLocked = sizeLocked;
        // curParaDrawing.deleteLocked = deleteLocked;
        curParaDrawing.setSizeLocked(sizeLocked);
        curParaDrawing.setDeleteLocked(deleteLocked);
        this.document.setDirty();
    }

    // consider separate to new function?
    // this.document.recalculate();
    // this.document.updateCursorXY();
  }

  public getAllImages(): ParaDrawing[] {
    const drawings = this.drawingObjects;
    const res = [];
    for (const [name, draw] of drawings) {
      res.push(draw);
    }
    return res;
  }

  public setAspectRatio(preserveAspectRatio: boolean): void {
    const curParaDrawing = this.getSelectedImage();
    if (curParaDrawing && curParaDrawing.preserveAspectRatio !== preserveAspectRatio) {
        // curParaDrawing.preserveAspectRatio = preserveAspectRatio;
        curParaDrawing.setPreserveAspectRatio(preserveAspectRatio);
        this.document.setDirty();
    }
  }

  public mouseButtonDown(pointX?: number, pointY?: number, pageIndex?: number, mouseEvent?: IMouseEvent): void {
    // console.log('graphicobjects mouse down');

    const para = this.document.getCurrentParagraph() as Paragraph;

    const portion = para.content[para.curPos.contentPos] as ParaPortion;
    // console.log(para, portion)

    const nextPortion = para.content[para.curPos.contentPos + 1];

    const uncheckedIndex = portion.selection.startPos;
    let selectionIndex = uncheckedIndex;
    // console.log(uncheckedIndex, portion.content.length);

    if (uncheckedIndex === 0) {
      // when startpos is 0(insert image at vert first and click on left part of it), may have multiple empty portions

      let correctPortion = para.content[uncheckedIndex];
      let addedIndex = 1;

      // if the first portion is empty
      if (para.content[0].content.length <= 0) {
        // XX|OOO, XX -> portion 1, OOO -> portion 2, | is cursor; para.curPos.contentPos: 1
        let correctedRunContentPos = para.curPos.contentPos + addedIndex;
        correctPortion = para.content[correctedRunContentPos];

        // find the first non-empty portion if any
        while (correctPortion.content.length === 0) {
          addedIndex++;
          correctedRunContentPos += 1;
          correctPortion = para.content[correctedRunContentPos];

          if (addedIndex > 5000) {
// tslint:disable-next-line: no-console
              console.warn('Maximum addedIndex reached.');
              break;
          }
        }

        const correctedSelectedImage = correctPortion.content[0] as ParaDrawing;

        if (this.selectionInfo === correctedSelectedImage.name) {
          selectionIndex = 0;

          // paragraph's selection need to change as well
          para.selection.startPos = correctedRunContentPos;
          para.selection.endPos = correctedRunContentPos;

          correctPortion.selection.bUse = true;
          correctPortion.selection.startPos = 0;
          correctPortion.selection.endPos = 1;

          // correct incorrect props
          para.curPos.contentPos += addedIndex;
          portion.selection.bUse = false;
          portion.selection.startPos = 0;
          portion.selection.endPos = 0;
        }
      } else {
        portion.selection.startPos = 0;
        portion.selection.endPos = 1;
      }

    } else if (uncheckedIndex === portion.content.length) {
      // if image is inserted at the end of a portion

      let addedIndex = 1;
      let correctedRunContentPos = para.curPos.contentPos + addedIndex;
      let correctedPortion = para.content[correctedRunContentPos];

      while (correctedPortion.content.length === 0) {
        addedIndex++;
        correctedRunContentPos += 1;
        correctedPortion = para.content[correctedRunContentPos];

        if (addedIndex > 100) {
// tslint:disable-next-line: no-console
          console.warn('Maximum addedIndex reached.');
          break;
        }
      }
      const correctedSelectedImage = correctedPortion.content[0] as ParaDrawing;

      // console.log(this.selectionInfo, correctedSelectedImage, correctedPortion, correctedRunContentPos);
      if (this.selectionInfo === correctedSelectedImage.name) {

        // if (nextPortion.content.length === 0) {
          // special case: insert an image in the middle of a paragraph, then change previous text props
        // all cases included for para selection change?
        para.selection.startPos = correctedRunContentPos;
        para.selection.endPos = correctedRunContentPos;
        // }

        selectionIndex = 0;

        correctedPortion.selection.bUse = true;
        correctedPortion.selection.startPos = 0;
        correctedPortion.selection.endPos = 1;

        // correct incorrect props
        para.curPos.contentPos += addedIndex;
        portion.selection.bUse = false;
        portion.selection.startPos = 0;
        portion.selection.endPos = 0;

      } else {
        // if image is before paraEnd(and click on right half of the image)
        selectionIndex = uncheckedIndex - 1;
        // selectionIndex must be paradrawing's current index
        portion.selection.startPos = selectionIndex;
        portion.selection.endPos = selectionIndex + 1;
      }

    } else {
      // click on left or right part of an image should have same startPos & endPos

      const selectionInfoInstance = this.getDrawingByName(this.selectionInfo);
      // console.log(selectionInfoInstance)
      if (selectionInfoInstance) {
        // const selectedImageX = selectionInfo.getAttribute('x') * 1;
        const selectedImageX = selectionInfoInstance.positionX;
        // const selectedImageWidth = selectionInfo.getAttribute('width') * 1;
        const selectedImageWidth = selectionInfoInstance.width;
        // console.log(selectedImageX, selectedImageWidth);

        // check x or x - 1;
        const candCur = portion.content[uncheckedIndex];
        const candPrev = portion.content[uncheckedIndex - 1];

        if (candCur.positionX === selectedImageX && candCur.width === selectedImageWidth) {
          selectionIndex = uncheckedIndex;
        } else if (candPrev.positionX === selectedImageX && candPrev.width === selectedImageWidth) {
          selectionIndex = uncheckedIndex - 1;
        } else {
          // TODO: when hdr/ftr is not triggered and image is in it, this is viable
          // tslint:disable-next-line: no-console
          // console.warn('Cannot relate image element with image model');
        }
      } else {
// tslint:disable-next-line: no-console
        console.warn('SelectionInfo is null. Neglect it during image insertion');
      }

      // selectionIndex must be paradrawing's current index
      portion.selection.startPos = selectionIndex;
      portion.selection.endPos = selectionIndex + 1;

    }

    this.document.updateSelectionState();
    // this.document.getSelectionBounds(mouseEvent, pageIndex);
    this.document.getSelectionBounds();

  }

  public isInDrawingObject(pointX?: number, pointY?: number, curPage?: number): boolean {
    // if (document.curPos.type === DocCurPosType.DrawingObjects) {
    if (this.document.getImageFlags().isImageOnClick) {
      return true;
    }
    return false;
  }

  /**
   * whole image collection ParaDrawing map
   */
  public getGraphicObject(): Map<string, ParaDrawing> {
    return this.drawingObjects;
  }

  public clearImages(): void {
    for (const [string, img] of this.drawingObjects) {
      img.portion = null;
      img.src = null;
      img.graphicObjects = null;
    }
    this.drawingObjects.clear();
    this.selectionInfo = null;
  }

  public setSelectionInfo(selectionInfo: string): void {
    if (selectionInfo != null) {
      this.selectionInfo = selectionInfo;
    }
  }

  public removeSelection(): void {
    this.selectionInfo = null;
  }

  /**
   * get current focused image elem
   */
  public getSelectionInfo(): any {
    return this.selectionInfo;
  }

  /**
   * convert pure svg to <image> href string
   */
  public convertSVGToImageString(svgEquationElem: any, width?: number, height?: number): string {
    const initialVals = {width: 50, height: 50};

    // console.log(svgEquationElem)
    // if initial template
    // if (svgEquationElem.getAttribute('width') === '0' && svgEquationElem.getAttribute('height') === '0') {
    //   svgEquationElem.setAttribute('width', initialVals.width);
    //   svgEquationElem.setAttribute('height', initialVals.height);
    // }
    // if (width > 0) {
    //   svgEquationElem.setAttribute('width', width);
    // }
    // if (height > 0) {
    //   svgEquationElem.setAttribute('height', height);
    // }

    let svgTemplate: string;
    if (typeof svgEquationElem === 'string') {
      svgTemplate = svgEquationElem;
    } else {
      svgTemplate = new XMLSerializer().serializeToString(svgEquationElem);
    }

    // let svgTemplate = svgEquationElem.innerHTML;

    // console.log(ordinaryTemplate)
    // let svgString =
    // `
    // <svg width="300" height="300" viewBox="0 0 300 300" xmlns="http://www.w3.org/2000/svg">
    //     ${svgTemplate}
    // </svg>
    // `;
    // console.log(ordinaryString)
    const encodedData = window.btoa(unescape(encodeURIComponent(svgTemplate)));
    const svgConvertedURI = 'data:image/svg+xml;base64,' + encodedData;

    return svgConvertedURI;

  }

  public addGraphicObject(paraDrawing: ParaDrawing): void {
    // this.drawingObjects.push(paraDrawing);
    if ( this.drawingObjects.has(paraDrawing.name) ) {
      return ;
    }

    const history = this.getHistory();
    if ( history ) {
      history.addChange(new ChangeGraphicObjectsAddParaImage(this, undefined, [paraDrawing]));
    }
    this.drawingObjects.set(paraDrawing.name, paraDrawing);
  }

  // public removeGraphicObject(imageName: string): boolean {
  //   return this.drawingObjects.delete(imageName);
  // }

  public clear(): void {
    // this.drawingObjects.length = 0;
    this.drawingObjects.clear();
  }

  public deleteImage(delImage: ParaDrawing): boolean {
      if ( !delImage || !this.drawingObjects.has(delImage.name) ) {
        return ;
      }

      const name = delImage.name;
      const history = this.getHistory();
      if ( history ) {
        history.addChange(new ChangeGraphicObjectsRemoveParaImage(this, undefined, [this.drawingObjects.get(name)]));
      }

      // set image focus to false
      // IMAGE_FLAGS.isImageOnClick = false;
      this.document.setImageOnClick(false)
      delImage.setDirty();
      return this.drawingObjects.delete(name);
  }

  public setName(oldName: string, name: string): number {
    // const obj = this.drawingObjects.get(oldName);
    // if (!obj) {
    //   return false;
    // }

    // this.drawingObjects.setName(sName, sNewName);
    // drawing.setName(sNewName);

    const drawing = this.getDrawingByName(oldName);
    if (!drawing) {
        return ResultType.Failure;
    }

    if (this.checkUniqueImageName(name) === false) {
        return ResultType.Failure;
    }

    drawing.setName(name);
    this.drawingObjects.delete(oldName);
    this.drawingObjects.set(name, drawing);

    return ResultType.Success;
  }

  public checkImageDeleteLockInSelection(delItems: ParaElementBase[]): boolean {
    for (let nIndex = 0, nCount = delItems.length; nIndex < nCount; ++nIndex) {
      const type = delItems[nIndex].type;
      if ( type === ParaElementType.ParaDrawing || type === ParaElementType.ParaMedEquation) {
        const delImage = delItems[nIndex] as ParaDrawing;
        if (delImage.deleteLocked) {
          return true;
        }
      }
    }
    return false;
  }

  /**
   * remove drawingObject from whole paragraph(s)/tables removal
   */
  public deleteImageFromParaTableRemoval(elementId: number, bTable: boolean): void { // elementId: paraId or tableId
    const delImages = [];
    const iterator = this.drawingObjects.values();
    let result = iterator.next();
    while (!result.done) {
      const drawingObject = result.value;
      // console.log(drawingObject);
      const curId = !bTable ? drawingObject.paraId : drawingObject.tableId;
      if (curId === elementId) {
        delImages.push(drawingObject);
      }
      result = iterator.next();
    }

    // console.log(delImages)

    this.delImagesFromRootElemRemoval(delImages);
  }

  public deleteImageFromRegionRemoval(region: Region): void {
    const rootElems = region.getContent();
    for (const rootElem of rootElems) {
      if (rootElem instanceof Paragraph) {
        this.deleteImageFromParaTableRemoval(rootElem.id, false);
      } else if (rootElem instanceof Region) {
        this.deleteImageFromRegionRemoval(rootElem);
      } else if (rootElem instanceof Table) {
        this.deleteImageFromParaTableRemoval(rootElem.id, true);
      }
    }
  }

  public addContentChanges( changes: ContentChangesElement ): void {
      this.contentChanges.add(changes);
  }

  public getHistory(): History {
    return this.document ? this.document.getHistory() : null;
  }

  public isImageInHeaderFooter(image: ParaDrawing): boolean {
    if (image != null) {
      // const portion = image.portion;
      // if (portion != null) {
      //   // if portion exists, para must wrap it
      //   const parentContainer = (portion.paragraph.parent as any);
      //   // console.log(portion.paragraph.parent.getTopElement())
      //   console.log(parentContainer.parent)
      //   if (parentContainer != null) {
      //     // console.log(parentContainer.parent)
      //     if (parentContainer.parent instanceof HeaderFooter) {
      //       return true;
      //     }
      //   }
      // }
      const result = image.getDocumentSectionType();
      if (result !== DocumentSectionType.Document) {
        return true;
      }
    }
    return false;
  }

  public addInsertGraphicObject(image: ParaDrawing): void {
      this.graphicObjectCaches.push(image);
  }

  public getGraphicObjectCaches(): ParaDrawing[] {
      return this.graphicObjectCaches;
  }

  public resetPasteDrawing(): void {
    const draws = this.graphicObjectCaches;
    if (!draws || draws.length === 0) {
      return;
    }
    const oldDraws = this.drawingObjects;
    draws.forEach((draw) => {
      oldDraws.delete(draw.name);
    });
    this.graphicObjectCaches = [];
  }

  public clearPasteSuccessDrawing(): void {
    const draws = this.graphicObjectCaches;
    if (!draws || !draws.length) {
      return;
    }
    this.graphicObjectCaches = [];
    const history = this.getHistory();
    if ( !history ) {
      return;
    }
    for ( const draw of draws ) {
      history.addChange(new ChangeGraphicObjectsAddParaImage(this, undefined, [draw]));
    }
  }

  public insertGraphicObjectCaches(): void {
      const drawings = [];
      for (let index = 0, length = this.graphicObjectCaches.length; index < length; index++) {
          const image = this.graphicObjectCaches[index];
          if (image) {
            image.name = this.makeUniqueImageName(image.getType(), image.getDrawingName());

            drawings.push(image);
            this.drawingObjects.set(image.name, image);
          }
      }

      const history = this.getHistory();
      if ( history && 0 < drawings.length ) {
        history.addChange(new ChangeGraphicObjectsAddParaImage(this, undefined, drawings));
      }

      this.graphicObjectCaches = [];
  }

  public resetInsertGraphicObjectCaches(): void {
      this.graphicObjectCaches = [];
  }

  public getDocument(): Document {
    return this.document;
  }

  private getImageContent(portion: ParaPortion, sec: PortionSelection): ParaElementBase {
    let pos = sec.endPos - sec.startPos;
    if (pos > 0) {
      pos = sec.startPos;
    } else {
      pos = sec.endPos;
    }
    return portion.content[pos];
  }

  private getClickImage(para: Paragraph, portion: ParaPortion): ParaDrawing {
    let selectedImage: ParaElementBase;
    // const types = [ParaElementType.ParaDrawing, ParaElementType.ParaMedEquation];
    const selection = para.selection;
    const sec = portion.selection;
    if (selection.startPos === selection.endPos) {
      const pos = sec.endPos - sec.startPos;
      if (Math.abs(pos) < 2) {
        selectedImage = this.getImageContent(portion, sec);
      }
    } else if (Math.abs(selection.startPos - selection.endPos) === 1) {

      if (sec.startPos === sec.endPos) { // 这里portion为空
        const otherPor = para.content[selection.startPos];
        const otherSec = otherPor.selection;
        const pos = otherSec.endPos - otherSec.startPos;
        if (Math.abs(pos) < 2) {
          selectedImage = this.getImageContent(otherPor, otherSec);
        }
      } else if (Math.abs(sec.endPos - sec.startPos) < 2) {
        const otherPor = para.content[selection.startPos];
        const otherSec = otherPor.selection;
        if (Math.abs(otherSec.startPos - otherSec.endPos) === 0) {
          selectedImage = this.getImageContent(portion, sec);
        }
      }
    }

    return selectedImage as ParaDrawing;
  }

  private delImagesFromRootElemRemoval(delImages: any[]): void {
    if ( 0 < delImages.length ) {
      const history = this.getHistory();
      if ( history ) {
        history.addChange(new ChangeGraphicObjectsRemoveParaImage(this, undefined, delImages));
      }
      this.document.setImageOnClick(false);
    }
    // console.log(delImages)

    for (const delImage of delImages) {
      this.drawingObjects.delete(delImage.name);
      // set image focus to false
      // IMAGE_FLAGS.isImageOnClick = false;
    }
  }

}

export const NEW_IMAGE_NAME = new Map([
  [ParaElementType.ParaDrawing, '图片'],
  [ParaElementType.ParaMedEquation, '公式'],
  [ParaElementType.ParaSvgDrawing, '图片'],
  [ParaElementType.ParaAudioDrawing, '音频'],
  [ParaElementType.ParaVideoDrawing, '视频'],
  [ParaElementType.ParaBarcode, 'barcode'],
]);
