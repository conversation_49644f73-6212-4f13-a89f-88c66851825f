import { ICanvasProps } from './common';
import { ParaBaseUI } from './ParaBase';

interface IProps {
    content: any;
    className?: string;
    scale?: number;
    pageIndex?: number;
    host?: any;
    cellId?: number;
    bFromHeaderFooter?: boolean;
}

export class TableCellContentUI {
    private props: IProps;
    private documentCore: any;
    private host: any;
    private ctx: any;
    constructor(props: ICanvasProps) {
        this.props = props.content;
        this.host = props.host;
        this.props.pageIndex = props.pageIndex;
        this.documentCore = props.host.documentCore;
        this.ctx = props.ctx;
        this.render();
    }

    private render(): void {
        this.renderPara(this.props.content.paragraphs, this.props.cellId);
    }

    private renderPara(paras: any[], cellId: number): any {
        if (!paras || !paras.length) {
            return null;
        }
        const host = this.host;
        const {pageIndex, bFromHeaderFooter} = this.props;
        const ctx = this.ctx;
        paras.forEach((para, index) => {
            const obj = new ParaBaseUI({content: {content: para, bFromHeaderFooter, cellId}, host, pageIndex, ctx});
            // return (
            // <ParaBaseUI
            //     key={para.id}
            //     host={host}
            //     content={para}
            //     pageIndex={pageIndex}
            //     scale={scale}
            //     bFromHeaderFooter={bFromHeaderFooter}
            //     cellId={cellId}
            // />
            // );
        });
    }
}
