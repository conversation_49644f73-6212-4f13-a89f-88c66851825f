import ParaPortion from '../../model/core/Paragraph/ParaPortion';
// tslint:disable-next-line: max-line-length
import { ErrorMessages, PAGENUMPROP_DEFAULT, IPageNumProperty, EquationType, rtNode, IModeFonts, getFontFamilyVal} from '../../common/commonDefines';
import { ParaElementType } from '../../model/core/Paragraph/ParagraphContent';
import ParaDrawing, { ParaBarcode, ParaBigDrawing, ParaEquation, ParaMediaDrawing } from '../../model/core/Paragraph/ParaDrawing';
import { IBarcodeAllProperties, IDrawingAttributesProperties } from '../writer/file/drawing';
// tslint:disable-next-line: max-line-length
import { IMedEquationAllProperties } from '../writer/file/drawing/medEquation/medEquation';
import { FontWeightType, TextDecorationLineType, FontStyleType, TextVertAlign } from '../../model/core/TextProperty';
import { NewControl } from '../../model/core/NewControl/NewControl';
// import {parse as circularParse, stringify as circularStringify} from 'flatted/esm';
import Document from '../../model/core/Document';
import { PLACEHOLDER_IMAGE } from '../../common/resources/imageSources';
import { logger } from '../../common/log/Logger';
import ParaPageNum from '../../model/core/Paragraph/ParaPageNum';
import HeaderFooter from '../../model/core/HeaderFooter';
import { IInsertFileContent, IUniqueImageProps } from './reader';
import { safeDecodeURIComponent } from '../../common/commonMethods';
import { unescapeXML } from '../../utils/xml';
import { TableCell } from '../../model/core/Table/TableCell';
import ParagraphCore from '../../model/core/Paragraph';
import { DocumentContent } from '../../model/core/DocumentContent';

export interface IControlObj {
  bFirstSiblingOfSdtStart?: boolean;
  unclosedNewControls: NewControl[];
  bFollowedBySdtEnd?: boolean;
  buttons?: Map<string, any>
}

export class RunReader {

  public static traverseRun(paraChild: any, para: any, runCount: any, controlObj: IControlObj,
                            uniqueImagelist: Map<string, IUniqueImageProps>, bInsertFile: boolean = false,
                            document: Document, headerFooter: HeaderFooter = null,
                            showPlaceholder: boolean = false): ParaPortion {
    const portion = new ParaPortion(para);
    const textProperty = portion.textProperty;
    // let runChild = paraChild.childNodes[0];
    let runChild = paraChild.children[0];

    while (runChild) {
      // if (runChild.nodeName !== '#text') {
      const runNodeName = runChild.nodeName;
      if (runNodeName === 'w:rPr') {
        const rFonts = runChild.getElementsByTagName('w:rFonts')[0];
        const sz = runChild.getElementsByTagName('w:sz')[0];
        const color = runChild.getElementsByTagName('w:color')[0];
        const b = runChild.getElementsByTagName('w:b')[0];
        const i = runChild.getElementsByTagName('w:i')[0];
        const u = runChild.getElementsByTagName('w:u')[0];
        const vertAlign  = runChild.getElementsByTagName('w:vertAlign')[0];
        const highLight = runChild.getElementsByTagName('w:highlight')[0];

        if (rFonts) {
          try {
            if (rFonts.attributes && rFonts.attributes.length > 0) {
              if (rFonts.attributes.getNamedItem('w:eastAsia')) {
                const fontFamilyVal = getFontFamilyVal(rFonts.attributes.getNamedItem('w:eastAsia').nodeValue);

                portion.textProperty.fontFamily = fontFamilyVal;
              }
            } else {
              throw new Error('rFonts has no attributes');
            }
          } catch (error) {
            // alert(ErrorMessages.XmlError);
            // tslint:disable-next-line: no-console
            // console.log(error)
            window.dispatchEvent(new ErrorEvent('error', {message: ErrorMessages.XmlError, error}));
            // tslint:disable-next-line: no-console
            // console.warn(ErrorMessages.XmlError);
            const date = new Date();
            logger.error({id: document && document.id, code: error.stack,
              startTime: date, args: null, name: 'traverseRun.rFonts.attributes'});
          }
        }
        if (sz) { // font size
          try {
            if (sz.attributes && sz.attributes.length > 0) {
              if (sz.attributes.getNamedItem('w:val')) {
                portion.textProperty.fontSize = sz.attributes.getNamedItem('w:val').nodeValue;
              }
            } else {
              throw new Error('font size has no attributes');
            }
          } catch (error) {
            // alert(ErrorMessages.XmlError);
            window.dispatchEvent(new ErrorEvent('error', {message: ErrorMessages.XmlError, error}));
            console.warn(ErrorMessages.XmlError);
            const date = new Date();
            logger.error({id: document && document.id, code: error.stack,
              startTime: date, args: null, name: 'traverseRun.sz.attributes'});
          }
        }
        if (color) { // font color
          try {
            if (color.attributes && color.attributes.length > 0) {
              if (color.attributes.getNamedItem('w:val')) {
                portion.textProperty.color = color.attributes.getNamedItem('w:val').nodeValue;
              }
            } else {
              throw new Error('font color has no attributes');
            }
          } catch (error) {
            // alert(ErrorMessages.XmlError);
            window.dispatchEvent(new ErrorEvent('error', {message: ErrorMessages.XmlError, error}));
            console.warn(ErrorMessages.XmlError);
            const date = new Date();
            logger.error({id: document && document.id, code: error.stack,
              startTime: date, args: null, name: 'traverseRun.color.attributes'});
          }
        }
        if (b) { // bold
          portion.textProperty.fontWeight = FontWeightType.Bold;
        }
        if (i) { // italic
          portion.textProperty.fontStyle = FontStyleType.Italic;
        }
        if (u) { // underline
          // for now, may have many variations
          portion.textProperty.textDecorationLine = TextDecorationLineType.Underline;
        }
        if (highLight) { // background color
          try {
            if (highLight.attributes && highLight.attributes.length > 0) {
              if (highLight.attributes.getNamedItem('w:val')) {
                portion.textProperty.backgroundColor = highLight.attributes.getNamedItem('w:val').nodeValue;
              }
            } else {
              throw new Error('background color has no attributes');
            }
          } catch (error) {
            // alert(ErrorMessages.XmlError);
            window.dispatchEvent(new ErrorEvent('error', {message: ErrorMessages.XmlError, error}));
            console.warn(ErrorMessages.XmlError);
            const date = new Date();
            logger.error({id: document && document.id, code: error.stack,
              startTime: date, args: null, name: 'traverseRun.highLight.attributes'});
          }
        }
        if (vertAlign) { // vertAlign
          try {
            if (vertAlign.attributes && vertAlign.attributes.length > 0) {
              const wVal = vertAlign.attributes.getNamedItem('w:val');
              if (wVal) {
                if (wVal.nodeValue === 'superscript') {
                  portion.textProperty.vertAlign = TextVertAlign.Super;
                  // portion.textProperty.superscript = true;
                } else if (wVal.nodeValue === 'subscript') {
                  portion.textProperty.vertAlign = TextVertAlign.Sub;
                  // portion.textProperty.subscript = true;
                }
              }
            } else {
              throw new Error('vertAlign has no attributes');
            }
          } catch (error) {
            // alert(ErrorMessages.XmlError);
            window.dispatchEvent(new ErrorEvent('error', {message: ErrorMessages.XmlError, error}));
            console.warn(ErrorMessages.XmlError);
            const date = new Date();
            logger.error({id: document && document.id, code: error.stack,
              startTime: date, args: null, name: 'traverseRun.vertAlign.attributes'});
          }
        }

        // console.log(portion.textProperty)
      } else if (runNodeName === 'w:t') {
        try {
          if (runChild.childNodes.length > 0) {
            portion.addText(runChild.childNodes[0].nodeValue);
          } else { // fail safe. empty <w:t></w:t> not allowed
            portion.addText(' ');
            throw new Error('empty text node');
          }
        } catch (error) {
          // alert(ErrorMessages.XmlError);
          window.dispatchEvent(new ErrorEvent('error', {message: ErrorMessages.XmlError, error}));
          // tslint:disable-next-line: no-console
          console.warn(ErrorMessages.XmlError);
          const date = new Date();
          logger.error({id: document && document.id, code: error.stack,
            startTime: date, args: null, name: 'traverseRun.w:t'});
        }
      } else if (runNodeName === 'w:tab') {
        portion.addParaItem(portion.content.length, ParaElementType.ParaTab);
      } else if (runNodeName === 'w:drawing') {

        const drawingAttrsXmlObj = runChild.attributes;
        try {
          if (drawingAttrsXmlObj && drawingAttrsXmlObj.length > 0) {
            let drawingAttrs = RunReader.prepareDrawingAttrs(drawingAttrsXmlObj);
            let drawing: ParaDrawing = null;

            drawingAttrs = drawingAttrs as IDrawingAttributesProperties;
            const {width, height, source, imageRatio} = drawingAttrs;
            // console.log(drawingAttrs) // has name attr

            // source => href
            let href = null;
            try {
              href = uniqueImagelist.get(source).dataUrl;
              if (!href) {
                href = PLACEHOLDER_IMAGE;
                throw new Error('cannot find image string based on image source');
              }
            } catch (error) {
              // maybe silent is better?
              // alert(ErrorMessages.XmlError);
              window.dispatchEvent(new ErrorEvent('error', {message: ErrorMessages.XmlError, error}));
            }

            // duplicate name case. should only happen when inserting file
            this.setNewNameForDuplicate(document, bInsertFile, drawingAttrs);

            const {name} = drawingAttrs;

            try {
              if (width > 0 && height > 0 && href) {
                drawing = new ParaDrawing(para.parent, width, height, href, false, name, imageRatio);
                const {preferRelativeResize, sizeProtect, deleteProtect, copyProtect} = drawingAttrs;
                drawing.setAdvancedProps((preferRelativeResize === 1 ? true : false),
                  (sizeProtect === 1 ? true : false), (deleteProtect === 1 ? true : false),
                  (copyProtect === 1 ? true : false));
                drawing.setPreload(true);
                portion.addToContent(portion.content.length, drawing);

                if ( bInsertFile ) {
                  drawing.setParaId(para.getId());
                  drawing.setPortion(portion);

                  const parent = para.getParent();
                  if ( parent && parent.isTableCellContent() ) {
                    drawing.setTableId(0);
                  }
                }
              } else {
                throw new Error('image attributes are not correctly retrieved');
              }
            } finally {
              // catch outside
            }
          } else {
            throw new Error('image has no attributes');
          }
        } catch (error) {
          // alert(ErrorMessages.XmlError);
          window.dispatchEvent(new ErrorEvent('error', {message: ErrorMessages.XmlError, error}));
          // tslint:disable-next-line: no-console
          console.warn(ErrorMessages.XmlError);
          const date = new Date();
          logger.error({id: document && document.id, code: error.stack,
            startTime: date, args: null, name: 'traverseRun.w:drawing'});
        }

      } else if (runNodeName === 'oMediaMath') {

        const oMediaMathAttrs = runChild.attributes;
        // console.log(oMediaMathAttrs);

        try {
          if (oMediaMathAttrs && oMediaMathAttrs.length > 0) {
            let medEquation: ParaEquation = null;
            const medEquationAttrs = RunReader.prepareMedEquationAttrs(runChild);
            const {width, height, name, mathType, mathValue, source} = medEquationAttrs;

            // source => href
            let href = null;
            try {
              href = uniqueImagelist.get(source).dataUrl;
              if (!href) {
                href = PLACEHOLDER_IMAGE;
                throw new Error('cannot find image string based on image source');
              }
            } catch (error) {
              // maybe silent is better?
              // alert(ErrorMessages.XmlError);
              window.dispatchEvent(new ErrorEvent('error', {message: ErrorMessages.XmlError, error}));
            }

            // duplicate name case. should only happen when inserting file
            this.setNewNameForDuplicate(document, bInsertFile, medEquationAttrs);

            try {
              // if (width > 0 && height > 0 && name && mathType != null && mathValue) {
              if (width > 0 && height > 0 && name && mathType != null) { // old xmls has no mathValue. TODO
                medEquation = new ParaEquation(para.parent, medEquationAttrs.width, medEquationAttrs.height,
                  href, medEquationAttrs.name, medEquationAttrs.mathType,
                  medEquationAttrs.mathValue);

                medEquation.setPreload(true);
                // console.log(medEquation);
                portion.addToContent(portion.content.length, medEquation);
              } else {
                throw new Error('medical equation attributes are not correctly retrieved');
              }
            } finally {
              // catch in outer for
            }

          } else {
            throw new Error('medical equation has no attributes');
          }
        } catch (error) {
          // alert(ErrorMessages.XmlError);
          window.dispatchEvent(new ErrorEvent('error', {message: ErrorMessages.XmlError, error}));
          // tslint:disable-next-line: no-console
          console.warn(ErrorMessages.XmlError);
          const date = new Date();
          logger.error({id: document && document.id, code: error.stack,
            startTime: date, args: null, name: 'traverseRun.oMediaMath'});
        }

      } else if (runNodeName === 'w:pgNum') {
        const pgNumChildren = runChild.children;
        const pgNumProps: IPageNumProperty = {
          pageNumType: PAGENUMPROP_DEFAULT.pageNumType,
          pageNumString: PAGENUMPROP_DEFAULT.pageNumString,
          startIndex: PAGENUMPROP_DEFAULT.startIndex,
        };
        let curPage = -1;
        let totalPages = -1;

        for (const pgNumChild of pgNumChildren) {
          const pgNumChildName = pgNumChild.nodeName;
          if (pgNumChildName === 'w:pgNumType') {
            pgNumProps.pageNumType = +pgNumChild.childNodes[0].nodeValue;
          } else if (pgNumChildName === 'w:pgNumString') {
            pgNumProps.pageNumString = pgNumChild.childNodes[0].nodeValue;
          } else if (pgNumChildName === 'w:startIndex') {
            pgNumProps.startIndex = +pgNumChild.childNodes[0].nodeValue;
          } else if (pgNumChildName === 'w:pageCount') {
            totalPages = +pgNumChild.childNodes[0].nodeValue;
          } else if (pgNumChildName === 'w:pageCurrent') {
            curPage = +pgNumChild.childNodes[0].nodeValue;
          }
        } // done for

        // document.addPageNum(pgNumProps);
        if (curPage !== -1 && totalPages !== -1) {
          const pageNum = new ParaPageNum();
          pageNum.setPageNumProperty(pgNumProps);
          pageNum.setPageNumFromReader(curPage, textProperty, totalPages, headerFooter);
          portion.addToContent(portion.content.length, pageNum);
        }

      } else if (runNodeName === 'Painting') {

        const paintingAttrs = runChild.attributes;
        try {
          if (paintingAttrs && paintingAttrs.length > 0) {
            let medEquation: ParaEquation = null;
            const medEquationAttrs = RunReader.prepareMedEquationAttrs(runChild, uniqueImagelist);
            const {width, height, name, mathType, mathValue, source} = medEquationAttrs;
            // console.log(medEquationAttrs)

            // source => href
            const imageSource = uniqueImagelist.get(source);
            // console.log(imageSource)
            let href = null;
            try {
              href = imageSource.dataUrl;
              if (!href) {
                href = PLACEHOLDER_IMAGE;
                throw new Error('cannot find image string based on image source');
              }
            } catch (error) {
              // maybe silent is better?
              // alert(ErrorMessages.XmlError);
              window.dispatchEvent(new ErrorEvent('error', {message: ErrorMessages.XmlError, error}));
            }

            // duplicate name case. should only happen when inserting file
            this.setNewNameForDuplicate(document, bInsertFile, medEquationAttrs);

            try {
              // if (width > 0 && height > 0 && name && mathType != null && mathValue) {
              if (width > 0 && height > 0 && name && mathType != null) { // old xmls has no mathValue. TODO
                medEquation = new ParaEquation(para.parent, medEquationAttrs.width, medEquationAttrs.height,
                  href, medEquationAttrs.name, medEquationAttrs.mathType,
                  medEquationAttrs.mathValue);

                medEquation.setPreload(true);
                // console.log(medEquation);
                portion.addToContent(portion.content.length, medEquation);
              } else {
                throw new Error('medical equation attributes are not correctly retrieved');
              }
            } finally {
              // catch in outer for
            }

          } else {
            throw new Error('medical equation has no attributes');
          }
        } catch (error) {
          // alert(ErrorMessages.XmlError);
          // tslint:disable-next-line: no-console
          console.log(error);
          window.dispatchEvent(new ErrorEvent('error', {message: ErrorMessages.XmlError, error}));
          // tslint:disable-next-line: no-console
          console.warn(ErrorMessages.XmlError);
          const date = new Date();
          logger.error({id: document && document.id, code: error.stack,
            startTime: date, args: null, name: 'traverseRun.oMediaMath'});
        }
      } else if (runNodeName === 'w:br') {
        portion.addParaItem(portion.content.length, ParaElementType.ParaNewLine);
      }
      // }
      runChild = runChild.nextElementSibling;
    }

    /** catch when the run is followed by <stdStart> */
    if (showPlaceholder === false) {
      if (!controlObj.bFirstSiblingOfSdtStart) {
        if (controlObj.bFollowedBySdtEnd) {
          // const unclosedNewControls = controlObj.unclosedNewControls;
          // const newControlContent = unclosedNewControls[unclosedNewControls.length - 1].getNewControlContent();
          // const newControlEndBorder = newControlContent.getEndBorder();
          // portion.addToContent(portion.content.length, newControlEndBorder);

          // unclosedNewControls.pop();
        }
      } else { // placeholder.content, i.e. next portion. But may not be in use
        // elements ready, add controlStart/controlEnd to the portion
        const unclosedNewControls = controlObj.unclosedNewControls;
        const newControlContent = unclosedNewControls[unclosedNewControls.length - 1].getNewControlContent();

        // const newControlStartBorder = newControlContent.getStartBorder();
        // const newControlEndBorder = newControlContent.getEndBorder();

        // portion.addToContent(0, newControlStartBorder);

        // if (controlObj.bFollowedBySdtEnd) {
        //   // add ] if everthing can be wrapped in 1 portion
        //   portion.addToContent(portion.content.length, newControlEndBorder);

        //   unclosedNewControls.pop();
        // }
        // r.w said no need any more <- confirmed 11.4.2020
        newControlContent.getPlaceHolder().content = portion.content;
      }
    }

    if (showPlaceholder === false) {
      para.addToContent(runCount, portion);
    }

    return portion;

  }

  public static tTraverseRun(paraChild: rtNode, para: any, runCount: any, controlObj: IControlObj,
                             uniqueImagelist: Map<string, IUniqueImageProps>, bInsertFile: boolean = false,
                             document: Document, headerFooter: HeaderFooter = null,
                             showPlaceholder: boolean = false, modeFonts: IModeFonts = null,
                             insertFileObj?: IInsertFileContent
  ): ParaPortion {
    const portion = new ParaPortion(para);
    const documentVersion = document != null ? document.getDocumentVersion() :
                              (insertFileObj ? insertFileObj.documentVersion : 0);

    if (modeFonts != null) {
      const {defaultFont, regionTitleFont} = modeFonts;
      const textProperty = portion.textProperty;

      if (defaultFont != null) {

        const fontFamilyVal = getFontFamilyVal(defaultFont.fontFamily);

        textProperty.fontFamily = fontFamilyVal;
        textProperty.fontSize = +defaultFont.fontSize;
      }
      if (regionTitleFont != null) {
        //
      }
      // console.log(textProperty)
    }

    // let runChild = paraChild.childNodes[0];
    const runChildren = paraChild.children;
    for (const runChild of runChildren) {
      // if (runChild.nodeName !== '#text') {
      if (typeof runChild === 'object') {
        const runNodeName = runChild.tagName;
        if (runNodeName === 'w:rPr') {

          let rFonts: rtNode = null;
          let sz: rtNode = null;
          let color: rtNode = null;
          let b: rtNode = null;
          let i: rtNode = null;
          let u: rtNode = null;
          let vertAlign: rtNode = null;
          let highLight: rtNode = null;
          for (const runPropNode of runChild.children) {
            if (typeof runPropNode === 'object') {
              switch (runPropNode.tagName) {
                case 'w:rFonts': {
                  rFonts = runPropNode;
                  break;
                }

                case 'w:sz': {
                  sz = runPropNode;
                  break;
                }

                case 'w:color': {
                  color = runPropNode;
                  break;
                }

                case 'w:b': {
                  b = runPropNode;
                  break;
                }

                case 'w:i': {
                  i = runPropNode;
                  break;
                }

                case 'w:u': {
                  u = runPropNode;
                  break;
                }

                case 'w:vertAlign': {
                  vertAlign = runPropNode;
                  break;
                }

                case 'w:highlight': {
                  highLight = runPropNode;
                  break;
                }
                case 'w:hidden': {
                  portion.bHidden = true;
                  break;
                }

                default : {
                  break;
                }
              }
            }
          }

          if (rFonts) {
            try {
              if (rFonts.attributes) {
                if (rFonts.attributes['w:eastAsia']) {
                  const fontFamilyVal = getFontFamilyVal(rFonts.attributes['w:eastAsia']);

                  portion.textProperty.fontFamily = fontFamilyVal;
                }
              } else {
                throw new Error('rFonts has no attributes');
              }
            } catch (error) {
              // alert(ErrorMessages.XmlError);
              // tslint:disable-next-line: no-console
              console.log(error);
              window.dispatchEvent(new ErrorEvent('error', { message: ErrorMessages.XmlError, error }));
              // tslint:disable-next-line: no-console
              console.warn(ErrorMessages.XmlError);
              const date = new Date();
              logger.error({
                id: document && document.id, code: error.stack,
                startTime: date, args: null, name: 'traverseRun.rFonts.attributes'
              });
            }
          }
          if (sz) { // font size
            try {
              if (sz.attributes) {
                if (sz.attributes['w:val']) {
                  portion.textProperty.fontSize = +sz.attributes['w:val'];
                }
              } else {
                throw new Error('font size has no attributes');
              }
            } catch (error) {
              // alert(ErrorMessages.XmlError);
              window.dispatchEvent(new ErrorEvent('error', { message: ErrorMessages.XmlError, error }));
              console.warn(ErrorMessages.XmlError);
              const date = new Date();
              logger.error({
                id: document && document.id, code: error.stack,
                startTime: date, args: null, name: 'traverseRun.sz.attributes'
              });
            }
          }
          if (color) { // font color
            try {
              if (color.attributes) {
                if (color.attributes['w:val']) {
                  portion.textProperty.color = color.attributes['w:val'];
                }
              } else {
                throw new Error('font color has no attributes');
              }
            } catch (error) {
              // alert(ErrorMessages.XmlError);
              window.dispatchEvent(new ErrorEvent('error', { message: ErrorMessages.XmlError, error }));
              console.warn(ErrorMessages.XmlError);
              const date = new Date();
              logger.error({
                id: document && document.id, code: error.stack,
                startTime: date, args: null, name: 'traverseRun.color.attributes'
              });
            }
          }
          if (b) { // bold
            portion.textProperty.fontWeight = FontWeightType.Bold;
          }
          if (i) { // italic
            portion.textProperty.fontStyle = FontStyleType.Italic;
          }
          if (u) { // underline
            // for now, may have many variations
            portion.textProperty.textDecorationLine = TextDecorationLineType.Underline;
          }
          if (highLight) { // background color
            try {
              if (highLight.attributes) {
                if (highLight.attributes['w:val']) {
                  portion.textProperty.backgroundColor = highLight.attributes['w:val'];
                }
              } else {
                throw new Error('background color has no attributes');
              }
            } catch (error) {
              // alert(ErrorMessages.XmlError);
              window.dispatchEvent(new ErrorEvent('error', { message: ErrorMessages.XmlError, error }));
              console.warn(ErrorMessages.XmlError);
              const date = new Date();
              logger.error({
                id: document && document.id, code: error.stack,
                startTime: date, args: null, name: 'traverseRun.highLight.attributes'
              });
            }
          }
          if (vertAlign) { // vertAlign
            try {
              if (vertAlign.attributes) {
                const wVal = vertAlign.attributes['w:val'];
                if (wVal) {
                  if (wVal === 'superscript') {
                    portion.textProperty.vertAlign = TextVertAlign.Super;
                    // portion.textProperty.superscript = true;
                  } else if (wVal === 'subscript') {
                    portion.textProperty.vertAlign = TextVertAlign.Sub;
                    // portion.textProperty.subscript = true;
                  }
                }
              } else {
                throw new Error('vertAlign has no attributes');
              }
            } catch (error) {
              // alert(ErrorMessages.XmlError);
              window.dispatchEvent(new ErrorEvent('error', { message: ErrorMessages.XmlError, error }));
              console.warn(ErrorMessages.XmlError);
              const date = new Date();
              logger.error({
                id: document && document.id, code: error.stack,
                startTime: date, args: null, name: 'traverseRun.vertAlign.attributes'
              });
            }
          }

          // console.log(portion.textProperty)
        } else if (runNodeName === 'w:t') {
          try {
            // TODO: before length > 0 even empty text node.
            if (runChild.children.length > 0) {
              let text = runChild.children[0];
              if (typeof text === 'string') {
                text = safeDecodeURIComponent(text, documentVersion);
                const textProperty = portion.textProperty;
                // console.log(textProperty)
                if (textProperty != null) {
                  portion.addText(text, textProperty);
                } else {
                  portion.addText(text);
                }
              }
            } else { // fail safe. empty <w:t></w:t> not allowed
              portion.addText(' ');
              // TODO: coerce for now
              // throw new Error('empty text node');
            }
          } catch (error) {
            // console.log(error)
            // alert(ErrorMessages.XmlError);
            window.dispatchEvent(new ErrorEvent('error', { message: ErrorMessages.XmlError, error }));
            // tslint:disable-next-line: no-console
            // console.warn(ErrorMessages.XmlError);
            const date = new Date();
            logger.error({
              id: document && document.id, code: error.stack,
              startTime: date, args: null, name: 'traverseRun.w:t'
            });
          }
        } else if (runNodeName === 'w:button') {
          const buttonName = unescapeXML(runChild.attributes['name']);
          portion.addParaItem(portion.content.length, ParaElementType.ParaButton);
          const button: any = portion.content[portion.content.length - 1];
          
          const {color, bPrint} =  runChild.attributes as any;
          const props: any = {};
          if (color) {
            props.color = color;
          }
          if (runChild.children.length) {
            props.content = unescapeXML(runChild.children[0] as string);
          }
          if (bPrint === 'true') {
            props.bPrint = true;
          }
          button.setButtonProps(props);
          if (bInsertFile) {
            let buttons = insertFileObj?.buttons;
            if (!buttons) {
              buttons = controlObj.buttons;
            }
            button.name = document.makeParaButtonName(buttonName);
            document.getButtonManager().addCache(button);
          } else {
            button.name = buttonName;
            document.getButtonManager().setButton(button);
          }
          
        } else if (runNodeName === 'w:tab') {
          portion.addParaItem(portion.content.length, ParaElementType.ParaTab);
        } else if (runNodeName === 'w:drawing') {

          const drawingAttrsXmlObj = runChild.attributes;
          // console.log(drawingAttrsXmlObj)
          try {
            if (drawingAttrsXmlObj) {
              // TODO: drawingAttrsXmlObj correct?
              let drawingAttrs = RunReader.tPrepareDrawingAttrs(drawingAttrsXmlObj);
              let drawing: ParaDrawing = null;

              // consider #158716
              if (uniqueImagelist.get(drawingAttrs.source).backendFlag === 1) {
                if (para instanceof ParagraphCore
                  && para.parent instanceof DocumentContent) {
                  const tableCell = para.parent.parent;

                  // console.log(tableCell)
                  if (tableCell instanceof TableCell) {
                    const row = tableCell.row;
                    const table = row.table;
                    let { width: tWidth, height: tHeight} = drawingAttrs;

                    // raw ratio, not inner ratio that is already saved in paradrawing
                    // this is said to be the same ratio as inner ratio from Chou
                    const rawRatio = tWidth / tHeight;
                    // table is always fixed-column
                    // if (true) {
                    //   let tableWidth = tableCell.getCellWidth().width;
                    //   const marginLeft = tableCell.getMargins().left.width;
                    //   const marginRight = tableCell.getMargins().right.width;
                    //   tableWidth -= (marginLeft + marginRight);
                    //   if (tWidth > tableWidth) {
                    //     tWidth = tableWidth;
                    //     tHeight = tWidth / rawRatio;
                    //   }
                    // }

                    if (table.isFixedRowHeight()) {
                      const tableHeight = tableCell.getTableCellContentHeight();
                      if (tHeight > tableHeight) {
                        tHeight = tableHeight;
                        tWidth = tHeight * rawRatio;
                      }
                    }
                    drawingAttrs.width = tWidth;
                    drawingAttrs.height = tHeight;
                  }
                }
              }

              drawingAttrs = drawingAttrs as IDrawingAttributesProperties;
              const { width, height, source, imageRatio, vertAlign } = drawingAttrs;
              // console.log(drawingAttrs) // has name attr

              // source => href
              let href = null;
              try {
                href = uniqueImagelist.get(source).dataUrl;
                if (!href) {
                  href = PLACEHOLDER_IMAGE;
                  //throw new Error('cannot find image string based on image source');
                }
              } catch (error) {
                // maybe silent is better?
                // alert(ErrorMessages.XmlError);
                window.dispatchEvent(new ErrorEvent('error', { message: ErrorMessages.XmlError, error }));
              }

              // duplicate name case. should only happen when inserting file
              this.setNewNameForDuplicate(document, bInsertFile, drawingAttrs, insertFileObj);

              const { name } = drawingAttrs;

              try {
                if (width >= 0 && height >= 0 && href) {
                  if (width === 0 || height === 0) {
                    // tslint:disable-next-line: no-console
                    console.warn('image height/width is 0');
                  }
                  const bInsert = (bInsertFile && insertFileObj && insertFileObj.drawingObjects);
                  // 选择构建方式： Image or Video/Audio
                  if (drawingAttrs.mediaType) {
                    const mtype = drawingAttrs.mediaType;
                    drawing = new ParaMediaDrawing(bInsert ? null : para.parent,
                                                  width,
                                                  height,
                                                  null, // src
                                                  false,
                                                  name,
                                                  imageRatio,
                                                  mtype,
                                                  href,
                                                  vertAlign);
                  } else {
                    const tmpImgAttr = uniqueImagelist.get(source);
                    const bind = tmpImgAttr && tmpImgAttr.bind && JSON.parse(tmpImgAttr.bind) || null;
                    if (bind && bind.isBig) {
                        drawing = new ParaBigDrawing(bInsert ? null : para.parent, width, height, href, false, name, imageRatio, vertAlign, bind);
                    } else {
                        // tslint:disable-next-line: max-line-length
                        drawing = new ParaDrawing(bInsert ? null : para.parent, width, height, href, false, name, imageRatio, vertAlign);
                    }
                  }
                  const { preferRelativeResize, sizeProtect, deleteProtect, copyProtect } = drawingAttrs;
                  drawing.setAdvancedProps((preferRelativeResize === 1 ? true : false),
                    (sizeProtect === 1 ? true : false), (deleteProtect === 1 ? true : false),
                    (copyProtect === 1 ? true : false));
                  drawing.setPreload(true);
                  portion.addToContent(portion.content.length, drawing);

                  if ( bInsert ) {
                    drawing.name = drawingAttrs.name;
                    insertFileObj.drawingObjects.addInsertGraphicObject(drawing);
                    drawing.setParaId(para.getId());
                    drawing.setPortion(portion);

                    const parent = para.getParent();
                    if ( parent && parent.isTableCellContent() ) {
                      drawing.setTableId(0);
                    }
                  }
                } else {
                  throw new Error('image attributes are not correctly retrieved');
                }
              } finally {
                // catch outside
              }
            } else {
              throw new Error('image has no attributes');
            }
          } catch (error) {
            // console.log(error)
            // alert(ErrorMessages.XmlError);
            window.dispatchEvent(new ErrorEvent('error', { message: ErrorMessages.XmlError, error }));
            // tslint:disable-next-line: no-console
            console.warn(ErrorMessages.XmlError);
            const date = new Date();
            logger.error({
              id: document && document.id, code: error.stack,
              startTime: date, args: null, name: 'traverseRun.w:drawing'
            });
          }

        } else if (runNodeName === 'oMediaMath') {

          const oMediaMathAttrs = runChild.attributes;
          // console.log(oMediaMathAttrs);

          try {
            if (oMediaMathAttrs) {
              let medEquation: ParaEquation = null;
              const medEquationAttrs = RunReader.tPrepareMedEquationAttrs(runChild);
              const { width, height, name, mathType, mathValue, source, vertAlign } = medEquationAttrs;

              // source => href
              // console.log(source)
              // console.log(uniqueImagelist)
              let href = null;
              try {
                href = uniqueImagelist.get(source).dataUrl;
                if (!href) {
                  href = PLACEHOLDER_IMAGE;
                  throw new Error('cannot find image string based on image source');
                }
              } catch (error) {
                // maybe silent is better?
                // alert(ErrorMessages.XmlError);
                window.dispatchEvent(new ErrorEvent('error', { message: ErrorMessages.XmlError, error }));
              }

              // duplicate name case. should only happen when inserting file
              this.setNewNameForDuplicate(document, bInsertFile, medEquationAttrs, insertFileObj);

              try {
                // if (width > 0 && height > 0 && name && mathType != null && mathValue) {
                if (width > 0 && height > 0 && name && mathType != null) { // old xmls has no mathValue. TODO
                  const bInsert = (bInsertFile && insertFileObj && insertFileObj.drawingObjects);
                  medEquation = new ParaEquation(bInsert ? null : para.parent, medEquationAttrs.width,
                    medEquationAttrs.height, href, medEquationAttrs.name, medEquationAttrs.mathType,
                    medEquationAttrs.mathValue, false, vertAlign);

                  medEquation.setPreload(true);
                  // console.log(medEquation);
                  portion.addToContent(portion.content.length, medEquation);

                  if ( bInsert ) {
                    medEquation.name = medEquationAttrs.name;
                    insertFileObj.drawingObjects.addInsertGraphicObject(medEquation);
                    medEquation.setParaId(para.getId());
                    medEquation.setPortion(portion);

                    const parent = para.getParent();
                    if ( parent && parent.isTableCellContent() ) {
                      medEquation.setTableId(0);
                    }
                  }
                } else {
                  throw new Error('medical equation attributes are not correctly retrieved');
                }
              } finally {
                // catch in outer for
              }

            } else {
              throw new Error('medical equation has no attributes');
            }
          } catch (error) {
            // console.log(error)
            // alert(ErrorMessages.XmlError);
            window.dispatchEvent(new ErrorEvent('error', { message: ErrorMessages.XmlError, error }));
            // tslint:disable-next-line: no-console
            // console.warn(ErrorMessages.XmlError);
            const date = new Date();
            logger.error({
              id: document && document.id, code: error.stack,
              startTime: date, args: null, name: 'traverseRun.oMediaMath'
            });
          }

        } else if (runNodeName === 'w:pgNum') {
          const pgNumChildren = runChild.children;
          const pgNumProps: IPageNumProperty = {
            pageNumType: PAGENUMPROP_DEFAULT.pageNumType,
            pageNumString: PAGENUMPROP_DEFAULT.pageNumString,
            startIndex: PAGENUMPROP_DEFAULT.startIndex,
          };
          let curPage = -1;
          let totalPages = -1;

          for (const pgNumChild of pgNumChildren) {
            if (typeof pgNumChild === 'object') {
              const pgNumChildName = pgNumChild.tagName;
              if (pgNumChildName === 'w:pgNumType') {
                // TODO: this may be even safer than typecheck
                pgNumProps.pageNumType = +pgNumChild.children[0];
              } else if (pgNumChildName === 'w:pgNumString') {
                const pageNumString = pgNumChild.children[0];
                if (typeof pageNumString === 'string') {
                  pgNumProps.pageNumString = pageNumString;
                }
              } else if (pgNumChildName === 'w:startIndex') {
                pgNumProps.startIndex = +pgNumChild.children[0];
              } else if (pgNumChildName === 'w:pageCount') {
                totalPages = +pgNumChild.children[0];
              } else if (pgNumChildName === 'w:pageCurrent') {
                curPage = +pgNumChild.children[0];
              }
            }

          } // done for

          // document.addPageNum(pgNumProps);
          if (curPage !== -1 && totalPages !== -1) {
            const pageNum = new ParaPageNum();
            const textProperty = portion.textProperty;
            pageNum.setPageNumProperty(pgNumProps);
            pageNum.setPageNumFromReader(curPage, textProperty, totalPages, headerFooter);
            portion.addToContent(portion.content.length, pageNum);
          }

        } else if (runNodeName === 'Painting') {

          const paintingAttrs = runChild.attributes;
          try {
            if (paintingAttrs) {
              let medEquation: ParaEquation = null;
              const medEquationAttrs = RunReader.tPrepareMedEquationAttrs(runChild, uniqueImagelist);
              const { width, height, name, mathType, mathValue, source, vertAlign } = medEquationAttrs;
              // console.log(medEquationAttrs)

              // source => href
              const imageSource = uniqueImagelist.get(source);
              // console.log(imageSource)
              let href = null;
              try {
                href = imageSource.dataUrl;
                if (!href) {
                  href = PLACEHOLDER_IMAGE;
                  throw new Error('cannot find image string based on image source');
                }
              } catch (error) {
                // maybe silent is better?
                // alert(ErrorMessages.XmlError);
                window.dispatchEvent(new ErrorEvent('error', { message: ErrorMessages.XmlError, error }));
              }

              // duplicate name case. should only happen when inserting file
              this.setNewNameForDuplicate(document, bInsertFile, medEquationAttrs, insertFileObj);

              try {
                // if (width > 0 && height > 0 && name && mathType != null && mathValue) {
                if (width > 0 && height > 0 && name && mathType != null) { // old xmls has no mathValue. TODO
                  const bInsert = (bInsertFile && insertFileObj && insertFileObj.drawingObjects);
                  medEquation = new ParaEquation(bInsert ? null : para.parent, medEquationAttrs.width,
                    medEquationAttrs.height, href, medEquationAttrs.name, medEquationAttrs.mathType,
                    medEquationAttrs.mathValue, false, vertAlign);

                  medEquation.setPreload(true);
                  // console.log(medEquation);
                  portion.addToContent(portion.content.length, medEquation);

                  if ( bInsert ) {
                    medEquation.name = medEquationAttrs.name;
                    insertFileObj.drawingObjects.addInsertGraphicObject(medEquation);
                    medEquation.setParaId(para.getId());
                    medEquation.setPortion(portion);

                    const parent = para.getParent();
                    if ( parent && parent.isTableCellContent() ) {
                      medEquation.setTableId(0);
                    }
                  }
                } else {
                  throw new Error('medical equation attributes are not correctly retrieved');
                }
              } finally {
                // catch in outer for
              }

            } else {
              throw new Error('medical equation has no attributes');
            }
          } catch (error) {
            // alert(ErrorMessages.XmlError);
            // tslint:disable-next-line: no-console
            // console.log(error)
            window.dispatchEvent(new ErrorEvent('error', { message: ErrorMessages.XmlError, error }));
            // tslint:disable-next-line: no-console
            // console.warn(ErrorMessages.XmlError);
            const date = new Date();
            logger.error({
              id: document && document.id, code: error.stack,
              startTime: date, args: null, name: 'traverseRun.oMediaMath'
            });
          }
        } else if (runNodeName === 'w:br') {
          portion.addParaItem(portion.content.length, ParaElementType.ParaNewLine);
        } else if ('Barcode' === runNodeName || 'QRCode' === runNodeName) {
          const paintingAttrs = runChild.attributes;
          if (paintingAttrs) {
            let barcode: ParaBarcode = null;
            const medEquationAttrs = RunReader.tPrepareBarcodeAttrs(runChild);
            const { width, height, name, textAlign, src, sourceBind, content, bUse, errorCL } = medEquationAttrs;
            // console.log(medEquationAttrs)

            // source => href
            const imageSource = uniqueImagelist.get(src);
            let source = imageSource?.dataUrl;
            if (!source && 'Barcode' === runNodeName) {
              source = PLACEHOLDER_IMAGE;
              console.log('cannot find image string based on ParaBarcode');
            }

            // duplicate name case. should only happen when inserting file
            this.setNewNameForDuplicate(document, bInsertFile, medEquationAttrs, insertFileObj);

            const bInsert = (bInsertFile && insertFileObj && insertFileObj.drawingObjects);
            let datas;
            if ('Barcode' === runNodeName) {
              datas = {bUse, content, textAlign,
                externalDataBind: JSON.parse(safeDecodeURIComponent(sourceBind, documentVersion))
              };
            } else {
              datas = {errorCL,
                      content,
                      externalDataBind: JSON.parse(safeDecodeURIComponent(sourceBind, documentVersion))
                    };
            }
            barcode = new ParaBarcode(bInsert ? null : para.parent,
                          width,
                          height,
                          source,
                          false,
                          name,
                          datas,
                        );
            barcode.setPreload(true);
            portion.addToContent(portion.content.length, barcode);

            if ( bInsert ) {
              barcode.name = medEquationAttrs.name;
              insertFileObj.drawingObjects.addInsertGraphicObject(barcode);
              barcode.setParaId(para.getId());
              barcode.setPortion(portion);

              const parent = para.getParent();
              if ( parent && parent.isTableCellContent() ) {
                barcode.setTableId(0);
              }
            }
          }
        }
        // }
      }

    }

    /** catch when the run is followed by <stdStart> */
    // if (showPlaceholder === false) {
    //   if (!controlObj.bFirstSiblingOfSdtStart) {
    //     if (controlObj.bFollowedBySdtEnd) {
    //       // const unclosedNewControls = controlObj.unclosedNewControls;
    //       // const newControlContent = unclosedNewControls[unclosedNewControls.length - 1].getNewControlContent();
    //       // const newControlEndBorder = newControlContent.getEndBorder();
    //       // portion.addToContent(portion.content.length, newControlEndBorder);

    //       // unclosedNewControls.pop();
    //     }
    //   } else { // placeholder.content, i.e. next portion. But may not be in use
    //     // elements ready, add controlStart/controlEnd to the portion
    //     const unclosedNewControls = controlObj.unclosedNewControls;
    //     const newControlContent = unclosedNewControls[unclosedNewControls.length - 1].getNewControlContent();

    //     // const newControlStartBorder = newControlContent.getStartBorder();
    //     // const newControlEndBorder = newControlContent.getEndBorder();

    //     // portion.addToContent(0, newControlStartBorder);

    //     // if (controlObj.bFollowedBySdtEnd) {
    //     //   // add ] if everthing can be wrapped in 1 portion
    //     //   portion.addToContent(portion.content.length, newControlEndBorder);

    //     //   unclosedNewControls.pop();
    //     // }
    //     // r.w said no need any more <- confirmed 11.4.2020
    //     newControlContent.getPlaceHolder().content = portion.content;
    //   }
    // }

    if (showPlaceholder === false) { // placeholder portion will not be added here
      para.addToContent(runCount, portion);
    }

    return portion;

  }

  /**
   * prepare IDrawingAttributesProperties that needed in rendering image
   */
  public static prepareDrawingAttrs(drawingAttrsXmlObj: any): IDrawingAttributesProperties {
    const drawingAttrs: IDrawingAttributesProperties = {
      name: null,
      width: null,
      height: null,
      imageRatio: null,
      anchorType: null,
      href: null,
      source: null,
      preferRelativeResize: null,
      sizeProtect: null,
      deleteProtect: null,
      copyProtect: 0, // todo
    };

    if (drawingAttrsXmlObj) {
      if (drawingAttrsXmlObj.getNamedItem('name')) {
        drawingAttrs.name = drawingAttrsXmlObj.getNamedItem('name').nodeValue;
      }
      if (drawingAttrsXmlObj.getNamedItem('width')) {
        drawingAttrs.width = Number(drawingAttrsXmlObj.getNamedItem('width').nodeValue);
      }
      if (drawingAttrsXmlObj.getNamedItem('height')) {
        drawingAttrs.height = Number(drawingAttrsXmlObj.getNamedItem('height').nodeValue);
      }
      if (drawingAttrsXmlObj.getNamedItem('ratio')) {
        drawingAttrs.imageRatio = Number(drawingAttrsXmlObj.getNamedItem('ratio').nodeValue);
      }
      if (drawingAttrsXmlObj.getNamedItem('anchor-type')) {
        drawingAttrs.anchorType = Number(drawingAttrsXmlObj.getNamedItem('anchor-type').nodeValue);
      }
      if (drawingAttrsXmlObj.getNamedItem('href')) {
        drawingAttrs.href = drawingAttrsXmlObj.getNamedItem('href').nodeValue;
      }
      if (drawingAttrsXmlObj.getNamedItem('source')) {
        drawingAttrs.source = drawingAttrsXmlObj.getNamedItem('source').nodeValue;
      }
      if (drawingAttrsXmlObj.getNamedItem('preferRelativeResize')) {
        drawingAttrs.preferRelativeResize = Number(drawingAttrsXmlObj.getNamedItem('preferRelativeResize').nodeValue);
      }
      if (drawingAttrsXmlObj.getNamedItem('sizeProtect')) {
        drawingAttrs.sizeProtect = Number(drawingAttrsXmlObj.getNamedItem('sizeProtect').nodeValue);
      }
      if (drawingAttrsXmlObj.getNamedItem('deleteProtect')) {
        drawingAttrs.deleteProtect = Number(drawingAttrsXmlObj.getNamedItem('deleteProtect').nodeValue);
      }
      if (drawingAttrsXmlObj.getNamedItem('copyProtect')) {
        drawingAttrs.copyProtect = Number(drawingAttrsXmlObj.getNamedItem('copyProtect').nodeValue);
      }
    }
    // console.log(drawingAttrs)
    return drawingAttrs;
  }

  public static tPrepareDrawingAttrs(drawingAttrsXmlObj: any): IDrawingAttributesProperties {
    const drawingAttrs: IDrawingAttributesProperties = {
      name: null,
      width: null,
      height: null,
      imageRatio: null,
      anchorType: null,
      href: null,
      source: null,
      preferRelativeResize: null,
      sizeProtect: null,
      deleteProtect: null,
      copyProtect: 0, // todo
      vertAlign: 2,
    };

    if (drawingAttrsXmlObj) {
      if (drawingAttrsXmlObj.name) {
        drawingAttrs.name = drawingAttrsXmlObj.name;
      }
      if (drawingAttrsXmlObj.width) {
        drawingAttrs.width = Number(drawingAttrsXmlObj.width);
      }
      if (drawingAttrsXmlObj.height) {
        drawingAttrs.height = Number(drawingAttrsXmlObj.height);
      }
      if (drawingAttrsXmlObj.ratio) {
        drawingAttrs.imageRatio = Number(drawingAttrsXmlObj.ratio);
      }
      if (drawingAttrsXmlObj['anchor-type']) {
        drawingAttrs.anchorType = Number(drawingAttrsXmlObj['anchor-type']);
      }
      if (drawingAttrsXmlObj.href) {
        drawingAttrs.href = drawingAttrsXmlObj.href;
      }
      if (drawingAttrsXmlObj.source) {
        drawingAttrs.source = drawingAttrsXmlObj.source;
      }
      if (drawingAttrsXmlObj.preferRelativeResize) {
        drawingAttrs.preferRelativeResize = Number(drawingAttrsXmlObj.preferRelativeResize);
      }
      if (drawingAttrsXmlObj.sizeProtect) {
        drawingAttrs.sizeProtect = Number(drawingAttrsXmlObj.sizeProtect);
      }
      if (drawingAttrsXmlObj.deleteProtect) {
        drawingAttrs.deleteProtect = Number(drawingAttrsXmlObj.deleteProtect);
      }
      if (drawingAttrsXmlObj.copyProtect) {
        drawingAttrs.copyProtect = Number(drawingAttrsXmlObj.copyProtect);
      }
      if (drawingAttrsXmlObj.mediaType) {
        drawingAttrs.mediaType = Number(drawingAttrsXmlObj.mediaType);
      }
      if (drawingAttrsXmlObj.vertAlign) {
        drawingAttrs.vertAlign = Number(drawingAttrsXmlObj.vertAlign);
      }
    }
    // console.log(drawingAttrs)
    return drawingAttrs;
  }

  /**
   * prepare IMedEquationAttributesProperties that needed in rendering medEquation
   */
  // tslint:disable-next-line: max-line-length
  public static prepareMedEquationAttrs(runChild: any, uniqueImagelist: Map<string, IUniqueImageProps> = null): IMedEquationAllProperties {
    // console.log(runChild.childNodes)
    // console.log(runChild.getElementsByTagName('mathValue')[0].childNodes[0].nodeValue)

    const medEquationAttrs: IMedEquationAllProperties = {
      name: null,
      width: null,
      height: null,
      imageRatio: null,
      href: null,
      source: null,
      mathType: null,
      mathValue: null,
      mathText: null,
    };

    if (runChild) {
      const runChildAttrs = runChild.attributes;
      const mathValueNodes = runChild.getElementsByTagName('mathValue');
      const mathTextNodes = runChild.getElementsByTagName('mathText');

      if (runChildAttrs && runChildAttrs.length > 0) { // this is already checked outside
        if (runChildAttrs.getNamedItem('name')) {
          medEquationAttrs.name = runChildAttrs.getNamedItem('name').nodeValue;
        }
        if (runChildAttrs.getNamedItem('width')) {
          medEquationAttrs.width = Number(runChildAttrs.getNamedItem('width').nodeValue);
        }
        if (runChildAttrs.getNamedItem('height')) {
          medEquationAttrs.height = Number(runChildAttrs.getNamedItem('height').nodeValue);
        }
        if (runChildAttrs.getNamedItem('ratio')) {
          medEquationAttrs.imageRatio = Number(runChildAttrs.getNamedItem('ratio').nodeValue);
        }
        if (runChildAttrs.getNamedItem('href')) {
          medEquationAttrs.href = runChildAttrs.getNamedItem('href').nodeValue;
        }
        if (runChildAttrs.getNamedItem('source')) {
          medEquationAttrs.source = runChildAttrs.getNamedItem('source').nodeValue;
        }
        if (runChildAttrs.getNamedItem('math-type')) {
          medEquationAttrs.mathType = Number(runChildAttrs.getNamedItem('math-type').nodeValue);
        }
        if (mathValueNodes.length > 0 && mathValueNodes[0].childNodes.length > 0) {
          medEquationAttrs.mathValue = mathValueNodes[0].childNodes[0].nodeValue;
        }
        if (mathTextNodes.length > 0 && mathTextNodes[0].childNodes.length > 0) {
          medEquationAttrs.mathText = mathTextNodes[0].childNodes[0].nodeValue;
        }

        // editable painting
        if (uniqueImagelist != null) {
          medEquationAttrs.mathType = EquationType.EditableSvg;
          const imageDataSource = uniqueImagelist.get(medEquationAttrs.source);
          if (imageDataSource != null) {
            medEquationAttrs.mathValue = unescapeXML(imageDataSource.editableData);
          }
        }

      } else {
        // alert(ErrorMessages.XmlError);
        // tslint:disable-next-line: no-console
        console.warn(ErrorMessages.XmlError);
      }
    }
    // console.log(medEquationAttrs)
    return medEquationAttrs;
  }

  /**
   * prepare IMedEquationAttributesProperties that needed in rendering medEquation
   */
  // tslint:disable-next-line: max-line-length
  public static tPrepareMedEquationAttrs(runChild: rtNode, uniqueImagelist: Map<string, IUniqueImageProps> = null): IMedEquationAllProperties {
    // console.log(runChild.childNodes)
    // console.log(runChild.getElementsByTagName('mathValue')[0].childNodes[0].nodeValue)

    const medEquationAttrs: IMedEquationAllProperties = {
      name: null,
      width: null,
      height: null,
      imageRatio: null,
      href: null,
      source: null,
      mathType: null,
      mathValue: null,
      mathText: null,
      vertAlign: 2,
    };

    if (runChild) {
      const runChildAttrs = runChild.attributes;
      let mathValueNodes: rtNode = null; // TODO: why it's called nodeS?
      let mathTextNodes: rtNode = null;
      for (const equationPropsNode of runChild.children) {
        if (typeof equationPropsNode === 'object') {
          if (equationPropsNode.tagName === 'mathValue') {
            mathValueNodes = equationPropsNode;
          } else if (equationPropsNode.tagName === 'mathText') {
            mathTextNodes = equationPropsNode;
          }
        }
      }

      if (runChildAttrs) { // this is already checked outside
        if (runChildAttrs['name']) {
          medEquationAttrs.name = runChildAttrs['name'];
        }
        if (runChildAttrs['width']) {
          medEquationAttrs.width = Number(runChildAttrs['width']);
        }
        if (runChildAttrs['height']) {
          medEquationAttrs.height = Number(runChildAttrs['height']);
        }
        if (runChildAttrs['ratio']) {
          medEquationAttrs.imageRatio = Number(runChildAttrs['ratio']);
        }
        if (runChildAttrs['href']) {
          medEquationAttrs.href = runChildAttrs['href'];
        }
        if (runChildAttrs['source']) {
          medEquationAttrs.source = runChildAttrs['source'];
        }
        if (runChildAttrs['math-type']) {
          medEquationAttrs.mathType = Number(runChildAttrs['math-type']);
        }
        if (mathValueNodes && mathValueNodes.children.length > 0) {
          const mathValue = mathValueNodes.children[0];
          let rMathValue = mathValue;
          if (typeof rMathValue === 'string') {
            // may be problematic
            if (rMathValue.search(/&amp;|&lt;|&quot;|&gt;/) !== -1) {
              rMathValue = rMathValue.replace(/&amp;/g, '&')
              .replace(/&lt;/g, '<')
              .replace(/&gt;/g, '>')
              .replace(/&quot;/g, '"');
            } else {
              rMathValue = decodeURIComponent(rMathValue);
            }

            medEquationAttrs.mathValue = rMathValue;
          }
        }
        if (mathTextNodes && mathTextNodes.children.length > 0) {
          const mathText = mathTextNodes.children[0];
          if (typeof mathText === 'string') {
            medEquationAttrs.mathText = mathText;
          }
        }
        if (runChildAttrs['vertAlign']) {
          medEquationAttrs.vertAlign = Number(runChildAttrs['vertAlign']);
        }

        // editable painting
        if (uniqueImagelist != null) {
          medEquationAttrs.mathType = EquationType.EditableSvg;
          const imageDataSource = uniqueImagelist.get(medEquationAttrs.source);
          if (imageDataSource != null) {
            medEquationAttrs.mathValue = unescapeXML(imageDataSource.editableData);
          }
        }

      } else {
        // alert(ErrorMessages.XmlError);
        // tslint:disable-next-line: no-console
        console.warn(ErrorMessages.XmlError);
      }
    }
    // console.log(medEquationAttrs)
    return medEquationAttrs;
  }

  /**
   * if find duplicate name, add '1' at end until unique case reached
   */
  public static setNewNameForDuplicate(
    document: Document, bInsertFile: boolean, attrCollection: any,
    insertFileObj?: IInsertFileContent
  ): void {
    try {
      if (document && !document.drawingObjects.checkUniqueImageName(attrCollection.name)) {
        if (!bInsertFile) {
          throw new Error('find duplicate paradrawing name out of inserting file');
        } else {
          let tempName = attrCollection.name;
          // change equation name
          tempName += '1';
          let tempTimes = 0;
          while (!document.drawingObjects.checkUniqueImageName(tempName)) {
            tempName += 1;
            ++tempTimes;
            if (tempTimes >= 100) {
              break;
            }
          }
          if (tempTimes >= 100) {
            throw new Error('cannot set a new name for a paradrawing which has duplicate name');
          } else {
            attrCollection.name = tempName;
          }

        }
      } else if (bInsertFile && insertFileObj) {
        const drawingObjects = insertFileObj.drawingObjects;
        let tempName = attrCollection.name;
        // change equation name
        // tempName += '1';
        let tempTimes = 0;

        while (!drawingObjects.checkUniqueImageName(tempName)) {
          tempName += 1;
          ++tempTimes;
        }

        attrCollection.name = tempName;
      }
    } catch (error) {
      window.dispatchEvent(new ErrorEvent('error', {message: ErrorMessages.XmlCorrupted, error}));
    }
  }

  public static traverseRunProperty(runChild: any, textProperty: any, document: Document): void {

    if (runChild.nodeName === 'w:rPr') {
      const rFonts = runChild.getElementsByTagName('w:rFonts')[0];
      const sz = runChild.getElementsByTagName('w:sz')[0];
      const color = runChild.getElementsByTagName('w:color')[0];
      const b = runChild.getElementsByTagName('w:b')[0];
      const i = runChild.getElementsByTagName('w:i')[0];
      const u = runChild.getElementsByTagName('w:u')[0];
      const vertAlign  = runChild.getElementsByTagName('w:vertAlign')[0];
      const highLight = runChild.getElementsByTagName('w:highlight')[0];

      if (rFonts) {
        try {
          if (rFonts.attributes && rFonts.attributes.length > 0) {
            if (rFonts.attributes.getNamedItem('w:eastAsia')) {
              const fontFamilyVal = getFontFamilyVal(rFonts.attributes.getNamedItem('w:eastAsia').nodeValue);

              textProperty.fontFamily = fontFamilyVal;
            }
          } else {
            throw new Error('rFonts has no attributes');
          }
        } catch (error) {
          // alert(ErrorMessages.XmlError);
          window.dispatchEvent(new ErrorEvent('error', {message: ErrorMessages.XmlError, error}));
          console.warn(ErrorMessages.XmlError);
          const date = new Date();
          logger.error({id: document && document.id, code: error.stack,
            startTime: date, args: null, name: 'traverseRunProperty.rFonts.attributes'});
        }
      }
      if (sz) { // font size
        try {
          if (sz.attributes && sz.attributes.length > 0) {
            if (sz.attributes.getNamedItem('w:val')) {
              textProperty.fontSize = sz.attributes.getNamedItem('w:val').nodeValue;
            }
          } else {
            throw new Error('font size has no attributes');
          }
        } catch (error) {
          // alert(ErrorMessages.XmlError);
          window.dispatchEvent(new ErrorEvent('error', {message: ErrorMessages.XmlError, error}));
          console.warn(ErrorMessages.XmlError);
          const date = new Date();
          logger.error({id: document && document.id, code: error.stack,
            startTime: date, args: null, name: 'traverseRunProperty.sz.attributes'});
        }
      }
      if (color) { // font color
        try {
          if (color.attributes && color.attributes.length > 0) {
            if (color.attributes.getNamedItem('w:val')) {
              textProperty.color = color.attributes.getNamedItem('w:val').nodeValue;
            }
          } else {
            throw new Error('font color has no attributes');
          }
        } catch (error) {
          // alert(ErrorMessages.XmlError);
          window.dispatchEvent(new ErrorEvent('error', {message: ErrorMessages.XmlError, error}));
          console.warn(ErrorMessages.XmlError);
          const date = new Date();
          logger.error({id: document && document.id, code: error.stack,
            startTime: date, args: null, name: 'traverseRunProperty.color.attributes'});
        }
      }
      if (b) { // bold
        textProperty.fontWeight = FontWeightType.Bold;
      }
      if (i) { // italic
        textProperty.fontStyle = FontStyleType.Italic;
      }
      if (u) { // underline
        // for now, may have many variations
        textProperty.textDecorationLine = TextDecorationLineType.Underline;
      }
      if (highLight) { // background color
        try {
          if (highLight.attributes && highLight.attributes.length > 0) {
            if (highLight.attributes.getNamedItem('w:val')) {
              textProperty.backgroundColor = highLight.attributes.getNamedItem('w:val').nodeValue;
            }
          } else {
            throw new Error('background color has no attributes');
          }
        } catch (error) {
          // alert(ErrorMessages.XmlError);
          window.dispatchEvent(new ErrorEvent('error', {message: ErrorMessages.XmlError, error}));
          console.warn(ErrorMessages.XmlError);
          const date = new Date();
          logger.error({id: document && document.id, code: error.stack,
            startTime: date, args: null, name: 'traverseRunProperty.highLight.attributes'});
        }
      }
      if (vertAlign) { // vertAlign
        try {
          if (vertAlign.attributes && vertAlign.attributes.length > 0) {
            const wVal = vertAlign.attributes.getNamedItem('w:val');
            if (wVal) {
              if (wVal.nodeValue === 'superscript') {
                textProperty.vertAlign = TextVertAlign.Super;
                // portion.textProperty.superscript = true;
              } else if (wVal.nodeValue === 'subscript') {
                textProperty.vertAlign = TextVertAlign.Sub;
                // portion.textProperty.subscript = true;
              }
            }
          } else {
            throw new Error('vertAlign has no attributes');
          }
        } catch (error) {
          // alert(ErrorMessages.XmlError);
          window.dispatchEvent(new ErrorEvent('error', {message: ErrorMessages.XmlError, error}));
          console.warn(ErrorMessages.XmlError);
          const date = new Date();
          logger.error({id: document && document.id, code: error.stack,
            startTime: date, args: null, name: 'traverseRunProperty.vertAlign.attributes'});
        }
      }

      // console.log(portion.textProperty)
    }
  }

  public static tTraverseRunProperty(runChild: rtNode, textProperty: any, document: Document,
    modeFonts: IModeFonts = null): void {
      if (modeFonts != null) {
        const {defaultFont} = modeFonts;
  
        if (defaultFont != null) {
          const fontFamilyVal = getFontFamilyVal(defaultFont.fontFamily);
  
          textProperty.fontFamily = fontFamilyVal;
          textProperty.fontSize = +defaultFont.fontSize;
        }
      }

    if (runChild.tagName === 'w:rPr') {
      let rFonts: rtNode = null;
      let sz: rtNode = null;
      let color: rtNode = null;
      let b: rtNode = null;
      let i: rtNode = null;
      let u: rtNode = null;
      let vertAlign: rtNode  = null;
      let highLight: rtNode = null;
      for (const runPropsNode of runChild.children) {
        if (typeof runPropsNode === 'object') {
          switch (runPropsNode.tagName) {
            case 'w:rFonts': {
              rFonts = runPropsNode;
              break;
            }
            case 'w:sz': {
              sz = runPropsNode;
              break;
            }
            case 'w:color': {
              color = runPropsNode;
              break;
            }
            case 'w:b': {
              b = runPropsNode;
              break;
            }
            case 'w:i': {
              i = runPropsNode;
              break;
            }
            case 'w:u': {
              u = runPropsNode;
              break;
            }
            case 'w:vertAlign': {
              vertAlign = runPropsNode;
              break;
            }
            case 'w:highlight': {
              highLight = runPropsNode;
              break;
            }
            default: {
              break;
            }
          }
        }
      }

      if (rFonts) {
        try {
          if (rFonts.attributes) {
            if (rFonts.attributes['w:eastAsia']) {
              const fontFamilyVal = getFontFamilyVal(rFonts.attributes['w:eastAsia']);
              textProperty.fontFamily = fontFamilyVal;
            }
          } else {
            throw new Error('rFonts has no attributes');
          }
        } catch (error) {
          // alert(ErrorMessages.XmlError);
          window.dispatchEvent(new ErrorEvent('error', {message: ErrorMessages.XmlError, error}));
          console.warn(ErrorMessages.XmlError);
          const date = new Date();
          logger.error({id: document && document && document.id, code: error.stack,
            startTime: date, args: null, name: 'traverseRunProperty.rFonts.attributes'});
        }
      }
      if (sz) { // font size
        try {
          if (sz.attributes) {
            if (sz.attributes['w:val']) {
              textProperty.fontSize = +sz.attributes['w:val'];
            }
          } else {
            throw new Error('font size has no attributes');
          }
        } catch (error) {
          // alert(ErrorMessages.XmlError);
          window.dispatchEvent(new ErrorEvent('error', {message: ErrorMessages.XmlError, error}));
          console.warn(ErrorMessages.XmlError);
          const date = new Date();
          logger.error({id: document && document && document.id, code: error.stack,
            startTime: date, args: null, name: 'traverseRunProperty.sz.attributes'});
        }
      }
      if (color) { // font color
        try {
          if (color.attributes) {
            if (color.attributes['w:val']) {
              textProperty.color = color.attributes['w:val'];
            }
          } else {
            throw new Error('font color has no attributes');
          }
        } catch (error) {
          // alert(ErrorMessages.XmlError);
          window.dispatchEvent(new ErrorEvent('error', {message: ErrorMessages.XmlError, error}));
          console.warn(ErrorMessages.XmlError);
          const date = new Date();
          logger.error({id: document && document && document.id, code: error.stack,
            startTime: date, args: null, name: 'traverseRunProperty.color.attributes'});
        }
      }
      if (b) { // bold
        textProperty.fontWeight = FontWeightType.Bold;
      }
      if (i) { // italic
        textProperty.fontStyle = FontStyleType.Italic;
      }
      if (u) { // underline
        // for now, may have many variations
        textProperty.textDecorationLine = TextDecorationLineType.Underline;
      }
      if (highLight) { // background color
        try {
          if (highLight.attributes) {
            if (highLight.attributes['w:val']) {
              textProperty.backgroundColor = highLight.attributes['w:val'];
            }
          } else {
            throw new Error('background color has no attributes');
          }
        } catch (error) {
          // alert(ErrorMessages.XmlError);
          window.dispatchEvent(new ErrorEvent('error', {message: ErrorMessages.XmlError, error}));
          console.warn(ErrorMessages.XmlError);
          const date = new Date();
          logger.error({id: document && document && document.id, code: error.stack,
            startTime: date, args: null, name: 'traverseRunProperty.highLight.attributes'});
        }
      }
      if (vertAlign) { // vertAlign
        try {
          if (vertAlign.attributes) {
            const wVal = vertAlign.attributes['w:val'];
            if (wVal) {
              if (wVal === 'superscript') {
                textProperty.vertAlign = TextVertAlign.Super;
                // portion.textProperty.superscript = true;
              } else if (wVal === 'subscript') {
                textProperty.vertAlign = TextVertAlign.Sub;
                // portion.textProperty.subscript = true;
              }
            }
          } else {
            throw new Error('vertAlign has no attributes');
          }
        } catch (error) {
          // alert(ErrorMessages.XmlError);
          window.dispatchEvent(new ErrorEvent('error', {message: ErrorMessages.XmlError, error}));
          console.warn(ErrorMessages.XmlError);
          const date = new Date();
          logger.error({id: document && document && document.id, code: error.stack,
            startTime: date, args: null, name: 'traverseRunProperty.vertAlign.attributes'});
        }
      }

      // console.log(portion.textProperty)
    }
  }

  public static tPrepareBarcodeAttrs(drawingAttrsXmlObj: any): IBarcodeAllProperties {
    const drawingAttrs: IBarcodeAllProperties = {
      name: null,
      width: null,
      height: null,
      src: null,
      sourceBind: null,
      textAlign: null,
      content: null,
      bUse: true,
      errorCL: null,
    };

    if (drawingAttrsXmlObj) {
      if (drawingAttrsXmlObj.attributes['name']) {
        drawingAttrs.name = drawingAttrsXmlObj.attributes['name'];
      }
      if (drawingAttrsXmlObj.attributes['width']) {
        drawingAttrs.width = Number(drawingAttrsXmlObj.attributes['width']);
      }
      if (drawingAttrsXmlObj.attributes['height']) {
        drawingAttrs.height = Number(drawingAttrsXmlObj.attributes['height']);
      }
      if (null != drawingAttrsXmlObj.attributes['bUse']) {
        drawingAttrs.bUse = 'false' === drawingAttrsXmlObj.attributes['bUse'] ? false : true;
      }
      if (drawingAttrsXmlObj.attributes['src']) {
        drawingAttrs.src = drawingAttrsXmlObj.attributes['src'];
      }
      if (drawingAttrsXmlObj.attributes['sourceBind']) {
        drawingAttrs.sourceBind = drawingAttrsXmlObj.attributes['sourceBind'];
      }
      if (drawingAttrsXmlObj.attributes['content']) {
        drawingAttrs.content = drawingAttrsXmlObj.attributes['content'];
      }
      if (drawingAttrsXmlObj.attributes['textAlign']) {
        drawingAttrs.textAlign = drawingAttrsXmlObj.attributes['textAlign'];
      }
      if (drawingAttrsXmlObj.attributes['errorCL']) {
        drawingAttrs.errorCL = drawingAttrsXmlObj.attributes['errorCL'];
      }
    }
    // console.log(drawingAttrs)
    return drawingAttrs;
  }

}
