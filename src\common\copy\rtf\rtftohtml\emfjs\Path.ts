import { Obj } from './Primitives';

export class Path extends Obj {
    public svgPath: any;

    constructor(svgPath: any, copy?: Path) {
        super('path');
        if (svgPath != null) {
            this.svgPath = svgPath;
        } else {
            this.svgPath = copy.svgPath;
        }
    }

    public clone(): Path {
        return new Path(null, this.svgPath);
    }

    public toString(): string {
        return '{[path]}';
    }
}
