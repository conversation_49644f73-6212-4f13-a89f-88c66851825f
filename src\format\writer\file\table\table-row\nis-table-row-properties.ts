import { XmlComponent, XmlAttributeComponent } from '../../xml-components';
import { TableRowHeight, TableRowLineRule } from '../../../../../model/core/Table/TableRowProperty';
import { TrHeight } from './table-row-properties';
import { INISRowProperty, NISRowType } from '../../../../../common/commonDefines';
import { TrNis } from './nis-table-row-properties-trNis';

export class NISTableRowProperties extends XmlComponent {
    private trNis: TrNis;

    constructor() {
        super('w:trPr');
    }

    public addRowHeight(height: TableRowHeight): NISTableRowProperties {
        const attrs: INISTableRowPropertiesProperties = {
            value: height.value,
            type: height.getRule()
        };
        this.root.push(new TrHeight(attrs));
        return this;
    }

    public addIsTableHeader(val: string, backcolor: string): NISTableRowProperties {
        const nisTblHeaderAttrs: INISTblHeaderAttributesProperties = {
            backcolor: '',
        };
        if (backcolor != null) {
            nisTblHeaderAttrs.backcolor = backcolor;
        }
        this.root.push(new NISTblHeader(val, nisTblHeaderAttrs));
        return this;
    }

    public addReadOnly(val: string): NISTableRowProperties {
        this.root.push(new NISTrReadonly(val));
        return this;
    }

    public addTrNis(nisProperty: INISRowProperty): NISTableRowProperties {

        const attrs = {type: NISRowType.Normal};
        if (nisProperty != null) {
            attrs.type = nisProperty.type;
        }
        this.trNis = new TrNis(attrs);

        // also add sub contents for each type
        this.trNis.addContent(nisProperty);

        this.root.push(this.trNis);
        return this;
    }

}

export interface INISTableRowPropertiesProperties {
    value: number;
    type: TableRowLineRule;
}

export interface INISTblHeaderAttributesProperties {
    backcolor: string;
}

class NISTblHeaderAttributes extends XmlAttributeComponent<INISTblHeaderAttributesProperties> {
    protected xmlKeys: any = {
        backcolor: 'backcolor',
    };
}

// tslint:disable-next-line: max-classes-per-file
export class NISTblHeader extends XmlComponent {
    constructor(flag: string, attrs: INISTblHeaderAttributesProperties) {
        super('w:tblHeader');
        this.root.push(new NISTblHeaderAttributes(attrs));
        this.root.push(flag);
    }
}

// tslint:disable-next-line: max-classes-per-file
export class NISTrBackColor extends XmlComponent {
    constructor(flag: string) {
        super('w:trBackcolor');
        this.root.push(flag);
    }
}

// tslint:disable-next-line: max-classes-per-file
export class NISTrReadonly extends XmlComponent {
    constructor(flag: string) {
        super('w:trReadonly');
        this.root.push(flag);
    }
}
