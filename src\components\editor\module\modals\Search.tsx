import * as React from 'react';
import { ISearchWord } from '@/common/commonDefines';
import {
    GlobalEvent as gEvent,
    GlobalEventName as gEventName,
} from '../../../../common/GlobalEvent';
import Checkbox from '../../ui/CheckboxItem';

interface IProps {
    documentCore: any;
    visible: boolean;
    id: string;
    host: any;
    refresh: (bRefresh: boolean, num?: number) => void;
    close: (name: string | number, bRefresh?: boolean) => void;
}

export default class SearchUI extends React.Component<IProps, {}> {
    private _documentCore: any;
    private visible: boolean;
    private host: any;
    private oldKeword: string;
    private datas: ISearchWord;
    private counter: number;
    private oldCounter: number;
    private currentIndex: number;
    private bSet: boolean;
    private bOpen: boolean;
    private inputRef: any;
    private devRef: any;
    private timer: any;
    private _className: string = '';
    private _bMove: boolean;
    private _subLeft: number;
    private _subTop: number;
    private _left: number | string;
    private _top: number | string;
    constructor(props: any) {
        super(props);
        this._documentCore = props.documentCore;
        this.visible = props.visible;
        this.bOpen = true;
        this.host = props.host;
        this.inputRef = React.createRef();
        this.devRef = React.createRef();
        this.init();
        this.open();
    }

    public render(): any {
        if (!this.visible) {
            return null;
        }
        return (
            <div className='search-dialog' ref={this.devRef}>
                <div className='editor-line search-dialog-box-header'>
                    <label>查找</label>
                    <span>
                        <i
                            className={'iconfont setting' + (this.bSet ? ' active' : '')}
                            onClick={this.onClick}
                        />
                        <i className='iconfont xLine' onClick={this.close}/>
                    </span>
                </div>
                <div className='editor-line'>
                    <i
                        onClick={this.confirm}
                        className={'iconfont search' + (this.datas.keyword ? '' : ' disabled')}
                    />
                    <input
                        value={this.datas.keyword}
                        onChange={this.onInputChange}
                        placeholder='请输入查找内容'
                        ref={this.inputRef}
                        onKeyDown={this.keydown}
                    />
                    <span>{this.renderCounter()}</span>
                </div>
                <div className='editor-line' style={{display: this.bSet ? '' : 'none'}}>
                    <Checkbox name='bPlaceholder' value={this.datas.bPlaceholder} onChange={this.onChange}>
                        忽略占位符
                    </Checkbox>
                    <Checkbox name='bCaseSensitive' value={this.datas.bCaseSensitive} onChange={this.onChange}>
                        忽略大小写
                    </Checkbox>
                </div>
                <div>
                    <span
                        onClick={this.prevItem}
                        className={this.counter === 0 ? 'disabled' : ''}
                    >
                        上一条
                    </span>
                    <span
                        onClick={this.nextItem}
                        className={this.counter === 0 ? 'disabled' : ''}
                    >
                        下一条
                    </span>
                </div>
            </div>
        );
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        if (nextProps.visible && nextProps.visible !== this.visible) {
            this.bOpen = true;
            this.open();
        }
        this.visible = nextProps.visible;
    }

    public componentDidUpdate(): void {
        if (this.bOpen) {
            this.inputRef.current?.focus();
            this.bOpen = false;
            const devDom = this.devRef.current;
            if (devDom) {
                const doc = devDom.ownerDocument;
                devDom.addEventListener('mousedown', this.mousedown);
                devDom.addEventListener('mousemove', this.mousemove);
                doc.addEventListener('mouseup', this.docMouseUp);

                const position = devDom.getBoundingClientRect();
                this._left = position.left;
                this._top = position.top;
            }
        }
    }

    public componentDidMount(): void {
        const dom = this.inputRef.current;
        if (dom) {
            dom.focus();
            // dom.addEventListener('keydown', this.keydown);
        }

        if (this.props.host) {
            gEvent.addEvent(this.host.docId, gEventName.ContentChange, this.clearSearch);
            gEvent.addEvent(this.host.docId, gEventName.OpenCompleted, this.openCompleted);
        }

        const devDom = this.devRef.current;
        if (devDom) {
            const doc = devDom.ownerDocument;
            devDom.addEventListener('mousedown', this.mousedown);
            devDom.addEventListener('mousemove', this.mousemove);
            doc.addEventListener('mouseup', this.docMouseUp);

            const position = devDom.getBoundingClientRect();
            this._left = position.left;
            this._top = position.top;
        }

        this.bOpen = false;
    }

    public componentWillUnmount(): void {
        // const dom = this.inputRef.current;
        // if (dom) {
        //     dom.removeEventListener('keydown', this.keydown);
        // }

        if (this.props.host) {
            gEvent.deleteEvent(this.host.docId, gEventName.ContentChange, this.clearSearch);
            gEvent.deleteEvent(this.host.docId, gEventName.OpenCompleted, this.openCompleted);
        }
    }

    private keydown = (e: any) => {
        if (e.keyCode === 13) {
            this.confirm();
        }
    }

    private openCompleted = (): void => {
        if (!this.visible) {
            return;
        }

        this.close();
    }

    private onClick = (e: any) => {
        this.bSet = !this.bSet;
        this.setState({});
    }

    private renderCounter(): any {
        let index = 0;
        if (this.counter) {
            index = this.currentIndex + 1;
        }
        return (
        <React.Fragment>
            {index}/{this.counter || 0}
        </React.Fragment>
        );
    }

    private init(): any {
        this.datas = {keyword: null};
        this.counter = 0;
        this.currentIndex = -1;
        this.bSet = false;
        // this.setState({});
    }

    private open = (): void => {
        const keyword = this._documentCore.getSelectText();
        this.datas.keyword = keyword;
        if (keyword) {
            setTimeout(() => {
                this.confirm();
            }, 1);
        }
    }

    private onInputChange = (event: any): void => {
        const keyword = event.target.value;
        if (this.datas.keyword === keyword) {
            return;
        }
        this.datas.keyword = keyword;
        if (!keyword) {
            if (this.oldCounter > 0) {
                this.counter = this.oldCounter;
                this.clearSearch();
                return;
            }
        }

        this.counter = 0;
        this.setState({});
    }

    private onChange = (value: any, name: string): void => {
        this.counter = 0;
        this.datas[name] = value;
        this.setState({});
    }

    private prevItem = (): void => {
        if (this.counter < 1) {
            return;
        }
        if (this.currentIndex <= 0) {
            this.currentIndex = this.counter;
        }
        this.currentIndex--;
        this._documentCore.jumpToOneSearch(this.currentIndex);
        this.setState({});
        this.refresh();
    }

    private nextItem = (): void => {
        if (this.counter < 1) {
            return;
        }
        if (this.currentIndex + 1 >= this.counter) {
            this.currentIndex = -1;
        }
        this.currentIndex++;
        this._documentCore.jumpToOneSearch(this.currentIndex);
        this.setState({});
        this.refresh();
    }

    private refresh = (bRefresh: any = true): void => {
        gEvent.setEvent(this.host.docId, gEventName.OutFocus, true);
        this.props.refresh(true);
        clearTimeout(this.timer);
        this.timer = setTimeout(() => {
            gEvent.setEvent(this.host.docId, gEventName.OutFocus, false);
        }, 500);
    }

    private close = (): void => {
        this.init();
        this.visible = false;
        const props = this.props;
        this.bOpen = false;
        this._documentCore.closeSearch();
        this.setState({});
        props.close(props.id, this.oldCounter > 0);
        this.oldCounter = 0;
        const devDom = this.devRef.current;
        if (devDom) {
            const doc = devDom.ownerDocument;
            devDom.addEventListener('mousedown', this.mousedown);
            devDom.addEventListener('mousemove', this.mousemove);
            doc.addEventListener('mouseup', this.docMouseUp);
        }
    }

    private confirm = (): void => {
        const keyword = this.datas.keyword;
        if (!keyword) {
            this.counter = 0;
            return;
        }
        const res = this._documentCore.setSearchInfo(this.datas);
        if (res === 0) {
            // this.currentIndex = -1;
            this.counter = 0;
            if (this.oldCounter !== res) {
                this.oldCounter = 0;
                this.setState({});
                this.refresh();
            }

            return;
        } else if (res !== this.counter) {
            if (this.oldKeword !== keyword) {
                this.oldKeword = keyword;
                this.currentIndex = -1;
            }

            this.counter = res;
        }
        if (this.currentIndex + 1 === this.counter) {
            this.currentIndex = -1;
        }
        this.oldCounter = res;
        this.nextItem();
    }

    private clearSearch = (): void => {
        if (0 !== this.counter) {
            this.counter = 0;
            this.currentIndex = -1;
            this.bSet = false;
            this.oldCounter = 0;

            this._documentCore.closeSearch();
            this.setState({});
            this.refresh();
        }
    }

    private mousedown = (e: any): void => {
        const target = e.target;
        const className = this._className = target.className;
        if (className.indexOf('search-dialog-box-header') > -1) {
            // this.removeModalClass();
            this._bMove = true;
            this._subLeft = e.clientX - (this._left as number);
            this._subTop = e.clientY - (this._top as number);
            // e.stopPropagation();
        }  else if (className.indexOf('dialog-box-close') > -1) {
            if (this.props.close) {
                this.props.close(this.props.id);
            }
        }
    }

    private mousemove = (e: any): void => {
        if (this._bMove !== true) {
            return;
        }

        if (this._className.indexOf('search-dialog-box-header') > -1) {
            const x = e.clientX;
            const y = e.clientY;
            let left = this._left as number;
            let top = this._top as number;
            left = x - this._subLeft;
            top = y - this._subTop;
            this._left = left;
            this._top = top;
            this.setPosition();
            e.stopPropagation();
        }
    }

    private docMouseUp = (e: any): void => {
        if (this._bMove === false) {
            return;
        }
        this._bMove = false;
    }

    private setPosition(): void {
        const dom = this.devRef.current;
        dom.style.left = this.getCell(this._left);
        dom.style.top = this.getCell(this._top);
    }

    private getCell(val: number | string): string {
        if (typeof val === 'number') {
            return val + 'px';
        }

        return val;
    }
}
