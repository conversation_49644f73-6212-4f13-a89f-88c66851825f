import * as React from 'react';
import Dialog from '../../ui/Dialog';
import Input from '../../ui/Input';
import Button from '../../ui/Button';
import { message } from '../../../../common/Message';

interface IDialogProps {
    documentCore: any;
    visible: boolean;
    id: string;
    callback?: (option: any) => void;
    data?: any;
    close: (name: string | number, bRefresh?: boolean) => void;
}

interface ITestContent {
    name?: string;
    step?: string;
    case?: string;
    url?: string;
}

export default class DeleteCellDialog extends React.Component<IDialogProps, {}> {
    private visible: boolean;
    private data: ITestContent;
    constructor(props: any) {
        super(props);
        this.visible = this.props.visible;
        this.data = {};
    }

    public render(): any {
        return (
            <Dialog
                id={this.props.id}
                preventDefault={true}
                visible={this.visible}
                open={this.open}
                footer={this.renderFooter()}
                width={350}
                title={'自动化测试'}
            >
                <div>
                    <div className='editor-line'>
                        <div className='w-70 must-input'>名称</div>
                        <div className='right-auto'>
                            <Input
                                name='name'
                                value={this.data.name}
                                onChange={this.onChange}
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <div className='w-70  must-input'>模块</div>
                        <div className='right-auto'>
                            <Input
                                name='case'
                                value={this.data.case}
                                onChange={this.onChange}
                            />
                        </div>
                    </div>
                    <div className='editor-line editor-multi-line'>
                        <div className='w-70'>步骤</div>
                        <div className='right-auto'>
                            <textarea
                                style={{width: '100%', height: '80px'}}
                                name='step'
                                value={this.data.step}
                                onChange={this.textareaChange}
                            />
                        </div>
                    </div>
                </div>
            </Dialog>
        );
    }

    public componentDidMount(): void {
        //
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        this.visible = nextProps.visible;
    }

    private renderFooter(): any {
        return (
            <span>
                <Button onClick={this.close}>取消</Button>
                <Button type='primary' onClick={this.confirm}>确认</Button>
            </span>
        );
    }

    private open = (): void => {
        if (this.props.data) {
            this.data = {...this.props.data};
        } else {
            this.data = {};
        }
        this.setState({});
    }

    private onChange = (value: any, name: string): void => {
        this.data[name] = value;
    }

    private textareaChange = (e) => {
        this.data.step = e.target.value;
        this.setState({});
    }

    private close = (bRefresh?: boolean): void => {
        this.props.close(this.props.id, bRefresh);
        this.visible = false;
        this.setState({});
    }

    private confirm = (): void => {
        const data = this.data;
        if (!data.name) {
            message.error('请输入名称');
            return;
        }
        if (!data.case) {
            message.error('请输入模块');
            return;
        }
        this.props.callback(this.data);
        this.close(true);
    }
}
