import {Document} from './Document';
import { DocumentCore } from '../../../../model/DocumentCore';
import { message } from '../../../../common/Message';
import { getPageElement, IFixedCellType, MessageType, NISTableCellColor,
    ResultType,
    ViewModeType } from '../../../../common/commonDefines';
import {GlobalEvent as gEvent, GlobalEventName as gEventName} from '../../../../common/GlobalEvent';
import { consoleLog, isGlobalTestData } from '../../../../common/GlobalTest';
import { CellMessageTip } from 'src/common/CellMessageTip';

enum CursorClass {
    Pointer = 'pointer',
    NotAllowed = 'not-allowed',
    Auto = 'auto',
}

export default class NISTableEvent {
    private host: Document;
    private docId: number;
    private documentCore: DocumentCore;
    private curCell: any;
    private bActiveClass: boolean;
    private oldSignCells: any;
    private oldRow: any;
    private downDom: any;
    private oldCell: any;
    // private oldChageCell: any;
    private bRenderCellBordered: boolean;
    private oldBoundData: any;
    private bTableMoveing: boolean;
    private oldCellPageNum: number;
    private oldTables: any;
    private oldTableTimeout: any;
    private errorCells: Map<number, any>;
    private curErrorCellId: any = null;
    private cellMessageTip: any;
    private oldNumBounds: any;

    constructor(host: Document) {
        this.host = host;
        this.docId = host.docId;
        this.documentCore = host.documentCore;
        this.addEvents();
        this.curCell = null;
        this.bActiveClass = false;
        // this.errorCells = new Map<number, any>();
        this.cellMessageTip = new CellMessageTip(this.host);
        this.oldNumBounds = {};
    }

    public getCellMessageTip(): CellMessageTip {
        return this.cellMessageTip;
    }

    public contentChange = (): void => {
        const table = this.documentCore.getCurrentTable();

        if ( table && table.isNISTable() && table.execFormulaCalc() ) {
            if (this.host.isNoRefresh()) {
                this.host.updateRefreshFlag2(true);
            } else {
                this.host.refresh();
            }
        }

        // this.oldDownEvent = null;
        // this.getCurrentCell();
        this.renderCellBorder();
        this.renderErrorCellBorder();
    }

    public clearDatas(): void {
        this.oldCell = null;
        this.oldRow = null;
        this.curCell = null;
        this.oldSignCells = null;
        this.downDom = null;
    }

    public onInput = (content: string, bDeleted: boolean = false): boolean => {
        if (isGlobalTestData()) {
            consoleLog(content, bDeleted);
            consoleLog(this.curCell);
        }
        const cell = this.curCell;
        if (!cell || cell.isTimeCell() !== true && cell.isBPCell() !== true) {
            return false;
        }

        return this.updateCellContent(content, bDeleted);
    }

    public onKeyDown = (e: any): boolean => {
        const cell = this.curCell;
        if (!cell) {
            return true;
        }

        if (!cell.isTimeCell() && !cell.isBPCell()) {
            return true;
        }

        const keyCode = e.keyCode;
        let text = e.key;
        const arrs = [16, 17, 18, 33, 34, 38, 40, 9];
        if (keyCode === 229) {
            text = e.code.replace('Digit', '');
            if (/^\d+$/.test(text)) {
                arrs.push(229);
            }
        }
        if (arrs.includes(keyCode)) {
            return true;
        }
        const documentCore = this.documentCore;

        if ([8, 46].includes(keyCode)) {
            if ( documentCore.isProtectedMode() || false === documentCore.canDelete() ) {
                return false;
            }
            this.updateCellContent(keyCode + '', true);
            return false;
        }

        if (/^\d+/.test(text)) {
            if ( documentCore.isProtectedMode() || false === documentCore.canInput() ) {
                return false;
            }
            this.updateCellContent(text, false);
            return false;
        }

        if (keyCode === 37 || keyCode === 39) {
            const res = cell.moveCursor(keyCode);
            if (res) {
                this.host.setSelections();
            } else {
                return true;
            }
        }

        if (isGlobalTestData()) {
            consoleLog(text, keyCode, cell);
        }

        return false;
    }

    private updateCellContent(content: string, bDeleted: boolean): boolean {
        let num: number;
        if (this.curCell.isTimeCell()) {
            num = this.curCell.updateTimeCellContent(content, bDeleted);
        } else {
            num = this.curCell.updateBPCellContent(content, bDeleted);
        }

        if (num === 0) {
            this.host.refreshPages();
            return true;
        }

        return false;
    }

    private getCurrentCell = (nType?: number): void => {
        let cell = null;
        const documentCore = this.documentCore;
        const table = documentCore.getCurrentTable();
        if ( table && table.isNISTable() ) {
            cell = table.getCurrentCell();
            // if ( cell === this.curCell ) {
            //     return ;
            // }
        }

        this.curCell = cell;
        if (nType !== 1) {
            this.renderCellBorder();
            this.cellChange();
        }
        // this.renderSignCellBackground();
    }

    private handleMouseDown = (e: any): void => {
        // this.bFromMouseDown = true;
        // const pageNode = getPageElement(e.target);
        // if (!pageNode) {
        //     return;
        // }

        // const documentCore = this.documentCore;
        // const scale = this.host.getScale();
        // let pointX = e.offsetX / scale;
        // let pointY = e.offsetY / scale + this.getPaddingtTop();

        // const curState = documentCore.getCursorStateInDocument(pointX, pointY, this.host.getPageIndex());
        // pointX = curState.pointX;
        // pointY = curState.pointY;
        // const curTableCell = documentCore.getTableCellByXY(pointX, pointY, this.host.getCurPageIndex());

        // this.bTableMoveing = documentCore.isMovingTableBorder();
        // if (this.bTableMoveing !== true) {
        //     this.downDom = e.target;
        //     setTimeout(() => {
        //         this.showNISTableOuterLayer(curTableCell, pointX, pointY, e);
        //     }, 0);
        // }
        // console.log(curTableCell);
        // console.log(curTable)

        // this.gMouseEvent.pointX = pointX;
        // this.gMouseEvent.pointY = pointY;

        // this.getCurrentRegion();
        // this.downDom = e.target;
        // setTimeout(() => {
        //     this.showNISTableOuterLayer(curTableCell, pointX, pointY, e);
        // }, 0);
    }

    private showNISTableOuterLayer(curTableCell: any, pointX: number, pointY: number, e: any): void {
        const documentCore = this.documentCore;
        const curCell = documentCore.getTableCellByXY(pointX, pointY, this.host.getCurPageIndex());
        const bReadOnly = documentCore.isProtectedMode();
        // console.log(curCell)
        if (!curCell || bReadOnly) {
            gEvent.setEvent(this.docId, gEventName.NewNISCellToShow);
            return;
        }

        const curRow = curTableCell != null ? curTableCell.getRow() : null;
        const curTable = curRow ? curRow.getTable() : null;
        const bRowReadOnly = curRow ? curRow.isReadOnly() : true;

        if ( !bRowReadOnly && curCell && curCell.isQuickCell() ) {
            if ( 2 === e.button && curTable && !curTable.isSelectionUse() ) {
                const cellType = curTableCell.getCellType();
                gEvent.setEvent(this.docId, gEventName.NewNISCellToShow, cellType, this.host.getPageIndex(),
                    curTableCell.getId(), undefined, curTableCell.getNISProperty());
            } else {
                gEvent.setEvent(this.docId, gEventName.NewNISCellToShow);
            }
            return;
        } else if ( 2 === e.button || bRowReadOnly ) {
            gEvent.setEvent(this.docId, gEventName.NewNISCellToShow);
            return;
        }

        if (curTable.isNISTable() &&
            !documentCore.isTableBorder(pointX, pointY, this.host.getPageIndex())) {
            const cellType = curTableCell.getCellType();
            gEvent.setEvent(this.docId, gEventName.NewNISCellToShow, cellType, this.host.getPageIndex(),
                curTableCell.getId(), undefined, curTableCell.getNISProperty());
        }
    }

    private handleMouseMove = (e: any): void => {
        const pageNode = getPageElement(e.target);
        if (!pageNode) {
            return;
        }

        // date1 = new Date();
        const pageId = this.host.getCurPageIndex();
        const scale = this.host.getScale();
        const offsetY = e.offsetY / scale + this.getPaddingtTop();

        // update gMouseEvent class
        const pointX = e.offsetX / scale;
        const pointY = offsetY;
        // const documentCore = this.documentCore;

        // const bPrint = this.host.isPrint();
        // const cursorState = !bPrint ? documentCore.getCursorStateInDocument(pointX , offsetY, pageId)
        //                     : null;
        // this.gMouseEvent.pointX = cursorState ? cursorState.pointX : this.gMouseEvent.pointX;
        const overCell = this.documentCore.getTableCellByXY(pointX, pointY, pageId);
        // show error message infomation
        if (overCell) {
            this.showCellErrorMessageInfo(overCell, 1);
        }
        this.getCurrentCell(1);
        this.setCurrentCursor(pointX , offsetY, pageId);
    }

    private handleMouseUp = (e: any): void => {
        this.getCurrentCell();
        const cell = this.curCell;

        const documentCore = this.documentCore;
        const scale = this.host.getScale();
        const pointX = e.offsetX / scale;
        const pointY = e.offsetY / scale + this.getPaddingtTop();
        this.bTableMoveing = documentCore.isMovingTableBorder();
        if (this.bTableMoveing !== true) {
            this.downDom = e.target;
            setTimeout(() => {
                this.showNISTableOuterLayer(cell, pointX, pointY, e);
            }, 0);
        }

        if (cell && cell.selectCellContent() && this.bTableMoveing !== true) {
            this.host.setSelections();
        }
        // this.downDom = undefined;
        // if (this.host.isSelected()) {
        //     this.removeCellBorder();
        // }

        // 由于禁止冒泡，所以这里进行事件添加
        this.winClick(e);
    }

    private removeCellHover = (): void => {
        if (!this.oldRow) {
            return;
        }
        const dom = this.getContainer();
        if (!dom) {
            return;
        }
        const rects = dom.querySelector('.table-hoverbackground > rect');
        if (!rects || !rects.parentNode) {
            return;
        }
        (rects.parentNode as HTMLElement).innerHTML = '';

        // for (let index = 0, len = rects.length; index < len; index++) {
        //     rects[index].removeAttribute('data-hover');
        // }
    }

    private setCurrentCellHover(cell: any): void {
        if (this.host.isPrint()) {
            return;
        }
        if (!cell || (cell && cell.row && cell.row.isTableHeader())) {
            this.removeCellHover();
            this.oldRow = null;
            return;
        }
        const row = cell.row;
        if (!row || !row.table.isNISTable()) {
            this.oldRow = null;
            return;
        }

        if (this.curCell && this.curCell.row.id === row.id) {
            this.removeCellHover();
            this.oldRow = null;
            return;
        }

        if (this.oldRow && this.oldRow === row.id) {
            return;
        }

        this.oldRow = row.id;
        const dom = this.getContainer();
        if (!dom) {
            return;
        }
        const pageIndex = this.host.getCurPageIndex();
        const svgs = dom.querySelectorAll(`.page-wrapper[page-index='${pageIndex}']>svg`);
        const svg = svgs[svgs.length - 1];
        if (!svg) {
            return;
        }
        let rectsText = '';
        const rects = row.getRowBounds2(pageIndex);
        // console.log(rects);
        rects.forEach((rect) => {
            if (!rect || rect.y == null) {
                return;
            }
            rectsText += `<rect  width="${rect.width}" height="${rect.height}" x="${rect.x}" y="${rect.y}"></rect>`;
        });
        let background = svg.querySelector('.table-hoverbackground');
        if (!background) {
            background = document.createElementNS('http://www.w3.org/2000/svg', 'g');
            background.setAttribute('class', 'table-hoverbackground');
            svg.appendChild(background);
        }
        background.innerHTML = rectsText;
        // for (let index = 0, len = rects.length; index < len; index++) {
        //     const rect = rects[index];
        //     const hoverAttr = rect.getAttribute('data-hover');
        //     if (hoverAttr) {
        //         rect.removeAttribute('data-hover');
        //     }
        //     const id = +rect.getAttribute('data-key');
        //     const cellIndex = cellIds.findIndex((item) => item === id);
        //     if (cellIndex !== -1) {
        //         rect.setAttribute('data-hover', '1');
        //         cellIds.splice(cellIndex, 1);
        //     }
        // }
    }

    private setCurrentCursor(pointX: number, pointY: number, pageId: number): void {
        // this.removeCursorClass();

        const cell = this.documentCore.getTableCellByXY(pointX , pointY, pageId);
        this.setCurrentCellHover(cell);
        const className = this.getCursorClass(cell);
        if (!className) {
            return;
        }

        const dom = this.getContainer();
        if (dom.className.indexOf(className) > -1) {
            return;
        }
        dom.className = className;
        this.bActiveClass = true;
    }

    private getCursorClass(cell: any): any {
        if ( this.documentCore.isCursorInNewControl() ) {
            return;
        }

        const className = ' cursor-';
        const bProtect = this.documentCore.isProtectedMode();
        if (bProtect) {
            return className + CursorClass.NotAllowed;
        }

        if ( cell ) {
            if (cell.isCellProtected() || (cell.row && cell.row.isReadOnly())) {
                return className + CursorClass.NotAllowed;
            } else if ( cell.isDateCell() || cell.isTimeCell() || cell.isSignCell() ) {
                return className + CursorClass.Pointer;
            }
        }

        return className + CursorClass.Auto;
    }

    private removeCursorClass(): void {
        if (this.bActiveClass !== true) {
            return;
        }
        const container = this.getContainer();
        container.className = container.className.replace(/\s+cursor-[a-z]+(-[a-z]+)?/g, '');
        this.bActiveClass = false;
    }

    private getTableContainer(target: any): HTMLElement {
        if (!target) {
            return;
        }
        const tagName = target.tagName;
        if (['body', 'svg'].includes(tagName)) {
            return;
        }
        if (tagName === 'g' && target.classList.contains('table-container')) {
            return target;
        }

        return this.getTableContainer(target.parentNode);
    }

    private bSameCellBorder(bound: any): boolean {
        if (!bound || bound.y == null) {
            return false;
        }
        const old = this.oldBoundData;
        if (!old) {
            return false;
        }
        const newBoundData = bound.width.toFixed(2) + bound.height.toFixed(2) + bound.x.toFixed(2) + bound.y.toFixed(2);
        if (newBoundData === old) {
            return true;
        }
        this.oldBoundData = newBoundData;
        return false;
    }

    private bSameNumCellBorder(bound: any, old?: any): boolean {
        if (!bound || bound.y == null) {
            return false;
        }

        if (!old) {
            return false;
        }
        const newBoundData = bound.width.toFixed(2) + bound.height.toFixed(2) + bound.x.toFixed(2) + bound.y.toFixed(2);
        const oldData = old.width.toFixed(2) + old.height.toFixed(2) + old.x.toFixed(2) + old.y.toFixed(2);
        if (newBoundData === oldData) {
            return true;
        }

        return false;
    }

    private renderCellBorder(): void {
        if (this.host.isPrint()) {
            return;
        }
        // if (this.downDom === undefined) {
        //     return;
        // }

        // if ( NURSING_FEATURE && this.oldCell && this.oldCell.isNumberCell() && this.curCell !== this.oldCell
        //     && !this.oldCell.isValidOfNumberCellContent() ) {
        //     this.oldCell.resetNumberCellContent();
        //     this.documentCore.recalculate();
        //     this.host.refresh();
        // }

        if ( !this.curCell ) {
            this.removeCellBorder();
            // this.oldCell = null;
            return;
        }
        const bounds = this.curCell.getSelectionBounds2(this.host.getPageIndex())[0];
        if (this.bSameCellBorder(bounds)) {
            return;
        }
        // this.oldCell = this.curCell;
        this.removeCellBorder();
        if ( bounds ) {
            const dom: HTMLDivElement = this.getContainer();
            if (!dom) {
                return;
            }
            const fixedType = this.curCell.getFixedProps();
            const name = this.getSvgName(fixedType) || '';
            const svg = dom.querySelector(`.page-wrapper[page-index='${bounds.pageIndex}']>svg` + name);
            if (!svg) {
                return;
            }
            let svgPageElement: any = svg.querySelector('.singlecell-select');
            this.bRenderCellBordered = true;
            // this.downDom = undefined;
            // this.oldDownEvent = svgPageElement;
            // if (svgPageElement) {
            //     svgPageElement = dom.querySelector(`.page-container[page-id='${bounds.pageIndex}']`);
            //     const tablecellBackground = (svgPageElement.getElementsByClassName(`content`) ?
            //             svgPageElement.getElementsByClassName(`content`)[0] :
            //             svgPageElement.getElementsByClassName(`header-footer-enabled`) ?
            //                 svgPageElement.getElementsByClassName(`header-footer-enabled`)[0] : null);
            //     svgPageElement = (tablecellBackground ?
            //             tablecellBackground.getElementsByClassName(`table-container`)[0] : null)
            //         || this.getTableContainer(svgPageElement);
            // }

            // svgPageElement = svgPageElement ||
            // dom.querySelector(`.page-container[page-id='${bounds.pageIndex}'] .table-container`);
            // if ( !svgPageElement ) {
            //     // 当前页面没有渲染
            //     return;
            // }
            // svgPageElement = svgPageElement.parentNode;
            // let borderDom = svgPageElement.querySelector(`.singlecell-select`);
            if (!svgPageElement) {
                const borderDom = document.createElementNS('http://www.w3.org/2000/svg', 'g');
                borderDom.setAttribute('class', 'singlecell-select');
                const content = svg.querySelector('.region-border');
                if (content) {
                    svg.insertBefore(borderDom, content);
                } else {
                    svg.appendChild(borderDom);
                }
                svgPageElement = borderDom;
            }

            const rect = this.createCellRect();
            this.setCellRectAttributes(rect, bounds);
            svgPageElement.appendChild(rect);
        }
    }

    private isValidCellContent(cell: any): boolean {
        if (!cell) {
            return true;
        }
        if (cell && (cell.isBPCell() || cell.isTimeCell())
            && cell.getCellContentText().indexOf('--') === -1) {
                return true;
            }
        return false;
    }
    private renderErrorCellBorder(): void {
        return;
        for (const [ cellId, {cell, pageNum} ] of this.errorCells) {
            if (this.isValidCellContent(cell)) {
                this.errorCells.delete(cellId);
                continue;
            }
            this.renderCellBorderByCell(cell, pageNum, true);
        }
    }

    private renderCellBorderByCell(cell: any, cellPageNum: number, isError: boolean = false): void {
        if (this.host.isPrint()) {
            return;
        }
        if (!cell) {
            return;
        }
        return;
        const cellId = cell.getId();
        const cellClassName = `cell-${cellId}-error`;
        const cellCalssQueryName = '.' + cellClassName;
        if ( !isError ) {
            this.errorCells.delete(cellId);
            this.removeCellBorderByCell(cellCalssQueryName);
            return;
        }
        let index = 0;
        const selectionBounds = cell.getSelectionBounds(cellPageNum);
        // if (cell.isBPCell()) {
        //     index = cell.selectionBounds.length - 1;
        // }
        const bounds = selectionBounds[index];
        // tslint:disable-next-line: max-line-length
        const boundStr = `${bounds.width.toFixed(2)},${bounds.height.toFixed(2)},${bounds.x.toFixed(2)},${bounds.y.toFixed(2)}`;
        if (!this.errorCells.has(cellId)) {
            const cellErrorObj = {
                cell,
                pageNum: this.oldCellPageNum,
                oldBoundStr: boundStr
            };
            this.errorCells.set(cellId, cellErrorObj);
        } else {
            const cellObj = this.errorCells.get(cellId);
            // if (cellObj.oldBoundStr === boundStr) {
            //     return;
            // } else {
            //     cellObj.oldBoundStr = boundStr;
            // }
            cellObj.oldBoundStr = boundStr;
        }
        this.removeCellBorderByCell(cellCalssQueryName);
        if ( bounds ) {
            const dom: HTMLDivElement = this.getContainer();
            if (!dom) {
                return;
            }
            const fixedType = cell.getFixedProps();
            const name = this.getSvgName(fixedType) || '';
            const svg = dom.querySelector(`.page-wrapper[page-index='${bounds.pageIndex}']>svg` + name);
            if (!svg) {
                return;
            }
            let svgPageElement: any = svg.querySelector(cellCalssQueryName);
            this.bRenderCellBordered = true;
            if (!svgPageElement) {
                const borderDom = document.createElementNS('http://www.w3.org/2000/svg', 'g');
                borderDom.setAttribute('class', cellClassName);
                const cursorSvgPageElement: any = svg.querySelector('.singlecell-select');
                if (cursorSvgPageElement) {
                    cursorSvgPageElement.before(borderDom);
                } else {
                    svg.appendChild(borderDom);
                }
                svgPageElement = borderDom;
                const rect = this.createCellRect();
                this.setCellRectAttributes(rect, bounds, {
                    stroke: '#FF3143',
                    stroke_width: '1'
                });
                svgPageElement.appendChild(rect);
            } else {
                const rect = svgPageElement.firstChild;
                this.setCellRectAttributes(rect, bounds, {
                    stroke: '#FF3143',
                    stroke_width: '1'
                });
            }
        }
    }

    private showCellErrorMessageInfo(cell: any, errorType: number): void {
        const cellId = cell.getId();
        if (this.curErrorCellId === cellId) {
            return;
        } else {
            this.curErrorCellId = null;
            this.cellMessageTip.close();
        }
        if (cell.bValidData !== false) {
            return;
        }
        this.curErrorCellId = cellId;
        const bounds = cell.getSelectionBounds()[0];
        if (bounds) {
            const scale = this.host.getScale();
            const positionX = bounds.x * scale;
            const positionY = (bounds.y - this.getPaddingtTop()) * scale;
            const newPosition = this.cellMessageTip.getActivePosition(this.host.getCurPageIndex(),
                                                                      positionX,
                                                                      positionY,
                                                                      bounds.width,
                                                                      bounds.height);
            if (!newPosition) {
                return;
            }
            const content = '此项没有填写完全，请填写完整';
            this.cellMessageTip.open(content, newPosition.x, newPosition.y);
        }
        // const { cell, pageNum, oldBoundStr } = this.errorCells.get(cellId);
        // const [ width, height, x, y] = oldBoundStr.split(',');
        // const scale = this.host.getScale();
        // const positionX = parseFloat(x) * scale;
        // const positionY = (parseFloat(y) - this.getPaddingtTop()) * scale;
        // const newPosition = this.cellMessageTip.getActivePosition(pageNum,
        //                                                           positionX,
        //                                                           positionY,
        //                                                           parseFloat(width),
        //                                                           parseFloat(height));
        // if (!newPosition) {
        //     return;
        // }
        // const content = '此项没有填写完全，请填写完整';
        // this.cellMessageTip.open(content, newPosition.x, newPosition.y);
    }

    private getSvgName(type: IFixedCellType): string {
        let svgName: string;
        switch (type) {
            case IFixedCellType.Left: {
                svgName = 'left';
                break;
            }
            case IFixedCellType.Right: {
                svgName = 'right';
                break;
            }
            case IFixedCellType.Header: {
                svgName = 'header';
                break;
            }
            case IFixedCellType.LeftHeader: {
                svgName = 'leftHeader';
                break;
            }
            case IFixedCellType.RightHeader: {
                svgName = 'rightHeader';
                break;
            }
        }

        if (svgName) {
            svgName = `[name="${svgName}"]`;
        }

        return svgName;
    }

    private removeCellBorder(): void {
        if (this.bRenderCellBordered !== true) {
            return;
        }
        this.bRenderCellBordered = false;
        const dom: HTMLDivElement = this.getContainer();
        if (!dom) {
            return;
        }

        const borders = dom.querySelectorAll('.singlecell-select');
        Array.from(borders)
        .forEach((border) => {
            if (border && 0 < border.childElementCount ) {
                border.innerHTML = '';
            }
        });
    }

    private removeCellBorderByCell(cellClassName: string): void {
        const dom: HTMLDivElement = this.getContainer();
        if (!dom) {
            return;
        }
        const borders = dom.querySelectorAll(cellClassName);
        Array.from(borders)
        .forEach((border) => {
            if (border && 0 < border.childElementCount ) {
                border.remove();
            }
        });
    }

    private isSameSignCell(cells: any[]): boolean {
        if (!cells || !cells.length) {
            return false;
        }

        const oldCells = this.oldSignCells;
        if (!oldCells) {
            return false;
        }
        if (oldCells.length !== cells.length) {
            return false;
        }
        for (let index = 0, length = cells.length; index < length; index++) {
            if (cells[index].id !== oldCells[index].id) {
                return false;
            }
        }
        return true;
    }

    private renderSignCellBackground(): void {
        const dom: HTMLDivElement = this.getContainer();
        if (!dom) {
            return;
        }

        if ( !this.curCell || !this.documentCore.isExistSignCellInCurRow()) {
            this.removeSignCellBackground();
            return;
        }

        const signCells = this.documentCore.getSignCellsInCurRow();
        if (this.isSameSignCell(signCells)) {
            return;
        }

        this.removeSignCellBackground();
        this.oldSignCells = signCells;
        const bounds = signCells[0].getSelectionBounds()[0];
        if ( bounds ) {
            const svgPageElement = dom.querySelector(`.page-container[page-id='${bounds.pageIndex}']`);
            if ( !svgPageElement ) {
                // 当前页面没有渲染
                return;
            }

            const tablecellBackground = (svgPageElement.getElementsByClassName(`content`) ?
                                            svgPageElement.getElementsByClassName(`content`)[0] :
                                            svgPageElement.getElementsByClassName(`header-footer-enabled`) ?
                                             svgPageElement.getElementsByClassName(`header-footer-enabled`)[0] : null);
            let borderDom = (tablecellBackground ?
                            tablecellBackground.getElementsByClassName(`tablecellBackground`)[0] : null);
            if (!borderDom) {
                borderDom = document.createElementNS('http://www.w3.org/2000/svg', 'g');
                borderDom.setAttribute('class', 'tablecellBackground');
                const rect = this.createCellRect();
                this.setSignCellRectAttributes(rect, bounds);
                borderDom.appendChild(rect);
                svgPageElement.appendChild(borderDom);
            } else {
                // let rect = borderDom.firstChild as SVGElement;
                let rect = borderDom.getElementsByClassName('isExistSignCellInCurRow')[0] as SVGElement;
                if ( rect ) {
                    this.setSignCellRectAttributes(rect, bounds);
                } else {
                    rect = this.createCellRect();
                    this.setSignCellRectAttributes(rect, bounds);
                    borderDom.appendChild(rect);
                }
            }
        }
    }

    private removeSignCellBackground(): void {
        const dom: HTMLDivElement = this.getContainer();
        if (!dom) {
            return;
        }
        this.oldSignCells = undefined;
        const borders = dom.getElementsByClassName('tablecellBackground');
        // tslint:disable-next-line: prefer-for-of
        for (let index = 0; index < borders.length; index++) {
            const border = borders[index] as SVGElement;
            if ( border ) {
                const rects = border.getElementsByClassName('isExistSignCellInCurRow');
                Array.from(rects)
                .forEach((rect) => {
                    if (rect) {
                        rect.remove();
                    }
                });
            }
        }
    }

    private getContainer(): HTMLDivElement {
        return this.host.getContainer();
    }

    /**
     * 生成光标所在单元格的矩形框
     */
    private createCellRect(): any {
        const rect = document.createElementNS('http://www.w3.org/2000/svg', 'rect') as any;

        return rect;
    }

    private setCellRectAttributes(rect: any, bounds: any, stroke: any = {stroke: '#099FFF', stroke_width: '2'}): void {
        rect.setAttribute('fill', 'none');
        rect.setAttribute('stroke', stroke.stroke);
        rect.setAttribute('stroke-width', stroke.stroke_width);

        rect.setAttribute('x', bounds.x);
        rect.setAttribute('y', bounds.y);
        rect.setAttribute('width', bounds.width);
        rect.setAttribute('height', bounds.height);
    }

    private setSignCellRectAttributes(rect: any, bounds: any): void {
        rect.setAttribute('class', 'isExistSignCellInCurRow');
        rect.setAttribute('fill', NISTableCellColor.SignCellBackground);

        rect.setAttribute('x', bounds.x);
        rect.setAttribute('y', bounds.y);
        rect.setAttribute('width', bounds.width);
        rect.setAttribute('height', bounds.height);
    }

    private addEvents(): void {
        gEvent.addEvent(this.docId, gEventName.NISTableEvent, this.handleTableEvent);
        gEvent.addEvent(this.docId, gEventName.UnMounted, this.removeEvents);
        gEvent.addEvent(this.docId, gEventName.ContentChange, this.contentChange);
        gEvent.addEvent(this.docId, gEventName.TableCellActive, this.cursorChange);
        gEvent.addEvent(this.docId, gEventName.Mousedown, this.handleMouseDown);
        gEvent.addEvent(this.docId, gEventName.Mousemove, this.handleMouseMove);
        gEvent.addEvent(this.docId, gEventName.Mouseup, this.handleMouseUp);
        gEvent.addEvent(this.docId, gEventName.MoveCursor, this.cursorChange);
        gEvent.addEvent(this.docId, gEventName.Click, this.onClick);
        gEvent.addEvent(this.docId, gEventName.Dblclick, this.onDBclick);
        gEvent.addEvent(this.docId, gEventName.WindowClick, this.winClick);
        gEvent.addEvent(this.docId, gEventName.Blur, this.winClick);

        const div: HTMLDivElement = this.host.currentRef.current;
        if (div) {
            div.addEventListener('mouseleave', this.removeCellHover);
        }
    }

    private removeEvents = (): void => {
        gEvent.deleteEvent(this.docId, gEventName.NISTableEvent, this.handleTableEvent);
        gEvent.deleteEvent(this.docId, gEventName.ContentChange, this.contentChange);
        gEvent.deleteEvent(this.docId, gEventName.TableCellActive, this.cursorChange);
        gEvent.deleteEvent(this.docId, gEventName.Mousedown, this.handleMouseDown);
        gEvent.deleteEvent(this.docId, gEventName.Mousemove, this.handleMouseMove);
        gEvent.deleteEvent(this.docId, gEventName.Mouseup, this.handleMouseUp);
        gEvent.deleteEvent(this.docId, gEventName.MoveCursor, this.cursorChange);
        gEvent.deleteEvent(this.docId, gEventName.Click, this.onClick);
        gEvent.deleteEvent(this.docId, gEventName.Dblclick, this.onDBclick);
        gEvent.deleteEvent(this.docId, gEventName.WindowClick, this.winClick);
        gEvent.deleteEvent(this.docId, gEventName.Blur, this.winClick);
        this.clearDatas();
        const div: HTMLDivElement = this.host.currentRef.current;
        if (div) {
            div.removeEventListener('mouseleave', this.removeCellHover);
        }
    }

    // private handleClear = () => {
    //     this.errorCells.clear();
    // }

    private onClick = (e: any) => {
        const event = this.host.externalEvent;
        if (!event || !event.nisCellClickEvent) {
            return;
        }

        const cell = this.curCell;
        if (!cell) {
            return;
        }

        const rowID = cell.row.getNISRowID();
        const cellId = cell.getNISColID();
        const cellPosition = cell.getPosition()[this.host.getPageIndex() - cell.content.getAbsoluteStartPage()];
        if (!cellPosition) {
            return;
        }
        const resPosition = {
            x: cellPosition.x,
            y: cellPosition.y + cellPosition.height,
            width: cellPosition.width
        };
        event.nisCellClickEvent(rowID, cellId, cell.getCellType(), resPosition);
    }

    private onDBclick = (e: any) => {
        const event = this.host.externalEvent;
        if (!event || !event.nisCellDBClickEvent) {
            return;
        }

        const cell = this.curCell;
        if (!cell) {
            return;
        }

        const rowID = cell.row.getNISRowID();
        const cellId = cell.getNISColID();
        const cellPosition = cell.getPosition()[this.host.getPageIndex() - cell.content.getAbsoluteStartPage()];
        if (!cellPosition) {
            return;
        }
        const resPosition = {
            x: cellPosition.x,
            y: cellPosition.y + cellPosition.height,
            width: cellPosition.width
        };
        event.nisCellDBClickEvent(rowID, cellId, cell.getCellType(), resPosition);
    }

    private cursorChange = (): void => {
        // bug157801 && tab: keyCode 9
        if (!this.host.isMousdown() && 9 === this.host.getKeyBoardEvent()?.keyCode) {
            gEvent.setEvent(this.docId, gEventName.NewNISCellToShow, undefined);
        }
        this.getCurrentCell();
        // clearTimeout(this.oldTableTimeout);
        // this.oldTableTimeout = setTimeout(() => {
        //     this.setNumberBoxBorder(this.curCell);
        // }, 40);
    }

    private winClick = (e: any): void => {
        // console.log(e)
        const oldCell = this.oldCell;
        const newCell = this.curCell;
        if (oldCell === newCell || oldCell && newCell && oldCell.id === newCell.id) {
            return;
        }

        this.cellBlur();
    }

    private cellChange = (): void => {
        const oldCell = this.oldCell;
        const newCell = this.curCell;
        if (oldCell === newCell) {
            return;
        }
        this.cellBlur();
        this.cellFocus(newCell);
        this.oldCell = this.curCell;
    }

    private cellBlur = (): void => {
        const oldCell = this.oldCell;
        if (oldCell && oldCell.isTimeCell() && oldCell.validTimeCellContent() === 0) {
            this.host.refresh();
        }
        // 检查cell内容
        if (oldCell && (oldCell.isTimeCell() || oldCell.isBPCell())) {
            const validRes = this.isValidCellContent(oldCell);
            if (validRes !== oldCell.bValidData) {
                oldCell.bValidData = validRes;
                this.host.refresh();
            }
            // this.renderCellBorderByCell(oldCell, this.oldCellPageNum, isError);
        } else if (oldCell && oldCell.isNumberCell() && !oldCell.isValidOfNumberCellContent()) {
            oldCell.resetNumberCellContent(true);
            this.host.refresh();
        }
        const event = this.host.externalEvent;
        if (oldCell && event && typeof event.nisCellLostFocusEvent === 'function') {
            const rowID = oldCell.row.getNISRowID();
            const cellId = oldCell.getNISColID();
            const type = oldCell.getCellType();
            event.nisCellLostFocusEvent(rowID, cellId, type);
        }

        this.setNumberBoxBorder(oldCell);
    }

    private cellFocus = (cell: any): void => {
        this.oldCellPageNum = this.host.documentCore.getCurPageIndex();
        const event = this.host.externalEvent;
        if (cell && event && typeof event.nisCellGainFocusEvent === 'function') {
            const rowID = cell.row.getNISRowID();
            const cellId = cell.getNISColID();
            const type = cell.getCellType();
            event.nisCellGainFocusEvent(rowID, cellId, type);
        }

        // if (cell) {
        //     this.removeNumCellBorder(cell.id);
        // }

        // this.setNumberBoxBorder();
    }

    private removeNumCellBorder(id: any): void {
        if (!this.oldNumBounds[id]) {
            return;
        }
        this.oldNumBounds[id] = false;
        const dom: HTMLDivElement = this.getContainer();
        if (!dom) {
            return;
        }
        const numDom: any = dom.querySelector(`svg [data-id="${id}"].numberCell-valid-rect`);
        if (numDom) {
            numDom.style.display = 'none';
        }
    }

    private showNumCellBorder(id: any): boolean {
        const dom: HTMLDivElement = this.getContainer();
        if (!dom) {
            return;
        }
        const numDom: any = dom.querySelector(`svg [data-id="${id}"].numberCell-valid-rect`);
        if (numDom) {
            numDom.style.display = 'block';
            this.oldNumBounds[id] = true;
            return true;
        }
    }

    private isValidWarnBorder(id: any): boolean {
        const dom: HTMLDivElement = this.getContainer();
        if (!dom) {
            return;
        }
        const numDom: any = dom.querySelector(`svg [data-id="${id}"].numberCell-valid-rect`);
        if (numDom) {
            return true;
        }
    }

    private setNumberBoxBorder(oldCell: any): void {
        const curCell = oldCell || this.curCell;
        if (!curCell || !curCell.isNumberCell()) {
            return;
        }

        const res = curCell.getNumberboxWarn();
        if (res !== ResultType.Failure) {
            this.removeNumCellBorder(curCell.id);
            return;
        }
        const oldNumBound = this.oldNumBounds[curCell.id];
        if (oldNumBound && this.isValidWarnBorder(curCell.id)) {
            return;
        }
        if (oldNumBound === false) {
            const restult = this.showNumCellBorder(curCell.id);
            if (restult) {
                return;
            }
        }
        this.host.refresh();
        this.oldNumBounds = {};
        // const bounds = curCell.getSelectionBounds2(this.host.getPageIndex())[0];
        this.oldNumBounds[curCell.id] = true;
        // this.oldCell = this.curCell;
        // this.removeCellBorder();
        // if ( bounds ) {
        //     const id = curCell.id;
        //     this.oldNumBounds[id] = bounds;
        //     this.refreshPageCellBorder(false);
        //     // const dom: HTMLDivElement = this.getContainer();
        //     // if (!dom) {
        //     //     return;
        //     // }
        //     // // const fixedType = curCell.getFixedProps();
        //     // // const name = this.getSvgName(fixedType) || '';
        //     // const svg = dom.querySelector(`.page-wrapper[page-index='${bounds.pageIndex}']>svg`);
        //     // if (!svg) {
        //     //     return;
        //     // }
        //     // let svgPageElement: any = svg.querySelector('.numberCell-valid');
        //     // let rect;
        //     // if (!svgPageElement) {
        //     //     const borderDom = document.createElementNS('http://www.w3.org/2000/svg', 'g');
        //     //     borderDom.setAttribute('class', 'numberCell-valid');
        //     //     svg.appendChild(borderDom);
        //     //     svgPageElement = borderDom;
        //     // } else {
        //     //     rect = svgPageElement.querySelector(`[data-id="${id}"].numberCell-valid-rect`);
        //     // }

        //     // if (!rect) {
        //     //     rect = this.createCellRect();
        //     // }
        //     // this.createNumCellRect(rect, bounds, id);
        //     // svgPageElement.appendChild(rect);
        // } else {
        //     this.removeNumCellBorder(curCell.id);
        // }
    }

    private createNumCellRect(rect: any, bounds: any, id: any): any {
        rect.setAttribute('class', 'numberCell-valid-rect');
        rect.setAttribute('data-id', id);
        rect.setAttribute('x', bounds.x);
        rect.setAttribute('width', bounds.width);
        rect.setAttribute('height', bounds.height);
        rect.setAttribute('y', bounds.y);
    }

    private handleTableEvent = (type: MessageType, ...arr: []) => {
        switch (type) {
        //   case MessageType.DeleteTableCells: {
        //  //    this.setState({tablePopMenuModalType: TableMenuModalType.DeleteTableCells});
        //      gEvent.setEvent(this.docId, gEventName.DialogEvent, MenuItemIndex.DeleteRow);
        //      break;
        //   }

        //   case MessageType.SplitTableCells: {
        //      // gEvent.setEvent(this.docId, gEventName.DialogEvent, MenuItemIndex.SplitCell);
        //      message.error(arr.toString());
        //      break;
        //   }

          case MessageType.AddTableRow: {
             // gEvent.setEvent(this.docId, gEventName.DialogEvent, MenuItemIndex.SplitCell);
             message.error(arr.toString());
             break;
          }

          case MessageType.AddTableColumn: {
             // gEvent.setEvent(this.docId, gEventName.DialogEvent, MenuItemIndex.SplitCell);
             message.error(arr.toString());
             break;
          }

          case MessageType.PasteTable: {
             // gEvent.setEvent(this.docId, gEventName.DialogEvent, MenuItemIndex.SplitCell);
             message.error(arr.toString());
             break;
          }

          default:
            break;
        }
    }

    private getPaddingtTop(): number {
        if (this.getViewMode() === ViewModeType.WebView) {
            const margin = this.documentCore.getPagePositionInfo(0);
            return margin.y;
        }

        return 0;
    }

    private getViewMode(): ViewModeType {
        return this.documentCore.getViewMode();
    }
}
