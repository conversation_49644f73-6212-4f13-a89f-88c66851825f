import { XmlComponent } from '../../xml-components';
import { BaseXmlComponent, IXmlResult } from '../../xml-components/base';
import { SectionProperties, SectionPropertiesOptions } from './section-properties';

export class Body extends XmlComponent {

    public get Root(): (BaseXmlComponent | string)[] {
        return this.root;
    }

    // private readonly defaultSection: SectionProperties;

    private readonly sections: SectionProperties[] = [];

    constructor(sectionPropertiesOptions?: SectionPropertiesOptions) {
        super('w:body');

        // this.defaultSection = new SectionProperties(sectionPropertiesOptions);
        // this.sections.push(this.defaultSection);
    }

    /* compose children xml elements: polymorphism */
    public prepForXml(): IXmlResult {
        if (this.sections.length === 1) {
            this.root.push(this.sections[0]);
        }

        return super.prepForXml();
    }

    public push(component: XmlComponent): void {
        this.root.push(component);
    }
}
