
import {VNode, vNodeBlock} from './ParseNode';
import { decodeTag } from './DataConver';

export default class HtmlParse {
    public images: VNode[];
    private html: string;
    // tslint:disable-next-line: max-line-length
    private regHtml: RegExp = /<(?:(?:\/([^>]+)>)|(?:!--([\S|\s]*?)-->)|(?:([^\s\/<>]+)\s*((?:(?:"[^"]*")|(?:'[^']*')|[^"'<>])*)\/?>))/g;
    private regAttr: RegExp = /([\w\-:.]+)(?:(?:\s*=\s*(?:(?:"([^"]*)")|(?:'([^']*)')|([^\s>]+)))|(?=\s|$))/g;
    private alinkDefatulAttrs: any;

    constructor() {
        this.images = [];
    }

    public parse(html: string, reg?: RegExp, alinkDefatulAttrs?: any): VNode {
        if (!html || !html.trim()) {
            return;
        }
        // 过滤掉新进进去的标识符
        if (reg) {
            html = html.replace(reg, '');
        }
        if (alinkDefatulAttrs) {
            this.alinkDefatulAttrs = alinkDefatulAttrs;
        }

        this.html = html.trim();
        this.filterHtml();
        return this.createNode();
    }

    private filterHtml(ignoreBlank: boolean = false): void {
        const reg = /<!--StartFragment-->((.|\s)*?)<!--EndFragment-->/i;
        const match = reg.exec(this.html);
        if (match) {
            this.html = `<body>${match[1]}</body>`;
        }

        // this.html = this.html.replace(/[ ][^ ]+/g, '&nbsp;');
        this.html = this.html.replace(new RegExp('[\\r\\t\\n' + (ignoreBlank ? '' : ' ')
            + ']*<\/?(\\w+)\\s*(?:[^>]*)>[\\r\\t\\n'
            + (ignoreBlank ? '' : ' ') + ']*', 'g'), (a, b) => {
            // br暂时单独处理
            if (b && b.toLowerCase() === 'br') {
                return a.replace(/(^[\n\r]+)|([\n\r]+$)/g, '');
            }
            return a.replace(new RegExp('^[\\r\\n' + (ignoreBlank ? '' : ' ') + ']+'), (c) => {
                    if (b && /^[ ]+&/.test(c)) {
                        return c;
                    }
                    return '';
                })
            .replace(new RegExp('[\\r\\n' + (ignoreBlank ? '' : ' ') + ']+$'), (c) => {
                    if (b && /^[ ]+$/.test(c)) {
                        return c;
                    }
                    return '';
                });
        })
        .replace(/\u21b5/g, '');
    }

    private isEmpty(name: string): boolean {
        const obj = {area: true, base: true, basefont: true, br: true, col: true, command: true,
                     dialog: true, embed: true, hr: true, img: true, input: true, isindex: true,
                     keygen: true, link: true, meta: true, param: true, source: true, track: true, wbr: true};
        return obj[name];
    }

    private isNotTextChild(name: string): boolean {
        const obj = {ul: true, ol: true, dl: true, table: true, tr: true};
        return obj[name];
    }

    private createText(parent: VNode, data: string): void {
        // || !data.trim()
        if (!data || this.isNotTextChild(parent.tagName)) {
            return;
        }

        const node = new VNode();
        node.type = 'text';
        // <>'" html反转义
        node.content = decodeTag(data)
            .replace(/&#160;/g, ' ');
        node.parentNode = parent;
        node.tagName = 'text';
        parent.children.push(node);
    }

    private crateElement(parent: VNode, tagName: string, htmlattr: string): VNode {
        const elm = new VNode();
        elm.parentNode = parent;
        elm.type = 'element';
        elm.tagName = tagName.toLowerCase();
        elm.isBlock = vNodeBlock[elm.tagName];
        elm.children = this.isEmpty(tagName) ? null : [];

        // 如果属性存在，处理属性
        if (htmlattr) {
            const regAttr = this.regAttr;
            const attr = {};
            let match = regAttr.exec(htmlattr);
            while (match) {
                attr[match[1].toLowerCase()] = match[2] || match[3] || match[4];
                match = regAttr.exec(htmlattr);
            }
            elm.attrs = attr;
        }

        if (elm.tagName === 'img') {
            this.images.push(elm);
        }

        if (elm.tagName === 'a' && this.alinkDefatulAttrs) {
            const defaultAttrs = this.alinkDefatulAttrs;
            if (!elm.attrs) {
                elm.attrs = {};
            }
            const attrs = elm.attrs as any;
            const style = attrs.style;
            if (!style) {
                attrs.style = `color: ${defaultAttrs.color}; textDecoration: ${defaultAttrs.textDecorationLine};`;
            } else {
                let actStyle = ';';
                if (style.search(/(?:[;\s]*)color:/) === -1) {
                    actStyle += `color: ${defaultAttrs.color};`;
                }

                if (style.search(/textDecoration|text-decoration/) === -1) {
                    actStyle += `textDecoration: ${defaultAttrs.textDecorationLine};`;
                }
                attrs.style += actStyle;
            }
        }

        parent.children.push(elm);
        // 如果是自闭合节点返回父亲节点
        return  this.isEmpty(tagName) ? parent : elm;
    }

    private createNode(): VNode {
        const parent = new VNode();
        parent.type = 'root';
        parent.tagName = 'root';
        parent.children = [];
        const reg = this.regHtml;
        const html = this.html;
        let nextIndex: number = 0;
        let currentIndex: number = 0;
        let currentParent = parent;
        if (this.alinkDefatulAttrs) {
            parent.attrs = {
                defaultColor: this.alinkDefatulAttrs.color,
                defaultTextDecorationLine: this.alinkDefatulAttrs.textDecorationLine,
            };
        }

        const cdata = {script: 1, style: 1};
        let match = reg.exec(html);
        while (match) { // match 1/2/3/  tagName  4: attr
            currentIndex = match.index;
            try {
                let tagName;
                if (currentIndex > nextIndex) {
                    // text node
                    this.createText(currentParent, html.slice(nextIndex, currentIndex));
                }

                // tslint:disable-next-line: no-conditional-assignment
                if (tagName = match[3]) {
                    tagName = tagName.toLowerCase();
                    if (cdata[currentParent.tagName]) {
                        this.createText(currentParent, match[0]);
                    } else if (tagName !== 'html' && tagName !== 'body' && tagName !== 'head') {
                        // start tag
                        currentParent = this.crateElement(currentParent, tagName, match[4]);
                    }
                } else if (match[1]) {
                    if (currentParent.type !== 'root') {
                        if (cdata[currentParent.tagName] && !cdata[match[1]]) {
                            this.createText(currentParent, match[0]);
                        } else {
                            const tmpParent = currentParent;
                            while (currentParent.type === 'element' &&
                                currentParent.tagName !== match[1].toLowerCase()) {
                                currentParent = currentParent.parentNode;
                                if (currentParent.type === 'root') {
                                    currentParent = tmpParent;
                                    throw new Error('break');
                                }
                            }
                            // end tag
                            currentParent = currentParent.parentNode;
                        }
                    }
                }
            } catch (e) {
                //
            }
            nextIndex = reg.lastIndex;
            match = reg.exec(html);
        }

        return parent;
    }
}
