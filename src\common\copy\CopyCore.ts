import Paste from './Paste';
import Copy from './Copy';
import {PasteType, CopyType, ContentType, embedData} from './DataType';
import {PasteContent, CopyContent} from './Content';
import Paragraph from '../../model/core/Paragraph';
import { PastePermission } from './Permission';
import { message } from '../Message';
import { getDocumentCoreRecorder } from '../../model/DocumentCoreRecordReplay';
// import { decodeTag } from './DataConver';

let pageFlagStr: string;
let pageStaticId: string;
export default class CopyCore {
    private pasteContents: PasteContent[];
    private copyContents: CopyContent[];
    private pasteContentLength: number;
    private pasteType: PasteType; // 通过复制面板进行粘贴
    private copyType: CopyType; // 复制方式
    private copy: Copy;
    private paste: Paste;
    private id: string;
    private access: boolean;
    private accessId: string;
    private byteCode: number;
    private md5Id: string;
    private permission: PastePermission;
    private doc: any;
    private bSameWebPage: boolean;
    private staticWebId: string;
    private randomStr: string;
    private bInnerFormat: boolean;
    private bOuterFormat: boolean;
    private internalTimestamp: number;

    constructor() {
        this.copy = new Copy();
        this.paste = new Paste();
        this.pasteContents = [];
        this.copyContents = [];
        // this.id = id;
        // this.access = access;
        this.pasteContentLength = 0;
        this.id = '';
        this.md5Id = '';
        this.byteCode = 36;
        // this.setIdToString(id);
        this.permission = new PastePermission();
        if (!pageFlagStr) {
            pageFlagStr = Math.ceil(Math.random() * 1000) + '561';
            pageFlagStr = pageFlagStr.slice(0, 3) + 'pg';
            this.setStrByCode(pageFlagStr);
        }
        this.permission.setPageStr(pageFlagStr);
        this.setRandomString();
        setTimeout(() => {
            if (window['editor']) {
                window['editor']._testPasteStr = this.testPasteStr;
            }
        }, 3000);
    }

    public clearContent(): void {
        this.accessId = null;
        this.pasteContentLength = 0;
        this.pasteContents = [];
    }

    public setWebPageFlag(flag: boolean = true): void {
        if (flag === this.bSameWebPage) {
            return;
        }
        this.bSameWebPage = flag;
        if (flag === false) {
            if (this.md5Id && !this.id) {
                this.md5Id = '';
            }
        }
    }

    public setSameWebPage(bSameWebPage?: boolean): void {
        const flag = bSameWebPage === true ? true : this.permission.isSamePage(this.accessId);
        this.setWebPageFlag(flag);
    }

    public setFormatFlag(bInnerFormat: boolean, bOuterFormat: boolean): void {
        this.bInnerFormat = bInnerFormat;
        this.bOuterFormat = bOuterFormat;
    }

    public getStaticId(): string {
        return this.randomStr;
    }

    public setStaticId(value: string): void {
        this.randomStr = value;
    }

    /**
     * 是否允许系统外的文件进行复制粘贴
     * @param access 控制的状态
     */
    public setAccess(access: boolean): void {
        this.access = access;
        this.permission.setAccess(access);
        if (access === false) {
            this.permission.setSourceId(this.md5Id);
        } else {
            this.permission.setSourceId('');
        }
    }

    /**
     * 允许同ID间进行复制粘贴
     * @param id 外部设置的id
     */
    public setAccessId(id: string): void {
        this.id = this.setStrByCode(id);
        // this.setIdToString(id);

        this.setRandomString();
        if (id) {
            this.permission.setSourceId(this.md5Id);
        }
    }

    public setDocument(doc: any): void {
        this.doc = doc;
        this.copy.setDocument(doc);
        this.paste.setDocument(doc);
    }

    public addContent(str: string, type: string, image?: File): void {
        let text: string;
        if (type === ContentType.TextPlain) {
            this.getAccessId(str) ? (text = str.replace(embedData.AccessReg, '')) : (text = str);
        } else {
            text = str;
        }

        const content = new PasteContent(text, type, image);
        this.pasteContents.push(content);
        this.pasteContentLength++;
        if (content.type === ContentType.TextHtml) {
            const reg = embedData.Reg;
            const match = reg.exec(text);
            if (match && match[1]) {
                const content1 = new PasteContent(match[1], ContentType.TextApollo, image);
                this.pasteContents.push(content1);
                this.accessId = match[2];
            }
        }
    }

    public getContentLen(): number {
        return this.pasteContentLength;
    }

    public setCopyType(copyType: CopyType): void {
        this.copyType = copyType;
    }

    public setPasteType(pasteType: PasteType): void {
        this.pasteType = pasteType;
    }

    public execCopy(selection: any): string {
        pageStaticId = this.staticWebId;
        const html = this.copy.copy(selection);
        this.copyContents = this.copy.getCopyContent();
        this.paste.initCopyContent(this.copyContents);
        return html;
    }

    public getPermission(str?: string, isExternalContent?: boolean): boolean {
        if (str) {
            this.getAccessId(str);
        } else if (this.bSameWebPage === true && this.pasteType !== PasteType.PasteCtrlV) {
            this.accessId = pageStaticId;
        }

        return this.permission.getPermission(this.accessId, isExternalContent);
    }

    /**
     * 判断当前粘贴内容是否为外部内容
     * 外部内容特征：没有Apollo格式且没有内部权限标识
     */
    private isExternalContent(): boolean {
        if (!this.pasteContents || this.pasteContents.length === 0) {
            return true; // 没有内容，视为外部
        }

        // 检查是否包含Apollo格式内容
        const hasApolloContent = this.pasteContents.some(content =>
            content.type === ContentType.TextApollo
        );

        if (hasApolloContent) {
            return false; // 包含Apollo格式，是内部内容
        }

        // 检查是否包含内部权限标识
        const hasInternalId = this.pasteContents.some(content => {
            if (content.type === ContentType.TextPlain && content.content) {
                return embedData.AccessReg.test(content.content);
            }
            return false;
        });

        return !hasInternalId; // 没有内部权限标识，是外部内容
    }

    public getText(): string {
        if (pageStaticId) {
            this.pasteType = PasteType.PasteClickCtrlShiftV;
            return pageStaticId;
        }

        return null;
    }

    public addTestContent(): void {
        const recorder = getDocumentCoreRecorder();
        const contents = this.pasteContents;
        let obj: PasteContent = contents.find((item) => item.type === ContentType.TextApollo);
        if (!obj) {
            obj = contents.find((item) => item.type === ContentType.TextHtml);
        }
        if (!obj || (!obj.content && !obj.image && obj.content !== contents[0].content) ) {
            obj = contents[0];
        }
        recorder.record({external: true, method: 'paste', args: [{content: obj.content, type: obj.type}]});
    }

    public execPaste(): Promise<Paragraph []> {
        if (!this.accessId && this.pasteType === PasteType.PasteCtrlV) {
            this.clearCopyContent();
        }

        if (this.permission.isOwnerContent(this.accessId, this.staticWebId)) {
            // this.addTestContent();
            return Promise.resolve(this.getParagraph());
        }

        // 判断是否为外部内容，外部内容不进行author验证
        const isExternal = this.isExternalContent();

        if (this.getPermission(undefined, isExternal)) {
            if (this.bSameWebPage === true && this.pasteType !== PasteType.PasteCtrlV) {
                return Promise.resolve(this.getParagraph());
            }
            this.addTestContent();
            this.clearCopyContent();
            this.paste.setFormatFlag(this.bInnerFormat, this.bOuterFormat);
            return this.paste.parse(this.pasteContents, this.pasteType);
        }
        if (this.id) {
            // message.error('禁止不同病人间拷贝病历!');
            // alert('禁止外部拷贝内容，拷贝无效!');
            message.error('禁止不同病人间拷贝病历!');
        } else {
            message.error('禁止外部拷贝内容，拷贝无效!');
        }

        // message.error('禁止外部拷贝内容，拷贝无效!');

        return Promise.resolve(null);
    }

    public getParagraph(): any {
        this.pasteContents = [];
        const apolloStr = this.copy.getApolloContents();
        // 只有当bInnerFormat为true时才添加Apollo数据
        if (apolloStr && this.bInnerFormat !== false) {
            this.addContent(apolloStr, ContentType.TextApollo);
            this.addTestContent();
            this.pasteContents = [];
        }
        return this.copy.getSelection(this.bInnerFormat);
    }

    public getUnFormatContent(str?: string): string {
        return this.paste.getUnFormatContent(str);
    }

    public isOwnerContent(str: string): boolean {
        let accessId: string;
        if (this.bSameWebPage === true) {
            accessId = str;
        } else {
            accessId = this.getAccessId(str);
            if (!accessId) {
                return false;
            }
        }

        return this.permission.isOwnerContent(accessId, this.staticWebId);
    }

    private clearCopyContent(): void {
        if (this.bSameWebPage !== true) {
            pageStaticId = null;
            this.copy.clearContent();
        }
    }

    private getAccessId(str: string): string {
        const matchs = embedData.AccessReg.exec(str);
        if (matchs) {
            // const accessId = matchs[1];
            // this.permission.setPasteId(accessId);
            return this.accessId = matchs[1];
        }
    }

    // private setIdToString(str: string): void {
    //     if (!str) {
    //         this.md5Id = '';
    //         this.permission.setSourceId(this.md5Id);
    //         return;
    //     }
    //     let result = '';
    //     const byte = this.byteCode;
    //     for (let index = 0, len = str.length; index < len; index++) {
    //         result += str.codePointAt(index)
    //             .toString(byte);
    //     }
    //     result += pageFlagStr;
    //     this.md5Id = result;
    //     // this.setRandomString();
    //     this.permission.setSourceId(result);
    // }

    private setStrByCode(str: string): string {
        if (!str) {
            return '';
        }
        let result = '';
        const byte = this.byteCode;
        for (let index = 0, len = str.length; index < len; index++) {
            result += str.codePointAt(index)
                .toString(byte);
        }

        return result;
    }

    private testPasteStr = (str: string): void => {
        this.paste.parse([{content: str, type: ContentType.TextApollo} as any], PasteType.PasteCtrlV)
        .then((result) => {
            if (this.doc) {
                this.doc.addPasteContent(result);
            }
        });
    }

    private setRandomString(): void {
        const id = this.id;
        let randomStr = this.randomStr;
        if (!randomStr) {
            this.randomStr = randomStr = this.setStrByCode(Math.ceil(Math.random() * 10000) + '');
        }
        const md5Str = pageFlagStr + id + '__' + randomStr;
        this.staticWebId = this.md5Id = md5Str;
        // this.permission.
        this.copy.setId(md5Str);
        // this.permission.setSourceId2(md5Str);
        this.permission.setSourceId('');
        this.permission.setAccessId(id);
    }

    public setInternalTimestamp(timestamp: number): void {
        this.internalTimestamp = timestamp;
    }

    public getInternalTimestamp(): number {
        return this.internalTimestamp || 0;
    }

    /**
     * 获取当前编辑器的author信息（用于跨编辑器复制权限验证）
     */
    public getAuthor(): string {
        return this.id || '';
    }

    public getApolloContents(): string {
        return this.copy.getApolloContents();
    }
}
