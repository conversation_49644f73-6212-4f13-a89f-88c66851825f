import { ParagraphContentPos } from "@/model/core/Paragraph/ParagraphContent";
import { DocumentCore } from "@/model/DocumentCore";
import { WavyUnderlineTooltipManager } from "./WavyUnderlineTooltipManager";
import { getPageElement } from "../../../../common/commonDefines";
import { getPagePadding } from "../../../../common/commonMethods";
import MouseEventHandler from "../../../../common/MouseEventHandler";
import ParaPortion from "../../../../model/core/Paragraph/ParaPortion";
import Paragraph from "../../../../model/core/Paragraph";
import { WavyUnderlineManager } from "../../../../model/core/WavyUnderline/WavyUnderlineManager";


export class WavyUnderlineHandler {
    private documentCore: DocumentCore;
    private tooltipManager: WavyUnderlineTooltipManager;
    private debounceTimer: number | null = null;
    private lastPosition: ParagraphContentPos | null = null;
    private gMouseEvent: MouseEventHandler;
    private wavyUnderlineManager: WavyUnderlineManager;
    
    constructor(documentCore: DocumentCore) {
        this.documentCore = documentCore;
        this.tooltipManager = new WavyUnderlineTooltipManager();
        this.gMouseEvent = new MouseEventHandler();
        this.wavyUnderlineManager = documentCore.getWavyUnderlineManager();
        // 添加全局点击监听，点击时隐藏弹框
        this._handleBodyClick = this._handleBodyClick.bind(this);
        document.body.addEventListener('click', this._handleBodyClick, true);
    }
    
    private _handleBodyClick(e: MouseEvent) {
        this.tooltipManager.hideImmediate();
    }
    
    /**
     * 处理鼠标移动事件
     */
    public handleMouseMove(e: any): void {
        const pageNode = getPageElement(e.target as HTMLElement);
        if (!pageNode) {
            return;
        }

        // 正在选择文本时隐藏提示
        if (this.documentCore.isSelecting()) {
            this.handleMouseLeave();
            return;
        }

        // 2000ms延迟显示（2秒）
        if (this.debounceTimer) {
            clearTimeout(this.debounceTimer);
        }

        this.debounceTimer = window.setTimeout(() => {
            this.processMouseMove(e);
        }, 2000);
    }
    
    /**
     * 处理鼠标离开事件
     */
    public handleMouseLeave(): void {
        const wavyUnderlineManager = this.documentCore.getWavyUnderlineManager();
        wavyUnderlineManager.deactiveAll();
        this.tooltipManager.hide();
        this.lastPosition = null;
    }
    
    /**
     * 销毁处理器
     */
    public destroy(): void {
        if (this.debounceTimer) {
            clearTimeout(this.debounceTimer);
        }
        this.tooltipManager.destroy();
        // 移除全局点击监听
        document.body.removeEventListener('click', this._handleBodyClick, true);
    }
    
    /**
     * 实际处理鼠标移动逻辑
     */
    private processMouseMove(e: any): void {
        const mousePosition = this.getPositionFromMouseEvent(e);
        if (!mousePosition) {
            this.tooltipManager.hide();
            return;
        }
             
        // 获取当前鼠标位置对应的ParaPortion
        const portion = this.getCurrentPortionByPosition(mousePosition);
        
        if (portion && (portion as any).wavyUnderlineId) {
            // 从管理器中获取波浪线对象
            const wavyUnderline = this.wavyUnderlineManager.getWavyUnderlineById((portion as any).wavyUnderlineId);
            
            if (wavyUnderline) {
                
                // 显示提示框
                this.tooltipManager.show({
                    x: e.clientX,
                    y: e.clientY,
                    content: wavyUnderline.getContent(),
                    type: wavyUnderline.getType(),
                    wavyUnderline: wavyUnderline
                });
                return;
            }
        }
        
        this.tooltipManager.hide();
    }
    // 根据位置获取ParaPortion
    private getCurrentPortionByPosition(mousePosition: ParagraphContentPos): ParaPortion | null {
        try {
            const portionIndex = mousePosition.get(0);
            
            if (portionIndex === undefined) {
                return null;
            }
            
            const currentParagraph = this.documentCore.getCurrentParagraph() as Paragraph;
            if (!currentParagraph || !currentParagraph.content) {
                return null;
            }
            
            const portion = currentParagraph.content[portionIndex];
           
            return portion || null;
        } catch (error) {
            return null;
        }
    }
    
    /**
     * 根据鼠标事件获取位置
     * @param e 鼠标事件
     * @returns 位置
     */
    private getPositionFromMouseEvent(e: any): ParagraphContentPos | null {
        try {
            // 获取页面相关信息
            const pageNode = getPageElement(e.target as HTMLElement);
            if (!pageNode) return null;

            const pageId = parseInt(pageNode.getAttribute('page-index') || '0');
            const scale = 1; // 可以从host获取缩放比例
            
            // 计算鼠标位置
            const position = getPagePadding(this.documentCore);
            let offsetX = (e.layerX || e.offsetX) / scale + position.left;
            const offsetY = (e.layerY || e.offsetY) / scale + position.top;

            // 获取光标状态
            const cursorState = this.documentCore.getCursorStateInDocument(offsetX, offsetY, pageId);
            offsetX = cursorState ? cursorState.pointX : offsetX;
            
            this.gMouseEvent.pointX = offsetX;
            this.gMouseEvent.pointY = offsetY;

            // 检查鼠标位置是否有效
            if (cursorState && cursorState.result) {
                // 获取段落位置
                const paragraphPos = this.documentCore.getParaContentPosByXY(pageId, offsetX, offsetY);
                if (paragraphPos && paragraphPos.pos)  {
                    return paragraphPos.pos;
                }
            }
            return null;
        } catch (error) {
            return null;
        }
    }
}