@import './global.less';
.hz-editor-container  {
    .editor-newcontrol-tips {
        display: none;
        position: absolute;
        z-index: 9;
        max-width: 780px;
        min-width: 50px;
        padding: 4px;

        color: #000;
        border-radius: 2px;
        background-color: #F0F2F5;
        border: 1px solid @color3;

        &.active {
            display: block;
        }

        &.visible {
            display: block;
            white-space:nowrap;
            opacity: 0;
        }

        & > div {
            line-height: 22px;
        }

        & > i {
            display: inline-block;
            position: absolute;
            bottom: -10px;
            &:after {
                display: inline-block;
                width: 0;
                height: 0;
                margin-top: 9px;
                vertical-align: middle;
                border-left: 5px solid  transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid @currenBgColor;
                border-bottom: 5px solid transparent;
                content: ' ';
            }
        }
    }

    .new-control-list {
        .editor-checkbox {
            line-height: 1.38;
            .checkbox-item > input[type='checkbox'] {
                height: 100%;
            }
        }
    }
}