import * as React from 'react';
import Dialog from '../../ui/Dialog';
import Button from '../../ui/Button';
import Checkbox from '../../ui/CheckboxItem';
import { CellTimeFormat, CustomPropertyElementType } from '../../../../common/commonDefines';
import CustomPropertyBtn from './CustomProperty';
import { IFRAME_MANAGER } from '../../../../common/IframeManager';
import Input from '../../ui/Input';
import { message } from '../../../../common/Message';
import { NISTableCellCommon } from './NISTableCellCommonProps';

interface IDialogProps {
    documentCore: any;
    visible: boolean;
    id?: string;
    close: (name: string | number, bRefresh?: boolean) => void;
    refresh: (bRefresh: boolean, timeout?: number) => void;
}

interface IState {
    bRefresh: boolean;
}

export default class NISTableCellTime extends React.Component<IDialogProps, IState> {
    private cell: any;
    // private tableProps: any;
    private visible: any;
    private bCustomProperty: boolean;
    private formats: string[];
    private bAutoFormat: boolean;
    nisProps: any;

    constructor(props: any) {
        super(props);
        this.cell = {};
        this.state = {
            bRefresh: false,
        };

        this.visible = this.props.visible;
        this.nisProps = {};
        // this.formats = [CellTimeFormat.HM, CellTimeFormat.HMS, CellTimeFormat.HM2, CellTimeFormat.HMS2];
        this.formats = [CellTimeFormat.HM, CellTimeFormat.HM2];
        // this.setDialogValue();
    }

    public componentDidMount(): void {
        // this.boxRef.current.ownerDocument.addEventListener('click', this.docClick);
    }

    public componentWillUnmount(): void {
        // this.boxRef.current.ownerDocument.removeEventListener('click', this.docClick);
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        this.visible = nextProps.visible;
    }

    public render(): any {

        return (
            <Dialog
                id={this.props.id}
                visible={this.visible}
                width={380}
                open={this.open}
                preventDefault={false}
                title='时间单元格'
                confirm={this.confirm}
                footer={this.renderFooter()}
            >
                <div>
                    <div className='editor-line'>
                        <div className='w-70'>内部名称</div>
                        <div className='right-auto'>
                            <Input
                                name='serialNumber'
                                value={this.cell.serialNumber}
                                onChange={this.onChange}
                                readonly={true}
                                placeholder={''}
                                disabled={true}
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <span className='title'>属性：</span>
                    </div>
                    <div className='editor-line'>
                        <Checkbox
                            name='bProtected'
                            value={this.cell.bProtected}
                            disabled={false}
                            onChange={this.onChange}
                        >
                            禁止编辑
                        </Checkbox>
                        <Checkbox
                            name='visible'
                            value={this.cell.visible}
                            disabled={false}
                            onChange={this.onChange}
                        >
                            网格线
                        </Checkbox>
                    </div>
                    <NISTableCellCommon prop={this.nisProps}/>
                    <div className='editor-line editor-multi-line'>
                        <label className='w-70'>时间格式</label>
                        <div className='right-auto'>
                            {this.renderFormat()}
                        </div>
                    </div>
                    <div className='editor-line'>
                        <Checkbox
                            name='bAutoFormat'
                            value={this.bAutoFormat}
                            disabled={false}
                            onChange={this.checkChange}
                        >
                            自定义时间格式
                        </Checkbox>
                    </div>
                    <div className='editor-line'>
                        <span style={{width: '100px', display: 'inline-block'}}>
                            <Input name='hour' disabled={true} value={this.cell.hour} />
                        </span>
                        <span style={{width: '50px', display: 'inline-block'}}>
                            <Input
                                name='split'
                                disabled={!this.bAutoFormat}
                                onChange={this.onChange}
                                value={this.cell.split}
                            />
                        </span>
                        <span style={{width: '100px', display: 'inline-block'}}>
                            <Input name='minute' disabled={true} value={this.cell.minute} />
                        </span>
                    </div>

                    <div className='editor-line'>
                        <CustomPropertyBtn
                            name='customProperty'
                            properties={this.cell.customProperty}
                            documentCore={this.props.documentCore}
                            onChange={this.onChange}
                            close={this.onClose}
                            id='bCustomProperty'
                            visible={this.bCustomProperty}
                            type={CustomPropertyElementType.TableCell}
                        />
                    </div>
                </div>
            </Dialog>
        );
    }

    private renderFooter(): any {
        return (
            <span>
                <Button onClick={this.close}>取消</Button>
                <Button type='primary' onClick={this.confirm}>确认</Button>
            </span>
        );
    }

    // private renderFormat(): any {
    //     const formats = this.formats;
    //     const format = this.cell.format;
    //     return formats.map((item, index) => {
    //         let className;
    //         if (format === item) {
    //             className = 'active';
    //         }
    //         return (<span key={index} className={className}>{item}</span>);
    //     });
    // }

    private renderFormat(): any {
        const activeValue = this.cell.format;
        const list = this.formats.map((item) => {
            let className = null;
            if (item === activeValue) {
                className = 'active';
            }
            return (
                <li className={className} key={item} onClick={this.onClick.bind(this, item)}>{item}</li>
            );
        });
        return (
            <ul className='format-list'>
                {list}
            </ul>
        );
    }

    private setDialogValue(): void {
        const cell = this.cell = {
            bProtected: false,
            customProperty: undefined,
            serialNumber: undefined,
            hour: 'HH',
            split: ':',
            minute: 'MM',
            format: '',
            visible: null,
        };

        this.bAutoFormat = false;
        const cellProps = this.props.documentCore.getTableCellProps();
        if (!cellProps) {
            return;
        }
        cell.bProtected = cellProps.bProtected;
        const property = cellProps.nisProperty;
        if (property) {
            this.nisProps = cellProps.nisProperty;
            cell.serialNumber = property.serialNumber;
            cell.customProperty = property.customProperty;
            cell.visible = property.gridLine?.visible;
            const format = cell.format = property.timeFormat;
            const obj = this.formats.find((item) => item === format);
            if (!obj) {
                this.bAutoFormat = true;
                const reg = /HH([\s\S]+)MM/g;
                const match = reg.exec(format);
                if (match) {
                    cell.split = match[1];
                }
            }
        }
    }

    private checkChange = (value: any, name: string): void => {
        this.bAutoFormat = value;
        if (value === true) {
            this.cell.format = null;
        }
        this.setState({});
    }

    private onClick = (item: string): void => {
        this.cell.format = item;
        this.bAutoFormat = false;
        this.setState({});
    }

    private open = (): void => {
        // const table = this.table;
        this.setDialogValue();
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private close = (bRefresh?: boolean): void => {
        this.props.close(this.props.id, bRefresh);
        this.visible = false;
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private onClose = (id: string, bRefresh: boolean): void => {
        this[id] = false;
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private onChange = (value: any, name: string): void => {
        this.cell[name] = value;
    }

    private confirm = (id?: any): void => {
        const { documentCore } = this.props;
        IFRAME_MANAGER.setDocId(documentCore.getCurrentId());
        let format: string;
        const cell = this.cell;
        if (this.bAutoFormat) {
            if (!cell.split) {
                message.error('时间格式不符合规范，请重新输入');
                return;
            }
            format = cell.hour + cell.split + cell.minute;
            const reg = /HH([\s\S]+)MM(([\s\S]+)SS)?/g;
            const match = reg.exec(format);
            if (!match) {
                message.error('时间格式不符合规范，请重新输入');
                return;
            }
        } else {
            format = cell.format;
            if (!format) {
                message.error('时间格式不符合规范，请重新输入');
                return;
            }
        }

        documentCore.setTableCellProps({bProtected: this.cell.bProtected,
            nisProperty: {
                timeFormat: format,
                serialNumber: this.cell.serialNumber,
                customProperty: this.cell.customProperty,
                gridLine: {visible: cell.visible}
            }});

        this.close(true);
    }

}
