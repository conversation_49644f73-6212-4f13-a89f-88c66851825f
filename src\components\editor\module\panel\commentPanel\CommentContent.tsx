import React from 'react';
import { CommentOperatorType } from '@/common/commonDefines';
import { CommentData } from '@/model/core/Comment/CommentData';

export interface ICommentInfo {
    name: string;
    id: number;
    userName: string;
    dateTime: string;
    content: string;
    isSolved: boolean;
}

enum CommentStatusType {
    IsNormal,   // 正常状态
    IsNew,      // 新增批注
    IsModify,   // 批注内容修改
    IsReply,    // 正在回复
}

interface IProps {
    index: number;
    userName: string;
    info?: {
        name: string;
        id: number;
        data?: CommentData;
    };
    isNew?: boolean;
    isActived?: boolean;
    isSolved?: boolean;
    onBlur?: (isNew: boolean) => void;
    updateComment: (type: CommentOperatorType, info: Partial<ICommentInfo>) => void;
    activeComment: (name: string) => void;
}

interface IState {
    review: string;
    editId: number;
}

export default class CommentContent extends React.Component<IProps, IState> {
    private domRef: any;
    private editRef: any;
    private status: CommentStatusType; // 状态内容

    constructor(props: IProps) {
        super(props);

        this.domRef = React.createRef();
        this.editRef = React.createRef();
        this.status = CommentStatusType.IsNormal;
        if (props.isNew) {
            this.status = CommentStatusType.IsNew;
        }
        this.state = {
            review: '',
            editId: -1,
        };
    }

    public render(): any {
        const {index, isActived} = this.props;
        if (!isActived) {
            this.status = CommentStatusType.IsNormal;
        }
        return (
            <div
                ref={this.domRef}
                className={`comment ${isActived ? 'active' : ''}`}
                data-index={index}
                tabIndex={-1}
            >
                {this.renderItemList()}
                {this.renderItemTool()}
                {this.renderReviewBar()}
                {this.renderBtns()}
            </div>
        );
    }

    public componentDidMount(): void {
        const dom = this.domRef.current as HTMLDivElement;
        if (dom) {
            dom.addEventListener('click', this.handleClick);
        }
        const editDom = this.editRef.current as HTMLTextAreaElement;
        if (editDom) {
            if (this.props.isNew) {
                editDom.focus();
            }
            editDom.addEventListener('keydown', this.handleKeyDown);
        }
    }

    public componentDidUpdate(prevProps: Readonly<IProps>, prevState: Readonly<IState>, snapshot?: any): void {
        const editDom = this.editRef.current as HTMLTextAreaElement;
        if (editDom) {
            if (
                this.status === CommentStatusType.IsModify ||
                this.status === CommentStatusType.IsReply ||
                this.props.isNew
            ) {
                setTimeout(() => {
                    editDom.focus();
                }, 0);
            }
        }
    }

    public componentWillUnmount(): void {
        const dom = this.domRef.current as HTMLDivElement;
        if (dom) {
            dom.removeEventListener('click', this.handleClick);
        }
        const editDom = this.editRef.current as HTMLTextAreaElement;
        if (editDom) {
            editDom.removeEventListener('keydown', this.handleKeyDown);
        }
    }

    private renderItemList(): React.ReactNode {
        const data = this.props.info?.data;
        if (!data) {
            return this.renderItem(this.getCommentInfo(undefined), -2, true, true);
        }
        const items = [data, ...data.replies];
        const children = [];
        for (let idx = 0, len = items.length - 1; idx <= len; idx++) {
            const reply = items[idx];
            children.push(this.renderItem(this.getCommentInfo(reply), reply.id, idx === len, idx === 0));
        }
        return children;
    }

    private renderItemTool(): React.ReactNode {
        let toolDom: any = null;
        const status = this.status;
        const style: any = {};
        if (status === CommentStatusType.IsNormal) {
            toolDom = (
                <div className='iconfont comments reply'>
                    <span className='reply'>回复</span>
                </div>
            );
        } else if (status === CommentStatusType.IsReply) {
            const data = this.props.info?.data;
            if (data && data.replies.length) {
                style.width = '22px';
                toolDom = (<span className='iconfont basicDelete delete'/>);
            }
        }
        let solveDom: any = null;
        if (status !== CommentStatusType.IsNew) {
            solveDom = <div className={`solve iconfont ${this.props.isSolved ? 'checked' : 'check'}`} />;
        }
        return (
            <React.Fragment>
                <div className='tools divide' style={style}>
                    {toolDom}
                </div>
                {solveDom}
            </React.Fragment>
        );
    }

    private renderItem(
        info: ICommentInfo,
        keyId: number,
        isLast: boolean = false,
        isFirst: boolean = false
    ): React.ReactNode {
        let toolDom: any;
        const status = this.status;
        if (status === CommentStatusType.IsReply && isLast) {
            const isSameUser = info.userName === this.props.userName;
            const style: any = {};
            let edit: any = null;
            if (isSameUser) {
                edit = <span className='iconfont basicEdit edit' data-index={info.id}/>;
            } else {
                style.width = '24px';
            }
            toolDom = (
                <div className={`tools${isFirst ? ' divide' : ''}`} style={style}>
                    {edit}
                    <span className='iconfont basicDelete delete' data-index={info.id}/>
                </div>
            );
        }
        let fullNameDom: any = null;
        let showedName = info.userName;
        if (showedName.length > 8) {
            fullNameDom = (<div className='label'>{showedName}</div>);
            showedName = showedName.substring(0, 8) + '...';
        }
        const isEditing = status === CommentStatusType.IsModify && info.id === this.state.editId;
        const contentStyle: any = {};
        if (!info.content || isEditing) {
            contentStyle.display = 'none';
        }
        return (
            <div className='item' key={keyId}>
                <div className='header'>
                    <div className='user-img'/>
                    <div className='title'>
                        <div className='username'>
                            {showedName}
                        </div>
                        {fullNameDom}
                        <div className='time'>{info.dateTime}</div>
                    </div>
                    {toolDom}
                </div>
                <div className='content' style={contentStyle}>
                    {info.content}
                </div>
            </div>
        );
    }

    private renderReviewBar(): React.ReactNode {
        const {review} = this.state;
        const status = this.status;
        const row = Math.floor(review.length / 18);
        const realRow = row + 1;
        const spanStyle = {
            top: row * 16 + 'px',
        };
        const barStyle: any = {};
        if (
            !this.props.isActived ||
            status === CommentStatusType.IsNormal
        ) {
            barStyle.display = 'none';
        }
        return (
            <div className='review-bar'style={barStyle}>
                <div className='review-input'>
                    <textarea
                        ref={this.editRef}
                        placeholder={status === CommentStatusType.IsReply ? '回复评论' : '添加批注'}
                        onChange={this.handleTextChange}
                        maxLength={50}
                        rows={realRow}
                        value={review}
                    />
                    <span style={spanStyle}>{review.length}/50</span>
                </div>
            </div>
        );
    }

    private renderBtns(): React.ReactNode {
        const status = this.status;
        const btnStyle: any = {};
        if (status === CommentStatusType.IsNormal) {
            btnStyle.display = 'none';
        }
        return (
            <div className='btns' style={btnStyle}>
                <span>按 Ctrl + Enter 发送</span>
                <button className='btn cancel'>取消</button>
                <button className='btn primary'>{status === CommentStatusType.IsReply ? '发送' : '确定'}</button>
            </div>
        );
    }

    private getCommentInfo(data: CommentData): ICommentInfo {
        if (!data) {
            return {
                name: '',
                id: -1,
                userName: this.props.userName || '默认用户',
                dateTime: '',
                content: '',
                isSolved: false,
            };
        }
        const info: ICommentInfo = {
            name: '',
            id: data.id,
            userName: data.getUserName(),
            dateTime: data.getStringTime(),
            content: data.getContent(),
            isSolved: data.isSolved(),
        };
        return info;
    }

    private handleKeyDown = (event: KeyboardEvent) => {
        if (event.ctrlKey && event.key === 'Enter') {
            this.confirm();
        } else if (event.key === 'Escape') {
            this.cancel();
        }
    }

    private handleTextChange = (event: any) => {
        const value = event.target.value;
        if (value !== this.state.review) {
            this.setState({ review: value });
        }
    }

    private handleClick = (event: any) => {
        event.preventDefault();
        const dom = event.target;
        if (!dom) {
            return;
        }
        const classList = dom.classList;
        let noHandle = true;
        if (classList.contains('btn')) {
            if (classList.contains('cancel')) {
                noHandle = false;
                this.cancel();
            } else if (classList.contains('primary')) {
                noHandle = false;
                this.confirm();
            }
        } else if (classList.contains('reply')) {
            noHandle = false;
            this.status = CommentStatusType.IsReply;
            const {activeComment, info} = this.props;
            if (activeComment && info?.name) {
                activeComment(info.name);
            }
            this.setState({});
        } else if (classList.contains('iconfont')) {
            if (classList.contains('solve')) {
                noHandle = false;
                const {name, data} = this.props.info;
                this.props.updateComment(CommentOperatorType.Solve, {
                    name,
                    isSolved: !data.isSolved(),
                });
            } else if (classList.contains('edit')) {
                this.status = CommentStatusType.IsModify;
                const data = this.props.info.data;
                const items = [data, ...data.replies];
                const target = items.find((item) => item.id === +dom.dataset['index']);
                if (target) {
                    noHandle = false;
                    this.setState({
                        editId: target.id,
                        review: target.getContent(),
                    });
                }
            } else if (classList.contains('delete')) {
                noHandle = false;
                const id = dom.dataset['index'] && +dom.dataset['index'];
                this.props.updateComment(CommentOperatorType.Delete, {
                    name: this.props.info.name,
                    id: id === this.props.info.data.id ? undefined : id,
                });
            }
        }
        if (noHandle) {
            const {activeComment, info} = this.props;
            if (activeComment && info?.name) {
                activeComment(info.name);
            }
        }
    }

    private confirm(): void {
        const {review, editId} = this.state;
        const { updateComment, info } = this.props;
        switch (this.status) {
            case CommentStatusType.IsNew: {
                updateComment(CommentOperatorType.New, {
                    content: review,
                });
                break;
            }
            case CommentStatusType.IsReply: {
                updateComment(CommentOperatorType.Reply, {
                    name: info.name,
                    content: review,
                });
                this.setState({review: ''});
                break;
            }
            case CommentStatusType.IsModify: {
                updateComment(CommentOperatorType.Update, {
                    name: info.name,
                    content: review,
                    id: editId
                });
                this.status = CommentStatusType.IsReply;
                this.setState({review: '', editId: -1});
                break;
            }
        }
    }

    private cancel(): void {
        switch (this.status) {
            case CommentStatusType.IsNew: {
                this.props.onBlur?.(true);
                break;
            }
            case CommentStatusType.IsModify:
            case CommentStatusType.IsReply: {
                this.status = CommentStatusType.IsNormal;
                this.setState({review: ''});
                break;
            }
        }
    }

}
