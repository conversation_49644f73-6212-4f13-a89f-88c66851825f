import { DocumentCore } from '../model/DocumentCore';
import { EmrEditor } from '../components/editor/Main';
import { getDocumentCoreRecorder, getDocumentCoreReplayer } from '../model/DocumentCoreRecordReplay';
import { GlobalEvent as gEvent, GlobalEventName as gEventName } from '../common/GlobalEvent';
import { APO_XMLS_ARRAY, EDITOR_VERSION, FileSaveType, ToolbarIndex } from './commonDefines';
import {message} from './Message';
import { IFRAME_MANAGER } from './IframeManager';
import { DiffXml } from './copy/DiffXml';
import { pasteTest, readFile } from './AutoTestMethod';
import { FormatWriter } from '../format/writer/writer';
import { saveAs } from 'file-saver';
import { getImageSelectionName } from '../components/editor/module/document/Image';

export default class AutoRunTest {
    private _host: EmrEditor;
    private _documentCore: DocumentCore;
    private xmlProps: any;
    private _initData: boolean;
    private _saveStr: string;
    // private _generateFile: string;
    private _generateFile: string[];
    private _origionResultFile: string;
    private _baseUrl: string;

    constructor(host: any) {
        this._host = host;
        this._documentCore = host.state.documentCore;

        this._baseUrl = '';

        // this.addEvent();
    }

    public init = (editor: any): void => {
        const arrs = ['startRecord', 'stopRecord', 'exportRecord', 'exportFileMd5', 'startReplay', 'stopReplay',
        'compare', 'setAutoTestFiles', 'editRecord', 'parseApollo'];
        editor._autoTest = {};

        arrs.forEach((name) => {
            editor._autoTest[name] = this[name];
        });
        editor._autoTest._host = this._host;
    }

    

    // private addEvent(): void {
    //     gEvent.addEvent(this._host.docId, 'autoTestPlay', this.playEvent);
    // }

    public compare = (id: string): Promise<any> => {
        return new Promise<any>((resolve, reject) => {
            gEvent.setEvent(this._host.docId, gEventName.DialogEvent, ToolbarIndex.AutoTestPlay,
                {id, callback: (any): any => {
                resolve(any);
            }});
        });
    }

    public playEvent = (id: string): Promise<any> => {
        return new Promise<any>((resolve, reject) => {
            IFRAME_MANAGER.setDocId(this._host.docId);
            ajax({type: 'GET', url: this._baseUrl + 'get_file?id=' + id, data: {id},
            success: (response) => {
                // console.dir(response)
                if (!response) {
                    return;
                }
                const data = JSON.parse(response).data;
                if (!data) {
                    message.error('获取到的数据异常');
                    console.dir(response);
                    return;
                }
                this.startReplay(data.origin_file, data.run_file)
                .then((res) => {
                    console.log(res)
                    // this.exportFileMd5()
                    this.setAutoTestFiles()
                    .then(() => {
                        // TODO: compose diff
                        // const contents = this.filterData(this.compareXML(data.generate_file, this._generateFile));
                        const contents = [];
                        resolve({content: contents,
                            generate_file: data.generate_file, status_code: ''});
                    });
                });
                // this.startReplay('', '');
            }, error: (response) => {
                message.error('获取数据失败');
                console.dir(response);
            }});
        });
    }

    private  parseApollo = (content: string, name?: string): boolean => {
        const arrs = this.convertStringToUInt8Arrs(content);
        if (!arrs) {
            return false;
        }

        const formatWriter = new FormatWriter(this._host);
        const revisedBuffer = formatWriter.addFileHeader(arrs.buffer, this._documentCore.getDocument(), false);
        const fileType: string = FileSaveType.APO + ';version=' + EDITOR_VERSION;
        const newBlob = new Blob([revisedBuffer], {type: fileType});
        name = name || ('editor-apo-' + (new Date()).getTime());
        //saveAs(newBlob, name + '.apo');
        saveAs(newBlob, name + '.hz');
        return true;
    }

    private convertStringToUInt8Arrs(sText: string): Uint8Array {
        if (!sText) {
            return null;
        }

        let arrs: string[];
        try {
            arrs = window.atob(sText)
            .split(',');
        } catch (err) {
            return null;
        }

        if (arrs.length === 0) {
            return null;
        }

        const len = arrs.length;
        const uint8Arrs = new Uint8Array(len);
        for (let index = 0; index < len; index++) {
            const num = +arrs[index];
            if (isNaN(num)) {
                return null;
            }
            uint8Arrs[index] = num;
        }
        return uint8Arrs;
    }

    private filterData(datas: any[]): any[] {
        if (!datas || datas.length === 0) {
            return [];
        }
        const obj = {};
        datas.forEach((data) => {
            let currentKey = '';
            Object.keys(data)
            .forEach((key) => {
                currentKey += key + data[key];
            });
            if (!obj[currentKey]) {
                obj[currentKey] = data;
            }
        });

        return Object.values(obj);
    }

    private compareXML = (xml1: string, xml2: string): any[] => {
        xml1 = decodeURIComponent(atob(xml1));
        xml2 = decodeURIComponent(atob(xml2));
        const reg = /<\?xml[\s\S]+?\?>/g;
        xml1 = `<xml>` + xml1.replace(reg, '') + `</xml>`;
        xml2 = `<xml>` + xml2.replace(reg, '') + `</xml>`;
        return new DiffXml().diff(xml1, xml2, null, {left: 'Old', right: 'New'});
    }

    private startRecord = (): void => {
        this.save(0);
        const recorder = getDocumentCoreRecorder();
        recorder.start();
    }

    private stopRecord = (): void => {
        const recorder = getDocumentCoreRecorder();
        recorder.stop();
        this.save(1);
        gEvent.setEvent(this._host.docId, gEventName.DialogEvent, ToolbarIndex.AutoTest, { callback: this.sendData });
    }

    private editRecord = (options: any): Promise<any> => {
        return new Promise((resolve, reject) => {
            if (!options) {
                return resolve(false);
            }
            const callback = async (data) => {
                const  res = await this.editData(data);
                resolve(res);
            };
            gEvent.setEvent(this._host.docId, gEventName.DialogEvent, ToolbarIndex.AutoTest,
                { callback, data: options });
        });
    }

    private exportRecord = (): string => {
        const recorder = getDocumentCoreRecorder();
        return recorder.getAtionsToString();
    }

    // private exportFileMd5 = (): Promise<string> => {
    //     return new Promise((resolve, reject) => {
    //         const xmlProps: any = {
    //             documentCore: this._documentCore,
    //             properties: this.getCustomPropertiesInSettings(),
    //             selectedArea: [],
    //         };
    //         new FormatWriter().generateXmls(xmlProps, APO_XMLS_ARRAY.slice(0, 4))
    //         .then((res) => {
    //             this._generateFile = btoa(encodeURIComponent(res.join('')));
    //             resolve(md5(res.join('')));
    //         });
    //     });
    // }

    private setAutoTestFiles = (): Promise<string[]> => {
        return new Promise((resolve, reject) => {
            // tslint:disable-next-line: newline-per-chained-call
            this._host.getEditor().generateAutoTestFiles().then((res) => {
                this._generateFile = res;
                resolve(res);
            });
        });
    }

    private startReplay = async (source: string, sJson: string): Promise<number> => {
        if (!source) {
            console.warn('请输入源文件字符串');
            return 3;
        }
        if (!sJson) {
            console.warn('请调用exportRecord获取参数');
            return 3;
        }
        return this.startReplay1(source, sJson);
    }

    private stopReplay = (): void => {
        const replayer = getDocumentCoreReplayer();
        replayer.cancel();
    }

    private execPaste = (option: any): Promise<void> => {
        return pasteTest(option, this._documentCore);
    }

    private execReadFile = (option: any): Promise<void> => {
        return readFile(option, this._documentCore);
    }

    private async startReplay1(source: string, sJson: string): Promise<number> {
        const replayer = getDocumentCoreReplayer();
        // this._documentCore.clearContent();
        // this._documentCore.createNewDocument(true);
        // this._documentCore.createNewDocument();
        const number = await this._host.getEditor()
        .openDocumentWithString(source, '2');
        if (number !== 0) {
            return 2;
        }

        return new Promise((resolve, reject) => {
            replayer.setDocumentCore(this._documentCore);
            replayer.addCustomMethod('paste', this.execPaste);
            replayer.addCustomMethod('readFile', this.execReadFile);
            replayer.addCustomMethod('setImageSelectionInfo', this.customRefresh);
            replayer.addCustomMethod('documentRefresh', async () => {
                return new Promise((resolve, reject) => {
                    this.finishedCallback();
                    this._documentCore.clearAllNewcontrolCascade();
                    setTimeout(() => {
                        resolve(null);
                    }, 0);
                });
            });
            replayer.importActionsByJson(sJson);
            replayer.onFramePlay(this.playFrameCallback);
            replayer.onFinished(() => {
                resolve(0);
                this.finishedCallback();
            });
            replayer.onCanceled(() => {
                resolve(1);
            });
            replayer.onError((e) => {
                console.error(e);
                resolve(4);
            });
            replayer.start();
        });
    }

    private customRefresh = (element: any): void => {
        const logicDocument = this._documentCore.getDocument();
        if (typeof element !== 'string') {
            const imageName = getImageSelectionName(element);
            logicDocument.setImageSelectionInfo(imageName);
            return;
        }

        // const dom = this._host.myRef.current;
        // if (!dom) {
        //     return;
        // }

        this.finishedCallback();
        // const el = dom.querySelector('#' + element);
        // if (!el) {
        //     return;
        // }

        // // IMAGE_FLAGS.isImageOnClick = true;
        // logicDocument.setImageOnClick(true);

        const selectionInfo = getImageSelectionName(element);
        this._documentCore.getDocument()
            .setImageSelectionInfo(selectionInfo);

        this._documentCore.selectedImageByName2(selectionInfo);
    }

    private getCustomPropertiesInSettings(): Map<string, any> {
        // TODO: some logic to get "业务属性"
        const properties = new Map();
        // example data
        properties.set('apple', 15);
        properties.set('soul', 'dark');
        properties.set('fire', 'fading');
        properties.set('lords', 'throneless');
        return properties;
    }

    private editData = (data: any): Promise<any> => {
        return new Promise((resolve, reject) => {
            const obj = {
                id: data.id,
                module: data.name,
                test_case: data.case,
                test_step: data.step || 'addf'
            };
            // tslint:disable-next-line: no-console
            console.log(obj);
            IFRAME_MANAGER.setDocId(this._host.docId);
            // ajax({type: 'POST', url: 'http://apollo-emr-backend-edit-auto.sy/api/add_case',
            // data: obj, success: (res) => {
            //     message.info('新增成功');
            //     // tslint:disable-next-line: no-console
            //     console.log(res);
            // }, error: (err) => {
            //     message.info('新增错误');
            //     console.log(err);
            // }});
            fetch(this._baseUrl + 'edit_case?id=' + data.id, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(obj),
            })
            .then((res) => res.json())
            .then(
                (result) => {
                    message.info('修改成功');
                    // tslint:disable-next-line: no-console
                    console.log(result);
                    resolve(true);
                },
                (error) => {
                    message.info('修改错误');
                    // tslint:disable-next-line: no-console
                    console.log(error);
                    resolve(false);
                }
            );
        });
    }

    private sendData = (data: any): void => {
        if (!data) {
            return;
        }
        // this.exportFileMd5()
        this.setAutoTestFiles()
        .then(() => {
            const obj = {
                module: data.name,
                test_case: data.case,
                test_step: data.step || 'addf',
                // status_code: '',
                origin_file: this._saveStr,
                generate_file: this._generateFile,
                origin_result_file: this._origionResultFile,
                run_file: this.exportRecord(),
            };
            // tslint:disable-next-line: no-console
            console.log(obj);
            IFRAME_MANAGER.setDocId(this._host.docId);
            // ajax({type: 'POST', url: 'http://apollo-emr-backend-edit-auto.sy/api/add_case',
            // data: obj, success: (res) => {
            //     message.info('新增成功');
            //     // tslint:disable-next-line: no-console
            //     console.log(res);
            // }, error: (err) => {
            //     message.info('新增错误');
            //     console.log(err);
            // }});
            fetch(this._baseUrl + 'add_case', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(obj),
            })
            .then((res) => res.json())
            .then(
                (result) => {
                    message.info('新增成功');
                    // tslint:disable-next-line: no-console
                    console.log(result);
                },
                (error) => {
                    message.info('新增错误');
                    // tslint:disable-next-line: no-console
                    console.log(error);
                }
            );
        });
    }

    private save(type: number): void {
        this._host.getEditor()
            .saveToString()
            .then((res) => {
                if ( 0 === type ) {
                    this._saveStr = res;
                } else if ( 1 === type ) {
                    this._origionResultFile = res;
                }
            });
    }

    private playFrameCallback = (): void => {
        // this._host.handleRefresh();
    }

    private finishedCallback = (): void => {
        this._host.handleRefresh();
        // console.log('finished')
    }

    private canceledCallback = (): void => {
        // console.log('cancel')
    }

}

function ajax(option: any): void {
    const callback = () => {return; };
    const ajaxData = {
        type: arguments[0].type || 'GET',
        url: arguments[0].url || '',
        async: arguments[0].async || 'true',
        data: arguments[0].data || null,
        dataType: arguments[0].dataType || 'text',
        contentType: arguments[0].contentType || 'application/x-www-form-urlencoded',
        beforeSend: arguments[0].beforeSend || callback,
        success: arguments[0].success || callback,
        error: arguments[0].error || callback
    };
    ajaxData.beforeSend();
    const xhr = new XMLHttpRequest();
    xhr.responseType = ajaxData.dataType;
    xhr.open(ajaxData.type, ajaxData.url, ajaxData.async);
    xhr.setRequestHeader('Content-Type', ajaxData.contentType);
    xhr.send(convertData(ajaxData.data));
    // tslint:disable-next-line: only-arrow-functions
    xhr.onreadystatechange = function(): void {
        if (xhr.readyState === 4) {
            if (xhr.status === 200) {
                ajaxData.success(xhr.response);
            } else {
                ajaxData.error(xhr);
            }
        }
    };
    xhr.onerror = function(e): void {
        ajaxData.error(e);
    };
}

function convertData(data: any): any {
    if (typeof data === 'object') {
        let convertResult = '';
        // tslint:disable-next-line: forin
        for (const c in data) {
            convertResult += c + '=' + data[c] + '&';
        }
        convertResult = convertResult.substring(0, convertResult.length - 1)
        return convertResult;
    } else {
        return data;
    }
}

