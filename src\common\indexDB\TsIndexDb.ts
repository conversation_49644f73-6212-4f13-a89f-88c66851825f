/*
 * @Description: file content
 * @Author: 小白
 * @Date: 2020-04-08 21:25:02
 * @LastEditors: 小白
 * @LastEditTime: 2020-04-10 14:40:36
 */
export interface IIndexDb {
    dbName: string;
    version: number;
    tables: IDbTable[];
}

export interface IDbIndex { key: string; option?: IDBIndexParameters; }
export interface IDbTable {
    tableName: string;
    option?: IDBObjectStoreParameters;
    indexs: IDbIndex[];
}
export interface IDbOperate<T> {
    tableName: string;
    key: string;
    data: T | T[];
    value: string | number;
    condition(data: T): boolean;
    success(res: T[] | T): void;
    handle(res: T): void;

}

export class TsIndexDb {

    private static _instance: TsIndexDb | null = null;

    public static getInstance(dbOptions?: IIndexDb): TsIndexDb {
        if (TsIndexDb._instance === null && dbOptions) {
            TsIndexDb._instance = new TsIndexDb(dbOptions);
        }
        return TsIndexDb._instance!;
    }

    protected db: IDBDatabase | null = null;
    private dbName: string = ''; // 数据库名称
    private version: number = 1; // 数据库版本
    private tableList: IDbTable[] = []; // 表单列表
    private queue: (() => void)[] = []; // 事务队列，实例化一次以后下次打开页面时数据库自启动
    constructor({ dbName, version, tables }: IIndexDb) {
        this.dbName = dbName;
        this.version = version;
        this.tableList = tables;
    }

    // =================relate select================================
    /**
     * @method 查询某张表的所有数据(返回具体数组)
     * @param {Object}
     *   @property {String} tableName 表名
     */
    public queryAll<T>({ tableName }: Pick<IDbOperate<T>, 'tableName'>) {
        const res: T[] = [];
        // tslint:disable-next-line: max-line-length
        return this.commitDb<T[]>(tableName, (transaction: IDBObjectStore) => transaction.openCursor(), 'readonly', (e: any, resolve: (data: T[]) => void) => {
            this.cursorSuccess(e, {
                condition: () => true,
                handler: ({ currentValue }: any) => res.push(currentValue),
                success: () => resolve(res)
            });
        });
    }

    /**
     * @method 查询(返回具体数组)
     * @param {Object}
     *   @property {String} tableName 表名
     *   @property {Function} condition 查询的条件
     */
    public query<T>({ tableName, condition }: Pick<IDbOperate<T>, 'condition' | 'tableName'>) {
        let res: T[] = [];
        // tslint:disable-next-line: max-line-length
        return this.commitDb<T[]>(tableName, (transaction: IDBObjectStore) => transaction.openCursor(), 'readonly', (e: any, resolve: (data: T[]) => void) => {
            this.cursorSuccess(e, {
                condition,
                handler: ({ currentValue }: any) => res.push(currentValue),
                success: () => resolve(res)
            });
        });
    }

    /**
     * @method 查询数据(更具表具体属性)返回具体某一个
     * @param {Object}
     *   @property {String} tableName 表名
     *   @property {Number|String} key 名
     *   @property {Number|String} value 值
     *
     */
    public queryByKeyValue<T>({ tableName, key, value }: Pick<IDbOperate<T>, 'tableName' | 'key' | 'value'>) {
        return this.commitDb<T>(tableName, (transaction: IDBObjectStore) => transaction.index(key).get(value), 'readonly', (e: any, resolve: (data: T) => void) => {
            resolve(e.target.result || null);
        });
    }

    /**
     * @method 查询数据（主键值）
     * @param {Object}
     *   @property {String} tableName 表名
     *   @property {Number|String} value 主键值
     *
     */
    public queryByPrimaryKey<T>({ tableName, value }: Pick<IDbOperate<T>, 'tableName' | 'value'>) {
        // tslint:disable-next-line: max-line-length
        return this.commitDb<T>(tableName, (transaction: IDBObjectStore) => transaction.get(value), 'readonly', (e: any, resolve: (data: T) => void) => {
            resolve(e.target.result || null);
        });
    }

    // =================relate update================================
    /**
     * @method 修改数据(返回修改的数组)
     * @param {Object}
     *   @property {String} tableName 表名
     *   @property {Function} condition 查询的条件，遍历，与filter类似
     *      @arg {Object} 每个元素
     *      @return 条件
     *   @property {Function} handle 处理函数，接收本条数据的引用，对其修改
     */
    public update<T>({ tableName, condition, handle }: Pick<IDbOperate<T>, 'tableName' | 'condition' | 'handle'>) {
        const res: T[] = [];
        // tslint:disable-next-line: max-line-length
        return this.commitDb<T>(tableName, (transaction: IDBObjectStore) => transaction.openCursor(), 'readwrite', (e: any, resolve: (data: T[]) => void) => {
            this.cursorSuccess(e, {
                condition,
                handler: ({ currentValue, cursor }: any) => {
                    const value = handle(currentValue);
                    res.push(value as any);
                    cursor.update(value);
                },
                success: () => {
                    resolve(res);
                }
            });
        });
    }

    /**
     * @method 修改某条数据(主键)返回修改的对象
     * @param {Object}
     *   @property {String} tableName 表名
     *   @property {String\|Number} value 目标主键值
     *   @property {Function} handle 处理函数，接收本条数据的引用，对其修改
     */
    public updateByPrimaryKey<T>({ tableName, value, handle }: Pick<IDbOperate<T>, 'tableName' | 'value' | 'handle'>) {
        return this.commitDb<T>(tableName, (transaction: IDBObjectStore) => transaction.get(value), 'readwrite',
            (e: any, resolve: (data: T | null) => void, store: IDBObjectStore) => {
                const currentValue = e.target.result;
                if (!currentValue) {
                    resolve(null);
                    return;
                }
                const value = handle(currentValue);
                store.put(value);
                resolve(value as any);
            });
    }

    // =================relate insert================================
    /**
     * @method 增加数据
     * @param {Object}
     *   @property {String} tableName 表名
     *   @property {Object} data 插入的数据
     */
    public insert<T>({ tableName, data }: Pick<IDbOperate<T>, 'tableName' | 'data'>) {
        return this.commitDb<T>(tableName, undefined, 'readwrite',
            (_: any, resolve: () => void, store: IDBObjectStore) => {
                data instanceof Array ? data.forEach(v => store.put(v)) : store.put(data);
                resolve();
            });

    }
    // =================relate delete================================
    /**
     * @method 删除数据(返回删除数组)
     * @param {Object}
     *   @property {String} tableName 表名
     *   @property {Function} condition 查询的条件，遍历，与filter类似
     *      @arg {Object} 每个元素
     *      @return 条件
     */
    public delete<T>({ tableName, condition }: Pick<IDbOperate<T>, 'tableName' | 'condition'>) {
        const res: T[] = [];
        // tslint:disable-next-line: max-line-length
        return this.commitDb<T>(tableName, (transaction: IDBObjectStore) => transaction.openCursor(), 'readwrite', (e: any, resolve: (data: T[]) => void) => {
            this.cursorSuccess(e, {
                condition,
                handler: ({ currentValue, cursor }: any) => {
                    res.push(currentValue);
                    cursor.delete();
                },
                success: () => {
                    resolve(res);
                }
            });
        });
    }

    /**
     * @method 删除数据(主键)
     * @param {Object}
     *   @property {String} tableName 表名
     *   @property {String\|Number} value 目标主键值
     */
    public deletByPrimaryKey<T>({ tableName, value }: Pick<IDbOperate<T>, 'tableName' | 'value'>) {
        // tslint:disable-next-line: max-line-length
        return this.commitDb<T>(tableName, (transaction: IDBObjectStore) => transaction.delete(value), 'readwrite', (e: any, resolve: () => void) => {
            resolve();
        });
    }

    // =================relate db================================

    /**
     * @method 打开数据库
     */
    public openDb() {
        return new Promise<TsIndexDb>((resolve, reject) => {
            const request = window.indexedDB.open(this.dbName, this.version);
            request.onerror = e => {
                reject(e);
            };
            request.onsuccess = (event: any) => {
                this.db = event.target.result;
                let task: () => void;

                // tslint:disable-next-line: no-conditional-assignment
                while (task = this.queue.pop() as any) {
                    task();
                }

                resolve(this);
            };
            // 数据库升级
            request.onupgradeneeded = e => {
                this.tableList.forEach((element: IDbTable) => {
                    const tmpDb = (e.target as any).result;
                    this.deleteTableFromDb(tmpDb, element.tableName);
                    this.createTable((e.target as any).result, element);
                });
            };
        });
    }

    /**
     * @method 关闭数据库
     * @param  {[type]} db [数据库名称]
     */
    public closeDb() {
        return new Promise((resolve, reject) => {
            try {
                if (!this.db) {
                    resolve('请开启数据库');
                    return;
                }
                this.db!.close();
                this.db = null;
                resolve(true);
            } catch (error) {
                reject(error);
            }
        });

    }
    /**
     * @method 删除数据库
     * @param {String}name 数据库名称
     */
    public deleteDb(name: string) {
        return new Promise((resolve, reject) => {
            const request = indexedDB.deleteDatabase(name);
            request.onerror = e => {
                reject(e);
            };
            request.onsuccess = e => {
                resolve(e);
            };
        });
    }

    /**
     * @method 删除表数据
     * @param {String}name 数据库名称
     */
    public deleteTable(tableName: string) {
        return this.commitDb(tableName, (transaction: IDBObjectStore) => transaction.clear(), 'readwrite',
            (_: any, resolve: () => void) => {
                resolve();
            });
    }

    /**
     * @method 游标开启成功,遍历游标
     * @param {Function} 条件
     * @param {Function} 满足条件的处理方式 @arg {Object} @property cursor游标 @property currentValue当前值
     * @param {Function} 游标遍历完执行的方法
     * @return {Null}
     */
    public cursorSuccess(e: any, { condition, handler, success }: any) {
        const cursor: IDBCursorWithValue = e.target.result;
        if (cursor) {
            const currentValue = cursor.value;
            if (condition(currentValue)) {
                handler({ cursor, currentValue });
            }
            cursor.continue();
        } else {
            success();
        }
    }
    /**
     * 创建table
     * @option<Object>  keyPath指定主键 autoIncrement是否自增
     * @index 索引配置
     */
    private createTable(idb: any, { tableName, option, indexs = [] }: IDbTable) {

        if (!idb.objectStoreNames.contains(tableName)) {
            const store = idb.createObjectStore(tableName, option);
            for (let { key, option } of indexs) {
                store.createIndex(key, key, option);
            }
        }
    }

    /**
     * 删除目标db中的指定表格
     * @param idb indexDb对象
     * @param tableName 
     */
    private deleteTableFromDb(idb: any, tableName: string) {
        if (idb.objectStoreNames.contains(tableName)) {
            idb.deleteObjectStore(tableName);
        }
    }

    /**
     * 提交Db请求
     * @param tableName  表名
     * @param commit 提交具体函数
     * @param mode 事物方式
     * @param backF 游标方法
     */
    private commitDb<T>(tableName: string,
                        commit?: (transaction: IDBObjectStore) => IDBRequest<any>,
                        mode: IDBTransactionMode = 'readwrite',
                        backF?: (request: any, resolve: any, store: IDBObjectStore) => void) {
        return new Promise<T>((resolve, reject) => {
            const task = () => {
                try {
                    if (this.db) {
                        const store = this.db.transaction(tableName, mode).objectStore(tableName);
                        if (!commit) {
                            backF!(null, resolve, store);
                            return;
                        }
                        const res = commit(store);
                        res!.onsuccess = (e: any) => {
                            if (backF) {
                                backF(e, resolve, store);
                            } else {
                                resolve(e);
                            }
                        };
                        res!.onerror = (event) => {
                            reject(event);
                        };

                    } else {
                        reject(new Error('请开启数据库'));
                    }
                } catch (error) {
                    reject(error);
                }
            };

            if (!this.db) {
                this.queue.push(task);
            } else {
                task();
            }

        });
    }
}
