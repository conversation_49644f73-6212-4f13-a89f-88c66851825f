@import './global.less';
.hz-editor-container .editor-font {
    & * {
        box-sizing: border-box;
    }

    .title {
        #less.title();
        line-height: 28px;
    }
    .font-top > ul {
        display: flex;
        width: 100%;
        margin: 10px 0;
        padding: 10px;
        background: #eee;
    }

    .font-top > ul > li:nth-of-type(1) {
        width: 50%;
        margin-right: 10px;
    }

    .font-top > ul > li:nth-of-type(2) {
        width: calc(25% - 20px);
        margin-right: 10px;
    }

    .font-top > ul > li:nth-of-type(3) {
        width: 25%;
    }

    .select-list {
        font-size: 12px;
        background: #fff;
    }

    .select-list-value {
        width: 100%;
        height: 22px;
        line-height: 22px;
        padding: 0 5px;
        border: 1px solid @activeColor;
    }

    .select-list > ul {
        height: calc(100% - 22px);
        border: 1px solid #ccc;
        overflow: auto;
    }

    .select-list > ul > li {
        height: 20px;
        padding: 0 5px;
        line-height: 20px;
        overflow: hidden;
    }

    .select-list > ul > li:hover {
        background: #eee;
        cursor: pointer;
    }

    .select-list > ul > li.active {
        color: #fff;
        background: @activeColor;
    }

    .font-footer > div {
        height: 60px;
        line-height: 60px;
        text-align: center;
        border: 1px solid #999;
        overflow: hidden;
    }
}
