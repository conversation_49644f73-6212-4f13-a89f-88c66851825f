// import * as React from 'react';
// import { EmrEditor } from '../../Main';
// import NewControlNum from './NewControlNumber';
// import NewControlText from './NewControlText';
// import NewComboBox from './NewComboBox';
// import Font from './Font';
// import Image from './Image';
// import Paragraph from './Paragraph';
// import {GlobalEvent as gEvent, GlobalEventName as gEventName} from '../../../../common/GlobalEvent';
// import { ToolbarIndex, MenuItemIndex } from '../../../../common/commonDefines';
// import Char from './SpecialCharacter';
// import DeleteCell from './DeleteCell';
// import InsertTable from './InsertTable';
// import PageSetting from './PageSetting';
// import InsertEquation from './InsertEquation';
// import EditEquation from './equation';
// import NewDateBox from './NewDateBox';
// import NewControlCheck from './NewControlCheck';
// import NewControlRadio from './NewControlRadio';
// import ViewScale from './ViewScale';
// import Region from './Region';
// import {PrintDialog} from './PrintDialog';
// import NewHeaderFooter from './NewHeaderFooter';
// import TableSettingDialog from './TableSetting';
// import NavMenu from './NavMenu';
// import NewPageNum from './NewPageNum';
// import Revision, { RevisionAcceptReject } from './Revision';
// import { TableCellSplit } from './SplitCell';
// import { DocumentCore } from '../../../../model/DocumentCore';
// import AutoTest from './AutoTest';
// import AutoTestPlay from './AutoTestPlay';
// import NewSignatureBox from './NewSignatureBox';

// interface IProps {
//     host: any;
// }

// interface IState {
//     bRefresh: boolean;
// }

// export default class Modal extends React.Component<IProps, IState> {
//     private documentCore: any;
//     private host: any;
//     private parent: any;
//     private property: any;

//     private bNewControlNum: boolean;
//     private bNewControlText: boolean;
//     private bNewComboBox: boolean;
//     private bNewControlCheck: boolean;
//     private bNewControlRadio: boolean;
//     private bChart: boolean;
//     private bFont: boolean;
//     private bParagraph: boolean;
//     private bImage: boolean;
//     private bSplitCell: boolean;
//     private bDeleteCell: boolean;
//     private bTableProps: boolean;
//     private bInsertTable: boolean;
//     private bSetPage: boolean;
//     private bInsertEquation: boolean;
//     private bNewDateBox: boolean;
//     private bNewSignatureBox: boolean;
//     private bNewHeaderFooter: boolean;
//     private bNewPageNum: boolean;
//     private bEditEquation: boolean;
//     private bViewScale: boolean;
//     private bRegion: boolean;
//     private bNavMenu: boolean;
//     private printDialog: PrintDialog;
//     private bRevisionSetting: boolean;
//     private bRevisionAcceptReject: boolean;
//     private bAutoTest: boolean;
//     private bAutoTestPlay: boolean;
//     private callback: any;
//     private width: number;
//     private image: any;
//     private tableName: string;
//     constructor(props: IProps) {
//         super(props);
//         this.parent = props.host;
//         // this.documentCore = props.host.state.documentCore;
//         this.state = {
//             bRefresh: false,
//         };
//     }

//     public render(): any {
//         return (
//             <React.Fragment>
//                 {this.renderNewControlNum()}
//                 {this.renderFont()}
//                 {this.renderParagraph()}
//                 {this.renderImage()}
//                 {this.renderSplitCell()}
//                 {this.renderNewControlText()}
//                 {this.renderNewComboBox()}
//                 {this.renderChart()}
//                 {this.renderDeleteCell()}
//                 {this.renderInsertTable()}
//                 {this.renderTable()}
//                 {this.renderSetPage()}
//                 {this.renderInsertEquation()}
//                 {this.renderNewDateBox()}
//                 {this.renderNewSignatureBox()}
//                 {this.renderEditEquation()}
//                 {this.renderNewControlCheck()}
//                 {this.renderNewControlRadio()}
//                 {this.renderViewScale()}
//                 {this.renderHeaderFooterDialog()}
//                 {this.renderNavMenu()}
//                 {this.renderPageNumDialog()}
//                 {this.renderRevisionSetting()}
//                 {this.renderRevisionAcceptReject()}
//                 {this.renderRegion()}
//                 {this.renderAutoTest()}
//                 {this.renderAutoTestPlay()}
//             </React.Fragment>
//         );
//     }

//     // public componentWillMount(): void {
//     //     gEvent.addEvent(this.host.docId, gEventName.DialogEvent, this.showTypeModal);
//     // }

//     // public componentDidMount(): void {
//     //     //
//     // }

//     // public componentWillUnmount(): void {
//     //     gEvent.deleteEvent(this.host.docId, gEventName.DialogEvent, this.showTypeModal);
//     // }

//     public showTypeModal = (type: number, option?: any): void => {
//         this.setEditorInfo(gEvent.getActiveDocId());
//         switch (type) {
//             case ToolbarIndex.NumberBox: {
//                 this.bNewControlNum = true;
//                 this.property = option;
//                 break;
//             }
//             case ToolbarIndex.TextBox: {
//                 this.bNewControlText = true;
//                 this.property = option;
//                 break;
//             }
//             case ToolbarIndex.SingleOrMultipleBox: {
//                 this.bNewComboBox = true;
//                 this.property = option;
//                 break;
//             }
//             case MenuItemIndex.Font: {
//                 this.bFont = true;
//                 break;
//             }
//             case ToolbarIndex.AutoTest: {
//                 this.callback = option.callback;
//                 this.bAutoTest = true;
//                 break;
//             }
//             case ToolbarIndex.AutoTestPlay: {
//                 this.bAutoTestPlay = true;
//                 break;
//             }
//             case MenuItemIndex.Paragraph: {
//                 this.bParagraph = true;
//                 break;
//             }
//             case MenuItemIndex.Image: {
//                 this.image = option;
//                 this.bImage = true;
//                 break;
//             }
//             case MenuItemIndex.SplitCell: {
//                 this.bSplitCell = true;
//                 break;
//             }
//             case ToolbarIndex.SpecialCharacter: {
//                 this.bChart = true;
//                 break;
//             }
//             case MenuItemIndex.DeleteRow:
//             case MenuItemIndex.DeleteCol: {
//                 this.bDeleteCell = true;
//                 break;
//             }
//             case MenuItemIndex.Table: {
//                 this.bTableProps = true;
//                 this.tableName = (option || {}).name;
//                 break;
//             }
//             case MenuItemIndex.Formula: {
//                 this.bEditEquation = true;
//                 this.image = option;
//                 break;
//             }
//             case ToolbarIndex.InsertTable: {
//                 this.bInsertTable = true;
//                 break;
//             }
//             case ToolbarIndex.PageSet: {
//                 this.bSetPage = true;
//                 break;
//             }
//             case ToolbarIndex.Region: {
//                 this.bRegion = true;
//                 this.property = option;
//                 break;
//             }
//             case ToolbarIndex.MedEquation: {
//                 this.bInsertEquation = true;
//                 break;
//             }
//             case ToolbarIndex.Checkbox: {
//                 this.bNewControlCheck = true;
//                 this.property = option;
//                 break;
//             }
//             case ToolbarIndex.Radio: {
//                 this.bNewControlRadio = true;
//                 this.property = option;
//                 break;
//             }
//             case ToolbarIndex.DateBox: {
//                 this.bNewDateBox = true;
//                 this.property = option;
//                 break;
//             }
//             case ToolbarIndex.SignatureBox: {
//                 this.bNewSignatureBox = true;
//                 this.property = option;
//                 break;
//             }
//             case ToolbarIndex.Print: {
//                 this.initPrint();
//                 this.printDialog.open(true);
//                 break;
//             }
//             case ToolbarIndex.PrintOutpatient: {
//                 this.initPrint();
//                 this.printDialog.open(true, option);
//                 break;
//             }
//             case ToolbarIndex.ReviewPrint: {
//                 this.initPrint();
//                 const flag = option === undefined ? false : option;
//                 // if (flag !== true) {
//                 //     this.documentCore.disableHeaderFooterForPrintReview();
//                 // }
//                 this.printDialog.open(flag);
//                 break;
//             }
//             case ToolbarIndex.ViewScale: {
//                 this.bViewScale = true;
//                 this.width = this.host.myRef.current.clientWidth;
//                 break;
//             }
//             case ToolbarIndex.HeaderFooter: {
//                 this.bNewHeaderFooter = true;
//                 break;
//             }
//             case ToolbarIndex.NavMenu: {
//                 this.bNavMenu = true;
//                 break;
//             }
//             case ToolbarIndex.PageNum: {
//                 this.bNewPageNum = true;
//                 break;
//             }
//             case ToolbarIndex.RevisionSetting: {
//                 this.bRevisionSetting = true;
//                 break;
//             }
//             case ToolbarIndex.RevisionAcceptReject: {
//                 this.bRevisionAcceptReject = true;
//                 break;
//             }
//             default:
//                 return;
//         }

//         this.setState({bRefresh: !this.state.bRefresh});
//     }

//     private setEditorInfo(id: number): void {
//         const editorInfo = this.parent.getEditorInfo(id);
//         this.host = editorInfo.host;
//         this.documentCore = editorInfo.documentCore;
//     }

//     private initPrint(): void {
//         this.printDialog = new PrintDialog();
//         const document = new DocumentCore(undefined, false);
//         this.printDialog.setDoc(document);
//         const documentCore = this.documentCore;
//         const content = documentCore.getDocContent();
//         // console.log(content);
//         document.addDocContent(content);
//         // this.printDialog.setContent(content);
//         documentCore.removeSelection();
//         this.printDialog.onClose(() => {
//             this.printDialog = null;
//         });
//         // this.printDialog.setDoc(document);
//     }

//     private renderAutoTest(): any {
//         if (this.bAutoTest === undefined) {
//             return null;
//         }

//         return (
//             <AutoTest
//                 id='bAutoTest'
//                 visible={this.bAutoTest}
//                 documentCore={this.documentCore}
//                 callback={this.callback}
//                 close={this.close}
//             />
//         );
//     }

//     private renderAutoTestPlay(): any {
//         if (this.bAutoTestPlay === undefined) {
//             return null;
//         }

//         return (
//             <AutoTestPlay
//                 id='bAutoTestPlay'
//                 visible={this.bAutoTestPlay}
//                 documentCore={this.documentCore}
//                 host={this.host}
//                 close={this.close}
//             />
//         );
//     }

//     private renderRegion(): any {
//         if (this.bRegion === undefined) {
//             return null;
//         }

//         return (
//             <Region
//                 id='bRegion'
//                 visible={this.bRegion}
//                 documentCore={this.documentCore}
//                 property={this.property}
//                 close={this.close}
//             />
//         );
//     }

//     private renderNavMenu(): any {
//         if (this.bNavMenu === undefined) {
//             return null;
//         }

//         return (
//             <NavMenu
//                 id='bNavMenu'
//                 refresh={this.refresh}
//                 host={this.props.host}
//                 visible={this.bNavMenu}
//                 documentCore={this.documentCore}
//                 close={this.close}
//             />
//         );
//     }

//     private renderViewScale(): any {
//         if (this.bViewScale === undefined) {
//             return null;
//         }

//         return (
//             <ViewScale
//                 id='bViewScale'
//                 width={this.width}
//                 visible={this.bViewScale}
//                 documentCore={this.documentCore}
//                 close={this.close}
//             />
//         );
//     }

//     private renderEditEquation(): any {
//         if (this.bEditEquation === undefined) {
//             return null;
//         }

//         return (
//             <EditEquation
//                 id='bEditEquation'
//                 visible={this.bEditEquation}
//                 equation={this.image}
//                 documentCore={this.documentCore}
//                 close={this.close}
//             />
//         );
//     }

//     private renderNewControlRadio(): any {
//         if (this.bNewControlRadio === undefined) {
//             return null;
//         }

//         return (
//             <NewControlRadio
//                 id='bNewControlRadio'
//                 visible={this.bNewControlRadio}
//                 documentCore={this.documentCore}
//                 property={this.property}
//                 close={this.close}
//             />
//         );
//     }

//     private renderNewControlCheck(): any {
//         if (this.bNewControlCheck === undefined) {
//             return null;
//         }
//         return (
//             <NewControlCheck
//                 id='bNewControlCheck'
//                 visible={this.bNewControlCheck}
//                 documentCore={this.documentCore}
//                 property={this.property}
//                 close={this.close}
//             />
//         );
//     }

//     private renderNewDateBox(): any {
//         if (this.bNewDateBox === undefined) {
//             return null;
//         }

//         return (
//             <NewDateBox
//                 id='bNewDateBox'
//                 visible={this.bNewDateBox}
//                 documentCore={this.documentCore}
//                 property={this.property}
//                 close={this.close}
//             />
//         );
//     }

//     private renderNewSignatureBox(): any {
//         if (this.bNewSignatureBox === undefined) {
//             return null;
//         }

//         return (
//             <NewSignatureBox
//                 id='bNewSignatureBox'
//                 visible={this.bNewSignatureBox}
//                 documentCore={this.documentCore}
//                 property={this.property}
//                 close={this.close}
//             />
//         );
//     }

//     private renderHeaderFooterDialog(): any {
//         if (this.bNewHeaderFooter === undefined) {
//             return null;
//         }

//         return (
//             <NewHeaderFooter
//                 id='bNewHeaderFooter'
//                 visible={this.bNewHeaderFooter}
//                 documentCore={this.documentCore}
//                 // property={this.property}
//                 close={this.close}
//             />
//         );
//     }

//     private renderPageNumDialog(): any {
//         if (this.bNewPageNum === undefined) {
//             return null;
//         }

//         return (
//             <NewPageNum
//                 id='bNewPageNum'
//                 visible={this.bNewPageNum}
//                 documentCore={this.documentCore}
//                 // property={this.property}
//                 close={this.close}
//             />
//         );
//     }

//     private renderInsertEquation(): any {
//         if (this.bInsertEquation === undefined) {
//             return null;
//         }

//         return (
//             <InsertEquation
//                 id='bInsertEquation'
//                 visible={this.bInsertEquation}
//                 documentCore={this.documentCore}
//                 close={this.close}
//             />
//         );
//     }

//     private renderSetPage(): any {
//         if (this.bSetPage === undefined) {
//             return null;
//         }

//         return (
//             <PageSetting
//                 id='bSetPage'
//                 visible={this.bSetPage}
//                 documentCore={this.documentCore}
//                 close={this.close}
//             />
//         );
//     }

//     private renderInsertTable(): any {
//         if (this.bInsertTable === undefined) {
//             return null;
//         }

//         return (
//             <InsertTable
//                 id='bInsertTable'
//                 visible={this.bInsertTable}
//                 documentCore={this.documentCore}
//                 close={this.close}
//             />
//         );
//     }

//     private renderDeleteCell(): any {
//         if (this.bDeleteCell === undefined) {
//             return null;
//         }

//         return (
//             <DeleteCell
//                 id='bDeleteCell'
//                 visible={this.bDeleteCell}
//                 documentCore={this.documentCore}
//                 close={this.close}
//             />
//         );
//     }

//     private renderTable(): any {
//         if (this.bTableProps === undefined) {
//             return null;
//         }

//         return (
//             <TableSettingDialog
//                 id='bTableProps'
//                 tableName={this.tableName}
//                 visible={this.bTableProps}
//                 documentCore={this.documentCore}
//                 close={this.close}
//                 refresh={this.refresh}
//             />
//         );
//     }

//     private renderChart(): any {
//         if (this.bChart === undefined) {
//             return null;
//         }

//         return (
//             <Char
//                 id='bChart'
//                 visible={this.bChart}
//                 documentCore={this.documentCore}
//                 close={this.close}
//             />
//         );
//     }

//     private renderFont(): any {
//         if (this.bFont === undefined) {
//             return null;
//         }

//         return (
//             <Font
//                 documentCore={this.documentCore}
//                 visible={this.bFont}
//                 id='bFont'
//                 close={this.close}
//             />
//         );
//     }

//     private renderSplitCell(): any {
//         if (this.bSplitCell === undefined) {
//             return null;
//         }

//         return (
//             <TableCellSplit
//                 documentCore={this.documentCore}
//                 visible={this.bSplitCell}
//                 id='bSplitCell'
//                 close={this.close}
//             />
//         );
//     }

//     private renderParagraph(): any {
//         if (this.bParagraph === undefined) {
//             return null;
//         }

//         return (
//             <Paragraph
//                 host={this.props.host}
//                 documentCore={this.documentCore}
//                 visible={this.bParagraph}
//                 id='bParagraph'
//                 close={this.close}
//             />
//         );
//     }

//     private renderImage(): any {
//         if (this.bImage === undefined) {
//             return null;
//         }

//         return (
//             <Image
//                 documentCore={this.documentCore}
//                 id='bImage'
//                 image={this.image}
//                 visible={this.bImage}
//                 close={this.close}
//             />
//         );
//     }

//     private renderNewComboBox(): any {
//         if (this.bNewComboBox === undefined) {
//             return null;
//         }

//         return (
//             <NewComboBox
//                 documentCore={this.documentCore}
//                 visible={this.bNewComboBox}
//                 property={this.property}
//                 id='bNewComboBox'
//                 close={this.close}
//             />
//         );
//     }

//     private renderNewControlNum(): any {
//         if (this.bNewControlNum === undefined) {
//             return null;
//         }

//         return (
//             <NewControlNum
//                 documentCore={this.documentCore}
//                 visible={this.bNewControlNum}
//                 property={this.property}
//                 id='bNewControlNum'
//                 close={this.close}
//             />
//         );
//     }

//     private renderNewControlText(): any {
//         if (this.bNewControlText === undefined) {
//             return null;
//         }

//         return (
//             <NewControlText
//                 documentCore={this.documentCore}
//                 visible={this.bNewControlText}
//                 property={this.property}
//                 id='bNewControlText'
//                 close={this.close}
//             />
//         );
//     }

//     private renderRevisionSetting(): any {
//         if ( null == this.bRevisionSetting ) {
//             return null;
//         }

//         return (
//             <Revision
//                 documentCore={this.documentCore}
//                 visible={this.bRevisionSetting}
//                 // property={this.property}
//                 id='bRevisionSetting'
//                 close={this.close}
//             />
//         );
//     }

//     private renderRevisionAcceptReject(): any {
//         if ( null == this.bRevisionAcceptReject ) {
//             return null;
//         }

//         // if ( this.bRevisionAcceptReject && !this.documentCore.haveRevisionChanges() ) {
//         //     this.bRevisionAcceptReject = false;
//         //     return null;
//         // }

//         return (
//             <RevisionAcceptReject
//                 documentCore={this.documentCore}
//                 visible={this.bRevisionAcceptReject}
//                 // property={this.property}
//                 id='bRevisionAcceptReject'
//                 close={this.close}
//             />
//         );
//     }

//     private close = (id: string, bRefresh: boolean, timeout?: number): void => {
//         this[id] = false;

//         // if onBlur() enabled, need to show cursor after cancellation
//         this.host.setCursorVisible(true);

//         this.refresh(bRefresh, timeout);
//     }

//    private refresh = (bRefresh: boolean, timeout?: number): void => {
//         if (bRefresh === true) {
//             this.host.handleRefresh(timeout);
//         }

//         // this.setState({bRefresh: !this.state.bRefresh});
//     }
// }
