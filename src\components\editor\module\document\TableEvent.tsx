import {Document} from './Document';
import { DocumentCore } from '../../../../model/DocumentCore';
import { layer, message } from '../../../../common/Message';
import { MenuItemIndex, MessageType } from '../../../../common/commonDefines';
import {GlobalEvent as gEvent, GlobalEventName as gEventName} from '../../../../common/GlobalEvent';
import { MessageTip } from '../../../../common/MessageTip';
import { getPagePadding } from '@/common/commonMethods';

export default class TableEvent {
    private host: Document;
    private docId: number;
    private documentCore: DocumentCore;
    private moveCell: any;
    private curCell: any;
    private bTimerline: boolean;
    private timer: any;
    private bMouseEvent: boolean;
    private bTripMouseEvent: boolean;
    private messageTip: any;
    private rowSettingDom: HTMLDivElement;
    private bSettingRow: boolean;
    private curRow: any;
    private cursorPageNum: number;
    private _bActiveDown: boolean;
    private bCusorChange: boolean;
    private bCursorTripChange: boolean;
    private _bTripAcitveDown: boolean;
    private rowSettingCell: any;

    private rowTripsDom: HTMLDivElement;
    private bSettingRowTrips: boolean;
    private rowTripsCell: any;

    constructor(host: Document) {
        this.host = host;
        this.docId = host.docId;
        this.documentCore = host.documentCore;
        this.addEvents();
    }

    public contentChange = (): void => {
        const table = this.documentCore.getCurrentTable();

        if ( table && table.execFormulaCalc() ) {
            if (this.host.isNoRefresh()) {
                this.host.updateRefreshFlag2(true);
            } else {
                this.host.refresh();
            }
        }
    }

    public clearDatas(): void {
        this.curCell = null;
        this.moveCell = null;
        this.curRow = null;
        this.moveCell = null;
        this.rowSettingCell = null;
        this.rowTripsCell = null;
    }

    public showRowtipsBtn(): void {
        return; //屏蔽此功能
        const row = this.curRow;
        const sContent = row.content[0].getCellContentText();
        if (!row) {
            this.hideRowtipsBtn();
            return;
        }
        if(sContent === '') {
            this.hideRowtipsBtn();
            return;
        }

        const pageNum = this.cursorPageNum;
        const table = row.table;
        const pageIndex = pageNum - table.getAbsolutePage();
        let y: number = table.rowsInfo[row.index]?.y[pageIndex];
        if (y === undefined) {
            y = row.content[0].content.y;
        }
        const container = this.host.getContainer()
        .querySelector(`div[page-index="${pageNum}"]`);
        if (!container) {
            return;
        }
        if (this.rowTripsDom) {
            this.hideRowtipsBtn();
        }
        let tripsDom: any = container.querySelector('.row-tips-modify');
        if (!tripsDom) {
            tripsDom = document.createElement('div');
            tripsDom.innerHTML = `<span row-index="0">${sContent}</span>`;
            tripsDom.className = 'row-tips-modify';

            if (!container) {
                return;
            }
            container.appendChild(tripsDom);
        }
        tripsDom.innerHTML = `<span row-index="0">${sContent}</span>`;
        this.rowTripsDom = tripsDom;
        const position = getPagePadding(this.documentCore);
        const {x, xLimit} = row.table;
        const height = -5; // Math.round(row.getCurrentRowHeight() / 2);
        this.bSettingRowTrips = true;
        const scale = this.host.getScale();
        tripsDom.style.left = x * scale + position.left + 'px';
        tripsDom.style.top = (y - position.top) * scale + height + 'px';
        tripsDom.style.width = Math.min((xLimit - x) * scale, tripsDom.scrollWidth) + 'px';
        const className = tripsDom.className;
        if (className.indexOf(' active') !== -1) {
            return;
        }
        tripsDom.className = className + ' active';
    }

    public showRowSettingBtn(): void {
        const row = this.curRow;
        if (!row) {
            this.hideRowSetting();
            return;
        }
        if (this.curCell === this.rowSettingCell && !this.bCusorChange) {
            return;
        }
        const bSetting = this.documentCore.isRowSettingVisible(row.table.tableName);
        this.rowSettingCell = this.curCell;
        if (!bSetting) {
            this.hideRowSetting();
            return;
        }

        const pageNum = this.cursorPageNum;
        const table = row.table;
        const pageIndex = pageNum - table.getAbsolutePage();
        let y: number = table.rowsInfo[row.index]?.y[pageIndex];
        if (y === undefined) {
            y = row.content[0].content.y;
        }
        const container = this.host.getContainer()
        .querySelector(`div[page-index="${pageNum}"]`);
        if (!container) {
            return;
        }
        if (this.rowSettingDom) {
            this.hideRowSetting();
        }
        let settingDom: any = container.querySelector('.row-setting-modify');
        if (!settingDom) {
            settingDom = document.createElement('div');
            settingDom.innerHTML = '<span row-index="0">-</span><span row-index="1">+</span>';
            settingDom.className = 'row-setting-modify';

            if (!container) {
                return;
            }
            container.appendChild(settingDom);
        }
        this.rowSettingDom = settingDom;
        const position = getPagePadding(this.documentCore);
        const {x, xLimit} = row.table;
        const height = -5; // Math.round(row.getCurrentRowHeight() / 2);
        this.bSettingRow = true;
        const scale = this.host.getScale();
        settingDom.style.left = x * scale + position.left + 'px';
        settingDom.style.top = (y - position.top) * scale + height + 'px';
        settingDom.style.width = (xLimit - x) * scale + 'px';
        const className = settingDom.className;
        if (className.indexOf(' active') !== -1) {
            return;
        }
        settingDom.className = className + ' active';
    }

    private addEvents(): void {
        gEvent.addEvent(this.docId, gEventName.TableEvent, this.handleTableEvent);
        gEvent.addEvent(this.docId, gEventName.UnMounted, this.removeEvents);
        gEvent.addEvent(this.docId, gEventName.ContentChange, this.contentChange);
        gEvent.addEvent(this.docId, gEventName.TableMouseup, this.mouseup);
        gEvent.addEvent(this.docId, gEventName.TableShowtips, this.ShowTips);
        gEvent.addEvent(this.docId, gEventName.TableMousemove, this.mouseomove);
        gEvent.addEvent(this.docId, gEventName.Mousedown, this.mousedown);
        gEvent.addEvent(this.docId, gEventName.MoveCursor, this.cursorChange);
        gEvent.addEvent(this.docId, gEventName.Click, this.rowModifyClick);
        // gEvent.addEvent(this.docId, gEventName.TableMessageTip, this.setOverCellContentFlag);
    }

    private removeEvents = (): void => {
        gEvent.deleteEvent(this.docId, gEventName.TableEvent, this.handleTableEvent);
        gEvent.deleteEvent(this.docId, gEventName.ContentChange, this.contentChange);
        gEvent.deleteEvent(this.docId, gEventName.TableMouseup, this.mouseup);
        gEvent.deleteEvent(this.docId, gEventName.TableShowtips, this.ShowTips);
        gEvent.deleteEvent(this.docId, gEventName.TableMousemove, this.mouseomove);
        gEvent.deleteEvent(this.docId, gEventName.MoveCursor, this.cursorChange);
        gEvent.deleteEvent(this.docId, gEventName.Mousedown, this.mousedown);
        gEvent.deleteEvent(this.docId, gEventName.Click, this.rowModifyClick);
        this.clearDatas();
        // gEvent.deleteEvent(this.docId, gEventName.TableMessageTip, this.setOverCellContentFlag);
    }

    private ShowTips =  ({pageId, offsetX, offsetY}, event: any): void => {
        if (this._bTripAcitveDown || this.bCursorTripChange !== true) {
            if (this.bTripMouseEvent && !this.bCursorTripChange && !this._bTripAcitveDown) {
                return;
            }
            this._bTripAcitveDown = false;
        }
        this.bTripMouseEvent = false;
        this.bCursorTripChange = false;
     
        const currentTable = this.documentCore.getCurrentTable();
        if(!currentTable) {
            return;
        }

        let isRepeatOnBreak = false;
        if (typeof currentTable.getRepeatOnBreak === 'function') {
            isRepeatOnBreak = currentTable.getRepeatOnBreak();
        }

        if(!isRepeatOnBreak) {
            return;
        }
        const currentCell = this.documentCore.getCurrentCell();
        if(currentCell && currentCell.pagesCount === 2)
         {
            this.showRowtipsBtn();
         } else {
            this.hideRowtipsBtn();
         }
    }

    private mouseup = ({pageId, offsetX, offsetY}, event: any): void => {
        if (this._bActiveDown || this.bCusorChange !== true) {
            if (this.bMouseEvent && !this.bCusorChange && !this._bActiveDown) {
                return;
            }
            setTimeout(() => {
                this.showRowSettingBtn();
            }, 1);
            this._bActiveDown = false;
        }
        this.bMouseEvent = false;
        this.bCusorChange = false;
 
        if (!this.isRenderCellText()) {
            return;
        }
        this.curCell = this.documentCore.getTableCellByXY(offsetX, offsetY, pageId);
    }

    private mousedown = (event: any): void => {
        this.bMouseEvent = true;
        this.bTripMouseEvent = true; //add by tinyzhi
    }

    private mouseomove = (option: any, event: any): void => {
        if (!this.isRenderCellText()) {
            return;
        }
        const {pageId, offsetX, offsetY, selection} = option;
        const cell  = this.documentCore.getTableCellByXY(offsetX, offsetY, pageId);
        if (!cell) {
            this.moveCell = null;
            this.bTimerline = false;
            return;
        }

        this.renderCellText(option, cell, event);
        this.moveCell = cell;
    }

    private cellChange(cell: any): void {
        if (!cell) {
            this.curRow = null;
            this.curCell = null;
            this.hideRowSetting();
            this.hideRowtipsBtn(); //add by tinyzhi
            return;
        }

        if (cell === this.curCell) {
            return;
        }
        this.curCell = cell;
        const row = cell.row;
        this.curRow = row;
        this._bActiveDown = true;
        this._bTripAcitveDown = true; //add by tinyzhi
        if (!this.bMouseEvent) {
            this.showRowSettingBtn();
        }
        if (this.bTripMouseEvent !== undefined && !this.bTripMouseEvent) {
            this.showRowtipsBtn();
        }
    }

    private rowModifyClick = (event: any): void => {
        const target = event.target;
        const index = target.getAttribute('row-index');
        if (!index) {
            return;
        }

        const row = this.curRow;
        if (!row) {
            return;
        }
        const tableName = row.table.tableName;
        if (!this.documentCore.isRowSettingVisible(tableName)) {
            this.hideRowSetting();
            this.hideRowtipsBtn(); //add by tinyzhi
            message.warning('当前行(参考行)存在合并拆分，不能进行新增');
            return;
        }

        switch (index) {
            case '0': {
                const table = row.table;
                const count = table.getEnableRowNum();
                if (table.content.length - 2 === count) {
                    layer.confirm('当前会删除整个表格，是否进行删除')
                    .then(() => {
                        this.documentCore.deleteTableByName(tableName);
                        this.host.handleRefresh();
                    });
                } else {
                    layer.confirm('是否删除光标所在位置的行')
                    .then(() => {
                        const bDelete = table.isTableCanDeleteRow();
                        table.setCanDeleteRow(true);
                        this.documentCore.removeTableRow(row.index);
                        table.setCanDeleteRow(bDelete);
                        this.host.handleRefresh();
                    });
                }

                break;
            }
            case '1': {
                const res = this.documentCore.addActionRow(tableName);
                if (typeof res === 'string') {
                    const bSetting = this.documentCore.isRowSettingVisible(tableName);
                    if (!bSetting) {
                        this.hideRowSetting();
                    }
                    this.host.handleRefresh();
                    if (typeof this.host.externalEvent?.nsoRowInsertedByTableAction === 'function') {
                        this.host.externalEvent.nsoRowInsertedByTableAction(res);
                    }
                }

                break;
            }
        }
    }

    private hideRowtipsBtn(): void {
        if (!this.bSettingRowTrips) {
            return;
        }
        this.rowTripsCell = null;
        const dom = this.rowTripsDom;
        this.bSettingRowTrips = false;
        dom.className = dom.className.replace(' active', '');
    }

    private hideRowSetting(): void {
        if (!this.bSettingRow) {
            return;
        }
        this.rowSettingCell = null;
        const dom = this.rowSettingDom;
        this.bSettingRow = false;
        dom.className = dom.className.replace(' active', '');
        dom.style.display = 'none';
    }

    private renderCellText(option: any, cell: any, e: any): void {
        const {pageId, offsetX, offsetY, selection} = option;
        if (selection.bUse || this.curCell === cell) {
            this.bTimerline = false;
            return;
        }

        if (this.moveCell === cell) {
            if (this.bTimerline) {
                return;
            }
        } else {
            this.getMessageTip()
            .close();
        }

        if (!cell.isOverCellBorder(pageId)) {
            return;
        }

        if (this.bTimerline) {
            clearTimeout(this.timer);
            this.timer = setTimeout(() => {
                this.bTimerline = false;
            }, 5000);
        }

        this.bTimerline = true;
        // console.log()
        const text = cell.content.getSelectTextNoHidden(true);
        if (!text) {
            return;
        }

        const messageTip = this.getMessageTip();
        messageTip.open(text, e.clientX, e.clientY);
    }

    private getMessageTip(): MessageTip {
        let messageTip = this.messageTip;
        if (!messageTip) {
            messageTip = this.messageTip = new MessageTip(this.host, 'cell-message-tip ');
        }
        return messageTip;
    }

    private cursorChange = (position: {x: number, y1: number, y2: number, pageNum: number}): void => {
        this.bCusorChange = true;
        this.bCursorTripChange = true; // add by tinyzhi

        const curCell = this.documentCore.getCurrentCell();
        const pageNum = position ? position.pageNum : this.documentCore.getCursorPosition().pageNum;
        if (pageNum !== this.cursorPageNum) {
            this.hideRowSetting();
            this.cursorPageNum = pageNum;
            this.hideRowtipsBtn(); //add by tinyzhi
        }
        this.cellChange(curCell);
        if (!this.isRenderCellText()) {
            return;
        }
        if (this.bMouseEvent) {
            this.bMouseEvent = false;
            return;
        }
        if (this.bTripMouseEvent) {
           this.bTripMouseEvent = false;
           return;
        }
        const keyEvent = this.host.getKeyEvent();
        if (keyEvent) {
            const code = keyEvent.keyCode;
            // 这些都会触发单元格变化
            const codes = [9, 33, 34, 37, 38, 39, 40];
            if (!codes.includes(code)) {
                return;
            }
        }
    }

    private isRenderCellText(): boolean {
        if (this.documentCore.isShelteredText() === false) {
            return false;
        }

        return true;
    }

    private handleTableEvent = (type: MessageType, ...arr: []) => {
        switch (type) {
          case MessageType.DeleteTableCells: {
         //    this.setState({tablePopMenuModalType: TableMenuModalType.DeleteTableCells});
             gEvent.setEvent(this.docId, gEventName.DialogEvent, MenuItemIndex.DeleteRow);
             break;
          }

          case MessageType.SplitTableCells: {
             // gEvent.setEvent(this.docId, gEventName.DialogEvent, MenuItemIndex.SplitCell);
             message.error(arr.toString());
             break;
          }

          case MessageType.AddTableRow: {
             // gEvent.setEvent(this.docId, gEventName.DialogEvent, MenuItemIndex.SplitCell);
             message.error(arr.toString());
             break;
          }

          case MessageType.AddTableColumn: {
             // gEvent.setEvent(this.docId, gEventName.DialogEvent, MenuItemIndex.SplitCell);
             message.error(arr.toString());
             break;
          }

          case MessageType.PasteTable: {
             // gEvent.setEvent(this.docId, gEventName.DialogEvent, MenuItemIndex.SplitCell);
             message.error(arr.toString());
             break;
          }

          default:
            break;
        }
     }
}
