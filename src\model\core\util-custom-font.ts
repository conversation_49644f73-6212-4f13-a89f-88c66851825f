import { fonts } from '../../components/editor/text/font';
import opentype from 'opentype.js';
import { TsIndexDb } from '../../common/indexDB/TsIndexDb';
import { FONT_MAPPING } from '@/common/commonDefines';


type FontCache = Record<string, { width: number; height: number; }>;
interface ICustomFont {
    cache: FontCache | null;
    family: string;
    fontName: string;
    path: string;
    version: number;
}
// 自定义字体的测距缓存对象
// tslint:disable-next-line: sy-global-const-name
export const customFontCache = new Map<string, ICustomFont>();
/**
 * fontName 和 Family 的反向映射  
 * { family: string, fontName: string}
 */
export const CustomFontReverseMap = new Map<string, string>();
const customFontTable = 'customFont';
// indexdb instance
const indexDbHelper = new TsIndexDb({dbName: 'hzEditor', version: 2, tables: [
    {
        tableName: customFontTable,
        option: {
            keyPath: 'fontName'
        },
        indexs: [{
            key: 'fontName',
            option: {
                unique: true,
            }
        }, {
            key: 'cache',
        }, {
            key: 'family',
        }, {
            key: 'path',
        }, {
            key: 'version'
        }],
    }
]});

async function loadIndexDbCache() {
    try {
        // 加载indexdb中的字体信息
        const db = await indexDbHelper.openDb();
        const fontCaches = await db.queryAll<ICustomFont>({tableName: customFontTable});
        for (const fontCache of fontCaches) {
            customFontCache.set(fontCache.fontName, fontCache);
        }
        await db.closeDb();
    } catch (err) {
        console.error('indexDB操作失败:', err);
    }
}

interface IFetchFontRes {
    dels: string[];
    inss: string[];
    upds: string[];
}

async function checkCustomFontExists(): Promise<boolean> {
    try {
        let url = '/customFont';
        const res = await fetch(url, { method: 'OPTIONS' });
        return res.status !== 404;
    } catch (err) {
        console.warn('Error checking customFont existence:', err);
        return false;
    }
}

async function fetchCustomFonts(): Promise<IFetchFontRes | null> {

    // 检查 customFont 是否存在
    const customFontExists = await checkCustomFontExists();
    if (!customFontExists) {
        // 用户没有提供 /customFont，返回空结果集
        return {
            dels: [],
            inss: [],
            upds: [],
        };
    }
    
    // 网络请求加载服务端字体信息
    try {
        let url = '/customFont';
        const res = await fetch(url, {method: 'get'});

        if (res.status === 404) {
            // 用户没有提供 /customFont，返回空结果集
            return {
                dels: [],
                inss: [],
                upds: [],
            };
        }

        if (res.status !== 200) {
            return null;
        }
        
        const resourceList: {
            fontName: string;
            path: string;
            version: number;
        }[] = await res.json();
        const needDelCaches: string[] = [];
        const needInsCaches: string[] = [];
        const needUpdCaches: string[] = [];
        // 更新字体信息 (删除/更新)
        if (Array.isArray(resourceList)) {
            const nameSet = new Set<string>();
            for (const item of resourceList) {
                nameSet.add(item.fontName);
                if (customFontCache.has(item.fontName)) {
                    // 内容无变更，继续使用
                    const old = customFontCache.get(item.fontName);
                    if (old.version >= item.version) {
                        continue;
                    }
                    old.cache = null;
                    old.version = item.version;
                    old.path = item.path;
                    old.family = '';
                    needUpdCaches.push(item.fontName);
                } else {
                    customFontCache.set(item.fontName, {
                        cache: null,
                        family: '',
                        fontName: item.fontName,
                        path: item.path,
                        version: item.version,
                    });
                    needInsCaches.push(item.fontName);
                }
            }
            
            for (const key of [...customFontCache.keys()]) {
                if (!nameSet.has(key)) {
                    needDelCaches.push(key);
                    customFontCache.delete(key);
                }
            }
        } else {
            needDelCaches.push(...customFontCache.keys());
            customFontCache.clear();
        }
        return {
            dels: needDelCaches,
            inss: needInsCaches,
            upds: needUpdCaches,
        };

    } catch (err) {
        console.warn('error:', err);
        // 处理所有其他错误
        return {
            dels: [],
            inss: [],
            upds: [],
        };
    }
}

// 初始化加载
// (async function initCustomFont() {
//     // load font caches from IndexDB
//     console.log("initCustomFont is beginning");
//     await loadIndexDbCache();
//     // fetch font caches from URL, update IndexDB and customFontCache
//     const fetchRes = await fetchCustomFonts();
//     if (!fetchRes) {
//         return;
//     }
//     // add font-face and measure text
//     const needDelCaches = await updateFontFace(Array.from(customFontCache.values()));
//     if (needDelCaches) {
//         fetchRes.dels.push(...needDelCaches);
//     }
//     if (customFontCache.size) {
//         // update fonts in editor
//         const families = fonts.find(item => item.key === 'fontFamily');
//         const options = families.options as any[];
//         for (const item of customFontCache.values()) {
//             options?.push({ name: item.fontName, value: item.family, macValue: item.family });
//             CustomFontReverseMap.set(item.family, item.fontName);
//             // 更新mapping
//             updateFontMapping(item);
//         }
//     }
//     await updateLocalFontCache(fetchRes)
// })();

// 导出 initCustomFont 函数  从自启函数修正为显式调用
export async function initCustomFont(bNeed:boolean) {  
    // 从 IndexDB 加载字体缓存  
    await loadIndexDbCache();  
    if(!bNeed) return;
    // 从 URL 获取字体缓存，更新 IndexDB 和 customFontCache  
    const fetchRes = await fetchCustomFonts();  
    if (!fetchRes) {  
        return;  
    }  
  
    // 添加 font-face 并测量文本  
    const needDelCaches = await updateFontFace(Array.from(customFontCache.values()));  
    if (needDelCaches) {  
        fetchRes.dels.push(...needDelCaches);  
    }  
  
    if (customFontCache.size) {  
        // 更新编辑器中的字体  
        const families = fonts.find(item => item.key === 'fontFamily');  
        const options = families.options as any[];  
        for (const item of customFontCache.values()) {  
            options?.push({ name: item.fontName, value: item.family, macValue: item.family });  
            CustomFontReverseMap.set(item.family, item.fontName);  
            // 更新 mapping  
            updateFontMapping(item);  
        }  
    }  
  
    // 更新本地字体缓存  
    await updateLocalFontCache(fetchRes);  
}
 
/** 向html中更新字体font */
async function updateFontFace(fontList: ICustomFont[]) {
    // add font-face
    const fontSets = document.fonts;
    const needDelCaches: string[] = [];
    const prs = [];
    for (const item of fontList) {
        const urlStr = `url(${item.path})`;

        prs.push(new Promise(resolve => {
            updateMeasureCache(item).then((res: any) => {
                if (res) {
                    // 更新缓存
                    customFontCache.set(res.fontName, res);
                    // 添加fontface节点
                    const fontFace = new FontFace(res.family, urlStr, { display: 'swap' });
                    fontSets.add(fontFace);
                    fontFace.load();
                } else {
                    // 解析失败，将当前缓存去除
                    customFontCache.delete(item.fontName);
                    needDelCaches.push(item.fontName);
                    console.warn(`字体【${item.fontName}】解析失败，未成功添加, path: "${item.path}"`);
                }
            }).catch(() => {
                // fontface加载失败
                customFontCache.delete(item.fontName);
                needDelCaches.push(item.fontName);
                console.warn(`fontface字体【${item.fontName}】加载失败, path: "${item.path}"`);
            }).finally(() => {
                resolve(null);
            });
        }));
    }
    await Promise.all(prs);
    return needDelCaches;
}

/**
 * 更新字体缓存，及indexDB
 * @param needUpInsCaches 需要更新的字体map
 * @param needDelCaches 需要删除的字体名称集合
 */
async function updateLocalFontCache(fetchRes: IFetchFontRes) {
    const { dels, inss, upds } = fetchRes;
    try {
        const db = await indexDbHelper.openDb();
        if (dels?.length) {
            const nameSet = new Set(dels);
            await db.delete({
                tableName: customFontTable,
                condition: (data: ICustomFont) => nameSet.has(data.fontName),
            });
        }
        if (upds?.length) {
            const nameSet = new Set(upds);
            await db.update({
                tableName: customFontTable,
                condition: (data: ICustomFont) => nameSet.has(data.fontName),
                handle: (data: ICustomFont) => customFontCache.get(data.fontName)
            });
        }
        if (inss?.length) {
            const fontList: ICustomFont[] = [];
            for (const key of inss) {
                const item = customFontCache.get(key);
                if (item && item.cache) {
                    fontList.push(item);
                }
            }
            await db.insert({
                tableName: customFontTable,
                data: fontList,
            });
        }
        await db.closeDb();
    } catch (err) {
        console.error('indexDB操作失败:', err);
    }
}

/**
 * 切割字体名，返回后缀和名称
 * @param fileName 字体名
 * @returns [ext, name]
 */
export function getCustomFontExt(fileName: string): [string, string] {
    const index = fileName.lastIndexOf('.');
    if (index === -1) {
        return ['', fileName];
    }
    return [fileName.substring(index + 1), fileName.substring(0, index)];
}

/**
 * 获取字符串格式的字体@font-face
 * @param needUrl 是否请求网络资源
 */
export function getCustomFontFaceStyleHtml(needUrl = true): string {
    let str = '';
    const fontList = Array.from(customFontCache.values());
    if (fontList.length) {
        for (const item of fontList) {
            const ext = getCustomFontExt(item.path)[0].toLowerCase();
            let srcStr = `local('${item.family}')`;
            if (needUrl) {
                srcStr += `,
                url(${item.path}) format(${ext === 'ttf' ? 'truetype' : 'opentype'});`;
            } else {
                srcStr += ';';
            }
            str += `
            @font-face {
                font-family: '${item.family}';
                src: ${srcStr}
            }`;
        }
    }
    return str;
}

/** 更新字体Mapping */
function updateFontMapping({ fontName, family }: ICustomFont) {
    if (!FONT_MAPPING[fontName]) {
        FONT_MAPPING[fontName] = {
            windows: family,
            mac: family,
        };
    }
}

/** 判断字体名是否存在 */
export function existsCustomFontName(fontName: string) {
    const families = fonts.find(item => item.key === 'fontFamily');
    if (families && (families.options as { name: string }[]).find(item => item.name === fontName)) {
        return true;
    }
    return customFontCache.has(fontName);
}

/**
 * 读取字体文件
 * @param fontName 预设的文件名
 * @param file 文件
 */
export async function updateMeasureCache(fontItem: ICustomFont): Promise<ICustomFont | boolean> {
    if (fontItem.cache) {
        return fontItem;
    }
    let font: opentype.Font;
    try {
        font = await opentype.load(fontItem.path);
    } catch {
        // 字体解析失败，不支持字体
        return false;
    }
    
    const fontFamily = font.names?.fontFamily?.en || 'no name';
    const strs = '!"#$%&\'()*+,-./0123456789:;<=>?@ABCDEFGHIJKLMNOPQRSTUVWXYZ[\\]^_`abcdefghijklmnopqrstuvwxyz{|}~↵我，。；：:’“？、（）【】{}☑☐⊙⭘◯℃↓';
    const height = (font.ascender - font.descender) / font.unitsPerEm;
    const fontCache: any = {};
    for (const char of strs) {
        if (font.charToGlyphIndex(char)) {
            fontCache[char] = { width: font.getAdvanceWidth(char, 1), height };
        }
    }
    // 更新测距信息
    return {
        cache: fontCache,
        fontName: fontItem.fontName,
        family: fontFamily,
        path: fontItem.path,
        version: fontItem.version
    };
}

