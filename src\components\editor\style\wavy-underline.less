// 波浪线提示框样式 - 主流蓝色风格
.wavy-underline-tooltip {
    display: none;
    position: fixed;
    z-index: 1000;
    width: 240px;
    min-width: 120px;
    max-width: 300px;
    padding: 12px 16px;
    font-size: 13px;
    color: #333;
    background: #fff;
    border-radius: 8px;
    border: 1px solid #e5e6eb;
    box-shadow: 0 4px 12px rgba(0,0,0,0.10);
    word-wrap: break-word;
    opacity: 0;
    transition: opacity 0.2s ease-in-out;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    border-left: 3px solid #1677ff;

    &.visible {
        display: block;
        opacity: 0;
        user-select: none;
    }

    &.active {
        display: block;
        opacity: 1;
    }

    .wavy-tooltip-header {
        padding-bottom: 6px;
        border-bottom: 1px solid #f0f2f5;
        margin-bottom: 8px;
        min-height: 18px;
        line-height: 18px;

        .wavy-tooltip-type {
            font-weight: 600;
            font-size: 13px;
            float: left;
            color: #1677ff;
        }
    }

    .wavy-tooltip-content {
        line-height: 1.5;
        font-size: 13px;
        color: #333;
        margin: 8px 0;
        word-break: break-all;
    }
}

.wavy-underline-tooltip.grammar,
.wavy-underline-tooltip.spellcheck,
.wavy-underline-tooltip.suggestion,
.wavy-underline-tooltip.warning,
.wavy-underline-tooltip.custom {
    border-left-color: #1677ff;
}

.wavy-tooltip-type.grammar,
.wavy-tooltip-type.spellcheck,
.wavy-tooltip-type.suggestion,
.wavy-tooltip-type.warning,
.wavy-tooltip-type.custom {
    color: #1677ff;
}

// 波浪线激活状态样式
.wavy-underline-active {
    background-color: rgba(22, 119, 255, 0.08) !important;
}