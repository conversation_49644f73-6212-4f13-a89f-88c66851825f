import { XmlComponent, XmlAttributeComponent } from '../xml-components';
export interface IMediaAttributesProperties {
    readonly w?: string;
}
class MediaAttributes extends XmlAttributeComponent<IMediaAttributesProperties> {
    protected readonly xmlKeys: any = {
        w: 'xmlns:w',
    };
}
export class Cascade extends XmlComponent {

    constructor() {
        super('Cascade');
        this.root.push(
            new MediaAttributes({
                w: 'http://schemas.openxmlformats.org/wordprocessingml/2006/main',
            })
        );
    }

    public addCascadeInfos(info: any): void {
        this.root.push(new CascadeInfo(info));
    }
}

// tslint:disable-next-line: max-classes-per-file
class CascadeInfo extends XmlComponent {
    constructor(cascadeInfo: any) {
        super('w:cascadeInfo');
        if (cascadeInfo && cascadeInfo.length > 0) {
            this.root.push(JSON.stringify(cascadeInfo));
        }
    }
}
