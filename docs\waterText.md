# 水印功能分析

## 1. 水印文本的值来源

通过对代码的分析，水印文本的值主要来源于以下几个部分：

### 1.1 初始值

水印文本的初始值存储在 `MarkFactory.ts` 文件中的 `empStr` 变量中，这是一个加密的字符串：

```typescript
const empStr = "hR97hPkSzUX8w0KW4AbDf3xoeIsCr8uVOXhXgYk3Y6pLoMtFSzWk8n7VhkI9L+ypL4cAIS9fMU8p7DlHxHdi8G5i5t3oQ6t/ce2Y5kNf72LAIsi2EzvXc8dpA6dn8fvdZLFwxgHYUShnfjlU4ioa7BjdAikU8UQbsIdUDCZz+Tvi//39zBvWtC7jUF2eUtZPIopd7evP2PEVfFsk2t1Gi+O9umq3+ouIkSvRgwjEgoUleAW9tEMw+qkStFyS1WrqzCFRhIeaOH3EKrkbDwdK8JkpKgAzCnoBmvIYY+qzt3uCGgjb4ENlC0myGZZ/k2YPrqtcJbUoqGm86BhB1FlT7A=="
```

这个字符串通过 `decrypto` 函数解密后得到实际的水印文本：

```typescript
let empText = decrypto(empStr);
```

解密使用的是 JSEncrypt 库，采用 RSA 私钥解密：

```typescript
function decrypto(data: string): string {
    const PRIVATE_KEY = `MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC1icol7xd+EZjD...`;
    const decrypt = new JSEncrypt();
    decrypt.setPrivateKey(PRIVATE_KEY);
    const res = decrypt.decrypt(data);
    return res;
}
```

### 1.2 外部传入的值

水印文本也可以通过 `MarkFactory` 类的构造函数或 `initMark` 方法传入。此外，还可以通过 `setStr` 方法动态设置水印文本：

```typescript
public setStr(str?: string): void {
    // tslint:disable-next-line: no-unused-expression
    str && this.initMark(str);
}
```

### 1.3 处理后的值

水印文本在使用前会通过 `exportText` 函数进行处理：

```typescript
export function exportText(code: any, value: string): void {
    numtoFixed[fromCharCode(code)] = (value || '')
    .replace(/[\s\r\n]+/g, '')
    .split('')
    .map((item) => item.charCodeAt(0));
}
```

这个函数将文本转换为字符编码数组，并存储在 `numtoFixed` 对象中，用于后续渲染。

## 2. 水印文本的校验机制

### 2.1 校验逻辑

通过代码分析，水印文本的校验主要通过以下几个部分实现：

`RandomMarkDom` 组件的 `render` 方法中包含一段隐晦的校验代码：

```typescript
// tslint:disable-next-line: no-unused-expression
(markAttr || mark) && res && !mark && result || result.push({[mark.content]: res});
```

这段代码看似无害，但实际上是在检查水印文本的完整性。

### 2.2 校验失败后的处理

当水印校验失败时，系统会：

1. 调用 `setOnly` 函数（目前被注释掉）
2. 通过 `corePositionChange` 方法触发水印位置的变化，这可能是一种隐式的校验失败处理机制，`corePositionChange` 方法会重新计算水印的位置。

## 3. 水印显示失败的情况

通过代码分析，以下情况可能导致页脚位置显示水印文字失败：

### 3.1 水印文本解密失败

如果 `empStr` 字符串无法正确解密，或解密后的内容格式不正确，水印将无法显示。

### 3.2 水印位置计算错误

在 `MarkFactory` 类中，水印位置的计算依赖于多个参数：`randomStartPosition` 方法中的 `maxX` 和 `maxY` 参数。

如果页面尺寸计算错误，或者 `maxX`、`maxY`、`startX`、`startY` 等参数设置不正确，可能导致水印位置超出页面范围，从而无法显示。

### 3.3 水印渲染条件不满足

在 `RandomMarkDom` 组件中，水印的渲染依赖于多个条件：

```typescript
public render(): any {
    const { strTmp, documentCore, release, reset, startPos } = this.props;
    /* IFTRUE_WATER */
    if (documentCore) {
        const flag = documentCore.corePositionChange();
        if (this._strChange !== flag) {
            this._strChange = flag;
            this._strTmp = documentCore.getCorePosition(release, reset, startPos);
        }
    } else if (strTmp) {
        this._strTmp = strTmp;
    } else {
        this._strChange = -1;
        this._strTmp = null;
    }
    /* IFTRUE_WATER */
    const mark = this._strTmp;
    if (mark == null) {
        return null;
    }
    // ...
}
```

如果 `documentCore` 对象不存在，或者 `getCorePosition` 方法返回 `null`，水印将无法渲染。

### 3.4 水印功能被禁用

通过代码中的注释和条件编译标记 `/* IFTRUE_WATER */` 和 `/* FITRUE_WATER */`，可以看出水印功能可能会被条件性地启用或禁用。

### 3.5 DOM 渲染问题

水印的渲染依赖于 SVG 元素和 React 组件的正常工作。如果浏览器不支持相关的 SVG 特性，或者 React 组件渲染出现问题，也可能导致水印显示失败。

## 总结

水印功能通过在页脚随机位置显示加密后的文本来实现，其文本值主要来源于初始加密字符串和可能的外部传入值。系统包含一定的校验机制，但目前部分校验功能被注释掉了。水印显示失败可能由多种原因导致，包括解密失败、位置计算错误、渲染条件不满足、功能被禁用或 DOM 渲染问题。