import { XmlComponent, XmlAttributeComponent } from '../../xml-components';

import {
    // GridSpan,
    // TableCellBorders,
    // TableCellWidth,
    // VMerge,
    CellFormula,
    CellVertAlign,
    GridSpan,
    TableCellBorders,
    VMerge,
    // WidthType,
} from './table-cell-components';
import { INISProperty, ITableCellBorder, NISTableCellType } from '../../../../../common/commonDefines';
import { TableWidthType } from '../../../../../model/core/TableProperty';
import { TcNis } from './nis-table-cell-properties-tcNis';

export class NISTableCellProperties extends XmlComponent {
    private readonly cellBorder: TableCellBorders;
    private tcNis: TcNis;

    constructor() {
        super('w:tcPr');
        this.cellBorder = new TableCellBorders();
        this.root.push(this.cellBorder);
    }

    public get Borders(): TableCellBorders {
        return this.cellBorder;
    }

    public addGridSpan(cellSpan: number): NISTableCellProperties {
        this.root.push(new GridSpan(cellSpan));

        return this;
    }

    public addVMerge(type: number): NISTableCellProperties {
        this.root.push(new VMerge(type));

        return this;
    }

    public addCellWidth(cellWidth: INISCellWidthProperties): NISTableCellProperties {
        this.root.push(new NISCellWidth(cellWidth));
        return this;
    }

    // public setWidth(width: string | number, type: WidthType): TableCellProperties {
    //     this.root.push(new TableCellWidth(width, type));

    //     return this;
    // }

    public addTopBorder(border: ITableCellBorder): NISTableCellProperties {
        this.root.push(new NISTopBorder(border));
        return this;
    }

    public addRightBorder(border: ITableCellBorder): NISTableCellProperties {
        this.root.push(new NISRightBorder(border));
        return this;
    }

    public addBottomBorder(border: ITableCellBorder): NISTableCellProperties {
        this.root.push(new NISBottomBorder(border));
        return this;
    }

    public addLeftBorder(border: ITableCellBorder): NISTableCellProperties {
        this.root.push(new NISLeftBorder(border));
        return this;
    }

    public addCellProtected(val: string): NISTableCellProperties {
        this.root.push(new NISCellProtect(val));
        return this;
    }

    public addCellFormula(val: string): NISTableCellProperties {
        this.root.push(new CellFormula(val));
        return this;
    }

    public addCellVertAlign(val: number): NISTableCellProperties {
        this.root.push(new CellVertAlign(val));
        return this;
    }

    public addTcNis(nisProperty: INISProperty): NISTableCellProperties {

        const attrs = {type: NISTableCellType.Text};
        if (nisProperty != null) {
            attrs.type = nisProperty.type;
        }
        this.tcNis = new TcNis(attrs);

        // also add sub contents for each type
        this.tcNis.addContent(nisProperty);

        this.root.push(this.tcNis);
        return this;
    }

}

export interface INISCellWidthProperties {
    width: number;
    type: TableWidthType;
}

class NISCellWidthAttributes extends XmlAttributeComponent<INISCellWidthProperties> {
    protected xmlKeys: any = {
        width: 'w:w',
        type: 'w:type',
    };
}

// tslint:disable-next-line: max-classes-per-file
export class NISCellWidth extends XmlComponent {
    constructor(attrs: INISCellWidthProperties) {
        super('w:tcW');
        this.root.push(new NISCellWidthAttributes(attrs));
    }
}

// export interface IBorderProperties {
//     val: TableBorderLineStyle;
//     size: number;
//     color: string;
// }

// tslint:disable-next-line: max-classes-per-file
class NISBorderAttributes extends XmlAttributeComponent<ITableCellBorder> {
    protected xmlKeys: any = {
        val: 'w:val',
        size: 'w:sz',
        color: 'w:color',
    };
}

// tslint:disable-next-line: max-classes-per-file
export class NISTopBorder extends XmlComponent {
    constructor(attrs: ITableCellBorder) {
        super('w:top');
        this.root.push(new NISBorderAttributes(attrs));
    }
}

// tslint:disable-next-line: max-classes-per-file
export class NISRightBorder extends XmlComponent {
    constructor(attrs: ITableCellBorder) {
        super('w:right');
        this.root.push(new NISBorderAttributes(attrs));
    }
}

// tslint:disable-next-line: max-classes-per-file
export class NISBottomBorder extends XmlComponent {
    constructor(attrs: ITableCellBorder) {
        super('w:bottom');
        this.root.push(new NISBorderAttributes(attrs));
    }
}

// tslint:disable-next-line: max-classes-per-file
export class NISLeftBorder extends XmlComponent {
    constructor(attrs: ITableCellBorder) {
        super('w:left');
        this.root.push(new NISBorderAttributes(attrs));
    }
}

// tslint:disable-next-line: max-classes-per-file
export class NISCellProtect extends XmlComponent {
    constructor(flag: string) {
        super('w:protected');
        this.root.push(flag);
    }
}
