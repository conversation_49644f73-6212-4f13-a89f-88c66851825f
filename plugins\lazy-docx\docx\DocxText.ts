import { ISerialPageNumObj, ISerialTextObj, PageNumType, SerialObjType } from '../../serialize/serialInterface';
import { IRunOptions, PageNumber, ParagraphChild, TextRun } from 'docx';
import { IAbstractDocx, IDocx } from './IDocx';

export default class DocxText extends IAbstractDocx implements IDocx {

    public static demo(): TextRun {
        const option: IRunOptions = {
            text: '测试文本',
            color: '000000',
            font: '宋体',
            size: 16,
        };
        return new TextRun(option);
    }

    constructor(private readonly textObj: ISerialTextObj | ISerialPageNumObj) { super(); }

    public buildTo(collector: ParagraphChild[] = []): ParagraphChild[] {

        if (this.textObj.type === SerialObjType.Text) {
            this.buildTextRunTo(this.textObj as ISerialTextObj, collector);
        } else if (this.textObj.type === SerialObjType.PageNum) {
            this.buildPageNumTo(this.textObj as ISerialPageNumObj, collector);
        }
        return collector;
    }

    /** 创建页码文本 */
    private buildPageNumTo(obj: ISerialPageNumObj, collector: ParagraphChild[] = []): ParagraphChild[] {
        const children = [];
        switch (obj.pageNumType) {
            case PageNumType.CurPage: {
                children.push(PageNumber.CURRENT);
                break;
            }
            case PageNumType.TotalPages: {
                children.push(PageNumber.TOTAL_PAGES);
                break;
            }
            case PageNumType.PageNumString: {
                if (obj.pageNumString) {
                    const [numBefore, numAfter] = obj.pageNumString.split('[页码]');
                    children.push(numBefore);
                    if (numAfter) {
                        const [totalBefore, totalAfter] = numAfter.split('[页数]');
                        children.push(PageNumber.CURRENT, totalBefore);
                        totalAfter && children.push(PageNumber.TOTAL_PAGES, totalAfter);
                    }
                }
                break;
            }
        }
        children.length && collector.push(new TextRun({children}));
        return collector;
    }

    private buildTextRunTo(obj: ISerialTextObj, collector: ParagraphChild[] = []): ParagraphChild[] {
        const option = Object.assign({}, obj);
        this.deleteUnUsefulProp(option, 'type');
        collector.push(new TextRun(option));
        return collector;
    }

}
