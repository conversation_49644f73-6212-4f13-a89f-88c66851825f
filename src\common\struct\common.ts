import { ALIGN_TYPE, DateBoxFormat, NewControlContentSecretType,
    NewControlType, SignatureCountType } from '../commonDefines';

// 读写入xml使用
export enum StructXmlName {
    Type = 'type',
    Name = 'name',
    Sdtcontent = 'w:sdtcontent',
    Secontent = 'w:secontent',
    SerialNumber = 'serialNumber',
    Identifier = 'identifier',
    Placeholder = 'placeholder',
    CodeLabel = 'codeLabel',
    ValueLabel = 'valueLabel',
    IsMustFill = 'isMustFill',
    DeleteProtect = 'deleteProtect',
    EditProtect = 'editProtect',
    CopyProtect = 'copyProtect',
    ShowBorder = 'showBorder',
    BTextBorder = 'bTextBorder',
    BShowCodeAndValue = 'bShowCodeAndValue',
    BorderString = 'borderString',
    LogicEvent = 'logicEvent',
    EditReverse = 'editReverse',
    BackgroundColorHidden = 'backgroundColorHidden',
    CustomProperty = 'customProperty',
    TabJump = 'tabJump',
    NewControlHidden = 'newControlHidden',
    ListItems = 'listItems',
    HelpTip = 'helpTip',
    ShowPlaceholder = 'showPlaceholder',
    SecretType = 'secretType',
    FixedLength = 'fixedLength',
    InterAlign = 'interAlign',
    EventInfo = 'eventInfo',
    MaxLength = 'maxLength',
    Title = 'title',
    ShowTitle = 'showTitle',
    HideHasTitle = 'hideHasTitle',
    MinValue = 'minValue',
    MaxValue = 'maxValue',
    Precision = 'precision',
    Unit = 'unit',
    ForceValidate = 'forceValidate',
    Retrieve = 'retrieve',
    SelectPrefixContent = 'selectPrefixContent',
    PrefixContent = 'prefixContent',
    Separator = 'separator',
    ShowValue = 'showValue',
    DateType = 'dateType',
    CustomDateFormat = 'customDateFormat',
    StartDate = 'startDate',
    EndDate = 'endDate',
    DateTime = 'dateTime',
    ShowRight = 'showRight',
    Checked = 'checked',
    PrintSelected = 'printSelected',
    Label = 'label',
    LabelCode = 'labelCode',
    Group = 'group',
    ShowType = 'showType',
    SpaceNum = 'spaceNum',
    SupportMultLines = 'supportMultLines',
    SignatureCount = 'signatureCount',
    PreText = 'preText',
    SignatureSeparator = 'signatureSeparator',
    PostText = 'postText',
    SignaturePlaceholder = 'signaturePlaceholder',
    SignatureRatio = 'signatureRatio',
    RowHeightRestriction = 'rowHeightRestriction',
    SignType = 'signType',
    AlwaysShow = 'alwaysShow',
    ShowSignBorder = 'showSignBorder',
    Hierarchy = 'hierarchy',
    Province = 'province',
    City = 'city',
    County = 'county',
    SourceBind = 'sourceBind',
    ItemTextColors = 'itemTextColors',
    RegExp = 'regExp',
}

export enum StructXmlProperty {
    Alignments = 'alignments',
    NewControlName = 'newControlName',
    NewControlSerialNumber = 'newControlSerialNumber',
    NewControlInfo = 'newControlInfo',
    NewControlPlaceHolder = 'newControlPlaceHolder',
    NewControlTitle = 'newControlTitle',
    NewControlType = 'newControlType',
    IsNewControlHidden = 'isNewControlHidden',
    IsNewControlCanntDelete = 'isNewControlCanntDelete',
    IsNewControlCanntEdit = 'isNewControlCanntEdit',
    IsNewControlCanntCopy = 'isNewControlCanntCopy',
    IsNewControlMustInput = 'isNewControlMustInput',
    IsNewControlShowBorder = 'isNewControlShowBorder',
    IsNewControlReverseEdit = 'isNewControlReverseEdit',
    IsNewControlHiddenBackground = 'isNewControlHiddenBackground',
    NewControlDisplayType = 'newControlDisplayType',
    NewControlFixedLength = 'newControlFixedLength',
    NewControlMaxLength = 'newControlMaxLength',
    CustomProperty = 'customProperty',
    IsShowBgColor = 'isShowBgColor',
    // text only
    HideHasTitle = 'hideHasTitle',

    NewControlItems = 'newControlItems',
    PrefixContent = 'prefixContent',
    SelectPrefixContent = 'selectPrefixContent',
    Separator = 'separator',
    IsShowValue = 'isShowValue',
    CodeLabel = 'codeLabel', // 自定义下拉名称显示值
    ValueLabel = 'valueLabel', // 自定义下拉值显示值

    MinValue = 'minValue',
    MaxValue = 'maxValue',
    Precision = 'precision', // 精度
    Unit = 'unit',
    ForceValidate = 'forceValidate',

    DateBoxFormat = 'dateBoxFormat',
    Retrieve = 'retrieve', // 是否进行索引检索
    Checked = 'checked',
    ShowRight = 'showRight',
    PrintSelected = 'printSelected',
    Label = 'label',
    SpaceNum = 'spaceNum',
    SupportMultLines = 'supportMultLines',
    ShowType = 'showType',
    ShowTitle = 'showTitle',
    TabJump = 'tabJump',
    BInsertFile = 'bInsertFile',
    BReadSign = 'bReadSign',
    CustomFormat = 'customFormat',
    BClearItems = 'bClearItems',
    StartDate = 'startDate',
    EndDate = 'endDate',
    DateTime = 'dateTime',
    SignatureCount = 'signatureCount',
    PreText = 'preText',
    SignatureSeparator = 'signatureSeparator',
    PostText = 'postText',
    SignaturePlaceholder = 'signaturePlaceholder',
    SignatureRatio = 'signatureRatio',
    RowHeightRestriction = 'rowHeightRestriction',

    SignType = 'signType',
    AlwaysShow = 'alwaysShow',
    ShowSignBorder = 'showSignBorder',
    EventInfo = 'eventInfo',
    Cascade = 'cascade',
    BTextBorder = 'bTextBorder',
    Hierarchy = 'hierarchy',
    Province = 'province',
    City = 'city',
    County = 'county',
    Group = 'group',
    BShowCodeAndValue = 'bShowCodeAndValue',
    Identifier = 'identifier',
    LabelCode = 'labelCode',
    ItemTextColors = 'itemTextColors',
    ShowPlaceholder = 'showPlaceholder',
    ExternalDataBind = 'externalDataBind',
}

export enum StructExportPrintType {
    Number,
    String,
    Boolean,
    Custom,
    List,
    Event,
    Align,
    CustomDate,
    County,
}

export interface IStructPropInfo {
    propName?: string;
    type: number;
}

const a = StructExportPrintType;
const b = StructXmlProperty;

// key: 写入xml的字段，假如不存在，需要在枚举内先新增
// propName：结构化元素属性名称（参考INewControlProperty），假如跟可以一样，那么可以省略
// type: 解析方法类型，假如为字符串时，可以省略
// 读取文档使用
export let readStructXmlName = {
    [StructXmlName.SerialNumber]: {propName: b.NewControlSerialNumber, type: null},
    [StructXmlName.Identifier]: {propName: null, type: null},
    [StructXmlName.Placeholder]: {propName: b.NewControlPlaceHolder, type: null},
    [StructXmlName.CodeLabel]: {propName: null, type: null},
    [StructXmlName.ValueLabel]: {propName: null, type: null},
    [StructXmlName.IsMustFill]: {propName: b.IsNewControlMustInput, type: a.Boolean},
    [StructXmlName.DeleteProtect]: {propName: b.IsNewControlCanntDelete, type: a.Boolean},
    [StructXmlName.EditProtect]: {propName: b.IsNewControlCanntEdit, type: a.Boolean},
    [StructXmlName.CopyProtect]: {propName: b.IsNewControlCanntCopy, type: a.Boolean},
    [StructXmlName.ShowBorder]: {propName: b.IsNewControlShowBorder, type: a.Boolean},
    [StructXmlName.BTextBorder]: {propName: null, type: a.Boolean},
    [StructXmlName.BShowCodeAndValue]: {propName: null, type: a.Boolean},
    [StructXmlName.BorderString]: {propName: null, type: null},
    [StructXmlName.LogicEvent]: {propName: b.Cascade, type: a.Event},
    [StructXmlName.EditReverse]: {propName: b.IsNewControlReverseEdit, type: a.Boolean},
    [StructXmlName.BackgroundColorHidden]: {propName: b.IsNewControlHiddenBackground, type: a.Boolean},
    [StructXmlName.CustomProperty]: {propName: null, type: a.Custom},
    [StructXmlName.TabJump]: {propName: null, type: a.Boolean},
    [StructXmlName.NewControlHidden]: {propName: b.IsNewControlHidden, type: a.Boolean},
    [StructXmlName.ListItems]: {propName: b.NewControlItems, type: a.List},
    [StructXmlName.HelpTip]: {propName: b.NewControlInfo, type: null},
    [StructXmlName.ShowPlaceholder]: {propName: null, type: a.Boolean},
    [StructXmlName.SecretType]: {propName: b.NewControlDisplayType, type: a.Number},
    [StructXmlName.FixedLength]: {propName: b.NewControlFixedLength, type: a.Number},
    [StructXmlName.InterAlign]: {propName: b.Alignments, type: a.Align},
    [StructXmlName.EventInfo]: {propName: null, type: a.Event},
    [StructXmlName.MaxLength]: {propName: b.NewControlMaxLength, type: a.Number},
    [StructXmlName.Title]: {propName: b.NewControlTitle, type: null},
    [StructXmlName.HideHasTitle]: {propName: null, type: a.Boolean},
    [StructXmlName.MinValue]: {propName: null, type: a.Number},
    [StructXmlName.MaxValue]: {propName: null, type: a.Number},
    [StructXmlName.Precision]: {propName: null, type: a.Number},
    [StructXmlName.Unit]: {propName: null, type: null},
    [StructXmlName.ForceValidate]: {propName: null, type: a.Boolean},
    [StructXmlName.Retrieve]: {propName: null, type: a.Boolean},
    [StructXmlName.SelectPrefixContent]: {propName: null, type: null},
    [StructXmlName.PrefixContent]: {propName: null, type: null},
    [StructXmlName.Separator]: {propName: null, type: null},
    [StructXmlName.ShowValue]: {propName: b.IsShowValue, type: a.Boolean},
    [StructXmlName.DateType]: {propName: b.DateBoxFormat, type: a.Number},
    [StructXmlName.CustomDateFormat]: {propName: b.CustomFormat, type: a.CustomDate},
    [StructXmlName.StartDate]: {propName: null, type: null},
    [StructXmlName.EndDate]: {propName: null, type: null},
    [StructXmlName.DateTime]: {propName: null, type: null},
    [StructXmlName.ShowRight]: {propName: null, type: a.Boolean},
    [StructXmlName.Checked]: {propName: null, type: a.Boolean},
    [StructXmlName.PrintSelected]: {propName: null, type: a.Boolean},
    [StructXmlName.Label]: {propName: null, type: null},
    [StructXmlName.LabelCode]: {propName: null, type: null},
    [StructXmlName.Group]: {propName: null, type: null},
    [StructXmlName.ShowType]: {propName: null, type: a.Number},
    [StructXmlName.SpaceNum]: {propName: null, type: a.Number},
    [StructXmlName.SignatureCount]: {propName: null, type: a.Number},
    [StructXmlName.PreText]: {propName: null, type: null},
    [StructXmlName.SignatureSeparator]: {propName: null, type: null},
    [StructXmlName.PostText]: {propName: null, type: null},
    [StructXmlName.SignaturePlaceholder]: {propName: null, type: null},
    [StructXmlName.SignatureRatio]: {propName: null, type: a.Number},
    [StructXmlName.RowHeightRestriction]: {propName: null, type: a.Boolean},
    [StructXmlName.SignType]: {propName: null, type: a.Number},
    [StructXmlName.AlwaysShow]: {propName: null, type: a.Number},
    [StructXmlName.ShowSignBorder]: {propName: null, type: a.Boolean},
    [StructXmlName.Hierarchy]: {propName: null, type: a.Number},
    [StructXmlName.Province]: {propName: null, type: a.County},
    [StructXmlName.City]: {propName: null, type: a.County},
    [StructXmlName.County]: {propName: null, type: a.County},
    [StructXmlName.SourceBind]: {propName: b.ExternalDataBind, type: a.County},
    [StructXmlName.ItemTextColors]: {propName: b.ItemTextColors, type: a.County},
    [StructXmlName.RegExp]: {},
    [StructXmlName.SupportMultLines]: {propName: b.SupportMultLines, type: a.Boolean},
};

const c = StructXmlName;

// key: 结构化元素的属性字段(不存在时，需要在枚举先新增一个)，propName: 写入xml的字段（假如跟key一样可省略），
// type: 解释调用的方法，假如都不合适需要新增，然后在write文件新增方法（字符串可省略）
// 有默认值请维护对象STUCT_DEFAULT
// 结构化元素写入文档只需要维护这里
export let writeStructXmlName = {
    [b.Alignments]: {propName: c.InterAlign, type: a.Align},
    [b.NewControlName]: {propName: c.Name, type:  null},
    [b.NewControlSerialNumber]: {propName: c.SerialNumber, type:  null},
    [b.NewControlInfo]: {propName: c.HelpTip, type:  null},
    [b.NewControlPlaceHolder]: {propName: c.Placeholder, type:  null},
    [b.NewControlTitle]: {propName: c.Title, type:  null},
    [c.RegExp]: {},
    [b.NewControlType]: {propName: c.Type, type:  a.Number},
    [b.IsNewControlHidden]: {propName: c.NewControlHidden, type:  a.Boolean},
    [b.IsNewControlCanntDelete]: {propName: c.DeleteProtect, type:  a.Boolean},
    [b.IsNewControlCanntEdit]: {propName: c.EditProtect, type:  a.Boolean},
    [b.IsNewControlCanntCopy]: {propName: c.CopyProtect, type:  a.Boolean},
    [b.IsNewControlMustInput]: {propName: c.IsMustFill, type:  a.Boolean},
    [b.IsNewControlShowBorder]: {propName: c.ShowBorder, type:  a.Boolean},
    [b.IsNewControlReverseEdit]: {propName: c.EditReverse, type:  a.Boolean},
    [b.IsNewControlHiddenBackground]: {propName: c.BackgroundColorHidden, type:  a.Boolean},
    [b.NewControlDisplayType]: {propName: c.SecretType, type:  a.Number},
    [b.NewControlFixedLength]: {propName: c.FixedLength, type:  a.Number},
    [b.NewControlMaxLength]: {propName: c.MaxLength, type:  a.Number},
    [b.CustomProperty]: {propName: c.CustomProperty, type:  a.Custom},
    // [b.IsShowBgColor]: {propName: c.IsShowBgColor, type:  null},
    // text only
    [b.HideHasTitle]: {propName: c.HideHasTitle, type:  a.Boolean},
    [b.ShowPlaceholder]: {type: a.Boolean},
    [b.NewControlItems]: {propName: c.ListItems, type:  a.List},
    [b.PrefixContent]: {propName: c.PrefixContent, type:  null},
    [b.SelectPrefixContent]: {propName: c.SelectPrefixContent, type:  null},
    [b.Separator]: {propName: c.Separator, type:  null},
    [b.IsShowValue]: {propName: c.ShowValue, type:  a.Boolean},
    [b.CodeLabel]: {propName: c.CodeLabel, type:  null}, // 自定义下拉名称显示值
    [b.ValueLabel]: {propName: c.ValueLabel, type:  null}, // 自定义下拉值显示值

    [b.MinValue]: {propName: c.MinValue, type:  a.Number},
    [b.MaxValue]: {propName: c.MaxValue, type:  a.Number},
    [b.Precision]: {propName: c.Precision, type:  a.Number}, // 精度
    [b.Unit]: {propName: c.Unit, type:  null},
    [b.ForceValidate]: {propName: c.ForceValidate, type:  a.Boolean},

    [b.DateBoxFormat]: {propName: c.DateType, type:  a.Number},
    [b.Retrieve]: {propName: c.Retrieve, type:  a.Boolean}, // 是否进行索引检索
    [b.Checked]: {propName: c.Checked, type:  a.Boolean},
    [b.ShowRight]: {propName: c.ShowRight, type:  a.Boolean},
    [b.PrintSelected]: {propName: c.PrintSelected, type:  a.Boolean},
    [b.Label]: {propName: c.Label, type:  null},
    [b.SpaceNum]: {propName: c.SpaceNum, type:  a.Number},
    [b.ShowType]: {propName: c.ShowType, type:  a.Number},
    [b.ShowTitle]: {propName: c.ShowTitle, type:  a.Boolean},
    [b.TabJump]: {propName: c.TabJump, type:  a.Boolean},
    // [b.BInsertFile]: {propName: c.ins, type:  null},
    // [b.BReadSign]: {propName: c.BReadSign, type:  null},
    [b.CustomFormat]: {propName: c.CustomDateFormat, type:  a.CustomDate},
    // [b.BClearItems]: {propName: 'aa', type:  null},
    [b.StartDate]: {propName: c.StartDate, type:  null},
    [b.EndDate]: {propName: c.EndDate, type:  null},
    [b.DateTime]: {propName: c.DateTime, type:  null},
    [b.SignatureCount]: {propName: c.SignatureCount, type: a.Number},
    [b.PreText]: {propName: c.PreText, type:  null},
    [b.SignatureSeparator]: {propName: c.SignatureSeparator, type:  null},
    [b.PostText]: {propName: c.PostText, type:  null},
    [b.SignaturePlaceholder]: {propName: c.SignaturePlaceholder, type:  null},
    [b.SignatureRatio]: {propName: c.SignatureRatio, type:  null},
    [b.RowHeightRestriction]: {propName: c.RowHeightRestriction, type:  null},

    [b.SignType]: {propName: c.ShowType, type:  null},
    [b.AlwaysShow]: {propName: c.AlwaysShow, type: a.Number},
    [b.ShowSignBorder]: {propName: c.ShowSignBorder, type:  null},
    [b.EventInfo]: {propName: c.EventInfo, type:  a.Event},
    [b.Cascade]: {propName: c.LogicEvent, type:  a.Event},
    [b.BTextBorder]: {propName: c.BTextBorder, type:  a.Boolean},
    [b.Hierarchy]: {propName: c.Hierarchy, type:  a.Number},
    [b.Province]: {propName: c.Province, type:  a.County},
    [b.City]: {propName: c.City, type:  a.County},
    [b.County]: {propName: c.County, type:  a.County},
    [b.Group]: {propName: c.Group, type:  null},
    [b.BShowCodeAndValue]: {propName: c.BShowCodeAndValue, type:  a.Boolean},
    [b.Identifier]: {propName: c.Identifier, type:  null},
    [b.LabelCode]: {propName: c.LabelCode, type:  null},
    [b.ItemTextColors]: {propName: c.ItemTextColors, type: a.County},
    [b.ExternalDataBind]: {propName: c.SourceBind, type: a.County},
    [b.SupportMultLines]: {propName: c.SupportMultLines, type: a.Boolean},
};

// 默认值，假如结构化元素设置默认值，也就是undefined的时候也能识别，那么这里不需要再设置默认值，当然也可以设置
export const STUCT_DEFAULT = {
    [c.SerialNumber]: '',
    [c.Placeholder]: '  ',
    [c.HelpTip]: '',
    [c.IsMustFill]: false,
    [c.DeleteProtect]: false,
    [c.EditProtect]: false,
    [c.CopyProtect]: false,
    [c.ShowBorder]: true,
    [c.BorderString]: '[',
    [c.EditReverse]: false,
    [c.BackgroundColorHidden]: false,
    [c.CustomProperty]: new Map(), // intentional? is [] in newControlProperty
    [c.TabJump]: undefined,
    [c.NewControlHidden]: false,
    [c.LogicEvent]: undefined,
    [c.Identifier]: undefined,

    // extent props
    [c.SecretType]: NewControlContentSecretType.DontSecret,
    [c.FixedLength]: undefined,
    [c.MaxLength]: undefined, // undefined & null means the same
    [c.Title]: '',
    [c.HideHasTitle]: false,

    // value struct
    [c.MinValue]: undefined,
    [c.MaxValue]: undefined,
    [c.Precision]: undefined,
    [c.Unit]: '',
    [c.ForceValidate]: false,

    // multi struct
    [c.Retrieve]: false,
    [c.SelectPrefixContent]: '',
    [c.PrefixContent]: '',
    [c.Separator]: '，',

    // date struct
    [c.DateType]: DateBoxFormat.DateAndHMS,
    [c.CustomDateFormat]: null,
    [c.StartDate]: '无',
    [c.EndDate]: '无',
    [c.DateTime]: '',

    // checkbox
    [c.ShowRight]: false, // both
    [c.Checked]: false,
    [c.PrintSelected]: false, // both
    [c.Label]: '',
    [c.Group]: undefined,
    [c.LabelCode]: '',

    // radiobox
    [c.ShowType]: NewControlType.RadioButton,
    [c.SpaceNum]: 1,
    [c.SupportMultLines]: false,

    // signature
    [c.SignatureCount]: SignatureCountType.Single,
    [c.PreText]: '  ',
    [c.SignatureSeparator]: ' ',
    [c.PostText]: '  ',
    [c.SignaturePlaceholder]: '',
    [c.SignatureRatio]: 1,
    [c.RowHeightRestriction]: true,

    [c.SignType]: 1,
    [c.AlwaysShow]: 3,
    [c.ShowSignBorder]: true,

    // ui only
    [c.ShowPlaceholder]: false,

    // address
    [c.Hierarchy]: undefined,
    // [c.hierarchy]: null,
    [c.Province]: null,
    [c.City]: null,
    [c.County]: null,
    [c.InterAlign]: ALIGN_TYPE.left,
    [c.CodeLabel]: undefined,
    [c.ValueLabel]: undefined,
};

export interface IReadStruct {
    readProp(textNode: string, documentVersion?: number, node?: any): any;
}
