import { ISerialBase, ISerialDocObj, ISerialHFObj, ISerialParaObj, ISerialTableObj, SerialObjType } from '../../serialize/serialInterface';
import { Document, Footer, Header, Packer } from 'docx';
import DocxParagraph from './DocxParagraph';
import DocxTable from './DocxTable';

export default class DocxBuilder {
    private docxDocument: Document;
    constructor(private readonly docObj: ISerialDocObj) {}

    /** 生成文档对象 */
    public generate(): DocxBuilder {
        this.docxDocument = new Document({
            sections: [{
                properties: this.docObj.property,
                children: this.serializedTo(this.docObj.children),
                headers: this.buildHeaderFooterObj(this.docObj.header),
                footers: this.buildHeaderFooterObj(this.docObj.footer),
            }]
        });
        return this;
    }

    /** 创建样例文档对象 */
    public demo(): DocxBuilder {
        this.docxDocument = new Document({
            sections: [{
                children: [
                    DocxParagraph.demo(),
                ]
            }]
        });
        return this;
    }

    /**  将文档转换为Blob */
    public toBlob(): Promise<Blob> {
        if (!this.docxDocument) {
            return Promise.resolve(undefined);
        }
        return Packer.toBlob(this.docxDocument);
    }

    /** 填充子节点到集合中 */
    private serializedTo(contents: ISerialBase[],
                         collector: any[] = []): any[] {
        if (!Array.isArray(contents)) {
            return undefined;
        }
        for (const content of contents) {
            if (content.type === SerialObjType.Paragraph) {
                new DocxParagraph(content as ISerialParaObj).buildTo(collector);
            } else if (content.type === SerialObjType.Table) {
                new DocxTable(content as ISerialTableObj).buildTo(collector);
            }
        }
        return collector;
    }

    /** 构造页眉页脚参数 */
    private buildHeaderFooterObj(hfObj: ISerialHFObj): {
        default?: Header | Footer;
        even?: Header | Footer;
        odd?: Header | Footer;
    } {
        if (!hfObj) {
            return undefined;
        }
        const option = {
            default: undefined,
            even: undefined,
            odd: undefined,
        };
        let hasContent = false;
        for (const key of ['default', 'even', 'odd']) {
            if (!hfObj[key] || !hfObj[key].length) {
                continue;
            }
            if (hfObj.type === SerialObjType.Header) {
                option[key] = this.buildHeader(hfObj[key]);
                hasContent = true;
            } else if (hfObj.type === SerialObjType.Footer) {
                option[key] = this.buildFooter(hfObj[key]);
                hasContent = true;
            }
        }
        return hasContent ? option : undefined;
    }

    /** 构造页脚内容 */
    private buildFooter(children: ISerialBase[]): Footer {
        if (!Array.isArray(children)) {
            return undefined;
        }
        return new Footer({
            children: this.serializedTo(children)
        });
    }

    /** 构造页眉内容 */
    private buildHeader(children: ISerialBase[]): Header {
        if (!Array.isArray(children)) {
            return undefined;
        }
        return new Header({
            children: this.serializedTo(children)
        });
    }
}
