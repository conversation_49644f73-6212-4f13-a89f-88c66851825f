import * as React from 'react';
import '../../style/customProp.less';
import {message} from '../../../../common/Message';
import Input from '../../ui/Input';
import { CodeValueItem } from '../../../../common/commonDefines';
import { IFRAME_MANAGER } from '../../../../common/IframeManager';

interface IProps {
    name: string;
    value: any[];
    docId: number;
    onChange: (value: any, name: string) => void;
    confirm?: (value: any, name: string) => void;
}

interface IState {
    bRefresh: boolean;
}

export default class NewComboBoxList extends React.Component<IProps, IState> {
    private custompProps: CodeValueItem[];
    private _name: string;
    private _names: object;
    private _activeName: string;
    private activeIndex: number;
    private curInputIndex: number;
    private currentProp: CodeValueItem;
    constructor(props: IProps) {
        super(props);
        this._name = '标题';
        this.state = {
            bRefresh: false,
        };
        this._names = {};

        this.init(this.props.value);
    }

    public render(): any {
        if (!this.custompProps || this.custompProps.length === 0) {
            return (<div className='custom-no-data custom-prop-add' onClick={this.rowClick.bind(this, 0)}>点击添加</div>);
        }
        return (
            <div className='newcontrol-custom'>
                <ul className='newcombox'>
                    <li>
                        <div>显示名</div>
                        <div>属性值</div>
                        <div>操作</div>
                    </li>
                    {this.renderList()}
                </ul>
            </div>
        );
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        this.init(nextProps.value);
    }

    private init(value: any[]): void {
        if (value) {
            this.custompProps = value.slice();
        } else {
            this.custompProps = [];
            this.addData();
        }
    }

    private renderList(): any {
        return this.custompProps.map((prop, index) => {
            return (
                <li
                    key={index}
                    onClick={this.rowClick.bind(this, index)}
                    onMouseEnter={this.mouseEnter.bind(this, index)}
                >
                    <div className='custom-prop-name'>
                        <Input
                            value={prop.code}
                            name='code'
                            onChange={this.onChange.bind(this, index)}
                            onBlur={this.onBlur.bind(this, index)}
                            focus={this.onFocus}
                        />
                    </div>
                    <div>
                        <Input
                            value={prop.value}
                            name='value'
                            onBlur={this.confirm}
                            onChange={this.onChange.bind(this, index)}
                        />
                    </div>
                    <div>
                        <i className='prev'>↑</i>
                        <i className='next'>↓</i>
                        <span className='custom-prop-add'>+</span>
                        <label className='custom-prop-delete'>-</label>
                    </div>
                </li>
            );
        });
    }

    private mouseEnter = (index: number, e: any): void => {
        this.currentProp = this.custompProps[index];
    }

    private rowClick(index: number, e: any): void {
        this.activeIndex = index;
        const target = e.target as HTMLDivElement;
        const className = target.className;
        if (this.curInputIndex !== index && className === 'custom-prop-name') {
            this.curInputIndex = index;
        } else if (className.indexOf('custom-prop-add') > -1) {
            this.addData(index);
            this.setState({bRefresh: !this.state.bRefresh});
        } else if (className === 'custom-prop-delete') {
            this.deleteData(index);
            this.setState({bRefresh: !this.state.bRefresh});
        } else if (className === 'prev') {
            if (index === 0) {
                return;
            }
            const datas = this.custompProps;
            const temp = datas[index - 1];
            datas[index - 1] = datas[index];
            datas[index] = temp;
            this.setState({bRefresh: !this.state.bRefresh});
            this.props.onChange(this.custompProps.slice(), this.props.name);
        } else if (className === 'next') {
            const datas = this.custompProps;
            if (index === datas.length - 1) {
                return;
            }
            const temp = datas[index];
            datas[index] = datas[index + 1];
            datas[index + 1] = temp;
            this.setState({bRefresh: !this.state.bRefresh});
            this.props.onChange(this.custompProps.slice(), this.props.name);
        }
        // this.currentProp = this.custompProps[index];
    }

    private onChange = (index: number, value: any, name: string): void => {
        this.currentProp = this.custompProps[index];
        this.currentProp[name] = value;
    }

    private addData = (index?: number): void => {
        const data: CodeValueItem = new CodeValueItem('', undefined, false);
        if (index === undefined) {
            this.custompProps.push(data);
        } else {
            this.custompProps.splice(index + 1, 0, data);
        }
        this.currentProp = data;
        this.props.onChange(this.custompProps.slice(), this.props.name);
    }

    private deleteData = (index: number): void => {
        const data = this.custompProps[index];
        delete this._names[data.code];
        this.custompProps.splice(index, 1);
        this.currentProp = undefined;
        this.props.onChange(this.custompProps.slice(), this.props.name);
        this.confirm();
    }

    // private getPropName(): string {
    //     const datas = this.custompProps;
    //     if (!datas || datas.length === 0) {
    //         const actName = this._name + 1;
    //         // this._name = actName;
    //         this._names[actName] = true;
    //         return actName;
    //     }

    //     const names = this._names;
    //     const name = this._name;
    //     let res: string;
    //     for (let index = 1; true; index++) {
    //         res = name + index;
    //         if (names[res] !== true) {
    //             names[res] = true;
    //             break;
    //         }
    //     }

    //     return res;
    // }

    private onBlur = (index: number, name: string, input: any): void => {
        const currentProp = this.custompProps[index];
        if (!currentProp) {
            return;
        }
        const preName = this._activeName;
        const currentName = currentProp.code;
        if (currentName) {
            const currentIndex = this.custompProps.findIndex((item, i) => item.code === currentName && i !== index);
            if (currentIndex > -1) {
                IFRAME_MANAGER.setDocId(this.props.docId);
                message.error('已存在相同的名称')
                .then(() => {
                    currentProp.code = '';
                    input.focus();
                    this.setState({bRefresh: !this.state.bRefresh});
                });
                return;
            }
            delete this._names[preName];
            this._names[currentName] = true;
        }
        this.confirm();
        // else if (!currentName) {
        //     message.error('显示名称不能为空')
        //     .then(() => {
        //         // input.focus();
        //         this.setState({bRefresh: !this.state.bRefresh});
        //     });
        // }
    }

    private onFocus = (name: string, e: any): void => {
        this._activeName = e.target.value;
    }

    private confirm = (): void => {
        if (typeof this.props.confirm !== 'function') {
            return;
        }

        this.props.confirm(this.custompProps, this.props.name);
    }
}
