// const regions = require('./Regions');
// const regions2 = require('./china-division');
// const fs = require('fs');
// let arrs2 = [];
// let arrs3 = [];
// regions2.forEach(function (item) {
//     if (item.children && item.children.length) {
//         arrs2 = arrs2.concat(item.children);
//         item.children.forEach(function (data) {
//             if (data.children && data.children.length) {
//                 arrs3 = arrs3.concat(data.children);
//             }
//         })
//     }
// });


// const json1 = regions.filter(function(item) {
//     return !item.ParentID && item.Level === 1;
// });

// const json2 = regions.filter(function(item) {
//     return item.ParentID && item.Level === 2;
// });

// const json3 = regions.filter(function(item) {
//     if (item.ParentID && item.Level === 3) {
//         if (item.AdDivCode === null) {
//             let obj = json2.find(function(data) {
//                 if (data.ID === item.ParentID) {
//                     return true;
//                 }
//                 return false;
//             })

//             if (obj && obj.Name === item.Name) {
//                 // console.log(obj.Name + '---');
//                 item.AdDivCode = obj.AdDivCode;
//             }

//             if (item.AdDivCode === null) {
//                 obj = arrs3.find(function(current) {
//                     if (current.name === item.Name || item.ShortNames && current.name.indexOf(item.ShortNames[0]) === 0) {
//                         item.AdDivCode = current.code;
//                     }
//                 })
//                 if (obj) {
//                     console.log(obj.name + '+++');
//                     return true;
//                 }
//             }
//             // console.log(item.Name);
//         }
//         return true;
//     }
//     return false;
// });

// json2.forEach(function(item) {
//     item.children = [];
//     json3.forEach(function(data) {
//         if (data.AdDivCode === null) {
//             return;
//         }
//         if (data.ParentID === item.ID) {
//             item.children.push({code: data.AdDivCode, name: data.Name, children: undefined});
//         }
//     });
// });

// const json12 = [];
// json1.forEach(function(item) {
//     if (item.AdDivCode === null) {
//         console.log(item.Name + '----1')
//     }
//     const obj = {
//         code: item.AdDivCode,
//         name: item.Name,
//         children: [],
//     }
//     json12.push(obj);
//     json2.forEach(function(data) {
//         if (data.AdDivCode === null) {
//             console.log(item.name + '----2')
//         }
//         if (data.ParentID === item.ID) {
//             obj.children.push({code: data.AdDivCode, name: data.Name, children: data.children});
//         }
//     });
// });


// fs.writeFile('./new-regions.json', JSON.stringify(json12), function(err) {
//     if (err) {
//         return;
//     }
//     console.log('Saved success!');
// });
// const {
//     [key: string]: {}
// }
const fs = require('fs');
const obj = {};
const address = require('./src/common/resources/china-city.json');
let bChange = false;
address.forEach((p) => {
    // if (bChange) {
    //     return;
    // }
    const obj1 = {};
    const codeName1 = p.code.slice(0, 2);
    // obj1.c = codeName1;
    obj1.n = p.name;
    obj[codeName1] = obj1;
    let c2Value;
    p.children.forEach((city) => {
        let obj2 = {};
        let codeName2;
        const blevel = city.level !== undefined;
        let prevCode;
        const codeName5 = city.code.slice(2, 4);
        city.children.forEach((county, index) => {
            
            // const obj3 = {};
            const codeName3 = county.code.slice(4, 6) || ('0' + ++index);
            const codeName4 = county.code.slice(2, 4) || codeName5;
            if (codeName4 !== prevCode) {
                obj2 = {};
                obj1[codeName4] = obj2;
                prevCode = codeName4;
                if (blevel) {
                    codeName2 = prevCode;
                }
            }
            obj2[codeName3] = county.name;
            
            // obj3.c = codeName3;
            // obj3.n = county.name;
        });
        // obj2.c = city.code.slice(2, 4);
        // codeName2 = codeName2 || city.code.slice(2, 4);
        // obj1[codeName2] = obj2;
        obj2.n = city.name;
        // c2Value = obj2.c;
        if (blevel) {
            obj1.c2 = codeName2;
            obj2.c = city.code.slice(2, 4);
        }
        if (!city.children.length) {
            obj1[city.code.slice(2, 4)] = obj2;
        }
    });
    
    // if (p.level !== undefined) {
    //     obj1.c2 = c2Value;
    // }
    // bChange = true;
});

fs.writeFile('./src/common/resources/address.js', `// 通过执行 node parseRegions.js代码编译所得;\nexport const CHINA_ADDRESS = ${JSON.stringify(obj)}`, function(err) {
    if (err) {
        return;
    }
    console.log('Saved success!');
});
