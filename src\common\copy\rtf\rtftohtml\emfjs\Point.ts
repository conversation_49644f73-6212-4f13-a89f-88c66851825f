export class PointS {
    public x: number;
    public y: number;

    constructor(reader: any, x?: number, y?: number) {
        if (reader != null) {
            this.x = reader.readInt16();
            this.y = reader.readInt16();
        } else {
            this.x = x;
            this.y = y;
        }
    }

    public clone(): PointS {
        return new PointS(null, this.x, this.y);
    }

    public toString(): string {
        return '{x: ' + this.x + ', y: ' + this.y + '}';
    }
}

export class PointL {
    public x: number;
    public y: number;

    constructor(reader: any, x?: number, y?: number) {
        if (reader != null) {
            this.x = reader.readInt32();
            this.y = reader.readInt32();
        } else {
            this.x = x;
            this.y = y;
        }
    }

    public clone(): PointL {
        return new PointL(null, this.x, this.y);
    }

    public toString(): string {
        return '{x: ' + this.x + ', y: ' + this.y + '}';
    }
}
