@font-face {
  font-family: 'hz-inconfont';
  src:
    url('./iconfont.ttf?72se5v') format('truetype'),
    url('./iconfont.woff?72se5v') format('woff'),
    url('./iconfont.svg?72se5v') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

[class^="hz-"], [class*=" hz-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'hz-inconfont' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.hz-add:before {
  content: "\e917";
}
.hz-clear:before {
  content: "\e918";
}
.hz-content_copy:before {
  content: "\e919";
}
.hz-content_cut:before {
  content: "\e91a";
}
.hz-content_paste:before {
  content: "\e91b";
}
.hz-redo1:before {
  content: "\e91c";
}
.hz-undo1:before {
  content: "\e91d";
}
.hz-low_priority:before {
  content: "\e91e";
}
.hz-align-center:before {
  content: "\e91f";
}
.hz-align_justify:before {
  content: "\e920";
}
.hz-align-left:before {
  content: "\e921";
}
.hz-align-right:before {
  content: "\e923";
}
.hz-bold:before {
  content: "\e924";
}
.hz-colorfill:before {
  content: "\e925";
}
.hz-textcolor:before {
  content: "\e927";
}
.hz-indent_dec:before {
  content: "\e92a";
}
.hz-indent_inc:before {
  content: "\e92b";
}
.hz-italic:before {
  content: "\e92e";
}
.hz-linespacing:before {
  content: "\e92f";
}
.hz-listbulleted:before {
  content: "\e932";
}
.hz-listnumber:before {
  content: "\e934";
}
.hz-format_underlined:before {
  content: "\e935";
}
.hz-wrap_text:before {
  content: "\e936";
}
.hz-crop_original:before {
  content: "\e939";
}
.hz-zoom_in:before {
  content: "\e914";
}
.hz-subscript:before {
  content: "\e93a";
}
.hz-superscript:before {
  content: "\e93b";
}
.hz-height:before {
  content: "\e916";
}
.hz-inden-right:before {
  content: "\e913";
}
.hz-inden-left:before {
  content: "\e912";
}
.hz-cell:before {
  content: "\e90a";
}
.hz-cell2:before {
  content: "\e90c";
}
.hz-rowsetting:before {
  content: "\e910";
}
.hz-trackchanges:before {
  content: "\e905";
}
.hz-water:before {
  content: "\e90b";
}
.hz-image:before {
  content: "\e90d";
}
.hz-svgimage:before {
  content: "\e90e";
}
.hz-panel:before {
  content: "\e922";
}
.hz-file-text2:before {
  content: "\e926";
}
.hz-audiofile:before {
  content: "\e928";
}
.hz-mediafile:before {
  content: "\e929";
}
.hz-copy:before {
  content: "\e92c";
}
.hz-paste:before {
  content: "\e92d";
}
.hz-open:before {
  content: "\e930";
}
.hz-new:before {
  content: "\e931";
}
.hz-save:before {
  content: "\e933";
}
.hz-barcode:before {
  content: "\e937";
}
.hz-qrcode:before {
  content: "\e938";
}
.hz-calculator:before {
  content: "\e940";
}
.hz-calendar:before {
  content: "\e953";
}
.hz-printer:before {
  content: "\e954";
}
.hz-save2:before {
  content: "\e962";
}
.hz-undo:before {
  content: "\e965";
}
.hz-redo:before {
  content: "\e966";
}
.hz-commit:before {
  content: "\e96e";
}
.hz-search:before {
  content: "\e986";
}
.hz-zoom:before {
  content: "\e987";
}
.hz-wrench:before {
  content: "\e991";
}
.hz-row:before {
  content: "\e992";
}
.hz-col:before {
  content: "\e993";
}
.hz-setting:before {
  content: "\e994";
}
.hz-cogs:before {
  content: "\e995";
}
.hz-list:before {
  content: "\e9b9";
}
.hz-list1:before {
  content: "\e9ba";
}
.hz-list2:before {
  content: "\e9bb";
}
.hz-link:before {
  content: "\e9cb";
}
.hz-new2:before {
  content: "\ea0a";
}
.hz-info:before {
  content: "\ea0c";
}
.hz-spellcheck:before {
  content: "\ea12";
}
.hz-loop2:before {
  content: "\ea2e";
}
.hz-struct:before {
  content: "\ea4e";
}
.hz-radio-checked:before {
  content: "\ea54";
}
.hz-radio-unchecked:before {
  content: "\ea56";
}
.hz-cut:before {
  content: "\ea5a";
}
.hz-font:before {
  content: "\ea5c";
}
.hz-specialfont:before {
  content: "\ea66";
}
.hz-pagebreak:before {
  content: "\ea68";
}
.hz-text-color:before {
  content: "\ea6d";
}
.hz-table1:before {
  content: "\ea70";
}
.hz-table:before {
  content: "\ea71";
}
.hz-insertfile:before {
  content: "\ea72";
}
.hz-return:before {
  content: "\ea73";
}
.hz-paragraphjustify:before {
  content: "\ea7a";
}
.hz-struct2:before {
  content: "\ea82";
}
.hz-html:before {
  content: "\ead9";
}
.hz-pdf:before {
  content: "\eadf";
}
.hz-word:before {
  content: "\eae1";
}
.hz-excel:before {
  content: "\eae2";
}
.hz-pagenumber:before {
  content: "\e911";
}
.hz-info2:before {
  content: "\e90f";
}
.hz-file:before {
  content: "\e900";
}
.hz-files:before {
  content: "\e901";
}
.hz-layout:before {
  content: "\e902";
}
.hz-layout1:before {
  content: "\e903";
}
.hz-layout2:before {
  content: "\e904";
}
.hz-layout3:before {
  content: "\e906";
}
.hz-layout4:before {
  content: "\e907";
}
.hz-layout5:before {
  content: "\e908";
}
.hz-layout6:before {
  content: "\e909";
}
.hz-ai:before {
  content: "\e900";  /* 这个编码需要根据实际下载的图标包中的编码进行修改 */
}
