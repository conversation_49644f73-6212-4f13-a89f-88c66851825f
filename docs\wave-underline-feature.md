# 波浪线下划线功能实现方案

## 功能概述

用户需要实现一个 `markTextWithWave` 函数，用于给选中文本添加类似拼写检查的波浪线标记。

## 最终技术方案

### 方案选择
- ✅ **使用**: `textDecorationLine = TextDecorationLineType.WavyUnderline`
- ✅ **渲染**: 统一使用 SVG 渲染波浪线


## 实现流程

### 1. 接口调用链路

```typescript
markTextWithWave() (外部接口)
  ↓
DocumentCore.markTextWithWave()
  ↓  
Document.markTextWithWaveController()
  ↓
设置 TextProperty.textDecorationLine = WavyUnderline/None
  ↓
Document.addToParagraph() (应用到段落)
  ↓
Document.js:17165 生成渲染数据 (style: 99)
  ↓
ParaBaseUI.tsx 渲染 SVG 波浪线
```

### 2. 核心函数实现

#### Document.markTextWithWaveController()

```typescript
public markTextWithWaveController(): number {
    const textPr = new TextProperty();
    textPr.clear();

    // 获取当前的波浪线状态（切换逻辑）
    let bWavyUnderline: boolean;
    const selection = this.getDocumentSelection();
    if (selection.bUse) {
        const activeStatus = this.getActiveStatus({textDecorationLine: undefined});
        bWavyUnderline = activeStatus === 1;
    } else {
        // 检查当前光标位置的状态
        const item = this.getCurrentParagraph();
        bWavyUnderline = item.getContent()[item.getCurPos().contentPos]
                            .textProperty.textDecorationLine !== TextDecorationLineType.WavyUnderline;
    }

    // 设置波浪线属性（仅设置一个值）
    textPr.textDecorationLine = bWavyUnderline ? TextDecorationLineType.WavyUnderline : TextDecorationLineType.None;
    
    const paraText = new ParaTextProperty();
    paraText.textProperty = textPr;
    
    // 使用addToParagraph来处理多段落选择
    const result = this.addToParagraph(paraText);
    return result;
}
```

#### 渲染数据生成 (Document.ts:17165)

```typescript
if (textPro.textDecorationLine === TextDecorationLineType.WavyUnderline && !bDrawing) {
    const y = positionY;
    oPara.textDecoration.push({
        x: item.positionX,
        y: y + 1.8,
        width: widthVisible,
        color,  // 使用当前的 color 变量
        style: 99,  // 特殊的样式标识，表示波浪线
    });
}
```

#### SVG 波浪线渲染 (ParaBaseUI.tsx)

```typescript
} else if (99 === data.style) {
    // 波浪线渲染 - 使用SVG path绘制波浪效果
    const waveWidth = 4; // 波浪宽度
    const waveHeight = 1; // 波浪高度
    const startX = data.x * scale;
    const endX = (data.x + data.width) * scale;
    const totalWidth = endX - startX;
    const waveCount = Math.ceil(totalWidth / waveWidth);
    
    let pathData = `M ${numtoFixed2(startX)} ${y}`;
    for (let i = 0; i < waveCount; i++) {
        const x1 = startX + i * waveWidth;
        const x2 = x1 + waveWidth / 2;
        const x3 = Math.min(x1 + waveWidth, endX);
        if (x3 > x1) {
            pathData += ` Q ${numtoFixed2(x2)} ${numtoFixed2(parseFloat(y) - waveHeight)} ${numtoFixed2(x3)} ${y}`;
        }
    }
    
    return (
        <path
            key={index}
            d={pathData}
            stroke="red"
            strokeWidth="0.5px"
            fill="none"
            clipPath={mask}
        />
    );
}
```

## 测试验证

### 功能测试
1. **选择文本**: 选中文本后调用 `markTextWithWave()` 应该添加波浪线
2. **切换状态**: 对已有波浪线的文本再次调用应该移除波浪线
3. **光标位置**: 光标在不同位置时状态判断正确
4. **多段落**: 跨段落选择时功能正常

### 性能测试
1. **渲染性能**: SVG 波浪线渲染流畅
2. **内存使用**: 删除冗余属性后内存占用减少
3. **代码体积**: 删除冗余代码后包体积减小

## 技术亮点

### 1. 架构优化
- **单一数据源**: 使用 `textDecorationLine` 统一管理
- **职责分离**: 属性设置与渲染逻辑分离
- **代码简化**: 删除冗余的双重状态管理

### 2. 渲染优化
- **统一渲染**: 使用 SVG 获得更好的视觉效果



## 后续优化建议

1. **颜色配置**: 考虑支持自定义波浪线颜色
2. **样式扩展**: 支持不同类型的波浪线样式
3. **性能监控**: 添加渲染性能监控
4. **单元测试**: 增加完整的单元测试覆盖

---

**版本**: v2.0  
**更新时间**: 2024-12-19  
**状态**: ✅ 已完成并优化

