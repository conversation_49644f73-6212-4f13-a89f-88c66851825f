import { DocumentBorder, DocumentShadow, AlignType } from './Style';
import { GenericBox, ICustomProps, DataType, ResultType, EnableRowActionType, parseBoolean } from '../../common/commonDefines';
import History from './History';
import { ChangeTableCanAddRow, ChangeTableCanDeleteRow, ChangeTableDeleteProtect,
    ChangeTableFixedColWidth, ChangeTableFixedRowHeight, ChangeTableReadOnly } from './Table/TableChange';

export enum TableBorderLineStyle {
    None,  // 无线条
    Single,  // 实线
    Dash,  // 虚线
}

export const TABLE_RECALC_TOL = 1; // 0.04

/**
 * 计算单位
 */
export enum TableWidthType {
    Auto,
    Mm,  // 毫米
    Pencent,  // 百分比
}

/**
 * 表格大小尺寸调整
 */
export enum TableLayoutType {
    AutoFit,  // 自适应内容
    Fixed,   // 手动调整
}
/**
 * 设置单元格，行的宽度
 */
export class TableMeasurement {
    public type: TableWidthType;  // 宽度类型
    public width: number;

    constructor(type: TableWidthType, width: number) {
        this.type = type;
        this.width = width;
    }

    public copy(): TableMeasurement {
        return new TableMeasurement(this.type, this.width);
    }
}

export class TableProperty {
    public alignment: AlignType; // 对齐方式;
    public tableBorders: {
        bottom: DocumentBorder,
        left: DocumentBorder,
        right: DocumentBorder,
        top: DocumentBorder,
        insideH: DocumentBorder,
        insideV: DocumentBorder,
    };

    public tableIndentation: number;  // 表格缩进

    public tableCellMargin: GenericBox<TableMeasurement>;

    public tableWidth: TableMeasurement;   // 表格宽度
    public cellSpacing: TableMeasurement;  // 单元格之间的间距

    public shadow: DocumentShadow;  // 表格背景填充
    public tableLayout: TableLayoutType;
    private enableRowAction: EnableRowActionType;
    // public cellGirdLines: {[key: number]: INISCellGridLine};
    // public headerCount: number; // 不正式使用

    private bFixedRowHeight: boolean;
    private bFixedColWidth: boolean;
    private bCanAddRow: boolean;
    private bCanDeleteRow: boolean;
    private bDeleteProtect: boolean;
    private bReadOnly: boolean;
    private customProperty: Map<string, ICustomProps>;
    private bHeaderReadOnly: boolean;
    private fixedLeft: number;
    private fixedRight: number;
    private bFixedHeader: boolean;
    private bRepeatHeader: boolean;
     
    private columnIDs: string[];
    private enableRowNum: number;
    private bPrintEmptyRow: boolean;
    private bRepeatOnBreak: boolean;

    constructor() {
        this.alignment = AlignType.Left;
        this.tableBorders = {
            bottom: new DocumentBorder(),
            left: new DocumentBorder(),
            right: new DocumentBorder(),
            top: new DocumentBorder(),
            insideH: new DocumentBorder(),
            insideV: new DocumentBorder(),
        };
        this.tableIndentation = 0;
        this.tableCellMargin = new GenericBox(new TableMeasurement(TableWidthType.Mm, 3.9),
                                                new TableMeasurement(TableWidthType.Mm, 3.9),
                                                new TableMeasurement(TableWidthType.Mm, 3.9),
                                                new TableMeasurement(TableWidthType.Mm, 3.9));
        this.tableWidth = new TableMeasurement(TableWidthType.Auto, 0);
        this.cellSpacing = null;
        this.shadow = null; // new DocumentShadow();
        this.tableLayout = TableLayoutType.AutoFit;

        this.bFixedRowHeight = false;
        this.bFixedColWidth = false;
        this.bCanAddRow = true;
        this.bCanDeleteRow = true;
        this.bDeleteProtect = false;
        this.bReadOnly = false;
        this.customProperty = undefined;
        this.bHeaderReadOnly = false;
        this.bRepeatHeader = true;
        this.columnIDs = undefined;
        this.bPrintEmptyRow = true;
        this.bRepeatOnBreak = false;
    }

    public copy(): TableProperty {
        const prop = new TableProperty();

        prop.alignment = this.alignment;

        const borders = this.tableBorders;
        const bottom = borders.bottom ? borders.bottom.copy() : undefined;
        const left = borders.left ? borders.left.copy() : undefined;
        const right = borders.right ? borders.right.copy() : undefined;
        const top = borders.top ? borders.top.copy() : undefined;
        const insideH = borders.insideH ? borders.insideH.copy() : undefined;
        const insideV = borders.insideV ? borders.insideV.copy() : undefined;
        prop.tableBorders = {
            bottom, left, right, top, insideH, insideV,
        };

        prop.tableIndentation = this.tableIndentation;
        prop.tableCellMargin = new GenericBox(
                            new TableMeasurement(this.tableCellMargin.bottom.type, this.tableCellMargin.bottom.width),
                            new TableMeasurement(this.tableCellMargin.left.type, this.tableCellMargin.left.width),
                            new TableMeasurement(this.tableCellMargin.right.type, this.tableCellMargin.right.width),
                            new TableMeasurement(this.tableCellMargin.top.type, this.tableCellMargin.top.width));
        prop.tableWidth = new TableMeasurement(this.tableWidth.type, this.tableWidth.width);
        prop.cellSpacing = ( null === this.cellSpacing ) ? null :
                            new TableMeasurement(this.cellSpacing.type, this.cellSpacing.width);
        prop.shadow = ( null === this.shadow ) ? null : this.shadow.copy();
        prop.tableLayout = this.tableLayout;
        prop.bPrintEmptyRow = this.bPrintEmptyRow;
        prop.bRepeatOnBreak = this.bRepeatOnBreak;
        prop.bFixedRowHeight = this.bFixedRowHeight;
        prop.bFixedColWidth = this.bFixedColWidth;
        prop.bCanAddRow = this.bCanAddRow;
        prop.bCanDeleteRow = this.bCanDeleteRow;
        prop.bDeleteProtect = this.bDeleteProtect;
        prop.bReadOnly = this.bReadOnly;
        prop.bHeaderReadOnly = this.bHeaderReadOnly;
        prop.customProperty = new Map();
        const customProps = this.customProperty;
        if (customProps) {
            for (const [key, custom] of customProps) {
                prop.customProperty.set(key, custom);
            }
        }
        prop.fixedLeft = this.fixedLeft;
        prop.fixedRight = this.fixedRight;
        prop.bRepeatHeader = this.bRepeatHeader;

        return prop;
    }

    public setFixedRowHeight(bFixedRowHeight: boolean, history?: History): void {
        if ( history && bFixedRowHeight !== this.bFixedRowHeight ) {
            history.addChange(new ChangeTableFixedRowHeight(this, this.bFixedRowHeight, bFixedRowHeight));
        }
        this.bFixedRowHeight = bFixedRowHeight;
    }

    public setEnableRowAction(type: EnableRowActionType): number {
        this.enableRowAction = type;
        return ResultType.Success;
    }

    public getEnableRowAction(): EnableRowActionType {
        return this.enableRowAction;
    }

    public getEnableRowNum(): number {
        return this.enableRowNum;
    }

    public setEnableRowNum(rowIndex: number): void {
        this.enableRowNum = rowIndex;
    }

    public isFixedRowHeight(): boolean {
        return this.bFixedRowHeight;
    }

    public setFixedColWidth(bFixedColWidth: boolean, history?: History): void {
        if ( history && bFixedColWidth !== this.bFixedColWidth ) {
            history.addChange(new ChangeTableFixedColWidth(this, this.bFixedColWidth, bFixedColWidth));
        }
        this.bFixedColWidth = bFixedColWidth;
    }

    public isFixedColWidth(): boolean {
        return this.bFixedColWidth;
    }

    public setTableReadOnly(bReadOnly: boolean, history?: History): void {
        if ( history && bReadOnly !== this.bReadOnly ) {
            history.addChange(new ChangeTableReadOnly(this, this.bReadOnly, bReadOnly));
        }
        this.bReadOnly = bReadOnly;
    }

    public isTableReadOnlyProtect(): boolean {
        return this.bReadOnly;
    }

    public setTableCanAddRow(bCanAddRow: boolean, history?: History): void {
        if ( history && bCanAddRow !== this.bCanAddRow ) {
            history.addChange(new ChangeTableCanAddRow(this, this.bCanAddRow, bCanAddRow));
        }
        this.bCanAddRow = bCanAddRow;
    }

    public isTableCanAddRow(): boolean {
        return this.bCanAddRow;
    }

    public setPrintEmptyRow(flag: boolean): number {
        if (flag == null || this.bPrintEmptyRow === flag) {
            return ResultType.UnEdited;
        }
        this.bPrintEmptyRow = flag;
        return ResultType.Success;
    }

    public setRepeatOnBreak(flag: boolean): number {
        if (flag == null || this.bRepeatOnBreak === flag) {
            return ResultType.UnEdited;
        }
        this.bRepeatOnBreak = flag;
        return ResultType.Success;
    }

    public isPrintEmptyRow(): boolean {
        return this.bPrintEmptyRow;
    }

    public isRepeatOnBreak(): boolean {
        return this.bRepeatOnBreak;
    }

    public setTableCanDeleteRow(bCanDeleteRow: boolean, history?: History): void {
        if ( history && bCanDeleteRow !== this.bCanDeleteRow ) {
            history.addChange(new ChangeTableCanDeleteRow(this, this.bCanDeleteRow, bCanDeleteRow));
        }
        this.bCanDeleteRow = bCanDeleteRow;
    }

    public isTableCanDeleteRow(): boolean {
        return this.bCanDeleteRow;
    }

    public addCustomPropItem(key: string, value: string): number {
        this.customProperty.set(key, {name: key, value, type: DataType.String, targetValue: value});
        return ResultType.Success;
    }

    public addCustomProps(props: ICustomProps[]): void {
        if (!props) {
            return;
        }

        if ( null == this.customProperty ) {
            this.customProperty = new Map();
        }

        props.forEach((prop) => {
            this.customProperty.set(prop.name, prop);
            switch (prop.type) {
                case DataType.Boolean:
                    prop.targetValue = prop.value ? parseBoolean(prop.value) : undefined;
                    break;
                case DataType.Number:
                    prop.targetValue = prop.value ? Number(prop.value) : undefined;
                    break;
                default:
                    prop.targetValue = prop.value;
            }
        });
    }

    public getCustomByPropName(name: string): string {
        const prop = this.customProperty.get(name);
        if (!prop) {
            return ResultType.StringEmpty;
        }

        return prop.value;
    }

    public getCustomProps(): ICustomProps[] {
        const arrs = [];
        if ( null == this.customProperty ) {
            return arrs;
        }

        for (const [name, prop] of this.customProperty) {
            arrs.push(prop);
        }

        return arrs;
    }

    public setCustomProps(props: ICustomProps[]): void {
        if (!props) {
            return;
        }

        if ( null == this.customProperty ) {
            this.customProperty = new Map();
        } else {
            this.customProperty.clear();
        }

        this.addCustomProps(props);
    }

    public isDeleteProtect(): boolean {
        return this.bDeleteProtect;
    }

    public setDeleteProtect(bDeleteProtect: boolean, history?: History): void {
        if ( history && bDeleteProtect !== this.bDeleteProtect ) {
            history.addChange(new ChangeTableDeleteProtect(this, this.bDeleteProtect, bDeleteProtect));
        }
        this.bDeleteProtect = bDeleteProtect;
    }

    public setFixedRight(right: number): number {
        if (right == null || right === this.fixedRight) {
            return ResultType.UnEdited;
        }

        this.fixedRight = right;
        return ResultType.Success;
    }

    public setFixedLeft(left: number): number {
        if (left == null || left === this.fixedLeft) {
            return ResultType.UnEdited;
        }

        this.fixedLeft = left;
        return ResultType.Success;
    }

    public getFixedRight(): number {
        return this.fixedRight;
    }

    public getFixedLeft(): number {
        return this.fixedLeft;
    }

    public isFixedHeader(): boolean {
        return this.bFixedHeader;
    }

    public setFixedHeader(flag: boolean): number {
        // if (flag == null || flag === this.bFixedHeader) {
        //     return ResultType.UnEdited;
        // }

        // this.bFixedHeader = flag;
        return ResultType.UnEdited;
    }

    public setHeaderReadOnly(bHeaderReadOnly: boolean, history?: History): void {
        if ( history && bHeaderReadOnly !== this.bHeaderReadOnly ) {
            history.addChange(new ChangeTableDeleteProtect(this, this.bHeaderReadOnly, bHeaderReadOnly));
        }
        this.bHeaderReadOnly = bHeaderReadOnly;
    }

    public isHeaderReadOnly(): boolean {
        return this.bHeaderReadOnly;
    }

    public setRepeatHeader(bRepeatHeader: boolean): boolean {
        if ( bRepeatHeader === this.bRepeatHeader || null == bRepeatHeader ) {
            return false;
        }

        this.bRepeatHeader = bRepeatHeader;
        return true;
    }

    public isRepeatHeader(): boolean {
        return this.bRepeatHeader;
    }

    // public getNISTableColNumber(): number {
    //     return (this.columnIDs ? this.columnIDs.length : -1);
    // }

    public getNISTableColIDs(): string[] {
        return this.columnIDs || [];
    }

    public setNISTableColIDs(ids: string[]): void {
        this.columnIDs = ids;
    }

    public setNISTableColID(index: number, id: string): boolean {
        if (!this.columnIDs || !id) {
            return false;
        }
        const colid = this.columnIDs[index];
        if (colid === id) {
            return true;
        }
        if (this.columnIDs.includes(id)) {
            return false;
        }

        this.columnIDs[index] = id;
        return true;
    }
}

export interface IRowInfoOfNewGrid {
    type: number;
    width: number;
    gridSpan: number;
}

// tslint:disable-next-line: max-classes-per-file
export class TableRecalcInfo {
    public bTableGrid: boolean;
    public bTableBorders: boolean;
    public bCellsAll: boolean;
    public recalcCells: any;

    constructor() {
        this.bTableGrid = true;
        this.bTableBorders = true;
        this.bCellsAll = true;
        this.recalcCells = {};
    }

    public reset(bNeedRecalc: boolean ): void {
        this.bTableGrid = bNeedRecalc;
        this.bTableBorders = bNeedRecalc;
        this.bCellsAll = bNeedRecalc;
        this.recalcCells = {};
    }

    public addCell(cell: any): void {
        if (cell) {
            this.recalcCells[cell.getId()] = cell;
        }
    }

    public checkCell(cell: any): boolean {
        if (this.bCellsAll || null != this.recalcCells[cell.getId()]) {
            return true;
        }

        return false;
    }

    public recalcBorders(): void {
        this.bTableBorders = true;
    }

    public recalcAllCells(): void {
        this.bCellsAll = true;
    }
}
