@import './global.less';

@messageColor: rgb(229, 245, 255);

@keyframes messageHidden {
    0% {
        opacity: 1;
    }
    99% {
        opacity: 0;
    }
    100% {
        height: 0;
        border: 0;
        overflow: hidden;
    }
}
.hz-editor-container {
    // .prop-name {
    //     input.warning {
    //         border: 1px solid red;
    //     }
    // }

    .prop-name {
        position: relative;

        &:focus-within .numberLogicMessage {
            display: block;
            animation: messageHidden 1s linear 3s;
            animation-fill-mode: forwards;
        }

        .numberLogicMessage {
            position: absolute;
            display: none;
            left: 0;
            width: 100%;
            top: 0;
            margin-top: -20px;
            font-size: 12px;
            line-height: 20px;
            padding: 0 4px;
            z-index: 100;
            background-color: white;
            border: 1px solid #ACB4C1;
        }
    }

    .cascade-prop-btn {
        height: 28px;
        color: @activeColor;
        font-size: @fontSize;
        font-family: @fontFamily;
        text-align: center;
        cursor: pointer;
        background: rgb(234, 237, 238);
        border-radius: 2px;
        border: 1px solid @activeColor;

        & > * {
            box-sizing: border-box;
        }
        & > i {
            display: inline-block;
            margin-right: 4px;
            color: inherit;
            font-style: normal;
            font-size: 18px;
            font-weight: bold;
            line-height: 26px;
        }

        & > span {
            display: inline-block;
            line-height: 1;
        }

        &.disable {
            color: @disabledColor;
            border: 1px solid @disabledColor;
        }
    }

    .newcontrol-cascade {
        font-size: @fontSize;
        font-family: @fontFamily;
        & * {
            box-sizing: border-box;
        }

        .no-data {
            width: 100%;
            color: @activeColor;
            font-size: @fontSize;
            font-family: @fontFamily;
            text-align: center;
            cursor: pointer;
            line-height: 30px;
            background: rgba(234, 237, 238);
            border-radius: 2px;
            border: 1px solid @activeColor;
        }
        .prop-btns {
            text-align: center;
        }
        ul > li {
            display: flex;
            border-bottom: 1px solid @borderColor;
            &:nth-child(1) {
                border-top: 1px solid @borderColor;
                line-height: 28px;
            }
            &:last-child {
                margin-bottom: 10px;
            }

            & > div {
                padding: 1px 3px;
                border-left: 1px solid @borderColor;
                &:last-child {
                    border-right: 1px solid @borderColor;
                }
                &:nth-child(2), &:nth-child(3), &:nth-child(4), &:nth-child(6) {
                    flex-basis: 108px;
                    min-width: 108px;
                }
                &:nth-child(1), &:nth-child(5)  {
                    flex-basis: 88px;
                    min-width: 88px;
                }
                &:last-child {
                    flex-grow: 5;
                    flex-basis: 560px;
                    // min-width: 60px;
                    // flex-shrink: 0;
                    line-height: 28px;
                    text-align: left;
                    & > *:hover {
                        color: @activeColor;
                        cursor: pointer;
                    }

                    & > * {
                        display: inline-block;
                        min-width: 12px;
                        margin-right: 6px;
                        font-size: 20px;
                        &:last-child {
                            margin-right: 0;
                        }
                    }

                    & > .prev, & > .next{
                        font-size: 15px;
                    }
                }
            }
        }
    }

    .cascade-center {
        height: 100%;
        & > div {
            height: 100%;
        }
        .hz-editor-container {
            height: 100%;
        }
    }

    .cascade-event {
        .w-150 {
            display: inline-block;
            width: 180px;
            line-height: 1;

            & + .right-auto {
                width: calc(100% - 183px);

                .message {
                    position: absolute;
                    left: 0;
                    top: 100%;

                    display: none;
                    grid-template-columns: 90px 1fr;
                    border: 1px solid #505050;
                    background-color: @messageColor;
                    width: 100%;
                    line-height: 1.2em;
                    padding: 3px;
                    z-index: 10;

                    & span:nth-child(odd) {
                        text-align: right;
                    }
                    & span:nth-child(even) {
                        padding-left: 5px;
                    }
                }

                .helptip {
                    position: absolute;
                    left: -25px;
                    top: 50%;
                    transform: translateY(-50%);
                    width: 20px;
                    height: 20px;
                    text-align: center;
                    line-height: 20px;

                    border-radius: 50%;
                    background-color: @messageColor;

                    &:hover + .message {
                        display: grid;
                    }
                }
            }
        }

        .align-top {
            vertical-align: top;
        }

        ul > li {
            margin-bottom: 4px;
            // & > span {
            //     display: inline-block;
            //     width: 160px;
            //     line-height: 1;
            // }

            // .event-select {
            //     display: inline-block;
            //     width: 168px;
            //     margin-left: 23px;
            // }

            // .event-btn {
            //     display: inline-block;
            //     i {
            //         display: inline-block;
            //         margin-left: 5px;
            //         font-style: normal;
            //         font-size: 20px;
            //         cursor: pointer;
            //     }
            // }
        }
    }

    .cascade-right {
      position: relative;
      width: 622px;
      height: 100%;
      max-width: 622px;
      min-width: 282px;
      padding-left: 12px;
      background: #fff;
      overflow-y: auto;
      z-index: 9;

      &::-webkit-scrollbar {
        width: 7px;
        height: 10px;
      }

      &::-webkit-scrollbar-thumb {
          /*滚动条里面小方块*/
          border-radius: 1px;
          background-color: #ddd;
      }
      &::-webkit-scrollbar-track {
          /*滚动条里面轨道*/
          box-shadow   : inset 0 0 2px rgba(0, 0, 0, 0.2);
          background   : #f2f2f2;
          border-radius: 7px;
      }

      .scroll-btn {
        position: absolute;
        left: 0;
        top: 0;
        z-index: 1;
        width: 5px;
        height: 100%;
        cursor: e-resize;
      }

      dd, dl, dt {
        margin: 0;
        padding: 0;
      }

      dl {
          width: 100%;

          & > dd:nth-of-type(1) {
            ul {
              width: 610px;
            }
            & > div {
              width: 100%;
              overflow: auto;
              &::-webkit-scrollbar {
                width: 7px;
                height: 10px;
              }
        
              &::-webkit-scrollbar-thumb {
                  /*滚动条里面小方块*/
                  border-radius: 1px;
                  background-color: #ddd;
              }
              &::-webkit-scrollbar-track {
                  /*滚动条里面轨道*/
                  box-shadow   : inset 0 0 2px rgba(0, 0, 0, 0.2);
                  background   : #f2f2f2;
                  border-radius: 7px;
              }

              ul {
                width: 610px;
              }
            }
          }
      }

      .title {
        padding-top: 15px;
        line-height: 20px;
      }

      & > div:first-child {
        margin-top: 30px;
        font-size: 16px;
        font-weight: bold;
        line-height:38px;
        & > label {
          margin-left: 20px;
          font-size: 15px;
        }
      }
      .cascade-event > div:nth-of-type(4) {
        padding-right: 10px;
      }
      .cascade-event-target {
        position: relative;
        left: 0;
        top: 0;
        padding-right: 10px;
        & > div:first-child {
          width: 100%;
        }
        & > div:last-child {
          display: none;
          position: absolute;
          left: 0;
          top: 32px;
          z-index: 99;
          width: 100%;
          padding: 10px;
          line-height: 28px;
          word-wrap: break-word;
          border: 1px solid #ccc;
          background: #fff;
          box-shadow: 0px 3px 7px 5px #ddd;
          pointer-events: none;
        }

        &:hover > div.active {
          display: inline-block;
        }
      }

      .searchid-icon {
        display: inline-block;
        width: 20px;
        margin-left: 5px;
        line-height: 28px;
        height: 28px;
        vertical-align: middle;
        font-size: 16px;
        text-align: center;
        cursor: pointer;
        background: url(../images/searchAdd.svg) no-repeat center;
      }

      .searchid-input {
        display: none;
        position: absolute;
        right: 0;
        top: 0;
        max-width: 100%;
        width: 300px;
        padding: 3px 3px;
        background: #fff;
        box-shadow: 2px 2px 7px 5px #aaa;

        & > div {
          display: flex;
          & > .btns {
            margin-left: 2px;
            cursor: pointer;
            line-height: 28px;
            vertical-align: middle;
            span {
              display: inline-block;
              padding: 0px 2px;
              color: @activeColor;
              line-height: 26px;
              border-radius: 2px;
              border: 1px solid #999;
            }
          }
          & > div:first-child {
            flex: 9;
          }
        }
        & > div:first-child {
          line-height: 28px;
        }
        & > div:nth-of-type(2) {
          margin-top: 3px;
          line-height: 28px;
          & > div:last-child {
            font-size: 20px;
            span {
              padding: 0px 8px;
            }
          }
        }
        & > div:last-child {
          margin-top: 3px;
        }
      }

      .cascade-event div:last-child {
        li {
          display: flex;
          flex-wrap: wrap;
          margin-bottom: 0;
          & > div:first-child {
            display: flex;
            flex: 1;
            min-width: 190px;
            margin-bottom: 10px;
            margin-right: 8px;
            & > span {
              flex: 10;
            }
            & > div {
              flex: 1;
              min-width: 32px;
            }
          }
          & > div:last-child {
            display: flex;
            flex: 1;
            min-width: 190px;
            margin-bottom: 10px;
            & > div:first-child {
              flex: 10;
            }
            & > div:last-child {
              flex: 1;
              min-width: 40px;
              margin-right: 8px;
              i {
                font-size: 20px;
                font-style: normal;
                padding-left: 5px;
                cursor: pointer;
                &:hover {
                  color: @activeColor;
                }
              }
            }
          }
        }
      }

      .cascade-manager {
        max-width: 610px;
        ul > li > div {
          &:nth-of-type(1) {
            min-width: 80px;
            flex-basis: 80px;
          }
          &:nth-of-type(2), &:nth-of-type(3), &:nth-of-type(4), &:nth-of-type(5) {
            min-width: 90px;
            flex-basis: 90px;
          }
          &:nth-of-type(6) {
            display: flex;
            flex: 3;
            min-width: 80;
            & > div:first-child {
              flex: 9;
            }
          }

          &:nth-of-type(7) {
            flex-basis: 43px;
            max-width: 43px;
          }
        }
        
        .prop-name {
          position: inherit;
        }
      }
    }
      .cascade-center {
        flex: 10;
        padding-top: 12px;
        background-color: #ebeff2;
        .hz-editor-container {
          background: #fff;
        }
      }

      .editor-cascade-manager {
        div.dialog-box-body {
          width: 100%;
          & > div:first-child {
            min-width: 1000px;
          }
          padding: 0;
          .left-hide > .cascade-left {
            display: none;
          }
          .right-hide > .cascade-right {
            display: none;
          }

          .move > .cascade-center {
            pointer-events: none;
          }
        }
        .title-set {
          pointer-events: none;
          span:nth-of-type(2) {
            padding: 0 14px;
          }
          .btn-1 {
            display: inline-block;
            color: #3664D9;
            cursor: pointer;
            pointer-events: all;
          }
          .btn-2 {
            pointer-events: all;
            float: right;
            color: #3664D9;
            margin-right: 25px;
            cursor: pointer;
          }
          div i, div span{
            pointer-events: none;
          }
        }
      }
  
      .editor-cascade {
        display: flex;
        height: calc(100% - 4px);
        .title {
          line-height: 38px;
        }
  
        &.scroll {
          .auto-hidden-left {
            z-index: 99;
          }
          .cascade-center {
            margin: 0;
          }
  
          .cascade-right {
            position: relative;
            .left-icon, .right-icon {
              position: absolute;
              top: 5px;
              z-index: 9;
              width: 24px;
              height: 24px;
              line-height: 16px;
              text-align: center;
              font-size: 28px;
              border: 1px dashed @activeColor;
              cursor: pointer;
              &:hover {
                color: @activeColor;
              }
            }
  
            .left-icon {
              left: 10%;
              &::after {
                content: '«';
              }
            }
  
            .right-icon {
              left: 10%;
              margin-left: 34px;
              &::after {
                content: '»';
              }
            }
          }
        }
      }
  
      // .auto-hidden-left {
      //     position: absolute;
      //     top: 0;
      //     font-size: 26px;
      //     cursor: pointer;
      //     color: @activeColor;
      //     height: 100%;
      //     display: flex;
      //     align-items: center;
      //     left: 234px;
      //     &::before {
      //         content: '«'; // &raquo
      //     }
      //     &.hide {
      //       left: 2px;
      //       &::before {
      //         content: '»'; // &laquo;
      //       }
      //       & + div {
      //         display: none;
      //       }
      //     }
      //     &:hover {
      //       background-color: rgba(0, 119, 255, 0.3);
      //     }
      // }
  
  
  
      .cascade-left {
        width: 200px;
        min-width: 200px;
        padding-top: 20px;
        padding-left: 12px;
        overflow: hidden;
        overflow-y: auto;
        background: #fff;
        & #colorMap {
          width: 100%;
          height: 30px;
          padding: 2px 0px;
          &>* {
              vertical-align: middle;
          }

          &.colorHidden {
              display: none;
          }

          & .colorBlock {
              display: inline-block;
              width: 20px;
              height: 20px;
          }

          & span {
              width: 20px;
              line-height: 20px;
              padding: 0 4px;
          }
        }
  
        & span.title {
          flex: 7;
          font-size: 14px;
        }
  
        & > div:first-child {
          display: flex;
          height: 30px;
          line-height: 30px;
          & > span {
            flex: 7;
            font-size: 16px;
          }
          & > div {
            width: 110px;
            font-size: 14px;
          }
  
          .title-icon {
            & > div {
              display: inline-block;
              position: relative;
              width: 28px;
              height: 18px;
              margin-right: 3px;
              vertical-align: middle;
              cursor: pointer;
              border: 2px solid #ACB4C1;
              box-sizing: border-box;
              border-radius: 9px;
              i {
                display: inline-block;
                position: absolute;
                left: 2px;
                top: 2px;
                width: 10px;
                height: 10px;
                border-radius: 50%;
                background: #ACB4C1;
                pointer-events: none;
              }
              &.active {
                border-color: @activeColor;
                i {
                  left: 11px;
                  background: @activeColor;
                }
              }
            }
            & > span {
              display: inline-block;
              vertical-align: middle;
            }
          }
        }
        & > div:nth-of-type(2), & > div:nth-of-type(3) {
          & > i {
            display: inline-block;
            font-style: normal;
            font-size: 17px;
            transform: translateX(-3px);
          }
          & > span {
            cursor: pointer;
          }
          & > ul {
            display: none;
          }
          &.title-active {
            & > i {
              transform: rotate(90deg) translateX(0px);
            }
            & > ul {
              display: block;
            }
          }
        }
        ul > li {
          margin-left: 10px;
          cursor: pointer;
          span {
            font-size: 13px;
            line-height: 30px;
            pointer-events: none;
            &.active {
              font-weight: bold;
              color: @activeColor;
            }
          }
        }
      }
}
