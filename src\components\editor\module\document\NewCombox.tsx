import * as React from 'react';
import { DocumentCore } from '../../../../model/DocumentCore';
import { INewControlProperty, NewControlType, CodeValueItem, ViewModeType } from '../../../../common/commonDefines';
import retrieve from '../../../../common/Retrieve';
import Checkbox from '../../ui/CheckboxItem';
import Input from '../../ui/Input';
import {
    GlobalEvent as gEvent,
    GlobalEventName as gEventName,
} from '../../../../common/GlobalEvent';
import Radiobox from '../../ui/RadioboxItem';
import { getPagePadding } from '../../../../common/commonMethods';
import { getTheme } from '@hz-editor/theme';

interface INewComboxListProps {
    documentCore: DocumentCore;
    newControlPropety: INewControlProperty;
    scale?: number;
    closeNewComboxList?: () => void;
    refresh?: (bClose?: boolean) => void;
    position?: {left: number, top: number, width: number};
    bFromEnter?: boolean;
    lineHeight?: number;
    host: any;
}

interface INewComboxListPropsState {
    curIndex?: number;      // 当前选中item索引
    bReflash: boolean;
    // curRadioIndex?: number; // which radio is selected
}

export class NewCombox extends React.Component<INewComboxListProps, INewComboxListPropsState> {
    private bSelect: boolean;
    private selectItemsPos: Set<string>; // the items that are of checked value(may not show)
    private bMulti: boolean;
    private newControlItems: CodeValueItem[];
    private searchItems: CodeValueItem[];
    private sortListItems: CodeValueItem[];
    private keyword: string;
    private unSearchKeyword: string;
    private searchTime: any;
    private inputRef: any;
    private bSearch: boolean;
    private documentCore: DocumentCore;
    private newControlPropety: INewControlProperty;
    private containerRef: any;
    private _tabIndex: number;
    private top: number;
    private radioSort: boolean; // whether to sort radio list
    private ColWidth: number; // 多列时每列的宽度
    private _dblTimer: any; // 用于双击控制的标记

    constructor(props: any) {
        super(props);
        this.documentCore = props.documentCore;
        const newControlPropety = this.newControlPropety = props.newControlPropety;
        this.state = {
            curIndex: -1,
            bReflash: false,
            // curRadioIndex: -1,
        };
        try {
            const newControl: any = this.documentCore.getNewControlByName(newControlPropety.newControlName);
            let bChanged: boolean;
            if (newControl && typeof newControl.updateSelectItemByText === 'function') {
                try {
                    bChanged = newControl.updateSelectItemByText();
                } catch (error) {
                    console.log(error);
                }
            }
            this.bSelect = false;
            this.selectItemsPos = new Set();
            let list;
            if (bChanged) {
                list = newControl.getItemList()
                .map((item) => {
                    return {...item};
                });
            } else {
                list = newControlPropety.newControlItems;
            }
            // this.bMulti = false;
            this.searchItems = this.newControlItems = list;
            this.inputRef = React.createRef();
            this.containerRef = React.createRef();
            this.bSearch = this.newControlPropety.retrieve;
            this._tabIndex = 1;
            this.bMulti = ( NewControlType.MultiCombox === newControlPropety.newControlType
                || NewControlType.MultiListBox === newControlPropety.newControlType);
            this.radioSort = true;
            this.init();
        } catch (error) {
            console.log(error);
        }

    }

    public render(): any {
        const position = this.props.position;
        let left: number;
        let top: number;
        let width: number;
        if (position) {
            left = position.left;
            top = position.top;
            width = position.width;
        } else {
            try {
                const scale = this.props.scale;
                const documentCore = this.documentCore;
                const bounds = documentCore.getNewControlsFocusBounds();
                const fixLine = bounds.bounds[0]; // 定位到首行
                // const lastLine = bounds.bounds[bounds.bounds.length - 1];
                const viewMode = documentCore.getViewMode();
                let subHeight = 0;
                let subLeft = 0;
                if (viewMode === ViewModeType.WebView) {
                    subHeight -= getPagePadding(documentCore).top;
                } else if (viewMode === ViewModeType.CompactView) {
                    subLeft = getPagePadding(documentCore).left + getPagePadding.PaddingLeft;
                }

                left = (fixLine.x - subLeft) * scale;
                top = (fixLine.y + fixLine.height + subHeight) * scale; // + 3;
                let minWidth = 150;
                if (this.newControlPropety.bShowCodeAndValue === true) {
                    minWidth = 200;
                }
                width = minWidth > fixLine.width ? minWidth : fixLine.width;
            } catch (error) {
                console.log(error);
            }
        }
        this.top = top;
        this.ColWidth = (width - 70);

        // modify left according to page xLimit
        const pScale = this.props.scale != null ? this.props.scale : 1;
        const logicDocument = this.props.documentCore.getDocument();
        const pages = logicDocument?.getPages();
        if (pages && pages.length > 0) {
            let xLimit = pages[0].xLimit * pScale;
            // editor can have a delta distance, which is needed for addressbox to determine new xLimit
            // if (position != null && position.deltaLeft != null) {
            //     // console.log(position.deltaLeft)
            //     xLimit += position.deltaLeft;
            // }
            if (xLimit > 0) {
                if (left + width > xLimit) {
                    left = xLimit - width;
                }
            }
        }
        return (
            <div className='new-control-list' tabIndex={-1} ref={this.containerRef} style={{top, left, width}}>
                {this.renderSearchContent()}
                <div className='combox-item-panel'>
                    {this.renderListHeader()}
                    {this.renderTable()}
                </div>
                {this.renderBtns()}
            </div>
        );
    }

    public componentDidMount(): void {
        // const position = this.props.position;
        // if (position) {
        //     const dom = this.containerRef.current;
        //     let top = position.top;
        //     const maxHeight = document.firstElementChild.clientHeight - top;
        //     const currentHeight = dom.clientHeight;
        //     const subHeight = currentHeight - maxHeight;
        //     if (subHeight > 1) {
        //         top -= subHeight + 50;
        //         dom.style.top = top + 'px';
        //     }
        // }

        const dom = this.containerRef.current;
        if (!dom) {
            return;
        }

        try {
            let top = dom.getBoundingClientRect().top;
            const maxHeight = document.firstElementChild.clientHeight - top;
            const currentHeight = dom.clientHeight;
            const subHeight = currentHeight - maxHeight;
            if (subHeight > 1) {
                // menu up
                top = this.top - currentHeight - this.props.lineHeight; // - 5;
                dom.style.top = top + 'px';
            }
            const lis = dom.querySelectorAll('.combox-item-list li[data-code]');
            if (lis && this.selectItemsPos.size > 0) {
                for (const li of lis) {
                    if (this.selectItemsPos.has(li.dataset.code)) {
                        const panel = li.parentElement.parentElement;
                        panel.scrollTop = li.offsetTop - panel.offsetTop;
                        break;
                    }
                }
            }

        } catch (error) {
            console.log(error);
        }
        dom.addEventListener('dblclick', this.onDblClick);
        dom.addEventListener('click', this.containerClick);
        dom.addEventListener('keydown', this.keydown);
        dom.focus();

        if (this.bSearch) {
            setTimeout(() => {
                if ( this.inputRef.current ) {
                    this.inputRef.current.onFocus();
                }
            }, 300);
        } else if (this.props.bFromEnter) {
            this.mousedownEvent(null);
            // setTimeout(() => {
            //     dom.querySelector('li').focus();
            // }, 150);
        }
        // if (this.bMulti === false) {
        //     // radio list scroll to view
        //     setTimeout(() => {
        //         const radioBoxChecked = this.containerRef.current.querySelector('.radiobox-icon-checked');
        //         if (radioBoxChecked) {
        //             // outer scroll bar shouldnt move
        //             radioBoxChecked.scrollIntoView({behavior: 'smooth', block: 'end'});
        //             // radioBoxChecked.scrollTo(0, 0)
        //         }
        //     }, 110);
        // }
    }

    public componentWillUnmount(): void {
        const dom = this.containerRef.current;
        if (!dom) {
            return;
        }
        dom.removeEventListener('dblclick', this.onDblClick);
        dom.removeEventListener('click', this.containerClick);
        dom.removeEventListener('keydown', this.keydown);
        gEvent.deleteEvent(this.documentCore.getCurrentId(), gEventName.Mouseup, this.mousedownEvent);
        // dom.removeEventListener('focus', this.focus);
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        const newControlPropety = nextProps.newControlPropety;
        this.searchItems = this.newControlItems = newControlPropety.newControlItems;
        this.bSearch = newControlPropety.retrieve;
        this.bMulti = ( NewControlType.MultiCombox === newControlPropety.newControlType
            || NewControlType.MultiListBox === newControlPropety.newControlType);
        if (this.bMulti === false) {
            this.radioSort = true;
        }
        this.reset();
    }

    private onDblClick = (e: any): void => {
        e.preventDefault();
        e.stopPropagation();
    }

    private keydown = (e: any): void => {
        const code = e.keyCode;
        let index: number;
        switch (code) {
            case 13: { // enter
                const searchItems = this.sortListItems;
                if (this.bMulti) {
                    // if (this.selectItemsPos.size === 0) {
                    //     this.selectItemsPos.add(searchItems[0].code);
                    // }
                    // this.confirm();

                    const curCode = searchItems[this._tabIndex - 1].code;
                    if (this.selectItemsPos.has(curCode)) {
                        this.selectItemsPos.delete(curCode);
                    } else {
                        this.selectItemsPos.add(curCode);
                    }
                    this.setState({});

                } else {
                    this.onClick(searchItems[this._tabIndex - 1].code);
                }
                break;
            }
            case 9: {
                this.props.host.updateRefreshFlag(false);
                this.close();
                const props = this.props;
                const res = props.documentCore.jumpToNextNewControl(props.newControlPropety.newControlName);
                this.props.host.updateRefreshFlag(true);
                if (res === true) {
                    props.refresh();
                }
                break;
            }
            case 38: {
                index = this._tabIndex;
                if (index > 1) {
                    this._tabIndex = --index;
                }

                break;
            }
            case 40: {
                index = this._tabIndex;
                const length = this.newControlItems.length;
                if (index < length ) {
                    this._tabIndex = ++index;
                }
                break;
            }
        }
        // if (code === 13) {
        //     bStop = true;
        // }
        // else if (code === 9) {
        //     const li = this.containerRef.current.querySelector(`li:nth-of-type(${++this._tabIndex})`);
        //     if (li) {
        //         li.focus();
        //     }

        //     bStop = true;
        // }
        if (index !== undefined) {
            const li = this.containerRef.current.querySelector(`.combox-item-list li:nth-of-type(${index})`);
            if (li) {
                li.focus();
            }
        }
        e.preventDefault();
        e.stopPropagation();
    }

    private containerClick = (e: any): void => {
        if (this.bMulti === false) {
            this.radioSort = false;
        }
        const target = e.target;
        const tagName = target.nodeName;
        // if (tagName === 'LI') {
        //     const code = target.getAttribute('data-code');
        //     if (!code) {
        //         return;
        //     }
        //     this.onClick(code);
        // } else {
        //     this.focusLiItem(target);
        //     if (tagName === 'LABEL') {
        //         const index = target.getAttribute('data-index');
        //         if (!index) {
        //             return;
        //         }
        //         switch (index) {
        //             case '0': {
        //                 this.close();
        //                 break;
        //             }
        //             case '1': {
        //                 this.setReset();
        //                 break;
        //             }
        //             case '2': {
        //                 this.confirm();
        //                 break;
        //             }
        //         }
        //     }
        // }
        if (tagName === 'LABEL') {
            const index = target.getAttribute('data-index');
            if (!index) {
                return;
            }
            switch (index) {
                case '0': {
                    this.close();
                    break;
                }
                case '1': {
                    this.setReset();
                    break;
                }
                case '2': {
                    this.confirm();
                    break;
                }
            }
        }
        e.stopPropagation();
    }

    private focusLiItem(target: HTMLElement): HTMLElement {
        if (!target) {
            return;
        }
        if (target.nodeName === 'LI') {
            this._tabIndex = +target.getAttribute('tabIndex') + 1;
            return target;
        }

        return this.focusLiItem(target.parentNode as any);
    }

    private init(): void {
        this.reset();
        gEvent.addEvent(this.documentCore.getCurrentId(), gEventName.Mouseup, this.mousedownEvent);
    }

    private mousedownEvent = (e: any): void => {
        setTimeout(() => {
            if (!this.searchItems || this.searchItems.length === 0) {
                return;
            }
            const dom = this.containerRef.current;
            if (dom) {
                this._tabIndex = 1;
                dom.querySelector('li').focus();
            }
        });
    }

    // private test(): void {
    //     const arrs = ['是的访问法个', '的佛那个人购买费共计', '低分狗溶剂法狗儿烹饪机构的交付给', '的方便么卤肉卷偶觉得发给我京东方',
    //     'fgml的烦恼够味儿解耦股么地方个人个', '么法平均打排位非贫困的分配给，二等分更肉片京东方', '人提供巨额融合柔而佛光和偶然间鹅肉感觉',
    //     '地方个人偶尔加工恶如，我的狗耳机而孤独感解耦'];
    //     const len = arrs.length;
    //     let arrIndex: number = 0;
    //     const datas: CodeValueItem[] = [];
    //     for (let index = 0; index < 1100; index++) {
    //         datas.push(new CodeValueItem(arrs[arrIndex++] + index, index.toString(), false));
    //         if (arrIndex === len) {
    //             arrIndex = 0;
    //         }
    //     }
    //     retrieve.search('s', 'a');
    //     this.searchItems = this.newControlItems = datas;
    // }

    private handleSearch = (value: string, name: string, e: any): void => {
        if (this.bMulti === false) {
            this.radioSort = true;
        }
        if (this.handleKeyDown(e)) {
            return;
        }
        const keyword = value || '';
        // if (/[\u4E00-\u9FA5]/.test(keyword)) {
        //     this.keyword = '';
        //     setTimeout(() => {
        //         e.target.value = '';
        //     });
        //     // this.setState({});
        //     return;
        // }
        this.keyword = keyword;
        this.setState({bReflash: !this.state.bReflash});
        clearTimeout(this.searchTime);
        this.searchTime = setTimeout(() => {
            this.search();
        }, 50);
    }

    private handleFocus(): void {
        if (!this.inputRef || !this.inputRef.current) {
            return;
        }
        setTimeout(() => {
            this.inputRef.current.onFocus();
        }, 50);
    }

    private handleKeyDown = (e: any): boolean => {
        if (e.keyCode === 13) {
            this.confirm();
            e.stopPropagation();
            return true;
        }
        return false;
    }

    private async search(): Promise<void> {
        const list = this.newControlItems;
        if (!list || list.length === 0) {
            return;
        }

        if (!this.keyword) {
            this.searchItems = this.newControlItems;
            // this.selectItemsPos.clear();
            this.bSelect = false;
            this.setState({bReflash: !this.state.bReflash});
            return;
        }

        if (this.unSearchKeyword && this.keyword.indexOf(this.unSearchKeyword) === 0) {
            return;
        }

        const items = this.searchItems = [];
        const keyword = this.keyword;
        for (const item of list) {
            const res = await retrieve.search(item.code, keyword, item.value);
            if (res.bSuccess === true) {
                items.push(item);
            }
        }

        if (items.length === 0) {
            this.unSearchKeyword = this.keyword;
            // this.selectItemsPos.clear();
            this.bSelect = true;
        } else {
            items.sort((a, b) => {
                return a.code.length - b.code.length;
            });
            // console.log(items)
            // if (this.bMulti === false) {
            //     this.selectItemsPos.clear();
            //     this.selectItemsPos.add(items[0].code);
            // }
            this.bSelect = true;
            this.unSearchKeyword = undefined;
            // this.setSelect(0);
        }
        this.setState({bReflash: !this.state.bReflash});
    }

    private renderBtns(): any {
        // if (this.bMulti === false) {
        //     return false;
        // }

        // return (
        //     <div className='list-button'>
        //         <label onClick={this.setReset} data-index={1}>清空</label>
        //         <label onClick={this.close} data-index={0} style={{color: 'inherit'}}>取消</label>
        //     </div>
        // );
        const confirmLabel = getTheme().NewControl.ConfirmStyle === 2 ? '确定' : '取消';
        if (this.bMulti === false) {
            return (
                <div className='list-button'>
                    <label onClick={this.setReset} data-index={1}>清空</label>
                    <label onClick={this.close} data-index={0}>{confirmLabel}</label>
                </div>
            );
        }
        return (
            <div className='list-button'>
                <label onClick={this.setReset} data-index={1}>清空</label>
                <label onClick={this.close} data-index={0}>{confirmLabel}</label>
                {/* <label onClick={this.confirm} data-index={2} className='confirm-label'>确认</label> */}
            </div>
        );
    }

    private renderSearchContent(): any {
        if (this.bSearch !== true) {
            return null;
        }
        return (
            <div className='search-container'>
                <Input
                    ref={this.inputRef}
                    value={this.keyword}
                    // onKeyDown={this.handleKeyDown}
                    onChange={this.handleSearch}
                />
            </div>
        );
    }

    private renderListHeader(): any {
        const newControlPropety = this.newControlPropety;
        if (newControlPropety.bShowCodeAndValue !== true) {
            return null;
        }
        let label = newControlPropety.codeLabel;
        let value = newControlPropety.valueLabel;
        if (label === '' && value === '') {
            return null;
        }

        if (label == null) {
            label = '名称';
        }

        if (value == null) {
            value = '值';
        }
        const mulStyle = {
            width: this.ColWidth / 2 + 'px'
        };
        return (
            <React.Fragment>
                <div className='list-header'>
                    <li>
                        <label/>
                        <span className='mul-col' style={mulStyle}>{label}</span>
                        <span className='mul-col' style={mulStyle}>{value}</span>
                    </li>
                </div>
            </React.Fragment>
        );
    }

    private renderTable(): any {
        const list = this.searchItems;
        if ( null == list || 0 === list.length ) {
            return null;
        } else {
            return (
                <div className='combox-item-list'>
                    {this.renderList()}
                </div>
            );
        }
    }

    private renderList(): any {
        const list = this.searchItems;
        if (!list || 0 === list.length ) {
            return null;
        }
        const bMulti = this.bMulti;
        // this.selectItemsPos.clear();
        const selectItemsPos = this.selectItemsPos; // the items that are really checked(may not show)
        // console.log(selectItemsPos)
        let sourceList = this.sortListItems;
        if (bMulti === true || this.sortListItems == null ||
            (bMulti === false && this.radioSort === true)) {
            // sourceList = this.sortListItems = this.sortList(list.slice(), selectItemsPos);
            sourceList = this.sortListItems = list.slice();
        }
        // console.log(sourceList) // sourceList: the list to show in dropdown
        return sourceList.map((item, index) => {
            const bSelect = selectItemsPos.has(item.code);
            if (bMulti) {
                return this.renderMultiLabel(item, bSelect, index);
            } else {
                return this.renderLabel(item, bSelect, index);
            }
        });
    }

    private renderMultiLabel(item: CodeValueItem, bChecked: boolean, index: number): any {
        return (
            <li key={item.code} data-code={item.code} tabIndex={index}>
                <Checkbox
                    value={bChecked}
                    name={item.code}
                    onChange={this.onChange}
                >
                    {this.renderLableText(item)}
                </Checkbox>
            </li>
        );
    }

    private renderLabel(item: CodeValueItem, bChecked: boolean, index: number): any {
        return (
            // <li key={item.code} className={bChecked ? 'active' : ''} data-code={item.code} tabIndex={index}>
            //     {item.code}
            // </li>
            <li key={item.code} data-code={item.code} tabIndex={index}>
                <Radiobox
                    value={bChecked}
                    name={item.code}
                    onChange={this.onRadioChange}
                    index={index}
                    // curIndex={this.state.curRadioIndex}
                >
                    {this.renderLableText(item)}
                </Radiobox>
            </li>
        );
    }

    private renderLableText(item: CodeValueItem): any {
        if (this.newControlPropety.bShowCodeAndValue !== true) {
            return <span className='one-col' style={{width: this.ColWidth + 'px'}}>{item.code}</span>;
        }

        const mulStyle = {
            width: this.ColWidth / 2 + 'px'
        };
        return (
            <React.Fragment>
                <span className='mul-col' style={mulStyle}>{item.code}</span>
                <span className='mul-col' style={mulStyle}>{item.value}</span>
            </React.Fragment>
        );
    }

    private sortList(items: CodeValueItem[], selectedItems: Set<string>): CodeValueItem[] {
        if (selectedItems.size === 0) {
            return items;
        }

        let arrs = [];
        for (const code of selectedItems) {
            const index = items.findIndex((item) => item.code === code);
            if (index !== -1) {
                arrs.push(items.splice(index, 1)[0]);
            }
        }
        arrs = arrs.concat(items);
        return arrs;
    }

    private onChange = (value: boolean, name: string, e: any, bRadioIndex?: number): void => {
        if (bRadioIndex == null) {
            // 单击操作时立即执行，双击时操作废弃
            if (!this._dblTimer) {
                if (value === true) {
                    this.selectItemsPos.add(name);
                } else {
                    this.selectItemsPos.delete(name);
                }
                this.props.host.updateRefreshFlag(false);
                // tslint:disable-next-line: no-unused-expression
                if (this.getSelectdNewControl()) {
                    this.props.host.updateRefreshFlag(true);
                    this.props.refresh(false);
                } else {
                    this.props.host.updateRefreshFlag(true);
                }
                this._dblTimer = setTimeout(() => {
                    this._dblTimer = null;
                }, 200);
            } else {
                clearTimeout(this._dblTimer);
                this._dblTimer = null;
                const delayFunc = async () => {
                    await Promise.resolve(true);
                    this.confirm();
                };
                delayFunc();
            }
        } else {
            if (value === true) {
                this.selectItemsPos.clear();
                this.selectItemsPos.add(name);
            } else {
                // if (this.selectItemsPos.size > 0) {
                //     this.selectItemsPos.clear();
                // }
            }
            // console.log(this.selectItemsPos)
            // this.setState({curRadioIndex: bRadioIndex})
            // need refresh, as to uncheck the potentially checked list
            this.setState({});
        }
    }

    private onRadioChange = (value: boolean, name: string, e: any, bRadioIndex?: number): void => {
        this.onClick(name);
    }

    private onClick = (code: string): void => {
        this.selectItemsPos.clear();
        this.selectItemsPos.add(code);
        this.confirm();
        // this.getSelectdNewControl();
    }

    private getSelectdNewControl(): boolean {
        const { documentCore, newControlPropety } = this.props;
        const list = this.searchItems;
        if ( null == list || 0 === list.length ) {
            return;
        }

        const pos = [];
        const sourceList = this.newControlItems;
        // const bSmame = sourceList === list;
        this.selectItemsPos.forEach((value) => {
            const index = sourceList.findIndex((item) => item.code === value);
            pos.push(index);
        });
        // if (pos.length === 0) {
        //     return;
        // }
        // if (this.bMulti === false) {
        //     this.selectItemsPos.clear();
        // }
        // const newControl = documentCore.getNewControlByName(newControlPropety.newControlName);
        documentCore.setNewControlListItems(newControlPropety.newControlName, pos);
        return true;
    }

    private setReset = (): void => {
        this.keyword = '';
        // let bFlag = true;
        // const oldItems = this._oldSelectedItem;
        const count = this.selectItemsPos.size;
        this.searchItems = this.newControlItems;
        this.selectItemsPos.clear();
        this.props.host.updateRefreshFlag(false);
        const newControl = this.documentCore.resetNewControlSelectItem(this.newControlPropety.newControlName);
        // newControl.resetSelectItems();
        // this.setState({bReflash: !this.state.bReflash});
        this.close();
        this.props.host.updateRefreshFlag(true);
        this.props.refresh();
    }

    private reset = (bRefresh: boolean = false): void => {
        const datas = this.newControlItems;
        const selectItemsPos = this.selectItemsPos;
        selectItemsPos.clear();
        if ( null != datas && datas.length ) {
            datas.forEach((item) => {
                if (item.bSelect === true) {
                    selectItemsPos.add(item.code);
                }
            });
        }
        this.keyword = '';
        this.searchItems = this.newControlItems;
        if (bRefresh === true) {
            this.setState({bReflash: !this.state.bReflash});
        }
    }

    private confirm = () => {
        this.props.host.updateRefreshFlag(false);
        const res = this.getSelectdNewControl();

        if (this.props.bFromEnter === true) {
            this.documentCore.selectNewControlContent(this.newControlPropety?.newControlName);
        }
        this.props.host.updateRefreshFlag(true);
        if (res === true) {
            this.props.refresh();
            return;
        }
        this.close();
    }

    private close = () => {
        // this.selectItemsPos.clear();
        this.props.closeNewComboxList();
    }

    // private renderListdd(): any {
    //     const list = this.searchItems;
    //     this.bMulti = ( NewControlType.MultiCombox === this.props.newControlPropety.newControlType
    //         || NewControlType.MultiListBox === this.props.newControlPropety.newControlType );
    //     if ( null == list || 0 === list.length ) {
    //         return ;
    //     }

    //     if ( false === this.bSelect ) {
    //         list.forEach((item, index) => {
    //             if ( true === item.bSelect ) {
    //                 this.selectItemsPos.add(index);
    //             }
    //         });
    //     }

    //     const newArry = [];
    //     if ( true === this.bMulti ) {
    //         for ( let index = 0; index < list.length; index++) {
    //             const item = list[index];
    //             const color = this.selectItemsPos.has(index) ? '#009FFF' : '' ;

    //             newArry.push((<tr className='table-tr' key={index} id={index.toString()} style={{background: color}}>
    //                         <td id={index.toString()}>
    //                             <input type='checkbox' id={index.toString()} checked={this.selectItemsPos.has(index)}
    // onChange={this.handleMouseDown.bind(this)}/>
    //                             <label htmlFor={index.toString()} id={index.toString()}>{item.code}</label>
    //                         </td>
    //                     </tr>));
    //         }
    //     } else {
    //         for ( let index = 0; index < list.length; index++) {
    //             const item = list[index];
    //             const color = this.selectItemsPos.has(index) ? '#009FFF' : '' ;

    //             newArry.push((<tr className='table-tr' key={index} id={index.toString()} style={{background: color}}>
    //                         <td id={index.toString()}>
    //                             <label id={index.toString()}>{item.code}</label>
    //                         </td>
    //                     </tr>));
    //         }
    //     }

    //     return newArry;
    // }

    // private handleMouseDown(type: string, e: any): void {
    //     if ( 'select-combox-item' === type ) {
    //         const table = document.getElementById('combox-item') as HTMLTableElement;
    //         if ( null != table && 0 < table.rows.length
    //             && ( 'TD' === e.target.tagName || 'INPUT' === e.target.tagName || 'LABEL' === e.target.tagName) ) {
    //             const curRowIndex = Number.parseInt(e.target.id, 0);
    //             this.bSelect = true;

    //             if ( null != curRowIndex ) {
    //                 if ( this.state.curIndex !== curRowIndex ) {
    //                     if ( -1 !== this.state.curIndex && table.rows[this.state.curIndex] ) {
    //                         table.rows[this.state.curIndex].bgColor = '';
    //                     }
    //                     table.rows[curRowIndex].bgColor = '#009FFF';

    //                     // const td = table.rows[curRowIndex].children[0];
    //                     // const input = td.firstElementChild as HTMLInputElement;

    //                     if ( false === this.bMulti ) {
    //                         this.selectItemsPos.clear();
    //                     }
    //                 }

    //                 if (this.bMulti === false) {
    //                     if (this.selectItemsPos.has(curRowIndex) === false) {
    //                         this.selectItemsPos.add(curRowIndex);
    //                     }

    //                     this.selectOneControl();
    //                     return;
    //                 }

    //                 this.handleFocus();

    //                 if ( false === this.selectItemsPos.has(curRowIndex) ) {
    //                     this.selectItemsPos.add(curRowIndex);
    //                 } else {
    //                     this.selectItemsPos.delete(curRowIndex);
    //                 }
    //                 this.setState({curIndex: curRowIndex});
    //             }
    //         }
    //     }
    // }

    // private handleButtonClick(e: any): void {
    //     const { documentCore, newControlPropety } = this.props;
    //     const newControl = documentCore.getNewControlByName(newControlPropety.newControlName);
    //     const list = this.searchItems;

    //     this.bSelect = false;

    //     if ( null == newControl ) {
    //         return ;
    //     }

    //     switch (e.target.innerHTML) {
    //         case '确定':
    //             if ( null == list || 0 === list.length ) {
    //                 break;
    //             }

    //             const pos = [];
    //             const sourceList = this.newControlItems;
    //             const bStart = sourceList.length !== list.length;
    //             this.selectItemsPos.forEach((value) => {
    //                 if (bStart) {
    //                     const data = list[value];
    //                     value = sourceList.findIndex((item) => item === data);
    //                 }
    //                 pos.push(value);
    //             });
    //             newControl.setNewControlListItems(pos);
    //             this.props.refresh();

    //         case '取消':
    //             break;

    //         case '复位':
    //             this.selectItemsPos.clear();
    //             newControl.resetSelectItems();
    //             this.props.refresh();
    //             break;

    //         default:
    //             break;
    //     }

    //     this.props.closeNewComboxList();
    // }
}
