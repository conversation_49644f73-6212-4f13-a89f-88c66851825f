
export class TableAnchorPosition {
    public calcX: number;
    public calcY: number;

    public x: number;
    public y: number;
    public width: number;
    public height: number;
    public leftMargin: number;
    public rightMargin: number;
    public topMargin: number;
    public bottomMargin: number;

    public pageWidth: number;
    public pageHeight: number;
    public pageTop: number;
    public pageBottom: number;

    public xMin: number;
    public xMax: number;
    public yMin: number;
    public yMax: number;

    constructor() {
        this.calcX = 0;
        this.calcY = 0;

        this.x = 0;
        this.y = 0;
        this.width = 0;
        this.height = 0;
        this.leftMargin = 0;
        this.rightMargin = 0;
        this.topMargin = 0;
        this.bottomMargin = 0;

        this.pageWidth = 0;  // 表格页面宽度
        this.pageHeight = 0;
        this.pageTop = 0;
        this.pageBottom = 0;

        this.xMin = 0;
        this.xMax = 0;
        this.yMin = 0;
        this.yMax = 0;
    }

    public setX(width: number, x: number, leftMargin: number, rightMargin: number,
                pageWidth: number, xMin: number, xMax: number): void {
        this.x = x;
        this.width = width;
        this.leftMargin = leftMargin;
        this.rightMargin = rightMargin;

        this.pageWidth = pageWidth;

        this.xMin = xMin;
        this.xMax = xMax;
    }

    public setY(height: number, y: number, topMargin: number, bottomMargin: number, pageHeight: number,
                yMin: number, yMax: number, pageTop: number, pageBottom: number): void {
        this.y = y;
        this.height = height;
        this.topMargin = topMargin;
        this.bottomMargin = bottomMargin;

        this.pageHeight = pageHeight;

        this.yMin = yMin;
        this.yMax = yMax;
        this.pageTop = pageTop;
        this.pageBottom = pageBottom;
    }
}
