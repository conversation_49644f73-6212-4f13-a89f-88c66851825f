import { IDocumentParaLine } from '../../../model/ParaLineProperty';
import { ICanvasProps } from './common';
import {PortionUI} from './Portion';

export class ParaLineUI {
    private props: IDocumentParaLine;
    private documentCore: any;
    private host: any;
    private ctx: any;
    constructor(props: ICanvasProps) {
        this.props = props.content;
        this.host = props.host;
        this.documentCore = props.host.documentCore;
        this.ctx = props.ctx;
        this.render();
    }

    public render(): void {
        const { content, cellId, pageIndex } = this.props;
        const curLine = this.props.id;

        content.forEach((item) => {
            if (
                curLine >= item.startLine &&
                item.startLine + item.getLinesCount() > curLine
            ) {
                const startPos = item.getRangeStartPos(curLine - item.startLine);
                const endPos = item.getRangeEndPos(curLine - item.startLine);
                // console.log(item)
                const curContent: any = {cellId, startPos, endPos};
                curContent.content = item.content;
                curContent.positionX = item.positionX;
                curContent.positionY = item.positionY;
                curContent.textProperty = item.textProperty;
                curContent.type = item.type;
                curContent.pageIndex = pageIndex;

                const props = {content: curContent, ctx: this.ctx, host: this.host, pageIndex};
                const abc = new PortionUI(props);
                // return (
                //     <ParaPortion
                //         key={item.id}
                //         id={item.id}
                //         cellId={cellId}
                //         content={item.content}
                //         type={item.type}
                //         textProperty={item.textProperty}
                //         positionX={item.positionX}
                //         positionY={item.positionY}
                //         startPos={startPos}
                //         endPos={endPos}
                //         pageIndex={this.props.pageIndex}
                //         documentCore={this.props.documentCore}
                //         textHeight={item.textHeight}
                //     />
                // );
            }
        });
    }
}
