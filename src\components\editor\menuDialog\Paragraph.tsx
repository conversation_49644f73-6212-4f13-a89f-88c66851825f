import * as React from 'react';
import Dialog from '../dialog/dialog';
import { LineSpacingType } from '../../../common/commonDefines';

interface IDialogProps {
    documentCore: any;
    visible: boolean;
    close: (name: string, bReflash?: boolean) => void;
    children?: React.ReactNode;
}

interface IState {
    bReflash: boolean;
}

export default class ParagraphSetDialog extends React.Component<IDialogProps, IState> {
    private paraPro: {
        alignment: number,
        bPageBreakBefore: boolean,
        paraLeftInd: number,
        paraInd: number,
        paraIndType: number,
        paraSpacing: number,
        paraSpacingType: number,
    };
    private paraSpacingTypes: Array<{name: string, value: number,
        disabled: boolean, cell: string, defaultValue: number}>;
    private paraProUI: {
        disabled: boolean,
        cell: string,
    };
    private pageBreakDisabled: boolean = true;

    constructor(props: any) {
        super(props);
        this.state = {
            bReflash: false,
        };
        this.paraSpacingTypes = [
            {name: '单倍行距', value: LineSpacingType.Single, disabled: true, cell: '倍', defaultValue: 1},
            {name: '1.5倍行距', value: LineSpacingType.SingeHalf, disabled: true, cell: '倍', defaultValue: 1.5},
            {name: '2倍行距', value: LineSpacingType.Double, disabled: true, cell: '倍', defaultValue: 2},
            {name: '多倍行距', value: LineSpacingType.Multi, disabled: false, cell: '倍', defaultValue: 3},
            {name: '最小值', value: LineSpacingType.Min, disabled: true, cell: '倍', defaultValue: 1},
            {name: '固定值', value: LineSpacingType.Fixed, disabled: false, cell: 'cm', defaultValue: 0.55},
            {name: '', value: -1, disabled: true, cell: '', defaultValue: undefined},
        ];

        this.paraProUI = {disabled: true, cell: '倍'};
        this.paraPro = {
            alignment: undefined,
            bPageBreakBefore: undefined,
            paraLeftInd: undefined,
            paraInd: undefined,
            paraIndType: undefined,
            paraSpacing: undefined,
            paraSpacingType: undefined,
        };
    }

    public render(): any {
        return (
        <Dialog
            id='paragraph'
            preventDefault={true}
            close={this.close}
            confirm={this.confirm}
            open={this.open}
            width={300}
            height={300}
            top='middle'
            title='段落设置'
            visible={this.props.visible}
        >
            <div className='paragraph-set'>
                <div className='paragraph-top'>
                <p className='common-title'>缩进</p>
                <div className='full-line'>
                    <span className='paragraph-label'>左缩进</span>
                    <span className='paragraph-input'>
                    <input
                        value={this.paraPro.paraLeftInd === undefined ? '' : this.paraPro.paraLeftInd}
                        onChange={this.paraValChange.bind(this, 'paraLeftInd')}
                    />
                    <label>cm</label>
                    </span>
                </div>
                <div className='full-line'>
                    <span className='paragraph-label'>
                        <select
                            value={this.paraPro.paraIndType || -1}
                            onChange={this.paraRadioChange.bind(this, 'paraIndType')}
                        >
                            <option value={1}>首行缩进</option>
                            <option value={2}>悬挂缩进</option>
                            <option
                                value={-1}
                                style={this.paraPro.paraIndType === undefined ? null : {display: 'none'}}
                            />
                        </select>
                        </span>
                        <span className='paragraph-input'>
                        <input
                            disabled={this.paraPro.paraIndType === undefined}
                            value={this.paraPro.paraInd === undefined ? '' : this.paraPro.paraInd}
                            onChange={this.paraValChange.bind(this, 'paraInd')}
                        />
                        <label>cm</label>
                    </span>
                </div>
                </div>
                <div>
                    <p>行距</p>
                    <div className='full-line'>
                        <span className='paragraph-label'>
                            <select
                                value={this.paraPro.paraSpacingType || 0}
                                onChange={this.paraSpacingChange.bind(this, 'paraSpacingType')}
                            >
                                {this.renderParaSpacing()}
                            </select>
                        </span>
                        <span className='paragraph-input'>
                            <input
                                disabled={this.paraProUI.disabled}
                                value={this.paraPro.paraSpacing || ''}
                                onChange={this.paraValChange.bind(this, 'paraSpacing')}
                            />
                            <label>{this.paraProUI.cell}</label>
                        </span>
                    </div>
                </div>

                <div>
                    <p>对齐</p>
                    <div className='full-line radio-box'>
                        <input
                            type='radio'
                            name='align'
                            value={0}
                            id='left'
                            checked={this.paraPro.alignment === 0}
                            onChange={this.paraRadioChange.bind(this, 'alignment')}
                        />
                        <label htmlFor='left'>左对齐</label>
                        <input
                            type='radio'
                            name='align'
                            value={2}
                            id='right'
                            checked={this.paraPro.alignment === 2}
                            onChange={this.paraRadioChange.bind(this, 'alignment')}
                        />
                        <label htmlFor='right'>右对齐</label>
                        <input
                            type='radio'
                            name='align'
                            value={3}
                            id='center'
                            checked={this.paraPro.alignment === 3}
                            onChange={this.paraRadioChange.bind(this, 'alignment')}
                        />
                        <label htmlFor='center'>两端对齐</label>
                    </div>
                </div>
                <div>
                    <p>特殊</p>
                    <div className='full-line'>
                        <input
                            disabled={this.pageBreakDisabled}
                            type='checkbox'
                            id='bPageBreakBefore'
                            checked={this.paraPro.bPageBreakBefore === true}
                            onChange={this.paraCheckChange.bind(this, 'bPageBreakBefore')}
                        />
                        <label htmlFor='bPageBreakBefore'>换页符</label>
                    </div>
                </div>
            </div>
        </Dialog>
        );
    }

    public componentMount(): void {
        // if (this._visible === true) {
        //     this.props.open();
        // }
    }

    private close = (id?: any): void => {
        this.props.close(id);
    }

    private setParaPro(): any {
        const para = this.paraPro;
        para.paraLeftInd = para.paraLeftInd * 10;
        para.paraInd = para.paraInd * 10;
        if (isNaN(para.paraLeftInd)) {
            para.paraLeftInd = undefined;
        }
        if (isNaN(para.paraInd)) {
            para.paraInd = undefined;
        }
        if (para.paraSpacingType === -1) {
            para.paraSpacingType = undefined;
            para.paraSpacing = undefined;
        }
        // 悬挂缩进
        if (para.paraIndType === 2) {
            para.paraLeftInd += para.paraInd;
            para.paraInd = -para.paraInd;
        } else if (para.paraIndType === -1 || para.paraIndType === undefined) {
            para.paraInd = undefined;
            para.paraIndType = undefined;
        }
    }

    private confirm = (id: any): void => {
        const documentCore = this.props.documentCore;
        this.setParaPro();
        documentCore.setParagraphProperty1(this.paraPro);
        this.props.close(id, true);
    }

    // private paragraphOpen(): void {
    //     this.setParaPro();
    // }

    private open = (): void => {
        const documentCore = this.props.documentCore;
        const para = documentCore.getSelectedParaPro();
        if (para.paraLeftInd !== undefined) {
            para.paraLeftInd = para.paraLeftInd / 10 || 0;
        }
        if (para.paraInd !== undefined) {
            para.paraInd = para.paraInd / 10 || 0;
        }
        // 悬挂缩进
        if (para.paraIndType === 2) {
            para.paraLeftInd += para.paraInd;
            para.paraInd = -para.paraInd;
        }
        para.bPageBreakBefore = para.bPageBreakBefore || false;
        this.pageBreakDisabled = true;
        if (para.bPageBreakBefore === true) {
            this.pageBreakDisabled = false;
        }
        let lineSpaceDisabled = true;
        let cell: string = '倍';
        switch (para.paraSpacingType) {
            case LineSpacingType.Single:
            case LineSpacingType.Min:
                para.paraSpacing = 1;
                break;

            case LineSpacingType.SingeHalf:
                para.paraSpacing = 1.5;
                break;

            case LineSpacingType.Double:
                para.paraSpacing = 2;
                break;

            case LineSpacingType.Multi:
                lineSpaceDisabled = false;
                // need to convert to times
                let ratio = para.paraSpacing;
                ratio = (ratio - 1) / 2 / 0.15;
                para.paraSpacing = Math.round(ratio * 100) / 100;
                break;

            case LineSpacingType.Fixed:
                lineSpaceDisabled = false;
                cell = 'cm';
                // para.paraSpacing = para.paraSpacing;
                break;
            default:
                para.paraSpacingType = -1;
                cell = '';
                // para.paraSpacing = 1;
                break;
        }

        this.paraPro = para;
        this.paraProUI = {disabled: lineSpaceDisabled, cell};
        this.setState({bReflash: !this.state.bReflash});
    }

    private renderParaSpacing(): any {
        const val = this.paraPro.paraSpacingType;
        return this.paraSpacingTypes.map((type, index) => {
            const style = {};
            if (type.value === -1 && val !== -1) {
                style['display'] = 'none';
            }
            return (<option key={index} style={style} value={type.value}>{type.name}</option>);
        });
    }

    private paraSpacingChange(name: string, e: any): void {
        const value = parseInt(e.target.value, 10);
        this.paraPro[name] = value;
        const obj = this.paraSpacingTypes.find((type) => type.value === value);
        if (obj) {
            this.paraProUI.disabled = obj.disabled;
            this.paraProUI.cell = obj.cell;
            this.paraPro.paraSpacing = obj.defaultValue;
        }
    }

    private paraRadioChange(name: string, e: any): void {
        this.paraPro[name] = parseInt(e.target.value, 10);
    }

    private paraValChange(name: string, e: any): void {
        this.paraPro[name] = e.target.value;
        this.setState({bReflash: !this.state.bReflash});
    }

    private paraCheckChange(name: string): void {
        this.paraPro[name] = !this.paraPro[name];
    }
}
