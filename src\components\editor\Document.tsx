// // import * as React from 'react';
// // import Page from './Page';
// // import { IDocumentProperty } from '../../model/DocumentProperty';
// // import { List } from './module/reactWindow';
// // import { DocumentCore } from 'src/model/DocumentCore';
// // import { SPACING_CONFIG, NewControlType, dynamicHeightEnabled } from '../../common/commonDefines';
// // import { NewCombox } from './NewCombox';
// // import { NewDateBox } from './NewDateBox';

// interface IDocumentProps extends IDocumentProperty {
//   /**
//    * 获得当前第几页
//    */
//   currentIndex?: number;
//   /**
//    * 通过pageid 获得当前page内容
//    */
//   getContentByPageId?: (pageIndex: number) => any;
//   onScroll: (event: any) => void;

//   // weird issue that spacingConfig cannot be imported in Document.tsx
//   spacingConfig?: any;

//   editorContainer?: any;

//   documentCore: DocumentCore;
//   handleRefresh?: any;
//   height?: number;
//   host?: any;
//   cursorType: string;
//   cursorInNewControlName?: string;
//   isShowComboxDropButton?: boolean;
//   isShowComboxList?: boolean;
//   isShowDateDropButton?: boolean;
//   isShowDateList?: boolean;
//   closeNewBoxList?: () => void;
//   showNewBoxList?: (bClickDropButton: boolean) => void;
// }

// // interface pageState {
// //   pageInfo: any,
// //   textPage: any,
// // }

// export default class Document extends React.Component<IDocumentProps> { // }, pageState> {
//   // pageProperty: any = {};
//   // textProperty: any = {};
//   private list: any = React.createRef();

//   constructor(props, state) {
//     super(props, state);
//     // const { pageProperty, textProperty } = props;
//     // this.pageProperty = pageProperty;
//     // this.textProperty = textProperty;
//     // console.log('Document---------------constructor----------------')
//   }

//   public UNSAFE_componentWillReceiveProps(nextProps) {

//     // TODO: better performance?
//     if (dynamicHeightEnabled.value || (dynamicHeightEnabled.old && !dynamicHeightEnabled.value)) {
//       this.list.current.recomputeRowHeights(this.props.documentCore.getPageCount() - 1);
//     }

//     // this.list.current.forceUpdateGrid();
//     // console.log(nextProps.documentCore.getDocument().curPos.type)
//     // console.log(this.props.documentCore.getDocument().curPos.type)
//   }

//   /**
//    * 渲染所有页样式以及当前页面内容
//    */
//   public renderPages = ({key, index, isScrolling, isVisible, style }) => {
//     // 只渲染可视区域的页面
//     if ( true !== isVisible ) {
//         return;
//     }

//     const { getContentByPageId, pageProperty} = this.props;
//     const leftPos = Number.parseInt(((document.documentElement.clientWidth - pageProperty.width)/2).toPrecision(5));
//     return (<div key={key} page-index={index} style={{...style, width: pageProperty.width, height: index===0 ? style.height - 20 : '', top: style.top, left: leftPos}} className="page-wrapper">
//             <Page key={key} {...pageProperty} index={index} showPageBorder content={getContentByPageId(index)} editorContainer={this.props.editorContainer}
//             documentCore={this.props.documentCore} handleRefresh={this.props.handleRefresh} cursorType={this.props.cursorType}/>
//             {this.renderComboxList(index)}
//             {this.renderDropButton(index)}
//             {this.renderDateList(index)}
//             {/* {this.renderDateDropButton(index)} */}
//           </div>);
//   }

//   public render(): any {
//     // console.log('-----------------------------------------Document----------------------------------')
//     const {gridVirtualizedMargin} = SPACING_CONFIG;
//     let height = this.props.height || document.documentElement.clientHeight - 76;
//     const props = this.props;
//     if (props.host.isPrint === true) {
//       height = props.pageProperty.height * props.total;
//     } else if (dynamicHeightEnabled.value && this.props.documentCore.getPageCount() === 1) {
//       const tempHeight = this.getLastPageRowHeight();
//       if (tempHeight < height) {
//         height = tempHeight;
//       }
//     }
//     return (
//         <List
//           ref={this.list}
//           onScroll={this.props.onScroll} // 会导致页面重刷，但是可以通过shouldComponentUpdate函数减少不必要的刷新。滚动页面时，选择区域的渲染也需要此onScroll
//           overscanRowCount={1}
//           width={document.documentElement.clientWidth}
//           height={height}
//           rowCount={this.props.total}
//           rowHeight={this.getPageRowHeight}
//           rowRenderer={this.renderPages}
//           containerStyle={{marginTop: gridVirtualizedMargin}} // containerStyle is must-needed, or render improperly!
//           scrollToIndex={this.props.currentIndex} // 初始化打开文档时，可指定显示第几页
//         />
//     );
//   }

//   private getPageRowHeight = (indexObj: {index: number}): number => {
//     const lastPageIndex = this.props.documentCore.getPageCount() - 1;

//     if (dynamicHeightEnabled.value && indexObj.index === lastPageIndex) {
//       return this.getLastPageRowHeight();
//     } else {
//       return this.props.pageProperty.height + 20;
//     }
//   }

//   private getLastPageRowHeight(): number {
//     const logicDocument = this.props.documentCore.getDocument();

//     // get bottom pos of last elem of last page
//     const lastDocumentContent: any = logicDocument.content[logicDocument.content.length - 1];
//     if (lastDocumentContent != null) {
//       const lastParaPage = lastDocumentContent.pages[lastDocumentContent.pages.length - 1];
//       if (lastParaPage != null) {
//         const lastPageYLimit = lastParaPage.bounds.bottom;
//         if (lastPageYLimit != null) {
//           return lastPageYLimit + 90;
//         }
//       }
//     }

//     return this.props.pageProperty.height + 20;
//   }

//   private renderComboxList = (pageIndex: number) => {
//     const { documentCore }  = this.props;
//     if ( true === this.props.isShowComboxList && true === documentCore.isCursorInNewControl() ) {
//       const bounds = documentCore.getNewControlsFocusBounds();

//       if ( bounds && bounds.bounds && pageIndex === bounds.bounds[bounds.bounds.length - 1].pageIndex ) {
//         return (
//           // <div className='new-control-combox-list' style={{top, left}}>
//               <NewCombox
//                 documentCore={documentCore}
//                 newControlPropety={documentCore.getCursorInNewControlProperty()}
//                 closeNewComboxList={this.props.closeNewBoxList}
//                 refresh={this.props.handleRefresh}
//               />
//           // </div>
//         );
//       }
//     }

//     return null;
//   }

//   private renderDateList = (pageIndex: number) => {

//     const { documentCore }  = this.props;
//     // console.log(this.props.isShowDateList, documentCore.isCursorInNewControl())
//     if ( true === this.props.isShowDateList && true === documentCore.isCursorInNewControl() ) {
//       const bounds = documentCore.getNewControlsFocusBounds();

//       if ( bounds && bounds.bounds && pageIndex === bounds.bounds[bounds.bounds.length - 1].pageIndex ) {
//         return (
//           <NewDateBox
//             documentCore={documentCore}
//             newControlPropety={documentCore.getCursorInNewControlProperty()}
//             closeNewBoxList={this.props.closeNewBoxList}
//             refresh={this.props.handleRefresh}
//           />
//         );
//       }
//     }

//     return null;
//   }

//   private renderDropButton = (pageIndex: number) => {
//     const { documentCore }  = this.props;
//     if ( true === this.props.isShowComboxDropButton && true === documentCore.isCursorInNewControl()
//       || (true === this.props.isShowDateDropButton && true === documentCore.isCursorInNewControl())) {
//       const bounds = documentCore.getNewControlsFocusBounds();

//       if ( bounds && bounds.bounds && pageIndex === bounds.bounds[bounds.bounds.length - 1].pageIndex ) {
//         const lastLine = bounds.bounds[bounds.bounds.length - 1];
//         const left = lastLine.x + lastLine.width;
//         const top = lastLine.y; // + 3;
//         const width = 10;

//         return (
//           <div className='new-control-drop-button' style={{top, left, width, height: lastLine.height}}
//               onClick={this.handleDropButton.bind(this)}>
//               <div className='drop-button-triangle' />
//           </div>
//         );
//       }
//     }

//     return null;
//   }

//   private handleDropButton(type: NewControlType = NewControlType.Combox): void {
//     switch (type) {
//       case NewControlType.DateTimeBox:
//       case NewControlType.Combox:
//       default:
//         this.props.showNewBoxList(true);
//         break;
//     }

//   }

//   // private renderDateDropButton = (pageIndex: number) => {
//   //   const { documentCore }  = this.props;
//   //   if ( true === this.props.isShowDateDropButton && true === documentCore.isCursorInNewControl() ) {
//   //     const bounds = documentCore.getNewControlsFocusBounds();

//   //     if ( bounds && bounds.bounds && pageIndex === bounds.bounds[bounds.bounds.length - 1].pageIndex ) {
//   //       const lastLine = bounds.bounds[bounds.bounds.length - 1];
//   //       const left = lastLine.x + lastLine.width;
//   //       const top = lastLine.y + 3;
//   //       const width = 10;

//   //       return (
//   //         <div className='new-control-drop-button' style={{top, left, width, height: lastLine.height}}
//   //             onClick={this.handleDropButton.bind(this)} >
//   //           <div className='drop-button-triangle' />
//   //         </div>
//   //         //   <NewComboxDropButton
//   //         //     documentCore={documentCore}
//   //         //     newControlPropety={documentCore.getCursorInNewControlProperty()}
//   //         //     showNewComboxList={this.props.host.state.showNewComboxList}
//   //         //     closeNewComboxList={this.props.host.state.closeNewComboxList}
//   //         //     refresh={this.props.handleRefresh}
//   //         //   />
//   //         // </div>
//   //       );
//   //     }
//   //   }

//   //   return null;
//   // }

//   // componentWillUnmount() {
//   //   console.log('Document--------componentWillUnmount---------------')
//   // }
// }
