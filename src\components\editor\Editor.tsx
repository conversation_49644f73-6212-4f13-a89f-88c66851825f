// import * as React from 'react';
// import './Editor.less';
// import Document from './Document';
// import { DocumentCore, IDrawSelectionBounds, IDrawNewControlBounds } from '../../model/DocumentCore';
// import { ICursorProperty } from '../../model/CursorProperty';
// import PageMenu from './PageMenu';
// import PageToolbar from './PageToolbar';
// import {GlobalEvent as gEvent, GlobalEventName as gEventName} from '../../common/GlobalEvent';
// import EditorEvent from '../../common/GlobalEditorEvent';
// import {Scrollbar} from '../../common/Scrollbar';

// import {TextArea} from './TextArea';
// import {Copy} from './CopyPaste';
// import MouseEventHandler, {KeyBoardEvent, MouseEventType,
//   MouseButtonType} from '../../common/MouseEventHandler';
// import { FormatWriter } from '../../format/writer/writer';
// import { SPACING_CONFIG, COLOR_CONFIG, IMAGE_FLAGS, ImageConfigModalType,
//   TableMenuModalType, EquationType, RenderSectionBackgroundType, INewControlProperty, ResultType,
//   APO_XMLS_ARRAY, NewControlErrorInfo} from '../../common/commonDefines';
// import { TablePopMenu } from './TablePopMenu';
// import {parse as circularParse, stringify as circularStringify} from 'flatted/esm';
// import { ParaEquation } from '../../model/core/Paragraph/ParaDrawing';
// import { DocCurPosType } from '../../model/core/Document';
// import { ParaElementType } from '../../model/core/Paragraph/ParagraphContent';
// import IExternalInterface, { IExternalEvent } from '../../common/external/IExternalInterface';
// import * as ReactDOM from 'react-dom';
// import MedEquation from './medEquation';
// import { message } from '../../common/Message';
// import ExternalEvent from '../../common/external/Event';
// import DocumentContentElementBase from '../../model/core/DocumentContentElementBase';

// import SynCheckbox from './ui/Dialog';
// import InputUI from './ui/Input';
// import CheckboxUI from './ui/Checkbox';
// import RadioUI from './ui/Radio';
// import SelectUI from './ui/select/Select';
// import ButtonUI from './ui/Button';
// import CheckboxItem from './ui/CheckboxItem';
// import PageContainer from './module/PageContainer';

// /**
//  * 根据节点获得pageid 如果不存在 返回null
//  * @param node
//  */
// export const getPageElement = (node) => {
//     if (!node) {
//       return null;
//     }
//     while (node.tagName !== 'svg'
//      || node.getAttribute('type') !== 'page') {
//        if (node.tagName === 'BODY') {
//          return null;
//        }
//        if (!node.parentNode) {
//          return null;
//        }
//        node = node.parentNode;
//     }
//     return node;
// };

// export interface IXmlProps {
//   documentCore: DocumentCore;
//   properties?: Map<string, any>;
//   selectedArea?: DocumentContentElementBase[];
// }

// interface IEmrState {
//   documentCore: DocumentCore;
//   currentIndex: number;
//   isScroll: boolean;
//   clickOutsideDropdown: boolean;
//   bInput: boolean;  // 文本输入

//   isCacheEnabled: boolean;
//   isShowNewControlTips: boolean;
//   isShowComboxList: boolean;
//   isShowComboxDropButton: boolean;
//   isShowDateList: boolean;
//   isShowDateDropButton: boolean;
//   showContextMenu: boolean;
//   imageConfigModalType: number;
//   tablePopMenuModalType: TableMenuModalType;

//   showEquationEditModal: boolean;
//   equationType: EquationType;
//   xmlProps: IXmlProps;
// }

// interface IProps {
//   id?: string;
//   isTest?: boolean;
// }

// export class EmrEditor extends React.Component<IProps, IEmrState> {

//   /**
//    *  判断当前输入是否为：上下左右，pageUp，pageDown
//    * @param keyCode
//    */
//   public static isLRUDKey( keyCode: number ): boolean {
//     return (keyCode === 33 || keyCode === 34 || keyCode === 37 || keyCode === 38 || keyCode === 39 ||
//               keyCode === 40);
//     }

//     /**
//      * 是否undo/redo：ctrl + z，ctrl + y
//      */
//   public static isUndoRedo( event: KeyBoardEvent ): boolean {
//      return ( event.keyCode === 89 || event.keyCode === 90 ) && ( true === event.bCtrlKey );
//   }

//   /**
//    * 当前输入是否为功能键：F1 ---- F12
//    * @param event
//    */
//   public static isFuntionKey( event: KeyBoardEvent ): boolean {
//     return ( event.keyCode >= 112 && event.keyCode <= 123 );
//   }

//   /**
//    * 判断当前鼠标右键是否在图片
//    * @param e
//    */
//   public static isClickOnImage(e: any): boolean {
//     return (e.target.tagName === 'image' || (e.target.tagName === 'circle' &&
//               e.target.className.baseVal.includes('image-handler')));
//   }

//   public docId: number;
//   public externalEvent: ExternalEvent;
//   public isPrint: boolean;

//   /**
//    * 光标闪动的定时器
//    */
//   private timer: any = null;
//   /**
//    * 光标的dom
//    */
//   private cursor: SVGElement | null = null;
//   /**
//    * textArea
//    */
//   private textArea: TextArea = null;

//   private viewCursorPoint: number[] = [];
//   /**
//    * 光标点
//    */
//   private endPoint: ICursorProperty = null;

//   // mouse operation
//   private gMouseEvent: MouseEventHandler = null;

//   private gKeyEvent: KeyBoardEvent = null;

//   private myRef: any;

//   private selections: IDrawSelectionBounds = null;  // 选中区域

//   private bSelected: boolean = false; // 当前是否选中状态

//   private newControlsCursorInBounds: IDrawNewControlBounds = null;
//   private newControlsMouseFocusBounds: IDrawNewControlBounds = null;
//   private curFocusNewControlName: string = null;
//   private cursorInNewControlName: string = null;
//   private curInNewControlName: string;

//   private pageIndex: number;  // 当前光标所在页面
//   private bJump: boolean;  // 是否需要跳转页面，todo： 判断当前页面是否已经生成，如果没有生成，先刷新页面；如果已经生成，则滚动页面

//   // cacheThreshold = null;
//   private cacheSaveTimer: any = null;

//   // 10 mins
//   private cacheEnabledCountDown: number = 600000;

//   // 10s
//   private cacheMonitorCountDown: number = 10000;

//   // curposType before dblClick
//   private oldCurposType: number;

//   private scrollTool: Scrollbar;
//   private bShowNewControlTip: boolean = false;

//   private newControlTips: any;
//   private bShowBoxList: boolean;
//   private _external: IExternalInterface;
//   private _clearTime: any;
//   private _oldPosition: {x: number, y: number, pageNum: number};
//   private _currentNewControl: any;
//   private _newControlTimeout: any;
//   private _newControlFresh: boolean;
//   private _bMousedown: boolean;
//   private test: any = {
//     name1: true,
//     data: [{key: 'a', value: 3}, {key: 'b水违反', value: 4}, {key: 'c', value: 5}, {key: 'd', value: 6}],
//   };

//   constructor(props: any) {
//     super(props);
//     let bText = true;
//     if (props.id && props.isTest === undefined || props.isTest === false) {
//       bText = false;
//     }
//     const documentCore = new DocumentCore(undefined, bText);
//     this.docId = documentCore.getCurrentId();
//     // this.documentCore = new DocumentCore();
//     this.pageIndex = 0;
//     this.bJump = false;
//     this.gMouseEvent = new MouseEventHandler();
//     this.gKeyEvent = new KeyBoardEvent();
//     this.myRef = React.createRef();
//     this.newControlTips = React.createRef();
//     this.bShowBoxList = false;
//     this.curInNewControlName = null;

//     this.state = {
//       documentCore,
//       currentIndex: 0,
//       isScroll: false,
//       clickOutsideDropdown: false,
//       bInput: false,
//       isCacheEnabled: false,
//       isShowNewControlTips: false,
//       isShowComboxList: false,
//       isShowComboxDropButton: false,
//       isShowDateList: false,
//       isShowDateDropButton: false,
//       showContextMenu: false,
//       imageConfigModalType: ImageConfigModalType.None,
//       tablePopMenuModalType: TableMenuModalType.None,
//       showEquationEditModal: false,
//       equationType: EquationType.Fraction,
//       xmlProps: null,
//     };

//     //  console.log('EmrEditor---------------constructor----------------')
//   }

//   public getEditor(): IExternalInterface {
//     if (this._external === undefined) {
//       // const external = new ExternalInterface(this);
//       // this._external = external.getEditor();
//     }

//     return this._external;
//   }

//   public setEditEvent(options: IExternalEvent): void {
//     if (!options) {
//       return;
//     }
//     if (this.externalEvent) {
//       this.externalEvent.setEvents(options);
//       return;
//     }
//     // this.externalEvent = new ExternalEvent(this, options);
//   }

//   public removeEvent(sNames?: string[]): boolean {
//     if (this.externalEvent) {
//       return this.externalEvent.removeEvent(sNames);
//     }
//     return false;
//   }

//     /**
//      * 初始化textArea，光标位置
//      */
//     public componentDidMount(): void {
//       const { documentCore }  = this.state;

//       new EditorEvent(this);
//       this.addGobalEventListener();
//       this.textArea = new TextArea(this);
//       new Copy(this);
//       this.initCursorPos();

//       window['editor'] = this.getEditor();
//       // console.log(this.state.documentCore.getDocument())
//       window['editor'].getDocument = () => {
//         console.log(this.state.documentCore.getDocument());
//       };

//       // console.log(editor)
//       // editor.setNewControlText('textedit6', '3455');

//       // console.log('EmrEditor---------------componentDidMount----------------')

//       /** cache temp turn off */
//       // setTimeout(() => {
//       //   this.setState({isCacheEnabled: true});
//       // }, this.cacheEnabledCountDown);
//       /** cache temp turn off */

//       this.scrollTool = new Scrollbar(this);
//       window.onblur = () => {
//         this.cursor.classList
//             .add('hidden');
//       };

//       // 获取业务属性(需要每次更新时获取吗？)
//       const xmlProps: IXmlProps = {
//         documentCore,
//         properties: this.getCustomPropertiesInSettings(),
//         selectedArea: [],
//       };
//       this.setState({xmlProps});
//     }

//     public componentWillUnmount(): void {
//       gEvent.setEvent(this.docId, gEventName.UnMounted);
//       this.removeGobalEventListener();
//       // if ( this.timer ) {
//       //     this.cursorBlur();
//       // }
//       // console.log('EmrEditor---------------componentWillUnmount----------------')
//     }

//     public getSnapshotBeforeUpdate(): void {
//       if (this.cursor && !this.state.isScroll && true !== this.isSelected() ) {
//         this.updateCursor(true, false);
//       }
//       return null;
//     }

//     public componentDidUpdate(): void {
//       const { documentCore }  = this.state;
//       if ( true === this.isSelected() ) {
//         this.clearSection();

//         this.selections = documentCore.getSelectionBounds();
//         this.renderSection();
//       }

//       if ( null != this.cursorInNewControlName ) {
//         this.clearHightLightNewControl();
//         this.renderFocusHightLightNewControl(null, null, false);
//       }

//       // 业务属性 需要每次更新时获取吗？)
//       // const xmlProps: IXmlProps = {
//       //   documentCore,
//       //   properties: this.getCustomPropertiesInSettings(),
//       // };

//       // console.log('didUpdate');
//       // console.log(this.state.isCacheEnabled); // after didMount(), will be true
//       if (this.state.isCacheEnabled) {
//         if (this.cacheSaveTimer) {
//           this.clearCacheSaveTimer();
//         }

//         // it updates, means content change, always save if allowed
//         if (!this.cacheSaveTimer) {
//           this.cacheSaveTimer = setTimeout(() => {

//             // let xmlProps = {
//             //   'document': this.state.documentCore.document,
//             //   'defaultParaProperty': this.state.documentCore.getDefaultParaProperty(),
//             //   'defaultTextProperty': this.state.documentCore.getDefaultTextProperty(),
//             // };

//             // console.log(xmlProps);

//             const formatWriter = new FormatWriter();

//             formatWriter.generateXmls(this.state.xmlProps, APO_XMLS_ARRAY).then((data) => {

//               // promise.all() resolve order is preserved!
//               const cacheDocument = localStorage.getItem('cacheDocument');
//               const cacheMedia = localStorage.getItem('cacheMedia');
//               // const cacheSettings = localStorage.getItem('cacheSettings');
//               const cacheStyles = localStorage.getItem('cacheStyles');
//               // console.log(data[2] === cacheSettings) // since date() changed, must be false

//               if (data[0] === cacheDocument && data[1] === cacheMedia && data[3] === cacheStyles) {
//                 console.log('same as cache, not saved');
//               } else {
//                 try {
//                   localStorage.setItem('cacheDocument', data[0]);
//                   localStorage.setItem('cacheMedia', data[1]);
//                   localStorage.setItem('cacheSettings', data[2]);
//                   localStorage.setItem('cacheStyles', data[3]);
//                   console.log('auto saved');
//                 } catch (error) {
//                   console.log('auto saved failed: ' + error);
//                 }
//               }

//             }).catch((error) => {
//               console.log('auto save failed: ' + error);
//             });

//             /** cache turn off */
//             // after auto saved, reset save countdown to monitor auto save again
//             // this.setState({isCacheEnabled: false}, () => {
//             //   setTimeout(() => {
//             //     this.setState({isCacheEnabled: true});
//             //   }, this.cacheEnabledCountDown);
//             // });
//             /** cache turn off */

//           }, this.cacheMonitorCountDown);

//         }
//       }
//     }

//   inputChange = (value: any, name: string): void => {
//     this.test[name] = value;
//   }

//   renderInputContent(): any {
//     return (
//       <span>
//         <ButtonUI>取消</ButtonUI>
//         <ButtonUI type='primary'>确认</ButtonUI>
//       </span>
//     );
//   }

//   public render(): any {
//     const {menuHeight, toolbarHeight} = SPACING_CONFIG;

//     // current padding is 0.2cm
//     const paddingTop = parseFloat(menuHeight.slice(0, -2)) + parseFloat(toolbarHeight.slice(0, -2)) + 0.2 * 4 + 'cm';
//     // console.log(paddingTop);

//     // console.log('render')
//     // <SynCheckbox readonly={false} onChange={this.inputChange} name='name1' value={this.test.name1} data={this.test.data}/>
//     return (
//       <div className='emr-editor-container' style={{paddingTop}} ref={this.myRef}>
//          <textarea id='textarea_input'/>
//          <div className='menu-panel noprint'>
//            {this.renderPageMenu()}
//            {this.renderPageToolbar()}
//            {this.renderPopMenu()}
//            {this.renderNewControlTips()}
//          </div>
//         {this.renderPages()}
//       </div>
//     );
//   }

//   public setSelections(): void {
//     if (this.isSelected()) {
//       this.clearSection();
//     }
//     const documentCore = this.state.documentCore;
//     this.selections = documentCore.getSelectionBounds();
//     this.renderSection();
//   }

//   /**
//    * 创建医学公司html
//    */
//   public getMedEquationDom(): any {
//     const medEquation = {
//       ordEquationRef: React.createRef(),
//       fractionEquationRef: React.createRef(),
//       menEquationRef: React.createRef(),
//     };
//     const div = document.createElement('div');
//     ReactDOM.render(
//         <MedEquation refs={medEquation} />,
//         div,
//     );
//     // setTimeout(() => {
//     //   div = null;
//     // }, 0);

//     return medEquation;
//   }

//   public clearDom(): void {
//     if (this.myRef.current && this.myRef.current.parentNode) {
//       ReactDOM.unmountComponentAtNode(this.myRef.current.parentNode);
//     }

//     // setTimeout(() => {
//     //   if (this.myRef.current) {
//     //     this.myRef.current.outerHTML = '';
//     //   }
//     // }, 10);
//   }

//   public setScrollbarToPage(pageIndex: number): void {
//     this.scrollTool.setPageFlag(this.pageIndex < pageIndex);
//   }

//   /**
//    * 强制页面重刷
//    */
//   public handleRefresh = (timeout?: number) => {

//     // 重新使页面获取焦点
//     if (timeout !== undefined) {
//       clearTimeout(this._clearTime);
//       this._clearTime = setTimeout(() => {
//         this.updateCursor(false, false, true);
//         // ++++++++++wen.luo key: bug36560 20190822+++++++++
//         if (this.isSelected()) {
//           this.cursorBlur();
//           this.setCursorVisible(false);
//         }
//         this.changeDocument();

//         // +++++++++++++++++++++++++++++++++++++++++++++++++
//       }, timeout);
//     } else {
//       this.updateCursor(false, false, true);
//       this.changeDocument();

//     }

//     // image refresh shall not show cursor (SUSPEND: same as line 629, bUse seems already take care of it)
//     if (IMAGE_FLAGS.isImageOnClick || IMAGE_FLAGS.isHandlerOnClick) {
//       // this.updateCursor(true, true);
//       // this.cursorBlur();
//       // this.setCursorVisible(false);
//     }
//   }

//   public openPrintDoc(): void {
//     this.state.documentCore.removeSelection();
//     clearInterval(this.timer);
//     this.setCursorVisible(false);
//     this.handleRefresh();
//   }

//   public getCursor(): SVGElement {
//     return this.cursor;
//   }

//   public getGMouseEvent(): MouseEventHandler {
//     return this.gMouseEvent;
//   }

//   public testDocumentXml = (bInsertFile: boolean = false) => {
//     // console.log(data);
//     // this.state.documentCore.testDocumentXml(data);
//     if (!bInsertFile) {
//       this.initCursorPos();
//     } else {
//       this.updateCursor(false, false, true);
//     }
//     this.forceUpdate();
//   }

//   public setNewControlDropButton(property: INewControlProperty): void {
//     const newControl = this.state.documentCore.getCurrentNewControl();

//     if ( newControl && property && (true === newControl.isNewCombox() || true === newControl.isNewMultiCombox()
//         || true === newControl.isNewList() || true === newControl.isNewMultiList()) ) {
//       if ( true === property.isNewControlCanntEdit
//           && property.isNewControlCanntEdit !== newControl.isReadOnly() ) {
//         this.closeNewDropButton();
//       } else if ( false === property.isNewControlCanntEdit
//         && property.isNewControlCanntEdit !== newControl.isReadOnly() ) {
//         // this.showNewComboxDropButton(false);
//         this.showNewDropButton(false);
//       }
//     }
//   }

//   private renderPages(): any {
//      const { documentCore, currentIndex }  = this.state;
//      const getContentByPageId = documentCore.getContentByPageId;
//      const { total, textProperty, pageProperty } = documentCore.render();
//      return (
//       <Document
//         onScroll={this.handleScroll}
//         total={total}
//         currentIndex={( true === this.bJump ? currentIndex : undefined )}
//         textProperty={textProperty}
//         pageProperty={pageProperty}
//         getContentByPageId={getContentByPageId}
//         editorContainer={this.myRef.current}
//         documentCore={documentCore}
//         handleRefresh={this.handleRefresh}
//         cursorInNewControlName={this.cursorInNewControlName}
//         host={this}
//         cursorType={documentCore.getCursorType()}
//         isShowComboxDropButton={this.state.isShowComboxDropButton}
//         isShowDateDropButton={this.state.isShowDateDropButton}
//         isShowComboxList={this.state.isShowComboxList}
//         isShowDateList={this.state.isShowDateList}
//         closeNewBoxList={this.closeNewBoxList}
//         showNewBoxList={this.showNewBoxList}
//       />
//     );
//   }

//   private renderPageMenu = () => {
//     const { documentCore }  = this.state;
//     const { pageProperty } = documentCore.render();
//     return (
//       <PageMenu
//         pageProperty={pageProperty}
//         handleClickOutsideDropdown={this.handleClickOutsideDropdown}
//         clickOutsideDropdown={this.state.clickOutsideDropdown}
//         changePagePro={documentCore.setPageProperty}
//         changeParagraphPro={documentCore.setParagraphProperty}
//         getParagraphPro={documentCore.getParagraphProperty}
//         refresh={this.handleRefresh}
//         testDocument={this.testDocument}
//         testDocumentXml={this.testDocumentXml}
//         documentCore={documentCore}
//         imageConfigModalType={this.state.imageConfigModalType}
//         handleModalState={this.handleModalState}
//         showEquationEditModal={this.state.showEquationEditModal}
//         equationType={this.state.equationType}
//         host={this}
//         xmlProps={this.state.xmlProps}
//       />
//     );
//   }

//   private renderNewControlTips = () => {
//     const { isShowNewControlTips } = this.state;

//     return (
//         <div className={true === isShowNewControlTips ? 'new-control-tooltip' :
//         'new-control-tooltip hide'} ref={this.newControlTips}>
//             {this.renderFocusNewControlTips()}
//         </div>
//       );
//   }

//   /**
//    * 处理table时弹出的窗口
//    */
//   private renderPopMenu = () => {
//     const { documentCore, tablePopMenuModalType }  = this.state;
//     if ( TableMenuModalType.DeleteTableCells === tablePopMenuModalType
//         || TableMenuModalType.SplitTableCells === tablePopMenuModalType ) {

//       return (
//         <TablePopMenu
//           documentCore={documentCore}
//           refresh={this.handleRefresh}
//           handleTableMenuModalState={this.handleTableMenuModalState}
//           tablePopMenuModalType={tablePopMenuModalType}
//         />
//       );
//     }

//     return null;
//   }

//   private renderPageToolbar = () => {
//     const { documentCore }  = this.state;
//     return (
//       <PageToolbar
//           changeParagraphAlignment={documentCore.setParagraphAlignment}
//           changeTextFontFamily={documentCore.setTextFontFamily}
//           changeTextFontSize={documentCore.setTextFontSize}
//           changeTextBold={documentCore.setTextBold}
//           refresh={this.handleRefresh}
//           spacingConfig={SPACING_CONFIG}
//           colorConfig={COLOR_CONFIG}
//           host={this}
//           documentCore={this.state.documentCore}
//           testDocumentXml={this.testDocumentXml}
//       />
//     );
//   }

//   private isCacheEnabled = () => {
//     return this.state.isCacheEnabled;
//   }

//   private clearCacheSaveTimer = () => {
//     clearTimeout(this.cacheSaveTimer);
//     this.cacheSaveTimer = null;
//     // console.log('clr')
//   }

//   /**
//    * 显示newcontrol的提示信息框
//    */
//   private showNewControlTips = (newControlName: string) => {
//     if ( this.curFocusNewControlName === newControlName && true === this.state.isShowNewControlTips) {
//       return ;
//     }

//     const content = this.state.documentCore.getNewControlTips(this.curFocusNewControlName);

//     if ( null != content && '' !== content ) {
//       const newControlTips = this.newControlTips.current;

//       newControlTips.style.top = this.viewCursorPoint[1] + 'px';
//       newControlTips.style.left = this.viewCursorPoint[0] + 10 + 'px';
//       this.setState({isShowNewControlTips: true});
//     }
//   }

//   /**
//    * 清除焦点在newControl时的所有状态值
//    * 并关闭newcontrol的提示信息框
//    */
//   private closeNewControlTips = () => {
//     if ( true === this.state.isShowNewControlTips ) {
//       this.setState({isShowNewControlTips: false});
//     }
//     this.bShowNewControlTip = false;
//     this.curFocusNewControlName = null;
//   }

//   /**
//    * 显示newcontrol的提示信息框
//    */
//   private showNewBoxList = (bClickDropButton: boolean = false) => {
//     const { documentCore } = this.state;
//     const newControl = documentCore.getCurrentNewControl();

//     if ( newControl && (true === newControl.isNewCombox() || true === newControl.isNewMultiCombox()
//       || true === newControl.isNewList() || true === newControl.isNewMultiList()
//       || true === newControl.isNewDateBox()) ) {
//       if ( null == this.curInNewControlName ) {
//         this.bShowBoxList = true;
//         this.curInNewControlName = newControl.getNewControlName();
//       } else {
//         if ( newControl.getNewControlName() !== this.curInNewControlName ) {
//           this.bShowBoxList = true;
//           this.curInNewControlName = newControl.getNewControlName();

//           if ( true === this.state.isShowComboxList ) {
//             this.setState({isShowComboxList: false});
//           } else if ( true === this.state.isShowDateList) {
//             this.setState({isShowDateList: false});
//           }
//         } else {
//           this.bShowBoxList = false;

//           if ( true === this.state.isShowComboxList ) {
//             this.setState({isShowComboxList: false});
//           } else if (true === this.state.isShowDateList) {
//             this.setState({isShowDateList: false});
//           } else if ( (true === this.state.isShowComboxDropButton || true === this.state.isShowDateDropButton)
//           && true === bClickDropButton ) {
//             this.bShowBoxList = true;
//           }
//         }
//       }
//     } else {
//       this.curInNewControlName = null;
//       this.bShowBoxList = false;
//     }

//     if ( false === this.bShowBoxList
//       && (false === this.state.isShowComboxList || false === this.state.isShowDateList) ) {
//       return ;
//     }

//     if ( (true === this.state.isShowComboxList || true === this.state.isShowDateList)
//       || true === newControl.isReadOnly() ) {
//       this.closeNewBoxList();
//       return ;
//     }

//     if ( newControl && (true === newControl.isNewCombox() || true === newControl.isNewMultiCombox()
//       || true === newControl.isNewList() || true === newControl.isNewMultiList()) ) {
//       this.setState({isShowComboxList: true});
//     } else if (newControl && true === newControl.isNewDateBox()) {
//       this.setState({isShowDateList: true});
//       // 隐藏光标 - 日期弹框展开时，光标必不闪
//       this.cursorBlur();
//       this.setCursorVisible(false);
//     }
//   }

//   /**
//    * 清除焦点在newControl时的所有状态值
//    * 并关闭newcontrol的提示信息框
//    */
//   private closeNewBoxList = () => {
//     if ( true === this.state.isShowComboxList ) {
//       this.setState({isShowComboxList: false});
//     } else if (true === this.state.isShowDateList) {
//       this.setState({isShowDateList: false});
//     }
//     this.bShowBoxList = false;
//   }

//   // private showNewComboxDropButton = (bNewReadOnly: boolean = true) => {
//   private showNewDropButton = (bNewReadOnly: boolean = true) => {
//     const { documentCore } = this.state;
//     const newControl = documentCore.getCurrentNewControl();

//     if ( newControl && true === newControl.isReadOnly() && bNewReadOnly ) {
//       this.closeNewDropButton();
//       return ;
//     }

//     if ( newControl && (true === newControl.isNewCombox() || true === newControl.isNewMultiCombox()
//       || true === newControl.isNewList() || true === newControl.isNewMultiList()) ) {
//       if ( null == this.curInNewControlName ) {
//         this.setState({isShowComboxDropButton: false});
//       } else {
//         if ( newControl.getNewControlName() !== this.curInNewControlName ) {

//           if ( true === this.state.isShowComboxDropButton ) {
//             this.setState({isShowComboxDropButton: false});
//           }
//         }
//       }

//       if ( newControl.getNewControlName() === this.curInNewControlName && true === this.state.isShowComboxDropButton ) {
//         return ;
//       }

//       this.setState({isShowComboxDropButton: true});
//     } else if (newControl && newControl.isNewDateBox() === true) {
//       if ( null == this.curInNewControlName ) {
//         this.setState({isShowDateDropButton: false});
//       } else {
//         if ( newControl.getNewControlName() !== this.curInNewControlName ) {
//           if (this.state.isShowDateDropButton === true) {
//             this.setState({isShowDateDropButton: false});
//           }
//         }
//       }
//       if ( newControl.getNewControlName() === this.curInNewControlName && true === this.state.isShowDateDropButton ) {
//         return ;
//       }

//       this.setState({isShowDateDropButton: true});

//     } else {
//       this.closeNewDropButton();
//     }
//   }

//   /**
//    * 清除焦点在newControl时的所有状态值
//    * 并关闭newcontrol的下拉框
//    */
//   private closeNewDropButton = () => {
//     const { documentCore } = this.state;

//     if ( true === this.state.isShowComboxDropButton ) {
//       this.setState({isShowComboxDropButton: false, isShowComboxList: false});
//       this.bShowBoxList = false;
//     } else if (true === this.state.isShowDateDropButton) {
//       this.setState({isShowDateDropButton: false, isShowDateList: false});
//       this.bShowBoxList = false;
//     }
//   }

//   private addGobalEventListener(): void {
//     gEvent.addEvent(this.docId, gEventName.Mousedown, this.handleMouseDown);
//     gEvent.addEvent(this.docId, gEventName.Mousemove, this.handleMouseMove);
//     gEvent.addEvent(this.docId, gEventName.Mouseup, this.handleMouseUp);
//     gEvent.addEvent(this.docId, 'visibilitychange', this.handleWindowVisible);
//     gEvent.addEvent(this.docId, gEventName.Dblclick, this.handleDoubleClick);
//     gEvent.addEvent(this.docId, gEventName.Click, this.handleClick);
//     // table
//     gEvent.addEvent(this.docId, 'deleteTableCells', this.handleTableEvent);
//     gEvent.addEvent(this.docId, 'splitTableCells', this.handleTableEvent);
//     gEvent.addEvent(this.docId, gEventName.ContentChange, this.contentChange);
//     // cursor
//     gEvent.addEvent(this.docId, 'updateCursorType', this.handleUpdateCursorType);
//   }

//   private removeGobalEventListener(): void {
//     gEvent.deleteEvent(this.docId, gEventName.Mousedown, this.handleMouseDown);
//     gEvent.deleteEvent(this.docId, gEventName.Mousemove, this.handleMouseMove);
//     gEvent.deleteEvent(this.docId, gEventName.Mouseup, this.handleMouseUp);
//     gEvent.deleteEvent(this.docId, 'visibilitychange', this.handleWindowVisible);
//     gEvent.deleteEvent(this.docId, gEventName.Dblclick, this.handleDoubleClick);
//     gEvent.deleteEvent(this.docId, gEventName.Click, this.handleClick);
//     gEvent.deleteEvent(this.docId, gEventName.ContentChange, this.contentChange);
//     // table
//     gEvent.deleteEvent(this.docId, 'deleteTableCells', this.handleTableEvent);
//     gEvent.deleteEvent(this.docId, 'splitTableCells', this.handleTableEvent);
//   }

//   private contentChange = (): void => {
//     if (this.externalEvent) {
//       setTimeout(() => {
//         const newControl = this._currentNewControl;
//         if (newControl) {
//           this.externalEvent.nsoStructChanged(newControl.getNewControlName(), newControl.getType());
//         }
//       }, 0);
//     }

//   }

//   private handleTableEvent = (type: string, ...arr: []) => {
//      switch (type) {
//        case 'deleteTableCells': {
//          this.setState({tablePopMenuModalType: TableMenuModalType.DeleteTableCells});
//          break;
//        }

//        case 'splitTableCells': {
//          alert('拆分非法！');
//          break;
//        }

//        default:
//          break;
//      }
//   }

//   private handleTableMenuModalState = () => {
//     this.setState({tablePopMenuModalType: TableMenuModalType.None});
//   }

//   /**
//    * 获得当前滚动到第几页,强制刷新页面
//    */
//   private handleScroll = (event: any) => {
//     this.setState(function(prevState, props) {
//       return {
//         isScroll: !this.state.isScroll,
//       };
//     });

//     // 选中多页，选中的页面会被替换，需要重新render
//     if ( this.selections ) {
//       this.renderSection();
//     }

//   }

//   /**
//    * 光标元素插入
//    */
//   private insertCursor = (pageNode: SVGElement, pageId?: number) => {

//     if (this.cursor) {
//       let newPageId;
//       const oldPageId = this.cursor.ownerSVGElement.getAttribute('page-id');
//       if (pageNode) {
//         newPageId = pageNode.getAttribute('page-id');
//       } else if (pageId) { // cross page
//         newPageId = pageId;
//       }

//       if ( pageNode && ( newPageId !== oldPageId || 0 === pageNode.getElementsByTagName('line').length ) ) {
//         // move the same cursor to new page
//         pageNode.appendChild(this.cursor);
//         pageNode.parentNode.appendChild(this.textArea.wrapper());
//       }
//     } else {
//       const cursor = document.createElementNS('http://www.w3.org/2000/svg', 'line');
//       cursor.setAttribute('id', 'cursor');
//       cursor.setAttribute('stroke', 'black');
//       cursor.setAttribute('stroke-width', '2');
//       cursor.setAttribute('style', 'visibility:hidden');
//       pageNode.appendChild(cursor);
//       this.cursor = cursor;
//       this.textArea.insertHiddenTextArea(pageNode);
//     }

//   }

//   private handleUpdateCursorType = (type: string, ...arr: []) => {
//     // switch (type) {
//     //   case 'updateCursorType': {
//     //   //   this.setState({tablePopMenuModalType: TableMenuModalType.DeleteTableCells});
//     //   //   break;
//     //   // }

//     //   // case 'splitTableCells': {
//     //   //   alert('拆分非法！');
//     //     this.changeDocument();
//     //     break;
//     //   }

//     //   default:
//     //     break;
//     // }
//     this.changeDocument();
//   }

//   private newControlHandler(): void {
//     // 在newControlChange里进行刷新是进行拦截
//     if (this._newControlFresh === true) {
//       this._newControlFresh = false;
//       return;
//     }
//     clearTimeout(this._newControlTimeout);
//     this._newControlTimeout = setTimeout(() => {
//       this.newControlChange();
//     }, 10);
//   }

//   private newControlFocus(newControl: any): void {
//     if (this.externalEvent) {
//       if (newControl) {
//         this.externalEvent.nsoStructGainFocus(newControl.getNewControlName(), newControl.getType());
//       } else {
//         const current = this._currentNewControl;
//         if (current) {
//           this.externalEvent.nsoStructLostFocus(current.getNewControlName(), current.getType());
//         }
//       }
//     }
//   }

//   private newControlChange(): void {
//     const documentCore = this.state.documentCore;
//     const currentNewControl = documentCore.getCurrentNewControl();
//     const newControl = this._currentNewControl;
//     // 假如同在一个newControl里移动光标，那么下面步骤的都不会再进行
//     if (currentNewControl === newControl) {
//       return;
//     }
//     this.newControlFocus(currentNewControl);

//     this._currentNewControl = currentNewControl;
//     if (newControl && newControl.isNumberBox()) {
//       const result = newControl.updateValidInput();
//       // 合理数据，不需要进行修改
//       if (result === NewControlErrorInfo.Success) {
//         return;
//       }

//       // 需要更新
//       if (result === NewControlErrorInfo.Refresh) {
//         this._newControlFresh = true;
//         this.handleRefresh();
//         return;
//       }
//       const callback = () => {
//         documentCore.selectNewControlContent(newControl.getNewControlName());
//         this._newControlFresh = true;
//         this.handleRefresh();
//       };
//       message.error(result)
//       .then(() => {
//         callback();
//       });
//       return;
//     }

//   }

//   /**
//    * 设置光标位置
//    * @param position
//    */
//   private setCursorPosition(position: ICursorProperty): void {
//     const { y1, y2, pageNum } = position;
//     let x = position.x;
//     const oldPosition: any = this._oldPosition || {};
//     // 阻止多次点同一个点，光标还在跳动的情况
//     if (x === oldPosition.x && y1 === oldPosition.y && pageNum === oldPosition.pageNum) {
//       return;
//     }
//     this._oldPosition = { x, y: y1, pageNum };

//     //  console.log(position.x);
//     const cursor = this.cursor;

//     // cursor stroke width is cut half at 0
//     if (x === 0) {
//       x = 1;
//     }

//     cursor.setAttribute('x1', `${x}`);
//     cursor.setAttribute('x2', `${x}`);
//     cursor.setAttribute('y1', `${y1}`);
//     cursor.setAttribute('y2', `${y2}`);
//     if (this.scrollTool) {
//       this.scrollTool.scrollToYByCursor(y1, y2);
//     }
//     this.textArea.setTextAreaPosition(x, y1);
//   }

//     /**
//      * 打开文档时，初始化光标位置：文档第一页第一段段首
//      */
//   private initCursorPos(): void {
//     const { documentCore } = this.state;

//     const position = documentCore.getCursorPositionBySetPoint(0, 0, 0);

//     const newPageNode = this.getNewPageNode(position);

//     if ( null != newPageNode ) {
//       this.insertCursor(newPageNode);
//       this.setCursorPosition(position);
//       this.cursorFocused();
//       this.textArea.focus();
//       // this.setCursorVisible(true);
//     }
//   }

//   /**
//    * 光标开始闪呀
//    */

//   private cursorFocused(): void {
//     clearInterval(this.timer);
//     this.timer = setInterval(() => {
//       const isHidden = this.cursor.style.visibility === 'hidden';
//       this.cursor.style.visibility = isHidden ? 'initial' : 'hidden';
//     }, 550);
//   }

//   /**
//    * 光标移动时：需要一直保持显示状态，不能为hidden
//    */
//   private setCursorVisible( bVisible: boolean ): void {
//     if ( 'hidden' === this.cursor.style.visibility ) {
//       if ( true === bVisible ) {
//         this.cursor.style.visibility = 'initial';
//       }
//     } else {
//       if ( false === bVisible ) {
//         this.cursor.style.visibility = 'hidden';
//       }
//     }
//   }

//   private cursorBlur(): void {
//     clearInterval(this.timer);
//   }

//   /**
//    *   插入文字
//    *
//    */
//   private insertCompositionInput(content: string, bComposition: boolean): void {
//     // todo: focus 是一个新的portion blur时需要根据具体情况 是否和前一个合并这个新portion
//     const { documentCore } = this.state;
//     if ( null == content || '' === content || false === documentCore.canInput() ) {
//       return ;
//     }
//     if ( false === bComposition ) {
//       documentCore.onCompositionStart();
//       documentCore.insertCompositionInput(content, bComposition);
//       // this.changeDocument();
//       // this.updateCursor(true, true, true);
//     }
//   }

//     /**
//      * 开始复合文本输入
//      */
//     private compositionStart(): void {
//         const { documentCore } = this.state;
//         this.closeNewControlTips();
//         this.closeNewBoxList();
//         if ( false === documentCore.canInput() ) {
//           return ;
//         }

//         // documentCore.onCompositionStart();
//     }

//     private compositionUpdate(): void {
//       // ;
//     }

//   /**
//    * 一次中文输入完成：输入完后按空格键
//    */
//   private compositionEnd(content: string, bComposition: boolean, event: any): void {
//     const { documentCore } = this.state;
//     if ( false === documentCore.canInput() || null == content || '' === content ) {
//       return ;
//     }
//     if ( true === bComposition ) {
//       documentCore.onCompositionStart();
//     }
//     // console.log('compositionEnd')
//     documentCore.insertCompositionInput(content, bComposition);

//     // 微软输入法
//     if ( true === bComposition && '' !== content ) {
//     // if ( 'Process' === event.key && 'Space' === event.code) {
//       this.changeDocument();
//       this.updateCursor(true, true, true);
//     }
//     if ( true === documentCore.isCursorInNewControl() ) {
//       const newControl = documentCore.getCurrentNewControl();

//       if ( newControl && (true === newControl.isNewCombox() || true === newControl.isNewMultiCombox()
//               || true === newControl.isNewList() || true === newControl.isNewMultiList())
//           && true === this.state.isShowComboxDropButton ) {
//           this.setState({isShowComboxDropButton: true});
//       }
//     }
//   }

//   /**
//    * textArea和document都会回调此函数，在页面滚动时，textArea可能会被销毁，
//    * 此时按键操作，则只有document回调此函数进行处理
//    * keyDown
//    * @param event
//    */
//   private onKeyDown(event: any, bComposition: boolean): void {
//       this.closeNewControlTips();
//       this.closeNewBoxList();
//       // if ( true === bComposition ) {
//       //   return ;
//       // }

//       // console.log('editor keydown')
//       if ( this instanceof EmrEditor && this.textArea ) {
//           const { documentCore } = this.state;
//           this.gKeyEvent.keyCode = event.keyCode;
//           this.gKeyEvent.bCtrlKey = event.ctrlKey;
//           this.gKeyEvent.bAltKey = event.altKey;
//           this.gKeyEvent.bShiftKey = event.shiftKey;

//           // 当前按键为功能键，不做任何处理
//           if ( true === EmrEditor.isFuntionKey(this.gKeyEvent) ) {
//               return ;
//           }

//           if (event.keyCode === 33 || event.keyCode === 34) {
//             event.preventDefault();
//             this.scrollTool.jumpNewPage(event.keyCode);
//             return;
//           }

//           const oldSelection = documentCore.getDocumentSelection();
//           const bOldSelection = oldSelection.bUse;

//           this.pageIndex = documentCore.getCursorPosition().pageNum;
//           this.bJump = false;

//           documentCore.onKeyDown(this.gKeyEvent);

//           const newSelection = documentCore.getDocumentSelection();
//           // console.log(bOldSelection, newSelection.bUse);

//           // already del after onKeyDown()
//           // console.log(circularParse(circularStringify(this.state.documentCore.getDocument())));

//           // 选择：
//           // 1. shift键 + left/right/up/down
//           // 2. ctrl + a: 全选
//           // 3. undo/redo : 有选择操作
//           // 只要当前有内容选中，就ok
//           if ( true === newSelection.bUse ) {

//               // update gMouseEvent class
//               const point = documentCore.getCursorPosition();
//               this.gMouseEvent.pointX = point.x;
//               this.gMouseEvent.pointY = point.y1;

//               // always clear selection before render (before selections[] is cleared)
//               this.clearSection();

//               const pageNode = getPageElement(event.target);
//               const pageId = this.getPageId(pageNode, event);

//               this.selections = documentCore.getSelectionBounds(this.gMouseEvent, pageId);

//               // console.log(selection)

//               // undo/redo
//               // todo: 只进行局部（某个段落、或某个页面）刷新
//               if ( true === newSelection.bUse && true === EmrEditor.isUndoRedo(this.gKeyEvent) ) {
//                   // this.forceUpdate();
//                   this.changeDocument();
//               }

//               // 只有在有内容选中，并且当前是选中状态，才进行选中区域的渲染
//               if ( this.selections ) {
//                   this.renderSetCursor(pageNode);
//                   this.cursorBlur();
//                   this.setCursorVisible(false);
//                   if (!IMAGE_FLAGS.isImageOnClick) {
//                     this.renderSection();
//                   }
//               }
//           } else if ( ( true === bOldSelection ) && ( true === event.shiftKey || true === event.ctrlKey ) ) {
//               // 选择状态下，单击shift、ctrl键没有效果，不需要进行光标等刷新
//               return;
//           } else {
//               this.clearSection();

//               // 之前选择的状态被清除，确保光标开始闪烁
//               if ( true === bOldSelection && false === newSelection.bUse ) {
//                   // 当前光标所在页面
//                   const curIndex = documentCore.getCursorPosition().pageNum;

//                   if ( curIndex !== this.state.currentIndex || this.pageIndex !== this.state.currentIndex ) {
//                       this.bJump = true;
//                       this.pageIndex = curIndex;
//                       this.setState({currentIndex: curIndex});
//                   }

//                   this.handleWindowVisible();
//               }
//           }

//           // newControl background
//           if ( true === documentCore.isCursorInNewControl() ) {
//             this.renderFocusHightLightNewControl();
//             this.showNewDropButton();
//           } else {
//             this.clearHightLightNewControl();
//             this.closeNewDropButton();
//           }
//       }
//   }

//   /**
//    * keyUp
//    */
//   private onKeyUp(event: any, bComposition: boolean): void {
//       if ( true === bComposition || ( 'Process' === event.key && 'Space' !== event.code )) {
//         return ;
//       }
//       // console.log('editor keyup')
//       // console.log(event)
//       // 当前按键为功能键，不做任何处理
//       if ( true === EmrEditor.isFuntionKey(this.gKeyEvent)) {
//       // ----key: bug31430 20190704--- || true === this.isSelected() )
//         return ;
//       }

//       // 上，下，左，右键不需要重刷页面，只有在跨页等需要重刷的情况下才重刷
//       // redo/undo时不需要重刷页面，react会自动重刷一次页面，这样减少一次页面刷新
//       const bForceUpdate = (false === EmrEditor.isLRUDKey(this.gKeyEvent.keyCode))
//                     && ( false === EmrEditor.isUndoRedo(this.gKeyEvent) );

//       // update paraelem's position x, position y here
//       this.updateCursor(true, bForceUpdate);
//       this.gKeyEvent.keyCode = 0;
//       // console.log(this.state.documentCore.getDocument())
//     }

//   private onKeyPress(): void {
//       // ;
//   }

//   private handleKeyDown = (event: any) => {
//     return ;
//   }

//   private handleKeyUp = (event: any) => {
//     return ;
//   }

//   /**
//    * 处理mouse down
//    */
//   private handleMouseDown = (event: any) => {
//     this._bMousedown = true;
//     // console.log('editor mousedown')
//     // console.log(event.pageX, event.pageY)
//     // console.log(event.target)

//     const { documentCore } = this.state;
//     // console.log(event.detail);
//     // console.log(documentCore.getDocument())
//     // console.log(documentCore.getDocument().getDrawingObjects()) // console would not show static obj

//     // smooth clicking in modal or mousedown event not expected to influence editor
//     const isClickOnNonSVGDOM = this.isCursorOnNonSVGDOM(event);
//     if (isClickOnNonSVGDOM) {
//       return null;
//     }

//     if (true === this.isCursorOnToolBar(event)) {
//       return null;
//     }

//     if ( TableMenuModalType.None !== this.state.tablePopMenuModalType
//           && 'contextMenu-item' !== event.target.className ) {
//       this.setState({tablePopMenuModalType: TableMenuModalType.None});
//     }

//     const callbackOption = {isStop: false};
//     gEvent.setEvent(this.docId, 'editorStopUnSelection', callbackOption, event);
//     if (callbackOption.isStop === true) {
//       return null;
//     }

//     // this.scrollTool.mousedown(true);

//     const pageNode = getPageElement(event.target);
//     const pointX = event.layerX;
//     const pointY = event.layerY;

//     // check if click on image
//     IMAGE_FLAGS.isImageOnClick = false;
//     IMAGE_FLAGS.isHandlerOnClick = false;
//     if ( event.target.tagName === 'image' ) {
//       // this.cursorBlur();
//       // this.setCursorVisible(false);
//       IMAGE_FLAGS.isImageOnClick = true;
//       documentCore.setImageSelectionInfo(event.target);
//     } else if (event.target.tagName === 'circle' && event.target.className.baseVal.includes('image-handler')) {
//       // this.cursorBlur();
//       IMAGE_FLAGS.isImageOnClick = true;
//       IMAGE_FLAGS.isHandlerOnClick = true;
//       documentCore.setImageSelectionInfo(event.target);
//     } else {
//       // this.cursorFocused();
//       documentCore.setImageSelectionInfo(null);
//     }

//     // set mouseEvent
//     this.gMouseEvent.button = event.button;
//     this.gMouseEvent.bShiftKey = event.shiftKey;
//     this.gMouseEvent.type = MouseEventType.MouseButtonDown;
//     this.gMouseEvent.pointX = pointX;
//     this.gMouseEvent.pointY = pointY;
//     this.gMouseEvent.clickCount = event.detail;

//     // store curposType before dblClick
//     if (event.detail === 1) {
//       this.oldCurposType = documentCore.getDocument().curPos.type;
//     }

//     const pageId = this.getPageId(pageNode, event);

//     let selection = documentCore.getDocumentSelection();
//     // console.log(circularParse(circularStringify(selection)));

//     // If selected area exists
//     if ( !( true === selection.bUse && true === event.shiftKey ) ) {
//       this.clearSection();
//       documentCore.removeSelection();
//     }

//     // console.log(circularParse(circularStringify(documentCore.getDocument())));
//     documentCore.mouseButtonDown(this.gMouseEvent, pageId, pointX, pointY);
//     // console.log(circularParse(circularStringify(documentCore.getDocument())));
//     selection = documentCore.getDocumentSelection();

//     this.pageIndex = documentCore.getCursorPosition().pageNum;
//     this.bJump = false;

//     if (this.shouldUpdateCursor(event)) {
//       this.renderSetCursor(pageNode);
//     }

//     // shift键选择
//     if ( true === selection.bUse && true === this.gMouseEvent.bShiftKey ) {
//       // 隐藏光标
//       this.cursorBlur();
//       this.setCursorVisible(false);

//       // always clear selection before render (before selections[] is cleared)
//       this.clearSection();

//       this.selections = documentCore.getSelectionBounds(this.gMouseEvent, pageId);
//       // console.log(documentCore.getDocumentSelection())
//       // console.log(selections)

//       // 只有在有内容选中，并且当前是选中状态，才进行选中区域的渲染
//       if ( this.selections && true === documentCore.getDocumentSelection().bUse ) {
//         this.renderSection();
//       }
//       // console.log(documentCore.document.selection)
//     }

//     // focus newControl
//     if ( true === documentCore.isCursorInNewControl() ) {
//       this.renderFocusHightLightNewControl();
//       this.showNewBoxList();
//       // this.showNewComboxDropButton();
//       this.showNewDropButton();
//     } else {
//       this.curInNewControlName = null;
//       this.closeNewDropButton();
//       this.clearHightLightNewControl();
//     }
//   }

//   /**
//    * 处理mouse move
//    * @param event
//    */
//   private handleMouseMove = (event: any) => {
//     // if (this._bMousedown !== true) {
//     //   return;
//     // }
//     // console.log('editor mousemove')
//     const { documentCore } = this.state;
//     const isClickOnNonSVGDOM = this.isCursorOnNonSVGDOM(event);
//     if (isClickOnNonSVGDOM) {
//       return null;
//     }

//     this.gMouseEvent.type = MouseEventType.MouseButtonMove;

//     const pageNode = getPageElement(event.target);

//     const pageId = this.getPageId(pageNode, event);

//     this.viewCursorPoint[0] = event.pageX;
//     this.viewCursorPoint[1] = event.pageY;

//     // update gMouseEvent class
//     this.gMouseEvent.pointX = event.layerX;
//     this.gMouseEvent.pointY = event.layerY;
//     // console.log(event.offsetX, event.offsetY); // when crossing page, y might be < 0

//     if (!IMAGE_FLAGS.isHandlerOnClick) {
//       documentCore.mouseButtonMove(this.gMouseEvent, pageId, event.layerX, event.layerY);
//     }

//     if ( documentCore.isMovingTableBorder() ) {
//       this.renderTableNewBorder();
//       return ;
//     }
//     // --- all selections{} should be ready at this point ---
//     this.pageIndex = documentCore.getCursorPosition().pageNum;
//     this.bJump = false;

//     const selection = documentCore.getDocumentSelection();

//     if ( true === selection.bUse && true === selection.bStart && !IMAGE_FLAGS.isHandlerOnClick) {
//       // console.log(documentCore.getDocument())
//       // always clear selection before render (before selections[] is cleared)
//       this.clearSection();

//       this.selections = documentCore.getSelectionBounds(this.gMouseEvent, pageId);

//       // 只有在有内容选中，并且当前是选中状态，才进行选中区域的渲染
//       if ( this.selections && (this.selections.cells.length > 0 || this.selections.lines != null )) {
//           this.closeNewDropButton();
//           // 隐藏光标
//           // mousemove on image polygons should not focus on 1px textarea so as not to jump wildly
//           if (!IMAGE_FLAGS.isHandlerOnClick) {
//             this.renderSetCursor(pageNode);
//           }
//           // console.log('hide cursor')
//           this.cursorBlur();
//           this.setCursorVisible(false);
//           if (!IMAGE_FLAGS.isImageOnClick) {
//             this.renderSection();
//           }
//       }
//     }

//     // 非文本选择中，光标在同一位置持续3秒
//     const newControlName = documentCore.getFocusNewControlName(this.gMouseEvent, pageId);
//     if ( null != newControlName && false === selection.bStart ) {
//       if ( newControlName !== this.curFocusNewControlName ) {
//         this.closeNewControlTips();
//       }

//       this.bShowNewControlTip = true;
//     } else {
//       this.closeNewControlTips();
//     }

//     // newControl background
//     if ( true === documentCore.isCursorInNewControl()
//     || true === documentCore.isFocusOnNewControl(this.gMouseEvent, pageId)) {
//       this.renderFocusHightLightNewControl(this.gMouseEvent, pageId);
//     } else {
//       this.clearHightLightNewControl();
//     }
//   }

//   /**
//    * 鼠标点击-放开
//    * 判断光标位置 重置位置或者移除光标
//    * @param event
//    */
//   private handleMouseUp = (event: any) => {
//     this._bMousedown = false;
//     // console.log('editor mouseup')
//     // console.log(event.target.tagName)

//     // smooth clicking in modal or mousedown event not expected to influence editor
//     const isClickOnNonSVGDOM = this.isCursorOnNonSVGDOM(event);
//     if (isClickOnNonSVGDOM) {
//       return null;
//     }

//     const callbackOption = {isStop: false};
//     gEvent.setEvent(this.docId, 'editorMouseUpStopUnSelection', callbackOption, event);
//     if (callbackOption.isStop === true) {
//       return null;
//     }

//     // During mouseup of LEFT CLICK, If contextMenu opens, close it
//     if (this.state.showContextMenu && event.button === MouseButtonType.MouseButtonLeft
//         && 'contextMenu-item' !== event.target.className ) {
//       this.setState({showContextMenu: false});
//     }

//     // If mousedown on image and drag, shouldn't scroll page and rerender cursor
//     if (IMAGE_FLAGS.isImageOnClick) {
//       this.textArea.focus(); // events in textArea should be preserved
//       return null;
//     }

//     // this.scrollTool.mousedown(false, event.clientY);

//     this.gMouseEvent.type = MouseEventType.MouseButtonUp;

//     const pageNode = getPageElement(event.target);
//     const pageId = this.getPageId(pageNode, event);

//     const { documentCore } = this.state;

//     documentCore.mouseButtonUp(this.gMouseEvent, pageId, event.layerX, event.layerY);

//     // const selection = documentCore.getDocumentSelection();

//     if (this.shouldUpdateCursor(event)) {
//       this.newControlHandler();

//       if (!this.isControlLoseFocus()) {
//         this.renderSetCursor(pageNode);
//       }

//       // this.textArea.focus(); // this alone might be enough
//     }

//     this.closeNewControlTips();

//     // if ( true === this.isSelected() || IMAGE_FLAGS.isImageOnClick || IMAGE_FLAGS.isHandlerOnClick ) {
//     if ( true === this.isSelected() ) {
//         // console.log('hide cursor')
//         this.closeNewDropButton();
//         this.cursorBlur();
//         this.setCursorVisible(false);
//         if (!IMAGE_FLAGS.isImageOnClick) {
//           this.selections = documentCore.getSelectionBounds(this.gMouseEvent, pageId);
//           this.renderSection();
//         }
//     }

//     // console.log(circularParse(circularStringify(documentCore.getDocumentSelection())))
//   }

//   /**
//    * 清除选区
//    * @param selected
//    */
//   private clearSection = () => {
//     if ( this.selections ) {
//         const lines = this.selections.lines;
//         if ( lines ) {
//           this.clearSectionBackground(lines, RenderSectionBackgroundType.ParagragphLineSelection);
//         }

//         const drawCells = this.selections.cells;
//         if ( drawCells ) {
//           this.clearSectionBackground(drawCells, RenderSectionBackgroundType.TableCellSelection);
//         }
//     }

//     this.selections = null;
//     this.setSelect(false);
//   }

//   /**
//    * 渲染选区
//    * @param e
//    * @param isUp 选区方向
//    */
//   private renderSection = (e?: MouseEvent, selections?: IDrawSelectionBounds ) => {

//     if ( null === this.selections ) {
//       this.setSelect(false);
//       return;
//     }

//     const drawLines = this.selections.lines;
//     const drawCells = this.selections.cells;

//     if ( ( !drawLines || 0 === drawLines.length ) && ( !drawCells || 0 === drawCells.length ) ) {
//         return;
//     }

//     if ( drawLines && 0 < drawLines.length ) {
//       this.renderSectionBackground(drawLines, RenderSectionBackgroundType.ParagragphLineSelection);
//     }

//     if ( drawCells && 0 < drawCells.length ) {
//       this.renderSectionBackground(drawCells, RenderSectionBackgroundType.TableCellSelection);
//     }

//     this.setSelect(true);
//     gEvent.setEvent(this.docId, gEventName.Selection);
//   }

//   private renderFocusHightLightNewControl = (mouseEvent?: MouseEventHandler, pageIndex?: number,
//                                              bRenderTips: boolean = true) => {
//     const {documentCore} = this.state;

//     const cursorInNewControlBounds = documentCore.getNewControlsFocusBounds();
//     const mouseFocusNewControlBounds = documentCore.getNewControlsFocusBounds(mouseEvent, pageIndex);

//     // 同一行有多个newControl，且有newControl跨行，先选中跨行的newControl， 然后再选中不跨行的newControl
//     if ( this.newControlsCursorInBounds && 0 < this.newControlsCursorInBounds.bounds.length ) {
//         this.clearSectionBackground(this.newControlsCursorInBounds.bounds,
//                  RenderSectionBackgroundType.NewControlCursorIn);
//     }

//     if ( this.newControlsMouseFocusBounds
//          && 0 < this.newControlsMouseFocusBounds.bounds.length ) {
//         this.clearSectionBackground(this.newControlsMouseFocusBounds.bounds,
//                     RenderSectionBackgroundType.NewControlFocus);
//     }

//     if ( mouseFocusNewControlBounds
//         && 0 < mouseFocusNewControlBounds.bounds.length ) {
//       if ( true === this.bShowNewControlTip && true === bRenderTips ) {
//         this.showNewControlTips(mouseFocusNewControlBounds.newControlName);
//         this.curFocusNewControlName = mouseFocusNewControlBounds.newControlName;
//       } else {
//         this.curFocusNewControlName = null;
//       }
//     }

//     this.newControlsCursorInBounds = cursorInNewControlBounds;
//     this.newControlsMouseFocusBounds = mouseFocusNewControlBounds;

//     if ( ( !cursorInNewControlBounds || null === cursorInNewControlBounds.newControlName )
//       && ( !mouseFocusNewControlBounds || null === mouseFocusNewControlBounds.newControlName ) ) {
//         return;
//     }

//     if ( cursorInNewControlBounds && 0 < cursorInNewControlBounds.bounds.length ) {
//       this.cursorInNewControlName = cursorInNewControlBounds.newControlName;
//       if ( mouseFocusNewControlBounds &&
//         cursorInNewControlBounds.newControlName === mouseFocusNewControlBounds.newControlName ) {
//         this.clearSectionBackground(mouseFocusNewControlBounds.bounds, RenderSectionBackgroundType.NewControlFocus);
//         this.newControlsMouseFocusBounds = null;
//       }

//       this.renderSectionBackground(cursorInNewControlBounds.bounds,
//             RenderSectionBackgroundType.NewControlCursorIn, cursorInNewControlBounds.color);
//     }

//     if ( mouseFocusNewControlBounds && 0 < mouseFocusNewControlBounds.bounds.length ) {

//       if ( cursorInNewControlBounds &&
//         cursorInNewControlBounds.newControlName === mouseFocusNewControlBounds.newControlName ) {
//         return ;
//       }

//       this.renderSectionBackground(mouseFocusNewControlBounds.bounds,
//             RenderSectionBackgroundType.NewControlFocus, mouseFocusNewControlBounds.color);
//     }
//   }

//   private renderFocusNewControlTips = () => {
//     if ( true === this.state.isShowNewControlTips && null != this.curFocusNewControlName ) {
//       const content = this.state.documentCore.getNewControlTips(this.curFocusNewControlName);
//       return (
//         <div className='tooltiptext'>
//             {content}
//         </div>
//       );
//     }

//     return null;
//   }

//   private clearHightLightNewControl = () => {
//     if ( this.newControlsCursorInBounds ) {
//       const lines = this.newControlsCursorInBounds.bounds;
//       if ( lines ) {
//         this.clearSectionBackground(lines, RenderSectionBackgroundType.NewControlCursorIn);
//       }
//     }

//     if ( this.newControlsMouseFocusBounds ) {
//       const lines = this.newControlsMouseFocusBounds.bounds;
//       if ( lines ) {
//         this.clearSectionBackground(lines, RenderSectionBackgroundType.NewControlFocus);
//       }
//     }

//     this.newControlsCursorInBounds = null;
//     this.newControlsMouseFocusBounds = null;
//     this.cursorInNewControlName = null;
//   }

//   private renderSectionBackground(renderLine: any[], type: RenderSectionBackgroundType, color?: string): void {
//     for (let index = 0, length = renderLine.length; index < length; index++) {
//       const item = renderLine[index];
//       if ( null != item ) {
//         const className = ( type === RenderSectionBackgroundType.TableCellSelection ) ?
//                                 `${type}-${item.id}-${item.pageIndex}` : `${type}-${item.line.id}`;
//         const tarLines = document.getElementsByClassName(className);
//         const tarLineIndex = 0;

//         const tarLine: any = tarLines[tarLineIndex];

//         // 选中多页，选中的页面会被替换，tarLine已经不在
//         if ( undefined !== tarLine && null !== tarLine ) {
//           if ( RenderSectionBackgroundType.ParagragphLineSelection === type
//               || RenderSectionBackgroundType.TableCellSelection === type ) {
//             tarLine.classList.add('selection-selected');
//           } else {
//             tarLine.classList.add('newcontrol-focus');
//           }

//           tarLine.setAttribute('x', `${item.x}`);
//           tarLine.setAttribute('width', `${item.width}`);

//           // if ( null != color ) {
//           //   tarLine.setAttribute('style', `fill:${color}`);
//           // }
//         }
//       }
//     }
//   }

//   private clearSectionBackground(renderLine: any[], type: RenderSectionBackgroundType): void {
//     for (let index = 0, length = renderLine.length; index < length; index++) {
//       const item = renderLine[index];
//       if ( null != item ) {
//         const className = ( type === RenderSectionBackgroundType.TableCellSelection ) ?
//                                 `${type}-${item.id}-${item.pageIndex}` : `${type}-${item.line.id}`;
//         const selectionCollection = document.getElementsByClassName(className);

//         // If parapage, there would exist two (or more, consider parapage > 2) such nodes
//         for (let i = 0, length2 = selectionCollection.length; i < length2; i++) {
//             const elem = selectionCollection[i];
//             if (elem) {
//               if ( RenderSectionBackgroundType.ParagragphLineSelection === type
//                 || RenderSectionBackgroundType.TableCellSelection === type ) {
//                 elem.classList.remove('selection-selected');
//               } else {
//                 elem.classList.remove('newcontrol-focus');
//               }
//             }
//         }
//       }
//     }
//   }

//   private renderTableNewBorder = () => {
//     this.changeDocument();
//   }
//   /**
//    * 页面失去或获得焦点
//    */
//   private handleWindowVisible = () => {
//       const isHidden = document.hidden;
//       if (isHidden) {
//           clearInterval(this.timer);
//       } else {
//         // console.log('fuking he')
//           this.cursorFocused();
//       }
//   }

//   private handleClick = (e: any): void => {
//     if (this.externalEvent) {
//       setTimeout(() => {
//         const newControl = this._currentNewControl;
//         if (!newControl) {
//           return;
//         }
//         this.externalEvent.nsoStructClick(newControl.getNewControlName(), newControl.getType());
//       }, 10);
//     }
//   }

//   private handleDoubleClick = (e: any) => {
//     // console.log('editor doubleclick');
//     const {documentCore} = this.state;

//     if (e.target.tagName === 'image') {
//       if (documentCore.isProtectedMode()) {
//         return;
//       }
//       const imageType = documentCore.getImageType();

//       if (imageType === ParaElementType.ParaMedEquation) {
//         const paraEquation = documentCore.getSelectedImage() as ParaEquation;
//         const equationType = paraEquation.equationType;
//         this.setState({showEquationEditModal: true, equationType});
//       }
//     } // headerfooter switch
//     // else if (this.enterOrExitHeaderFooter(e) != null) {
//     //   // console.log('dblclick refreshed')
//     //   this.setState({});
//     //   // this.handleRefresh();
//     //   // this.global_mouseEvent.clickCount = 2;
//     // }

//     if (this.externalEvent) {
//       setTimeout(() => {
//         const newControl = this._currentNewControl;
//         let sName: string = ResultType.StringEmpty;
//         let type: number;
//         if (newControl) {
//           sName = newControl.getNewControlName();
//           type = newControl.getType();
//         }
//         this.externalEvent.nsoStructDBClick(sName, type);
//       }, 10);
//     }

//   }

//   private enterOrExitHeaderFooter(event: any): number {
//     const {documentCore} = this.state;
//     const logicDocument = documentCore.getDocument();
//     const pageMetrics = logicDocument.getPageContentStartPos(this.pageIndex);
//     const y = event.layerY;

//     const curPosType = logicDocument.curPos.type;

//     // console.log(curPosType)
//     // console.log(this.oldCurposType);
//     // console.log(event.target)

//     if (curPosType !== this.oldCurposType) {
//       // enter headerfooter
//       if ((y <= pageMetrics.y || y >= pageMetrics.yLimit) &&
//         (this.oldCurposType === DocCurPosType.Content) &&
//         (curPosType === DocCurPosType.HdrFtr)) {
//         return DocCurPosType.HdrFtr;
//       }
//       // exit headerfooter
//       if ((y > pageMetrics.y && y < pageMetrics.yLimit) &&
//         (this.oldCurposType === DocCurPosType.HdrFtr) &&
//         (curPosType === DocCurPosType.Content)) {
//         return DocCurPosType.Content;
//       }
//     }
//     return null;
//   }

//   /**
//    * close modal
//    */
//   private handleModalState = (type: string) => {
//     if (type === 'equation') {
//       this.setState({showEquationEditModal: false});
//     } else { // image
//       this.setState({imageConfigModalType: ImageConfigModalType.None});
//     }
//   }

//   /**
//    *
//    */
//   private handleClickOutsideDropdown = () => {
//     this.setSelect(false);
//     this.setState({clickOutsideDropdown: false});
//   }

//   /**
//    * 在model层设置cursor并渲染
//    * @param pageNode
//    */
//   private renderSetCursor( pageNode: SVGElement ): void {
//     // console.log('blink func')
//     if (pageNode) {
//       // 右键粘贴时，光标变化暂时代替内容变化
//       // gEvent.setEvent(this.docId, gEventName.ContentChange);
//       const position = this.state.documentCore.getCursorPosition();
//       const newPageId = pageNode.getAttribute('page-id');
//       // console.log(newPageId, position.pageNum)

//       if ( position.pageNum === Number.parseInt(newPageId, 0) ) { // ++++++luo.wen key: bug35954 20190814+++++
//         this.endPoint = position;
//         this.insertCursor(pageNode);
//         this.setCursorPosition(position);
//         this.cursorFocused();
//         this.textArea.focus();
//         this.setCursorVisible(true);
//         gEvent.setEvent(this.docId, gEventName.Selection);
//       }
//     }
//   }

//   /**
//    * 获取当前事件的page id
//    * @param pageNode
//    * @param event
//    */
//   private getPageId(pageNode: SVGElement, event: any): number {
//     let pageId;
//     // when clicking outside svg
//     if (!pageNode) {

//       // assume each page's height is the same
//       const pageHeight = this.state.documentCore.getDocument().pages[0].height;

//       // when clicking parapage margin area
//       if (event.layerY / pageHeight >= 1 ) {
//         pageId = Math.floor(event.layerY / pageHeight) - 1;
//       }

//       // TODO: click left/right of current page

//     } else {
//       pageId = Number.parseInt(pageNode.getAttribute('page-id'), 0);
//     }
//     return pageId;
//   }

//   /**
//    * 更新光标位置
//    * @bComposition 是否有键盘输入
//    * @bForceUpdate 是否需要强制更新：一般只有在对文档内容进行编辑的时候，才需要强制更新
//    * @bResetCursor 是否需要重新插入光标，使当前页面重新获取焦点：在操作工具栏后或其他操作，页面焦点会失去
//    */
//   private updateCursor( bComposition: boolean = false, bForceUpdate: boolean = false,
//                         bResetCursor: boolean = false ): void {
//     const { documentCore } = this.state;
//     const position = documentCore.getCursorPosition();
//     // console.log(position)
//     // console.log(documentCore.document.content[0]);
//     // console.log(circularParse(circularStringify(documentCore.document.content[0]))); // no position yet
//     if (bComposition === false || bComposition === true && bForceUpdate === false) {
//       this.newControlHandler();
//     }
//     // 键盘移动光标时，判断光标是否需要跨页
//     if ( ( null !== this.cursor.ownerSVGElement &&
//       Number.parseInt(this.cursor.ownerSVGElement.getAttribute('page-id'), 0) !== position.pageNum )
//          || ( true === bResetCursor ) ) {
//       let newPageNode = this.getNewPageNode(position);

//       // 当光标移动到页面临界处（换页），当光标所在页面还没有被刷新出来，则需要先刷新所需的页面
//       // if ( !newPageNode && position.pageNum !== this.pageIndex ) {   //----luo.wen key: bug35529 20190814---
//       if ( position.pageNum !== this.pageIndex ) {  // ++++++luo.wen key: bug35529 20190814+++++
//         this.pageIndex = position.pageNum;
//         this.scrollTool.scrollToNewPage(position);

//         // this.bJump = true;
//         this.cursorBlur();
//         this.setCursorVisible(false);
//         if (true === bForceUpdate || this.bJump === true) {  // ++++++luo.wen key: bug35529 20190814+++++
//           this.setState({currentIndex: this.pageIndex});
//         }
//       }

//       newPageNode = this.getNewPageNode(position);
//       if ( null !== newPageNode ) {
//         this.endPoint = position;
//         this.insertCursor(newPageNode);
//         this.setCursorPosition(position);
//         this.cursorFocused();
//         this.textArea.focus();
//       }
//     } else {
//       this.setCursorPosition(position);

//       if ( true === bForceUpdate ) {
//         this.changeDocument();
//       } else {
//         this.cursorFocused();
//       }
//     }
//     // 强制光标可见
//     this.setCursorVisible(true);

//     // same SUSPEND
//     if (IMAGE_FLAGS.isImageOnClick || IMAGE_FLAGS.isHandlerOnClick) {
//       // this.cursorBlur();
//       // this.setCursorVisible(false);
//     }
//   }

//   private testDocument = (data: any) => {
//     // console.log(data);
//     this.state.documentCore.testDocument(data);
//     this.initCursorPos();
//     this.forceUpdate();
//   }

//   /**
//    * 获取文档页的node数组
//    */
//   private getDocumentNode(): SVGElement {
//     if ( null == this.myRef ) {
//       return null;
//     }

//     const editorContainerNode = this.myRef.current;

//     if ( null == editorContainerNode ) {
//       return null;
//     }

//     let documentNode = null;
//     for ( let i = 0; i < editorContainerNode.childElementCount; i++ ) {
//       // 获取当前页面的页码
//       if ( 'grid' === (editorContainerNode.childNodes.item(i) as SVGElement).getAttribute('role') ) {
//         // 获取当前页SVG
//         documentNode = editorContainerNode.childNodes.item(i) ;
//         break;
//       }
//     }

//     return documentNode.childNodes.item(0);
//   }

//   /**
//    * 重新设置光标pageNode
//    * @param position
//    */
//   private getNewPageNode(position: ICursorProperty): SVGElement {
//       const documentNode = this.getDocumentNode();

//       if ( null == documentNode ) {
//         return null;
//       }

//       let curPage = null;
//       for ( let i = 0, length = documentNode.childElementCount; i < length; i++ ) {
//           // 获取当前页面的页码
//           if ( Number.parseInt((documentNode.childNodes.item(i) as SVGElement).getAttribute('page-index'), 0)
//                 === position.pageNum ) {
//               // 获取当前页SVG
//               curPage = documentNode.childNodes.item(i).childNodes.item(0) ;
//               break;
//           }
//       }

//       return curPage;
//   }

//   /**
//    * 设置当前是否是选择状态
//    * @param bSelect
//    */
//   private setSelect( bSelect: boolean ): void {
//     this.bSelected = bSelect;
//   }

//   /**
//    * 是否选中内容
//    */
//   private isSelected(): boolean  {
//     return this.bSelected;
//   }

//   private changeDocument(): void {
//     // console.log('update')
//     this.setState( {bInput: !this.state.bInput} );
//   }

//   /**
//    * 是否在表格，单击右键时，弹出菜单
//    */
//   private isPopTableMenu(): boolean {
//      return ( this.state.documentCore.isInTableCell() || this.state.documentCore.isSelectedTableCells() );
//   }

//   /**
//    *  * 鼠标事件触发时，是否在非svg上
//    */
//   private isCursorOnNonSVGDOM(event: any): boolean {
//     const nonSVGDOM = ['SELECT', 'INPUT', 'BUTTON', 'DIV', 'SPAN', 'LABEL',
//                       'FORM', 'FIELDSET', 'TABLE', 'TH', 'TD', 'TR'];
//     if (nonSVGDOM.includes(event.target.tagName)) {
//       return true;
//     }
//     return false;
//   }

//   /**
//    * 是否鼠标点击工具栏
//    * @param e
//    */
//   private isCursorOnToolBar(e: any): boolean {
//     let item = e.target.parentNode;

//     while (item) {
//       if ('toolbar-icon' === item.className || 'toolbar-icon cye-lm-tag' === item.className) {
//         return true;
//       }

//       if ('emr-editor-container' === item.className) {
//         return false;
//       }

//       item = item.parentNode;
//     }

//     return false;
//   }

//   private shouldUpdateCursor(event: any): boolean {
//     const {documentCore} = this.state;
//     const logicDocument = documentCore.getDocument();
//     const pageMetrics = logicDocument.getPageContentStartPos(this.pageIndex);
//     const y = event.layerY;

//     const curPosType = logicDocument.curPos.type;

//     // 1. in editing header footer mode, click on doc content
//     if (curPosType === DocCurPosType.HdrFtr && (y > pageMetrics.y && y < pageMetrics.yLimit)) {
//       return false;
//     } else if (IMAGE_FLAGS.isHandlerOnClick) {
//       // 2. mousedown on image polygons should not focus on 1px textarea so as not to jump wildly
//       return false;
//     }
//     return true;
//   }

//   /**
//    * 元素是否失去焦点
//    */
//   private isControlLoseFocus(): boolean {
//     const newControl = this.state.documentCore.getCurrentNewControl();
//     // 1. 在日期框且弹框展开时
//     if (newControl && newControl.isNewDateBox() && this.state.isShowDateList === true) {
//       return true;
//     }
//     return false;
//   }

//   /**
//    * 获取业务属性
//    */
//   private getCustomPropertiesInSettings(): Map<string, any> {
//     // TODO: some logic to get "业务属性"

//     const properties = new Map();
//     // example data
//     properties.set('apple', 15);
//     properties.set('soul', 'dark');
//     properties.set('fire', 'fading');
//     properties.set('lords', 'throneless');
//     return properties;
//   }
// }
