@import './global.less';

// #editor-cascade-id {
//     .ReactVirtualized__Grid__innerScrollContainer {
//         &::-webkit-scrollbar {
//             width: 8px;
//         }
//     }
// }

#editor-dom-container {
    z-index: 9999;
    & * {
        box-sizing: border-box;
    }
    #editor-equation-container {
        position: fixed;
        left: -300%;
        top: 0;
        z-index: -9;
        width: 0;
        height: 0;
        opacity: 0;

        // position: fixed;
        // left: 340px;
        // top: 10px;
        // z-index: 999;
        // width: 100px;
        // height: 100px;
        // border: 1px solid red;
    }

    // .editor-message {
    //     display: none;
    //     position: fixed;
    //     left: 0;
    //     top: 0;
    //     z-index: 9999;
    //     width: 100%;
    //     height: 100%;
    //     background-color: rgba(0, 0, 0, 0.02);

    //     &.active {
    //         display: block;
    //     }

    //     .editor-message-box {
    //         position: relative;
    //         left: 0;
    //         right: 0;
    //         top: 50%;
    //         z-index: 1;
    //         width: 200px;
    //         margin: 0 auto;
    //         padding: 5px 10px;
    //         font-size: 13px;
    //         font-family: @fontFamily;
    //         box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.36);
    //         background-color: #fff;
    //         border-radius: 3px;
    //         border: 1px solid #eee;
    //         transform: translateY(-50%);
    //     }

    //     .editor-message-title {
    //         height: 22px;
    //         padding-bottom: 10px;
    //         text-align: left;
    //         font-size: 14px;
    //         line-height: 22px;
    //     }

    //     .editor-message-body {
    //         padding: 0 5px;
    //         text-align: center;
    //         line-height: 24px;
    //     }

    //     .editor-message-btns {
    //         margin-top: 20px;
    //         padding: 5px 0;
    //         text-align: center;
    //         line-height: 22px;
    //     }

    //     .editor-message-btns > span {
    //         margin-right: 20px;
    //         padding: 2px 8px;
    //         cursor: pointer;
    //         border: 1px solid @borderColor;
    //         border-radius: 2px;
    //     }

    //     .editor-message-btns > span:last-child {
    //         margin-right: 0;
    //     }

    //     &.editor-confirm .editor-message-btns > span:last-child {
    //         color: @activeColor;
    //     }
    // }

    // .printFrame-box {
    //     position: fixed;
    //     left: -300%;
    //     top: 85px;
    //     /* width: 146.6mm;
    //     height: 246.2mm;
    //     margin-top: -2.54cm;
    //     margin-left: -3.17cm; */
    //     width: 21cm;
    //     height: 29.7cm;
    //     opacity: 0;
    //     background: #fff;
    // }

    // .print-dialog {
    //     display: none;
    //     position: fixed;
    //     left: 0;
    //     top: 0;
    //     width: 100%;
    //     height: 100%;
    //     z-index: 99;
    //     background: rgba(0, 0, 0, 0.1);

    //     &.visible {
    //         display: block;
    //         opacity: 0;
    //     }

    //     &.active {
    //         display: block;
    //     }
    //     &.print-hide {
    //         left: -500%;
    //         opacity: 0;
    //     }

    //     .print-dialog-box {
    //         position: absolute;
    //         left: 0;
    //         right: 0;
    //         top: 5%;
    //         height: 92%;
    //         margin: auto;
    //         background: #fff;
    //         border: 1px solid #999;
    //         border-radius: 4px;
    //     }
    //     .print-dialog-box-header {
    //         height: 50px;
    //     }
    //     .print-dialog-box-header > div:first-child {
    //         line-height: 50px;
    //         margin-left: 10px;
    //         text-align: left;
    //     }
    //     .print-dialog-box-header-clinc {
    //         display: inline-block;
    //         line-height: 20px;
    //         font-size: 12px;
    //     }
    //     .print-dialog-box-body {
    //         height: calc(100% - 50px);
    //     }

    //     p {
    //         margin: 0;
    //         padding: 0;
    //     }
    // }
}

body {
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
    overflow: hidden; // menu would not be viewable otherwise with several pages
    font-family: PingFang SC,Microsoft YaHei, 微软雅黑, 宋体, Tahoma, Arial; // bottomline
    // font-family: PingFang SC,Microsoft YaHei,\\5FAE\8F6F\96C5\9ED1,\\5B8B\4F53,Tahoma,Arial; // bottomline
}

.hz-editor-container {
    background: #f0f2f5;
    text-align: center;
    overflow: hidden;
    // margin: 0 auto;

    .cursor-pointer {
        svg {
            cursor: pointer;
        }
    }

    .editor-revision-tips {
        display: none;
        position: absolute;
        z-index: 9;
        max-width: 780px;
        min-width: 50px;
        padding: 4px;
        font-size: 8px;
        // -webkit-user-select: none;

        color: #fff;
        border-radius: 2px;
        // background-color: @currenBgColor;
        background-color: #54627B; // TODO: should it be currentBgColor?
        text-align: left;
        border-top: 2px solid #FD8701;

        &.alert {
            border-top: 2px solid #FF3143;
        }

        &.cell-message-tip {
            border-top: 0px solid;
            pointer-events: none;
        }

        &.active {
            display: block;
        }

        &.visible {
            display: block;
            white-space:nowrap;
            opacity: 0;
            user-select: none;
        }

        & > div {
            line-height: 22px;
        }

        // & > i {
        //     display: inline-block;
        //     position: absolute;
        //     bottom: -10px;
        //     &:after {
        //         display: inline-block;
        //         width: 0;
        //         height: 0;
        //         margin-top: 9px;
        //         vertical-align: middle;
        //         border-left: 5px solid  transparent;
        //         border-right: 5px solid transparent;
        //         border-top: 5px solid @currenBgColor;
        //         border-bottom: 5px solid transparent;
        //         content: ' ';
        //     }
        // }
    }

    &.cascade-manager .ReactVirtualized__Grid__innerScrollContainer {
        &::-webkit-scrollbar {
            width: 7px;
            height: 9px;
        }
  
        &::-webkit-scrollbar-thumb {
            /*滚动条里面小方块*/
            border-radius: 1px;
            background-color: #ddd;
        }
        &::-webkit-scrollbar-track {
            /*滚动条里面轨道*/
            box-shadow   : inset 0 0 2px rgba(0, 0, 0, 0.2);
            background   : #f2f2f2;
            border-radius: 7px;
        }
      }

    div.editor-revision-cell-tips {
        display: none;
        position: absolute;
        z-index: 9;
        max-width: 780px;
        min-width: 50px;
        color: #fff;
        text-align: left;

        &.alert {
            border-top: 2px solid #FF3143;
        }

        &.active {
            display: block;
        }

        &.visible {
            display: block;
            white-space:nowrap;
            opacity: 0;
            user-select: none;
        }

        & > div {
            padding: 4px;
            font-size: 8px;
            // -webkit-user-select: none;
            line-height: 22px;
            border-radius: 2px;
            // background-color: @currenBgColor;
            background-color: #54627B; // TODO: should it be currentBgColor?
        }
    }
    

    .editor-revision-cell-tips::before {
        content: '';
        display: block;
        margin: auto;
        width: 0;
        height:0;
        border-left:8px solid transparent;
        border-right: 8px solid transparent;
        border-bottom: 8px solid #54627B;
    }

    div.editor-tips {
        position: fixed;
    }

    .cursor-not-allowed {
        svg {
            cursor: not-allowed;
        }
    }

    .editor-warning-message {
        display: none;
        position: absolute;
        right: 20px;
        top: 10px;
        padding: 5px 10px;
        border: 1px solid #999;
        &.active {
            display: block;
        }
    }

    .safe-message {
        position: absolute;
        top: 20px;
        z-index: 2;
        & > span {
            display: none;
            width: 16px;
            height: 16px;
            cursor: wait;

            &.active {
                display: block;
                &.success {display: none;}
            }
        }

        // .success {
        //     // background: url(../images/success.png) no-repeat center;
        // }

        .warning {
            background: url(../images/warning.png) no-repeat center;
        }

        .info {
            background: url(../images/info.png) no-repeat center;
        }

        .msg {
            position: absolute;
            top: -5px;
            left: 30px;
            padding: 0px 10px;
            color: #fff;
            font-size: 12px;
            line-height: 30px;
            border-radius: 3px;
            background: #54627B;
            &::before {
                position: absolute;
                left: -5px;
                top: 13px;
                width: 0;
                height: 0;
                border-top: 5px solid  #54627B;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                content: " ";
                pointer-events: none;
            }

            span {
                white-space: nowrap;
            }
        }
    }

    .spell-name {
        stroke: #f00;
        stroke-width: 1px;
        stroke-dasharray: 3;
    }
    
    .editor-region-max-length {
        display: none;
        position: absolute;
        z-index: 9;
        max-width: 780px;
        min-width: 50px;
        padding: 4px;
        font-size: 8px;
        // -webkit-user-select: none;

        color: #fff;
        border-radius: 2px;
        background-color: @currenBgColor;
        text-align: left;

        &.active {
            display: block;
        }

        &.visible {
            display: block;
            white-space:nowrap;
            opacity: 0;
            user-select: none;
        }

        & > div {
            line-height: 22px;
        }

        & > i {
            display: inline-block;
            position: absolute;
            bottom: -10px;
            &:after {
                display: inline-block;
                width: 0;
                height: 0;
                margin-top: 9px;
                vertical-align: middle;
                border-left: 5px solid  transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid @currenBgColor;
                border-bottom: 5px solid transparent;
                content: ' ';
            }
        }
    }

    .ReactVirtualized__Grid__innerScrollContainer {
        max-width: 100000px!important;
    }

    .scroll-page {
        position: absolute;
        right: 20px;
        top: 46%;
        padding: 5px 10px;
        color: #fff;
        font-size: 12px;
        background: #000;
        border-radius: 3px;
    }

    .scroll-page-position {
        position: absolute;
        z-index: -1;
        top: 0;
        left: 500%;
        pointer-events: none;
    }

    & *, ::after, ::before {
        box-sizing: border-box;
    }

    .editor-page-header {
        user-select: none;
    }

    /*添加页面间的margin*/
    .page-wrapper:first-child {
        margin-top: 0;
    }
    .page-wrapper {
        /* background-color: #fff; */
        box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.16);
        /* text-align: left;  */
    }
    svg {
        display: block;
        margin: 0 auto;
        -webkit-font-smoothing: antialiased;
        // background: white;
        // cursor: text;
    }

    svg + svg {
        margin-top: 20px;
    }

    &.scroll-not {
        .ReactVirtualized__Grid__innerScrollContainer::-webkit-scrollbar{
            display: none;
        }
    }

    &.scroll-not-x {
        .ReactVirtualized__Grid__innerScrollContainer {
            overflow-x: hidden!important;
        }
    }

    &.scroll-not-y {
        .ReactVirtualized__Grid__innerScrollContainer {
            overflow-y: hidden!important;
        }
    }

    svg line {
        shape-rendering: crispEdges;
    }
    .soft-line {
        font-family: '宋体';
        font-size: 16px;
        fill: #ccc;
    }

    .diagonalLine {
        shape-rendering: initial;
    }

    #textarea_input {
        position: fixed;
        z-index: 0;
        width: 0.5em;
        height: 1em;
        opacity: 0;
        background: transparent;
        -moz-appearance: none;
        appearance: none;
        border: none;
        resize: none;
        outline: none;
        overflow: hidden;
        font: inherit;
        padding: 0 1px;
        margin: 0 -1px;
        contain: strict;
        -ms-user-select: text;
        -moz-user-select: text;
        -webkit-user-select: text;
        user-select: text;
        /* white-space: pre !important; */
        pointer-events: none;
    }
    tspan,
    text {
        -moz-user-select: none; /*火狐*/
        -webkit-user-select: none; /*webkit浏览器*/
        -ms-user-select: none; /*IE10*/
        user-select: none;
    }

    #input_display_win {
      position: fixed;
      z-index: 0;
      min-height: 16px;
    //   width: 100%;
    //   opacity: 0;
      background: white;
      display: inline-block;
      -moz-appearance: none;
      appearance: none;
      border: none;
      resize: none;
      outline: none;
      overflow: hidden;
      font: inherit;
      padding: 0 1px;
      margin: 0 -1px;
      -ms-user-select: none;
      -moz-user-select: none;
      -webkit-user-select: none;
      user-select: none;
      /* white-space: pre !important; */
      pointer-events:none;
    }
    // form input:invalid {
    //     border: 1px solid red;
    //     /* background-color: pink; */
    // }

    // form input:focus {
    //     outline: none;
    // }
    .image-detail-block .image-checkbox {
        font-size: 30px;
        margin-left: 2px;
    }

    .image-detail-block .text-disabled {
        opacity: 0.5;
    }
    .image-processor-nw {
        cursor: nw-resize;
    }

    .image-processor-ne {
        cursor: ne-resize;
    }

    .image-processor-sw {
        cursor: sw-resize;
    }

    .image-processor-se {
        cursor: se-resize;
    }

    .image-processor-n {
        cursor: n-resize;
    }

    .image-processor-s {
        cursor: s-resize;
    }

    .image-processor-e {
        cursor: e-resize;
    }

    .image-processor-w {
        cursor: w-resize;
    }

    image {
        cursor: all-scroll;
    }
    image.select-button {
        cursor: pointer;
    }
    image.select-button-red {
        filter: hue-rotate(140deg) saturate(45);
    }
    .selection {
        fill: white;
        opacity: 0;
    }
    .selection-selected {
        /* fill: cornflowerblue; */
        // fill: #009fff;
        // fill: #B4D7FF;
        fill: #ABC8F2;
        opacity: 1;
    }
    .newcontrol-focus {
        /* fill: cornflowerblue; */
        // fill: #009fff;
        fill: #CCEEFF;
        opacity: 1;
    }
    .newcontrol-cursor {
        fill: #93F2FF;
        opacity: 1;
    }

    .hide,
    .hidden {
        display: none;
    }

    ul,
    li,
    p {
        padding: 0;
        margin: 0;
        list-style: none;
    }
    .editor-page-content {
        position: relative;
        clear: both;
        overflow: hidden;

        & .header-footer-enabled {
            opacity: 0.5;
            pointer-events: none;
        }

        & .show {
            display: block;
        }

        & .opacity {
            opacity: 0.5;
            pointer-events: none;
        }

        & .hide {
            display: none;
        }
    }
    .editor-line {
        #less.line();
        #less.text();
        .w-5 {
            #less.inline-block();
            #less.w-5();
        }
        .w-10 {
            #less.inline-block();
            #less.w-10();
        }
        .w-020 {
            #less.inline-block();
            #less.w-020();
        }
        .w-20 {
            #less.inline-block();
            #less.w-20();
        }
        .w-40 {
            #less.inline-block();
            #less.w-40();
        }
        .w-050 {
            #less.inline-block();
            #less.w-050();
        }
        .w-70 {
            #less.inline-block();
            #less.w-70();
        }

        .w-90 {
            #less.inline-block();
            #less.w-90();
        }

        .w-120 {
            #less.inline-block();
            width: 120px;
            line-height: 1;
        }

        .right-auto-50 {
            #less.inline-block();
            #less.right-auto(40px);
        }

        .right-auto-90 {
            #less.inline-block();
            #less.right-auto(92px);
        }

        .right-auto-120 {
            #less.inline-block();
            #less.right-auto(122px);
        }

        .right-auto-210 {
            #less.inline-block();
            #less.right-auto(212px);
        }

        .right-auto {
            #less.inline-block();
            #less.right-auto(72px);
        }

        .title {
            #less.title();
        }

        &.editor-multi-line {
            height: auto;
            margin-bottom: 0;
            line-height: 1;

            .w-70 {
                vertical-align: top;
                line-height: 28px;
            }
        }

        .must-input {
            &::after {
                content: '*';
                color: #f00;
            }
        }

        .in-label {
            float: left;
            display: flex;
            align-items: center;
            & > :first-child {
                flex-grow: 0;
                padding-right: 3px;
                word-break: keep-all;
            }
            & > :last-child {
                flex-grow: 2;
                padding-right: 10px;
            }
        }
    }

    .datebox-wrapper {
        height: 100%;
        position: relative;
    }

    .noEvent {
        pointer-events: none;
    }

    .editor-default-view {
        .page-wrapper > .view-mode-span {
            position: absolute;
            z-index: 2;
            left: 0;
            bottom: -7px;
            height: 10px;
            width: 100%;
            content: ' ';
            cursor: ns-resize;
        }
        &.editor-other-view .page-wrapper[page-index='0'] > .view-mode-span {
            bottom: -20px;
        }
        .page-wrapper:last-child > .view-mode-span {
            display: none;
        }
        &.editor-web-view .page-wrapper {
            box-shadow: 0px 1px 8px 0px rgba(0, 0, 0, 0.16);
            &::after {
                height: 5px;
                margin-bottom: 5px;
                background: #fff;
                border-top: 1px solid #999;
            }
        }
    }

    .bpTimeCell-rect-warn {
        stroke: #FF3143;
        stroke-width: 1px;
    }

    .numberCell-valid-rect {
        fill: none;
        stroke: #FD8701;
        stroke-width: 1px;
    }
    // .editor-other-view {
    //     .page-wrapper::after {
    //         position: absolute;
    //         z-index: 2;
    //         left: 0;
    //         bottom: -10px;
    //         height: 20px;
    //         width: 100%;
    //         content: ' ';
    //         cursor: ns-resize;
    //     }
    //     .page-wrapper:last-child::after {
    //         display: none;
    //     }
    // }

    .newcontrol-prop .editor-line {
        margin-bottom: 0;
        &:last-child {
            margin-bottom: 8px;
        }
    }
    // &.hz-editor-from-iframe {
    //     .page-wrapper {
    //         padding-bottom: 20px;
    //         overflow: hidden;
    //         box-sizing: content-box;
    //     }
    //     div > .page-wrapper:last-child {
    //         padding-bottom: 0;
    //     }
    // }

    .ReactVirtualized__Grid {
        outline: none;
        margin: 0 auto;
        height: 100%;
        & > div {
            margin: 0 auto;
        }
        & > div:first-child {
            height: 100%!important;
        }
        // overflow: visible !important;

        .textarea-wrapper {
            left: 0;
            top: 0;
        }

        .page-wrapper {
            right: 0;
            margin: 0 auto;
            svg {
                max-width: 100%;
                max-height: 100%;
            }

            .newControl-fixed-tips {
                display: block;
                position: absolute;
                z-index: 9;
                min-height: 18px;
                min-width: 26px;
                padding: 0 4px;
                font-size: 6px;
                cursor: pointer;
                color: black;
                line-height: 16px;
                border-radius: 2px;
                // opacity: 0.9;
                // transform: scale(0.8);
                margin-top: -14px;
                margin-left: -3px;
                user-select: none;

                &:hover {
                    background-color: #E6F9FF;
                    border-color: #009FFF;
                }

                // &.active, &:hover {
                //     color: #171819;
                //     border: 1px solid #171819;
                // }
            }
        }

        .new-control-combox-drop-button,
        .new-control-drop-button {
            position: absolute;
            /* display: inline-block; */
            /* background-color: blue; */

            width: 10px;
            pointer-events: visible;
            z-index: 1;

            background: @activeColor;

            .drop-button-triangle {
                width: 0;
                height: 0;
                border-left: 4px solid transparent;
                border-right: 4px solid transparent;
                border-top: 8px solid #000;
                position: relative;
                top: 40%;
                left: 1px;
            }
        }

        //.region-border {
        //    line {
        //        stroke: #ACB4C1;
        //    }
        //    &.active {
        //        line {
        //            stroke: #171819;
        //        }
        //    }
        //}

    }

    .insert-image,
    .insert-file,
    .open-file,
    .apo-to-zip,
    .zip-to-apo,
    .open-old-file {
        position: fixed;
        z-index: -5;
        left: -300%;
        top: 0;
        opacity: 0;
    }

    .newcontrol-box-placeholder {
        position: absolute;
    }

    .copy-paste-iframe {
        position: absolute;
        left: -300%;
        top: -100%;
        z-index: 999;
        width: 0;
        height: 0;
    }

}

.region-btn {
  display: none;
  width: 20px;
  height: 20px;
  padding: 0;
  position: absolute;
  top: 0;
  left: 0;
  border: 1px dashed #ACB4C1;
  background-color: #fff;
  cursor: pointer;

  &.default {

    &:before, &:after {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      content: '';
      display: inline-block;
      background: #727B7F;
    }
  
    &:before {
      width: 14px;
      height: 1px;
    }
  
    &:after {
      width: 1px;
      height: 14px;
    }
  }

  .btn-tooltip {
    display: none;
    font-size: 14px;
    color: #fff;
    height: 30px;
    line-height: 30px;
    background: #54627B;
    border-radius: 2px;
    position: absolute;
    top: 100%;
    margin-top: 4px;
    left: 0;
    width: 100px;
    text-align: center;
  }

  &.actived {
    display: inline-block;
  }

  &:hover {
    background-color: #F1F1F2;
    .btn-tooltip {
      display: inline-block;
    }
  }

  &:focus {
    outline: none;
    background-color: #D4D7D8;
    .btn-tooltip {
      display: none;
    }
  }

}

// .printFrame-box {
//     position: fixed;
//     left: 800px;
//     top: 60px;
//     width: 900px;
//     z-index: 9999;
//     background: #fff;
// }
.printFrame-box {
    position: fixed;
    // width: 800px;
    // left: -100px;
    // top: 150px;
    // z-index: 9999;
    left: -300%;
    top: -100%;
    z-index: -1;
    width: 0;
    height: 0;
}

// body.hz-editor-body {
//     margin: 0;
//     padding: 0;
//     overflow: hidden;
// }