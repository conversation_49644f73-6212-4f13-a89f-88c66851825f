import { isValidName} from '../../../../common/commonDefines';
import { message } from '../../../../common/Message';
import { IFRAME_MANAGER } from '../../../../common/IframeManager';
class NewControlManageExt {
    private documentCore: any;
    private property: any;
    private callback: () => void;
    constructor() {
        //  TODO:
    }

    public setNewControlProps(documentCore: any, property: any): void {
        this.documentCore = documentCore;
        this.property = property;
        IFRAME_MANAGER.setDocId(documentCore.getCurrentId());
    }

    public setNewControlProps2(property: any): void {
        this.property = property;
    }

    public setRefresh(callback: () => void): void {
        this.callback = callback;
    }

    public isValidData(name: string, value: any): boolean {
        switch (name) {
            case 'newControlName': {
                const res = isValidName(value);
                if (!res) {
                    message.error('名称不符合规范，请重新输入');
                    return false;
                }

                const property = this.property;
                if (property[name] !== value && !this.documentCore.checkNewControlName(value)) {
                    message.error('该名称不符合，请重新填写');
                    return false;
                }
                property[name] = value;
                return true;
            }
        }

        return true;
    }

    public isRefresh(name: string): boolean {
        switch (name) {
            case 'showTitle':
            case 'isNewControlHidden':
            case 'isNewControlHiddenBackground':
            case 'prefixContent':
            case 'selectPrefixContent':
            case 'separator':
            case 'precision':
            case 'showRight':
            case 'newControlDisplayType':
            case 'isNewControlShowBorder':
            case 'newControlTitle': {
                return true;
            }
        }

        return false;
    }

    public confirm(name: string, value: any): any {
        const oldName = this.property.newControlName;
        if (!NewControlManageInfo.isValidData(name, value)) {
            return;
        }
        const obj = {};
        obj[name] = value;
        const res = this.documentCore.setNewControlProperty(obj, oldName);
        if (res) {
            // if (NewControlManageInfo.isRefresh(name)) {
            //     if (typeof this.callback === 'function') {
            //         this.callback();
            //     }
            // }
        }
        if (typeof this.callback === 'function') {
            this.callback();
        }
    }
}

// tslint:disable-next-line: sy-global-const-name
export const NewControlManageInfo = new NewControlManageExt();
