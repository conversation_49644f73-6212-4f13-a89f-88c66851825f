import * as <PERSON><PERSON><PERSON><PERSON> from './jszip';
// tslint:disable-next-line: max-line-length

import { ResultType, IModeFonts, rtNode, IXmlInfoStruct, STD_START_DEFAULT, IXmlInfoRegion, EXTERNAL_OUTER_STRUCT_TYPE, REGION_PROPS_DEFAULT, IXmlInfoRegionContainer, IXmlInfoSection, DataType, NewControlType, IRecordContent, FILE_HEADER_VERSION, DocumentSectionType, EXTERNAL_DOCUMENTSECTION_TYPE, DEFAULT_TEXT_PROPERTY, ViewModeType, FILE_HEADER_ENCODE_VERSION, FILE_HEADER_VERSION_GRACEFUL_DEGRADATION, IDocumentBuffer, InsertFilePositionType, FILE_HEADER_VERSION2, IReadDocOption, ErrorMessages } from './commonDefines';
import { Reader } from '../format/reader/reader';
// import * as txml from 'txml';
import { tXmlCustomParse } from './txml/tXml';
import { getDocumentCoreRecorder } from '../model/DocumentCoreRecordReplay';

import { unescapeXML } from '@hz-editor/utils';
import Document from '../model/core/Document';
import TextProperty from '../model/core/TextProperty';
import { IOpenDocPars } from './menu/ToolbarAction2';
// import { getZstdCodec } from '@sy-editor/plugins';
import JsBarcode from 'jsbarcode';
import { message } from './Message';
import QRCode from 'qrcode';

export interface IOpenFileAPIPars {
  bNoEndPara: boolean;
  bRecalc: boolean;
  options: IReadDocOption;
  modeFonts: IModeFonts;
  bInsertFile?: boolean;
  type?: InsertFilePositionType;  // 插入文件时的插入位置类型
}

export interface IParseXmlPars extends IOpenDocPars, IOpenFileAPIPars {
}

export function analyzeDocVersion(params: IOpenDocPars, openFileParams?: IOpenFileAPIPars): Promise<number> {
    // const fileReader = new FileReader();

    return new Promise((resolve) => {
      const {rawBuffer, reader, bToolbarOpenFile} = params;
      const {headerBuffer, contentBuffer}: IDocumentBuffer = reader.separateRawBuffer(new Uint8Array(rawBuffer));
      const {versionNum, documentNum} = reader.getHeaderBufferItems(headerBuffer);

      const document = reader.getDocument();
      const props = {...openFileParams, ...params, documentVersion: versionNum};

      if (!openFileParams) {
        openFileParams = {
          bNoEndPara: false,
          bRecalc: true,
          options: null,
          modeFonts: null,
        };
      }

      if (!openFileParams.bInsertFile) {
        document.setLoadFile(true);
      }

      switch (versionNum) {
        case FILE_HEADER_VERSION:
        case FILE_HEADER_VERSION2:
        case FILE_HEADER_ENCODE_VERSION: {
          if (bToolbarOpenFile && FILE_HEADER_ENCODE_VERSION === versionNum) {
            // console.log(versionNum);
            resolve(ResultType.NeedDebug);
            document.setLoadFile(false);
            break;
          }

          if (document) {
            if ((!openFileParams.bInsertFile && !props.bInsertFile) && documentNum != null) {
              document.setFileVersion(documentNum);
            }

            document.setDocumentVersion(versionNum);
          }

          parseDocXml(contentBuffer, reader, props)
          .then((res) => {
            resolve(res);
            document.setLoadFile(false);
          })
          .catch((error) => {
            document.setLoadFile(false);
            // tslint:disable-next-line: no-console
            // console.log(error, documentNum);
            resolve(ResultType.NeedDebug);
          });
          break;
        }

        case FILE_HEADER_VERSION_GRACEFUL_DEGRADATION: {

          if (document && documentNum != null) {
            document.setFileVersion(documentNum);
            document.setDocumentVersion(versionNum);
          }

          parseOldDocXml(null, contentBuffer, reader, props)
          .then((res) => {
            resolve(res);
            document.setLoadFile(false);
          })
          .catch((error) => {
            document.setLoadFile(false);
            // tslint:disable-next-line: no-console
            // console.log(error, documentNum);
            resolve(ResultType.NeedDebug);
          });

          break;
        }

        default: {

          readerFileEditorVersionError(document);
          resolve(ResultType.NeedDebug);
          // getZstdCodec().then((zstdCodec) => {
          //   return zstdCodec.run( (zstd) => {
          //     if (documentNum != null && versionNum != null) {
          //       document.setFileVersion(documentNum);
          //       document.setDocumentVersion(versionNum);
          //     }
          //     parseOldDocXml(zstd, contentBuffer, reader, props)
          //     .then((res) => {
          //       resolve(res);
          //       document.setLoadFile(false);
          //     })
          //     .catch((error) => {
          //       document.setLoadFile(false);
          //       // tslint:disable-next-line: no-console
          //       // console.log(error, documentNum);
          //       resolve(ResultType.NeedDebug);
          //     });
          //   }); // zstdCodec.run
          // })
          // .catch((error) => {
          //   document.setLoadFile(false);
          //   // tslint:disable-next-line: no-console
          //   // console.log(versionNum, documentNum);
          //   resolve(ResultType.NeedDebug);
          // });

          break;
        }
      }

      // document.setLoadFile(false);
    });
}

export function parseDocXml(contentBuffer: Uint8Array, reader: Reader, props: IParseXmlPars): Promise<number> {

  const myDate = performance.now();
  return new Promise((resolve) => {
    const { bInsertFile, bNoEndPara, bRecalc, options, type, documentVersion, modeFonts } = props;
    // let newBlob = null;
    let date = new Date();
    // try {
    //     newBlob = new Blob([contentBuffer], { type: 'application/apollo-zip' });
    // } catch (error) {
    //   // tslint:disable-next-line: no-console
    //   // console.log(error);
    //   // return;
    //   resolve(ResultType.NeedDebug);
    // }

    // if (options != null) {
    //   options.time += new Date().getTime() - date.getTime();
    // }


    const newZip = new JSZip();
    // unzip the zipped blob
    // tslint:disable-next-line: newline-per-chained-call

    newZip.loadAsync(contentBuffer).then((zip) => {
      date = new Date();
      let documentZippedFile = null;
      let stylesZippedFile = null;
      let settingsZippedFile = null;
      let mediaZippedFile = null;
      let headerZippedFile = null;
      let footerZippedFile = null;
      // let cascadeZippedFile = null;
      let nisTableFile = null;

      newZip.forEach((relativePath: string, file: any): void => {
        // console.log(relativePath);
        // console.log(file);
        switch (relativePath) {
          case 'Document.xml': {
            documentZippedFile = file;
            break;
          }
          case 'Styles.xml': {
            stylesZippedFile = file;
            break;
          }
          case 'Header1.xml': {
            headerZippedFile = file;
            break;
          }
          case 'Footer.xml': {
            footerZippedFile = file;
            break;
          }
          case 'Settings.xml': {
            settingsZippedFile = file;
            break;
          }
          case 'Media.xml': {
            mediaZippedFile = file;
            break;
          }
          case 'Cascade.xml': {
            // cascadeZippedFile = file;
            break;
          }
          case 'Editor-html.html': {
            //
            break;
          }
          case 'SourceBind.json': {
            //
            break;
          }
          case 'NISTable.json': {
            nisTableFile = file;
            break;
          }
          default: {
            // tslint:disable-next-line: no-console
            console.warn('unexpected xml file detected.');
            break;
          }
        }
      });

      // critical xmls
      try {
        if (!mediaZippedFile || !documentZippedFile) {
          throw new Error('Critical xml file missing');
        }
      } catch (error) {
        // tslint:disable-next-line: no-console
        console.log(error);
        // return ;
        resolve(ResultType.NeedDebug);
      }

      // minor xmls
      try {
        if (!stylesZippedFile || !settingsZippedFile) {
          throw new Error('Minor xml file missing');
        }
      } catch (error) {
        // tslint:disable-next-line: no-console
        console.log(error);
        // continue
      }

      if (options != null) {
        options.time += new Date().getTime() - date.getTime();
      }

      const recordContent: IRecordContent = {
          document: null,
          style: null,
          settings: null,
          media: null,
          header: null,
          footer: null,
          cascade: null,
          documentVersion,
          insertType: type,
      };

      // get all xmls content
      const mediaPromise = mediaZippedFile.async('string');
      const stylePromise = stylesZippedFile.async('string');
      const documentPromise = documentZippedFile.async('string');
      const settingsPromise = settingsZippedFile.async('string');
      const headerPromise = headerZippedFile != null ? headerZippedFile.async('string') : null;
      const footerPromise = footerZippedFile != null ? footerZippedFile.async('string') : null;
      const nisTablePromise = nisTableFile ? nisTableFile.async('string') : null;
      // const cascadePromise = cascadeZippedFile != null ? cascadeZippedFile.async('string') : null;
      const fPromises = [mediaPromise, stylePromise, nisTablePromise, documentPromise, settingsPromise,
                        headerPromise, footerPromise];

      // tslint:disable-next-line: newline-per-chained-call
      Promise.all(fPromises).then((values) => {
        if (options && options.closeObj && options.closeObj.bClose) {
          return resolve(null);
        }

        // Returned values will be in order of the Promises passed, regardless of completion order.
        // console.log(values);
        const [mediaFile, stylesFile, nisTableJsonFile, documentFile, settingsFile, headerFile, footerFile] = values;

        /** read media.xml first */
        date = new Date();
        recordContent.media = mediaFile;
        if (!reader.readMedia(mediaFile) && !bInsertFile) { // fail safe
          // TODO: create new document?
          // this.props.documentCore.createNewDocument();
          // tslint:disable-next-line: no-console
          // console.log('readMedia');
          resolve(ResultType.NeedDebug);
        }
        if (options != null) {
          options.time += new Date().getTime() - date.getTime();
        }

        /** read styles.xml */
        date = new Date();
        recordContent.style = stylesFile;
        if (!bInsertFile && !reader.readStyles(stylesFile as string, bInsertFile)) { // fail safe
          // this.documentCore.createNewDocument();
        }

        /** then read document.xml */
        date = new Date();
        recordContent.document = documentFile;
        // if headerfooter exists, still need recalc!
        let bRecalcTrue = bRecalc;
        if (headerZippedFile != null && !bInsertFile) {
          bRecalcTrue = true;
        }

        if (nisTableJsonFile && options?.nisTableJson &&
            compareNISJsonEqual(JSON.parse(nisTableJsonFile), options?.nisTableJson)) {
          options.nisTableJson = null;
          console.log(nisTableJsonFile, options.nisTableJson);
        }

        // prepare default fonts
        const curModeFonts = reader.getCurModeFonts(modeFonts);

        if (!reader.read2(documentFile as string, bInsertFile, bNoEndPara, bRecalcTrue, curModeFonts, options,
                          type, documentVersion)
          && !bInsertFile) {
          // fail safe
          // this.props.documentCore.createNewDocument();
          // tslint:disable-next-line: no-console
          // console.log('read');
          resolve(ResultType.NeedDebug);
        }
        if (options != null) {
          options.time += new Date().getTime() - date.getTime();
        }

        /** read settings.xml */
        date = new Date();
        recordContent.settings = settingsFile;
        const setMap = new Map();
        reader.readSettings(settingsFile as string, bInsertFile, setMap);

        if (!bInsertFile && setMap.size > 0) {
          reader['document'].setAllFileProperties(setMap);
        }

        // read cascade
        date = new Date();

        //// deal with headerfooter ////
        if (headerZippedFile != null) {
          // when insert file, inserted file's headerfooter should be deserted
          if (bInsertFile === true) {
            reader.readAllCallback();
            if (options != null) {
              options.time += new Date().getTime() - date.getTime();
            }

            fileStartRecord(recordContent, bInsertFile);
            resolve(ResultType.Success);

          } else {
            if (options != null) {
              options.time += new Date().getTime() - date.getTime();
            }

            // read header
            date = new Date();
            recordContent.header = headerFile;
            if (!reader.readHeader2(headerFile as string) && !bInsertFile) { // fail safe
              // this.documentCore.createNewDocument();
            }
            if (options != null) {
              options.time += new Date().getTime() - date.getTime();
            }

            // read footer
            date = new Date();
            recordContent.footer = footerFile;
            fileStartRecord(recordContent, bInsertFile);
            if (!reader.readFooter2(footerFile as string) && !bInsertFile) { // fail safe
              // this.documentCore.createNewDocument();
            }

            const res = reader.recalculate();
            reader.readAllCallback();
            if (options != null) {
              options.recalcPromise = res;
              options.time += new Date().getTime() - date.getTime();
            }

            // all done
            resolve(ResultType.Success);

          }
        } else { // headerZippedFile null
          const res = reader.recalculate();
          reader.readAllCallback();
          if (options != null) {
            options.recalcPromise = res;
            options.time += new Date().getTime() - date.getTime();
          }
          fileStartRecord(recordContent, bInsertFile);
          resolve(ResultType.Success);
        }
        //// deal with headerfooter end ////

      // tslint:disable-next-line: newline-per-chained-call
      }).catch((error) => {
        // tslint:disable-next-line: no-console
        console.log(error);
        // return;
        resolve(ResultType.NeedDebug);
      });
    }) // newzip loadasync
      .catch((error) => {
        // tslint:disable-next-line: no-console
        console.log(error);
        // return;
        resolve(ResultType.NeedDebug);
      });

  });
}


export function parseOldDocXml(zstd: any = null, contentBuffer: Uint8Array,
                               reader: Reader, props: any): Promise<number> {
  const {bInsertFile, bNoEndPara, bRecalc, options, type, documentVersion} = props;
  // console.log(zstd, options, contentBuffer)
  let date = new Date();
  const newZip = new JSZip();
  // const reader = this;

  // reset DEFAULT_FONT
  // resetDefaultFont();

  return new Promise((resolve) => {
    let newBlob = null;
    try {
      // if (menuMode === true) {
      //   // tslint:disable-next-line: no-console
      //   // console.time('decompression');
      // }
      if (zstd != null) {
        const streaming = new zstd.Streaming();
        const deCompressed = streaming.decompress(contentBuffer);
        // if (menuMode === true) {
        //   // tslint:disable-next-line: no-console
        //   // console.timeEnd('decompression');
        // }
        // After buffer separation, type can be original. No need version number
        newBlob = new Blob([deCompressed.buffer], {type: 'application/apollo-zip'});
      } else {
        // After buffer separation, type can be original. No need version number
        newBlob = new Blob([contentBuffer], {type: 'application/apollo-zip'});
      }

    } catch (error) {
      // tslint:disable-next-line: no-console
      // if (menuMode === true) {
      //   // tslint:disable-next-line: no-console
      //   // console.timeEnd('read time');
      // }
      // tslint:disable-next-line: no-console
      console.log(error);
      // return;
      resolve(ResultType.NeedDebug);
    }
    if (options != null) {
      options.time += new Date().getTime() - date.getTime();
    }
    // unzip the zipped blob
    // tslint:disable-next-line: newline-per-chained-call
    newZip.loadAsync(newBlob).then((zip) => {
      date = new Date();
      let documentZippedFile = null;
      let stylesZippedFile = null;
      let settingsZippedFile = null;
      let mediaZippedFile = null;
      let headerZippedFile = null;
      let footerZippedFile = null;
      // let cascadeZippedFile = null;

      // try {
      //   cascadeZippedFile = newZip.file('Cascade.xml');
      //   if (!cascadeZippedFile) {
      //     throw new Error('Cascade.xml missing');
      //   }
      // } catch (error) {
      //   // tslint:disable-next-line: no-console
      //   console.log(error);
      //   // return ;
      // }

      try {
        mediaZippedFile = newZip.file('Media.xml');
        if (!mediaZippedFile) {
          throw new Error('Media.xml missing');
        }
      } catch (error) {
        // tslint:disable-next-line: no-console
        console.log(error);
        // return ;
        resolve(ResultType.NeedDebug);
      }

      try {
        documentZippedFile = newZip.file('Document.xml');
        if (!documentZippedFile) {
          throw new Error('Document.xml missing');
        }
      } catch (error) {
        // tslint:disable-next-line: no-console
        console.log(error);
        // return ;
        resolve(ResultType.NeedDebug);
      }

      try {
        stylesZippedFile = newZip.file('Styles.xml');
        if (!stylesZippedFile) {
          throw new Error('Styles.xml missing');
        }
      } catch (error) {
        // tslint:disable-next-line: no-console
        console.log(error);
        // continue
      }

      try {
        settingsZippedFile = newZip.file('Settings.xml');
        if (!settingsZippedFile) {
          throw new Error('Settings.xml missing');
        }
      } catch (error) {
        // tslint:disable-next-line: no-console
        console.log(error);
        // continue
      }

      try {
        headerZippedFile = newZip.file('Header1.xml');
        if (!headerZippedFile) {
          // throw new Error('Header1.xml missing');
        }
      } catch (error) {
        // tslint:disable-next-line: no-console
        console.log(error);

        // continue/return?
      }
      // console.log(headerZippedFile);

      try {
        footerZippedFile = newZip.file('Footer.xml');
        if (!footerZippedFile) {
          // throw new Error('Footer.xml missing');
        }
      } catch (error) {
        // tslint:disable-next-line: no-console
        console.log(error);

        // continue/return?
      }
      if (options != null) {
        options.time += new Date().getTime() - date.getTime();
      }

      const recordContent: IRecordContent = {
        document: null,
        style: null,
        settings: null,
        media: null,
        header: null,
        footer: null,
        cascade: null,
        documentVersion,
        insertType: type,
      };

      /** read media.xml first */
      // tslint:disable-next-line: newline-per-chained-call
      mediaZippedFile.async('blob').then((unZippedMediaFile: any) => {
        const fileReader = new FileReader();
        // tslint:disable-next-line: newline-per-chained-call
        fileReader.readAsText(unZippedMediaFile);
        fileReader.onloadend = () => {
          date = new Date();
          const mediaFile: string = fileReader.result as string;
          recordContent.media = mediaFile;
          if (!reader.readMedia(mediaFile) && !bInsertFile) { // fail safe
            // TODO: create new document?
            // this.props.documentCore.createNewDocument();
            // tslint:disable-next-line: no-console
            // console.log('readMedia');
            resolve(ResultType.NeedDebug);
          }
          if (options != null) {
            options.time += new Date().getTime() - date.getTime();
          }
          /** then read document.xml */
          // tslint:disable-next-line: newline-per-chained-call
          documentZippedFile.async('blob').then((unZippedDocumentFile: any) => {
            fileReader.readAsText(unZippedDocumentFile);
            fileReader.onloadend = () => {
              date = new Date();
              const documentFile: string = fileReader.result as string;
              // console.log(documentFile) // plain txt of .xml file
              recordContent.document = documentFile;
              // if headerfooter exists, still need recalc!
              let bRecalcTrue = bRecalc;
              if (headerZippedFile != null) {
                bRecalcTrue = true;
              }
              if (!reader.read(documentFile as string, bInsertFile, bNoEndPara, bRecalcTrue, options,
                                type, documentVersion)
                  && !bInsertFile) {
                // fail safe
                // this.props.documentCore.createNewDocument();
                // tslint:disable-next-line: no-console
                // console.log('read');
                resolve(ResultType.NeedDebug);
              }
              if (options != null) {
                options.time += new Date().getTime() - date.getTime();
              }
              // rerender
              // this.props.testDocumentXml(bInsertFile); // cursor is at 1st para's start
              // this.props.refresh();

              /** read settings.xml */
              // tslint:disable-next-line: newline-per-chained-call
              settingsZippedFile.async('blob').then((unZippedSettingsFile: any) => {
                fileReader.readAsText(unZippedSettingsFile);
                fileReader.onloadend = () => {
                  date = new Date();
                  const settingsFile: string = fileReader.result as string;
                  recordContent.settings = settingsFile;
                  const setMap = new Map();
                  if (!reader.readSettings(settingsFile as string, bInsertFile, setMap) && !bInsertFile) { // fail safe
                    // this.documentCore.createNewDocument();
                  }

                  /** read styles.xml */
                  // tslint:disable-next-line: newline-per-chained-call
                  stylesZippedFile.async('blob').then((unZippedStylesFile: any) => {
                    fileReader.readAsText(unZippedStylesFile);
                    fileReader.onloadend = () => {
                      date = new Date();
                      const stylesFile: string = fileReader.result as string;
                      recordContent.style = stylesFile;
                      if (!reader.readStyles(stylesFile as string, bInsertFile) && !bInsertFile) { // fail safe
                        // this.documentCore.createNewDocument();
                      }

                      //// deal with headerfooter ////
                      if (headerZippedFile != null) {

                        // when insert file, inserted file's headerfooter should be deserted
                        if (bInsertFile === true) {
                          if (options != null) {
                            options.time += new Date().getTime() - date.getTime();
                          }
                          fileStartRecord(recordContent, bInsertFile);
                          resolve(ResultType.Success);

                        } else {
                          if (options != null) {
                            options.time += new Date().getTime() - date.getTime();
                          }
                          // tslint:disable-next-line: newline-per-chained-call
                          headerZippedFile.async('blob').then((unZippedHeaderFile: any) => {
                            fileReader.readAsText(unZippedHeaderFile);
                            fileReader.onloadend = () => {
                              date = new Date();
                              const headerFile: string = fileReader.result as string;
                              // console.log(headerFile)
                              recordContent.header = headerFile;
                              if (!reader.readHeader(headerFile as string) && !bInsertFile) { // fail safe
                                // this.documentCore.createNewDocument();
                              }
                              if (options != null) {
                                options.time += new Date().getTime() - date.getTime();
                              }
                              // if header.xml exists, footer.xml exists as well
                              // tslint:disable-next-line: newline-per-chained-call
                              footerZippedFile.async('blob').then((unZippedFooterFile: any) => {
                                fileReader.readAsText(unZippedFooterFile);
                                fileReader.onloadend = () => {
                                  date = new Date();
                                  const footerFile: string = fileReader.result as string;
                                  // console.log(footerFile)
                                  recordContent.footer = footerFile;
                                  fileStartRecord(recordContent, bInsertFile);
                                  if (!reader.readFooter(footerFile as string) && !bInsertFile) { // fail safe
                                    // this.documentCore.createNewDocument();
                                  }

                                  if (options != null) {
                                    options.time += new Date().getTime() - date.getTime();
                                  }
                                  // if (menuMode === true) {
                                  //   // tslint:disable-next-line: no-console
                                  //   // console.timeEnd('read time');
                                  // }

                                  resolve(ResultType.Success);

                                };
                              });

                            };
                          });
                        }

                      } else { // headerZippedFile null

                        if (options != null) {
                          options.time += new Date().getTime() - date.getTime();
                        }
                        // if (menuMode === true) {
                        //   // tslint:disable-next-line: no-console
                        //   // console.timeEnd('read time');
                        // }
                        // if (cascadeZippedFile !== null) {
                        //     cascadeZippedFile.async('blob').then((unZippedCascadeFile: any) => {
                        //             fileReader.readAsText(unZippedCascadeFile);
                        //             fileReader.onloadend = () => {
                        //                 date = new Date();
                        //                 const cascadeFile: string = fileReader.result as string;
                        //                 recordContent.style = cascadeFile;
                        //                 // tslint:disable-next-line: max-line-length
                        //                 if (!reader.readCascade(stylesFile as string) && !bInsertFile) { // fail safe
                        //                     // this.documentCore.createNewDocument();
                        //                 }
                        //                 resolve(ResultType.Success);
                        //             };
                        //     });
                        // } // read cascade
                        fileStartRecord(recordContent, bInsertFile);
                        resolve(ResultType.Success);
                      }
                      //// deal with headerfooter end ////

                    };
                  }); // read styles

                };
              }); // read settings

            };
          }); // read document
        };
      }); // read media
    }) // newzip loadasync
    .catch((error) => {
      // tslint:disable-next-line: no-console
      console.log(error);
      // return;
      resolve(ResultType.NeedDebug);
    });
  });
}

export function analyzeContentBuffer(zstd: any = null, contentBuffer: Uint8Array,
                                     fileReader: FileReader, reader: Reader,
                                     props: any, menuMode: boolean = false): Promise<number> {
  const {bInsertFile, bNoEndPara, bRecalc, options, type, documentVersion} = props;
  // console.log(zstd, options, contentBuffer)
  let date = new Date();
  const newZip = new JSZip();
  // const reader = this;

  // reset DEFAULT_FONT
  // resetDefaultFont();

  return new Promise((resolve) => {
    let newBlob = null;
    try {
      if (menuMode === true) {
        // tslint:disable-next-line: no-console
        // console.time('decompression');
      }
      if (zstd != null) {
        const streaming = new zstd.Streaming();
        const deCompressed = streaming.decompress(contentBuffer);
        if (menuMode === true) {
          // tslint:disable-next-line: no-console
          // console.timeEnd('decompression');
        }
        // After buffer separation, type can be original. No need version number
        newBlob = new Blob([deCompressed.buffer], {type: 'application/apollo-zip'});
      } else {
        // After buffer separation, type can be original. No need version number
        newBlob = new Blob([contentBuffer], {type: 'application/apollo-zip'});
      }

    } catch (error) {
      // tslint:disable-next-line: no-console
      if (menuMode === true) {
        // tslint:disable-next-line: no-console
        // console.timeEnd('read time');
      }
      // tslint:disable-next-line: no-console
      // console.log(error);
      // return;
      resolve(ResultType.NeedDebug);
    }
    if (options != null) {
      options.time += new Date().getTime() - date.getTime();
    }
    // unzip the zipped blob
    // tslint:disable-next-line: newline-per-chained-call
    newZip.loadAsync(newBlob).then((zip) => {
      date = new Date();
      let documentZippedFile = null;
      let stylesZippedFile = null;
      let settingsZippedFile = null;
      let mediaZippedFile = null;
      let headerZippedFile = null;
      let footerZippedFile = null;
      let cascadeZippedFile = null;

      try {
        cascadeZippedFile = newZip.file('Cascade.xml');
        if (!cascadeZippedFile) {
          throw new Error('Cascade.xml missing');
        }
      } catch (error) {
        // tslint:disable-next-line: no-console
        console.log(error);
        // return ;
      }

      try {
        mediaZippedFile = newZip.file('Media.xml');
        if (!mediaZippedFile) {
          throw new Error('Media.xml missing');
        }
      } catch (error) {
        // tslint:disable-next-line: no-console
        console.log(error);
        // return ;
        resolve(ResultType.NeedDebug);
      }

      try {
        documentZippedFile = newZip.file('Document.xml');
        if (!documentZippedFile) {
          throw new Error('Document.xml missing');
        }
      } catch (error) {
        // tslint:disable-next-line: no-console
        console.log(error);
        // return ;
        resolve(ResultType.NeedDebug);
      }

      try {
        stylesZippedFile = newZip.file('Styles.xml');
        if (!stylesZippedFile) {
          throw new Error('Styles.xml missing');
        }
      } catch (error) {
        // tslint:disable-next-line: no-console
        console.log(error);
        // continue
      }

      try {
        settingsZippedFile = newZip.file('Settings.xml');
        if (!settingsZippedFile) {
          throw new Error('Settings.xml missing');
        }
      } catch (error) {
        // tslint:disable-next-line: no-console
        console.log(error);
        // continue
      }

      try {
        headerZippedFile = newZip.file('Header1.xml');
        if (!headerZippedFile) {
          // throw new Error('Header1.xml missing');
        }
      } catch (error) {
        // tslint:disable-next-line: no-console
        console.log(error);

        // continue/return?
      }
      // console.log(headerZippedFile);

      try {
        footerZippedFile = newZip.file('Footer.xml');
        if (!footerZippedFile) {
          // throw new Error('Footer.xml missing');
        }
      } catch (error) {
        // tslint:disable-next-line: no-console
        console.log(error);

        // continue/return?
      }
      if (options != null) {
        options.time += new Date().getTime() - date.getTime();
      }

      const recordContent: IRecordContent = {
        document: null,
        style: null,
        settings: null,
        media: null,
        header: null,
        footer: null,
        cascade: null,
        documentVersion,
        insertType: type,
      };

      /** read media.xml first */
      // tslint:disable-next-line: newline-per-chained-call
      mediaZippedFile.async('blob').then((unZippedMediaFile: any) => {
        // tslint:disable-next-line: newline-per-chained-call
        fileReader.readAsText(unZippedMediaFile);
        fileReader.onloadend = () => {
          date = new Date();
          const mediaFile: string = fileReader.result as string;
          recordContent.media = mediaFile;
          if (!reader.readMedia(mediaFile) && !bInsertFile) { // fail safe
            // TODO: create new document?
            // this.props.documentCore.createNewDocument();
            // tslint:disable-next-line: no-console
            console.log('readMedia');
            resolve(ResultType.NeedDebug);
          }
          if (options != null) {
            options.time += new Date().getTime() - date.getTime();
          }
          /** then read document.xml */
          // tslint:disable-next-line: newline-per-chained-call
          documentZippedFile.async('blob').then((unZippedDocumentFile: any) => {
            fileReader.readAsText(unZippedDocumentFile);
            fileReader.onloadend = () => {
              date = new Date();
              const documentFile: string = fileReader.result as string;
              // console.log(documentFile) // plain txt of .xml file
              recordContent.document = documentFile;
              // if headerfooter exists, still need recalc!
              let bRecalcTrue = bRecalc;
              if (headerZippedFile != null) {
                bRecalcTrue = true;
              }
              if (!reader.read(documentFile as string, bInsertFile, bNoEndPara, bRecalcTrue, options,
                                type, documentVersion)
                  && !bInsertFile) {
                // fail safe
                // this.props.documentCore.createNewDocument();
                // tslint:disable-next-line: no-console
                console.log('read');
                resolve(ResultType.NeedDebug);
              }
              if (options != null) {
                options.time += new Date().getTime() - date.getTime();
              }
              // rerender
              // this.props.testDocumentXml(bInsertFile); // cursor is at 1st para's start
              // this.props.refresh();

              /** read settings.xml */
              // tslint:disable-next-line: newline-per-chained-call
              settingsZippedFile.async('blob').then((unZippedSettingsFile: any) => {
                fileReader.readAsText(unZippedSettingsFile);
                fileReader.onloadend = () => {
                  date = new Date();
                  const settingsFile: string = fileReader.result as string;
                  recordContent.settings = settingsFile;
                  const setMap = new Map();
                  if (!reader.readSettings(settingsFile as string, bInsertFile, setMap) && !bInsertFile) { // fail safe
                    // this.documentCore.createNewDocument();
                  }

                  /** read styles.xml */
                  // tslint:disable-next-line: newline-per-chained-call
                  stylesZippedFile.async('blob').then((unZippedStylesFile: any) => {
                    fileReader.readAsText(unZippedStylesFile);
                    fileReader.onloadend = () => {
                      date = new Date();
                      const stylesFile: string = fileReader.result as string;
                      recordContent.style = stylesFile;
                      if (!reader.readStyles(stylesFile as string, bInsertFile) && !bInsertFile) { // fail safe
                        // this.documentCore.createNewDocument();
                      }

                      //// deal with headerfooter ////
                      if (headerZippedFile != null) {

                        // when insert file, inserted file's headerfooter should be deserted
                        if (bInsertFile === true) {
                          if (options != null) {
                            options.time += new Date().getTime() - date.getTime();
                          }
                          fileStartRecord(recordContent, bInsertFile);
                          resolve(ResultType.Success);

                        } else {
                          if (options != null) {
                            options.time += new Date().getTime() - date.getTime();
                          }
                          // tslint:disable-next-line: newline-per-chained-call
                          headerZippedFile.async('blob').then((unZippedHeaderFile: any) => {
                            fileReader.readAsText(unZippedHeaderFile);
                            fileReader.onloadend = () => {
                              date = new Date();
                              const headerFile: string = fileReader.result as string;
                              // console.log(headerFile)
                              recordContent.header = headerFile;
                              if (!reader.readHeader(headerFile as string) && !bInsertFile) { // fail safe
                                // this.documentCore.createNewDocument();
                              }
                              if (options != null) {
                                options.time += new Date().getTime() - date.getTime();
                              }
                              // if header.xml exists, footer.xml exists as well
                              // tslint:disable-next-line: newline-per-chained-call
                              footerZippedFile.async('blob').then((unZippedFooterFile: any) => {
                                fileReader.readAsText(unZippedFooterFile);
                                fileReader.onloadend = () => {
                                  date = new Date();
                                  const footerFile: string = fileReader.result as string;
                                  // console.log(footerFile)
                                  recordContent.footer = footerFile;
                                  fileStartRecord(recordContent, bInsertFile);
                                  if (!reader.readFooter(footerFile as string) && !bInsertFile) { // fail safe
                                    // this.documentCore.createNewDocument();
                                  }

                                  if (options != null) {
                                    options.time += new Date().getTime() - date.getTime();
                                  }
                                  if (menuMode === true) {
                                    // tslint:disable-next-line: no-console
                                    // console.timeEnd('read time');
                                  }

                                  // if (cascadeZippedFile !== null) {
                                  //     cascadeZippedFile.async('blob').then((unZippedCascadeFile: any) => {
                                  //             fileReader.readAsText(unZippedCascadeFile);
                                  //             fileReader.onloadend = () => {
                                  //                 date = new Date();
                                  //                 const cascadeFile: string = fileReader.result as string;
                                  //                 recordContent.cascade = cascadeFile;
                                  //                 // tslint:disable-next-line: max-line-length
                                  //                 if (!reader.readCascade(stylesFile as string) && !bInsertFile) { // fail safe
                                  //                     // this.documentCore.createNewDocument();
                                  //                 }
                                  //                 resolve(ResultType.Success);
                                  //             };
                                  //         });
                                  // } // read cascade

                                  resolve(ResultType.Success);

                                };
                              });

                            };
                          });
                        }

                      } else { // headerZippedFile null

                        if (options != null) {
                          options.time += new Date().getTime() - date.getTime();
                        }
                        if (menuMode === true) {
                          // tslint:disable-next-line: no-console
                          // console.timeEnd('read time');
                        }
                        // if (cascadeZippedFile !== null) {
                        //     cascadeZippedFile.async('blob').then((unZippedCascadeFile: any) => {
                        //             fileReader.readAsText(unZippedCascadeFile);
                        //             fileReader.onloadend = () => {
                        //                 date = new Date();
                        //                 const cascadeFile: string = fileReader.result as string;
                        //                 recordContent.style = cascadeFile;
                        //                 // tslint:disable-next-line: max-line-length
                        //                 if (!reader.readCascade(stylesFile as string) && !bInsertFile) { // fail safe
                        //                     // this.documentCore.createNewDocument();
                        //                 }
                        //                 resolve(ResultType.Success);
                        //             };
                        //     });
                        // } // read cascade
                        fileStartRecord(recordContent, bInsertFile);
                        resolve(ResultType.Success);
                      }
                      //// deal with headerfooter end ////

                    };
                  }); // read styles

                };
              }); // read settings

            };
          }); // read document
        };
      }); // read media
    }) // newzip loadasync
    .catch((error) => {
      // tslint:disable-next-line: no-console
      console.log(error);
      // return;
      resolve(ResultType.NeedDebug);
    });
  });
}

export function analyzeContentBuffer2(zstd: any = null, contentBuffer: Uint8Array,
                                      fileReader: FileReader, reader: Reader,
                                      props: any, menuMode: boolean = false,
                                      modeFonts: IModeFonts = null): Promise<number> {
  const { bInsertFile, bNoEndPara, bRecalc, options, type, documentVersion } = props;
  // console.log(zstd, options, contentBuffer)
  let date = new Date();
  const newZip = new JSZip();
  // const reader = this;

  // reset DEFAULT_FONT
  // resetDefaultFont();

  return new Promise((resolve) => {
    let newBlob = null;
    try {
      if (menuMode === true) {
        // tslint:disable-next-line: no-console
        // console.time('decompression');
      }
      if (zstd != null) {
        const streaming = new zstd.Streaming();
        const deCompressed = streaming.decompress(contentBuffer);
        if (menuMode === true) {
          // tslint:disable-next-line: no-console
          // console.timeEnd('decompression');
        }
        // After buffer separation, type can be original. No need version number
        newBlob = new Blob([deCompressed.buffer], { type: 'application/apollo-zip' });
      } else {
        // After buffer separation, type can be original. No need version number
        newBlob = new Blob([contentBuffer], { type: 'application/apollo-zip' });
      }

    } catch (error) {
      // tslint:disable-next-line: no-console
      if (menuMode === true) {
        // tslint:disable-next-line: no-console
        // console.timeEnd('read time');
      }
      // tslint:disable-next-line: no-console
      console.log(error);
      // return;
      resolve(ResultType.NeedDebug);
    }
    if (options != null) {
      options.time += new Date().getTime() - date.getTime();
    }
    // unzip the zipped blob
    // tslint:disable-next-line: newline-per-chained-call
    newZip.loadAsync(newBlob).then((zip) => {
      date = new Date();
      let documentZippedFile = null;
      let stylesZippedFile = null;
      let settingsZippedFile = null;
      let mediaZippedFile = null;
      let headerZippedFile = null;
      let footerZippedFile = null;
      let cascadeZippedFile = null;

      newZip.forEach((relativePath: string, file: any): void => {
        // console.log(relativePath);
        // console.log(file);
        switch (relativePath) {
          case 'Document.xml': {
            documentZippedFile = file;
            break;
          }
          case 'Styles.xml': {
            stylesZippedFile = file;
            break;
          }
          case 'Header1.xml': {
            headerZippedFile = file;
            break;
          }
          case 'Footer.xml': {
            footerZippedFile = file;
            break;
          }
          case 'Settings.xml': {
            settingsZippedFile = file;
            break;
          }
          case 'Media.xml': {
            mediaZippedFile = file;
            break;
          }
          case 'Cascade.xml': {
            cascadeZippedFile = file;
            break;
          }
          case 'Editor-html.html': {
            //
            break;
          }
          default: {
            // tslint:disable-next-line: no-console
            console.warn('unexpected xml file detected.');
            break;
          }
        }
      });

      // critical xmls
      try {
        if (!mediaZippedFile || !documentZippedFile) {
          throw new Error('Critical xml file missing');
        }
      } catch (error) {
        // tslint:disable-next-line: no-console
        console.log(error);
        // return ;
        resolve(ResultType.NeedDebug);
      }

      // minor xmls
      try {
        if (!stylesZippedFile || !settingsZippedFile) {
          throw new Error('Minor xml file missing');
        }
      } catch (error) {
        // tslint:disable-next-line: no-console
        console.log(error);
        // continue
      }

      if (options != null) {
        options.time += new Date().getTime() - date.getTime();
      }

      const recordContent: IRecordContent = {
          document: null,
          style: null,
          settings: null,
          media: null,
          header: null,
          footer: null,
          cascade: null,
          documentVersion,
          insertType: type,
      };

      // get all xmls content
      const mediaPromise = mediaZippedFile.async('string');
      const stylePromise = stylesZippedFile.async('string');
      const documentPromise = documentZippedFile.async('string');
      const settingsPromise = settingsZippedFile.async('string');
      const headerPromise = headerZippedFile != null ? headerZippedFile.async('string') : null;
      const footerPromise = footerZippedFile != null ? footerZippedFile.async('string') : null;
      // const cascadePromise = cascadeZippedFile != null ? cascadeZippedFile.async('string') : null;
      const fPromises = [mediaPromise, stylePromise, documentPromise, settingsPromise,
                        headerPromise, footerPromise];

      // tslint:disable-next-line: newline-per-chained-call
      Promise.all(fPromises).then((values) => {
        if (options && options.closeObj && options.closeObj.bClose) {
          return resolve(null);
        }
        // Returned values will be in order of the Promises passed, regardless of completion order.
        // console.log(values);
        const [mediaFile, stylesFile, documentFile, settingsFile, headerFile, footerFile, cascadeFile] = values;

        /** read media.xml first */
        date = new Date();
        recordContent.media = mediaFile;
        if (!reader.readMedia(mediaFile) && !bInsertFile) { // fail safe
          // TODO: create new document?
          // this.props.documentCore.createNewDocument();
          // tslint:disable-next-line: no-console
          // console.log('readMedia');
          resolve(ResultType.NeedDebug);
        }
        if (options != null) {
          options.time += new Date().getTime() - date.getTime();
        }

        /** read styles.xml */
        date = new Date();
        recordContent.style = stylesFile;
        if (!bInsertFile && !reader.readStyles(stylesFile as string, bInsertFile)) { // fail safe
          // this.documentCore.createNewDocument();
        }

        /** then read document.xml */
        date = new Date();
        recordContent.document = documentFile;
        // if headerfooter exists, still need recalc!
        let bRecalcTrue = bRecalc;
        if (headerZippedFile != null) {
          bRecalcTrue = true;
        }

        // prepare default fonts
        const curModeFonts = reader.getCurModeFonts(modeFonts);

        if (!bInsertFile) {
          const doc = reader.getDoc();
          doc?.setTrackRevisions(false);
        }

        if (!reader.read2(documentFile as string, bInsertFile, bNoEndPara, bRecalcTrue, curModeFonts, options,
                          type, documentVersion)
          && !bInsertFile) {
          // fail safe
          // this.props.documentCore.createNewDocument();
          // tslint:disable-next-line: no-console
          // console.log('read');
          resolve(ResultType.NeedDebug);
        }
        if (options != null) {
          options.time += new Date().getTime() - date.getTime();
        }

        /** read settings.xml */
        date = new Date();
        recordContent.settings = settingsFile;
        const setMap = new Map();
        if (!reader.readSettings(settingsFile as string, bInsertFile, setMap) && !bInsertFile) { // fail safe
          // this.documentCore.createNewDocument();
        }

        if (!bInsertFile && setMap.size > 0) {
          reader['document'].setAllFileProperties(setMap);
        }

        // read cascade
        date = new Date();
        // recordContent.cascade = cascadeFile;
        // if (!reader.readCascade(cascadeFile as string) && !bInsertFile) { // fail safe
        //     // this.documentCore.createNewDocument();
        // }

        //// deal with headerfooter ////
        if (headerZippedFile != null) {
          // when insert file, inserted file's headerfooter should be deserted
          if (bInsertFile === true) {
            reader.readAllCallback();
            if (options != null) {
              options.time += new Date().getTime() - date.getTime();
            }

            fileStartRecord(recordContent, bInsertFile);
            resolve(ResultType.Success);

          } else {
            if (options != null) {
              options.time += new Date().getTime() - date.getTime();
            }

            // read header
            date = new Date();
            recordContent.header = headerFile;
            if (!reader.readHeader2(headerFile as string) && !bInsertFile) { // fail safe
              // this.documentCore.createNewDocument();
            }
            if (options != null) {
              options.time += new Date().getTime() - date.getTime();
            }

            // read footer
            date = new Date();
            recordContent.footer = footerFile;
            fileStartRecord(recordContent, bInsertFile);
            if (!reader.readFooter2(footerFile as string) && !bInsertFile) { // fail safe
              // this.documentCore.createNewDocument();
            }

            const res = reader.recalculate();
            options.recalcPromise = res;
            reader.readAllCallback();
            if (options != null) {
              options.time += new Date().getTime() - date.getTime();
            }
            if (menuMode === true) {
              // tslint:disable-next-line: no-console
              // console.timeEnd('read time');
            }

            // all done
            resolve(ResultType.Success);

          }
        } else { // headerZippedFile null
          const res = reader.recalculate();
          options.recalcPromise = res;
          reader.readAllCallback();
          if (options != null) {
            options.time += new Date().getTime() - date.getTime();
          }
          if (menuMode === true) {
            // tslint:disable-next-line: no-console
            // console.timeEnd('read time');
          }
          fileStartRecord(recordContent, bInsertFile);
          resolve(ResultType.Success);
        }
        //// deal with headerfooter end ////

      // tslint:disable-next-line: newline-per-chained-call
      }).catch((error) => {
        // tslint:disable-next-line: no-console
        console.log(error);
        // return;
        resolve(ResultType.NeedDebug);
      });
    }) // newzip loadasync
      .catch((error) => {
        // tslint:disable-next-line: no-console
        console.log(error);
        // return;
        resolve(ResultType.NeedDebug);
      });

  });
}

export function analyzeFileForXmlInfo(documentFile: string, versionNum: number,
                                      documentSectionType: DocumentSectionType, sJson?: string): string {
  const result = {};
  const xmlDoc = tXmlCustomParse(documentFile);

  // w:document, w:hdr, w:ftr
  const rootNode: rtNode = xmlDoc[1] as unknown as rtNode;
  if (typeof rootNode === 'object') {
    const rootNodeName = rootNode.tagName;
    switch (rootNodeName) {
      case 'w:document': {
        const bodyNode = rootNode.children[0];
        if (typeof bodyNode === 'object') {
          const contentNodes = bodyNode.children;
          traverseContentNodes(contentNodes, result, versionNum, documentSectionType, sJson);
        }
        break;
      }

      case 'w:hdr':
      case 'w:ftr': {
        const contentNodes = rootNode.children;
        traverseContentNodes(contentNodes, result, versionNum, documentSectionType, sJson);
        break;
      }
      default: {
        break;
      }
    }
  }

  return JSON.stringify(result);
}

export function analyzeRecordContent(recordContent: IRecordContent, reader: Reader, props: any,
                                     modeFonts: IModeFonts = null): Promise<number> {

  const {document: documentFile, style: stylesFile, settings: settingsFile, media: mediaFile,
    header: headerFile, footer: footerFile, cascade: cascadeFile} = recordContent;
  const { bInsertFile, bNoEndPara, bRecalc, type, documentVersion } = props;

  return new Promise((resolve) => {
    if (!reader.readMedia(mediaFile) && !bInsertFile) { // fail safe
      resolve(ResultType.NeedDebug);
    }

    if (!bInsertFile && !reader.readStyles(stylesFile as string, bInsertFile)) { // fail safe
      // this.documentCore.createNewDocument();
    }

    // if headerfooter exists, still need recalc!
    let bRecalcTrue = bRecalc;
    if (headerFile != null) {
      bRecalcTrue = true;
    }

    // prepare default fonts
    const curModeFonts = reader.getCurModeFonts(modeFonts);
    if (!reader.read2(documentFile as string, bInsertFile, bNoEndPara, bRecalcTrue, curModeFonts, null,
                        type, documentVersion) &&
      !bInsertFile) {
      resolve(ResultType.NeedDebug);
    }

    if (!reader.readSettings(settingsFile as string, bInsertFile) && !bInsertFile) { // fail safe
      // this.documentCore.createNewDocument();
    }

    // read cascade xml
    if (cascadeFile != null) {
        reader.readCascade(cascadeFile);
    }

    if (headerFile != null) {
      // when insert file, inserted file's headerfooter should be deserted
      if (bInsertFile === true) {
        reader.readAllCallback();
        resolve(ResultType.Success);
      } else {
        if (!reader.readHeader2(headerFile as string) && !bInsertFile) { // fail safe
          // this.documentCore.createNewDocument();
        }
        if (!reader.readFooter2(footerFile as string) && !bInsertFile) { // fail safe
          // this.documentCore.createNewDocument();
        }

        // reader.recalculate();
        // reader.readAllCallback();

        // resolve(ResultType.Success);
      }

    } // else { // headerZippedFile null

    reader.recalculate();

    reader.readAllCallback();
    resolve(ResultType.Success);
  // }

  });

}

// function collectStructsXmlInfoInPara(rootNode: rtNode, result: any,
//                                      regionContainer: IXmlInfoRegionContainer = null,
//                                      sections: IXmlInfoSection[] = [], versionNum: number,
//                                      documentSectionType: DocumentSectionType): void {

//   let bPrevItemSectionStart = false; // for checking section title
//   // pick out sdt
//   for (const paraChild of rootNode.children) {
//     if (typeof paraChild === 'object') {
//       if (paraChild.tagName === 'sdt') {
//         // write sdt into json
//         const attrs = paraChild.attributes;
//         const sdtChildren = paraChild.children;
//         const externalType =  +Object.keys(EXTERNAL_OUTER_STRUCT_TYPE)
//           .find((key) => EXTERNAL_OUTER_STRUCT_TYPE[key] === +attrs['type']);
//         const type = EXTERNAL_OUTER_STRUCT_TYPE[externalType];
//         // default
//         const sdt: IXmlInfoStruct = {
//           content_text: '',
//           type: externalType,
//           location: 1,
//           property: {
//             serialNumber: STD_START_DEFAULT.serialNumber,
//             placeholder: STD_START_DEFAULT.placeholder,
//             helpTip: STD_START_DEFAULT.helpTip, // 提示符
//             deleteProtect: STD_START_DEFAULT.deleteProtect === 1 ? true : false,
//             editProtect: STD_START_DEFAULT.editProtect === 1 ? true : false,
//             copyProtect: STD_START_DEFAULT.copyProtect === 1 ? true : false,
//             showBorder: STD_START_DEFAULT.showBorder === 1 ? true : false,
//             borderString: STD_START_DEFAULT.borderString,
//             editReverse: STD_START_DEFAULT.editReverse === 1 ? true : false,
//             backgroundColorHidden: STD_START_DEFAULT.backgroundColorHidden === 1 ? true : false,
//             customProperty: STD_START_DEFAULT.customProperty,
//             newControlHidden: STD_START_DEFAULT.newControlHidden === 1 ? true : false,
//             tabJump: STD_START_DEFAULT.tabJump === 1 ? true : false,
//             cascade: STD_START_DEFAULT.cascade, // TODO: not exist?

//             itemList: undefined,

//             // ui only
//             showPlaceholder: STD_START_DEFAULT.showPlaceholder === 1 ? true : false,
//           },
//         };
//         let text = '';
//         // set location info
//         if (documentSectionType != null) {
//           sdt['location'] = EXTERNAL_DOCUMENTSECTION_TYPE[documentSectionType];
//         }

//         const props = sdt.property;
//         if (type === NewControlType.DateTimeBox) {
//           props.dateBoxFormat = STD_START_DEFAULT.dateBoxFormat,
//           props.customFormat = STD_START_DEFAULT.customFormat,
//           props.startDate = STD_START_DEFAULT.startDate,
//           props.endDate = STD_START_DEFAULT.endDate;
//           props.printSelected = STD_START_DEFAULT.printSelected === 1 ? true : false;
//           // need datetime?
//         } else if (type === NewControlType.SignatureBox) {
//           props.signatureCount = STD_START_DEFAULT.signatureCount,
//           props.preText = STD_START_DEFAULT.preText,
//           props.signatureSeparator = STD_START_DEFAULT.signatureSeparator,
//           props.postText = STD_START_DEFAULT.postText,
//           props.signaturePlaceholder = STD_START_DEFAULT.signaturePlaceholder,
//           props.signatureRatio = STD_START_DEFAULT.signatureRatio,
//           // tslint:disable-next-line: max-line-length
//           props.rowHeightRestriction = STD_START_DEFAULT.rowHeightRestriction === 1 ? true : false; // boolean actually
//           props.signType = STD_START_DEFAULT.signType;
//           props.alwaysShow = STD_START_DEFAULT.alwaysShow;
//           props.showSignBorder = STD_START_DEFAULT.showSignBorder === 1 ? true : false;
//         } else if ([NewControlType.RadioButton, NewControlType.MultiRadio].includes(type)) {
//           props.spaceNum = STD_START_DEFAULT.spaceNum;
//           props.supportMultLines = STD_START_DEFAULT.supportMultLines === 1 ? true : false;
//         } else if (type === NewControlType.CheckBox) {
//             props.label = STD_START_DEFAULT.label;
//             props.labelCode = STD_START_DEFAULT.labelCode;
//         } else if ([NewControlType.ListBox, NewControlType.MultiListBox, NewControlType.Combox,
//           NewControlType.MultiCombox].includes(type)) {
//           props.retrieve = STD_START_DEFAULT.retrieve === 1 ? true : false,
//           props.selectPrefixContent = STD_START_DEFAULT.selectPrefixContent,
//           props.prefixContent = STD_START_DEFAULT.prefixContent,
//           props.separator = STD_START_DEFAULT.separator,
//           props.showValue = undefined;
//           props.printSelected = STD_START_DEFAULT.printSelected === 1 ? true : false;
//         } else if (NewControlType.NumberBox === type) {
//           props.minValue = STD_START_DEFAULT.minValue,
//           props.maxValue = STD_START_DEFAULT.maxValue,
//           props.precision = STD_START_DEFAULT.precision, // 精度
//           props.unit = STD_START_DEFAULT.unit;
//           props.forceValidate = STD_START_DEFAULT.forceValidate === 1 ? true : false;
//           props.printSelected = STD_START_DEFAULT.printSelected === 1 ? true : false;
//         } else if (NewControlType.TextBox === type) {
//           props.secretType = STD_START_DEFAULT.secretType, // both in text and value struct
//           props.fixedLength = STD_START_DEFAULT.fixedLength,
//           props.maxLength = STD_START_DEFAULT.maxLength,
//           props.isMustFill = STD_START_DEFAULT.isMustFill === 1 ? true : false,
//           props.title = STD_START_DEFAULT.title;
//           props.hideHasTitle = STD_START_DEFAULT.hideHasTitle === 1 ? true : false;

//           props.printSelected = STD_START_DEFAULT.printSelected === 1 ? true : false;

//         } else if (NewControlType.AddressBox === type) {
//           props.hierarchy = STD_START_DEFAULT.hierarchy;
//           props.province = STD_START_DEFAULT.province;
//           props.city = STD_START_DEFAULT.city;
//           props.county = STD_START_DEFAULT.county;
//         }

//         if ([NewControlType.RadioButton, NewControlType.MultiRadio, NewControlType.CheckBox].includes(type)) {
//           props.checked = STD_START_DEFAULT.checked === 1 ? true : false,
//           props.showRight = STD_START_DEFAULT.showRight === 1 ? true : false, // 勾选框居右 (checkbox, radiobox)
//           props.printSelected = STD_START_DEFAULT.printSelected === 1 ? true : false;
//         }

//         for (const sdtChild of sdtChildren) {
//           if (typeof sdtChild === 'object') {
//             const sdtChildName = sdtChild.tagName;
//             const sdtProps = sdt.property;
//             switch (sdtChildName) {
//               // not related to sdt prop
//               case 'w:sdtcontent': {
//                 const sdtContentItems = sdtChild.children;
//                 for (let i = 0, len = sdtContentItems.length; i < len; i++) {
//                   const sdtContentItem = sdtContentItems[i];
//                   if (typeof sdtContentItem === 'object' && sdtContentItem.tagName === 'w:r') {
//                     for (const runItem of sdtContentItem.children) {
//                       if (typeof runItem === 'object' && runItem.tagName === 'w:t') {
//                         const curText = safeDecodeURIComponent(runItem.children[0] as string, versionNum);
//                         // already read title/placeholder if any
//                         // not add title/placeholder to content
//                         if (i === 0 || i === 1) { // TODO: sure title/placeholder only show on 1/2 items of sdtcontent?
//                           if (curText === sdtProps.title ||
//                               (curText === sdtProps.placeholder && sdtProps.showPlaceholder === true)) {
//                             continue;
//                           }
//                         }
//                         text += curText;
//                         // may be in region
//                         if (regionContainer != null) {
//                           const regionTexts = regionContainer.regionTexts;
//                           if (regionTexts.length > 0) {
//                             for (let j = 0, leng = regionTexts.length; j < leng; j++) {
//                               regionTexts[j] += curText;
//                             }
//                           }
//                         }
//                         // may be in section
//                         if (sections.length > 0) {
//                           for (let j = 0, leng = sections.length; j < leng; j++) {
//                             const curSection = sections[j];
//                             curSection.content_text += safeDecodeURIComponent(curText, versionNum);
//                           }
//                         }
//                       } else if (NewControlType.TextBox === type && typeof runItem === 'object'
//                                   && runItem.tagName === 'w:br') {
//                         text += '\n';
//                       }
//                     }
//                   } else if (typeof sdtContentItem === 'object' &&
//                     sdt.type === 15 && sdtContentItem.tagName === 'sdt') {
//                     // add text for sign box!
//                     for (const signItem of sdtContentItem.children) {
//                       if (typeof signItem === 'object' && signItem.tagName === 'w:sdtcontent') {
//                         for (const subStructContent of signItem.children) {
//                           if (typeof subStructContent === 'object' && subStructContent.tagName === 'w:r') {
//                             for (const subRunItem of subStructContent.children) {
//                               if (typeof subRunItem === 'object' && subRunItem.tagName === 'w:t') {
//                                 // is it the same as sign placeholder?
//                                 const subRunText = safeDecodeURIComponent(subRunItem.children[0] as string, versionNum);
//                                 if (subRunText.indexOf(sdtProps.signaturePlaceholder) !== -1) {
//                                   continue;
//                                 } else {

//                                   text += subRunText;

//                                   // may be in region
//                                   if (regionContainer != null) {
//                                     const regionTexts = regionContainer.regionTexts;
//                                     if (regionTexts.length > 0) {
//                                       for (let j = 0, leng = regionTexts.length; j < leng; j++) {
//                                         regionTexts[j] += subRunText;
//                                       }
//                                     }
//                                   }
//                                   // may be in section
//                                   if (sections.length > 0) {
//                                     for (let j = 0, leng = sections.length; j < leng; j++) {
//                                       const curSection = sections[j];
//                                       curSection.content_text += safeDecodeURIComponent(subRunText, versionNum);
//                                     }
//                                   }
//                                 }
//                               }
//                             }
//                           }
//                         }
//                         break;
//                       }
//                     }

//                   }
//                 }
//                 break;
//               }
//               case 'logicEvent': {
//                 // NO NEED
//                 break;
//               }
//               // props
//               case 'serialNumber': {
//                 sdtProps.serialNumber = sdtChild.children[0] as string;
//                 break;
//               }
//               case 'placeholder': {
//                 sdtProps['placeholder'] = safeDecodeURIComponent(sdtChild.children[0] as string, versionNum);
//                 break;
//               }
//               case 'isMustFill': {
//                 sdtProps['isMustFill'] = (sdtChild.children[0] === '1') ? true : false;
//                 break;
//               }
//               case 'deleteProtect': {
//                 sdtProps['deleteProtect'] = (sdtChild.children[0] === '1') ? true : false;
//                 break;
//               }
//               case 'editProtect': {
//                 sdtProps['editProtect'] = (sdtChild.children[0] === '1') ? true : false;
//                 break;
//               }
//               case 'copyProtect': {
//                 sdtProps['copyProtect'] = (sdtChild.children[0] === '1') ? true : false;
//                 break;
//               }
//               case 'showBorder': {
//                 sdtProps['showBorder'] = (sdtChild.children[0] === '1') ? true : false;
//                 break;
//               }
//               case 'borderString': {
//                 sdtProps['borderString'] = sdtChild.children[0] as string;
//                 break;
//               }
//               case 'editReverse': {
//                 sdtProps['editReverse'] = (sdtChild.children[0] === '1') ? true : false;
//                 break;
//               }
//               case 'backgroundColorHidden': {
//                 sdtProps['backgroundColorHidden'] = (sdtChild.children[0] === '1') ? true : false;
//                 break;
//               }
//               case 'customProperty': {
//                 // <customProperty>
//                 //   <first type="1">111</first>
//                 //   <second type="3">222</second>
//                 //   <third type="2">true</third>
//                 // </customProperty>
//                 const customProperty = {};
//                 for (const customPropItem of sdtChild.children) {
//                   if (typeof customPropItem === 'object') {
//                     // tslint:disable-next-line: no-shadowed-variable
//                     const type: DataType = +customPropItem.attributes['type'];
//                     if (type === DataType.String) {
//                       const v = customPropItem.children[0] as string || '';
//                       customProperty[customPropItem.tagName] =
//                       safeDecodeURIComponent(v, versionNum);
//                     } else if (type === DataType.Number) {
//                       customProperty[customPropItem.tagName] = +customPropItem.children[0];
//                     } else if (type === DataType.Boolean) {
//                       if (customPropItem.children[0] === undefined) {
//                         customProperty[customPropItem.tagName] = '';
//                       } else {
//                         customProperty[customPropItem.tagName] = (customPropItem.children[0] === 'true') ? true : false;
//                       }
//                     }
//                   }
//                 }
//                 sdtProps['customProperty'] = customProperty;
//                 break;
//               }
//               case 'tabJump': {
//                 sdtProps['tabJump'] = (sdtChild.children[0] === '1') ? true : false;
//                 break;
//               }
//               case 'newControlHidden': {
//                 sdtProps['newControlHidden'] = (sdtChild.children[0] === '1') ? true : false;
//                 break;
//               }
//               case 'listItems': {
//                 const listItemObjs = [];
//                 for (const listItem of sdtChild.children) {
//                   if (typeof listItem === 'object' && listItem.tagName === 'listItem') {
//                     // <listItem>
//                     //   <name>you</name>
//                     //   <value>11</value>
//                     //   <select>0</select>
//                     // </listItem>
//                     const listItemObj = {};
//                     for (const listItemPair of listItem.children) {
//                       if (typeof listItemPair === 'object') {
//                         if (listItemPair.tagName === 'name') {
//                           listItemObj['name'] = listItemPair.children[0];
//                         } else if (listItemPair.tagName === 'value') {
//                           listItemObj['value'] = listItemPair.children[0];
//                         } else if (listItemPair.tagName === 'select') {
//                           listItemObj['select'] = +listItemPair.children[0];
//                         }
//                       }
//                     }
//                     listItemObjs.push(listItemObj);
//                   }
//                 }
//                 sdtProps['listItems'] = listItemObjs;
//                 break;
//               }
//               case 'helpTip': {
//                 sdtProps['helpTip'] = sdtChild.children[0] as string;
//                 break;
//               }

//               // ui only
//               case 'showPlaceholder': {
//                 if (sdtChild.children[0] === '1') {
//                   sdtProps['showPlaceholder'] = true;
//                 }
//                 break;
//               }

//               // text struct
//               case 'secretType': {
//                 sdtProps['secretType'] = +sdtChild.children[0];
//                 break;
//               }
//               case 'fixedLength': {
//                 sdtProps['fixedLength'] = +sdtChild.children[0];
//                 break;
//               }
//               case 'maxLength': {
//                 sdtProps['maxLength'] = +sdtChild.children[0];
//                 break;
//               }
//               case 'title': {
//                 sdtProps['title'] = safeDecodeURIComponent(sdtChild.children[0] as string, versionNum);
//                 break;
//               }
//               case 'hideHasTitle': {
//                 sdtProps['hideHasTitle'] = (sdtChild.children[0] === '1') ? true : false;
//               }

//               // value struct
//               case 'minValue': {
//                 sdtProps['minValue'] = +sdtChild.children[0];
//                 break;
//               }
//               case 'maxValue': {
//                 sdtProps['maxValue'] = +sdtChild.children[0];
//                 break;
//               }
//               case 'precision': {
//                 sdtProps['precision'] = +sdtChild.children[0];
//                 break;
//               }
//               case 'unit': {
//                 sdtProps['unit'] = sdtChild.children[0] as string;
//                 break;
//               }
//               case 'forceValidate': {
//                 sdtProps['forceValidate'] = (sdtChild.children[0] === '1') ? true : false;
//                 break;
//               }

//               // multi struct
//               case 'retrieve': {
//                 sdtProps['retrieve'] = (sdtChild.children[0] === '1') ? true : false;
//                 break;
//               }
//               case 'selectPrefixContent': {
//                 sdtProps['selectPrefixContent'] = sdtChild.children[0] as string;
//                 break;
//               }
//               case 'prefixContent': {
//                 sdtProps['prefixContent'] = sdtChild.children[0] as string;
//                 break;
//               }
//               case 'separator': {
//                 sdtProps['separator'] = sdtChild.children[0] as string;
//                 break;
//               }
//               case 'showValue': {
//                 sdtProps['showValue'] = (sdtChild.children[0] === '1') ? true : false;
//                 break;
//               }
//               // datebox struct
//               case 'dateType': {
//                 sdtProps['dateType'] = +sdtChild.children[0];
//                 break;
//               }
//               case 'customDateFormat': {
//                 sdtProps['customDateFormat'] = sdtChild.children[0];
//                 break;
//               }
//               case 'startDate': {
//                 sdtProps['startDate'] = sdtChild.children[0] as string;
//                 break;
//               }
//               case 'endDate': {
//                 sdtProps['endDate'] = sdtChild.children[0] as string;
//                 break;
//               }
//               // checkbox
//               case 'showRight': { // also radiobutton
//                 sdtProps['showRight'] = (sdtChild.children[0] === '1') ? true : false;
//                 break;
//               }
//               case 'checked': {
//                 sdtProps['checked'] = (sdtChild.children[0] === '1') ? true : false;
//                 break;
//               }
//               case 'printSelected': {
//                 sdtProps['printSelected'] = (sdtChild.children[0] === '1') ? true : false;
//                 break;
//               }
//               case 'label': {
//                 sdtProps['label'] = sdtChild.children[0] as string;
//                 break;
//               }
//               case 'labelCode': {
//                 sdtProps['labelCode'] = sdtChild.children[0] as string;
//                 break;
//               }
//               case 'group': {
//                 sdtProps['group'] = sdtChild.children[0] as string;
//                 break;
//               }
//               // radiobutton
//               case 'showType': {
//                 sdtProps['showType'] = +sdtChild.children[0];
//                 if (isNaN(sdtProps['showType']) === true) {
//                   sdtProps['showType'] = STD_START_DEFAULT.showType;
//                 }
//                 break;
//               }
//               case 'spaceNum': {
//                 sdtProps['spaceNum'] = +sdtChild.children[0];
//                 break;
//               }
//               case 'supportMultLines': {
//                 sdtProps['supportMultLines'] = (sdtChild.children[0] === '1') ? true : false;
//                 break;
//               }

//               // signature box
//               case 'signatureCount': {
//                 sdtProps['signatureCount'] = +sdtChild.children[0];
//                 break;
//               }
//               case 'preText': {
//                 sdtProps['preText'] = sdtChild.children[0] as string;
//                 break;
//               }
//               case 'signatureSeparator': {
//                 sdtProps['signatureSeparator'] = sdtChild.children[0] as string;
//                 break;
//               }
//               case 'postText': {
//                 sdtProps['postText'] = sdtChild.children[0] as string;
//                 break;
//               }
//               case 'signaturePlaceholder': {
//                 sdtProps['signaturePlaceholder'] = sdtChild.children[0] as string;
//                 break;
//               }
//               case 'signatureRatio': {
//                 sdtProps['signatureRatio'] = +sdtChild.children[0];
//                 break;
//               }
//               case 'rowHeightRestriction': {
//                 sdtProps['rowHeightRestriction'] = (sdtChild.children[0] === '1') ? true : false;
//                 break;
//               }
//               case 'signType': {
//                 sdtProps['signType'] = +sdtChild.children[0];
//                 break;
//               }
//               case 'alwaysShow': {
//                 sdtProps['alwaysShow'] = +sdtChild.children[0];
//                 break;
//               }
//               case 'showSignBorder': {
//                 sdtProps['showSignBorder'] = (sdtChild.children[0] === '1') ? true : false;
//                 break;
//               }

//               default: {
//                 break;
//               }

//             }
//           }
//         }
//         // add content_text
//         sdt.content_text = text;
//         // add ref
//         result[attrs['name']] = sdt;

//         bPrevItemSectionStart = false;

//       } //else if (paraChild.tagName === 'sectionStart') {
//       //节的存储方案发生变化了，标签是section by tinyzhi
//       //TODO: 2025-04-10
//         else if (paraChild.tagName === 'section') {
//         // write section into json
//         const attrs = paraChild.attributes;
//         const sectionChildren = paraChild.children;
//         // default
//         const section: IXmlInfoSection = {
//           content_text: '',
//           type: 13,
//           location: 1,
//           property: {
//             serialNumber: STD_START_DEFAULT.serialNumber,
//             placeholder: STD_START_DEFAULT.placeholder,
//             isMustFill: STD_START_DEFAULT.isMustFill === 1 ? true : false,
//             deleteProtect: STD_START_DEFAULT.deleteProtect === 1 ? true : false,
//             editProtect: STD_START_DEFAULT.editProtect === 1 ? true : false,
//             copyProtect: STD_START_DEFAULT.copyProtect === 1 ? true : false,
//             showBorder: STD_START_DEFAULT.showBorder === 1 ? true : false,
//             borderString: STD_START_DEFAULT.borderString,
//             editReverse: STD_START_DEFAULT.editReverse === 1 ? true : false,
//             backgroundColorHidden: STD_START_DEFAULT.backgroundColorHidden === 1 ? true : false,
//             customProperty: STD_START_DEFAULT.customProperty,
//             tabJump: STD_START_DEFAULT.tabJump === 1 ? true : false,
//             newControlHidden: STD_START_DEFAULT.newControlHidden === 1 ? true : false,
//             helpTip: STD_START_DEFAULT.helpTip, // 提示符
//             showPlaceholder: STD_START_DEFAULT.showPlaceholder === 1 ? true : false,
//             secretType: STD_START_DEFAULT.secretType, // both in text and value struct
//             fixedLength: STD_START_DEFAULT.fixedLength,
//             maxLength: STD_START_DEFAULT.maxLength,
//             title: STD_START_DEFAULT.title,
//           }
//         };
//         // let sectionText = '';
//         const externalType =  +Object.keys(EXTERNAL_OUTER_STRUCT_TYPE)
//           .find((key) => EXTERNAL_OUTER_STRUCT_TYPE[key] === +attrs['type']);
//         section.type = externalType;

//         // set location info
//         if (documentSectionType != null) {
//           section['location'] = EXTERNAL_DOCUMENTSECTION_TYPE[documentSectionType];
//         }

//         for (const sectionChild of sectionChildren) {
//           if (typeof sectionChild === 'object') {
//             const sectionChildName = sectionChild.tagName;
//             const sectionProps = section.property;
//             switch (sectionChildName) {
//               case 'serialNumber': {
//                 sectionProps.serialNumber = sectionChild.children[0] as string;
//                 break;
//               }
//               case 'placeholder': {
//                 sectionProps.placeholder = safeDecodeURIComponent(sectionChild.children[0] as string, versionNum);
//                 break;
//               }
//               case 'isMustFill': {
//                 sectionProps['isMustFill'] = (sectionChild.children[0] === '1') ? true : false;
//                 break;
//               }
//               case 'deleteProtect': {
//                 sectionProps['deleteProtect'] = (sectionChild.children[0] === '1') ? true : false;
//                 break;
//               }
//               case 'editProtect': {
//                 sectionProps['editProtect'] = (sectionChild.children[0] === '1') ? true : false;
//                 break;
//               }
//               case 'copyProtect': {
//                 sectionProps['copyProtect'] = (sectionChild.children[0] === '1') ? true : false;
//                 break;
//               }
//               case 'showBorder': {
//                 sectionProps['showBorder'] = (sectionChild.children[0] === '1') ? true : false;
//                 break;
//               }
//               case 'borderString': {
//                 sectionProps.borderString = sectionChild.children[0] as string;
//                 break;
//               }
//               case 'editReverse': {
//                 sectionProps['editReverse'] = (sectionChild.children[0] === '1') ? true : false;
//                 break;
//               }
//               case 'backgroundColorHidden': {
//                 sectionProps['backgroundColorHidden'] = (sectionChild.children[0] === '1') ? true : false;
//                 break;
//               }
//               case 'customProperty': {
//                 // <customProperty>
//                 //   <first type="1">111</first>
//                 //   <second type="3">222</second>
//                 //   <third type="2">true</third>
//                 // </customProperty>
//                 const customProperty = {};
//                 for (const customPropItem of sectionChild.children) {
//                   if (typeof customPropItem === 'object') {
//                     const type: DataType = +customPropItem.attributes['type'];
//                     if (type === DataType.String) {
//                       customProperty[customPropItem.tagName] =
//                       safeDecodeURIComponent(customPropItem.children[0]as string, versionNum);
//                     } else if (type === DataType.Number) {
//                       customProperty[customPropItem.tagName] = +customPropItem.children[0];
//                     } else if (type === DataType.Boolean) {
//                       customProperty[customPropItem.tagName] = (customPropItem.children[0] === 'true') ? true : false;
//                     }
//                   }
//                 }
//                 sectionProps['customProperty'] = customProperty;
//                 break;
//               }
//               case 'tabJump': {
//                 sectionProps['tabJump'] = (sectionChild.children[0] === '1') ? true : false;
//                 break;
//               }
//               case 'newControlHidden': {
//                 sectionProps['newControlHidden'] = (sectionChild.children[0] === '1') ? true : false;
//                 break;
//               }
//               case 'helpTip': {
//                 sectionProps.helpTip = sectionChild.children[0] as string;
//                 break;
//               }
//               // ui only
//               case 'showPlaceholder': {
//                 if (sectionChild.children[0] === '1') {
//                   sectionProps['showPlaceholder'] = true;
//                 }
//                 break;
//               }
//               // text struct
//               case 'secretType': { // shared in text and value struct
//                 sectionProps['secretType'] = +sectionChild.children[0];
//                 break;
//               }
//               case 'fixedLength': {
//                 sectionProps['fixedLength'] = +sectionChild.children[0];
//                 break;
//               }
//               case 'maxLength': {
//                 sectionProps['maxLength'] = +sectionChild.children[0];
//                 break;
//               }
//               case 'title': {
//                 sectionProps.title = safeDecodeURIComponent(sectionChild.children[0] as string, versionNum);
//                 break;
//               }
//               default: {
//                 break;
//               }
//             }
//           }
//         }

//         // add ref
//         result[attrs['name']] = section;

//         // push to sections which pass around cross para.
//         // this is only for context_text, so... have fun but not mess around
//         sections.push(section);
//         bPrevItemSectionStart = true;
//       //} else if (paraChild.tagName === 'sectionEnd') {
//         // add content_text
//         // section.content_text = sectionText;
//         //if (sections.length > 0) {
//         //  sections.splice(sections.length - 1, 1);
//        // } else {
//           // tslint:disable-next-line: no-console
//         //  console.warn('no section found at sectionEnd');
//        // }
//        // bPrevItemSectionStart = false;

//       } else if (paraChild.tagName === 'w:r') {
//         let bTitle = false;
//         for (const runItem of paraChild.children) {
//           if (typeof runItem === 'object' && runItem.tagName === 'w:t') {
//             if (regionContainer != null) {
//               const regionTexts = regionContainer.regionTexts;
//               if (regionTexts.length > 0) {
//                 const curText = safeDecodeURIComponent(runItem.children[0] as string, versionNum);
//                 // if curText is region title, skip adding
//                 const {paraIndexInRegion, rg} = regionContainer;
//                 if (paraIndexInRegion === 0 && rg != null) {
//                   if (rg.property.title === curText) {
//                     continue;
//                   }
//                 }
//                 for (let i = 0, len = regionTexts.length; i < len; i++) {
//                   regionTexts[i] += curText;
//                 }
//               }
//             }
//             if (sections.length > 0) {
//               const curText = runItem.children[0] as string;
//               if (bPrevItemSectionStart === true) {
//                 // prev item is section start
//                 // compare with last section's title | placeholder
//                 const curSection = sections[sections.length - 1];
//                 if (curSection.property.title === curText) {
//                   bTitle = true;
//                   continue;
//                 } else if (curText.indexOf(curSection.property.placeholder) !== -1) {
//                   continue;
//                 }
//               }
//               for (let i = 0, len = sections.length; i < len; i++) {
//                 const curSection = sections[i];
//                 curSection.content_text += safeDecodeURIComponent(curText, versionNum);
//               }

//             }
//           } else if (typeof runItem === 'object' && runItem.tagName === 'w:br') {
//               for (let i = 0, len = sections?.length; i < len; i++) {
//                 const curSection = sections[i];
//                 curSection.content_text += '\n';
//               }
//           }
//         }
//         if (bTitle === false) {
//           bPrevItemSectionStart = false;
//         }
//       }
//     }
//   }

//   for (let i = 0, len = sections?.length; i < len; i++) {
//     const curSection = sections[i];
//     curSection.content_text += '\n';
//   }
// }

// Assume rtNode, IXmlInfoRegionContainer, IXmlInfoSection, IXmlInfoStruct, interfaces are defined
// Assume EXTERNAL_OUTER_STRUCT_TYPE, STD_START_DEFAULT, DocumentSectionType, etc. are defined
// Assume safeDecodeURIComponent function exists

/**
 * Helper function to recursively extract text from w:sdtcontent node.
 * Includes handling for w:r/w:t, w:br, and potentially nested sdt elements.
 */
function extractTextFromSdtContent(sdtContentNode: rtNode, versionNum: number, sdtProps?: IXmlInfoStruct['property']): string {
  // ... (Implementation from the previous answer remains the same) ...
   let sdtText = '';
   if (!sdtContentNode || !sdtContentNode.children) return sdtText;
   const isSignatureBox = sdtProps?.signType !== undefined;
   for (let i = 0, len = sdtContentNode.children.length; i < len; i++) {
       const sdtContentItem = sdtContentNode.children[i];
       if (typeof sdtContentItem === 'object' && sdtContentItem.tagName === 'w:r') {
           for (const runItem of sdtContentItem.children) {
               if (typeof runItem === 'object' && runItem.tagName === 'w:t') {
                   const curText = safeDecodeURIComponent(runItem.children[0] as string, versionNum);
                   if (sdtProps && (i === 0 || i === 1)) {
                       if (curText === sdtProps.title) { continue; }
                       if (sdtProps.showPlaceholder === true && curText === sdtProps.placeholder) { continue; }
                   }
                   sdtText += curText;
               } else if (typeof runItem === 'object' && runItem.tagName === 'w:br') {
                   sdtText += '\n';
               }
           }
       } else if (typeof sdtContentItem === 'object' && sdtContentItem.tagName === 'sdt') {
           let nestedSdtContentNode: rtNode | null = null;
           let nestedSdtProps: IXmlInfoStruct['property'] | undefined = undefined;
           for (const nestedSdtChild of sdtContentItem.children) {
               if (typeof nestedSdtChild === 'object' && nestedSdtChild.tagName === 'w:sdtcontent') {
                   nestedSdtContentNode = nestedSdtChild;
                   break;
               }
               // TODO: Extract nestedSdtProps if needed
           }
           if (isSignatureBox && nestedSdtContentNode) {
               for (const signItem of nestedSdtContentNode.children) {
                   if (typeof signItem === 'object' && signItem.tagName === 'w:r') {
                       for (const subRunItem of signItem.children) {
                           if (typeof subRunItem === 'object' && subRunItem.tagName === 'w:t') {
                               const subRunText = safeDecodeURIComponent(subRunItem.children[0] as string, versionNum);
                               if (sdtProps && sdtProps.signaturePlaceholder && subRunText.includes(sdtProps.signaturePlaceholder)) { continue; }
                               sdtText += subRunText;
                           }
                       }
                   }
               }
           } else if (nestedSdtContentNode) {
               sdtText += extractTextFromSdtContent(nestedSdtContentNode, versionNum, nestedSdtProps);
           }
       }
   }
   return sdtText;
}


/**
* Parses an SDT node into an IXmlInfoStruct object.
*/
function parseSdtNode(sdtNode: rtNode, versionNum: number, documentSectionType: DocumentSectionType): IXmlInfoStruct | null {
  // --- 验证输入节点 ---
  if (!sdtNode || sdtNode.tagName !== 'sdt' || !sdtNode.attributes || !sdtNode.attributes['name']) {
    console.warn('Invalid SDT node passed to parseSdtNode: Missing tag, attributes, or name.');
    return null;
  }

  const attrs = sdtNode.attributes;
  const sdtChildren = sdtNode.children || [];
  const sdtName = attrs['name']; // 获取 SDT 名称

  const externalType = +Object.keys(EXTERNAL_OUTER_STRUCT_TYPE)
    .find((key) => EXTERNAL_OUTER_STRUCT_TYPE[key] === +attrs['type']);

  // --- 初始化 SDT 对象 (包括 name 和 location) ---
  const sdt: IXmlInfoStruct = {
    name: sdtName, // 添加 name 属性
    content_text: '',
    type: externalType,
    // 使用传入的 documentSectionType 设置 location，提供默认值 1
    location: EXTERNAL_DOCUMENTSECTION_TYPE[documentSectionType] ?? 1,
    property: { // 包含所有通用默认属性
      serialNumber: STD_START_DEFAULT.serialNumber,
      placeholder: STD_START_DEFAULT.placeholder,
      helpTip: STD_START_DEFAULT.helpTip,
      deleteProtect: STD_START_DEFAULT.deleteProtect === 1, // 简化布尔转换
      editProtect: STD_START_DEFAULT.editProtect === 1,
      copyProtect: STD_START_DEFAULT.copyProtect === 1,
      showBorder: STD_START_DEFAULT.showBorder === 1,
      borderString: STD_START_DEFAULT.borderString,
      editReverse: STD_START_DEFAULT.editReverse === 1,
      backgroundColorHidden: STD_START_DEFAULT.backgroundColorHidden === 1,
      customProperty: STD_START_DEFAULT.customProperty, // 确保这是深拷贝或后续正确处理
      newControlHidden: STD_START_DEFAULT.newControlHidden === 1,
      tabJump: STD_START_DEFAULT.tabJump === 1,
      cascade: STD_START_DEFAULT.cascade,
      itemList: undefined, // 显式设置为 undefined
      showPlaceholder: STD_START_DEFAULT.showPlaceholder === 1,
      // ---> 确保这里包含了所有在 STD_START_DEFAULT 中定义的 *通用* 属性的默认值 <---
    } as any, // 使用 'as any' 或确保 IXmlInfoStruct['property'] 类型包含所有可能的属性
  };

  // --- 添加特定类型控件的默认属性 ---
  const controlType = EXTERNAL_OUTER_STRUCT_TYPE[externalType]; // 获取具体控件类型
  const props = sdt.property; // 定义 props 别名

  // 使用 controlType 进行判断，并修复语法 (用分号或换行代替逗号)
  if (controlType === NewControlType.DateTimeBox) {
    props.dateBoxFormat = STD_START_DEFAULT.dateBoxFormat;
    props.customFormat = STD_START_DEFAULT.customFormat;
    props.startDate = STD_START_DEFAULT.startDate;
    props.endDate = STD_START_DEFAULT.endDate;
    props.printSelected = STD_START_DEFAULT.printSelected === 1;
  } else if (controlType === NewControlType.SignatureBox) { // 使用 controlType
    props.signatureCount = STD_START_DEFAULT.signatureCount;
    props.preText = STD_START_DEFAULT.preText;
    props.signatureSeparator = STD_START_DEFAULT.signatureSeparator;
    props.postText = STD_START_DEFAULT.postText;
    props.signaturePlaceholder = STD_START_DEFAULT.signaturePlaceholder;
    props.signatureRatio = STD_START_DEFAULT.signatureRatio;
    props.rowHeightRestriction = STD_START_DEFAULT.rowHeightRestriction === 1;
    props.signType = STD_START_DEFAULT.signType;
    props.alwaysShow = STD_START_DEFAULT.alwaysShow;
    props.showSignBorder = STD_START_DEFAULT.showSignBorder === 1;
  } else if ([NewControlType.RadioButton, NewControlType.MultiRadio].includes(controlType)) { // 使用 controlType
    props.spaceNum = STD_START_DEFAULT.spaceNum;
    props.supportMultLines = STD_START_DEFAULT.supportMultLines === 1;
    // 添加通用于 Radio/Checkbox 的默认值
    props.checked = STD_START_DEFAULT.checked === 1;
    props.showRight = STD_START_DEFAULT.showRight === 1;
    props.printSelected = STD_START_DEFAULT.printSelected === 1; // 注意重复设置的可能性
  } else if (controlType === NewControlType.CheckBox) { // 使用 controlType
    props.label = STD_START_DEFAULT.label;
    props.labelCode = STD_START_DEFAULT.labelCode;
    // 添加通用于 Radio/Checkbox 的默认值
    props.checked = STD_START_DEFAULT.checked === 1;
    props.showRight = STD_START_DEFAULT.showRight === 1;
    props.printSelected = STD_START_DEFAULT.printSelected === 1; // 注意重复设置的可能性
  } else if ([NewControlType.ListBox, NewControlType.MultiListBox, NewControlType.Combox,
             NewControlType.MultiCombox].includes(controlType)) { // 使用 controlType
    props.retrieve = STD_START_DEFAULT.retrieve === 1;
    props.selectPrefixContent = STD_START_DEFAULT.selectPrefixContent;
    props.prefixContent = STD_START_DEFAULT.prefixContent;
    props.separator = STD_START_DEFAULT.separator;
    props.showValue = undefined; // 显式 undefined
    props.printSelected = STD_START_DEFAULT.printSelected === 1;
    props.itemList = undefined; // listItems 会在稍后从 XML 子节点解析
  } else if (controlType === NewControlType.NumberBox) { // 使用 controlType
    props.minValue = STD_START_DEFAULT.minValue;
    props.maxValue = STD_START_DEFAULT.maxValue;
    props.precision = STD_START_DEFAULT.precision;
    props.unit = STD_START_DEFAULT.unit;
    props.forceValidate = STD_START_DEFAULT.forceValidate === 1;
    props.printSelected = STD_START_DEFAULT.printSelected === 1;
  } else if (controlType === NewControlType.TextBox) { // 使用 controlType
    props.secretType = STD_START_DEFAULT.secretType;
    props.fixedLength = STD_START_DEFAULT.fixedLength;
    props.maxLength = STD_START_DEFAULT.maxLength;
    // isMustFill 和 title 可能已在通用默认值中设置，检查是否需要覆盖
    props.isMustFill = STD_START_DEFAULT.isMustFill === 1;
    props.title = STD_START_DEFAULT.title;
    props.hideHasTitle = STD_START_DEFAULT.hideHasTitle === 1;
    props.printSelected = STD_START_DEFAULT.printSelected === 1;
  } else if (controlType === NewControlType.AddressBox) { // 使用 controlType
    props.hierarchy = STD_START_DEFAULT.hierarchy;
    props.province = STD_START_DEFAULT.province;
    props.city = STD_START_DEFAULT.city;
    props.county = STD_START_DEFAULT.county;
  }

  // --- 移除重复的 Radio/Checkbox 检查块 ---
  // (上面的逻辑已经包含了 checked, showRight, printSelected 的设置)
  // if ([NewControlType.RadioButton, NewControlType.MultiRadio, NewControlType.CheckBox].includes(controlType)) { ... }

  // --- 解析 XML 子节点以覆盖默认属性 ---
  let sdtContentNode: rtNode | null = null;
  for (const sdtChild of sdtChildren) {
    if (typeof sdtChild === 'object') {
      const sdtChildName = sdtChild.tagName;
      // 使用 sdt.property (即 props) 来存储解析到的值
      switch (sdtChildName) {
        case 'w:sdtcontent': {
          sdtContentNode = sdtChild;
          break;
        }
        // --- Cases for ALL property tags ---
        // 这些 case 会用 XML 中的实际值覆盖上面设置的默认值
        case 'serialNumber': { props.serialNumber = sdtChild.children[0] as string; break; }
        case 'placeholder': { props.placeholder = safeDecodeURIComponent(sdtChild.children[0] as string, versionNum); break; }
        case 'isMustFill': { props.isMustFill = (sdtChild.children[0] === '1'); break; }
        case 'deleteProtect': { props.deleteProtect = (sdtChild.children[0] === '1'); break; }
        // ... etc. for ALL properties that can be defined in XML ...
        case 'customProperty': { /* ... parse custom property ... */
            const customProperty = {};
            for (const customPropItem of sdtChild.children) { /* ... */ }
            props.customProperty = customProperty; // Assign parsed object
            break;
        }
        case 'listItems': { /* ... parse list items ... */
            const listItemObjs = [];
            for (const listItem of sdtChild.children) { /* ... */ }
            props.itemList = listItemObjs; // Assign parsed array
            break;
        }
         case 'signaturePlaceholder': { props.signaturePlaceholder = sdtChild.children[0] as string; break; }
         case 'showPlaceholder': { props.showPlaceholder = (sdtChild.children[0] === '1'); break; }
         case 'title': { props.title = safeDecodeURIComponent(sdtChild.children[0] as string, versionNum); break; }
         // ---> 确保包含所有 XML 中可能出现的属性标签 <---
        default: break;
      }
    }
  }

  // --- 提取文本内容 ---
  if (sdtContentNode) {
    sdt.content_text = extractTextFromSdtContent(sdtContentNode, versionNum, sdt.property);
  }

  return sdt;
}


function collectStructsXmlInfoInPara(rootNode: rtNode, result: any,
                                   regionContainer: IXmlInfoRegionContainer = null,
                                   sections: IXmlInfoSection[] = [], // Param still present but unused for section tracking
                                   versionNum: number,
                                   documentSectionType: DocumentSectionType): void {

for (const paraChild of rootNode.children) {
  if (typeof paraChild === 'object') {

    // --- Handle Top-Level SDT ---
    if (paraChild.tagName === 'sdt') {
      const sdtObject = parseSdtNode(paraChild, versionNum, documentSectionType);
      if (sdtObject) { // Check if parsing was successful
        result[sdtObject.name!] = sdtObject; // Add to main result, using non-null assertion for name

        // Add text to active regions if needed
        if (regionContainer != null && sdtObject.content_text) {
           const regionTexts = regionContainer.regionTexts;
           if (regionTexts.length > 0) {
              for (let j = 0, leng = regionTexts.length; j < leng; j++) {
                 regionTexts[j] += sdtObject.content_text;
              }
           }
        }
      }
    }
    // --- Handle Section ---
    else if (paraChild.tagName === 'section') {
      const attrs = paraChild.attributes;
      if (!attrs || !attrs['name']) {
          console.warn('Section encountered without a name attribute.');
          continue; // Skip section if it has no name
      }
      const sectionName = attrs['name'];
      const sectionChildren = paraChild.children || [];

      const section: IXmlInfoSection = {
        content_text: '',
        type: 13,
        location: 1,
        children: {}, // 初始化children属性为空对象
        property: {
          serialNumber: STD_START_DEFAULT.serialNumber,
          placeholder: STD_START_DEFAULT.placeholder,
          isMustFill: STD_START_DEFAULT.isMustFill === 1 ? true : false,
          deleteProtect: STD_START_DEFAULT.deleteProtect === 1 ? true : false,
          editProtect: STD_START_DEFAULT.editProtect === 1 ? true : false,
          copyProtect: STD_START_DEFAULT.copyProtect === 1 ? true : false,
          showBorder: STD_START_DEFAULT.showBorder === 1 ? true : false,
          borderString: STD_START_DEFAULT.borderString,
          editReverse: STD_START_DEFAULT.editReverse === 1 ? true : false,
          backgroundColorHidden: STD_START_DEFAULT.backgroundColorHidden === 1 ? true : false,
          customProperty: STD_START_DEFAULT.customProperty,
          tabJump: STD_START_DEFAULT.tabJump === 1 ? true : false,
          newControlHidden: STD_START_DEFAULT.newControlHidden === 1 ? true : false,
          helpTip: STD_START_DEFAULT.helpTip, // 提示符
          showPlaceholder: STD_START_DEFAULT.showPlaceholder === 1 ? true : false,
          secretType: STD_START_DEFAULT.secretType, // both in text and value struct
          fixedLength: STD_START_DEFAULT.fixedLength,
          maxLength: STD_START_DEFAULT.maxLength,
          title: STD_START_DEFAULT.title,
        }
      };

      // Determine type
      const externalType = +Object.keys(EXTERNAL_OUTER_STRUCT_TYPE)
          .find((key) => EXTERNAL_OUTER_STRUCT_TYPE[key] === +attrs['type']);
      section.type = externalType;

      let currentSectionText = ''; // Accumulator for section text
      let seContentNode: rtNode | null = null;

      // Parse section properties AND find w:secontent
      for (const sectionChild of sectionChildren) {
          if (typeof sectionChild === 'object') {
              const sectionChildName = sectionChild.tagName;
              const sectionProps = section.property;
              switch (sectionChildName) {
                  case 'w:secontent': {
                      seContentNode = sectionChild; // Store content node
                      break; // Found content, continue parsing other props if needed
                  }
                case 'serialNumber': {
                 sectionProps.serialNumber = sectionChild.children[0] as string;
                 break;
                 }
                 case 'placeholder': {
                 sectionProps.placeholder = safeDecodeURIComponent(sectionChild.children[0] as string, versionNum);
                 break;
                 }
                 case 'isMustFill': {
                 sectionProps['isMustFill'] = (sectionChild.children[0] === '1') ? true : false;
                 break;
                 }
                 case 'deleteProtect': {
                 sectionProps['deleteProtect'] = (sectionChild.children[0] === '1') ? true : false;
                 break;
                 }
                 case 'editProtect': {
                 sectionProps['editProtect'] = (sectionChild.children[0] === '1') ? true : false;
                 break;
                 }
                 case 'copyProtect': {
                 sectionProps['copyProtect'] = (sectionChild.children[0] === '1') ? true : false;
                 break;
                 }
                 case 'showBorder': {
                 sectionProps['showBorder'] = (sectionChild.children[0] === '1') ? true : false;
                 break;
                 }
                 case 'borderString': {
                 sectionProps.borderString = sectionChild.children[0] as string;
                 break;
                 }
                 case 'editReverse': {
                 sectionProps['editReverse'] = (sectionChild.children[0] === '1') ? true : false;
                 break;
                 }
                 case 'backgroundColorHidden': {
                 sectionProps['backgroundColorHidden'] = (sectionChild.children[0] === '1') ? true : false;
                 break;
                 }
                 case 'customProperty': {
                 // <customProperty>
                 //   <first type="1">111</first>
                 //   <second type="3">222</second>
                 //   <third type="2">true</third>
                 // </customProperty>
                 const customProperty = {};
                 for (const customPropItem of sectionChild.children) {
                     if (typeof customPropItem === 'object') {
                     const type: DataType = +customPropItem.attributes['type'];
                     if (type === DataType.String) {
                         customProperty[customPropItem.tagName] =
                         safeDecodeURIComponent(customPropItem.children[0]as string, versionNum);
                     } else if (type === DataType.Number) {
                         customProperty[customPropItem.tagName] = +customPropItem.children[0];
                     } else if (type === DataType.Boolean) {
                         customProperty[customPropItem.tagName] = (customPropItem.children[0] === 'true') ? true : false;
                     }
                     }
                 }
                 sectionProps['customProperty'] = customProperty;
                 break;
                 }
                 case 'tabJump': {
                 sectionProps['tabJump'] = (sectionChild.children[0] === '1') ? true : false;
                 break;
                 }
                 case 'newControlHidden': {
                 sectionProps['newControlHidden'] = (sectionChild.children[0] === '1') ? true : false;
                 break;
                 }
                 case 'helpTip': {
                 sectionProps.helpTip = sectionChild.children[0] as string;
                 break;
                 }
                 // ui only
                 case 'showPlaceholder': {
                 if (sectionChild.children[0] === '1') {
                     sectionProps['showPlaceholder'] = true;
                 }
                 break;
                 }
                 // text struct
                 case 'secretType': { // shared in text and value struct
                 sectionProps['secretType'] = +sectionChild.children[0];
                 break;
                 }
                 case 'fixedLength': {
                 sectionProps['fixedLength'] = +sectionChild.children[0];
                 break;
                 }
                 case 'maxLength': {
                 sectionProps['maxLength'] = +sectionChild.children[0];
                 break;
                 }
                 case 'title': {
                 sectionProps.title = safeDecodeURIComponent(sectionChild.children[0] as string, versionNum);
                 break;
                 }
                 default: break;
             }
          }
      }

      // Process w:secontent children AFTER section properties are parsed
      if (seContentNode && seContentNode.children) {
          for (const seContentChild of seContentNode.children) {
              if (typeof seContentChild === 'object') {
                  // --- Handle <w:r> inside section ---
                  if (seContentChild.tagName === 'w:r') {
                      let runText = '';
                      let runHasBreak = false;
                      for (const runItem of seContentChild.children) {
                          if (typeof runItem === 'object' && runItem.tagName === 'w:t') {
                              runText += safeDecodeURIComponent(runItem.children[0] as string, versionNum);
                          } else if (typeof runItem === 'object' && runItem.tagName === 'w:br') {
                              runHasBreak = true;
                          }
                      }
                      currentSectionText += runText; // Append text to accumulator
                      if (runHasBreak) {
                          currentSectionText += '\n'; // Append newline if break found
                      }
                  }
                  // --- Handle <sdt> inside section ---
                  else if (seContentChild.tagName === 'sdt') {
                      // Parse the nested SDT fully
                      const nestedSdtObject = parseSdtNode(seContentChild, versionNum, documentSectionType);
                      if (nestedSdtObject) { // Check parse success
                           // Store the nested SDT object under the section's children
                           section.children![nestedSdtObject.name!] = nestedSdtObject;

                           // Append the nested SDT's text to the section's text accumulator
                           if (nestedSdtObject.content_text) {
                               currentSectionText += nestedSdtObject.content_text;
                               // Optional: add separator? currentSectionText += '\n';
                           }
                      }
                  }
                  // Handle other potential direct children of w:secontent if needed
              }
          }
      }

      // Assign the collected text to the section object
      section.content_text = currentSectionText;

      // 根据上下文决定section的存储位置
      if (regionContainer && regionContainer.rg) {
        // 如果在region内部，将section添加到region的children中
        if (!regionContainer.rg.children) {
          regionContainer.rg.children = {};
        }
        regionContainer.rg.children[sectionName] = section;
        
        // 关键添加：将section的content_text添加到regionTexts
        if (regionContainer.regionTexts && regionContainer.regionTexts.length > 0 && section.content_text) {
          for (let i = 0; i < regionContainer.regionTexts.length; i++) {
            regionContainer.regionTexts[i] += section.content_text;
          }
        }
      } else {
        // 如果不在region内部，添加到顶层
        result[sectionName] = section;
      }
    }
    // --- Handle Top-Level <w:r> (Not inside section/sdt) ---
    else if (paraChild.tagName === 'w:r') {
       // This part remains mostly the same as the previous answer,
       // primarily for adding text to regions if needed.
       let runText = '';
       let containsBreak = false;
       for (const runItem of paraChild.children) { /* ... extract text/check break ... */
          if (typeof runItem === 'object') {
               if (runItem.tagName === 'w:t') { runText += safeDecodeURIComponent(runItem.children[0] as string, versionNum); }
               else if (runItem.tagName === 'w:br') { containsBreak = true; }
          }
       }
       if (regionContainer != null) { /* ... add runText and break to regionTexts ... */
          const regionTexts = regionContainer.regionTexts;
          if (regionTexts.length > 0) {
              const { paraIndexInRegion, rg } = regionContainer;
               if (!(paraIndexInRegion === 0 && rg != null && rg.property.title === runText)) { // Skip title logic
                   for (let i = 0, len = regionTexts.length; i < len; i++) { if (runText) regionTexts[i] += runText; if (containsBreak) regionTexts[i] += '\n';}
               } else if (containsBreak) { // Still add break even if title is skipped
                   for (let i = 0, len = regionTexts.length; i < len; i++) { regionTexts[i] += '\n'; }
               }
          }
       }
    }
    // Handle other top-level paragraph children if necessary
  } else if (typeof paraChild === 'string') {
       // Handle plain text nodes if they appear directly under the paragraph
       if (regionContainer != null) { /* ... add text to regionTexts ... */ }
  }
}
// No final loop needed here anymore
}

function collectStructsXmlInfoInTable(rootNode: rtNode, result: any, regionTexts: string[] = [],
                                      sections: IXmlInfoSection[] = [], versionNum: number,
                                      documentSectionType: DocumentSectionType, sJson?: string): void {
  // console.log(rootNode)

  let sJsonObj = null;
  let needTable = false;
  try {
    if (sJson != null) {
      sJsonObj = JSON.parse(sJson);
    }
  } catch (error) {
    // tslint:disable-next-line: no-console
    console.log('sJson format is incorrect');
  }

  if (sJsonObj != null && sJsonObj.needTable === 1) {
    needTable = true;
  }
  // console.log(needTable)

  const tableInfo = {name: undefined, CustomProperty: {}};
  if (needTable === true) {
    const tableName = rootNode.attributes['name'];
    // console.log(tableName)

    tableInfo.name = tableName;

    // put in tableInfo, can add first modify later
    if (result.table == null) {
      result.table = [];
    }
    result.table.push(tableInfo);
  }

  for (const tableItem of rootNode.children) {

    if (needTable === true) {
      // get custom props
      if (typeof tableItem === 'object' && tableItem.tagName === 'w:tblPr') {
        for (const tblProp of tableItem.children) {
          if (typeof tblProp === 'object' && tblProp.tagName === 'customProperty') {
            const customProperties = tblProp.children;
            if (customProperties.length > 0) { // usually if in this block, length must be > 0
              const customProperty = fillCustomPropsXmlInfo(customProperties, versionNum);
              // console.log(customProperty)
              tableInfo.CustomProperty = customProperty;
            }
          }
        }
      }
    }

    if (typeof tableItem === 'object' && tableItem.tagName === 'w:tr') {
      for (const trItem of tableItem.children) {
        if (typeof trItem === 'object' && trItem.tagName === 'w:tc') {
          for (const tcItem of trItem.children) {
            if (typeof tcItem === 'object' && tcItem.tagName === 'w:p') {
              let regionContainer: IXmlInfoRegionContainer = null;
              if (regionTexts.length > 0) {
                regionContainer = {
                  regionTexts,
                  rg: null,
                  paraIndexInRegion: -1,
                };
              }
              collectStructsXmlInfoInPara(tcItem, result, regionContainer, sections, versionNum, documentSectionType);
            }
          }
        }
      }
    }
  }

}

function collectStructsXmlInfoInRegion(rootNode: rtNode, result: any, regionTexts: string[] = [],
                                       sections: IXmlInfoSection[] = [], versionNum: number, sJson?: string): void {
  // write sdt into json
  const attrs = rootNode.attributes;

  // default
  const rg: IXmlInfoRegion = {
    content_text: '',
    type: 14,
    location: 1,
    children: {}, // 添加children属性初始化，用于存储嵌套的section
    property: {
      serialNumber: REGION_PROPS_DEFAULT.serialNumber,
      deleteProtect: REGION_PROPS_DEFAULT.bCanntDelete === 1 ? true : false,
      editProtect: REGION_PROPS_DEFAULT.bCanntEdit === 1 ? true : false,
      hidden: REGION_PROPS_DEFAULT.bHidden === 1 ? true : false,
      editReverse: REGION_PROPS_DEFAULT.bCanntEdit === 1 ? true : false,
      title: REGION_PROPS_DEFAULT.title,
      showTitle: REGION_PROPS_DEFAULT.bShowTitle === 1 ? true : false,
      customProperty: REGION_PROPS_DEFAULT.customProperty,
      maxLength: REGION_PROPS_DEFAULT.maxLength,
    },
  };
  const regionText = '';
  // const curTegionTexts = (regionTexts != null) ? regionTexts : [];
  regionTexts.push(regionText);

  const externalType =  +Object.keys(EXTERNAL_OUTER_STRUCT_TYPE)
          .find((key) => EXTERNAL_OUTER_STRUCT_TYPE[key] === +attrs['type']);
  rg.type = externalType;

  // connect rg
  result[attrs['name']] = rg;

  let paraIndex = -1;
  for (const regionItem of rootNode.children) {
    if (typeof regionItem === 'object') {
      if (regionItem.tagName === 'rgPr') {
        const rgProps = rg.property;
        for (const rgPropItem of regionItem.children) {
          if (typeof rgPropItem === 'object') {
            switch (rgPropItem.tagName) {
              case 'deleteProtect': {
                rgProps['deleteProtect'] = (rgPropItem.children[0] === '1') ? true : false;
                break;
              }

              case 'editProtect': {
                rgProps['editProtect'] = (rgPropItem.children[0] === '1') ? true : false;
                break;
              }

              case 'hidden': {
                rgProps['hidden'] = (rgPropItem.children[0] === '1') ? true : false;
                break;
              }

              case 'editReverse': {
                rgProps['editReverse'] = (rgPropItem.children[0] === '1') ? true : false;
                break;
              }

              case 'title': {
                rgProps['title'] = safeDecodeURIComponent(rgPropItem.children[0] as string, versionNum);
                break;
              }

              case 'showTitle': {
                rgProps['showTitle'] = (rgPropItem.children[0] === '1') ? true : false;
                break;
              }

              case 'beginPos': {
                //
                break;
              }

              case 'endPos': {
                //
                break;
              }

              case 'serialNumber': {
                rgProps.serialNumber = rgPropItem.children[0] as string;
                break;
              }

              case 'maxLength': {
                rgProps.maxLength = +rgPropItem.children[0];
                break;
              }

              case 'customProperty': {
                // <customProperty>
                //   <first type="1">111</first>
                //   <second type="3">222</second>
                //   <third type="2">true</third>
                // </customProperty>
                const customProperty = {};
                for (const customPropItem of rgPropItem.children) {
                  if (typeof customPropItem === 'object') {
                    const type: DataType = +customPropItem.attributes['type'];
                    if (type === DataType.String) {
                      // tslint:disable-next-line: max-line-length
                      customProperty[customPropItem.tagName] = safeDecodeURIComponent(customPropItem.children[0] as string, versionNum);
                    } else if (type === DataType.Number) {
                      customProperty[customPropItem.tagName] = +customPropItem.children[0];
                    } else if (type === DataType.Boolean) {
                      customProperty[customPropItem.tagName] = (customPropItem.children[0] === 'true') ? true : false;
                    }
                  }
                }
                rgProps['customProperty'] = customProperty;
                break;
              }
              default: {
                break;
              }
            }
          }
        }

      } else if (regionItem.tagName === 'w:p') {
        paraIndex++; // first is 0
        // the object is for checking region title
        // only para case will need this
        const regionContainer = {
          regionTexts,
          rg,
          paraIndexInRegion: paraIndex,
        };
        collectStructsXmlInfoInPara(regionItem, result, regionContainer, sections, versionNum,
          DocumentSectionType.Document);
      } else if (regionItem.tagName === 'w:tbl') {
        collectStructsXmlInfoInTable(regionItem, result, regionTexts, sections, versionNum,
          DocumentSectionType.Document, sJson);
      } else if (regionItem.tagName === 'rg') {
        collectStructsXmlInfoInRegion(regionItem, result, regionTexts, sections, versionNum, sJson);
      }
    }
  }

  // add content_text
  let curRegionText = '';
  if (regionTexts.length > 0) {
    curRegionText = regionTexts.splice(regionTexts.length - 1, 1)[0];
  }
  rg.content_text = safeDecodeURIComponent(curRegionText, versionNum);

  // add ref (if getStructsXmlInfoByParament's order, should add here)
  // result[attrs['name']] = rg;
}

function fillCustomPropsXmlInfo(customProperties: any[], versionNum: number): any {
  const customProperty = {};
  for (const customPropItem of customProperties) {
    if (typeof customPropItem === 'object') {
      // tslint:disable-next-line: no-shadowed-variable
      const type: DataType = +customPropItem.attributes['type'];
      if (type === DataType.String) {
        const v = customPropItem.children[0] as string || '';
        customProperty[customPropItem.tagName] =
        safeDecodeURIComponent(v, versionNum);
      } else if (type === DataType.Number) {
        customProperty[customPropItem.tagName] = +customPropItem.children[0];
      } else if (type === DataType.Boolean) {
        if (customPropItem.children[0] === undefined) {
          customProperty[customPropItem.tagName] = '';
        } else {
          customProperty[customPropItem.tagName] = (customPropItem.children[0] === 'true') ? true : false;
        }
      }
    }
  }

  return customProperty;
}

export function safeDecodeURIComponent(value: string, documentVersion?: number): string {
  let val = value;
  if (val == null || val.length === 0) {
    return val;
  }
  if (typeof val === 'object') {
    // tslint:disable-next-line: no-console
    console.warn('object is passed to unescape');
    // error tolerant
    val = '';
  }

  // if (document != null && document.getDocumentVersion() === FILE_HEADER_VERSION) {
  if (documentVersion === FILE_HEADER_VERSION || documentVersion === FILE_HEADER_VERSION2) {
    val = unescapeXML(val);
  } else {
    // let warnMsg = 'old document version detected';
    // if (document != null) {
    //   warnMsg += ': ' + document.getDocumentVersion();
    // }
    // // tslint:disable-next-line: no-console
    // console.warn(warnMsg);

    try {
      val = decodeURIComponent(val);
    } catch (error) {
      // tslint:disable-next-line: no-console
      // console.log(error);
      // tslint:disable-next-line: no-console
      // console.log(value);
      val = value;
    }
  }

  return val;
}

export function customEncodeURIComponent(value: string): string {
  // also deal with '
  // tslint:disable-next-line: newline-per-chained-call
  // return encodeURIComponent(value).replace(/'/g, '%27');
  // const val = escapeXML(value);
  const val = value; // jszip already take care of encoding
  return val;
}

// export function isMacOs(): boolean {
//   const result = (navigator.userAgent.toLowerCase()
//     .indexOf('mac') > -1);

//   return result;
// }

export function checkFontAvailability(font: string): boolean {
  // eg: '16px 宋体'
  return (document as any).fonts.check(font);
}

export function getRegWord(text: string): string {
  if (!text) {
    return '';
  }
  return text.replace(/\(|\)|\[|\]|\{|\}|\/|\\|\*|\+|\.|\?|\^|\$|\|/g, (match) => {
    return '\\' + match;
  });
}

export function getDefaultFont(document: Document): TextProperty {

  let textProps: any = {}; // corresponds to textProperty();
  if (document != null) {
    textProps = new TextProperty();
    const defaultFont = document.getDefaultFont();
    textProps.fontFamily = defaultFont.fontFamily;
    textProps.fontSize = defaultFont.fontSize;
  }

  return textProps;
}

export function resetDocumentDefaultFont(logicDocument: Document): void {
  // tslint:disable-next-line: no-console
  // console.log('reset document default font');
  if (logicDocument != null) {
    const defaultFont = logicDocument.getDefaultFont();
    defaultFont.fontFamily = DEFAULT_TEXT_PROPERTY.fontFamily;
    defaultFont.fontSize = DEFAULT_TEXT_PROPERTY.fontSize;
  }
}

export function skipEscapeString(content: string): string {
  return (content || '').replace(/[\b\f\v\0\r\t]/g, '');
}

// function resetDefaultFont(): void {
//   // tslint:disable-next-line: no-console
//   console.log('reset default font');
//   DEFAULT_FONT.fontFamily = DEFAULT_TEXT_PROPERTY.fontFamily;
//   DEFAULT_FONT.fontSize = DEFAULT_TEXT_PROPERTY.fontSize;
// }

export function fileStartRecord(recordContent: IRecordContent, bInsertFile: boolean): void {
  // const status = getDocumentCoreRecordReplayState();
  const recorder = getDocumentCoreRecorder();
  recorder.record({external: true, method: 'readFile', args: [{content: recordContent, bInsertFile}] });
}

function traverseContentNodes(rootNodes: (string | number | rtNode)[], result: any, versionNum: number,
                              documentSectionType: DocumentSectionType, sJson?: string): void {
  const sections = []; // this shit can cross para, have table, self recursive indefi.
  for (const rootNode of rootNodes) {
    if (typeof rootNode === 'object') {
      if (rootNode.tagName === 'w:p') {
        collectStructsXmlInfoInPara(rootNode, result, null, sections, versionNum, documentSectionType);
      } else if (rootNode.tagName === 'w:tbl') {
        collectStructsXmlInfoInTable(rootNode, result, [], sections, versionNum, documentSectionType, sJson);
      } else if (rootNode.tagName === 'rg') {
        collectStructsXmlInfoInRegion(rootNode, result, [], sections, versionNum, sJson);
      } else {
        //
      }
    }
  }
}

export function getPagePadding(documentCore: any): {top: number, left: number} {
  const obj = {
      top: 0,
      left: 0,
  };

  const viewMode = documentCore.getViewMode();
  if (viewMode === ViewModeType.WebView) {
      const margin = documentCore.getPagePositionInfo(0);
      obj.top = margin.y;
  } else if (viewMode === ViewModeType.CompactView) {
      const page = documentCore.getPageProperty();
      obj.top = 0; // page.paddingTop;
      obj.left = Math.max(page.paddingLeft - getPagePadding.CompactWidth, 0);
  }

  return obj;
}

getPagePadding.PaddingLeft = 0;
getPagePadding.CompactWidth = 12;

export function genarateBarcode(parameters: any): string {
  const options = {
      xmlDocument: document,
      width: 2,
      height: !parameters.height ? 50 : parameters.height,
      textAlign: parameters.textAlign,
      displayValue: parameters.bUse,
      fontSize: 16,
      valid: parameters.valid ? parameters.valid : function() { return ; },
  };

  const svgNode = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
  const content = parameters.content ? parameters.content : '';
  JsBarcode(svgNode, content, options);

  return parameters.drawingObjects.convertSVGToImageString(svgNode);
}

/**
 * 判断是否使用apo文件的json
 * @param apoJson apo文件中保存的护理表格json
 * @param externalJson 接口传入的护理表格json
 * @returns
 */
export function compareNISJsonEqual(apoJson: any, externalJson: any): boolean {
  if (!externalJson) {
    return true;
  }

  const length1 = JSON.stringify(apoJson).length;
  const length2 = JSON.stringify(externalJson).length;

  // 字符长度
  if ((0 !== length1 && 0 === length2) ||
      (0 === length1 && 0 === length2)) {
    return true;
  } else if (length1 !== length2) {
    return false;
  }

  // 护理表格数目
  if (apoJson.length !== externalJson.length) {
    return false;
  }

  // 字符长度相同且 !== 0
  let flag = false;
  for (let index = 0, length = apoJson.length; index < length; index++) {
    const element = apoJson[index];
    const tableName = element['tableID'];
    const row1 = element['rows'];

    const element2 = externalJson[index];
    const row2 = element2['rows'];
    if (tableName === element2['tableID'] &&
        Object.keys(row1).length === Object.keys(row2).length) {
        // 遍历
        for (let rowIndex = 0, rowLength = row1.length; rowIndex < rowLength; rowIndex++) {
          const row = row1[rowIndex];

          for (const key in row) {
            for (let rowIndex2 = 0, rowLength2 = row2.length; rowIndex2 < rowLength2; rowIndex2++) {
              const row_2 = row2[rowIndex2];
              if (row_2.hasOwnProperty(key)) {
                if ('rowID' !== key && row[key] !== row_2[key]) {
                  return false;
                }
                flag = true;
              }
            }
          }

          if (!flag) {
            return flag;
          }
        }
    } else {
      flag = false;
      break;
    }

    if (!flag) {
      return flag;
    }
  }

  return flag;
}

export function parseFromString(text: string): string {
  if (!text) {
    return '';
  }

  return text.replace(/(&quot;)|(&apos;)|(&amp;)|(&lt;)|(&gt;)/g, (all, text1, text2, text3, t4, t5) => {
    if (text1) {
      return '"';
    }
    if (text2) {
      return '\'';
    }
    if (text3) {
      return '&';
    }
    if (t4) {
      return '<';
    }
    if (t5) {
      return '>';
    }
    return '';
  });
}

/**
 * h获取当前日期时间
 * @param date
 * @returns
 */
export function getCurTime(date?: Date): string {
  date = date || new Date();
  return (date.getFullYear() + '/' + (date.getMonth() + 1) + '/' + date.getDate() + ' '
        + date.getHours() + ':' + date.getMinutes() + ':' + date.getSeconds());
}

/**
 * 读取文档时，编辑器版本不支持
 */
export function readerFileEditorVersionError(document: Document): void {
  if (0 >= document.content.length) {
    document.testDocument(undefined, false);
  }
  document.setLoadFile(false);

  message.error(ErrorMessages.XmlError);
}

/**
 * 生成二维码
 * @param parameters
 * @returns
 */
export function genarateQRCode(parameters: any): string {
  let source = '';
  QRCode.toString(parameters.content, {
    errorCorrectionLevel: parameters.errorCL, // 'H',
    type: 'svg',
    // quality: 0.3,
    margin: 0,
    // color: {
    //     dark:"#000000",
    //     light:"#FFFFFF"
    // }
  }, parameters.callback || function(err, url) {
    if (err) {
        console.log(err);
    }
    source = url;
  });

  if (!parameters.callback) {
    return parameters.drawingObjects.convertSVGToImageString(source);
  }
}
