import * as React from 'react';
import '../../style/pagenum.less';
// tslint:disable-next-line: max-line-length
import { INewControlProperty, IPageNumProperty, PageNumType } from '../../../../common/commonDefines';
import Dialog from '../../ui/Dialog';
import Button from '../../ui/Button';
import Radio from '../../ui/Radio';
import { message } from '../../../../common/Message';
import { IFRAME_MANAGER } from '../../../../common/IframeManager';

interface IDialogProps {
    documentCore: any;
    visible: boolean;
    id: string;
    property?: INewControlProperty;
    close: (name: string | number, bRefresh?: boolean) => void;
}

interface IState {
    bRefresh: boolean;
}

export default class NewPageNum extends React.Component<IDialogProps, IState> {
    private pageNumProperty: IPageNumProperty;
    private visible: any;
    private datas: any[];
    private value: number;
    private defaultPageNumString = '第[页码]页/总[页数]页';

    constructor(props: any) {
        super(props);
        this.state = {
            bRefresh: false,
        };
        this.visible = this.props.visible;
        this.value = 0;
        this.datas = [
            {key: '页码', value: 0},
            {key: '总页数', value: 1},
            {key: '页码字符串', value: 2},
        ];
        this.pageNumProperty = {
            pageNumType: PageNumType.CurPage,
            pageNumString: this.defaultPageNumString,
            startIndex: 1, // 起始页码
        };
    }

    public render(): any {
        return (
            <Dialog
                visible={this.visible}
                width={350}
                open={this.open}
                title='插入页码'
                footer={this.renderFooter()}
                id={this.props.id}
            >
                <div className='pagenum-box'>
                    <div className='pagenum-type'>
                        <Radio value={this.value} data={this.datas} name='pageNumType' onChange={this.onChange} />
                        <input
                            className='pagenum-string'
                            value={this.pageNumProperty.pageNumString}
                            onChange={this.pageNumStringChange}
                            name='pageNumString'
                            onBlur={this.pageNumStringBlur}
                            disabled={this.value === PageNumType.PageNumString ? false : true}
                        />
                    </div>
                    <div className='editor-line'>
                        <label>起始页码:</label>
                        <input
                            className='pagenum-startindex'
                            value={this.pageNumProperty.startIndex}
                            onChange={this.numChange.bind(this, 'startIndex')}
                            name='startIndex'
                            onBlur={this.onBlur}
                            type='number'
                            min={1}
                            step={1}
                            disabled={this.value === PageNumType.TotalPages ? true : false}
                        />
                    </div>
                </div>
            </Dialog>
        );
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        this.visible = nextProps.visible;
        if (this.visible === true) {
            // is there any reason to support pagenum modification?
            this.pageNumProperty = {
                pageNumType: PageNumType.CurPage,
                pageNumString: this.defaultPageNumString,
                startIndex: 1,
            };
            this.value = PageNumType.CurPage;
        }
    }

    private renderFooter(): any { // return dom, no () => {}
        return (
            <span>
                <Button onClick={this.close}>取消</Button>
                <Button type='primary' onClick={this.confirm}>确认</Button>
            </span>
        );
    }

    private open = () => {
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private close = (bRefresh?: boolean): void => {
        this.props.close(this.props.id, bRefresh);
        this.visible = false;
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private confirm = (): void => {
        const documentCore = this.props.documentCore;
        if (!this.isValidPageNumString(this.pageNumProperty.pageNumString)) {
            IFRAME_MANAGER.setDocId(documentCore.getCurrentId());
            message.error(`页码字符串格式错误`);
            this.pageNumProperty.pageNumString = this.defaultPageNumString;
            this.setState({bRefresh: !this.state.bRefresh});
            return ;
        }
        // console.log(this.headerFooterProperty);
        // const logicDocument: Document = this.props.documentCore.getDocument();
        // const {pageNumType, pageNumString, startIndex} = this.pageNumProperty;
        // console.log(this.pageNumProperty)
        documentCore.addPageNum(this.pageNumProperty);

        this.close(true);
    }

    private onChange = (value: any, name: string): void => {
        // console.log(value, name)
        this.pageNumProperty[name] = value;
        this.value = value;
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private onBlur = (): void => {
        //
    }

    private numChange(name: string, e: any): void {
        const target = e.target;
        // let value = +target.value;
        let value = parseInt(target.value, 10);
        if (isNaN(value) === true) {
            return ;
        }
        // console.log(name, value)
        if (value < 1) {
            value = 1;
        }

        this.pageNumProperty[name] = value;
        // console.log(this.headerFooterProperty)
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private pageNumStringChange = (e) => {
        this.pageNumProperty.pageNumString = e.target.value;
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private pageNumStringBlur = (e) => {
        const input = e.target;
        const pageNumString = input.value;
        // console.log(input);
        if (pageNumString != null) {
            if (this.isValidPageNumString(pageNumString)) {
                input.classList.remove('warning');
            } else {
                input.classList.add('warning');
            }
        }
    }

    private isValidPageNumString(pageNumString: string): boolean {

        if (pageNumString.length < 1) {
            return false;
        }

        // 1. [页数]或者[页码] 超过1 非法

        const pageNumChars = '[页码]';
        const totalPageChars = '[页数]';

        const pageNumCharsStartIndex = pageNumString.indexOf(pageNumChars);
        const pageNumCharsEndIndex = pageNumString.lastIndexOf(pageNumChars);
        const totalPageCharsStartIndex = pageNumString.indexOf(totalPageChars);
        const totalPageCharsEndIndex = pageNumString.lastIndexOf(totalPageChars);
        // const separatorIndex = pageNumString.indexOf('/');
        if (pageNumCharsStartIndex !== -1) {
            if (pageNumCharsStartIndex !== pageNumCharsEndIndex) {
                return false;
            }
        }
        if (totalPageCharsStartIndex !== -1) {
            if (totalPageCharsStartIndex !== totalPageCharsEndIndex) {
                return false;
            }
        }

        return true;
    }

}
