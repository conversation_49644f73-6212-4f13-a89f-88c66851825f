import gbk from '../../../../resources/gbk';
export default class Codepage {
    private _data: any;
    constructor() {
        const data = {};
        gbk.forEach((font) => {
            data[font.G] = font.U;
        });
        this._data = data;
    }

    /**
     * 根据机器内码返回对应的Unicode码
     * @param code gbk码
     */
    public getCodepage(code: string): number {
        if (!code) {
            return;
        }
        return parseInt(this._data[code.toLowerCase()], 10);
    }
}
