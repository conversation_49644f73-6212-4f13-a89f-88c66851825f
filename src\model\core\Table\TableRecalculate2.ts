import { RecalcResultType } from '../Document';
import { TablePage, TableHeaderPage } from '../TablePage';
import { VerticalMergeType } from './TableCellProperty';
import { TableCell } from './TableCell';
import { TableRowLineRule, TableRowInfo } from './TableRowProperty';
import { AlignType, DocumentBorder } from '../Style';
import { TableBorderLineStyle, TableLayoutType, TableWidthType,
    TABLE_RECALC_TOL } from '../TableProperty';
import { VertAlignType } from '../../../common/commonDefines';
import { TableRecalculate } from './TableRecalculate';
import { TableBase } from '../TableBase';
import { getPxForMM } from '../util';

export class TableRecalculate2 extends TableRecalculate {
    constructor( table: TableBase ) {
        super(table);
    }

    public recalculatePage(curPage: number = 0, bHidden: boolean): RecalcResultType {

        if ( bHidden ) {
            this.recalculateAllRows(curPage);
            return RecalcResultType.RecalResultNextElement;
        }

        if ( 0 === curPage ) {
            this.recalculateGrid();
            this.recalculateBorders();
            this.recalculateHeader();
        }

        this.recalculatePageXY(curPage);

        // if (true !== this.private_RecalculateCheckPageColumnBreak(PageIndex))
        //     return recalcresult_NextPage | recalcresultflags_Column;

        this.recalculatePositionX(curPage);

        const result = this.recalculate(curPage, false);
        if ( result & RecalcResultType.RecalResultCurPage ) {
            return result;
        }

        this.recalculatePositionY(curPage);

        if ( result & RecalcResultType.RecalResultNextElement ) {
            this.recalcInfo.reset(false);
        }

        return result;
    }

    /**
     * 计算表格单元格宽度
     */
    public recalculateGrid(): void {
        const recalcMinUnit = 1;
        const maxWordWidth = getPxForMM(558.7);
        const table = this.table;

        if ( 0 >= table.content.length ) {
            return;
        }

        let tempSum = 0;
        let sumGrid: number[] = [];
        const grid = table.tableGrid;
        sumGrid[-1] = 0;

        // 统计每列的宽度位置
        for (let index = 0; index < grid.length; index++) {
            tempSum += grid[index];
            sumGrid[index] = tempSum;
        }

        const tableW = 0;
        const minWidth = table.getTableMinWidth();

        let curGridCol = 0;
        const length = table.content.length;
        // const cellGridLines = this.table.property.cellGirdLines;
        // 处理单元格拆分，合并的情况
        for (let index = 0; index < length; index++) {
            const row = table.content[index];
            row.setIndex(index);
            
            const beforeInfo = row.getBefore();
            curGridCol = beforeInfo.gridBefore;
            // const bHeader = row.isTableHeader();
            if ( 0 < curGridCol && sumGrid[curGridCol - 1] < beforeInfo.widthBefore.width ) {
                const tempDiff = beforeInfo.widthBefore.width - sumGrid[curGridCol - 1];

                for (let index2 = curGridCol - 1, length2 = sumGrid.length; index2 < length2; index2++) {
                    sumGrid[index2] += tempDiff;
                }
            }

            const cellsCount = row.getCellsCount();

            for (let cellIndex = 0; cellIndex < cellsCount; cellIndex++) {
                const cell = row.getCell(cellIndex);
                const cellW = cell.getCellWidth();
                const gridSpan = cell.getGridSpan();

                cell.setIndex(cellIndex);
                // if (!bHeader && cellGridLines) { // 护理网格线进行处理
                //     const girdLines = cellGridLines[cellIndex];
                //     if (girdLines) {
                //         cell.setGridLine(girdLines);
                //     }
                // } else {
                //     cell.setGridLine(null);
                // }
                if ( sumGrid.length < curGridCol + gridSpan - 1 ) {
                    for (let index2 = sumGrid.length; index2 <= curGridCol + gridSpan - 1; index2++) {
                        sumGrid[index2] = sumGrid[index2 - 1] + 20;
                    }
                }

                if ( TableWidthType.Auto !== cellW.type ) {
                    let cellWidth = 0;
                    if ( TableWidthType.Pencent === cellW.type ) {
                        cellWidth = cellW.width / 100;
                    } else {
                        cellWidth = cellW.width;
                    }

                    if ( cellWidth + sumGrid[curGridCol - 1] > sumGrid[curGridCol + gridSpan - 1] ) {
                        const nTempDiff = cellWidth + sumGrid[curGridCol - 1] - sumGrid[curGridCol + gridSpan - 1];
                        for (let index2 = curGridCol + gridSpan - 1; index2 < sumGrid.length; index2++) {
                            sumGrid[index2] += nTempDiff;
                        }
                    }
                }

                curGridCol += gridSpan;
            }

            // 查看行尾的缺失网格列的宽度。
            const afterInfo = row.getAfter();
            if ( curGridCol + afterInfo.gridAfter - 1 > sumGrid.length) {
                for (let addIndex = sumGrid.length; addIndex <= curGridCol + afterInfo.gridAfter - 1; addIndex++) {
                    sumGrid[addIndex] = sumGrid[addIndex - 1] + 20;
                }
            }

            if ( sumGrid[curGridCol + afterInfo.gridAfter - 1] <
                afterInfo.widthAfter.width + sumGrid[curGridCol - 1] ) {
                const nTempDiff = afterInfo.widthAfter.width + sumGrid[curGridCol - 1]
                                    - sumGrid[curGridCol + afterInfo.gridAfter - 1];
                for (let nTempIndex = curGridCol + afterInfo.gridAfter - 1; nTempIndex < sumGrid.length; nTempIndex++) {
                    sumGrid[nTempIndex] += nTempDiff;
                }
            }
        }

        // 处理表格和单元格的最小宽度
        // 设置表的总宽度，最后一个单元格超出此宽度。 按比例减少所有网格列，以使总宽度等于指定的表宽度。
        if ( 0 < tableW && Math.abs(sumGrid[sumGrid.length - 1] - tableW) > TABLE_RECALC_TOL ) {
            sumGrid = table.scaleTableWidth(sumGrid, tableW);
        } else if ( minWidth > sumGrid[sumGrid.length - 1] ) {
            sumGrid = table.scaleTableWidth(sumGrid, sumGrid[sumGrid.length - 1]);
        }

        table.tableGridCalc = [];
        table.tableGridCalc[0] = sumGrid[0];
        // 通过数组SumGrid设置每列的宽度
        for (let index = 1, count = sumGrid.length; index < count; index++) {
            table.tableGridCalc[index] = sumGrid[index] - sumGrid[index - 1];
        }

        table.tableSumGrid = sumGrid;

        const topTable = null; // table.parent.isInTable(true);
        if ( TableLayoutType.AutoFit === table.property.tableLayout ) {
            // 计算单元格内容宽度

            const minMargin: number[] = [];
            const minContent: number[] = [];
            const maxContent: number[] = [];
            const maxFlags: boolean[] = [];
            const gridCount = table.tableGridCalc.length;

            for (let curCol = 0; curCol < gridCount; curCol++) {
                minMargin[curCol] = 0;
                minContent[curCol] = 0;
                maxContent[curCol] = 0;
                maxFlags[curCol] = false;
            }

            // 1. 为table的所有列计算minMargin，minContent
            //    此外，如果单元格GridSpan > 1，则minMargin仅在第一列中被考虑，minContent在所有列中均匀分布。
            // let leftMargin = 0;
            // let rightMargin = 0;
            for (let curRow = 0, rowsCount = table.content.length; curRow < rowsCount; curRow++) {
                const row = table.content[curRow];
                const spacing = row.getCellSpacing();
                const spacingW = ( null != spacing ? spacing : 0 );
                // tslint:disable-next-line: no-shadowed-variable
                let curGridCol = 0;

                const beforeInfo = row.getBefore();
                const gridBefore = beforeInfo.gridBefore;
                const widthBefore = beforeInfo.widthBefore;
                let widthBeforeW = null;

                if ( TableWidthType.Mm === widthBefore.type ) {
                    widthBeforeW = widthBefore.width;
                // } else if ( TableWidthType.Pencent === widthBefore.type ) {
                //     widthBeforeW = widthBeforeW / 100;
                }

                if ( 1 === gridBefore ) {
                    if ( null !== widthBeforeW ) {
                        if ( minContent[curGridCol] < widthBeforeW ) {
                            minContent[curGridCol] = widthBeforeW;
                        }

                        if ( false === maxFlags[curGridCol] ) {
                            maxFlags[curGridCol] = true;
                            maxContent[curGridCol] = widthBeforeW;
                        } else if ( maxContent[curGridCol] < widthBeforeW ) {
                            maxContent[curGridCol] = widthBeforeW;
                        }
                    }
                } else if ( 1 < gridBefore ) {
                    let sumSpanminContent = 0;
                    let sumSpanmaxContent = 0;
                    let sumSpanCurContent = 0;
                    let sumSpanminMargin  = 0;
                    for ( let curSpan = curGridCol; curSpan < curGridCol + gridBefore; curSpan++ ) {
                        sumSpanminContent += minContent[curSpan];
                        sumSpanmaxContent += maxContent[curSpan];
                        sumSpanminMargin  += minMargin[curSpan];
                        sumSpanCurContent += table.tableGridCalc[curSpan];
                    }

                    if ( null !== widthBeforeW && sumSpanminContent < widthBeforeW - sumSpanminMargin ) {
                        for (let curSpan = curGridCol; curSpan < curGridCol + gridBefore; curSpan++) {
                            minContent[curSpan] = widthBeforeW * table.tableGridCalc[curSpan]
                                                    / sumSpanCurContent - minMargin[curSpan];
                        }
                    }

                    // 如果在联合中有多个列，那么显式记录的单元格宽度不会与任何列的宽度重叠，它只参与确定最大宽度。
                    if ( null !== widthBeforeW && widthBeforeW > sumSpanmaxContent ) {
                        for ( let curSpan = curGridCol; curSpan < curGridCol + gridBefore; curSpan++ ) {
                            maxContent[curSpan] = widthBeforeW * table.tableGridCalc[curSpan] / sumSpanCurContent;
                        }
                    }
                }

                curGridCol = beforeInfo.gridBefore;

                for (let curCell = 0, cellsCount = row.getCellsCount(); curCell < cellsCount; curCell++) {
                    const cell = row.getCell(curCell);
                    const cellMinMax   = cell.recalculateMinmaxContentWidth(false);
                    let cellMin      = cellMinMax.Min;
                    let cellMax      = cellMinMax.Max;
                    const gridSpan     = cell.getGridSpan();
                    const cellMargins  = cell.getMargins();
                    const cellW        = cell.getCellWidth();
                    const cellRBorder  = cell.getBorder(1);
                    const cellLBorder  = cell.getBorder(3);
                    let cellWW       = null;

                    const add = ( ( 0 === curCell || cellsCount - 1 === curCell ) ? 3 / 2 * spacingW : spacingW );

                    cellMin += add;
                    cellMax += add;

                    // if ( 0 > cellW.width) {
                    //     cellMin = cellMin + cellW.width;
                    // }

                    if ( TableWidthType.Mm === cellW.type ) {
                        cellWW = cellW.width + add;
                    }
                    // else if ( TableWidthType.Pencent === CellW.Type )
                    //     CellWW = PctWidth * CellW.W / 100 + Add;

                    // let cellMarginsLeftW = 0;
                    // let cellMarginsRightW = 0;
                    // if ( null !== spacing ) {
                    //     //
                    // } else {
                    //     if ( TableBorderLineStyle.None !== cellRBorder.value ) {
                    //         cellMarginsRightW += Math.max(cellRBorder.size / 2, cellMargins.right.width);
                    //     } else {
                    //         cellMarginsRightW += cellMargins.right.width;
                    //     }

                    //     if ( TableBorderLineStyle.None !== cellLBorder.value ) {
                    //         cellMarginsLeftW += Math.max(cellLBorder.size / 2, cellMargins.left.width);
                    //     } else {
                    //         cellMarginsLeftW += cellMargins.left.width;
                    //     }
                    // }

                    // if (gridSpan <= 1) {
                    //     if (minMargin[curGridCol] < cellMarginsLeftW + cellMarginsRightW) {
                    //         minMargin[curGridCol] = cellMarginsLeftW + cellMarginsRightW;
                    //     }
                    // } else {
                    //     if (minMargin[curGridCol] < cellMarginsLeftW) {
                    //         minMargin[curGridCol] = cellMarginsLeftW;
                    //     }

                    //     if (minMargin[curGridCol + gridSpan - 1] < cellMarginsRightW) {
                    //         minMargin[curGridCol + gridSpan - 1] = cellMarginsRightW;
                    //     }
                    // }

                    // 实际上，情况1 === GridSpan将正常处理，并且作为GridSpan> 1的情况，但由于它是最常见的，尽可能快地进行处理（没有循环）
                    if ( 1 === gridSpan ) {
                        if ( minContent[curGridCol] < cellMin ) {
                            minContent[curGridCol] = cellMin;
                        }

                        if ( false === maxFlags[curGridCol] && maxContent[curGridCol] < cellMax ) {
                            maxContent[curGridCol] = cellMax;
                        }

                        // 根据规范，如果在某处设置宽度，则仅使用第一个值。
                        if (null !== cellWW && false === maxFlags[curGridCol]) {
                            maxFlags[curGridCol]   = true;
                            maxContent[curGridCol] = cellWW;
                        }
                    } else {
                        let sumSpanMinContent = 0;
                        let sumSpanMaxContent = 0;
                        let sumSpanCurContent = 0;
                        let sumSpanMinMargin  = 0;

                        for ( let curSpan = curGridCol; curSpan < curGridCol + gridSpan; curSpan++ ) {
                            sumSpanMinContent += minContent[curSpan];
                            sumSpanMaxContent += maxContent[curSpan];
                            sumSpanMinMargin  += minMargin[curSpan];
                            sumSpanCurContent += table.tableGridCalc[curSpan];
                        }

                        if (sumSpanMinContent < cellMin - sumSpanMinMargin) {
                            for (let curSpan = curGridCol; curSpan < curGridCol + gridSpan; curSpan++) {
                                minContent[curSpan] = cellMin * table.tableGridCalc[curSpan]
                                                        / sumSpanCurContent - minMargin[curSpan];
                            }
                        }

                        // 如果在联合中有多个列，那么显式记录的单元格宽度不会与任何列的宽度重叠，它只参与确定最大宽度。
                        if (null !== cellWW && cellWW > cellMax) {
                            cellMax = cellWW;

                            for (let curSpan = curGridCol; curSpan < curGridCol + gridSpan; curSpan++) {
                                // 根据规范，如果在某处指定宽度，则仅使用第一个值
                                if (false === maxFlags[curSpan]) {
                                    maxFlags[curSpan]   = true;
                                    maxContent[curSpan] = table.tableGridCalc[curSpan];
                                }
                            }
                        } else {
                            if (sumSpanMaxContent < cellMax) {
                                for (let curSpan = curGridCol; curSpan < curGridCol + gridSpan; curSpan++) {
                                    // 根据规范，如果在某处指定宽度，则仅使用第一个值
                                    if (true !== maxFlags[curSpan]) {
                                        maxContent[curSpan] = cellMax * table.tableGridCalc[curSpan]
                                                                        / sumSpanCurContent;
                                    }
                                }
                            }
                        }
                    }

                    // if ( 0 === curRow && 0 === curCell ) {
                    //     leftMargin = cellMargins.left.width;
                    // }

                    // if ( 0 === curRow && cellsCount - 1 === curCell ) {
                    //     rightMargin = cellMargins.right.width;
                    // }

                    curGridCol += gridSpan;
                }

                const afterInfo = row.getAfter();
                const gridAfter = afterInfo.gridAfter;
                const wAfter    = afterInfo.widthAfter;

                let wAfterW   = null;

                if ( TableWidthType.Mm === wAfter.type) {
                    wAfterW = wAfter.width;
                }
                // else if ( TableWidthType.Pencent === WAfter.type)
                //     WAfterW = PctWidth * WAfter.W / 100;

                if ( 1 === gridAfter ) {
                    if (null !== wAfterW) {
                        if (minContent[curGridCol] < wAfterW) {
                            minContent[curGridCol] = wAfterW;
                        }

                        if ( false === maxFlags[curGridCol] ) {
                            maxFlags[curGridCol] = true;
                            maxContent[curGridCol] = wAfterW;
                        } else if (maxContent[curGridCol] < wAfterW) {
                            maxContent[curGridCol] = wAfterW;
                        }
                    }
                } else if ( gridAfter > 1 ) {
                    let sumSpanMinContent = 0;
                    let sumSpanMaxContent = 0;
                    let sumSpanCurContent = 0;
                    for ( let curSpan = curGridCol; curSpan < curGridCol + gridAfter; curSpan++ ) {
                        sumSpanMinContent += minContent[curSpan];
                        sumSpanMaxContent += maxContent[curSpan];
                        sumSpanCurContent += table.tableGridCalc[curSpan];
                    }

                    if (null !== wAfterW && sumSpanMinContent < wAfterW) {
                        for ( let curSpan = curGridCol; curSpan < curGridCol + 1; curSpan++ ) {
                            minContent[curSpan] = wAfterW * table.tableGridCalc[curSpan] / sumSpanCurContent;
                        }
                    }

                    // 如果在并集中有多个列，则显式记录的单元格宽度不会与任何列的宽度重叠，它只会参与确定最大宽度。
                    if (null !== wAfterW && wAfterW > sumSpanMaxContent ) {
                        for ( let curSpan = curGridCol; curSpan < curGridCol + gridAfter; curSpan++ ) {
                            maxContent[curSpan] = wAfterW * table.tableGridCalc[curSpan] / sumSpanCurContent;
                        }
                    }
                }
            }

            for ( let curCol = 0; curCol < gridCount; curCol++ ) {
                if ( true === maxFlags[curCol] ) {
                    maxContent[curCol] = Math.max( 0, maxContent[curCol] - minMargin[curCol] );
                }

                if (maxContent[curCol] < minContent[curCol]) {
                    maxContent[curCol] = minContent[curCol];
                }
            }

            // 2. 确保minContent + minMargin和maxContent + minMargin值不超过55.87厘米的值（这是Word的工作方式）
            for ( let curCol = 0; curCol < gridCount; curCol++ ) {
                if ( minMargin[curCol] + minContent[curCol] > maxWordWidth ) {
                    minContent[curCol] = Math.max(maxWordWidth - minMargin[curCol] , 0);
                }

                if ( minMargin[curCol] + maxContent[curCol] > maxWordWidth ) {
                    maxContent[curCol] = Math.max(maxWordWidth - minMargin[curCol] , 0);
                }
            }

            // 3. 计算整个表的最大允许宽度
            const pageFields = table.parent.getPageFields(table.getRelativePageIndex(table.pageNum));
            let maxTableW = pageFields.xLimit - pageFields.x - table.property.tableIndentation;
            // if ( null === topTable )
            //     MaxTableW += ( leftMargin + rightMargin ); // 为上表添加第一个单元格的左边距 + 右边单元格的右边距

            const tableSpacing = table.content[0].getCellSpacing();
            if ( null != tableSpacing ) {
                maxTableW += 2 * tableSpacing;
            }

            // 4. 计算表格所需的宽度。数字2表示额外的差异
            const maxContent2 = [];
            let sumMin = 0;
            let sumMinMargin = 0;
            let sumMinContent = 0;
            let sumMax = 0;
            let sumMaxContent2 = 0;
            const tableGrid2 = [];
            for ( let curCol = 0; curCol < gridCount; curCol++ ) {
                const temp = minMargin[curCol] + minContent[curCol];
                tableGrid2[curCol] = table.tableGridCalc[curCol];

                if ( temp < table.tableGridCalc[curCol] ) {
                    tableGrid2[curCol] = table.tableGridCalc[curCol];
                } else {
                    tableGrid2[curCol] = temp;
                }

                maxContent2[curCol] = Math.max( 0, maxContent[curCol] - minContent[curCol] );

                sumMin         += temp;
                sumMaxContent2 += maxContent2[curCol];
                sumMinMargin   += minMargin[curCol];
                sumMinContent  += minContent[curCol];
                sumMax         += minMargin[curCol] + minContent[curCol] + maxContent2[curCol];
            }

            if (( TableWidthType.Mm === table.property.tableWidth.type ||
                    TableWidthType.Pencent === table.property.tableWidth.type ) && maxTableW < tableW ) {
                maxTableW = tableW;
            }

            if ( sumMin < maxTableW ) {
                // SumMin < MaxTableW，这意味着有可用的分配空间。有三种宽度：Min <Preffered <Max

                // tslint:disable-next-line: no-shadowed-variable
                let sumMin = 0;
                // tslint:disable-next-line: no-shadowed-variable
                let sumMax = 0;
                let sumPreffered = 0;
                const preffOverMin = [];
                const maxOverPreff = [];
                let sumPreffOverMin = 0;
                let sumMaxOverPreff = 0;
                const preffContent = [];

                for (let curCol = 0; curCol < gridCount; ++curCol) {
                    const minW   = minMargin[curCol] + minContent[curCol];
                    const maxW   = minMargin[curCol] + maxContent[curCol];
                    const preffW = (true === maxFlags[curCol] ? maxW : minW);

                    sumMin       += minW;
                    sumPreffered += preffW;
                    sumMax       += maxW;

                    preffContent[curCol] = preffW - minMargin[curCol];
                    preffOverMin[curCol] = Math.max(0, preffW - minW);
                    maxOverPreff[curCol] = Math.max(0, maxW - preffW);

                    sumPreffOverMin += preffOverMin[curCol];
                    sumMaxOverPreff += maxOverPreff[curCol];
                }

                if ( sumMax <= maxTableW || sumMaxContent2 < recalcMinUnit ) {
                    for ( let curCol = 0; curCol < gridCount; curCol++ ) {
                        table.tableGridCalc[curCol] = minMargin[curCol]
                                                            + Math.max(minContent[curCol], maxContent[curCol]);
                    }
                } else {
                    for ( let curCol = 0; curCol < gridCount; curCol++ ) {
                        table.tableGridCalc[curCol] = minMargin[curCol] + minContent[curCol]
                                                    + (maxTableW - sumMin) * maxContent2[curCol] / sumMaxContent2;
                    }
                }

                // 如果表具有给定的宽度，那么宽度是朝向的，如果没有，那么就以最大值为导向。
                if ( TableWidthType.Mm === table.property.tableWidth.type
                    || TableWidthType.Pencent === table.property.tableWidth.type ) {
                    if (sumMin < recalcMinUnit && sumMax < recalcMinUnit) {
                        // 分配列数的宽度
                        for (let curCol = 0; curCol < gridCount; ++curCol) {
                            table.tableGridCalc[curCol] = tableW / gridCount;
                        }
                    } else if ( sumMin >= tableW ) {
                        // 设定最小值
                        for (let curCol = 0; curCol < gridCount; ++curCol) {
                            table.tableGridCalc[curCol] = minMargin[curCol] + minContent[curCol];
                        }
                    } else if ( sumPreffered >= tableW && sumPreffOverMin > recalcMinUnit ) {
                        // 仅拉伸指定首选宽度的列
                        for (let curCol = 0; curCol < gridCount; ++curCol) {
                            table.tableGridCalc[curCol] = minMargin[curCol] + minContent[curCol]
                                                        + (tableW - sumMin) * preffOverMin[curCol] / sumPreffOverMin;
                        }
                    } else {
                        // 如果满足此条件，那么将所有单元格都设置为首选值，然后均匀地拉伸所有单元格。
                        // 如果没有，那么有些单元格中没有指定首选值，然后只拉伸这样的单元格。
                        if ( Math.abs(sumMax - sumPreffered) < recalcMinUnit ) {
                            if ( sumMax >= tableW ) {
                                for (let curCol = 0; curCol < gridCount; ++curCol) {
                                    table.tableGridCalc[curCol] = minMargin[curCol] + minContent[curCol]
                                                            + (tableW - sumMin) * maxContent2[curCol] / sumMaxContent2;
                                }
                            } else {
                                for (let curCol = 0; curCol < gridCount; curCol++) {
                                    table.tableGridCalc[curCol] = minMargin[curCol] + maxContent[curCol]
                                                + (tableW - sumMax) * (minMargin[curCol] + maxContent[curCol]) / sumMax;
                                }
                            }
                        } else {
                            for (let curCol = 0; curCol < gridCount; ++curCol) {
                                table.tableGridCalc[curCol] = minMargin[curCol] + preffContent[curCol]
                                                    + (tableW - sumPreffered) * maxOverPreff[curCol] / sumMaxOverPreff;
                            }
                        }
                    }
                } else {
                    // 5. 如果在表中使所有单元格的宽度为零（对于内容），并且结果仍然比最大允许宽度宽，则将所有列的宽度设置为最小边距值并保持原样
                    if (maxTableW - sumMinMargin < recalcMinUnit) {
                        for ( let curCol = 0; curCol < gridCount; curCol++ ) {
                            table.tableGridCalc[curCol] = minMargin[curCol];
                        }
                    } else {

                        // 6. 均匀地减少所有列以达到MaxTableW的总值

                        const colsDiff = [];
                        let sumColsDiff = 0;
                        for ( let curCol = 0; curCol < gridCount; curCol++ ) {
                            const temp = tableGrid2[curCol] - minMargin[curCol];
                            colsDiff[curCol] = temp;
                            sumColsDiff += temp;
                        }

                        for ( let curCol = 0; curCol < gridCount; curCol++ ) {
                            tableGrid2[curCol] = minMargin[curCol]
                                                + (maxTableW - sumMinMargin) * colsDiff[curCol] / sumColsDiff;
                        }

                        // 7. 寻找当前宽度小于minContent的列（同时寻找缺少的数量）,记得剩余的列并找到它们的多余数量。
                        let sumN = 0;
                        let sumI = 0;
                        const gridCols = [];
                        for ( let curCol = 0; curCol < gridCount; curCol++ ) {
                            const temp = tableGrid2[curCol] - (minMargin[curCol] + minContent[curCol]);

                            if ( temp >= 0 ) {
                                gridCols[curCol] = temp;
                                sumI += temp;
                            } else {
                                gridCols[curCol] = -1;
                                sumN -= temp;
                            }
                        }

                        // 8. 如果剩余空间多于超出空间，那么寻找差异（MaxTableW - SumminMargin）并将其与minContent值相互关联的方式进行分配。
                        if ( sumN > sumI || sumI < recalcMinUnit ) {
                            if ( sumMinContent > recalcMinUnit ) {
                                const sumDiff = maxTableW - sumMinMargin;

                                for (let curCol = 0; curCol < gridCount; curCol++) {
                                    table.tableGridCalc[curCol] = minMargin[curCol]
                                                            + sumDiff * minContent[curCol] / sumMinContent;
                                }
                            } else {
                                for (let curCol = 0; curCol < gridCount; curCol++) {
                                    table.tableGridCalc[curCol] = minMargin[curCol];
                                }
                            }
                        } else {
                            for ( let curCol = 0; curCol < gridCount; curCol++ ) {
                                if ( gridCols[curCol] < 0 ) {
                                    table.tableGridCalc[curCol] = minMargin[curCol] + minContent[curCol];
                                } else {
                                    table.tableGridCalc[curCol] = tableGrid2[curCol] - sumN * gridCols[curCol] / sumI;
                                }
                            }
                        }
                    }
                }
            }

            table.tableSumGrid[-1] = 0;
            for ( let curCol = 0; curCol < gridCount; curCol++ ) {
                table.tableSumGrid[curCol] = table.tableSumGrid[curCol - 1] + table.tableGridCalc[curCol];
            }

            if ( 1 <= Math.abs(tempSum - maxTableW) ) {
                table.tableGrid = tableGrid2;
            }
        }

        table.recalcInfo.bTableGrid = false;
    }

    public recalculateGridCols(): void {
        for (let curRow = 0, rowsCount = this.table.content.length; curRow < rowsCount; curRow++) {
            const row = this.table.content[curRow];
            const beforeInfo = row.getBefore();
            let curGridCol = beforeInfo.gridBefore;

            for (let curCell = 0, cellsCount = row.getCellsCount(); curCell < cellsCount; curCell++) {
                const cell = row.getCell(curCell);
                row.setCellInfo(curCell, curGridCol, 0, 0, 0, 0, 0, 0);
                cell.setMetrics(curGridCol, 0, 0, 0, 0, 0, 0);
                curGridCol += cell.getGridSpan();
            }
        }
    }

    /**
     * 计算表格边框
     */
    protected recalculateBorders(): void {
        const table = this.table;

        // if ( true !== table.recalcInfo.bTableBorders )
        //     return;

        const rowsCount = table.content.length;
        for (let index = -1; index < rowsCount; index++) {
            table.tableRowsBottom[index] = [];
            table.tableRowsBottom[index][0] = 0;
        }

        // 记录每个单元格的top，bottom
        const maxTopBorder = [];
        const maxBottomBorder = [];
        const maxBottomMargin = [];

        for (let index = 0; index < rowsCount; index++) {
            maxTopBorder[index] = 0;
            maxBottomBorder[index] = 0;
            maxBottomMargin[index] = 0;
        }

        const tableBorders = table.getTableBorders();

        for (let curRow = 0; curRow < rowsCount; curRow++) {
            const row = table.content[curRow];
            const cellsCount = row.getCellsCount();
            const cellSpacing = row.getCellSpacing();

            const beforeInfo = row.getBefore();
            const afterInfo = row.getAfter();
            let curGridCol = beforeInfo.gridBefore;

            // 遍历当前行并找出上边框宽度的最大值和下边框的宽度，
            // 同时计算每个单元格的边框类型，还需要计算整条线的下边距的最大值。

            for (let curCell = 0; curCell < cellsCount; curCell++) {
                const cell = row.getCell(curCell);
                const gridSpan = cell.getGridSpan();
                // const vMerge = cell.getVMerge();

                row.setCellInfo(curCell, curGridCol, 0, 0, 0, 0, 0, 0);

                const vMergeCount = table.getVerticalMergeCount(curRow, curGridCol, gridSpan);

                const cellMargins = cell.getMargins();
                if ( cellMargins.bottom.width > maxBottomMargin[curRow + vMergeCount - 1] ) {
                    maxBottomMargin[curRow + vMergeCount - 1] = cellMargins.bottom.width;
                }

                const cellBorders = cell.getBorders();

                // 1. 计算top border
                if ( 0 === curRow ) {
                    // 获取单元格top边框：table默认边框，或是cell单独设置的边框
                    const resultBorder = table.compareBorders(tableBorders.top, cellBorders.top, true, false);
                    if ( TableBorderLineStyle.Single === resultBorder.value
                        && maxTopBorder[curRow] < resultBorder.size ) {
                        maxTopBorder[curRow] = resultBorder.size;
                    }

                    const borderInfoTop: DocumentBorder[] = [];
                    for (let index = 0; index < gridSpan; index++) {
                        borderInfoTop.push(resultBorder);
                    }

                    cell.setBorderInfoTop(borderInfoTop);
                } else {
                    const prevRow = table.content[curRow - 1];
                    const prevCelllsCount = prevRow.getCellsCount();
                    const prevBeforeInfo = prevRow.getBefore();
                    const prevAfterInfo = prevRow.getAfter();

                    let prevPos = -1;
                    let prevGridCol = prevBeforeInfo.gridBefore;

                    // 查看与前一行的交集，可确定当前cell与上一行cell所在同一列
                    for (let index = 0; index < prevCelllsCount; index++) {
                        const prevCell = prevRow.getCell(index);
                        const prevGridSpan = prevCell.getGridSpan();

                        if ( prevGridCol <= curGridCol + gridSpan - 1
                            && prevGridCol + prevGridSpan - 1 >= curGridCol ) {
                            prevPos = index;
                            break;
                        }

                        prevGridCol += prevGridSpan;
                    }

                    const borderInfoTop: DocumentBorder[] = [];

                    if ( curGridCol <= prevBeforeInfo.gridBefore - 1 ) {
                        const resultBorder = table.compareBorders(tableBorders.left, cellBorders.top, true, false);
                        if ( TableBorderLineStyle.Single === resultBorder.value
                            && maxTopBorder[curRow] < resultBorder.size ) {
                            maxTopBorder[curRow] = resultBorder.size;
                        }

                        // const borderInfoTop: DocumentBorder[] = [];
                        for (let index = 0; index < gridSpan; index++) {
                            borderInfoTop.push(resultBorder);
                        }
                    }

                    if ( -1 !== prevPos ) {
                        while ( prevGridCol <= curGridCol + gridSpan - 1 && prevPos < prevCelllsCount ) {
                            let prevCell = prevRow.getCell(prevPos);
                            const prevGridSpan = prevCell.getGridSpan();
                            const prevVMerge = prevCell.getVMerge();

                            if ( VerticalMergeType.Continue === prevVMerge ) {
                                prevCell = table.getEndMergeCell(curRow - 1, prevGridCol, prevGridSpan);
                            }

                            const prevCellBorders = prevCell.getBorders();
                            const resultBorder = table.compareBorders(prevCellBorders.bottom,
                                                            cellBorders.top, false, false);
                            if ( TableBorderLineStyle.Single === resultBorder.value
                                && maxTopBorder[curRow] < resultBorder.size ) {
                                maxTopBorder[curRow] = resultBorder.size;
                            }

                            let addCount = 0;
                            if ( prevGridCol >= curGridCol) {
                                if ( prevGridCol + prevGridSpan - 1 > curGridCol + gridSpan - 1 ) {
                                    addCount = curGridCol + gridSpan - prevGridCol;
                                } else {
                                    addCount = prevGridSpan;
                                }
                            } else if ( prevGridCol + prevGridSpan - 1 > curGridCol + gridSpan - 1 ) {
                                addCount = gridSpan;
                            } else {
                                addCount = prevGridCol + prevGridSpan - curGridCol;
                            }

                            for (let index = 0; index < addCount; index++) {
                                borderInfoTop.push(resultBorder);
                            }

                            prevPos++;
                            prevGridCol += prevGridSpan;
                        }
                    }

                    if ( 0 < prevAfterInfo.gridAfter ) {
                        const startAfterGrid = prevRow.getCellInfo(prevCelllsCount - 1).startGridCol
                                                + prevRow.getCell(prevCelllsCount - 1)
                                                                                    .getGridSpan();

                        if ( curGridCol + gridSpan - 1 >= startAfterGrid ) {
                            const resultBorder = table.compareBorders(tableBorders.right, cellBorders.top, true, false);
                            if ( TableBorderLineStyle.Single === resultBorder.value
                                && maxTopBorder[curRow] < resultBorder.size ) {
                                maxTopBorder[curRow] = resultBorder.size;
                            }

                            const addCount = Math.min(curGridCol + gridSpan - startAfterGrid, gridSpan);
                            for (let index = 0; index < addCount; index++) {
                                borderInfoTop.push(resultBorder);
                            }
                        }
                    }

                    cell.setBorderInfoTop(borderInfoTop);
                }

                // 2. 计算bottom border
                let cellBordersBottom = cellBorders.bottom;
                if ( 1 < vMergeCount ) {
                    const bottomCell = table.getEndMergeCell(curRow, curGridCol, gridSpan);
                    if ( bottomCell ) {
                        cellBordersBottom = bottomCell.getBorders().bottom;
                    }
                }

                // 单元格是table最后一行row
                if ( rowsCount - 1 === curRow + vMergeCount - 1 ) {
                    const resultBorder = table.compareBorders(tableBorders.bottom, cellBordersBottom, true, false);

                    if ( TableBorderLineStyle.Single === resultBorder.value
                        && maxBottomBorder[curRow + vMergeCount - 1] < resultBorder.size ) {
                        maxBottomBorder[curRow + vMergeCount - 1] = resultBorder.size;
                    }

                    if ( 0 < gridSpan ) {
                        for (let index = 0; index < gridSpan; index++) {
                            cell.setBorderInfoBottom([resultBorder], -1, -1);
                        }
                    } else {
                        cell.setBorderInfoBottom([], -1, -1);
                    }
                } else {
                    const nextRow = table.content[curRow + vMergeCount];
                    const nextCelllsCount = nextRow.getCellsCount();
                    const nextBeforeInfo = nextRow.getBefore();
                    const nextAfterInfo = nextRow.getAfter();

                    const borderInfoBottom: DocumentBorder[] = [];
                    let beforeCount = 0;
                    if ( curGridCol <= nextBeforeInfo.gridBefore - 1 ) {
                        const resultBorder = table.compareBorders(tableBorders.left, cellBordersBottom, true, false);
                        beforeCount = Math.min(nextBeforeInfo.gridBefore - curGridCol, gridSpan);

                        for (let index = 0; index < beforeCount; index++) {
                            borderInfoBottom.push(resultBorder);
                        }
                    }

                    let nextGridCol = nextBeforeInfo.gridBefore;
                    for (let index = 0; index < nextCelllsCount; index++) {
                        const nextCell = nextRow.getCell(index);
                        const nextGridSpan = nextCell.getGridSpan();
                        nextGridCol += nextGridSpan;
                    }

                    let afterCount = 0;
                    if ( 0 < nextAfterInfo.gridAfter ) {
                        const startAfterGrid = nextGridCol;

                        if ( curGridCol + gridSpan - 1 >= startAfterGrid ) {
                            const resultBorder = table.compareBorders(tableBorders.right,
                                                                            cellBordersBottom, true, false);
                            afterCount = Math.min(curGridCol + gridSpan - startAfterGrid, gridSpan);

                            for (let index = 0; index < afterCount; index++) {
                                borderInfoBottom.push(resultBorder);
                            }
                        }
                    }

                    cell.setBorderInfoBottom(borderInfoBottom, beforeCount, afterCount);
                }

                curGridCol += gridSpan;
            }
        }

        table.maxTopBorder = maxTopBorder;
        table.maxBottomBorder = maxBottomBorder;
        table.maxBottomMargin = maxBottomMargin;

        // 3. 计算每个单元格的左右边框
        for (let curRow = 0; curRow < rowsCount; curRow++) {
            const row = table.content[curRow];
            const cellsCount = row.getCellsCount();
            const cellSpacing = row.getCellSpacing();

            const beforeInfo = row.getBefore();
            const afterInfo = row.getAfter();
            let curGridCol = beforeInfo.gridBefore;

            let rowXMax = 0;
            let rowXMin = 0;

            for (let curCell = 0; curCell < cellsCount; curCell++) {
                const cell = row.getCell(curCell);
                const gridSpan = cell.getGridSpan();
                const vMerge = cell.getVMerge();
                const cellBorders = cell.getBorders();
                const cellMargins = cell.getMargins();

                // 给定GridSpan的起点和终点
                const xGridStart = table.tableSumGrid[curGridCol - 1];
                const xGridEnd = table.tableSumGrid[curGridCol + gridSpan - 1];

                // cell边界
                const xCellStart = xGridStart;
                const xCellEnd = xGridEnd;

                // 单元格内容的起点和终点
                let xContentStart = xCellStart;
                let xContentEnd = xCellEnd;

                if ( VerticalMergeType.Continue === vMerge) {
                    xContentStart += cellMargins.left.width;
                    xContentEnd -= cellMargins.right.width;

                    // cell.setBorderInfoLeft([], 0);
                    // cell.setBorderInfoRight([], 0);
                } else {
                    let rightMaxWidth = 0;
                    let leftMaxWidth = 0;
                    const bordersInfo = {
                        right: [],
                        left: [],
                        rightMax: 0,
                        leftMax: 0,
                    };

                    const vMergeCount = table.getVerticalMergeCount(curRow, curGridCol, gridSpan);
                    for (let tempCurRow = 0; tempCurRow < vMergeCount; tempCurRow++) {
                        const tempRow = table.content[tempCurRow + curRow];
                        const tempCellsCount = tempRow.getCellsCount();

                        // 1. 寻找当前的合并单元格
                        const tempCurCell = table.getCellIndexByStartGridCol(curRow + tempCurRow, curGridCol, false);

                        if ( 0 > tempCurCell ) {
                            continue;
                        }

                        // 2. 左边界
                        // row的第一个单元格
                        if ( 0 === tempCurCell ) {
                            const leftBorder = table.compareBorders(tableBorders.left, cellBorders.left, true, false);

                            if ( TableBorderLineStyle.Single === leftBorder.value && leftBorder.size > leftMaxWidth ) {
                                leftMaxWidth = leftBorder.size;
                            }

                            bordersInfo.left.push(leftBorder);
                        } else {
                            const tempPrevCell = tempRow.getCell(tempCurCell - 1);
                            const tempPrevVMerge = tempPrevCell.getVMerge();

                            if ( 0 !== tempCurRow && VerticalMergeType.Continue === tempPrevVMerge ) {
                                bordersInfo.left.push(bordersInfo.left[bordersInfo.left.length - 1]);
                            } else {
                                const tempPrevMainCell = table.getStartMergedCell(curRow + tempCurRow,
                                    curGridCol - tempPrevCell.getGridSpan(), tempPrevCell.getGridSpan());
                                const tempPrevMainCellBorders = tempPrevMainCell.getBorders();

                                const leftBorder = table.compareBorders(tempPrevMainCellBorders.right,
                                                                            cellBorders.left, false, false);

                                if ( TableBorderLineStyle.Single === leftBorder.value
                                    && leftBorder.size > leftMaxWidth ) {
                                    leftMaxWidth = leftBorder.size;
                                }

                                bordersInfo.left.push(leftBorder);
                            }
                        }

                        // 3. 右边界
                        // 当前row的最后一个单元格
                        if ( tempCellsCount - 1 === tempCurCell ) {
                            const rightBorder = table.compareBorders(tableBorders.right,
                                                                        cellBorders.right, true, false);

                            if ( TableBorderLineStyle.Single === rightBorder.value
                                && rightBorder.size > rightMaxWidth ) {
                                rightMaxWidth = rightBorder.size;
                            }

                            bordersInfo.right.push(rightBorder);
                        } else {
                            const tempNextCell = tempRow.getCell(tempCurCell + 1);
                            const tempNextVMerge = tempNextCell.getVMerge();

                            if ( 0 !== tempCurRow && VerticalMergeType.Continue === tempNextVMerge ) {
                                bordersInfo.right.push(bordersInfo.right[bordersInfo.right.length - 1]);
                            } else {
                                const tempNextMainCell = table.getStartMergedCell(curRow + tempCurRow,
                                    curGridCol + gridSpan, tempNextCell.getGridSpan());
                                const tempNextMainCellBorders = tempNextMainCell.getBorders();

                                const rightBorder = table.compareBorders(tempNextMainCellBorders.left,
                                    cellBorders.right, false, false);

                                if ( TableBorderLineStyle.Single === rightBorder.value
                                    && rightBorder.size > rightMaxWidth ) {
                                    rightMaxWidth = rightBorder.size;
                                }

                                bordersInfo.right.push(rightBorder);
                            }
                        }
                    }

                    bordersInfo.leftMax = leftMaxWidth;
                    bordersInfo.rightMax = rightMaxWidth;

                    xContentStart += ( leftMaxWidth / 2 > cellMargins.left.width ?
                        leftMaxWidth / 2 : cellMargins.left.width );
                    xContentEnd -= ( rightMaxWidth / 2 > cellMargins.right.width ?
                        rightMaxWidth / 2 : cellMargins.right.width );

                    cell.setBorderInfoLeft(bordersInfo.left, leftMaxWidth);
                    cell.setBorderInfoRight(bordersInfo.right, rightMaxWidth);
                }

                const borderInfo = cell.getBorderInfo();
                if ( 0 === curCell ) {
                    rowXMin = xGridStart - borderInfo.maxLeft / 2;
                }

                if ( cellsCount - 1 === curCell ) {
                    rowXMax = xGridEnd + borderInfo.maxRight / 2;
                }

                cell.setMetrics(curGridCol, xGridStart, xGridEnd, xCellStart, xCellEnd, xContentStart, xContentEnd);

                curGridCol += gridSpan;
            }

            row.setMetricsX(rowXMin, rowXMax);
        }

        table.recalcInfo.bTableBorders = false;
    }

    /**
     * 计算表格标题行
     */
    protected recalculateHeader(): void {
        const table = this.table;

        // 如果在table中有一个子table，那么该子table中不应该有任何标题行
        if ( true === table.parent.isTableCellContent() || !table.isRepeatHeader() ) {
            table.headerInfo.count = 0;
            return;
        }

        let headerRowsCount = 0;
        const rowsCount = table.content.length;

        for (let index = 0; index < rowsCount; index++) {
            const row = table.content[index];
            if ( true !== row.isTableHeader() ) {
                break;
            }

            headerRowsCount++;
        }

        for (let curRow = headerRowsCount - 1; curRow >= 0; curRow--) {
            const row = table.content[curRow];
            const cellsCount = row.getCellsCount();
            let bContinue = false;

            for (let curCell = 0; curCell < cellsCount; curCell++) {
                const cell = row.getCell(curCell);
                const gridSpan = cell.getGridSpan();
                const curGridCol = cell.metrics.startGridCol;
                const vMergeCount = table.getVerticalMergeCount(curRow, curGridCol, gridSpan);

                // 在这一行中，找到了一个垂直合并的单元格，其中一个单元格不是来自标题。因此，抛出这一行并检查前一行。
                if ( 1 < vMergeCount ) {
                    headerRowsCount--;
                    bContinue = true;
                    break;
                }
            }

            if ( true !== bContinue ) {
                break;
            }
        }

        table.headerInfo.count = headerRowsCount;
    }

    protected recalculatePageXY(curPage: number = 0): void {
        let firstRow = 0;
        const table = this.table;

        if ( 0 !== curPage ) {
            if ( true === table.isEmptyPage(curPage - 1) ) {
                firstRow = table.pages[curPage - 1].firstRow;
            } else if ( true === table.pages[curPage - 1].bLastRowSplit ) {
                firstRow = table.pages[curPage - 1].lastRow;
            } else {
                firstRow = Math.min(table.pages[curPage - 1].lastRow + 1, table.content.length - 1);
            }
        }

        const tempMaxTopBorder = table.getMaxTopBorder(firstRow);
        table.pages.length = Math.max(curPage, 0);

        if ( 0 === curPage ) {
            table.pages[0] = new TablePage(table.x, table.y, table.xLimit, table.yLimit, firstRow, tempMaxTopBorder);
        } else {
            const startPos = table.parent.getPageContentStartPos2(table.pageNum, curPage, table.index);
            table.pages[curPage] = new TablePage(startPos.x, startPos.y,
                startPos.xLimit, startPos.yLimit, firstRow, tempMaxTopBorder);
        }
    }

    protected recalculate(curPage: number = 0, bHidden: boolean): RecalcResultType {
        const table = this.table;
        // if ( true === table.bTurnOffRecalc )
        //     return;

        table.bTurnOffRecalc = true;

        // const pageAbs = table.getAbsolutePage(curPage);
        let firstRow = 0;
        let lastRow = 0;
        let bResetStartElement = false;

        // 确定当前页的firstRow，lastRow
        if ( 0 === curPage ) {
            for (let index = -1, length = table.content.length; index < length; index++) {
                table.tableRowsBottom[index] = [];
                table.tableRowsBottom[index][0] = 0;
            }
        } else {
            // 表格跨页

            if ( true === table.isEmptyPage(curPage - 1) ) {
                // 当前表格单元跨页，且单元格在上一页没有内容

                bResetStartElement = false;
                firstRow = table.pages[curPage - 1].firstRow;
            } else if ( true === table.pages[curPage - 1].bLastRowSplit ) {
                // 当前表格单元跨页，被拆分

                bResetStartElement = false;
                firstRow = table.pages[curPage - 1].lastRow;
            } else {
                // 表格跨页

                bResetStartElement = true;
                firstRow = Math.min(table.pages[curPage - 1].lastRow + 1, table.content.length - 1);
            }

            lastRow = firstRow;
        }

        const maxTopBorder = table.maxTopBorder;
        const maxBottomBorder = table.maxBottomBorder;

        const startPos = table.pages[curPage];
        if ( true === table.checkEmptyPages(curPage - 1) ) {
            table.headerInfo.pageIndex = -1;
        }

        const page = table.pages[curPage];
        // const tempMaxTopBorder = page.maxTopBorder;
        let y = startPos.y;
        let tableHeight = 0;

        const tableBorders = table.getTableBorders();
        const bFixedRowsHeight = table.isFixedRowHeight();

        let xMin = -1;
        let xMax = -1;

        // tableHeader
        if ( 0 < table.headerInfo.count && -1 !== table.headerInfo.pageIndex && curPage > table.headerInfo.pageIndex ) {
            table.headerInfo.bHeaderRecalculate = true;
            table.headerInfo.pages[curPage] = new TableHeaderPage();
            table.headerInfo.pages[curPage].rowsInfo = [];
            const headerPage = table.headerInfo.pages[curPage];

            headerPage.bDraw = true;
            headerPage.rows = [];

            const turnOn = table.logicDocument.history.isTurnOn();
            table.logicDocument.history.turnOff();
            table.logicDocument.bRecalcTableHeader = true;
            for (let index = 0; index < table.headerInfo.count; index++) {
                headerPage.rows[index] = table.content[index].copy(table);
                headerPage.rows[index].index = index;
            }

            if (turnOn) {
                table.logicDocument.history.turnOn();
            }

            let bHeaderNextPage = false;

            // tslint:disable-next-line: no-shadowed-variable
            for (let curRow = 0; curRow < table.headerInfo.count; curRow++) {
                headerPage.rowsInfo[curRow] = new TableRowInfo();
                headerPage.rowsInfo[curRow].height = [];
                headerPage.rowsInfo[curRow].y = [];
                headerPage.rowsInfo[curRow].topDy = [];
                headerPage.rowsInfo[curRow].maxTopBorder = [];
                headerPage.rowsInfo[curRow].maxBottomBorder = 0;

                headerPage.rowsInfo[curRow].y[0] = 0;
                headerPage.rowsInfo[curRow].height[0] = 0;
                headerPage.rowsInfo[curRow].topDy[0] = 0;
                headerPage.rowsInfo[curRow].maxTopBorder[0] = 0;

                const row = headerPage.rows[curRow];
                const cellsCount = row.getCellsCount();
                const beforeInfo = row.getBefore();
                let curGridCol = beforeInfo.gridBefore;

                y += maxTopBorder[curRow];
                tableHeight += maxTopBorder[curRow];

                const rowXMin = row.metrics.xMin;
                const rowXMax = row.metrics.xMax;
                if ( -1 === xMin || rowXMin < xMin ) {
                    xMin = rowXMin;
                }

                if ( -1 === xMax || rowXMax > xMax ) {
                    xMax = rowXMax;
                }

                let maxTopMargin = 0;

                for (let cellIndex = 0; cellIndex < cellsCount; cellIndex++) {
                    const cell = row.getCell(cellIndex);
                    const vMerge = cell.getVMerge();
                    const cellMargins = cell.getMargins();

                    if ( VerticalMergeType.Restart === vMerge && cellMargins.top.width > maxTopMargin ) {
                        maxTopMargin = cellMargins.top.width;
                    }
                }

                const rowH = row.getHeight();
                let rowHValue = rowH.value;
                rowHValue = rowH.value + table.maxBottomMargin[curRow] + maxTopMargin;

                // if ( null !== cellSpacing ) {
                //     rowHValue -= cellSpacing;
                // }

                let maxBottomValueVMerge = -1;
                const verticalCells: TableCell[] = [];

                for (let cellIndex = 0; cellIndex < cellsCount; cellIndex++) {
                    let cell = row.getCell(cellIndex);
                    const gridSpan = cell.getGridSpan();
                    const vMerge = cell.getVMerge();
                    let cellMargins = cell.getMargins();

                    row.updateCellInfo(cellIndex);

                    const cellMetrics = row.getCellInfo(cellIndex);

                    const xContentStart = page.x + cellMetrics.xContentStart;
                    const xContentEnd = page.x + cellMetrics.xContentEnd;
                    let yContentStart = y + cellMargins.top.width;
                    let yContentEnd = page.yLimit;

                    // if ( null !== cellSpacing ) {
                    //     if ( table.content.length - 1 === curRow ){
                    //         yContentEnd -= cellSpacing;
                    //     } else {
                    //         yContentEnd -= cellSpacing / 2;
                    //     }
                    // }

                    const vMergeCount = table.getVerticalMergeCount(curRow, curGridCol, gridSpan);
                    const bottomMargin = table.maxBottomMargin[curRow + vMergeCount - 1];
                    yContentEnd -= bottomMargin;

                    cell.temp.y = yContentStart;

                    // 多个单元格合并
                    if ( 1 < vMergeCount ) {
                        curGridCol += gridSpan;
                        // mergeCell.push(cell);
                        continue;
                    } else {
                        if ( VerticalMergeType.Restart !== vMerge ) {
                            cell = table.getStartMergedCell(curRow, curGridCol, gridSpan);
                            cell = headerPage.rows[cell.row.index].getCell(cell.index);

                            cellMargins = cell.getMargins();

                            yContentStart = cell.temp.y; // + cellMargins.top.width;
                        }
                    }

                    if ( true === cell.isVerticalText() ) {
                        verticalCells.push(cell);
                        curGridCol += gridSpan;
                        continue;
                    }

                    cell.content.setStartPage(curPage);
                    cell.content.reset(xContentStart, yContentStart, xContentEnd, yContentEnd, table.logicDocument);
                    const res = cell.content.recalculatePage(0, true, true, false, bHidden);

                    if ( RecalcResultType.RecalcResult2NextPage === res ) {
                        bHeaderNextPage = true;
                        if ( !(bFixedRowsHeight || rowH.isFixed()) || y + rowHValue > table.yLimit ) {
                            bHeaderNextPage = true;
                            break;
                        } else {
                            cell.content.yLimit *= 2;
                            cell.content.recalculatePage(0, true, true, false, bHidden);
                        }
                    }

                    // 获取单元格内容的位置
                    const cellContentBounds = cell.content.getPageBounds(0);
                    const cellContentBoundsBottom = cellContentBounds.bottom + bottomMargin;

                    if ( undefined === headerPage.rowsInfo[curRow].tableRowsBottom
                        || cellContentBoundsBottom > headerPage.rowsInfo[curRow].tableRowsBottom ) {
                        // headerPage.rowsInfo[curRow].tableRowsBottom = cellContentBoundsBottom;
                        const bottom = (bFixedRowsHeight || rowH.isFixed()) ?
                                    rowHValue + y : cellContentBoundsBottom;
                        headerPage.rowsInfo[curRow].tableRowsBottom = bottom; // cellContentBoundsBottom;
                    }

                    // 单元格是否垂直合并
                    if ( VerticalMergeType.Continue === vMerge ) {
                        if ( -1 === maxBottomValueVMerge || cellContentBoundsBottom > maxBottomValueVMerge ) {
                            maxBottomValueVMerge = cellContentBoundsBottom;
                        }
                    }

                    curGridCol += gridSpan;
                }

                if ( true === bHeaderNextPage ) {
                    y = startPos.y;
                    tableHeight = 0;
                    headerPage.bDraw = false;
                    break;
                }

                const tempY = y;
                const tempMaxTopBorder = maxTopBorder[curRow];

                headerPage.rowsInfo[curRow].y[0] = tempY - tempMaxTopBorder;
                // headerPage.rowsInfo[curRow].height[0] = 0;
                headerPage.rowsInfo[curRow].topDy[0] = tempMaxTopBorder;
                headerPage.rowsInfo[curRow].xMin = rowXMin;
                headerPage.rowsInfo[curRow].xMax = rowXMax;
                headerPage.rowsInfo[curRow].maxTopBorder[0] = tempMaxTopBorder;
                headerPage.rowsInfo[curRow].maxBottomBorder = maxBottomBorder[curRow];

                let cellHeight = headerPage.rowsInfo[curRow].tableRowsBottom - y;

                if ( false === bHeaderNextPage && ( TableRowLineRule.AtLeast === rowH.hRule
                    || TableRowLineRule.Exact === rowH.hRule ) && (cellHeight < rowHValue) ) {
                    cellHeight = rowHValue; // rowH.value - maxTopBorder[curRow];
                    headerPage.rowsInfo[curRow].tableRowsBottom = y + cellHeight;
                }

                for (let cellIndex = 0, cellsCount2 = verticalCells.length; cellIndex < cellsCount2; cellIndex++) {
                    let cell = verticalCells[cellIndex];
                    const gridSpan = cell.getGridSpan();
                    const curCell = cell.index;
                    const curGridCol2 = cell.metrics.startGridCol;

                    cell = table.getStartMergedCell(curRow, curGridCol2, gridSpan);
                    cell = headerPage.rows[cell.row.index].getCell(cell.index);

                    const cellMargins = cell.getMargins();
                    const cellMetrics = cell.row.getCellInfo(curCell);

                    const xContentStart = page.x + cellMetrics.xContentStart;
                    const xContentEnd = page.x + cellMetrics.xContentEnd;
                    const yContentStart = y + cellMargins.top.width;
                    let yContentEnd = page.yLimit;

                    // if ( null !== cellSpacing ) {
                    //     if ( table.content.length - 1 === curRow ){
                    //         yContentEnd -= cellSpacing;
                    //     } else {
                    //         yContentEnd -= cellSpacing / 2;
                    //     }
                    // }

                    const vMergeCount = table.getVerticalMergeCount(curRow, curGridCol2, gridSpan);
                    const bottomMargin = table.maxBottomMargin[curRow + vMergeCount - 1];
                    yContentEnd -= bottomMargin;

                    cell.pagesCount = 1;
                    cell.content.setStartPage(curPage);
                    cell.content.reset(0, 0, yContentEnd - yContentStart, 10000, table.logicDocument);
                    cell.temp.xStart = xContentStart;
                    cell.temp.yStart = yContentStart;
                    cell.temp.xEnd = xContentEnd;
                    cell.temp.yEnd = yContentEnd;

                    cell.temp.xCellStart = page.x + cellMetrics.xCellStart;
                    cell.temp.yCellStart = yContentStart - cellMargins.top.width;
                    cell.temp.xCellEnd = page.x + cellMetrics.xCellEnd;
                    cell.temp.yCellEnd = yContentEnd + bottomMargin;

                    const cellPageIndex = curPage - cell.content.getRelativeStartPage();
                    if ( 0 === cellPageIndex ) {
                        cell.content.recalculatePage(cellPageIndex, true, true, false, bHidden);
                    }
                }

                headerPage.rowsInfo[curRow].height[0] = cellHeight + tempMaxTopBorder;

                y += cellHeight;
                tableHeight += cellHeight;
                row.height = cellHeight;

                // console.log(row);

                y += maxBottomBorder[curRow];
                tableHeight += maxBottomBorder[curRow];
            }

            if ( false === bHeaderNextPage ) {
                // tslint:disable-next-line: no-shadowed-variable
                for (let curRow = 0; curRow < table.headerInfo.count; curRow++) {
                    const row = headerPage.rows[curRow];

                    for (let curCell = 0, cellsCount = row.getCellsCount(); curCell < cellsCount; curCell++) {
                        let cell = row.getCell(curCell);
                        const vMergeCount = table.getVerticalMergeCount(curRow,
                            cell.metrics.startGridCol, cell.getGridSpan());

                        if ( 1 < vMergeCount ) {
                            continue;
                        } else {
                            const vMerge = cell.getVMerge();

                            if ( VerticalMergeType.Restart !== vMerge ) {
                                cell = table.getStartMergedCell(curRow, cell.metrics.startGridCol, cell.getGridSpan());
                                cell = headerPage.rows[cell.row.index].getCell(cell.index);
                            }
                        }

                        const cellVertAlign = cell.getVertAlign();
                        const cellPageIndex = curPage - cell.content.getRelativeStartPage();

                        if ( cellPageIndex > cell.pagesCount ) {
                            continue;
                        }

                        if ( 1 < cellPageIndex ) {
                            continue;
                        }

                        const cellMargins = cell.getMargins();
                        const tempCurRow = cell.row.index;
                        let y0 = headerPage.rowsInfo[tempCurRow].y[cellPageIndex] + maxTopBorder[tempCurRow];
                        const y1 = headerPage.rowsInfo[curRow].tableRowsBottom - cellMargins.bottom.width;

                        y0 += cellMargins.top.width;

                        let cellHeight = y1 - y0;
                        const cellContentBounds = cell.content.getPageBounds(cellPageIndex);
                        const contentHeight = cellContentBounds.bottom - cellContentBounds.top;

                        if ( true === cell.isVerticalText() ) {
                            const cellMetrics = cell.row.getCellInfo(cell.index);
                            cellHeight = cellMetrics.xCellEnd - cellMetrics.xCellStart
                                        - cellMargins.left.width - cellMargins.right.width;
                        }

                        let dy = cellHeight - contentHeight;
                        if ( 1 <= dy ) {
                            if ( VertAlignType.Middle === cellVertAlign ) {
                                dy /= 2;
                            } else if ( VertAlignType.Top === cellVertAlign ) {
                                dy = 0;
                            }
                            cell.content.shift(cellPageIndex, 0, dy);
                        }
                    }
                }
            }

            table.logicDocument.bRecalcTableHeader = false;
        } else {
            table.headerInfo.pages[curPage] = new TableHeaderPage();
            table.headerInfo.pages[curPage].bDraw = false;
        }
        table.headerInfo.bHeaderRecalculate = false;

        let bNextPage = false;
        // let nFootnotesHeight     = 0;
        // let savedY: number[] = [];
        // let savedTableHeight: number[] = [];

        // tslint:disable-next-line: no-shadowed-variable
        for (let curRow = firstRow, length = table.content.length; curRow < length; curRow++) {
            if ( ( 0 === curRow && true === table.checkEmptyPages(curPage - 1) ) || curRow !== firstRow
            || ( curRow === firstRow && true === bResetStartElement ) ) {
                table.rowsInfo[curRow] = new TableRowInfo();
                table.rowsInfo[curRow].pages = 1;
                table.rowsInfo[curRow].bFirstPage = true;
                table.rowsInfo[curRow].startPage = curPage;
                table.tableRowsBottom[curRow] = [];
            } else {
                table.rowsInfo[curRow].pages = curPage - table.rowsInfo[curRow].startPage + 1;
            }

            table.tableRowsBottom[curRow][curPage] = y;

            const row = table.content[curRow];
            const cellsCount = row.getCellsCount();
            const cellSpacing = row.getCellSpacing();
            const beforeInfo = row.getBefore();
            // const afterInfo = row.getAfter();
            let curGridCol = beforeInfo.gridBefore;

            // 添加当前行的上边框的宽度
            y += maxTopBorder[curRow];
            tableHeight += maxTopBorder[curRow];

            // 表格与单元格只有是否有间距
            if ( firstRow === curRow ) {
                if ( null != cellSpacing ) {
                    //
                }
            } else {
                const prevCellSpacing = table.content[curRow];
                if ( null !== cellSpacing && null !== prevCellSpacing ) {
                    //
                }
            }

            const rowXMin = row.metrics.xMin;
            const rowXMax = row.metrics.xMax;
            if ( -1 === xMin || rowXMin < xMin ) {
                xMin = rowXMin;
            }

            if ( -1 === xMax || rowXMax > xMax ) {
                xMax = rowXMax;
            }

            let maxTopMargin = 0;

            for (let cellIndex = 0; cellIndex < cellsCount; cellIndex++) {
                const cell = row.getCell(cellIndex);
                const vMerge = cell.getVMerge();
                const cellMargins = cell.getMargins();
                cell.selectionBounds = [];

                if ( VerticalMergeType.Restart === vMerge && cellMargins.top.width > maxTopMargin ) {
                    maxTopMargin = cellMargins.top.width;
                }
            }

            const rowH = row.getHeight();
            let rowHValue = rowH.value + table.maxBottomMargin[curRow] + maxTopMargin;

            if ( null !== cellSpacing ) {
                rowHValue -= cellSpacing;
            }

            let maxBottomValueVMerge = -1;
            const mergeCell: TableCell[] = [];
            const verticalCells: TableCell[] = [];
            let bAllCellsVertical = true;
            let curYContentEnd = 0;

            for (let cellIndex = 0; cellIndex < cellsCount; cellIndex++) {
                let cell = row.getCell(cellIndex);
                const gridSpan = cell.getGridSpan();
                const vMerge = cell.getVMerge();
                let cellMargins = cell.getMargins();

                row.updateCellInfo(cellIndex);

                const cellMetrics = row.getCellInfo(cellIndex);

                const xContentStart = page.x + cellMetrics.xContentStart;
                const xContentEnd = page.x + cellMetrics.xContentEnd;
                let yContentStart = y + cellMargins.top.width;
                let yContentEnd = page.yLimit;

                if ( null !== cellSpacing ) {
                    if ( table.content.length - 1 === curRow ) {
                        yContentEnd -= cellSpacing;
                    } else {
                        yContentEnd -= cellSpacing / 2;
                    }
                }

                const vMergeCount = table.getVerticalMergeCount(curRow, curGridCol, gridSpan);
                const bottomMargin = table.maxBottomMargin[curRow + vMergeCount - 1];
                yContentEnd -= bottomMargin;

                cell.temp.y = yContentStart;
                curYContentEnd = yContentEnd;

                // 多个单元格合并
                if ( 1 < vMergeCount ) {
                    curGridCol += gridSpan;
                    mergeCell.push(cell);
                    continue;
                } else {
                    if ( VerticalMergeType.Restart !== vMerge ) {
                        cell = table.getStartMergedCell(curRow, curGridCol, gridSpan);
                        cellMargins = cell.getMargins();

                        yContentStart = cell.temp.y + cellMargins.top.width;
                    }
                }

                if ( true === cell.isVerticalText() ) {
                    verticalCells.push(cell);
                    curGridCol += gridSpan;
                    continue;
                }

                bAllCellsVertical = false;

                let bCanShift = false;
                const shiftDx = 0;
                let shiftDy = 0;

                // 设置单元格的内容：开始page，位置信息
                if ( ( 0 === cell.row.index && true === table.checkEmptyPages(curPage - 1)) || cell.row.index > firstRow
                    || ( cell.row.index === firstRow && true === bResetStartElement )) {
                    cell.content.setStartPage(curPage);

                    if ( 1 === cell.pagesCount && 1 === cell.content.pages.length ) {
                        const xContentStartOld = cell.content.pages[0].x;
                        const xContentEndOld = cell.content.pages[0].xLimit;
                        const yContentHeightOld = cell.content.pages[0].bounds.bottom
                        - cell.content.pages[0].bounds.top;

                        if ( 1 > Math.abs( xContentStart - xContentStartOld )
                            && 1 > Math.abs( xContentEnd - xContentEndOld )
                            && ( yContentStart + yContentHeightOld < yContentEnd ) ) {
                            bCanShift = true;
                            shiftDy = -cell.content.pages[0].y + yContentStart;
                        }
                    }

                    cell.pagesCount = 1;
                    cell.content.reset(xContentStart, yContentStart, xContentEnd, yContentEnd, table.logicDocument);
                }

                const cellPageIndex = curPage - cell.content.getStartPage();
                if ( cellPageIndex < cell.pagesCount ) {
                    // 排版计算单元格内容
                    // if ( true === bCanShift ) {
                    //     cell.content.shift(0, shiftDx, shiftDy);
                    //     cell.content.updateEndInfo();
                    // } else {
                        const recalcResult = cell.content.recalculatePage(cellPageIndex, true, true, false, bHidden);

                        if ( RecalcResultType.RecalcResult2CurPage & recalcResult ) {
                            let tempRecal = RecalcResultType.RecalResultCurPage;

                            if ( recalcResult & RecalcResultType.RecalResultFlagColumn ) {
                                tempRecal |= RecalcResultType.RecalResultFlagColumn;
                            }

                            return tempRecal;
                        } else if ( RecalcResultType.RecalcResult2NextPage & recalcResult ) {
                            if ( !(bFixedRowsHeight || rowH.isFixed()) || y + rowHValue > table.yLimit ) {
                                cell.pagesCount = cell.content.pages.length + 1;
                                bNextPage = true;
                            } else {
                                cell.content.yLimit *= 2;
                                cell.content.recalculatePage(cellPageIndex, true, true, false, bHidden);
                            }
                        } else if ( RecalcResultType.RecalcResult2End & recalcResult ) {
                            // 计算结束，nothing to do;
                        }
                    // }

                    // 获取单元格内容的位置
                        const cellContentBounds = cell.content.getPageBounds(cellPageIndex);
                        const cellContentBoundsBottom = cellContentBounds.bottom + bottomMargin;

                        if ( undefined === table.tableRowsBottom[curRow][curPage]
                            || cellContentBoundsBottom > table.tableRowsBottom[curRow][curPage] ) {
                            const bottom = ( bFixedRowsHeight || rowH.isFixed() ) ?
                                            rowHValue + y : cellContentBoundsBottom;
                            table.tableRowsBottom[curRow][curPage] = bottom; // cellContentBoundsBottom;
                    }

                    // 单元格是否垂直合并
                        if ( VerticalMergeType.Continue === vMerge ) {
                        if ( -1 === maxBottomValueVMerge || cellContentBoundsBottom > maxBottomValueVMerge ) {
                            maxBottomValueVMerge = cellContentBoundsBottom;
                        }
                    }
                }

                curGridCol += gridSpan;
            }

            if ( undefined === table.tableRowsBottom[curRow][curPage] ) {
                table.tableRowsBottom[curRow][curPage] = y;
            }

            if ( true === bAllCellsVertical && TableRowLineRule.Auto === rowH.hRule ) {
                table.tableRowsBottom[curRow][curPage] = y + 4.5 + table.maxBottomMargin[curRow] + maxTopMargin;
            }

            if ( ( TableRowLineRule.AtLeast === rowH.hRule || TableRowLineRule.Exact === rowH.hRule )
            && y + rowHValue > curYContentEnd
                && ((0 === curRow && 0 === curPage && (table.getDocumentPrev ||
                                                true === table.parent.isTableCellContent())) || curRow !== firstRow) ) {
                bNextPage = true;

                for (let curCell = 0; curCell < cellsCount; curCell++) {
                    const cell = row.getCell(curCell);
                    const vMerge = cell.getVMerge();
                    const vMergeCount = table.getVerticalMergeCount(curRow,
                        cell.metrics.startGridCol, cell.getGridSpan());

                    if ( VerticalMergeType.Continue === vMerge || 1 < vMergeCount ) {
                        continue;
                    }

                    cell.content.startFromNewPage();
                    cell.pagesCount = 2;
                }
            }

            // 当前行的单元格跨页：单元格占据多页，计算步骤：
            // 1. 检查第一页上是否有此行的内容，如果没有，则该行的内容实际是从第2页开始
            // 2. 重新计算所有垂直合并的单元格，
            if ( true === bNextPage ) {
                let bContentOnFirstPage = false;
                let bNoContentOnFirstPage = false;

                for (let index = 0; index < cellsCount; index++) {
                    const cell = row.getCell(index);
                    const vMerge = cell.getVMerge();
                    const vMergeCount = table.getVerticalMergeCount(curRow,
                        cell.metrics.startGridCol, cell.getGridSpan());

                    // 单元格合并
                    if ( VerticalMergeType.Continue === vMerge || 1 < vMergeCount ) {
                        continue;
                    }

                    if ( true === cell.isVerticalText() || true === cell.isContentOnFirstPage() ) {
                        bContentOnFirstPage = true;
                    } else {
                        bNoContentOnFirstPage = true;
                    }
                }

                if ( true === bContentOnFirstPage && true === bNoContentOnFirstPage ) {
                    for (let index = 0; index < cellsCount; index++) {
                        const cell = row.getCell(index);
                        const vMerge = cell.getVMerge();
                        const vMergeCount = table.getVerticalMergeCount(curRow,
                            cell.metrics.startGridCol, cell.getGridSpan());

                        // 单元格合并
                        if ( VerticalMergeType.Continue === vMerge || 1 < vMergeCount ) {
                            continue;
                        }

                        cell.content.startFromNewPage();
                        cell.pagesCount = 2;
                    }

                    bContentOnFirstPage = false;
                }

                table.rowsInfo[curRow].bFirstPage = bContentOnFirstPage;

                // 表格行跨页
                if ( 0 !== curRow && false === table.rowsInfo[curRow].bFirstPage ) {
                    if ( maxBottomValueVMerge > table.tableRowsBottom[curRow - 1][curPage] ) {
                        const diff = maxBottomValueVMerge - table.tableRowsBottom[curRow - 1][curPage];
                        table.tableRowsBottom[curRow - 1][curPage] = maxBottomValueVMerge;
                        table.rowsInfo[curRow - 1].height[curPage] += diff;
                        tableHeight += diff;
                    }
                }

                // 计算垂直合并的单元格
                for (let index = 0, count = mergeCell.length; index < count; index++) {
                    let cell = mergeCell[index];
                    const curCell = cell.index;
                    const gridSpan = cell.getGridSpan();
                    // tslint:disable-next-line: no-shadowed-variable
                    let  curGridCol = cell.metrics.startGridCol;

                    cell = table.getStartMergedCell(curRow, curGridCol, gridSpan);

                    if ( true === cell.isVerticalText() ) {
                        verticalCells.push(cell);
                        curGridCol += gridSpan;
                        continue;
                    }

                    const vMerge = cell.getVMerge();
                    const cellMargins = cell.getMargins();
                    const cellMetrics = row.getCellInfo(curCell);

                    const xContentStart = page.x + cellMetrics.xContentStart;
                    const xContentEnd = page.x + cellMetrics.xContentEnd;
                    const yContentStart = cell.temp.y;
                    let yContentEnd = ( false === bContentOnFirstPage ) ? y : table.pages[curPage].yLimit;

                    // if ( null !== cellSpacing ) {
                    //     if ( table.content.length - 1 === curRow ){
                    //         yContentEnd -= cellSpacing;
                    //     } else {
                    //         yContentEnd -= cellSpacing / 2;
                    //     }
                    // }

                    const vMergeCount = table.getVerticalMergeCount(curRow, curGridCol, gridSpan);
                    const bottomMargin = table.maxBottomMargin[curRow + vMergeCount - 1];
                    yContentEnd -= bottomMargin;

                    if ( ( 0 === cell.row.index && 0 === curPage ) || firstRow < cell.row.index ) {
                        cell.pagesCount = 1;
                        cell.content.setStartPage(curPage);
                        cell.content.reset(xContentStart, yContentStart, xContentEnd, yContentEnd, table.logicDocument);
                    }

                    const cellPageIndex = curPage - cell.content.getStartPage();
                    if ( cellPageIndex < cell.pagesCount ) {

                        const recalcResult = cell.content.recalculatePage(cellPageIndex, true, true, false, bHidden);

                        if ( RecalcResultType.RecalcResult2NextPage === recalcResult ) {
                            if ( !(bFixedRowsHeight || rowH.isFixed()) || y + rowHValue > table.yLimit ) {
                                cell.pagesCount = cell.content.pages.length + 1;
                                bNextPage = true;
                            } else {
                                cell.content.yLimit *= 2;
                                cell.content.recalculatePage(cellPageIndex, true, true, false, bHidden);
                            }
                        }

                        const cellContentBounds = cell.content.getPageBounds(cellPageIndex);
                        const cellContentBoundsBottom = cellContentBounds.bottom + bottomMargin;

                        if ( 0 !== curRow && false === table.rowsInfo[curRow].bFirstPage ) {
                            if ( maxBottomValueVMerge > table.tableRowsBottom[curRow - 1][curPage] ) {
                                const diff = maxBottomValueVMerge - table.tableRowsBottom[curRow - 1][curPage];
                                table.tableRowsBottom[curRow - 1][curPage] = maxBottomValueVMerge;
                                table.rowsInfo[curRow - 1].height[curPage] += diff;
                            }
                        } else {
                            if ( undefined === table.tableRowsBottom[curRow][curPage]
                                || cellContentBoundsBottom > table.tableRowsBottom[curRow][curPage] ) {
                                table.tableRowsBottom[curRow][curPage] = cellContentBoundsBottom;
                            }
                        }

                        if ( VerticalMergeType.Continue === vMerge ) {
                            if ( -1 === maxBottomValueVMerge || cellContentBoundsBottom > maxBottomValueVMerge ) {
                                maxBottomValueVMerge = cellContentBoundsBottom;
                            }
                        }
                    }

                    curGridCol += gridSpan;
                }

                bContentOnFirstPage = false;

                for (let cellIndex = 0; cellIndex < cellsCount; cellIndex++) {
                    const cell = row.getCell(cellIndex);
                    const vMerge = cell.getVMerge();

                    if ( VerticalMergeType.Continue === vMerge ) {
                        continue;
                    }

                    if ( true === cell.isVerticalText() ) {
                        continue;
                    }

                    if ( true === cell.isContentOnFirstPage() ) {
                        bContentOnFirstPage = true;
                        break;
                    }
                }

                table.rowsInfo[curRow].bFirstPage = bContentOnFirstPage;
            }

            if ( true !== table.rowsInfo[curRow].bFirstPage && curPage === table.rowsInfo[curRow].startPage ) {
                table.tableRowsBottom[curRow][curPage] = y;
            }

            const tempY = y;
            const tempMaxTopBorder = maxTopBorder[curRow];

            if ( !cellSpacing ) {
                table.rowsInfo[curRow].y[curPage] = tempY - tempMaxTopBorder;
                table.rowsInfo[curRow].topDy[curPage] = tempMaxTopBorder;
                table.rowsInfo[curRow].xMin = rowXMin;
                table.rowsInfo[curRow].xMax = rowXMax;
                table.rowsInfo[curRow].maxTopBorder[curPage] = tempMaxTopBorder;
                table.rowsInfo[curRow].maxBottomBorder = maxBottomBorder[curRow];
            }

            let cellHeight = table.tableRowsBottom[curRow][curPage] - y;

            if ( false === bNextPage && ( cellHeight < rowHValue ) // || y + rowHValue < curYContentEnd )
                && (TableRowLineRule.AtLeast === rowH.hRule || TableRowLineRule.Exact === rowH.hRule) ) {
                cellHeight = rowHValue;
                table.tableRowsBottom[curRow][curPage] = y + cellHeight;
            }

            // 计算垂直文本的单元格
            for (let index = 0, count = verticalCells.length; index < count; index++) {
                let cell = mergeCell[index];
                const curCell = cell.index;
                const gridSpan = cell.getGridSpan();
                const curGridCol2 = cell.metrics.startGridCol;

                cell = table.getStartMergedCell(curRow, curGridCol2, gridSpan);

                const cellMargins = cell.getMargins();
                const cellMetrics = row.getCellInfo(curCell);

                const xContentStart = page.x + cellMetrics.xContentStart;
                const xContentEnd = page.x + cellMetrics.xContentEnd;
                const yContentStart = cell.temp.y;
                let yContentEnd = table.tableRowsBottom[curRow][curPage];

                // if ( null !== cellSpacing ) {
                //     if ( table.content.length - 1 === curRow ){
                //         yContentEnd -= cellSpacing;
                //     } else {
                //         yContentEnd -= cellSpacing / 2;
                //     }
                // }

                const vMergeCount = table.getVerticalMergeCount(curRow, curGridCol2, gridSpan);
                const bottomMargin = table.maxBottomMargin[curRow + vMergeCount - 1];
                yContentEnd -= bottomMargin;

                if ( ( 0 === cell.row.index && 0 === curPage ) || firstRow < cell.row.index
                    || ( firstRow === cell.row.index && true === bResetStartElement ) ) {
                    cell.pagesCount = 1;
                    cell.content.setStartPage(curPage);
                    cell.content.reset(0, 0, yContentEnd - yContentStart, 10000, table.logicDocument);

                    cell.temp.xStart =  xContentStart;
                    cell.temp.xEnd =  xContentEnd;
                    cell.temp.yStart = yContentStart;
                    cell.temp.yEnd = yContentEnd;

                    cell.temp.xCellStart = page.x + cellMetrics.xContentStart;
                    cell.temp.xCellEnd = page.x + cellMetrics.xContentEnd;
                    cell.temp.yCellStart = yContentStart - cellMargins.top.width;
                    cell.temp.yCellEnd = yContentEnd + bottomMargin;
                }

                const cellPageIndex = curPage - cell.content.getStartPage();
                if ( 0 === cellPageIndex ) {
                    cell.content.recalculatePage(cellPageIndex, true, true, false, bHidden);
                }
            }

            if ( !cellSpacing ) {
                table.rowsInfo[curRow].height[curPage] = cellHeight + tempMaxTopBorder;
            }

            y += cellHeight;
            tableHeight += cellHeight;
            row.height += cellHeight;

            y += maxBottomBorder[curRow];
            tableHeight += maxBottomBorder[curRow];

            if ( true === bNextPage ) {
                lastRow = curRow;
                table.pages[curPage].lastRow = curRow;

                if ( -1 === table.headerInfo.pageIndex && 0 < table.headerInfo.count
                    && curRow >= table.headerInfo.count ) {
                    table.headerInfo.pageIndex = curPage;
                }

                break;
            } else if ( curRow === table.content.length - 1 ) {
                lastRow = table.content.length - 1;
                table.pages[curPage].lastRow = lastRow;
            }
        }

        // tslint:disable-next-line: no-shadowed-variable
        for (let curRow = firstRow; curRow <= lastRow; curRow++) {
            const row = table.content[curRow];
            const cellsCount = row.getCellsCount();

            for (let curCell = 0; curCell < cellsCount; curCell++) {
                let cell = row.getCell(curCell);
                const vMergeCount = table.getVerticalMergeCount(curRow, cell.metrics.startGridCol, cell.getGridSpan());

                if ( 1 < vMergeCount && curRow !== lastRow ) {
                    continue;
                } else {
                    const vMerge = cell.getVMerge();

                    if ( VerticalMergeType.Restart !== vMerge ) {
                        cell = table.getStartMergedCell(curRow, cell.metrics.startGridCol, cell.getGridSpan());
                    }
                }

                const cellMargins = cell.getMargins();
                const cellVertAlign = cell.getVertAlign();
                const cellPageIndex = curPage - cell.content.getRelativeStartPage();

                if ( cellPageIndex >= cell.pagesCount ) {
                    continue;
                }

                const tempCurRowIndex = cell.row.index;
                if ( ( 1 < cellPageIndex /*|| VertAlignType.Top === cellVertAlign*/ ) ||
                    ( 1 === cellPageIndex && true === table.rowsInfo[tempCurRowIndex].bFirstPage )) {
                    continue;
                }

                let tempY = table.rowsInfo[tempCurRowIndex].y[curPage] + maxTopBorder[tempCurRowIndex];
                tempY += cellMargins.top.width;

                // const diffY = (curRow - tempCurRowIndex - 1) * (cellMargins.bottom.width + cellMargins.top.width);
                const y1 = table.tableRowsBottom[curRow][curPage] - cellMargins.bottom.width; // diffY;
                const top = (curRow !== tempCurRowIndex ? cellMargins.top.width : 0);
                let cellHeight = y1 - tempY - top;

                const cellContentBounds = cell.content.getPageBounds(cellPageIndex);
                const contentHeight = cellContentBounds.bottom - cellContentBounds.top;

                // const dy = 0;

                if ( true === cell.isVerticalText() ) {
                   const cellMetrics = row.getCellInfo(curCell);
                   cellHeight = cellMetrics.xCellEnd - cellMetrics.xCellStart
                                - cellMargins.left.width - cellMargins.right.width;
                }

                let dy = cellHeight - contentHeight;
                if ( 1 <= dy ) {
                    if ( VertAlignType.Middle === cellVertAlign ) {
                        dy /= 2;
                    } else if ( VertAlignType.Top === cellVertAlign ) {
                        dy = -top;
                    }

                    cell.content.shift(cellPageIndex, 0, dy);
                }
            }
        }

        // 计算当前页面上表格的下边框
        let curRow = lastRow;
        if ( 0 === curRow && false === table.rowsInfo[curRow].bFirstPage && 0 === curPage ) {
            table.pages[0].maxBottomBorder = 0;
            table.pages[0].bottomBorders = [];
        } else {
            // 如果此页面上的最后一行没有内容，那么计算上一行的边框
            if ( false === table.rowsInfo[curRow].bFirstPage && curPage === table.rowsInfo[curRow].startPage ) {
                curRow--;
            }

            // tslint:disable-next-line: no-shadowed-variable
            let maxBottomBorder = 0;
            const bottomBorders: DocumentBorder[] = [];

            if ( curRow >= table.pages[curPage].firstRow ) {
                const row = table.content[curRow];
                const cellsCount = row.getCellsCount();

                if ( curRow === table.content.length - 1 ) {

                    for (let index = 0; index < cellsCount; index++) {
                        let cell = row.getCell(index);
                        if ( VerticalMergeType.Continue === cell.getVMerge() ) {
                            cell = table.getStartMergedCell(curRow,
                                row.getCellInfo(index).startGridCol, cell.getGridSpan());
                        }

                        const borderInfo = cell.getBorderInfo().bottom;

                        for (let borderId = 0, length = borderInfo.length; borderId < length; borderId++) {
                            const border = borderInfo[borderId];
                            if ( TableBorderLineStyle.Single === border.value && maxBottomBorder < border.size ) {
                                maxBottomBorder = border.size;
                            }

                            bottomBorders.push(border);
                        }
                    }
                } else {
                    for (let index = 0; index < cellsCount; index++) {
                        let cell = row.getCell(index);
                        if ( VerticalMergeType.Continue === cell.getVMerge() ) {
                            cell = table.getStartMergedCell(curRow,
                                row.getCellInfo(index).startGridCol, cell.getGridSpan());

                            if ( null === cell ) {
                                bottomBorders.push(tableBorders.bottom);
                                continue;
                            }
                        }

                        const border = cell.getBorders().bottom;
                        const resultBorder = table.compareBorders(border, tableBorders.bottom, false, true);

                        if ( TableBorderLineStyle.Single === resultBorder.value
                            && maxBottomBorder < resultBorder.size ) {
                            maxBottomBorder = resultBorder.size;
                        }

                        bottomBorders.push(resultBorder);
                    }
                }
            }
            table.pages[curPage].maxBottomBorder = maxBottomBorder;
            table.pages[curPage].bottomBorders = bottomBorders;
        }

        table.pages[curPage].bounds.bottom = table.pages[curPage].bounds.top + tableHeight;
        table.pages[curPage].bounds.left = xMin + table.pages[curPage].x;
        table.pages[curPage].bounds.right = xMax + table.pages[curPage].x;
        table.pages[curPage].height = tableHeight;

        if ( true === bNextPage ) {
            const rowIndex = table.pages[curPage].lastRow;

            if ( false === table.rowsInfo[rowIndex].bFirstPage ) {
                table.pages[curPage].lastRow = rowIndex - 1;
            } else {
                table.pages[curPage].bLastRowSplit = true;
            }
        }

        table.bTurnOffRecalc = false;

        // table.bounds = table.pages[table.pages.length - 1].bounds;

        if ( true === bNextPage ) {
            return RecalcResultType.RecalResultNextPage;
        } else {
            return RecalcResultType.RecalResultNextElement;
        }
    }

    protected recalculatePositionX(curPage: number): void {
        const table = this.table;
        const tablePr = table.property;
        const pageLimits = table.parent.getPageLimits(table.pageNum);
        const pageFields = table.parent.getPageFields(table.pageNum);
        const documentPageLimits = table.logicDocument.getPageLimits(table.getAbsoluteStartPage());
        const documentPageFields = table.logicDocument.getPageFields(table.getAbsoluteStartPage());

        if ( true === table.isInline() ) {
            const page = table.pages[curPage];

            if ( 0 === curPage ) {
                // table.anchorPosition.calcX = table.xOrigin; // + tablePr.tableInd;
                // table.anchorPosition.setX(table.tableSumGrid[table.tableSumGrid.length - 1], table.xOrigin,
                //     documentPageFields.x, documentPageFields.xLimit, documentPageLimits.xLimit,
                //     pageLimits.x, pageLimits.xLimit);
            }

            switch ( tablePr.alignment ) {
                case AlignType.Left : {
                    page.x = page.xOrigin ; // + tablePr.tableInd;
                    break;
                }

                case AlignType.Center : {
                    const tableWidth = table.tableSumGrid[table.tableSumGrid.length - 1];
                    const rangWidth = page.xLimit - page.xOrigin;

                    page.x = page.xOrigin + ( rangWidth - tableWidth ) / 2;
                    break;
                }

                case AlignType.Right : {
                    const tableWidth = table.tableSumGrid[table.tableSumGrid.length - 1];

                    page.x = page.xLimit - tableWidth + 1.9; // 表格右侧始终添加1.9毫米
                    break;
                }
            }
        }

    }

    protected recalculatePositionY(curPage: number): void {
        // const table = this.table;
        // const tablePr = table.property;
        // const pageLimits = table.parent.getPageLimits(table.pageNum + curPage);
        // const pageFields = table.parent.getPageFields(table.pageNum + curPage);
        // const documentPageLimits = table.logicDocument.getPageLimits(table.getAbsoluteStartPage() + curPage);
        // const documentPageFields = table.logicDocument.getPageFields(table.getAbsoluteStartPage() + curPage);
    }

    protected recalculateAllRows(curPage: number): void {
        const table = this.table;
        for (let curRow = 0, length = table.content.length; curRow < length; curRow++) {
            const row = table.content[curRow];
            for (let curCell = 0; curCell < row.getCellsCount(); curCell++) {
                const cell = row.getCell(curCell);
                const vMerge = cell.getVMerge();

                if ( VerticalMergeType.Continue === vMerge ) {
                    continue;
                }

                cell.pagesCount = 1;
                cell.content.setStartPage(curPage);
                cell.content.reset(0, 0, table.xLimit, table.yLimit, table.logicDocument);
                cell.content.recalculatePage(curPage, true, true, false, true);
            }
        }
    }

}
