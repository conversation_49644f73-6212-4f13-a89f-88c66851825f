import Document from './Document';
import { ReviewInfo, Revision } from './Revision';
import { IRevisionSetting, REVISION_LEVEL, RevisionStyle, RevisionChangeType } from '../../common/commonDefines';
import DocumentContentElementBase from './DocumentContentElementBase';
import Paragraph from './Paragraph';
import { Table } from './Table';

// export interface rev {
//     revisionId: number;
//     revisionList: Revision[];
// }

export class RevisionsManager {
    private parent: Document;
    private curRevSetting: IRevisionSetting;
    private revisionList: Map<number, Revision[]>; // Revision[][];
    private checkElements: Map<number, number>;
    private userList: string[];

    constructor(document: Document) {
        this.curRevSetting = undefined;
        this.parent = document;
        this.revisionList = new Map<number, Revision[]>();
        this.checkElements = new Map<number, number>();
        this.userList = [];
    }

    public setRevision(revisonSetting: IRevisionSetting): void {
        if ( null == revisonSetting ) {
            return ;
        }

        this.curRevSetting = {
            userName: revisonSetting.userName,
            userId: revisonSetting.userId,
            description: revisonSetting.description,
            style: revisonSetting.style ? revisonSetting.style : RevisionStyle.SingleLine,
            color: revisonSetting.color && '' !== revisonSetting.color ? revisonSetting.color : '活力橙',
            level: revisonSetting.level ? revisonSetting.level : REVISION_LEVEL[0],
        };
    }

    // public getCurRevision(): IRevisionSetting {
    //     this.startCollectRevisions();

    //     return this.curRevSetting;
    // }

    public addRevision(elementId: number, revision: Revision): void {
        this.checkRevisionId(elementId);

        // revision.setUserName(this.curRevSetting.name);
        // revision.setUserId(this.curRevSetting.name);
        // revision.setDescription(this.curRevSetting.description);
        // revision.setStyle(this.curRevSetting.style);
        // revision.setColor(this.curRevSetting.color);
        // revision.setLevel(this.curRevSetting.level);

        this.revisionList.get(elementId).push(revision);
    }

    /**
     * 获取修订统计信息
     * @returns 修订统计信息
     */
    public getRevisionCount(): {
        total: number;           // 总修订数
        additions: number;       // 新增修订数
        deletions: number;       // 删除修订数
        users: string[];         // 参与修订的用户列表
    } {
        const stats = {
            total: 0,
            additions: 0,
            deletions: 0,
            users: []
        };
        
        const userSet = new Set<string>();
        
        // 从父文档获取所有修订数据
        const allRevisions = this.parent.getAllRevision();
        
        allRevisions.forEach(rev => {
            stats.total++;
            userSet.add(rev.userName);
            
            // 统计各类型数量
            if (this.isAdditionType(rev.type)) {
                stats.additions++;
            } else {
                stats.deletions++;
            }
        });
        
        stats.users = Array.from(userSet);
        return stats;
    }

    /**
     * 获取修订详细信息
     * @param author 可选，指定修订者姓名进行过滤
     * @returns 修订详细信息数组，包含userName、userId、time、level、type、value字段
     */
    public getRevisionDetails(author?: string): Array<{
        userName: string;        // 修订者姓名
        userId: string;          // 修订者ID
        time: string;            // 修订时间 (ISO格式)
        level: number;           // 修订级别
        type: string;            // 修订类型：'add' | 'remove'
        value: string;           // 修订内容
    }> {
        const result = [];
        
        // 从父文档获取所有修订数据
        const allRevisions = this.parent.getAllRevision();
        
        allRevisions.forEach(rev => {
            // 如果指定了作者，进行过滤
            if (author && rev.userName !== author) {
                return;
            }
            
            // 转换为JSON格式
            result.push({
                userName: rev.userName,
                userId: rev.userId,
                time: rev.time.toISOString(),
                level: rev.level || 0,
                type: this.isAdditionType(rev.type) ? 'add' : 'remove',
                value: rev.value || ''
            });
        });
        
        return result;
    }

    /**
     * 判断是否为新增类型的修订
     * @param type 修订类型
     * @returns 是否为新增类型
     */
    private isAdditionType(type: RevisionChangeType): boolean {
        return type === RevisionChangeType.TextAdd || 
               type === RevisionChangeType.ParaAdd || 
               type === RevisionChangeType.RowsAdd;
    }

    /**
     * 判断是否为删除类型的修订
     * @param type 修订类型
     * @returns 是否为删除类型
     */
    private isRemovalType(type: RevisionChangeType): boolean {
        return type === RevisionChangeType.TextRemove || 
               type === RevisionChangeType.ParaRemove || 
               type === RevisionChangeType.RowsRemove;
    }

    public addRevisionByReviewInfo(element: DocumentContentElementBase, info: ReviewInfo): void {
        this.checkElement(element);
        const revision = new Revision(info.getUserName(), info.getUserId(), info.getStyle(),
                    info.getColor(), info.getLevel(), info.getDescription(), info.getTime());
        this.addRevision(element.id, revision);
    }

    public startCollectRevisions(): void {
        if ( !this.curRevSetting ) {
            this.curRevSetting = {
                userName: '用户1',
                userId: '0001',
                description: '',
                style: RevisionStyle.SingleLine,
                color: '活力橙',
                level: REVISION_LEVEL[0],
            };
        }
    }

    public endCollectRevisions(): void {
        ;
    }

    public checkElement(element: DocumentContentElementBase): void {
        if ( !(element instanceof Paragraph) && !(element instanceof Table) && !this.curRevSetting ) {
            return ;
        }

        const elementId = element.id;
        if ( !this.checkElements.has(elementId) ) {
            this.checkElements.set(elementId, 1); // [elementId] = 1;
        } else {
            const count = this.checkElements.get(elementId) + 1; // [elementId]++;
            this.checkElements.delete(elementId);
            this.checkElements.set(elementId, count + 1);
        }
    }

    public hasElement(elementId: number): boolean {
        return this.checkElements.has(elementId);
    }

    public completeTrackChanges(): boolean {
        let bChecked = false;
        this.checkElements.forEach((value, key) => {
            const element = this.getElementById(key);

            if ( element && element instanceof Paragraph ) {
                element.checkRevisionsChanges();
            }
        });

        // this.checkElements.clear();

        return bChecked;
    }

    public getCurrentUserId(): string {
        if ( this.curRevSetting ) {
            return this.curRevSetting.userId;
        }

        return undefined;
    }

    public getCurrentUserName(): string {
        if ( this.curRevSetting ) {
            return this.curRevSetting.userName;
        }

        return undefined;
    }

    public getCurrentColor(): string {
        if ( this.curRevSetting ) {
            return this.curRevSetting.color;
        }

        return undefined;
    }

    public getCurrentDescription(): string {
        if ( this.curRevSetting ) {
            return this.curRevSetting.description;
        }

        return undefined;
    }

    public getCurrentLevel(): number {
        if ( this.curRevSetting ) {
            return this.curRevSetting.level;
        }

        return undefined;
    }

    public getCurrentStyle(): number {
        if ( this.curRevSetting ) {
            return this.curRevSetting.style;
        }

        return RevisionStyle.SingleLine;
    }

    // public getUniqueName(name?: string): string {
    //     if ( null != name ) {
    //         if (true === this.checkNewControlName(name)) {
    //             return name;
    //         }
    //     }

    //     let newControlName = NEW_CONTROL_NAME.get(type);
    //     const pasteNewControls = this.pasteNewControls;
    //     for (let number = 1; number < Number.MAX_SAFE_INTEGER; number++) {
    //         const temp = newControlName + number;
    //         if ( false === this.newControlNames.has(temp) ) {
    //             if (pasteNewControls.length !== 0 && pasteNewControls
    //                 .find((control) => control.control.getNewControlName() === temp)) {
    //                 continue;
    //             }
    //             newControlName = temp;
    //             break;
    //         }
    //     }

    //     // console.log(newControlName)

    //     return newControlName;
    // }

    public getUniqueID(id?: string): string {
        if ( null != id ) {
            if (true === this.checkId(id)) {
                return id;
            }
        }

        // const start = bName ? '用户' : '';
        let bSuccess = false;
        const count = this.userList.length;
        for (let number = 1; number < Number.MAX_SAFE_INTEGER; number++) {
            const temp = '000' + number;
            // tslint:disable-next-line: prefer-for-of
            for (let index = 0; index < count; index++) {
                if ( temp !== this.userList[index]) {
                    id = temp;
                    bSuccess = true;
                    return id;
                }
            }

            if ( 0 === count || bSuccess ) {
                break;
            }
        }

        return id;
    }

    public checkId(id: string): boolean {
        if ( 0 === id.length ) {
            return false;
        }

        // tslint:disable-next-line: prefer-for-of
        for (let index = 0; index < this.userList.length; index++) {
            if ( id === this.userList[index]) {
                return false;
            }
        }

        return true;
    }

    public getRevisionsById(id: string): Revision[] {
        const revs: Revision[] = [];

        // for (let index = 0, count = this.revisionList.size; index < count; index++) {
        //     const element = this.revisionList.get;
        //     ;
        // }

        if ( !this.revisionList || 0 === this.revisionList.size ) {
            return revs;
        }

        this.revisionList.forEach((rev) => {
            if ( rev && 0 < rev.length ) {
                rev.forEach((item) => {
                    if ( item.getUserId() === id ) {
                        revs.push(item);
                    }
                });
            }
        });

        return revs;
    }

    public getCurrentRevsionSetting(): IRevisionSetting {
        return this.curRevSetting;
    }

    // public canDelete(): boolean {
    //     const selection = this.parent.getSelection();

    //     if ( selection ) {
    //         let startPos = selection.startPos; // 段落开始
    //         let endPos = selection.endPos; // 段落结束

    //         if ( startPos > endPos ) {
    //             const temp = startPos;
    //             startPos = endPos;
    //             endPos = temp;
    //         }

    //         for (let index = startPos; index <= endPos; index++) {
    //             const revs = this.revisionList.get(index);

    //             if ( revs && 0 < revs.length ) {
    //                 const element = this.parent.content[index];
    //                 if ( element && element.isParagraph() ) {
    //                     ;
    //                 }
    //             }
    //         }
    //     }

    //     return true;
    // }

    public getRevisionsList(): Map<number, Revision[]> {
        return this.revisionList;
    }

    public continueTrackRevisions(): void {
        this.checkElements.forEach((vaule, id) => {
            this.trackChangesForSingleElement(id);
        });
    }

    public haveRevisionChanges(): boolean {
        if ( !this.revisionList || 0 === this.revisionList.size ) {
            return false;
        }

        return true;
    }

    public clear(): void {
        this.revisionList.clear();
    }

    public getAllChangesLogicDocuments(): any[] {
        const logicDocuments = [];
        for (const elementId of this.checkElements ) {
            const element = this.getElementById(elementId[0]);
            if ( element ) {
                const topDoc = element.getTopDocument();
                if ( -1 === logicDocuments.indexOf(topDoc) ) {
                    logicDocuments.push(topDoc);
                }
            }
        }

        return logicDocuments;
    }

    private checkRevisionId(elementId: number): boolean {
        if ( !this.revisionList.get(elementId) ) {
            this.revisionList.set(elementId, []);
        }

        return true;
    }

    private trackChangesForSingleElement(elementId: number): boolean {
        if ( this.checkElements.has(elementId) ) {
            this.checkElements.delete(elementId);
        }

        const element = this.getElementById(elementId);
        if ( element ) {
            element.checkRevisionsChanges();
        }

        return false;
    }

    private getElementById(elementId: number): DocumentContentElementBase {
        let length = this.parent.getElementsCount();
        for (let index = 0; index < length; index++) {
            const item = this.parent.content[index];
            if ( item ) {
                const element = item.getElementById(elementId);
                if ( element ) {
                    return element;
                }
            }
        }

        const sectHdrFtr = this.parent.getSectionHdrFtr(0, false);
        const header = sectHdrFtr.header;
        const footer = sectHdrFtr.footer;
        length = header ? header.content.getElementsCount() : 0;
        for (let index = 0; index < length; index++) {
            const item = header.content.content[index];
            if ( item ) {
                const element = item.getElementById(elementId);
                if ( element ) {
                    return element;
                }
            }
        }

        length = footer ? footer.content.getElementsCount() : 0;
        for (let index = 0; index < length; index++) {
            const item = footer.content.content[index];
            if ( item ) {
                const element = item.getElementById(elementId);
                if ( element ) {
                    return element;
                }
            }
        }

        return null;
    }
}
