import { isMacOs } from '@/common/commonDefines';
import { checkFontAvailability } from '../../../common/commonMethods';

export let fonts = [
    {
        label: '字体',
        key: 'fontFamily',
        options: [{name: '宋体', value: '宋体', macValue: 'STSong'}, {name: '黑体', value: '黑体', macValue: 'STHeiti'},
        {name: '仿宋', value: '仿宋', macValue: 'FangSong_GB2312'}, {name: '楷体', value: '楷体', macValue: 'STKaiti'},
        {name: '幼圆', value: '幼圆', macValue: 'Hiragino Sans GB'},
        {name: 'Times New Roman', value: 'Times New Roman', macValue: 'Times New Roman'},
        ],
    }, {
        label: '字号',
        key: 'fontSize',
        options: [
            {name: '初号', value: 58.7},
            {name: '小初', value: 48},
            {name: '一号', value: 34.7},
            {name: '小一', value: 32},
            {name: '二号', value: 29.3},
            {name: '小二', value: 24},
            {name: '三号', value: 21.3},
            {name: '小三', value: 20},
            {name: '四号', value: 18.7},
            {name: '小四', value: 16},
            {name: '五号', value: 14},
            {name: '小五', value: 12},
            {name: '六号', value: 10},
            {name: '小六', value: 8.7},
            {name: '七号', value: 7.3},
            {name: '八号', value: 6.7},
            {name: '8', value: 10.7},
            {name: '9', value: 12},
            {name: '10', value: 13.3},
            {name: '11', value: 14.7},
            {name: '12', value: 16},
            {name: '14', value: 18.7},
            {name: '16', value: 21.3},
            {name: '18', value: 24},
            {name: '20', value: 26.7},
            {name: '22', value: 29.3},
            {name: '24', value: 32},
            {name: '26', value: 34.7},
            {name: '28', value: 37.3},
            {name: '36', value: 48},
            {name: '48', value: 64},
            {name: '72', value: 96},
        ],
    }, {
        label: '字形',
        key: 'fontStyle',
        options: [{name: '常规', value: 0}, {name: '斜体', value: 1}, {name: '粗体', value: 2}, {name: '粗斜体', value: 3}],
    },
];
if (isMacOs === true && checkFontAvailability('16px 宋体') === true) {
    fonts[0].options[0]['macValue'] = '宋体';
}
