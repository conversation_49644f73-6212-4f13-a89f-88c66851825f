import { DocumentCore } from '../../model/DocumentCore';
import { layer, message } from '../Message';
import { FormatWriter } from '../../format/writer/writer';
import DocumentContentElementBase from '../../model/core/DocumentContentElementBase';
import * as J<PERSON><PERSON><PERSON> from '../jszip';
import { ErrorMessages, EquationType, FileSaveType, EDITOR_VERSION, ToolbarIndex, ResultType, NewControlType, FILE_HEADER_VERSION, FILE_HEADER_VERSION_GRACEFUL_DEGRADATION, IDocumentBuffer, InsertFilePositionType, FILE_HEADER_ENCODE_VERSION, videoIconStr, audioIconStr, ImageMediaType, FILE_HEADER_VERSION2 } from '../commonDefines';
import { Reader } from '../../format/reader/reader';
import { EmrEditor } from '../../components/editor/Main';
import { saveAs } from 'file-saver';
import { getDocumentCoreRecorder, getDocumentCoreReplayer } from '../../model/DocumentCoreRecordReplay';
import {PasteBtn} from './PasteBtn';
import { analyzeContentBuffer, analyzeContentBuffer2, readerFileEditorVersionError, resetDocumentDefaultFont } from '../commonMethods';

import { pasteTest, readFile } from '../AutoTestMethod';

export interface IXmlProps {
    documentCore: DocumentCore;
    properties?: Map<string, any>;
    selectedArea?: DocumentContentElementBase[];
}
export class ToolbarAction {
    private xmlProps: IXmlProps;
    private documentCore: DocumentCore;
    private host: EmrEditor;
    private copyPaste: PasteBtn;
    constructor(host: EmrEditor) {
        this.host = host;
        this.documentCore = host.state.documentCore;
    }

    public createNew(): Promise<boolean> {
        return new Promise((resolve, reject) => {
            if (this.documentCore.getBDisableSaveDialog() === false && this.documentCore.isDirty() === true) {
                layer.confirm('当前文档未保存。需要保存吗？')

                .then((result) => {
                    this.generate();
                    resolve(false);
                })
                .catch((error) => {
                    this.documentCore.createNewDocument();
                    resolve(true);
                });

            } else {
                this.documentCore.createNewDocument();
                resolve(true);
            }
        });
    }

    public fileChange(inputTarget: HTMLInputElement): Promise<boolean> {

        const file = inputTarget.files[0];
        const id = inputTarget.id;
        const fileReader = new FileReader();
        const formatWriter = new FormatWriter();

        return new Promise((resolve, reject) => {
            switch (id) {
                case 'openFile': {
                    const fileType = file.name ? file.name.slice(file.name.lastIndexOf('.')) : null;
                    const newZip = new JSZip();

                    switch (fileType) {
                        case '.txt': {
                            // create new document first
                            this.documentCore.createNewDocument(true);

                            // tslint:disable-next-line: no-console
                            // console.time('read time');
                            this.readTxtFile(fileReader, file, inputTarget);
                            resolve(true);

                            break;
                        }
                        case '.hz':
                        case '.apo': {
                            this.documentCore.createNewDocument(true);

                            this.readZstFile(fileReader, file, inputTarget, newZip).then(() => {
                                // if (this.host.externalEvent) {
                                //     this.host.externalEvent.nsoFileOpenCompleted(null);
                                // }
                                this.host.openCompleted();
                                resolve(true);
                            });

                            break;
                        }

                        // case '.xml': {

                        //   fileReader.readAsText(file);
                        //   fileReader.onloadend = () => {
                        //     const xmlFile: string = fileReader.result as string;
                        //     const parser = new DOMParser();
                        //     console.time('parsexml')
                        //     let result = parser.parseFromString(xmlFile, 'text/xml');
                        //     console.timeEnd('parsexml')
                        //     console.log(result)
                        //   };

                        //   break;
                        // }

                        default: {
                            // alert(ErrorMessages.UnSupportedFileType);
                            message.error(ErrorMessages.UnSupportedFileType);
                            break;
                        }
                    }

                    break;
                }

                case 'insertFile': {
                    const fileType = file.name ? file.name.slice(file.name.lastIndexOf('.')) : null;
                    const newZip = new JSZip();

                    switch (fileType) {
                        case '.txt': {
                            // tslint:disable-next-line: no-console
                            // console.time('read time');
                            this.readTxtFile(fileReader, file, inputTarget, true);

                            break;
                        }
                        case '.hz':
                        case '.apo': {
                            const type = this.documentCore.getInsertFilePositionType();
                            // if (  false === this.documentCore.canInput() ||
                            //     this.documentCore.getCurrentNewControl() instanceof NewControlNumer ) {
                            if ( /*InsertFilePositionType.HeaderFooter === type
                                ||*/ InsertFilePositionType.Selection === type
                                || InsertFilePositionType.OtherNewControlsExceptTextAndSection === type
                                || false === this.documentCore.canInput()
                            ) {
                                // window.alert('插入位置点非法，插入文件失败');
                                message.error('插入位置点非法，插入文件失败');
                            } else {
                                this.readZstFile(fileReader, file, inputTarget, newZip, true, type);
                            }

                            break;
                        }

                        default: {
                            break;
                        }

                    }

                    break;
                }

                case 'insertImage': {
                    fileReader.readAsDataURL(file);

                    fileReader.onload = () => {
                        const imgSrc = fileReader.result;

                        const img = new Image();
                        img.src = imgSrc as string;

                        const maxWidth = this.documentCore.getMaxWidth(true);
                        const maxHeight = this.documentCore.getMaxHeight(true);
                        // console.log(maxWidth, maxHeight);

                        // in signature box?
                        let ratio = 1;
                        const curControl = this.documentCore.getCurrentNewControl();
                        // console.log(curControl);
                        if (curControl != null) {
                            const parentControl = curControl.getParent();
                            if (parentControl != null && parentControl.getType() === NewControlType.SignatureBox) {
                                const tempRatio = parentControl.getSignatureRatio();
                                if (isNaN(tempRatio) === false) {
                                    ratio = parentControl.getSignatureRatio();
                                }
                            }
                        }
                        // console.log(ratio)

                        img.onload = () => {

                            img.width *= ratio;
                            img.height *= ratio;

                            // if image width/height exceeds limits, shrink them to fit
                            if (img.width > maxWidth) {
                                const wRatio = maxWidth / img.width;
                                const hRatio = maxHeight / img.height;
                                if (img.height * wRatio > maxHeight) {
                                    img.height = maxHeight;
                                    img.width *= hRatio;
                                } else {
                                    img.width = maxWidth;
                                    img.height *= wRatio;
                                }
                            } else if (img.height > maxHeight) {
                                const wRatio = maxWidth / img.width;
                                const hRatio = maxHeight / img.height;
                                if (img.width * hRatio > maxWidth) {
                                    img.width = maxWidth;
                                    img.height *= wRatio;
                                } else {
                                    img.height = maxHeight;
                                    img.width *= hRatio;
                                }
                            }

                            this.addInlineImage(img.width, img.height, img.src);

                            // trick input field to "onChange" every time
                            inputTarget.value = '';
                        };

                        resolve(true);
                    }; // onload()
                    break;
                }

                case 'insertVideo': {
                    if (file.size > 1048576) { // 1MB
                        message.error('插入的视频文件最大为1MB');
                        resolve(false);
                        break;
                    }
                    fileReader.readAsDataURL(file);

                    fileReader.onload = () => {
                        this.addVideoOrAudio(ImageMediaType.Video, fileReader.result as string);
                        resolve(true);
                    }; // onload()
                    break;
                }

                case 'insertAudio': {
                    if (file.size > 1048576) { // 1MB
                        message.error('插入的音频文件最大为1MB');
                        resolve(false);
                        break;
                    }
                    fileReader.readAsDataURL(file);

                    fileReader.onload = () => {
                        this.addVideoOrAudio(ImageMediaType.Audio, fileReader.result as string);
                        resolve(true);
                    }; // onload()
                    break;
                }

                case 'apoToZip': {
                    fileReader.readAsArrayBuffer(file);

                    fileReader.onloadend = async () => {
                        const reader = new Reader(this.documentCore.getDocument());
                        const rawBuffer = fileReader.result as ArrayBuffer;

                        // separate rawBuffer to two parts

                        const {headerBuffer, contentBuffer}: IDocumentBuffer =
                            reader.separateRawBuffer(new Uint8Array(rawBuffer));
                        const {versionNum} = reader.getHeaderBufferItems(headerBuffer);


                        let newBlob = null;
                        const newFileVersions = [FILE_HEADER_VERSION, FILE_HEADER_VERSION_GRACEFUL_DEGRADATION,
                            FILE_HEADER_ENCODE_VERSION, FILE_HEADER_VERSION2];
                        if (newFileVersions.includes(versionNum) === true) {

                          newBlob = new Blob([contentBuffer], {type: 'application/apollo-zip'});
                          saveAs(newBlob, file.name.slice(0, file.name.lastIndexOf('.')) + '.zip');

                        } else {
                            readerFileEditorVersionError(this.documentCore.getDocument());
                        //   const zstdCodec = await getZstdCodec();
                        //   zstdCodec.run( (zstd) => {
                        //     const streaming = new zstd.Streaming();

                        //     const deCompressed = streaming.decompress(contentBuffer);
                        //     newBlob = new Blob([deCompressed.buffer], {type: 'application/apollo-zip'});
                        //     saveAs(newBlob, file.name.slice(0, file.name.lastIndexOf('.')) + '.zip');

                        //   });
                        }

                    };
                    break;
                }

                case 'zipToApo': {

                    fileReader.readAsArrayBuffer(file);

                    fileReader.onloadend = () => {
                        const buffer = fileReader.result as ArrayBuffer;


                        // TODO
                        // const reader = new Reader(this.documentCore.getDocument());
                        // // separate rawBuffer to two parts
                        // const {headerBuffer, contentBuffer}: IDocumentBuffer =
                        //     reader.separateRawBuffer(new Uint8Array(buffer));
                        // const {versionNum, documentNum, bHtmlFile} = reader.getHeaderBufferItems(headerBuffer);

                        const revisedBuffer = formatWriter.addFileHeader(buffer);

                        // const newBlob: any = new Blob([compressed.buffer], {type: 'application/apollo-zstd'});
                        const fileType: string = FileSaveType.APO + ';version=' + EDITOR_VERSION;
                        const newBlob = new Blob([revisedBuffer], {type: fileType});

                        saveAs(newBlob, file.name.slice(0, file.name.lastIndexOf('.')) + '.apo');
                        // zstdCodec.run( (zstd) => {
                        //     const streaming = new zstd.Streaming();
                        //     // default 3. 1 - 22. https://facebook.github.io/zstd/zstd_manual.html
                        //     const compressionLevel = 3;
                        //     // tslint:disable-next-line: no-console
                        //     const chunks = [];
                        //     const threshold = 5897510;
                        //     // const threshold = 1024000;
                        //     const arrayBufferLength = buffer.byteLength;
                        //     const times = Math.ceil(arrayBufferLength / threshold);
                        //     let start = 0;
                        //     for (let i = 1; i <= times; i++) {
                        //         start = (i - 1) * threshold;
                        //         if (i === times) {
                        //         chunks.push(new Uint8Array(buffer.slice(start)));
                        //         } else {
                        //         chunks.push(new Uint8Array(buffer.slice(start, start + threshold)));
                        //         }
                        //     }
                        //     // console.log(chunks)
                        //     // tslint:disable-next-line: newline-per-chained-call
                        //     const sizeHint = chunks.map((ar) => ar.length).reduce((p, c) => p + c);
                        //     const compressed = streaming.compressChunks(chunks, sizeHint, compressionLevel);

                        //     const revisedBuffer = formatWriter.addFileHeader(compressed.buffer);

                        //     // const newBlob: any = new Blob([compressed.buffer], {type: 'application/apollo-zstd'});
                        //     const fileType: string = FileSaveType.APO + ';version=' + EDITOR_VERSION;
                        //     const newBlob = new Blob([revisedBuffer], {type: fileType});

                        //     saveAs(newBlob, file.name.slice(0, file.name.lastIndexOf('.')) + '.apo');

                        // });
                    };
                    break;
                }

                default: {
                    break;
                }
            }

        });
    }

    public getProtected(): boolean {
        return this.documentCore.isProtectedMode();
    }

    public getViewMode(): any {
        return this.documentCore.getViewMode();
    }

    public getEditMode(): any {
        return this.documentCore.getEditMode();
    }

    public setProtected(value: boolean): void {
      this.documentCore.protectDoc(value);
    }

    public setTrackRevsions(bTrack: boolean): void {
      if ( bTrack ) {
        this.documentCore.startTrackRevisions();
      } else {
        this.documentCore.closeTrackRevisions();
      }
    }

    public setFinalRevisions(bFinal: boolean): void {
        this.documentCore.changeRevisionState(bFinal);
    }

    public isTrackRevisions(): boolean {
      return this.documentCore.isTrackRevisions();
    }

    public isFinalRevision(): boolean {
        return this.documentCore.isFinalRevision();
    }

    public isStrictMode(): boolean {
        return this.documentCore.isStrictMode();
    }

    public setStrictMode(bStrictMode: boolean): number {
      return this.documentCore.setStrictMode(bStrictMode);
    }

    public setShowTableCellNames(bShow: boolean): void {
      this.documentCore.setShowTableCellNames(bShow);
    }

    public isShowTableCellNames(): boolean {
      return this.documentCore.isShowTableCellNames();
    }

    public isInTable(): boolean {
        return (null != this.documentCore.getCurrentTable());
    }

    public getNISTableMode(): number {
        return this.documentCore.getNISTableMode();
    }

    public openFile(bInsertFile: boolean = false): Promise<boolean> {
        return new Promise((resolve, reject) => {
            if (this.documentCore.getBDisableSaveDialog() === false &&
                this.documentCore.isDirty() === true && bInsertFile === false) {
                layer.confirm('当前文档未保存。需要保存吗？')

                .then((result) => {
                    this.generate();
                    resolve(false);
                })
                .catch((error) => {
                    resolve(true);
                });

            } else {
                resolve(true);
            }
        });
    }

    public setShowParaEnd(): void {
      this.documentCore.setShowParaEnd(!this.documentCore.isShowParaEnd());
    }

    public addPageBreak(): Promise<boolean> {
        return new Promise((resolve, reject) => {
            this.documentCore.addPageBreak();
            this.host.handleRefresh();
            resolve(true);
        });
    }

    public handleEditMenu(index: number): void {
        if (this.documentCore.isProtectedMode()) {
            return;
        }
        switch (index) {
            case ToolbarIndex.Undo:
                this.documentCore.undo();
                break;
            case ToolbarIndex.Redo:
                this.documentCore.redo();
                break;
            case ToolbarIndex.Cut:
                this.getCopyPaste()
                  .cutEvent();
                return;
            case ToolbarIndex.Copy:
                this.getCopyPaste()
                  .copyEvent();
                return;
            case ToolbarIndex.SelectAll: {
                this.documentCore.selectAll();
                break;
            }
            case ToolbarIndex.Paste:
                this.copyPaste.pasteText()
                .then((res) => {
                  if (res) {
                    this.host.handleRefresh();
                  }
                });
                return;
            default:
              return;
        }

        this.host.handleRefresh();
    }

    public isActiveUndo(): boolean {
        return this.documentCore.canUndo();
    }

    public isActiveRedo(): boolean {
      return this.documentCore.canRedo();
    }

    public isActiveCopy(): boolean {
      return this.host.isSelected();
    }

    public isActivePaste(cb: (flag: boolean) => void): void {
      const copyPaste = this.getCopyPaste();
      copyPaste.isActivePaste()
      .then((res) => {
        cb(res);
      });
      // (async () => {
      //     let text = await this.copyPaste.getClipboardData();
      //     const core = copyPaste.getCopyPaste();
      //     if (text === null) {
      //       core.setWebPageFlag();
      //       text = core.getText();
      //     } else {
      //       core.setWebPageFlag(false);
      //     }
      //     const access = this.copyPaste.copyPaste.getPermission();
      //     // if clipboard has content & permission allowed
      //     if (text != null && access) {
      //         cb(true);
      //     } else {
      //         cb(false);
      //     }
      // })();
    }

    public getCopyPaste(): PasteBtn {
        if (!this.copyPaste) {
            const copy = this.host.getCopyPaste();
            this.copyPaste = new PasteBtn(copy);
        }

        return this.copyPaste;
    }

    public generate(): void {
        // prepare for html generation if pages <= 3
        const formatWriter = new FormatWriter(this.host);
        if (this.xmlProps === undefined) {
            this.xmlProps = {
                documentCore: this.documentCore,
                properties: new Map(),
                selectedArea: [],
            };
        }

        formatWriter.generate(this.xmlProps, false, false, true)
        .then(() => {
            this.documentCore.setDirty(false);
        })
        .catch((error) => {
            this.documentCore.setDirty(false);
        });
    }

    public async generateOfficeBlob(type: string): Promise<void> {
        let resBlob: Blob | undefined;
        let ext = '';
        let errorTxt = '';
        switch (type) {
            case 'docx': {
                resBlob = await this.documentCore.saveToDocxBlob();
                ext = '.docx';
                if (!resBlob) {
                    errorTxt = 'docx文件流转换错误';
                }
                break;
            }
            case 'excel-table': {
                resBlob = await this.documentCore.saveToExcelByTables();
                ext = '.xlsx';
                if (!resBlob) {
                    errorTxt = '当前文档不存在表格';
                }
                break;
            }
        }
        if (resBlob) {
            const tmpUrl = URL.createObjectURL(resBlob);
            const tmpA = document.createElement('a');
            tmpA.href = tmpUrl;
            tmpA.download = '测试文档' + ext;
            tmpA.click();
            setTimeout(() => {
                tmpA.remove();
                URL.revokeObjectURL(tmpUrl);
            }, 100000);
            return;
        }
        message.info(errorTxt);
    }

    public startRecord(): void {
      // this.documentCore.createNewDocument();
      const recorder = getDocumentCoreRecorder();
      recorder.start();
    }

    public stopRecord(): void {
      const recorder = getDocumentCoreRecorder();
      recorder.stop();
    }

    public addCustomMethod(replayer: any): void {
        replayer.addCustomMethod('paste', (option) => {
            return pasteTest(option, this.documentCore);
        });
        replayer.addCustomMethod('documentRefresh', async () => {
            return new Promise((resolve, reject) => {
                this.host.handleRefresh();
                setTimeout(() => {
                    resolve(null);
                }, 0);
            });
        });
        replayer.addCustomMethod('readFile', (option) => {
            return readFile(option, this.documentCore);
        });
    }

    public exportRecord(): void {
      const recorder = getDocumentCoreRecorder();
      const actionsFile = recorder.exportAtions();
      const anchor: HTMLAnchorElement = document.createElement('a');
      anchor.href = URL.createObjectURL(actionsFile);
      anchor.download = 'actions.json';
      anchor.click();
    }

    public async startReplay(playFrameCallback: Function, finishedCallback: Function,
                             canceledCallback: Function): Promise<void> {
      const replayer = getDocumentCoreReplayer();
      this.addCustomMethod(replayer);
      const actionsFile = await this.openFileFromBrowser();

      replayer.setDocumentCore(this.documentCore);
      await replayer.importActions(actionsFile);
      replayer.onFramePlay(playFrameCallback);
      replayer.onFinished(finishedCallback);
      replayer.onCanceled(canceledCallback);

      // this.documentCore.createNewDocument();
      replayer.start();
    }

    public cancelReplay(): void {
      const replayer = getDocumentCoreReplayer();
      replayer.cancel();
    }

    public readFromBuffer( rawBuffer: ArrayBuffer, fileReader: FileReader, inputTarget: HTMLInputElement,
                           bInsertFile: boolean = false, type?: InsertFilePositionType): Promise<void> {
      const logicDocument = this.documentCore.getDocument();
      const reader = new Reader(logicDocument);

      if ( !bInsertFile ) {
          resetDocumentDefaultFont(logicDocument);
      }

      const {headerBuffer, contentBuffer}: IDocumentBuffer = reader.separateRawBuffer(new Uint8Array(rawBuffer));
      const {versionNum, documentNum} = reader.getHeaderBufferItems(headerBuffer);
      // tslint:disable-next-line: no-console
      // console.log(versionNum + ' ' + documentNum);

      return new Promise((resolve, reject) => {
        if (versionNum === FILE_HEADER_VERSION_GRACEFUL_DEGRADATION) {
          if (documentNum != null) {
              logicDocument.setFileVersion(documentNum);
              logicDocument.setDocumentVersion(versionNum);
          }
          const props = {bInsertFile, bNoEndPara: false, bRecalc: true, options: null,
                            type, documentVersion: versionNum};
          analyzeContentBuffer(null, contentBuffer, fileReader, reader, props, true)
          .then((result) => {
              if (result === ResultType.NeedDebug) {
              this.errorProcess(inputTarget, null, null, bInsertFile);
              }
              this.refresh(inputTarget, bInsertFile);
              resolve();
          });
        } else if (versionNum === FILE_HEADER_VERSION || versionNum === FILE_HEADER_ENCODE_VERSION
            || versionNum === FILE_HEADER_VERSION2) {
          if (documentNum != null) {
            logicDocument.setFileVersion(documentNum);
            logicDocument.setDocumentVersion(versionNum);
          }
          const props = {bInsertFile, bNoEndPara: false, bRecalc: true, options: null,
            type, documentVersion: versionNum};
          analyzeContentBuffer2(null, contentBuffer, fileReader, reader, props, true)
          .then((result) => {
              if (result === ResultType.NeedDebug) {
              this.errorProcess(inputTarget, null, null, bInsertFile);
              }
              this.refresh(inputTarget, bInsertFile);
              resolve();
          });

        } else {
            readerFileEditorVersionError(logicDocument);
            this.refresh(inputTarget, bInsertFile);
            resolve();
        //   getZstdCodec().then((zstdCodec) => {
        //     return zstdCodec.run( (zstd) => {
        //       if (documentNum != null && versionNum != null) {
        //         logicDocument.setFileVersion(documentNum);
        //         logicDocument.setDocumentVersion(versionNum);
        //       }
        //       const props = {bInsertFile, bNoEndPara: false, bRecalc: true, options: null,
        //                         type, documentVersion: versionNum};
        //       analyzeContentBuffer(zstd, contentBuffer, fileReader, reader, props, true)
        //       .then((result) => {
        //           // tslint:disable-next-line: no-console
        //             // console.log(result);
        //           if (result === ResultType.NeedDebug) {
        //           this.errorProcess(inputTarget, null, null, bInsertFile);
        //           }
        //           this.refresh(inputTarget, bInsertFile);
        //           resolve();
        //       });
        //     }); // zstdCodec.run
        //   })
        //   .catch((error) => {
        //       if (documentNum != null) {
        //         logicDocument.setFileVersion(documentNum);
        //       } else {
        //         logicDocument.setFileVersion(1);
        //       }
        //       logicDocument.setDocumentVersion(FILE_HEADER_VERSION2);
        //       const props = {bInsertFile, bNoEndPara: false, bRecalc: true, options: null,
        //         type, documentVersion: FILE_HEADER_VERSION2};
        //       analyzeContentBuffer2(null, contentBuffer, fileReader, reader, props, true)
        //         .then((result) => {
        //           if (result === ResultType.NeedDebug) {
        //             this.errorProcess(inputTarget, null, null, bInsertFile);
        //           }
        //           this.refresh(inputTarget, bInsertFile);
        //           resolve();
        //       });
        //   })
        //   .catch((error) => {
        //     // tslint:disable-next-line: no-console
        //     console.log(error, documentNum);
        //   });
        }
      });
    }

    private openFileFromBrowser(): Promise<Blob> {
      return new Promise((resolve, reject) => {
        const input: HTMLInputElement = document.createElement('input');
        input.type = 'file';
        input.style.display = 'none';
        input.multiple = false;
        input.onchange = (e) => {
          resolve(input.files[0]);
          document.body.removeChild(input);
        };
        document.body.appendChild(input);
        input.click();
      });
    }

    /**
     * 插入视频或音频
     * @param type 视频：1，音频：0
     * @param mediaSrc 多媒体资源链接（base64 / link）
     */
    private addVideoOrAudio = (type: ImageMediaType, mediaSrc: string) => {
        const img = new Image();
        if (type === ImageMediaType.Video) {
            img.src = videoIconStr;
        } else if (type === ImageMediaType.Audio) {
            img.src = audioIconStr;
        }
        img.width = 40;
        img.height = 40;
        const maxWidth = this.documentCore.getMaxWidth(true);
        const maxHeight = this.documentCore.getMaxHeight(true);

        // in signature box?
        let ratio = 1;
        const curControl = this.documentCore.getCurrentNewControl();
        if (curControl != null) {
            const parentControl = curControl.getParent();
            if (parentControl != null && parentControl.getType() === NewControlType.SignatureBox) {
                const tempRatio = parentControl.getSignatureRatio();
                if (isNaN(tempRatio) === false) {
                    ratio = parentControl.getSignatureRatio();
                }
            }
        }

        img.onload = () => {
            img.width *= ratio;
            img.height *= ratio;
            // if image width/height exceeds limits, shrink them to fit
            if (img.width > maxWidth) {
                const wRatio = maxWidth / img.width;
                const hRatio = maxHeight / img.height;
                if (img.height * wRatio > maxHeight) {
                    img.height = maxHeight;
                    img.width *= hRatio;
                } else {
                    img.width = maxWidth;
                    img.height *= wRatio;
                }
            } else if (img.height > maxHeight) {
                const wRatio = maxWidth / img.width;
                const hRatio = maxHeight / img.height;
                if (img.width * hRatio > maxWidth) {
                    img.width = maxWidth;
                    img.height *= wRatio;
                } else {
                    img.height = maxHeight;
                    img.width *= hRatio;
                }
            }
            this.addInlineImage(img.width, img.height, img.src, undefined,
                    undefined, undefined, type, mediaSrc);
        };
    }

    private addInlineImage = (width: number, height: number, src: string,
                              name?: string, type?: EquationType, svgElem?: any,
                              mediaType?: ImageMediaType, mediaSrc?: string) => {
        this.documentCore.addInlineImage(width, height, src, name, type, svgElem, mediaType, mediaSrc);
        this.host.handleRefresh();
    }

    private readTxtFile(fileReader: FileReader, file: any,
                        inputTarget: HTMLInputElement, bInsertFile: boolean = false): void {
        fileReader.readAsText(file);
        fileReader.onloadend = () => {

            const reader = new Reader(this.documentCore.getDocument());
            const result = reader.readFromTxt(fileReader.result as string, bInsertFile);
            if (!result && !bInsertFile) { // fail safe
                this.documentCore.createNewDocument();
            }

            // tslint:disable-next-line: no-console
            // console.timeEnd('read time');

            // rerender
            // cursor is at 1st para's start or current pos(insert file)
            this.host.testDocumentXml(undefined, bInsertFile);

            // trick input field to "onChange" every time
            inputTarget.value = '';
        };

        // loading animation?
        fileReader.onprogress = () => {
            // console.log('loading...');
        };
    }

    private readZstFile(fileReader: FileReader, zstfile: any, inputTarget: HTMLInputElement,
                        newZip: JSZip, bInsertFile: boolean = false, type?: InsertFilePositionType): Promise<boolean> {

        // don't forget to sync up with reader - readFromString() method
        // tslint:disable-next-line: no-console
        // console.time('read time');
        return new Promise((resolve, reject) => {
            fileReader.readAsArrayBuffer(zstfile);
            fileReader.onloadend = () => {
              const rawBuffer = fileReader.result as ArrayBuffer;
              this.readFromBuffer(rawBuffer, fileReader, inputTarget, bInsertFile, type).then(() => {
                  resolve(true);
              });
            }; // onloadend

            // loading animation?
            fileReader.onprogress = () => {
              // console.log('loading...');
            };
        });
    }

    private errorProcess(inputTarget: HTMLInputElement,
                         errorMessage: ErrorMessages, error: any, bInsertFile: boolean = false): void {


        if (!bInsertFile) {
            this.documentCore.createNewDocument();
        }

        // rerender
        // this.host.testDocumentXml(); // cursor is at 1st para's start

        // trick input field to "onChange" every time
        // inputTarget.value = '';

        // alert(errorMessage);
        if (errorMessage != null) {
          message.error(errorMessage);
        }

        // window.dispatchEvent(new ErrorEvent('error', {message: errorMessage, error}));
    }

    private refresh(inputTarget: HTMLInputElement, bInsertFile: boolean = false): void {
      // cursor is at 1st para's start
      this.host.testDocumentXml(undefined, bInsertFile);
      // this.props.refresh();

      // trick input field to "onChange" every time
      inputTarget.value = '';
      this.documentCore.setDirty(false);
    }

}
