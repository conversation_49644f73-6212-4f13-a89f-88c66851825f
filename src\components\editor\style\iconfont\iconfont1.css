@font-face {
  font-family: "iconfont"; /* Project id 2218574 */
  src: url('iconfont.woff2?t=1628565507478') format('woff2'),
       url('iconfont.woff?t=1628565507478') format('woff'),
       url('iconfont.ttf?t=1628565507478') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.iconfont.slashHeader:before {
  content: "\e86b";
}

.iconfont.mode:before {
  content: "\e86c";
}

.iconfont.row:before {
  content: "\e869";
}

.iconfont.cellType:before {
  content: "\e86a";
}

.iconfont.cellFormat:before {
  content: "\e868";
}

.iconfont.more:before {
  content: "\e860";
}

.iconfont.text:before {
  content: "\e854";
}

.iconfont.commentList:before {
  content: "\e843";
}

.iconfont.apoTurnZip:before {
  content: "\e844";
}

.iconfont.comments:before {
  content: "\e845";
}

.iconfont.font:before {
  content: "\e846";
}

.iconfont.file:before {
  content: "\e847";
}

.iconfont.designMode:before {
  content: "\e848";
}

.iconfont.editPicture:before {
  content: "\e849";
}

.iconfont.headerFooter:before {
  content: "\e84a";
}

.iconfont.automation:before {
  content: "\e84b";
}

.iconfont.formatTag:before {
  content: "\e84c";
}

.iconfont.Nav:before {
  content: "\e84d";
}

.iconfont.incrementalSave:before {
  content: "\e84e";
}

.iconfont.exportHtml:before {
  content: "\e84f";
}

.iconfont.incrementalLoad:before {
  content: "\e850";
}

.iconfont.open:before {
  content: "\e851";
}

.iconfont.editMode:before {
  content: "\e852";
}

.iconfont.pageSetup:before {
  content: "\e853";
}

.iconfont.paragraph:before {
  content: "\e855";
}

.iconfont.pageBreak:before {
  content: "\e856";
}

.iconfont.rangeName:before {
  content: "\e857";
}

.iconfont.selectAll:before {
  content: "\e858";
}

.iconfont.specialCharacters:before {
  content: "\e859";
}

.iconfont.pageNumber:before {
  content: "\e85a";
}

.iconfont.save:before {
  content: "\e85b";
}

.iconfont.insertTable:before {
  content: "\e85c";
}

.iconfont.revise:before {
  content: "\e85d";
}

.iconfont.printPreview:before {
  content: "\e85e";
}

.iconfont.redo:before {
  content: "\e85f";
}

.iconfont.new:before {
  content: "\e861";
}

.iconfont.watermark:before {
  content: "\e862";
}

.iconfont.structureElement:before {
  content: "\e863";
}

.iconfont.tableAttribute:before {
  content: "\e864";
}

.iconfont.viewRatio:before {
  content: "\e865";
}

.iconfont.viewMode:before {
  content: "\e866";
}

.iconfont.zipTurnApo:before {
  content: "\e867";
}

.iconfont.editorDelete:before {
  content: "\e804";
}

.iconfont.editorLineThickness:before {
  content: "\e803";
}

.iconfont.editorFill:before {
  content: "\e802";
}

.iconfont.editorQuickText:before {
  content: "\e801";
}

.iconfont.editorTextLine:before {
  content: "\e800";
}

.iconfont.editorText:before {
  content: "\e7ff";
}

.iconfont.editorOval:before {
  content: "\e7fe";
}

.iconfont.editorRectangle:before {
  content: "\e7fd";
}

.iconfont.editorFreeLine:before {
  content: "\e7fc";
}

.iconfont.editorLine:before {
  content: "\e7fb";
}

.iconfont.editorSelect:before {
  content: "\e7fa";
}

.iconfont.editorImage:before {
  content: "\e7f9";
}

.iconfont.editorPaste:before {
  content: "\e7f8";
}

.iconfont.editorCopy:before {
  content: "\e7f7";
}

.iconfont.editorCut:before {
  content: "\e7f6";
}

.iconfont.editorPrint:before {
  content: "\e7f5";
}

.iconfont.editorFormat:before {
  content: "\e7f4";
}

.iconfont.editorPage:before {
  content: "\e7f3";
}

.iconfont.editorAdvance:before {
  content: "\e7f2";
}

.iconfont.editorBack:before {
  content: "\e7f1";
}

.iconfont.editorSearch:before {
  content: "\e7ef";
}

.iconfont.editorExport:before {
  content: "\e7ed";
}

.iconfont.editorAnnotate:before {
  content: "\e7ec";
}

.iconfont.editorTable:before {
  content: "\e7eb";
}

.iconfont.editorFormula:before {
  content: "\e7ea";
}

.iconfont.editorItalic:before {
  content: "\e7e8";
}

.iconfont.editorSuperscript:before {
  content: "\e7e7";
}

.iconfont.editorSubscript:before {
  content: "\e7e6";
}

.iconfont.editorUnderline:before {
  content: "\e7e5";
}

.iconfont.editorColor:before {
  content: "\e7e4";
}

.iconfont.editorBold:before {
  content: "\e7e3";
}

.iconfont.editorSpace:before {
  content: "\e7e2";
}

.iconfont.editorList:before {
  content: "\e7e1";
}

.iconfont.editorReduceIndent:before {
  content: "\e7e0";
}

.iconfont.editorAddIndent:before {
  content: "\e7df";
}

.iconfont.editorAlignBetween:before {
  content: "\e7db";
}

.iconfont.editorAlignCenter:before {
  content: "\e7dc";
}

.iconfont.editorAlignLeft:before {
  content: "\e7dd";
}

.iconfont.editorAlignRight:before {
  content: "\e7de";
}

