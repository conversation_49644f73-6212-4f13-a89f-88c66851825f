import * as React from 'react';
import Dialog from '../dialog/dialog';
import { DocumentCore } from '../../../model/DocumentCore';
import { message } from '../../../common/Message';

interface IDialogProps {
    documentCore: DocumentCore;
    visible: boolean;
    close: (name: string, bRefresh?: boolean) => void;
    children: React.ReactNode;
}

interface IState {
    bRefresh: boolean;
}

export default class ImageSetDialog extends React.Component<IDialogProps, IState> {
    private image: {imageName: string, imageSource: string, imageWidth: number, imageHeight: number,
        imageSizeLocked: boolean, imageDeleteLocked: boolean,
        imageCopyLocked: boolean, imagePreserveAspectRatio: boolean};

    private showImageError: string = '';
    private imageName: string;
    private imageSizeRatio: number;
    private timeout: any;
    constructor(props: any) {
        super(props);
        this.state = {
            bRefresh: false,
        };
        this.image = {
            imageName: '',
            imageSource: '',
            imageWidth: 0,
            imageHeight: 0,
            imageSizeLocked: false,
            imageDeleteLocked: false,
            imageCopyLocked: false,
            imagePreserveAspectRatio: false,
        };
        this.imageSizeRatio = 1;
    }

    public render(): any {
        return (
            <Dialog
                visible={this.props.visible}
                top='middle'
                width={400}
                height={240}
                close={this.close}
                open={this.open}
                preventDefault={true}
                title='图片设置'
                confirm={this.confirm}
                id='image'
            >
                <div>
                    <div className='full-line'>
                        <div className='inline-block'>
                            <span>图片名称</span>
                            <input
                                value={this.image.imageName}
                                onChange={this.textChange.bind(this, 'imageName')}
                            />
                        </div>
                        <div className='inline-block'>
                            <span>图片路径</span>
                            <input
                                value={this.image.imageSource}
                                disabled={true}
                                onChange={this.textChange.bind(this, 'imageSource')}
                            />
                        </div>
                    </div>
                    <div className='full-line'>
                        <div className='inline-block'>
                            <span>宽度</span>
                            <input
                                disabled={this.image.imageSizeLocked === true}
                                value={this.image.imageWidth}
                                onChange={this.numChange.bind(this, 'imageWidth')}
                            />
                        </div>
                        <div className='inline-block'>
                            <span>高度</span>
                            <input
                                disabled={this.image.imageSizeLocked === true}
                                value={this.image.imageHeight}
                                onChange={this.numChange.bind(this, 'imageHeight')}
                            />
                        </div>
                    </div>
                    <div className='full-line'>
                        <p className='common-title'>高级</p>
                        <div className='full-line'>
                            <div className='inline-block'>
                                <input
                                    checked={this.image.imagePreserveAspectRatio === true}
                                    id='imagePreserveAspectRatio'
                                    onChange={this.checkChange.bind(this, 'imagePreserveAspectRatio')}
                                    type='checkbox'
                                />
                                <label htmlFor='imagePreserveAspectRatio'>保持比例</label>
                            </div>
                            <div className='inline-block'>
                                <input
                                    checked={this.image.imageSizeLocked === true}
                                    id='imageSizeLocked'
                                    type='checkbox'
                                    disabled={false}
                                    onChange={this.checkChange.bind(this, 'imageSizeLocked')}
                                />
                                <label>大小保护</label>
                            </div>

                        </div>
                        <div className='full-line'>
                            <div className='inline-block'>
                                <input
                                    checked={this.image.imageDeleteLocked === true}
                                    id='imageDeleteLocked'
                                    disabled={false}
                                    type='checkbox'
                                    onChange={this.checkChange.bind(this, 'imageDeleteLocked')}
                                />
                                <label>删除保护</label>
                            </div>
                            <div className='inline-block'>
                                <input
                                    checked={this.image.imageCopyLocked === true}
                                    id='imageCopyLocked'
                                    disabled={false}
                                    onChange={this.checkChange.bind(this, 'imageCopyLocked')}
                                    type='checkbox'
                                />
                                <label>拷贝保护</label>
                            </div>
                        </div>
                    </div>
                </div>
            </Dialog>
        );
    }

    private open = (): void => {
        const documentCore = this.props.documentCore;
        const file = documentCore.getSelectedDrawing();
        if (!file) {
            return;
        }
        const image = this.image;
        this.imageName = image.imageName = file.name;
        image.imageSource = file.src;
        image.imageWidth = file.width;
        image.imageHeight = file.height;
        image.imagePreserveAspectRatio = file.preserveAspectRatio;
        image.imageSizeLocked = file.sizeLocked;
        image.imageDeleteLocked = file.deleteLocked;
        image.imageCopyLocked = file.copyProtect;
        this.imageSizeRatio = file.imageRatio || 1;

        this.setState({bRefresh: !this.state.bRefresh});
    }

    // private sizeProtectChange(): void {

    // }

    private textChange = (name: string, e: any): void => {
        this.image[name] = e.target.value;
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private numChange = (name: string, e: any): void => {
        if (e.target.value === '' || e.target.value < 1) {
            e.target.value = 1;
        }
        const image = this.image;
        image[name] = parseInt(e.target.value, 10);
        this.setState({bRefresh: !this.state.bRefresh});
        this.setImageSize(name);
    }

    private setImageSize(name?: string): void {
        if (this.image.imagePreserveAspectRatio !== true) {
            return;
        }
        clearTimeout(this.timeout);
        this.timeout = setTimeout(() => {
            const image = this.image;
            let result = 1;
            switch (name) {
                case 'imageHeight':
                    result = Math.round(image.imageHeight * this.imageSizeRatio);
                    if (result < 1) {
                        result = 1;
                    }
                    image.imageWidth = result;
                    break;
                default:
                    // imageWidth && imagePreserveAspectRatio
                    // imagePreserveAspectRatio: in case from false -> true,
                    // need to recalculate height based on current width
                    result = Math.round(image.imageWidth / this.imageSizeRatio);
                    if (result < 1) {
                        result = 1;
                    }
                    image.imageHeight = result;
                    break;
            }
            this.setState({bRefresh: !this.state.bRefresh});
        }, 100);
    }

    private checkChange = (name: string, e: any): void => {
        this.image[name] = !this.image[name];
        if (name === 'imagePreserveAspectRatio') {
            this.setImageSize();
        }
    }

    private close = (id?: any): void => {
        this.props.close(id);
    }

    private confirm = (id?: any): void => {
        const documentCore = this.props.documentCore;
        const image = this.image;
        if (!documentCore.checkUniqueImageNameOtherThanSelectedImage(image.imageName)) {
            // alert('已存在改名字');
            message.error('已存在的图片名称');
            return null;
        } else {
            const curParaDrawing = documentCore.getSelectedDrawing();

            if (curParaDrawing.name !== this.image.imageName) {
                const graphicObjects = documentCore.getDrawingObjects();
                // old image name object
                const paraDrawingFromMap = graphicObjects.getDrawingByName(curParaDrawing.name);
                // remove pair of old image name
                if (graphicObjects.deleteImage(curParaDrawing) && paraDrawingFromMap) {
                    if (curParaDrawing === paraDrawingFromMap) { // same reference?
                        curParaDrawing.setName(this.image.imageName);
                        // add new image name pair
                        graphicObjects.addGraphicObject(paraDrawingFromMap);
                    } else {
                        return null;
                    }
                } else {
                    // cannot find pair
                    return null;
                }
            }
        }

        // remove SVG's fixed ratio if want to set manually
        // const curImage = documentCore.getImageSelectionInfo();
        // // console.log(curImage);
        // if (curImage) {
        //     if (curImage.getAttribute('preserveAspectRatio') !== 'none') {
        //         curImage.setAttribute('preserveAspectRatio', 'none');
        //     }
        // }

        documentCore.setDrawingProp(this.image.imageName, {width: image.imageWidth, height: image.imageHeight,
            preserveAspectRatio: image.imagePreserveAspectRatio,
            sizeLocked: image.imageSizeLocked, deleteLocked: image.imageDeleteLocked,
            copyProtect: image.imageCopyLocked});

        this.props.close(id, true);
    }
}
