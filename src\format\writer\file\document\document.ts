import { XmlComponent } from '../xml-components';
import { DocumentAttributes } from './document-attributes';
import { Body, SectionPropertiesOptions, SectionProperties } from './body';
import { Paragraph } from '../paragraph';
import { Region } from '../region';
import { Table } from '../table';
import { NISTable } from '../table/nis-table';
import { IXmlResult } from '../xml-components/base';

export class Document extends XmlComponent {

  public get Body(): Body {
    return this.body;
  }

  private readonly body: Body;

  constructor(sectionPropertiesOptions?: SectionPropertiesOptions) {
    super('w:document');
    this.root.push(
      new DocumentAttributes({
          w: 'http://schemas.openxmlformats.org/wordprocessingml/2006/main',
      }),
    );

    this.body = new Body(sectionPropertiesOptions);
    this.root.push(this.body); // this.body === this.root[bodyIndex]. must need? maybe not, just for convenience
  }

  public addParagraph(paragraph: Paragraph): Document {
    this.body.push(paragraph);
    return this;
  }

  public addRegion(region: Region): Document {
    this.body.push(region);
    return this;
  }

  public addTable(table: Table): Document {
    this.body.push(table);
    return this;
  }

  public addNISTable(table: NISTable): Document {
    this.body.push(table);
    return this;
  }

  public createParagraph(text?: string): Paragraph {
    const para = new Paragraph(text);
    this.addParagraph(para);
    return para;
  }

  public addSectionProperties(sectionPropertiesOptions?: SectionPropertiesOptions): Document {
    this.body.push(new SectionProperties(sectionPropertiesOptions));
    return this;
  }

  public prepForXml(): IXmlResult {
    const result = super.prepForXml();
    const key = this.rootKey;
    const text = `<${key}${result.attrs}>${result.text}</${key}>`;
    return {
      text,
      attrs: null
    };
  }
}
