
export class IncrementData {
    private data: any;
    // private state: any;

    constructor() {
        this.data = null;
        // this.state = null;
    }

    public setData(data: any): void {
        this.data = data;
    }

    public getData(): any {
        return this.data;
    }

    // public setState(state: any): void {
    //     this.state = state;
    // }

    // public getState(): any {
    //     return this.state;
    // }

    public saveData(item: any): boolean {
        const changeClass = item.changeClass;
        if ( !changeClass.write || !item.data.write) {
            return false;
        }

        // this.data = changeClass.write(item.data.isAdd());
        this.data = item.data.write();
        // const pos = data.pos;
        // const length = data.length;
        // this.data = (length + ':' + data);
        return true;
    }

    public getIncrementData(): any {
        return this.data;
        // return {
        //     data: this.data,
        //     state: this.state,
        // };
    }

    public applyData(): void {
        ;
    }
}

export class IncrementPoint {
    private datas: IncrementData[];
    private state: any;

    constructor(datas?: IncrementData[], state?: any) {
        this.datas = datas ? datas : [];
        this.state = state ? state : null;
    }

    public setDatas(datas: IncrementData[]): void {
        this.datas = datas;
    }

    public getDatas(): IncrementData[] {
        return this.datas;
    }

    public setState(state: any): void {
        this.state = state;
    }

    public getState(): any {
        return this.state;
    }

    // public saveState(state: any): void {
    //     this.state = {
    //         state: JSON.stringify(state),
    //     };
    // }

    public pushData(data: IncrementData): void {
        this.datas.push(data);
    }
}
