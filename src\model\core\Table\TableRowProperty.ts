import { INISCellSignInfo, INISRowProperty, NISRowType, ResultType } from '../../../common/commonDefines';
import { TableMeasurement, TableWidthType } from '../TableProperty';

export enum TableRowLineRule {
    Auto,
    Exact,
    AtLeast,
    Fixed,
}

/**
 * 表格行高度
 */
export class TableRowHeight {
    public value: number;
    public hRule: TableRowLineRule;

    constructor(value: number, hRule: TableRowLineRule) {
        this.value = value;
        this.hRule = hRule;
    }

    public isAuto(): boolean {
        return ( TableRowLineRule.Auto === this.hRule ) ? true : false;
    }

    public isFixed(): boolean {
        return ( TableRowLineRule.Exact === this.hRule ) ? true : false;
    }

    public getRule(): TableRowLineRule {
        return this.hRule;
    }
}

/**
 * 表格行属性
 */
export class TableRowProperty {
    public bCanSplit: boolean;  // 是否拆分
    public height: TableRowHeight;
    public cellSpacing: number;  // 表格单元格之间间距
    public widthAfter: TableMeasurement;
    public widthBefore: TableMeasurement;
    public gridBefore: number;
    public gridAfter: number;
    public bTableHeader: boolean;  // 是否表格标题行
    public bReadOnly: boolean;
    public readOnlyBackgroundColor: string;

    private nisProperty: INISRowProperty;

    constructor(prop?: TableRowProperty) {
        this.bCanSplit = false;
        this.height = new TableRowHeight(0, TableRowLineRule.Auto);
        this.cellSpacing = null;
        this.widthAfter = new TableMeasurement(TableWidthType.Auto, 0);
        this.widthBefore = new TableMeasurement(TableWidthType.Auto, 0);
        this.gridAfter = 0;
        this.gridBefore = 0;
        this.bTableHeader = false;
        this.bReadOnly = false;
    }

    public copy(): TableRowProperty {
        const prop = new TableRowProperty();

        prop.bCanSplit = this.bCanSplit;
        prop.height = new TableRowHeight(this.height.value, this.height.hRule);
        prop.cellSpacing = this.cellSpacing;
        prop.widthAfter = new TableMeasurement(this.widthAfter.type, this.widthAfter.width);
        prop.widthBefore = new TableMeasurement(this.widthBefore.type, this.widthBefore.width);
        prop.gridAfter = this.gridAfter;
        prop.gridBefore = this.gridBefore;
        prop.bTableHeader = this.bTableHeader;
        prop.bReadOnly = this.bReadOnly;
        prop.readOnlyBackgroundColor = this.readOnlyBackgroundColor;

        return prop;
    }

    public initNISRowProperty(): void {
        this.nisProperty = {
            type: NISRowType.Normal,
            rowID: '',
            signStatus: 0,
            bDeleteProtect: false,
            bReadOnly: this.bReadOnly,
            customProperty: [],
            creator: '',
            // signInfos: [],
            sumStatus: NISRowType.Normal,
        };
    }

    public getNISRowProperty(): INISRowProperty {
        this.nisProperty.bReadOnly = this.bReadOnly;

        return this.nisProperty;
    }

    public setNISRowProperty(prop: INISRowProperty): void {
        let nisProperty = this.nisProperty;
        if ( !nisProperty ) {
            this.initNISRowProperty();
            nisProperty = this.getNISRowProperty();
        }

        if ( null != prop.type && prop.type !== nisProperty.type ) {
            nisProperty.type = prop.type;
        }

        nisProperty.sumStatus = nisProperty.type;

        if ( null != prop.rowID && prop.rowID !== nisProperty.rowID ) {
            nisProperty.rowID = prop.rowID;
        }

        if ( null != prop.bReadOnly && prop.bReadOnly !== nisProperty.bReadOnly ) {
            nisProperty.bReadOnly = prop.bReadOnly;
            this.bReadOnly = nisProperty.bReadOnly;
        }

        if ( null != prop.signStatus && prop.signStatus !== nisProperty.signStatus ) {
            nisProperty.signStatus = prop.signStatus;
        }

        if ( null != prop.creator && prop.creator !== nisProperty.creator ) {
            nisProperty.creator = prop.creator;
        }

        if ( null != prop.bDeleteProtect && prop.bDeleteProtect !== nisProperty.bDeleteProtect ) {
            nisProperty.bDeleteProtect = prop.bDeleteProtect;
        }

        if (prop.customProperty != null &&
            JSON.stringify(nisProperty.customProperty) !== JSON.stringify(prop.customProperty)) {
                nisProperty.customProperty = prop.customProperty.map((item) => {
                return {...item};
            });
        }

        // if (prop.signInfos != null &&
        //     JSON.stringify(nisProperty.signInfos) !== JSON.stringify(prop.signInfos)) {
        //         nisProperty.signInfos = prop.signInfos.map((item) => {
        //         return {...item};
        //     });
        // }
    }

    public getSignStatus(): number {
        return (this.nisProperty ? this.nisProperty.signStatus : 0);
    }

    public setSignStatus(status: number): number {
        let nisProperty = this.nisProperty;
        if ( !nisProperty ) {
            this.initNISRowProperty();
            nisProperty = this.getNISRowProperty();
        }

        if ( nisProperty.signStatus === status ) {
            return ResultType.UnEdited;
        }

        nisProperty.signStatus = status;
        return ResultType.Success;
    }

    public isDeleteProtect(): boolean {
        return (this.nisProperty ? this.nisProperty.bDeleteProtect : null);
    }

    public setRowID(id: string): number {
        let nisProperty = this.nisProperty;
        if ( !nisProperty ) {
            this.initNISRowProperty();
            nisProperty = this.getNISRowProperty();
        }

        if ( nisProperty.rowID === id ) {
            return ResultType.UnEdited;
        }

        nisProperty.rowID = id;
        return ResultType.Success;
    }

    public getRowID(): string {
        return (this.nisProperty ? this.nisProperty.rowID : '');
    }

    public setNISCreator(creator: string): number {
        let nisProperty = this.nisProperty;
        if ( !nisProperty ) {
            this.initNISRowProperty();
            nisProperty = this.getNISRowProperty();
        }

        if ( nisProperty.creator === creator ) {
            return ResultType.UnEdited;
        }

        nisProperty.creator = creator;
        return ResultType.Success;
    }

    public getNISCreator(): string {
        return (this.nisProperty ? this.nisProperty.creator : '');
    }

    public getNISSignInfos(): INISCellSignInfo[] {
        return (this.nisProperty ? this.nisProperty.signInfos : null);
    }

    public isSumRow(): boolean {
        return (this.nisProperty ? NISRowType.Sum === this.nisProperty.type : false);
    }

    public isSumDetailRow(): boolean {
        return (this.nisProperty ? NISRowType.SumDetail === this.nisProperty.type : false);
    }

    public getSumStatus(): number {
        return (this.nisProperty ?
                    (null != this.nisProperty.sumStatus ? this.nisProperty.sumStatus : 0)
                    : ResultType.Failure2);
    }

    public getNISRowType(): number {
        return this.nisProperty.type;
    }

    public setNISRowType(type: NISRowType): number {
        let nisProperty = this.nisProperty;
        if ( !nisProperty ) {
            this.initNISRowProperty();
            nisProperty = this.getNISRowProperty();
        }

        if (type === nisProperty.type) {
            return ResultType.UnEdited;
        }

        if (null == type) {
            nisProperty.type = NISRowType.Normal;
        } else {
            nisProperty.type = type;
        }

        return ResultType.Success;
    }
}

// tslint:disable-next-line: max-classes-per-file
export class TableRowInfo {
    public xMin: number;
    public xMax: number;
    public y: number[];  // 当前行开始的y坐标
    public height: number[];  // 当前行的高度
    public topDy: number[];
    public maxTopBorder: number[];
    public maxBottomBorder: number;
    public tableRowsBottom: number;
    public bFirstPage: boolean;
    public startPage: number;  // 行开始页面
    public pages: number;  // 行所在页面

    constructor() {
        this.xMin = 0;
        this.xMax = 0;
        this.y = [];
        this.height = [];
        this.topDy = [];
        this.maxTopBorder = [];
        this.maxBottomBorder = 0;
        this.tableRowsBottom = 0;
        this.bFirstPage = false;
        this.startPage = 0;
        this.pages = 0;
    }
}
