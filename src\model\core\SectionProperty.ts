import { SectionPageSize, SectionPageMargins } from './Sections';
import { DocumentBorder } from './Style';
import HeaderFooter from './HeaderFooter';
import Document from './Document';
// import { PageNumberType } from '../../format/format-debug';
import { idCounter } from './util';
import { ChangeSectionFirstPageDiff, ChangeSectionFooterDefault, ChangeSectionHeaderDefault,
  ChangeSectionPageMargins, ChangeSectionPageMarginsFooter, ChangeSectionPageMarginsHeader,
  ChangeSectionHeaderFirst, ChangeSectionFooterFirst,
  ChangeSectionPageSize, ChangeSectionTitlePage } from './SectionPropertyChange';

// section的属性
// section: general conception.  主要包含 结，正文，页眉页脚。
// As of 8-3-2020, only 1 section exists. Diff sections can have diff headers/footers.

export class SectionProperty {
    public id: number;
    public type: SectionBreakType;
    public pageSize: SectionPageSize;
    public pageMargins: SectionPageMargins;
    public logicDocument: Document;
    public borders: SectionBorders;
    // public pageNumberType: PageNumberType;
    public headerFirst: HeaderFooter;
    public headerDefault: HeaderFooter;
    public footerFirst: HeaderFooter;
    public footerDefault: HeaderFooter;
    public titlePage: boolean;

    // custom
    public firstPageDiff: boolean;
    public isCurHeaderFooterEmpty: boolean; // header for just now
    // sectionBorders: SectionBorders = new SectionBorders();

    constructor(document: Document) {
        this.id = idCounter.getNewId();
        this.type = undefined;
        this.pageSize = new SectionPageSize();
        this.pageMargins = new SectionPageMargins();

        this.logicDocument = document;

        this.borders = new SectionBorders();
        // this.pageNumberType = new PageNumberType();

        this.headerFirst = null;
        this.headerDefault = null;
        this.footerFirst = null;
        this.footerDefault = null;
        this.titlePage = false;

        this.firstPageDiff = false;
        this.isCurHeaderFooterEmpty = false;
    }

    public setPageSize(width: number, height: number): void {
        let bChanged = false;
        if (null != height && Math.abs(height - this.pageSize.height) > 0.001 ) {
            height = Math.max(2.6, height);
            this.pageSize.height = height;
            bChanged = true;
        }

        if (null != width && Math.abs(width - this.pageSize.width) > 0.001) {
          width = Math.max(12.7, width);
          this.pageSize.width = width;
          bChanged = true;
        }

        if (bChanged === true) {
          const history = this.logicDocument.getHistory();
          if ( history ) {
            history.addChange(new ChangeSectionPageSize(this,
              {height: this.pageSize.height, width: this.pageSize.width}, {height, width}));
          }
        }
    }

    public getPageWidth(): number {
        return this.pageSize.width;
    }

    public getPageHeight(): number {
        return this.pageSize.height;
    }

    public setPageMargins(l: number, t: number, r: number, b: number, h?: number, f?: number): boolean {
        const left = ( undefined !== l ? l : this.pageMargins.paddingLeft );
        const top = ( undefined !== t ? t : this.pageMargins.paddingTop );
        const right = ( undefined !== r ? r : this.pageMargins.paddingRight );
        const bottom = ( undefined !== b ? b : this.pageMargins.paddingBottom );
        const header = ( undefined !== h ? h : this.pageMargins.header );
        const footer = ( undefined !== f ? f : this.pageMargins.footer );

        if (Math.abs(left - this.pageMargins.paddingLeft) > 0.001
            || Math.abs(top - this.pageMargins.paddingTop) > 0.001
            || Math.abs(right - this.pageMargins.paddingRight) > 0.001
            || Math.abs(bottom - this.pageMargins.paddingBottom) > 0.001) {

            const history = this.logicDocument.getHistory();
            if ( history ) {
              history.addChange(new ChangeSectionPageMargins(this,
                {left: this.pageMargins.paddingLeft, top: this.pageMargins.paddingTop,
                  right: this.pageMargins.paddingRight, bottom: this.pageMargins.paddingBottom},
                {left, top, right, bottom}));
            }

            this.pageMargins.paddingLeft   = left;
            this.pageMargins.paddingTop    = top;
            this.pageMargins.paddingRight  = right;
            this.pageMargins.paddingBottom = bottom;
            if (header != null) {
              this.pageMargins.header = header;
            }
            if (footer != null) {
              this.pageMargins.footer = footer;
            }

            return true;
        }

        return false;
    }

    public getPageProperty(): any {
      const pageSize = this.pageSize;
      const page = {
        width: pageSize.width,
        height: pageSize.height,
        margins: {}
      };
      const margins = this.pageMargins;
      const pageMargins: any = page.margins;

      pageMargins.left   = margins.paddingLeft;
      pageMargins.top    = margins.paddingTop;
      pageMargins.right  = margins.paddingRight;
      pageMargins.bottom = margins.paddingBottom;
      pageMargins.header = margins.header;
      pageMargins.footer = margins.footer;

      return page;
    }

    public getPageMarginLeft(): number {
        return this.pageMargins.paddingLeft;
    }

    public getPageMarginRight(): number {
        return this.pageMargins.paddingRight;
    }

    public getPageMarginTop(): number {
        return this.pageMargins.paddingTop;
    }

    public getPageMarginBottom(): number {
        return this.pageMargins.paddingBottom;
    }

    public getType(): SectionBreakType {
        return this.type;
    }

    public setType(type: SectionBreakType): void {
        this.type = type;
    }

    public comparePageSize(sectPr: SectionProperty): boolean {

        const thisPS = this.pageSize;
        const otherPS = sectPr.pageSize;

        if (Math.abs(thisPS.width - otherPS.width) > 0.001 || Math.abs(thisPS.height - otherPS.height) > 0.001
            || thisPS.orient !== otherPS.orient) {
            return false;
        }

        return true;
    }

    public setHeaderFirst(header: HeaderFooter): void {
      if (this.headerFirst !== header) {
        const history = this.logicDocument.getHistory();
        if ( history ) {
          history.addChange(new ChangeSectionHeaderFirst(this, this.headerFirst, header));
        }
        this.headerFirst = header;
      }
    }

    public getHeaderFirst(): HeaderFooter {
      return this.headerFirst;
    }

    public setHeaderDefault(header: HeaderFooter): void {
      if (this.headerDefault !== header) {
        const history = this.logicDocument.getHistory();
        if ( history ) {
          history.addChange(new ChangeSectionHeaderDefault(this, this.headerDefault, header));
        }
        this.headerDefault = header;
      }
    }

    public getHeaderDefault(): HeaderFooter {
      return this.headerDefault;
    }

    public setFooterFirst(footer: HeaderFooter): void {
      if (this.footerFirst !== footer) {
        const history = this.logicDocument.getHistory();
        if ( history ) {
          history.addChange(new ChangeSectionFooterFirst(this, this.footerFirst, footer));
        }
        this.footerFirst = footer;
      }
    }

    public getFooterFirst(): HeaderFooter {
      return this.footerFirst;
    }

    public setFooterDefault(footer: HeaderFooter): void {
      if (this.footerDefault !== footer) {
        const history = this.logicDocument.getHistory();
        if ( history ) {
          history.addChange(new ChangeSectionFooterDefault(this, this.footerDefault, footer));
        }
        this.footerDefault = footer;
      }
    }

    public getFooterDefault(): HeaderFooter {
      return this.footerDefault;
    }

    public setPageMarginsHeader(header: number): void {
      if (this.pageMargins.header !== header) {
        const history = this.logicDocument.getHistory();
        if ( history ) {
          history.addChange(new ChangeSectionPageMarginsHeader(this, this.pageMargins.header, header));
        }
        this.pageMargins.header = header;
      }
    }

    public getPageMarginsHeader(): number {
      return this.pageMargins.header;
    }

    public setPageMarginsFooter(footer: number): void {
      if (this.pageMargins.footer !== footer) {
        const history = this.logicDocument.getHistory();
        if ( history ) {
          history.addChange(new ChangeSectionPageMarginsFooter(this, this.pageMargins.footer, footer));
        }
        this.pageMargins.footer = footer;
      }
    }

    public getPageMarginsFooter(): number {
      return this.pageMargins.footer;
    }

    public getHeaderFooterInfo(headerFooter: HeaderFooter): {header: boolean, first: boolean} {
      if (this.headerFirst === headerFooter) {
        return {header: true, first: true};
      } else if (this.headerDefault === headerFooter) {
        return {header: true, first: false};
      } else if (this.footerFirst === headerFooter) {
        return {header: false, first: true};
      } else if (this.footerDefault === headerFooter) {
        return {header: false, first: false};
      }
      return null;
    }

    public getHeaderFooter(bHeader: boolean, bFirst: boolean): HeaderFooter {
      if (bHeader === true) {
        if (bFirst === true) {
          return this.headerFirst;
        } else {
          return this.headerDefault;
        }
      } else {
        if (bFirst === true) {
          return this.footerFirst;
        } else {
          return this.footerDefault;
        }
      }
    }

    public setHeaderFooter(bHeader: boolean, bFirst: boolean, headerFooter: HeaderFooter): void {
      if (bHeader === true) {
        if (bFirst === true) {
          return this.setHeaderFirst(headerFooter);
        } else {
          return this.setHeaderDefault(headerFooter);
        }
      } else {
        if (bFirst === true) {
          return this.setFooterFirst(headerFooter);
        } else {
          return this.setFooterDefault(headerFooter);
        }
      }
    }

    public clearAllHeaderFooter(): void {
      this.setHeaderFirst(null);
      this.setHeaderDefault(null);
      this.setFooterFirst(null);
      this.setFooterDefault(null);
    }

    public getAllHeaderFooters(headerFooters: HeaderFooter[]): HeaderFooter[] {
      if (!headerFooters) {
        headerFooters = [];
      }

      if (this.headerFirst !== null) {
        headerFooters.push(this.headerFirst);
      }
      if (this.headerDefault !== null) {
        headerFooters.push(this.headerDefault);
      }
      if (this.footerFirst !== null) {
        headerFooters.push(this.footerFirst);
      }
      if (this.footerDefault !== null) {
        headerFooters.push(this.footerDefault);
      }

      return headerFooters;
    }

    public setTitlePage(value: boolean): void {
      if (this.titlePage !== value) {
        const history = this.logicDocument.getHistory();
        if ( history ) {
          history.addChange(new ChangeSectionTitlePage(this, this.titlePage, value));
        }
        this.titlePage = value;
      }
    }

    public setFirstPageDiff(value: boolean): void {
      if (this.firstPageDiff !== value) {
        const history = this.logicDocument.getHistory();
        if ( history ) {
          history.addChange(new ChangeSectionFirstPageDiff(this, this.firstPageDiff, value));
        }
        this.firstPageDiff = value;
      }
    }

    public getFirstPageDiff(): boolean {
      return this.firstPageDiff;
    }

    public getTitlePage(): boolean {
      return this.titlePage;
    }

    public isAllHeaderFooterNull(): boolean {
      if (this.footerFirst !== null || this.headerFirst !== null || this.footerDefault !== null ||
        this.headerDefault !== null) {
        return false;
      }
      return true;
    }

    public getIsCurHeaderFooterEmpty(): boolean {
      return this.isCurHeaderFooterEmpty;
    }

    public setIsCurHeaderFooterEmpty(val: boolean): void {
      this.isCurHeaderFooterEmpty = val;
    }

    public getHeaderNum(): number {
      let count = 0;
      if (this.headerDefault) {
        count++;
      }
      if (this.headerFirst) {
        count++;
      }

      return count;
    }

    public getFooterNum(): number {
      let count = 0;
      if (this.footerDefault) {
        count++;
      }
      if (this.footerFirst) {
        count++;
      }

      return count;
    }

}

export class SectionBorders {
    public top: DocumentBorder;
    public left: DocumentBorder;
    public right: DocumentBorder;
    public bottom: DocumentBorder;

    constructor() {
        this.top = new DocumentBorder();
        this.left = new DocumentBorder();
        this.right = new DocumentBorder();
        this.bottom = new DocumentBorder();
    }
}

export enum SectionBreakType {
    NextPage   = 0x00,
    OddPage    = 0x01, // 奇数页
    EvenPage   = 0x02, // 偶数页
    Continuous = 0x03,
}
