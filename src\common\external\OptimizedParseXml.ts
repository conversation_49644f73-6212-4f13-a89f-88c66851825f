import { IParseXmlNode as INode } from '../commonDefines';
import { ParseXml } from './ParseXml';
import { ParseContent } from './ParseXml';

/**
 * 优化版XML解析类，使用索引结构加速节点查找
 */
export class OptimizedParseXml extends ParseXml {
    private nodeIndex: Map<string, INode[]>; // 按标签名索引节点
    private attrIndex: Map<string, Map<string, INode[]>>; // 按属性索引节点
    private propIndex: Map<string, Map<string, INode[]>>; // 按自定义属性索引节点

    constructor(xmlDoc: string, bShowTitle?: boolean) {
        super(xmlDoc, bShowTitle);
        this.buildIndices(); // 构建索引
    }

    /**
     * 构建节点索引，用于快速查找
     */
    private buildIndices(): void {
        this.nodeIndex = new Map();
        this.attrIndex = new Map();
        this.propIndex = new Map();
        
        // 递归构建索引
        if (this['node']) {
            this.indexNode(this['node']);
        }
        
        // 为自定义节点建立索引
        if (this['customNodes']) {
            for (const customNode of this['customNodes']) {
                if (customNode && customNode.node) {
                    let curNode = customNode.node.parentNode;
                    if (curNode && curNode.tagName === 'rgPr') {
                        curNode = curNode.parentNode;
                    }
                    
                    if (curNode) {
                        // 获取自定义属性名和值
                        const propName = customNode.node.parentNode ? 
                            String(customNode.node.parentNode.tagName) : null;
                        const propValue = String(customNode.node.innerHTML || '');
                        
                        if (propName && propValue) {
                            if (!this.propIndex.has(propName)) {
                                this.propIndex.set(propName, new Map());
                            }
                            
                            const valueMap = this.propIndex.get(propName);
                            if (!valueMap.has(propValue)) {
                                valueMap.set(propValue, []);
                            }
                            
                            valueMap.get(propValue).push(curNode);
                        }
                    }
                }
            }
        }
        
        // 为区域节点建立索引
        if (this['customRegionNodes']) {
            for (const regionNode of this['customRegionNodes']) {
                if (regionNode && regionNode.node) {
                    const node = regionNode.node;
                    const name = node.attributes && node.attributes.name;
                    
                    if (name) {
                        if (!this.attrIndex.has('name')) {
                            this.attrIndex.set('name', new Map());
                        }
                        
                        const nameMap = this.attrIndex.get('name');
                        if (!nameMap.has(name)) {
                            nameMap.set(name, []);
                        }
                        
                        nameMap.get(name).push(node);
                    }
                }
            }
        }
    }

    /**
     * 递归索引节点及其子节点
     */
    private indexNode(node: INode): void {
        if (!node || typeof node === 'string') return;
        
        // 按标签名索引
        if (!this.nodeIndex.has(node.tagName)) {
            this.nodeIndex.set(node.tagName, []);
        }
        this.nodeIndex.get(node.tagName).push(node);
        
        // 按属性索引
        if (node.attributes) {
            for (const [key, value] of Object.entries(node.attributes)) {
                if (!this.attrIndex.has(key)) {
                    this.attrIndex.set(key, new Map());
                }
                
                const attrMap = this.attrIndex.get(key);
                const strValue = String(value); // 确保值是字符串类型
                if (!attrMap.has(strValue)) {
                    attrMap.set(strValue, []);
                }
                attrMap.get(strValue).push(node);
            }
        }
        
        // 递归处理子节点
        if (node.children) {
            for (const child of node.children) {
                if (typeof child !== 'string') {
                    this.indexNode(child);
                }
            }
        }
    }

    /**
     * 优化版的节点查找方法，使用索引加速查找
     * 保持与原始filterNodesByCustomProps相同的API
     */
    public filterNodesByCustomPropsOptimized(tagNames: string[] | string, json: any[]): INode[][] {
        const names = Array.isArray(tagNames) ? tagNames : [tagNames];
        const result: INode[][] = new Array(json.length).fill(null);
        
        // 处理name匹配和customProp匹配
        for (let i = 0; i < json.length; i++) {
            const obj = json[i];
            
            if (obj['name']) {
                // 按名称查找
                if (this.attrIndex.has('name') && this.attrIndex.get('name').has(obj['name'])) {
                    const nodes = this.attrIndex.get('name').get(obj['name']);
                    for (const node of nodes) {
                        if (names.includes(node.tagName)) {
                            // 确保节点内容已加载
                            this.ensureNodeContent(node);
                            // 调用父类的setAllSectionContent方法
                            const setAllSectionContentMethod = this['setAllSectionContent'] || super['setAllSectionContent'];
                            if (typeof setAllSectionContentMethod === 'function') {
                                result[i] = setAllSectionContentMethod.call(this, node);
                            }
                            break; // 只取第一个匹配的
                        }
                    }
                }
            } else if (obj['propName'] && obj['propValue']) {
                // 按自定义属性查找
                if (this.propIndex.has(obj['propName']) && 
                    this.propIndex.get(obj['propName']).has(obj['propValue'])) {
                    
                    const nodes = this.propIndex.get(obj['propName']).get(obj['propValue']);
                    for (const node of nodes) {
                        if (names.includes(node.tagName)) {
                            // 确保节点内容已加载
                            this.ensureNodeContent(node);
                            
                            // 使用原始类的方法处理节点
                            this['actParentNode'] = node;
                            
                            // 调用父类的getNodesByCustomProp方法
                            const getNodesByCustomPropMethod = this['getNodesByCustomProp'] || super['getNodesByCustomProp'];
                            if (typeof getNodesByCustomPropMethod === 'function') {
                                getNodesByCustomPropMethod.call(this, node.innerHTML, node);
                                
                                if (this['result'] && this['result'].length > 0) {
                                    // 调用父类的setAllSectionContent方法
                                    const setAllSectionContentMethod = this['setAllSectionContent'] || super['setAllSectionContent'];
                                    if (typeof setAllSectionContentMethod === 'function') {
                                        result[i] = setAllSectionContentMethod.call(this, this['result'][0].res);
                                    }
                                break; // 只取第一个匹配的
                                }
                            }
                        }
                    }
                }
            }
        }
        
        return result;
    }
    
    /**
     * 确保节点的内容已经加载
     */
    private ensureNodeContent(node: INode): void {
        if (!node.innerHTML) {
            // 调用父类的getInnerHTML方法
            const getInnerHTMLMethod = this['getInnerHTML'] || super['getInnerHTML'];
            if (typeof getInnerHTMLMethod === 'function') {
                node.innerHTML = getInnerHTMLMethod.call(this, node);
            } else {
                // 如果无法访问父类方法，则使用空字符串
                node.innerHTML = '';
            }
        }
    }
}

/**
 * 优化版内容解析类，使用缓存加速解析
 */
export class OptimizedParseContent extends ParseContent {
    private nodeCache: Map<string, any> = new Map();
    
    /**
     * 优化版解析方法，使用缓存加速
     */
    public parseOptimized(nodes: INode[][]): any[][] {
        // 检查缓存
        const cacheKey = this.generateCacheKey(nodes);
        if (this.nodeCache.has(cacheKey)) {
            return this.nodeCache.get(cacheKey);
        }
        
        // 调用原始解析方法
        const result = super.parse(nodes);
        
        // 存入缓存
        this.nodeCache.set(cacheKey, result);
        
        return result;
    }
    
    /**
     * 为节点数组生成缓存键
     */
    private generateCacheKey(nodes: INode[][]): string {
        return nodes.map(nodeArray => 
            nodeArray ? nodeArray.map(node => 
                node ? `${node.tagName}:${node.startPos || 0}` : 'null'
            ).join('|') : 'null'
        ).join('||');
    }
}
