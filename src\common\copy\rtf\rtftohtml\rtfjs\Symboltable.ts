// The MIT License (MIT)
//
// Copyright (c) 2016 <PERSON>
//
// Permission is hereby granted, free of charge, to any person obtaining a copy
// of this software and associated documentation files (the "Software"), to deal
// in the Software without restriction, including without limitation the rights
// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
// copies of the Software, and to permit persons to whom the Software is
// furnished to do so, subject to the following conditions:
//
// The above copyright notice and this permission notice shall be included in all
// copies or substantial portions of the Software.
//
// THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
// SOFTWARE.

// Based on the mapping documented at http://unicode.org/Public/MAPPINGS/VENDORS/ADOBE/symbol.txt

// tslint:disable-next-line: sy-global-const-name
export const SymbolTable: {[key: string]: string} = {
    '20': '\u0020',
    '21': '\u0021',
    '22': '\u2200',
    '23': '\u0023',
    '24': '\u2203',
    '25': '\u0025',
    '26': '\u0026',
    '27': '\u220b',
    '28': '\u0028',
    '29': '\u0029',
    '2a': '\u2217',
    '2b': '\u002b',
    '2c': '\u002c',
    '2d': '\u2212',
    '2e': '\u002e',
    '2f': '\u002f',
    '30': '\u0030',
    '31': '\u0031',
    '32': '\u0032',
    '33': '\u0033',
    '34': '\u0034',
    '35': '\u0035',
    '36': '\u0036',
    '37': '\u0037',
    '38': '\u0038',
    '39': '\u0039',
    '3a': '\u003a',
    '3b': '\u003b',
    '3c': '\u003c',
    '3d': '\u003d',
    '3e': '\u003e',
    '3f': '\u003f',
    '40': '\u2245',
    '41': '\u0391',
    '42': '\u0392',
    '43': '\u03a7',
    '44': '\u0394',
    '45': '\u0395',
    '46': '\u03a6',
    '47': '\u0393',
    '48': '\u0397',
    '49': '\u0399',
    '4a': '\u03d1',
    '4b': '\u039a',
    '4c': '\u039b',
    '4d': '\u039c',
    '4e': '\u039d',
    '4f': '\u039f',
    '50': '\u03a0',
    '51': '\u0398',
    '52': '\u03a1',
    '53': '\u03a3',
    '54': '\u03a4',
    '55': '\u03a5',
    '56': '\u03c2',
    '57': '\u03a9',
    '58': '\u039e',
    '59': '\u03a8',
    '5a': '\u0396',
    '5b': '\u005b',
    '5c': '\u2234',
    '5d': '\u005d',
    '5e': '\u22a5',
    '5f': '\u005f',
    '60': '\uf8e5',
    '61': '\u03b1',
    '62': '\u03b2',
    '63': '\u03c7',
    '64': '\u03b4',
    '65': '\u03b5',
    '66': '\u03c6',
    '67': '\u03b3',
    '68': '\u03b7',
    '69': '\u03b9',
    '6a': '\u03d5',
    '6b': '\u03ba',
    '6c': '\u03bb',
    '6d': '\u00b5',
    '6e': '\u03bd',
    '6f': '\u03bf',
    '70': '\u03c0',
    '71': '\u03b8',
    '72': '\u03c1',
    '73': '\u03c3',
    '74': '\u03c4',
    '75': '\u03c5',
    '76': '\u03d6',
    '77': '\u03c9',
    '78': '\u03be',
    '79': '\u03c8',
    '7a': '\u03b6',
    '7b': '\u007b',
    '7c': '\u007c',
    '7d': '\u007d',
    '7e': '\u223c',
    'a0': '\u20ac',
    'a1': '\u03d2',
    'a2': '\u2032',
    'a3': '\u2264',
    'a4': '\u2044',
    'a5': '\u221e',
    'a6': '\u0192',
    'a7': '\u2663',
    'a8': '\u2666',
    'a9': '\u2665',
    'aa': '\u2660',
    'ab': '\u2194',
    'ac': '\u2190',
    'ad': '\u2191',
    'ae': '\u2192',
    'af': '\u2193',
    'b0': '\u00b0',
    'b1': '\u00b1',
    'b2': '\u2033',
    'b3': '\u2265',
    'b4': '\u00d7',
    'b5': '\u221d',
    'b6': '\u2202',
    'b7': '\u2022',
    'b8': '\u00f7',
    'b9': '\u2260',
    'ba': '\u2261',
    'bb': '\u2248',
    'bc': '\u2026',
    'bd': '\uf8e6',
    'be': '\uf8e7',
    'bf': '\u21b5',
    'c0': '\u2135',
    'c1': '\u2111',
    'c2': '\u211c',
    'c3': '\u2118',
    'c4': '\u2297',
    'c5': '\u2295',
    'c6': '\u2205',
    'c7': '\u2229',
    'c8': '\u222a',
    'c9': '\u2283',
    'ca': '\u2287',
    'cb': '\u2284',
    'cc': '\u2282',
    'cd': '\u2286',
    'ce': '\u2208',
    'cf': '\u2209',
    'd0': '\u2220',
    'd1': '\u2207',
    'd2': '\uf6da',
    'd3': '\uf6d9',
    'd4': '\uf6db',
    'd5': '\u220f',
    'd6': '\u221a',
    'd7': '\u22c5',
    'd8': '\u00ac',
    'd9': '\u2227',
    'da': '\u2228',
    'db': '\u21d4',
    'dc': '\u21d0',
    'dd': '\u21d1',
    'de': '\u21d2',
    'df': '\u21d3',
    'e0': '\u25ca',
    'e1': '\u2329',
    'e2': '\uf8e8',
    'e3': '\uf8e9',
    'e4': '\uf8ea',
    'e5': '\u2211',
    'e6': '\uf8eb',
    'e7': '\uf8ec',
    'e8': '\uf8ed',
    'e9': '\uf8ee',
    'ea': '\uf8ef',
    'eb': '\uf8f0',
    'ec': '\uf8f1',
    'ed': '\uf8f2',
    'ee': '\uf8f3',
    'ef': '\uf8f4',
    'f1': '\u232a',
    'f2': '\u222b',
    'f3': '\u2320',
    'f4': '\uf8f5',
    'f5': '\u2321',
    'f6': '\uf8f6',
    'f7': '\uf8f7',
    'f8': '\uf8f8',
    'f9': '\uf8f9',
    'fa': '\uf8fa',
    'fb': '\uf8fb',
    'fc': '\uf8fc',
    'fd': '\uf8fd',
    'fe': '\uf8fe',
};
