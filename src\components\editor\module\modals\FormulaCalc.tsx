import * as React from 'react';
import Dialog from '../../ui/Dialog';
import Button from '../../ui/Button';
import Input from '../../ui/Input';
import Select from '../../ui/select/Select';
import { CellFormulaCalc, ITableCellFormulaPar, MixFormulaParser } from '../../../../common/commonDefines';
import { message } from '../../../../common/Message';

interface IDialogProps {
    documentCore: any;
    visible: boolean;
    id: string;
    close: (name: string | number, bRefresh?: boolean) => void;
}

interface IState {
    bRefresh: boolean;
}

export default class FormulaCalc extends React.Component<IDialogProps, IState> {
    private visible: boolean;
    // private datas: any[];
    // private formula: any;
    private types: { key: string; value: number; }[];
    private formula: ITableCellFormulaPar;
    private prevFormulaType: CellFormulaCalc;
    private cellNames: string[];
    constructor(props: any) {
        super(props);
        this.state = {
            bRefresh: false,
        };
        this.visible = this.props.visible;

        this.types = [
            {key: '求和', value: CellFormulaCalc.SUM},
            {key: '相加', value: CellFormulaCalc.ADD},
            {key: '相乘', value: CellFormulaCalc.MUL},
            {key: '四则混合运算', value: CellFormulaCalc.MIX},
            {key: '清空', value: CellFormulaCalc.CLEAR},
        ];

        this.formula = {
            formulaType: undefined,
            startCell: undefined,
            endCell: undefined,
            addFormula: undefined,
            multi1: undefined,
            multi2: undefined,
            mixFormula: undefined,
            bClear: false,
        };

        this.cellNames = [];
        this.prevFormulaType = CellFormulaCalc.DEFAULT;

        this.open();
    }

    public render(): any {
        return (
            <Dialog
                id={this.props.id}
                preventDefault={true}
                visible={this.visible}
                // open={this.open}
                width={400}
                title={'公式'}
                footer={this.renderFooter()}
            >
                <div>
                    <div className='editor-line'>
                        <span className='w-90'>公式类型</span>
                        <div className='right-auto-90'>
                            <Select
                                data={this.types}
                                value={this.formula.formulaType}
                                name='formulaType'
                                disabled={false}
                                onChange={this.onChange}
                            />
                        </div>
                    </div>
                    {this.renderExternal()}
                </div>
            </Dialog>
        );
    }

    public componentDidMount(): void {
        this.setState({bRefresh: !this.state.bRefresh});
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        this.visible = nextProps.visible;
        if ( this.visible ) {
            this.open();
        }
    }

    private renderExternal(): any {
        this.formula.bClear = false;
        switch ( this.formula.formulaType ) {
            case CellFormulaCalc.SUM:
                this.formula.addFormula = undefined;
                this.formula.multi1 = undefined;
                this.formula.multi2 = undefined;
                this.formula.mixFormula = undefined;
                return this.renderFormulaCalcSum();
            case CellFormulaCalc.ADD:
                this.formula.startCell = undefined;
                this.formula.endCell = undefined;
                this.formula.multi1 = undefined;
                this.formula.multi2 = undefined;
                this.formula.mixFormula = undefined;
                return this.renderFormulaCalcAdd();
            case CellFormulaCalc.MUL:
                this.formula.startCell = undefined;
                this.formula.endCell = undefined;
                this.formula.addFormula = undefined;
                this.formula.mixFormula = undefined;
                return this.renderFormulaCalcMul();
            case CellFormulaCalc.MIX:
                this.formula.startCell = undefined;
                this.formula.endCell = undefined;
                this.formula.addFormula = undefined;
                this.formula.multi1 = undefined;
                this.formula.multi2 = undefined;
                return this.renderFormulaCalcMix();
            case CellFormulaCalc.CLEAR:
                return this.clearFormula();
            default:
                this.formula.startCell = undefined;
                this.formula.endCell = undefined;
                this.formula.addFormula = undefined;
                this.formula.multi1 = undefined;
                this.formula.multi2 = undefined;
                this.formula.mixFormula = undefined;
                return null;
        }
    }

    private renderFormulaCalcSum(): any {
        return (
            <React.Fragment>
                <div className='editor-line'>
                    <span className='w-90'>开始单元格: </span>
                    <div className='right-auto-90'>
                        <Input
                            value={this.formula.startCell}
                            name='startCell'
                            onChange={this.onChange}
                        />
                    </div>
                </div>
                <div className='editor-line'>
                    <span className='w-90'>结束单元格: </span>
                    <div className='right-auto-90'>
                        <Input
                            value={this.formula.endCell}
                            name='endCell'
                            onChange={this.onChange}
                        />
                    </div>
                </div>
            </React.Fragment>
        );
    }

    private renderFormulaCalcAdd(): any {
        return (
            <React.Fragment>
                <div className='editor-line'>
                    <span className='w-90'>表达式: </span>
                    <div className='right-auto-90'>
                        <Input
                            value={this.formula.addFormula}
                            name='addFormula'
                            onChange={this.onChange}
                        />
                    </div>
                </div>
                <div className='editor-line' style={{textAlign: 'center'}}>
                    <span>示例：A1+B3+C4+E5</span>
                </div>
            </React.Fragment>
        );
    }

    private renderFormulaCalcMul(): any {
        return (
            <React.Fragment>
                <div className='editor-line'>
                    <span className='w-90'>乘数: </span>
                    <div className='right-auto-90'>
                        <Input
                            value={this.formula.multi1}
                            name='multi1'
                            onChange={this.onChange}
                        />
                    </div>
                </div>
                <div className='editor-line'>
                    <span className='w-90'>被乘数: </span>
                    <div className='right-auto-90'>
                        <Input
                            value={this.formula.multi2}
                            name='multi2'
                            onChange={this.onChange}
                        />
                    </div>
                </div>
                <div className='editor-line' style={{textAlign: 'center'}}>
                    <span>示例：A1*B2 (只能计算2个)</span>
                </div>
            </React.Fragment>
        );
    }

    private renderFormulaCalcMix(): any {
        return (
            <React.Fragment>
                <div className='editor-line'>
                    <span className='w-90'>表达式: </span>
                    <div className='right-auto-90'>
                        <Input
                            value={this.formula.mixFormula}
                            name='mixFormula'
                            onChange={this.onChange}
                        />
                    </div>
                </div>
                <div className='editor-line' style={{textAlign: 'center'}}>
                    <span>示例：(A1-1)+2*(B1/3+4)</span>
                </div>
            </React.Fragment>
        );
    }

    private clearFormula(): any {
        return (
            <React.Fragment>
                <div className='editor-line' style={{textAlign: 'center'}}>
                    <span>移除设置的公式</span>
                </div>
            </React.Fragment>
        );
    }

    private renderFooter(): any {
        return (
            <span>
                <Button onClick={this.close}>取消</Button>
                <Button type='primary' onClick={this.confirm}>确认</Button>
            </span>
        );
    }

    private open = (): void => {
        const formula = this.props.documentCore.getCellFormula();
        this.formula.formulaType = formula ? formula.formulaType : undefined;
        this.formula.startCell = formula ? formula.startCell : undefined;
        this.formula.endCell = formula ? formula.endCell : undefined;
        this.formula.addFormula = formula ? formula.addFormula : undefined;
        this.formula.multi1 = formula ? formula.multi1 : undefined;
        this.formula.multi2 = formula ? formula.multi2 : undefined;
        this.formula.mixFormula = formula ? formula.mixFormula : undefined;
    }

    private onChange = (value: any, name: string): void => {
        this.formula[name] = value;
        if ( 'formulaType' === name ) {
            this.setState({bRefresh: !this.state.bRefresh});
        }
    }

    private close = (bRefresh?: boolean): void => {
        this.formula = {
            formulaType: undefined,
            startCell: undefined,
            endCell: undefined,
            addFormula: undefined,
            multi1: undefined,
            multi2: undefined,
            mixFormula: undefined,
            bClear: false,
        };
        this.props.close(this.props.id, bRefresh);
        this.visible = false;
        this.prevFormulaType = CellFormulaCalc.DEFAULT;
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private confirm = (): void => {
        const documentCore = this.props.documentCore;
        switch (this.formula.formulaType) {
            case CellFormulaCalc.SUM:
                if ( null == this.formula.startCell || !this.checkNameValidate(this.formula.startCell)
                    || null == documentCore.getCellByName(this.formula.startCell) ) {
                    message.error('开始单元格非法，请重新输入');
                    return ;
                }
                if ( null == this.formula.endCell || !this.checkNameValidate(this.formula.endCell)
                    || null == documentCore.getCellByName(this.formula.endCell) ) {
                    message.error('结束单元格非法，请重新输入');
                    return ;
                }
                if ( null != this.formula.startCell && null != this.formula.endCell
                    && !documentCore.isCellsInRowOrCol([this.formula.startCell, this.formula.endCell])) {
                    message.error('单元格不连续，请重新输入');
                    return ;
                }
                break;

            case CellFormulaCalc.MUL:
                if ( null == this.formula.multi1 || !this.checkNameValidate(this.formula.multi1)
                    || null == documentCore.getCellByName(this.formula.multi1) ) {
                    message.error('乘数非法，请重新输入');
                    return ;
                }
                if ( null == this.formula.multi2 || !this.checkNameValidate(this.formula.multi2)
                    || null == documentCore.getCellByName(this.formula.multi2) ) {
                    message.error('被乘数非法，请重新输入');
                    return ;
                }
                break;

            case CellFormulaCalc.ADD:
                if ( null == this.formula.addFormula ||
                    !this.checkAddFormulaValidate(this.formula.addFormula) ) {
                    message.error('表达式非法，请重新输入');
                    return ;
                }
                for (let index = 0, length = this.cellNames.length; index < length; index++) {
                    if ( null == documentCore.getCellByName(this.cellNames[index]) ) {
                        message.error('单元格' + this.cellNames[index] + '非法，请重新输入');
                        return;
                    }
                }
                break;
            case CellFormulaCalc.MIX:
                if ( null == this.formula.mixFormula ||
                    !this.checkMixFormulaValidate(this.formula.mixFormula) ) {
                    message.error('表达式非法，请重新输入');
                    return ;
                }
                for (let index = 0, length = this.cellNames.length; index < length; index++) {
                    if ( null == documentCore.getCellByName(this.cellNames[index]) ) {
                        message.error('单元格' + this.cellNames[index] + '非法，请重新输入');
                        return;
                    }
                }
                break;

            case CellFormulaCalc.CLEAR:
                const formula = documentCore.getCellFormula();
                this.formula.formulaType = formula ? formula.formulaType : undefined;
                this.formula.bClear = true;
                break;

            default:
                return;
        }

        documentCore.setCellFormula(this.formula, this.cellNames);
        this.close(true);
    }

    private checkNameValidate(str: string): boolean {
        const reg = '^[a-z0-9A-Z]+$'; // /[A-Za-z0-9]/;
        if (str.match(reg)) {
            return true;
        } else {
            return false;
        }
    }

    private checkAddFormulaValidate(str: string): boolean {
        const reg = '^[a-z0-9A-Z+]+$';
        if (str.match(reg)) {
            const reg1 = /[^\+]+/g;
            const reg2 = /[\+]/g;
            const names = str.match(reg1);
            const chars = str.match(reg2);

            if ( 1 > names.length || 0 > chars.length || (names.length - 1 !== chars.length) ) {
                return false;
            }

            this.cellNames = (names.slice(0, names.length - 1));
            return true;
        } else {
            return false;
        }
    }

    private checkMixFormulaValidate(str: string): boolean {
        const { infixes, elemNames } = MixFormulaParser.toInfixExpressions(str);
        if (MixFormulaParser.validateInfixExpression(infixes)) {
            this.cellNames = elemNames.slice();
            return true;
        }
        return false;
    }

}


