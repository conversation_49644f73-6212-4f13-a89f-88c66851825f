import { DocumentCore } from '../../model/DocumentCore';
import { EmrEditor } from '../../components/editor/Main';
import { ResultType } from '../commonDefines';
export default class CursorPosition {
    private _documentCore: DocumentCore;
    private _host: EmrEditor;
    constructor(host: EmrEditor) {
        this._host = host;
        this._documentCore = host.state.documentCore;
    }

    public getSelectionRangeStart(): string {
        return this._documentCore.getSelectionRangeStart();
    }

    public getSelectionRangeEnd(): string {
        return this._documentCore.getSelectionRangeEnd();
    }

    public  selectOneArea2(sStartPos: string, sEndPos: string): void {
        const reg = /^(\d+-)+\d+$/;
        if (!reg.test(sStartPos) || !reg.test(sEndPos)) {
            return;
        }

        const result = this._documentCore.selectArea(sStartPos, sEndPos);
        if (result === ResultType.Success) {
            const host = this._host;
            host.setSelections();
            host.handleRefresh(0);
        }
    }

    /**
     * 在当前光标位置选中字符
     * @param nCharNumber 选中长度
     * @param direction 选择方向： 1-- 左，2 -- 右
     * @returns 
     */
    public selectOneArea(nCharNumber: number, direction: number): void {
        const result = this._documentCore.selectAreaByDirection(nCharNumber, direction);
        if (result === ResultType.Success) {
            const host = this._host;
            host.setSelections();
            host.handleRefresh(0);
        }
    }

    public setFocus(flag: boolean): number {
        if (typeof flag !== 'boolean') {
            return ResultType.ParamError;
        }
        // this._host.setStopCursorBlur(false);
        const res = this._host.onFocus(flag);
        if (res && flag === false) {
            this._host.addTabindex();
        }
        return res ? ResultType.Success : ResultType.Failure;
    }

    public selectAllDoc(): void {
        const result = this._documentCore.selectAll();
        if (result === ResultType.Success) {
            this._host.setSelections();
            this._host.handleRefresh();
        }
    }

    public jumpToOnePosition(sPos: string): number {
        if (!/^(\d+-)+\d+$/.test(sPos)) {
            return ResultType.ParamError;
        }


        this._host.contentRef?.current?.resetTextArea();

        const result = this._documentCore.jumpToOnePosition(sPos);
        if (result === ResultType.Success) {
            this._host.handleRefresh();
        }

        return result;
    }

    public jumpToPage(pageIndex: number): void {
        pageIndex -= 1;
        const documentCore = this._documentCore;
        const pageCount = documentCore.getPageCount();
        if (-1 >= pageIndex || pageCount <= pageIndex ) {
            return ;
        }


        this._host.contentRef?.current?.resetTextArea();

        documentCore.setControlActiveByType();
        documentCore.jumpToPage(pageIndex);
        // this._host.setScrollbarToPage(pageIndex);
        this._host.handleRefresh();
    }

    public jumpToFirstPage(): void {
        this.jumpToPage(1);
    }

    public jumpToLastPage(): void {
        const documentCore = this._documentCore;
        documentCore.setControlActiveByType();
        const pageIndex = documentCore.getPageCount();
        this.jumpToPage(pageIndex);
    }

    public jumpToEndOfPage(): void {
        const documentCore = this._documentCore;
        documentCore.setControlActiveByType();
        const pageIndex = documentCore.getCurPageIndex();
        documentCore.jumpToPage(pageIndex, false);
        this._host.handleRefresh();
    }

    public jumpToStartOfPage(): void {
        const documentCore = this._documentCore;
        documentCore.setControlActiveByType();
        const pageIndex = documentCore.getCurPageIndex();
        documentCore.jumpToPage(pageIndex);
        this._host.handleRefresh();
    }

    public jumpToFileEnd(): void {
        const documentCore = this._documentCore;
        documentCore.setControlActiveByType();
        const pageIndex = documentCore.getPageCount();
        documentCore.jumpToPage(pageIndex - 1, false);
        this._host.handleRefresh();
    }
}
