﻿/* http://keith-wood.name/svg.html
   SVG filters for jQuery v1.5.0.
   Written by <PERSON> (kbwood{at}iinet.com.au) August 2007.
   Available under the MIT (http://keith-wood.name/licence.html) license.
   Please attribute the author if you use it. */

// Follow the UMD template https://github.com/umdjs/umd/blob/master/templates/jqueryPlugin.js
!function(e){"function"==typeof define&&define.amd?define(["jquery"],e):"object"==typeof module&&module.exports?module.exports=function(t,r){return void 0===r&&(r="undefined"!=typeof window?require("jquery"):require("jquery")(t)),e(r),r}:e(jQuery)}(function(e){function t(e){this._wrapper=e}e.svg.addExtension("filters",t),e.extend(e.svg._wrapperClass.prototype,{filter:function(t,r,n,s,i,a,u){var o=this._args(arguments,["id","x","y","width","height"]);return this._makeNode(o.parent,"filter",e.extend({id:o.id,x:o.x,y:o.y,width:o.width,height:o.height},o.settings||{}))}}),e.extend(t.prototype,{distantLight:function(t,r,n,s,i){var a=this._wrapper._args(arguments,["result","azimuth","elevation"]);return this._wrapper._makeNode(a.parent,"feDistantLight",e.extend({result:a.result,azimuth:a.azimuth,elevation:a.elevation},a.settings||{}))},pointLight:function(t,r,n,s,i,a){var u=this._wrapper._args(arguments,["result","x","y","z"]);return this._wrapper._makeNode(u.parent,"fePointLight",e.extend({result:u.result,x:u.x,y:u.y,z:u.z},u.settings||{}))},spotLight:function(t,r,n,s,i,a,u,o,p){var l=this._wrapper._args(arguments,["result","x","y","z","toX","toY","toZ"],["toX"]),d=e.extend({result:l.result,x:l.x,y:l.y,z:l.z},null!=l.toX?{pointsAtX:l.toX,pointsAtY:l.toY,pointsAtZ:l.toZ}:{});return this._wrapper._makeNode(l.parent,"feSpotLight",e.extend(d,l.settings||{}))},blend:function(t,r,n,s,i,a){var u=this._wrapper._args(arguments,["result","mode","in1","in2"]);return this._wrapper._makeNode(u.parent,"feBlend",e.extend({result:u.result,mode:u.mode,in_:u.in1,in2:u.in2},u.settings||{}))},colorMatrix:function(t,r,n,s,i,a){var u=this._wrapper._args(arguments,["result","in1","type","values"]);if(e.isArray(u.values)){for(var o="",p=0;p<u.values.length;p++)o+=(0===p?"":" ")+u.values[p].join(" ");u.values=o}else"object"==typeof u.values&&(u.settings=u.values,u.values=null);var l=e.extend({result:u.result,in_:u.in1,type:u.type},null!=u.values?{values:u.values}:{});return this._wrapper._makeNode(u.parent,"feColorMatrix",e.extend(l,u.settings||{}))},componentTransfer:function(t,r,n,s){for(var i=this._wrapper._args(arguments,["result","functions"]),a=this._wrapper._makeNode(i.parent,"feComponentTransfer",e.extend({result:i.result},i.settings||{})),u=["R","G","B","A"],o=0;o<Math.min(4,i.functions.length);o++){var p=i.functions[o],l=e.extend({type:p[0]},"table"===p[0]||"discrete"===p[0]?{tableValues:p[1].join(" ")}:"linear"===p[0]?{slope:p[1],intercept:p[2]}:"gamma"===p[0]?{amplitude:p[1],exponent:p[2],offset:p[3]}:{});this._wrapper._makeNode(a,"feFunc"+u[o],l)}return a},composite:function(t,r,n,s,i,a,u,o,p,l){var d=this._wrapper._args(arguments,["result","operator","in1","in2","k1","k2","k3","k4"],["k1"]),h=e.extend({result:d.result,operator:d.operator,in:d.in1,in2:d.in2},null!=d.k1?{k1:d.k1,k2:d.k2,k3:d.k3,k4:d.k4}:{});return this._wrapper._makeNode(d.parent,"feComposite",e.extend(h,d.settings||{}))},convolveMatrix:function(t,r,n,s,i){for(var a=this._wrapper._args(arguments,["result","order","matrix"]),u="",o=0;o<a.matrix.length;o++)u+=(0===o?"":" ")+a.matrix[o].join(" ");return a.matrix=u,this._wrapper._makeNode(a.parent,"feConvolveMatrix",e.extend({result:a.result,order:a.order,kernelMatrix:a.matrix},a.settings||{}))},diffuseLighting:function(t,r,n,s){var i=this._wrapper._args(arguments,["result","colour"],["colour"]);return this._wrapper._makeNode(i.parent,"feDiffuseLighting",e.extend(e.extend({result:i.result},i.colour?{lightingColor:i.colour}:{}),i.settings||{}))},displacementMap:function(t,r,n,s,i){var a=this._wrapper._args(arguments,["result","in1","in2"]);return this._wrapper._makeNode(a.parent,"feDisplacementMap",e.extend({result:a.result,in_:a.in1,in2:a.in2},a.settings||{}))},flood:function(t,r,n,s,i,a,u,o,p){var l=this._wrapper._args(arguments,["result","x","y","width","height","colour","opacity"]);arguments.length<6&&(l.colour=l.x,l.opacity=l.y,l.settings=l.width,l.x=null);var d=e.extend({result:l.result,floodColor:l.colour,floodOpacity:l.opacity},null!=l.x?{x:l.x,y:l.y,width:l.width,height:l.height}:{});return this._wrapper._makeNode(l.parent,"feFlood",e.extend(d,l.settings||{}))},gaussianBlur:function(t,r,n,s,i,a){var u=this._wrapper._args(arguments,["result","in1","stdDevX","stdDevY"],["stdDevY"]);return this._wrapper._makeNode(u.parent,"feGaussianBlur",e.extend({result:u.result,in_:u.in1,stdDeviation:u.stdDevX+(u.stdDevY?" "+u.stdDevY:"")},u.settings||{}))},image:function(t,r,n,s){var i=this._wrapper._args(arguments,["result","href"]),a=this._wrapper._makeNode(i.parent,"feImage",e.extend({result:i.result},i.settings||{}));return a.setAttributeNS(e.svg.xlinkNS,"href",i.href),a},merge:function(t,r,n,s){for(var i=this._wrapper._args(arguments,["result","refs"]),a=this._wrapper._makeNode(i.parent,"feMerge",e.extend({result:i.result},i.settings||{})),u=0;u<i.refs.length;u++)this._wrapper._makeNode(a,"feMergeNode",{in_:i.refs[u]});return a},morphology:function(t,r,n,s,i,a,u){var o=this._wrapper._args(arguments,["result","in1","operator","radiusX","radiusY"],["radiusY"]);return this._wrapper._makeNode(o.parent,"feMorphology",e.extend({result:o.result,in_:o.in1,operator:o.operator,radius:o.radiusX+(o.radiusY?" "+o.radiusY:"")},o.settings||{}))},offset:function(t,r,n,s,i,a){var u=this._wrapper._args(arguments,["result","in1","dx","dy"]);return this._wrapper._makeNode(u.parent,"feOffset",e.extend({result:u.result,in_:u.in1,dx:u.dx,dy:u.dy},u.settings||{}))},specularLighting:function(t,r,n,s,i,a,u){var o=this._wrapper._args(arguments,["result","in1","surfaceScale","specularConstant","specularExponent"],["surfaceScale","specularConstant","specularExponent"]);return this._wrapper._makeNode(o.parent,"feSpecularLighting",e.extend({result:o.result,in_:o.in1,surfaceScale:o.surfaceScale,specularConstant:o.specularConstant,specularExponent:o.specularExponent},o.settings||{}))},tile:function(t,r,n,s,i,a,u,o){var p=this._wrapper._args(arguments,["result","in1","x","y","width","height"]);return this._wrapper._makeNode(p.parent,"feTile",e.extend({result:p.result,in_:p.in1,x:p.x,y:p.y,width:p.width,height:p.height},p.settings||{}))},turbulence:function(t,r,n,s,i,a){var u=this._wrapper._args(arguments,["result","type","baseFreq","octaves"],["octaves"]);return this._wrapper._makeNode(u.parent,"feTurbulence",e.extend({result:u.result,type:u.type,baseFrequency:u.baseFreq,numOctaves:u.octaves},u.settings||{}))}})});
