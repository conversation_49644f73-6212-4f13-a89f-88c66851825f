
import {ContentType} from './DataType';
export class PasteContent {
    public content: string;
    public image: File;
    public type: ContentType;

    constructor(content: string, type: string, image?: File) {
        switch (type) {
            case ContentType.TextPlain:
                this.type = ContentType.TextPlain;
                break;
            case ContentType.TextHtml:
                this.type = ContentType.TextHtml;
                break;
            case ContentType.TextApollo:
                this.type = ContentType.TextApollo;
                break;
            case ContentType.TextRtf:
                this.type = ContentType.TextRtf;
                break;
            case ContentType.FileImage:
                this.type = ContentType.FileImage;
                break;
            default:
                // console.log(type);
                this.type = ContentType.TextPlain;
        }
        this.content = content;
        this.image = image;
    }
}

export class CopyContent {
    public id: number;
    public content: string;
}
