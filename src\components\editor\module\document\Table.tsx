import * as React from 'react';
import { numtoFixed2 } from '../../../../common/commonDefines';
import { IDocumentTable } from '../../../../model/TableProperty';

export default class Table extends React.Component<IDocumentTable> {
    constructor(props: IDocumentTable) {
        super(props);
    }

    public render(): any {
        let className;
        if (this.props.bNISTable) {
            className = 'nistable';
        }
        return (
            <g data-key='table-border' className={className}>
                {this.renderBackground()}
                {this.renderBorderLine()}
                {this.renderMessageBorder()}
            </g>
        );
    }

    private renderMessageBorder(): any {
        const { tableCells } = this.props;
        const messageBorder: any[] = [];
        tableCells.forEach(cell => {
            if (cell.bValidData === false) {
                messageBorder.push(
                    <rect 
                        key={cell.id}
                        width={cell.width}
                        height={cell.height}
                        x={cell.x}
                        y={cell.y}
                        data-key={cell.id}
                        className='bpTimeCell-rect-warn'
                        fill={'none'}
                    />
                );
            }
        });
        return <g className={'tablecell-messageBorder'}>{messageBorder}</g>;
    }

    private renderBorderLine(): any {
        // const { tableBorderLines, scale } = this.props;
        // return tableBorderLines.map((item, index) => {
        const { scale, bNISTable } = this.props;
        const lines = this.parseLine();
        // console.log(lines)
        let leftLineGap: any;
        let rightLineGap: any;
        let color: string;
        if (bNISTable) {
            color = '#ACB4C1';
        }
        const newLines = lines.map((item, index) => {
            if (!item || isNaN(item.y1)) {
                return;
            }

            let name = !item.bDiagonalLine ? '' : 'diagonalLine';
            let line: any;
            // let className;
            if (item.className) {
                name += item.className;
            }
            if (item.bLeftLineGap === true) {
                line = (
                    <rect
                        x={item.x1}
                        y={item.y1}
                        key={index}
                        width='8'
                        height={item.y2 - item.y1}
                        fill='url(#gradLeft)'
                    />
                );
                if (leftLineGap === undefined) {
                    leftLineGap = this.getLeftLineGap();
                }
            } else if (item.bRightLineGap === true) {
                line = (
                <rect
                    x={item.x1 - 8}
                    y={item.y1}
                    key={index}
                    width='8'
                    height={item.y2 - item.y1}
                    fill='url(#gradRight)'
                />
                );
                if (rightLineGap === undefined) {
                    rightLineGap = this.getRightLineGap();
                }
            } else {
                line = (
                <line
                    key={index++}
                    x1={numtoFixed2(item.x1 * scale)}
                    y1={numtoFixed2(item.y1 * scale)}
                    x2={numtoFixed2(item.x2 * scale)}
                    y2={numtoFixed2(item.y2 * scale)}
                    stroke={color || item.stroke}
                    strokeWidth={item.strokeWidth}
                />
                );
            }
            return line;
        });
        if (leftLineGap || rightLineGap) {
            newLines.unshift(
                <defs key={newLines.length}>
                    {leftLineGap}
                    {rightLineGap}
                </defs>
            );
        }
        return newLines;
    }

    private getRightLineGap(): any {
        return (
            <linearGradient id='gradRight' x1='0%' y1='0%' x2='100%' y2='0%'>
                <stop offset='0%' style={{stopColor: '#f0f0f0', stopOpacity: 0.4}}/>
                <stop offset='35%' style={{stopColor: '#eee', stopOpacity: 0.5}}/>
                <stop offset='80%' style={{stopColor: '#bbb', stopOpacity: 0.7}}/>
                <stop offset='90%' style={{stopColor: '#aaa', stopOpacity: 1}}/>
                <stop offset='100%' style={{stopColor: '#aaa', stopOpacity: 1}}/>
            </linearGradient>
        );
    }

    private getLeftLineGap(): any {
        return (
            <linearGradient id='gradLeft' x1='0%' y1='0%' x2='100%' y2='0%'>
                <stop offset='0%' style={{stopColor: '#aaa', stopOpacity: 1}}/>
                <stop offset='10%' style={{stopColor: '#aaa', stopOpacity: 1}}/>
                <stop offset='20%' style={{stopColor: '#bbb', stopOpacity: 0.7}}/>
                <stop offset='70%' style={{stopColor: '#eee', stopOpacity: 0.5}}/>
                <stop offset='100%' style={{stopColor: '#f0f0f0', stopOpacity: 0.4}}/>
            </linearGradient>
        );
    }

    private parseLine(): any[] {
        const { tableBorderLines, bFixedTable } = this.props;
        let lines = [];
        if (tableBorderLines.length === 0) {
            return lines;
        }

        const rows = {};
        const cols = {};
        let maxX: string;
        let minX: string;
        if (bFixedTable) {
            maxX = (tableBorderLines[0] as any).maxLeftX + '';
            minX = (tableBorderLines[0] as any).minRightX + '';
        }

        tableBorderLines.forEach((line: any) => {
            if (bFixedTable !== true && line.bFixed === true) {
                return;
            }
            if (line.strokeWidth === 0) {
                return;
            }
            if ( line.bDiagonalLine ) {
                lines = lines.concat(line);
                return;
            }

            if (line.bSlash) {
                lines.push(line);
                return;
            }
            // 先转换成字符串，后面好比较
            const x1 = line.X1 = line.x1 + '';
            const x2 = line.X2 = line.x2 + '';
            const y1 = line.Y1 = line.y1 + '';
            const y2 = line.Y2 = line.y2 + '';
            line.StrokeWidth = line.strokeWidth + '';

            // 行数据放在一起
            if (y1 === y2) {
                const xx: string = y1 + y2;
                if (rows[xx]) {
                    rows[xx].push(line);
                } else {
                    rows[xx] = [line];
                }
            }

            // 列数据放在一起
            if (x1 === x2) {
                const yy: string = x1 + x2;
                if (cols[yy]) {
                    cols[yy].push(line);
                } else {
                    cols[yy] = [line];
                }
                if (bFixedTable) {
                    if (x1 === maxX) {
                        line.bLeftLineGap = true;
                    } else if (x1 === minX) {
                        line.bRightLineGap = true;
                    }
                }
            }
        });
        const rowLines = this.getRowLines(rows);
        const colLines = this.getColLines(cols);
        lines = lines.concat(rowLines);
        lines = lines.concat(colLines);
        return lines;
    }

    private getRowLines(rows: any): any[] {
        const keys = Object.keys(rows);
        if (!keys.length) {
            return;
        }

        const lines = [];
        keys.forEach((key) => {
            const cells = rows[key].sort((a, b) => a.x1 - b.x1);
            // console.log(cells);
            let line: any;
            const length = cells.length - 1;
            cells.forEach((cell, index) => {
                if (index === length) {
                    if (index === 0 && line === undefined) {
                        line = {...cell};
                    }
                    lines.push(line);
                    return;
                }

                if (index === 0) {
                    line = {...cell};
                }

                const nextLine = cells[index + 1];
                // 所有条件相同才会平成 同一条直线
                if (cell.X2 === nextLine.X1 && cell.stroke === nextLine.stroke && cell.StrokeWidth ===
                    nextLine.StrokeWidth) {
                    line.x2 = nextLine.x2;
                } else {
                    lines.push(line);
                    // 重新构建新的直线
                    line = {...nextLine};
                }
            });
        });
        // console.log(lines);
        return lines;
    }

    private getColLines(cols: any): any[] {
        const keys = Object.keys(cols);
        if (!keys.length) {
            return;
        }

        const lines = [];
        keys.forEach((key) => {
            const datas = cols[key].sort((a, b) => a.x1 - b.x1);
            const length = datas.length - 1;
            let line: any;
            datas.forEach((data, index) => {
                if (index === length) {
                    if (index === 0 && line === undefined) {
                        line = {...data};
                    }
                    lines.push(line);
                    return;
                }

                if (index === 0) {
                    line = {...data};
                }

                const nextLine = datas[index + 1];
                if (data.Y2 === nextLine.Y1 && data.stroke === nextLine.stroke && data.StrokeWidth ===
                nextLine.StrokeWidth) {
                    line.y2 = nextLine.y2;
                } else {
                    lines.push(line);
                    // 重新构建新的直线
                    line = {...nextLine};
                }
            });
        });

        return lines;
    }

    private renderBackground(): any {
        const { tableBackground, scale } = this.props;
        return tableBackground.map((item, index) => {
            return (
                <rect
                    key={index}
                    line-key={index++}
                    width={numtoFixed2(item.width * scale)}
                    height={numtoFixed2(item.height * scale)}
                    x={numtoFixed2(item.x * scale)}
                    y={numtoFixed2(item.y * scale)}
                    fill={item.fill.toString()}
                    fillOpacity={item.fillOpacity}
                />
            );
        });
    }
}
