import { NewControl } from './NewControl';
import DocumentContentBase from '../DocumentContentBase';
// tslint:disable-next-line: max-line-length
import { INewControlProperty, IStructParamJson, parseAndType, parseResultType, ResultType } from '../../../common/commonDefines';
import ParaPortion from '../Paragraph/ParaPortion';
import TextProperty from '../TextProperty';
import Paragraph from '../Paragraph';
import ParaTextExtend from '../Paragraph/ParaTextExtend';
import { ParaTextName } from '../Paragraph/ParagraphContent';
import {GlobalEvent as gEvent, GlobalEventName as gEventName} from '../../../common/GlobalEvent';
// tslint:disable-next-line: max-line-length
import { ChangeNewControlCheckBoxChecked, ChangeNewControlGroup, ChangeNewControlLabelCode, ChangeNewControlShowRight } from './NewControlChange';

const checkedStrs = ['☑', '☐'];
/**
 * 结构化元素: 文本框
 */
export class NewControlCheck extends NewControl {
    private bSelected: boolean;
    private bShowRight: boolean;
    private text: string;
    private labelCode: string;
    private bPrintSelected: boolean;
    private boxPortion: ParaPortion;
    private docId: number;
    private group: string;
    constructor(parent: DocumentContentBase, name: string, property: INewControlProperty, text?: string) {
        super(parent, name, property, property.label || '');
        // this.setEditProtect(true);
        this.bSelected = property.checked === undefined ? false : property.checked;
        this.bShowRight = property.showRight === undefined ? false : property.showRight;
        this.bPrintSelected = property.printSelected === undefined ? false : property.printSelected;
        this.text = property.label || '';
        this.labelCode = property.labelCode || '';
        this.setPlaceHolder(false);
        // this.addCheckBox();
        this.group = property.group;
    }

    public setProperty(property: INewControlProperty): number {
        let res = parseAndType(super.setProperty(property));
        res = parseAndType(this.setCheckBoxSelect(property.checked)) & res;
        res = parseAndType(this.setShowRight(property.showRight)) & res;
        res = parseAndType(this.setCheckBoxText(property.label)) & res;
        res = parseAndType(this.setLabelCode(property.labelCode)) & res;
        res = parseAndType(this.setPrintSelected(property.printSelected)) & res;
        res = parseAndType(this.setGroup(property.group)) & res;
        return parseResultType(res);
    }

    public getProperty(): INewControlProperty {
        const newControlProperty = super.getProperty();
        newControlProperty.showRight = this.bShowRight;
        newControlProperty.checked = this.bSelected;
        newControlProperty.printSelected = this.bPrintSelected;
        newControlProperty.label = this.text;
        newControlProperty.labelCode = this.labelCode;
        newControlProperty.group = this.group;

        return newControlProperty;
    }

    public setCheckBoxSelect(bSelected: boolean): number {
        if (this.bSelected === bSelected || bSelected == null) {
            return ResultType.UnEdited;
        }

        this.setCheckBoxChecked(bSelected);
        return ResultType.Success;
    }

    public setNewControlText(text: string): number {
        return ResultType.UnEdited;
    }

    public setNewControlTextByJson(json: IStructParamJson): number {
        const num = json.isCheck;
        if (typeof num !== 'number' || ![0, 1].includes(num)) {
            return ResultType.UnEdited;
        }

        const bSelected = json.isCheck === 1;
        if (this.bSelected === bSelected) {
            return ResultType.UnEdited;
        }
        this.setCheckBoxChecked(bSelected);

        return ResultType.Success;
    }

    public setCheckBoxChecked(bSelected?: boolean): void {
        if (!this.boxPortion) {
            return;
        }

        this.setCheckBoxChecked2(bSelected, false);

        if ( this.group ) {
            this.triggerGroup();
        } else {
            this.setTextFlag(true);
            this.setDirty();
        }
    }

    public getNewControlText3(): string {
        return this.bSelected ? 'true' : 'false';
        // if (!text) {
        //     return;
        // }
        // if (this.bShowRight === true) {
        //     return text.slice(0, -1);
        // }
        // return text.slice(1);
    }

    public setCheckBoxChecked2(bSelected: boolean, bDirty?: boolean): number {
        if (!this.boxPortion) {
            return ResultType.Failure;
        }

        const history = this.getHistory();
        if ( history ) {
            history.addChange(new ChangeNewControlCheckBoxChecked(this, this.bSelected, bSelected));
        }

        if (bSelected === undefined) {
            this.bSelected = !this.bSelected;
        } else {
            this.bSelected = bSelected;
        }

        this.boxPortion.content[0].repalceText((this.bSelected ? checkedStrs[0] : checkedStrs[1]), history);
        if (bDirty !== false) {
            this.setTextFlag(true);
            this.setDirty();
        }

        gEvent.setEvent(this.getDocId(), gEventName.CheckedChange, this.getNewControlName(), this.bSelected);

        return ResultType.Success;
    }

    public isSelected(): boolean {
        return this.bSelected;
    }

    public isShowRight(): boolean {
        return this.bShowRight;
    }

    public getCheckLabel(): string {
        return this.text;
    }

    public getLabelCode(): string {
        return this.labelCode;
    }

    public isPrintSelected(): boolean {
        return this.bPrintSelected;
    }

    public getPrintSelected(): boolean {
        return this.bPrintSelected;
    }

    public setPrintSelected(bPrintSelected: boolean): number {
        if (this.bPrintSelected === bPrintSelected || bPrintSelected == null) {
            return ResultType.UnEdited;
        }

        this.bPrintSelected = bPrintSelected;
        this.setDirty();
        return ResultType.Success;
    }

    public setShowRight(bShowRight: boolean, bRefresh: boolean = true): number {
        if (this.bShowRight === bShowRight || bShowRight == null) {
            return ResultType.UnEdited;
        }

        const history = this.getHistory();
        if ( history ) {
            history.addChange(new ChangeNewControlShowRight(this, this.bShowRight, bShowRight));
        }

        this.bShowRight = bShowRight;
        const portion: ParaPortion = this.boxPortion;
        const para = portion.paragraph;
        let index: number;
        index = para.content.findIndex((item) => item === portion);
        // para.content.splice(index, 1);
        para.contentRemove2(index, 1);
        if (bShowRight === true) {
            const pos = this.getEndPos();
            index = pos.get(pos.getDepth() - 1);
        } else {
            const pos = this.getStartPos();
            index = pos.get(pos.getDepth() - 1) + 1;
        }
        para.addToContent(index, portion);
        if (bRefresh === false) {
            this.recalculate();
        }
        this.setDirty();
        return ResultType.Success;
    }

    public setCheckBoxText(text: string, bRefresh: boolean = true): number {
        if (text == null || this.text === text) {
            return ResultType.UnEdited;
        }
        this.text = text;
        super.setNewControlText(text);
        let index: number;
        if (this.bShowRight) {
            const pos = this.getEndPos();
            index = pos.get(pos.getDepth() - 1);
        } else {
            const pos = this.getStartPos();
            index = pos.get(pos.getDepth() - 1) + 1;
        }
        this.boxPortion.paragraph.addToContent(index, this.boxPortion);
        if ( this.isTrackRevisions() ) {
            const textPortion = this.getPlaceHolder();
            if ( textPortion ) {
                textPortion.resetReviewTypeAndInfo();
            }
        }

        this.setDirty();
        return ResultType.Success;
    }

    public setLabelCode(labelCode: string): number {
        if (labelCode == null || this.labelCode === labelCode) {
            return ResultType.UnEdited;
        }

        const history = this.getHistory();
        if ( history ) {
            history.addChange(new ChangeNewControlLabelCode(this, this.labelCode, labelCode));
        }

        this.labelCode = labelCode;
        this.setDirty();
        return ResultType.Success;
    }

    public addToParagragh( para: Paragraph, curPos: number ): void {
        super.addToParagragh(para, curPos);
        let index: number = curPos + 2;
        if (this.bShowRight) {
            index++;
        }
        this.addCheckBox(index);
    }

    public addContent(): void {
        let index: number;
        let para: Paragraph;
        if (this.bShowRight) {
            const endBorder = this.getEndBorderPortion();
            para = endBorder.paragraph;
            index = para.content.findIndex((item) => item === endBorder) - 1;
        } else {
            const startBorder = this.getStartBorderPortion();
            para = startBorder.paragraph;
            index = para.content.findIndex((item) => item === startBorder) + 1;
        }
        let portion = para.content[index];
        if (portion.content.length === 0) {
            if (this.bShowRight) {
                index--;
            } else {
                index++;
            }
            portion = para.content[index];
        }
        this.boxPortion = portion;
        const contents = portion.content;
        const str = (contents[0]?.content || checkedStrs[(this.bSelected ? 0 : 1)]);
        if (!checkedStrs.includes(str)) {
            return;
        }
        // 修复选中占位符跟文本同一个Portion
        if (contents.length > 1) {
            const newPortion = new ParaPortion(para);
            newPortion.textProperty = portion.textProperty.copy();
            contents.slice(1)
            .forEach((item, itemIndex) => {
                newPortion.addToContent(itemIndex, item);
            });
            para.addToContent(index + 1, newPortion);
            portion.removeFromContent(1, contents.length - 1);
            // this.recalculate();
        }
        // const red = (this.isMustInput() === true) ? true : false;
        const text = new ParaTextExtend(str, ParaTextName.ParaCheckbox);
        portion.content[0] = text;
    }

    public toPrint(): void {
        super.toPrint();
        if (this.isHidden()) {
            return;
        }

        if (this.bSelected !== true && this.bPrintSelected) {
            this.setHidden(true, false);
        }
    }

    public getGroup(): string {
        return this.group;
    }

    public setGroup(group: string): number {
        if (this.group === group || group == null) {
            return ResultType.UnEdited;
        }

        const history = this.getHistory();
        if ( history ) {
            history.addChange(new ChangeNewControlGroup(this, this.group, group));
        }

        this.group = group;

        if ( this.getCascades() && group ) {
            this.setCascades([]);
        }

        this.setDirty();
        return ResultType.Success;
    }

    private getDocId(): number {
        if (this.docId === undefined) {
            const parent = this.getDocumentParent();
            const doc = parent.getTopDocument();
            this.docId = doc.id;
        }
        return this.docId;
    }

    private addCheckBox(index: number, para?: Paragraph): void {
        const portion: ParaPortion = this.getPlaceHolder();
        para = para || portion.paragraph;
        const newPortion = this.boxPortion = new ParaPortion(para);
        const textProperty = portion.textProperty;
        const textPro = new TextProperty();
        textPro.color = '#000';
        textPro.fontSize = textProperty.fontSize;
        textPro.fontFamily = textProperty.fontFamily;
        textPro.textDecorationLine = textProperty.textDecorationLine;
        newPortion.textProperty = new TextProperty(textPro);
        let str: string;
        if (this.bSelected) {
            str = checkedStrs[0];
        } else {
            str = checkedStrs[1];
        }
        // const red = (this.isMustInput() === true) ? true : false;
        const text = new ParaTextExtend(str, ParaTextName.ParaCheckbox);
        newPortion.addToContent(0, text);
        if (this.isHidden()) {
            newPortion.setHidden(true);
        }

        this.boxPortion = newPortion;
        para.addToContent(index, newPortion);
    }

}
