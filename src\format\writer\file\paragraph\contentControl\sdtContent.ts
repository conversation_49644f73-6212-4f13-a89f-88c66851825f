import { XmlComponent } from '../../xml-components';
import { Run } from '../run';
import { IContentControlAttributesProperties, Sdt, Section } from './contentControl';
import { RunProperties } from '../run/properties';
import { IContentControlStartElementVals } from './contentControlElements';
import { STD_START_DEFAULT, NewControlType, INewControlProperty } from '../../../../../common/commonDefines';
import { Revision } from '../revision/revision';
import { WriteStruct } from '@/common/struct/write';
import { XmlComment } from '../comment/comment';

export class SdtContent extends XmlComponent {
  constructor(flag: string) {
    super(flag);
  }

  public addRun(run: Run): SdtContent {
    this.root.push(run);
    return this;
  }

  public addSdt(attrs: IContentControlAttributesProperties, portionProp: RunProperties,
                contentControlElementVals: IContentControlStartElementVals,
                showPlaceholder: boolean = false, bSignatureBox: boolean = false): Sdt {

    // tslint:disable-next-line: max-line-length
    const { serialNumber, placeholder, helpTip, isMustFill, deleteProtect, editProtect, copyProtect, showBorder, borderString,
      editReverse, backgroundColorHidden, customProperty, tabJump, newControlHidden, showRight,
      secretType, fixedLength, maxLength, title, hideHasTitle,
      minValue, maxValue, precision, unit, forceValidate,
      retrieve, selectPrefixContent, prefixContent, separator, itemList, showValue,
      dateBoxFormat, customFormat, startDate, endDate, dateTime,
      checked, printSelected, label, labelCode, // checkbox
      showType, spaceNum, supportMultLines, // radiobox
      signatureCount, preText, signatureSeparator, postText, signaturePlaceholder, // signature box
      signatureRatio, rowHeightRestriction, // signature box
      // hierarchy, province, city, county, // address box
      cascade,
      bTextBorder,
      signType, alwaysShow, showSignBorder, identifier, codeLabel, valueLabel,
    } = contentControlElementVals;

    // TODO: alleviate attrs?
    const sdt = new Sdt(attrs, portionProp);

    // if values are default, do not save them
    if (serialNumber !== STD_START_DEFAULT.serialNumber) {
      sdt.addSerialNumber(serialNumber);
    }

    // tslint:disable-next-line: triple-equals
    if (identifier != STD_START_DEFAULT.identifier) {
      sdt.addIdentifier(identifier);
    }

    if (placeholder !== STD_START_DEFAULT.placeholder) {
      sdt.addPlaceHolder(placeholder);
    }
    if (codeLabel !== undefined) {
      sdt.addCodeLabel(codeLabel);
    }
    if (valueLabel !== undefined) {
      sdt.addValueLabel(valueLabel);
    }
    if (helpTip != null && helpTip !== STD_START_DEFAULT.helpTip) { // val passed undefined, should be bug
      sdt.addHelpTip(helpTip);
    }
    if (isMustFill !== STD_START_DEFAULT.isMustFill) {
      sdt.addIsMustFill(isMustFill + '');
    }
    if (deleteProtect !== STD_START_DEFAULT.deleteProtect) {
      sdt.addDeleteProtect(deleteProtect + '');
    }
    if (bTextBorder != null) {
      sdt.addTextBorder(bTextBorder + '');
    }
    if (editProtect != null) {
      sdt.addEditProtect(editProtect + '');
    }
    if (copyProtect !== STD_START_DEFAULT.copyProtect) {
      sdt.addCopyProtect(copyProtect + '');
    }
    if (cascade && cascade.length > 0) {
      sdt.addLogicEvent(JSON.stringify(cascade));
    }
    if (showBorder !== STD_START_DEFAULT.showBorder) {
      sdt.addShowBorder(showBorder + '');
    }
    if (borderString !== STD_START_DEFAULT.borderString) {
      sdt.addBorderString(borderString);
    }
    if (editReverse !== STD_START_DEFAULT.editReverse) {
      sdt.addEditReverse(editReverse + '');
    }
    if (backgroundColorHidden !== STD_START_DEFAULT.backgroundColorHidden) {
      sdt.addBackgroundColorHidden(backgroundColorHidden + '');
    }
    if (customProperty instanceof Map && customProperty.size > 0) { // default empty map
      sdt.addCustomProperty(customProperty);
    }
    if (tabJump !== STD_START_DEFAULT.tabJump) {
      sdt.addTabJump(tabJump + '');
    }
    if (newControlHidden !== STD_START_DEFAULT.newControlHidden) {
      sdt.addNewControlHidden(newControlHidden + '');
    }

    // ui prop
    let showPlaceholderTemp = (showPlaceholder === true) ? 1 : 0;
    if (bSignatureBox === true) {
      // signaturebox's sub text struts would remove content and show ph
      showPlaceholderTemp = 1;
    }
    if (showPlaceholderTemp !== STD_START_DEFAULT.showPlaceholder) {
      sdt.addShowPlaceholder(showPlaceholderTemp + '');
    }

    // extent props
    if (attrs.type === NewControlType.TextBox || attrs.type === NewControlType.Section) {
      if (attrs.type === NewControlType.TextBox) {
        if (secretType !== STD_START_DEFAULT.secretType) {
          sdt.addSecretType(secretType + '');
        }
        if (fixedLength !== STD_START_DEFAULT.fixedLength) {
          sdt.addFixedLength(fixedLength + '');
        }
        if (maxLength !== STD_START_DEFAULT.maxLength) {
          sdt.addMaxLength(maxLength + '');
        }
        if (hideHasTitle !== STD_START_DEFAULT.hideHasTitle) {
          sdt.addHideHasTitle(hideHasTitle + '');
        }
        if (printSelected !== STD_START_DEFAULT.printSelected) {
          sdt.addPrintSelected(printSelected + '');
        }
      }

      if (title !== STD_START_DEFAULT.title) {
        sdt.addTitle(title + '');
      }
    } else if (attrs.type === NewControlType.NumberBox) {
      if (secretType !== STD_START_DEFAULT.secretType) { // shared
        sdt.addSecretType(secretType + '');
      }
      if (minValue !== STD_START_DEFAULT.minValue) {
        sdt.addMinValue(minValue + '');
      }
      if (maxValue != null && maxValue !== STD_START_DEFAULT.maxValue) { 
        sdt.addMaxValue(maxValue + '');
      }
      if (precision !== STD_START_DEFAULT.precision) {
        sdt.addPrecision(precision + '');
      }
      if (unit !== STD_START_DEFAULT.unit) {
        sdt.addUnit(unit);
      }
      if (forceValidate !== STD_START_DEFAULT.forceValidate) {
        sdt.addForceValidate(forceValidate + '');
      }
      if (printSelected !== STD_START_DEFAULT.printSelected) {
        sdt.addPrintSelected(printSelected + '');
      }
    } else if (attrs.type === NewControlType.Combox || attrs.type === NewControlType.MultiCombox ||
      attrs.type === NewControlType.ListBox || attrs.type === NewControlType.MultiListBox) {

      if (retrieve !== STD_START_DEFAULT.retrieve) {
        sdt.addRetrieve(retrieve + '');
      }
      if (selectPrefixContent !== STD_START_DEFAULT.selectPrefixContent) {
        sdt.addSelectPrefixContent(selectPrefixContent);
      }
      if (prefixContent !== STD_START_DEFAULT.prefixContent) {
        sdt.addPrefixContent(prefixContent);
      }
      if (separator !== STD_START_DEFAULT.separator) {
        sdt.addSeparator(separator);
      }
      if (showValue !== STD_START_DEFAULT.showValue) {
        sdt.addShowValue(showValue + '');
      }

      if (itemList.length > 0) {
        sdt.addItemList(itemList);
      }
      if (printSelected !== STD_START_DEFAULT.printSelected) {
        sdt.addPrintSelected(printSelected + '');
      }

    } else if (attrs.type === NewControlType.DateTimeBox) {
      if (dateBoxFormat !== STD_START_DEFAULT.dateBoxFormat) {
        sdt.addDateBoxFormat(dateBoxFormat + '');
      }
      // also rule out {}
      if (customFormat !== STD_START_DEFAULT.customFormat) {
        if (customFormat instanceof Object && Object.keys(customFormat).length > 0) {
          sdt.addCustomFormat(JSON.stringify(customFormat));
        }
      }
      if (startDate !== STD_START_DEFAULT.startDate) {
        sdt.addStartDate(startDate);
      }
      if (endDate !== STD_START_DEFAULT.endDate) {
        sdt.addEndDate(endDate);
      }
      // datetime: iso string
      if (dateTime !== STD_START_DEFAULT.dateTime) {
        sdt.addDateTime(dateTime);
      }
      if (printSelected !== STD_START_DEFAULT.printSelected) {
        sdt.addPrintSelected(printSelected + '');
      }

    } else if (attrs.type === NewControlType.RadioButton) {
      if (showRight !== STD_START_DEFAULT.showRight) {
        sdt.addShowRight(showRight + '');
      }
      if (printSelected !== STD_START_DEFAULT.printSelected) {
        sdt.addPrintSelected(printSelected + '');
      }
      if (itemList.length > 0) {
        sdt.addItemList(itemList);
      }
      if (showType !== STD_START_DEFAULT.showType) {
        // why here can have 'NaN'?
        if (isNaN(showType) === false) {
          sdt.addShowType(showType + '');
        }
      }
      if (spaceNum !== STD_START_DEFAULT.spaceNum) {
        sdt.addSpaceNum(spaceNum + '');
      }
      if (supportMultLines !== STD_START_DEFAULT.supportMultLines) {
        sdt.addSupportMultLines(supportMultLines + '');
      }

    } else if (attrs.type === NewControlType.CheckBox) {
      if (showRight !== STD_START_DEFAULT.showRight) {
        sdt.addShowRight(showRight + '');
      }
      if (checked !== STD_START_DEFAULT.checked) {
        sdt.addChecked(checked + '');
      }
      if (printSelected !== STD_START_DEFAULT.printSelected) {
        sdt.addPrintSelected(printSelected + '');
      }
      if (label !== STD_START_DEFAULT.label) {
        sdt.addLabel(label);
      }
      if (labelCode !== STD_START_DEFAULT.labelCode) {
        sdt.addLabelCode(labelCode);
      }

    } else if (attrs.type === NewControlType.SignatureBox) {
      if (signatureCount !== STD_START_DEFAULT.signatureCount) {
        sdt.addSignatureCount(signatureCount + '');
      }
      if (preText !== STD_START_DEFAULT.preText) {
        sdt.addPreText(preText);
      }
      if (signatureSeparator !== STD_START_DEFAULT.signatureSeparator) {
        sdt.addSignatureSeparator(signatureSeparator);
      }
      if (postText !== STD_START_DEFAULT.postText) {
        sdt.addPostText(postText);
      }
      if (signaturePlaceholder !== STD_START_DEFAULT.signaturePlaceholder) {
        sdt.addSignaturePlaceholder(signaturePlaceholder);
      }
      if (signatureRatio !== STD_START_DEFAULT.signatureRatio) {
        sdt.addSignatureRatio(signatureRatio + '');
      }
      if (rowHeightRestriction !== STD_START_DEFAULT.rowHeightRestriction) {
        sdt.addRowHeightRestriction(rowHeightRestriction + '');
      }

      if (signType !== STD_START_DEFAULT.signType) {
        sdt.addSignType(signType + '');
      }
      if (alwaysShow !== STD_START_DEFAULT.alwaysShow) {
        sdt.addAlwaysShow(alwaysShow + '');
      }
      if (showSignBorder !== STD_START_DEFAULT.showSignBorder) {
        sdt.addShowSignBorder(showSignBorder + '');
      }

    }
    // else if (attrs.type === NewControlType.AddressBox) {
    //   if (hierarchy != STD_START_DEFAULT.hierarchy) {
    //     sdt.addHierarchy(hierarchy + '');
    //   }
    //   if (province != STD_START_DEFAULT.province) {
    //     sdt.addProvince(JSON.stringify(province));
    //   }
    //   if (province != STD_START_DEFAULT.city) {
    //     sdt.addProvince(JSON.stringify(city));
    //   }
    //   if (province != STD_START_DEFAULT.county) {
    //     sdt.addProvince(JSON.stringify(county));
    //   }
    // }
    // else {
      //
    // }

    // new sdt (TODO: need the container always?)
    sdt.addSdtContent();

    this.root.push(sdt);
    return sdt;
  }

  public addSdt2(attrs: IContentControlAttributesProperties, portionProp: RunProperties,
                 contentControlElementVals: INewControlProperty, showPlaceholder: boolean = false,
                 bSignatureBox: boolean = false): Sdt {
    const sdt = new Sdt(attrs, portionProp);
    const struct = new WriteStruct(contentControlElementVals, sdt);

    sdt.addSdtContent();

    this.root.push(sdt);
    return sdt;
  }

  public addRevision(revision: Revision): SdtContent {
    this.root.push(revision);
    return this;
  }

  public addComment(comment: XmlComment): SdtContent {
    this.root.push(comment);
    return this;
  }

  /**
   * for section
   * @param sdt
   * @returns
   */
  public addSdtContent(sdt: Sdt | Section): SdtContent {
    this.root.push(sdt);
    return this;
  }
}
