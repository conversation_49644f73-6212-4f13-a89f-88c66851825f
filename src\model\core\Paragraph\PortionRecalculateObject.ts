import ParaPortion from './ParaPortion';
import { ParaElementBase } from './ParaElementBase';
import { ParaElementType } from './ParagraphContent';

/**
 * 重新计算排版行时，保存portion在当前行的信息
 */
export default class PortionRecalculateObject {
    public startLine: number;  // portion的开始行号
    public startRange: number;  // portion的开始range
    public lines: number[];  // portion所在行号
    public content: ParaElementBase[];  // portion的内容

    constructor( startLine: number, startRange: number = 0 ) {
        this.startLine = startLine;
        this.startRange = startRange;
        this.lines = [];
        this.content = [];
    }

    /**
     * 保存portion所在行的信息
     * @param portion 需要保存的portion
     * @param bCopy 是否需要拷贝
     */
    public saveLines( portion: ParaPortion, bCopy: boolean ): void {
        if ( true === bCopy ) {
            const lines = portion.lines;

            for (let index = 0, count = lines.length; index < count; index++) {
                this.lines[index] = lines[index];
            }
        } else {
            this.lines = portion.lines;
        }
    }

    /**
     * 保存portion内容：开始行号等
     * @param para
     * @param bCopy
     */
    public saveContent( portion: ParaPortion, bCopy: boolean ): void {
        const content = portion.content;

        for (let index = 0, count = content.length; index < count; index++) {
            this.content[index] = content[index].saveRecalculateObject(bCopy);
        }
    }

    /**
     * 保存portion的内容，类型必须为：段落中的页码，图片等
     * @param portion
     * @param bCopy
     */
    public savePortionContent( portion: ParaPortion, bCopy: boolean ): void {
        const content = portion.content;

        for (let index = 0, index2 = 0, count = content.length; index < count; index++) {
            const item = portion.content[index];

            if ( ParaElementType.ParaPageNum === item.type ) {
                this.content[index] = content[index].saveRecalculateObject(bCopy);
            }
        }
    }

    /**
     * 重新写入保存的portion行信息
     * @param portion
     */
    public loadLines( portion: ParaPortion ): void {
        portion.startLine = this.startLine;
        portion.startRange = this.startRange;
        portion.lines = this.lines;
    }

    public loadContent( portion: ParaPortion ): void {
        const content = portion.content;

        for (let index = 0, count = content.length; index < count; index++) {
            content[index].loadRecalculateObject(this.content[index]);
        }
    }

    public loadPortionContent( portion: ParaPortion ): void {
        const content = portion.content;

        for (let index = 0, index2 = 0, count = content.length; index < count; index++) {
            const item = portion.content[index];

            if ( ParaElementType.ParaPageNum === item.type ) {
                item.loadRecalculateObject(this.content[index2]);
            }
        }
    }

    public compare( curLine: number, portion: ParaPortion, curRange: number = 0 ): boolean {
        const line = curLine - this.startLine;

        if ( ( 0 === this.lines.length ) && ( 0 === portion.lines.length || 0 === portion.linesLength ) ) {
            return false;
        }

        if ( this.startLine !== portion.startLine || line < 0
            || line >= this.getLinesCount() || line >= portion.getLinesCount() ) {
            return false;
        }

        const startPos1 = this.getRangeStartPos(line, curRange);
        const endPos1 = this.getRangeEndPos(line, curRange);

        const startPos2 = portion.getRangeStartPos(line, curRange);
        const endPos2 = portion.getRangeEndPos(line, curRange);

        if ( startPos1 !== startPos2 || endPos1 !== endPos2 ) {
            // console.log(this, portion);
            return false;
        }

        if ( ( ( !portion.content || ParaElementType.ParaPortion === portion.type ) && this.content.length > 0 )
             || ( ( portion.content && ParaElementType.ParaPortion !== portion.type )
             && this.content.length !== portion.content.length ) ) {
            return false;
        }

        return true;
    }

    public getLinesCount(): number {
        return this.lines[0];
    }

    public getRangeCount( lineIndex: number ): number {
        if ( this.lines[0] - 1 === lineIndex ) {
            return ( this.lines.length - this.lines[1 + lineIndex] ) / 2;
        }
    }

    public getRangeOffset( lineIndex: number, rangeIndex: number = 0 ): number {
        return (1 + this.lines[0] + this.lines[1 + lineIndex] + rangeIndex * 2);
    }

    /**
     * 获取在curLine中内容开始的索引
     * @param lineIndex
     * @param rangeIndex
     */
    public getRangeStartPos( lineIndex: number, rangeIndex: number = 0 ): number {
        return this.lines[this.getRangeOffset(lineIndex, rangeIndex)];
    }

    /**
     * 获取在curLine中内容结束的索引
     * @param lineIndex
     * @param rangeIndex
     */
    public getRangeEndPos( lineIndex: number, rangeIndex: number = 0 ): number {
        return this.lines[this.getRangeOffset(lineIndex, rangeIndex) + 1];
    }
}
