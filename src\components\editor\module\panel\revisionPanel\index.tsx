import Dialog from "@/components/editor/ui/Dialog";
import { DocumentCore } from "@/model/DocumentCore";
import React from "react";
import {
    GlobalEvent as gEvent,
    GlobalEventName as gEventName
} from '@/common/GlobalEvent';
import { IRevisions, RevisionPanel } from "./RevisionPanel";
import Paragraph from "@/model/core/Paragraph";

interface IProps {
    id: string;
    visible: boolean;
    documentCore: DocumentCore;
    close: (name: string, bRefresh?: boolean) => void;
    refresh: () => void;
}

interface IState {
    revisions: IRevisions[];
    userName: string;
}

export class RevisionPanelDialog extends React.Component<IProps, IState> {
    private visible: boolean;
    private panelRef: React.RefObject<RevisionPanel>;

    constructor(props: IProps) {
        super(props);
        this.visible = props.visible;
        this.state = {
            revisions: [],
            userName: '用户1',
        };
        this.panelRef = React.createRef();
    }


    public render() {
        const fullHeight = window.document.firstElementChild.clientHeight - 50;
        const height = fullHeight > 600 ? 600 : fullHeight < 230 ? 230 : fullHeight;
        return (
            <Dialog
                bCloseIcon={true}
                title="修订管理面板"
                visible={this.visible}
                width={400}
                height={height}
                close={this.close}
                open={this.refreshRevisions}
                noModal={true}
            >
                <RevisionPanel
                    handleActive={this.handleActive}
                    height={height - 60}
                    ref={this.panelRef}
                    revisions={this.state.revisions}
                    userName={this.state.userName}
                />
            </Dialog>
        );
    }

    public UNSAFE_componentWillReceiveProps(nextProps: Readonly<IProps>, nextContext: any): void {
        this.visible = nextProps.visible;
    }

    public componentDidMount(): void {
        const docId = this.props.documentCore.getCurrentId();
        gEvent.addEvent(docId, gEventName.ContentChange, this.refreshRevisions);
        gEvent.addEvent(docId, gEventName.RevisionChange, this.refreshRevisions);
        gEvent.addEvent(docId, gEventName.MoveCursor, this.onActiveRevision);
    }

    public componentWillUnmount(): void {
        const docId = this.props.documentCore.getCurrentId();
        gEvent.deleteEvent(docId, gEventName.ContentChange, this.refreshRevisions);
        gEvent.deleteEvent(docId, gEventName.RevisionChange, this.refreshRevisions);
        gEvent.deleteEvent(docId, gEventName.MoveCursor, this.onActiveRevision);
    }

    private onActiveRevision = (e) => {
        const { documentCore } = this.props;
        const para = documentCore.getCurrentParagraph();
        const manager = documentCore.getDocument().getRevisionsManager();
        if (para && manager && manager.hasElement(para.id)) {
            const portion = (para as Paragraph).getCurrentPortion();
            if (portion.isReviewType()) {
                const info = portion.getReviewInfo();
                this.panelRef.current?.activeRevision(portion.id);
            }
        }
    }

    private close = () => {
        this.props.close(this.props.id);
        this.visible = false;
        this.setState({});
    }

    private handleActive = (rev: IRevisions) => {
        if (!rev) return;
        const {documentCore} = this.props;
        const {pagePos} = rev;
        documentCore.jumpToOnePosition(pagePos.copy());
        this.props.refresh();
    }

    private refreshRevisions = (...params: any[]) => {
        const { documentCore, visible } = this.props;
        // 面板不可见时不进行更新
        if (!visible) return;
        const newUserName = documentCore.getCurRevisionSetting()?.userName || '用户1';
        const newRevisions = documentCore.getAllRevision() as IRevisions[];
        const {revisions, userName} = this.state;
        let isSame = userName == newUserName && revisions.length === newRevisions.length;
        if (!isSame || revisions.length !== 0) {
            this.setState({revisions: newRevisions.map(item => {
                const nids = item.ids.map(id => '' + id);
                item.key = item.userName + nids.join('-');
                return item;
            }), userName: newUserName});
        }
    }
}