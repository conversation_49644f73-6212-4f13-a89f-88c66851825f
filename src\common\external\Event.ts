
import {IExternalEvent} from './IExternalInterface';

import { EXTERNAL_STRUCT_TYPE, ICustomToolbarItem, IEventPosition } from '../commonDefines';

import { EmrEditor } from '../../components/editor/Main';
import {logger} from '../log/Logger';

export default class ExternalEvent {
    private _funs: IExternalEvent;
    private _timeout: any;
    private _timeout2: any;
    private _host: EmrEditor;
    constructor(host: EmrEditor, funs: IExternalEvent) {
        this._host = host;
        this._funs = funs || ({} as any);
    }

    public setEvents(options: IExternalEvent): void {
        const keys = Object.keys(options);
        const funs = this._funs;
        keys.forEach((key) => {
            funs[key] = options[key];
        });
    }

    public removeEvent(sNames?: string[]): boolean {
        if (sNames === undefined) {
            this._funs = {} as any;
            return true;
        }

        if (!sNames || sNames.length === 0) {
            return false;
        }

        const funs = this._funs;
        let flag = false;
        sNames.forEach((name) => {
            if (name in funs) {
                delete funs[name];
                flag = true;
            }
        });

        return flag;
    }

    public nsoKeyPressedEvent(e: any): boolean {
        if (typeof this._funs.nsoKeyPressedEvent === 'function') {
            if (this._host.isKeyEnabled() !== true) {
                return undefined;
            }
            const date = new Date();
            const res =  this._funs.nsoKeyPressedEvent(e.keyCode, e.ctrlKey, e.shiftKey, e.altKey);
            logger.interface({id: this._host.docId, name: 'nsoKeyPressedEvent', startTime: date, result: res});
            return res;
        }
    }

    public nsoFileOpenCompleted(sPath: string): void {
        if (typeof this._funs.nsoFileOpenCompleted === 'function') {
            if (this._host.isFileEnabled() !== true) {
                return;
            }
            const date = new Date();
            this._funs.nsoFileOpenCompleted(sPath);
            this._host.removeAllListen();
            logger.interface({id: this._host.docId, name: 'nsoFileOpenCompleted', args: {sPath},
                startTime: date, result: undefined});
        }
    }

    public nisCellClickEvent(rowId: string, cellId: string, type: number, position: IEventPosition): void {
        if (typeof this._funs.nisCellClickEvent === 'function') {
            if (this._host.isStructEnabled() !== true) {
                return;
            }
            const date = new Date();
            this._funs.nisCellClickEvent(rowId, cellId, type, position);
            logger.interface({id: this._host.docId, name: 'nisCellClickEvent', args: {rowId, cellId, type, position},
                startTime: date, result: undefined});
        }
    }

    public nisCellDBClickEvent(rowId: string, cellId: string, type: number, position: IEventPosition): void {
        if (typeof this._funs.nisCellDBClickEvent === 'function') {
            if (this._host.isStructEnabled() !== true) {
                return;
            }
            const date = new Date();
            this._funs.nisCellDBClickEvent(rowId, cellId, type, position);
            logger.interface({id: this._host.docId, name: 'nisCellDBClickEvent', args: {rowId, cellId, type, position},
                startTime: date, result: undefined});
        }
    }

    public nisCellGainFocusEvent(rowId: string, cellId: string, type: number): void {
        if (typeof this._funs.nisCellGainFocusEvent === 'function') {
            if (this._host.isStructEnabled() !== true) {
                return;
            }
            const date = new Date();
            this._funs.nisCellGainFocusEvent(rowId, cellId, type);
            logger.interface({id: this._host.docId, name: 'nisCellGainFocusEvent', args: {rowId, cellId, type},
                startTime: date, result: undefined});
        }
    }

    public nisCellLostFocusEvent(rowId: string, cellId: string, type: number): void {
        if (typeof this._funs.nisCellLostFocusEvent === 'function') {
            if (this._host.isStructEnabled() !== true) {
                return;
            }
            const date = new Date();
            this._funs.nisCellLostFocusEvent(rowId, cellId, type);
            logger.interface({id: this._host.docId, name: 'nisCellLostFocusEvent', args: {rowId, cellId, type},
                startTime: date, result: undefined});
        }
    }

    public nsoStructClick(sName: string, type: number, position: any): void {
        if (typeof this._funs.nsoStructClick === 'function') {
            if (this._host.isStructEnabled() !== true) {
                return;
            }
            const date = new Date();
            this._funs.nsoStructClick(sName, EXTERNAL_STRUCT_TYPE[type], position);
            logger.interface({id: this._host.docId, name: 'nsoStructClick', args: {sName, type, ...position},
                startTime: date, result: undefined});
        }
    }

    public nsoStructDBClick(sName: string, type: number, position: any): void {
        if (typeof this._funs.nsoStructDBClick === 'function') {
            if (this._host.isStructEnabled() !== true) {
                return;
            }
            const date = new Date();
            this._funs.nsoStructDBClick(sName, EXTERNAL_STRUCT_TYPE[type], position);
            logger.interface({id: this._host.docId, name: 'nsoStructDBClick', args: {sName, type, ...position},
                startTime: date, result: undefined});
        }
    }

    /**
     * 编辑器的下拉类型的元素的选择弹框消失后发送事件
     * 只有下拉类型的结构化元素会触发该事件，事件触发是编辑器的选择弹框消失后触发，可以通过事件的返回参数，调用接口获取当前选择的项值。
     */
    public nsoStructItemSelected(sName: string): void {
        if (typeof this._funs.nsoStructItemSelected === 'function') {
            const date = new Date();
            this._funs.nsoStructItemSelected(sName);
            logger.interface({id: this._host.docId, name: 'nsoStructItemSelected', args: {sName},
                startTime: date, result: undefined});
        }
    }

    public nsoSendPrintDataCompleted(type: number): void {
        if (typeof this._funs.nsoSendPrintDataCompleted === 'function') {
            const date = new Date();
            this._funs.nsoSendPrintDataCompleted(type);
            logger.interface({id: this._host.docId, name: 'nsoSendPrintDataCompleted', args: {type},
                startTime: date, result: undefined});
        }
    }

    /** 表格新建行后产生的事件 */
    public nsoRowInsertedInTable(sName: string): void {
        if (typeof this._funs.nsoRowInsertedInTable === 'function') {
            const date = new Date();
            this._funs.nsoRowInsertedInTable(sName);
            logger.interface({id: this._host.docId, name: 'nsoRowInsertedInTable', args: {},
                startTime: date, result: undefined});
        }
    }

    public nsoStructGainFocus(sName: string, type: number): void {
        if (typeof this._funs.nsoStructGainFocus === 'function') {
            if (this._host.isStructEnabled() !== true) {
                return;
            }
            const date = new Date();
            this._funs.nsoStructGainFocus(sName, EXTERNAL_STRUCT_TYPE[type]);
            logger.interface({id: this._host.docId, name: 'nsoStructGainFocus', args: {sName, type},
                startTime: date, result: undefined});
        }
    }

    public nsoStructLostFocus(sName: string, type: number): void {
        if (typeof this._funs.nsoStructLostFocus === 'function') {
            if (this._host.isStructEnabled() !== true) {
                return;
            }
            const date = new Date();
            this._funs.nsoStructLostFocus(sName, EXTERNAL_STRUCT_TYPE[type]);
            logger.interface({id: this._host.docId, name: 'nsoStructLostFocus', args: {sName, type},
                startTime: date, result: undefined});
        }
    }

    public nsoStructChanged(sName: string, type: number): void {
        if (typeof this._funs.nsoStructChanged === 'function') {
            if (this._host.isStructEnabled() !== true) {
                return;
            }
            clearTimeout(this._timeout);
            this._timeout = setTimeout(() => {
                const date = new Date();
                this._funs.nsoStructChanged(sName, EXTERNAL_STRUCT_TYPE[type]);
                logger.interface({id: this._host.docId, name: 'nsoStructChanged', args: {sName, type},
                    startTime: date, result: undefined});
            }, 30);
        }
    }

    public nsoRegionCustomEvent(sRegionName: string): void {
        if (typeof this._funs.nsoRegionCustomEvent === 'function') {
            const date = new Date();
            this._funs.nsoRegionCustomEvent(sRegionName);
            logger.interface({id: this._host.docId, name: 'nsoRegionCustomEvent', args: {sRegionName}, startTime: date, result: undefined});
          }
    }

    public nsoRegionOperate(x: number, y: number): void {
      if (typeof this._funs.nsoRegionOperate === 'function') {
        const date = new Date();
        this._funs.nsoRegionOperate(x, y);
        logger.interface({id: this._host.docId, name: 'nsoRegionOperate', args: {x, y}, startTime: date, result: undefined});
      }
    }

    public nsoRegionGainFocus(sName: string, type: number): void {
        if (typeof this._funs.nsoRegionGainFocus === 'function') {
            // if (this._host.isStructEnabled() !== true) {
            //     return;
            // }
            const date = new Date();
            this._funs.nsoRegionGainFocus(sName, EXTERNAL_STRUCT_TYPE[type]);
            logger.interface({id: this._host.docId, name: 'nsoRegionGainFocus', args: {sName, type},
                startTime: date, result: undefined});
        }
    }

    public nsoRegionLostFocus(sName: string, type: number): void {
        if (typeof this._funs.nsoRegionLostFocus === 'function') {
            // if (this._host.isStructEnabled() !== true) {
            //     return;
            // }
            const date = new Date();
            this._funs.nsoRegionLostFocus(sName, EXTERNAL_STRUCT_TYPE[type]);
            logger.interface({id: this._host.docId, name: 'nsoRegionLostFocus', args: {sName, type},
                startTime: date, result: undefined});
        }
    }

    public nsoRegionDBClick(sName: string, type: number): void {
        if (typeof this._funs.nsoRegionDBClick === 'function') {
            // if (this._host.isStructEnabled() !== true) {
            //     return;
            // }
            const date = new Date();
            this._funs.nsoRegionDBClick(sName, EXTERNAL_STRUCT_TYPE[type]);
            logger.interface({id: this._host.docId, name: 'nsoRegionDBClick', args: {sName, type},
                startTime: date, result: undefined});
        }
    }

    public nsoStructCheckChanged(sName: string, bChecked: boolean): void {
        if (typeof this._funs.nsoStructCheckChanged === 'function') {
            // if (this._host.isStructEnabled() !== true) {
            //     return;
            // }
            const date = new Date();
            this._funs.nsoStructCheckChanged(sName, bChecked);
            logger.interface({id: this._host.docId, name: 'nsoStructCheckChanged', args: {sName, bChecked},
                startTime: date, result: undefined});
        }
    }

    public nsoFileModifyChanged(bModified: boolean): void {
        if (typeof this._funs.nsoFileModifyChanged === 'function') {
            // if (this._host.isStructEnabled() !== true) {
            //     return;
            // }
            const date = new Date();
            this._funs.nsoFileModifyChanged(bModified);
            logger.interface({id: this._host.docId, name: 'nsoFileModifyChanged', args: {bModified},
                startTime: date, result: undefined});
        }
    }

    public nsoRegionChanged(sName: string, type: number): void {
        if (typeof this._funs.nsoRegionChanged === 'function') {
            // if (this._host.isStructEnabled() !== true) {
            //     return;
            // }
            clearTimeout(this._timeout2);
            this._timeout2 = setTimeout(() => {
                const date = new Date();
                this._funs.nsoRegionChanged(sName, EXTERNAL_STRUCT_TYPE[type]);
                logger.interface({id: this._host.docId, name: 'nsoRegionChanged', args: {sName, type},
                    startTime: date, result: undefined});
            }, 30);
        }
    }

    /** 文档滚动到最底部时产生的事件 */
    public nsoScrollReachEnd(): void {
        if (typeof this._funs.nsoScrollReachEnd === 'function') {
            const date = new Date();
            this._funs.nsoScrollReachEnd();
            logger.interface({id: this._host.docId, name: 'nsoScrollReachEnd', args: {},
                startTime: date, result: undefined});
        }
    }

    /** 文档滚动离开最底部时产生的事件 */
    public nsoScrollLeaveEnd(): void {
        if (typeof this._funs.nsoScrollLeaveEnd === 'function') {
            const date = new Date();
            this._funs.nsoScrollLeaveEnd();
            logger.interface({id: this._host.docId, name: 'nsoScrollLeaveEnd', args: {},
                startTime: date, result: undefined});
        }
    }

    /**
     * 表格操作符
     * @param structJson 按照单元格从左到右，返回里面的结构化元素的类型以及name。[{name,type}]
     */
    public nsoRowInsertedByTableAction(structJson: string): void {
        if (typeof this._funs.nsoRowInsertedByTableAction === 'function') {
            const date = new Date();
            this._funs.nsoRowInsertedByTableAction(structJson);
            logger.interface({id: this._host.docId, name: 'nsoRowInsertedByTableAction', args: structJson,
                startTime: date, result: undefined});
        }
    }

    public nsoContextMenuEvent(id: string): void {
        if (typeof this._funs.nsoContextMenuEvent === 'function') {
            const date = new Date();
            this._funs.nsoContextMenuEvent(id);
            logger.interface({id: this._host.docId, name: 'nsoContextMenuEvent', args: id,
                startTime: date, result: undefined});
        }
    }

    /**
     * 选项按钮选择项变更事件
     * @param name 选项元素名称
     * @param checkedIndexes 选中的选项集合
     */
    public nsoRadioButtonCheckChanged(sName: string, checkedIndexes: string): void {
        if (typeof this._funs.nsoRadioButtonCheckChanged === 'function') {
            const date = new Date();
            this._funs.nsoRadioButtonCheckChanged(sName, checkedIndexes);
            logger.interface({id: this._host.docId, name: 'nsoRadioButtonCheckChanged', args: [sName, checkedIndexes],
                startTime: date, result: undefined});
        }
    }


    /**
     * 按钮点击事件
     * @param sName 按钮名称
     * @param option 按钮其他属性
     */
    public nsoButtonClick(sName: string, option?: any): void {
        if (typeof this._funs.nsoButtonClick === 'function') {
            const date = new Date();
            this._funs.nsoButtonClick(sName, option);
            logger.interface({id: this._host.docId, name: 'nsoButtonClick', args: [sName, option],
            startTime: date, result: undefined});
        }
    }

    public nsoCustomToolbarEvent(item: ICustomToolbarItem): void {
        if (typeof this._funs.nsoCustomToolbarEvent === 'function') {
            const date = new Date();
            this._funs.nsoCustomToolbarEvent(item);
            logger.interface({id: this._host.docId, name: 'nsoCustomToolbarEvent', args: item,
                startTime: date, result: undefined});
        }
    }
    
    /**
     * 触发内联模式高度变化事件
     * @param rectInfo 包含高度、宽度和光标位置的信息
     */
    public inlineHeightChange(rectInfo: {height: number, width: number, cursorX?: number, cursorY?: number, isInline?: boolean}): void {
        if (typeof this._funs.inlineHeightChange === 'function') {
            const date = new Date();
            this._funs.inlineHeightChange(rectInfo);
            logger.interface({id: this._host.docId, name: 'inlineHeightChange', args: rectInfo,
                startTime: date, result: undefined});
        }
    }

    // 添加  的公共方法
    public nsoPrinterDataEvent(htmlContent: string): void {
        if (typeof this._funs.nsoPrinterDataEvent === 'function') {
            const date = new Date();
            this._funs.nsoPrinterDataEvent(htmlContent);
            // 可以选择性地添加日志记录
            logger.interface({id: this._host.docId, name: 'nsoPrinterDataEvent', args: {'htmlContentLength': htmlContent.length}, startTime: date, result: undefined});
        }
    }

}
