import Document from './Document';
import { NewControl, findLowerBound } from './NewControl/NewControl';
import DocumentContentElementBase from './DocumentContentElementBase';
import { INewControlProperty, NewControlType, ResultType, ICustomProps,
    NewControlContentSecretType, filterChars, NewControlDefaultSetting,
    CascadeActionType, CascadeTriggerCondition, ICascade, SignatureType, IStructParamJson,
    CascadeTriggerType, ICascadeEvent, EventType, ITriggerEventInfo, CodeAndValue, FileSource, MixFormulaParser,
    IExternalDataProperty, 
    MonitorAction} from '../../common/commonDefines';
import { ParagraphContentPos } from './Paragraph/ParagraphContent';
import DocumentContentBase from './DocumentContentBase';
import { NewComboBox } from './NewControl/NewComboBox';
import { NewControlNumer } from './NewControl/NewControlNum';
import { NewControlText, NewControlSection } from './NewControl/NewControlText';
import { NewControlDate } from './NewControl/NewControlDate';
import { ChangeNewControlRemoveItem, ChangeNewControlAddItem,
    ChangeNewControlMapAddItem,
    ChangeNewControlRemoveLeafItems2,
    ChangeNewControlAddItems,
    ChangeNewControlMapAddItems} from './NewControl/NewControlChange';
// import { HistroyItemType } from './HistoryDescription';
import { NewControlCheck } from './NewControl/NewControlCheck';
import { NewControlRadio } from './NewControl/NewControlRadio';
import { message } from '../../common/Message';
import History from './History';
import { IFRAME_MANAGER } from '../../common/IframeManager';
import { NewControlSignature } from './NewControl/NewControlSignature';
import { DocumentContentType } from './Style';
import {GlobalEvent as gEvent, GlobalEventName as gEventName} from '../../common/GlobalEvent';
import ContentChanges, { ContentChangesElement } from './ContentChanges';
import { NewControlAddress } from './NewControl/NewControlAddress';
import { CascadeManager } from './NewControl/CascadeManager';
import Paragraph from './Paragraph';
import { ButtonManager } from './Paragraph/ParaButtonManager';
import { getCurTime } from '@/common/commonMethods';
// import {parse as circularParse, stringify as circularStringify} from 'flatted/esm';

interface IPasteNewControl {
    control: NewControl;
    contentPos: ParagraphContentPos;
    parentName?: string;
}

interface ICascadeMap {
    text: string;
    newControl: NewControl;
}

export class NewControlManager {
    private parent: Document;
    private newControls: NewControl[]; // record the tree structure. specify struct order, if not right would not crash
    private newControlNames: Map<string, NewControl>; // usually use this to retrieve struct, highly related with crash
    private pasteNewControls: IPasteNewControl[];
    private pastelNames: Map<string, NewControl>;
    private cascade: any; // {string: boolean}
    private _timeout: any;
    private _triggerCascades: any;
    private contentChanges: ContentChanges;
    private cascadesMap: Map<string, ICascadeMap>;
    // private cascadeRegionMap: Map<string, any[]>;
    private cascadeEvents: any;
    private cascadeManager: CascadeManager;
    private triggerTime: number;
    private buttonManager: ButtonManager;
    // private sourceBindMap: Map<string, any[]>;
    private currentRegionContext?: string;

    constructor(document: Document) {
        this.parent = document;
        this.newControls = [];
        this.newControlNames = new Map();
        this.pasteNewControls = [];
        this.cascade = {};
        this.cascadeEvents = {};
        this.contentChanges = new ContentChanges();
        this.cascadeManager = new CascadeManager(this);
        this.pastelNames = new Map();
    }

    public setRegionContext(regionName: string): void {
        this.currentRegionContext = regionName;
    }
    
    public clearRegionContext(): void {
        this.currentRegionContext = undefined;
    }
    

    public setNewControlName(newControl: NewControl, name: string): boolean {
        if ( '' !== name && true === this.checkNewControlName(name) ) {
            newControl.setNewControlName(name);
            return true;
        }

        return false;
    }

    public clear(): void {
        this.newControls = [];
        this.newControlNames.clear();
        this.cascadeManager.clear();
    }

    public getButtonManager(): ButtonManager {
        if (!this.buttonManager) {
            this.buttonManager = new ButtonManager(this.parent, {datas: this.newControlNames, caches: this.pastelNames});
        }

        return this.buttonManager;
    }

    public getCascadeManager(): CascadeManager {
        return this.cascadeManager;
    }

    public copyNewControl(name: string, para?: Paragraph): NewControl {
        const oldNewControl = this.newControlNames.get(name);
        if (!oldNewControl) {
            return;
        }
        if (!para) {
            para = new Paragraph(this.parent, this.parent);
        }
        const props = this.parent.getCustomForNewControl(oldNewControl);
        props.newControlName = this.makeUniqueName(props.newControlType);
        const bPlaceholder = oldNewControl.isPlaceHolderContent();
        const newControl = this.createNewControl(para, props);
        if (bPlaceholder !== true) {
            newControl.setPlaceHolder(false);
        }
        return newControl;
    }

    public makeUniqueName(type: number, name?: string, source?: FileSource): string {
        if (null != name) {
            // 📍 新增：区域特殊处理逻辑
            if (this.currentRegionContext && source === FileSource.InsertFile) {
                if (true === this.checkNewControlName(name)) {
                    return name;  // 名称可用，直接返回
                }
                
                // 拼接区域名称
                const regionPrefixedName = `${this.currentRegionContext}_${name}`;
                
                // 检查拼接后的名称是否可用
                if (true === this.checkNewControlName(regionPrefixedName)) {
                    return regionPrefixedName;
                }
                
                // 如果还是重复，末尾加随机数字
                let finalName = regionPrefixedName;
                let randomSuffix = Math.floor(Math.random() * 10000); // 生成4位随机数
                
                do {
                    finalName = `${regionPrefixedName}_${randomSuffix}`;
                    randomSuffix = Math.floor(Math.random() * 10000); // 重新生成随机数
                } while (
                    this.newControlNames.has(finalName) ||
                    this.pasteNewControls.find((item) =>
                        (item && item.control.getNewControlName() === finalName))
                );
                
                return finalName;
            }
            
            if (true === this.checkNewControlName(name)) {
                return name;
            } else if (FileSource.Copy === source || FileSource.InsertFile === source) {
                do {
                    name += '1';
                } while (this.newControlNames.has(name) ||
                      this.pasteNewControls.find((item) =>
                          (item && item.control.getNewControlName() === name)));
                return name;
            }
        }
    
        let newControlName = NEW_CONTROL_NAME.get(type);
        const pasteNewControls = this.pasteNewControls;
        for (let number = 1; number < Number.MAX_SAFE_INTEGER; number++) {
            const temp = newControlName + number;
            if (false === this.newControlNames.has(temp)) {
                if (pasteNewControls.find((control) => control.control.getNewControlName() === temp)) {
                    continue;
                }
                newControlName = temp;
                break;
            }
        }
    
        return newControlName;
    }

    public makeUniqueNameByInsertFile(type: number, newControlNamesArr: string[], name?: string): string {
        if ( null != name ) {
            if (true === this.checkNewControlName(name)) {
                return name;
            }
        }

        let newControlName = NEW_CONTROL_NAME.get(type);
        const pasteNewControls = this.pasteNewControls;
        for (let number = 1; number < Number.MAX_SAFE_INTEGER; number++) {
            const temp = newControlName + number;
            if ( !newControlNamesArr.includes(temp) ) {
                if (pasteNewControls.length !== 0 && pasteNewControls
                    .find((control) => control.control.getNewControlName() === temp)) {
                    continue;
                }
                newControlName = temp;
                break;
            }
        }

        // console.log(newControlName)

        return newControlName;
    }

    public isContain( newControlName: string ): boolean {
        if ( true === this.newControlNames.has(newControlName) ) {
            return true;
        }

        return false;
    }

    public addCustomProps(name: string, props: ICustomProps[]): boolean {
        const newControl = this.newControlNames.get(name);
        if (!newControl) {
            return false;
        }
        newControl.addCustomProps(props);

        return true;
    }

    public getCustomPropsByName(name: string): ICustomProps[] {
        const newControl = this.newControlNames.get(name);
        if (!newControl) {
            return [];
        }
        const props = newControl.getCustomProps();
        return props;
    }

    public setTriggerTime(time: number): void {
        this.triggerTime = time;
    }

    public updateAllCascades(): number {
        this.clearAllNewcontrolCascade();
        const newControls = this.newControls;
        if (newControls.length === 0) {
            return ResultType.UnEdited;
        }

        this.triggerTime = 3;
        this.forEachNewControl(newControls);

        const res = this.triggerCascadeMap();
        this.triggerTime = undefined;
        this.cascadesMap = null;
        return res;
    }

    public forEachNewControl(newControls: NewControl[]): void {
        for (const newControl of newControls) {
            this.addCascadeMap(newControl, null);
            const leafs = newControl.getLeafList();
            if (leafs.length) {
                this.forEachNewControl(leafs);
            }
        }
    }

    /**
     * 插入时，检查名称的合法性
     * @param newControlName
     */
    public checkNewControlName(newControlName: string): boolean {
        if (this.newControlNames.has(newControlName) || this.pastelNames.has(newControlName) || this.buttonManager?.checkButtonName(newControlName) === false) {
            return false;
        }

        return true;
    }

    public checkNewControlIsExist(newControlName: string): boolean {
        // 主要判断：检查是否在名称映射中存在且有效
        if (this.newControlNames.has(newControlName)) {
            const existingControl = this.newControlNames.get(newControlName);
            if (existingControl) {
                const startPos = existingControl.getStartPos();
                const endPos = existingControl.getEndPos();
                // 如果位置为null，说明已被剪切，视为不存在
                if (startPos === null || endPos === null) {
                    return false;  // 已被剪切，不存在
                }
                return true;  // 存在且有效
            }
        }
        
        // 检查按钮管理器中是否存在（这个是独立的存在性检查）
        if (this.buttonManager && this.buttonManager.checkButtonName(newControlName) === false) {
            return true;  // 在按钮中存在
        }
    
        return false;  // 不存在
    }

    /**
     * 跳转到下一个可跳转的结构化元素
     * @param newControl 当前结构化元素
     */
    public jumpToNextNewControl(newControl: NewControl): NewControl {
        if (!newControl) {
            return null;
        }
        if (!newControl.isTabJump()) {
            return null;
        }

        return this.getNextTabJumpNewControl(newControl);
    }

    /**
     * 判断当前位置插入newcontrol是否合法
     * @param pos 插入位置信息
     * @param type 插入newcontrol的类型
     * @param depth
     */
    public isValidAddNewControl(startPos: ParagraphContentPos, endPos: ParagraphContentPos,
                                type: number, depth: number = 1): { bValid: boolean, parentNewControl: NewControl } {
        let result = true;

        if (this.parent != null && this.parent.isInNISTable()) {
            result = false;
        }
        const bSelected = ( startPos === endPos ? false : true );

        const endIndex = findLowerBound(this.newControls, endPos); // element.getNewControlLeftBorderPos();

        if ( !bSelected || (NewControlType.TextBox !== type && NewControlType.Section !== type) ) {
            if ( -1 < endIndex ) {
                const newControl = this.newControls[endIndex];
                if ( newControl ) {
                    result = newControl.isValidAddNewControl(endPos, type, depth);
                }

                return { bValid: result, parentNewControl: newControl.getPosNewControl(endPos)};
            }
        } else if ( bSelected ) {
            const startIndex = findLowerBound(this.newControls, startPos);
            if ( -1 === startIndex && startIndex === endIndex ) {
                return { bValid: true, parentNewControl: null};
            }

            if ( NewControlType.TextBox === type ) {
                if ( startIndex !== endIndex ) {
                    return { bValid: false, parentNewControl: null};
                } else {
                    const newControl = this.newControls[endIndex];
                    if ( newControl.isInsideNewControl(startPos, endPos) ) {
                        if ( newControl && newControl.isNewSection() ) {
                            const startNewControl = newControl.getPosNewControl(startPos);
                            const endNewControl = newControl.getPosNewControl(endPos);

                            if ( startNewControl !== endNewControl
                                || ( startNewControl && !startNewControl.isNewSection())
                                || ( endNewControl && !endNewControl.isNewSection()) ) {
                                return { bValid: false, parentNewControl: null};
                            }

                            const containNewControls = newControl.getLeafListNamesByPos(startPos, endPos, true);

                            if ( containNewControls && 0 < containNewControls.length ) {
                                return { bValid: false, parentNewControl: null};
                            }

                            return { bValid: true, parentNewControl: startNewControl};
                        } else {
                            return { bValid: false, parentNewControl: null};
                        }
                    } else if ( this.newControls[startIndex] ) {
                        const endNewControlPos = this.newControls[startIndex].getEndPos();

                        if ( -1 === startPos.compare(endNewControlPos)
                            && 1 === endPos.compare(endNewControlPos) ) {
                            return { bValid: false, parentNewControl: null};
                        }
                    }

                    return { bValid: true, parentNewControl: null};
                }
            } else if ( NewControlType.Section === type ) {
                if ( startIndex !== endIndex ) {
                    const startNewControl = this.newControls[startIndex];
                    const endNewControl = this.newControls[endIndex];
                    if ( (startNewControl && startNewControl.getPosNewControl(startPos)) ||
                        (endNewControl && endNewControl.getPosNewControl(endPos)) ) {
                        result = false;
                    }

                    return { bValid: result, parentNewControl: null};
                } else {
                    const newControl = this.newControls[endIndex];
                    const startNewControl = newControl.getPosNewControl(startPos);
                    const endNewControl = newControl.getPosNewControl(endPos);

                    if ( startNewControl !== endNewControl || ( startNewControl && !startNewControl.isNewSection())
                        || ( endNewControl && !endNewControl.isNewSection())) {
                        result = false;
                    }

                    return { bValid: result, parentNewControl: startNewControl};
                }
            }
        }

        return { bValid: result, parentNewControl: null};
    }

    public getUnValidNumberBox(flagType?: number): string[] {
        const names: string[] = [];

        const newControlNames = this.newControlNames;
        for (const [name, newControl] of newControlNames) {
            if (!newControl.isNumberBox()) {
                continue;
            }
            if (flagType === 1 && newControl.getForceValidate() !== true) {
                continue;
            }
            const item: NewControlNumer = newControl as any;
            const errorType = item.getValidNumberType();
            if (errorType === ResultType.Success) {
                continue;
            }
            names.push(name);
        }

        return names;
    }

    /**
     * 根据pos插入newControl
     * @param newControl
     * @param pos 插入位置信息
     */
    public addNewControl( newControl: NewControl, startPos: ParagraphContentPos, endPos?: ParagraphContentPos): void {
        if ( null == newControl ) {
            return ;
        }

        const name = newControl.getNewControlName();
        if (true === this.newControlNames.has(name ) ) {
            return ;
        }

        // find where to insert the new content control in the array
        let startIndex = findLowerBound(this.newControls, startPos);
        const bSelected = !(null == endPos || startPos === endPos);
        // console.log("insert pos: ", startIndex + 1);

        const history = this.getHistory();

        if ( !bSelected || (newControl && newControl.isNewTextBox()) ) {
            if ( -1 === startIndex ) {
                this.newControls.splice(startIndex + 1, 0, newControl);
                if ( history ) {
                    history.addChange(new ChangeNewControlAddItem(newControl, startIndex + 1, null));
                }
            } else {
                const parentControl = this.newControls[startIndex];

                if ( true === parentControl.isCursorInNewControl(startPos) ) {
                    parentControl.addLeaf(newControl, startPos);
                } else {
                    this.newControls.splice(startIndex + 1, 0, newControl);
                    if ( history ) {
                        history.addChange(new ChangeNewControlAddItem(newControl, startIndex + 1, null));
                    }
                }
            }
        } else if ( newControl && newControl.isNewSection() ) {
            let endIndex = findLowerBound(this.newControls, endPos);
            const startNewControl = this.newControls[startIndex];
            const endNewControl = this.newControls[endIndex];
            const parentControl = (startNewControl && startNewControl === endNewControl) ?
                                startNewControl.getPosNewControl(startPos) : null;
            const containNewControls = parentControl ?
                                parentControl.getNewControlNamesByPos(startPos, endPos, false, false) :
                                this.getNewControlNamesByPos(startPos, endPos, false, false);

            if ( containNewControls && 0 < containNewControls.length ) {
                containNewControls.forEach((item, index) => {
                    const leafControl = this.getNewControlByName(item);
                    if ( leafControl ) {
                        newControl.addLeaf1(leafControl, undefined, false);

                        if ( parentControl ) {
                            const leafList = parentControl.getLeafList();
                            if ( 0 === index ) {
                                leafList.find((item2, index2) => {
                                    if ( leafControl === item2) {
                                        startIndex = index2;
                                        return ;
                                    }
                                });
                            }
                            if ( containNewControls.length - 1 === index ) {
                                leafList.find((item2, index2) => {
                                    if ( leafControl === item2) {
                                        endIndex = index2;
                                        return ;
                                    }
                                });
                            }
                        }
                    }
                });

                if ( parentControl) {
                    const leafList = parentControl.getLeafList();
                    const partList = leafList.slice(startIndex, endIndex + 1);
                    if ( history ) {
                        history.addChange(new ChangeNewControlRemoveLeafItems2(leafList, startIndex, partList));
                    }
                    leafList.splice(startIndex, endIndex - startIndex + 1);
                } else {
                    const partList = this.newControls.slice(startIndex + 1, endIndex + 1);
                    if ( history ) {
                        history.addChange(new ChangeNewControlRemoveLeafItems2(
                            this.newControls, startIndex + 1, partList));
                    }
                    this.newControls.splice(startIndex + 1, endIndex - startIndex);
                }
            }

            if ( parentControl) {
                parentControl.addLeaf1(newControl, startIndex - 1);
            } else {
                this.newControls.splice(startIndex + 1, 0, newControl);
                if ( history ) {
                    history.addChange(new ChangeNewControlAddItem(newControl, startIndex + 1, null));
                }
            }
        }

        this.newControlNames.set(name, newControl);

        if (newControl.getType() === NewControlType.SignatureBox) {
            const leafList = newControl.getLeafList();
            // console.log(leafList)
            // 1 shallow depth list
            for (const leaf of leafList) {
                // console.log(leaf.getNewControlName())
                this.newControlNames.set(leaf.getNewControlName(), leaf);
            }
        }
    }

    public addNewControl1( newControl: NewControl): void {
        const name = newControl.getNewControlName();
        if (true === this.newControlNames.has(name ) ) {
            return ;
        }

        const history = this.getHistory();
        const startIndex = this.newControls.length;
        this.newControls.push(newControl);
        this.newControlNames.set(name, newControl);
        if ( history ) {
            history.addChange(new ChangeNewControlAddItem(newControl, startIndex, null));
        }
    }

    public getUnCopyNames(para: DocumentContentElementBase): string[] {
        const res = [];
        const newControls = this.newControlNames;
        for (const [name, newControl] of newControls) {
            if (!newControl.isSignatureBox() || !newControl.isCopyProtect()) {
                continue;
            }
            if (newControl.getParagraph() === para) {
                res.push(newControl.getNewControlName());
            }
        }
        return res;
    }

    /**
     * 生成新元素
     * @param parent 插入所在段落
     * @param property 元素属性
     * @param sText 元素内容
     */
    public createNewControl( parent: DocumentContentElementBase, property: INewControlProperty,
                             sText?: string ): NewControl {
        if ( null == property.newControlName || false === this.checkNewControlName(property.newControlName) ) {
            // insert file name dup is allowed
            if ( property.bInsertFile !== true) {
                return null;
            }
        }

        let newControl;
        const source = property.bInsertFile ? FileSource.InsertFile : null;

        switch ( property.newControlType ) {
            case NewControlType.TextBox:
                newControl = new NewControlText(parent.parent,
                    this.makeUniqueName(property.newControlType, property.newControlName, source),
                    property, sText);
                break;

            case NewControlType.Section:
                newControl = new NewControlSection(parent.parent,
                    this.makeUniqueName(property.newControlType, property.newControlName, source),
                    property, sText);
                break;
            case NewControlType.NumberBox:
                newControl = new NewControlNumer(parent.parent,
                    this.makeUniqueName(property.newControlType, property.newControlName, source),
                    property, sText);
                break;
            case NewControlType.Combox:
            case NewControlType.MultiCombox:
            case NewControlType.ListBox:
            case NewControlType.MultiListBox:
                newControl = new NewComboBox(parent.parent,
                    this.makeUniqueName(property.newControlType, property.newControlName, source),
                    property, sText);
                break;

            case NewControlType.DateTimeBox:
                newControl = new NewControlDate(parent.parent,
                    this.makeUniqueName(property.newControlType, property.newControlName, source),
                    property, sText);
                break;
            case NewControlType.CheckBox:
                newControl = new NewControlCheck(parent.parent,
                    this.makeUniqueName(property.newControlType, property.newControlName, source),
                    property, sText);
                break;
            case NewControlType.MultiRadio:
            case NewControlType.RadioButton:
                newControl = new NewControlRadio(parent.parent,
                    this.makeUniqueName(property.newControlType, property.newControlName, source),
                    property, sText);
                break;
            case NewControlType.SignatureBox: {
                newControl = new NewControlSignature(parent.parent,
                    this.makeUniqueName(property.newControlType, property.newControlName, source),
                    property, sText);
                if (property.bReadSign == null) {
                    const textProperty: INewControlProperty = {
                        newControlName: undefined,
                        newControlSerialNumber: undefined,
                        newControlInfo: undefined,
                        newControlPlaceHolder: NewControlDefaultSetting.DefaultPlaceHolderContent,
                        newControlTitle: undefined,
                        newControlType: NewControlType.TextBox,
                        isNewControlHidden: false,
                        isNewControlCanntDelete: true,
                        isNewControlCanntEdit: true,
                        isNewControlMustInput: false,
                        isNewControlShowBorder: property.showSignBorder,
                        isNewControlReverseEdit: false,
                        isNewControlHiddenBackground: true,
                        newControlDisplayType: NewControlContentSecretType.DontSecret,
                        newControlMaxLength: undefined,
                        newControlFixedLength: undefined,
                        customProperty: undefined,
                        tabJump: false,
                    };
                    // console.log(newControl)
                    // console.log(circularParse(circularStringify(newControl)));
                    const textControlCount = property.signatureCount; // 1 - 3
                    textProperty.newControlPlaceHolder = property.signaturePlaceholder;
                    let textUniqueName = this.makeUniqueName(NewControlType.SignatureElement,
                                            textProperty.newControlName, source);
                    let newControlText = new NewControlText(parent.parent, textUniqueName, textProperty, sText);
                    const textNameLen = NEW_CONTROL_NAME.get(NewControlType.SignatureElement).length;
                    const signType = newControl.getSignType();

                    for (let i = 0; i < textControlCount; i++) {

                        if (i !== 0) {
                            textUniqueName = NEW_CONTROL_NAME.get(NewControlType.SignatureElement) +
                                (+textUniqueName.slice(textNameLen) + 1) ;

                            const loopSafeCheck = 100;
                            let count = 1;
                            while (this.newControlNames.has(textUniqueName)) {
                                textUniqueName = NEW_CONTROL_NAME.get(NewControlType.SignatureElement) +
                                    (+textUniqueName.slice(textNameLen) + 1) ;
                                count++;
                                if (count > loopSafeCheck) {
                                    // tslint:disable-next-line: no-console
                                    console.warn('max loop time reached in newcontrolmanager');
                                    break;
                                }
                            }
                            // console.log(textUniqueName)
                            newControlText = new NewControlText(parent.parent, textUniqueName, textProperty, sText);
                        }

                        // add to leafList. two-way
                        newControlText.setParent(newControl);
                        if ( SignatureType.Common === signType ) {
                            newControl.getLeafList()
                                .push(newControlText);
                        } else {
                            newControl.getLeafList()
                                .splice(0, 0, newControlText);
                        }
                    }
                }

                break;
            }
            case NewControlType.AddressBox: {
                newControl = new NewControlAddress(parent.parent,
                    this.makeUniqueName(property.newControlType, property.newControlName, source),
                    property, sText);
                break;
            }

            default:
                break;
        }

        if (property.customProperty) {
            newControl.addCustomProps(property.customProperty);
        }

        if (property.externalDataBind) {
            this.addSourceData(newControl.getNewControlName(), property.externalDataBind);
        }

        return newControl;
    }

    /**
     * 存储结构化元素
     * @param pasteNode 结构化元素， 位置
     */
    public addPasteControl(pasteNode: IPasteNewControl, bCopy: boolean = false): void {
        if (bCopy === true) {
            if (pasteNode != null) {
                const newControl = pasteNode.control;
                if (newControl.isSignatureBox()) {
                    newControl['leafList'] = [];
                }
            }
        }
        const newControl = pasteNode.control;
        this.pastelNames.set(newControl.getNewControlName(), newControl);
        this.pasteNewControls.push(pasteNode);
    }

    public addPasteControls(pasteNodes: IPasteNewControl[]): void {
        pasteNodes.forEach((node) => {
            this.addPasteControl(node);
        });
        // this.pasteNewControls = this.pasteNewControls.concat(pasteNodes);
    }

    public sortPastControl(): IPasteNewControl[] {
        const pastControls = this.pasteNewControls;
        const objs = {};
        let index = 0;
        for (const len = pastControls.length; index < len; index++) {
            const paste = pastControls[index];
            const parentName = paste.parentName;
            if (!parentName) {
                continue;
            }

            if (objs[parentName]) {
                continue;
            }
            for (let parentIndex = 0; parentIndex < len; parentIndex++) {
                const parent = pastControls[parentIndex];
                if (parent.control.getNewControlName() === parentName) {
                    pastControls.splice(parentIndex, 1);
                    pastControls.splice(index, 0, parent);
                    parent.control['leafList'] = [];
                    index = index + 1;
                    break;
                }
            }
            objs[parentName] = true;
        }

        return this.pasteNewControls.splice(0);
    }

    public getPasteNewControlByName(name: string): NewControl {
        if (!name || this.pasteNewControls.length === 0) {
            return null;
        }
        const ctr = this.pasteNewControls.find((ctrol) => ctrol.control.getNewControlName() === name);
        if (!ctr) {
            return null;
        }
        return ctr.control;
    }

    public clearPasteControls(): void {
        this.pastelNames.clear();
        this.pasteNewControls = [];
    }

    public removeInsertParentName(): void {
        const newControls = this.pasteNewControls;
        newControls.forEach((newControl) => {
            newControl.parentName = undefined;
        });
    }

    public getPasteNewControlsByParent(parent: DocumentContentElementBase, newControls: IPasteNewControl[],
                                       doc: DocumentContentBase): IPasteNewControl[] {
        if (!newControls || newControls.length === 0) {
            return;
        }
        const type = parent.getType();
        // let b
        const result: IPasteNewControl[] = [];
        // let index = 0;
        for (let index = newControls.length - 1; index >= 0; index--) {
            const control = newControls[index];
            let currentParent: any = control.control.getParagraph();
            // if (type === DocumentContentType.Paragraph) {

            // } else {
            //     = control.control.getParagraphParent();
            // }
            switch (type) {
                case DocumentContentType.Paragraph: {
                    break;
                }
                case DocumentContentType.Region: {
                    currentParent = currentParent.getParent()
                        .getParent();
                    break;
                }
                case DocumentContentType.Table: {
                    currentParent = currentParent.getParent().parent.row.table;
                    break;
                }
                default: {
                    continue;
                }
            }
            if (currentParent === parent) {
                result.unshift(control);
                newControls.splice(index, 1);
                control.control.setDocumentParent(doc);
            }
        }
        return result;
    }

    public setParentByNames(names: string[], parent: DocumentContentBase, history?: History): void {
        const newControls = this.newControlNames;
        names.forEach((name) => {
            const newControl = newControls.get(name);
            if (newControl) {
                const portion = newControl.getStartBorderPortion();
                const actParent = newControl.getDocumentParent();
                if (portion.paragraph.parent !== actParent) {
                    newControl.setContentParent(parent, history);
                }
            }
        });
    }

    /**
     * 数据粘贴完，排版完再对存储的结构化元素进行插入
     */
    public insertAllPasteNewControl(parent: DocumentContentBase, pos: ParagraphContentPos,
                                    bInsertFile: boolean = false, pastes?: IPasteNewControl[]): void {
        const pasteNewControls = pastes || this.pasteNewControls;
        if (pasteNewControls.length === 0) {
            return;
        }
        const firstControl = pasteNewControls[0];
        const parentName = firstControl.parentName;
        let parentNewControl: NewControl;
        let startIndex: number;
        if (parentName) {
            parentNewControl = this.newControlNames.get(parentName);
            if (!parentNewControl) {
                return;
            }
        } else {
            parentNewControl = this.getPosNewControl(pos);
            if (!parentNewControl) {
                startIndex = findLowerBound(this.newControls, pos);
            }
        }

        const newControls = this.adjustPasteNewControl(parent, pasteNewControls, bInsertFile);
        let changed: boolean = false;
        const eventInit = new Map();
        newControls.forEach((newControl) => {
            if (parentNewControl) {
                if (startIndex === undefined) {
                    parentNewControl.addLeaf(newControl, pos);
                    startIndex = parentNewControl.getLeafList()
                        .findIndex((control) => control === newControl);
                } else {
                    parentNewControl.addLeaf1(newControl, startIndex++);
                }
                // 检查控件名称是否已存在，如果存在则重命名
                //const originalName = newControl.getNewControlName();
                const bExist = this.checkNewControlIsExist(newControl.getNewControlName());
                if(bExist){
                    const originalName = newControl.getNewControlName();
                    const uniqueName = this.makeUniqueName(newControl.getType(), originalName,FileSource.Copy);
                    if (originalName !== uniqueName) {
                        newControl.setNewControlName(uniqueName);
                    }
                    this.newControlNames.set(uniqueName, newControl);
                }
                //this.newControlNames.set(newControl.getNewControlName(), newControl);
                this.addPasteChild(parentNewControl.getLeafList(), newControl);
            } else {
                this.addPasteNewControl(newControl, startIndex++);
            }
            const displayType = newControl.getViewSecretType();
            if (displayType !== undefined && displayType !== NewControlContentSecretType.DontSecret) {
                newControl['viewSecretType'] = NewControlContentSecretType.DontSecret;
                newControl.setViewSecret(displayType);
            }

            // if (newControl.isSignatureBox()) {
            //     changed = newControl.setAlwaysShow2();
            // }

            changed = newControl.initPasteContent() || changed;
            changed = this.setNewControlElementHide(newControl) || changed;
            const fixedLength = newControl.getFixedLength();
            if (typeof fixedLength === 'number' && fixedLength > 0) {
                changed = true;
            }
            const eventInfo = newControl.getEvent();
            if (eventInfo) {
                newControl.setEvent(null);
            }
        });

        if (!pastes) {
            this.clearPasteControls();
        }

        if (changed) {
            this.parent.recalculate();
            this.parent.updateCursorXY();
        }
    }

    public getNewControlByName( newControlName: string ): NewControl {
        return this.newControlNames.get(newControlName);
    }

    public initOpenDocument(): void {
        const newControlNames = this.newControlNames;
        for (const [newControlName, newControl] of newControlNames) {
            const eventInfo = newControl.getEvent();
            if (eventInfo) {
                this.setEventInfo(newControl, eventInfo);
            }
        }
    }

    /**
     * 根据id获取当前元素的索引
     * @param id 结构化元素Id
     */
    public getNewControlIndexById( id: number ): number {
        return this.newControls.findIndex( (control) => control.getId() === id);
    }

    public getAllNewControls(): NewControl[] {
        return this.newControls;
    }

    public getAllNewControlsName( type?: NewControlType ): string[] {
        let names: string[] = [];

        if ( null == type ) {
            names = Array.from(this.newControlNames.keys());
        } else {
            for (const newControl of this.newControls) {
                if ( type === newControl.getType() ) {
                    names.push(newControl.getNewControlName());
                }
            }
        }

        return names;
    }

    public getInsertNewControlCaches(): IPasteNewControl[] {
        return this.pasteNewControls;
    }

    public resetInsertNewControlCaches(): void {
        this.pasteNewControls = [];
        this.pastelNames.clear();
    }

    /**
     * 插入文件中的所有NewControl
     * @param parent
     * @param pos
     * @returns
     */
    public insertFileNewControlCaches(
        parent: DocumentContentBase, pos: ParagraphContentPos
    ): void {
        const pasteNewControls = this.pasteNewControls;
        if (pasteNewControls.length === 0) {
            return;
        }

        const firstControl = pasteNewControls[0];
        const parentName = firstControl.parentName;
        let parentNewControl: NewControl;
        let startIndex: number;
        if (parentName) {
            parentNewControl = this.newControlNames.get(parentName);
            if (!parentNewControl) {
                return;
            }
        } else {
            parentNewControl = this.getPosNewControl(pos);
            if (!parentNewControl) {
                startIndex = findLowerBound(this.newControls, pos);
            }
            
        }

        const newControls = this.adjustPasteNewControl(parent, pasteNewControls, true);

        if ( parentNewControl ) {

            newControls.forEach((newControl) => {
                if (newControl.getParent()) {
                    return;
                }
                if (parentNewControl) {
                    if (startIndex === undefined) {
                        parentNewControl.addLeaf(newControl, pos);
                        startIndex = parentNewControl.getLeafList()
                            .findIndex((control) => control === newControl);
                    } else {
                        parentNewControl.addLeaf1(newControl, startIndex++);
                    }
                    this.newControlNames.set(newControl.getNewControlName(), newControl);
                    this.addPasteChild(newControl.getLeafList(), newControl);
                }
                // else {
                //     this.addPasteNewControl(newControl, startIndex++);
                // }
                // this.addPasteNewControl(newControl, startIndex++);
                const displayType = newControl.getViewSecretType();
                if (displayType !== undefined && displayType !== NewControlContentSecretType.DontSecret) {
                    newControl['viewSecretType'] = NewControlContentSecretType.DontSecret;
                    newControl.setViewSecret(displayType);
                }
                const cascades = newControl.getCascades1();
                if (cascades) {
                    newControl.setCascades(cascades);
                    newControl.setCascades1(null);
                }
                // if (newControl.isSignatureBox()) {
                //     newControl.setAlwaysShow2();
                // }
            });
        } else {
            // if ( -1 < startIndex ) {
            //     const newControl = this.newControls[startIndex];
            //     if ( newControl && 1 === pos.compare(newControl.getEndPos()) ) {
            //         startIndex++;
            //     }
            // } else {
            //     startIndex = 0;
            // }
            let index = startIndex;
            startIndex = -1 < startIndex ? startIndex + 1 : 0;
            // this.newControls.splice(startIndex, 0, ...newControls);
            newControls.forEach((newControl) => {
                if (!newControl.getParent()) {
                    this.addPasteNewControl(newControl, index++);
                }
                if (newControl.isHidden()) {
                    newControl.getOperateContent().setHidden(true);
                }
            });

            const history = this.getHistory();
            if ( history ) {
                history.addChange(new ChangeNewControlAddItems(this.newControls, startIndex, newControls));
            }
        }

        this.clearPasteControls();
    }

    public insertFileNewControlNames(names: Map<string, NewControl>): void {
        const newControls = [];
        if ( names && 0 < names.size ) {
            names.forEach((newControl, name) => {
                if ( newControl && !newControl.isHidden() ) {
                    // this.newControlNames.set(name, value);
                    // this.addNewControlInMap(value);
                    newControls.push(newControl);
                    if ( newControl instanceof NewControlCheck || newControl instanceof NewControlRadio ) {
                        newControl.addContent();
                    }
                }
            });
        }

        if ( 0 < newControls.length ) {
            newControls.forEach((newControl) => {
                this.newControlNames.set(newControl.getNewControlName(), newControl);
            });

            const history = this.getHistory();
            if ( history ) {
                history.addChange(new ChangeNewControlMapAddItems(this, 0, newControls));
            }
        }
    }

    public getNameMap(): Map<string, NewControl> {
        return this.newControlNames;
    }

    public setNameMap(names: Map<string, NewControl>): void {
        this.newControlNames = names;
    }

    public setNameToNameMap(name: string, newControl: NewControl): void {
        if (!name || !newControl) {
            return;
        }

        this.newControlNames.set(name, newControl);
    }

    public getNewControlByPos( contentPos: ParagraphContentPos ): NewControl {
        if ( null == contentPos ) {
            return;
        }

        const pos = findLowerBound(this.newControls, contentPos); // element.getNewControlLeftBorderPos();
        // const endPos = findLowerBound(this.newControls, contentPos); // element.getNewControlRightBorderPos();

        if ( -1 < pos ) {
            const newControl = this.newControls[pos];
            if ( newControl && true === newControl.isCursorInNewControl(contentPos) ) {
                return newControl;
            }
        }

        return;
    }

    public isCursorInNewControl( contentPos: ParagraphContentPos ): boolean {
        if ( null == contentPos ) {
            return false;
        }

        const pos = findLowerBound(this.newControls, contentPos); // element.getNewControlLeftBorderPos();
        // const endPos = findLowerBound(this.newControls, contentPos); // element.getNewControlRightBorderPos();

        if ( -1 < pos ) {
            const newControl = this.newControls[pos];
            if ( newControl && true === newControl.isCursorInNewControl(contentPos) ) {
                return true;
            }
        }

        return false;
    }

    /**
     * 获取光标位置，或者指定位置所在的newControl
     * @param contentPos
     */
    public getPosNewControl( contentPos: ParagraphContentPos, getLastControl: boolean = false): NewControl {

        const pos = findLowerBound(this.newControls, contentPos);
        const count = this.newControls.length;

        if ( -1 < pos && pos < count ) {
            const newControl = this.newControls[pos];
            if (getLastControl === true) {
                return newControl;
            }
            if ( getLastControl === false && newControl && 1 !== contentPos.compare(newControl.getEndPos() ) ) {
                return newControl.getPosNewControl(contentPos);
            }
        }

        return null;
    }

    /**
     * 获取指定位置的所有newControl名称
     * @param startPos
     * @param endPos
     * @param bOverLay 是否获取未被全部包含的newcontrols
     */
    public getNewControlNamesByPos( startPos: ParagraphContentPos, endPos: ParagraphContentPos,
                                    bOverLay: boolean = false, bLeaf: boolean = true): string[] {
        const result: string[] = [];

        if ( 1 === startPos.compare(endPos) ) {
            return result;
        }

        const startIndex = findLowerBound(this.newControls, startPos);
        const endIndex = findLowerBound(this.newControls, endPos);
        let curPos: any;
        if ( -1 < startIndex ) {
            const newControl = this.newControls[startIndex];
            // tslint:disable-next-line:no-conditional-assignment
            if ( newControl && (curPos = newControl.getEndPos()) && 1 !== startPos.compare(curPos) ) {

                // if ( 1 === endPos.compare(newControl.getEndPos()) ) {
                if ( true === newControl.isLayOverNewControl(startPos, endPos) ) {
                    result.push(newControl.getNewControlName());
                } else if ( true === bOverLay && 1 === endPos.compare(newControl.getStartPos()) ) {
                    result.push(newControl.getNewControlName());
                }

                if (bLeaf === true) {
                    const leafListNames = newControl.getLeafListNamesByPos(startPos, endPos, bOverLay);

                    for (const name of leafListNames) {
                        result.push(name);
                    }
                }
            }
        }

        const count = this.newControls.length;
        for ( let curIndex = startIndex + 1; curIndex <= endIndex && curIndex < count; curIndex++ ) {
            const newControl = this.newControls[curIndex];

            if ( newControl ) {
                if ( curIndex !== endIndex || 1 === endPos.compare(newControl.getEndPos()) ) {
                    result.push(newControl.getNewControlName());
                } else if ( true === bOverLay && -1 === endPos.compare(newControl.getEndPos()) ) {
                    result.push(newControl.getNewControlName());
                }

                if ( bLeaf ) {
                    const leafListNames = newControl.getLeafListNamesByPos(startPos, endPos, bOverLay);

                    for (const name of leafListNames) {
                        result.push(name);
                    }
                }
            }
        }

        // console.log(result)
        return result;
    }

    public browseTemplet(type: number, nProtected?: number): number {
        const newControlNames = this.newControlNames;
        for (const [name, newControl] of newControlNames) {
            newControl.browseTemplet(type, nProtected);
        }
        this.parent.recalculate();
        return ResultType.Success;
    }

    /**
     * 获取在指定位置内的所有newcontrols
     * @param startPos
     * @param endPos
     */
    public getNewControls( startPos: ParagraphContentPos, endPos: ParagraphContentPos,
                           bOverLay: boolean = false, bLeaf: boolean = true): NewControl[] {
        const newControls: NewControl[] = [];
        const newControlNames = this.getNewControlNamesByPos(startPos, endPos, bOverLay, bLeaf);

        for (const name of newControlNames) {
            const newControl = this.newControlNames.get(name);

            if ( newControl ) {
                newControls.push(newControl);
            }
        }

        return newControls;
    }

    public getNewControlTips( name: string ): string {
        const newControl = this.getNewControlByName(name);
        if ( null == newControl ) {
            return null;
        }

        return newControl.getTipsContent();
    }

    public setNewControlTips( name: string, content: string ): void {
        const newControl = this.getNewControlByName(name);
        if ( null == newControl ) {
            return ;
        }

        newControl.setTipsContent(content);
    }

    public setNewControlsToPrint(): void {
        const newControlNames = this.newControlNames;
        for (const [name, newControl] of newControlNames) {
            newControl.toPrint();
        }
    }

    public setNewControlProperty(oldName: string, property: INewControlProperty): number {
        if ( null == oldName ) {
            return ResultType.Failure;
        }

        if (property.newControlName !== undefined && property.newControlName !== oldName ) {
            IFRAME_MANAGER.setDocId(this.parent.id);
            if ( false === this.checkNewControlName(property.newControlName) ) {
                // alert('此名称已存在！');
                message.error('此名称已存在！');
                return ResultType.Failure;
            }

            if ( '' === property.newControlName ) {
                // alert('名称不能为空！');
                message.error('名称不能为空！');
                return ResultType.Failure;
            }
        }

        const newControl = this.getNewControlByName(oldName);
        if (!newControl) {
            return ResultType.Failure;
        }
        const res = newControl.setProperty(property); // can be extended class
        if (res !== ResultType.Success) {
            return res;
        }
        if (property.customProperty !== undefined && Object.keys(property).length === 2) {
            return ResultType.UnRefresh;
        }
        this.parent.removeSelection();
        this.parent.recalculate();
        this.parent.updateCursorXY();

        return res;
    }

    public updateControlName( newControl: NewControl, oldName: string ): void {
        if ( newControl !== this.newControlNames.get(oldName) ) {
            return ;
        }

        const newName = newControl.getNewControlName();
        this.newControlNames.delete(oldName);
        this.newControlNames.set(newName, newControl);
    }

    public removeNewControlByName( name: string ): boolean {
        const newControl = this.newControlNames.get(name);

        if ( newControl ) {
            const res = this.removeNewControl(newControl);
            if ( res === false ) {
                return false;
            }
            this.newControlNames.delete(name);
            return true;
        }

        return false;
    }

    public deleteNewControl(sName: string, bDeletePara?: boolean): number {
        const newControl = this.getNewControlByName(sName);
        if (!newControl) {
            return ResultType.Failure;
        }

        const startBorder = newControl.getStartBorderPortion();
        const endBorder = newControl.getEndBorderPortion();
        const parentControl = newControl.getParent();
        // 先删结构
        let res = this.removeNewControlByName(sName);
        if (!res) {
            return ResultType.Failure;
        }

        const para: any = startBorder.paragraph;
        const parent = para.parent;
        const contents = para.content;

        const startPorIndex = contents.findIndex((por) => por === startBorder);
        const endPorIndex = endBorder.paragraph.getContent()
            .findIndex((por) => por === endBorder);

        // 再删内容
        res = this.deleteContent(startPorIndex, endPorIndex, para, endBorder.paragraph, newControl);

        if (res) {
            para.checkNewControlPlaceContent();
            const portion = para.content[startPorIndex];
            portion.portionContentPos = 0;
        }

        if ( parentControl && parentControl.checkPlaceHolderContent() ) {
            parentControl.addPlaceHolderContent();
        }

        // const deleteIndex = [];

        // for (let curIndex = startParaIndex; curIndex <= endParaIndex; curIndex++) {

        // }

        // // if (deleteIndex.length > 0) {
        // //     newControl.getDocumentParent()
        // //         .splice(deleteIndex[0], deleteIndex[deleteIndex.length - 1] - deleteIndex[0] + 1);
        // //     // contents.splice(deleteIndex[0], deleteIndex[deleteIndex.length - 1] - deleteIndex[0] + 1);
        // // }

        return ResultType.Success;
    }

    public removeNewControl( delNewControl: NewControl ): boolean {
        if ( null == delNewControl ) {
            return false;
        }

        const startPos = delNewControl.getStartPos();
        const endPos = delNewControl.getEndPos();

        if ( null == startPos || null == endPos) {
            return false;
        }

        const startIndex = findLowerBound(this.newControls, startPos);
        const endIndex = findLowerBound(this.newControls, endPos);

        const count = this.newControls.length;
        const cascadeManager = this.cascadeManager;
        if ( -1 < endIndex && endIndex < count ) {
            const newControl = this.newControls[endIndex];

            if ( delNewControl.getNewControlName() === newControl.getNewControlName() ) {
                const leafList = newControl.getLeafList();
                let name: string;
                for (const leafControl of leafList) {
                    name = leafControl.getNewControlName();
                    this.newControlNames.delete(name);
                }
                const pos = this.newControls.indexOf(newControl);
                cascadeManager.deleteCascade(newControl);
                this.newControls.splice(pos, 1);

                const history = this.getHistory();
                if ( history ) {
                    history.addChange(new ChangeNewControlRemoveItem(newControl, pos, null));
                }
                this.removeSourceData(newControl);
            } else if (  1 === startPos.compare(newControl.getStartPos())
                        && -1 === endPos.compare(newControl.getEndPos() ) ) {
                cascadeManager.deleteCascade(delNewControl);
                newControl.remove(delNewControl);
                this.removeSourceData(delNewControl);
            }
        }

        return true;
    }

    public clearAllCascadeFlag(): void {
        if (Object.keys(this.cascade).length === 0 &&
        Object.keys(this.cascadeEvents).length === 0) {
            return;
        }
        clearTimeout(this._timeout);
        this._timeout = setTimeout(() => {
            this.cascade = {};
            this.cascadeEvents = {};
        }, 0);
    }

    public clearAllNewcontrolCascade(): void {
        this.cascade = {};
        this.cascadeEvents = {};
    }

    public addRunCascade(name: string): void {
        this.cascade[name] = true;
    }

    public isOldCascade(name: string): boolean {
        return this.cascade[name];
    }

    public getAllCascades(bChecked?: boolean): any[] {
        return this.cascadeManager.getAllCascades(bChecked);
    }

    public initOpenCascades(datas: any[]): void {
        this.cascadeManager.initOpenDoc(datas);
    }

    public setTriggerCascadeStatus(flag: boolean): Promise<any> {
        if (flag) {
            if (!this._triggerCascades) {
                this._triggerCascades = {};
            }
            return;
        }

        return new Promise<void>((resolve, reject) => {
            const callback: any = () => {
                const triggerCascades = this._triggerCascades;
                if (!triggerCascades) {
                    return;
                }
                const keys = Object.keys(triggerCascades);
                this._triggerCascades = undefined;
                let res = false;
                keys.forEach((key) => {
                    res = this.triggerCascade(key) || res;
                });
                if (res) {
                    gEvent.setEvent(this.parent.id, gEventName.RefreshDocUI);
                }
            };
            resolve(callback);
        });
    }

    public triggerCascade(name: string, value?: string): boolean {
        // 进行批量触发级联，减少刷新
        if (this.cascadesMap && this.cascadesMap.has(name)) {
            return false;
        }

        if (this._triggerCascades) {
            this._triggerCascades[name] = true;
            return false;
        }
        const newControl = this.newControlNames.get(name);
        if (!newControl) {
            return false;
        }
        if (this.cascade[name]) {
            return false;
        }
        this.cascade[name] = true;
        const datas = this.cascadeManager.getCascades(newControl);
        if (!datas || datas.length === 0) {
            return false;
        }

        if (value === undefined) {
            if (newControl.isMultiAndRadio()) {
                value = (newControl as NewControlRadio).getSelected() || '';
            } else if (newControl.isCheckBox()) {
                value = (newControl as NewControlCheck).isSelected()
                    .toString();
            } else if (newControl.isNewDateBox()) {
                value = newControl.isPlaceHolderContent() ? 'false' : 'true';
            } else {
                value = newControl.getNewControlText();
                const title = newControl.getTitle();
                if (title) {
                    value = value.replace(title, '');
                }
            }
        }
        const parent = this.parent;
        const pos = parent.curPos.contentPos;
        const endPortion = newControl.getEndBorderPortion();
        const para = endPortion.paragraph;
        let porIndex = para.curPos.contentPos;
        let paraIndex = para.index;
        let result = ResultType.UnEdited;
        let bRefresh: boolean = false;
        const arrs = [CascadeActionType.Show, CascadeActionType.Hidden, CascadeActionType.SyncText,
            CascadeActionType.SetMustItem, CascadeActionType.UnsetMustItem];
        datas.forEach((item) => {
            if (item.target === name) {
                return;
            }
            result = this.trigger(item, value, newControl) && result;
            if (bRefresh === false && result === ResultType.Success && arrs.includes(item.action)) {
                bRefresh = true;
            }
        });
        // const res = result === ResultType.Success;
        if (result === ResultType.Success) {
            const paraParent = para.getParent();
            let len = paraParent.content.length;
            if (paraIndex >= len) {
                paraIndex = len - 1;
            }
            paraParent.curPos.contentPos = paraIndex;
            len = para.content.length;
            if (porIndex >= len) {
                porIndex = len - 1;
            }
            para.curPos.contentPos = porIndex;
            parent.curPos.contentPos = pos;
            if (bRefresh) {
                parent.recalculate();
            }
            parent.updateCursorXY();
        }
        return (ResultType.Success === result);
    }

    /**
     * 前端UI进行校验事件数据的准确性
     * @param eventInfo 事件数据集合
     * @returns 异常信息
     */
    public checkEventInfo(eventInfo: ICascadeEvent): string {
        const newControls = this.newControlNames;
        if (eventInfo && eventInfo.event) {
            const events = eventInfo.event;
            for (let index = 0, len = events.length; index < len; index++) {
                const data = events[index];
                let source = data.source;
                if (source === '*') {
                    source = eventInfo.target;
                }
                const ids = source.split(',');
                for (let childIndex = 0, childLen = ids.length; childIndex < childLen; childIndex++) {
                    const id = ids[childIndex];
                    const curNewControl = newControls.get(id);
                    if (!curNewControl) {
                        return `第${index + 1}行的${source}中的${id}元素不存在，请重新填写`;
                    }
                    if (curNewControl) {
                        switch (data.triggerType) {
                            case CascadeTriggerType.SelectChanged: {
                                if (!curNewControl.isAmongComboxOrListStruct()) {
                                    return `第${index + 1}行的${id}与触发事件类型不符合，请重新填写`;
                                }
                                break;
                            }

                            case CascadeTriggerType.CheckChanged: {
                                if (!curNewControl.isCheckBox() && !curNewControl.isMultiAndRadio()) {
                                    return `第${index + 1}行的${id}与触发事件类型不符合，请重新填写`;
                                }
                                break;
                            }

                            case CascadeTriggerType.TextChanged: {
                                if (eventInfo.key !== CodeAndValue.Value) {
                                    return `第${index + 1}行的${id}与触发事件的逻辑值不符，请重新填写`;
                                }

                                if (!curNewControl.isNewTextBox() && !curNewControl.isNumberBox()) {
                                    return `第${index + 1}行的${id}与触发事件类型不符合，请重新填写`;
                                }
                                break;
                            }

                            default: {
                                return `第${index + 1}行的${id}与触发事件类型不存在，请重新填写`;
                            }
                        }

                        // curNewControl.setTriggetEventInfo({name, type: data.triggerType});
                    }
                }
            }
        }
    }

    public setEventInfo(newControl: NewControl, eventInfo: ICascadeEvent): void {
        const event: ICascadeEvent = newControl.getEvent();
        const newControls = this.newControlNames;
        const name = newControl.getNewControlName();
        // 删除旧的数据
        if (event) {
            if (event !== eventInfo && event.expression) { // 在initOpenDocument方法中会调用一次设置eventInfo
                delete event.expression;
            }
            event.event?.forEach((data) => {
                let source = data.source;
                if (source === '*') {
                    source = event.target;
                }
                const ids = source.split(',');
                ids.forEach((id) => {
                    const curNewControl = newControls.get(id);
                    if (curNewControl) {
                        curNewControl.removeTriggerEventInfo(name);
                    }
                });
            });
        }

        // 新增新的
        if (eventInfo && eventInfo.event) {
            const isExpres = eventInfo.key === CodeAndValue.Expression;
            eventInfo.event.forEach((data) => {
                let source = data.source;
                if (source === '*' || isExpres) {
                    source = eventInfo.target;
                }
                const ids = source.split(',');
                ids.forEach((id) => {
                    const curNewControl = newControls.get(id);
                    if (curNewControl) {
                        switch (data.triggerType) {
                            case CascadeTriggerType.SelectChanged: {
                                if (!curNewControl.isAmongComboxOrListStruct()) {
                                    return;
                                }
                                break;
                            }

                            case CascadeTriggerType.CheckChanged: {
                                if (!curNewControl.isCheckBox() && !curNewControl.isMultiAndRadio()) {
                                    return;
                                }
                                break;
                            }

                            case CascadeTriggerType.TextChanged: {
                                if (isExpres) {
                                    if (!curNewControl.isNumberBox()) {
                                        return;
                                    }
                                } else {
                                    if (eventInfo.key !== CodeAndValue.Value) {
                                        return;
                                    }

                                    if (!curNewControl.isNewTextBox() && !curNewControl.isNumberBox()
                                        && !curNewControl.isNewDateBox()) {
                                        return;
                                    }
                                }
                                break;
                            }

                            default: {
                                return;
                            }
                        }

                        curNewControl.setTriggetEventInfo({name, type: data.triggerType});
                    }
                });
            });
        }
    }

    public triggerEvent(name: string, value?: string, bTrigger?: boolean): boolean {
        if (this.cascadeEvents[name] === true) {
            return false;
        }

        const newControl = this.newControlNames.get(name);
        if (!newControl) {
            return false;
        }

        const infos = newControl.getTriggetEventInfo2();
        if (!infos || infos.length === 0) {
            return false;
        }

        this.cascadeEvents[name] = true;
        let result = ResultType.UnEdited;
        const newControlNames = this.newControlNames;
        infos.forEach((info) => {
            const source = newControlNames.get(info.name);
            if (!source) {
                newControl.removeTriggerEventInfo(info.name);
                return false;
            }
            result = this.triggerEventInfo(source, info.type) && result;
        });

        if (bTrigger !== false && result === ResultType.Success) {
            this.parent.recalculate();
            this.triggerCascadeMap();
            return true;
        }

        this.cascadesMap = null;
        return false;
    }

    /**
     * 执行事件级联
     * @param newControl 被赋值结构化元素
     * @param type 触发事件的类型
     * @returns 返回值
     */
    public triggerEventInfo(newControl: NewControl, type: CascadeTriggerType): number {
        const info: ICascadeEvent = newControl.getEvent();
        if (!info || !info.event || !info.event.length) {
            return ResultType.UnEdited;
        }
        const newControlNames = this.newControlNames;
        const key = info.key;
        const curControlName = newControl.getNewControlName();
        let num: number;
        if (key === CodeAndValue.Expression) {
            // 混合运算
            const { calculateByString } = MixFormulaParser;
            num = calculateByString(info.expression, (name: string): number => {
                const control = newControlNames.get(name);
                if (!control || !control.isNumberBox()) {
                    return NaN;
                }
                return +control.getNewControlText2();
            });

        } else {
            const values: number[] = [];
            info.event.forEach((event) => {
                let name = event.source;
                if (name === '*') {
                    name = info.target;
                }

                if (!name) {
                    return;
                }

                const names = name.split(',');
                names.forEach((cur) => {
                    // 如果目标包含元素自身，则进行过滤
                    if (cur === curControlName) return;
                    const control = newControlNames.get(cur);
                    if (!control) {
                        return;
                    }

                    if (key === CodeAndValue.Value && (control.isNumberBox() || control.isNewTextBox())) {
                        const text2: any = control.getNewControlText2();
                        if (!text2 || isNaN(text2)) {
                            return;
                        }
                        values.push(+text2);
                        return;
                    } else if (control.isCheckBox()) {
                        if (!control['isSelected']()) {
                            return;
                        }
                        let text2: any;
                        if (key === CodeAndValue.Value ) {
                            text2 = control.getLabelCode();
                        } else {
                            text2 = control.getCheckLabel();
                        }
                        if (!text2 || isNaN(text2)) {
                            return;
                        }
                        values.push(+text2);
                        return;
                    }

                    const list = control.getItemList();
                    if (!list || list.length === 0) {
                        return;
                    }

                    list.forEach((item) => {
                        if (item.bSelect !== true || !item[key] || isNaN(item[key])) {
                            return;
                        }
                        values.push(+item[key]);
                    });
                });
            });

            if (values.length === 0) {
                // tslint:disable-next-line: no-unused-expression
                (newControl.isNumberBox() || newControl.isNewTextBox()) && this.addCascadeMap(newControl, '');
                return newControl.setNewControlText('', false);
            }

            switch (info.action) {
                case EventType.Sum: {
                    num = 0;
                    values.forEach((item) => num += item);
                    break;
                }
                case EventType.Minus: {
                    num = values[0];
                    values.forEach((item, itemIndex) => itemIndex > 0 && (num -= item));
                }
                default: {
                    return ResultType.UnEdited;
                }
            }
        }
        if (num === undefined || isNaN(num)) {
            return ResultType.UnEdited;
        }

        let text = newControl.getNewControlText2();
        if (text && num === (+text)) {
            return ResultType.UnEdited;
        }
        text = num.toString();
        this.addCascadeMap(newControl, text);
        let res;
        if (newControl.isNumberBox()) {
            res = (newControl as any).setNumberBoxText(num, false);
            res = res === 0 ? ResultType.Success : ResultType.UnEdited;

            text = newControl.getNewControlText();
            if (newControl.getUnit()) {
                text = text.replace(newControl.getUnit(), '');
            }
        } else {
            res = newControl.setNewControlText(text, false);
        }
        // 有可能会被上一步清除
        this.addCascadeMap(newControl, text);
        return res;
    }

    public clearCascadeMap(): void {
        this.cascadesMap = null;
    }

    public addCascadeMap(newControl: NewControl, value?: any): void {
        const cascadeManager = this.cascadeManager;
        const cases = cascadeManager.getCascades(newControl);
        let text: string;
        if (cases && cases.length > 0) {
            if (!this.cascadesMap) {
                this.cascadesMap = new Map();
            }
            if (value === null) {
                text = value = newControl.getNewControlText3();
            }
            this.cascadesMap.set(newControl.getNewControlName(), {text: value, newControl});
        }

        const infos = cascadeManager.getTriggetEventInfo(newControl);
        if (infos && infos.size > 0) {
            if (!this.cascadesMap) {
                this.cascadesMap = new Map();
            }
            if (value === null && text === undefined) {
                value = newControl.getNewControlText3();
            }
            this.cascadesMap.set(newControl.getNewControlName(), {text: value, newControl});
        }
    }

    public triggerCascadeMap(): number {
        if (this.triggerTime !== undefined && this.triggerTime-- < 0) {
            this.cascadesMap = null;
            return ResultType.UnEdited;
        }
        const map = this.cascadesMap;
        if (!map || map.size === 0) {
            return ResultType.UnEdited;
        }

        this.cascadesMap = null;
        const list: Map<string, any[]> = new Map();
        const datas = Array.from(map);
        const cascadeManager = this.cascadeManager;
        // 根据元素从后往前进行收集，这样可以少排版
        for (let index = datas.length - 1; index >= 0; index--) {
            const item = datas[index];
            const option = item[1];
            const newControl = option.newControl;
            const infos = cascadeManager.getTriggetEventInfo2(newControl);
            const name = newControl.getNewControlName();
            if (infos && infos.length > 0) { // 事件级联
                this.getTriggerInfoList(list, infos, option.text, name, 1);
                // this.triggerEvent(item[0], option.text);
            }

            const cases = cascadeManager.getCascades(newControl);
            if (cases && cases.length > 0) { // 普通级联
                this.getTriggerInfoList(list, cases, option.text, name);
            }
        }
        // 先触发区域级联，区域范围大，有可能会删除一些结构化元素
        let res = this.triggerRegions(list);
        res = this.triggerNewControls(list) && res;
        // 触发完成所有级联后，再执行下一批级联
        res = this.triggerCascadeMap() && res;

        return res;
    }

    public triggerRegions(list: Map<string, any[]>): number {
        const keyRegion: any = 111111111111111111111;
        const regionList: any = list.get(keyRegion);
        if (!regionList) {
            return ResultType.UnEdited;
        }
        list.delete(keyRegion);
        // 先对区域进行级联
        const regions: Map<string, any[]> = regionList;
        // this.cascadeRegionMap = null;
        if (regions && regions.size > 0) {
            const newControlNames = this.newControlNames;
            for (const [name, infos] of regions) {
                for (let index = infos.length - 1; index >= 0; index--) {
                    const info = infos[index];
                    const newControl = newControlNames.get(info.oldName);

                    // 判断当前元素是否符合条件
                    if (!this.isTrigger(info, info.value, newControl)) {
                        infos.splice(index, 1);
                    }
                }
                if (infos.length === 0) {
                    regions.delete(name);
                }
            }

            return this.parent.getRegionManager()
            .triggerCascade(regions);
        }

        return ResultType.UnEdited;
    }

    public triggerNewControls(list: Map<string, any[]>): number {
        const names = Array.from(list.keys());
        if (!names || names.length === 0) {
            return ResultType.UnEdited;
        }

        const newControlNames = this.newControlNames;
        const newControls: NewControl[] = [];
        this.getSortNewControls(newControls, names, null);
        let res = ResultType.UnEdited;
        const warnNewcontrols = {};
        newControls.forEach((newControl) => {
            const name = newControl.getNewControlName(); // 设置值的结构化元素
            const infos = list.get(name);
            const obj = { // 过滤多次操作（设置值，显隐元素）
                text: false,
                hide: false,
                checked: false,
                must: false,
                bWarnTip: undefined,
            };
            infos.forEach((info) => {
                // 事件触发
                if (info.name !== undefined) {
                    if (obj.text === true) {
                        return;
                    }
                    const curRes = this.triggerEventInfo(newControl, info.type);
                    res = curRes && res;
                    if (curRes === ResultType.Success) {
                        obj.text = true;
                    }

                    return;
                } else { // 级联触发
                    switch (info.action) {
                        case CascadeActionType.Show:
                        case CascadeActionType.Hidden: {
                            if (obj.hide === true) {
                                return;
                            }

                            const oldNewControl = newControlNames.get(info.oldName);
                            if (oldNewControl && oldNewControl.isCheckBox()) {
                                info.value = oldNewControl['isSelected']() ? 'true' : 'false';
                            }
                            // 判断条件是否能触发级联
                            if (this.isTrigger(info, info.value, oldNewControl)) {
                                obj.hide = true;
                                // 是否包含级联
                                this.addCascadeMap(newControl, info.value);
                                res = this.triggerAction(info, info.value, newControl, 0, false) && res;
                            }
                            break;
                        }
                        case CascadeActionType.Unchecked:
                        case CascadeActionType.Checked: {
                            if (obj.checked === true) {
                                return;
                            }

                            const oldNewControl = newControlNames.get(info.oldName);
                            if (oldNewControl && oldNewControl.isCheckBox()) {
                                info.value = oldNewControl['isSelected']() ? 'true' : 'false';
                            }
                            // 判断条件是否能触发级联
                            if (this.isTrigger(info, info.value, oldNewControl)) {
                                obj.checked = true;
                                // 是否包含级联
                                this.addCascadeMap(newControl, info.value);
                                res = this.triggerAction(info, info.value, newControl, 0, false) && res;
                            }
                            break;
                        }
                        case CascadeActionType.SetMustItem:
                        case CascadeActionType.UnsetMustItem: {
                            if (obj.must === true) {
                                return;
                            }

                            const oldNewControl = newControlNames.get(info.oldName);
                            if (oldNewControl && oldNewControl.isCheckBox()) {
                                info.value = oldNewControl['isSelected']() ? 'true' : 'false';
                            } else if (oldNewControl && oldNewControl.isNewDateBox()) {
                                info.value = oldNewControl.isPlaceHolderContent() ? 'false' : 'true';
                            } else if (oldNewControl && oldNewControl.isNewTextBox()) {
                                // 确保文本框的值正确设置
                                info.value = oldNewControl.getNewControlText();
                            } else {
                                info.value = oldNewControl.getNewControlText2();
                            }

                            // 判断条件是否能触发级联
                            if (this.isTrigger(info, info.value, oldNewControl)) {
                                obj.must = true;
                                // 是否包含级联
                                this.addCascadeMap(newControl, info.value);
                                res = this.triggerAction(info, info.value, newControl, 0, false) && res;
                            }
                            break;
                        }
                        case CascadeActionType.WarnTip: {
                            // if (obj.bWarnTip) {
                            //     return;
                            // }
                            const oldNewControl: any = newControlNames.get(info.oldName);
                            const curText = newControl.getDateTime();
                            const oldText = oldNewControl.getDateTime();

                            // 判断条件是否能触发级联
                            if (curText && oldText && this.isTrigger(info, (new Date(oldText).getTime()).toString(),
                            oldNewControl, false, (new Date(curText).getTime()).toString())) {
                                obj.bWarnTip = true;
                                // 是否包含级联
                                this.addCascadeMap(newControl, info.value);
                                res = this.triggerAction(info, info.value, oldNewControl, 0, false) && res;
                            } else {
                                obj.bWarnTip = false;
                            }
                            break;
                        }
                        default: {
                            if (obj.text === true) {
                                return;
                            }

                            const oldNewControl = newControlNames.get(info.oldName); // 触发源结构化元素
                            if (oldNewControl && oldNewControl.isCheckBox()) {
                                info.value = oldNewControl['isSelected']() ? 'true' : 'false';
                            } else if (oldNewControl && oldNewControl.isNewTextBox()) {
                                // 确保文本框的值正确设置
                                info.value = oldNewControl.getNewControlText();
                            }

                            if (this.isTrigger(info, info.value, oldNewControl)) {
                                obj.text = true;
                                let value = info.value;
                                // 设置文本，文本来源于已配置好的文本，而不是当前文本，所以这里需要改变值，否则后面进行判断有异常
                                if (info.action === CascadeActionType.SetText) {
                                    value = info.actionText;
                                }
                                this.addCascadeMap(newControl, value);
                                res = this.triggerAction(info, info.value, newControl, 0, false, oldNewControl) && res;
                            }
                            break;
                        }
                    }
                }

                if (obj.bWarnTip !== undefined) {
                    warnNewcontrols[info.oldName] = warnNewcontrols[info.oldName] || obj.bWarnTip;
                }
            });
        });
        const keys = Object.keys(warnNewcontrols);
        keys.forEach((key) => {
            if (warnNewcontrols[key] === false) {
                (newControlNames.get(key) as any).setWarnTip();
            }
        });

        if (res === ResultType.Success) {
            this.parent.recalculate();
        }
        return res;
    }

    public getTriggerInfoList(list: Map<string, any[]>, infos: any[], value: string,  sname?: string,
                              rgionType?: number): void {
        const names: Map<string, any[]> = list;
        const newControlNames = this.newControlNames;
        if (!newControlNames) {
            return;
        }

        // 后面覆盖前面的；去除重复，拒绝重复填充、隐藏一个值
        if (rgionType === 1) { // event
            for (let num = infos.length - 1; num >= 0; num--) {
                const item: ITriggerEventInfo = infos[num];
                if (!item || !item.name) {
                    continue;
                }

                const name = item.name;
                if (!newControlNames.has(name)) {
                    continue;
                }

                const objs = names.get(name);
                if (objs) {
                    const obj: any = {...item};
                    obj.value = value;
                    obj.oldName = sname;
                    objs.push(obj);
                    continue;
                }
                const obj2: any = {...item};
                obj2.value = value;
                obj2.oldName = sname;
                names.set(name, [obj2]);
            }
            return;
        }

        const regions = this.parent.getRegionManager()
        .getRegionMap();
        if (!regions) {
            return;
        }
        // 区域数据收集，另外存一个map
        const keyRegion: any = 111111111111111111111;
        let regionList: any = list.get(keyRegion);
        if (!regionList) {
            regionList = new Map();
            list.set(keyRegion, regionList);
        }
        for (let index = infos.length - 1; index >= 0; index--) {
            const item: ICascade = infos[index];
            if (!item.target || item.target === sname) {
                continue;
            }

            const action = item.action;
            if (!action) {
                return;
            }

            const ids = item.target.split(',');
            let changeNum: number = 0;
            // 收集所有的操作类型，后续可以根据目标除掉重复填充或者隐藏
            ids.forEach((id) => {
                let map;
                if (regions.has(id)) { // 当前是否在区域内
                    map = regionList;
                } else if (!newControlNames.has(id)) { // 当前也不在结构化元素内
                    changeNum++;
                    return;
                } else {
                    map = names;
                }
                const objs: any[] = map.get(id);
                if (objs) {
                    const obj: any = {...item};
                    obj.value = value;
                    obj.oldName = sname;
                    objs.push(obj);
                    return;
                }
                const obj2: any = {...item};
                obj2.value = value;
                obj2.oldName = sname;
                map.set(id, [obj2]);
            });
            // let newControl;
            // if (ids.length === changeNum && (newControl = newControlNames.get(sname)).isNewDateBox()) {
            //     newControl.setWarnTip();
            // }
            // 先获取 newControl
            const tempNewControl = newControlNames.get(sname);
            // 然后检查是否存在且满足其他条件
            if (ids.length === changeNum && tempNewControl && tempNewControl.isNewDateBox()) {
                if (tempNewControl instanceof NewControlDate) {
                    tempNewControl.setWarnTip();
                }
            }
        }
    }

    /**
     * 删除指定位置的newControl
     * @param startPos
     * @param endPos
     */
    public remove( startPos: ParagraphContentPos, endPos: ParagraphContentPos ): void {
        const startIndex = findLowerBound(this.newControls, startPos);
        let endIndex = findLowerBound(this.newControls, endPos);
        const delNewControlNames = this.getNewControlNamesByPos(startPos, endPos, false);

        const history = this.getHistory();
        // history.createNewHistoryPoint(HistroyItemType.NewControlRemoveItem);
        const cascadeManager = this.cascadeManager;
        if ( startIndex === endIndex ) {
            if ( -1 < startIndex ) {
                const newControl = this.newControls[startIndex];
                if ( newControl && NewControlType.Section === newControl.getType() ) {
                    // const pos = newControl.getEndPos();
                    // if ( 1 !== startPos.compare(pos) && */1 !== endPos.compare(pos) ) {
                        newControl.deleteLeaf(startPos, endPos);
                    // }
                }
            }
        } else {
            const count = this.newControls.length;
            if ( endIndex >= count ) {
                endIndex = count - 1;
            }
            // this.newControls.splice(startIndex + 1, endIndex - startIndex);

            if (-1 < startIndex) {
                const newControl = this.newControls[startIndex];
                if ( newControl ) {
                    if ( false === newControl.isLayOverNewControl(startPos, endPos) ) {
                        const leafListNames = newControl.getLeafListNamesByPos(startPos, endPos);

                        for (const name of leafListNames) {
                            const leafControl = this.getNewControlByName(name);
                            if ( leafControl ) {
                                if ( true === leafControl.isLayOverNewControl(startPos, endPos) ) {
                                    cascadeManager.deleteCascade(leafControl);
                                    newControl.remove(leafControl);
                                    this.removeSourceData(leafControl);
                                }
                            }
                        }
                    }
                }
            }

            // for (let index = endIndex; index >= startIndex && index >= 0; index--) {
            if ( -1 < endIndex ) {
                const newControl = this.newControls[endIndex];
                if ( newControl ) {
                    if ( false === newControl.isLayOverNewControl(startPos, endPos) ) {
                    //     // newControlNames.push(newControl.getNewControlName());
                    //     newControl.deleteLeaf(startPos, endPos);
                    //     const pos = this.newControls.indexOf(newControl);
                    //     this.newControls.splice(pos, 1);
                    // }
                        endIndex--;

                        const leafListNames = newControl.getLeafListNamesByPos(startPos, endPos);

                        for (const name of leafListNames) {
                            const leafControl = this.getNewControlByName(name);
                            if ( leafControl ) {
                                if ( true === leafControl.isLayOverNewControl(startPos, endPos) ) {
                                    cascadeManager.deleteCascade(leafControl);
                                    newControl.remove(leafControl);
                                    this.removeSourceData(leafControl);
                                }
                            }
                        }
                    }
                }
            }

            for (let pos = endIndex; pos >= startIndex + 1; pos--) {
                const newControl = this.newControls[pos];
                if ( history ) {
                    history.addChange(new ChangeNewControlRemoveItem(newControl, pos, null));
                }
            }
            cascadeManager.deleteEachNewControl(this.newControls.slice(startIndex + 1, endIndex + 1));
            this.newControls.splice(startIndex + 1, endIndex - startIndex);
            // console.log(this.newControls)
        }

        // console.log(delNewControlNames)
        for (const name of delNewControlNames) {
            this.newControlNames.delete(name);
        }
    }

    // public getMustInputPlaceholder(): NewControl[] {
    //     const newControls = this.newControlNames;
    //     const res: NewControl[] = [];
    //     for (const [name, newControl] of newControls) {
    //         if (newControl.isMustInput() && newControl.isPlaceHolderContent()) {
    //             res.push(newControl);
    //         }
    //     }

    //     return res;
    // }

    public isValidDelete( startPos: ParagraphContentPos, endPos: ParagraphContentPos,
                          newControlNames?: string[] ): boolean {
        const bAdminMode = this.parent.isAdminMode();
        if ( bAdminMode ) {
            return true;
        }

        let result = true;
        const startNewControl = this.getPosNewControl(startPos);
        const endNewControl = this.getPosNewControl(endPos);

        // if (startNewControl && startNewControl.isMultiAndRadio()
        // || endNewControl && endNewControl.isMultiAndRadio()) {
        //     return false;
        // }

        if ( startNewControl && startNewControl === endNewControl
            && false === startNewControl.isEditProtect() && true === startNewControl.isReverseEdit() ) {
            if ( false === startNewControl.isNewSection() ) {
                return result;
            } else {
                let bHasDelProtect = false;
                const newControls = this.getNewControls(startPos, endPos);

                for (const newControl of newControls) {
                    if ( newControl && newControl.isDeleteProtect() ) {
                        bHasDelProtect = true;
                        if ( null != newControlNames ) {
                            newControlNames.push(newControl.getNewControlName());
                        }
                        break;
                    }
                }

                if ( false === bHasDelProtect ) {
                    return result;
                }
            }
        }

        // if ( startNewControl && startNewControl === endNewControl
        //     && true === startNewControl.getStartBorderPortion().bPlaceHolder ) {
        //     return false;
        // }

        const startIndex = findLowerBound(this.newControls, startPos);
        const endIndex = findLowerBound(this.newControls, endPos);
        const count = this.newControls.length;

        for (let index = startIndex; index <= endIndex && index < count; index++) {
            const newControl = this.newControls[index];

            if ( newControl ) {
                result = newControl.isValidDelete(startPos, endPos, newControlNames);

                if ( false === result ) {
                    if ( null != newControlNames ) {
                        newControlNames.push(newControl.getNewControlName());
                    }
                    break;
                }
            }
        }

        return result;
    }

    public isValidDeleteSpecialNewCtrls( startPos: ParagraphContentPos, endPos: ParagraphContentPos,
                                         newControlNames?: string[] ): boolean {
        let result = true;

        const startIndex = findLowerBound(this.newControls, startPos);
        const endIndex = findLowerBound(this.newControls, endPos);
        const count = this.newControls.length;

        for (let index = startIndex; index <= endIndex && index < count; index++) {
            const newControl = this.newControls[index];

            if ( newControl ) {
                result = newControl.isValidDeleteSpecialNewCtrls(startPos, endPos, newControlNames);

                if ( false === result ) {
                    if ( null != newControlNames ) {
                        newControlNames.push(newControl.getNewControlName());
                    }
                    break;
                }
            }
        }

        return result;
    }

    public isContainNewControls(startContentPos: ParagraphContentPos, endContentPos: ParagraphContentPos): boolean {
        const newControlNames = this.getNewControlNamesByPos(startContentPos, endContentPos);

        if ( null != newControlNames && 0 < newControlNames.length ) {
            return true;
        }

        return false;
    }

    public isSelectionInNewControl( startPos: ParagraphContentPos, endPos: ParagraphContentPos ): boolean {
        const startIndex = findLowerBound(this.newControls, startPos);
        const endIndex = findLowerBound(this.newControls, endPos);

        if ( -1 < startIndex && startIndex === endIndex ) {
            return true;
        }

        return false;
    }

    public getSelectionInNewControl( startPos: ParagraphContentPos, endPos: ParagraphContentPos ): NewControl {
        const startIndex = findLowerBound(this.newControls, startPos);
        const endIndex = findLowerBound(this.newControls, endPos);

        if ( -1 < startIndex && startIndex === endIndex ) {
            const newControl = this.newControls[endIndex];
            if ( newControl ) {
                if ( -1 === startPos.compare(newControl.getStartPos())
                    || 1 === endPos.compare(newControl.getEndPos()) ) {
                    return null;
                }

                const leafList = newControl.getLeafList();

                if ( leafList ) {
                    const startLeafControl = newControl.getPosNewControl(startPos);
                    const endLeafControl = newControl.getPosNewControl(endPos);
                    const behindOverNewControls = this.getBehindOverNewControls(startPos, endPos);
                    const forwardOverNewControls = this.getForwardOverNewControls(startPos, endPos);
                    const containNewControls = this.getNewControlNamesByPos(startPos, endPos);

                    if ( containNewControls && 0 < containNewControls.length ) {
                        for (const name of containNewControls) {
                            const element = this.getNewControlByName(name);
                            if ( element && false === element.isReverseEdit() ) {
                                return element;
                            }
                        }
                    }

                    if ( startLeafControl === endLeafControl ) {
                        return startLeafControl;
                    } else {
                        if ( false === startLeafControl.isReverseEdit() ) {
                            return startLeafControl;
                        } else if ( false === endLeafControl.isReverseEdit() ) {
                            return endLeafControl;
                        }

                        if ( behindOverNewControls && 0 < behindOverNewControls.length ) {
                            for (const element of behindOverNewControls) {
                                if ( element && false === element.isReverseEdit() ) {
                                    return element;
                                }
                            }
                        }

                        if ( forwardOverNewControls && 0 < forwardOverNewControls.length ) {
                            for (const element of forwardOverNewControls) {
                                if ( element && false === element.isReverseEdit() ) {
                                    return element;
                                }
                            }
                        }
                    }
                }

                return newControl;
            }
        }

        return null;
    }

    /**
     * 获取包含前边框的newControls(只包含前边框的newcontrols, 未关闭)
     * @param startPos
     * @param endPos
     */
    public getForwardOverNewControls( startPos: ParagraphContentPos, endPos: ParagraphContentPos ): NewControl[] {
        const newControls: NewControl[] = [];
        const startIndex = findLowerBound(this.newControls, startPos);
        const endIndex = findLowerBound(this.newControls, endPos);

        if ( -1 < endIndex ) {
            const newControl = this.newControls[endIndex];

            let innerNewControl = this.getPosNewControl(endPos);
            const startNewControl = this.getPosNewControl(startPos);

            if ( startIndex !== endIndex ) {
                while ( innerNewControl && innerNewControl !== newControl ) {
                    newControls.push(innerNewControl);
                    innerNewControl = innerNewControl.getParent();
                }

                if ( true === newControl.isCursorInNewControl(endPos)
                    || ( 1 !== startPos.compare(newControl.getStartPos())
                            && -1 === endPos.compare(newControl.getEndPos()))  ) {
                    newControls.push(newControl);
                }
            } else {
                if ( innerNewControl !== startNewControl ) {
                    while ( innerNewControl && innerNewControl !== newControl
                            && 1 !== startPos.compare(innerNewControl.getStartPos()) ) {
                        newControls.push(innerNewControl);
                        innerNewControl = innerNewControl.getParent();
                    }
                }
            }
        }

        return newControls;
    }

    /**
     * 获取包含后边框的newControls(只包含后边框的newcontrols, 未关闭)
     * @param startPos
     * @param endPos
     */
    public getBehindOverNewControls( startPos: ParagraphContentPos, endPos: ParagraphContentPos ): NewControl[] {
        const newControls: NewControl[] = [];
        const startIndex = findLowerBound(this.newControls, startPos);
        const endIndex = findLowerBound(this.newControls, endPos);

        if ( -1 < startIndex ) {
            const newControl = this.newControls[startIndex];
            let innerNewControl = this.getPosNewControl(startPos);
            const endNewControl = this.getPosNewControl(endPos);

            if ( startIndex !== endIndex ) {
                while ( innerNewControl && innerNewControl !== newControl ) {
                    newControls.push(innerNewControl);
                    innerNewControl = innerNewControl.getParent();
                }

                if ( true === newControl.isCursorInNewControl(startPos)
                    || ( startIndex === endIndex && 1 !== startPos.compare(newControl.getEndPos())
                                && 1 === endPos.compare(newControl.getEndPos()) ) ) {
                    newControls.push(newControl);
                }
            } else {
                if ( innerNewControl !== endNewControl ) {
                    while ( innerNewControl && 1 === endPos.compare(innerNewControl.getEndPos()) ) {
                        newControls.push(innerNewControl);
                        innerNewControl = innerNewControl.getParent();
                    }

                }
            }
        }

        return newControls;
    }

    /**
     * 设置newcontrol的内容
     * @param sName
     * @param sText
     */
    public setNewControlText( sName: string, sText: string ): number {
        let result = ResultType.Failure;
        const newControl = this.newControlNames.get(sName);
        if ( null != newControl ) {
            result = newControl.setNewControlText(sText);

            if ( ResultType.Success === result && newControl.isPlaceHolderContent() ) {
                // combox and addressbox
                newControl.resetSelectItems();
            }
        }

        return result;
    }

    public getNewControlText( sName: string ): string {
        let sText = '';
        const newControl = this.newControlNames.get(sName);
        const isSection = newControl?.isNewSection();
        if ( null != newControl && !isSection ) {
            sText = newControl.getNewControlText();
        }
        if (isSection) {
            sText = this.getSectionText(newControl);
        }
        return sText;
    }

    // 处理节文本的函数
    public getSectionText(newControl: NewControl): string {
        // 如果节为空，返回空字符串
        if (!newControl) {
            return ResultType.StringEmpty;
        }
        
        // 获取节的内容
        const content = newControl.getNewControlContent();
        if (!content) {
            return ResultType.StringEmpty;
        }
        
        // 检查节元素是否只包含占位符
        if (content.hasOnlyPlaceHolderContent()) {
            return ResultType.StringEmpty;
        }
        
        // 获取节内所有段落的文本
        const startPara = content.getStartBorderPortion().paragraph;
        const endPara = content.getEndBorderPortion().paragraph;
        const startParaIndex = startPara.index;
        const endParaIndex = endPara.index;
        const startPortionIndex = startPara.getPortionIndexById(content.getStartBorderPortion().id);
        const endPortionIndex = endPara.getPortionIndexById(content.getEndBorderPortion().id);
        
        // 处理节内文本
        let sText = '';
        
        // 处理节内第一个段落
        if (startPara && startPortionIndex !== null) {
            sText = startPara.getNewControlText(startPortionIndex, 
                startParaIndex === endParaIndex ? endPortionIndex : null, false, true);
        }
        
        // 处理节内中间段落
        const parent = content.getParent();
        if (parent && startParaIndex < endParaIndex) {
            for (let index = startParaIndex + 1; index < endParaIndex; index++) {
                const element = parent.content[index];
                if (element && element.isParagraph()) {
                    sText += '\n';
                    sText += element.getNewControlText(null, null, false, true);
                }
            }
        }
        
        // 处理节内最后一个段落
        if (endPara && endPortionIndex !== null && startParaIndex !== endParaIndex) {
            sText += '\n';
            sText += endPara.getNewControlText(null, endPortionIndex, false, true);
        }
        
        // 遍历节内的所有子元素，检查是否有子元素只包含占位符
        const leafList = newControl.getLeafList();
        if (leafList && leafList.length > 0) {
            for (const leaf of leafList) {
                // 检查子元素是否隐藏
                if (leaf && leaf.isHidden()) {
                    // 如果子元素是隐藏的，从文本中移除该子元素的内容
                    const leafText = leaf.getNewControlText();
                    if (leafText) {
                        sText = sText.replace(leafText, '');
                    }
                }
                // 原有逻辑：检查子元素是否只包含占位符
                else if (leaf && leaf.isPlaceHolderContent()) {
                    // 如果子元素只包含占位符，获取其完整的显示文本进行精确移除
                    const leafText = leaf.getNewControlText();
                    if (leafText) {
                        // 使用完整的显示文本进行替换，确保精确移除占位符内容
                        sText = sText.replace(leafText, '');
                    }
                }
            }
        }
        
        return sText;
    }

    public getNewControlTextAI( sName: string ): string {
        let sText = '';
        const newControl = this.newControlNames.get(sName);
        if ( null != newControl ) {
            sText = newControl.getNewControlTextAI();
        }

        return sText;
    }

    /**
     * 设置多个newcontrols的内容
     * @param sNameList newControl列表
     * @param sTextList 内容列表
     */
    public setNewControlsText( texts: Map<string, string[]> ): number {
        let result = ResultType.Failure;

        for (const [name, contents] of texts.entries() ) {
            const newControl = this.newControlNames.get(name);

            if ( null != newControl ) {
                result = newControl.setNewControlText(filterChars(contents[0]));
            }
        }

        return result;
    }

    public getDoc(): Document {
        return this.parent;
    }

    /**
     * 根据json内容设置text值
     * @param json 设置的结构化元素的条件
     * @param pos 收集结构化元素所处的位置
     * @returns 返回值
     */
    public setNewControlsText3(json: IStructParamJson[], pos: number[]): number {
        let result = ResultType.UnEdited;
        if (!json || json.length === 0) {
            return result;
        }

        const newControls: NewControl[] = [];
        const names = [];
        const texts = new Map<string, IStructParamJson>();
        json.forEach((data) => {
            if (!data.name) {
                return;
            }
            names.push(data.name);
            texts.set(data.name, data);
        });

        if (names.length === 0) {
            return ResultType.UnEdited;
        }

        const monitor = this.parent?.getElementMonitor();
        const bAddAction = monitor?.canAddAction(MonitorAction.Insert);
        const startTime = getCurTime();
        let insertContent = '';

        this.getSortNewControls(newControls, json.map((data) => data.name));
        newControls.forEach((newControl) => {
            const item = texts.get(newControl.getNewControlName());
            const text = item.content_text;
            insertContent = '';
            if (newControl.isNumberBox()) {
                const tempRes = (newControl as any).setNewControlTextByJson(item);
                result = tempRes && result;

                if (ResultType.Success === tempRes) {
                    insertContent = item.content_text;
                }
            } else if (text !== undefined && typeof text === 'string') {
                const tempRes = newControl.setNewControlText(text, false);
                result = tempRes && result;

                if (ResultType.Success === tempRes) {
                    insertContent = item.content_text;
                }
            } else {
                result = newControl.setNewControlTextByJson(item) && result;
            }

            if (insertContent && bAddAction) {
                const element = monitor.getMonitorElement(newControl);
                if (element) {
                    monitor.addAction(element.getSerialNumber(), {start: startTime,
                        end: getCurTime(),
                        type: MonitorAction.Insert, insertContent});
                }
            }

            pos.push(newControl.getDocumentSectionType());
        });

        return result;
    }

    public setNewControlsText2( texts: Map<string, string[]> , pos: number[]): number {
        let result = ResultType.Failure;
        const newControls: NewControl[] = [];
        this.getSortNewControls(newControls, Array.from(texts.keys()));

        const monitor = this.parent?.getElementMonitor();
        const bAddAction = monitor?.canAddAction(MonitorAction.Insert);
        const startTime = getCurTime();

        newControls.forEach((newControl) => {
            let contents = texts.get(newControl.getNewControlName());
            if (!contents) {
                contents = [''];
            }
            pos.push(newControl.getDocumentSectionType());
            newControl.setNewControlText(contents[0], false);

            if (contents[0] && bAddAction) {
                const element = monitor.getMonitorElement(newControl);
                if (element) {
                    monitor.addAction(element.getSerialNumber(), {start: startTime,
                        end: getCurTime(),
                        type: MonitorAction.Insert, insertContent: contents[0]});
                }
            }
            result = ResultType.Success;
        });
        // for (const [name, contents] of texts.entries() ) {
        //     const newControl = this.newControlNames.get(name);

        //     if ( null != newControl ) {
        //         result = newControl.setNewControlText(filterChars(contents[0]));
        //     }
        // }

        return result;
    }

    /**
     * 按结构化的物理顺序获取结构化元素集合(返回的数据为倒序)
     * @param results 返回结果
     * @param names 结构化话元素名称
     */
    public getSortNewControls(results: NewControl[], names: string[], bParent?: boolean): void {
        const datas = this.newControls;
        for (let index = 0, len = datas.length; index < len; index++) {
            if (names.length === 0) {
                break;
            }

            datas[index].filterByName(results, names, bParent);
        }
    }

    /**
     * 设置多个newcontrols的属性
     * @param propertys
     */
    public setNewControlsProp( propertys: INewControlProperty[] ): number {
        // let result = ResultType.Success;
        let result = ResultType.UnEdited;
        const buttonManager = this.buttonManager;
        for (let index = 0, count = propertys.length; index < count; index++) {
            const props = propertys[index];
            const newControlName = props.newControlName;
            
            if ( null != newControlName ) {
                const newControl = this.newControlNames.get(newControlName);
                if ( null != newControl ) {
                    result = newControl.setProperty(props) && result;
                } else if (buttonManager && props._bInterface === true) {
                    const button = buttonManager.getButtonByName(newControlName);
                    if (button) {
                        result = button.setButtonProps({bPrint: props.printSelected, content: undefined}) && result;
                    }
                }
            }
        }

        return result;
    }

    public setNewControlListItems(sName: string, pos: any[]): number {
        let result = ResultType.Failure;
        const newControl = this.newControlNames.get(sName);
        if ( null != newControl ) {
            result = newControl.setNewControlListItems(pos);
        }

        return result;
    }

    public getPasteNewControlByNames(): string[] {
        const newControls = this.pasteNewControls;
        if (!newControls || newControls.length === 0) {
            return;
        }

        return newControls.map((newControl) => newControl.control.getNewControlName());
    }

    public addNewControlInMapOnly(newControl: NewControl): void {
        this.newControlNames.set(newControl.getNewControlName(), newControl);
    }

    public removeNewControlInMapOnly(newControl: NewControl): void {
        this.newControlNames.delete(newControl.getNewControlName());
    }

    public addNewControlInMap(newControl: NewControl): void {
        if ( !newControl ) {
            return ;
        }

        const newControlName = newControl.getNewControlName();

        if ( this.newControlNames.has(newControlName) ) {
            return ;
        }

        const history = this.getHistory();
        if ( history ) {
            history.addChange(new ChangeNewControlMapAddItem(newControl));
        }
        this.newControlNames.set(newControlName, newControl);
    }

    // 调整选择区域内newControl的排序
    public updateNewControlPos(/*startPos: ParagraphContentPos, endPos: ParagraphContentPos*/): void {
        // const startIndex = findLowerBound(this.newControls, startPos);
        // const endIndex = findLowerBound(this.newControls, endPos);

        // if ( endIndex > startIndex + 1 ) {
        //     const startPos = this.newControls[startIndex + 1].getStartPos();
        // }

        // for (let index = startIndex + 1; index < endIndex && index < this.newControls.length; index++) {
        //     const pos = this.newControls[index].getStartPos();

        //     for (let index2 = index + 1; index2 <= endIndex && index < this.newControls.length; index2++) {
        //         const pos2 = this.newControls[index2].getStartPos();

        //         if ( 1 === pos.compare(pos2) ) {
        //             this.newControls.sort();
        //         }
        //     }
        // }
        this.newControls.sort(this.compareNewControlPos);
    }

    public getHistory(): History {
        return this.parent ? this.parent.getHistory() : null;
    }

    public addContentChanges( changes: ContentChangesElement ): void {
        this.contentChanges.add(changes);
    }

    public getNewControlsByGroup(group: string): NewControl[] {
        const result = [];
        this.newControlNames.forEach((value) => {
            if ( value.isCheckBox() && group === value.getGroup() ) {
                result.push(value);
            }
        });

        return result;
    }

    public getNewControlNamesByGroup(group: string): string[] {
        const result: string[] = [];
        this.newControlNames.forEach((value, key) => {
            if ( value.isCheckBox() && group === value.getGroup() ) {
                result.push(key);
            }
        });

        return result;
    }

    public getCheckboxsByNames(names: string[]): NewControl[] {
        const result: NewControl[] = [];
        this.newControlNames.forEach((value) => {
            if ( value.isCheckBox() && names.indexOf(value.getNewControlName()) !== -1 ) {
                result.push(value);
            }
        });
        return result;
    }

    public getAllGroupCheckboxName(): string {
        const result = [];
        this.newControlNames.forEach((value) => {
            if ( value.isCheckBox() && value.getGroup() && !result.includes(value.getGroup()) ) {
                result.push(value.getGroup());
            }
        });

        return result.toString();
    }

    public getGroupCheckboxStatus(sGroupName: string): number {
        let result = -1;
        const newControls = this.getNewControlsByGroup(sGroupName);

        if (newControls && newControls.length) {
            for (let index = 0, length = newControls.length; index < length; index++) {
                const item = newControls[index];
                if (item && item.isCheckBox()) {
                    result = (item as NewControlCheck).isSelected() ? 1 : 0;

                    if (1 === result) {
                        break;
                    }
                }
            }
        }

        return result;
    }

    public getLastNewControl(newControls: NewControl[]): NewControl {
        let result: NewControl;
        if (newControls && newControls.length) {
            result = newControls[0];

            for (let index = 1, length = newControls.length; index < length; index++) {
                const item = newControls[index];
                const startBorderPos = item.getStartPos();

                if ( 1 === startBorderPos.compare(result.getStartPos()) ) {
                    result = item;
                }
            }
        }

        return result;
    }

    public triggerGroup(newControl: NewControl): boolean {
        const group = (newControl && newControl.isCheckBox() ? newControl.getGroup() : null);
        if ( group ) {
            const newControls = this.getNewControlsByGroup(group);
            if ( newControls && 1 < newControls.length ) {
                const bSelected = (newControl as NewControlCheck).isSelected();
                newControls.forEach((value) => {
                    if ( value !== newControl ) {
                        this.addCascadeMap(newControl, (!bSelected).toString());
                        if (bSelected !== false) {
                            (value as NewControlCheck).setCheckBoxChecked2(!bSelected);
                        }
                    }
                });
                this.triggerCascadeMap();
                return true;
            }
        }

        return false;
    }

    public getNewControlContainName(subName: string): string[] {
        const newControlNames: string[] = [];
        this.newControlNames.forEach((newControl, name) => {
            if ( name.includes(subName) ) {
                newControlNames.push(name);
            }
        });

        return newControlNames;
    }

    // public getSourceBindMap(): Map<string, any> {
    //     return this.sourceBindMap;
    // }

    public addSourceData(name: string, data: IExternalDataProperty): void {
        // let sourceBindMap = this.parent.getSourceBindMap();
        // if (data) {
        //     if (!sourceBindMap) {
        //         sourceBindMap = new Map();
        //     }

        //     const sourceObj = data.sourceObj;
        //     const sourceKey = data.sourceKey;
        //     const commandUpdate = data.commandUpdate;
        //     const bReadOnly = data.bReadOnly;
        //     if (sourceBindMap.has(sourceObj)) {
        //         const item = sourceBindMap.get(sourceObj);
        //         const index = item.findIndex((value) => {
        //             return name === value.name;
        //         });

        //         if (-1 !== index) {
        //             item[index].sourceKey = data.sourceKey;
        //             item[index].commandUpdate = data.commandUpdate;
        //             item[index].bReadOnly = data.bReadOnly;
        //         } else {
        //             item.push({
        //                 name,
        //                 sourceKey,
        //                 commandUpdate,
        //                 bReadOnly,
        //             });
        //         }
        //     } else {
        //         sourceBindMap.set(sourceObj, [{
        //             name,
        //             sourceKey,
        //             commandUpdate,
        //             bReadOnly,
        //         }]);
        //     }
        // }
        this.parent.addSourceData(name, data);
    }

    public removeSourceData(newControl: NewControl, sourceObj?: string): number {
        // const sourceBindMap = this.parent.getSourceBindMap();
        const data = newControl.getSourceDataBind();
        if (data) {
            return this.parent.removeSourceData(newControl.getNewControlName(), data.sourceObj);
        }
        // if (data && sourceBindMap) {
        //     const _sourceObj = sourceObj ? sourceObj : data.sourceObj;

        //     if (sourceBindMap.has(_sourceObj)) {
        //         const item = sourceBindMap.get(_sourceObj);
        //         const index = item.findIndex((value) => {
        //             return newControl.getNewControlName() === value.name;
        //         });

        //         if (-1 !== index) {
        //             item.splice(index, 1);
        //         }

        //         return ResultType.Success;
        //     }
        // }

        return ResultType.UnEdited;
    }

    /**
     * 级联是否能触发进行判断
     * @param data 当前级联配置元素
     * @param value 触发源结构化元素值
     * @param oldControl 触发源的结构化元素
     * @param bRefresh 是否触发排版更新
     * @param curText 当前结构化时间框文本？
     * @returns bool
     */
    private isTrigger(data: ICascade, value: string, oldControl: NewControl, bRefresh: boolean = true,
                      curText?: string): boolean {
        let bUnValid: boolean = false; // 是否超出规则
        // 如果是target是当前元素，则进行删除
        if (data.target === oldControl?.getNewControlName()) {
            return false;
        }
        if (
            oldControl?.isNumberBox() &&
            ![
                CascadeTriggerCondition.Range,
                CascadeTriggerCondition.Empty,
            ].includes(data.logic)
        ) {
            const numNewControl = this.getNewControlByName(data.logicText);
            if (numNewControl && numNewControl.isNumberBox()) {
                // tslint:disable-next-line: max-line-length
                data.logicText = numNewControl.getNewControlText();
            }
        }
        if (data.action !== CascadeActionType.SyncText) {
            switch (data.logic) {
                case CascadeTriggerCondition.Selected:
                case CascadeTriggerCondition.Equal: {
                    const bNewDateBox = (oldControl && oldControl.isNewDateBox());
                    const text = data.logicText;
                    if (value !== text && !bNewDateBox) {
                        if (
                            oldControl?.isNumberBox() &&
                            (Math.abs(Number.parseFloat(value) - Number.parseFloat(text)) <= 0.000001)
                        ) {
                            break;
                        }
                        bUnValid = true;
                    } else if (bNewDateBox && value !== 'true') {
                        bUnValid = true;
                    }
                    break;
                }
                case CascadeTriggerCondition.Includes: {
                    if (oldControl.isNewTextBox()) {
                        // 文本框的包含判断
                        if (!value || (typeof value === 'string' && !value.includes(data.logicText))) {
                            bUnValid = true;
                        }
                    } else {
                        // 多选控件的包含判断
                        if (!value || !oldControl.isContainSelected(data.logicText)) {
                            bUnValid = true;
                        }
                    }
                    break;
                }
                case CascadeTriggerCondition.UnIncludes: {
                    if (oldControl.isNewTextBox()) {
                        // 文本框的不包含判断
                        if (!value || (typeof value === 'string' && value.includes(data.logicText))) {
                            bUnValid = true;
                        }
                    } else {
                        // 多选控件的不包含判断
                        if (value && oldControl.isContainSelected(data.logicText)) {
                            bUnValid = true;
                        }
                    }
                    break;
                }
                case CascadeTriggerCondition.NoSelect: {
                    // 目前只有选择按钮有“未选中”级联
                    if (!oldControl.isMultiAndRadio() ||
                        (oldControl instanceof NewControlRadio && !!oldControl.getSelected())) {
                        bUnValid = true;
                    }
                    break;
                }
                case CascadeTriggerCondition.UnSelected:
                case CascadeTriggerCondition.Neq: {
                    const bNewDateBox = (oldControl && oldControl.isNewDateBox());
                    const text = data.logicText;
                    if (value === text && !bNewDateBox) {
                        bUnValid = true;
                    } else if (oldControl?.isNumberBox()) {
                        if (Number.parseFloat(value) === Number.parseFloat(text)) {
                            bUnValid = true;
                        }
                    } else if (bNewDateBox && value === 'true') {
                        bUnValid = true;
                    }
                    break;
                }
                case CascadeTriggerCondition.Less: {
                    if (!value) {
                        return false;
                    }
                    const num = parseFloat(value);
                    if (isNaN(num) || num >= parseFloat(data.logicText)) {
                        bUnValid = true;
                    }
                    break;
                }
                case CascadeTriggerCondition.Larger: {
                    if (!value) {
                        return false;
                    }
                    const num = parseFloat(value);
                    if (isNaN(num) || num <= parseFloat(data.logicText)) {
                        bUnValid = true;
                    }
                    break;
                }
                case CascadeTriggerCondition.LEQ: { // 小于等于
                    if (!value) {
                        return false;
                    }
                    const num = parseFloat(value);
                    if (isNaN(num) || num > parseFloat(data.logicText)) {
                        bUnValid = true;
                    }
                    break;
                }
                case CascadeTriggerCondition.GEQ: { // 大于等于
                    if (!value) {
                        return false;
                    }
                    const num = parseFloat(value);
                    if (isNaN(num) || num < parseFloat(data.logicText)) {
                        bUnValid = true;
                    }
                    break;
                }
                case CascadeTriggerCondition.Range: {
                    if (!value) {
                        return false;
                    }
                    const reg = /\[(\d+(\.\d+)?)\s*,\s*(\d+(\.\d+)?)\]/;
                    const match = reg.exec(data.logicText);
                    if (!match || !match[1] || !match[3]) {
                        return false;
                    }
                    const arr1 = parseFloat(match[1]);
                    const arr2 = parseFloat(match[3]);
                    const arr3 = parseFloat(value);
                    if (arr1 > arr3 || arr2 < arr3) {
                        bUnValid = true;
                    }
                    break;
                }
                case CascadeTriggerCondition.Empty: {
                    const controlType = oldControl.getType();
                    const bValidType = oldControl && [NewControlType.TextBox, NewControlType.Combox,
                        NewControlType.MultiCombox, NewControlType.ListBox,
                        NewControlType.MultiListBox, NewControlType.NumberBox].includes(controlType);
                    if (!bValidType || !(!value || oldControl.isPlaceHolderContent())) {
                        bUnValid = true;
                    }
                    break;
                }
                case CascadeTriggerCondition.UnEmpty: {
                    const bValidType = oldControl && [NewControlType.TextBox, NewControlType.Combox,
                        NewControlType.MultiCombox, NewControlType.ListBox,
                        NewControlType.MultiListBox, NewControlType.NumberBox].includes(oldControl.getType());
                    if (!bValidType || (!value || oldControl.isPlaceHolderContent())) {
                        bUnValid = true;
                    }
                    break;
                }
                case CascadeTriggerCondition.DateCompare: {
                    switch (data.logicText) {
                        case CascadeTriggerCondition.Less: {
                            if (+value < +curText) {
                                return true;
                            }
                            break;
                        }
                        case CascadeTriggerCondition.LEQ: {
                            if (+value <= +curText) {
                                return true;
                            }
                            break;
                        }
                        case CascadeTriggerCondition.Larger: {
                            if (+value > +curText) {
                                return true;
                            }
                            break;
                        }
                        case CascadeTriggerCondition.GEQ: {
                            if (+value >= +curText) {
                                return true;
                            }
                            break;
                        }
                        case CascadeTriggerCondition.Equal: {
                            if (value === curText) {
                                return true;
                            }
                            break;
                        }
                    }
                    return false;
                }
            }
        }

        return !bUnValid;
    }

    /**
     * 当前结构化触发函数
     * @param data 当前级联配置元素
     * @param value 触发源结构化元素值
     * @param oldControl 触发源结构化元素
     * @param bRefresh 是否更新
     * @returns bool
     */
    private trigger(data: ICascade, value: string, oldControl: NewControl, bRefresh: boolean = true): number {
        let bUnValid: boolean = false;
        if (data.action !== CascadeActionType.SyncText) {
            switch (data.logic) {
                case CascadeTriggerCondition.Selected:
                case CascadeTriggerCondition.Equal: {
                    const bNewDateBox = (oldControl && oldControl.isNewDateBox());
                    const bNewNumberBox = (oldControl && oldControl.isNumberBox());
                    if (value !== data.logicText && !bNewDateBox && !bNewNumberBox) {
                        bUnValid = true;
                    } else if (bNewDateBox && value !== 'true') {
                        bUnValid = true;
                    } else if (bNewNumberBox) {
                        const numLogicText = data.logicText;
                        if (numLogicText?.toLowerCase() !== 'null') {
                            if (value !== numLogicText) {
                                bUnValid = true;
                            }
                        } else {
                            // null case
                            if (value !== '') {
                                bUnValid = true;
                            }
                        }
                    }
                    break;
                }
                case CascadeTriggerCondition.Includes: {
                    if (oldControl.isNewTextBox()) {
                        // 文本框的包含判断
                        if (!value || (typeof value === 'string' && !value.includes(data.logicText))) {
                            bUnValid = true;
                        }
                    } else {
                        // 多选控件的包含判断
                        if (!value || !oldControl.isContainSelected(data.logicText)) {
                            bUnValid = true;
                        }
                    }
                    break;
                }
                case CascadeTriggerCondition.UnIncludes: {
                    if (oldControl.isNewTextBox()) {
                        // 文本框的不包含判断
                        if (!value || (typeof value === 'string' && value.includes(data.logicText))) {
                            bUnValid = true;
                        }
                    } else {
                        // 多选控件的不包含判断
                        if (value && oldControl.isContainSelected(data.logicText)) {
                            bUnValid = true;
                        }
                    }
                    break;
                }

                case CascadeTriggerCondition.UnSelected:
                case CascadeTriggerCondition.Neq: {
                    const bNewDateBox = (oldControl && oldControl.isNewDateBox());
                    const bNewNumberBox = (oldControl && oldControl.isNumberBox());
                    if (value === data.logicText && !bNewDateBox) {
                        bUnValid = true;
                    } else if (bNewDateBox && value === 'true') {
                        bUnValid = true;
                    } else if (bNewNumberBox) {
                        const numLogicText = data.logicText;
                        if (numLogicText?.toLowerCase() === 'null') {
                            // null case
                            if (value === '') {
                                bUnValid = true;
                            }
                        } else {
                            if (value === numLogicText) {
                                bUnValid = true;
                            }
                        }
                    }
                    break;
                }
                case CascadeTriggerCondition.Less: {
                    if (!value) {
                        return ResultType.Failure;
                    }
                    const num = parseFloat(value);
                    if (isNaN(num) || num >= parseFloat(data.logicText)) {
                        bUnValid = true;
                    }
                    break;
                }
                case CascadeTriggerCondition.Larger: {
                    if (!value) {
                        return ResultType.Failure;
                    }
                    const num = parseFloat(value);
                    if (isNaN(num) || num <= parseFloat(data.logicText)) {
                        bUnValid = true;
                    }
                    break;
                }
                case CascadeTriggerCondition.LEQ: { // 小于等于
                    if (!value) {
                        return ResultType.Failure;
                    }
                    const num = parseFloat(value);
                    if (isNaN(num) || num > parseFloat(data.logicText)) {
                        bUnValid = true;
                    }
                    break;
                }
                case CascadeTriggerCondition.GEQ: { // 大于等于
                    if (!value) {
                        return ResultType.Failure;
                    }
                    const num = parseFloat(value);
                    if (isNaN(num) || num < parseFloat(data.logicText)) {
                        bUnValid = true;
                    }
                    break;
                }
                case CascadeTriggerCondition.Range: {
                    if (!value) {
                        return ResultType.Failure;
                    }
                    const reg = /\[(\d+(\.\d+)?)\s*,\s*(\d+(\.\d+)?)\]/;
                    const match = reg.exec(data.logicText);
                    if (!match || !match[1] || !match[3]) {
                        return ResultType.Failure;
                    }
                    const arr1 = parseFloat(match[1]);
                    const arr2 = parseFloat(match[3]);
                    const arr3 = parseFloat(value);
                    if (arr1 > arr3 || arr2 < arr3) {
                        bUnValid = true;
                    }
                    break;
                }
                case CascadeTriggerCondition.Empty: {
                    const bValidType = oldControl && [NewControlType.TextBox, NewControlType.Combox,
                        NewControlType.MultiCombox, NewControlType.ListBox,
                        NewControlType.MultiListBox, NewControlType.NumberBox].includes(oldControl.getType());
                    if (!bValidType || !(!value || oldControl.isPlaceHolderContent())) {
                        bUnValid = true;
                    }
                    break;
                }
                case CascadeTriggerCondition.UnEmpty: {
                    const bValidType = oldControl && [NewControlType.TextBox, NewControlType.Combox,
                        NewControlType.MultiCombox, NewControlType.ListBox,
                        NewControlType.MultiListBox, NewControlType.NumberBox].includes(oldControl.getType());
                    if (!bValidType || (!value || oldControl.isPlaceHolderContent())) {
                        bUnValid = true;
                    }
                    break;
                }
            }
        }

        if (bUnValid) {
            return ResultType.Failure;
        }

        const target = data.target;

        let result = ResultType.Failure;
        const names = target.split(',');
        names.forEach((name) => {
            let node: any;
            let type: number = 0; // looks like struct or region
            node = this.getNewControlByName(name);
            if (!node) {
                node = this.getRegionByName(name);
                type = 1;
            }
            if (!node) {
                return ResultType.Failure;
            }
            result = this.triggerAction(data, value, node, type, bRefresh, oldControl) && result;
        });
        return result;
    }

    private triggerAction(data: ICascade, value: string, node: any, type: number, bRefresh: boolean,
                          oldControl?: NewControl): number {
        let result: number = ResultType.UnEdited;
        switch (data.action) {
            case CascadeActionType.Show: {
                result = node.setHidden(false, false);
                break;
            }
            case CascadeActionType.Hidden: {
                result = node.setHidden(true, false);
                break;
            }
            case CascadeActionType.Edit: {
                result = node.setEditProtect(false);
                break;
            }
            case CascadeActionType.UnEdit: {
                result = node.setEditProtect(true);
                break;
            }
            case CascadeActionType.SyncText: {
                if (type === 0) {
                    if (node.isNewDateBox() && oldControl && oldControl.isNewDateBox()) {
                        const dateTime = oldControl.getDateTime();
                        if (!dateTime) {
                            result = node.resetDateBoxContent();
                        } else {
                            result = node.setNewControlText(dateTime, false);
                        }
                    } else if (node.isNumberBox()) {
                        if (oldControl && oldControl.isNumberBox() && !value) {
                            result = node.setNewControlText(value, bRefresh);
                        } else {
                            result = node.setNumberBoxText(+value, bRefresh);
                        }
                        result = result === 0 ? ResultType.Success : ResultType.UnEdited;
                    } else {
                        result = node.setNewControlText(value, bRefresh);
                    }
                } else {
                    result = node.setRegionText(value, bRefresh);
                }
                break;
            }
            case CascadeActionType.SetText: {
                if (type === 0) {
                    // structs?
                    let candidateText = data.actionText;
                    let bPlaceHolder = false;
                    if (oldControl != null && oldControl.isNumberBox() && data.actionText?.toLowerCase() === 'null') {
                        candidateText = '';
                        bPlaceHolder = true;
                    }
                    if (node.isNumberBox() && !bPlaceHolder) {
                        result = node.setNumberBoxText(+candidateText, bRefresh);
                        result = result === 0 ? ResultType.Success : ResultType.UnEdited;
                    } else {
                        result = node.setNewControlText(candidateText, bRefresh);
                    }
                } else {
                    result = node.setRegionText(data.actionText, bRefresh);
                }
                break;
            }
            case CascadeActionType.Checked: {
                if (node instanceof NewControlCheck) {
                    result = node.setCheckBoxChecked2(true);
                }
                break;
            }
            case CascadeActionType.Unchecked: {
                if (node instanceof NewControlCheck) {
                    result = node.setCheckBoxChecked2(false);
                }
                break;
            }
            case CascadeActionType.SetMustItem:
            case CascadeActionType.UnsetMustItem: {
                if (type === 0 && !(node.isCheckBox() || node.isSignatureBox())) {
                    result = node.setMustInput(CascadeActionType.SetMustItem === data.action);
                }
                break;
            }
            case CascadeActionType.WarnTip: {
                result = node.setWarnTip(data.actionText, value);
                break;
            }
        }

        return result;
    }

    private deleteContent(startPortionIndex: number, endPortionIndex: number, startPara: any, endPara: any,
                          newControl: NewControl): boolean {
        let bUpdate: boolean = true;
        if (startPara === endPara) {
            startPara.contentRemove2(startPortionIndex, endPortionIndex - startPortionIndex + 1);
        } else {
            const startIndex = startPara.index;
            const endIndex = endPara.index;
            startPara.contentRemove2(startPortionIndex, startPara.content.length - startPortionIndex);
            const endParaPortions = endPara.getContent()
            .slice(endPortionIndex + 1);
            startPara.contentConcat(endParaPortions);
            const parent = startPara.parent;
            parent.curPos.contentPos = startIndex;
            startPara.curPos.contentPos = startPortionIndex;
            let index = 1;
            const parentContents = parent.content;
            if (newControl.isNewSection() && parentContents.length !== 1 && startPara.isEmpty()) {
                bUpdate = false;
                index--;
            }
            parent.contentRemove(startIndex + index, endIndex - startIndex + 1 - index);
            if (parentContents.length <= startIndex) {
                parent.curPos.contentPos = startIndex - 1;
            }
        }
        return bUpdate;
    }

    private getRegionByName(name: string): any {
        return this.parent.getRegionByName(name);
    }

    private compareNewControlPos(newControl1: NewControl, newControl2: NewControl): number {
        const pos1 = newControl1.getStartPos();
        const pos2 = newControl2.getStartPos();

        return pos1.compare(pos2);
    }

    /**
     * 查找下一个可跳转的结构化元素
     * @param newControl 当前结构化元素
     */
    private getNextTabJumpNewControl(newControl: NewControl): NewControl {
        const newControls = this.newControls;
        let currentNewControl = this.findNextTabJumpNewControl(newControl);
        if (currentNewControl && !currentNewControl.isHidden()) {
            return currentNewControl;
        }
        let parent: NewControl = newControl.getParent();
        // let bFirst: boolean = false;
        if (parent) {
            currentNewControl = this.findNextTabJumpNewControl2(parent, newControl);
            if (currentNewControl && !currentNewControl.isHidden()) {
                return currentNewControl;
            }
            while (true) {
                const currentParent = parent.getParent();
                if (currentParent) {
                    // let actNewControl = parent;
                    // if (bFirst === false) {
                    //     bFirst = true;
                    //     actNewControl = newControl;
                    // }
                    currentNewControl = this.findNextTabJumpNewControl2(currentParent, parent);
                    if (currentNewControl && !currentNewControl.isHidden()) {
                        return currentNewControl;
                    }
                    parent = currentParent;
                } else {
                    break;
                }
            }
        } else {
            parent = newControl;
        }

        let index = this.newControls.findIndex((ctrl) => ctrl === parent);
        if (index === -1) {
            return;
        }

        index++;
        for (const len = newControls.length; index < len; index++) {
            currentNewControl = newControls[index];
            if (currentNewControl.isHidden()) {
                continue;
            }
            if (currentNewControl.isTabJump()) {
                return currentNewControl;
            }
            currentNewControl = this.findNextTabJumpNewControl(currentNewControl);
            if (currentNewControl && !currentNewControl.isHidden()) {
                return currentNewControl;
            }
        }
        return;
    }

    /**
     * 兄弟节点进行遍历
     * @param parent 父节点
     * @param newControl 当前节点
     */
    private findNextTabJumpNewControl2(parent: NewControl, newControl: NewControl): NewControl {
        const newControls = parent.getLeafList();
        if (!newControls || !newControls.length) {
            return;
        }
        let index = newControls.findIndex((ctrl) => ctrl === newControl) + 1;
        if (index === 0) {
            return;
        }
        for (const len = newControls.length; index < len; index++) {
            let current = newControls[index];
            if (current.isTabJump() && !current.isHidden()) {
                return current;
            }

            current = this.findNextTabJumpNewControl(current);
            if (current && !current.isHidden()) {
                return current;
            }
        }

        return;
    }

    private findNextTabJumpNewControl(newControl: NewControl): NewControl {
        const childs = newControl.getLeafList();
        if (!childs || !childs.length) {
            return;
        }

        for (let index = 0, len = childs.length; index < len; index++) {
            let current = childs[index];
            if (current.isHidden()) {
                continue;
            }
            if (current.isTabJump()) {
                return current;
            }
            current = this.findNextTabJumpNewControl(current);
            if (current  && !current.isHidden()) {
                return current;
            }
        }
        return;
    }

    private setNewControlElementHide(newControl: NewControl): boolean {
        if (newControl.isHidden()) {
            newControl['content'].setHidden(true, newControl.isNewSection());
            return true;
        }
        const list = newControl.getLeafList();
        if (list && list.length > 0) {
            let bChanged = false;
            for (let index = 0, length = list.length; index < length; index++) {
                bChanged = this.setNewControlElementHide(list[index]) || bChanged;
            }
            return bChanged;
        }
    }

    private addPasteNewControl( newControl: NewControl, index: number): void {
        const name = newControl.getNewControlName();
        if (true === this.newControlNames.has(name ) ) {
            return ;
        }

        const history = this.getHistory();
        this.newControls.splice(index + 1, 0, newControl);
        if ( history ) {
            history.addChange(new ChangeNewControlAddItem(newControl, index + 1, null));
        }

        this.newControlNames.set(name, newControl);
        // if (newControl.isSignatureBox()) {
        //     newControl['leafList'] = [];
        // }
        this.addPasteChild(newControl.getLeafList(), newControl);
    }

    private addPasteChild(childs: NewControl[], parent: NewControl): void {
        if (!childs || childs.length === 0) {
            return;
        }
        const names = this.newControlNames;
        const top = parent.getDocumentParent();
        childs.forEach((child) => {
            const name = child.getNewControlName();
            child.setDocumentParent(top);
            if (!names.has(name)) {
                names.set(name, child);
            }
            const cascades = child.getCascades1();
            if (cascades) {
                child.setCascades(cascades);
                child.setCascades1(null);
            }

            this.addPasteChild(child.getLeafList(), child);
        });
    }

    private adjustPasteNewControl(parent: DocumentContentBase, pasteNewControls: IPasteNewControl[],
                                  bInsertFile: boolean = false): NewControl[] {
        // const pasteNewControls = this.pasteNewControls;
        const newControls: NewControl[] = [];
        if (bInsertFile === false) {
            let index = 0;
            let control: IPasteNewControl = pasteNewControls[index];
            const parentName = control.parentName;

            while (control) {
                if (parentName === control.parentName) {
                    const newControl = control.control;
                    const newControlContent = newControl.getNewControlContent();
                    const oldParent = newControl.getParagraphParent();
                    newControlContent.setParent(oldParent);

                    newControls.push(newControl);
                    pasteNewControls.splice(index, 1);
                    this.setPasteLeafs(parent, newControl, pasteNewControls);
                } else {
                    index++;
                }
                control = pasteNewControls[index];
            }
        } else {
            for (const pasteControl of pasteNewControls) {
                newControls.push(pasteControl.control);
            }
        }

        return newControls;
    }

    private setPasteLeafs(parent: DocumentContentBase, newControl: NewControl,
                          pasteNewControls: IPasteNewControl[]): void {
        const parentName: string = newControl.getNewControlName();
        // const pasteNewControls = this.pasteNewControls;
        let index = 0;
        let control: IPasteNewControl = pasteNewControls[index];
        while (control) {
            if (control.parentName === parentName) {
                const actNewControl = control.control;
                const newControlContent = newControl.getNewControlContent();
                const oldParent = actNewControl.getParagraphParent();
                newControlContent.setParent(oldParent);

                newControl.addLeaf1(actNewControl);
                // 检查控件名称是否已存在，如果存在则重命名
                const originalName = actNewControl.getNewControlName();
                const uniqueName = this.makeUniqueName(actNewControl.getType(), originalName,FileSource.Copy);
                if (originalName !== uniqueName) {
                    actNewControl.setNewControlName(uniqueName);
                }
                this.newControlNames.set(uniqueName, actNewControl);
                pasteNewControls.splice(index, 1);
                this.setPasteLeafs(parent, actNewControl, pasteNewControls);
            } else {
                index++;
            }
            control = pasteNewControls[index];
        }
    }
}

export const NEW_CONTROL_NAME = new Map([
    [1, 'textedit'],
    [2, 'section'],
    [3, 'numberbox'],
    [4, 'datetimebox'],
    [5, 'listbox'],
    [6, 'multilistbox'],
    [7, 'comboBoxS'],
    [8, 'comboBoxM'],
    [NewControlType.RadioButton, 'radioButton'],
    [NewControlType.CheckBox, 'checkbox'],
    [NewControlType.MultiCheckBox, 'multicheckbox'],
    [NewControlType.SignatureBox, 'signatureBox'],
    [NewControlType.MultiRadio, 'multiradioButton'],
    [NewControlType.AddressBox, 'addressBox'],
    [NewControlType.SignatureElement, 'signatureElement'],
]);
