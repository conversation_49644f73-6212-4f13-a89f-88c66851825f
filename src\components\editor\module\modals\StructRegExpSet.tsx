import * as React from 'react';
import '../../style/cascade.less';
import Dialog from '../../ui/Dialog';
import Input from '../../ui/Input';
import Button from '../../ui/Button';

interface IDialogProps {
    documentCore: any;
    visible: boolean;
    property: string;
    onChange: (value: any, name: string) => void;
    close: (name: string | number, bRefresh?: boolean) => void;
    id: string;
}

interface IState {
    bRefresh: boolean;
}

export default class StructRegExpBtn extends React.Component<IDialogProps, IState> {
    private visible: boolean;
    private value: string;
    constructor(props: IDialogProps) {
        super(props);
        this.state = {
            bRefresh: false,
        };
        this.value = this.props.property;
    }

    public render(): any {
        
        return (
            <React.Fragment>
                <div className='editor-line'>
                    <div className='w-70'>正则验证</div>
                    <div className='right-auto'>
                        <div className='editor-regexp-btn'>
                            <div>
                                <Input value={this.value} readonly={true} placeholder=''/>
                            </div>
                            <div onClick={this.onClick}>配置</div>
                            <div onClick={this.onClear}>清空</div>
                        </div>
                    </div>
                </div>
                {this.renderDialog()}
            </React.Fragment>
        );
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        this.value = nextProps.property;
    }


    private onClick = (e: any): void => {
        this.visible = true;
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private onChange = (value: any, name: string): void => {
        this.value = value;
        this.props.onChange(value, name);
    }

    private onClear = (e: any): void => {
        if (!this.value) {
            return;
        }
        this.value = '';
        this.props.onChange('', this.props.id);
        this.onClose(null, null);
    }

    private onClose = (name: string, bRefresh: boolean): void => {
        this.visible = false;
        this.setState({});
    }

    private renderDialog(): any {
        if (this.visible === undefined) {
            return null;
        }
        const props = this.props;
        return (
            <StructRegExp
                visible={this.visible}
                id={props.id}
                onChange={this.onChange}
                close={this.onClose}
                property={this.value}
                documentCore={props.documentCore}
            />
        );
    }
}

class StructRegExp extends React.Component<IDialogProps, IState> {
    private visible: boolean;
    private data: string;

    constructor(props: any) {
        super(props);
        this.visible = this.props.visible;
    }

    public render(): any {
        return (
            <Dialog
                visible={this.visible}
                width={400}
                top='25%'
                open={this.open}
                title='正则表达式配置'
                footer={this.renderFooter()}
                id={this.props.id}
            >
                <div className='struct-regexp'>
                    <div className='editor-line'>
                        <span>正则表达式</span>
                        <div>
                            <Input value={this.data} onChange={this.onChange} placeholder='请不要加入外面的两个/'/>
                        </div>
                    </div>
                    <div className='editor-line'>包含正则：/<span style={{userSelect: 'all'}}>xxx</span>/</div>
                    <div className='editor-line'>不包含正则(?!exp)：/<span style={{userSelect: 'all'}}>^((?!xxx).)*$</span>/</div>
                </div>
            </Dialog>
        );
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        this.visible = nextProps.visible;
    }

    private renderFooter(): any {
        return (
            <span>
                <Button onClick={this.close}>取消</Button>
                <Button type='primary' onClick={this.confirm}>确认</Button>
            </span>
        );
    }

    

    private onChange = (value: any, name: string): void => {
        this.data = value;
    }

    

    private open = (): void => {
        this.data = this.props.property;
        this.setState({});
    }

    

    private close = (id?: any): void => {
        this.props.close(id);
        this.visible = false;
        this.setState({});
    }

    private confirm = (id?: any): void => {
        let bChange = false;
        if (this.data !== this.props.property) {
            this.props.onChange(this.data, this.props.id);
            bChange = true;
        }
        this.props.close(this.props.id, bChange);
    }
}
