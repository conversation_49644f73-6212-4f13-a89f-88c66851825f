const HtmlWebpackPlugin = require('html-webpack-plugin');
const {CleanWebpackPlugin} = require('clean-webpack-plugin');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const TsconfigPathsPlugin = require('tsconfig-paths-webpack-plugin');
const path = require('path');
const webpack = require('webpack');
const features = require('./features.json');
const conditionalCompiler = {
    loader: 'js-conditional-compile-loader',
    options: {
        WATER: false, // 去除水印代码
        NOWATER: true, // 无水印版
    }
}

module.exports = {
  entry: { 'hz-editor': './main.tsx', 'emr-iframe': './site.tsx'},
  devtool: 'inline-source-map',
  stats: {
    warnings: false, // This will suppress all warnings
  },
  module: {
    rules: [
      {
        test: /\.tsx?$/,
        use: ['ts-loader', conditionalCompiler],
        exclude: /node_modules/
      },
      {
        test: /\.css$/,
        use: [
          'style-loader',
          'css-loader'
        ]
      },
      {
        test: /\.less$/,
        use: [
          {
            loader: 'style-loader', // creates style nodes from JS strings
          },
          {
            loader: 'css-loader', // translates CSS into CommonJS
          },
          {
            loader: 'less-loader', // compiles Less to CSS
          },
        ],
      },
      {
        test: /\.(jpe?g|png|gif)$/i,
        use: [
          {
            loader: 'url-loader',
            options: {
              limit: 5000,
            }
          }
        ]
      },
      {
        test: /\.svg$/,
        use: [
          {
            loader: 'svg-url-loader',
            options: {
              limit: 5000,
              encoding: "base64"
            }
          },
        ],
      },
      {
        test: /\.(eot|ttf|woff|woff2)$/,
        use: [
          {
            loader: 'file-loader',
            options: {
              outputPath: 'fonts/'
            }
          }
        ]
      },
    ]
  },
   node: {
    //  fs: 'empty'
     __dirname: true,
     __filename: true,
   },
  resolve: {
    extensions: ['.tsx', '.ts', '.js'],
    plugins: [new TsconfigPathsPlugin({})],
    alias: {
      '@': path.resolve(__dirname, './src')
    },
    fallback: {
      stream: require.resolve('stream-browserify')
    }
  },
  devServer: {
    // contentBase: './dist/hz-editor',
    // port: 5000,
    // host: '127.0.0.1',
    //disableHostCheck: true
    static: {
      directory: path.resolve(__dirname, './dist/hz-editor-site'),
    },
    port: 5000,
    host: '127.0.0.1',
    allowedHosts: 'all'
  },
  output: {
    filename: '[name].js',
    chunkFilename: '[id].[contenthash].js',
    path: path.resolve(__dirname, 'dist/hz-editor-dev')
  },
  plugins: [
    new webpack.ProvidePlugin({
      process: 'process/browser',
    }),
    new CleanWebpackPlugin(),
    new webpack.DefinePlugin(features),
    new HtmlWebpackPlugin({
      title: 'hz-editor',
      filename: 'index.html',
      template: 'index.html',
      chunks: ['hz-editor']
    }),
    new HtmlWebpackPlugin({
      title: 'hz-editor',
      filename: 'iframe.html',
      template: 'index.html',
      chunks: ['emr-iframe']
    }),
    // new CopyWebpackPlugin([
    //   {
    //     from: path.resolve(__dirname, 'src/static/wasm'),
    //     to: '', // config.dev.assetsSubDirectory,
    //     ignore: ['.*']
    //   },
    //   {
    //     from: path.resolve(__dirname, 'node_modules/hz-editor-sdk-new/index.js'),
    //     to: path.resolve(__dirname, 'dist/iframe/sdk.js'), // config.dev.assetsSubDirectory,
    //     ignore: ['.*']
    //   },
    //   {
    //     from: path.resolve(__dirname, 'node_modules/typo-js/dictionaries'),
    //     to: 'dictionaries', // config.dev.assetsSubDirectory,
    //     toType: 'dir'
    //   }
    // ]),
    new CopyWebpackPlugin({
      patterns: [
        {
          from: path.resolve(__dirname, 'node_modules/hz-editor-sdk-new/index.js'),
          to: path.resolve(__dirname, 'dist/sdk.js'), // config.dev.assetsSubDirectory,
          globOptions: {
            ignore: ['.*']
          }
        },
        {
          from: path.resolve(__dirname, 'node_modules/hz-editor-sdk-new/index.js'),
          to: path.resolve(__dirname, 'sdk.js'), // config.dev.assetsSubDirectory,
          globOptions: {
            ignore: ['.*']
          }
        },
        {
          from: path.resolve(__dirname, 'src/static/unlimited'),
          to: ''
        },
        {
          from: path.resolve(__dirname, 'node_modules/typo-js/dictionaries'),
          to: 'dictionaries', // config.dev.assetsSubDirectory,
        },
      ]
    })
  ],
  mode: 'development'
};
