import * as React from 'react';
import { IDocumentParaElement } from '../../model/ParaElementProperty';

export default class ParaElementBase extends React.Component<IDocumentParaElement, {}> {

  constructor(props) {
    super(props);
  }

  render() {
    const {
      index,
      type,
      // width,
      positionX,
      positionY,
      dy,
      bVisible,
      bViewSecret,
      // content,
    } = this.props;
    const props = {
      // width,
      x: positionX,
      y: positionY,
      dy,
    };

    let content = true === bViewSecret ? '*' : this.props.content;
    // console.log(content)
    if (13 === type) {
      if ( true !== bVisible ) {
        content = ' ';
      }
      return <tspan textLength='2' lengthAdjust='spacingAndGlyphs' fill='#ACB4C1' fontStyle='normal' key={index} item-key={index}  {...props} >{content}</tspan>;
    } else {
      return <tspan key={index} item-key={index}  {...props} >{content}</tspan>;
    }
  }

  componentWillUnmount() {
    // console.log('ParaElementBase--------componentWillUnmount---------------')
  }
}
