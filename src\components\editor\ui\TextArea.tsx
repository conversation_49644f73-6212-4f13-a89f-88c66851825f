import React from 'react';

interface IProps {
    onChange?: (value: any, name?: string, e?: any) => void;
    onBlur?: (name?: string, input?: any) => void;
    onFocus?: (name?: string, input?: any) => void;
    onSelectionChange?: (index: number) => void;
    placeholder?: string;
    disabled?: boolean;
    readonly?: boolean;
    unDeleted?: boolean;
    name?: string;
    value: string;
    rows?: number;
}

export default class TextArea extends React.Component<IProps, {}> {
    private input: any;
    private value: string;
    constructor(props: any) {
        super(props);
        this.input = React.createRef();
        this.value = props.value;
    }

    public render(): any {
        const {disabled, readonly, placeholder, rows} = this.props;
        let className = 'editor-textarea';
        if (readonly) {
            className += ' readonly';
        }
        if (disabled) {
            className += ' disabled';
        }
        const value = this.props.value || '';
        return (
            <div className={className}>
                <div className='input'>
                    <textarea
                        onChange={this.onChange}
                        placeholder={placeholder}
                        disabled={disabled}
                        readOnly={readonly}
                        onBlur={this.onBlur}
                        onFocus={this.onFocus}
                        onKeyUp={this.onSelectionChange}
                        onMouseUp={this.onSelectionChange}
                        rows={rows}
                        value={value}
                        ref={this.input}
                    />
                </div>
            </div>
        );
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        if (nextProps.value !== this.value) {
            this.value = nextProps.value;
            this.setState({});
        }
    }

    private onSelectionChange = (event: any) => {
        const startIndex = event.target?.selectionStart;
        this.props?.onSelectionChange(startIndex == undefined ? -1 : startIndex);
    }

    private onChange = (e: any) => {
        this.value = e.target.value;
        if (this.props.onChange) {
            this.props.onChange(this.value, this.props.name);
        }
        this.setState({});
    }

    private onBlur = (e: any): void => {
        if (this.props.onBlur) {
            this.props.onBlur(this.props.name, e);
        }
    }

    private onFocus = (e: any): void => {
        if (this.props.onFocus) {
            this.props.onFocus(this.props.name, e);
        }
    }
}
