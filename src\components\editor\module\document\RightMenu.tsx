import * as React from 'react';
import { EmrEditor } from '../../Main';
import '../style/rightMenu.less';
import { MenuItemIndex, MENU_LIST, NewControlType, ToolbarIndex, EquationType,
    RIGHT_MENU_CODE,
    ResultType,
    fromCharCode} from '@/common/commonDefines';
import {
    GlobalEvent as gEvent,
    GlobalEventName as gEventName,
} from '@/common/GlobalEvent';

import { getImageEditor } from '@hz-editor/plugins';


interface IProps {
    host: EmrEditor;
}

interface IState {
    bRefresh: boolean;
}

interface IMenuItem {
    name: string;
    hide: boolean;
    disabled: boolean;
    className: string;
    index: number;
    childs?: IMenuItem[];
}

enum MenuType {
    // DocumentContent = 0,
    // HeaderContent,
    // FooterContent,
    Paragragph = 0,
    Region,
    Table,
    NewControl,
    Drawing,
    SvgDrawing,
    MedEquation,
    // DrawingInRegion,
    // DrawingInTable,
    NewControlInTableCell,
    NewControlInRegion,
    TableCellsSelected,
}

function getWaterText(doms: any[], waterText: number[]): boolean {
    if (!doms || !doms.length || !waterText.length) {
        return;
    }

    function forEachNode(obj: any, key: string): string {
        let childs: any[];
        if (Array.isArray(obj)) {
            childs = obj;
        } else {
            childs = obj.props?.children;
        }

        if (!childs || !childs.length) {
            return '';
        }

        if (obj.type === 'text') {
            let res = childs as any;
            if (typeof res !== 'string') {
                res = '';
            }
            return res;
        }

        let str = '';
        childs.forEach((child) => {
            str += forEachNode(child, key);
        });
        return str;
    }

    // let text = '';
    const keyNames = {};
    let count1 = 0; // 新组
    const keyNames2 = {};
    let index = 0;
    const waterCount = waterText.length;
    for (const len = doms.length; index < len; index++) {
        const obj = doms[index];
        const keys = Object.keys(obj);
        const key = keys[0];
        if (!key || !key.trim()) {
            continue;
        }

        if (keyNames[key]) {
            const datas = keyNames2[key];
            if (!datas || !datas.length) {
                return;
            }
            for (let dataIndex = 0, dataLen = datas.length; dataIndex < dataLen; dataIndex++) {
                const str = fromCharCode(waterText[count1]);
                if (str !== datas[dataIndex]) {
                    return;
                }
                count1++;
            }
            continue;
        }

        let curText = forEachNode(obj[key], key);
        if (!curText || !curText.trim()) {
            continue;
        }

        keyNames[key] = true;
        curText = curText.replace(/[\r|\s|\n]+/g, '');
        let curIndex = 0;
        keyNames2[key] = curText;
        let flag = false;
        for (const curLen = curText.length; curIndex < curLen; ++curIndex) {
            let str;
            // tslint:disable-next-line: no-conditional-assignment
            while (str = waterText[count1]) {
                if (isNaN(str) || typeof str !== 'number') {
                    return;
                }
                str = fromCharCode(waterText[count1]);
                if (str == null || str === '\x00') {
                    return;
                }

                if (curText.charAt(curIndex) !== str) {
                    return;
                }
                count1++;
                if (count1 === waterCount) {
                    flag = true;
                }
                break;
            }
            if (flag) {
                break;
            }
        }

        if (flag) {
            doms.length = index + 1;
            break;
        }
    }

    if (count1 !== waterCount) {
        return false;
    }

    return true;
}

const win = window;
let bRefresh = true;
const set2 = 'T';
const set3 = 'i';
const set4 = 'me';
const set5 = 'out';
const set1 = 'set';
export default class RightMenu extends React.Component<IProps, IState> {
    private host: EmrEditor;
    private list: IMenuItem[];
    private menuOption: any;
    private documentCore: any;
    private stopDocClick: boolean;
    private rightMenuDom: any;
    private isShowRightMenu: boolean;
    private listDemo: any;
    private bReadOnly: boolean;
    private region: any;
    private showMenus: any;
    private bReadOnlyMode: boolean;
    private oprateOptions: IMenuItem[];
    // private currentMoveNode: any;
    // private bCursorInNewControl: boolean;
    constructor(props: IProps) {
        super(props);
        this.host = props.host;
        this.documentCore = props.host.state.documentCore;
        this.initMenuList();
        this.rightMenuDom = React.createRef();
        this.state = {
            bRefresh: false,
        };
    }

    public render(): any {
        let className;
        if (this.bReadOnly && this.bReadOnlyMode && this.oprateOptions) {
            className += 'readonly-mode';
        }
        return (
            <div className='editor-rightMenu'>
                <div className='box' ref={this.rightMenuDom}>
                    <ul className={className}>{this.renderMenu()}</ul>
                </div>
            </div>
        );
    }

    public componentDidMount(): void {
        this.addEvent();
        const rightMenu = this.host.getRightMenus();
        if (rightMenu) {
            this.setRightMenuVisible(rightMenu, true);
        }

        // if (this.option) {
        //     this.contextmenu(this.option.event);
        // }
    }

    public componentWillUnmount(): void {
        this.removeEvent();
    }

    private removeEvent(): void {
        const dom = this.listDemo;
        if ( !dom ) {
            return ;
        }

        dom.removeEventListener('contextmenu', this.contextmenu, false);
        document?.removeEventListener('click', this.docClickEvent);
        gEvent.deleteEvent(this.host.docId, 'setToRightMenu', this.setToRightMenu);
        gEvent.deleteEvent(this.host.docId, gEventName.RightMenuVisible, this.setRightMenuVisible);
        const grid = dom.querySelector('.ReactVirtualized__Grid__innerScrollContainer');
        if (grid) {
            grid.removeEventListener('scroll', this.scrollHandler);
        }

        if (this.rightMenuDom.current) {
            this.rightMenuDom.current.removeEventListener(
                'click',
                this.handerRightMenu,
            );
        }

        // gEvent.deleteEvent(this.host.docId, gEventName.Blur, this.scrollHandler);
        gEvent.deleteEvent(this.host.docId, gEventName.Resize, this.scrollHandler);
        gEvent.deleteEvent(this.host.docId, gEventName.Click, this.docClickEvent);
        gEvent.deleteEvent(this.host.docId, gEventName.Readonly, this.setReadonly);
    }

    private addEvent(): void {
        const dom = this.host['myRef'].current ?
                    this.host['myRef'].current.querySelector('.ReactVirtualized__Grid') : null;
        if ( !dom ) {
            console.log('RightMenu:', this.host['myRef'].current)
            return ;
        }

        loopWater(this.host.contentRef?.current);
        // if (this.option) {
        //     dom = this.option.dom;
        // } else {
        //     dom = this.host['myRef'].current.querySelector('.ReactVirtualized__Grid');
        // }
        this.listDemo = dom;
        dom.addEventListener('contextmenu', this.contextmenu, false);
        document.addEventListener('click', this.docClickEvent);
        gEvent.addEvent(this.host.docId, gEventName.Click, this.docClickEvent);
        gEvent.addEvent(this.host.docId, 'setToRightMenu', this.setToRightMenu);
        gEvent.addEvent(this.host.docId, gEventName.RightMenuVisible, this.setRightMenuVisible);
        const grid = dom.querySelector('.ReactVirtualized__Grid__innerScrollContainer');
        if (grid) {
            grid.addEventListener('scroll', this.scrollHandler);
        } else {
            console.log('ReactVirtualized__Grid__innerScrollContainer is not found')
        }
        // this.rightMenuDom.current.addEventListener('mousemove', this.handleMouseMove);
        if ( this.rightMenuDom.current ) {
            this.rightMenuDom.current.addEventListener(
                'click',
                this.handerRightMenu,
            );
        } else {
            console.log('rightMenuDom is not found')
        }
        // gEvent.addEvent(this.host.docId, gEventName.Blur, this.scrollHandler);
        gEvent.addEvent(this.host.docId, gEventName.Resize, this.scrollHandler);
        gEvent.addEvent(this.host.docId, gEventName.Readonly, this.setReadonly);
    }

    private setRightMenuVisible = (option: any, bInit: boolean): void => {
        if (!option) {
            return;
        }
        // 设置只读状态下是否可以显示右键菜单
        const readonlyMode = option.readonlyMode;
        if (readonlyMode && typeof readonlyMode === 'object') {
            if (this.bReadOnlyMode === readonlyMode.bEnable) {
                readonlyMode.result = ResultType.UnEdited;
            } else {
                this.bReadOnlyMode = readonlyMode.bEnable;
                readonlyMode.result = ResultType.Success;
            }
            if (!this.oprateOptions) {
                const menuOption = this.menuOption;
                this.oprateOptions = [menuOption[MenuItemIndex.Copy], menuOption[MenuItemIndex.Refresh]];
            }

            if (bInit === true) {
                delete option.readonlyMode;
            } else {
                return;
            }
        }
        const showMenus = this.showMenus;
        const vals = Object.values(option);
        option.result = 1;
        if (vals.length === 0) {
            return;
        }

        if (vals.find((val) => typeof val !== 'boolean')) {
            return;
        }

        const keys = Object.keys(option);
        keys.forEach((key) => {
            // 兼容英文
            const originKey = key;
            if (key in RIGHT_MENU_CODE) {
                key = RIGHT_MENU_CODE[key];
            }
            showMenus[key] = option[originKey];
        });
        // keys.forEach((key: string) => {
        //     const actIndex = RIGHT_MENU_CODE[key];
        //     if (actIndex === undefined) {
        //         return;
        //     }
        //     showMenus[actIndex] = option[key];
        // });
        option.result = 0;
    }

    private setReadonly = (readonly: boolean): void => {
        this.bReadOnly = readonly;
    }

    private initMenuList(): void {
        const rightMenuList: IMenuItem[] = JSON.parse(JSON.stringify(MENU_LIST));

        this.list = rightMenuList;
        const menuOption: any = {};
        rightMenuList.forEach((item) => {
            menuOption[item.index] = item;
        });
        this.menuOption = menuOption;
        this.showMenus = {
            [MenuItemIndex.Cut]: true,
            [MenuItemIndex.Copy]: true,
            [MenuItemIndex.Format]: true,
            [MenuItemIndex.Delete]: true,
            [MenuItemIndex.Font]: true,
            [MenuItemIndex.Paragraph]: true,
            [MenuItemIndex.Image]: true,
            [MenuItemIndex.Formula]: true,
            [MenuItemIndex.EditImage]: true,
            [MenuItemIndex.MergeCell]: true,
            [MenuItemIndex.SplitCell]: true,
            [MenuItemIndex.Insert]: true,
            [MenuItemIndex.DeleteRow]: true,
            [MenuItemIndex.DeleteCol]: true,
            [MenuItemIndex.Table]: true,
            [MenuItemIndex.CellProperty]: true,
            [MenuItemIndex.Undo]: true,
            [MenuItemIndex.Redo]: true,
            [MenuItemIndex.Region]: true,
            [MenuItemIndex.DeleteRegion]: true,
            [MenuItemIndex.InsertTopRow]: true,
            [MenuItemIndex.InsertBottomRow]: true,
            [MenuItemIndex.InsertLeftCol]: true,
            [MenuItemIndex.InsertRightCol]: true,
            // [MenuItemIndex.Protected]: true,
            // [MenuItemIndex.UnProtected]: true,
            [MenuItemIndex.Struct]: true,
            [MenuItemIndex.Refresh]: true,
            [MenuItemIndex.AICheck]: true,
        };
    }

    private scrollHandler = (e: Event): any => {
        if (this.isShowRightMenu === true) {
            this.isShowRightMenu = false;
            this.hideRightMenu();
        }
    }

    private setToRightMenu = (datas: any[]): void => {
        const option = this.menuOption;
        datas.forEach((item) => {
            const current = option[item.index];
            if (current) {
                current[item.key] = item.value;
            }
        });
        this.setState({ bRefresh: !this.state.bRefresh });
    }

    private hideRightMenu(): void {
        const dom = this.rightMenuDom.current;
        dom.className = dom.className.replace(/\s+right-menu-visible/g, '');
        this.isShowRightMenu = false;
    }

    private docClickEvent = () => {
        if (this.isShowRightMenu !== true) {
            return;
        }
        if (this.stopDocClick) {
            this.stopDocClick = false;
            return;
        }
        this.hideRightMenu();
    }

    private setMenuItemHide(index: number): boolean {
        if (this.showMenus[index] !== true) {
            this.menuOption[index].hide = true;
            return true;
        }

        return false;
    }

    private resetRigthMenu(type: number, option: any): void {
        switch (type) {
            case MenuType.Drawing:
            // case 1: // 图片
                this.hideTableMenu(true);
                this.hideEquationImageMenu(true);
                this.hideEditImageMenu(true);
                this.hideImageMenu(false);
                this.hideParagraphMenu(true);
                this.hideStructMenu(true);
                this.hideRegionMenu(true);
                this.setBaseMenu(option);
                break;
            case MenuType.MedEquation:
            // case 2: // 医学公式
                this.hideTableMenu(true);
                this.hideEquationImageMenu(this.documentCore.isInNewControlReadOnly());
                this.hideEditImageMenu(true);
                this.hideImageMenu(true);
                this.hideParagraphMenu(true);
                this.hideStructMenu(true);
                this.hideRegionMenu(true);
                this.setBaseMenu(option);
                break;
            case MenuType.Table:
            // case 3: // 表格内容
            // case 4: // 表格单元格
                this.hideEquationImageMenu(true);
                this.hideEditImageMenu(true);
                this.hideImageMenu(true);
                this.hideTableMenu(false);
                this.hideStructMenu(true);
                // this.hideParagraphMenu(false);
                // if (type === 3) {
                //     this.menuOption[MenuItemIndex.MergeCell].disabled = true;
                // } else {
                //     this.menuOption[MenuItemIndex.SplitCell].disabled = true;
                // }
                break;
            // case 5:
            // case 6: // 表格内的医学公式和图片
            //     this.hideTableMenu(true);
            //     this.hideParagraphMenu(true);
            //     this.hideStructMenu(true);
            //     this.hideRegionMenu(true);
            //     if (type === 5) {
            //         this.hideImageMenu(false);
            //         this.hideEquationImageMenu(true);
            //         this.hideEditImageMenu(true);
            //     } else {
            //         this.hideImageMenu(true);
            //         this.hideEquationImageMenu(false);
            //         this.hideEditImageMenu(false);
            //     }
            //     this.menuOption[MenuItemIndex.MergeCell].disabled = true;
            //     break;
            case MenuType.NewControl:
            // case 7: // 结构化元素
                this.hideStructMenu(false);
                this.hideTableMenu(true);
                this.hideEquationImageMenu(true);
                this.hideEditImageMenu(true);
                this.hideImageMenu(true);
                this.hideParagraphMenu(false);
                // this.setBaseMenu(option);
                break;
            case MenuType.NewControlInTableCell:
            // case 8: // 表格内的结构化元素
                this.hideTableMenu(false);
                this.hideParagraphMenu(false);
                this.hideEquationImageMenu(true);
                this.hideEditImageMenu(true);
                this.hideImageMenu(true);
                this.hideStructMenu(false);
                // this.setBaseMenu(option);
                // this.menuOption[MenuItemIndex.MergeCell].disabled = true;
                break;
            case MenuType.SvgDrawing:
            // case 9: //可编辑图片
                this.hideTableMenu(true);
                this.hideEquationImageMenu(true);
                this.hideEditImageMenu(false);
                this.hideImageMenu(true);
                this.hideParagraphMenu(true);
                this.hideStructMenu(true);
                this.hideRegionMenu(true);
                this.setBaseMenu(option);
                break;
            default:
                this.hideEquationImageMenu(true);
                this.hideImageMenu(true);
                this.hideTableMenu(true);
                this.hideStructMenu(true);
                this.hideParagraphMenu(false);
        }

        // if (!this.setMenuItemHide(MenuItemIndex.Cut)) {
        //     const obj = this.menuOption[MenuItemIndex.Cut];
        //     obj.hide = false;
        //     obj.disabled = false;
        // }
        // if (!this.setMenuItemHide(MenuItemIndex.Format)) {
        //     const obj = this.menuOption[MenuItemIndex.Format];
        //     obj.hide = false;
        //     obj.disabled = false;
        // }
        // if (!this.setMenuItemHide(MenuItemIndex.Copy)) {
        //     const obj = this.menuOption[MenuItemIndex.Copy];
        //     obj.hide = false;
        //     obj.disabled = false;
        // }
        // if (!this.setMenuItemHide(MenuItemIndex.Delete)) {
        //     const obj = this.menuOption[MenuItemIndex.Delete];
        //     obj.hide = false;
        //     obj.disabled = false;
        // }
        // if (!this.setMenuItemHide(MenuItemIndex.Refresh)) {
        //     const obj = this.menuOption[MenuItemIndex.Refresh];
        //     obj.hide = false;
        //     obj.disabled = false;
        // }

        this.setBaseMenusItemHide([MenuItemIndex.Cut, MenuItemIndex.Format, MenuItemIndex.Copy, MenuItemIndex.Delete,
            MenuItemIndex.Refresh, MenuItemIndex.Undo, MenuItemIndex.Redo, MenuItemIndex.Font,
            MenuItemIndex.Paragraph]);
    }

    private setBaseMenusItemHide(indexs: number[]): void {
        const menus = this.menuOption;
        indexs.forEach((index) => {
            if (!this.setMenuItemHide(index)) {
                const obj = menus[index];
                obj.hide = false;
                obj.disabled = false;
            }
        });
    }

    private toggleCellPro(index: number): void {
        const childs = this.menuOption[MenuItemIndex.CellProperty].childs;
        // const bProtected = this.documentCore.isTableCellProtected();

        childs.forEach((child) => {
            if (child.index === index) {
                child.hide = true;
            } else {
                child.hide = false;
            }
        });

        if ( MenuItemIndex.Protected === index ) {
            this.documentCore.setTableCellProtected(true);
        } else if ( MenuItemIndex.UnProtected === index ) {
            this.documentCore.setTableCellProtected(false);
        }
        // if (index !== undefined) {
        //     childs.forEach((child) => {
        //         if (child.index === index) {
        //             child.hide = false;
        //         } else {
        //             child.hide = true;
        //         }
        //     });
        // } else {
        //     childs.forEach((child) => {
        //         child.hide = !child.hide;
        //     });
        // }
    }

    private hideStructMenu(bHide: boolean): void {
        if (this.setMenuItemHide(MenuItemIndex.Struct)) {
            return;
        }
        const obj = this.menuOption[MenuItemIndex.Struct];
        obj.hide = bHide;
        obj.disabled = bHide;
    }

    private hideRegionMenu(bHide: boolean): void {
        if (!this.setMenuItemHide(MenuItemIndex.Region)) {
            const obj = this.menuOption[MenuItemIndex.Region];
            obj.hide = bHide;
            obj.disabled = bHide;
        }

        if (!this.setMenuItemHide(MenuItemIndex.DeleteRegion)) {
            const obj = this.menuOption[MenuItemIndex.DeleteRegion];
            obj.hide = bHide;
            // 区域删除
            const region = this.documentCore.getCurrentRegion();
            obj.disabled = region ? !region.canDeleteSelf() : true;
        }
    }

    private hideImageMenu(bHide: boolean): void {
        if (this.setMenuItemHide(MenuItemIndex.Image)) {
            return;
        }
        if ( this.isHideDrawingMenu() ) {
            bHide = true;
        }

        const obj = this.menuOption[MenuItemIndex.Image];
        obj.hide = bHide;
        obj.disabled = bHide;
    }

    private hideEquationImageMenu(bHide: boolean): void {
        if (this.setMenuItemHide(MenuItemIndex.Formula)) {
            return;
        }
        if ( this.isHideDrawingMenu() ) {
            bHide = true;
        }

        const obj = this.menuOption[MenuItemIndex.Formula];
        obj.hide = bHide;
        obj.disabled = bHide;
    }

    private hideEditImageMenu(bHide: boolean): void {
        if (this.setMenuItemHide(MenuItemIndex.EditImage)) {
            return;
        }
        if ( this.isHideDrawingMenu() ) {
            bHide = true;
        }

        const obj = this.menuOption[MenuItemIndex.EditImage];
        obj.hide = bHide;
        obj.disabled = bHide;
    }

    private hideTableMenu(bHide: boolean): void {
        const documentCore = this.documentCore;
        const table = documentCore.getCurrentTable();
        const showMenus = this.showMenus;
        const option = this.menuOption;
        // for (let i = MenuItemIndex.MergeCell; i <= MenuItemIndex.CellProperty; i++) {
        //     if (showMenus[i] === false) {
        //         option[i].hide = true;
        //     }
        // }
        // if (this.setMenuItemHide(MenuItemIndex.Table)) {
        //     return;
        // }

        if ( null == table ) {
            for (let i = MenuItemIndex.MergeCell; i <= MenuItemIndex.CellProperty; i++) {
                if (showMenus[i] === false) {
                    option[i].hide = true;
                    continue;
                }
                option[i].hide = bHide;
                option[i].disabled = true;
            }
            return ;
        }

        const bProtected = documentCore.isTableCellProtected();
        const bSelectCells = (documentCore.isSelectedTableCells() && 1 < table.getSelectionArray().length) ?
                                    true : false;
        const bTableReadOnlyProtect = table.isReadOnlyProtect();
        const bReadOnly = table.isInRegionReadOnly() || documentCore.isInNewControlReadOnly();
        // const bTableDeleteProtect = table.isDeleteProtect();
        const bCanAddRow = table.canAddRow();
        const bCanDeleteRow = table.canDeleteRow();
        const bCanDeleteCol = table.canDeleteCol();
        for (let i = MenuItemIndex.MergeCell; i <= MenuItemIndex.CellProperty; i++) {
            if (showMenus[i] === false) {
                option[i].hide = true;
                continue;
            }
            option[i].hide = bHide;

            if ( bReadOnly && false === bTableReadOnlyProtect ) {
                option[i].disabled = true;
                continue;
            }

            switch ( i ) {
                case MenuItemIndex.MergeCell:
                    option[i].disabled = ( !bSelectCells || bProtected || bTableReadOnlyProtect );
                    break;

                case MenuItemIndex.SplitCell:
                    option[i].disabled = ( false === option[MenuItemIndex.MergeCell].disabled
                                        || bProtected || bTableReadOnlyProtect );
                    break;

                case MenuItemIndex.Insert:
                    option[i].disabled = bTableReadOnlyProtect;

                    if ( option[i].childs && false === option[i].disabled ) {
                        option[i].childs.forEach((child) => {
                            if (child.index >= MenuItemIndex.InsertTopRow &&
                                child.index <= MenuItemIndex.InsertRightCol) {
                                child.hide = !this.showMenus[child.index];
                            } else {
                                child.hide = option[i].hide;
                            }

                            if ( MenuItemIndex.InsertTopRow === child.index
                                || MenuItemIndex.InsertBottomRow === child.index ) {
                                child.disabled = !bCanAddRow;
                            } else {
                                child.disabled = option[i].disabled;
                            }
                        });
                    }
                    break;

                case MenuItemIndex.DeleteCol:
                    option[i].disabled = (bReadOnly || bProtected || bTableReadOnlyProtect);

                    if ( !bCanDeleteCol ) {
                        option[i].disabled = true;
                    }
                    break;
                case MenuItemIndex.DeleteRow:
                    option[i].disabled = (bReadOnly || bProtected || bTableReadOnlyProtect);

                    if ( !bCanDeleteRow ) {
                        option[i].disabled = true;
                    }
                    break;

                case MenuItemIndex.Table:
                    option[i].disabled = bHide;
                    break;

                case MenuItemIndex.CellProperty:
                    option[i].hide = bHide;
                    option[i].disabled = bHide;
                    option[i].childs.forEach((child) => {
                        if ( MenuItemIndex.Protected === child.index ) {
                            child.hide = option[i].disabled || bProtected;
                            child.disabled = bProtected;
                        } else if ( MenuItemIndex.FormulaCalc === child.index ||
                            MenuItemIndex.TableCellVertAlign === child.index ) {
                            child.disabled = false;
                            child.hide = false;
                        } else {
                            child.hide = option[i].disabled || !bProtected;
                            child.disabled = !bProtected;
                        }
                    });
                    break;

                default:
                    break;
            }
        }

        if ( table ) {
            this.hideParagraphMenu(bTableReadOnlyProtect);
        }
    }

    private setBaseMenu(option: { copy: boolean }): void {
        option.copy = false;
    }

    private hideParagraphMenu(bHide: boolean): void {
        const option = this.menuOption;
        option[MenuItemIndex.Font].disabled = bHide;
        option[MenuItemIndex.Paragraph].disabled = bHide;
        option[MenuItemIndex.Font].hide = bHide;
        option[MenuItemIndex.Paragraph].hide = bHide;
        option[MenuItemIndex.Paragraph].hide = bHide;
        if (this.setMenuItemHide(MenuItemIndex.Table)) {
            return;
        }
        if (this.setMenuItemHide(MenuItemIndex.Font)) {
            return;
        }
    }

    private contextmenu = (e: any) => {
        this.bReadOnly = this.documentCore.isProtectedMode();
        if ( this.documentCore.isMovingTableBorder() ) {
            this.documentCore.resetMovingTableNewBorder();
        }

        if (this.bReadOnly && !this.documentCore.isStrictMode() && this.bReadOnlyMode !== true) {
            e.preventDefault();
            e.stopPropagation();
            return;
        }
        // if (true === EmrEditor.isClickOnImage(e) || this.host.isPopTableMenu()) {
        //   return;
        // }
        const option = { isStop: false, copy: true };
        let dom = this.rightMenuDom.current;
        gEvent.setEvent(this.host.docId, 'contextmenu', e, dom, option);
        if (option.isStop === true) {
            return;
        }
        e.preventDefault();
        e.stopPropagation();
        const type = this.getContextMenuType(e);
        this.resetRigthMenu(type, option);
        const documentCore = this.documentCore;
        const bRedo = documentCore.canRedo();
        const bUndo = documentCore.canUndo();
        const menus = this.menuOption;
        if (bRedo !== true) {
            menus[MenuItemIndex.Redo].disabled = true;
        }
        if (bUndo !== true) {
            menus[MenuItemIndex.Undo].disabled = true;
        }
        let x: number = 0;
        let y: number = 0;
        const doc = dom.ownerDocument;
        if (this.listDemo.ownerDocument !== doc) {
            const position = doc.activeElement.getBoundingClientRect();
            x = position.x;
            y = position.y;
        }
        const top = 16 + e.clientY + y;
        let left = 12 + e.clientX + x;
        const subWidth = doc.body.clientWidth - left - 124;
        if (subWidth < 0 ) {
            left += subWidth;
        }
        dom.style.left = left + 'px';
        dom.style.top = top + 'px';
        dom.className += ' right-menu-visible';
        this.isShowRightMenu = true;
        this.setState({bRefresh: !this.state.bRefresh});
        // this.imageRef = e;
        setTimeout(() => {
            const height = dom.clientHeight;
            const maxHeight = doc.firstElementChild.clientHeight;
            const nextTop = maxHeight - height - 80;
            if (top > nextTop) {
                dom.style.top = nextTop + 'px';
            }
            dom = null;
        }, 10);
    }

    private getLiDom(target: any): HTMLElement {
        if (target.tagName === 'LI') {
            return target;
        }

        return target.parentNode;
    }

    private handerRightMenu = (e: any): void => {
        const target = this.getLiDom(e.target);
        const dataIndex = target.getAttribute('data-index');
        if (!dataIndex) {
            this.stopDocClick = true;
            return;
        }

        let index = parseInt(dataIndex, 10);

        let item: any;

        // const isChild: boolean = target.getAttribute('data-child') === '1';
        const parentIndex: string = target.getAttribute('data-parent-index');
        if (parentIndex) {
            item = this.menuOption[parentIndex].childs[index];
            index = item.index;
        } else {
            item = this.menuOption[index];
        }

        // if (isChild === true) {
        //   e.stopPropagation();
        // }
        e.stopPropagation();

        if (item.disabled === true || item.childs !== undefined) {
            // this.stopDocClick = true;
            // e.stopPropagation();
            return;
        }
        // e.stopPropagation();
        const option = { isStop: false };

        gEvent.setEvent(this.host.docId, 'rightMenuClick', e, option);
        this.stopDocClick = option.isStop;
        switch (index) {
            case MenuItemIndex.Font:
                gEvent.setEvent(this.host.docId, gEventName.DialogEvent, index);
                break;
            case MenuItemIndex.Paragraph:
                gEvent.setEvent(this.host.docId, gEventName.DialogEvent, index);
                // this.open('paragraph');
                break;
            case MenuItemIndex.Image:
                gEvent.setEvent(this.host.docId, gEventName.DialogEvent, index);
                // this.open('image');
                // const paraDrawing = this.props.documentCore.getSelectedImage() as ParaDrawing;
                // this.host.setState({imageConfigModalType: ImageConfigModalType.Advanced});
                // // this.imageConfigModalType = ImageConfigModalType.Advanced;
                // // this.imageConfigModalType = ImageConfigModalType.Basic;
                // const imageRatio = paraDrawing.width / paraDrawing.height;
                // this.setState({imageWidth: paraDrawing.width, imageHeight: paraDrawing.height,
                //              imageSource: paraDrawing.src, imageName: paraDrawing.name,
                //              imagePreserveAspectRatio: paraDrawing.preserveAspectRatio,
                //              imageSizeLocked: paraDrawing.sizeLocked,
                //              imageDeleteLocked: paraDrawing.deleteLocked,
                //              imageRatio});
                break;
            case MenuItemIndex.Formula:
                const equation = this.documentCore.getSelectedImage();
                gEvent.setEvent(this.host.docId, gEventName.DialogEvent, index, equation);
                // if (this.imageRef) {
                //     this.host.handleDoubleClick(this.imageRef);
                // }
                break;
            case MenuItemIndex.EditImage:
              const img = this.documentCore.getSelectedImage();
              const { width, height } = img;
              getImageEditor().then((editor) => {
                const json = img.equationElem as string;
                if (!!json) {
                  editor.open(json);
                } else {
                  editor.openByOriginImage(img.src as string);
                }
                editor.onsave = (json, url, _) => {
                  const documentCore = this.host.getDocumentCore();
                  documentCore.addInlineImage(width, height, url, '', EquationType.EditableSvg, json);
                  this.host.handleRefresh();
                };
              });
              break;
            case MenuItemIndex.MergeCell:
                // gEvent.setEvent(this.host.docId, gEventName.DialogEvent, index);
                this.mergeTableCells();
                break;
            case MenuItemIndex.SplitCell:
                gEvent.setEvent(this.host.docId, gEventName.DialogEvent, index);
                // this.open('splitCell');
                break;
            case MenuItemIndex.DeleteRow:
                // remove dialog when click deleteRow or deleteCol
                this.documentCore.removeTableRow();
                this.host.handleRefresh();
                break;
            case MenuItemIndex.DeleteCol:
                this.documentCore.removeTableColumn();
                this.host.handleRefresh();
                // gEvent.setEvent(this.host.docId, gEventName.DialogEvent, index);
                // this.host.clearSection();
                // setTimeout(() => {
                //     this.host.setCursorVisible(true);
                // });
                // this.host.setState({
                //     tablePopMenuModalType: TableMenuModalType.DeleteTableCells
                // });
                break;
            case MenuItemIndex.Table:
                gEvent.setEvent(this.host.docId, gEventName.DialogEvent, index);
                // this.open('table');
                break;
            case MenuItemIndex.Undo: {
                this.documentCore.undo();
                this.host.handleRefresh();
                break;
            }
            case MenuItemIndex.Redo: {
                this.documentCore.redo();
                this.host.handleRefresh();
                break;
            }
            case MenuItemIndex.InsertTopRow:
                this.documentCore.insertTableRow(true);
                this.host.setSelections();
                this.host.handleRefresh();
                // add event
                {
                    const externalEvent = this.host.externalEvent;
                    if (this.documentCore.getCurrentTable() && externalEvent &&
                        typeof externalEvent.nsoRowInsertedInTable === 'function') {
                        externalEvent.nsoRowInsertedInTable(this.documentCore.getCurrentTableName());
                    }
                }
                break;
            case MenuItemIndex.InsertBottomRow:
                this.documentCore.insertTableRow(false);
                this.host.setSelections();
                this.host.handleRefresh();
                // add event
                {
                    const externalEvent = this.host.externalEvent;
                    if (this.documentCore.getCurrentTable() && externalEvent &&
                        typeof externalEvent.nsoRowInsertedInTable === 'function') {
                        externalEvent.nsoRowInsertedInTable(this.documentCore.getCurrentTableName());
                    }
                }
                break;
            case MenuItemIndex.InsertLeftCol:
                this.documentCore.insertTableColumn(true);
                this.host.setSelections();
                this.host.handleRefresh();
                break;
            case MenuItemIndex.InsertRightCol:
                this.documentCore.insertTableColumn(false);
                this.host.setSelections();
                this.host.handleRefresh();
                break;
            case MenuItemIndex.Protected:
                gEvent.setEvent(this.host.docId, gEventName.DialogEvent, index);
                this.toggleCellPro(MenuItemIndex.Protected);
                break;
            case MenuItemIndex.UnProtected:
                gEvent.setEvent(this.host.docId, gEventName.DialogEvent, index);
                this.toggleCellPro(MenuItemIndex.UnProtected);
                break;
            case MenuItemIndex.TableCellVertAlign:
                gEvent.setEvent(this.host.docId, gEventName.DialogEvent, index);
                break;
            case MenuItemIndex.FormulaCalc:
                gEvent.setEvent(this.host.docId, gEventName.DialogEvent, index);
                break;
            case MenuItemIndex.Region: {
                gEvent.setEvent(this.host.docId, gEventName.DialogEvent, ToolbarIndex.Region, this.region);
                break;
            }
            case MenuItemIndex.DeleteRegion: {
                const res = this.documentCore.deleteRegion(this.region.newControlName);
                if (res === 0) {
                    this.host.handleRefresh();
                }
                break;
            }
            case MenuItemIndex.Struct:
                const props = this.getNewControlProps();
                if (!props) {
                    break;
                }
                let nType: ToolbarIndex;
                switch (props.newControlType) {
                    case NewControlType.Combox:
                    case NewControlType.ListBox:
                    case NewControlType.MultiCombox:
                    case NewControlType.MultiListBox: {
                        nType = ToolbarIndex.SingleOrMultipleBox;
                        break;
                    }
                    case NewControlType.Section:
                    case NewControlType.TextBox: {
                        nType = ToolbarIndex.TextBox;
                        break;
                    }
                    case NewControlType.NumberBox: {
                        nType = ToolbarIndex.NumberBox;
                        break;
                    }
                    case NewControlType.DateTimeBox: {
                        nType = ToolbarIndex.DateBox;
                        break;
                    }
                    case NewControlType.MultiRadio: {
                        nType = ToolbarIndex.MultiRadio;
                        break;
                    }
                    case NewControlType.RadioButton: {
                        nType = ToolbarIndex.Radio;
                        break;
                    }
                    case NewControlType.CheckBox: {
                        nType = ToolbarIndex.Checkbox;
                        break;
                    }
                    case NewControlType.TextBox: {
                        nType = ToolbarIndex.TextBox;
                        break;
                    }
                    case NewControlType.SignatureBox: {
                        nType = ToolbarIndex.SignatureBox;
                        break;
                    }
                    case NewControlType.AddressBox: {
                        nType = ToolbarIndex.AddressBox;
                        break;
                    }
                }
                gEvent.setEvent(this.host.docId, gEventName.DialogEvent, nType, props);

                break;

            // case MenuItemIndex.Refresh:
            //     this.host.contentRef.current.forceUpdate2();
            //     break;
        }
        if (option.isStop === false) {
            this.hideRightMenu();
        }

        if ( MenuItemIndex.Refresh === index ) {
            /* IFTRUE_WATER */
                // 触发刷新时重置水印及坐标
            this.documentCore.resetCorePosition(true, false);
            /* FITRUE_WATER */
            this.documentCore.recalculateAllForce();
            this.host.mainRefresh();

            const document = this.host.contentRef.current.getContainer();
            const svgs = !!document ?
                    document.getElementsByClassName('page-container') : null;
            for (let index2 = 0; svgs && index2 < svgs.length; index2++) {
                const element = svgs[index2];
                if (element) {
                    element.setAttribute('transform', 'rotate(0)');
                }
            }
        }
    }

    private handleMouseMove = (e: any): void => {
        const target = this.getLiDom(e.target);
        const dataIndex = target.getAttribute('data-index');
        if (!dataIndex) {
            return;
        }

        const index = parseInt(dataIndex, 10);
        switch (index) {
            case MenuItemIndex.Insert:
            case MenuItemIndex.Protected:
                const option = this.menuOption[index];
                // if ( option.childs ) {
                //     option.childs.forEach((child) => {
                //         child.hide = false;
                //         child.disabled = false;
                //     });
                // }
                // this.setState({bRefresh: !this.state.bRefresh});
                break;
        }
    }

    /**
     * 合并单元格
     */
    private mergeTableCells(): void {
        this.documentCore.mergeTableCells();
        this.host.handleRefresh(10);
    }

    private isImage(e: any): boolean {
        return (
            e.target.tagName === 'image' ||
            (e.target.tagName === 'circle' &&
                e.target.className.baseVal.includes('image-processor'))
        );
    }

    private isRegion(): any {
        const region = this.region = this.documentCore.getCurrentRegionProps();
        if (region) {
            this.hideRegionMenu(false);
        } else {
            this.hideRegionMenu(true);
        }
    }

    private getContextMenuType(e: any): number {
        let menuType = MenuType.Paragragph;
        const documentCore = this.documentCore;

        if (true === this.isImage(e) &&
            e.target.className.baseVal.includes('select-button') === false) {
            const image = documentCore.getSelectedImage();
            if ( image ) {
                if ( image.isImage()) {
                    menuType = image.isSvgDrawing() ? MenuType.SvgDrawing : MenuType.Drawing;
                    // documentCore.setImageSelectionInfo(e.target);
                    // if ( image.isSvgDrawing() ) {
                    //     type = 9;
                    // } else {
                    //     type = 1;
                    // }
                } else if ( image.isMedEquation() ) {
                    // type = 2;
                    menuType = MenuType.MedEquation;
                }
            }
        } else {
            const bInTableCell = documentCore.isInTableCell();
            const bSelectCells = !bInTableCell ? documentCore.isSelectedTableCells() : false;

            if ( bSelectCells ) {
                menuType = MenuType.TableCellsSelected;
            } else {
                const bCursorInNewControl = documentCore.isCursorInNewControl();
                if (true === bCursorInNewControl) {
                    // type = 7;
                    menuType = bInTableCell ? MenuType.NewControlInTableCell : MenuType.NewControl;
                } else {
                    menuType = bInTableCell ? MenuType.Table : menuType;
                }
            }

            this.isRegion();
        }

        return menuType;

        // let type1: number = 0;
        // if (true === documentCore.isInTableCell()) {
        //     // 在单元格内
        //     type1 = 3;
        // } else if (true === documentCore.isSelectedTableCells()) {
        //     // 选中单元格
        //     type1 = 4;
        // }

        // switch (type.toString() + type1) {
        //     case '03':
        //         type = 3;
        //         this.isRegion();
        //         break;
        //     case '04':
        //         type = 4;
        //         break;
        //     case '13':
        //         type = 5; // 表格里面选中图片
        //         break;
        //     case '23':
        //         type = 6; // 表格里面选中医学公式
        //         break;
        //     case '70': {
        //         this.isRegion();
        //         break;
        //     }
        //     case '73':
        //         type = 8;
        //         this.isRegion();
        //         break;
        //     default:
        //         // this.isRegion();
        //         break;
        // }

        // return type;
    }

    private getNewControlProps(): any {
        // if ( true === this.props.documentCore.isCursorInNewControl() ) {
        const property = this.documentCore.getCursorInNewControlProperty();

        return property;
    }

    private isHideDrawingMenu(): boolean {
        return false; // bug 在区域、表格禁止编辑情况下还是可以设置图片属性
        const drawing = this.documentCore.getSelectedDrawing();

        return (drawing && (drawing.isImage() || drawing.isMedEquation()) && drawing.isReadOnly());
    }

    private renderMenu(): any {
        // let options: number[];
        let list = this.list;
        if (this.bReadOnly && this.bReadOnlyMode && this.oprateOptions) {
            list = this.oprateOptions;
        }

        return list.map((item) => {
            if (item.hide === true) {
                return;
            }
            let onMouseEnter: any;
            let className = item.className;
            if (item.disabled) {
                className += ' disabled';
            }

            let children = null;
            let icon = null;
            if (item.childs && !item.disabled) {
                children = (
                    <ul className='inner-ul'>
                        {this.rightMenuChildRender(item.childs, item.index)}
                    </ul>
                );
                onMouseEnter = (e: any) => {
                    this.mouseEnter(item, e);
                };
                icon = (<i className='next-child' />);
            }
            return (
                <li
                    key={item.index}
                    data-index={item.index}
                    className={className}
                    onMouseEnter={onMouseEnter}
                >
                    <span>{item.name}</span>
                    {icon}
                    {children}
                </li>
            );
        });
    }

    private mouseEnter(item: IMenuItem, e: any): void {
        const dom = this.rightMenuDom.current;
        const left = parseInt(dom.style.left, 10);
        const width = dom.clientWidth;
        const doc = dom.ownerDocument;
        const body = doc.body;
        const subWidth = left + width + 130 - body.clientWidth;
        let li = e.target;
        if (li.tagName !== 'LI') {
            li = li.parentNode;
        }
        const target = li.querySelector('ul');
        if (subWidth > 0) {
            target.style.left = -width + 'px';
        } else {
            target.style.left = '';
        }
    }

    private rightMenuChildRender(datas: any[], parentIndex: number): any {
        return datas.map((item, index) => {
            let className = '';
            if (item.disabled) {
                className += 'disabled';
            } else {
                className += 'abled';
            }

            if (item.hide === true) {
                className += ' hide';
            }
            return (
                <li
                    key={item.index}
                    data-index={index}
                    data-parent-index={parentIndex}
                    data-child='1'
                    className={className}
                >
                    <span>{item.name}</span>
                </li>
            );
        });
    }
}
const ab1: any = '115';
const ab2: any = '101';
const ab3: any = '116';
const ab4: any = '79';
const ab5: any = '110';
const ab6: any = '108';
const ab7: any = '121';
const ab8: any = '98';

function loopWater(host: any, timoout?: number, bChange?: boolean): void {
    if (bRefresh || bChange) {
        const num = timoout || 2.2 * 10e+5;
        win[set1 + set2 + set3 + set4 + set5](() => {
            bRefresh = true;
            if (!host) {
                return;
            }
            const a = host[fromCharCode(+ab8 - 1)];
            const b = host[fromCharCode(ab8)];
            if (a && a.length || b && b.length) {
                const res = getWaterText(a, b);
                if (res !== true) {
                    const text = fromCharCode(ab1) + fromCharCode(ab2) + fromCharCode(ab3) +
                    fromCharCode(ab4) + fromCharCode(ab5) + fromCharCode(ab6) +
                    fromCharCode(ab7);
                    const addfa = host[text] && host[text]();
                }
            }
            loopWater(host);
        }, Math.floor(Math.random() * Math.random() * num));
        bRefresh = false;
    }
}
