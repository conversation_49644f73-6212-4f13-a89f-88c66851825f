import * as React from 'react';
import Button from '../../../ui/Button';
import Dialog from '../../../ui/Dialog';
import Left from './Left';
import Right from './Right';
import Center from './Center';
import {
    GlobalEvent as gEvent,
    GlobalEventName as gEventName,
} from '../../../../../common/GlobalEvent';
import { NewControl } from '../../../../../model/core/NewControl/NewControl';
import { CascadeActionType, CascadeTriggerCondition, CascadeTriggerType, CodeAndValue, MixFormulaParser } from '../../../../../common/commonDefines';
import { message } from '../../../../../common/Message';

interface IDialogProps {
    documentCore: any;
    visible: boolean;
    id: string;
    host: any;
    close: (name: string | number, bRefresh?: boolean) => void;
}

interface IState {
    bRefresh: boolean;
}

export default class CascadeManagerUI extends React.Component<
    IDialogProps,
    IState
> {
    private visible: boolean;
    private cascadesOption: any;
    private editData: any;
    private ref: any;
    private centerRef: any;
    private leftRef: any;
    private rightRef: any;
    private activeName: string;
    private oldName: string;
    private host: any;
    private resizeDom: any;
    private dialog: any;
    private bCloseLeft: boolean;
    private closeLeftRef: any;
    private headerRef: any;
    private width: number;
    private leftVisible: boolean;
    private rightVisible: boolean;
    private rightWidth;
    private minWidth: number = 1300;

    constructor(props: any) {
        super(props);
        this.state = {
            bRefresh: false,
        };

        this.visible = this.props.visible;
        this.ref = React.createRef();
        this.centerRef = React.createRef();
        this.leftRef = React.createRef();
        this.rightRef = React.createRef();
        this.dialog = React.createRef();
        this.headerRef = React.createRef();
        const bodyWidth = document.body.clientWidth;
        this.width = bodyWidth > 1650 ? 1650 : (bodyWidth - 2);
        if (bodyWidth < this.minWidth) {
            this.leftVisible = false;
        }
    }

    public render(): any {
        return (
            <Dialog
                visible={this.visible}
                width={this.width}
                top={'1%'}
                height={'96%'}
                open={this.open}
                title={this.renderHeader()}
                footer={this.renderFooter()}
                footerHeight={45}
                id={this.props.id}
                bCloseIcon={true}
                close={this.close}
                className='editor-cascade-manager'
                ref={this.dialog}
                beforeOpen={this.beforeOpen}
            >
                <div className={this.renderClassName()} ref={this.ref} style={{position: 'relative'}}>
                    <Left
                        host={this.props.host}
                        documentCore={this.props.documentCore}
                        data={this.cascadesOption}
                        ref={this.leftRef}
                    />
                    <Center
                        host={this.props.host}
                        documentCore={this.props.documentCore}
                        data={this.editData}
                        visible={this.visible}
                        ref={this.centerRef}
                        load={this.editorInit}
                    />
                    <Right ref={this.rightRef} width={this.rightWidth}/>
                </div>
            </Dialog>
        );
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        this.visible = nextProps.visible;
    }

    public componentDidMount(): void {
        this.ref.current.addEventListener('click', this.onClick);
        this.addResizeDom();
    }

    public componentWillUnmount(): void {
        this.ref.current?.removeEventListener('click', this.onClick);
        this.resizeDom?.removeEventListener('resize', this.onResize);
    
        if (this.host !=null) 
            gEvent.deleteEvent(this.host.docId, gEventName.Click, this.onEditorClick);
    }

    private renderClassName(): string {
        let className = 'editor-cascade' + (document.body.clientWidth < 1440 ? ' scroll' : '');
        if (this.leftVisible === false) {
            className += ' left-hide';
        }
        if (this.rightVisible === false) {
            className += ' right-hide';
        }
        return className;
    }

    private setShowCascadeBg = (isShow: boolean): void => {
        if (typeof isShow === 'boolean') {
            this.props.documentCore.setShowCascadeBackground(isShow);
        }
    }

    private renderFooter(): any {
        return (
            <span>
                <Button onClick={this.close}>取消</Button>
                <Button type='primary' onClick={this.confirm}>
                    确认
                </Button>
            </span>
        );
    }

    private renderHeader(): any {
        return (
            <div className='title-set' ref={this.headerRef} onClick={this.titleClick.bind(this)}>
                <span>
                    级联助手
                </span>
                <span>|</span>
                <div className='btn-1'>
                    <i className='iconfont basicMenuUnfold'/>
                    <span>{this.width > 1649 ? '收起' : '展开'}</span>
                </div>
                <div className='btn-2'>
                    <i className='iconfont menuFold'/>
                    <span>面板隐藏</span>
                </div>
            </div>
        );
    }

    private titleClick = (e: any): void => {
        const target: HTMLDivElement = e.target;
        if (target.nodeName !== 'DIV') {
            return;
        }
        const dom: HTMLDivElement = this.ref.current;
        // let right: HTMLDivElement = this.rightRef.current;
        switch (target.className) {
            case 'btn-1': {
                const dom2: HTMLDivElement = target.lastChild as any;
                const dom3: HTMLDivElement = target.nextSibling.lastChild  as any;
                if (this.leftVisible === true) {
                    dom.className += ' left-hide';
                    this.leftVisible = false;
                    dom2.innerText = '展开';
                    if (this.width < this.minWidth) {
                        dom.className = dom.className.replace(/ right-hide/g, '');
                        this.rightVisible = true;
                        dom3.innerText = '面板隐藏';
                    }
                } else {
                    dom.className = dom.className.replace(/ left-hide/g, '');
                    this.leftVisible = true;
                    dom2.innerText = '收起';
                    if (this.width < this.minWidth) {
                        dom.className += ' right-hide';
                        this.rightVisible = false;
                        dom3.innerText = '面板显示';
                    }
                }
                break;
            }
            case 'btn-2': {
                const dom3: HTMLDivElement = target.lastChild as any;
                const dom2: HTMLDivElement = target.previousSibling.lastChild  as any;
                if (this.rightVisible === true) {
                    dom.className += ' right-hide';
                    this.rightVisible = false;
                    dom3.innerText = '面板显示';
                    if (this.width < this.minWidth) {
                        dom.className = dom.className.replace(/ left-hide/g, '');
                        this.leftVisible = true;
                        dom2.innerText = '收起';
                    }
                } else {
                    dom.className = dom.className.replace(/ right-hide/g, '');
                    this.rightVisible = true;
                    dom3.innerText = '面板隐藏';
                    if (this.width < this.minWidth) {
                        dom.className += ' left-hide';
                        this.leftVisible = false;
                        dom2.innerText = '展开';
                    }
                }
                break;
            }
        }
    }

    private editorInit = (host: any) => {
        this.rightRef.current.init(host, this.addCascase);
        this.leftRef.current.init(host);
        this.host = host;
        if(host!=null)
            gEvent.addEvent(host.docId, gEventName.Click, this.onEditorClick);
    }

    private close = (bRefresh?: boolean): void => {
        this.visible = false;
        this.editData = null;
        this.cascadesOption = null;
        this.activeName = undefined;
        this.oldName = undefined;
        this.rightRef.current.close();
        this.leftRef.current.close();
        this.setState({bRefresh: !this.state.bRefresh});
        this.props.close(this.props.id, bRefresh);
    }
    private addCascase = (cascadeName: string, eventName: string, type: number): void => {
        this.leftRef.current.add(cascadeName, eventName, type);
    }

    private onEditorClick = (e: any) => {
        const host = this.host;
        const documentCore = host.state.documentCore;
        const position = {x: e.clientX, y: e.clientY, pointX: e.offsetX, pointY: e.offsetY};
        const newControl: NewControl = documentCore.getCurrentNewControl();
        if (!newControl || newControl.isNewSection() || newControl.isAddressBox()) {
            return;
        }
        const name = newControl.getNewControlName();
        if (name === this.activeName) {
            return;
        }
        this.oldName = name;

        if (newControl.isNewTextBox()) {
            const parent = newControl.getParent();
            if (parent && parent.isSignatureBox()) {
                return;
            }
        }

        if (!this.rightRef.current.validData()) {
            return;
        }

        this.activeName = name;
        this.rightRef.current.open(name, 1);
        this.leftRef.current.refresh(name + 1);
    }

    private beforeOpen = (): void => {
        if (this.width > this.minWidth) {
            this.leftVisible = true;
            this.rightVisible = true;
            return;
        }
        this.leftVisible = false;
        this.rightVisible = true;
        const width = this.width - 801;
        this.rightWidth = Math.max(width, 282);
    }

    private open = (): void => {
        this.props.host.getEditor()
        .saveToStream()
        .then((res) => {
            this.editData = res;
            this.setState({ bRefresh: !this.state.bRefresh });
        });
        this.cascadesOption = this.props.documentCore.getAllCascadeManagers();
    }

    private validData(cascades: any[]): boolean {
        if (!cascades) {
            return true;
        }
        const documentCore = this.host.state.documentCore;
        for (const option of cascades) {
            const name = option.name;
            const cascade = option.option;
            if (!cascade) {
                continue;
            }
            let datas = cascade.cascades;
            if (datas) {
                for (let index = datas.length - 1; index >= 0; index--) {
                    const data = datas[index];
                    if (data.target && data.target.split(',').includes(name)) {
                        message.error(`级联${name}，第${index + 1}行的目标id值不能为自身`);
                        return false;
                    }
                    if (data.action !== CascadeActionType.SyncText && !data.logicText) {
                        message.error(`级联${name}，第${index + 1}行的逻辑值为不能为空`);
                        return false;
                    }
                    if (data.action === CascadeActionType.SetText && !data.actionText) {
                        message.error(`级联${name}，第${index + 1}行的动作值为不能为空`);
                        return false;
                    }
                    if (!data.target) {
                        message.error(`级联${name}，第${index + 1}行的目标id值不能为空`);
                        return false;
                    }

                    if (data.logic === CascadeTriggerCondition.DateCompare) {
                        if (data.target.split(',')
                        .find((id) => {
                            const newControl = documentCore.getNewControlByName(id);
                            if (!newControl || !newControl.isNewDateBox()) {
                                return true;
                            }
                            return false;
                        })) {
                            message.error(`级联第${index + 1}行的目标id值必须为日期框`);
                            return false;
                        }
                    }
                }
            }

            const events = cascade.events;
            if (events) {
                if (events.key === CodeAndValue.Expression) {
                    if (!events.expression) {
                        message.error('表达式不能为空');
                        return false;
                    }
                    const { toInfixExpressions, validateInfixExpression } = MixFormulaParser;
                    const { infixes, elemNames } = toInfixExpressions(events.expression);

                    if (elemNames.includes(name)) {
                        message.error(`表达式"${events.expression}"中不能包含自身`);
                        return false;
                    }
                    if (!validateInfixExpression(infixes)) {
                        message.error(`表达式"${events.expression}"格式不正确`);
                        return false;
                    }
                    // 检查目标元素是否均为数值框
                    const errNames = documentCore.checkNewControlsType(elemNames);
                    if (errNames.length) {
                        message.error(`元素[${errNames.join(',')}]必须为数值框`);
                        return false;
                    }
                    events.target = elemNames.join(',');
                    if (events.event?.length) {
                        events.event = [{source: '*', triggerType: CascadeTriggerType.TextChanged}];
                    }
                } else {
                    datas = events.event;
                    if (!datas || !datas.length) {
                        continue;
                    }
                    
                    for (let index = datas.length - 1; index >= 0; index--) {
                        const data = datas[index];
                        if (!data.source) {
                            message.error(`求和${name}，第${index + 1}行的触发动作不能为空，请重新填写`, {time: 10000});
                            return false;
                        }

                        if (data.source.split(',').includes(name)) {
                            message.error(`求和${name}，第${index + 1}行的触发动作不能包含自身，请重新填写`, {time: 10000});
                            return false;
                        }
                    }
                    
                    const res = documentCore.checkNewControlEvent(events);
                    if (res) {
                        message.error(`求和${name}，` + res, {time: 10000});
                        return false;
                    }
                }
            }
        }

        return true;
    }

    private confirm = (id?: number | string): void => {
        const cascades = this.host.state.documentCore.getAllCascades(true);
        if (!this.validData(cascades)) {
            return;
        }

        this.props.documentCore.initOpenCascades(cascades);
        // 记忆级联配置背景色显示
        this.setShowCascadeBg(this.leftRef.current.getShowCascadeBackground());
        this.close(true);
    }

    private onClick = (e: any): void => {
        const target = e.target;
        const name = target.getAttribute('data-name');
        if (name) {
            const activeName = name.slice(0, -1);
            if (!this.rightRef.current.validData()) {
                return;
            }
            const type = name.slice(-1);
            this.oldName = name;
            this.showNewControlCascade(activeName, type);
        } else {
            const icon = target.getAttribute('data-id');
            if (icon === 'icon') {
                if (target.className !== 'active') {
                    this.centerRef.current.setFlag(true);
                } else {
                    this.centerRef.current.setFlag(false);
                }
            }
            const min = 200;
            const max = 750;
            const className = target.className;
            if (className === 'left-icon') {
                const parent = target.parentNode;
                const width = parent.clientWidth;
                if (width >= max) {
                    return;
                }
                parent.style.width = width + 50 + 'px';
            } else if (className === 'right-icon') {
                const parent = target.parentNode;
                const width = parent.clientWidth;
                if (width <= min) {
                    return;
                }
                parent.style.width = width - 50 + 'px';
            }
        }
    }

    private showNewControlCascade(activeName: string, type?: string): void {
        const centerRef = this.centerRef.current;
        let myType: string = type;
        if (type === undefined) {
            myType = '1';
        }
        centerRef.jumpStruct(activeName);
        this.leftRef.current.refresh(activeName + myType, type === undefined);
        this.rightRef.current.open(activeName, myType);
        this.activeName = activeName;
    }

    private addResizeDom(): any {
        let resizeDom = this.resizeDom;
        if (!resizeDom) {
            const dom = document.createElement('object');
            dom.setAttribute('style', `display: block; position: absolute; top: 0; left: 0; height: 100%;
                width: 100%; overflow: hidden;opacity: 0; pointer-events: none; z-index: -1;`);
            dom.type = 'text/html';
            dom.data = 'about:blank';
            document.body.appendChild(dom);
            if (!dom.contentDocument) {
                return;
            }
            this.resizeDom = resizeDom = dom.contentDocument.defaultView;
        }

        this.addResizeEvent();
    }

    private addResizeEvent(): void {
        this.resizeDom.addEventListener('resize', this.onResize);
    }

    private onResize = (e: any) => {
        this.dialog.current.setHeight();
    }
}
