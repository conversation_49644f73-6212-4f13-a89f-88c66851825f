import { escapeXML , unescapeXML } from './xml';

describe('Test escapeXML & unescapeXML', function () {
  it('should escapeXML & unescapeXML valid', function () {
    const input = `我是"谁";年龄 > 10'岁', 身高 < "180cm", 喜欢music&movie; who is 'she', age < 10 & >= 20`;
    const output = `我是&quot;谁&quot;;年龄 &gt; 10&apos;岁&apos;, 身高 &lt; &quot;180cm&quot;, 喜欢music&amp;movie; who is &apos;she&apos;, age &lt; 10 &amp; &gt;= 20`;

    expect(escapeXML(input)).toEqual(output);
    expect(unescapeXML(output)).toEqual(input);


    const normal = "我是正常字符，无特殊字符..，，，，1234567890-=qwertyuioop[]\asddfghjkklk;zxcvbnm,./?~!@#$%^*()_+{[}}]]【】「」？》《?/}";
    expect(escapeXML(normal)).toEqual(normal);
    expect(unescapeXML(normal)).toEqual(normal);
  });
});
