import { IMonitorElementAction, IMonitorElementConfig, IMonitorElementRecord, MonitorAction, MonitorActionMap, MonitorEvent } from '@/common/commonDefines';
import { getCurTime } from '@/common/commonMethods';
import { DocumentContentType } from './Style';

/**
 * 元素埋点
 */
export class ElementMonitor {
    private doc: any;
    private patientID: string;
    private apoName: string;
    private apoId: string;
    private outpatientNum: string;
    private hash: string;

    private elementConfigs: IMonitorElementConfig[];
    private elementRecords: IMonitorElementRecord[];

    private curMonitorElement: {
        serialNumber: string,
        start: string,
        end?: string,
        preContent: string,
        endContent: string,
        historyIndex?: number,
        element?: any,
        type?: MonitorAction,
        newControl?: any;
    }

    constructor(doc: any, config?: string) {
        this.doc = doc;
        this.setConfig(config);
    }

    /**
     * open接口信息设置
     * @param config 主ID设置
     */
    public setConfig(config: any): void {
        if (config) {
            this.patientID = config.patientID;
            this.apoName = config.apoName;
            this.outpatientNum = config.outpatientNum;
            this.apoId = config.apoID;
        }

        this.elementConfigs = [];
        this.elementRecords = [];
    }

    /**
     * 设置监控元素配置信息
     * @param elementConfig 
     */
    public setElementConfig(elementConfig?: IMonitorElementConfig): void {
        this.getElementConfigFromServer(this.loadElementConfigCache());
    }

    /**
     * 获取本地缓存配置信息
     */
    public loadElementConfigCache(): boolean {
        let bFlag = false;
        try {
            const configs = localStorage.getItem('monitorConfigs');
            const obj = JSON.parse(configs);
            if (obj) {
                this.hash = obj.hash;
                this.elementConfigs = [];
                obj.configs?.forEach((config) => {
                    this.elementConfigs.push({
                        name: config.name,  
                        element: config.element,
                        event: config.event,
                        action: config.action,
                    });
                });

                bFlag = true;
            }
        } catch (error) {
            bFlag = false;
        }

        return bFlag;
    }

    /**
     * 写入本地缓存
     */
    public writeElementConfigCache(): void {
        try {
            const configs = localStorage.getItem('monitorConfigs');
            const obj = JSON.parse(configs);
            if (!configs || (configs && obj?.hash !== this.hash)) {
                localStorage.setItem('monitorConfigs', JSON.stringify({
                    hash: this.hash,
                    configs: this.elementConfigs,
                }));
            } 
        } catch (error) {
            ;
        }
    }

    /**
     * 记录每次动作
     * @param name 元素显示名称serialNumber
     * @param action 动作内容
     */
    public addAction(name: string, action: IMonitorElementAction, bEnd?: boolean): void {
        if (!this.elementRecords) {
            this.elementRecords = [];
        }

        name = name ? name : this.curMonitorElement?.serialNumber;

        if (!this.hasMonitorNameForConfigs(name)) {
            return ;
        }

        const index = this.elementRecords.findIndex((item) => {
            return item.id === name;
        });

        if (-1 !== index) {
            const record = this.elementRecords[index];
            record.actions.push(action);
        } else {
            this.elementRecords.push({id: name, actions: [action]});
        }

        if (this.curMonitorElement) {
            const element = this.curMonitorElement;
            const content = (element.element ? element.element.getSelectText(true) :
                                element.newControl.getMonitorText());
            element.preContent = content;
            element.historyIndex = this.doc.getHistory()
                                                    .getCurPointIndex();
        }
    }

    /**
     * 进行插入、粘贴、替换action之前，需要push当前已编辑的内容
     */
    public addEditAction(): void {
        const element = this.curMonitorElement;
        if (element?.serialNumber && element.historyIndex !== this.doc.getHistory()
                .getCurPointIndex()) {
            const content = (element.element ? element.element.getSelectText(true) :
                                element.newControl.getMonitorText());
            this.addAction(undefined, {
                type: element.type || MonitorAction.Edit,
                start: element.start,
                end: getCurTime(),
                textLen: content.length - this.curMonitorElement.preContent.length,
            })
        }
    }

    /**
     * 是否记录当前动作
     * @returns
     */
    public canAddAction(type: MonitorAction): boolean {
        if (!this.patientID || !this.apoName || !this.elementConfigs || 0 === this.elementConfigs.length) {
            return false;
        }

        return (-1 !== this.elementConfigs.findIndex((config) => {
            return (config.name === this.apoName && -1 !== config.action?.findIndex((action) => {
                return type === MonitorActionMap[action];
            }));
        }));
    }

    /**
     * 开始监控元素
     * @param name 元素名称
     * @param content 当前元素内容
     */
    public startMonitorElement(newControl: any): void {
        const element = this.getCurMonitorElement(newControl);
        const serialNumber = element?.getSerialNumber();
        const curElement = this.curMonitorElement;

        // 在region内操作：region切换到元素，得先结束当前region的监控
        if (curElement?.element) {
            const history = this.doc.getHistory();

            // 不同区域间切换
            // serialNumber = serialNumber || this.curMonitorElement.element.getSerialNumber(); 
            if (this.curMonitorElement.element !== element
                && history.getCurPointIndex() !== this.curMonitorElement.historyIndex) {
                const content = this.curMonitorElement.element.getSelectText(true);
                this.addAction(this.curMonitorElement.element.getSerialNumber(), // || serialNumber,
                    { type: this.curMonitorElement.type || MonitorAction.Edit,
                    start: this.curMonitorElement.start,
                    end: getCurTime(),
                    textLen: content.length - this.curMonitorElement.preContent.length,
                });
            }
        }

        if (serialNumber) {
            // 区域间切换，或元素切换到区域，或元素之间切换
            // 区域切换到元素，且元素与区域serialNumber相同
            if ((serialNumber !== curElement?.serialNumber
                && element !== curElement?.element) ||
                (serialNumber === curElement?.serialNumber
                && element !== curElement?.element
                && element.getType() !== curElement?.element?.getType())) {
                const bRegion = DocumentContentType.Region === element.getType();
                const content = (bRegion ? element.getSelectText(true) : element.getMonitorText());
                this.curMonitorElement = {
                    serialNumber: serialNumber,
                    start: getCurTime(),
                    preContent: content,
                    endContent: content,
                    element: bRegion ? element : null,
                    newControl: !bRegion ? element : null,
                    historyIndex: this.doc.getHistory()
                                            .getCurPointIndex(),
                };
            }
        } else if (!this.curMonitorElement?.element || !element) {
            if (!element && this.curMonitorElement && !this.curMonitorElement.element
                && DocumentContentType.Region === newControl?.getType()) {
                return;
            }
            this.curMonitorElement = undefined;
        }
    }

    /**
     * 结束监控元素
     * @param name 元素名称
     * @param content 当前元素内容
     */
    public endMonitorElement(newControl: any): void {
        const element = this.getCurMonitorElement(newControl);
        const serialNumber = element?.getSerialNumber();
        const history = this.doc.getHistory();
        const curElement = this.curMonitorElement;

        // 监听从区域内的元素切换到元素（非区域内）时，由于区域内的元素会先进行此end操作，
        // 因此，需要先add区域的action
        if (element && element === newControl && curElement?.element
            && element !== curElement.element
            && element.getType() !== curElement.element.getType()) {
            if (element.isInRegion(curElement.element)
                && history.getCurPointIndex() !== curElement?.historyIndex) {
                const content = curElement.element.getSelectText(true);
                this.addAction(serialNumber, {
                    type: MonitorAction.Edit,
                    start: curElement.start,
                    end: getCurTime(),
                    textLen: content.length - curElement.preContent.length,
                }, true);
            }
        } else if (serialNumber && curElement?.serialNumber === serialNumber) {
            // 区域内有编辑
            if (history.getCurPointIndex() !== curElement?.historyIndex) {
                const bRegion = DocumentContentType.Region === element.getType();

                // 区域内：重名元素切换
                if (curElement.element && !bRegion) {
                    return ;
                }

                const content = (bRegion ? element.getSelectText(true) : element.getMonitorText());
                this.addAction(serialNumber, {
                    type: MonitorAction.Edit,
                    start: curElement.start,
                    end: getCurTime(),
                    textLen: content.length - curElement.preContent.length,
                }, true);
            }
            
            // 如果光标仍在监控区域内，则nothing to do
            // 或者光标已经切换到其他元素内（非区域）
            if (curElement.element === element && element !== newControl) {
                return ;
            } else if (DocumentContentType.Region === element.getType() && !curElement.element) {
                return;
            }
        } else if (curElement && !curElement.element && DocumentContentType.Region === newControl.getType()) {
            // 区域切换元素
            return;
        } else if (curElement && !curElement.element) {
            // 元素切换区域
            if (curElement.serialNumber === newControl?.getSerialNumber() &&
                history.getCurPointIndex() !== curElement?.historyIndex) {
                const content = newControl.getMonitorText();
                this.addAction(curElement.serialNumber, {
                    type: MonitorAction.Edit,
                    start: curElement.start,
                    end: getCurTime(),
                    textLen: content.length - curElement.preContent.length,
                }, true);
            }
        } else if (curElement && curElement.element) {
            // 从监听区域内的非监听元素切换到其他监听区域
            // 从监听区域内的非监听元素切换到元素（非本区域）
            if ((serialNumber && element?.getParentRegion) || !element) {
                if (history.getCurPointIndex() !== curElement?.historyIndex) {
                    const content = curElement.element.getSelectText(true);
                    this.addAction(curElement.serialNumber, {
                        type: MonitorAction.Edit,
                        start: curElement.start,
                        end: getCurTime(),
                        textLen: content.length - curElement.preContent.length,
                    }, true);
                }
            }
        }

        this.curMonitorElement = undefined;
    }

    /**
     * 是否记录当前事件
     * @returns
     */
    public canAddEvent(element: any, type: MonitorEvent): boolean {
        if (!this.patientID || !this.apoName || !this.elementConfigs || 0 === this.elementConfigs.length) {
            return false;
        }

        return (-1 !== this.elementConfigs.findIndex((config) => {
            return (config.name === this.apoName && -1 !== config.event?.findIndex((event) => {
                return type === event;
            }));
        }));
    }

    public addEvent(element: any, type: MonitorEvent): void {
        if (this.canAddEvent(element, type)) {
            if (!this.elementRecords) {
                this.elementRecords = [];
            }
    
            // name = name ? name : this.curMonitorElement?.serialNumber;
    
            // const index = this.elementRecords.findIndex((item) => {
            //     return item.id === name;
            // });
    
            // if (-1 !== index) {
            //     const record = this.elementRecords[index];
            //     record.events.push({
            //         start: getCurTime(),
            //         type
            //     });
            // } else {
            //     this.elementRecords.push({id: name, events: [{
            //         start: getCurTime(),
            //         type
            //     }]});
            // }
        }
    }

    public getAllRecords(): IMonitorElementRecord[] {
        return this.elementRecords;
    }

    public sendAllRecords(bInterface?: boolean): void {
        if (bInterface) {
            this.checkRecords();
        }

        const records = this.getAllRecords();

        if (records?.length) {
            new Promise((resolve, reject) => {
                const fetchUrl = `/monitor?hash=${this.hash}`;

                fetch(fetchUrl, {
                    method: 'POST',
                    // mode: 'cors',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        apoName: this.apoName,
                        patientID: this.patientID,
                        records,
                        outpatientNum: this.outpatientNum,
                        apoId: this.apoId,
                    }),
                })
                // .then((res) => res.json())
                .then((result) => {
                    if (200 === result.status) {
                        // 发送成功后，清除当前记录
                        this.elementRecords = [];
                    }

                    // console.log(result);
                    resolve(result);
                })
                .catch((error) => {
                    console.log(error);
                });
            });
        }
    }

    public reset(): void {
        this.patientID = '';
        this.apoName = '';
        this.apoId = '';
        this.outpatientNum = '';
        this.elementConfigs = [];
        this.elementRecords = [];
    }

    /**
     * 是否监听元素及其动作类型
     * @param name
     * @param type
     * @returns
     */
    public isMonitorElement(name: string, type: MonitorAction): boolean {
        return (this.canAddAction(type) && this.hasMonitorNameForConfigs(name));
    }

    /**
     * 非光标处，获取当前监听元素
     * @param element
     * @returns
     */
    public getMonitorElement(element: any): any {
        const name = element?.getSerialNumber();

        if (this.hasMonitorNameForConfigs(name)) {
            return element;
        } else {
            if (element.getLeafList) {
                // 元素：先检查是否匹配元素
                // 不匹配，则匹配元素所在区域
                let parent = element.getParent();
                while (parent) {
                    if (this.hasMonitorNameForConfigs(parent?.getSerialNumber())) {
                        return parent;
                    }
                    parent = parent.getParent();
                }

                parent = element.getDocumentParent();
                if (parent.isTableCellContent()) {
                    parent = parent.getTable()
                                .parent;
                }

                parent = parent?.parent;

                if (parent?.getType && DocumentContentType.Region === parent.getType()) {
                    if (this.hasMonitorNameForConfigs(parent?.getSerialNumber())) {
                        return parent;
                    } else {
                        parent = parent.getTopParent();
                        if (this.hasMonitorNameForConfigs(parent?.getSerialNumber())) {
                            return parent;
                        }
                    }
                }
            } else if (element?.getType && DocumentContentType.Region === element.getType()) {
                const parent = element.getTopParent();
                if (this.hasMonitorNameForConfigs(parent?.getSerialNumber())) {
                    return parent;
                }
            }
        }

        return null;
    }

    /**
     * 从server获取元素配置信息，并写入本地缓存,
     * 如果加载了本地缓存数据，需要传递hash值和server的进行比对，
     * status返回值为304，则无需变更，否则需要进行更新元素配置信息
     * @param bLoad 是否已经加载本地缓存数据
     * @returns
     */
    private getElementConfigFromServer(bLoad: boolean): Promise<any> {
        return new Promise((resolve, reject) => {
            const fetchUrl = bLoad ? `/monitor?hash=${this.hash}` :
                                    `/monitor`;

            fetch(fetchUrl, {method: 'get'})
            .then((res) => {
                if (304 === res.status) {
                    resolve(res);
                } else {
                    // const result = res;
                    if (200 === res.status) {
                        res.json().then((result) => {
                            if (result) {
                                this.hash = result.hash;
                                this.elementConfigs = [];
            
                                result.config?.forEach((config) => {
                                    this.elementConfigs.push({
                                        name: config.name,  
                                        element: config.element,
                                        event: config.event,
                                        action: config.action,
                                    });
                                });

                                // console.log(result)
                                this.writeElementConfigCache();
                            }
                            resolve(result);
                        });
                    }
                }
            })
            .catch((error) => {
                console.log(error);
            });
        });
    }

    /**
     * 获取当前监听的元素
     * 监听规则：优先顺序 region > section > 其他
     * @param newControl 光标所在元素
     */
    private getCurMonitorElement(newControl: any): any {
        const name = newControl?.getSerialNumber();

        if (newControl.getParentRegion && DocumentContentType.Region === newControl.getType()) {
            if (this.hasMonitorNameForConfigs(name)) {
                return newControl;
            } else {
                const parent = newControl.getTopParent();
                if (this.hasMonitorNameForConfigs(parent?.getSerialNumber())) {
                    return parent;
                }
            }
        } else {
            // 先匹配region
            const region = this.doc.getCursorInRegion();
            if (region) {
                if (this.hasMonitorNameForConfigs(region.getSerialNumber())) {
                    return region;
                } else {
                    const parent = region.getTopParent();
                    if (this.hasMonitorNameForConfigs(parent?.getSerialNumber())) {
                        return parent;
                    }
                }
            }

            // 当前光标处，无region匹配，则考虑元素
            if (this.hasMonitorNameForConfigs(name)) {
                return newControl;
            } else {
                let parent = newControl.getParent();
                while (parent) {
                    if (this.hasMonitorNameForConfigs(parent?.getSerialNumber())) {
                        return parent;
                    }
                    parent = parent.getParent();
                }
            }
        }

        return null;
    }

    private hasMonitorNameForConfigs(name: string): boolean {
        if (!name) {
            return false;
        }

        return (-1 !== this.elementConfigs?.findIndex((config) => {
            return (config.name === this.apoName && -1 !== config.element?.findIndex((item) => {
                return item === name }))}));
    }

    /**
     * 关闭文档当前，检查当前文档是否还有未push的记录
     */
    private checkRecords(): void {
        if (this.curMonitorElement?.serialNumber) {
            const history = this.doc.getHistory();
            if (history.getCurPointIndex() !== this.curMonitorElement?.historyIndex) {
                const element = this.curMonitorElement.element || this.curMonitorElement.newControl;
                const content = (this.curMonitorElement.element ? element.getSelectText(true) : element.getMonitorText());
                this.addAction(this.curMonitorElement.serialNumber, {
                    type: MonitorAction.Edit,
                    start: this.curMonitorElement.start,
                    end: getCurTime(),
                    textLen: content.length - this.curMonitorElement.preContent.length,
                }, true);
            }
        };
    }

}
