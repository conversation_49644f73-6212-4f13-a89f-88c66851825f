import * as React from 'react';
import Dialog from '../../ui/Dialog';
import But<PERSON> from '../../ui/Button';
import { SelectColor } from '../../ui/SelectColor';
import '../../style/tableSetting.less';
import '../../style/newTableSetting.less';

import { TABLE_BORDER_LINE_TYPES, ITableBorder, TableBorderSettingType,
    CustomPropertyElementType,
    isValidName,
    colorRgb,
    EnableRowActionType} from '../../../../common/commonDefines';
import CustomPropertyBtn from './CustomProperty';
import { message } from '../../../../common/Message';
import { IFRAME_MANAGER } from '../../../../common/IframeManager';

interface IDialogProps {
    documentCore: any;
    tableProps?: any;
    visible: boolean;
    id?: string;
    tableName?: string;
    close: (name: string | number, bRefresh?: boolean) => void;
    refresh: (bRefresh: boolean, timeout?: number) => void;
}

interface IState {
    bRefresh: boolean;
}

interface IStyle {
    left: number;
}

enum TablePropsDialogTabType {
    Table = 0,
    RowHeight,
    ColumnWidth,
    TableBorderLine,
}

export default class TableSettingDialog extends React.Component<IDialogProps, IState> {
    private table: any;
    private cellMargins: any;
    private column: any;
    private row: any;
    // private cell: any;
    private tabIndex: TablePropsDialogTabType;
    private tableProps: any;
    private visible: any;
    private borderLine: any;
    private pickColor: any;
    private pickColorStyle: IStyle;
    private bCustomProperty: boolean;
    private boxRef: any;
    private bActiveColor: boolean;
    private oldTableProps: any;
    private tableBorders: {
        top?: ITableBorder;
        bottom?: ITableBorder;
        left?: ITableBorder;
        right?: ITableBorder;
        insideH?: ITableBorder;
        insideV?: ITableBorder;
        type?: number;
    };

    constructor(props: any) {
        super(props);
        this.table = {
            tableName: '',
            customProperty: undefined,
            bCanAddRow: true,
            bCanDeleteRow: true,
            bFixedRowHeight: false,
            bFixedColWidth: false,
            bDeleteProtect: false,
            bReadOnly: false,
            bRepeatHeader: false,
            repeatHeaderNum: 0,
            rowsAuto: [],
            enableRowAction: undefined,
        };
        this.tableProps = null;

        this.cellMargins = {
            top: 0,
            bottom: 0,
            left: 0,
            right: 0,
        };
        this.state = {
            bRefresh: false,
        };
        this.row = {
            rowIndex: 1,
            rowHeights: [],
            rowHeight: 0,
            bAuto: true,
            length: 1,
        };
        this.column = {
            columnIndex: 1,
            columnWidths: [],
            columnWidth: 0,
            length: 1,
        };
        // this.cell = {
        //     ff: 0,
        // };
        this.borderLine = {
            type: 1,
            color: '#000000',
            style: '',
            bg: [],
        };

        this.tabIndex = TablePropsDialogTabType.Table;
        this.visible = this.props.visible;
        this.pickColor = 'pick-color';
        this.pickColorStyle = {left: 0};

        this.tableBorders = {
            top: undefined,
            bottom: undefined,
            left: undefined,
            right: undefined,
            insideH: undefined,
            insideV: undefined,
            type: -1,
        };
        this.boxRef = React.createRef();
        // this.setDialogValue();
    }

    public componentDidMount(): void {
        // this.setState({bRefresh: !this.state.bRefresh});
        this.boxRef.current.ownerDocument.addEventListener('click', this.docClick);
    }

    public componentWillUnmount(): void {
        this.boxRef.current?.ownerDocument?.removeEventListener('click', this.docClick);
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        this.visible = nextProps.visible;
    }

    public render(): any {

        return (
            <Dialog
                id={this.props.id}
                visible={this.visible}
                width={400}
                height={466}
                // close={this.close}
                open={this.open}
                preventDefault={false}
                title='表格属性'
                confirm={this.confirm}
                footer={this.renderFooter()}
                // id='table'
            >
                <div className='editor-tableSetting' ref={this.boxRef}>
                    <div className='editor-tabs'>
                        <ul>
                            <li
                                className={this.tabIndex === TablePropsDialogTabType.Table ? 'active' : null}
                                onClick={this.tabClick.bind(this, TablePropsDialogTabType.Table)}
                            >
                                表格
                            </li>
                            <li
                                className={this.tabIndex === TablePropsDialogTabType.RowHeight ? 'active' : null}
                                onClick={this.tabClick.bind(this, TablePropsDialogTabType.RowHeight)}
                            >
                                行高
                            </li>
                            <li
                                className={this.tabIndex === TablePropsDialogTabType.ColumnWidth ? 'active' : null}
                                onClick={this.tabClick.bind(this, TablePropsDialogTabType.ColumnWidth)}
                            >
                                列宽
                            </li>
                            <li
                                className={this.tabIndex === TablePropsDialogTabType.TableBorderLine ? 'active' : null}
                                onClick={this.tabClick.bind(this, TablePropsDialogTabType.TableBorderLine)}
                            >
                                边框线
                            </li>
                        </ul>
                    </div>
                    <div className='editor-tabs-content'>
                        <ul className='table'>
                            <li className={this.tabIndex === TablePropsDialogTabType.Table ? 'active' : null}>
                                <div className='new-full-line'>
                                    <span className='table-label'>表格名：</span>
                                    <input
                                        value={this.table.tableName}
                                        onChange={this.textChange.bind(this, 'table', 'tableName')}
                                    />
                                </div>
                                <div className='new-full-line'>
                                    <span className='table-label'>样式保护：</span>
                                    <span className='table-inline-block'>
                                        <input
                                            checked={true === this.table.bCanAddRow}
                                            type='checkbox'
                                            onChange={this.checkChange.bind(this, 'table', 'bCanAddRow')}
                                        />
                                        <label>允许行新增</label>
                                    </span>
                                    <span className='table-inline-block'>
                                        <input
                                            checked={true === this.table.bCanDeleteRow}
                                            type='checkbox'
                                            onChange={this.checkChange.bind(this, 'table', 'bCanDeleteRow')}
                                        />
                                        <label>允许行删除</label>
                                    </span>
                                </div>
                                <div className='new-full-line'>
                                    <span className='table-label'>表格属性：</span>
                                    <span className='table-inline-block'>
                                        <input
                                            checked={true === this.table.bFixedRowHeight}
                                            type='checkbox'
                                            onChange={this.checkChange.bind(this, 'table', 'bFixedRowHeight')}
                                        />
                                        <label>固定行高</label>
                                    </span>
                                    <span className='table-inline-block'>
                                        <input
                                            checked={true === this.table.bFixedColWidth}
                                            type='checkbox'
                                            onChange={this.checkChange.bind(this, 'table', 'bFixedColWidth')}
                                        />
                                        <label>固定列宽</label>
                                    </span>
                                </div>
                                <div className='new-full-line'>
                                    <span className='table-label'/>
                                    <span className='table-inline-block'>
                                        <input
                                            checked={true === this.table.bDeleteProtect}
                                            type='checkbox'
                                            onChange={this.checkChange.bind(this, 'table', 'bDeleteProtect')}
                                        />
                                        <label>表格禁删除</label>
                                    </span>
                                    <span className='table-inline-block'>
                                        <input
                                            checked={true === this.table.bReadOnly}
                                            type='checkbox'
                                            onChange={this.checkChange.bind(this, 'table', 'bReadOnly')}
                                        />
                                        <label>表格禁编辑</label>
                                    </span>
                                </div>
                                <div className='new-full-line'>
                                    <span className='table-label'/>
                                    <span className='table-inline-block'>
                                        <input
                                            checked={true === this.table.bRepeatOnBreak}
                                            type='checkbox'
                                            onChange={this.checkChange.bind(this, 'table', 'bRepeatOnBreak')}
                                        />
                                        <label>跨页重复</label>
                                    </span>
                                    <span className='table-inline-block'>
                                        <input
                                            checked={true === this.table.bPrintEmptyRow}
                                            type='checkbox'
                                            onChange={this.checkChange.bind(this, 'table', 'bPrintEmptyRow')}
                                        />
                                        <label>打印空行</label>
                                    </span>
                                </div>
                                <div className='new-full-line'>
                                    <span className='table-label'>表头：</span>
                                    <span className='table-inline-block'>
                                        <input
                                            checked={false === this.table.bRepeatHeader}
                                            type='checkbox'
                                            onChange={this.checkChange.bind(this, 'table', 'bRepeatHeader')}
                                        />
                                        <label>重复表头</label>
                                    </span>
                                    <span className='table-inline-block'>
                                        <label>前</label>
                                        <input
                                            type='number'
                                            min={0}
                                            step={1}
                                            value={this.table.repeatHeaderNum}
                                            style={{width: '50px'}}
                                            onChange={this.numChange.bind(this, 'table', 'repeatHeaderNum')}
                                        />
                                        <label>行</label>
                                    </span>
                                </div>
                                <div className='new-full-line'>
                                    <span className='table-label'>行操作符</span>
                                    <span className='right-auto'>
                                        <input
                                            checked={true === this.table.enableRowAction}
                                            type='checkbox'
                                            onChange={this.checkChange.bind(this, 'table', 'enableRowAction')}
                                        />
                                        <label>启用操作符(会隐藏非标题首行)</label>
                                    </span>
                                </div>
                                <div className='new-full-line'>
                                    <span className='table-label'>单元格边距：</span>

                                    <span className='table-inline-block'>
                                        <span className='common-label'>上</span>
                                        <input
                                            type='number'
                                            min={0}
                                            step={0.1}
                                            value={this.cellMargins.top}
                                            style={{width: '50px'}}
                                            onChange={this.numChange.bind(this, 'cellMargins', 'top')}
                                        />
                                        <span>cm</span>
                                    </span>

                                    <span className='table-inline-block'>
                                    <span className='common-label'>左</span>
                                    <input
                                        type='number'
                                        min={0}
                                        step={0.1}
                                        style={{width: '50px'}}
                                        value={this.cellMargins.left}
                                        onChange={this.numChange.bind(this, 'cellMargins', 'left')}
                                    />
                                    <span>cm</span>
                                    </span>
                                </div>
                                <div className='new-full-line'>
                                    <span className='table-label'/>
                                    <span className='table-inline-block'>
                                        <span className='common-label'>下</span>
                                        <input
                                            type='number'
                                            min={0}
                                            step={0.1}
                                            style={{width: '50px'}}
                                            value={this.cellMargins.bottom}
                                            onChange={this.numChange.bind(this, 'cellMargins', 'bottom')}
                                        />
                                        <span>cm</span>
                                    </span>

                                    <span className='table-inline-block'>
                                        <span className='common-label'>右</span>
                                        <input
                                            type='number'
                                            min={0}
                                            step={0.1}
                                            style={{width: '50px'}}
                                            value={this.cellMargins.right}
                                            onChange={this.numChange.bind(this, 'cellMargins', 'right')}
                                        />
                                        <span>cm</span>
                                    </span>
                                </div>

                                <div className='new-full-line'>
                                    <CustomPropertyBtn
                                        name='customProperty'
                                        properties={this.table.customProperty}
                                        documentCore={this.props.documentCore}
                                        onChange={this.onChange}
                                        close={this.onClose}
                                        id='bCustomProperty'
                                        visible={this.bCustomProperty}
                                        type={CustomPropertyElementType.Table}
                                    />
                                </div>
                            </li>
                            <li className={this.tabIndex === TablePropsDialogTabType.RowHeight ? 'active' : null}>
                                <div className='new-full-line'>
                                    <span className='table-label'>第{this.row.rowIndex}行</span>

                                    <input
                                        checked={this.checkedRowHeightAuto()}
                                        type='checkbox'
                                        onChange={this.checkChange.bind(this, 'row', 'bAuto')}
                                    />
                                    <label>自动调整</label>
                                </div>
                                <div className='new-full-line'>
                                    <label className='table-label'>高度:</label>
                                    <input
                                        type='number'
                                        min={0}
                                        step={0.1}
                                        disabled={this.table.bFixedRowHeight || this.row.bAuto}
                                        value={this.row.rowHeight}
                                        onChange={this.numChange.bind(this, 'row', 'rowHeight')}
                                    />
                                    <span>cm</span>
                                </div>
                                <div className='new-full-line'>
                                    <button
                                        className='prevRow'
                                        style={{left: '40px'}}
                                        disabled={1 >= this.row.rowHeights.length ? true : false}
                                        onClick={this.handlePrevNextIndex.bind(this, 'prevRow')}
                                    >
                                        上一行
                                    </button>
                                    <button
                                        className='nextRow'
                                        style={{right: '-20px'}}
                                        disabled={1 >= this.row.rowHeights.length ? true : false}
                                        onClick={this.handlePrevNextIndex.bind(this, 'nextRow')}
                                    >
                                        下一行
                                    </button>
                                </div>
                            </li>
                            <li className={this.tabIndex === TablePropsDialogTabType.ColumnWidth ? 'active' : null}>
                                <div className='new-full-line'>
                                    <span className='table-label'>第{this.column.columnIndex}列</span>
                                </div>
                                <div className='new-full-line'>
                                    <label className='table-label'>宽度:</label>
                                    <input
                                        type='number'
                                        min={0}
                                        step={0.1}
                                        value={this.column.columnWidth}
                                        disabled={this.table.bFixedColWidth}
                                        onChange={this.numChange.bind(this, 'column', 'columnWidth')}
                                    />
                                    <span>cm</span>
                                </div>
                                <div className='new-full-line'>
                                    <button 
                                        className='prevColumn'
                                        style={{left: '40px'}}
                                        onClick={this.handlePrevNextIndex.bind(this, 'prevColumn')}
                                    >
                                        上一列
                                    </button>
                                    <button
                                        className='nextColumn'
                                        style={{right: '-20px'}}
                                        onClick={this.handlePrevNextIndex.bind(this, 'nextColumn')}
                                    >
                                        下一列
                                    </button>
                                </div>
                            </li>
                            <li className={this.tabIndex === TablePropsDialogTabType.TableBorderLine ? 'active' : null}>
                                <div className='new-full-line'>
                                    <span className='table-label'>线型：</span>
                                    <select
                                        // data={TABLE_BORDER_LINE_TYPES}
                                        value={this.borderLine.type}
                                        // style={{width: 80}}
                                        onChange={this.textChange.bind(this, 'borderLine', 'type')}
                                    >
                                        <option value={0}>{TABLE_BORDER_LINE_TYPES[0].key}</option>
                                        <option value={1}>{TABLE_BORDER_LINE_TYPES[1].key}</option>
                                        <option value={2}>{TABLE_BORDER_LINE_TYPES[2].key}</option>
                                        <option value={3}>{TABLE_BORDER_LINE_TYPES[3].key}</option>
                                        <option value={4}>{TABLE_BORDER_LINE_TYPES[4].key}</option>
                                        <option value={5}>{TABLE_BORDER_LINE_TYPES[5].key}</option>
                                        {/* <option value={6}>{TABLE_BORDER_LINE_TYPES[6].key}</option> */}
                                    </select>
                                </div>
                                <div className='new-full-line'>
                                    <span className='table-label'>颜色：</span>
                                    <input
                                        type='string'
                                        value={this.borderLine.color}
                                        onChange={this.textChange.bind(this, 'borderLine', 'color')}
                                        onClick={this.handleRenderColor.bind(this, 'borderLine', 'color')}
                                    />
                                    <div className={this.pickColor} style={{...this.pickColorStyle}}>
                                        {this.renderColorPicker()}
                                    </div>
                                </div>
                                <div className='new-full-line'>
                                    <span className='table-label'>表格：</span>
                                    <button className='hz-table-button fixed-margin2'
                                        style={{backgroundColor: this.borderLine.bg[0]}}
                                        onClick={this.changeBorderLineStyle.bind(this, 'borderLine', 'style')}
                                    >
                                        {TableBorderSettingType[0]}
                                    </button>
                                    <button className='hz-table-button'
                                        style={{backgroundColor: this.borderLine.bg[1]}}
                                        onClick={this.changeBorderLineStyle.bind(this, 'borderLine', 'style')}
                                    >
                                        {TableBorderSettingType[1]}
                                    </button>
                                    <button className='hz-table-button'
                                        style={{backgroundColor: this.borderLine.bg[2]}}
                                        onClick={this.changeBorderLineStyle.bind(this, 'borderLine', 'style')}
                                    >
                                        {TableBorderSettingType[2]}
                                    </button>
                                    <button className='hz-table-button'
                                        style={{backgroundColor: this.borderLine.bg[3]}}
                                        onClick={this.changeBorderLineStyle.bind(this, 'borderLine', 'style')}
                                    >
                                        {TableBorderSettingType[3]}
                                    </button>
                                    </div>
                                    <div className='new-full-line'>
                                    <span className='table-label'>单元格：</span>
                                    <button className='hz-table-button fixed-margin'
                                        style={{backgroundColor: this.borderLine.bg[4]}}
                                        onClick={this.changeBorderLineStyle.bind(this, 'borderLine', 'style')}
                                    >
                                        {TableBorderSettingType[4]}
                                    </button>
                                    <button className='hz-table-button'
                                        style={{backgroundColor: this.borderLine.bg[5]}}
                                        onClick={this.changeBorderLineStyle.bind(this, 'borderLine', 'style')}
                                    >
                                        {TableBorderSettingType[5]}
                                    </button>
                                    <button className='hz-table-button'
                                        style={{backgroundColor: this.borderLine.bg[6]}}
                                        onClick={this.changeBorderLineStyle.bind(this, 'borderLine', 'style')}
                                    >
                                        {TableBorderSettingType[6]}
                                    </button>
                                    </div>
                                    <div className='new-full-line'>
                                    <span className='table-label'/>
                                    <button className='hz-table-button fixed-margin'
                                        style={{backgroundColor: this.borderLine.bg[7]}}
                                        onClick={this.changeBorderLineStyle.bind(this, 'borderLine', 'style')}
                                    >
                                        {TableBorderSettingType[7]}
                                    </button>
                                    <button className='hz-table-button'
                                        style={{backgroundColor: this.borderLine.bg[8]}}
                                        onClick={this.changeBorderLineStyle.bind(this, 'borderLine', 'style')}
                                    >
                                        {TableBorderSettingType[8]}
                                    </button>
                                    <button className='hz-table-button'
                                        style={{backgroundColor: this.borderLine.bg[9]}}
                                        onClick={this.changeBorderLineStyle.bind(this, 'borderLine', 'style')}
                                    >
                                        {TableBorderSettingType[9]}
                                    </button>
                                </div>               
                            </li>
                        </ul>
                    </div>
                </div>
            </Dialog>

        );
    }

    private renderFooter(): any {
        return (
            <span>
                <Button onClick={this.close}>取消</Button>
                <Button type='primary' onClick={this.confirm}>确认</Button>
            </span>
        );
    }

    private setDialogValue(): void {
        const tableProps = this.oldTableProps = this.props.documentCore.getTableProperty(this.props.tableName);
        if ( null != tableProps ) {
            this.tableProps = tableProps;
            this.table = {
                tableName: tableProps.tableName,
                customProperty: tableProps.customProperty,
                bCanAddRow: tableProps.bCanAddRow,
                bCanDeleteRow: tableProps.bCanDeleteRow,
                bFixedRowHeight: tableProps.bFixedRowHeight,
                bFixedColWidth: tableProps.bFixedColWidth,
                bDeleteProtect: tableProps.bDeleteProtect,
                bReadOnly: tableProps.bReadOnly,
                bRepeatHeader: tableProps.bRepeatHeader,
                repeatHeaderNum: tableProps.repeatHeaderNum,
                rowsAuto: tableProps.rowsAuto,
                enableRowAction: tableProps.enableRowAction === EnableRowActionType.Enable,
                bPrintEmptyRow: tableProps.bPrintEmptyRow,
                bRepeatOnBreak: tableProps.bRepeatOnBreak,
            };

            this.cellMargins.top = tableProps.tableDefaultMargins.top;
            this.cellMargins.bottom = tableProps.tableDefaultMargins.bottom;
            this.cellMargins.left = tableProps.tableDefaultMargins.left;
            this.cellMargins.right = tableProps.tableDefaultMargins.right;

            let rowIndex = tableProps.curPos.rowIndex[0] + 1;
            const rowHeight = ( false === tableProps.rowsAuto[rowIndex - 1] ? tableProps.rowHeights[rowIndex - 1] : 0);
            if ( 1 < tableProps.curPos.rowIndex.length && rowIndex !== tableProps.curPos.rowIndex[1] + 1 ) {
                rowIndex += ( '-' + ( tableProps.curPos.rowIndex[1] + 1 ) );
            }

            let columnIndex = tableProps.curPos.cellIndex[0] + 1;
            const columnWidth = tableProps.columnWidths[tableProps.curPos.cellIndex[0]];
            if ( 1 < tableProps.curPos.cellIndex.length && rowIndex !== tableProps.curPos.cellIndex[1] + 1 ) {
                columnIndex += ( '-' + ( tableProps.curPos.cellIndex[1] + 1 ) );
            }

            this.row = {
                rowIndex,
                rowHeights: tableProps.rowHeights,
                rowHeight,
                bAuto: tableProps.rowsAuto[rowIndex - 1],
                length: tableProps.rows,
            };

            this.column = {
                columnIndex,
                columnWidths: tableProps.columnWidths,
                columnWidth,
                length: tableProps.columnWidths.length,
            };

            this.borderLine = {
                type: this.getBorderLineSizeIndex(tableProps.borderLineSize),
                color: tableProps.borderColor,
                style: '',
                bg: [],
            };

            this.tableBorders = {
                top: undefined,
                bottom: undefined,
                left: undefined,
                right: undefined,
                insideH: undefined,
                insideV: undefined,
                type: -1,
            };
            // for (const width of tableProps.columnWidth) {
            //     this.column.columnWidth.push(width);
            // }

            // const tableBorders = tableProps.tableBorders;
            // const cellBorders = tableProps.cellBorders;
            // if ( tableBorders ) {
            //     const index = TABLE_BORDER_LINE_TYPES.findIndex((item, index) => {
            //         return item.value === tableBorders.top.size;
            //     });
            //     this.borderLine.type = TABLE_BORDER_LINE_TYPES[index].key;
            // } else if ( cellBorders ) {
            //     ;
            // }
        }
    }

    private tabClick(index: TablePropsDialogTabType): void {
        if ( this.tabIndex === index ) {
            return ;
        }

        this.tabIndex = index;
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private numChange(name: string, subname: string, e: any): void {
        this[name][subname] = parseFloat(e.target.value);
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private checkChange(name: string, subname: string, e: any): void {
        if ( 'bAuto' === subname && this.table.bFixedRowHeight ) {
            this.row.rowHeight = this.row.rowHeights[this.row.rowIndex - 1];
            return;
        }

        this[name][subname] = !this[name][subname];

        if ( 'bFixedRowHeight' === subname ) {
            if ( true === this[name][subname] ) {
                this.row.bAuto = false;
            } else {
                let index = 0;
                if ( 'string' === typeof(this.row.rowIndex) ) {
                    const indexs = this.row.rowIndex.split('-');
                    const startRowIndex = parseInt(indexs[0], 0);
                    const endRowIndex = parseInt(indexs[1], 0);
                    index = startRowIndex - 1;
                    if ( startRowIndex > endRowIndex ) {
                        index = endRowIndex - 1;
                    }
                } else {
                    index = this.row.rowIndex - 1;
                }

                this.row.bAuto = this.table.rowsAuto[index];
            }
        }

        if ( true === this.row.bAuto ) {
            this.row.rowHeight = 0;
        } else {
            // const rowHeight = this.row.rowHeights[this.row.rowIndex];
            this.setRowHeight(); // this.row.rowHeight = this.row.rowHeights[this.row.rowIndex - 1];
        }

        this.setState({bRefresh: !this.state.bRefresh});
    }

    private textChange(name: string, subname: string, e: any): void {
        this[name][subname] = e.target.value;
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private checkedRowHeightAuto(): boolean {
        return ( this.table.bFixedRowHeight ? false : this.row.bAuto );
    }

    private handlePrevNextIndex(position: string): void {
        let result = false;
        const { documentCore } = this.props;
        const bSelectCells = documentCore.isSelectedTableCells();

        if ( ('prevColumn' === position || 'nextColumn' === position ) ) {
            if ( 'string' === typeof(this.column.columnIndex) ) {
                const indexs = this.column.columnIndex.split('-');
                this.column.columnIndex = 'prevColumn' === position ?
                        parseInt(indexs[0], 0) : parseInt(indexs[1], 0);
            }

            const bDiff = (0.01 < Math.abs(this.column.columnWidth -
                        this.column.columnWidths[this.column.columnIndex - 1]));
            if ( !bSelectCells && !this.table.bFixedRowHeight &&
                this.checkValue() && bDiff && this.checkColumnWidth() ) {
                result = documentCore.setTableProperty({columnWidths: this.column.columnWidths});
                result = true;
            }
        }

        if ( ('prevRow' === position || 'nextRow' === position ) ) {
            const bAuto = this.row.bAuto !== this.table.rowsAuto[this.row.rowIndex - 1];
            if ( 'string' === typeof(this.row.rowIndex) ) {
                const indexs = this.row.rowIndex.split('-');
                this.row.rowIndex = 'prevRow' === position ?
                        parseInt(indexs[0], 0) : parseInt(indexs[1], 0);
                this.row.bAuto = this.table.rowsAuto[this.row.rowIndex - 1];
            }

            const bDiff = (0.01 < Math.abs(this.row.rowHeight * 10 - this.row.rowHeights[this.row.rowIndex - 1]));
            if ( !bSelectCells && !this.table.bFixedRowHeight && this.checkValue() && ( bAuto || bDiff ) ) {
                documentCore.setTableProperty({rowHeight: this.row.rowHeight * 10,
                    rowIndex: this.row.rowIndex - 1 , bRowAuto: this.row.bAuto});
                result = true;
            }
        }

        switch (position) {
            case 'prevColumn':
                if ( 0 < this.column.columnIndex - 1) {
                    this.column.columnIndex--;
                } else {
                    this.column.columnIndex = this.column.length;
                }
                this.column.columnWidth = this.column.columnWidths[this.column.columnIndex - 1];
                break;

            case 'nextColumn':
                if ( this.column.length > this.column.columnIndex ) {
                    this.column.columnIndex++;
                } else {
                    this.column.columnIndex = 1;
                }
                this.column.columnWidth = this.column.columnWidths[this.column.columnIndex - 1];
                break;

            case 'prevRow':
                if ( 0 < this.row.rowIndex - 1) {
                    this.row.rowIndex--;
                } else {
                    this.row.rowIndex = this.row.length;
                }
                break;

            case 'nextRow':
                if ( this.row.length > this.row.rowIndex ) {
                    this.row.rowIndex++;
                } else {
                    this.row.rowIndex = 1;
                }
                break;

            default:
                break;
        }

        if ( true === result ) {
            const tableProps = documentCore.getTableProperty(this.props.tableName);
            this.row.rowHeights = tableProps.rowHeights;
            this.row.length = tableProps.rows;
            this.table.rowsAuto = tableProps.rowsAuto;

            this.column.columnWidths = tableProps.columnWidths;
            this.column.length = tableProps.columnWidths.length;
            this.props.refresh(true);
        }

        if ( ('prevRow' === position || 'nextRow' === position ) ) {
            this.row.bAuto = this.table.rowsAuto[this.row.rowIndex - 1];
            this.row.rowHeight = ( this.table.bFixedRowHeight || !this.row.bAuto ?
                                    this.row.rowHeights[this.row.rowIndex - 1] : 0 );
        }

        // documentCore.removeSelection();
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private changeBorderLineStyle(name: string, subname: string, e: any): void {
        let bReset = false;
        this.borderLine.style = e.currentTarget.textContent;

        switch (e.currentTarget.textContent) {
            case TableBorderSettingType[0]:
                if ( '#93F2FF' === this.borderLine.bg[0] ) {
                    bReset = true;
                } else {
                    for (let index = 0; index < this.borderLine.bg.length; index++) {
                        this.borderLine.bg[index] = '';
                    }
                    this.borderLine.bg[0] = '#93F2FF';
                }
                break;
            case TableBorderSettingType[1]:
                if ( '#93F2FF' === this.borderLine.bg[1] ) {
                    bReset = true;
                } else {
                    for (let index = 0; index < this.borderLine.bg.length; index++) {
                        this.borderLine.bg[index] = '';
                    }
                    this.borderLine.bg[1] = '#93F2FF';
                }
                break;
            case TableBorderSettingType[2]:
                if ( '#93F2FF' === this.borderLine.bg[2] ) {
                    bReset = true;
                } else {
                    for (let index = 0; index < this.borderLine.bg.length; index++) {
                        this.borderLine.bg[index] = '';
                    }
                    this.borderLine.bg[2] = '#93F2FF';
                }
                break;
            case TableBorderSettingType[3]:
                if ( '#93F2FF' === this.borderLine.bg[3] ) {
                    bReset = true;
                } else {
                    for (let index = 0; index < this.borderLine.bg.length; index++) {
                        this.borderLine.bg[index] = '';
                    }
                    this.borderLine.bg[3] = '#93F2FF';
                }
                break;
            case TableBorderSettingType[4]:
                if ( '#93F2FF' === this.borderLine.bg[4] ) {
                    bReset = true;
                } else {
                    for (let index = 0; index < this.borderLine.bg.length; index++) {
                        this.borderLine.bg[index] = '';
                    }
                    this.borderLine.bg[4] = '#93F2FF';
                }
                break;
            case TableBorderSettingType[5]:
                if ( '#93F2FF' === this.borderLine.bg[5] ) {
                    bReset = true;
                } else {
                    for (let index = 0; index < this.borderLine.bg.length; index++) {
                        this.borderLine.bg[index] = '';
                    }
                    this.borderLine.bg[5] = '#93F2FF';
                }
                break;
            case TableBorderSettingType[6]:
                if ( '#93F2FF' === this.borderLine.bg[6] ) {
                    bReset = true;
                } else {
                    for (let index = 0; index < this.borderLine.bg.length; index++) {
                        this.borderLine.bg[index] = '';
                    }
                    this.borderLine.bg[6] = '#93F2FF';
                }
                break;
            case TableBorderSettingType[7]:
                if ( '#93F2FF' === this.borderLine.bg[7] ) {
                    bReset = true;
                } else {
                    for (let index = 0; index < this.borderLine.bg.length; index++) {
                        this.borderLine.bg[index] = '';
                    }
                    this.borderLine.bg[7] = '#93F2FF';
                }
                break;
            case TableBorderSettingType[8]:
                if ( '#93F2FF' === this.borderLine.bg[8] ) {
                    bReset = true;
                } else {
                    for (let index = 0; index < this.borderLine.bg.length; index++) {
                        this.borderLine.bg[index] = '';
                    }
                    this.borderLine.bg[8] = '#93F2FF';
                }
                break;
            case TableBorderSettingType[9]:
                if ( '#93F2FF' === this.borderLine.bg[9] ) {
                    bReset = true;
                } else {
                    for (let index = 0; index < this.borderLine.bg.length; index++) {
                        this.borderLine.bg[index] = '';
                    }
                    this.borderLine.bg[9] = '#93F2FF';
                }
                break;
        }

        if ( bReset ) {
            this.borderLine.style = '';
            this.borderLine.bg = [];
            // for (let index = 0; index < this.borderLine.bg.length; index++) {
            //     this.borderLine.bg[index] = '';
            // }
        }
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private getTableBorders(bSelect?: boolean): void {
        this.tableBorders = null;

        const border: ITableBorder = {
            size: TABLE_BORDER_LINE_TYPES[this.borderLine.type].value,
            color: colorRgb(this.borderLine.color),
            value: 1,
        };

        if ( '' === this.borderLine.style ) {
            // this.tableBorders = {
            //     top: border,
            //     bottom: border,
            //     left: border,
            //     right: border,
            //     insideH: border,
            //     insideV: border,
            // };
            return ;
        }

        // const bShow = this.props.documentCore.checkSelectTableCellBorder(this.borderLine.style);
        // if ( false === bShow ) {
        //     border.size = 0;
        // }

        switch (this.borderLine.style) {
            case TableBorderSettingType[0]:
                this.tableBorders = {
                    top: border,
                    bottom: border,
                    left: border,
                    right: border,
                    insideH: border,
                    insideV: border,
                    type: 0,
                };
                break;

            case TableBorderSettingType[1]:
                border.size = 0;
                this.tableBorders = {
                    top: border,
                    bottom: border,
                    left: border,
                    right: border,
                    insideH: border,
                    insideV: border,
                    type: 0,
                };
                break;

            case TableBorderSettingType[2]:
                this.tableBorders = {
                    top: null,
                    bottom: null,
                    left: null,
                    right: null,
                    insideH: border,
                    insideV: border,
                    type: 0,
                };
                break;

            case TableBorderSettingType[3]:
                this.tableBorders = {
                    top: border,
                    bottom: border,
                    left: border,
                    right: border,
                    insideH: null,
                    insideV: null,
                    type: 0,
                };
                break;

            case TableBorderSettingType[4]:
                this.tableBorders = {
                    top: null,
                    bottom: null,
                    left: border,
                    right: null,
                    insideH: null,
                    insideV: null,
                    type: 1,
                };
                break;

            case TableBorderSettingType[5]:
                this.tableBorders = {
                    top: null,
                    bottom: null,
                    left: null,
                    right: null,
                    insideH: null,
                    insideV: border,
                    type: 1,
                };
                break;

            case TableBorderSettingType[6]:
                this.tableBorders = {
                    top: null,
                    bottom: null,
                    left: null,
                    right: border,
                    insideH: null,
                    insideV: null,
                    type: 1,
                };
                break;

            case TableBorderSettingType[7]:
                this.tableBorders = {
                    top: border,
                    bottom: null,
                    left: null,
                    right: null,
                    insideH: null,
                    insideV: null,
                    type: 1,
                };
                break;

            case TableBorderSettingType[8]:
                this.tableBorders = {
                    top: null,
                    bottom: null,
                    left: null,
                    right: null,
                    insideH: border,
                    insideV: null,
                    type: 1,
                };
                break;

            case TableBorderSettingType[9]:
                this.tableBorders = {
                    top: null,
                    bottom: border,
                    left: null,
                    right: null,
                    insideH: null,
                    insideV: null,
                    type: 1,
                };
                break;
        }
    }

    private setColumnValue(columnIndex: number): any {

        if ( columnIndex <= this.column.columnWidth.length ) {
            return this.column.columnWidth[columnIndex - 1];
        }

        return '';
    }

    private setRowHeight(): void {
        if ( 'string' === typeof(this.row.rowIndex) ) {
            const indexs = this.row.rowIndex.split('-');
            const startRowIndex = parseInt(indexs[0], 0);
            const endRowIndex = parseInt(indexs[1], 0);
            this.row.rowIndex = startRowIndex;

            if ( startRowIndex > endRowIndex ) {
                this.row.rowIndex = endRowIndex;
            }

            this.row.rowHeight = this.row.rowHeights[this.row.rowIndex - 1];
        } else {
            const index = this.row.rowIndex - 1;
            if ( null != this.row.rowHeights[index] ) {
                this.row.rowHeight = this.row.rowHeights[index];
            }
        }
    }

    private open = (): void => {
        this.tabIndex = 0;
        // const table = this.table;
        this.setDialogValue();
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private close = (bRefresh?: boolean): void => {
        this.props.close(this.props.id, bRefresh);
        this.visible = false;
        this.tabIndex = 0;
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private onClose = (id: string, bRefresh: boolean): void => {
        this[id] = false;
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private onChange = (value: any, name: string): void => {
        this.table[name] = value;
    }

    private confirm = (id?: any): void => {
        const { documentCore } = this.props;
        IFRAME_MANAGER.setDocId(documentCore.getCurrentId());
        if (!isValidName(this.table.tableName)) {
            message.error('表格名称不符合规范，请重新输入');
            return;
        }

        if ( true !== documentCore.checkTableName2(this.table.tableName) ) {
            message.error('表格名称重名，请重新输入');
            return ;
        }

        const enableRowAction = this.table.enableRowAction === true ? EnableRowActionType.Enable :
        EnableRowActionType.Unable;
        const oldEnableRowAction = this.oldTableProps.enableRowAction != null ? this.oldTableProps.enableRowAction :
        EnableRowActionType.Unable;
        if (enableRowAction !== oldEnableRowAction) {
            const type = this.props.documentCore.getRowActionType(this.oldTableProps.tableName);
            switch (type) {
                case 0: {
                    break;
                }
                case 1: {
                    message.error('不能在只有一行有效行的表格内设置行操作符', {time: 10000});
                    return;
                }
                case 2: {
                    message.error('不能在第一行合并拆分的表格内设置行操作符', {time: 10000});
                    return;
                }
                default: {
                    message.error('找不到表格，异常', {time: 10000});
                    return;
                }
            }
        }

        if ( true !== this.checkValue() || true !== this.checkColumnWidth() ) {
            return ;
        }

        const oldMargins = this.tableProps.tableDefaultMargins;
        const leftNew = parseFloat(this.cellMargins.left.toFixed(2));
        const rightNew = parseFloat(this.cellMargins.right.toFixed(2));
        const topNew = parseFloat(this.cellMargins.top.toFixed(2));
        const bottomNew = parseFloat(this.cellMargins.bottom.toFixed(2));
        let bNeedChange = false;

        if ( 0.01 <= Math.abs(leftNew - oldMargins.left) ||
             0.01 <= Math.abs(rightNew - oldMargins.right) ||
             0.01 <= Math.abs(topNew - oldMargins.top) ||
             0.01 <= Math.abs(bottomNew - oldMargins.bottom) ) {
            bNeedChange = true;
        }

        let tableDefaultMargins = null;
        if ( bNeedChange ) {
            tableDefaultMargins = { top: topNew * 10,
            bottom: bottomNew * 10,
            left: leftNew * 10,
            right: rightNew * 10 };
        }

        const oldColWidth = this.column.columnWidths[this.column.columnIndex];
        let columnWidth = parseFloat(this.column.columnWidth.toFixed(2));

        if ( 0.01 > Math.abs(columnWidth - oldColWidth) || null == oldColWidth ) {
            columnWidth = null;
        }

        const bAuto = ( this.row.bAuto && false === this.table.bFixedRowHeight );
        if ( false === bAuto ) {
            this.row.rowHeight = parseFloat(this.row.rowHeight.toFixed(2));
        }
        const bDiff = (0.01 > Math.abs(this.row.rowHeight - this.row.rowHeights[this.row.rowIndex - 1])
                        ? false : true);
        let rowHeight = this.row.rowHeight * 10;

        if ( this.table.bFixedRowHeight || true === bAuto ) {
            rowHeight = null;
        }

        if ( false === this.row.bAuto && bDiff && this.row.bAuto === this.table.rowsAuto[this.row.index] ) {
            rowHeight = null;
        }

        const tableProps = {
            tableName: this.table.tableName,
            tableDefaultMargins,
            rowHeight,
            rowIndex: this.row.rowIndex - 1,
            columnWidths: null == columnWidth ? null : this.column.columnWidths,
            // columnWidth: this.column.columnWidth,
            bFixedRowHeight: this.table.bFixedRowHeight,
            bFixedColWidth: this.table.bFixedColWidth,
            bCanAddRow: this.table.bCanAddRow,
            bCanDeleteRow: this.table.bCanDeleteRow,
            bDeleteProtect: this.table.bDeleteProtect,
            customProperty: this.table.customProperty,
            bRepeatHeader: this.table.bRepeatHeader,
            repeatHeaderNum: this.table.repeatHeaderNum,
            bReadOnly: this.table.bReadOnly,
            tableBorders: null,
            cellBorders: null,
            bRowAuto: bAuto,
            borderLineSize: this.borderLine.type,
            borderColor: this.borderLine.color,
            enableRowAction,
            bPrintEmptyRow: this.table.bPrintEmptyRow,
            bRepeatOnBreak: this.table.bRepeatOnBreak,
        };

        if ( tableProps.repeatHeaderNum >= this.row.length ) {
            message.error('重复表头行数需小于总行数，请重新输入');
            return ;
        }
        const oldTableProps = this.oldTableProps;
        if (tableProps.repeatHeaderNum !== oldTableProps.repeatHeaderNum && this.table.enableRowAction === true) {
            tableProps.enableRowAction =  EnableRowActionType.Unable;
            message.info('重新设置了标题行，会把参考行恢复显示', {time: 1000});
        }

        const bSelect = documentCore.isSelectedTable();
        this.getTableBorders(bSelect);
        // if ( bSelect ) {
        //     tableProps.tableBorders = this.tableBorders;
        // } else {
        tableProps.tableBorders = this.tableBorders;
        // }

        documentCore.setTableProperty(tableProps);
        this.close(true);
    }

    private renderColorPicker(): any {
        return (
            <SelectColor color={this.borderLine.color} onChange={this.handleChangeColor}  />
        );
    }

    private docClick = (e: any) => {
        if (this.bActiveColor === true) {
            this.bActiveColor = false;
            return;
        }

        if (this.bActiveColor === false) {
            this.pickColor = 'pick-color';
            this.bActiveColor = undefined;
            this.setState({});
            return;
        }

        // this.bActiveColor = undefined;
    }

    private handleRenderColor(name: string, subname: string, e: any): void {
        if ( 'borderLine' === name && 'color' === subname ) {
            this.bActiveColor = true;
            this.pickColor += ' active'; // display: block
            const currentTarget = e.currentTarget;
            this.pickColorStyle.left = currentTarget.offsetLeft;
            this.setState({bRefresh: !this.state.bRefresh});
        }
    }

    private handleChangeColor = (color: {hex: string, rgb: object}): void => {
        // this.color = color.hex;
        // console.log(this.color)
        // this.selectActive = false;
        this.borderLine.color = color.hex;
        this.pickColor = 'pick-color';
        this.setState({bRefresh: !this.state.bRefresh});

        // if (this.activeMenuIndex === MenuItemIndex.Color) {
        //   this.menuAction.setTextColor(this.color);
        //   this.textColor = this.color;
        // } else if (this.activeMenuIndex === MenuItemIndex.BackgroundColor) {
        //   this.menuAction.setBackgroundColor(this.color);
        //   this.backgroundColor = this.color;
        // } else {
        //     // console.log('r u fuking kidding me?')
        // }
        // this.refresh();
    }

    private checkValue(): boolean {
        if ( (0 > this.row.rowHeight || isNaN(this.row.rowHeight))
            && ( false === this.table.bFixedRowHeight ) ) {
            message.error('行高输入非法，请重新输入');
            return false;
        }

        if ( 0 === this.row.rowHeight) {
            this.row.rowHeight = 0.01;
        }

        if ( 0 > this.column.columnWidth || isNaN(this.column.columnWidth) ) {
            message.error('列宽输入非法，请重新输入');
            return false;
        }

        if ( 0 > this.cellMargins.top || isNaN(this.cellMargins.top) ) {
            message.error('单元格上边距请输入非法，请重新输入');
            return false;
        }

        if ( 0 > this.cellMargins.left || isNaN(this.cellMargins.left) ) {
            message.error('单元格左边距请输入非法，请重新输入');
            return false;
        }

        if ( 0 > this.cellMargins.right || isNaN(this.cellMargins.right) ) {
            message.error('单元格右边距请输入非法，请重新输入');
            return false;
        }

        if ( 0 > this.cellMargins.bottom || isNaN(this.cellMargins.bottom) ) {
            message.error('单元格下边距请输入非法，请重新输入');
            return false;
        }

        return true;
    }

    private checkColumnWidth(): boolean {
        if ( 'string' === typeof(this.column.columnIndex) ) {
            const indexs = this.column.columnIndex.split('-');
            const startColumnIndex = parseInt(indexs[0], 0);
            const endColumnIndex = parseInt(indexs[1], 0);
            this.column.columnIndex = startColumnIndex;

            if ( startColumnIndex > endColumnIndex ) {
                this.column.columnIndex = endColumnIndex;
            }
        }

        const columnIndex = this.column.columnIndex - 1;
        const oldWidth = this.column.columnWidths[columnIndex];

        if ( 0.01 > Math.abs(this.column.columnWidth - parseFloat(oldWidth.toFixed(2))) ) {
            return true;
        }

        let sumWidth = 0;
        let newWidth = this.column.columnWidth * 100;
        const minColWidth = 1;
        const colsLength = this.column.columnWidths.length;

        for (const width of this.column.columnWidths) {
            sumWidth += (width * 100);
        }

        sumWidth -= (colsLength - 1);
        sumWidth = parseInt(sumWidth.toFixed(0), 0);

        if ( newWidth >= sumWidth ) {
            if ( newWidth > sumWidth ) {
                message.error('列宽输入非法，请重新输入: 0.01---' + sumWidth / 100);
                return false;
            }

            for (let index = 0; index < colsLength; index++) {
                if ( columnIndex !== index ) {
                    this.column.columnWidths[index] = minColWidth;
                }
            }

            newWidth = sumWidth;
            this.column.columnWidths[columnIndex] = newWidth;
            this.column.columnWidth = sumWidth / 100;
        } else {
            let diff = oldWidth * 100 - newWidth;
            const newCols = [];
            this.column.columnWidths.forEach((col) => {
                newCols.push(parseInt((col * 100).toFixed(0), 0));
            });

            newCols[columnIndex] = newWidth;
            diff = parseInt(diff.toFixed(0), 0);

            if ( 0 < diff ) {
                if ( colsLength === columnIndex + 1 ) {
                    newCols[0] += diff;
                } else {
                    newCols[columnIndex + 1] += diff;
                }
            } else if ( 0 > diff ) {
                let bFlag = false;
                diff = Math.abs(diff);

                for (let index = columnIndex + 1; index < colsLength; index++) {
                    if ( diff + minColWidth <= newCols[index] ) {
                        bFlag = true;
                        newCols[index] = newCols[index] - diff;
                        break;
                    } else if ( 0 < diff ) {
                        diff = diff - ( newCols[index] - minColWidth );
                        newCols[index] = minColWidth;
                    }
                }

                if ( false === bFlag ) {
                    for (let index = 0; index < columnIndex; index++) {
                        if ( diff + minColWidth <= newCols[index] ) {
                            newCols[index] = newCols[index] - diff;
                            break;
                        } else if ( 0 < diff ) {
                            diff = diff - ( newCols[index] - minColWidth );
                            newCols[index] = minColWidth;
                        }
                    }
                }
            }

            newCols.forEach((col, index) => {
                this.column.columnWidths[index] = col / 100;
            });
        }

        return true;
    }

    private getBorderLineSizeIndex(size: number): number {
        let index = 1;
        switch (size) {
            case TABLE_BORDER_LINE_TYPES[0].value:
                index = 0;
                break;
            case TABLE_BORDER_LINE_TYPES[1].value:
                index = 1;
                break;
            case TABLE_BORDER_LINE_TYPES[2].value:
                index = 2;
                break;
            case TABLE_BORDER_LINE_TYPES[3].value:
                index = 3;
                break;
            case TABLE_BORDER_LINE_TYPES[4].value:
                index = 4;
                break;
            case TABLE_BORDER_LINE_TYPES[5].value:
                index = 5;
                break;
            // case TABLE_BORDER_LINE_TYPES[6].value:
            //     index = 6;
            //     break;
            default:
                break;
        }

        return index;
    }
}
