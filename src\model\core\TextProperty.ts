import { FONT_MAPPING, COLOR_TYPE, DEFAULT_TEXT_PROPERTY, isMacOs } from '../../common/commonDefines';
import { ReviewInfo } from './Revision';

/**
 * 斜体
 */
export enum FontStyleType {
    Normal, Italic,
}

/**
 * 删除线，下划线
 */
export enum TextDecorationLineType {
    None, Underline, Overline, Blink, WavyUnderline,
}

/**
 * 粗体
 */
export enum FontWeightType {
    Normal, Bold,
}

export enum TextVertAlign {
    Baseline, // 默认。元素放置在父元素的基线上。
    Sub, // 垂直对齐文本的下标。
    Super, // 垂直对齐文本的上标
    Top,
    TextTop,
    Middle,
    Bottom, // 把元素的顶端与行中最低的元素的顶端对齐。
    TextBottom,
}

export interface ITextProperty {
    textDecorationLine?: TextDecorationLineType;
    fontSize?: number;
    fontFamily?: string;
    fontWeight?: FontWeightType;
    fontStyle?: FontStyleType;
    color?: string;
    backgroundColor?: string;
    vertAlign?: TextVertAlign;
}

// enum TextDecorationStyleType {
//     solid, double, dotted, dashed, wavy
// }

/**
 * 文本属性类
 */
export default class TextProperty {
    public textDecorationLine: TextDecorationLineType;
    // textDecorationStyle?: TextDecorationStyleType;
    // textDecorationColor?: string;
    public fontSize: number = 16;
    public fontFamily: string = `宋体`;
    public fontWeight: FontWeightType;
    public fontStyle: FontStyleType;
    public color: string;
    public backgroundColor: string;
    // public subscript: boolean; // 下标
    // public superscript: boolean; // 上标
    public vertAlign: TextVertAlign;

    public propertyChange: TextProperty = null;  // 属性发生改变
    public reviewInfo: ReviewInfo = null;
    private maxLengthBg: string;

    constructor(config: TextProperty = {} as TextProperty) {
        this.textDecorationLine = config.textDecorationLine || TextDecorationLineType.None;
        // this.textDecorationStyle = config.textDecorationStyle;
        // this.textDecorationColor = config.textDecorationColor;
        // this.fontSize = config.fontSize || DEFAULT_FONT.fontSize;
        // this.fontFamily = config.fontFamily || DEFAULT_FONT.fontFamily;
        this.fontSize = config.fontSize || DEFAULT_TEXT_PROPERTY.fontSize;
        this.fontFamily = config.fontFamily || DEFAULT_TEXT_PROPERTY.fontFamily;
        this.fontWeight = config.fontWeight || FontWeightType.Normal;
        this.fontStyle = config.fontStyle || FontStyleType.Normal;
        this.color = config.color || COLOR_TYPE.get('black');
        this.backgroundColor = config.backgroundColor || COLOR_TYPE.get('white');
        // this.superscript = config.superscript || false;
        // this.subscript = config.subscript || false;
        this.vertAlign = config.vertAlign || TextVertAlign.Baseline;

        if (this.fontFamily === `宋体` && isMacOs) {
            this.fontFamily = FONT_MAPPING['宋体'].mac;
        }
    }

    public setProperty(textPr: TextProperty): void {
        this.textDecorationLine = (undefined !== textPr.textDecorationLine) ?
            textPr.textDecorationLine : this.textDecorationLine;
        this.fontSize = (undefined !== textPr.fontSize) ? textPr.fontSize : this.fontSize;
        this.fontFamily = (undefined !== textPr.fontFamily) ? textPr.fontFamily : this.fontFamily;
        this.fontWeight = (undefined !== textPr.fontWeight) ? textPr.fontWeight : this.fontWeight;
        this.fontStyle = (undefined !== textPr.fontStyle) ? textPr.fontStyle : this.fontStyle;
        this.color = (undefined !== textPr.color) ? textPr.color : this.color;
        this.backgroundColor = (undefined !== textPr.backgroundColor) ? textPr.backgroundColor : this.backgroundColor;
    }

    public copy(): TextProperty {
        const textPr = new TextProperty(this);

        textPr.fontFamily = this.fontFamily;
        textPr.fontSize = this.fontSize;
        textPr.fontStyle = this.fontStyle;
        textPr.fontWeight = this.fontWeight;
        textPr.color = this.color;
        textPr.backgroundColor = this.backgroundColor;
        textPr.vertAlign = this.vertAlign;
        textPr.textDecorationLine = this.textDecorationLine;

        return textPr;
    }

    /**
     * 是否有属性发生改变
     */
    public hasPropertyChange(): boolean {
        if (!this.propertyChange) {
            return false;
        }

        return true;
    }

    public setMaxLengthBackgroundColor(color: string): void {
        this.maxLengthBg = color;
    }

    public getBackgroundColor(): string {
        return this.maxLengthBg || this.backgroundColor;
    }

    /**
     * 属性发生改变
     */
    public addPropertyChange(): void {
        this.propertyChange = this.copy();
        this.reviewInfo = new ReviewInfo(null);
        this.reviewInfo.update();
    }

    /**
     * 设置属性
     * @param propertyChange
     */
    public setPropertyChange(propertyChange: TextProperty, reviewInfo: ReviewInfo): void {
        this.propertyChange = propertyChange;
        this.reviewInfo = reviewInfo;
    }

    /**
     * 删除属性变化
     */
    public removePropertyChange(): void {
        delete this.propertyChange;
        delete this.reviewInfo;
    }

    public getDiffPrChange(): TextProperty {
        const textPr = new TextProperty(this);

        if (false === this.hasPropertyChange()) {
            return textPr;
        }

        const prChange = this.propertyChange;
        if (this.fontFamily !== prChange.fontFamily) {
            textPr.fontFamily = this.fontFamily;
        }

        if (this.fontSize !== prChange.fontSize) {
            textPr.fontSize = this.fontSize;
        }

        if (this.fontStyle !== prChange.fontStyle) {
            textPr.fontStyle = this.fontStyle;
        }

        if (this.color !== prChange.color) {
            textPr.color = this.color;
        }

        if (this.fontWeight !== prChange.fontWeight) {
            textPr.fontWeight = this.fontWeight;
        }

        if (this.vertAlign !== prChange.vertAlign) {
            textPr.vertAlign = this.vertAlign;
        }

        if (this.textDecorationLine !== prChange.textDecorationLine) {
            textPr.textDecorationLine = this.textDecorationLine;
        }

        if (this.backgroundColor !== prChange.backgroundColor) {
            textPr.backgroundColor = this.backgroundColor;
        }

        return textPr;
    }

    public clear(): void {
        this.textDecorationLine = undefined;
        this.fontSize = undefined;
        this.fontFamily = undefined;
        this.fontWeight = undefined;
        this.fontStyle = undefined;
        this.color = undefined;
        this.backgroundColor = undefined;
        // this.superscript = undefined;
        // this.subscript = undefined;
        this.vertAlign = undefined;
    }

    public isEqual(pr: TextProperty): boolean {
        if (null == pr) {
            return false;
        }

        if (this.fontFamily !== pr.fontFamily) {
            return false;
        }

        if (this.fontSize !== pr.fontSize) {
            return false;
        }

        if (this.fontStyle !== pr.fontStyle) {
            return false;
        }

        if (this.color !== pr.color) {
            return false;
        }

        if (this.fontWeight !== pr.fontWeight) {
            return false;
        }

        if (this.vertAlign !== pr.vertAlign) {
            return false;
        }

        if (this.textDecorationLine !== pr.textDecorationLine) {
            return false;
        }

        if (this.backgroundColor !== pr.backgroundColor) {
            return false;
        }

        return true;
    }

    public toJson(): string {
        // const items: any[] = [];
        // items.push({'textDecorationLine': this.textDecorationLine,
        //     'backgroundColor': this.backgroundColor,
        //     'vertAlign': this.vertAlign,
        //     'fontWeight': this.fontWeight,
        //     'color': this.color,
        //     'fontStyle': this.fontStyle,
        //     'fontSize': this.fontSize,
        //     'fontFamily': this.fontFamily,
        // });

        return JSON.stringify(
            {
                textDecorationLine: this.textDecorationLine,
                backgroundColor: this.backgroundColor,
                vertAlign: this.vertAlign,
                fontWeight: this.fontWeight,
                color: this.color,
                fontStyle: this.fontStyle,
                fontSize: this.fontSize,
                fontFamily: this.fontFamily,
            }
        );
    }

    public load(prop: any): void {
        prop = JSON.parse(prop);
        this.textDecorationLine = prop.textDecorationLine;
        this.backgroundColor = prop.backgroundColor;
        this.vertAlign = prop.vertAlign;
        this.fontWeight = prop.fontWeight;
        this.color = prop.color;
        this.fontStyle = prop.fontStyle;
        this.fontSize = prop.fontSize;
        this.fontFamily = prop.fontFamily;
    }
}
