import Document from './Document';
import { CHeaderFooterController } from './HeaderFooter';
import DocumentControllerBase from './DocumentControllerBase';
import { ICurrentCursorPos } from './Paragraph/ParagraphSearchPosXY';
import { EquationType, ResultType, INewControlProperty, IRevisionChange,
  DocumentSectionType, 
  ImageMediaType} from '../../common/commonDefines';
import { ParaElementBase } from './Paragraph/ParaElementBase';
import { ParaElementType } from './Paragraph/ParagraphContent';
import { IDrawSelectionBounds } from '../DocumentCore';
import DocumentContentElementBase from './DocumentContentElementBase';
import Selection from './Selection';
import Paragraph from './Paragraph';
import MouseEventHandler from '../../common/MouseEventHandler';
import { NewControl } from './NewControl/NewControl';
import { DocumentElementState } from './HistoryState';
import { TableBase } from './TableBase';
import { IOperateResult } from './History';

/**
 * special class of command handler in headers and footers
 * @param LogicDocument - Link to the main document
 * @param hdrFtr - Link to the footer management object
 */
export default class HeaderFooterController extends DocumentControllerBase {
  private hdrFtr: CHeaderFooterController;
  constructor(logicDocument: Document, hdrFtr: CHeaderFooterController) {
    super(logicDocument);
    this.hdrFtr = hdrFtr;
  }

  public canUpdateTarget(): boolean {
    return true;
  }

  public recalculateCurPos(bUpdateX: boolean = true, bUpdateY: boolean = true): ICurrentCursorPos {
    return this.hdrFtr.recalculateCurPos(bUpdateX, bUpdateY);
  }

  public getCurPage(): number {
    const curHeaderFooter = this.hdrFtr.curHeaderFooter;
    if (curHeaderFooter !== null && curHeaderFooter.recalcInfo.curPage !== -1) {
      return curHeaderFooter.recalcInfo.curPage;
    }
    return -1;
  }

  public addNewParagraph(): number {
    return this.hdrFtr.addNewParagraph();
  }

  public getCurrentTextProps(): any {
    return this.hdrFtr.getCurrentTextProps();
  }

  public getParagraphProperty(): any {
    return this.hdrFtr.getParagraphProperty();
  }

  // public getCurrentTextProps(): any {
  //   return this.hdrFtr.getCurrentTextProps();
  // }

  public addInlineImage(width: number, height: number, src: string,
                        name?: string, type?: EquationType, svgElem?: any,
                        mediaType?: ImageMediaType, mediaSrc?: string, datas?: any): string {
    return this.hdrFtr.addInlineImage(width, height, src, name, type,
                                      svgElem, mediaType, mediaSrc, datas);
  }

  public isNotOverNewControl(): boolean {
    return this.hdrFtr.isNotOverNewControl();
  }

  public canCopy(): boolean {
    return this.hdrFtr.canCopy();
  }

  public getParaContentByXY(mouseEvent: MouseEventHandler, pageIndex: number): any {
    return this.hdrFtr.getParaContentByXY(mouseEvent, pageIndex);
  }

  public addInlineTable(cols: number, rows: number, tableHeaderNum?: number,
                        tableName?: string, bRepeatHeader?: boolean): boolean {
    return this.hdrFtr.addInlineTable(cols, rows, tableHeaderNum, tableName, bRepeatHeader);
  }

  public addToParagraph(paraItem: ParaElementBase, bRecal?: boolean): number {
    if (paraItem.type === ParaElementType.ParaNewLine) { // true === ParaItem.IsPageOrColumnBreak()
      return ;
    }
    const res = this.hdrFtr.addToParagraph(paraItem, bRecal);
    this.logicDocument.updateSelectionState();
    return res;
    // this.logicDocument.updateUndoRedoState();
  }

  public remove(direction: number, bOnlyText: boolean, bOnlySelection?: boolean, bAddText?: boolean):
            IOperateResult {
    const nResult = this.hdrFtr.remove(direction, bOnlyText, bOnlySelection, bAddText);
    return nResult;
  }

  public getCursorPosXY(): any {
  // public getCursorPosXY(): {x: number; y: number} {
  // public getCursorPosXY(): ICursorProperty {
    // return this.hdrFtr.getCurPosXY();
    return this.hdrFtr.getCursorPosXY();
  }

  public moveCursorToStartPos(bAddToSelect: boolean): void {
    this.hdrFtr.moveCursorToStartPos(bAddToSelect);
  }

  public moveCursorToEndPos(bAddToSelect: boolean): void {
    this.hdrFtr.moveCursorToEndPos(bAddToSelect);
  }

  public moveCursorLeft(bShiftKey: boolean): boolean {
    return this.hdrFtr.moveCursorLeft(bShiftKey);
  }

  public getSelectText(): string {
    return this.hdrFtr.getSelectText();
  }

  public getNewControlBySelectArea(): string {
    return this.hdrFtr.getNewControlBySelectArea();
  }

  public moveCursorRight(bShiftKey: boolean): boolean {
    return this.hdrFtr.moveCursorRight(bShiftKey);
  }

  public moveCursorUp(bShiftKey: boolean): boolean {
    const retValue = this.hdrFtr.moveCursorUp(bShiftKey);
    this.logicDocument.updateSelectionState();
    return retValue;
  }

  public moveCursorDown(bShiftKey: boolean): boolean {
    const retValue = this.hdrFtr.moveCursorDown(bShiftKey);
    this.logicDocument.updateSelectionState();
    return retValue;
  }

  public moveCursorToEndOfLine(bAddToSelect: boolean): boolean {
    return this.hdrFtr.moveCursorToEndOfLine(bAddToSelect);
  }

  public moveCursorToStartOfLine(bAddToSelect: boolean): boolean {
    return this.hdrFtr.moveCursorToStartOfLine(bAddToSelect);
  }

  public moveCursorToXY(curPage: number, pointX: number, pointY: number, bAddToSelect: boolean = false): void {
    return this.hdrFtr.moveCursorToXY(curPage, pointX, pointY, bAddToSelect);
  }

  public moveCursorToCell(bNext: boolean): void {
    return this.hdrFtr.moveCursorToCell(bNext);
  }

  public setParagraphAlignment(alignment: number): number {
    this.hdrFtr.setParagraphAlignment(alignment);
    return ResultType.Success;
  }

  public setParagraphProperty(paraProperty: any): boolean {
    return this.hdrFtr.setParagraphProperty(paraProperty);
  }

  public setParagraphContextualSpacing(bValue: boolean): void {
    return this.hdrFtr.setParagraphContextualSpacing(bValue);
  }

  public setParagraphPageBreakBefore(bValue: boolean): void {
    return this.hdrFtr.setParagraphPageBreakBefore(bValue);
  }

  public removeSelection(): void {
    this.hdrFtr.removeSelection();
  }

  public isSelectionEmpty(bContainParaEnd?: boolean): boolean {
    return this.hdrFtr.isSelectionEmpty(bContainParaEnd);
  }

  public getSelection(): Selection {
      return this.hdrFtr.getSelection();
  }

  public getSelectionBounds(): IDrawSelectionBounds {
    return this.hdrFtr.getSelectionBounds();
  }

  public checkPosInSelection(pageIndex: number, pointX: number, pointY: number, nearPos?: number): boolean {
    return this.hdrFtr.checkPosInSelection(pageIndex, pointX, pointY, nearPos);
  }

  public selectAll(): void {
    this.hdrFtr.selectAll();
  }

  public getSelectedContent(selectedContent: any): DocumentContentElementBase[] {
    return this.hdrFtr.getSelectedContent(selectedContent);
  }

  public updateCursorType(x: number, y: number, pageAbs: number): void {
    if ( this.hdrFtr ) {
      this.hdrFtr.updateCursorType(x, y, pageAbs);
    }
  }

  public isSelectionUse(): boolean {
    return this.hdrFtr.isSelectionUse();
  }

  public isTextSelectionUse(): boolean {
    return this.hdrFtr.isTextSelectionUse();
  }

  public getCurPosXY(): {x: number; y: number} {
    return this.hdrFtr.getCurPosXY();
  }

  public getSelectedText(bClearText: boolean, oPr: any): string {
    return this.hdrFtr.getSelectedText(bClearText, oPr);
  }

  public getCurrentParagraph(): Paragraph {
    return this.hdrFtr.getCurrentParagraph();
  }

  public getSelectedElementsInfo(oInfo: any): void {
    if ( this.hdrFtr ) {
      this.hdrFtr.getSelectedElementsInfo();
    }
  }

  public isInTableCell(): boolean {
    if ( this.hdrFtr ) {
      return this.hdrFtr.isInTableCell();
    }
  }

  public isSelectedTableCells(): boolean {
    if ( this.hdrFtr ) {
      return this.hdrFtr.isSelectedTableCells();
    }
  }

  public getCurrentTable(): TableBase {
      if ( this.hdrFtr ) {
          return this.hdrFtr.getCurrentTable();
      }

      return undefined;
  }

  public addTableRow(bBefore: boolean): boolean {
    return this.hdrFtr.addTableRow(bBefore);
  }

  // public addTableRow(bBefore: boolean): boolean {
  //   return this.cHeaderFooterController.addTableRow(bBefore);
  // }

  public removeTableRow(rowIndex?: number): boolean {
    if ( this.hdrFtr ) {
      return this.hdrFtr.removeTableRow(rowIndex);
    }
    return false;
  }

  public mergeTableCells(): boolean {
    if ( this.hdrFtr ) {
      return this.hdrFtr.mergeTableCells();
    }

    return false;
  }

  public splitTableCells(cols: number, rows: number): boolean {
    if ( this.hdrFtr ) {
      this.hdrFtr.splitTableCells(cols, rows);
    }

    return false;
  }

  public removeTable(): boolean {
    if ( this.hdrFtr ) {
      return this.hdrFtr.removeTableRow();
    }

    return false;
  }

  public removeTableColumn(): boolean {
    if ( this.hdrFtr ) {
      return this.hdrFtr.removeTableColumn();
    }

    return false;
  }

  public canMergeTableCells(): boolean {
    if ( this.hdrFtr ) {
      return this.hdrFtr.canMergeTableCells();
    }

    return false;
  }

  public updateSelectionState(): void {
    this.hdrFtr.updateSelectionState();
    // this.logicDocument.updateTracks();
  }

  public getSelectionState(): any {
    this.hdrFtr.getSelectionState();
  }

  public setSelectionState(state: any, stateIndex: number): void {
    this.hdrFtr.setSelectionState();
  }

  public addComment(comment: any): string {
    this.hdrFtr.addComment();
    return ResultType.StringEmpty;
  }

  public canAddComment(): boolean {
    return this.hdrFtr.canAddComment();
  }

  public startSelectionFromCurPos(): void {
    this.hdrFtr.startSelectionFromCurPos();
  }

  public getCurrentSectionPr(): void {
    return null;
  }

  public addNewControl(property: INewControlProperty, sText?: string): number {
    return this.hdrFtr.addNewControl(property, sText);
  }

  public isCursorInNewControl(): boolean {
    return this.hdrFtr.isCursorInNewControl();
  }

  // public isPopWinNewControl(): boolean {
  //   return this.hdrFtr.isPopWinNewControl();
  // }

  public canInput(): boolean {
      return (!this.logicDocument.getProtectHeaderFooter() && this.hdrFtr.canInput());
  }

  public canDelete(direction?: number): boolean {
      return (!this.logicDocument.getProtectHeaderFooter() && this.hdrFtr.canDelete(direction));
  }

  public canInsertNewControl(): boolean {
      return this.hdrFtr.canInsertNewControl();
  }

  // BELOW ARE CUSTOM METHODS

  public getDocumentSelection(): Selection {
    return this.hdrFtr.getDocumentSelection();
  }

  public getContentPosByXY(pageIndex: number, pointX: number, pointY: number): number {
    return this.hdrFtr.getContentPosByXY(pageIndex, pointX, pointY);
  }

  public isFocusInNewControl(mouseEvent?: MouseEventHandler, pageIndex?: number): boolean {
    return this.hdrFtr.isFocusInNewControl(mouseEvent, pageIndex);
  }

  public getFocusInNewControl(mouseEvent?: MouseEventHandler, pageIndex?: number): NewControl {
    return this.hdrFtr.getFocusInNewControl(mouseEvent, pageIndex);
  }

  public getCursorInNewControl(): NewControl {
    return this.hdrFtr.getCursorInNewControl();
  }

  public getDocumentElementState(): DocumentElementState[][] {
    if ( this.hdrFtr ) {
      return this.hdrFtr.getDocumentElementState();
    }

    return [];
  }

  public setDocumentElementState(state: any, stateIndex: number): void {
    if ( this.hdrFtr ) {
      this.hdrFtr.setDocumentElementState(state, stateIndex);
    }
  }

  public getDocumentContent(): DocumentContentElementBase[] {
    if ( this.hdrFtr && this.hdrFtr.curHeaderFooter) {
      return this.hdrFtr.curHeaderFooter.getTrueContent();
    }
  }

  public getFocusRevision(mouseEvent: MouseEventHandler, pageIndex: number): IRevisionChange[] {
    if ( this.hdrFtr ) {
      return this.hdrFtr.getFocusRevision(mouseEvent, pageIndex);
    }

    return null;
  }

  public getDocumentSectionType(): DocumentSectionType {
    if ( this.hdrFtr ) {
      return this.hdrFtr.getDocumentSectionType();
    }

    return DocumentSectionType.Document;
  }

  public getTableCellByXY(pointX: number, pointY: number, pageIndex: number): any {
    if ( this.hdrFtr ) {
      return this.hdrFtr.getTableCellByXY(pointX, pointY, pageIndex);
    }

    return null;
  }

}
