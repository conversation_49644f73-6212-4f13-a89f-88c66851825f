import {PasteType, CopyType } from './DataType';
import {GlobalEvent as gEvent, GlobalEventName as gEventName} from '../GlobalEvent';

import { MenuItemIndex, MonitorAction } from '../commonDefines';
import {logger} from '../log/Logger';
import CopyCore from '.';
import { message } from '../Message';
import { getCurTime } from '../commonMethods';


export default class  CopyParse {
    public host: any;
    public docId: number;
    public editorInstanceId: string; // 真正唯一的编辑器实例ID
    public copyPaste: CopyCore = null; // copyParse 粘贴复制class的实例
    public operatePanelDom: HTMLDivElement = null; // 复制粘贴操作面板
    public copyDom: HTMLDivElement = null; // 复制Dom 容器
    public copyIframeWindow: any;
    public editorDom: HTMLDivElement = null; // 富文本dom
    public repasteDom: HTMLDivElement = null; // 格式选择粘贴面板
    public repasteDialogDom: HTMLDivElement = null; // 粘贴弹出面板
    private isHandlerOwner: boolean = false; // 本次操作是否是来自自己操作
    private pasteType: PasteType = PasteType.PasteCtrlV; // 通过复制面板进行粘贴
    private copyType: CopyType = CopyType.CopyCtrlC;
    private showPasteDialog: boolean = false;
    private copyDisabled: boolean = false;
    private pasteDisabled: boolean = false; // 不带格式的粘贴板是否禁止点击
    private pasteUnAccess: boolean = false; // 带格式的粘贴板是否禁止点击
    private stopDocClick: boolean = false;
    private isSourceContextmenu: boolean = false;
    private timeout: any = null;
    private bUnSelected: boolean = false;
    private _startTime: Date;
    private isActiveRepaste: boolean = false;

    constructor() {
        // this.copyPaste = new CopyCore();
        // 生成真正唯一的编辑器实例ID
        this.editorInstanceId = this.generateUniqueEditorId();
    }

    private generateUniqueEditorId(): string {
        return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}-${performance.now()}`;
    }

    // private currentPar: any = null;

    public removeRespateBtn = () => {
        if (this.isActiveRepaste) {
            this.hideRepastePanel();
        }
    }

    public stopUnSelection = (option: {isStop: boolean}, e: MouseEvent) => {
        if ((e.button === 2 || this.bUnSelected) && this.host.getSelected()) {
            option.isStop = true;
        }
    }

    public stopProgapation = (e: any) => {
        // if (e.stopProgapation) {
        //     e.stopProgapation();
        // }
        e.preventDefault();
        return false;
    }

    public repasteDialogEvent = (e: any) => {
        const target = e.target;
        if (target.nodeName !== 'BUTTON') {
            return;
        }
        const html = this.repasteDialogDom.querySelector('.dialog-content').innerHTML;
        this.showPasteDialog = false;
        if (html) {
            const documentCore = this.host.documentCore;
            documentCore.undo();
            this.execPaste();
        }
        const parent = this.repasteDialogDom.parentNode as HTMLDivElement;
        parent.className = parent.className.replace(' active', '');
        this.hideRepastePanel();
    }

    public cutEvent(): number {
        const date = new Date();
        if (this.copyDisabled) {
            this.stopDocClick = true;
            return;
        }
        const copyPaste = this.getCopyPaste();
        copyPaste.setCopyType(CopyType.CopyClickCtrlX); // 剪切
        const html = this.getSelection();
        const documentCore  = this.host.documentCore;
        const res = documentCore.removeSelectedContent();
        if (res !== 0) {
            return res;
        }
        this.host.handleRefresh();
        this.setContentToClipboard(html);
        gEvent.setEvent(this.host.docId, gEventName.RevisionChange);
        logger.devlog({id: this.docId, name: 'cutEvent', startTime: date});
        return res;
    }

    public rightMenuClick = (e: any, option: {isStop: boolean}) => {
        e.preventDefault();
        const target = e.target;
        let activeNode = target;
        if (target.nodeName !== 'LI') {
            activeNode = target.parentNode;
        }
        if (activeNode.nodeName === 'LI' &&
            ( '剪切' === activeNode.innerText || '复制' === activeNode.innerText
                || '粘贴' === activeNode.innerText || '删除' === activeNode.innerText ) ) {
            const type = parseInt(activeNode.getAttribute('data-index'), 10);
            const documentCore  = this.host.documentCore;

            this.stopDocClick = false;
            switch (type) {
                case 0:
                    if ( documentCore.canDelete() ) {
                        this.cutEvent(); // 剪切
                    }
                    break;
                case 1:
                    this.copyEvent(); // 复制
                    break;
                case 2:
                case 3:
                    if ( documentCore.canInput() ) {
                        this.pasteEvent(type); // 粘贴
                    }
                    break;
                case 4:
                    if ( documentCore.canDelete() ) {
                        this.deleteEvent(); // 删除
                    }
                    break;
            }
            option.isStop = this.stopDocClick;
        }
    }

    public docmentCopyEvent = (e: any): void => {
        e.stopPropagation();
        const date = new Date();
        const timestamp = date.getTime();

        //修改复制类型 从CopyCtrlC修改为CopyClickCtrlC tinyzhi 2025.6.10
        // this.copyType = CopyType.CopyCtrlC;
        this.copyType = CopyType.CopyClickCtrlC;
        const copyPaste = this.getCopyPaste();
        copyPaste.setCopyType(CopyType.CopyClickCtrlC); // 设置CopyCore实例的复制类型，确保结构化信息正确保存
        copyPaste.setInternalTimestamp(timestamp); // 设置内部时间戳
        const html = this.getSelection();
        const plainText = this.getPlainText(html);

        // 保存到localStorage（包含纯文本和Blob）
        this.saveToLocalStorageWithText(timestamp, copyPaste, plainText);

        // 使用纯文本方案
        if (e.clipboardData) {
            try {
                // 设置HTML格式
                e.clipboardData.setData('text/html', html);
                // 设置纯文本格式
                e.clipboardData.setData('text/plain', plainText);
                // 阻止默认复制行为
                e.preventDefault();

            } catch (error) {
                console.error('[复制] clipboardData设置失败:', error);
                this.setContentToClipboard(html);
            }
        } else {
            this.setContentToClipboard(html);
        }
        logger.devlog({id: this.docId, name: 'docmentCopyEvent', startTime: date});
    }

    // 剪切事件
    public documentCutEvent = (e: any): void => {
        e.stopPropagation();
        if (!this.host.getSelected()) {
            return;
        }
        this.copyDisabled = false;
        this.cutEvent();
        // this.copyType = CopyType.copyCtrlX;
        // const html = this.getSelection();
        // this.setContentToClipboard(html);
    }

    // toRtf(html: string) {
    //     console.log(html)
    //     let rtf = new HtmlToRtf().parse(html);
    //     console.log(rtf)
    // }

    public documentPasteEvent = (e: any): void => {
        // console.log(e)
        e.stopPropagation();
        if (this.host.documentCore.isProtectedMode()) {
            return;
        }
        this._startTime = new Date();
        const copyPaste = this.getCopyPaste();
        copyPaste.setPasteType(PasteType.PasteCtrlV);

        // 使用纯文本相似度检测内容来源
        this.detectContentSourceByText(e, copyPaste);
    }

    public contextmenu = (e: any, panel: HTMLDivElement, option: {isStop: boolean, copy: boolean}) => {
        e.stopPropagation();
        const target = e.target;
        if (this.isSourceContextmenu) {
            if (!target.className ||  target.className !== 'editor-dialog-content dialog-content') {
                e.preventDefault();
            }
            option.isStop = true;
            return;
        }
        this.operatePanelDom = panel;
        (async () => {
            let sourceText;
            let text = sourceText = await this.getClipboardData();
            // console.log(text);
            if (text === null) {
                this.getCopyPaste()
                .setWebPageFlag();
                text = this.getCopyPaste()
                .getText();
            } else {
                this.getCopyPaste()
                .setWebPageFlag(false);
            }

            const access = this.getCopyPaste()
                .getPermission(sourceText);

            const menu = [];
            const copyObj = {index: MenuItemIndex.Copy, value: true, key: 'disabled'};
            menu.push(copyObj);
            if (this.host.isSelected()) {
                copyObj.value = false;
                this.copyDisabled = false;
            }
            copyObj.value = option.copy && copyObj.value;
            const deleteObj = {...copyObj};
            deleteObj.index = MenuItemIndex.Delete;
            menu.push(deleteObj);
            const cutObj = {...copyObj};
            cutObj.index = MenuItemIndex.Cut;
            menu.push(cutObj);
            const docId: number = this.host.docId;

            const pasteObj = {index: MenuItemIndex.Format, value: false, key: 'disabled'};
            menu.push(pasteObj);
            if (text === null) {
                pasteObj.value = true;
                this.pasteUnAccess = true;
            } else if (access !== true) {
                pasteObj.value = true;
                if (this.pasteDisabled === false) {
                    this.pasteDisabled = true;
                }
                if (!this.pasteUnAccess) {
                    this.pasteUnAccess = true;
                    pasteObj.value = true;
                }
                gEvent.setEvent(docId, 'setToRightMenu', menu);
                return;
            } else {
                if (this.pasteUnAccess === true) {
                    this.pasteUnAccess = false;
                    // pasteObj.value = false;
                }
            }

            const isOwnerContent = this.getCopyPaste()
                .isOwnerContent(text);
            if (isOwnerContent === true && text) {
                panel.className += ' ownerEditor';
                pasteObj.value = false;
                this.pasteDisabled = false;
            } else {
                // if (this.pasteDisabled)
                this.pasteDisabled = true;
                // pasteObj.value = true;
            }
            gEvent.setEvent(docId, 'setToRightMenu', menu);
            // console.log(menu) // menu:  key + value -> item disabled or not
        })();
        this.createOperateDom(panel);
    }

    public repasteEvent = (e: any) => {
        const target = e.target;
        const dom = this.repasteDom.querySelector('.repaste-btns');
        const className = target.className;
        if (dom.className.indexOf('active') > -1) {
            switch (className) {
                case 'format':
                    this.showDialog();
                    break;
                case 'unformat':
                    this.hideRepastePanel();
                    break;
                default:
                    // dom.className = dom.className.replace(' active', '');
            }
            if (className.indexOf('repaste-ctr') > -1 || target.parentNode.className.indexOf('repaste-ctr') > -1) {
                dom.className = dom.className.replace(' active', '');
            }
        } else {
            dom.className += ' active';
        }
    }

    public copyEvent(host?: any): void {
        if (host) {
            this.host = host;
        }
        const date = new Date();
        const timestamp = date.getTime();

        if (this.copyDisabled) {
            this.stopDocClick = true;
            return;
        }
        let html: string;
        const copyPaste = this.getCopyPaste();
        copyPaste.setCopyType(CopyType.CopyClickCtrlC); // 复制
        copyPaste.setInternalTimestamp(timestamp); // 设置内部时间戳
        html = this.getSelection();
        const plainText = this.getPlainText(html);

        // 保存到localStorage（包含纯文本和Blob）
        this.saveToLocalStorageWithText(timestamp, copyPaste, plainText);

        // 右键复制使用原有方式
        this.setContentToClipboard(html);
        logger.devlog({id: this.docId, name: 'copyEvent', startTime: date});
    }

    public async pasteEvent(type: number, host?: any): Promise<boolean> {
        if (host) {
            this.host = host;
        }
        this._startTime = new Date();
        const copyPaste = this.getCopyPaste();
        const documentCore = this.host.documentCore;
        copyPaste.setDocument(documentCore.getDocument());
        if (type === 2) {
            if (this.pasteUnAccess) {
                this.stopDocClick = true;
                return false;
            }
            copyPaste.setPasteType(PasteType.PasteClickCtrlV); // 格式粘贴
            if (this.pasteDisabled === false) {
                const parste = this.getCopyPaste();
                const myhost = this.host;
                if (myhost && myhost.host) {
                    const copyOption = myhost.host.copyOption;
                    parste.setFormatFlag(copyOption.bInnerFormat, copyOption.bOuterFormat);
                }
                // 如果需要粘贴的内容来自本编辑器，则直接取缓存进行粘贴
                const res = this.insertPara(parste.getParagraph());
                gEvent.setEvent(this.host.docId, gEventName.RevisionChange);
                this.bUnSelected = false;
                logger.devlog({id: this.docId, name: 'pasteEvent', startTime: this._startTime});
                return res;
            }
        } else {
            if (this.pasteDisabled) {
                this.stopDocClick = true;
                return false;
            }
            copyPaste.setPasteType(PasteType.PasteClickCtrlShiftV); // 去格式粘贴
        }
        this.bUnSelected = true;

        const result = await this.getClipboardData();
        // 右键粘贴，无权限读取剪切板的情况下才使用同一页
        const bSameWebPage = type === 2 && result === null;
        return await this.insertContent(result, type, bSameWebPage);
    }

    public documentDrop = (e: any) => {
        e.preventDefault();
        const content = e.dataTransfer.getData('text');
        if (!content) {
            return;
        }

        const host = this.host;
        host.setCursorByXY(e.offsetX, e.offsetY, e);
        if (!host.documentCore.canInput()) {
            message.error('此处为只读，不支持此操作');
            return;
        }

        this.insertContent(content, PasteType.PasteCtrlV);
    }

    public handleDragOver = (event: DragEvent) => {
        // 需要阻止over的默认行为，才会最终触发drop
        event.preventDefault();
        // const { documentCore } = this.state;
        // // 光标更新
        // const element = this.isCursorInElement(event);
        // if(element) {
        //   this.insertCursor(element.pageNode)
        //   this.setCursorPosition(element.curPorperty);
        //   this.cursor.setAttribute("style", "visibility:visible");
        // } else {
        //   if(this.cursor)
        //     this.cursor.setAttribute("style", "visibility:hidden");
        // }
      }

      public getClipboardData(): Promise<string> {
        const clipboard = (window.navigator as any).clipboard;
    
        return new Promise(async (resolve, reject) => {
            // 检查 Clipboard API 是否可用
            if (!clipboard) {
                //console.warn('Clipboard API is not supported');
                return resolve(null);
            }
    
            try {
                // 检查剪贴板读取权限
                const permissionStatus = await navigator.permissions.query({ name: 'clipboard-read' as PermissionName });
    
                // 如果权限没有被允许或者被拒绝，直接返回 null
                if (permissionStatus.state !== 'granted') {
                    return resolve(null);
                }
    
                // 在用户触发事件时读取剪贴板内容
                const text = await clipboard.readText();
                resolve(text);
            } catch (error) {
                //console.warn('Failed to read clipboard:', error);
                resolve(null); // 发生错误时，返回 null
            }
        });
    }

    public getCopyPaste(): CopyCore {
        let copyPaste: CopyCore;
        if (!this.copyPaste) {
            copyPaste = this.copyPaste = new CopyCore();
            try {
                const host = this.host.host.copyOption;
                if (host.staticWebId) {
                    copyPaste.setStaticId(host.staticWebId);
                }
                if (host.sCopyInformation !== undefined) {
                    copyPaste.setAccessId(host.sCopyInformation);
                }
                if (host.bEnable !== undefined) {
                    copyPaste.setAccess(host.bEnable);
                }
            } catch (error) {
                console.error(error);
            }
        } else {
            copyPaste = this.copyPaste;
        }

        return copyPaste;
    }

    // 无格式插入
    public async insertContent(str: string, type?: number, bSameWebPage?: boolean): Promise<boolean> {
        const copyPaste = this.getCopyPaste();
        copyPaste.clearContent();
        if (type !== undefined) {
            copyPaste.setPasteType(type);
        }

        copyPaste.addContent(str, 'text/plain');
        copyPaste.setSameWebPage(bSameWebPage);
        // tslint:disable-next-line: ban-types
        // let callback: Function;
        // if (type === 2) {
        //     callback = () => {setTimeout(() => {this.initRepastePanel(); }, 20); };
        // }
        // this.execPaste(callback);
        return await this.execPaste();
    }

    protected createOperateDom(panel: HTMLDivElement): void {
        return;
    }

    protected setExtraCopyInformation = (sCopyInformation: string): void => {
        const paste = this.getCopyPaste();
        if (this.host && this.host.host) {
            const copyOption = this.host.host.copyOption;
            // host.sCopyInformation = sCopyInformation;
            copyOption.staticWebId = paste.getStaticId();
        }
        paste.setAccessId(sCopyInformation);
    }

    protected enableCopyFromExternal = (bEnable: boolean): void => {
        const paste = this.getCopyPaste();
        if (this.host && this.host.host) {
            const copyOption = this.host.host.copyOption;
            // host.bEnable = bEnable;
            copyOption.staticWebId = paste.getStaticId();
        }
        paste.setAccess(bEnable);
    }

    // 有格式插入
    private insertPara(para: any[]): boolean {
        if (!para) {
            return false;
        }

        const time = new Date();
        const documentCore = this.host.documentCore;
        const elementMonitor = documentCore.getElementMonitor();
        const bAddAction = elementMonitor?.canAddAction(MonitorAction.Paste);
        if (bAddAction) {
            elementMonitor.addEditAction();
        }

        const bSucceed = documentCore.insertContent(para);
        if (bSucceed !== false) {
            if (bAddAction) {
                let pasteText = '';
                para.forEach((item) => {
                    pasteText += item.getSelectText(true);
                });
                elementMonitor.addAction(undefined, {start: getCurTime(time),
                    end: getCurTime(),
                    type: MonitorAction.Paste, pasteContent: pasteText});
            }

            this.host.handleRefresh();
        }

        return bSucceed;
    }

    // tslint:disable-next-line: ban-types
    private execPaste(callback?: Function): Promise<boolean> {
        return new Promise((resolve, reject) => {
            // 通过弹窗进行粘贴
            if (this.showPasteDialog === true) {
                resolve(false);
                return;
            }

            const patse = this.getCopyPaste();
            const myhost = this.host;
            if (myhost && myhost.host) {
                const copyOption = myhost.host.copyOption;
                patse.setFormatFlag(copyOption.bInnerFormat, copyOption.bOuterFormat);
            }
            const documentCore = this.host.documentCore;
            patse.setDocument(documentCore.getDocument());
            patse.execPaste()
                .then((para) => {
                    let bUnSelected = false;
                    if (this.bUnSelected === true) {
                        bUnSelected = true;
                        this.bUnSelected = false;
                    }

                    if (this.insertPara(para) === false) {
                        resolve(false);
                        return;
                    }
                    logger.devlog({id: this.docId, name: 'execPaste', startTime: this._startTime});
                    // 让光标闪起来
                    if (bUnSelected === true) {
                        setTimeout(() => {
                            this.host.updateCursor(true, true);
                        }, 10);
                    }
                    if (typeof callback === 'function') {
                        callback.call(this);
                    }
                    gEvent.setEvent(this.host.docId, gEventName.RevisionChange);
                    resolve(true);
            });
        });
    }

    private deleteEvent(): number {
        const date = new Date();
        if (this.copyDisabled) {
            this.stopDocClick = true;
            return;
        }
        const documentCore  = this.host.documentCore;
        const res = documentCore.removeSelectedContent();
        this.host.handleRefresh();
        logger.devlog({id: this.docId, name: 'deleteEvent', startTime: date});
        return res;
    }

    private setContentToClipboard(html: string): void {
        if (!html) {
            return;
        }

        this.copyDom.innerHTML = html;
        const win = this.copyIframeWindow;
        const element = this.copyDom;
        setTimeout(() => {
            if (document.body['createTextRange']) {
                const range = win.body.createTextRange();
                range.moveToElementText(element);
                range.select();
            } else if (window.getSelection) {
                win.getSelection()
                    .removeAllRanges();
                // const selection = win.getSelection();
                // const range = win.document.createRange();
                // range.selectNodeContents(element);
                // selection.removeAllRanges();
                // selection.addRange(range);

                const range = win.document.createRange();
                range.selectNode(element);
                win.getSelection()
                    .addRange(range);
            }
            win.document.execCommand('copy');
        }, 10);
    }

    private async saveToLocalStorageWithText(timestamp: number, copyPaste: any, plainText: string): Promise<void> {
        try {
            const apolloData = copyPaste.getApolloContents();

            // 生成Blob流用于跨编辑器复制
            let blobString = '';
            try {
                // 尝试不同的方式获取编辑器接口
                let editor = null;
                if (typeof this.host.getEditor === 'function') {
                    editor = this.host.getEditor();
                } else if (this.host.host && typeof this.host.host.getEditor === 'function') {
                    editor = this.host.host.getEditor();
                } else {
                    return;
                }

                // 使用正确的参数设置，保留结构化元素
                const options = {
                    NeedRevision: 0,
                    NeedHeaderFooter: 0,
                    NeedStruct: 1  // 现在1表示保留所有结构化元素
                };

                const blob = await editor.saveSelectAreaToStream(JSON.stringify(options));
                if (blob) {
                    blobString = await this.blobToBase64(blob);
                }
            } catch (error) {
                console.error('[复制阶段] 生成Blob失败:', error);
            }

            // 获取当前编辑器的author信息
            let author = '';
            try {
                const copyPasteInstance = this.getCopyPaste();
                if (copyPasteInstance) {
                    author = copyPasteInstance.getAuthor();
                }
            } catch (error) {
                // 获取author失败，使用空字符串
            }

            const clipboardData = {
                timestamp: timestamp,
                apolloData: apolloData,
                plainText: plainText,
                textLength: plainText.length,
                blobString: blobString,
                editorId: this.editorInstanceId,
                author: author  // 添加author信息
            };
            localStorage.setItem('editor-clipboard', JSON.stringify(clipboardData));
        } catch (e) {
            console.error('[localStorage] localStorage保存失败:', e);
        }
    }

    private async blobToBase64(blob: Blob): Promise<string> {
        const buffer = await blob.arrayBuffer();
        const bytes = new Uint8Array(buffer);
        let binary = '';
        for (let i = 0; i < bytes.byteLength; i++) {
            binary += String.fromCharCode(bytes[i]);
        }
        return btoa(binary);
    }

    private base64ToBlob(base64String: string): Blob {
        const binaryString = atob(base64String);
        const bytes = new Uint8Array(binaryString.length);
        for (let i = 0; i < binaryString.length; i++) {
            bytes[i] = binaryString.charCodeAt(i);
        }
        return new Blob([bytes]);
    }

    private handleExternalContent(e: any, copyPaste: any): void {
        // 检查是否允许外部复制
        const copyOption = this.host.host?.copyOption || {};
        const bEnableExternal = copyOption.bEnable !== false;

        if (!bEnableExternal) {
            return;
        }

        // 按现有逻辑处理外部内容
        const items = e.clipboardData.items;

        if (items.length > 0) {
            let index = 0;
            copyPaste.clearContent();
            Array.from(items)
            .forEach((item: any) => {
                index++;
                const type = item.type;

                if (item.kind === 'file' && item.type.indexOf('image/') > -1) {
                    copyPaste.addContent(null, 'image', item.getAsFile());
                    if (index === copyPaste.getContentLen()) {
                        this.execPaste();
                    }
                } else {
                    item.getAsString((text: string) => {
                        copyPaste.addContent(text, type);
                        if (index === copyPaste.getContentLen()) {
                            this.execPaste();
                        }
                    });
                }
            });
        } else {
        }
    }

    private getLocalStorageClipboard(): any {
        try {
            const stored = localStorage.getItem('editor-clipboard');

            if (!stored) {
                return null;
            }

            const data = JSON.parse(stored);

            const isValid = this.isValidTimestamp(data.timestamp);

            if (!isValid) {
                localStorage.removeItem('editor-clipboard');
                return null;
            }
            return data;
        } catch (e) {
            localStorage.removeItem('editor-clipboard');
            return null;
        }
    }

    private isValidTimestamp(timestamp: number): boolean {
        const now = Date.now();
        const fiveMinutes = 5 * 60 * 1000;
        const diff = now - timestamp;
        const isValid = timestamp > 0 && diff < fiveMinutes;
        return isValid;
    }

    private useInternalClipboard(copyPaste: any): void {
        // 使用内部缓存，保持完整的结构化信息
        const paragraph = copyPaste.getParagraph();
        const res = this.insertPara(paragraph);
        gEvent.setEvent(this.host.docId, gEventName.RevisionChange);
    }

    private useLocalStorageClipboard(localStorageData: any, copyPaste: any): void {
        // 使用localStorage中的Apollo数据
        copyPaste.clearContent();

        if (localStorageData.apolloData) {
            try {

                copyPaste.addContent(localStorageData.apolloData, 'text/Apollo');
                const paragraph = copyPaste.getParagraph();

                if (!paragraph) {
                    console.warn('[使用localStorage] 段落数据为空，无法粘贴');
                    return;
                }
                const res = this.insertPara(paragraph);
              

                gEvent.setEvent(this.host.docId, gEventName.RevisionChange);
               

            } catch (error) {
            }
        } else {
        }
    }

    private usePlainTextClipboard(plainText: string, copyPaste: any): void {
        // 清空copyPaste内容
        copyPaste.clearContent();

        // 添加纯文本内容
        copyPaste.addContent(plainText, 'text/plain');

        // 执行粘贴
        this.execPaste();
    }

    /**
     * 验证跨编辑器复制的author权限
     * @param localStorageData localStorage中的数据
     * @returns 是否允许粘贴
     */
    private validateCrossEditorAuthor(localStorageData: any): boolean {
        // 获取localStorage中的author信息
        const storedAuthor = localStorageData.author;

        // 获取当前编辑器的author信息
        let currentAuthor = '';
        try {
            const copyPasteInstance = this.getCopyPaste();
            if (copyPasteInstance) {
                currentAuthor = copyPasteInstance.getAuthor();
            }
        } catch (error) {
            // 获取失败，使用空字符串
        }

        // 如果localStorage中存在author值（非空）且当前编辑器中author值非空，且两个值不一样，则不允许粘贴
        if (storedAuthor && currentAuthor && storedAuthor !== currentAuthor) {
            message.error('禁止不同病人间拷贝病历!');
            return false;
        }

        return true;
    }

    private async useCrossEditorClipboard(localStorageData: any, copyPaste: any): Promise<void> {
        // 跨编辑器复制需要进行author验证
        if (!this.validateCrossEditorAuthor(localStorageData)) {
            console.log('[跨编辑器拷贝] Author验证失败，不执行粘贴');
            return; // 验证失败，不执行粘贴
        }

        if (!localStorageData.blobString) {
            // 没有Blob数据，直接使用纯文本
            console.log('[跨编辑器拷贝] 降级原因：localStorage中没有blob数据');
            this.usePlainTextClipboard(localStorageData.plainText, copyPaste);
            return;
        }

        try {
            // 将base64字符串转换为Blob
            const blob = this.base64ToBlob(localStorageData.blobString);

            // 通过insertFileWithStream插入
            let editor = null;
            if (typeof this.host.getEditor === 'function') {
                editor = this.host.getEditor();
            } else if (this.host.host && typeof this.host.host.getEditor === 'function') {
                editor = this.host.host.getEditor();
            } else {
                // 无法获取编辑器接口，降级到纯文本
                this.usePlainTextClipboard(localStorageData.plainText, copyPaste);
                return;
            }

            // 使用空的options对象
            const options = {};
            const result = await editor.insertFileWithStream(blob, options);

            if (result === 0) { // ResultType.Success
                // Blob插入成功
            } else {
                // Blob插入失败，降级到纯文本
                this.usePlainTextClipboard(localStorageData.plainText, copyPaste);
            }

        } catch (error) {
            // 异常情况，降级到纯文本
            this.usePlainTextClipboard(localStorageData.plainText, copyPaste);
        }
    }

    private getPlainText(html: string): string {
        // 从HTML中提取纯文本
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = html;
        return tempDiv.textContent || tempDiv.innerText || '';
    }





    private getSelection(): string {
        const documentCore = this.host.documentCore;
        if (!documentCore.canCopy()) {
            return;
        }
        const copyPaste = this.getCopyPaste();
        copyPaste.setDocument(documentCore.getDocument());
        const selections = documentCore.getSelectedContent();
        if (!selections) {
            return;
        }
        return copyPaste.execCopy(selections);
    }

    private detectContentSourceByText(e: any, copyPaste: any): void {
        const items = e.clipboardData.items;
        if (items.length === 0) {
            this.tryUseLocalStorageAsFallback(copyPaste);
            return;
        }

        // 获取纯文本内容
        let plainTextItem = null;
        for (let i = 0; i < items.length; i++) {
            if (items[i].type === 'text/plain') {
                plainTextItem = items[i];
                break;
            }
        }

        if (plainTextItem) {
            plainTextItem.getAsString((clipboardText: string) => {
                // 检查是否为内部内容
                if (this.isInternalContentByText(clipboardText)) {
                    this.handleInternalContentByText(copyPaste);
                } else {
                    this.handleExternalContentDirect(clipboardText, copyPaste);
                }
            });
        } else {
            this.handleExternalContent(e, copyPaste);
        }
    }

    private isInternalContentByText(clipboardText: string): boolean {
        const localStorageData = this.getLocalStorageClipboard();
        if (!localStorageData || !this.isValidTimestamp(localStorageData.timestamp)) {
            return false;
        }

        if (!localStorageData.plainText) {
            return false;
        }

        // 简单高效的相似度计算
        const similarity = this.calculateTextSimilarity(clipboardText, localStorageData.plainText);
        return similarity;
    }

    private calculateTextSimilarity(text1: string, text2: string): boolean {
        // 精确匹配
        if (text1 === text2) {
            return true;
        }

        // 长度差异过大
        if (Math.abs(text1.length - text2.length) > 10) {
            return false;
        }

        // 前缀匹配（取较短文本的长度，最多50字符）
        const minLength = Math.min(text1.length, text2.length);
        const prefixLength = Math.min(50, minLength);
        const prefixMatch = text1.substring(0, prefixLength) === text2.substring(0, prefixLength);

        return prefixMatch;
    }

    private handleInternalContentByText(copyPaste: any): void {
       
        // 检查业务控制
        const copyOption = this.host.host?.copyOption || {};
        const sCopyInformation = copyOption.sCopyInformation || '';

        if (sCopyInformation) {
            // 权限验证逻辑
        }

        // 获取格式控制设置
        const bInnerFormat = copyOption.bInnerFormat !== false; // 默认为true，控制内部Apollo格式
        const bOuterFormat = copyOption.bOuterFormat !== false; // 默认为true，控制外部内容格式
    
        // 数据源选择逻辑
        const internalTimestamp = copyPaste.getInternalTimestamp();
        const localStorageData = this.getLocalStorageClipboard();

        if (internalTimestamp > 0 && localStorageData && internalTimestamp === localStorageData.timestamp) {
            if (!bInnerFormat) {
                this.usePlainTextClipboard(localStorageData.plainText, copyPaste);
            } else {
                this.useInternalClipboard(copyPaste);
            }
        } else if (localStorageData && this.isValidTimestamp(localStorageData.timestamp)) {
            // 判断是否跨编辑器
            const isCrossEditor = localStorageData.editorId !== this.editorInstanceId;

            if (isCrossEditor) {
                if (!bInnerFormat) {
                    this.usePlainTextClipboard(localStorageData.plainText, copyPaste);
                } else {
                    this.useCrossEditorClipboard(localStorageData, copyPaste);
                }
            } else {
                if (!bInnerFormat) {
                    this.usePlainTextClipboard(localStorageData.plainText, copyPaste);
                } else {
                    this.useLocalStorageClipboard(localStorageData, copyPaste);
                }
            }
        } else {
        }
    }



    private tryUseLocalStorageAsFallback(copyPaste: any): void {
        const localStorageData = this.getLocalStorageClipboard();

        if (localStorageData && this.isValidTimestamp(localStorageData.timestamp)) {
            // 判断是否跨编辑器
            const isCrossEditor = localStorageData.editorId !== this.editorInstanceId;

            if (isCrossEditor) {
                // 跨编辑器需要进行author验证
                if (!this.validateCrossEditorAuthor(localStorageData)) {
                    return; // 验证失败，不执行粘贴
                }
                this.useCrossEditorClipboard(localStorageData, copyPaste);
            } else {
                this.useLocalStorageClipboard(localStorageData, copyPaste);
            }
        } else {
        }
    }

    private handleExternalContentDirect(plainText: string, copyPaste: any): void {

        // 检查是否允许外部复制
        const copyOption = this.host.host?.copyOption || {};
        const bEnableExternal = copyOption.bEnable !== false;
        const bOuterFormat = copyOption.bOuterFormat !== false; // 默认为true，控制外部内容格式
      
        if (!bEnableExternal) {
            return;
        }

        // 根据bOuterFormat设置处理外部内容
        copyPaste.clearContent();
        copyPaste.addContent(plainText, 'text/plain');

        // 注意：外部纯文本内容本身就是无格式的，bOuterFormat主要影响HTML等富文本内容
        // 这里的纯文本处理不受bOuterFormat影响，但我们记录日志以便调试
        this.execPaste();
    }

    private hideRepastePanel(): void {
        this.isSourceContextmenu = false;
        const dom = this.repasteDom;
        dom.className = dom.className.replace(/ active/g, '');
        const activeNode = dom.querySelector('.active');
        if (activeNode) {
            activeNode.className = activeNode.className.replace(/ active/g, '');
        }
        this.isActiveRepaste = false;
    }

    private showDialog(): void {
        this.getCopyPaste()
            .clearContent();
        this.isSourceContextmenu = true;
        this.showPasteDialog = true;
        const parentNode = this.repasteDialogDom.parentNode as HTMLDivElement;
        parentNode.className += ' active';
        this.repasteDialogDom.querySelector('.dialog-content').innerHTML = '';
    }

}
