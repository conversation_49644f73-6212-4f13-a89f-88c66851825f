import { ISerialParaObj, ISerialTableCellObj } from '../serialize/serialInterface';
import { ICellSpanInfo, IExcel } from './IExcel';
import ExcelJS, { RichText } from 'exceljs';
import ExcelParagraph from './ExcelParagraph';

export default class ExcelTableCell implements IExcel {
    private spanInfo?: ICellSpanInfo;
    constructor(private tableCell: ISerialTableCellObj) {

    }

    public get cellSpanInfo(): ICellSpanInfo | null {
        return this.spanInfo || null;
    }

    public buildTo(cell: ExcelJS.Cell): ExcelTableCell {
        const { columnSpan, rowSpan } = this.tableCell;
        if (rowSpan > 1 || columnSpan > 1) {
            this.spanInfo = {
                colSpan: columnSpan || 1,
                rowSpan,
            };
        }

        // 单元格样式
        const { borders, verticalAlign } = this.tableCell;
        const cellStyle: Partial<ExcelJS.Style> = {
            alignment: {
                wrapText: true,
                vertical: this.getVertical(verticalAlign),
            }
        };
        if (borders) {
            cellStyle.border = {
                bottom: {
                    color: this.getColor(borders.bottom?.color),
                    style: this.getBorderStyle(borders.bottom?.style),
                },
                left: {
                    color: this.getColor(borders.left?.color),
                    style: this.getBorderStyle(borders.left?.style),
                },
                right: {
                    color: this.getColor(borders.right?.color),
                    style: this.getBorderStyle(borders.right?.style),
                },
                top: {
                    color: this.getColor(borders.top?.color),
                    style: this.getBorderStyle(borders.top?.style),
                }
            };
        }

        const paraTexts: RichText[] = [];
        let idx = 0;
        for (const para of this.tableCell.children) {
            const texts = new ExcelParagraph(para as ISerialParaObj).toRichText();
            if (idx++ !== 0) {
                const font = texts[texts.length - 1]?.font || {};
                paraTexts.push({
                    text: '\r',
                    font: { ...font },
                });
            } else {
                // 单元格使用首段对齐信息
                cellStyle.alignment.horizontal = (para as ISerialParaObj).alignment as any;
            }
            paraTexts.push(...texts);
        }
        cell.value = { richText: paraTexts };

        cell.style = cellStyle;

        return this;
    }

    private getBorderStyle(style?: string): undefined | 'thin' | 'dashDot' {
        switch (style) {
            case 'single': {
                return 'thin';
            }
            case 'dashed': {
                return 'dashDot';
            }
        }
        return undefined;
    }

    private getColor(color?: string): any {
        if (!color) {
            return undefined;
        }
        return {
            argb: color.replace('#', ''),
        };
    }

    private getVertical(vert: string): 'top' | 'middle' | 'bottom' {
        switch (vert.toLowerCase()) {
            case 'top': {
                return 'top';
            }
            case 'center': {
                return 'middle';
            }
            case 'bottom': {
                return 'bottom';
            }
        }
        return 'bottom';
    }

}
