// import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from './../ChinaCity';
import { DocumentCore } from '../../model/DocumentCore';
import { EmrEditor } from '../../components/editor/Main';
import { ResultType, NewControlPropName, INewControlProperty, EXTERNAL_STRUCT_TYPE,
    EXTERNAL_OUTER_STRUCT_TYPE, ICustomProps, DataType, filterChars2,
    ICustomFormatDateProps, NewControlType, DateBoxFormat, CodeValueItem,
    STD_START_DEFAULT, isValiDate, NewControlFilterType, IDocumentBuffer, isValidUnit,
    DocumentSectionType,
    SignatureType,
    isValidName,
    IStructParamJson,

    filterChars,
    MonitorAction,
    NewControlErrorInfo} from '../commonDefines';
import { NewControl as ParaNewControl } from '../../model/core/NewControl/NewControl';
import Apollo from '../copy/apollo';
import { NewControlNumer } from '../../model/core/NewControl/NewControlNum';
import { NewComboBox } from '../../model/core/NewControl/NewComboBox';
import { ExternalAction } from './ExternalAction';
import { NewControlSignature } from '../../model/core/NewControl/NewControlSignature';
import { NewControlText } from '../../model/core/NewControl/NewControlText';
import { Reader } from '../../format/reader/reader';
import * as JSZip from '../jszip';
import { analyzeFileForXmlInfo, getCurTime, skipEscapeString } from '../commonMethods';
import { NewControlDate } from '../../model/core/NewControl/NewControlDate';
import { NewControlAddress } from 'src/model/core/NewControl/NewControlAddress';
// import addressData from '../resources/china-division.json';
// import addressData from '../resources/Regions.json';
// import addressData from '../resources/china-city.json';
// import { IAddressPair } from '../../components/editor/module/document/NewAddressbox';

const customFormatProps = {
    Year: 'Year',
    YearSeparat: 'YearSeparat',
    Month: 'Month',
    MonthSeparat: 'MonthSeparat',
    Day: 'Day',
    DaySeparat: 'DaySeparat',
    Hour: 'Hour',
    HourSeparat: 'HourSeparat',
    Minute: 'Minute',
    MinuteSeparat: 'MinuteSeparat',
    Second: 'Second',
    SecondSeparat: 'SecondSeparat',
    Millisecond: 'Millisecond',
    MillisecondSeparat: 'MillisecondSeparat',
};

export default class NewControl extends ExternalAction {
    private _documentCore: DocumentCore;
    private _host: EmrEditor;
    private _propNames: string[];
    constructor(host: EmrEditor) {
        super(host.state.documentCore);
        this._host = host;
        this._documentCore = host.state.documentCore;
        this._propNames = [NewControlPropName.DeleteProtect, NewControlPropName.EditProtect, NewControlPropName.Hidden,
            NewControlPropName.HiddenBackground, NewControlPropName.ShowBorder, NewControlPropName.IsMustInput,
        NewControlPropName.CopyProtect, NewControlPropName.ReverseEdit, NewControlPropName.IsKeyJump,
        NewControlPropName.Placeholder, NewControlPropName.Identifier, NewControlPropName.Alignments,
        NewControlPropName.SerialNumber, NewControlPropName.DisplayType, NewControlPropName.Helptip,
        NewControlPropName.Title, NewControlPropName.FixedLength, NewControlPropName.MaxLength,
        NewControlPropName.PrefixContent, NewControlPropName.SelectPrefixContent, NewControlPropName.Separator,
        NewControlPropName.ShowValue, NewControlPropName.MaxValue, NewControlPropName.MinValue,
        NewControlPropName.Precision, NewControlPropName.Unit, NewControlPropName.ForceValidate,
        NewControlPropName.Retrieve, NewControlPropName.ShowCodeAndValue, NewControlPropName.TextBorder,
        NewControlPropName.PrintSelected, NewControlPropName.Group, NewControlPropName.HideHasTitle,

        NewControlPropName.ShowSignBorder, NewControlPropName.LabelCode, NewControlPropName.SpaceNum,
        NewControlPropName.Hierarchy, NewControlPropName.SupportLine];
    }

    public setNewControlText(sName: string, sText: string): number {
        if (!sName || typeof sText !== 'string') {
            return ResultType.ParamError;
        }
        
        const startTime = getCurTime();
        const elementMonitor = this._documentCore.getElementMonitor();
        const bAddAction = elementMonitor?.canAddAction(MonitorAction.Insert);
        if (bAddAction) {
            elementMonitor.addEditAction();
        }

        const result = this._documentCore.setNewControlText(sName, sText);

        if (result === ResultType.Success && sText && bAddAction) {
            const element = elementMonitor.getMonitorElement(this._documentCore.getNewControlByName(sName));
            if (element) {
                elementMonitor.addAction(element.getSerialNumber(), {start: startTime,
                    end: getCurTime(),
                    type: MonitorAction.Insert, insertContent: filterChars2(sText)});
            }
        }

        if (result === ResultType.Success) {
            this._host.handleRefresh();
        }

        return result;
    }

    public setStructsBorderVisible(bFlag: boolean): number {
        if (typeof bFlag !== 'boolean') {
            return ResultType.ParamError;
        }

        const newControls = this._documentCore.getAllNewControls2();

        let result = ResultType.UnEdited;
        for (const newControl of newControls) {
            if (newControl.isEmrStruct(true) || newControl.isNewSection()) {
                const tmpResult = newControl.setShowBorder(bFlag);
                result = result === ResultType.Success ? result : tmpResult;
            }
        }

        this._documentCore.clearUndoList();
        // if ResultType.unedited, still better to refresh?
        // if (result === ResultType.Success) {
        this._documentCore.recalculate();
        this._host.handleRefresh();
        // }

        return result;
    }

    public resetStructsPlacehold(onlyBank: boolean, sString: string): number {
        if (typeof onlyBank !== 'boolean' || !sString) {
            return ResultType.ParamError;
        }

        const newControls = this._documentCore.getAllNewControls2();

        let result = ResultType.Success;
        for (const newControl of newControls) {
            if (newControl.isEmrStruct(true) || newControl.isNewSection()) {
                if (onlyBank === true) {
                    if (newControl.getPlaceHolderContent() !== '') {
                        continue;
                    }
                }

                result = result || newControl.setPlaceHolderContent(filterChars2(sString));
                // newControl.setPlaceHolderContent(filterChars(sString));
            }
        }

        this._documentCore.clearUndoList();
        // if ResultType.unedited, still better to recalc?
        // if (result === ResultType.Success) {
        this._documentCore.recalculate();
        this._host.handleRefresh();
        // }

        return result;
    }

    /**
     * 一次获取指定结构化元素的所有属性。
     * @param sName 结构化名称
     */
    public getStructPropByArray(sName: string): string {
        if (!sName) {
            return ResultType.StringEmpty;
        }
        const obj = this._documentCore.getNewControlPropsWithParam(sName,true,1);
        if (!obj) {
            return ResultType.StringEmpty;
        }

        const keys = Object.keys(obj);
        if (keys.length === 0) {
            return ResultType.StringEmpty;
        }
        keys.forEach((key) => {
            obj[key].type = EXTERNAL_STRUCT_TYPE[obj[key].type];
        });

        return JSON.stringify(obj);
    }

    public getCurrentStructName(): string {
        const newControl = this._documentCore.getCurrentNewControl();
        if (!newControl) {
            return ResultType.StringEmpty;
        }
        const sName = newControl.getNewControlName();
        if( sName.includes('signatureElement')) {
            const parent = newControl.getParent();
            if( parent)
                return parent.getNewControlName();
        }
        return sName;
    }

    public getNewControlText(sName: string): string {
        if (!sName) {
            return ResultType.StringEmpty;
        }
        return this._documentCore.getNewControlText(sName);
    }

    public getNewControlTextAI(sName: string): string {
        if (!sName) {
            return ResultType.StringEmpty;
        }
        return this._documentCore.getNewControlTextAI(sName);
    }

    public getNewControlBegin(sName: string): string {
        if (!sName) {
            return ResultType.StringEmpty;
        }
        return this._documentCore.getNewControlBegin(sName);
    }

    public getNewControlEnd(sName: string): string {
        if (!sName) {
            return ResultType.StringEmpty;
        }
        return this._documentCore.getNewControlEnd(sName);
    }

    /**
     * 获得指定名称结构化元素的最近的父级结构化元素名称
     * @param sName 结构化元素名称
     */
    public getFatherStructName(sName: string): string {
        if (!sName) {
            return ResultType.StringEmpty;
        }
        return this._documentCore.getFatherNewControlName(sName);
    }

    /**
     * 获得选中区域中结构化元素的名称列表
     * @return 选中区域中结构化元素的名称列表, 空则表示选中区域中无结构化元素. 多个结构化元素 采用逗号分隔，依此排列。节跟元素之间用分号隔开。
     */
    public getStructBySelectArea(): string {
        return this._documentCore.getNewControlBySelectArea();
    }

    public getStructsNameListFromSelectedArea(): string {
        return this._documentCore.getStructsNameListFromSelectedArea();
    }

    /**
     * 获取指定下拉框的当前Code值
     * @param sName 结构化名称
     * @param nType 中标预留值，暂时没用
     */
    public getCompoundBoxCurrentCode(sName: string, nType?: number): string {
        if (!sName) {
            return ResultType.StringEmpty;
        }

        const newControl = this._documentCore.getNewControlByName(sName);
        if (!newControl) {
            return ResultType.StringEmpty;
        }
        const items = newControl.getItemList();
        if (!items || items.length === 0) {
            return ResultType.StringEmpty;
        }
        const objs = items.filter((item) => item.bSelect);
        if (objs.length > 0) {
            return objs.map((item) => item.code)
            .join(',');
        }
        return ResultType.StringEmpty;
    }

    /**
     * 获取指定下拉框的当前value值
     * @param sName 结构化名称
     * @param nType 中标预留值，暂时没用
     */
    public getCompoundBoxCurrentValue(sName: string, nType?: number): string {
        if (!sName) {
            return ResultType.StringEmpty;
        }

        const newControl = this._documentCore.getNewControlByName(sName);
        if (!newControl) {
            return ResultType.StringEmpty;
        }
        const items = newControl.getItemList();
        if (!items || items.length === 0) {
            return ResultType.StringEmpty;
        }
        const objs = items.filter((item) => item.bSelect);
        if (objs.length > 0) {
            return objs.map((item) => item.value)
            .join(',');
        }
        return ResultType.StringEmpty;
    }

    /**
     * 获取指定名称控件的code数组
     * @param sName 结构化名称
     * @param nType 中标预留值，暂时没用
     */
    public getCompoundBoxCodeWithArray(sName: string, nType?: number): string[] {
        if (!sName) {
            return [];
        }

        const newControl = this._documentCore.getNewControlByName(sName);
        if (!newControl) {
            return [];
        }
        const items = newControl.getItemList();
        if (!items || items.length === 0) {
            return [];
        }

        return items.map((item) => item.code);
    }

    public changeStrcutType(json: string): number {
        let options: any;
        try {
            options = JSON.parse(json);
        } catch (error) {
            return ResultType.ParamError;
        }

        if (!options || !options.name || options.name.length === 0) {
            return ResultType.ParamError;
        }
        const names = options.name;
        for (let index = 0, len = names.length; index < len; index++) {
            if (!names[index] || typeof names[index] !== 'string') {
                return ResultType.ParamError;
            }
        }

        return this._documentCore.changeNewControlType(names);
    }

    /**
     * 获取指定名称控件的value数组
     * @param sName 结构化名称
     * @param nType 中标预留值，暂时没用
     */
    public getCompoundBoxValueWithArray(sName: string, nType?: number): string[] {
        if (!sName) {
            return [];
        }

        const newControl = this._documentCore.getNewControlByName(sName);
        if (!newControl) {
            return [];
        }
        const items = newControl.getItemList();
        if (!items || items.length === 0) {
            return [];
        }

        return items.map((item) => item.value);
    }

    /**
     * 获取指定名称控件的Code对应的Value值
     * @param sName 结构化名称
     * @param sCode 控件列表的Code值
     * @param nType 中标预留值，暂时没用
     */
    public getCompoundBoxValueByCode(sName: string, sCode: string, nType?: number): string {
        if (!sName || !sCode) {
            return ResultType.StringEmpty;
        }

        const newControl = this._documentCore.getNewControlByName(sName);
        if (!newControl) {
            return ResultType.StringEmpty;
        }
        const items = newControl.getItemList();
        if (!items || items.length === 0) {
            return ResultType.StringEmpty;
        }

        const obj = items.find((item) => item.code === sCode);

        if (obj) {
            return obj.value;
        }

        return ResultType.StringEmpty;
    }

    /**
     * 设置指定名称控件的Code和Value值；当已存在code值是，则修改value，当不存在时，则新增
     * @param sName 结构化名称
     * @param sCode 控件列表的Code值
     * @param sValue 控件列表的Value值
     * @param nType 中标预留值，暂时没用
     */
    public setCompoundBoxCodeAndValue(sName: string, sCode: string, sValue: string, nType?: number): number {
        if (!sName || !sCode) {
            return ResultType.ParamError;
        }
        sCode = filterChars2(sCode);

        const newControl = this._documentCore.getNewControlByName(sName);
        if (!newControl) {
            return ResultType.Failure;
        }
        const items = newControl.getItemList();
        if (!items) {
            return ResultType.Failure;
        }
        // 
        const obj = items.find((item) => item.code === sCode);
        if (obj) {
            obj.setCodeValue((sCode), sValue);
        } else {
            newControl.addItem(0, sCode, sValue);
        }
        // 
        return ResultType.Success;
    }

    /**
     * 设置指定多选下拉控件的分隔符
     * @param sName 结构化名称
     * @param sSeparator 分隔符的信息
     */
    public setMultiDropdownControlSeparator(sName: string, sSeparator: string): number {
        if (!sName) {
            return ResultType.ParamError;
        }

        const newControl = this._documentCore.getNewControlByName(sName) as NewComboBox;
        if (!newControl || !newControl.isMultiple()) {
            return ResultType.Failure;
        }
        // 
        this._documentCore.setNewControlControllerActive(newControl);
        const res = newControl.setSeparator(sSeparator);
        // 
        if (res === ResultType.Success) {
            newControl.updateNewControlText();
            this._host.handleRefresh();
            return ResultType.Success;
        }
        return ResultType.Failure;
    }

    /**
     * 设置指定多选下拉控件的选中项及未选中项之间的分隔符
     * @param sName 结构化名称
     * @param sSeparator 分隔符的信息
     */
    public setMultiDropdownControlGroupSeparator(sName: string, sSeparator: string): number {
        return;
        // if (!sName) {
        //     return ResultType.Failure;
        // }

        // const newControl = this._documentCore.getNewControlByName(sName) as NewComboBox;
        // if (!newControl || !newControl.getItemList) {
        //     return ResultType.Failure;
        // }
        // return;
    }

    /**
     * 设置下拉框控件的下拉窗体弹出模式( 单击弹出还是双击弹出)
     * @param bEnable True – 双击弹出 False – 单击弹出
     */
    public setCompoundBoxDropMode(bEnable: boolean): number {
        return;
    }

    /**
     * 设置Numbox的文本
     * @param sName 结构化名称
     * @param nText 数值框的文本
     */
    public setNumboxText(sName: string, nText: number): number {
        if (!sName || isNaN(nText)/* || nText > 65535 || nText < -65535*/) {
            return ResultType.ParamError;
        }

        const newControl = this._documentCore.getNewControlByName(sName);
        if (!newControl || newControl.isNumberBox() !== true) {
            return ResultType.Failure;
        }
        const control = this._documentCore.setNewControlControllerActive(newControl);
        const result = (newControl as NewControlNumer).setNumberBoxText(nText);
        if (result === ResultType.Success) {
            // if (control !== this._documentCore.getDocument()) {
            //     control.recalculate();
            // }
            this._documentCore.updateCursorXY();
            this._host.handleRefresh();
        }

        return result;
    }

    /**
     * 获取Numbox的文本
     * @param name 结构化名称
     */
    public getNumboxText(name: string): number {
        if (!name) {
            return ResultType.NumberNaN;
        }

        const newControl = this._documentCore.getNewControlByName(name);
        if (!newControl || newControl.isNumberBox() !== true) {
            return ResultType.NumberNaN;
        }

        const res = (newControl as NewControlNumer).getNumber();

        return res;
    }

    /**
     * 设置Numbox的取值上限
     * @param name 结构化名称
     * @param maxValue 最大值
     */
    public setNumboxMaxValue(name: string, maxValue: number): number {
        if (!name || isNaN(maxValue) || maxValue > 65535) {
            return ResultType.ParamError;
        }

        const newControl = this._documentCore.getNewControlByName(name);
        if (!newControl || newControl.isNumberBox() !== true) {
            return ResultType.Failure;
        }
        const res = (newControl as NewControlNumer).setMaxValue(maxValue);

        return res;
    }

    /**
     * 获取Numbox的取值上限
     * @param name 结构化名称
     */
    public getNumboxMaxValue(name: string): number {
        if (!name) {
            return ResultType.NumberNaN;
        }

        const newControl = this._documentCore.getNewControlByName(name);
        if (!newControl || newControl.isNumberBox() !== true) {
            return ResultType.NumberNaN;
        }

        const res = (newControl as NewControlNumer).getMaxValue();

        return res;
    }

    /**
     * 设置Numbox的取值下限
     * @param name 结构化名称
     * @param minValue 最小值
     */
    public setNumboxMinValue(name: string, minValue: number): number {
        if (!name || isNaN(minValue) || minValue < -65535) {
            return ResultType.ParamError;
        }

        const newControl = this._documentCore.getNewControlByName(name);
        if (!newControl || newControl.isNumberBox() !== true) {
            return ResultType.Failure;
        }
        const res = (newControl as NewControlNumer).setMinValue(minValue);

        return res;
    }

    /**
     * 获取Numbox的取值下限
     * @param name 结构化名称
     */
    public getNumboxMinValue(name: string): number {
        if (!name) {
            return ResultType.NumberNaN;
        }

        const newControl = this._documentCore.getNewControlByName(name);
        if (!newControl || newControl.isNumberBox() !== true) {
            return ResultType.NumberNaN;
        }

        const res = (newControl as NewControlNumer).getMinValue();

        return res;
    }

    /**
     * 设置Numbox的精度
     * @param name 结构化名称
     * @param precision 精度值
     */
    public setNumboxPrecision(name: string, precision: number): number {
        if (!name || isNaN(precision) || precision < 0 || precision > 8) {
            return ResultType.ParamError;
        }

        const newControl = this._documentCore.getNewControlByName(name);
        if (!newControl || newControl.isNumberBox() !== true) {
            return ResultType.Failure;
        }
        const res = (newControl as NewControlNumer).setPrecision(Math.ceil(precision));

        return res;
    }

    public setStructTitle(sName: string, sTitle: string): number {
        if (!sName || sTitle == null) {
            return ResultType.ParamError;
        }

        const newControl = this._documentCore.getNewControlByName(sName);
        if (!newControl) {
            return ResultType.Failure;
        }

        const type = newControl.getType();
        const types = [NewControlType.TextBox, NewControlType.Section, NewControlType.Combox,
            NewControlType.MultiCombox, NewControlType.ListBox, NewControlType.MultiListBox];
        if (!types.includes(type)) {
            return ResultType.Failure;
        }

        sTitle = filterChars(skipEscapeString(sTitle));
        const res = newControl.setTitle(sTitle);
        if (res === ResultType.Success) {
            const control = this._documentCore.setNewControlControllerActive(newControl);
            if (control) {
                if (control !== this._documentCore.getDocument()) {
                    control.recalculate();
                }
            }
            this._host.handleRefresh();
        }
        return res;
    }

    public getStructTitle(sName: string): string {
        if (!sName) {
            return ResultType.StringEmpty;
        }
        const newControl = this._documentCore.getNewControlByName(sName);
        if (!newControl) {
            return ResultType.StringEmpty;
        }
        return newControl.getTitle() || ResultType.StringEmpty;
    }

    /**
     * 获取Numbox的精度
     * @param name 结构化名称
     */
    public getNumboxPrecision(name: string): number {
        if (!name) {
            return ResultType.NumberNaN;
        }

        const newControl = this._documentCore.getNewControlByName(name);
        if (!newControl || newControl.isNumberBox() !== true) {
            return ResultType.NumberNaN;
        }

        return (newControl as NewControlNumer).getPrecision();
    }

    /**
     * 设置数字框输入错误后的警告信息，比如输入英文字母
     * @param strName 结构化名称
     * @param strInfo 警告信息
     */
    public setNumboxErrorInputInfo(strName: string, strInfo: string): number {
        if (!strName) {
            return ResultType.NumberNaN;
        }

        const newControl = this._documentCore.getNewControlByName(strName);
        if (!newControl || newControl.isNumberBox() !== true) {
            return ResultType.NumberNaN;
        }

        return (newControl as NewControlNumer).setErrorInputInfo(strInfo);
    }

    /**
     * 设置数字框输入不在最小值到最大值范围的警告信息
     * @param strName 结构化名称
     * @param strInfo 警告信息
     */
    public setNumboxOutRangeInfo(strName: string, strInfo: string): number {
        if (!strName) {
            return ResultType.NumberNaN;
        }

        const newControl = this._documentCore.getNewControlByName(strName);
        if (!newControl || newControl.isNumberBox() !== true) {
            return ResultType.NumberNaN;
        }

        return (newControl as NewControlNumer).setErrorOutRangeInfo(strInfo);
    }

    public getStructTypeByName(name: string): number {
        if (!name) {
            return ResultType.NumberNaN;
        }

        const newControl = this._documentCore.getNewControlByName(name);
        if (!newControl) {
            const region = this._documentCore.getRegionByName(name);
            if (region) {
                return EXTERNAL_STRUCT_TYPE[NewControlType.Region];
            }

            const button = this._documentCore.getParaButtonByName(name);
            if (button) {
                return EXTERNAL_STRUCT_TYPE[button.getStructType()];
            }

            return ResultType.NumberNaN;
        }

        let type = newControl.getType();
        type = EXTERNAL_STRUCT_TYPE[type];
        // switch (type) {
        //     case NewControlType.Combox:
        //         type = 1;
        //         break;
        //     case NewControlType.ListBox:
        //         type = 2;
        //         break;
        //     case NewControlType.TextBox:
        //         type = 3;
        //         break;
        //     case NewControlType.CheckBox:
        //         type = 4;
        //     case NewControlType.NumberBox:
        //         type = 5;
        //         break;
        //     case NewControlType.MultiListBox:
        //         type = 6;
        //         break;
        //     case NewControlType.MultiCombox:
        //         type = 7;
        //         break;
        //     case NewControlType.DateTimeBox:
        //         type = 8;
        //         break;
        //     case NewControlType.RadioButton:
        //         type = 9;
        //         break;
        //     case NewControlType.MultiCheckBox:
        //         type = 10;
        //         break;
        //     case NewControlType.Section:
        //         type = 13;
        //         break;
        //     case NewControlType.TextBox:
        //         type = 3;
        //     default:
        //         type = NaN;
        // }

        return type;
    }

    public insertStructAtCurrentCursor(sName: string, sText: string, nType: number): number {
        if (!sName || typeof sText !== 'string') {
            return ResultType.ParamError;
        }

        if (nType === EXTERNAL_STRUCT_TYPE[NewControlType.SignatureBox]) {
            return ResultType.Failure;
        }

        const type: number = EXTERNAL_OUTER_STRUCT_TYPE[nType];

        // 特殊处理 Region 类型
        if (type === NewControlType.Region) {
            const regionProperty = {
                newControlName: sName,
                newControlTitle: sText || '',
            };
            const result = this._documentCore.addRegion(regionProperty);
            if (result === ResultType.Success) {
                this._host.handleRefresh();
            }
            return result;
        }

        // switch (nType) {
        //     case 1:
        //         type = NewControlType.Combox;
        //         break;
        //     case 2:
        //         type = NewControlType.ListBox;
        //         break;
        //     case 3:
        //         type = NewControlType.TextBox;
        //         break;
        //     case 4:
        //         type = NewControlType.CheckBox;
        //         break;
        //     case 5:
        //         type = NewControlType.NumberBox;
        //         break;
        //     case 6:
        //         type = NewControlType.MultiListBox;
        //         break;
        //     case 7:
        //         type = NewControlType.MultiCombox;
        //         break;
        //     case 8:
        //         type = NewControlType.DateTimeBox;
        //         break;
        //     case 9:
        //         type = NewControlType.RadioButton;
        //         break;
        //     case 10:
        //         type = NewControlType.MultiCheckBox;
        //         break;
        //     case 13:
        //         type = NewControlType.Section;
        //         break;
        //     default:
        //         type = NewControlType.TextBox;
        // }
        if (sText) {
            if (type === NewControlType.NumberBox && !/\d+(\.\d+)?/.test(sText)) {
                return ResultType.ParamError;
            }
            if (type === NewControlType.DateTimeBox && !isValiDate(sText)) {
                return ResultType.ParamError;
            }
        }
        const types = [NewControlType.SignatureBox, NewControlType.Section];

        const newControlProperty = {
            newControlName: sName,
            newControlInfo: null,
            newControlPlaceHolder: null,
            newControlType: type,
            isNewControlHidden: null,
            isNewControlCanntDelete: null,
            isNewControlCanntEdit: null,
            isNewControlMustInput: null,
            isNewControlShowBorder: null,
            isNewControlReverseEdit: null,
            isNewControlHiddenBackground: null,
            newControlDisplayType: null,
            newControlFixedLength: 0,
            newControlMaxLength: 0,
            tabJump: !types.includes(type),
            hideHasTitle: null,
            showType: undefined,
        };
        if (type === NewControlType.MultiRadio) {
            newControlProperty.showType = type;
        }
        
        const result = this._documentCore.addNewControl(newControlProperty, filterChars2(sText));
        
        if (result === ResultType.Success) {
            this._host.handleRefresh();
        }

        return result;
    }

    public deleteNewControl(sName: string): number {
        
        const result = this._documentCore.deleteNewControl(sName);
        
        if (result === ResultType.Success) {
            this._host.handleRefresh();
        }

        return result;
    }

    public cursorJumpOutOfOneStruct(sName: string, nMark: number): number {
        if (!sName) {
            return ResultType.ParamError;
        }

        if (nMark !== 0 && nMark !== 1 && nMark !== 2 && nMark !== 3) {
            return ResultType.ParamError;
        }

        const result = this._documentCore.cursorJumpOutOfOneStruct(sName, nMark);
        if (result === ResultType.Success) {
            this._host.handleRefresh();
        }

        return result;
    }

    public setNewControlName(sName: string, sNewName: string): number {
        if (!sName || !sNewName) {
            return ResultType.ParamError;
        }

        if (sName === sNewName) {
            return ResultType.UnEdited;
        }

        if (this._documentCore.checkNewControlName(sNewName) === false) {
            return ResultType.Failure;
        }
        
        const newControl = this._documentCore.getNewControlByName(sName);
        if (!newControl) {
            const button = this._documentCore.getParaButtonByName(sName);
            if (button) {
                const res = this._documentCore.setParaButtonProps(button.name, {name: sNewName});
                if (ResultType.Refresh === res) {
                    this._host.handleRefresh();
                }
                 if ([ResultType.Refresh, ResultType.Success].includes(res)) {
                    return ResultType.Success;
                }
            }
            return ResultType.Failure;
        }
        
        return newControl.setNewControlName(sNewName);
    }

    public filterStructsByProp(sJson: string, sRev1?: string): string {
        if (!sJson) {
            return ResultType.StringEmpty;
        }

        let oProp: object;
        try {
            oProp = JSON.parse(sJson);
        } catch (e) {
            return ResultType.StringEmpty;
        }

        return this._filterStrcut(oProp, sRev1);
    }

    public setNewControlProp(sName: string, sPropName: string, val: any): number {
        if (!sName || !sPropName) {
            return ResultType.ParamError;
        }

        // let type: DataType;

        // if (names.includes(sPropName) && typeof val !== 'string') {
        //     return ResultType.ParamError;
        // } else if (sPropName === NewControlPropName.DisplayType) {
        //     if (typeof val !== 'number') {
        //         return ResultType.ParamError;
        //     }
        // } else
        if (!this._propNames.includes(sPropName)) {
            const type = this._getDataType(val);
            if (type === undefined) {
                return ResultType.ParamError;
            }
            val = {[sPropName]: val};
            sPropName = NewControlPropName.CustomProperty;
        } else if (sPropName === NewControlPropName.Unit && !isValidUnit(val)) {
            return ResultType.ParamError;
        }

        // 经过上面的判断，不再次赋值，导致无法正常通过语法检测
        const value: any = val;
        // const newControl: ParaNewControl = this._documentCore.getNewControlByName(sName);

        // if (!newControl) {
        //     return ResultType.Failure;
        // }
        const props: any = {
            bClearItems: false,
            _bInterface: true,

        };
        if (this.setPropValue(sPropName, value, props) === false) {
            return ResultType.ParamError;
        }
        // let bRefresh = false;
        const result: number = this._documentCore.setNewControlProperty1(props, sName);
        // switch (sPropName) {
        //     case NewControlPropName.ReverseEdit:
        //         result = newControl.setReverseEdit(value);
        //         break;
        //     case NewControlPropName.DeleteProtect:
        //         result = newControl.setDeleteProtect(value);
        //         break;
        //     case NewControlPropName.EditProtect:
        //         result = newControl.setEditProtect(value);
        //         break;
        //     case NewControlPropName.CopyProtect:
        //         result = newControl.setCopyProtect(value);
        //         break;
        //     case NewControlPropName.IsKeyJump:
        //         result = newControl.setTabJump(value);
        //         break;
        //     case NewControlPropName.Placeholder: {
        //         result = newControl.setPlaceHolderContent(filterChars(value));
        //         if (result === ResultType.Success && newControl.isPlaceHolderContent()) {
        //             this._documentCore.setNewControlControllerActive(newControl);
        //             bRefresh = true;
        //             newControl.recalculate();
        //         }
        //         break;
        //     }
        //     case NewControlPropName.SerialNumber: {
        //         result = newControl.setSerialNumber(value);
        //         if (result === ResultType.Success) {
        //             this._documentCore.setNewControlControllerActive(newControl);
        //             bRefresh = true;
        //             newControl.recalculate();
        //         }
        //         break;
        //     }
        //     case NewControlPropName.IsMustInput:
        //         result = newControl.setMustInput(value);
        //         if (result === ResultType.Success && newControl.isPlaceHolderContent()) {
        //             bRefresh = true;
        //             this._documentCore.setNewControlControllerActive(newControl);
        //             newControl.recalculate();
        //         }
        //         break;
        //     case NewControlPropName.ShowBorder:
        //         result = newControl.setShowBorder(value);
        //         break;
        //     case NewControlPropName.Helptip:
        //         result = newControl.setTipsContent(filterChars(value));
        //         break;
        //     case NewControlPropName.HiddenBackground:
        //         result = newControl.setHiddenBackground(value);
        //         break;
        //     case NewControlPropName.Title:
        //         result = newControl.setTitle(value);
        //         break;
        //     case NewControlPropName.Hidden:
        //         result = newControl.setHidden(value);
        //         if (result === ResultType.Success) {
        //             bRefresh = true;
        //             this._documentCore.setNewControlControllerActive(newControl);
        //             newControl.recalculate();
        //         }
        //         break;
        //     case NewControlPropName.DisplayType: {
        //         result = newControl.setViewSecret(value + 1);
        //         break;
        //     }
        //     default:
        //         result = newControl.addCustomPropItem(sPropName, value + '', type, value);
        // }
        const date1 = new Date();
        if (result === ResultType.Success) {
            // if (bRefresh) {
            //     const control = this._documentCore.setNewControlControllerActive(newControl);
            //     if (control !== this._documentCore.getDocument()) {
            //         control.recalculate();
            //     }
            // }
            this._documentCore.setNewControlControllerActive(this._documentCore.getNewControlByName(sName));
            this._host.handleRefresh();
        } else if (result === ResultType.UnRefresh) {
            return ResultType.Success;
        }

        return result;
    }

    /**
     * 设置指定名称控件的Code和Value值（传数组，一次设多条code，value）
     * @param sName 结构化名称
     * @param value 设置的值 json
     * @param nType 结构化类型
     */
    public setCompoundBoxCodeAndValueByArray(sName: string, sValue: string, nType?: number): number {
        if (!sName || !sValue) {
            return ResultType.ParamError;
        }

        let value: {code: any[], value: any[]};
        try {
            value = JSON.parse(sValue);
        } catch (error) {
            return ResultType.ParamError;
        }

        if (!value || !value.code || value.code.length === 0) {
            return ResultType.ParamError;
        }

        const newControl = this._documentCore.getNewControlByName(sName);
        if (!newControl || !newControl.isAmongComboxOrListStruct() ) {
            return ResultType.Failure;
        }
        const codes = value.code.map((code) => {
            return filterChars2(code);
        });
        const res = newControl.addItems(codes, value.value);

        return res === true ? ResultType.Success : ResultType.Failure;
    }

    /**
     * 根据指定下拉框的value值设置当前显示的code值
     * @param sName 结构化名称
     * @param value 设置的值
     * @param nType 结构化类型
     */
    public setCompoundBoxCurrentCodeByValue(sName: string, sValue: string, nType?: number): number {
        if (!sName || !sValue) {
            return ResultType.ParamError;
        }

        sValue = sValue.trim();
        let values: string[];
        if (/^\[[\S\s]*\]$/.test(sValue)) {
            try {
                values = JSON.parse(sValue);
            } catch (error) {
                return ResultType.ParamError;
            }
        } else {
            values = [sValue];
        }

        if (!values || !values.length) {
            return ResultType.ParamError;
        }

        const newControl = this._documentCore.getNewControlByName(sName);
        if (!newControl || !newControl.isAmongComboxOrListStruct()) {
            return ResultType.Failure;
        }

        if (values.length > 1 && !(newControl.isNewMultiCombox() || newControl.isNewMultiList())) {
            return ResultType.ParamError;
        }

        const list = newControl.getItemList();
        if (!list.length) {
            return ResultType.Failure;
        }

        const indexs: number[] = [];
        values.forEach((value) => {
            const index = list.findIndex((item) => item.value === value);
            if (index !== -1) {
                indexs.push(index);
            }
        });
        if (!indexs.length) {
            return ResultType.Failure;
        }
        const controller = this._documentCore.setNewControlControllerActive(newControl);
        const res = newControl.setNewControlListItems(indexs);

        if (res === ResultType.Success) {
            // if (this._documentCore.getDocument() !== controller) {
            //     // controller.recalculate();
            // }
            this._documentCore.updateCursorXY();
            this._host.handleRefresh();
        }

        return res;
    }

    /**
     * 删除指定名称控件的所有条目
     * @param sName 结构化名称
     * @param nType 结构化类型
     */
    public deleteAllCompoundBoxCodeAndValue(sName: string, nType?: number): number {
        if (!sName) {
            return ResultType.ParamError;
        }
        const newControl = this._documentCore.getNewControlByName(sName);
        if (!newControl || !newControl.isAmongComboxOrListStruct() ) {
            return ResultType.Failure;
        }

        newControl.removeAllItems();
        return ResultType.Success;
    }

    public getErrorCtrlNameList():string{
        const newControls = this._documentCore.getAllNewControls2();
        if (newControls.length === 0) {
            return ResultType.StringEmpty;
        }

        const filterNewControls = [];
        newControls.forEach((newControl) => {
            if (!newControl) {
                return; // 如果 newControl 为空，跳过当前循环
            }
            let category: number | undefined = undefined;
        
            if ('matchRegExp2' in newControl) {
                const text = (newControl as any).matchRegExp2();
                if (text) {
                    category = 1;//正则校准失败
                    filterNewControls.push({ name: newControl.getNewControlName(), type: category });
                }
            }
        
            if (newControl.isNumberBox()) {
                const result: any = (newControl as any).getValidNumberType();
                //注意 result返回的是number
                if (String(result) !== NewControlErrorInfo.Success) {
                    category = 2; //数值框内容非法
                    filterNewControls.push({ name: newControl.getNewControlName(), type: category });
                }
            } else if (newControl.isNewTextBox()) {
                if ((newControl as any).isMoreThanMaxLength1()) {
                    category = 3; //超过最大字数
                    filterNewControls.push({ name: newControl.getNewControlName(), type: category });
                }
            } else if (newControl.isNewDateBox()) {
                if (newControl instanceof NewControlDate) {
                    const res = newControl.isValidDate();
                    if (res !== NewControlErrorInfo.Success) {
                        category = 4; //日期非法
                        filterNewControls.push({ name: newControl.getNewControlName(), type: category });
                    }
                }
            }
        });
        
       return JSON.stringify(filterNewControls);
    }

    public getIncompletedCtrlNameList(): string {
        const newControls = this._documentCore.getAllNewControls2();
        if (newControls.length === 0) {
            return ResultType.StringEmpty;
        }

        const filterNewControls = [];
        newControls.forEach((newControl) => {
            if (!newControl.isMustInput()) {
                return;
            }

            if (newControl.isMultiAndRadio()) {
                if ((newControl as any).getSelected() == null) {
                    filterNewControls.push(newControl.getNewControlName());
                }
                return;
            }

            if (newControl.isPlaceHolderContent()) {
                filterNewControls.push(newControl.getNewControlName());
            }
        });

        // const filterNewControls = newControls.filter((control) => control.isMustInput()
        //     && control.isPlaceHolderContent());

        if (filterNewControls.length === 0) {
            return ResultType.StringEmpty;
        }

        return filterNewControls.join(',');
    }

    public getNewControlProp(sName: string, sPropName: string): string | boolean {
        if (!sName) {
            return ResultType.StringEmpty;
        }

        // if (!this._propNames.includes(sPropName)) {
        //     return ResultType.StringEmpty;
        // }


        let newControl: any = this._documentCore.getNewControlByName(sName);

        if (!newControl) {
            if (!(sPropName === NewControlPropName.PrintSelected && (newControl = this._documentCore.getParaButtonByName(sName)))) {
                return ResultType.StringEmpty;
            }

        }

        return this._getValueByPropName(sPropName, newControl);
    }

    public selectOneStruct(sName: string): number {
        if (!sName) {
            return ResultType.ParamError;
        }

        const result = this._documentCore.selectNewControl(sName);
        if (result === ResultType.Success) {
            this._host.setSelections();
            this._host.handleRefresh();
        }

        return result;
    }

    public setTextBoxMaxLen(sName: string, nMaxLen: number): number {
        if (!sName) {
            return ResultType.ParamError;
        }

        if (isNaN(nMaxLen) || nMaxLen < 0) {
            return ResultType.ParamError;
        }

        const newControl = this._documentCore.getNewControlByName(sName) as any;
        if (!newControl || !newControl.isNewTextBox()) {
            return ResultType.Failure;
        }
        
        newControl.setMaxLength(nMaxLen);
        

        return ResultType.Success;
    }

    /**
     * 设置checkBox的勾选状态
     * @param name 结构化名称
     * @param bChecked 选中状态
     */
    public setCheckboxStatus(name: string, bChecked: boolean): number {
        if (!name || typeof bChecked !== 'boolean') {
            return ResultType.ParamError;
        }

        const res = this._documentCore.setCheckboxStatus(name, bChecked);
        if (res === ResultType.Success) {
            const newControl = this._documentCore.getNewControlByName(name);
            const currentNewControl = this._documentCore.getCurrentNewControl();
            if (currentNewControl !== newControl) {
                newControl.triggerCascade();
            }
            this._host.handleRefresh();
        }
        return res;
    }

    public setCheckboxCaption(name: string, sCaption: string): number {
        if (!name || sCaption == null || typeof sCaption !== 'string') {
            return ResultType.ParamError;
        }

        const res = this._documentCore.setCheckboxCaption(name, sCaption);
        if (res === ResultType.Success) {
            this._host.handleRefresh();
        }

        return res;
    }

    /**
     * 读取checkBox的文本
     * @param name 获取文本
     */
    public getCheckboxCaption(name: string): string {
        if (!name) {
            return ResultType.StringEmpty;
        }

        return this._documentCore.getCheckboxCaption(name);
    }

    /**
     * 设置checkBox的 code 文本
     * @param name checkBox名称
     * @param sCode 对应的属性值
     */
     public setCheckboxCode(name: string, sCode: string): number {
        if (!name || typeof name !== 'string' || typeof sCode !== 'string') {
            return ResultType.ParamError;
        }
        return this._documentCore
                   .setCheckBoxCode(name, sCode);
    }

    /**
     * 获取checkBox的 code 文本
     * @param name checkBox名称
     */
    public getCheckboxCode(name: string): string {
        if (!name || typeof name !== 'string') {
            return ResultType.StringEmpty;
        }
        return this._documentCore
                   .getCheckBoxCode(name);
    }

    /**
     * 返回所有设置过组的checkbox的组名
     */
    public getAllGroupCheckboxName(): string {
        return this._documentCore.getAllGroupCheckboxName();
    }

    /**
     * 返回指定组的checkbox的check状态
     * @param sGroupName 组名
     */
    public getGroupCheckboxStatus(sGroupName: string): number {
        if (!sGroupName || typeof sGroupName !== 'string' ) {
            return ResultType.Failure2;
        }

        return this._documentCore.getGroupCheckboxStatus(sGroupName);
    }

    public getCheckboxGroupName(sCheckBox: string): string {
        if (!sCheckBox || typeof sCheckBox !== 'string' ) {
            return ResultType.StringEmpty;
        }

        return this._documentCore.getCheckboxGroupName(sCheckBox);
    }

    /**
     * 对指定组的checkbox的最后一个checkbox显示特定的信息
     * @param sGroupName 组名
     * @param sInfo 特定的信息
     */
    public showInfoToGroupCheckbox(sGroupName: string, sInfo: string): number {
        if (!sGroupName || typeof sGroupName !== 'string' || !sInfo || typeof sInfo !== 'string') {
            return ResultType.Failure2;
        }

        return this._documentCore.showInfoToGroupCheckbox(sGroupName, sInfo);
    }

    /**
     * 对指定的checkbox，显示特定的信息
     * @param sCheckBoxName checkbox名
     * @param sInfo 特定的信息
     */
    public showInfoToCheckBoxs(sCheckBoxName: string, sInfo: string): number {
        if (!sCheckBoxName || typeof sCheckBoxName !== 'string' || !sInfo || typeof sInfo !== 'string') {
            return ResultType.Failure2;
        }
        return this._documentCore.showInfoToCheckBoxs(sCheckBoxName, sInfo);
    }

    public setRadioButtonCodeAndValueByArray(sName: string, sJson: string, bClear: boolean = true): number {
        if (!sName || !sJson || typeof sJson !== 'string') {
            return ResultType.ParamError;
        }
        let arrs: {code: string[], value: string[]};
        try {
            arrs = JSON.parse(sJson);
        } catch  {
            return ResultType.ParamError;
        }

        const actCodes = arrs.code;
        if (!actCodes || !actCodes.length) {
            return ResultType.ParamError;
        }
        const actValues = arrs.value;
        if (!actValues || !actValues.length) {
            return ResultType.ParamError;
        }
        const items: CodeValueItem[] = [];
        const codes = {};
        actCodes.forEach((arr, index) => {
            if (typeof arr !== 'string' || !arr) {
                return;
            }
            if (codes[arr]) {
                return;
            }
            const actValue = actValues[index];
            if (typeof actValue !== 'string') {
                return;
            }
            codes[arr] = true;
            items.push(new CodeValueItem(arr, actValue));
        });
        if (items.length === 0) {
            return ResultType.ParamError;
        }
        const res = this._documentCore.setRadioCodeAndValueByArray(sName, items, bClear);
        if (res === ResultType.Success) {
            this._host.handleRefresh();
        }
        return res;
    }

    public getRadioButtonSelectItemValue(sName: string): string {
        if (!sName) {
            return ResultType.StringEmpty;
        }

        return this._documentCore.getRadioSelectedValue(sName);
    }

     /**
     * 设置指定按钮元素目标项的文本颜色
     * @param sName 按钮元素名称
     * @param index 选择项位置索引（0-）
     * @param color 颜色值
     */
    public setRadioButtonItemTextColor(sName: string, index: string, color: string): number {
        if (!sName || !index || typeof index !== 'string') {
            return ResultType.ParamError;
        }

        const res = this._documentCore.setRadioButtonItemTextColor(sName, index, color);
        if (res === ResultType.Success) {
            this._host.handleRefresh();
        }

        return res;
    }

     /**
      * 根据索引选中指定名称RadioButton的指定项
      * @param sName 按钮元素名称
      * @param index 选择索引：选择多项时，用逗号分隔
      */
    public selectRadioButtonItemByIndex(sName: string, index: string = ''): number {
        if (!sName || typeof index !== 'string') {
            return ResultType.ParamError;
        }
        const res = this._documentCore.setRadioButtonItemByIndex(sName, index);
        if (res === ResultType.Success) {
            this._host.handleRefresh();
        }
        return res;
    }
 
     /**
      * 获取指定名称RadioButton当前选中项的索引值
      * @param sName 按钮元素名称
      */
    public getRadioButtonSelectedIndexes(sName: string): string {
        if (!sName) {
            return ResultType.StringEmpty;
        }
        return this._documentCore.getRadioButtonSelectedIndexes(sName);
    }

    public selectRadioButtonItemByValue(sName: string, sValue: string): number {
        if (!sName || typeof sValue !== 'string') {
            return ResultType.ParamError;
        }

        const res = this._documentCore.setRadioSelectedValue(sName, sValue);
        if (res === ResultType.Success) {
            this._host.handleRefresh();
        }

        return res;
    }

    public deleteAllRadioButtonItem(sName: string): number {
        if (!sName) {
            return ResultType.ParamError;
        }

        const res = this._documentCore.deleteAllRadioItem(sName);
        if (res === ResultType.Success) {
            this._host.handleRefresh();
        }
        return res;
    }

    /**
     * 获取指定名称控件的value数组
     * @param sName 结构化名称
     */
    public getRadioButtonValueWithArray(sName: string): string {
        if (!sName) {
            return ResultType.StringEmpty;
        }

        return this._documentCore.getRadioButtonItemWithArray(sName, 'value');
    }

    /**
     * 选中指定名称RadioButton的指定Code项
     * @param sName 结构化名称
     */
    public getRadioButtonCodeWithArray(sName: string): string {
        if (!sName) {
            return ResultType.StringEmpty;
        }

        return this._documentCore.getRadioButtonItemWithArray(sName, 'code');
    }

    /**
     * 选中指定名称RadioButton的指定Code项
     * @param sName 结构化名称
     * @param sCode code值
     */
    public selectRadioButtonItemByCode(sName: string, sCode: string): number {
        if (!sName || typeof sCode !== 'string') {
            return ResultType.ParamError;
        }

        const res = this._documentCore.setRadioSelectedByCode(sName, sCode);
        if (res === ResultType.Success) {
            this._host.handleRefresh();
        }
        return res;
    }

    /**
     * 将指定名称RadioButton 选项清空
     * @param sName 结构化名称
     */
     public clearRadioButtonCheckItem(sName: string): number {
        if (!sName) {
            return ResultType.ParamError;
        }
        const res = this._documentCore.clearRadioButtonCheckItem(sName);
        if (res === ResultType.Success) {
            this._host.handleRefresh();
        }
        return res;
    }

    public getTextBoxMaxLen(sName: string): number {
        if (!sName) {
            return -1;
        }

        const newControl = this._documentCore.getNewControlByName(sName) as any;
        if (!newControl.isNewTextBox()) {
            return -1;
        }

        return newControl.getMaxLength();
    }

    public selectOneStructContent(sName: string): number {
        if (!sName) {
            return ResultType.ParamError;
        }

        const result = this._documentCore.selectNewControlContent(sName);
        if (result === ResultType.Success) {
            this._host.setSelections();
            this._host.handleRefresh();
        }

        return result;
    }

    public saveStructContentToString(sName: string): string {
        if (!sName) {
            return ResultType.StringEmpty;
        }

        const elements = this._documentCore.getElementsByNewControl(sName);
        if (elements.length === 0) {
            return ResultType.StringEmpty;
        }

        const apollo = new Apollo(elements, this._documentCore.getDocument());

        return apollo.getApollo();
    }

    public updateAllConnections(): number {
        const res = this._documentCore.updateAllConnections();
        if (res === ResultType.Success) {
            this._host.handleRefresh();
        }

        return res;
    }

    public setStructsTextByArray(sJson: any): number {
        if ( null == sJson || '' === sJson ) {
            return ResultType.ParamError;
        }

        const text = JSON.parse(sJson);
        const newControlNames = Object.keys(text);
        const newControlContents: Map<string, string[]> = new Map();

        for (let index = 0, length = newControlNames.length; index < length; index++) {
            const contents = text[newControlNames[index]];
            newControlContents.set(newControlNames[index], Object.values(contents));
        }

        if ( 0 === newControlContents.size ) {
            return ResultType.ParamError;
        }
        
        // const newControl = this._documentCore.getNewControlByName(newControlNames[0]);
        // if (!newControl) {
        //     return ResultType.Failure;
        // }
        // this._documentCore.setNewControlControllerActive(newControl);
        const result = this._documentCore.setStructsTextByArray(newControlContents);
        
        if ( ResultType.Success === result ) {
            this._host.handleRefresh();
        }

        return result;
    }

    public setNumboxUnit(name: string, unit: string): number {
        if (!name || typeof unit !== 'string' || !isValidUnit(unit)) {
            return ResultType.ParamError;
        }
        const result: number = this._documentCore.setNewControlProperty1({unit, newControlName: undefined}, name);
        if ( result === ResultType.Success) {
            this._host.handleRefresh();
        }
        return result;
    }

    /**
     * 功能描述：获取包含非法值得Numbox的名称列表
     * @param bFlag 1 只包含强制校验得数值框 2 所有的数值框
     */
    public getIllegalValueNumbox(bFlag: number): string {
        if (![1, 2].includes(bFlag)) {
            return ResultType.StringEmpty;
        }
        return this._documentCore.getUnValidNumberBox(bFlag);
    }

    public getNumboxUnit(name: string): string {
        if (!name) {
            return ResultType.StringEmpty;
        }
        const newControl = this._documentCore.getNewControlByName(name);
        if (!newControl) {
            return ResultType.StringEmpty;
        }

        return newControl.getUnit() || '';
    }

    /**
     * 获取无嵌套信息的结构化信息
     * @returns
     */
    public getStructsXmlInfoByParament(): string {
        const obj = this._documentCore.getNewControlsProps();
        const keys = Object.keys(obj);
        if (keys.length === 0) {
            return ResultType.StringEmpty;
        }
        keys.forEach((key) => {
            obj[key].type = EXTERNAL_STRUCT_TYPE[obj[key].type];
        });

        return JSON.stringify(obj);
    }

    public getStructsXmlInfoByFile(content: Blob, sJson?: string): Promise<string> {
    return new Promise((resolve, reject) => {
        // let result = '';
        const newZip = new JSZip();
        const reader = new Reader(this._documentCore.getDocument());
        const fileReader = new FileReader();
        fileReader.readAsArrayBuffer(content);
        fileReader.onloadend = () => {
            const rawBuffer = fileReader.result as ArrayBuffer;
            // tslint:disable-next-line: max-line-length
            const {headerBuffer, contentBuffer}: IDocumentBuffer = reader.separateRawBuffer(new Uint8Array(rawBuffer));
            // console.log(contentBuffer);
            const {versionNum} = reader.getHeaderBufferItems(headerBuffer);

            const newBlob = new Blob([contentBuffer], {type: 'application/apollo-zip'});
            // tslint:disable-next-line: newline-per-chained-call
            newZip.loadAsync(newBlob).then((zip) => {
                // const documentZippedFile = newZip.file('Document.xml');
                let documentZippedFile = null;
                let headerZippedFile = null;
                let footerZippedFile = null;

                newZip.forEach((relativePath: string, file: any): void => {
                    // console.log(relativePath);
                    // console.log(file);
                    switch (relativePath) {
                        case 'Document.xml': {
                            documentZippedFile = file;
                            break;
                        }
                        case 'Header1.xml': {
                            headerZippedFile = file;
                            break;
                        }
                        case 'Footer.xml': {
                            footerZippedFile = file;
                            break;
                        }
                        default: {
                            // // tslint:disable-next-line: no-console
                            // console.warn('unexpected xml file detected.');
                            break;
                        }
                    }
                });
                // if (documentZippedFile != null) {
                //     // tslint:disable-next-line: newline-per-chained-call
                //     documentZippedFile.async('blob').then((unZippedDocumentFile: any) => {
                //         fileReader.readAsText(unZippedDocumentFile);
                //         fileReader.onloadend = () => {
                //             const documentFile: string = fileReader.result as string;
                    //             if (documentFile != null) {
                    //                 resolve(analyzeFileForXmlInfo(documentFile, versionNum));
                    //             }
                    //         };
                    //     });
                    // }
                    const documentPromise = documentZippedFile.async('string');
                    const headerPromise = headerZippedFile != null ? headerZippedFile.async('string') : null;
                    const footerPromise = footerZippedFile != null ? footerZippedFile.async('string') : null;
                    const fPromises = [documentPromise, headerPromise, footerPromise];

                    // tslint:disable-next-line: newline-per-chained-call
                    Promise.all(fPromises).then((values) => {
                        // Returned values will be in order of the Promises passed, regardless of completion order.
                        let documentStr = '';
                        let headerStr = '';
                        let footerStr = '';
                        // console.log(values);
                        const [documentFile, headerFile, footerFile] = values;
                        // console.log(documentFile)
                        // console.log(headerFile)
                        // console.log(footerFile)
                        if (documentFile != null) {
                            documentStr = analyzeFileForXmlInfo(documentFile, versionNum, DocumentSectionType.Document,
                                sJson);
                        }
                        if (headerFile != null) {
                            headerStr = analyzeFileForXmlInfo(headerFile, versionNum, DocumentSectionType.Header,
                                sJson);
                        }
                        if (footerFile != null) {
                            footerStr = analyzeFileForXmlInfo(footerFile, versionNum, DocumentSectionType.Footer,
                                sJson);
                        }

                        const documentObj = JSON.parse(documentStr);
                        const headerObj = JSON.parse(headerStr);
                        const footerObj = JSON.parse(footerStr);

                        // construct tableInfo
                        const tableObj: any = {};
                        if (documentObj != null && documentObj.table != null) {
                            tableObj.table = documentObj.table;
                        }
                        if (headerObj != null && headerObj.table != null) {
                            for (const item of headerObj.table) {
                                tableObj.table.push(item);
                            }
                        }
                        if (footerObj != null && footerObj.table != null) {
                            for (const item of footerObj.table) {
                                tableObj.table.push(item);
                            }
                        }

                        const resultObj = {};
                        Object.assign(resultObj, documentObj, headerObj, footerObj, tableObj);
                        resolve(JSON.stringify(resultObj));

                    // tslint:disable-next-line: newline-per-chained-call
                    }).catch((error) => {
                        // tslint:disable-next-line: no-console
                        console.log(error);
                        // return;
                    });
                });
            };
            // return result;
        });
    }

    public setStructsPropByArray(sJson: any): number {
        if ( null == sJson || '' === sJson ) {
            return ResultType.ParamError;
        }

        let text: object;
        try {
            text = JSON.parse(sJson);
        } catch (e) {
            return ResultType.ParamError;
        }

        const newControlNames: string[] = Object.keys(text);
        const propertys: INewControlProperty[] = [];
        for (let index = 0, length = newControlNames.length; index < length; index++) {
            let contents: any = text[newControlNames[index]];
            if (!contents.property) {
                continue;
            }
            contents = contents.property;
            const props = contents;
            const propsType = Object.keys(contents);
            // const propsValue = Object.values(props);

            const prop: INewControlProperty = {
                newControlName: newControlNames[index],
                newControlSerialNumber: undefined,
                newControlInfo: undefined,
                newControlPlaceHolder: undefined,
                isNewControlHidden: undefined,
                isNewControlCanntDelete: undefined,
                isNewControlCanntEdit: undefined,
                isNewControlCanntCopy: undefined,
                isNewControlMustInput: undefined,
                isNewControlShowBorder: undefined,
                isNewControlReverseEdit: undefined,
                isNewControlHiddenBackground: undefined,
                newControlDisplayType: undefined,
                newControlTitle: undefined,
                tabJump: undefined,
                hideHasTitle: undefined,
                bClearItems: true,
                _bInterface: true,
                //add by tinyzhi
                regExp: undefined,
                cascade: undefined,
                eventInfo: undefined,
                externalDataBind:undefined,
                showType:undefined,
            };
            for (let index2 = 0, length2 = propsType.length; index2 < length2; index2++) {
                const key = propsType[index2];
                const value = props[key];
                this.setPropValue(key, value, prop);
            }
            propertys.push(prop);
        }

        if ( 0 === propertys.length ) {
            return ResultType.ParamError;
        }
        
        const result = this._documentCore.setStructsPropByArray(propertys);
        
        if ( ResultType.Success === result ) {
            this._host.handleRefresh();
        }

        return result;
    }

    // public getIncompletedCtrlNameList(): string {
    //     return this._documentCore.getNewControlNamesByCustoms();
    // }

    /**
     * 获取指定名称日期框的值
     * @param name 结构化名称
     * @param sRev 扩展参数”AllDate” 假如年份月日格式里面不存在的话，也会返回
     */
    public getDateTimeBoxValueEx(sName: string, sRev?: string): string {
        if (!sName) {
            return ResultType.StringEmpty;
        }

        const newControl = this._documentCore.getNewControlByName(sName);
        if (!newControl || !newControl.isNewDateBox()) {
            return ResultType.StringEmpty;
        }

        let curDate = (newControl as NewControlDate).getDateTime();
        if (!curDate) {
            curDate = newControl.getNewControlText();
            if (curDate) {
                // console.log(curDate)
                newControl.setDateTime(curDate);
            } else {
                return ResultType.StringEmpty;
            }
        }

        if (sRev === 'AllDate' && curDate) {
            return (newControl as NewControlDate).getAllDateText();
        }

        return newControl.getNewControlText();
    }

    /**
     * 设置指定名称日期框的值
     * @param sName 结构化名称
     * @param sValue 日期框的值
     */
    public setDateTimeBoxValue(sName: string, sValue: string): number {
        if (!sName) {
            return ResultType.ParamError;
        }

        const date = this._getDateTime(sValue);
        if (!date) {
            return ResultType.ParamError;
        }

        const newControl = this._documentCore.getNewControlByName(sName);
        if (!newControl || !newControl.isNewDateBox()) {
            return ResultType.Failure;
        }

        if (newControl.getAllDate() === sValue) {
            return ResultType.UnEdited;
        }

        const preReviewType = newControl.isReviewType();
        const preRevisionInfo = newControl.getContentReviewInfo();

        const result = newControl.setNewControlText(date);
        if (result === ResultType.Success) {
            (newControl as any).setDateTime(date);

            if (this._documentCore.isTrackRevisions()) {
                const curPortion = newControl.getPlaceHolder();

                if (preReviewType) {
                    curPortion.setReviewType2(2);
                    curPortion.setReviewInfo2(preRevisionInfo);
                } else {
                    // curPortion.resetReviewTypeAndInfo();
                    curPortion.setReviewType2(2);

                }
            }
            this._host.handleRefresh();
        }

        return result;
    }

    /**
     * 获取指定名称日期框的默认显示格式
     * @param sName 结构化名称
     */
    public getDateTimeFormat(sName: string): string {
        if (!sName) {
            return ResultType.StringEmpty;
        }

        const newControl = this._documentCore.getNewControlByName(sName);
        if (!newControl || !newControl.isNewDateBox()) {
            return ResultType.StringEmpty;
        }

        // if (newControl instanceof NewControlDate) {
        //     if (newControl.getDateBoxFormat() === DateBoxFormat.AutoCustom) {
        //         return newControl.getCustomDateBoxFormatString();
        //     }
        // }
        return (newControl as NewControlDate).getDateBoxFormatString();
    }

    /**
     * 设置指定指定名称日期框的默认显示格式
     * @param sName 结构化名称
     * @param nType 0 custom props  1   YYY-MM-DD 2	YYYY-MM-DD	HH：MM：SS	3	YYYY-MM-DD	HH：MM 4  HH：MM：SS
     */
    public setDateTimeFormat(sName: string, nType: number, setDateTimeFormat?: string): number {

        if (!sName || typeof nType !== 'number' || isNaN(nType) || !(nType in DateBoxFormat)) {
            return ResultType.ParamError;
        }

        let obj: object;
        if (setDateTimeFormat) {
            try {
                obj = JSON.parse(setDateTimeFormat);
                obj = this._getCustomFormatProps(obj);
            } catch (error) {
                return ResultType.ParamError;
            }
        }

        const newControl = this._documentCore.getNewControlByName(sName);
        if (!newControl || !newControl.isNewDateBox()) {
            return ResultType.Failure;
        }

        const result = (newControl as any).setDateBoxFormat(nType, obj);
        if (result) {
            newControl.setDateBoxText();
            this._host.handleRefresh();
            return ResultType.Success;
        }
        return ResultType.Failure;
    }

    public insertSignControlAtCurrentCursor(sName: string, sJson: string): string | number {
        if (!sName) {
            return ResultType.ParamError;
        }

        if ( null == sJson || '' === sJson ) {
            return ResultType.ParamError;
        }

        // const type: number = EXTERNAL_OUTER_STRUCT_TYPE[15];
        const signatureProperty: INewControlProperty = {
            newControlName: sName,
            newControlSerialNumber: undefined,
            newControlInfo: undefined,
            newControlPlaceHolder: '', // no space at all
            newControlType: NewControlType.SignatureBox,
            isNewControlHidden: false,
            isNewControlCanntDelete: false,
            isNewControlCanntEdit: true,
            isNewControlShowBorder: true,
            isNewControlHiddenBackground: true,
            isNewControlCanntCopy: false,
            customProperty: undefined,
            hideHasTitle: false,
            // extension
            signatureCount: STD_START_DEFAULT.signatureCount,
            preText: STD_START_DEFAULT.preText,
            signatureSeparator: STD_START_DEFAULT.signatureSeparator,
            postText: STD_START_DEFAULT.postText,
            signaturePlaceholder: STD_START_DEFAULT.signaturePlaceholder,
            signatureRatio: STD_START_DEFAULT.signatureRatio,
            rowHeightRestriction: STD_START_DEFAULT.rowHeightRestriction === 1 ? true : false
        };

        const text = JSON.parse(sJson);
        // console.log(text)
        const {signNumber, frontChar, midChar, endChar, signPlaceholder, signRatio,
            signType, alwaysShow, showSignBorder} = text;
        //if (signNumber == null || frontChar == null || midChar == null || endChar == null ||
        //    signPlaceholder == null || signRatio == null) {

            const {SignNumber, FrontChar, MidChar, EndChar, SignPlaceholder, SignRatio} = text;
         //   if (SignNumber == null || FrontChar == null || MidChar == null || EndChar == null ||
         //       SignPlaceholder == null || SignRatio == null) {
                // tslint:disable-next-line: no-console
         //       console.warn('sjon format is incorrect');
          //  } else {
         //       signatureProperty.signatureCount = SignNumber;
          //      signatureProperty.preText = FrontChar;
          //      signatureProperty.signatureSeparator = MidChar;
          //      signatureProperty.postText = EndChar;
          //      signatureProperty.signaturePlaceholder = SignPlaceholder;
           //     signatureProperty.signatureRatio = SignRatio;
         //  }

       // } else {
            // correct
         //   signatureProperty.signatureCount = signNumber;
         //   signatureProperty.preText = frontChar;
         //   signatureProperty.signatureSeparator = midChar;
         //   signatureProperty.postText = endChar;
         //   signatureProperty.signaturePlaceholder = signPlaceholder;
          //  signatureProperty.signatureRatio = signRatio;
       // }
        signatureProperty.signatureCount = SignNumber || 1;  // 默认值为1
        signatureProperty.preText = FrontChar || " ";         // 默认值为空字符串
        signatureProperty.signatureSeparator = MidChar || " ";  // 默认值为空字符串
        signatureProperty.postText = EndChar || " ";          // 默认值为空字符串
        signatureProperty.signaturePlaceholder = SignPlaceholder || "  ";  // 默认值为" "
        signatureProperty.signatureRatio = SignRatio || 1;  // 默认值为1   
        // if ( typeof signType !== 'number' || (2 !== signType && 1 !== signType) ) {
        //     return ResultType.ParamError;
        // }

        if ( SignatureType.Set === signType ) {
            if ( typeof alwaysShow !== 'number' || 1 > alwaysShow || alwaysShow > signNumber ) {
                return ResultType.ParamError;
            }
            signatureProperty.signType = signType;
            signatureProperty.alwaysShow = alwaysShow;
            signatureProperty.showSignBorder = showSignBorder ? true : false;
        } else {
            signatureProperty.signType = SignatureType.Common;
            signatureProperty.alwaysShow = signatureProperty.signatureCount;
            signatureProperty.showSignBorder = true;
        }

        const result = this._documentCore.addNewControl(signatureProperty);

        let strResult = '';
        if (result === ResultType.Success) {
            strResult = sName;
            this._host.handleRefresh();
        }

        return strResult;
    }

    public getElementCountByName(sName: string): number {
        if (!sName) {
            return ResultType.Failure2;
        }
        const newControl = this._documentCore.getNewControlByName(sName);
        // console.log(newControl);

        let result = 0;
        if (newControl != null && newControl.isSignatureBox()) {
            result = newControl.getSignatureCount();
        }

        return result;
    }

    /**
     * 批量对多个结构化元素的内容赋值。
     * @param json 设置text json
     */
    public setStructsTextByJson(json: string): number {
        if (!json) {
            return ResultType.ParamError;
        }
        let arrs: IStructParamJson[];
        try {
            arrs = JSON.parse(json);
        } catch (e) {
            return ResultType.ParamError;
        }

        if (!Array.isArray(arrs) || arrs.length === 0) {
            return ResultType.ParamError;
        }

        const res = this._documentCore.setStructsTextByJson(arrs);
        if (res === ResultType.Success) {
            this._host.handleRefresh();
        }
        return res;
    }

    public addSignTextToControl(sName: string, index: number, sText: string): number {
        if (!sName || typeof index !== 'number' || typeof sText !== 'string') {
            return ResultType.ParamError;
        }

        const newControl = this._documentCore.getNewControlByName(sName);
        // console.log(newControl);

        let subTextStructName = null;
        if (newControl != null && newControl.isSignatureBox()) {
            if (newControl instanceof NewControlSignature) {
                subTextStructName = newControl.getSubTextStructNameBySignCount(index);
            }
        }
        // console.log(subTextStructName)

        let result = ResultType.Failure;
        if (subTextStructName != null) {

            result = this._documentCore.setNewControlText(subTextStructName, sText);

            if (result === ResultType.Success) {
                this._host.handleRefresh();
            }
        }

        return result;
    }

    public deleteSignControlByName(sName: string): number {
        if (!sName) {
            return ResultType.ParamError;
        }
        const result = this._documentCore.deleteNewControl(sName);
        if (result === ResultType.Success) {
            this._host.handleRefresh();
        }
        return result;
    }

    /**
     * straightforward
     * @param sName
     * @param index 1-3-- 索引 0 – 全部子元素删除
     */
    public deleteSignContentByName(sName: string, index: number): number {
        if (!sName || typeof index !== 'number') {
            return ResultType.ParamError;
        }

        const newControl = this._documentCore.getNewControlByName(sName);
        // console.log(newControl);

        let subTextStructArray: NewControlText[] = []; // can have multiple
        if (newControl != null && newControl.isSignatureBox()) {
            if (newControl instanceof NewControlSignature) {
                if (index !== 0) {
                    subTextStructArray.push(newControl.getSubTextStructBySignCount(index));
                } else {
                    // TODO: array.slice(), inner elements pass by ref? seem so
                    subTextStructArray = (newControl.getAllSubTextStructs()).slice();
                }
            }
        }
        // console.log(subTextStructArray)

        if (subTextStructArray.length > 0) {
            for (const subTextStruct of subTextStructArray) {
                // subTextStruct.removeNewControlContent();
                // subTextStruct.setPlaceHolder(true);
                const content = subTextStruct.getNewControlContent();
                if (content && !content.hasOnlyPlaceHolderContent()) {
                    subTextStruct.addPlaceHolderContent();
                    subTextStruct.setTipsContent("");//清空提示内容
                    subTextStruct.recalculate();
                }
            }

            this._host.handleRefresh();
            return ResultType.Success;
        }

        return ResultType.Failure;
    }

    public getSignControlCount(): number {
        const newControls = this._documentCore.getAllNewControls();

        let result = 0;
        for (const newControl of newControls) {
            if (newControl.isSignatureBox()) {
                result++;
            }
        }

        return result;
    }

    public getSignControlNames(): string[] {
        const newControls = this._documentCore.getAllNewControls2();

        const result = [];
        for (const newControl of newControls) {
            if (newControl.isSignatureBox()) {
                result.push(newControl.getNewControlName());
            }
        }

        return result;
    }

    public getSignElementNames(sName: string): string {
        let res = ResultType.StringEmpty;

        if (!sName) {
            return res;
        }

        const newControl = this._documentCore.getNewControlByName(sName);
        if (newControl && newControl instanceof NewControlSignature) {
            const list = newControl.getAllSubTextStructs();
            const result = [];
            if (list) {
                list.forEach((control) => {
                    if (control) {
                        result.push(control.getNewControlName());
                    }
                });

                res = result.toString();
            }
        }

        return res;
    }

    public getSignedElementNames(sName: string): string {
        let res = ResultType.StringEmpty;

        if (!sName) {
            return res;
        }

        const newControl = this._documentCore.getNewControlByName(sName);
        if (newControl && newControl instanceof NewControlSignature) {
            const list = newControl.getAllSubTextStructs();
            const result = [];
            if (list) {
                list.forEach((control) => {

                    if (control && !control.isPlaceHolderContent()
                        && !control.isContentEmpty()) {
                        result.push(control.getNewControlName());
                    }
                });

                res = result.toString();
            }
        }

        return res;
    }

    public deleteSignContent(sName: string, sJson: string): number {
        if (!sName || !sJson ) {
            return ResultType.ParamError;
        }

        let obj;
        try {
            obj = JSON.parse(sJson);
        } catch (error) {
            return ResultType.ParamError;
        }

        const newControl = this._documentCore.getNewControlByName(sName);
        if (newControl && newControl instanceof NewControlSignature) {
            let res = false;
            if ( 0 === obj.type) { // 删除指定
                if ( obj.nameList ) {
                    const deleteList = obj.nameList.split(',');
                    if (deleteList && 0 < deleteList.length) {
                        const leafList = newControl.getLeafList();
                        for (let index = leafList.length - 1; index >= 0; index--) {
                            const leaf = leafList[index];
                            if (leaf && deleteList.includes(leaf.getNewControlName())
                                && !leaf.isPlaceHolderContent() ) {
                                leaf.addPlaceHolderContent();
                                res = true;
                            }
                        }
                        if ( !res ) {
                            return ResultType.Success;
                        }
                    }
                }
            } else if ( 1 === obj.type) { // 全部删除
                const leafList = newControl.getLeafList();
                for (let index = leafList.length - 1; index >= 0; index--) {
                    const leaf = leafList[index];
                    if (leaf && !leaf.isPlaceHolderContent()) {
                        leaf.addPlaceHolderContent();
                        res = true;
                    }
                }
                if ( !res ) {
                    return ResultType.Success;
                }
            }

            if (res) {
                this._documentCore.recalculateAllForce();
                this._documentCore.updateCursorXY();
                this._host.handleRefresh();
                return ResultType.Success;
            }
        }

        return ResultType.Failure;
    }

    public getAddressControlText(name: string): string {
        const newControl = this._documentCore.getNewControlByName(name);
        // console.log(newControl);

        const result: any = {};
        if (newControl instanceof NewControlAddress) {
            const province = newControl.getProvince();
            const city = newControl.getCity();
            const county = newControl.getCounty();

            if (province != null) {
                result.province = province;
            }
            if (city != null) {
                result.city = city;
            }
            if (county != null) {
                result.county = county;
            }
        }

        return JSON.stringify(result);
    }

    public setAddressControlText(name: string, sText: string): number {
        if (name == null || sText == null) {
            return ResultType.ParamError;
        }

        const newControl = this._documentCore.getNewControlByName(name);
        // console.log(newControl);

        if (newControl instanceof NewControlAddress) {
            // const addressCollection = composeAddressHierarchy(addressData);
            // const addressCollection = addressData;
            // console.log(data)

            let obj: any;
            try {
               obj = JSON.parse(sText);
            } catch (error) {
                return ResultType.ParamError;
            }

            // let text = '';

            // const {province, city, county} = obj;
            // let provinceIndex = -1;
            // let cityIndex = -1;
            // let countyIndex = -1;
            // if (province != null) {
            //     for (let i = 0, len = addressCollection.length; i < len; i++) {
            //         const provinceItem = addressCollection[i];
            //         if (province.code === provinceItem['code']) {
            //             provinceIndex = i;

            //             const provincePair: IAddressPair = {code: provinceItem['code'], name: provinceItem['name']};
            //             newControl.setProvince(provincePair);
            //             text += provincePair.name;
            //             break;
            //         }
            //     }
            // } else {
            //     newControl.setProvince(null);
            // }

            // if (city != null && provinceIndex !== -1) {
            //     const provinceInstance = addressCollection[provinceIndex];
            //     // console.log(provinceInstance)
            //     for (let i = 0, len = provinceInstance.children.length; i < len; i++) {
            //         const cityItem = provinceInstance.children[i];
            //         // console.log(cityItem)
            //         if (city.code === cityItem['code']) {
            //             cityIndex = i;

            //             const cityPair: IAddressPair = {code: cityItem['code'], name: cityItem['name']};
            //             newControl.setCity(cityPair);
            //             text += cityPair.name;

            //             break;
            //         }
            //     }
            // } else {
            //     newControl.setCity(null);
            // }

            // if (county != null && provinceIndex !== -1 && cityIndex !== -1) {
            //     const cityInstance = addressCollection[provinceIndex].children[cityIndex];
            //     for (let i = 0, len = cityInstance.children.length; i < len; i++) {
            //         const countyItem = cityInstance.children[i];
            //         if (county.code === countyItem['code']) {
            //             countyIndex = i;

            //             const countyPair: IAddressPair = {code: countyItem['code'], name: countyItem['name']};
            //             newControl.setCounty(countyPair);
            //             text += countyPair.name;

            //             break;
            //         }
            //     }
            // } else {
            //     newControl.setCounty(null);
            // }
            newControl.setAddress(obj);
            // newControl.setNewControlText(text);
            this._documentCore.updateCursorXY();
            // this._documentCore.recalculate();
            this._host.handleRefresh();
        }

        return ResultType.Success;
    }

    public removeCertainCharInSignControl(sName: string, sRemoveChar: string): number {
        if (!sName || typeof sName !== 'string' || !sRemoveChar || typeof sRemoveChar !== 'string') {
            return ResultType.ParamError;
        }

        // const newControl = this._documentCore.getNewControlByName(sName);
        const newControls = this._documentCore.getAllNewControls2();
        if (newControls && newControls.length > 0) {
            for (const newControl of newControls) {
                if (newControl && newControl.isSignatureBox()) {
                    const leafList = newControl.getLeafList();
                    if (leafList && leafList.length) {
                        for (let index = leafList.length - 1; index >= 0; index--) {
                            const subNewControl = leafList[index];
                            if (subNewControl.getNewControlName() === sName) {
                                const result = subNewControl.removeCertainChar(sRemoveChar);
                                if (result) {
                                    this._documentCore.recalculate();
                                    this._documentCore.updateCursorXY();
                                    this._host.handleRefresh();
                                    return ResultType.Success;
                                }
                                return ResultType.UnEdited;
                            }
                        }
                    }
                }
            }
            return ResultType.UnEdited;
        }
        return ResultType.Failure;
    }

    private setPropValue(key: string, value: any, prop: INewControlProperty): boolean {
        if (value == null) {
            return false;
        }
        switch (key) {
            case NewControlPropName.Hidden: {
                if (typeof value !== 'boolean') {
                    return false;
                }
                prop.isNewControlHidden = true === value;
                break;
            }

            case NewControlPropName.DeleteProtect: {
                if (typeof value !== 'boolean') {
                    return false;
                }
                prop.isNewControlCanntDelete = true === value;
                break;
            }

            case NewControlPropName.Alignments: {
                if (![0, 1, 2, 3].includes(value)) {
                    return false;
                }
                prop.alignments = value;
                break;
            }

            case NewControlPropName.EditProtect: {
                if (typeof value !== 'boolean') {
                    return false;
                }
                prop.isNewControlCanntEdit = true === value;
                break;
            }

            case NewControlPropName.IsKeyJump: {
                if (typeof value !== 'boolean') {
                    return false;
                }
                prop.tabJump = true === value;
                break;
            }

            case NewControlPropName.CopyProtect: {
                if (typeof value !== 'boolean') {
                    return false;
                }
                prop.isNewControlCanntCopy = true === value;
                break;
            }

            case NewControlPropName.ReverseEdit: {
                if (typeof value !== 'boolean') {
                    return false;
                }
                prop.isNewControlReverseEdit = true === value;
                break;
            }

            case NewControlPropName.Identifier: {
                if (typeof value !== 'string') {
                    return false;
                }
                if (value && !isValidName(value)) {
                    return false;
                }
                prop.identifier = value;
                break;
            }

            case NewControlPropName.Helptip: {
                if (typeof value !== 'string') {
                    return false;
                }
                prop.newControlInfo = value;
                break;
            }

            case NewControlPropName.Placeholder: {
                if (typeof value !== 'string') {
                    return false;
                }
                prop.newControlPlaceHolder = filterChars2(value);
                break;
            }

            case NewControlPropName.SerialNumber: {
                if (typeof value !== 'string') {
                    return false;
                }
                prop.newControlSerialNumber = filterChars(value);
                break;
            }

            case NewControlPropName.IsMustInput: {
                if (typeof value !== 'boolean') {
                    return false;
                }
                prop.isNewControlMustInput = true === value;
                break;
            }

            case NewControlPropName.TextBorder: {
                if (typeof value !== 'boolean') {
                    return false;
                }
                prop.bTextBorder = true === value;
                break;
            }

            case NewControlPropName.ShowCodeAndValue: {
                if (typeof value !== 'boolean') {
                    return false;
                }
                prop.bShowCodeAndValue = true === value;
                break;
            }

            case NewControlPropName.ShowBorder: {
                if (typeof value !== 'boolean') {
                    return false;
                }
                prop.isNewControlShowBorder = true === value;
                break;
            }

            case NewControlPropName.HiddenBackground: {
                if (typeof value !== 'boolean') {
                    return false;
                }
                prop.isNewControlHiddenBackground = true === value;
                break;
            }

            case NewControlPropName.DisplayType: {
                if (![1, 2, 3].includes(value)) {
                    return false;
                }
                prop.newControlDisplayType = value;
                break;
            }

            case NewControlPropName.CustomProperty: {
                if (typeof value !== 'object') {
                    return false;
                }
                prop.customProperty = this._getCustomProps(value as any);
                break;
            }

            //增加对下拉属性的支持 add by tinyzhi
            case NewControlPropName.ListItem: {
                if (typeof value !== 'object') {
                    return false;
                }
                prop.newControlItems = value;
                break;
            }

            case NewControlPropName.RegExp:{
                if (typeof value !== 'string') {
                    return false;
                }
                prop.regExp = value;
                break;
            }

            case NewControlPropName.ExternalDataBind:{
                if (typeof value !== 'string') {
                    return false;
                }
                const obj = JSON.parse(value);
                if (typeof obj === 'object' && obj !== null){
                    prop.externalDataBind = obj;
                }
                break;
            }

            // case NewControlPropName.LogicEvent:{
            //     if (typeof value === 'string') {
            //         const obj = JSON.pnarse(value);
            //         if (typeof obj === 'object' && obj !== null){
            //             prop.cascade = obj;
            //             break;
            //         }
            //     }
            //     else
            //         return false;
                
            // }

            // case NewControlPropName.EventInfo:{
            //     if (typeof value === 'string') {
            //         const obj = JSON.parse(value);
            //         if (typeof obj === 'object' && obj !== null){
            //             prop.eventInfo = obj;
            //             break;
            //         }
            //     }
            //     else
            //         return false;
            // }

            case NewControlPropName.Title: {
                if (typeof value !== 'string') {
                    return false;
                }
                prop.newControlTitle = filterChars2(value);
                break;
            }
            case NewControlPropName.FixedLength: {
                if (typeof value !== 'number') {
                    return false;
                }
                prop.newControlFixedLength = value;
                break;
            }
            case NewControlPropName.MaxLength: {
                if (typeof value !== 'number') {
                    return false;
                }
                prop.newControlMaxLength = value;
                break;
            }
            case NewControlPropName.PrefixContent: {
                if (typeof value !== 'string') {
                    return false;
                }
                prop.prefixContent = filterChars2(value);
                break;
            }
            case NewControlPropName.SelectPrefixContent: {
                if (typeof value !== 'string') {
                    return false;
                }
                prop.selectPrefixContent = filterChars2(value);
                break;
            }
            case NewControlPropName.Separator: {
                if (typeof value !== 'string') {
                    return false;
                }
                prop.separator = filterChars2(value);
                break;
            }
            case NewControlPropName.MaxValue: {
                if (typeof value !== 'number') {
                    return false;
                }
                prop.maxValue = value;
                break;
            }
            case NewControlPropName.MinValue: {
                if (typeof value !== 'number') {
                    return false;
                }
                prop.minValue = value;
                break;
            }
            case NewControlPropName.Precision: {
                if (typeof value !== 'number') {
                    return false;
                }
                prop.precision = value;
                break;
            }
            case NewControlPropName.Unit: {
                if (typeof value !== 'string') {
                    return false;
                }
                prop.unit = value;
                break;
            }
            case NewControlPropName.ForceValidate: {
                if (typeof value !== 'boolean') {
                    return false;
                }
                prop.forceValidate = value;
                break;
            }
            case NewControlPropName.Retrieve: {
                if (typeof value !== 'boolean') {
                    return false;
                }
                prop.retrieve = value;
                break;
            }
            case NewControlPropName.PrintSelected: {
                if (typeof value !== 'boolean') {
                    return false;
                }
                prop.printSelected = value;
                break;
            }
            case NewControlPropName.Group: {
                if (typeof value !== 'string') {
                    return false;
                }
                prop.group = value;
                break;
            }
            case NewControlPropName.HideHasTitle: {
                if (typeof value !== 'boolean') {
                    return false;
                }
                prop.hideHasTitle = true === value;
                break;
            }
            case NewControlPropName.LabelCode: {
                if (typeof value !== 'string') {
                    return false;
                }
                prop.labelCode = value;
                break;
            }
            case NewControlPropName.ShowSignBorder: {
                if (typeof value !== 'boolean') {
                    return false;
                }
                prop.showSignBorder = value;
                break;
            }

            case NewControlPropName.SpaceNum: {
                if (typeof value !== 'number' || value < 0 || !Number.isInteger(value) || !Number.isSafeInteger(value)) {
                    return false;
                }
                prop.spaceNum = value;
                break;
            }
            case NewControlPropName.SupportLine: {
                if (typeof value !== 'boolean') {
                    return false;
                }
                prop.supportMultLines = value;
                break;
            }

            //add by tinyzhi 
            case NewControlPropName.ShowType:{
                if (typeof value !== 'number') {
                    return false;
                }
                prop.showType = value;
                break;
            }

            case NewControlPropName.ShowRight:{
                if (typeof value !== 'boolean') {
                    return false;
                }
                prop.showRight = value;
                break;
            }

            case NewControlPropName.Label:{
                if (typeof value !== 'string') {
                    return false;
                }
                prop.label = value;
                break;
            }

            //add date time by tinyzhi
            case NewControlPropName.StartDate:{
                if (typeof value !== 'string') {
                    return false;
                }
                prop.startDate = value;
                break;
            }

            case NewControlPropName.EndDate:{
                if (typeof value !== 'string') {
                    return false;
                }
                prop.endDate = value;
                break;
            }

            case NewControlPropName.DateType:{
                if (typeof value !== 'number') {
                    return false;
                }
                prop.dateBoxFormat = value;
                break;
            }

            case NewControlPropName.CustomDateFormat:{
                if (typeof value !== 'string') {
                    return false;
                }
                const obj = this._parseFormatString(value);
                if(obj)
                    prop.customFormat = obj;
                break;
            }

            case NewControlPropName.Hierarchy:{
                if (typeof value !== 'number') {
                    return false;
                }
                prop.hierarchy = value;
                break;
            }
            // case NewControlPropName.Hierarchy: {
            //     if (typeof value !== 'number') {
            //         return false;
            //     }
            //     const hierarchyVals = [HierarchyLevel.First, HierarchyLevel.Second, HierarchyLevel.Third];
            //     if (hierarchyVals.includes(value) === false) {
            //         return false;
            //     }

            //     prop.hierarchy = value;
            //     break;
            // }
            default:
                return false;
        }

        return true;
    }

    private _parseFormatString(formatString: string): ICustomFormatDateProps {
        const result: ICustomFormatDateProps = {};
      
        // 正则表达式来匹配日期和时间部分的各个组成部分
        const regex = /(y{2,4})([^yM]+)?(M{1,2})([^Md]+)?(d{1,2})([^dH]+)?(H{1,2})([^Hm]+)?(m{1,2})([^ms]+)?(s{1,2})?(.*)?(S{1,3})?(.*)?/;
        const match = regex.exec(formatString);
      
        if (match) {
          const [
            , year, yearAndMonth, month, monthAnyDay, day, dayAfter,
            hour, hourAndMinute, minute, minuteAndSecond, second, secondAfter, millisecond, millisecondAfter
          ] = match;
      
          // 年、月、日、时、分、秒、毫秒 和 分隔符
          result.year = year ? year : undefined;
          result.yearAndMonth = yearAndMonth ? yearAndMonth : undefined;
          result.month = month ? month : undefined;
          result.monthAnyDay = monthAnyDay ? monthAnyDay : undefined;
          result.day = day ? day : undefined;
          result.dayAfter = dayAfter ? dayAfter : undefined;
          result.hour = hour ? hour : undefined;
          result.hourAndMinute = hourAndMinute ? hourAndMinute : undefined;
          result.minute = minute ? minute : undefined;
          result.minuteAndSecond = minuteAndSecond ? minuteAndSecond : undefined;
          result.second = second ? second : undefined;
          result.secondAfter = secondAfter ? secondAfter : undefined;
          result.millisecond = millisecond ? millisecond : undefined;
          result.millisecondAfter = millisecondAfter ? millisecondAfter : undefined;
        }
      
        return result;
      }


    private _getCustomFormatProps(obj: object): ICustomFormatDateProps {
        const keys = Object.keys(obj);
        const res: ICustomFormatDateProps = {};
        // tslint:disable-next-line: forin
        keys.forEach((key) => {
            switch (key) {
                case customFormatProps.Year: {
                    res.year = obj[key];
                    break;
                }
                case customFormatProps.YearSeparat: {
                    res.yearAndMonth = obj[key];
                    break;
                }
                case customFormatProps.Month: {
                    res.month = obj[key];
                    break;
                }
                case customFormatProps.MonthSeparat: {
                    res.monthAnyDay = obj[key];
                    break;
                }
                case customFormatProps.Day: {
                    res.day = obj[key];
                    break;
                }
                case customFormatProps.DaySeparat: {
                    res.dayAfter = obj[key];
                    break;
                }
                case customFormatProps.Hour: {
                    res.hour = obj[key];
                    break;
                }
                case customFormatProps.HourSeparat: {
                    res.hourAndMinute = obj[key];
                    break;
                }
                case customFormatProps.Minute: {
                    res.minute = obj[key];
                    break;
                }
                case customFormatProps.MinuteSeparat: {
                    res.minuteAndSecond = obj[key];
                    break;
                }
                case customFormatProps.Second: {
                    res.second = obj[key];
                    break;
                }
                case customFormatProps.SecondSeparat: {
                    res.secondAfter = obj[key];
                    break;
                }
                case customFormatProps.Millisecond: {
                    res.millisecond = obj[key];
                    break;
                }
                case customFormatProps.MillisecondSeparat: {
                    res.millisecondAfter = obj[key];
                    break;
                }
                default:
                    break;
            }
        });

        return res;
    }

    private _getCustomProps(obj: object): ICustomProps[] {
        if (!obj) {
            return;
        }
        const keys = Object.keys(obj);
        if (!keys.length) {
            return;
        }
        const arrs: ICustomProps[] = [];
        keys.forEach((key) => {
            const val = obj[key];
            let type = this._getDataType(val);
            if (type === undefined) {
                type = DataType.String;
            }
            arrs.push({name: key, value: val + '', type});
        });
        return arrs;
    }

    private _getDataType(str: any): DataType {
        let type: DataType;
        switch (typeof str) {
            case 'string': {
                type = DataType.String;
                break;
            }
            case 'number': {
                type = DataType.Number;
                break;
            }
            case 'boolean': {
                type = DataType.Boolean;
                break;
            }
            default: {
                return;
            }
        }
        return type;
    }

    private _getDateTime(sDate: string): string {
        if (!sDate) {
            return '';
        }

        const reg = /(Date=(\d{4}-\d{1,2}-\d{1,2}))?(\s?Time=(\d{1,2}:\d{1,2}(:\d{1,2})?))?/ig;
        const matchs = reg.exec(sDate);
        if (!matchs) {
            return '';
        }

        let time1 = matchs[2];
        if (!time1) {
            time1 = '2000-01-01';
        }

        let time: string = matchs[4];
        if (!time) {
            time = '00:00:00';
        } else if (!matchs[5]) {
            time += ':00';
        }

        const result: string = `${time1} ${time}`;
        return result;
    }

    private _filterStrcut(oProp: object, sRev1?: string): string {
        const keys = Object.keys(oProp);
        if (keys.length === 0) {
            return ResultType.StringEmpty;
        }
        // const propNames = this._propNames;
        // const activeProps = {};
        // // 过滤有效的属性
        // keys.forEach((key) => {
        //     // const newKey = key.replace(/^([a-z])(\w+)/, (str1, str2: string, str3) => {
        //     //     return str2.toLocaleUpperCase() + str3;
        //     // });

        //     let val = oProp[key];
        //     if (typeof val === 'number') {
        //         val = !!val;
        //     }
        //     activeProps[key] = val;
        // });

        // keys = Object.keys(activeProps);
        // if (keys.length === 0) {
        //     return ResultType.StringEmpty;
        // }

        const newControls = this._documentCore.getAllNewControls2();
        if (newControls.length === 0) {
            return ResultType.StringEmpty;
        }

        // const filterDatas: ParaNewControl[] = [];
        const filterDatas = this.forEachNewControl(newControls, oProp, keys, sRev1);

        if (filterDatas.length > 0) {
            return filterDatas.join(',');
        } else if (sRev1 === NewControlFilterType.completeObject && Object.keys(filterDatas).length > 0) {
            return JSON.stringify(filterDatas);
        }

        return ResultType.StringEmpty;
    }

    private forEachNewControl(newControls: ParaNewControl[], prop: any, keys: string[], sRev1?: string): any {
        let result: any;
        if (sRev1 === NewControlFilterType.completeObject) {
            result = {};
        } else {
            result = [];
        }
        for (let newIndex = 0, length = newControls.length; newIndex < length; newIndex++) {
            const newControl = newControls[newIndex];
            let flag = true;
            const resultObj = {};
            for (let index = 0, len = keys.length; index < len; index++) {
                const sPropName = keys[index];
                let val = prop[sPropName];
                const current = this._getValueByPropName(sPropName, newControl);
                if (current === undefined) {
                    flag = false;
                    break;
                }
                if (val === null && sRev1 === NewControlFilterType.completeObject) {
                    val = current;
                } else if (val !== current) {
                    flag = false;
                    break;
                }
                resultObj[sPropName] = val;
            }
            if (flag === true) {
                const name = newControl.getNewControlName();
                if (sRev1 === NewControlFilterType.completeObject) {
                    result[name] = resultObj;
                } else {
                    result.push(name);
                }
            }
        }

        return result;
    }

    private _getValueByPropName(sPropName: string, newControl: ParaNewControl): any {
        let result: any;
        switch (sPropName) {
            case NewControlPropName.ReverseEdit:
                result = newControl.isReverseEdit();
                break;
            case NewControlPropName.DeleteProtect:
                result = newControl.isDeleteProtect();
                break;
            case NewControlPropName.EditProtect:
                result = newControl.isEditProtect();
                break;
            case NewControlPropName.CopyProtect:
                result = newControl.isCopyProtect();
                break;
            case NewControlPropName.Placeholder:
                result = newControl.getPlaceHolderContent();
                break;
            case NewControlPropName.IsMustInput:
                result = newControl.isMustInput();
                break;
            case NewControlPropName.Alignments:
                result = newControl.getAlignments();
                break;
            case NewControlPropName.IsKeyJump:
                result = newControl.isTabJump();
                break;
            case NewControlPropName.Identifier:
                result = newControl.getIdentifier();
                break;
            case NewControlPropName.Helptip:
                result = newControl.getTipsContent();
                break;
            case NewControlPropName.ShowBorder:
                result = newControl.isShowBorder();
                break;
            case NewControlPropName.DisplayType:
                result = newControl.getViewSecretType();
                break;
            case NewControlPropName.HiddenBackground:
                result = newControl.isHiddenBackground();
                break;
            case NewControlPropName.Hidden:
                result = newControl.isHidden();
                break;
            case NewControlPropName.Title:
                result = newControl.getTitle();
                break;
            case NewControlPropName.SerialNumber:
                result = newControl.getSerialNumber();
                break;
            case NewControlPropName.FixedLength: {
                result = newControl.getFixedLength();
                break;
            }
            case NewControlPropName.MaxLength: {
                result = newControl.getMaxLength();
                break;
            }
            case NewControlPropName.TextBorder: {
                result = newControl.isTextBorder();
                break;
            }
            case NewControlPropName.ShowCodeAndValue: {
                result = newControl.isShowCodeAndValue();
                break;
            }
            case NewControlPropName.PrefixContent: {
                result = newControl.getPrefixContent();
                break;
            }
            case NewControlPropName.SelectPrefixContent: {
                result = newControl.getSelectPrefixContent();
                break;
            }
            case NewControlPropName.Separator: {
                result = newControl.getSeparator();
                break;
            }
            case NewControlPropName.MaxValue: {
                result = newControl.getMaxValue();
                break;
            }
            case NewControlPropName.MinValue: {
                result = newControl.getMinValue();
                break;
            }
            case NewControlPropName.Precision: {
                result = newControl.getPrecision();
                break;
            }
            case NewControlPropName.Unit: {
                result = newControl.getUnit();
                break;
            }
            case NewControlPropName.ForceValidate: {
                result = newControl.getForceValidate();
                break;
            }
            case NewControlPropName.Retrieve: {
                result = newControl.getRetrieve();
                break;
            }
            case NewControlPropName.PrintSelected: {
                result = newControl.getPrintSelected();
                break;
            }
            case NewControlPropName.Group: {
                result = newControl.getGroup();
                break;
            }
            case NewControlPropName.HideHasTitle: {
                result = newControl.getHideHasTitle();
                break;
            }
            case NewControlPropName.LabelCode: {
                result = newControl.getLabelCode();
                break;
            }
            case NewControlPropName.ShowSignBorder: {
                if (newControl.getType() === NewControlType.SignatureBox && newControl instanceof NewControlSignature) {
                    result = newControl.getShowSignBorder();
                }
                break;
            }

            case NewControlPropName.SpaceNum: {
                if (newControl.isMultiAndRadio()) {
                    result = (newControl as any).getSpaceNum();
                }
                break;
            }
            case NewControlPropName.SupportLine: {
                if (newControl.isMultiAndRadio()) {
                    result = (newControl as any).isSupportMultLines();
                }
                break;
            }

            default:
                result = newControl.getCustomByPropName(sPropName);
        }

        // if (typeof result === 'boolean') {
        //     result = result ? 1 : 0;
        // }

        return result;
    }
}
