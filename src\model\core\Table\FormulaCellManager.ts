import { ResultType } from '../../../common/commonDefines';

export class FormulaCellManager {
    private cellNames: Map<string, string[]>;
    private table: any;

    constructor(table: any) {
        this.table = table;
        this.cellNames = new Map();
    }

    public setFormulaCells(resultCell: string, cellsName: string[], bClear?: boolean): number {
        if ( true === bClear ) {
            return this.clearFormulaCells(resultCell, cellsName);
        }

        if ( !this.isValid(resultCell, cellsName) ) {
            return ResultType.Invalid;
        }

        for (const cell of cellsName) {
            if ( this.cellNames.has(cell) ) {
                this.cellNames.get(cell)
                        .push(resultCell);
            } else {
                const res: string[] = [];
                res.push(resultCell);
                this.cellNames.set(cell, res);
            }
        }

        // console.log(this.cellNames)

        return ResultType.Success;
    }

    public clearFormulaCells(resultCell: string, cellNames: string[]): number {
        let result = ResultType.UnEdited;

        for (const name of cellNames) {
            if ( this.cellNames.has(name) ) {
                const res = this.cellNames.get(name);
                const pos = res.indexOf(resultCell);
                if ( -1 !== pos ) {
                    res.splice(pos, 1);
                    result = ResultType.Success;
                }
            }
        }

        return result;
    }

    public hasFormula(cellName: string): boolean {
        return this.cellNames.has(cellName);
    }

    public execFormulaCalc(cellName: string): boolean {
        let result = false;
        if ( this.table && this.hasFormula(cellName) ) {
            const cellNames = this.cellNames.get(cellName);
            const extentCellNames: string[] = [];

            for (const name of cellNames) {
                if ( this.cellNames.has(name) ) {
                    extentCellNames.push(name);
                }
                result = this.table.calcFormulaByCellName(name) || result;
            }

            for (let index = 0, length = extentCellNames.length; index < length; index++) {
                const element = extentCellNames[index];
                result = this.execFormulaCalc(element) || result;
            }
        }

        return result;
    }

    private isValid(resultCell: string, cellsName: string[]): boolean {
        const res = this.cellNames.get(resultCell);

        for (const cellName of cellsName) {
            if ( res && 0 < res.length && -1 !== res.indexOf(cellName) ) {
                return false;
            }
        }

        return true;
    }
}
