@import './global.less';
.hz-editor-container {
    .title-container > ul {
        overflow: hidden;
    }

    .title-container > ul > li {
        float: left;
        width: 80px;
        height: 30px;
        margin: 0 auto;
        line-height: 30px;
        font-size: 14px;
        text-align: center;
        cursor: pointer;
        background-color: #eee;
    }

    .title-container > ul > li.active {
        color: @activeColor;
        cursor: default;
        font-weight: bold;
        background-color: #fff;
    }

    .char-content-container > span {
        display: inline-block;
        width: 62px;
        margin: 4px 2px;
        line-height: 20px;
        text-align: center;
        cursor: pointer;
    }
}
