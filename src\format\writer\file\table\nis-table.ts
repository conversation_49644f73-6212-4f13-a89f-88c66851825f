// http://officeopenxml.com/WPtableGrid.php
import { XmlComponent, XmlAttributeComponent } from '../xml-components';

import { TableGrid } from './grid';
import { NISTableCell } from './table-cell';
import { TableProperties } from './table-properties';
import { NISTableRow } from './table-row';
import { TableRow as TableRowCore } from '../../../../model/core/Table/TableRow';

export interface INISTableAttributesProperties {
    name: string;
}

class NISTableAttributes extends XmlAttributeComponent<INISTableAttributesProperties> {
    protected xmlKeys: any = {
        name: 'name',
    };
}

export class NISTable extends XmlComponent {
    private properties: TableProperties;
    private readonly rows: NISTableRow[];
    private readonly grid: TableGrid;

    constructor(rows: number, cols: number, attrs: INISTableAttributesProperties, colSizes?: number[]) {
        // super('w:NIStbl');
        super('w:CTTbl');
        this.root.push(new NISTableAttributes(attrs));

        this.initializeProperties();

        if (colSizes && colSizes.length > 0) {
            this.grid = new TableGrid(colSizes);
        } else {
            const gridCols: number[] = [];
            for (let i = 0; i < cols; i++) {
                /*
                  0-width columns don't get rendered correctly, so we need
                  to give them some value. A reasonable default would be
                  ~6in / numCols, but if we do that it becomes very hard
                  to resize the table using setWidth, unless the layout
                  algorithm is set to 'fixed'. Instead, the approach here
                  means even in 'auto' layout, setting a width on the
                  table will make it look reasonable, as the layout
                  algorithm will expand columns to fit its content
                 */
                gridCols.push(100);
            }
            this.grid = new TableGrid(gridCols);
        }

        this.root.push(this.grid);

        this.rows = [];
        // rows and cells are dynamic, shouldn't set in constructor
        // for (let i = 0; i < rows; i++) {
        //     const cells: TableCell[] = [];
        //     for (let j = 0; j < cols; j++) {
        //         cells.push(new TableCell());
        //     }
        //     const row = new TableRow(cells);
        //     // const row = new TableRow(); // gridspan, cell may be less
        //     this.rows.push(row);
        //     this.root.push(row);
        // }
    }

    public addRow(tableRow: TableRowCore): void {
        const cols = tableRow.content.length;
        const cells: NISTableCell[] = [];
        for (let i = 0; i < cols; i++) {
            cells.push(new NISTableCell());
        }
        const row = new NISTableRow(cells);
        this.rows.push(row);
        this.root.push(row);
    }

    public getRow(ix: number): NISTableRow {
        const row = this.rows[ix];

        if (!row) {
            throw Error('Index out of bounds when trying to get row on table');
        }

        return row;
    }

    public getCell(row: number, col: number): NISTableCell {
        return this.getRow(row)
                    .getCell(col);
    }

    // public setWidth(type: WidthType, width: number | string): Table {
    //     this.properties.setWidth(type, width);
    //     return this;
    // }

    public get Properties(): TableProperties {
        return this.properties;
    }

    /**
     * initialize properties if not
     */
    private initializeProperties(): void {
        if (!this.properties) {
            this.properties = new TableProperties();

            // should be the first child element
            // this.root.push(this.properties);
            this.root.unshift(this.properties);
        }
    }
}
