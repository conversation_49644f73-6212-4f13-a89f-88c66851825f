import * as React from 'react';
import { DocumentCore } from '../../model/DocumentCore';
import './style/NewDateBox.less';
// tslint:disable-next-line: max-line-length
import { INewControlProperty, DateBoxFormat, YEAR_RANGE, ICustomFormatDateProps, DateBoxMode, ViewModeType } from '../../common/commonDefines';
import { NewControlDate } from '../../model/core/NewControl/NewControlDate';
import {
  GlobalEvent as gEvent,
  GlobalEventName as gEventName,
} from '../../common/GlobalEvent';
// import { message } from '../../common/Message';

interface INewDateBoxListProps {
  host?: any;
  documentCore: DocumentCore;
  newControlPropety: INewControlProperty;
  scale?: number;
  closeNewBoxList?: () => void;
  refresh?: () => void;
  // date format
  mode?: DateBoxMode;
  setStartEndDate?: (mode?: DateBoxMode, selectedDate?: string) => void;
  className?: string;
  position?: {left: number, top: number, width: number};
  bFromEnter?: boolean;
  lineHeight?: number;
}

interface INewDateBoxListState {
  year: number;
  month: number;
  date: number;
  hour: number;
  minute: number;
  second: number;
  selectedDate: string;
  dateBoxFormat: DateBoxFormat;
  dropdownCenterYear: number; // dropdown center year as ui reference
  bYMPage: boolean;
  tempYear: number;
  tempMonth: number;
}

interface IDataFromContrlText {
  dateObj: any;
  dateString: string;
  dateBoxFormat: DateBoxFormat;
}

export class NewDateBox extends React.Component<INewDateBoxListProps, INewDateBoxListState> {
  private yearRef: any;
  private yearContainerRef: any;
  private dateDom: any;
  // 日期框下边与右边距离不够往左面挤时，可能导致鼠标指针在日期框界面内，使编辑器的mouseup不触发。该var即为追踪该情况
  private allowEditorMouseUp: boolean;
  private mode: DateBoxMode;
  private dateString: string;
  private top: number;
  // private yearMax: number;
  // private yearMin: number;

  constructor(props: any) {
    super(props);
    this.yearRef = React.createRef();
    this.yearContainerRef = React.createRef();
    this.dateDom = React.createRef();
    this.allowEditorMouseUp = false;
    this.mode = props.mode != null ? props.mode : DateBoxMode.Normal;
    // read existing text
    try {
      const {dateObj, dateString, dateBoxFormat} = this.setDataFromControlText(this.props);
      // this.setYearRange();

      this.state = {
        year: dateObj.getFullYear(),
        month: dateObj.getMonth() + 1,
        date: dateObj.getDate(),
        selectedDate: dateString,
        hour: dateObj.getHours(),
        minute: dateObj.getMinutes(),
        second: dateObj.getSeconds(),
        dateBoxFormat: (dateBoxFormat != null) ? dateBoxFormat : DateBoxFormat.DateAndHMS,
        dropdownCenterYear: dateObj.getFullYear(),
        bYMPage: false,
        tempYear: dateObj.getFullYear(),
        tempMonth: dateObj.getMonth() + 1,
      };
    } catch (error) {
      this.state = {
        year: undefined,
        month: undefined,
        date: undefined,
        selectedDate: undefined,
        hour: undefined,
        minute: undefined,
        second: undefined,
        dateBoxFormat: undefined,
        dropdownCenterYear: undefined,
        bYMPage: false,
        tempYear: undefined,
        tempMonth: undefined,
      };
      console.log('NewDateBox: ', error);
    }
    gEvent.addEvent(this.props.documentCore.getCurrentId(), gEventName.Mouseup, this.mousedownEvent);
  }

  public dateClick = (e: any): void => {
    const target = e.target;
    const tagName = target.nodeName;
    const className: string = target.className || '';
    const classList = target.classList;
    if (tagName === 'TD') {
      if (classList.contains('date-cell')) {
        this.selectDate(e);
      } else if (classList.contains('year-cell') && classList.contains('year-scroll')) {
        // special year-cell, which scroll year
        this.handleArrowClick(e);
      } else { // in ympage, should be enough
        this.selectYMPage(e);
      }

    } else if (tagName === 'BUTTON') {
      const index = target.getAttribute('data-index');
      switch (index) {
        case '0': {
          this.clearDateBox();
          break;
        }
        case '1': {
          this.setToday(e);
          break;
        }
        case '2': {
          this.confirm();
          break;
        }
        case '4': { // ymPage confirm
          this.confirmYMPage();
          break;
        }
        case '5': { // ymPage cancel
          this.cancelYMPage();
          break;
        }
        default: {
          break;
        }
      }
    } else {

      this.handleArrowClick(e);

    }
    e.stopPropagation();
    // e.preventDefault();
  }

  public handleDblclick = (e: any): void => {
    if (e.target.classList.contains('disabled-text')) {
      return ;
    }
    if (e.target.classList.contains('year-scroll')) {
      return ;
    }

    if (e.target.classList.contains('date-cell')) {
      this.confirm();
    } else if (e.target.classList.contains('month-cell') || e.target.classList.contains('year-cell')) {
      this.confirmYMPage();
    }
    e.stopPropagation();
  }

  public dateMouseUp = (e: any): void => {
    if (this.allowEditorMouseUp === true) {
      // gEvent.setEvent(this.props.host.docId, gEventName.Mouseup, e);
      // const logicDocument = this.props.host.documentCore.getDocument();
      // logicDocument.selection.bStart = false;
      // logicDocument.selection.bUse = false;
      this.props.host.documentCore.removeSelection();
      this.allowEditorMouseUp = false;
    }
  }

  public componentDidMount(): void {
    const dateDom = this.dateDom.current;
    if (!dateDom) {
      return;
    }
    dateDom.addEventListener('click', this.dateClick);
    dateDom.addEventListener('dblclick', this.handleDblclick);
    try {
      if (this.isNormalDateBox() === true) {
        // const position = this.props.position;
        // if (position) {
        //     const dom = dateDom;
        //     let top = position.top;
        //     const maxHeight = document.firstElementChild.clientHeight - top;
        //     const currentHeight = dom.clientHeight;
        //     const subHeight = currentHeight - maxHeight;
        //     if (subHeight > 1) {
        //         top -= subHeight + 50;
        //         dom.style.top = top + 'px';
        //     }
        // } else {
        //   // this.adjustDateBoxPosition();
        // }
        // let top = this.top;
        let top = dateDom.getBoundingClientRect().top;
        const maxHeight = document.firstElementChild.clientHeight - top;
        const currentHeight = dateDom.clientHeight;
        const subHeight = currentHeight - maxHeight;
        if (subHeight > 1) {
            top = this.top - currentHeight - this.props.lineHeight; // - 5;
            dateDom.style.top = top + 'px';
        }

        // focus in hour box
        const hourbox = dateDom.querySelector('.time-wrapper input[name=hour]');
        // console.log(hourbox)
        hourbox.focus();
      }

      if (this.props.bFromEnter) {
        this.mousedownEvent(null);
        // setTimeout(() => {
        //     dom.querySelector('li').focus();
        // }, 150);
      }
    } catch (error) {
      console.log(error);
    }

    dateDom.addEventListener('mouseup', this.dateMouseUp);
    dateDom.addEventListener('keydown', this.keydown);
  }

  public UNSAFE_componentWillReceiveProps(nextProps: INewDateBoxListProps): void {
    if (nextProps.newControlPropety.newControlName !== this.props.newControlPropety.newControlName) {
      try {
        // read existing text
        const {dateObj, dateString, dateBoxFormat} = this.setDataFromControlText(nextProps);

        this.setState({
          year: dateObj.getFullYear(),
          month: dateObj.getMonth() + 1,
          date: dateObj.getDate(),
          selectedDate: dateString,
          hour: dateObj.getHours(),
          minute: dateObj.getMinutes(),
          second: dateObj.getSeconds(),
          dateBoxFormat: (dateBoxFormat != null) ? dateBoxFormat : DateBoxFormat.DateAndHMS,
          dropdownCenterYear: dateObj.getFullYear(),
          bYMPage: false,
          tempYear: dateObj.getFullYear(),
          tempMonth: dateObj.getMonth() + 1,
        });
      } catch (error) {
        console.log(error);
      }

    }
  }

  public componentWillUnmount(): void {
    const dateDom = this.dateDom.current;
    if (!dateDom) {
      return;
    }
    dateDom.removeEventListener('click', this.dateClick);
    dateDom.removeEventListener('dblclick', this.handleDblclick);
    dateDom.removeEventListener('mouseup', this.dateMouseUp);
    dateDom.removeEventListener('keydown', this.keydown)
    gEvent.deleteEvent(this.props.documentCore.getCurrentId(), gEventName.Mouseup, this.mousedownEvent);
  }

  public renderDate(): any {

    const isoDate = this.state.year + '-' + (this.state.month) + '-' + this.state.date; // no problem!
    const dateArr = this.prepareDateArray(new Date(isoDate)); // current 'potential-to-be-selected' date

    const dayClasses = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
    return dateArr.map((dateLine, index) => {
      return (
        <tr key={index}>
          <td className={dayClasses[0] + ' date-cell ' + (this.bDateDisabled(+dateLine[0], index, dateArr) ? 'disabled-text':'') + ' ' + (this.bSelectedDate(dateLine[0], index, dateArr) ? 'focused-text':'')} data-value={dateLine[0]}>{dateLine[0]}</td>
          <td className={dayClasses[1] + ' date-cell ' + (this.bDateDisabled(+dateLine[1], index, dateArr) ? 'disabled-text':'') + ' ' + (this.bSelectedDate(dateLine[1], index, dateArr) ? 'focused-text':'')} data-value={dateLine[1]}>{dateLine[1]}</td>
          <td className={dayClasses[2] + ' date-cell ' + (this.bDateDisabled(+dateLine[2], index, dateArr) ? 'disabled-text':'') + ' ' + (this.bSelectedDate(dateLine[2], index, dateArr) ? 'focused-text':'')} data-value={dateLine[2]}>{dateLine[2]}</td>
          <td className={dayClasses[3] + ' date-cell ' + (this.bDateDisabled(+dateLine[3], index, dateArr) ? 'disabled-text':'') + ' ' + (this.bSelectedDate(dateLine[3], index, dateArr) ? 'focused-text':'')} data-value={dateLine[3]}>{dateLine[3]}</td>
          <td className={dayClasses[4] + ' date-cell ' + (this.bDateDisabled(+dateLine[4], index, dateArr) ? 'disabled-text':'') + ' ' + (this.bSelectedDate(dateLine[4], index, dateArr) ? 'focused-text':'')} data-value={dateLine[4]}>{dateLine[4]}</td>
          <td className={dayClasses[5] + ' date-cell ' + (this.bDateDisabled(+dateLine[5], index, dateArr) ? 'disabled-text':'') + ' ' + (this.bSelectedDate(dateLine[5], index, dateArr) ? 'focused-text':'')} data-value={dateLine[5]}>{dateLine[5]}</td>
          <td className={dayClasses[6] + ' date-cell ' + (this.bDateDisabled(+dateLine[6], index, dateArr) ? 'disabled-text':'') + ' ' + (this.bSelectedDate(dateLine[6], index, dateArr) ? 'focused-text':'')} data-value={dateLine[6]}>{dateLine[6]}</td>
        </tr>
      );
    });
  }

  public renderYMPage(): any {
    return (
      <div className={'ym-container' + (this.state.bYMPage ? ' show' : '')}>
        <div className='ym-container-left'>
          <table className='month-block ym-table' >
            <tbody>
              {this.renderDropdownMonth()}
            </tbody>
          </table>
        </div>
        <div className='ym-container-right'>
          <table className='year-block ym-table' >
            <tbody>
              {this.renderDropdownYear()}
            </tbody>
          </table>
        </div>

        <div className='button-wrapper'>
          <button className='button' data-index={4}>确定</button>
          <button className='button' data-index={5}>取消</button>
        </div>
      </div>
    );
  }

  public renderDropdownYear(): any {

    const currentYear = (this.state.dropdownCenterYear != null) ? this.state.dropdownCenterYear : this.state.tempYear;
    // const currentYear = (this.state.tempYear != null) ? this.state.dropdownCenterYear : this.state.year;
    // pattern:
    // << >>
    //  - -
    //  - -
    //  - X
    //  - -
    //  - -
    // make currentYear in this dropdown pos
    const yearNodeCollection = [];

    try {
      // compose related years (okay if exceed boundary)
      const yearArray = [];
      for (let i = currentYear - 5; i <= currentYear + 4; i++) {
        yearArray.push(i);
      }

      const yearNodeTempCollection = [];
      // fist push << >> into it
      yearNodeTempCollection.push(<td key={'yearLeftArrow0'} className='year-cell year-scrollup year-scroll'>{'<<'}</td>);
      // tslint:disable-next-line: max-line-length
      yearNodeTempCollection.push(<td key={'yearRightArrow0'} className='year-cell year-scrolldown year-scroll'>{'>>'}</td>);


      for (const i of yearArray) {
        // tslint:disable-next-line: max-line-length
        yearNodeTempCollection.push(<td key={'yearCell' + i} className={'year-cell ' + (this.state.tempYear === i ? 'focused-text' : '') + ((i < YEAR_RANGE.minYear || i > YEAR_RANGE.maxYear) ? 'disabled-text' : '')} data-value={i}>{i}</td>);
        // tslint:disable-next-line: max-line-length
        // yearNodeTempCollection.push(<td key={'yearCell' + i} className={'year-cell' + (this.isTempYear(i) ? ' focused-text' : '') + ((i < this.yearMin || i > this.yearMax) ? ' disabled-text' : '')} data-value={i}>{i}</td>);
      }

      // [0, 1, 2, 3] => [[0, 1], [2, 3]]
      for (let i = 0; i < yearNodeTempCollection.length; i++) {
        if (i % 2 === 0) {
          yearNodeCollection[i / 2] = [yearNodeTempCollection[i]];
        } else {
          yearNodeCollection[Math.floor(i / 2)].push(yearNodeTempCollection[i]);
        }
      }
    } catch (error) {
      console.log(error);
    }


    return yearNodeCollection.map((yearLine, index) => {
      return (
        <tr key={'yearTr' + index}>
          {/* array elements can be used directly in jsx */}
          {yearLine}
        </tr>
      );
    });
  }

  public renderDropdownMonth(): any {
    const monthNodeTempCollection = [];
    const monthNodeCollection = [];
    try {
      for (let i = 1; i <= 12; i++) {
        // tslint:disable-next-line: max-line-length
        monthNodeTempCollection.push(<td key={i} className={'month-cell ' + (this.state.tempMonth === i ? 'focused-text' : '')} data-value={i}>{this.mapNumberToChinese(i)}月</td>);
      }

      // [0, 1, 2, 3] => [[0, 1], [2, 3]]
      for (let i = 0; i < monthNodeTempCollection.length; i++) {
        if (i % 2 === 0) {
          monthNodeCollection[i / 2] = [monthNodeTempCollection[i]];
        } else {
          monthNodeCollection[Math.floor(i / 2)].push(monthNodeTempCollection[i]);
        }
      }
    } catch (error) {
      console.log(error);
    }

    return monthNodeCollection.map((monthLine, index) => {
      return (
        <tr key={index}>
          {monthLine}
        </tr>
      );
    });
  }

  public bDateDisabled(value: number, index: number, dateArr: any): boolean {
    if (index === 0) {
      if (value > 7) {
        return true;
      }
    } else if (index >= dateArr.length - 2 && index <= dateArr.length - 1) {
      if (value < 15) {
        return true;
      }
    }
    return false;
  }

  public bSelectedDate(dateVal: string, index: number, dateArr: any): boolean {

    if (+dateVal === this.state.date) {
      // if it's disabled date, return false
      if (this.bDateDisabled(+dateVal, index, dateArr)) {
        return false;
      } else {
        return true;
      }
    }
    return false;
  }

  public selectDate = (e: any) => {

    if (e.target.classList.contains('disabled-text')) {
      return null;
    }
    // if (this.state.dateBoxFormat === DateBoxFormat.HMS) {
    //   return null;
    // }
    // here shouldn't mess with selectedDate
    // just set date
    const date = +e.target.getAttribute('data-value');
    this.setState({date});
  }

  /**
   * click on year of ympage
   */
  public selectYMPage(e: any): any {
    const classList = e.target.classList;
    if (classList.contains('year-cell')) {
      if (!classList.contains('disabled-text')) {
        const tempYear = +e.target.getAttribute('data-value');
        this.setState({tempYear});
      }

    } else if (classList.contains('month-cell')) {
      if (!classList.contains('disabled-text')) {
        const tempMonth = +e.target.getAttribute('data-value');
        this.setState({tempMonth});
      }

    } else {
      //
    }
  }

  /**
   * prepare current date table to be rendered
   * the array must have 5(sometimes 6 if not enough) elements
   * each of them must have 7 sub elements (Sun -> Sat)
   */
  public prepareDateArray(currentDate: Date): string[] {
    // console.log(currentDate)
    // currentDate should be interchangeble with ISO string

    const dateArray = [];
    try {
      const year = currentDate.getFullYear();
      const month = currentDate.getMonth();
      // let firstDayThisMonthISOString = year + '-' + (month + 1) + '-' + '01';
      const thisMonthISOString = year + '-' + (month + 1) + '-';
      // get the last day of a month - month from Date(), need + 1
      const lastDay = new Date(year, month + 1, 0).getDate(); // the last of Feb -> new Date(2020, 2, 0)
      // let lastDayThisMonthISOString = year + '-' + (month + 1) + '-' + lastDay;

      // console.log(lastDay);
      // console.log(firstDayThisMonthISOString, lastDayThisMonthISOString)

      let lineDates = [];
      let dateStr = '';
      let dateObj;
      for (let i = 1; i <= lastDay; i++) {
        dateStr = (i > 9) ? (i + '') : ('0' + i);
        dateObj = new Date(thisMonthISOString + dateStr);
        lineDates.push(dateObj.getDate());
        if (dateObj.getDay() === 6 || i === lastDay) { // Saturday
          dateArray.push(lineDates);
          lineDates = [];
        }
      }

      // check the first/last element to fill in disabled elements
      const firstDateArray = dateArray[0];
      const dateArrayLen = dateArray.length;
      const lastDateArray = dateArray[dateArrayLen - 1];
      if (firstDateArray.length < 7) {
        let lastDayInLastMonth;
        lastDayInLastMonth = new Date(year, month, 0).getDate(); // (2019, 11, 31) seems = (2020, 0, 0)
        // console.log(lastDayInLastMonth);
        while (firstDateArray.length < 7) {
          firstDateArray.unshift(lastDayInLastMonth);
          lastDayInLastMonth--;
        }
      }
      if (lastDateArray.length < 7) {
        let firstDayInLastMonth = 1;
        while (lastDateArray.length < 7) {
          lastDateArray.push(firstDayInLastMonth);
          firstDayInLastMonth++;
        }
      }
      // each month should have 6 rows
      if (dateArrayLen === 5) {
        // add last row
        const lastDayInLastRow = dateArray[dateArrayLen - 1][6];
        const addedArr = [];
        let curDay = lastDayInLastRow;
        // 1. already have date of next month
        // 2. must be last day of last month
        // TODO: need to add strict condition?
        if (lastDayInLastRow > 25) {
          curDay = 0;
        }
        for (let i = 0; i < 7; i++) {
          ++curDay;
          addedArr.push(curDay);
        }
        dateArray.push(addedArr);

      } else if (dateArrayLen === 4) {
        // rare case. add two rows. eg: 1998.2
        let curDay = 0;
        for (let i = 0; i < 2; i++) {
          const addedArr = [];
          for (let j = 0; j < 7; j++) {
            ++curDay;
            addedArr.push(curDay);
          }
          dateArray.push(addedArr);
        }
      }

    } catch (error) {
      console.log(error);
    }

    // console.log(dateArray);

    return dateArray;
  }

  public handleArrowClick = (e: any) => {
    // if (this.state.dateBoxFormat === DateBoxFormat.HMS) {
    //   return null;
    // }
    const classList = e.target.classList;
    if (classList.contains('month-arrow-left')) {
      let month = this.state.month;
      let year = this.state.year;
      if (month - 1 <= 0) {
        month = 12;
        if (year - 1 >= YEAR_RANGE.minYear) {
        // if (year - 1 >= this.yearMin) {
          year--;
        }
      } else {
        month--;
      }

      // The date may be over limit of the new month!
      let date = this.state.date;
      // month saved in state, no need + 1
      const lastDay = new Date(this.state.year, month, 0).getDate(); // the last of Feb -> new Date(2020, 2, 0)
      if (date > lastDay) {
        date = lastDay;
      }

      this.setState({month, date, year, tempMonth: month, tempYear: year});
      this.closeDrowndown();
    } else if (classList.contains('month-arrow-right')) {
      let month = this.state.month;
      let year = this.state.year;
      if (month + 1 >= 13) {
        month = 1;
        if (year + 1 <= YEAR_RANGE.maxYear) {
        // if (year + 1 <= this.yearMax) {
          year++;
        }
      } else {
        month++;
      }

      // The date may be over limit of the new month!
      let date = this.state.date;
      // month saved in state, no need + 1
      const lastDay = new Date(this.state.year, month, 0).getDate(); // the last of Feb -> new Date(2020, 2, 0)
      if (date > lastDay) {
        date = lastDay;
      }

      this.setState({month, date, year, tempMonth: month, tempYear: year});
      this.closeDrowndown();

    } else if (classList.contains('year-scrollup')) {

      // if dropdownCenterYear exceeds year range, should not interact
      if (this.state.dropdownCenterYear - 5 >= YEAR_RANGE.minYear) {
      // if (this.state.dropdownCenterYear - 5 >= this.yearMin) {
        this.setState({dropdownCenterYear: this.state.dropdownCenterYear - 10, tempYear: this.state.tempYear - 10});
      }

    } else if (classList.contains('year-scrolldown')) {

      // if dropdownCenterYear exceeds year range, should not interact
      if (this.state.dropdownCenterYear + 4 <= YEAR_RANGE.maxYear) {
      // if (this.state.dropdownCenterYear + 4 <= this.yearMax) {
        this.setState({dropdownCenterYear: this.state.dropdownCenterYear + 10, tempYear: this.state.tempYear + 10});
      }

    } else if (classList.contains('ym-content')) {
      this.setState({bYMPage: true});
    } else {
      // shouldn't be here
    }
  }

  public closeDrowndown(): void {
    if (this.state.bYMPage) {
      this.setState({bYMPage: false});
    }
  }

  public clearDateBox = (bRefresh: boolean = true) => {
    if (this.isNormalDateBox() === true) {
      const { documentCore, newControlPropety } = this.props;
      documentCore.resetNewControlDateTime(newControlPropety.newControlName);
      // const newControl = documentCore.getNewControlByName(newControlPropety.newControlName);

      // newControl.resetDateBoxContent();
      // newControl.setDateTime(); // set dateTime to today;
      // newControl.clearText();
    } else {
      this.props.setStartEndDate(this.mode, '无');
    }

    if (bRefresh === true) {
      this.props.closeNewBoxList();
      this.props.refresh();
    }
  }

  public setToday = (e: any) => {
    const today = new Date();
    this.setState({year: today.getFullYear(), month: today.getMonth() + 1, date: today.getDate(),
      hour: today.getHours(), minute: today.getMinutes(), second: today.getSeconds(),
      dropdownCenterYear: today.getFullYear(), tempYear: today.getFullYear(), tempMonth: today.getMonth() + 1}, () => {

      // output text and close modal
      this.confirm();
    });
  }

  public confirm = () => {
    const { documentCore, newControlPropety } = this.props;
    const {year, month, date, tempYear, tempMonth} = this.state;
    // the process is to form selectedDate from states

    // date
    let selectedDate = year + '-' + month + '-' + date;
    if (year !== tempYear || month !== tempMonth) {
      // in ymPage and year/month selected
      selectedDate = tempYear + '-' + tempMonth + '-' + date;
      this.setState({year: tempYear, month: tempMonth});
    }

    // time
    const hour = this.state.hour;
    const minute = this.state.minute;
    const second = this.state.second;
    const milliseconds = new Date().getMilliseconds();
    selectedDate = selectedDate + ' ' + hour + ':' + minute + ':' + second + '.' + milliseconds;
    // newControl.setDateTime(selectedDate);
    // newControl.setDateBoxText();

    if (this.isNormalDateBox() === true) {
      const newControl = documentCore.getNewControlByName(newControlPropety.newControlName);
      const dateBoxFormatProp = newControl.getDateBoxFormat();
      if ( !(dateBoxFormatProp in DateBoxFormat) && newControl instanceof NewControlDate ) {
        // fail safe
        newControl.setDateBoxFormat(DateBoxFormat.DateAndHMS);
        this.setState({dateBoxFormat: DateBoxFormat.DateAndHMS});
      }

      // check if date string is within startDate/endDate range
      // if (this.checkDateStringInRange(selectedDate) === true) {
      // newControl.setDateTime(selectedDate);
      // newControl.setDateBoxText();
      selectedDate = this.normalizeValidDateString(selectedDate);
      documentCore.setNewControlDateTime(newControl.getNewControlName(), selectedDate);
      // } else {
      //   message.error('输入时间必须在“起始时间” – “截止时间”之间');
      //   this.clearDateBox(false);
      // }

    }

    // console.log(selectedDate);

    this.setState({selectedDate}, () => {
      if (this.isNormalDateBox() === false) {
        this.props.setStartEndDate(this.mode, selectedDate);
      }
      this.props.closeNewBoxList();
      this.props.refresh();
    });

  }

  public confirmYMPage(): any {
    // this.setState({bYMPage: false});
    this.setState({year: this.state.tempYear, month: this.state.tempMonth,
      dropdownCenterYear: this.state.tempYear}, () => {
      this.closeDrowndown();
    });
  }

  public cancelYMPage(): any {
    // this.setState({bYMPage: false});
    this.setState({tempYear: this.state.year, tempMonth: this.state.month,
      dropdownCenterYear: this.state.year}, () => {
      this.closeDrowndown();
    });
  }

  public timeValueChange = (e: any) => {
    const type = e.target.getAttribute('name');

    // this.setState({[var]: val}) not working??
    switch (type) {
      case 'hour':
        this.setState({hour: +e.target.value});
        break;

      case 'minute':
        this.setState({minute: +e.target.value});
        break;

      case 'second':
        this.setState({second: +e.target.value});
        break;
      default:
        break;
    }
    // this.setState({[type]: +e.target.value})
  }

  public selectText = (e: any) => {
    e.target.select();
  }

  public closeNewBoxList = () => {
    this.props.closeNewBoxList();
  }

  public render(): any {
    const {scale, className} = this.props;
    let classNameStr = '';
    if (className != null) {
      classNameStr = ' ' + className;
    }
    let left = null;
    let top = null;
    let width = 250;
    if (this.isNormalDateBox() === true) {
      // const bounds = this.props.documentCore.getNewControlsFocusBounds();
      // const lastLine = bounds.bounds[bounds.bounds.length - 1];
      // left = lastLine.x * scale;
      // top = (lastLine.y + lastLine.height + 3) * scale;
      // width = (250 > lastLine.width ? 250 : lastLine.width) * scale;
      const position = this.props.position;
      // console.log(position)
      if (position) {
        left = position.left;
        top = position.top;
        // width = position.width;
        // if (width < 250) {
        //   width = 250;
        // }
        width = 250;
      } else {
          try {
            const documentCore = this.props.documentCore;
            const bounds = documentCore.getNewControlsFocusBounds();
            const lastLine = bounds.bounds[bounds.bounds.length - 1];
            const viewMode = documentCore.getViewMode();
            let subHeight = 0;
            if (viewMode === ViewModeType.WebView) {
                const page = documentCore.getPagePositionInfo(0);
                subHeight -= page.y;
            }
            left = lastLine.x * scale;
            top = (lastLine.y + lastLine.height + /*3 +*/ subHeight) * scale;
            // width = 250 > lastLine.width ? 250 : lastLine.width;
            width = 250;
          } catch (error) {
            // tslint:disable-next-line: no-console
            console.log('datebox render catch');
            // console.log(error);
          }
      }
    }

    const bHideTimeInput = this.bHideTimeInput();
    const bHourDisabled = !bHideTimeInput && this.bHourDisabled();
    const bMinuteDisabled = !bHideTimeInput && this.bMinuteDisabled();
    const bSecondDisabled = !bHideTimeInput && this.bSecondDisabled();
    this.top = top;

    return (
      // tslint:disable-next-line: max-line-length
      <div tap-index={-1} className={'new-control-datebox-widget' + classNameStr + (this.bNoTimeInputNYMPage() ? ' no-time-widget-height-compensation' : '')} ref={this.dateDom} style={{top, left, width}}>
        <input className='focus-hidden' />
        <div className='ym-wrapper'>
          <span className='arrow-left month-arrow-left arrow-symbol'>{'<'}</span>
          <span className='ym-content'>{this.mapNumberToChinese(this.state.month)}月,  {this.state.year}</span>
          <span className='arrow-right month-arrow-right arrow-symbol'>{'>'}</span>

          {this.renderYMPage()}

        </div>
        {/* <table className={'date-wrapper ' + (dateBoxFormat === DateBoxFormat.HMS ? 'disabled-date' : '')}> */}
        <table className='date-wrapper'>
          <thead>
            <tr>
              <th>日</th>
              <th>一</th>
              <th>二</th>
              <th>三</th>
              <th>四</th>
              <th>五</th>
              <th>六</th>
            </tr>
          </thead>
          <tbody>
            {this.renderDate()}
          </tbody>
        </table>

        <div className={'time-wrapper' + (bHideTimeInput ? ' hidden' : '')}>
          {/* <div className='time-section'> */}
            <div className='description'>时间</div>
            <input tabIndex={1} type='number' name='hour' min='0' max='23' value={this.state.hour} onChange={this.timeValueChange}
              disabled={bHourDisabled ? true : false}
              className={bHourDisabled ? 'disabled-text' : ''}
              onFocus={this.selectText}
            />
            <div className='separator'>:</div>
            <input tabIndex={2} type='number' name='minute' min='0' max='59' value={this.state.minute} onChange={this.timeValueChange}
              disabled={bMinuteDisabled ? true : false}
              className={bMinuteDisabled ? 'disabled-text' : ''}
              onFocus={this.selectText}
            />
            <div className='separator'>:</div>
            <input tabIndex={3} type='number' name='second' min='0' max='59' value={this.state.second} onChange={this.timeValueChange} 
              disabled={bSecondDisabled ? true : false}
              className={bSecondDisabled ? 'disabled-text' : ''}
              onFocus={this.selectText}
            />
          {/* </div> */}
        </div>
        <div className={'button-wrapper' + (this.bNoTimeInputNYMPage() ? ' no-time-main-button-wrapper' : '')}>
          <button className='button' data-index={0}>清空</button>
          <button className='button' data-index={1}>今天</button>
          <button className='button' data-index={2}>确认</button>
        </div>

      </div>
    );
  }

  public normalizeValidDateString(dateString: string): string {
    const dateObj = new Date(dateString);
    let dateTime = dateString;

    const year: string = dateObj.getFullYear() + '';
    let month: string = dateObj.getMonth() + 1 + '';
    let date: string = dateObj.getDate() + '';
    if (dateObj.getMonth() + 1 < 10) {
      month = '0' + month;
    }
    if (dateObj.getDate() < 10) {
      date = '0' + date;
    }
    dateTime = year + '-' + month + '-' + date;

    // time
    let hour = dateObj.getHours() + '';
    let minute = dateObj.getMinutes() + '';
    let second = dateObj.getSeconds() + '';
    if (+hour >= 0 && +minute >= 0 && +second >= 0) {
      hour = (+hour < 10) ? ('0' + hour) : hour;
      minute = (+minute < 10) ? ('0' + minute) : minute;
      second = (+second < 10) ? ('0' + second) : second;
      dateTime = dateTime + ' ' + hour + ':' + minute + ':' + second;
    }

    return dateTime;
  }

  private keydown = (e: any): void => {
    const code = e.keyCode;
    let bStop: boolean = false;
    switch (code) {
        case 13: {
            if (!this.dateString) {
                this.confirm();
            } else {
                this.closeNewBoxList();
                this.props.refresh();
            }
            bStop = true;
            break;
        }
        case 9: {
            // this.close();
            if (e.target == null || e.target.getAttribute('tabindex') == null) {
              const props = this.props;
              const res = props.documentCore.jumpToNextNewControl(props.newControlPropety.newControlName);
              if (res === true) {
                  props.refresh();
              }
              bStop = true;
            }
            break;
        }
    }
    if (bStop === true) {
      e.preventDefault();
      e.stopPropagation();
    }
}

  private bNoTimeInputNYMPage(): boolean {
    // in ympage, if date widget has no time boxes, height need to compensated & main buttons moved down
    const {bYMPage, dateBoxFormat} = this.state;
    return (dateBoxFormat === DateBoxFormat.Date && bYMPage);
  }

  private bHideTimeInput(): boolean {
    let result = false;
    if (this.isNormalDateBox() === false) {
      return result;
    }

    const {dateBoxFormat} = this.state;

    if (dateBoxFormat === DateBoxFormat.Date) {
      result = true;
    } else if (dateBoxFormat === DateBoxFormat.AutoCustom) {
      const customFormat = this.getCustomFormat();
      if (customFormat != null) {
        if (customFormat.hour === '' && customFormat.minute === '' && customFormat.second === '') {
          // strictly no time inputs
          result = true;
        }
      } else {
        console.warn('customFormat is not correctly retrieved')
      }
    }
    return result;
  }

  private bHourDisabled(): boolean {
    let result = false;
    if (this.isNormalDateBox() === false) {
      return result;
    }

    // only when AutoCustom and hour set to empty
    if (this.state.dateBoxFormat === DateBoxFormat.AutoCustom) {
      const customFormat = this.getCustomFormat();
      if (customFormat != null) {
        if (customFormat.hour === '') {
          result = true;
        }
      }
    }
    return result;
  }

  private mousedownEvent = (e: any): void => {
    setTimeout(() => {
        const dom = this.dateDom.current;
        if (dom) {
          dom.querySelector('.focus-hidden').focus();
        }
    }, 200)
  }

  private bMinuteDisabled(): boolean {
    let result = false;
    if (this.isNormalDateBox() === false) {
      return result;
    }

    // only when AutoCustom and minute set to empty
    if (this.state.dateBoxFormat === DateBoxFormat.AutoCustom) {
      const customFormat = this.getCustomFormat();
      if (customFormat != null) {
        if (customFormat.minute === '') {
          result = true;
        }
      }
    }
    return result;
  }

  private bSecondDisabled(): boolean {
    let result = false;
    if (this.isNormalDateBox() === false) {
      return result;
    }

    const {dateBoxFormat} = this.state;

    // 1. DateAndHM 2. AutoCustom, second -> empty
    if (dateBoxFormat === DateBoxFormat.DateAndHM) {
      result = true;
    } else if (dateBoxFormat === DateBoxFormat.AutoCustom) {
      const customFormat = this.getCustomFormat();
      if (customFormat != null) {
        if (customFormat.second === '') {
          result = true;
        }
      }
    }
    return result;
  }

  private getCustomFormat(): ICustomFormatDateProps {
    const { documentCore, newControlPropety } = this.props;
    const newControl = documentCore.getNewControlByName(newControlPropety.newControlName);
    let customFormat = null;
    if (newControl != null) {
      try {
        customFormat = newControl.getCustomDateFormat();
      } catch (error) {
        console.log(error);
      }
    }
    return customFormat;
  }

  private setDataFromControlText(props: INewDateBoxListProps): IDataFromContrlText {
    let dateObj = new Date(); // default to be current date
    const { documentCore, newControlPropety } = props;

    let dateTime = null;
    let dateString = null;
    if (this.isNormalDateBox() === true) {
      const newControl = documentCore.getNewControlByName(newControlPropety.newControlName);
      const newControlText = newControl.getNewControlText();
      dateTime = newControl.getDateTime();
      // console.log(dateTime)
      dateString = newControlText;
      this.dateString = dateString;
    } else {
      // start/end date
      if (this.props.mode === DateBoxMode.StartDate) {
        dateTime = newControlPropety.startDate;
      } else if (this.props.mode === DateBoxMode.EndDate) {
        dateTime = newControlPropety.endDate;
      }

      // dateString always the same as dateTime?
      dateString = dateTime;
    }

    const {dateBoxFormat} = newControlPropety;

    if (!Number.isNaN(Date.parse(dateTime))) {
      dateString = dateTime;
      dateObj = new Date(dateString);
    }

    // // check if newControlText conform to date string standard
    // if (!Number.isNaN(Date.parse(newControlText))) { // conformed to date standard

    //   dateString = this.normalizeValidDateString(newControlText);
    //   dateObj = new Date(dateString);
    //   // newControl.setDateTime(dateString); // must be full string

    // } else {

    //   // HH:MM:SS?
    //   if (dateString && !Number.isNaN(Date.parse('1970-01-01 ' + dateString))) {
    //     // it is
    //     dateString = dateObj.getFullYear() + '-' + (dateObj.getMonth() + 1) + '-' + dateObj.getDate() +
    //     ' ' + dateString;
    //     dateString = this.normalizeValidDateString(dateString);
    //     dateObj = new Date(dateString);
    //     // newControl.setDateTime(dateString); // must be full string
    //   } else {
    //     // check if dateTime is available; if not, set current date(already)
    //     if (dateTime != null) {
    //       dateObj = new Date(dateTime);
    //       // same dateTime, skip set
    //     }
    //   }
    // }

    return {
      dateObj,
      dateString,
      dateBoxFormat,
    };

  }

  // private setYearRange(): void {
  //   const {startDate, endDate} = this.props.newControlPropety;
  //   this.yearMax = YEAR_RANGE.maxYear;
  //   this.yearMin = YEAR_RANGE.minYear;
  //   if (this.isNormalDateBox() === true && !Number.isNaN(Date.parse(startDate))) { // valid date string
  //     this.yearMin = new Date(startDate).getFullYear();
  //   }
  //   if (this.isNormalDateBox() === true && !Number.isNaN(Date.parse(endDate))) { // valid date string
  //     this.yearMax = new Date(endDate).getFullYear();
  //   }
  // }

  private adjustDateBoxPosition(): void {
    const dateboxElement: HTMLElement = this.dateDom.current;
    this.allowEditorMouseUp = false;

    if (dateboxElement) {
      const dateboxWidth = dateboxElement.offsetWidth;
      const dateboxHeight = dateboxElement.offsetHeight;
      const dateboxLeft = dateboxElement.offsetLeft;
      const dateboxTop = dateboxElement.offsetTop;
      // width: 350px, height: 262px/288px
      // console.log(dateboxWidth, dateboxHeight, dateboxLeft, dateboxTop)

      const curEditorDocContainer: HTMLElement = this.props.host.currentRef.current;
      if (curEditorDocContainer) {
        // tslint:disable-next-line: max-line-length
        const pageWrapperDom: HTMLElement = curEditorDocContainer.querySelector('.ReactVirtualized__Grid__innerScrollContainer .page-wrapper');
        const dropButtonDom: HTMLElement = pageWrapperDom.parentElement.querySelector('.new-control-drop-button');

        const pageWidth = pageWrapperDom.offsetWidth;
        const pageHeight = pageWrapperDom.offsetHeight;

        const dropButtonWidth = dropButtonDom.offsetWidth;
        const dropButtonLeft = dropButtonDom.offsetLeft;

        if (dateboxTop + dateboxHeight > pageHeight) {
          // height is cropped from bottom
          const newTop = pageHeight - dateboxHeight - 10;
          dateboxElement.style.top = newTop + 'px';
          if (dateboxLeft + dateboxWidth > pageWidth) {
            // width is cropped from right
            this.allowEditorMouseUp = true;
            const newLeft = dropButtonLeft - dropButtonWidth - 5 - dateboxWidth;
            dateboxElement.style.left = newLeft + 'px';
          } else {
            const newLeft = dropButtonLeft + dropButtonWidth + 1;
            dateboxElement.style.left = newLeft + 'px';
          }
        } else if (dateboxLeft + dateboxWidth > pageWidth) {
          const newLeft = dropButtonLeft + dropButtonWidth - dateboxWidth; // not on same level
          dateboxElement.style.left = newLeft + 'px';
        }
      }

      // 1. down area enough?
      // 2. right area of down arrow enough?
      // rule: position reference to the down arrow
    }

    return ;
  }

  private mapNumberToChinese(month: number): string {
    const chars = ['一', '二', '三', '四', '五', '六', '七', '八', '九', '十', '十一', '十二'];
    return chars[month - 1];
  }

  private isNormalDateBox(): boolean {
    return (this.props.mode !== DateBoxMode.StartDate) && (this.props.mode !== DateBoxMode.EndDate);
  }

  // private isTempYear(year: number): boolean {
  //   return this.state.tempYear === year && (year >= this.yearMin && year <= this.yearMax);
  // }

  // private checkDateStringInRange(selectedDate: string): boolean {
  //   const {startDate, endDate} = this.props.newControlPropety;
  //   console.log(startDate, endDate);
  //   const selectedDateParsed = Date.parse(selectedDate);
  //   const startDateParsed = Date.parse(startDate);
  //   const endDateParsed = Date.parse(endDate);

  //   if (!Number.isNaN(startDateParsed)) {
  //     if (selectedDateParsed < startDateParsed) {
  //       return false;
  //     }
  //   }

  //   if (!Number.isNaN(endDateParsed)) {
  //     if (selectedDateParsed > endDateParsed) {
  //       return false;
  //     }
  //   }

  //   return true;
  // }
}
