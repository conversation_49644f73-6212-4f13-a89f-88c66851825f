

interface IMearsureTextInfo {
    fontFamily: string;
    fontSize: number;
    height: number;
    width: number;
}

export type ToothMeasureMap = Map<string, IMearsureTextInfo>;
export type ToothMeasureInfo = {
    controlTags: ToothMeasureMap;
    tags: ToothMeasureMap;
}

interface ISvgTextInfo {
    text: string; // 文本内容
    x: number[];  // x坐标集合
    y: number[]; // y 坐标集合
}

export class ToothSvgBuilder {

    public tagFont = {
        fontFamily: '宋体',
        fontSize: 8,
        fontStyle: 'italic',
    }

    public controlFont = {
        fontFamily: '宋体',
        fontSize: 16,
        fontStyle: 'normal',
    }

    private measure: ToothMeasureInfo;

    constructor(
        private readonly tags: string[],
        private readonly controlTags: string[]    
    ) {
        this.measure = {
            controlTags: new Map(),
            tags: new Map()
        }
    }

    public setMeasure(measure: ToothMeasureInfo) {
        this.measure = measure;
    }

    public buildSvgHtml(flags: number[][], isLeft = false): {
        height: number;
        html: string;
        width: number;
    } {
        const { measure, controlTags, tagFont, controlFont } = this;
        let x = 0, y = 0;
        const controlText: ISvgTextInfo = {
            text: '',
            x: [],
            y: [],
        };
        const tagText: ISvgTextInfo = {
            text: '',
            x: [],
            y: [],
        };
        const indexs: number[] = [];
        if (isLeft) {
            for (let i = flags.length - 1; i >= 0; i--) indexs.push(i);
        } else {
            for (let i = 0, len = flags.length; i < len; i++) indexs.push(i);
        }
        for (const colIndex of indexs) {
            const subFlags = flags[colIndex];
            if (subFlags.length) {
                const char = controlTags[colIndex];
                const controlRect = measure.controlTags.get(char);
                if (!controlRect) continue;
                // control
                controlText.text += char;
                controlText.x.push(x);
                controlText.y.push(controlRect.height);

                x += controlRect.width;
                (y < controlRect.height) && (y = controlRect.height);

                // facing
                const facingRes = this.getHtmlBySubFlags(subFlags);
                tagText.text += facingRes.tagText.text;
                tagText.x.push(...facingRes.tagText.x.map(item => +item + x));
                tagText.y.push(...facingRes.tagText.y);
                x += facingRes.width;
                (y < facingRes.height) && (y = facingRes.height);
            }
        }
        // 拼凑html字符串
        let html = `<text x="${this.getStrByArr(controlText.x)}" y="${this.getStrByArr(controlText.y)}" 
            font-family="${controlFont.fontFamily}" font-size="${controlFont.fontSize}" font-style="${controlFont.fontStyle}">
            ${controlText.text}
        </text>`;
        html += `<text x="${this.getStrByArr(tagText.x)}" y="${this.getStrByArr(tagText.y)}" 
            font-family="${tagFont.fontFamily}" font-size="${tagFont.fontSize}" font-style="${tagFont.fontStyle}">
            ${tagText.text}
        </text>`;
        return {
            html,
            width: x,
            height: y
        };
    }

    private getHtmlBySubFlags(subFlags: number[]) {
        let width = 0, height = 0;
        const { measure, tags } = this;
        const tagText: ISvgTextInfo = {
            text: '',
            x: [],
            y: [],
        };
        for (let i = subFlags.length - 1; i >= 0; i--) {
            const char = tags[subFlags[i]];
            const rect = measure.tags.get(char);
            if (!rect) continue;
            tagText.text += char;
            tagText.x.push(width);
            tagText.y.push(rect.height);
            width += rect.width;
            height < rect.height && (height = rect.height);
        }
        return {
            width, height, tagText,
        };
    }

    private getStrByArr(arr: number[]): string {
        return arr.map(x => x.toFixed(2)).join(' ');
    }

}