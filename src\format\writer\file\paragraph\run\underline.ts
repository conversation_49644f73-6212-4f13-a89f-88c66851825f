import { Attributes, XmlComponent } from '../../xml-components';

export abstract class BaseUnderline extends XmlComponent {
    constructor(underlineType: string, color?: string) {
        super('w:u');
        this.root.push(
            new Attributes({
                val: underlineType,
                color,
            }),
        );
    }
}

export class Underline extends BaseUnderline {
    constructor(underlineType: string = 'single', color?: string) {
        super(underlineType, color);
    }
}

// tslint:disable-next-line: max-classes-per-file
export class DashUnderline extends BaseUnderline {
    constructor() {
        super('dash');
    }
}

// tslint:disable-next-line: max-classes-per-file
export class DashDotDotHeavyUnderline extends BaseUnderline {
    constructor() {
        super('dashDotDotHeavy');
    }
}

// tslint:disable-next-line: max-classes-per-file
export class DashDotHeavyUnderline extends BaseUnderline {
    constructor() {
        super('dashDotHeavy');
    }
}

// tslint:disable-next-line: max-classes-per-file
export class DashLongUnderline extends BaseUnderline {
    constructor() {
        super('dashLong');
    }
}

// tslint:disable-next-line: max-classes-per-file
export class DashLongHeavyUnderline extends BaseUnderline {
    constructor() {
        super('dashLongHeavy');
    }
}

// tslint:disable-next-line: max-classes-per-file
export class DotDashUnderline extends BaseUnderline {
    constructor() {
        super('dotDash');
    }
}

// tslint:disable-next-line: max-classes-per-file
export class DotDotDashUnderline extends BaseUnderline {
    constructor() {
        super('dotDotDash');
    }
}

// tslint:disable-next-line: max-classes-per-file
export class DottedUnderline extends BaseUnderline {
    constructor() {
        super('dotted');
    }
}

// tslint:disable-next-line: max-classes-per-file
export class DottedHeavyUnderline extends BaseUnderline {
    constructor() {
        super('dottedHeavy');
    }
}

// tslint:disable-next-line: max-classes-per-file
export class DoubleUnderline extends BaseUnderline {
    constructor() {
        super('double');
    }
}

// tslint:disable-next-line: max-classes-per-file
export class SingleUnderline extends BaseUnderline {
    constructor() {
        super('single');
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ThickUnderline extends BaseUnderline {
    constructor() {
        super('thick');
    }
}

// tslint:disable-next-line: max-classes-per-file
export class WaveUnderline extends BaseUnderline {
    constructor() {
        super('wave');
    }
}

// tslint:disable-next-line: max-classes-per-file
export class WavyDoubleUnderline extends BaseUnderline {
    constructor() {
        super('wavyDouble');
    }
}

// tslint:disable-next-line: max-classes-per-file
export class WavyHeavyUnderline extends BaseUnderline {
    constructor() {
        super('wavyHeavy');
    }
}

// tslint:disable-next-line: max-classes-per-file
export class WordsUnderline extends BaseUnderline {
    constructor() {
        super('words');
    }
}
