import { ParagraphReader } from './paragraph';
import Document from '../../model/core/Document';
// import {parse as circularParse, stringify as circularStringify} from 'flatted/esm';
// tslint:disable-next-line: max-line-length
import { INewControlProperty, ErrorMessages, STD_START_DEFAULT, STD_TYPE_DEFAULT, INSERT_FILE_END_EMPTY_PARA, FILE_HEADER_LENGTH, HeaderFooterType, SECTION_PROPERTIES_DEFAULT, IEditorBackground, DEFAULT_BACKGROUND, rtNode, FILE_HEADER_VERSION, FILE_HEADER_VERSION_GRACEFUL_DEGRADATION, IDocumentBuffer, IHeaderBufferItems, IModeFonts, FILE_HEADER_ENCODE_VERSION, InsertFilePositionType, REGION_MAX_DEPTH, FILE_HEADER_DESC, ResultType, ClearHeaderFooterType, OperateType, FILE_HEADER_VERSION2, IReadDocO<PERSON>, MonitorAction } from '../../common/commonDefines';
import { NewControlManager } from '../../model/core/NewControlManager';
import { NewControl } from '../../model/core/NewControl/NewControl';
// import { ParaNewControl } from '../../model/core/NewControl/NewControl';
import { PageOrientation } from '../writer/file/document/body/section-properties/page-size';
import * as JSZip from '@/common/jszip';
import Paragraph from '../../model/core/Paragraph';
import ParaPortion from '../../model/core/Paragraph/ParaPortion';
import { RegionReader } from './region';
import { TableReader } from './table';
import { logger } from '../../common/log/Logger';
import HeaderFooter from '../../model/core/HeaderFooter';
import { Region } from '../../model/core/Region';
import { TableCell } from '../../model/core/Table/TableCell';
import { DocumentContent } from '../../model/core/DocumentContent';
import { message } from '../../common/Message';
// tslint:disable-next-line: max-line-length
import { analyzeContentBuffer, analyzeContentBuffer2, analyzeDocVersion, getDefaultFont,
  IOpenFileAPIPars, 
  readerFileEditorVersionError} from '../../common/commonMethods';
// import * as txml from 'txml';
import { tXmlCustomParse, tXmlFilter } from '../../common/txml/tXml';
// import { getZstdCodec } from '@sy-editor/plugins';
import TextProperty from '../../model/core/TextProperty';
import { RevisionsManager } from '../../model/core/RevisionsManager';
import { unescapeXML } from '../../utils/xml';
import { TableBase } from '@/model/core/TableBase';

export interface IContentControlDescObj {
  newControlProperty: INewControlProperty;
  newControlManager: NewControlManager;
  unclosedNewControls: NewControl[];
  headerStructCount: number;
  strcuts?: {
    start: {[name: string]: boolean},
    end: {[name: string]: boolean}
  },
  bHeaderFooter?: boolean;
  buttons?: Map<string, any>,
}

export interface IInsertFileContent {
  content: any[];
  newControlManager: NewControlManager;
  regionManager: any;
  tableManager: any;
  drawingObjects: any;
  revisionManager: RevisionsManager;
  bRemoveRegionTitle?: boolean;
  bRemoveNewControlTitle?: boolean;
  bResetTextProperty?: boolean;
  bRemoveHiddenRegion?: boolean;
  bRemoveHiddenNewControl?: boolean;
  bRetainCascade?: boolean;
  insertEventInfos?: Map<NewControl, any>;
  insertFileAllNewControlNames?: Map<string, NewControl>;
  documentVersion: number;
  bSelectedRegionContent?: boolean;
  bMergeLinkInterface?: boolean;
  logicDocument?: Document;
  treatedContent?: any[];
  bRecalc?: boolean; // 是否需要排版
  buttons: Map<string, any>;
  reNameNewControls?: Map<string, string>;
}

export interface IUniqueImageProps {
  dataUrl: string;
  editableData: string;
  backendFlag: number;
  bind?: string;
}

export class Reader {

  private xmlDoc: any = null;
  // private testXmlDoc: any = null;
  private indexMap: any = {};
  private document: Document = null;
  private contentControlDescObj: IContentControlDescObj = null;
  private uniqueImagelist: Map<string, IUniqueImageProps> = new Map();
  private editorBackground: IEditorBackground = null;
  private defaultFont: TextProperty = null;
  // private insertParaCount: number = 0;
  private sectionEnds: any[] = [];
  private versionNum: number;
  private elements: any[];
  private headerInstance: HeaderFooter = null;
  private footerInstance: HeaderFooter = null;

  /**
   * 预处理XML文档，移除无效的cell边距值
   * @param xmlDoc XML文档字符串
   * @returns 处理后的XML文档字符串
   */
  private preprocessCellMargins(xmlDoc: string): string {
    // 使用正则表达式替换所有无效的cell边距值，支持命名空间前缀
    const invalidCellPattern = /<((?:w:)?(cellLeft|cellRight|cellTop|cellBottom))>(?:Infinity|NaN)<\/\1>/g;
    return xmlDoc.replace(invalidCellPattern, '');
  }

  constructor(document?: any) {
    if (document) {
      this.document = document;

      this.contentControlDescObj = {
        newControlProperty: {
          newControlName: null,
          newControlInfo: '', // title?
          newControlSerialNumber: STD_START_DEFAULT.serialNumber,
          newControlPlaceHolder: STD_START_DEFAULT.placeholder,
          newControlType: STD_TYPE_DEFAULT,
          isNewControlHidden: false,
          // isNewControlCanntDelete: false,
          isNewControlCanntDelete: STD_START_DEFAULT.deleteProtect === 1 ? true : false,
          // isNewControlCanntEdit: false,
          isNewControlCanntEdit: STD_START_DEFAULT.editProtect === 1 ? true : false,
          isNewControlCanntCopy: STD_START_DEFAULT.copyProtect === 1 ? true : false,
          // isNewControlMustInput: false,
          isNewControlMustInput: STD_START_DEFAULT.isMustFill === 1 ? true : false,
          // isNewControlShowBorder: true,
          isNewControlShowBorder: STD_START_DEFAULT.showBorder === 1 ? true : false,
          // isNewControlReverseEdit: false,
          isNewControlReverseEdit: STD_START_DEFAULT.editReverse === 1 ? true : false,
          // isNewControlHiddenBackground: false,
          isNewControlHiddenBackground: STD_START_DEFAULT.backgroundColorHidden === 1 ? true : false,
          newControlDisplayType: STD_START_DEFAULT.secretType,
          newControlFixedLength: 0,
          newControlMaxLength: STD_START_DEFAULT.maxLength,
          customProperty: null,
          tabJump: STD_START_DEFAULT.tabJump === 1 ? true : false,

          newControlItems: null, // todo
          prefixContent: STD_START_DEFAULT.prefixContent,
          selectPrefixContent: STD_START_DEFAULT.selectPrefixContent,
          separator: STD_START_DEFAULT.separator,
          minValue: STD_START_DEFAULT.minValue,
          maxValue: STD_START_DEFAULT.maxValue,
          precision: STD_START_DEFAULT.precision,
          unit: STD_START_DEFAULT.unit,
          forceValidate: STD_START_DEFAULT.forceValidate === 1 ? true : false,
          retrieve: STD_START_DEFAULT.retrieve === 1 ? true : false,
          isShowValue: STD_START_DEFAULT.showValue === 1 ? true : false,
          dateBoxFormat: STD_START_DEFAULT.dateBoxFormat,
          startDate: STD_START_DEFAULT.startDate,
          endDate: STD_START_DEFAULT.endDate,

          customFormat: STD_START_DEFAULT.customFormat,

          // checkbox/radiobutton
          showRight: STD_START_DEFAULT.showRight === 1 ? true : false,
          checked: STD_START_DEFAULT.checked === 1 ? true : false,
          printSelected: STD_START_DEFAULT.printSelected === 1 ? true : false,
          label: STD_START_DEFAULT.label,
          labelCode: STD_START_DEFAULT.labelCode,
          showType: STD_START_DEFAULT.showType,
          spaceNum: STD_START_DEFAULT.spaceNum,
          supportMultLines: STD_START_DEFAULT.supportMultLines === 1 ? true : false,

          // signature box
          signatureCount: STD_START_DEFAULT.signatureCount,
          preText: STD_START_DEFAULT.preText,
          signatureSeparator: STD_START_DEFAULT.signatureSeparator,
          postText: STD_START_DEFAULT.postText,
          signaturePlaceholder: STD_START_DEFAULT.signaturePlaceholder,
          signatureRatio: STD_START_DEFAULT.signatureRatio,
          rowHeightRestriction: STD_START_DEFAULT.rowHeightRestriction === 1 ? true : false,

          signType: STD_START_DEFAULT.signType,
          alwaysShow: STD_START_DEFAULT.alwaysShow,
          showSignBorder: STD_START_DEFAULT.showSignBorder === 1 ? true : false,

          // text
          hideHasTitle: STD_START_DEFAULT.hideHasTitle === 1 ? true : false,
        },
        newControlManager: document.getNewControlManager(),
        // lastNewControl: null, // pay attention to null. no ref passed. seems no way to init
        unclosedNewControls: [], // stack
        headerStructCount: 0, // header struct count
        buttons: new Map(),
      };
    }
  }

  /**
   * load uploaded file and parse into the xmlDoc
   */
  public load(file: string): boolean {
    // 预处理XML，移除无效的cell边距值
    file = this.preprocessCellMargins(file);
    
    // const parser = new DOMParser();
    // console.time('normal')
    // this.xmlDoc = parser.parseFromString(file, 'text/xml');
    // console.timeEnd('normal')
    // console.time('new')
    // console.log(file)
    // this.xmlDoc = txml.parse(file);
    this.xmlDoc = tXmlCustomParse(file);
    // check sections integrity
    if (FILE_HEADER_VERSION2 !== this.document.getDocumentVersion()) {
      this.prepareSectionEnds(this.xmlDoc);
    }

    // this.testXmlDoc = txml.parse(file);
    // console.timeEnd('new')
    // console.log(this.testXmlDoc)
    // console.log(this.xmlDoc);
    // try {
    //   if (this.xmlDoc.getElementsByTagName('parsererror')[0]) {
    //     throw new Error('parser error');
    //   }
    // } catch (error) {
    //   // alert(ErrorMessages.XmlCorrupted);
    //   message.error(ErrorMessages.XmlCorrupted);

    //   // tslint:disable-next-line: no-console
    //   console.log(error);
    //   window.dispatchEvent(new ErrorEvent('error', {message: ErrorMessages.XmlCorrupted, error}));
    //   const date = new Date();
    //   logger.error({id: this.document.id, code: error.stack,
    //     startTime: date, args: null, name: 'load.parseError'});
    //   return false;
    // }
    // this.composeIndex();
    // console.log(this.indexMap);

    // this.navigate();
    return true;
  }

  /**
   * read and parse xml
   */
  public read(file: string, bInsertFile: boolean = false, bNoEndPara: boolean = false,
              bRecalc: boolean = true, options?: any, type?: number, documentVersion?: number): boolean {

    if (file) {
      if (this.load(file) === false) {
        return false;
      }
    }

    // reset document default font
    // resetDocumentDefaultFont(this.document);

    let headFooter = null;
    if ( bInsertFile ) { // insert file
      // this.document.createNewHistoryPointForInsertFile();
      // if ( this.document.isInHeaderFooter() ) {
      //   const headFooterController = this.document.getHdrFtr();
      //   headFooter = headFooterController ? headFooterController.getCurHeaderFooter() : null;
      // }
      const bSelectedRegionContent = (options && true === options.bSelectedRegionContent);
      const insertType = ((options && bSelectedRegionContent) ? options.type : type);
      const params = {
        type: insertType,
        bNoEndPara,
        documentVersion,
        bSelectedRegionContent,
        bMergeLinkInterface: (options ? options.bMergeLinkInterface : undefined),
      };
      return this.readInsertOldFile(params);
    }

    try {
      ////////////
      // console.time('new loop')
      if (this.xmlDoc) {
        try {
          const bodyNode = this.tGetBodyNode();
          if (bodyNode != null) {
            // console.log(bodyNode)
            const bodyNodeChildren = bodyNode.children;
            if (bodyNodeChildren.length > 0) {

              try {
                if (!this.tGetSectPrNode()) {
                  // to confirm: this can be 0009?
                  throw new Error('sectPr node is not found');
                }
              } catch (error) {
                // alert(ErrorMessages.XmlCorrupted);
                message.error(ErrorMessages.XmlCorrupted);

                // tslint:disable-next-line: no-console
                // console.log(error);
                window.dispatchEvent(new ErrorEvent('error', {message: ErrorMessages.XmlCorrupted, error}));
                const date = new Date();
                logger.error({id: this.document.id, code: error.stack,
                  startTime: date, args: null, name: 'read.getSectPrNode'});
                // no return for this one?
                // return false;
              }

              let paras = []; // prepare for addPasteContent()
              for (let i = 0, len = bodyNodeChildren.length; i < len; i++) { // rootNode: same level as w:p
                const rootNode = bodyNodeChildren[i];
                if (typeof rootNode === 'object') {
                  if (rootNode.tagName === 'w:p') {
                    let container = null;
                    if (bInsertFile === true) {
                      // is this inside a region/table?
                      container = this.getCurrentContainer(this.document);
                      // console.log(container);
                    }

                    ParagraphReader.tTraverseParagraph(rootNode, this.document, this.contentControlDescObj,
                      this.uniqueImagelist, bInsertFile, paras, bNoEndPara, container, headFooter, i, bodyNodeChildren);

                  } else if (rootNode.tagName === 'rg') {
                    const depth = 1;
                    let container = null;
                    let forceIgnoreRegion = false;
                    if (bInsertFile === true) {
                      // is this inside a region/table?
                      container = this.getCurrentContainer(this.document);
                      // console.log(container);
                      // TODO: though container should never be table, better to block it?
                      if (container instanceof Region) {
                        // 插入文件时，光标处是否已存在region
                        forceIgnoreRegion = true;
                      }
                      if (this.document.isInHeaderFooter() === true) {
                        forceIgnoreRegion = true;
                      }
                    }
                    RegionReader.tTraverseRegion(rootNode, this.document, this.contentControlDescObj,
                      this.uniqueImagelist, bInsertFile, paras, bNoEndPara, container, depth, forceIgnoreRegion,
                      i, bodyNodeChildren);
                  } else if (rootNode.tagName === 'w:tbl') {
                    // table
                    TableReader.tTraverseTable(rootNode, this.document, this.contentControlDescObj,
                      this.uniqueImagelist, bInsertFile, paras, bNoEndPara, headFooter, this.document,
                      i, bodyNodeChildren);
                  } else if (rootNode.tagName === 'w:sectPr') {
                    if (!bInsertFile) {
                      this.tTraverseSectPr(rootNode, options);
                    }
                  } else {
                    // tslint:disable-next-line: no-console
                    console.warn('unknown root node detected');
                  }
                }
              }

              paras = null;

              // add background stuff if existed
              if (this.editorBackground != null) {
                this.document.setEditorBackground(this.editorBackground);
              }

              if (bRecalc === true) {
                this.document.recalculateFromStart();
              }

              // if exists structs with secret type, render accordingly
              this.checkStructsViewSecret();

              return true;
            } else {
              // tslint:disable-next-line: no-console
              console.warn('no child within body node');
            }
          } else {
            throw new Error('body node is not found');
          }
        } finally {
          // catch outside
          // this.insertParaCount = 0;
        }
      } else {
        throw new Error('xmlDoc object is empty');
      }
      // console.timeEnd('new loop')
      ///////////

    } catch (error) {
      // alert(ErrorMessages.XmlCorrupted);
      message.error(ErrorMessages.XmlCorrupted);

      // tslint:disable-next-line: no-console
      // console.log(error);
      window.dispatchEvent(new ErrorEvent('error', {message: ErrorMessages.XmlCorrupted, error}));
      const date = new Date();
      logger.error({id: this.document.id, code: error.stack,
        startTime: date, args: null, name: 'read'});
      return false;
    }

  }

  public readAllCallback(): void {
    const newControlManager = this.document.getNewControlManager();
    const newControls = newControlManager.getNameMap();
    // tslint:disable-next-line: no-console
    // console.time('setSecretType');

    // let bChanged: boolean = false;
    for (const [name, newControl] of newControls) {
      if (newControl.isViewSecret()) {
        // console.log(newControl.getViewSecretType())
        newControl.getNewControlContent()
          .setViewSecret(newControl.getViewSecretType());
      }
      // if (newControl.isHidden()) {
      //   // newControl['bHidden'] = undefined;
      //   // newControl.setHidden(true);
      //   const bSection = newControl.isNewSection();
      //   newControl['content'].setHidden(true, bSection);
      //   bChanged = bChanged || bSection;
      // // } else if (newControl.isSignatureBox()) {
      // //   bChanged = newControl.setAlwaysShow2();
      // }
    }
    // if (bChanged) {
    //   this.document.recalculate();
    // }
  }

  /**
   * read and parse xml
   */
  public read2(file: string, bInsertFile: boolean = false, bNoEndPara: boolean = false,
               bRecalc: boolean = true, modeFonts: IModeFonts = null, options?: IReadDocOption,
               type?: number, documentVersion?: number): boolean {
    if (file) {
      if (this.load(file) === false) {
        return false;
      }
    }

    // reset document default font
    // resetDocumentDefaultFont(this.document);

    let headFooter = null;
    // if (bInsertFile) { // insert file
    //   this.document.createNewHistoryPointForInsertFile();
    //   if (this.document.isInHeaderFooter()) {
    //     const headFooterController = this.document.getHdrFtr();
    //     headFooter = headFooterController ? headFooterController.getCurHeaderFooter() : null;
    //   }
    // }

    if (bInsertFile) {
      const bSelectedRegionContent = (options && true === options.bSelectedRegionContent);
      const insertType = ((options && bSelectedRegionContent) ? options.type : type);
      const params = {
        type: insertType,
        bNoEndPara,
        documentVersion,
        bRetainCascade: (options?.retainCascade == 1) || false,
        bSelectedRegionContent,
        bRecalc,
        bMergeLinkInterface: (options ? options.bMergeLinkInterface : undefined),
      };

      if (this.document.isInNISTable()) {
        if (insertType === InsertFilePositionType.NISTableCellText) {
          return this.readInsertNewFileNISCell(params);
        }
      }

      return this.readInsertNewFile(params);
    }

    try {
      ////////////
      // console.time('new loop')
      if (this.xmlDoc) {
        try {
          const bodyNode = this.tGetBodyNode();
          if (bodyNode != null) {
            // console.log(bodyNode)
            const bodyNodeChildren = bodyNode.children;
            if (bodyNodeChildren.length > 0) {

              try {
                if (!this.tGetSectPrNode()) {
                  // to confirm: this can be 0009?
                  throw new Error('sectPr node is not found');
                }
              } catch (error) {
                // alert(ErrorMessages.XmlCorrupted);
                message.error(ErrorMessages.XmlCorrupted);

                // tslint:disable-next-line: no-console
                // console.log(error);
                window.dispatchEvent(new ErrorEvent('error', { message: ErrorMessages.XmlCorrupted, error }));
                const date = new Date();
                logger.error({
                  id: this.document.id, code: error.stack,
                  startTime: date, args: null, name: 'read.getSectPrNode'
                });
                // no return for this one?
                // return false;
              }

              let paras = []; // prepare for addPasteContent()
              this.contentControlDescObj.strcuts = {start: {}, end: {}}
              const bNewVersion = FILE_HEADER_VERSION2 === documentVersion;
              
              for (let i = 0, len = bodyNodeChildren.length; i < len; i++) { // rootNode: same level as w:p
                const rootNode = bodyNodeChildren[i];
                if (typeof rootNode === 'object') {
                  if (rootNode.tagName === 'w:p') {
                    let container = null;
                    // if (bInsertFile === true) {
                    //   // is this inside a region/table?
                    //   container = this.getCurrentContainer(this.document);
                    //   // console.log(container);
                    // }

                    if (bNewVersion) {
                      ParagraphReader.tTraverseParagraphForSec(rootNode, this.document, this.contentControlDescObj,
                        this.uniqueImagelist, bInsertFile, paras, container, headFooter, modeFonts);
                    } else {
                      ParagraphReader.tTraverseParagraph2(rootNode, this.document, this.contentControlDescObj,
                        this.uniqueImagelist, bInsertFile, paras, container, headFooter,
                        modeFonts, this.sectionEnds);
                    }

                  } else if (rootNode.tagName === 'rg') {
                    let depth = 1;
                    let container = null;
                    // let forceIgnoreRegion = false;
                    // let bInsertFromRegion = false;
                    // if (bInsertFile === true) {
                    //   // is this inside a region/table?
                    //   container = this.getCurrentContainer(this.document);
                    //   // console.log(container);
                    //   // TODO: though container should never be table, better to block it?
                    //   if (container instanceof Region) {
                    //     // 插入文件时，光标处是否已存在region
                    //     bInsertFromRegion = true;
                    //     depth++;
                    //     if (container.getTopParent() instanceof Region) {
                    //       // desert depth 2+ content for now

                    //       // depth++;
                    //       // // max depth is 2, reached
                    //       // forceIgnoreRegion = true;
                    //       // tslint:disable-next-line: no-console
                    //       console.warn('region max depth reached');
                    //       continue;
                    //     }
                    //   }
                    //   if (this.document.isInHeaderFooter() === true) {
                    //     forceIgnoreRegion = true;
                    //   }
                    //   const curNewControl = this.document.getCurrentNewControl();
                    //   if (curNewControl && curNewControl.getType() === NewControlType.Section) {
                    //     forceIgnoreRegion = true;
                    //   }
                    // }
                    RegionReader.tTraverseRegion2(rootNode, this.document, this.contentControlDescObj,
                      this.uniqueImagelist, bInsertFile, paras, container, depth, false,
                      modeFonts, this.sectionEnds, false, undefined, options);
                  } else if (rootNode.tagName === 'w:tbl') {
                    // table
                    let container: any = this.document;
                    // if (bInsertFile === true) {
                    //   // is this inside a region?
                    //   container = this.getCurrentContainer(this.document);
                    //   const curNewControl = this.document.getCurrentNewControl();
                    //   // table cannot be inserted into section
                    //   if (curNewControl && curNewControl.isNewSection()) {
                    //     continue;
                    //   }
                    // }
                    TableReader.tTraverseTable2(rootNode, this.document, this.contentControlDescObj,
                      this.uniqueImagelist, bInsertFile, paras, headFooter, container,
                      modeFonts, this.sectionEnds);
                  } else if (rootNode.tagName === 'w:CTTbl'/*'w:NIStbl'*/) {
                    // NIStable
                    let container: any = this.document;
                    if (bInsertFile === true) {
                      // is this inside a region?
                      container = this.getCurrentContainer(this.document);
                      const curNewControl = this.document.getCurrentNewControl();
                      // table cannot be inserted into section
                      if (curNewControl && curNewControl.isNewSection()) {
                        continue;
                      }
                    }

                    let rows;
                    if (options?.nisTableJson) {
                      for (let index = 0, length = options.nisTableJson.length; index < length; index++) {
                        const element = options.nisTableJson[index];
                        const tableName = element['tableID'];

                        if (tableName === rootNode.attributes['name']) {
                          rows = element['rows'];
                          break;
                        }
                      }
                    }
                    TableReader.tTraverseNISTable(rootNode, this.document, this.contentControlDescObj,
                      this.uniqueImagelist, bInsertFile, paras, headFooter, container,
                      modeFonts, this.sectionEnds, rows);
                  } else if (rootNode.tagName === 'w:sectPr') {
                    if (!bInsertFile) {
                      this.tTraverseSectPr(rootNode, options);
                    }
                  } else {
                    // tslint:disable-next-line: no-console
                    console.warn('unknown root node detected');
                  }
                }
              }

              // if (bInsertFile) {
              //   if (!bNoEndPara) {
              //     for (let i = 0; i < INSERT_FILE_END_EMPTY_PARA + 1; i++) {
              //       paras.push(new Paragraph(this.document, this.document));
              //     }
              //   }
              //   this.document.addPasteContent(paras, false);
              // }
              paras = null;

              this.document.getNumManager()
              .initDoc();

              // add background stuff if existed
              if (this.editorBackground != null) {
                this.document.setEditorBackground(this.editorBackground);
              }

              if (bRecalc === true) {
                // this.document.recalculateFromStart();

                // pastenewcontrols[] => newcontrols[]
                // if (bInsertFile === true) {
                //   const newControlManager = this.document.getNewControlManager();
                //   const curPara = this.document.getCurrentParagraph();
                //   const pos = curPara.getCurContentPosInDoc(false, false);
                //   newControlManager.insertAllPasteNewControl(curPara.parent, pos);
                // } else {
                  this.document.getNewControlManager()
                  .initOpenDocument();
                // }
              }

              // if exists structs with secret type, render accordingly
              this.checkStructsViewSecret();

              return true;
            } else {
              // tslint:disable-next-line: no-console
              console.warn('no child within body node');
            }
          } else {
            throw new Error('body node is not found');
          }
        } finally {
          // catch outside
          // this.insertParaCount = 0;
        }
      } else {
        throw new Error('xmlDoc object is empty');
      }
      // console.timeEnd('new loop')
      ///////////

    } catch (error) {
      // alert(ErrorMessages.XmlCorrupted);
      message.error(ErrorMessages.XmlCorrupted);

      // tslint:disable-next-line: no-console
      // console.log(error);
      window.dispatchEvent(new ErrorEvent('error', { message: ErrorMessages.XmlCorrupted, error }));
      const date = new Date();
      logger.error({
        id: this.document.id, code: error.stack,
        startTime: date, args: null, name: 'read'
      });
      return false;
    }

  }

  private setTableGrid(table: TableBase): void {
    if (!table || !table.isTable() || table.tableGrid?.length) {
      return;
    }
    const row = table.content[0];
    let maxCell = 0;
    row.content.forEach((cell) => {
      maxCell += cell.property.gridSpan;
    });
    table.tableGrid = [];
    for (let index = 0; index < maxCell; index++) {
      table.tableGrid.push(700);
    }
  }

  /**
   * read txt file
   */
  public readFromTxt(file: string, bInsertFile: boolean = false): boolean {
    if (!file) {
      return false;
    }
    let initIndex = 0;

    let paras = []; // prepare for addPasteContent()
    for (let i = 0; i < file.length; i++) {
      let para: Paragraph = null;

      if (file[i] === '\n') {
        let endIndex = i;
        if (file[i - 1] === '\r') {
          // windows txt is \r\n
          endIndex = i - 1;
        }
        const texts = file.slice(initIndex, endIndex);
        para = new Paragraph(this.document, this.document);
        // add portion
        this.prepareParaFromTxt(para, texts);

        initIndex = i + 1;
      }
      if (i === file.length - 1 && initIndex < file.length) {
        const texts = file.slice(initIndex, i);
        para = new Paragraph(this.document, this.document);
        // add portion
        this.prepareParaFromTxt(para, texts);

      }
      if (para) {
        if (!bInsertFile) {
          this.document.addToContent(this.document.content.length, para);
        } else {
          paras.push(para);
        }
      }
    }

    if (bInsertFile) {
      for (let i = 0; i < INSERT_FILE_END_EMPTY_PARA + 1; i++) {
        paras.push(new Paragraph(this.document, this.document));
      }
      // console.log(paras)
      this.document.addPasteContent(paras);
    } else {
        this.document.recalculateFromStart();
        this.document.setDirty(false);
    }

    paras = null;

    return true;
  }

  public readFromTxtStream(content: Blob, bInsertFile: boolean = false, options: any = {}): Promise<boolean> {
    let date = new Date();
    // const newZip = new JSZip();
    // const reader = this;
    const fileReader = new FileReader();
    options.time = new Date().getTime() - date.getTime();
    return new Promise((resolve) => {
        fileReader.readAsText(content);
        fileReader.onloadend = () => {
            date = new Date();
            const rawText = fileReader.result as string;
            // separate rawBuffer to get contentBuffer
            const result = this.readFromTxt(rawText, bInsertFile);
            // console.log(versionNum, documentNum)
            options.time += new Date().getTime() - date.getTime();
            resolve(result);
        };
    });
  }

  public readMedia(file?: string, uniqueImagelist: Map<string, IUniqueImageProps> = null): boolean {
    // console.log(file)
    if (!file) {
      return false;
    }
    try {
      // const parser = new DOMParser();
      // const mediaXML = parser.parseFromString(file, 'text/xml');
      // const mediaJson = txml.parse(file);
      const mediaJson = tXmlCustomParse(file);

      // let a0 = performance.now();
      // const images = mediaXML.getElementsByTagName('Image');
      // const paintings = mediaXML.getElementsByTagName('Painting');
      // let a1 = performance.now();
      // console.log(a1 - a0)
      // console.log(images)
      // console.log(paintings)

      const media = mediaJson[1]; // 0
      const tImages: rtNode[] = [];
      const tPaintings: rtNode[] = [];
      // let t0 = performance.now();
      // let t1 = 0;
      if (typeof media === 'object') {
        // (string | number | txml.tNode). so ts would know it's tNode in compile time
        const mediaChildren = media.children;
        if (mediaChildren instanceof Array && mediaChildren.length <= 2) { // may exist old files!

          // images / paintings
          // it seems declaration is wrong for txml children
          const imagesNode: rtNode = mediaChildren[0] as unknown as rtNode;
          const paintingsNode: rtNode = mediaChildren[1] as unknown as rtNode;
          const images = imagesNode.children;
          // t1 = performance.now();
          for (const image of images) {
            if (typeof image === 'object') {
              tImages.push(image);
            }
          }
          if (paintingsNode != null && paintingsNode.children.length > 0) {
            const paintings = paintingsNode.children;
            for (const painting of paintings) {
              if (typeof painting === 'object') {
                tPaintings.push(painting);
              }
            }
          }

        }
      }
      // let t2 = performance.now();
      // console.log(t1-t0)
      // console.log(t2-t1)
      // console.log(t2-t0)
      // console.log(tImages)
      // console.log(tPaintings)

      // console.time('normal loop')
      // // tslint:disable-next-line: prefer-for-of
      // for (let i = 0; i < images.length; i++) {
      //   let source = null;
      //   const content: IUniqueImageProps = {
      //     dataUrl: null,
      //     editableData: null
      //   };

      //   const currentImage = images[i];
      //   // source
      //   if (currentImage.attributes && currentImage.attributes.length > 0) {
      //     const imageName = currentImage.attributes.getNamedItem('name');
      //     if (imageName) {
      //       source = imageName.nodeValue;
      //     }
      //   }
      //   // content
      //   const contentNode = currentImage.childNodes[0];
      //   if (contentNode && contentNode.childNodes[0]) {
      //     content.dataUrl = contentNode.childNodes[0].nodeValue;
      //   }

      //   // paintings?
      //   if (paintings.length > 0) {
      //     for (let j = 0, len = paintings.length; j < len; j++) {
      //       const currentPainting = paintings[j];
      //       if (currentPainting.attributes && currentPainting.attributes.length > 0) {
      //         const paintingName = currentPainting.attributes.getNamedItem('name');
      //         if (paintingName && paintingName.nodeValue === source) { // check if same name
      //           // set 'equationElem'!
      //           // console.log(paintingName)
      //           const paintingContentNode = currentPainting.childNodes[0];
      //           if (paintingContentNode && paintingContentNode.childNodes[0]) {
      //             content.editableData = paintingContentNode.childNodes[0].nodeValue;
      //           }
      //         }
      //       }
      //     }
      //     // console.log(content);
      //   }

      //   try {
      //     if (source && content) {
      //       // console.log(source)
      //       // console.log(content)
      //       this.uniqueImagelist.set(source, content);
      //     } else {
      //       // Images in Media.xml should all be used. Cannot think of a scenario to be in here.
      //       // But even so, the whole steps are not compromised. Totally tolerable
      //       throw new Error('Images in media.xml exists null source/content');
      //     }
      //   } catch (error) {
      //     // silent. not a big deal
      //     window.dispatchEvent(new ErrorEvent('error', {message: ErrorMessages.XmlError, error}));
      //   }

      // } // for
      // console.timeEnd('normal loop')

      // console.time('json loop')
      for (const tImage of tImages) {
        let source = null;
        const content: IUniqueImageProps = {
          dataUrl: null,
          editableData: '',
          backendFlag: null,
        };

        const currentImage: rtNode = tImage as unknown as rtNode;
        // source && backendflag
        const currentImageAttrs = currentImage.attributes;
        if (currentImageAttrs && Object.keys(currentImageAttrs).length > 0) {
          const imageName = currentImageAttrs['name'];
          const backendFlag = +currentImageAttrs['backendFlag'];
          if (imageName) {
            source = imageName;
          }
          if (backendFlag != null) {
            content.backendFlag = backendFlag;
          }
          if (currentImageAttrs['bind']) {
            content.bind = unescapeXML(currentImageAttrs['bind']);
          }
        }
        // content
        const contentNode = currentImage.children[0];
        if (typeof contentNode === 'object') {
          if (contentNode && contentNode.tagName === 'content' && contentNode.children.length > 0 ) {
            const dataURL = contentNode.children[0];
            content.dataUrl = (typeof dataURL === 'string') ? dataURL : null;
          }
        }

        // paintings?
        if (tPaintings.length > 0) {
          for (let k = 0, len = tPaintings.length; k < len; k++) {
            const currentPainting: rtNode = tPaintings[k] as unknown as rtNode;
            const currentPaintingAttrs = currentPainting.attributes;
            if (currentPaintingAttrs && Object.keys(currentPaintingAttrs).length > 0) {
              const paintingName = currentPaintingAttrs['name'];
              if (paintingName && paintingName === source) {
                // set 'equationElem'!
                // console.log(paintingName)
                const paintingContentNode = currentPainting.children[0];
                if (typeof paintingContentNode === 'object') {
                  if (paintingContentNode && paintingContentNode.tagName === 'content' &&
                    paintingContentNode.children.length > 0) {
                    let editableData = paintingContentNode.children[0];
                    if (typeof editableData === 'string') {
                      // let t1 = performance.now();
                      // TODO: replace &quotes better solution?
                      editableData = editableData.replace(/&quot;/g, '"');
                      // let t2 = performance.now();
                      content.editableData = editableData;
                    }
                  }
                }
              }
            }
          }
        //   // console.log(content);
        }

        try {
          if (source && content) {
            // console.log(source)
            // console.log(content)
            (uniqueImagelist || this.uniqueImagelist).set(source, content);
          } else {
            // Images in Media.xml should all be used. Cannot think of a scenario to be in here.
            // But even so, the whole steps are not compromised. Totally tolerable
            throw new Error('Images in media.xml exists null source/content');
          }
        } catch (error) {
          // silent. not a big deal
          window.dispatchEvent(new ErrorEvent('error', {message: ErrorMessages.XmlError, error}));
        }

      }
      // console.timeEnd('json loop')
      // console.log(t2-t1)
      // console.log(this.uniqueImagelist)

    } catch (error) {
      // alert(ErrorMessages.XmlCorrupted);
      message.error(ErrorMessages.XmlCorrupted);

      // tslint:disable-next-line: no-console
      // console.log(error);
      window.dispatchEvent(new ErrorEvent('error', {message: ErrorMessages.XmlCorrupted, error}));
      const date = new Date();
      logger.error({id: this.document.id, code: error.stack,
        startTime: date, args: null, name: 'readMedia'});
      return false;
    }

    return true;
  }

    public readCascade(file?: string): boolean {
        if (!file) {
            return false;
        }
        try {
            const cascadeJson = tXmlCustomParse(file);
            const cascade = cascadeJson[1]; // 0
            if (typeof cascade === 'object') {
                const cascadeChildren = cascade.children;
                if (cascadeChildren instanceof Array && cascadeChildren.length <= 2) { // may exist old files!
                    for (const child of cascadeChildren) {
                        if (child.tagName === 'w:cascadeInfo' && child.children
                            && child.children.length > 0) {
                            const children: string = child.children[0] as any;
                            try {
                                this.document.initOpenCascades(JSON.parse(children
                                    .replace(/&amp;/g, '&')
                                    .replace(/&lt;/g, '<')
                                    .replace(/&gt;/g, '>')
                                    .replace(/&quot;/g, '"')
                                    .replace(/&apos;/g, "'")));
                            } catch (error) {
                                console.log(error);
                            }
                        }
                    }
                }
            }

        } catch (error) {
            // alert(ErrorMessages.XmlCorrupted);
            message.error(ErrorMessages.XmlCorrupted);

            // tslint:disable-next-line: no-console
            // console.log(error);
            window.dispatchEvent(new ErrorEvent('error', { message: ErrorMessages.XmlCorrupted, error }));
            const date = new Date();
            logger.error({
                id: this.document.id, code: error.stack,
                startTime: date, args: null, name: 'readCascade'
            });
            return false;
        }
        return true;
    }

  public readHeader(file?: string, bInsertFile: boolean = false, bNoEndPara: boolean = false): boolean {
    if (!file) {
      return false;
    }

    try {
      // const parser = new DOMParser();
      // const headerXML = parser.parseFromString(file, 'text/xml');
      // const hdr = headerXML.getElementsByTagName('w:hdr')[0];
      // const headerXML = txml.parse(file);
      const headerXML = tXmlCustomParse(file);

      const hdr = headerXML[1] as rtNode;

      const hdrChildren = hdr.children;
      if (hdrChildren.length <= 0) {
        // empty header
        return true;
      }

      let paras = []; // prepare for addPasteContent()
      for ( let i = 0, len = hdrChildren.length; i < len; i++) {
        const node = hdrChildren[i];
        if (typeof node === 'object') {
          if (node.tagName === 'w:p') {
            // console.log(this.contentControlDescObj)
            const headerFooterInstance = this.getHeaderFooterInstance(HeaderFooterType.Header);

            ParagraphReader.tTraverseParagraph(node, this.document, this.contentControlDescObj,
              this.uniqueImagelist, bInsertFile, paras, bNoEndPara, null, headerFooterInstance, i, hdrChildren);

          } else if (node.tagName === 'rg') { // shouldn't have region
            // tslint:disable-next-line: no-console
            console.warn('region should not exist in headerfooter');

          } else if (node.tagName === 'w:tbl') {
            const headerFooterInstance = this.getHeaderFooterInstance(HeaderFooterType.Header);
            // table
            TableReader.tTraverseTable(node, this.document, this.contentControlDescObj,
              this.uniqueImagelist, bInsertFile, paras, bNoEndPara, headerFooterInstance, null,
              i, hdrChildren);

          }
        }
      }
      paras = null;

    } catch (error) {
      // alert(ErrorMessages.XmlCorrupted);
      message.error(ErrorMessages.XmlCorrupted);

      // tslint:disable-next-line: no-console
      // console.log(error);
      window.dispatchEvent(new ErrorEvent('error', {message: ErrorMessages.XmlCorrupted, error}));
      const date = new Date();
      logger.error({id: this.document.id, code: error.stack,
        startTime: date, args: null, name: 'readHeader'});
      return false;
    }

    // seems as long as mainDoc is recalced, this recalc can delay to footer
    // TODO: find a way to avoid this recalc
    this.document.recalculateFromStart();
    return true;
  }

  public readHeader2(file?: string, bInsertFile: boolean = false, bNoEndPara: boolean = false): boolean {
    if (!file) {
      return false;
    }

    try {
      // 预处理XML，移除无效的cell边距值
      file = this.preprocessCellMargins(file);

      // const parser = new DOMParser();
      // const headerXML = parser.parseFromString(file, 'text/xml');
      // const hdr = headerXML.getElementsByTagName('w:hdr')[0];
      // const headerXML = txml.parse(file);
      const headerXML = tXmlCustomParse(file);

      const hdr = headerXML[1] as rtNode;

      const hdrChildren = hdr.children;
      if (hdrChildren.length <= 0) {
        // empty header
        return true;
      }

      const bNewVersion = FILE_HEADER_VERSION2 === (this.versionNum || this.document.getDocumentVersion());
      // check sections integrity
      if (!bNewVersion) {
        this.prepareSectionEnds(headerXML);
      }

      let paras = []; // prepare for addPasteContent()
      for ( let i = 0, len = hdrChildren.length; i < len; i++) {
        const node = hdrChildren[i];
        if (typeof node === 'object') {
          if (node.tagName === 'w:p') {
            // console.log(this.contentControlDescObj)
            const headerFooterInstance = this.getHeaderFooterInstance(HeaderFooterType.Header);

            if (bNewVersion) {
              ParagraphReader.tTraverseParagraphForSec(node, this.document, this.contentControlDescObj,
                this.uniqueImagelist, bInsertFile, paras, null, headerFooterInstance);
            } else {
              ParagraphReader.tTraverseParagraph2(node, this.document, this.contentControlDescObj,
                this.uniqueImagelist, bInsertFile, paras, null, headerFooterInstance, null,
                this.sectionEnds);
            }

          } else if (node.tagName === 'rg') { // shouldn't have region
            // tslint:disable-next-line: no-console
            console.warn('region should not exist in headerfooter');

          } else if (node.tagName === 'w:tbl') {
            const headerFooterInstance = this.getHeaderFooterInstance(HeaderFooterType.Header);
            // table
            TableReader.tTraverseTable2(node, this.document, this.contentControlDescObj,
              this.uniqueImagelist, bInsertFile, paras, headerFooterInstance, null,
              null, this.sectionEnds);

          }
        }
      }
      if (this.versionNum != null) {
        this.elements = paras;
      }
    } catch (error) {
      // alert(ErrorMessages.XmlCorrupted);
      message.error(ErrorMessages.XmlCorrupted);

      // tslint:disable-next-line: no-console
      // console.log(error);
      window.dispatchEvent(new ErrorEvent('error', {message: ErrorMessages.XmlCorrupted, error}));
      const date = new Date();
      logger.error({id: this.document.id, code: error.stack,
        startTime: date, args: null, name: 'readHeader'});
      return false;
    }

    // seems as long as mainDoc is recalced, this recalc can delay to footer
    // TODO: find a way to avoid this recalc
    // this.document.recalculateFromStart();
    return true;
  }

  public readFooter(file?: string, bInsertFile: boolean = false, bNoEndPara: boolean = false): boolean {
    if (!file) {
      return false;
    }

    try {
      // const parser = new DOMParser();
      // const headerXML = parser.parseFromString(file, 'text/xml');
      // const hdr = headerXML.getElementsByTagName('w:ftr')[0];

      // const footerXML = txml.parse(file);
      const footerXML = tXmlCustomParse(file);

      const ftr = footerXML[1] as rtNode;

      const ftrChildren = ftr.children;
      if (ftrChildren.length <= 0) {
        // empty header
        return true;
      }

      let paras = []; // prepare for addPasteContent()
      for (let i = 0, len = ftrChildren.length; i < len; i++) {
        const node = ftrChildren[i];
        if (typeof node === 'object') {
          if (node.tagName === 'w:p') {
            // console.log(this.contentControlDescObj)
            const headerFooterInstance = this.getHeaderFooterInstance(HeaderFooterType.Footer);

            ParagraphReader.tTraverseParagraph(node, this.document, this.contentControlDescObj,
              this.uniqueImagelist, bInsertFile, paras, bNoEndPara, null, headerFooterInstance, i, ftrChildren);

          } else if (node.tagName === 'rg') { // shouldn't have region
            // tslint:disable-next-line: no-console
            console.warn('region should not exist in headerfooter');

          } else if (node.tagName === 'w:tbl') {
            // table
            const headerFooterInstance = this.getHeaderFooterInstance(HeaderFooterType.Footer);
            TableReader.tTraverseTable(node, this.document, this.contentControlDescObj,
              this.uniqueImagelist, bInsertFile, paras, bNoEndPara, headerFooterInstance, null, i, ftrChildren);

          }
        }
      }
      paras = null;

    } catch (error) {
      // alert(ErrorMessages.XmlCorrupted);
      message.error(ErrorMessages.XmlCorrupted);

      // tslint:disable-next-line: no-console
      // console.log(error);
      window.dispatchEvent(new ErrorEvent('error', {message: ErrorMessages.XmlCorrupted, error}));
      const date = new Date();
      logger.error({id: this.document.id, code: error.stack,
        startTime: date, args: null, name: 'readFooter'});
      return false;
    }

    this.document.recalculateFromStart();
    return true;
  }

  public readFooter2(file?: string, bInsertFile: boolean = false, bNoEndPara: boolean = false): boolean {
    if (!file) {
      return false;
    }

    try {
      // 预处理XML，移除无效的cell边距值
      file = this.preprocessCellMargins(file);
      // const parser = new DOMParser();
      // const headerXML = parser.parseFromString(file, 'text/xml');
      // const hdr = headerXML.getElementsByTagName('w:ftr')[0];

      // const footerXML = txml.parse(file);
      const footerXML = tXmlCustomParse(file);

      const ftr = footerXML[1] as rtNode;

      const ftrChildren = ftr.children;
      if (ftrChildren.length <= 0) {
        // empty header
        return true;
      }

      const bNewVersion = FILE_HEADER_VERSION2 === (this.versionNum || this.document.getDocumentVersion());
      if (!bNewVersion) {
        this.prepareSectionEnds(footerXML);
      }

      let paras = []; // prepare for addPasteContent()
      for (let i = 0, len = ftrChildren.length; i < len; i++) {
        const node = ftrChildren[i];
        if (typeof node === 'object') {
          if (node.tagName === 'w:p') {
            // console.log(this.contentControlDescObj)
            const headerFooterInstance = this.getHeaderFooterInstance(HeaderFooterType.Footer);

            if (bNewVersion) {
              ParagraphReader.tTraverseParagraphForSec(node, this.document, this.contentControlDescObj,
                this.uniqueImagelist, bInsertFile, paras, null, headerFooterInstance);
            } else {
              ParagraphReader.tTraverseParagraph2(node, this.document, this.contentControlDescObj,
                this.uniqueImagelist, bInsertFile, paras, null, headerFooterInstance, null,
                this.sectionEnds);
            }

          } else if (node.tagName === 'rg') { // shouldn't have region
            // tslint:disable-next-line: no-console
            console.warn('region should not exist in headerfooter');

          } else if (node.tagName === 'w:tbl') {
            // table
            const headerFooterInstance = this.getHeaderFooterInstance(HeaderFooterType.Footer);
            TableReader.tTraverseTable2(node, this.document, this.contentControlDescObj,
              this.uniqueImagelist, bInsertFile, paras, headerFooterInstance, null, null,
              this.sectionEnds);

          }
        }
      }
      paras = null;

    } catch (error) {
      // alert(ErrorMessages.XmlCorrupted);
      message.error(ErrorMessages.XmlCorrupted);

      // tslint:disable-next-line: no-console
      // console.log(error);
      window.dispatchEvent(new ErrorEvent('error', {message: ErrorMessages.XmlCorrupted, error}));
      const date = new Date();
      logger.error({id: this.document.id, code: error.stack,
        startTime: date, args: null, name: 'readFooter'});
      return false;
    }

    // this.document.recalculateFromStart();
    return true;
  }

  public readSettings(file?: string, bInsertFile: boolean = false, map?: Map<string, string>): boolean {
    if (!file) {
      return false;
    }

    try {
      // const parser = new DOMParser();
      // const settingsXML = parser.parseFromString(file, 'text/xml');
      // const protectModeNode = settingsXML.getElementsByTagName('ProtectMode')[0];
      // const testSettingsXml = txml.parse(file);
      const testSettingsXml = tXmlCustomParse(file);

      const settingsNode = testSettingsXml[1] as rtNode;
      // let protectMode = false;
      // in info
      const properties = map;
      if (typeof settingsNode === 'object') {
        for (const rootNode of settingsNode.children) {
          if (typeof rootNode === 'object' && rootNode.tagName === 'Properties') {
            if (!properties) {
              continue;
            }
            for (const infoChild of rootNode.children) {
              if (typeof infoChild === 'object' && infoChild.tagName) {
                const value = infoChild.children[0] as string;
                properties.set(infoChild.tagName, (value ? unescapeXML(value) : value));
              }
            }
          } else if (typeof rootNode === 'object' && rootNode.tagName === 'SaveHistory') {
            const docSavedInfo = {
              createdBy: null,
              createdTime: null,
              lastButOne: null,
              lastSavedT: null,
            };
            for (const history of rootNode.children) {
              if (typeof history === 'object') {
                const historyValue = history.children[0];
                switch (history.tagName) {
                  case 'CreatedBy': {
                    docSavedInfo['createdBy'] = historyValue;
                    break;
                  }
                  case 'CreatedTime': {
                    docSavedInfo['createdTime'] = historyValue;
                    break;
                  }
                  case 'LastButOne': {
                    docSavedInfo['lastButOne'] = historyValue;
                    break;
                  }
                  case 'LastSavedT': {
                    docSavedInfo['lastSavedT'] = historyValue;
                    break;
                  }
                  default:
                    break;
                }
              }
            }
            this.document.setDocSavedInfo(docSavedInfo);
          }
        }
      }

      // console.log(testSettingsXml)

      // insert file should not apply protect mode
      // if (bInsertFile === false && protectModeNode && protectModeNode.childNodes[0]) {
      //   const protectMode = protectModeNode.childNodes[0].nodeValue === '1' ? true : false ;
      //   if (protectMode === true) {
      //     this.document.setDocProtect(protectMode);
      //   }
      // }
      // if (bInsertFile === false && protectMode) {
      //   if (protectMode === true) {
      //     this.document.setDocProtect(protectMode);
      //   }
      // }

    } catch (error) {
      // alert(ErrorMessages.XmlError);
      window.dispatchEvent(new ErrorEvent('error', {message: ErrorMessages.XmlError, error}));
      // tslint:disable-next-line: no-console
      console.warn(ErrorMessages.XmlError);
      const date = new Date();
      logger.error({id: this.document.id, code: error.stack,
      startTime: date, args: null, name: 'readSettings'});
      return false;
    }

    return true;
  }

  public readStyles(file?: string, bInsertFile: boolean = false): boolean {
    if (!file) {
      return false;
    }
    // console.log(file)

    try {
      // const parser = new DOMParser();
      // const stylesXML = parser.parseFromString(file, 'text/xml');
      // const stylesXML = txml.parse(file);
      const stylesXML = tXmlCustomParse(file);

      // const stylesXML = parser.parseFromString(file, 'text/xml');
      const editorBackground: IEditorBackground = {
        watermarkEnabled: DEFAULT_BACKGROUND.watermarkEnabled,
        watermarkType: DEFAULT_BACKGROUND.watermarkType,
        watermarkText: JSON.parse(JSON.stringify(DEFAULT_BACKGROUND.watermarkText)), // obj separation
        fillColorEnabled: DEFAULT_BACKGROUND.fillColorEnabled,
        fillColorType: DEFAULT_BACKGROUND.fillColorType,
        fillTextColor: DEFAULT_BACKGROUND.fillTextColor,
      };

      let watermark: rtNode = null;
      let fillColor: rtNode = null;
      const settingsNode = stylesXML[1] as rtNode;
      let rPrDefaultNode: rtNode = null;
      let pPrDefaultNode: rtNode = null;
      let bDynamicLine = false;
      for (const rootNode of settingsNode.children) {
        if (typeof rootNode === 'object') {
          if (rootNode.tagName === 'w:background') {
            for (const watermarkNode of rootNode.children) {
              if (typeof watermarkNode === 'object') {
                if (watermarkNode.tagName === 'w:watermark') {
                  watermark = watermarkNode;
                } else if (watermarkNode.tagName === 'w:fillColor') {
                  fillColor = watermarkNode;
                }
              }
            }
            // break;
          } else if (rootNode.tagName === 'w:docDefaults') {
            // rpr and ppr
            for (const defaultNode of rootNode.children) {
              if (typeof defaultNode === 'object') {
                if (defaultNode.tagName === 'w:rPrDefault') {
                  rPrDefaultNode = defaultNode;
                } else if (defaultNode.tagName === 'w:pPrDefault') {
                  pPrDefaultNode = defaultNode;
                }
              }
            }
          } else if (rootNode.tagName === 'w:dynamicGridLine') {
            bDynamicLine = +rootNode.attributes['w:val'] ? true : false;
            break;
          }
        }
      }

      // defaultfonts
      if (rPrDefaultNode != null) {
        for (const rPr of rPrDefaultNode.children) {
          if (typeof rPr === 'object') {
            if (rPr.tagName === 'w:rPr') {
              const defaultFont = new TextProperty();
              for (const rPrItem of rPr.children) {
                if (typeof rPrItem === 'object') {
                  let attrs = null;
                  switch (rPrItem.tagName) {
                    case 'w:sz': {
                      attrs = rPrItem.attributes;
                      // console.log(attrs) // {w:val: "18"}
                      const sizeVal = attrs['w:val'];
                      if (sizeVal != null) {
                        defaultFont.fontSize = +sizeVal;
                      }
                      break;
                    }

                    case 'w:rFonts': {
                      attrs = rPrItem.attributes;
                      const fontVal = attrs['w:eastAsia'];
                      if (fontVal != null) {
                        defaultFont.fontFamily = fontVal;
                      }
                      break;
                    }

                    default: {
                      break;
                    }
                  }
                }
              }
              // add to prop
              this.defaultFont = defaultFont;
            }
          }
        }
      }

      // insert file should not apply background
      if (bInsertFile === false) {
        this.document.setDynamicGridLine(bDynamicLine);

        if (watermark != null) {
          const watermarkAttr = watermark.attributes;
          const enabled = watermarkAttr['w:enabled'];
          const type = watermarkAttr['w:type'];
          const textColor = watermarkAttr['w:textColor'];
          // const text = watermark.children[0] as string;
          const wmChildren = watermark.children;
          // console.log(enabled, type, text);
          if (enabled != null) {
            editorBackground.watermarkEnabled = (enabled === '1') ? true : false;
          }
          if (type != null) {
            editorBackground.watermarkType = +type;
          }
          // console.log(textColor)
          if (textColor != null) {
            editorBackground.fillTextColor = +textColor;
          }
          // if (text != null) {
          //   // editorBackground.watermarkText = text;
          //   editorBackground.watermarkText.push({text, size: WATERMARK_DEFAULT_FONTSIZE + ''});
          // }
          if (wmChildren != null && wmChildren.length > 0) {
            const firstChild: any = wmChildren[0];
            if (firstChild != null) {
              if (firstChild.tagName === 'w:watermarkItem') {

                // new two line watermark

                this.readWaterMarkItem(firstChild, editorBackground, 0);

                const secondChild: rtNode = wmChildren[1] as rtNode;
                // if there exists 2nd child, must be watermarkitem
                if (secondChild != null && secondChild.tagName === 'w:watermarkItem') {
                  this.readWaterMarkItem(secondChild, editorBackground, 1);
                }

              } else {
                // old one line watermark
                editorBackground.watermarkText[0].text = firstChild;
              }
            }
          }
        }
        if (fillColor != null) {
          const fillColorAttr = fillColor.attributes;
          const enabled = fillColorAttr['w:enabled'];
          const type = fillColorAttr['w:type'];
          // console.log(enabled, type);
          if (enabled != null) {
            editorBackground.fillColorEnabled = (enabled === '1') ? true : false;
          }
          if (type != null) {
            editorBackground.fillColorType = +type;
          }
        }

        this.editorBackground = editorBackground;
        // this.document.setEditorBackground(editorBackground);
      }

    } catch (error) {
      // tslint:disable-next-line: no-console
      // console.log(error);
      // alert(ErrorMessages.XmlError);
      window.dispatchEvent(new ErrorEvent('error', {message: ErrorMessages.XmlError, error}));
      // tslint:disable-next-line: no-console
      console.warn(ErrorMessages.XmlError);
      const date = new Date();
      logger.error({id: this.document.id, code: error.stack,
      startTime: date, args: null, name: 'readStyles'});
      return false;
    }

    return true;
  }

  /**
   * read and render from .apo's string - API
   */
  public readFromString(rawBuffer: Uint8Array, bInsertFile: boolean = false,
                        bNoEndPara: boolean = false, bRecalc: boolean = true,
                        type?: InsertFilePositionType): Promise<number> {
    // synced up with pageMenu - readZstFile()
    // const newZip = new JSZip();
    // const reader = this;
    const fileReader = new FileReader();

    return new Promise((resolve) => {
      // separate rawBuffer to get contentBuffer
      const {headerBuffer, contentBuffer}: IDocumentBuffer = this.separateRawBuffer(rawBuffer);
      const {versionNum, documentNum} = this.getHeaderBufferItems(headerBuffer);
      if (versionNum === FILE_HEADER_VERSION_GRACEFUL_DEGRADATION) {
        if (documentNum != null) {
          this.document.setFileVersion(documentNum);
          this.document.setDocumentVersion(versionNum);
        }
        const props = {bInsertFile, bNoEndPara, bRecalc, options: null, type, documentVersion: versionNum};
        analyzeContentBuffer(null, contentBuffer, fileReader, this, props)
        .then((res) => {
          resolve(res);
        });
      } else if (versionNum === FILE_HEADER_VERSION || versionNum === FILE_HEADER_ENCODE_VERSION
        || versionNum === FILE_HEADER_VERSION2) {
        // new sdt structure
        if (documentNum != null) {
          this.document.setFileVersion(documentNum);
          this.document.setDocumentVersion(versionNum);
        }
        const props = {bInsertFile, bNoEndPara, bRecalc, options: null, type, documentVersion: versionNum};
        analyzeContentBuffer2(null, contentBuffer, fileReader, this, props)
        .then((res) => {
          resolve(res);
        });
      } else {
        readerFileEditorVersionError(this.document);
        // const zstdCodec = zstdCodecPack.ZstdCodec;
        // getZstdCodec().then((zstdCodec) => {
        //   return zstdCodec.run( (zstd) => {
        //     if (documentNum != null && versionNum != null) {
        //       this.document.setFileVersion(documentNum);
        //       this.document.setDocumentVersion(versionNum);
        //     }
        //     const props = {bInsertFile, bNoEndPara, bRecalc, options: null, type, documentVersion: versionNum};
        //     analyzeContentBuffer(zstd, contentBuffer, fileReader, this, props)
        //     .then((res) => {
        //       resolve(res);
        //     });
        //   }); // zstdCodec.run
        // })
        // .catch((error) => {
        //   if (documentNum != null) {
        //     this.document.setFileVersion(documentNum);
        //   } else {
        //     this.document.setFileVersion(1);
        //   }
        //   this.document.setDocumentVersion(FILE_HEADER_VERSION2);
        //   const props = {bInsertFile, bNoEndPara, bRecalc, options: null, type, documentVersion: FILE_HEADER_VERSION2};
        //   analyzeContentBuffer2(null, contentBuffer, fileReader, this, props)
        //     .then((result) => {
        //       resolve(result);
        //   });
        // })
        // .catch((error) => {
        //   // tslint:disable-next-line: no-console
        //   console.log(error, documentNum);
        // });
      }

    });
  }

  public readFromString2(rawBuffer: Uint8Array, params: IOpenFileAPIPars): Promise<number> {
    // synced up with pageMenu - readZstFile()
    // const newZip = new JSZip();
    // const reader = this;

    return new Promise((resolve) => {
      // separate rawBuffer to get contentBuffer
      // const {headerBuffer, contentBuffer}: IDocumentBuffer = this.separateRawBuffer(new Uint8Array(rawBuffer));
      // const {versionNum, documentNum} = this.getHeaderBufferItems(headerBuffer);
      // console.log(versionNum, documentNum)

      analyzeDocVersion(
        {rawBuffer, reader: this}, params
      )
      .then((result) => {
        resolve(result);
      });

    });
  }

  public readWaterMarkItem(waterMarkItem: rtNode, editorBackground: IEditorBackground, index: number): void {
    // const watermarkItem: IWaterMarkText = {text: '', size: WATERMARK_DEFAULT_FONTSIZE + ''};
    const watermarkItem = editorBackground.watermarkText[index];
    const waterMarkItemChildren = waterMarkItem.children as rtNode[];

    if (waterMarkItemChildren != null && waterMarkItemChildren.length > 0) {
      if (waterMarkItemChildren[0] != null && waterMarkItemChildren[0].tagName === 'text') {
        // console.log(waterMarkItemChildren[0].children[0])
        watermarkItem.text = waterMarkItemChildren[0].children[0] as string;
      }
      if (waterMarkItemChildren[1].tagName === 'size') {
        // console.log(waterMarkItemChildren[1].children[0])
        watermarkItem.size = waterMarkItemChildren[1].children[0] as string;
      }
    }

  }

  public replaceHeader(content: Blob, options: any = {}): Promise<number> {
    const newZip = new JSZip();
    return new Promise((resolve, reject) => {
      newZip.loadAsync(content)
      .then(async (zip) => {
        const headerFile = await zip.file('Header1.xml')
        .async('string');
        if (!headerFile) {
          resolve(ResultType.Failure);
        }
        const mediaFile = await zip.file('Media.xml')
                                      .async('string');
        this.readMedia(mediaFile);
        const document = this.document;
        const arrayBuffer = await content.arrayBuffer();
        const {headerBuffer}: IDocumentBuffer = this.separateRawBuffer(new Uint8Array(arrayBuffer));
        const {versionNum} = this.getHeaderBufferItems(headerBuffer);
        this.versionNum = versionNum;
        const bDelete = document.clearHeaderFooterContent(ClearHeaderFooterType.Header) === ResultType.Success;
        this.contentControlDescObj.bHeaderFooter = true;
        const res = this.readHeader2(headerFile);
        this.versionNum = null;
        if (res === true || bDelete) {
          document.resetSectionInfo();
          document.recalculateAllForce();
          document.updateCursorXY();
        }
        resolve(ResultType.Success);
      });
    });
  }

  public replaceFooter(content: Blob, options: any = {}): Promise<number> {
    const newZip = new JSZip();
    return new Promise((resolve, reject) => {
      newZip.loadAsync(content)
      .then(async (zip) => {
        const headerFile = await zip.file('Footer.xml')
        .async('string');
        if (!headerFile) {
          resolve(ResultType.Failure);
        }
        const mediaFile = await zip.file('Media.xml')
                                      .async('string');
        this.readMedia(mediaFile);
        const document = this.document;
        const arrayBuffer = await content.arrayBuffer();
        const {headerBuffer}: IDocumentBuffer = this.separateRawBuffer(new Uint8Array(arrayBuffer));
        const {versionNum} = this.getHeaderBufferItems(headerBuffer);
        this.versionNum = versionNum;
        const bDelete = document.clearHeaderFooterContent(ClearHeaderFooterType.Footer) === ResultType.Success;
        const res = this.readFooter2(headerFile);
        this.versionNum = null;
        if (res === true || bDelete) {
          document.resetSectionInfo();
          document.recalculateAllForce();
          document.updateCursorXY();
        }
        resolve(ResultType.Success);
      });
    });
  }

  /**
   * read and render from .apo's string - API
   */
  public readFromStream(content: Blob, bInsertFile: boolean = false,
                        bNoEndPara: boolean = false, bRecalc: boolean = true, options: any = {},
                        modeFonts: IModeFonts = null, type?: InsertFilePositionType): Promise<number> {
    // synced up with pageMenu - readZstFile()
    let date = new Date();
    // const newZip = new JSZip();
    // const reader = this;
    const fileReader = new FileReader();
    options.time = new Date().getTime() - date.getTime();
    return new Promise((resolve) => {

      fileReader.readAsArrayBuffer(content);
      fileReader.onloadend = () => {
        date = new Date();
        const rawBuffer = fileReader.result as ArrayBuffer;
        // separate rawBuffer to get contentBuffer
        const {headerBuffer, contentBuffer}: IDocumentBuffer = this.separateRawBuffer(new Uint8Array(rawBuffer));
        const {versionNum, documentNum} = this.getHeaderBufferItems(headerBuffer);
        // console.log(versionNum, documentNum)
        options.time += new Date().getTime() - date.getTime();
        if (versionNum === FILE_HEADER_VERSION_GRACEFUL_DEGRADATION) {
          if (documentNum != null) {
            this.setFileVersionWithOperate(options, documentNum);
            this.document.setDocumentVersion(versionNum);
          }
          const props = {bInsertFile, bNoEndPara, bRecalc, options, type, documentVersion: versionNum};
          analyzeContentBuffer(null, contentBuffer, fileReader, this, props)
          .then((res) => {
            resolve(res);
          });
        } else if (versionNum === FILE_HEADER_VERSION || versionNum === FILE_HEADER_ENCODE_VERSION) {
          // new sdt structure
          if (documentNum != null) {
            this.setFileVersionWithOperate(options, documentNum);
            this.document.setDocumentVersion(versionNum);
          }
          const props = {bInsertFile, bNoEndPara, bRecalc, options, type, documentVersion: versionNum};
          analyzeContentBuffer2(null, contentBuffer, fileReader, this, props, false, modeFonts)
          .then((res) => {
            resolve(res);
          });
        } else {
          console.log("zstd-format is not supported.")
        }
      };

    });
  }

  public readFromStream2(content: Blob, params: IOpenFileAPIPars): Promise<number> {
    // synced up with pageMenu - readZstFile()
    let date = new Date();
    // const newZip = new JSZip();
    // const reader = this;
    const { options } = params;
    options.time = new Date().getTime() - date.getTime();
    return new Promise((resolve) => {
      const fileReader = new FileReader();
      fileReader.readAsArrayBuffer(content);
      fileReader.onloadend = () => {
        date = new Date();
        const rawBuffer = fileReader.result as ArrayBuffer;
        // separate rawBuffer to get contentBuffer
        // const {headerBuffer, contentBuffer}: IDocumentBuffer = this.separateRawBuffer(new Uint8Array(rawBuffer));
        // const {versionNum, documentNum} = this.getHeaderBufferItems(headerBuffer);
        // console.log(versionNum, documentNum)
        options.time += new Date().getTime() - date.getTime();

        analyzeDocVersion(
          {rawBuffer: new Uint8Array(rawBuffer), reader: this}, params
        )
        .then((result) => {
          resolve(result);
        });
      };

    });
  }

  // public getHeaderFooterInstance(type: HeaderFooterType): HeaderFooter {
  //   const defaultHeaderFooter = (type === HeaderFooterType.Header) ?
  //     this.document.hdrFtr.pages[0].header : this.document.hdrFtr.pages[0].footer;
  //   let headerFooterInstance = null;
  //   if ( defaultHeaderFooter == null) {
  //     headerFooterInstance = (type === HeaderFooterType.Header) ?
  //       this.document.createSectionHeaderFooter(HeaderFooterType.Header, 0) :
  //       this.document.createSectionHeaderFooter(HeaderFooterType.Footer, 0);
  //     this.document.hdrFtr.setCurHeaderFooter(headerFooterInstance);
  //     if (type === HeaderFooterType.Header) {
  //       this.document.hdrFtr.pages[0].header = headerFooterInstance;
  //     } else {
  //       this.document.hdrFtr.pages[0].footer = headerFooterInstance;
  //     }

  //   } else {
  //     headerFooterInstance = defaultHeaderFooter;
  //   }
  //   return headerFooterInstance;
  // }

  public getHeaderFooterInstance(type: HeaderFooterType): HeaderFooter {
    let headerFooterInstance = null;
    if (HeaderFooterType.Header === type) {
      if (!this.headerInstance) {
        this.headerInstance = (type === HeaderFooterType.Header) ?
          this.document.createSectionHeaderFooter(HeaderFooterType.Header, 0) :
          this.document.createSectionHeaderFooter(HeaderFooterType.Footer, 0);
        this.document.hdrFtr.setCurHeaderFooter(this.headerInstance);
        this.headerInstance.content.content = [];
      }
      headerFooterInstance = this.headerInstance;
    } else {
      if (!this.footerInstance) {
        this.footerInstance = this.document.createSectionHeaderFooter(HeaderFooterType.Footer, 0);
        this.footerInstance.content.content = [];
        this.document.hdrFtr.setCurHeaderFooter(this.footerInstance);
      }
      headerFooterInstance = this.footerInstance;
    }

    return headerFooterInstance;
  }

  public recalculate(): Promise<any> {
      const commentManager = this.document.getCommentManager();
      commentManager.clearUnValidCommentsInReading();
      const res = this.document.recalculatePageForFileReader();
      commentManager.buildCommentPageCaches();
      return res;
  }

  public getFileXMLFromString(rawBuffer: Uint8Array): Promise<string> {
    const newZip = new JSZip();
    const reader = this;
    const fileReader = new FileReader();

    return new Promise((resolve) => {
      // separate rawBuffer to get contentBuffer
      const {headerBuffer, contentBuffer}: IDocumentBuffer = this.separateRawBuffer(rawBuffer);
      const {versionNum} = this.getHeaderBufferItems(headerBuffer);
      let newBlob = null;
      const newFileVersions = [FILE_HEADER_VERSION, FILE_HEADER_VERSION_GRACEFUL_DEGRADATION,
        FILE_HEADER_ENCODE_VERSION, FILE_HEADER_VERSION2];
      if (newFileVersions.includes(versionNum) === true) {
        // After buffer separation, type can be original. No need version number
        newBlob = new Blob([contentBuffer], {type: 'application/apollo-zip'});
        // unzip the zipped blob
        // tslint:disable-next-line: newline-per-chained-call
        newZip.loadAsync(newBlob).then((zip) => {
          let documentZippedFile = null;
          try {
            documentZippedFile = newZip.file('Document.xml');
            if (!documentZippedFile) {
              throw new Error('Document.xml missing');
            }
          } catch (error) {
            // tslint:disable-next-line: no-console
            console.log(error);
            return ;
          }

          // tslint:disable-next-line: newline-per-chained-call
          documentZippedFile.async('blob').then((unZippedDocumentFile: any) => {
            fileReader.readAsText(unZippedDocumentFile);
            fileReader.onloadend = () => {
              const documentFile: string = fileReader.result as string;
              resolve(documentFile);
            };
          });

        }) // newzip loadasync
        .catch((error) => {
          // tslint:disable-next-line: no-console
          console.log(error);
          return;
        });
      } else {
        readerFileEditorVersionError(this.document);
        // const zstdCodec = zstdCodecPack.ZstdCodec;
        // getZstdCodec().then((zstdCodec) => {
        //   return zstdCodec.run( (zstd) => {
        //     const streaming = new zstd.Streaming();

        //     try {
        //       const deCompressed = streaming.decompress(contentBuffer);

        //       // After buffer separation, type can be original. No need version number
        //       newBlob = new Blob([deCompressed.buffer], {type: 'application/apollo-zip'});
        //     } catch (error) {
        //       // tslint:disable-next-line: no-console
        //       console.log(error);
        //       return;
        //     }

        //     // unzip the zipped blob
        //     // tslint:disable-next-line: newline-per-chained-call
        //     newZip.loadAsync(newBlob).then((zip) => {
        //       let documentZippedFile = null;
        //       try {
        //         documentZippedFile = newZip.file('Document.xml');
        //         if (!documentZippedFile) {
        //           throw new Error('Document.xml missing');
        //         }
        //       } catch (error) {
        //         // tslint:disable-next-line: no-console
        //         console.log(error);
        //         return ;
        //       }

        //       // tslint:disable-next-line: newline-per-chained-call
        //       documentZippedFile.async('blob').then((unZippedDocumentFile: any) => {
        //         fileReader.readAsText(unZippedDocumentFile);
        //         fileReader.onloadend = () => {
        //           const documentFile: string = fileReader.result as string;
        //           resolve(documentFile);
        //         };
        //       });

        //     }) // newzip loadasync
        //     .catch((error) => {
        //       // tslint:disable-next-line: no-console
        //       console.log(error);
        //       return;
        //     });

        //   });
        // });
        // zstdCodec.run
      }

    });
  }

  public getFileXMLFromBlob(content: Blob, names?: string[]): Promise<{fileXML: string[], versionNum}> {
    const newZip = new JSZip();
    const reader = this;
    const fileReader = new FileReader();

    return new Promise((resolve) => {

      fileReader.readAsArrayBuffer(content);
      fileReader.onloadend = () => {
        const rawBuffer = fileReader.result as ArrayBuffer;
        // separate rawBuffer to get contentBuffer
        const {headerBuffer, contentBuffer}: IDocumentBuffer = this.separateRawBuffer(new Uint8Array(rawBuffer));
        const {versionNum} = this.getHeaderBufferItems(headerBuffer);
        let newBlob = null;
        const newFileVersions = [FILE_HEADER_VERSION, FILE_HEADER_VERSION_GRACEFUL_DEGRADATION,
          FILE_HEADER_ENCODE_VERSION, FILE_HEADER_VERSION2];
        if (newFileVersions.includes(versionNum) === true) {
          newBlob = new Blob([contentBuffer], {type: 'application/apollo-zip'});
          // unzip the zipped blob
          // tslint:disable-next-line: newline-per-chained-call
          newZip.loadAsync(newBlob).then((zip) => {
            (async () => {
              const result = [];
              for (let index = 0, len = names.length; index < len; index++) {
                const xmlName = names[index];
                const xml = await this.getXmlByAsync(fileReader, xmlName, zip);
                result.push(xml);
              }
              resolve({fileXML: result, versionNum});
            })();

            // Promise.all(promises).then((results) => {
            //   resolve(results);
            // })
          }) // newzip loadasync
          .catch((error) => {
            // tslint:disable-next-line: no-console
            console.log(error);
            return;
          });
        } else {
          console.log("zstd-format is not supported.")
        }

      };

    });
  }

  /**
   * compose root index map for body node
   */
  public composeIndex(): void {
    if (this.xmlDoc) {
      const body = this.xmlDoc.getElementsByTagName('w:body')[0];
      // let obj = {};
      // let node = body.childNodes[0];
      let node = body.children[0];
      // console.log(node);
      while (node) {
        // console.log(node);
        // if (node.nodeName !== '#text') {
        this.indexMap[Object.keys(this.indexMap).length + 1] = node;
        // }
        // node = node.nextSibling;
        node = node.nextElementSibling;
      }
      // console.log(this.indexMap);
      // return this.indexMap;
    }
  }

  public navigate(): void {
    if (this.xmlDoc) {
      // console.log(this.xmlDoc.getElementsByTagName("w:p"));
      const paras = this.getParagraphs();
      // console.log(paras);
      const paraProperty = this.getParaProperty(paras[0]);
      // console.log(paraProperty);
      const paraText = this.getParaText(paras[0]);
      // console.log(paraText);
      const runs = this.getRuns(paras[0]);
      // console.log(runs);
      const runProperty = this.getRunProperty(runs[0]);
      // console.log(runProperty);
      const runText = this.getRunText(runs[1]);
      // console.log(runText);
    }
  }

  public getBodyNode(): any {
    if (this.xmlDoc) {
      return this.xmlDoc.getElementsByTagName('w:body')[0];
    }
    return null;
  }

  public tGetBodyNode(): rtNode {
    let body = null;
    if (this.xmlDoc) {
      const documentNode = this.xmlDoc[1] as unknown as rtNode;
      if (documentNode.tagName === 'w:document') {
        const bodyNode = documentNode.children[0];
        if (typeof bodyNode === 'object') {
          if (bodyNode.tagName === 'w:body') {
            body = bodyNode;
          }
        }
      }
    }
    return body;
  }

  public getSectPrNode(): any {
    if (this.xmlDoc) {
      return this.xmlDoc.getElementsByTagName('w:sectPr')[0];
    }
    return null;
  }

  public tGetSectPrNode(): rtNode {
    let sectPrNode = null;
    const bodyNode = this.tGetBodyNode();
    const bodyNodeChildren = bodyNode.children;
    // usu. the last node
    const lastRootNode = bodyNodeChildren[bodyNodeChildren.length - 1];
    if (typeof lastRootNode === 'object') {
      if (lastRootNode.tagName === 'w:sectPr') {
        sectPrNode = lastRootNode;
      }
    }
    // console.log(sectPrNode)
    return sectPrNode;
  }

  /**
   * get xml dom object
   */
  public getXmlDoc(): any {
    return this.xmlDoc;
  }

  public getDocument(): Document {
    return this.document;
  }

  /**
   * get paragraph collection
   */
  public getParagraphs(): any {
    return this.xmlDoc.getElementsByTagName('w:p');
  }

  /**
   * get properties of a paragraph, return as json
   */
  public getParaProperty(paragraph: any): any {

    if (paragraph != null) {

      // console.log(paragraph);
      const propNode = paragraph.getElementsByTagName('w:pPr')[0];
      const nodes =  propNode ? propNode.children : [];
      const paraProperty = [];
      for (const node of nodes) {
        // console.log(nodes[i])
        // if (node.nodeName !== '#text') {
          // need to simplify value structure?
        paraProperty.push(node);
        // }
      }

      return paraProperty;

    }
    return null;
  }

  /**
   * get combined text of the paragraph
   */
  public getParaText(paragraph: any): any {

    if (paragraph != null) {
      const textNodes = paragraph.getElementsByTagName('w:t');
      let text = '';
      for (const textNode of textNodes) {
        text += textNode.childNodes[0].nodeValue;
      }
      // console.log(textNodes)

      return text;
    }
    return null;
  }

  /**
   * Given a certain paragraph node, return all runs within it
   */
  public getRuns(paragraph: any): any {

    if (paragraph != null) {
      // contains text nodes
      // console.log(paragraph.childNodes);
      let nodes = [];
      // if (paragraph.childNodes.length > 0) {
      //   nodes = paragraph.childNodes;
      // }
      const paraChildren = paragraph.children;
      if (paraChildren.length > 0) {
        nodes = paraChildren;
      }

      const runsArray = [];
      for (const node of nodes) {
        if (node.nodeName === 'w:r') {
          // console.log(nodes[i]);
          runsArray.push(node);
        }
      }

      return runsArray;
    }
    return null;

  }

  /**
   * get properties of a run, return as json
   */
  public getRunProperty(run: any): any {

    if (run != null) {
      const runPropNode = run.getElementsByTagName('w:rPr')[0];
      const nodes =  runPropNode ? runPropNode.children : [];
      const runProperty = [];

      for (const node of nodes) {
        // if (node.nodeName !== '#text') {
          // need to simplify value structure?
          runProperty.push(node);
        // }
      }

      return runProperty;
    }

    return null;
  }

  /**
   * get text of the run
   */
  public getRunText(run: any): any {

    if (run != null) {
      const textNodes = run.getElementsByTagName('w:t');
      let text = '';
      for (const textNode of textNodes) {
        text += textNode.childNodes[0].nodeValue;
      }
      // console.log(textNodes)

      return text;
    }

    return null;
  }

  public traverseSectPr(node: any): void {
    // const paraChildren = node.childNodes;
    const paraChildren = node.children;
    for (const paraChild of paraChildren) {

      // if (paraChild.nodeName !== '#text') {
      // console.log(paraChild)
      const paraChildAttr = paraChild.attributes;
      if (paraChild.nodeName === 'w:pgSz') {
        let width = 0;
        let height = 0;
        let orient = PageOrientation.PORTRAIT;
        if (paraChildAttr) {
          if (paraChildAttr.getNamedItem('w:w')) {
            width = paraChildAttr.getNamedItem('w:w').nodeValue;
          }
          if (paraChildAttr.getNamedItem('w:h')) {
            height = paraChildAttr.getNamedItem('w:h').nodeValue;
          }
          if (paraChildAttr.getNamedItem('w:orient')) {
            orient = paraChildAttr.getNamedItem('w:orient').nodeValue;
          }
          this.document.sectionProperty.setPageSize(+width, +height);
        }

      } else if (paraChild.nodeName === 'w:pgMar') {
        let top = 0;
        let right = 0;
        let bottom = 0;
        let left = 0;
        let header = SECTION_PROPERTIES_DEFAULT.header;
        let footer = SECTION_PROPERTIES_DEFAULT.footer;
        if (paraChildAttr) {
          if (paraChildAttr.getNamedItem('w:top')) {
            top = paraChildAttr.getNamedItem('w:top').nodeValue;
          }
          if (paraChildAttr.getNamedItem('w:right')) {
            right = paraChildAttr.getNamedItem('w:right').nodeValue;
          }
          if (paraChildAttr.getNamedItem('w:bottom')) {
            bottom = paraChildAttr.getNamedItem('w:bottom').nodeValue;
          }
          if (paraChildAttr.getNamedItem('w:left')) {
            left = paraChildAttr.getNamedItem('w:left').nodeValue;
          }
          if (paraChildAttr.getNamedItem('w:header')) {
            header = +paraChildAttr.getNamedItem('w:header').nodeValue;
            if (header === 708) { // old apo compatible improvise
              header = SECTION_PROPERTIES_DEFAULT.header;
            }
            this.document.sectionProperty.setPageMarginsHeader(header);
          }
          if (paraChildAttr.getNamedItem('w:footer')) {
            footer = +paraChildAttr.getNamedItem('w:footer').nodeValue;
            if (footer === 708) { // old apo compatible improvise
              footer = SECTION_PROPERTIES_DEFAULT.footer;
            }
            this.document.sectionProperty.setPageMarginsFooter(footer);
          }
          // if (paraChildAttr.getNamedItem('w:gutter')) {
          //   // console.log(paraChildAttr.getNamedItem('w:gutter').nodeValue);
          // }
          this.document.sectionProperty.setPageMargins(+left, +top, +right, +bottom);
        }
      } else if (paraChild.nodeName === 'w:headerfooter') {
        let bShowHeader = true;
        let bShowFooter = true;
        let bProtectHeaderFooter = false;
        let bShowHeaderBorder = true;
        let bShowFooterBorder = false;
        if (paraChildAttr) {
          if (paraChildAttr.getNamedItem('w:showHeader')) {
            bShowHeader = paraChildAttr.getNamedItem('w:showHeader').nodeValue === '1' ? true : false;
          }
          if (paraChildAttr.getNamedItem('w:showFooter')) {
            bShowFooter = paraChildAttr.getNamedItem('w:showFooter').nodeValue === '1' ? true : false;
          }
          if (paraChildAttr.getNamedItem('w:protect')) {
            bProtectHeaderFooter = paraChildAttr.getNamedItem('w:protect').nodeValue === '1' ? true : false;
          }
          if (paraChildAttr.getNamedItem('w:showHeaderBorder')) {
            bShowHeaderBorder = paraChildAttr.getNamedItem('w:showHeaderBorder').nodeValue === '1' ? true : false;
          }
          if (paraChildAttr.getNamedItem('w:showFooterBorder')) {
            bShowFooterBorder = paraChildAttr.getNamedItem('w:showFooterBorder').nodeValue === '1' ? true : false;
          }
          this.document.setShowHeader(bShowHeader);
          this.document.setShowFooter(bShowFooter);
          this.document.setProtectHeaderFooter(bProtectHeaderFooter);
          this.document.setShowHeaderBorder(bShowHeaderBorder);
          this.document.setShowFooterBorder(bShowFooterBorder);
        }
      }
      // }
    }
  }

  public tTraverseSectPr(node: rtNode, options?: any): void {
    const paraChildren = node.children;
    for (const paraChild of paraChildren) {

      // if (paraChild.nodeName !== '#text') {
      // console.log(paraChild)
      if (typeof paraChild === 'object') {
        const paraChildAttr = paraChild.attributes;
        if (paraChild.tagName === 'w:pgSz') {
          let width = 0;
          let height = 0;
          let orient = PageOrientation.PORTRAIT;
          if (paraChildAttr) {
            if (paraChildAttr['w:w']) {
              width = paraChildAttr['w:w'];
            }
            if (paraChildAttr['w:h']) {
              height = paraChildAttr['w:h'];
            }
            if (paraChildAttr['w:orient']) {
              orient = paraChildAttr['w:orient'];
            }
            this.document.sectionProperty.setPageSize(+width, +height);
          }

        } else if (paraChild.tagName === 'w:pgMar') {
          let top = 0;
          let right = 0;
          let bottom = 0;
          let left = 0;
          let header = SECTION_PROPERTIES_DEFAULT.header;
          let footer = SECTION_PROPERTIES_DEFAULT.footer;
          if (paraChildAttr) {
            if (paraChildAttr['w:top']) {
              top = paraChildAttr['w:top'];
            }
            if (paraChildAttr['w:right']) {
              right = paraChildAttr['w:right'];
            }
            if (paraChildAttr['w:bottom']) {
              bottom = paraChildAttr['w:bottom'];
            }
            if (paraChildAttr['w:left']) {
              left = paraChildAttr['w:left'];
            }
            if (paraChildAttr['w:header']) {
              header = +paraChildAttr['w:header'];
              if (header === 708) { // old apo compatible improvise
                header = SECTION_PROPERTIES_DEFAULT.header;
              }
              this.document.sectionProperty.setPageMarginsHeader(header);
            }
            if (paraChildAttr['w:footer']) {
              footer = +paraChildAttr['w:footer'];
              if (footer === 708) { // old apo compatible improvise
                footer = SECTION_PROPERTIES_DEFAULT.footer;
              }
              this.document.sectionProperty.setPageMarginsFooter(footer);
            }
            // if (paraChildAttr.getNamedItem('w:gutter')) {
            //   // console.log(paraChildAttr.getNamedItem('w:gutter').nodeValue);
            // }
            this.document.sectionProperty.setPageMargins(+left, +top, +right, +bottom);
          }
        } else if (paraChild.tagName === 'w:headerfooter') {
          let bShowHeader = true;
          let bShowFooter = true;
          let bProtectHeaderFooter = false;
          let bShowHeaderBorder = true;
          let bShowFooterBorder = false;
          if (paraChildAttr) {
            if (paraChildAttr['w:showHeader']) {
              bShowHeader = paraChildAttr['w:showHeader'] === '1' ? true : false;
            }
            if (paraChildAttr['w:showFooter']) {
              bShowFooter = paraChildAttr['w:showFooter'] === '1' ? true : false;
            }
            if (paraChildAttr['w:protect']) {
              bProtectHeaderFooter = paraChildAttr['w:protect'] === '1' ? true : false;
            }
            if (paraChildAttr['w:showHeaderBorder']) {
              bShowHeaderBorder = paraChildAttr['w:showHeaderBorder'] === '1' ? true : false;
            }
            if (paraChildAttr['w:showFooterBorder']) {
              bShowFooterBorder = paraChildAttr['w:showFooterBorder'] === '1' ? true : false;
            }
            this.document.setShowHeader(bShowHeader);
            this.document.setShowFooter(bShowFooter);
            this.document.setProtectHeaderFooter(bProtectHeaderFooter);
            this.document.setShowHeaderBorder(bShowHeaderBorder);
            this.document.setShowFooterBorder(bShowFooterBorder);
          }
        }
      }

      // }
    }

    // set pageSize from openDocumentWithStream(sModJson)
    if (options != null && options.pageProperty != null) {
      const customPageProps = options.pageProperty;
      // console.log(customPageProps)
      this.document.sectionProperty.setPageSize(customPageProps.width, customPageProps.height);
    }
  }

  /**
   * separate raw buffer into contentbuffer and headerbuffer
   */
  public separateRawBuffer(rawBuffer: Uint8Array): IDocumentBuffer {
    if (rawBuffer) {
      // let headerBuffer = null;
      // let contentBuffer = null;
      // compatible with old apo
      // const testArray = Array.from(new Uint8Array(rawBuffer));
      // let output = '';
      // let t1 = performance.now();
      // for (let i = 0; i < 3; i++) {
      //   output += String.fromCharCode(parseInt(testArray[i] + '', 8));
      // }

      // console.log(versionNum);
      // if (output !== 'APO') { // backward compatibility. maybe txt?
      //   headerBuffer = rawBuffer.slice(0, EDITOR_VERSION.length);
      //   contentBuffer = rawBuffer.slice(EDITOR_VERSION.length);
      // } else {
      const headerBuffer = rawBuffer.slice(0, FILE_HEADER_LENGTH);
      const contentBuffer = rawBuffer.slice(FILE_HEADER_LENGTH);
      // }
      // let t2 = performance.now();
      // console.log(t2-t1)

      return {headerBuffer, contentBuffer};
    }

    return null;
  }

  public getHeaderBufferItems(headerBuffer: Uint8Array): IHeaderBufferItems {
    if (headerBuffer != null) {
      // console.log(Array.from(headerBuffer));
      // const testArray = Array.from(new Uint8Array(headerBuffer));
      const testArray = headerBuffer;
      // console.log(testArray)

      let radix = 10;
      // detect if its legacy 'exu' file
      const firstLetter = String.fromCharCode(testArray[0]);
      if (firstLetter !== FILE_HEADER_DESC[0]) {
        // tslint:disable-next-line: no-console
        // console.log('legacy version detected');
        radix = 8;
      }
      // the 6th byte is file header version
      const versionNum = parseInt(testArray[5] + '', radix);
      // console.log(parseInt(testArray[6] + '', 8), parseInt(testArray[7] + '', 8));
      // base 10 + base 10 -> reasonable
      const documentNum = parseInt(testArray[6] + '', radix) + parseInt(testArray[7] + '', radix);
      // console.log(documentNum);
      // console.log(versionNum);
      const bHtmlFile = parseInt(testArray[8] + '', radix);
      // console.log(bHtmlFile);

      // console.log(versionNum, documentNum)
      return {versionNum, documentNum, bHtmlFile};
    }
    return null;
  }

  /**
   * get default fonts
   * @param modeFonts from opendocumentwithstream() parameter
   */
  public getCurModeFonts(modeFonts: IModeFonts = null): IModeFonts {

    let curModeFonts: IModeFonts = null;
    if (this.defaultFont != null) {
      curModeFonts = {defaultFont: this.defaultFont, regionTitleFont: null};
      if (modeFonts != null) { // from opendocumentwithstream, highest priority
        const {defaultFont, regionTitleFont} = modeFonts;
        if (defaultFont != null) {
          curModeFonts.defaultFont = defaultFont;
        }
        if (regionTitleFont != null) {
          curModeFonts.regionTitleFont = regionTitleFont;
        }
      }
      // DEFAULT_FONT.fontFamily = curModeFonts.defaultFont.fontFamily;
      // DEFAULT_FONT.fontSize = curModeFonts.defaultFont.fontSize;
      if (curModeFonts.defaultFont != null) {
        const {fontSize, fontFamily} = curModeFonts.defaultFont;
        this.document.setDefaultFont({fontSize, fontFamily});
      }
    }

    // console.log(curModeFonts.defaultFont)
    return curModeFonts;
  }

  public getDoc(): Document {
    return this.document;
  }

  private prepareParaFromTxt(para: Paragraph, texts: string): void {
    const portion = new ParaPortion(para);
    portion.addText(texts);
    para.addToContent(0, portion);
  }

  private checkStructsViewSecret(): void {
    const newControlManager = this.document.getNewControlManager();
    const newControls = newControlManager.getNameMap();
    // tslint:disable-next-line: no-console
    // console.time('setSecretType');

    // let bChanged: boolean = false;
    for (const [name, newControl] of newControls) {
      // if (newControl.isViewSecret()) {
      //   // console.log(newControl.getViewSecretType())
      //   newControl.getNewControlContent()
      //     .setViewSecret(newControl.getViewSecretType());
      // }
      if (newControl.isHidden() && newControl.isNewSection()
          && newControl.getLeafList().length) {
        // newControl['bHidden'] = undefined;
        // newControl.setHidden(true);
        // const bSection = newControl.isNewSection();
        newControl['content'].setHidden(true, true);
        // bChanged = true;
      }
    }
    // if (bChanged) {
    //   this.document.recalculate();
    // }
    // tslint:disable-next-line: no-console
    // console.timeEnd('setSecretType');
  }

  /**
   * get current's cursor's container(i.e. table/region) if any. Intended to use in insertfile
   */
  private getCurrentContainer(document: Document): Region | TableCell {
    let container = null;
    // para always exists
    const curPara = document.getCurrentParagraph();
    if (curPara == null) {
      return null;
    } else {
      const documentContent = curPara.getParent();
      if (documentContent instanceof DocumentContent) {
        const tempContainer = documentContent.getParent();
        if (tempContainer instanceof Region || tempContainer instanceof TableCell) {
          container = tempContainer;
        }
      }
    }
    return container;
  }

  private async getXmlByAsync(fileReader: FileReader, xmlName: string, newZip: any): Promise<string> {
    let documentZippedFile = null;
    try {
      documentZippedFile = newZip.file(xmlName);
      if (!documentZippedFile) {
        throw new Error(xmlName + ' missing');
      }
    } catch (error) {
      // tslint:disable-next-line: no-console
      console.log(error);
      return ;
    }

    return new Promise((resolve) => {
      // tslint:disable-next-line: newline-per-chained-call
      documentZippedFile.async('blob').then((unZippedDocumentFile: any) => {
        fileReader.readAsText(unZippedDocumentFile);
        fileReader.onloadend = () => {
          const documentFile: string = fileReader.result as string;
          resolve(documentFile);
        };
      });
    });
  }

  private prepareSectionEnds(parsedXml: (string | number | rtNode)[]): void {
    this.sectionEnds = [];
    // check sections integrity
    const sectionEnds = tXmlFilter(parsedXml, (node) => node.tagName === 'sectionEnd');
    // console.log(sectionEnds)
    if (sectionEnds && sectionEnds.length > 0) {
      for (const sectionEnd of sectionEnds) {
        const attrs = sectionEnd.attributes;
        const name = attrs['name'];
        if (name != null) {
          this.sectionEnds.push(name);
        }
      }
    }
  }

  private readInsertOldFile(options: any): boolean {
    if ( true !== this.canInsertFile(options) ) {
      return true;
    }

    try {
      ////////////
      // console.time('new loop')
      if (this.xmlDoc) {
        const insertFileObj: IInsertFileContent = {
          content: [],
          newControlManager: null,
          regionManager: null,
          tableManager: null,
          drawingObjects: null,
          revisionManager: null,
          bRemoveRegionTitle: true,
          bRemoveNewControlTitle: false,
          bResetTextProperty: true,
          bRemoveHiddenRegion: true,
          bRemoveHiddenNewControl: true,
          insertFileAllNewControlNames: new Map(),
          documentVersion: options.documentVersion,
          bSelectedRegionContent: options.bSelectedRegionContent,
          bMergeLinkInterface: options.bMergeLinkInterface,
          logicDocument: this.document,
          bRecalc: null != options.bRecalc ? options.bRecalc : true,
          buttons: new Map(),
        };

        try {
          this.initInsertFileObj(insertFileObj);

          const bodyNode = this.tGetBodyNode();
          if (bodyNode != null) {
            // console.log(bodyNode)
            const bodyNodeChildren = bodyNode.children;
            if (bodyNodeChildren.length > 0) {

              try {
                if (!this.tGetSectPrNode()) {
                  // to confirm: this can be 0009?
                  // tslint:disable-next-line: no-console
                  console.warn('sectPr node is not found');
                }
              } catch (error) {
                // alert(ErrorMessages.XmlCorrupted);
                message.error(ErrorMessages.XmlCorrupted);

                // tslint:disable-next-line: no-console
                console.log(error);
                window.dispatchEvent(new ErrorEvent('error', { message: ErrorMessages.XmlCorrupted, error }));
                const date = new Date();
                logger.error({
                  id: this.document.id, code: error.stack,
                  startTime: date, args: null, name: 'read.getSectPrNode'
                });
                // no return for this one?
                // return false;
              }

              // let paras = []; // prepare for addPasteContent()
              for (let i = 0, len = bodyNodeChildren.length; i < len; i++) { // rootNode: same level as w:p
                const rootNode = bodyNodeChildren[i];
                if (typeof rootNode === 'object') {
                  if (rootNode.tagName === 'w:p') {
                    const container = null;

                    ParagraphReader.traverseParagraphForInsertFile2(rootNode, insertFileObj, this.contentControlDescObj,
                      this.uniqueImagelist, true, container, null);

                  } else if (rootNode.tagName === 'rg') {
                    const depth = 1;
                    const container = null;

                    RegionReader.traverseRegionForInsertFile2(rootNode, insertFileObj, this.contentControlDescObj,
                      this.uniqueImagelist, true, container, depth);
                  } else if (rootNode.tagName === 'w:tbl') {
                    // table
                    const container: any = document;
                    TableReader.traverseTableForInsertFile2(rootNode, insertFileObj, this.contentControlDescObj,
                      this.uniqueImagelist, true, container);
                  }
                }
              }

              this.addFileToContent(insertFileObj, options);

              return true;
            } else {
              // tslint:disable-next-line: no-console
              console.warn('no child within body node');
            }
          } else {
            throw new Error('body node is not found');
          }
        } finally {
          // catch outside
          // this.insertParaCount = 0;
          this.resetNewDocument(insertFileObj);
        }
      } else {
        throw new Error('xmlDoc object is empty');
      }
      // console.timeEnd('new loop')
      ///////////

    } catch (error) {
      // alert(ErrorMessages.XmlCorrupted);
      message.error(ErrorMessages.XmlCorrupted);

      // tslint:disable-next-line: no-console
      console.log(error);
      window.dispatchEvent(new ErrorEvent('error', { message: ErrorMessages.XmlCorrupted, error }));
      const date = new Date();
      logger.error({
        id: this.document.id, code: error.stack,
        startTime: date, args: null, name: 'readInsertOldFile'
      });
      return false;
    }
  }

  private readInsertNewFile(options: any): boolean {
    if ( true !== this.canInsertFile(options) ) {
      return true;
    }

    try {
      ////////////
      // console.time('new loop')
      if (this.xmlDoc) {
        const insertFileObj: IInsertFileContent = {
          content: [],
          newControlManager: null,
          regionManager: null,
          tableManager: null,
          drawingObjects: null,
          revisionManager: null,
          bRemoveRegionTitle: true,
          bRemoveNewControlTitle: false,
          bResetTextProperty: true,
          bRemoveHiddenRegion: true,
          bRemoveHiddenNewControl: true,
          bRetainCascade: options?.bRetainCascade || false,
          insertFileAllNewControlNames: new Map(),
          documentVersion: options.documentVersion,
          bSelectedRegionContent: options.bSelectedRegionContent,
          bMergeLinkInterface: options.bMergeLinkInterface,
          logicDocument: this.document,
          bRecalc: null != options.bRecalc ? options.bRecalc : true,
          buttons: new Map(),
          reNameNewControls: new Map(),
        };

        const history = this.document.getHistory();
        let bTurnOnHistory = history.isTurnOn();
        const time = new Date();

        try {
          this.initInsertFileObj(insertFileObj);
          this.contentControlDescObj.newControlProperty.bInsertFile = true;
          // this.contentControlDescObj.buttons = new Map();

          const bodyNode = this.tGetBodyNode();
          if (bodyNode != null) {
            // console.log(bodyNode)
            const bodyNodeChildren = bodyNode.children;
            if (bodyNodeChildren.length > 0) {

              try {
                if (!this.tGetSectPrNode()) {
                  // to confirm: this can be 0009?
                  // tslint:disable-next-line: no-console
                  console.warn('sectPr node is not found');
                }
              } catch (error) {
                // alert(ErrorMessages.XmlCorrupted);
                message.error(ErrorMessages.XmlCorrupted);

                // tslint:disable-next-line: no-console
                console.log(error);
                window.dispatchEvent(new ErrorEvent('error', { message: ErrorMessages.XmlCorrupted, error }));
                const date = new Date();
                logger.error({
                  id: this.document.id, code: error.stack,
                  startTime: date, args: null, name: 'read.getSectPrNode'
                });
                // no return for this one?
                // return false;
              }

              history.turnOff();
              const bNewVersion = FILE_HEADER_VERSION2 === options.documentVersion;
              // let paras = []; // prepare for addPasteContent()
              for (let i = 0, len = bodyNodeChildren.length; i < len; i++) { // rootNode: same level as w:p
                const rootNode = bodyNodeChildren[i];
                if (typeof rootNode === 'object') {
                  if (rootNode.tagName === 'w:p') {
                    const container = null;

                    if (bNewVersion) {
                      ParagraphReader.traverseParagraphForInsertFileSec(rootNode, insertFileObj, this.contentControlDescObj,
                        this.uniqueImagelist, container);
                    } else {
                      ParagraphReader.traverseParagraphForInsertFile3(rootNode, insertFileObj, this.contentControlDescObj,
                        this.uniqueImagelist, container, null, this.sectionEnds);
                    }

                  } else if (rootNode.tagName === 'rg') {
                    const depth = 1;
                    const container = null;

                    RegionReader.traverseRegionForInsertFile3(rootNode, insertFileObj, this.contentControlDescObj,
                      this.uniqueImagelist, container, depth, null, this.sectionEnds, false);
                  } else if (rootNode.tagName === 'w:tbl') {
                    // table
                    const container: any = document;
                    TableReader.traverseTableForInsertFile3(rootNode, insertFileObj, this.contentControlDescObj,
                      this.uniqueImagelist, null, container, this.sectionEnds);
                  }
                }
              }

              if (bTurnOnHistory) {
                history.turnOn();
                bTurnOnHistory = undefined;
              }
              this.addFileToContent(insertFileObj, options);
              insertFileObj.logicDocument.getButtonManager().clearCaches(true);
              this.contentControlDescObj.newControlProperty.bInsertFile = false;

              // const elementMonitor = this.document.getElementMonitor();
              // if (elementMonitor?.canAddAction(MonitorAction.Insert)) {
              //   let insertText = '';
              //   insertFileObj.content.forEach((item) => {
              //     insertText += item.getSelectText(true);
              //   });
              //   elementMonitor.addAction(undefined, {start: getCurTime(time),
              //     end: getCurTime(),
              //     type: MonitorAction.Insert, insertOrPasteContent: insertText});
              // }
              return true;
            } else {
              // tslint:disable-next-line: no-console
              console.warn('no child within body node');
            }
          } else {
            throw new Error('body node is not found');
          }
        } finally {
          // catch outside
          // this.insertParaCount = 0;
          if (bTurnOnHistory) {
            history.turnOn();
          }

          insertFileObj.logicDocument.getButtonManager().clearCaches();
          this.resetNewDocument(insertFileObj);
          this.contentControlDescObj.newControlProperty.bInsertFile = false;
        }
      } else {
        throw new Error('xmlDoc object is empty');
      }
      // console.timeEnd('new loop')
      ///////////

    } catch (error) {
      // alert(ErrorMessages.XmlCorrupted);
      message.error(ErrorMessages.XmlCorrupted);

      // tslint:disable-next-line: no-console
      console.log(error);
      window.dispatchEvent(new ErrorEvent('error', { message: ErrorMessages.XmlCorrupted, error }));
      const date = new Date();
      logger.error({
        id: this.document.id, code: error.stack,
        startTime: date, args: null, name: 'readInsertNewFile'
      });
      return false;
    }
  }

  private readInsertNewFileNISCell(options: any): boolean {
    if ( true !== this.canInsertFile(options) ) {
      return true;
    }

    try {
      ////////////
      // console.time('new loop')
      if (this.xmlDoc) {
        const insertFileObj: IInsertFileContent = {
          content: [],
          newControlManager: null,
          regionManager: null,
          tableManager: null,
          drawingObjects: null,
          revisionManager: null,
          bRemoveRegionTitle: true,
          bRemoveNewControlTitle: false,
          bRetainCascade: options?.bRetainCascade || false,
          bResetTextProperty: true,
          bRemoveHiddenRegion: true,
          bRemoveHiddenNewControl: true,
          insertFileAllNewControlNames: new Map(),
          documentVersion: options.documentVersion,
          bSelectedRegionContent: options.bSelectedRegionContent,
          bMergeLinkInterface: options.bMergeLinkInterface,
          logicDocument: this.document,
          bRecalc: null != options.bRecalc ? options.bRecalc : true,
          buttons: new Map(),
          reNameNewControls: new Map(),
        };

        try {
          this.initInsertFileObj(insertFileObj);

          const bodyNode = this.tGetBodyNode();
          if (bodyNode != null) {
            // console.log(bodyNode)
            const bodyNodeChildren = bodyNode.children;
            if (bodyNodeChildren.length > 0) {

              try {
                if (!this.tGetSectPrNode()) {
                  // to confirm: this can be 0009?
                  // tslint:disable-next-line: no-console
                  console.warn('sectPr node is not found');
                }
              } catch (error) {
                // alert(ErrorMessages.XmlCorrupted);
                message.error(ErrorMessages.XmlCorrupted);

                // tslint:disable-next-line: no-console
                console.log(error);
                window.dispatchEvent(new ErrorEvent('error', { message: ErrorMessages.XmlCorrupted, error }));
                const date = new Date();
                logger.error({
                  id: this.document.id, code: error.stack,
                  startTime: date, args: null, name: 'read.getSectPrNode'
                });
                // no return for this one?
                // return false;
              }

              const bNewVersion = FILE_HEADER_VERSION2 === options.documentVersion;
              // let paras = []; // prepare for addPasteContent()
              for (let i = 0, len = bodyNodeChildren.length; i < len; i++) { // rootNode: same level as w:p
                const rootNode = bodyNodeChildren[i];
                if (typeof rootNode === 'object') {
                  if (rootNode.tagName === 'w:p') {
                    const container = null;

                    if (bNewVersion) {
                      ParagraphReader.traverseParagraphForInsertFileSec(rootNode, insertFileObj, this.contentControlDescObj,
                        this.uniqueImagelist, container);
                    } else {
                      ParagraphReader.traverseParagraphForInsertFile3(rootNode, insertFileObj, this.contentControlDescObj,
                        this.uniqueImagelist, container, null, this.sectionEnds);
                    }

                  } else if (rootNode.tagName === 'rg') {
                    const depth = 1;
                    const container = null;

                    RegionReader.traverseRegionForInsertFile3(rootNode, insertFileObj, this.contentControlDescObj,
                      this.uniqueImagelist, container, depth, null, this.sectionEnds, false);
                  } else if (rootNode.tagName === 'w:tbl') {
                    // table
                    const container: any = document;
                    TableReader.traverseTableForInsertFile3(rootNode, insertFileObj, this.contentControlDescObj,
                      this.uniqueImagelist, null, container, this.sectionEnds);
                  }
                }
              }

              this.addFileToContent(insertFileObj, options);

              return true;
            } else {
              // tslint:disable-next-line: no-console
              console.warn('no child within body node');
            }
          } else {
            throw new Error('body node is not found');
          }
        } finally {
          // catch outside
          // this.insertParaCount = 0;
          this.resetNewDocument(insertFileObj);
        }
      } else {
        throw new Error('xmlDoc object is empty');
      }
      // console.timeEnd('new loop')
      ///////////

    } catch (error) {
      // alert(ErrorMessages.XmlCorrupted);
      message.error(ErrorMessages.XmlCorrupted);

      // tslint:disable-next-line: no-console
      console.log(error);
      window.dispatchEvent(new ErrorEvent('error', { message: ErrorMessages.XmlCorrupted, error }));
      const date = new Date();
      logger.error({
        id: this.document.id, code: error.stack,
        startTime: date, args: null, name: 'readInsertNewFile'
      });
      return false;
    }
  }

  private initInsertFileObj(initInsertFileObj: IInsertFileContent): void {
    if ( !initInsertFileObj ) {
      initInsertFileObj = {
        content: [],
        newControlManager: null,
        regionManager: null,
        tableManager: null,
        drawingObjects: null,
        revisionManager: null,
        bRemoveRegionTitle: true,
        bRemoveNewControlTitle: false,
        bResetTextProperty: true,
        bRemoveHiddenRegion: true,
        bRemoveHiddenNewControl: true,
        insertFileAllNewControlNames: new Map(),
        documentVersion: 0,
        bSelectedRegionContent: false,
        bMergeLinkInterface: undefined,
        bRecalc: true,
        buttons: new Map(),
        reNameNewControls: new Map(),
      };
    }

    initInsertFileObj.newControlManager = this.document.getNewControlManager();
    initInsertFileObj.tableManager = this.document.getTableManager();
    initInsertFileObj.regionManager = this.document.getRegionManager();
    initInsertFileObj.drawingObjects = this.document.getDrawingObjects();
    initInsertFileObj.revisionManager = this.document.getRevisionsManager();
    initInsertFileObj.content = [];
  }

  private resetNewDocument(initInsertFileObj: IInsertFileContent): void {
    if ( initInsertFileObj ) {
      initInsertFileObj.newControlManager.resetInsertNewControlCaches();
      initInsertFileObj.tableManager.resetInsertTableCaches();
      initInsertFileObj.regionManager.resetInsertRegionCaches();
      initInsertFileObj.drawingObjects.resetInsertGraphicObjectCaches();

      initInsertFileObj.newControlManager = null;
      initInsertFileObj.tableManager = null;
      initInsertFileObj.regionManager = null;
      initInsertFileObj.drawingObjects = null;
      initInsertFileObj.content = null;
      initInsertFileObj.insertFileAllNewControlNames = null;
      initInsertFileObj.reNameNewControls = null;
      initInsertFileObj = null;
    }
  }

  private canInsertFile(options: any): boolean {
    let type = options ? options.type : null;
    const bSelectedRegionContent = (options ? true === options.bSelectedRegionContent : false);
    type = bSelectedRegionContent ? options.type : type;
    const bSelect = (bSelectedRegionContent
                && (InsertFilePositionType.ParentRegion === type || InsertFilePositionType.LeafRegion === type))
                ? false : (InsertFilePositionType.Selection === type);

    if ( null == type
      || bSelect
      // || type === InsertFilePositionType.HeaderFooter
      || type === InsertFilePositionType.OtherNewControlsExceptTextAndSection ) {
      return false;
    }

    return true;
  }

  private addFileToContent(insertFileObj: IInsertFileContent, options: any): void {
    const type = options ? options.type : null;

    switch (type) {
      // case InsertFilePositionType.HeaderFooter:
      case InsertFilePositionType.Selection:
      case InsertFilePositionType.OtherNewControlsExceptTextAndSection:
        break;

      case InsertFilePositionType.Paragraph:
        this.addContentToParaPos(insertFileObj);
        break;

      case InsertFilePositionType.NewControlText:
      case InsertFilePositionType.NewControlTextInTableCell:
      case InsertFilePositionType.NewControlTextInRegion:
      case InsertFilePositionType.NewControlTextOfTableCellInRegion:
        this.addContentToNewControlTextPos(insertFileObj);
        break;

      case InsertFilePositionType.NISTableCellText:
        this.addContentToNISCellText(insertFileObj);
        break;

      case InsertFilePositionType.NewControlSection:
      case InsertFilePositionType.NewControlSectionInTableCell:
      case InsertFilePositionType.NewControlSectionInRegion:
      case InsertFilePositionType.NewControlSectionInLeafRegion:
      case InsertFilePositionType.NewControlSectionOfTableCellInRegion:
      case InsertFilePositionType.NewControlSectionOfTableCellInLeafRegion:
      case InsertFilePositionType.TableCell:
      case InsertFilePositionType.TableCellInParentRegion:
      case InsertFilePositionType.TableCellInLeafRegion:
        this.addContentToNewControlSectionOrCellPos(insertFileObj);
        break;

      case InsertFilePositionType.ParentRegion:
        this.addContentToParentRegionPos(insertFileObj);
        break;

      case InsertFilePositionType.LeafRegion:
      case InsertFilePositionType.HeaderFooter:
        this.addContentToLeafRegionPos(insertFileObj);
        break;
    }
    if (insertFileObj.bRetainCascade && insertFileObj.insertEventInfos) {
        const { newControlManager, insertEventInfos } = insertFileObj;
        const cascadeManager = newControlManager.getCascadeManager();

        this.updateEventInfos(insertFileObj);

        // 重刷插入文件中存在的求和级联内容
        for (const [control, eventInfo] of insertEventInfos.entries()) {
            // 插入文件时会保留元素events，但可能因为newControlManager内部未插入元素，导致事件并没有分发给对应求和元素
            cascadeManager.setEvent(eventInfo, control, true);
        }
    }
    if (insertFileObj.bRetainCascade && insertFileObj.reNameNewControls.size) {
        this.updateRetainCascade(insertFileObj);
    }
  }

  private addContentToParaPos(insertFileObj: IInsertFileContent, type?: InsertFilePositionType): void {
    this.removeHiddenNewControl(insertFileObj);
    this.setAllContentLogic(insertFileObj);
    this.setAllContentParent(insertFileObj);
    this.document.insertFileContent(insertFileObj.content, {
        type,
        bSelectedRegionContent: insertFileObj.bSelectedRegionContent,
        bMergeLinkInterface: insertFileObj.bMergeLinkInterface,
        bRecalc: insertFileObj.bRecalc,
    });
    // insertFileObj.newControlManager.insertFileNewControlNames(insertFileObj.insertFileAllNewControlNames);

    if (insertFileObj.bRecalc) {
      this.document.recalculateAllForce();
    }
  }

  private addContentToNewControlTextPos(insertFileObj: IInsertFileContent): void {
    this.removeAllRegions(insertFileObj);
    this.removeAllTables(insertFileObj);

    const para = new Paragraph(this.document, this.document);
    const drawingList = [];

    para.content = [];

    for (const element of insertFileObj.content) {
      if ( element && element.isParagraph() ) {

        for (const portion of element.content ) {
          if ( portion && portion.content && 0 < portion.content.length ) {
            const result = portion.isTextContent(true);

            if ( portion.isRegionTitle(false) ) {
              if ( false === insertFileObj.bRemoveRegionTitle ) {
                if ( insertFileObj.bResetTextProperty ) {
                  portion.textProperty = new TextProperty(getDefaultFont(insertFileObj.logicDocument));
                }

                para.content.push(portion);
              }
            } else if ( result.result ) {
              const drawNames = result.drawNames;
              if ( drawNames && 0 < drawNames.length ) {
                drawingList.concat(drawNames);
              }
              para.content.push(portion);
            } else if ( portion.isNewControlStart() ) {
              if ( false === insertFileObj.bRemoveNewControlTitle ) {
                const newControlName = portion.getStartNewControlName();
                const newControl = insertFileObj.newControlManager.getPasteNewControlByName(newControlName);
                if (newControl && newControl.getTitle() && '' !== newControl.getTitle() ) {
                  portion.content.splice(0, 1);

                  para.content.push(portion);
                }
              }
            }
          }
        }
      }
    }

    para.checkParaEnd();
    insertFileObj.content = [];
    insertFileObj.content.push(para);

    const graphicObjectCaches = insertFileObj.drawingObjects.getGraphicObjectCaches();

    if ( 0 < graphicObjectCaches.length && 0 < drawingList.length
        && graphicObjectCaches.length !== drawingList.length ) {

      for (const drawingName of drawingList) {
        for (let index2 = 0; index2 < graphicObjectCaches.length; index2++) {
          const element = graphicObjectCaches[index2];
          if ( !(element && element.getDrawingName && drawingName === element.getDrawingName()) ) {
            graphicObjectCaches.splice(index2, 1);
            break;
          }
        }
      }
    }

    const newControlCaches = insertFileObj.newControlManager.getInsertNewControlCaches();
    if ( newControlCaches && 0 < newControlCaches.length ) {
      insertFileObj.newControlManager.resetInsertNewControlCaches();
      insertFileObj.insertFileAllNewControlNames.clear();
    }

    const tableCaches = insertFileObj.tableManager.getInsertTableCaches();
    const regionCaches = insertFileObj.regionManager.getAllPasteRegion();
    if ( tableCaches && 0 < tableCaches.length ) {
      insertFileObj.tableManager.resetInsertTableCaches();
    }

    if ( regionCaches && 0 < regionCaches.length ) {
      insertFileObj.regionManager.resetInsertRegionCaches();
    }

    this.addContentToParaPos(insertFileObj);
  }

  private addContentToNISCellText(insertFileObj: IInsertFileContent): void {

    this.removeAllRegions(insertFileObj);
    this.removeAllTables(insertFileObj);

    insertFileObj.treatedContent = insertFileObj.content;
    insertFileObj.content = [];

    for (const element of insertFileObj.treatedContent) {
      if ( element && element.isParagraph() ) {

        // init para
        const para = new Paragraph(this.document, this.document);
        para.content = [];

        for (let i = 0, len = element.content.length; i < len; i++ ) {
          const portion = element.content[i];
          if ( portion && portion.content && 0 < portion.content.length ) {
            const result = portion.isTextContent(true);

            if ( portion.isRegionTitle(false) ) {
              if ( false === insertFileObj.bRemoveRegionTitle ) {
                if ( insertFileObj.bResetTextProperty ) {
                  portion.textProperty = new TextProperty(getDefaultFont(insertFileObj.logicDocument));
                }

                para.content.push(portion);
              }
            } else if ( result.result ) {
              const drawNames = result.drawNames;
              if ( drawNames && 0 < drawNames.length ) {
                portion.removeDrawing();
              }
              para.content.push(portion);
            } else if ( portion.isNewControlStart() ) {
              if ( false === insertFileObj.bRemoveNewControlTitle ) {
                const newControlName = portion.getStartNewControlName();
                const newControl = insertFileObj.newControlManager.getPasteNewControlByName(newControlName);
                if (newControl && newControl.getTitle() && '' !== newControl.getTitle() ) {
                  portion.content.splice(0, 1);

                  para.content.push(portion);
                }
              }
            }
          }

          if (i === len - 1) {
            para.checkParaEnd();
            insertFileObj.content.push(para);
          }

        }
      }
    }

    insertFileObj.treatedContent = [];

    const newControlCaches = insertFileObj.newControlManager.getInsertNewControlCaches();
    if ( newControlCaches && 0 < newControlCaches.length ) {
      insertFileObj.newControlManager.resetInsertNewControlCaches();
      insertFileObj.insertFileAllNewControlNames.clear();
    }

    const tableCaches = insertFileObj.tableManager.getInsertTableCaches();
    const regionCaches = insertFileObj.regionManager.getAllPasteRegion();
    if ( tableCaches && 0 < tableCaches.length ) {
      insertFileObj.tableManager.resetInsertTableCaches();
    }

    if ( regionCaches && 0 < regionCaches.length ) {
      insertFileObj.regionManager.resetInsertRegionCaches();
    }

    this.addContentToParaPos(insertFileObj);
  }

  /**
   * 在节中插入文档：删除所有区域结构，区域标题也删除，删除所有表格及其内容
   * @param insertFileObj
   */
  private addContentToNewControlSectionOrCellPos(insertFileObj: IInsertFileContent): void {
    // 删除表格内的结构化元素、图片
    const newControlCaches = insertFileObj.newControlManager.getInsertNewControlCaches();
    const graphicObjectCaches = insertFileObj.drawingObjects.getGraphicObjectCaches();
    const allNewControlNames = insertFileObj.insertFileAllNewControlNames;

    for (let index = newControlCaches.length - 1; index >= 0; index--) {
      const newControlInfo = newControlCaches[index];
      const newControl = newControlInfo ? newControlInfo.control : null;

      if ( newControl && newControl.getStartBorderInParagraph() ) {
        const para = newControl.getStartBorderInParagraph();
        const parent = para.getParent();

        if ( parent && parent.isTableCellContent() ) {
          this.removeLeafNewControlNames(newControl, newControlCaches, true);
          allNewControlNames.delete(newControl.getNewControlName());
          newControlCaches.splice(index, 1);
        }
      }
    }

    for (let index = graphicObjectCaches.length - 1; index >= 0; index--) {
      const drawing = graphicObjectCaches[index];

      if ( drawing && 0 === drawing.tableId ) {
        graphicObjectCaches.splice(index, 1);
      }
    }

    this.removeAllRegions(insertFileObj);
    this.removeAllTables(insertFileObj);

    // remove all table refs and region refs 删除管理类的所有表格和区域
    const tableCaches = insertFileObj.tableManager.getInsertTableCaches();
    const regionCaches = insertFileObj.regionManager.getAllPasteRegion();
    if ( tableCaches && 0 < tableCaches.length ) {
      insertFileObj.tableManager.resetInsertTableCaches();
    }

    if ( regionCaches && 0 < regionCaches.length ) {
      insertFileObj.regionManager.resetInsertRegionCaches();
    }

    this.addContentToParaPos(insertFileObj);
  }

  /**
   * 在父区域插入文档：删除有子区域的父区域结构，父区域标题也删除
   * @param insertFileObj
   */
  private addContentToParentRegionPos(insertFileObj: IInsertFileContent): void {
    this.removeParentRegions(insertFileObj);
    this.addContentToParaPos(insertFileObj, InsertFilePositionType.ParentRegion);
  }

  /**
   * 在子区域插入文档：删除所有区域结构，区域标题也删除
   * @param insertFileObj
   */
  private addContentToLeafRegionPos(insertFileObj: IInsertFileContent): void {
    // 删除所有区域结构
    this.removeAllRegions(insertFileObj);

    const regionCaches = insertFileObj.regionManager.getAllPasteRegion();

    if ( regionCaches && 0 < regionCaches.length ) {
      insertFileObj.regionManager.resetInsertRegionCaches();
    }

    this.addContentToParaPos(insertFileObj, InsertFilePositionType.LeafRegion);
  }

  /**
   * 删除所有表格
   * @param container
   */
  private removeAllTables(container: any): void {
    for (let index = container.content.length - 1; index >= 0; index--) {
      const element = container.content[index];
      if ( !element || (element && element.isTable()) ) {
        container.content.splice(index, 1);
      } else if ( element && element.isRegion() ) {
        const content = element.getOperateContent();
        this.removeAllTables(content);
      }
    }
  }

  /**
   * 删除所有区域
   * @param insertFileObj
   */
  private removeAllRegions(insertFileObj: IInsertFileContent): void {
    let regionDepth = REGION_MAX_DEPTH;

    while ( 0 <= regionDepth ) {
      for (let index = insertFileObj.content.length - 1; index >= 0; index--) {
        const element = insertFileObj.content[index];
        if ( !element ) {
          insertFileObj.content.splice(index, 1);
        } else if ( element && element.isRegion() ) {
          const regionContent = element.getOperateContent();
          const title = element.getTitle();
          const hasTitle = ('' !== title && element.isShowTitle2() );
          const contentElements = regionContent.content ?
                          regionContent.content.splice(0, regionContent.content.length) : null;
          insertFileObj.content.splice(index, 1);

          // const elementsLength = contentElements ? contentElements.length : 0;

          const firstPara = contentElements ? contentElements[0] : null;
          if ( hasTitle && firstPara.isParagraph() ) {
            this.removeRegionTitle(firstPara, title);
          }

          // for (let index2 = 0; index2 < elementsLength; index2++) {
          //   const element2 = contentElements[index2];
          //   if ( element2 ) {
          //     insertFileObj.content.splice(index + index2, 0, element2);
          //   }
          // }
          insertFileObj.content.splice(index, 0, ...contentElements);
        }
      }
      regionDepth--;
    }
  }

  /**
   * 删除所有的父区域
   * @param insertFileObj
   * @returns
   */
  private removeParentRegions(insertFileObj: IInsertFileContent): void {
    const regionCaches = insertFileObj.regionManager.getAllPasteRegion();
    if ( !regionCaches || 0 >= regionCaches.length ) {
      return ;
    }

    const regionNames = [];
    for (let index = insertFileObj.content.length - 1; index >= 0; index--) {
      const region = insertFileObj.content[index];
      if ( region && region.isRegion() ) {
        const elements = region.getContent();

        let bFlag = false;
        for (let index2 = elements.length - 1; index2 >= 0; index2--) {
          const element = elements[index2];
          if ( element && element.isRegion() ) {
            bFlag = true;
            break;
          }
        }

        if ( bFlag ) {
          regionNames.push(region.getName());

          const title = region.getTitle();
          const hasTitle = ('' !== title && region.isShowTitle2() );
          const regionContent = region.getOperateContent();
          const regionContentElements = regionContent.content.splice(0, regionContent.content.length);

          insertFileObj.content.splice(index, 1);

          const firstPara = regionContentElements ? regionContentElements[0] : null;
          if ( hasTitle && firstPara.isParagraph() ) {
            this.removeRegionTitle(firstPara, title);
          }

          // for (let index2 = 0; index2 < regionContentElements.length; index2++) {
          //   const element = regionContentElements[index2];
          //   if ( element ) {
          //     insertFileObj.content.splice(index + index2, 0, element);
          //   }
          // }
          insertFileObj.content.splice(index, 0, ...regionContentElements);
        }
      }
    }

    for (const regionName of regionNames) {
      for (let index2 = regionCaches.length - 1; index2 >= 0; index2--) {
        const regionInfo = regionCaches[index2];
        if ( regionInfo && regionInfo.region && regionInfo.region.getName() === regionName ) {
          regionCaches.splice(index2, 1);
        }
      }
    }
  }

  /**
   * 设置所有节点的父节点
   * @param container
   */
  private setAllContentLogic(container: any): void {
    for (const element of container.content) {
      if ( element ) {
        element.setLogicDocument(this.document);
        if ( element && element.isRegion() ) {
          const content = element.getOperateContent();
          content.setLogicDocument(this.document);
          this.setAllContentLogic(content);
        }
      }
    }
  }

  private setAllContentParent(insertFileContent: IInsertFileContent): void {
    for (const element of insertFileContent.content) {
      if ( element ) {
        element.parent = this.document;
      }
    }
  }

  /**
   * 删除区域的标题portion
   * @param paragraph 区域首行段落
   * @param title 标题
   */
  private removeRegionTitle(paragraph: Paragraph, title: string): void {
    if ( paragraph && paragraph.isParagraph() ) {
      const firstPortion = paragraph.content[0];
      if ( firstPortion && title === firstPortion.getText() ) {
        paragraph.content.splice(0, 1);
      }
    }
  }

  /**
   * 删除隐藏的节，及其包含的子元素和图片等
   * @param insertFileObj
   */
  private removeHiddenNewControl(insertFileObj: IInsertFileContent): void {
    return; // 根据bug，不能对隐藏元素进行删除
    // 删除表格内的结构化元素、图片
    const newControlCaches = insertFileObj.newControlManager.getInsertNewControlCaches();
    const newControls = this.getAllParentNewControls(insertFileObj);

    for (let index = newControls.length - 1; index >= 0; index--) {
      const newControl = newControls[index];

      if ( newControl && newControl.isHidden() ) {
        // 删除最顶层隐藏的元素

        this.removeHiddenNewControlContent(newControl, index, newControlCaches, insertFileObj, true);
      } else if ( newControl && newControl.getLeafList() ) {
        // 删除隐藏的子元素

        const leafList = newControl.getLeafList();

        if ( leafList && 0 < leafList.length ) {
          this.removeHiddenLeafNewControls(leafList, insertFileObj);
        }
      }
    }
  }

  /**
   * 删除隐藏元素的内容，及其子元素，并从list列表中删除其信息，删除内容中的图片
   * @param newControl 顶层元素
   * @param pos 位置
   * @param list
   * @param insertFileObj
   * @param bPaste 是否paste
   */
  private removeHiddenNewControlContent(
    newControl: NewControl, pos: number, list: any[], insertFileObj: IInsertFileContent, bPaste?: boolean
  ): void {
    const graphicObjectCaches = insertFileObj.drawingObjects.getGraphicObjectCaches();

    const startPara = newControl.getStartBorderInParagraph();
    const endPara = newControl.getEndBorderInParagraph();
    const startPortion = newControl.getStartBorderPortion();
    const endPortion = newControl.getEndBorderPortion();
    const startPortionIndex = startPara.getPortionIndexById(startPortion.id);
    const endPortionIndex = endPara.getPortionIndexById(endPortion.id);
    const parent = (insertFileObj.logicDocument === startPara?.getParent() || !startPara?.getParent())
                    ? insertFileObj : startPara?.getParent();

    if ( startPara && startPara === endPara ) {
      // 删除内容中的图片
      this.removeDrawingInPara(graphicObjectCaches, startPara, startPortionIndex,
                                endPortionIndex - startPortionIndex + 1);
      startPara.content.splice(startPortionIndex, endPortionIndex - startPortionIndex + 1);
      startPara.checkParaEnd();

      if ( startPara && startPara.isEmpty() && newControl.isNewSection() ) {
        for (let index = parent?.content.length - 1; index >= 0; index--) {
          const element = parent.content[index];
          if ( element === startPara ) {
            parent.content.splice(index, 1);
            break;
          }
        }
      }
    } else {
      this.removeDrawingInPara(graphicObjectCaches, startPara, startPortionIndex,
                                startPara.content.length);
      startPara.removeNewControlText(startPortionIndex, null);
      startPara.checkParaEnd();

      this.removeDrawingInPara(graphicObjectCaches, endPara, 0, endPortionIndex);
      endPara.removeNewControlText(0, endPortionIndex);

      if (parent) {
        let bStart = false;

        for (let index = parent.content.length - 1; index >= 0; index--) {
          const element = parent.content[index];

          if ( bStart ) {
            const preElement = parent.content[index - 1];

            if ( preElement && preElement.id === startPara.id ) {
              this.removeDrawingInPara(graphicObjectCaches, element, 0, element.content.length);
              parent.content.splice(index, 1);

              if ( startPara && startPara.isEmpty() ) {
                parent.content.splice(index - 1, 1);
              }
              bStart = false;
              break;
            } else if ( element && element.id === startPara.id ) {
              if ( startPara && startPara.isEmpty() ) {
                parent.content.splice(index, 1);
              }
              break;
            }

            this.removeDrawingInPara(graphicObjectCaches, element, 0, element.content.length);
            parent.content.splice(index, 1);
          }

          if ( element && element.id === endPara.id ) {
            bStart = true;
          }
        }
      }
    }

    // 单元格、区域只有一个段落，且段落只含有一个隐藏的节
    if (0 === parent?.content.length && insertFileObj !== parent) {
      parent.content.splice(0, 0, new Paragraph(startPara.getParent(), insertFileObj.logicDocument));
    }

    this.removeLeafNewControlNames(newControl, list, bPaste);
    list.splice(pos, 1);
  }

  /**
   * 删除隐藏的父元素上的所有子节点的名称
   * @param newControl 隐藏的父元素
   * @param list 所有元素的列表
   * @param bPaste 是否paste
   */
  private removeLeafNewControlNames(newControl: NewControl, list: any[], bPaste?: boolean): void {
    const leafList = newControl.getLeafList();
    if ( leafList ) {
      leafList.forEach((leaf) => {
        if ( leaf ) {
          this.removeLeafNewControlNames(leaf, list, bPaste);
          for (let index = list.length - 1; index >= 0; index--) {
            const item = bPaste ? list[index].control : list[index];
            if (leaf.getNewControlName() === item.getNewControlName()) {
                list.splice(index, 1);
                break;
            }
          }
        }
      });
    }
  }

  /**
   * 删除子节点上的隐藏元素
   * @param leafList 子节点
   * @param insertFileObj
   */
  private removeHiddenLeafNewControls(leafList: NewControl[], insertFileObj: IInsertFileContent): void {
    for (let index = leafList.length - 1; index >= 0; index--) {
      const newControl = leafList[index];
      if ( newControl && newControl.isHidden() ) {
        this.removeHiddenNewControlContent(newControl, index, leafList, insertFileObj);
      } else if ( newControl && newControl.getLeafList() ) {
        const leafLeafList = newControl.getLeafList();

        if ( leafLeafList && 0 < leafLeafList.length ) {
          this.removeHiddenLeafNewControls(leafLeafList, insertFileObj);
        }
      }
    }
  }

  private getAllParentNewControls(insertFileObj: IInsertFileContent): NewControl[] {
    const newControls: NewControl[] = [];

    if ( insertFileObj && insertFileObj.newControlManager.getInsertNewControlCaches() ) {
      const newControlCaches = insertFileObj.newControlManager.getInsertNewControlCaches();

      newControlCaches.forEach((value) => {
        if ( value && value.control ) {
          newControls.push(value.control);
        }
      });
    }

    return newControls;
  }

  private removeDrawingInPara(drawings: any[], para: Paragraph, startPos: number, endPos: number): void {
    for (let index = drawings.length - 1; index >= 0; index--) {
      const drawing = drawings[index];
      if ( drawing && drawing.paraId === para.id && drawing.portion ) {
        const drawingIndex = para.getPortionIndexById(drawing.portion.id);
        if ( drawingIndex > startPos && drawingIndex < endPos ) {
          drawings.splice(index, 1);
        }
      } else if ( !drawing ) {
        drawings.splice(index, 1);
      }
    }
  }

  private setFileVersionWithOperate(options: any, documentNum: number): void {
    if (options.operate === OperateType.Open) {
      this.document.setFileVersion(documentNum);
    } else if (options.operate === OperateType.New) {
      // shouldn't be FILE_HEADER_DOCUMENT_VERSION
      this.document.setFileVersion(0);
    } else {
      // tslint:disable-next-line: no-console
      // console.log('fileversion is not written to document');

      // this may come from insertfile, so keep it normal
      this.document.setFileVersion(documentNum);
    }
  }

  /**
   * 更新重命名的级联target
   * @param insertFileObj
   */
  private updateRetainCascade(insertFileObj: IInsertFileContent): void {
    const reNameNewControls = insertFileObj.reNameNewControls;
    insertFileObj.insertFileAllNewControlNames.forEach((newControl) =>{
      if (newControl.isNewSection()) {
        const leafList = newControl.getLeafList();
        leafList?.forEach((leaf) => {
          this.updateAllCascades(reNameNewControls, leaf);
        })
      } else {
        this.updateAllCascades(reNameNewControls, newControl);
      }
    });
  }

  /**
   * 更新重命名的计算级联target
   * @param insertFileObj
   */
  private updateEventInfos(insertFileObj: IInsertFileContent): void {
    const reNameNewControls = insertFileObj.reNameNewControls;
    insertFileObj.insertEventInfos.forEach((events, control) =>{
        if (events && events.event.length) {
          const newEvent = [];
          const allTartgets = {};
          const allNames = (events.target || '').split(',');
          let expression = events.expression;
          for (let curIndex = 0, len = events.event.length; curIndex < len; curIndex++) {
              const event = events.event[curIndex];
              const curTarget = (event.source || '');
              let targets;
              if (curTarget === '*') {
                  targets = allNames
              } else {
                  targets = curTarget.split(',');
              }
              if (!targets.length) {
                  continue;
              }
              let curTargets: string[] = [];
              for (let index = 0, len = targets.length; index < len; index++) {
                  const curName = targets[index];
                  const newName = reNameNewControls.get(curName);
                  if (!newName) {
                      break;
                  }
                  const reg = new RegExp(curName, 'g');
                  expression = expression?.replace(reg, (all, t): string => {
                      const curindex = all.length + t;
                      const curText = expression.charAt(curindex);
                      if (!curText || ['*', '/', '+', ')', '}', ']', ' '].includes(curText)) {
                          return newName
                      }
                      return all;
                  });
                  curTargets.push(newName);
                  allTartgets[newName] = true;
              }
              if (curTargets.length) {
                  newEvent.push({source: curTargets.join(','), triggerType: event.triggerType});
              }
          }
          if (newEvent.length) {
            insertFileObj.insertEventInfos.set(control, {
                  target: Object.keys(allTartgets).join(','),
                  event: newEvent,
                  key: events.key,
                  action: events.action,
                  expression: expression,
              });
          }
      }
    });
  }

  private updateAllCascades(reNameNewControls: Map<string, string>, newControl: NewControl): void {
    const cascades = newControl.getCascades();
      if (cascades?.length) {
        cascades.forEach((cascasde) => {
          const targets = (cascasde.target || '').split(',');
          if (!targets.length) {
              return;
          }
          let curTargets: string[] = [];
          for (let index = 0, len = targets.length; index < len; index++) {
              const curName = targets[index];
              const newName = reNameNewControls.get(curName);
              if (!newName) {
                  return;
              }
              curTargets.push(newName);
          }
          cascasde.target = curTargets.join(',');
        });
      }
  }

}
