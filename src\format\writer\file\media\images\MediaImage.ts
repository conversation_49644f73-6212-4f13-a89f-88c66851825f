import { XmlComponent, XmlAttributeComponent } from '../../xml-components';
import { Content } from './MediaImageElements';

export interface IMediaImageAttributesProperties {
  name: string;
  bind?: string; // 用来绑定一些数据信息
  type?: number;
  vertAlign?: number;
}

class MediaImageAttributes extends XmlAttributeComponent<IMediaImageAttributesProperties> {
  protected xmlKeys: any = {
    name: 'name',
    bind: 'bind',
    type: 'type', // 存储音视频类型
    vertAlign: 'vertAlign',
  };
}

export class MediaImage extends XmlComponent {

  constructor(attrs: IMediaImageAttributesProperties) {
    super('Image');
    this.root.push(new MediaImageAttributes(attrs));
  }

  public addContent(content: string): MediaImage {
    this.root.push(new Content(content));
    return this;
  }

}

// tslint:disable-next-line: max-classes-per-file
export class AudioImage extends XmlComponent {
    constructor(attrs: IMediaImageAttributesProperties) {
        super('Audio');
        this.root.push(new MediaImageAttributes(attrs));
    }
    public addContent(content: string): AudioImage {
        this.root.push(new Content(content));
        return this;
    }
}

// tslint:disable-next-line: max-classes-per-file
export class VideoImage extends XmlComponent {
    constructor(attrs: IMediaImageAttributesProperties) {
        super('Video');
        this.root.push(new MediaImageAttributes(attrs));
    }
    public addContent(content: string): VideoImage {
        this.root.push(new Content(content));
        return this;
    }
}

// tslint:disable-next-line: max-classes-per-file
export class EditableMediaImage extends XmlComponent {
  constructor(attrs: IMediaImageAttributesProperties) {
    super('Painting');
    this.root.push(new MediaImageAttributes(attrs));
  }

  public addContent(content: string): MediaImage {
    this.root.push(new Content(content));
    return this;
  }

}
