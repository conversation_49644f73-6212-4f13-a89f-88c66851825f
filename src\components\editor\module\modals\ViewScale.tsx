import * as React from 'react';
import Dialog from '../../ui/Dialog';
import Input from '../../ui/Input';
import Radio from '../../ui/Radio';
import Button from '../../ui/Button';
import {message} from '../../../../common/Message';
import { VIEW_SCALE } from '../../../../common/commonDefines';
import {GlobalEvent as gEvent, GlobalEventName as gEventName} from '../../../../common/GlobalEvent';
import { IFRAME_MANAGER } from '../../../../common/IframeManager';

interface IProps {
    documentCore: any;
    visible: boolean;
    id: string;
    width: number;
    close: (name: string | number, bRefresh?: boolean) => void;
}

interface IState {
    bRefresh: boolean;
}

export default class ViewScale extends React.Component<IProps, IState> {
    private visible: boolean;
    private data: any[];
    private disabled: boolean;
    private view: any;
    private oldScale: number;
    constructor(props: any) {
        super(props);
        this.data = [{key: '页宽', value: 1}, {key: '百分比', value: 2}];
        this.view = {};
        this.state = {
            bRefresh: false,
        };
        this.visible = this.props.visible;
    }

    public render(): any {
        return (
            <Dialog
                visible={this.visible}
                width={300}
                open={this.open}
                title='缩放'
                footer={this.renderFooter()}
                id={this.props.id}
            >
                <div>
                    <div className='editor-line'>
                        <Radio value={this.view.type} data={this.data} name='type' onChange={this.onChange} />
                    </div>
                    <div className='editor-line'>
                        <Input
                            value={this.view.scale}
                            name='scale'
                            type='number'
                            disabled={this.disabled}
                            onChange={this.onChange}
                            onBlur={this.blur}
                        />
                    </div>
                </div>
            </Dialog>
        );
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        this.visible = nextProps.visible;
    }

    private renderFooter(): any {
        return (
            <span>
                <Button onClick={this.close}>取消</Button>
                <Button type='primary' onClick={this.confirm}>
                    确认
                </Button>
            </span>
        );
    }

    private open = (): void => {
        const scale = this.oldScale = Math.round(this.props.documentCore.getViewScale() * 100);
        this.view = {
            type: 2,
            scale,
        };
        this.disabled = false;
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private onChange = (value: any, name: string): void => {
        this.view[name] = value;
        if (name === 'type') {
            if (value === 1) {
                this.disabled = true;
            } else {
                this.disabled = false;
            }
            this.setState({bRefresh: !this.state.bRefresh});
        }
    }

    private blur = (e: any): void => {
        const scale = this.view.scale;
        if (typeof scale !== 'number' || scale.toString()
            .indexOf('.') === -1) {
            return;
        }
        this.view.scale = parseInt(this.view.scale, 10);
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private confirm = (id?: any): void => {
        let scale = this.view.scale;
        const documentCore = this.props.documentCore;
        if (this.view.type === 1) {
            const page = documentCore.getPageProperty();
            scale = (this.props.width - 40) / page.width;
        } else {
            IFRAME_MANAGER.setDocId(documentCore.getCurrentId());
            if (this.oldScale === scale) {
                this.close();
                return;
            }
            if (scale === null) {
                message.error('请输入比例值');
                return;
            }

            if (scale > VIEW_SCALE.max) {
                message.error('最大值不能超过' + VIEW_SCALE.max);
                return;
            }
            if (scale < VIEW_SCALE.min) {
                message.error('最小值不能小于' + VIEW_SCALE.min);
                return;
            }
            scale /= 100;
        }
        const obj = {
            result: 0,
        };
        gEvent.setEvent(documentCore.getCurrentId(), gEventName.ViewScale, scale, obj);
        // const res = documentCore.setViewScale(scale);

        this.close(obj.result === 0);
    }

    private close = (bRefresh?: boolean): void => {
        this.visible = false;
        this.props.close(this.props.id, bRefresh);
        this.setState({bRefresh: !this.state.bRefresh});
    }
}
