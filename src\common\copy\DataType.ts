export enum CopySource {
    CopySystem, // 来自系统的
    CopyOwnerEditor, // 本编辑器
    CopyOtherEditor,    // 本编辑外的编辑器
}

export enum PasteType {
    PasteCtrlV, // 键盘 ctrl + v
    PasteCtrlShiftV, // 键盘 ctrl + shift + v
    PasteClickCtrlV, // 通过复制面板来的点击事件
    PasteClickCtrlShiftV, // 通过复制面板来的点击事件
}

export enum CopyType {
    CopyCtrlC, // 键盘复制
    CopyCtrlX, // 键盘剪切
    CopyClickCtrlC, // 复制面板复制
    CopyClickCtrlX, // 复制面板剪切
}

export enum ContentType {
    TextPlain = 'text/plain', // 纯文本格式
    TextHtml = 'text/html', // 网页格式
    TextRtf = 'text/rtf', // word等文档格式
    TextApollo = 'text/Apollo', // 阿波罗格式
    FileImage = 'image', // 图片格式(从word等文档中单独选择图片进行粘贴)
}

export enum SaveFileType {
    Apollo = 'dataType;Apollo;V1;1001;AA',
}

export let embedData = {
    AccessReg: /<\:([0-9A-Za-z_]+)\:>$/,
    Reg: /<span data-text="([^"]*?)" id="<:([0-9A-Za-z_]+):>"[^>]*?>[\s\S]*?<\/span>/,
    // tslint:disable-next-line: no-invalid-template-strings
    setNode: (content: string, text: string, id: string): string => {
        return `<span data-text="${content}" id="<:${id}:>" class="Embed-Data">${text}</span>`;
    },
};
