import { MouseEventType } from '../../common/MouseEventHandler';
import { TableCell } from './Table/TableCell';
import { NewControl } from './NewControl/NewControl';
import Paragraph from './Paragraph';

/**
 * 选择对象所在区域或类型
 */
export enum SelectionFlagType {
    Common,  // 选择区域在正文部分
    Drawing,
}

export interface ISelectionData {
    x: number;  // 选择的x坐标
    y: number;  // 选择的y坐标
    pageNum: number;  // 页码
}

export interface ITableCellPos {
    rowIndex: number;
    cellIndex: number;
}

/**
 * 拖放状态
 */
export class DragDropState {
    public flag: number;   // 0:不要拖放，需要check，1: 拖放，-1: 不检查拖放
    public data: ISelectionData;  // 拖放对象

    constructor() {
        this.flag = 0;
        this.data = null;
    }

    public reset(): void {
        this.flag = 0;
        this.data = null;
    }
}

// document selection
export default class DocumentSelection {
    public bUse: boolean;
    public bStart: boolean; // 开始选择
    public bWordSelected: boolean; //

    public flag: SelectionFlagType;

    public startPos: number; // 段落开始
    public endPos: number;  // 段落结束
    public dragDrop: DragDropState;  // 拖放
    public data: {
        bTableBorder: boolean,  // 是否选中表格边框
        pos: number,            // 当前元素索引
        bSelected: boolean,     // 是否已有选中内容
    };

    constructor() {
        this.bUse = false;
        this.bStart = false;
        this.startPos = 0;
        this.endPos = 0;
        this.data = null;
        this.bWordSelected = false;
        this.flag = SelectionFlagType.Common;
        this.dragDrop = new DragDropState();
    }
}

/**
 * 段落选择类
 */
// tslint:disable-next-line: max-classes-per-file
export class ParagraphSelection {
    public bUse: boolean;  // 是否在进行选择
    public bStart: boolean;  // 是否开始选择

    public startPos: number; // 开始选择时的文本内容位置
    public endPos: number; // 结束选择时的文本内容位置

    public flag: SelectionFlagType;
    constructor() {
        this.bUse = false;
        this.bStart = false;
        this.startPos = 0;
        this.endPos = 0;
        this.flag = SelectionFlagType.Common;
    }
}

/**
 * portion
 */
// tslint:disable-next-line: max-classes-per-file
export class PortionSelection {
    public bUse: boolean;
    public startPos: number;
    public endPos: number;

    constructor() {
        this.bUse = false;
        this.startPos = 0;
        this.endPos = 0;
    }

}

// tslint:disable-next-line: max-classes-per-file
export class TableSelectionPosition {
    public pos: ITableCellPos;

    public x: number;
    public y: number;
    public pageIndex: number;
    public mouseEvent: {
        clickCount: number;
        type: MouseEventType;
        bCtrlKey: boolean;
    }; // TableSelectionMouseEvent;

    constructor() {
        this.x = 0;
        this.y = 0;
        this.pageIndex = 0;
        this.pos = {rowIndex: 0 , cellIndex: 0};
        // this.mouseEvent = new TableSelectionMouseEvent();
    }

    public copy(): TableSelectionPosition {
        const ts = new TableSelectionPosition();
        ts.pos = {
            rowIndex: this.pos.rowIndex,
            cellIndex: this.pos.cellIndex,
        };

        ts.x = this.x;
        ts.y = this.y;
        ts.pageIndex = this.pageIndex;

        if ( this.mouseEvent ) {
            ts.mouseEvent = {
                clickCount: this.mouseEvent.clickCount,
                type: this.mouseEvent.type,
                bCtrlKey: this.mouseEvent.bCtrlKey,
            };
        }

        return ts;
    }
}

// tslint:disable-next-line: max-classes-per-file
export class TableSelection {
    public bUse: boolean;
    public bStart: boolean;
    public type: TableSelectionType;
    public type2: TableSelectionType;
    public startPos: TableSelectionPosition;
    public endPos: TableSelectionPosition;
    // 用于选中：cell，cells，row，rows
    public data: ITableCellPos[];
    public data2: TableCell | ITableSelectionData;
    public curRow: number;

    constructor() {
        this.bUse = false;
        this.bStart = false;
        this.startPos = new TableSelectionPosition();
        this.endPos = new TableSelectionPosition();
        this.data = null;
        this.data2 = null;
        this.curRow = 0;
    }
}

// export interface ITableSelectionPosIndex {
//     rowIndex: number;
//     cellIndex: number;
// }

// export interface TableSelectionMouseEvent {
//     clickCount: number,
//     type: MouseEventType,
//     bCtrlKey: boolean,
// }

export interface ITableSelectionData {
    x?: number;
    y?: number;

    min?: number;
    max?: number;

    startX?: number;
    startY?: number;

    startColumnX?: number;
    startColumnY?: number;

    pageNum?: number;
    index?: number;

    bStart?: boolean;
    bColumn?: boolean;       // 是否选中列边框：左右边框

    pos?: ITableCellPos;
}

export enum TableSelectionType {
    Common = 0x00,
    Text,
    TableCell,
    TableBorder,
    TableCells,
    TableRows,
    TableColumns,
    TableBorderInnerTable,
}

export interface ICheckSelectionNewControls {
    para: Paragraph;
    bInsertNewControlsBorder: boolean;
    behindOverNewControls: NewControl[];
    forwardOverNewControls: NewControl[];
    direction?: number;
}
