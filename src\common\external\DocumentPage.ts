import { DocumentCore } from '../../model/DocumentCore';
import { PageProperty } from '../../model/StyleProperty';
import { PAGE_FORMAT, ResultType, VIEW_SCALE } from '../commonDefines';
import { EmrEditor } from '../../components/editor/Main';
import { getMMFromPx, getPxForMM } from '../../model/core/util';
import { ExternalAction } from './ExternalAction';
import {GlobalEvent as gEvent, GlobalEventName as gEventName} from '../GlobalEvent';

export default class DocumentPage extends ExternalAction {
    private _documentCore: DocumentCore;
    private _host: EmrEditor;
    constructor(host: EmrEditor) {
        super(host.state.documentCore);
        this._host = host;
        this._documentCore = host.state.documentCore;
    }

    public getPageCount(): number {
        return this._documentCore.getPageCount();
    }

    public getCurrentCursorPage(): number {
        const res = this._documentCore.getCurPageIndex() + 1;
        if (!res) {
            return ResultType.NumberNaN;
        }

        return res;
    }

    /**
     * 设置文字水印
     * @param sText 文字水印
     * @param nMode 1 – 宽松型   2 – 紧凑型
     */
    public setTextWaterMark(sText: string, nMode: number, colorType?: number): number {
        if (typeof sText !== 'string' || ![1, 2].includes(nMode)) {
            return ResultType.ParamError;
        }
        const res = this._documentCore.setTextWaterMark(sText, nMode - 1, colorType);
        if (res === ResultType.Success) {
            this._host.handleRefresh();
        }
        return res;
    }

    /**
     * 删除文档的文字水印
     */
    public deleteTextWaterMark(): number {
        const res = this._documentCore.deleteTextWaterMark();
        if (res === ResultType.Success) {
            this._host.handleRefresh();
        }
        return res;
    }

    /**
     * 判断当前文档是否有水印
     */
    public hasTextWaterMark(): boolean {
        const res = this._documentCore.hasTextWaterMark();
        return res;
    }

    /**
     * 获取文档显示比例
     */
     public getViewProportion(): number {
        return Math.round(this._documentCore.getViewScale() * 100);
    }

    /**
     * 文档显示比例设置
     * @param nType 显示比例类型
     * @param nValue 显示比例的值
     */
    public setViewProportion(nType: number, nValue?: number): number {
        let scale: number;
        switch (nType) {
            case 1: {
                const page = this._documentCore.getPageProperty();
                scale = (this._host.myRef.current.clientWidth - 40) / page.width;
                break;
            }
            case 4: {
                scale = nValue / 100;
                if (isNaN(scale)) {
                    return ResultType.ParamError;
                }
                const max = Math.round(VIEW_SCALE.max / 100);
                if (scale > max) {
                    return ResultType.ParamError;
                }
                const min = VIEW_SCALE.min / 100;
                if (scale < min) {
                    return ResultType.ParamError;
                }
                break;
            }
            default:
            return ResultType.ParamError;
        }
        const option = {
            result: 0,
        };
        gEvent.setEvent(this._host.docId, gEventName.ViewScale, scale, option);

        return option.result;
    }

    //单位cm
    public getPageMargin(): { fWidth: number; fHeight: number; fPageLeft: number; fPageRight: number; fPageTop: number; fPageBottom: number } {
        const page = this._documentCore.getPageProperty();
        return {
            fWidth: getMMFromPx(page.width / 10),
            fHeight: getMMFromPx(page.height / 10),
            fPageLeft: getMMFromPx(page.paddingLeft / 10),
            fPageRight: getMMFromPx(page.paddingRight / 10),
            fPageTop: getMMFromPx(page.paddingTop / 10),
            fPageBottom: getMMFromPx(page.paddingBottom / 10),
        };
    }

    public setPageMargin(fPageLeft: number, fPageRight: number, fPageTop: number, fPageBottom: number): number {
        if (typeof fPageLeft !== 'number' || typeof fPageRight !== 'number' || typeof fPageTop !== 'number'
        || typeof fPageBottom !== 'number') {
            return ResultType.ParamError;
        }
        if (fPageLeft <= 0 || fPageRight <= 0 || fPageTop <= 0 || fPageBottom <= 0) {
            return ResultType.ParamError;
        }

        const page = new PageProperty();
        page.paddingLeft = getPxForMM(fPageLeft * 10);
        page.paddingRight = getPxForMM(fPageRight * 10);
        page.paddingTop = getPxForMM(fPageTop * 10);
        page.paddingBottom = getPxForMM(fPageBottom * 10);

        
        const result = this._documentCore.setPageProperty(page);
        
        if (result === ResultType.Success) {
            this._host.handleRefresh();
        }

        return result;
    }

    public setPageFormat(nPageFormat: number, fPageWidth: number, fPageHeight: number, bHorOrVer: boolean,
                         nPageLayOut: number): number {
        if (nPageFormat !== 3 && nPageFormat !== 4 && nPageFormat !== 11) {
            return ResultType.ParamError;
        }
        const page = new PageProperty();
        let pagePro: number[];
        switch (nPageFormat) {
            case 3:
                pagePro = PAGE_FORMAT.A3;
                page.width = getPxForMM(pagePro[0] * 10);
                page.height = getPxForMM(pagePro[1] * 10);
                break;
            case 4:
                pagePro = PAGE_FORMAT.A4;
                page.width = getPxForMM(pagePro[0] * 10);
                page.height = getPxForMM(pagePro[1] * 10);
                break;
            case 11:
                if (fPageHeight <= 0 || fPageWidth <= 0) {
                    return ResultType.ParamError;
                }

                page.width = getPxForMM(fPageWidth * 10);
                page.height = getPxForMM(fPageHeight * 10);
                break;
            default:
                return ResultType.ParamError;
        }
        
        const result = this._documentCore.setPageProperty(page);
        
        if (result === ResultType.Success) {
            this._host.handleRefresh();
        }
        return result;
    }
}
