import {ParaElementType} from './ParagraphContent';
import TextProperty from '../TextProperty';
import { ChangeParaElementViewSecret, ChangeParaElementVisible } from './PortionChange';
import History from '../History';

export abstract class ParaElementBase {
  public content: string;
  // public src: string;
  // public height: number;
  public width: number; // 文本本身宽度
  public widthVisible: number;  // 绘制时的可见文本宽度：两端对齐时会发生变化
  public bVisible: boolean;   // 是否显示文本
  public bViewSecret: boolean;    //

  public positionX: number;  // UI：绘制坐标X
  public positionY: number;  // UI：绘制坐标Y
  public dy: number; // y上的偏移量，垂直对齐使用

  public type: ParaElementType;  // 文本类型

  protected constructor() {
    this.content = null;
    this.width = 0;
    this.widthVisible = 0;
    this.bVisible = true;
    this.bViewSecret = false;

    this.type = ParaElementType.ParaText;

    this.positionX = 0;
    this.positionY = 0;
  }

  public abstract measure(textPr: TextProperty, text?: string): number;

  public abstract copy(bFlag: boolean): ParaElementBase;

  public isParaEnd(): boolean {
    return false;
  }

  public isSoftLine(): boolean {
    return false;
  }

  public canBeAtBeginOfLine(): boolean {
    return true;
  }

  public canBeAtEndOfLine(): boolean {
      return true;
  }

  public isSpaceAfter(): boolean {
    return true;
  }

  public saveRecalculateObject( bCopy: boolean ): ParaElementBase {
    return null;
  }

  public isEastAsianScript(): boolean {
    return false;
  }

  public loadRecalculateObject( obj: any ): void { return; }

  /**
   * 当做字符
   */
  public isInline(): boolean {
    return true;
  }

  public getNewControlName(): string {
    return null;
  }

  public isNewControlStartBoder(): boolean {
    return null;
  }

  public isNewControlEndBoder(): boolean {
    return null;
  }

  /**
   * 设置当前文本是否为隐私显示
   * @param bViewSecret
   */
  public setViewSecret( bViewSecret: boolean, history: History ): void {
    if ( bViewSecret === this.bViewSecret || ParaElementType.ParaNewLine === this.type) {
        return ;
    }

    if ( history) {
      history.addChange(new ChangeParaElementViewSecret(this, this.bViewSecret, bViewSecret));
    }
    this.bViewSecret = bViewSecret;
  }

  public setParaIndex( paraId: number ): void {
    //
  }

  public setPortionId( portionId: number ): void {
    //
  }

  public setVisible(bVisible: boolean, history: History): void {
    if ( bVisible === this.bVisible ) {
      return ;
    }

    if ( history) {
      history.addChange(new ChangeParaElementVisible(this, this.bVisible, bVisible));
    }
    this.bVisible = bVisible;
  }

  public isVisible(): boolean {
    return this.bVisible;
  }

  public isParaTab(): boolean {
    return false;
  }

  public isPopWinNewControl(): boolean {
      return false;
  }

  public isImage(): boolean {
    return false;
  }

  public isSpace(): boolean {
    return false;
  }

  public isButton(): boolean {
    return false;
  }

  public isMedEquation(): boolean {
    return false;
  }

  public isSvgDrawing(): boolean {
    return false;
  }

  public getType(): ParaElementType {
    return this.type;
  }

  public isParaPageNum(): boolean {
    return false;
  }

  public shift(shiftDx: number, shiftDy: number): void { return; }

  public getRemainWidth(): number { return 0; }

  public setRemainWidth(width: number): void { return; }

  public repalceText(text: string, history?: History): void { return ; }
  // public isSymbol(): boolean {
  //   if ( /[\uff00-\uffef]/.test(this.content) || /[\u3000-\u303f]/.test(this.content) ||
  //      -1 !== ('(\[{£¥·‘“〈《「『【〔〖〝﹙﹛﹝＄．［｛￡￥,.\';{}<()>+?=-*&^%$@#!\/~\\|').indexOf(this.content) ) {
  //     return true;
  //   }

  //   return false;
  // }

  public isParaSoftLine(): boolean {
    return (ParaElementType.ParaNewLine === this.type);
  }
}

export enum RepalceText {
  Space = '\u0020',
  SpaceForUI = '\u00a0',
  Hyphen1 = '\u002D',
  Hyphen2 = '\u2014',
}
