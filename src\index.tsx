
import { createRoot } from 'react-dom/client';
import * as React from 'react';
import EditorInterface, { IExternalEvent } from './common/external/IExternalInterface';
import IAsyncExternalInterface from './common/external/IAsyncExternalInterface';
import { EmrEditor } from './components/editor/Main';
import { WasmInstance } from './common/WasmInstance';
import { setTheme } from '@hz-editor/theme';

let dataIndex: number = 1;
export default class Editor {
  private _editor: any;
  private _options: any;
  private _div: any;
  private _iframe: any;
  private _visible: boolean;
  private root:any;

  constructor() {
    // options.bShowMenu = true;
    // options.bShowToolbar = true;
  }

  public getEditor(): EditorInterface {
    return this._editor.getEditor();
  }

  public getAsyncEditor(): IAsyncExternalInterface {
    return this._editor.getAsyncEditor();
  }

  public setEvent(events: IExternalEvent): any {
    this._editor.setEvent(events);
    return this;
  }

  public removeEvent(sNames?: string[]): boolean {
    return this._editor.removeEvent(sNames);
  }

  public hideEditor(): void {
    if (this._options.dom) {
      this._editor.setVisible(false);
      this._options.dom.innerHTML = '';
    }
  }

  public showEditor(): void {
    document.body.appendChild(this._iframe);
    const div = this.addContainer();
    div.parentNode.replaceChild(this._div, div);
    this._editor.setVisible(true);
  }

  public init(options: any): Promise<any> {
    return new Promise((resolve, reject) => {
      options = this._options = options || {};
      const dom = options.dom;
      if (dom) {
        const iframe = this._iframe = document.createElement('iframe');
        iframe.width = '100%';
        iframe.height = '100%';

        iframe.style.border  = 'none';
        iframe.src = 'about:blank';
        iframe.setAttribute('scrolling', 'no'); 

        iframe.id = 'hz-editor-wrap-iframe';
        dom.appendChild(iframe);
        const iframeDocument = iframe.contentDocument;
        const div = this._div = iframeDocument.createElement('div');
        div.id = 'editor-container-id';
        div.setAttribute('data-index', (dataIndex++).toString());
        iframeDocument.body.appendChild(div);
        if (Editor['emrEditorCss']) {
          const style = iframeDocument.createElement('style');
          style.textContent = Editor['emrEditorCss'];
          iframeDocument.head.appendChild(style);
        }

        // 使用优化后的方法确保WASM已初始化
      if (WasmInstance.isWasmReady()) {
        this.initEditor(options, iframe, div);
        resolve(this);
        return;
      }

        WasmInstance.createWasmInstsanceAsync()
          .then((instance: any) => {
            this.root = this.initEditor(options, iframe, div);
            resolve(this);
          });
      }
    });
  }

  private close = () => {
    // this._editor = {};
    if (this._visible === false && this._iframe) {
        document.body.appendChild(this._iframe);
        this._iframe.contentDocument.body.appendChild(this._div);
        
        if (this.root) {
            this.root.unmount(); // 使用 root.unmount 替代 ReactDOM.unmountComponentAtNode
            this.root = null; // 清除 root 引用
        }

        this._iframe.outerHTML = '';
    }
    this._options = null;
    this._div = null;
    this._iframe = null;
    this._editor = null;
}

  // private close = () => {
  //   // this._editor = {};
  //   if (this._visible === false && this._iframe) {
  //     document.body.appendChild(this._iframe);
  //     this._iframe.contentDocument.body.appendChild(this._div);
  //     ReactDOM.unmountComponentAtNode(this._div);
  //     this._iframe.outerHTML = '';
  //   }
  //   this._options = null;
  //   this._div = null;
  //   this._iframe = null;
  //   this._editor = null;
  // }

  // private initEditor(options: any, iframe: any, div: HTMLElement): void {
  //   if (options.theme) {
  //     setTheme(options.theme);
  //   }
  //   const id = options.id || 'text-id-1';
  //   delete options.id;
  //   delete options.dom;
  //   delete options.theme;
  //   const editor: EmrEditor = ReactDOM.render(
  //     // tslint:disable-next-line: jsx-wrap-multiline
  //     <EmrEditor
  //       win={iframe.contentWindow}
  //       {...options}
  //       id={id}
  //       iframe={iframe}
  //       close={this.close}
  //     />,
  //     div,
  //   ) as any;

  //   const _this = this;
  //   this._editor = {
  //     getEditor: () => {
  //       return editor.getEditor();
  //     },
  //     getAsyncEditor: () => {
  //       return editor.getAsyncEditor();
  //     },
  //     setEvent: (events: IExternalEvent): void => {
  //       editor.setEditEvent(events);
  //     },
  //     removeEvent(sNames?: string[]): boolean {
  //       return editor.removeEvent(sNames);
  //     },
  //     setVisible(flag: boolean): void {
  //       _this._visible = flag;
  //       editor.setVisible(flag, _this._iframe);
  //     },
  //   };
  // }

  private initEditor(options: any, iframe: any, div: HTMLElement): any {
    if (options.theme) {
        setTheme(options.theme);
    }
    const id = options.id || 'text-id-1';
    delete options.id;
    delete options.dom;
    delete options.theme;

    const editorRef = React.createRef<EmrEditor>();

    const root = createRoot(div);
    root.render(
        <EmrEditor
            ref={editorRef}
            win={iframe.contentWindow}
            {...options}
            id={id}
            iframe={iframe}
            close={this.close}
        />
    );

    const editor = editorRef.current;
    const _this = this;

    if (editor) {
        this._editor = {
            getEditor: () => {
                return editor.getEditor();
            },
            getAsyncEditor: () => {
                return editor.getAsyncEditor();
            },
            setEvent: (events: IExternalEvent): void => {
                editor.setEditEvent(events);
            },
            removeEvent(sNames?: string[]): boolean {
                return editor.removeEvent(sNames);
            },
            setVisible(flag: boolean): void {
                _this._visible = flag;
                editor.setVisible(flag, _this._iframe);
            },
        };
    }
    return root;
}

  private addContainer(bRenderStyle: boolean = true): HTMLDivElement {
    const iframeDocument = this._iframe.contentDocument;
    const div = iframeDocument.createElement('div');
    div.id = 'editor-container-id';
    iframeDocument.body.appendChild(div);
    if (bRenderStyle && Editor['emrEditorCss']) {
      const style = iframeDocument.createElement('style');
      style.innerHTML = Editor['emrEditorCss'];
      style.type = 'text/css';
      iframeDocument.head.appendChild(style);
    }

    return div;
  }
}
