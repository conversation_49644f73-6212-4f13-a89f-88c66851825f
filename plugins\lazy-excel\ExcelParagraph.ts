import { RichText } from 'exceljs';
import { ISerialParaObj, ISerialTextObj, SerialObjType } from '../serialize/serialInterface';

export default class ExcelParagraph {
    constructor(private para: ISerialParaObj) {

    }

    public toRichText(): RichText[] {
        const richTexts: RichText[] = [];
        const para = this.para;
        for (const portion of para.children) {
            if (portion.type === SerialObjType.Portion) {
                for (const content of portion.children) {
                    if (content.type === SerialObjType.Text) {
                        richTexts.push(this.textPortionToRich(content as any));
                    }
                }
            }
        }
        return richTexts;
    }

    private textPortionToRich(portion: ISerialTextObj): RichText {
        const text: RichText = {
            text: portion.text,
            font: {
                bold: portion.bold,
                italic: portion.italics,
                name: portion.font as string,
                underline: portion.underline ? 'single' : false,
                vertAlign: portion.subScript ? 'subscript' :
                    portion.superScript ? 'superscript' : undefined,
                color: portion.color ? { argb: portion.color.replace('#', '')} : undefined,
                size: portion.size && portion.size / 2 || undefined,
            },
        };

        return text;
    }
}
