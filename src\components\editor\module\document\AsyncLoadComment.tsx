import { message } from '@/common/Message';
import * as React from 'react';

interface IProps {
    host: any;
    documentCore: any;
    props: any;
    visible: boolean;
}

export class AsyncLoadComment extends React.Component<IProps, {}> {
    private _component: any;
    private _top: number;
    private _bspanRef: boolean;
    constructor(props: IProps) {
        super(props);
    }

    public render(): any {
        const {visible} = this.props;
        if (visible !== true) {
            return null;
        }

        if (!this._component) {
            this.importComponent();
            return this.loadInfo();
        }

        const {documentCore, props, host} = this.props;
        const pageInfo = props.pageInfo;
        const pageHeight = props.pageHeight;
        const addCommentFlag = props.addCommentFlag;
        const commentStatusInfo = props.commentStatusInfo; // documentCore.getCommentStatusInfo();
        // if (!obj.hasOwnProperty('id')) {
        //     obj['id'] = name;
        // }

        // if (!obj.hasOwnProperty('visible')) {
        //     obj['visible'] = host[name];
        // }

        const newPageInfo: any = {
            ...pageInfo,
            scale: host._scale,
            firstPageHeight: pageHeight,
            firstPageHeader: documentCore.getPagePositionInfo(0).y,
        };

        if (props.pageCount > 1) {
            newPageInfo.otherPageHeight = pageHeight;
            newPageInfo.otherPageHeader = documentCore.getPagePositionInfo(1).y;
        }

        const outerStyle: any = {
            width: pageInfo.width * host._scale + 'px',
            position: 'absolute',
            top: '0',
            left: '0',
            right: '0',
            margin: '0 auto',
        };
        let top = this._top;
        if (top === undefined) {
            const position = host.getCursorPosition() || {};
            top = position.pageNum * pageHeight + position.y1;
            // if (position.pageNum > 0) {
            //     top += (position.pageNum - 1) * 20;
            // }
        }

        return (
            <div className='commentContainer' key={'comment'} style={outerStyle}>
                {React.createElement(this._component[0], {
                    pageInfo: newPageInfo,
                    refresh: host.refresh,
                    userName: commentStatusInfo.userName,
                    bShowPanel: commentStatusInfo.bShowPanel,
                    documentCore,
                    bShowNewComment: addCommentFlag,
                    top
                })}
                {React.createElement(this._component[1], {
                    pageInfo: newPageInfo,
                    refresh: host.refresh,
                    bShowAnnotation: commentStatusInfo.bShowAnnotation,
                    documentCore
                })}
            </div>
        );
    }

    public componentDidCatch(error: any, info: any): void {
        // tslint:disable-next-line: no-console
        console.log(error, info);
    }

    private loadInfo(): any {
        setTimeout(() => {
            if (!this._component) {
                message.info('文件加载中', {time: 9000})
                .then(() => {
                    if (!this._component) {
                        message.error('文件加载异常', {time: 1000});
                        this.props.host._bAsyncLoadComment = false;
                    }
                });
            }
        }, 2000);
        return (<span />);
    }

    private async importComponent(): Promise<void> {
        const arrs = [];
        arrs.push(import('./ParaCommentLayer'));
        arrs.push(import('./ParaCommentSymbol'));
        const res = await Promise.all(arrs);
        message.close();
        this._component = {};
        res.forEach((item, index) => {
            this._component[index] = item.default;
        });
        this.setState({});
    }
}
