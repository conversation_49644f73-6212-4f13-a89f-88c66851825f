import { RecalcResultType } from './Document';

export class DocumentRecalculateState {
    public id: number = null;
    public pageIndex: number = 0; // 页面索引
    public sectionIndex: number = 0;
    public bStart: boolean = true; // 标志，是否第一次计算此页面
    public startIndex: number = 0; // 开始排版的元素索引
    public startPage: number = 0;  // 开始排版的页面索引
    public bResetStartElement: boolean = false; // 排版时的元素是否需要重置可编辑区域等：比较上一个元素的SectionProperty
    public mainStartPos: number = -1;
    public bBreakStartElement: boolean = false;

    constructor() {
        //
    }

    public reset(): void {
        this.pageIndex = 0;
        this.sectionIndex = 0;
        this.bStart = true;
        this.startIndex = 0;
        this.startPage = 0;
        this.bResetStartElement = false;
        this.mainStartPos = -1;
    }
}

export class DocumentRecalcInfo {
    public recalcResult: RecalcResultType;

    constructor() {
        this.recalcResult = RecalcResultType.RecalResultNextElement;
    }

    public reset(): void {
        this.recalcResult = RecalcResultType.RecalResultNextElement;
    }

    public canRecalcObject(): boolean {
        return true;
    }
}
