import { XmlComponent } from '../../xml-components';
import { RunProperties } from '../../paragraph/run/properties';
import { Size } from '../../paragraph/run/formatting';
import { RunFonts } from '../../paragraph/run/run-fonts';

export class RunPropertiesDefaults extends XmlComponent {
    // private readonly properties: RunProperties;
    private properties: RunProperties;

    constructor() {
        super('w:rPrDefault');
        // this.properties = new RunProperties();
        // this.root.push(this.properties);
    }

    public size(size: number): RunPropertiesDefaults {
        this.initializeProperties();
        this.properties.push(new Size(size));
        // this.properties.push(new SizeComplexScript(size));
        return this;
    }

    public font(fontName: string): RunPropertiesDefaults {
        this.initializeProperties();
        this.properties.push(new RunFonts(fontName));
        return this;
    }

    /**
     * initialize run properties if not
     */
    private initializeProperties(): void {
        if (!this.properties) {
            this.properties = new RunProperties();

            // should be the first child element
            // this.root.push(this.properties);
            this.root.unshift(this.properties);
        }
    }
}
