import { exportText } from './commonDefines';

/**
 * 是否开启了测试模式，开启了将会输出一些比较机密的数据，用来调试
 * @returns 是否
 */
export function isGlobalTestData(): boolean {
    if (window['__EMR_EDITOR_VER__TEST'] === true) {
        return true;
    }

    return false;
}

const code = 97;
const logs = [];
const dirLogs = [];
function printLog(): void {
    dirLogs.forEach((log) => {
        // tslint:disable-next-line: no-console
        console.dir(log);
    });

    logs.forEach((log) => {
        // tslint:disable-next-line: no-console
        console.log.apply(undefined, log);
    });
    logs.length = 0;
    dirLogs.length = 0;
}

let timeout;

/**
 * 在console.log输出数据
 * @param args 输出数据
 */
export function consoleLog(...args: any[]): void {
    logs.push(args);
    clearTimeout(timeout);
    timeout = setTimeout(() => {
        printLog();
    }, 1000);
}

export function consoleDir(arg: any): void {
    dirLogs.push(arg);
    clearTimeout(timeout);
    timeout = setTimeout(() => {
        printLog();
    }, 1000);
}

// tslint:disable-next-line:sy-global-const-name
export const editorCallback = (type: number) => {
    const funcs = editorCallback.funcs;
    if (funcs && funcs.length) {
        funcs.forEach((func) => {
            if (typeof func === 'function') {
                func(type);
            }
        });
    }
};


editorCallback.data = {};
editorCallback.funcs = null;
editorCallback.errorSetItem = null;
editorCallback.errorGetItem = null;
editorCallback.errorRemoveItem = null;
editorCallback.text = null;

editorCallback.setItem = (key: string, value: string): any => {
    try {
        localStorage.setItem(key, value);
        editorCallback.data[key] = value;
    } catch (e) {
        if (typeof editorCallback.errorSetItem === 'function') {
            return editorCallback.errorSetItem(key, value);
        }
    }
};

editorCallback.getItem = (key: string): any => {

    if (editorCallback.data[key]) {
        return editorCallback.data[key];
    }
    try {
        return  editorCallback.data[key] = localStorage.getItem(key);

    } catch (error) {
        if (typeof editorCallback.errorSetItem === 'function') {
            return editorCallback.errorGetItem(key);
        }
    }
};

editorCallback.removeItem = (key: string): any => {

    delete editorCallback.data[key];
    try {
        localStorage.removeItem(key);

    } catch (error) {
        if (typeof editorCallback.errorSetItem === 'function') {
            return editorCallback.errorRemoveItem(key);
        }
    }
};

editorCallback.setFuncs = (func): void => {
    editorCallback.funcs = func;
};
let funca;
editorCallback.addFunc = (func): void => {
    funca = func;
};
editorCallback.triggerText = (text) => {
    if (typeof funca === 'function') {
        funca(text);
    }
};

window['editorCallback'] = editorCallback;

Object.defineProperty(editorCallback, 'text', {
    set(value: string): void {
        if (!value) {
            delete this._text;
            return;
        }
        exportText(code, value);
        this._text = btoa(encodeURIComponent(value));
    },
    get(): string {
        if (!this._text) {
            return '';
        }
        return decodeURIComponent(atob(this._text));
    }
});
