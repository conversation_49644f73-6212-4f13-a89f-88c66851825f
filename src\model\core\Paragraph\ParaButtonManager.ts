import { IParaButtonProp, ResultType } from '@/common/commonDefines';
import ParaButton from './ParaButton';
import Document from '../Document';
import Paragraph from '../Paragraph';
import { ChangeBaseContent } from '../HistoryChange';
import { HistoryDescriptionType, HistroyItemType } from '../HistoryDescription';
import ContentChanges, { ContentChangesElement } from '../ContentChanges';

// interface IDocNumbered {
//     num: Numbered;
//     paras: DocumentContentElementBase[];
// }
export class ButtonManager {
    private _doc: Document;
    private _datas: Map<string, ParaButton>;
    private _caches: Map<string, ParaButton>;
    private _strcutDatas: Map<string, any>;
    private _strcutCaches: Map<string, any>;
    private contentChanges: ContentChanges;
    constructor(doc: any, option: {datas: any, caches: any}) {
        this._doc = doc;
        this._datas = new Map();
        this._strcutDatas = option.datas;
        this._strcutCaches = option.caches;
        this.contentChanges = new ContentChanges();
    }

    public getButtonByName(name: string): ParaButton {
        return this._datas.get(name);
    }

    public updateButtonName(name: string, newName: string): number {
        if (!newName || name === newName) {
            return ResultType.UnEdited;
        }

        if (this._datas.has(newName)) {
            return ResultType.Failure;
        }

        const button = this._datas.get(name);
        button.name = newName;
        this._datas.delete(name);
        this._datas.set(newName, button);
        return ResultType.Success;
    }

    public getMap(): Map<string, ParaButton> {
        return this._datas;
    }

    public setButton(button: ParaButton): void {
        this._datas.set(button.name, button);
        const history = this._doc.getHistory();
        if ( history ) {
            history.addChange(new ChangeButtonManagerAddParaButton(this, undefined, [button]));
        }
    }

    public addCaches(map: Map<string, ParaButton>): void {
        this._caches = map;
    }

    public addCache(button: ParaButton): void {
        if (!this._caches) {
            this._caches = new Map();
        }
        this._caches.set(button.name, button);
    }

    public deleteButton(button: ParaButton): void {
        this._datas.delete(button.name);
        const history = this._doc.getHistory();
        if ( history ) {
            history.addChange(new ChangeButtonManagerRemoveParaButton(this, undefined, [button]));
        }
    }

    public deleteButtonByName(name: string): number {
        const button = this._datas.get(name);
        if (!button) {
            return ResultType.UnEdited;
        }

        button.portion.selectTopPos();
        this._doc.removeSelection();
        const index = button.portion.content.findIndex((item) => item === button);
        if (index !== -1) {
            button.portion.portionContentPos = index;
            const parent = button.portion.paragraph.parent.getTopDocument();
            parent.remove(1, false, false, true);
            return ResultType.Success;
            // parent.recalculate();
            // parent.updateCursorXY();
        }
        // this.deleteButton(button);
        
        return ResultType.Failure;
    }

    public clearCaches(bAdd?: boolean): void {
        if (bAdd === true && this._caches) {
            for (const [name, button] of this._caches) {
                this._datas.set(name, button);
            }
        }
        this._caches = null;
    }

    public addButton(props: IParaButtonProp): string {
        const name = this.makeUniqueName(props.name);
        props.name = name;
        const para: Paragraph = this._doc.getCurrentParagraph() as Paragraph;
        if (!para) {
            return '';
        }
        this._doc.startAction(HistoryDescriptionType.DocumentEnterButton);
        const portion = para.getElementByPos(para.getParaContentPos(), true);

        const button = new ParaButton(portion, props);
        portion.add(portion.portionContentPos, button, true);
        this._datas.set(name, button);
        this._doc.recalculate();
        this._doc.updateCursorXY();
        const history = this._doc.getHistory();
        if ( history ) {
            history.addChange(new ChangeButtonManagerAddParaButton(this, undefined, [button]));
        }
        this._doc.endAction();
        return name;
    }

    public addContentChanges(changes: ContentChangesElement): void {
        this.contentChanges.add(changes);
    }

    public clear(): boolean {
        if (!this._datas.size) {
            return false;
        }
        this._datas.clear();
        return true;
    }

    /**
     * 插入时，检查名称的合法性
     * @param newControlName
     */
    public checkButtonName(name: string): boolean {
        if (!name || this._datas.has(name) || this._caches?.has(name)) {
            return false;
        }

        return true;
    }

    public makeUniqueName(name: string, map?: Map<string, ParaButton>): string {
        if (true === this.checkName(name)) {
            return name;
        }

        if (!map && this._caches) {
            map = this._caches;
        }

        const datas = this._datas;
        let curName: string = 'structbutton';
        for (let number = 1; number < Number.MAX_SAFE_INTEGER; number++) {
            const temp = curName + number;
            if (map && map.has(temp)) {
                continue;
            }
            if (!datas.has(temp) ) {
                curName = temp;
                break;
            }
        }

        return curName;
    }

    private checkName(name: string): boolean {
        if (!name || this._datas.has(name) || this._caches?.has(name) || this._strcutDatas.has(name) || this._strcutCaches?.has(name)) {
            return false;
        }

        return true;
    }
}

export class ChangeButtonManagerAddParaButton extends ChangeBaseContent {
    constructor( changeClass: ButtonManager, pos: number, items: any, color?: boolean ) {
        super(changeClass, pos, items, true);
        this.type = HistroyItemType.DocumentContentAddItem;
    }

    /**
     * 删除加入的items
     */
    public undo(): void {
        const buttonManager = this.changeClass as ButtonManager;
        const datas = buttonManager.getMap();
        if ( this.items ) {
            this.items.forEach((item) => {
                datas.delete(item.name);
            });
        }
    }

    /**
     * 重新加入items
     */
    public redo(): void {
        const buttonManager = this.changeClass as ButtonManager;
        const datas = buttonManager.getMap();
        if ( this.items ) {
            this.items.forEach((item) => {
                datas.set(item.name, item);
            });
        }
    }

    public createReverseChange(): ChangeButtonManagerRemoveParaButton {
        return this.createReverseChangeBase(ChangeButtonManagerRemoveParaButton);
    }
}

export class ChangeButtonManagerRemoveParaButton extends ChangeBaseContent {
    constructor( changeClass: ButtonManager, pos: number, items: any, color?: boolean ) {
        super(changeClass, pos, items, true);
        this.type = HistroyItemType.DocumentContentRemoveItem;
    }

    /**
     * 删除加入的items
     */
    public undo(): void {
        const datas = (this.changeClass as ButtonManager).getMap();
        if ( this.items ) {
            this.items.forEach((item) => {
                datas.set(item.name, item);
            });
        }
    }

    /**
     * 重新加入items
     */
    public redo(): void {
        const datas = (this.changeClass as ButtonManager).getMap();
        if ( this.items ) {
            this.items.forEach((item) => {
                datas.delete(item.name);
            });
        }
    }

    public createReverseChange(): ChangeButtonManagerAddParaButton {
        return this.createReverseChangeBase(ChangeButtonManagerAddParaButton);
    }
}
