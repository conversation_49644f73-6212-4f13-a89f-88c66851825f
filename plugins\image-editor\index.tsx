import * as React from 'react';
import { createRoot } from 'react-dom/client';
import { ImageEditorManger, ImageEditor } from './ImageEditor';

// export async function createImageEditor(style?: {color?: string; size?: number;}): Promise<ImageEditorManger> {
//   const containerId = 'emr-image-editor-fadafsdf';
//   let container = document.getElementById(containerId);
//   if (!container) {
//     container = document.createElement('div');
//     container.id = containerId;
//     document.body.appendChild(container);
//   }
//   return new Promise((resolve, reject) => {
//     const initEditor = (_editor: ImageEditorManger) => {
//       resolve(_editor);
//     };
//     ReactDOM.render(<ImageEditor style={style} editorRef={initEditor} />, container);
//   });
// }

export async function createImageEditor(style?: { color?: string; size?: number; }): Promise<ImageEditorManger> {
  const containerId = 'emr-image-editor-fadafsdf';
  let container = document.getElementById(containerId);
  if (!container) {
      container = document.createElement('div');
      container.id = containerId;
      document.body.appendChild(container);
  }
  return new Promise((resolve, reject) => {
      const initEditor = (_editor: ImageEditorManger) => {
          resolve(_editor);
      };

      const root = createRoot(container);
      root.render(<ImageEditor style={style} editorRef={initEditor} />);
  });
}
