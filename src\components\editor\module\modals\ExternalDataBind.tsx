import * as React from 'react';
import '../../style/externalDataBtn.less';
import Dialog from '../../ui/Dialog';
import {IExternalDataProperty} from '../../../../common/commonDefines';
import {message} from '../../../../common/Message';
import Input from '../../ui/Input';
import Button from '../../ui/Button';
import { IFRAME_MANAGER } from '../../../../common/IframeManager';
import CheckboxItem from '../../ui/CheckboxItem';
import Select from '../../ui/select/Select';

interface IDialogProps {
    documentCore: any;
    visible: boolean;
    name?: string;
    properties: IExternalDataProperty;
    onChange: (value: any, name: string) => void;
    close: (name: string | number, bRefresh?: boolean) => void;
    id: string;
    resetId?: string;
    bBarcode?: boolean;
}

interface IState {
    bRefresh: boolean;
}

export class ExternalDataBind extends React.Component<IDialogProps, IState> {
    private externalDataJson: string;
    private properties: IExternalDataProperty;
    private visible: boolean;

    constructor(props:any) {
        super(props);
        this.properties = props.properties;
        this.externalDataJson = this.properties?.sourceObj ? JSON.stringify(this.properties) : '';
        this.state = {
            bRefresh: false,
        };
        this.visible = props.visible;
    }

    public render(): React.ReactNode {
        if (!this.visible) {
            return null;
        }

        const width = !this.props.bBarcode ? 225 : 200;
        return (
            <React.Fragment>
                    <div className='editor-line'>
                        <div className='w-120' >绑定外部数据源</div>
                    </div>
                    <div className='editor-line'>
                        <div className="w-120" style={{width: width}}>
                            <Input
                                name='externalDataBind'
                                value={this.externalDataJson}
                                readonly={true}
                                placeholder={''}
                            />
                        </div>
                        
                        <ExternalDataBindBtn
                            name={this.props.name}
                            id={this.props.id}
                            visible={this.props.visible}
                            documentCore={this.props.documentCore}
                            onChange={this.onChange}
                            close={this.onClose}
                            properties={this.properties}
                            resetId={this.props.resetId}
                        />
                    </div>
                
            </React.Fragment>
                );
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        this.visible = nextProps.visible;
        this.properties = nextProps.properties;
        this.externalDataJson = this.properties?.sourceObj ? JSON.stringify(this.properties) : '';
    }

    private onClose = (name: string, bRefresh: boolean): void => {
        this.props.close(this.props.id, true);
    }

    private onChange = (value: any, name: string): void => {
        if (this.props.resetId === name) {
            this.externalDataJson = '';
        }

        this.props.onChange(value, name);
        this.setState({bRefresh: !this.state.bRefresh});
    }
}

 class ExternalDataBindBtn extends React.Component<IDialogProps, IState> {
    private visible: boolean;
    constructor(props: IDialogProps) {
        super(props);
        this.state = {
            bRefresh: false,
        };
    }

    public render(): any {
        return (
            <div className='right-auto' style={{width: 80}}>
                <button className='external-data-btn' onClick={this.onClick} >设置</button>
                <button className='external-data-btn' onClick={this.onReset} >重置</button>
                {this.renderDialog()}
            </div>
        );
    }

    private onClick = (e: any): void => {
        this.visible = true;
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private onClose = (name: string, bRefresh: boolean): void => {
        this.visible = false;
        if (bRefresh) {
            this.props.close(this.props.id, true);
        } else {
            // this.setState({bRefresh: !this.state.bRefresh});
        }
    }

    private onReset = (): void => {
        this.props.onChange(true, this.props.resetId);
    }

    private renderDialog(): any {
        if (this.visible !== true) {
            return null;
        }
        const props = this.props;
        return (
            <ExternalDataBindInt
                visible={this.visible}
                id={props.id}
                name={props.name}
                onChange={props.onChange}
                close={this.onClose}
                properties={props.properties}
                documentCore={props.documentCore}
            />
        );
    }
}

class ExternalDataBindInt extends React.Component<IDialogProps, IState> {
    // private custompProps: any[];
    private currentProp: any;
    private properties: IExternalDataProperty;
    private visible: boolean;
    private updates: any[];
    constructor(props: any) {
        super(props);
        this.state = {
            bRefresh: false,
        };

        this.properties = {
            sourceObj: '',
            sourceKey: '',
            bReadOnly: false,
            commandUpdate: 1,
        }

        this.currentProp = {
            sourceObj: '',
            sourceKey: '',
            bReadOnly: false,
            commandUpdate: 1,
        }

        this.updates = [
            {key: '总刷新', value: 1},
            // {key: '刷新一次', value: 2},
            {key: '不刷新', value: 3},
        ];
        this.visible = this.props.visible;
    }

    public render(): any {
        return (
            <Dialog
                visible={this.visible}
                width={310}
                height={420}
                top='0%'
                open={this.open}
                title='绑定外部数据源'
                footer={this.renderFooter()}
                id={this.props.id}
            >
                {this.renderContent()}
            </Dialog>
        );
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        this.visible = nextProps.visible;
    }

    private renderContent(): any {
        return (<div>
            <ul>
                <li>
                    <div className='w-90'>数据源对象：</div>
                    <div className='input-content'>
                        <Input
                            value={this.properties.sourceObj}
                            name='sourceObj'
                            onChange={this.onChange}
                        />
                    </div>
                </li>
                <li>
                    <div className='w-90'>数据源字段：</div>
                    <div className='input-content'>
                        <Input
                            value={this.properties.sourceKey}
                            name='sourceKey'
                            onChange={this.onChange}
                        />
                    </div>
                </li>
                <li style={{borderTop: '1px solid '}}>
                    <div className='input-content'>
                        <CheckboxItem
                            value={this.properties.bReadOnly}
                            name='bReadOnly'
                            onChange={this.onChange}
                        >
                            只读，无需回填
                        </CheckboxItem>
                    </div>
                </li>
                <li >
                    <div className='w-90'>控制命令：</div>
                    <div className='input-content'>
                        <Select
                            data={this.updates}
                            value={this.properties.commandUpdate}
                            name='commandUpdate'
                            onChange={this.onChange}
                        />
                    </div>
                </li>
                
            </ul>
                    </div>
        );
    }

    private renderFooter(): any {
        return (
            <span>
                <Button onClick={this.close}>取消</Button>
                <Button type='primary' onClick={this.confirm}>确认</Button>
            </span>
        );
    }

    private onChange = (value: any, name: string): void => {
        this.currentProp[name] = value;
    }

    private open = (): void => {
        let props: IExternalDataProperty;
        if (this.props.properties) {
            props = this.props.properties;
        } else if (this.props.name) {
            props = this.props.documentCore.getSourceDataBind(this.props.name);
        }

        if (props) {
            this.properties.sourceObj = props.sourceObj;
            this.properties.sourceKey = props.sourceKey;
            this.properties.bReadOnly = props.bReadOnly;
            this.properties.commandUpdate = props.commandUpdate;

            this.currentProp.sourceObj = props.sourceObj;
            this.currentProp.sourceKey = props.sourceKey;
            this.currentProp.bReadOnly = props.bReadOnly;
            this.currentProp.commandUpdate = props.commandUpdate;
        }

        this.setState({bRefresh: !this.state.bRefresh});
    }

    private close = (id?: any): void => {
        this.props.close(id);
        this.visible = false;
        // this.custompProps = [];
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private confirm = (id?: any): void => {
        IFRAME_MANAGER.setDocId(this.props.documentCore.getCurrentId());

        if (!this.currentProp.sourceObj) {
            message.error('数据源对象不合法，请重新输入');
            return ;
        }

        if (!this.currentProp.sourceKey) {
            message.error('数据源字段不合法，请重新输入');
            return ;
        }

        this.props.onChange(this.currentProp, this.props.id);
        // this.props.documentCore.setSourceDataBind(this.props.name, this.currentProp);
        this.props.close(id, true);
    }

    // private checkXmlElementNameValidity(value: string): boolean {
    //     // Element names are case-sensitive
    //     // Element names must start with a letter or underscore
    //     // Element names cannot start with the letters xml (or XML, or Xml, etc)
    //     // Element names can contain letters, digits, hyphens, underscores, and periods
    //     // Element names cannot contain spaces

    //     let validity = /^[a-zA-Z_].*/.test(value);  // condition 2
    //     validity = validity && /^[a-zA-Z0-9_\-\.]+$/.test(value); // condition 4
    //     return validity;
    // }
}
