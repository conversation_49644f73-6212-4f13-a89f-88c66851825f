import {saveAs} from 'file-saver';
import { EmrEditor } from '../../components/editor/Main';
import { DocumentCore } from '../../model/DocumentCore';
import { CleanModeType } from '../commonDefines';
import { doPrintStyle } from '../css/style';
import {PrintContent} from '../../../src/components/editor/module/PrintContent';
import * as React from 'react';
/* IFTRUE_WATER */
import MarkFactory from '../MarkFactory';
/* FITRUE_WATER */

export class HtmlModel {
    protected iframe: any;
    protected reactVm: any;
    public reactVm2: React.RefObject<PrintContent> = React.createRef(); // 使用 React.createRef
    protected bAutoTest: boolean;
    protected cleanMode: CleanModeType;
    private _host: EmrEditor;
    private _documentCore: DocumentCore;
    private backgroundImage: any;

    constructor(host: EmrEditor) {
        this._host = host;
        this._documentCore = host.state.documentCore;
    }

    protected async exportContent(bReturnHtml: boolean = false): Promise<void | string> {
        let html = await this.getPager();
        if (!html) {
            return;
        }
        html = this.shrinkImage(html);

        const blob = new Blob([html], {type: 'text/html;charset=utf-8'});
        if (bReturnHtml === false) {
            saveAs(blob, 'editor-html.html');
        }
        this.reactVm.clearDom();
        this.reactVm = null;
        this.iframe.outerHTML = '';
        /* IFTRUE_WATER */
        // 屏蔽水印生成
        MarkFactory.getInstance().setBGenStart(true);
        this._host.handleRefresh();
        /* FITRUE_WATER */
        if (bReturnHtml === true) {
            return html;
        }
    }

    protected async getContent(): Promise<Blob> {
        const html = await this.getPager();
        if (!html) {
            return null;
        }
        this.reactVm.clearDom();
        this.reactVm = null;
        this.iframe.outerHTML = '';
         /* IFTRUE_WATER */
        // 屏蔽水印生成
        MarkFactory.getInstance().setBGenStart(true);
        /* FITRUE_WATER */
        return new Blob([html], {type: 'text/html;charset=utf-8'});
    }

    protected async getCleanContent(): Promise<Blob> {
        // 创建文档核心的拷贝
        const originalDocumentCore = this._documentCore;
        const tempDocumentCore = new DocumentCore(undefined, false);
        
        // 复制原始文档内容到新实例
        const content = originalDocumentCore.getDocContent();
        tempDocumentCore.addDocContent(content);
        
        // 复制必要的状态和设置
        tempDocumentCore.changeRevisionState2(originalDocumentCore.isFinalRevision());
        tempDocumentCore.getDocument().setHeaderFooterInterface(
            originalDocumentCore.isSetHeadFooterInterface()
        );
        tempDocumentCore.getDocument().setDynamicGridLine(
            originalDocumentCore.getDocument().isDynamicGridLine(),
            originalDocumentCore.getDocument().getDynamicGridLineColor()
        );
        
        // 临时替换文档核心
        const originalDocCore = this._documentCore;
        this._documentCore = tempDocumentCore;
        
        try {
            // 在临时文档上调用 getPager 方法，此时修改的是临时文档的状态
            const html = await this.getPager(CleanModeType.CleanMode);
            if (!html) {
                return null;
            }
            
            this.reactVm.clearDom();
            this.reactVm = null;
            this.iframe.outerHTML = '';
            
            /* IFTRUE_WATER */
            // 屏蔽水印生成
            MarkFactory.getInstance().setBGenStart(true);
            /* FITRUE_WATER */
            
            return new Blob([html], {type: 'text/html;charset=utf-8'});
        } finally {
            // 恢复原始文档核心
            this._documentCore = originalDocCore;
        }
    }

    protected async getSvgContent(): Promise<string> {
        await this.reactVm.setDocumentCore(this._documentCore);
        const pages = this.getPages();
        // console.log(pages)
        if (!pages.length) {
            return;
        }
        const content = this.setPageContent(pages);
        return content;
    }


    protected setDocCore(core: DocumentCore): void {
        this._documentCore = core;
    }

    protected getHost(): EmrEditor {
        return this._host;
    }

    protected getDocumentCore(): DocumentCore {
        return this._documentCore;
    }

    protected async getPager(clearMode?: CleanModeType): Promise<string> {
        if (clearMode === CleanModeType.CleanMode || clearMode === CleanModeType.CleanModeSpecial) {
        // if (clearMode !== CleanModeType.Normal) {

            // TODO: clean mode and cleanspecial, cover all?
            this._documentCore.browseTemplet(clearMode, 0);
            // revision signature
            this._documentCore.changeRevisionState(true);
        }
        /* IFTRUE_WATER */
        // 屏蔽水印生成
        MarkFactory.getInstance().setBGenStart(false);
        /* FITRUE_WATER */
        // const documentCore = this.initPrint();
        await this.reactVm.setDocumentCore(this._documentCore);
        const pages = this.getPages();
        if (!pages.length) {
            return;
        }
        const content = this.setPageContent(pages);
        this._documentCore.setPrintStatus(undefined);

        return this.setPageHtml(content);
    }

    protected shrinkImage(html: string): string {
        let result = html;
        if (html != null && html.indexOf('<image') !== -1) {
            // console.log(html)
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');
            // console.log(doc)
            // console.log(html === doc.documentElement.outerHTML)
            const body = doc.body;
            const svg = body.getElementsByTagName('svg');
            // need a static collection! get... returna HTMLCollection which is live
            const images = body.querySelectorAll('image');

            const hrefMap = new Map();
            // compose href arr
            let index = 1;
            for (let i = 0, len = images.length; i < len; i++) {
                const href = images[i].href.baseVal;
                if (Array.from(hrefMap.values()).includes(href) === false) {
                    hrefMap.set('image-' + index, href);
                    index++;
                }
            }

            if (hrefMap.size === 0) {
                return result;
            }

            let imageTemplates = '';
            for (const [imageId, imageHref] of hrefMap) {
                // <svg id='${imageId}' viewBox="0 0 ${originalW} ${originalH}">
                const imageTemplate =
                `
                    <svg id='${imageId}'>
                        <g>
                            <image href='${imageHref}' preserveAspectRatio="xMinYMin meet"></image>
                        </g>
                    </svg>
                `;
                // const imageTemplate =
                // `
                //     <image id='${imageId}' href='${imageHref}' preserveAspectRatio="xMinYMin meet"></image>
                // `;
                imageTemplates += imageTemplate;
            }
            // console.log(imageTemplates)
            // console.log(hrefMap)

            const defStr =
            `
                <svg>
                    <defs>
                        ${imageTemplates}
                    </defs>
                </svg>
            `;
            const defTemplate = document.createElement('template');
            defTemplate.innerHTML = defStr.trim();
            const defNode = defTemplate.content.firstChild.childNodes[1]; // has textNode but no firstElementChild lol
            // console.log(defNode) // this is not the real node that is attached in dom
            svg[0].prepend(defNode); // not matter if have several pages
            const svgNodes = body.querySelectorAll('defs svg');

            for (let i = 0, len = svgNodes.length; i < len; i++) {
                const svgNode = svgNodes[i].cloneNode(true); // need clone: appendChild will move current node
                // append to real dom to calc BBox()
                document.body.appendChild(svgNode);
                const id = svgNodes[i].id;

                const curImage: any = document.body.querySelector('#' + id + ' image');
                // console.log(curImage)
                const curImageBBox = curImage.getBBox();
                document.body.removeChild(svgNode);

                // set viewBox, so that viewbox has the true width/height of the image
                svgNodes[i].setAttribute('viewBox', '0 0 ' + curImageBBox.width + ' ' + curImageBBox.height);
            }

            // <use>
            for (let i = 0, len = images.length; i < len; i++) {
                const curImage = images[i];
                const href = curImage.href.baseVal;
                const height = curImage.height.baseVal.valueAsString;
                const width = curImage.width.baseVal.valueAsString;
                const x = curImage.x.baseVal.valueAsString;
                const y = curImage.y.baseVal.valueAsString;
                const style = ('select-button select-button-red' === curImage.getAttribute('class') ?
                                'filter: hue-rotate(140deg) saturate(45)' : null);
                let imageId = '';
                for (const [key, value] of hrefMap) {
                    // console.log(key, value)
                    if (value === href) {
                        imageId = key;
                        break;
                    }
                }
                const useStr =
                `
                    <use href='#${imageId}' x='${x}' y='${y}' height='${height}' width='${width}' style='${style}' />
                `;
                const useTemplate = document.createElement('template');
                useTemplate.innerHTML = useStr.trim();
                const useNode = useTemplate.content.firstChild;
                // console.log(useNode)

                // get transform attr if any(radio/check boxes bug)
                const transformAttr = curImage.getAttribute('transform');
                if (transformAttr != null) {
                    (useNode as any).setAttribute('transform', transformAttr);
                }
                curImage.parentNode.replaceChild(useNode, curImage);
            }

            // let defNode = parser.parseFromString(defStr, 'text/html').body.firstChild;
            // let useNode = parser.parseFromString(useStr, 'text/html').body.firstChild;

            // console.log(body)
            result = '<!DOCTYPE html>' + doc.documentElement.outerHTML;
        }
        return result;

    }

    private initPrint(options?: any): any {
        const document = new DocumentCore(undefined, false);
        const documentCore = this.getHost().state.documentCore; // host, core

        const bTrackRevisions = documentCore.isTrackRevisions();
        if ( bTrackRevisions ) {
            documentCore.setTrackRevisions(false);
        }
        const content = documentCore.getDocContent();
        // const pageSize = this.getPageSize(options);
        // let newOptions;
        // if (pageSize) {
        //     newOptions = {pageSize};
        //     options.pageSize = pageSize;
        // }
        // console.log(content);
        document.addDocContent(content, undefined, undefined);
        document.changeRevisionState2(documentCore.isFinalRevision());

        // this.printDialog.addSideEffectsForPrintInstance(document, documentCore);
        document.recalculateAllForce();

        // this.printDialog.setContent(content);
        documentCore.removeSelection();
        // this.printDialog.onClose(() => {
        //     // if ( bTrackRevisions ) {
        //     //     documentCore.setTrackRevisions(bTrackRevisions);
        //     // }
        //     this.printDialog.clearContent();
        //     this.printDialog.clearDom();
        //     // documentCore.recalculateAllForce();
        //     // this.refresh(true);
        // });
        // this.printDialog.setDoc(document);
        return document;
    }

    private getPageProp(): any {
        const { pageProperty } = this._documentCore.render();
        return pageProperty;
    }

    private setPageHtml(content: string): string {
        const pageProp = this.getPageProp();
        const height = pageProp.height.toFixed(0);
        const width = pageProp.width.toFixed(0);
        const pageSize = `<style>
                @media print {
                    @page {
                        size: ${width}px ${height}px;
                        margin-top: 0;
                        margin-bottom: 0;
                    }
                }
                </style>
        `
        return `
            <!DOCTYPE html>
            <html>
                <head>
                    <meta charset="utf-8">
                    <style type="text/css">${doPrintStyle()}</style>
                </head>
                <body class='print-interface'>
                    ${pageSize}
                    ${content}
                </body>
            </html>
        `;
    }

    private setPageContent(contents: string[]): string {
        if (contents.length === 0) {
            return '';
        }
        let pagers = '';
        // const title = '导出文档'; // this.title;
        // const len = texts.length;
        const pageProp = this.getPageProp();
        const height = (pageProp.height - 2).toFixed(0);
        const width = pageProp.width.toFixed(0);

        let style = `height: ${height}px; width: ${width}px;`;
        if (this.backgroundImage && this.bAutoTest !== true) {
            style += `background-image: ${this.backgroundImage};`;
        }
        
        contents.forEach((item) => {
            pagers += `<div class="pager" style='${style}'>
                <svg width="${width}" height="${height}">${item}</svg>
            </div>`;
        });

        return pagers;
    }

    private getPages(): string[] {
        const container = this.reactVm.myRef.current;
        if (!container) {
            return [];
        }
        const doms = container.querySelectorAll('.ReactVirtualized__Grid__innerScrollContainer .page-wrapper> svg');
        const contents = [];
        // const arrs = ['header-footer', 'content'];
        for (let i = 0, ii = doms.length; i < ii; i++) {
            const dom = doms[i];
            const childs = dom.children;
            let text = '';
            let activeIndex = 0;
            if (!this.backgroundImage) {
                this.backgroundImage = dom.parentNode.style.backgroundImage;
            }
            for (let index = 0, len = childs.length; index < len; index++) {
                const child = childs[index];
                if (child.tagName !== 'g') {
                    continue;
                }
                const classList = child.classList;
                if (!(classList.contains('content') || classList.contains('header-footer'))) {
                    continue;
                }
                activeIndex++;
                // const child = childs[arrs[index]];
                const textNodes = child.getElementsByTagName('text');
                if ( textNodes ) {
                    for (let index2 = 0; textNodes && index2 < textNodes.length; index2++) {
                        const textNode = textNodes[index2];
                        if ( textNode && textNode.childNodes[1] && 0 === textNode.childNodes[1].length ) {
                            textNode.parentNode.removeChild(textNode);
                            index2--;
                        }
                    }
                }
                const svg = child.innerHTML;
                text += this.filterContent(svg);
                if (activeIndex === 2) {
                    break;
                }
            }
            contents.push(text);
        }
        return contents;
    }

    private filterContent(content: string): string {
        // 结构化元素
        content = content.replace(/<g\s+class="newcontrol-[\s\S]+?<\/g>/g, '');
        // 表格
        const reg = /<g\s+class="(paragraphline-containerselection [\w\-]+|tablecell-container)"*?>([\s\S]*?)<\/g>/g;
        const bgReg: RegExp = /<(\w+)\s*class="paragraphline-container(\s+outer)?"[^\x01]*?<\/\1>/g;
        content = content.replace(reg, '');
        content = content.replace(bgReg, '')
            .replace(/¶/g, '');

        return content;
    }

}
