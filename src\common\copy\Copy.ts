import {CopyContent} from './Content';
import copyModel from './ClipboardContent';
import DocumentContentElementBase from '../../model/core/DocumentContentElementBase';
// import { decodeTag } from './DataConver';

let staticSelectionContent: DocumentContentElementBase[] = [];
export default class Copy extends copyModel {
    private bCopyFromEdit: boolean;
    public constructor() {
        super();
        this.copyContent = [];
    }

    public setCopyFrom(bOnlyCopyFromWebEdit: boolean): void {
        this.bCopyFromEdit = bOnlyCopyFromWebEdit;
    }

    public copy(seletions: DocumentContentElementBase[]): string {
        staticSelectionContent = this.selections = seletions;
        const html = this.parseHtml(); // 如果复制的内容来自编辑器内部，对他进行html保存
        return html;
    }

    public setId(id: string): void {
        this.id = id;
    }

    public getCopyContent(): CopyContent[] {
        return this.copyContent;
    }

    public getSelection(bFormat: boolean): DocumentContentElementBase[] {
        this.selections = staticSelectionContent;
        return this.getNewContents(this.selections, bFormat);
    }

    public getApolloContents(): string {
        return this.getApollo();
    }

    public clearContent(): void {
        staticSelectionContent = [];
        this.clear();
    }

    // public hasContent(): boolean {
    //     return staticSelectionContent.length > 0;
    // }

    // public isOwnerContent(str: string): boolean {
    //     if (!str) {
    //         return false;
    //     }

    //     if (this.copyContent.length === 0) {
    //         return false;
    //     }

    //     const text1 = this.getUnFormatContent(str);
    //     const text2 = this.getUnFormatContent();

    //     return text1 === text2;
    // }

    // private getUnFormatContent = (content?: string): string => {
    //     let text = content === undefined ? this.copyContent.map((data) => data.content)
    //         .join('') : content;
    //     if (!text) {
    //         text = text.trim();
    //         if (!text) {
    //             return;
    //         }
    //     }
    //     text = decodeTag(text);
    //     return document.createTextNode(text)
    //         .textContent
    //         .replace(/\s+/g, ' ');
    // }
}
