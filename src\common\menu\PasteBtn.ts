import CopyPaste from '../copy/CopyPaste';
import CopyCore from '../copy/CopyCore';
import { PasteType } from '../copy/DataType';
export class PasteBtn {
    private paste: CopyPaste;
    private pasteCore: CopyCore;

    constructor(paste: CopyPaste) {
        this.paste = paste;
        this.pasteCore = paste.getCopyPaste();
    }

    public cutEvent(): number {
        return this.paste.cutEvent();
    }

    public copyEvent(): void {
        this.paste.copyEvent();
    }

    public async isActivePaste(): Promise<boolean> {
        let text = await this.paste.getClipboardData();
        if (text === null) {
            text = this.getText();
        } else {
            this.setWebPageFlag(false);
        }
        this.pasteCore.setPasteType(PasteType.PasteClickCtrlShiftV);
        let access = this.pasteCore.getPermission(text);
        if (!text) {
            access = false;
        }
        return access;
    }

    public async pasteText(): Promise<boolean> {
        // const text = await this.paste.getClipboardData();
        // this.pasteCore.setPasteType(PasteType.PasteClickCtrlShiftV);
        return this.paste.pasteEvent(PasteType.PasteClickCtrlV);
    }

    private setWebPageFlag(flag?: boolean): void {
        this.pasteCore.setWebPageFlag(flag);
    }

    private getText(): string {
        this.setWebPageFlag();
        return this.pasteCore.getText();
    }
}
