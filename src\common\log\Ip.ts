export class Ip {
    private _datas: any[];
    private _ip: string;
    constructor() {
        this.getIp();
        this._datas = [];
    }

    public setIp(data: any): void {
        if (this._ip) {
            data.ip = this._ip;
        } else {
            this._push(data);
        }
    }

    private _push(data: any): void {
        this._datas.push(data);
    }

    private _forEach(): void {
        const datas = this._datas;
        const ip = this._ip;
        datas.forEach((data) => {
            data.ip = ip;
        });
        this._datas = [];
    }

    private getIp(): void {
        if (this._ip) {
            return;
        }
        const rTCPeerConnection = window['RTCPeerConnection'] || window['mozRTCPeerConnection']
            || window['webkitRTCPeerConnection'];
        // 如果不存在则使用一个iframe绕过
        // if (!rTCPeerConnection) {
        //     因为这里用到了iframe，所以在调用这个方法的script上必须有一个iframe标签
        //     <iframe id="iframe" sandbox="allow-same-origin" style="display:none;"></iframe>
        //     let win = iframe.contentWindow;
        //     rTCPeerConnection = win.RTCPeerConnection || win.mozRTCPeerConnection || win.webkitRTCPeerConnection;
        // }

        // 创建实例，生成连接
        const pc = new rTCPeerConnection();

        // 匹配字符串中符合ip地址的字段
        const handleCandidate = (candidate): string => {
            if (typeof candidate !== 'string' || !candidate) {
                return '';
            }
            const ipRegexp =  /([0-9]{1,3}(\.[0-9]{1,3}){3})/;
            // /([0-9]{1,3}(\.[0-9]{1,3}){3}|([a-f0-9]{1,4}((:[a-f0-9]{1,4}){7}|:+[a-f0-9]{1,4}){6}))/;
            const matchs = candidate.match(ipRegexp);
            if (!matchs) {
                return '';
            }
            return matchs[1];
        };

        // 监听icecandidate事件
        // pc.onicecandidate = (ice) => {
        //     console.log(ice);
        //     // if (ice.candidate) {
        //     //     handleCandidate(ice.candidate);
        //     // }
        // };
        // 建立一个伪数据的通道
        const channel = pc.createDataChannel('abc');
        // channel.onopen = function(event) {
        //     console.log(event);
        // }
        pc.createOffer()
        .then((res) => {
            if (!res.sdp) {
                return;
            }
            this._ip = handleCandidate(res.sdp);
            // console.log(this._ip);
            this._forEach();
        });
    }
}
