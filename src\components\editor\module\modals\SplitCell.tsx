import * as React from 'react';
import Dialog from '../../ui/Dialog';
import Button from '../../ui/Button';
import { message } from '../../../../common/Message';
import { IFRAME_MANAGER } from '../../../../common/IframeManager';

interface IProps {
    documentCore: any;
    visible: boolean;
    id: string;
    close: (name: string | number, bRefresh?: boolean) => void;
}

interface IState {
    bRefresh: boolean;
}

export default class TableCellSplit extends React.Component<IProps, IState> {
    private table: {splitTableCols: number, splitTableRows: number};
    private visible: boolean;
    constructor(props: any) {
        super(props);
        this.state = {
            bRefresh: false,
        };
        this.table = {
            splitTableCols: 2,
            splitTableRows: 1,
        };
        this.visible = this.props.visible;
    }

    public render(): any {
        return (
            <Dialog
                visible={this.visible}
                width={350}
                open={this.open}
                title='拆分单元格'
                footer={this.renderFooter()}
                id={this.props.id}
            >
                <div>
                    <div className='editor-line'>
                        <div className='w-70'>列数：</div>
                        <div className='right-auto'>
                            <input
                                min={1}
                                max={9}
                                step={1}
                                value={this.table.splitTableCols}
                                name='splitTableCols'
                                type='number'
                                onFocus={this.selectText}
                                onChange={this.numChange.bind(this, 'splitTableCols')}
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <div className='w-70'>行数：</div>
                        <div className='right-auto'>
                            <input
                                min={1}
                                max={9}
                                step={1}
                                value={this.table.splitTableRows}
                                name='splitTableRows'
                                type='number'
                                onFocus={this.selectText}
                                onChange={this.numChange.bind(this, 'splitTableRows')}
                            />
                        </div>
                    </div>
                </div>
            </Dialog>
        );
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        this.visible = nextProps.visible;
    }

    public selectText = (e: any) => {
        e.target.select();
    }

    private numChange(name: string, e: any): void {
        if ( e.target.value && '' !== e.target.value ) {
            this.table[name] = parseInt(e.target.value, 0);
        } else {
            this.table[name] = '';
        }
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private renderFooter(): any {
        return (
            <span>
                <Button onClick={this.close}>取消</Button>
                <Button type='primary' onClick={this.confirm}>
                    确认
                </Button>
            </span>
        );
    }

    private open = (): void => {
        const table = this.table;
        table.splitTableCols = 2;
        table.splitTableRows = 1;
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private onChange = (value: any, name: string): void => {
        this.table[name] = value;
    }

    private confirm = (id?: any): void => {
        const documentCore = this.props.documentCore;
        IFRAME_MANAGER.setDocId(documentCore.getCurrentId());
        const table = this.table;

        if ( 9 < table.splitTableRows ) {
            message.error('拆分单元格行数不超过9');
            return;
        }

        if ( 9 < table.splitTableCols ) {
            message.error('拆分单元格列数不超过9');
            return;
        }

        const res = documentCore.splitTableCells(table.splitTableRows, table.splitTableCols);

        if ( res ) {
            this.close(true);
        }
    }

    private close = (bRefresh?: boolean): void => {
        this.props.close(this.props.id, bRefresh);
        this.visible = false;
        this.setState({bRefresh: !this.state.bRefresh});
    }
}
