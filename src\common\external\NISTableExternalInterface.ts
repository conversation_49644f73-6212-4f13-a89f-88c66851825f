import { ExternalInterface } from '.';
import INISTableExternalInterface from './INISTableExternalInterface';

export class NISTableExternalInterface implements INISTableExternalInterface {
    private _host: ExternalInterface;

    constructor(host: ExternalInterface) {
        this._host = host;
    }

    public insertTable(sName: string, sJson: string): number {
        return this._host.getTable()
                        .insertNISTable(sName, sJson);
    }

    public deleteTable(sName: string): number {
        return this._host.getTable()
                        .deleteNISTable(sName);
    }

    public getTableName(): string {
        return this._host.getTable()
                    .getNISTableNames();
    }

    public setTableColID(sTableName: string, sJson: string): number {
        return this._host.getTable()
                        .setNISTableColID(sTableName, sJson);
    }

    public getTableColID(sTableName: string): string {
        return this._host.getTable()
                        .getNISTableColID(sTableName);
    }

    public filterColID(sTableName: string, serialNumberJson: string): string {
        return this._host.getTable()
                        .filterColID(sTableName, serialNumberJson);
    }

    public setNisMode(nMode: number): number {
        return this._host.getTable()
                        .setNISTableMode(nMode);
    }

    public setTableHeaderReadOnly(sName: string, nMode: boolean): number {
        return this._host.getTable()
                        .setTableHeaderReadOnly(sName, nMode);
    }

    public isNew(sTableName: string): number {
        return this._host.getTable()
                        .isNewNISTable(sTableName);
    }

    public hideFirstRow(sName: string, bHide: boolean): number {
        return this._host.getTable()
                        .hideNISFirstRow(sName, bHide);
    }

    public getCurrentRowID(): string {
        return this._host.getTable()
                    .getNISCurrentRowID();
    }

    public getTableInfo(sName: string, sJson: string): string {
        return this._host.getTable()
                    .getNISTableInfo(sName, sJson);
    }


    public getTableProp(sTableName: string): string {
        return this._host.getTable()
                        .getNISTableProp(sTableName);
    }

    public getRowInfo(sTableName: string, rowID: string, sParam: string): string {
        return this._host.getTable()
                         .getRowInfo(sTableName, rowID, sParam);
    }
    public setRowInfo(sTableName: string, sInfo: string): number {
        return this._host.getTable()
                         .setRowInfo(sTableName, sInfo);
    }

    public getAllRowsID(sTableName: string): string {
        return this._host.getTable()
                    .getNISAllRowsID(sTableName);
    }

    public filterRowID(sName: string, sJon: string): string {
        return this._host.getTable()
                    .filterNISRowID(sName, sJon);
    }

    public getLastRowID(sTableName: string): string {
        return this._host.getTable()
                    .getNISLastRowID(sTableName);
    }

    public getFirstRowID(sTableName: string): string {
        return this._host.getTable()
                    .getNISFirstRowID(sTableName);
    }

    public getHeaderRowText(sTableName: string): string {
        return this._host.getTable()
                    .getHeaderRowText(sTableName);
    }

    // public moveRowToPosition(sTableName: string, movedRow: string, PositionRow: string, direct: number): number {
    //     return this._host.getTable()
    //                 .moveRowToPosition(sTableName, movedRow, PositionRow, direct);
    // }

    public sortRow(sTableName: string, rowID: string): number {
        return this._host.getTable()
                    .sortRow(sTableName, rowID);
    }

    public sortRowInRange(sTableName: string, beginRow: string, rowJson: string): number {
        return this._host.getTable()
                    .sortRowInRange(sTableName, beginRow, rowJson);
    }


    public setRowProp(sTableName: string, rowID: string, sJson: string): number {
        return this._host.getTable()
                        .setNISRowProp(sTableName, rowID, sJson);
    }

    public setRowText(sTableName: string, rowID: string, sJson: string): number {
        return this._host.getTable()
                        .setNISRowText(sTableName, rowID, sJson);
    }

    public getRowProp(sTableName: string, rowID: string): string {
        return this._host.getTable()
                    .getNISRowProp(sTableName, rowID);
    }


    public getRowCreator(sTableName: string, rowID: string): string {
        return this._host.getTable()
                    .getNISRowCreator(sTableName, rowID);
    }


    /**
     * 获取护理表格的表头信息。
     * @param sTable 护理表格名
     * @param colIDs colId集合 ['a1', 'a2' , 'a3', 'a4']
     */
    public getHeaderRowTextByColID(sTable: string, colIDs: string): string {
        return this._host.getTable()
                    .getHeaderRowTextByColID(sTable, colIDs);
    }


    public setRowCreator(sTableName: string, rowID: string, creator: string): number {
        return this._host.getTable()
                    .setNISRowCreator(sTableName, rowID, creator);
    }

    public getRowText(sTableName: string, sJon: string): string {
        return this._host.getTable()
                    .getNISRowText(sTableName, sJon);
    }

    public protectRows(sTableName: string, sJon: string, bFlag: boolean): number {
        return this._host.getTable()
                        .protectNISRows(sTableName, sJon, bFlag);
    }

    public protectCurrentRow(bFlag: boolean): number {
        return this._host.getTable()
                        .protectNISCurrentRow(bFlag);
    }

    public selectRow(sTableName: string, rowID: string): number {
        return this._host.getTable()
                        .selectNISRow(sTableName, rowID);
    }

    // public getSelectedRowNumber(): number {
    //     return this._host.getTable()
    //                     .getSelectedNISRowNumber();
    // }

    public cancelSelecteRow(): number {
        return this._host.getTable()
                        .cancelSelecteNISRow();
    }

    public isCurrentRowCanDelete(): boolean {
        return this._host.getTable()
                        .isCurrentNISRowCanDelete();
    }

    // 判断当前光标选中的列是否已经签名，如果选中多列，则返回结果为与关系

    public isCurrentRowSigned(): number {
        return this._host.getTable()
                        .isCurrentNISRowSigned();
    }


    // 删除光标所在的当前行
    public deleteCurrentRow(): number {
        return this._host.getTable()
                        .deleteNISCurrentRow();
    }

    // 单行或者多行 {row1,row2,row3}
    public deleteRows(sTableName: string, sJson: string): number {
        return this._host.getTable()
                        .deleteNISRows(sTableName, sJson);
    }

    public setSignCellText(sName: string, sJson: string): number {
        return this._host.getTable()
                        .setNISSignCellText(sName, sJson);
    }

    public deleteSignCellsText(sName: string, sJson: string): number {
        return this._host.getTable()
                        .deleteNISSignCellsText(sName, sJson);
    }

    public signCurrentRow(sJson: string): Promise<number> {
        return this._host.getTable()
                        .signCurrentNISRow(sJson);
    }

    public signRows(sName: string, sJson: string): Promise<number> {
        return this._host.getTable()
                        .signNISRows(sName, sJson);
    }

    public deleteCurrentRowSign(sJson: string): number {
        return this._host.getTable()
                        .deleteCurrentNISRowSign(sJson);
    }

    public deleteRowsSign(sName: string, sJson: string): number {
        return this._host.getTable()
                        .deleteNISRowsSign(sName, sJson);
    }

    public getSignNumber(sName: string, sJson: string): number {
        return this._host.getTable()
                        .getNISSignNumber(sName, sJson);
    }

    public getSignStatus(sName: string, sJson: string): number {
        return this._host.getTable()
                        .getNISSignStatus(sName, sJson);
    }

    public setSignStatus(sName: string, sJson: string): number {
        return this._host.getTable()
                        .setNISSignStatus(sName, sJson);
    }

    public filterSignRow(sName: string, sJson: string): string {
        return this._host.getTable()
                        .filterNISSignRow(sName, sJson);
    }

    public getSignNameByRow(sName: string, rowID: string): string {
        return this._host.getTable()
                        .getSignNameByRow(sName, rowID);
    }

    public insertSumRowAfterCurrentRow(sJson: string): number {
        return this._host.getTable()
                        .insertSumRowAfterCurrentRow(sJson);
    }

    public insertSumRowBeforeCurrentRow(sJson: string): number {
        return this._host.getTable()
                        .insertSumRowBeforeCurrentRow(sJson);
    }

    public insertSumRows(sName: string, sJson: string): string {
        return this._host.getTable()
                        .insertSumRows(sName, sJson);
    }

    public deleteSumRows(sName: string, sJson: string): number {
        return this._host.getTable()
                        .deleteSumRows(sName, sJson);
    }

    public setRowSumStatus(sName: string, sJson: string): number {
        return this._host.getTable()
                        .setRowSumStatus(sName, sJson);
    }

    public getRowSumStatus(sTable: string, sJson: string): string {
        return this._host.getTable()
                        .getRowSumStatus(sTable, sJson);
    }

    public getCurrentRowSumStatus(): number {
        return this._host.getTable()
                        .getCurrentRowSumStatus();
    }

    public cleanRowSumStatus(sName: string, sJson: string): number {
        return this._host.getTable()
                        .cleanRowSumStatus(sName, sJson);
    }

    /**
     * 对指定行的血压单元格设置血压格式
     * @param sName 表格名称
     * @param rowID 行id
     * @param colID 列id
     * @param format 1 – AAA:BBB 2 – AAA/BBB 3 – AAA BBB
     */
    public setBPCellFormat(sName: string, rowID: string, colID: string, type: number, format?: string): number {
        return this._host.getTable()
                        .setBPCellFormat(sName, rowID, colID, type, format);
    }

    /**
     * 对指定行的血压单元格设置血压格式
     * @param sName 表格名称
     * @param rowID 行id
     * @param colID 列id
     * @param value “AAA=xxx,BBB=xxx” 严格按照此格式传入，否则会失败
     */
    public setBPCellValue(sName: string, rowID: string, colID: string, value: string): number {
        return this._host.getTable()
                        .setBPCellValue(sName, rowID, colID, value);
    }

    public judgeTimeAndBpCell(sTable: string, sRowID: string): string {
        return this._host.getTable()
                        .judgeTimeAndBpCell(sTable, sRowID);
    }

    public getLastSumRow(sName: string): string {
        return this._host.getTable()
                        .getLastSumRow(sName);
    }

    public filterSumRow(sName: string, sJson: string): string {
        return this._host.getTable()
                        .filterSumRow(sName, sJson);
    }

    public getRowsTextByJson(sName: string, sJson: string): string {
        return this._host.getTable()
                        .getNISRowsTextByJson(sName, sJson);
    }

    public insertSumDetailRowAfterCurrentRow(sJson: string): number {
        return this._host.getTable()
                        .insertSumDetailRowAfterCurrentRow(sJson);
    }

    public insertSumRowDetailBeforeCurrentRow(sJson: string): number {
        return this._host.getTable()
                        .insertSumRowDetailBeforeCurrentRow(sJson);
    }

    public insertSumDetailRows(sName: string, sJson: string): string {
        return this._host.getTable()
                        .insertSumDetailRows(sName, sJson);
    }


    public insertRowAfterCurrentRow(sJson: string): number {
        return this._host.getTable()
                        .insertNISRowAfterCurrentRow(sJson);
    }

    public insertRowBeforeCurrentRow(sJson: string): number {
        return this._host.getTable()
                        .insertNISRowBeforeCurrentRow(sJson);
    }

    public insertRows(sName: string, sJson: string): string {
        return this._host.getTable()
                        .insertNISRows(sName, sJson);
    }

    public setCellType(sName: string, rowID: string, colID: string, nType: number): number {
        return this._host.getTable()
                        .setNISCellType(sName, rowID, colID, nType);
    }

    public getCellType(sName: string, rowID: string, colID: string): number {
        return this._host.getTable()
                        .getNISCellType(sName, rowID, colID);
    }

    public setCellLayout(sName: string, rowID: string, colID: string, sJson: string): number {
        return this._host.getTable()
                        .setNISCellLayout(sName, rowID, colID, sJson);
    }

    public setCellText(sName: string, sJson: string): number {
        return this._host.getTable()
                        .setNISCellText(sName, sJson);
    }

    public getCellText(sName: string, sJson: string): string {
        return this._host.getTable()
                        .getNISCellText(sName, sJson);
    }

    public setCellProp(sName: string, rowID: string, colID: string, sJson: string): number {
        return this._host.getTable()
                        .setNISCellProp(sName, rowID, colID, sJson);
    }

    public getCellProp(sName: string, rowID: string, colID: string, sPropName: string): string {
        return this._host.getTable()
                        .getNISCellProp(sName, rowID, colID, sPropName);
    }

    public getCellsPropOfFirstRow(sName: string, sPropName: string): string {
        return this._host.getTable()
                        .getNISCellsPropOfFirstRow(sName, sPropName);
    }

    public setDateCellFormat(sTableName: string, rowID: string, colID: string, nType: number, sFormat: string): number {
        return this._host.getTable()
                        .setNISDateCellFormat(sTableName, rowID, colID, nType, sFormat);
    }

    public setDateCellValue(sTableName: string, rowID: string, colID: string, sValue: string): number {
        return this._host.getTable()
                        .setNISDateCellValue(sTableName, rowID, colID, sValue);
    }

    public setDateCellProp(sTableName: string, rowID: string, colID: string, sJson: string): number {
        return this._host.getTable()
                        .setNISDateCellProp(sTableName, rowID, colID, sJson);
    }

    public setTimeCellFormat(sTableName: string, rowID: string, colID: string, nType: number, sFormat: string): number {
        return this._host.getTable()
                        .setNISTimeCellFormat(sTableName, rowID, colID, nType, sFormat);
    }

    public setTimeCellValue(sTableName: string, rowID: string, colID: string, sValue: string): number {
        return this._host.getTable()
                        .setNISTimeCellValue(sTableName, rowID, colID, sValue);
    }

    public setCompoundCellCodeAndValueByArray(
        sTableName: string, rowID: string, colID: string, sJson: string, nType?: number): number {
        return this._host.getTable()
                        .setNISCompoundCellCodeAndValueByArray(sTableName, rowID, colID, sJson, nType);
    }

    public setCompoundCellSeparator(sTableName: string, rowID: string, colID: string, sSeparator: string): number {
        return this._host.getTable()
                        .setNISCompoundCellSeparator(sTableName, rowID, colID, sSeparator);
    }

    public getCompoundCellCurrentValue(sTableName: string, rowID: string, colID: string, nType?: number): string {
        return this._host.getTable()
                        .getNISCompoundCelllCurrentValue(sTableName, rowID, colID, nType);
    }

    public getCompoundCellCurrentCode(sTableName: string, rowID: string, colID: string, nType?: number): string {
        return this._host.getTable()
                        .getNISCompoundCelllCurrentCode(sTableName, rowID, colID, nType);
    }

    public setNumCellMaxValue(sTableName: string, rowID: string, colID: string, maxValue: number): number {
        return this._host.getTable()
                        .setNISNumCellMaxValue(sTableName, rowID, colID, maxValue);
    }

    public getNumCellMaxValue(sTableName: string, rowID: string, colID: string): number {
        return this._host.getTable()
                        .getNISNumCellMaxValue(sTableName, rowID, colID);
    }

    public setNumCellMinValue(sTableName: string, rowID: string, colID: string, minValue: number): number {
        return this._host.getTable()
                        .setNISNumCellMinValue(sTableName, rowID, colID, minValue);
    }

    public getNumCellMinValue(sTableName: string, rowID: string, colID: string): number {
        return this._host.getTable()
                        .getNISNumCellMinValue(sTableName, rowID, colID);
    }

    public setNumCellPrecision(sTableName: string, rowID: string, colID: string, precision: number): number {
        return this._host.getTable()
                        .setNISNumCellPrecision(sTableName, rowID, colID, precision);
    }

    public getNumCellPrecision(sTableName: string, rowID: string, colID: string): number {
        return this._host.getTable()
                        .getNISNumCellPrecision(sTableName, rowID, colID);
    }

    public setQuickCellValueByArray(sName: string, rowID: string, colID: string, sJson: string): number {
        return this._host.getTable()
                        .setNISQuickCellValueByArray(sName, rowID, colID, sJson);
    }
}
