import { StructurePro } from './apollo/NodeType';
import { Region, RegionManager } from '../../model/core/Region';

export class RegionCore {
    private host: any;
    private style: any;
    private doc: any;
    private manager: any;
    constructor(host: any) {
        this.host = host;
        this.doc = host.doc;
    }

    public makeUniqueName(): string {
        return this.getRegionManager()
            .makeUniqueName();
    }

    public addRegion(region: Region, parent?: Region): void {
        const manager = this.getRegionManager();
        manager.addPasteRegion(region, parent);
    }

    public resetPaste(): void {
        const manager = this.getRegionManager();
        manager.resetPaste();
    }

    public getCurrentMaxDepth(): number {
        let regions = this.getRegionManager()
            .getAllPasteRegion();
        if (!regions.length) {
            return 0;
        }
        regions = regions.slice()
            .reverse();
        // const index = regions.findIndex((item) => item.parent === undefined);
        // const count = regions.length - index + 1;
        const parents = [];
        for (let curIndex = regions.length - 1; curIndex >= 0; curIndex--) {
            const item = regions[curIndex];
            if (item.parent === undefined) {
                parents.push(item);
                regions.splice(curIndex, 1);
            }
        }
        if (!regions.length) {
            return 1;
        }
        let depth = 1;
        for (let curIndex = 0, len = parents.length; curIndex < len; curIndex++) {
            const currentDepth = this.getDepth(regions, parents[curIndex].region.getName());
            if (depth < currentDepth) {
                depth = currentDepth;
            }
        }

        return depth;
    }

    public getRegionProps(node: any): any {
        const attrs = node.attrs;
        const newControlProperty = {
            newControlName: this.makeUniqueName(),
            newControlSerialNumber: attrs[StructurePro.SerialNumber],
            isNewControlHidden: attrs[StructurePro.IsHidden] === '1',
            isNewControlCanntDelete: attrs[StructurePro.DeleteProtect] === '1',
            isNewControlCanntEdit: attrs[StructurePro.EditProtect] === '1',
            isNewControlReverseEdit: attrs[StructurePro.ReverseEdit] === '1',
            customProperty: this.parseCustomAttr(attrs[StructurePro.CustomProperty]),
            newControlTitle: attrs[StructurePro.Title],
            showTitle: attrs[StructurePro.ShowTitle] === '1',
            newControlMaxLength: this.parseAttrToNum(attrs[StructurePro.MaxLength]),
            newControlInfo: attrs[StructurePro.TipsContent],
            identifier: attrs[StructurePro.Identifier],
        };

        return newControlProperty;
    }

    private parseAttrToNum(str: string): number {
        if (!str) {
            return;
        }

        const res = parseFloat(str);
        if (isNaN(res)) {
            return;
        }

        return res;
    }

    private getDepth(regions: any, parentName: string): number {
        if (!regions.length) {
            return 0;
        }
        const arrs = [];
        for (let index = regions.length - 1; index >= 0; index--) {
            const item = regions[index];
            if (item.parent === parentName) {
                arrs.push(item);
                regions.splice(index, 1);
            }
        }

        if (arrs.length === 0) {
            return 0;
        }
        let depth = 1;
        for (let index = 0, len = arrs.length; index < len; index++) {
            const currentDepth = this.getDepth(regions, arrs[index].region.getName());
            if (depth < currentDepth) {
                depth = currentDepth;
            }
        }

        return depth + 1;
    }

    private parseStyle(style: any): any {
        this.style = this.host.getTextProperty(style);
    }

    private getRegionManager(): RegionManager {
        if (this.manager) {
            return this.manager;
        }

        return this.manager = this.doc.getRegionManager();
    }

    private parseCustomAttr(str: string): any {
        if (!str) {
            return;
        }

        let res: any;
        try {
            res = JSON.parse(str);
        } catch (error) {
            return;
        }
        if (!res || res.length === 0) {
            return;
        }
        return res.map((prop) => {
            return {
                ...prop,
            };
        });
    }
}
