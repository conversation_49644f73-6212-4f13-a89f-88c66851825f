import { BaseXmlComponent } from "../file/xml-components/base";

export class Formatter {
    public format(input: BaseXmlComponent): string {
        const output = input.prepForXml();
        if (output) {
            // console.log(output);
            return '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>' + output.text;
        } else {
            throw Error('XMLComponent did not format correctly');
        }
    }
}
