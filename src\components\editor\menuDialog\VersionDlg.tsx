// VersionDialog.tsx
import * as React from 'react';
import { VERSION } from '../../../version';

interface IProps {
    documentCore: any;
    visible?: boolean;
    id: string;
    close: (name: string | number, bRefresh?: boolean) => void;
}

interface IState {
    bRefresh: boolean;
}

export default class VersionDialog extends React.Component<IProps, IState>{
    private visible: boolean;
    constructor(props: any) {
        super(props);
        this.state = {
            bRefresh: false,
        };
        this.visible = this.props.visible;
    }

    public render(): any {
        // 自定义对话框样式，确保没有滚动条
        const styles = {
            // 对话框容器
            dialogContainer: {
                position: 'fixed' as const,
                top: 0,
                left: 0,
                width: '100%',
                height: '100%',
                display: this.visible ? 'flex' : 'none',
                justifyContent: 'center' as const,
                alignItems: 'center' as const,
                zIndex: 1000,
                backgroundColor: 'rgba(0, 0, 0, 0.5)',
                fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif',
            },
            // 对话框主体
            dialogBox: {
                width: '420px',
                height: '340px', // 增加高度
                backgroundColor: '#ffffff',
                borderRadius: '8px',
                boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
                display: 'flex',
                flexDirection: 'column' as const,
                overflow: 'hidden',
            },
            // 对话框标题栏
            dialogHeader: {
                padding: '12px 16px',
                borderBottom: '1px solid #f0f0f0',
                display: 'flex',
                justifyContent: 'space-between' as const,
                alignItems: 'center' as const,
            },
            dialogTitle: {
                margin: 0,
                fontSize: '16px',
                fontWeight: 600,
                color: '#1f2937',
            },
            closeButton: {
                background: 'none',
                border: 'none',
                fontSize: '18px',
                cursor: 'pointer',
                color: '#9ca3af',
            },
            // 对话框内容区域
            dialogContent: {
                flex: 1,
                padding: '16px 24px',
                display: 'flex',
                flexDirection: 'column' as const,
                overflow: 'hidden', // 防止滚动条
                justifyContent: 'space-between', // 确保内容均匀分布
            },
            // 对话框底部
            dialogFooter: {
                padding: '12px 16px',
                borderTop: '1px solid #f0f0f0',
                display: 'flex',
                justifyContent: 'flex-end' as const,
            },
            // 关于对话框内容样式
            aboutHeader: {
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                marginBottom: '20px',
            },
            logoIcon: {
                width: '40px',
                height: '40px',
                marginRight: '12px',
                backgroundImage: `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%233B82F6'%3E%3Cpath d='M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5'/%3E%3C/svg%3E")`,
                backgroundSize: 'contain',
                backgroundRepeat: 'no-repeat',
            },
            productName: {
                fontSize: '22px',
                fontWeight: 600,
                color: '#1f2937',
                margin: 0,
            },
            infoSection: {
                marginBottom: '8px', // 减少底部间距
            },
            infoItem: {
                margin: '8px 0',
                color: '#374151',
                fontSize: '14px',
                lineHeight: '1.6',
            },
            link: {
                color: '#3B82F6',
                textDecoration: 'none',
            },
            divider: {
                margin: '8px 0', // 减少上下间距
                height: '1px',
                backgroundColor: '#e5e7eb',
                border: 'none',
            },
            copyright: {
                textAlign: 'center' as const,
                color: '#6B7280',
                fontSize: '13px',
                paddingTop: '8px',
                marginBottom: '4px', // 确保底部有一些间距
            },
            copyrightText: {
                margin: '4px 0',
            },
            // 按钮样式
            primaryButton: {
                backgroundColor: '#3B82F6',
                color: 'white',
                border: 'none',
                padding: '6px 16px',
                borderRadius: '4px',
                cursor: 'pointer',
                fontSize: '14px',
                fontWeight: 500,
            }
        };

        // 使用自定义对话框而不是编辑器的 Dialog 组件
        return (
            <div style={styles.dialogContainer}>
                <div style={styles.dialogBox}>
                    <div style={styles.dialogHeader}>
                        <h3 style={styles.dialogTitle}>关于鸿至编辑器</h3>
                        <button style={styles.closeButton} onClick={() => this.close()}>×</button>
                    </div>
                    <div style={styles.dialogContent}>
                        <div>
                            <div style={styles.aboutHeader}>
                                <div style={styles.logoIcon}></div>
                                <h2 style={styles.productName}>鸿至编辑器</h2>
                            </div>
                            <div style={styles.infoSection}>
                                <p style={styles.infoItem}>内核版本: v{VERSION}</p>
                                <p style={styles.infoItem}>开发商: 上海鸿腾卓至信息科技有限公司</p>
                                <p style={styles.infoItem}>
                                    官网: <a href="https://www.flowkit.cn" target="_blank" rel="noopener noreferrer" style={styles.link}>https://www.flowkit.cn</a>
                                </p>
                            </div>
                        </div>
                        <div>
                            <hr style={styles.divider} />
                            <div style={styles.copyright}>
                                <p style={styles.copyrightText}>Copyright © {new Date().getFullYear()} 上海鸿腾卓至信息科技有限公司</p>
                                <p style={styles.copyrightText}>保留所有权利</p>
                            </div>
                        </div>
                    </div>
                    <div style={styles.dialogFooter}>
                        <button style={styles.primaryButton} onClick={() => this.close()}>确定</button>
                    </div>
                </div>
            </div>
        );
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        this.visible = nextProps.visible;
        // 强制重新渲染
        this.setState({bRefresh: !this.state.bRefresh});
    }

    // 不再需要 open 方法，因为我们使用自定义对话框

    private close = (bRefresh?: boolean): void => {
        this.visible = false;
        this.props.close(this.props.id, bRefresh);
        this.setState({bRefresh: !this.state.bRefresh});
    }
};
