import { RecalcResultType } from './Document';
import TextProperty, { ITextProperty } from './TextProperty';
import { IDrawSelectionsLine, IDrawNewControlBounds } from '../DocumentCore';
import ParaLine from './Paragraph/ParaLine';
import {DocumentContentType, CursorType} from './Style';
import DocumentContentBase from './DocumentContentBase';
import DocumentFrameBounds from './FrameBounds';
import { ICurrentCursorPos } from './Paragraph/ParagraphSearchPosXY';
import MouseEventHandler, { IMouseEvent } from '../../common/MouseEventHandler';
import { CleanModeType, ElementDefaultIndex, EquationType, ImageMediaType, INewControlProperty, IParaNumPr, ITableProperty,
    ReviewType } from '../../common/commonDefines';
import { ParagraphContentPos } from './Paragraph/ParagraphContent';
import HeaderFooter from './HeaderFooter';
import { NewControl } from './NewControl/NewControl';
import { idCounter } from './util';
import { ParaElementBase } from './Paragraph/ParaElementBase';
import { IParaProperty } from '../ParaProperty';
import {Comment} from './Comment/Comment';
import { ParagraphRecalcObject } from './Paragraph';
import { Region } from './Region';
import { ReviewInfo } from './Revision';
import ParaPortion from './Paragraph/ParaPortion';
import { TableBase } from './TableBase';
import { ICheckSelectionNewControls } from './Selection';
import { IOperateResult } from './History';

/**
 * 段落和表格类的基类
 */
export default class DocumentContentElementBase {
    public id: number; // 唯一标示，自动生成
    public index: number; // 顺序

    public parent: DocumentContentBase;
    public prev: DocumentContentElementBase;
    public next: DocumentContentElementBase;

    public x: number;
    public y: number;
    public xLimit: number;
    public yLimit: number;
    public pageNum: number; // 所在页面的页码

    public bApplyToAll: boolean;  // 应用于整个段落、表格，比如：选中整个段落、表格，设置整个段落、表格的属性等
    public logicDocument: any;

    // startPos: number;
    // endPos: number;

    constructor(parent: DocumentContentBase) {
        this.id = idCounter.getNewId();
        this.index = ElementDefaultIndex.Default;

        this.parent = parent;
        this.prev = null;
        this.next = null;

        this.x = 0;
        this.y = 0;
        this.xLimit = 0;
        this.yLimit = 0;
        this.pageNum = 0;
        this.bApplyToAll = false;
        // this.startPos = 0;
        // this.endPos = 0;
    }

    public getId(): number {
      return this.id;
    }

    public getName(): string {
        return;
    }

    public parentHidden(): boolean {
        return false;
    }

    public getType(): number {
        return DocumentContentType.Unknown;
    }

    /**
     * 插入批注的Portion
     * @param comment 批注对象
     * @param pos 插入位置
     * @param bStart 是否是起点
     */
     public insertCommentPortion(comment: Comment, pos: ParagraphContentPos, bStart?: boolean): ParagraphContentPos {
        return pos.copy();
    }

     /**
      * 在指定位置插入Portion
      * @param portion 需要插入的Portion
      * @param pos 插入的位置
      */
    public insertPortion(portion: any, pos: ParagraphContentPos): ParagraphContentPos { return pos.copy(); }

    public addComment(bSamePara: boolean, commentId: number, comments?: Comment[]): number {
        return;
    }

    public deleteCommentPortion(contentPos: ParagraphContentPos, bDelete?: boolean): void {
        return;
    }

    public setDocumentNext(o: DocumentContentElementBase): void {
        this.next = o;
    }

    public getDocumentNext(): DocumentContentElementBase {
        return this.next;
    }

    public setDocumentPrev(o: DocumentContentElementBase): void {
        this.prev = o;
    }

    public getDocumentPrev(): DocumentContentElementBase {
        return this.prev;
    }

    public getParaContentByXY(mouseEvent: MouseEventHandler, pageIndex: number): ParaPortion {
        return;
    }

    /**
     * 是否包含表格元素
     * @returns true/false
     */
    public isContainerTableOrTitle(): boolean {
        return false;
    }

    /**
     * 返回选中范围内的段落（不包括隐藏段落）
     * @returns 段落集合
     */
    public getSelectParas(): DocumentContentElementBase[] {
        return;
    }

    public setContentPos(pos: ParagraphContentPos): boolean {
        return false;
    }

    /**
     * 获取当前元素所在的页索引
     * @param pos 当前元素具体位置
     */
    public getCurrentPageByPos(pos: ParagraphContentPos): number {
        return -1;
    }

    /**
     * 根据选中区域返回开始或者结束元素的位置
     * @param pos 数据收集 【表格索引、行索引、列索引、段落索引、部分索引、内容索引】或 【段落索引、部分索引、内容索引】
     * @param bStart 是否开始标志
     */
    public getSelectionNodePos(pos: ParagraphContentPos, bStart: boolean, bApplyToAll?: boolean): void {
        return;
    }

    public isHeaderFooter(bReturnHeaderFooter: boolean): boolean | HeaderFooter {
        return this.parent.isHeaderFooter(bReturnHeaderFooter);
    }

    public getRegion(): Region {
        return;
    }

    public selectTopPos(): void {
        return;
    }

    public filterContentNodes(type: number): any[] {
        return [];
    }

    public getFoucsInRegion(pointX: number, pointY: number, pagetIndex: number): Region {
        return;
    }

    public getCurParaPortion(): ParaPortion {
        return;
    }

    /**
     * 获取当前元素往上的索引值
     * 当前元素可能是table、para、region
     */
    public getParentPos(pos: ParagraphContentPos): void {
        pos.splice(0, 0, [this.index]);
        if ('getParentPos' in this.parent) {
            (this.parent as any).getParentPos(pos);
        }
    }

    /**
     * 获取选中的内容
     * @param element 当前创建的元素 表格或段落
     * @param names 结构化名称集合
     */
    public getSelectedContent(element: DocumentContentElementBase, names: string[],
                              bKeepHalfStructBorder: boolean = false, option?: any):
        DocumentContentElementBase[] | void {
        return;
    }

    public getParent(): DocumentContentBase {
        return this.parent;
    }

    public setParent(parent: DocumentContentBase): void {
        this.parent = parent;
    }

    public setDocumentIndex(index: number): void {
        this.index = index;
    }

    public getStartPos(pos?: ParagraphContentPos): void {
        return;
    }

    public getEndPos(bParaEnd: boolean, pos?: ParagraphContentPos): void {
        return;
    }

    public setParentPos(pos: ParagraphContentPos): void {
        return;
    }

    public getDocumentIndex(): number {
        return this.index;
    }

    public reset(x: number, y: number, xLimit: number, yLimit: number, pageAbs: number): void {
        this.x            = x;
        this.y            = y;
        this.xLimit       = xLimit;
        this.yLimit       = yLimit;
        this.pageNum      = pageAbs;
    }

    public recalculatePage(curPage: number, bHidden?: boolean): RecalcResultType {
        return RecalcResultType.RecalResultNextElement;
    }

    public getLineCount(): number {
        return null;
    }

    public getPages(): any  {
        return null;
    }

    public getLines(): any  {
        return null;
    }

    public getLineById(id: number): ParaLine {
        return null;
    }

    public getContent(): any {
        return null;
    }

    public getCurPos(): any {
        return null;
    }

    /**
     * 获取所在文档的页码索引
     * @param curPage
     */
    // getAbsolutePageIndex(curPage: number = 0): number{
    //     return this.pageNum + curPage;
    // }

    // getStartPageIndex(): number {
    //     return this.pageNum;
    // }

    public getAbsolutePage(curPage: number = 0): number {
        return this.getAbsolutePageIndex(curPage);
    }

    public getCurrPage(): number {
        return this.pageNum;
    }

    /**
     * the page index where a paragraph starts at
     */
    public getAbsoluteStartPage(): number {
        return this.getAbsolutePage(0);
    }

    /**
     * 当前元素的开始页面
     */
    public getRelativeStartPage(): number {
        return this.pageNum;
    }

    public getRelativePageIndex(curPage: number): number {
        return this.pageNum + curPage;
    }

    public getPageBounds(curPage: number): DocumentFrameBounds {
        return new DocumentFrameBounds(this.x, this.y, this.xLimit, this.yLimit);
    }

    public getSectPr(): any {
        return undefined;
    }

    public clear(): void { return ; }

    public isEmpty(): boolean {
        return false;
    }

    public isEmptyPage(curPage: number): boolean {
        return false;
    }

    public isInline(): boolean {
        return true;
    }

    public addDocContentChild(option: any, cleanMode: CleanModeType): void {
        return;
    }

    public startFromNewPage(): void { return ; }

    public isContentOnFirstPage(): boolean {
        return false;
    }

    public isStartFromNewPage(): boolean {
        return false;
    }

    public setLogicDocument(doc: any): void {
        if ( doc ) {
            this.logicDocument = doc;
        }
    }

    public getElementByPos(index: any, bCompositeInput: boolean = false ): any {
        return null;
    }

    public getParaContentPos(bSelection?: boolean, bStart?: boolean): any {
        return null;
    }

    public setImageSelection(startPos: ParagraphContentPos): void {
        return null;
    }

    public getSelectedParaPros(paraPro: IParaProperty): boolean {
        return false;
    }

    public getTopElement(): DocumentContentElementBase {
        return;
    }

    public moveCursorToXY(elementPageIndex: number, pointX: number, pointY: number,
                          bLine: boolean = false, bDontChangeRealPos: boolean = true): void { return ; }

    public moveCursorToStartPos( bAddToSelect: boolean = false, bSelectFromStart: boolean = false ): void { return ; }

    public moveCursorToEndPos( bAddToSelect: boolean = false, bSelectFromStart: boolean = false ): void { return ; }

    public moveCursorLeft( bShiftLey: boolean  = false ): boolean {
        return false;
    }

    public getLastVisibleLine(clientY: number, bEnd?: boolean): {x: number, y: number, xLimit: number} { return ; }

    public moveCursorRight( bShiftLey: boolean  = false ): boolean {
        return false;
    }

    public moveCursorUp( bShiftLey: boolean  = false ): boolean {
        return false;
    }

    public moveCursorDown( bShiftLey: boolean  = false ): boolean {
        return false;
    }

    public getSelectedTextProperty(textPros: ITextProperty): boolean {
        return;
    }

    public moveCursorRightWithSelectionFromStart(): void { return ; }

    public moveCursorLeftWithSelectionFromEnd(): void { return ; }

    public moveCursorDownToFirstRow( pointX: number, pointY: number, bShiftLey: boolean ): void { return ; }

    public moveCursorUpToLastRow( pointX: number, pointY: number, bShiftLey: boolean ): void { return ; }

    public remove( direction: number, bOnlyText: boolean, bOnlySelection: boolean,
                   bAddText: boolean, bSelectParaEnd?: boolean,
                   checkNewControls?: ICheckSelectionNewControls, option?: any): IOperateResult {
        return {res: true, bNeedRecal: true};
    }

    public removePortion(nPos: number, length: number): void { return ; }

    public add(item: any): number { return ; }

    /**
     * 护理表格设置内容专用
     * @param paraItem
     * @returns
     */
    public add2(paraItem: any, bDirty?: boolean): number { return ; }

    public addToParagraph(item: any): number {
        return this.add(item);
    }

    public resetParagraphCursorPos(): void { return ; }


    /**
     * 根据坐标位置设置选择区域
     * @param startPos 开始位置
     * @param endPos 结束位置
     * @param direction 方向：1：正方向，-1：反方向（未实现）
     */
    public selectAreaByPos(startPos: ParagraphContentPos, endPos: ParagraphContentPos, direction?: number): boolean {
        return false;
    }

    /**
     * 跳过或者删掉隐藏的元素
     * @param direction 删除方向：Backspace是删除光标前的元素：-1，Delete键是删除光标后面的元素：1
     */
    public jumpOrDeleteTheHidden(direction: number = -1): number {
        return;
    }

    /**
     * 在指定位置插入内容集合
     * @param items 插入项集合
     * @param curPos 插入位置
     * @param addAfter 是否插入在当前位置后面
     */
    public addChildrenToContent(items: any[], curPos?: number, addAfter?: boolean): number {
        return 2;
    }

    public addToContent( nPos: number, item: any , unEmptyPara?: boolean): void {
        return ;
    }

    public getParaLines(pageIndex: number, result?: {paras: any[], tables: any[]}): any {
        return;
    }

    public getCurrentPageLastLine(pageIndex: number, result?: any): any {
        return;
    }

    public getParentSumPageIndex(): number {
        return this.parent.getParentSumPageIndex() + this.pageNum;
    }

    /**
     * 只读判断
     * @param bOnlySelectCells 是否只考虑选中的单元格
     * @returns
     */
    public isReadOnly(bOnlySelectCells?: boolean): boolean {
        return false;
    }

    public isCursorReadOnly(): boolean { return false; }

    public isHidden(): boolean {
        return false;
    }

    public getHidden(): boolean {
        return false;
    }

    public addNewParagraph(): void { return ; }

    public getCursorPosXY(): any {
        return {
          pageNum: 0, // 绝对页码
            x: 0,
            y: 0,
        };
    }

    public isCursorAtEnd(): boolean {
        return false;
    }

    public isCursorAtBegin(): boolean {
        return false;
    }

    public recalculateCurPos(bUpdateX: boolean = true, bUpdateY: boolean = true): ICurrentCursorPos {
        return {
            x: 0,
            y: 0,
            pageNum: 0,
            line: 0,
            page: 0,
        };
    }

    public getSelection(): any {
        return null;
    }

    public selectAll( direction: number ): void { return ; }

    public isSelectionEmpty( bContainParaEnd?: boolean ): boolean {
        return true;
    }

    public isSelectionUse(): boolean {
        return false;
    }

    public isSelectedAll( bContainParaEnd?: boolean ): boolean {
        return false;
    }

    public removeSelection(): void { return ; }

    public getSelectionBounds(bStart: boolean, bMultis: boolean, pageIndex?: number): any {
        return null;
    }

    public getSelectionStartPos( direction: number ): any {
        return null;
    }

    // tslint:disable-next-line: no-empty
    public startSelectionByCurPos(): void {}

    public setSelectionStart(pointX: number, pointY: number, curPage: number, mouseEvent?: IMouseEvent,
                             bTableBorder?: boolean): void { return ; }

    /**
     * newcontrol左边框辅助自动选中
     */
    public setSelectionStart2(): void { return ; }

    /**
     * newcontrol右边框辅助自动选中
     */
    public setSelectionStart3(): void { return ; }

    public setSelectionEnd(pointX: number, pointY: number, curPage: number, mouseEvent?: IMouseEvent,
                           bTableBorder?: boolean): void { return ; }

    public setSelectionUse( bUse: boolean ): void { return ; }

    public setSelectionBeginEnd( bSelectStart: boolean, bEnd: boolean ): void { return ; }

    public getSelectionDirection(): number {
        return 0;
    }

    public getSelectedLines( selections: any, bParaEnd?: boolean ): any {
        return null;
    }

    public checkPosInSelection( pageIndex: number, pointX: number, pointY: number ): boolean {
        return false;
    }

    /**
     * 停止选择
     */
    public stopSelection(): void {
        // if ( true !== this.selection.bUse )
        //     return;

        // this.selection.bStart = false;

        // if ( this.content[this.selection.startPos] )
        //     this.content[this.selection.startPos].stopSelection();
    }

    public concat(element: any, bDelFromKeyDown?: boolean): void { return ; }

    public contentConcat(element: any): void { return ; }

    public setParagraphProperty(paraProperty: any): boolean {
        return false;
    }

    public getParagraphProperty(): any {
        return null;
    }

    public hasPageBreak(): boolean {
        return false;
    }

    public setParagraphAlignment(alignment: number): boolean {
        return false;
    }

    public getDirectTextProperty(): TextProperty {
        return null;
    }

    public checkParaEnd(pos: number): void { return ; }

    public split(para: DocumentContentElementBase): void { return ; }

    public getLinesByPageId( pageIndex: number ): any {
        return null;
    }

    public getElementStartLineByPageId( pageIndex: number ): number {
        return null;
    }

    public getElementEndLineByPageId( pageIndex: number ): number {
        return null;
    }

    public getDocumentElementState(): any {
        return null;
    }

    public getSelectText(bSelectAll?: boolean, needPara?: boolean): string {
        return;
    }

    public getSelectTextAI(bSelectAll?: boolean, needPara?: boolean): string {
        return;
    }

    /**
     * 获取选择文本，去除隐藏内容
     * @param bSelectAll
     * @returns
     */
    public getSelectTextNoHidden(bSelectAll?: boolean): string {
        return;
    }

    public getTextLength(bSkipSomeCh: boolean = false): number { return 0; }

    public setDocumentElementState(state: any, stateIndex: number): void { return ; }

    public shift(curPage: number, shiftDx: number, shiftDy: number): void { return ; }

    public isTableBorder(pointX: number, pointY: number, pageIndex: number, options?: any): TableBase {
        return null;
    }

    public isSelectedSingleElement(): boolean {
        return this.parent.isSelectedSingleElement();
    }

    public getNumberingPr(): IParaNumPr {
        return;
    }

    public isCellSelection(): boolean {
        return false;
    }

    public getCurrentParagraph(): any {
        return null;
    }

    public addToChildren(element: any, index: number): void {
        return ;
    }

    public updatePortionCursor(): void {
        return ;
    }

    public splitPortion(): void { return ; }

    /**
     * 删除此元素前需要调用此函数
     */
    public preDelete(): void { return ; }

    public removeTableRow(rowIndex?: number): boolean { return false; }

    public removeTableRow2(): boolean { return false; }

    public removeTableRow3(): void { return ; }

    public removeTableColumn(): boolean { return false; }

    public isInnerTable(): boolean {
        return false;
    }

    /**
     * 判断当前光标，或当前选择区域是否在单元格内
     */
    public isInTableCell(): boolean { return false; }

    public canMergeTableCells(): boolean {
        return false;
    }

    public mergeTableCells(bClearMerge?: boolean): boolean {
        return false;
    }

    public splitTableCells(rows: number, cols: number): boolean {
        return false;
    }

    public moveCursorToCell(): void { return ; }

    public getCurPageLastNode(): ParaElementBase {
        return;
    }

    public getEmptyHeight(): number { return 0; }

    public recalculateMinmaxContentWidth(bRotated: boolean): { Min: number, Max: number} {
        return { Min: 0, Max: 0};
    }

    public copy(parent: DocumentContentBase, option?: any): DocumentContentElementBase { return null; }

    public addInlineImage(width: number, height: number, src: string,
                          name: string, type: EquationType, svgElem?: any,
                          mediaType?: ImageMediaType, mediaSrc?: string, datas?: any): string { return ; }

    public setApplyToAll(bAll: boolean): void {
        this.bApplyToAll = bAll;
    }

    public getApplyToAll(): boolean {
        return this.bApplyToAll;
    }

    public getHeaderFooter(): boolean | HeaderFooter {
        if (this.parent) {
            return this.parent.isHeaderFooter(true);
        }
        return null;
    }

    public addNewControl(newControl: NewControl, parentNewControl: NewControl,
                         property: INewControlProperty, sText?: string): number {
        return 0;
    }

    public addNewControlBySelectedPos(newControl: NewControl, bPos: number, direction: number = 1): number {
        return 0;
    }

    // public getNewControlLeftBorderPos(): number {
    //     return -1;
    // }

    // public getNewControlRightBorderPos(): number {
    //     return -1;
    // }

    /**
     * 获取当前光标位置所在的newControl元素
     */
    public isCursorInNewControl(): boolean {
        return false;
    }

    /**
     * 获取当前光标位置所在的newControl元素
     */
    public getCursorInNewControl(): NewControl {
        return null;
    }

    public getNewControlParaBounds( newControlName: string, startPos: number,
                                    endPos: number ): IDrawSelectionsLine[] {
        return null;
    }

    /**
     * 当前鼠标位置是否处于newControl元素内
     * @param pointX
     * @param pointY
     * @param pageIndex
     */
    public isFocusInNewControl(pointX: number, pointY: number, pageIndex: number): boolean {
        return false;
    }

    /**
     * 获取当前鼠标位置所在的newControl元素
     * @param pointX
     * @param pointY
     * @param pageIndex
     */
    public getFocusInNewControl(pointX: number, pointY: number, pageIndex: number): NewControl {
        return null;
    }

    public getNewControlBounds( newControl: NewControl, mouseEvent?: MouseEventHandler,
                                pageIndex?: number ): IDrawNewControlBounds {
        return null;
    }

    /**
     * 根据当前插入newControl的光标位置，获取在documentContentBase中的相关位置信息
     * @param portionPos
     */
    public getCurContentPosInDoc(bSelection?: boolean, bStart?: boolean, bDeep?: boolean): ParagraphContentPos {
        return null;
    }

    /**
     * 当前选择区域是否有不可删除内容
     * 或者当前光标处不可删除
     */
    public isValidDelete( startPos: ParagraphContentPos, endPos: ParagraphContentPos ): boolean {
        return this.parent.isValidDelete(startPos, endPos);
    }

    public getNewControlNamesByPos( startPos: ParagraphContentPos, endPos: ParagraphContentPos ): string[] {
        const newControlManager = this.parent.getNewControlManager();
        if ( null != newControlManager ) {
            return newControlManager.getNewControlNamesByPos(startPos, endPos);
        }

        return null;
    }

    public isSelectionInNewControl( startPos: ParagraphContentPos, endPos: ParagraphContentPos ): boolean {
        const newControlManager = this.parent.getNewControlManager();
        if ( null != newControlManager ) {
            return newControlManager.isSelectionInNewControl(startPos, endPos);
        }

        return false;
    }

    public getNewControlText( startPortionIndex: number, endPortionIndex: number, bParaEnd: boolean = false,
                              bSection: boolean = false ): string {
        return '';
    }

    public removeNewControlText( startPortionIndex: number, endPortionIndex: number ): void {
        return ;
    }

    /**
     * 根据portionId获取portion在paragraph中的index位置
     * @param portionId
     */
    public getPortionIndexById( portionId: number ): number {
        return -1;
    }

    public setTableProps( props: ITableProperty ): boolean {
        return false;
    }

    public getTableProps(): ITableProperty {
        return null;
    }

    public updateCursorType(pointX: number, pointY: number, pageIndex: number): void {
        return ;
    }

    public setCursorType(type: CursorType): void {
        return ;
    }

    public addTableRow(bBefore: boolean): boolean {
        return false;
    }

    public addTableColumn(bBefore: boolean): boolean {
        return false;
    }

    // public isDeleteProtect(): boolean {
    //     return false;
    // }

    public isSelectedOrCursorInNewControlTitle(): boolean {
        return false;
    }

    public getPosition(type?: any): {x: number, y: number, pageNum: number} {
        return;
    }

    public acceptRevisionChanges(bAll: boolean): void { return ; }

    public rejectRevisionChanges(bAll: boolean): void { return ; }

    public acceptPrChanges(): void { return ; }

    public rejectPrChanges(): void { return ; }

    public getReviewType(): ReviewType { return ReviewType.Common; }

    public setReviewType(type: ReviewType): void { return ; }

    public getReviewInfo(): ReviewInfo { return new ReviewInfo(null); }

    public setReviewTypeWithInfo(reviewType: ReviewType, reviewInfo: ReviewInfo): void { return ; }

    public getFocusRevision(pointX: number, pointY: number, pageIndex: number): any { return null; }
    public getAllRevision(): any[] { return []; }

    public checkRevisionsChanges(): void { return ; }

    public getElementById(elementId: number): DocumentContentElementBase {
        return null;
    }

    public saveRecalculateObject(): ParagraphRecalcObject {
        return null;
    }

    public loadRecalculateObject(recalcObj: ParagraphRecalcObject): void {
        return null;
    }

    public getTopDocument(): DocumentContentBase {
        return this.parent.getTopDocument();
    }

    public canAddNewParagraphByEnter(): boolean { return false; }

    public addNewParagraphByEnter(): void { return ; }

    public getCurrentTable(): TableBase { return null; }

    public isInTable(): boolean { return false; }

    public isParagraph(): boolean {
        return (DocumentContentType.Paragraph === this.getType());
    }

    public isTable(): boolean {
        return (DocumentContentType.Table === this.getType());
    }

    /**
     * 是否是护理表格
     * @returns boolean
     */
    public isNISTable(): boolean {
        return false;
    }

    public isRegion(): boolean {
        return (DocumentContentType.Region === this.getType());
    }

    public isCursorInRegion(): boolean { return false; }

    public getCurrentRegion(): Region { return null; }

    public canDelete(): boolean {
        return true;
    }

    public getSearchInfos(search: any): void {
        return;
    }

    public isReverseEdit(): boolean {
        return false;
    }

    public isValidAddNewControl(): boolean {
        return true;
    }

    public getAllNewControls(): NewControl[] {
        return null;
    }

    public getContentByPos(pos: any): any { return null; }

    public getTableCellByXY(x: number, y: number, pageIndex: number): any { return null; }

    public getLastRowBorderSize(): number {
        return 0;
    }

    public isSelectedMultiRows(): boolean { return false; }

    /**
     * 插入软回车
     * @returns
     */
    public addSoftNewParagraph(pos?: number): number {
        return -1;
    }

    /*
     * 清除修订信息
     */
    public resetRevisions(): void { ; }

    public getContentText(): string {
        return '';
    }

    private getAbsolutePageIndex(curPage: number): number {
        return this.parent.getAbsolutePage(this.getRelativePageIndex(curPage));
    }
}
