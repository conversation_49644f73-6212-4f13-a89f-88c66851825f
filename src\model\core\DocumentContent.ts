import DocumentContentBase from './DocumentContentBase';
import DocumentPage, { ILimits } from './DocumentPage';
import Paragraph, { ParagraphRecalcObject } from './Paragraph';
import Document, { RecalcResultType, DocCurPosType, CurPos } from './Document';
import { TableCell } from './Table/TableCell';
import { DocumentContentType, CursorType } from './Style';
import { DocumentRecalcInfo } from './DocumentRecalculateState';
import DocumentFrameBounds from './FrameBounds';
import Selection, { SelectionFlagType, ICheckSelectionNewControls, ITableSelectionData,
    TableSelectionType } from './Selection';
import MouseEventHandler, { MouseEventType, IMouseEvent } from '../../common/MouseEventHandler';
import DocumentContentElementBase from './DocumentContentElementBase';
import ContentChanges, { ContentChangesElement } from './ContentChanges';
import { ParaElementBase } from './Paragraph/ParaElementBase';
import { ISelectionsPara , IDrawSelectionsLine, IDrawSelectionBounds,
    IDrawNewControlBounds, IDrawTableNewBorder } from '../DocumentCore';
import ParaDrawing, { ParaBarcode, ParaBigDrawing, ParaEquation, ParaMediaDrawing } from './Paragraph/ParaDrawing';
import { EquationType, INewControlProperty, ResultType,
    DocumentSectionType, ITableProperty, IRevisionChange, AlignType,
    MessageType,
    NeedStructType,
    ITableCellContentProps,
    CleanModeType,
    ImageMediaType} from '../../common/commonDefines';
import { ParaElementType, ParagraphContentPos } from './Paragraph/ParagraphContent';
import { ITextProperty } from './TextProperty';
import { ICurrentCursorPos } from './Paragraph/ParagraphSearchPosXY';
import { Table } from './Table';
import { NewControlManager } from './NewControlManager';
import { NewControl } from './NewControl/NewControl';
import HeaderFooter from './HeaderFooter';
import ParaPortion from './Paragraph/ParaPortion';
import ParaPageNum from './Paragraph/ParaPageNum';
import ParaProperty from './Paragraph/ParaProperty';
import { ICursorProperty } from '../CursorProperty';
import ParaText from './Paragraph/ParaText';
import { getAcitveNodeIndex } from './util';
import { IParaProperty } from '../ParaProperty';
import { GraphicObjects } from './GraphicObjects/GraphicObjects';
import { Comment } from './Comment/Comment';
import { ChangeDocumentContentAddItem, ChangeDocumentContentRemoveItem } from './DocumentContentChange';
import { DocumentElementState, DocumentState } from './HistoryState';
import { ParaTextProperty } from './Paragraph/ParaTextProperty';
import History, { IOperateResult } from './History';
import {Region} from './Region';
import { RevisionsManager } from './RevisionsManager';
import {TextProperty} from '../StyleProperty';
import { GlobalEvent as gEvent , GlobalEventName as gEventName } from '../../common/GlobalEvent';
import { ChangeTableAddTableName, ChangeTableRemoveTableNames } from './Table/TableChange';
import { TableManager } from './Table/TableManager';
import { TableBase } from './TableBase';
import { TableRow } from './Table/TableRow';
import { NewControlNumer } from './NewControl/NewControlNum';
import { CommentResultType } from './Comment/CommentManager';
import { HistroyItemType } from './HistoryDescription';

export class DocumentContent extends DocumentContentBase {
    public x: number;
    public y: number;
    public xLimit: number;
    public yLimit: number;
    public bSplit: boolean;

    public parent: TableCell | HeaderFooter | Region;
    public logicDocument: Document;

    public pages: DocumentPage[];

    // startColumn: number;
    // columnCount: number;

    public recalcInfo: DocumentRecalcInfo;
    public bTurnOffRecalc: boolean;

    public contentChanges: ContentChanges;

    public bApplyToAll: boolean;

    constructor( parent: TableCell | HeaderFooter | Region, logicDocument: Document, x: number, y: number,
                 xLimit: number, yLimit: number, bSplit: boolean ) {
        super();
        this.parent = parent;
        this.logicDocument = logicDocument;
        this.x = x;
        this.y = y;
        this.xLimit = xLimit;
        this.yLimit = yLimit;
        this.pages = [];
        // this.startColumn = 0;
        // this.columnCount = 1;
        this.bTurnOffRecalc = false;

        this.bSplit = bSplit;
        this.content[0] = new Paragraph(this, logicDocument,
                                parent instanceof TableCell || parent instanceof HeaderFooter);
        this.content[0].setDocumentPrev(null);
        this.content[0].setDocumentNext(null);
        this.content[0].getParagraphProperty().alignment = 0; // 左对齐
        this.curPos = new CurPos();
        this.selection = new Selection();
        this.contentChanges = new ContentChanges();
        this.bApplyToAll = false;
    }

    public reset(x: number, y: number, xLimit: number, yLimit: number, document?: Document): void {
        this.x = x;
        this.y = y;
        this.xLimit = xLimit;
        this.yLimit = yLimit;

        if ( 0 === this.curPos.x && 0 === this.curPos.y ) {
            this.curPos.x = x;
            this.curPos.y = y;
            this.curPos.realX = x;
            this.curPos.realY = y;
        }

        this.setLogicDocument(document);
    }

    public setStartPage( startPage: number ): void {
        this.startPage = startPage;
    }

    public getParaContentByXY(mouseEvent: MouseEventHandler, pageIndex: number): ParaPortion {
        const pos = this.getContentPosByXY(pageIndex, mouseEvent.pointX, mouseEvent.pointY);
        if (pos == null) {
            return;
        }

        return this.content[pos].getParaContentByXY(mouseEvent, pageIndex);
    }

    public getSelectionNodePos(pos: ParagraphContentPos, bStart: boolean, bApplyToAll: boolean): void {
        const contents = this.content;
        const activeIndex = getAcitveNodeIndex(this.selection, bStart, bApplyToAll, contents.length - 1);
        pos.add(activeIndex);
        contents[activeIndex].getSelectionNodePos(pos, bStart, bApplyToAll);
    }

    public getStartPage(): number {
        return this.startPage;
    }

    public getCursorInRegion(): Region {
        if (this.isRegionContent()) {
            return this.parent as Region;
        }

        if (this.isTableCellContent()) {
            const parent = (this.parent as TableCell).row.table.parent;
            if (parent.isRegionContent()) {
                return parent as any;
            }
        }

        return;
    }
    public getTableCellByXY(pointX: number, pointY: number, pageIndex: number): TableCell {
        const elementPos = this.getContentPosByXY(pageIndex, pointX, pointY);
        if ( elementPos === undefined ) {
            return null;
        }

        const element = this.content[elementPos];
        if (element) {
            if (element.isTable()) {
                const elementPageIndex = this.getElementPageIndex(elementPos, pageIndex);
                return element.getTableCellByXY(pointX, pointY, elementPageIndex);
            }

            return element.getTableCellByXY(pointX, pointY, pageIndex);
        }

        return null;
    }

    public shift(curPage: number, shiftDx: number, shiftDy: number): void {
        this.pages[curPage].shift(shiftDx, shiftDy);

        const startPos = this.pages[curPage].pos;
        const endPos = this.pages[curPage].endPos;

        for (let index = startPos; index <= endPos; index++) {
            const element = this.content[index];
            const pageIndex = this.getElementPageIndex(index, curPage);

            element.shift(pageIndex, shiftDx, shiftDy);
        }
    }

    public updateEndInfo(): void {
        for (let index = 0, count = this.content.length; index < count; index++) {
            // this.content[index].updateEndInfo();
        }
    }

    public getSearchInfos(search: any): void {
        const content = this.content;
        content.forEach((item) => {
            item.getSearchInfos(search);
        });
    }

    public isTableCellContent(): boolean {
      if (this.parent instanceof TableCell) {
        return true;
      }
      return false;
    }

    public getTableId(): number {
      if (this.isTableCellContent()) {
        if ('row' in this.parent) {
          return this.parent.row.table.id;
        }
      }
      return -1;
    }

    public getTable(): TableBase {
        if ( this.isTableCellContent() && 'row' in this.parent ) {
            return this.parent.row.table;
        }

        return undefined;
     }

    public getDocument(): Document {
        const doc: Document = this.logicDocument;
        if (!doc) {
            // doc = this;
        }

        return doc;
    }

    public getTopDocument(): DocumentContentBase {
        if ( this.parent instanceof TableCell ) {
            const table = this.parent.row.getTable();
            return table.getTopDocument();
        } else if ( this.parent instanceof Region) {
            return this.parent.getTopDocument();
        } else {
            return this;
        }
    }

    public getDocumentSectionType(): DocumentSectionType {
        const topParent = this.getTopDocument();
        if ( topParent instanceof Document ) {
            return DocumentSectionType.Document;
        // } else if ( topParent === this ) {
        } else if ( topParent instanceof DocumentContent ) {
            // if ( topParent instanceof HeaderFooter ) {
            // const parent = this.parent as HeaderFooter;
            const parent = topParent.parent;
            if (parent instanceof HeaderFooter) {
                return parent.isHeader() ? DocumentSectionType.Header : DocumentSectionType.Footer;
            }
            // }
        }

        return DocumentSectionType.Document;
    }

    public deleteCommentPortion(pos: ParagraphContentPos, bDeleteComment: boolean = false): void {
        pos = pos.copy();
        this.content[pos.shift()].deleteCommentPortion(pos, bDeleteComment);
    }

    public addComment(bSamePara: boolean, commentId: number, comments: Comment[],
                      bApplyToAll: boolean = false): number {
        const bStart = commentId !== undefined;
        const contents = this.content;
        const selection = this.selection;
        let startParaPos: number;
        let endParaPos: number;
        if (bApplyToAll === true) {
            startParaPos = 0;
            endParaPos = contents.length - 1;
        } else {
            startParaPos = selection.startPos;
            endParaPos = selection.endPos;
            if (startParaPos === endParaPos) {
                bSamePara = true;
            } else if (startParaPos > endParaPos) {
                const tem = startParaPos;
                startParaPos = endParaPos;
                endParaPos = tem;
                bSamePara = false;
            }
            // endParaPos++;
        }

        let contentPos: number;

        if (bStart) {
            contentPos = startParaPos;
        } else {
            contentPos = endParaPos;
        }
        const content = this.content[contentPos];
        content.setApplyToAll(bApplyToAll);
        const num = content.addComment(bSamePara, commentId, comments);
        content.setApplyToAll(false);
        return num;
    }

    public insertCommentPortion(comment: Comment, pos: ParagraphContentPos, bStart?: boolean): ParagraphContentPos {
        const curContentPos = pos.shift();
        const content = this.content[curContentPos];

        const resPos = content.insertCommentPortion(comment, pos, bStart);
        resPos.unShift(curContentPos);
        return resPos;
    }

    public getCurContentPosInDoc(bSelection: boolean = false, bStart: boolean, depth?: boolean): ParagraphContentPos {
        let index: number;
        const bApplyToAll = this.bApplyToAll;
        if (bApplyToAll) {
            if (bStart) {
                index = 0;
            } else {
                index = this.content.length - 1;
            }
        } else if (bSelection) {
            const selection = this.selection;
            let start = selection.startPos;
            let end = selection.endPos;
            if (start > end) {
                const temp = start;
                start = end;
                end = temp;
            }
            if (bStart) {
                index = start;
            } else {
                index = end;
            }
        } else {
            index = this.curPos.contentPos;
            if (index >= this.content.length) {
                index = this.content.length - 1;
            }
        }

        const content = this.content[index];
        content.setApplyToAll(bApplyToAll);
        const pos = content.getCurContentPosInDoc(bSelection, bStart, depth);
        content.setApplyToAll(false);
        return pos;
    }

    public getTableIndex(): number {
        if ( this.isTableCellContent() && this.parent instanceof TableCell ) {
            return this.parent.row.table.index;
        }
        return -1;
    }

    public getTableRowIndex(): number {
        if ( this.isTableCellContent() && this.parent instanceof TableCell ) {
            return this.parent.row.index;
        }
        return -1;
    }

    public getTableRow(): TableRow {
        if ( this.isTableCellContent() && this.parent instanceof TableCell ) {
            return this.parent.row;
        }

        return null;
    }

    public getTableCellIndex(): number {
        if ( this.isTableCellContent() && this.parent instanceof TableCell ) {
            return this.parent.index;
        }
        return -1;
    }

    public getTableCell(): TableCell {
        if ( this.isTableCellContent() && this.parent instanceof TableCell ) {
            return this.parent;
        }

        return null;
    }

    public getLastVisibleLine(clientY: number, bEnd?: boolean): any {
        const contents = this.content;
        for (let index = contents.length - 1; index >= 0; index--) {
            const position = contents[index].getLastVisibleLine(clientY, bEnd);
            if (position) {
                return position;
            }
        }
    }

    /**
     * 元素开始位置所在页面是否有内容
     */
    public isContentOnFirstPage(): boolean {
        if ( 0 >= this.content.length ) {
            return false;
        }

        const element = this.content[0];
        return element.isContentOnFirstPage();
    }

    public startFromNewPage(): void {
        this.pages.length = 1;
        this.pages[0] = new DocumentPage();

        const element = this.content[0];
        element.startFromNewPage();
    }

    public isEmptyPage(curPage: number): boolean {
        if ( 0 > curPage || curPage >= this.pages.length ) {
            return true;
        }

        const startPos = this.pages[curPage].pos;
        const endPos = this.pages[curPage].endPos;

        if ( startPos > endPos ) {
            return true;
        } else if ( startPos < endPos ) {
            return false;
        }

        const elementPageIndex = this.getElementPageIndex(startPos, curPage);
        return this.content[startPos].isEmptyPage(elementPageIndex);
    }

    /**
     * 获取指定页面区域
     * @param pageIndex
     */
    public getPageLimits(pageIndex: number): ILimits {
      if (this.parent instanceof TableCell) {
        if ( true === this.parent.isCell() ) {
          const margins = this.parent.getMargins();

          const x = this.pages[pageIndex].x - margins.left.width;
          const xLimit = this.pages[pageIndex].xLimit + margins.right.width;
          const y = this.pages[pageIndex].y - margins.top.width;
          const yLimit = this.pages[pageIndex].yLimit + margins.bottom.width;

          return { x, y, xLimit, yLimit };
        } else {
          if ( !this.logicDocument ) {
              return { x: 0, y: 0, xLimit: 0, yLimit: 0 };
          }

          const pageAbs = this.getStartPage() + pageIndex;
          const index = ( undefined !== this.logicDocument.pages[pageAbs] ) ?
                          this.logicDocument.pages[pageAbs].pos : 0;
          const sectPr = this.logicDocument.sectionsInfo.getSectPr(index).sectProperty;

          const w = sectPr.getPageWidth();
          const h = sectPr.getPageHeight();

          return { x: 0, y: 0, xLimit: w, yLimit: h};
        }
      }
      return null;
    }

    /**
     * 获取当前页的首个元素的可编辑区域
     * @param pageIndex
     */
    public getPageFields(pageIndex: number): ILimits {
    //   if (this.parent instanceof TableCell) {
        if ( this.parent instanceof TableCell ) {
          if ( 0 <= pageIndex && pageIndex > this.pages.length ) {
              const x = this.pages[pageIndex].x;
              const xLimit = this.pages[pageIndex].xLimit;
              const y = this.pages[pageIndex].y;
              const yLimit = this.pages[pageIndex].yLimit;

              return { x, y, xLimit, yLimit };
          }  else {
              if ( !this.logicDocument ) {
                  return { x: 0, y: 0, xLimit: 0, yLimit: 0 };
              }

              const pageAbs = this.getStartPage() + pageIndex;
              const index = ( undefined !== this.logicDocument.pages[pageAbs] ) ?
                              this.logicDocument.pages[pageAbs].pos : 0;
              const sectPr = this.logicDocument.sectionsInfo.getSectPr(index).sectProperty;

              const w = sectPr.getPageWidth();
              const h = sectPr.getPageHeight();

              return { x: 0, y: 0, xLimit: w, yLimit: h };
          }
        } else {
          if ( !this.logicDocument ) {
              return { x: 0, y: 0, xLimit: 0, yLimit: 0 };
          }

          const pageAbs = this.getAbsolutePage(pageIndex); // this.getStartPage() + pageIndex;
          const index = ( undefined !== this.logicDocument.pages[pageAbs] ) ?
                          this.logicDocument.pages[pageAbs].pos : 0;
          const sectPr = this.logicDocument.sectionsInfo.getSectPr(index).sectProperty;

          const x = sectPr.pageMargins.paddingLeft;
          const xLimit = sectPr.pageSize.width - sectPr.pageMargins.paddingRight;
          const y = sectPr.pageMargins.paddingTop;
          const yLimit = sectPr.pageSize.height - sectPr.pageMargins.paddingBottom;

          return { x, y, xLimit, yLimit};
        }
    //   }
    //   return null;
    }

    public getPageContentStartPos(pageNum: number): ILimits {
      if ('getPageContentStartPos' in this.parent) {
        return this.parent.getPageContentStartPos(pageNum);
      }
      return null;
    }

    public getPageContentStartPos2( startPageIndex: number, elementPageIndex: number,
                                    elementIndex: number = 0): ILimits {
      if ('getPageContentStartPos' in this.parent) {
        return this.parent.getPageContentStartPos(startPageIndex + elementPageIndex);
      }
      return null;
    //   }
    //   return null;
    }

    /**
     * 单元格内容排版
     * @param pageIndex
     * @param bStart
     */
    public recalculatePage(pageIndex: number, bStart: boolean, bBreakStartElement: boolean = true,
                           bResetStartElement: boolean = false, bHidden?: boolean): RecalcResultType {
        if ( 0 === pageIndex && true === bStart ) {
            //
        }

        // const parent = this.getParent();
        // if ( parent instanceof TableCell && parent.property.formula ) {
        //     const formula = parent.property.formula;
        //     if ( null != formula.formulaType ) {
        //         const table = parent.row ? parent.row.table : null;

        //         if ( table ) {
        //             table.calcFormula(parent);
        //         }
        //     }
        // }

        let startIndex = 0;
        if ( 0 < pageIndex ) {
            startIndex = this.pages[pageIndex - 1].endPos;
            startIndex = (0 <= startIndex ? startIndex : 0);
        }

        if ( true === bStart ) {
            this.pages.length = pageIndex;
            this.pages[pageIndex] = new DocumentPage();
            this.pages[pageIndex].pos = startIndex;
        }

        const startPos: ILimits = this.getPageContentStartPos(pageIndex);

        if ( 0 === pageIndex ) {
            startPos.x = this.x;
            startPos.xLimit = this.xLimit;
            startPos.y = this.y; // can be related with footer.content.set()
            // console.log(this.getParent())
            // console.log(this.yLimit)
            startPos.yLimit = this.yLimit;
        }
        // console.log(startPos) // all good

        this.pages[pageIndex].updateLimits(startPos);

        const x = startPos.x;
        const xLimit = startPos.xLimit;
        let y = startPos.y;
        const yLimit = startPos.yLimit;

        let result = RecalcResultType.RecalcResult2End;
        const count = this.content.length;
        let index = startIndex;
        let bCurResetStartElement = false;
        let bCurBreakStartElement = false;

        for (; index < count; index++) {
            const element = this.content[index];
            element.setLogicDocument(this.logicDocument);
            element.setParent(this);

            let recalcResult = RecalcResultType.RecalResultNextElement;
            // if (element.isHidden()) {
            //     const elementPageIndex = this.getElementPageIndex(index, pageIndex);
            //     element.setDocumentIndex(index);

            //     const prevElement = this.content[index - 1];
            //     if ( element.isTable() && prevElement && prevElement.isTable() ) {
            //         y -= prevElement.getLastRowBorderSize();
            //     }
            //     element.reset(x, y, xLimit, yLimit, pageIndex);
            //     if (element.getType() !== DocumentContentType.Paragraph) {
            //         element.recalculatePage(elementPageIndex);
            //     }
            //     continue;
            // }

            // 表中表
            if ( element.isTable() && true !== element.isInline() ) {
                if ( true === this.recalcInfo.canRecalcObject() ) {
                    element.setDocumentIndex(index);
                    element.reset(x, y, xLimit, yLimit, pageIndex);
                    const tempRecalcResult = element.recalculatePage(0, bHidden);
                }
            } else {
                if ( ( 0 === index && 0 === pageIndex ) || index !== startIndex
                    || (index === startIndex && true === bResetStartElement) ) {
                    element.setDocumentIndex(index);

                    const prevElement = this.content[index - 1];
                    if ( element.isTable() && prevElement && prevElement.isTable() ) {
                        y -= prevElement.getLastRowBorderSize();
                    }
                    element.reset(x, y, xLimit, yLimit, pageIndex);
                }
                if ( element.isParagraph() && element.hasPageBreak() === true
                    && this.isRegionContent() ) {
                    if (!bStart && index === startIndex) {
                        bBreakStartElement = true;
                        bStart = true;
                    }

                    if ( false === bBreakStartElement ) {
                        if ((this.parent as Region).isHidden()) {
                            continue;
                        // result = RecalcResultType.RecalResultNextElement;
                        } else {
                            bCurBreakStartElement = true;
                            bCurResetStartElement = true;
                            this.pages[pageIndex].endPos = index;
                            y = this.yLimit;
                            result = RecalcResultType.RecalcResult2NextPage;
                        }
                        
                        break;
                    } else {
                        bBreakStartElement = false;
                    }
                }

                // 单元格： 最后一个空段落元素，且单元格内容元素大于1，
                if ( index === count - 1 && 0 < index && element.isParagraph()
                     && true === element.isEmpty() && this.content[index - 1].isTable()
                     && true === this.isTableCellContent()) {
                    recalcResult = RecalcResultType.RecalResultNextElement;
                    this.pages[pageIndex].endSectionParas.push(element);
                } else {
                    const elementPageIndex = this.getElementPageIndex(index, pageIndex);

                    if ( element instanceof Region ) {
                        recalcResult = element.recalculatePage(elementPageIndex, bStart);
                    } else {
                        recalcResult = element.recalculatePage(elementPageIndex, bHidden);
                    }
                }
            }

            if ( true ) {
                const elementPageIndex = this.getElementPageIndex(index, pageIndex);
                y = element.getPageBounds(elementPageIndex).bottom;
            }

            if ( recalcResult & RecalcResultType.RecalResultCurPage ) {
                if (true === this.isRegionContent()) {
                     return RecalcResultType.RecalcResult2CurPage;
                }

                return this.recalculatePage(pageIndex, false, true, false, bHidden);
            } else if (recalcResult & RecalcResultType.RecalResultNextElement) {
                // nothing todo
            } else if ( recalcResult & RecalcResultType.RecalResultNextPage ) {
                this.pages[pageIndex].endPos = index;
                result = RecalcResultType.RecalcResult2NextPage;
                break;
            }
        }

        if ( this.isRegionContent() ) {
            const region = this.parent as Region;
            if (!region.isHidden()) {
                region.bBreakStartElement = bCurBreakStartElement;
                region.bResetStartElement = bCurResetStartElement;
            }
        }

        // both header and footer may face content overflow -> bounds.bottom
        if (this.parent instanceof HeaderFooter) {
            // restrict header area domain so that it will not mess with logic document domain
            if (this.parent.isHeader()) {
                const restoredYLimit = this.yLimit / 10;
                if (y > restoredYLimit) {
                    y = restoredYLimit;
                }
            } else if (this.parent.isFooter()) {
                if (this.y !== 0) { // footer may come here from recalculate2, which needs to be filtered
                    const restoredYLimit = this.yLimit / 10;
                    // console.trace()
                    // console.log(circularParse(circularStringify(this)));
                    // console.log(restoredYLimit)

                    // console.log(y);
                    if (y > restoredYLimit) {
                        y = restoredYLimit;
                    }
                }
            }
        }

        this.pages[pageIndex].bounds.top = startPos.y;
        this.pages[pageIndex].bounds.left = x;
        this.pages[pageIndex].bounds.right = xLimit;
        this.pages[pageIndex].bounds.bottom = y;

        if ( count <= index ) {
            this.pages[pageIndex].endPos = count - 1;
        }

        return result;
    }

    public isRegionContent(): boolean {
        return 'getType' in this.parent && this.parent['getType']() === DocumentContentType.Region;
    }

    public getRegion(bInTable: boolean = true): Region {
        if (this.isRegionContent()) {
            if ((this.parent as Region).getRelevanceId()) {
                return;
            }
            return this.parent as any;
        }
        if (bInTable && this.parent instanceof TableCell) {
            const parent = this.parent.row.table.parent;
            if (parent && parent instanceof DocumentContent) {
                return parent.getRegion(bInTable);
            }
        }
        return;
    }

    public getCurParaPortion(): ParaPortion {
        const selection = this.selection;
        let elem: DocumentContentElementBase;
        if (selection.bUse) {
            let start = selection.startPos;
            let end = selection.endPos;
            if (start > end) {
                const temp = start;
                start = end;
                end = temp;
            }
            elem = this.content[start];
        } else {
            elem = this.content[this.curPos.contentPos];
        }

        return elem.getCurParaPortion();
    }

    public getTopElement(): DocumentContentElementBase {
        const parent = this.parent;
        if (parent instanceof TableCell) {
            return parent.row.table.getTopElement();
        } else if (parent instanceof Region) {
            return parent.getTopElement();
        }
        return;
    }

    public getParentPos(pos: ParagraphContentPos): void {
        const parent = this.parent;
        if (parent instanceof TableCell) {
            pos.splice(0, 0, [parent.row.index, parent.index]);
            parent.row.table.getParentPos(pos);
        } else if (parent instanceof Region) {
            parent.getParentPos(pos);
        }
    }

    public getParentIndexs(pos: ParagraphContentPos): void {
        const actPos = new ParagraphContentPos();
        actPos.clear();
        this.getElementIndex(actPos);
        const datas = actPos.data.reverse();
        datas.forEach((data) => {
            pos.add(data);
        });
    }

    public getPageBounds(curPage: number, height?: number): DocumentFrameBounds {
        if ( 0 >= this.pages.length ) { // TODO: may be wrong
            return new DocumentFrameBounds(0, 0, 0, 0);
        }

        curPage = ( 0 > curPage ) ? 0 : ( ( this.pages.length - 1 < curPage ) ? this.pages.length - 1 : curPage );

        const bounds = this.pages[curPage].bounds;
        // let pageAbs = this.get

        return bounds;
    }

    /**
     * 当前元素在整个文档中的页面
     */
    public getAbsolutePage(curPage: number = 0): number {
        const topParent = this.getTopDocument();
        if ( this.isTableCellContent() && topParent && topParent instanceof DocumentContent ) {
            const parent = topParent.parent as HeaderFooter;

            if ( parent ) {
                return topParent.getAbsolutePageIndex(curPage);
            }
        }

        return this.getAbsolutePageIndex(curPage);
    }

    /**
     * 当前元素在整个文档中的开始页面
     */
    public getAbsoluteStartPage(): number {
        return this.getAbsolutePage(0);
    }

    /**
     * 获取区域的y坐标范围
     */
    public getRegionYRange(): number[] {
        const result = [-1, -1];
        const regionContent = this.content;
        // console.log(regionContent)

        // region can have 2 max recursive depths

        const firstItemInRegionContent = regionContent[0];
        let startLines = firstItemInRegionContent.getLines();
        if (firstItemInRegionContent instanceof Region) {
            // first item must be para since 2 max depth
            startLines = firstItemInRegionContent.getContent()[0]
                .getLines();
        }
        if (startLines != null) {
            if (startLines[0] != null) {
                result[0] = startLines[0].top;
            }
        }

        const lastItemInRegionContent = regionContent[regionContent.length - 1];
        let endLines = lastItemInRegionContent.getLines();
        // console.log(endLines);
        if (lastItemInRegionContent instanceof Region) {
            // last item must be para since 2 max depth
            const lastItemContent = lastItemInRegionContent.getContent();
            endLines = lastItemContent[lastItemContent.length - 1].getLines();
        }
        if (endLines != null) {
            if (endLines[endLines.length - 1] != null) {
                result[1] = endLines[endLines.length - 1].bottom;
            }
        }

        return result;
    }

    /**
     * 当前元素的开始页面
     */
    public getRelativeStartPage(): number {
        return this.startPage;
    }

    public setApplyToAll(bApply: boolean): void {
        this.bApplyToAll = bApply;
    }

    public getApplyToAll(): boolean {
        return this.bApplyToAll;
    }

    public isSelectionUse(): boolean {
        return this.selection.bUse;
    }

    // public isPopWinNewControl(): boolean {
    //     const para = this.getCurrentParagraph();
    //     const parent = para ? para.getParent() : null;
    //     if ( para && parent ) {
    //         if ( parent.isTableCellContent() ) {
    //             const table = this.getCurrentTable();

    //             if ( table && false === table.isPopWinNewControl() ) {
    //                 return false;
    //             }
    //         }

    //         const region = this.getCurrentRegion();
    //         if ( region && false === region.isPopWinNewControl()) {
    //             return false;
    //         }
    //     }

    //     return true;
    // }

    public selectTopPos(): void {
        const parent = this.parent as any;
        if (this.isRegionContent()) {
            parent.selectTopPos();
        } else if (this.isTableCellContent()) {
            const table: TableBase = parent.row.table;
            table.selectTopPos();
            const selection = table.selection;
            selection.bUse = true;
            selection.bStart = false;
            const rowIndex = parent.row.index;
            const cellIndex = parent.index;
            selection.startPos.pos = {rowIndex, cellIndex};
            selection.endPos.pos = {rowIndex, cellIndex};
            table.curCell = parent;
            selection.type = TableSelectionType.Text;
        } else {
            // const selection = parent.selection;
            // selection.bUse = true;
            // parent.curPos.contentPos = selection.endPos = selection.startPos = this.index;
        }
    }

    public setContentCurPos(): void {
        const parent = this.parent as any;
        if (this.isRegionContent()) {
            parent.setContentCurPos();
        } else if (this.isTableCellContent()) {
            const table: TableBase = parent.row.table;
            table.setContentCurPos();
            const selection = table.selection;
            const rowIndex = parent.row.index;
            const cellIndex = parent.index;
            selection.startPos.pos = {rowIndex, cellIndex};
            selection.endPos.pos = {rowIndex, cellIndex};
        } else {
            // const selection = parent.selection;
            // selection.bUse = true;
            // parent.curPos.contentPos = selection.endPos = selection.startPos = this.index;
        }
    }

    public canInput(): boolean {
        if ( !this.selection.bUse ) {
            const para = this.getCurrentParagraph();
            const parent = para ? para.getParent() : null;
            if ( para && parent ) {
                if ( parent.isTableCellContent() ) {
                    const table = this.getCurrentTable();

                    if ( false === table.canInput() ) {
                        return false;
                    }
                }

                const region = this.getCurrentRegion();
                if ( region && false === region.canInput()) {
                    // alert('区域设置了编辑保护，无法输入');
                    gEvent.setEvent(this.logicDocument.id, gEventName.MessageEvent, this, MessageType.UnEdited);
                    // message.error('区域设置了编辑保护，无法输入');
                    return false;
                }
            }
        } else {
            return this.canDelete();
        }

        return true;
    }

    public isReadonly(): boolean {
        const parent = this.parent;
        if (this.isTableCellContent()) {
            return (parent as TableCell).isNewControlReadOnly();
        } else if (this.isRegionContent()) {
            return (parent as Region).isReadOnly();
        }

        return false;
    }

    public canDelete(): boolean {
        const region = this.isRegionContent() ? this.parent as Region : null;
        const bEditable = region ? region.isEditProtect() : false;
        const bAdminMode = region ? region.getDocumentParent()
                                                .isAdminMode() : false;

        if ( this.bApplyToAll ) {
            const bSelectAll = true;
            const startPos = 0; // 段落开始
            const endPos = this.content.length - 1; // 段落结束

            let flag = true;
            for (let index = startPos; index <= endPos; index++) {
                const item = this.content[index];

                item.setApplyToAll(true);
                if ( item && false === item.canDelete() ) {
                    item.setApplyToAll(false);
                    return false;
                }

                flag = flag && item.isReverseEdit();
                if (flag && (bEditable && !bAdminMode) && startPos !== endPos && item.isSelectedAll()) {
                    item.setApplyToAll(false);
                    return false;
                }
                item.setApplyToAll(false);
            }

            if ( flag === false && region) {
                if (bSelectAll) {
                    const selection = region.parent.selection;
                    if (selection.startPos !== selection.endPos) {
                        return true;
                    }
                }

                if ( bAdminMode ) {
                    return true;
                } else if (bEditable === true) {
                    // alert('区域设置了删除保护，无法删除');
                    gEvent.setEvent(this.logicDocument.id, gEventName.MessageEvent, this, MessageType.UnDelete);
                    // message.error('区域设置了删除保护，无法删除');
                }
                return !bEditable;
            }
        } else if ( !this.selection.bUse ) {
            // const bRegionContent = this.isRegionContent();
            if (region) {
                // const region = this.parent as Region;
                if (this.isNotDeleteRegionTitle(region) === false) {
                    // 区域标题不能删除
                    // alert('当前光标在元素标题中，无法删除');
                    gEvent.setEvent(this.logicDocument.id, gEventName.MessageEvent, this, MessageType.DeleteTitle);
                    // message.error('当前光标在元素标题中，无法删除');
                    return false;
                }
                // else if (this.isReadOnlyToRegion(region)) {
                //     alert('区域设置了编辑保护，无法删除');
                //     return false;
                // }
            }
            const para = this.getCurrentParagraph();
            const parent = para ? para.getParent() : null;
            if ( para && parent ) {
                if ( parent.isTableCellContent() ) {
                    const table = this.getCurrentTable();

                    if ( table && false === table.canDelete() ) {
                        return false;
                    }
                }

                const curRegion = this.getCurrentRegion();
                if ( curRegion && false === curRegion.canDelete()) {
                    // alert('区域设置了删除保护，无法删除');
                    gEvent.setEvent(this.logicDocument.id, gEventName.MessageEvent, this, MessageType.UnDelete);
                    // message.error('区域设置了删除保护，无法删除');
                    return false;
                } else if (para.getRegion() && para.getRegion().isReadOnly()) {
                    // alert('区域设置了删除保护，无法删除');
                    gEvent.setEvent(this.logicDocument.id, gEventName.MessageEvent, this, MessageType.UnDelete);
                    // message.error('区域设置了删除保护，无法删除');
                    return false;
                }
            }
        } else {
            // const bRegion = this.isRegionContent();
            // let region: Region;
            let bSelectAll: boolean;
            if (region) {
                bSelectAll = this.isSelectedAll();
                // region = this.parent as Region;
                // 包含标题
                if (!bSelectAll && this.isNotContainRegionTitle(region) === false) {
                    // alert('选中了区域的标题，无法删除');
                    // message.error('选中了区域的标题，无法删除');
                    gEvent.setEvent(this.logicDocument.id, gEventName.MessageEvent, this, MessageType.DeleteTitle);
                    return false;
                }
            }
            let startPos = this.selection.startPos; // 段落开始
            let endPos = this.selection.endPos; // 段落结束

            if ( startPos > endPos ) {
                const temp = startPos;
                startPos = endPos;
                endPos = temp;
            }
            let flag = true;
            for (let index = startPos; index <= endPos; index++) {
                const item = this.content[index];

                if ( item && false === item.canDelete() ) {
                    return false;
                }

                flag = flag && item.isReverseEdit();
                if (flag && (bEditable && !bAdminMode) && startPos !== endPos && item.isSelectedAll()) {
                    return false;
                }
            }

            if ( flag === false && region) {
                if (bSelectAll) {
                    const selection = region.parent.selection;
                    if (selection.startPos !== selection.endPos) {
                        return true;
                    }
                }

                if ( bAdminMode ) {
                    return true;
                } else if (bEditable === true) {
                    // alert('区域设置了删除保护，无法删除');
                    // message.error('区域设置了删除保护，无法删除');
                    gEvent.setEvent(this.logicDocument.id, gEventName.MessageEvent, this, MessageType.UnDelete);
                }
                return !bEditable;
            }
        }

        return true;
    }

    public canInsertNewControl(): boolean {
        return true;
    }

    // isReadOnlyToRegion(region: Region): boolean {
    //     if (!region.isEditProtect()) {
    //         return false;
    //     }
    //     const selection = this.selection;
    //     const startPos = selection.startPos; // 段落开始
    //     const endPos = selection.endPos; // 段落结束

    //     if ( startPos !== endPos ) {
    //         return true;
    //     }

    //     const item = this.content[startPos] as Region;
    //     if ( item.getType() === DocumentContentType.Region) {
    //         if (item.isReverseEdit()) {
    //             return false;
    //         }
    //     }

    //     return true;
    // }

    public getCurrentPageByPos(pos: ParagraphContentPos): number {
        const content = this.content[pos.shift()];
        return content.getCurrentPageByPos(pos);
    }

    public addDocContentChild(options: any, cleanMode: CleanModeType): void {
        // this.parent = options.parent;
        options = {...options};
        const {startName, endName} = options;
        // options.parent = this;
        this.logicDocument = options.doc;
        this.content.forEach((item) => {
            item.addDocContentChild(options, cleanMode);
            const name = item.getName();
            if (startName && name === startName) {
                options.eles.startEle = item;
            } else if (endName && name === endName) {
                options.eles.endEle = item;
            }
        });
    }

    public setParentPos(pos: ParagraphContentPos): void {
        if (this.isTableCellContent() || this.isRegionContent()) {
            (this.parent as any).setParentPos(pos);
        }
    }

    public parentHidden(bSource: boolean = false): boolean {
        const parent: any = this.parent;
        if (this.isTableCellContent()) {
            const table = parent.row.table;
            return table.parentHidden();
        }
        if (this.isRegionContent()) {
            return parent.parentHidden(bSource);
        }
        return false;
    }

    public setParent(parent: any): void {
        this.parent = parent;
    }

    /**
     * 移动光标到段落开始位置
     * @param bAddToSelect
     * @param bSelectFromStart
     */
    public moveCursorToStartPos( bAddToSelect: boolean = false, bSelectFromStart: boolean = false ): void {
        if ( true === bAddToSelect ) {
            // ;
        } else {
            this.removeSelection();

            this.selection.bUse = false;
            this.selection.bStart = false;
            this.selection.startPos = 0;
            this.selection.endPos = 0;
            this.selection.flag = SelectionFlagType.Common;
            this.curPos.contentPos = 0;

            this.setDocPosType(DocCurPosType.Content);

            this.content[0].moveCursorToStartPos();
        }
    }

    public moveCursorToEndPos( bAddToSelect: boolean = false, bSelectFromStart: boolean = false ): void {
        if ( true === bAddToSelect ) {
            // ;
        } else {
            this.removeSelection();
            const contents = this.content;
            const endIndex = contents.length - 1;
            this.selection.bUse = false;
            this.selection.bStart = false;
            this.selection.startPos = endIndex;
            this.selection.endPos = endIndex;
            this.selection.flag = SelectionFlagType.Common;
            this.curPos.contentPos = endIndex;

            this.setDocPosType(DocCurPosType.Content);

            contents[endIndex].moveCursorToEndPos(bAddToSelect, bSelectFromStart);
        }
    }

    public getRegionManager(): any {
        return this.logicDocument.getRegionManager();
    }

    public getCurrentPageLastLine(pageIndex: number, result?: any): any {
        const curPage = pageIndex - this.getParentSumPageIndex();
        const page = this.pages[curPage];
        if (!page) {
            return;
        }

        return this.content[page.endPos].getCurrentPageLastLine(pageIndex, result);
    }

    public getParaLines(pageIndex: number, result?: {paras: any[], tables: any[]}): any {
        const curPage = pageIndex - this.getParentSumPageIndex();
        const page = this.pages[curPage];
        if (!page) {
            return;
        }
        let res: any[];
        if (result === undefined) {
            res = []; // 表格单元格使用
            result = {paras: res, tables: []};
        }
        const contents = this.content;
        for (let index = page.pos, length = page.endPos; index <= length; index++) {
            contents[index].getParaLines(pageIndex, result);
        }
        return res;
    }

    public isRegionValidDelete(): boolean {
        const selection = this.selection;
        if (selection.bUse) {
            let flag = this.isSelectedAll();
            flag = flag || this.isNotContainRegionTitle(this.parent as any);
            if (flag === false) {
                gEvent.setEvent(this.logicDocument.id, gEventName.MessageEvent, this, MessageType.DeleteTitle);
                // message.error('所选内容包含标题，无法删除！');
                return false;
            }
            let start = selection.startPos;
            let end = selection.endPos;
            if (start > end) {
                const temp = start;
                start = end;
                end = temp;
            }
            const contents = this.content;
            const bSamePos = start === end;
            for (let index = start; index <= end; index++) {
                const content = contents[index] as Region;
                if (content.getType() === DocumentContentType.Region) {
                    flag = content.isRegionValidDelete(bSamePos);
                    // flag = flag && this.isNotContainRegionTitle(content as Region);
                    if (flag === false) {
                        return false;
                    }
                }
            }
        } else {
            let flag = this.isNotDeleteRegionTitle(this.parent as any);
            if (flag === false) {
                // alert('当前光标在元素标题中，无法删除');
                // message.error('当前光标在元素标题中，无法删除');
                gEvent.setEvent(this.logicDocument.id, gEventName.MessageEvent, this, MessageType.DeleteTitle);
                return false;
            }
            const content = this.content[this.curPos.contentPos] as Region;
            if (content.getType() === DocumentContentType.Region) {
                flag = content.isRegionValidDelete(true);
                // flag = flag && this.isNotDeleteRegionTitle(content as Region);
                if (flag === false) {
                    return false;
                }
            }
        }

        return true;
    }

    public isTimeCell(): boolean {
        if (this.isTableCellContent()) {
            return (this.parent as any).isTimeCell();
        }

        return false;
    }

    public selectCellContent(): boolean {
        const para = this.content[0];
        if (!para.isParagraph()) {
            return false;
        }
        return (para as any).selectCellContent();
    }

    public initOpenDefalutCellContent(splitTag: string, cellText: string): boolean {
        if (this.content.length !== 1) {
            return false;
        }
        return (this.content[0] as Paragraph).initOpenDefalutContent(splitTag, cellText);
    }

    /**
     * 获取所有文本
     * @param bApplyToAll 是否获取所有文本
     * @param needPara 是否需要段落内容
     */
    public getSelectText(bApplyToAll: boolean, needPara: boolean = false): string {
        let startPos: number;
        let endPos: number;
        if (bApplyToAll === true) {
            startPos = 0;
            endPos = this.content.length;
        } else {
            const selection = this.selection;
            startPos = selection.startPos;
            endPos = selection.endPos;
            if (startPos > endPos) {
                const pos = startPos;
                startPos = endPos;
                endPos = pos;
            }
            endPos += 1;
        }

        const contents = this.content.slice(startPos, endPos);
        let text: string = '';
        contents.forEach((content) => {
            text += content.getSelectText(bApplyToAll);
            needPara && content instanceof Paragraph && (text += '\n');
        });

        return text;
    }

     /**
     * 获取所有文本
     * @param bApplyToAll 是否获取所有文本
     * @param needPara 是否需要段落内容
     * 为AI 设计 里面元素边框显示出来
     */
     public getSelectTextAI(bApplyToAll: boolean, needPara: boolean = false): string {
        let startPos: number;
        let endPos: number;
        if (bApplyToAll === true) {
            startPos = 0;
            endPos = this.content.length;
        } else {
            const selection = this.selection;
            startPos = selection.startPos;
            endPos = selection.endPos;
            if (startPos > endPos) {
                const pos = startPos;
                startPos = endPos;
                endPos = pos;
            }
            endPos += 1;
        }

        const contents = this.content.slice(startPos, endPos);
        let text: string = '';
        contents.forEach((content) => {
            text += content.getSelectTextAI(bApplyToAll);
            needPara && content instanceof Paragraph && (text += '\n');
        });

        return text;
    }

    /**
     * 获取选择文本，去除隐藏内容
     * @param bSelectAll
     * @returns
     */
    public getSelectTextNoHidden(bApplyToAll: boolean): string {
        let startPos: number;
        let endPos: number;
        if (bApplyToAll === true) {
            startPos = 0;
            endPos = this.content.length;
        } else {
            const selection = this.selection;
            startPos = selection.startPos;
            endPos = selection.endPos;
            if (startPos > endPos) {
                const pos = startPos;
                startPos = endPos;
                endPos = pos;
            }
            endPos += 1;
        }

        const contents = this.content.slice(startPos, endPos);
        let text: string = '';
        contents.forEach((content) => {
            if (!content.isHidden()) {
                text += content.getSelectTextNoHidden(bApplyToAll);
            }
        });

        return text;
    }

    public getSelectedParaPros(paraPro: IParaProperty, bUse: boolean): boolean {
        const contents = this.content;
        const selection = this.selection;
        let startParaPos: number;
        let endParaPos: number;
        if (bUse === true) {
            startParaPos = 0;
            endParaPos = contents.length - 1;
        } else {
            startParaPos = selection.startPos;
            endParaPos = selection.endPos;

            if ( selection.startPos > selection.endPos ) {
                startParaPos = selection.endPos;
                endParaPos = selection.startPos;
            }
        }

        const pros: ParaProperty[] = [];
        for (let i = startParaPos; i <= endParaPos; i++) {
            const item = contents[i];
            if (item.getSelectedParaPros(paraPro)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 获取单元格选中区域的内容
     */
    public getSelectedContent(base?: DocumentContentElementBase, names?: string[],
                              bKeepHalfStructBorder?: boolean, option?: any): DocumentContentElementBase[] {
        const selection = this.selection;
        let starParaPos: number = selection.startPos;
        let endParaPos: number = selection.endPos;
        const contents = this.content;
        // if (this.bApplyToAll === true) {
        //     starParaPos = selection.startPos;
        //     endParaPos = selection.endPos;
        // } else {
        //     starParaPos = selection.startPos;
        //     endParaPos = selection.endPos;
        // }

        // let dir = this.getSelectionDirection();
        // const paras = [];
        if (starParaPos > endParaPos) {
            const pos = starParaPos;
            starParaPos = endParaPos;
            endParaPos = pos;
        }

        if (!base) {
            base = this.parent as any;
        }

        let res: DocumentContentElementBase[] = [];
        const parent = base['content'];
        for (let i = starParaPos; i <= endParaPos; i++) {
            let curBase: any;
            const curPar = contents[i];
            const type = curPar.getType();
            if (type === DocumentContentType.Region) {
                if (curPar.isSelectedAll()) {
                    res.push(curPar.copy(parent, option));
                } else {
                    curBase = new Region(parent, this.logicDocument, {newControlName: curPar.getName()});
                    const items = curPar.getSelectedContent(curBase, names, bKeepHalfStructBorder, option);
                    if (items && items.length > 0) {
                        res = res.concat(items);
                    }
                }
            } else if (type === DocumentContentType.Table) {
                curBase = new Table(parent, this.logicDocument, 0, 0, []);
                const cellParas = curPar.getSelectedContent(curBase, names, bKeepHalfStructBorder, option);
                if (cellParas && cellParas.length > 0) {
                    res = res.concat(cellParas);
                } else {
                    res.push(curBase);
                }
            } else {
                curBase = new Paragraph(parent, this.logicDocument);
                curPar.getSelectedContent(curBase, names, bKeepHalfStructBorder, option);
                if (curBase.content.length > 1) {
                    res.push(curBase);
                }
            }
        }

        if (res.length === 0) {
            res.push(new Paragraph(this, this.logicDocument));
        }

        // paras.forEach((item, index) => {
        //     parent.content.push(item);
        // });

        return res;
    }

    public getSelectedCellContent(names: string[], option?: any): DocumentContentElementBase[] {
        const selection = this.selection;
        let starParaPos: number = selection.startPos;
        let endParaPos: number = selection.endPos;
        const contents = this.content;
        // if (this.bApplyToAll === true) {
        //     starParaPos = selection.startPos;
        //     endParaPos = selection.endPos;
        // } else {
        //     starParaPos = selection.startPos;
        //     endParaPos = selection.endPos;
        // }

        // let dir = this.getSelectionDirection();
        const paras = [];
        if (starParaPos > endParaPos) {
            const pos = starParaPos;
            starParaPos = endParaPos;
            endParaPos = pos;
        }
        for (let i = starParaPos; i <= endParaPos; i++) {
            let par: Paragraph;
            const curPar = contents[i];
            if (curPar.getType() === DocumentContentType.Table) {
                // par= new Table(this, this, curPar.getRow());
            } else {
                par = new Paragraph(this, this.logicDocument);
            }
            curPar.getSelectedContent(par, names, undefined, option);
            par.paraProperty = curPar.getParagraphProperty()
                .copy();
            paras.push(par);
        }
        return paras;
    }

    /**
     * 根据属性集合进行数据收集
     */
    public getSelectedTextProperty(bUse: boolean, textPros: ITextProperty): boolean {
        const contents = this.content;
        let startPos: number;
        let endPos: number;
        if (bUse === true) {
            startPos = 0;
            endPos = contents.length - 1;
        } else {
            const selection = this.selection;
            startPos = selection.startPos;
            endPos = selection.endPos;
            if (startPos > endPos) {
                const pos = startPos;
                startPos = endPos;
                endPos = pos;
            }
        }

        for (let i = startPos; i <= endPos; i++) {
            const para = contents[i];
            para.setApplyToAll(bUse);
            const flag = para.getSelectedTextProperty(textPros);
            para.setApplyToAll(false);
            if (flag) {
                return true;
            }
        }

        return false;
    }

    public getTextLength(bSkipSomeCh: boolean = false): number {
        let length = 0;
        const elements = this.content;
        for (let index = 0, count = elements.length; index < count; index++) {
            const element = elements[index];
            if ( element ) {
                length += element.getTextLength(bSkipSomeCh);
            }
        }

        return length;
    }

    //
    public isEmptyCell(bSkipStruct: boolean = false): boolean {
        const contents = this.content;
        if (contents.length !== 1) {
            return false;
        }

        const para = contents[0] as Paragraph;
        if (!para.isParagraph()) {
            return false;
        }

        if (bSkipStruct) {
            return para.isEmpty();
        }

        return para.isEmptyPara();
    }

    /**
     * 移动光标到指定（x，y）处
     * @param curPage
     * @param pointX
     * @param pointY
     * @param bAddToSelect
     * @param bDontChangeRealPos
     */
    public moveCursorToXY(curPage: number, pointX: number, pointY: number,
                          bAddToSelect: boolean = false, bDontChangeRealPos: boolean = true): void {
        if ( 0 >= this.pages.length) {
            return;
        }

        if ( curPage ) {
            if ( curPage < 0 ) {
                curPage = 0;
                pointY = 0;
            } else if ( curPage >= this.pages.length ) {
                curPage = this.pages.length - 1;
                pointY = this.pages[curPage].yLimit;
            }

            this.curPage = curPage;
        }

        if ( true === this.selection.bUse ) {
            // 继续选中
            if ( true === bAddToSelect ) {
                this.setSelectionEnd(pointX, pointY, curPage, null);
                // this.updateSelectionState();
                return ;
            } else {
                this.removeSelection();
            }
        } else {
            if ( true === bAddToSelect ) {
                //
                this.startSelectionByCurPos();
                const mouseEvent = new MouseEventHandler();
                mouseEvent.type = MouseEventType.MouseButtonUp;
                this.setSelectionEnd(pointX, pointY, curPage, mouseEvent);
                // this.updateSelectionState();
            }
        }

        const contentPos = this.getContentPosByXY(curPage, pointX, pointY);
        if ( contentPos === undefined ) {
            return;
        }
        this.curPos.contentPos = contentPos;

        const elementPageIndex = this.getElementPageIndexByXY(contentPos, pointX, pointY, curPage);
        this.content[contentPos].moveCursorToXY(elementPageIndex, pointX, pointY, false, false);
    }

    /**
     * 键盘光标左移
     * @param bShiftLey
     */
    public moveCursorLeft( bShiftLey: boolean  = false ): boolean {
            if ( DocCurPosType.DrawingObjects === this.curPos.type ) {
                // ;
            } else {
                if ( 0 > this.curPos.contentPos ) {
                    return false;
                }

                let result = true;

                if ( true === this.selection.bUse ) {
                    if ( true === bShiftLey ) {
                        if ( false === this.content[this.selection.endPos].moveCursorLeft(bShiftLey) ) {
                            // 当前段落不是第一个段落
                            if ( 0 !== this.selection.endPos ) {
                                this.selection.endPos--;
                                this.curPos.contentPos = this.selection.endPos;

                                this.content[this.selection.endPos].moveCursorLeftWithSelectionFromEnd();
                            } else {
                                result = false;
                            }
                        }

                        // 当前没有内容选择
                        if ( this.selection.startPos === this.selection.endPos
                            && true === this.content[this.curPos.contentPos].isSelectionEmpty() ) {
                            this.removeSelection();
                        }
                    } else {
                        let startPos = this.selection.startPos;
                        if ( startPos > this.selection.endPos ) {
                            startPos = this.selection.endPos;
                        }

                        this.curPos.contentPos = startPos;

                        this.content[this.curPos.contentPos].moveCursorLeft(bShiftLey);

                        this.removeSelection();
                    }
                } else {
                    // shift: selection
                    if ( true === bShiftLey) {
                        this.selection.bUse = true;
                        this.selection.startPos = this.curPos.contentPos;
                        this.selection.endPos = this.curPos.contentPos;

                        // 光标在段尾，进入下一个段落
                        if ( false === this.content[this.curPos.contentPos].moveCursorLeft(bShiftLey) ) {
                            // 当前段落不是最后一个段落
                            if ( 0 !== this.curPos.contentPos ) {
                                this.curPos.contentPos--;
                                this.selection.endPos = this.curPos.contentPos;

                                this.content[this.curPos.contentPos].moveCursorLeftWithSelectionFromEnd();
                            } else {
                                result = false;
                            }
                        }

                        // 当前没有内容选择
                        if ( this.selection.startPos === this.selection.endPos
                            && false === this.content[this.curPos.contentPos].isSelectionUse() ) {
                            this.selection.bUse = false;
                            this.curPos.contentPos = this.selection.endPos;
                        }
                    } else {
                        // 光标在段尾，进入下一个段落
                        if ( false === this.content[this.curPos.contentPos].moveCursorLeft(bShiftLey) ) {
                            // 当前段落不是最后一个段落
                            if ( 0 !== this.curPos.contentPos ) {
                                this.curPos.contentPos--;
                                this.selection.endPos = this.curPos.contentPos;

                                this.content[this.curPos.contentPos].moveCursorToEndPos(false);
                            } else {
                                result = false;
                            }
                        }
                    }
                }

                return result;
                // this.updateCursorXY();
                // this.curPos.x = this.curPos.realX;
                // this.curPos.y = this.curPos.realY;
            }
    }

    /**
     * 键盘光标右移
     * @param bShiftLey
     */
    public moveCursorRight( bShiftLey: boolean  = false ): boolean {
        if ( 0 > this.curPos.contentPos ) {
            return false;
        }

        let result = true;

        if ( true === this.selection.bUse ) {
            if ( true === bShiftLey ) {
                // 光标在段尾，进入下一个段落
                if ( false === this.content[this.selection.endPos].moveCursorRight(bShiftLey) ) {
                    // 当前段落不是最后一个段落
                    if ( this.content.length - 1 !== this.selection.endPos ) {
                        this.selection.endPos++;
                        this.curPos.contentPos = this.selection.endPos;

                        this.content[this.selection.endPos].moveCursorRightWithSelectionFromStart();
                    } else {
                        result = false;
                    }
                }

                // 当前没有内容选择
                if ( this.selection.startPos === this.selection.endPos
                    && true === this.content[this.curPos.contentPos].isSelectionEmpty() ) {
                    this.removeSelection();
                }

            } else {
                let endPos = this.selection.endPos;
                if ( endPos < this.selection.startPos ) {
                    endPos = this.selection.startPos;
                }

                this.curPos.contentPos = endPos;

                this.content[this.curPos.contentPos].moveCursorRight(bShiftLey);

                this.removeSelection();
            }
        } else {
            // shift: selection
            if ( true === bShiftLey) {
                this.selection.bUse = true;
                this.selection.startPos = this.curPos.contentPos;
                this.selection.endPos = this.curPos.contentPos;

                // 光标在段尾，进入下一个段落
                if ( false === this.content[this.curPos.contentPos].moveCursorRight(bShiftLey) ) {
                    // 当前段落不是最后一个段落
                if ( this.content.length - 1 !== this.curPos.contentPos ) {
                    this.curPos.contentPos++;
                    this.selection.endPos = this.curPos.contentPos;

                    this.content[this.curPos.contentPos].moveCursorRightWithSelectionFromStart();
                            } else {
                                result = false;
                            }
                }

                // 当前没有内容选择
                if ( this.selection.startPos === this.selection.endPos
                    && false === this.content[this.curPos.contentPos].isSelectionUse() ) {
                    this.selection.bUse = false;
                    this.curPos.contentPos = this.selection.endPos;
                }
            } else {
                // 光标在段尾，进入下一个段落
                if ( false === this.content[this.curPos.contentPos].moveCursorRight(bShiftLey) ) {
                    // 当前段落不是最后一个段落
                    if ( this.content.length - 1 !== this.curPos.contentPos ) {
                        this.curPos.contentPos++;
                        this.selection.endPos = this.curPos.contentPos;

                        this.content[this.curPos.contentPos].moveCursorToStartPos();
                    } else {
                        result = false;
                    }
                }
            }
        }

        return result;

        // this.updateCursorXY();
        // this.curPos.x = this.curPos.realX;
        // this.curPos.y = this.curPos.realY;
    }

    public getPosition(type: any): {x: number, y: number, pageNum: number} {
        const selection = this.selection;
        let element: DocumentContentElementBase;
        if (selection.bUse) {
            let start = selection.startPos;
            let end = selection.endPos;
            if (start > end) {
                const temp = start;
                start = end;
                end = temp;
            }
            element = this.content[start];
        } else {
            element = this.content[this.curPos.contentPos];
        }
        return element.getPosition(type);
    }

    /**
     * 键盘光标上移
     * @param bShiftLey
     */
    public moveCursorUp( bShiftLey: boolean  = false ): boolean {
        if ( 0 > this.curPos.contentPos ) {
            return false;
        }

        let result = true;

        if ( true === this.selection.bUse ) {
            if ( true === bShiftLey) {
                // 光标进入上一个段落
                const pos = this.selection.endPos;
                const direction = this.selection.startPos === this.selection.endPos ?
                                    0 : this.selection.startPos < this.selection.endPos ? 1 : -1;

                if ( false === this.content[pos].moveCursorUp(bShiftLey) ) {
                    // 当前段落不是第一个段落
                    if ( 0 !== pos ) {
                        // 保存光标在当前段落的位置
                        // this.updateCursorXY();
                        this.curPos.x = this.curPos.realX;
                        this.curPos.y = this.curPos.realY;

                        if ( 1 === direction ) {
                            this.content[pos].removeSelection();
                        }

                        this.selection.endPos--;

                        this.content[this.selection.endPos].moveCursorUpToLastRow(
                                        this.curPos.x, this.curPos.realY, bShiftLey);
                    } else {
                        result = false;
                    }

                    // 检查选择是否未重置（即，没有填充任何内容）
                    if ( this.selection.startPos === this.selection.endPos
                        && false === this.content[this.selection.startPos].isSelectionUse() ) {
                        this.selection.bUse = false;
                    }

                    this.curPos.contentPos = this.selection.endPos;
                }
            } else {
                this.selection.bUse = false;
                this.curPos.contentPos = this.selection.startPos < this.selection.endPos ?
                                            this.selection.startPos : this.selection.endPos;

                // 光标进入上一个段落
                if ( false === this.content[this.curPos.contentPos].moveCursorUp(bShiftLey) ) {
                    // 当前段落不是第一个段落
                    if ( 0 !== this.curPos.contentPos ) {
                        this.updateCursorXY();
                        this.curPos.contentPos--;

                        this.content[this.curPos.contentPos].moveCursorUpToLastRow(
                                            this.curPos.x, this.curPos.realY, bShiftLey);
                    } else {
                        result = false;
                    }
                }
                this.removeSelection();
            }
        } else {
            // shift: selection
            if ( true === bShiftLey) {
                this.selection.bUse = true;
                this.selection.startPos = this.curPos.contentPos;
                this.selection.endPos = this.curPos.contentPos;

                // 光标进入上一个段落
                if ( false === this.content[this.curPos.contentPos].moveCursorUp(bShiftLey) ) {
                    // 当前段落不是第一个段落
                    if ( 0 !== this.curPos.contentPos ) {
                        // 保存光标在当前段落的位置
                        this.updateCursorXY();
                        this.curPos.contentPos--;
                        this.selection.endPos = this.curPos.contentPos;
                        this.curPos.x = this.curPos.realX;
                        this.content[this.curPos.contentPos].moveCursorUpToLastRow(
                                        this.curPos.x, this.curPos.realY, bShiftLey);
                    } else {
                        result = false;
                    }
                }

                // 当前没有内容选择
                if ( this.selection.startPos === this.selection.endPos
                        && false === this.content[this.selection.startPos].isSelectionUse() ) {
                    this.selection.bUse = false;
                }

                this.curPos.contentPos = this.selection.endPos;
            } else {
                // 光标进入上一个段落
                if ( false === this.content[this.curPos.contentPos].moveCursorUp(bShiftLey) ) {
                    // 当前段落不是第一个段落
                    if ( 0 !== this.curPos.contentPos ) {
                        // 保存光标在当前段落的位置
                        // this.updateCursorXY();
                        // console.log("doc      ("+ this.curPos.x + ", " + this.curPos.y + ")");
                        this.curPos.x = this.curPos.realX;
                        this.curPos.contentPos--;
                        this.content[this.curPos.contentPos].moveCursorUpToLastRow(
                                                this.curPos.x, this.curPos.realY, bShiftLey);
                    } else {
                        result = false;
                    }
                }
            }
        }

        return result;
    }

    /**
     * 键盘光标下移
     * @param bShiftLey
     */
    public moveCursorDown(bShiftLey: boolean = false): boolean {
        if ( 0 > this.curPos.contentPos ) {
            return false;
        }

        let result = true;

        if ( true === this.selection.bUse ) {
            if ( true === bShiftLey) {
                const direction = this.selection.startPos === this.selection.endPos ?
                                    0 : this.selection.startPos < this.selection.endPos ? 1 : -1;

                if ( false === this.content[this.selection.endPos].moveCursorDown(bShiftLey) ) {
                    // 当前段落不是最后一个段落
                    if ( this.content.length - 1 !== this.selection.endPos ) {
                        // 保存光标在当前段落的位置
                        // this.updateCursorXY();
                        this.curPos.x = this.curPos.realX;
                        this.curPos.y = this.curPos.realY;

                        if ( -1 === direction ) {
                            this.content[this.selection.endPos].removeSelection();
                        }

                        this.selection.endPos++;
                        this.content[this.selection.endPos].moveCursorDownToFirstRow(
                                                        this.curPos.x, this.curPos.realY, bShiftLey);
                    } else {
                        result = false;
                    }

                    // 当前没有内容选择
                    if ( this.selection.startPos === this.selection.endPos
                        && false === this.content[this.selection.startPos].isSelectionUse() ) {
                        this.selection.bUse = false;
                    }

                    this.curPos.contentPos = this.selection.endPos;
                }
            } else {
                this.curPos.contentPos = this.selection.startPos > this.selection.endPos ?
                                            this.selection.startPos : this.selection.endPos;
                // 光标进入上一个段落
                if ( false === this.content[this.curPos.contentPos].moveCursorDown(bShiftLey) ) {
                    // 当前段落不是第一个段落
                    if ( 0 !== this.curPos.contentPos ) {
                        this.updateCursorXY();
                        this.curPos.contentPos++;
                        this.content[this.curPos.contentPos].moveCursorDownToFirstRow(
                                        this.curPos.x, this.curPos.realY, bShiftLey);
                    } else {
                        result = false;
                    }
                }
                this.removeSelection();
            }
        } else {
            // shift: selection
            if ( true === bShiftLey) {
                this.selection.bUse = true;
                this.selection.startPos = this.curPos.contentPos;
                this.selection.endPos = this.curPos.contentPos;

                // 光标进入下一个段落
                if ( false === this.content[this.curPos.contentPos].moveCursorDown(bShiftLey) ) {
                    // 当前段落不是最后一个段落
                    if ( this.content.length - 1 !== this.curPos.contentPos ) {
                        // 保存光标在当前段落的位置
                        this.updateCursorXY();
                        this.curPos.x = this.curPos.realX;
                        this.curPos.contentPos++;
                        this.selection.endPos = this.curPos.contentPos;
                        this.content[this.curPos.contentPos].moveCursorDownToFirstRow(
                                            this.curPos.x, this.curPos.realY, bShiftLey);
                    } else {
                        result = false;
                    }
                }

                // 当前没有内容选择
                if ( this.selection.startPos === this.selection.endPos
                    && false === this.content[this.selection.startPos].isSelectionUse() ) {
                    this.selection.bUse = false;
                }
                this.curPos.contentPos = this.selection.endPos;
            } else {
                // 光标进入下一个段落
                if ( false === this.content[this.curPos.contentPos].moveCursorDown(bShiftLey) ) {
                    // 当前段落不是最后一个段落
                    if ( this.content.length - 1 !== this.curPos.contentPos ) {
                        // 保存光标在当前段落的位置
                        // this.updateCursorXY();
                        this.curPos.contentPos++;
                        this.curPos.x = this.curPos.realX;

                        this.content[this.curPos.contentPos].moveCursorDownToFirstRow(
                                                    this.curPos.x, this.curPos.realY, bShiftLey);
                    } else {
                        result = false;
                    }
                }
            }
        }

        return result;
    }

    public moveCursorUpToLastRow(pointX: number, pointY: number, bShiftLey: boolean = false): void {
        this.setCurPosXY(pointX, pointY);

        if ( true === bShiftLey ) {
            // todo
        } else {
            this.curPos.contentPos = this.content.length - 1;
            this.content[this.curPos.contentPos].moveCursorUpToLastRow(pointX, pointY, bShiftLey);
        }
    }

    public moveCursorDownToFirstRow(pointX: number, pointY: number, bShiftLey: boolean = false): void {
        this.setCurPosXY(pointX, pointY);

        if ( true === bShiftLey ) {
            // todo
        } else {
            this.curPos.contentPos = 0;
            this.content[this.curPos.contentPos].moveCursorDownToFirstRow(pointX, pointY, bShiftLey);
        }
    }

    public getFoucsInRegion(pointX: number, pointY: number, pageIndex: number): Region {
        const contentPos = this.getContentPosByXY(pageIndex, pointX, pointY);
        if ( contentPos === undefined ) {
            return this.parent as Region;
        }

        const content = this.content[contentPos];
        if (content.getType() !== DocumentContentType.Region) {
            return this.parent as Region;
        }

        const elementIndex = this.getElementPageIndexByXY(contentPos, pointX, pointY, pageIndex);

        return content.getFoucsInRegion(pointX, pointY, elementIndex);
    }

    /**
     * 计算当前光标位置
     * @param bUpdateX
     * @param bUpdateY
     */
    public recalculateCurPos(bUpdateX: boolean = true, bUpdateY: boolean = true): ICurrentCursorPos {
        let curPos = null;

        if ( DocCurPosType.Content === this.curPos.type ) { // headerfooter's content, naturally in?
            if ( 0 <= this.curPos.contentPos && this.content[this.curPos.contentPos] ) {
                // todo: headerfooter
                if ( 0 < this.curPage && (this.parent as any).isHeaderFooter(false) === true ) {
                    this.curPage = 0;
                } else {
                    // console.log(circularParse(circularStringify(this.content)));
                    curPos = this.content[this.curPos.contentPos].recalculateCurPos(bUpdateX, bUpdateY);

                    if ( this.parent.isHeaderFooter(false) && curPos
                        && curPos.pageNum !== this.logicDocument.getCurPage() ) {
                        curPos.pageNum = this.logicDocument.getCurPage();
                    }
                }
            }
        } else {
            // todo: Drawing
        }

        if ( curPos ) {
            if ( bUpdateX ) {
                this.curPos.realX = curPos.x;
            }

            if ( bUpdateY ) {
                this.curPos.realY = curPos.y;
            }
        }

        // improvised way, may be problematic
        // if (this.logicDocument.getDocPosType() === DocCurPosType.HdrFtr) {
        //     this.startPage = this.logicDocument.getCurPage();
        // }

        return curPos;
    }

    /**
     * 更行当前光标位置
     * @param bUpdateX
     * @param bUpdateY
     */
    public updateCursorXY( bUpdateX: boolean = true, bUpdateY: boolean = true ): void {
        let newCurPos = null;

        // 光标没有进行选择
        if ( true !== this.selection.bUse ) {
            newCurPos = this.recalculateCurPos(bUpdateX, bUpdateY);
        } else {
            // 选择
            newCurPos = this.getCursorPosXY();
        }

        if (newCurPos == null) {
            return;
        }

        if ( bUpdateX ) {
            this.curPos.realX = newCurPos.x;
        }

        if ( bUpdateY ) {
            this.curPos.realY = newCurPos.y1 || newCurPos.y;
        }
    }

    public getCursorPosXY(): ICursorProperty {
        const position = {
            x: 0,
            y1: 0,
            y2: 0,
            pageNum: 0,
        };

        if ( DocCurPosType.Content === this.curPos.type ) { // should also naturally include headerfooter
            let element = this.content[this.curPos.contentPos];
            if ( element.isParagraph() ) {
                let portionPos = element.getCurPos().contentPos;

                if ( true === this.selection.bUse && SelectionFlagType.Common === this.selection.flag ) {
                    const selElement = this.content[this.selection.endPos];
                    if ( selElement && selElement.isSelectionUse() ) {
                        element = selElement;
                        portionPos = selElement.getSelection().endPos;
                    }
                }

                const portion = element.getContent()[portionPos];
                let textPos = element.getCursorPosXY();
                // console.log(portionPos, ",  " + portion.portionContentPos);
                if ( 0 >= textPos.x || 0 >= textPos.y ) {
                    textPos = element.recalculateCurPos();
                }

                const measure = portion.measure(new ParaText(' '));
                let textHeight = portion.textHeight;
                if (textHeight === 0) {
                    textHeight = measure;
                }

                if ( null != textPos ) {
                    if ( this.parent.isHeaderFooter(false) && textPos.pageNum !== this.logicDocument.getCurPage() ) {
                        textPos.pageNum = this.logicDocument.getCurPage();
                    }

                    return {
                        x: textPos.x,
                        y1: textPos.y - textHeight, // - portion.ascent, // - ( portion.descent >> 2),
                        y2: textPos.y + 3,
                        pageNum: textPos.pageNum,
                    };
                }
            } else {
                if (this.selection.bUse) {
                    return this.content[this.selection.endPos].getCursorPosXY();
                } else {
                    return this.content[this.curPos.contentPos].getCursorPosXY();
                }
            }
        } else {
            // todo: Drawing;
        }

        return position;
    }

    public setCurPosXY(pointX: number, pointY: number): void {
        this.curPos.realX = pointX;
        this.curPos.realY = pointY;
    }

    public setContentPos(pos: ParagraphContentPos): boolean {
        const index = pos.shift();
        if (index === undefined || this.content.length <= index) {
            return false;
        }

        this.curPos.contentPos = index;
        return this.content[index].setContentPos(pos);
    }

    public isNotDeleteRegionTitle(region: Region, bInput: boolean = false): boolean {
        if (!region.isShowTitle()) {
            return true;
        }

        const content = region.getOperateContent();
        const contentPos = content.curPos.contentPos;
        if (contentPos !== 0) {
            return true;
        }
        const contents = content.content;
        const titlePara = contents[0];
        if (!(titlePara instanceof Paragraph)) {
            return true;
        }
        const portionPos = titlePara.curPos.contentPos;
        const portions = titlePara.content;
        // 修复第一个为批注的情况下，判断情况会发生改变
        if (portionPos === 0) {
            const portion = portions[portionPos];
            const len = portion.content.length;
            if (bInput === true && len !== 0 && portion.portionContentPos === len && !portion.isComment()) {
                return true;
            }
            return false;
        }  else {
            const titlePortion = region.getTitlePortion();
            // 为了减少影响，只判断当前第一个为批注的情况
            if (portions[portionPos - 1].isComment() && portions[portionPos] === titlePortion && titlePortion.portionContentPos !== titlePortion.content.length) {
                return false;
            } else if (bInput === true) {
                return true;
            }
        }

        // while (portionPos > 0) {
        //     const portion = portions[portionPos];
        //     if (portion.content.length !== 0 && portion.portionContentPos !== 0) {
        //         return true;
        //     }
        //     portionPos--;
        // }
        const searchPos = {
            pos: new ParagraphContentPos(),
            bFound: false,
            bSelection: false,
        };
        const pos = titlePara.getParaContentPos(false);
        if (titlePara.getLeftPos(searchPos, pos) && searchPos.bFound) {
            return !(titlePara.content[searchPos.pos.get(0)]?.isRegionTitle(false));
        }

        return false;
    }

    public removeContainedComments(bRemovePortion: boolean = false): void {
        const contentPos = new ParagraphContentPos();
        const topParent = this.getTopDocument();

        switch (topParent.getDocumentSectionType()) {
            case DocumentSectionType.Header:
                contentPos.add(0);
                break;
            case DocumentSectionType.Document:
                contentPos.add(1);
                break;
            case DocumentSectionType.Footer:
                contentPos.add(2);
                break;
        }
        this.getParentIndexs(contentPos);
        const startPos = contentPos.copy();
        this.getStartPos(startPos);
        const endPos = contentPos.copy();
        this.getEndPos(true, endPos);
        const manager = this.getDocument()
            .getCommentManager();
        if (bRemovePortion) {
            const deletedComments = manager.getCrossComments(startPos, endPos)
                .map((info) => info.comment);
            manager.deleteComments(deletedComments);
        } else {
            const deletedComments = manager.getCrossComments(startPos, endPos)
                .filter((info) => info.type === CommentResultType.Inner)
                .map((info) => info.comment);
            manager.deleteCommentsDirected(deletedComments, true);
        }
    }

    public removeSelection(): void {
        if ( true === this.selection.bUse ) {
            switch ( this.selection.flag ) {
                case SelectionFlagType.Common: {
                    let startPos = this.selection.startPos;
                    let endPos = this.selection.endPos;

                    if ( startPos > endPos ) {
                        const temp = startPos;
                        startPos = endPos;
                        endPos = temp;
                    }

                    startPos = Math.max(0, startPos);
                    endPos = Math.min(endPos, this.content.length - 1);

                    for (let index = startPos; index <= endPos; index++) {
                        this.content[index].removeSelection();
                    }

                    break;
                }
                default:
                    break;
            }
        }

        this.selection.bStart = false;
        this.selection.bUse = false;

        this.selection.startPos = 0;
        this.selection.endPos = 0;
        this.selection.flag = SelectionFlagType.Common;
    }

    public canRemoveAtCurCell(direction: number): any {
        // 跨页不处理，太过于复杂
        if (this.pages.length !== 1) {
            return;
        }
        const contents = this.content;
        const selection = this.selection;
        const cell = this.parent as TableCell;
        if (selection.bUse) {
            let start = selection.startPos;
            let end = selection.endPos;
            if (start > end) {
                const tem = start;
                start = end;
                end = tem;
            }

            let maxHeight: number = 0; // 当前单元格删除后剩余的内容高度
            const option = {width: 0, height: 0, bMerge: false, bLast: false, lineHeight: 0};
            contents.forEach((content: Paragraph, index) => {
                if (index > start && index < end) {
                    return;
                }
                if (index === start) {
                    option.bMerge = true;
                    option.bLast = end === index;
                    const res: any = content.canRemoveAtCurCell(option) || {};
                    if (!res) {
                        return;
                    }
                    option.width = res.width;
                    option.height = res.height;
                    option.lineHeight = res.lineHeight;
                    if (option.bLast) {
                        maxHeight += option.height;
                    }
                    return;
                } else if (end === index) {
                    option.bMerge = false;
                    option.bLast = true;
                    const res = content.canRemoveAtCurCell(option);
                    if (res) {
                        maxHeight += res.height;
                    }
                   return;
                }
                const lines = content.getLines();
                maxHeight += lines[lines.length - 1].bottom - lines[0].top;
            });
            
            const rects = cell.oldSelectionBounds;
            const rect = rects[0];
            let result: any;
            if (start === end) {
                result = contents[start];
            } else {
                result = cell;
            }

            const contentHeight = rect.contentHeight - 0.03;
            if (contentHeight < maxHeight) { // 段落行高不变情况下
                return result;
            }

            const cellMaxHeight = cell.getMaxHeight();
            if (cellMaxHeight > contentHeight) { // 表格行高不变的情况下进行刷新
                return result;
            }

            return;
        }
        const obj: any = {};
        const para: Paragraph = contents[this.curPos.contentPos] as Paragraph;
        const curRes = para.canRemoveAtCurCell(obj, direction);
        if (!curRes || !curRes.height) {
            return;
        }
        const lines = para.getLines();
        const oldMaxParaHeight = lines[lines.length - 1].bottom - lines[0].top - 0.03;
        const curParaHeight = curRes.height;
        if (oldMaxParaHeight < curParaHeight) { // 当前段落行高不变
            return para;
        }

        const curCellMaxHeight = cell.getMaxHeight();
        const cellContentHeight = cell.oldSelectionBounds[0].contentHeight;
        if (cellContentHeight - (oldMaxParaHeight - curParaHeight) < curCellMaxHeight) {
            return cell;
        }
    }

    public remove(direction: number, bOnlyText: boolean, bOnlySelection: boolean,
                  bAddText: boolean, bSelectParaEnd?: boolean, option?: any): IOperateResult {
        let result = {res: true, bNeedRecal: true};
        if ( true === this.bApplyToAll ) {
            this.removeAllContent();
            this.addToContent(0, this.createNewParagraph());

            this.curPos.contentPos = 0;
            this.curPos.type = DocCurPosType.Content;

            this.selection.bUse = false;
            this.selection.bStart = false;
            this.selection.startPos = 0;
            this.selection.endPos = 0;
            this.selection.flag = SelectionFlagType.Common;
            this.selection.data = null;

            return result;
        }

        if ( DocCurPosType.DrawingObjects === this.curPos.type ) {
            // todo
        } else {
            const checkNewControls: ICheckSelectionNewControls = {
                para: null,
                bInsertNewControlsBorder: false,
                behindOverNewControls: [],
                forwardOverNewControls: [],
            };

            result = this.privateRemove(direction, bOnlyText, bOnlySelection, bAddText, checkNewControls);

            if ( (null !== checkNewControls.para || result.bNeedRecal) && option?.bNeedRecal !== false) {
                this.recalculate();
                result.bNeedRecal = false;
            }

            this.updateCursorXY();
            this.checkNewControlPlaceHolder(checkNewControls);

            const bHeaderFooter = this.isHeaderFooter(false);
            if ( bHeaderFooter ) {
                const doc = this.getDocument();

                if ( doc ) {
                    doc.behindOverNewControls = null;
                    doc.forwardOverNewControls = null;
                }
            }

            return result;
        }

        return result;
    }

    public preDelete(): void {
        for (let index = 0, count = this.content.length; index < count; index++) {
            this.content[index].preDelete();
        }
    }

    public getCurrentTextProps(): any {
        if (this.selection.bUse === true) {
            return this.getSelectedTextPro();
        }

        const curPara = this.getCurrentParagraph() as Paragraph;
        if (!curPara) {
            return {} as any;
        }

        const portion = curPara.getCursorPortion();
        return portion.textProperty;
    }

    /**
     * 获取选中的字所有状态
     */
    public getSelectedTextPro(): TextProperty {
        const textPrs: ITextProperty = {fontWeight: undefined, fontSize: undefined, fontStyle: undefined,
            textDecorationLine: undefined, vertAlign: undefined, fontFamily: undefined, color: undefined,
            backgroundColor: undefined};
        this.getTextProperty(textPrs);
        const res = new TextProperty();
        Object.keys(textPrs)
            .forEach((key) => {
                if (textPrs[key] === null) {
                    textPrs[key] = undefined;
                }
                res[key] = textPrs[key];
        });

        return res;
    }

    public add(paraItem: any): number {
        const selection = this.selection;
        if (!selection.bUse) {
            return ResultType.Failure;
        }
        let startPos = selection.startPos;
        let endPos = selection.endPos;
        if ( startPos > endPos ) {
            const temp = endPos;
            endPos = startPos;
            startPos = temp;
        }
        let res: number = ResultType.UnEdited;
        for ( let curPos = startPos; curPos <= endPos; curPos++ ) {
            res = this.content[curPos].add(paraItem) && res;
        }

        return res;
    }

    /**
     * 获取当前光标处段落
     */
    public getCurrentParagraph(): Paragraph {
        if ( DocCurPosType.Content === this.curPos.type ) {
            const pos = ( true === this.selection.bUse ? this.selection.startPos : this.curPos.contentPos );
            if ( 0 > pos || this.content.length <= pos) {
                return null;
            }

            return this.content[pos].getCurrentParagraph();
        }

        return null;
    }

    /**
     * 是否在table边框
     * @param pointX
     * @param pointY
     * @param curPage
     */
    public isTableBorder(pointX: number, pointY: number, curPage: number, options?: any): TableBase {
        curPage = Math.max(0, Math.min(curPage, this.pages.length - 1));

        const elementPos = this.getContentPosByXY(curPage, pointX, pointY);
        const element = this.content[elementPos];
        const elementPageIndex = this.getElementPageIndex(elementPos, curPage);
        return element.isTableBorder(pointX, pointY, elementPageIndex, options);
    }

    public setSelectionUse(bUse: boolean): void {
        if (true === bUse) {
            this.selection.bUse = true;
        } else {
            this.removeSelection();
        }
    }

    public setSelectionBeginEnd(bSelectStart: boolean, bEnd: boolean): void {
        if (this.content.length <= 0) {
            return;
        }

        const contents = this.content;
        const selection = this.selection;
        if (true === bEnd) {
            contents[0].setSelectionUse(true);
            contents[0].setSelectionBeginEnd(bSelectStart, true);
            if (bSelectStart) {
                selection.startPos = 0;
            } else {
                selection.endPos = 0;
            }
        } else {
            contents[contents.length - 1].setSelectionUse(true);
            contents[contents.length - 1].setSelectionBeginEnd(bSelectStart, false);

            if (bSelectStart) {
                selection.startPos = contents.length - 1;
            } else {
                selection.endPos = contents.length - 1;
            }
        }
    }

    public isMovingTableBorder(): boolean {
        if ( DocCurPosType.DrawingObjects === this.curPos.type ) {
            // todo
        } else {
            if ( null != this.selection.data && true === this.selection.data.bTableBorder ) {
                return true;
            }
        }

        return false;
    }

    public getMovingTableNewBorder(): IDrawTableNewBorder {
        const border = {
            bColumn: false,
            x: 0,
            y: 0,
            height: 0,
            width: 0,
            pageIndex: 0,
        };

        if ( this.isMovingTableBorder() ) {
            const element = this.content[this.selection.data.pos];

            if ( element && element instanceof TableBase ) {
                const data = element.selection.data2 as ITableSelectionData;

                if ( data ) {
                    border.bColumn = data.bColumn;
                    border.x = data.x;
                    border.y = data.y;

                    const document = this.getDocument();
                    border.height = document.isInlineMode()
                        ? document.inlinePageBoundY
                        : document.pages[this.curPage].height;
                    border.width = document.pages[this.curPage].width;
                }
            } else if ( element && element instanceof Region ) {
                return element.getMovingTableNewBorder();
            }
        }

        return border;
    }

    /**
     * 设置选择开始位置
     * @param pointX
     * @param pointY
     * @param curPage
     * @param mouseEvent
     */
    public setSelectionStart(pointX: number, pointY: number, curPage: number, mouseEvent: IMouseEvent): void {
        if ( 0 >= this.pages.length ) {
            return;
        }

        if ( 0 > curPage ) {
            curPage = 0;
            pointY = 0;
        } else if ( this.pages.length <= curPage ) {
            curPage = this.pages.length - 1;
            pointY = this.pages[curPage].yLimit;
        }

        this.curPage = curPage;
        const absolutePage = this.getAbsolutePage(this.curPage);

        let bTableBorder = ( null != this.isTableBorder(pointX, pointY, absolutePage) ? true : false );

        const contentPos = this.getContentPosByXY(this.curPage, pointX, pointY);
        const bSelected = this.selection.bUse;
        const element = this.content[contentPos];

        // 当前不是选择状态，或者shift键
        if ( !( true === bSelected && true === mouseEvent.bShiftKey ) ) {
            if ( ( SelectionFlagType.Common !== this.selection.flag )
                 || ( true === this.selection.bUse && 1 >= mouseEvent.clickCount ) ) {
                this.removeSelection();
            }
        }

        this.selection.bUse = true;

        if ( this.getDocument().getImageFlags().isImageOnClick ) {
            this.selection.bStart = false;
            this.selection.flag = SelectionFlagType.Drawing;
        } else {
            this.selection.bStart = true;
            this.selection.flag = SelectionFlagType.Common;
        }

        bTableBorder = ( null != element.isTableBorder(pointX, pointY, absolutePage) ? true : false );

        // 当前已经有选中区域，且同时在shift键下，继续选择
        if ( true === bSelected && true === mouseEvent.bShiftKey ) {
            this.setSelectionEnd(pointX, pointY, curPage, {
                clickCount: 1,
                type: MouseEventType.MouseButtonUp,
                bCtrlKey: mouseEvent.bCtrlKey,
            });
            this.selection.endPos = contentPos;
            this.selection.data = null;
        } else {
            const elementPageIndex = this.getElementPageIndexByXY(contentPos, pointX, pointY, curPage);
            element.setSelectionStart(pointX, pointY, elementPageIndex, mouseEvent);
            element.setSelectionEnd(pointX, pointY, elementPageIndex, {
                clickCount: 1,
                type: MouseEventType.MouseButtonMove,
                bCtrlKey: mouseEvent.bCtrlKey,
            });

            // 未选中表格边框
            if ( true !== bTableBorder ) {
                this.selection.startPos = contentPos;
                this.selection.endPos = contentPos;
                this.selection.data = null;
                this.curPos.contentPos = contentPos;
            } else {
                this.selection.data = {
                    bTableBorder: true,
                    pos: contentPos,
                    bSelected,
                };
            }
        }
    }

    /**
     * 设置选择结束位置
     * @param pointX
     * @param pointY
     * @param curPage
     * @param mouseEvent
     */
    public setSelectionEnd(pointX: number, pointY: number, curPage: number, mouseEvent: IMouseEvent): void {
        if ( 0 >= this.pages.length ) {
            return;
        }

        if ( 0 > curPage ) {
            curPage = 0;
            pointY = 0;
        } else if ( this.pages.length <= curPage ) {
            curPage = this.pages.length - 1;
            pointY = this.pages[curPage].yLimit;
        }

        this.curPage = curPage;

        if ( null != this.selection.data && true === this.selection.data.bTableBorder
            && DocumentContentType.Table === this.content[this.selection.data.pos].getType() ) {
            const pos = this.selection.data.pos;

            if ( mouseEvent != null && MouseEventType.MouseButtonUp === mouseEvent.type ) {
                this.selection.bStart = false;
                this.selection.bUse = this.selection.data.bSelected;
                this.selection.data = null;
            }
            const element = this.content[pos];
            const elementPageIndex = this.getElementPageIndexByXY(pos, pointX, pointY, this.curPage);
            element.setSelectionEnd(pointX, pointY, elementPageIndex, mouseEvent);

            return;
        }

        if ( false === this.selection.bUse ) {
            return;
        }

        const contentPos = this.getContentPosByXY(curPage, pointX, pointY);

        // 上一次选择的结束位置
        const oldEndPos   = this.selection.endPos;

        // 设置当前光标位置
        this.curPos.contentPos = contentPos;
        this.selection.endPos = contentPos;

        // 删除旧的选择区域：不在[startPos, endPos]之间的都需要删除选中标记
        if ( oldEndPos < this.selection.startPos && oldEndPos < this.selection.endPos ) {
            const temp = Math.min(this.selection.startPos, this.selection.endPos);

            for (let index = oldEndPos; index < temp; index++) {
                this.content[index].removeSelection();
            }
        } else if ( oldEndPos > this.selection.startPos && oldEndPos > this.selection.endPos ) {
            const temp = Math.max(this.selection.startPos, this.selection.endPos);

            for (let index = temp + 1; index <= oldEndPos; index++) {
                this.content[index].removeSelection();
            }
        }

        // 鼠标释放，停止选择
        if ( mouseEvent != null && MouseEventType.MouseButtonUp === mouseEvent.type ) {
            this.stopSelection(mouseEvent.bShiftKey);
        }

        const direction = ( contentPos > this.selection.startPos ?
                                                1 : ( contentPos < this.selection.startPos ? -1 : 0 ) );

        let start = this.selection.startPos;
        let end = contentPos;
        if ( -1 === direction ) {
            const temp = start;
            start    = end;
            end      = temp;
        } else if ( 0 === direction ) {
            const element = this.content[start];
            const elementPageIndex = this.getElementPageIndexByXY(start, pointX, pointY, this.curPage);
            element.setSelectionEnd(pointX, pointY, elementPageIndex, mouseEvent);

            if ( false === element.isSelectionUse() ) {
                this.selection.bUse = false;
            } else {
                this.selection.bUse = true;
            }

            return ;
        }

        const pageIndex = this.getElementPageIndexByXY(contentPos, pointX, pointY, this.curPage);
        this.content[contentPos].setSelectionEnd(pointX, pointY, pageIndex, mouseEvent);

        for (let index = start; index <= end; index++) {
            const element = this.content[index];
            element.setSelectionUse(true);

            switch ( index ) {
                case start:
                    element.setSelectionBeginEnd( 0 < direction ? false : true, false);
                    break;

                case end:
                    element.setSelectionBeginEnd( 0 < direction ? true : false, true);
                    break;

                default:
                    element.selectAll(direction);
                    break;
            }
        }
    }

    /**
     * 从当前光标位置开始选择
     */
    public startSelectionByCurPos(): void {
        if ( DocCurPosType.DrawingObjects === this.curPos.type ) {
            return ;
        } else {
            this.selection.bStart = true;
            this.selection.bUse = true;

            this.selection.startPos = this.curPos.contentPos;
            this.selection.endPos = this.curPos.contentPos;
            this.content[this.curPos.contentPos].startSelectionByCurPos();
        }
    }

    /**
     * 选择区域是否为空
     * @param bContainParaEnd 是否包含段落结束符
     */
    public isSelectionEmpty( bContainParaEnd?: boolean ): boolean {
        if ( true === this.selection.bUse ) {
            if ( null != this.selection.data && true === this.selection.data.bTableBorder
                && DocumentContentType.Table === this.content[this.selection.data.pos].getType() ) {
                return false;
            } else {
                if ( this.selection.startPos === this.selection.endPos ) {
                    return this.content[this.selection.startPos].isSelectionEmpty(bContainParaEnd);
                } else {
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * 获取选择区域
     */
    public getSelectionBounds(bStart: boolean, bMultis: boolean): IDrawSelectionBounds {

        let result = null;
        if ( true === this.selection.bUse && SelectionFlagType.Common === this.selection.flag ) {
            result = {
                lines: [],
                cells: [],
                direction: this.getSelectionDirection(),
            };

            let start = this.selection.startPos;
            let end = this.selection.endPos;

            if ( start > end ) {
                start = this.selection.endPos;
                end = this.selection.startPos;
            }

            if ( start === end ) {
                const startElementType = this.content[start].getType();
                const selections = this.content[start].getSelectionBounds(bStart, bMultis);

                if ( DocumentContentType.Table !== startElementType &&
                     DocumentContentType.Region !== startElementType && selections ) {
                    result.lines = this.getSelectedLines(selections, null);
                } else if ( (DocumentContentType.Table === startElementType ||
                    DocumentContentType.Region === startElementType) && selections ) {
                    result.lines = result.lines ?
                                (selections && selections.lines ? result.lines.concat(selections.lines) : null)
                                : (selections && selections.lines ? selections.lines : null);
                    result.cells = result.cells ?
                                (selections && selections.cells ? result.cells.concat(selections.cells) : null)
                                : (selections && selections.cells ? selections.cells : null);
                } else if ( selections ) {
                    result.lines = selections;
                }
            } else {
                const startElementType = this.content[start].getType();
                const endElementType = this.content[end].getType();
                const startSelections = this.content[start].getSelectionBounds(true, true);

                let bEndStart = false;

                // todo: if cursor in this end, not: bEndStart = true;

                const endSelections = this.content[end].getSelectionBounds(bEndStart, true);

                if ( DocumentContentType.Paragraph !== startElementType && startSelections ) {
                    result.lines = result.lines ?
                                ( startSelections.lines ? result.lines.concat(startSelections.lines) : null)
                                 : (startSelections && startSelections.lines ? startSelections.lines : null);
                    result.cells = result.cells ?
                                ( startSelections.cells ? result.cells.concat(startSelections.cells) : null)
                                : (startSelections && startSelections.cells ? startSelections.cells : null);
                }

                for (let index = start + 1; index < end; index++) {
                    const element = this.content[index];
                    const elementType = element.getType();

                    if ( DocumentContentType.Paragraph !== elementType && !(element.isHidden()) ) {
                        const selections = element.getSelectionBounds(true, true);
                        result.lines = result.lines ?
                                    (selections && selections.lines ? result.lines.concat(selections.lines) : null)
                                    : (selections && selections.lines ? selections.lines : null);
                        result.cells = result.cells ?
                                    (selections && selections.cells ? result.cells.concat(selections.cells) : null)
                                    : (selections && selections.cells ? selections.cells : null);
                    }
                }

                if ( null == result.lines ) {
                    result.lines = this.getSelectedLines(startSelections, endSelections);
                } else {
                    result.lines = result.lines.concat(this.getSelectedLines(startSelections, endSelections));
                }

                if ( DocumentContentType.Paragraph !== endElementType && endSelections ) {
                    result.lines = result.lines.concat(endSelections.lines);
                    result.cells = result.cells.concat(endSelections.cells);
                }
            }
        // } else if ( true === bApplyToAll ) {
        //     result = {
        //         lines: [],
        //         cells: [],
        //         direction: this.getSelectionDirection(),
        //     };

        //     for (const element of this.content) {
        //         if ( DocumentContentType.Table === element.getType() ) {
        //             result.lines = element.getNewControlBounds(undefined, null, null);
        //         } else {
        //             result.lines = result.lines.concat(element.getSelectedLines(null));
        //         }
        //     }
        }

        if (result && result.lines && 0 < result.lines.length) {
            if (this.isRegionContent()) {
                result.lines.forEach((element) => {
                    if (element) {
                        element['region'] = 1;
                    }
                });
            } else if (this.isTableCellContent()) {
                result.fixedType = (this.parent as TableCell).getFixedProps();
            }
        }

        return result;
    }

    /**
     * 判断当前选择区域的方向
     * 1: 向下选择（正向选择）； -1：向上选择（反向选择）；
     */
    public getSelectionDirection(): number {
        if ( true !== this.selection.bUse ) {
            return 0;
        }

        if ( this.selection.startPos < this.selection.endPos ) {
            return 1;
        } else if ( this.selection.startPos > this.selection.endPos ) {
            return -1;
        }

        return this.content[this.selection.startPos].getSelectionDirection();
    }

    public isSelectedAll(): boolean {
        const selection = this.selection;
        if (!selection.bUse) {
            return false;
        }

        let startPos = selection.startPos;
        let endPos = selection.endPos;
        if (startPos > endPos) {
            const temp = startPos;
            startPos = endPos;
            endPos = temp;
        }
        if (startPos !== 0) {
            return false;
        }
        const contents = this.content;
        const length = contents.length - 1;
        if (endPos !== length) {
            return false;
        }

        if (contents[startPos].isSelectedAll() !== true) {
            return false;
        }

        return contents[endPos].isSelectedAll();
    }

    public selectAll(direction: number = 1): void {
        if ( DocCurPosType.DrawingObjects === this.curPos.type ) {
            // todo
        } else {
            if ( true === this.selection.bUse ) {
                this.removeSelection();
            }

            this.setDocPosType(DocCurPosType.Content);
            this.selection.bUse = true;
            this.selection.bStart = false;
            this.selection.flag = SelectionFlagType.Common;
            this.selection.startPos = 0;
            this.selection.endPos = this.curPos.contentPos = this.content.length - 1;

            for (let index = 0, count = this.content.length; index < count; index++) {
                this.content[index].selectAll(direction);
            }
        }
    }

    public getDocumentElementState(): DocumentElementState[][] {
        let state: DocumentElementState[][];

        const docState = new DocumentState(this.curPos, this.selection);

        docState.curPage = this.curPage;

        if ( true === this.selection.bUse ) {
            let startPos = this.selection.startPos;
            let endPos = this.selection.endPos;

            if ( startPos > endPos ) {
                const temp = startPos;
                startPos = endPos;
                endPos = temp;
            }

            state = [];
            const tempState: DocumentElementState[] = [];
            for (let index = startPos; index <= endPos; index++) {
                tempState.push(this.content[index].getDocumentElementState());
            }

            state.push(tempState);
        } else {
            state = [this.content[this.curPos.contentPos].getDocumentElementState()];
        }

        state.push([docState]);
        return state;
    }

    /**
     * 设置当前文档状态，用于undo/redo
     * @param state
     */
    public setDocumentState( state: any, stateIndex: number ): void {
        const count = state.length;
        if ( 0 >= count ) {
            return ;
        }

        const docState = state[stateIndex][0];

        this.curPos.x = docState.curPos.x;
        this.curPos.y = docState.curPos.y;
        this.curPos.realX = docState.curPos.realX;
        this.curPos.realY = docState.curPos.realY;
        // this.curPos.type = docState.curPos.type;
        this.curPos.contentPos = docState.curPos.contentPos;
        this.setDocPosType(docState.curPos.type);

        this.selection.bStart = docState.selection.bStart;
        this.selection.bUse = docState.selection.bUse;
        this.selection.startPos = docState.selection.startPos;
        this.selection.endPos = docState.selection.endPos;
        this.selection.data = docState.selection.data;
        this.selection.bWordSelected = docState.selection.bWordSelected;

        this.curPage = docState.curPage;

        this.setDocumentElementState( state, stateIndex - 1 );
    }

    /**
     * 设置当前文档元素（文档中的段落、表格等）状态，用于undo/redo
     * @param state
     * @param index
     */
    public setDocumentElementState( state: any, stateIndex: number ): void {
        if ( true === this.selection.bUse ) {
            let startPos = this.selection.startPos;
            let endPos = this.selection.endPos;

            if ( startPos > endPos ) {
                const temp = startPos;
                startPos = endPos;
                endPos = temp;
            }

            const curState = state[stateIndex];
            for (let index = startPos; index <= endPos; index++) {
                const data = curState[index - startPos];
                if (!data) {
                    continue;
                }
                this.content[index].setDocumentElementState(data, data.length - 1);
            }
        } else {
            this.content[this.curPos.contentPos].setDocumentElementState(state[stateIndex], stateIndex);
        }
    }

    public addNewParagraph(): number {
        if ( 0 > this.curPos.contentPos ) {
            return ResultType.Failure;
        }

        if ( true === this.selection.bUse ) {
            this.remove(1, true, false, true);
        }

        const element = this.content[this.curPos.contentPos];

        if ( element.isParagraph() ) {
            const newPara = new Paragraph(this, this.logicDocument);
            const bAtBegin = element.isCursorAtBegin();
            let contentPos = this.curPos.contentPos;
            const contents = element.getContent();
            const bHidden = contents[Math.min(element.getCurPos().contentPos + 1, contents.length - 1)]
                            .isHidden();
            const region = this.getParent() as Region;
            const bRegionContent = (this.isRegionContent() && region.isCursorInRegionTitle(false));

            // 段落末尾，新增新段落
            if (bHidden !== true && true === element.isCursorAtEnd() || true === bAtBegin ) {
                // 拷贝当前光标处的文本属性
                // TODO： 拷贝段落属性
                let textPr = element.getContent()[element.getCurPos().contentPos].textProperty
                                                                                    .copy();
                if (bRegionContent) {
                    textPr = this.getRegionContentTextPr(newPara)
                                    .copy();
                }

                // newPara.addEnd(textPr);
                const paraText = new ParaTextProperty();
                paraText.textProperty = textPr;
                newPara.add(paraText);
                newPara.paraProperty = element.getParagraphProperty()
                                                        .copy();
            } else {
                element.split(newPara);
                if (bRegionContent) {
                    newPara.content[0].textProperty = this.getRegionContentTextPr(newPara)
                                                        .copy();
                }
            }

            contentPos = true === bAtBegin ? contentPos : this.curPos.contentPos + 1;
            this.addToContent(contentPos, newPara);
            this.curPos.contentPos++;
            newPara.setHidden(element.getHidden());

        } else if ( element.isTable() || element.isRegion() ) {
            element.addNewParagraph();
        }

        return ResultType.Success;
    }

    /**
     * 插入软回车
     * @returns
     */
    public addSoftNewParagraph(): number {
        let contentPos = this.curPos.contentPos;
        if ( 0 > contentPos ) {
            return ResultType.Failure;
        }

        // first: remove selection
        if ( true === this.selection.bUse ) {
            this.remove(1, true, false, true);
        }

        contentPos = this.curPos.contentPos;
        const curItem = this.content[contentPos];

        if ( !curItem ) {
            return ResultType.Failure;
        }

        if ( curItem instanceof Paragraph ) {
            const curPortion = curItem.getElementByPos(curItem.getParaContentPos(), true);
            curPortion.addParaItem(curPortion.portionContentPos, ParaElementType.ParaNewLine);
            ++curPortion.portionContentPos;
            return ResultType.Success;
            // return curItem.add(new ParaSoftLine());
        } else {
            return curItem.addSoftNewParagraph();
        }

        return ResultType.Failure;
    }

    public addInlineImage(width: number, height: number, src: string,
                          name?: string, type?: EquationType, svgElem?: any,
                          mediaType?: ImageMediaType, mediaSrc?: string, datas?: any): string {
        let currentDrawingName: string;
        if ( DocCurPosType.DrawingObjects === this.curPos.type ) {
            // todo
        } else {
            if ( true === this.selection.bUse ) {
                this.remove(1, true, false, true);
            }

            // console.log(src);

            const curItem = this.content[this.curPos.contentPos];

            // console.log(curItem.getType())
            if ( curItem.isParagraph() ) {
                const newControl = this.getCursorInNewControl();
                if ( newControl && newControl.isNumberBox()) {
                    return;
                }
                // 混排？
                // if (true === bFlow) {

                // }
                let imageObject = null;
                // console.log(equationProperties)
                if (name == null && type == null) {
                    if (!mediaType) {
                        if (datas) {
                            // 当前只有大网络图片
                            imageObject = new ParaBigDrawing(this, width, height, src,
                                undefined, undefined, undefined, undefined, datas);
                        } else {
                            imageObject = new ParaDrawing(this, width, height, src);
                        }
                    } else {
                        imageObject = new ParaMediaDrawing(this, width, height, null,
                                undefined, undefined, undefined, mediaType, mediaSrc);
                    }
                    // imageObject = new ParaDrawing(this, width, height, src);
                } else {
                    // imageObject = new ParaDrawing(this, width, height, src);
                    if (!datas) {
                        let svgElemStr: string;
                        if (typeof svgElem === 'string') {
                            svgElemStr = svgElem;
                        } else {
                            svgElemStr = new XMLSerializer().serializeToString(svgElem);
                        }
                        // const svgElemStr = new XMLSerializer().serializeToString(svgElem);
                        imageObject = new ParaEquation(this, width, height, src, name, type, svgElemStr);
                    } else {
                        imageObject = new ParaBarcode(this, width, height, src, undefined, name, datas);
                    }
                }

                
                curItem.getElementByPos(curItem.getParaContentPos(), true);
                // ++++++++++++++++++++++++++++
                // add imageObject to whole image array
                // if (imageObject) {
                //     this.getDrawingObjects().addGraphicObject(imageObject);
                // }

                // add image selection
                const pointX = this.curPos.x;
                const pointY = this.curPos.y;
                const contentPos = this.getContentPosByXY(this.curPage, pointX, pointY);
                // const pageIndex = this.getElementPageIndexByXY(contentPos, pointX, pointY, this.curPage);
                const element = this.content[contentPos];

                // const startPos = element.getParaContentPos();
                currentDrawingName = imageObject.getDrawingName();

                this.addToParagraph(imageObject);

                // focus
                // IMAGE_FLAGS.isImageOnClick = true;
                const doc = this.getDocument();
                doc.setImageSelectionState(true);

                // console.log(startPos, contentPos, element)

                // this.setImageSelection(startPos, contentPos, element, false);
                (imageObject as ParaDrawing).selectTopPos();

            } else {
                currentDrawingName = curItem.addInlineImage(width, height, src, name, type, svgElem, mediaType, mediaSrc, datas);
            }
        }
        return currentDrawingName;
    }

    /**
     * 添加DocumentContent
     * @param otherContent
     */
    public addContent(otherContent: DocumentContent): void {
        this.addContentElements(otherContent.content);
    }

    /**
     * 添加table，paragraphs 等内容数组
     * @param elements
     */
    public addContentElements(elements: DocumentContentElementBase[]): void {
        if ( !elements || 0 >= elements.length) {
            return;
        }

        if ( 0 >= this.content.length || true === this.isEmpty() ) {
            if ( 0 < this.content.length ) {
                this.removeAllContent(false);
            }

            for (let index = 0, count = elements.length; index < count; index++) {
                const element = elements[index];
                this.addToContent(index, element);
            }
        } else {
            this.content[this.content.length - 1].setDocumentNext(elements[0]);
            elements[0].setDocumentPrev(this.content[this.content.length - 1]);

            for (let index = 0, count = elements.length; index < count; index++) {
                const element = elements[index];
                this.addToContent(this.content.length, element);
            }
        }
    }

    public addContentChanges( changes: ContentChangesElement ): void {
        this.contentChanges.add(changes);
    }

    public addToParagraph(paraItem: ParaElementBase, bRecal?: boolean): number {
        let result: number;
        if (true === this.bApplyToAll) {
            if (ParaElementType.ParaTextPr === paraItem.type) {
                let res: number = ResultType.UnEdited;
                for (let index = 0, length = this.content.length; index < length; index++) {
                    const content = this.content[index];
                    content.setApplyToAll(true);
                    content.resetParagraphCursorPos();
                    res = content.addToParagraph(paraItem) && res;
                    content.setApplyToAll(false);
                }
                result = res;
            }

            return result;
        }

        if ( true === this.selection.bUse ) {
            switch ( paraItem.type ) {
                case ParaElementType.ParaText:
                case ParaElementType.ParaSpace:
                case ParaElementType.ParaTab: {
                    this.remove(1, true, false, true);
                    break;
                }

                case ParaElementType.ParaTextPr: {
                    let startPos = this.selection.startPos;
                    let endPos = this.selection.endPos;

                    // 选中区域是否是同一个portion
                    if ( startPos === endPos ) {
                        result = this.content[startPos].addToParagraph(paraItem);
                    } else {
                        let direction = 1; // 1: 向下选择，-1：向上选择
                        if ( startPos > endPos ) {
                            const temp = endPos;
                            endPos = startPos;
                            startPos = temp;
                            direction = -1;
                        }
                        let res: number = ResultType.UnEdited;
                        for ( let curPos = startPos; curPos <= endPos; curPos++ ) {
                            res = this.content[curPos].addToParagraph(paraItem) && res;
                        }
                        result = res;
                    }
                    this.recalculate();

                    return result;
                }
                default:
                    break;
            }
        }

        const element = this.content[this.curPos.contentPos];

        if ( false === this.selection.bUse && paraItem.isParaTab() ) {
            element.getElementByPos(element.getParaContentPos(), true);
        }
        result = element.addToParagraph(paraItem);

        if (false !== bRecal) {
            this.recalculate();

            if ( element.isParagraph() ) {
                element.recalculateCurPos();
                const curPos = element.getCurPos();
                this.curPos.realX = curPos.x;
                this.curPos.realY = curPos.y;
            }
        }

        return result;
    }

    public isEmpty(): boolean {
        if ( 1 < this.content.length || DocumentContentType.Paragraph !== this.content[0].getType() ) {
            return false;
        }

        return this.content[0].isEmpty();
    }

    public getEmptyHeight(): number {
        const count = this.content.length;
        if ( 0 >= count ) {
            return 0;
        }

        const element = this.content[count - 1];

        if ( element.isParagraph() ) {
            return element.getEmptyHeight();
        }

        return 0;
    }

    public getStartPos(pos?: ParagraphContentPos): void {
        pos.add(0);
        this.content[0].getStartPos(pos);
    }

    public getEndPos(bParaEnd: boolean, pos?: ParagraphContentPos): void {
        const num = this.content.length - 1;
        pos.add(num);
        this.content[num].getEndPos(bParaEnd, pos);
    }

    public clearContent(bRemoveNewControl: boolean = false): void {
        this.removeSelection();
        this.curPos.reset();

        this.selection.bUse  = false;
        this.removeAllContent(bRemoveNewControl);

        const para = new Paragraph(this, this.logicDocument);
        this.addToContent(0, para);

        this.recalculate();
    }

    public clearContent1(bRemoveNewControl: boolean = false): void {
        this.removeSelection();
        this.curPos.reset();

        this.selection.bUse  = false;
        this.removeAllContent(bRemoveNewControl);

        const para = new Paragraph(this, this.logicDocument);
        this.addToContent(0, para);
    }

    public recalculateMinmaxContentWidth(bRotated: boolean): { Min: number, Max: number } {
        let min = 0;
        let max = 0;
        const length = this.content.length;

        if ( true === bRotated ) {
            for (let index = 0; index < length; index++) {
                const element = this.content[index];
                const curMinMax = element.recalculateMinmaxContentWidth(bRotated);

                min += curMinMax.Min;
                max += curMinMax.Max;
            }
        } else {
            for (let index = 0; index < length; index++) {
                const element = this.content[index];
                const curMinMax = element.recalculateMinmaxContentWidth(bRotated);

                if ( min < curMinMax.Min ) {
                    min = curMinMax.Min;
                }

                if ( max < curMinMax.Max ) {
                    max = curMinMax.Max;
                }
            }
        }

        return { Min: min, Max: max };
    }

    public isTableFirstRowOnNewPage(): boolean {
        if (this.parent && this.parent instanceof TableCell) {
            return this.parent.isTableFirstRowOnNewPage();
        }

        return false;
    }

    public isRegionFirstOnNewPage(): boolean {
        if (this.parent && this.parent instanceof Region) {
            return this.parent.isRegionFirstOnNewPage();
        }

        return false;
    }

    public copy(parent: TableCell, option?: any): DocumentContent {
        const content = new DocumentContent(parent, parent.row.table.logicDocument, 0, 0, 0, 0, this.bSplit);
        content.removeAllContent();
        let curIndex = 0;
        for (let index = 0, count = this.content.length; index < count; index++) {
            const element = this.content[index].copy(content, option);
            if (element == null) {
                continue;
            }
            content.addToContent(curIndex, element);
            curIndex++;
        }

        // 假如所有的段落都被删除，这里是有问题，直接返回一个空段落
        if (content.content.length === 0) {
            content.addToContent(0, new Paragraph(this, this.logicDocument));
        }

        return content;
    }

    public copy2(otherContent: DocumentContent, bFlag?: boolean, option?: any): void {
        this.removeAllContent();
        let curIndex = 0;
        for (let index = 0, count = otherContent.content.length; index < count; index++) {
            const element = otherContent.content[index].copy(this, option);
            if (element == null) {
                continue;
            }

            this.addToContent(curIndex, element, bFlag);
            curIndex++;
        }
        // 假如所有的段落都被删除，这里是有问题，直接返回一个空段落
        if (this.content.length === 0) {
            this.addToContent(0, new Paragraph(this, this.logicDocument));
        }
    }

    public setParagraphAlignment(alignment: number): boolean {
        if (true === this.bApplyToAll) {
            for (let index = 0, length = this.content.length; index < length; index++) {
                const element = this.content[index];
                element.setApplyToAll(true);
                element.setParagraphAlignment(alignment);
                element.setApplyToAll(false);
            }

            return true;
        }

        if (true === this.selection.bUse) {
            let startPos = this.selection.startPos;
            let endPos = this.selection.endPos;

            if (startPos > endPos) {
                const pos = startPos;
                startPos = endPos;
                endPos = pos;
            }
            let flag: boolean = false;
            for (let index = startPos; index <= endPos; index++) {
                const element = this.content[index];
                flag = element.setParagraphAlignment(alignment) || flag;
            }
            return flag;
        } else {
            const element = this.content[this.curPos.contentPos];
            return element.setParagraphAlignment(alignment);
        }
    }

    public setParagraphProperty(paraProperty: any): boolean {
        let result = false;
        if (true === this.bApplyToAll) {
            for (let index = 0, length = this.content.length; index < length; index++) {
                const element = this.content[index];
                element.setApplyToAll(true);
                result = element.setParagraphProperty(paraProperty);
                element.setApplyToAll(false);
            }

            return result;
        }

        if (true === this.selection.bUse) {
            let startPos = this.selection.startPos;
            let endPos = this.selection.endPos;

            if (startPos > endPos) {
                const pos = startPos;
                startPos = endPos;
                endPos = pos;
            }

            for (let index = startPos; index <= endPos; index++) {
                const element = this.content[index];
                result = element.setParagraphProperty(paraProperty) || result;
            }
            return result;
        } else {
            const element = this.content[this.curPos.contentPos];
            return element.setParagraphProperty(paraProperty);
        }
    }

    /**
     * 获取当前段落属性
     */
    public getParagraphProperty(): ParaProperty {
        const elementPos = this.curPos.contentPos;
        if ( this.content[elementPos] ) {
            return this.content[elementPos].getParagraphProperty();
        }

        return undefined;
    }

    /**
     * 对页面进行排版
     * @param bAsync 是否懒加载
     */
    public recalculate(bAsync?: boolean): void {
        if ( !this.logicDocument.isInHeaderFooter() ) {
            const topParent = this.getTopDocument();
            if ( topParent.isHeaderFooter(false) ) {
                const headerFooter = topParent.isHeaderFooter(true);
                if ( headerFooter && headerFooter instanceof HeaderFooter ) {
                    headerFooter.recalcType = headerFooter.type;
                }
            }
        }

        if (bAsync === true) {
            return this.logicDocument.recalculateAsync();
        }

        this.logicDocument.recalculate();
    }

    public getNewControlManager(): NewControlManager {
        if ( this.logicDocument instanceof Document ) {
            return this.logicDocument.getNewControlManager();
        }

        return null;
    }

    public addNewControl(property: INewControlProperty, sText?: string): number {
        const result = this.addNewControlInternal(property, sText);

        if ( ResultType.Success !== result ) {
            return result;
        }

        if ( true === this.selection.bUse ) {
            this.removeSelection();
        }

        // this.recalculate();
        // this.updateCursorXY();

        return result;
    }

    // public isCursorInNewControl(): boolean {
    //     let contentPos = this.curPos.contentPos;

    //     if ( true === this.selection.bUse ) {
    //         contentPos = this.selection.endPos;
    //     }

    //     return this.content[contentPos].isCursorInNewControl();
    // }

    // public getCursorInNewControl(): NewControl {
    //     if ( true === this.isCursorInNewControl() ) {
    //         let contentPos = this.curPos.contentPos;

    //         if ( true === this.selection.bUse ) {
    //             contentPos = this.selection.endPos;
    //         }

    //         return this.content[contentPos].getCursorInNewControl();
    //     }

    //     return null;
    // }

    public isFocusInNewControl(pointX: number, pointY: number, pageIndex: number): boolean {
        if ( 0 > pageIndex || pageIndex >= this.pages.length ) {
            return false;
        }

        const contentPos = this.getContentPosByXY(pageIndex, pointX, pointY);
        const elementIndex = this.getElementPageIndexByXY(contentPos, pointX, pointY, pageIndex);
        return this.content[contentPos].isFocusInNewControl(pointX, pointY, elementIndex);
    }

    public getFocusInNewControl(pointX: number, pointY: number, pageIndex: number): NewControl {
        const contentPos = this.getContentPosByXY(pageIndex, pointX, pointY);
        const elementIndex = this.getElementPageIndexByXY(contentPos, pointX, pointY, pageIndex);
        return this.content[contentPos].getFocusInNewControl(pointX, pointY, elementIndex);
    }

    public getNewControlBounds(newControl: NewControl): IDrawNewControlBounds {
        const renderBounds: IDrawNewControlBounds = {
            bounds: [],
            newControlName: null,
            color: null,
        };

        if ( null != newControl ) {
            const name = newControl.getNewControlName();
            renderBounds.newControlName = name;
            renderBounds.color = newControl.getFocusHighLightColor();

            const startPos = newControl.getStartPos();
            const endPos = newControl.getEndPos();
            if (!startPos || !endPos) {
                return renderBounds;
            }

            // if ( newControl.getDocumentParent() instanceof DocumentContent ) {
            const depth = startPos.getDepth();
            const startParaPos = startPos.get(depth - 2);
            const endParaPos = endPos.get(depth - 2);

            if ( startParaPos !== endParaPos ) {
                let bounds = this.content[startParaPos].
                        getNewControlParaBounds(name, startPos.get(depth - 1), null);

                renderBounds.bounds = renderBounds.bounds.concat(bounds);

                for (let index = startParaPos + 1; index < endParaPos; index++) {
                    bounds = this.content[index].getNewControlParaBounds(name, null, null);
                    renderBounds.bounds = renderBounds.bounds.concat(bounds);
                }

                bounds = this.content[endParaPos].
                        getNewControlParaBounds(name, null, endPos.get(depth - 1));
                renderBounds.bounds = renderBounds.bounds.concat(bounds);
            } else {
                const bounds = this.content[startParaPos].
                        getNewControlParaBounds(name, startPos.get(depth - 1), endPos.get(depth - 1));
                renderBounds.bounds = renderBounds.bounds.concat(bounds);
            }
            // }
        }

        // console.log(renderBounds)
        return renderBounds;
    }

    public getNewControlParaBounds(newControlName?: string, start?: number, end?: number): IDrawSelectionBounds {
        const result = {
            lines: [],
            cells: [],
            direction: this.getSelectionDirection(),
        };

        for (const element of this.content) {
            if ( DocumentContentType.Table === element.getType() ) {
                result.lines = result.lines.concat(element.getNewControlParaBounds(undefined, start, end));
            } else if (DocumentContentType.Region === element.getType()) {
                result.lines = result.lines.concat(element.getNewControlParaBounds(newControlName, start, end));
            } else {
                result.lines = result.lines.concat(element.getSelectedLines(null));
            }
        }

        return result;
    }

    // public getNewControlText(): string {
    //     let text = '';

    //     for (let index = 0, length = this.content.length; index < length; index++) {
    //         const element = this.content[index];
    //         text += element.getNewControlText(null, null);
    //     }

    //     return text;
    // }

    public getAllNewControls(): NewControl[] {
        let result = [];
        for (let index = 0, len = this.content.length; index < len; index++) {
            const element = this.content[index];
            result = result.concat(element.getAllNewControls());
        }
        return result;
    }

    public getSelectBehindOverNewControls(): NewControl[] {
        return this.logicDocument.getSelectBehindOverNewControls(this);
    }

    public getSelectForwardOverNewControls(): NewControl[] {
        return this.logicDocument.getSelectForwardOverNewControls(this);
    }

    public getParent(): TableCell | HeaderFooter | Region {
        return this.parent;
    }

    // Special function used in headers and footers to add page numbers (in which page?)
    // This removes all paragraphs. Two new ones are added.
    public headerFooterAddPageNum(alignment: AlignType, styleId?: number): void { // 插入页码
      this.removeSelection();

      this.curPos = new CurPos(); // object literal in onlyoffice

      this.selection.bUse = false;

      this.removeAllContent();

      const para1 = new Paragraph(this, this.logicDocument);
      // let para2 = new Paragraph(this);

      this.addToContent(0, para1);
      // this.addToContent(1, para2);

      para1.setDocumentPrev(null);
      para1.setDocumentNext(null);
      // para2.setDocumentPrev(para1);
      // para2.setDocumentNext(null);

      // Para1.Style_Add(StyleId);
      para1.setParagraphAlignment(alignment);
      const portion = new ParaPortion(para1);
      portion.addToContent(0, new ParaPageNum(this.logicDocument, this.logicDocument.curPage + 1));
      para1.addToContent(0, portion);

      this.recalculate();
    }

    // custom add pageNum: can add in header, footer and document
    // think of it as image
    public addPageNum(): void {
        // TODO
    }

    public updateCursorType(pointX: number, pointY: number, curPage: number): void {
        if ( 0 > curPage || curPage >= this.pages.length ) {
            return this.setCursorType(CursorType.Text);
        }

        // const bTableBorder = (null !== this.isTableBorder(pointX, pointY, this.curPage) ? true : false);
        const contentPos = this.getContentPosByXY(curPage, pointX, pointY);
        const elementIndex = this.getElementPageIndexByXY(contentPos, pointX, pointY, curPage);
        this.content[contentPos].updateCursorType(pointX, pointY, elementIndex);
    }

    public setCursorType(type: CursorType): void {
        this.logicDocument.setCursorType(type);
    }

    public addTableRow(bBefore: boolean): boolean {
      return this.addTableRowController(bBefore);
    }

    public removeTableRow(rowIndex?: number): boolean {
        return this.removeTableRowController(rowIndex);
    }

    public removeTableColumn(): boolean {
        return this.removeTableColumnController();
    }

    public mergeTableCells(bClearMerge?: boolean): boolean {
        return this.mergeTableCellsController(bClearMerge);
    }

    public removeTable(): boolean {
        return this.removeTableController();
    }

    public getSummaryHeight(): number {
        let height = 0;
        const pagesCount = this.pages.length;
        for (let page = 0; page < pagesCount; page++) {
            const bounds = this.getPageBounds(page);
            height += bounds.bottom - bounds.top;
        }

        return height;
    }

    public isHeaderFooter(bReturnHeaderFooter: boolean): boolean | HeaderFooter {
        if (this.parent instanceof HeaderFooter) {
            return (bReturnHeaderFooter ? this.parent : true);
        } else {
            return (bReturnHeaderFooter ? null : false);
        }
    }

    public isHeaderFooterContent(): boolean {
        let parent;
        parent = this.parent;
        while (parent) {
            if (parent instanceof HeaderFooter) {
                return true;
            } else if (parent instanceof TableCell) {
                parent = this.getTable()?.parent;
            } else if (parent instanceof Region) {
                parent = this.getRegion()?.parent;
            } else {
                return false;
            }
        }

        return false;
    }

    /**
     * 指定位置插入新对象
     * @param nPos 插入位置
     * @param newObj 插入对象
     */
    public addToContent(nPos: number, newObj: DocumentContentElementBase, unEmptyPara?: boolean): void {
        if (nPos < 0 || nPos > this.content.length) {
            return;
        }

        const prevObj = this.content[nPos - 1] ? this.content[nPos - 1] : null;
        const nextObj = this.content[nPos] ? this.content[nPos] : null;

        const history = this.getHistory();
        if ( history && history.canAdd() ) {
            history.addChange(new ChangeDocumentContentAddItem(this, nPos, [newObj]));

            const tableManager = this.getTableManager();
            if ( newObj.isTable() && tableManager ) {
                history.addChange(new ChangeTableAddTableName(newObj as TableBase));
            }
        }
        this.content.splice(nPos, 0, newObj);
        newObj.setParent(this);
        newObj.setDocumentPrev(prevObj);
        newObj.setDocumentNext(nextObj);

        if (null != prevObj) {
            prevObj.setDocumentNext(newObj);
        }

        if (null != nextObj) {
            nextObj.setDocumentPrev(newObj);
        }

        if ( unEmptyPara !== true && DocumentContentType.Table === this.content[this.content.length - 1].getType() ) {
            this.addToContent(this.content.length, new Paragraph(this, this.logicDocument));
        }
    }

    /**
     * 设置文档脏标记
     * @param bDirty 默认为true：已被修改
     */
    public setDirty( bDirty: boolean = true ): void {
        this.logicDocument.setDirty(bDirty);
        if (this.isRegionContent()) {
            (this.parent as Region).setDirty(bDirty);
        }
    }

    public contentRemove( nPos: number, count: number, bCheckLastElement?: boolean, bNotDeleteGrf?: boolean ): boolean {
        if ( nPos < 0 || nPos > this.content.length || count <= 0 ) {
            return false;
        }

        const prevObj = this.content[nPos - 1] ? this.content[nPos - 1] : null;
        const nextObj = this.content[nPos + count] ? this.content[nPos + count] : null;

        const history = this.getHistory();
        if ( history ) {
            history.addChange(new ChangeDocumentContentRemoveItem(this, nPos, this.content.slice(nPos, nPos + count)));
        }
        const delItems = this.content.splice(nPos, count);

        const tables = [];
        const tableManager = this.getTableManager();
        const numManager = this.logicDocument.getNumManager();
        if ( true !== bNotDeleteGrf ) {
            for (const element of delItems) {
                const bTable = (element.isTable()) ? true : false;
                const bRegion = (element.isRegion()) ? true : false;

                if (bRegion === false) {
                    if (!bTable) {
                        numManager.removeLvlByPara(element);
                    }
                    this.getDrawingObjects()
                        .deleteImageFromParaTableRemoval(element.id, bTable);
                } else {
                    if (element instanceof Region) {
                        this.getDrawingObjects()
                            .deleteImageFromRegionRemoval(element);
                    }
                }

                if ( bTable ) {
                    tables.push(element);
                    tableManager.removeTableNameByTable(element as TableBase);
                }
            }

            const tableMap = tableManager.getTableMap();
            tableMap.forEach((table) => {
                if ( table && table.parent.isRegionContent() ) {
                    const region = table.getRegion();
                    for (const element of delItems) {
                        if ( element && element === region ) {
                            tables.push(table);
                            tableManager.removeTableNameByTable(table);
                        }
                    }
                }
            });

            if ( 0 < tables.length && history && tableManager ) {
                history.addChange(new ChangeTableRemoveTableNames(tableManager, 0, tables));
            }
        }

        if (null != prevObj) {
            prevObj.setDocumentNext(nextObj);
        }

        if (null != nextObj) {
            nextObj.setDocumentPrev(prevObj);
        }

        if ( false !== bCheckLastElement && (0 >= this.content.length ||
            DocumentContentType.Table === this.content[this.content.length - 1].getType()) ) {
            this.addToContent(this.content.length, new Paragraph(this, this.logicDocument));
        }

        return true;
    }

    public setTableProps( props: ITableProperty ): boolean {
        if ( true === this.bApplyToAll ) {
            return false;
        }

        if ( DocCurPosType.Content === this.curPos.type ) {
            let pos = -1;

            if ( true === this.selection.bUse ) {
                pos = this.selection.startPos;
            } else if ( false === this.selection.bUse ) {
                pos = this.curPos.contentPos;
            }

            if ( -1 !== pos ) {
                return this.content[pos].setTableProps(props);
            }
        }

        return false;
    }

    public getTableManager(): TableManager {
        return this.logicDocument ? this.logicDocument.getTableManager() : null;
    }

    public getDrawingObjects(): GraphicObjects {
        return this.logicDocument ? this.logicDocument.getDrawingObjects() : null;
    }

    public setDrawingObjects(drawingObjects: GraphicObjects): void {
        if ( this.logicDocument ) {
            this.logicDocument.setDrawingObjects(drawingObjects);
        }
    }

    public getHistory(): History {
        return this.logicDocument ? this.logicDocument.getHistory() : null;
    }

    public removeAllNewControl(bRemoveNewControl: boolean = true): void {
        if ( (this.isTableCellContent() || this.isRegionContent()) && bRemoveNewControl
            && !this.logicDocument.bRecalcTableHeader ) {
            const newControlManager = this.getNewControlManager();

            if ( newControlManager ) {
                this.selectAll();

                const startPos = this.getStartRemoveSelectionPos();
                const endPos = this.getEndRemoveSelectionPos();

                // const history = this.getHistory();
                // let turnOn = false;
                // if ( history ) {
                //     turnOn = history.isTurnOn();
                //     history.turnOff();
                // }

                newControlManager.remove(startPos, endPos);

                // if ( history && turnOn ) {
                //     history.turnOn();
                // }
                // this.setApplyToAll(false);
            }
        }
    }

    public isTrackRevisions(): boolean {
        return this.logicDocument ? this.logicDocument.isTrackRevisions() && !this.isHeaderFooterContent() : false;
    }

    public getSelectedContentFlag(): boolean {
        return this.logicDocument ? this.logicDocument.getSelectedContentFlag() : false;
    }

    public isCursorAtBegin(): boolean {
        if (false !== this.selection.bUse || 0 !== this.curPos.contentPos ) {
            return false;
        }

        return this.content[0].isCursorAtBegin();
    }

    public isCursorAtEnd(): boolean {
        if (false !== this.selection.bUse || this.content.length - 1 !== this.curPos.contentPos ) {
            return false;
        }

        return this.content[this.curPos.contentPos].isCursorAtEnd();
    }

    // public canAddNewParagraphByEnter(): boolean {
    //     if ( this.isRegion() ) {
    //         ;
    //     } else {
    //         return super.canAddNewParagraphByEnter();
    //     }
    // }

    public getRevisionsManager(): RevisionsManager {
        return this.logicDocument ? this.logicDocument.getRevisionsManager() : null;
    }

    public getAllRevision(): IRevisionChange[] {
        const res = [];
        for (const item of this.content) {
            res.push(...item.getAllRevision());
        }
        return res;
    }

    public getFocusRevision(pointX: number, pointY: number, pageIndex: number): IRevisionChange[] {
        const contentPos = this.getContentPosByXY(pageIndex, pointX, pointY);
        if ( contentPos === undefined ) {
            return null;
        }

        const elementIndex = this.getElementPageIndexByXY(contentPos, pointX, pointY, pageIndex);
        return this.content[contentPos].getFocusRevision(pointX, pointY, elementIndex);
    }

    public getContentPosByXY(pageIndex: number, pointX: number, pointY: number): number {

        pageIndex = Math.max(0, Math.min(pageIndex, this.pages.length - 1));
        let page = this.pages[pageIndex] || {pos: 0};
        const startPos = Math.min(page.pos, this.content.length - 1);
        let endPos = this.content.length - 1;

        if ( pageIndex < this.pages.length - 1 ) {
            page = this.pages[pageIndex + 1];
            if (page) {
                endPos = Math.min(page.pos, endPos);
            }
        }

        // inline元素
        const inlineElements = [];
        for (let index = startPos; index <= endPos; index++) {
            const element = this.content[index];
            if (!element || element.isHidden()) {
                continue;
            }
            const bEmptySectPara = this.pages[pageIndex].checkEndSectionPara(element);

            if ( false !== element.isInline()
                && ( DocumentContentType.Paragraph !== element.getType() || false === bEmptySectPara )
                && pageIndex >= element.pageNum ) {
                inlineElements.push(index);
            }
        }

        const count = inlineElements.length;
        if ( 0 >= count ) {
            return startPos;
        }

        for (let index = 0; index < count - 1; index++) {
            const element = this.content[inlineElements[index + 1]];
            const pageBounds = element.getPageBounds(0);

            // 在当前元素top之上
            if ( pointY < pageBounds.top ) {
                return inlineElements[index];
            }

            // 当前元素跨页
            if ( 1 < element.getPages().length ) {
                // 判断元素在开始页面上是否有内容存在
                if ( true !== element.isStartFromNewPage() ) {
                    return inlineElements[index + 1];
                }

                return inlineElements[index];
            }

            // 只有最后一个元素没有比较，此时，无需比较坐标，直接返回最后一个
            if ( index === count - 2 ) {
                return inlineElements[count - 1];
            }
        }

        return inlineElements[0];
    }

    public saveRecalculateObject(): DocumentRecalcObject {
        const recalcObj = new DocumentRecalcObject();
        recalcObj.save(this);
        return recalcObj;
    }

    public loadRecalculateObject(recalcObj: DocumentRecalcObject): void {
        recalcObj.load(this);
    }

    public getPages(): DocumentPage[] {
        return this.pages;
    }

    public filterContentNodes(type: number): DocumentContentElementBase[] {
        const contents = this.content;
        let content = [];
        const bRegion = this.isRegionContent();
        const doc = this.logicDocument;
        contents.forEach((item, index) => {
            if (bRegion && index === 0) {
                const res = this.removeRegionTitle();
                if (res && contents.length > 1 && item.isEmpty()) {
                    return;
                }
            }
            const bCurrentRegion = item.isRegion();
            if (!bCurrentRegion && type === NeedStructType.NoRegion) {
                content.push(item);
                item.setParent(doc);
            } else if (type === NeedStructType.All) {
                // 保留所有结构，直接添加
                content.push(item);
                item.setParent(doc);
            } else {
                const res = item.filterContentNodes(type);
                if (res.length) {
                    content = content.concat(res);
                    if (bCurrentRegion) {
                        res.forEach((data) => {
                            data.setParent(doc);
                        });
                    }
                }
            }
        });

        return content; // this.filterContentNodes(type);
    }

    // public getCurrentTable(): Table {
    //     if ( this.selection.bUse ) {
    //         if ( this.selection.startPos === this.selection.endPos ) {
    //             return this.content[this.selection.startPos].getCurrentTable();
    //         }
    //     } else {
    //         return this.content[this.curPos.contentPos].getCurrentTable();
    //     }

    //     return null;
    // }

    /**
     * 删除元素初始化时默认内容(只发现为空段落的情况)
     */
    public removeDefaultContent(): void {
        this.content = [];
    }

    public getElementById(elementId: number): DocumentContentElementBase {
        for (let index = 0, length = this.content.length; index < length; index++) {
            let element = this.content[index];
            if ( element ) {
                element = element.getElementById(elementId);

                if ( element ) {
                    return element;
                }
            }
        }

        return null;
    }

    public getContentByPos(pos: any): any {
        const index = pos.shift();
        if ( this.content[index] ) {
            return this.content[index].getContentByPos(pos);
        }

        return null;
    }

    public write(bAdd: boolean): any {
        // const pos = this.get();
        // pos.splice(pos.getDepth() - 1, 2);
        // const newControl = (false === bAdd ? this.getCursorInNewControl(): null);

        return {
            // pos: pos.data.toString(),
            selection: this.selection,
        };
    }

    public getContentForFormula(): number {
        let text;
        const newControl = this.getFirstNewControlInTableCell(false);
        if ( newControl ) {
            text = newControl.getNewControlText();
        } else {
            text = (this.content[0] as Paragraph).getContentText();
        }

        return parseFloat(text);
    }

    public setContentForFormula(result: string): void {
        const newControl = this.getFirstNewControlInTableCell(undefined);
        if ( newControl && !newControl.isSignatureBox() ) {
            if (newControl.isNumberBox()) {
                (newControl as NewControlNumer).setNumberBoxText(+result, false, true);
            } else {
                newControl.setNewControlText2(result);
            }
        } else {
            this.setApplyToAll(true);
            this.remove(-1, true, true, false);
            this.setApplyToAll(false);

            const portion = new ParaPortion(undefined);
            portion.addText(result);
            this.content[0].addToContent(0, portion);
        }
    }

    public getFirstNewControlInTableCell(bSection: boolean): NewControl {
        let newControl;

        for (let index = 0, length = this.content.length; index < length; index++) {
            const element = this.content[index];
            if ( element && element instanceof Paragraph ) {
                newControl = element.getFirstNewControl(bSection);
                if ( newControl ) {
                    break;
                }
            }
        }

        return newControl;
    }

    public setImageSelection(startPos: ParagraphContentPos, contentPos: number,
                             element: DocumentContentElementBase, isMousdown?: boolean): void {
        if (this.curPos.type !== DocCurPosType.DrawingObjects) {
            this.removeSelection();
        }

        // this.DrawingDocument.TargetEnd();
        // this.DrawingDocument.SetCurrentPage(this.CurPage);

        this.selection.bUse = true;
        this.selection.bStart = false;
        this.selection.flag = SelectionFlagType.Common;
        // this.setDocPosType(DocCurPosType.DrawingObjects);

        // console.log(circularParse(circularStringify(this)));

        // ------ caution: autonomous -------
        // get para index and set startPos of selection
        this.selection.startPos = contentPos; // this.getContentPosByXY(this.curPage, pointX, pointY);

        // endPos is the same as startPos when mouse down
        this.selection.endPos = contentPos;
        this.curPos.contentPos = contentPos;

        // element(para) should always be correct
        // element.setSelectionStart(pointX, pointY, pageIndex);
        // element.setSelectionEnd(pointX, pointY, pageIndex);

        // console.log(circularParse(circularStringify(element)));
        // console.log(element)
        // console.log(startPos)

        // console.log(element.getParaContentPos());
        // element.setSelectionContentPos();
        if (startPos) {
            element.setImageSelection(startPos);
        } else {
            // console.warn('startPos is null');
        }

        // console.log(circularParse(circularStringify(element)));
        // ------ caution: autonomous -------

        if (isMousdown === true) {
            this.logicDocument.getDrawingObjects()
                                .mouseButtonDown();
        //   console.log(circularParse(circularStringify(element)));
        }

        // console.log(element)

        // console.log(circularParse(circularStringify(this))); // portion is good
    }

    public setLogicDocument(document: Document): void {
        if ( document ) {
            this.logicDocument = document;
        }
    }

    /**
     * 设置护理单元格内容文本属性
     * @param props
     * @returns
     */
    public setCellContentProps(props: ITableCellContentProps, bDirty: boolean = true): boolean {
        let result = false;

        if ( props ) {
            const fontFamily = props.fontFamily;
            const fontSize = props.fontSize;
            const paraSpacing = props.paraSpacing;
            const alignType = props.alignType;

            const paraText = new ParaTextProperty();
            const textPro = paraText.textProperty;
            textPro.fontSize = fontSize;
            textPro.fontFamily = fontFamily;

            this.content.forEach((element) => {
                element.setApplyToAll(true);
                let paraPro;
                if (element.isParagraph()) {
                    const paraProperty = element.getParagraphProperty();
                    paraPro = {lineSpace: {type: paraSpacing}, alignment: alignType, indentation: paraProperty.paraInd};
                } else {
                    paraPro = {lineSpace: {type: paraSpacing}, alignment: alignType};
                }

                result = element.setParagraphProperty(paraPro) || result;
                result = (ResultType.Success === element.add2(paraText, bDirty)) || result;
                element.setApplyToAll(false);
            });
        }

        return result;
    }

    public getContentText(): string {
        let text = '';
        for (const element of this.content) {
            if ( element && element instanceof Paragraph ) {
                text += element.getContentText() || '';
            }
        }

        return text;
    }

    /**
     * 重置现有内容，并插入新的内容
     * @param element 新内容
     */
    public resetContentByText(text: string, props?: ITableCellContentProps): boolean {
        const para = new Paragraph(this, this.logicDocument);
        para.addText(text);

        this.removeAllContent(true);
        this.addToContent(0, para);

        this.setCellContentProps(props);
        return true;
    }

    public setSignCellText(creator: string, props?: ITableCellContentProps): boolean {
        if ( !this.isEmpty() || !creator ) {
            return false;
        }

        const para = this.content[0] as Paragraph;
        para.addText(creator);

        this.setCellContentProps(props);
        return true;
    }

    public async setSignContent2(sJson: any): Promise<boolean> {
        return new Promise((resolve) => {
            const content = sJson.signContent;
            const count = this.content.length;
            const bEmpty = this.isEmpty();

            switch (sJson.signType) {
                case 1:     // text
                case 2: {   // image
                    const para = this.content[0] as Paragraph;
                    const newPara = (bEmpty ? para : new Paragraph(this, this.logicDocument));

                    if ( !bEmpty ) {
                        newPara.paraProperty = para.paraProperty.copy();
                        this.addToContent(count, newPara);
                    }

                    newPara.paraProperty.nisSignAuthor = sJson.signAuthor;

                    if ( 1 === sJson.signType ) {
                        newPara.addText(content);

                        this.recalculate();
                        resolve(true);
                    } else if ( 2 === sJson.signType ) {
                        const size = {height: 0, width: 0};
                        const image = new Image();
                        image.src = content;
                        image.onload = () => {
                            const width = image.width;
                            const height = image.height;
                            size.height = height;
                            size.width = width;
                            if (size != null) {
                                const maxWidth = (this.parent as TableCell).getTableCellMaxWidth();
                                const maxHeight = (this.parent as TableCell).getTableCellMaxHeight();

                                if (size.width > maxWidth) {
                                    const wRatio = maxWidth / size.width;
                                    const hRatio = maxHeight / size.height;
                                    if (size.height * wRatio > maxHeight) {
                                        size.height = maxHeight;
                                        size.width *= hRatio;
                                    } else {
                                        size.width = maxWidth;
                                        size.height *= wRatio;
                                    }
                                } else if (size.height > maxHeight) {
                                    const wRatio = maxWidth / size.width;
                                    const hRatio = maxHeight / size.height;
                                    if (size.width * hRatio > maxWidth) {
                                        size.width = maxWidth;
                                        size.height *= wRatio;
                                    } else {
                                        size.height = maxHeight;
                                        size.width *= hRatio;
                                    }
                                }
                            }
                            const newDrawing = new ParaDrawing(this, size.width || 40, size.height || 40, content);
                            // newPara.add(newDrawing);
                            const portion = new ParaPortion(newPara);
                            portion.add(0, newDrawing);
                            portion.textProperty = para.content[0].textProperty.copy();
                            newPara.addToContent(0, portion);
                            this.recalculate();
                            resolve(true);
                        };
                    } else {
                        resolve(false);
                    }
                }
            }
        });
    }

    public deleteSignContentByAuthor(author: string): boolean {
        let res = false;
        for (let index = this.content.length - 1; index >= 0; index--) {
            const element = this.content[index];
            if ( element && element instanceof Paragraph && author === element.paraProperty.nisSignAuthor) {
                this.contentRemove(index, 1);
                res = true;
            }
        }

        return res;
    }

    public setSignContent(sJson: any): boolean {
        const content = sJson.signContent;
        const insertPos = sJson.signOrd;
        const count = this.content.length;

        if ( insertPos > count + 1 || 0 >= count ) {
            return false;
        }

        switch (sJson.signType) {
            case 1:     // text
            case 2: {   // image
                const para = this.content[0] as Paragraph;
                const newPara = new Paragraph(this, this.logicDocument);
                newPara.paraProperty = para.paraProperty.copy();
                const portion = new ParaPortion(newPara);
                portion.textProperty = para.content[0].textProperty.copy();

                if ( 1 === sJson.signType ) {
                    portion.addText(content);
                } else if ( 2 === sJson.signType ) {
                    const newDrawing = new ParaDrawing();
                    newDrawing.src = content;
                    portion.add(0, newDrawing);
                }

                newPara.addToContent(0, portion);
                this.addToContent(insertPos - 1, newPara);
                return true;
            }
        }

        return false;
    }

    public deleteSignContent(sJson: any): boolean {
        const deletePos = sJson.signOrd;
        const count = this.content.length;

        if ( deletePos > count || 0 >= count ) {
            return false;
        }

        this.contentRemove(deletePos - 1, 1);

        if ( 0 === this.content.length ) {
            const newPara = new Paragraph(this, this.logicDocument);
            this.addToContent(0, newPara);
        }

        this.recalculate();
        return true;
    }

    public refreshRecalData(data: any): void {
        let curPage = 0;

        switch (data.type) {
            case HistroyItemType.DocumentContentAddItem:
            case HistroyItemType.DocumentContentAddItem: {
                let pos = 0;
                if (data instanceof ChangeDocumentContentAddItem || data instanceof ChangeDocumentContentRemoveItem) {
                    pos = data.getMinPos();
                } else if (null != data.position) {
                    pos = data.position;
                }

                for (curPage = this.pages.length - 1; curPage > 0; curPage--) {
                    if (pos > this.pages[curPage].pos) {
                        break;
                    }
                    
                }
                break;
            }
            default:
                break;
        }

        this.refreshRecalData2(0, curPage);
    }

    public refreshRecalData2( index: number, pageIndex: number ): void {
        if (-1 !== index) {
            this.parent.refreshRecalData2(index, this.startPage + pageIndex);
        }
    }

    /**
     * 删除全部内容
     */
    protected removeAllContent(bRemoveNewControl: boolean = true): void {
        const count = this.content.length;

        for (let index = 0; index < count; index++) {
            this.content[index].preDelete();
        }

        this.removeAllNewControl(bRemoveNewControl);
        // gHistory.addChange(new ChangeDocumentContentRemoveItem(this, 0, this.content.slice(0, count)));
        const history = this.getHistory();
        if ( history ) {
            history.addChange(new ChangeDocumentContentRemoveItem(this, 0, this.content.slice(0, count)));
        }

        this.content = [];
    }

    private getElementPageIndex(elementIndex: number, curPage: number): number {
        const element = this.content[elementIndex];
        if ( !element ) {
            return 0;
        }

        const startPage = element.getRelativeStartPage();
        return curPage - startPage;
    }

    private getAbsolutePageIndex(curPage: number): number {
      if ('getAbsolutePage' in this.parent) {
        //   console.log(this.parent, curPage) // -> this.parent: HeaderFooter [content, id, ...]
        return this.parent.getAbsolutePage(this.getRelativePageIndex(curPage));
      }
      return -1;
    }

    private getRelativePageIndex(curPage: number): number {
        // console.log(this.startPage) // hdrftr: this.startPage = 2?????
        if (this.parent instanceof HeaderFooter) {
            // console.trace();
            // console.log(this.startPage)
            // TODO: true meaning of startPage?
            return (this.startPage < 0) ? 0 : this.startPage + curPage;
        } else {
            return this.startPage + curPage;
        }
    }

    private getElementPageIndexByXY(elementPos: number, pointX: number, pointY: number, curPage: number): number {
        return this.getElementPageIndex(elementPos, curPage);
    }

    /**
     * 获取段落的被选择的行数组
     * @param selections
     * @param bParaEnd
     */
    private getSelectedLines(startSelections?: ISelectionsPara,
                             endSelections?: ISelectionsPara): IDrawSelectionsLine[] {
        if ( true === this.selection.bUse && SelectionFlagType.Common === this.selection.flag ) {
            let startParaPos = this.selection.startPos;
            let endParaPos = this.selection.endPos;

            // compose selections[] (line id array)
            let selections = [];

            if ( startParaPos > endParaPos ) {
                const temp = startParaPos;
                startParaPos    = endParaPos;
                endParaPos      = temp;
            }

            const startElementType = this.content[startParaPos].getType();
            const bSelectMultiElements = ( startParaPos !== endParaPos ? true : false );

            if ( DocumentContentType.Paragraph === startElementType ) {
                const lines = this.content[startParaPos].getSelectedLines(startSelections, bSelectMultiElements);
                if ( !lines || 0 === lines.length ) {
                    return null;
                }

                selections = selections.concat(lines);
            }

            if (  bSelectMultiElements ) {

                for (let index = startParaPos + 1; index < endParaPos; index++) {
                    const element = this.content[index];
                    const elementType = element.getType();

                    if ( DocumentContentType.Paragraph === elementType ) {
                        const curLines = element.getSelectedLines(null);

                        selections = selections.concat(curLines);
                    }
                }

                const endElementType = this.content[endParaPos].getType();
                if ( DocumentContentType.Paragraph === endElementType ) {
                    selections = selections.concat(this.content[endParaPos].getSelectedLines(endSelections));
                }
            }

            if ( true === this.isSelectionEmpty() ) {
                this.selection.data = null;
                selections = null;
            }

            return selections;
        }

        return null;
    }

    private getElementIndex(pos: ParagraphContentPos): void {
        let parent: any = this.parent;
        if (this.isRegionContent()) {
            pos.add((parent as Region).index);
        } else if (this.isTableCellContent()) {
            const cell = parent as TableCell;
            pos.add(cell.index);
            pos.add(this.getTableRowIndex());
            pos.add(this.getTableIndex());
            parent = cell.row.table;
        }

        parent = parent.parent;
        if (parent instanceof DocumentContent) {
            parent.getElementIndex(pos);
        }
    }

    private isNotContainRegionTitle(region: Region): boolean {
        if (!region.isShowTitle()) {
            return true;
        }
        const content = region.getOperateContent();
        const contents = content.content;
        const selection = content.selection;
        let start = selection.startPos;
        let end = selection.endPos;
        if (start > end) {
            const temp = start;
            start = end;
            end = temp;
        }
        if (start !== 0 || start === 0 && end === contents.length - 1 && content.isSelectedAll()) {
            return true;
        }
        const titlePara = contents[0];
        if (!(titlePara instanceof Paragraph)) {
            return true;
        }
        const paraSelection = titlePara.selection;
        start = paraSelection.startPos;
        end = paraSelection.endPos;
        let direction = 1;
        if (start > end) {
            const temp = start;
            start = end;
            end = temp;
            direction = -1;
        }
        // 选中段落的第一个portion就是标题portion
        if (start === 0) {
            if (end !== 0) {
                const portion = titlePara.content[start];
                const portionSelection = portion.selection;
                let portionContentPos = portionSelection.startPos;
                if (direction === -1) {
                    portionContentPos = portionSelection.endPos;
                }
                if (portionContentPos === portion.content.length && !portion.isEmpty(false)) {
                    return true;
                }
            }

            return false;
        } else {
            // 在某种特殊情况下，比如第一个portion为批注时会出现这个问题
            const titlePortion = region.getTitlePortion();
            if (titlePortion === titlePara.content[start] || titlePortion === titlePara.content[end]) {
                return false;
            }
        }
        return true;
    }

    private removeRegionTitle(): boolean {
        const parent = this.parent as Region;
        if (parent.isShowTitle3()) {
            const para = this.content[0] as Paragraph;
            if (!para.isParagraph()) {
                return false;
            }
            para.contentRemove2(0, 1);
            return true;
        }

        return false;
    }

    /**
     * @param textPros: 属性名称集合
     */
    private getTextProperty(textPros: ITextProperty): void {
        const selection = this.selection;
        if (!selection.bUse) {
            return;
        }

        let startPos = selection.startPos;
        let endPos = selection.endPos;
        const contents = this.content;
        // let dir = this.getSelectionDirection();
        if (startPos > endPos) {
            const pos = startPos;
            startPos = endPos;
            endPos = pos;
        }

        for (let i = startPos; i <= endPos; i++) {
            const para = contents[i];
            const flag = para.getSelectedTextProperty(textPros);
            if (flag === true) {
                return;
            }
        }
    }

    private getRegionContentTextPr(para: Paragraph): any {
        const textPr = new ParaPortion(para).textProperty;
        return (!this.logicDocument.getRegionContentDefaultFont() ? textPr :
                                this.logicDocument.getRegionContentDefaultFont());
    }
}

export class DocumentRecalcObject {
    public startPage: number;
    public pages: DocumentPage[];
    public recalcContent: ParagraphRecalcObject[];
    // public clipInfo: [];

    constructor(doc?: any) { // documentContent?
        this.startPage = 0;
        this.pages    = [];
        this.recalcContent  = [];
        // this.clipInfo = [];
    }

    public save(doc: DocumentContent): void { // documentContent?
        this.startPage = doc.startPage;
        this.pages     = doc.pages;
        // this.clipInfo  = doc.clipInfo;

        const content = doc.content;
        const count = content.length;
        for (let index = 0; index < count; index++) {
            this.recalcContent[index] = content[index].saveRecalculateObject();
        }
    }

    public load(doc: DocumentContent): void {
        doc.startPage = this.startPage;
        doc.pages     = this.pages;
        // doc.ClipInfo  = this.ClipInfo;

        const count = doc.content.length;
        for (let index = 0; index < count; index++) {
            doc.content[index].loadRecalculateObject( this.recalcContent[index] );
        }
    }
}
