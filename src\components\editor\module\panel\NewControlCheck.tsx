import * as React from 'react';
import { INewControlProperty, NewControlType } from '../../../../common/commonDefines';
import Input from '../../ui/Input';
import Checkbox from '../../ui/CheckboxItem';
import CustomProperty from './CustomProperty';
import { NewControlManageInfo } from './NewControlManage';

interface IDialogProps {
    documentCore: any;
    property: INewControlProperty;
    visible?: boolean;
}

export default class NewControlCheck extends React.Component<IDialogProps> {
    private newControl: INewControlProperty;
    constructor(props: IDialogProps) {
        super(props);
        this.init();
    }

    public render(): any {
        return (
            <React.Fragment>
                <div className='editor-line'>
                    <span className='title'>常规</span>
                </div>
                <div className='editor-line'>
                    <div className='w-70'>名称</div>
                    <div className='right-auto'>
                        <Input
                            name='newControlName'
                            value={this.newControl.newControlName}
                            onChange={this.onChange}
                            onBlur={this.onBlur}
                        />
                    </div>
                </div>
                <div className='editor-line'>
                    <div className='w-70'>内部名称</div>
                    <div className='right-auto'>
                        <Input
                            name='newControlSerialNumber'
                            value={this.newControl.newControlSerialNumber}
                            onChange={this.onChange}
                            readonly={true}
                            placeholder={''}
                            disabled={true}
                            onBlur={this.onBlur}
                        />
                    </div>
                </div>
                <div className='editor-line'>
                    <div className='w-70'>提示符</div>
                    <div className='right-auto'>
                        <Input
                            name='newControlInfo'
                            value={this.newControl.newControlInfo}
                            onChange={this.onChange}
                            onBlur={this.onBlur}
                        />
                    </div>
                </div>
                <div className='editor-line'>
                    <div className='w-70'>内容</div>
                    <div className='right-auto'>
                        <Input
                            name='label'
                            value={this.newControl.label}
                            onChange={this.onChange}
                            onBlur={this.onBlur}
                        />
                    </div>
                </div>
                <div className='editor-line editor-multi-line'>
                    <span className='title w-70'>属性</span>
                    <div className='right-auto newcontrol-prop'>
                        <div className='editor-line'>
                            <div className='w-050'>
                                <Checkbox
                                    name='isNewControlHidden'
                                    value={this.newControl.isNewControlHidden}
                                    onChange={this.checkedChange}
                                >
                                    隐藏
                                </Checkbox>
                            </div>
                            <div className='w-050'>
                                <Checkbox
                                    name='isNewControlCanntDelete'
                                    value={this.newControl.isNewControlCanntDelete}
                                    onChange={this.checkedChange}
                                >
                                    禁止删除
                                </Checkbox>
                            </div>
                        </div>

                        <div className='editor-line'>
                            <div className='w-050'>
                                <Checkbox
                                    name='isNewControlShowBorder'
                                    value={this.newControl.isNewControlShowBorder}
                                    onChange={this.checkedChange}
                                >
                                    显示边框
                                </Checkbox>
                            </div>

                            <div className='w-050'>
                                <Checkbox
                                    name='isNewControlHiddenBackground'
                                    value={this.newControl.isNewControlHiddenBackground}
                                    onChange={this.checkedChange}
                                >
                                    隐藏背景色
                                </Checkbox>
                            </div>
                        </div>

                        <div className='editor-line'>
                            <div className='w-050'>
                                <Checkbox
                                    name='showRight'
                                    value={this.newControl.showRight}
                                    onChange={this.checkedChange}
                                >
                                    勾选框居右
                                </Checkbox>
                            </div>
                            <div className='w-050'>
                                <Checkbox
                                    name='tabJump'
                                    value={this.newControl.tabJump}
                                    onChange={this.checkedChange}
                                >
                                    键盘跳转
                                </Checkbox>
                            </div>
                        </div>
                    </div>
                </div>

                <div className='editor-line'>
                    <span className='title'>固有属性：</span>
                </div>

                <div>
                    <div className='editor-line'>
                        <div className='w-050'>
                            <Checkbox
                                name='checked'
                                value={this.newControl.checked}
                                onChange={this.checkedChange}
                            >
                                勾选
                            </Checkbox>
                        </div>

                        <div className='w-050'>
                            <Checkbox
                                name='printSelected'
                                value={this.newControl.printSelected}
                                onChange={this.checkedChange}
                            >
                                勾选才打印
                            </Checkbox>
                        </div>
                    </div>
                </div>
                <div className='editor-line editor-multi-line'>
                    <span className='title w-70'>自定义属性</span>
                </div>
                <div className='editor-line'>
                    <CustomProperty
                        name='customProperty'
                        properties={this.newControl.customProperty}
                        documentCore={this.props.documentCore}
                    />
                </div>
            </React.Fragment>
        );
    }

    private init(): void {
        const newControl: any = this.newControl = {newControlName: null};
        const property = this.props.property;
        if (!property) {
            return;
        }
        Object.keys(property)
        .forEach((key) => {
            newControl[key] = property[key];
        });
    }

    private onChange = (value: any, name: string): void => {
        this.newControl[name] = value;
    }

    private checkedChange = (value: any, name: string): void => {
        this.onChange(value, name);
        this.confirm(name);
    }

    private onBlur = (name: string): void => {
        // if (!this.isValidData(name)) {
        //     return;
        // }
        // const obj = {};
        // obj[name] = this.newControl[name];
        // const res = this.props.documentCore.setNewControlProperty(obj, this.props.property.newControlName);
        this.confirm(name);
    }

    private confirm = (name: string): void => {
        const value = this.newControl[name];
        NewControlManageInfo.confirm(name, value);
    }
}
