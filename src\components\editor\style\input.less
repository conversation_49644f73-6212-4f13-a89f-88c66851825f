@import './global.less';

.hz-editor-container textarea {
    border-color: @borderColor;
    outline: none;
    &:focus, &:hover, &:active {
        border-color: @activeColor;
    }
}

.hz-editor-container .editor-textarea {
    display: inline-block;
    width: 100%;
    background: #fff;
    textarea {
        width: 100%;
        line-height: 20px;
        color: @color;
        font-size: @fontSize;
        font-family: @fontFamily;
    }
}

.hz-editor-container .editor-input {
    display: inline-block;
    width: 100%;
    color: @color;
    font-size: @fontSize;
    font-family: @fontFamily;
    vertical-align: middle;

    * {
        box-sizing: border-box;
    }

    .small {
        height: 40px;
    }

    .large {
        height: 60px;
    }

    .default {
        height: 26px;
        line-height: 26px;
    }

    // base css
    .inputWrapper {
        position: relative;
        display: flex;
        width: 100%;
        height: 28px;
        & > span {
            flex-grow: 0;
            line-height: 2;
        }
        .inputGroupWrapper {
            flex-grow: 1;
            line-height: 1;
            line-height: 1;
            &:not(:first-child) {
                border-top-left-radius: 0;
                border-bottom-left-radius: 0;
            }
            &:not(:last-child) {
                border-top-right-radius: 0;
                border-bottom-right-radius: 0;
            }
        }
    }

    &:hover .closeBtn {
        display: inline-block;
    }

    .inputGroupWrapper {
        position: relative;
        height: 100%;
        min-height: 100%;
        border: 1px solid @borderColor;
        border-radius: 3px;
        overflow: hidden;
        line-height: 1;

        &:focus, &:hover {
            border-color: @activeColor;
        }
    }

    .number-icon {
        position: absolute;
        top: 0;
        right: 4px;
        height: 100%;
        width: 10px;
        i {
            position: absolute;
            width: 100%;
            height: 50%;
            font-style: normal;
            vertical-align: middle;
            &::before {
                position: absolute;
                top: calc(50% - 5px);
                width: 0;
                height: 0;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                content: " ";
                cursor: pointer;
            }
            &:first-child::before {
                border-top: 5px solid transparent;
                border-bottom: 5px solid  #000000;
            }
            &:last-child {
                top: 50%;
            }
            &:last-child::before {
                border-top: 5px solid  #000000;
                border-bottom: 5px solid transparent;
            }
        }

        & + .closeBtn {
            display: none;
        }
    }

    .disabled, .readonly {
        & > .inputWrapper {
            input::placeholder {
                display: none;
            }
            span {
                &:focus, &:hover {
                    border-color: @borderColor;
                    .closeBtn {
                        display: none;
                    }
                }
            }
        }
    }

    &.readonly:not(.disabled) {
        // input::placeholder {
        //     display: none;
        // }
        span.inputWrapper > span {
            border-width: 1px;
            border-style: solid;
            border-color: @borderColor;
            .closeBtn {
                display: none;
            }

            input {
                &::placeholder {
                    color: @disabledColor;
                }
            }
        }
    }

    &.disabled {
        & > .inputWrapper > span {
            border: none;
            background-color: @disabledColor;
            .closeBtn {
                display: none;
            }
            input {
                background-color: @disabledColor;
                cursor: not-allowed;
                &::placeholder {
                    color: @disabledColor;
                }
            }
        }
    }

    // .disabled {
    //     .addonBeforeGroupWrapper, .addonAfterGroupWrapper {
    //         background-color: @disabledColor;
    //     }
    // }

    .input {
        display: table-cell;
        position: relative;
        border: none;
        width: 100%;
        height: 100%;
        line-height: 100%;
        padding: 0px 5px;
        line-height: 1;
        transition: all 0.3s;
        outline: none;
        -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
        &:not(:first-child) {
            padding-left: 4px;
        }
        &:not(:last-child) {
            padding-right: 4px;
        }
        &::placeholder {
            color: @color4;
        }
        background:rgba(255,255,255,1);
    }

    input::-webkit-outer-spin-button,
    input::-webkit-inner-spin-button {
        -webkit-appearance: none;
    }

    input[type="number"] {
        -moz-appearance: textfield;
    }


    .suffix,
    .prefix,
    .closeBtn {
        position: absolute;
        max-height: 100%;
        overflow: hidden;
        top: 50%;
        transform: translateY(-50%);
        z-index: 2;
        font-size: 12px;
        font-style: normal;
    }

    .suffix {
        right: 6px;
    }
    .prefix {
        left: 6px;
    }

    .closeBtn {
        display: none;
        right: 6px;
        width: @closeBtnW;
        height: @closeBtnW;
        border-radius: 50%;
        color: #fff;
        text-align: center;
        cursor: pointer;
        background-color: #ccc;
        transition: all 0.3s;
        animation: scale 0.2s ease-in;
        &::before {
            content: '×';
            display: block;
            position: absolute;
            font-size: 12px;
            top: 40%;
            left: 50%;
            transform: translate(-50%, -50%);
            pointer-events: none;
        }
        &:active {
            background-color: @activeColor;
        }
    }

    @keyframes scale {
        0% {
            opacity: 0;
        }
        100% {
            opacity: 1;
        }
    }

    .addonBeforeGroupWrapper,
    .addonAfterGroupWrapper {
        position: relative;
        white-space: nowrap;
        vertical-align: middle;
        background-color: @addonColor;
        &.isString {
            padding: 0 6px;
        }
    }

    .addonBeforeGroupWrapper {
        border-top-left-radius: 3px;
        border-bottom-left-radius: 3px;
        border: 1px solid @borderColor;
        border-right: none;
    }

    .addonAfterGroupWrapper {
        padding: 0 2px;
        border-top-right-radius: 3px;
        border-bottom-right-radius: 3px;
        border: 1px solid @borderColor;
        border-left: none;
    }
}

.hz-editor-container .editor-input.trigger-datebox { // input trigger datebox
    .inputWrapper {
        .inputGroupWrapper {
            border-right: none !important;

            +.addonAfterGroupWrapper {
                border-left: none;
                background-color: #fff;
                cursor: pointer;

                .select-btn {
                    display: inline-block;
                    width: 12px;
                    height: 26px;
                    margin-right: 5px;
                    margin-left: 2px;
                    line-height: 22px;
                    vertical-align: top;
                    pointer-events: none;
            
                    &::before {
                        display: inline-block;
                        width: 0;
                        height: 0;
                        margin-top: 10px;
                        border-top: 5px solid  #000000;
                        border-left: 5px solid transparent;
                        border-right: 5px solid transparent;
                        content: " ";
                        pointer-events: none;
                    }
                }
            }

            input {
                cursor: pointer;
            }
        }
    }
}
