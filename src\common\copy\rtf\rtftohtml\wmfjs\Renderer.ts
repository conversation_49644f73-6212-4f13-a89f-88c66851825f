/*

The MIT License (MIT)

Copyright (c) 2015 Thomas <PERSON>

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.

*/

import * as $ from 'jquery';
import { Blob } from './Blob';
import { GDIContext } from './GDIContext';
import { Helper, WMFJSError } from './Helper';
import { WMFRecords } from './WMFRecords';
import { WMF, WMFPlacable } from './WMF';

export interface IRendererSettings {
    width: string;
    height: string;
    xExt: number;
    yExt: number;
    mapMode: number;
}

export class Renderer {
    // tslint:disable-next-line: variable-name
    private _img: WMF;

    constructor(blob: ArrayBuffer) {
        this.parse(blob);
        Helper.log('WMFJS.Renderer instantiated');
    }

    public render(info: IRendererSettings): any {
        const img = ($('<div>') as any).svg({
            onLoad: (svg: any) => {
                return this._render(svg, info.mapMode, info.xExt, info.yExt);
            },
            settings: {
                viewBox: [0, 0, info.xExt, info.yExt].join(' '),
                preserveAspectRatio: 'none', // TODO: MM_ISOTROPIC vs MM_ANISOTROPIC
            },
        });
        const svgContainer = ($(img[0]) as any).svg('get');
        return $(svgContainer.root())
            .attr('width', info.width)
            .attr('height', info.height);
    }

    private parse(blob: ArrayBuffer): void {
        this._img = null;

        const reader = new Blob(blob);

        let type;
        let size;
        let placable;
        let headerstart;
        const key = reader.readUint32();
        if (key === 0x9ac6cdd7) {
            placable = new WMFPlacable(reader);
            headerstart = reader.pos;
            type = reader.readUint16();
            size = reader.readUint16();
        } else {
            headerstart = 0;
            // tslint:disable-next-line: no-bitwise
            type = key & 0xffff;
            // tslint:disable-next-line: no-bitwise
            size = (key >>> 16) & 0xffff;
        }
        switch (type) {
            case Helper.GDI.MetafileType.MEMORYMETAFILE:
            case Helper.GDI.MetafileType.DISKMETAFILE:
                if (size === Helper.GDI.METAHEADER_SIZE / 2) {
                    const version = reader.readUint16();
                    switch (version) {
                        case Helper.GDI.MetafileVersion.METAVERSION100:
                        case Helper.GDI.MetafileVersion.METAVERSION300:
                            this._img = new WMF(reader, placable, version, headerstart + (size * 2));
                            break;
                    }
                }
                break;
        }

        if (this._img == null) {
            throw new WMFJSError('Format not recognized');
        }
    }

    // tslint:disable-next-line: sy-method-name
    private _render(svg: any, mapMode: number, xExt: number, yExt: number): void {
        // See https://www-user.tu-chemnitz.de/~ygu/petzold/ch18b.htm
        const gdi = new GDIContext(svg);
        gdi.setViewportExt(xExt, yExt);
        gdi.setMapMode(mapMode);
        Helper.log('[WMF] BEGIN RENDERING --->');
        this._img.render(gdi);
        Helper.log('[WMF] <--- DONE RENDERING');
    }
}

export class WMFRect16 {
    private left: number;
    private top: number;
    private right: number;
    private bottom: number;

    constructor(reader: Blob) {
        this.left = reader.readInt16();
        this.top = reader.readInt16();
        this.right = reader.readInt16();
        this.bottom = reader.readInt16();
    }

    public toString(): string {
        return '{left: ' + this.left + ', top: ' + this.top + ', right: ' + this.right
            + ', bottom: ' + this.bottom + '}';
    }
}
