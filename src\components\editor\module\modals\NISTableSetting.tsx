import * as React from 'react';
import Dialog from '../../ui/Dialog';
import Button from '../../ui/Button';
import '../../style/tableSetting.less';
import { CustomPropertyElementType,
    INISCellGridLine,
    isValidName} from '../../../../common/commonDefines';
import CustomPropertyBtn from './CustomProperty';
import { message } from '../../../../common/Message';
import { IFRAME_MANAGER } from '../../../../common/IframeManager';
import Input from '../../ui/Input';
import Checkbox from '../../ui/CheckboxItem';
// import { getMMFromPx } from '@/model/core/util';
// import { getPxForMM } from 'plugins/serialize/serializer';

interface IDialogProps {
    documentCore: any;
    tableProps?: any;
    visible: boolean;
    id?: string;
    tableName?: string;
    close: (name: string | number, bRefresh?: boolean) => void;
    refresh: (bRefresh: boolean, timeout?: number) => void;
}

interface IState {
    bRefresh: boolean;
}

// interface IStyle {
//     left: number;
// }

enum TablePropsDialogTabType {
    Table = 0,
    // RowHeight,
    ColumnWidth,
    // TableBorderLine,
}

export default class NISTableSetting extends React.Component<IDialogProps, IState> {
    private table: any;
    // private cellMargins: any;
    private column: any;
    private row: any;
    // private cell: any;
    private tabIndex: TablePropsDialogTabType;
    // private tableProps: any;
    private visible: any;
    private bCustomProperty: boolean;
    private oldGridLines: {[key: number]: INISCellGridLine};
    private gridLines: {[key: number]: INISCellGridLine};

    constructor(props: any) {
        super(props);
        this.table = {
            tableName: '',
            customProperty: undefined,
            bCanAddRow: true,
            bCanDeleteRow: true,
            bFixedColWidth: false,
            bDeleteProtect: false,
            bRepeatHeader: false,
            repeatHeaderNum: 1,
            bHeaderReadOnly: false,
            // rowsAuto: [],
        };
        this.state = {
            bRefresh: false,
        };
        this.row = {
            // rowIndex: 1,
            // rowHeights: [],
            // rowHeight: 0,
            // bAuto: true,
            length: 1,
        };
        this.column = {
            columnIndex: 1,
            columnWidths: [],
            columnWidth: 0,
            height: null,
            length: 1,
        };

        this.tabIndex = TablePropsDialogTabType.Table;
        this.visible = this.props.visible;
        this.setDialogValue();
    }

    public componentDidMount(): void {
        // this.boxRef.current.ownerDocument.addEventListener('click', this.docClick);
    }

    public componentWillUnmount(): void {
        // this.boxRef.current.ownerDocument.removeEventListener('click', this.docClick);
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        this.visible = nextProps.visible;
    }

    public render(): any {

        return (
            <Dialog
                id={this.props.id}
                visible={this.visible}
                width={380}
                // close={this.close}
                open={this.open}
                preventDefault={false}
                title='表格属性'
                confirm={this.confirm}
                footer={this.renderFooter()}
                // id='table'
            >
                <div className='editor-tableSetting  nis-table'>
                    <div className='editor-tabs'>
                        <ul>
                            <li
                                className={this.tabIndex === TablePropsDialogTabType.Table ? 'active' : null}
                                onClick={this.tabClick.bind(this, TablePropsDialogTabType.Table)}
                            >
                                表格
                            </li>
                            <li
                                className={this.tabIndex === TablePropsDialogTabType.ColumnWidth ? 'active' : null}
                                onClick={this.tabClick.bind(this, TablePropsDialogTabType.ColumnWidth)}
                            >
                                列属性
                            </li>
                        </ul>
                    </div>
                    <div className='editor-tabs-content'>
                        <ul className='table'>
                            <li className={this.tabIndex === TablePropsDialogTabType.Table ? 'active' : null}>
                                <div className='editor-line'>
                                    <span className='title'>常规</span>
                                </div>
                                <div className='editor-line'>
                                    <span className='w-70'>表格名：</span>
                                    <div className='right-auto'>
                                        <Input
                                            name='tableName'
                                            value={this.table.tableName}
                                            onChange={this.textChange}
                                        />
                                    </div>
                                </div>
                                <div className='editor-line editor-multi-line'>
                                    <span className='w-70 title'>属性</span>
                                    <div className='right-auto'>
                                        <div className='editor-line'>
                                            <div className='w-050'>
                                                <Checkbox
                                                    name='bCanAddRow'
                                                    value={this.table.bCanAddRow}
                                                    onChange={this.checkChange}
                                                >
                                                    允许行新增
                                                </Checkbox>
                                            </div>
                                            <div className='w-050'>
                                                <Checkbox
                                                    name='bCanDeleteRow'
                                                    value={this.table.bCanDeleteRow}
                                                    onChange={this.checkChange}
                                                >
                                                    允许行删除
                                                </Checkbox>
                                            </div>
                                        </div>
                                        <div className='editor-line'>
                                            <div className='w-050'>
                                                <Checkbox
                                                    name='bFixedColWidth'
                                                    value={this.table.bFixedColWidth}
                                                    onChange={this.checkChange}
                                                >
                                                    固定列宽
                                                </Checkbox>
                                            </div>
                                            <div className='w-050'>
                                                <Checkbox
                                                    name='bDeleteProtect'
                                                    value={this.table.bDeleteProtect}
                                                    onChange={this.checkChange}
                                                >
                                                    表格禁止删除
                                                </Checkbox>
                                            </div>
                                        </div>
                                        <div className='editor-line'>
                                            <CustomPropertyBtn
                                                name='customProperty'
                                                properties={this.table.customProperty}
                                                documentCore={this.props.documentCore}
                                                onChange={this.onChange}
                                                close={this.onClose}
                                                id='bCustomProperty'
                                                visible={this.bCustomProperty}
                                                type={CustomPropertyElementType.Table}
                                            />
                                        </div>
                                    </div>
                                </div>
                                <div className='editor-line'>
                                    <span className='title'>扩展属性</span>
                                </div>
                                <div className='editor-line editor-multi-line'>
                                    <span className='w-70'>列冻结：</span>
                                    <div className='right-auto'>
                                        <div className='editor-line'>
                                            <div className='w-050 in-label'>
                                                <label className='w-label'>左侧</label>
                                                <span>
                                                    <Input
                                                        name='fixedLeft'
                                                        type='number'
                                                        min={0}
                                                        step={1}
                                                        value={this.table.fixedLeft}
                                                        onChange={this.numChange}
                                                        disabled={true}
                                                    />
                                                </span>
                                            </div>
                                            <div className='w-050 in-label'>
                                                <label className='w-label'>右侧</label>
                                                <span>
                                                    <Input
                                                        name='fixedRight'
                                                        type='number'
                                                        min={0}
                                                        step={1}
                                                        value={this.table.fixedRight}
                                                        onChange={this.numChange}
                                                        disabled={true}
                                                    />
                                                </span>
                                            </div>
                                        </div>
                                        <div className='editor-line'>
                                            <span className='w-050 in-label'>
                                                <Checkbox
                                                    name='bFixedHeader'
                                                    value={this.table.bFixedHeader}
                                                    onChange={this.checkChange}
                                                    disabled={true}
                                                >
                                                    冻结表头
                                                </Checkbox>
                                            </span>
                                        </div>
                                    </div>

                                </div>

                                <div className='editor-line editor-multi-line'>
                                    <span className='w-70'>表头：</span>
                                    <div className='right-auto'>
                                        <div className='editor-line'>
                                            <div className='w-050'>
                                                <Checkbox
                                                    name='bRepeatHeader'
                                                    value={this.table.bRepeatHeader}
                                                    onChange={this.checkChange}
                                                >
                                                    重复表头
                                                </Checkbox>
                                            </div>
                                            <div className='w-050'>
                                                <Checkbox
                                                    name='bHeaderReadOnly'
                                                    value={this.table.bHeaderReadOnly}
                                                    onChange={this.checkChange}
                                                >
                                                    表头只读
                                                </Checkbox>
                                            </div>
                                        </div>
                                        <div className='editor-line'>
                                            <label>前</label>
                                            <span style={{width: '45px', display: 'inline-block', padding: '0 3px'}}>
                                                <Input
                                                    name='repeatHeaderNum'
                                                    type='number'
                                                    min={0}
                                                    step={1}
                                                    value={this.table.repeatHeaderNum}
                                                    onChange={this.numChange}
                                                />
                                            </span>
                                            <label>行</label>
                                        </div>
                                    </div>
                                </div>
                            </li>
                            <li className={this.tabIndex === TablePropsDialogTabType.ColumnWidth ? 'active' : null}>
                                <div className='editor-line'>
                                    <span className='w-70 title'>第{this.column.columnIndex}列</span>
                                    <div className='right-auto col-btns'>
                                        <span onClick={this.handlePrevNextIndex.bind(this, 'prevColumn')}/>
                                        <span onClick={this.handlePrevNextIndex.bind(this, 'nextColumn')}/>
                                    </div>
                                </div>
                                <div className='editor-line'>
                                    <label className='w-70'>宽度:</label>
                                    <div className='right-auto'>
                                        <Input
                                            name='columnWidth'
                                            type='number'
                                            min={0}
                                            step={0.01}
                                            value={this.column.columnWidth}
                                            onChange={this.numChange}
                                            renderAppend={this.renderUnitDom}
                                        />
                                    </div>
                                </div>
                                {/* <div className='editor-line'>
                                    <label className='w-70'>网格线</label>
                                    <Checkbox
                                        name='visible'
                                        value={this.column.visible}
                                        onChange={this.checkChange}
                                    >
                                        是否显示
                                    </Checkbox>
                                </div> */}
                                {/* <div className='editor-line'>
                                    <label className='w-70'>网格线跨度</label>
                                    <div className='right-auto'>
                                        <Input
                                            name='height'
                                            type='number'
                                            min={0}
                                            step={0.01}
                                            value={this.column.height}
                                            onChange={this.numChange}
                                            renderAppend={this.renderUnitDom}
                                        />
                                    </div>
                                </div> */}
                            </li>
                        </ul>
                    </div>
                </div>
            </Dialog>
        );
    }

    private renderFooter(): any {
        return (
            <span>
                <Button onClick={this.close}>取消</Button>
                <Button type='primary' onClick={this.confirm}>确认</Button>
            </span>
        );
    }

    private renderUnitDom(): any {
        return (<span>cm</span>);
    }

    private setDialogValue(): void {
        const tableProps = this.props.documentCore.getTableProperty(this.props.tableName);
        if ( null != tableProps ) {
            // this.tableProps = tableProps;
            this.table = {
                tableName: tableProps.tableName,
                customProperty: tableProps.customProperty,
                bCanAddRow: tableProps.bCanAddRow,
                bCanDeleteRow: tableProps.bCanDeleteRow,
                bFixedColWidth: tableProps.bFixedColWidth,
                bDeleteProtect: tableProps.bDeleteProtect,
                bRepeatHeader: tableProps.bRepeatHeader,
                repeatHeaderNum: tableProps.repeatHeaderNum,
                bHeaderReadOnly: tableProps.bHeaderReadOnly,
                fixedLeft: tableProps.fixedLeft || 0,
                fixedRight: tableProps.fixedRight || 0,
                bFixedHeader: tableProps.bFixedHeader,
            };

            let rowIndex = tableProps.curPos.rowIndex[0] + 1;
            if ( 1 < tableProps.curPos.rowIndex.length && rowIndex !== tableProps.curPos.rowIndex[1] + 1 ) {
                rowIndex += ( '-' + ( tableProps.curPos.rowIndex[1] + 1 ) );
            }

            let columnIndex = tableProps.curPos.cellIndex[0] + 1;
            const columnWidth = tableProps.columnWidths[tableProps.curPos.cellIndex[0]];
            if ( 1 < tableProps.curPos.cellIndex.length && rowIndex !== tableProps.curPos.cellIndex[1] + 1 ) {
                columnIndex += ( '-' + ( tableProps.curPos.cellIndex[1] + 1 ) );
            }

            this.row = {
            //     rowIndex,
            //     rowHeights: tableProps.rowHeights,
            //     rowHeight,
            //     bAuto: tableProps.rowsAuto[rowIndex - 1],
                length: tableProps.rows,
            };

            // let line: INISCellGridLine;
            // const lines = this.oldGridLines = tableProps.cellGirdLines;
            // const gridLines = this.gridLines = {};
            // if (lines) {
            //     const keys = Object.keys(lines);
            //     line = lines[columnIndex - 1];
            //     keys.forEach((key) => {
            //         // const curLine: INISCellGridLine = lines[key];
            //         // if (!curLine.height) {
            //         //     gridLines[key] = {...lines[key]};
            //         //     return;
            //         // }
            //         // curLine.height = parseFloat((getMMFromPx(curLine.height) / 10).toFixed(2));
            //         gridLines[key] = {...lines[key]};
            //     });
            // }

            this.column = {
                columnIndex,
                columnWidths: tableProps.columnWidths,
                columnWidth,
                // height: line?.height,
                // visible: line?.visible,
                length: tableProps.columnWidths.length,
            };
        }
    }

    private tabClick(index: TablePropsDialogTabType): void {
        if ( this.tabIndex === index ) {
            return ;
        }

        this.tabIndex = index;
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private numChange = (value: any, name: string, e: any): void => {
        if ('columnWidth' !== name) {
            this.table[name] = value;
        } else {
            this.column[name] = parseFloat(e.target.value);
        }

        // this.setState({bRefresh: !this.state.bRefresh});
    }

    private checkChange = (value: any, name: string, e: any): void => {
        // if ( 'bAuto' === subname && this.table.bFixedRowHeight ) {
        //     this.row.rowHeight = this.row.rowHeights[this.row.rowIndex - 1];
        //     return;
        // }
        if (name === 'visible') {
            let line = this.gridLines[this.column.columnIndex - 1];
            if (!line) {
                line = this.gridLines[this.column.columnIndex - 1] = {} as any;
            }
            line.visible = value;
            return;
        }

        this.table[name] = value;

        // this.setState({bRefresh: !this.state.bRefresh});
    }

    private textChange = (value: any, name: string, e: any): void => {
        this.table[name] = value;
        // this.setState({bRefresh: !this.state.bRefresh});
    }

    private handlePrevNextIndex(position: string): void {
        let result = false;
        if ( ('prevColumn' === position || 'nextColumn' === position ) ) {
            if ( 'string' === typeof(this.column.columnIndex) ) {
                const indexs = this.column.columnIndex.split('-');
                this.column.columnIndex = 'prevColumn' === position ?
                        parseInt(indexs[0], 0) : parseInt(indexs[1], 0);
            }

            const bDiff = (0.01 < Math.abs(this.column.columnWidth -
                        this.column.columnWidths[this.column.columnIndex - 1]));
            if ( !this.table.bFixedRowHeight && this.checkValue() && bDiff && this.checkColumnWidth() ) {
                result = this.props.documentCore.setTableProperty({columnWidths: this.column.columnWidths});
                result = true;
            }
        }

        switch (position) {
            case 'prevColumn':
                if ( 0 < this.column.columnIndex - 1) {
                    this.column.columnIndex--;
                } else {
                    this.column.columnIndex = this.column.length;
                }
                this.column.columnWidth = this.column.columnWidths[this.column.columnIndex - 1];
                break;

            case 'nextColumn':
                if ( this.column.length > this.column.columnIndex ) {
                    this.column.columnIndex++;
                } else {
                    this.column.columnIndex = 1;
                }
                this.column.columnWidth = this.column.columnWidths[this.column.columnIndex - 1];
                break;

            default:
                break;
        }

        if ( true === result ) {
            const tableProps = this.props.documentCore.getTableProperty(this.props.tableName);
            // this.row.rowHeights = tableProps.rowHeights;
            // this.row.length = tableProps.rows;
            // this.table.rowsAuto = tableProps.rowsAuto;

            this.column.columnWidths = tableProps.columnWidths;
            this.column.length = tableProps.columnWidths.length;
            this.props.refresh(true);
        }
        // this.column.visible = this.gridLines[this.column.columnIndex - 1]?.visible;
        // if ( ('prevRow' === position || 'nextRow' === position ) ) {
        //     this.row.bAuto = this.table.rowsAuto[this.row.rowIndex - 1];
        //     this.row.rowHeight = ( this.table.bFixedRowHeight || !this.row.bAuto ?
        //                             this.row.rowHeights[this.row.rowIndex - 1] : 0 );
        // }

        this.props.documentCore.removeSelection();
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private setColumnValue(columnIndex: number): any {

        if ( columnIndex <= this.column.columnWidth.length ) {
            return this.column.columnWidth[columnIndex - 1];
        }

        return '';
    }

    private open = (): void => {
        this.tabIndex = 0;
        // const table = this.table;
        this.setDialogValue();
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private close = (bRefresh?: boolean): void => {
        this.props.close(this.props.id, bRefresh);
        this.visible = false;
        this.tabIndex = 0;
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private onClose = (id: string, bRefresh: boolean): void => {
        this[id] = false;
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private onChange = (value: any, name: string): void => {
        this.table[name] = value;
    }

    private confirm = (id?: any): void => {
        const { documentCore } = this.props;
        IFRAME_MANAGER.setDocId(documentCore.getCurrentId());
        if (!isValidName(this.table.tableName)) {
            message.error('表格名不符合规范，请重新输入');
            return;
        }

        if ( true !== documentCore.checkTableName2(this.table.tableName) ) {
            message.error('表格名重名，请重新输入');
            return ;
        }

        if ( true !== this.checkValue() || true !== this.checkColumnWidth() ) {
            return ;
        }

        const oldColWidth = this.column.columnWidths[this.column.columnIndex];
        let columnWidth = parseFloat(this.column.columnWidth.toFixed(2));

        if ( 0.01 > Math.abs(columnWidth - oldColWidth) || null == oldColWidth ) {
            columnWidth = null;
        }


        // const gridLines = this.gridLines;
        // let keys = Object.keys(gridLines);
        // let cellGirdLines = this.oldGridLines || {};
        // if (keys.length) {
        //     keys.forEach((key) => {
        //         cellGirdLines[key] = gridLines[key];
        //     });
        // }
        // keys = Object.keys(cellGirdLines);
        // keys.forEach((key) => {
        //     const line: INISCellGridLine = cellGirdLines[key];
        //     if (!line.height) {
        //         return;
        //     }
        //     line.height = getPxForMM(line.height * 10);
        // });

        const tableProps = {
            tableName: this.table.tableName,
            bCanAddRow: this.table.bCanAddRow,
            bCanDeleteRow: this.table.bCanDeleteRow,
            bFixedColWidth: this.table.bFixedColWidth,
            bDeleteProtect: this.table.bDeleteProtect,
            bRepeatHeader: this.table.bRepeatHeader,
            repeatHeaderNum: this.table.repeatHeaderNum,
            bHeaderReadOnly: this.table.bHeaderReadOnly,
            customProperty: this.table.customProperty,
            columnWidths: null == columnWidth ? null : this.column.columnWidths,
            fixedRight: this.table.fixedRight,
            fixedLeft: this.table.fixedLeft,
            bFixedHeader: this.table.bFixedHeader,
            // cellGirdLines,
        };

        if ( tableProps.repeatHeaderNum >= this.row.length ) {
            message.error('重复表头行数需小于总行数，请重新输入');
            return ;
        }

        // const bSelect = documentCore.isSelectedTable();
        // this.getTableBorders(bSelect);
        // tableProps.tableBorders = this.tableBorders;

        documentCore.setTableProperty(tableProps);
        this.close(true);
    }

    private checkValue(): boolean {
        if ( 0 > this.column.columnWidth || isNaN(this.column.columnWidth) ) {
            message.error('列宽输入非法，请重新输入');
            return false;
        }

        return true;
    }

    private checkColumnWidth(): boolean {
        if ( 'string' === typeof(this.column.columnIndex) ) {
            const indexs = this.column.columnIndex.split('-');
            const startColumnIndex = parseInt(indexs[0], 0);
            const endColumnIndex = parseInt(indexs[1], 0);
            this.column.columnIndex = startColumnIndex;

            if ( startColumnIndex > endColumnIndex ) {
                this.column.columnIndex = endColumnIndex;
            }
        }

        const columnIndex = this.column.columnIndex - 1;
        const oldWidth = this.column.columnWidths[columnIndex];

        if ( 0.01 > Math.abs(this.column.columnWidth - parseFloat(oldWidth.toFixed(2))) ) {
            return true;
        }

        let sumWidth = 0;
        let newWidth = this.column.columnWidth * 100;
        const minColWidth = 1;
        const colsLength = this.column.columnWidths.length;

        for (const width of this.column.columnWidths) {
            sumWidth += (width * 100);
        }

        sumWidth -= (colsLength - 1);
        sumWidth = parseInt(sumWidth.toFixed(0), 0);

        if ( newWidth >= sumWidth ) {
            if ( newWidth > sumWidth ) {
                message.error('列宽输入非法，请重新输入: 0.01---' + sumWidth / 100);
                return false;
            }

            for (let index = 0; index < colsLength; index++) {
                if ( columnIndex !== index ) {
                    this.column.columnWidths[index] = minColWidth;
                }
            }

            newWidth = sumWidth;
            this.column.columnWidths[columnIndex] = newWidth;
            this.column.columnWidth = sumWidth / 100;
        } else {
            let diff = oldWidth * 100 - newWidth;
            const newCols = [];
            this.column.columnWidths.forEach((col) => {
                newCols.push(parseInt((col * 100).toFixed(0), 0));
            });

            newCols[columnIndex] = newWidth;
            diff = parseInt(diff.toFixed(0), 0);

            if ( 0 < diff ) {
                if ( colsLength === columnIndex + 1 ) {
                    newCols[0] += diff;
                } else {
                    newCols[columnIndex + 1] += diff;
                }
            } else if ( 0 > diff ) {
                let bFlag = false;
                diff = Math.abs(diff);

                for (let index = columnIndex + 1; index < colsLength; index++) {
                    if ( diff + minColWidth <= newCols[index] ) {
                        bFlag = true;
                        newCols[index] = newCols[index] - diff;
                        break;
                    } else if ( 0 < diff ) {
                        diff = diff - ( newCols[index] - minColWidth );
                        newCols[index] = minColWidth;
                    }
                }

                if ( false === bFlag ) {
                    for (let index = 0; index < columnIndex; index++) {
                        if ( diff + minColWidth <= newCols[index] ) {
                            newCols[index] = newCols[index] - diff;
                            break;
                        } else if ( 0 < diff ) {
                            diff = diff - ( newCols[index] - minColWidth );
                            newCols[index] = minColWidth;
                        }
                    }
                }
            }

            newCols.forEach((col, index) => {
                this.column.columnWidths[index] = col / 100;
            });
        }

        return true;
    }

}
