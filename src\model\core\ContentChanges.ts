import { ChangeContent } from './HistoryChange';
import { ContentChangeType } from './HistoryDescription';

export default class ContentChanges {
    public changes: ContentChangesElement[];

    constructor() {
        this.changes = [];
    }

    public add( change: ContentChangesElement ): void {
        this.changes.push(change);
    }

    public refresh(): void {
        for (let index = 0, count = this.changes.length; index < count; index++) {
            this.changes[index].refreshData();
        }
    }

    private removeByHistoryItem( item: ChangeContent ): void {
        for (let index = 0, count = this.changes.length; index < count; index++) {
            const element = this.changes[index];

            if ( element.data === item ) {
                this.changes.splice(index, 1);
                return ;
            }
        }
    }

    private clear(): void {
        this.changes.length = 0;
    }

    private check( type: ContentChangeType, pos: number ): number|boolean {
        let curPos = pos;

        for (let index = 0, count = this.changes.length; index < count; index++) {
            const element = this.changes[index];
            const newPos = element.checkChanges(type, curPos);

            if ( false === newPos) {
                return false;
            }

            curPos = newPos;
        }

        return curPos;
    }
}

export class ContentChangesElement {
    public type: ContentChangeType;
    public count: number;
    public data: ChangeContent;
    public positions: number|boolean [];

    constructor( type: ContentChangeType, count: number, data: ChangeContent, pos: number ) {
        this.type = type;
        this.count = count;
        this.data = data;
        this.positions = this.makeArrayOfSimplyActions(type, pos, count);
    }

    public checkChanges( type: ContentChangeType, pos: number ): any {
        let curPos = pos;

        if ( ContentChangeType.ContentChangeAdd === type ) {
            for (let index = 0; index < this.count; index++) {
                if ( false !== this.positions[index] ) {
                    if ( curPos <= this.positions[index] ) {
                        this.positions[index]++;
                    } else {
                        if ( ContentChangeType.ContentChangeAdd === this.type ) {
                            curPos++;
                        } else {
                            curPos--;
                        }
                    }
                }
            }
        } else { // : ContentChangeType.ContentChangeRemove
            for (let index = 0; index < this.count; index++) {
                if ( false !== this.positions[index] ) {
                    if ( curPos < this.positions[index] ) {
                        this.positions[index]--;
                    } else {
                        if ( ContentChangeType.ContentChangeRemove === this.type ) {
                            this.positions[index] = false;
                            return false;
                        } else {
                            curPos++;
                        }
                    }
                }
            }
        }

        return curPos;
    }

    public makeArrayOfSimplyActions( type: ContentChangeType, pos: number, count: number ): number|boolean [] {
        const positions = [];

        if ( ContentChangeType.ContentChangeAdd === type ) {
            for (let index = 0; index < count; index++) {
                positions[index] = pos + index;
            }
        } else { // : ContentChangeType.ContentChangeRemove
            for (let index = 0; index < count; index++) {
                positions[index] = pos;
            }
        }

        return positions;
    }

    public refreshData(): void {
        this.data.data.bUseArray = true;
        this.data.data.posArray = this.positions;
    }
}
