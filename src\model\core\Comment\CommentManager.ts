import {Comment} from './Comment';
import Document from '../Document';
import { ParagraphContentPos } from '../Paragraph/ParagraphContent';
import ParaPortion from '../Paragraph/ParaPortion';
import Paragraph from '../Paragraph';
import History from '../History';
import { ChangeCommentAddItem, ChangeCommentRemoveItem } from './CommentChange';
import { GlobalEvent, GlobalEventName } from '@/common/GlobalEvent';
import { CommentOperatorType } from '@/common/commonDefines';
interface ICommentSortData {
    id: number;
    name: string;
}
interface IDeletedInfo {
    fullDeletes: Comment[];
    halfDeletes: {
        comment: Comment;
        portion: ParaPortion;
    }[];
    relates: string[];
}
interface ICommentResult {
    type: CommentResultType;
    comment: Comment;
}

export enum CommentResultType {
    /** 批注在选区内部 */
    Inner,
    /** 选区在批注内部 */
    Outer,
    /** 选区覆盖批注左侧 */
    Left,
    /** 选区覆盖批注右侧 */
    Right,
}

export interface ICommentStatusInfo {
    bInsertNew: boolean;
    userName: string;
    bShowPanel?: boolean; // 批注列表
}

interface ICommentPageCache {
    name: string;
    id: number;
    startPage: number;      // 起始页索引
    endPage: number;        // 结束页索引
    startPid: number;   // 起始段落id
    endPid: number;     // 结束段落id
}

export class CommentManager {
    public commentStatusInfo: ICommentStatusInfo;
    private data: Map<string, Comment>;
    private sortData: ICommentSortData[];
    private commentPageCaches: ICommentPageCache[];
    private saveData: ICommentSortData;
    private doc: Document;
    private deletedCommentsInfo: IDeletedInfo;
    private oldActivedCommentName: string;
    private dirtyFlag: boolean = false; // 用于避免连续触发dirty

    // 用于读取批注使用
    private preparedComment: Comment;
    private preparedCommentId: string;
    private unvalidComments: Comment[];
    constructor(doc: Document) {
        this.doc = doc;
        this.data = new Map();
        this.sortData = [];
        this.commentStatusInfo = {
            bInsertNew: false,
            userName: '默认用户',
        };
        this.unvalidComments = [];
        this.commentPageCaches = [];
    }

    public getAllCommentCount(): number {
        return this.data.size;
    }

    /**
     * 根据批注名激活批注
     * @param name 批注内部名（类id）
     * @returns 0: success, 1: failure, 2: unedit
     */
    public activeComment(name: string): number {
        if (name === this.oldActivedCommentName) {
            return 2;
        }
        this.cancelActived();
        const comment = this.data.get(name);
        if (comment) {
            comment.setActive(true);
            this.oldActivedCommentName = name;
            return 0;
        }
        return 1;
    }

    public addComment(comment: Comment, bHistory: boolean = true): void {
        comment.setLogicDocument(this.doc);
        const id = comment.getId();
        comment.setName(this.makeUniqueName());
        const commentData = comment.getData();
        if (!commentData.getUserName()) {
            commentData.setUserName(this.commentStatusInfo.userName);
        }
        const name = comment.getName();
        this.saveData = {id, name};
        this.data.set(name, comment);

        const history = this.getHistory();
        if (history && bHistory) {
            history.addChange(new ChangeCommentAddItem(comment, this, null));
        }
        // 后续绑定sort操作
    }

    /**
     * 构建批注所在页信息缓存
     */
    public buildCommentPageCaches(): void {
        if (this.commentPageCaches.length || this.dirtyFlag) {
            this.dirtyFlag = false;
            return;
        }
        this.commentPageCaches = [];
        for (const {id, name} of this.sortData) {
            const comment = this.data.get(name);
            const startPara = comment.getStartPortion().getParagraph();
            const endPara = comment.getEndPortion().getParagraph();
            const startPage = startPara.getCurrentPageByPos(comment.getStartPos());
            const endPage = endPara.getCurrentPageByPos(comment.getEndPos());
            this.commentPageCaches.push({
                id,
                name,
                startPage,
                endPage,
                startPid: startPara.id,
                endPid: endPara.id,
            });
        }
    }

    public cancelActived(): boolean {
        if (this.oldActivedCommentName) {
            const oldComment = this.data.get(this.oldActivedCommentName);
            oldComment && oldComment.setActive(false);
            this.oldActivedCommentName = '';
            return true;
        }
        return false;
    }

    public cancelAllActive(): void {
        const datas = this.data;
        for (const cmt of datas) {
            cmt[1].setActive(false);
        }
    }

    /**
     * 清除被删除的批注信息
     * @returns 需要再插入的Portion集合（有序）
     */
     public clearDeletedComments(): ParaPortion[] {
        const {fullDeletes, halfDeletes} = this.deletedCommentsInfo;
        for (const comment of fullDeletes) {
            this.deleteCommentByName(comment.getName());
        }
        return halfDeletes.map((half) => half.portion);
    }

    /**
     * 删除不合法的批注内容
     * @param isDeleted 是否是添加清除标记的批注
     */
    public clearUnValidComments(isDeleted: boolean = false): void {
        let comments: Comment[] = [];
        if (isDeleted) {
            const data = this.data;
            for (const name of this.deletedCommentsInfo.relates) {
                data.has(name) && comments.push(data.get(name));
            }
        } else {
            comments = this.getAllComments();
        }
        const emptyComments: Comment[] = [];
        for (const comment of comments) {
            comment.isEmpty() && emptyComments.push(comment);
        }
        this.deleteComments(emptyComments);
    }

    /** 清除读取过程中所有不符合的批注集合 */
    public clearUnValidCommentsInReading(): void {
        if (this.preparedComment) {
            const comment = this.preparedComment;
            if (comment.getStartPortion() && comment.getEndPortion()) {
                this.addComment(comment);
                this.sortData.push({id: comment.getId(), name: comment.getName()});
            }
        }
        this.saveData = null;
        this.preparedComment = null;
        this.preparedCommentId = '';
        this.deleteComments(this.unvalidComments);
    }

    public contains(name: string): boolean {
        if (this.data.has(name)) {
            return false;
        }
        return true;
    }

    public deleteCommentByName(name: string, bHistory: boolean = true): boolean {
        const comment = this.data.get(name);
        if (!comment) {
            return false;
        }

        return this.deleteCommentById(comment.getId(), bHistory);
    }

    public deleteCommentById(id: number, bHistory: boolean = true): boolean {
        const index = this.sortData.findIndex((data) => id === data.id);
        if (index === -1) {
            return false;
        }
        const obj = this.sortData[index];
        const comment = this.data.get(obj.name);
        const history = this.getHistory();
        if (history && bHistory) {
            history.addChange(new ChangeCommentRemoveItem(comment, this, null));
        }

        if (this.data.has(obj.name)) {
            this.data.delete(obj.name);
            this.sortData.splice(index, 1);
        } else if (this.saveData?.name === obj.name) {
            this.saveData = null;
        }
        this.setDirty();

        return true;
    }

    /**
     * 删除批注的指定边界Portion
     * @param comment 批注对象
     * @param bStart 是否是开始Portion
     */
     public deleteCommentPortion(comment: Comment, bStart: boolean): boolean {
        let portion: ParaPortion;
        if (bStart) {
            portion = comment.getStartPortion();
        } else {
            portion = comment.getEndPortion();
        }
        if (!portion) {
            return false;
        }
        const para = portion.getParagraph();
        para.contentRemove(portion.getPosInParent());
        return true;
    }

    /**
     * 删除目标批注集合
     * * 同时会删除对应的Portion
     * @param comments 批注集合
     */
    public deleteComments(comments: Comment[]): void {
        if (!comments) {
            return;
        }
        while (comments.length) {
            const comment = comments.pop();
            if (this.deleteCommentPortion(comment, false) && this.deleteCommentPortion(comment, true)) {
                this.deleteCommentByName(comment.getName());
            }
        }
    }

    /**
     * 直接删除批注内容（不会涉及批注portion的删除）
     * @param comments 批注集合
     * @param bHistory 是否进入历史操作
     */
    public deleteCommentsDirected(comments: Comment[], bHistory: boolean = false): void {
        while (comments.length) {
            this.deleteCommentByName(comments.pop().getName(), bHistory);
        }
    }

    public makeUniqueName(name?: string): string {
        if (name) {
            if (true === this.contains(name)) {
                return name;
            }
        }

        // let newControlName = NEW_CONTROL_NAME.get(type);
        // const pasteNewControls = this.pasteNewControls;
        let commentName = '批注';
        const datas = this.data;
        for (let number = 1; number < Number.MAX_SAFE_INTEGER; number++) {
            const temp = commentName + number;
            if ( datas.has(temp) === false) {
                commentName = temp;
                break;
            }
        }

        return commentName;
    }

    /**
     * 获取跨段落或跨页的批注
     * @param pageIndex 页码索引
     * @param para 目标段落
     * @returns 批注id
     */
    public getStartCommentCrossParaOrPage(pageIndex: number, para: Paragraph): number {
        this.buildCommentPageCaches();
        const pageCache = this.commentPageCaches;
        const len = pageCache.length;
        if (!len) {
            return -1;
        }
        const paraPos = para.getCurContentPosInDoc().copy();
        const paraPages = para.getPages();
        // 根据pages信息进行坐标校正
        const abPage = para.getAbsolutePage();
        if (abPage > pageIndex) {
            return -1;
        }
        const curParaPage = paraPages[pageIndex - abPage];
        if (!curParaPage) return -1;
        const curLine = para.getLines()[curParaPage.startLine];
        const startPortionPos = curLine.ranges[0]?.startPos;
        const portion = para.getContent()[startPortionPos];
        if (portion) {
            const contentPos = portion.getRangeStartPos(curParaPage.startLine - portion.startLine);
            paraPos.update2(startPortionPos, paraPos.getDepth() - 1);
            paraPos.update2(contentPos, paraPos.getDepth());
        }

        // （跨页段落的目标页首行坐标，跨段落的段落首行）
        for (let i = 0; i < len; i++) {
            const {id, name, startPage: start, endPage: end, startPid, endPid} = pageCache[i];
            // 未跨页
            if (paraPages.length === 1) {
                // 考虑跨段落情况
                if (startPid !== endPid) {
                    const comment = this.data.get(name);
                    const startPos = comment.getStartPos();
                    const endPos = comment.getEndPos();
                    if (startPos.compare(paraPos) !== 1 && endPos.compare(paraPos) !== -1) {
                        return id;
                    }
                }
            } else  {// 跨页
                if (start <= pageIndex && pageIndex <= end) {
                    const comment = this.data.get(name);
                    const startPos = comment.getStartPos();
                    const endPos = comment.getEndPos();
                    if (startPos.compare(paraPos) !== 1 && endPos.compare(paraPos) !== -1) {
                        return id;
                    }
                }
            }
            if (start > pageIndex) {
                break;
            }
        }
        return -1;
    }

    public getStartCommentIdByPageIndex(pageIndex: number, pos: number): number {
        const comments = this.getAllComments();
        if (!comments.length) {
            return -1;
        }
        const contents = this.doc.content;
        let comment: Comment;
        for (let index = pos, length = contents.length; index < length; index++) {
            const content = contents[index];
            if (content.pageNum > pageIndex) {
                return;
            }
            content.getParaContentPos
            comment = comments.find((data) => {
                return data.getEndPos()
                .get(1) === index;
            });
            if (comment) {
                break;
            }
        }

        if (!comment) {
            return;
        }
        const endPortion = comment.getEndPortion();
        const portions = endPortion.paragraph.content;
        if (!portions.find((portion) => endPortion === portion)) {
            return;
        }
        const startPos = comment.getStartPos()
            .copy();
        startPos.shift();
        startPos.add(0);
        // const contentPos = startPos.shift();
        const curPageIndex: number = this.doc.content[startPos.shift()].getCurrentPageByPos(startPos);
        // const startPortion = comment.getSartPortions();
        // const curPageIndex = startPortion.paragraph.getAbsolutePage();
        if (curPageIndex !== pageIndex) {
            return comment.getId();
        }

        return -1;
    }

    public getAllComments(pageIndex?: number): Comment[] {
        this.buildCommentPageCaches();
        const comments: Comment[] = [];
        for (const {id, name} of this.sortData) {
            const comment = this.data.get(name);
            if (pageIndex !== undefined) {
                const portion = comment.getEndPortion();
                const para = portion.getParagragh();
                const curPage = para.getCurrentPageByPos(comment.getEndPos());
                if (pageIndex !== curPage) {
                    continue;
                }
            }
            comments.push(comment);
        }

        return comments;
    }

    public getAllCommentNames(): string[] {
        return this.sortData.map((data) => data.name);
    }

    public getCommentByName(name: string): Comment {
        return this.data.get(name);
    }

    public getCommentById(id: number): Comment {
        const datas = this.data;
        for (const [sname, comment] of datas) {
            if (comment.getId() === id) {
                return comment;
            }
        }
        return null;

    }

    public getCommmentByPos(pos: ParagraphContentPos): Comment {
        if (!pos) {
            return null;
        }
        const lowIndex = this.findCommentLowBoundByPos(pos);

        const sortData = this.sortData;
        const comment = this.data.get(sortData[lowIndex]?.name);
        if (!comment) {
            return null;
        }

        const endPos = comment.getEndPos();
        const startPos = comment.getStartPos();
        if (startPos.compare(pos) === -1 && pos.compare(endPos) === -1) {
            return comment;
        }

        return null;
    }

    public getCommentsByUsername(sUerName: string): Comment[] {
        const comments: Comment[] = [];
        const datas = this.data;
        for (const [sname, comment] of datas) {
            if (comment.getData()
                .getUserName() === sUerName) {
                comments.push(comment);
            }
        }

        return comments;
    }

    public getCommentsByName(name: string): Comment[] {
        const comments: Comment[] = [];
        const datas = this.data;
        for (const [sname, comment] of datas) {
            if (sname === name) {
                comments.push(comment);
            }
        }

        return comments;
    }

    public getCrossComments(startPos: ParagraphContentPos, endPos: ParagraphContentPos): ICommentResult[] {
        const sortData = this.sortData;
        const result: ICommentResult[] = [];
        if (!startPos || !endPos || !sortData.length) {
            return result;
        }

        const start = this.findCommentLowBoundByPos(startPos);
        const comments = this.data;
        let end = start;
        const length = sortData.length;
        while (end < length) {
            const comment = comments.get(sortData[end++].name);
            const left = comment.getStartPos();
            const right = comment.getEndPos();
            const leftCStart = left.compare(startPos);
            const leftCEnd = left.compare(endPos);
            const rightCStart = right.compare(startPos);
            const rightCEnd = right.compare(endPos);
            if ( rightCStart === -1) {
                continue;
            } else if (leftCEnd === 1) {
                break;
            }

            if (leftCStart === -1 && rightCEnd === 1) { // left < select < right
                result.push({type: CommentResultType.Outer, comment});
            } else if (leftCStart !== -1 && rightCEnd !== 1) { // start <= comment <= end
                result.push({type: CommentResultType.Inner, comment});
            } else if (leftCStart !== -1) { // left >= start
                result.push({type: CommentResultType.Left, comment});
            } else if (rightCEnd !== 1) { // right <= end
                result.push({type: CommentResultType.Right, comment});
            }
        }
        return result;
    }

    public getLastComment(): Comment {
        if (this.saveData) {
            return this.data.get(this.saveData.name);
        }
        const sortData = this.sortData;
        if (sortData.length === 0) {
            return;
        }
        let data;
        let maxId: number = -1;
        for (const obj of sortData) {
            if (maxId < obj.id) {
                data = obj;
                maxId = obj.id;
            }
        }

        return this.data.get(data.name);
    }

    /**
     * 收集区间内准备要删除的批注信息
     * @param startPos 起始位置
     * @param endPos 结束位置
     */
    public prepareDeletedComments(startPos: ParagraphContentPos, endPos: ParagraphContentPos): void {
        const info = this.resetDeletedCommentsInfo();
        for (const cross of this.getCrossComments(startPos, endPos)) {
            info.relates.push(cross.comment.getName());
            if (cross.type === CommentResultType.Inner) {
                info.fullDeletes.push(cross.comment);
            } else if (cross.type === CommentResultType.Left) {
                info.halfDeletes.push({
                    comment: cross.comment,
                    portion: cross.comment.getStartPortion(),
                });
            } else if (cross.type === CommentResultType.Right) {
                info.halfDeletes.push({
                    comment: cross.comment,
                    portion: cross.comment.getEndPortion(),
                });
            }
        }
    }

    /**
     * 根据读取的批注信息，返回创建的批注
     * @param id 读取的批注id
     * @param portion 创建的portion
     * @param bStart 是否为起始批注
     */
    public prepareReadedComment(id: string, portion: ParaPortion, bStart: boolean): Comment | null {
        if (!id || !portion) {
            return null;
        }
        if (this.preparedCommentId === id) {
            const comment = this.preparedComment;
            if (bStart) {
                return null;
            } else {
                comment.setEndPortion(portion);
                return comment;
            }
        } else {
            let comment = this.preparedComment;
            if (comment) {
                if (this.isValidComment(comment)) {
                    this.addComment(comment);
                    this.sortData.push({id: comment.getId(), name: comment.getName()});
                } else {
                    this.unvalidComments.push(comment);
                }
            }
            this.preparedComment = null;
            this.preparedCommentId = '';
            if (bStart) {
                comment = this.preparedComment = new Comment();
                comment.setStartPortion(portion);
                this.preparedCommentId = id;
                return comment;
            } else {
                return null;
            }
        }
    }

    public removeComment(id: number): void {
        const sortData = this.sortData;
        const index = sortData.findIndex((data) => id === data.id);
        if (index === -1) {
            return;
        }
        const obj = sortData[index];
        this.data.delete(obj.name);
        sortData.splice(index, 1);
    }

    public setName(sName: string, newName: string): boolean {
        if (sName === newName) {
            return false;
        }
        const saveData = this.saveData;
        if (saveData && saveData.name === sName) {
            saveData.name = newName;
        } else {
            const data = this.sortData.find((item) => item.name === sName);
            if (!data) {
                return false;
            }
            data.name = newName;
        }

        const comment = this.data.get(sName);
        comment.setName(newName);
        this.data.delete(sName);
        this.data.set(newName, comment);
        return true;
    }

    public setContent(name: string, content: string): boolean {
        const comment = this.data.get(name);
        if (!comment) {
            return false;
        }
        if (comment.getData()
            .setContent(content)
        ) {
            return true;
        }
        return false;
    }

    /**
     * 更新
     */
    public sortCommentByNearestInsert(): void {
        const saveData = this.saveData;
        if (saveData == null) {
            return;
        }

        const sortData = this.sortData;
        // let res = saveData.slice(0);
        const comments = this.data;
        if (sortData.length === 0) {
            sortData.push(saveData);
        } else {
            const startPos = comments.get(saveData.name)
                .getStartPos();
            const index = this.findCommentLowBoundByPos(startPos);
            sortData.splice(index, 0, saveData);
        }

        this.saveData = undefined;
        this.setDirty();
    }

    public getHistory(): History {
        return this.doc ? this.doc.getHistory() : null;
    }

    /** 获取指定pos最近的批注索引 */
    private findCommentLowBoundByPos(pos: ParagraphContentPos): number {
        const sortData = this.sortData;
        let high = sortData.length;
        const comments = this.data;
        let low = 0;
        let middle: number;

        while ( low < high ) {
            middle = Math.floor(( low + high ) / 2);
            const comment = comments.get(sortData[middle].name);
            if ( undefined === comment ) {
                high = middle;
                continue;
            }

            const startPos = comment.getStartPos();
            const endPos = comment.getEndPos();

            if (endPos.compare(pos) === -1) {
                low = middle + 1;
            } else if (startPos.compare(pos) === 1) {
                high = middle;
            } else {
                return middle;
            }
        }
        return low;
    }

    private isValidComment(comment: Comment): boolean {
        if (!comment) {
            return false;
        }
        if (comment.getStartPortion() && comment.getEndPortion()) {
            return true;
        }
        return false;
    }

    private resetDeletedCommentsInfo(): IDeletedInfo {
        return this.deletedCommentsInfo = {
            fullDeletes: [],
            halfDeletes: [],
            relates: [],
        };
    }

    /** 批注变更 */
    private setDirty(): void {
        this.commentPageCaches = [];
        GlobalEvent.setEvent(this.doc.id, GlobalEventName.CommentChange, CommentOperatorType.Update);
    }
}
