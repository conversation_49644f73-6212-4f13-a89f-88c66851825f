@import './global.less';
.watermark-box {
    .editor-line {
        .editor-checkbox {
            margin-top: 8px;
        }

        .text-descriptor {
            display: inline-block;
            padding-left: 20px;
        }

        .editor-input {
            width: 70%;
        }

        .editor-radio {
            display: block;
            padding-left: 20px;
            // position: relative;

            .radio-item {
                // position: absolute;
                // top: 10px;

                // &:nth-child(2n+1) {
                //     left: 20px;
                // }

                &:nth-child(2n) {
                    // right: 0;
                    // float: right;
                    // top: 8px;
                    margin-left: 100px;
                }
            }
        }

        &.fill-color-type {
            height: 56px;

            .radio-item {
                // &:nth-child(3), &:nth-child(4) {
                //     top: 30px;
                // }
                // width: 45%;
                &:nth-child(2) {
                    margin-left: 114px;
                }
            }
        }
    }

}