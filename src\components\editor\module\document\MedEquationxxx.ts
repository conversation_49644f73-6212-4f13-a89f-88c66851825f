import { EquationType } from '../../../../common/commonDefines';

const obj = {};
export class EquationRefs {
    public getEquationByType(type: EquationType): SVGElement {
        const dom = obj[type];
        if (!dom) {
            return this.getEquation(type);
        }

        return dom;
    }

    public getEquationSizeByType(type?: EquationType): {height: number, width: number} {
        let width: number;
        const height: number = 50;
        switch (type) {
            case EquationType.Menstruation: {
                width = 106;
                break;
            }
            default:
                width = 50;
        }

        return {width, height};
    }

    private getEquation(type: EquationType): any {
        let str: string;
        switch (type) {
            case EquationType.Fraction: {
                str = this.getFraEquation();
                break;
            }
            case EquationType.Menstruation: {
                str = this.getMenEquation();
                break;
            }
            case EquationType.Ordinary: {
                str = this.getOrdEquation();
                break;
            }
            default:
                return;
        }

        let dom = window['editor-equation-container'];
        if (!dom) {
            dom = document.createElement('div');
            dom.id = 'editor-equation-container';
            document.body.appendChild(dom);
        }

        const div = document.createElement('div');
        div.innerHTML = str.trim();
        const svg = div.firstChild;
        dom.appendChild(svg);
        obj[type] = svg;
        return svg;
    }

    private getOrdEquation(): string {
        const size = this.getEquationSizeByType();
        return `
        <svg
            class="medical-equation-wrapper"
            width="${size.width}"
            height="${size.height}"
            viewBox="0 20 300 300"
            xmlns="http://www.w3.org/2000/svg"
        >
            <rect width="100%" height="100%" fill="white"/>
            <g class="medical-equation">
                <line x1="0" y1="200" x2="300" y2="200" class="liner" style="stroke: black; stroke-width: 5;"/>
                <line
                    x1="150"
                    y1="90"
                    x2="150"
                    y2="300"
                    class="liner"
                    style="stroke: black; stroke-width: 5;"
                />
                <text x="75" y="160" text-anchor="middle" style="font-size: 80px;">
                    <tspan class="equation-text">1</tspan>
                </text>
                <text x="225" y="160" text-anchor="middle" style="font-size: 80px;">
                    <tspan class="equation-text">1</tspan>
                </text>
                <text x="75" y="290" text-anchor="middle" style="font-size: 80px;">
                    <tspan class="equation-text">1</tspan>
                </text>
                <text x="225" y="290" text-anchor="middle" style="font-size: 80px;">
                    <tspan class="equation-text">1</tspan>
                </text>
            </g>
        </svg>
        `;
    }

    private getFraEquation(): string {
        const size = this.getEquationSizeByType();
        return `
        <svg
            class="medical-equation-wrapper"
            width="${size.width}"
            height="${size.height}"
            viewBox="30 20 300 300"
            xmlns="http://www.w3.org/2000/svg"
        >
            <rect width="100%" height="100%" fill="white"/>
            <g class="medical-equation">
                <line x1="0" y1="200" x2="300" y2="200" class="liner" style="stroke: black; stroke-width: 5"/>
                <text x="150" y="160" text-anchor="middle" style="font-size: 80px;">
                    <tspan class="equation-text">1</tspan>
                </text>
                <text x="150" y="310" text-anchor="middle" style="font-size: 80px;">
                    <tspan class="equation-text">1</tspan>
                </text>
            </g>
        </svg>
        `;
    }

    private getMenEquation(): string {
        const size = this.getEquationSizeByType(EquationType.Menstruation);
        return `
        <svg
            class="medical-equation-wrapper"
            width="${size.width}"
            height="${size.height}"
            viewBox="0 0 636 300"
            xmlns="http://www.w3.org/2000/svg"
        >
            <rect width="100%" height="100%" fill="white"/>
            <g class="medical-equation">
                <g class="hidden-values unitEnabled" />
                <text x="192" y="220" text-anchor="end" style="font-size: 80px;">
                    <tspan class="equation-text">15岁</tspan>
                </text>

                <line x1="212" y1="200" x2="408" y2="200" class="liner" style="stroke: black; stroke-width: 5px;"/>
                <text x="310" y="160" text-anchor="middle" style="font-size: 80px;">
                    <tspan class="equation-text">3天</tspan>
                </text>
                <text x="310" y="290" text-anchor="middle" style="font-size: 80px;">
                    <tspan class="equation-text">30天</tspan>
                </text>

                <text x="428" y="220" text-anchor="start" style="font-size: 80px;">
                    <tspan class="equation-text">50岁</tspan>
                </text>
            </g>
        </svg>
        `;
    }
}
