export class TextArea {
    /**
     * textArea
     */
    private textArea: HTMLTextAreaElement | null = null;
    /**
     * foreignObject
     */
    private textAreaWrapper: any | null = null;

    /**
     * 是否在进行中文输入法
     */
    private inComposition: boolean | object = false;

    private isPrint: boolean;
    private bContinue: boolean;

    /**
     * parentNode
     */
    private host = null;

    constructor(host, isPrint?: boolean) {
        // console.log(host)
        this.host = host;
        this.isPrint = isPrint;
        this.initTextArea();
        console.log('TextArea---------------constructor----------------')
    }

    public wrapper = () => {
        return this.textAreaWrapper;
    }

    public focus = () => {
        this.textArea.focus();
    }

    public getTextAreaElem(): HTMLTextAreaElement {
        return this.textArea;
    }

    public setTextAreaPosition(x: number, y: number): void {
        const wrapper = this.textAreaWrapper;
        // wrapper.setAttribute("x", `${x}`);
        // wrapper.setAttribute("y", `${y}`);
        wrapper.style.left = x + 'px';
        wrapper.style.top = y + 'px';
    }

    public insertHiddenTextArea(parentNode: SVGElement): void {
        // foreignObject
        // const foreignObject = document.createElementNS("http://www.w3.org/2000/svg", "foreignObject");
        const foreignObject = document.createElement('div');
        const textArea = this.host.myRef.current.querySelector("#textarea_input");
        // foreignObject.setAttribute("width", '1px');
        // foreignObject.setAttribute("height", '1px');
        foreignObject.style.position = 'absolute';
        foreignObject.style.width = '1px';
        foreignObject.style.height = '1px';
        parentNode.parentNode.appendChild(foreignObject);
        foreignObject.appendChild(textArea);
        this.textAreaWrapper = foreignObject;
    }

    private blur = () => {
        this.textArea.blur();
    }

    /**
     * focus
     */
    private onFocus = () => {
        const cursorClassList = this.host.getCursor().classList;
        if (cursorClassList.contains('hidden')) {
            cursorClassList.remove('hidden');
        }
    }

    /**
     * 去除光标闪动
     * textArea值清空
     */
   private onBlur = () => {
        // console.log(this.host.state.documentCore.getCursorPosition())
        // console.log(this.host.getGMouseEvent());
        this.textArea.value = '';
        this.host.cursorBlur();
    }

    /**
     * 中文输入开始
     */
    private onCompositionStart = ( event: any ) => {
        if (this.bContinue === false) {
            return;
        }
        // console.log("onCompositionStart")
        /**
         * 似乎可以做撤销操作 具体看ace.js 2465
         */
        this.inComposition = true;
        this.host.compositionStart();
        // this.host.addEventListener("mousedown", this.onCompositionEnd);
        // console.log("onCompositionStart: " + event.data);
    }

    private onCompositionUpdate = (event: any ) => {
        if (this.bContinue === false) {
            return;
        }
        // console.log("onCompositionUpdate");
        // this.host.compositionUpdate();
        if ( event && event.data ) {
            // console.log("start time: " + new Date());
            // console.log(event.target);
            // this.host.insertCompositionInput(event.data, this.inComposition);
            // console.log("onCompositionUpdate: " + event.data);
        }
    }

    private onCompositionEnd = (event: any ) => {
        if (this.bContinue === false) {
            return;
        }
        // console.log("compositionend")
        // console.log(this.inComposition)
        this.host.compositionEnd(event.data, this.inComposition, event);
        this.inComposition = false;
        // this.host.compositionEnd();
    }

    private onInput = (event: any ) => {
        if (this.bContinue === false) {
            return;
        }
        // console.log("onInput")

        // 非中文输入
        if ( false === this.inComposition ) {
            if ( event.data ) {
                this.host.compositionStart();
            }
        } else {
            return;
        }

        // console.log("onInput: " + event.data);
        // console.log("onInput: " + new Date().getSeconds() + "." + new Date().getMilliseconds());// + "  " + event.data);
        const target = event.data;
        this.host.insertCompositionInput(target, this.inComposition);
    }

    private onKeyDown = (event: any ) => {
        // console.log("onKeyDown")
        // if ( event.key === 'Shift' && 0 === event.charCode )
        //    return;
        // if ( true === this.inComposition || event.key === "Shift" ) { // || 'Process' === event.key ) {
        //     return ;
        // }
        const host = this.host;
        if (host.externalEvent) {
            const bContinue = this.bContinue = host.externalEvent.nsoKeyPressedEvent(event);
            if (bContinue === false) {
                return;
            }
        }
        this.host.onKeyDown(event, this.inComposition);

        // Tab键需要额外处理：取消Tab事件的默认动作
        if ( event.key === "Tab" ) {
            event.preventDefault();
        }
    }

    // todo: reset
    private onKeyUp = (event: any ) => {
        if (this.bContinue === false) {
            return;
        }
        // console.log(this.inComposition)
        // console.log(event)
        // if ( event.key === 'Shift' && 0 === event.charCode )
        //    return;
        // if ( true === this.inComposition || ( 'Process' === event.key && 'Space' !== event.code ) ) {
        //     return ;
        // }
        this.host.onKeyUp(event, this.inComposition);
    }

    /**
     * 设置textArea属性 绑定事件
     */
    private initTextArea(): void {
        const textArea = this.host.myRef.current.querySelector('#textarea_input');
        if (textArea) {
            textArea.setAttribute("wrap", "off");
            textArea.setAttribute("autocorrect", "off");
            textArea.setAttribute("autocapitalize", "off");
            textArea.setAttribute("spellcheck", "false");
            this.textArea = textArea as HTMLTextAreaElement;
            this.addEvents();
        }
    }

    private addEvents(): void {
        const textArea = this.textArea;
        textArea.addEventListener("focus", this.onFocus);
        // textArea.addEventListener("blur", this.onBlur);
        // textArea.addEventListener("select", this.onSelect);
        // textArea.addEventListener("cut", this.onCut;
        // textArea.addEventListener("copy", this.onCopy);
        // textArea.addEventListener("paste", this.onPaste);
        if (this.isPrint !== true) {
            textArea.addEventListener("compositionstart", this.onCompositionStart);
            textArea.addEventListener("compositionupdate", this.onCompositionUpdate);
            textArea.addEventListener("compositionend", this.onCompositionEnd);
            textArea.addEventListener("input", this.onInput);
        }
        textArea.addEventListener("keydown", this.onKeyDown);
        textArea.addEventListener("keyup", this.onKeyUp);
    }
}
