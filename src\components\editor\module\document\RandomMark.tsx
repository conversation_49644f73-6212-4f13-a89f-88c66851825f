/* IFTRUE_WATER */
import { EXEC_DOM } from '@/common/commonDefines';
import { getCode } from '@/common/MarkFactory';
import { IMarkRect, IMarkBounding } from '@/common/MarkFactory';
import React from 'react';

interface IProp {
    strTmp?: IMarkRect;
    documentCore?: any;
    startPos?: IMarkBounding;
    release?: boolean;
    reset?: boolean;
    coreGen?: number;
}
const result = EXEC_DOM.a;
export class RandomMarkDom extends React.Component<IProp, {}> {
    private genMethodsClass: ((markAttr: any, mark: any) => any)[];
    private genMethodsNoClass: ((markAttr: any, mark: any) => any)[];
    private _strTmp: any;
    private _strChange: number = -1;
    constructor(prop: any) {
        super(prop);
        this.genMethodsClass = [];
        this.genMethodsNoClass = [];
        this.initMethod();
    }

    public render(): any {
        const { strTmp, documentCore, release, reset, startPos } = this.props;
        /* IFTRUE_WATER */
        if (documentCore) {
            const flag = documentCore.corePositionChange();
            if (this._strChange !== flag) {
                this._strChange = flag;
                this._strTmp = documentCore.getCorePosition(release, reset, startPos);
            }
        } else if (strTmp) {
            this._strTmp = strTmp;
        } else {
            this._strChange = -1;
            this._strTmp = null;
        }
        /* IFTRUE_WATER */
        const mark = this._strTmp;
        if (mark == null) {
            return null;
        }
        const markAttr = {
            x: mark.xs,
            y: mark.ys,
            fontSize: mark.font.fontSize,
            fontFamily: mark.font.fontFamily,
            fontStyle: mark.font.fontStyle,
            fontWeight: mark.font.fontWeight,
            fill: mark.font.fontColor,
        };
        let res = null;
        if (mark.needClass) {
            res = this.genMethodsClass[Math.floor(Math.random() * this.genMethodsClass.length)](markAttr, mark);
        } else {
            res = this.genMethodsNoClass[Math.floor(Math.random() * this.genMethodsNoClass.length)](markAttr, mark);
        }
        // tslint:disable-next-line: no-unused-expression
        (markAttr || mark) && res && !mark && result || result.push({[mark.content]: res});
        return res;
    }

    private reColor(color: string, tail: string = ''): string {
        return color.slice(0, 7) + tail;
    }

    private initMethod(): void {

        // g.class > text
        this.genMethodsClass.push((markAttr, mark) => {
            markAttr.fill = this.reColor(markAttr.fill, Math.random() < 0.5 ? 'FF' : '');
            return (
                <g className={mark.font.className}>
                    {this.buildEmptyG(markAttr, mark.font.className)}
                    <text {...markAttr} >
                        {mark.content}
                    </text>
                    {this.buildEmptyG(markAttr, mark.font.className)}
                </g>
            );
        });
        // g + g.class > text
        this.genMethodsClass.push((markAttr, mark) => {
            markAttr.fill = this.reColor(markAttr.fill, Math.random() < 0.5 ? 'FF' : '');
            return (
                <g  className={Math.random() < 0.4 ? getCode() : null}>
                    <g className={mark.font.className}>
                        <text {...markAttr} >
                            {mark.content}
                        </text>
                    </g>
                    {this.buildEmptyG(markAttr, mark.font.className)}
                </g>
            );
        });

        //  g.class > g + text
        this.genMethodsClass.push((markAttr, mark) => {
            markAttr.fill = this.reColor(markAttr.fill, Math.random() < 0.5 ? 'FF' : '');
            return (
                <g className={mark.font.className}>
                    <g className={Math.random() < 0.4 ? getCode() : null}>
                        {this.buildEmptyG(markAttr, mark.font.className)}
                        <text {...markAttr} >
                            {mark.content}
                        </text>
                    </g>
                </g>
            );
        });

        // text
        this.genMethodsNoClass.push((markAttr, mark) => {
            return (
                <text
                    {...markAttr}
                >
                    {mark.content}
                </text>
            );
        });
        // g + text
        this.genMethodsNoClass.push((markAttr, mark) => {
            return (
                <g>
                    {this.buildEmptyG(markAttr, mark.font.className)}
                    <text
                        {...markAttr}
                    >
                        {mark.content}
                    </text>
                </g>
            );
        });
        // g > ( g > text )
        this.genMethodsNoClass.push((markAttr, mark) => {
            return (
                <g>
                    <g className={mark.font.className}>{this.buildEmptyG(markAttr, mark.font.className)}</g>
                    <g>
                        {this.buildEmptyG(markAttr, mark.font.className)}
                        <text {...markAttr} >
                            {mark.content}
                        </text>
                    </g>
                </g>
            );
        });
    }

    private buildEmptyG(markAttr: any, className: string): any {
        const gs = [];
        for (let idx = 0, len = Math.floor(Math.random() * 4); idx < len; idx++) {
            if (Math.random() < 0.5) {
                gs.push(
                    <g key={Math.random().toFixed(10)} className={Math.random() < 0.5 ? className : null}>
                        { Math.random() < 0.5 ? <text {...markAttr}>{' '}</text> : null }
                    </g>
                );
            } else {
                gs.push(<text key={Math.random().toFixed(10)} {...markAttr} >{' '}</text>);
            }
        }
        return gs;
    }
}
EXEC_DOM.a = result;
/* FITRUE_WATER */