import * as React from 'react';
import OtherEquation from './Base';
import '../../../style/equation.less';
import ToothBitMap from './ToothBitMap';
import LightPositioningMap from './LightPositioningMap';
import PupilMapping from './PupilMapping';
import FetalHeartChart from './FetalHeartChart';
import Rule from './Rule';
import Menstruation2 from './Menstruation2';
import Menstruation3 from './Menstruation3';
import Menstruation4 from './Menstruation4';
import { EquationType } from '../../../../../common/commonDefines';
import DiseasedUpperTeeth from './DiseasedUpperTeeth';
import DiseasedLowerTeeth from './DiseasedLowerTeeth';
import PermOrDeciToothBitmap from './PermOrDeciToothBitmap';

interface IProps {
    documentCore: any;
    visible: boolean;
    id: string;
    equation?: any;
    close: (name: string | number, bRefresh?: boolean) => void;
}

interface IState {
    bRefresh: boolean;
}

export default class Paragraph extends React.Component<IProps, {}> {
    private bOther: boolean;
    private bToothBitMap: boolean;
    private bLightPositioningMap: boolean;
    private bPupilMapping: boolean;
    private bFetalHeartChart: boolean;
    private bRule: boolean;
    private bMenstruation4: boolean;
    private bMenstruation3: boolean;
    private bMenstruation2: boolean;
    private bDiseasedUpperTeeth: boolean;
    private bDiseasedLowerTeeth: boolean;
    private bPermanentToothBitmap: boolean;
    private bDeciduousToothBitmap: boolean;
    constructor(props: IProps) {
        super(props);
        // this.init();
    }

    public render(): any {
        return (
            <React.Fragment>
                {this.renderOther()}
                {this.renderToothBitMap()}
                {this.renderLightPositioningMap()}
                {this.renderPupilMapping()}
                {this.renderFetalHeartChart()}
                {this.renderRule()}
                {this.renderMenstruation2()}
                {this.renderMenstruation3()}
                {this.renderMenstruation4()}
                {this.renderDiseasedUpperTeeth()}
                {this.renderDiseasedLowerTeeth()}
                {this.renderPermanentToothBitmap()}
                {this.renderDeciduousToothBitmap()}
            </React.Fragment>
        );
    }

    public componentDidMount(): void {
        this.init(this.props);
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        if (!nextProps.visible) {
            return;
        }
        this.init(nextProps);
    }

    private init(props: IProps): void {
        const equation = props.equation;
        if (equation) {
            switch (equation.equationType) {
                case EquationType.ToothBitMap: {
                    this.bToothBitMap = true;
                    break;
                }
                case EquationType.LightPositioningMap: {
                    this.bLightPositioningMap = true;
                    break;
                }
                case EquationType.PupilMapping: {
                    this.bPupilMapping = true;
                    break;
                }
                case EquationType.FetalHeartChart: {
                    this.bFetalHeartChart = true;
                    break;
                }
                case EquationType.Rule: {
                    this.bRule = true;
                    break;
                }
                case EquationType.Menstruation4: {
                    this.bMenstruation4 = true;
                    break;
                }
                case EquationType.Menstruation3: {
                    this.bMenstruation3 = true;
                    break;
                }
                case EquationType.Menstruation2: {
                    this.bMenstruation2 = true;
                    break;
                }
                case EquationType.DiseasedLowerTeeth: {
                    this.bDiseasedLowerTeeth = true;
                    break;
                }
                case EquationType.DiseasedUpperTeeth: {
                    this.bDiseasedUpperTeeth = true;
                    break;
                }
                case EquationType.PermanentToothBitmap: {
                    this.bPermanentToothBitmap = true;
                    break;
                }
                case EquationType.DeciduousToothBitmap: {
                    this.bDeciduousToothBitmap = true;
                    break;
                }
                default: {
                    this.bOther = true;
                }
            }
        } else {
            this.bOther = true;
        }
        this.setState({});
    }

    private renderFetalHeartChart(): any {
        if (this.bFetalHeartChart === undefined) {
            return null;
        }

        return (
            <FetalHeartChart
                id='bFetalHeartChart'
                visible={this.bFetalHeartChart}
                equation={this.props.equation}
                documentCore={this.props.documentCore}
                close={this.close}
            />
        );
    }

    private renderMenstruation2(): any {
        if (this.bMenstruation2 === undefined) {
            return null;
        }

        return (
            <Menstruation2
                id='bMenstruation2'
                visible={this.bMenstruation2}
                equation={this.props.equation}
                documentCore={this.props.documentCore}
                close={this.close}
            />
        );
    }
    private renderMenstruation3(): any {
        if (this.bMenstruation3 === undefined) {
            return null;
        }

        return (
            <Menstruation3
                id='bMenstruation3'
                visible={this.bMenstruation3}
                equation={this.props.equation}
                documentCore={this.props.documentCore}
                close={this.close}
            />
        );
    }

    private renderMenstruation4(): any {
        if (this.bMenstruation4 === undefined) {
            return null;
        }

        return (
            <Menstruation4
                id='bMenstruation4'
                visible={this.bMenstruation4}
                equation={this.props.equation}
                documentCore={this.props.documentCore}
                close={this.close}
            />
        );
    }

    private renderRule(): any {
        if (this.bRule === undefined) {
            return null;
        }

        return (
            <Rule
                id='bRule'
                visible={this.bRule}
                equation={this.props.equation}
                documentCore={this.props.documentCore}
                close={this.close}
            />
        );
    }

    private renderPupilMapping(): any {
        if (this.bPupilMapping === undefined) {
            return null;
        }

        return (
            <PupilMapping
                id='bPupilMapping'
                visible={this.bPupilMapping}
                equation={this.props.equation}
                documentCore={this.props.documentCore}
                close={this.close}
            />
        );
    }

    private renderToothBitMap(): any {
        if (this.bToothBitMap === undefined) {
            return null;
        }

        return (
            <ToothBitMap
                id='bToothBitMap'
                visible={this.bToothBitMap}
                equation={this.props.equation}
                documentCore={this.props.documentCore}
                close={this.close}
            />
        );
    }

    private renderLightPositioningMap(): any {
        if (this.bLightPositioningMap === undefined) {
            return null;
        }

        return (
            <LightPositioningMap
                id='bLightPositioningMap'
                visible={this.bLightPositioningMap}
                equation={this.props.equation}
                documentCore={this.props.documentCore}
                close={this.close}
            />
        );
    }

    private renderDiseasedUpperTeeth(): any {
        if (this.bDiseasedUpperTeeth === undefined) {
            return null;
        }

        return (
            <DiseasedUpperTeeth
                id='bDiseasedUpperTeeth'
                visible={this.bDiseasedUpperTeeth}
                equation={this.props.equation}
                documentCore={this.props.documentCore}
                close={this.close}
            />
        );
    }

    private renderDiseasedLowerTeeth(): any {
        if (this.bDiseasedLowerTeeth === undefined) {
            return null;
        }

        return (
            <DiseasedLowerTeeth
                id='bDiseasedLowerTeeth'
                visible={this.bDiseasedLowerTeeth}
                equation={this.props.equation}
                documentCore={this.props.documentCore}
                close={this.close}
            />
        );
    }
    
    private renderPermanentToothBitmap(): any {
        if (this.bPermanentToothBitmap === undefined) {
            return null;
        }

        return (
            <PermOrDeciToothBitmap
                id='bPermanentToothBitmap'
                visible={this.bPermanentToothBitmap}
                equation={this.props.equation}
                documentCore={this.props.documentCore}
                close={this.close}
            />
        );
    }
    
    private renderDeciduousToothBitmap(): any {
        if (this.bDeciduousToothBitmap === undefined) {
            return null;
        }

        return (
            <PermOrDeciToothBitmap
                isDeciduous={true}
                id='bDeciduousToothBitmap'
                visible={this.bDeciduousToothBitmap}
                equation={this.props.equation}
                documentCore={this.props.documentCore}
                close={this.close}
            />
        );
    }

    private renderOther(): any {
        if (this.bOther === undefined) {
            return null;
        }

        return (
            <OtherEquation
                id='bOther'
                visible={this.bOther}
                equation={this.props.equation}
                documentCore={this.props.documentCore}
                close={this.close}
            />
        );
    }

    private close = (id: string, bRefresh: boolean) => {
        this[id] = false;
        // this.setState({});
        this.props.close(this.props.id, bRefresh);
    }

}
