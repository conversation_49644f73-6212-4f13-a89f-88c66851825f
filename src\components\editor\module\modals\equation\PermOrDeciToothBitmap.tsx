import * as React from 'react';
import Dialog from '../../../ui/Dialog';
import Button from '../../../ui/Button';
// import { message } from '../../../../../common/Message';
import { ParaEquation } from '../../../../../model/core/Paragraph/ParaDrawing';
import {IFRAME_MANAGER} from '../../../../../common/IframeManager';
import { DentalBitmap, initialControl, initialFacing, IToothStruct } from './toothBitmapControl';

interface IDialogProps {
    isDeciduous?: boolean;
    documentCore: any;
    visible: boolean;
    id: string;
    equation: ParaEquation;
    close: (name: string | number, bRefresh?: boolean) => void;
}

interface ITestContent {
    value1?: string;
    value2?: string;
    value3?: string;
}
interface IState {
    bRefresh: boolean;
}

/** 乳牙(Deciduous)/恒牙(Permanent)牙位图 */
export default class PermOrDeciToothBitmap extends React.Component<IDialogProps, IState> {
    private visible: boolean;
    private data: IToothStruct;
    private oldData: ITestContent;
    private isDeciduous: boolean = false; // 是否是乳牙，否则为恒牙
    private bitmapRef: React.RefObject<DentalBitmap>;
    constructor(props: IDialogProps) {
        super(props);
        this.visible = this.props.visible;
        this.data = this.initialData();
        this.state = {
            bRefresh: false,
        };
        this.isDeciduous = props.isDeciduous;
        this.bitmapRef = React.createRef();
    }

    public render(): any {
        return (
            <Dialog
                id={this.props.id}
                preventDefault={true}
                visible={this.visible}
                open={this.open}
                footer={this.renderFooter()}
                width={this.props.isDeciduous ? 300 : 400}
                // height={450}
                title={this.isDeciduous ? '乳牙牙位图' : '恒牙牙位图'}
            >
                <DentalBitmap 
                    measureNode={IFRAME_MANAGER.getTextElement()}
                    data={this.data}
                    ref={this.bitmapRef}
                />

            </Dialog>
        );
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        this.visible = nextProps.visible;
    }

    private renderFooter(): any {
        return (
            <span>
                <Button onClick={this.close}>取消</Button>
                <Button type='primary' onClick={this.confirm}>确认</Button>
            </span>
        );
    }

    private open = (): void => {
        const equation = this.props.equation;
        // let equationElemDom: HTMLElement;
        if (equation.equationElem) {
            
            const svgDom = new DOMParser().parseFromString(
                equation.equationElem,
                'text/xml',
            ).documentElement;
            const source = svgDom.dataset['source'];
            if (source) {
                this.data = JSON.parse(source.replace(/'/g, '"'));
            } else {
                this.data = this.initialData();
            }
        } else {
            this.data = this.initialData();
        }
        this.setState({});
    }

    private close = (bRefresh: boolean = false): void => {
        this.props.close(this.props.id, bRefresh);
        this.visible = false;
        this.setState({});
    }

    private confirm = (): void => {
        if (!this.bitmapRef.current) {
            this.close();
            return;
        }
        const res = this.bitmapRef.current.confirm();
        const documentCore = this.props.documentCore;
        const drawObj = documentCore.getDrawingObjects();
        const svgConvertedURI = drawObj.convertSVGToImageString(res.html);
        documentCore.setDrawingProp(this.props.equation.name, {
            width: res.width,
            src: svgConvertedURI,
            equationElem: res.html,
        });
        this.close(true);
    }

    private initialData() {
        const teethTitles = this.isDeciduous ? [
            '第二乳磨牙',
            '第一乳磨牙',
            '乳尖牙',
            '乳侧尖牙',
            '乳中切牙',
        ] : [
            '第三磨牙',
            '第二磨牙',
            '第一磨牙',
            '第二前磨牙',
            '第一前磨牙',
            '尖牙',
            '侧切牙',
            '中切牙',
        ];
        const controlTags = this.isDeciduous ? [
            'Ⅴ', 'Ⅳ', 'Ⅲ', 'Ⅱ', 'Ⅰ',
        ] : [
            '8', '7', '6', '5', '4', '3', '2', '1'
        ];
        const len = teethTitles.length;
        // 定义内容存储的数据结构
        const teethStruct: IToothStruct = {
            controlTags,
            titles: teethTitles,
            maxilla: initialFacing(len * 2),
            control: {
                maxilla: initialControl(len),
                mandible: initialControl(len),
            },
            mandible: initialFacing(len * 2, true),
            tags: ['P', 'L', 'B', 'D', 'O', 'M'],
        };
        return teethStruct;
    }
}
