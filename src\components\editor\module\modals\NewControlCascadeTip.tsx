import * as React from 'react';
import {
    GlobalEvent as gEvent,
    GlobalEventName as gEventName,
} from '../../../../common/GlobalEvent';

interface ITipProps {
    host: any;
    newControl: any;
    region?: any;
    left: number;
    top: number;
    borderActiveColor: string; // 选中时边框颜色
    borderStyle?: string;
    content?: string; // 显示文本
    backgroundColor: string;
    backgroundActiveColor: string;
    bDbClicked?: boolean;
    hasCascade: boolean;
    hasSum: boolean;
}

interface IState {
    bRefresh: boolean;
}

export default class NewControlCascadeTip extends React.Component<ITipProps, IState> {
    private _tipDom: any;
    private host: any;
    private newControl: any;
    private region: any;
    private realBgColor: string;
    private readColor: string;
    private defaultBorderColor: string;
    private borderActiveColor: string;
    private borderStyle: string;
    private backgroundActiveColor: string;
    private content: string;
    private bDbClicked: boolean;
    private name: string;

    constructor(props: any) {
        super(props);
        this.state = {
            bRefresh: false,
        };
        this.host = this.props.host;
        this._tipDom = React.createRef();
        this.newControl = this.props.newControl;
        this.region = this.props.region;
        // 选择应该显示的背景颜色
        const { hasSum, backgroundColor, backgroundActiveColor, borderActiveColor } = this.props;
        this.backgroundActiveColor = backgroundActiveColor;
        this.defaultBorderColor = hasSum ? borderActiveColor : 'black';
        this.realBgColor = hasSum ? backgroundActiveColor : backgroundColor;
        this.readColor = hasSum ? '#009FFF' : 'black';
        this.borderStyle = this.props.borderStyle ? this.props.borderStyle : 'solid';
        this.borderActiveColor = borderActiveColor;
        this.bDbClicked = this.props.bDbClicked ? this.props.bDbClicked : false;
        this.content = this.props.content ? this.props.content : '有';
        this.name = this.props.newControl && this.props.newControl.getNewControlName() ||
                    this.props.region && this.props.region.getName();
    }

    public render(): any {
        const { left, top, hasCascade, hasSum } = this.props;
        let tipContent = hasCascade ? this.content ? this.content : '有' : '';
        tipContent += !this.bDbClicked ? '添加' : '√';
        const style: any = {
            backgroundColor: this.realBgColor,
            left: left + 'px',
            top: top + 'px',
            borderColor: this.defaultBorderColor,
            borderWidth: '1px',
            borderStyle: this.borderStyle,
            color: this.readColor,
            // zoom: 1 / window.devicePixelRatio,
        };
        return (
            <div
                ref={this._tipDom}
                tabIndex={-1}
                className={'newControl-fixed-tips'}
                style={style}
            >
                <span>{tipContent}</span>
            </div>
        );
    }

    public componentDidMount(): void {
        const dom = this._tipDom.current;
        dom.addEventListener('focus', this.handleFocus);
        dom.addEventListener('blur', this.handleBlur);
        dom.addEventListener('dblclick', this.handleDbClick);
        dom.addEventListener('click', this.defaultClick);
        gEvent.addEvent(this.host.docId, 'resetCascadeManager', this.resetCascadeManager);
        gEvent.addEvent(this.host.docId, 'resetCascadeManagerFlag', this.onChangeFlags);
    }

    public componentWillUnmount(): void {
        const dom = this._tipDom.current;
        if (!dom) {
            console.log('undelete event cascade tip');
            return;
        }
        dom.removeEventListener('focus', this.handleFocus);
        dom.removeEventListener('blur', this.handleBlur);
        dom.removeEventListener('dblclick', this.handleDbClick);
        dom.removeEventListener('click', this.defaultClick);
        gEvent.deleteEvent(this.host.docId, 'resetCascadeManager', this.resetCascadeManager);
        gEvent.deleteEvent(this.host.docId, 'resetCascadeManagerFlag', this.onChangeFlags);
    }

    private defaultClick = (e): void => {
        e.preventDefault();
        e.stopPropagation();
    }

    private onChangeFlags = (names: string[], flag: boolean = true): void => {
        if (!names.includes(this.name)) {
            if (this.bDbClicked === true) {
                this.bDbClicked = false;
                this.setState({});
            }
            return;
        }
        if (this.bDbClicked === flag) {
            return;
        }
        this.bDbClicked = flag;
        this.setState({});
    }

    private resetCascadeManager = (): void => {
        if (!this.bDbClicked) {
            return;
        }

        this.bDbClicked = false;
        this.setState({});
    }

    private handleFocus = (e: any): void => {
        e.stopPropagation();
        const dom = this._tipDom.current as HTMLElement;
        dom.style.borderColor = this.borderActiveColor;
        dom.style.backgroundColor = this.backgroundActiveColor;
    }

    private handleBlur = (e: any): void => {
        e.stopPropagation();
        const dom = this._tipDom.current as HTMLElement;
        dom.style.borderColor = this.defaultBorderColor;
        dom.style.backgroundColor = this.realBgColor;
    }

    private handleDbClick = (e: any): void => {
        e.preventDefault();
        e.stopPropagation();
        // dispatch dblClick event
        this.bDbClicked = !this.bDbClicked;
        gEvent.setEvent(this.host.docId, gEventName.TipDbClick,
                        this.newControl ? this.newControl : this.region,
                        this.bDbClicked);
        this.setState({bRefresh: !this.state.bRefresh});
    }

}
