import { ITableBorder, ITableProperty } from '../commonDefines';
import { TablePro, ColPro, RowPro } from './apollo/NodeType';
import { TableCell } from '../../model/core/Table/TableCell';
import { DocumentBorder, DocumentColor } from '../../model/core/Style';
// import { Table } from '../../model/core/Table';
import { TableRow } from '../../model/core/Table/TableRow';
import { TableManager } from '../../model/core/Table/TableManager';
import { TableBase } from '@/model/core/TableBase';

export class TableCore {
    private _host: any;
    private _tableManager: TableManager;
    constructor(host: any) {
        this._host = host;
        this._tableManager = host.doc.getTableManager();
    }

    public parseTableAttrs(attrs: any, table: TableBase): void {
        if (!attrs) {
            return;
        }
        const props: ITableProperty = {};
        props.tableCellMargins = {
            top: this.parseAttrToNum(attrs[TablePro.CellTop]),
            bottom: this.parseAttrToNum(attrs[TablePro.CellBottom]),
            left: this.parseAttrToNum(attrs[TablePro.CellLeft]),
            right: this.parseAttrToNum(attrs[TablePro.CellRight]),
        };
        // props.tableBorders = {
        //     top: this.getBorders(ColPro.BorderTop, attrs),
        //     bottom: this.getBorders(ColPro.BorderBottom, attrs),
        //     left: this.getBorders(ColPro.BorderLeft, attrs),
        //     right: this.getBorders(ColPro.BorderRight, attrs),
        //     insideH: this.getBorders(ColPro.BorderInsideH, attrs),
        //     insideV: this.getBorders(ColPro.BorderInsideV, attrs),
        // } as any,
        props['borders'] = {
            top: this.parseBorder(ColPro.BorderTop, attrs)[0],
            bottom: this.parseBorder(ColPro.BorderBottom, attrs)[0],
            left: this.parseBorder(ColPro.BorderLeft, attrs)[0],
            right: this.parseBorder(ColPro.BorderRight, attrs)[0],
            insideH: this.parseBorder(ColPro.BorderInsideH, attrs)[0],
            insideV: this.parseBorder(ColPro.BorderInsideV, attrs)[0],
        },
        props.bCanAddRow = !this.parseBoolean(attrs[TablePro.AddRowProtect]);
        props.bCanDeleteRow = !this.parseBoolean(attrs[TablePro.DelRowProtect]);
        props.bFixedRowHeight = !this.parseBoolean(attrs[TablePro.FixedRowHeight]);
        props.bFixedColWidth = !this.parseBoolean(attrs[TablePro.FixedColWidth]);
        props.bDeleteProtect = this.parseBoolean(attrs[TablePro.DeleteProtect]);
        props.bReadOnly = this.parseBoolean(attrs[TablePro.EditProtect]);
        props.customProperty = this.parseCustomAttr(attrs[TablePro.CustomProperty]);
        props.repeatHeaderNum = this.parseAttrToNum(attrs[TablePro.HeaderNum]);
        props.tableName = this._tableManager.getUniqueTableName();
        table.setPropertys(props);
    }

    public getBorders(name: string, props: any): ITableBorder {
        let border = props[name];
        if (!border) {
            return;
        }
        border = border[0];
        if (!border) {
            return;
        }
        const colors = border[ColPro.Color].split(',');
        const res: ITableBorder = {
            size: this.parseAttrToNum(border[ColPro.Linetype]),
            value: this.parseAttrToNum(border[ColPro.Style]),
            color: {r: this.parseAttrToNum(colors[0]), g: this.parseAttrToNum(colors[1]),
                b: this.parseAttrToNum(colors[2])},
        };
        return res;
    }

    public parseRowAttrs(attrs: any, row: TableRow): void {
        row.setHeight(this.parseAttrToNum(attrs[TablePro.Val]), this.parseAttrToNum(attrs[RowPro.Rule]));
        row.setTableHeader(this.parseBoolean(attrs[RowPro.TableHeader]));
    }

    public parseColAttrs(cell: TableCell, attrs: any): void {
        const info: any = {
            top: this.parseBorder('top', attrs),
            bottom: this.parseBorder('bottom', attrs),
            left: this.parseBorder('left', attrs),
            right: this.parseBorder('right', attrs),
            maxLeft: this.parseAttrToNum(attrs[ColPro.MaxLeft]),
            maxRight: this.parseAttrToNum(attrs[ColPro.MaxRight]),
            bottomAfterCount: this.parseAttrToNum(attrs[ColPro.BottomAfterCount]),
            bottomBeforeCount: this.parseAttrToNum(attrs[ColPro.BottomBeforeCount]),
        };
        cell.setCellProtected(this.parseBoolean(attrs[ColPro.Protected]));
        cell.setBorderInfo(info);
        cell.setVMerge(this.parseAttrToNum(attrs[TablePro.Val]));
    }

    public setTableProps(table: TableBase, props: any): void {
        table.property = props.copy();
    }

    public setCellProps(cell: TableCell, props: any, info: any): void {
        cell.setCellProtected(props.bProtected);
        cell.setVMerge(props.verticalMerge);
        cell.setBorderInfo(info.copy());
        // cell.property = props;
    }

    public setRowProps(row: TableRow, props: any): void {
        const height = props.height;
        row.setHeight(height.value, height.hRule);
        row.setTableHeader(props.bTableHeader);
    }

    public setTableGrid(maxCol: number, table: TableBase): void {
        const grid: number[] = [];
        const doc = this._host.doc;
        const pageFields = doc.getPageFields(doc.curPage);
        let tableWidth = pageFields.xLimit - pageFields.x; // + 2 * 1.9; // mm
        // const cols = parseInt(node.attrs['val'], 10) || 1;
        tableWidth = Math.max(tableWidth, maxCol * 2); // * 1.9);

        // 均分表格：每列的宽度
        for (let index = 0; index < maxCol; index++) {
            grid[index] = tableWidth / maxCol;
        }
        table.setTableGrid(grid);
    }

    public setTableGrid2(grid: number[], table: TableBase): void {
        table.setTableGrid(grid);
    }

    public setTableGrid3(maxCol: number, attrs: any, table: TableBase): void {
        const grid: number[] = [];
        for (let index = 0; index < maxCol; index++) {
            const col = attrs[TablePro.Col + index];
            if (!col) {
                continue;
            }
            grid.push(this.parseAttrToNum(col.width));
        }
        table.setTableGrid(grid);
    }

    public setDefaultCell(table: TableBase): void {
        // table.moveCursorToStartPos();
    }

    public resetPaste(): void {
        this._tableManager?.resetInsertTableCaches();
    }

    public addTable(table: TableBase): void {
        this._tableManager?.addInsertTable(table);
    }

    private parseBorder(name: string, attrs: any): any[] {
        const borders = attrs[name];
        if ( null == borders) {
            return [];
        }

        return borders.map((border) => {
            const colors = border[ColPro.Color].split(',');
            const res: any = {
                value: this.parseAttrToNum(border[ColPro.Style]),
                bPrint: this.parseBoolean(border[ColPro.BPrint]),
                color: new DocumentColor(this.parseAttrToNum(colors[0]), this.parseAttrToNum(colors[1]),
                    this.parseAttrToNum(colors[2]), this.parseBoolean(border[ColPro.BAuto])),
                size: this.parseAttrToNum(border[ColPro.Linetype]),
                space: this.parseAttrToNum(border[ColPro.Space]) || 0,
                dashArray: border[ColPro.DashArray],
                bVisible: this.parseBoolean(border[ColPro.Visible]),
            };
            return new DocumentBorder(res);
        });
    }

    private parseBoolean(str: string): boolean {
        if (!str) {
            return;
        }

        return str === '1';
    }

    private parseAttrToNum(str: string): number {
        if (!str) {
            return;
        }

        return parseFloat(str);
    }

    private parseCustomAttr(str: string): any {
        if (!str) {
            return;
        }

        let res: any;
        try {
            res = JSON.parse(str);
        } catch (error) {
            return;
        }
        if (!res || res.length === 0) {
            return;
        }
        return res;
        // .map((prop) => {
        //     return {
        //         ...prop,
        //     };
        // });
    }
}
