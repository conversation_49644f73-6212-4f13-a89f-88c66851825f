import * as React from 'react';
import { numtoFixed2 } from '../../../../common/commonDefines';
import { IDocumentParaElement } from '../../../../model/ParaElementProperty';

export default class ParaElementBase extends React.Component<
    IDocumentParaElement,
    {}
> {
    constructor(props: IDocumentParaElement) {
        super(props);
    }

    public render(): any {
        const {
            index,
            type,
            // width,
            positionX,
            positionY,
            dy,
            bVisible,
            bViewSecret,
            // content,
        } = this.props;
        const props = {
            // width,
            x: numtoFixed2(positionX),
            y: numtoFixed2(positionY),
            dy: numtoFixed2(dy),
        };

        let content = true === bViewSecret ? '*' : this.props.content;
        if (13 === type) {
            if (true !== bVisible) {
                content = ' ';
            }
            return (
                <tspan
                    textLength='2'
                    lengthAdjust='spacingAndGlyphs'
                    fill='#ACB4C1'
                    fontStyle='normal'
                    key={index}
                    item-key={index}
                    {...props}
                >
                    {content}
                </tspan>
            );
        } else {
            return (
                <tspan key={index} item-key={index} {...props}>
                    {content}
                </tspan>
            );
        }
    }
}
