import * as React from 'react';
import { PageProperty } from '../../model/core/Style';
import Content from './Content';
import { IDocumentContent, DocumentCore } from '../../model/DocumentCore';
import { DocCurPosType } from '../../model/core/Document';
import { SectionIndex } from '../../model/core/Sections';
import Paragraph from '../../model/core/Paragraph';
import HeaderFooter from '../../model/core/HeaderFooter';
// import {parse as circularParse, stringify as circularStringify} from 'flatted/esm';
import { NewControlDefaultSetting } from '../../common/commonDefines';

interface IPageProps extends PageProperty {
  showPageBorder?: boolean; // 边界是否显示
  content: IDocumentContent;
  index: number;
  editorContainer?: any;
  documentCore?: DocumentCore;
  handleRefresh?: any;
  // curPosType: number;
  host?: any;
  cursorType: string;
  cursorInNewControlName?: string;
}

interface IPageState {
  // curPosType: number;
}

// 假设边框长度20
const BORDER_LENGTH = 20;
export default class Page extends React.Component<IPageProps, IPageState> {
  constructor(props: IPageProps) {
    super(props);
    // console.log('Page---------------constructor----------------')
    // this.state = {
    //   curPosType: DocCurPosType.Content,
    // };
  }

  public render(): any {
    const { showPageBorder, width, height, index, content, cursorType } = this.props;
    const padding = this.renderPageBorder();
    // console.log(showPageBorder, content);
    return (
      <svg width={width} height={height} page-id={index} type='page' className='print-style' cursor={cursorType}>
        {showPageBorder && padding}
        {this.renderHeaderFooter()}
        {content && this.renderContent()}
        {this.renderTableNewBorder()}
      </svg>
    );
  }

  /**
   * get width of content
   */
  private getTrulyWidth(): any {
    const { width, height, paddingRight, paddingLeft} = this.props;
    return width - paddingLeft - paddingRight;
  }

  /**
   * get height of content
   */
  private getTrulyHeight(): any {
    const { height, paddingTop, paddingBottom } = this.props;
    return height - paddingTop - paddingBottom;
  }

  // Header/footer observation
  // A header/footer's size/position should only resort from SectionPageMargins, not PageProperty.
  // Rendering should easily use those props prepared from recalculate().
  // If still need complex calculation in this file, sth is off
  // |_(|-) and _|(-|) are the indicators of page content section.
  // They have nothing to do with header/footer's size/position calculation.
  // But If header/footer area overlaps them, need to deal with the ui overlapping. (*1)
  // Page content's start pos is decided from Max(PageProperty.paddingTop, SectionPageMargins.paddingTop)
  // i.e, the bigger height between "header's height" and 页上边距
  // likewise as of Page content's end pos -> Max("footer's height", 页下边距)
  // if ("header's height" > 页上边距), refer to (*1) line. At that time, |- and -| are meaningless
  // likewise as of |_ and _| at page bottom.

  getHeaderFromTop() {
    const {documentCore} = this.props;
    return documentCore.getPageMarginsHeader();
  }

  // everything needs for calculation of header/footer should be included in below 2 functions
  getSectionPageMargins() {
    const {documentCore} = this.props;
    return documentCore.getSectionPageMargins();
  }

  getSectionPageSize() {
    const {documentCore} = this.props;
    return documentCore.getSectionPageSize();
  }

  /**
   * get header CONTENT area's height
   */
  getHeaderContentTrulyHeight() {
    const headerFromTop = this.getHeaderFromTop();

    const sectionPageMargins = this.getSectionPageMargins();
    // console.log(sectionPageMargins);

    return sectionPageMargins.paddingTop - headerFromTop;
  }

  getFooterFromBottom() {
    const {documentCore} = this.props;
    return documentCore.getPageMarginsFooter();
  }

  /**
   * get footer CONTENT area's height
   */
  getFooterContentTrulyHeight() {
    const sectionPageMargins = this.getSectionPageMargins();
    const footerFromBottom = this.getFooterFromBottom();

    return sectionPageMargins.paddingBottom - footerFromBottom;
  }

  /**
   * get position of content
   */
  private getTrulyPosition(): any {
    const { width, height, paddingRight, paddingLeft, paddingTop, paddingBottom} = this.props;
    return {
      topLeft: {
        x: paddingLeft,
        y: paddingTop,
      },
      topRight: {
        x: width - paddingRight,
        y: paddingTop,
      },
      bottomLeft: {
        x: paddingLeft,
        y: height - paddingBottom,
      },
      bottomRight: {
        x: width - paddingRight,
        y: height - paddingBottom,
      },
    };
  }

  /**
   * get position of header content
   */
  private getHeaderContentTrulyPosition() {
    const sectionPageSize = this.getSectionPageSize();
    const sectionPageMargins = this.getSectionPageMargins();
    const {width} = sectionPageSize;
    const {paddingTop, paddingLeft, paddingRight, paddingBottom,
          header: headerFromTop, footer: footerFromBottom} = sectionPageMargins;
    // console.log(sectionPageSize)
    // console.log(sectionPageMargins)
    return {
      topLeft: {
        x: paddingLeft,
        y: headerFromTop,
      },
      topRight: {
        x: width - paddingRight,
        y: headerFromTop,
      },
      bottomLeft: {
        x: paddingLeft,
        y: paddingTop,
      },
      bottomRight: {
        x: width - paddingRight,
        y: paddingTop,
      },
    };
  }

  /**
   * get position of footer content
   */
  private getFooterContentTrulyPosition() {
    const sectionPageSize = this.getSectionPageSize();
    const sectionPageMargins = this.getSectionPageMargins();
    const {width, height} = sectionPageSize;
    const {paddingTop, paddingLeft, paddingRight, paddingBottom,
          header: headerFromTop, footer: footerFromBottom} = sectionPageMargins;

    return {
      topLeft: {
        x: paddingLeft,
        y: height - paddingBottom,
      },
      topRight: {
        x: width - paddingRight,
        y: height - paddingBottom,
      },
      bottomLeft: {
        x: paddingLeft,
        y: height - footerFromBottom,
      },
      bottomRight: {
        x: width - paddingRight,
        y: height - footerFromBottom,
      },
    };
  }

  private renderHeaderFooter(): any {
    const {
      topLeft: { x: topLeftX, y: topLeftY },
      topRight: { x: topRightX, y: topRightY },
      bottomLeft: { x: bottomLeftX, y: bottomLeftY },
      bottomRight: { x: bottomRightX, y: bottomRightY},
    } = this.getTrulyPosition();
    const {
      topLeft: headerTopLeft,
      topRight: headerTopRight,
      bottomLeft: headerBottomLeft,
      bottomRight: headerBottomRight,
    } = this.getHeaderContentTrulyPosition();
    const {
      topLeft: footerTopLeft,
      topRight: footerTopRight,
      bottomLeft: footerBottomLeft,
      bottomRight: footerBottomRight,
    } = this.getFooterContentTrulyPosition();

    const {documentCore, index} = this.props;
    const logicDocument = documentCore.getDocument();

    const header = documentCore.getHdrFtr().pages[index].header;
    const footer = documentCore.getHdrFtr().pages[index].footer;

    // header content bottom line
    let lastLineInHeaderY = this.getLastLineInHeaderY(header);

    // line separator. Max(页上边距，页眉正文线y)
    const lineSeparatorY = Math.max(topLeftY, lastLineInHeaderY);

    // console.log(footer)
    const isHeaderFooterEnabled = logicDocument.curPos.type === DocCurPosType.HdrFtr ? true : false;
    const headerContentLineState = isHeaderFooterEnabled ? "show" : (header ? "opacity" : "hide");
    const footerContentLineState = isHeaderFooterEnabled ? "show" : (footer ? "opacity" : "hide");
    return (
      <g className="header-footer">
        {/* header */}
        <line className={headerContentLineState} x1={headerBottomLeft.x} y1={lastLineInHeaderY} x2={headerBottomRight.x} y2={lastLineInHeaderY} stroke="dimgray" strokeWidth="1"/>
        <line className={isHeaderFooterEnabled ? "" : "hide"} x1={0} y1={lineSeparatorY} x2={this.props.width} y2={lineSeparatorY} stroke="gray" strokeDasharray="4,2"/>
        {/* footer */}
        <line className={isHeaderFooterEnabled ? "" : "hide"} x1={0} y1={footerTopLeft.y} x2={this.props.width} y2={footerTopRight.y} stroke="gray" strokeDasharray="4,2"/>
        {/* content */}
        {header && this.renderHeaderContent(headerContentLineState)}
        {footer && this.renderFooterContent(footerContentLineState)}
      </g>
    );
  }

  private getLastLineInHeaderY(header: HeaderFooter) {
    let lastLineInHeaderY = 0;
    if (header) {
      const contentArr = header.content.content;
      const element = contentArr[contentArr.length - 1] as Paragraph;
      lastLineInHeaderY = element.getPageBounds(0).bottom + 3;
    }
    return lastLineInHeaderY;
  }

  private getPageBorderPosition(type: string): any {
    const {
      topLeft: { x: topLeftX, y: topLeftY },
      topRight: { x: topRightX, y: topRightY },
      bottomLeft: { x: bottomLeftX, y: bottomLeftY },
      bottomRight: { x: bottomRightX, y: bottomRightY}
    } = this.getTrulyPosition();

    switch (type) {
      case 'topLeft':
        return `${topLeftX},${topLeftY - BORDER_LENGTH} ${topLeftX},${topLeftY} ${topLeftX - BORDER_LENGTH},${topLeftY}`;
      case 'topRight':
        return `${topRightX},${topRightY - BORDER_LENGTH} ${topRightX},${topRightY} ${topRightX + BORDER_LENGTH},${topRightY}`;
      case 'bottomLeft':
        return `${bottomLeftX},${bottomLeftY + BORDER_LENGTH} ${bottomLeftX},${bottomLeftY} ${bottomLeftX - BORDER_LENGTH},${bottomLeftY}`;
      case 'bottomRight':
        return `${bottomRightX},${bottomRightY + BORDER_LENGTH} ${bottomRightX},${bottomRightY} ${bottomRightX + BORDER_LENGTH},${bottomRightY}`;
      default:
        return '';
    }
  }

  /**
   *  render padding of page's content
   */
  private renderPageBorder(): any {
    const styles = {
      "stroke": "#ccc",
      "fill": "white",
    };
    const positions = ['topLeft', 'topRight', 'bottomLeft', 'bottomRight'];
    const {
      0: topLeft,
      1: topRight,
      2: bottomLeft,
      3: bottomRight
    } = positions.map((value) => {
      return {
        points: this.getPageBorderPosition(value),
        ...styles,
      }
    });
    return (
      <g>
        <polyline {...topLeft}/>
        <polyline {...topRight}/>
        <polyline {...bottomLeft}/>
        <polyline {...bottomRight}/>
      </g>
    );
  }

  /**
   * render content
   */
  private renderContent(): any {
    const { index, content, documentCore } = this.props;
    // const height = this.getTrulyHeight();
    // const width = this.getTrulyWidth();
    const { topLeft: { x, y } } = this.getTrulyPosition();
    const logicDocument = documentCore.getDocument();
    // console.log(content)

    const header = documentCore.getHdrFtr().pages[index].header;
    // header content bottom line
    let lastLineInHeaderY = this.getLastLineInHeaderY(header);

    // line separator. Max(页上边距，页眉正文线y)
    const lineSeparatorY = Math.max(y, lastLineInHeaderY);

    return (
      <g className={logicDocument.curPos.type === DocCurPosType.HdrFtr ? "header-footer-enabled" : ""}>
        {/* <rect width={width} height={height} x={x} y={y} fill="white"/> */}
        <g className='newcontrol-backgroundColor-container'>
            {this.renderNewControlBackgroundContent()}
        </g>
        <Content pageIndex={index} content={content} editorContainer={this.props.editorContainer}
            documentCore={this.props.documentCore} handleRefresh={this.props.handleRefresh} host={this.props.host}/>
      </g>
    );
  }

  private renderHeaderContent(headerContentLineState: string): any {
    const { index, documentCore, content } = this.props;
    const header = documentCore.getHdrFtr().pages[index].header;

    const headerContent: IDocumentContent = documentCore.getContentByPageId(index, SectionIndex.Header);
    // console.log(headerContent)

    // let paraPortion = headerContent.paragraphs[0].content[0];
    // console.log(paraPortion)
    // paraPortion.positionY = 69;
    // for (const paraText of paraPortion.content) {
      // paraText.positionY = 69;
    // }
    const height = this.getHeaderContentTrulyHeight();
    // const width = this.getTrulyWidth();
    const width = this.getSectionPageSize().width;
    const { topLeft: { x, y } } = this.getHeaderContentTrulyPosition();
    const headerContentState = headerContentLineState;
    return (
      <g className={headerContentState}>
        {/* <rect> indicates content area, no real use */}
        {/* <rect width={width} height={height} x={x} y={y} fill="white"/> */}
        <Content pageIndex={index} content={headerContent} editorContainer={this.props.editorContainer} documentCore={this.props.documentCore} 
                 handleRefresh={this.props.handleRefresh} host={this.props.host}/>
      </g>
    );
  }

  private renderFooterContent(footerContentLineState: string): any {
    const { index, documentCore, content } = this.props;
    const footer = documentCore.getHdrFtr().pages[index].footer;
    const footerContent: IDocumentContent = documentCore.getContentByPageId(index, SectionIndex.Footer);

    const height = this.getFooterContentTrulyHeight();
    // const width = this.getTrulyWidth();
    const width = this.getSectionPageSize().width;
    const { topLeft: { x, y } } = this.getFooterContentTrulyPosition();

    // console.log(footerContent)

    return (
      <g className={footerContentLineState}>
        {/* <rect width={width} height={height} x={x} y={y + 2} fill="white"/> */}
        <Content pageIndex={index} content={footerContent} editorContainer={this.props.editorContainer} documentCore={this.props.documentCore} 
                 handleRefresh={this.props.handleRefresh} host={this.props.host}
        />
      </g>
    );
  }

  private renderNewControlBackgroundContent(): any {
    const newControls = this.props.documentCore.getNewControlsWithoutFocus(this.props.index,
      this.props.cursorInNewControlName);
    return newControls.map((newControl) => {
      if ( false === newControl.isHiddenBackground() ) {
        const newControlsBounds = this.props.documentCore.getNewControlBounds(newControl.getNewControlName());
        if ( newControlsBounds && 0 < newControlsBounds.bounds.length) {
          return newControlsBounds.bounds.map((bound, index) => {
            if ( bound && this.props.index === bound.pageIndex ) {
              return (<rect width={bound.width} key={index} height={bound.height} x={bound.x} y={bound.y} // + 3}
                  fill={NewControlDefaultSetting.DefaultBackgroundColor}/>);
            }
          });
        }
      }
    });
  }

  private renderTableNewBorder = () => {
    // if ( this.props.documentCore.isMovingTableBorder() ) {
    //   const {bColumn, x, y, width, height} = this.props.documentCore.getTableNewBorder();
    //   if ( bColumn ) {
    //     return (<line x1={x} y1={0} x2={x} y2={height} stroke='#000' strokeDasharray='4,2'/>);
    //   } else {
    //     return (<line x1={0} y1={y} x2={0 + width} y2={y} stroke='#000' strokeDasharray='4,2'/>);
    //   }
    // }

    const bDisplay = this.props.documentCore.isMovingTableBorder();
    let bColumn = false;
    let x1, y1, x2, y2, width, height = 0;

    if ( bDisplay ) {
      const border = this.props.documentCore.getTableNewBorder();
      bColumn = border.bColumn;
      width = border.width;
      height = border.height;
      x1 = bColumn ? border.x : 0;
      x2 = bColumn ? border.x : width;
      y1 = bColumn ? 0 : border.y;
      y2 = bColumn ? height : border.y;
    }
    return (<g style={{display: bDisplay ? 'block' : 'none'}}>
      <line x1={x1} y1={y1} x2={x2} y2={y2} stroke={`rgb(51,102,204)`} strokeDasharray='4,2'/>
    </g>);
  }

  // componentWillUnmount() {
  //   console.log('Page--------componentWillUnmount---------------')
  // }
}
