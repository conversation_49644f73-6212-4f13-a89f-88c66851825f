// 请不要直接修改TextMeasureSize.js， 修改完请在终端运行node setTextCache.js即可
export const CACHES = new Map(); 
export function initMeasureCaches() {
    const items = {"12":[['↵'].map(i=> [i,0,12,1]),['我','，','。','；','’','“','？','、','（','）','【','】','：','⊙','⭘','℃','Ⅰ','Ⅱ'].map(i=> [i,12,12,1]),[' ','0','1','2','3','4','5','6','7','8','9','a','b','c','d','e','f','g','h','i','j','k','l','m','n','o','p','q','r','s','t','u','v','w','x','y','z','A','B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q','R','S','T','U','V','W','X','Y','Z',',','.',';','/','?','(',')','[',']','=','-','+','*','<','>',':','{','}'].map(i=> [i,6,12,1]),['☑','☐'].map(i=> [i,10.34,12,1]),['◯'].map(i=> [i,13,12,1])],"14":[['↵'].map(i=> [i,0,14,1]),['我','，','。','；','’','“','？','、','（','）','【','】','：','⊙','⭘','℃','Ⅰ','Ⅱ'].map(i=> [i,14,14,1]),[' ','0','1','2','3','4','5','6','7','8','9','a','b','c','d','e','f','g','h','i','j','k','l','m','n','o','p','q','r','s','t','u','v','w','x','y','z','A','B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q','R','S','T','U','V','W','X','Y','Z',',','.',';','/','?','(',')','[',']','=','-','+','*','<','>',':','{','}'].map(i=> [i,7,14,1]),['☑','☐'].map(i=> [i,12.06,14,1]),['◯'].map(i=> [i,15.24,14,1])],"15":[['↵'].map(i=> [i,0,15,1]),['我','！','？','《','》','；','，','、','。','（','）','：','【','】','“','”','‘','’','Ⅰ','Ⅱ'].map(i=> [i,15,15,1]),[' ','0','1','2','3','4','5','6','7','8','9','a','b','c','d','e','f','g','h','i','j','k','l','m','n','o','p','q','r','s','t','u','v','w','x','y','z','A','B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q','R','S','T','U','V','W','X','Y','Z',',','.','/','?','\"',':','<','>','=','~','`','!','@','#','$','%','^','&','*','(',')','_',';','-','+','{','}','[',']','\\','|','\''].map(i=> [i,8,15,1]),['☑'].map(i=> [i,12.92,15,1]),['☐'].map(i=> [i,12.94,15,1]),['⊙','⭘','℃'].map(i=> [i,15.02,15,1]),['◯'].map(i=> [i,16.84,15,1])],"16":[['↵'].map(i=> [i,0,16,1]),['我','，','。','；','’','“','？','、','（','）','【','】','：','⊙','⭘','℃','Ⅰ','Ⅱ'].map(i=> [i,16,16,1]),[' ','0','1','2','3','4','5','6','7','8','9','a','b','c','d','e','f','g','h','i','j','k','l','m','n','o','p','q','r','s','t','u','v','w','x','y','z','A','B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q','R','S','T','U','V','W','X','Y','Z',',','.',';','/','?','(',')','[',']','=','-','+','*','<','>',':','{','}'].map(i=> [i,8,16,1]),['☑','☐'].map(i=> [i,13.78,16,1]),['◯'].map(i=> [i,17,16,1])],"18":[['我','，','。','；','’','“','？','、','（','）','【','】','Ⅰ','Ⅱ','：','⊙','⭘','℃'].map(i=> [i,18,18,1]),[' ','0','1','2','3','4','5','6','7','8','9','a','b','c','d','e','f','g','h','i','j','k','l','m','n','o','p','q','r','s','t','u','v','w','x','y','z','A','B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q','R','S','T','U','V','W','X','Y','Z',',','.',';','/','?','(',')','[',']','=','-','+','*','<','>',':','{','}'].map(i=> [i,9,18,1]),['☑','☐'].map(i=> [i,15.52,18,1]),['◯'].map(i=> [i,19.59,18,1]),['↵'].map(i=> [i,10.19,18,1])],"19":[['↵'].map(i=> [i,0,19,1]),['我','！','？','《','》','；','，','、','。','（','）','：','【','】','“','”','‘','’','Ⅰ','Ⅱ'].map(i=> [i,19,19,1]),[' ','0','1','2','3','4','5','6','7','8','9','a','b','c','d','e','f','g','h','i','j','k','l','m','n','o','p','q','r','s','t','u','v','w','x','y','z','A','B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q','R','S','T','U','V','W','X','Y','Z',',','.','/','?','\"',':','<','>','=','~','`','!','@','#','$','%','^','&','*','(',')','_',';','-','+','{','}','[',']','\\','|','\''].map(i=> [i,9.5,19,1]),['☑','☐'].map(i=> [i,16.38,19,1]),['⊙','⭘','℃'].map(i=> [i,19.02,19,1]),['◯'].map(i=> [i,21.34,19,1])],"20":[['我','，','。','；','’','“','？','、','（','）','【','】','：','⊙','⭘','℃','Ⅰ','Ⅱ'].map(i=> [i,20,20,1]),[' ','0','1','2','3','4','5','6','7','8','9','a','b','c','d','e','f','g','h','i','j','k','l','m','n','o','p','q','r','s','t','u','v','w','x','y','z','A','B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q','R','S','T','U','V','W','X','Y','Z',',','.',';','/','?','(',')','[',']','=','-','+','*','<','>',':','{','}'].map(i=> [i,10,20,1]),['↵'].map(i=> [i,0,20,1]),['☑','☐'].map(i=> [i,17.23,20,1]),['◯'].map(i=> [i,21.77,20,1])],"24":[['↵'].map(i=> [i,0,24,1]),['我','，','。','；','’','“','？','、','（','）','【','】','：','⊙','⭘','℃','Ⅰ','Ⅱ'].map(i=> [i,24,24,1]),[' ','0','1','2','3','4','5','6','7','8','9','a','b','c','d','e','f','g','h','i','j','k','l','m','n','o','p','q','r','s','t','u','v','w','x','y','z','A','B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q','R','S','T','U','V','W','X','Y','Z',',','.',';','/','?','(',')','[',']','=','-','+','*','<','>',':','{','}'].map(i=> [i,12,24,1]),['☑','☐'].map(i=> [i,20.67,24,1]),['◯'].map(i=> [i,26.12,24,1])],"32":[['↵'].map(i=> [i,0,32,1]),['我','，','。','；','’','“','？','、','（','）','【','】','：','⊙','⭘','℃','Ⅰ','Ⅱ'].map(i=> [i,32,33,1]),[' ','0','1','2','3','4','5','6','7','8','9','a','b','c','d','e','f','g','h','i','j','k','l','m','n','o','p','q','r','s','t','u','v','w','x','y','z','A','B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q','R','S','T','U','V','W','X','Y','Z',',','.',';','/','?','(',')','[',']','=','-','+','*','<','>',':','{','}'].map(i=> [i,16,33,1]),['☑','☐'].map(i=> [i,27.56,33,1]),['◯'].map(i=> [i,34.83,33,1])],"48":[['我','，','。','；','：','’','“','？','、','（','）','【','】','⊙','⭘','℃','Ⅰ','Ⅱ'].map(i=> [i,48,48,1]),[' ','0','1','2','3','4','5','6','7','8','9','a','b','c','d','e','f','g','h','i','j','k','l','m','n','o','p','q','r','s','t','u','v','w','x','y','z','A','B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q','R','S','T','U','V','W','X','Y','Z',',','.',';','/','?','(',')','[',']','=','-','+','*','<','>',':','{','}'].map(i=> [i,24,48,1]),['↵'].map(i=> [i,0,48,1]),['☑','☐'].map(i=> [i,41.34,48,1]),['◯'].map(i=> [i,52.24,48,1])],"Times New Roman58.7":[['我'].map(i=> [i,58.7,65,1]),[' '].map(i=> [i,14.67,65,1]),['0','1','2','3','4','5','6','7','8','9','b','d','g','h','k','n','o','p','q','u','v','x','y','*'].map(i=> [i,29.35,65,1]),['a','c','e','z','?','“'].map(i=> [i,26.05,65,1]),['f','r','I','(',')','[',']','-','’','Ⅰ'].map(i=> [i,19.55,65,1]),['i','j','l','t',';','/',':'].map(i=> [i,16.31,65,1]),['m'].map(i=> [i,45.66,65,1]),['s','J'].map(i=> [i,22.84,65,1]),['w','A','D','G','H','K','N','O','Q','U','V','X','Y'].map(i=> [i,42.39,65,1]),['B','C','R'].map(i=> [i,39.15,65,1]),['E','L','T','Z'].map(i=> [i,35.86,65,1]),['F','P','S'].map(i=> [i,32.65,65,1]),['M'].map(i=> [i,52.19,65,1]),['W'].map(i=> [i,55.4,65,1]),[',','.'].map(i=> [i,14.68,65,1]),['=','+','<','>'].map(i=> [i,33.1,65,1]),['，','。','；','？','、','（','）','【','】','：','⊙','⭘'].map(i=> [i,58.7,65,1]),['Ⅱ'].map(i=> [i,37.95,65,1]),['↵'].map(i=> [i,0,65,1]),['{','}'].map(i=> [i,28.17,65,1]),['☑','☐'].map(i=> [i,50.56,65,1]),['◯'].map(i=> [i,63.89,65,1]),['℃'].map(i=> [i,60.1,65,1])],"Times New Roman48":[['我','，','。','；','？','、','（','）','【','】','：','⊙','⭘'].map(i=> [i,48,53,1]),[' ',',','.'].map(i=> [i,12,53,1]),['0','1','2','3','4','5','6','7','8','9','b','d','g','h','k','n','o','p','q','u','v','x','y','*'].map(i=> [i,24,53,1]),['a','c','e','z','?','“'].map(i=> [i,21.3,53,1]),['f','r','I','(',')','[',']','-','’','Ⅰ'].map(i=> [i,15.98,53,1]),['i','j','l','t',';','/',':'].map(i=> [i,13.34,53,1]),['m'].map(i=> [i,37.34,53,1]),['s','J'].map(i=> [i,18.68,53,1]),['w','A','D','G','H','K','N','O','Q','U','V','X','Y'].map(i=> [i,34.66,53,1]),['B','C','R'].map(i=> [i,32.02,53,1]),['E','L','T','Z'].map(i=> [i,29.32,53,1]),['F','P','S'].map(i=> [i,26.7,53,1]),['M'].map(i=> [i,42.68,53,1]),['W'].map(i=> [i,45.3,53,1]),['=','+','<','>'].map(i=> [i,27.07,53,1]),['Ⅱ'].map(i=> [i,31.03,53,1]),['↵'].map(i=> [i,0,53,1]),['{','}'].map(i=> [i,23.04,53,1]),['☑','☐'].map(i=> [i,41.34,53,1]),['◯'].map(i=> [i,52.24,53,1]),['℃'].map(i=> [i,49.15,53,1])],"Times New Roman37.3":[['我','，','。','；','？','、','（','）','【','】','：','⊙','⭘'].map(i=> [i,37.3,41,1]),[' ',',','.'].map(i=> [i,9.32,41,1]),['0','1','2','3','4','5','6','7','8','9','b','d','g','h','k','n','o','p','q','u','v','x','y','*'].map(i=> [i,18.65,41,1]),['a','c','e','z','?','“'].map(i=> [i,16.56,41,1]),['f','r','I','(',')','[',']','-','’','Ⅰ'].map(i=> [i,12.42,41,1]),['i','j','l','t',';','/',':'].map(i=> [i,10.36,41,1]),['m'].map(i=> [i,29.01,41,1]),['s','J'].map(i=> [i,14.52,41,1]),['w','A','D','G','H','K','N','O','Q','U','V','X','Y'].map(i=> [i,26.94,41,1]),['B','C','R'].map(i=> [i,24.88,41,1]),['E','L','T','Z'].map(i=> [i,22.78,41,1]),['F','P','S'].map(i=> [i,20.74,41,1]),['M'].map(i=> [i,33.17,41,1]),['W'].map(i=> [i,35.21,41,1]),['=','+','<','>'].map(i=> [i,21.04,41,1]),['Ⅱ'].map(i=> [i,24.11,41,1]),['↵'].map(i=> [i,0,41,1]),['{','}'].map(i=> [i,17.9,41,1]),['☑','☐'].map(i=> [i,32.13,41,1]),['◯'].map(i=> [i,40.6,41,1]),['℃'].map(i=> [i,38.19,41,1])],"Times New Roman34.7":[['我','，','。','；','？','、','（','）','【','】','：','⊙','⭘'].map(i=> [i,34.7,39,1]),[' '].map(i=> [i,8.67,39,1]),['0','1','2','3','4','5','6','7','8','9','b','d','g','h','k','n','o','p','q','u','v','x','y','*'].map(i=> [i,17.35,39,1]),['a','c','e','z','?','“'].map(i=> [i,15.4,39,1]),['f','r','I','(',')','[',']','-','’','Ⅰ'].map(i=> [i,11.56,39,1]),['i','j','l','t',';','/',':'].map(i=> [i,9.64,39,1]),['m'].map(i=> [i,26.99,39,1]),['s','J'].map(i=> [i,13.5,39,1]),['w','A','D','G','H','K','N','O','Q','U','V','X','Y'].map(i=> [i,25.06,39,1]),['B','C','R'].map(i=> [i,23.14,39,1]),['E','L','T','Z'].map(i=> [i,21.2,39,1]),['F','P','S'].map(i=> [i,19.3,39,1]),['M'].map(i=> [i,30.85,39,1]),['W'].map(i=> [i,32.75,39,1]),[',','.'].map(i=> [i,8.68,39,1]),['=','+','<','>'].map(i=> [i,19.57,39,1]),['Ⅱ'].map(i=> [i,22.43,39,1]),['↵'].map(i=> [i,0,39,1]),['{','}'].map(i=> [i,16.66,39,1]),['☑','☐'].map(i=> [i,29.89,39,1]),['◯'].map(i=> [i,37.77,39,1]),['℃'].map(i=> [i,35.53,39,1])],"Times New Roman32":[['我','，','。','；','？','、','（','）','【','】','⊙','⭘','：'].map(i=> [i,32,36,1]),[' ',',','.'].map(i=> [i,8,36,1]),['0','1','2','3','4','5','6','7','8','9','b','d','g','h','k','n','o','p','q','u','v','x','y','*'].map(i=> [i,16,36,1]),['a','c','e','z','?','“'].map(i=> [i,14.2,36,1]),['f','r','I','(',')','[',']','-','’','Ⅰ'].map(i=> [i,10.66,36,1]),['i','j','l','t',';','/',':'].map(i=> [i,8.89,36,1]),['m'].map(i=> [i,24.89,36,1]),['s','J'].map(i=> [i,12.45,36,1]),['w','A','D','G','H','K','N','O','Q','U','V','X','Y'].map(i=> [i,23.11,36,1]),['B','C','R'].map(i=> [i,21.34,36,1]),['E','L','T','Z'].map(i=> [i,19.55,36,1]),['F','P','S'].map(i=> [i,17.8,36,1]),['M'].map(i=> [i,28.45,36,1]),['W'].map(i=> [i,30.2,36,1]),['=','+','<','>'].map(i=> [i,18.05,36,1]),['↵'].map(i=> [i,0,36,1]),['{','}'].map(i=> [i,15.36,36,1]),['☑','☐'].map(i=> [i,27.56,36,1]),['◯'].map(i=> [i,34.83,36,1]),['℃'].map(i=> [i,32.77,36,1]),['Ⅱ'].map(i=> [i,20.69,36,1])],"Times New Roman29.3":[['我','，','。','；','？','、','（','）','【','】','⊙','⭘','：'].map(i=> [i,29.3,32,1]),[' ',',','.'].map(i=> [i,7.32,32,1]),['0','1','2','3','4','5','6','7','8','9','b','d','g','h','k','n','o','p','q','u','v','x','y','*'].map(i=> [i,14.65,32,1]),['a','c','e','z','?','“'].map(i=> [i,13,32,1]),['f','r','I','(',')','[',']','-','’','Ⅰ'].map(i=> [i,9.76,32,1]),['i','j','l','t',';','/',':'].map(i=> [i,8.14,32,1]),['m'].map(i=> [i,22.79,32,1]),['s','J'].map(i=> [i,11.4,32,1]),['w','A','D','G','H','K','N','O','Q','U','V','X','Y'].map(i=> [i,21.16,32,1]),['B','C','R'].map(i=> [i,19.54,32,1]),['E','L','T','Z'].map(i=> [i,17.9,32,1]),['F','P','S'].map(i=> [i,16.3,32,1]),['M'].map(i=> [i,26.05,32,1]),['W'].map(i=> [i,27.65,32,1]),['=','+','<','>'].map(i=> [i,16.52,32,1]),['↵'].map(i=> [i,0,32,1]),['{','}'].map(i=> [i,14.06,32,1]),['☑','☐'].map(i=> [i,25.24,32,1]),['◯'].map(i=> [i,31.89,32,1]),['℃'].map(i=> [i,30,32,1]),['Ⅱ'].map(i=> [i,18.94,32,1])],"Times New Roman26.7":[['我','，','。','；','？','、','（','）','【','】','：','⊙','⭘'].map(i=> [i,26.7,30,1]),[' ',',','.'].map(i=> [i,6.67,30,1]),['0','1','2','3','4','5','6','7','8','9','b','d','g','h','k','n','o','p','q','u','v','x','y','*'].map(i=> [i,13.35,30,1]),['a','c','e','z','?','“'].map(i=> [i,11.85,30,1]),['f','r','I','(',')','[',']','-','’','Ⅰ'].map(i=> [i,8.89,30,1]),['i','j','l','t',';','/',':'].map(i=> [i,7.42,30,1]),['m'].map(i=> [i,20.77,30,1]),['s','J'].map(i=> [i,10.39,30,1]),['w','A','D','G','H','K','N','O','Q','U','V','X','Y'].map(i=> [i,19.28,30,1]),['B','C','R'].map(i=> [i,17.81,30,1]),['E','L','T','Z'].map(i=> [i,16.31,30,1]),['F','P','S'].map(i=> [i,14.85,30,1]),['M'].map(i=> [i,23.74,30,1]),['W'].map(i=> [i,25.2,30,1]),['=','+','<','>'].map(i=> [i,15.06,30,1]),['Ⅱ'].map(i=> [i,17.26,30,1]),['↵'].map(i=> [i,0,30,1]),['{','}'].map(i=> [i,12.82,30,1]),['☑','☐'].map(i=> [i,23,30,1]),['◯'].map(i=> [i,29.06,30,1]),['℃'].map(i=> [i,27.34,30,1])],"Times New Roman24":[['我','，','。','；','？','、','（','）','【','】','⊙','⭘','：'].map(i=> [i,24,26,1]),[' ',',','.'].map(i=> [i,6,26,1]),['0','1','2','3','4','5','6','7','8','9','b','d','g','h','k','n','o','p','q','u','v','x','y','*'].map(i=> [i,12,26,1]),['a','c','e','z','?','“'].map(i=> [i,10.65,26,1]),['f','r','I','(',')','[',']','-','’','Ⅰ'].map(i=> [i,7.99,26,1]),['i','j','l','t',';','/',':'].map(i=> [i,6.67,26,1]),['m'].map(i=> [i,18.67,26,1]),['s','J'].map(i=> [i,9.34,26,1]),['w','A','D','G','H','K','N','O','Q','U','V','X','Y'].map(i=> [i,17.33,26,1]),['B','C','R'].map(i=> [i,16.01,26,1]),['E','L','T','Z'].map(i=> [i,14.66,26,1]),['F','P','S'].map(i=> [i,13.35,26,1]),['M'].map(i=> [i,21.34,26,1]),['W'].map(i=> [i,22.65,26,1]),['=','+','<','>'].map(i=> [i,13.54,26,1]),['↵'].map(i=> [i,0,26,1]),['{','}'].map(i=> [i,11.52,26,1]),['☑','☐'].map(i=> [i,20.67,26,1]),['◯'].map(i=> [i,26.12,26,1]),['℃'].map(i=> [i,24.57,26,1]),['Ⅱ'].map(i=> [i,15.52,26,1])],"Times New Roman21.3":[['我','，','。','；','？','、','（','）','【','】','⊙','⭘','：'].map(i=> [i,21.3,24,1]),[' '].map(i=> [i,5.32,24,1]),['0','1','2','3','4','5','6','7','8','9','b','d','g','h','k','n','o','p','q','u','v','x','y','*'].map(i=> [i,10.65,24,1]),['a','c','e','z','?','“'].map(i=> [i,9.45,24,1]),['f','r','I','(',')','[',']','-','’','Ⅰ'].map(i=> [i,7.09,24,1]),['i','j','l','t',';','/',':'].map(i=> [i,5.92,24,1]),['m'].map(i=> [i,16.57,24,1]),['s','J'].map(i=> [i,8.29,24,1]),['w','A','D','G','H','K','N','O','Q','U','V','X','Y'].map(i=> [i,15.38,24,1]),['B','C','R'].map(i=> [i,14.21,24,1]),['E','L','T','Z'].map(i=> [i,13.01,24,1]),['F','P','S'].map(i=> [i,11.85,24,1]),['M'].map(i=> [i,18.94,24,1]),['W'].map(i=> [i,20.1,24,1]),[',','.'].map(i=> [i,5.33,24,1]),['=','+','<','>'].map(i=> [i,12.01,24,1]),['↵'].map(i=> [i,0,24,1]),['{','}'].map(i=> [i,10.22,24,1]),['☑','☐'].map(i=> [i,18.35,24,1]),['◯'].map(i=> [i,23.18,24,1]),['℃'].map(i=> [i,21.81,24,1]),['Ⅱ'].map(i=> [i,13.77,24,1])],"Times New Roman20":[['我','，','。','；','？','、','（','）','【','】','⊙','⭘','：'].map(i=> [i,20,22,1]),[' ',',','.'].map(i=> [i,5,22,1]),['0','1','2','3','4','5','6','7','8','9','b','d','g','h','k','n','o','p','q','u','v','x','y','*'].map(i=> [i,10,22,1]),['a','c','e','z','?','“'].map(i=> [i,8.88,22,1]),['f','r','I','(',')','[',']','-','’','Ⅰ'].map(i=> [i,6.66,22,1]),['i','j','l','t',';','/',':'].map(i=> [i,5.56,22,1]),['m'].map(i=> [i,15.56,22,1]),['s','J'].map(i=> [i,7.78,22,1]),['w','A','D','G','H','K','N','O','Q','U','V','X','Y'].map(i=> [i,14.44,22,1]),['B','C','R'].map(i=> [i,13.34,22,1]),['E','L','T','Z'].map(i=> [i,12.22,22,1]),['F','P','S'].map(i=> [i,11.12,22,1]),['M'].map(i=> [i,17.78,22,1]),['W'].map(i=> [i,18.88,22,1]),['=','+','<','>'].map(i=> [i,11.28,22,1]),['↵'].map(i=> [i,0,22,1]),['{','}'].map(i=> [i,9.6,22,1]),['☑','☐'].map(i=> [i,17.23,22,1]),['◯'].map(i=> [i,21.77,22,1]),['℃'].map(i=> [i,20.48,22,1]),['Ⅱ'].map(i=> [i,12.93,22,1])],"Times New Roman18.7":[['我','，','。','；','？','、','（','）','【','】','⊙','⭘','：'].map(i=> [i,18.7,21,1]),[' ',',','.'].map(i=> [i,4.67,21,1]),['0','1','2','3','4','5','6','7','8','9','b','d','g','h','k','n','o','p','q','u','v','x','y','*'].map(i=> [i,9.35,21,1]),['a','c','e','z','?','“'].map(i=> [i,8.3,21,1]),['f','r','I','(',')','[',']','-','’','Ⅰ'].map(i=> [i,6.23,21,1]),['i','j','l','t',';','/',':'].map(i=> [i,5.2,21,1]),['m'].map(i=> [i,14.55,21,1]),['s','J'].map(i=> [i,7.28,21,1]),['w','A','D','G','H','K','N','O','Q','U','V','X','Y'].map(i=> [i,13.5,21,1]),['B','C','R'].map(i=> [i,12.47,21,1]),['E','L','T','Z'].map(i=> [i,11.42,21,1]),['F','P','S'].map(i=> [i,10.4,21,1]),['M'].map(i=> [i,16.63,21,1]),['W'].map(i=> [i,17.65,21,1]),['=','+','<','>'].map(i=> [i,10.55,21,1]),['↵'].map(i=> [i,0,21,1]),['{','}'].map(i=> [i,8.98,21,1]),['☑','☐'].map(i=> [i,16.11,21,1]),['◯'].map(i=> [i,20.35,21,1]),['℃'].map(i=> [i,19.15,21,1]),['Ⅱ'].map(i=> [i,12.09,21,1])],"Times New Roman18":[['我','，','。','；','？','、','（','）','【','】','：','⊙','⭘'].map(i=> [i,18,20,1]),[' ',',','.'].map(i=> [i,4.5,20,1]),['0','1','2','3','4','5','6','7','8','9','b','d','g','h','k','n','o','p','q','u','v','x','y','*'].map(i=> [i,9,20,1]),['a','c','e','z','?','“'].map(i=> [i,8,20,1]),['f','r','I','(',')','[',']','-','’','Ⅰ'].map(i=> [i,6,20,1]),['i','j','l','t',';','/',':'].map(i=> [i,5.02,20,1]),['m'].map(i=> [i,14.02,20,1]),['s','J'].map(i=> [i,7.02,20,1]),['w','A','D','G','H','K','N','O','Q','U','V','X','Y'].map(i=> [i,13,20,1]),['B','C','R'].map(i=> [i,12.02,20,1]),['E','L','T','Z'].map(i=> [i,11,20,1]),['F','P','S'].map(i=> [i,10.02,20,1]),['M'].map(i=> [i,16.02,20,1]),['W'].map(i=> [i,17,20,1]),['=','+','<','>'].map(i=> [i,10.16,20,1]),['Ⅱ'].map(i=> [i,11.64,20,1]),['{','}'].map(i=> [i,8.64,20,1]),['☑','☐'].map(i=> [i,15.52,20,1]),['◯'].map(i=> [i,19.59,20,1]),['℃'].map(i=> [i,18.44,20,1]),['↵'].map(i=> [i,10.19,20,1])],"Times New Roman16":[['我','，','。','；','？','、','（','）','【','】','⊙','⭘','：'].map(i=> [i,16,17,1]),[' ',',','.'].map(i=> [i,4,17,1]),['0','1','2','3','4','5','6','7','8','9','b','d','g','h','k','n','o','p','q','u','v','x','y','*'].map(i=> [i,8,17,1]),['a','c','e','z','?','“'].map(i=> [i,7.1,17,1]),['f','r','I','(',')','[',']','-','’','Ⅰ'].map(i=> [i,5.33,17,1]),['i','j','l','t',';','/',':'].map(i=> [i,4.45,17,1]),['m'].map(i=> [i,12.45,17,1]),['s','J'].map(i=> [i,6.23,17,1]),['w','A','D','G','H','K','N','O','Q','U','V','X','Y'].map(i=> [i,11.55,17,1]),['B','C','R'].map(i=> [i,10.67,17,1]),['E','L','T','Z'].map(i=> [i,9.77,17,1]),['F','P','S'].map(i=> [i,8.9,17,1]),['M'].map(i=> [i,14.23,17,1]),['W'].map(i=> [i,15.1,17,1]),['=','+','<','>'].map(i=> [i,9.02,17,1]),['↵'].map(i=> [i,0,17,1]),['{','}'].map(i=> [i,7.68,17,1]),['☑','☐'].map(i=> [i,13.78,17,1]),['◯'].map(i=> [i,17,17,1]),['℃'].map(i=> [i,16.38,17,1]),['Ⅱ'].map(i=> [i,10.34,17,1])],"Times New Roman14.7":[['我','，','。','；','？','、','（','）','【','】','：','⊙','⭘'].map(i=> [i,14.7,16,1]),[' ',',','.'].map(i=> [i,3.67,16,1]),['0','1','2','3','4','5','6','7','8','9','b','d','g','h','k','n','o','p','q','u','v','x','y','*'].map(i=> [i,7.35,16,1]),['a','c','e','z','?','“'].map(i=> [i,6.52,16,1]),['f','r','I','(',')','[',']','-','’','Ⅰ'].map(i=> [i,4.9,16,1]),['i','j','l','t',';','/',':'].map(i=> [i,4.08,16,1]),['m'].map(i=> [i,11.43,16,1]),['s','J'].map(i=> [i,5.72,16,1]),['w','A','D','G','H','K','N','O','Q','U','V','X','Y'].map(i=> [i,10.62,16,1]),['B','C','R'].map(i=> [i,9.8,16,1]),['E','L','T','Z'].map(i=> [i,8.98,16,1]),['F','P','S'].map(i=> [i,8.18,16,1]),['M'].map(i=> [i,13.07,16,1]),['W'].map(i=> [i,13.87,16,1]),['=','+','<','>'].map(i=> [i,8.29,16,1]),['Ⅱ'].map(i=> [i,9.5,16,1]),['↵'].map(i=> [i,0,16,1]),['{','}'].map(i=> [i,7.06,16,1]),['☑','☐'].map(i=> [i,12.66,16,1]),['◯'].map(i=> [i,16,16,1]),['℃'].map(i=> [i,15.05,16,1])],"Times New Roman14":[['我','，','。','；','？','、','（','）','【','】','⊙','⭘','：'].map(i=> [i,14,15,1]),[' ',',','.'].map(i=> [i,3.5,15,1]),['0','1','2','3','4','5','6','7','8','9','b','d','g','h','k','n','o','p','q','u','v','x','y','*'].map(i=> [i,7,15,1]),['a','c','e','z','?','“'].map(i=> [i,6.21,15,1]),['f','r','I','(',')','[',']','-','’','Ⅰ'].map(i=> [i,4.66,15,1]),['i','j','l','t',';','/',':'].map(i=> [i,3.89,15,1]),['m'].map(i=> [i,10.89,15,1]),['s','J'].map(i=> [i,5.45,15,1]),['w','A','D','G','H','K','N','O','Q','U','V','X','Y'].map(i=> [i,10.11,15,1]),['B','C','R'].map(i=> [i,9.34,15,1]),['E','L','T','Z'].map(i=> [i,8.55,15,1]),['F','P','S'].map(i=> [i,7.79,15,1]),['M'].map(i=> [i,12.45,15,1]),['W'].map(i=> [i,13.21,15,1]),['=','+','<','>'].map(i=> [i,7.9,15,1]),['↵'].map(i=> [i,0,15,1]),['{','}'].map(i=> [i,6.72,15,1]),['☑','☐'].map(i=> [i,12.06,15,1]),['◯'].map(i=> [i,15.24,15,1]),['℃'].map(i=> [i,14.33,15,1]),['Ⅱ'].map(i=> [i,9.05,15,1])],"Times New Roman13.3":[['◯'].map(i=> [i,14,15,1]),['↵'].map(i=> [i,0,15,1]),['我','，','。','；','？','、','（','）','【','】','：','⊙','⭘'].map(i=> [i,13.3,15,1]),[' '].map(i=> [i,3.32,15,1]),['0','1','2','3','4','5','6','7','8','9','b','d','g','h','k','n','o','p','q','u','v','x','y','*'].map(i=> [i,6.65,15,1]),['a','c','e','z','?','“'].map(i=> [i,5.9,15,1]),['f','r','I','(',')','[',']','-','’','Ⅰ'].map(i=> [i,4.43,15,1]),['i','j','l','t',';','/',':'].map(i=> [i,3.7,15,1]),['m'].map(i=> [i,10.35,15,1]),['s','J'].map(i=> [i,5.18,15,1]),['w','A','D','G','H','K','N','O','Q','U','V','X','Y'].map(i=> [i,9.6,15,1]),['B','C','R'].map(i=> [i,8.87,15,1]),['E','L','T','Z'].map(i=> [i,8.12,15,1]),['F','P','S'].map(i=> [i,7.4,15,1]),['M'].map(i=> [i,11.83,15,1]),['W'].map(i=> [i,12.55,15,1]),[',','.'].map(i=> [i,3.33,15,1]),['=','+','<','>'].map(i=> [i,7.5,15,1]),['Ⅱ'].map(i=> [i,8.6,15,1]),['{','}'].map(i=> [i,6.38,15,1]),['☑','☐'].map(i=> [i,11.46,15,1]),['℃'].map(i=> [i,13.62,15,1])],"Times New Roman12":[['我','，','。','；','？','、','（','）','【','】','⊙','⭘','：'].map(i=> [i,12,14,1]),[' ',',','.'].map(i=> [i,3,14,1]),['0','1','2','3','4','5','6','7','8','9','b','d','g','h','k','n','o','p','q','u','v','x','y','*'].map(i=> [i,6,14,1]),['a','c','e','z','?','“'].map(i=> [i,5.33,14,1]),['f','r','I','(',')','[',']','-','’','Ⅰ'].map(i=> [i,4,14,1]),['i','j','l','t',';','/',':'].map(i=> [i,3.33,14,1]),['m'].map(i=> [i,9.33,14,1]),['s','J'].map(i=> [i,4.67,14,1]),['w','A','D','G','H','K','N','O','Q','U','V','X','Y'].map(i=> [i,8.67,14,1]),['B','C','R'].map(i=> [i,8,14,1]),['E','L','T','Z'].map(i=> [i,7.33,14,1]),['F','P','S'].map(i=> [i,6.67,14,1]),['M'].map(i=> [i,10.67,14,1]),['W'].map(i=> [i,11.33,14,1]),['=','+','<','>'].map(i=> [i,6.77,14,1]),['↵'].map(i=> [i,0,14,1]),['{','}'].map(i=> [i,5.76,14,1]),['☑','☐'].map(i=> [i,10.34,14,1]),['◯'].map(i=> [i,13,14,1]),['℃'].map(i=> [i,12.29,14,1]),['Ⅱ'].map(i=> [i,7.76,14,1])],"Times New Roman10.7":[['我','，','。','；','？','、','（','）','【','】','：','⊙','⭘'].map(i=> [i,10.7,12,1]),[' ',',','.'].map(i=> [i,2.67,12,1]),['0','1','2','3','4','5','6','7','8','9','b','d','g','h','k','n','o','p','q','u','v','x','y','*'].map(i=> [i,5.35,12,1]),['a','c','e','z','?','“'].map(i=> [i,4.75,12,1]),['f','r','I','(',')','[',']','-','’','Ⅰ'].map(i=> [i,3.56,12,1]),['i','j','l','t',';','/',':'].map(i=> [i,2.97,12,1]),['m'].map(i=> [i,8.32,12,1]),['s','J'].map(i=> [i,4.16,12,1]),['w','A','D','G','H','K','N','O','Q','U','V','X','Y'].map(i=> [i,7.73,12,1]),['B','C','R'].map(i=> [i,7.14,12,1]),['E','L','T','Z'].map(i=> [i,6.54,12,1]),['F','P','S'].map(i=> [i,5.95,12,1]),['M'].map(i=> [i,9.51,12,1]),['W'].map(i=> [i,10.1,12,1]),['=','+','<','>'].map(i=> [i,6.03,12,1]),['Ⅱ'].map(i=> [i,6.92,12,1]),['↵'].map(i=> [i,0,12,1]),['{','}'].map(i=> [i,5.14,12,1]),['☑','☐'].map(i=> [i,9.22,12,1]),['◯'].map(i=> [i,11.65,12,1]),['℃'].map(i=> [i,10.96,12,1])],"58.7":[['我','：','，','。','；','’','“','？','、','（','）','【','】','⊙','⭘','℃','Ⅰ','Ⅱ'].map(i=> [i,58.7,58,1]),[' ','0','1','2','3','4','5','6','7','8','9','a','b','c','d','e','f','g','h','i','j','k','l','m','n','o','p','q','r','s','t','u','v','w','x','y','z','A','B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q','R','S','T','U','V','W','X','Y','Z',',','.',';',':','/','?','(',')','[',']','=','-','+','*','<','>','{','}'].map(i=> [i,29.35,58,1]),['↵'].map(i=> [i,0,58,1]),['☑','☐'].map(i=> [i,50.56,58,1]),['◯'].map(i=> [i,63.89,58,1])],"37.3":[['我','，','。','；','’','“','？','、','（','）','【','】','Ⅰ','Ⅱ','：','⊙','⭘','℃'].map(i=> [i,37.3,37,1]),[' ','0','1','2','3','4','5','6','7','8','9','a','b','c','d','e','f','g','h','i','j','k','l','m','n','o','p','q','r','s','t','u','v','w','x','y','z','A','B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q','R','S','T','U','V','W','X','Y','Z',',','.',';','/','?','(',')','[',']','=','-','+','*','<','>',':','{','}'].map(i=> [i,18.65,37,1]),['☑','☐'].map(i=> [i,32.13,37,1]),['◯'].map(i=> [i,40.6,37,1]),['↵'].map(i=> [i,0,37,1])],"34.7":[['我','，','。','；','’','“','？','、','（','）','【','】','：','⊙','⭘','℃','Ⅰ','Ⅱ'].map(i=> [i,34.7,35,1]),[' ','0','1','2','3','4','5','6','7','8','9','a','b','c','d','e','f','g','h','i','j','k','l','m','n','o','p','q','r','s','t','u','v','w','x','y','z','A','B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q','R','S','T','U','V','W','X','Y','Z',',','.',';','/','?','(',')','[',']','=','-','+','*','<','>',':','{','}'].map(i=> [i,17.35,35,1]),['↵'].map(i=> [i,0,35,1]),['☑','☐'].map(i=> [i,29.89,35,1]),['◯'].map(i=> [i,37.77,35,1])],"29.3":[['我','，','。','；','’','“','？','、','（','）','【','】','：','⊙','⭘','℃','Ⅰ','Ⅱ'].map(i=> [i,29.3,29,1]),[' ','0','1','2','3','4','5','6','7','8','9','a','b','c','d','e','f','g','h','i','j','k','l','m','n','o','p','q','r','s','t','u','v','w','x','y','z','A','B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q','R','S','T','U','V','W','X','Y','Z',',','.',';',':','/','?','(',')','[',']','=','-','+','*','<','>','{','}'].map(i=> [i,14.65,29,1]),['↵'].map(i=> [i,0,29,1]),['☑','☐'].map(i=> [i,25.24,29,1]),['◯'].map(i=> [i,31.89,29,1])],"26.7":[['我','，','。','；','’','“','？','、','（','）','【','】','Ⅰ','Ⅱ','：','⊙','⭘','℃'].map(i=> [i,26.7,27,1]),[' ','0','1','2','3','4','5','6','7','8','9','a','b','c','d','e','f','g','h','i','j','k','l','m','n','o','p','q','r','s','t','u','v','w','x','y','z','A','B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q','R','S','T','U','V','W','X','Y','Z',',','.',';','/','?','(',')','[',']','=','-','+','*','<','>',':','{','}'].map(i=> [i,13.35,27,1]),['☑','☐'].map(i=> [i,23,27,1]),['◯'].map(i=> [i,29.06,27,1]),['↵'].map(i=> [i,0,27,1])],"21.3":[['↵'].map(i=> [i,0,21,1]),['我','，','。','；','’','“','？','、','（','）','【','】','：','⊙','⭘','℃','Ⅰ','Ⅱ'].map(i=> [i,21.3,21,1]),[' ','0','1','2','3','4','5','6','7','8','9','a','b','c','d','e','f','g','h','i','j','k','l','m','n','o','p','q','r','s','t','u','v','w','x','y','z','A','B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q','R','S','T','U','V','W','X','Y','Z',',','.',';','/','?','(',')','[',']','=','-','+','*','<','>',':','{','}'].map(i=> [i,10.65,21,1]),['☑','☐'].map(i=> [i,18.35,21,1]),['◯'].map(i=> [i,23.18,21,1])],"18.7":[['我','，','。','；','’','“','？','、','（','）','【','】','：','⊙','⭘','℃','Ⅰ','Ⅱ'].map(i=> [i,18.7,19,1]),[' ','0','1','2','3','4','5','6','7','8','9','a','b','c','d','e','f','g','h','i','j','k','l','m','n','o','p','q','r','s','t','u','v','w','x','y','z','A','B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q','R','S','T','U','V','W','X','Y','Z',',','.',';','/','?','(',')','[',']','=','-','+','*','<','>',':','{','}'].map(i=> [i,9.35,19,1]),['↵'].map(i=> [i,0,19,1]),['☑','☐'].map(i=> [i,16.11,19,1]),['◯'].map(i=> [i,20.35,19,1])],"14.7":[['↵'].map(i=> [i,0,15,1]),[' '].map(i=> [i,7.5,15,1]),['我','，','。','；','’','“','？','、','（','）','【','】','Ⅰ','Ⅱ','：','⊙','℃'].map(i=> [i,15,15,1]),['0','1','2','3','4','5','6','7','8','9','a','b','c','d','e','f','g','h','i','j','k','l','m','n','o','p','q','r','s','t','u','v','w','x','y','z','A','B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q','R','S','T','U','V','W','X','Y','Z',',','.',';','/','?','(',')','[',']','=','-','+','*','<','>',':','{','}'].map(i=> [i,8,15,1]),['☑','☐'].map(i=> [i,12.66,15,1]),['⭘'].map(i=> [i,14.7,15,1]),['◯'].map(i=> [i,16,15,1])],"13.3":[['我','，','。','；','’','“','？','、','（','）','【','】','Ⅰ','Ⅱ','：','⊙','℃'].map(i=> [i,13,13,1]),[' ','0','1','2','3','4','5','6','7','8','9','a','b','c','d','e','f','g','h','i','j','k','l','m','n','o','p','q','r','s','t','u','v','w','x','y','z','A','B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q','R','S','T','U','V','W','X','Y','Z',',','.',';','/','?','(',')','[',']','=','-','+','*','<','>',':','{','}'].map(i=> [i,7,13,1]),['☑','☐'].map(i=> [i,11.46,13,1]),['⭘'].map(i=> [i,13.3,13,1]),['◯'].map(i=> [i,14,13,1]),['↵'].map(i=> [i,0,13,1])],"10.7":[['我','，','。','；','’','“','？','、','（','）','【','】','Ⅰ','Ⅱ','：','⊙','⭘','℃'].map(i=> [i,10.7,11,1]),[' ','0','1','2','3','4','5','6','7','8','9','a','b','c','d','e','f','g','h','i','j','k','l','m','n','o','p','q','r','s','t','u','v','w','x','y','z','A','B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q','R','S','T','U','V','W','X','Y','Z',',','.',';','/','?','(',')','[',']','=','-','+','*','<','>',':','{','}'].map(i=> [i,5.35,11,1]),['☑','☐'].map(i=> [i,9.22,11,1]),['◯'].map(i=> [i,11.65,11,1]),['↵'].map(i=> [i,0,11,1])]};

    for (const size in items) {
        const arrs = [];
        items[size].forEach((item) => {
            const arr = item[0];
            const obj = {width: arr[1], height: arr[2], type: arr[3]};
            item.forEach((i) => {
                arrs.push([i[0], obj]);
            });
        });
        CACHES.set(size, new Map(arrs));
    }
}
