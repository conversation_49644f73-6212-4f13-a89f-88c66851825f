import { XmlComponent, XmlAttributeComponent } from '../xml-components';
import { AnchorType } from '../../../../common/commonDefines';

export interface IDrawingAttributesProperties {
  name: string;
  width: number;
  height: number;
  imageRatio?: number;
  anchorType?: AnchorType;
  href?: string;
  source?: string;
  preferRelativeResize?: number;
  sizeProtect?: number;
  deleteProtect?: number;
  copyProtect?: number;
  mediaType?: number;
  vertAlign?: number;
}

export interface IBarcodeAllProperties {
  name: string;
  width: number;
  height: number;
  // imageRatio?: number;
  textAlign?: string;
  src: string;
  bUse?: boolean;
  content: string; // equationElem Text
  sourceBind?: string;
  errorCL?: string;
}

class DrawingAttributes extends XmlAttributeComponent<IDrawingAttributesProperties> {
  // <T> -> type variable, shape this.root in base
  protected xmlKeys: any = {
    name: 'name',
    width: 'width',
    height: 'height',
    imageRatio: 'ratio',
    anchorType: 'anchor-type',
    href: 'href', // will discard if empty
    source: 'source',
    preferRelativeResize: 'preferRelativeResize',
    sizeProtect: 'sizeProtect',
    deleteProtect: 'deleteProtect',
    copyProtect: 'copyProtect',
    mediaType: 'mediaType',
    vertAlign: 'vertAlign',
  };
}

class PaintingAttributes extends XmlAttributeComponent<IDrawingAttributesProperties> {
  // <T> -> type variable, shape this.root in base
  protected xmlKeys: any = {
    name: 'name',
    width: 'width',
    height: 'height',
    source: 'source',
    vertAlign: 'vertAlign',
  };
}

class BarcodeAttributes extends XmlAttributeComponent<IBarcodeAllProperties> {
  // <T> -> type variable, shape this.root in base
  protected xmlKeys: any = {
    name: 'name',
    width: 'width',
    height: 'height',
    src: 'src',
    textAlign: 'textAlign',
    bUse: 'bUse',
    content: 'content',
    sourceBind: 'sourceBind',
  };
}

class QRCodeAttributes extends XmlAttributeComponent<IBarcodeAllProperties> {
  // <T> -> type variable, shape this.root in base
  protected xmlKeys: any = {
    name: 'name',
    width: 'width',
    height: 'height',
    content: 'content',
    src: 'src',
    errorCL: 'errorCL',
    sourceBind: 'sourceBind',
  };
}

// tslint:disable-next-line: max-classes-per-file
export class Drawing extends XmlComponent {

  constructor(attrs: IDrawingAttributesProperties) {
    super('w:drawing');

    this.root.push(new DrawingAttributes(attrs));
  }
}

// tslint:disable-next-line: max-classes-per-file
export class EditableDrawing extends XmlComponent {

  constructor(attrs: IDrawingAttributesProperties) {
    super('Painting');

    this.root.push(new PaintingAttributes(attrs));
  }
}

// tslint:disable-next-line: max-classes-per-file
export class Barcode extends XmlComponent {

  constructor(attrs: IBarcodeAllProperties) {
    super('Barcode');

    this.root.push(new BarcodeAttributes(attrs));
  }
}

export class QRCode extends XmlComponent {

  constructor(attrs: IBarcodeAllProperties) {
    super('QRCode');

    this.root.push(new QRCodeAttributes(attrs));
  }
}
