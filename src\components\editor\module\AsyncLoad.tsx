import { message } from '@/common/Message';
import * as React from 'react';

interface IProps {
    host: any;
    component: () => Promise<any>;
    name: string;
    props: any;
}

export class AsyncLoad extends React.Component<IProps, { componentLoaded: boolean }> {
    private _component: any;
    private isMountedFlag = false; // 标识组件是否挂载

    constructor(props: IProps) {
        super(props);
        this.state = { componentLoaded: false };
    }

    async componentDidMount() {
        this.isMountedFlag = true;
        await this.importComponent(this.props.component);
    }

    componentWillUnmount() {
        this.isMountedFlag = false; // 在卸载时标识组件不再挂载
    }

    render() {
        const { name, props, host } = this.props;

        if (!host[name]) {
            return null; // 条件渲染在这里进行
        }

        if (!this._component) {
            return this.loadInfo();
        }

        const obj = {
            ...props,
            id: props.id || name,
            visible: props.visible !== undefined ? props.visible : host[name],
        };

        return React.createElement(this._component, obj);
    }

    componentDidCatch(error: any, info: any) {
        console.error('Component did catch error:', error, info);
    }

    private loadInfo() {
        return (
            <React.Suspense fallback={<span>Loading...</span>}>
                {this._component ? React.createElement(this._component, this.props.props) : <span>Loading...</span>}
            </React.Suspense>
        );
    }

    private async importComponent(component: () => Promise<any>) {
        try {
            const res = await component();
            if (!res.default) {
                console.error('export文件不正确：export default xxx');
                return;
            }
            message.close();
            this._component = res.default;
            if (this.isMountedFlag) { // 确保在组件挂载状态下更新
                this.setState({ componentLoaded: true });
            }
        } catch (error) {
            console.error('Error loading component:', error);
        }
    }
}
