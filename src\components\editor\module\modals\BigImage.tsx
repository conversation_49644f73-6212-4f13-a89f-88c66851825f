
import * as React from "react";
import Dialog from "../../ui/Dialog";
import './BigImage.less';

interface IProps {
    documentCore: any;
    visible: boolean;
    id: string;
    image?: any;
    close: (name: string | number, bRefresh?: boolean) => void;
}

enum LoadingStatus {
    None,
    Loading,
    Success,
}

interface IState {
    bRefresh: boolean;
    status: LoadingStatus;
}
export default class BigImagePanel extends React.Component<IProps, IState> {
    private visible: boolean;
    private title: string;
    private imgUrl: string;
    private height: number;
    private container: React.RefObject<HTMLDivElement>;
    private isDragging = false;
    private startPosition = {x: 0, y: 0};

    constructor(props: any) {
        super(props);
        this.state = {
            bRefresh: false,
            status: LoadingStatus.None,
        };
        this.visible = this.props.visible;
        this.title = '网络大图片';
        this.height = document.body.clientHeight * 2 / 3;
        this.container = React.createRef();
    }

    public render(): any {
        const width = 500;
        let animateClass = `${this.state.status === LoadingStatus.Loading ? 'loading-container' : 'hidden'}`;
        let imgClass = this.state.status === LoadingStatus.Success ? 'big' : 'big hidden';

        return (
            <Dialog
                visible={this.visible}
                width={(width + 30)}
                height={this.height}
                top={document.body.clientHeight / 2 - 200 + ''}
                open={this.open}
                title={this.title}
                id={this.props.id}
                scale={true}
                bCloseIcon={true}
                close={this.close}
            >
                <div className="dialog-bigimage" ref={this.container}>
                    <div className={animateClass}>
                        <div className='loading-animation'></div>
                    </div>
                    <img src={this.imgUrl} onLoad={this.handleImageLoad} className={imgClass} />
                </div>
            </Dialog>
        );
    }
    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        this.visible = nextProps.visible;
    }

    componentDidMount(): void {
        const container = this.container.current;
        if (!container) return;
        const image = container.querySelector('img.big');
        if (!image) return;
        container.addEventListener('mousedown', this.imgMouseDown);
        container.addEventListener('mousemove', this.imgMouseMove);
        container.addEventListener('mouseup', this.imgMouseUp);
        container.addEventListener('mouseleave', this.imgMouseUp);
    }

    componentWillUnmount(): void {
        const container = this.container.current;
        if (!container) return;
        const image = container.querySelector('img.big');
        if (!image) return;
        container.removeEventListener('mousedown', this.imgMouseDown);
        container.removeEventListener('mousemove', this.imgMouseMove);
        container.removeEventListener('mouseup', this.imgMouseUp);
        container.removeEventListener('mouseleave', this.imgMouseUp);
    }

    private open = (): void => {
        let file = this.props.image;
        if (!file) {
            const documentCore = this.props.documentCore;
            file = documentCore.getSelectedDrawing();
        }

        if (!file || !file.hasBind()) {
            return;
        }
        const {sourceUrl} = file.getBind();
        if (this.imgUrl === sourceUrl) return;
        this.imgUrl = sourceUrl || '';
        this.setState({ status: LoadingStatus.Loading });
    }

    private close = (bRefresh?: boolean): void => {
        this.visible = false;
        this.setState({ bRefresh: !this.state.bRefresh });
        this.props.close(this.props.id, bRefresh);
    }

    private handleImageLoad = (e) => {
        const target = e.target;
        if (!target) return;
        this.setState({status: LoadingStatus.Success});
    }

    private imgMouseDown = (event: any) => {
        event.preventDefault();
        this.isDragging = true;
        this.startPosition.x = event.clientX;
        this.startPosition.y = event.clientY;
        
        const img = event.target as HTMLImageElement;
        if (img) {
            img.style.cursor = 'grabbing';
        }
    }
    private imgMouseMove = (event: any) => {
        event.preventDefault();
        const container = this.container.current;
        if (!container) return;
        const startPosition = this.startPosition;
        if (this.isDragging) {
            const deltaX = event.clientX - startPosition.x;
            const deltaY = event.clientY - startPosition.y;
            container.scrollLeft -= deltaX;
            container.scrollTop -= deltaY;
            startPosition.x = event.clientX;
            startPosition.y = event.clientY;
        }
    }
    private imgMouseUp = (event: any) => {
        event.preventDefault();
        if (!this.isDragging) return;
        this.isDragging = false;
        const img = this.container.current?.querySelector('img.big') as HTMLImageElement;
        if (img) {
            img.style.cursor = 'grab';
        }
    }

}