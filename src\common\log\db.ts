import {IndexDB} from '../indexDB/IndexDB';
import { IIndexDB, ITable, LoggerType, IDataBase } from './LoggerType';
export default class LoggerDB implements IIndexDB {
    private _db: IndexDB;
    constructor() {
        //
    }

    public async init(): Promise<boolean> {
        if (this._db) {
            return true;
        }

        const keys: {key: string}[] = [
            {key: 'description'},
            {key: 'wasted'},
            {key: 'time'},
            {key: 'editorID'},
            {key: 'result'},
        ];

        const tables: ITable[] = [];
        // tables.push(this.getTableKeys(LoggerType.Info, keys));
        tables.push(this.getTableKeys(LoggerType.DevLog, keys));
        // tables.push(this.getTableKeys(LoggerType.Debug, keys));
        tables.push(this.getTableKeys(LoggerType.Interface, keys));
        tables.push(this.getTableKeys(LoggerType.Error, keys));
        const dataBase: IDataBase = {
            dbName: 'hzEditorLog',
            version: 1,
            tables
        };
        try {
            const db = new IndexDB(dataBase);
            await db.openDb();
            this._db = db;
        } catch (error) {
            return false;
        }

        return true;
    }

    public insert(tableName: string, items: any[]): Promise<void> {
        return this._db?.insert({tableName, data: items});
    }

    public deleteMaxDatas(tableName: string): Promise<number> {
        return this._db?.deleteMaxDatas(tableName);
    }

    private getTableKeys(tableName: LoggerType, keys: {key: string}[]): ITable {
        const table: ITable = {
            tableName,
            option: {
                keyPath: 'id',
                autoIncrement: true
            },
            indexs: [
                {
                    key: 'id',
                    option: {
                        unique: true,
                    }
                }
            ]
        };
        table.indexs.push(...keys);

        return table;
    }

    // public updateById(id: number, item: any): Promise<number> {
    //     return this._update(item, (e: any) => {
    //         if (e.key === id) {
    //             this._db.setEnd(true);
    //             return true;
    //         }

    //         return false;
    //     });
    // }

    // public update(item: any, condition: (e: any) => boolean): Promise<number> {
    //     return this._update(item, (e: any) => {
    //         return condition(e.value);
    //     });
    // }

    // public getCount(condition?: (item: any) => boolean): Promise<number> {
    //     return new Promise((resolve, reject) => {
    //         this._db.getCount(
    //             this._tbName,
    //             (result) => {
    //                 resolve(result);
    //             },
    //             condition,
    //         );
    //     });

    //     // return res.result.length;
    // }

    // public delete(condition: (item: any) => boolean): Promise<number> {
    //     return this._delete((e) => {
    //         return condition(e.value);
    //     });
    // }

    // public deleteById(id: number): Promise<number> {
    //     return this._delete((e: any) => {
    //         if (e.key === id) {
    //             this._db.setEnd(true);
    //             return true;
    //         }

    //         return false;
    //     });
    // }

    // public query(
    //     condition: (item: any) => boolean,
    //     count: number = 0,
    // ): Promise<any[]> {
    //     return new Promise((resolve, reject) => {
    //         let index = 0;
    //         this._db.query(
    //             this._tbName,
    //             (e: any) => {
    //                 if (count > 0 && index === count) {
    //                     this._db.setEnd(true);
    //                     return false;
    //                 }
    //                 if (condition(e.value)) {
    //                     index++;
    //                     return true;
    //                 }
    //                 return false;
    //             },
    //             (res) => {
    //                 this._db.setEnd(false);
    //                 resolve(res);
    //             },
    //         );
    //     });
    // }

    // public queryAll(): Promise<any[]> {
    //     return new Promise((resolve, reject) => {
    //         this._db.queryAll(this._tbName, (res) => {
    //             resolve(res);
    //         });
    //     });
    // }

    // public setTableName(tbName: string): void {
    //     this._tbName = tbName;
    // }

    // private _update(item: any, condition: (e: any) => void): Promise<number> {
    //     return new Promise((resolve, reject) => {
    //         this._db.update(
    //             this._tbName,
    //             condition,
    //             (res) => {
    //                 return item;
    //             },
    //             (res) => {
    //                 this._db.setEnd(false);
    //                 resolve(res);
    //             },
    //         );
    //     });
    // }

    // private _delete(condition: (e: any) => void): Promise<number> {
    //     return new Promise((resolve, reject) => {
    //         this._db.delete(this._tbName, condition, (res) => {
    //             this._db.setEnd(false);
    //             resolve(res);
    //         });
    //     });
    // }
}
