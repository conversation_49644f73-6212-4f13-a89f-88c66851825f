import { Document, Paragraph, Packer, TextRun } from '.';
import { ParaElementType } from '../../model/core/Paragraph/ParagraphContent';
// tslint:disable-next-line: max-line-length
import { FONT_MAPPING, AnchorType, EquationType, FONT_WEIGHT_TYPE, FONT_STYLE_TYPE, TEXT_DECORATION_LINE_TYPE, SECTION_PROPERTIES_DEFAULT, ErrorMessages, FileSaveType, STD_START_DEFAULT, STD_END_DEFAULT, STD_TYPE_DEFAULT, EDITOR_VERSION, FILE_HEADER_LENGTH, FILE_HEADER_DESC, REGION_PROPS_DEFAULT, RegionType, RegionMode, TABLE_TR_HEIGHT_DEFAULT, TABLE_PROPS_DEFAULT, ICustomProps, IS_TABLE_TR_HEADER, TABLE_CELL_PROPS_DEFAULT, TABLE_CELL_BORDER, ITableCellBorder, HeaderFooterType, <PERSON><PERSON><PERSON><PERSON>ROP_DEFAULT, IRevisionSetting, ReviewType, RE<PERSON><PERSON><PERSON>_COLOR, NewControlType, IEditorBackground, FILE_HEADER_DOCUMENT_VERSION, ISaveModJson, NeedsignalContent, ISaveCleanElementMode, FILE_HEADER_BYTELENGTH, FILE_HEADER_DESC_BYTELENGTH, FILE_HEADER_VERSION_BYTELENGTH, FILE_HEADER_HTMLEXIST_BYTELENGTH, FILE_HEADER_DOCUMENT_VERSION_BYTELENGTH, NISTableCellType, isDefaultTextColor, isValidORGBColor, NumberingType, isMacOs, FILE_HEADER_VERSION2, NISTABLE_CELL_BORDER } from '../../common/commonDefines';
import { saveAs } from 'file-saver';
// import { IXmlProps } from '../../components/editor/Editor';
import ParagraphCore from '../../model/core/Paragraph';
import { Table as TableCore } from '../../model/core/Table';
import { IDefaultParaProperty, IDefaultTextProperty } from '../../model/DocumentCore';
import ParaProperty from '../../model/core/Paragraph/ParaProperty';
import TextProperty, { TextVertAlign, TextDecorationLineType } from '../../model/core/TextProperty';
import ParaDrawing, { ParaBarcode, ParaEquation, ParaMediaDrawing } from '../../model/core/Paragraph/ParaDrawing';
import { IBarcodeAllProperties, IDrawingAttributesProperties } from './file/drawing';
import { IMedEquationAttributesProperties } from './file/drawing/medEquation/medEquation';
import { ParaNewControlBorder } from '../../model/core/Paragraph/ParaNewControlBorder';
// tslint:disable-next-line: max-line-length
import { IContentControlAttributesProperties, ContentControlStart, ContentControlEnd, Sdt, ContentControlAttributes, Section } from './file/paragraph/contentControl/contentControl';
import { NewControl } from '../../model/core/NewControl/NewControl';
import LogicDocument from '../../model/core/Document';
// tslint:disable-next-line: max-line-length
import { IContentControlStartElementVals, IContentControlEndElementVals } from './file/paragraph/contentControl/contentControlElements';
import ParaPortion from '../../model/core/Paragraph/ParaPortion';
import { PageProperty } from '../../model/StyleProperty';
import { SectionPropertiesOptions } from './file/document';
import { Images, Paintings } from './file/media/images';
import { IMediaImageAttributesProperties, MediaImage, EditableMediaImage } from './file/media/images/MediaImage';
import { Region as RegionCore } from '../../model/core/Region';
import { IRegionAttributesProperties, Region, IRegionProps } from './file/region';
import { Table, ITableAttributesProperties, TableCell, NISTableCell } from './file/table';
// import { getPxForMM } from '../../model/core/util';
import { TableRowHeight } from 'src/model/core/Table/TableRowProperty';
import { logger } from '../../common/log/Logger';
import { TableCell as TableCellCore } from '../../model/core/Table/TableCell';
import { ICellWidthProperties } from './file/table/table-cell/table-cell-properties';
import { IPageNumAttributesProperties } from './file/paragraph/run/run-components/pageNum';
import ParaPageNum from '../../model/core/Paragraph/ParaPageNum';
import { NewControlRadio } from '../../model/core/NewControl/NewControlRadio';
import { NewControlCheck } from '../../model/core/NewControl/NewControlCheck';
import { message } from '../../common/Message';
import { NewControlSignature } from '../../model/core/NewControl/NewControlSignature';
import { IRevisionAttributesProperties, Revision } from './file/paragraph/revision/revision';
import ParaTextExtend from '../../model/core/Paragraph/ParaTextExtend';
import { SdtContent } from './file/paragraph/contentControl/sdtContent';
import { Run } from './file';
import { customEncodeURIComponent } from '../../common/commonMethods';
import ParaText from '../../model/core/Paragraph/ParaText';
import { DocumentContent } from '../../model/core/DocumentContent';
import { ExportHtml } from '../../components/editor/module/ExportHtml';
import { EmrEditor } from '../../components/editor/Main';
import { NISTable as NISTableCore} from '../../model/core/NISTable';
import { INISTableAttributesProperties, NISTable } from './file/table/nis-table';
import { NewControlAddress } from '../../model/core/NewControl/NewControlAddress';
import { CommentData } from '@/model/core/Comment/CommentData';
import { XmlCommentData } from './file/paragraph/comment/commentData';
import { XmlComment } from './file/paragraph/comment/comment';
import { addNode } from '@/common/struct/write';
import { XmlComponent } from './file/xml-components';

export class FormatWriter {

  private host: EmrEditor;
  constructor(host?: EmrEditor) {
    this.host = host;
  }

  /**
   * Generate zipped files. Called when click "save". Entry point
   */
  public generate(props: any, bSaveToString: boolean = false,
                  bSaveSelectedArea: boolean = false, bAllowHtml: boolean = false): Promise<ArrayBuffer> {

    const doc = new Document();
    const uniqueImageList: Map<string, string> = new Map();

    // tslint:disable-next-line: no-console
    // console.time('save time');
    // tslint:disable-next-line: no-console
    // console.time('save time before packer');
    // Goal is to fill up "doc" for file generation. doc: File
    try {
      this.generateFromSVG2(doc, props, uniqueImageList, bSaveSelectedArea, {nisTable: []});
    } catch (error) {
      const documentCore = props.documentCore;
      const date = new Date();
      // tslint:disable-next-line: no-console
      console.warn(ErrorMessages.SaveIterationFailure);
      // tslint:disable-next-line: no-console
      console.log(error)
      window.dispatchEvent(new ErrorEvent('error', {message: ErrorMessages.SaveIterationFailure, error}));
      logger.error({id: documentCore.getCurrentId(), code: error.stack,
        startTime: date, args: null, name: 'generate.generateFromSVG'});
    }

    // tslint:disable-next-line: no-console
    // console.timeEnd('save time before packer');

    return new Promise((resolve, reject) => {

      // check document.xml
      try {
        // root should not be root.length === 0, even create new doc -> has one new paragraph
        const nodesInBody = doc.Document.Body.Root;
        if ( !(nodesInBody && nodesInBody.length > 0) ) {
          // console.log(new Error('Document.xml save failure'))
          throw new Error('Document.xml save failure');
        }
      } catch (error) {
        // alert(ErrorMessages.MainXmlSaveFailure);
        // tslint:disable-next-line: no-console
        console.warn(ErrorMessages.MainXmlSaveFailure);
        window.dispatchEvent(new ErrorEvent('error', {message: ErrorMessages.MainXmlSaveFailure, error}));
        reject(error);
      }

      // check other xmls
      try {
        const nodesInSettings = doc.Settings.Root;
        const nodesInStyles = doc.Styles.Root;
        const nodesInHeader = doc.Headers[0].Root;
        const nodesInFooter = doc.Footer.Root;
        // console.log(nodesInHeader, nodesInFooter)
        // console.log(nodesInSettings, nodesInStyles)
        // TODO
        if ( !(nodesInSettings && nodesInSettings.length > 0 && nodesInStyles && nodesInStyles.length > 0) ) {
          throw new Error('Other xmls save failure');
        }
      } catch (error) {
        // alert(ErrorMessages.MinorXmlSaveFailure);
        // tslint:disable-next-line: no-console
        console.warn(ErrorMessages.MinorXmlSaveFailure);
        window.dispatchEvent(new ErrorEvent('error', {message: ErrorMessages.MinorXmlSaveFailure, error}));
        reject(error);
      }

    
      this.fileToBlob(doc, props, bSaveToString, bSaveSelectedArea)
      .then((result) => {
        resolve(result);
      });
    });
  }

  /**
   * 为AI设计的，只生成content.xml部分
   */
   public generateContentBlob(props: any, bSaveToString: boolean = false,
                      bSaveSelectedArea: boolean = false, options: any = {},
                      sModJson: ISaveModJson = null, bAllowHtml: boolean = false): Promise<Blob> {
    let date = new Date();
    options.time = 0;
    const doc = new Document();
    const uniqueImageList: Map<string, string> = new Map();

    try {
      this.generateContentFromSVG(doc, props, uniqueImageList, bSaveSelectedArea, sModJson);
    } catch (error) {
      const documentCore = props.documentCore;
      date = new Date();
      // alert(ErrorMessages.FetchStructFailure);
      // tslint:disable-next-line: no-console
      console.warn(ErrorMessages.SaveIterationFailure);
      // tslint:disable-next-line: no-console
      console.log(error);
      window.dispatchEvent(new ErrorEvent('error', {message: ErrorMessages.SaveIterationFailure, error}));
      logger.error({id: documentCore.getCurrentId(), code: error.stack,
        startTime: date, args: null, name: 'generateBlob.generateFromSVG'});
    }
    // tslint:disable-next-line: no-console
    // console.timeEnd('save time before packer');

    return new Promise((resolve, reject) => {

      // check document.xml
      try {
        // root should not be root.length === 0, even create new doc -> has one new paragraph
        const nodesInBody = doc.Document.Body.Root;
        if ( !(nodesInBody && nodesInBody.length > 0) ) {
          // console.log(new Error('Document.xml save failure'))
          throw new Error('Document.xml save failure');
        }
      } catch (error) {
        // alert(ErrorMessages.MainXmlSaveFailure);
        // tslint:disable-next-line: no-console
        console.warn(ErrorMessages.MainXmlSaveFailure);
        window.dispatchEvent(new ErrorEvent('error', {message: ErrorMessages.MainXmlSaveFailure, error}));
        reject(error);
      }

      this.fileToBlobApi(doc, props, bSaveToString, bSaveSelectedArea, options).then((result) => {
        resolve(result);
      });   
    });
  }

  public generateBlob(props: any, bSaveToString: boolean = false,
                      bSaveSelectedArea: boolean = false, options: any = {},
                      sModJson: ISaveModJson = null, bAllowHtml: boolean = false): Promise<Blob> {
    let date = new Date();
    options.time = 0;
    const doc = new Document();
    const uniqueImageList: Map<string, string> = new Map();

    // tslint:disable-next-line: no-console
    // console.time('save time');
    // tslint:disable-next-line: no-console
    // console.time('save time before packer');
    // Goal is to fill up "doc" for file generation. doc: File
    try {
      this.generateFromSVG2(doc, props, uniqueImageList, bSaveSelectedArea, sModJson);
    } catch (error) {
      const documentCore = props.documentCore;
      date = new Date();
      // alert(ErrorMessages.FetchStructFailure);
      // tslint:disable-next-line: no-console
      console.warn(ErrorMessages.SaveIterationFailure);
      // tslint:disable-next-line: no-console
      console.log(error);
      window.dispatchEvent(new ErrorEvent('error', {message: ErrorMessages.SaveIterationFailure, error}));
      logger.error({id: documentCore.getCurrentId(), code: error.stack,
        startTime: date, args: null, name: 'generateBlob.generateFromSVG'});
    }
    // tslint:disable-next-line: no-console
    // console.timeEnd('save time before packer');

    return new Promise((resolve, reject) => {

      // check document.xml
      try {
        // root should not be root.length === 0, even create new doc -> has one new paragraph
        const nodesInBody = doc.Document.Body.Root;
        if ( !(nodesInBody && nodesInBody.length > 0) ) {
          // console.log(new Error('Document.xml save failure'))
          throw new Error('Document.xml save failure');
        }
      } catch (error) {
        // alert(ErrorMessages.MainXmlSaveFailure);
        // tslint:disable-next-line: no-console
        console.warn(ErrorMessages.MainXmlSaveFailure);
        window.dispatchEvent(new ErrorEvent('error', {message: ErrorMessages.MainXmlSaveFailure, error}));
        reject(error);
      }

      // check other xmls
      try {
        const nodesInSettings = doc.Settings.Root;
        const nodesInStyles = doc.Styles.Root;
        // console.log(nodesInSettings, nodesInStyles)
        if ( !(nodesInSettings && nodesInSettings.length > 0 && nodesInStyles && nodesInStyles.length > 0) ) {
          throw new Error('Other xmls save failure');
        }
      } catch (error) {
        // alert(ErrorMessages.MinorXmlSaveFailure);
        // tslint:disable-next-line: no-console
        console.warn(ErrorMessages.MinorXmlSaveFailure);
        window.dispatchEvent(new ErrorEvent('error', {message: ErrorMessages.MinorXmlSaveFailure, error}));
        reject(error);
      }

      /** generate format files from examples */
      // FormatExamples.generateFromExamples(doc);

      // const zstdCodec = zstdCodecPack.ZstdCodec;
      const fileReader = new FileReader();
      const packer = new Packer();

      // test save cache
      // let testPromise = new Promise((resolve, reject) => {
      //   reject(new Error('zip failure'))
      // });
      options.time += new Date().getTime() - date.getTime();

      const pages = props.documentCore.getDocument().getPages();
      if (bAllowHtml === true && pages.length <= 3) {
        const html = new ExportHtml(this.host);
        // tslint:disable-next-line: newline-per-chained-call
        html.getExportHtml().then((result) => {
          // console.log(result)
          doc.setHtml(result);
          // tslint:disable-next-line: newline-per-chained-call
          this.fileToBlobApi(doc, props, bSaveToString, bSaveSelectedArea, options, bAllowHtml).then((newBlob) => {
            // console.log(newBlob)
            resolve(newBlob);
          });
        });
      } else {
        // tslint:disable-next-line: newline-per-chained-call
        this.fileToBlobApi(doc, props, bSaveToString, bSaveSelectedArea, options).then((result) => {
          resolve(result);
        });
      }
    });
  }

  public generateXml(props: any, xmlName: string): any {
    const doc = new Document();
    const uniqueImageList: Map<string, string> = new Map();

    try {
      this.generateFromSVG(doc, props, uniqueImageList);
    } catch (error) {
      const documentCore = props.documentCore;
      const date = new Date();
      // tslint:disable-next-line: no-console
      console.warn(ErrorMessages.SaveIterationFailure);
      // tslint:disable-next-line: no-console
      console.log(error);
      window.dispatchEvent(new ErrorEvent('error', {message: ErrorMessages.SaveIterationFailure, error}));
      logger.error({id: documentCore.getCurrentId(), code: error.stack,
        startTime: date, args: null, name: 'generateXml.generateFromSVG'});
    }

    /** generate format files from examples */
    // FormatExamples.generateFromExamples(doc);

    const packer = new Packer();

    // packer.getXmlData(doc, xmlName).then(data => {
    //     console.log(data);
    // });

    return packer.getXmlData(doc, xmlName);

  }

  public generateXmls(props: any, xmlNameArray: string[]): Promise<any[]> {
    const doc = new Document();
    const uniqueImageList: Map<string, string> = new Map();

    try {
      this.generateFromSVG2(doc, props, uniqueImageList);
    } catch (error) {
      const documentCore = props.documentCore;
      const date = new Date();
      // tslint:disable-next-line: no-console
      console.warn(ErrorMessages.SaveIterationFailure);
      // tslint:disable-next-line: no-console
      console.log(error);
      window.dispatchEvent(new ErrorEvent('error', {message: ErrorMessages.SaveIterationFailure, error}));
      logger.error({id: documentCore.getCurrentId(), code: error.stack,
        startTime: date, args: null, name: 'generateXmls.generateFromSVG'});
    }

    const packer = new Packer();

    const promiseArr = [];
    for (const xmlName of xmlNameArray) {
      promiseArr.push(packer.getXmlData(doc, xmlName));
    }

    return Promise.all(promiseArr);
  }

  /**
   * Generate format files from editor
   */
  public generateFromSVG(doc: Document, props: any, uniqueImageList: Map<string, string>,
                         bSaveSelectedArea: boolean = false): void {

    const logicDocument: LogicDocument = props.documentCore.getDocument();
    const defaultParaProperty: IDefaultParaProperty = props.documentCore.getDefaultParaProperty();
    const defaultTextProperty: IDefaultTextProperty = props.documentCore.getDefaultTextProperty();
    const pageProperty: PageProperty = props.documentCore.getPageProperty();
    const editorBackground: IEditorBackground = props.documentCore.getEditorBackground();

    // current
    // console.log(document);

    // defaults
    // console.log(defaultParaProperty);
    // console.log(defaultTextProperty);

    /** set styles.xml */
    doc.setDefaultParaProperty(defaultParaProperty);
    doc.setDefaultTextProperty(defaultTextProperty);
    doc.setEditorBackground(editorBackground);

    /** set settings.xml */
    doc.setSettings(props);

    /** set document.xml */

    /** save selected area? */
    let loopContents = null;
    if (!bSaveSelectedArea) {
      loopContents = logicDocument.content;
    } else {
      loopContents = props.selectedArea;
    }
    for (const para of loopContents) { // DocumentContent

      if (para instanceof ParagraphCore) { // Paragraph
        const curPara = new Paragraph();

        this.traversePara(curPara, para, logicDocument, defaultParaProperty, defaultTextProperty,
           doc, uniqueImageList, bSaveSelectedArea);

        // console.log(curPara)
        /** add back the paragraph to doc */
        doc.addParagraph(curPara);

      } else if (para instanceof TableCore) { // table

        try {
          this.traverseTable(para, logicDocument, defaultParaProperty, defaultTextProperty, doc, uniqueImageList,
             -1, doc);
        } catch (error) {
          // tslint:disable-next-line: no-console
          console.warn(ErrorMessages.XmlCorrupted);
          // console.log(error)
          window.dispatchEvent(new ErrorEvent('error', {message: ErrorMessages.MainXmlSaveFailure, error}));
        }

      } else if (para instanceof RegionCore) {
        // console.log(para)
        const depth = 1;
        this.traverseRegion(para, logicDocument, defaultParaProperty,
          defaultTextProperty, doc, uniqueImageList, depth, doc);
      }

    } // DocumentContent end

    // console.log(pageProperty)
    const {header, footer, gutter, orientation} = SECTION_PROPERTIES_DEFAULT;

    // console.log(getMMFromPx(logicDocument.sectionProperty.getPageMarginsHeader()));
    const sectionProperties: SectionPropertiesOptions = {
      width: pageProperty.width,
      height: logicDocument.isInlineMode() ? 
             SECTION_PROPERTIES_DEFAULT.height : // 使用默认A4高度
             pageProperty.height,
      top: pageProperty.paddingTop,
      right: pageProperty.paddingRight,
      bottom: pageProperty.paddingBottom,
      left: pageProperty.paddingLeft,
      header: logicDocument.sectionProperty.getPageMarginsHeader(),
      footer: logicDocument.sectionProperty.getPageMarginsFooter(),
      gutter,
      orientation,
      showHeader: logicDocument.getShowHeader() === true ? 1 : 0,
      showFooter: logicDocument.getShowFooter() === true ? 1 : 0,
      protectHeaderFooter: logicDocument.getProtectHeaderFooter() === true ? 1 : 0,
      showHeaderBorder: logicDocument.getShowHeaderBorder() === true ? 1 : 0,
      showFooterBorder: logicDocument.getShowFooterBorder() === true ? 1 : 0,
    };

    doc.addSectionProperties(sectionProperties);

    /** set header1.xml */
    doc.createHeader();
    const headerDefault = logicDocument.sectionProperty.getHeaderDefault(); // suppose no first page diff yet
    if (headerDefault != null) {
      const headerContents = headerDefault.getTrueContent();
      for (const para of headerContents) {
        if (para instanceof ParagraphCore) { // Paragraph
          const curPara = new Paragraph();

          this.traversePara(curPara, para, logicDocument, defaultParaProperty, defaultTextProperty,
             doc, uniqueImageList, bSaveSelectedArea);

          // console.log(curPara)
          /** add back the paragraph to doc */
          doc.Headers[0].addParagraph(curPara);
        } else if (para instanceof TableCore) { // table

          try {
            // tslint:disable-next-line: max-line-length
            this.traverseTable(para, logicDocument, defaultParaProperty, defaultTextProperty, doc, uniqueImageList, HeaderFooterType.Header);
          } catch (error) {
            // tslint:disable-next-line: no-console
            console.warn(ErrorMessages.XmlCorrupted);
            // console.log(error)
            window.dispatchEvent(new ErrorEvent('error', {message: ErrorMessages.MainXmlSaveFailure, error}));
          }

        } else if (para instanceof RegionCore) {
          // tslint:disable-next-line: no-console
          console.warn('headerfooter should have no region');
        }
      }
    }

    /** set footer.xml */
    doc.createFooter();
    const footerDefault = logicDocument.sectionProperty.getFooterDefault(); // suppose no first page diff yet
    if (footerDefault != null) {
      const footerContents = footerDefault.getTrueContent();
      for (const para of footerContents) {
        if (para instanceof ParagraphCore) { // Paragraph
          const curPara = new Paragraph();

          this.traversePara(curPara, para, logicDocument, defaultParaProperty, defaultTextProperty,
            doc, uniqueImageList, bSaveSelectedArea);

          // console.log(curPara)
          /** add back the paragraph to doc */
          doc.Footer.addParagraph(curPara);
        } else if (para instanceof TableCore) { // table

          try {
            // tslint:disable-next-line: max-line-length
            this.traverseTable(para, logicDocument, defaultParaProperty, defaultTextProperty, doc, uniqueImageList, HeaderFooterType.Footer);
          } catch (error) {
            // tslint:disable-next-line: no-console
            console.warn(ErrorMessages.XmlCorrupted);
            // console.log(error)
            window.dispatchEvent(new ErrorEvent('error', {message: ErrorMessages.MainXmlSaveFailure, error}));
          }

        } else if (para instanceof RegionCore) {
          // tslint:disable-next-line: no-console
          console.warn('headerfooter should have no region');
        }
      }
    }

    /** set media.xml */
    // console.log(uniqueImageList) // media should wait till uniqueImageList is filled
    const imageContainer = new Images();
    doc.Media.addImageContainer(imageContainer);
    // console.log(uniqueImageList)
    const paintingContainer = new Paintings();
    doc.Media.addPaintingContainer(paintingContainer);

    for (const [key, value] of uniqueImageList.entries()) {
      const mediaImageAttributes: IMediaImageAttributesProperties = {
        name: key,
      };
      const image = new MediaImage(mediaImageAttributes);
      imageContainer.addImage(image);
      image.addContent(value);

      const drawing = logicDocument.getDrawingByName(key);
      // console.log(drawing)
      if (drawing.isSvgDrawing() === true) {
        const painting = new EditableMediaImage(mediaImageAttributes);
        paintingContainer.addEditableImage(painting);

        // equationElem
        if (drawing instanceof ParaEquation) {
          painting.addContent(drawing.equationElem);
        }

      }
    }

  }

  /****
   * 只生成content.xml 相关的
   */
  public generateContentFromSVG(doc: Document, props: any, uniqueImageList: Map<string, string>,
                          bSaveSelectedArea: boolean = false,
                          sModJson: ISaveModJson = null): void {

    const logicDocument: LogicDocument = props.documentCore.getDocument();
    const defaultParaProperty: IDefaultParaProperty = props.documentCore.getDefaultParaProperty();
    const defaultTextProperty: IDefaultTextProperty = props.documentCore.getDefaultTextProperty();
   
    /** set document.xml */

    /** save selected area? */
    let loopContents = null;
    if (!bSaveSelectedArea) {
      loopContents = logicDocument.content;
    } else {
      loopContents = props.selectedArea;
    }

    let secStack = [];
    let prePara;
    // console.log(loopContents) // saveselectedareatostream() may have diff content(region title)
    for (const para of loopContents) { // DocumentContent

      if (para instanceof ParagraphCore) { // Paragraph
        let curPara; // = new Paragraph();
        const secCount = secStack.length;

        if (0 === secCount) {
          curPara = new Paragraph();
          prePara = curPara;
        } else {
          curPara = prePara;
        }
        this.traversePara3(curPara, para, logicDocument, defaultParaProperty, defaultTextProperty,
          doc, uniqueImageList, bSaveSelectedArea, sModJson, secStack);

        // console.log(curPara)
        /** add back the paragraph to doc */
        if (0 === secCount) {
          doc.addParagraph(curPara);
        }

      } else if (para instanceof TableCore) { // table
        secStack = [];

        try {
          this.traverseTable2(para, logicDocument, defaultParaProperty, defaultTextProperty, doc, uniqueImageList,
            -1, doc, bSaveSelectedArea, sModJson);
        } catch (error) {
          // tslint:disable-next-line: no-console
          console.warn(ErrorMessages.XmlCorrupted);
          console.log(error)
          window.dispatchEvent(new ErrorEvent('error', { message: ErrorMessages.MainXmlSaveFailure, error }));
        }

      } else if (para instanceof RegionCore) {
        if (para.isLoadCacheRegion()) {
          continue;
        }
        secStack = [];
        // console.log(para)
        const depth = 1;
        this.traverseRegion2(para, logicDocument, defaultParaProperty,
          defaultTextProperty, doc, uniqueImageList, depth, doc, bSaveSelectedArea, sModJson);
      } else if (para instanceof NISTableCore) { // nisTable
        secStack = [];
        try {
          this.traverseNISTable(para, logicDocument, defaultParaProperty, defaultTextProperty, doc, uniqueImageList,
            -1, doc, bSaveSelectedArea, sModJson);
        } catch (error) {
          // tslint:disable-next-line: no-console
          console.warn(ErrorMessages.XmlCorrupted);
          // tslint:disable-next-line: no-console
          // console.log(error);
          window.dispatchEvent(new ErrorEvent('error', { message: ErrorMessages.MainXmlSaveFailure, error }));
        }
      }

    } // DocumentContent end

  }

  /**
   * Generate format files from editor(new sdt structure)
   */
  public generateFromSVG2(doc: Document, props: any, uniqueImageList: Map<string, string>,
                          bSaveSelectedArea: boolean = false,
                          sModJson: ISaveModJson = null): void {

    const logicDocument: LogicDocument = props.documentCore.getDocument();
    const defaultParaProperty: IDefaultParaProperty = props.documentCore.getDefaultParaProperty();
    const defaultTextProperty: IDefaultTextProperty = props.documentCore.getDefaultTextProperty();
    const pageProperty: PageProperty = props.documentCore.getPageProperty();
    const editorBackground: IEditorBackground = props.documentCore.getEditorBackground();
    // const cascades = logicDocument.getAllCascades();

    // current
    // console.log(document);

    // defaults
    // console.log(defaultParaProperty);
    // console.log(defaultTextProperty);

    /** set styles.xml */
    doc.setDefaultParaProperty(defaultParaProperty);
    doc.setDefaultTextProperty(defaultTextProperty);
    doc.setEditorBackground(editorBackground);
    doc.setDynamicGridLine(logicDocument.isDynamicGridLine());

    /** set settings.xml */
    doc.setSettings(props);

    /** set document.xml */

    /** save selected area? */
    let loopContents = null;
    if (!bSaveSelectedArea) {
      loopContents = logicDocument.content;
    } else {
      loopContents = props.selectedArea;
    }

    let secStack = [];
    let prePara;
    // console.log(loopContents) // saveselectedareatostream() may have diff content(region title)
    for (const para of loopContents) { // DocumentContent

      if (para instanceof ParagraphCore) { // Paragraph
        let curPara; // = new Paragraph();
        const secCount = secStack.length;

        if (0 === secCount) {
          curPara = new Paragraph();
          prePara = curPara;
        } else {
          curPara = prePara;
        }
        this.traversePara3(curPara, para, logicDocument, defaultParaProperty, defaultTextProperty,
          doc, uniqueImageList, bSaveSelectedArea, sModJson, secStack);

        // console.log(curPara)
        /** add back the paragraph to doc */
        if (0 === secCount) {
          doc.addParagraph(curPara);
        }

      } else if (para instanceof TableCore) { // table
        secStack = [];

        try {
          this.traverseTable2(para, logicDocument, defaultParaProperty, defaultTextProperty, doc, uniqueImageList,
            -1, doc, bSaveSelectedArea, sModJson);
        } catch (error) {
          // tslint:disable-next-line: no-console
          console.warn(ErrorMessages.XmlCorrupted);
          console.log(error)
          window.dispatchEvent(new ErrorEvent('error', { message: ErrorMessages.MainXmlSaveFailure, error }));
        }

      } else if (para instanceof RegionCore) {
        if (para.isLoadCacheRegion()) {
          continue;
        }
        secStack = [];
        // console.log(para)
        const depth = 1;
        this.traverseRegion2(para, logicDocument, defaultParaProperty,
          defaultTextProperty, doc, uniqueImageList, depth, doc, bSaveSelectedArea, sModJson);
      } else if (para instanceof NISTableCore) { // nisTable
        secStack = [];
        try {
          this.traverseNISTable(para, logicDocument, defaultParaProperty, defaultTextProperty, doc, uniqueImageList,
            -1, doc, bSaveSelectedArea, sModJson);
        } catch (error) {
          // tslint:disable-next-line: no-console
          console.warn(ErrorMessages.XmlCorrupted);
          // tslint:disable-next-line: no-console
          // console.log(error);
          window.dispatchEvent(new ErrorEvent('error', { message: ErrorMessages.MainXmlSaveFailure, error }));
        }
      }

    } // DocumentContent end

    // console.log(pageProperty)
    const { header, footer, gutter, orientation } = SECTION_PROPERTIES_DEFAULT;

    // console.log(getMMFromPx(logicDocument.sectionProperty.getPageMarginsHeader()));
    const sectionProperties: SectionPropertiesOptions = {
      width: pageProperty.width,
      height: logicDocument.isInlineMode() ? 
             SECTION_PROPERTIES_DEFAULT.height : // 使用默认A4高度
             pageProperty.height,
      top: pageProperty.paddingTop,
      right: pageProperty.paddingRight,
      bottom: pageProperty.paddingBottom,
      left: pageProperty.paddingLeft,
      header: logicDocument.sectionProperty.getPageMarginsHeader(),
      footer: logicDocument.sectionProperty.getPageMarginsFooter(),
      gutter,
      orientation,
      showHeader: logicDocument.getShowHeader() === true ? 1 : 0,
      showFooter: logicDocument.getShowFooter() === true ? 1 : 0,
      protectHeaderFooter: logicDocument.getProtectHeaderFooter() === true ? 1 : 0,
      showHeaderBorder: logicDocument.getShowHeaderBorder() === true ? 1 : 0,
      showFooterBorder: logicDocument.getShowFooterBorder() === true ? 1 : 0,
    };

    doc.addSectionProperties(sectionProperties);

    // Cascade.xml
    // const cascade = doc.createCascade();
    // cascade.addCascadeInfos(cascades);

    /** set header1.xml */
    doc.createHeader();
    const headerDefault = logicDocument.sectionProperty.getHeaderDefault(); // suppose no first page diff yet
    if (headerDefault != null && (bSaveSelectedArea === false || sModJson && sModJson.NeedHeaderFooter === 1)) {
      const headerContents = headerDefault.getTrueContent();
      let secStack: Section[] = [];
      for (const para of headerContents) {
        if (para instanceof ParagraphCore) { // Paragraph
          const curPara = new Paragraph();

          this.traversePara3(curPara, para, logicDocument, defaultParaProperty, defaultTextProperty,
            doc, uniqueImageList, bSaveSelectedArea, sModJson, secStack);

          // console.log(curPara)
          /** add back the paragraph to doc */
          doc.Headers[0].addParagraph(curPara);
        } else if (para instanceof TableCore) { // table
          secStack = [];
          try {
            // tslint:disable-next-line: max-line-length
            this.traverseTable2(para, logicDocument, defaultParaProperty, defaultTextProperty, doc, uniqueImageList, HeaderFooterType.Header, null, bSaveSelectedArea, sModJson);
          } catch (error) {
            // tslint:disable-next-line: no-console
            console.warn(ErrorMessages.XmlCorrupted);
            // console.log(error)
            window.dispatchEvent(new ErrorEvent('error', { message: ErrorMessages.MainXmlSaveFailure, error }));
          }

        } else if (para instanceof RegionCore) {
          // tslint:disable-next-line: no-console
          console.warn('headerfooter should have no region');
        }
      }
    }

    /** set footer.xml */
    doc.createFooter();
    const footerDefault = logicDocument.sectionProperty.getFooterDefault(); // suppose no first page diff yet
    if (footerDefault != null && (bSaveSelectedArea === false || sModJson && sModJson.NeedHeaderFooter === 1)) {
      const footerContents = footerDefault.getTrueContent();
      let secStack = [];
      for (const para of footerContents) {
        if (para instanceof ParagraphCore) { // Paragraph
          const curPara = new Paragraph();

          this.traversePara3(curPara, para, logicDocument, defaultParaProperty, defaultTextProperty,
            doc, uniqueImageList, bSaveSelectedArea, sModJson, secStack);

          // console.log(curPara)
          /** add back the paragraph to doc */
          doc.Footer.addParagraph(curPara);
        } else if (para instanceof TableCore) { // table
          secStack = [];

          try {
            // tslint:disable-next-line: max-line-length
            this.traverseTable2(para, logicDocument, defaultParaProperty, defaultTextProperty, doc, uniqueImageList, HeaderFooterType.Footer, null, bSaveSelectedArea, sModJson);
          } catch (error) {
            // tslint:disable-next-line: no-console
            console.warn(ErrorMessages.XmlCorrupted);
            // console.log(error)
            window.dispatchEvent(new ErrorEvent('error', { message: ErrorMessages.MainXmlSaveFailure, error }));
          }

        } else if (para instanceof RegionCore) {
          // tslint:disable-next-line: no-console
          console.warn('headerfooter should have no region');
        }
      }
    }

    /** set media.xml */
    // console.log(uniqueImageList) // media should wait till uniqueImageList is filled
    const imageContainer = new Images();
    doc.Media.addImageContainer(imageContainer);
    // console.log(uniqueImageList)
    const paintingContainer = new Paintings();
    doc.Media.addPaintingContainer(paintingContainer);

    for (const [key, value] of uniqueImageList.entries()) {
      const mediaImageAttributes: IMediaImageAttributesProperties = {
        name: key,
      };
      const image = new MediaImage(mediaImageAttributes);
      imageContainer.addImage(image);
      image.addContent(value);

      let drawing = logicDocument.getDrawingByName(key);
      if (drawing == null) {
        drawing = logicDocument.getDrawingBySrc(value);
      }

      if (drawing != null) {
        if (drawing instanceof ParaMediaDrawing) {
          // 添加音视频类型
          mediaImageAttributes.type = drawing.mediaType;
        } else if (drawing.isSvgDrawing() === true) {
          const painting = new EditableMediaImage(mediaImageAttributes);
          paintingContainer.addEditableImage(painting);

          // equationElem
          if (drawing instanceof ParaEquation) {
            painting.addContent(drawing.equationElem);
          }
        } else if (drawing.hasBind()) {
            const bind = drawing.getBind();
            if (bind) mediaImageAttributes.bind = JSON.stringify(bind);
        }
      } else {
        // tslint:disable-next-line: no-console
        console.warn('cannot get drawing instance in saving');
      }
    }

    const sourceBindData = logicDocument.getSourceBindJson();
    if (sourceBindData) {
      doc.setSourceBind(sourceBindData);
    }

    if (sModJson?.nisTable) {
      doc.setNISTableJson(JSON.stringify(sModJson.nisTable));
    }

  }

  /** create current run if null */
  public initializeRun(currentRun: TextRun, portion: any, defaultTextProperty: IDefaultTextProperty ): TextRun {
    if (!currentRun) {

        // Observation: it's safe to compose w:rPr when run has just been created.
      //              So always pair them up
      currentRun = new TextRun(); // just pure <w:r> if no parameter passed in

      /** check if run contains unique properties; if so, write them into w:rPr  */
      this.processRunUniqueProperties(currentRun, portion.textProperty, defaultTextProperty);

      if (portion.getHidden() && !(portion.isNewControlStart() || portion.isNewControlEnd())) {
        currentRun.hidden();
      }
    }
    return currentRun; // need to return, since it's initially null, not object, so not pass by ref yet
  }

  public prepareIsToPourText(para: ParagraphCore, portion: any, i: number): boolean {
    let isToPourText = true;
    let nextPortion = para.content[i + 1];
    const reviewInfo = portion.getReviewInfo();
    const nextReviewInfo = nextPortion ? nextPortion.getReviewInfo() : null;

    if (nextPortion && nextPortion.getReviewType() === portion.getReviewType() ) {
      // region title portion must pour
      // TODO: may be not accurate
      const paraParent = para.getParent();
      const curRegion = (paraParent instanceof DocumentContent) ? (paraParent as DocumentContent).getParent() : null;
      // console.log(curRegion)
      if (curRegion instanceof RegionCore) {
        if (portion.content.length > 0 && portion.content[0] instanceof ParaTextExtend) {
          isToPourText = true;
          return isToPourText;
        } else {
          // when coming from saveselectedareatostream(), ParaTextExtend is not there
          const paraItems = portion.content;
          const regionTitle = curRegion.getTitle();
          // console.log(curRegion)
          // console.log(regionTitle)
          let text = '';
          for (const paraItem of paraItems) {
            if (paraItem instanceof ParaText) {
              text += paraItem.content;
            } else {
              // as long as not ParaText, must not be title portion
              break;
            }
          }
          // console.log(text)
          if (text === regionTitle) {
            isToPourText = true;
            return isToPourText;
          }

        }
      }

      // check if in NIS table time type
      const isInNISTimeCell = this.isInNISTimeCell(para);
      if (isInNISTimeCell === true) {
        return true;
      }

      // simple compare: whether next run with same props of current run
      if (JSON.stringify(portion.textProperty) === JSON.stringify(nextPortion.textProperty)
        || (nextPortion.content.length === 0)) {

          // 签名控件-集合： pretext{空portion}[]. improvise to solve it.
          if (nextPortion.isEmpty(false) === true) {
            // tslint:disable-next-line: no-console
            // console.log('signature collection empty portion before [');
            nextPortion = para.content[i + 2];
            if (nextPortion == null) {
              return isToPourText;
            }
          }

          const firstParaItem = nextPortion.content[0];
          if ( firstParaItem instanceof ParaNewControlBorder) { // aaa[TITLExxxxx]
            if (firstParaItem.isNewControlStartBoder() === true) {
              isToPourText = true;
            }
          } else {
            // if ( nextReviewInfo && reviewInfo &&
            //         (nextReviewInfo.getUserId() !== reviewInfo.getUserId()
            //           || nextReviewInfo.getLevel() !== reviewInfo.getLevel()) ) {
            if (reviewInfo) {
              isToPourText = true;
            } else {
              // TODO: add more limitation? only pure paraText portion?
              isToPourText = false;
            }
          }
      }
    } else { /** if it's the last portion, must pour out text */
      isToPourText = true;
    }
    return isToPourText;
  }

  public addFileHeaderOld(buffer: ArrayBuffer, logicDocument?: LogicDocument, bAllowHtml: boolean = false):
        ArrayBuffer {
    const newBuffer: ArrayBuffer = new ArrayBuffer(buffer.byteLength + FILE_HEADER_LENGTH);
    let revisedUint8Array: Uint8Array = null;
    const output = [];

    // add file header desc - 3 bytes
    for (const fileDescChar of FILE_HEADER_DESC) {
      // tslint:disable-next-line: newline-per-chained-call
      output.push(+fileDescChar.charCodeAt(0).toString(8));
    }

    // add file header length - 2 bytes
    if (FILE_HEADER_LENGTH > 0 && FILE_HEADER_LENGTH < 256) {
      output.push(+(0).toString(8));
    }
    output.push(+FILE_HEADER_LENGTH.toString(8));

    // add file header version - 1 byte
    output.push(+FILE_HEADER_VERSION2.toString(8));
    // output.push(+FILE_HEADER_ENCODE_VERSION.toString(8));

    // add file header document version - 2 bytes
    if (logicDocument != null) {
      let documentVersion = logicDocument.getFileVersion();
      // console.log(documentVersion);
      if (documentVersion >= 0) {
        documentVersion += 1;
        const binaryDocVer = documentVersion.toString(2);
        const binaryDocVerLen = binaryDocVer.length;
        // console.log(documentVersion)
        let firstByte = 0;
        let secondByte = 0;
        if (binaryDocVerLen > 8) {
          // > 255
          const diff = binaryDocVerLen - 8;
          if (diff < 9) {
            // <= 65535
            firstByte = parseInt(binaryDocVer.slice(0, diff) + '00000000', 2); // return base 10
            secondByte = parseInt(binaryDocVer.slice(diff), 2);
            // console.log(firstByte, secondByte)
          }
          // toString(): convert BASE 10 number to base 2 8 10 16...
          output.push(+firstByte.toString(8));
          output.push(+secondByte.toString(8));
        } else if (binaryDocVerLen > 0) {
          // [0, 255]
          firstByte = 0;
          secondByte = parseInt(binaryDocVer, 2);
          output.push(+firstByte.toString(8));
          output.push(+secondByte.toString(8));
        }
      } else {
        this.addDefaultDocumentVersion(output);
      }

    } else {
      this.addDefaultDocumentVersion(output);
    }

    // indicate if package has html file - 1 byte
    if (bAllowHtml === true && logicDocument != null && logicDocument.getPages().length <= 3) {
        output.push(+(1).toString(8));
    } else {
      output.push(+(0).toString(8));
    }

    // add preserved chars - 15 bytes
    const remainingLen = FILE_HEADER_LENGTH - FILE_HEADER_BYTELENGTH - FILE_HEADER_DESC_BYTELENGTH
                          - FILE_HEADER_VERSION_BYTELENGTH - FILE_HEADER_DOCUMENT_VERSION_BYTELENGTH
                          - FILE_HEADER_HTMLEXIST_BYTELENGTH;
    // console.log(remainingLen)
    for (let i = 0; i < remainingLen; i++) {
      // output.push(+FILE_HEADER_PLACEHOLDER[i].charCodeAt(0)
      output.push(+'0'.charCodeAt(0)
        .toString(8)); // '+' -> to number -> Uint8Array: array of 8-bit unsigned integers
    }

    // convert Uint8Array to a normal array so that it can be concated, and then convert back
    revisedUint8Array = new Uint8Array(output.concat(Array.from(new Uint8Array(buffer))));

    // set Uint8Array in the newBuffer, it would sync up with other arrays within the newBuffer
    new Uint8Array(newBuffer).set(revisedUint8Array);

    // console.log(buffer)
    // console.log(newBuffer)
    return newBuffer;
  }

  public addFileHeader(buffer: ArrayBuffer, logicDocument?: LogicDocument, bAllowHtml: boolean = false,
                       options?: any): ArrayBuffer {
    const header = new ArrayBuffer(FILE_HEADER_LENGTH);
    const headerView = new DataView(header);

    // add file header desc - 3 bytes
    let current = 0;
    for (const fileDescChar of FILE_HEADER_DESC) {
      headerView.setUint8(current, fileDescChar.charCodeAt(0));
      current++;
    }

    // add file header length - 2 bytes
    headerView.setUint16(current, FILE_HEADER_LENGTH);
    current = current + 2;

    // add file header version - 1 byte
    headerView.setUint8(current, FILE_HEADER_VERSION2);
    current++;

    // add file header document version - 2 bytes
    let documentVersion = logicDocument != null ? logicDocument.getFileVersion() : -1;
    if (documentVersion >= 0) {
      documentVersion += 1;
    } else {
      documentVersion = FILE_HEADER_DOCUMENT_VERSION;
    }

    // special case
    if (options != null && options.bSaveStructContent === true) {
      documentVersion = FILE_HEADER_DOCUMENT_VERSION;
    }

    headerView.setUint16(current, documentVersion);
    current = current + 2;

    // indicate if package has html file - 1 byte
    if (bAllowHtml === true && logicDocument != null && logicDocument.getPages().length <= 3) {
      headerView.setUint8(current, 1);
    } else {
      headerView.setUint8(current, 0);
    }
    current++;

    // rest is placeholder, default to be 0

    const bufferView = new DataView(buffer);
    const newLength = buffer.byteLength + FILE_HEADER_LENGTH;
    const newBuffer: ArrayBuffer = new ArrayBuffer(newLength);
    const newView = new Uint8Array(newBuffer);
    for (let i = 0; i < newLength; i++) {
      if (i < FILE_HEADER_LENGTH) {
        newView[i] = headerView.getUint8(i);
      } else {
        newView[i] = bufferView.getUint8(i - FILE_HEADER_LENGTH);
      }
    }
    return newBuffer;

  }

  /**
   * check if paragraph contains unique properties; if so, write them into w:pPr
   */
  public processParaUniqueProperties(curPara: Paragraph, curParaProperty: ParaProperty,
                                     defaultParaProperty: IDefaultParaProperty): void {

    if (!curParaProperty || !curPara || !defaultParaProperty) {
      // tslint:disable-next-line: no-console
      console.warn('!curParaProperty || !curPara || !defaultParaProperty');
      return null;
    }

    /** check indentation, not included in defaults */
    if (curParaProperty.paraInd.left || curParaProperty.paraInd.firstLine || curParaProperty.paraInd.right) {
      const indentMapping = {};
      indentMapping['left'] = (curParaProperty.paraInd.left !== 0) ? curParaProperty.paraInd.left : 0;
      indentMapping['firstLine'] = (curParaProperty.paraInd.firstLine !== 0) ?
        curParaProperty.paraInd.firstLine : 0;
      indentMapping['right'] = (curParaProperty.paraInd.right !== (0 || undefined)) ?
        curParaProperty.paraInd.right : 0;

      curPara.indent(indentMapping);
    }

    /** alignment */
    if (defaultParaProperty.alignment !== curParaProperty.alignment) {
      curPara.alignment(curParaProperty.alignment);
    }

    /** paraspacing */
    if ((defaultParaProperty.paraSpacing) &&
      curParaProperty.paraSpacing.lineSpacingType !== defaultParaProperty.paraSpacing) {
      // console.log(curParaProperty.paraSpacing.lineSpacingType);
      // console.log(defaultParaProperty.paraSpacing)
      curPara.paraSpacing(curParaProperty.paraSpacing.lineSpacingType,
        Number(curParaProperty.paraSpacing.lineSpacing.toFixed(2)));
    }

    /** pagebreak */
    if (curParaProperty.bPageBreakBefore === true) {
      curPara.pageBreak();
    }

    curPara.paraWesNewLine(curParaProperty.bWordWrap ? 1 : 0);

    if (NURSING_FEATURE && curParaProperty.nisSignAuthor) {
      curPara.nisSignAuthor(curParaProperty.nisSignAuthor);
    }
  }

  /**
   * check if run contains unique properties; if so, write them into w:rPr
   */
  public processRunUniqueProperties(textRun: TextRun, curTextProperty: TextProperty,
                                    defaultTextProperty: IDefaultTextProperty): void {

    if (!textRun || !curTextProperty || !defaultTextProperty) {
      // tslint:disable-next-line: no-console
      console.warn('!textRun || !curTextProperty || !defaultTextProperty');
      return null;
    }

    if (curTextProperty.fontSize !== defaultTextProperty.fontSize) {
      textRun.size(curTextProperty.fontSize);
    }

    // if (curTextProperty.color && curTextProperty.color !== defaultTextProperty.color) {
    if (curTextProperty.color && isDefaultTextColor(curTextProperty.color) === false) {
      // console.log(curTextProperty.color, defaultTextProperty.color)
      textRun.color(curTextProperty.color);
    }

    // console.log(curTextProperty.fontFamily, defaultTextProperty.fontFamily)
    /** if platform is mac, use windows' equivalant to compare */
    if (isMacOs) {
      let curFont = JSON.parse(JSON.stringify(curTextProperty.fontFamily));
      for (const i in FONT_MAPPING) {
        if (FONT_MAPPING[i].mac === curTextProperty.fontFamily) {
          curFont = FONT_MAPPING[i].windows;
          break;
        }
      }
      if (curFont !== defaultTextProperty.fontFamily) {
        // textRun.font(curTextProperty.fontFamily);
        textRun.font(curFont);
      }
    } else if (curTextProperty.fontFamily !== defaultTextProperty.fontFamily) {
      textRun.font(curTextProperty.fontFamily);
    }

    if (curTextProperty.fontWeight &&
      FONT_WEIGHT_TYPE.get(curTextProperty.fontWeight) !== defaultTextProperty.fontWeight) {
      textRun.bold();
    }

    if (curTextProperty.fontStyle &&
      FONT_STYLE_TYPE.get(curTextProperty.fontStyle) !== defaultTextProperty.fontStyle) {
      textRun.italic();
    }

    // 忽略波浪线属性，不保存到文档中
    if (curTextProperty.textDecorationLine && 
        curTextProperty.textDecorationLine !== TextDecorationLineType.WavyUnderline &&
        TEXT_DECORATION_LINE_TYPE.get(curTextProperty.textDecorationLine) !== defaultTextProperty.textDecorationLine) {
      textRun.underline(); // for now, may have many variations
    }

    if (curTextProperty.backgroundColor && curTextProperty.backgroundColor !== defaultTextProperty.backgroundColor) {
      textRun.highLight(curTextProperty.backgroundColor);
    }

    if (curTextProperty.vertAlign === TextVertAlign.Super) {
      // one of the check may be redundant
      textRun.superScript();
    }

    if (curTextProperty.vertAlign === TextVertAlign.Sub) {
      // one of the check may be redundant
      textRun.subScript();
    }

  }

  /**
   * save xml info to cache
   */
  public saveToCache(props: any, data: any): void {
    // console.log(props, data);

    const prevData = localStorage.getItem('cache');
    if (prevData !== data) {
      try {
        localStorage.setItem('cache', data);
      } catch (error) {
        // tslint:disable-next-line: no-console
        console.log('cache save failed');
        // const newError = new Error('cache save failed');
        window.dispatchEvent(new ErrorEvent('error', {message: error.message, error}));
      }
    } else {
      // tslint:disable-next-line: no-console
      console.log('same as cache, not saved(when zip fail)');
    }
  }

  /**
   * remove empty portion if any
   */
  // public removeEmptyPortion(para: any): void {

  //   const portions = para.content;
  //   // console.log(portions)

  //   const emptyIndices = [];
  //   for (let i = 0; i < portions.length; i++) {
  //     if (portions[i].content.length === 0) {
  //       emptyIndices.push(i);
  //       // console.log("empty!");
  //     }
  //   }

  //   for (const index of emptyIndices) {
  //     // console.log('removing empty portion');
  //     portions.splice(index, 1);
  //   }

  //   // console.log(portions);
  // }

  /**
   * prepare IDrawingAttributesProperties that needed in saving image
   */
  // tslint:disable-next-line: max-line-length
  public prepareImageAttrs(paraItem: ParaDrawing, doc: Document, uniqueImageList: Map<string, string>): IDrawingAttributesProperties {
    // console.log(paraItem);

    // search existing image href first; if not exist, set the new image href into the list
    let sourceId = null;
    for (const [key, value] of uniqueImageList.entries()) {
        // 处理普通图片和 音视频
      if (paraItem instanceof ParaMediaDrawing) {
        if (value === paraItem.mediaSrc) {
            sourceId = key;
            break;
        }
      } else if (value === paraItem.src) {
        sourceId = key;
        break;
      }
    }
    if (!sourceId) {
      // cannot find unique image in the map. save image to the map
      // sourceId = '图片' + (uniqueImageList.size + 1);
      sourceId = paraItem.getDrawingName();
      if (paraItem instanceof ParaMediaDrawing) {
        uniqueImageList.set(sourceId, paraItem.mediaSrc);
      } else {
        uniqueImageList.set(sourceId, paraItem.src);
      }
    }
    // console.log(uniqueImageList)

    let drawingAttributes: IDrawingAttributesProperties = { // fail safe
      name: '',
      width: 0,
      height: 0,
      imageRatio: 1,
      anchorType: AnchorType.Inline,
      // href: '',
      source: '',
      preferRelativeResize: 0,
      sizeProtect: 0,
      deleteProtect: 0,
      copyProtect: 0,
    };

    if (paraItem) {
      drawingAttributes = {
        name: paraItem.name,
        width: paraItem.width,
        height: paraItem.height,
        imageRatio: paraItem.imageRatio,
        anchorType: AnchorType.Inline,
        // href: paraItem.src,
        source: sourceId,
        preferRelativeResize: Number(paraItem.preserveAspectRatio),
        sizeProtect: Number(paraItem.sizeLocked),
        deleteProtect: Number(paraItem.deleteLocked),
        copyProtect: Number(paraItem.copyProtect),
      };
      if (2 !== paraItem.getVertAlign()) {
        drawingAttributes.vertAlign = paraItem.getVertAlign();
      }
      if (paraItem instanceof ParaMediaDrawing) {
          drawingAttributes.mediaType = paraItem.mediaType;
      }
    }

    return drawingAttributes;

  }

  // tslint:disable-next-line: max-line-length
  public prepareEditableImageAttrs(paraItem: ParaDrawing, uniqueImageList: Map<string, string>): IDrawingAttributesProperties {
    // console.log(paraItem);

    // search existing image href first; if not exist, set the new image href into the list
    let sourceId = null;
    for (const [key, value] of uniqueImageList.entries()) {
      if (value === paraItem.src) {
        sourceId = key;
        break;
      }
    }
    if (!sourceId) {
      // cannot find unique image in the map. save image to the map
      // sourceId = '图片' + (uniqueImageList.size + 1);
      sourceId = paraItem.getDrawingName();
      uniqueImageList.set(sourceId, paraItem.src);
    }
    // console.log(uniqueImageList)

    let drawingAttributes: IDrawingAttributesProperties = { // fail safe
      name: '',
      width: 0,
      height: 0,
      source: '',
    };

    if (paraItem) {
      drawingAttributes = {
        name: paraItem.name,
        width: paraItem.width,
        height: paraItem.height,
        source: sourceId,
      };
      if (2 !== paraItem.getVertAlign()) {
        drawingAttributes.vertAlign = paraItem.getVertAlign();
      }
    }

    return drawingAttributes;

  }

  public preparePageNumProps(paraItem: ParaPageNum, doc: Document): IPageNumAttributesProperties {
    const pageNumProps: IPageNumAttributesProperties = {
      pageNumType: null,
      pageNumString: null,
      startIndex: null,
    };

    if (paraItem != null) {
      const pageNumType = paraItem.getPageNumType();
      if ( pageNumType !== PAGENUMPROP_DEFAULT.pageNumType) {
        pageNumProps.pageNumType = pageNumType;
      }

      const pageNumString = paraItem.getPageNumString();
      if ( pageNumString !== PAGENUMPROP_DEFAULT.pageNumString) {
        pageNumProps.pageNumString = pageNumString;
      }

      const startIndex = paraItem.getStartIndex();
      if (startIndex !== PAGENUMPROP_DEFAULT.startIndex) {
        pageNumProps.startIndex = startIndex;
      }
    }

    return pageNumProps;
  }

  // tslint:disable-next-line: max-line-length
  public prepareMedEquationAttrs(paraItem: ParaEquation, doc: Document, uniqueImageList: Map<string, string>): IMedEquationAttributesProperties {

    let sourceId = null;
    for (const [key, value] of uniqueImageList.entries()) {
      if (value === paraItem.src) {
        sourceId = key;
        break;
      }
    }
    if (!sourceId) {
      // cannot find unique image in the map. save image to the map
      // sourceId = '图片' + (uniqueImageList.size + 1);
      sourceId = paraItem.getDrawingName();
      uniqueImageList.set(sourceId, paraItem.src);
    }
    // console.log(uniqueImageList)

    let medEquationAttributes: IMedEquationAttributesProperties = { // fail safe
      name: '',
      width: 0,
      height: 0,
      imageRatio: 1,
      // href: '',
      source: '',
      mathType: EquationType.Default,
    };

    if (paraItem) {
      medEquationAttributes = {
        name: paraItem.name,
        width: paraItem.width,
        height: paraItem.height,
        imageRatio: paraItem.imageRatio,
        // href: paraItem.src,
        source: sourceId,
        mathType: paraItem.equationType,
      };
      if (2 !== paraItem.getVertAlign()) {
        medEquationAttributes.vertAlign = paraItem.getVertAlign();
      }
    }

    return medEquationAttributes;
  }

  public prepareBarcodeAttrs(paraItem: ParaBarcode, imageList: Map<string, string>, bQRCode?: boolean): IBarcodeAllProperties {

    let sourceId = null;
    for (const [key, value] of imageList.entries()) {
      if (value === paraItem.src) {
        sourceId = key;
        break;
      }
    }
    if (!sourceId) {
      // cannot find unique image in the map. save image to the map
      // sourceId = '图片' + (uniqueImageList.size + 1);
      sourceId = paraItem.getDrawingName();
      imageList.set(sourceId, paraItem.src);
    }
    // console.log(uniqueImageList)

    let medEquationAttributes: IBarcodeAllProperties = { // fail safe
      name: '',
      width: 0,
      height: 0,
      // imageRatio: 1,
      // href: '',
      src: '',
      textAlign: '',
      // bUse: false,
      content: '',
      errorCL: '',
      // sourceBind: '',
    };

    if (paraItem) {
      if (!bQRCode) {
        medEquationAttributes = {
          name: paraItem.name,
          width: paraItem.width,
          height: paraItem.height,
          // imageRatio: paraItem.imageRatio,
          // href: paraItem.src,
          src: sourceId,
          textAlign: paraItem.textAlign,
          // bUse: paraItem.bUse,
          content: paraItem.content,
        };

        if (!paraItem.bUse) {
          medEquationAttributes.bUse = false;
        }
      } else {
        medEquationAttributes = {
          name: paraItem.name,
          width: paraItem.width,
          height: paraItem.height,
          // imageRatio: paraItem.imageRatio,
          // href: paraItem.src,
          src: sourceId,
          content: paraItem.content,
          errorCL: paraItem.getErrorCL(),
        };
      }

      if (paraItem.getSourceDataBind()) {
        medEquationAttributes.sourceBind = JSON.stringify(paraItem.getSourceDataBind());
      }
    }

    return medEquationAttributes;
  }

  public retrieveCurrentContentControl(paraItem: ParaNewControlBorder, logicDocument: LogicDocument): NewControl {
    let currentContentControl: NewControl = null;
    if (logicDocument && paraItem) {
      let slicePos = -1;
      const indexOfStart = paraItem.name.lastIndexOf('_start');
      if (indexOfStart === -1) {
        const indexOfEnd = paraItem.name.lastIndexOf('_end');
        if (indexOfEnd !== -1) {
          slicePos = indexOfEnd;
        }
      } else {
        slicePos = indexOfStart;
      }
      const contentControlName = paraItem.name.slice(0, slicePos);
      currentContentControl = logicDocument.newControlManager.getNewControlByName(contentControlName);
    }

    return currentContentControl;
  }

  public prepareContentControlAttrs(contentControl: NewControl): IContentControlAttributesProperties {
    let contentControlAttrs: IContentControlAttributesProperties = { // fail safe
      name: '',
      type: STD_TYPE_DEFAULT,
    };

    if (contentControl) {
      contentControlAttrs = {
        name: contentControl.getNewControlName(),
        type: contentControl.getType(),
      };
    }

    return contentControlAttrs;
  }

  public prepareContentControlStartElementVals(
    contentControl: NewControl, paraItem: ParaNewControlBorder,
    bSavePlaceHolder: boolean): IContentControlStartElementVals {

    const contentControlStartElementVals: IContentControlStartElementVals =
      JSON.parse(JSON.stringify(STD_START_DEFAULT));

    // console.log(contentControlStartElementVals)
    if (contentControl && paraItem) {
      contentControlStartElementVals.cascade = contentControl.getCascades();
      contentControlStartElementVals.placeholder = contentControl.getPlaceHolderContent();
      contentControlStartElementVals.helpTip = contentControl.getTipsContent();
      contentControlStartElementVals.isMustFill = contentControl.isMustInput() === true ? 1 : 0;
      contentControlStartElementVals.deleteProtect = contentControl.isDeleteProtect() === true ? 1 : 0;
      contentControlStartElementVals.editProtect = contentControl.isEditProtect() === true ? 1 : 0;
      contentControlStartElementVals.copyProtect = contentControl.isCopyProtect() === true ? 1 : 0;
      contentControlStartElementVals.showBorder = contentControl.isShowBorder() === true ? 1 : 0;
      contentControlStartElementVals.borderString = paraItem.content;
      contentControlStartElementVals.editReverse = contentControl.isReverseEdit() === true ? 1 : 0;
      contentControlStartElementVals.backgroundColorHidden = contentControl.isHiddenBackground() === true ? 1 : 0;
      contentControlStartElementVals.customProperty = contentControl.getCustomProperty();
      contentControlStartElementVals.tabJump = contentControl.isTabJump() === true ? 1 : 0;
      contentControlStartElementVals.newControlHidden = contentControl.isHidden() === true ? 1 : 0;
      contentControlStartElementVals.serialNumber = contentControl.getSerialNumber();
      const bTextBorder = contentControl.isTextBorder();
      contentControlStartElementVals.identifier = contentControl.getIdentifier();
      const eventInfo = contentControl.getEvent();
      if (eventInfo) {
        contentControlStartElementVals.eventInfo = eventInfo;
      }
      if (bTextBorder != null) {
        contentControlStartElementVals.bTextBorder = bTextBorder ? 1 : 0;
      }
      contentControlStartElementVals.fixedLength = contentControl.getFixedLength();
      contentControlStartElementVals.alignments = contentControl.getAlignments();

      if (contentControl.isNewTextBox() || contentControl.isNewSection()) {
        if ( contentControl.isNewTextBox() ) {
          contentControlStartElementVals.secretType = contentControl.getViewSecretType();
          // contentControlStartElementVals.fixedLength = contentControl.getFixedLength();
          contentControlStartElementVals.maxLength = contentControl.getMaxLength();
          contentControlStartElementVals.hideHasTitle = contentControl.getHideHasTitle() === true ? 1 : 0;
          contentControlStartElementVals.printSelected = (contentControl as any).isPrintSelected() === true ? 1 : 0;
        }

        contentControlStartElementVals.title = contentControl.getTitle();

      } else if (contentControl.isNumberBox()) {
        contentControlStartElementVals.secretType = contentControl.getViewSecretType();
        contentControlStartElementVals.minValue = typeof contentControl.getMinValue() === 'string' ?
         +contentControl.getMinValue() : contentControl.getMinValue();
        contentControlStartElementVals.maxValue = contentControl.getMaxValue();
        contentControlStartElementVals.precision = contentControl.getPrecision();
        contentControlStartElementVals.unit = contentControl.getUnit();
        contentControlStartElementVals.forceValidate = contentControl.getForceValidate() === true ? 1 : 0;
        contentControlStartElementVals.printSelected = (contentControl as any).isPrintSelected() === true ? 1 : 0;

      } else if (contentControl.isAmongComboxOrListStruct()) {
        contentControlStartElementVals.retrieve = contentControl.getRetrieve() === true ? 1 : 0;
        contentControlStartElementVals.selectPrefixContent = contentControl.getSelectPrefixContent();
        contentControlStartElementVals.prefixContent = contentControl.getPrefixContent();
        contentControlStartElementVals.separator = contentControl.getSeparator();
        contentControlStartElementVals.showValue = (true === contentControl.isShowValue() ? 1 : 0);
        // CodeValueItem[]
        contentControlStartElementVals.itemList = contentControl.getItemList();
        contentControlStartElementVals.bShowCodeAndValue = contentControl.isShowCodeAndValue() ? 1 : 0;
        contentControlStartElementVals.title = contentControl.getTitle();
        contentControlStartElementVals.hideHasTitle = contentControl.getHideHasTitle() === true ? 1 : 0;
        contentControlStartElementVals.codeLabel = contentControl.getLabelCode();
        contentControlStartElementVals.valueLabel = contentControl.getValueLabel();
        contentControlStartElementVals.printSelected = (contentControl as any).isPrintSelected() === true ? 1 : 0;
      } else if (contentControl.isNewDateBox()) {
        contentControlStartElementVals.dateBoxFormat = contentControl.getDateBoxFormat();
        contentControlStartElementVals.customFormat = contentControl.getCustomDateFormat();
        contentControlStartElementVals.startDate = contentControl.getStartDate();
        contentControlStartElementVals.endDate = contentControl.getEndDate();
        contentControlStartElementVals.dateTime = (bSavePlaceHolder === true) ? '' : contentControl.getDateTime();
        contentControlStartElementVals.printSelected = (contentControl as any).isPrintSelected() === true ? 1 : 0;
      } else if (contentControl.isMultiAndRadio()) { // 单选框
        if (contentControl instanceof NewControlRadio) {
          contentControlStartElementVals.showRight = contentControl.isShowRight() === true ? 1 : 0;
          contentControlStartElementVals.printSelected = contentControl.isPrintSelected() === true ? 1 : 0;
          contentControlStartElementVals.itemList = contentControl.getItemList(); // two places
          contentControlStartElementVals.showType = contentControl.getShowType();
          contentControlStartElementVals.spaceNum = contentControl.getSpaceNum();
          contentControlStartElementVals.supportMultLines = contentControl.isSupportMultLines() === true ? 1 : 0;
        }

      } else if (contentControl.isCheckBox()) { // 单个的复选框
        if (contentControl instanceof NewControlCheck) {
          contentControlStartElementVals.showRight = contentControl.isShowRight() === true ? 1 : 0;
          contentControlStartElementVals.checked = contentControl.isSelected() === true ? 1 : 0;
          contentControlStartElementVals.printSelected = contentControl.isPrintSelected() === true ? 1 : 0;
          contentControlStartElementVals.label = contentControl.getCheckLabel();
          contentControlStartElementVals.labelCode = contentControl.getLabelCode();
          contentControlStartElementVals.group = contentControl.getGroup();
        }

      } else if (contentControl.isSignatureBox()) {
        if (contentControl instanceof NewControlSignature) {
          contentControlStartElementVals.signatureCount = contentControl.getSignatureCount();
          contentControlStartElementVals.preText = contentControl.getPreText();
          contentControlStartElementVals.signatureSeparator = contentControl.getSignatureSeparator();
          contentControlStartElementVals.postText = contentControl.getPostText();
          contentControlStartElementVals.signaturePlaceholder = contentControl.getSignaturePlaceholder();
          contentControlStartElementVals.signatureRatio = contentControl.getSignatureRatio();
          // tslint:disable-next-line: max-line-length
          contentControlStartElementVals.rowHeightRestriction = contentControl.getRowHeightRestriction() === true ? 1 : 0;

          contentControlStartElementVals.signType = contentControl.getSignType();
          contentControlStartElementVals.alwaysShow = contentControl.getAlwaysShow();
          contentControlStartElementVals.showSignBorder = contentControl.getShowSignBorder() === true ? 1 : 0;
        }
      } else if (contentControl.isAddressBox()) {
        if (contentControl instanceof NewControlAddress) {
          contentControlStartElementVals.hierarchy = contentControl.getHierarchy();
          contentControlStartElementVals.province = (bSavePlaceHolder === true) ? null : contentControl.getProvince();
          contentControlStartElementVals.city = (bSavePlaceHolder === true) ? null : contentControl.getCity();
          contentControlStartElementVals.county = (bSavePlaceHolder === true) ? null : contentControl.getCounty();
        }
      }
    }

    return contentControlStartElementVals;
  }

  public prepareContentControlEndElementVals(
    contentControl: NewControl, paraItem: ParaNewControlBorder): IContentControlEndElementVals {

    const contentControlEndElementVals: IContentControlEndElementVals = JSON.parse(JSON.stringify(STD_END_DEFAULT));

    if (contentControl && paraItem) {
      contentControlEndElementVals.borderString = paraItem.content;
    }

    return contentControlEndElementVals;
  }

  public prepareRegionAttrs(region: RegionCore): IRegionAttributesProperties {
    const regionAttrs: IRegionAttributesProperties = { // fail safe
      name: '',
      type: RegionType.Menu,
      mode: RegionMode.Normal,
    };

    if (region != null) {
      regionAttrs.name = region.getName();
    }

    return regionAttrs;
  }

  public prepareTableAttrs(table: TableCore): ITableAttributesProperties {
    const tableAttrs: ITableAttributesProperties = { // fail safe
      name: '',
    };

    if (table != null) {
      tableAttrs.name = table.getName();
    }

    return tableAttrs;
  }

  public prepareNISTableAttrs(nisTable: NISTableCore): ITableAttributesProperties {
    const nisTableAttrs: INISTableAttributesProperties = { // fail safe
      name: '',
    };

    if (nisTable != null) {
      nisTableAttrs.name = nisTable.getName();
    }

    return nisTableAttrs;
  }

  public prepareRegionProps(region: RegionCore): IRegionProps {
    const regionProps: IRegionProps = JSON.parse(JSON.stringify(REGION_PROPS_DEFAULT)); // stringify map -> object
    if (region != null) {
      regionProps.bHidden = region.isHidden() === true ? 1 : 0;
      regionProps.bCanntEdit = region.isEditProtect() === true ? 1 : 0;
      regionProps.bCanntDelete = region.isDeleteProtect() === true ? 1 : 0;
      regionProps.bReverseEdit = region.isReverseEdit() === true ? 1 : 0;
      regionProps.title = region.getTitle();
      regionProps.bShowTitle = region.isShowTitle2() === true ? 1 : 0;
      regionProps.serialNumber = region.getSerialNumber();
      regionProps.maxLength = region.getMaxLength();
      regionProps.newControlInfo = region.getTipsContent();
      regionProps.identifier = region.getIdentifier();
      regionProps.placeholder = region.getPlaceholder();
      regionProps.regExp = region.getRegExp();

      // convert array to map so it's the same as customProps in content control
      const customProps = region.getCustomProps();
      const customPropsMap = this.convertCustomPropsArrToMap(customProps);
      regionProps.customProperty = customPropsMap;

    }
    return regionProps;
  }

  public prepareRevisionAtts(setting: IRevisionSetting, records?: any[]): IRevisionAttributesProperties {
    const revisionAttrs: IRevisionAttributesProperties = {
      id: '', des: '', line: '', color: '', date: '', author: '', level: '', savedCount: '',
    };

    if ( null != setting ) {
      const date = setting.date;
      const date2 = (date ? date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate()
                  + 'T' + date.getHours() + ':' + date.getMinutes() + ':' + date.getSeconds() : '');
      revisionAttrs.id = setting.userId;
      revisionAttrs.des = setting.description;
      revisionAttrs.line = setting.style.toString();
      revisionAttrs.color = isValidORGBColor(setting.color) ? setting.color : REVISION_COLOR.get(setting.color);
      revisionAttrs.date = date2;
      revisionAttrs.author = setting.userName;
      revisionAttrs.level = setting.level.toString();
      revisionAttrs.savedCount = (setting.savedCount ? setting.savedCount : 0).toString();
    }

    if (records?.length) {
      const tempRecords = [];
      records.forEach((record) => {
        if (record) {
          tempRecords.push({
            userId: record.userId,
            userName: record.userName,
            date: (record.date ? record.date.getTime() : ''),
            description: record.description,
            // level: record.level,
            // color: record.color,
            // style: record.style,
            savedCount: record.savedCount + 1,
            content: record.content,
          });
        }
      });

      revisionAttrs.record = JSON.stringify(tempRecords);
    }

    return revisionAttrs;
  }

  /**
   * compose mathEquation's mathText
   */
  public composeMathText(paraItem: ParaEquation): string {

    let mathString: string = '';

    if (paraItem) {
      const mathValue = paraItem.equationElem;
      // console.log(mathValue)
      const medEquationDom = new DOMParser().parseFromString(mathValue, 'text/xml').documentElement;
      // console.log(medEquationDom)
      const equationTexts = medEquationDom.querySelectorAll('.equation-text');

      if (equationTexts.length > 0) {
        switch (paraItem.equationType) {
          case EquationType.Menstruation:
            if (equationTexts.length === 4) {
              mathString = `初潮: ${equationTexts[0].innerHTML}; 经期: ${equationTexts[1].innerHTML}; ` +
                          `周期: ${equationTexts[2].innerHTML}; 绝经: ${equationTexts[3].innerHTML}`;
            }
            break;

          case EquationType.Fraction:
              if (equationTexts.length === 2) {
                mathString = `值1: ${equationTexts[0].innerHTML}; 值2: ${equationTexts[1].innerHTML}; `;
              }
              break;

            case EquationType.Ordinary:
                if (equationTexts.length === 4) {
                  mathString = `值1: ${equationTexts[0].innerHTML}; 值2: ${equationTexts[1].innerHTML}; ` +
                                `值3: ${equationTexts[2].innerHTML}; 值4: ${equationTexts[3].innerHTML}`;
                }
                break;
          default:
            break;
        }
      }

      // console.log(equationTexts)
    }

    return mathString;
  }

  public isParaEndPortion(portion: ParaPortion): boolean {
    let isParaEndPortion = false;
    if (portion.content.length === 1 && portion.content[0].type === ParaElementType.ParaEnd) {
      isParaEndPortion = true;
    }
    return isParaEndPortion;
  }

  public async saveToString(newBlob: any): Promise<ArrayBuffer> {
    return await(newBlob.arrayBuffer());
  }

  /**
   * traverse region
   * @param para just a name, can be region
   * @param logicDocument /
   * @param defaultParaProperty /
   * @param defaultTextProperty /
   * @param doc /
   * @param uniqueImageList /
   * @param depth /
   * @param parent only used in MAIN DOC
   */
  private traverseRegion(para: RegionCore, logicDocument: LogicDocument,
                         defaultParaProperty: IDefaultParaProperty, defaultTextProperty: IDefaultTextProperty,
                         doc: Document, uniqueImageList: Map<string, string>, depth: number,
                         parent: Document | Region = null, bSaveSelectedArea: boolean = false): void {
    // dont be confused, 'para' is region here. 'modify as less tested code as possible'
    const regionAttrs = this.prepareRegionAttrs(para);
    const curRegion = new Region(regionAttrs);

    const regionContent = para.getContent();
    const regionContentLen = regionContent.length;

    // retrieve and add region props
    const regionProps = this.prepareRegionProps(para);
    curRegion.addRegionProps(regionProps, para.getOperateContent());

    if (regionContentLen > 0) { // has para or table
      for (let i = 0; i < regionContentLen; i++) {

        const curParaCore = regionContent[i];
        if (curParaCore instanceof ParagraphCore) {

          const curPara = new Paragraph();

          this.traversePara(curPara, curParaCore, logicDocument, defaultParaProperty,
            defaultTextProperty, doc, uniqueImageList, bSaveSelectedArea);

          curRegion.addParagraph(curPara);

        } else if (curParaCore instanceof RegionCore) {

          depth++;
          if (depth > 2) {
            // tslint:disable-next-line: no-console
            console.warn('Region depth ' + depth + ' exceeds max depth. Content within will be discarded.');
            continue;
          }
          // recursive
          this.traverseRegion(curParaCore, logicDocument, defaultParaProperty,
            defaultTextProperty, doc, uniqueImageList, depth, curRegion);

          depth--;
        } else if (curParaCore instanceof TableCore) {
          // region only exist in main doc
          this.traverseTable(curParaCore, logicDocument, defaultParaProperty,
            defaultTextProperty, doc, uniqueImageList, -1, curRegion);
        }

      }
    }

    if (parent != null) {
      parent.addRegion(curRegion);
    }

  }

  /**
   * traverse region
   * @param para just a name, can be region
   * @param logicDocument /
   * @param defaultParaProperty /
   * @param defaultTextProperty /
   * @param doc /
   * @param uniqueImageList /
   * @param depth /
   * @param parent only used in MAIN DOC
   */
  private traverseRegion2(para: RegionCore, logicDocument: LogicDocument,
                          defaultParaProperty: IDefaultParaProperty, defaultTextProperty: IDefaultTextProperty,
                          doc: Document, uniqueImageList: Map<string, string>, depth: number,
                          parent: Document | Region = null, bSaveSelectedArea: boolean = false,
                          sModJson: ISaveModJson = null): void {
    // dont be confused, 'para' is region here. 'modify as less tested code as possible'
    const regionAttrs = this.prepareRegionAttrs(para);
    const curRegion = new Region(regionAttrs);

    const regionContent = para.getContent();
    const regionContentLen = regionContent.length;

    // retrieve and add region props
    const regionProps = this.prepareRegionProps(para);
    curRegion.addRegionProps(regionProps, para.getOperateContent());

    if (regionContentLen > 0) { // has para or table
      let secStack = [];
      let prePara;
      for (let i = 0; i < regionContentLen; i++) {

        const curParaCore = regionContent[i];
        if (curParaCore instanceof ParagraphCore) {

          let curPara; // = new Paragraph();
          const secCount = secStack.length;

          if (0 === secCount) {
            curPara = new Paragraph();
            prePara = curPara;
          } else {
            curPara = prePara;
          }

          this.traversePara3(curPara, curParaCore, logicDocument, defaultParaProperty,
            defaultTextProperty, doc, uniqueImageList, bSaveSelectedArea, sModJson, secStack);

          if (0 === secCount) {
            curRegion.addParagraph(curPara);
          }

        } else if (curParaCore instanceof RegionCore) {
          secStack = [];

          depth++;
          if (depth > 2) {
            // tslint:disable-next-line: no-console
            console.warn('Region depth ' + depth + ' exceeds max depth. Content within will be discarded.');
            continue;
          }
          // recursive
          this.traverseRegion2(curParaCore, logicDocument, defaultParaProperty,
            defaultTextProperty, doc, uniqueImageList, depth, curRegion, bSaveSelectedArea, sModJson);

          depth--;
        } else if (curParaCore instanceof TableCore) {
          secStack = [];
          // region only exist in main doc
          this.traverseTable2(curParaCore, logicDocument, defaultParaProperty,
            defaultTextProperty, doc, uniqueImageList, -1, curRegion, bSaveSelectedArea, sModJson);
        }

      }
    }

    if (parent != null) {
      parent.addRegion(curRegion);
    }

  }

  /**
   * traverse para
   * @param curPara cur para save file structure
   * @param para cur para object instance
   * @param logicDocument
   * @param defaultParaProperty
   * @param defaultTextProperty
   * @param doc
   * @param uniqueImageList
   */
  private traversePara(curPara: Paragraph, para: ParagraphCore, logicDocument: LogicDocument,
                       defaultParaProperty: IDefaultParaProperty, defaultTextProperty: IDefaultTextProperty,
                       doc: Document, uniqueImageList: Map<string, string>, bSaveSelectedArea: boolean = false): void {

    /** check if paragraph contains unique properties; if so, write them into w:pPr */
    this.processParaUniqueProperties(curPara, para.paraProperty, defaultParaProperty);

    /** remove empty portion if any */
    // this.removeEmptyPortion(para);

    /** portions with same properties need to combine into one, so defined out of the for loop */
    let text = '';
    let currentRun: TextRun = null;
    let isToPourText = true;
    let bPlaceHolder = false; // content control's placeholder indicator
    let bSignatureBox = false; // signature indicator
    let revision: Revision = null;
    for (let i = 0, paraContentLen = para.content.length; i < paraContentLen; i++) { // iterate portions[]

      const portion = para.content[i];

      // skip empty portion
      if (portion.content.length === 0) {
        // if next portion is struct right border, add previous run to para
        const tempNextPortion = para.content[i + 1];
        if (tempNextPortion != null) {
          if (currentRun != null) {
            const paraBorder = tempNextPortion.content[0];
            if ( paraBorder instanceof ParaNewControlBorder) {
              if (paraBorder.isNewControlEndBoder()) {
                if (currentRun) { // if run even not initialized yet
                  if (text) {
                    currentRun.addText(text);
                  }

                  // add run that has content
                  if (currentRun.getRoot().length > 0) {
                    const nReviewType = tempNextPortion.getReviewType();
                    if ( ReviewType.Common !== nReviewType
                        && !(tempNextPortion.isNewControlStart() || tempNextPortion.isNewControlEnd())) {
                      revision = this.addRevisionToPara(tempNextPortion, curPara);
                      revision.addRun(currentRun);
                    } else {
                      // Usually you need isToPourText to add run into paragraph
                      curPara.addRun(currentRun);
                    }
                    revision = null;
                  }

                  /** after adding run, clear text and nullify current run */
                  text = '';
                  currentRun = null;
                } else {
                  // tslint:disable-next-line: no-console
                  console.warn('attempt to save null portion1');
                }
              }
            }
          }
        }

        continue;
      }

      const reviewType = portion.getReviewType();
      if ( ReviewType.Common !== reviewType
          && !(portion.isNewControlStart() || portion.isNewControlEnd()) ) {
        // const bInsert = (ReviewType.Add === reviewType ? true : false);
        // const reviewInfo = portion.getReviewInfo();
        // const settings = reviewInfo ? reviewInfo.getRevisionSetting() : null;
        // const revisionArrts = this.prepareRevisionAtts(settings);
        // revision = new Revision(bInsert, revisionArrts);
        // curPara.addRevision(revision);
        revision = this.addRevisionToPara(portion, curPara);
      }

      /** --If portion.bPlaceHolder is true, neglect all potential portions(usu. 1) except ']' -R.W-- */
      /** Not true as of 07.07.2020 since struct title is added */
      const bNewControlBorder = portion.content[0].type === ParaElementType.ParaNewControlBorder;

      if (bPlaceHolder) {
        if ( false === bNewControlBorder ) {
          // here is placeholder portion.
          // It should only exist 1 ph portion and no other portions between [ ] portions
          // struct title: it's the same portion with [ portion(the prev portion), so okay

          // 11.03.2020: textprops of placeholder portion has nothing todo with [ portion
          // bPlaceHolder = false;

          // continue;

        } else {
          bPlaceHolder = false;
        }
      }

      /** Pay attention to content control with placeholder! */
      if (!portion.bPlaceHolder) { // the portion is the same as newControlStartBorderPortion if any
        // initialize run if null
        currentRun = this.initializeRun(currentRun, portion, defaultTextProperty);

        isToPourText = bNewControlBorder ? true : this.prepareIsToPourText(para, portion, i);
      } else {
        // only ] will be true? [ is always false?
        isToPourText = false;

        // if struct.title exists, need to initialize run!
        const paraBorder = portion.content[0];
        // tslint:disable-next-line: max-line-length
        if (paraBorder.type === ParaElementType.ParaNewControlBorder && paraBorder instanceof ParaNewControlBorder) {

          if (paraBorder.isNewControlEndBoder()) {
            // if no error, first paraItem should be ]
            // tslint:disable-next-line: max-line-length
            const curControl = this.retrieveCurrentContentControl(paraBorder as ParaNewControlBorder, logicDocument);
            if (curControl != null) {
              if (curControl.getTitle() != null) { // check if struct has title
                // TODO: not sure why needs to initialize here? seems to be related with region/title?
                // no. feel like only relate with [ title ] old pattern
                // currentRun = this.initializeRun(currentRun, portion, defaultTextProperty);
              }
            } else {
              // tslint:disable-next-line: no-console
              console.warn('Critical: cannot get current struct');
            }
          }
        } else {
          // TODO: define error level and behavior
          // tslint:disable-next-line: no-console
          console.warn('Critical: first item in bPlaceHolder portion is not Border');
        }

      }

      // for (const paraItem of portion.content) { // paraText | paraTab | paradrawing | paraNewControlBorder []
      for (let j = 0, portionContentLen = portion.content.length; j < portionContentLen; j++) {
        /** paraText | paraTab | paradrawing | paraNewControlBorder [] */
        const paraItem = portion.content[j];
        const paraItemType = paraItem.type;

        if (paraItemType === ParaElementType.ParaTab) {

          /** Observation: When reaching tab, always pour out previously accumulated text into a w:t */
          if (text) {
            currentRun.addText(text);
          }

          currentRun.tab();
          text = '';

        } else if (paraItemType === ParaElementType.ParaDrawing && paraItem instanceof ParaDrawing) {
          /** Observation: When reaching image, always pour out previously accumulated text into a w:t */
          if (text) {
            currentRun.addText(text);
          }

          // console.log(paraItem)
          if (paraItem.isSvgDrawing() === false) {
            // add image
            currentRun.addImage(this.prepareImageAttrs(paraItem, doc, uniqueImageList));
          } else {
            // editble image
            currentRun.addEditableImage(this.prepareEditableImageAttrs(paraItem, uniqueImageList));
          }

          text = '';

        } else if (paraItemType === ParaElementType.ParaMedEquation && paraItem instanceof ParaEquation) {
          /** Observation: When reaching medEquation, always pour out previously accumulated text into a w:t */
          if (text) {
            currentRun.addText(text);
          }

          // compose mathText
          const mathText = this.composeMathText(paraItem);
          // add medEquation
          currentRun.addMedEquation(this.prepareMedEquationAttrs(paraItem, doc, uniqueImageList),
           paraItem.equationElem, mathText);

          text = '';

        } else if (paraItemType === ParaElementType.ParaNewControlBorder) {

          /**
           * even if two portions' props are the same, if cc exists, should not combine the two,
           * so 1. if exists previous portion and not poured out yet('['), OR (first line only account for 1.)
           * 2. encounter ] and *assume content exists between [ ].
           * pour out text and add run to para
           */
          if (text) {
            if (currentRun == null) {
              // tslint:disable-next-line: no-console
              console.warn('currentRun is not initialized when it should be.');
              currentRun = this.initializeRun(currentRun, portion, defaultTextProperty);
            }
            currentRun.addText(text);
            if ( revision ) {
              revision.addRun(currentRun);
            } else {
              curPara.addRun(currentRun);
            }

            /** after adding run, clear text and nullify current run */
            text = '';
            currentRun = null;
            revision = null;

            /** Since already added run into paragraph, no need to pour */
            isToPourText = false;
          }
          // right struct border portion now cannot have other items!
          // else if (!(paraItem as ParaNewControlBorder).bStart && portion.content[j - 1]) {

          //   /** check the last portion before sdtEnd. Make sure the w:r prints out before sdtend */
          //   const lastPortion = portion.content[j - 1];
          //   // console.log(lastPortion)
          //   if (lastPortion instanceof ParaDrawing || lastPortion instanceof ParaEquation) {
          //     curPara.addRun(currentRun);
          //     currentRun = null;
          //     isToPourText = false;
          //   }
          // }

          if (paraItem instanceof ParaNewControlBorder) {

            let currentContentControl: NewControl = null;
            try {
              currentContentControl = this.retrieveCurrentContentControl(paraItem, logicDocument);
              if (currentContentControl == null) {
                throw new Error('Cannot find current struct.');
              }
            } catch (error) {
              // alert(ErrorMessages.FetchStructFailure);
              // tslint:disable-next-line: no-console
              console.warn(ErrorMessages.FetchStructFailure);
              window.dispatchEvent(new ErrorEvent('error', {message: ErrorMessages.FetchStructFailure, error}));
              continue;
            }

            const contentControlStartElementVals = this.prepareContentControlStartElementVals(
              currentContentControl, paraItem, false
            );
            const contentControlEndElementVals = this.prepareContentControlEndElementVals(
              currentContentControl, paraItem,
            );
            const contentControlAttrs = this.prepareContentControlAttrs(currentContentControl);
            const tempRun = this.initializeRun(currentRun, portion, defaultTextProperty);

            // console.log(contentControlStartElementVals)

            /** add content control */
            if (paraItem.bStart) {
              curPara.addContentControlStart(
                contentControlAttrs, tempRun.getRunProperty(), contentControlStartElementVals,
                portion.bPlaceHolder, bSignatureBox
              );

              /** if it's signature box & saveselectedareatostream */
              if (bSaveSelectedArea === true && currentContentControl.getType() === NewControlType.SignatureBox) {
                bSignatureBox = true;
              }

              if (portion.bPlaceHolder) {
                bPlaceHolder = true;
              }

            } else { // 右括号

              /** first: add run to para(already did), add ] next */
              curPara.addContentControlEnd(
                contentControlAttrs, tempRun.getRunProperty(), contentControlEndElementVals,
              );

              /** if it's signature box, remove child structs' content */
              if (bSaveSelectedArea === true && currentContentControl.getType() === NewControlType.SignatureBox) {
                bSignatureBox = false;

                this.setSignatureSubTextStrutsAsPlaceholder(currentContentControl, curPara);

              }

            }

          } else {
          }

        } else if (paraItemType === ParaElementType.ParaPageNum) {
          /** Observation: When reaching pagenum, always pour out previously accumulated text into a w:t */
          if (text) {
            currentRun.addText(text);
          }

          // add pagenum
          if (paraItem instanceof ParaPageNum) {
            const totalPages = logicDocument.getPages().length;
            const curPage = paraItem.getCurPage();
            // currentRun.addPageNum(this.preparePageNumProps(paraItem, doc), totalPages, );
            currentRun.addPageNum(this.preparePageNumProps(paraItem, doc), totalPages, curPage);
          }

          text = '';
        } else if (ParaElementType.ParaNewLine === paraItemType) {
          if (text) {
            currentRun.addText(text);
          }

          currentRun.addSoftNewPara();
          text = '';
        } else if (paraItemType !== ParaElementType.ParaEnd) { /** other paraItems which are content */
          // ignore paraEnd since has nothing todo with content/xml
          text += paraItem.content;
        }
      } // paraItems loop

      // console.log(text);

      // if next portion is right struct border OR PLACEHOLDER PORTION, pour out
      // empty portion cannot be here
      const nextPortion = para.content[i + 1];
      if (nextPortion != null) {
        const paraBorder = nextPortion.content[0];
        if ( paraBorder instanceof ParaNewControlBorder) {
          if (paraBorder.isNewControlEndBoder()) {
            isToPourText = true;
          }
        } else if (bPlaceHolder === true) {
          // next portion is placeholder, must pour out if has text
          if (text && !currentRun) {
            currentRun = this.initializeRun(currentRun, portion, defaultTextProperty);
          }
          isToPourText = true;
        }
      }

      if (isToPourText) {

        if (currentRun) { // if run even not initialized yet
          if (text) {
            currentRun.addText(text);
          }

          // add run that has content
          if (currentRun.getRoot().length > 0) {
            // Usually you need isToPourText to add run into paragraph
            if ( revision ) {
              revision.addRun(currentRun);
            } else {
              curPara.addRun(currentRun);
            }
          }

          /** after adding run, clear text and nullify current run */
          text = '';
          currentRun = null;
          revision = null;
        } else {
          // TODO
          // tslint:disable-next-line: no-console
          // console.warn('attempt to save null portion 2');
        }

      }

    } // portion loop
  }

  
  /**
   * traverse para
   * @param curPara cur para save file structure
   * @param para cur para object instance
   * @param logicDocument
   * @param defaultParaProperty
   * @param defaultTextProperty
   * @param doc
   * @param uniqueImageList
   */
  private traversePara3(curPara: Paragraph, para: ParagraphCore, logicDocument: LogicDocument,
                        defaultParaProperty: IDefaultParaProperty, defaultTextProperty: IDefaultTextProperty,
                        doc: Document, uniqueImageList: Map<string, string>, bSaveSelectedArea: boolean = false,
                        sModJson: ISaveModJson = null, secStack: Section[]): void {
    const curSectionCount = secStack.length;
    /** check if paragraph contains unique properties; if so, write them into w:pPr */
    if (0 === curSectionCount) {
      this.processParaUniqueProperties(curPara, para.paraProperty, defaultParaProperty);
      curPara.paraHidden(para.getHidden());

      const numId: number = para.paraProperty.numPr?.numId;
      if (numId) {
        curPara.addNumbered(numId, logicDocument.getNumManager()
          .getNum(numId)
          ?.getType() || NumberingType.Bullet
        );
      }
    }

    /** remove empty portion if any */
    // this.removeEmptyPortion(para);
    /** portions with same properties need to combine into one, so defined out of the for loop */
    let text = '';
    let currentRun: TextRun = null;
    let isToPourText = true;
    let bPlaceHolder = false; // content control's placeholder indicator
    let bSignatureBox = false; // signature indicator
    let revision: Revision = null;
    const needRevision = (sModJson && sModJson.needRevision != null) ? sModJson.needRevision : NeedsignalContent.Keep;

    // let bInSdt = false; // if portions are within <sdt>
    const sdtStack: Sdt[] = []; // usually just one item, except for signature box
    // const secStack: Section[] = [];
    let bSavePlaceHolder = false;
    let bSectionStart = false;
    const commentManager = logicDocument.getCommentManager();
    for (let i = 0, paraContentLen = para.content.length; i < paraContentLen; i++) { // iterate portions[]

      const portion = para.content[i];
      bSectionStart = false;

      // skip empty portion
      if (portion.content.length === 0) {
        // if next portion is struct right border, add previous run to para
        const tempNextPortion = para.content[i + 1];
        if (tempNextPortion != null) {
          if (currentRun != null) {
            const paraBorder = tempNextPortion.content[0];
            if (paraBorder instanceof ParaNewControlBorder) {
              if (paraBorder.isNewControlEndBoder()) {
                if (currentRun) { // if run even not initialized yet
                  if (text) {
                    currentRun.addText(text);
                  }

                  // add run that has content
                  if (currentRun.getRoot().length > 0) {
                    const nReviewType = tempNextPortion.getReviewType();
                    const sdtStackLen = sdtStack.length;
                    if (ReviewType.Common !== nReviewType && sdtStackLen <= 0
                      && !(tempNextPortion.isNewControlStart() || tempNextPortion.isNewControlEnd())) {
                      if (needRevision === NeedsignalContent.Keep) {
                        revision = this.addRevisionToPara(tempNextPortion, curPara);
                        revision.addRun(currentRun);
                      }
                    } else {
                      if (sdtStackLen <= 0) {
                        if (needRevision === NeedsignalContent.Clear && nReviewType === ReviewType.Remove) {
                          // NeedRevision:0 and del sign, content shouldn't add
                        } else {
                          // Usually you need isToPourText to add run into paragraph
                          // curPara.addRun(currentRun);
                          if (secStack.length <= 0) {
                            curPara.addRun(currentRun);
                          } else {
                            const curSeContent = secStack[secStack.length - 1].getSectionContent();
                            curSeContent.addRun(currentRun);
                          }
                        }
                      } else {
                        const curSdtContent = sdtStack[sdtStackLen - 1].getSdtContent();
                        if (ReviewType.Common !== nReviewType
                          && !(tempNextPortion.isNewControlStart() || tempNextPortion.isNewControlEnd())) {
                          if (needRevision === NeedsignalContent.Keep) {
                            revision = this.addRevisionToPara(tempNextPortion, curSdtContent);
                            revision.addRun(currentRun);
                          }
                        } else {
                          if (revision != null) {
                            revision = this.addRevisionToPara(tempNextPortion, curSdtContent);
                            revision.addRun(currentRun);
                          } else {
                            if (needRevision === NeedsignalContent.Clear && nReviewType === ReviewType.Remove) {
                              // NeedRevision:0 and del sign, content shouldn't add
                            } else {
                              curSdtContent.addRun(currentRun);
                            }
                          }
                        }
                      }
                    }
                    revision = null;
                  }

                  /** after adding run, clear text and nullify current run */
                  text = '';
                  currentRun = null;
                } else {
                  // tslint:disable-next-line: no-console
                  console.warn('attempt to save null portion1');
                }
              }
            }
          }
        }

        continue;
      }

      if (portion.isComment()) {
        const sdtStackLen = sdtStack.length;
        const bStart = portion.isStartComment();
        const xmlComment = new XmlComment({
            start: bStart ? '1' : '0',
            id: portion.getCommentId() + '',
        });
        const comment = commentManager.getCommentById(portion.getCommentId());
        if (comment) {
            if (bStart) {
                const xmlData = this.buildCommentData(comment.getData());
                xmlComment.addChildElement(xmlData);
            }
            const commentRun = this.initializeRun(null, portion, defaultTextProperty);
            xmlComment.addChildElement(commentRun);
            if (sdtStackLen <= 0) {
                if (secStack.length <= 0) {
                  curPara.addComment(xmlComment);
                } else {
                  const curSeContent = secStack[secStack.length - 1].getSectionContent();
                  curSeContent.addComment(xmlComment);
                }
            } else {
                const curSdtContent = sdtStack[sdtStackLen - 1].getSdtContent();
                curSdtContent.addComment(xmlComment);
            }
        }
        continue;
      }

      // TODO: this may result to empty <w:ins />
      const reviewType = portion.getReviewType();
      if (ReviewType.Common !== reviewType
          && !(portion.isNewControlStart() || portion.isNewControlEnd())) {
        const sdtStackLen = sdtStack.length;
        if (sdtStackLen <= 0) {
          if (needRevision === NeedsignalContent.Keep) {
            if (secStack.length <= 0) {
              revision = this.addRevisionToPara(portion, curPara);
            } else {
              const curSeContent = secStack[secStack.length - 1].getSectionContent();
              revision = this.addRevisionToPara(portion, curSeContent);
            }
          }
        } else {
          if (needRevision === NeedsignalContent.Keep) {
            const curSdtContent = sdtStack[sdtStackLen - 1].getSdtContent();
            revision = this.addRevisionToPara(portion, curSdtContent);
          }
        }
      }

      /** --If portion.bPlaceHolder is true, neglect all potential portions(usu. 1) except ']' -R.W-- */
      /** Not true as of 07.07.2020 since struct title is added */
      const bNewControlBorder = portion.content[0].type === ParaElementType.ParaNewControlBorder;
      const bNewControlStart = portion.isNewControlStart();
      let newControlName: string;
      if (bNewControlBorder) {
        if (bNewControlStart) {
          newControlName = portion.getStartNewControlName();
          if (doc.structs.start[newControlName]) {
            console.warn('the start border is repeat: ' + newControlName);
            continue;
          }
        } else {
          newControlName = portion.getEndNewControlName();
          if (doc.structs.end[newControlName]) {
            console.warn('the end border is repeat: ' + newControlName);
            continue;
          }
        }
      }
      if (bPlaceHolder) {
        if (false === bNewControlBorder) {
          // here is placeholder portion.
          // It should only exist 1 ph portion and no other portions between [ ] portions
          // struct title: it's the same portion with [ portion(the prev portion), so okay

          // 11.03.2020: textprops of placeholder portion has nothing todo with [ portion
          // bPlaceHolder = false;

          // continue;

        } else {
          bPlaceHolder = false;
        }
      }

      if ( bNewControlBorder && bNewControlStart && !portion.bPlaceHolder && sModJson && sModJson.cleanElementArray ) {
        newControlName = newControlName || portion.getStartNewControlName();

        switch ( sModJson.cleanElementMode ) {
          case ISaveCleanElementMode.Name: {
            if (-1 !== sModJson.cleanElementArray.findIndex((element) => element === newControlName) ) {
              const control = logicDocument.getNewControlByName(newControlName);
              bSavePlaceHolder = (control && !(control.isNewSection() || control.isSignatureBox()) &&
                                  control.getNewControlName() === newControlName);
            }
            break;
          }

          case ISaveCleanElementMode.SerialNumber: {
            const control = logicDocument.getNewControlByName(newControlName);
            const serialNumber = control.getSerialNumber();
            if (control && !(control.isNewSection() || control.isSignatureBox()) &&
                -1 !== sModJson.cleanElementArray.findIndex((element) => element === serialNumber) ) {
              bSavePlaceHolder = true;
            }
            break;
          }
        }
      }

      /** Pay attention to content control with placeholder! */
      if (!portion.bPlaceHolder || !bSavePlaceHolder) {// the portion is the same as newControlStartBorderPortion if any
        // initialize run if null
        currentRun = this.initializeRun(currentRun, portion, defaultTextProperty);

        isToPourText = bNewControlBorder ? true : this.prepareIsToPourText(para, portion, i);
      } else {
        // only ] will be true? [ is always false?
        isToPourText = false;

        // if struct.title exists, need to initialize run!
        const paraBorder = portion.content[0];
        // tslint:disable-next-line: max-line-length
        if (paraBorder.type === ParaElementType.ParaNewControlBorder && paraBorder instanceof ParaNewControlBorder) {

          if (paraBorder.isNewControlEndBoder()) {
            // if no error, first paraItem should be ]
            // tslint:disable-next-line: max-line-length
            const curControl = this.retrieveCurrentContentControl(paraBorder as ParaNewControlBorder, logicDocument);
            if (curControl != null) {
              if (curControl.getTitle() != null) { // check if struct has title
                // TODO: not sure why needs to initialize here? seems to be related with region/title?
                // no. feel like only relate with [ title ] old pattern
                // currentRun = this.initializeRun(currentRun, portion, defaultTextProperty);
              }
            } else {
              // tslint:disable-next-line: no-console
              console.warn('Critical: cannot get current struct');
            }
          }
        } else {
          // TODO: define error level and behavior
          // tslint:disable-next-line: no-console
          console.warn('Critical: first item in bPlaceHolder portion is not Border');
        }

      }

      if ( bSavePlaceHolder && !(bNewControlStart || portion.isNewControlEnd()) && !portion.bPlaceHolder ) {
        continue;
      }

      // for (const paraItem of portion.content) { // paraText | paraTab | paradrawing | paraNewControlBorder []
      for (let j = 0, portionContentLen = portion.content.length; j < portionContentLen; j++) {
        /** paraText | paraTab | paradrawing | paraNewControlBorder [] */
        const paraItem = portion.content[j];
        const paraItemType = paraItem.type;
        if (paraItemType === ParaElementType.ParaButton) {
          if (text) {
            currentRun.addText(text);
          }
          currentRun.addParaButton(paraItem);
          text = '';
        } else if (paraItemType === ParaElementType.ParaTab) {
          /** Observation: When reaching tab, always pour out previously accumulated text into a w:t */
          if (text) {
            currentRun.addText(text);
          }

          currentRun.tab();
          text = '';

        } else if (paraItemType === ParaElementType.ParaDrawing && paraItem instanceof ParaDrawing) {
          /** Observation: When reaching image, always pour out previously accumulated text into a w:t */
          if (text) {
            currentRun.addText(text);
          }

          // console.log(paraItem)
          if (paraItem.isSvgDrawing() === false) {
            // add image (and audio or video)
            currentRun.addImage(this.prepareImageAttrs(paraItem, doc, uniqueImageList));
          } else {
            // editble image
            currentRun.addEditableImage(this.prepareEditableImageAttrs(paraItem, uniqueImageList));
          }

          text = '';

        } else if (paraItemType === ParaElementType.ParaMedEquation && paraItem instanceof ParaEquation) {
          /** Observation: When reaching medEquation, always pour out previously accumulated text into a w:t */
          if (text) {
            currentRun.addText(text);
          }

          // compose mathText
          const mathText = this.composeMathText(paraItem);
          // add medEquation
          currentRun.addMedEquation(this.prepareMedEquationAttrs(paraItem, doc, uniqueImageList),
            paraItem.equationElem, mathText);

          text = '';

        } else if (paraItem instanceof ParaBarcode) {
          /** Observation: When reaching image, always pour out previously accumulated text into a w:t */
          if (text) {
            currentRun.addText(text);
          }

          if (ParaElementType.ParaBarcode === paraItemType) {
            currentRun.addBarcode(this.prepareBarcodeAttrs(paraItem, uniqueImageList));
          } else {
            currentRun.addQRCode(this.prepareBarcodeAttrs(paraItem, uniqueImageList, true));
          }
          // console.log(paraItem)

          text = '';

        } else if (paraItemType === ParaElementType.ParaNewControlBorder) {
          /**
           * even if two portions' props are the same, if cc exists, should not combine the two,
           * so 1. if exists previous portion and not poured out yet('['), OR (first line only account for 1.)
           * 2. encounter ] and *assume content exists between [ ].
           * pour out text and add run to para
           */
          if (text) {
            if (currentRun == null) {
              // tslint:disable-next-line: no-console
              // console.warn('currentRun is not initialized when it should be.');
              currentRun = this.initializeRun(currentRun, portion, defaultTextProperty);
            }
            currentRun.addText(text);
            if (revision && needRevision === NeedsignalContent.Keep) {
              revision.addRun(currentRun);
            } else {
              if (needRevision === NeedsignalContent.Clear && reviewType === ReviewType.Remove) {
                // NeedRevision:0 and del sign, content shouldn't add
              } else {
                curPara.addRun(currentRun);
              }
            }

            /** after adding run, clear text and nullify current run */
            text = '';
            currentRun = null;
            revision = null;

            /** Since already added run into paragraph, no need to pour */
            isToPourText = false;
          }
          // right struct border portion now cannot have other items!
          // else if (!(paraItem as ParaNewControlBorder).bStart && portion.content[j - 1]) {

          //   /** check the last portion before sdtEnd. Make sure the w:r prints out before sdtend */
          //   const lastPortion = portion.content[j - 1];
          //   // console.log(lastPortion)
          //   if (lastPortion instanceof ParaDrawing || lastPortion instanceof ParaEquation) {
          //     curPara.addRun(currentRun);
          //     currentRun = null;
          //     isToPourText = false;
          //   }
          // }

          if (paraItem instanceof ParaNewControlBorder) {

            let currentContentControl: NewControl = null;
            try {
              currentContentControl = this.retrieveCurrentContentControl(paraItem, logicDocument);
              if (currentContentControl == null) {
                // TODO: maybe can just desert ] part and still make the document readable
                throw new Error('Cannot find current struct.');
              }
            } catch (error) {
              // alert(ErrorMessages.FetchStructFailure);
              // tslint:disable-next-line: no-console
              console.warn(ErrorMessages.FetchStructFailure);
              window.dispatchEvent(new ErrorEvent('error', { message: ErrorMessages.FetchStructFailure, error }));
              continue;
            }

            const contentControlStartElementVals = logicDocument.getCustomForNewControl(currentContentControl);
            // this.prepareContentControlStartElementVals(
            //   currentContentControl, paraItem, bSavePlaceHolder
            // );

            // let contentControlEndElementVals = null;
            // if (currentContentControl.getType() === NewControlType.Section) {
            //   contentControlEndElementVals = this.prepareContentControlEndElementVals(
            //     currentContentControl, paraItem,
            //   );
            // }

            const contentControlAttrs = this.prepareContentControlAttrs(currentContentControl);
            const tempRun = this.initializeRun(currentRun, portion, defaultTextProperty);
            const newControlType = currentContentControl.getType();
            doc.structs.type[newControlName] = newControlType;

            // console.log(contentControlStartElementVals)
            /** add content control */
            if (paraItem.bStart) {
              contentControlStartElementVals.showPlaceholder = portion.bPlaceHolder;
              if (newControlType === NewControlType.Section) {
                // curPara.addSectionStart(
                //   contentControlAttrs, tempRun.getRunProperty(), contentControlStartElementVals,
                //   paraItem.content
                // );
                let container: any = curPara;
                if (0 === secStack.length) {
                  secStack.push(container.addSection(
                    contentControlAttrs, tempRun.getRunProperty(), contentControlStartElementVals
                  ));
                } else {
                  container = secStack[secStack.length - 1];
                  secStack.push(container.addSdt2(
                    contentControlAttrs, tempRun.getRunProperty(), contentControlStartElementVals
                  ));
                }
                bSectionStart = true;
              } else {
                let container: any = secStack[secStack.length - 1] || curPara;
                if (sdtStack.length > 1) {
                  // tslint:disable-next-line: no-console
                  console.warn('sdtstack has more than 1 item');
                } else if (sdtStack.length > 0) {
                  // only 1, means signature
                  container = sdtStack[0].getSdtContent();
                }
                sdtStack.push(container.addSdt2(
                  contentControlAttrs, tempRun.getRunProperty(), contentControlStartElementVals,
                  portion.bPlaceHolder || bSavePlaceHolder, bSignatureBox
                ));
              }

              let bNeedSignContent = NeedsignalContent.Keep;
              if (sModJson != null) {
                bNeedSignContent = sModJson.needsignalContent;
              }
              /** if it's signature box & clear sign struct content */
              if (bNeedSignContent === NeedsignalContent.Clear &&
                newControlType === NewControlType.SignatureBox) {
                bSignatureBox = true;
              }

              if (portion.bPlaceHolder) {
                bPlaceHolder = true;
              }
              doc.structs.start[newControlName] = true;
            } else { // 右括号
              doc.structs.end[newControlName] = true;
              let curSdt;
              if (newControlType === NewControlType.Section) {
                /** first: add run to para(already did), add ] next */
                // curPara.addSectionEnd(
                //   contentControlAttrs, tempRun.getRunProperty(), paraItem.content,
                // );
                if ( bSavePlaceHolder ) {
                  const control = logicDocument.getNewControlByName(portion.getEndNewControlName());
                  if ( control ) {
                    const newPortion = control.getNewControlContent()
                                          .initPlaceHolder(false, control.isMustInput(), portion.textProperty);
                    const run = this.initializeRun(null, newPortion, defaultTextProperty);
                    run.addText(control.getPlaceHolderContent());
                    const curSdtContent = secStack[secStack.length - 1].getSectionContent();
                    curSdtContent.addRun(run);
                  }
                  bSavePlaceHolder = false;
                }
                secStack.pop();
              } else {
                // other struct: fill in its w:sdtContent
                if ( bSavePlaceHolder ) {
                  const control = logicDocument.getNewControlByName(portion.getEndNewControlName());
                  if ( control ) {
                    const newPortion = control.getNewControlContent()
                                          .initPlaceHolder(false, control.isMustInput(), portion.textProperty);
                    const run = this.initializeRun(null, newPortion, defaultTextProperty);
                    run.addText(control.getPlaceHolderContent());
                    const curSdtContent = sdtStack[sdtStack.length - 1].getSdtContent();
                    curSdtContent.addRun(run);
                  }
                  bSavePlaceHolder = false;
                }

                curSdt = sdtStack.pop();
              }

              let bNeedSignContent = NeedsignalContent.Keep;
              if (sModJson != null) {
                bNeedSignContent = sModJson.needsignalContent;
              }
              /** if it's signature box, remove child structs' content */
              if (bNeedSignContent === NeedsignalContent.Clear &&
                newControlType === NewControlType.SignatureBox && curSdt) {
                bSignatureBox = false;
                const sdtContent = curSdt.getRoot().find(item => item.rootKey === 'w:sdtcontent');
                // this.setSignatureSubTextStrutsAsPlaceholder2(currentContentControl, curPara);
                sdtContent && this.clearSignatureSubTextStrutsContent(currentContentControl, curPara, defaultTextProperty, sdtContent);

              }

            }

          } else {
           
          }

        } else if (paraItemType === ParaElementType.ParaPageNum) {
          /** Observation: When reaching pagenum, always pour out previously accumulated text into a w:t */
          if (text) {
            currentRun.addText(text);
          }

          // add pagenum
          if (paraItem instanceof ParaPageNum) {
            const totalPages = logicDocument.getPages().length;
            const curPage = paraItem.getCurPage();
            // currentRun.addPageNum(this.preparePageNumProps(paraItem, doc), totalPages, );
            currentRun.addPageNum(this.preparePageNumProps(paraItem, doc), totalPages, curPage);
          }

          text = '';
        } else if (ParaElementType.ParaNewLine === paraItemType) {
          if (text) {
            currentRun.addText(text);
          }

          currentRun.addSoftNewPara();
          text = '';
        } else if (paraItemType !== ParaElementType.ParaEnd) { /** other paraItems which are content */
          // ignore paraEnd since has nothing todo with content/xml
          text += paraItem.content;
        }
      } // paraItems loop

      // console.log(text);

      // if next portion is right struct border OR PLACEHOLDER PORTION, pour out
      // empty portion cannot be here
      // caveat: if it's empty portion + ], handled at the beginning
      const nextPortion = para.content[i + 1];
      let bNextSectionEndPortion = false;
      let bNextEndPortion = false;
      if (nextPortion != null) {
        // console.log(nextPortion)
        const paraBorder = nextPortion.content[0];
        if (paraBorder instanceof ParaNewControlBorder) {
          if (paraBorder.isNewControlEndBoder()) {
            isToPourText = true;
            bNextSectionEndPortion = (NewControlType.Section === doc.structs.type[nextPortion.getEndNewControlName()]);
            bNextEndPortion = !bNextEndPortion;
          }
        } else if (bPlaceHolder === true) {
          // next portion is placeholder, must pour out if has text
          if (text && !currentRun) {
            currentRun = this.initializeRun(currentRun, portion, defaultTextProperty);
          }
          isToPourText = true;
        }
      }

      if (isToPourText) {

        if (currentRun) { // if run even not initialized yet
          if (text) {
            currentRun.addText(text);
          }

          // add run that has content
          if (currentRun.getRoot().length > 0) {
            // Usually you need isToPourText to add run into paragraph
            if (revision && needRevision === NeedsignalContent.Keep) {
              revision.addRun(currentRun);
            } else {
              if (needRevision === NeedsignalContent.Clear && reviewType === ReviewType.Remove) {
                // NeedRevision:0 and del sign, content shouldn't add
              } else if (!(bSectionStart && bPlaceHolder)) {
                if (!bNextSectionEndPortion) {
                  if (sdtStack.length <= 0) {
                    if (secStack.length <= 0) {
                      curPara.addRun(currentRun);
                    } else {
                      const curSeContent = secStack[secStack.length - 1].getSectionContent();
                      curSeContent.addRun(currentRun);
                    }
                  } else {
                    const curSdtContent = sdtStack[sdtStack.length - 1].getSdtContent();
                    curSdtContent.addRun(currentRun);
                  }
                } else {
                  if (secStack.length <= 0) {
                    curPara.addRun(currentRun);
                  } else {
                    const curSeContent = secStack[secStack.length - 1].getSectionContent();
                    curSeContent.addRun(currentRun);
                  }
                }
              }
            }
          }

          /** after adding run, clear text and nullify current run */
          text = '';
          currentRun = null;
          revision = null;
        } else {
          // TODO
          // tslint:disable-next-line: no-console
          // console.warn('attempt to save null portion 2');
        }

      }

    } // portion loop

    // new section: add softline
    if (secStack.length) {
      const run = this.initializeRun(null, this.getLastPortion(para), defaultTextProperty);
      run.addSoftNewPara();
      secStack[secStack.length - 1].getSectionContent().addRun(run);
    }
  }

  /**
   * traverse table
   * @param tableInstance /
   * @param logicDocument /
   * @param defaultParaProperty /
   * @param defaultTextProperty /
   * @param doc /
   * @param uniqueImageList /
   * @param headerFooterType /
   * @param parent only used in MAIN DOC
   */
  private traverseTable(tableInstance: TableCore, logicDocument: LogicDocument,
                        defaultParaProperty: IDefaultParaProperty, defaultTextProperty: IDefaultTextProperty,
                        doc: Document, uniqueImageList: Map<string, string>, headerFooterType: number = -1,
                        parent: Document | Region = null, bSaveSelectedArea: boolean = false): void {
    const tableContent = tableInstance.content;
    let cols = 0;

    // first loop: get cols of table
    for (const tableRow of tableContent) {
      const tableCellCount = tableRow.content.length;
      cols = cols > tableCellCount ? cols : tableCellCount;
    }

    const rows = tableInstance.getRowsCount();
    const name = this.prepareTableAttrs(tableInstance);
    // const colSizes = tableInstance.getAllColumnsWidth(); // TODO: is this updated with true column count?
    // for (let i = 0, colSizesLen = colSizes.length; i < colSizesLen; i++) {
    //   colSizes[i] = getPxForMM(colSizes[i] * 10);
    // }
    const colSizes = tableInstance.getTableGrid();
    // console.log(colSizes)
    const curTableXML = new Table(rows, colSizes.length, name, colSizes);
    // const curTableXML = new Table(rows, cols, name, colSizes);

    // set table props
    this.prepareTableTableProps(curTableXML, tableInstance);

    // second loop: populate data
    for (let i = 0, tableContentLen = tableContent.length; i < tableContentLen; i++) {
      const tableRow = tableContent[i];
      const tableRowContent = tableRow.content;
      const tableRowContentLen = tableRowContent.length;
      // in case cell merge, add row dynamically
      curTableXML.addRow(tableRow);

      // --- table row properties ---
      const curRowXML = curTableXML.getRow(i);

      const heightProps: TableRowHeight = tableRow.getHeight();
      if (heightProps.value !== TABLE_TR_HEIGHT_DEFAULT.value ||
        heightProps.getRule() !== TABLE_TR_HEIGHT_DEFAULT.type) {
        curRowXML.addRowHeight(heightProps);
      }
      const tblHeader = tableRow.isTableHeader();
      if (tblHeader !== IS_TABLE_TR_HEADER) {
        curRowXML.addIsTableHeader(tblHeader === true ?  '1' : '0');
      }
      // --- table row properties end ---

      for (let j = 0; j < tableRowContentLen; j++) {
        const tableCell = tableRowContent[j];
        const tableCellContent = tableCell.content.content;

        // table cell props
        const curCellXML = curRowXML.getCell(j);
        this.prepareTableCellProps(curCellXML, tableCell);

        if (tableCellContent.length < 1) {
          // shouldn't be here, at least one empty para
          // tslint:disable-next-line: no-console
          console.warn('tablecell has 0 item');
        } else {

          for (const item of tableCellContent) {
            if (item instanceof ParagraphCore) {
              // item is para
              const curPara = new Paragraph();

              this.traversePara(curPara, item, logicDocument, defaultParaProperty, defaultTextProperty,
                 doc, uniqueImageList, bSaveSelectedArea);

              const curTableCellXML = curTableXML.getCell(i, j);
              curTableCellXML.addContent(curPara);
            }
          }
        }
      }
    }

    if (headerFooterType === -1) {
      // main doc
      // doc.addTable(curTableXML);
      if (parent != null) { // may have region in main doc
        parent.addTable(curTableXML);
      }
    } else {
      if (headerFooterType === HeaderFooterType.Header) {
        doc.Headers[0].addTable(curTableXML);
      } else if (headerFooterType === HeaderFooterType.Footer) {
        doc.Footer.addTable(curTableXML);
      }

    }

  }

  /**
   * traverse table
   * @param tableInstance /
   * @param logicDocument /
   * @param defaultParaProperty /
   * @param defaultTextProperty /
   * @param doc /
   * @param uniqueImageList /
   * @param headerFooterType /
   * @param parent only used in MAIN DOC
   */
  private traverseTable2(tableInstance: TableCore, logicDocument: LogicDocument,
                         defaultParaProperty: IDefaultParaProperty, defaultTextProperty: IDefaultTextProperty,
                         doc: Document, uniqueImageList: Map<string, string>, headerFooterType: number = -1,
                         parent: Document | Region = null, bSaveSelectedArea: boolean = false,
                         sModJson: ISaveModJson = null): void {
    const tableContent = tableInstance.content;
    let cols = 0;

    // first loop: get cols of table
    for (const tableRow of tableContent) {
      const tableCellCount = tableRow.content.length;
      cols = cols > tableCellCount ? cols : tableCellCount;
    }

    const rows = tableInstance.getRowsCount();
    const name = this.prepareTableAttrs(tableInstance);
    // const colSizes = tableInstance.getAllColumnsWidth(); // TODO: is this updated with true column count?
    // for (let i = 0, colSizesLen = colSizes.length; i < colSizesLen; i++) {
    //   colSizes[i] = getPxForMM(colSizes[i] * 10);
    // }
    const colSizes = tableInstance.getTableGrid();
    // console.log(colSizes)
    const curTableXML = new Table(rows, colSizes.length, name, colSizes);
    // const curTableXML = new Table(rows, cols, name, colSizes);

    // set table props
    this.prepareTableTableProps(curTableXML, tableInstance);
    let secStack = [];

    // second loop: populate data
    for (let i = 0, tableContentLen = tableContent.length; i < tableContentLen; i++) {
      const tableRow = tableContent[i];
      const tableRowContent = tableRow.content;
      const tableRowContentLen = tableRowContent.length;
      // in case cell merge, add row dynamically
      curTableXML.addRow(tableRow);

      // --- table row properties ---
      const curRowXML = curTableXML.getRow(i);

      const heightProps: TableRowHeight = tableRow.getHeight();
      if (heightProps.value !== TABLE_TR_HEIGHT_DEFAULT.value ||
        heightProps.getRule() !== TABLE_TR_HEIGHT_DEFAULT.type) {
        curRowXML.addRowHeight(heightProps);
      }
      const tblHeader = tableRow.isTableHeader();
      if (tblHeader !== IS_TABLE_TR_HEADER) {
        curRowXML.addIsTableHeader(tblHeader === true ? '1' : '0');
      }
      // --- table row properties end ---

      for (let j = 0; j < tableRowContentLen; j++) {
        const tableCell = tableRowContent[j];
        const tableCellContent = tableCell.content.content;

        // table cell props
        const curCellXML = curRowXML.getCell(j);
        this.prepareTableCellProps(curCellXML, tableCell);

        if (tableCellContent.length < 1) {
          // shouldn't be here, at least one empty para
          // tslint:disable-next-line: no-console
          console.warn('tablecell has 0 item');
        } else {
          secStack = [];
          let prePara;

          for (const item of tableCellContent) {
            if (item instanceof ParagraphCore) {
              // item is para
              let curPara; // = new Paragraph();
              const secCount = secStack.length;

              if (0 === secCount) {
                curPara = new Paragraph();
                prePara = curPara;
              } else {
                curPara = prePara;
              }

              // if (bNewVersion) {
              this.traversePara3(curPara, item, logicDocument, defaultParaProperty, defaultTextProperty,
                doc, uniqueImageList, bSaveSelectedArea, sModJson, secStack);
              // } else {this.traversePara2(curPara, item, logicDocument, defaultParaProperty, defaultTextProperty,
              //     doc, uniqueImageList, bSaveSelectedArea, sModJson);
              // }

              if (0 === secCount) {
                const curTableCellXML = curTableXML.getCell(i, j);
                curTableCellXML.addContent(curPara);
              }
            }
          }
        }
      }
    }

    if (headerFooterType === -1) {
      // main doc
      // doc.addTable(curTableXML);
      if (parent != null) { // may have region in main doc
        parent.addTable(curTableXML);
      }
    } else {
      if (headerFooterType === HeaderFooterType.Header) {
        doc.Headers[0].addTable(curTableXML);
      } else if (headerFooterType === HeaderFooterType.Footer) {
        doc.Footer.addTable(curTableXML);
      }

    }

  }

  /**
   * traverse table
   * @param tableInstance /
   * @param logicDocument /
   * @param defaultParaProperty /
   * @param defaultTextProperty /
   * @param doc /
   * @param uniqueImageList /
   * @param headerFooterType /
   * @param parent only used in MAIN DOC
   */
  private traverseNISTable(nisTableInstance: NISTableCore, logicDocument: LogicDocument,
                           defaultParaProperty: IDefaultParaProperty, defaultTextProperty: IDefaultTextProperty,
                           doc: Document, uniqueImageList: Map<string, string>, headerFooterType: number = -1,
                           parent: Document | Region = null, bSaveSelectedArea: boolean = false,
                           sModJson: ISaveModJson = null): void {

    const tableContent = nisTableInstance.content;
    // let cols = 0;

    // first loop: get cols of table
    // for (const tableRow of tableContent) {
    //   const tableCellCount = tableRow.content.length;
    //   cols = cols > tableCellCount ? cols : tableCellCount;
    // }

    const rows = nisTableInstance.getRowsCount();
    const name = this.prepareNISTableAttrs(nisTableInstance);

    const colSizes = nisTableInstance.getTableGrid();
    // console.log(colSizes)
    const curNISTableXML = new NISTable(rows, colSizes.length, name, colSizes);
    // const curTableXML = new Table(rows, cols, name, colSizes);
    const nisTableJson = {'tableID': nisTableInstance.getTableName(), 'rows': []};

    // set table props
    this.prepareNISTableTableProps(curNISTableXML, nisTableInstance);

    let secStack = [];

    // second loop: populate data
    for (let i = 0, tableContentLen = tableContent.length; i < tableContentLen; i++) {
      const tableRow = tableContent[i];
      const tableRowContent = tableRow.content;
      const tableRowContentLen = tableRowContent.length;
      // in case cell merge, add row dynamically
      curNISTableXML.addRow(tableRow);

      // --- table row properties ---
      const curNISRowXML = curNISTableXML.getRow(i);
      const rowJson = {'rowID': tableRow.getNISRowID()};

      const heightProps: TableRowHeight = tableRow.getHeight();
      if (heightProps.value !== TABLE_TR_HEIGHT_DEFAULT.value ||
        heightProps.getRule() !== TABLE_TR_HEIGHT_DEFAULT.type) {
        curNISRowXML.addRowHeight(heightProps);
      }
      const tblHeader = tableRow.isTableHeader();
      const backcolor = nisTableInstance.getTableHeaderShadow();
      if (tblHeader !== IS_TABLE_TR_HEADER) {
        curNISRowXML.addIsTableHeader(tblHeader === true ? '1' : '0', backcolor);
      }
      const rowReadOnly = tableRow.isReadOnly();
      if (rowReadOnly === true) {
        curNISRowXML.addReadOnly('1');
      }
      // trNis
      const nisProperty = tableRow.getNISRowProperty();
      if (nisProperty != null) {
        curNISRowXML.addTrNis(nisProperty);
      }

      // --- table row properties end ---

      for (let j = 0; j < tableRowContentLen; j++) {
        const tableCell = tableRowContent[j];
        const tableCellContent = tableCell.content.content;

        // table cell props
        const curNISCellXML = curNISRowXML.getCell(j);
        this.prepareNISTableCellProps(curNISCellXML, tableCell);

        // if (!tblHeader) {
        rowJson[tableCell.getNISColID()] = tableCell.content.getContentText();
        // }

        if (tableCellContent.length < 1) {
          // shouldn't be here, at least one empty para
          // tslint:disable-next-line: no-console
          console.warn('tablecell has 0 item');
        } else {
          secStack = [];
          let prePara;

          for (const item of tableCellContent) {
            if (item instanceof ParagraphCore) {
              // item is para
              let curPara; // = new Paragraph();
              const secCount = secStack.length;

              if (0 === secCount) {
                curPara = new Paragraph();
                prePara = curPara;
              } else {
                curPara = prePara;
              }

              this.traversePara3(curPara, item, logicDocument, defaultParaProperty, defaultTextProperty,
                doc, uniqueImageList, bSaveSelectedArea, sModJson, secStack);

              if (0 === secCount) {
                const curNISTableCellXML = curNISTableXML.getCell(i, j);
                curNISTableCellXML.addContent(curPara);
              }
            }
          }
        }
      }

      // if (!tblHeader) {
      nisTableJson['rows'].push(rowJson);
      // }
    }

    if (headerFooterType === -1) {
      // main doc
      // doc.addTable(curTableXML);
      if (parent != null) { // may have region in main doc
        parent.addNISTable(curNISTableXML);
        if (sModJson?.nisTable) {
          sModJson.nisTable.push(nisTableJson);
        }
      }
    } else {

      // nistable shouldn't be in header footer

      // if (headerFooterType === HeaderFooterType.Header) {
      //   doc.Headers[0].addTable(curTableXML);
      // } else if (headerFooterType === HeaderFooterType.Footer) {
      //   doc.Footer.addTable(curTableXML);
      // }
    }

  }

  private prepareTableTableProps(curTableXML: Table, tableInstance: TableCore): void {
    // set table props
    const {bFixedRowHeight, bFixedColWidth, bCanAddRow, bCanDeleteRow, bRepeatHeader,
      bReadOnly, bDeleteProtect, tableCellMargins, tableDefaultMargins, enableRowAction,
      customProperty, bPrintEmptyRow, bRepeatOnBreak} = tableInstance.getTableProps();

    const curTableXMLProps = curTableXML.Properties;
    curTableXMLProps.enableRowAction(enableRowAction ? (enableRowAction + '') : null);
    if (bFixedRowHeight !== TABLE_PROPS_DEFAULT.bFixedRowHeight) {
      curTableXMLProps.setFixedRowHeight(bFixedRowHeight === true ? '1' : '0');
    }
    if (bFixedColWidth !== TABLE_PROPS_DEFAULT.bFixedColWidth) {
      curTableXMLProps.setFixedColWidth(bFixedColWidth === true ? '1' : '0');
    }
    if (bCanAddRow !== TABLE_PROPS_DEFAULT.bCanAddRow) {
      curTableXMLProps.setAddRowProtect(bCanAddRow === false ?  '1' : '0');
    }
    if (bCanDeleteRow !== TABLE_PROPS_DEFAULT.bCanDeleteRow) {
      curTableXMLProps.setDelRowProtect(bCanDeleteRow === false ?  '1' : '0');
    }
    if (bRepeatHeader !== TABLE_PROPS_DEFAULT.bRepeatHeader) {
      curTableXMLProps.setRepeatHeader(bRepeatHeader === false ?  '1' : '0');
    }
    if (bReadOnly !== TABLE_PROPS_DEFAULT.bReadOnly) {
      curTableXMLProps.setEditProtect(bReadOnly === true ?  '1' : '0');
    }
    if (bDeleteProtect !== TABLE_PROPS_DEFAULT.bDeleteProtect) {
      curTableXMLProps.setDeleteProtect(bDeleteProtect === true ?  '1' : '0');
    }
    // console.log(tableCellMargins) // not used?
    // console.log(tableDefaultMargins)
    if (tableDefaultMargins.left !== TABLE_PROPS_DEFAULT.tableCellMargins.left) {
      // const left = getPxForMM(tableDefaultMargins.left);
      const left = tableDefaultMargins.left * 10;
      curTableXMLProps.setCellLeft(left + '');
    }
    if (tableDefaultMargins.right !== TABLE_PROPS_DEFAULT.tableCellMargins.right) {
      // const right = getPxForMM(tableDefaultMargins.right);
      const right = tableDefaultMargins.right * 10;
      curTableXMLProps.setCellRight(right + '');
    }
    if (tableDefaultMargins.top !== TABLE_PROPS_DEFAULT.tableCellMargins.top) {
      // const top = getPxForMM(tableDefaultMargins.top);
      const top = tableDefaultMargins.top * 10;
      curTableXMLProps.setCellTop(top + '');
    }
    if (tableDefaultMargins.bottom !== TABLE_PROPS_DEFAULT.tableCellMargins.bottom) {
      // const bottom = getPxForMM(tableDefaultMargins.bottom);
      const bottom = tableDefaultMargins.bottom * 10;
      curTableXMLProps.setCellBottom(bottom + '');
    }
    if (customProperty.length > 0) {
      const customPropsMap = this.convertCustomPropsArrToMap(customProperty);
      curTableXMLProps.addCustomProperty(customPropsMap);
    }

    if (bPrintEmptyRow === false) {
      addNode('bPrintEmptyRow', bPrintEmptyRow.toString(), curTableXMLProps.root);
    }
    if (bRepeatOnBreak === true) {
      addNode('bRepeatOnBreak', bRepeatOnBreak.toString(), curTableXMLProps.root);
    }

  }

  private prepareNISTableTableProps(curNISTableXML: NISTable, nisTableInstance: NISTableCore): void {
    // set table props
    const {bFixedRowHeight, bFixedColWidth, bCanAddRow, bCanDeleteRow, bRepeatHeader,
      bReadOnly, bDeleteProtect, bHeaderReadOnly, customProperty, columnIDs,
      fixedLeft, fixedRight, bFixedHeader} = nisTableInstance.getTablePropsToSave();

    const curNISTableXMLProps = curNISTableXML.Properties;
    if (bFixedRowHeight !== TABLE_PROPS_DEFAULT.bFixedRowHeight) {
      curNISTableXMLProps.setFixedRowHeight(bFixedRowHeight === true ? '1' : '0');
    }
    if (bFixedColWidth !== TABLE_PROPS_DEFAULT.bFixedColWidth) {
      curNISTableXMLProps.setFixedColWidth(bFixedColWidth === true ? '1' : '0');
    }
    if (bCanAddRow !== TABLE_PROPS_DEFAULT.bCanAddRow) {
      curNISTableXMLProps.setAddRowProtect(bCanAddRow === false ?  '1' : '0');
    }
    if (bCanDeleteRow !== TABLE_PROPS_DEFAULT.bCanDeleteRow) {
      curNISTableXMLProps.setDelRowProtect(bCanDeleteRow === false ?  '1' : '0');
    }
    if (bRepeatHeader !== TABLE_PROPS_DEFAULT.bRepeatHeader) {
      curNISTableXMLProps.setRepeatHeader(bRepeatHeader === false ?  '1' : '0');
    }
    if (bReadOnly !== TABLE_PROPS_DEFAULT.bReadOnly) {
      curNISTableXMLProps.setEditProtect(bReadOnly === true ?  '1' : '0');
    }
    if (bDeleteProtect !== TABLE_PROPS_DEFAULT.bDeleteProtect) {
      curNISTableXMLProps.setDeleteProtect(bDeleteProtect === true ?  '1' : '0');
    }
    if (bHeaderReadOnly !== TABLE_PROPS_DEFAULT.bHeaderReadOnly) {
      curNISTableXMLProps.setHeaderReadOnly(bHeaderReadOnly === true ?  '1' : '0');
    }
    if (columnIDs !== TABLE_PROPS_DEFAULT.columnIDs) {
      curNISTableXMLProps.setColumnIDs(columnIDs);
    }
    if (!isNaN(fixedLeft)  && fixedLeft !== TABLE_PROPS_DEFAULT.fixedLeft) {
      curNISTableXMLProps.setFixedLeft(fixedLeft + '');
    }
    if (!isNaN(fixedRight) && fixedRight !== TABLE_PROPS_DEFAULT.fixedRight) {
      curNISTableXMLProps.setFixedRight(fixedRight + '');
    }
    if (bFixedHeader != null && bFixedHeader !== TABLE_PROPS_DEFAULT.bFixedHeader) {
      curNISTableXMLProps.setBFixedHeader(bFixedHeader === true ? '1' : '0');
    }

    // if (bFixedHeader !== TABLE_PROPS_DEFAULT.bFixedHeader) {
    //   curNISTableXMLProps.setBFixedHeader(bFixedHeader === true ? '1' : '0');
    // }

    // if (cellGridLines != null) {
    //   addNode('cellGridLines', JSON.stringify(cellGridLines), curNISTableXMLProps.root);
    // }
  
    // console.log(tableCellMargins) // not used?
    // console.log(tableDefaultMargins)
    // if (tableDefaultMargins.left !== TABLE_PROPS_DEFAULT.tableCellMargins.left) {
    //   // const left = getPxForMM(tableDefaultMargins.left);
    //   const left = tableDefaultMargins.left * 10;
    //   curNISTableXMLProps.setCellLeft(left + '');
    // }
    // if (tableDefaultMargins.right !== TABLE_PROPS_DEFAULT.tableCellMargins.right) {
    //   // const right = getPxForMM(tableDefaultMargins.right);
    //   const right = tableDefaultMargins.right * 10;
    //   curNISTableXMLProps.setCellRight(right + '');
    // }
    // if (tableDefaultMargins.top !== TABLE_PROPS_DEFAULT.tableCellMargins.top) {
    //   // const top = getPxForMM(tableDefaultMargins.top);
    //   const top = tableDefaultMargins.top * 10;
    //   curNISTableXMLProps.setCellTop(top + '');
    // }
    // if (tableDefaultMargins.bottom !== TABLE_PROPS_DEFAULT.tableCellMargins.bottom) {
    //   // const bottom = getPxForMM(tableDefaultMargins.bottom);
    //   const bottom = tableDefaultMargins.bottom * 10;
    //   curNISTableXMLProps.setCellBottom(bottom + '');
    // }
    if (customProperty.length > 0) {
      const customPropsMap = this.convertCustomPropsArrToMap(customProperty);
      curNISTableXMLProps.addCustomProperty(customPropsMap);
    }
  }

  private prepareTableCellProps(curCellXML: TableCell, tableCell: TableCellCore): void {
    // gridspan
    const gridSpan = tableCell.getGridSpan();
    // console.log(gridSpan)
    if (gridSpan !== TABLE_CELL_PROPS_DEFAULT.gridSpan) {
      curCellXML.addGridSpan(gridSpan);
    }

    // vMerge
    const vMerge = tableCell.getVMerge();
    // console.log(vMerge);
    if (vMerge !== TABLE_CELL_PROPS_DEFAULT.verticalMerge) {
      curCellXML.addVMerge(vMerge);
    }

    // tcW
    const tcW = tableCell.getCellWidth();
    // console.log(tcW);
    const cellWidth: ICellWidthProperties = {
      width: +tcW.width.toFixed(2),
      type: tcW.type,
    };
    curCellXML.addCellWidth(cellWidth);

    // protected
    const cellProtected = tableCell.isCellProtected();
    // console.log(cellProtected)
    if (cellProtected !== TABLE_CELL_PROPS_DEFAULT.bProtected) {
      curCellXML.addCellProtected(cellProtected === true ?  '1' : '0');
    }

    const cellFormula = tableCell.getCellFormula();
    if ( TABLE_CELL_PROPS_DEFAULT.cellFormula !== cellFormula) {
      curCellXML.addCellFormula(cellFormula);
    }

    const cellVertAlign = tableCell.getVertAlign();
    if ( TABLE_CELL_PROPS_DEFAULT.cellVertAlign !== cellVertAlign) {
      curCellXML.addCellVertAlign(cellVertAlign);
    }

    // table cell slash
    for (const slashType of tableCell.getSlashBorders()) {
        curCellXML.addCellSlash(slashType);
    }

    // 0 -- top；1 -- right；2 -- bottom； 3 -- left
    const topTcBorder = tableCell.getBorder(0);
    if (topTcBorder.size !== TABLE_CELL_BORDER.size ||
      topTcBorder.value !== TABLE_CELL_BORDER.val || topTcBorder.color.toHex() !== TABLE_CELL_BORDER.color) {
        const border: ITableCellBorder = {
          size: topTcBorder.size, color: topTcBorder.color.toHex(), val: topTcBorder.value};
        curCellXML.addTopBorder(border);
    }
    const rightTcBorder = tableCell.getBorder(1);
    if (rightTcBorder.color.toHex() !== TABLE_CELL_BORDER.color || rightTcBorder.size !== TABLE_CELL_BORDER.size ||
      rightTcBorder.value !== TABLE_CELL_BORDER.val) {
        const border: ITableCellBorder = {
          size: rightTcBorder.size, color: rightTcBorder.color.toHex(), val: rightTcBorder.value};
        curCellXML.addRightBorder(border);
    }
    const bottomTcBorder = tableCell.getBorder(2);
    if (bottomTcBorder.color.toHex() !== TABLE_CELL_BORDER.color || bottomTcBorder.size !== TABLE_CELL_BORDER.size ||
      bottomTcBorder.value !== TABLE_CELL_BORDER.val) {
        const border: ITableCellBorder = {
          size: bottomTcBorder.size, color: bottomTcBorder.color.toHex(), val: bottomTcBorder.value};
        curCellXML.addBottomBorder(border);
    }
    const leftTcBorder = tableCell.getBorder(3);
    if (leftTcBorder.color.toHex() !== TABLE_CELL_BORDER.color || leftTcBorder.size !== TABLE_CELL_BORDER.size ||
      leftTcBorder.value !== TABLE_CELL_BORDER.val) {
        const border: ITableCellBorder = {
          size: leftTcBorder.size, color: leftTcBorder.color.toHex(), val: leftTcBorder.value};
        curCellXML.addLeftBorder(border);
    }
  }

  private prepareNISTableCellProps(curNISCellXML: NISTableCell, tableCell: TableCellCore): void {
    // gridspan
    const gridSpan = tableCell.getGridSpan();
    // console.log(gridSpan)
    if (gridSpan !== TABLE_CELL_PROPS_DEFAULT.gridSpan) {
      curNISCellXML.addGridSpan(gridSpan);
    }

    // vMerge
    const vMerge = tableCell.getVMerge();
    // console.log(vMerge);
    if (vMerge !== TABLE_CELL_PROPS_DEFAULT.verticalMerge) {
      curNISCellXML.addVMerge(vMerge);
    }

    // tcW
    const tcW = tableCell.getCellWidth();
    const defaultWidth = TABLE_CELL_PROPS_DEFAULT.width;
    if (!(tcW.width === defaultWidth.width && tcW.type === defaultWidth.type)) {
      // console.log(tcW);
      const cellWidth: ICellWidthProperties = {
        width: tcW.width,
        type: tcW.type,
      };
      curNISCellXML.addCellWidth(cellWidth);
    }

    // protected
    const cellProtected = tableCell.isCellProtected();
    // console.log(cellProtected)
    if (cellProtected !== TABLE_CELL_PROPS_DEFAULT.bProtected) {
      curNISCellXML.addCellProtected(cellProtected === true ?  '1' : '0');
    }

    const cellFormula = tableCell.getCellFormula();
    if ( TABLE_CELL_PROPS_DEFAULT.cellFormula !== cellFormula) {
      curNISCellXML.addCellFormula(cellFormula);
    }

    const cellVertAlign = tableCell.getVertAlign();
    if ( TABLE_CELL_PROPS_DEFAULT.cellVertAlign !== cellVertAlign) {
      curNISCellXML.addCellVertAlign(cellVertAlign);
    }

    // 0 -- top；1 -- right；2 -- bottom； 3 -- left
    const topTcBorder = tableCell.getBorder(0);
    if (topTcBorder.color.toHex() !== TABLE_CELL_BORDER.color || topTcBorder.size !== NISTABLE_CELL_BORDER.size ||
      topTcBorder.value !== TABLE_CELL_BORDER.val) {
        const border: ITableCellBorder = {
          size: topTcBorder.size, color: topTcBorder.color.toHex(), val: topTcBorder.value};
        curNISCellXML.addTopBorder(border);
    }
    const rightTcBorder = tableCell.getBorder(1);
    if (rightTcBorder.color.toHex() !== TABLE_CELL_BORDER.color || rightTcBorder.size !== NISTABLE_CELL_BORDER.size ||
      rightTcBorder.value !== TABLE_CELL_BORDER.val) {
        const border: ITableCellBorder = {
          size: rightTcBorder.size, color: rightTcBorder.color.toHex(), val: rightTcBorder.value};
        curNISCellXML.addRightBorder(border);
    }
    const bottomTcBorder = tableCell.getBorder(2);
    if (bottomTcBorder.color.toHex() !== TABLE_CELL_BORDER.color || bottomTcBorder.size !== NISTABLE_CELL_BORDER.size ||
      bottomTcBorder.value !== TABLE_CELL_BORDER.val) {
        const border: ITableCellBorder = {
          size: bottomTcBorder.size, color: bottomTcBorder.color.toHex(), val: bottomTcBorder.value};
        curNISCellXML.addBottomBorder(border);
    }
    const leftTcBorder = tableCell.getBorder(3);
    if (leftTcBorder.color.toHex() !== TABLE_CELL_BORDER.color || leftTcBorder.size !== NISTABLE_CELL_BORDER.size ||
      leftTcBorder.value !== TABLE_CELL_BORDER.val) {
        const border: ITableCellBorder = {
          size: leftTcBorder.size, color: leftTcBorder.color.toHex(), val: leftTcBorder.value};
        curNISCellXML.addLeftBorder(border);
    }

    // tcNis
    const nisProperty = tableCell.getNISProperty();
    if (nisProperty != null) {
      curNISCellXML.addTcNis(nisProperty);
    }

  }

  private convertCustomPropsArrToMap(customProps: ICustomProps[]): Map<string, ICustomProps> {
    if (customProps.length > 0) {
      const customPropsMap = new Map();
      for (const item of customProps) {
        customPropsMap.set(item.name, item);
      }
      return customPropsMap;
    }
  }

  private setSignatureSubTextStrutsAsPlaceholder(currentContentControl: NewControl, curPara: Paragraph): void {
    const leafList = currentContentControl.getLeafList();
    const leafListNames = [];
    const removeIndexes = [];

    for (const leaf of leafList) {
      leafListNames.push(leaf.getNewControlName());
    }
    const curParaRoot = curPara.getRoot();

    // indicator of real content search btw sub text structs
    let searchTrigger = false;
    let subStructIndex = 0;
    const curPlaceholder = currentContentControl.getSignaturePlaceholder();
    let withinStruct = false;

    for (let rootIndex = 0, len = curParaRoot.length; rootIndex < len; rootIndex++) {
      const curItemRoot = curParaRoot[rootIndex];
      if (curItemRoot instanceof TextRun) {
        if (searchTrigger === true && withinStruct === true) {
          const curItemRootRoot = curItemRoot.getRoot();
          // if it's not placeholder portion, remove it
          // TODO: may need to think of title
          for (const rootItem of curItemRootRoot) {
            if (rootItem['rootKey'] === 'w:rPr') {
              // ignore
              continue;
            }
            if (rootItem['rootKey'] === 'w:t') {
              // text
              const rootText = customEncodeURIComponent(rootItem['root'][0]);
              if (rootText.indexOf(curPlaceholder) !== -1) {
                // only case need to preserve
              } else {
                // text not the same as placeholder
                removeIndexes.push(rootIndex);
              }
            } else {
              // should be anything else?
              removeIndexes.push(rootIndex);
            }

          }
        }
      } else if (curItemRoot instanceof ContentControlStart) {
        withinStruct = true;
        const rootKey = curItemRoot.getRootKey();
        // console.log(rootKey)
        if (rootKey === 'sdtStart') {
          const curItemRootRoot = curItemRoot.getRoot();
          // first is contentcontrolattr
          const attrRoot = curItemRootRoot[0]['root'];
          // console.log(attrRoot);
          if ( leafListNames[subStructIndex] === attrRoot.name ) {
            searchTrigger = true;
          }
          // also find & set placeholder
          // for (const rootItem of curItemRootRoot) {
          //   if (rootItem['rootKey'] === 'placeholder') {
          //     curPlaceholder = rootItem['root'][0];
          //   }
          // }
        } else if (rootKey === 'sdtEnd') {
          const curItemRootRoot = curItemRoot.getRoot();
          // first is contentcontrolattr
          const attrRoot = curItemRootRoot[0]['root'];
          // console.log(attrRoot);
          if ( leafListNames[subStructIndex] === attrRoot.name ) {
            searchTrigger = false;
            subStructIndex++;
          }
        }

      } else if (curItemRoot instanceof ContentControlEnd) {
        withinStruct = false;
      }
    }
    // console.log(removeIndexes)

    for (const removeIndex of removeIndexes) {
      // console.log(removeIndex)
      const curItemRoot = curParaRoot[removeIndex];
      // console.log(curItemRoot)
      if (curItemRoot instanceof TextRun) {
        const curItemRootRoot = curItemRoot.getRoot();
        // preserve w:rPr, and if there exists 'w:t', replace content with placeholder;
        // if not, initialize a new placeholder w:t
        let hasTextNode = false;

        // remove items from textrun (better rear to front?)
        for (let len = curItemRootRoot.length, index = len - 1; index >= 0; index--) {
          const textRunItem = curItemRootRoot[index];
          if (textRunItem['rootKey'] === 'w:rPr') {
            // TODO: if w:rPr is found, add to sdtStart/sdtEnd as well
            // console.log(textRunItem)
            continue;
          } else if (textRunItem['rootKey'] === 'w:t') {
            // change to placeholder
            textRunItem['root'][0] = curPlaceholder;
            hasTextNode = true;
          } else {
            curItemRootRoot.splice(index, 1);
          }
        }
        if (hasTextNode === false) {
          curItemRoot.addText(curPlaceholder);
        }
      }
    }
  }

  // private setSignatureSubTextStrutsAsPlaceholder2(currentContentControl: NewControl, curPara: Paragraph): void {
  private clearSignatureSubTextStrutsContent(
    currentContentControl: NewControl, curPara: Paragraph, defaultTextProperty: IDefaultTextProperty, sdtContent?: any
  ): void {
    let sdtContentNode = null;
    const curPlaceholder = currentContentControl.getSignaturePlaceholder();
    if (sdtContent === undefined) {
      const curParaRoot = curPara.getRoot();
      
      const curControlName = currentContentControl.getNewControlName();
      // console.log(curPlaceholder)
  
      
      for (let rootIndex = 0, len = curParaRoot.length; rootIndex < len; rootIndex++) {
        const curItemRoot = curParaRoot[rootIndex];
        if (curItemRoot instanceof Sdt) {
          const sdtRoot = curItemRoot.getRoot();
          const contentControlAttrNode = sdtRoot[0];
          if ( contentControlAttrNode instanceof ContentControlAttributes) {
            const contentControlAttrNodeRoot = contentControlAttrNode.getRoot();
            if (contentControlAttrNodeRoot['name'] === curControlName) {
              if (contentControlAttrNodeRoot['type'] === NewControlType.SignatureBox) {
                // get sdtContent node, which should always be the last
                sdtContentNode = sdtRoot[sdtRoot.length - 1];
                break;
              }
            } else {
              // para may have multi sign control
              continue;
            }
          }
        }
      }
    } else {
      sdtContentNode = sdtContent;
    }
    

    // console.log(sdtContentNode);

    // find out child sign structs
    const sdtContentNodeRoot = sdtContentNode.getRoot();
    const signChildStructSdtContents = [];
    for (let sdtContentIndex = 0, len = sdtContentNodeRoot.length; sdtContentIndex < len; sdtContentIndex++) {
      const sdtContentItem = sdtContentNodeRoot[sdtContentIndex];
      if (sdtContentItem instanceof Sdt) {
        const sdtContentItemRoot = sdtContentItem.getRoot();

        const length = sdtContentItemRoot.length; 
        for (let index = 0; index < length; index++) {
          const item = sdtContentItemRoot[index];
          if (item instanceof XmlComponent && 'helpTip' === item.getRootKey()) {
            item.getRoot()
                  ?.pop();
            break;
          }
        }

        signChildStructSdtContents.push(sdtContentItemRoot[length - 1]);
      }
    }

    // console.log(signChildStructSdtContents)
    for (const signChildStructsdtcontent of signChildStructSdtContents) {
      const curSdtContentRoot = signChildStructsdtcontent.getRoot();
      if (curPlaceholder != null && curPlaceholder !== STD_START_DEFAULT.signaturePlaceholder) {
        // placeholder can only be the 1st run
        // if (curSdtContentRoot.length > 1) {
        //   curSdtContentRoot.splice(1);
        // }
        // check the 1st run whether ph or not. if not, remove as well
        if (curSdtContentRoot[0] instanceof Run) {
          const curSdtContents = curSdtContentRoot[0].getRoot();
          // for (const curSdtContent of curSdtContents) {
          //   if (curSdtContent['rootKey'] === 'w:t') {
          //     const rootText = customEncodeURIComponent(curSdtContent['root'][0]); // readonly
          //     if (rootText.indexOf(curPlaceholder) === -1) {
          //       // not placeholder. remove all
          //       // change to placeholder
          //       curSdtContent['root'][0] = curPlaceholder;
          //     }
          //     break;
          //   }
          // }

          if (1 <= curSdtContents.length) {
            const newPortion = currentContentControl.getNewControlContent()
                                  .initPlaceHolder(false, currentContentControl.isMustInput());
            const run = this.initializeRun(null, newPortion, defaultTextProperty);
            run.addText(curPlaceholder);

            // 删除多个portion，只留下1个portion保存placeholder
            if (1 < curSdtContents.length) {
              curSdtContentRoot.splice(1, curSdtContentRoot.length);
            }

            curSdtContentRoot[0] = run;
          }
        }
      } else {
        // no sign placeholder, remove everything
        curSdtContentRoot.length = 0;
      }
    }
  }

  private addRevisionToPara(portion: ParaPortion, curPara: Paragraph | SdtContent): Revision {
    const reviewType = portion.getReviewType();
    const reviewInfo = portion.getReviewInfo();
    const records = reviewInfo?.getSavedRecord();
    const bInsert = (ReviewType.Add === reviewType ? true : false);
    const bSecondRev = (!bInsert && !!reviewInfo && !!reviewInfo.getDeleteInfo());

    if (bSecondRev) {
      // 第二次删除的修订信息
      const secondIndo = reviewInfo.getDeleteInfo();
      secondIndo.setSavedCount(secondIndo.getSavedCount() + 1);

      const settings2 = secondIndo.getRevisionSetting();
      const revisionArrts2 = this.prepareRevisionAtts(settings2);
      const revision2 = new Revision(false, revisionArrts2);

      // 第一次新增的修订信息
      reviewInfo.setSavedCount(reviewInfo.getSavedCount() + 1);

      const settings = reviewInfo ? reviewInfo.getRevisionSetting() : null;
      const revisionArrts = this.prepareRevisionAtts(settings);
      const revision = new Revision(true, revisionArrts);

      revision2.addSubRevision(revision);
      curPara.addRevision(revision2);

      return revision2;
    } else {
      if (reviewInfo) {
        reviewInfo.setSavedCount(reviewInfo.getSavedCount() + 1);
      }

      const settings = reviewInfo ? reviewInfo.getRevisionSetting() : null;
      const revisionArrts = this.prepareRevisionAtts(settings, records);
      if (reviewInfo['firstDate']) {
        revisionArrts.firstDate = reviewInfo['firstDate'];
      }

      const revision = new Revision(bInsert, revisionArrts);

      curPara.addRevision(revision);

      return revision;
    }
  }

  private addDefaultDocumentVersion(output: number[]): void {
    // default val
    if (FILE_HEADER_DOCUMENT_VERSION > 0 && FILE_HEADER_DOCUMENT_VERSION < 65536) {
      output.push(+(0).toString(8));
    }
    output.push(+FILE_HEADER_DOCUMENT_VERSION.toString(8));
  }

  private fileToBlob(doc: any, props: any, bSaveToString: boolean, bSaveSelectedArea: boolean,
                     bAllowHtml: boolean = false): Promise<ArrayBuffer> {
    return new Promise((resolve, reject) => {
      const fileReader = new FileReader();
      const packer = new Packer();
      packer.toBlob(doc) // doc: File!
      .then((blob: any) => {
        fileReader.readAsArrayBuffer(blob);
        fileReader.onloadend = () => {
          const buffer = fileReader.result as ArrayBuffer;

          const revisedBuffer = this.addFileHeader(buffer, props.documentCore.getDocument(), bAllowHtml);

          // tslint:disable-next-line: no-console
          // console.timeEnd('compression level' + compressionLevel);
          // compressed = null;
          let newBlob: any = null;
          let fileType: string = FileSaveType.APO + ';version=' + EDITOR_VERSION;
          try {
            // newBlob = new Blob([new Uint8Array(revisedBuffer)], {type: fileType}); // also ok
            newBlob = new Blob([revisedBuffer], {type: fileType});
            // newBlob = new Blob([compressed.buffer], {type: fileType});
          } catch (error) {
            // alert(ErrorMessages.CompressFailure);
            // tslint:disable-next-line: no-console
            console.warn(ErrorMessages.CompressFailure);

            // compress failure, save as zip
            fileType = FileSaveType.ZIP + ';version=' + EDITOR_VERSION;
            newBlob = new Blob([buffer], {type: fileType});

            const customizedError = new Error('compress failure');
            window.dispatchEvent(new ErrorEvent('error',
              {message: ErrorMessages.CompressFailure, error: customizedError},
            ));
          }

          const type = fileType.slice(0, fileType.indexOf(';'));
          if (!bSaveToString && !bSaveSelectedArea) {
            if (type === FileSaveType.APO) {
              saveAs(newBlob, 'document.hz');
            } else if (type === FileSaveType.ZIP) {
              saveAs(newBlob, 'document.zip');
            } else {
              // alert(ErrorMessages.UnknownSaveType);
              message.error(ErrorMessages.UnknownSaveType);

              const customizedError = new Error('Unknown save type');
              window.dispatchEvent(new ErrorEvent('error',
                {message: ErrorMessages.UnknownSaveType, error: customizedError},
              ));
            }
          }

          // tslint:disable-next-line: no-console
          // console.timeEnd('save time');

          fileReader.readAsArrayBuffer(newBlob);
          fileReader.onloadend = () => {
            const newBlobBuffer = fileReader.result as ArrayBuffer;
            resolve(newBlobBuffer);
          };

        };
        // console.log(blob);
        // console.log(doc); // file structure. First 5 are just getter funcs.
        // saveAs(blob, 'testzip2.zip');
        // console.log("Document created successfully");
        // tslint:disable-next-line: no-console
        // console.timeEnd('save time');
      // tslint:disable-next-line: newline-per-chained-call
      }).catch((error) => {
        // tslint:disable-next-line: no-console
        console.log(error);

        // alert(ErrorMessages.ZipFailure); // more exactly, zip compiling/xmlifying error
        // tslint:disable-next-line: no-console
        console.warn(ErrorMessages.ZipFailure);
        const customizedError = new Error('zip compiling/xmlifying failure');
        window.dispatchEvent(new ErrorEvent('error', {message: ErrorMessages.ZipFailure, error: customizedError}));

        /** turn cache off temporarily */
        // save to cache if zipping fail
        // tslint:disable-next-line: newline-per-chained-call
        // this.generateXml(props, 'Document.xml').then((data) => {
        //   this.saveToCache(props, data);
        //   reject(customizedError);
        // // tslint:disable-next-line: newline-per-chained-call
        // }).catch((error2) => {
        //   alert(ErrorMessages.GenerateMainXmlFailure);
        //   const customizedError2 = new Error('cannot generate Document.xml to save in cache');
        //   window.dispatchEvent(new ErrorEvent('error',
        //     {message: ErrorMessages.GenerateMainXmlFailure, error: customizedError2},
        //   ));
        //   reject(customizedError2);
        // });
        /** turn cache off temporarily */
      });
    });
  }

  private fileToBlobApi(doc: any, props: any, bSaveToString: boolean, bSaveSelectedArea: boolean,
                        options: any, bAllowHtml: boolean = false): Promise<Blob> {
    return new Promise((resolve, reject) => {
      // const fileReader = new FileReader();
      const packer = new Packer();
      let date = new Date();

      packer.toArray(doc) // doc: File!
      .then((buffer: ArrayBuffer) => {
        // console.log(new Date().getTime() - date.getTime());
        // fileReader.readAsArrayBuffer(blob);
        // fileReader.onloadend = () => {
          // const buffer = blob; // fileReader.result as ArrayBuffer;
          // console.log(new Date().getTime() - date.getTime());
          const date1 = new Date();
          const revisedBuffer = this.addFileHeader(buffer, props.documentCore.getDocument(), bAllowHtml, options);

          // tslint:disable-next-line: no-console
          // console.timeEnd('compression level' + compressionLevel);
          // compressed = null;
          let newBlob: any = null;
          let fileType: string = FileSaveType.APO + ';version=' + EDITOR_VERSION;
          try {
            // newBlob = new Blob([new Uint8Array(revisedBuffer)], {type: fileType}); // also ok
            newBlob = new Blob([revisedBuffer], {type: fileType});
            // newBlob = new Blob([compressed.buffer], {type: fileType});
          } catch (error) {
            // alert(ErrorMessages.CompressFailure);
            // tslint:disable-next-line: no-console
            console.warn(ErrorMessages.CompressFailure);

            // compress failure, save as zip
            fileType = FileSaveType.ZIP + ';version=' + EDITOR_VERSION;
            newBlob = new Blob([buffer], {type: fileType});

            const customizedError = new Error('compress failure');
            window.dispatchEvent(new ErrorEvent('error',
              {message: ErrorMessages.CompressFailure, error: customizedError},
            ));
          }

          const type = fileType.slice(0, fileType.indexOf(';'));
          if (!bSaveToString && !bSaveSelectedArea) {
            if (type === FileSaveType.APO) {
              // saveAs(newBlob, 'document.apo');
            } else if (type === FileSaveType.ZIP) {
              // saveAs(newBlob, 'document.zip');
            } else {
              // alert(ErrorMessages.UnknownSaveType);
              message.error(ErrorMessages.UnknownSaveType);

              const customizedError = new Error('Unknown save type');
              window.dispatchEvent(new ErrorEvent('error',
                {message: ErrorMessages.UnknownSaveType, error: customizedError},
              ));
            }
          }
          options.time += new Date().getTime() - date.getTime();
          resolve(newBlob);
        // };
      }).catch((error) => {
        // tslint:disable-next-line: no-console
        console.warn(ErrorMessages.ZipFailure);
        const customizedError = new Error('zip compiling/xmlifying failure');
        window.dispatchEvent(new ErrorEvent('error', {message: ErrorMessages.ZipFailure, error: customizedError}));

      });
    });
  }

  private buildCommentData(data: CommentData): XmlCommentData {
    const xmlData = new XmlCommentData({
        userName: data.getUserName(),
        time: data.getStringTime(),
        solved: data.isSolved() ? '1' : '0',
        content: data.getContent(),
    });
    for (const reply of data.replies) {
        xmlData.addChildElement(this.buildCommentData(reply));
    }
    return xmlData;
  }

  private isInNISTimeCell(para: ParagraphCore): boolean {
    let result = false;
    if (para != null) {
      const paraParent = para.getParent();
      if (paraParent != null && paraParent instanceof DocumentContent) {
        const tableCell = paraParent.getParent();
        if (tableCell instanceof TableCellCore) {
          const nisProperty = tableCell.getNISProperty();
          if (nisProperty != null && nisProperty.type === NISTableCellType.Time) {
            result = true;
          }
        }
      }
    }

    return result;
  }

  /**
   * 获取当前段落的最后一个portion（非结束符）
   * 如果只有一个结束符，则返回结束符
   * @param para
   * @returns
   */
  private getLastPortion(para: ParagraphCore): ParaPortion {
    const index = Math.max(para.content.length - 2, 0);
    return para.content[index];
  }
}
