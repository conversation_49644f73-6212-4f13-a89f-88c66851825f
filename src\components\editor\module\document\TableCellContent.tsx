import { INISCellGridLine, numtoFixed } from '@/common/commonDefines';
import { SPELL_CHECK } from '@/common/Spellcheck';
import React from 'react';
import { ParaBaseUI } from './ParaBaseUI';

interface IProps {
    host: any;
    paras: any;
    pageIndex: number;
    scale: number;
    cellId: number;
    option?: any;
    nHeaderFooter?: number;
    cellRect?: any;
    bShowContent?: boolean;
    documentCore: any;
}
export class TableCellContent extends React.Component<IProps, {}> {
    private _content: any;
    // private _paraRefs: any;
    private scale: number;

    constructor(props: IProps) {
        super(props);
    }

    public render(): any {
        let options = this.props;
        this.scale = options.scale;
        if (this._content) {
            options = this._content;
        }
        const { paras, cellId} = options;
        return this.renderPara(paras, cellId);
    }

    public refresh(options: any): boolean {
        if (!options) {
            return false;
        }
        this._content = options;
        this.setState({}, () => {
            this._content = null;
            SPELL_CHECK.renderCell(this.props.pageIndex);
        });
        return true;
    }

    private renderPara(paras: any[], cellId: number): any {
        if (!paras || !paras.length) {
            return this.renderCellGirdLines(this.props.option);
        }
        const {host, scale, pageIndex, nHeaderFooter, option} = this.props;
        const len = paras.length - 1;
        return paras.map((para, index) => {
            let obj = option;
            if (cellId && obj) {
                if (len === index) {
                    obj = {...obj};
                    obj.bLast = true;
                }
            }
            
            return (
            <ParaBaseUI
                key={para.id}
                host={host}
                content={para}
                pageIndex={pageIndex}
                scale={scale}
                nHeaderFooter={nHeaderFooter}
                cellId={cellId}
                option={obj}
                cellRect={this.props.cellRect}
                bShowContent={this.props.bShowContent}
            />
            );
        });
    }

    /**
     * 换页时使用
     * @param item 
     * @param option 
     * @param lastLine 
     * @returns 
     */
    private renderCellGirdLines(option: any): any {
        if (!option) {
            return null;
        }

        const line: INISCellGridLine = option.gridLine;
        if (!line || !line.visible || !line.lineHeight) {
            return null;
        }

        const {x, cellBoundX, cellBoundY, y} = option;
        const x1 = numtoFixed(x);
        const x2 = numtoFixed(cellBoundX);
        
        const arrs = [];
        const height = line.lineHeight;
        const style = { stroke: line.color || '#ACB4C1', strokeWidth: (line.borderHeight || 1) + 'px' };
        for (let y1 = line.top + height + y + 1; y1 < cellBoundY; y1 = y1 + height) {
            if (y1 + height > cellBoundY) {
                break;
            }
            const y2 = numtoFixed(y1);
            arrs.push((<line className='nisgrid' key={y1 + 'cellgridline'} x1={x1} y1={y2} style={style} x2={x2} y2={y2} />));
        }

        return arrs;
    }
}
