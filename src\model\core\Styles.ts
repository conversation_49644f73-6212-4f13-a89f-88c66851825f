import { IEditorBackground, DEFAULT_BACKGROUND } from '../../common/commonDefines';

export default class Styles {

  private default: any;
  private background: IEditorBackground;

  constructor() {
    this.default = {
      header: null,
      footer: null,
    };
    this.background = {
      watermarkEnabled: DEFAULT_BACKGROUND.watermarkEnabled,
      watermarkType: DEFAULT_BACKGROUND.watermarkType,
      watermarkText: JSON.parse(JSON.stringify(DEFAULT_BACKGROUND.watermarkText)),
      fillColorEnabled: DEFAULT_BACKGROUND.fillColorEnabled,
      fillColorType: DEFAULT_BACKGROUND.fillColorType,
      fillTextColor: DEFAULT_BACKGROUND.fillTextColor,
    };
  }

  public getDefaultHeader(): any {
    return this.default.header;
  }

  public getDefaultFooter(): any {
    return this.default.footer;
  }

  public getEditorBackground(): IEditorBackground {
    return this.background;
  }

  public copyBackground(): IEditorBackground {
    return {...this.background};
  }
}
