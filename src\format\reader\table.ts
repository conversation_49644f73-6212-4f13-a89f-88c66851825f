import Document from '../../model/core/Document';
import { IContentControlDescObj, IInsertFileContent, IUniqueImageProps } from './reader';
import DocumentContentElementBase from 'src/model/core/DocumentContentElementBase';
import { Table } from '../../model/core/Table';
import { ParagraphReader } from './paragraph';
import { readCustomProperties, tReadCustomProperties } from '../miscellaneous';
import { TABLE_PROPS_DEFAULT, colorRgb, INSERT_FILE_END_EMPTY_PARA, rtNode,
  IModeFonts,
  CodeValueItem,
  FILE_HEADER_VERSION2,
  NISTableCellType,
  NIS_PROPERTY_DEFAULT,
  NISSelectType} from '../../common/commonDefines';
import { VerticalMergeType } from '../../model/core/Table/TableCellProperty';
import { TableMeasurement } from '../../model/core/TableProperty';
import { DocumentBorder } from '../../model/core/Style';
import HeaderFooter from '../../model/core/HeaderFooter';
import Paragraph from '../../model/core/Paragraph';
import { Region } from '../../model/core/Region';
import { NISTable } from '../../model/core/NISTable';
import { TableBase } from '../../model/core/TableBase';
import { parseFromString, safeDecodeURIComponent } from '../../common/commonMethods';
import { TableCell } from '../../model/core/Table/TableCell';
import { TableRow } from '../../model/core/Table/TableRow';
// import {parse as circularParse, stringify as circularStringify} from 'flatted/esm';

export class TableReader {

  /**
   * traverse table
   * @param rootNode /
   * @param document /
   * @param ContentControlDescObj /
   * @param uniqueImagelist /
   * @param bInsertFile /
   * @param paras /
   * @param bNoEndPara /
   * @param headerFooter /
   * @param parent only used in MAIN DOC
   */
  public static traverseTable(rootNode: any, document: Document, ContentControlDescObj: IContentControlDescObj,
                              uniqueImagelist: Map<string, IUniqueImageProps>, bInsertFile: boolean = false,
                              paras: DocumentContentElementBase[], bNoEndPara: boolean = false,
                              headerFooter: HeaderFooter = null, parent: Document | Region = null): void {
    const table = new Table(document, document);
    const tableChildren = rootNode.children;
    let rowIndex = 0;

    const colSizes: number[] = [];
    const cellMargins = {left: 1, right: 1, top: 1, bottom: 1};

    // 验证cell边距值的函数
    function validateCellMargin(value: string): number {
        const num = parseFloat(value);
        if (isNaN(num) || !isFinite(num)) {
            return 1; // 使用与traverseTableForInsertFile2一致的默认值
        }
        return num;
    }

    const nameAttr = rootNode.attributes.getNamedItem('name');
    let tableName = null;
    if (nameAttr != null) {
      // when open file, it must have unique table name! n
      tableName = nameAttr.nodeValue;
    }
    if (bInsertFile === true || tableName == null) {
      // need to check dup name when inserting
      if (tableName == null) {
        // tslint:disable-next-line: no-console
        console.warn('table name is null in reader, will fetch a new unique name');
      }
      tableName = document.tableManager.getUniqueTableName(tableName);
    }

    // set table name
    table.setTableName(tableName);

    // let cellWidthDefault = 0; // cellwidth must equal colWidth when auto and cols are with same width?
    for (const tableChild of tableChildren) {
      if (tableChild.nodeName === 'w:tblPr') {
        const prChildren = tableChild.children;
        for (const prChild of prChildren) {

          const nodeName = prChild.nodeName;
          switch (nodeName) {
            case 'fixedRowHeight': {
              // set fixed row height
              table.setFixedRowHeight(true);
              break;
            }
            case 'fixedColWidth': {
              // set fixed col width
              table.setFixedColWidth(true);
              break;
            }
            case 'addRowProtect': {
              table.setCanAddRow(false);
              break;
            }
            case 'delRowProtect': {
              table.setCanDeleteRow(false);
              break;
            }
            case 'deleteProtect': {
              table.setDeleteProtect(true);
              break;
            }
            case 'editProtect': {
              table.setTableReadOnly(true);
              break;
            }
            case 'repeatHeader': {
              table.setRepeatHeader(false);
              break;
            }
            case 'cellLeft': {
                const value = prChild.childNodes[0].nodeValue;
                cellMargins.left = validateCellMargin(value);
                break;
            }
            case 'cellRight': {
                const value = prChild.childNodes[0].nodeValue;
                cellMargins.right = validateCellMargin(value);
                break;
            }
            case 'cellTop': {
                const value = prChild.childNodes[0].nodeValue;
                cellMargins.top = validateCellMargin(value);
                break;
            }
            case 'cellBottom': {
                const value = prChild.childNodes[0].nodeValue;
                cellMargins.bottom = validateCellMargin(value);
                break;
            }
            case 'customProperty': {
              const customProperties = prChild.children;
              if (customProperties.length > 0) { // usually if in this block, length must be > 0

                const customPropertyArr = readCustomProperties(customProperties);
                // console.log(customPropertyArr)
                table.addCustomProps(customPropertyArr);
              }
              break;
            }
            default: {
              break;
            }
          }
        }

      } else if (tableChild.nodeName === 'w:tblGrid') {
        const colChildren = tableChild.children;

        for (const colChild of colChildren) {
          if (colChild.nodeName === 'w:gridCol') {
            const colWidth = +colChild.attributes.getNamedItem('w:w').nodeValue;
            if (!isNaN(colWidth)) {
              colSizes.push(colWidth);
              // cellWidthDefault = colWidth;
            } else {
              colSizes.push(100); // default val
            }
          }
        }
      } else if (tableChild.nodeName === 'w:tr') {
        // add empty row first
        table.addRow(rowIndex, 0, false);
        const row = table.getRow(rowIndex);

        // may have several tc
        const rowChildren = tableChild.children;
        let cellIndex = 0;

        for (const rowChild of rowChildren) {
          if (rowChild.nodeName === 'w:trPr') {
            const trPropChildren = rowChild.children;
            for (const trPropChild of trPropChildren) {
              if (trPropChild.nodeName === 'w:trHeight') {
                const heightVal = +trPropChild.attributes.getNamedItem('w:val').nodeValue;
                const heightType = +trPropChild.attributes.getNamedItem('w:type').nodeValue;
                row.setHeight(heightVal, heightType);
              } else if (trPropChild.nodeName === 'w:tblHeader') {
                row.setTableHeader(true);
              }
            }

          } else if (rowChild.nodeName === 'w:tc') {
            row.addCell(cellIndex, row, null, false);
            const cellChildren = rowChild.children;

            let pCount = 0;

            // let isCellBorderDirty = false;
            for (const cellChild of cellChildren) {
              const cell = row.getCell(cellIndex);

              if (cellChild.nodeName === 'w:tcPr') {
                const tcPrChildren = cellChild.children;
                for (const tcPrChild of tcPrChildren) {
                  const nodeName = tcPrChild.nodeName;

                  switch (nodeName) {
                    case 'w:tcW': {
                      const cellWidth = +tcPrChild.attributes.getNamedItem('w:w').nodeValue;
                      const cellType = +tcPrChild.attributes.getNamedItem('w:type').nodeValue;
                      // console.log(cellWidth, cellType);
                      if (cellWidth !== 0) { // if width = 0 , set or not no matter
                        cell.setCellWidth(new TableMeasurement(cellType, cellWidth));
                      }
                      // else {
                      //   console.log(cellWidthDefault);
                      //   cellWidth = cellWidthDefault;
                      //   cell.setCellWidth(new TableMeasurement(cellType, cellWidth));
                      // }

                      break;
                    }
                    case 'w:top': {
                      const border = new DocumentBorder();
                      const borderSize = tcPrChild.attributes.getNamedItem('w:sz').nodeValue;
                      const borderColor = tcPrChild.attributes.getNamedItem('w:color').nodeValue;

                      if ( null != borderSize ) {
                        border.size = parseFloat(borderSize);
                      }

                      if ( null != borderColor ) {
                        border.color = colorRgb(borderColor);
                      }
                      // const borderValue = tcPrChild.attributes.getNamedItem('w:sz').nodeValue;
                      cell.setBorder(border, 0);
                      break;
                    }
                    case 'w:right': {
                      const border = new DocumentBorder();
                      const borderSize = tcPrChild.attributes.getNamedItem('w:sz').nodeValue;
                      const borderColor = tcPrChild.attributes.getNamedItem('w:color').nodeValue;

                      if ( null != borderSize ) {
                        border.size = parseFloat(borderSize);
                      }

                      if ( null != borderColor ) {
                        border.color = colorRgb(borderColor);
                      }
                      cell.setBorder(border, 1);
                      break;
                    }
                    case 'w:bottom': {
                      const border = new DocumentBorder();
                      const borderSize = tcPrChild.attributes.getNamedItem('w:sz').nodeValue;
                      const borderColor = tcPrChild.attributes.getNamedItem('w:color').nodeValue;

                      if ( null != borderSize ) {
                        border.size = parseFloat(borderSize);
                      }

                      if ( null != borderColor ) {
                        border.color = colorRgb(borderColor);
                      }
                      cell.setBorder(border, 2);
                      break;
                    }
                    case 'w:left': {
                      const border = new DocumentBorder();
                      const borderSize = tcPrChild.attributes.getNamedItem('w:sz').nodeValue;
                      const borderColor = tcPrChild.attributes.getNamedItem('w:color').nodeValue;

                      if ( null != borderSize ) {
                        border.size = parseFloat(borderSize);
                      }

                      if ( null != borderColor ) {
                        border.color = colorRgb(borderColor);
                      }
                      cell.setBorder(border, 3);
                      break;
                    }
                    case 'w:gridSpan': {
                      const gridSpan = +tcPrChild.attributes.getNamedItem('w:val').nodeValue;
                      // console.log(gridSpan)
                      cell.setGridSpan(gridSpan);
                      break;
                    }
                    case 'w:vMerge': {
                      // const vMerge = tcPrChild.attributes.getNamedItem('w:val').nodeValue;
                      // console.log(vMerge)
                      cell.setVMerge(VerticalMergeType.Continue);
                      break;
                    }
                    case 'w:protected': {
                      cell.setCellProtected(true);
                      break;
                    }
                    case 'w:cellFormula': {
                      const formula = tcPrChild.attributes.getNamedItem('w:val').nodeValue;
                      cell.setCellFormula(formula, rowIndex, cellIndex);
                      break;
                    }
                    case 'w:cellVertAlign': {
                      const cellVertAlign = +tcPrChild.attributes.getNamedItem('w:val').nodeValue;
                      cell.setVertAlign2(cellVertAlign);
                      break;
                    }
                    default: {
                      break;
                    }
                  }
                }

              } else if (cellChild.nodeName === 'w:p') {
                if (pCount++ === 0) {
                  // remove empty para initialized in creating tablecell if true para is reached
                  cell.content.removeDefaultContent();
                }
                ParagraphReader.traverseParagraph(cellChild, document, ContentControlDescObj,
                  uniqueImagelist, bInsertFile, paras, bNoEndPara, cell, headerFooter);
              }
            }


            cellIndex++;
          }
        }
        rowIndex++;
      }
    }

    const {left, right, top, bottom} = cellMargins;
    const {left: leftDefault, right: rightDefault,
       top: topDefault, bottom: bottomDefault} = TABLE_PROPS_DEFAULT.tableCellMargins;
    if (left !== leftDefault * 10 || right !== rightDefault * 10 ||
      top !== topDefault * 10 || bottom !== bottomDefault * 10) {
        table.setTableCellMargin(left, right, top, bottom);
    }

    // console.log(colSizes)
    // table.setColumnsWidth(colSizes); // this can be used after recalc()
    table.setTableGrid(colSizes, true);

    // console.log(table)

    if (bInsertFile === false) {
      if (headerFooter == null) {
        // main doc & region
        // document.addToContent(document.content.length, table, true);
        if (parent) {
          if (parent instanceof Document) {
            parent.addToContent(document.content.length, table, true);
          } else if (parent instanceof Region) {
            parent.addToContent(parent.getContent().length, table, true);
          }
        }
      } else {
        const headerFooterOperateContent = headerFooter.getContent();
        const headerFooterContent = headerFooter.getTrueContent();
        // if (headerFooterContent.length === 1 && headerFooterContent[0].isEmpty() === true) { // only exist empty para
        //   // headerFooterContent = [];
        //   // remove empty para if there exists true para
        //   headerFooterContent.splice(headerFooterContent.length - 1, 1);
        // }
        headerFooterOperateContent.addToContent(headerFooterContent.length, table, true);
      }
    } else { // insert file
      if (parent && parent instanceof Region) {
        parent.addToContent(parent.getContent().length, table, true);
      } else {
        paras.push(table);

        if (rootNode.nextElementSibling && (rootNode.nextElementSibling.nodeName === 'w:sectPr')) {
          // TODO: always same lvl as sectpr? no! headerfooter insert file with region
          // add empty paras when required
          if (!bNoEndPara) {
            for (let i = 0; i < INSERT_FILE_END_EMPTY_PARA + 1; i++) {
              paras.push(new Paragraph(document, document));
            }
          }

          // console.log(paras)
          document.addPasteContent(paras, false);

          // add empty paragraph at end
          // for (let i = 0; i < INSERT_FILE_END_EMPTY_PARA; i++) {
          //   document.addToContent(document.selection.startPos + insertParaCount + 2,
          // new Paragraph(document, document));
          // }
        }
      }
    }

    document.tableManager.add(table, table.getTableName());
  }

  public static tTraverseTable(rootNode: rtNode, document: Document, ContentControlDescObj: IContentControlDescObj,
                               uniqueImagelist: Map<string, IUniqueImageProps>, bInsertFile: boolean = false,
                               paras: DocumentContentElementBase[], bNoEndPara: boolean = false,
                               headerFooter: HeaderFooter = null, parent: Document | Region = null,
                               index: number, nodes: (string | number | rtNode)[]): void {
    const table = new Table(document, document);
    const tableChildren = rootNode.children;
    let rowIndex = 0;

    const colSizes: number[] = [];
    const cellMargins = { left: 1, right: 1, top: 1, bottom: 1 };

    const nameAttr = rootNode.attributes['name'];
    let tableName = null;
    if (nameAttr != null) {
      // when open file, it must have unique table name! n
      tableName = nameAttr;
    }
    if (bInsertFile === true || tableName == null) {
      // need to check dup name when inserting
      if (tableName == null) {
        // tslint:disable-next-line: no-console
        console.warn('table name is null in reader, will fetch a new unique name');
      }
      tableName = document.tableManager.getUniqueTableName(tableName);
    }

    // set table name
    table.setTableName(tableName);

    // let cellWidthDefault = 0; // cellwidth must equal colWidth when auto and cols are with same width?
    for (const tableChild of tableChildren) {
      if (typeof tableChild === 'object') {

        if (tableChild.tagName === 'w:tblPr') {
          this.parseTableProperty(table, tableChild.children, cellMargins);
          // const prChildren = tableChild.children;
          // for (const prChild of prChildren) {
          //   if (typeof prChild === 'object') {
          //     const nodeName = prChild.tagName;
          //     switch (nodeName) {
          //       case 'fixedRowHeight': {
          //         // set fixed row height
          //         table.setFixedRowHeight(true);
          //         break;
          //       }
          //       case 'fixedColWidth': {
          //         // set fixed col width
          //         table.setFixedColWidth(true);
          //         break;
          //       }
          //       case 'addRowProtect': {
          //         table.setCanAddRow(false);
          //         break;
          //       }
          //       case 'delRowProtect': {
          //         table.setCanDeleteRow(false);
          //         break;
          //       }
          //       case 'deleteProtect': {
          //         table.setDeleteProtect(true);
          //         break;
          //       }
          //       case 'editProtect': {
          //         table.setTableReadOnly(true);
          //         break;
          //       }
          //       case 'cellLeft': {
          //         cellMargins.left = +(prChild.children[0] as string);
          //         break;
          //       }
          //       case 'cellRight': {
          //         cellMargins.right = +(prChild.children[0] as string);
          //         break;
          //       }
          //       case 'cellTop': {
          //         cellMargins.top = +(prChild.children[0] as string);
          //         break;
          //       }
          //       case 'cellBottom': {
          //         cellMargins.bottom = +(prChild.children[0] as string);
          //         break;
          //       }
          //       case 'customProperty': {
          //         const customProperties = prChild.children;
          //         if (customProperties.length > 0) { // usually if in this block, length must be > 0

          //           const customPropertyArr = tReadCustomProperties(customProperties);
          //           // console.log(customPropertyArr)
          //           table.addCustomProps(customPropertyArr);
          //         }
          //         break;
          //       }
          //       default: {
          //         break;
          //       }
          //     }
          //   }

          // }
        } else if (tableChild.tagName === 'w:tblGrid') {
          const colChildren = tableChild.children;

          for (const colChild of colChildren) {
            if (typeof colChild === 'object') {
              if (colChild.tagName === 'w:gridCol') {
                const colWidth = +colChild.attributes['w:w'];
                if (!isNaN(colWidth)) {
                  colSizes.push(colWidth);
                  // cellWidthDefault = colWidth;
                } else {
                  colSizes.push(100); // default val
                }
              }
            }

          }
        } else if (tableChild.tagName === 'w:tr') {
          // add empty row first
          table.addRow(rowIndex, 0, false);
          const row = table.getRow(rowIndex);

          // may have several tc
          const rowChildren = tableChild.children;
          let cellIndex = 0;

          for (const rowChild of rowChildren) {
            if (typeof rowChild === 'object') {
              if (rowChild.tagName === 'w:trPr') {
                this.parseTableRowProperty(row, rowChild.children);
              } else if (rowChild.tagName === 'w:tc') {
                row.addCell(cellIndex, row, null, false);
                const cellChildren = rowChild.children;

                let pCount = 0;

                // let isCellBorderDirty = false;
                for (let i = 0, len = cellChildren.length; i < len; i++) {
                  const cellChild = cellChildren[i];
                  const cell = row.getCell(cellIndex);
                  if (typeof cellChild === 'object') {
                    if (cellChild.tagName === 'w:tcPr') {
                      this.parseTableCellProperty(cell, cellChild.children, rowIndex, cellIndex);
                    } else if (cellChild.tagName === 'w:p') {
                      if (pCount++ === 0) {
                        // remove empty para initialized in creating tablecell if true para is reached
                        cell.content.removeDefaultContent();
                      }
                      ParagraphReader.tTraverseParagraph(cellChild, document, ContentControlDescObj,
                        uniqueImagelist, bInsertFile, paras, bNoEndPara, cell, headerFooter, i, cellChildren);
                    }
                  }
                }


                cellIndex++;
              }
            }

          }
          rowIndex++;
        }
      }

    }

    const { left, right, top, bottom } = cellMargins;
    const { left: leftDefault, right: rightDefault,
      top: topDefault, bottom: bottomDefault } = TABLE_PROPS_DEFAULT.tableCellMargins;
    if (left !== leftDefault * 10 || right !== rightDefault * 10 ||
      top !== topDefault * 10 || bottom !== bottomDefault * 10) {
      table.setTableCellMargin(left, right, top, bottom);
    }

    // console.log(colSizes)
    // table.setColumnsWidth(colSizes); // this can be used after recalc()
    table.setTableGrid(colSizes, true);

    // console.log(table)

    if (bInsertFile === false) {
      if (headerFooter == null) {
        // main doc & region
        // document.addToContent(document.content.length, table, true);
        if (parent) {
          if (parent instanceof Document) {
            parent.addToContent(document.content.length, table, true);
          } else if (parent instanceof Region) {
            parent.addToContent(parent.getContent().length, table, true);
          }
        }
      } else {
        const headerFooterOperateContent = headerFooter.getContent();
        const headerFooterContent = headerFooter.getTrueContent();
        // if (headerFooterContent.length === 1 && headerFooterContent[0].isEmpty() === true) { // only exist empty para
        //   // headerFooterContent = [];
        //   // remove empty para if there exists true para
        //   headerFooterContent.splice(headerFooterContent.length - 1, 1);
        // }
        headerFooterOperateContent.addToContent(headerFooterContent.length, table, true);
      }
    } else { // insert file
      if (parent && parent instanceof Region) {
        parent.addToContent(parent.getContent().length, table, true);
      } else {
        paras.push(table);

        const nextNode = nodes[index + 1];
        if ( nextNode && typeof nextNode === 'object' && nextNode.tagName === 'w:sectPr') {
          // TODO: always same lvl as sectpr? no! headerfooter insert file with region
          // add empty paras when required
          if (!bNoEndPara) {
            for (let i = 0; i < INSERT_FILE_END_EMPTY_PARA + 1; i++) {
              paras.push(new Paragraph(document, document));
            }
          }

          // console.log(paras)
          document.addPasteContent(paras, false);

          // add empty paragraph at end
          // for (let i = 0; i < INSERT_FILE_END_EMPTY_PARA; i++) {
          //   document.addToContent(document.selection.startPos + insertParaCount + 2,
          // new Paragraph(document, document));
          // }
        }
      }
    }

    document.tableManager.add(table, table.getTableName());
  }

  public static tTraverseTable2(rootNode: rtNode, document: Document, ContentControlDescObj: IContentControlDescObj,
                                uniqueImagelist: Map<string, IUniqueImageProps>, bInsertFile: boolean = false,
                                paras: DocumentContentElementBase[],
                                headerFooter: HeaderFooter = null, parent: Document | Region = null,
                                modeFonts: IModeFonts = null, sectionEnds: any[] = []): void {
    const table = new Table(document, document);
    const tableChildren = rootNode.children;
    let rowIndex = 0;

    const colSizes: number[] = [];
    const cellMargins = { left: 1, right: 1, top: 1, bottom: 1 };

    const documentVersion = document != null ? document.getDocumentVersion() : 0;

    const nameAttr = rootNode.attributes['name'];
    let tableName = null;
    if (nameAttr != null) {
      // when open file, it must have unique table name! n
      tableName = nameAttr;
    }
    if (bInsertFile === true || tableName == null) {
      // need to check dup name when inserting
      if (tableName == null) {
        // tslint:disable-next-line: no-console
        console.warn('table name is null in reader, will fetch a new unique name');
      }
      tableName = document.tableManager.getUniqueTableName(tableName);
    }

    // set table name
    table.setTableName(tableName);
    const bNewVersion = FILE_HEADER_VERSION2 === documentVersion;

    // let cellWidthDefault = 0; // cellwidth must equal colWidth when auto and cols are with same width?
    for (const tableChild of tableChildren) {
      if (typeof tableChild === 'object') {

        if (tableChild.tagName === 'w:tblPr') {
          this.parseTableProperty(table, tableChild.children, cellMargins, documentVersion);
          // const prChildren = tableChild.children;
          // for (const prChild of prChildren) {
          //   if (typeof prChild === 'object') {
          //     const nodeName = prChild.tagName;
          //     switch (nodeName) {
          //       case 'fixedRowHeight': {
          //         // set fixed row height
          //         table.setFixedRowHeight(true);
          //         break;
          //       }
          //       case 'fixedColWidth': {
          //         // set fixed col width
          //         table.setFixedColWidth(true);
          //         break;
          //       }
          //       case 'addRowProtect': {
          //         table.setCanAddRow(false);
          //         break;
          //       }
          //       case 'delRowProtect': {
          //         table.setCanDeleteRow(false);
          //         break;
          //       }
          //       case 'deleteProtect': {
          //         table.setDeleteProtect(true);
          //         break;
          //       }
          //       case 'editProtect': {
          //         table.setTableReadOnly(true);
          //         break;
          //       }
          //       case 'cellLeft': {
          //         cellMargins.left = +(prChild.children[0] as string);
          //         break;
          //       }
          //       case 'cellRight': {
          //         cellMargins.right = +(prChild.children[0] as string);
          //         break;
          //       }
          //       case 'cellTop': {
          //         cellMargins.top = +(prChild.children[0] as string);
          //         break;
          //       }
          //       case 'cellBottom': {
          //         cellMargins.bottom = +(prChild.children[0] as string);
          //         break;
          //       }
          //       case 'customProperty': {
          //         const customProperties = prChild.children;
          //         if (customProperties.length > 0) { // usually if in this block, length must be > 0

          //           const customPropertyArr = tReadCustomProperties(customProperties, documentVersion);
          //           // console.log(customPropertyArr)
          //           table.addCustomProps(customPropertyArr);
          //         }
          //         break;
          //       }
          //       default: {
          //         break;
          //       }
          //     }
          //   }

          // }

        } else if (tableChild.tagName === 'w:tblGrid') {
          const colChildren = tableChild.children;

          for (const colChild of colChildren) {
            if (typeof colChild === 'object') {
              if (colChild.tagName === 'w:gridCol') {
                const colWidth = +colChild.attributes['w:w'];
                if (!isNaN(colWidth)) {
                  colSizes.push(colWidth);
                  // cellWidthDefault = colWidth;
                } else {
                  colSizes.push(100); // default val
                }
              }
            }

          }
        } else if (tableChild.tagName === 'w:tr') {
          // add empty row first
          table.addRow(rowIndex, 0, false);
          const row = table.getRow(rowIndex);

          // may have several tc
          const rowChildren = tableChild.children;
          let cellIndex = 0;

          for (const rowChild of rowChildren) {
            if (typeof rowChild === 'object') {
              if (rowChild.tagName === 'w:trPr') {
                this.parseTableRowProperty(row, rowChild.children);
              } else if (rowChild.tagName === 'w:tc') {
                row.addCell(cellIndex, row, null, false);
                const cellChildren = rowChild.children;

                let pCount = 0;

                // let isCellBorderDirty = false;
                for (let i = 0, len = cellChildren.length; i < len; i++) {
                  const cellChild = cellChildren[i];
                  const cell = row.getCell(cellIndex);
                  if (typeof cellChild === 'object') {
                    if (cellChild.tagName === 'w:tcPr') {
                      this.parseTableCellProperty(cell, cellChild.children, rowIndex, cellIndex);
                    } else if (cellChild.tagName === 'w:p') {
                      if (pCount++ === 0) {
                        // remove empty para initialized in creating tablecell if true para is reached
                        cell.content.removeDefaultContent();
                      }

                      if (bNewVersion) {
                        ParagraphReader.tTraverseParagraphForSec(cellChild, document, ContentControlDescObj,
                          uniqueImagelist, bInsertFile, paras, cell, headerFooter, modeFonts);
                      } else {
                        ParagraphReader.tTraverseParagraph2(cellChild, document, ContentControlDescObj,
                          uniqueImagelist, bInsertFile, paras, cell, headerFooter,
                          modeFonts, sectionEnds);
                      }
                    }
                  }
                }


                cellIndex++;
              }
            }

          }
          rowIndex++;
        }
      }

    }

    const { left, right, top, bottom } = cellMargins;
    const { left: leftDefault, right: rightDefault,
      top: topDefault, bottom: bottomDefault } = TABLE_PROPS_DEFAULT.tableCellMargins;
    if (left !== leftDefault * 10 || right !== rightDefault * 10 ||
      top !== topDefault * 10 || bottom !== bottomDefault * 10) {
      table.setTableCellMargin(left, right, top, bottom);
    }

    // console.log(colSizes)
    // table.setColumnsWidth(colSizes); // this can be used after recalc()
    table.setTableGrid(colSizes, true);

    // console.log(table)
    table.setEnableRowNum();
    if (bInsertFile === false) {
      if (headerFooter == null) {
        // main doc & region
        // document.addToContent(document.content.length, table, true);
        if (parent) {
          if (parent instanceof Document) {
            parent.addToContent(document.content.length, table, true);
          } else if (parent instanceof Region) {
            parent.addToContent(parent.getContent().length, table, true);
          }
        }
      } else {
        const headerFooterOperateContent = headerFooter.getContent();
        const headerFooterContent = headerFooter.getTrueContent();
        // if (headerFooterContent.length === 1 && headerFooterContent[0].isEmpty() === true) { // only exist empty para
        //   // headerFooterContent = [];
        //   // remove empty para if there exists true para
        //   headerFooterContent.splice(headerFooterContent.length - 1, 1);
        // }
        headerFooterOperateContent.addToContent(headerFooterContent.length, table, true);
      }
    } else { // insert file
      if (parent && parent instanceof Region) {
        parent.addToContent(parent.getContent().length, table, true);
      } else {
        paras.push(table);

        // const nextNode = nodes[index + 1];
        // if (nextNode && typeof nextNode === 'object' && nextNode.tagName === 'w:sectPr') {
        //   // TODO: always same lvl as sectpr? no! headerfooter insert file with region
        //   // add empty paras when required
        //   if (!bNoEndPara) {
        //     for (let i = 0; i < INSERT_FILE_END_EMPTY_PARA + 1; i++) {
        //       paras.push(new Paragraph(document, document));
        //     }
        //   }

        //   // console.log(paras)
        //   document.addPasteContent(paras, false);

        //   // add empty paragraph at end
        //   // for (let i = 0; i < INSERT_FILE_END_EMPTY_PARA; i++) {
        //   //   document.addToContent(document.selection.startPos + insertParaCount + 2,
        //   // new Paragraph(document, document));
        //   // }
        // }
      }
    }

    document.tableManager.add(table, table.getTableName());
  }

  public static tTraverseNISTable(
    rootNode: rtNode, document: Document, ContentControlDescObj: IContentControlDescObj,
    uniqueImagelist: Map<string, IUniqueImageProps>, bInsertFile: boolean = false,
    paras: DocumentContentElementBase[],
    headerFooter: HeaderFooter = null, parent: Document | Region = null,
    modeFonts: IModeFonts = null, sectionEnds: any[] = [], rowsJson: any[] = null
  ): void {
    const table = new NISTable(document, document);
    const tableChildren = rootNode.children;
    let rowIndex = 0;

    const colSizes: number[] = [];
    const cellMargins = { left: 1, right: 1, top: 1, bottom: 1 };

    const documentVersion = document != null ? document.getDocumentVersion() : 0;

    const nameAttr = rootNode.attributes['name'];
    let tableName = null;
    if (nameAttr != null) {
      // when open file, it must have unique table name! n
      tableName = nameAttr;
    }
    if (bInsertFile === true || tableName == null) {
      // need to check dup name when inserting
      if (tableName == null) {
        // tslint:disable-next-line: no-console
        console.warn('table name is null in reader, will fetch a new unique name');
      }
      tableName = document.tableManager.getUniqueTableName(tableName);
    }

    // set table name
    table.setTableName(tableName);
    const bNewVersion = FILE_HEADER_VERSION2 === documentVersion;

    let colIDs;
    // let cellWidthDefault = 0; // cellwidth must equal colWidth when auto and cols are with same width?
    for (const tableChild of tableChildren) {
      if (typeof tableChild === 'object') {

        if (tableChild.tagName === 'w:tblPr') {
          this.parseTableProperty(table, tableChild.children, cellMargins, documentVersion);
          colIDs = table.property.getNISTableColIDs();
          // const prChildren = tableChild.children;
          // for (const prChild of prChildren) {
          //   if (typeof prChild === 'object') {
          //     const nodeName = prChild.tagName;
          //     switch (nodeName) {
          //       case 'fixedRowHeight': {
          //         // set fixed row height
          //         table.setFixedRowHeight(true);
          //         break;
          //       }
          //       case 'fixedColWidth': {
          //         // set fixed col width
          //         table.setFixedColWidth(true);
          //         break;
          //       }
          //       case 'addRowProtect': {
          //         table.setCanAddRow(false);
          //         break;
          //       }
          //       case 'delRowProtect': {
          //         table.setCanDeleteRow(false);
          //         break;
          //       }
          //       case 'deleteProtect': {
          //         table.setDeleteProtect(true);
          //         break;
          //       }
          //       case 'editProtect': {
          //         table.setTableReadOnly(true);
          //         break;
          //       }
          //       case 'cellLeft': {
          //         cellMargins.left = +(prChild.children[0] as string);
          //         break;
          //       }
          //       case 'cellRight': {
          //         cellMargins.right = +(prChild.children[0] as string);
          //         break;
          //       }
          //       case 'cellTop': {
          //         cellMargins.top = +(prChild.children[0] as string);
          //         break;
          //       }
          //       case 'cellBottom': {
          //         cellMargins.bottom = +(prChild.children[0] as string);
          //         break;
          //       }
          //       case 'customProperty': {
          //         const customProperties = prChild.children;
          //         if (customProperties.length > 0) { // usually if in this block, length must be > 0

          //           const customPropertyArr = tReadCustomProperties(customProperties, documentVersion);
          //           // console.log(customPropertyArr)
          //           table.addCustomProps(customPropertyArr);
          //         }
          //         break;
          //       }
          //       default: {
          //         break;
          //       }
          //     }
          //   }

          // }

        } else if (tableChild.tagName === 'w:tblGrid') {
          const colChildren = tableChild.children;

          for (const colChild of colChildren) {
            if (typeof colChild === 'object') {
              if (colChild.tagName === 'w:gridCol') {
                const colWidth = +colChild.attributes['w:w'];
                if (!isNaN(colWidth)) {
                  colSizes.push(colWidth);
                  // cellWidthDefault = colWidth;
                } else {
                  colSizes.push(100); // default val
                }
              }
            }

          }
        } else if (tableChild.tagName === 'w:tr') {
          // add empty row first
          table.addRow(rowIndex, 0, false);
          const row = table.getRow(rowIndex);

          // may have several tc
          const rowChildren = tableChild.children;
          let cellIndex = 0;
          let cellsJson;

          for (const rowChild of rowChildren) {
            if (typeof rowChild === 'object') {
              if (rowChild.tagName === 'w:trPr') {
                this.parseTableRowProperty(row, rowChild.children);

                if (rowsJson?.length) {
                  for (let index = 0, length = rowsJson.length; index < length; index++) {
                    const element = rowsJson[index];
                    if (row.getNISRowID() === element['rowID']) {
                      cellsJson = element;
                    }
                  }
                }
              } else if (rowChild.tagName === 'w:tc') {
                row.addCell(cellIndex, row, null, false);
                const cellChildren = rowChild.children;

                let pCount = 0;
                let cell;
                let cellContent = null;

                // let isCellBorderDirty = false;
                for (let i = 0, len = cellChildren.length; i < len; i++) {
                  const cellChild = cellChildren[i];
                  cell = row.getCell(cellIndex);
                  if (typeof cellChild === 'object') {
                    if (cellChild.tagName === 'w:tcPr') {
                      this.parseTableCellProperty(cell, cellChild.children, rowIndex, cellIndex, documentVersion);

                      if (cellsJson && cellsJson.hasOwnProperty(colIDs[cellIndex])) {
                        cellContent = cellsJson[colIDs[cellIndex]];
                      }
                    } else if (cellChild.tagName === 'w:p') {
                      if (pCount++ === 0) {
                        // remove empty para initialized in creating tablecell if true para is reached
                        cell.content.removeDefaultContent();
                      }

                      if (bNewVersion) {
                        ParagraphReader.tTraverseParagraphForSec(cellChild, document, ContentControlDescObj,
                          uniqueImagelist, bInsertFile, paras, cell, headerFooter, modeFonts);
                        if (null !== cellContent) {
                          ParagraphReader.setNISCellContent(cell, cellContent, modeFonts);
                          // ParagraphReader.setNISCellContent(cellChild, cellContent, document, 
                          //       bInsertFile, paras, cell, modeFonts);
                        }
                      } else {
                        ParagraphReader.tTraverseParagraph2(cellChild, document, ContentControlDescObj,
                          uniqueImagelist, bInsertFile, paras, cell, headerFooter,
                          modeFonts, sectionEnds);
                      }
                    }
                  }
                }

                cellIndex++;
              }
            }

          }
          rowIndex++;
        }
      }

    }

    const { left, right, top, bottom } = cellMargins;
    const { left: leftDefault, right: rightDefault,
      top: topDefault, bottom: bottomDefault } = TABLE_PROPS_DEFAULT.tableCellMargins;
    if (left !== leftDefault * 10 || right !== rightDefault * 10 ||
      top !== topDefault * 10 || bottom !== bottomDefault * 10) {
      table.setTableCellMargin(left, right, top, bottom);
    }

    // console.log(colSizes)
    // table.setColumnsWidth(colSizes); // this can be used after recalc()
    table.setTableGrid(colSizes);

    // console.log(table)

    if (bInsertFile === false) {
      if (headerFooter == null) {
        // main doc & region
        // document.addToContent(document.content.length, table, true);
        if (parent) {
          if (parent instanceof Document) {
            parent.addToContent(document.content.length, table, true);
          } else if (parent instanceof Region) {
            parent.addToContent(parent.getContent().length, table, true);
          }
        }
      } else {
        const headerFooterOperateContent = headerFooter.getContent();
        const headerFooterContent = headerFooter.getTrueContent();
        // if (headerFooterContent.length === 1 && headerFooterContent[0].isEmpty() === true) { // only exist empty para
        //   // headerFooterContent = [];
        //   // remove empty para if there exists true para
        //   headerFooterContent.splice(headerFooterContent.length - 1, 1);
        // }
        headerFooterOperateContent.addToContent(headerFooterContent.length, table, true);
      }
    } else { // insert file
      if (parent && parent instanceof Region) {
        parent.addToContent(parent.getContent().length, table, true);
      } else {
        paras.push(table);

        // const nextNode = nodes[index + 1];
        // if (nextNode && typeof nextNode === 'object' && nextNode.tagName === 'w:sectPr') {
        //   // TODO: always same lvl as sectpr? no! headerfooter insert file with region
        //   // add empty paras when required
        //   if (!bNoEndPara) {
        //     for (let i = 0; i < INSERT_FILE_END_EMPTY_PARA + 1; i++) {
        //       paras.push(new Paragraph(document, document));
        //     }
        //   }

        //   // console.log(paras)
        //   document.addPasteContent(paras, false);

        //   // add empty paragraph at end
        //   // for (let i = 0; i < INSERT_FILE_END_EMPTY_PARA; i++) {
        //   //   document.addToContent(document.selection.startPos + insertParaCount + 2,
        //   // new Paragraph(document, document));
        //   // }
        // }
      }
    }

    document.tableManager.add(table, table.getTableName());
  }

  public static traverseTableForInsertFile3(
    rootNode: rtNode, insertFileContent: IInsertFileContent, ContentControlDescObj: IContentControlDescObj,
    uniqueImagelist: Map<string, IUniqueImageProps>,
    headerFooter: HeaderFooter = null, parent: Document | Region = null, sectionEnds: any[] = []
  ): void {
    const table = new Table(null, insertFileContent.logicDocument);
    const tableChildren = rootNode.children;
    let rowIndex = 0;

    const colSizes: number[] = [];
    const cellMargins = { left: 1, right: 1, top: 1, bottom: 1 };

    const nameAttr = rootNode.attributes['name'];
    let tableName = null;
    if (nameAttr != null) {
      // when open file, it must have unique table name! n
      tableName = nameAttr;
    }

    tableName = insertFileContent.tableManager.getUniqueTableName(tableName);

    // set table name
    table.tableName = tableName;
    const bNewVersion = FILE_HEADER_VERSION2 === insertFileContent.documentVersion;

    // let cellWidthDefault = 0; // cellwidth must equal colWidth when auto and cols are with same width?
    for (const tableChild of tableChildren) {
      if (typeof tableChild === 'object') {

        if (tableChild.tagName === 'w:tblPr') {
          this.parseTableProperty(table, tableChild.children, cellMargins, insertFileContent.documentVersion);
        } else if (tableChild.tagName === 'w:tblGrid') {
          const colChildren = tableChild.children;

          for (const colChild of colChildren) {
            if (typeof colChild === 'object') {
              if (colChild.tagName === 'w:gridCol') {
                const colWidth = +colChild.attributes['w:w'];
                if (!isNaN(colWidth)) {
                  colSizes.push(colWidth);
                  // cellWidthDefault = colWidth;
                } else {
                  colSizes.push(100); // default val
                }
              }
            }

          }
        } else if (tableChild.tagName === 'w:tr') {
          // add empty row first
          table.addRow(rowIndex, 0, false);
          const row = table.getRow(rowIndex);

          // may have several tc
          const rowChildren = tableChild.children;
          let cellIndex = 0;

          for (const rowChild of rowChildren) {
            if (typeof rowChild === 'object') {
              if (rowChild.tagName === 'w:trPr') {
                this.parseTableRowProperty(row, rowChild.children);
              } else if (rowChild.tagName === 'w:tc') {
                row.addCell(cellIndex, row, null, false);
                const cellChildren = rowChild.children;

                let pCount = 0;

                // let isCellBorderDirty = false;
                for (let i = 0, len = cellChildren.length; i < len; i++) {
                  const cellChild = cellChildren[i];
                  const cell = row.getCell(cellIndex);
                  if (typeof cellChild === 'object') {
                    if (cellChild.tagName === 'w:tcPr') {
                      this.parseTableCellProperty(cell, cellChild.children, rowIndex, cellIndex);
                    } else if (cellChild.tagName === 'w:p') {
                      if (pCount++ === 0) {
                        // remove empty para initialized in creating tablecell if true para is reached
                        cell.content.removeDefaultContent();
                      }

                      if (bNewVersion) {
                        ParagraphReader.traverseParagraphForInsertFileSec(cellChild, insertFileContent,
                          ContentControlDescObj, uniqueImagelist, cell);
                      } else {
                        ParagraphReader.traverseParagraphForInsertFile3(cellChild, insertFileContent,
                          ContentControlDescObj, uniqueImagelist, cell, headerFooter, sectionEnds);
                      }
                    }
                  }
                }

                cellIndex++;
              }
            }
          }
          rowIndex++;
        }
      }

    }

    const { left, right, top, bottom } = cellMargins;
    const { left: leftDefault, right: rightDefault,
      top: topDefault, bottom: bottomDefault } = TABLE_PROPS_DEFAULT.tableCellMargins;
    if (left !== leftDefault * 10 || right !== rightDefault * 10 ||
      top !== topDefault * 10 || bottom !== bottomDefault * 10) {
      table.setTableCellMargin(left, right, top, bottom);
    }

    // console.log(colSizes)
    // table.setColumnsWidth(colSizes); // this can be used after recalc()
    table.setTableGrid(colSizes, true);

    // console.log(table)
    table.setEnableRowNum();
    if (parent && parent instanceof Region) {
      parent.addToContent(parent.getContent().length, table, true);
    } else {
      // paras.push(table);
      insertFileContent.content.push(table);
    }

    insertFileContent.tableManager.addInsertTable(table);
  }

  public static traverseTableForInsertFile2(
            rootNode: rtNode, insertFileContent: IInsertFileContent, ContentControlDescObj: IContentControlDescObj,
            uniqueImagelist: Map<string, IUniqueImageProps>, bInsertFile: boolean = false,
            parent: Document | Region = null
  ): void {
    const table = new Table(null, insertFileContent.logicDocument);
    const tableChildren = rootNode.children;
    let rowIndex = 0;

    const colSizes: number[] = [];
    const cellMargins = { left: 1, right: 1, top: 1, bottom: 1 };

    const nameAttr = rootNode.attributes['name'];
    let tableName = null;
    if (nameAttr != null) {
      // when open file, it must have unique table name! n
      tableName = nameAttr;
    }
    if (bInsertFile === true || tableName == null) {
      // need to check dup name when inserting
      if (tableName == null) {
        // tslint:disable-next-line: no-console
        console.warn('table name is null in reader, will fetch a new unique name');
      }
      tableName = insertFileContent.tableManager.getUniqueTableName(tableName);
    }

    // set table name
    table.setTableName(tableName);
    const bNewVersion = FILE_HEADER_VERSION2 === insertFileContent.documentVersion;

    // let cellWidthDefault = 0; // cellwidth must equal colWidth when auto and cols are with same width?
    for (const tableChild of tableChildren) {
      if (typeof tableChild === 'object') {

        if (tableChild.tagName === 'w:tblPr') {
          this.parseTableProperty(table, tableChild.children, cellMargins);
        } else if (tableChild.tagName === 'w:tblGrid') {
          const colChildren = tableChild.children;

          for (const colChild of colChildren) {
            if (typeof colChild === 'object') {
              if (colChild.tagName === 'w:gridCol') {
                const colWidth = +colChild.attributes['w:w'];
                if (!isNaN(colWidth)) {
                  colSizes.push(colWidth);
                  // cellWidthDefault = colWidth;
                } else {
                  colSizes.push(100); // default val
                }
              }
            }

          }
        } else if (tableChild.tagName === 'w:tr') {
          // add empty row first
          table.addRow(rowIndex, 0, false);
          const row = table.getRow(rowIndex);

          // may have several tc
          const rowChildren = tableChild.children;
          let cellIndex = 0;

          for (const rowChild of rowChildren) {
            if (typeof rowChild === 'object') {
              if (rowChild.tagName === 'w:trPr') {
                this.parseTableRowProperty(row, rowChild.children);
              } else if (rowChild.tagName === 'w:tc') {
                row.addCell(cellIndex, row, null, false);
                const cellChildren = rowChild.children;

                let pCount = 0;

                // let isCellBorderDirty = false;
                for (let i = 0, len = cellChildren.length; i < len; i++) {
                  const cellChild = cellChildren[i];
                  const cell = row.getCell(cellIndex);
                  if (typeof cellChild === 'object') {
                    if (cellChild.tagName === 'w:tcPr') {
                      this.parseTableCellProperty(cell, cellChild.children, rowIndex, cellIndex);
                    } else if (cellChild.tagName === 'w:p') {
                      if (pCount++ === 0) {
                        // remove empty para initialized in creating tablecell if true para is reached
                        cell.content.removeDefaultContent();
                      }

                      if (bNewVersion) {
                        ParagraphReader.traverseParagraphForInsertFileSec(cellChild, insertFileContent,
                          ContentControlDescObj, uniqueImagelist, cell);
                      } else {
                        ParagraphReader.traverseParagraphForInsertFile2(cellChild, insertFileContent,
                          ContentControlDescObj, uniqueImagelist, true, cell);
                      }
                    }
                  }
                }


                cellIndex++;
              }
            }

          }
          rowIndex++;
        }
      }

    }

    const { left, right, top, bottom } = cellMargins;
    const { left: leftDefault, right: rightDefault,
      top: topDefault, bottom: bottomDefault } = TABLE_PROPS_DEFAULT.tableCellMargins;
    if (left !== leftDefault * 10 || right !== rightDefault * 10 ||
      top !== topDefault * 10 || bottom !== bottomDefault * 10) {
      table.setTableCellMargin(left, right, top, bottom);
    }

    // console.log(colSizes)
    // table.setColumnsWidth(colSizes); // this can be used after recalc()
    table.setTableGrid(colSizes, true);

    // console.log(table)
    table.setEnableRowNum();
    if (parent && parent instanceof Region) {
      parent.addToContent(parent.getContent().length, table, true);
    } else {
      insertFileContent.content.push(table);
    }

    insertFileContent.tableManager.addInsertTable(table);
  }

  /**
   * 抽取表格属性
   * @param table 表格
   * @param prChildren xml中的表格node
   * @param cellMargins 单元格的边距
   * @param documentVersion 文档版本号
   */
  private static parseTableProperty(
    table: TableBase, prChildren: (string | number | rtNode)[], cellMargins: any, documentVersion?: number
  ): void {
    for (const prChild of prChildren) {
      if (typeof prChild === 'object') {
        const nodeName = prChild.tagName;
        switch (nodeName) {
          case 'fixedRowHeight': {
            // set fixed row height
            table.setFixedRowHeight(true);
            break;
          }
          case 'fixedColWidth': {
            // set fixed col width
            table.setFixedColWidth(true);
            break;
          }
          case 'enableRowAction': {
            // set fixed col width
            const text: any = prChild.children[0];
            if (text) {
              table.setEnableRowAction2(+text);
            }
            break;
          }
          case 'addRowProtect': {
            table.setCanAddRow(false);
            break;
          }
          case 'delRowProtect': {
            table.setCanDeleteRow(false);
            break;
          }
          case 'deleteProtect': {
            table.setDeleteProtect(true);
            break;
          }
          case 'repeatHeader': {
            table.setRepeatHeader(false);
            break;
          }
          case 'editProtect': {
            table.setTableReadOnly(true);
            break;
          }
          case 'cellGridLines': {
            const text = (prChild.children[0] as string);
            if (text) {
              try {
                (table as any).setCellGridLines(JSON.parse(text.replace(/&quot;/g, '"')));
              } catch (error) {
                console.warn(error);
              }
            }
            
            // set fixed row height
            
            break;
          }
          case 'cellLeft': {
            cellMargins.left = +(prChild.children[0] as string);
            break;
          }
          case 'cellRight': {
            cellMargins.right = +(prChild.children[0] as string);
            break;
          }
          case 'cellTop': {
            cellMargins.top = +(prChild.children[0] as string);
            break;
          }
          case 'cellBottom': {
            cellMargins.bottom = +(prChild.children[0] as string);
            break;
          }
          case 'headerReadOnly': {
            const headerReadOnly = ('1' === prChild.children[0] ? true : false);
            table.setHeaderReadOnly(headerReadOnly);
            break;
          }
          case 'bPrintEmptyRow': {
            const curText: string = prChild.children[0] as string;
            table.setPrintEmptyRow(curText === 'true');
            break;
          }
          case 'bRepeatOnBreak': {
            const curText: string = prChild.children[0] as string;
            table.setRepeatOnBreak(curText === 'true');
            break;
          }
          case 'customProperty': {
            const customProperties = prChild.children;
            if (customProperties.length > 0) { // usually if in this block, length must be > 0

              const customPropertyArr = tReadCustomProperties(customProperties, documentVersion);
              // console.log(customPropertyArr)
              table.addCustomProps(customPropertyArr);
            }
            break;
          }
          case 'columnIDs': {
            let str = prChild.children[0] as string;
            if (!str) {
              break;
            }
            str = safeDecodeURIComponent(str, documentVersion);
            const columnIDs = str.split(',');
            table.setColumnIDs(columnIDs);
            break;
          }
          case 'fixedLeft': {
            const fixedLeft = +(prChild.children[0] as string);
            table.property.setFixedLeft(fixedLeft);
            break;
          }
          case 'fixedRight': {
            const fixedRight = +(prChild.children[0] as string);
            table.property.setFixedRight(fixedRight);
            break;
          }
          case 'bFixedHeader': {
            const bFixedHeader = ('1' === prChild.children[0] ? true : false);
            table.property.setFixedHeader(bFixedHeader);
            break;
          }
          default: {
            break;
          }
        }
      }
    }
  }

  /**
   * 抽取表格行属性
   * @param row 表格行
   * @param prChildren xml中的表格行node
   */
  private static parseTableRowProperty(row: TableRow, trPropChildren: (string | number | rtNode)[]): void {
    for (const trPropChild of trPropChildren) {
      if (typeof trPropChild === 'object') {
        if (trPropChild.tagName === 'w:trHeight') {
          const heightVal = +trPropChild.attributes['w:val'];
          const heightType = +trPropChild.attributes['w:type'];
          row.setHeight(heightVal, heightType);
        } else if (trPropChild.tagName === 'w:tblHeader') {
          const tblHeader = trPropChild.children[0] === '1' ? true : false;
          row.setTableHeader(tblHeader);
        } else if (trPropChild.tagName === 'w:trReadonly') {
          const trReadonly = trPropChild.children[0] === '1' ? true : false;
          row.setReadOnly(trReadonly);
        } else if (trPropChild.tagName === 'w:trNis') {
          const type = trPropChild.attributes['type'];
          const rowNISProperty = row.getNISRowProperty();
          if (null != type) {
            rowNISProperty.type = +type;
          }
          this.parseTableRowNISProperty(row.getNISRowProperty(), trPropChild.children);
        }
      }
    }
  }

  /**
   * 抽取单元格属性
   * @param cell 单元格
   * @param prChildren xml中的单元格node
   * @param rowIndex 行号
   * @param cellIndex 列号
   */
  private static parseTableCellProperty(
    cell: TableCell, prChildren: (string | number | rtNode)[], rowIndex: number, cellIndex: number,
    documentVersion?: number
  ): void {
    for (const tcPrChild of prChildren) {
      if (typeof tcPrChild === 'object') {
        const nodeName = tcPrChild.tagName;

        switch (nodeName) {
          case 'w:tcW': {
            const cellWidth = +tcPrChild.attributes['w:w'];
            const cellType = +tcPrChild.attributes['w:type'];
            // console.log(cellWidth, cellType);
            if (cellWidth !== 0) { // if width = 0 , set or not no matter
              cell.setCellWidth(new TableMeasurement(cellType, cellWidth));
            }
            // else {
            //   console.log(cellWidthDefault);
            //   cellWidth = cellWidthDefault;
            //   cell.setCellWidth(new TableMeasurement(cellType, cellWidth));
            // }

            break;
          }
          case 'w:top': {
            const border = new DocumentBorder();
            const borderSize = tcPrChild.attributes['w:sz'];
            const borderColor = tcPrChild.attributes['w:color'];

            if (null != borderSize) {
              border.size = parseFloat(borderSize);
            }

            if (null != borderColor) {
              border.color = colorRgb(borderColor);
            }
            // const borderValue = tcPrChild.attributes.getNamedItem('w:sz').nodeValue;
            cell.setBorder(border, 0);
            break;
          }
          case 'w:right': {
            const border = new DocumentBorder();
            const borderSize = tcPrChild.attributes['w:sz'];
            const borderColor = tcPrChild.attributes['w:color'];

            if (null != borderSize) {
              border.size = parseFloat(borderSize);
            }

            if (null != borderColor) {
              border.color = colorRgb(borderColor);
            }
            cell.setBorder(border, 1);
            break;
          }
          case 'w:bottom': {
            const border = new DocumentBorder();
            const borderSize = tcPrChild.attributes['w:sz'];
            const borderColor = tcPrChild.attributes['w:color'];

            if (null != borderSize) {
              border.size = parseFloat(borderSize);
            }

            if (null != borderColor) {
              border.color = colorRgb(borderColor);
            }
            cell.setBorder(border, 2);
            break;
          }
          case 'w:left': {
            const border = new DocumentBorder();
            const borderSize = tcPrChild.attributes['w:sz'];
            const borderColor = tcPrChild.attributes['w:color'];

            if (null != borderSize) {
              border.size = parseFloat(borderSize);
            }

            if (null != borderColor) {
              border.color = colorRgb(borderColor);
            }
            cell.setBorder(border, 3);
            break;
          }
          case 'w:gridSpan': {
            const gridSpan = +tcPrChild.attributes['w:val'];
            // console.log(gridSpan)
            cell.setGridSpan(gridSpan);
            break;
          }
          case 'w:vMerge': {
            // const vMerge = tcPrChild.attributes.getNamedItem('w:val').nodeValue;
            // console.log(vMerge)
            cell.setVMerge(VerticalMergeType.Continue);
            break;
          }
          case 'w:protected': {
            cell.setCellProtected(true);
            break;
          }
          case 'w:cellFormula': {
            const formula = tcPrChild.attributes['w:val'];
            cell.setCellFormula(formula, rowIndex, cellIndex);
            break;
          }
          case 'w:cellVertAlign': {
            const cellVertAlign = +tcPrChild.attributes['w:val'];
            cell.setVertAlign2(cellVertAlign);
            break;
          }
          case 'w:bCellLeftSlash': {
              const bLeft = !!tcPrChild.attributes['w:val'];
              bLeft && cell.setTableCellSlash(1);
              break;
          }
          case 'w:bCellRightSlash': {
              const bRight = !!tcPrChild.attributes['w:val'];
              bRight && cell.setTableCellSlash(2);
              break;
          }
          case 'w:tcNis': {
            const cellNISProperty = cell.getNISProperty2();
            const type = tcPrChild.attributes['type'];
            if (null != type) {
              cellNISProperty.type = +type;
            }

            this.parseTableCellNISProperty(cell.getNISProperty2(), tcPrChild.children, documentVersion);
            break;
          }
          default: {
            break;
          }
        }
      }
    }
  }

  private static parseTableCellNISProperty(
    cellNISProperty: any, prChildren: (string | number | rtNode)[], documentVersion?: number
  ): void {
    for (const tcPrChild of prChildren) {
      if (typeof tcPrChild === 'object') {
        const nodeName = tcPrChild.tagName;

        switch (nodeName) {
          case 'customProperty': {
            const customProperties = tcPrChild.children;
            if (customProperties.length > 0) { // usually if in this block, length must be > 0

              const customPropertyArr = tReadCustomProperties(customProperties);
              // console.log(customPropertyArr)
              cellNISProperty.customProperty = customPropertyArr;
            }
            break;
          }
          case 'gridLine': {
            const text: string = tcPrChild.children[0] as any;
            if (text) {
              try {
                cellNISProperty.gridLine = JSON.parse(text.replace(/&quot;/g, '"'));
              } catch (error) {
                console.warn(error);
              }
            }
            
            break;
          }
          case 'serialNumber': {
            const serialNumber = tcPrChild.children[0];
            if (serialNumber !== null && serialNumber !== undefined && typeof serialNumber === 'string') {
              cellNISProperty.serialNumber = safeDecodeURIComponent(serialNumber, FILE_HEADER_VERSION2);
            }
            break;
          }
          case 'bDiagonalLine': {
            const bDiagonalLine = ('1' === tcPrChild.children[0] ? true : false);
            cellNISProperty.bDiagonalLine = bDiagonalLine;
            break;
          }
          case 'bShowCodeAndValue': {
            const bShowCodeAndValue = ('1' === tcPrChild.children[0] ? true : false);
            cellNISProperty.bShowCodeAndValue = bShowCodeAndValue;
            break;
          }
          case 'canFormularCalc': {
            const canFormularCalc = ('1' === tcPrChild.children[0] ? true : false);
            cellNISProperty.canFormularCalc = canFormularCalc;
            break;
          }

          // datebox
          case 'dateBoxFormat': {
            const dateBoxFormat = +tcPrChild.children[0];
            cellNISProperty.dateBoxFormat = dateBoxFormat;
            break;
          }
          case 'customFormat': {
            const textNode = tcPrChild.children[0];
            if (textNode != null && typeof textNode === 'string') {
                let nodeValue = textNode;
                if (nodeValue === '{}') {
                    break;
                }
                // const domParser = new DOMParser();
                // // handle unescaped string
                // const doc = domParser.parseFromString(nodeValue, 'text/html');
                // if (doc != null) {
                //     nodeValue = doc.documentElement.textContent;
                // }
                nodeValue = parseFromString(nodeValue);

                cellNISProperty.customFormat = JSON.parse(nodeValue);
            }
            break;
          }
          case 'dateTime': {
            const dateTime = tcPrChild.children[0];
            cellNISProperty.dateTime = dateTime;
            break;
          }
          case 'text': {
            const text = tcPrChild.children[0];
            cellNISProperty.text = text;
            break;
          }
          case 'time': {
            const time = new Date(tcPrChild.children[0] as string);
            cellNISProperty.time = time;
            break;
          }
          case 'hideDateText': {
            const hideDateText = ('1' === tcPrChild.children[0] ? true : false);
            cellNISProperty.hideDateText = hideDateText;
            break;
          }

          // combox/quick
          case 'listItems': {
            const listItems = tcPrChild.children;
            // console.log(listItems)
            if (listItems.length >= 0) {
                const listItemsArr = [];
                for (const listItem of listItems) {
                    if (typeof listItem === 'object') {
                        // so that find() can be used
                        let nameNode: rtNode = null;
                        let valueNode: rtNode = null;
                        let selectNode: rtNode = null;
                        for (const listItemNode of listItem.children) {
                            if (typeof listItemNode === 'object') {
                                if (listItemNode.tagName === 'name') {
                                    nameNode = listItemNode;
                                } else if (listItemNode.tagName === 'value') {
                                    valueNode = listItemNode;
                                } else if (listItemNode.tagName === 'select') {
                                    selectNode = listItemNode;
                                }
                            }
                        }

                        if (
                            null == nameNode ||
                            0 === nameNode.children.length
                        ) {
                            continue;
                        }

                        // TODO: as
                        // const name = nameNode.children[0] as string;
                        // const value = (valueNode != null && valueNode.children[0] ) ?
                        //                             valueNode.children[0] as string : '';

                        // const select = (selectNode != null && selectNode.children[0] ) ?
                        //                 (selectNode.children[0] === '1' ? true : false) : false;
                        listItemsArr.push(new CodeValueItem(
                            safeDecodeURIComponent(nameNode.children[0] as string, FILE_HEADER_VERSION2),
                            safeDecodeURIComponent(valueNode.children[0] as string, FILE_HEADER_VERSION2),
                            (selectNode.children[0] as string) === '1' ? true : false)
                        );

                        // listItemsArr.push(
                        //     new CodeValueItem(
                        //         nameNode.children[0] as string,
                        //         valueNode.children[0] as string,
                        //         (selectNode.children[0] as string) === '1'
                        //             ? true
                        //             : false
                        //     )
                        // );
                    }
                }
                cellNISProperty.items = listItemsArr;
            }
            break;
          }
          // case 'prefixContent': {
          //   const prefixContent = tcPrChild.attributes['w:val'];
          //   cellNISProperty.prefixContent = prefixContent;
          //   break;
          // }
          // case 'selectPrefixContent': {
          //   const selectPrefixContent = tcPrChild.attributes['w:val'];
          //   cellNISProperty.selectPrefixContent = selectPrefixContent;
          //   break;
          // }
          case 'separator': {
            const separator = tcPrChild.children[0];
            cellNISProperty.separator = separator;
            break;
          }
          case 'bRetrieve': {
            const bRetrieve = ('1' === tcPrChild.children[0] ? true : false);
            cellNISProperty.bRetrieve = bRetrieve;
            break;
          }
          case 'bShowValue': {
            const bShowValue = ('1' === tcPrChild.children[0] ? true : false);
            cellNISProperty.bShowValue = bShowValue;
            break;
          }
          case 'bCheckMultiple': {
            const bCheckMultiple = ('1' === tcPrChild.children[0] ? true : false);
            cellNISProperty.bCheckMultiple = bCheckMultiple;
            break;
          }
          case 'selectType': {
            const selectType = +tcPrChild.children[0];
            cellNISProperty.selectType = selectType;
            break;
          }
          case 'codeLabel': {
            const codeLabel = tcPrChild.children[0];
            cellNISProperty.codeLabel = codeLabel;
            break;
          }
          case 'valueLabel': {
            const valueLabel = tcPrChild.children[0];
            cellNISProperty.valueLabel = valueLabel;
            break;
          }

          // time
          case 'timeFormat': {
            const timeFormat = safeDecodeURIComponent(tcPrChild.children[0] as string, documentVersion) ;
            cellNISProperty.timeFormat = timeFormat;
            break;
          }
          case 'bpFormat': {
            const bpFormat = tcPrChild.children[0];
            cellNISProperty.bloodPressureFormat = bpFormat;
            break;
          }
          case 'cellText': {
            const cellText = tcPrChild.children[0];
            if (cellText) {
              cellNISProperty.cellText = cellText;
            }
            break;
          }

          // number
          case 'maxValue': {
            const maxValue = +tcPrChild.children[0];
            cellNISProperty.maxValue = maxValue;
            break;
          }
          case 'minValue': {
            const minValue = +tcPrChild.children[0];
            cellNISProperty.minValue = minValue;
            break;
          }
          case 'maxWarn': {
            const maxWarn = +tcPrChild.children[0];
            cellNISProperty.maxWarn = maxWarn;
            break;
          }
          case 'minWarn': {
            const minWarn = +tcPrChild.children[0];
            cellNISProperty.minWarn = minWarn;
            break;
          }
          case 'precision': {
            const precision = +tcPrChild.children[0];
            cellNISProperty.precision = precision;
            break;
          }
          case 'unit': {
            const unit = tcPrChild.children[0];
            cellNISProperty.unit = unit;
            break;
          }

          // format
          case 'fontFamily': {
            const fontFamily = tcPrChild.children[0];
            cellNISProperty.fontFamily = fontFamily;
            break;
          }
          case 'fontSize': {
            const fontSize = +tcPrChild.children[0];
            cellNISProperty.fontSize = fontSize;
            break;
          }
          case 'paraSpacing': {
            const paraSpacing = +tcPrChild.children[0];
            cellNISProperty.paraSpacing = paraSpacing;
            break;
          }
          case 'alignType': {
            const alignType = +tcPrChild.children[0];
            cellNISProperty.alignType = alignType;
            break;
          }
          case 'range': {
            const range = +tcPrChild.children[0];
            cellNISProperty.range = range;
            break;
          }
          default: {
            break;
          }

        }
      }
    }
    if (NISTableCellType.List === cellNISProperty.type) {
      if (cellNISProperty.separator == null) {
        cellNISProperty.separator = NIS_PROPERTY_DEFAULT.separator;
      }
      if (cellNISProperty.selectType == null) {
        cellNISProperty.selectType = NISSelectType.Dropdown;
      }
    }
    
    if (cellNISProperty.type === NISTableCellType.Date && cellNISProperty.dateBoxFormat == null) {
      cellNISProperty.dateBoxFormat = NIS_PROPERTY_DEFAULT.dateBoxFormat;
    }
  }

  private static parseTableRowNISProperty(rowNISProperty: any, prChildren: (string | number | rtNode)[]): void {
    for (const rowPrChild of prChildren) {
      if (typeof rowPrChild === 'object') {
        const nodeName = rowPrChild.tagName;

        switch (nodeName) {
          case 'customProperty': {
            const customProperties = rowPrChild.children;
            if (customProperties.length > 0) { // usually if in this block, length must be > 0

              const customPropertyArr = tReadCustomProperties(customProperties);
              // console.log(customPropertyArr)
              rowNISProperty.customProperty = customPropertyArr;
            }
            break;
          }
          case 'signStatus': {
            const signStatus = +rowPrChild.children[0];
            rowNISProperty.signStatus = signStatus;
            break;
          }
          case 'bDeleteProtect': {
            const bDeleteProtect = ('1' === rowPrChild.children[0] ? true : false);
            rowNISProperty.bDeleteProtect = bDeleteProtect;
            break;
          }
          case 'rowID': {
            const rowID = rowPrChild.children[0];
            rowNISProperty.rowID = rowID;
            break;
          }
          case 'creator': {
            const creator = rowPrChild.children[0];
            rowNISProperty.creator = creator;
            break;
          }
          // case 'type': {
          //   const rowType = +rowPrChild.children[0];
          //   rowNISProperty.type = rowType;
          //   break;
          // }
          case 'sumStatus': {
            const sumStatus = +rowPrChild.children[0];
            rowNISProperty.sumStatus = sumStatus;
            break;
          }
          case 'sumKey': {
            const sumKey = rowPrChild.children[0];
            rowNISProperty.sumKey = sumKey;
            break;
          }
          default: {
            break;
          }

        }
      }
    }
  }
}
