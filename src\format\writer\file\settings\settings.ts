import { XmlComponent, XmlAttributeComponent } from '../xml-components';
import { Info } from './info';
import { SaveHistory } from './saveHistory';
import { Properties } from './properties';
import { BaseXmlComponent, IXmlResult } from '../xml-components/base';

export interface ISettingsAttributesProperties {
  readonly w?: string;
}
export class SettingsAttributes extends XmlAttributeComponent<ISettingsAttributesProperties> {
  protected readonly xmlKeys: any = {
      w: 'xmlns:w',
  };
}

export class Settings extends XmlComponent {

  public get Root(): (BaseXmlComponent | string)[] {
    return this.root;
  }

  private readonly info: Info;
  private readonly saveHistory: SaveHistory;
  private readonly properties: Properties;

  constructor() {
    super('w:settings');
    this.root.push(
      new SettingsAttributes({
          w: 'http://schemas.openxmlformats.org/wordprocessingml/2006/main',
      }),
    );

    this.info = new Info();
    this.saveHistory = new SaveHistory();
    this.properties = new Properties();
    this.root.push(this.info);
    this.root.push(this.saveHistory);
    this.root.push(this.properties);
  }

  public setInfo(pages: number, application: string, version: number, protectMode: number): void {
    this.info.addPages(pages);
    this.info.addApplication(application);
    this.info.addVersion(version);
    this.info.addProtectMode(protectMode);
  }

  public setSaveHistory(lastModifiedBy: string,
                        lastSavedT: string,
                        LastPrintedT: string,
                        createdBy: string,
                        createTime: string,
                        lastButOne: string): void {
    this.saveHistory.addLastModifiedBy(lastModifiedBy);
    this.saveHistory.addLastSavedT(lastSavedT);
    // this.saveHistory.addLastPrintedT(LastPrintedT);
    this.saveHistory.addCreatedBy(createdBy);
    this.saveHistory.addCreatedTime(createTime);
    this.saveHistory.addLastButOne(lastButOne);
  }

  public setProperties(properties: Map<string, any>): void {
    this.properties.addCustomElements(properties);
  }

  public prepForXml(): IXmlResult {
    const result = super.prepForXml();
    const key = this.rootKey;
    const text = `<${key}${result.attrs}>${result.text}</${key}>`;
    return {
      text,
      attrs: null
    };
  }

}
