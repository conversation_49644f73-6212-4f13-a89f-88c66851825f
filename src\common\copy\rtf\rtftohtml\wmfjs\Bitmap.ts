/*

The MIT License (MIT)

Copyright (c) 2015 Thomas <PERSON>

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.

*/

import { Blo<PERSON> } from './Blob';
import { Helper, WMFJSError } from './Helper';
import { BitmapBase, BitmapCoreHeader } from './BitmapBase';
import { BitmapInfo, BitmapInfoHeader } from './BitmapInfo';

export class DIBitmap extends BitmapBase {
    // tslint:disable-next-line: variable-name
    private _reader: Blob;
    // tslint:disable-next-line: variable-name
    private _offset: number;
    // tslint:disable-next-line: variable-name
    private _size: number;
    // tslint:disable-next-line: variable-name
    private _info: BitmapInfo;

    constructor(reader: Blob, size: number) {
        super();
        this._reader = reader;
        this._offset = reader.pos;
        this._size = size;
        this._info = new BitmapInfo(reader, true);
    }

    public getWidth(): number {
        return this._info.getWidth();
    }

    public getHeight(): number {
        return this._info.getHeight();
    }

    public base64ref(): string {
        const prevpos = this._reader.pos;
        this._reader.seek(this._offset);
        let mime = 'image/bmp';
        const header = this._info.header();
        let data;
        if (header instanceof  BitmapInfoHeader && header.compression != null) {
            switch (header.compression) {
                case Helper.GDI.BitmapCompression.BI_JPEG:
                    mime = 'data:image/jpeg';
                    break;
                case Helper.GDI.BitmapCompression.BI_PNG:
                    mime = 'data:image/png';
                    break;
                default:
                    data = this.makeBitmapFileHeader();
                    break;
            }
        } else {
            data = this.makeBitmapFileHeader();
        }

        if (data != null) {
            data += this._reader.readBinary(this._size);
        } else {
            data = this._reader.readBinary(this._size);
        }

        const ref = 'data:' + mime + ';base64,' + btoa(data);
        this._reader.seek(prevpos);
        return ref;
    }

    private makeBitmapFileHeader(): string {
        const buf = new ArrayBuffer(14);
        const view = new Uint8Array(buf);
        view[0] = 0x42;
        view[1] = 0x4d;
        Helper._writeUint32Val(view, 2, this._size + 14);
        Helper._writeUint32Val(view, 10, this._info.infosize() + 14);
        return Helper._blobToBinary(view);
    }
}

export class Bitmap16 extends BitmapBase {
    public type: number;
    public width: number;
    public height: number;
    public widthBytes: number;
    public planes: number;
    public bitsPixel: number;
    public bitsOffset: number;
    public bitsSize: number;
    // tslint:disable-next-line: variable-name
    private _reader: Blob;
    // tslint:disable-next-line: variable-name
    private _offset: number;
    // tslint:disable-next-line: variable-name
    private _size: number;

    constructor(reader: Blob, size: number | Bitmap16) {
        super();
        if (reader != null) {
            size = size as number;
            this._reader = reader;
            this._offset = reader.pos;
            this._size = size;
            this.type = reader.readInt16();
            this.width = reader.readInt16();
            this.height = reader.readInt16();
            this.widthBytes = reader.readInt16();
            this.planes = reader.readUint8();
            this.bitsPixel = reader.readUint8();
            this.bitsOffset = reader.pos;
            // tslint:disable-next-line: no-bitwise
            this.bitsSize = (((this.width * this.bitsPixel + 15) >> 4) << 1) * this.height;
            if (this.bitsSize > size - 10) {
                throw new WMFJSError('Bitmap should have ' + this.bitsSize + ' bytes, but has ' + (size - 10));
            }
        } else {
            const copy = size as Bitmap16;
            this._reader = copy._reader;
            this._offset = copy._offset;
            this._size = copy._size;
            this.type = copy.type;
            this.width = copy.width;
            this.height = copy.height;
            this.widthBytes = copy.widthBytes;
            this.planes = copy.planes;
            this.bitsPixel = copy.bitsPixel;
            this.bitsOffset = copy.bitsOffset;
            this.bitsSize = copy.bitsSize;
        }
    }

    public getWidth(): number {
        return this.width;
    }

    public getHeight(): number {
        return this.height;
    }

    public clone(): Bitmap16 {
        return new Bitmap16(null, this);
    }
}

// tslint:disable-next-line: max-classes-per-file
export class PatternBitmap16 extends Bitmap16 {
    constructor(reader: Blob, size: number | Bitmap16) {
        super(reader, size);
        if (reader != null) {
            this.bitsOffset += 22; // skip bits (4 bytes) + reserved (18 bytes)
        }
    }

    public clone(): PatternBitmap16 {
        return new PatternBitmap16(null, this);
    }
}
