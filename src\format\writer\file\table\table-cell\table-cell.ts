// http://officeopenxml.com/WPtableGrid.php
import { Paragraph } from '../../paragraph';
import { XmlComponent } from '../../xml-components';

import { Table } from '../table';
import { TableCellProperties, ICellWidthProperties } from './table-cell-properties';
import { ITableCellBorder } from '../../../../../common/commonDefines';
import { IXmlResult } from '../../xml-components/base';

export class TableCell extends XmlComponent {
    private properties: TableCellProperties;

    constructor() {
        super('w:tc');
        this.initializeProperties();
    }

    public addContent(content: Paragraph | Table): TableCell {
        this.root.push(content);
        return this;
    }

    public prepForXml(): IXmlResult {
        // Cells must end with a paragraph
        const retval = super.prepForXml();
        if (!retval) {
            return undefined;
        }

        // const content = retval['w:tc'];
        // if (!content[content.length - 1]['w:p']) {
        //     content.push(new Paragraph().prepForXml());
        // }
        return retval;
    }

    public createParagraph(text?: string): Paragraph {
        const para = new Paragraph(text);
        this.addContent(para);
        return para;
    }

    public get CellProperties(): TableCellProperties {
        return this.properties;
    }

    public addGridSpan(val: number): TableCell {
        this.properties.addGridSpan(val);
        return this;
    }

    public addVMerge(val: number): TableCell {
        this.properties.addVMerge(val);
        return this;
    }

    public addCellWidth(cellWidth: ICellWidthProperties): TableCell {
        this.properties.addCellWidth(cellWidth);
        return this;
    }

    public addTopBorder(border: ITableCellBorder): TableCell {
        this.properties.addTopBorder(border);
        return this;
    }

    public addRightBorder(border: ITableCellBorder): TableCell {
        this.properties.addRightBorder(border);
        return this;
    }

    public addBottomBorder(border: ITableCellBorder): TableCell {
        this.properties.addBottomBorder(border);
        return this;
    }

    public addLeftBorder(border: ITableCellBorder): TableCell {
        this.properties.addLeftBorder(border);
        return this;
    }

    public addCellProtected(val: string): TableCell {
        this.properties.addCellProtected(val);
        return this;
    }

    public addCellFormula(val: string): TableCell {
        this.properties.addCellFormula(val);
        return this;
    }

    public addCellVertAlign(val: number): TableCell {
        this.properties.addCellVertAlign(val);
        return this;
    }

    public addCellSlash(slashType: number): TableCell {
        this.properties.addCellSlash(slashType);
        return this;
    }

    /**
     * initialize properties if not
     */
    private initializeProperties(): void {
        if (!this.properties) {
            this.properties = new TableCellProperties();

            // should be the first child element
            // this.root.push(this.properties);
            this.root.unshift(this.properties);
        }
    }
}
