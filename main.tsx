import * as React from 'react';
import { EmrEditor } from './src/components/editor/Main';
import {WasmInstance} from './src/common/WasmInstance';
import { createRoot } from 'react-dom/client';

WasmInstance.createWasmInstsanceAsync()
    .then((that: any) => {
        let href = location.search;
        if (href) {
            href = href.slice(1);
            const obj: { path?: string } = {};
            const arrs = href.split('&');
            arrs.forEach((arr) => {
                if (!arr) {
                    return;
                }
                const data = arr.split('=');
                obj[data[0]] = data[1];
            });
            if (obj.path) {
                that.setUrl(obj.path);
            }
            that.setUrl('/editor/');
        } else {
            that.setUrl('/editor/');
        }
        const container = document.getElementById('hz-editor-app');
        if (container) {
            const root = createRoot(container);
            window['__EmrEditorComponent__'] = root.render(
                <EmrEditor
                    isTest={true}
                    bShowMenu={true}
                    bShowToolbar={true}
                    textColorChangeInRevision={1}
                />
            );
        }
    });
