import * as React from 'react';
import Dialog from '../../ui/Dialog';
import { INewControlProperty, isValidName} from '../../../../common/commonDefines';
import Input from '../../ui/Input';
import Checkbox from '../../ui/CheckboxItem';
import Button from '../../ui/Button';
import NewControlCustomProps from './CustomProperty';
import { message} from '../../../../common/Message';
import { IFRAME_MANAGER } from '../../../../common/IframeManager';
import StructRegExpBtn from './StructRegExpSet';

interface IDialogProps {
    documentCore: any;
    visible: boolean;
    id: string;
    property?: INewControlProperty;
    close: (name: string | number, bRefresh?: boolean) => void;
}

interface IState {
    bRefresh: boolean;
}

export default class Region extends React.Component<IDialogProps, IState> {
    private newControl: INewControlProperty;
    private bCustomProperty: boolean;
    private visible: any;
    constructor(props: any) {
        super(props);
        this.state = {
            bRefresh: false,
        };
        this.visible = this.props.visible;
        this.newControl = {newControlName: undefined};
    }

    public render(): any {
        return (
            <Dialog
                visible={this.visible}
                width={350}
                top='15%'
                open={this.open}
                title='区域'
                footer={this.renderFooter()}
                id={this.props.id}
            >
                <div>
                    <div className='editor-line'>
                        <span className='title'>常规</span>
                    </div>
                    <div className='editor-line'>
                        <div className='w-70'>名称</div>
                        <div className='right-auto'>
                            <Input
                                name='newControlName'
                                value={this.newControl.newControlName}
                                onChange={this.onChange}
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <div className='w-70'>内部名称</div>
                        <div className='right-auto'>
                            <Input
                                name='newControlSerialNumber'
                                value={this.newControl.newControlSerialNumber}
                                onChange={this.onChange}
                                readonly={true}
                                placeholder={''}
                                disabled={true}
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <div className='w-70'>标识符</div>
                        <div className='right-auto'>
                            <Input
                                name='identifier'
                                value={this.newControl.identifier}
                                onChange={this.onChange}
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <div className='w-70'>提示符</div>
                        <div className='right-auto'>
                            <Input
                                name='newControlInfo'
                                value={this.newControl.newControlInfo}
                                onChange={this.onChange}
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <div className='w-70'>
                            <Checkbox
                                name='showTitle'
                                disabled={false}
                                value={this.newControl.showTitle}
                                onChange={this.onTitleChange}
                            >
                                标题
                            </Checkbox>
                        </div>
                        <div className='right-auto'>
                            <Input
                                name='newControlTitle'
                                disabled={!this.newControl.showTitle}
                                value={this.newControl.newControlTitle}
                                onChange={this.onChange}
                            />
                        </div>
                    </div>
                    <div className='editor-line editor-multi-line'>
                        <span className='title w-70'>属性</span>
                        <div className='right-auto newcontrol-prop'>
                            <div className='editor-line'>
                                <div className='w-050'>
                                    <Checkbox
                                        name='isNewControlHidden'
                                        disabled={false}
                                        value={this.newControl.isNewControlHidden}
                                        onChange={this.onChange}
                                    >
                                        隐藏
                                    </Checkbox>
                                </div>
                                <div className='w-050'>
                                    <Checkbox
                                        name='isNewControlCanntDelete'
                                        value={this.newControl.isNewControlCanntDelete}
                                        onChange={this.onChange}
                                    >
                                        禁止删除
                                    </Checkbox>
                                </div>
                            </div>

                            <div className='editor-line'>
                                <div className='w-050'>
                                    <Checkbox
                                        name='isNewControlCanntEdit'
                                        value={this.newControl.isNewControlCanntEdit}
                                        onChange={this.onChange}
                                    >
                                        禁止编辑
                                    </Checkbox>
                                </div>
                                <div className='w-050'>
                                    <Checkbox
                                        name='isNewControlReverseEdit'
                                        value={this.newControl.isNewControlReverseEdit}
                                        onChange={this.onChange}
                                    >
                                        反向编辑
                                    </Checkbox>
                                </div>
                            </div>
                            
                            <div className='editor-line'>
                                <div className='w-050'>
                                    <Checkbox
                                        name='enableAISupport'
                                        value={this.newControl.enableAISupport}
                                        onChange={this.onChange}
                                    >
                                        AI支持
                                    </Checkbox>
                                </div>
                            </div>

                            <div className='editor-line'>
                                <NewControlCustomProps
                                    name='customProperty'
                                    properties={this.newControl.customProperty}
                                    documentCore={this.props.documentCore}
                                    onChange={this.onChange}
                                    close={this.onClose}
                                    id='bCustomProperty'
                                    visible={this.bCustomProperty}
                                />
                            </div>
                        </div>

                        <div className='editor-line'>
                            <div className='w-70'>
                                最大长度
                            </div>
                            <div className='right-auto'>
                                <Input
                                    name='newControlMaxLength'
                                    type='number'
                                    value={this.newControl.newControlMaxLength}
                                    onChange={this.onChange}
                                    renderAppend={this.renderCell}
                                />
                            </div>
                        </div>

                        <div className='editor-line'>
                            <div className='w-70'>收缩信息</div>
                            <div className='right-auto'>
                                <Input
                                    name='newControlPlaceHolder'
                                    value={this.newControl.newControlPlaceHolder}
                                    onChange={this.onChange}
                                />
                            </div>
                        </div>
                        <StructRegExpBtn
                            id='regExp'
                            onChange={this.onChange}
                            property={this.newControl.regExp}
                            visible={true}
                            close={this.onClose}
                            documentCore={this.props.documentCore}
                        />
                    </div>

                </div>
            </Dialog>
        );
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        this.visible = nextProps.visible;
    }

    private renderFooter(): any {
        return (
            <span>
                <Button onClick={this.close}>取消</Button>
                <Button type='primary' onClick={this.confirm}>确认</Button>
            </span>
        );
    }

    private onClose = (id: string, bRefresh: boolean): void => {
        this[id] = false;
        if (id === 'bCustomProperty' && bRefresh) {
            this.setState({bRefresh: !this.state.bRefresh});
        }
    }

    private open = (): void => {
        const props = this.props.property;
        const newControl: INewControlProperty = this.newControl;
        if (props === undefined) {
            this.init();
            newControl.newControlName = this.props.documentCore.makeUniqueRegionName();
        } else {
            const keys = Object.keys(props);
            keys.forEach((key) => {
                const val = props[key];
                newControl[key] = val;
            });
        }
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private close = (bRefresh?: boolean): void => {
        this.props.close(this.props.id, bRefresh);
        this.visible = false;
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private confirm = (): void => {
        const documentCore = this.props.documentCore;
        IFRAME_MANAGER.setDocId(documentCore.getCurrentId());
        const props = this.props.property;
        const newControl = this.newControl;
        if (!isValidName(newControl.newControlName)) {
            message.error('名称不符合规范，请重新输入');
            return;
        }
        if (newControl.identifier && !isValidName(newControl.identifier)) {
            message.error('标识符不符合规范，请重新输入');
            return;
        }
        if ((!props || props.newControlName !== newControl.newControlName)
            && !documentCore.checkRegionName(newControl.newControlName)) {
            message.error('该名称不符合，请重新输入');
            return;
        }

        if ( 0 > newControl.newControlMaxLength ) {
            message.error('最大长度需>= 0，请重新输入');
            return;
        } else if ( 0 < newControl.newControlMaxLength ) {
            newControl.newControlMaxLength = Math.round(newControl.newControlMaxLength);
        }

        if (props === undefined) {
            documentCore.addRegion(newControl);
            // gEvent.setEvent(documentCore.getCurrentId(), gEventName.RegionActive);
        } else {
            documentCore.setRegionProperty(newControl, props.newControlName);
        }

        this.close(true);
    }

    private init(): void {
        const newControl = this.newControl;
        newControl.newControlName = undefined;
        newControl.newControlTitle = undefined;
        newControl.newControlSerialNumber = undefined;
        newControl.isNewControlHidden = false;
        newControl.isNewControlCanntDelete = false;
        newControl.isNewControlCanntEdit = false;
        newControl.isNewControlReverseEdit = false;
        newControl.customProperty = undefined;
        newControl.showTitle = false;
        newControl.newControlMaxLength = 0;
        newControl.newControlInfo = undefined;
        newControl.identifier = undefined;
        newControl.newControlPlaceHolder = undefined;
        newControl.regExp = undefined;
        newControl.enableAISupport = false; // 默认不启用AI支持
    }

    private onChange = (value: any, name: string): void => {
        this.newControl[name] = value;
    }

    private onTitleChange = (value: any, name: string): void => {
        this.newControl[name] = value;
        this.setState({});
    }

    private renderCell(): any {
        return '字符';
    }

}
