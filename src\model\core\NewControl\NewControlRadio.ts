import { NewControl } from './NewControl';
import DocumentContentBase from '../DocumentContentBase';
import { INewControlProperty, ResultType, NewControlType, CodeValueItem,
    NEWCTR_BTN_MAP,
    IStructParamJson,
    isMacOs} from '../../../common/commonDefines';
import ParaPortion from '../Paragraph/ParaPortion';
import TextProperty from '../TextProperty';
import Paragraph from '../Paragraph';
import ParaTextExtend from '../Paragraph/ParaTextExtend';
import { ParaElementType, ParaTextName } from '../Paragraph/ParagraphContent';
import ParaText from '../Paragraph/ParaText';
import { ChangeNewControlShowRight, ChangeNewControlRadioChecked,
    ChangeNewControlRadioPortions, ChangeNewControlRadioPortions2,
    ChangeNewControlPrintChecked, ChangeNewControlRadioItemSelected, ChangeNewControlRadioShowType } from './NewControlChange';

// const btnMap = NEWCTR_BTN_MAP;

// const isMacOs = (navigator.userAgent.toLowerCase()
//     .indexOf('mac') > -1);
/**
 * 结构化元素: 单选框
 */
export class NewControlRadio extends NewControl {
    private checked: string;
    private bShowRight: boolean;
    private bPrintSelected: boolean;
    private radioPortions: ParaPortion[]; // 存放单选框
    private portions: ParaText[][]; // 存放内容框
    // private btnMap: Map<string, string>;
    private spaceNum: number;
    private items: CodeValueItem[];
    private itemTextColors: {[key: number]: string}; // 存放指定选中项文本的颜色
    private showType: number;
    private bHasItems: boolean;
    private bSupportMultLines: boolean;

    constructor(parent: DocumentContentBase, name: string, property: INewControlProperty, text?: string) {
        super(parent, name, property, '');
        this.bShowRight = property.showRight === undefined ? false : property.showRight;
        this.bPrintSelected = property.printSelected === undefined ? false : property.printSelected;
        this.spaceNum = property.spaceNum === undefined ? 1 : property.spaceNum;
        this.bSupportMultLines = property.supportMultLines === undefined ? false : property.supportMultLines;
        if (this.bSupportMultLines) {
            this.spaceNum = undefined;
        }
        // this.checked = property.radioChecked;
        // this.btnMap = btnMap;
        if (property.showType === undefined) {
            this.showType = NewControlType.RadioButton;
        } else {
            this.showType = property.showType;
        }
        // if (!property.itemTextColors) property.itemTextColors = {};
        this.itemTextColors = property.itemTextColors || {};
        // this.showType = property.showType === undefined ? NewControlType.RadioButton : property.showType;
        this.initItems(property.newControlItems);
        this.setPlaceHolder(false);
    }

    public setProperty(property: INewControlProperty): number {
        let res = super.setProperty(property);
        // this.setSelect(property.radioChecked);
        const change = this.setShowType(property.showType);
        res = res && change;
        let num = this.setShowRight(property.showRight);
        num = this.initItems(property.newControlItems, property.bClearItems) && num;
        if ((null == property.supportMultLines && !this.bSupportMultLines) || false === property.supportMultLines) {
            num = this.setSpaceNum(property.spaceNum) && num;
        }

        num = this.setSupportMultLines(property.supportMultLines) && num;
        res = res && num;
        // this.setCheckBoxText(property.label);
        res = this.setPrintSelected(property.printSelected) && res;
        if (num === ResultType.Success) {
            this.addRadioBox();
        } else if (change === ResultType.Success) {
            this.setAllRadioContent();
        }

        return res;
    }

    public getProperty(): INewControlProperty {
        const newControlProperty = super.getProperty();
        newControlProperty.showType = this.showType;
        newControlProperty.spaceNum = this.spaceNum;
        newControlProperty.newControlItems = this.items;
        newControlProperty.itemTextColors = this.itemTextColors;
        newControlProperty.supportMultLines = this.bSupportMultLines;

        return newControlProperty;
    }

    public updateItems(items: CodeValueItem[], bClearItems?: boolean): number {
        if (!items || items.length === 0) {
            return ResultType.UnEdited;
        }

        const num = this.initItems(items, bClearItems);
        if (num === ResultType.Success) {
            this.setDirty();
            this.addRadioBox();
        }

        return num;
    }

    public getItems(): CodeValueItem[] {
        return this.items;
    }

    public getCheckedValue(): string {
        if (this.getType() === NewControlType.MultiRadio) {
            return this.items.filter((item) => item.bSelect)
            .map((item) => item.value)
            .join(',');
        }
        const data = this.items.find((item) => item.bSelect);
        if (data) {
            return data.value;
        }
    }

    public removeAllItems(): number {
        if (this.items.length === 0 || this.isSameItems([new CodeValueItem('', '')])) {
            return ResultType.UnEdited;
        }
        // this.items = [];
        this.checked = undefined;
        this.initItems([], true);
        this.addRadioBox();
        return ResultType.Success;
    }

    public setShowType(type: NewControlType): number {
        if (type === this.showType || type === undefined) {
            return ResultType.UnEdited;
        }

        const history = this.getHistory();
        if ( history ) {
            history.addChange(new ChangeNewControlRadioShowType(this, this.showType, type));
        }

        this.showType = type;
        this.setDirty();
        return ResultType.Success;
    }

    public getShowType(): NewControlType {
        return this.showType;
    }

    public isContainSelected(text: string): boolean {
        if (!text || !this.isMultiRadio()) {
            return false;
        }
        const selected = this.getSelected();
        if (!selected) {
            return false;
        }
        const texts = text.split(',');
        const selecteds = selected.split(',');
        for (let index = 0, len = selecteds.length; index < len; index++) {
            const actText = selecteds[index];
            const actIndex = texts.findIndex((item) => item === actText);
            if (actIndex !== -1) {
                texts.splice(actIndex, 1);
            }
            if (texts.length === 0) {
                break;
            }
        }

        return texts.length === 0;
    }

    public setSpaceNum(num: number): number {
        if (num === this.spaceNum || num === undefined) {
            return ResultType.UnEdited;
        }

        this.spaceNum = num;
        this.setDirty();
        return ResultType.Success;
    }

    public getSpaceNum(): number {
        return this.spaceNum;
    }

    public setSupportMultLines(bSupportMultLines: boolean): number {
        if (bSupportMultLines === this.bSupportMultLines
            || bSupportMultLines === undefined) {
            return ResultType.UnEdited;
        }

        this.bSupportMultLines = bSupportMultLines;
        if (this.bSupportMultLines && !!this.spaceNum) {
            this.spaceNum = undefined;
        } else if (!this.bSupportMultLines && null == this.spaceNum) {
            this.spaceNum = 1;
        }

        this.setDirty();
        return ResultType.Success;
    }

    public isSupportMultLines(): boolean {
        return this.bSupportMultLines;
    }

    public getNewControlText3(): string {
        return this.getSelected();
        // if (!text) {
        //     return;
        // }
        // if (this.bShowRight === true) {
        //     return text.slice(0, -1);
        // }
        // return text.slice(1);
    }

    public setRadioSelect(checked: string): number {
        if (this.checked === checked || checked == null) {
            return ResultType.UnEdited;
        }

        if (this.getType() === NewControlType.MultiRadio) {
            const checkedDatas = checked.split(',');
            if (!this.items.find((item) => checkedDatas.includes(item.code))) {
                return ResultType.UnEdited;
            }
            return this.setMultiRadioCheckeds(checkedDatas);
        }

        return this.setRadioChecked(checked);
    }

    public clearSelected(): number {
        const selected = this.getSelected();
        if (!selected) {
            return ResultType.UnEdited;
        }
        if (this.getType() === NewControlType.MultiRadio) {
            return this.setMultiRadioCheckeds([]);
        }
        return this.setRadioChecked(selected);
    }

    public setSelectByIndex(sIndex: string): number {
        if (!sIndex) {
            return ResultType.UnEdited;
        }
        if (this.getType() === NewControlType.MultiRadio) {
            const indexes = sIndex.split(',').map(i => +i).sort();
            const checkeds: string[] = [];
            const items = this.items;
            for (const index of indexes) {
                const item = items[index];
                if (item) {
                    checkeds.push(item.code);
                }
            }
            if (checkeds.length === 0) {
                return ResultType.UnEdited;
            }
            return this.setMultiRadioCheckeds(checkeds);
        }
        const index = +sIndex;
        const item = this.items[index];
        if (item) {
            if (item.code === this.checked) {
                return ResultType.UnEdited;
            }
            return this.setRadioChecked(item.code);
        }
        return ResultType.Failure;
    }

    public setItemTextColor(sIndex: string, color: string = ''): number {
        if (!sIndex) return ResultType.ParamError;
        const indexes = sIndex.split(',').map(i => +i).sort();
        // 允许多次设置颜色
        for (const index of indexes) {
            this.itemTextColors[index] = color;
        }
        this.initItemTextColor();
        return ResultType.Success;
    }

    public getItemTextColors(): {[key: number]: string;} {
        return {...this.itemTextColors};
    }

    public setSelectByValue(value: string): number {
        if (value == null) {
            return ResultType.UnEdited;
        }

        if (this.getType() === NewControlType.MultiRadio) {
            const values = value.split(',');
            const checkeds: string[] = [];
            const items = this.items;
            for (let index = 0, len = items.length; index < len; index++) {
                const item = items[index];
                if (values.includes(item.value)) {
                    checkeds.push(item.code);
                }
            }
            if (checkeds.length === 0) {
                return ResultType.UnEdited;
            }

            return this.setMultiRadioCheckeds(checkeds);
        }

        const obj = this.items.find((item) => item.value === value);
        if (obj) {
            if (obj.code === this.checked) {
                return ResultType.UnEdited;
            }
            return this.setRadioChecked(obj.code);
        }
        return ResultType.Failure;
    }

    public setRadioBoxChecked(x: number, y: number): boolean {
        if (this.isEditProtect()) {
            return;
        }
        const index = this.getSelecteIndex(x, y);
        if (index === -1) {
            return false;
        }

        const code = this.items[index].code;
        if (this.getType() === NewControlType.MultiRadio) {
            this.setMultiRadioChecked(code);
        } else {
            this.setRadioChecked(code);
        }

        return true;
    }

    public getItemList(): CodeValueItem[] {
        if (this.bHasItems === true) {
            return [];
        }
        return this.items;
    }

    public getSelected(): string {
        return this.checked;
    }

    public getSelectedValue(): string {
        if (this.checked == null) {
            return ResultType.StringEmpty;
        }
        return this.items.filter((item) => item.bSelect)
            .map((item) => item.value)
            .join(',');
    }

    public getSelectedIndexes(): string {
        if (this.checked == null) {
            return ResultType.StringEmpty;
        }
        const indexes = [];
        this.items.forEach((item, index) => {
            if (item.bSelect) {
                indexes.push(index);
            }
        })
        return indexes.join(',');
    }

    public isShowRight(): boolean {
        return this.bShowRight;
    }

    // public getCheckLabel(): string {
    //     return this.text;
    // }

    public isPrintSelected(): boolean {
        return this.bPrintSelected;
    }

    public getPrintSelected(): boolean {
        return this.bPrintSelected;
    }

    public setPrintSelected(bPrintSelected: boolean): number {
        if (this.bPrintSelected === bPrintSelected || bPrintSelected == null) {
            return ResultType.UnEdited;
        }

        const history = this.getHistory();
        if ( history ) {
            history.addChange(new ChangeNewControlPrintChecked(this, this.bPrintSelected, bPrintSelected));
        }

        this.bPrintSelected = bPrintSelected;
        this.setDirty();
        return ResultType.Success;
    }

    public setShowRight(bShowRight: boolean, bRefresh: boolean = true): number {
        if (this.bShowRight === bShowRight || bShowRight == null) {
            return ResultType.UnEdited;
        }

        const history = this.getHistory();
        if ( history ) {
            history.addChange(new ChangeNewControlShowRight(this, this.bShowRight, bShowRight));
        }

        this.bShowRight = bShowRight;
        this.setDirty();
        return ResultType.Success;
    }

    public addToParagragh( para: Paragraph, curPos: number ): void {
        super.addToParagragh(para, curPos);
        this.addRadioBox();
    }

    public addContent(): void {
        // this.addRadioBox();
        this.initIPasteContent();
    }

    public toPrint(): void {
        super.toPrint();
        if (this.isHidden()) {
            return;
        }

        if (this.checked === undefined && this.bPrintSelected) {
            this.setHidden(true, false);
        }
    }

    public setNewControlText(text: string): number {
        return ResultType.UnEdited;
    }

    public setNewControlTextByJson(json: IStructParamJson): number {
        return this.setSelectByValue(json.code_text);
    }

    // copy/paste：select error, 多次拷贝粘贴，引用同一份portions
    public checkCheckBoxSelect(): void {
        this.items?.forEach((item, index) => {
            const curContent = this.getRadioBoxStr(item.bSelect);
            if (this.portions && this.portions[index]) {
                const length = this.portions[index].length;
                const content = this.bShowRight ? this.portions[index][length - 1].content :
                            this.portions[index][0].content;
                if (curContent !== content) {
                    item.bSelect = !item.bSelect;
                }
            }
        });
    }

    private setRadioChecked(checked?: string): number {
        const history = this.getHistory();
        if (this.checked === checked) {
            if ( history ) {
                history.addChange(new ChangeNewControlRadioChecked(this, this.checked, undefined));
            }

            this.checked = undefined;
            this.resetItems();
            this.setTextFlag(true);
            this.triggerCascade();
            this.setDirty();
            return ResultType.Success;
        }
        const portions = this.radioPortions;
        if (!portions || portions.length === 0 || checked === this.checked) {
            return ResultType.UnEdited;
        }

        const items = this.items;
        const index = items.findIndex((item) => item.code === checked);
        if (index === -1) {
            return ResultType.Failure;
        }
        if ( history ) {
            history.addChange(new ChangeNewControlRadioChecked(this, this.checked, checked));
        }

        this.checked = checked;
        this.resetItems();

        this.setRadioContent(index, true);
        items[index].bSelect = true;
        this.setTextFlag(true);
        this.triggerCascade();
        this.setDirty();
        return ResultType.Success;
    }

    private setMultiRadioCheckeds(checkeds: string[]): number {
        // if (this.checked === checked) {
        //     return ResultType.UnEdited;
        // }

        const portions = this.radioPortions;
        if (!portions || portions.length === 0) {
            return ResultType.UnEdited;
        }
        this.resetItemTextColor();
        // this.resetItems();
        const items = this.items;
        const checkDatas: string[] = [];
        // const datas = items.findIndex((item) => checkeds.includes(item.code));
        for (let index = 0, len = items.length; index < len; index++) {
            const item = items[index];
            if (!checkeds.includes(item.code)) {
                if (item.bSelect) {
                    this.setRadioContent(index, false);
                    item.bSelect = false;
                }
                continue;
            }
            checkDatas.push(item.code);
            this.setRadioContent(index, true);
            item.bSelect = true;
        }
        // 选择项为空时
        // if (checkDatas.length === 0) {
        //     return ResultType.UnEdited;
        // }

        const checkedStr = checkDatas.join(',');
        const history = this.getHistory();
        if ( history ) {
            history.addChange(new ChangeNewControlRadioChecked(this, this.checked, checkedStr));
        }

        this.checked = checkedStr;
        this.setTextFlag(true);
        this.triggerCascade();
        this.setDirty();
        return ResultType.Success;
    }

    private setMultiRadioChecked(checked?: string): number {
        // if (this.checked === checked) {
        //     return ResultType.UnEdited;
        // }

        const portions = this.radioPortions;
        if (!portions || portions.length === 0) {
            return ResultType.UnEdited;
        }

        const items = this.items;
        const index = items.findIndex((item) => item.code === checked);
        if (index === -1) {
            return ResultType.Failure;
        }
        // this.checked = checked;
        // this.resetItems();
        const itme = items[index];
        this.setRadioContent(index, !itme.bSelect);
        itme.bSelect = itme.bSelect === true ? false : true;
        const checkedItems = items.filter((item) => item.bSelect);
        let checkedStr;
        if (checkedItems.length > 0) {
            checkedStr = checkedItems.map((item) => item.code)
            .join(',');
        }

        const history = this.getHistory();
        if ( history ) {
            history.addChange(new ChangeNewControlRadioChecked(this, this.checked, checkedStr));
        }
        this.resetItemTextColor();
        this.checked = checkedStr;
        this.setTextFlag(true);
        this.triggerCascade();
        this.setDirty();
        return ResultType.Success;
    }

    private getSelecteIndex(x: number, y: number): number {
        const portions = this.portions;
        if (!portions || portions.length === 0) {
            return -1;
        }
        const startPortion = this.getStartBorderPortion();
        const para = startPortion.paragraph;
        const portion = para.getCurParaPortion();
        let actText = portion.getCurrentText();
        if (!actText) {
            return -1;
        }
        if (startPortion.content[0] === actText) {
            return 0;
        }

        actText = this.getItemTag(actText, portion, para, x);

        for (let index = 0, length = portions.length; index < length; index++) {
            const contents = portions[index];
            if (contents.length === 0) {
                continue;
            }
            const actIndex = contents.findIndex((item) => item === actText);
            if (actIndex > -1) {
                return index;
            }
        }
        return -1;
    }

    private getItemTag(actText: any, portion: ParaPortion, para: Paragraph, x: number): any {
        if (this.bShowRight) {
            return actText;
        }
        const actContents = portion.content;
        if (actText.content === ' ' && actContents[actContents.length - 1] === actText) {
            if (x - (actText.positionX + actText.widthVisible) > -0.5) {
                const paraContents = para.content;
                let index = para.getPortionIndexById(portion.id);
                let newPortion;
                // tslint:disable-next-line: no-conditional-assignment
                while (newPortion = paraContents[++index]) {
                    if (newPortion.content.length) {
                        const text = newPortion.content[0];
                        if (text.content !== ' ') {
                            actText = text;
                        }
                        break;
                    }
                    // newPortion = paraContents[index];
                }
            }
        }

        return actText;
    }

    private clearItems(): void {
        this.items = [];
    }

    private resetItems(): number[] {
        this.resetItemTextColor();
        if (!this.items || this.items.length === 0) {
            return;
        }

        const arrs: number[] = [];
        // const text = this.getRadioBoxStr(false);
        this.items.forEach((item, index) => {
            if (item.bSelect === true) {
                this.setRadioContent(index, false);
            }
            item.bSelect = false;
        });
        return arrs;
    }

    /** 重置选项文本颜色 */
    private resetItemTextColor(): void {
        if (!Object.keys(this.itemTextColors).length) return;
        const startPortion = this.getStartBorderPortion();
        const para = startPortion.paragraph;
        if (!para) return;
        const portions = para.content;
        const offset = this.bShowRight ? -1 : 1;
        for (const portion of this.radioPortions) {
            const curIndex = portion.getCurInParaIndex();
            const realIndex = curIndex + offset;
            const textPortion = portions[realIndex];
            if (textPortion && textPortion.checkedTextColor) {
                textPortion.checkedTextColor = '';
            }
        }
        this.itemTextColors = {};
    }

    private initItemTextColor(): void {
        if (!this.items || !Object.keys(this.itemTextColors).length) return;
        const startPortion = this.getStartBorderPortion();
        const para = startPortion.paragraph;
        if (!para) return;
        const portions = para.content;
        const offset = this.bShowRight ? -1 : 1;
        this.radioPortions.forEach((portion, index) => {
            const curIndex = portion.getCurInParaIndex();
            const realIndex = curIndex + offset;
            const textPortion = portions[realIndex];
            if (textPortion && this.itemTextColors[index]) {
                textPortion.checkedTextColor = this.itemTextColors[index];
            }
        });
    }

    private setAllRadioContent(): void {
        const portions = this.radioPortions;
        if (!portions || portions.length === 0) {
            if (this.items.length > 0) {
                this.addRadioBox(this.getStartBorderInParagraph());
            }
            return;
        }
        // const text = this.getRadioBoxStr(false);
        const indexs = [];
        if (this.checked) {
            const checkeds = this.checked.split(',');
            this.items.forEach((item, index) => {
                if (checkeds.includes(item.code)) {
                    indexs.push(index);
                    this.setRadioContent(index, item.bSelect);
                }
            });
        }

        portions.forEach((portion, itemIndex) => {
            // let content = text;
            // if (indexs.includes(itemIndex)) {
            //     content = this.getRadioBoxStr(true);
            // }
            if (!indexs.includes(itemIndex)) {
                this.setRadioContent(itemIndex, false);
            }
        });
    }

    private setRadioContent(index: number, bSelect: boolean): void {
        const history = this.getHistory();
        const item = this.items[index];
        if ( history && item && item.bSelect !== bSelect ) {
            history.addChange(new ChangeNewControlRadioItemSelected(item, item.bSelect, bSelect));
        }

        const portion = this.radioPortions[index];
        if (!portion || portion.content.length === 0) {
            return;
        }
        // portion.content[0].content = text;
        portion.content[0].repalceText(this.getRadioBoxStr(bSelect), history);
    }

    private addRadioBox(para?: Paragraph): void {
        const portion: ParaPortion = this.getStartBorderPortion();
        para = para || portion.paragraph;
        if (!para) {
            return;
        }
        // portion.setPlaceHolder(false);
        const contents = para.content;
        const start = contents.findIndex((item) => item === portion) + 1;
        const endPortion = this.getEndBorderPortion();
        const end = contents.findIndex((item) => item === endPortion);
        // const startPos = this.getStartPos();
        // const endPos = this.getEndPos();
        // // const start = startPos.get(startPos.getDepth() - 1) + 1;
        // const end = endPos.get(endPos.getDepth() - 1);
        const count = end - start;
        if (count > 0) {
            // para.removePortion(start, count);
            para.contentRemove2(start, count);
        }
        this.createItemPortion(para, start, portion.textProperty);
    }

    private initIPasteContent(): void {
        if (!this.items) {
            this.getDefaultItems();
            return;
        }
        const portion: ParaPortion = this.getStartBorderPortion();
        const para = portion.paragraph;
        if (!para) {
            return;
        }
        // portion.setPlaceHolder(false);
        const contents = para.content;
        const start = contents.findIndex((item) => item === portion) + 1;
        const endPortion = this.getEndBorderPortion();
        const end = contents.findIndex((item) => item === endPortion);
        const box1 = NEWCTR_BTN_MAP.get('1' + this.showType + false);
        const box2 = NEWCTR_BTN_MAP.get('' + this.showType + false);
        const checkeds = [box1, box2, this.getRadioBoxStr(true)];
        const bShowRight = this.bShowRight;
        const items: string[] = this.items.map((item) => item.code);
        let itemIndex: number = 0;
        let pos: number = 0;
        const radioBox = [];
        // const checked = this.checked;
        // let radioIndex: number = 0;
        let portionIndex: number = 0;
        const paraTexts: ParaText[][] = [];
        let bChecked: boolean = false;
        let codeStr: string[] = items[itemIndex].split('');
        let codeLen = codeStr.length;
        let bBreak = false;
        for (let index = start; index < end; index++) {
            if (bBreak) {
                break;
            }
            const portionTexts = contents[index].content;
            for (let textIndex = 0, len = portionTexts.length; textIndex < len; textIndex++) {
                if (bChecked && pos === codeLen) {
                    portionIndex++;
                    itemIndex++;
                    if (itemIndex === items.length) {
                        bBreak = true;
                        break;
                    }
                    codeStr = items[itemIndex].split('');
                    codeLen = codeStr.length;
                    bChecked = false;
                    pos = 0;
                }
                const portionText = portionTexts[textIndex] as ParaText;
                const text = portionText.content;
                if (!bChecked && (bShowRight && pos === codeLen || !bShowRight && pos === 0)) {
                    const checkedIndex = checkeds.indexOf(text);
                    if (checkedIndex > -1) {
                        // const red = (this.isMustInput() === true) ? true : false;
                        const radioText = new ParaTextExtend(text, ParaTextName.ParaCheckbox);
                        contents[index].content[0] = radioText;
                        radioBox.push(contents[index]);
                        if (!paraTexts[portionIndex]) {
                            paraTexts[portionIndex] = [];
                        }
                        paraTexts[portionIndex].push(radioText);
                        if (checkedIndex === 2) {
                            this.checked = items[itemIndex];
                            this.items[itemIndex].bSelect = true;
                        }
                        bChecked = true;
                        continue;
                    }
                } else {
                    if (codeStr[pos] === text) {
                        if (!paraTexts[portionIndex]) {
                            paraTexts[portionIndex] = [];
                        }
                        paraTexts[portionIndex].push(portionText);
                        pos++;
                    }
                }
            }
        }
        if (!radioBox.length) {
            this.setAllRadioContent();
        } else {
            this.radioPortions = radioBox;
            // item text colors
            this.initItemTextColor();
            this.portions = paraTexts;
        }
    }

    // private getPortionText(): void {

    // }

    private getRadioBoxStr(checked: boolean): string {
        let str = '';
        if (isMacOs && checked === false) {
            str = '1';
        }
        return NEWCTR_BTN_MAP.get(str + this.showType + checked);
    }

    private addSpaces(portion: ParaPortion, bRight?: boolean): void {
        const num = this.spaceNum;
        if (!num || num < 0) {
            return;
        }
        const len = bRight ? 0 : portion.content.length;
        for (let index = 0; index < num; index++) {
            const text = new ParaText(' ');
            portion.addToContent(len, text);
        }

        if ( this.isTrackRevisions() ) {
            portion.resetReviewTypeAndInfo();
        }
    }

    private addNewLine(portion: ParaPortion, bRight?: boolean): void {
        if (!this.bSupportMultLines) {
            return;
        }

        portion.addParaItem(bRight ? 0 : portion.content.length, ParaElementType.ParaNewLine);

        if ( this.isTrackRevisions() ) {
            portion.resetReviewTypeAndInfo();
        }
    }

    private createItemPortion(para: Paragraph, pos: number, textProp: TextProperty): void {
        if (!this.items) {
            this.getDefaultItems();
            // return;
        }
        const items = this.items;
        const len = items.length - 1;
        const history = this.getHistory();
        if (len < 0) {
            if ( history ) {
                history.addChange(new ChangeNewControlRadioPortions(this, this.radioPortions, []));
            }

            this.radioPortions = [];
            return;
        }

        const bShowRight = this.bShowRight;
        const radioBox = [];
        // const checked = this.checked;
        let index: number = 0;
        let portionIndex: number = 0;
        const paraTexts: ParaText[][] = [];
        const bHidden = this.isHidden();
        const bTrackRevision = this.isTrackRevisions();

        items.forEach((item, itemIndex) => {
            const radio = this.createRadioBox(para, item.bSelect, textProp);
            radioBox.push(radio);
            const portion = new ParaPortion(para);
            portion.addText(item.code);
            portion.textProperty = textProp.copy();
            // 有选中色
            if (this.itemTextColors[itemIndex]) {
                portion.checkedTextColor = this.itemTextColors[itemIndex];
            }
            let texts = [];
            if (bShowRight) {
                para.addToContent(pos + index++, portion);
                para.addToContent(pos + index++, radio);
                texts = texts.concat(portion.content.slice(0), radio.content.slice(0));
                // paraTexts[portionIndex].push(position, radio);
                if (0 !== itemIndex) {
                    if (this.spaceNum > 0) {
                        // const newPortion = new ParaPortion(para);
                        // newPortion.textProperty = textProp.copy();
                        // this.addSpaces(newPortion);
                        // para.addToContent(pos + index++, newPortion);
                        this.addSpaces(portion, bShowRight);
                    } else if (this.bSupportMultLines) {
                        // const newPortion = new ParaPortion(para);
                        // newPortion.textProperty = textProp.copy();
                        // this.addNewLine(newPortion);
                        // para.addToContent(pos + index++, newPortion);
                        this.addNewLine(portion, bShowRight);
                    }
                }
            } else {
                para.addToContent(pos + index++, radio);
                texts = texts.concat(radio.content.slice(0), portion.content.slice(0));
                if (itemIndex !== len) {
                    this.addSpaces(portion);
                    this.addNewLine(portion);
                }
                para.addToContent(pos + index++, portion);
            }
            paraTexts[portionIndex++] = texts;
            if (bHidden === true) {
                portion.setHidden(true);
            }

            if ( bTrackRevision ) {
                portion.resetReviewTypeAndInfo();
            }
        });

        if ( history ) {
            history.addChange(new ChangeNewControlRadioPortions(this, this.radioPortions, radioBox));
            history.addChange(new ChangeNewControlRadioPortions2(this, this.portions, paraTexts));
        }

        this.radioPortions = radioBox;
        this.portions = paraTexts;
    }

    private createRadioBox(para: Paragraph, checked: boolean, textProp: TextProperty): ParaPortion {
        const newPortion = new ParaPortion(para);

        // const red = (this.isMustInput() === true) ? true : false;
        const text = new ParaTextExtend(this.getRadioBoxStr(checked), ParaTextName.ParaCheckbox);
        newPortion.addToContent(0, text);
        const textProperty = newPortion.textProperty;
        textProperty.color = '#000';
        textProperty.fontSize = textProp.fontSize;
        textProperty.textDecorationLine = textProp.textDecorationLine;
        if (this.isHidden()) {
            newPortion.setHidden(true);
        }

        if ( this.isTrackRevisions() ) {
            newPortion.resetReviewTypeAndInfo();
        }
        return newPortion;
    }

    private initItems(items: CodeValueItem[], bClearItems: boolean = true): number {
        if (items == null) {
            return ResultType.UnEdited;
        }
        if (items.length === 0) {
            if (bClearItems === false) {
                return ResultType.UnEdited;
            }
            this.getDefaultItems();
            return ResultType.Success;
        }

        if (this.isSameItems(items)) {
            return ResultType.UnEdited;
        }
        if (bClearItems) {
            this.clearItems();
            this.items = items.slice();
        } else {
            const allItems = this.items;
            items.forEach((item) => {
                const code = item.code;
                const index = allItems.findIndex((current) => current.code === code);
                if (index > -1) {
                    allItems.splice(index, 1, item);
                } else {
                    allItems.push(item);
                }
            });
        }

        return ResultType.Success;
    }

    private isSameItems(items: CodeValueItem[]): boolean {
        const oldItems = this.items;
        if (!oldItems || oldItems.length === 0) {
            return false;
        }
        if (items.length !== oldItems.length) {
            return false;
        }
        for (let index = 0, len = items.length; index < len; index++) {
            if (!(items[index].code === oldItems[index].code && items[index].value === oldItems[index].value)) {
                return false;
            }
        }

        return true;
    }

    private getDefaultItems(): void {
        this.items = [new CodeValueItem('', '')];
    }
}
