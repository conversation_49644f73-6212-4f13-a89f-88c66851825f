import {message} from '../Message';
import { getPxForMM } from '../../model/core/util';
import { CleanModeType, ICPrintData, PAGE_FORMAT, PrintType } from '../commonDefines';
import { DocumentCore } from '../../model/DocumentCore';
import { DocumentContentType } from '../../model/core/Style';
import { EmrEditor } from '../../components/editor/Main';
import {PrintContent} from '../../../src/components/editor/module/PrintContent';
import * as React from 'react';
/* IFTRUE_WATER */
import MarkFactory from '../MarkFactory';
/* FITRUE_WATER */
import { editorCallback } from '../GlobalTest';

export default class PrintDialog {
    public oPrintContentRef: any;
    public option: any;
    public print: Element;
    public isPrint: boolean; // directly from print, no pre-print
    public isCPrint: boolean;
    public src: string;
    public documentCore: DocumentCore;
    public reactVm: any;
    public reactVm2: React.RefObject<PrintContent> = React.createRef(); // 使用 React.createRef
    public iframeContainer: any;
    public contents: any[];
    public docId: number;
    protected printData: ICPrintData;
    protected clincCheckDom: Element;
    protected clincRadioDom: HTMLDivElement;
    protected clincRadioDoms: any;
    protected closeFn: () => void;
    private clincMode: string;
    private pageProperty: object;
    private bProtected: boolean;
    private bContent: boolean;
    private host: EmrEditor;
    private bClose: boolean;
    private _disablePrintAction: boolean; // 新增：存储配置项


    constructor(documentCore?: any, host: EmrEditor = null) {
        this.documentCore = documentCore;
        if (documentCore) {
            this.docId = documentCore.getCurrentId();
        }
        if (host != null) {
            this.host = host;
            this._disablePrintAction = (host.props as any)?.disablePrintAction ?? false;
        }
    }

    public setDoc(documentCore: DocumentCore): void {
        this.docId = documentCore.getCurrentId();
        this.documentCore = documentCore; // set to reference, fill content later
    }

    public setContent(contents: any[], option: any): void {
        // this.contents = contents;
        // this.bContent = true;
        // const copyManager = new ClipboardContent();
        // const documentCore = this.documentCore;
        // this.pageProperty = option.pageProps;
        // copyManager.setDocument(documentCore.getDocument());
        // copyManager.setNewControlProps(option.newControlProps);
        // copyManager.setRegionProps(option.regionProps);
        // const newContents = copyManager.getNewContents(contents);
        // documentCore.setPageProperty(option.pageProps);
        // documentCore.insertContent(newContents);
        // const lastIndex = newContents.length - 1;
        // const docContents = documentCore.getDocument().content;
        // this.setProps(0, docContents, contents[0]);
        // this.setProps(lastIndex, docContents, contents[lastIndex]);
        // documentCore.recalculate();
        // documentCore.protectDoc(true);
    }

    public setProps(index: number, contents: any[], para: any): void {
        // const para = this.content[index] as Paragraph;
        if (para.getType() !== DocumentContentType.Paragraph) {
            return;
        }

        contents[index].paraProperty = para.paraProperty.copy();
    }

    public getPagePro(): any {
        return this.documentCore.getPageProperty();
    }

    public intPrintDialog(pagePro: any): void {
        // this.initPrint(print);
        let className = '';
        const printDom = this.print;
        if (!this.isPrint) {
            let hideClassName: string;
            if ((hideClassName = printDom.className).indexOf('print-hide') > -1) {
                printDom.className = hideClassName.replace(' print-hide', '');
            }
        } else if (printDom.className.indexOf('print-hide') === -1) {
            className = ' print-hide';
        }
        printDom.className = printDom.className + ' active' + className;
        // const firstChild = printDom.querySelector('div') as HTMLDivElement;
        // firstChild.style.width = pagePro.width + 18 + 'px';
        printDom.querySelector('select').value = '100';
        this.updateContent(false);
    }

    public updateContent(isFirst: boolean = true, bCprint: boolean = false): void {

        this.bClose = false;
        if (this.isPrint !== true) {
            this.bProtected = this.documentCore.isProtectedMode();
            if (this.bProtected === false) {
                this.documentCore.protectDoc(true);
            }
        }
        this.reactVm.setDocumentCore(this.documentCore, bCprint);

        const {startEle, endEle} = this.option || {};
        if (startEle) {
            this.documentCore.addSelection(startEle, endEle);
            this.reactVm.setSelections();
            this.option.startEle = null;
            this.option.endEle = null;
        }
        this.printContent();
    }

    public bntsClick = (e: any) => {
        const target = e.target as Element;
        if (target.nodeName !== 'BUTTON') {
            return;
        }
        if (target.className === 'close') {
            return ;
        }
        if (this.clincMode === 'clinc') {

            message.error('请选择当前打印哪一页');

            return;
        }
        let type = parseInt(target.getAttribute('type'), 10);
        // add cont printing type
        const contPrintDom: any = this.print.querySelector('.print-dialog-box .cont-print');
        const printLandScapeElem: any = this.print.querySelector('.print-dialog-box .print-landscape');
        let printLandScape: boolean = false;
        if (printLandScapeElem != null) {
            printLandScape = printLandScapeElem.checked;
        }
        const option: any = {printLandScape};
        if (contPrintDom != null) {
            if (contPrintDom.checked === true) {
                type = PrintType.Continuous;
            } else {
                const dom: any = this.print.querySelector('.print-dialog-box .batch-print');
                if (dom?.checked === true) {
                    type = PrintType.Batch;
                }
            }
            if (type === PrintType.Continuous) {
                option.startPageHdrFtr = (this.print.querySelector('.print-dialog-box .continue-block > input') as any)?.checked;
            }
            
        }
        /* IFTRUE_WATER */
        // 屏蔽水印生成
        MarkFactory.getInstance().setBGenStart(false);
        /* FITRUE_WATER */
        this.reactVm.handleRefresh();

        // 添加 host 的 externalEvent 和 disablePrintAction 到选项中
        if (this.host) {
            option.externalEvent = this.host.externalEvent;
            option.disablePrintAction = this._disablePrintAction;
        }
        this.reactVm.openPrint(type, this.clincMode, option);
    }

    public cPrintClick = (e: any) => {
        const target = e.target as Element;
        if (target.nodeName !== 'BUTTON') {
            return;
        }

        // const type = parseInt(target.getAttribute('type'), 10);

        let type = 1; // 1: all, 2: continuous
        // {
        //     "printData": "content",
        //     "printerName": "NPI5DAB36 (HP LaserJet Professional M1216nfh MFP)",
        //     "printCount": 1,
        //     "printDouble": false,
        //     "pageCount": 1
        // }
        // console.log(this.print)

        const printCountNumber: any = this.print.querySelector('.print-dialog-box-c #print-count-number');
        // const specificPage: any = this.print.querySelector('.print-dialog-box-c #specific-page');
        const printerSelect: any = this.print.querySelector('.print-dialog-box-c #printer-select');
        // tslint:disable-next-line: max-line-length
        const pageNumType: any = this.print.querySelector('.print-dialog-box-c .print-dialog-box-side input[name=pageNum]:checked');
        const doublePrint: any = this.print.querySelector('.print-dialog-box-c .print-dialog-box-side #double-print');
        const pageRange: any = this.print.querySelector('.print-dialog-box-c .print-dialog-box-side #specific-page');
        const singlePage: any = this.print.querySelector('.print-dialog-box-body-c .jumpPage-center #singlePage');
        const pageCount = this.documentCore.getPageCount();
        // tslint:disable-next-line: max-line-length
        const startPageHdrFtrDom: any = this.print.querySelector('.print-dialog-box-c .print-dialog-box-side .cont-startpage-hf');
        const landscape: any = this.print.querySelector('.print-dialog-box-c #print-landscape');

        const data: ICPrintData = {
            printData: '',
            printerName: '',
            printCount: 1,
            printDouble: false,
            pageCount: 1,
            pageRange: '',
            startPageHdrFtr: false,
            printType: PrintType.All,
            landscape: false,
            printOrientPortrait: true,

            showWatermark: this.printData.showWatermark
        };

        // type: 1: all, 2: continuous
        if (pageNumType.value === 'all') {
            type = 1;
            data.printType = PrintType.All;
        } else if (pageNumType.value === 'continous') {
            type = 2;
            data.printType = PrintType.Continuous;
        } else if (pageNumType.value === 'specific') {
            type = 1; // still 1, need to organize data manually
            data.pageRange = pageRange.value;
            data.printType = PrintType.Specific;
        } else if (pageNumType.value === 'single') {
            type = 1;
            data.pageRange = '' + (+singlePage.value);
            data.printType = PrintType.Specific;

        } else if (pageNumType.value === 'batch') {
            data._batch = true;
        }
        // console.log(type)
        // document.querySelector('.print-dialog-box-c .print-dialog-box-side #double-print').checked

        if (printCountNumber != null) {
            let printCount = Math.round(+printCountNumber.value);
            // set 1-99
            if (printCount < 1) {
                printCount = 1;
            } else if (printCount > 99) {
                printCount = 99;
            }
            data.printCount = printCount;
        }
        // if (specificPage != null) {
        //     data.pageCount = +specificPage.value;
        // }
        data.pageCount = pageCount;
        if (printerSelect != null) {
            data.printerName = printerSelect.value;
        }
        // if (pageNumType != null) {

        // }
        if (doublePrint != null) {
            data.printDouble = doublePrint.checked;
            if (this.printData.printDouble !== data.printDouble) {
                editorCallback.setItem('printDouble', data.printDouble.toString());
                window['bPrintDouble'] = data.printDouble;
            }
        }
        if (startPageHdrFtrDom != null) {
            data.startPageHdrFtr = startPageHdrFtrDom.checked;
        }
        if (landscape != null) {
            data.landscape = landscape.checked;
        }

        // 创建扩展选项对象，使用类型断言
        const extendedOptions: any = {
            ...data
        };
        
        // 添加 host 的 externalEvent 和 disablePrintAction 到扩展选项中
        if (this.host) {
            extendedOptions.externalEvent = this.host.externalEvent;
            extendedOptions.disablePrintAction = this._disablePrintAction;
        }

        // console.log(data);

        this.reactVm.openCPrint(type, extendedOptions)
        .then((result) => {
            this.close(result ? 1 : 2);
        });
        // console.log(this.reactVm.getTruePrintContent());
    }

    public pageRangeBlur = (e: any) => {
        // const target = e.target as Element;
        if (e.target.value.search(/^(\d+-\d+)*(\d)*$/) !== -1) {
            //
        } else {
            message.warning('打印页设定有误，请检查');
        }
    }

    public pageNumChange = (e: any) => {
        const target = e.target as Element;
        const contStartPageHFDom = this.print.querySelector('.cont-startpage-hf-text');
        // single page panel
        const contSingleSpanDom = this.print.querySelector('span.cont-single-page-text');
        const contSinglePageDom = this.print.querySelector('.print-dialog-box-body-c div#curPageBar') as HTMLDivElement;
        const contDiv = this.reactVm?.current.myRef?.current?.querySelector('div.ReactVirtualized__Grid__innerScrollContainer');
        if (e.target.value === 'continous') {
            message.info('请选择需要续打的病历内容');
            if (contStartPageHFDom.classList.contains('hidden')) {
                contStartPageHFDom.classList.remove('hidden');
            }
        } else if (e.target.value === 'single') {
            if (contSingleSpanDom && contSingleSpanDom.classList.contains('hidden')) {
                contSingleSpanDom.classList.remove('hidden');
            }
            // jump page bar
            if (contSinglePageDom && contSinglePageDom.style.visibility === 'hidden') {
                contSinglePageDom.style.visibility = 'visible';
                const iframeDom = contSinglePageDom.previousElementSibling as HTMLIFrameElement;
                iframeDom.style.height = 'calc(100% - 41px)';
                contSinglePageDom.addEventListener('click', this.handleSinglePageBarEvent, false);
                // add scrol event
                if (contDiv) {
                    this.handlePrintScrollEvent({target: contDiv});
                    contDiv.addEventListener('scroll', this.handlePrintScrollEvent, false);
                }
            }
        } else {
            if (contStartPageHFDom.classList.contains('hidden') === false) {
                contStartPageHFDom.classList.add('hidden');
            }
            if (contSingleSpanDom && contSingleSpanDom.classList.contains('hidden') === false) {
                contSingleSpanDom.classList.add('hidden');
            }
            if (contSinglePageDom && contSinglePageDom.style.visibility === 'visible') {
                contSinglePageDom.style.visibility = 'hidden';
                const iframeDom = contSinglePageDom.previousElementSibling as HTMLIFrameElement;
                iframeDom.style.height = '100%';
                contSinglePageDom.removeEventListener('click', this.handleSinglePageBarEvent, false);
                // remove scrol event
                if (contDiv) {
                    contDiv.removeEventListener('scroll', this.handlePrintScrollEvent);
                }

            }
        }
    }

    public cleanModeChange = (e: any) => {
        // const target = e.target as Element;
        const checked = e.target.checked;
        this.cleanModeChangeBase(checked === true ? CleanModeType.CleanMode : CleanModeType.Normal, false);
    }

    public watermarkChange = (e: any) => {
        const checked = e.target.checked;
        // show or hide watermark
        this.watermarkChangeBase(checked);
    }

    public printGridLineChange = (e: any) => {
        const checked = e.target.checked;
        this.printGridLineChangeBase(checked);
    }

    public cleanModeCChange = (e: any) => {
        // const target = e.target as Element;
        const checked = e.target.checked;
        this.cleanModeChangeBase(checked === true ? CleanModeType.CleanMode : CleanModeType.Normal, true);

        // reset pageNum
        const contSinglePageDom = this.print.querySelector('.print-dialog-box-body-c div#curPageBar') as HTMLDivElement;
        if (contSinglePageDom && contSinglePageDom.classList.contains('activeLabel') === false) {
            const contDiv = this.reactVm?.current.myRef?.current?.querySelector('div.ReactVirtualized__Grid__innerScrollContainer');
            if (contDiv) {
                this.handlePrintScrollEvent({target: contDiv});
            }
        }
    }

    public cleanModeChangeBase(cleanMode: CleanModeType, bCprint: boolean): void {
        const printLogicDocument = this.documentCore.getDocument(); // print document
        const content = this.host.state.documentCore.getDocContent(); // main document

        // connect main's DocContent(detached from main reference) with printDoc
        content.styles.background = printLogicDocument.styles.copyBackground();

        const bFinalRevision = this.documentCore.isFinalRevision();
        // if (checked === true) {
        //     printLogicDocument.addDocContent(content, true, this.printData);
        // } else {
        //     printLogicDocument.addDocContent(content, false, this.printData);
        // }
        printLogicDocument.addDocContent(content, cleanMode, this.printData);

        printLogicDocument.changeRevisionState2(bFinalRevision);

        printLogicDocument.setDynamicGridLine(this.host.state.documentCore.getDocument()
                            .isDynamicGridLine(), this.host.state.documentCore.getDocument()
                                    .getDynamicGridLineColor());

        const printGridline = this.print.querySelector('.print-dialog-box .print-gridline');
        const cPrintGridline = this.print.querySelector('.print-dialog-box-c .print-gridline');
        const bPrintGridline = (null != printGridline ? (printGridline as any).checked :
                                 (null != cPrintGridline ? (cPrintGridline as any).checked : undefined));
        printLogicDocument.setDynamicGridLine(bPrintGridline);
        // this.addSideEffectsForPrintInstance(this.documentCore, this.host.state.documentCore);
        this.documentCore.recalculateAllForce();

        // this.documentCore.recalculateAllForce();
        // console.log(this.documentCore.getDocument().content)
        // just need the refresh. this.documentCore already changed
        // this.reactVm.setDocumentCore();

        // this.printContent();
        this.updateContent(false, bCprint);
        this.reactVm.handleRefresh();
        // this.updateIframe();
    }

    public watermarkChangeBase(checked: boolean): void {
        const printLogicDocument = this.documentCore.getDocument();
        // const mainDocument = this.host.state.documentCore.getDocument();

        // shouldn't touch main doc
        const editorBackground = printLogicDocument.getEditorBackground();
        editorBackground.watermarkEnabled = checked;

        // printLogicDocument.setEditorBackground(editorBackground);

        // this.updateContent(false, false);
        this.reactVm.handleRefresh();
    }

    public printGridLineChangeBase(checked: boolean): void {
        const printLogicDocument = this.documentCore.getDocument();

        if (checked !== printLogicDocument.isDynamicGridLine()) {
            printLogicDocument.setDynamicGridLine(checked);
            this.reactVm.handleRefresh();
        }
    }

    public preventDefault = (e: any) => {
        e.preventDefault();
    }

    public checkboxChange = (e: any) => {
        const target = e.target as HTMLInputElement;
        const childWindow = this.getIframeContainer();
        switch (target.value) {
            case '1':
                // tslint:disable-next-line: no-string-literal
                childWindow['showPrintHeaderEvent'](target.checked);
                break;
            default:
                break;
        }
    }

    public selectChange = (e: any) => {
        const target = e.target as HTMLInputElement;
        this.reactVm.scaleViewEvent(target.value);
    }

    public close = (type?: any) => {
        if (this.bClose === true) {
            return;
        }
        /* IFTRUE_WATER */
        MarkFactory.getInstance().setBGenStart(true);
        /* FITRUE_WATER */
        if (this.bProtected === false && this.isPrint !== true) {
            this.documentCore.protectDoc(false);
        }
        // this.documentCore.clearSelection();
        const printDom = this.print;
        if (this.clincMode) {
            this.clincCheckDom['checked'] = false;
            this.setRadioDoms(false);
            this.resetPageProperty();
        }
        if (printDom) {
            printDom.className = printDom.className.replace(/\s+active\b/g, '');
            this.reactVm.scaleViewEvent('100');
        }
        if (this.closeFn) {
            this.closeFn();
        }
        if (this.printData) {
            const externalEvent = this.host.externalEvent;
            if (externalEvent && typeof externalEvent.nsoSendPrintDataCompleted === 'function') {
                externalEvent.nsoSendPrintDataCompleted(type || 2);
            }
            this.printData = null;
        }
        this.oPrintContentRef.current?.clearDom();
        this.bClose = true;
    }

    public clearDom = () => {
        if ( this.reactVm?.prentNode ) {
            this.reactVm.clearDom();
            // this.documentCore = null;
            this.reactVm = null;
        }
        if ( this.print ) {
            this.deleteEvent();
            this.print = null;
        }
        this.isPrint = false;
        this.isCPrint = false;
    }

    public slideOut = () => {
        // for animation
        (this.print as any).style.height = '0';
    }

    public transitionEnd = () => {
        const printHeight = (this.print as any).style.height;
        // console.log(printHeight)
        // only trigger when closed
        if (printHeight === '0px' || printHeight === '0') {
            this.close();
        }
    }

    public getHost(): EmrEditor {
        return this.host;
    }

    // public addSideEffectsForPrintInstance(documentPrintCore: any, documentHostCore: any): void {
    //     if (documentPrintCore != null && documentHostCore != null) {
    //         // set pageMargins
    //         const pageMarginsHeader = documentHostCore.getPageMarginsHeader();
    //         const pageMarginsFooter = documentHostCore.getPageMarginsFooter();
    //         documentPrintCore.setPageMarginsHeader(pageMarginsHeader);
    //         documentPrintCore.setPageMarginsFooter(pageMarginsFooter);

    //         documentPrintCore.recalculateAllForce();
    //     }
    // }

    protected clincCheckChange = (e: any): void => {
        this.setRadioDoms(e.target.checked);
    }

    protected clincRadioChange = (e: any): void => {
        this.clincMode = e.target.value;
    }

    protected setPageProperty(): void {
        if (!this.pageProperty) {
            const pageProperty = this.getPagePro();
            this.pageProperty = {
                width: pageProperty.width,
                height: pageProperty.height,
                paddingTop: pageProperty.paddingTop,
                paddingBottom: pageProperty.paddingBottom,
                paddingLeft: pageProperty.paddingLeft,
                paddingRight: pageProperty.paddingRight,
                pageFormat: pageProperty.pageFormat,
            };
        }

        const curPage = PAGE_FORMAT.C1;
        const actPage = { paddingTop: getPxForMM(curPage[4] * 10), paddingBottom:
            getPxForMM(curPage[5] * 10), paddingLeft: getPxForMM(curPage[2] * 10),
            paddingRight: getPxForMM(curPage[3] * 10), pageFormat: 'C1',
        height: getPxForMM(curPage[1] * 10), width: getPxForMM(curPage[0] * 10)};
        this.documentCore.setPageProperty(actPage);
        this.refreshDialog(actPage);
    }

    private resetPageProperty(): void {
        this.documentCore.setPageProperty(this.pageProperty);
        this.refreshDialog(this.pageProperty);
    }

    private refreshDialog(pageProperty: any): void {
        this.intPrintDialog(pageProperty);
    }

    private deleteEvent = (): void => {
        if (!this.print) {
            return ;
        }
        this.print.removeEventListener('mousewheel', this.preventDefault);
        this.print.querySelector('.btns')
            .removeEventListener('click', this.bntsClick);
        const closeElems = this.print.querySelectorAll('.print-dialog .close');
        // tslint:disable-next-line: prefer-for-of
        for (let i = 0; i < closeElems.length; i++) {
            closeElems[i].removeEventListener('click', this.close);
        }
        // this.print.querySelector('.close')
        //     .removeEventListener('click', this.close);

        // this.print.querySelector('select')
        //     .removeEventListener('change', this.selectChange);
        this.print.outerHTML = '';
        // this.print.querySelector('.header-box').removeEventListener('change', this.checkboxChange);

        // ok if event listener not exist
        // https://www.w3.org/TR/DOM-Level-2-Events/events.html#Events-EventTarget-removeEventListener
        // this.print.querySelector('.close')
        //     .removeEventListener('click', this.slideOut);
        const cCloseElems = this.print.querySelectorAll('.print-dialog-box-c .close');
        // tslint:disable-next-line: prefer-for-of
        for (let i = 0; i < cCloseElems.length; i++) {
            cCloseElems[i].removeEventListener('click', this.slideOut);
        }
        this.print.removeEventListener('transitionend', this.transitionEnd);
        const cleanModeDom = this.print.querySelector('.print-dialog-box .clean-mode');
        if (cleanModeDom != null) {
            cleanModeDom.removeEventListener('change', this.cleanModeChange);
        }
        const watermarkDom = this.print.querySelector('.print-dialog-box .show-watermark');
        if (watermarkDom != null) {
            watermarkDom.removeEventListener('change', this.watermarkChange);
        }
        const printGridline = this.print.querySelector('.print-dialog-box .print-gridline');
        if (printGridline != null) {
            printGridline.removeEventListener('change', this.printGridLineChange);
        }
        const cPrintDom = this.print.querySelector('.print-dialog-box-c .print');
        if (cPrintDom != null) {
            cPrintDom.removeEventListener('click', this.cPrintClick);
        }
        const cSpecificPageDom = this.print.querySelector('.print-dialog-box-c #specific-page');
        if (cSpecificPageDom != null) {
            cSpecificPageDom.removeEventListener('blur', this.pageRangeBlur);
        }
        const pageNumRadios = this.print.querySelectorAll('.print-dialog-box-c input[name=pageNum]');
        if (pageNumRadios.length > 0) {
            // tslint:disable-next-line: prefer-for-of
            for (let i = 0; i < pageNumRadios.length; i++) {
                pageNumRadios[i].removeEventListener('change', this.pageNumChange);
            }
        }
        const cCleanModeDom = this.print.querySelector('.print-dialog-box-c .clean-mode');
        if (cCleanModeDom != null) {
            cCleanModeDom.removeEventListener('change', this.cleanModeCChange);
        }
        const cWaterMarkDom = this.print.querySelector('.print-dialog-box-c .show-watermark');
        if (cWaterMarkDom != null) {
            cWaterMarkDom.removeEventListener('change', this.watermarkChange);
        }
        const cPrintGridline = this.print.querySelector('.print-dialog-box-c .print-gridline');
        if (cPrintGridline != null) {
            cPrintGridline.removeEventListener('change', this.printGridLineChange);
        }
    }

    private setRadioDoms(bChecked: boolean): void {
        if (!this.clincRadioDoms) {
            this.clincRadioDoms = this.clincRadioDom.querySelectorAll('input');
        }

        const doms = this.clincRadioDoms as HTMLInputElement[];
        if (bChecked === true) {
            for (let index = 0, length = doms.length; index < length; index++) {
                doms[index].removeAttribute('disabled');
            }
            this.clincMode = 'clinc';
            this.setPageProperty();
        } else {
            for (let index = 0, length = doms.length; index < length; index++) {
                doms[index].setAttribute('disabled', 'disabled');
            }
            this.clincMode = undefined;
            this.resetPageProperty();
        }

        doms[0].checked = false;
        doms[1].checked = false;
    }

    private printContent(): void {
        if (this.isPrint !== true || this.isCPrint) {
            return;
        }

        setTimeout(() => {
                const options = this.option || {};
                options.close = (result: boolean) => {
                    this.close();
                };
                
                // 添加 host 的 externalEvent 和 disablePrintAction 到选项中
                if (this.host) {
                    options.externalEvent = this.host.externalEvent;
                    options.disablePrintAction = this._disablePrintAction;
                }
                
                this.reactVm.openPrint(options.type || '1', undefined, options);
        }, 1);
    }

    private getIframeContainer(): any {
        if (!this.iframeContainer) {
            this.iframeContainer = this.print.querySelector('iframe').contentWindow;
        }

        return this.iframeContainer;
    }

    /** 处理预览页面的滚动事件 */
    private handlePrintScrollEvent = (e: any) => {
        const div = e.target as HTMLDivElement;
        const pages = Array.from(div.querySelectorAll('div[page-index]'));
        const singlePageInput = this.print.querySelector('.jumpPage-center input#singlePage') as HTMLInputElement;
        const fullPageDom = this.print.querySelector('.jumpPage-center span#fullPage');
        if (!!singlePageInput && !!fullPageDom) {
            fullPageDom.innerHTML = '' + pages.length;
            for (const page of pages) {
                if (page.getBoundingClientRect().bottom < 0) {
                    continue;
                }
                singlePageInput.value = '' + (+ page.getAttribute('page-index') + 1);
                break;
            }
        }
        // single page panel
        const contSingleSpanDom = this.print.querySelector('span#singlePageSpan');
        if (contSingleSpanDom) {
            contSingleSpanDom.innerHTML = singlePageInput.value;
        }
        this.updateCurPageBarClass(+singlePageInput.value, +fullPageDom.innerHTML);
    }

    /** 处理预览页面下方当前页跳转工具栏的点击事件 */
    private handleSinglePageBarEvent = (e: any) => {
        let dom = e.target as Element;
        if (dom.tagName === 'path') {
            dom = dom.parentElement;
        }
        if (!dom.classList.contains('activeLabel')) {
            return;
        }
        const singlePageInput = this.print.querySelector('.jumpPage-center input#singlePage') as HTMLInputElement;
        const fullPageDom = this.print.querySelector('.jumpPage-center span#fullPage');
        const curPage = +singlePageInput.value;
        const fullPage = +fullPageDom.innerHTML;
        let index;
        switch (dom.id) {
            case 'jumpHead': {
                index = 1;
                break;
            }
            case 'jumpPre': {
                index = curPage - 1 < 1 ? 1 : curPage - 1;
                break;
            }
            case 'jumpNext': {
                index = curPage + 1 > fullPage ? fullPage : curPage + 1;
                break;
            }
            case 'jumpTail': {
                index = fullPage;
                break;
            }
        }
        if (index) {
            const contDiv = this.reactVm?.current.myRef?.current?.querySelector('div.ReactVirtualized__Grid__innerScrollContainer');
            if (contDiv) {
                const divPage = contDiv.querySelector(' div[page-index="' + (index - 1) + '"]');
                if (divPage) {
                    contDiv.scrollTop += divPage.getBoundingClientRect().top;

                }
            }
            singlePageInput.value = '' + index;
            this.updateCurPageBarClass(curPage, fullPage);
        }
    }

    /**  更新跳转箭头的颜色 */
    private updateCurPageBarClass = (curPage: number, fullPage: number) => {
        const barDom = this.print.querySelector('.print-dialog-box-body-c div#curPageBar');
        const jumpHead = barDom.querySelector('#jumpHead');
        const jumpPre = barDom.querySelector('#jumpPre');
        const jumpNext = barDom.querySelector('#jumpNext');
        const jumpTail = barDom.querySelector('#jumpTail');
        if (curPage <= 1) {
            jumpHead.classList.remove('activeLabel');
            jumpPre.classList.remove('activeLabel');
            this.updateSVGPathColor('#ACB4C1', jumpHead);
            this.updateSVGPathColor('#ACB4C1', jumpPre);
        } else {
            jumpHead.classList.add('activeLabel');
            jumpPre.classList.add('activeLabel');
            this.updateSVGPathColor('#54627B', jumpHead);
            this.updateSVGPathColor('#54627B', jumpPre);
        }
        if (curPage >= fullPage) {
            jumpNext.classList.remove('activeLabel');
            jumpTail.classList.remove('activeLabel');
            this.updateSVGPathColor('#ACB4C1', jumpNext);
            this.updateSVGPathColor('#ACB4C1', jumpTail);
        } else {
            jumpNext.classList.add('activeLabel');
            jumpTail.classList.add('activeLabel');
            this.updateSVGPathColor('#54627B', jumpNext);
            this.updateSVGPathColor('#54627B', jumpTail);
        }
    }

    /** 变更svg中path的颜色 */
    private updateSVGPathColor(color: string, svg: any): void {
        const paths = svg.querySelectorAll('path');
        if (paths) {
            for (let i = 0; i < paths.length; i++) {
                const path = paths[i];
                if (path) {
                    path.setAttribute('fill', color);
                }
            }
        }
    }
    // private getContents(): void {
    //     return this.documentCore.getContent();
    // }

}
