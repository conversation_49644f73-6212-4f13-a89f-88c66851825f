# 打印预览中临时 Document 对象分析

## 概述

在编辑器的打印预览功能中，系统会创建一个临时的 `DocumentCore` 对象，用于在不影响原始文档的情况下展示打印效果。这个临时对象在整个打印预览过程中扮演着重要角色，会根据用户的操作多次被修改，以实现不同的打印预览效果。

## 流程分析

### 1. 创建临时 Document 对象

在 `Modal.initPrint` 函数中，通过以下步骤创建并初始化临时 document 对象：

```typescript
private initPrint(options?: any): void {
    const document = this.initPrintDialog();
    const documentCore = this.documentCore;
    
    // ... 其他代码 ...
    
    const content = documentCore.getDocContent();
    // ... 其他代码 ...
    
    document.addDocContent(content, undefined, newOptions);
    document.changeRevisionState2(documentCore.isFinalRevision());
    document.getDocument()
            .setHeaderFooterInterface(documentCore.isSetHeadFooterInterface());
    document.getDocument()
            .setDynamicGridLine(documentCore.getDocument()
                        .isDynamicGridLine(), documentCore.getDocument()
                                .getDynamicGridLineColor());
    
    // ... 其他代码 ...
}
```

其中 `initPrintDialog` 函数创建了 `DocumentCore` 对象并将其设置到 `PrintDialog` 实例中：

```typescript
private initPrintDialog(): any {
    if (this.printDialog && this.printDialog.getHost() === this.host) {
        return this.printDialog.getDocumentCore();
    }
    this.printDialog = new PrintDialog(null, this.host);
    const document = new DocumentCore(undefined, false);
    this.printDialog.setDoc(document);
    return document;
}
```

### 2. 保存临时 Document 对象

临时 document 对象通过 `this.printDialog.setDoc(document)` 被保存在 `PrintDialog` 实例的 `documentCore` 属性中：

```typescript
public setDoc(documentCore: DocumentCore): void {
    this.docId = documentCore.getCurrentId();
    this.documentCore = documentCore; // set to reference, fill content later
}
```

### 3. 使用临时 Document 对象

在打印预览过程中，这个临时 document 对象被用于生成打印预览内容：

```typescript
public updateContent(isFirst: boolean = true, bCprint: boolean = false): void {
    // ... 其他代码 ...
    this.reactVm.setDocumentCore(this.documentCore, bCprint);
    // ... 其他代码 ...
}
```

### 4. 修改临时 Document 对象

在用户交互过程中，临时 document 对象会根据用户的操作被多次修改：

#### 4.1 清洁模式修改

```typescript
public cleanModeChangeBase(cleanMode: CleanModeType, bCprint: boolean): void {
    const printLogicDocument = this.documentCore.getDocument(); // print document
    const content = this.host.state.documentCore.getDocContent(); // main document
    
    // connect main's DocContent(detached from main reference) with printDoc
    content.styles.background = printLogicDocument.styles.copyBackground();
    
    const bFinalRevision = this.documentCore.isFinalRevision();
    
    printLogicDocument.addDocContent(content, cleanMode, this.printData);
    
    printLogicDocument.changeRevisionState2(bFinalRevision);
    
    // ... 其他代码 ...
    
    this.documentCore.recalculateAllForce();
    
    this.updateContent(false, bCprint);
    this.reactVm.handleRefresh();
}
```

#### 4.2 水印设置修改

```typescript
public watermarkChangeBase(checked: boolean): void {
    const printLogicDocument = this.documentCore.getDocument();
    
    const editorBackground = printLogicDocument.getEditorBackground();
    editorBackground.watermarkEnabled = checked;
    
    this.reactVm.handleRefresh();
}
```

#### 4.3 网格线设置修改

```typescript
public printGridLineChangeBase(checked: boolean): void {
    const printLogicDocument = this.documentCore.getDocument();
    
    if (checked !== printLogicDocument.isDynamicGridLine()) {
        printLogicDocument.setDynamicGridLine(checked);
        this.reactVm.handleRefresh();
    }
}
```

#### 4.4 页面属性修改

```typescript
protected setPageProperty(): void {
    // ... 其他代码 ...
    this.documentCore.setPageProperty(actPage);
    this.refreshDialog(actPage);
}

private resetPageProperty(): void {
    this.documentCore.setPageProperty(this.pageProperty);
    this.refreshDialog(this.pageProperty);
}
```

### 5. 清理临时 Document 对象

当打印对话框关闭时，通过 `onClose` 回调函数清理这个临时 document 对象：

```typescript
this.printDialog.onClose(() => {
    if (bTrackRevisions) {
        documentCore.setTrackRevisions(bTrackRevisions);
    }
    this.printDialog.clearContent();
    this.printDialog.clearDom();
    documentCore.recalculateAllForce();
    this.refresh(true);
});
```

## 临时 Document 对象的作用

1. **隔离修改**：在不影响原始文档的情况下，创建一个可以自由修改的文档副本用于预览或打印。

2. **实时预览**：允许用户在打印前查看文档在不同设置下的打印效果，如清洁模式、水印、网格线等。

3. **用户交互**：根据用户的操作（如更改清洁模式、显示/隐藏水印、显示/隐藏网格线等），实时更新打印预览效果。

4. **资源管理**：当打印预览结束时，临时 document 对象会被清理，释放资源。

## 结论

临时 document 对象在 `initPrint` 函数结束后并不是无作用的，而是被保存在 `PrintDialog` 实例中，并在整个打印预览过程中发挥重要作用。它会根据用户的操作多次被修改，以实现不同的打印预览效果，直到打印对话框关闭时才会被清理。这种设计模式使得系统能够在不影响原始文档的情况下，提供丰富的打印预览功能。

## 控件元素的打印隐藏功能分析与修复

### 概述

`bPrintSelected` 属性用于控制各类控件元素（如 `NewControlText`、`NewControlSection` 等）在打印预览和最终打印时是否显示。当设置为 `true` 时，元素应该仅在打印时被隐藏，而在正常编辑模式下保持可见。

### 问题分析

在原有实现中，存在以下几个关键问题：

1. **立即隐藏问题**：在 `setPrintSelected` 方法中，当 `bPrintSelected` 属性被设置为 `true` 时，会立即调用 `setHidden(true)` 方法将控件隐藏，导致控件在正常编辑模式下也不可见。

2. **边框隐藏不完全**：在打印预览时，虽然控件内容被隐藏了，但控件的边框仍然可见，特别是对于节控件和特殊控件（如签名框、按钮等）。

3. **递归隐藏不完整**：对于嵌套控件（如节控件内的子控件），隐藏状态未能正确递归传播，导致部分内容在打印时仍然可见。

### 解决方案

#### 1. 修改 `setPrintSelected` 方法

在 `NewControlText` 和 `NewControlSection` 类中，修改 `setPrintSelected` 方法，移除立即隐藏控件的代码：

```typescript
public setPrintSelected(bPrintSelected: boolean): number {
    if (this.bPrintSelected === bPrintSelected || bPrintSelected == null) {
        return ResultType.UnEdited;
    }

    const history = this.getHistory();
    if (history) {
        history.addChange(new ChangeNewControlPrintChecked(this, this.bPrintSelected, bPrintSelected));
    }

    this.bPrintSelected = bPrintSelected;
    this.setDirty(); // 只标记控件为脏，不立即隐藏

    return ResultType.Success;
}
```

#### 2. 在 `toPrint` 方法中处理隐藏

在 `toPrint` 方法中，根据 `bPrintSelected` 属性决定是否隐藏控件，这样控件只会在打印预览时隐藏：

```typescript
public toPrint(): void {
    super.toPrint();
    if (this.isHidden()) {
        return;
    }

    if (this.bPrintSelected) {
        this.setHidden(true, false); // 在打印预览时隐藏控件及其子控件
    }
}
```

#### 3. 增强 `setPrintPortionHide` 方法

在 `Paragraph.ts` 文件中，增强 `setPrintPortionHide` 方法，确保所有类型的控件（节控件、特殊控件和普通控件）在设置了打印隐藏属性时都能正确隐藏其内容和边框：

```typescript
// 处理特殊控件
if (types2.includes(newControlProp.newControlType)) {
    if (newControlProp.printSelected) {
        // 隐藏开始边框
        portion.setHidden(true);
        
        // 隐藏内容和结束边框
        const contents = portion.paragraph.content;
        for (let index = porIndex, len = contents.length; index < len; index++) {
            const curPortion = contents[index];
            if (curPortion.isNewControlEnd()) {
                curPortion.setHidden(true);
                break;
            }
            curPortion.setHidden(true);
        }
    }
    return true;
}
```

### 实现效果

通过以上修改，实现了以下效果：

1. 控件的打印隐藏属性只影响打印预览，不影响正常编辑模式下的显示。
2. 在打印预览时，所有设置了打印隐藏属性的控件（无论类型）都能完全隐藏，包括内容和边框。
3. 对于嵌套控件，隐藏状态能够正确递归传播，确保所有子控件也被隐藏。

### 属性定义和设置

在各控件类中，`bPrintSelected` 属性被定义为一个私有布尔值：

```typescript
private bPrintSelected: boolean;
```

这个属性在控件初始化时从传入的属性对象中获取值：

```typescript
this.bPrintSelected = property.printSelected === undefined ? false : property.printSelected;
```
    
    if (this.bPrintSelected) {
        this.setHidden(true, false);
    }
    
    // ... 其他代码 ...
}
```

这段代码是关键，它表明当 `bPrintSelected` 设置为 `true` 时，控件会被隐藏。

### 打印预览流程中的处理

在打印预览流程中，处理逻辑如下：

1. 当用户调用 `initPrint` 函数进入打印预览模式时，会创建一个临时的 `DocumentCore` 对象。

2. 通过 `document.addDocContent(content, undefined, newOptions)` 将当前文档的内容添加到这个临时对象中。

3. 在 `addDocContent` 方法中，会遍历文档内容中的每个元素，并调用 `item.addDocContentChild(option, cleanMode)` 方法。

4. 在这个过程中，对于每个 `NewControlText` 元素，如果它的 `bPrintSelected` 属性为 `true`，那么在 `setPrintSelected` 方法中会调用 `setHidden(true, false)` 将其隐藏。

5. 这样，在打印预览和最终打印的文档中，这些设置了 `bPrintSelected = true` 的控件就不会显示。

### 业务逻辑位置

这个业务逻辑主要在以下位置处理：

1. **属性定义和初始化**：在各个控件类（如 `NewControlText`、`NewControlRadio` 等）的构造函数中。

2. **关键处理逻辑**：在各个控件类的 `setPrintSelected` 方法中，特别是当 `bPrintSelected` 为 `true` 时调用 `setHidden` 方法将控件隐藏。

3. **打印预览流程**：在 `Document` 类的 `addDocContent` 方法和相关的 `addDocContentChild` 方法链中，这些方法会在打印预览过程中被调用，从而触发控件的 `setPrintSelected` 方法。

### 结论

`bPrintSelected` 属性的作用是控制元素在打印预览和最终打印时是否显示。当设置为 `true` 时，元素会在打印时被隐藏。这个功能可能用于那些只需要在编辑时显示，但在打印时不需要显示的辅助信息或控件。

## 为 NewControlSection 类添加打印时隐藏功能

经过分析，打印预览流程对所有控件类型的处理是一致的，没有对 `NewControlSection` 类型的特殊排斥。因此，我们可以为 `NewControlSection` 类添加与 `NewControlText` 类相同的打印时隐藏功能。

