const fs = require('fs-extra');
const archiver = require('archiver');
const path = require('path');

// 定义绝对路径
const projectRoot = process.cwd();
const targetRoot = path.join(path.dirname(projectRoot), 'hzeditor-encryption-server-go');

// 调试版本使用不同的目录和文件名
const staticFilesPath = path.join(projectRoot, 'staticfiles-debug');
const dictIframePath = path.join(projectRoot, 'dist', 'iframe-debug'); // 调试版本输出目录
const zipFilePath = path.join(projectRoot, 'static.zip');
const targetZipFilePath = path.join(targetRoot, 'static.zip');

// 清空目录内容
async function clearDirectory(directoryPath) {
    try {
        await fs.emptyDir(directoryPath);
        console.log(`已清空目录: ${directoryPath}`);
    } catch (err) {
        console.error(`清空目录${directoryPath}时出错:`, err);
    }
}

// 创建目录（如果不存在）
async function ensureDirectory(directoryPath) {
    try {
        await fs.ensureDir(directoryPath);
        console.log(`确保目录存在: ${directoryPath}`);
    } catch (err) {
        console.error(`创建目录${directoryPath}时出错:`, err);
    }
}

// 复制目录内容
async function copyDirectory(srcPath, destPath) {
    try {
        await fs.copy(srcPath, destPath);
        console.log(`已复制目录内容，从 ${srcPath} 到 ${destPath}`);
    } catch (err) {
        console.error(`复制目录时出错，从 ${srcPath} 到 ${destPath}:`, err);
    }
}

// 删除文件
async function removeFile(filePath) {
    try {
        if (await fs.pathExists(filePath)) {
            await fs.remove(filePath);
            console.log(`已删除文件: ${filePath}`);
        } else {
            console.log(`文件不存在: ${filePath}`);
        }
    } catch (err) {
        console.error(`删除文件${filePath}时出错:`, err);
    }
}

// 压缩目录（指定固定的根目录名）
async function zipDirectoryWithFixedName(sourceDir, outPath, zipRootDirName) {
    const output = fs.createWriteStream(outPath);
    const archive = archiver('zip', { zlib: { level: 9 } });
    
    return new Promise((resolve, reject) => {
        output.on('close', () => {
            console.log(`已压缩 ${archive.pointer()} 总字节到 ${outPath}`);
            resolve();
        });
        
        archive.on('error', (err) => reject(err));
        archive.pipe(output);
        
        // 遍历源目录内容并添加到zip，但使用固定的根目录名
        const items = fs.readdirSync(sourceDir);
        items.forEach(item => {
            const fullPath = path.join(sourceDir, item);
            const stats = fs.statSync(fullPath);
            
            if (stats.isDirectory()) {
                archive.directory(fullPath, path.join(zipRootDirName, item));
            } else {
                archive.file(fullPath, { name: path.join(zipRootDirName, item) });
            }
        });
        
        archive.finalize();
    });
}

// 复制文件
async function copyFile(srcPath, destPath) {
    try {
        await fs.copy(srcPath, destPath);
        console.log(`已复制文件，从 ${srcPath} 到 ${destPath}`);
    } catch (err) {
        console.error(`复制文件时出错，从 ${srcPath} 到 ${destPath}:`, err);
    }
}

// 主函数，执行所有任务
async function main() {
    try {
        console.log('开始处理调试版本...');
        
        // 确保调试静态文件目录存在
        await ensureDirectory(staticFilesPath);
        
        // 清空调试静态文件目录
        await clearDirectory(staticFilesPath);
        
        // 删除旧的调试版本zip文件（如果存在）
        await removeFile(zipFilePath);
        
        // 检查调试构建输出目录是否存在
        if (await fs.pathExists(dictIframePath)) {
            console.log('找到调试构建输出目录，继续处理...');
            
            // 复制调试构建输出到静态文件目录
            await copyDirectory(dictIframePath, staticFilesPath);
            
            // 压缩静态文件目录为zip，但在压缩包内使用"staticfiles"作为根目录名
            await zipDirectoryWithFixedName(staticFilesPath, zipFilePath, 'staticfiles');
            
            // 复制zip文件到目标位置
            await copyFile(zipFilePath, targetZipFilePath);
            
            console.log('调试版本静态文件处理完成！');
        } else {
            console.error(`调试构建输出目录不存在: ${dictIframePath}`);
            console.log('请先运行 npm run pack:watersite:debug 构建调试版本');
            return;
        }
        
        console.log('调试版本处理完成！');
    } catch (err) {
        console.error('主函数执行出错:', err);
    }
}

main(); 