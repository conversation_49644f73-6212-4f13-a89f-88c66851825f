import { XmlComponent, XmlAttributeComponent } from '../../../xml-components';

export interface IPageNumAttributesProperties {
    pageNumType: number;
    pageNumString: string;
    startIndex: number;
}

// class PageNumAttributes extends XmlAttributeComponent<IPageNumAttributesProperties> {
//     // <T> -> type variable, shape this.root in base
//     protected xmlKeys: any = {
//         pageNumType: 'w:pgNumString',
//         pageNumString: 'w:pgNumType',
//         startIndex: 'w:startIndex',
//     };
// }

export class PageNum extends XmlComponent {
    constructor(props: IPageNumAttributesProperties, totalPages?: number, curPage?: number) {
        super('w:pgNum');
        if (props.pageNumType != null) {
            this.root.push(new PageNumType(props.pageNumType + ''));
        }
        if (props.pageNumString != null) {
            this.root.push(new PageNumString(props.pageNumString));
        }
        if (props.startIndex != null) {
            this.root.push(new StartIndex(props.startIndex + ''));
        }
        if (totalPages != null) {
            this.root.push(new PageCount(totalPages + ''));
        }
        if (curPage != null) {
            this.root.push(new PageCurrent(curPage + ''));
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class PageNumString extends XmlComponent {
    constructor(pageNumString: string) {
        super('w:pgNumString');
        if (pageNumString) {
            this.root.push(pageNumString);
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class PageNumType extends XmlComponent {
    constructor(pageNumType: string) {
        super('w:pgNumType');
        if (pageNumType) {
            this.root.push(pageNumType);
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class StartIndex extends XmlComponent {
    constructor(startIndex: string) {
        super('w:startIndex');
        if (startIndex) {
            this.root.push(startIndex);
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class PageCount extends XmlComponent {
    constructor(page: string) {
        super('w:pageCount');
        if (page) {
            this.root.push(page);
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class PageCurrent extends XmlComponent {
    constructor(page: string) {
        super('w:pageCurrent');
        if (page) {
            this.root.push(page);
        }
    }
}
