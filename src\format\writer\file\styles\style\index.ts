// The whole file shouldn't be used for now

// tslint:disable-next-line: ordered-imports
import { XmlComponent, XmlAttributeComponent } from '../../xml-components';
import { Name } from './component';
import { ParagraphProperties } from '../../paragraph';
import { RunProperties } from '../../paragraph/run/properties';

export interface IStyleAttributes {
    readonly type?: string;
    readonly styleId?: string;
    // readonly default?: boolean;
    // readonly customStyle?: string;
}

class StyleAttributes extends XmlAttributeComponent<IStyleAttributes> {
    protected readonly xmlKeys: any = {
        type: 'w:type',
        styleId: 'w:styleId',
        // default: "w:default",
        // customStyle: "w:customStyle",
    };
}

export class Style extends XmlComponent {
    constructor(attributes: IStyleAttributes, name?: string) {
        super('w:style');
        this.root.push(new StyleAttributes(attributes));
        if (name) {
            this.root.push(new Name(name));
        }
    }

    public push(styleSegment: XmlComponent): void {
        this.root.push(styleSegment);
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ParagraphStyle extends Style {
    private readonly paragraphProperties: ParagraphProperties;
    private readonly runProperties: RunProperties;

    constructor(styleId: string, name?: string) {
        super({ type: 'paragraph', styleId }, name);

        // this.paragraphProperties = new ParagraphProperties();
        // this.runProperties = new RunProperties();
        // this.root.push(this.paragraphProperties);
        // this.root.push(this.runProperties);
    }

    // public basedOn(parentId: string): ParagraphStyle {
    //     this.root.push(new BasedOn(parentId));
    //     return this;
    // }

    // public quickFormat(): ParagraphStyle {
    //     this.root.push(new QuickFormat());
    //     return this;
    // }

    // public next(nextId: string): ParagraphStyle {
    //     this.root.push(new Next(nextId));
    //     return this;
    // }

}
