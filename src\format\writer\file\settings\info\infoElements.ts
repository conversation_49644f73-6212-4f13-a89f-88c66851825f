import { XmlComponent } from '../../xml-components';

export class Pages extends XmlComponent {
  constructor(pageNum: number) {
      super('Pages');
      if (pageNum != null) {
        this.root.push(pageNum + '');
      }
  }
}

export class Application extends XmlComponent {
  constructor(appName: string) {
      super('Application');
      if (appName != null) {
        this.root.push(appName);
      }
  }
}

// tslint:disable-next-line: max-classes-per-file
export class Version extends XmlComponent {
  constructor(versionNum: number) {
      super('Version');
      if (versionNum != null) {
        this.root.push(versionNum + '');
      }
  }
}

// tslint:disable-next-line: max-classes-per-file
export class ProtectMode extends XmlComponent {
  constructor(bProtect: number) {
      super('ProtectMode');
      if (bProtect != null) {
        this.root.push(bProtect + '');
      }
  }
}
