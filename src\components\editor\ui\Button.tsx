import * as React from 'react';
import '../style/button.less';

// interface IState {
//     bReflash: boolean;
// }

interface IProps {
    onClick?: (e: any) => void;
    type?: string;
    disabled?: boolean;
    children?: React.ReactNode; 
}

export default class CheckboxUI extends React.Component<IProps, {}> {
    private button: any;
    constructor(props: any) {
        super(props);
        this.button = React.createRef();
    }

    public render(): any {
        const props = this.props;
        const childrens = props.children as any;
        let className = 'editor-button';
        if (props.disabled === true) {
            className += ' disabled';
        }

        if (props.type) {
            className += ' ' + props.type;
        }

        return (
            <div className={className} ref={this.button}>
                <span>{childrens}</span>
            </div>
        );
    }

    public componentDidMount(): void {
        this.button.current.addEventListener('click', this.handleClick);
    }

    public componentWillUnmount(): void {
        this.button.current.addEventListener('click', this.handleClick);
    }

    private handleClick = (e: any): void => {
        if (this.props.disabled === true) {
            return;
        }

        if (typeof this.props.onClick === 'function') {
            this.props.onClick(e);
        }
    }
}
