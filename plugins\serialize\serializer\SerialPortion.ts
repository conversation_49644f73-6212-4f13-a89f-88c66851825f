import { pxToPt, pureHexColor, hexColorToName } from './Utils';
import { ISerialImageObj, ISerialPageNumObj, ISerialPortionObj, ISerialTextObj, SerialObjType } from '../serialInterface';
import ISerial, { FontStyleType, FontWeightType, ParaElementType, TextDecorationLineType, TextVertAlign } from './ISerial';
import SerialParagraph from './SerialParagraph';

export default class SerialPortion implements ISerial {

    constructor(private readonly portion: any) {}

    public serializedTo(collector: ISerialPortionObj[], parent?: SerialParagraph): ISerialPortionObj[] {
        let portionObj: ISerialPortionObj = this.buildPortionObj();
        // paragraph contains: portion, image
        let text = '';
        for (const content of this.portion.content) {
            const ctype = content.getType();
            if ([ParaElementType.ParaText,
                ParaElementType.ParaSpace,
                ParaElementType.ParaEnd,
                ParaElementType.ParaSym,
            ].includes(ctype)) {
                if (content.isParaEnd()) {
                    continue;
                }
                text += content.content;
            } else if (ctype === ParaElementType.ParaTab) {
                text += '\t';
            } else {
                // tslint:disable-next-line: no-unused-expression
                text && portionObj.children.push(this.buildSerialText(text));
                text = '';
            }
            if ([ParaElementType.ParaDrawing,
                ParaElementType.ParaMedEquation,
                ParaElementType.ParaSvgDrawing
            ].includes(ctype)) {
                portionObj.children.push(this.buildSerialDrawing(content));
            } else if (ctype === ParaElementType.ParaPageNum) {
                portionObj.children.push(this.buildPageNum(content));
            } else if (ctype === ParaElementType.ParaNewLine) {
                if (parent) {
                    collector.push(portionObj);
                    portionObj = this.buildPortionObj();
                    collector = parent.reBuildCollector();
                }
            }
        }
        // tslint:disable-next-line: no-unused-expression
        text && portionObj.children.push(this.buildSerialText(text));
        collector.push(portionObj);
        return collector;
    }

    private buildPortionObj(): ISerialPortionObj {
        return {
            type: SerialObjType.Portion,
            children: [],
        };
    }

    /** 构造普通文本 */
    private buildSerialText(text: string): ISerialTextObj {
        const prop = this.portion.textProperty;
        const textObj: any = {
            type: SerialObjType.Text,
            // children: [],  // 空children节点会遮盖 text属性内容
            break: 0,
            text,
            style: '',
            bold: prop.fontWeight === FontWeightType.Bold,
            boldComplexScript: false,
            italics: prop.fontStyle === FontStyleType.Italic,
            italicsComplexScript: false,
            // 下划线目前只有类型，无对应颜色
            underline: prop.textDecorationLine === TextDecorationLineType.Underline && {
                // color: '',
                type: 'single',
            } || undefined,
            emphasisMark: undefined,

            // font color: 000000 (without #)
            color: pureHexColor(prop.color) || '',
            size: pxToPt(prop.fontSize) * 2, // 6版本需要 *2
            // sizeComplexScript: '',
            rightToLeft: false,
            smallCaps: false,
            allCaps: false,
            strike: false,
            doubleStrike: false,
            subScript: prop.vertAlign === TextVertAlign.Sub,
            superScript: prop.vertAlign === TextVertAlign.Super,
            font: prop.fontFamily || '',
            highlight:  hexColorToName(prop.backgroundColor) || '', // 需要预设颜色： yellow, red, green, blue 等
            highlightComplexScript: '',
            characterSpacing: 0,
            shading: undefined,
            emboss: false,
            imprint: false,
            // revision: undefined, // TODO: 修订  （7版本才支持）
        };
        return textObj;
    }

    /** 构造图片对象 */
    private buildSerialDrawing(drawing: any): ISerialImageObj {
        const imgData = drawing.src;
        const drawingObj: ISerialImageObj = {
            type: SerialObjType.Image,
            data: imgData,
            transformation: {
                width: drawing.width,
                height: drawing.height
            }
        };
        return drawingObj;
    }

    /** 构造页码对象 */
    private buildPageNum(pageNum: any): ISerialPageNumObj {
        const numObj: ISerialPageNumObj = {
            type: SerialObjType.PageNum,
            pageNumType: pageNum.getPageNumType(),
            pageNumString: pageNum.getPageNumString(),
        };
        return numObj;
    }

}
