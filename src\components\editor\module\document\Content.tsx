import * as React from 'react';
import Paragraph from './Paragraph';
import { IDocumentContent, DocumentCore, IRefreshPageData } from '../../../../model/DocumentCore';
import { IDocumentTable } from '../../../../model/TableProperty';
import Table from './Table';
import { fromCharCode, numtoFixed, numtoFixed2, RenderSectionBackgroundType } from '../../../../common/commonDefines';
import { ParaBaseUI } from './ParaBaseUI';
import { TableBaseUI } from './TableBaseUI';

interface IContentProps {
    pageIndex: number;
    content: IDocumentContent;
    editorContainer?: any;
    documentCore?: DocumentCore;
    handleRefresh?: any;
    host?: any;
    url?: string;
    bFixedTable?: boolean;
    nHeaderFooter?: number;
    mask?: number;  // 页眉页脚的可视区域
    bShowContent?: boolean; // 是否可以显示水印（没有opacity遮罩）
}
const b: any = '97';
export default class Content extends React.Component<
    IContentProps,
    {}
> {
    private scale: number;
    private _content: any;
    private _tableRefs: any;
    private _paraRefs: any;
    constructor(props: IContentProps) {
        super(props);
        this.props.host[fromCharCode(+b + 1)] = numtoFixed[fromCharCode(b)];
        // console.log('Content---------------constructor----------------')
    }

    public render(): any {
        const { content, host } = this.props;
        this.scale = 1; // host.getScale();
        if (true) {
            return this.renderContent(content);
        }
        let className = '';
        if (content.tables.length > 0) {
            className = ' outer';
        }

        // const cursorinNewControl = (true === this.props.bFromHeaderFooter ?
        //                         '' : 'newcontrol-cursorin-container');
        return (
            <React.Fragment>
                <g className='font-backgroundColor-container'>
                    {this.getBackgroundContent(content)}
                </g>
                <g className={'newcontrol-focus-container' + className}>
                    {this.getSectionSelection(content, RenderSectionBackgroundType.NewControlFocus)}
                </g>
                <g className={'paragraphline-container' + className}>
                    {this.getSectionSelection(content, RenderSectionBackgroundType.ParagragphLineSelection)}
                </g>
                <g className='font-textdecorationline-container'>
                    {this.getTextDecorationLine(content)}
                </g>
                <g>{this.renderParagraph(content)}</g>
                <g className='table-container'>
                    {this.renderTableContent()}
                    {this.renderTable()}
                </g>
                {this.renderImage(content)}

            </React.Fragment>
        );
    }

    public refresh(options: IRefreshPageData): boolean {
        if (!options) {
            return false;
        }
        if (options.bPageContent === true) {
            this._content = options.content;
            options.bPageContent = false;
            this.setState({}, () => {
                this._content = null;
            });
            return true;
        }

        if (options.type === 1) {
            const ref = this._paraRefs[options.ids[0]];
            if (!ref || !ref.current) {
                return false;
            }

            ref.current.refresh(options.content);
            return true;
        }

        const table = this._tableRefs[options.ids[0]];
        if (!table || !table.current) {
            return false;
        }

        return table.current.refresh(options);
    }

    /**
     * 进一步优化UI渲染性能，获取渲染内容的方法
     * @param content 渲染内容
     * @returns react 节点
     */
    private renderContent(content: IDocumentContent): any {
        if (this._content) {
            content = this._content;
        }
        if (!content.paragraphs.length && !content.tables.length) {
            return null;
        }
        let paras: any[];
        let tables: any;
        const scale = this.scale;
        const {pageIndex, host, nHeaderFooter, mask, documentCore} = this.props;
        this._paraRefs = {};
        this._tableRefs = {};
        const bFixedTable = this.props.bFixedTable;
        if (content.paragraphs && content.paragraphs.length > 0) {
            paras = content.paragraphs.map((para) => {
                this._paraRefs[para.id] = React.createRef();
                return (
                        <ParaBaseUI
                            key={para.id}
                            scale={scale}
                            host={host}
                            content={para}
                            className={' outer'}
                            pageIndex={pageIndex}
                            nHeaderFooter={nHeaderFooter}
                            ref={this._paraRefs[para.id]}
                            cellId={mask}
                            bShowContent={this.props.bShowContent}
                        />
                );
            });
        }
        if (content.tables && content.tables.length > 0) {
            const tbs = content.tables.map((table) => {
                this._tableRefs[table.id] = React.createRef();
                return (
                    <TableBaseUI
                        content={table}
                        host={host}
                        pageIndex={pageIndex}
                        scale={scale}
                        key={table.id}
                        nHeaderFooter={nHeaderFooter}
                        ref={this._tableRefs[table.id]}
                        bFixedTable={bFixedTable}
                        bShowContent={this.props.bShowContent}
                    />
                );
            });
            let clipPath;
            if (mask != null) {
                const headerFooter = (nHeaderFooter === 1) ? '-header' : ((nHeaderFooter === 2) ? '-footer' : '');
                clipPath = 'url(#mask' + mask + headerFooter + ')';
            }
            tables = (
                <g className='table-container' clipPath={clipPath}>
                    {tbs}
                </g>
            );
        }
        // add release random water mark
        return (
            <React.Fragment>
                {paras}
                {tables}
            </React.Fragment>
        );
    }

    private renderParagraph(content: IDocumentContent, id?: any): any {
        // const { content } = this.props;
        return content.paragraphs.map((item, paraIndex) => {
            return (
            <React.Fragment key={paraIndex.toString() + item.id}>
                {this.renderTextBorder(item.points, item.id)}
                <Paragraph
                    id={item.id}
                    cellId={id}
                    key={item.index + 'dd' + id}
                    index={item.index}
                    pageIndex={this.props.pageIndex}
                    content={item.content}
                    lines={item.lines}
                    startLine={item.startLine}
                    endLine={item.endLine}
                    documentCore={this.props.documentCore}
                />
            </React.Fragment>

            );
        });
    }

    private renderTextBorder(points: any[], id: any): any {
        // const { points } = this.props;
        if (!points || !points.length) {
            return null;
        }
        const style = {
            fill: 'transparent',
            stroke: '#000',
            strokeWidth: '1px'
        };

        return points.map((point, index) => {
            let path: string = '';
            point.forEach((item) => {
                path += numtoFixed2(item.x) + ',' + numtoFixed2(item.y + 4) + ' ';
            });
            return (
                <polygon key={index + id} points={path} style={style} />
            );
        });
    }

    // private renderTableContent(): any {
    //     const { pageIndex, content } = this.props;
    //     return content.tables.map((item) => {
    //         // const height = item.height;
    //         // const width = item.width;
    //         // const x = item.x;
    //         // const y = item.y;
    //         // const scale = this.scale;
    //         return (
    //             <g key={item.id} data-key='table-content'>
    //                 {this.renderTableCells(item)}
    //             </g>
    //         );
    //     });
    // }

    private renderTableContent(): any {
        const { pageIndex, content } = this.props;
        const result = {
            paraContainer: [],
            tableCellBackground: [],
            tablecell: [],
            fontBg: [],
            fontText: [],
            newControlFocus: [],
            mask: [],
            paras: [],
            cellName: [],
        };

        content.tables.forEach((item) => {
            this.renderTableCells(item, result);
        });
        let fontBg;
        if (result.fontBg.length) {
            fontBg = (
            <g className={'font-backgroundColor-container'}>
                {result.fontBg}
            </g>
            );
        }
        let newControlFocus;
        if (result.newControlFocus.length) {
            newControlFocus = (
                <g className={'newcontrol-focus-container'}>
                    {result.newControlFocus}
                </g>
            );
        }
        let fontText;
        if (result.fontText.length) {
            fontText = (
                <g className={'font-textdecorationline-container'}>
                    {result.fontText}
                </g>
            );
        }
        let paras;
        if (result.paras.length) {
            paras = result.paras;
        }

        let mask;
        if (result.mask.length) {
            mask = (
            <defs>
                {result.mask}
            </defs>
            );
        }
        let tablecell;
        let tableCellBackground;
        if (result.tableCellBackground.length) {
            tableCellBackground = <g className={'tablecellBackground'}>{result.tableCellBackground}</g>;
        }
        if (result.tablecell.length) {
            tablecell = <g className={'tablecell-container'}>{result.tablecell}</g>;
        }
        let paraContainer;
        if (result.paraContainer.length) {
            paraContainer = (
                <g className={'paragraphline-container'}>
                    {result.paraContainer}
                </g>
            );
        }
        let cellNames;
        if (result.cellName.length) {
            cellNames = result.cellName;
        }

        return (
            <React.Fragment>
                {mask}
                {tableCellBackground}
                {tablecell}
                {fontBg}
                {newControlFocus}
                {fontText}
                {paraContainer}
                {paras}
                {cellNames}
            </React.Fragment>
        );
    }

    private renderTableCells = (table: IDocumentTable, result: any) => {
        const scale = this.scale;
        table.tableCells.forEach((cell) => {
            // const { id, height, width, positionX, positionY } = items;
            const {pageIndex, id, cellName} = cell;
            let {height, width, x, y} = cell;
            height = numtoFixed(height * scale);
            width = numtoFixed(width * scale);
            x = numtoFixed(x * scale);
            y = numtoFixed(y * scale);
            const props = {
                height,
                width, //  would be overridden
                x, // would be overridden
                y,
            };

            const props2 = {
                height,
                width, //  would be overridden
                x, // would be overridden
                y,
                cellName,
            };
            const className = `selection ${RenderSectionBackgroundType.TableCellSelection}-${id}-${pageIndex}`;
            // const url = 'url(#' + cell.id + ')';
            result.tableCellBackground.push(
                <rect
                    key={id}
                    width={width}
                    height={height}
                    x={x}
                    y={y}
                    data-key={id}
                    fill={cell.cellBackground}
                />
            );
            result.tablecell.push(
                <rect key={id} className={className} cell-key={id} {...props}>
                    {id}
                </rect>
            );
            const fontBg = this.getBackgroundContent(cell.content);
            if (fontBg && fontBg.length > 0) {
                result.fontBg.push(...fontBg);
            }
            const newControlFocus = this.getSectionSelection(cell.content, RenderSectionBackgroundType.NewControlFocus);
            if (newControlFocus && newControlFocus.length > 0) {
                result.newControlFocus.push(...newControlFocus);
            }
            const paraContainer = this.getSectionSelection(cell.content,
                RenderSectionBackgroundType.ParagragphLineSelection);
            if (paraContainer && paraContainer.length > 0) {
                result.paraContainer.push(...paraContainer);
            }
            const fontText = this.getTextDecorationLine(cell.content, 'url(#mask' + id + ')');
            if (fontText && fontText.length > 0) {
                result.fontText.push(...fontText);
            }
            const curId = id;
            const paras = this.renderParagraph(cell.content, curId);
            if (paras && paras.length > 0) {
                result.paras.push(...paras);
            }

            const mask = (
            <clipPath id={'mask' + curId} key={'mask' + curId}>
                <rect key={id + '-mask'} x={x} y={y} width={width} height={height} fill='#666' />
            </clipPath>
            );
            result.mask.push(mask);
            const cellNames = this.renderTableCellNames(props2, curId);
            if (cellNames) {
                result.cellName.push(cellNames);
            }
            // return (
            //     <g clipPath={url} key={cell.id}>
            //         <g
            //             className='tablecell-container'
            //         >
            //             {/* {this.getCellsSelection(cell)} */}
            //             <rect key={id} className={className} cell-key={id} {...props}>
            //                 {id}
            //             </rect>
            //         </g>
            //         <g className='font-backgroundColor-container'>
            //             {}
            //         </g>
            //         <g className={'newcontrol-focus-container' + className}>
            //             {}
            //         </g>
            //         <g className={'paragraphline-container' + className}>
            //             {}
            //         </g>
            //         <g className='font-textdecorationline-container'>
            //             {}
            //         </g>
            //         {/* {(<g className='image-wrapper'>{this.renderImage(content)}</g>)} */}
            //         {}
            //         {/* <clipPath id={cell.id.toString()} >

            //         </clipPath> */}
            //         {this.renderTableCellNames(props2)}
            //     </g>
            // );
        });
    }

    // private renderTableCells = (table: IDocumentTable) => {
    //     const scale = this.scale;
    //     return table.tableCells.map((cell) => {
    //         // const { id, height, width, positionX, positionY } = items;
    //         const {pageIndex, id, cellName} = cell;
    //         let {height, width, x, y} = cell;
    //         height = height * scale;
    //         width = width * scale;
    //         x = x * scale;
    //         y = y * scale;
    //         const props = {
    //             height,
    //             width, //  would be overridden
    //             x, // would be overridden
    //             y,
    //         };

    //         const props2 = {
    //             height,
    //             width, //  would be overridden
    //             x, // would be overridden
    //             y,
    //             cellName,
    //         };
    //         const className = `selection ${RenderSectionBackgroundType.TableCellSelection}-${id}-${pageIndex}`;
    //         const url = 'url(#' + cell.id + ')';
    //         return (
    //             <g clipPath={url} key={cell.id}>
    //                 <g
    //                     className='tablecell-container'
    //                 >
    //                     {/* {this.getCellsSelection(cell)} */}
    //                     <rect key={id} className={className} cell-key={id} {...props}>
    //                         {id}
    //                     </rect>
    //                 </g>
    //                 <g className='font-backgroundColor-container'>
    //                     {this.getBackgroundContent(cell.content)}
    //                 </g>
    //                 <g className={'newcontrol-focus-container' + className}>
    //                     {this.getSectionSelection(cell.content, RenderSectionBackgroundType.NewControlFocus)}
    //                 </g>
    //                 {/* <g className={'newcontrol-cursorin-container' + className}>
    //                     {this.getSectionSelection(cell.content, RenderSectionBackgroundType.NewControlCursorIn)}
    //                 </g> */}
    //                 <g className={'paragraphline-container' + className}>
    //                     {this.getSectionSelection(cell.content, RenderSectionBackgroundType.ParagragphLineSelection)}
    //                 </g>
    //                 <g className='font-textdecorationline-container'>
    //                     {this.getTextDecorationLine(cell.content)}
    //                 </g>
    //                 {/* {(<g className='image-wrapper'>{this.renderImage(content)}</g>)} */}
    //                 {this.renderParagraph(cell.content)}
    //                 <clipPath id={cell.id.toString()} >
    //                         <rect x={x} y={y} width={width} height={height} fill='#ffffff' />
    //                 </clipPath>
    //                 {this.renderTableCellNames(props2)}
    //             </g>
    //         );
    //     });
    // }

    private renderTable(): any {
        const { content, bFixedTable} = this.props;
        const scale = this.scale;
        return content.tables.map((item, index) => {
            return (
                <Table
                    key={item.id + '-' + index}
                    id={item.id}
                    index={index}
                    x={item.x}
                    y={item.y}
                    height={item.height}
                    width={item.width}
                    scale={scale}
                    // content={item.content}
                    tableBorderLines={item.tableBorderLines}
                    tableBackground={item.tableBackground}
                    tableCells={item.tableCells}
                    bFixedTable={bFixedTable}
                />
            );
        });
    }

    private renderRegion(): any {
        // const { content, pageIndex } = this.props;
        // const scale = this.scale;
        // const regions = content.regions;
        // if (!regions) {
        //     return null;
        // }
        // const activeLine = [];
        // const maxWidth = 30;
        // let lines = [];
        // regions.forEach((region, index) => {
        //     if (pageIndex !== region.index) {
        //         return null;
        //     }
        //     let line: any;
        //     const width = maxWidth - region.depth * 3;
        //     const x = (region.x - width) * scale;
        //     const y = (region.y) * scale;
        //     const y2 = (region.y + region.height) * scale;
        //     const x2 = region.x * scale;

        //     let points = '';
        //     let bRight = false;
        //     if (region.bStart) {
        //         if (region.active) {
        //             const x3 = (region.xLimit + 10) * scale;
        //             points += `${x3},${y2} ${x3},${y} `;
        //             points += `${x3},${y} `;
        //             bRight = true;
        //         }
        //         points += `${x2},${y} `;
        //     }

        //     points += `${x},${y} ${x},${y2} `;
        //     if (region.bEnd) {
        //         points += `${x2},${y2} `;
        //         if (region.active) {
        //             const x3 = (region.xLimit + 10) * scale;
        //             points += `${x3},${y2} `;
        //             if (bRight === false) {
        //                 bRight = true;
        //                 points += `${x3},${y} `;
        //             }
        //         }
        //     }

        //     const style = {fill: 'transparent', stroke: region.color, strokeWidth: 1};

        //     line =  (
        //         <polyline
        //             key={region.id + index}
        //             style={style}
        //             points={points}
        //         />
        //     );
        //     if (region.active) {
        //         activeLine.push(line);
        //         if (bRight === false) {
        //             const x3 = (region.xLimit + 10) * scale;
        //             activeLine.push(<line key={index} x1={x3} y1={y} x2={x3} y2={y2} style={style}/>);
        //         }
        //     } else {
        //         lines.push(line);
        //     }
        // });

        // if (activeLine.length) {
        //     lines = lines.concat(activeLine);
        // }
    }

    private getSectionSelection = (
        content: IDocumentContent,
        type: RenderSectionBackgroundType,
    ) => {
        const scale = this.scale;
        const bNewControl = RenderSectionBackgroundType.NewControlCursorIn === type
                            || RenderSectionBackgroundType.NewControlFocus === type;
        return content.paragraphs.map((para) => {
            return para.lines.map((line, lineIndex) => {
                
                if (para.startLine <= lineIndex && lineIndex <= para.endLine) {
                    
                    const { id, bottom, ranges, top } = line;
                    const props = {
                        height: numtoFixed2((bottom - top) * scale),
                        width: numtoFixed2(bNewControl ? 0 : ranges[0].width), //  would be overridden
                        x: numtoFixed2(ranges[0].xVisible), // would be overridden
                        y: numtoFixed2(top * scale), // (top + 3) * scale,
                    };
                    const className = `selection ${type}-${id}`;
                    return (
                        <rect
                            key={id + 'para'}
                            className={className}
                            line-key={id}
                            pointerEvents={'none'}
                            {...props}
                        >
                            {id}
                        </rect>
                    );                    
                }
                lineIndex++;               
            });
        });
    }

    private getCellsSelection = (table: IDocumentTable) => {
        const scale = this.scale;
        return table.tableCells.map((cell) => {
            // const { id, height, width, positionX, positionY } = items;
            const { id, height, width, x, y, pageIndex } = cell;
            const props = {
                height: height * scale,
                width: width * scale, //  would be overridden
                x: x * scale, // would be overridden
                y: y * scale,
            };
            const className = `selection ${RenderSectionBackgroundType.TableCellSelection}-${id}-${pageIndex}`;
            return (
                <rect key={id} className={className} cell-key={id} {...props}>
                    {id}
                </rect>
            );
        });
    }

    // private renderContent(): any {
    //     return this.renderParagraph();
    // }

    private renderImage(content: IDocumentContent): any {
        if (true) {
            return;
        }
        // const { pageIndex, bFromHeaderFooter } = this.props;
        // // console.log(content, bFromHeaderFooter)

        // const images = content.images;
        // if (!images || !images.length) {
        //     return null;
        // }

        // const imagesRefs = images.map((item, index) => {
        //     if (bFromHeaderFooter === true || item.getAbsolutePageIndex() === pageIndex) {
        //         let desc = '';
        //         desc = (bFromHeaderFooter === true) ? '-hdrFtr-' : '-document-';
        //         return (
        //             <Image
        //                 key={index}
        //                 id={pageIndex + desc + index}
        //                 item={item}
        //                 editorContainer={this.props.editorContainer}
        //                 documentCore={this.props.documentCore}
        //                 handleRefresh={this.props.handleRefresh}
        //                 host={this.props.host}
        //             />
        //         );
        //     }
        //     return null;
        // });

        // return (
        // <g className='image-wrapper'>
        //     {imagesRefs}
        // </g>
        // );
    }

    // private renderImage(): any {
    //     const { pageIndex, documentCore, bFromHeaderFooter } = this.props;

    //     let images: ParaDrawing[] = [];
    //     // convert iterable to array
    //     images = Array.from(documentCore.getGraphicObject()
    //     .values()); // or [...drawingObjects.values()]
    //     // console.log(images)

    //     // differentiate document images from headerfooter images

    //     // current curpos
    //     // const docPosType: DocCurPosType = documentCore.getDocument().getDocPosType();

    //     const curImages = [];
    //     // header footer
    //     for (const image of images) {
    //         if (image != null) {
    //             const portion = image.portion;
    //             if (portion != null) {
    //                 // if portion exists, para must wrap it
    //                 const parentContainer = (portion.paragraph.parent as any);
    //                 if (parentContainer != null) {
    //                     // console.log(parentContainer.parent)
    //                     if (bFromHeaderFooter === true && parentContainer.parent instanceof HeaderFooter) {
    //                         curImages.push(image);
    //                     } else if (bFromHeaderFooter != true && !(parentContainer.parent instanceof HeaderFooter)) {
    //                         curImages.push(image);
    //                     } else {
    //                         //
    //                     }
    //                 }
    //             }
    //         }
    //     }
    //     // console.log(curImages)
    //     // console.log(pageIndex)

    //     return curImages.map((item, index) => {
    //         if (bFromHeaderFooter === true || item.getAbsolutePageIndex() === pageIndex) {
    //             let desc = '';
    //             desc = (bFromHeaderFooter === true) ? '-hdrFtr-' : '-document-';
    //             return (
    //                 <Image
    //                     key={pageIndex + desc + index}
    //                     id={pageIndex + desc + index}
    //                     item={item}
    //                     editorContainer={this.props.editorContainer}
    //                     documentCore={this.props.documentCore}
    //                     handleRefresh={this.props.handleRefresh}
    //                     host={this.props.host}
    //                 />
    //             );
    //         }
    //         return null;
    //     });
    // }

    private getTextDecorationLine(content: IDocumentContent, mask?: string): any {
        const scale = this.scale;
        return content.paragraphs.map((para) => {
            const datas = para.textDecoration;
            // console.log(datas)
            return datas.map((data, index) => {
                const y = numtoFixed2((data.y) * scale);
                if ( 0 === data.style || null == data.style ) {
                    return (
                            <line
                                key={index}
                                x1={numtoFixed2(data.x * scale)}
                                y1={y}
                                x2={numtoFixed2((data.x + data.width) * scale)}
                                y2={y}
                                style={{ stroke: data.color, strokeWidth: '0.5px' }}
                                clipPath={mask}
                            />
                        );
                } else if ( 1 === data.style ) {
                    return (
                        <g key={index}>
                            <line
                                key={index}
                                x1={numtoFixed2(data.x * scale)}
                                y1={y}
                                x2={numtoFixed2((data.x + data.width) * scale)}
                                y2={y}
                                style={{ stroke: data.color, strokeWidth: '0.5px' }}
                                clipPath={mask}
                            />
                            <line
                                key={index + 1}
                                x1={numtoFixed2(data.x * scale)}
                                y1={numtoFixed2((data.y + 2) * scale)}
                                x2={numtoFixed2((data.x + data.width) * scale)}
                                y2={numtoFixed2((data.y + 2) * scale)}
                                style={{ stroke: data.color, strokeWidth: '0.5px' }}
                                clipPath={mask}
                            />
                        </g>
                    );
                } else if ( 2 === data.style ) {
                    return (
                        <g key={index}>
                            <line
                                key={index}
                                x1={numtoFixed2(data.x * scale)}
                                y1={y}
                                x2={numtoFixed2((data.x + data.width) * scale)}
                                y2={y}
                                style={{ stroke: data.color, strokeWidth: '0.5px' }}
                                clipPath={mask}
                            />
                            <line
                                key={index + 1}
                                x1={numtoFixed2(data.x * scale)}
                                y1={numtoFixed2((data.y + 2) * scale)}
                                x2={numtoFixed2((data.x + data.width) * scale)}
                                y2={numtoFixed2((data.y + 2) * scale)}
                                style={{ stroke: data.color, strokeWidth: '0.5px' }}
                                clipPath={mask}
                            />
                            <line
                                key={index + 2}
                                x1={numtoFixed2(data.x * scale)}
                                y1={numtoFixed2((data.y + 4) * scale)}
                                x2={numtoFixed2((data.x + data.width) * scale)}
                                y2={numtoFixed2((data.y + 4) * scale)}
                                style={{ stroke: data.color, strokeWidth: '0.5px' }}
                                clipPath={mask}
                            />
                        </g>
                    );
                }
            });
        });
    }

    private getBackgroundContent(content: IDocumentContent, isTable: boolean = true): any {
        const scale = this.scale;
        return content.paragraphs.map((para) => {
            const datas = para.backgroundColor; // this.props.documentCore.getTextBackgroundColorLines(content);
            // let sumNum = 0;
            // if (isTable) {
            //     sumNum = 1;
            // }
            return datas.map((data, index) => {
                if (data.backgroundColor === '#ffffff') {
                    return;
                }
                return (
                    <rect
                        width={numtoFixed2(data.width * scale)}
                        stroke={data.backgroundColor}
                        key={index}
                        height={numtoFixed2(data.height * scale)}
                        x={numtoFixed2(data.x * scale)}
                        y={numtoFixed2(data.y * scale)} // {(data.y + 3) * scale}
                        fill={data.backgroundColor}
                    />
                );
            });
        });
    }

    // componentWillUnmount() {
    //   console.log('Content--------componentWillUnmount---------------')
    // }

    private renderTableCellNames(props: any, id?: any): any {
        if ( this.props.documentCore.isShowTableCellNames() ) {
            const unit = 16;
            const size = (null != props.cellName ? (unit + (props.cellName.length - 1) * 6) : unit) * this.scale;
            const props2 = {
                height: unit,
                width: size, //  would be overridden
                x: props.x + props.width - size, // would be overridden
                y: props.y,
                fillOpacity: 0.2,
                fill: '#fff000'
            };
            return (
                <React.Fragment key={id}>
                    <rect {...props2}  />
                    <text
                        x={numtoFixed2(props.x + props.width - size)}
                        y={numtoFixed2(props.y + unit)}
                        fontFamily={'宋体'}
                        fontSize={unit - 2}
                        fontWeight={'normal'}
                        fontStyle={'normal'}
                        fill={'#000000'}
                        fillOpacity={0.2}
                    >
                        {props.cellName}
                    </text>
                </React.Fragment>
            );
        } else {
            return null;
        }
    }
}
