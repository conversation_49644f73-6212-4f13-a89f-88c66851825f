import * as React from 'react';
import Dialog from '../../ui/Dialog';
import Button from '../../ui/Button';
import Checkbox from '../../ui/CheckboxItem';
import { CustomPropertyElementType } from '../../../../common/commonDefines';
import CustomPropertyBtn from './CustomProperty';
import { IFRAME_MANAGER } from '../../../../common/IframeManager';
import NewComboBoxList from './NewComboBoxList';
import Input from '../../ui/Input';
import { NISTableCellCommon } from './NISTableCellCommonProps';

interface IDialogProps {
    documentCore: any;
    visible: boolean;
    id?: string;
    close: (name: string | number, bRefresh?: boolean) => void;
    refresh: (bRefresh: boolean, timeout?: number) => void;
}

interface IState {
    bRefresh: boolean;
}

export default class NISTableCellQuick extends React.Component<IDialogProps, IState> {
    private cell: any;
    // private tableProps: any;
    private visible: any;
    private bCustomProperty: boolean;
    private docId: number;
    nisProps: any;

    constructor(props: any) {
        super(props);
        this.cell = {
            bProtected: false,
            nisProperty: {
                bRetrieve: false,
                customProperty: undefined,
                serialNumber: undefined,
            },
            visible: undefined
        };
        this.state = {
            bRefresh: false,
        };
        this.nisProps = {};
        this.visible = this.props.visible;
        this.setDialogValue();
        this.docId = this.props.documentCore.getCurrentId();
    }

    public componentDidMount(): void {
        // this.boxRef.current.ownerDocument.addEventListener('click', this.docClick);
    }

    public componentWillUnmount(): void {
        // this.boxRef.current.ownerDocument.removeEventListener('click', this.docClick);
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        this.visible = nextProps.visible;
    }

    public render(): any {
        const nisProps = this.cell.nisProperty;
        return (
            <Dialog
                id={this.props.id}
                visible={this.visible}
                width={380}
                // close={this.close}
                open={this.open}
                preventDefault={false}
                title='快捷文本框'
                confirm={this.confirm}
                footer={this.renderFooter()}
                // id='table'
            >
                <div>
                    <div className='editor-line'>
                        <div className='w-70'>内部名称</div>
                        <div className='right-auto'>
                            <Input
                                name='nis-serialNumber'
                                value={this.cell.nisProperty.serialNumber}
                                onChange={this.onChange}
                                readonly={true}
                                placeholder={''}
                                disabled={true}
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <span className='table-label'>属性</span>
                    </div>
                    <div className='editor-line'>
                        <Checkbox
                            name='nis-bRetrieve'
                            value={this.cell.nisProperty.bRetrieve}
                            disabled={false}
                            onChange={this.onChange}
                        >
                            索引检索
                        </Checkbox>
                        <Checkbox
                            name='bProtected'
                            value={this.cell.bProtected}
                            disabled={false}
                            onChange={this.onChange}
                        >
                            禁止编辑
                        </Checkbox>
                    </div>
                    <div className='editor-line'>
                        <Checkbox
                            name='visible'
                            value={this.cell.visible}
                            disabled={false}
                            onChange={this.onChange}
                        >
                            网格线
                        </Checkbox>
                    </div>
                    <NISTableCellCommon prop={this.nisProps}/>
                    <div className='editor-line'>
                        <CustomPropertyBtn
                            name='nis-customProperty'
                            properties={this.cell.nisProperty.customProperty}
                            documentCore={this.props.documentCore}
                            onChange={this.onChange}
                            close={this.onClose}
                            id='bCustomProperty'
                            visible={this.bCustomProperty}
                            type={CustomPropertyElementType.TableCell}
                        />
                    </div>
                    <div className='editor-line'>
                        <span className='title'>下拉选项</span>
                    </div>
                    <NewComboBoxList
                        value={nisProps.items}
                        docId={this.docId}
                        onChange={this.onChange}
                        name='nis-items'
                    />
                </div>
            </Dialog>
        );
    }

    private renderFooter(): any {
        return (
            <span>
                <Button onClick={this.close}>取消</Button>
                <Button type='primary' onClick={this.confirm}>确认</Button>
            </span>
        );
    }

    private setDialogValue(): void {
        const cellProps = this.props.documentCore.getTableCellProps();
        const nisProperty = cellProps.nisProperty;
        // this.cell = cellProps; // TODO:  这种直接传reference的设法可能有问题？

        this.cell.bProtected = cellProps.bProtected;
        if (nisProperty) {
            const nisProps = this.cell.nisProperty;
            nisProps.serialNumber = nisProperty.serialNumber;
            nisProps.bRetrieve = nisProperty.bRetrieve;
            this.nisProps = cellProps.nisProperty;
            this.cell.visible = nisProperty.gridLine?.visible;
            nisProps.customProperty = nisProperty.customProperty ?
                                            nisProperty.customProperty.map((value) => {
                                                return value;
                                            }) : undefined;
            nisProps.items = nisProperty.items ? nisProperty.items.slice(0) : undefined;
        }
    }

    private open = (): void => {
        // const table = this.table;
        this.setDialogValue();
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private close = (bRefresh?: boolean): void => {
        this.props.close(this.props.id, bRefresh);
        this.visible = false;
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private onClose = (id: string, bRefresh: boolean): void => {
        this[id] = false;
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private onChange = (value: any, name: string): void => {
        if (name.indexOf('nis-') !== -1) {
            const trueName = name.slice('nis-'.length);
            this.cell.nisProperty[trueName] = value;
        } else {
            this.cell[name] = value;
        }
    }

    private confirm = (id?: any): void => {
        const { documentCore } = this.props;
        IFRAME_MANAGER.setDocId(this.docId);
        const cell = this.cell;
        cell.nisProperty.gridLine = {visible: cell.visible};
        // console.log(this.cell)
        documentCore.setTableCellProps(cell);

        this.close(true);
    }

}
