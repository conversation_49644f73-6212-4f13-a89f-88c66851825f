{"compileOnSave": false, "compilerOptions": {"baseUrl": "./", "outDir": "./lib", "sourceMap": true, "declaration": false, "module": "esNext", "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "downlevelIteration": true, "target": "es5", "jsx": "react", "typeRoots": ["node_modules/@types", "./features.d.ts"], "paths": {"@hz-editor/plugins": ["./plugins"], "@hz-editor/theme": ["./src/common/Theme.ts"], "@hz-editor/utils": ["./src/utils"], "@/*": ["src/*"]}, "lib": ["es2017", "dom"], "allowSyntheticDefaultImports": true}}