import * as React from 'react';
import { HierarchyLevel, INewControlProperty, ViewModeType } from '../../../../common/commonDefines';
import { DocumentCore, IDrawSelectionsLine } from '../../../../model/DocumentCore';
// import addressData from '../../../../common/resources/china-division.json';
// import addressData from '../../../../common/resources/Regions.json';
// import addressData from '../../../../common/resources/china-city.json';
import { getPagePadding } from '../../../../common/commonMethods';
import { chinaCity, IAddressKey } from '@/common/ChinaCity';

interface INewAddressboxListProps {
    // host?: any;
    documentCore: DocumentCore;
    newControlPropety: INewControlProperty;
    scale?: number;
    refresh?: () => void;
    closeNewBoxList?: () => void;
    lineHeight?: number;
    position?: {left: number, top: number, width: number, deltaLeft?: number};
    bFromEnter?: boolean;
    host: any;
}

interface INewAddressboxListPropsState {
    bRefresh: boolean;
    provinceCode: string;
    bShowCity: boolean;
    cityCode: string;
    bShowCounty: boolean;
    countyCode: string;

    // maybe for apis
    provincePair: IAddressPair;
    cityPair: IAddressPair;
    countyPair: IAddressPair;
}

export interface IAddressPair {
    code: string;
    name: string;
}

interface IAddressIndex {
    provinceIndex: number;
    cityIndex: number;
    countyIndex: number;
}

export class NewAddressbox extends React.Component<INewAddressboxListProps, INewAddressboxListPropsState> {
    private documentCore: DocumentCore;
    private newControlPropety: INewControlProperty;
    private top: number;
    private addressCollection: IAddressKey[]; // 省份
    private addrContainerRef: any;
    private citys: IAddressKey[]; // 城市
    private countys: IAddressKey[]; // 县区

    constructor(props: any) {
        super(props);
        this.documentCore = props.documentCore;
        this.newControlPropety = props.newControlPropety;
        // console.log(this.newControlPropety)
        // this.addressCollection = composeAddressHierarchy(addressData);
        this.addressCollection = chinaCity.getProvinces();

        const {province: provincePair, city: cityPair, county: countyPair, hierarchy} = this.newControlPropety;
        // connect props with state
        // const {provinceIndex, cityIndex, countyIndex} = this.getAddressIndexFromProps();

        // set certain states based on hierarchy
        let bShowCity = false;
        let bShowCounty = false;
        if (cityPair != null && hierarchy >= HierarchyLevel.Second) { // may through api
            bShowCity = true;
        }
        if (countyPair != null && hierarchy === HierarchyLevel.Third) { // may through api
            bShowCounty = true;
        }

        this.state = {
            bRefresh: false,
            provinceCode: provincePair?.code,
            cityCode: cityPair?.code,
            bShowCity,
            bShowCounty,
            countyCode: countyPair?.code,
            provincePair,
            cityPair,
            countyPair,
        };
        try {
            // TODO
            this.addrContainerRef = React.createRef();
        } catch (error) {
            // tslint:disable-next-line: no-console
            console.log(error);
        }
    }

    public componentDidMount(): any {
        const dom = this.addrContainerRef.current;
        if (!dom) {
            return;
        }

        // menu up
        try {
            let top = dom.getBoundingClientRect().top;
            const maxHeight = document.firstElementChild.clientHeight - top;
            const currentHeight = dom.clientHeight;
            const subHeight = currentHeight - maxHeight;
            if (subHeight > 1) {
                // menu up
                top = this.top - currentHeight - this.props.lineHeight - 10;
                dom.style.top = top + 'px';
            }
        } catch (error) {
            // tslint:disable-next-line: no-console
            console.log(error);
        }

        if (this.props.bFromEnter) {
            this.mousedownEvent(null);
            // setTimeout(() => {
            //     dom.querySelector('li').focus();
            // }, 150);
        }

        // true entrance
        dom.addEventListener('click', this.containerClick);
        dom.addEventListener('keydown', this.keydown);

        this.focusElems();

    }

    public componentWillUnmount(): void {
        const dom = this.addrContainerRef.current;
        if (!dom) {
            console.log('undelete event address');
            return;
        }
        dom.removeEventListener('click', this.containerClick);
        dom.removeEventListener('keydown', this.keydown);
    }

    public render(): any {
        const position = this.props.position;
        let left: number;
        let top: number;
        let width: number;
        let lastLine: IDrawSelectionsLine;
        // this.prepareAddress();
        const maxWidth = 170;
        // console.log(position)
        if (position) {
            left = position.left;
            top = position.top;
            width = position.width;
            width = width > maxWidth ? maxWidth : width;
        } else {
            try {
                const scale = this.props.scale;
                const documentCore = this.documentCore;
                const bounds = documentCore.getNewControlsFocusBounds();
                lastLine = bounds.bounds[bounds.bounds.length - 1];
                const viewMode = documentCore.getViewMode();
                const defaultItemWidth = 170;

                let subHeight = 0;
                let subLeft = 0;
                if (viewMode === ViewModeType.WebView) {
                    subHeight -= getPagePadding(documentCore).top;
                } else if (viewMode === ViewModeType.CompactView) {
                    subLeft = getPagePadding(documentCore).left + getPagePadding.PaddingLeft;
                }

                // console.log(lastLine)
                left = (lastLine.x  - subLeft) * scale;
                top = (lastLine.y + lastLine.height + subHeight) * scale; // + 3;
                width = defaultItemWidth > lastLine.width ? defaultItemWidth : lastLine.width;
                width = width > maxWidth ? maxWidth : width;
            } catch (error) {
                // tslint:disable-next-line: no-console
                console.log(error);
            }
        }
        this.top = top;

        // console.log(top, left, width)
        const {bShowCity, bShowCounty} = this.state;
        width = bShowCity ? (bShowCounty ? width * 3 : width * 2) : width;

        const pScale = this.props.scale != null ? this.props.scale : 1;
        // width *= pScale;

        // modify left according to page xLimit
        const logicDocument = this.documentCore.getDocument();
        if (logicDocument != null && bShowCity) {
            const pages = logicDocument.getPages();
            if (pages != null && pages.length > 0) {
                let xLimit = pages[0].xLimit * pScale;
                // editor can have a delta distance, which is needed for addressbox to determine new xLimit
                if (position != null && position.deltaLeft != null) {
                    // console.log(position.deltaLeft)
                    xLimit += position.deltaLeft;
                }
                if (xLimit > 0) {
                    if (left + width > xLimit) {
                        left = xLimit - width;
                    }
                }
            }
        }

        // console.log(top, left, width)
        // modify left according to range(to confirm)
        // const xEnd = lastLine != null ? lastLine.line.ranges[0].xEnd * pScale : -1;
        // if (xEnd > 0) {
        //     if (left + width > xEnd) {
        //         left = xEnd - width;
        //     }
        // }
        // console.log(xEnd, left, width)

        return (
            <div className='new-address-list' tabIndex={-1} ref={this.addrContainerRef} style={{top, left, width}}>
                <div className={this.getProvinceClassName(bShowCity, bShowCounty)}>
                    {this.renderProvince()}
                </div>
                {
                    bShowCity ?
                    (<div className={this.getCityClassName(bShowCity, bShowCounty)}>
                        {this.renderCity()}
                    </div>)
                    : null
                }
                {
                    bShowCounty ? 
                    (<div className={this.getCountyClassName(bShowCity, bShowCounty)}>
                        {this.renderCounty()}
                    </div>)
                    : null
                }
                <div className='list-button'>
                    <label data-index={1}>清空</label>
                </div>
            </div>
        );
    }

    private renderProvince(): any {
        const triangleElem = (<span className='expand-triangle' />);
        return this.addressCollection.map((item, index) => {
            return (
                <li
                    key={'province' + item.code}
                    data-code={item.code}
                    tabIndex={index}
                    className={item.code === this.state.provinceCode ? 'focusedItem' : ''}
                >
                    {item.name}
                    {this.showExpandTriangle(HierarchyLevel.First, item) === true ? triangleElem : null}
                </li>
            );
        });
    }

    private renderCity(): any {
        const triangleElem = (<span className='expand-triangle' />);
        let cityCollection = [];
        const provinceCode = this.state.provinceCode;
        if ( provinceCode) {
            cityCollection = this.citys || chinaCity.getCitys(provinceCode);
            this.citys = cityCollection;
        }

        // console.log(cityCollection)

        return cityCollection.map((item, index) => {
            if (item.code == null) {
                // tslint:disable-next-line: no-console
                console.log('city address has no code');
            }
            return (
                <li
                    key={'city' + item.code}
                    data-code={item.code}
                    data-level={item.level}
                    tabIndex={index}
                    className={item.code === this.state.cityCode ? 'focusedItem' : ''}
                >
                    {item.name}
                    {this.showExpandTriangle(HierarchyLevel.Second, item) === true ? triangleElem : null}
                </li>
            );
        });
    }

    private renderCounty(): any {
        const triangleElem = (<span className='expand-triangle' />);
        const {provinceCode, cityCode, countyCode} = this.state;
        let countyCollection = [];
        if ( provinceCode) {
            const cityCollection = this.citys;
            if (cityCollection && cityCode) {
                countyCollection = this.countys || chinaCity.getCountys(cityCode);
                this.countys = countyCollection;
            }
        }

        // console.log(countyCollection)

        return countyCollection.map((item, index) => {
            if (item.code == null) {
                // tslint:disable-next-line: no-console
                console.log('county address has no code');
            }
            return (
                <li
                    key={'city' + item.code}
                    data-code={item.code}
                    data-level={item.level}
                    tabIndex={index}
                    className={item.code === countyCode ? 'focusedItem' : ''}
                >
                    {item.name}
                    {this.showExpandTriangle(HierarchyLevel.Third, item) === true ? triangleElem : null}
                </li>
            );
        });
    }

    private containerClick = (e: any): void => {
        // not sure why onClick on child elems not work: may be stop propogated
        // console.log(e.target)

        const target = e.target;
        const tagName = target.nodeName;
        // console.log(tagName)
        // const {cityPair, countyPair} = this.state;

        if (tagName === 'LABEL') {
            const index = target.getAttribute('data-index');
            if (!index) {
                return;
            }
            switch (index) {
                case '1': {
                    this.setReset();
                    break;
                }
                default: {
                    break;
                }
            }
        } else if (tagName === 'LI') {
            const index = +target.getAttribute('tabIndex');
            // console.log(target)

            if (this.getListBlock(target) === 'province') {
                // province
                // const {code, name} = ;
                const provincePair = this.addressCollection[index];
                let bShowCity = false;
                const bShowCounty = false;
                if (this.newControlPropety.hierarchy >= HierarchyLevel.Second) {
                    bShowCity = true;
                    // bShowCounty = true;
                }
                this.citys = null;
                this.countys = null;
                this.setState({provinceCode: provincePair.code, cityCode: null, countyCode: null, bShowCity,
                    bShowCounty, provincePair, cityPair: null, countyPair: null}, () => {
                    // reset city focus
                    this.resetScrollPosition('city');

                    // hierarchy passed in is 1
                    if (this.newControlPropety.hierarchy === HierarchyLevel.First) {
                        // set the data
                        this.setAddressData();
                    }

                });
            // } else if (dataCode < 10000) {
            } else if (this.getListBlock(target) === 'city') {
                // city
                // const {code, name} = this.addressCollection[this.state.provinceIndex].children[index];
                const cityPair = this.citys[index];
                let bShowCounty = false;
                if (this.newControlPropety.hierarchy === HierarchyLevel.Third) {
                    bShowCounty = true;
                }
                this.countys = null;
                this.setState({cityCode: cityPair.code, countyCode: null, bShowCounty, cityPair, countyPair: null}, () => {
                    // reset city focus
                    this.resetScrollPosition('county');

                    // hierarchy passed in is 2
                    if (this.newControlPropety.hierarchy === HierarchyLevel.Second) {
                        // set the data
                        this.setAddressData();
                    }

                });
            } else if (this.getListBlock(target) === 'county') { // 存在不符合规律: 嘉峪关市的county: 620201100

                // county
                // const {provinceIndex, cityIndex} = this.state;
                // const {code, name} = this.addressCollection[provinceIndex].children[cityIndex].children[index];
                const countyPair = this.countys[index];
                this.setState({countyCode: countyPair.code, bShowCity: false, bShowCounty: false, countyPair}, () => {
                    if (this.newControlPropety.hierarchy === HierarchyLevel.Third) {
                        // set the data
                        this.setAddressData();
                    } else {
                        // tslint:disable-next-line: no-console
                        console.warn('county tab is illegally shown');
                    }
                });
            } else {
                // tslint:disable-next-line: no-console
                console.log('cannot retrieve list type');
            }
        }
        e.stopPropagation();
    }

    private getListBlock(target: any): string {
        if (target == null) {
            return ;
        }

        const dataCode = target.getAttribute('data-code');
        const level = +target.getAttribute('data-level'); // +'' => 0
        const parentElem = target.parentElement;
        let result = '';

        if (parentElem.classList.contains('province-block')) {
            if (dataCode.indexOf('0000') > 0 && (level === 0 || level === 1)) { // doublecheck
                result = 'province';
            }

        } else if (parentElem.classList.contains('city-block')) {
            if (dataCode.indexOf('00') > 0 && (level === 0 || level === 2)) { // doublecheck
                result = 'city';
            }

        } else if (parentElem.classList.contains('county-block')) {
            // 存在不符合规律: 西沙，阿图什市等
            result = 'county';

        } else {
            // tslint:disable-next-line: no-console
            console.log('cannot retrieve parent block');
        }

        return result;
    }

    private setReset = () => {
        this.setState({provincePair: null, cityPair: null, countyPair: null, countyCode: null,
            cityCode: null, provinceCode: null, bShowCity: false, bShowCounty: false}, () => {

            this.resetNewControlProperty();

            // combine setProps() and setText() in one step
            this.setAddressDataText('');

            // close addresssbox
            this.props.closeNewBoxList();
        });
    }

    private showExpandTriangle(currentLevel: HierarchyLevel, item: any): boolean {
        let result = false;
        const hierarchy = this.newControlPropety.hierarchy; // this can only be changed in modal

        if (hierarchy === HierarchyLevel.First) {
            return false;
        } else if (hierarchy === HierarchyLevel.Second) {
            if (currentLevel === HierarchyLevel.First) {
                if (item != null && item.children != null) {
                    return true;
                }
            } else if (currentLevel >= HierarchyLevel.Second) {
                return false;
            }
        } else if (hierarchy === HierarchyLevel.Third) {
            if (item != null) {
                switch (currentLevel) {
                    case HierarchyLevel.First:
                    case HierarchyLevel.Second: {
                        if (item.children != null) {
                            result = true;
                        }
                        break;
                    }
                    case HierarchyLevel.Third: {
                        result = false;
                        break;
                    }
                    default: {
                        break;
                    }
                }
            }
        }

        return result;
    }

    private getProvinceClassName(bShowCity: boolean, bShowCounty: boolean): string {
        let result = 'address-item-list province-block';
        if (bShowCity === true && bShowCounty === true) {
            result += ' divide-thrice';
        } else if (bShowCity === true) {
            result += ' divide-half';
        }

        return result;
    }

    private getCityClassName(bShowCity: boolean, bShowCounty: boolean): string {
        let result = 'address-item-list city-block';
        if (bShowCity === true && bShowCounty === true) {
            result += ' divide-thrice';
        } else if (bShowCity === true) {
            result += ' divide-half';
        } else {
            result += ' hide';
        }

        return result;
    }

    private getCountyClassName(bShowCity: boolean, bShowCounty: boolean): string {
        let result = 'address-item-list county-block';
        if (bShowCounty === true) {
            result += ' divide-thrice';
        } else {
            result += ' hide';
        }

        return result;
    }

    // private getAddressIndexFromProps(): IAddressIndex {
    //     const result: IAddressIndex = {provinceIndex: -1, cityIndex: -1, countyIndex: -1};
    //     const {province, city, county, hierarchy} = this.newControlPropety;
    //     let provinceIndex = -1;
    //     let cityIndex = -1;
    //     let countyIndex = -1;
    //     const addressCollection = this.addressCollection;

    //     // find province
    //     if (province != null) {
    //         for (let i = 0, len = addressCollection.length; i < len; i++) {
    //             const provinceItem = addressCollection[i];
    //             if (province.code === provinceItem['code']) {
    //                 provinceIndex = i;
    //                 break;
    //             }
    //         }
    //     }

    //     if (city != null && provinceIndex !== -1) {
    //         const provinceInstance = addressCollection[provinceIndex];
    //         for (let i = 0, len = provinceInstance.children.length; i < len; i++) {
    //             const cityItem = provinceInstance.children[i];
    //             if (city.code === cityItem['code']) {
    //                 cityIndex = i;
    //                 break;
    //             }
    //         }
    //     }

    //     if (county != null && provinceIndex !== -1 && cityIndex !== -1) {
    //         const cityInstance = addressCollection[provinceIndex].children[cityIndex];
    //         for (let i = 0, len = cityInstance.children.length; i < len; i++) {
    //             const countyItem = cityInstance.children[i];
    //             if (county.code === countyItem['code']) {
    //                 countyIndex = i;
    //                 break;
    //             }
    //         }
    //     }

    //     result.provinceIndex = provinceIndex;
    //     result.cityIndex = cityIndex;
    //     result.countyIndex = countyIndex;

    //     // console.log(result);
    //     return result;
    // }

    private setAddressDataText(text: string): void {
        const {documentCore} = this.props;

        // documentCore.setNewControlProperty(this.newControlPropety);
        // documentCore.setNewControlText(this.newControlPropety.newControlName, text);

        // in order to change in one step, combine setProps() and setText()
        this.props.host.updateRefreshFlag(false);
        documentCore.setNewControlPropertyAndText(this.newControlPropety, text);

        documentCore.recalculate();
        this.props.host.updateRefreshFlag(true);
        this.props.refresh();
    }

    private resetNewControlProperty(): void {
        const newControlPropety = this.newControlPropety;
        newControlPropety.province = null;
        // heierarchy still need to preserve
        // newControlPropety.hierarchy = STD_START_DEFAULT.hierarchy;
        newControlPropety.city = null;
        newControlPropety.county = null;
    }

    private setAddressData(): void {
        // get text
        const {provincePair, cityPair, countyPair} = this.state;

        let text = '';
        this.newControlPropety.province = provincePair;
        if (provincePair != null) {
            text += provincePair.name;
        }
        this.newControlPropety.city = cityPair;
        if (cityPair != null) {
            text += cityPair.name;
        }
        this.newControlPropety.county = countyPair;
        if (countyPair != null) {
            text += countyPair.name;
        }

        if (text != null && text.length > 0) {
            this.setAddressDataText(text);
        }

        // close addresssbox
        this.props.closeNewBoxList();
    }

    private resetScrollPosition(listType: string): void {
        const addrContainerRefElem = this.addrContainerRef.current;
        if (addrContainerRefElem != null) {
            if (listType === 'city') {
                const cityContainer = addrContainerRefElem.querySelector('.city-block');
                // console.log(cityContainer);
                if (cityContainer != null) {
                    const cityItems = cityContainer.children;
                    if (cityItems != null && cityItems.length > 0) {
                        cityItems[0].focus();
                    }
                }
            } else if (listType === 'county') {
                const countyContainer = addrContainerRefElem.querySelector('.county-block');
                // console.log(countyContainer)
                if (countyContainer != null) {
                    const countyItems = countyContainer.children;
                    if (countyItems != null && countyItems.length > 0) {
                        countyItems[0].focus();
                    }
                }
            }
        }
    }

    private mousedownEvent(e: any): void {
        setTimeout(() => {
            this.focusElems();
        });
    }

    private focusElems(): void {
        const addrContainerRefElem = this.addrContainerRef.current;
        if (addrContainerRefElem != null) {
            const focusedElems = addrContainerRefElem.querySelectorAll('.focusedItem');
            // console.log(focusedElems);
            if (focusedElems != null && focusedElems.length > 0) {
                for (const focusedElem of focusedElems) {
                    focusedElem.focus();
                }
            }
        }
    }

    private keydown = (e: any): void => {
        const code = e.keyCode;
        switch (code) {
            case 13: {
                this.props.closeNewBoxList();
                break;
            }
        }

        e.preventDefault();
        e.stopPropagation();
    }

}
