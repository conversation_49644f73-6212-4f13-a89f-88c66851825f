@import './global.less';
.hz-editor-container {
    .custom-prop-name {
        input.warning {
            border: 1px solid red;
        }
    }

    .custom-prop-btn {
        width: 100%;
        height: 28px;
        margin-top: 5px;
        color: @activeColor;
        font-size: @fontSize;
        font-family: @fontFamily;
        text-align: center;
        cursor: pointer;
        background: rgba(234, 237, 238);
        border-radius: 2px;
        border: 1px solid @activeColor;

        & > * {
            box-sizing: border-box;
        }
        & > i {
            display: inline-block;
            margin-right: 4px;
            color: inherit;
            font-style: normal;
            font-size: 18px;
            font-weight: bold;
            line-height: 26px;
        }

        & > span {
            display: inline-block;
            line-height: 1;
        }
    }

    .editor-dialog .custom-no-data {
        width: 100%;
        color: @activeColor;
        font-size: @fontSize;
        font-family: @fontFamily;
        text-align: center;
        cursor: pointer;
        background: rgba(234, 237, 238);
        border-radius: 2px;
        border: 1px solid @activeColor;
    }

    .newcontrol-custom {
        font-size: @fontSize;
        font-family: @fontFamily;
        & * {
            box-sizing: border-box;
        }

        .custom-no-data {
            width: 100%;
            color: @activeColor;
            font-size: @fontSize;
            font-family: @fontFamily;
            text-align: center;
            cursor: pointer;
            background: rgba(234, 237, 238);
            border-radius: 2px;
            border: 1px solid @activeColor;
        }
        .custom-prop-btns {
            text-align: center;
        }
        ul > li {
            display: flex;
            border-bottom: 1px solid @borderColor;
            line-height: 30px;
            &:nth-child(1) {
                border-top: 1px solid @borderColor;
                line-height: 28px;
            }
            &:last-child {
                margin-bottom: 10px;
            }

            & > div {
                padding: 1px 8px;
                border-left: 1px solid @borderColor;
                &:last-child {
                    border-right: 1px solid @borderColor;
                }
                &:nth-child(1) {
                    width: 158px;
                }
                &:nth-child(2) {
                    flex-basis: 110px;
                }
                &:nth-child(3) {
                    flex-basis: 110px;
                }
                &:last-child {
                    flex-grow: 1;
                    flex-basis: 60px;
                    min-width: 60px;
                    flex-shrink: 0;
                    line-height: 28px;
                    & > *:hover {
                        color: @activeColor;
                        cursor: pointer;
                    }

                    & > * {
                        display: inline-block;
                        min-width: 12px;
                        margin-right: 6px;
                        font-size: 20px;
                        &:last-child {
                            margin-right: 0;
                        }
                    }

                    & > .prev, & > .next{
                        font-size: 15px;
                    }
                }
            }
        }

        .newcontrol-custom-edit {
            width: 100%;
            height: 28px;
            margin-bottom: 16px;
        }
        .newcombox > li {
            & > div {
                &:nth-child(2) {
                    flex-basis: 130px;
                }
                &:nth-child(2) {
                    flex-basis: 120px;
                }
                &:last-child {
                    flex-shrink: 0;
                    flex-basis: 80px;
                    min-width: 80px;
                    padding-right: 0;
                    & > * {
                        display: inline-block;
                        min-width: 12px;
                        margin-right: 6px;
                    }
                    i {
                        font-style: normal;
                        line-height: 20px;
                    }
                    i:nth-child(2) {
                        border-right: 1px solid #ccc;
                    }

                    span:first-child {
                        margin-left: 6px;
                    }
                    & > *:last-child {
                        margin-right: 0;
                    }
                }
            }
        }
    }
}
