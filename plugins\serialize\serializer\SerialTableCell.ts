import ISerial, {
    ShadowFillType,
    TableWidthType,
    TextDirectionType,
    VertAlignType,
    VerticalMergeType as CVMergeType,
} from './ISerial';
import {
    ITableShadingAttributesProperties,
    ShadingType,
    TextDirection,
    VerticalAlign,
    VerticalMergeType,
    WidthType
} from 'docx';
import { SerialObjType } from '../serialInterface';
import { ISerialTableCellObj } from '../serialInterface/ISerialTableObj';
import SerialParagraph from './SerialParagraph';
import { buildBorderOption, buildTableCellMargin, convertWidthType, pureHexColor, transPxToDocxLength } from './Utils';

export default class SerialTableCell implements ISerial {

    constructor(private readonly cell: any) { }

    public serializedTo(collector: ISerialTableCellObj[]): ISerialTableCellObj[] {
        const prop = this.cell.property;
        const borders = this.cell.getBorders();
        const cellObj: ISerialTableCellObj = {
            isVerticalMerged: prop.verticalMerge === CVMergeType.Continue, // 当前单元格被合并
            type: SerialObjType.TableCell,
            children: [],
            shading: this.buildShadingOption(prop.shadow),
            margins: buildTableCellMargin(this.cell.getMargins()),
            verticalAlign: this.getVerticalAlign(prop.vertAlign),
            textDirection: this.getTextDirection(prop.textDirection),
            verticalMerge: this.getVerticalMerge(prop.verticalMerge),
            width: this.computeCellWidth(),
            columnSpan: prop.gridSpan > 1 ? prop.gridSpan : undefined,
            rowSpan: this.cell.row.table.getVMergeCount(this.cell.index, this.cell.row.index),
            borders: borders && {
                top: buildBorderOption(borders.top),
                right: buildBorderOption(borders.right),
                bottom: buildBorderOption(borders.bottom),
                left: buildBorderOption(borders.left),
            } || undefined,
        };
        for (const para of this.cell.getContent()) {
            new SerialParagraph(para).serializedTo(cellObj.children as any[]);
        }
        collector.push(cellObj);
        return collector;
    }

    /** 根据边距计算单元格真实宽度 */
    private computeCellWidth(): {
        size: number | string;
        type?: WidthType;
    } {
        const prop = this.cell.property;
        let cellWidth = prop.cellWidth.width;
        let type = prop.cellWidth.type;
        if (!cellWidth) {
            const cellInfo = this.cell.row.cellsInfo[this.cell.index];
            if (cellInfo) {
                type = TableWidthType.Mm;
                cellWidth = cellInfo.xGridEnd - cellInfo.xGridStart;
            }
        }
        return {
            size: transPxToDocxLength(cellWidth),
            type: convertWidthType(type)
        };
    }

    /** 获取阴影选项 */
    private buildShadingOption(shadow: any): ITableShadingAttributesProperties {
        if (!shadow) {
            return undefined;
        }
        const option: ITableShadingAttributesProperties = {
            // fill: shadow,
            color: shadow.color ? pureHexColor(shadow.color.toHex()) : undefined,
            val: shadow.value === ShadowFillType.Fill ? ShadingType.SOLID : undefined, // 7版本为style
        };
        return option;
    }

    /** 获取单元格垂直合并类型 */
    private getVerticalMerge(type: CVMergeType): VerticalMergeType {
        switch (type) {
            case CVMergeType.Continue: {
                return VerticalMergeType.CONTINUE;
            }
            case CVMergeType.Restart: {
                return VerticalMergeType.RESTART;
            }
        }
    }

    /** 获取单元格文字书写方向 */
    private getTextDirection(type: TextDirectionType): TextDirection {
        switch (type) {
            case TextDirectionType.LRTB: {
                return TextDirection.LEFT_TO_RIGHT_TOP_TO_BOTTOM;
            }
        }
    }

    /** 获取单元格垂直对齐方式 */
    private getVerticalAlign(type: VertAlignType): VerticalAlign {
        switch (type) {
            case VertAlignType.Bottom: {
                return VerticalAlign.BOTTOM;
            }
            case VertAlignType.Middle: {
                return VerticalAlign.CENTER;
            }
            case VertAlignType.Top: {
                return VerticalAlign.TOP;
            }
        }
    }
}
