import * as React from 'react';
import Dialog from '../dialog/dialog';
import {ICustomProps, DataType} from '../../../common/commonDefines';
import {message} from '../../../common/Message';

interface IDialogProps {
    documentCore: any;
    visible: boolean;
    name?: string;
    properties: ICustomProps[];
    callback: (props: ICustomProps[]) => void;
    close: (name: string | number, bReflash?: boolean) => void;
    children: React.ReactNode;
}

interface IState {
    bReflash: boolean;
}

export default class CustomPropertyDialog extends React.Component<IDialogProps, IState> {
    private custompProps: ICustomProps[];
    private currentProp: ICustomProps;
    private displayType: boolean;
    private isValidData: boolean;
    private activeIndex: number;
    private curInputIndex: number;
    private types: number[];
    private _name: string;
    private _names: object;
    private _activeName: string;
    private unValidIndex: number;
    constructor(props: any) {
        super(props);
        this.state = {
            bReflash: false,
        };

        this._name = 'propName';
        this.types = [DataType.String, DataType.Number, DataType.Boolean];
    }

    public render(): any {
        return (
            <Dialog
                visible={this.props.visible}
                top='middle'
                width={500}
                height={450}
                close={this.close}
                open={this.open}
                preventDefault={true}
                title='自定义属性'
                confirm={this.confirm}
                id='newControlCustomProp'
            >
                <div className='custom-prop-content'>
                    {this.renderContent()}
                </div>
            </Dialog>
        );
    }

    private renderContent(): any {
        if (this.custompProps && this.custompProps.length > 0) {
            return (
                <ul>
                    <li>
                        <div>操作</div>
                        <div>属性名</div>
                        <div>类型</div>
                        <div>属性值</div>
                    </li>
                    {this.renderList()}
                </ul>
            );
        } else {
            return (<div className='custom-prop-add' onClick={this.rowClick.bind(this, 0)}>点击添加</div>);
        }
    }

    private renderList(): any {
        return this.custompProps.map((prop, index) => {
            return (
                <li key={index} className={this.setClassName(index)} onClick={this.rowClick.bind(this, index)}>
                    <div className='custom-prop-btns'>
                        <span className='custom-prop-add'>+</span>
                        <label className='custom-prop-delete'>-</label>
                    </div>
                    <div className='custom-prop-name'>
                        <span>{prop.name}</span>
                        <input
                            value={prop.name || ''}
                            id='name'
                            onFocus={this.onFocus.bind(this, prop.name)}
                            onBlur={this.onBlur.bind(this, index)}
                            onChange={this.textChange.bind(this, 'name')}
                        />
                    </div>
                    <div>
                        <select value={prop.type || ''} onChange={this.selectChange.bind(this, 'type')}>
                            {this.renderTypes()}
                        </select>
                    </div>
                    <div>
                        {this.renderValue(prop)}
                    </div>
                </li>
            );
        });
    }

    private renderValue(prop: ICustomProps): any {
        if (prop.type === DataType.Boolean) {
            return (
                <select value={prop.value || ''} onChange={this.selectChange.bind(this, 'value')}>
                    <option value='true'>true</option>
                    <option value='false'>false</option>
                </select>
            );
        } else {
            return (
                <input
                    value={prop.value || ''}
                    id='value'
                    onChange={this.textChange.bind(this, 'value')}
                />
            );
        }
    }

    private renderTypes(): any {
        return this.types.map((type) => {
            return (<option key={type} value={type}>{type}</option>);
        });
    }

    private setClassName(index: number): string {
        let className: string;
        if (this.activeIndex === index) {
            className = 'active';
        }

        if (className && this.curInputIndex === index) {
            className += ' show-input';
        }

        return className;
    }

    private selectChange(name: string, e: any): void {
        if (!this.currentProp) {
            return;
        }

        this.currentProp[name] = e.target.value;
        if (name === 'type' && this.currentProp[name] === DataType.Boolean) {
            this.currentProp.value = 'true';
        }
    }

    private onBlur(index: number, e: any): void {
        if (!this.currentProp) {
            return;
        }

        const preName = this._activeName;
        const currentName = this.currentProp.name;

        // check xml element name validity
        if (this.checkXmlElementNameValidity(currentName)) {
            e.target.classList.remove('warning');
        } else {
            e.target.classList.add('warning');
        }

        if (currentName !== preName) {
            if (currentName in this._names) {
                message.error('已存在相同的属性名')
                .then(() => {
                    this.currentProp.name = preName;
                    // e.target.focus();
                    this.setState({bReflash: !this.state.bReflash});
                });
                return;
            }
            delete this._names[preName];
            this._names[currentName] = true;
        }
    }

    private onFocus(name: string): void {
        this._activeName = name;
    }

    private rowClick(index: number, e: any): void {
        this.activeIndex = index;
        const target = e.target as HTMLDivElement;
        const className = target.className;
        if (this.curInputIndex !== index && className === 'custom-prop-name') {
            this.curInputIndex = index;
            this.currentProp = this.custompProps[index];
        } else if (className === 'custom-prop-add') {
            this.addData(index);
        } else if (className === 'custom-prop-delete') {
            this.deleteData(index);
        } else {
            this.currentProp = this.custompProps[index];
        }

        this.setState({bReflash: !this.state.bReflash});
    }

    private addData = (index?: number): void => {
        const data: ICustomProps = {name: this.getPropName(), type: DataType.String, value: undefined};
        if (index === undefined) {
            this.custompProps.push(data);
        } else {
            this.custompProps.splice(index + 1, 0, data);
        }
        this.currentProp = data;
    }

    private deleteData = (index: number): void => {
        const data = this.custompProps[index];
        delete this._names[data.name];
        this.custompProps.splice(index, 1);
        this.currentProp = undefined;
    }

    private getPropName(): string {
        const datas = this.custompProps;
        if (!datas || datas.length === 0) {
            const actName = this._name + 1;
            this._names[actName] = true;
            return actName;
        }

        const names = this._names;
        const name = this._name;
        let res: string;
        for (let index = 1; true; index++) {
            res = name + index;
            if (names[res] !== true) {
                names[res] = true;
                break;
            }
        }

        return res;
    }

    private textChange = (name: string, e: any): void => {
        if (!this.currentProp) {
            return;
        }
        this.currentProp[name] = e.target.value;
        this.setState({bReflash: !this.state.bReflash});
    }

    private open = (): void => {
        const names = this._names = {};
        const arrs: ICustomProps[] = this.custompProps = [];
        let datas: ICustomProps[];
        if (this.props.properties) {
            datas = this.props.properties;
        } else if (this.props.name) {
            datas = this.props.documentCore.getNewControlCustomPropsByName(this.props.name);
        } else {
            datas = [];
        }

        datas.forEach((data) => {
            arrs.push({...data});
            names[data.name] = true;
        });
        this.setState({bReflash: !this.state.bReflash});
    }

    private validData(): boolean {
        const datas = this.custompProps;
        if (datas.length === 0) {
            return true;
        }

        for (let index = 0, length = datas.length; index < length; index++) {
            const data = datas[index];
            if (data.type === DataType.Number && Number.isNaN(Number(data.value))) {
                message.error(`第${index + 1}行的属性值为非法数字`);
                return false;
            }
            if (!this.checkXmlElementNameValidity(data.name)) {
                message.error(`属性名 不符合xml命名规范`);
                return false;
            }
        }

        return true;
    }

    private close = (id?: any): void => {
        this.props.close(id);
        this.custompProps = [];
    }

    private confirm = (id?: any): void => {
        if (this.validData() === false) {
            return;
        }

        if (this.custompProps && this.custompProps.length > 0) {
            this.props.callback(this.custompProps.slice());
        }
        this.custompProps = [];
        this.props.close(id, true);
    }

    private checkXmlElementNameValidity(name: string): boolean {
        // Element names are case-sensitive
        // Element names must start with a letter or underscore
        // Element names cannot start with the letters xml (or XML, or Xml, etc)
        // Element names can contain letters, digits, hyphens, underscores, and periods
        // Element names cannot contain spaces

        let validity = !/^[xX][mM][lL].*/.test(name); // condition 3
        validity = validity && /^[a-zA-Z_].*/.test(name);  // condition 2
        validity = validity && /^[a-zA-Z0-9_\-\.]+$/.test(name); // condition 4
        return validity;
    }
}
