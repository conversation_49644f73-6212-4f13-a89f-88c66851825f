import { XmlComponent, XmlAttributeComponent } from '../../xml-components';
import { ICustomProps, DataType } from '../../../../../common/commonDefines';
import { customEncodeURIComponent } from '../../../../../common/commonMethods';

export class CustomProperty extends XmlComponent {
  constructor() {
    super('customProperty');
  }

  public addCustomProperties(properties: Map<string, ICustomProps>): CustomProperty {
    properties.forEach( (values, key, map) => {
      this.root.push(new CustomPropertyEntry(key, values));
    });
    return this;
  }

}

export interface ICustomPropertyEntryAttributesProperties {
  type: DataType;
}

class CustomPropertyEntryAttributes extends XmlAttributeComponent<ICustomPropertyEntryAttributesProperties> {
  protected xmlKeys: any = {
    type: 'type',
  };
}

// tslint:disable-next-line: max-classes-per-file
export class CustomPropertyEntry extends XmlComponent {
  constructor(key: string, values: ICustomProps) {
    // console.log(values)
    super(key);
    this.root.push(new CustomPropertyEntryAttributes({type: values.type}));
    if (values != null) {
      this.root.push(customEncodeURIComponent(values.value));
    }
  }
}
