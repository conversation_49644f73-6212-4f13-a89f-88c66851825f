import { TableRow } from './TableRow';
import { HistroyItemType } from '../HistoryDescription';
import { ChangeBaseObjectProperty, ChangeBaseContent, ChangeBaseBoolProperty } from '../HistoryChange';
import { TableRowHeight, TableRowProperty } from './TableRowProperty';

export class ChangeTableRowHeight extends ChangeBaseObjectProperty {
    private type: HistroyItemType;

    constructor(changeClass: TableRow, old: TableRowHeight, news: TableRowHeight, color?: any) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.TableRowHeight;
    }

    public setValue(rowHeight: TableRowHeight): void {
        const row = this.changeClass as TableRow;
        if ( row ) {
            row.property.height.value = rowHeight.value;
            row.property.height.hRule = rowHeight.hRule;
        }
    }
}

export class ChangeTableRowAddCell extends ChangeBaseContent {

    constructor( changeClass: TableRow, pos: number, items: any, bAdd?: boolean ) {
        super(changeClass, pos, items, true);
        this.type = HistroyItemType.TableRowAddCell;
    }

    public undo(): void {
        if ( 0 >= this.items.length ) {
            return;
        }

        const row = this.changeClass as TableRow;
        row.content[this.position].setIndex(-1);
        row.content.splice(this.position, 1);
        row.cellsInfo.splice(this.position, 1);
        row.reIndex(this.position);
        row.checkCurCell();
    }

    public redo(): void {
        if ( 0 >= this.items.length ) {
            return;
        }

        const row = this.changeClass as TableRow;
        row.content.splice(this.position, 0, this.items[0]);
        row.cellsInfo.splice(this.position, 0);
        row.reIndex(this.position);
        row.checkCurCell();
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeTableRowRemoveCell extends ChangeBaseContent {

    constructor( changeClass: TableRow, pos: number, items: any, bAdd?: boolean ) {
        super(changeClass, pos, items, true);
        this.type = HistroyItemType.TableRowRemoveCell;
    }

    public undo(): void {
        if ( 0 >= this.items.length ) {
            return;
        }

        const row = this.changeClass as TableRow;
        row.content.splice(this.position, 0, this.items[0]);
        row.cellsInfo.splice(this.position, 0);
        row.reIndex(this.position);
        row.checkCurCell();
    }

    public redo(): void {
        if ( 0 >= this.items.length ) {
            return;
        }

        const row = this.changeClass as TableRow;
        row.content[this.position].setIndex(-1);
        row.content.splice(this.position, 1);
        row.cellsInfo.splice(this.position, 1);
        row.reIndex(this.position);
        row.checkCurCell();
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeTableRowProperty extends ChangeBaseObjectProperty {
    private type: HistroyItemType;

    constructor(changeClass: TableRow, old: TableRowProperty, news: TableRowProperty, color?: any) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.TableRowProperty;
    }

    public setValue(value: TableRowProperty): void {
        const row = this.changeClass as TableRow;
        row.property = value;
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeTableRowTableHeader extends ChangeBaseBoolProperty {
    private type: HistroyItemType;

    constructor(changeClass: TableRow, old: boolean, news: boolean, color?: any) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.TableRowTableHeader;
    }

    public setValue(value: boolean): void {
        const row = this.changeClass as TableRow;
        row.property.bTableHeader = value;
    }
}
