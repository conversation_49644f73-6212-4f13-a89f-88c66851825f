import { XmlComponent } from '../../../../xml-components';
import { HeaderFooterAttributes } from './header-footer-attributes';

export class HeaderFooter extends XmlComponent {
    constructor(showHeader: number, showFooter: number, protectHeaderFooter: number,
                showHeaderBorder: number, showFooterBorder: number) {
        super('w:headerfooter');

        this.root.push(
            new HeaderFooterAttributes({
                showHeader,
                showFooter,
                protectHeaderFooter,
                showHeaderBorder,
                showFooterBorder
            })
        );
    }
}
