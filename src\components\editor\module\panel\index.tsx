import * as React from 'react';
import { INewControlProperty, NewControlType } from '../../../../common/commonDefines';
import Dialog from '../../ui/Dialog';
import NewComboBox from './NewComboBox';
import NewControlText from './NewControlText';
import { GlobalEvent as gEvent, GlobalEventName as gEventName} from '../../../../common/GlobalEvent';
import { NewControlManageInfo } from './NewControlManage';
import NewControlCheck from './NewControlCheck';
import NewControlRadio from './NewControlRadio';
import NewDateBox from './NewDateBox';

interface IDialogProps {
    documentCore: any;
    visible: boolean;
    id: string;
    host: any;
    refresh: (bRefresh?: boolean) => void;
    close: (name: string | number, bRefresh?: boolean) => void;
}

export default class AttributePanel extends React.Component<IDialogProps> {
    private visible: boolean;
    private property: any;
    private bManagInit: boolean;
    private flagNum: number;
    constructor(props: IDialogProps) {
        super(props);
        // this.visible = this.props.visible;
        this.init(this.props.visible);
        this.flagNum = 0;
    }

    public render(): any {
        return (
            <Dialog
                visible={this.visible}
                width={340}
                height={600}
                left='75%'
                top='10%'
                scale={true}
                bCloseIcon={true}
                noModal={true}
                open={this.open}
                close={this.close}
                title='属性面板'
                id={this.props.id}
            >
                <div className='panel-module'>
                    {this.renderModal()}
                </div>
            </Dialog>
        );
    }

    public componentDidMount(): void {
        gEvent.addEvent(this.props.documentCore.getCurrentId(), gEventName.NewControlChange, this.newControlChange);
    }

    public componentWillUnmount(): void {
        gEvent.deleteEvent(this.props.documentCore.getCurrentId(), gEventName.NewControlChange, this.newControlChange);
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        this.init(nextProps.visible);
    }

    public refresh = (): void => {
        this.props.refresh(true);
    }

    private newControlChange = (newControl: any): void => {
        if (!this.visible) {
            return;
        }

        if (!newControl) {
            if (this.property) {
                this.property = null;
                this.setState({});
            }
            return;
        }
        this.flagNum++;
        this.property  = this.props.documentCore.getNewControlPropByName(newControl.getNewControlName());
        if (this.bManagInit !== true) {
            this.setManageInfo();
        } else {
            NewControlManageInfo.setNewControlProps2(this.property);
        }
        this.setState({});
    }

    private isNewControl(): boolean {
        if (this.property && typeof this.property.newControlType === 'number') {
            return true;
        }
        return false;
    }

    private init(visible: boolean): void {
        if (visible !== true || this.visible === visible) {
            return;
        }
        this.visible = visible;
        const documentCore = this.props.documentCore;
        const newControl = documentCore.getCurrentNewControl();
        if (this.initNewControl(newControl)) {
            return;
        }
    }

    private initNewControl(newControl: any): boolean {
        if (!newControl) {
            this.property = null;
            return;
        }

        const documentCore = this.props.documentCore;
        this.property  = documentCore.getNewControlPropByName(newControl.getNewControlName());
        this.setManageInfo();
    }

    private setManageInfo(): void {
        this.bManagInit = true;
        NewControlManageInfo.setNewControlProps(this.props.documentCore, this.property);
        NewControlManageInfo.setRefresh(this.refresh);
    }

    private renderModal(): any {
        const property = this.property;
        if (!property) {
            return null;
        }
        // tslint:disable-next-line: sy-global-const-name
        let Modal: any;
        if (this.isNewControl()) {
            switch (property.newControlType) {
                case NewControlType.ListBox:
                case NewControlType.MultiListBox:
                case NewControlType.Combox:
                case NewControlType.MultiCheckBox: {
                    Modal = NewComboBox;
                    break;
                }
                case NewControlType.TextBox:
                case NewControlType.Section: {
                    Modal = NewControlText;
                    break;
                }
                case NewControlType.CheckBox: {
                    Modal = NewControlCheck;
                    break;
                }
                case NewControlType.RadioButton: {
                    Modal = NewControlRadio;
                    break;
                }
                case NewControlType.DateTimeBox: {
                    Modal = NewDateBox;
                    break;
                }
            }
        }

        if (!Modal) {
            return null;
        }
        const props = this.props;
        return (
            <Modal
                property={this.property}
                refresh={this.refresh}
                documentCore={props.documentCore}
                flag={this.flagNum}
            />
        );
    }

    private close = (id: string): void => {
        this.visible = false;
        this.setState({});
        this.props.close(this.props.id);
    }

    private open = (): void => {};
}
