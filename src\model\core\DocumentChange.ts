import { ChangeBaseContent, ChangeBaseBoolProperty } from './HistoryChange';
import DocumentContentBase from './DocumentContentBase';
import { HistroyItemType } from './HistoryDescription';
import Document from './Document';

export class ChangeDocumentAddItem extends ChangeBaseContent {

    constructor( changeClass: DocumentContentBase, pos: number, items: any, bAdd?: boolean ) {
        super(changeClass, pos, items, true);
        this.type = HistroyItemType.DocumentAddItem;
    }

    public undo(): void {
        const doc = this.getClass();

        for (let index = 0, count = this.items.length; index < count; index++) {
            const pos = ( true !== this.bUseArray ) ? this.position : this.posArray[index];
            const element = doc.content.splice(pos, 1);

            // 判断增加的段落等，是否在第一段
            if ( 0 < pos ) {
                // 判断增加的段落等，是否在最后一段
                if ( doc.content.length - 1 >= pos ) {
                    doc.content[pos - 1].next = doc.content[pos];
                    doc.content[pos].prev = doc.content[pos - 1];
                } else {
                    doc.content[pos - 1].next = null;
                }
            } else if ( pos <= doc.content.length - 1) {
                doc.content[pos].prev = null;
            }
        }
    }

    public redo(): void {
        const doc = this.getClass();

        for (let index = 0, count = this.items.length; index < count; index++) {
            const element = this.items[index];
            const pos = ( true !== this.bUseArray ) ? this.position : this.posArray[index];
            doc.content.splice(pos, 0, element);

            // 判断增加的段落等，是否在第一段
            if ( 0 < pos ) {
                doc.content[pos - 1].next = element;
                element.prev = doc.content[pos - 1];
            } else {
                element.prev = null;
            }

            // 判断增加的段落等，是否在最后一段
            if ( doc.content.length - 1 > pos ) {
                element.next = doc.content[pos + 1];
                doc.content[pos + 1].prev = element;
            } else {
                element.next = null;
            }

            element.setParent(doc);
        }
    }

    public createReverseChange(): ChangeDocumentRemoveItem {
        return this.createReverseChangeBase(ChangeDocumentRemoveItem);
    }
}

export class ChangeDocumentRemoveItem extends ChangeBaseContent {

    constructor( changeClass: DocumentContentBase, pos: number, items: any, bAdd?: boolean ) {
        super(changeClass, pos, items, false);
        this.type = HistroyItemType.DocumentRemoveItem;
    }

    public undo(): void {
        const doc = this.getClass();

        const start = doc.content.slice(0, this.position);
        const end = doc.content.slice(this.position);

        doc.content = start.concat(this.items, end);

        const startIndex = Math.max(this.position - 1, 0);
        const endIndex = Math.min(doc.content.length - 1, this.position + this.items.length + 1);

        for (let index = startIndex; index <= endIndex; index++) {
            const element = doc.content[index];

            if ( 0 < index ) {
                element.prev = doc.content[index - 1];
            } else {
                element.prev = null;
            }

            if ( index < doc.content.length - 1 ) {
                element.next = doc.content[index + 1];
            } else {
                element.next = null;
            }

            element.setParent(doc);
        }
    }

    public redo(): void {
        const doc = this.getClass();

        const elements = doc.content.splice(this.position, this.items.length);

        if ( 0 < this.position ) {
            if ( doc.content.length - 1 >= this.position ) {
                doc.content[this.position - 1].next = doc.content[this.position];
                doc.content[this.position].prev = doc.content[this.position - 1];
            } else {
                doc.content[this.position - 1].next = null;
            }
        } else if ( doc.content.length - 1 >= this.position ) {
            doc.content[this.position].prev = null;
        }
    }

    public createReverseChange(): ChangeDocumentAddItem {
        return this.createReverseChangeBase(ChangeDocumentAddItem);
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeImageSelectionState extends ChangeBaseBoolProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: Document, old: boolean, news: boolean, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.ImageSelectionState;
    }

    public setValue( value: boolean ): void {
        this.changeClass.setImageOnClick(value);
    }
}
