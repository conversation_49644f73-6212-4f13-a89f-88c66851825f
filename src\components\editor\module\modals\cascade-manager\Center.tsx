import * as React from 'react';
import * as ReactDOM from 'react-dom/client';
import styles from '../../../../../../style.js';

interface IDialogProps {
    documentCore: any;
    host: any;
    data: any;
    visible: boolean;
    load: (host: any) => void;
}

export default class CenterCascadeManagerUI extends React.Component<IDialogProps, {}> {
    private ref: React.RefObject<HTMLDivElement>;
    private editor: any;
    private vmEditorRef: React.RefObject<any>;

    constructor(props: IDialogProps) {
        super(props);
        this.ref = React.createRef();
        this.vmEditorRef = React.createRef();
    }

    public async componentDidMount(): Promise<void> {
        await this.init(); // 使用 await 等待 init 方法完成
    }

    public render(): JSX.Element {
        return (
            <div className='cascade-center'>
                <div ref={this.ref} />
            </div>
        );
    }

    public UNSAFE_componentWillReceiveProps(nextProps: IDialogProps): void {
        if (nextProps.visible === false) {
            return;
        }
        this.open(nextProps.data);
    }

    public componentWillUnmount(): void {
        if (this.vmEditorRef.current && this.vmEditorRef.current.myRef.current) {
            this.vmEditorRef.current.myRef.current?.removeEventListener('contextmenu', this.contextmenu);
        }
    }

    public refresh = () => {
        if (this.vmEditorRef.current) {
            this.vmEditorRef.current.handleRefresh();
        }
    }

    public jumpStruct(name: string): void {
        if (!this.vmEditorRef.current) {
            return;
        }
        this.vmEditorRef.current.state.documentCore.cursorJumpOutOfOneStruct(name, 3);
        this.vmEditorRef.current.handleRefresh();
    }

    public open(data: any): void {
        if (data && this.vmEditorRef.current) {
            const editor = this.vmEditorRef.current.getEditor();
            editor.openDocumentWithStream(data);
            const documentCore = this.vmEditorRef.current.state.documentCore;
            documentCore.designTemplet(true);

            documentCore.setViewMode(0);
            documentCore.forbidMoveTableBorder(true);
            // 设置背景色是否显示
            documentCore.setShowCascadeBackground(this.props.documentCore.isShowCascadeBackground());
            documentCore.setShowCascadeHiddenBackground(false);
            // documentCore.protectDoc(true);
            this.vmEditorRef.current.handleRefresh();
        }
    }

    public setFlag(flag: boolean): void {
        if (this.vmEditorRef.current) {
            this.vmEditorRef.current.setCascadeModeFlag(flag);
        }
    }

    private async init(): Promise<void> {
        if (this.editor) {
            await this.renderEditor(); // 使用 await 等待异步函数完成
        } else {
            const result = await import('@/components/editor/Main');
            this.editor = result.EmrEditor;
            await this.renderEditor(); // 使用 await 等待异步函数完成
        }
    }

    private async renderEditor(): Promise<void> {
        await this.createEditor();
        if (this.props.load) {
            this.props.load(this.vmEditorRef.current);
        }
    }

    private contextmenu = (e: Event) => {
        e.preventDefault();
    }

    // private createEditor(): void {
    //     const iframe = document.createElement('iframe');

    //     iframe.setAttribute('width', '100%');
    //     iframe.setAttribute('height', '100%');
    //     iframe.setAttribute('src', 'about:blank');
    //     iframe.setAttribute('loading', 'lazy'); // 建议添加loading属性以改进性能
    //     // 设置CSS样式来替代frameBorder和scrolling
    //     iframe.style.border = 'none'; // 替代frameBorder
    //     iframe.style.overflow = 'hidden'; // 替代scrolling
        
    //     iframe.id = 'hz-editor-wrap-iframe';

    //     const dom = this.ref.current;
    //     dom.appendChild(iframe);
    //     const iframeDocument = iframe.contentDocument;
    //     const div = iframeDocument.createElement('div');
    //     div.id = 'editor-cascade-id';
    //     // div.setAttribute('data-index', (dataIndex++).toString());
    //     iframeDocument.body.appendChild(div);
    //     if (styles) {
    //       const style = iframeDocument.createElement('style');
    //       style.innerHTML = styles;
    //       iframeDocument.head.appendChild(style);
    //     }

    //     this.vmEditor = ReactDOM.render(
    //         React.createElement(this.editor, {isTest: false, _bCascadeManager: true}),
    //         div,
    //     );

    // }

    private async createEditor(): Promise<void> {
        const iframe = document.createElement('iframe');

        iframe.setAttribute('width', '100%');
        iframe.setAttribute('height', '100%');
        iframe.setAttribute('src', 'about:blank');
        iframe.setAttribute('loading', 'lazy');
        iframe.style.border = 'none';
        iframe.style.overflow = 'hidden';
        iframe.id = 'hz-editor-wrap-iframe';

        const dom = this.ref.current;
        dom.appendChild(iframe);
        const iframeContent = iframe.contentWindow;
        if (iframeContent) {
            const div = iframeContent.document.createElement('div');
            div.id = 'hz-editor-app';
            iframeContent.document.body.appendChild(div);
            if (styles) {
                const style = iframeContent.document.createElement('style');
                style.innerHTML = styles;
                iframeContent.document.head.appendChild(style);
              }
      

            // 创建 React Root 并渲染组件
            const root = ReactDOM.createRoot(iframeContent.document.getElementById('hz-editor-app') as HTMLElement);
            root.render(<this.editor ref={this.vmEditorRef} isTest={false} _bCascadeManager={true} />);

            // 确保组件实例渲染完成后获取到值
            await new Promise<void>((resolve) => {
                const checkInstance = () => {
                    if (this.vmEditorRef.current) {
                        resolve(); // 解析 Promise
                    } else {
                        setTimeout(checkInstance, 100); // 继续检查
                    }
                };
                checkInstance();
            });
        } else {
            console.error('Iframe content window is not available');
        }
    }
}
