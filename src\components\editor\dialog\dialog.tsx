import * as React from 'react';

interface IDialogProps {
    title: string;
    width?: number | string;
    height?: number | string;
    top?: number | string;
    left?: number | string;
    id?: number | string;
    visible?: boolean;
    className?: string;
    preventDefault?: boolean;
    open?: () => void;
    close?: (id?: number | string) => void;
    confirm?: (id?: number | string) => void;
    children?: React.ReactNode;
}

// interface IDialogStates {
//     bReflash: boolean;
// }

export default class Dialog extends React.Component<IDialogProps, {}> {
    private _width: number | string;
    private _height: number | string;
    private _left: number | string;
    private _top: number | string;
    private _visible: boolean;
    private _className: string = '';
    private _bMove: boolean;
    private _bStart: boolean;
    private _subLeft: number;
    private _subTop: number;
    private _sourceLeft: number;
    private _sourceTop: number;
    private _ref: any;
    private _dialogBox: HTMLDivElement;
    private _maxHeight: number;
    constructor(props: any) {
        super(props);
        const body = document.body;
        let width: number | string = props.width;
        let height: number | string = props.height;
        let left: number | string = props.left;
        let top: number | string = props.top;
        if (width === undefined) {
            width = 500;
        }
        if (height === undefined) {
            height = '';
        }
        if (left === undefined) {
            left = (body.clientWidth - (width as number)) / 2;
        }
        if (top === undefined) {
            top = 50;
        }

        this._height = height;
        this._width = width;
        this._top = top;
        this._left = left;
        this._ref = React.createRef();
    }

    // public componentDimMount(): void {
    //     if (this.props.visible === true) {
    //         this.props.open();
    //         this._visible = true;
    //     }
    // }

    public componentDidMount(): void {
        this.initDialog();
    }

    public componentWillUnmount(): void {
        document?.removeEventListener('mouseup', this.docMouseUp);

        const dom = this._ref.current;
        if (!dom) {
          return;
        }
        dom.removeEventListener('mousedown', this.mousedown);
        dom.removeEventListener('click', this.click);
        dom.removeEventListener('mousemove', this.mousemove);
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        this.init(nextProps.visible);
    }

    public render(): any {
        let bodyContent: any;
        const childrens = this.props.children as any;
        if (Array.isArray(childrens)) {
            bodyContent = childrens.filter((child) => this.filterBodyChild(child.props));
        } else if (childrens && this.filterBodyChild(childrens.props)) {
            bodyContent = childrens;
        }

        const style = {
            width: null,
            height: null,
        };

        style.width = this.getCell(this._width);
        style.height = this.getCell(this._height);
        let className = (this.props.className || '') + this._className;
        if (this._bStart !== true) {
            className += ' opacity0';
        } else if (this.props.visible) {
            className += ' visible';
        }

        return (
            <div
                className={`editor-dialog ${className}`}
                ref={this._ref}
            >
                <div className={'dialog-box'} style={style}>
                    <div className={'dialog-box-header'}>{this.props.title}</div>
                    <div className={'dialog-box-body'}>
                        {bodyContent}
                    </div>
                    <div className={'dialog-box-footer'}>
                        <div className={'dialog-box-footer-btns'}>
                            <button className='dialog-box-cancel-btn'>取消</button>
                            <button className='dialog-box-confirm-btn'>确定</button>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    private filterBodyChild(props: any): boolean {
        const keys = Object.keys(props);
        if (keys.find((key) => {
            return key.includes('data-header') || key.includes('data-footer');
        })) {
            return false;
        }

        return true;
    }

    private init(visible: boolean): void {
        if (visible === this._visible) {
            return;
        }
        this._visible = visible;
        if (visible === true && typeof this.props.open === 'function') {
            setTimeout(() => {
                this.props.open();
            }, 0);
        }
    }

    private reset(): void {
        this._left = this._sourceLeft;
        this._top = this._sourceTop;
        this.setPosition();
    }

    private initDialog(): void {
        const dom = this._ref.current;
        dom.addEventListener('mousedown', this.mousedown);
        dom.addEventListener('click', this.click);
        dom.addEventListener('mousemove', this.mousemove);
        document.addEventListener('mouseup', this.docMouseUp);
        if (this.props.visible === true) {
            this.init(true);
        }

        const dialogBox = this._dialogBox = dom.firstChild;
        // const width = dialogBox.clientWidth;
        const height = dialogBox.clientHeight;
        const body = document.body;
        const bodyHeight = body.clientHeight;
        const bodyWidth = body.clientWidth;
        const props = this.props;
        if (props.top === 'middle') {
            this._top = (bodyHeight - height) / 2;
            if (this._top < 0) {
                this._top = 0;
            }
        } else if (typeof props.top === 'string') {
            if (props.top.indexOf('%') > -1) {
                this._top = parseInt(props.top, 10) * bodyHeight / 100 || 0;
            } else {
                this._top = parseInt(props.top, 10) || 0;
            }
        }

        if (typeof props.left === 'string') {
            if (props.left.indexOf('%') > -1) {
                this._left = parseInt(props.left, 10) * bodyWidth / 100 || 0;
            } else {
                this._left = parseInt(props.left, 10) || 0;
            }
        }
        this._bStart = true;
        this._sourceLeft = this._left as number;
        this._sourceTop = this._top as number;
        this._maxHeight = bodyHeight - this._sourceTop - 90;
        this.setMaxHeight();
        this.setPosition();
    }

    private setMaxHeight(): void {
        (this._dialogBox.querySelector('.dialog-box-body') as HTMLElement).style.maxHeight = this._maxHeight + 'px';
    }

    private setPosition(): void {
        const dom = this._dialogBox;
        dom.style.left = this.getCell(this._left);
        dom.style.top = this.getCell(this._top);
    }

    // private setPosition(): void {
    //     todo
    // }

    private getCell(val: number | string): string {
        if (typeof val === 'number') {
            return val + 'px';
        }

        return val;
    }

    private _close = (e?: any): void => {
        this.props.close(this.props.id);
        this.reset();
    }

    private _confirm = (e: any): void => {
        this.props.confirm(this.props.id);
    }

    private click = (e: any): void => {
        const target = e.target;
        if (target.className === 'dialog-box-cancel-btn') {
            this._close(e);
        } else if (target.className === 'dialog-box-confirm-btn') {
            this._confirm(e);
        }
    }

    private mousedown = (e: any): void => {
        const target = e.target;
        if (target.className.indexOf('dialog-box-header') > -1) {
            this._bMove = true;
            this._subLeft = e.clientX - (this._left as number);
            this._subTop = e.clientY - (this._top as number);
        }
        if (this.props.preventDefault !== true) {
            return;
        }

        e.stopPropagation();
    }

    // private mouseup = (e: any): void => {
    //     this._bMove = true;
    // }

    private mousemove = (e: any): void => {
        if (this._bMove !== true) {
            return;
        }

        const x = e.clientX;
        const y = e.clientY;
        let left = this._left as number;
        let top = this._top as number;
        left = x - this._subLeft;
        top = y - this._subTop;
        this._left = left;
        this._top = top;
        this.setPosition();
    }

    private docMouseUp = (e: any): void => {
        this._bMove = false;
    }
}
