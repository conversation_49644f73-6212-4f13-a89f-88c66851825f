import ParagraphContentWithContentBase from './ParagraphContentBase';
import {measure, getAcitveNodeIndex, idCounter} from '../util';
import {ParagraphRecalculateStateWrap} from './ParagraphRecalculateStateWrap';
import Paragraph from '../Paragraph';
import {ParagraphRecalculateStateCounter} from './ParagraphRecalculateStateCounter';
import {ParaElementType, ParagraphContentPos, IParaPos, ParaTextName} from './ParagraphContent';
import {ParagraphRecalculateStateAlign} from './ParagraphRecalculateStateAlign';
import {IParagraphSearchPos, IParagraphSearchPosXY} from './ParagraphSearchPosXY';
import TextProperty, { TextVertAlign, FontWeightType } from '../TextProperty';
import ParaProperty from './ParaProperty';
import ParaText from './ParaText';
import CompositeInput from '../CompositeInput';
import {ParaElementBase, RepalceText} from './ParaElementBase';
import { PortionSelection } from '../Selection';
import { ChangePortionAddItem, ChangePortionRemoveItem, ChangePortionPropertyChange, ChangePortionTextProperty,
    ChangePortionStartSplit, ChangePortionEndSplit, ChangePortionFontSize, ChangePortionFont, ChangePortionBold,
    ChangePortionItalic, ChangePortionDecorationLine, ChangePortionBackgroundColor,
    ChangePortionColor, ChangePortionVertAlign, ChangePortionHidden, ChangePortionContentReviewInfo,
    ChangePortionReviewType, ChangePortionPrReviewInfo, ChangePortionReplaceText } from './PortionChange';
import { HistroyItemType } from '../HistoryDescription';
import { ChangeBaseContent, ChangeContent } from '../HistoryChange';
import PortionRecalculateObject from './PortionRecalculateObject';
import ContentChanges, { ContentChangesElement } from '../ContentChanges';
import { IRanges } from './ParaLine';
import ParaTab from './ParaTab';
import ParaDrawing from './ParaDrawing';
import { ChangeNewControlPlaceHolder } from '../NewControl/NewControlChange';
import { LineSpacingType, LineSpacingRatio, ResultType, ReviewType,
    RevisionChangeType,
    AlignType,
    CleanModeType} from '../../../common/commonDefines';
import { ParaComment } from './ParaComment';
import History from '../History';
import ParaPageNum from './ParaPageNum';
import { ReviewInfo } from '../Revision';
import { ParagraphRevisionChangesChecker } from './ParagraphRevisionChangesChecker';
import ParaTextExtend from './ParaTextExtend';
import { WasmInstance } from '../../../common/WasmInstance';
import { getDefaultFont } from '../../../common/commonMethods';
import Document from '../Document';
import { ParaSoftLine } from './ParaSoftLine';
import ParaButton from './ParaButton';
import { clearWavyUnderlineFromPortion, clearWavyUnderlineFromTextProperty } from '../WavyUnderline/WavyUnderlineUtils';
// import {parse as circularParse, stringify as circularStringify} from 'flatted/esm';
// import { WasmInstance } from '../../../common/WasmInstance';

class ParaPortionRecalInfo {
    public bTextPr: boolean;
    public bMeasure: boolean;  // 重新测量
    public bRecalc: boolean;  // 重新计算排版
    public portionLen: number;

    constructor() {
        this.bTextPr = true;
        this.bMeasure = true;
        this.bRecalc = true;
        this.portionLen = 0;
    }

    public reset(): void {
        this.bTextPr = true;
        this.bMeasure = true;
        this.bRecalc = true;
        this.portionLen = 0;
    }
}

/**
 * 光标定位，选择时，记录信息
 */
// export class ParaPortionState {
//     public portionContentPos: number = 0;
// }

/**
 * 段落中属性相同的连续文本
 */
export default class ParaPortion extends ParagraphContentWithContentBase {
    public id: number;
    public wavyUnderlineId?: string;
    public content: ParaElementBase[];  // 文本内容
    public textProperty: TextProperty; // 文本属性
    public type: ParaElementType;
    public recalcInfo: ParaPortionRecalInfo; // 在排版时保存的相关数据
    // nPos: number; //在段落content中的位置
    // public state: ParaPortionState;
    public portionContentPos: number; // 光标定位，选择时，记录信息

    public compositeInput: CompositeInput; // 复合文本输入

    public ascent: number;
    public descent: number;
    public textHeight: number;
    // width: number;
    // height: number;
    public positionX: number; // 目前为UI显示定义，后期可删除
    public positionY: number; // 目前为UI显示定义，后期可删除

    public selection: PortionSelection; // 选择
    public checkedTextColor: string; // 选中项的颜色

    // history: History;

    public contentChanges: ContentChanges;

    // 与newControl相关
    // 结构化元素的边框所在portion 指示该struct当前是否只是placeholder. as of 7/8/2020仅start border的portion stays updated
    // 注意 title+placeholder，没有输入内容，bPlaceHolder亦为true
    public bPlaceHolder: boolean;
    // public bCanntDelete: boolean;
    // public bCanntEdit: boolean;

    public bHidden: boolean;
    // dirty: boolean;

    private reviewType: ReviewType;
    private reviewInfo: ReviewInfo;

    constructor(para: Paragraph, doc?: any, bCopy?: boolean, bFromTable?: boolean) {
        super();
        this.id = idCounter.getNewId();
        this.type = ParaElementType.ParaPortion;
        this.wavyUnderlineId = undefined;
        const topDocument = (para ? para.getDocument() : null);
        if (topDocument instanceof Document) {
            this.textProperty  = new TextProperty(getDefaultFont(topDocument as Document));
        } else {
            this.textProperty  = new TextProperty();
        }

        this.paragraph = para;
        this.recalcInfo = new ParaPortionRecalInfo();
        this.ascent = 0;
        this.descent = 0;
        this.textHeight = 0;
        // this.width = 0;
        // this.height = 0;
        this.positionX = 0;
        this.positionY = 0;
        // this.nPos = -1;
        this.content = [];
        this.portionContentPos = 0;
        this.compositeInput = null;
        this.selection = new PortionSelection();
        // gHistory = para.history;
        this.contentChanges = new ContentChanges();
        // this.dirty = false;
        this.bPlaceHolder = false;
        // this.bCanntDelete = true;
        // this.bCanntEdit = true;
        this.bHidden = false;
        this.reviewType = ReviewType.Common;

        if ( true !== bCopy && true !== bFromTable ) {
            let parent;
            if ( para && para.getParent() ) {
                parent = para.getParent();
            } else if ( doc && doc.getTopDocument() ) {
                parent = doc.getTopDocument();
            } else {
                parent = topDocument;
            }

            this.reviewInfo = new ReviewInfo(parent);
            if ( parent && parent.isTrackRevisions() ) {
                if ( !parent.getDocument().bRecalcTableHeader ) {
                    this.reviewType = ReviewType.Add;
                    this.reviewInfo.update();
                } else {
                    this.reviewInfo = new ReviewInfo(null);
                }
            }
        } else {
            this.reviewInfo = new ReviewInfo(null);
        }
    }

    public getId(): number {
      return this.id;
    }

    public add(pos: number, item: ParaElementBase, bUpdateRange: boolean = false): void {
        if ( this.isParaEndPortion() ) {
            const newPortion = this.splitPortionInCurPos();

            if ( newPortion ) {
                newPortion.moveCursorToStartPos();
                newPortion.add(newPortion.portionContentPos, item, bUpdateRange);
                newPortion.setCurrentPosInParagraph();
                return;
            }
        }

        const trackRevisonsRun = this.checkTrackRevisionsBeforeAdd();

        if ( trackRevisonsRun ) {
            trackRevisonsRun.addToContent(trackRevisonsRun.portionContentPos, item, bUpdateRange);
        } else {
            this.addToContent(pos, item, bUpdateRange);
        }
    }

    /**
     * 将字符添加到content末端
     * @param text：string
     * @param nPos：number
     */
    public addText(text: string, textPr?: TextProperty, nPos?: number, bNotSaveHistory: boolean = false): void {
        if (textPr) {
            this.textProperty = textPr;
        } else if ( !textPr && !this.textProperty) {
            const topDocument = (this.paragraph == null ? null : this.paragraph.getDocument());
            if (topDocument instanceof Document) {
                this.textProperty = new TextProperty(getDefaultFont(topDocument));
            } else {
                this.textProperty = new TextProperty();
            }
        }

        let nCharPos = ( undefined !== nPos && null !== nPos && -1 !== nPos ) ? nPos : this.content.length;

        for (let i = 0, len = text.length; i < len; ++i) {
            const item = new ParaText(text[i]);

            this.addToContent(nCharPos++, item, false, bNotSaveHistory);
        }
    }

    public setContentPos(pos: ParagraphContentPos): boolean {
        let index = pos.shift();
        if (index === undefined) {
            return false;
        }
        const maxLength = this.content.length;
        if (index > maxLength) {
            index = maxLength;
        }
        const selection = this.selection;
        selection.bUse = false;
        selection.startPos = index;
        this.portionContentPos = index;
        return true;
    }

    public addParaItem(pos: number, type: ParaElementType): void {
        // const nCharPos = this.content.length;
        switch (type) {
            case ParaElementType.ParaTab: {
                this.addToContent(pos, new ParaTab());
                break;
            }

            case ParaElementType.ParaNewLine: {
                this.addToContent(pos, new ParaSoftLine());
                break;
            }

            case ParaElementType.ParaButton: {
                this.addToContent(pos, new ParaButton(this));
                break;
            }

            default:
                break;
        }
    }

    /**
     * 将字符对象添加到content
     * @param pos 插入元素时的开始位置
     * @param item 插入元素
     * @param bUpdateRange 是否更新光标等
     */
    public addToContent(pos: number, item: ParaElementBase, bUpdateRange: boolean = false,
                        bForUI: boolean = false): void {
        // this.dirty = true;

        if ( -1 === pos ) {
            pos = this.content.length;
        }

        if ( false === bForUI ) {
            const history = this.getHistory();
            if ( history && history.canAdd() ) {
                history.addChange(new ChangePortionAddItem(this, pos, [item], true));
            }
        }

        if ( true === this.bPlaceHolder && false === this.isNewControlStart() ) {
            this.removePlaceHolderContent(); // addContentReplacePlaceHolder(item);
        } else {
            this.content.splice(pos, 0, item);

            // 更新光标，portion在行range中的位置等
            if ( true === bUpdateRange ) {
                this.updatePositionOnAdd(pos);
            }
        }

        this.recalcInfo.bMeasure = true;

        if ( false === bForUI ) {
            this.updateTrackRevisionOnChangeContent(true);
        }
    }

    // public addNewControl(newControl: NewControl): boolean {
    //     let curPos = this.portionContentPos;
    //     if ( true === this.selection.bUse ) {
    //         curPos = this.selection.endPos;
    //     }

    //     // this.addToContent(curPos, newControl.endBorder);
    //     // this.addToContent(curPos, newControl.startBorder);
    //     return true;
    // }

    /**
     * 插入字符对象数组
     * @param pos
     * @param items
     */
    public concatToContent( items: ParaElementBase[] ): void {
        const startPos = this.content.length;
        items.forEach((item) => {
            this.content.push(item);
            if (item.isButton()) {
                (item as ParaButton).portion = this;
            }
        });
        // this.content = this.content.concat();

        const history = this.getHistory();
        if ( history) {
            history.addChange(new ChangePortionAddItem(this, startPos, items));
        }
        this.recalcInfo.bMeasure = true;
    }

    /**
     * 删除指定长度的文本内容
     * @param pos 删除开始位置
     * @param count 删除长度
     * @param bUpdatePos 是否更新光标等
     * @param bFromSplit
     */
    public removeFromContent( pos: number, count: number, bUpdatePos?: boolean, bFromSplit?: boolean ): boolean {
        const delItems = this.content.slice(pos, pos + count);

        if ( !delItems || 0 === delItems.length ) {
            return false;
        }

        // !bFromSplit means remove()
        if (!bFromSplit && this.containDelProtectImages(delItems)) {
            return false;
        }

        const history = this.getHistory();
        if ( history) {
            history.addChange(new ChangePortionRemoveItem(this, pos, delItems));
        }

        // check if contains image
        if (!bFromSplit) {
            this.removeImageFromPortion(delItems);
            // this.paragraph.parent.getDrawingObjects().deleteImage(this.paragraph.index);
        }

        this.content.splice(pos, count);

        // if ( true === this.bPlaceHolder) {
        //     this.bPlaceHolder = false;
        // }

        // 是否更新当前光标位置等
        if ( true === bUpdatePos ) {
            this.updatePositionOnRemove(pos, count);
        }

        this.recalcInfo.bRecalc = true;
        return true;
    }

    public removeImageFromPortion(delItems: ParaElementBase[]): void {
        const types = [ParaElementType.ParaDrawing, ParaElementType.ParaMedEquation,
            ParaElementType.ParaBarcode, ParaElementType.ParaQRCode];
        for (let nIndex = 0, nCount = delItems.length; nIndex < nCount; ++nIndex) {
            const type = delItems[nIndex].type;
            if (types.includes(type)) {
                const delImage = delItems[nIndex] as ParaDrawing;
                this.paragraph.parent
                .getDrawingObjects()
                .deleteImage(delImage);
            } else if (type === ParaElementType.ParaButton) {
                this.paragraph.getDocument()
                .getButtonManager()
                .deleteButton(delItems[nIndex] as any);
            }
        }
    }

    /**
     * 删除portion内容，主要用于外部删除：键盘等
     * @param direction
     */
    public remove( direction: number, bSelectParaEnd?: boolean  ): boolean {
        const para = this.paragraph;
        const bTrackRevisions = this.isTrackRevisions();

        if ( true === bTrackRevisions && false === this.canDeleteInReviewMode() ) {
            if ( ReviewType.Remove !== this.getReviewType() ) {
                if ( this.isCurLowLevel() ) {
                    return true;
                }

                if ( true !== this.selection.bUse ) {
                    const pos = this.getCurInParaIndex();
                    const curPos = this.portionContentPos;
                    const revisionManager = para.logicDocument.getRevisionsManager();
                    const curUserId = revisionManager.getCurrentUserId();
                    const curUserName = revisionManager.getCurrentUserName();
                    const savedCount = this.getRevisionSavedCount();

                    if ( 0 > direction ) {
                        // while ( 0 < curPos ) {
                        //     curPos--;
                        // }
                        if ( 0 >= curPos ) {
                            return false;
                        }

                        const tempPos = curPos - 1;
                        const element = this.content[tempPos];
                        if ( element instanceof ParaDrawing && element.isDeleteLocked() ) {
                            return true;
                        }

                        if ( ParaElementType.ParaNewControlBorder === this.content[tempPos].type ) {
                            this.portionContentPos = tempPos;
                            return true;
                        } else if ( true === this.bPlaceHolder
                            || ( this.isNewControlStart() && 1 < this.content.length ) ) {
                            return true;
                        }

                        if ( 1 === curPos && 1 === this.content.length ) {
                            if (ReviewType.Add !== this.getReviewType()) {
                                this.setReviewType(ReviewType.Remove, true);
                            } else {
                                const nextPortion = para.content[pos + 1];
                                const reviewInfo = nextPortion.getReviewInfo();
                                const delRevInfo = reviewInfo.getDeleteInfo();
                                if ( ReviewType.Remove === nextPortion.getReviewType() &&
                                    this.textProperty.isEqual(nextPortion.textProperty) &&
                                    reviewInfo.getUserId() === curUserId &&
                                    reviewInfo.getUserName() === curUserName &&
                                    savedCount === reviewInfo.getSavedCount()  &&
                                    (!delRevInfo || 0 === delRevInfo.getSavedCount())) {
                                    const item = this.content[curPos - 1];
                                    this.removeFromContent(curPos - 1, 1, true);

                                    nextPortion.addToContent(0, item);
                                } else {
                                    this.setReviewType(ReviewType.Remove, true);
                                }
                            }

                            this.portionContentPos--;
                            this.makeElementCurrentPos();
                            return true;
                        } else if ( 1 === curPos && 0 < pos ) {
                            const prePortion = para.content[pos - 1];
                            const reviewInfo = prePortion.getReviewInfo();
                            const delRevInfo = reviewInfo.getDeleteInfo();
                            if ( ReviewType.Remove === prePortion.getReviewType() &&
                                this.textProperty.isEqual(prePortion.textProperty) &&
                                reviewInfo.getUserId() === curUserId &&
                                reviewInfo.getUserName() === curUserName &&
                                savedCount === reviewInfo.getSavedCount() &&
                                (!delRevInfo || 0 === delRevInfo.getSavedCount())) {
                                const item = this.content[curPos - 1];
                                this.removeFromContent(curPos - 1, 1, true);

                                prePortion.addToContent(prePortion.content.length, item);
                                prePortion.portionContentPos = prePortion.content.length - 1;
                                prePortion.makeElementCurrentPos();
                                return true;
                            }
                        } else if ( curPos === this.content.length && pos < para.content.length - 1) {
                            const nextPortion = para.content[pos + 1];
                            const reviewInfo = nextPortion.getReviewInfo();
                            const delRevInfo = reviewInfo.getDeleteInfo();
                            if ( ReviewType.Remove === nextPortion.getReviewType() &&
                                this.textProperty.isEqual(nextPortion.textProperty) &&
                                reviewInfo.getUserId() === curUserId &&
                                reviewInfo.getUserName() === curUserName &&
                                savedCount === reviewInfo.getSavedCount()  &&
                                (!delRevInfo || 0 === delRevInfo.getSavedCount())) {
                                const item = this.content[curPos - 1];
                                this.removeFromContent(curPos - 1, 1, true);

                                nextPortion.addToContent(0, item);
                                this.portionContentPos = curPos - 1;
                                this.makeElementCurrentPos();
                                return true;
                            }
                        }

                        const rightPortion = this.splitImplement(curPos, para, pos);
                        const centerPortion = this.splitImplement(curPos - 1, para, pos);

                        centerPortion.setReviewType(ReviewType.Remove, true);
                        this.portionContentPos = curPos - 1;
                        this.makeElementCurrentPos();
                    } else {
                        if ( 0 === this.content.length || this.content.length <= curPos ||
                            ParaElementType.ParaEnd === this.content[curPos].type ) {
                            return false;
                        }

                        const element = this.content[curPos];
                        if ( element instanceof ParaDrawing && element.isDeleteLocked() ) {
                            return true;
                        }

                        // if ( ParaElementType.ParaNewControlBorder === this.content[curPos].type ) {
                        //     this.portionContentPos = curPos + 1;
                        //     return true;
                        // } else if ( true === this.bPlaceHolder ) {
                        //     return true;
                        // }

                        // const tempPos = curPos + 1;
                        if ( ParaElementType.ParaNewControlBorder === this.content[curPos].type ) {
                            this.portionContentPos = curPos + 1;
                            return true;
                        } else if ( true === this.bPlaceHolder
                            || ( this.isNewControlStart() && 1 < this.content.length ) ) {
                            return true;
                        }

                        if ( 0 === curPos && 1 === this.content.length ) {
                            if (ReviewType.Add !== this.getReviewType()) {
                                this.setReviewType(ReviewType.Remove, true);
                            } else {
                                const prePortion = para.content[pos - 1];
                                const reviewInfo = prePortion.getReviewInfo();
                                const delRevInfo = reviewInfo.getDeleteInfo();
                                if ( ReviewType.Remove === prePortion.getReviewType() &&
                                    this.textProperty.isEqual(prePortion.textProperty) &&
                                    reviewInfo.getUserId() === curUserId &&
                                    reviewInfo.getUserName() === curUserName &&
                                    savedCount === reviewInfo.getSavedCount() &&
                                    (!delRevInfo || 0 === delRevInfo.getSavedCount())) {
                                    const item = this.content[curPos];
                                    this.removeFromContent(curPos, 1, true);

                                    prePortion.addToContent(prePortion.content.length, item);
                                } else {
                                    this.setReviewType(ReviewType.Remove, true);
                                }
                            }

                            this.portionContentPos = 1;
                            this.makeElementCurrentPos();
                            return true;
                        } else if ( 0 === curPos && 0 < pos ) {
                            const prePortion = para.content[pos - 1];
                            const reviewInfo = prePortion.getReviewInfo();
                            const delRevInfo = reviewInfo.getDeleteInfo();
                            if ( ReviewType.Remove === prePortion.getReviewType() &&
                                this.textProperty.isEqual(prePortion.textProperty) &&
                                reviewInfo.getUserId() === curUserId &&
                                reviewInfo.getUserName() === curUserName &&
                                savedCount === reviewInfo.getSavedCount() &&
                                (!delRevInfo || 0 === delRevInfo.getSavedCount())) {
                                const item = this.content[curPos];
                                this.removeFromContent(curPos, 1, true);

                                prePortion.addToContent(prePortion.content.length, item);
                                this.portionContentPos = curPos;
                                this.makeElementCurrentPos();
                                return true;
                            }
                        } else if ( curPos === this.content.length - 1 && pos < para.content.length - 1) {
                            const nextPortion = para.content[pos + 1];
                            const reviewInfo = nextPortion.getReviewInfo();
                            const delRevInfo = reviewInfo.getDeleteInfo();
                            if ( ReviewType.Remove === nextPortion.getReviewType() &&
                                this.textProperty.isEqual(nextPortion.textProperty) &&
                                reviewInfo.getUserId() === curUserId &&
                                reviewInfo.getUserName() === curUserName &&
                                savedCount === reviewInfo.getSavedCount() &&
                                (!delRevInfo || 0 === delRevInfo.getSavedCount()) ) {
                                const item = this.content[curPos];
                                this.removeFromContent(curPos, 1, true);

                                nextPortion.addToContent(0, item);
                                nextPortion.portionContentPos = 1;
                                nextPortion.makeElementCurrentPos();
                                return true;
                            }
                        }

                        const rightPortion = this.splitImplement(curPos + 1, para, pos);
                        const centerPortion = this.splitImplement(curPos, para, pos);

                        centerPortion.setReviewType(ReviewType.Remove, true);
                        rightPortion.portionContentPos = 0;
                        rightPortion.makeElementCurrentPos();
                    }
                } else {
                    let startPos = this.selection.startPos;
                    let endPos = this.selection.endPos;
                    const length = this.content.length;

                    if ( startPos > endPos ) {
                        startPos = this.selection.endPos;
                        endPos = this.selection.startPos;
                    }

                    const pos = this.getCurInParaIndex();

                    if ( -1 !== pos ) {
                        let deletePortion: ParaPortion = null;

                        if ( 0 >= startPos && length <= endPos ) {
                            deletePortion = this;
                        } else if ( 0 >= startPos ) {
                            this.splitImplement(endPos, para, pos);
                            deletePortion = this;
                        } else if ( length <= endPos ) {
                            deletePortion = this.splitImplement(startPos, para, pos);
                        } else {
                            this.splitImplement(endPos, para, pos);
                            deletePortion = this.splitImplement(startPos, para, pos);
                        }

                        if ( !deletePortion.isNewControl() ) {
                            deletePortion.setReviewType(ReviewType.Remove);
                        }

                        this.removeSelection();
                        if ( deletePortion ) {
                            deletePortion.removeSelection();
                        }
                    }
                }
            } else {
                if ( true !== this.selection.bUse ) {
                    const curPos = this.portionContentPos;

                    if ( 0 > direction ) {
                        // while ( 0 < curPos ) {
                        //     curPos--;
                        // }
                        if ( 0 >= curPos ) {
                            return false;
                        }

                        this.portionContentPos--;
                    } else {
                        if ( 0 === this.content.length || this.content.length <= curPos ||
                            ParaElementType.ParaEnd === this.content[curPos].type ) {
                            return false;
                        }

                        if ( ParaElementType.ParaNewControlBorder === this.content[curPos].type ) {
                            this.portionContentPos++;
                            return true;
                        } else if ( true === this.bPlaceHolder ) {
                            return true;
                        }

                        this.portionContentPos++;
                    }

                    this.makeElementCurrentPos();
                } else {
                    // ;
                }
            }
        } else {
            if ( true === bTrackRevisions ) {
                if ( this.isCurLowLevel() ) {
                    return true;
                }
            }

            if ( true === this.selection.bUse ) {
                let startPos = this.selection.startPos;
                let endPos = this.selection.endPos;

                if ( startPos > endPos ) {
                    const temp = startPos;
                    startPos = endPos;
                    endPos = temp;
                }

                // 未全选中newControl，且newControl没有内容只有默认占位符时，不能被删除
                // if ( true === this.bPlaceHolder && this.content.length > endPos - startPos ) {
                //     this.removeSelection();
                //     this.portionContentPos = endPos;
                //     return true;
                // }

                // console.log(startPos, endPos)
                // 是否选中了段尾
                if ( true === bSelectParaEnd && null != this.getParaEnd() ) {
                    // image del lock. need improve
                    // tslint:disable-next-line: max-line-length
                    //   if (this.paragraph.parent.getDrawingObjects().checkImageDeleteLockInSelection(this.content.slice(startPos, endPos))) {
                    //     return null;
                    //   }
                    this.removeFromContent(startPos, endPos - startPos + 1, true);
                } else {
                    // tslint:disable-next-line: max-line-length
                    //   if (this.paragraph.parent.getDrawingObjects().checkImageDeleteLockInSelection(this.content.slice(startPos, endPos))) {
                    //     return null;
                    //   }

                    if ( !(true === bTrackRevisions && this.canDeleteInReviewMode() && this.isNewControl()) ) {
                        this.removeFromContent(startPos, endPos - startPos, true);
                    }
                }

                this.removeSelection();
                this.portionContentPos = startPos;
            } else {
                // if (this.isComment()) {
                //     return false;
                // }
                const bComment = this.isComment();
                let curPos = this.portionContentPos;

                const maxLen = this.content.length;
                if (curPos > maxLen) {
                    curPos = maxLen;
                }

                if ( 0 > direction ) {
                    // curPos--;

                    if ( 0 >= curPos ) {
                        return false;
                    }

                    curPos--;
                    if (bComment) {
                        this.portionContentPos = curPos;
                        return false;
                    }

                    if ( ParaElementType.ParaNewControlBorder === this.content[curPos].type ) {
                        this.portionContentPos = curPos;
                        return true;
                    } else if ( true === this.bPlaceHolder
                        || ( this.isNewControlStart() && 1 < this.content.length ) ) {
                        return true;
                    }

                    this.removeFromContent(curPos, 1, true);
                    this.portionContentPos = curPos;
                } else {
                    if (bComment) {
                        return false;
                    }
                    // curPos++;

                    if ( 0 === this.content.length || this.content.length <= curPos ||
                        ParaElementType.ParaEnd === this.content[curPos].type ) {
                        return false;
                    }

                    if ( ParaElementType.ParaNewControlBorder === this.content[curPos].type ) {
                        this.portionContentPos = curPos + 1;
                        return true;
                    } else if ( true === this.bPlaceHolder ) {
                        return true;
                    }

                    this.removeFromContent(curPos, 1, true);
                    this.portionContentPos = curPos;
                }

                this.paragraph.parent.setDirty();
            }
        }

        return true;
    }

    public getCurrentText(): ParaElementBase {
        const selection = this.selection;
        let text: ParaElementBase;
        const maxLen = this.content.length - 1;
        if (selection.bUse) {
            let start = selection.startPos;
            let end = selection.endPos;
            if (start > end) {
                const pos = start;
                start = end;
                end = pos;
            }

            if (start > maxLen) {
                start = maxLen;
            }
            text = this.content[start];
        } else {
            let start = this.portionContentPos;
            if (start > maxLen) {
                start = maxLen;
            }
            text = this.content[start];
        }

        return text;
    }

    public getCurrentVisibleText(): ParaElementBase {
        const selection = this.selection;
        // let text: ParaElementBase;
        const maxLen = this.content.length - 1;
        let start: number;
        let end: number;
        if (selection.bUse) {
            start = selection.startPos;
            end = selection.endPos;
            if (start > end) {
                const pos = start;
                start = end;
                end = pos;
            }
        } else {
            start = this.portionContentPos;
            end = maxLen;
        }

        let index: number = start;
        const contents = this.content;
        for (; index <= end; index++) {
            if (index > maxLen) {
                index = maxLen;
            } else if (!contents[index].isVisible()) {
                continue;
            }
            break;
        }

        return this.content[index];
    }

    public getParentPos(pos: ParagraphContentPos): void {
        const index = this.paragraph.content.findIndex((item) => item === this);
        pos.splice(0, 0, [index]);
        this.paragraph.getParentPos(pos);
    }

    /**
     * 插入段落结束符
     */
    public addEnd( textPr?: TextProperty ): void {
        const item = new ParaText(String.fromCharCode(0x21B5)); // 00B6 21A9 2BA0 21B5
        item.type = ParaElementType.ParaEnd;

        if ( undefined !== textPr ) {
            this.textProperty = textPr;
        }

        this.addToContent(this.content.length, item);
    }

    public addEndInternal( textPr?: TextProperty ): void {
        const item = new ParaText(String.fromCharCode(0x21B5));
        item.type = ParaElementType.ParaEnd;

        if ( undefined !== textPr ) {
            this.textProperty = textPr;
        }

        this.content.splice(0, 0, item);
    }

    public deleteLastSpaces(bSpace: boolean, bTag: boolean): number {
        const contents = this.content;
        if (contents.length === 0) {
            return 0;
        }

        let endIndex = -1;
        const length = contents.length;
        let isParaEnd = true;
        let sub = 0;
        for (let index = length - 1; index >= 0; index--) {
            const text = contents[index];
            const type = text.type;
            if (type === ParaElementType.ParaEnd) {
                sub = 1;
                continue;
            }
            isParaEnd = false;
            if (bSpace && bTag) {
                if (type === ParaElementType.ParaTab || type === ParaElementType.ParaSpace
                    || type === ParaElementType.ParaText && text.content === ' ') {
                    endIndex = index;
                } else {
                    break;
                }
            } else if (bSpace) {
                if (type === ParaElementType.ParaSpace || type === ParaElementType.ParaText && text.content === ' ') {
                    endIndex = index;
                } else {
                    break;
                }
            } else {
                if (type === ParaElementType.ParaTab) {
                    endIndex = index;
                } else {
                    break;
                }
            }
        }

        if (isParaEnd) {
            return 0;
        }

        if (endIndex === -1) {
            return 1;
        }

        this.removeFromContent(endIndex, length - endIndex - sub, false);
        this.portionContentPos = endIndex;
        if (this.content.length === 0) {
            return 0;
        }
        return 2;
    }

    public setBorderHide(): void {
        const contents = this.content;
        if (!contents.length) {
            return;
        }
        contents[0].bVisible = false;
    }

    /**
     * 删除段落结束符
     */
    public removeParaEnd(): boolean {
        const length = this.content.length;

        for (let index = length - 1; index >= 0; index++) {
            const element = this.content[index];

            if ( ParaElementType.ParaEnd === element.type ) {
                this.removeFromContent(index, 1, true);
                return true;
            }
        }

        return false;
    }

    /**
     * 将portion分割，startPos从0开始计算
     * @param startPos
     * @param endPos
     */
    public split(contentPos: ParagraphContentPos, depth: number = 1): ParaPortion {
        const curPos = contentPos.get(depth);
        return this.splitImplement(curPos);
    }

    /**
     * 分割portion
     * @param pos
     */
    public splitPortion( pos: number ): ParaPortion {
        const history = this.getHistory();
        if ( history ) {
            history.addChange(new ChangePortionStartSplit(this, pos));
        }

        const newPortion = new ParaPortion(this.paragraph);
        newPortion.setProperty(this.textProperty.copy());
        newPortion.bHidden = this.bHidden;

        // 清除新portion的波浪线属性
        clearWavyUnderlineFromTextProperty(newPortion.textProperty);

        const oldCursorPos = this.portionContentPos;
        const oldStartPos = this.selection.startPos;
        const oldEndPos = this.selection.endPos;

        newPortion.concatToContent(this.content.slice(pos));
        this.removeFromContent(pos, this.content.length - pos, true, true);

        // 重新设置portion中光标的位置
        if ( oldCursorPos >= pos ) {
            this.portionContentPos = this.content.length;
            newPortion.portionContentPos = oldCursorPos - pos;
        } else {
            newPortion.portionContentPos = 0;
        }

        //  重新设置portion中光标选择的位置
        if ( oldStartPos >= pos ) {
            newPortion.selection.startPos = oldStartPos - pos;
            this.selection.startPos = this.content.length;
        } else {
            this.selection.startPos = 0;
        }

        if ( oldEndPos >= pos ) {
            newPortion.selection.endPos = oldEndPos - pos;
            this.selection.endPos = this.content.length;
        } else {
            this.selection.endPos = 0;
        }

        if ( history ) {
            history.addChange(new ChangePortionEndSplit(this, newPortion));
        }

        return newPortion;
    }

    public setParagragh(para: Paragraph): void {
        this.paragraph = para;
    }

    public getParagragh(): Paragraph {
        return this.paragraph;
    }

    public getType(): ParaElementType {
        return this.type;
    }

    public setProperty(textPr: TextProperty): void {
        this.textProperty = textPr;
        this.recalcInfo.bMeasure = true;
    }

    public getTextProperty( contentPos: ParagraphContentPos, depth: number = 1 ): TextProperty {
        return this.textProperty.copy();
    }

    public getDirectTextProperty(): TextProperty {
        return this.textProperty;
    }

    /**
     * 获得portion的显示宽度
     */
    // getPortionVisibleWidth():  number{
    //     this.width = 0;

    //     for (let i = 0, len = this.content.length; i < len; ++i){
    //         this.width += this.content[i].widthVisible;
    //     }

    //     return this.width;
    // }

    /**
     * 测量字符对象的高宽
     * @param item
     * @param textPr
     */
    public measure(item: ParaText, textPr?: TextProperty): number {
        if ( !textPr) {
            textPr = this.textProperty;
        }

        let text = item.content;
        // 字符对象为空格，则使用"我"替代
        if ( item.isSpace()) {
            text = '我';
            item.type = ParaElementType.ParaSpace;
        } else if ( item.isEastAsianScript() ) {
            text = '我';
        }

        const m = measure(text, textPr)[0];

        // 字符对象为段落结束符，强制指定其宽度 = 0
        // if ( item.isParaEnd() ) {
        //     m.width = 0;
        // }

        item.width = m.width;

        // 字符对象为空格，强制指定其宽度 / 2
        if ( item.isSpace()) {
            item.width /= 2;
        }

        item.widthVisible = item.width;

        return m.height;
    }

    /**
     * 检查portion是否只包含三种字符：空格，-，0
     */
    public isSpecialCharactor(): boolean {
        for (const content of this.content) {
            if (content.isParaEnd()) {
                continue;
            }
            if (/[^0\- ]/.test(content.content)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 检查portion是否为空，分两种情况：
     * 1. portion只有一个段落结束符
     * 2. portion为空，没有内容
     * @param bSkipParaEnd 是否考虑段落结束符
     */
    public isEmpty( bSkipParaEnd: boolean ): boolean {
        const nCount = this.content.length;

        if ( true !== bSkipParaEnd ) {
            if ( 0 < nCount ) {
                return false;
            } else {
                return true;
            }
        }

        // 只有段落结束符
        if ( 1 === nCount && this.content[0].isParaEnd()) {
           return true;
        }

        if ( nCount > 0) {
            return false;
        } else {
            return true;
        }
    }

    /** 检查portion是否为空（无有效内容） */
    public isEmptyContent(): boolean {
        const nCount = this.content.length;
        if (nCount === 0) {
            return true;
        }
        const first = this.content[0];
        if (nCount === 1 && (first.isParaEnd() || !first.content.length)) {
            return true;
        }
        return false;
    }

    public isEmptyRange( line: number, range: number = 0 ): boolean {
        const curLine = line - this.startLine;
        const curRange = ( 0 === curLine ? range - this.startRange : range );

        const startPos = this.getRangeStartPos(curLine, curRange);
        const endPos = this.getRangeEndPos(curLine, curRange);

        if ( startPos >= endPos ) {
            return true;
        }

        return false;
    }

    /**
     * 是否为段落结束的portion
     */
    public isParaEndPortion(): boolean {
        return ( null != this.getParaEnd() ? true : false);
        // const length = this.content.length;

        // if ( 0 === length ) {
        //     return false;
        // }

        // if ( ParaElementType.ParaEnd === this.content[length - 1].type) {
        //     return true;
        // }

        // return false;
    }

    /**
     * 是否为段落结束的portion
     */
    public isSingleParaEndPortion(): boolean {
        const length = this.content.length;

        if ( 1 === length && ParaElementType.ParaEnd === this.content[length - 1].type ) {
            return true;
        }

        return false;
    }

    /**
     * 是否为newControl的开始portion
     */
    public isNewControlStart(): boolean {
        if ( this.content.length > 0 && true === this.content[0].isNewControlStartBoder() ) {
            return true;
        }

        return false;
    }

    /**
     * 是否为newControl的结束portion
     */
    public isNewControlEnd(): boolean {
        if ( this.content.length > 0 && true === this.content[this.content.length - 1].isNewControlEndBoder() ) {
            return true;
        }

        return false;
    }

    public isNewControl(): boolean {
        const content = this.content[0];
        if (!content) {
            return false;
        }

        if (true === content.isNewControlEndBoder() || content.isNewControlStartBoder() === true) {
            return true;
        }

        return false;
    }

    /**
     * 获取以此portion开始的newControl名称
     */
    public getStartNewControlName(): string {
        if ( true === this.isNewControlStart() ) {
            return this.content[0].getNewControlName();
        }

        return null;
    }

    public getEndNewControlName(): string {
        if ( true === this.isNewControlEnd() ) {
            return this.content[0].getNewControlName();
        }

        return null;
    }

    public isParaEnd(): boolean {
        const contents = this.content;
        const type = ParaElementType.ParaEnd;
        let length = contents.length - 2;
        if (length < 0) {
            length = 0;
        }
        for (let index = contents.length - 1; index >= length; index--) {
            if ( type === contents[index].type ) {
                return true;
            }
        }

        return false;
    }

    public getParaEnd(): ParaElementBase {
        const contents = this.content;
        const type = ParaElementType.ParaEnd;
        for (let index = contents.length - 1; index >= 0; index--) {
            if ( type === contents[index].type ) {
                return this.content[index];
            }
        }

        return null;
    }

    public getSelectionNodePos(pos: ParagraphContentPos, bStart: boolean, bApplyToAll: boolean): void {
        const contents = this.content;
        const activeIndex = getAcitveNodeIndex(this.selection, bStart, bApplyToAll, contents.length - 1);
        pos.add(activeIndex);
    }

    public getSelectText(bApplyToAll: boolean): string {
        if (this.bPlaceHolder === true && this.content.length === 1) {
            return '';
        }

        if (ReviewType.Remove === this.reviewType ||
            (this.reviewInfo && this.reviewInfo.isSecondInfo())) {
            return '';
        }

        let text = '';
        let start: number;
        let end: number;
        if (bApplyToAll === true) {
            start = 0;
            end = this.content.length;
        } else {
            const selection = this.selection;
            start = selection.startPos;
            end = selection.endPos;
            if (start > end) {
                const pos = start;
                start = end;
                end = pos;
            }
        }

        const contents = this.content.slice(start, end);
        contents.forEach((content) => {
            switch (content.type) {
                case ParaElementType.ParaPageNum: {
                    text += (content as ParaPageNum).getRenderedText(true, this.paragraph.getDocument().curPage + 1);
                    break;
                }
                case ParaElementType.ParaText:
                case ParaElementType.ParaTab:
                case ParaElementType.ParaDrawing:
                case ParaElementType.ParaMedEquation:
                case ParaElementType.ParaBarcode:
                case ParaElementType.ParaQRCode:
                case ParaElementType.ParaSpace:
                    text += content.content;
                    break;
                case ParaElementType.ParaNewLine:
                    text += '\n';
                    break;
                default:
            }
        });

        return text;
    }

    public getSelectTextAI(bApplyToAll: boolean): string {
        if (this.bPlaceHolder === true && this.content.length === 1) {
            return '';
        }

        if (ReviewType.Remove === this.reviewType ||
            (this.reviewInfo && this.reviewInfo.isSecondInfo())) {
            return '';
        }

        let text = '';
        let start: number;
        let end: number;
        if (bApplyToAll === true) {
            start = 0;
            end = this.content.length;
        } else {
            const selection = this.selection;
            start = selection.startPos;
            end = selection.endPos;
            if (start > end) {
                const pos = start;
                start = end;
                end = pos;
            }
        }

        const contents = this.content.slice(start, end);
        contents.forEach((content) => {
            switch (content.type) {
                case ParaElementType.ParaPageNum: {
                    text += (content as ParaPageNum).getRenderedText(true, this.paragraph.getDocument().curPage + 1);
                    break;
                }
                case ParaElementType.ParaNewControlBorder:
                    {
                        if(content.isNewControlStartBoder())
                        {
                            text += '[';
                        }else if(content.isNewControlEndBoder()){
                            text += ']';
                        }
                        break;
                    }
                case ParaElementType.ParaText:
                case ParaElementType.ParaTab:
                case ParaElementType.ParaDrawing:
                case ParaElementType.ParaText:
                case ParaElementType.ParaTab:
                case ParaElementType.ParaDrawing:
                case ParaElementType.ParaMedEquation:
                case ParaElementType.ParaBarcode:
                case ParaElementType.ParaQRCode:
                case ParaElementType.ParaSpace:
                    text += content.content;
                    break;
                case ParaElementType.ParaNewLine:
                    text += '\n';
                    break;
                default:
            }
        });

        return text;
    }

    /**
     * 光标位置是否在portion开始位置
     */
    public isCusorInStart(): boolean {
        if ( this.portionContentPos <= 0 ) {
            return true;
        }

        return false;
    }

    /**
     * 光标位置是否在portion尾部
     */
    public isCusorInEnd(): boolean {
        if ( this.portionContentPos >= this.content.length ) {
            return true;
        }

        return false;
    }

    /**
     * 检查是否需要修正光标的位置
     */
    public isCusorNeedCorrectPos(): boolean {
        // 当前portion为空，段落结束符
        if ( true === this.isEmpty(true) ) {
            return true;
        }

        if (this.isHidden() || this.isComment()) {
            return true;
        }

        let bNewStart = false;
        let bEnd = false;
        const pos = this.portionContentPos;

        for (let curLine = 0, count = this.getLinesCount(); curLine < count; curLine++) {
            const rangesCount = this.getRangesCount[curLine];

            const startPos = this.getRangeStartPos(curLine);
            const endPos = this.getRangeEndPos(curLine);

            if ( 0 !== curLine ) {
                if ( pos === startPos ) {
                    bNewStart = true;
                }
            }

            if ( pos === endPos ) {
                bEnd = true;
            }

            if ( true === bNewStart ) {
                break;
            }
        }

        if ( true !== bNewStart && true !== bEnd && true === this.isCusorInStart() ) {
            return true;
        }

        return false;
    }

    /**
     * portion的内容占据2行以上，即：portion是否在会在一行的开始
     */
    public isStartFromNewLine(): boolean {
        if ( this.getLinesCount() < 2 ||  0 !== this.getRangeStartPos(1) ) {
            return false;
        }

        return true;
    }

    public isCheckBox(): boolean {
        if (this.content.length !== 1) {
            return false;
        }

        const content = this.content[0] as any;
        if (!content.getTypeName) {
            return false;
        }

        return content.getTypeName() === ParaTextName.ParaCheckbox;
    }

    // public canRemoveAtCurCell(bStart: boolean, bEnd: boolean): number {
    //     const selection = this.selection;
    //     if (selection.bUse) {
    //         let start = selection.startPos;
    //         let end = selection.endPos;
    //         if (start > end) {
    //             const tem = start;
    //             start = end;
    //             end = tem;
    //         }

    //         for
    //     }
    // }

    public isPortionStyle(): boolean {
        const content = this.content[0] as any;
        if (!content || !content.getTypeName) {
            return false;
        }

        return content.getTypeName() === ParaTextName.PortionStyle;
    }

    /**
     * 测量portion内容，设置portion的文本高度，行高
     */
    public recalculateMeasureContent(textHeight: number, paraPr: ParaProperty): void {
        const bMeasure = (0 === textHeight && this.isEmpty(false));

        if ( false === this.recalcInfo.bMeasure && false === bMeasure ) {
            return ;
        }
       
        //  if ( false === this.recalcInfo.bMeasure )
        //      return;
        

        const ratio = this.recalculateLineSpacing(paraPr);

        // 重新测量空portion，更新光标
        if ( bMeasure ) {
            textHeight = this.measure(new ParaText('a'));
        }

        this.textHeight = textHeight;
        this.ascent = this.textHeight * ratio * 0.4;
        this.descent = this.textHeight * ratio * 0.6; // this.ascent;

        this.recalcInfo.bRecalc = true;
        this.recalcInfo.bMeasure = false;
    }

    /**
     * 计算行间距
     * @param paraPr
     */
    public recalculateLineSpacing(paraPr: ParaProperty): number {
        let ratio = ( LineSpacingRatio.Single - 1 ); // / 2;

        if ( paraPr.paraSpacing.lineSpacingType !== LineSpacingType.Fixed ) {
            ratio = ( paraPr.paraSpacing.lineSpacing - 1 ); // / 2;
        } else {
            const cmSingleLineSpacing = this.paragraph.calculateSingleLineSpacing(16, LineSpacingRatio.Single);
            const rawRatio = paraPr.paraSpacing.lineSpacing / cmSingleLineSpacing;
            ratio *= rawRatio;
        }

        return ratio;
    }

    /**
     * 在当前行的range对portion进行排版
     * @param oPRS
     * @param paraPr
     * @param depth
     */
    // tslint:disable-next-line: max-line-length
    public recalculateRange(oPRS: ParagraphRecalculateStateWrap, paraPr: ParaProperty, depth: number = 1, curPage?: number): void {
        // console.log("portion_recalculate_Range")
        if ( oPRS.paragraph !== this.paragraph ) {
            this.paragraph = oPRS.paragraph;
            this.recalcInfo.bTextPr = true;
            this.recalcInfo.bMeasure = true;
        }

        const para = oPRS.paragraph;
        const curLine = oPRS.line - this.startLine;
        const curRange = ( 0 === curLine ? oPRS.range - this.startRange : oPRS.range );
        // 保存portion在当前行的位置
        const portionContentStartPos = this.addRange(curLine, curRange);
        let portionContentEndPos = 0;

        let bMoveToLBP           = oPRS.bMoveToLBP;
        let bNewRange            = oPRS.bNewRange;
        const bForceNewPage        = oPRS.bForceNewPage;
        const bNewPage             = oPRS.bNewPage;
        let bEnd                 = oPRS.bEnd;

        let bWord                = oPRS.bWord;
        let bStartWord           = oPRS.bStartWord;
        let bFirstItemOnLine     = oPRS.bFirstItemOnLine;
        let bEmptyLine           = oPRS.bEmptyLine;
        let bTextOnLine          = oPRS.bTextOnLine;

        const rangesCount         = oPRS.rangesCount;

        let spaceLen            = oPRS.spaceLen;
        let wordLen             = oPRS.wordLen;

        let x                   = oPRS.x;
        const xEnd                = oPRS.xEnd;

        const paraLine            = oPRS.line;
        const paraRange           = oPRS.range;

        let pos = portionContentStartPos;

        const contentLen = this.content.length;
        let textHeight = 0;
        let fixedLength = 0;
        const bHidden = this.isHidden();
        const bNewControlFixed = (0 < oPRS.fixedLength && this.isNewControlEnd()
                                && this.getEndNewControlName() === oPRS.newControlName);

        // 超过可编辑宽度，开始下一个新range的排版
        // key-wasm by tinyzhi
        // if (false === bStartWord && true === bFirstItemOnLine && xEnd - x < 0.001 && rangesCount > 0) {
        const bResult = WasmInstance.instance._ParaPortion_recalculateRange(Number(bStartWord),
                                            Number(bFirstItemOnLine), xEnd, x, rangesCount);
        if (Boolean(bResult)) {
        // end by tinyzhi
            bNewRange = true;
            portionContentEndPos = pos;
        } else if (bHidden !== true || bNewControlFixed) {
            for (; pos < contentLen; pos++) {
                const item = this.content[pos];
                // if ( false === item.bVisible && ParaElementType.ParaEnd !== item.type ) {
                //     continue;
                // }

                // ParaDrawing and ParaEnd's height shouldn't be considered in Recalculation
                const itemHeight = item.measure(this.textProperty);
                textHeight = (item.type !== ParaElementType.ParaDrawing && ParaElementType.ParaBarcode !== item.type
                            && item.type !== ParaElementType.ParaMedEquation && ParaElementType.ParaQRCode !== item.type)
                            ? itemHeight : textHeight;

                // console.log(textHeight);

                switch (item.type) {
                    case ParaElementType.ParaSym:
                    case ParaElementType.ParaText:
                    case ParaElementType.ParaSpace:
                    case ParaElementType.ParaTab:
                        case ParaElementType.ParaButton:
                    case ParaElementType.ParaNewControlBorder: {
                        bStartWord = true;

                        if ( ParaElementType.ParaNewControlBorder === item.type && item.isNewControlEndBoder() ) {
                            if (!oPRS.newControlName) {
                                item.setRemainWidth(0);
                            } else if ( item.getNewControlName() === oPRS.newControlName ) {
                                item.width = (bHidden && bNewControlFixed) ? 0 : item.width;
                                const bBreak = oPRS.newControlEndBorderBreak;
                                const alignType = oPRS.newControlAlign;
                                this.setNewControlFixedLength(oPRS, item, fixedLength, x, wordLen);

                                if ( bBreak ) {
                                    oPRS.resetNewControlFixedLength();
                                } else if (AlignType.Center === alignType) {
                                    x += oPRS.startXDiff;
                                    oPRS.startXDiff = 0;
                                }
                            }
                        }

                        let tempLen = item.width;

                        // 前一个元素是否为中日韩文字或标点或空格
                        if ( true !== bWord ) {
                            // 是否为此行第一个单词组合，或者数字组合，如果为true则不需要进行下面的宽度判断
                            if ( true !== bFirstItemOnLine) {
                                // 判断是否超过可编辑宽度，超过则在新行开始下一个新range的排版
                                if ( x + tempLen > xEnd ) {
                                    // 元素为字符，且不能放在行首，同时此item不在首行
                                    if ( (ParaElementType.ParaText === item.type
                                        || ParaElementType.ParaSpace ===  item.type
                                        || ( ParaElementType.ParaNewControlBorder === item.type
                                                && item.isNewControlEndBoder() ) )
                                        && !item.canBeAtBeginOfLine() && !oPRS.bLineBreakFirst) {
                                        bMoveToLBP = true;
                                    } else {
                                        portionContentEndPos = pos;
                                    }
                                    bNewRange = true;
                                }
                            }

                            if (true !== bNewRange ) {
                                if ( ParaElementType.ParaText === item.type
                                    || ParaElementType.ParaSpace === item.type
                                    || ParaElementType.ParaTab === item.type
                                    || ParaElementType.ParaNewControlBorder === item.type ) {
                                    // 是否可以断行，并且当前字符是否可放在行首：主要处理标点
                                    if ( oPRS.bLineBreakFirst && !item.canBeAtBeginOfLine() ) {
                                        bFirstItemOnLine = true;
                                        tempLen += spaceLen;
                                        spaceLen = 0;
                                    } else if (item.canBeAtBeginOfLine()) {
                                        oPRS.setLineBreakPos(pos, bFirstItemOnLine);
                                    }
                                }

                                // 针对中日韩文字和标点
                                if ( item.isSpaceAfter() || oPRS.bWordWrap ) {
                                    x += tempLen; // 将空格的长度添加到单词和单词本身的宽度

                                    bWord = false;
                                    bEmptyLine = false;
                                    bFirstItemOnLine = false;
                                    bTextOnLine = true;
                                    spaceLen = 0;
                                    wordLen = 0;
                                } else {
                                    // 针对西文，数字等  针对西文标点
                                    bWord = true;
                                    wordLen = tempLen;
                                }
                                fixedLength += tempLen;
                            }
                        } else {
                            // 当前字符超过可编辑宽度
                            if ( x + wordLen + tempLen > xEnd ) {
                                bNewRange = true;

                                if ( true === bFirstItemOnLine ) {
                                    if (false === para.checkRanges(paraLine, paraRange)) {
                                        bMoveToLBP = true;
                                    } else {
                                        bEmptyLine = false;
                                        bTextOnLine = true;
                                        x += wordLen;
                                        portionContentEndPos = pos;
                                    }
                                } else {
                                    bMoveToLBP = true;
                                }
                            }

                            if ( true !== bNewRange ) {
                                wordLen += tempLen;

                                if ( item.isSpaceAfter() || oPRS.bWordWrap ) {
                                    x += wordLen; // 将空格的长度添加到单词和单词本身的宽度

                                    bWord = false;
                                    bEmptyLine = false;
                                    bFirstItemOnLine = false;
                                    bTextOnLine = true;
                                    spaceLen = 0;
                                    wordLen = 0;
                                }
                                fixedLength += tempLen;
                            }
                        }
                        break;
                    }

                    // case ParaElementType.ParaTab: {
                    //     if ( x + item.width > xEnd ) {
                    //         portionContentEndPos = pos;
                    //         bNewRange = true;
                    //     } else {
                    //         x += item.width;
                    //     }

                    //     bWord = false;
                    //     bEmptyLine = false;
                    //     bFirstItemOnLine = false;
                    //     bTextOnLine = true;
                    //     spaceLen = 0;
                    //     wordLen = 0;
                    //     break;
                    // }

                    case ParaElementType.ParaEnd: {
                        if ( true === bWord ) {
                            bFirstItemOnLine = false;
                            bEmptyLine = false;
                            bTextOnLine = true;
                        }

                        x += wordLen;

                        if ( true === bWord ) {
                            x += spaceLen;
                            spaceLen = 0;
                            wordLen = 0;
                        }

                        bNewRange = true;
                        bEnd = true;
                        portionContentEndPos = pos + 1;
                        break;
                    }

                    case ParaElementType.ParaDrawing:
                    case ParaElementType.ParaMedEquation:
                    case ParaElementType.ParaBarcode:
                    case ParaElementType.ParaQRCode:
                    case ParaElementType.ParaPageNum: {
                        if ( true === bStartWord ) {
                            bFirstItemOnLine = false;
                        }

                        if ( true === bWord || 0 < wordLen ) {
                            x += spaceLen + wordLen;
                            bWord = false;
                            wordLen = 0;
                            spaceLen = 0;
                            bEmptyLine = false;
                            bTextOnLine = true;
                        }

                        let itemWidth = item.width;
                        if (item.type === ParaElementType.ParaPageNum && this.isInHeaderFooter()) {
                            const itemDetailed = item as ParaPageNum;
                            const pageIndex = this.paragraph.pageNum + curPage;
                            // console.log(itemDetailed.getNumWidths());
                            const numWidth = itemDetailed.getNumWidths()[pageIndex];
                            if (numWidth != null) {
                                itemWidth = numWidth;
                            }
                        }

                        
                        // add: bFirstItemOnLine
                        if ( x + itemWidth > xEnd && false === bFirstItemOnLine ) {
                            bNewRange = true;
                            portionContentEndPos = pos;
                        } else {
                            x += itemWidth;
                            bEmptyLine = false;
                            bFirstItemOnLine = false;
                            fixedLength += itemWidth;
                        }

                        spaceLen = 0;

                        break;
                    }

                    case ParaElementType.ParaNewLine: {
                        if ( true === bWord ) {
                            bWord = false;
                            bEmptyLine = false;
                            bTextOnLine = true;
                            spaceLen = 0;
                        }

                        x += wordLen;

                        oPRS.bSoftLine = true;
                        bNewRange = true;
                        portionContentEndPos = pos + 1;
                        break;
                    }
                }

                oPRS.lastItem.portionContentPos = pos;

                if ( true === bNewRange ) {
                    break;
                }
            }
        }

        if ( 0 < oPRS.fixedLength && 0 < fixedLength && !oPRS.newControlEndBorderBreak ) {
            if ( bNewRange ) {
                oPRS.setLineBreakPos(pos, false);
                oPRS.newControlContentBreak = true;
            }
            oPRS.fixedLength -= fixedLength;
        }

        oPRS.bMoveToLBP = bMoveToLBP;
        oPRS.bNewRange = bNewRange;
        oPRS.bForceNewPage = bForceNewPage;
        oPRS.bNewPage = bNewPage;
        oPRS.bEnd = bEnd;

        oPRS.bWord = bWord;
        oPRS.bStartWord = bStartWord;
        oPRS.bFirstItemOnLine = bFirstItemOnLine;
        oPRS.bEmptyLine = bEmptyLine;
        oPRS.bTextOnLine = bTextOnLine;

        oPRS.spaceLen = spaceLen;
        oPRS.wordLen = wordLen;

        oPRS.x = x;
        oPRS.xEnd = xEnd;

        if ( pos >= contentLen ) {
            portionContentEndPos = pos;
        }
        portionContentEndPos = bHidden ? contentLen : portionContentEndPos;
        this.fillRange(curLine, curRange, portionContentStartPos, portionContentEndPos);

        this.recalculateMeasureContent(textHeight, paraPr);

        if ( this.isSingleParaEndPortion() && 1 < para.content.length ) {
            this.resetParaEndProperty(para);
        }

        if (bHidden) {
            this.recalcInfo.bMeasure = true;
        }

        this.recalcInfo.bRecalc = false;
    }

    /**
     * 设置当前行的当前range末尾所在的portion
     * @param oPRS
     * @param depth
     */
    public recalculateSetRangeEndPos(oPRS: ParagraphRecalculateStateWrap, depth: number = 1): void {

        const curLine = oPRS.line - this.startLine;
        const curRange = (0 === curLine) ? oPRS.range - this.startRange : oPRS.range;
        const curPortionPos = oPRS.lineBreakPos.get(depth);

        this.fillRangeEndPos(curLine, curRange, curPortionPos);
    }

    /**
     * 计算当前行的当前range的portion宽度，统计字符数，空格数等
     * @param oPRSC
     * @param curLine
     * @param curRange
     */
    // tslint:disable-next-line: max-line-length
    public recalculateRangeWidth(oPRSC: ParagraphRecalculateStateCounter, curLine: number, curRange: number = 0, curPage?: number): void {
        const line = curLine - this.startLine;
        const startPos = this.getRangeStartPos(line, curRange);
        const endPos = this.getRangeEndPos(line, curRange);

        for (let pos = startPos; pos < endPos; pos++) {
            const item = this.content[pos];

            switch (item.type) {
                case ParaElementType.ParaButton:
                case ParaElementType.ParaText:
                case ParaElementType.ParaSym:
                case ParaElementType.ParaSpace:
                case ParaElementType.ParaTab:
                case ParaElementType.ParaNewControlBorder: {
                    oPRSC.letters++;

                    if (true !== oPRSC.bWord) {
                        oPRSC.bWord = true;
                        oPRSC.words++;
                    }

                    oPRSC.range.width += item.width; // Item.Width / TEXTWIDTH_DIVIDER;//Item.Get_Width();
                    // oPRSC.range.width += oPRSC.spaceLen;

                    oPRSC.spaceLen = 0;

                    // 字符串中第一个单词之前的空格不计算在内
                    if (oPRSC.words > 1) {
                        oPRSC.spaces += oPRSC.spacesCount;
                    } else {
                        oPRSC.spacesSkip += oPRSC.spacesCount;
                    }

                    oPRSC.spacesCount = 0;

                    break;
                }

                case ParaElementType.ParaDrawing:
                case ParaElementType.ParaMedEquation:
                case ParaElementType.ParaBarcode:
                case ParaElementType.ParaQRCode:
                case ParaElementType.ParaPageNum: {
                    oPRSC.words++;
                    // oPRSC.range.width += oPRSC.spaceLen;

                    // 字符串中第一个单词之前的空格不计算在内
                    if (oPRSC.words > 1) {
                        oPRSC.spaces += oPRSC.spacesCount;
                    } else {
                        oPRSC.spacesSkip += oPRSC.spacesCount;
                    }

                    oPRSC.bWord = false;
                    oPRSC.spacesCount = 0;
                    oPRSC.spaceLen = 0;

                    if ( true === item.isInline() ) {
                        if (item.type === ParaElementType.ParaPageNum && this.isInHeaderFooter()) {
                            const itemDetailed = item as ParaPageNum;
                            // const pageIndex = this.paragraph.pageNum + curPage;
                            // console.log(itemDetailed.getNumWidths());
                            const totalPages = this.paragraph.logicDocument.getPages().length;

                            // update the last numWidth of paraPageNum's numWidths
                            (item as ParaPageNum).updatePageNumWidths(this.textProperty, totalPages, true);
                            // const numWidth = itemDetailed.getNumWidths()[pageIndex];
                            const numWidths = itemDetailed.getNumWidths();
                            // const numWidth = paraPageNum.getNumWidths()[pageIndex];
                            const numWidth = numWidths[numWidths.length - 1];
                            if (numWidth != null) {
                                oPRSC.range.width += numWidth;
                            } else {
                                oPRSC.range.width += item.width;
                            }

                        } else {
                            oPRSC.range.width += item.width;
                        }
                    }

                    break;
                }

                /*case ParaElementType.ParaSpace:
                    // 上一个字符的类型
                    if ( true === oPRSC.bWord ) {
                        oPRSC.bWord        = false;
                        oPRSC.spacesCount = 1;
                        oPRSC.spaceLen    = item.width;//Item.Width / TEXTWIDTH_DIVIDER;//Item.Get_Width();
                    } else {
                        oPRSC.spacesCount++;
                        oPRSC.spaceLen += item.width;//Item.Width / TEXTWIDTH_DIVIDER;//Item.Get_Width();
                    }

                    oPRSC.range.width += item.width;
                    break;*/

                case ParaElementType.ParaEnd: {
                    if ( true === oPRSC.bWord ) {
                        oPRSC.spaces += oPRSC.spacesCount;
                    }

                    oPRSC.range.widthEnd = item.width; // Item.Get_widthVisible();

                    break;
                }
            }
        }
    }

    /**
     * 两端对齐时，设置每个文本对象的可见宽度
     * @param oPRSA
     * @param curLine
     * @param curRange
     * @param curPage
     */
    public recalculateRangeSpaces(oPRSA: ParagraphRecalculateStateAlign, curLine: number,
                                  curRange: number, curPage: number): void {
      const line = curLine - this.startLine;
      const startPos = this.getRangeStartPos(line, curRange);
      const endPos = this.getRangeEndPos(line, curRange);

        // EndPos - 1：行末的最后一个字符不需要添加额外宽度
      for (let pos = startPos; pos < endPos; pos++) {
            const item = this.content[pos];
            let widthVisible = 0;

            switch (item.type) {
                case ParaElementType.ParaText:
                case ParaElementType.ParaSym:
                case ParaElementType.ParaTab:
                case ParaElementType.ParaButton:
                case ParaElementType.ParaSpace: {
                    widthVisible = item.width;

                    if (0 !== oPRSA.lettersSkip) {
                        oPRSA.lettersSkip--;
                    } else {
                        const bSoftLineEnd = this.content[pos + 1]?.isParaSoftLine();
                        // EndPos - 1：行末的最后一个字符不需要添加额外宽度
                        if ( pos !== endPos - 1 && !bSoftLineEnd ) {
                            widthVisible += oPRSA.justifyWord;
                        }// widthVisible = Item.Get_Width() + oPRSA.JustifyWord;
                    }

                    item.widthVisible = widthVisible;

                    oPRSA.x += widthVisible;
                    oPRSA.lastWidth = widthVisible;
                    break;
                }

                case ParaElementType.ParaDrawing:
                case ParaElementType.ParaMedEquation:
                case ParaElementType.ParaBarcode:
                case ParaElementType.ParaQRCode: {
                    // const paraDrawing = item as ParaDrawing;

                    // get absolute page number index. curPage seems to be parapage number
                    // paraDrawing.setPageIndex(this.paragraph.pageNum + curPage);
                    // paraDrawing.setPortion(this);
                    // paraDrawing.setParaId(this.paragraph.id);
                    // if (this.paragraph.parent.isTableCellContent()) {
                    //   paraDrawing.setTableId(this.paragraph.parent.getTableId());
                    // }

                    if ( true === item.isInline() ) {
                        // paraDrawing.updatePosition();
                        oPRSA.x += item.widthVisible;
                        oPRSA.lastWidth = item.widthVisible;
                    }
                    break;
                }

                case ParaElementType.ParaPageNum: {
                    const paraPageNum = item as ParaPageNum;
                    // console.trace(); // from paragraphrecalculate
                    // paraPageNum.setPageIndex(this.paragraph.pageNum + curPage);
                    // paraPageNum.setParent(this);

                    if (this.isInHeaderFooter()) {
                        const pageIndex = this.paragraph.pageNum + curPage;
                        // console.log(paraPageNum.getNumWidths());
                        const numWidths = paraPageNum.getNumWidths();
                        // const numWidth = paraPageNum.getNumWidths()[pageIndex];
                        const numWidth = numWidths[numWidths.length - 1];

                        if (numWidth != null) {
                            oPRSA.x += numWidth;
                            oPRSA.lastWidth = numWidth;
                        } else {
                            oPRSA.x += item.widthVisible;
                            oPRSA.lastWidth = item.widthVisible;
                        }
                    }

                    break;
                }

                case ParaElementType.ParaNewControlBorder: {
                    // const paraNewControlBorder = item as ParaNewControlBorder;

                    // paraNewControlBorder.setParaIndex(this.paragraph.index);
                    // paraNewControlBorder.setPortionId(this.id);
                    // if (this.paragraph.parent.isTableCellContent()) {
                    //     paraNewControlBorder.tableIndex = this.paragraph.parent.getTableIndex();
                    // }

                    oPRSA.x += item.widthVisible;
                    oPRSA.lastWidth = item.widthVisible;
                    break;
                }

                /*case ParaElementType.ParaSpace:

                    widthVisible = item.width;//Item.Width / TEXTWIDTH_DIVIDER;//widthVisible = Item.Get_Width();

                    if (0 !== oPRSA.spacesSkip) {
                        oPRSA.spacesSkip--;
                    }
                    else if (0 !== oPRSA.spacesCounter) {
                        widthVisible += oPRSA.justifySpace;
                        oPRSA.spacesCounter--;
                    }

                    item.widthVisible = widthVisible;//Item.Set_widthVisible(widthVisible);

                    oPRSA.x += widthVisible;
                    oPRSA.lastWidth = widthVisible;

                    break;*/

                case ParaElementType.ParaEnd: {
                    oPRSA.x += item.widthVisible; // Item.widthVisible;

                    break;
                }

                // case ParaElementType.ParaNewLine:
                // {
                //     oPRSA.x += item.widthVisible;
                //     break;
                // }
            }
        }
    }

    /**
     * 获取当前行的最大文本高度，行距
     * @param oPRS 计算portion的文本高度，间距
     * @param curLine
     * @param curRange
     * @param paraPr
     */
    public recalculateLineMetrics(oPRS: ParagraphRecalculateStateWrap, curLine: number,
                                  curRange: number, paraPr: ParaProperty, curPage: number): void {
        if (this.isComment()) {
            this.updateCommentPos();
            return;
        }
       
        const line = curLine - this.startLine;
        const startPos = this.getRangeStartPos(line, curRange);
        const endPos = this.getRangeEndPos(line, curRange);
        // console.log(startPos, endPos);

        const bSecondRevision = (ReviewType.Remove === this.getReviewType() &&
                                this.getReviewInfo() && this.getReviewInfo().isSecondInfo());
        const bSecondFinalRevision = (bSecondRevision && this.paragraph.getDocument()
                                        && this.paragraph.getDocument().isFinalRevision());

        if (!bSecondFinalRevision) {
            for (let i = startPos; i < endPos; i++) {
                const paraElem = this.content[i];
                let paraElemHeight = 0;

                switch (paraElem.type) {
                    case ParaElementType.ParaDrawing:
                    case ParaElementType.ParaMedEquation:
                    case ParaElementType.ParaBarcode:
                    case ParaElementType.ParaQRCode: {
                        const paraDrawing = paraElem as ParaDrawing;

                        // get absolute page number index. curPage seems to be parapage number
                        paraDrawing.setPageIndex(this.paragraph.pageNum + curPage);
                        paraDrawing.setPortion(this);
                        paraDrawing.setParaId(this.paragraph.id);
                        if (this.paragraph.parent.isTableCellContent()) {
                        paraDrawing.setTableId(this.paragraph.parent.getTableId());
                        }

                        paraElemHeight = paraDrawing.height;
                        // console.log(paraElemHeight);
                        // oPRS.textHeight = ( oPRS.textHeight < paraElemHeight ) ? paraElemHeight : oPRS.textHeight;
                        if (oPRS.textHeight < paraElemHeight) {
                            oPRS.textHeight = paraElemHeight;
                            oPRS.paragraph.lines[curLine].vertAlign = paraDrawing.getVertAlign();
                        }
                        break;
                    }
                    case ParaElementType.ParaButton: {
                        const height: number = (paraElem as any).height;
                        if (oPRS.textHeight < height) {
                            oPRS.textHeight = height;
                        }
                        break;
                    }

                    case ParaElementType.ParaPageNum: {
                        const paraPageNum = paraElem as ParaPageNum;
                        // console.trace(); // from paragraphrecalculate
                        // paraPageNum.setPageIndex(this.paragraph.pageNum + curPage);
                        paraPageNum.setParent(this);
                        break;
                    }
                    case ParaElementType.ParaNewControlBorder: {
                        // const paraNewControlBorder = item as ParaNewControlBorder;

                        paraElem.setParaIndex(this.paragraph.index);
                        paraElem.setPortionId(this.id);
                        // if (this.paragraph.parent.isTableCellContent()) {
                        //     paraNewControlBorder.tableIndex = this.paragraph.parent.getTableIndex();
                        // }
                        break;
                    }
                }
                // if (ParaElementType.ParaDrawing === paraElem.type || ParaElementType.ParaMedEquation === paraElem.type) {
                //     const paraDrawing = paraElem as ParaDrawing;
                //     paraElemHeight = paraDrawing.height;
                //     // console.log(paraElemHeight);
                //     oPRS.textHeight = ( oPRS.textHeight < paraElemHeight ) ? paraElemHeight : oPRS.textHeight;
                // }
            }
            // console.log(this.textHeight);
        }

        
        // portion在下一行开始，在上一行没有内容
        if ( startPos || endPos ) {
            oPRS.textHeight = ( oPRS.textHeight < this.textHeight ) ? this.textHeight : oPRS.textHeight;

            oPRS.lineAscent = ( oPRS.lineAscent < this.ascent ) ? this.ascent : oPRS.lineAscent;
            oPRS.lineDescent = ( oPRS.lineDescent < this.descent ) ? this.descent : oPRS.lineDescent;
        }
    }

    public recalculatePageEndInfo(oPRS: ParagraphRecalculateStateWrap, curPage: number, curRange: number): void {
        //
    }

    /**
     * 根据定位的content的位置，计算光标坐标X
     * @param positionX
     * @param positionY
     * @param bCurPortionPos
     * @param curLine
     * @param curPage
     */
    public recalculateCurPos(positionX: number, positionY: number, bCurPortionPos: boolean,
                             curLine: number, curPage: number): number {
        const para = this.paragraph;
        const curLine2 = curLine - this.startLine;

        const startPos = this.getRangeStartPos(curLine2);
        const endPos = this.getRangeEndPos(curLine2);
        const endPos2 = bCurPortionPos ? Math.min(this.portionContentPos, endPos) : endPos;

        let curPos = startPos;
        const bHidden = this.isHidden();
        for (; curPos < endPos2; curPos++) {
            const item = this.content[curPos];

            if ( true === bHidden ) {
                continue;
            }

            // TODO: 页眉页脚的页码宽度问题。此处心有不甘
            // if (this.isInHeaderFooter() === true && item.type === ParaElementType.ParaPageNum) {
            //     const itemDetailed = item as ParaPageNum;
            //     console.log(curPage)
            //     positionX += itemDetailed.getNumWidths()[curPage];
            // } else {
            //     positionX += item.widthVisible;
            // }
            positionX += (item.widthVisible); // + item.getRemainWidth());
        }

        if ( bCurPortionPos && curPos === this.portionContentPos ) {
            para.curPos.realX = positionX;
            para.curPos.realY = positionY;
            para.curPos.pagePos = curPage;
        }

        return positionX;
    }

    /**
     * 获取当前portion在paragraph的位置索引
     */
    public getCurInParaIndex(): number {
        const contents = this.paragraph.content;
        const id = this.id;
        return contents.findIndex((portion) => portion.id === id);
    }

    public setCommentPosition(x: number, y: number): void {
        this.positionY = y;
        this.positionX = x;
        const content = this.content[0];
        if (!content) {
            return;
        }
        content.positionY = y;
        content.positionX = x;
    }

    public addComment(bStart: boolean, id: number): void {
        const commentText = new ParaComment(bStart, id);
        this.addToContent(0, commentText);
        this.setType(ParaElementType.ParaComment);
        const comment = this.paragraph.getDocument()
        .getCommentManager()
        .getCommentById(id);
        if (comment) {
            if (bStart) {
                comment.setStartPortion(this);
            } else {
                comment.setEndPortion(this);
            }
        }
    }

    public setType(type: ParaElementType): void {
        this.type = type;
    }

    public updateCommentPos(): void {
        const para = this.paragraph;

        const content = this.content[0] as ParaComment;
        if (!content || content.type !== ParaElementType.ParaComment) {
            return;
        }
        const doc = para.getDocument();
        if (!doc) {
            return;
        }
        const comment = doc.getCommentManager()
            .getCommentById(content.getCommentId());
        if (!comment) {
            return;
        }

        const pos = para.getCurContentPos();
        pos.add(this.getCurInParaIndex());
        pos.add(0);
        if (content.bStart) {
            comment.setStartPos(pos);
        } else {
            comment.setEndPos(pos);
        }
    }

    public showBackground(): boolean {
        const para = this.paragraph;
        const doc = para.getDocument();
        if (!doc) {
            return;
        }
        const content = this.content[0] as ParaComment;
        const comment = doc.getCommentManager()
            .getCommentById(content.getCommentId());
        if (!comment) {
            return false;
        }

        return comment.isHiddenBackground() === false && !comment.isHidden();
    }

    public isStartComment(): boolean {
        const content = this.content[0] as ParaComment;
        if (!content) {
            return false;
        }
        return content.bStart;
    }

    public isComment(): boolean {
        if (this.type === ParaElementType.ParaComment) {
            return true;
        }
        return false;
    }

    public getCommentId(): number {
        const content = this.content[0] as ParaComment;
        return content.commentId;
    }

    /**
     * 获取portion中选中的内容，从新返回一个新的portion
     */
    public getSelectdPortionContent(para: Paragraph, names: string[], bKeepHalfStructBorder: boolean = false,
                                    option?: {name: string, names: string[], bCopy?: boolean}): ParaPortion {
        // console.log(option.bCopy)
        // if coming from headerfooter, bCopy is undefined if from saveselectedareatostream()
        if (option.bCopy === true && this.reviewType === ReviewType.Remove && !this.isNewControl()) {
            return null;
        }
        const content = this.content;
        const contentPos = new ParagraphContentPos();
        this.getParaContentPos(contentPos, true, true);
        this.getParaContentPos(contentPos, true, false);
        let startPos = contentPos.get(0);
        let endPos = contentPos.get(1);
        if (startPos > endPos) {
            const pos = startPos;
            startPos = endPos;
            endPos = pos;
        }

        const contents = content.slice(startPos, endPos);
        const len = contents.length;

        if (len === 0) {
            return;
        } else if (len === 1 && contents[0].type === ParaElementType.ParaEnd) {
            return;
        }

        const portion = new ParaPortion(para);
        portion.bHidden = this.isHidden();
        portion.setTextProperty(this.textProperty.copy());
        portion.setReviewTypeWithInfo(this.reviewType, this.reviewInfo.copy());
        // let bContinue: boolean;
        // if (option && option.names && option.names.length) {
        //     if (option.name) {
        //         bContinue = true;
        //     } else {
        //         bContinue = false;
        //     }
        // }
        contents.forEach((text, index) => {
            if (text.type === ParaElementType.ParaEnd) {
                return;
            }

            if (text.type === ParaElementType.ParaNewControlBorder) {
                // const name: string = text.getNewControlName();
                // if (bContinue !== undefined) {
                //     const unCopyNames = option.names;
                //     const unCopyNameIndex = unCopyNames.findIndex((item) => item === name);
                //     if (bContinue) {
                //         if (option.name === name) {
                //             unCopyNames.splice(unCopyNameIndex, 1);
                //             option.name = null;
                //             if (unCopyNames.length) {
                //                 bContinue = false;
                //             } else {
                //                 bContinue = undefined;
                //             }
                //         }
                //         return;
                //     } else if (unCopyNameIndex > -1) {
                //         option.name = name;
                //         bContinue = true;
                //         return;
                //     }

                // } else if (bContinue) {
                //     return;
                // }
                if (names.length > 0) {
                    const name: string = text.getNewControlName();
                    const deleteIndex = names.findIndex((newControlName) => newControlName === name);
                    if (deleteIndex !== -1) {
                        names.splice(deleteIndex, 1);
                        if (!bKeepHalfStructBorder) {
                            return;
                        }
                    }
                }
            // } else if (bContinue) {
            //     return;
            }

            if (text.type === ParaElementType.ParaDrawing) {
                // const drawing = text as ParaDrawing;
                // 在前面进行判断，不需要在内部进行判断
                // if (drawing.copyProtect === true) {
                //     return;
                // }
                portion.addToContent(index, text.copy(true));
            } else {
                portion.addToContent(index, text.copy(false));
            }
        });
        portion.bPlaceHolder = this.bPlaceHolder;

        return portion;
    }

    /**
     * 确定光标所在行
     * @param paraPos
     */
    public getCurParaPos(paraPos: IParaPos): void {
        const pos = this.portionContentPos;

        if (-1 === this.startLine) {
            paraPos.line = -1;
            paraPos.page = -1;
            paraPos.pos = -1;
            paraPos.range = -1;
            return ;
        }

        let curLine  = 0;
        const lineCount = this.getLinesCount();
        for (; curLine < lineCount; curLine++) {
            const startPos = this.getRangeStartPos(curLine);
            const endPos = this.getRangeEndPos(curLine);

            if ( pos >= startPos && pos < endPos) {
                paraPos.pos = pos;
                paraPos.line = curLine + this.startLine;
                paraPos.page = 0;
                paraPos.range = this.startRange;
                return ;
            }
        }

        paraPos.pos = pos;
        paraPos.line = lineCount - 1 + this.startLine;
        paraPos.page = 0;
        paraPos.range = this.startRange;
    }

    /**
     * 获取光标位置所在portion内容的位置
     * @param searchPos
     * @param depth
     */
    public getParaContentPosByXY(searchPos: IParagraphSearchPosXY, depth: number = 1, bParaEnd?: boolean): boolean {
        let result = false;
        const curLine = searchPos.line - this.startLine;

        const startPortionContentPos = this.getRangeStartPos(curLine);
        const endPortionContentPos = this.getRangeEndPos(curLine);

        let curPos = startPortionContentPos;
        // console.log(curPos, endPortionContentPos)
        const bHidden = this.isHidden() || this.isComment();
        if (bHidden !== true) {
            for ( ; curPos < endPortionContentPos; curPos++) {
                const item = this.content[curPos];
                if (!item) {
                    searchPos.pos['moveNextPortion'] = false;
                    continue;
                }
                // if ( false === item.bVisible ) {
                //     continue;
                // }

                const bStartBorder = item.isNewControlStartBoder();
                const bEndBorder = bStartBorder ? false : item.isNewControlEndBoder();
                let remainWidth = 0;
                if (bStartBorder) {
                    const width = measure(item.content[0], this.textProperty)[0].width;
                    remainWidth = width < item.widthVisible ? item.widthVisible - width : 0;
                } else if (bEndBorder && 0 < item.getRemainWidth()) {
                    remainWidth = item.getRemainWidth();
                }

                const itemDx = (item.widthVisible); // + item.getRemainWidth());
                let diff = searchPos.x - searchPos.curX;
                // console.log(diff, searchPos)
                const diffStartBorder = (bStartBorder && 0 !== remainWidth && searchPos.curX < searchPos.x
                                        ? remainWidth : 0);
                const searchPosDiffX = searchPos.diffX;

                if ( (Math.abs(diff) < searchPos.diffX + diffStartBorder) ) {
                    const bNoTitle = (0 !== diffStartBorder && 1 === this.content.length
                                    && searchPos.curX < searchPos.x);
                    const moveOneStep = !bNoTitle && (0 < diffStartBorder && searchPos.curX < searchPos.x
                                    && Math.abs(diff) < searchPosDiffX + diffStartBorder) ? 1 : 0;
                    searchPos.diffX = Math.abs(diff);
                    searchPos.pos.update(curPos + moveOneStep, depth);
                    if (bNoTitle) {
                        searchPos.pos['moveNextPortion'] = true;
                    } else {
                        searchPos.pos['moveNextPortion'] = false;
                    }

                    result = true;

                    if ( diff >= -0.001 && diff <= itemDx + 0.001) {
                        searchPos.textPos.update(curPos + moveOneStep, depth);
                        searchPos.bInText = true;
                    }
                }

                searchPos.curX += itemDx;
                diff = searchPos.x - searchPos.curX;
                const diffEndBorder = (bEndBorder && remainWidth ? remainWidth : 0);

                if ( Math.abs(diff) + diffEndBorder < searchPos.diffX ) {
                    if ( ParaElementType.ParaEnd === item.type ) {
                        searchPos.bEnd = true;

                        // if ( true === bParaEnd ) {
                        //     searchPos.diffX = Math.abs(diff);
                        //     searchPos.pos.update(this.content.length, depth);
                        //     result = true;
                        // }
                        result = false;
                    } else if ( curPos === endPortionContentPos - 1) {
                        searchPos.diffX = Math.abs(diff);
                        searchPos.pos.update(endPortionContentPos, depth);
                        result = true;
                    }
                } else if (diffEndBorder) {
                    searchPos.diffX = Math.abs(diff);
                }
            }
        }

        // 光标点在文档左margin？ 1. 空portion
        if ( searchPos.diffX > 100000 - 1) {
            searchPos.diffX = searchPos.x - searchPos.curX;
            searchPos.pos.update(startPortionContentPos, depth);
            result = true;
        }

        return result;
    }

    /**
     * 获取contentPos所在portion内容的位置
     * @param contentPos
     * @param depth
     */
    public getParaPosByContentPos( contentPos: ParagraphContentPos, depth: number ): IParaPos {
        const paraPos = {
      line:  0,
      page:  0,
            pos: 0,
            range: 0,
        };

        const linesCount = this.getLinesCount();

        if ( 0 > this.startLine || 0 >= linesCount ) {
            return paraPos;
        }

        const curPos = contentPos.get(depth);
        paraPos.pos = curPos;
        paraPos.line = this.getLineByPos(paraPos.pos);

        return paraPos;
    }

    /**
     * 光标定位时，记录位置信息
     * @param contentPos
     * @param depth
     */
    public setParaContentPos(contentPos: ParagraphContentPos, depth: number = 1): void {
        let portionContentPos = contentPos.get(depth);

        if ( portionContentPos > this.content.length) {
            portionContentPos = this.content.length;
        }

        if ( portionContentPos < 0) {
            portionContentPos = 0;
        }

        this.portionContentPos = portionContentPos;
    }

    /**
     * 记录portion首字符
     * @param contentPos
     * @param depth
     */
    public getStartPos( contentPos: ParagraphContentPos, depth: number = 1 ): void {
        contentPos.add(0);
        // this.portionContentPos = 0;
    }

    /**
     * 记录portion最后一个字符
     * @param contentPos
     * @param depth
     */
    public getEndPos( contentPos: ParagraphContentPos, depth: number = 1 ): void {
        const length = this.content.length;

        // if ( true === this.isParaEndPortion() ) {
        //     pos--;
        // }

        for (let index = 0; index < length; index++) {
            if ( ParaElementType.ParaEnd === this.content[index].type ) {
                contentPos.add(index);
                return;
            }
        }

        contentPos.add(length);
    }

    /**
     * 将光标移动到段首位置
     * @param bAddToSelect
     */
    public moveCursorToStartPos( bAddToSelect: boolean = false): void {
        // selection
        if ( true === bAddToSelect ) {
            this.selection.bUse = true;

            this.selection.startPos = 0;
            this.selection.endPos = 0;
        } else {
            this.portionContentPos = 0;
        }
    }

    /**
     * 将光标移动到段尾位置
     * @param bAddToSelect
     */
    public moveCursorToEndPos( bAddToSelect: boolean = false ): void {
        // selection
        if ( true === bAddToSelect ) {
            this.selection.bUse = true;
            this.selection.startPos = this.content.length;
            this.selection.endPos = this.content.length;
        } else {

            let pos = this.content.length;

            while ( 0 < pos) {
                if ( ParaElementType.ParaEnd === this.content[pos - 1].type ) {
                    pos--;
                } else {
                    break;
                }
            }

            this.portionContentPos = pos;
        }
    }

    /**
     * 获取当前光标所在portion的内容位置
     * @param bSelection
     * @param bStart
     */
    public getParaContentPos(contentPos: ParagraphContentPos, bSelection?: boolean, bStart?: boolean): void {
        // todo: selection
        let pos = true !== bSelection ? this.portionContentPos :
                        ( false !== bStart ? this.selection.startPos : this.selection.endPos);

        pos = 0 > pos ? 0 : ( this.content.length < pos ? this.content.length : pos );

        contentPos.add(pos);
    }

    /**
     * 获取当前光标所在portion
     * @param contentPos
     */
    public getElementByPos( contentPos?: ParagraphContentPos ): ParaPortion {
        return this;
    }

    /**
     * 获取指定位置所在的range和line
     * @param pos
     */
    public getRangesByPos( pos: number ): IRanges[] {
        const ranges = [];

        for (let index = 0, linesCount = this.getLinesCount(); index < linesCount; index++) {
            const rangesCount = this.getRangesCount(index);

            const startPos = this.getRangeStartPos(rangesCount - 1);
            const endPos = this.getRangeEndPos(rangesCount - 1);

            if ( startPos <= pos && pos <= endPos ) {
                ranges.push({
                    range: ( index === 0 ? this.startRange : rangesCount - 1),
                    line: index + this.startLine,
                });
            }
        }

        return ranges;
    }

    /**
     * 设置输入
     * @param compositeInput
     */
    public setCompositeInput( compositeInput: CompositeInput ): void {
        this.compositeInput = compositeInput;
    }

    /**
     * 获取当前光标所在位置信息：行
     */
    public getCurrentParaPos(): IParaPos {
        const paraPos = {
        line:  -1,
        page:  0,
                pos: -1,
                range: -1,
        };

        if ( -1 === this.startLine ) {
            return paraPos;
        }

        const curPos = this.portionContentPos;
        const linesCount = this.getLinesCount();
        for ( let curLine = 0; curLine < linesCount; curLine++ ) {
            const startPos = this.getRangeStartPos(curLine);
            const endPos = this.getRangeEndPos(curLine);

            if ( curPos >= startPos && curPos < endPos ) {
                paraPos.line = curLine + this.startLine;
                return paraPos;
            }
        }

        paraPos.line = linesCount - 1 + this.startLine;

        return paraPos;
    }

    /**
     * 获取contentPos左边位置信息
     * @param searchPos
     * @param contentPos
     * @param depth
     */
    public getLeftPos( searchPos: IParagraphSearchPos, contentPos: ParagraphContentPos, depth: number = 1 ): void {
        if (this.isHidden() || this.isComment()) {
            return;
        }
        let curPos = ( null == contentPos ? this.content.length : contentPos.get(depth) );

        while ( true ) {
            curPos--;
            const item = this.content[curPos];

            // if ( 0 <= curPos && false === item.bVisible ) {
            //     continue;
            // }

            if ( 0 > curPos || !( (ParaElementType.ParaDrawing === item.type || ParaElementType.ParaBarcode === item.type ||
                            ParaElementType.ParaMedEquation === item.type || ParaElementType.ParaQRCode === item.type)
                            && true !== item.isInline() ) ) {
                break;
            }
        }

        if ( 0 <= curPos ) {
            searchPos.bFound = true;
            searchPos.pos.update(curPos, depth);
        }
    }

    /**
     * 获取contentPos右边位置信息
     * @param searchPos
     * @param contentPos
     * @param depth
     */
    public getRightPos( searchPos: IParagraphSearchPos, contentPos: ParagraphContentPos, depth: number = 1 ): void {
        if (this.isHidden() || this.isComment()) {
            return;
        }
        let curPos = ( null == contentPos ? 0 : contentPos.get(depth) );
        const nCount = this.content.length;

        while ( true ) {
            curPos++;

            if ( nCount === curPos ) {
                if ( 0 === curPos ) {
                    return;
                }

                const prevElement = this.content[curPos - 1];

                if ( ParaElementType.ParaEnd === prevElement.type ) {
                    return;
                }

                // if ( false === prevElement.bVisible) {
                //     continue;
                // }

                break;
            }

            if ( nCount < curPos ) {
                break;
            }

            const prevItem = this.content[curPos - 1];

            // if ( false === prevItem.bVisible ) {
            //     continue;
            // }

            if ( ParaElementType.ParaEnd !== prevItem.type ) {
                break;
            }
        }

        if ( curPos <= nCount ) {
            searchPos.bFound = true;
            searchPos.pos.update(curPos, depth);
        }
    }

    /**
     * 设置文本属性: 字体
     * @param prop
     */
    public setTextFontFamily( prop: string ): boolean {
        if ( prop !== this.textProperty.fontFamily ) {
            const old = this.textProperty.fontFamily;
            this.textProperty.fontFamily = prop;

            const history = this.getHistory();
            if ( history ) {
                history.addChange(new ChangePortionFont(this, old, prop, null));
            }

            this.recalcInfo.bMeasure = true;
            this.recalcInfo.bRecalc = true;
            return;
        }
    }

    public getTextFontFamily(): string {
        return this.textProperty.fontFamily;
    }

    /**
     * 设置文本属性: 字号
     * @param fontSize
     */
    public setTextFontSize( fontSize: number ): boolean {
        if (fontSize !== undefined && fontSize !== this.textProperty.fontSize ) {
            const old = this.textProperty.fontSize;
            this.textProperty.fontSize = fontSize;

            const history = this.getHistory();
            if ( history ) {
                history.addChange(new ChangePortionFontSize(this, old, fontSize, null));
            }
            this.recalcInfo.bMeasure = true;
            this.recalcInfo.bRecalc = true;
            return true;
        }
    }

    public getTextFontSize(): number {
        return this.textProperty.fontSize;
    }

    /**
     * 设置文本粗体
     * @param fontWeight
     */
    public setTextBold( fontWeight: FontWeightType ): boolean {
        if ( fontWeight !== this.textProperty.fontWeight ) {
            const old = this.textProperty.fontWeight;
            this.textProperty.fontWeight = fontWeight;

            const history = this.getHistory();
            if ( history ) {
                history.addChange(new ChangePortionBold(this, old, fontWeight, null));
            }
            // this.recalcInfo.bMeasure = true;
            // this.recalcInfo.bRecalc = true;
            return true;
        }
    }

    public getTextBold(): boolean {
        return 1 === this.textProperty.fontWeight;
    }

    public setTextVertAlign(vertAlign: TextVertAlign): boolean {
        if ( vertAlign !== this.textProperty.vertAlign && null != vertAlign ) {

            const history = this.getHistory();
            if ( history ) {
                history.addChange(new ChangePortionVertAlign(this, this.textProperty.vertAlign, vertAlign));
            }

            this.textProperty.vertAlign = vertAlign;

            this.recalcInfo.bMeasure = true;
            this.recalcInfo.bRecalc = true;
            return true;
        }

        return false;
    }

    /**
     * 设置文本颜色
     * @param setTextColor
     */
    public setTextColor( color: string): boolean {
        if ( color !== this.textProperty.color ) {
            const old = this.textProperty.color;
            this.textProperty.color = color;

            const history = this.getHistory();
            if ( history ) {
                history.addChange(new ChangePortionColor(this, old, color, null));
            }
            // this.recalcInfo.bMeasure = true;
            // this.recalcInfo.bRecalc = true;
            return true;
        }
    }

    public getTextColor(): string {
        return this.textProperty.color;
    }

    /**
     * 设置文本背景颜色
     * @param setTextColor
     */
    public setTextBackgroundColor( backgroundColor: string): boolean {
        if ( backgroundColor !== this.textProperty.backgroundColor ) {
            const old = this.textProperty.backgroundColor;
            this.textProperty.backgroundColor = backgroundColor;

            const history = this.getHistory();
            if ( history ) {
                history.addChange(new ChangePortionBackgroundColor(this, old, backgroundColor, null));
            }
            // this.recalcInfo.bMeasure = true;
            // this.recalcInfo.bRecalc = true;
            return true;
        }
    }

    public getTextBackgroundColor(): string {
        return this.textProperty.backgroundColor;
    }

    /**
     * 设置文本斜体
     * @param setTextItalic
     */
    public setTextItalic( fontStyle: number): boolean {
        if ( fontStyle !== this.textProperty.fontStyle ) {
            const old = this.textProperty.fontStyle;
            this.textProperty.fontStyle = fontStyle;

            const history = this.getHistory();
            if ( history ) {
                history.addChange(new ChangePortionItalic(this, old, fontStyle, null));
            }
            // this.recalcInfo.bMeasure = true;
            // this.recalcInfo.bRecalc = true;
            return true;
        }
    }

    public getTextItalic(): boolean {
        return 1 === this.textProperty.fontStyle;
    }

    /**
     * 设置文本划线
     * @param setTextDecorationLine
     */
    public setTextDecorationLine( textDecorationLine: number): boolean {
        if (textDecorationLine !== undefined && textDecorationLine !== this.textProperty.textDecorationLine ) {
            const old = this.textProperty.textDecorationLine;
            this.textProperty.textDecorationLine = textDecorationLine;

            const history = this.getHistory();
            if ( history ) {
                history.addChange(new ChangePortionDecorationLine(this, old, textDecorationLine, null));
            }
            // this.recalcInfo.bMeasure = true;
            // this.recalcInfo.bRecalc = true;
            return true;
        }
    }

    public getTextDecorationLine(): number {
        return this.textProperty.textDecorationLine;
    }

    /**
     * 获取下标状态
     */
    // public getTextSubscript(): boolean {
    //     return this.textProperty.subscript;
    // }

    /**
     * 获取上下标状态
     */
    public getTextVertAlign(): number {
        return this.textProperty.vertAlign;
    }

    /**
     * 设置文本属性
     * @param textProperty
     */
    public setTextProperty( textProperty: TextProperty ): boolean {
        const old = this.textProperty;
        this.textProperty = textProperty;

        const history = this.getHistory();
        if ( history ) {
            history.addChange(new ChangePortionTextProperty(this, old, textProperty, null));
        }
        this.recalcInfo.bMeasure = true;
        this.recalcInfo.bRecalc = true;

        this.updateTrackRevisionOnChangeTextPr(true);
        return true;
    }

    /**
     * 应用光标处的文本属性
     * @param textProperty
     */
    public applyTextPr( textProperty: TextProperty, bApplyToAll: boolean = false,
                        option?: {result: any, bContainParaEnd?: boolean}): ParaPortion[] {
        let bReview = false;
        const reviewType = this.getReviewType();
        const bPropertyChange = this.hasPropertyChange();

        if ( this.isTrackRevisions() ) {
            bReview = true;
        }

        if (this.isPortionStyle()) {
            if ( true === bReview && true !== this.hasPropertyChange() ) {
                this.addPropertyChange();
            }
            this.applyProperty(textProperty);
            return;
        // } else if (this.isCheckBox()) {
        //     if ( true === bReview && true !== this.hasPropertyChange() ) {
        //         this.addPropertyChange();
        //     }

        //     this.setTextFontSize(textProperty.fontSize);
        //     this.setTextDecorationLine(textProperty.textDecorationLine);
        //     return;
        } else if (true === bApplyToAll) {
            // todo
            if (this.content.length === 1 && this.content[0].type === ParaElementType.ParaEnd
                && true !== option.bContainParaEnd ) {
                if (option) {
                    option.result = ResultType.UnEdited;
                }
                return;
            }

            if ( true === bReview && true !== this.hasPropertyChange() ) {
                this.addPropertyChange();
            }

            const num = this.applyProperty(textProperty);
            if (option) {
                option.result = num && option.result;
            }
        } else {
            const result: ParaPortion[] = [];
            let leftPortion: ParaPortion = this;
            let centerPortion: ParaPortion = null;
            let rightPortion: ParaPortion = null;

            // const bPropertyChange = this.hasPropertyChange();
            const length = this.content.length;

            if ( true === this.selection.bUse ) {
                let startPos = this.selection.startPos;
                let endPos = this.selection.endPos;

                if ( startPos === endPos && 0 !== this.content.length ) {
                    leftPortion = null;
                    rightPortion = null;
                    centerPortion = this;

                    // 段落结束符单独处理
                    // if ( true === this.isSingleParaEndPortion() )
                    //     centerPortion.applyProperty(textProperty);
                } else {
                    let direction = 1; // 1: 向下选择，-1：向上选择
                    if ( startPos > endPos ) {
                        const temp = endPos;
                        endPos = startPos;
                        startPos = temp;
                        direction = -1;
                    }

                    // if ( true === this.bPlaceHolder ) {
                    //     if ( ( 0 === startPos && 1 === endPos )
                    //         || ( this.content.length - 1 === startPos && this.content.length === endPos ) ) {
                    //         return [null, this, null];
                    //     } else {
                    //         this.applyProperty(textProperty);
                    //         return [null, this, null];
                    //     }
                    // }

                    if ( this.isNewControlStart() && 1 < length || this.isRegionTitle(false)) {
                        startPos = 0;
                        endPos = length;
                    }

                    if ( endPos < this.content.length ) {
                        rightPortion = leftPortion.splitPortion(endPos);
                        rightPortion.setReviewType(reviewType);

                        if ( bPropertyChange ) {
                            rightPortion.addPropertyChange();
                        }
                    }

                    if ( 0 < startPos ) {
                        centerPortion = leftPortion.splitPortion(startPos);
                        centerPortion.setReviewType(reviewType);

                        if ( bPropertyChange ) {
                            centerPortion.addPropertyChange();
                        }
                    } else {
                        centerPortion = leftPortion;
                        leftPortion = null;
                    }

                    if ( null !== leftPortion ) {
                        leftPortion.selection.bUse = true;
                        leftPortion.selection.startPos = leftPortion.content.length;
                        leftPortion.selection.endPos = leftPortion.content.length;
                        leftPortion.recalcInfo.bRecalc = true;
                    }

                    centerPortion.selectAll(direction);

                    if ( true === bReview && true !== centerPortion.hasPropertyChange() ) {
                        centerPortion.addPropertyChange();
                        centerPortion.setReviewType(ReviewType.Add);
                    }

                    // centerPortion.textProperty.setProperty(textProperty);
                    const num = centerPortion.applyProperty(textProperty);
                    if (option) {
                        option.result = num && option.result;
                    }

                    if ( null !== rightPortion ) {
                        rightPortion.selection.bUse = true;
                        rightPortion.selection.startPos = 0;
                        rightPortion.selection.endPos = 0;
                        rightPortion.recalcInfo.bRecalc = true;
                    }

                    // if ( true === this.isSelectionParaEnd() && rightPortion.isParaEndPortion() ) {
                    //     rightPortion.applyProperty(textProperty);
                    // }
                }
            } else {

                // 段落结束符单独处理
                // if ( true === this.isSingleParaEndPortion() ) {
                //     this.applyProperty(textProperty);
                //     return null;
                // }

                const curPos = this.portionContentPos;

                // 光标不在portion内容末尾
                if ( curPos < this.content.length ) {
                    rightPortion = leftPortion.splitPortion(curPos);
                    rightPortion.setReviewType(reviewType);

                    if ( bPropertyChange ) {
                        rightPortion.addPropertyChange();
                    }
                }

                // 光标是否在portion内容开始位置
                if ( 0 < curPos ) {
                    // centerPortion是内容为空的portion
                    centerPortion = leftPortion.splitPortion(curPos);
                    centerPortion.setReviewType(reviewType);

                    if ( bPropertyChange ) {
                        centerPortion.addPropertyChange();
                    }
                } else {
                    // 光标在portion开始位置：curPos = 0，
                    // this：centerPortion是内容为空的portion，右边portion为原始portion，左边就不需要插入portion
                    centerPortion = leftPortion;
                    leftPortion = null;
                }

                if ( null !== leftPortion ) {
                    leftPortion.removeSelection();
                    leftPortion.recalcInfo.bRecalc = true;
                }

                if ( true !== centerPortion.hasPropertyChange() ) {
                    centerPortion.addPropertyChange();
                }

                // centerPortion.textProperty.setProperty(textProperty);
                const num = centerPortion.applyProperty(textProperty);
                if (option) {
                    option.result = num && option.result;
                }
                centerPortion.removeSelection();
                // let text = new ParaText(String.fromCharCode(0xFEFF));
                // centerPortion.addToContent(centerPortion.content.length, text, true);
                centerPortion.moveCursorToStartPos();

                if ( null !== rightPortion ) {
                    rightPortion.removeSelection();
                    rightPortion.recalcInfo.bRecalc = true;
                }
            }

            result.push(leftPortion);
            result.push(centerPortion);
            result.push(rightPortion);

            return result;
        }
    }

    /**
     * 设置当前光标处文本属性
     * @param textPr
     */
    public applyProperty( textPr: TextProperty ): number {
        let flag = false;
        if ( undefined !== textPr.fontFamily ) {
            flag = this.setTextFontFamily(textPr.fontFamily) || flag;
        }

        if (undefined !== textPr.fontSize) {
            flag = this.setTextFontSize(textPr.fontSize) || flag;
        }

        if (undefined !== textPr.fontWeight) {
            flag = this.setTextBold(textPr.fontWeight) || flag;
        }

        if (undefined !== textPr.vertAlign) {
            flag = this.setTextVertAlign(textPr.vertAlign) || flag;
        }



        if (undefined !== textPr.textDecorationLine) {
            const decorationResult = this.setTextDecorationLine(textPr.textDecorationLine);
            flag = decorationResult || flag;
        }
        // if (undefined !== textPr.superscript) {
        //     this.setTextSuperscript(textPr.superscript);
        // }

        // if (undefined !== textPr.subscript) {
        //     this.setTextSubscript(textPr.subscript);
        // }

        if (undefined !== textPr.color) {
            flag = this.setTextColor(textPr.color) || flag;
        }

        if (undefined !== textPr.fontStyle) {
            flag = this.setTextItalic(textPr.fontStyle) || flag;
        }

        if (undefined !== textPr.textDecorationLine) {
            flag = this.setTextDecorationLine(textPr.textDecorationLine) || flag;
        }

        if (undefined !== textPr.backgroundColor) {
            flag = this.setTextBackgroundColor(textPr.backgroundColor) || flag;
        }

        if (flag !== true) {
            return ResultType.UnEdited;
        }

        return ResultType.Success;
    }

    /**
     * 选中portion所有内容，direction为选择方向
     * direction： 1 ---》向下选择，-1 ---》向上选择，
     * @param direction
     */
    public selectAll( direction: number ): void {
        const selection = this.selection;
        selection.bUse = true;

        if ( 1 === direction ) {
            selection.startPos = 0;
            selection.endPos = this.content.length;
        } else {
            selection.startPos = this.content.length;
            selection.endPos = 0;
        }
    }

    /**
     * 删除portion的选择信息
     */
    public removeSelection(): void {
        this.selection.bUse      = false;
        this.selection.startPos = 0;
        this.selection.endPos   = 0;
    }

    /**
     * 设置选择区域
     * @param startContentPos 开始位置
     * @param endContentPos 结束位置
     * @param depth
     * @param startDirection 确定开始位置：1：portion开始位置； -1：portion结束位置 ； 0： portion当前内容的位置
     * @param endDirection 确定结束位置
     */
    public setSelectionContentPos(startContentPos: ParagraphContentPos,  endContentPos: ParagraphContentPos,
                                  depth: number, startDirection: number, endDirection: number ): void {
        let startPos = 0;
        let endPos = 0;
        const pos = this.content.length;

        const isParaEnd = ( true === this.isParaEndPortion() );

        switch ( startDirection ) {
            case 1:
                startPos = 0;
                break;

            case -1:
                startPos = ( isParaEnd ? pos - 1 : pos );
                break;

            case 0:
                startPos = startContentPos.get(depth);
                break;
        }

        switch ( endDirection ) {
            case 1:
                endPos = 0;
                break;

            case -1:
                endPos = ( isParaEnd ? pos - 1 : pos );
                break;

            case 0:
                endPos = endContentPos.get(depth);
                break;
        }

        this.selection.startPos = startPos;
        this.selection.endPos = endPos;
        this.selection.bUse = true;
    }

    public selectAreaByPos(startPos: ParagraphContentPos, endPos: ParagraphContentPos, dir?: number): boolean {
        const startIndex = startPos.shift();
        let endIndex = endPos.shift();
        if  (startIndex === undefined || endIndex === undefined) {
            return false;
        }
        if (startIndex > endIndex) {
            endIndex = startIndex;
        }
        const selection = this.selection;
        selection.bUse = true;
        selection.startPos = startIndex;
        selection.endPos = endIndex;
        this.portionContentPos = endIndex;
        return true;
    }

    /**
     * 通过portion的内容索引获取绝对行号
     * @param pos
     */
    public getLineByPos(pos: number, direction?: number): number {
        for ( let curLine = 0, linesCount = this.getLinesCount(); curLine < linesCount; curLine++ ) {
            if (pos >= this.getRangeStartPos(curLine) && pos < this.getRangeEndPos(curLine)) {
                return curLine + this.startLine;
            }
        }

        if ( pos === this.content.length ) {
            return this.getLinesCount() - 1 + this.startLine;
        } else if ( true === this.isSingleParaEndPortion() ) {
            return this.getLinesCount() - 1;
        }

        return this.startLine;
    }

    public getSelectionBounds( index: number, drawSelelction: any, direction: number ): any {
        const curLine = index - this.startLine;

        const startPos = this.getRangeStartPos(curLine);
        const endPos = this.getRangeEndPos(curLine);

        let selectionStartPos = this.selection.startPos;
        let selectionEndPos = this.selection.endPos;
        if ( selectionStartPos > selectionEndPos ) {
            selectionStartPos = this.selection.endPos;
            selectionEndPos = this.selection.startPos;
        }

        let bFindStart = drawSelelction.bFindStart;
        if (this.isHidden()) {
            return;
        }

        for (let curPos = startPos; curPos < endPos; curPos++) {
            const item = this.content[curPos];
            if (!item) {
                continue;
            }
            // if ( false === item.bVisible ) {
            //     continue;
            // }

            let bDrawSelelction = false;

            if ( true === bFindStart ) {
                if ( curPos >= selectionStartPos && curPos < selectionEndPos ) {
                    bFindStart = false;
                    bDrawSelelction = true;
                } else {
                    drawSelelction.startPos += item.widthVisible; // = item.positionX;//+= item.widthVisible;
                }
            } else {
                if ( curPos >= selectionStartPos && curPos < selectionEndPos ) {
                    bDrawSelelction = true;
                }
            }

            if ( true === bDrawSelelction ) {
                drawSelelction.width += item.widthVisible;
            }

        }

        drawSelelction.bFindStart = bFindStart;
    }

    /**
     * 是否存在选择区域
     * @param bContainParaEnd 是否包含段落结束符
     */
    public isSelectionEmpty( bContainParaEnd: boolean ): boolean {
        if ( true !== this.selection.bUse ) {
            return true;
        }

        if ( 0 === this.content.length ) {
            return true;
        }

        let startPos = this.selection.startPos;
        let endPos = this.selection.endPos;

        if ( startPos > endPos ) {
        startPos = this.selection.endPos;
        endPos = this.selection.startPos;
      }

        // 选中段落结束符
        if ( true === bContainParaEnd ) {
            return ( endPos > startPos ? false : true );
        }

        for ( let curPos = startPos; curPos < endPos; curPos++ ) {
        if ( ParaElementType.ParaEnd !== this.content[curPos].type ) {
          return false;
        }
      }

        return true;
    }

    /**
     * 当前portion是否被全选
     * @param bSkipParaEnd
     */
    public isSelectedAll(bSkipParaEnd: boolean = true): boolean {
        if (true !== this.selection.bUse && true !== this.isEmpty(bSkipParaEnd)) {
            return false;
        }

        let startPos = this.selection.startPos;
        let endPos = this.selection.endPos;
        if (startPos > endPos) {
            startPos = this.selection.endPos;
            endPos = this.selection.startPos;
        }

        for (let pos = 0; pos < startPos; pos++) {
            const item = this.content[pos];

            if (!(ParaElementType.ParaEnd === item.type && bSkipParaEnd)) {
                return false;
            }
        }

        for (let pos = endPos, count = this.content.length; pos < count; pos++) {
            const item = this.content[pos];

            if (!(ParaElementType.ParaEnd === item.type && bSkipParaEnd)) {
                return false;
            }
        }

        return true;
    }

    /**
     * 选中区域是否包含段尾
     * @param bContainParaEnd
     */
    public isSelectionParaEnd( bContainParaEnd: boolean ): boolean {
        // 当前portion为空时
        if ( true !== this.selection.bUse && 0 !== this.content.length && true !== this.isSingleParaEndPortion() ) {
            return false;
        }

        let startPos = this.selection.startPos;
        let endPos = this.selection.endPos;

        if ( startPos > endPos ) {
            startPos = this.selection.endPos;
            endPos = this.selection.startPos;
        }

        for (const curPos = startPos; curPos <= endPos; startPos++) {
            const item = this.content[startPos];

            if ( ParaElementType.ParaEnd === item.type ) {
                return true;
            }
        }

        return false;
    }

    /**
     * 当前pos是否为portion最后一个字符
     * @param bSkipParaEnd
     */
    public isEndPos(pos: number, bSkipParaEnd: boolean = true): boolean {
        if (true === this.isEmpty(bSkipParaEnd)) {
            return true;
        }

        const length = this.content.length;
        if ( pos >= length - 1 ||
            (pos === length - 2 && ParaElementType.ParaEnd === this.content[length - 1].type) ) {
            return true;
        }

        return false;
    }

    /**
     * 在当前光标位置开始选择
     */
    public startSelectionByCurPos(): void {
        this.selection.bUse = true;

        this.selection.startPos = this.portionContentPos;
        this.selection.endPos = this.portionContentPos;
    }

    /**
     * 判断当前选择区域的方向
     */
    public getSelectionDirection(): number {
        if ( true !== this.selection.bUse ) {
            return 0;
        }

        return ( this.selection.startPos < this.selection.endPos ?
                1 : ( this.selection.startPos > this.selection.endPos ? -1 : 0 ) );
    }

    /**
     * 对portion进行简单的内容增删改变，获取改变发生的所在行
     * @param changes 改变的信息
     */
    public getParaPosBySimpleChanges( changes: any[] ): IParaPos {
        const change = changes[0];
        const type = change.type;
        const pos = change.position;

        const linesCount = this.getLinesCount();

        const paraPos = {
            line: this.startLine,
            page: 0,
            pos: 0,
            range: this.startRange,
        };

        let curLine = 0;
        for (curLine = 0; curLine < linesCount; curLine++) {
            const startPos = this.getRangeStartPos(curLine);
            const endPos = this.getRangeEndPos(curLine);

            if ( ( HistroyItemType.ParaPortionAddItem === type && pos >= startPos && pos < endPos )
                 || ( HistroyItemType.ParaPortionRemoveItem === type && pos >= startPos && pos < endPos )
                 || ( HistroyItemType.ParaPortionRemoveItem === type &&
                        pos >= endPos && linesCount - 1 === curLine ) ) {

                // 如果段落为空，则必须重新计算所有内容
                if ( startPos === endPos ) {
                    return null;
                }

                paraPos.line = curLine + this.startLine;

                return paraPos;
            }
        }

        // 如果段落为空，则必须重新计算所有内容
        if ( this.getRangeStartPos(0) === this.getRangeEndPos(0) ) {
            return null;
        }

        paraPos.line = curLine + this.startLine;
        return paraPos;
    }

    /**
     * 检查是否发生了简单的更改（输入或删除文本）
     * @param changes
     */
    public getSimpleChangePos(changes: any[]): number {
        let paraPos = null;
        const count = changes.length;

        for (let index = 0; index < count; index++) {
            const changeContent = changes[index];

            if ( !changeContent.items || 1 !== changeContent.items.length ) {
                return null;
            }

            const changeType = changeContent.type;
            const item = changeContent.items[0];

            if (!item) {
                return null;
            }

            if ( HistroyItemType.ParaPortionAddItem !== changeType
                && HistroyItemType.ParaPortionRemoveItem !== changeType ) {
                return null;
            }

            const itemType = item.type;
            if ( ParaElementType.ParaDrawing === itemType || ParaElementType.ParaMedEquation === itemType
                || ParaElementType.ParaPageNum === itemType || ParaElementType.ParaBarcode === itemType
                || ParaElementType.ParaNewLine === itemType || ParaElementType.ParaQRCode === itemType
                || ParaElementType.ParaButton === itemType) {
                return null;
            }

            const portion = changeContent.changeClass as ParaPortion;
            if ( portion && portion.paragraph && 1 === portion.getLinesCount()
                && changes.length === portion.content.length) {
                const linePos = portion.getLineByPos(0);
                const textHeight = portion.measure(item);
                if ( textHeight > portion.paragraph.lines[linePos].metrics.textHeight ) {
                    return null;
                }
            }

            const curParaPos = this.getParaPosBySimpleChanges([changes[index]]);

            if ( !curParaPos ) {
                return null;
            }

            if ( !paraPos ) {
                paraPos = curParaPos;
            } else if ( paraPos.line !== curParaPos.line ) {
                return null;
            }
        }

        return paraPos;
    }

    /**
     * 检查是否发生了简单的段落更改，不应添加/删除图片或链接到复杂字段的脚注或标记。
     * @param changes
     */
    public isParaSimpleChanges( changes: ChangeContent[] ): boolean {
        const count = changes.length;

        for (let index = 0; index < count; index++) {
            const element = changes[index].data;
            const changeType = element.type;

            if ( HistroyItemType.ParaPortionAddItem === changeType
                || HistroyItemType.ParaPortionRemoveItem !== changeType ) {
                for (let itemIndex = 0, itemCount = element.items.length; itemIndex < itemCount; itemIndex++) {
                    const item = element.items[itemIndex];

                    if ( ParaElementType.ParaDrawing === item.type || ParaElementType.ParaMedEquation === item.type
                        || ParaElementType.ParaBarcode === item.type || ParaElementType.ParaQRCode === item.type) {
                        return false;
                    }
                }
            }
        }

        return true;
    }

    /**
     * 检查两个HistoryPoint能否合并
     * @param data1
     * @param data2
     */
    public checkUnionHistory( data1: ChangeBaseContent, data2: ChangeBaseContent ): boolean {
        const type1 = data1.type;
        const type2 = data2.type;

        if ( HistroyItemType.DocumentAddItem === type1 && HistroyItemType.DocumentAddItem === type2 ) {
            if ( 1 === data1.items.length && 1 === data2.items.length && data1.position === data2.position - 1
                && ParaElementType.ParaText === data1.items[0].type
                && ParaElementType.ParaText === data2.items[0].type ) {
                return true;
            }
        }
        return false;
    }

    public refreshRecalData(data?: any): void {
        const para = this.paragraph;

        if ( -1 !== this.startLine && para ) {
            let curLine = this.startLine;

            if (data instanceof ChangePortionAddItem || data instanceof ChangePortionRemoveItem) {
                curLine = -1;
                const pos = data.getMinPos();

                for (let line = 0, count = this.getLinesCount(); line < count; line++) {
                    const startPos = this.getRangeStartPos(curLine);
                    const endPos = this.getRangeEndPos(curLine);

                    if ( pos >= startPos && pos < endPos) {
                        curLine = line + this.startLine;
                        break;
                    }

                    if (-1 !== curLine) {
                        break;
                    }
                }

                if (-1 === curLine) {
                    curLine = this.startLine + this.getLinesCount() - 1;
                }
            }

            for (let index = 0, count = para.pages.length; index < count; index++) {
                const page = para.pages[index];
                if ( page.startLine <= this.startLine && page.endLine >= this.startLine ) {
                    para.refreshRecalData2(index);
                    return;
                }
            }

            para.refreshRecalData2(0);
        }
    }

    public refreshRecalData2(): void {
        this.refreshRecalData();
    }

    public saveRecalculateObject( bCopy: boolean = false ): PortionRecalculateObject {
        const obj = new PortionRecalculateObject(this.startLine, this.startRange);
        obj.saveLines(this, bCopy);
        obj.savePortionContent(this, bCopy);

        return obj;
    }

    public loadRecalculateObject( obj: PortionRecalculateObject ): void {
        obj.loadLines(this);
        obj.loadPortionContent(this);
    }

    public addContentChanges( changes: ContentChangesElement ): void {
        this.contentChanges.add(changes);
    }

    public refreshContentChanges(): void {
        this.contentChanges.refresh();
    }

    /**
     * 属性发生变化
     */
    public addPropertyChange(): void {
        if ( false === this.hasPropertyChange() ) {
            this.textProperty.addPropertyChange();

            const history = this.getHistory();

            if ( history) {
                history.addChange(new ChangePortionPropertyChange(this,
                    { PrChange: undefined,
                      reviewInfo: undefined },
                    { PrChange: this.textProperty.propertyChange,
                      reviewInfo: this.textProperty.reviewInfo }));
            }

            this.updateTrackRevisions();
        }
    }

    /**
     * 设置portion内容属性
     * @param change
     */
    public setPropertyChange( change: TextProperty, reviewInfo: ReviewInfo ): void {
        const history = this.getHistory();

        if ( history) {
            history.addChange(new ChangePortionPropertyChange(this,
                { PrChange: this.textProperty.propertyChange,
                  reviewInfo: this.textProperty.reviewInfo ? this.textProperty.reviewInfo.copy() : undefined},
                { PrChange: change,
                  reviewInfo: reviewInfo ? reviewInfo.copy() : undefined}));
        }

        this.textProperty.setPropertyChange(change, reviewInfo);
        this.updateTrackRevisions();
    }

    /**
     * 是否有portion内容属性发生改变
     */
    public hasPropertyChange(): boolean {
        return this.textProperty.hasPropertyChange();
    }

    public copy( para: Paragraph, bShowParaEnd: boolean = true, bForUI: boolean = false, option?: any): ParaPortion {
        // 根据需求，修订删除的内容不能进行粘贴
        const bFromCopy  = option && option.bCopy;
        if (bFromCopy === true && this.reviewType === ReviewType.Remove) {
            return null;
        }
        const newPortion = new ParaPortion(para);
        
        // 清除新portion的波浪线属性
        clearWavyUnderlineFromTextProperty(newPortion.textProperty);

        const logicDocument = this.paragraph.getDocument();
        let buttonManager;
        // const bHidden = this.isHidden();
        for (let index = 0, count = this.content.length; index < count; index++) {
            const element = this.content[index];

            // 不显示段落结束符
            if (false === bShowParaEnd &&
                (ParaElementType.ParaEnd === element.type) // || ParaElementType.ParaNewLine === element.type)
            ) {
                continue;
            } else if (option?.bPrint && element.isButton() && (element as any).bPrint !== true) {
                continue
            }

            // if ( true === bHidden ) {
            //     continue;
            // }
            // if (element.type === ParaElementType.ParaPageNum) {
                // console.trace() // from ui - getPara()
                // (element as ParaPageNum).setParent(this);
            // }

            // 新增行，会附带结构化元素等进行复制
            if (option?.create && element.isNewControlStartBoder()) {
                const newControl = option.create(element.getNewControlName(), para);
                if (newControl) {
                    const startPorion: ParaPortion = newControl.getStartBorderPortion();
                    const actItem = startPorion.content[0];
                    actItem.bVisible = element.bVisible;
                    actItem.content = element.content;
                    startPorion.setTextProperty(this.textProperty.copy());
                    startPorion.bHidden = this.bHidden;
                    option.bPlaceHolder = newControl.isPlaceHolderContent();
                    return startPorion;
                }
            }

            if (option?.structs && element.isNewControlEndBoder()) {
                const name = option.structNames.splice(option.structNames.length - 1, 1);
                const newControl = option.structs[name];
                if (newControl) {
                    const actPortion: ParaPortion = newControl.getEndBorderPortion();
                    const actItem = actPortion.content[0];
                    actItem.bVisible = element.bVisible;
                    actItem.content = element.content;
                    actPortion.setProperty(this.textProperty.copy());
                    actPortion.bHidden = this.bHidden;
                    return actPortion;
                }
            }

            const newElem = element.copy(bForUI);
            if (element['_name']) {
                newElem['_name'] = element['_name'];
            }

            if (bForUI && !bShowParaEnd && newElem.isParaSoftLine()) {
                newElem.content = '';
            }

            if (element.isButton() && option?.create) {
                const button: ParaButton = newElem as any;
                if (!buttonManager) {
                    buttonManager = logicDocument.getButtonManager();
                }
                button.name = buttonManager.makeUniqueName(button.name);
                buttonManager.setButton(button);
            }

            newPortion.addToContent(newPortion.content.length, newElem, false, bForUI);
            if (option?.imgs && element.isImage()) {
                option.imgs[newElem['name']] = {old: element, new: newElem};
            }
        }
        newPortion.type = this.type;
        newPortion.bPlaceHolder = this.bPlaceHolder;
        newPortion.positionX = this.positionX;
        newPortion.positionY = this.positionY;
        newPortion.textHeight = this.textHeight;
        newPortion.bHidden = this.isHidden();
        newPortion.setProperty(this.textProperty.copy());

         // 清除新portion的波浪线属性
         clearWavyUnderlineFromTextProperty(newPortion.textProperty);

        newPortion.startLine = this.startLine;
        newPortion.lines = this.lines.slice(0);

        const parent = para.getParent();
        if (parent != null) {
            if (logicDocument != null &&
                (logicDocument.bRecalcTableHeader || logicDocument.getSelectedContentFlag() === true)) {
                newPortion.reviewType = this.reviewType;
                newPortion.reviewInfo = this.reviewInfo.copy();
            }
        }
        if (option && option.bRevision === true && option.cleanMode === CleanModeType.CleanStruct) {
            this.bHidden = this.isHidden();
        } else if (option && option.bRevision === true) {
            newPortion.reviewType = this.reviewType;
            newPortion.reviewInfo = this.reviewInfo.copy();
        }
        // 打印专用
        if (this['bTextBorder'] === true) {
            newPortion['bTextBorder'] = true;
        }
        // 选项文本色
        newPortion.checkedTextColor = this.checkedTextColor;

        return newPortion;
    }

    public preDelete(): void { //
        this.content.forEach((item) => {
            if (item.type === ParaElementType.ParaButton) {
                this.paragraph.getDocument()
                .getButtonManager()
                .deleteButton(item as any);
            }
        });
    }

    public recalculateMinmaxContentWidth(bRotated: boolean): void {
        //
    }

    public shift(shiftDx: number, shiftDy: number, lineIndex: number, rangeIndex: number = 0): void {
        const curLine = lineIndex - this.startLine;
        const curRange = ( 0 === curLine ? rangeIndex - this.startRange : rangeIndex );

        const startPos = this.getRangeStartPos(curLine, curRange);
        const endPos = this.getRangeEndPos(curLine, curRange);

        for (let index = startPos; index < endPos; index++) {
            const element = this.content[index];

            if ( ParaElementType.ParaDrawing === element.type ) {
                element.shift(shiftDx, shiftDy);
            }
        }
    }

    public getFlagByStr(text1: string, text2?: string): number {
        const contents = this.content;
        if (contents.length !== 1) {
            return 0;
        }

        const text = contents[0].content;
        if (text === text1) {
            return 1;
        }
        if (text2 === text) {
            return 2;
        }
    }

    public selectTopPos(): void {
        const parent = this.paragraph;
        const selection = parent.selection;
        selection.bUse = true;
        selection.bStart = false;
        const index = parent.content.findIndex((item) => item === this);
        parent.curPos.contentPos = selection.endPos = selection.startPos = index;
        parent.selectTopPos();
    }

    public setContentCurPos(): void {
        const parent = this.paragraph;
        const index = parent.content.findIndex((item) => item === this);
        parent.curPos.contentPos = index;
        parent.setContentCurPos();
    }

    /**
     * 获取portion的文本内容
     */
    public getTextContent(): string {
        let text = '';

        if ( true !== this.isEmpty(false) ) {
            const length = this.content.length;
            for (let index = 0; index < length; index++) {
                const element = this.content[index];
                if ( ParaElementType.ParaText === element.type ||
                    ParaElementType.ParaSpace ) {
                    text += element.content;
                }
            }
        }

        return text;
    }

    /**
     * 初始化进行赋值
     * @param bPlaceHolder 状态true或false
     */
    public setPlaceHolder1(bPlaceHolder: boolean): void {
        this.bPlaceHolder = bPlaceHolder;
    }

    public setPlaceHolder(bPlaceHolder: boolean): void {
        if ( bPlaceHolder === this.bPlaceHolder ) {
            return ;
        }

        const history = this.getHistory();
        if ( history) {
            history.addChange(new ChangeNewControlPlaceHolder(this, this.bPlaceHolder, bPlaceHolder));
        }
        this.bPlaceHolder = bPlaceHolder;

        // if (bPlaceHolder) {
        //     this.textProperty.color = NewControlDefaultSetting.DefaultTextColor;
        // } else {
        //     this.textProperty.color = NewControlDefaultSetting.InputContentColor;
        // }
    }

    public isPlaceHolder(): boolean {
        return this.bPlaceHolder;
    }

    /**
     * 输入内容替换占位符
     * @param item
     */
    public addContentReplacePlaceHolder(item?: ParaElementBase): void {
        this.removeFromContent(0, this.content.length - 1, true);
        this.setPlaceHolder(false);
        // this.content.splice(1, 0, item);
        // this.portionContentPos = 2;
        // this.addToContent(1, item, true);
    }

    public removePlaceContent(): void {
        this.content = [];
        this.moveCursorToStartPos();
    }

    public removePlaceHolderContent(): void {
        const name = this.getStartNewControlName();
        const newControlManager = this.paragraph.getParent()
            .getNewControlManager();
        const newControl = null == newControlManager ? null : newControlManager.getNewControlByName(name);
        if ( newControl ) {
            newControl.removePlaceHolderContent();
        }
        this.setPlaceHolder(false);
    }

    public isAfterNewControlEndBorder(pos?: number): boolean {
        const portionContentPos = (null != pos ? pos : this.portionContentPos);
        const item = this.content[portionContentPos - 1];

        if ( null != item && ParaElementType.ParaNewControlBorder === item.type
            && true === item.isNewControlEndBoder() && true === this.isEndPos(portionContentPos) ) {
            return true;
        }

        return false;
    }

    public getNewControlStartBoder(newControlName: string): number {
        for (let pos = 0, length = this.content.length; pos < length; pos++) {
            const item = this.content[pos];
            if ( ParaElementType.ParaNewControlBorder === item.type
                && newControlName === item.getNewControlName() && true === item.isNewControlStartBoder()) {
                return pos;
            }
        }

        return -1;
    }

    public isEnd(): boolean {
        if (this.portionContentPos === this.content.length) {
            return true;
        }
        return false;
    }

    public getNewControlEndBoder(newControlName: string): number {
        for (let pos = this.content.length - 1; pos >= 0; pos--) {
            const item = this.content[pos];
            if ( ParaElementType.ParaNewControlBorder === item.type
                && newControlName === item.getNewControlName() && true === item.isNewControlEndBoder()) {
                return pos;
            }
        }

        return -1;
    }

    /** 获取当前段落中的后一个Portion */
    public getNextPortion(): ParaPortion {
        const para = this.getParent();
        const pos = this.getPosInParent();
        if (pos === -1 || pos === para.content.length - 1) {
            return null;
        }
        return para.content[pos + 1];
    }

    public getTextLength( bNewControl?: boolean, bSym?: boolean): number {
        let length = 0;

        for (const item of this.content) {
            if ( !(true === item.isParaEnd() ||
                ( true === bNewControl && ParaElementType.ParaNewControlBorder === item.type)) ) {
                ++length;
            }
        }

        return length;
    }

    /**
     * 设置NewControl内文本是否为隐私显示
     * @param bViewSecret 是否隐私显示
     * @param pos 指定从pos位置开始加密
     */
    public setNewControlViewSecret( bViewSecret: boolean, pos: number = 0 ): number {
        let textLength = 0;

        const history = this.getHistory();

        if ( true === bViewSecret || 0 === pos ) {
            for (const item of this.content) {
                if ( ParaElementType.ParaText === item.type
                    || ParaElementType.ParaTab === item.type
                    || ParaElementType.ParaSpace === item.type
                    || ParaElementType.ParaDrawing === item.type ) {
                    textLength++;

                    if ( 0 !== pos && pos >= textLength ) {
                        item.setViewSecret(!bViewSecret, history);
                        continue;
                    }

                    item.setViewSecret(bViewSecret, history);
                }
            }
        } else if ( false === bViewSecret ) {
            const length = this.content.length;
            if ( -1 !== pos ) {
                for (let index = 0; index < length; index++) {
                    const element = this.content[index];
                    const type = element.type;
                    if ( ParaElementType.ParaText === type || ParaElementType.ParaTab === type
                        || ParaElementType.ParaSpace === type || ParaElementType.ParaDrawing === type
                        || ParaElementType.ParaMedEquation === type || ParaElementType.ParaBarcode === type
                        || ParaElementType.ParaQRCode === type) {
                        element.setViewSecret(bViewSecret, history);
                        textLength++;
                        break;
                    }
                }
            } else {
                for (let index = length - 1; index >= 0; index--) {
                    const element = this.content[index];
                    const type = element.type;
                    if ( ParaElementType.ParaText === type || ParaElementType.ParaTab === type
                        || ParaElementType.ParaSpace === type || ParaElementType.ParaDrawing === type
                        || ParaElementType.ParaMedEquation === type || ParaElementType.ParaBarcode === type
                        || ParaElementType.ParaQRCode === type) {
                        element.setViewSecret(bViewSecret, history);
                        textLength++;
                        break;
                    }
                }
            }
        }

        return textLength;
    }

    /**
     * 设置NewControl内文本是否显示
     * @param bHidden
     */
    public setNewControlTextHidden( bHidden: boolean, bNotSaveHistory: boolean = false ): void {
        const history = this.getHistory();
        // for (const item of this.content) {
        //     // item.bVisible = !bHidden;
        //     item.setVisible(!bHidden, history);
        // }

        this.setHidden(bHidden, bNotSaveHistory);
    }

    public isSelectedOrCursorInNewControlTitle(): boolean {
        let result = false;
        const length = this.content.length;

        if ( this.selection.bUse ) {
            let startPos = this.selection.startPos;
            let endPos = this.selection.endPos;

            if ( startPos > endPos ) {
                startPos = this.selection.endPos;
                endPos = this.selection.startPos;
            }

            result = ( this.isNewControlStart() && 1 < length
                       && ( ( 0 === startPos && endPos < length ) || ( 0 < startPos && endPos <= length )
                            && ( startPos !== endPos )) );
        } else {
            result = ( this.isNewControlStart() && 1 < length && this.portionContentPos < length );
        }

        return result;
    }

    public isRegionTitle(exceptEnd: boolean = true): boolean {
        const contents = this.content;
        const text = contents[0];
        if (text instanceof ParaTextExtend && text.getTypeName() === ParaTextName.Region) {
            if (exceptEnd === true && contents.length === this.portionContentPos) {
                return false;
            }
            return true;
        }

        return false;
    }

    public getTextAI(bParaEnd: boolean = false, bSection: boolean = false): string {
        let text = '';

        for (let index = 0, length = this.content.length; index < length; index++) {
            const item = this.content[index];
            const itemType = item.type;

            switch (itemType) {
                case ParaElementType.ParaText:
                case ParaElementType.ParaSpace:
                case ParaElementType.ParaTab: {
                    text += item.content;
                    break;
                }

                case ParaElementType.ParaNewControlBorder: {
                    if(item.isNewControlStartBoder()) {
                        text += '[';
                    }else {
                        text += ']';
                    }
                    
                    break;
                }

                case ParaElementType.ParaDrawing:

                case ParaElementType.ParaDrawing:
                case ParaElementType.ParaMedEquation:
                // case ParaElementType.ParaNewControlBorder:
                case ParaElementType.ParaEnd:
                case ParaElementType.ParaPageNum:
                case ParaElementType.ParaBarcode:
                case ParaElementType.ParaQRCode: {
                    if ( true === bParaEnd ) {
                        text += ' ';
                    }
                    break;
                }

                case ParaElementType.ParaNewLine: {
                    if (bSection) {
                        text += '\n';
                    }
                    break;
                }
            }
        }

        return text;
    }

    public getText(bParaEnd: boolean = false, bSection: boolean = false): string {
        let text = '';

        for (let index = 0, length = this.content.length; index < length; index++) {
            const item = this.content[index];
            const itemType = item.type;

            switch (itemType) {
                case ParaElementType.ParaText:
                case ParaElementType.ParaSpace:
                case ParaElementType.ParaTab: {
                    text += item.content;
                    break;
                }

                case ParaElementType.ParaDrawing:
                case ParaElementType.ParaMedEquation:
                // case ParaElementType.ParaNewControlBorder:
                case ParaElementType.ParaEnd:
                case ParaElementType.ParaPageNum:
                case ParaElementType.ParaBarcode:
                case ParaElementType.ParaQRCode: {
                    if ( true === bParaEnd ) {
                        text += ' ';
                    }
                    break;
                }

                case ParaElementType.ParaNewLine: {
                    if (bSection) {
                        text += '\n';
                    }
                    break;
                }
            }
        }

        return text;
    }

    public getHistory(): History {
        return this.paragraph ? this.paragraph.getHistory() : null;
    }

    public getHidden(): boolean {
        return this.bHidden;
    }

    public isHidden(): boolean {
        const document = this.paragraph.getDocument();
        return this.bHidden && !document.isDesignModel() || this.reviewType === ReviewType.Remove
        && document.isFinalRevision() && !this.isParaEnd();
    }

    public setHidden(bHidden: boolean, bNotSaveHistory: boolean = false): boolean {
        if ( this.bHidden === bHidden ) {
            return false;
        }

        const history = this.getHistory();
        if ( history && !bNotSaveHistory) {
            history.addChange(new ChangePortionHidden(this, this.bHidden, bHidden));
        }
        this.bHidden = bHidden;
        return true;
    }

    public isInHeaderFooter(): boolean {
        if (this.paragraph != null && this.paragraph.parent != null) {
            if (this.paragraph.parent.isHeaderFooter(false)) {
                return true;
            }
        }
        return false;
    }

    public isSelectionUse(): boolean {
        return this.selection.bUse;
    }

    public setReviewType(type: ReviewType, bCheckDeleteAdded?: boolean): void {
        const para = this.paragraph;

        if ( this.isSingleParaEndPortion() && para ) {
            if ( ReviewType.Common !== type && !para.getDocumentNext() && para.getParent() ) {
                return ;
            }
        }

        if ( type !== this.reviewType) {
            const oldReviewType = this.reviewType;
            const oldReviewInfo = this.reviewInfo.copy();
            if (oldReviewInfo && ReviewType.Add === oldReviewType && ReviewType.Common !== type) {
                this.reviewType = type;
                const secondRevInof = new ReviewInfo(this.paragraph.getDocument());

                secondRevInof.update();
                this.reviewInfo.setDeleteInfo(secondRevInof);
            } else {

                this.reviewType = type;
                this.reviewInfo.update();
            }

            const history = this.getHistory();
            if ( history ) {
                history.addChange(new ChangePortionReviewType(this,
                    { reviewType: oldReviewType,
                    reviewInfo: oldReviewInfo},
                    { reviewType: this.reviewType,
                    reviewInfo: this.reviewInfo.copy()}));
            }

            this.updateTrackRevisions();
        }
    }

    public setReviewType2(type: ReviewType): void {
        this.reviewType = type;
    }

    public setReviewInfo2(reviewInfo: ReviewInfo): void {
        this.reviewInfo = reviewInfo;
    }

    /**
     * 重置review
     */
    public resetReviewTypeAndInfo(): void {
        this.reviewType = ReviewType.Common;
        this.reviewInfo.reset();
    }

    public setReviewTypeWithInfo(reviewType: ReviewType, reviewInfo: ReviewInfo): void {
        const para = this.paragraph;

        if ( para && this.isSingleParaEndPortion()) {
            // ;
        }

        const history = this.getHistory();
        if ( history ) {
            history.addChange(new ChangePortionReviewType(this,
                { reviewType: this.reviewType,
                reviewInfo: this.reviewInfo ? this.reviewInfo.copy() : undefined},
                { reviewType,
                reviewInfo: reviewInfo ? reviewInfo.copy() : undefined}));
        }

        this.reviewInfo = reviewInfo;
        this.reviewType = reviewType;

        this.updateTrackRevisions();
    }

    public getReviewType(): ReviewType {
        return this.reviewType;
    }

    public getReviewInfo(): ReviewInfo {
        return this.reviewInfo;
    }

    public canDeleteInReviewMode(): boolean {
        const revisionManager = this.paragraph.parent.getRevisionsManager();
        const curUserId = revisionManager.getCurrentUserId();
        const reviewInfo = this.reviewInfo;
        return ( curUserId === reviewInfo.getUserId() &&
            (ReviewType.Add === this.reviewType && !reviewInfo.getSavedCount() && !reviewInfo.getDeleteInfo())
             );
    }

    public isReviewType(): boolean {
        return ( (ReviewType.Add === this.reviewType || ReviewType.Remove === this.reviewType ) );
    }

    public checkRevisionsChanges(checker: ParagraphRevisionChangesChecker,
                                 contentPos: ParagraphContentPos, depth: number): void {
        if ( this.isEmpty(false) ) {
            return ;
        }

        // if ( true !== this.isSingleParaEndPortion() ) {
        const reviewType = this.getReviewType();
        if ( reviewType !== checker.getReviewType() || ReviewType.Common !== reviewType ) {
            if ( this.isNewControlStart() && ReviewType.Remove === reviewType ) {
                const name = this.getStartNewControlName();
                const newControlManager = this.paragraph.getParent()
                            .getNewControlManager();
                const newControl = newControlManager ? newControlManager.getNewControlByName(name) : null;
                if ( newControl && newControl.getEndBorderPortion() ) {
                    const endBorderPortion = newControl.getEndBorderPortion();
                    if ( endBorderPortion && reviewType !== endBorderPortion.getReviewType() ) {
                        this.reviewType = endBorderPortion.getReviewType();
                        return ;
                    }
                }
            } else if ( this.isNewControlEnd() && ReviewType.Remove === reviewType ) {
                const name = this.getEndNewControlName();
                const newControlManager = this.paragraph.getParent()
                            .getNewControlManager();
                const newControl = newControlManager ? newControlManager.getNewControlByName(name) : null;
                if ( newControl && newControl.getStartBorderPortion() ) {
                    const startBorderPortion = newControl.getStartBorderPortion();
                    if ( startBorderPortion && reviewType !== startBorderPortion.getReviewType() ) {
                        this.reviewType = startBorderPortion.getReviewType();
                        return ;
                    }
                }
            }

            checker.flushAddRemoveChange();
            contentPos.update(0, depth);

            if ( ReviewType.Add === reviewType || ReviewType.Remove === reviewType ) {
                checker.startAddRemove(reviewType, contentPos);
            }
        }

        if ( ReviewType.Add === reviewType || ReviewType.Remove === reviewType ) {
            let text = '';
            const length = this.content.length;

            for (let index = 0; index < length; index++) {
                const item = this.content[index];
                const type = item.type;

                switch (type) {
                    case ParaElementType.ParaSym:
                    case ParaElementType.ParaText:
                    case ParaElementType.ParaSpace:
                    case ParaElementType.ParaTab: {
                        text += item.content;
                        break;
                    }
                    case ParaElementType.ParaMedEquation:
                    case ParaElementType.ParaBarcode:
                    case ParaElementType.ParaQRCode:
                    case ParaElementType.ParaDrawing: {
                        checker.addText(text);
                        text = '';
                        checker.addDrawing(item);
                        break;
                    }
                }
            }

            checker.addText(text);
            contentPos.update(length, depth);
            checker.setAddRemoveEndPos(contentPos);
            checker.updateAddRemoveReviewInfo(this.reviewInfo);
        }
        // }

        const hasPrChange = this.hasPropertyChange();
        const diffPr = this.getDiffPrChange();
        if ( hasPrChange !== checker.haveTextPrChange() || false === checker.comparePrChange(diffPr)) {
            checker.flushTextPrChange();
            contentPos.update(0, depth);
            if ( hasPrChange ) {
                checker.startTextPrChange(diffPr, contentPos);
            }
        }

        if ( hasPrChange ) {
            contentPos.update(this.content.length, depth);
            checker.setTextPrChangeEndPos(contentPos);
            checker.updateTextPrChangeReviewInfo(this.textProperty.reviewInfo);
        }
    }

    public acceptTextPrChange(): void {
        this.removePrChange();
    }

    public rejectTextPrChange(): void {
        if ( this.hasPropertyChange() ) {
            this.setTextProperty(this.textProperty.propertyChange);
            this.removePrChange();
        }
    }

    public acceptRevisionChanges(bAll: boolean, type?: RevisionChangeType): boolean {
        const reviewType = this.reviewType;
        if ( ReviewType.Common === reviewType || this.isSingleParaEndPortion() ) {
            return false;
        }

        if ( this.selection.bUse || bAll ) {
            // let startPos = this.selection.startPos;
            // let endPos = this.selection.endPos;
            // const length = this.content.length;

            // if ( startPos > endPos ) {
            //     startPos = this.selection.endPos;
            //     endPos = this.selection.startPos;
            // }

            // if ( bAll ) {
            //     startPos = 0;
            //     endPos = length;
            // }

            // let centerPortion = null;
            // let centerPortionPos = 0;

            // if ( 0 === startPos && length === endPos ) {
            //     centerPortion = this;
            // } else if ( 0 < startPos && length === endPos ) {
            //     centerPortion = this.splitImplement(startPos, this.paragraph, centerPortionPos);
            //     centerPortionPos++;
            // } else if ( 0 === startPos && length > endPos ) {
            //     centerPortion = this;
            //     this.splitImplement(endPos, this.paragraph, centerPortionPos);
            // } else {
            //     this.splitImplement(endPos, this.paragraph, centerPortionPos);
            //     centerPortion = this.splitImplement(startPos, this.paragraph, centerPortionPos);
            //     centerPortionPos++;
            // }

            if ( ReviewType.Add === reviewType ) {
                this.setReviewType(ReviewType.Common);
            } else if ( ReviewType.Remove === reviewType ) {
                this.paragraph.contentRemove2(this.getCurInParaIndex(), 1);
            }

            return true;
        }

        return false;
    }

    public rejectRevisionChanges(bAll: boolean, type?: RevisionChangeType): void {
        if ( ReviewType.Common === this.getReviewType() ) {
            return ;
        }

        if ( this.selection.bUse || bAll ) {
            // let startPos = this.selection.startPos;
            // let endPos = this.selection.endPos;
            // const length = this.content.length;

            // if ( startPos > endPos ) {
            //     startPos = this.selection.endPos;
            //     endPos = this.selection.startPos;
            // }

            // if ( bAll ) {
            //     startPos = 0;
            //     endPos = length;
            // }

            // let centerPortion = null;
            // let centerPortionPos = 0;

            // if ( 0 === startPos && length === endPos ) {
            //     centerPortion = this;
            // } else if ( 0 < startPos && length === endPos ) {
            //     centerPortion = this.splitImplement(startPos, this.paragraph, centerPortionPos);
            //     centerPortionPos++;
            // } else if ( 0 === startPos && length > endPos ) {
            //     centerPortion = this;
            //     this.splitImplement(endPos, this.paragraph, centerPortionPos);
            // } else {
            //     this.splitImplement(endPos, this.paragraph, centerPortionPos);
            //     centerPortion = this.splitImplement(startPos, this.paragraph, centerPortionPos);
            //     centerPortionPos++;
            // }

            if ( ReviewType.Add === this.reviewType ) {
                this.paragraph.contentRemove2(this.getCurInParaIndex(), 1);
            } else if ( ReviewType.Remove === this.reviewType ) {
                this.setReviewType(ReviewType.Common);
            }
        }
    }

    public checkTrackRevisionsBeforeAdd(): ParaPortion {
        const para = this.getParent();
        if ( !(para && para.parent) ) {
        //     bTrackRevisions = para.parent.isTrackRevisions();
        // } else {
            return null;
        }

        const bTrackRevisions = this.isTrackRevisions();
        const reviewType = this.getReviewType();
        const reviewInfo = this.getReviewInfo();
        const manager = para.parent.getRevisionsManager();
        const bAdd = ( reviewInfo.getUserId() === manager.getCurrentUserId() ||
                        (reviewInfo.getUserId() !== manager.getCurrentUserId()
                            && reviewInfo.getLevel() <= manager.getCurrentLevel()));
        if ( ( bTrackRevisions && (ReviewType.Add !== reviewType || bAdd)
            || ( false === bTrackRevisions && ReviewType.Common !== reviewType ) )) {
            const dstReviewType = ( bTrackRevisions ? ReviewType.Add : ReviewType.Common );
            const portionPos = this.getPosInParent();

            if ( -1 === portionPos ) {
                return null;
            }

            let bNew = false;
            const savedCount = this.getRevisionSavedCount();
            const bEquMode = para.logicDocument.getRevisionLevelEquMode();
            const curPos = this.portionContentPos;
            if ( 0 === curPos && 0 < portionPos ) {
                const prePortion = para.content[portionPos - 1];
                if ( ParaElementType.ParaPortion === prePortion.type && dstReviewType === prePortion.getReviewType()
                  && prePortion.getReviewInfo() && !(prePortion.isNewControlStart() || prePortion.isNewControlEnd())
                  && savedCount === prePortion.getRevisionSavedCount()) {
                    prePortion.portionContentPos = prePortion.content.length;
                    return prePortion;
                }
            }

            if ( curPos === this.content.length && ( portionPos < para.content.length - 2 ) ) {
                if ( bTrackRevisions && reviewInfo.getUserId() === manager.getCurrentUserId() &&
                    reviewInfo.getLevel() === manager.getCurrentLevel() && !savedCount) {
                    if ( dstReviewType === reviewType ) {
                        return this;
                    } else {
                        bNew = true;
                    }
                } else if ( reviewInfo.getLevel() > manager.getCurrentLevel()
                        || reviewInfo.getLevel() < manager.getCurrentLevel() ) {
                    // ;
                } else if ( bTrackRevisions && reviewInfo.getLevel() === manager.getCurrentLevel() ) {
                    if ( bEquMode ) {
                        bNew = true;
                    } else {
                        return null;
                    }
                }

                const nextPortion = para.content[portionPos + 1];
                const nextReviewInfo = nextPortion.getReviewInfo();
                if ( false === bNew && ParaElementType.ParaPortion === nextPortion.type
                    && dstReviewType === nextPortion.getReviewType() && nextReviewInfo ) {
                    if ( bTrackRevisions && nextReviewInfo.getUserId() === manager.getCurrentUserId() &&
                        nextReviewInfo.getLevel() === manager.getCurrentLevel()
                        && savedCount === nextPortion.getRevisionSavedCount()) {
                        nextPortion.portionContentPos = 0;
                        return nextPortion;
                    } else if ( reviewInfo.getLevel() > manager.getCurrentLevel()
                        || reviewInfo.getLevel() < manager.getCurrentLevel() ) {
                        // ;
                    } else if ( bTrackRevisions && reviewInfo.getLevel() === manager.getCurrentLevel() ) {
                        if ( !bEquMode ) {
                            return null;
                        }
                    }
                }
            } else if ( curPos < this.content.length ) {
                if ( bTrackRevisions && reviewInfo.getUserId() === manager.getCurrentUserId() && !savedCount &&
                    reviewInfo.getLevel() === manager.getCurrentLevel() && ReviewType.Add === reviewType ) {
                    return this;
                } else if ( bTrackRevisions && reviewInfo.getLevel() > manager.getCurrentLevel() ) {
                    return null;
                } else if ( reviewInfo.getLevel() < manager.getCurrentLevel() ) {
                    // ;
                } else if ( bTrackRevisions && reviewInfo.getLevel() === manager.getCurrentLevel() ) {
                    if ( !bEquMode ) {
                        return null;
                    }
                }
            } else if ( curPos === this.content.length && bTrackRevisions ) {
                if (  reviewInfo.getUserId() === manager.getCurrentUserId() &&
                    reviewInfo.getLevel() === manager.getCurrentLevel() && !savedCount) {
                    if ( dstReviewType === reviewType ) {
                        return this;
                    }
                }
            }

            const newPortion = new ParaPortion(para);
            newPortion.setProperty(this.textProperty.copy());
            // 清除波浪线属性
            clearWavyUnderlineFromTextProperty(newPortion.textProperty);

            newPortion.setReviewType(dstReviewType);
            newPortion.portionContentPos = 0;

            if ( 0 === curPos ) {
                para.addToContent(portionPos, newPortion);
            } else if ( curPos === this.content.length ) {
                para.addToContent(portionPos + 1, newPortion);
            } else {
                const oldReviewInfo = ( this.reviewInfo ? this.reviewInfo.copy() : undefined );
                const oldReviewType = this.reviewType;

                const rightPortion = this.splitPortion(curPos);
                para.addToContent(portionPos + 1, newPortion);
                para.addToContent(portionPos + 2, rightPortion);

                this.setReviewTypeWithInfo(oldReviewType, oldReviewInfo);
                rightPortion.setReviewTypeWithInfo(oldReviewType, oldReviewInfo.copy());
            }

            return newPortion;
        } else if ( bTrackRevisions && ReviewType.Add === reviewType && !bAdd ) {
            if ( reviewInfo.getUserId() === manager.getCurrentUserId() &&
                reviewInfo.getLevel() === manager.getCurrentLevel() ) {
                return this;
            } else if ( reviewInfo.getLevel() > manager.getCurrentLevel() ) {
                const curPos = this.portionContentPos;
                const bParaEnd = ( this.isParaEndPortion() && curPos === this.content.length - 1);
                if ( curPos === this.content.length || 0 === curPos || bParaEnd ) {
                    if ( curPos === this.content.length ) {
                        // tslint:disable-next-line: no-shadowed-variable
                        const portionPos = this.getPosInParent();

                        if ( portionPos < para.content.length - 2 ) {
                            const nextPortion = para.content[portionPos + 1];
                            const nextReviewInfo = nextPortion.getReviewInfo();
                            const bEquMode = para.logicDocument.getRevisionLevelEquMode();

                            if ( reviewInfo.getLevel() < nextReviewInfo.getLevel() ) {
                                return null;
                            } else if (reviewInfo.getLevel() === nextReviewInfo.getLevel() ) {
                                if ( !bEquMode ) {
                                    return null;
                                }
                            }
                        }
                    }

                    const portionPos = this.getPosInParent();
                    const newPortion = new ParaPortion(para);
                    newPortion.setProperty(this.textProperty.copy());
                    // 清除波浪线属性
                    clearWavyUnderlineFromTextProperty(newPortion.textProperty);

                    newPortion.setReviewType(reviewType);
                    newPortion.portionContentPos = 0;

                    if ( 0 === curPos ) {
                        para.addToContent(portionPos, newPortion);
                    } else if ( curPos === this.content.length ) {
                        para.addToContent(portionPos + 1, newPortion);
                    } else if ( bParaEnd ) {
                        const oldReviewInfo = ( this.reviewInfo ? this.reviewInfo.copy() : undefined );
                        const oldReviewType = this.reviewType;

                        const rightPortion = this.splitPortion(curPos);
                        para.addToContent(portionPos + 1, newPortion);
                        para.addToContent(portionPos + 2, rightPortion);

                        this.setReviewTypeWithInfo(oldReviewType, oldReviewInfo);
                        rightPortion.setReviewTypeWithInfo(oldReviewType, oldReviewInfo.copy());
                    }

                    return newPortion;
                }
            }
        }

        return null;
    }

    public getRevisionContent(): string {
        let content = '';
        let bDrawing = false;
        // let bNewControl = false;
        const bPlaceHolder = this.isPlaceHolder();

        this.content.forEach((item) => {
            switch ( item.type ) {
                case ParaElementType.ParaText:
                    // if ( bNewControl ) {
                    //     if ( !bPlaceHolder ) {
                    //         content = '';
                    //     } else {
                    //         break;
                    //     }
                    //     bNewControl = false;
                    // } else if ( bDrawing ) {
                    //     bDrawing = false;
                    //     content += '\n';
                    // }

                    content += item.content;
                    break;

                case ParaElementType.ParaSpace:
                    content += RepalceText.SpaceForUI;
                    break;

                case ParaElementType.ParaMedEquation:
                    // content = '' !== content ? content + '\n' : content;
                    content += '<医学表达式>';
                    bDrawing = true;
                    break;

                case ParaElementType.ParaDrawing:
                    // content = '' !== content ? content + '\n' : content;
                    content += '<图片>';
                    bDrawing = true;
                    break;
                case  ParaElementType.ParaBarcode: {
                    content += '<条形码>';
                    bDrawing = true;
                    break;
                }
                case ParaElementType.ParaQRCode: {
                    content += '<二维码>';
                    bDrawing = true;
                    break;
                }

                case ParaElementType.ParaNewControlBorder:
                    // content = '结构化元素';
                    // bNewControl = true;
                    break;

                case ParaElementType.ParaPageNum:
                    content += (item as ParaPageNum).getString();
                    break;
            }
        });

        return content;
    }

    public canDelete(): boolean {
        let startPos = this.selection.startPos;
        let endPos = this.selection.endPos;
        if ( startPos > endPos ) {
            const temp = startPos;
            startPos = endPos;
            endPos = temp;
        }

        for (let i = startPos; i < endPos; i++) {
            const item = this.content[i];
            if (item instanceof ParaDrawing) {
                if (item.deleteLocked === true) {
                    return false;
                }
            }
        }

        const para = this.getParent();
        if ( this.isTrackRevisions() ) {

            const revisionManager = para.parent.getRevisionsManager();
            const curUserId = revisionManager.getCurrentUserId();
            const curLevel = revisionManager.getCurrentLevel();
            const bEquMode = para.logicDocument.getRevisionLevelEquMode();

            for (let index = startPos; index <= endPos; index++) {
                const element = this.content[index];
                const reviewInfo = this.getReviewInfo();
                if ( element ) {
                    if ( curLevel < reviewInfo.getLevel() ) {
                        return false;
                    } else if ( curLevel === reviewInfo.getLevel() ) {
                        if ( curUserId === reviewInfo.getUserId() ) {
                            continue;
                        } else if ( !bEquMode ) {
                            return false;
                        }
                    }
                }
            }
        }

        return true;
    }

    public makeElementCurrentPos(bUpdateStates?: boolean): void {
        if ( this.paragraph && this.paragraph.parent ) {
            this.setCurrentPosInParagraph();
            this.paragraph.setElementCurrentPos(true === bUpdateStates ? true : false);
        }
    }

    public getPosByElement(portion: ParaPortion): boolean {
        if ( this === portion ) {
            return true;
        }

        return false;
    }

    public getReviewColor(): string {
        if ( this.reviewInfo ) {
            // return this.reviewInfo.getColor();
            return this.reviewInfo.isSecondInfo() ? this.reviewInfo.getDeleteInfo()
                        .getColor() : this.reviewInfo.getColor();
        }

        return '活力橙';
    }

    public getReviewStyle(): number {
        if ( this.reviewInfo ) {
            // return this.reviewInfo.getStyle();
            return this.reviewInfo.isSecondInfo() ? this.reviewInfo.getDeleteInfo()
                        .getStyle() : this.reviewInfo.getStyle();
        }

        return 0;
    }

    public repalceText(pos: number, text: string = ' '): void {
        if ( 0 > pos || 0 >= this.content.length ) {
            return ;
        }

        const paraText = new ParaText(text);
        const history = this.getHistory();
        if ( history ) {
            history.addChange(new ChangePortionReplaceText(this, this.content[pos], paraText));
        }

        if ( this.content[pos] ) {
            this.content[pos] = paraText;
            this.setPlaceHolder(false);
        }
    }

    public write(bAdd: boolean): any {
        const pos = this.paragraph.getCurContentPosInDoc();
        pos.update2(this.getCurInParaIndex(), pos.getDepth() - 1);
        pos.update2(this.portionContentPos, pos.getDepth());

        return {
            pos: pos.data.toString(),
            lines: this.lines.toString(),
            // content: this.getText(),
            // contentType: this.content[0] ? this.content[0].getType() : -1,
            // selection: {
            //     bUse: this.selection.bUse,
            //     startPos: this.selection.startPos,
            //     endPos: this.selection.endPos,
            // },
            // newControl: {
            //     name: name,
            //     bPlaceHolder: this.bPlaceHolder,
            //     type:
            // },
        };
    }

    public getInsideNewControlName(pos?: number, bPlaceHolder?: boolean): string {
        const para = this.paragraph;
        const index = (null == pos ? this.getCurInParaIndex() : pos);
        if ( (para.content[index - 1] && para.content[index - 1].isNewControlStart()) &&
            (para.content[index + 1] && para.content[index + 1].isNewControlEnd()) ) {
            const name = para.content[index - 1].content[0].getNewControlName();

            if ( true === bPlaceHolder ) {
                return (para.content[index - 1].bPlaceHolder ? name : null);
            } else {
                return name;
            }
        }

        return null;
    }

    public checkMinFixedLength(): any {
        if ( this.isNewControlStart() ) {
            const name = this.content[0].getNewControlName();
            if ( null != name ) {
                const newControlManager = this.paragraph.getParent()
                                                .getNewControlManager();
                const newControl = newControlManager.getNewControlByName(name);
                if ( newControl && 0 < newControl.getFixedLength() ) {
                    let titleLength = 0;
                    if (1 < this.content.length) {
                        for (let index = 1; index < this.content.length; index++) {
                            const element = this.content[index];
                            titleLength += element.width;
                        }
                    }
                    return { newControlName: name, fixedLength: newControl.getFixedLength(),
                        align: newControl.getAlignments(), titleLength };
                } else if (0 < this['newControlFixedLength']) {
                    let titleLength = 0;
                    if (1 < this.content.length) {
                        for (let index = 1; index < this.content.length; index++) {
                            const element = this.content[index];
                            titleLength += element.width;
                        }
                    }
                    return { newControlName: name, fixedLength: this['newControlFixedLength'],
                        align: this['alignment'], titleLength };
                }
            }
        }

        return null;
    }

    public checkEndMinFixedLength(name: string): boolean {
        if ( this.isNewControlEnd() ) {
            return (name === this.content[this.content.length - 1].getNewControlName() ? true : false);
        }

        return false;
    }

    public isTableCellContent(): boolean {
        return (this.paragraph && this.paragraph.parent && this.paragraph.parent.isTableCellContent());
    }

    /**
     * 是否只含有普通文本内容或图片
     */
    public isTextContent(bContainDrawing: boolean): { result: boolean, drawNames?: string[] } {
        if ( this.isSingleParaEndPortion() ) {
            return { result: false };
        }

        let drawNames = [];
        for (let index = 0, length = this.content.length; index < length; index++) {
            const element = this.content[index];
            const bDrawing = (element && element.isSvgDrawing() || element.isImage() || element.isMedEquation());

            if ( element &&
                    (element.isNewControlStartBoder() || element.isNewControlEndBoder() || element.isParaEnd()
                        || this.bPlaceHolder
                        || (!bContainDrawing && bDrawing)
                    )
                ) {
                drawNames = null;
                return { result: false };
            }

            if ( bDrawing ) {
                drawNames.push((element as ParaDrawing).getDrawingName());
            }
        }

        return { result: true, drawNames };
    }

    public removeDrawing(): void {
        if ( this.isSingleParaEndPortion() ) {
            return;
        }

        for (let index = 0, length = this.content.length; index < length; index++) {
            const element = this.content[index];
            const bDrawing = (element && element.isSvgDrawing() || element.isImage() || element.isMedEquation());

            if ( bDrawing ) {
                this.content.splice(index, 1);
                index--;
                length = this.content.length;
            }
        }
    }

    public isTrackRevisions(): boolean {
        const para = this.paragraph;
        if ( para && para.logicDocument && para.parent && !para.parent.isHeaderFooterContent() ) {
            return para.logicDocument.isTrackRevisions();
        }

        return false;
    }

    public isNeedRecalc(): boolean {
        return this.recalcInfo.bRecalc;
    }

    public getRevisionSavedCount(): number {
        return (this.reviewInfo ? this.reviewInfo.getSavedCount() : 0);
    }

    /**
     * 清除修订信息
     */
    public resetRevisions(): void {
        if (ReviewType.Common !== this.reviewType) {
            this.reviewType = ReviewType.Common;
        }

        this.reviewInfo = new ReviewInfo(null);
    }

    public removeSoftLine(): void {
        const length = this.content.length;
        for (let index = length - 1; index >= 0; index--) {
            const element = this.content[index];
            if (element && element.isSoftLine()) {
                this.content.splice(index, 1);
            }
        }
    }

    public getPosInParent(): number {
        const parent = this.getParent();

        if ( null == parent || null == parent.content ) {
            return -1;
        }

        for (let index = 0, count = parent.content.length; index < count; index++) {
            if ( this === parent.content[index] ) {
                return index;
            }
        }

        return -1;
    }

    /**
     * 具体实现portion分割
     * @param curPos
     */
    private splitImplement(curPos: number, parent?: Paragraph, parentPos?: number): ParaPortion {
        const history = this.getHistory();
        if ( history) {
            history.addChange(new ChangePortionStartSplit(this, curPos));
        }

        const bUpdateParent = ( parent && null != parentPos && this === parent.content[parentPos] ? true : false);
        const bUpdateSelection = (true === bUpdateParent && parent.isSelectionUse()
                                    && this.isSelectionUse() ? true : false);

        const newPortion = new ParaPortion(this.paragraph);
        newPortion.setReviewTypeWithInfo(this.reviewType, this.reviewInfo ? this.reviewInfo.copy() : undefined);

        // 设置文本属性，插入文本内容
        newPortion.setProperty(this.textProperty.copy());
        newPortion.bHidden = this.bHidden;

        // if ( bUpdateSelection ) {
        //     ;
        // }

        if ( bUpdateParent ) {
            parent.addToContent(parentPos + 1, newPortion);
        }
        newPortion.concatToContent(this.content.slice(curPos));

        // 删除文本内容
        this.removeFromContent(curPos, this.content.length - curPos, true, true);

        if ( history) {
            history.addChange(new ChangePortionEndSplit(this, newPortion));
        }
        return newPortion;
    }

    /**
     * 在向Portion的内容添加元素时,更新位置，选择，光标和换行符等
     * @param pos 插入元素时的开始位置
     */
    private updatePositionOnAdd( pos: number ): void {
        // console.log("updatePositionOnAdd: pos: " + pos + " ; contentPos: " + this.portionContentPos);
        // 更新当前位置
        if ( this.portionContentPos >= pos ) {
            this.portionContentPos++;
        }
        // else
        //    this.portionContentPos = pos;

        // 更新选择的开始和结束位置
        if ( true === this.selection.bUse ) {
            if ( this.selection.startPos >= pos ) {
                this.selection.startPos++;
            }

            if ( this.selection.endPos >= pos ) {
                this.selection.endPos++;
            }
        }

        const linesCount = this.getLinesCount();
        for ( let curLine = 0; curLine < linesCount; curLine++ ) {
            let startPos = this.getRangeStartPos(curLine);
            let endPos = this.getRangeEndPos(curLine);

            if ( startPos > pos ) {
                startPos++;
            }

            if ( endPos > pos ) {
                endPos++;
            }

            this.fillRange(curLine, 0, startPos, endPos);

            // 在portion尾部输入内容
            if ( pos === this.content.length - 1 && linesCount - 1 === curLine ) {
                this.fillRangeEndPos(curLine, 0, this.getRangeEndPos(curLine) + 1);
            }
        }
    }

    /**
     * 在向Portion的内容删除元素时,更新位置，选择，光标和换行符等
     * @param pos 删除开始位置
     * @param count 删除的长度
     */
    private updatePositionOnRemove( pos: number, count: number ): void {
        // 更新当前位置
        if ( this.portionContentPos > pos + count ) {
            this.portionContentPos -= count;
        } else if ( this.portionContentPos > pos ) {
            this.portionContentPos = pos;
        }

        // 更新选择的开始和结束位置
        if ( true === this.selection.bUse ) {
            // 正向选择
            if ( this.selection.startPos <= this.selection.endPos ) {
                if ( this.selection.startPos > pos + count ) {
                    this.selection.startPos -= count;
                } else if ( this.selection.startPos > pos ) {
                    this.selection.startPos = pos;
                }

                if ( this.selection.endPos >= pos + count ) {
                    this.selection.endPos -= count;
                } else if ( this.selection.endPos > pos ) {
                    this.selection.endPos = Math.max(0, pos - 1);
                }
            } else {
                if ( this.selection.startPos >= pos + count ) {
                    this.selection.startPos -= count;
                } else if ( this.selection.startPos > pos ) {
                    this.selection.startPos = Math.max(0, pos - 1);
                }

                if ( this.selection.endPos > pos + count ) {
                    this.selection.endPos -= count;
                } else if ( this.selection.endPos > pos ) {
                    this.selection.endPos = pos;
                }
            }
        }

        const linesCount = this.getLinesCount();
        for ( let curLine = 0; curLine < linesCount; curLine++ ) {
            let startPos = this.getRangeStartPos(curLine);
            let endPos = this.getRangeEndPos(curLine);

            if ( startPos > pos + count ) {
                startPos -= count;
            } else if ( startPos > pos ) {
                startPos = pos;
            }

            if ( endPos >= pos + count ) {
                endPos -= count;
             } else if ( endPos >= pos ) {
                endPos = Math.max(0, pos);
            }

            this.fillRange(curLine, 0, startPos, endPos);
        }
    }

    /**
     * 是否包含受保护图片
     */
    private containDelProtectImages(delItems: ParaElementBase[]): boolean {
        let result = false;
        for (let nIndex = 0, nCount = delItems.length; nIndex < nCount; ++nIndex) {
            const type = delItems[nIndex].type;
            if ( type === ParaElementType.ParaDrawing || type === ParaElementType.ParaMedEquation) {
                const delImage = delItems[nIndex] as ParaDrawing;
                if (delImage.deleteLocked) {
                    result = true;
                    break;
                }
            }
        }
        return result;
    }

    private updateTrackRevisions(): void {
        const para = this.paragraph;

        if ( this.isTrackRevisions() && para && para.parent && para.parent.getRevisionsManager() ) {
            const revisionManager = para.parent.getRevisionsManager();
            revisionManager.checkElement(this.paragraph);
        }
    }

    private updateTrackRevisionOnChangeContent(bUpdate: boolean): void {
        if ( ReviewType.Common !== this.reviewType ) {
            this.updateTrackRevisions();

            if ( true === bUpdate && this.paragraph ) {
                const oldReviewInfo = this.reviewInfo.copy();
                const type = this.getReviewType();
                if (!!oldReviewInfo && ReviewType.Remove === type && !!this.reviewInfo.getDeleteInfo() ) {
                    const secondRevInof = this.reviewInfo.getDeleteInfo();

                    secondRevInof.update();
                } else {
                    this.reviewInfo.update();
                }

                const history = this.getHistory();
                if ( history ) {
                    history.addChange(new ChangePortionContentReviewInfo(this, oldReviewInfo, this.reviewInfo.copy()));
                }
            }
        }
    }

    private updateTrackRevisionOnChangeTextPr(bUpdate: boolean): void {
        if ( this.hasPropertyChange() ) {
            this.updateTrackRevisions();

            if ( true === bUpdate && this.paragraph ) {
                const oldReviewInfo = this.reviewInfo.copy();
                this.reviewInfo.update();

                const history = this.getHistory();
                if ( history ) {
                    history.addChange(new ChangePortionPrReviewInfo(this, oldReviewInfo,
                        this.textProperty.reviewInfo.copy()));
                }
            }
        }
    }

    private removePrChange(): void {
        if ( this.hasPropertyChange() ) {
            const pr = this.textProperty;
            const history = this.getHistory();
            if ( history ) {
                history.addChange(new ChangePortionPropertyChange(this,
                    { PrChange: pr.propertyChange, reviewInfo: pr.reviewInfo },
                    { PrChange: undefined, reviewInfo: undefined }));
            }

            pr.removePropertyChange();
            this.updateTrackRevisions();
        }
    }

    private getDiffPrChange(): TextProperty {
        return this.textProperty.getDiffPrChange();
    }

    private setCurrentPosInParagraph(): void {
        const para = this.paragraph;
        if ( null == para ) {
            return ;
        }

        const contentPos = para.getPosByElement(this);
        if ( null == contentPos ) {
            return ;
        }

        contentPos.add(this.portionContentPos);
        para.setParaContentPos(contentPos, -1);
    }

    private splitPortionInCurPos(): ParaPortion {
        let newPortion: ParaPortion = null;
        const para = this.getParent();
        const pos = this.getPosInParent();

        if ( para && -1 !== pos ) {
            newPortion = new ParaPortion(para);
            newPortion.setTextProperty(this.textProperty.copy());

            if ( 0 === this.portionContentPos ) {
                para.addToContent(pos, newPortion);
            } else if ( this.content.length === this.portionContentPos ) {
                para.addToContent(pos + 1, newPortion);
            } else {
                const rightPortion = this.splitPortion(this.portionContentPos);
                para.addToContent(pos + 1, newPortion);
                para.addToContent(pos + 2, rightPortion);
            }
        }

        return newPortion;
    }

    private getParent(): Paragraph {
        if ( null == this.paragraph ) {
            return null;
        }

        const contentPos = this.paragraph.getPosByElement(this);
        if ( null == contentPos || 0 > contentPos.getDepth()) {
            return null;
        }

        return this.paragraph;
    }

    /**
     * 修订状态下，当前修订人的权限等级是否低于其他修订人的权限等级
     * @returns
     */
    private isCurLowLevel(): boolean {
        const para = this.paragraph;
        const revisionManager = para.logicDocument.getRevisionsManager();
        const curUserId = revisionManager.getCurrentUserId();
        const curLevel = revisionManager.getCurrentLevel();

        if ( curLevel < this.reviewInfo.getLevel() ) {
            return true;
        }  else if ( curLevel === this.reviewInfo.getLevel() ) {
            const bEquMode = para.logicDocument.getRevisionLevelEquMode();
            if ( curUserId !== this.reviewInfo.getUserId() && !bEquMode ) {
                return true;
            }
        }

        return false;
    }

    /**
     * 重置结束符的属性：计算行高
     * 空段落时：结束符属性设置为默认或上一个空portion的属性；
     * 非空段落时，设置为前一个非空portion的属性，且portion的高度 != 当前结束符的高度
     * @param para
     */
    private resetParaEndProperty(para: Paragraph): void {
        let curPos = para.content.length - 2;
        let prePortion = para.content[curPos];

        if ( para.isEmpty() ) {
            this.textHeight = prePortion.textHeight;
            this.textProperty.fontSize = prePortion.textProperty.fontSize;
            this.textProperty.fontFamily = prePortion.textProperty.fontFamily;
            this.ascent = prePortion.ascent;
            this.descent = prePortion.descent;
        } else {
            const searchPos = {
                pos: new ParagraphContentPos(),
                bFound: false,
                bSelection: false,
            };
            const contentPos = new ParagraphContentPos();
            contentPos.clear();
            contentPos.add(curPos);
            contentPos.add(prePortion.content.length);
            para.getLeftPos(searchPos, contentPos);

            if ( searchPos.bFound ) {
                curPos = searchPos.pos.get(0);
                prePortion = para.content[curPos];
            }

            if ( prePortion.textHeight !== this.textHeight && 0 !== prePortion.textHeight ) {
                this.textHeight = para.content[curPos].textHeight;
                this.textProperty.fontSize = prePortion.textProperty.fontSize;
                this.textProperty.fontFamily = prePortion.textProperty.fontFamily;
                this.ascent = prePortion.ascent;
                this.descent = prePortion.descent;
            }

            if ( 0 === this.ascent || 0 === this.descent ) {
                const ratio = this.recalculateLineSpacing(para.paraProperty);

                this.ascent = this.textHeight * ratio * 0.4;
                this.descent = this.textHeight * ratio * 0.6; // this.ascent;
            }
        }
    }

    private setNewControlFixedLength(
        oPRS: ParagraphRecalculateStateWrap, curItem: ParaElementBase, textLength: number, startX: number,
        wordLen: number
    ): void {
        const para = oPRS.paragraph;
        if ( 0 < oPRS.fixedLength ) {
            const diff = oPRS.fixedLength - textLength;

            if ( 0 < diff ) {
                // 检查是否换行
                let bNewRange = oPRS.newControlEndBorderBreak;
                if ( bNewRange || curItem.width + startX > oPRS.xEnd
                    || curItem.width + startX > oPRS.xEnd
                    || curItem.width + startX + wordLen > oPRS.xEnd
                    || diff + startX > oPRS.xEnd
                    || diff + curItem.width > oPRS.xEnd
                    || diff + curItem.width + startX > oPRS.xEnd
                    || diff + curItem.width + startX + wordLen > oPRS.xEnd ) {

                    bNewRange = true;
                    oPRS.newControlEndBorderBreak = true;
                    oPRS.setLineBreakPos(0, false);
                }

                let alignType = (bNewRange || oPRS.newControlContentBreak) ? AlignType.Left : oPRS.newControlAlign;
                const startBorderPortion = para.content[oPRS.startPortionIndex];
                const startBorderItem = startBorderPortion.content[0];
                const lines = startBorderPortion.getLinesCount();

                // 居中对齐：前边框在上一行，内容和后边框在下一行
                let bAddDiff = false;
                if ( AlignType.Center === alignType &&
                    ( (!bNewRange && 1 === lines) ||
                        (1 < lines && AlignType.Center === alignType && 1 < startBorderPortion.content.length))
                    && startBorderPortion.getLineByPos(0) !== oPRS.line ) {
                    alignType = AlignType.Left;
                    bAddDiff = true;
                }

                // 有标题
                // const length = startBorderPortion.content.length;
                // if (1 < length) {
                //     for (let index = 1; index < length; index++) {
                //         const element = startBorderPortion.content[index];
                //         diff -= element.width;
                //     }
                // }

                switch (alignType) {
                    case AlignType.Center: {
                        startBorderItem.width += diff / 2;
                        startBorderItem.widthVisible = startBorderItem.width; // + diff / 2;

                        curItem.setRemainWidth(diff / 2);
                        curItem.width += curItem.getRemainWidth();
                        curItem.widthVisible = curItem.width;
                        if (bAddDiff) {
                            oPRS.startXDiff = curItem.getRemainWidth();
                        }
                        break;
                    }

                    case AlignType.Right: {
                        // startBorderItem.widthVisible = startBorderItem.width + diff;

                        // curItem.setRemainWidth(0);
                        // curItem.widthVisible = curItem.width;
                        break;
                    }

                    case AlignType.Left:
                    default: {
                        curItem.setRemainWidth(diff);
                        curItem.width += curItem.getRemainWidth();
                        curItem.widthVisible = curItem.width;
                        break;
                    }
                }
            }
        } else {
            curItem.setRemainWidth(0);
        }

        if ( !oPRS.newControlEndBorderBreak ) {
            oPRS.resetNewControlFixedLength();
        }
    }
}
