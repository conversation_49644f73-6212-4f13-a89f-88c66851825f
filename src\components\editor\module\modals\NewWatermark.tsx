import * as React from 'react';
import '../../style/watermark.less';
// tslint:disable-next-line: max-line-length
import { INewControlProperty, IEditorBackground, DEFAULT_BACKGROUND, FillColorType, WatermarkType, WATERMARK_DEFAULT_FONTSIZE } from '../../../../common/commonDefines';
import Dialog from '../../ui/Dialog';
import Button from '../../ui/Button';
import Checkbox from '../../ui/CheckboxItem';
import Input from '../../ui/Input';
import Radio from '../../ui/Radio';
// import { message } from '../../../../common/Message';
// import { IFRAME_MANAGER } from '../../../../common/IframeManager';

interface IDialogProps {
    documentCore: any;
    visible: boolean;
    id: string;
    property?: INewControlProperty;
    close: (name: string | number, bRefresh?: boolean) => void;
}

interface IState {
    bRefresh: boolean;
}

export default class NewWatermark extends React.Component<IDialogProps, IState> {
    private watermarkProperty: IEditorBackground;
    private visible: any;
    private watermarkTypeDatas: any[];
    private watermarkValue: number; // for radio button
    private fillColorTypeDatas: any[];
    private fillColorValue: number; // for radio button

    constructor(props: any) {
        super(props);
        this.state = {
            bRefresh: false,
        };
        this.watermarkValue = WatermarkType.Loose;
        this.watermarkTypeDatas = [
            {key: '宽松型', value: WatermarkType.Loose},
            {key: '紧凑型', value: WatermarkType.Tight},
        ];
        this.fillColorValue = FillColorType.White;
        this.fillColorTypeDatas = [
            {key: '白色', value: FillColorType.White},
            {key: '练色', value: FillColorType.Granite},
            {key: '瓷白色', value: FillColorType.Ceramic},
            {key: '素色', value: FillColorType.Plain},
        ];

        this.visible = this.props.visible;
        this.watermarkProperty = {
            watermarkEnabled: DEFAULT_BACKGROUND.watermarkEnabled,
            watermarkType: DEFAULT_BACKGROUND.watermarkType,
            watermarkText: JSON.parse(JSON.stringify(DEFAULT_BACKGROUND.watermarkText)),
            fillColorEnabled: DEFAULT_BACKGROUND.fillColorEnabled,
            fillColorType: DEFAULT_BACKGROUND.fillColorType,
            fillTextColor: DEFAULT_BACKGROUND.fillTextColor,
        };

        // set props
        this.setEditorBackgroundFromCore();
    }

    public render(): any {
        return (
            <Dialog
                visible={this.visible}
                width={350}
                open={this.open}
                title='背景与水印'
                footer={this.renderFooter()}
                id={this.props.id}
            >
                <div className='watermark-box'>
                    <div className='editor-line'>
                        <Checkbox
                            name='watermarkEnabled'
                            value={this.watermarkProperty.watermarkEnabled}
                            onChange={this.onChange}
                        >
                            文字水印
                        </Checkbox>
                    </div>
                    <div className='editor-line'>
                        <span className='text-descriptor'>第一行：</span>
                        <Input
                            name='watermarkText1'
                            disabled={!this.watermarkProperty.watermarkEnabled}
                            value={this.watermarkProperty.watermarkText[0].text}
                            onChange={this.onChangeText}
                        />
                    </div>
                    <div className='editor-line'>
                        <span className='text-descriptor'>第二行：</span>
                        <Input
                            name='watermarkText2'
                            disabled={!this.watermarkProperty.watermarkEnabled}
                            value={this.watermarkProperty.watermarkText[1].text}
                            onChange={this.onChangeText}
                        />
                    </div>
                    <div className='editor-line'>
                        <Radio value={this.watermarkValue} data={this.watermarkTypeDatas} name='watermarkType' onChange={this.onChange} />
                    </div>
                    <div className='editor-line'>
                        <Checkbox
                            name='fillColorEnabled'
                            value={this.watermarkProperty.fillColorEnabled}
                            onChange={this.onChange}
                        >
                            背景填充
                        </Checkbox>
                    </div>
                    <div className='editor-line fill-color-type'>
                        <Radio value={this.fillColorValue} data={this.fillColorTypeDatas} name='fillColorType' onChange={this.onChange} />
                    </div>
                </div>
            </Dialog>
        );
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        this.visible = nextProps.visible;
    }

    private renderFooter(): any { // return dom, no () => {}
        return (
            <span>
                <Button onClick={this.close}>取消</Button>
                <Button type='primary' onClick={this.confirm}>确认</Button>
            </span>
        );
    }

    private open = () => {
        // set props
        this.setEditorBackgroundFromCore();
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private close = (bRefresh?: boolean): void => {
        this.props.close(this.props.id, bRefresh);
        this.visible = false;
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private confirm = (): void => {
        const documentCore = this.props.documentCore;
        documentCore.setEditorBackground(this.watermarkProperty);
        this.close(true);
    }

    private onChange = (value: any, name: string): void => {
        // console.log(value, name)
        this.watermarkProperty[name] = value;
        if (name === 'watermarkType') {
            this.watermarkValue = value;
        } else if (name === 'fillColorType') {
            this.fillColorValue = value;
        }

        if (!this.watermarkProperty.watermarkEnabled) {
            this.watermarkProperty.watermarkText = JSON.parse(JSON.stringify(DEFAULT_BACKGROUND.watermarkText));
        }

        this.setState({bRefresh: !this.state.bRefresh});
    }

    private onChangeText = (value: any, name: string): void => {
        // if (name === 'watermarkText') {
        //     this.watermarkProperty[name][0].text = value;
        // }
        if (name === 'watermarkText1') {
            this.watermarkProperty['watermarkText'][0].text = value;
        } else if (name === 'watermarkText2') {
            let secondWatermark = this.watermarkProperty['watermarkText'][1];
            if (secondWatermark == null) {
                secondWatermark = JSON.parse(JSON.stringify(DEFAULT_BACKGROUND.watermarkText[1]));
            }
            secondWatermark.text = value;
        }
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private onBlur = (): void => {
        //
    }

    private setEditorBackgroundFromCore(): void {
        const editorBackground = this.props.documentCore.getEditorBackground();
        if (editorBackground != null) {
            this.watermarkProperty.watermarkEnabled = editorBackground.watermarkEnabled;
            this.watermarkProperty.watermarkType = editorBackground.watermarkType;
            this.watermarkProperty.watermarkText = editorBackground.watermarkText;
            this.watermarkProperty.fillColorEnabled = editorBackground.fillColorEnabled;
            this.watermarkProperty.fillColorType = editorBackground.fillColorType;
            this.watermarkProperty.fillTextColor = editorBackground.fillTextColor;
            if (this.watermarkProperty.watermarkText.length === 1) {
                this.watermarkProperty.watermarkText.push({text: '', size: WATERMARK_DEFAULT_FONTSIZE + ''});
            }

            // ui
            this.watermarkValue = editorBackground.watermarkType;
            this.fillColorValue = editorBackground.fillColorType;
        }
    }

}
