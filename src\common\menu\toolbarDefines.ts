import { IParaProperty } from '../../model/ParaProperty';
import { AlignType, LineSpacingType } from '../commonDefines';

export interface IMenuSelectResult {
    items: IPropKey[];
    font: any;
    paraProps: IParaProperty;
}

export interface IParaSpacing {
    paraSpacing?: number;
    paraSpacingType?: number;
}


export enum MenuItemIndex {
    Undo = 'undo',
    Redo = 'redo',
    ClearFormats = 'clearFormats',
    Cut = 'cut',
    Copy = 'copy',
    Paste = 'paste',
    LeftAlign = 'leftAlign',
    CenterAlign = 'centerAlign',
    RightAlign = 'rightAlign',
    JustifyAlign = 'justifyAlign',
    LeftIndent = 'leftIndent',
    RightIndent = 'rightIndent',
    FontSize = 'fontSize',
    FontFamily = 'fontFamily',
    Color = 'color',
    BackgroundColor = 'backgroundColor',
    Bold = 'bold',
    Underline = 'underline',
    Italic = 'italic',
    Sub = 'subScript',
    Sup = 'superScript',
    List = 'list',
    LineHeight = 'lineHeight',
    File = 'file',
    Table = 'table',
    Image = 'image',
    Equation = 'equation',
    Search = 'search',
    Print = 'doPrint',
    Export = 'export',
    Scale = 'scale',
    Numbered = 'numbered',
    Bullet = 'bullet',
    AutoFormat = 'autoFormat',
    SpecialCharacter = 'specialCharacter',
    BreakPage = 'breakPage',
    AICheck = 'aiCheck',
}

export interface IMenuItem {
    name?: string;
    index?: string;
    hide?: boolean;
    disabled?: boolean;
    selected?: boolean;
    content?: any;
    value?: any;
    icon?: string;
    tip?: string;
    source?: any; // 绑定一些其他内容
}

export interface IPropKey {
    name: string;
    value: any;
    disabled?: string;
}

export let menus: IMenuItem[] = [
    {
        name: '撤销',
        index: MenuItemIndex.Undo,
        disabled: false,
        icon: 'hz-undo1',
        tip: '撤销(ctrl+z)',
    },
    {
        name: '重做',
        index: MenuItemIndex.Redo,
        disabled: false,
        icon: 'hz-redo1',
        tip: '重做(ctrl+y)',
    },
    {
        name: '清除格式',
        index: MenuItemIndex.ClearFormats,
        disabled: true,
        hide: true,
        icon: 'hz-clear',
    },
    {
        name: '分割线',
        index: 'separator',
        disabled: true,
    },
    {
        name: '剪切',
        index: MenuItemIndex.Cut,
        icon: 'hz-content_cut',
        tip: '剪切(ctrl+x)'
    },
    {
        name: '复制',
        index: MenuItemIndex.Copy,
        icon: 'hz-content_copy',
        tip: '复制(ctrl+c)'
    },
    {
        name: '粘贴',
        index: MenuItemIndex.Paste,
        icon: 'hz-content_paste',
        tip: '粘贴(ctrl+v)'
    },
    {
        name: '分割线',
        index: 'separator',
        disabled: true,
    },
    {
        name: '数字符号',
        index: MenuItemIndex.Numbered,
        icon: 'hz-listnumber',
        tip: '编号'
    },
    {
        name: '项目编号',
        index: MenuItemIndex.Bullet,
        icon: 'hz-listbulleted',
        tip: '项目符号'
    },
    // {
    //     name: '对齐',
    //     index: 'align',
    //     selected: true,
    //     content: '左对齐',
    //     icon: 'editorAlignLeft',
    //     tip: '段落对齐'
    // },
    {
        name: '左对齐',
        index: MenuItemIndex.LeftAlign,
        value: AlignType.Left,
        icon: 'hz-align-left',
        tip: '左对齐',
    },
    {
        name: '居中对齐',
        index: MenuItemIndex.CenterAlign,
        value: AlignType.Center,
        icon: 'hz-align-center',
        tip: '居中对齐',
    },
    {
        name: '右对齐',
        index: MenuItemIndex.RightAlign,
        value: AlignType.Right,
        icon: 'hz-align-right',
        tip: '右对齐',
    },
    {
        name: '两端对齐',
        index: MenuItemIndex.JustifyAlign,
        value: AlignType.Justify,
        icon: 'hz-align_justify',
        tip: '两端对齐',
    },
    {
        name: '左缩进',
        index: MenuItemIndex.LeftIndent,
        disabled: false,
        icon: 'hz-indent_dec',
        tip: '左缩进',
    },
    {
        name: '右缩进',
        index: MenuItemIndex.RightIndent,
        disabled: false,
        icon: 'hz-indent_inc',
        tip: '右缩进'
    },
    {
        name: '分割线',
        index: 'separator',
        disabled: true,
    },
    {
        name: '行高',
        index: MenuItemIndex.LineHeight,
        selected: true,
        icon: 'hz-linespacing',
        tip: '行高'
    },
    {
        name: '分割线',
        index: 'separator',
        disabled: true,
    },
    {
        name: '字体',
        index: MenuItemIndex.FontFamily,
        selected: true,
        content: '宋体',
        tip: '字体'
    },
    {
        name: '字号',
        index: MenuItemIndex.FontSize,
        selected: true,
        content: '14',
        tip: '字号'
    },
    {
        name: '颜色',
        index: MenuItemIndex.Color,
        selected: true,
        icon: 'hz-textcolor',
        tip: '字体颜色'
    },
    {
        name: '背景色',
        index: MenuItemIndex.BackgroundColor,
        selected: true,
        icon: 'hz-colorfill',
        tip: '填充背景色'
    },
    {
        name: '粗体',
        index: MenuItemIndex.Bold,
        value: 1,
        icon: 'hz-bold',
        tip: '粗体'
    },
    {
        name: '下划线',
        index: MenuItemIndex.Underline,
        value: 1,
        icon: 'hz-format_underlined',
        tip: '下划线'
    },
    {
        name: '斜体',
        index: MenuItemIndex.Italic,
        value: 1,
        icon: 'hz-italic',
        tip: '斜体'
    },
    {
        name: '分割线',
        index: 'separator',
        disabled: true,
    },
    {
        name: '下标',
        index: MenuItemIndex.Sub,
        value: true,
        icon: 'hz-subscript',
        tip: '下标'
    },
    {
        name: '上标',
        index: MenuItemIndex.Sup,
        value: true,
        icon: 'hz-superscript',
        tip: '上标'
    },
    {
        name: '分割线',
        index: 'separator',
        disabled: true,
    },
    {
        name: '列表',
        index: MenuItemIndex.List,
        disabled: true,
        selected: true,
        hide: true,
    },
    {
        name: '插入文件',
        index: MenuItemIndex.File,
        disabled: true,
        hide: true,
    },
    {
        name: '表格',
        index: MenuItemIndex.Table,
        hide: true,
    },
    {
        name: '缩放',
        index: MenuItemIndex.Scale,
        icon: 'hz-zoom_in',
        tip: '视图缩放'
    },
    {
        name: '分割线',
        index: 'separator',
        disabled: true,
    },
    {
        name: '图片',
        index: MenuItemIndex.Image,
        selected: false,
        icon: 'hz-crop_original',
        tip: '插入图片'
    },
    {
        name: '表达式',
        index: MenuItemIndex.Equation,
        disabled: true,
        hide: true,
        icon: 'editorFormula',
    },
    {
        name: '分割线',
        index: 'separator',
        disabled: true,
    },
    // {
    //     name: '格式整理',
    //     index: MenuItemIndex.AutoFormat,
    //     icon: 'hz-spellcheck',
    //     tip: '格式整理'
    // },
     {
        name: 'AI面板',
        index: MenuItemIndex.AICheck,
        icon: 'hz-ai',
        tip: 'AI面板'
    },
    {
        name: '分割线',
        index: 'separator',
        disabled: true,
    },
    {
        name: '特殊字符',
        index: MenuItemIndex.SpecialCharacter,
        icon: 'hz-specialfont',
        tip: '插入特殊字符'
    },
    {
        name: '分页符',
        index: MenuItemIndex.BreakPage,
        icon: 'hz-pagebreak',
        tip: '插入分页符'
    },
    {
        name: '分割线',
        index: 'separator',
        disabled: true,
    },
    {
        name: '搜索',
        index: MenuItemIndex.Search,
        disabled: true,
        hide: true,
    },
    {
        name: '打印',
        index: MenuItemIndex.Print,
        icon: 'hz-printer',
        tip: '打印'
    },
    {
        name: '导出',
        index: MenuItemIndex.Export,
        hide: true,
    },
];

export let alignMenus: IMenuItem[] = [
    {
        name: '居中',
        index: MenuItemIndex.CenterAlign,
        value: 1,
        icon: 'editorAlignCenter',
    },
    {
        name: '左对齐',
        index: MenuItemIndex.LeftAlign,
        value: 0,
        icon: 'editorAlignLeft',
    },
    {
        name: '右对齐',
        index: MenuItemIndex.RightAlign,
        value: 2,
        icon: 'editorAlignRight',
    },
    {
        name: '两端对齐',
        index: MenuItemIndex.JustifyAlign,
        value: 3,
        icon: 'editorAlignBetween',
    },
];

export let lineHeights: IMenuItem[] = [
    {
        name: '1',
        value: LineSpacingType.Single,
    },
    {
        name: '1.5',
        value: LineSpacingType.SingeHalf,
    },
    {
        name: '2',
        value: LineSpacingType.Double,
    },
];

export let toolbarImages: IMenuItem[] = [
    {
        name: '医学图片',
        value: 1,
    },
    {
        name: '本地图片',
        value: 2,
    },
];
