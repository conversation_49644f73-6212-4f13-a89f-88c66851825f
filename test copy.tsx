import EmrEditor from './index';
import './style.css';

class TestModel {
    private _data: Array<{name: string, id: number}>;
    private _activeId: number;
    private _id: number;
    private _titleDom: HTMLElement;
    private _contentDom: HTMLElement;
    private _addDom: HTMLElement;
    private _editors: any;

    constructor() {
        this._init();
    }

    public deleteItem(id: number): void {
        const datas = this._data;
        const index = datas.findIndex((item) => item.id === id);
        if (index === -1) {
            return;
        }

        const editor = this._editors[id].getEditor();
        if (editor) {
            this._editors[id] = null;
            editor.close();
        }

        datas.splice(index, 1);

        // let dom = this._contentDom.querySelector('#editor-content-' + id);
        // if (dom) {
        //     dom.remove();
        // }
        let dom = this._titleDom.querySelector('#editor-title-' + id);
        if (dom) {
            dom.remove();
        }
    }

    public addItem(): void {
        const id = ++this._id;
        const item = {name: '选项卡' + id, id};
        this._data.push(item);
        this._addTitleDom(item);
        this._addContentDom(item);
    }

    private _initDom(): void {
        const dom = document.createElement('div');
        dom.className = 'editor-test-demo';
        dom.innerHTML = `
            <style>
                .editor-test-header {
                    position: fixed;
                    left: 30px;
                    top: 70px;
                    z-index: 129;
                }
                .editor-test-header > ul > li {
                    position: relative;
                    float: left;
                    padding: 0 5px;
                    margin-right: 10px;
                    margin-bottom: 20px;
                    font-size: 13px;
                    line-height: 26px;
                    cursor: pointer;
                }
                .editor-test-header > ul > li > label {
                    position: absolute;
                    bottom: -30px;
                    left: 48%;
                    cursor: pointer;
                }
                .editor-test-header > ul > li.active, .editor-test-header > ul > li:hover {
                    font-weight: bold;
                    color: #f00;
                }

                .editor-test-content {
                    position: relative;
                    height: 700px;
                }

                .editor-test-content > div {
                    display: none;
                }

                .editor-test-content > div.active {
                    display: block;
                    width: 100%;
                    height: 100%;
                }

                .editor-test-add {
                    position: fixed;
                    left: 30px;
                    bottom: 70px;
                    z-index: 129;
                    color: #f00;
                    cursor: pointer;
                }
            </style>
            <div class="editor-test-header">
                <ul></ul>
            </div>
            <div class="editor-test-content"></div>
            <div class="editor-test-add">新增选项卡</div>
        `;
        document.body.appendChild(dom);
        this._titleDom = dom.querySelector('.editor-test-header > ul');
        this._contentDom = dom.querySelector('.editor-test-content');
        this._addDom = dom.querySelector('.editor-test-add');
    }

    private _init(): void {
        this._initDom();
        this._editors = {};
        const data = this._data = [];
        for (let i = 0; i < 3; i++) {
            const item = {name: '选项卡' + i, id: i};
            data.push(item);
            this._id = i;
            this._addTitleDom(item);
            this._addContentDom(item);
        }
        this._addEvent();
        this._tabClick(0);
    }

    private _addTitleDom(item: any): void {
        const dom = document.createElement('li');
        dom.id = 'editor-title-' + item.id;
        dom.innerHTML = `<span data-title="${item.id}">${item.name}</span><label data-delete="${item.id}">X</label>`;
        this._titleDom.appendChild(dom);
    }

    private _addContentDom(item: any): void {
        // const dom = document.createElement('div');
        // dom.innerText = item.name;
        // dom.className = 'editor-content';
        // dom.id = 'editor-content-' + item.id;
        // this._contentDom.appendChild(dom);
        // const editor = new EmrEditor({id: item.id, dom, isTest: true})
        // .setEvent({
        //     nsoKeyPressedEvent: (a, b, c, d) => {
        //         console.log(a, b, c, d);
        //         return true;
        //     },
        //     nsoFileOpenCompleted: (path) => {
        //         console.log(path);
        //     },
        //     nsoStructClick: (name) => {
        //         console.log(name);
        //     },
        //     nsoStructDBClick: (name) => {
        //         console.log(name);
        //     },
        //     nsoStructGainFocus: (name, type) => {
        //         console.log(name, type);
        //     },
        //     nsoStructChanged: (name) => {
        //         console.log(name);
        //     },
        //     nsoRegionGainFocus: (name, type) => {
        //         console.log(name, type, 'nsoRegionGainFocus');
        //     },
        //     nsoRegionLostFocus: (name, type) => {
        //         console.log(name, type, 'nsoRegionLostFocus');
        //     },
        //     nsoRegionChanged: (name, type) => {
        //         console.log(name, type, 'nsoRegionChanged');
        //     },
        //     nsoRegionDBClick: (name, type) => {
        //         console.log(name, type, 'nsoRegionDBClick');
        //     },
        //     nsoStructCheckChanged: (name, type) => {
        //         console.log(name, type, 'nsoStructCheckChanged');
        //     },
        //     nsoFileModifyChanged: (flag) => {
        //         console.log(flag, 'nsoFileModifyChanged');
        //     },
        // })
        // .setEvent({
        //     nsoStructLostFocus: (name) => {
        //         console.log(name, '结构化元素焦点离开');
        //         console.log(editor.removeEvent(['nsoStructLostFocus']))
        //     },
        // });

        // this._editors[item.id] = editor;
        // window['editor' + item.id] = editor.getEditor();
        // console.log(editor)
    }

    private _tabClick(id: number): void {
        if (this._activeId === id) {
            return;
        }
        let activeDom = this._titleDom.querySelector('li.active');
        if (activeDom) {
            activeDom.className = '';
        }
        const titleDom = this._titleDom.querySelector('#editor-title-' + id);
        if (titleDom) {
            titleDom.className = 'active';
        }
        activeDom = this._contentDom.querySelector('.editor-content.active');
        if (activeDom) {
            activeDom.className = activeDom.className.replace(' active', '');
        }
        const contentDom = this._contentDom.querySelector('#editor-content-' + id);
        if (contentDom) {
            contentDom.className += ' active';
        }

        this._activeId = id;
    }

    private _addEvent(): void {
        this._titleDom.addEventListener('click', (e) => {
            const target = e.target as HTMLElement;
            let id = target.getAttribute('data-title');
            if (id) {
                this._tabClick(+id);
                return;
            }
            id = target.getAttribute('data-delete');
            if (id) {
                this.deleteItem(+id);
                return;
            }
        });

        this._addDom.addEventListener('click', (e) => {
            this.addItem();
        });
    }
}

window.onload = () => {
    const abc = new TestModel();
};
