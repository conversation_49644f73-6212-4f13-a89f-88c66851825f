import ParaPortion from './core/Paragraph/ParaPortion';
import ParaLine from './core/Paragraph/ParaLine';
import { IDcoumentPortionBackgroundColor, IDcoumentPortionTextDecoration, ICommentData } from './ParaPortionProperty';
import { DocumentCore } from './DocumentCore';

export interface IDocumentParagraph {
    id?: number;
    index: number;
    content: ParaPortion[];
    lines: ParaLine[];
    pageIndex: number;
    startLine: number;
    endLine: number;
    scale?: number;
    commentData?: ICommentData[];
    backgroundColor?: IDcoumentPortionBackgroundColor[];
    textDecoration?: IDcoumentPortionTextDecoration[];
    documentCore?: DocumentCore;
    cellId?: any;
    points?: any[];
    bTextBorderRed?: boolean;
    yEnd?: number;
    yStart?: number;
    lastLineWidth?: number;
    images?: any[];
    option?: any;
    nHeaderFooter?: number;
    numbering?: IRenderNumbered;
    dynamicGridLine?: IDcoumentPortionTextDecoration[];
}

export interface IRenderNumbered {
    width: number;
    height?: number;
    textPr: any;
    text: string;
    x: number;
    y: number;
}

export interface IParaProperty {
    alignment?: number; // 对齐方式
    bPageBreakBefore?: boolean; // 段前分页符
    paraLeftInd?: number;
    paraInd?: number;  // 段落缩进
    paraIndType?: number; //
    paraSpacing?: number;  // 段落间距
    paraSpacingType?: number; //
    bWordWrap?: boolean;
}
