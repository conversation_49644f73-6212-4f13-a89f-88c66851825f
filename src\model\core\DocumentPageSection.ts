import DocumentFrameBounds from './FrameBounds';

/**
 * 页面区域
 */
export class DocumentPageSection {
    public pos: number = 0;
    public endPos: number = -1;
    public empty: boolean = true;

    public x: number = 0;
    public y: number = 0;
    public xLimit: number = 0;
    public yLimit: number = 0;
    public yLimit2: number = 0;

    public bounds: DocumentFrameBounds = new DocumentFrameBounds(0, 0, 0, 0);

    // columns: []; // 每个section的排版列数，默认页面都是一列排版
    // columnsSep: boolean = false;

    public iterationsCount: number = 0;
    public currentY: number = 0;
    public bCanRecalculateBottomLine: boolean = true;

    constructor() {
        //
    }

    // 是否绘制section的边框
    public isCalculatingSectionBottomLine(): boolean {
        // 已经计算过边框，且可以计算边框线
        if (this.iterationsCount > 0 && true === this.bCanRecalculateBottomLine) {
            return true;
        }

        return false;
    }

    public calculateBottomLine(): void {
        this.iterationsCount++;
    }
}
