import { XmlComponent } from '../xml-components';
import { MediaImage, EditableMediaImage, AudioImage, VideoImage } from './images/MediaImage';

export class Images extends XmlComponent {

  constructor() {
      super('Images');
  }

  public addImage(image: MediaImage): Images {
    this.root.push(image);
    return this;
  }

}

export class Audios extends XmlComponent {
    constructor() {
        super('Audios');
    }

    public addAudio(image: AudioImage): Audios {
        this.root.push(image);
        return this;
    }
}

// tslint:disable-next-line: max-classes-per-file
export class Videos extends XmlComponent {
    constructor() {
        super('Videos');
    }
    public addVideo(image: VideoImage): Videos {
        this.root.push(image);
        return this;
    }
}

// tslint:disable-next-line: max-classes-per-file
export class Paintings extends XmlComponent {

  constructor() {
      super('Paintings');
  }

  public addEditableImage(image: EditableMediaImage): Paintings {
    this.root.push(image);
    return this;
  }

}
