/* IFTRUE_NOWATER */
import wasmUnlimited from './editor-Wasm-unlimited';
/* FITRUE_NOWATER */
/* IFTRUE_WATER */
import wasmWater from './editor-Wasm-water';
/* FITRUE_WATER */

class WasmInstanceEx {
    private _wasm: any;
    private _callback: any;
    private _bSuccess: boolean;
    // 添加静态初始化锁
    private static _initializingPromise: Promise<any> | null = null;
    
    constructor() {
        /* IFTRUE_NOWATER */
        if (true) {
            wasmUnlimited.onRuntimeInitialized = () => {
                this.setWasm(wasmUnlimited);
                this._bSuccess = true;
                if (this._callback) {
                    this._callback();
                }
            };
        }
        /* FITRUE_NOWATER */
        /* IFTRUE_WATER */
        if (true) {
            wasmWater.onRuntimeInitialized = () => {
                this.setWasm(wasmWater);
                this._bSuccess = true;
                if (this._callback) {
                    this._callback();
                }
            };
        }
        /* FITRUE_WATER */
    }

    public get instance(): any {
        return this._wasm;
    }

    // 添加状态检查方法
    public isWasmReady(): boolean {
        return this._bSuccess === true && this._wasm != null;
    }
    
    // 优化初始化方法
    public createWasmInstsanceAsync(): Promise<any> {
        // 如果已经成功初始化，直接返回
        if (this.isWasmReady()) {
            return Promise.resolve(this);
        }
        
        // 如果正在初始化中，返回现有的promise
        if (WasmInstanceEx._initializingPromise) {
            return WasmInstanceEx._initializingPromise;
        }
        
        // 创建新的初始化promise
        WasmInstanceEx._initializingPromise = new Promise((resolve, reject) => {
            this._callback = () => {
                WasmInstanceEx._initializingPromise = null; // 清除初始化锁
                resolve(this);
            };
        });
        
        return WasmInstanceEx._initializingPromise;
    }

    public arrayToHeap(typedArray: any): any {
        const numBytes = typedArray.length * typedArray.BYTES_PER_ELEMENT;
        const ptr = this._wasm._malloc(numBytes);
        const heapBytes = new Uint8Array(this._wasm.HEAPU8.buffer, ptr, numBytes);
        heapBytes.set(new Uint8Array(typedArray.buffer));
        return heapBytes;
    }

    public setUrl(url: string): void {
        this._wasm.ccall('SetRemoteUrl', 'null', ['string'], [url]);
    }

    public freeArray(heapBytes: any): any {
        this._wasm._free(heapBytes.byteOffset);
    }

    /* IFTRUE_WATER */
    public paragraphRecalculateRecalculateLinePositionWithInfo(a: number, b: number, c: number, d: number): string {
        return this._wasm.ccall('ParagraphRecalculate_recalculateLinePositionWithInfo',
                    'string', ['number', 'number', 'number', 'number'], [a, b, c, d]);
    }

    public paragraphRecalculateRecalculateLineBottomBound2WithInfo(
        a: number, b: number, c: number, d: number, e: number, f: number, g: number,
        h: number, j: number, k: number, l: number, m: number, o: number, p: number): string {
        return this._wasm.ccall('ParagraphRecalculate_recalculateLineBottomBound2WithInfo',
                    'string', ['number', 'number', 'number', 'number', 'number', 'number', 'number',
                    'number', 'number', 'number', 'number', 'number', 'number', 'number'],
                    [a, b, c, d, e, f, g, h, j, k, l, m, o, p]);
    }

    public paragraphRecalculateRecalculateLineAlign2WithInfo(
        a: number, b: number, c: number, d: number, e: number): string {
        return this._wasm.ccall('ParagraphRecalculate_recalculateLineAlign2WithInfo',
                    'string', ['number', 'number', 'number', 'number', 'number'], [a, b, c, d, e]);
    }

    public paragraphRecalculateRecalculateFastWholeParagraphWithInfo(
        a: number, b: number, c: number, d: number, e: number, f: number): string {
        return this._wasm.ccall('ParagraphRecalculate_recalculateFastWholeParagraphWithInfo',
                'string', ['number', 'number', 'number', 'number', 'number', 'number'], [a, b, c, d, e, f]);
    }

    public paragraphRecalculateRecalculateFastWholeParagraph2WithInfo(
        a: number, b: number, c: number): string {
        return this._wasm.ccall('ParagraphRecalculate_recalculateFastWholeParagraph2WithInfo',
            'string', ['number', 'number', 'number'], [a, b, c]);
    }

    public paragraphRecalculateRecalculateFastWholeParagraph3WithInfo(
        a: number, b: number, c: number, d: number): string {
        return this._wasm.ccall('ParagraphRecalculate_recalculateFastWholeParagraph3WithInfo',
            'string', ['number', 'number', 'number', 'number'], [a, b, c, d]);
    }

    public paragraphRecalculateRecalculateFastWholeParagraph4WithInfo(
        a: number, b: number, c: number): string {
        return this._wasm.ccall('ParagraphRecalculate_recalculateFastWholeParagraph4WithInfo',
            'string', ['number', 'number', 'number'], [a, b, c]);
    }

    public paragraphRecalculateRecalculateFastRangeWithInfo(
        a: number, b: number, c: number, d: number, e: number): string {
        return this._wasm.ccall('ParagraphRecalculate_recalculateFastRangeWithInfo',
            'string', ['number', 'number', 'number', 'number', 'number'], [a, b, c, d, e]);
    }
    /* FITRUE_WATER */

    private setWasm(instance: any): void {
        this._wasm = instance;
        // For debug purpose only!
        // window['wasm'] = instance;
    }
}

// tslint:disable-next-line: sy-global-const-name
export const WasmInstance = new WasmInstanceEx();

// export function _arrayToHeap(typedArray: any): any {
//     const numBytes = typedArray.length * typedArray.BYTES_PER_ELEMENT;
//     const ptr = wasm._malloc(numBytes);
//     const heapBytes = new Uint8Array(this._wasm.HEAPU8.buffer, ptr, numBytes);
//     heapBytes.set(new Uint8Array(typedArray.buffer));
//     return heapBytes;
// }

// export function _freeArray(heapBytes: any): any {
//     wasm._free(heapBytes.byteOffset);
// }
