import * as React from 'react';
import {DocumentUI} from './canvas/Document';
import ReactDOM from 'react-dom/client';

import PageContainer from './module/PageContainer';
import { DocumentCore } from '../../model/DocumentCore';
import ExternalEvent from '../../common/external/Event';
import IExternalInterface, { IExternalEvent } from '../../common/external/IExternalInterface';
import IAsyncExternalInterface from '../../common/external/IAsyncExternalInterface';
import { ExternalInterface } from '../../common/external';
import { logger } from '../../common/log/Logger';

import {
    GlobalEvent as gEvent,
    GlobalEventName as gEventName,
} from '../../common/GlobalEvent';
import { IFRAME_MANAGER } from '../../common/IframeManager';
import { idCounter } from '../../model/core/util';
import './style/message.less';
import './style/warpper.less';
import { SDKcontainer } from './module/SDKcontainer';
import { CleanModeType, EditorHeader, ICopyProperty, TEXTCOLOR_CHANGEINREVISION } from '../../common/commonDefines';
import { ExportHtml } from './module/ExportHtml';
import { ToolbarAction } from '../../common/menu/ToolbarAction';
// import {EquationRefs} from '../../common/MedEquation';
// import { EquationType } from '../../common/commonDefines';

const writeClose = (): void => {
    // tslint:disable-next-line: no-console
    console.log('编辑器已经关闭');
};

interface IProps {
    id?: string;
    isTest?: boolean;
    bShowMenu?: boolean;
    bNeedCustomFont?:boolean;
    bShowToolbar?: boolean;
    win?: Window;
    height?: number;
    iframe?: any;
    bEnableScroll?: boolean;
    bScrollX?: boolean;
    bScrollY?: boolean;
    close?: any;
    textColorChangeInRevision?: number;
}

interface IEmrState {
    bReflesh: boolean;
    documentCore: DocumentCore;
}
let resizeDom;
let editorIndex = 0;
export class EmrEditorUI extends React.Component<IProps, IEmrState> {
    public externalEvent: ExternalEvent;
    public id: string;
    public docId: number;
    public myRef: any;
    public bPrint: boolean;
    public contentRef: any;
    public containerRef: any;
    public clientWidth: number;
    public copyOption: ICopyProperty;
    private documentCore: DocumentCore;
    private _external: IExternalInterface;
    private _asyncExternal: IAsyncExternalInterface;
    private height: number;
    private visible: boolean;
    private _document: any;
    private _height: number;
    // 事件监听器
    private _fileEnabled: boolean;
    private _structEnabled: boolean;
    private _keyEnabled: boolean;
    private _unResizeCursor: boolean;
    private _bFirstVisible: boolean;
    private _runTest: any;
    private _bFocus: boolean;
    private _bFromInterfaces: boolean;
    private _rightMenus: any;
    private bHideMenu: boolean;
    private bHideToolbar: boolean;

    private menuAction: ToolbarAction;
    private bResetMenuDatas: boolean; // 是否重置所有菜单的勾选项（打开新文档）

    constructor(props: IProps) {
        super(props);
        let bText = props.isTest;
        let bHideMenu = !props.bShowMenu;
        let bNeedCustomFont = props.bNeedCustomFont;
        let bHideToolbar = !props.bShowToolbar;
        let height: number = EditorHeader.Total;
        this.id = props.id || 'editor-id';
        const docId = idCounter.getNewId();
        IFRAME_MANAGER.addIframe(docId, props.iframe);

        TEXTCOLOR_CHANGEINREVISION.value = [1, 2].includes(props.textColorChangeInRevision) ?
                                        props.textColorChangeInRevision : 1;
        if (props.id) {
            if (props.iframe.offsetParent) {
                this._bFirstVisible = true;
            }
            if (props.isTest === undefined || props.isTest === false) {
                bText = false;
            }
            if (props.bShowMenu !== true) {
                bHideMenu = true;
                height -= EditorHeader.Menu;
            }
            if (props.bShowToolbar !== true) {
                bHideToolbar = true;
                height -= EditorHeader.Toolbar;
            }
            // this._document = props.win.document;
            this._height = props.height || props.win.document.firstElementChild.clientHeight || 700;
            this.clientWidth = props.win.document.firstElementChild.clientWidth || 1000;
            // console.dir(props.win.document)
            // this.height = this._height - height;
            // console.dir(this._document)
            // console.log(this._document.firstElementChild.clientHeight)
            // console.log(props.win.document.firstElementChild.clientHeight,
            // props.win.document.firstElementChild.clientWidth)
        } else {
            // 服务端使用
            window['__EmrEditorComponent__'] = this;
            this._height = undefined;
            // this._document = document;
            // this._height = document.body.
            // this.height = document.firstElementChild.clientHeight - height;
            // this.clientWidth = document.body.clientWidth;
            if (props.bShowMenu !== true) {
                bHideMenu = true;
                height -= EditorHeader.Menu;
            }
            if (props.bShowToolbar !== true) {
                bHideToolbar = true;
                height -= EditorHeader.Toolbar;
            }
        }
        this.height = height;
        this.documentCore = new DocumentCore(undefined, bText, docId);
        this.state = {
            bReflesh: false,
            documentCore: this.documentCore,
        };
        this.bHideMenu = bHideMenu;
        this.bHideToolbar = bHideToolbar;
        this.docId = docId;
        this.myRef = React.createRef();
        this.contentRef = React.createRef();
        this.containerRef = React.createRef();
        logger.setId(this.id, this.docId);
        this._fileEnabled = true;
        this._structEnabled = true;
        this._keyEnabled = true;
        this.copyOption = {};
        // console.dir(new EquationRefs().getEquationByType(EquationType.LightPositioningMap));
    }

    public render(): any {
        if (this.visible === false) {
            return null;
        }
        let className = 'hz-editor-container';
        const props = this.props;
        if (props.id !== undefined) {
            className += ' hz-editor-from-iframe';
        }
        if (props.bEnableScroll === false) {
            className += ' scroll-not';
        }
        if (props.bScrollX === false) {
            className += ' scroll-not-x';
        }
        if (props.bScrollY === false) {
            className += ' scroll-not-y';
        }
        // const {pageProperty} = this.documentCore.render();
        // const width = this.id === 'editor-id' ? 'auto' : pageProperty.width;
        return (
            <div className={className} ref={this.myRef}>
                <PageContainer
                    host={this as any}
                    bHideToolbar={this.bHideToolbar}
                    bHideMenu={this.bHideMenu}
                    ref={this.containerRef}
                    bIframe={!!this.props.id}
                    isResetMenuDatas={this.isResetMenuDatas}
                />
                <DocumentUI
                    host={this}
                    ref={this.contentRef}
                    height={this._height}
                    headerHeight={this.height}
                    iframe={this.props.iframe}
                    unResizeCursor={this._unResizeCursor}
                />
            </div>
        );
    }

    public componentDidMount(): void {
        if (process.env.NODE_ENV === 'development') {
            window['utilEditor'] = this;
        }
        // this.menuAction = new ToolbarAction(this);
        // let index: string = '';
        // if (editorIndex > 0) {
        //     index = this.docId.toString();
        // }
        // window['editor' + index] = this.getEditor();
        // // this.addResizeDom();
        // this.addWindowErrorEvent();
        // editorIndex++;
        // window['that'] = this;
        // gEvent.addEvent(this.docId, gEventName.Blur, this.blur);
        // gEvent.addEvent(this.docId, gEventName.RightMenuVisible, this.setRightMenuVisible);
    }

    public componentWillUnmount(): void {
        // resizeDom.removeEventListener('resize', this.onResize);
        IFRAME_MANAGER.deleteIframe(this.docId);
        gEvent.deleteEvent(this.docId, gEventName.Blur, this.blur);
        gEvent.deleteEvent(this.docId, gEventName.RightMenuVisible, this.setRightMenuVisible);
        if (this.props.id) {
            SDKcontainer.removeEditor(this.docId);
        }
        this.removeWindowErrorEvent();
    }

    public setCopyOptions(options: ICopyProperty): void {
        if (!options) {
            return;
        }

        const keys = Object.keys(options);
        const current = this.copyOption;
        let bChanged = false;
        keys.forEach((key) => {
            if (options[key] !== undefined && options[key] !== current[key]) {
                current[key] = options[key];
                bChanged = true;
            }
       });
    }

    public componentDidUpdate(): void {
        this.bResetMenuDatas = false;
    }

    /**
     * 重置Menu菜单的所有勾选项，恢复为默认的选项
     */
    public resetMenuDatas(): void {
        this.bResetMenuDatas = true;
    }

    public isResetMenuDatas = (): boolean => {
        return this.bResetMenuDatas;
    }

    public onFocus(flag: boolean): boolean {
        if (this._bFromInterfaces) {
            this._bFromInterfaces = false;
            return false;
        }
        // if (flag === this._bFocus) {
        //     return true;
        // }
        // this._bFocus = flag;
        return this.contentRef.current.setCursorTrueVisible(flag);
    }

    public addTabindex(): void {
        this.contentRef.current.addTabindex();
    }

    public openCompleted(): void {
        gEvent.setEvent(this.docId, gEventName.OpenCompleted);
        if (this.externalEvent) {
            this.externalEvent.nsoFileOpenCompleted(null);
        }
    }

    public setStopCursorBlur(flag: boolean = true): void {
        this._bFromInterfaces = flag;
    }

    public setCursorVisible( bVisible: boolean): void {
        this.contentRef.current.setCursorVisible(bVisible);
    }

    public visibleChange(bVisible: boolean): void {
        const bFirstVisible = this._bFirstVisible;
        gEvent.setEvent(this.docId, gEventName.VisibleChange, bVisible, bFirstVisible === undefined);
        if (bFirstVisible === undefined && bVisible) {
            this._bFirstVisible = true;
        }
    }

    public mainRefresh(): void {
        // 当前元素不存在时，不进行刷新否则容易出现异常
        if (!this.myRef.current) {
            return;
        }
        this.setState({}, () => {
            const current = this.contentRef.current;
            // current.clearContainer();
            current.handleRefresh();
        });
    }

    public handleRefresh(timeout?: number): void {
        // 当前元素不存在时，不进行刷新否则容易出现异常
        if (!this.myRef.current) {
            return;
        }
        // if (this.contentRef.current.dynamicModeRefresh() === true) {
        //     return ;
        // }

        if (timeout !== undefined) {
            setTimeout(() => {
                this.contentRef.current.handleRefresh();
            }, timeout);
        } else {
            if (this.contentRef.current != null) {
                this.contentRef.current.handleRefresh();
            } else {
                this.mainRefresh();
            }
        }

        // console.log(this.state.documentCore.getDocument());
    }

    public handleContainerRefresh(): void {
        // 当前元素不存在时，不进行刷新否则容易出现异常
        if (!this.myRef.current) {
            return;
        }
        this.containerRef.current.handleRefresh();
    }

    public initCursorPos(): void {
        if (this.contentRef.current != null) {
            this.contentRef.current.initCursorPos();
        }
    }

    public async convertToHTMLFromBuffer(buffer: ArrayBuffer, bCleanMode?: any): Promise<string> {
      const fileReader = new FileReader();
      const input = document.createElement('input');
      this.documentCore.createNewDocument(true);
      await this.menuAction.readFromBuffer(buffer, fileReader, input);
      await new Promise((resolve) => {
        this.setState({bReflesh: !this.state.bReflesh}, () => {
          resolve(null);
        });
      });
      // tslint:disable-next-line: no-console
      console.log('in main2 convertToHTMLFromBuffer');
      const html = new ExportHtml(this);
      return await html.exportAsString(bCleanMode);
    }

    public async exportHtml(): Promise< string > {
        const html = new ExportHtml(this);
        return await html.exportAsString(CleanModeType.CleanMode);
    }

    public getEditor(): IExternalInterface {
        if (this._external === undefined) {
            const external = new ExternalInterface(this);
            this._external = external.getEditor();

            // 后期会删除
            this._external['_addEditorEvent'] = () => {
                this.createExternalEvent();
            };

            // 后期会删除
            this._external['_runAutoTestScript'] = () => {
                this.createAutoTestEvent();
            };

            this._external['_getExtend'] = () => {
                const obj: any = this._external['_extend'] = {};
                obj.getPageLines = (pageIndex) => {
                    return this.documentCore.getPageLines(pageIndex);
                };
                return this._external;
            };
        }

        return this._external;
    }

    public getAsyncEditor(): IAsyncExternalInterface {
        if (this._asyncExternal === undefined) {
            const external = this.getEditor();
            const _asyncExternal = new Proxy(external, {
                get: function (target, prop, receiver) {
                    if (target[prop] instanceof Function) {
                        return async function (...args) {
                            return await target[prop].apply(target, args);
                        }
                    } else {
                        return Promise.resolve(target[prop]);
                    }
                }
            });
            Object.assign(this, {_asyncExternal});
        }
        return this._asyncExternal;
    }

    public setEditEvent(options: IExternalEvent): void {
        if (!options) {
            return;
        }
        if (this.externalEvent) {
            this.externalEvent.setEvents(options);
            return;
        }
        // this.externalEvent = new ExternalEvent(this, options);
        // if (this.contentRef.current) {
        //     this.contentRef.current.externalEvent = this.externalEvent;
        // }
    }

    public removeEvent(sNames?: string[]): boolean {
        if (this.externalEvent) {
            return this.externalEvent.removeEvent(sNames);
        }
        return false;
    }

    public resetCursor(): void {
        this.contentRef.current.resetCursor();
    }

    // public clearDom(): void {
    //     const editor = this._external;
    //     const keys = Object.keys(editor);
    //     keys.forEach((key) => {
    //         editor[key] = writeClose;
    //     });

    //     delete window['editor' + this.docId];
    //     if (this.myRef.current && this.myRef.current.parentNode) {
    //         ReactDOM.unmountComponentAtNode(this.myRef.current.parentNode);
    //         const iframe = this.props.iframe;
    //         if (iframe) {
    //             iframe.outerHTML = '';
    //         }
    //     }

    //     if (typeof this.props.close === 'function') {
    //         this.props.close();
    //     }
    // }

    public clearDom(): void {
        const editor = this._external;
        const keys = Object.keys(editor);
        keys.forEach((key) => {
            editor[key] = writeClose;
        });
    
        delete window['editor' + this.docId];
    
        const container = this.myRef.current?.parentNode as HTMLElement;
        if (container) {
            // 创建根实例并卸载组件
            const root = ReactDOM.createRoot(container);
            root.unmount(); // 卸载 React 组件
    
            // 清理 iframe
            const iframe = this.props.iframe;
            if (iframe) {
                iframe.outerHTML = '';
            }
        }
    
        if (typeof this.props.close === 'function') {
            this.props.close();
        }
    }

    public setSelections(): void {
        this.contentRef.current.setSelections();
    }

    public setScrollbarToPage(): void {
        //
    }

    public getRightMenus(): any {
        return this._rightMenus;
    }

    public testDocumentXml(timeout?: number, bInsertFile: boolean = false): void {
        // 当前元素不存在时，不进行刷新否则容易出现异常
        if (!this.myRef.current) {
            return;
        }
        if (timeout !== undefined) {
            setTimeout(() => {
                this.contentRef.current.handleRefresh();
            }, timeout);
        } else {
            if (this.contentRef.current != null) {
                this.contentRef.current.handleRefresh();
            } else {
                this.mainRefresh();
            }
        }
        // console.log(this.state.documentCore.getDocument());
    }

    public getDocumentCore(): DocumentCore {
        return this.documentCore;
    }

    public updateCursor(bComposition: boolean = false, bForceUpdate: boolean = false,
                        bResetCursor: boolean = false): boolean {
        // through ref, u can use anything from child component
        return this.contentRef.current.updateCursor(bComposition, bForceUpdate, bResetCursor);
    }

    public isSelected(): boolean {
        return this.contentRef.current.isSelected();
    }

    public getCopyPaste(): any {
        if (!this.contentRef.current) {
            return;
        }
        return this.contentRef.current.getCopyPaste();
    }

    public showToolbar(bVisible: boolean, name?: string): boolean {
        bVisible = !bVisible;
        if (bVisible === this.bHideToolbar) {
            return false;
        }
        this.bHideToolbar = bVisible;
        this.setHeight(this.bHideMenu, bVisible);
        this.mainRefresh();
        return true;
    }

    public showMenu(bVisible: boolean): boolean {
        bVisible = !bVisible;
        if (bVisible === this.bHideMenu) {
            return false;
        }
        this.bHideMenu = bVisible;
        this.setHeight(bVisible, this.bHideToolbar);
        this.mainRefresh();
        return true;
    }

    public isFileEnabled(): boolean {
        return this._fileEnabled;
    }

    public isKeyEnabled(): boolean {
        return this._keyEnabled;
    }

    public isStructEnabled(): boolean {
        return this._structEnabled;
    }

    public setFileEnabled(enabled: boolean): void {
        this._fileEnabled = enabled;
    }

    public setKeyEnabled(enabled: boolean): void {
        this._keyEnabled = enabled;
    }

    public setVisible(flag: boolean, iframe: HTMLIFrameElement): void {
        // if (iframe && flag === true) {
        //     IFRAME_WINDOW[this.docId] = iframe.contentWindow;
        // }
        if (flag === this.visible) {
            return;
        }
        this.visible = flag;
        if (flag === false) {
            gEvent.setEvent(this.docId, gEventName.UnMounted);
            if (this.props.id) {
                SDKcontainer.removeEditor(this.docId);
            }
        } else {
            this._unResizeCursor = true;
        }
        this.setState({}, () => {
            if (flag === true) {
                this.handleRefresh();
                if (this.externalEvent && this.contentRef.current) {
                    this.contentRef.current.externalEvent = this.externalEvent;
                }

                this._unResizeCursor = false;
                if (this.props.id) {
                    SDKcontainer.init(this.docId, this);
                }
            }
        });
    }

    public setStructEnabled(enabled: boolean): void {
        this._structEnabled = enabled;
    }

    public removeAllListen(): void {
        // this._fileEnabled = false;
        // this._structEnabled = false;
        // this._keyEnabled = false;
    }

    public async createAutoTestEvent(): Promise<any> {
        if (this._runTest) {
            return this._runTest;
        }
        const module = await import('../../common/AutoRunTest');
        const runTest = module.default;
        const test = new runTest(this);
        this._runTest = test;
        test.init(this._external);
        return this._runTest;
    }

    private setRightMenuVisible = (options: any): void => {
        let rightMenu = this._rightMenus;
        if (!rightMenu) {
            rightMenu = this._rightMenus = options;
            return;
        }

        if (!options.readonlyMode) {
            const readonlyMode = this._rightMenus.readonlyMode;
            this._rightMenus = options;
            if (readonlyMode) {
                this._rightMenus = readonlyMode;
            }
            return;
        }
        this._rightMenus = options;
    }

    private createExternalEvent(): void {
        const names: any = {};
        const keys = Object.keys(ExternalEvent.prototype);
        keys.forEach((key) => {
            names[key] = (...args) => {
                // tslint:disable-next-line: no-console
                console.log(key, ...args);
            };
        });

        this.setEditEvent(names);
    }

    private createAutoTest = () => {
        this.createAutoTestEvent();
    }

    private blur = (e: any): void => {
        this.onFocus(false);
    }

    private setHeight(bHideMenu: boolean, bHideToolbar: boolean): void {
        let height: number = EditorHeader.Total;
        if (bHideMenu === true) {
            height -= EditorHeader.Menu;
        }

        if (bHideToolbar === true) {
            height -= EditorHeader.Toolbar;
        }
        this.height = height;
    }

    private addResizeDom(): any {
        if (!resizeDom) {
            const dom = document.createElement('object');
            dom.setAttribute('style', `display: block; position: absolute; top: 0; left: 0; height: 100%;
                width: 100%; overflow: hidden;opacity: 0; pointer-events: none; z-index: -1;`);
            dom.type = 'text/html';
            dom.data = 'about:blank';
            document.body.appendChild(dom);
            if (!dom.contentDocument) {
                return;
            }
            resizeDom = dom.contentDocument.defaultView;
        }

        this.addResizeEvent();
    }

    private addResizeEvent(): void {
        resizeDom.addEventListener('resize', this.onResize);
    }

    private onResize = (e: any) => {
        gEvent.setEvent(this.docId, gEventName.Resize, e);
    }

    private addWindowErrorEvent = () => {
        this.myRef.current.ownerDocument.defaultView.addEventListener('error', this.windowError, true);
    }

    private removeWindowErrorEvent = () => {
        const myRef = this.myRef.current;
        if (myRef && myRef.ownerDocument && myRef.ownerDocument.defaultView) {
            myRef.ownerDocument.defaultView.removeEventListener('error', this.windowError);
        }
    }

    private windowError = (err: any): void => {
        logger.error({id: this.docId, description: `message: ${err.error.message};stack: ${err.error.stack};`});
    }
}
