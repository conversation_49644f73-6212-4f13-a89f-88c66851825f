import * as React from 'react';
import { EditModeType, EquationType, MenuItemIndex, NISTableCellType, NISTableMode, ResultType, ToolbarIndex,
    ViewModeType } from '../../../common/commonDefines';
import { EmrEditor } from '../Main';
import {GlobalEvent as gEvent, GlobalEventName as gEventName} from '../../../common/GlobalEvent';
import '../style/menu.less';
import { IFRAME_MANAGER } from '../../../common/IframeManager';
// import { incrementDB } from '../../../common/indexDB/IncrementDB';
import { message } from '../../../common/Message';
import { ExportHtml } from './ExportHtml';

import { getImageEditor } from '@hz-editor/plugins';
import { ToolbarAction } from '../../../common/menu/ToolbarAction2';
import { getDocumentCoreRecordReplayState } from '../../../model/DocumentCoreRecordReplay';
import { SPELL_CHECK } from '@/common/Spellcheck';
import OfdFileHandler from './modals/ofdhelper';

// import { measureChar } from '../../../model/core/util';

interface IBar {
    name: string;
    index: number;
    hide?: boolean;
    className?: string;
    children?: IBar[];
    disabled?: boolean;
    checked?: boolean;
    bSplit?: boolean;
    initChildCheck?: boolean;
    icon?: string;
}

let toolbars: IBar[];

interface IProps {
    host: EmrEditor;
    isResetMenuDatas?: () => boolean; // 是否重置所有菜单的勾选项（打开新文档）
}

interface IState {
    activeIndex: number;
    bRefresh: boolean;
}

export default class Menu extends React.Component<IProps, {}> { // 'Menu' actually
    private datas: IBar[];
    private checkedDatas: number[];
    private ulRef: any;
    private bItemClick: boolean;
    private host: EmrEditor;
    private timeout: any;
    private insertFileRef: any;
    private documentCore: any;
    private openFileRef: React.RefObject<HTMLInputElement>;
    private insertImageRef: React.RefObject<HTMLInputElement>;
    private insertVideoRef: React.RefObject<HTMLInputElement>;
    private insertAudioRef: React.RefObject<HTMLInputElement>;
    private zipToApoRef: React.RefObject<HTMLInputElement>;
    private apoToZipRef: React.RefObject<HTMLInputElement>;
    private openOldFileRef: React.RefObject<HTMLInputElement>;
    private viewModeObj: any;
    private editModeObj: any;
    private nisTableMode: any;
    private secondMenus: IBar[];
    private threeMenus: IBar[];
    private firstMenuActive: number;
    private secondMenuActive: number;
    private threeMenuPointX: number;
    private bDocClick: boolean;
    private menuAction: ToolbarAction;

    constructor(props: IProps) {
        super(props);
        this.host = this.props.host;
        if ( !NURSING_FEATURE ) {
            toolbars.splice(7, 1);
        }
        this.datas = JSON.parse(JSON.stringify(toolbars));
        this.documentCore = this.host.getDocumentCore();
        this.checkedDatas = [];
        this.ulRef = React.createRef();
        this.insertFileRef = React.createRef();
        this.insertImageRef = React.createRef();
        this.insertVideoRef = React.createRef();
        this.insertAudioRef = React.createRef();
        this.openFileRef = React.createRef();
        this.zipToApoRef = React.createRef();
        this.apoToZipRef = React.createRef();
        this.openOldFileRef = React.createRef();
        const viewModeObj = this.viewModeObj = {};
        viewModeObj[ViewModeType.BreakPageView] = ToolbarIndex.BreakPageView;
        // viewModeObj[ViewModeType.WebView] = ToolbarIndex.WebView;
        viewModeObj[ViewModeType.CompactView] = ToolbarIndex.CompactView;
        viewModeObj[ViewModeType.MorePage] = ToolbarIndex.MorePage;
        this.editModeObj = {};
        this.editModeObj[EditModeType.Normal] = ToolbarIndex.Normal;
        this.editModeObj[EditModeType.Protected] = ToolbarIndex.Protected;
        this.editModeObj[EditModeType.StrictMode] = ToolbarIndex.StrictMode;
        this.nisTableMode = {};
        this.nisTableMode[NISTableMode.Template] = ToolbarIndex.NISTemplateMode;
        this.nisTableMode[NISTableMode.Edit] = ToolbarIndex.NISEditMode;
    }

    public render(): any {
        const datas = this.datas;
        if (!datas || datas.length === 0) {
            return null;
        }

        if ( this.props.isResetMenuDatas && this.props.isResetMenuDatas() ) {
            this.resetMenuDatas();
        }

        const activeIndex = this.firstMenuActive;
        const lis = datas.filter((data) => data.hide !== true)
        .map((data, index) => {
            let className = '';
            if (activeIndex === index) {
                className = 'active';
            }
            return (
                <li key={data.index} className={className} onClick={this.firstMenuClick.bind(this, data, index)}>
                    {data.name}
                </li>
            );
        });

        return (
            <div className='editor-menu'>
                <ul className='first-menu'>
                    {lis}
                </ul>
                {this.renderSecondMenu(this.secondMenus)}
                {this.renderThreeMenu(this.threeMenus)}
                <input
                    id='insertImage'
                    className='insert-image'
                    type='file'
                    ref={this.insertImageRef}
                    accept='image/*'
                    onChange={this.fileChange}
                />
                <input
                    id='insertVideo'
                    className='insert-image'
                    type='file'
                    ref={this.insertVideoRef}
                    accept='video/*'
                    onChange={this.fileChange}
                />
                <input
                    id='insertAudio'
                    className='insert-image'
                    type='file'
                    ref={this.insertAudioRef}
                    accept='audio/*'
                    onChange={this.fileChange}
                />
                <input
                    id='insertFile'
                    className='insert-file'
                    type='file'
                    ref={this.insertFileRef}
                    onChange={this.fileChange}
                />
                <input
                    id='openFile'
                    className='open-file'
                    type='file'
                    ref={this.openFileRef}
                    onChange={this.fileChange}
                />
                <input
                    id='apoToZip'
                    className='apo-to-zip'
                    type='file'
                    ref={this.apoToZipRef}
                    onChange={this.fileChange}
                />
                <input
                    id='zipToApo'
                    className='zip-to-apo'
                    type='file'
                    ref={this.zipToApoRef}
                    onChange={this.fileChange}
                />
                <input
                    id='openOldFile'
                    className='open-old-file'
                    type='file'
                    ref={this.openOldFileRef}
                    onChange={this.fileChange}
                />
            </div>
        );
    }

    public componentDidMount(): void {
        this.menuAction = new ToolbarAction(this.host);
        this.insertFileRef.current.ownerDocument.addEventListener('click', this.docClick);
        gEvent.addEvent(this.host.docId, gEventName.Click, this.docClick);
        // 添加对主菜单可见性事件的监听
        gEvent.addEvent(this.host.docId, gEventName.MainMenuVisible, this.handleMainMenuVisibility);
    }

    public componentWillUnmount(): void {
        if (!this.insertFileRef.current?.ownerDocument) {
            console.log('undelete event menu');
            return;
        }
        this.insertFileRef.current.ownerDocument.removeEventListener('click', this.docClick);
        gEvent.deleteEvent(this.host.docId, gEventName.Click, this.docClick);
        // 移除事件监听
        gEvent.deleteEvent(this.host.docId, gEventName.MainMenuVisible, this.handleMainMenuVisibility);
    }

    private renderSecondMenu(menus: IBar[]): any {
        if (!menus || menus.length === 0 || this.firstMenuActive === undefined) {
            return null;
        }

        const documentCore = this.documentCore;
        // let bIntable: boolean;
        let bSelecteded = documentCore.isSelectionUse();
        const bInNumberCellOfNISTable = documentCore.isInNumberCellOfNISTable();
        const bInCellOfNISTable = documentCore.isInNISTable();
        const bInHeaderFooter = documentCore.isInHeaderFooter();
        const bIntable = documentCore.isInTableCell();

        const datas = menus.filter((data) => data.hide !== true);
        const len = datas.length - 1;
        const secondMenusActive = this.secondMenuActive;
        const bReadOnly = documentCore.isProtectedMode();
        const table = documentCore.getCurrentTable();
        const activeFirstMenu = this.datas[this.firstMenuActive];
        let bSpellCheck: boolean;
        if (activeFirstMenu?.index === ToolbarIndex.ReviewPanel) {
            bSpellCheck = SPELL_CHECK.isOpen();
        }
        const lis = datas.map((data, index) => {
            let className = '';
            data.disabled = false;

            if (data.children && data.children.length) {
                className = 'node-children';
            }
            let liClass = data.className;
            let iconClass = data.icon;
            if (data.bSplit || index === len) {
                liClass += ' split-line';
            } else if (bSpellCheck && data.index === ToolbarIndex.SpellCheck) {
                iconClass = liClass = 'hz-radio-checked';
                bSpellCheck = false;
            }

            if (bInCellOfNISTable && (ToolbarIndex.Struct === data.index ||
                ToolbarIndex.InsertFile === data.index)) {
                data.disabled = true;
            // } else {
            //     data.disabled = false;
            }

            // text cells can insert file
            if (bInCellOfNISTable && ToolbarIndex.InsertFile === data.index) {
                const cellType = documentCore.getNISTableCellType();
                if (cellType === NISTableCellType.Quick || cellType === NISTableCellType.Text) {
                    data.disabled = false;
                }
            }

            if ( bInNumberCellOfNISTable &&
                (ToolbarIndex.Struct === data.index || ToolbarIndex.InsertEditableImage === data.index
                || ToolbarIndex.InsertFile === data.index ||  ToolbarIndex.PageNum === data.index
                || ToolbarIndex.Image === data.index || ToolbarIndex.BreakPage === data.index
                || ToolbarIndex.MedEquation === data.index || ToolbarIndex.SpecialCharacter === data.index) ) {
                data.disabled = true;
            } else if (ToolbarIndex.InsertNISTable === data.index ) {
                if (table || bReadOnly || bSelecteded ||
                    (bInHeaderFooter && ToolbarIndex.InsertNISTable === data.index)) {
                    data.disabled = true;
                } else {
                    data.disabled = false;
                }
            } else if ([ToolbarIndex.InsertTable,
                ToolbarIndex.TableCellLeftSlash,
                ToolbarIndex.TableCellRightSlash].includes(data.index)) {
                    if (ToolbarIndex.InsertTable === data.index) {
                        data.disabled = bIntable || bSelecteded ? true : false;
                    } else {
                        data.disabled = bIntable && !bSelecteded ? false : true;
                    }
            } else if ( ToolbarIndex.NISTableProps === data.index || ToolbarIndex.NISTableCellProps === data.index
                || ToolbarIndex.NISTableCellContentProps === data.index || ToolbarIndex.NISRowOpera === data.index
                || ToolbarIndex.DiagonalLineHeader === data.index ) {

                if ( table && table.isNISTable() && !bReadOnly ) {
                    bSelecteded = table.isSelectionUse();
                    const bSelectedCells = documentCore.isSelectedTableCells();

                    if ( bSelecteded && (ToolbarIndex.NISRowOpera === data.index
                        || ToolbarIndex.DiagonalLineHeader === data.index) ) {
                        data.disabled = true;
                    } else if ( bSelectedCells && (ToolbarIndex.NISTableCellProps === data.index
                        || ToolbarIndex.NISTableCellContentProps === data.index) ) {
                        data.disabled = true;
                    } else {
                        data.disabled = false;
                    }
                } else {
                    data.disabled = true;
                }
            }

            if (data.disabled) {
                liClass += ' disabled';
            }
            let divClass = '';
            if (index === secondMenusActive) {
                divClass = 'active';
            }
            return (
                <li className={liClass} key={data.index}>
                    <div className={divClass} onClick={this.secondMenuClick.bind(this, data, index)}>
                        <i data-index={data.index} className={`image-icon iconfont ${iconClass}`}/>
                        <span className={className}>{data.name}</span>
                    </div>
                </li>
            );
        });

        lis.push(
            <li key='-01' onClick={this.secondMenuClick.bind(this, undefined, undefined)}>
                <div>
                    <span className='close'>关闭</span>
                </div>
            </li>
        );

        return (
            <ul className='second-menu'>
                {lis}
            </ul>
        );
    }

    private renderThreeMenu(menus: IBar[]): any {
        if (!menus || menus.length === 0 || this.secondMenuActive === undefined) {
            return null;
        }
        const checkedDatas = this.checkedDatas;
        const table = this.documentCore.getCurrentTable();

        // tslint:disable-next-line: max-line-length
        const lis = menus.filter((data) => data.hide !== true /*IFTRUE_WATER && data.index !== ToolbarIndex.WebView FITRUE_WATER*/)
        .map((data, index) => {
            let node = null;
            if (data.bSplit) {
                node = <span className='split-line'/>;
            }

            if ( (MenuItemIndex.InsertTopRow === data.index || MenuItemIndex.InsertBottomRow === data.index)
                && table && table.isNISTable() ) {
                data.disabled = !table.canAddRow();
            // } else if ( ToolbarIndex.RemoveRow === data.index && table && table.isNISTable()) {
            //     data.disabled = !table.canDeleteRow();
            } else if ( ToolbarIndex.ReadOnlyRow === data.index && table && table.isNISTable() ) {
                data.disabled = table.checkRowHasCellMerge();
            }

            let liClass = '';
            if (data.disabled) {
                liClass = 'disabled';
            }
            let iNode = null;
            if (checkedDatas.includes(data.index)) {
                iNode = <i className='checked-menu-flag'/>;
            }
            return (
            <li key={data.index} className={liClass} onClick={this.threeMenuClick.bind(this, data, index)}>
                {iNode}
                <label>{data.name}</label>
                {node}
            </li>
            );
        });

        return (
            <ul className='three-menu' style={{left: this.threeMenuPointX + 'px'}}>
                {lis}
            </ul>
        );
    }

    private firstMenuClick(menu: IBar, index: number, e?: any): void {
        this.bDocClick = false;
        if (this.firstMenuActive === index) {
            return;
        }
        this.firstMenuActive = index;
        this.secondMenus = menu.children;
        this.secondMenuActive = undefined;
        this.setItemsFlag(menu);
        this.setState({});
    }

    private close = (): void => {
        this.firstMenuActive = undefined;
        this.secondMenuActive = undefined;
        this.bDocClick = true;
        this.setState({});
    }

    private secondMenuClick(menu: IBar, index: number, e?: any): void {
        this.bDocClick = false;
        if (!menu || this.secondMenuActive === index) {
            this.close();
            return;
        }

        if (menu.disabled === true) {
            return;
        }

        if (!menu.children || !menu.children.length) {
            this.itemClick(menu, index);
            this.close();
            // gEvent.setEvent(this.host.docId, gEventName.DialogEvent, menu.index);
            return;
        }
        this.setItemsFlag(menu);
        this.threeMenuPointX = e.currentTarget.offsetLeft;
        this.secondMenuActive = index;
        this.threeMenus = menu.children;
        this.setState({});
    }

    private threeMenuClick(menu: IBar, index: number, e?: any): void {
        this.bDocClick = false;
        if (menu.disabled === true) {
            return;
        }
        this.itemClick(menu, index);
        this.close();
        // gEvent.setEvent(this.host.docId, gEventName.DialogEvent, menu.index);
        return;
    }

    private docClick = (e: any): void => {
        if (this.firstMenuActive === undefined) {
            return;
        }
        if (this.bDocClick === false) {
            this.bDocClick = true;
            return;
        }

        this.close();
    }

    private setItemsFlag(item: IBar): void {
        switch (item.index) {
            case ToolbarIndex.File:
            case ToolbarIndex.Edit: {
                item.children.forEach((data) => {
                    this.setItemFlag(data, data.index);
                });
                break;
            }
            case ToolbarIndex.Table: {
                const index = ToolbarIndex.TableProps;
                const data = item.children.find((node) => node.index === index);
                this.setItemFlag(data, data.index);
                break;
            }
            case ToolbarIndex.ViewMode: {
                this.setItemFlag(item, item.index);
                break;
            }
            case ToolbarIndex.EditMode: {
                this.setItemFlag(item, item.index);
                break;
            }
            // case ToolbarIndex.StartTrackRevisions: {
            //     const flag = this.menuAction.isTrackRevisions();
            //     const activeIndex = this.checkedDatas.findIndex((data) => (ToolbarIndex.StartTrackRevisions === data));
            //     if (flag) {
            //         this.checkedDatas.splice(activeIndex, 1);
            //     } else {
            //         this.checkedDatas.push(item.index);
            //     }
            //     break;
            // }
            // case ToolbarIndex.FinalRevisions: {
            //     const flag = this.menuAction.isFinalRevision();
            //     const activeIndex = this.checkedDatas.findIndex((data) => (ToolbarIndex.FinalRevisions === data));
            //     if (flag) {
            //         this.checkedDatas.splice(activeIndex, 1);
            //     } else {
            //         this.checkedDatas.push(item.index);
            //     }
            //     break;
            // }
            case ToolbarIndex.TableCellName: {
                const flag = this.menuAction.isShowTableCellNames();
                const activeIndex = this.checkedDatas.findIndex((data) => (ToolbarIndex.TableCellName === data));
                if (flag) {
                    this.checkedDatas.splice(activeIndex, 1);
                } else {
                    this.checkedDatas.push(item.index);
                }
            }
            case ToolbarIndex.Revision: {
                let flag = this.menuAction.isTrackRevisions();
                let activeIndex: number;
                activeIndex = ToolbarIndex.StartTrackRevisions;
                let actIndex = this.checkedDatas.findIndex((act) => act === activeIndex);
                if (flag) {
                    if (actIndex === -1) {
                        this.checkedDatas.push(activeIndex);
                    }
                } else {
                    if (actIndex > -1) {
                        this.checkedDatas.splice(actIndex, 1);
                    }
                }
                flag = this.menuAction.isFinalRevision();
                activeIndex = ToolbarIndex.FinalRevisions;
                actIndex = this.checkedDatas.findIndex((act) => act === activeIndex);
                if (flag) {
                    if (actIndex === -1) {
                        this.checkedDatas.push(activeIndex);
                    }
                } else {
                    if (actIndex > -1) {
                        this.checkedDatas.splice(actIndex, 1);
                    }
                }

                break;
            }
            case ToolbarIndex.NISTableMode: {
                this.setItemFlag(item, item.index);
                break;
            }
        }
    }

    private setItemFlag(item: IBar, index: number): void {
        // const data: IBar = this.datas[index];
        switch (index) {
            case ToolbarIndex.Cut:
            case ToolbarIndex.Copy: {
                item.disabled = !this.menuAction.isActiveCopy();
                break;
            }
            case ToolbarIndex.Redo: {
                item.disabled = !this.menuAction.isActiveRedo();
                break;
            }
            case ToolbarIndex.Undo: {
                item.disabled = !this.menuAction.isActiveUndo();
                break;
            }
            case ToolbarIndex.EditMode: {
                let editMode = this.menuAction.getEditMode();
                if (editMode === EditModeType.ReadRevision) {
                    editMode = EditModeType.Protected;
                }
                const arr = [ToolbarIndex.Normal, ToolbarIndex.Protected, ToolbarIndex.StrictMode];
                const checkedDatas = this.checkedDatas;
                const curIndex = checkedDatas.findIndex((myIndex) => arr.includes(myIndex));
                const activeIndex = this.editModeObj[editMode];
                if (curIndex > -1) {
                    checkedDatas[curIndex] = activeIndex;
                } else {
                    checkedDatas.push(activeIndex);
                }
                break;
            }
            case ToolbarIndex.ViewMode: {
                const viewMode = this.menuAction.getViewMode();
                const arrrs = [ToolbarIndex.BreakPageView, ToolbarIndex.MorePage, ToolbarIndex.CompactView];
                // const arrrs = [ToolbarIndex.BreakPageView, ToolbarIndex.WebView, ToolbarIndex.CompactView];
                const checkedDatas = this.checkedDatas;
                const curIndex = checkedDatas.findIndex((myIndex) => arrrs.includes(myIndex));
                const activeIndex = this.viewModeObj[viewMode];
                if (curIndex > -1) {
                   checkedDatas[curIndex] = activeIndex;
                } else {
                    checkedDatas.push(activeIndex);
                }
                break;
            }
            case ToolbarIndex.TableProps: {
                item.disabled = !this.menuAction.isInTable();
                break;
            }
            case ToolbarIndex.NISTableMode: {
                const nNISTableMode = this.menuAction.getNISTableMode();
                const arrrs = [ToolbarIndex.NISTemplateMode, ToolbarIndex.NISEditMode];
                const checkedDatas = this.checkedDatas;
                const curIndex = checkedDatas.findIndex((myIndex) => arrrs.includes(myIndex));
                const activeIndex = this.nisTableMode[nNISTableMode];
                if (curIndex > -1) {
                    checkedDatas[curIndex] = activeIndex;
                } else {
                    checkedDatas.push(activeIndex);
                }
            }
            // case ToolbarIndex.Revision: {
            //     const bTrackRevision = this.menuAction.isTrackRevisions();
            //     // const activeIndex = this.checkedDatas
            // .findIndex((index) => ToolbarIndex.StartTrackRevisions === index);
            //     this.menuAction.setTrackRevsions(bTrackRevision);
            //     const bFinalRevision = this.menuAction.isFinalRevision();
            //     this.menuAction.setFinalRevisions(bFinalRevision);
            //     break;
            // }
            default:
                return;
        }

        // this.setState({});
    }

    private itemClick = (data: IBar, index: number): boolean => {
        // this.setItemsFlag(data);
        IFRAME_MANAGER.setDocId(this.host.docId);
        return this.setItemEvent(data);
    }

    private setItemEvent(item: IBar): boolean {
        const documentCore = this.documentCore;
        switch (item.index) {
            case ToolbarIndex.NewFile: {
                this.menuAction.createNew()
                .then((res) => {
                    if (res) {
                        this.checkedDatas = [];
                        this.host.removeAllListen();
                        // 新建文档，完全刷新水印
                        /* IFTRUE_WATER */
                        // 刷新随机水印
                        documentCore.resetCorePosition(true, false);
                        /* FITRUE_WATER */
                        this.refresh();
                        this.host.initCursorPos();
                        gEvent.setEvent(this.host.docId, gEventName.RevisionChange);
                    }
                });
                break;
            }
            case ToolbarIndex.SpellCheck: {
                if (SPELL_CHECK.isOpen()) {
                    SPELL_CHECK.close();
                } else {
                    SPELL_CHECK.open()
                    .then(() => {
                        this.refresh();
                    });
                }
                return false;
            }
            case ToolbarIndex.Open: {
                this.menuAction.openFile()
                .then((res) => {
                    if (res) {
                        this.host.removeAllListen();
                        this.openFileRef.current.click();
                    }
                });

                break;
            }
            case ToolbarIndex.Search: {
                gEvent.setEvent(this.host.docId, gEventName.Search);
                return;
            }
            case ToolbarIndex.AutoFormat: {
                if (this.documentCore.deleteRedundantEx(true, true, true, false) === ResultType.Success) {
                    this.refresh();
                }
                return;
            }
            case ToolbarIndex.ImageLocal: {
                this.insertImageRef.current.click();
                break;
            }
            case ToolbarIndex.InsertEditableImage: {
              getImageEditor()
              .then((editor) => {
                editor.create();
                editor.onsave = (json, url, {width, height}) => {

                  // tslint:disable-next-line: no-shadowed-variable
                  const computeScale = (width: number, height: number): number => {
                      const maxWidth = this.host.state.documentCore.getMaxWidth(true);
                      const maxHeight = this.host.state.documentCore.getMaxHeight(true);
                      // tslint:disable-next-line: no-shadowed-variable
                      let scale = 1;
                      if (width > maxWidth || height > maxHeight) {
                        if (width / height >= maxWidth / maxHeight) {
                          scale = maxWidth / width;
                        } else {
                          scale = maxHeight / height;
                        }
                      }
                      return scale;
                  };
                  const scale = computeScale(width, height);

                  this.host.state.documentCore.addInlineImage(width * scale, height * scale, url,
                    '', EquationType.EditableSvg, json);
                  this.refresh();
                };
              });
              break;
            }
            case ToolbarIndex.VideoLocal: {
                this.insertVideoRef.current.click();
                break;
            }
            case ToolbarIndex.Audio: {
                this.insertAudioRef.current.click();
                break;
            }
            case ToolbarIndex.DesignMode: {
                documentCore.designTemplet(!documentCore.isDesignModel());
                this.refresh();
                break;
            }
            case ToolbarIndex.Normal: {
                const res = documentCore.setEditMode(EditModeType.Normal);
                if (res === ResultType.Success) {
                    this.exitFinalRevision();
                    this.refresh();
                }
                return;
            }
            case ToolbarIndex.Protected: {
                // const activeIndex = this.checkedDatas.findIndex((index) => index === ToolbarIndex.Protected);
                // this.menuAction.setProtected(activeIndex === -1);
                // this.refresh();
                const res = this.documentCore.setEditMode(EditModeType.Protected);
                if (res === ResultType.Success) {
                    this.refresh();
                }
                return;
            }

            case ToolbarIndex.Save: {
                this.menuAction.generate();
                break;
            }
            case ToolbarIndex.SaveDocx: {
                this.menuAction.generateOfficeBlob('docx');
                break;
            }
            case ToolbarIndex.SaveExcelByTable: {
                this.menuAction.generateOfficeBlob('excel-table');
                break;
            }

            case ToolbarIndex.InsertFile: {
                // tslint:disable-next-line: newline-per-chained-call
                if (!this.host.getDocumentCore().isProtectedMode()) {
                    this.menuAction.openFile(true)
                    .then((res) => {
                        if (res) {
                            this.insertFileRef.current.click();
                        }
                    });
                }

                break;
            }

            case ToolbarIndex.BreakPage: {
                this.menuAction.addPageBreak();
                // .then((res) => {});

                break;
            }

            case ToolbarIndex.AboutVersion:{
               // console.log("hz-editor version:20240719");
               // break;
               gEvent.setEvent(this.host.docId, gEventName.DialogEvent,item.index);
               return;
            }

            case ToolbarIndex.AICheck:{
                gEvent.setEvent(this.host.docId, gEventName.DialogEvent,item.index);
                return;
             }

            case ToolbarIndex.ApoToZip: {
                this.apoToZipRef.current.click();
                break;
            }

            case ToolbarIndex.ZipToApo: {
                this.zipToApoRef.current.click();
                break;
            }

            case ToolbarIndex.OpenOldFile: {
                this.menuAction.openFile()
                .then((res) => {
                    if (res) {
                        this.host.removeAllListen();
                        this.openOldFileRef.current.click();
                    }
                });

                break;
            }

            case ToolbarIndex.FormatFlag: {
                this.menuAction.setShowParaEnd();
                this.refresh();
                return;
            }

            case ToolbarIndex.ExportHtml: {
                const html = new ExportHtml(this.host);
                html.export();
                // html.exportAsString(true).then(html => console.log(html));
                return;
            }

            case ToolbarIndex.ExportPdf: {
                const html = new ExportHtml(this.host);
                const ofdFileHandler = new OfdFileHandler();
                ofdFileHandler.setOfdUrl(this.host.odfSvrUrl);
                ofdFileHandler.setPdfUrl(this.host.pdfSvrUrl);

                // Ensure export completes before getting HTML
                html.getHtml().then(blobData => {  
                    // 只有当blobData成功获取后，才调用html2Pdf  
                    ofdFileHandler.html2Pdf(blobData).then(() => {  
                    }).catch(error => {  
                        console.error('Error converting PDF-step2:', error);  
                    });  
                }).catch(error => {  
                    console.error('Error exporting PDF-step1:', error);  
                });
                return;
            }

            case ToolbarIndex.ExportOfd: {
                const html = new ExportHtml(this.host);
                const ofdFileHandler = new OfdFileHandler();
                ofdFileHandler.setOfdUrl(this.host.odfSvrUrl);
                ofdFileHandler.setPdfUrl(this.host.pdfSvrUrl);

               // Ensure export completes before getting HTML
               // Ensure export completes before getting HTML
               html.getHtml().then(blobData => {  
                // 只有当blobData成功获取后，才调用html2Pdf  
                ofdFileHandler.html2ofd(blobData).then(() => {  
                }).catch(error => {  
                    console.error('Error converting OFD-step2:', error);  
                });  
            }).catch(error => {  
                console.error('Error converting OFD-step1:', error);  
            });
            return;
            }

            case ToolbarIndex.OpenOfd:{
                const ofdFileHandler = new OfdFileHandler();
                ofdFileHandler.setOfdUrl(this.host.odfSvrUrl);
                ofdFileHandler.setPdfUrl(this.host.pdfSvrUrl);
                ofdFileHandler.ofd2htmlWithOpenFileSelector();
                return;
             }
            case ToolbarIndex.StartTrackRevisions: {
                // const activeIndex = this.checkedDatas.findIndex((index) => ToolbarIndex.StartTrackRevisions === index);
                // this.menuAction.setTrackRevsions(-1 < activeIndex);
                const flag = this.menuAction.isTrackRevisions();
                const activeIndex = this.checkedDatas.findIndex((data) => (ToolbarIndex.StartTrackRevisions === data));
                if (flag) {
                    this.checkedDatas.splice(activeIndex, 1);
                } else {
                    this.checkedDatas.push(item.index);
                }
                this.menuAction.setTrackRevsions(-1 === activeIndex);
                this.refresh();
                break;
            }

            case ToolbarIndex.FinalRevisions: {
                // const activeIndex = this.checkedDatas.findIndex((index) => ToolbarIndex.FinalRevisions === index);
                // this.menuAction.setFinalRevisions(-1 < activeIndex);
                const flag = this.menuAction.isFinalRevision();
                const activeIndex = this.checkedDatas.findIndex((data) => (ToolbarIndex.FinalRevisions === data));
                if (flag) {
                    this.checkedDatas.splice(activeIndex, 1);
                } else {
                    this.checkedDatas.push(item.index);
                }
                this.menuAction.setFinalRevisions(-1 === activeIndex);
                this.refresh();
                break;
            }

            case ToolbarIndex.StartRecord: {
              this.menuAction.startRecord();
              this.setRecordReplayMenuItemsStatus();
              this.refresh();
              break;
            }

            case ToolbarIndex.StopRecord: {
              this.menuAction.stopRecord();
              this.setRecordReplayMenuItemsStatus();
              this.refresh();
              break;
            }

            case ToolbarIndex.ExportRecord: {
              this.menuAction.exportRecord();
              break;
            }

            case ToolbarIndex.StartReplay: {
              const onFramePlay = () => this.refresh();
            //   const onFramePlay = () => {};

              const onFinished = () => {
                // this.refresh();
                // tslint:disable-next-line: no-console
                console.log('finished');
              };
              // tslint:disable-next-line: no-console
              const onCanceled = () => console.log('cancel');
              this.menuAction.startReplay(onFramePlay, onFinished, onCanceled);
              this.setRecordReplayMenuItemsStatus();
              break;
            }

            case ToolbarIndex.StopReplay: {
              this.menuAction.stopRecord();
              this.setRecordReplayMenuItemsStatus();
              this.refresh();
              break;
            }

            case ToolbarIndex.StatusCode: {
                //   alert(this.host.getEditor().getFileMd5());
                this.host.getEditor()
                .getFileMd5()
                .then((file) => {
                    message.error(file);
                });
                break;
            }

            case ToolbarIndex.BreakPageView: {
                const res = this.documentCore.setViewMode(ViewModeType.BreakPageView);
                if (res === ResultType.Success) {
                    this.refresh();
                }
                return;
            }

            // case ToolbarIndex.WebView: {
            //     const res = this.documentCore.setViewMode(ViewModeType.WebView);
            //     if (res === ResultType.Success) {
            //         this.refresh();
            //         gEvent.setEvent(this.host.docId, gEventName.ViewMode);
            //     }
            //     return;
            // }
            case ToolbarIndex.MorePage: {
                const res = this.documentCore.setViewMode(ViewModeType.MorePage);
                if (res === ResultType.Success) {
                    this.refresh();
                    gEvent.setEvent(this.host.docId, gEventName.ViewMode);
                }
                return;
            }
            case ToolbarIndex.CompactView: {
                const res = this.documentCore.setViewMode(ViewModeType.CompactView);
                if (res === ResultType.Success) {
                    this.refresh();
                    gEvent.setEvent(this.host.docId, gEventName.ViewMode);
                }
                return;
            }

            case ToolbarIndex.StrictMode: {
                // const activeIndex = this.checkedDatas.findIndex((index) => ToolbarIndex.StrictMode === index);
                // this.menuAction.setStrictMode(-1 < activeIndex);
                // this.refresh();
                this.exitFinalRevision();
                const res = this.documentCore.setEditMode(EditModeType.StrictMode);
                if (res === ResultType.Success) {
                    this.refresh();
                }
                return;
            }

            case ToolbarIndex.TableCellName: {
                // const activeIndex = this.checkedDatas.findIndex((index) => ToolbarIndex.TableCellName === index);
                // this.menuAction.setShowTableCellNames(-1 < activeIndex);
                const flag = this.menuAction.isShowTableCellNames();
                const activeIndex = this.checkedDatas.findIndex((data) => (ToolbarIndex.TableCellName === data));
                if (flag) {
                    this.checkedDatas.splice(activeIndex, 1);
                } else {
                    this.checkedDatas.push(item.index);
                }
                this.menuAction.setShowTableCellNames(-1 === activeIndex);
                this.refresh();
                return;
            }

            case ToolbarIndex.TableCellLeftSlash: {
                if (this.menuAction.setTableCellSlash(1)) {
                    this.refresh();
                }
                return;
            }
            case ToolbarIndex.TableCellRightSlash: {
                if (this.menuAction.setTableCellSlash(2)) {
                    this.refresh();
                }
                return;
            }

            case ToolbarIndex.ReadOnlyRow: {
                if ( NURSING_FEATURE ) {
                    this.documentCore.setTableRowReadOnly();
                    this.refresh();
                }
                break;
            }

            case ToolbarIndex.RemoveRow: {
                if ( NURSING_FEATURE ) {
                    this.documentCore.removeTableRow();
                    this.refresh();
                }
                break;
            }

            case MenuItemIndex.InsertTopRow: {
                if (NURSING_FEATURE) {
                    this.documentCore.insertTableRow(true);
                    this.refresh();
                }
                break;
            }

            case MenuItemIndex.InsertBottomRow: {
                if ( NURSING_FEATURE ) {
                    this.documentCore.insertTableRow(false);
                    this.refresh();
                }
                break;
            }

            case ToolbarIndex.DiagonalLineHeader: {
                if ( NURSING_FEATURE && this.documentCore.setDiagonalLineCell() ) {
                    this.refresh();
                }
                break;
            }

            case ToolbarIndex.NISTemplateMode: {
                if ( NURSING_FEATURE ) {
                    this.documentCore.setNISTableMode(NISTableMode.Template);
                }
                break;
            }

            case ToolbarIndex.NISEditMode: {
                if ( NURSING_FEATURE ) {
                    this.documentCore.setNISTableMode(NISTableMode.Edit);
                }
                break;
            }

            case ToolbarIndex.IncrementSave: {
                // const data = this.documentCore.getIncrementDatas();
                // if ( data ) {
                //     // logger.open(this.host.id);
                //     // logger.info({id: this.documentCore.getCurrentId(),
                //     //     description: 'IncrementSave', result: data});
                //     incrementDB.insertItem(101, data);
                // }
                return;
            }

            case ToolbarIndex.IncrementLoad: {
                // logger.open(this.host.id);
                // const items = incrementDB.getItem(101); // logger.getItem();

                // if ( items ) {
                //     let data;
                //     items.then((item1) => {
                //         data = item1.value;

                //         if ( this.documentCore.loadIncrementDatas(JSON.parse(data)) ) {
                //             this.refresh();
                //         }
                //     });
                // }
                return;
            }

            default: {
                this.menuAction.handleEditMenu(item.index);
                break;
            }

        }
        gEvent.setEvent(this.host.docId, gEventName.DialogEvent, item.index);

        return true;
    }

    private refresh(): void {
        this.host.handleRefresh();
    }

    private exitFinalRevision(): void {
        const activeIndex = (this.checkedDatas ?
            this.checkedDatas.findIndex((pos) => ToolbarIndex.FinalRevisions === pos) : -1);
        if ( -1 < activeIndex ) {
            this.checkedDatas.splice(activeIndex, 1);
            this.menuAction.setFinalRevisions(false);
        }
    }

    private setRecordReplayMenuItemsStatus(): void {
        const rrState = getDocumentCoreRecordReplayState();
        const rrMenuItems = this.datas.find((item) => item.index === ToolbarIndex.Tool).children
          .find((item) => item.index === ToolbarIndex.Auto).children
          .filter((data: IBar) => data.index !== ToolbarIndex.AutoTestPlay);

        if (rrState.isInit || rrState.isReplayFinished) {
          rrMenuItems.forEach((item) => {
            if ([ToolbarIndex.StartRecord, ToolbarIndex.StartReplay, ToolbarIndex.StatusCode].includes(item.index)) {
              item.disabled = false;
            } else {
              item.disabled = true;
            }
          });
        }

        if (rrState.isRecording) {
          rrMenuItems.forEach((item) => {
            if ([ToolbarIndex.StopRecord].includes(item.index)) {
              item.disabled = false;
            } else {
              item.disabled = true;
            }
          });
        }

        if (rrState.isRecordFinished) {
          rrMenuItems.forEach((item) => {
            if ([ToolbarIndex.StartRecord, ToolbarIndex.ExportRecord,
                  ToolbarIndex.StartReplay, ToolbarIndex.StatusCode].includes(item.index)) {
              item.disabled = false;
            } else {
              item.disabled = true;
            }
          });
        }

        if (rrState.isReplaying) {
          rrMenuItems.forEach((item) => {
            if ([ToolbarIndex.StopReplay].includes(item.index)) {
              item.disabled = false;
            } else {
              item.disabled = true;
            }
          });
        }

    }

    private fileChange = (e: any) => {
        const inputTarget = e.target as HTMLInputElement;
        // tslint:disable-next-line: newline-per-chained-call
        this.menuAction.fileChange(inputTarget).then((res) => {
            if (res === true) {
                this.resetMenuDatas();
                // this.refresh();
            }
        });

        // trick input field to "onChange" every time
        inputTarget.value = '';
    }

    private resetMenuDatas(): void {
        this.checkedDatas = [];
    }

    // 处理主菜单可见性的方法
    private handleMainMenuVisibility = (option: any): void => {
        // 检查是否有 helper 或 Helper 键
        if ('helper' in option || 'Helper' in option) {
            const isVisible = option.helper !== false && option.Helper !== false;
            
            // 找到帮助菜单项并设置其 hide 属性
            for (let i = 0; i < this.datas.length; i++) {
                if (this.datas[i].index === ToolbarIndex.Helper) {
                    this.datas[i].hide = !isVisible;
                    break;
                }
            }
            
            // 更新状态以触发重新渲染
            this.setState({});
        }
    }
}

toolbars = [
    {
        name: '文件',
        index: ToolbarIndex.File,
        children: [
            {
                name: '新建',
                index: ToolbarIndex.NewFile,
                className: 'newFile',
                icon: 'hz-add',
            },
            {
                name: '打开',
                index: ToolbarIndex.Open,
                className: 'open',
                icon: 'hz-open',
            },
            {
                name: '保存',
                index: ToolbarIndex.Save,
                bSplit: true,
                className: 'save',
                icon: 'hz-save2',
            },
           
            {
                name: '打印预览',
                index: ToolbarIndex.ReviewPrint,
                className: 'reviewPrint',
                icon: 'hz-search',
            },
            {
                name: '打印',
                index: ToolbarIndex.Print,
                className: 'print',
                icon: 'hz-printer',
            },
            // {
            //     name: 'C端打印',
            //     index: ToolbarIndex.CPrint,
            //     className: 'cprint',
            //     icon: 'hz-printer',
            // },
        ],
    },
    {
        name: '编辑',
        index: ToolbarIndex.Edit,
        initChildCheck: true,
        children: [
            {
                name: '撤销',
                index: ToolbarIndex.Undo,
                disabled: true,
                className: 'undo',
                icon: 'hz-undo1',
            },
            {
                name: '重做',
                index: ToolbarIndex.Redo,
                disabled: true,
                className: 'redo',
                icon: 'hz-redo1',
            },
            {
                name: '复制',
                index: ToolbarIndex.Copy,
                disabled: true,
                className: 'copy',
                icon: 'hz-content_copy',
            },
            {
                name: '粘贴',
                index: ToolbarIndex.Paste,
                className: 'paste',
                icon: 'hz-content_paste',
            },
            {
                name: '剪切',
                index: ToolbarIndex.Cut,
                disabled: true,
                bSplit: true,
                className: 'cut',
                icon: 'hz-content_cut',
            },
            {
                name: '全选',
                index: ToolbarIndex.SelectAll,
                className: 'selectAll',
                icon: 'hz-loop2',
            },
            {
                name: '编辑模式',
                index: ToolbarIndex.EditMode,
                className: 'editMode',
                icon: 'hz-cogs',
                children: [
                    {
                        name: '正常',
                        index: ToolbarIndex.Normal,
                        checked: true,
                    },
                    {
                        name: '只读',
                        index: ToolbarIndex.Protected,
                        checked: false,
                    },
                    {
                        name: '仅元素编辑',
                        index: ToolbarIndex.StrictMode,
                        checked: false,
                    },
                ]
            },
        ],
    },
    {
        name: '布局',
        index: ToolbarIndex.View,
        children: [
            {
                name: '页面设置',
                index: ToolbarIndex.PageSet,
                className: 'pageSet',
                icon: 'hz-layout',
            },
            {
                name: '字符设置',
                index: ToolbarIndex.Char,
                className: 'char',
                icon: 'hz-font',
            },
            {
                name: '段落设置',
                index: ToolbarIndex.Paragraph,
                className: 'paragraph',
                icon: 'hz-paragraphjustify',
            },
            {
                name: '页眉页脚',
                index: ToolbarIndex.HeaderFooter,
                className: 'headerFooter',
                bSplit: true,
                icon: 'hz-insertfile',
            },
            {
                name: '缩放',
                index: ToolbarIndex.ViewScale,
                className: 'viewScale',
                icon: 'hz-zoom',
            },
            {
                name: '显示格式标记',
                index: ToolbarIndex.FormatFlag,
                className: 'formatFlag',
                icon: 'hz-return',
            },        
            {
                name: '导航',
                index: ToolbarIndex.NavMenu,
                className: 'navMenu',
                icon: 'hz-files',
            },
            // {
            //     name: '属性面板',
            //     index: ToolbarIndex.AttributePanel,
            // },
            // {
            //     name: '视图模式',
            //     index: ToolbarIndex.ViewMode,
            //     className: 'viewMode',
            //     initChildCheck: true,
            //     icon: 'viewMode',
            //     children: [
            //         {
            //             name: '分页视图',
            //             index: ToolbarIndex.BreakPageView,
            //             checked: true,
            //         },
            //         {
            //             name: 'Web视图',
            //             index: ToolbarIndex.WebView,
            //             checked: false,
            //         },
            //         {
            //             name: '紧缩型',
            //             index: ToolbarIndex.CompactView,
            //             checked: false,
            //         }
            //     ]
            // },
        ],
    },
    {
        name: '插入',
        index: ToolbarIndex.Insert,
        children: [
            {
                name: '控件元素',
                index: ToolbarIndex.Struct,
                className: 'struct',
                bSplit: true,
                icon: 'hz-struct',
                children: [
                    {
                        name: '文本框',
                        index: ToolbarIndex.TextBox,
                    },
                    {
                        name: '节',
                        index: ToolbarIndex.Section,
                        bSplit: true,
                    },
                    {
                        name: '数字框',
                        index: ToolbarIndex.NumberBox,
                        bSplit: true,
                    },
                    {
                        name: '勾选框',
                        index: ToolbarIndex.Checkbox,
                        bSplit: true,
                    },
                    {
                        name: '列表框(单选)',
                        index: ToolbarIndex.ListBox,
                    },
                    {
                        name: '列表框(多选)',
                        index: ToolbarIndex.MultiListBox,
                        bSplit: true,
                    },
                    {
                        name: '组合框(单选)',
                        index: ToolbarIndex.Combox,
                    },
                    {
                        name: '组合框(多选)',
                        index: ToolbarIndex.MultiCombox,
                        bSplit: true,
                    },
                    {
                        name: '日期框',
                        index: ToolbarIndex.DateBox,
                        bSplit: true,
                    },
                    {
                        name: '选项按钮(单选)',
                        index: ToolbarIndex.Radio,
                    },
                    {
                        name: '选项按钮(多选)',
                        index: ToolbarIndex.MultiRadio,
                        bSplit: true,
                    },
                    {
                        name: '区域',
                        index: ToolbarIndex.Region,
                        bSplit: true,
                    },
                    {
                        name: '签名控件',
                        index: ToolbarIndex.SignatureBox,
                    },
                    {
                        name: '地址框',
                        index: ToolbarIndex.AddressBox,
                    },
                    {
                        name: '按钮',
                        index: MenuItemIndex.ParaButton,
                     },
                ],
            },
            {
                name: '分页符',
                index: ToolbarIndex.BreakPage,
                
                className: 'breakPage',
                icon: 'hz-pagebreak',
            },
            {
                name: '图片',
                index: ToolbarIndex.Image,
                className: 'image',
                icon: 'hz-image',
                children: [
                    {
                        name: '本地',
                        index: ToolbarIndex.ImageLocal,
                    },
                    {
                        name: '网络U',
                        index: ToolbarIndex.ImageUrl,
                    },
                    {
                        name: '大图片',
                        index: ToolbarIndex.ImageBigUrl,
                    }
                ]
            },
            {
               name: '矢量图片',
               index: ToolbarIndex.InsertEditableImage,
               className: 'insertEditableImage',
               bSplit: true,
               icon: 'hz-svgimage',

            },
            {
                name: '视频',
                index: ToolbarIndex.Video,
                className: 'image',
                icon: 'hz-mediafile',
                children: [
                    {
                        name: '本地',
                        index: ToolbarIndex.VideoLocal,
                    },
                    {
                        name: '网络URL',
                        index: ToolbarIndex.VideoUrl,
                    }
                ]
            },
            {
                name: '音频',
                index: ToolbarIndex.Audio,
                className: 'image',
                bSplit: true,
                icon: 'hz-audiofile',
            },
            {
                name: '医学表达式',
                index: ToolbarIndex.MedEquation,
                className: 'medEquation',
                icon: 'hz-calculator',
            },
            {
                name: '条形码',
                index: ToolbarIndex.Barcode,
                className: 'image',
                icon: 'hz-barcode',
                bSplit: true,
            },
            {
                name: '二维码',
                index: ToolbarIndex.QRCode,
                className: 'image',
                bSplit: true,
                icon: 'hz-qrcode',
            },
            {
                name: '特殊字符',
                index: ToolbarIndex.SpecialCharacter,
                className: 'specialCharacter',
                icon: 'hz-specialfont',
            },
            {
                name: '页码',
                index: ToolbarIndex.PageNum,
                className: 'pageNum',
                bSplit: true,
                icon: 'hz-pagenumber',
            },
            {
                name: '文件',
                index: ToolbarIndex.InsertFile,
                className: 'insertFile',
                bSplit: true,
                icon: 'hz-file-text2',
                disabled: false,
            },           
        ],
    },
   // {
    //    name: '格式',
   //     index: ToolbarIndex.Format,
   //     children: [
   //         {
   //             name: '字符',
   //             index: ToolbarIndex.Char,
   //             className: 'char',
  //              icon: 'font',
   //         },
    //        {
    //            name: '段落',
    //            index: ToolbarIndex.Paragraph,
     //           className: 'paragraph',
     //           icon: 'paragraph',
     //       },
    //    ],
   // },
    {
        name: '审阅',
        index: ToolbarIndex.ReviewPanel,
        children: [
            {
                name: '查找',
                index: ToolbarIndex.Search,
                className: 'search',
                bSplit: true,
                icon: 'hz-search',
            },
            {
                name: '拼写检查',
                index: ToolbarIndex.SpellCheck,
                className: 'toggleClose',
                icon: 'hz-radio-unchecked'
            },
            {
                name: '批注',
                index: ToolbarIndex.Comment,
                className: 'comments',
                icon: 'hz-commit',
                children: [
                    {
                        name: '插入批注',
                        index: ToolbarIndex.CommentInsert,
                    },
                    {
                        name: '批注助手',
                        index: ToolbarIndex.CommentShow,
                        checked: false,
                    },
                ],
            },
            {
                name: '留痕',
                index: ToolbarIndex.Revision,
                className: 'revision',
                icon: 'hz-trackchanges',
                children: [
                    {
                        name: '留痕设置',
                        index: ToolbarIndex.RevisionSetting,
                    },
                    {
                        name: '开启留痕',
                        index: ToolbarIndex.StartTrackRevisions,
                        checked: false,
                        disabled: false,
                    },
                    {
                        name: '接受留痕',
                        index: ToolbarIndex.RevisionAcceptReject,
                        disabled: false,
                    },
                    {
                        name: '最终状态',
                        index: ToolbarIndex.FinalRevisions,
                        checked: false,
                    },
                    {
                        name: '审阅窗格',
                        index: ToolbarIndex.ReviewPanel,
                        disabled: false,
                    },
                ],
            },
            {
                name: '水印',
                index: ToolbarIndex.WaterMark,
                className: 'waterMark',
                bSplit: true,
                icon: 'hz-water',
            },
            {
                name: '格式整理',
                index: ToolbarIndex.AutoFormat,
                className: 'autoFormat',
                icon: 'hz-spellcheck',
            },
            {
                name: '级联助手',
                index: ToolbarIndex.CascadeManager,
                className: 'cascadeManager',
                bSplit: true,
                icon: 'hz-link',
            },
        ]
    },
    {
        name: '导出',
        index: ToolbarIndex.Tool,
        children: [
            {
                name: '导出docx',
                index: ToolbarIndex.SaveDocx,
                className: 'basicWord',
                icon: 'hz-word',
            },
            {
                name: '导出excel',
                index: ToolbarIndex.SaveExcelByTable,
                className: 'basicExcel',
                icon: 'hz-excel',
            },
           
            {
                name: '导出html',
                index: ToolbarIndex.ExportHtml,
                className: 'exportHtml',
                icon: 'hz-html',
            },

            {
                name: '导出pdf',
                index: ToolbarIndex.ExportPdf,
                className: 'exportPdf',
                icon: 'hz-pdf',
            },

            {
                name: '导出ofd',
                index: ToolbarIndex.ExportOfd,
                className: 'exportOfd',
                icon: 'hz-file-text2',
            },
            {
                name: 'ofd转html',
                index: ToolbarIndex.OpenOfd,
                className: 'openOfd',
                icon: 'hz-loop2',
            },
        ],
    },
    {
        name: '表格',
        index: ToolbarIndex.Table,
        children: [
            {
                name: '插入',
                index: ToolbarIndex.InsertTable,
                className: 'insertTable',
                icon: 'hz-table',
            },
            {
                name: '表格属性',
                index: ToolbarIndex.TableProps,
                disabled: false,
                className: 'tableProps',
                icon: 'hz-info',
            },
            {
                name: '单元格名',
                index: ToolbarIndex.TableCellName,
                className: 'tableCellName',
                checked: false,
                disabled: false,
                icon: 'hz-rowsetting',
            },
            {
                name: '左斜线',
                index: ToolbarIndex.TableCellLeftSlash,
                checked: false,
                disabled: false,
                icon: 'hz-cell',
            },
            {
                name: '右斜线',
                index: ToolbarIndex.TableCellRightSlash,
                checked: false,
                disabled: false,
                icon: 'hz-cell2',
            },
        ],
    },
    // {
    //     name: '护理表格',
    //     index: ToolbarIndex.NISTable,
    //     children: [
    //         {
    //             name: '插入护理表格',
    //             index: ToolbarIndex.InsertNISTable,
    //             className: 'insertNISTable',
    //             icon: 'insertTable',
    //         },
    //         {
    //             name: '护理表格属性',
    //             index: ToolbarIndex.NISTableProps,
    //             disabled: false,
    //             className: 'NISTableProps',
    //             icon: 'tableAttribute',
    //         },
    //         {
    //             name: '显示单元格名称',
    //             index: ToolbarIndex.TableCellName,
    //             className: 'tableCellName',
    //             checked: false,
    //             disabled: false,
    //             icon: 'rangeName',
    //         },
    //         {
    //             name: '单元格类型设置',
    //             index: ToolbarIndex.NISTableCellProps,
    //             className: 'NISTableCellProps',
    //             checked: false,
    //             disabled: false,
    //             icon: 'cellType',
    //         },
    //         {
    //             name: '单元格格式设置',
    //             index: ToolbarIndex.NISTableCellContentProps,
    //             className: 'NISTableCellContentProps',
    //             checked: false,
    //             disabled: false,
    //             icon: 'cellFormat',
    //         },
    //         {
    //             name: '行操作',
    //             index: ToolbarIndex.NISRowOpera,
    //             className: 'NISRowOpera',
    //             checked: false,
    //             disabled: false,
    //             icon: 'row',
    //             children: [
    //               {
    //                 name: '行只读',
    //                 index: ToolbarIndex.ReadOnlyRow,
    //               },
    //               {
    //                 name: '删除行',
    //                 index: ToolbarIndex.RemoveRow,
    //               },
    //               {
    //                 name: '上方插入行',
    //                 index: MenuItemIndex.InsertTopRow,
    //               },
    //               {
    //                 name: '下方插入行',
    //                 index: MenuItemIndex.InsertBottomRow,
    //               },
    //             ],
    //         },
    //         {
    //             name: '斜线表头',
    //             index: ToolbarIndex.DiagonalLineHeader,
    //             className: 'diagonalLineHeader',
    //             checked: false,
    //             disabled: false,
    //             icon: 'slashHeader',
    //         },
    //         {
    //             name: '模式',
    //             index: ToolbarIndex.NISTableMode,
    //             className: 'NISTableMode',
    //             checked: false,
    //             disabled: false,
    //             icon: 'mode',
    //             children: [
    //               {
    //                 name: '模板模式',
    //                 index: ToolbarIndex.NISTemplateMode,
    //               },
    //               {
    //                 name: '书写模式',
    //                 index: ToolbarIndex.NISEditMode,
    //               },
    //             ],
    //         },
    //     ],
    // },
    {
        name: '类型表格',
        index: ToolbarIndex.NISTable,
        children: [
            {
                name: '插入',
                index: ToolbarIndex.InsertNISTable,
                className: 'insertNISTable',
                icon: 'hz-table1',
            },
            {
                name: '表格属性',
                index: ToolbarIndex.NISTableProps,
                disabled: false,
                className: 'NISTableProps',
                icon: 'hz-info',
            },
            {
                name: '显示名称',
                index: ToolbarIndex.TableCellName,
                className: 'tableCellName',
                checked: false,
                disabled: false,
                icon: 'hz-rowsetting',
            },
            {
                name: '类型设置',
                index: ToolbarIndex.NISTableCellProps,
                className: 'NISTableCellProps',
                checked: false,
                disabled: false,
                icon: 'hz-cell2',
            },
            {
                name: '格式设置',
                index: ToolbarIndex.NISTableCellContentProps,
                className: 'NISTableCellContentProps',
                checked: false,
                disabled: false,
                icon: 'hz-cell',
            },
            {
                name: '行操作',
                index: ToolbarIndex.NISRowOpera,
                className: 'NISRowOpera',
                checked: false,
                disabled: false,
                icon: 'hz-row',
                children: [
                  {
                    name: '行只读',
                    index: ToolbarIndex.ReadOnlyRow,
                  },
                  {
                    name: '行删除',
                    index: ToolbarIndex.RemoveRow,
                  },
                  {
                    name: '上方插入行',
                    index: MenuItemIndex.InsertTopRow,
                  },
                  {
                    name: '下方插入行',
                    index: MenuItemIndex.InsertBottomRow,
                  },
                ],
            },
            {
                name: '斜线表头',
                index: ToolbarIndex.DiagonalLineHeader,
                className: 'diagonalLineHeader',
                checked: false,
                disabled: false,
                icon: 'hz-struct2',
            },
            {
                name: '模式',
                index: ToolbarIndex.NISTableMode,
                className: 'NISTableMode',
                checked: false,
                disabled: false,
                icon: 'hz-setting',
                children: [
                  {
                    name: '模板模式',
                    index: ToolbarIndex.NISTemplateMode,
                  },
                  {
                    name: '书写模式',
                    index: ToolbarIndex.NISEditMode,
                  },
                ],
            },
        ],
    },
     {
        name: 'AI',
        index: ToolbarIndex.AI,
        children: [
            {
                name:"智能检查",
                index:ToolbarIndex.AICheck,
                icon: 'hz-ai',
            },
        ],
    },
    {
        name: '帮助',
        index: ToolbarIndex.Helper,
        children: [
            {
                name:"关于",
                index:ToolbarIndex.AboutVersion,
                icon: 'hz-info',
            },
            // {
            //     name: '文字输入',
            //     index: ToolbarIndex.InputChart,
            //     className: 'inputChart',
            //     icon: '',
            // },
            // {
            //     name: 'apo转为zip',
            //     index: ToolbarIndex.ApoToZip,
            //     className: 'apoToZip',
            //     icon: 'apoTurnZip',
            // },
            // {
            //     name: 'zip转为apo',
            //     index: ToolbarIndex.ZipToApo,
            //     className: 'zipToApo',
            //     icon: 'zipTurnApo',
            // },
            // {
            //     name: '打开旧格式文档',
            //     index: ToolbarIndex.OpenOldFile,
            //     className: 'open',
            //     icon: 'open',
            // },
        ],
    },
];
