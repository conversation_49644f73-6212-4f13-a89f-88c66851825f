import CopyModel from '../../common/copy/CopyPaste'; // the name lol
import {GlobalEvent as gEvent, GlobalEventName as gEventName} from '../../common/GlobalEvent';
import { getEditorDomContainer } from '../../common/commonDefines';

let coypContainerDomWindow: any;
export class Copy extends CopyModel {
    private _paseDom: HTMLDivElement;
    public constructor(host: any) {
        super();
        this.host = host;
        this.docId = host.docId;
        this.init();
    }

    protected createOperateDom(panel: HTMLDivElement): void {
        // if (this.repasteDom) {
        //     return;
        // } else {
        //     const div = document.createElement('div');
        //     div.className = 'editor-copy-repaste';
        //     div.innerHTML = `
        //     <div class="right-menu-repaste">
        //         <div class="repaste-ctr"><span>V</span></div>
        //         <div class="repaste-btns">
        //             <div class="list">粘贴选项</div>
        //             <span class="format" title="保留原格式">格式</span>
        //             <span class="unformat" title="只保留文本">文本</span>
        //         </div>
        //     </div>`;
        //     this.editorDom.querySelector('.ReactVirtualized__Grid.ReactVirtualized__List')
        //         .appendChild(div);
        //     this.repasteDom = div.firstChild.nextSibling as HTMLDivElement;
        //     this._paseDom = div;
        // }
        // this.repasteDialogDom = panel.nextSibling.firstChild as HTMLDivElement;
        // this.repasteDom.addEventListener('click', this.repasteEvent, false);
        // this.repasteDialogDom.addEventListener('click', this.repasteDialogEvent, false);
        // this.repasteDialogDom.addEventListener('keydown', this.stopProgapation);
    }

    protected createCopyDom(): void {
        if (coypContainerDomWindow) {
            this.copyIframeWindow = coypContainerDomWindow;
            this.copyDom = this.copyIframeWindow.document.body;
            return;
        }
        const iframe = document.createElement('iframe');
        iframe.src = 'about:blank';
        iframe.className = 'copy-paste-iframe';
        getEditorDomContainer()
        .appendChild(iframe);
        this.copyIframeWindow = coypContainerDomWindow = iframe.contentWindow;
        this.copyDom = this.copyIframeWindow.document.body;
    }

    private init(): void {
        this.addEvent();
        const id = '女王陛下';
        this.createCopyDom();
        // this.copyPaste = this.getCopyPaste();
        // this.copyPaste.setAccessId(id);
        // this.copyPaste.setAccess(true);
    }

    private addEvent(): void {
        if (this.host.currentRef) {
            this.editorDom = this.host.currentRef.current;
        } else if (this.host.myRef) {
            this.editorDom = this.host.myRef.current;
        }
        this.editorDom.addEventListener('cut', this.documentCutEvent, false);
        this.editorDom.addEventListener('copy', this.docmentCopyEvent, false);
        this.editorDom.addEventListener('paste', this.documentPasteEvent, false);
        this.editorDom.addEventListener('drop', this.documentDrop, false);

        gEvent.addEvent(this.docId, 'setExtraCopyInformation', this.setExtraCopyInformation);
        gEvent.addEvent(this.docId, 'enableCopyFromExternal', this.enableCopyFromExternal);
        gEvent.addEvent(this.docId, 'editorStopUnSelection', this.stopUnSelection);
        gEvent.addEvent(this.docId, gEventName.UnMounted, this.removeEvent);
        // gEvent.addEvent(this.docId, gEventName.ContentChange, this.removeRespateBtn);
        gEvent.addEvent(this.docId, 'contextmenu', this.contextmenu);
        gEvent.addEvent(this.docId, 'rightMenuClick', this.rightMenuClick);
        // gEvent.addEvent(this.docId, gEventName.UnMounted, this.removeEvent);
        this.editorDom.addEventListener('drop', this.documentDrop, false);
        this.editorDom.addEventListener('dragover', this.handleDragOver, false);
    }

    // private svgDom: HTMLElement;

    private removeEvent = (): void => {
        gEvent.deleteEvent(this.docId, 'setExtraCopyInformation', this.setExtraCopyInformation);
        gEvent.deleteEvent(this.docId, 'enableCopyFromExternal', this.enableCopyFromExternal);
        gEvent.deleteEvent(this.docId, 'contextmenu', this.contextmenu);
        gEvent.deleteEvent(this.docId, 'rightMenuClick', this.rightMenuClick);
        gEvent.deleteEvent(this.docId, 'editorStopUnSelection', this.stopUnSelection);
        gEvent.deleteEvent(this.docId, gEventName.UnMounted, this.removeEvent);
        if (!this.editorDom) {
            console.log('undelete event copypaste');
            return;
        }
        this.editorDom.removeEventListener('drop', this.documentDrop, false);
        this.editorDom.removeEventListener('dragover', this.handleDragOver, false);
        // gEvent.deleteEvent(this.docId, gEventName.ContentChange, this.removeRespateBtn);

        this.editorDom.removeEventListener('copy', this.docmentCopyEvent);
        this.editorDom.removeEventListener('paste', this.documentPasteEvent);
        this.editorDom.removeEventListener('cut', this.documentCutEvent);
        if (this.repasteDom) {
            this.repasteDom.removeEventListener('click', this.repasteEvent);
            this.repasteDialogDom.removeEventListener('click', this.repasteDialogEvent);
            this.repasteDialogDom.removeEventListener('keydown', this.stopProgapation);
            this._paseDom.outerHTML = '';
        }
        // this.copyDom.outerHTML = '';

        this.repasteDom = null;
        this.operatePanelDom = null;
        this.editorDom = null;
        this.copyDom = null;
    }
}
