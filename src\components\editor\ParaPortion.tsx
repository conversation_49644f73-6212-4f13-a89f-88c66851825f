import * as React from 'react';
import { IDocumentParaPortion } from '../../model/ParaPortionProperty';
import { TEXT_SCARE_NUM } from '../../model/StyleProperty';
import ParaElementBase from './ParaElementBase';
import { FONT_WEIGHT_TYPE, FONT_STYLE_TYPE, NewControlDefaultSetting, isMacOs } from '../../common/commonDefines';
import { fonts } from './text/font';
import { TextVertAlign } from '../../model/core/TextProperty';

// interface PageMenuState {
//   currentX?: number
// }

export default class ParaPortion extends React.Component<IDocumentParaPortion> { // }, PageMenuState> {

  constructor(props) {
    super(props);
    // console.log('ParaPortion---------------constructor----------------')
    // this.state = {}
  }

    renderContent() {
        const { content } = this.props;
        const startPos = this.props.startPos;
        const endPos = this.props.endPos;

        return content.map((item, index) => {
          if ( startPos <= index && endPos > index ) {
            const {
                // width,
                type,
                positionX,
                positionY,
                dy,
                bVisible,
                bViewSecret,
            } = item;
            return <ParaElementBase dy={dy} key={index} positionX={positionX}
              positionY={positionY} type={type} bVisible={bVisible} bViewSecret={bViewSecret} content={item.content}/>
          }
        });
    }

    render() {
      const textPro = this.props.textProperty;
      const vertAlign = textPro.vertAlign;
      const fontSize = (vertAlign === TextVertAlign.Sub || vertAlign === TextVertAlign.Super)
        ? textPro.fontSize / 2 * TEXT_SCARE_NUM : textPro.fontSize;
      const weight = FONT_WEIGHT_TYPE.get(this.props.textProperty.fontWeight);
      const fontStyle = FONT_STYLE_TYPE.get(this.props.textProperty.fontStyle);
      // const textDecoration = TEXT_DECORATION_LINE_TYPE.get(this.props.textProperty.textDecorationLine);

      // const isMacOs = (navigator.userAgent.toLowerCase().indexOf('mac') > -1);

      let fontFamily = this.props.textProperty.fontFamily;
      if (isMacOs) {
        // change render font family if mac
        fontFamily = JSON.parse(JSON.stringify(this.props.textProperty.fontFamily));
        const fontOptions = fonts[0].options;
        let index = -1;
        for (let i = 0; i < fontOptions.length; i++) {
          if (fontFamily === fontOptions[i].value) {
            index = i;
            break;
          }
        }
        if (index !== -1) {
          fontFamily = (fontOptions[index] as any).macValue;
        }
      }

      const { content } = this.props;
      const startPos = this.props.startPos;
      const endPos = this.props.endPos;

      let text = '';
      let posX = null;
      let posY = null;
      let dy = null;
      // let bNewControlContentSecret = false;
      let bNewControlBorder = false;
      let bNewControlBorderVisible = true;
      for (let index = 0, length = content.length; index < length; index++) {
        const item = content[index];

        bNewControlBorder = 1 === length && ( item.isNewControlStartBoder() || item.isNewControlEndBoder() );
        bNewControlBorderVisible = bNewControlBorder && item.isVisible();
        if ( startPos <= index && endPos > index ) {
            text += ( item.bViewSecret ? NewControlDefaultSetting.DefaultSecretReplaceChar : item.content );
            if ( null != posX ) {
              posX = posX + ' ' + item.positionX;
            } else {
              posX = item.positionX;
            }
            if ( null != posY /*&& posY !== item.positionY*/ ) {
              posY = posY + ' ' + item.positionY;
            } else {
              posY = item.positionY;
            }
            if ( null != dy && item.dy ) {
              dy = dy + ' ' + item.dy;
            } else {
              dy = item.dy;
            }
        }
      }

      if ( bNewControlBorder ) {
        if ( false === bNewControlBorderVisible ) {
          text = NewControlDefaultSetting.DefaultReplaceSpaceChar;
        }
        return <text x={posX} y={posY} textLength='2' lengthAdjust='spacingAndGlyphs' fill={NewControlDefaultSetting.DefaultNewControlBorderColor}
                fontStyle='normal' fontSize={fontSize} >{text}</text>;
      } else {
        return (
          <text x={posX} y={posY}
            fontFamily={fontFamily}
            fontSize={fontSize}
            fontWeight={weight}
            fontStyle={fontStyle}
            fill={this.props.textProperty.color}
            dy={dy}
          > {text}
          </text>
        );
      }

      // return (
      //   <tspan
      //     fontFamily={fontFamily}
      //     fontSize={fontSize}
      //     fontWeight={weight}
      //     fontStyle={fontStyle}
      //     fill={this.props.textProperty.color}
      //   > {this.renderContent()}
      //   </tspan>
      // );
    }

    // shouldComponentUpdate(nextProps, nextState) {
    //   console.log('aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa')
    //   // if ( true === this.props.dirty ) {
    //   if ( this.props.content.length === nextProps.content.length
    //       && this.props.positionX === nextProps.positionX
    //       && this.props.positionY === nextProps.positionY
    //       ) {
    //         //console.log('aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa')
    //       return false;
    //   }

    //   console.log('ppppppppppppppppppppppppppppppppppppppppppppppppp')
    //   return true;
    // }

  // componentWillUnmount() {
  //   console.log('ParaPortion--------componentWillUnmount---------------')
  // }
}
