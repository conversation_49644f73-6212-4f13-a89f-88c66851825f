import { HtmlModel } from '../../../common/export/html';
import { CleanModeType, getEditorDomContainer } from '../../../common/commonDefines';
import {PrintContent} from './PrintContent';
import ReactDOM from 'react-dom/client';
import * as React from 'react';
import { TableBase } from '../../../model/core/TableBase';

export class ExportHtml extends HtmlModel {
    constructor(host: any, bAutoTest?: boolean, cleanMode?: CleanModeType) {
        super(host);
        this.bAutoTest = bAutoTest;
        this.cleanMode = cleanMode;
    }

    public async export(): Promise<void> {
        await this.addIframe();
        this.exportContent();
    }

    public async exportAsString(clearMode?: CleanModeType,documentCore?: any): Promise<string> {
    if (documentCore) {
        this.setDocCore(documentCore);
    }
      await this.addIframe();
      let html = await this.getPager(clearMode);
      html = this.shrinkImage(html);
      this.reactVm.clearDom();
      this.reactVm = null;
      this.iframe.outerHTML = '';
      return html;
    }

    public async getExportHtml(): Promise<string> {
        await this.addIframe();
        const exportedHtml = await this.exportContent(true);
        if (typeof exportedHtml === 'string') {
            return exportedHtml;
        }
        return '';
    }

    public async exportAutoTestFiles(): Promise<string[]> {
        await this.addIframe();
        const content = await this.getSvgContent();
        // console.log(content)
        const htmlContent = this.generateCustomHtml(content);
        const structsContent = this.generateStructsAttrJson();
        const imageContent = this.generateImageAttrJson();
        const tableContent = this.generateTableAttrJson();
        // console.log(htmlContent)
        // console.log(tableContent)

        const result = [];
        result.push(htmlContent);
        result.push(structsContent);
        result.push(imageContent);
        result.push(tableContent);

        return result;
    }

    public async getHtml(): Promise<Blob> {
        await this.addIframe();
        return this.getContent();
    }

    public async getCleanHtml(): Promise<Blob> {
        await this.addIframe();
        return this.getCleanContent();
    }

    // private addIframe(): void {
    //     const src = window.location.href + 'about:link?time=' + (new Date()).getTime();
    //     const iframe = this.iframe = document.createElement('iframe');
    //     iframe.src = src;
    //     iframe.setAttribute('style', 'width: 100%; height: 100%; opacity: 1;');
    //     getEditorDomContainer()
    //     .appendChild(iframe);

    //     const iframeContent = iframe.contentWindow;
    //     const div = iframeContent.document.createElement('div');
    //     div.id = 'hz-editor-app';
    //     iframeContent.document.body.appendChild(div);
    //     const currentReact = ReactDOM.render(<PrintContent />, iframeContent['hz-editor-app']);
    //     this.reactVm = currentReact;
    // }
    private async addIframe(): Promise<void> {
        
        //const src = `${window.location.href}about:link?time=${new Date().getTime()}`;
        const src = 'about:blank';
        const iframe = this.iframe = document.createElement('iframe');
        iframe.src = src;
        iframe.setAttribute('style', 'width: 100%; height: 100%; opacity: 1;');
        getEditorDomContainer().appendChild(iframe);
    
        const iframeContent = iframe.contentWindow;
        if (iframeContent) {
          const div = iframeContent.document.createElement('div');
          div.id = 'hz-editor-app';
          iframeContent.document.body.appendChild(div);
    
          // 创建 React Root 并渲染组件
          const root = ReactDOM.createRoot(iframeContent.document.getElementById('hz-editor-app') as HTMLElement);
          root.render(<PrintContent ref={this.reactVm2} />);
    
          // 确保组件实例渲染完成后获取到值
          await new Promise<void>((resolve) => {
            setTimeout(() => {
              const instance = this.reactVm2.current;
              if (instance) {
                // 可以在这里调用 instance 的方法
                this.reactVm = instance;
                resolve(); // 解析 Promise
              } else {
                console.error('PrintContent instance is not available');
                resolve(); // 即使未找到实例也解析 Promise，防止阻塞
              }
            }, 100); // 适当的延迟确保组件渲染
          });
        } else {
          console.error('Iframe content window is not available');
        }
      }

    private generateCustomHtml(content: string): string {
        // const macFonts = ['STSong']; TODO
        const curContent = content.replace(/ clip-path=".+?"/g, ' clip-path=""')
            .replace(/ id=".+?"/g, ' id=""')
            .replace(/ x=".+?"/g, ' x=""')
            .replace(/ y=".+?"/g, ' y=""')
            .replace(/ x1=".+?"/g, ' x1=""')
            .replace(/ x2=".+?"/g, ' x2=""')
            .replace(/ y1=".+?"/g, ' y1=""')
            .replace(/ y2=".+?"/g, ' y2=""')
            .replace(/ points=".+?"/g, ' points=""')
            .replace(/ font-family=\"STSong\"/g, ' font-family=\"\u5b8b\u4f53\"') // 宋体
            // .replace(/ class=".+?"/g, ' class=""')
            ;
        return curContent;
    }

    private generateStructsAttrJson(): string {
        const host = this.getHost();
        const documentCore = this.getDocumentCore();
        const logicDocument = documentCore.getDocument();
        const newControlManager = logicDocument.getNewControlManager();
        const newControls = newControlManager.getAllNewControls();

        const result = {};
        for (const newControl of newControls) {
            const newControlName = newControl.getNewControlName();
            const newControlProps = newControl.getProperty();
            result[newControlName] = newControlProps;
        }
        // console.log(result)

        // what if new props? seems reasonable that do not touch here, but deal with it in compare
        return JSON.stringify(result);
    }

    private generateImageAttrJson(): string {
        // const host = this.getHost();
        const documentCore = this.getDocumentCore();
        // const logicDocument = documentCore.getDocument();
        const graphicObject = documentCore.getGraphicObject();
        // console.log(graphicObject)

        const result = {};
        for (const [name, paradrawing] of graphicObject.entries()) {
            // console.log(key + ' = ' + value)
            const props = paradrawing.getProps();
            result[name] = props;
        }
        // console.log(result)

        // what if new props? seems reasonable that do not touch here, but deal with it in compare
        return JSON.stringify(result);
    }

    private generateTableAttrJson(): string {
        const host = this.getHost();
        const documentCore = this.getDocumentCore();
        const logicDocument = documentCore.getDocument();
        const tableManager = logicDocument.getTableManager();
        const tables = tableManager.getTableMap();

        // table, row, cell(including borders)
        const result = {};
        for (const [name, table] of tables.entries()) {
            // result[name] = table;
            // retainTableProps
            const tableProps = this.retainTableProps(table);
            result[name] = tableProps;
        }

        // console.log(result)
        return JSON.stringify(result);
    }

    private retainTableProps(table: TableBase): any {
        // table, cell
        // it seems u dont need to compare border stuff, since they are diff in html already

        // console.log(table)
        const result: any = table.getTableProps();
        // const result: any = table.property;
        // console.log(result.content)

        for (const tableRow of table.content) {
            if (result.content == null) {
                result.content = [];
            }
            const customizedTableRow: any = tableRow.property;
            // cell
            if (customizedTableRow.content == null) {
                customizedTableRow.content = [];
            } else {
                // reset
                customizedTableRow.content.length = 0;
            }
            for (const tableCell of tableRow.content) {
                const customizedTableCell = tableCell.property;
                customizedTableRow.content.push(customizedTableCell);
            }
            result.content.push(customizedTableRow);
        }

        return result;
    }

}
