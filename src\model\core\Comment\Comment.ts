import {CommentData} from './CommentData';
import {ParagraphContentPos} from '../Paragraph/ParagraphContent';
import { idCounter } from '../util';
import { ResultType } from '../../../common/commonDefines';
import Document from '../Document';
import ParaPortion from '../Paragraph/ParaPortion';
export class Comment {
    private logicDocument: Document;
    private id: number;
    private bHiddenBackground: boolean;
    private bActive: boolean;
    private data: CommentData;
    private name: string;
    private startPortion: ParaPortion;
    private endPortion: ParaPortion;
    private startPos: ParagraphContentPos;
    private endPos: ParagraphContentPos;
    private canDelete: boolean;
    private canModify: boolean;
    constructor() {
        this.bHiddenBackground = false;
        this.bActive = false;
        this.id = idCounter.getNewId();
        this.data = new CommentData();
        this.canDelete = true;
        this.canModify = true;
    }

    public getId(): number {
        return this.id;
    }

    public getUserName(): string {
        return this.data.getUserName();
    }

    public setStartPortion(startPortion: ParaPortion): void {
        this.startPortion = startPortion;
    }

    public setEndPortion(endPortion: ParaPortion): void {
        this.endPortion = endPortion;
    }

    public setName(name: string): boolean {
        if (name === this.name) {
            return false;
        }
        this.name = name;
        return true;
    }

    public getName(): string {
        return this.name;
    }

    public setModify(flag: boolean): boolean {
        if (flag === this.canModify) {
            return false;
        }

        this.canModify = flag;
        return true;
    }

    public setDelete(flag: boolean): boolean {
        if (flag === this.canDelete) {
            return false;
        }

        this.canDelete = flag;
        return true;
    }

    public setLogicDocument(document: Document): void {
        this.logicDocument = document;
    }

    public isCanModify(): boolean {
        return this.canModify;
    }

    public isCanDelete(): boolean {
        return this.canDelete;
    }

    public getData(): CommentData {
        return this.data;
    }

    public getStartPos(): ParagraphContentPos {
        return this.startPos;
    }

    public setStartPos(startPos: ParagraphContentPos): void {
        this.startPos = startPos;
    }

    public getEndPos(): ParagraphContentPos {
        return this.endPos;
    }

    public setEndPos(endPos: ParagraphContentPos): void {
        this.endPos = endPos;
    }

    public setActive(bActive: boolean): void {
        this.bActive = bActive;
    }

    public isActived(): boolean {
        return this.bActive;
    }

    public isHiddenBackground(): boolean {
        return this.bHiddenBackground;
    }

    public setHiddenBackground( bHiddenBackground: boolean ): number {
        if ( bHiddenBackground === this.bHiddenBackground || undefined === bHiddenBackground ) {
            return ResultType.UnEdited;
        }

        this.bHiddenBackground = bHiddenBackground;

        return ResultType.Success;
    }

    public getStartPortion(): ParaPortion {
        // const pos = this.startPos.copy();
        // pos.update(undefined, pos.getDepth());
        return this.startPortion; // this.logicDocument.getElementByPos(this.startPos, false);
    }

    public getEndPortion(): ParaPortion {
        // const pos = this.endPos.copy();
        // pos.update(undefined, pos.getDepth());
        return this.endPortion; // this.logicDocument.getElementByPos(this.endPos, false);
    }

    public isHidden(): boolean {
        const element = this.logicDocument.content[this.endPos.get(2)];
        if (!element) {
            return false;
        }
        if (element.isHidden()) {
            return true;
        }
        const endProtion = this.getEndPortion();
        if (endProtion.isHidden()) {
            return true;
        }

        return false;
    }

    public isEmpty(): boolean {
        if (!this.startPortion || !this.endPortion) {
            return true;
        }
        const startPara = this.startPortion.getParagraph();
        const endPara = this.endPortion.getParagraph();
        if (startPara !== endPara) {
            return false;
        }
        for (let start = this.startPortion.getPosInParent(),
            end = this.endPortion.getPosInParent();
            start <= end;
            start++
        ) {
            if (!startPara.content[start].isEmptyContent()) {
                return false;
            }
        }
        return true;
    }

}
