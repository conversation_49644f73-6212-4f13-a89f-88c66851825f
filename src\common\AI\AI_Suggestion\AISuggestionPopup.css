.ai-suggestion-popup {
  position: absolute;
  z-index: 9999;
  min-width: 240px;
  max-width: 360px;
  max-height: 300px;
  background-color: #ffffff;
  border-radius: 6px;
  box-shadow: 0 4px 14px rgba(0, 0, 0, 0.12), 0 0 0 1px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  font-size: 13px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
  border: none;
  backdrop-filter: blur(8px);
  animation: ai-popup-fade-in 0.15s ease-out;
}

.ai-suggestion-popup-content {
  max-height: 300px;
  overflow-y: auto;
  padding: 6px 0;
  scrollbar-width: thin;
}

.ai-suggestion-item {
  padding: 6px 12px;
  cursor: pointer;
  line-height: 1.5;
  transition: all 0.15s ease-out;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #333333;
  display: flex;
  align-items: center;
  position: relative;
  border-left: 2px solid transparent;
}

.ai-suggestion-item:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.ai-suggestion-item.highlighted {
  background-color: rgba(66, 133, 244, 0.08);
  border-left: 2px solid #4285f4;
  color: #1a73e8;
}

/* 滚动条样式 */
.ai-suggestion-popup-content::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

.ai-suggestion-popup-content::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.15);
  border-radius: 4px;
}

.ai-suggestion-popup-content::-webkit-scrollbar-track {
  background-color: transparent;
}

/* 添加淡入动画 */
@keyframes ai-popup-fade-in {
  from {
    opacity: 0;
    transform: translateY(4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 添加键盘快捷键提示 */
.ai-suggestion-item::after {
  content: attr(data-shortcut);
  position: absolute;
  right: 8px;
  font-size: 11px;
  color: #999;
  opacity: 0;
  transition: opacity 0.2s;
}

.ai-suggestion-item:hover::after,
.ai-suggestion-item.highlighted::after {
  opacity: 1;
}

/* 添加图标空间 */
.ai-suggestion-item::before {
  content: '';
  display: inline-block;
  width: 16px;
  height: 16px;
  margin-right: 8px;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><path d="M12 16v-4"></path><path d="M12 8h.01"></path></svg>');
  background-repeat: no-repeat;
  background-position: center;
  opacity: 0.6;
}

/* 加载状态样式 */
.ai-suggestion-item.loading {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #666;
  cursor: default;
}

.ai-suggestion-item.loading::before {
  display: none;
}

.loading-spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid rgba(0, 0, 0, 0.1);
  border-left-color: #4285f4;
  border-radius: 50%;
  animation: spinner 1s linear infinite;
}

@keyframes spinner {
  to { transform: rotate(360deg); }
}

/* 错误状态样式 */
.ai-suggestion-item.error {
  color: #d93025;
  cursor: default;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.ai-suggestion-item.error::before {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="%23d93025" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><line x1="12" y1="8" x2="12" y2="12"></line><line x1="12" y1="16" x2="12.01" y2="16"></line></svg>');
}

.error-retry {
  margin-left: 10px;
  padding: 2px 8px;
  background-color: #f8f9fa;
  border: 1px solid #dadce0;
  border-radius: 4px;
  font-size: 12px;
  color: #3c4043;
  cursor: pointer;
}

.error-retry:hover {
  background-color: #f1f3f4;
}

/* 暗色模式支持 */
@media (prefers-color-scheme: dark) {
  .ai-suggestion-popup {
    background-color: #252526;
    box-shadow: 0 4px 14px rgba(0, 0, 0, 0.36), 0 0 0 1px rgba(255, 255, 255, 0.1);
  }
  
  .ai-suggestion-item {
    color: #e0e0e0;
  }
  
  .ai-suggestion-item:hover {
    background-color: rgba(255, 255, 255, 0.06);
  }
  
  .ai-suggestion-item.highlighted {
    background-color: rgba(66, 133, 244, 0.15);
    color: #4dabf7;
  }
}
