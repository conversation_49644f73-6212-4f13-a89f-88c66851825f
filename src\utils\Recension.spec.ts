import {EmrEditor} from '../components/editor/Main';
import IExternalInterface from '../common/external/IExternalInterface';
import { ResultType, ReviewType, RevisionStyle } from '../common/commonDefines';
import { editorInit } from './Main';

describe('revision start', () => {
    it ('revision state change', async () => {
        const test = await editorInit();
        const editorVm: EmrEditor = window['utilEditor'];
        const editor: IExternalInterface = editorVm.getEditor();
        editor.enableTrackRevisions(true);
        editor.setStructText('textedit5', 'abcd');
        const documentCore = editorVm.state.documentCore;
        const doc = documentCore.getDocument();
        const newControl = documentCore.getNewControlByName('textedit5');
        const startBorder = newControl.getStartBorderPortion();
        let para = startBorder.getParagragh();
        let contents = para.content;
        let index = contents.findIndex((item) => item === startBorder) + 1;
        let textPortion = contents[index];
        expect(textPortion.getReviewType())
        .toEqual(ReviewType.Add);
        editor.selectOneStruct('textedit6');
        const startPos = editor.getSelectionRangeStart();
        editor.delete();
        // newControl = documentCore.getNewControlByName('textedit6');
        // startBorder = newControl.getStartBorderPortion();
        // para = startBorder.getParagragh();
        // contents = para.content;
        // index = contents.findIndex((item) => item === startBorder) + 1;
        para = documentCore.getCurrentParagraph() as any;
        contents = para.content;
        const pos = (doc as any).getPosByPositionString(startPos);
        index = pos.get(pos.getDepth() - 1);
        textPortion = contents[index];
        expect(textPortion.getReviewType())
        .toEqual(ReviewType.Remove);
        editor.showRecension(2);
        expect(textPortion.isHidden()).toEqual(true);
        editor.showRecension(1);
        expect(textPortion.isHidden()).toEqual(false);
    });
});

describe('revision interface', () => {
    it('setRecensionInfo', async () => {
        const obj = await editorInit();
        const editorVm: EmrEditor = window['utilEditor'];
        const documentCore = editorVm.state.documentCore;
        const editor: IExternalInterface = editorVm.getEditor();
        const doc = documentCore.getDocument();
        const newControl = documentCore.getNewControlByName('textedit7');
        const startBorder = newControl.getStartBorderPortion();
        const para = startBorder.getParagragh();
        const contents = para.content;
        editor.enableTrackRevisions(true);
        editor.showRecension(1);

        expect(editor.setRecensionInfo(null, null, null, null, null))
        .toEqual(ResultType.Success);
        editor.setStructText('textedit7', 'abcd');
        let index = contents.findIndex((item) => item === startBorder) + 1;
        let textPortion = contents[index];
        let info = textPortion.getReviewInfo();

        expect(info.getColor()).toEqual('红色');
        expect(info.getUserName()).toEqual('');
        expect(info.getDescription()).toEqual('');
        expect(info.getUserId()).toEqual('');
        expect(info.getStyle()).toEqual(RevisionStyle.SingleLine);

        expect(editor.setRecensionInfo('msnk', '大神', '一切都那么牛掰', 2, '蓝色'))
        .toEqual(ResultType.Success);
        editor.setStructText('textedit7', 'wfwewgwef');
        index = contents.findIndex((item) => item === startBorder) + 1;
        textPortion = contents[index];

        info = textPortion.getReviewInfo();
        expect(info.getColor()).toEqual('蓝色');
        expect(info.getUserName()).toEqual('大神');
        expect(info.getDescription()).toEqual('一切都那么牛掰');
        expect(info.getUserId()).toEqual('msnk');
        expect(info.getStyle()).toEqual(RevisionStyle.DoubleLine);

        expect(editor.setRecensionInfo('msnk', '大神', '一切都那么牛掰', 0, '蓝色'))
        .toEqual(ResultType.ParamError);

        expect(editor.setRecensionInfo({} as any, '大神', '一切都那么牛掰', 2, '蓝色'))
        .toEqual(ResultType.ParamError);
    });

    it('enableTrackRevisions and getTrackRevisions', async () => {
        const obj = await editorInit();
        const editorVm: EmrEditor = window['utilEditor'];
        const documentCore = editorVm.state.documentCore;
        const editor: IExternalInterface = editorVm.getEditor();
        // const doc = documentCore.getDocument();
        editor.enableTrackRevisions(false);

        expect(editor.enableTrackRevisions(true))
        .toEqual(ResultType.Success);

        expect(editor.getTrackRevisions())
        .toEqual(true);

        expect(editor.enableTrackRevisions(false))
        .toEqual(ResultType.Success);

        expect(editor.getTrackRevisions())
        .toEqual(false);

        expect(editor.enableTrackRevisions(null))
        .toEqual(ResultType.ParamError);

        expect((editor as any).enableTrackRevisions())
        .toEqual(ResultType.ParamError);
    });

    it('showRecension and getRecensionShowState', async () => {
        const obj = await editorInit();
        const editorVm: EmrEditor = window['utilEditor'];
        const documentCore = editorVm.state.documentCore;
        const editor: IExternalInterface = editorVm.getEditor();
        const doc = documentCore.getDocument();
        editor.showRecension(1);
        expect(editor.showRecension(1))
        .toEqual(ResultType.UnEdited);
        expect(editor.getRecensionShowState())
        .toEqual(1);
        expect(editor.showRecension(2))
        .toEqual(ResultType.Success);
        expect(editor.getRecensionShowState())
        .toEqual(2);
        expect(editor.showRecension(1))
        .toEqual(ResultType.Success);
        expect(editor.showRecension(NaN))
        .toEqual(ResultType.ParamError);
        expect(editor.showRecension(0))
        .toEqual(ResultType.ParamError);
        expect(editor.showRecension({} as any))
        .toEqual(ResultType.ParamError);
    });

    // it('setRecensionInfo', async () => {
    //     const obj = await editorInit();
    //     const editorVm: EmrEditor = window['utilEditor'];
    //     const documentCore = editorVm.state.documentCore;
    //     const editor: IExternalInterface = editorVm.getEditor();
    //     const doc = documentCore.getDocument();
    // });

    // it('setRecensionInfo', async () => {
    //     const obj = await editorInit();
    //     const editorVm: EmrEditor = window['utilEditor'];
    //     const documentCore = editorVm.state.documentCore;
    //     const editor: IExternalInterface = editorVm.getEditor();
    //     const doc = documentCore.getDocument();
    // });

    // it('setRecensionInfo', async () => {
    //     const obj = await editorInit();
    //     const editorVm: EmrEditor = window['utilEditor'];
    //     const documentCore = editorVm.state.documentCore;
    //     const editor: IExternalInterface = editorVm.getEditor();
    //     const doc = documentCore.getDocument();
    // });
});
