var Module=typeof Module!=="undefined"?Module:{};var objAssign=Object.assign;var moduleOverrides=objAssign({},Module);var arguments_=[];var thisProgram="./this.program";var quit_=function(status,toThrow){throw toThrow};var ENVIRONMENT_IS_WEB=true;var ENVIRONMENT_IS_WORKER=false;var scriptDirectory="";function locateFile(path){if(Module["locateFile"]){return Module["locateFile"](path,scriptDirectory)}return scriptDirectory+path}var read_,readAsync,readBinary,setWindowTitle;if(ENVIRONMENT_IS_WEB||ENVIRONMENT_IS_WORKER){if(ENVIRONMENT_IS_WORKER){scriptDirectory=self.location.href}else if(typeof document!=="undefined"&&document.currentScript){scriptDirectory=document.currentScript.src}if(scriptDirectory.indexOf("blob:")!==0){scriptDirectory=scriptDirectory.substr(0,scriptDirectory.replace(/[?#].*/,"").lastIndexOf("/")+1)}else{scriptDirectory=""}{read_=function(url){var xhr=new XMLHttpRequest;xhr.open("GET",url,false);xhr.send(null);return xhr.responseText};if(ENVIRONMENT_IS_WORKER){readBinary=function(url){var xhr=new XMLHttpRequest;xhr.open("GET",url,false);xhr.responseType="arraybuffer";xhr.send(null);return new Uint8Array(xhr.response)}}readAsync=function(url,onload,onerror){var xhr=new XMLHttpRequest;xhr.open("GET",url,true);xhr.responseType="arraybuffer";xhr.onload=function(){if(xhr.status==200||xhr.status==0&&xhr.response){onload(xhr.response);return}onerror()};xhr.onerror=onerror;xhr.send(null)}}setWindowTitle=function(title){document.title=title}}else{}var out=Module["print"]||console.log.bind(console);var err=Module["printErr"]||console.warn.bind(console);objAssign(Module,moduleOverrides);moduleOverrides=null;if(Module["arguments"])arguments_=Module["arguments"];if(Module["thisProgram"])thisProgram=Module["thisProgram"];if(Module["quit"])quit_=Module["quit"];var POINTER_SIZE=4;function warnOnce(text){if(!warnOnce.shown)warnOnce.shown={};if(!warnOnce.shown[text]){warnOnce.shown[text]=1;err(text)}}function convertJsFunctionToWasm(func,sig){if(typeof WebAssembly.Function==="function"){var typeNames={"i":"i32","j":"i64","f":"f32","d":"f64"};var type={parameters:[],results:sig[0]=="v"?[]:[typeNames[sig[0]]]};for(var i=1;i<sig.length;++i){type.parameters.push(typeNames[sig[i]])}return new WebAssembly.Function(type,func)}var typeSection=[1,0,1,96];var sigRet=sig.slice(0,1);var sigParam=sig.slice(1);var typeCodes={"i":127,"j":126,"f":125,"d":124};typeSection.push(sigParam.length);for(var i=0;i<sigParam.length;++i){typeSection.push(typeCodes[sigParam[i]])}if(sigRet=="v"){typeSection.push(0)}else{typeSection=typeSection.concat([1,typeCodes[sigRet]])}typeSection[1]=typeSection.length-2;var bytes=new Uint8Array([0,97,115,109,1,0,0,0].concat(typeSection,[2,7,1,1,101,1,102,0,0,7,5,1,1,102,0,0]));var module=new WebAssembly.Module(bytes);var instance=new WebAssembly.Instance(module,{"e":{"f":func}});var wrappedFunc=instance.exports["f"];return wrappedFunc}var freeTableIndexes=[];var functionsInTableMap;function getEmptyTableSlot(){if(freeTableIndexes.length){return freeTableIndexes.pop()}try{wasmTable.grow(1)}catch(err){if(!(err instanceof RangeError)){throw err}throw"Unable to grow wasm table. Set ALLOW_TABLE_GROWTH."}return wasmTable.length-1}function updateTableMap(offset,count){for(var i=offset;i<offset+count;i++){var item=getWasmTableEntry(i);if(item){functionsInTableMap.set(item,i)}}}var tempRet0=0;var setTempRet0=function(value){tempRet0=value};var wasmBinary;if(Module["wasmBinary"])wasmBinary=Module["wasmBinary"];var noExitRuntime=Module["noExitRuntime"]||true;if(typeof WebAssembly!=="object"){abort("no native wasm support detected")}var wasmMemory;var ABORT=false;var EXITSTATUS;function assert(condition,text){if(!condition){abort(text)}}function getCFunc(ident){var func=Module["_"+ident];return func}function ccall(ident,returnType,argTypes,args,opts){var toC={"string":function(str){var ret=0;if(str!==null&&str!==undefined&&str!==0){var len=(str.length<<2)+1;ret=stackAlloc(len);stringToUTF8(str,ret,len)}return ret},"array":function(arr){var ret=stackAlloc(arr.length);writeArrayToMemory(arr,ret);return ret}};function convertReturnValue(ret){if(returnType==="string")return UTF8ToString(ret);if(returnType==="boolean")return Boolean(ret);return ret}var func=getCFunc(ident);var cArgs=[];var stack=0;if(args){for(var i=0;i<args.length;i++){var converter=toC[argTypes[i]];if(converter){if(stack===0)stack=stackSave();cArgs[i]=converter(args[i])}else{cArgs[i]=args[i]}}}var ret=func.apply(null,cArgs);function onDone(ret){if(stack!==0)stackRestore(stack);return convertReturnValue(ret)}ret=onDone(ret);return ret}var ALLOC_STACK=1;var UTF8Decoder=typeof TextDecoder!=="undefined"?new TextDecoder("utf8"):undefined;function UTF8ArrayToString(heap,idx,maxBytesToRead){var endIdx=idx+maxBytesToRead;var endPtr=idx;while(heap[endPtr]&&!(endPtr>=endIdx))++endPtr;if(endPtr-idx>16&&heap.subarray&&UTF8Decoder){return UTF8Decoder.decode(heap.subarray(idx,endPtr))}else{var str="";while(idx<endPtr){var u0=heap[idx++];if(!(u0&128)){str+=String.fromCharCode(u0);continue}var u1=heap[idx++]&63;if((u0&224)==192){str+=String.fromCharCode((u0&31)<<6|u1);continue}var u2=heap[idx++]&63;if((u0&240)==224){u0=(u0&15)<<12|u1<<6|u2}else{u0=(u0&7)<<18|u1<<12|u2<<6|heap[idx++]&63}if(u0<65536){str+=String.fromCharCode(u0)}else{var ch=u0-65536;str+=String.fromCharCode(55296|ch>>10,56320|ch&1023)}}}return str}function UTF8ToString(ptr,maxBytesToRead){return ptr?UTF8ArrayToString(HEAPU8,ptr,maxBytesToRead):""}function stringToUTF8Array(str,heap,outIdx,maxBytesToWrite){if(!(maxBytesToWrite>0))return 0;var startIdx=outIdx;var endIdx=outIdx+maxBytesToWrite-1;for(var i=0;i<str.length;++i){var u=str.charCodeAt(i);if(u>=55296&&u<=57343){var u1=str.charCodeAt(++i);u=65536+((u&1023)<<10)|u1&1023}if(u<=127){if(outIdx>=endIdx)break;heap[outIdx++]=u}else if(u<=2047){if(outIdx+1>=endIdx)break;heap[outIdx++]=192|u>>6;heap[outIdx++]=128|u&63}else if(u<=65535){if(outIdx+2>=endIdx)break;heap[outIdx++]=224|u>>12;heap[outIdx++]=128|u>>6&63;heap[outIdx++]=128|u&63}else{if(outIdx+3>=endIdx)break;heap[outIdx++]=240|u>>18;heap[outIdx++]=128|u>>12&63;heap[outIdx++]=128|u>>6&63;heap[outIdx++]=128|u&63}}heap[outIdx]=0;return outIdx-startIdx}function stringToUTF8(str,outPtr,maxBytesToWrite){return stringToUTF8Array(str,HEAPU8,outPtr,maxBytesToWrite)}function lengthBytesUTF8(str){var len=0;for(var i=0;i<str.length;++i){var u=str.charCodeAt(i);if(u>=55296&&u<=57343)u=65536+((u&1023)<<10)|str.charCodeAt(++i)&1023;if(u<=127)++len;else if(u<=2047)len+=2;else if(u<=65535)len+=3;else len+=4}return len}var UTF16Decoder=typeof TextDecoder!=="undefined"?new TextDecoder("utf-16le"):undefined;function allocateUTF8OnStack(str){var size=lengthBytesUTF8(str)+1;var ret=stackAlloc(size);stringToUTF8Array(str,HEAP8,ret,size);return ret}function writeArrayToMemory(array,buffer){HEAP8.set(array,buffer)}function writeAsciiToMemory(str,buffer,dontAddNull){for(var i=0;i<str.length;++i){HEAP8[buffer++>>0]=str.charCodeAt(i)}if(!dontAddNull)HEAP8[buffer>>0]=0}function alignUp(x,multiple){if(x%multiple>0){x+=multiple-x%multiple}return x}var buffer,HEAP8,HEAPU8,HEAP16,HEAPU16,HEAP32,HEAPU32,HEAPF32,HEAPF64;function updateGlobalBufferAndViews(buf){buffer=buf;Module["HEAP8"]=HEAP8=new Int8Array(buf);Module["HEAP16"]=HEAP16=new Int16Array(buf);Module["HEAP32"]=HEAP32=new Int32Array(buf);Module["HEAPU8"]=HEAPU8=new Uint8Array(buf);Module["HEAPU16"]=HEAPU16=new Uint16Array(buf);Module["HEAPU32"]=HEAPU32=new Uint32Array(buf);Module["HEAPF32"]=HEAPF32=new Float32Array(buf);Module["HEAPF64"]=HEAPF64=new Float64Array(buf)}var INITIAL_MEMORY=Module["INITIAL_MEMORY"]||16777216;var wasmTable;var __ATPRERUN__=[];var __ATINIT__=[];var __ATMAIN__=[];var __ATPOSTRUN__=[];var runtimeInitialized=false;var runtimeExited=false;var runtimeKeepaliveCounter=0;function keepRuntimeAlive(){return noExitRuntime||runtimeKeepaliveCounter>0}function preRun(){if(Module["preRun"]){if(typeof Module["preRun"]=="function")Module["preRun"]=[Module["preRun"]];while(Module["preRun"].length){addOnPreRun(Module["preRun"].shift())}}callRuntimeCallbacks(__ATPRERUN__)}function initRuntime(){runtimeInitialized=true;callRuntimeCallbacks(__ATINIT__)}function preMain(){callRuntimeCallbacks(__ATMAIN__)}function exitRuntime(){runtimeExited=true}function postRun(){if(Module["postRun"]){if(typeof Module["postRun"]=="function")Module["postRun"]=[Module["postRun"]];while(Module["postRun"].length){addOnPostRun(Module["postRun"].shift())}}callRuntimeCallbacks(__ATPOSTRUN__)}function addOnPreRun(cb){__ATPRERUN__.unshift(cb)}function addOnInit(cb){__ATINIT__.unshift(cb)}function addOnPostRun(cb){__ATPOSTRUN__.unshift(cb)}var runDependencies=0;var runDependencyWatcher=null;var dependenciesFulfilled=null;function addRunDependency(id){runDependencies++;if(Module["monitorRunDependencies"]){Module["monitorRunDependencies"](runDependencies)}}function removeRunDependency(id){runDependencies--;if(Module["monitorRunDependencies"]){Module["monitorRunDependencies"](runDependencies)}if(runDependencies==0){if(runDependencyWatcher!==null){clearInterval(runDependencyWatcher);runDependencyWatcher=null}if(dependenciesFulfilled){var callback=dependenciesFulfilled;dependenciesFulfilled=null;callback()}}}Module["preloadedImages"]={};Module["preloadedAudios"]={};function abort(what){{if(Module["onAbort"]){Module["onAbort"](what)}}what="Aborted("+what+")";err(what);ABORT=true;EXITSTATUS=1;what+=". Build with -s ASSERTIONS=1 for more info.";var e=new WebAssembly.RuntimeError(what);throw e}var dataURIPrefix="data:application/octet-stream;base64,";function isDataURI(filename){return filename.startsWith(dataURIPrefix)}var wasmBinaryFile;wasmBinaryFile="editor-Wasm-water.wasm";if(!isDataURI(wasmBinaryFile)){wasmBinaryFile=locateFile(wasmBinaryFile)}function getBinary(file){try{if(file==wasmBinaryFile&&wasmBinary){return new Uint8Array(wasmBinary)}if(readBinary){return readBinary(file)}else{throw"both async and sync fetching of the wasm failed"}}catch(err){abort(err)}}function getBinaryPromise(){if(!wasmBinary&&(ENVIRONMENT_IS_WEB||ENVIRONMENT_IS_WORKER)){if(typeof fetch==="function"){return fetch(wasmBinaryFile,{credentials:"same-origin"}).then(function(response){if(!response["ok"]){throw"failed to load wasm binary file at '"+wasmBinaryFile+"'"}return response["arrayBuffer"]()}).catch(function(){return getBinary(wasmBinaryFile)})}}return Promise.resolve().then(function(){return getBinary(wasmBinaryFile)})}function createWasm(){var info={"env":asmLibraryArg,"wasi_snapshot_preview1":asmLibraryArg};function receiveInstance(instance,module){var exports=instance.exports;Module["asm"]=exports;wasmMemory=Module["asm"]["memory"];updateGlobalBufferAndViews(wasmMemory.buffer);wasmTable=Module["asm"]["__indirect_function_table"];addOnInit(Module["asm"]["__wasm_call_ctors"]);removeRunDependency("wasm-instantiate")}addRunDependency("wasm-instantiate");function receiveInstantiationResult(result){receiveInstance(result["instance"])}function instantiateArrayBuffer(receiver){return getBinaryPromise().then(function(binary){return WebAssembly.instantiate(binary,info)}).then(function(instance){return instance}).then(receiver,function(reason){err("failed to asynchronously prepare wasm: "+reason);abort(reason)})}function instantiateAsync(){if(!wasmBinary&&typeof WebAssembly.instantiateStreaming==="function"&&!isDataURI(wasmBinaryFile)&&typeof fetch==="function"){return fetch(wasmBinaryFile,{credentials:"same-origin"}).then(function(response){var result=WebAssembly.instantiateStreaming(response,info);return result.then(receiveInstantiationResult,function(reason){err("wasm streaming compile failed: "+reason);err("falling back to ArrayBuffer instantiation");return instantiateArrayBuffer(receiveInstantiationResult)})})}else{return instantiateArrayBuffer(receiveInstantiationResult)}}if(Module["instantiateWasm"]){try{var exports=Module["instantiateWasm"](info,receiveInstance);return exports}catch(e){err("Module.instantiateWasm callback failed with error: "+e);return false}}instantiateAsync();return{}}var tempDouble;var tempI64;function callRuntimeCallbacks(callbacks){while(callbacks.length>0){var callback=callbacks.shift();if(typeof callback=="function"){callback(Module);continue}var func=callback.func;if(typeof func==="number"){if(callback.arg===undefined){getWasmTableEntry(func)()}else{getWasmTableEntry(func)(callback.arg)}}else{func(callback.arg===undefined?null:callback.arg)}}}function demangle(func){return func}function demangleAll(text){var regex=/\b_Z[\w\d_]+/g;return text.replace(regex,function(x){var y=demangle(x);return x===y?x:y+" ["+x+"]"})}var wasmTableMirror=[];function getWasmTableEntry(funcPtr){var func=wasmTableMirror[funcPtr];if(!func){if(funcPtr>=wasmTableMirror.length)wasmTableMirror.length=funcPtr+1;wasmTableMirror[funcPtr]=func=wasmTable.get(funcPtr)}return func}function handleException(e){if(e instanceof ExitStatus||e=="unwind"){return EXITSTATUS}quit_(1,e)}function jsStackTrace(){var error=new Error;if(!error.stack){try{throw new Error}catch(e){error=e}if(!error.stack){return"(no stack trace available)"}}return error.stack.toString()}function setWasmTableEntry(idx,func){wasmTable.set(idx,func);wasmTableMirror[idx]=func}function _StorageRead(key){var jsString;if(typeof window["editorCallback"].getItem==="function"){jsString=window["editorCallback"].getItem(UTF8ToString(key))}else{jsString=localStorage.getItem(UTF8ToString(key))}if(jsString===null)return null;var lengthBytes=lengthBytesUTF8(jsString)+1;var stringOnWasmHeap=_malloc(lengthBytes);stringToUTF8(jsString,stringOnWasmHeap,lengthBytes);return stringOnWasmHeap}function _StorageRemove(key){if(typeof window["editorCallback"].removeItem==="function"){window["editorCallback"].removeItem(UTF8ToString(key))}else{localStorage.removeItem(UTF8ToString(key))}}function _StorageWrite(key,value){if(typeof window["editorCallback"].setItem==="function"){window["editorCallback"].setItem(UTF8ToString(key),UTF8ToString(value))}else{localStorage.setItem(UTF8ToString(key),UTF8ToString(value))}}function _XHRGet(url,cb){function JS_XHRGet(url,cb){var request=new XMLHttpRequest;request.open("GET",url,true);request.responseType="text";request.url=url;request.wrap_cb=cb;request.onreadystatechange=function(){if(request.readyState==4){if(request.status==200){Module.ccall("XHROnMessage","null",["string","string","number"],[request.url,request.responseText,request.wrap_cb])}else{Module.ccall("XHROnError","null",["string","number","number"],[request.url,request.status,request.wrap_cb])}}};request.send()}return JS_XHRGet(UTF8ToString(url),cb)}function ___assert_fail(condition,filename,line,func){abort("Assertion failed: "+UTF8ToString(condition)+", at: "+[filename?UTF8ToString(filename):"unknown filename",line,func?UTF8ToString(func):"unknown function"])}function ___cxa_allocate_exception(size){return _malloc(size+16)+16}function _atexit(func,arg){}function ___cxa_atexit(a0,a1){return _atexit(a0,a1)}function ExceptionInfo(excPtr){this.excPtr=excPtr;this.ptr=excPtr-16;this.set_type=function(type){HEAP32[this.ptr+4>>2]=type};this.get_type=function(){return HEAP32[this.ptr+4>>2]};this.set_destructor=function(destructor){HEAP32[this.ptr+8>>2]=destructor};this.get_destructor=function(){return HEAP32[this.ptr+8>>2]};this.set_refcount=function(refcount){HEAP32[this.ptr>>2]=refcount};this.set_caught=function(caught){caught=caught?1:0;HEAP8[this.ptr+12>>0]=caught};this.get_caught=function(){return HEAP8[this.ptr+12>>0]!=0};this.set_rethrown=function(rethrown){rethrown=rethrown?1:0;HEAP8[this.ptr+13>>0]=rethrown};this.get_rethrown=function(){return HEAP8[this.ptr+13>>0]!=0};this.init=function(type,destructor){this.set_type(type);this.set_destructor(destructor);this.set_refcount(0);this.set_caught(false);this.set_rethrown(false)};this.add_ref=function(){var value=HEAP32[this.ptr>>2];HEAP32[this.ptr>>2]=value+1};this.release_ref=function(){var prev=HEAP32[this.ptr>>2];HEAP32[this.ptr>>2]=prev-1;return prev===1}}var exceptionLast=0;var uncaughtExceptionCount=0;function ___cxa_throw(ptr,type,destructor){var info=new ExceptionInfo(ptr);info.init(type,destructor);exceptionLast=ptr;uncaughtExceptionCount++;throw ptr}function _abort(){abort("")}var _emscripten_get_now;_emscripten_get_now=function(){return performance.now()};var _emscripten_get_now_is_monotonic=true;function setErrNo(value){HEAP32[___errno_location()>>2]=value;return value}function _clock_gettime(clk_id,tp){var now;if(clk_id===0){now=Date.now()}else if((clk_id===1||clk_id===4)&&_emscripten_get_now_is_monotonic){now=_emscripten_get_now()}else{setErrNo(28);return-1}HEAP32[tp>>2]=now/1e3|0;HEAP32[tp+4>>2]=now%1e3*1e3*1e3|0;return 0}function _dispatchEditorEvent(value){var callback=window["editorCallback"];if(typeof callback==="function"){callback(value)}}function _dispatchEditorEvent2(value){var callback=window["editorCallback"];if(typeof callback==="function"){callback.triggerText(UTF8ToString(value))}}function _emscripten_clear_interval(id){clearInterval(id)}function _emscripten_memcpy_big(dest,src,num){HEAPU8.copyWithin(dest,src,src+num)}function emscripten_realloc_buffer(size){try{wasmMemory.grow(size-buffer.byteLength+65535>>>16);updateGlobalBufferAndViews(wasmMemory.buffer);return 1}catch(e){}}function _emscripten_resize_heap(requestedSize){var oldSize=HEAPU8.length;requestedSize=requestedSize>>>0;var maxHeapSize=2147483648;if(requestedSize>maxHeapSize){return false}for(var cutDown=1;cutDown<=4;cutDown*=2){var overGrownHeapSize=oldSize*(1+.2/cutDown);overGrownHeapSize=Math.min(overGrownHeapSize,requestedSize+100663296);var newSize=Math.min(maxHeapSize,alignUp(Math.max(requestedSize,overGrownHeapSize),65536));var replacement=emscripten_realloc_buffer(newSize);if(replacement){return true}}return false}function callUserCallback(func,synchronous){if(runtimeExited||ABORT){return}if(synchronous){func();return}try{func()}catch(e){handleException(e)}}function _emscripten_set_interval(cb,msecs,userData){return setInterval(function(){callUserCallback(function(){getWasmTableEntry(cb)(userData)})},msecs)}var ENV={};function getExecutableName(){return thisProgram||"./this.program"}function getEnvStrings(){if(!getEnvStrings.strings){var lang=(typeof navigator==="object"&&navigator.languages&&navigator.languages[0]||"C").replace("-","_")+".UTF-8";var env={"USER":"web_user","LOGNAME":"web_user","PATH":"/","PWD":"/","HOME":"/home/<USER>","LANG":lang,"_":getExecutableName()};for(var x in ENV){if(ENV[x]===undefined)delete env[x];else env[x]=ENV[x]}var strings=[];for(var x in env){strings.push(x+"="+env[x])}getEnvStrings.strings=strings}return getEnvStrings.strings}var SYSCALLS={mappings:{},buffers:[null,[],[]],printChar:function(stream,curr){var buffer=SYSCALLS.buffers[stream];if(curr===0||curr===10){(stream===1?out:err)(UTF8ArrayToString(buffer,0));buffer.length=0}else{buffer.push(curr)}},varargs:undefined,get:function(){SYSCALLS.varargs+=4;var ret=HEAP32[SYSCALLS.varargs-4>>2];return ret},getStr:function(ptr){var ret=UTF8ToString(ptr);return ret},get64:function(low,high){return low}};function _environ_get(__environ,environ_buf){var bufSize=0;getEnvStrings().forEach(function(string,i){var ptr=environ_buf+bufSize;HEAP32[__environ+i*4>>2]=ptr;writeAsciiToMemory(string,ptr);bufSize+=string.length+1});return 0}function _environ_sizes_get(penviron_count,penviron_buf_size){var strings=getEnvStrings();HEAP32[penviron_count>>2]=strings.length;var bufSize=0;strings.forEach(function(string){bufSize+=string.length+1});HEAP32[penviron_buf_size>>2]=bufSize;return 0}function _fd_close(fd){return 0}function _fd_seek(fd,offset_low,offset_high,whence,newOffset){}function _fd_write(fd,iov,iovcnt,pnum){var num=0;for(var i=0;i<iovcnt;i++){var ptr=HEAP32[iov>>2];var len=HEAP32[iov+4>>2];iov+=8;for(var j=0;j<len;j++){SYSCALLS.printChar(fd,HEAPU8[ptr+j])}num+=len}HEAP32[pnum>>2]=num;return 0}function _setTempRet0(val){setTempRet0(val)}function __isLeapYear(year){return year%4===0&&(year%100!==0||year%400===0)}function __arraySum(array,index){var sum=0;for(var i=0;i<=index;sum+=array[i++]){}return sum}var __MONTH_DAYS_LEAP=[31,29,31,30,31,30,31,31,30,31,30,31];var __MONTH_DAYS_REGULAR=[31,28,31,30,31,30,31,31,30,31,30,31];function __addDays(date,days){var newDate=new Date(date.getTime());while(days>0){var leap=__isLeapYear(newDate.getFullYear());var currentMonth=newDate.getMonth();var daysInCurrentMonth=(leap?__MONTH_DAYS_LEAP:__MONTH_DAYS_REGULAR)[currentMonth];if(days>daysInCurrentMonth-newDate.getDate()){days-=daysInCurrentMonth-newDate.getDate()+1;newDate.setDate(1);if(currentMonth<11){newDate.setMonth(currentMonth+1)}else{newDate.setMonth(0);newDate.setFullYear(newDate.getFullYear()+1)}}else{newDate.setDate(newDate.getDate()+days);return newDate}}return newDate}function _strftime(s,maxsize,format,tm){var tm_zone=HEAP32[tm+40>>2];var date={tm_sec:HEAP32[tm>>2],tm_min:HEAP32[tm+4>>2],tm_hour:HEAP32[tm+8>>2],tm_mday:HEAP32[tm+12>>2],tm_mon:HEAP32[tm+16>>2],tm_year:HEAP32[tm+20>>2],tm_wday:HEAP32[tm+24>>2],tm_yday:HEAP32[tm+28>>2],tm_isdst:HEAP32[tm+32>>2],tm_gmtoff:HEAP32[tm+36>>2],tm_zone:tm_zone?UTF8ToString(tm_zone):""};var pattern=UTF8ToString(format);var EXPANSION_RULES_1={"%c":"%a %b %d %H:%M:%S %Y","%D":"%m/%d/%y","%F":"%Y-%m-%d","%h":"%b","%r":"%I:%M:%S %p","%R":"%H:%M","%T":"%H:%M:%S","%x":"%m/%d/%y","%X":"%H:%M:%S","%Ec":"%c","%EC":"%C","%Ex":"%m/%d/%y","%EX":"%H:%M:%S","%Ey":"%y","%EY":"%Y","%Od":"%d","%Oe":"%e","%OH":"%H","%OI":"%I","%Om":"%m","%OM":"%M","%OS":"%S","%Ou":"%u","%OU":"%U","%OV":"%V","%Ow":"%w","%OW":"%W","%Oy":"%y"};for(var rule in EXPANSION_RULES_1){pattern=pattern.replace(new RegExp(rule,"g"),EXPANSION_RULES_1[rule])}var WEEKDAYS=["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"];var MONTHS=["January","February","March","April","May","June","July","August","September","October","November","December"];function leadingSomething(value,digits,character){var str=typeof value==="number"?value.toString():value||"";while(str.length<digits){str=character[0]+str}return str}function leadingNulls(value,digits){return leadingSomething(value,digits,"0")}function compareByDay(date1,date2){function sgn(value){return value<0?-1:value>0?1:0}var compare;if((compare=sgn(date1.getFullYear()-date2.getFullYear()))===0){if((compare=sgn(date1.getMonth()-date2.getMonth()))===0){compare=sgn(date1.getDate()-date2.getDate())}}return compare}function getFirstWeekStartDate(janFourth){switch(janFourth.getDay()){case 0:return new Date(janFourth.getFullYear()-1,11,29);case 1:return janFourth;case 2:return new Date(janFourth.getFullYear(),0,3);case 3:return new Date(janFourth.getFullYear(),0,2);case 4:return new Date(janFourth.getFullYear(),0,1);case 5:return new Date(janFourth.getFullYear()-1,11,31);case 6:return new Date(janFourth.getFullYear()-1,11,30)}}function getWeekBasedYear(date){var thisDate=__addDays(new Date(date.tm_year+1900,0,1),date.tm_yday);var janFourthThisYear=new Date(thisDate.getFullYear(),0,4);var janFourthNextYear=new Date(thisDate.getFullYear()+1,0,4);var firstWeekStartThisYear=getFirstWeekStartDate(janFourthThisYear);var firstWeekStartNextYear=getFirstWeekStartDate(janFourthNextYear);if(compareByDay(firstWeekStartThisYear,thisDate)<=0){if(compareByDay(firstWeekStartNextYear,thisDate)<=0){return thisDate.getFullYear()+1}else{return thisDate.getFullYear()}}else{return thisDate.getFullYear()-1}}var EXPANSION_RULES_2={"%a":function(date){return WEEKDAYS[date.tm_wday].substring(0,3)},"%A":function(date){return WEEKDAYS[date.tm_wday]},"%b":function(date){return MONTHS[date.tm_mon].substring(0,3)},"%B":function(date){return MONTHS[date.tm_mon]},"%C":function(date){var year=date.tm_year+1900;return leadingNulls(year/100|0,2)},"%d":function(date){return leadingNulls(date.tm_mday,2)},"%e":function(date){return leadingSomething(date.tm_mday,2," ")},"%g":function(date){return getWeekBasedYear(date).toString().substring(2)},"%G":function(date){return getWeekBasedYear(date)},"%H":function(date){return leadingNulls(date.tm_hour,2)},"%I":function(date){var twelveHour=date.tm_hour;if(twelveHour==0)twelveHour=12;else if(twelveHour>12)twelveHour-=12;return leadingNulls(twelveHour,2)},"%j":function(date){return leadingNulls(date.tm_mday+__arraySum(__isLeapYear(date.tm_year+1900)?__MONTH_DAYS_LEAP:__MONTH_DAYS_REGULAR,date.tm_mon-1),3)},"%m":function(date){return leadingNulls(date.tm_mon+1,2)},"%M":function(date){return leadingNulls(date.tm_min,2)},"%n":function(){return"\n"},"%p":function(date){if(date.tm_hour>=0&&date.tm_hour<12){return"AM"}else{return"PM"}},"%S":function(date){return leadingNulls(date.tm_sec,2)},"%t":function(){return"\t"},"%u":function(date){return date.tm_wday||7},"%U":function(date){var janFirst=new Date(date.tm_year+1900,0,1);var firstSunday=janFirst.getDay()===0?janFirst:__addDays(janFirst,7-janFirst.getDay());var endDate=new Date(date.tm_year+1900,date.tm_mon,date.tm_mday);if(compareByDay(firstSunday,endDate)<0){var februaryFirstUntilEndMonth=__arraySum(__isLeapYear(endDate.getFullYear())?__MONTH_DAYS_LEAP:__MONTH_DAYS_REGULAR,endDate.getMonth()-1)-31;var firstSundayUntilEndJanuary=31-firstSunday.getDate();var days=firstSundayUntilEndJanuary+februaryFirstUntilEndMonth+endDate.getDate();return leadingNulls(Math.ceil(days/7),2)}return compareByDay(firstSunday,janFirst)===0?"01":"00"},"%V":function(date){var janFourthThisYear=new Date(date.tm_year+1900,0,4);var janFourthNextYear=new Date(date.tm_year+1901,0,4);var firstWeekStartThisYear=getFirstWeekStartDate(janFourthThisYear);var firstWeekStartNextYear=getFirstWeekStartDate(janFourthNextYear);var endDate=__addDays(new Date(date.tm_year+1900,0,1),date.tm_yday);if(compareByDay(endDate,firstWeekStartThisYear)<0){return"53"}if(compareByDay(firstWeekStartNextYear,endDate)<=0){return"01"}var daysDifference;if(firstWeekStartThisYear.getFullYear()<date.tm_year+1900){daysDifference=date.tm_yday+32-firstWeekStartThisYear.getDate()}else{daysDifference=date.tm_yday+1-firstWeekStartThisYear.getDate()}return leadingNulls(Math.ceil(daysDifference/7),2)},"%w":function(date){return date.tm_wday},"%W":function(date){var janFirst=new Date(date.tm_year,0,1);var firstMonday=janFirst.getDay()===1?janFirst:__addDays(janFirst,janFirst.getDay()===0?1:7-janFirst.getDay()+1);var endDate=new Date(date.tm_year+1900,date.tm_mon,date.tm_mday);if(compareByDay(firstMonday,endDate)<0){var februaryFirstUntilEndMonth=__arraySum(__isLeapYear(endDate.getFullYear())?__MONTH_DAYS_LEAP:__MONTH_DAYS_REGULAR,endDate.getMonth()-1)-31;var firstMondayUntilEndJanuary=31-firstMonday.getDate();var days=firstMondayUntilEndJanuary+februaryFirstUntilEndMonth+endDate.getDate();return leadingNulls(Math.ceil(days/7),2)}return compareByDay(firstMonday,janFirst)===0?"01":"00"},"%y":function(date){return(date.tm_year+1900).toString().substring(2)},"%Y":function(date){return date.tm_year+1900},"%z":function(date){var off=date.tm_gmtoff;var ahead=off>=0;off=Math.abs(off)/60;off=off/60*100+off%60;return(ahead?"+":"-")+String("0000"+off).slice(-4)},"%Z":function(date){return date.tm_zone},"%%":function(){return"%"}};for(var rule in EXPANSION_RULES_2){if(pattern.includes(rule)){pattern=pattern.replace(new RegExp(rule,"g"),EXPANSION_RULES_2[rule](date))}}var bytes=intArrayFromString(pattern,false);if(bytes.length>maxsize){return 0}writeArrayToMemory(bytes,s);return bytes.length-1}function _strftime_l(s,maxsize,format,tm){return _strftime(s,maxsize,format,tm)}function _time(ptr){var ret=Date.now()/1e3|0;if(ptr){HEAP32[ptr>>2]=ret}return ret}var ASSERTIONS=false;function intArrayFromString(stringy,dontAddNull,length){var len=length>0?length:lengthBytesUTF8(stringy)+1;var u8array=new Array(len);var numBytesWritten=stringToUTF8Array(stringy,u8array,0,u8array.length);if(dontAddNull)u8array.length=numBytesWritten;return u8array}var asmLibraryArg={"StorageRead":_StorageRead,"StorageRemove":_StorageRemove,"StorageWrite":_StorageWrite,"XHRGet":_XHRGet,"__assert_fail":___assert_fail,"__cxa_allocate_exception":___cxa_allocate_exception,"__cxa_atexit":___cxa_atexit,"__cxa_throw":___cxa_throw,"abort":_abort,"clock_gettime":_clock_gettime,"dispatchEditorEvent":_dispatchEditorEvent,"dispatchEditorEvent2":_dispatchEditorEvent2,"emscripten_clear_interval":_emscripten_clear_interval,"emscripten_memcpy_big":_emscripten_memcpy_big,"emscripten_resize_heap":_emscripten_resize_heap,"emscripten_set_interval":_emscripten_set_interval,"environ_get":_environ_get,"environ_sizes_get":_environ_sizes_get,"fd_close":_fd_close,"fd_seek":_fd_seek,"fd_write":_fd_write,"setTempRet0":_setTempRet0,"strftime_l":_strftime_l,"time":_time};var asm=createWasm();var ___wasm_call_ctors=Module["___wasm_call_ctors"]=function(){return(___wasm_call_ctors=Module["___wasm_call_ctors"]=Module["asm"]["__wasm_call_ctors"]).apply(null,arguments)};var _main=Module["_main"]=function(){return(_main=Module["_main"]=Module["asm"]["main"]).apply(null,arguments)};var _SetRemoteUrl=Module["_SetRemoteUrl"]=function(){return(_SetRemoteUrl=Module["_SetRemoteUrl"]=Module["asm"]["SetRemoteUrl"]).apply(null,arguments)};var _XHROnMessage=Module["_XHROnMessage"]=function(){return(_XHROnMessage=Module["_XHROnMessage"]=Module["asm"]["XHROnMessage"]).apply(null,arguments)};var _XHROnError=Module["_XHROnError"]=function(){return(_XHROnError=Module["_XHROnError"]=Module["asm"]["XHROnError"]).apply(null,arguments)};var _MultiThreadedDemo=Module["_MultiThreadedDemo"]=function(){return(_MultiThreadedDemo=Module["_MultiThreadedDemo"]=Module["asm"]["MultiThreadedDemo"]).apply(null,arguments)};var _MMI=Module["_MMI"]=function(){return(_MMI=Module["_MMI"]=Module["asm"]["MMI"]).apply(null,arguments)};var _MMX=Module["_MMX"]=function(){return(_MMX=Module["_MMX"]=Module["asm"]["MMX"]).apply(null,arguments)};var _MFL3=Module["_MFL3"]=function(){return(_MFL3=Module["_MFL3"]=Module["asm"]["MFL3"]).apply(null,arguments)};var _MFL2=Module["_MFL2"]=function(){return(_MFL2=Module["_MFL2"]=Module["asm"]["MFL2"]).apply(null,arguments)};var _MFL1=Module["_MFL1"]=function(){return(_MFL1=Module["_MFL1"]=Module["asm"]["MFL1"]).apply(null,arguments)};var _FLEq=Module["_FLEq"]=function(){return(_FLEq=Module["_FLEq"]=Module["asm"]["FLEq"]).apply(null,arguments)};var _YYS=Module["_YYS"]=function(){return(_YYS=Module["_YYS"]=Module["asm"]["YYS"]).apply(null,arguments)};var _Paragraph_addNewControl_endPos=Module["_Paragraph_addNewControl_endPos"]=function(){return(_Paragraph_addNewControl_endPos=Module["_Paragraph_addNewControl_endPos"]=Module["asm"]["Paragraph_addNewControl_endPos"]).apply(null,arguments)};var _Paragraph_addNewControl_CurPos=Module["_Paragraph_addNewControl_CurPos"]=function(){return(_Paragraph_addNewControl_CurPos=Module["_Paragraph_addNewControl_CurPos"]=Module["asm"]["Paragraph_addNewControl_CurPos"]).apply(null,arguments)};var _Paragraph_addNewControl_Judge=Module["_Paragraph_addNewControl_Judge"]=function(){return(_Paragraph_addNewControl_Judge=Module["_Paragraph_addNewControl_Judge"]=Module["asm"]["Paragraph_addNewControl_Judge"]).apply(null,arguments)};var _Paragraph_add_Judge=Module["_Paragraph_add_Judge"]=function(){return(_Paragraph_add_Judge=Module["_Paragraph_add_Judge"]=Module["asm"]["Paragraph_add_Judge"]).apply(null,arguments)};var _Paragraph_perpareParaSpacing_Count=Module["_Paragraph_perpareParaSpacing_Count"]=function(){return(_Paragraph_perpareParaSpacing_Count=Module["_Paragraph_perpareParaSpacing_Count"]=Module["asm"]["Paragraph_perpareParaSpacing_Count"]).apply(null,arguments)};var _SectionInfo_getIndex=Module["_SectionInfo_getIndex"]=function(){return(_SectionInfo_getIndex=Module["_SectionInfo_getIndex"]=Module["asm"]["SectionInfo_getIndex"]).apply(null,arguments)};var _Table_isEmptyPage=Module["_Table_isEmptyPage"]=function(){return(_Table_isEmptyPage=Module["_Table_isEmptyPage"]=Module["asm"]["Table_isEmptyPage"]).apply(null,arguments)};var _Table_checkEmptyPages=Module["_Table_checkEmptyPages"]=function(){return(_Table_checkEmptyPages=Module["_Table_checkEmptyPages"]=Module["asm"]["Table_checkEmptyPages"]).apply(null,arguments)};var _Table_isSelectionEmpty=Module["_Table_isSelectionEmpty"]=function(){return(_Table_isSelectionEmpty=Module["_Table_isSelectionEmpty"]=Module["asm"]["Table_isSelectionEmpty"]).apply(null,arguments)};var _Table_isCellSelection=Module["_Table_isCellSelection"]=function(){return(_Table_isCellSelection=Module["_Table_isCellSelection"]=Module["asm"]["Table_isCellSelection"]).apply(null,arguments)};var _Table_canMergeTableCells=Module["_Table_canMergeTableCells"]=function(){return(_Table_canMergeTableCells=Module["_Table_canMergeTableCells"]=Module["asm"]["Table_canMergeTableCells"]).apply(null,arguments)};var _Table_splitTableCells=Module["_Table_splitTableCells"]=function(){return(_Table_splitTableCells=Module["_Table_splitTableCells"]=Module["asm"]["Table_splitTableCells"]).apply(null,arguments)};var _Table_CellsJudge=Module["_Table_CellsJudge"]=function(){return(_Table_CellsJudge=Module["_Table_CellsJudge"]=Module["asm"]["Table_CellsJudge"]).apply(null,arguments)};var _Table_setParagraphProperty=Module["_Table_setParagraphProperty"]=function(){return(_Table_setParagraphProperty=Module["_Table_setParagraphProperty"]=Module["asm"]["Table_setParagraphProperty"]).apply(null,arguments)};var _Table_createNewGrid_Judge=Module["_Table_createNewGrid_Judge"]=function(){return(_Table_createNewGrid_Judge=Module["_Table_createNewGrid_Judge"]=Module["asm"]["Table_createNewGrid_Judge"]).apply(null,arguments)};var _Util_getAcitveNodeIndex=Module["_Util_getAcitveNodeIndex"]=function(){return(_Util_getAcitveNodeIndex=Module["_Util_getAcitveNodeIndex"]=Module["asm"]["Util_getAcitveNodeIndex"]).apply(null,arguments)};var _Util_getPM=Module["_Util_getPM"]=function(){return(_Util_getPM=Module["_Util_getPM"]=Module["asm"]["Util_getPM"]).apply(null,arguments)};var _Util_getMP=Module["_Util_getMP"]=function(){return(_Util_getMP=Module["_Util_getMP"]=Module["asm"]["Util_getMP"]).apply(null,arguments)};var _ParagraphRecalculate_recalculateFastRange=Module["_ParagraphRecalculate_recalculateFastRange"]=function(){return(_ParagraphRecalculate_recalculateFastRange=Module["_ParagraphRecalculate_recalculateFastRange"]=Module["asm"]["ParagraphRecalculate_recalculateFastRange"]).apply(null,arguments)};var _ParagraphRecalculate_recalculateFastRangeWithInfo=Module["_ParagraphRecalculate_recalculateFastRangeWithInfo"]=function(){return(_ParagraphRecalculate_recalculateFastRangeWithInfo=Module["_ParagraphRecalculate_recalculateFastRangeWithInfo"]=Module["asm"]["ParagraphRecalculate_recalculateFastRangeWithInfo"]).apply(null,arguments)};var _ParagraphRecalculate_recalculateFastWholeParagraph=Module["_ParagraphRecalculate_recalculateFastWholeParagraph"]=function(){return(_ParagraphRecalculate_recalculateFastWholeParagraph=Module["_ParagraphRecalculate_recalculateFastWholeParagraph"]=Module["asm"]["ParagraphRecalculate_recalculateFastWholeParagraph"]).apply(null,arguments)};var _ParagraphRecalculate_recalculateFastWholeParagraph2=Module["_ParagraphRecalculate_recalculateFastWholeParagraph2"]=function(){return(_ParagraphRecalculate_recalculateFastWholeParagraph2=Module["_ParagraphRecalculate_recalculateFastWholeParagraph2"]=Module["asm"]["ParagraphRecalculate_recalculateFastWholeParagraph2"]).apply(null,arguments)};var _ParagraphRecalculate_recalculateFastWholeParagraph3=Module["_ParagraphRecalculate_recalculateFastWholeParagraph3"]=function(){return(_ParagraphRecalculate_recalculateFastWholeParagraph3=Module["_ParagraphRecalculate_recalculateFastWholeParagraph3"]=Module["asm"]["ParagraphRecalculate_recalculateFastWholeParagraph3"]).apply(null,arguments)};var _ParagraphRecalculate_recalculateFastWholeParagraph4=Module["_ParagraphRecalculate_recalculateFastWholeParagraph4"]=function(){return(_ParagraphRecalculate_recalculateFastWholeParagraph4=Module["_ParagraphRecalculate_recalculateFastWholeParagraph4"]=Module["asm"]["ParagraphRecalculate_recalculateFastWholeParagraph4"]).apply(null,arguments)};var _ParagraphRecalculate_recalculateFastWholeParagraphWithInfo=Module["_ParagraphRecalculate_recalculateFastWholeParagraphWithInfo"]=function(){return(_ParagraphRecalculate_recalculateFastWholeParagraphWithInfo=Module["_ParagraphRecalculate_recalculateFastWholeParagraphWithInfo"]=Module["asm"]["ParagraphRecalculate_recalculateFastWholeParagraphWithInfo"]).apply(null,arguments)};var _ParagraphRecalculate_recalculateFastWholeParagraph2WithInfo=Module["_ParagraphRecalculate_recalculateFastWholeParagraph2WithInfo"]=function(){return(_ParagraphRecalculate_recalculateFastWholeParagraph2WithInfo=Module["_ParagraphRecalculate_recalculateFastWholeParagraph2WithInfo"]=Module["asm"]["ParagraphRecalculate_recalculateFastWholeParagraph2WithInfo"]).apply(null,arguments)};var _ParagraphRecalculate_recalculateFastWholeParagraph3WithInfo=Module["_ParagraphRecalculate_recalculateFastWholeParagraph3WithInfo"]=function(){return(_ParagraphRecalculate_recalculateFastWholeParagraph3WithInfo=Module["_ParagraphRecalculate_recalculateFastWholeParagraph3WithInfo"]=Module["asm"]["ParagraphRecalculate_recalculateFastWholeParagraph3WithInfo"]).apply(null,arguments)};var _ParagraphRecalculate_recalculateFastWholeParagraph4WithInfo=Module["_ParagraphRecalculate_recalculateFastWholeParagraph4WithInfo"]=function(){return(_ParagraphRecalculate_recalculateFastWholeParagraph4WithInfo=Module["_ParagraphRecalculate_recalculateFastWholeParagraph4WithInfo"]=Module["asm"]["ParagraphRecalculate_recalculateFastWholeParagraph4WithInfo"]).apply(null,arguments)};var _ParagraphRecalculate_recalculateLineFillRanges1=Module["_ParagraphRecalculate_recalculateLineFillRanges1"]=function(){return(_ParagraphRecalculate_recalculateLineFillRanges1=Module["_ParagraphRecalculate_recalculateLineFillRanges1"]=Module["asm"]["ParagraphRecalculate_recalculateLineFillRanges1"]).apply(null,arguments)};var _ParagraphRecalculate_recalculateLineFillRanges2=Module["_ParagraphRecalculate_recalculateLineFillRanges2"]=function(){return(_ParagraphRecalculate_recalculateLineFillRanges2=Module["_ParagraphRecalculate_recalculateLineFillRanges2"]=Module["asm"]["ParagraphRecalculate_recalculateLineFillRanges2"]).apply(null,arguments)};var _ParagraphRecalculate_recalculatePage=Module["_ParagraphRecalculate_recalculatePage"]=function(){return(_ParagraphRecalculate_recalculatePage=Module["_ParagraphRecalculate_recalculatePage"]=Module["asm"]["ParagraphRecalculate_recalculatePage"]).apply(null,arguments)};var _ParagraphRecalculate_recalculateLineRanges=Module["_ParagraphRecalculate_recalculateLineRanges"]=function(){return(_ParagraphRecalculate_recalculateLineRanges=Module["_ParagraphRecalculate_recalculateLineRanges"]=Module["asm"]["ParagraphRecalculate_recalculateLineRanges"]).apply(null,arguments)};var _ParagraphRecalculate_recalculateLinePosition=Module["_ParagraphRecalculate_recalculateLinePosition"]=function(){return(_ParagraphRecalculate_recalculateLinePosition=Module["_ParagraphRecalculate_recalculateLinePosition"]=Module["asm"]["ParagraphRecalculate_recalculateLinePosition"]).apply(null,arguments)};var _ParagraphRecalculate_recalculateLinePositionWithInfo=Module["_ParagraphRecalculate_recalculateLinePositionWithInfo"]=function(){return(_ParagraphRecalculate_recalculateLinePositionWithInfo=Module["_ParagraphRecalculate_recalculateLinePositionWithInfo"]=Module["asm"]["ParagraphRecalculate_recalculateLinePositionWithInfo"]).apply(null,arguments)};var _ParagraphRecalculate_recalculateLineBottomBound2WithInfo=Module["_ParagraphRecalculate_recalculateLineBottomBound2WithInfo"]=function(){return(_ParagraphRecalculate_recalculateLineBottomBound2WithInfo=Module["_ParagraphRecalculate_recalculateLineBottomBound2WithInfo"]=Module["asm"]["ParagraphRecalculate_recalculateLineBottomBound2WithInfo"]).apply(null,arguments)};var _ParagraphRecalculate_recalculateLineBottomBound2=Module["_ParagraphRecalculate_recalculateLineBottomBound2"]=function(){return(_ParagraphRecalculate_recalculateLineBottomBound2=Module["_ParagraphRecalculate_recalculateLineBottomBound2"]=Module["asm"]["ParagraphRecalculate_recalculateLineBottomBound2"]).apply(null,arguments)};var _ParagraphRecalculate_recalculateLineAlign=Module["_ParagraphRecalculate_recalculateLineAlign"]=function(){return(_ParagraphRecalculate_recalculateLineAlign=Module["_ParagraphRecalculate_recalculateLineAlign"]=Module["asm"]["ParagraphRecalculate_recalculateLineAlign"]).apply(null,arguments)};var _ParagraphRecalculate_recalculateLineAlign2=Module["_ParagraphRecalculate_recalculateLineAlign2"]=function(){return(_ParagraphRecalculate_recalculateLineAlign2=Module["_ParagraphRecalculate_recalculateLineAlign2"]=Module["asm"]["ParagraphRecalculate_recalculateLineAlign2"]).apply(null,arguments)};var _ParagraphRecalculate_recalculateLineAlign2WithInfo=Module["_ParagraphRecalculate_recalculateLineAlign2WithInfo"]=function(){return(_ParagraphRecalculate_recalculateLineAlign2WithInfo=Module["_ParagraphRecalculate_recalculateLineAlign2WithInfo"]=Module["asm"]["ParagraphRecalculate_recalculateLineAlign2WithInfo"]).apply(null,arguments)};var _ParaPortion_recalculateRange=Module["_ParaPortion_recalculateRange"]=function(){return(_ParaPortion_recalculateRange=Module["_ParaPortion_recalculateRange"]=Module["asm"]["ParaPortion_recalculateRange"]).apply(null,arguments)};var ___errno_location=Module["___errno_location"]=function(){return(___errno_location=Module["___errno_location"]=Module["asm"]["__errno_location"]).apply(null,arguments)};var _emscripten_main_thread_process_queued_calls=Module["_emscripten_main_thread_process_queued_calls"]=function(){return(_emscripten_main_thread_process_queued_calls=Module["_emscripten_main_thread_process_queued_calls"]=Module["asm"]["emscripten_main_thread_process_queued_calls"]).apply(null,arguments)};var stackSave=Module["stackSave"]=function(){return(stackSave=Module["stackSave"]=Module["asm"]["stackSave"]).apply(null,arguments)};var stackRestore=Module["stackRestore"]=function(){return(stackRestore=Module["stackRestore"]=Module["asm"]["stackRestore"]).apply(null,arguments)};var stackAlloc=Module["stackAlloc"]=function(){return(stackAlloc=Module["stackAlloc"]=Module["asm"]["stackAlloc"]).apply(null,arguments)};var _malloc=Module["_malloc"]=function(){return(_malloc=Module["_malloc"]=Module["asm"]["malloc"]).apply(null,arguments)};var _free=Module["_free"]=function(){return(_free=Module["_free"]=Module["asm"]["free"]).apply(null,arguments)};var dynCall_viijii=Module["dynCall_viijii"]=function(){return(dynCall_viijii=Module["dynCall_viijii"]=Module["asm"]["dynCall_viijii"]).apply(null,arguments)};var dynCall_iiiiij=Module["dynCall_iiiiij"]=function(){return(dynCall_iiiiij=Module["dynCall_iiiiij"]=Module["asm"]["dynCall_iiiiij"]).apply(null,arguments)};var dynCall_iiiiijj=Module["dynCall_iiiiijj"]=function(){return(dynCall_iiiiijj=Module["dynCall_iiiiijj"]=Module["asm"]["dynCall_iiiiijj"]).apply(null,arguments)};var dynCall_iiiiiijj=Module["dynCall_iiiiiijj"]=function(){return(dynCall_iiiiiijj=Module["dynCall_iiiiiijj"]=Module["asm"]["dynCall_iiiiiijj"]).apply(null,arguments)};var dynCall_jiji=Module["dynCall_jiji"]=function(){return(dynCall_jiji=Module["dynCall_jiji"]=Module["asm"]["dynCall_jiji"]).apply(null,arguments)};Module["ccall"]=ccall;var calledRun;function ExitStatus(status){this.name="ExitStatus";this.message="Program terminated with exit("+status+")";this.status=status}var calledMain=false;dependenciesFulfilled=function runCaller(){if(!calledRun)run();if(!calledRun)dependenciesFulfilled=runCaller};function callMain(args){var entryFunction=Module["_main"];args=args||[];var argc=args.length+1;var argv=stackAlloc((argc+1)*4);HEAP32[argv>>2]=allocateUTF8OnStack(thisProgram);for(var i=1;i<argc;i++){HEAP32[(argv>>2)+i]=allocateUTF8OnStack(args[i-1])}HEAP32[(argv>>2)+argc]=0;try{var ret=entryFunction(argc,argv);exit(ret,true);return ret}catch(e){return handleException(e)}finally{calledMain=true}}function run(args){args=args||arguments_;if(runDependencies>0){return}preRun();if(runDependencies>0){return}function doRun(){if(calledRun)return;calledRun=true;Module["calledRun"]=true;if(ABORT)return;initRuntime();preMain();if(Module["onRuntimeInitialized"])Module["onRuntimeInitialized"]();if(shouldRunNow)callMain(args);postRun()}if(Module["setStatus"]){Module["setStatus"]("Running...");setTimeout(function(){setTimeout(function(){Module["setStatus"]("")},1);doRun()},1)}else{doRun()}}Module["run"]=run;function exit(status,implicit){EXITSTATUS=status;if(keepRuntimeAlive()){}else{exitRuntime()}procExit(status)}function procExit(code){EXITSTATUS=code;if(!keepRuntimeAlive()){if(Module["onExit"])Module["onExit"](code);ABORT=true}quit_(code,new ExitStatus(code))}if(Module["preInit"]){if(typeof Module["preInit"]=="function")Module["preInit"]=[Module["preInit"]];while(Module["preInit"].length>0){Module["preInit"].pop()()}}var shouldRunNow=true;if(Module["noInitialRun"])shouldRunNow=false;run();if(typeof module!=="undefined"){module.exports=Module}else if(typeof window!=="undefined"){window.Module=Module}if(typeof exports!=="undefined"){exports.default=Module}
