import { TableLayoutType } from 'docx';
import { SerialObjType } from '../serialInterface';
import { ISerialTableObj } from '../serialInterface/ISerialTableObj';
import ISerial from './ISerial';
import SerialTableRow from './SerialTableRow';
import {
    convertAlignment,
    convertWidthType,
    transPxToDocxLength,
    buildTableCellMargin,
    buildBorderOption
} from './Utils';

/**
* 表格大小尺寸调整
*/
enum TLayoutType {
    AutoFit,  // 自适应内容
    Fixed,   // 手动调整
}
export default class SerialTable implements ISerial {

    constructor(private readonly table: any) { }

    public serializedTo(collector: ISerialTableObj[]): ISerialTableObj[] {
        const prop = this.table.property;
        const tableObj: ISerialTableObj = {
            name: this.table.tableName,
            type: SerialObjType.Table,
            children: [],
            width: {
                size: transPxToDocxLength(prop.tableWidth.width),
                type: convertWidthType(prop.tableWidth.type)
            },
            columnWidths: this.getTableColumnWidths(this.table.tableGrid),
            margins: buildTableCellMargin(prop.tableCellMargin),
            // indent: { // 7 版本才有
            //     size: transPxToDocxLength(prop.tableIndentation)
            // },
            float: undefined,
            layout: this.getTableLayoutType(prop.tableLayout),
            style: '',
            borders: prop.tableBorders && {
                top: buildBorderOption(prop.tableBorders.top),
                bottom: buildBorderOption(prop.tableBorders.bottom),
                left: buildBorderOption(prop.tableBorders.left),
                right: buildBorderOption(prop.tableBorders.right),
                insideHorizontal: buildBorderOption(prop.tableBorders.insideH),
                insideVertical: buildBorderOption(prop.tableBorders.insideV),
            } || undefined,
            alignment: convertAlignment(prop.alignment),
            visuallyRightToLeft: false
        };
        for (const row of this.table.content) {
            new SerialTableRow(row).serializedTo(tableObj.children);
        }
        collector.push(tableObj);
        return collector;
    }

    /** 获取表格大小尺寸调整类型 */
    private getTableLayoutType(layout: TLayoutType): TableLayoutType {
        switch (layout) {
            case TLayoutType.AutoFit: {
                return TableLayoutType.AUTOFIT;
            }
            case TLayoutType.Fixed: {
                return TableLayoutType.FIXED;
            }
        }
    }

    private getTableColumnWidths(widths: number[]): number[] {
        if (!Array.isArray(widths)) {
            return [];
        }
        return widths.map((num: number) => transPxToDocxLength(num));
    }

}
