import * as React from 'react';
import Dialog from '../../ui/Dialog';
import Button from '../../ui/Button';
import Input from '../../ui/Input';
import Checkbox from '../../ui/CheckboxItem';
import { isValidName, TABLE_ROW_COUNT } from '../../../../common/commonDefines';
import { message } from '../../../../common/Message';
import { IFRAME_MANAGER } from '../../../../common/IframeManager';

interface IDialogProps {
    documentCore: any;
    visible?: boolean;
    id: string;
    bInsertNISTable?: boolean;
    close: (name: string | number, bRefresh?: boolean) => void;
}

interface IState {
    bRefresh: boolean;
}

interface ITable {
    tableName: string;
    rows: number;
    cols: number;
    headerCount: number;
}

export default class InsertTableDialog extends React.Component<IDialogProps, IState> {
    private visible: boolean;
    private table: ITable;
    private bRepeatHeader: boolean;
    private colsNumInDoc: number;
    private rowsNumInDoc: number;
    private colsNumInHF: number;
    private rowsNumInHF: number;
    private bInsertNISTable: boolean;
    constructor(props: any) {
        super(props);
        this.state = {
            bRefresh: false,
        };
        this.visible = this.props.visible;
        this.init();
        // this.bInsertNISTable = this.props.bInsertNISTable;
        // this.table = {
        //     tableName: this.props.documentCore.getUniqueTableName(this.bInsertNISTable),
        //     rows: 2,
        //     cols: 2,
        //     headerCount: !this.bInsertNISTable ? 0 : 1,
        // };
        // this.bRepeatHeader = !this.bInsertNISTable ? false : true;
        this.colsNumInDoc = 50;
        this.rowsNumInDoc = TABLE_ROW_COUNT;
        this.colsNumInHF = 10;
        this.rowsNumInHF = 10;
    }

    public render(): any {
        return (
            <Dialog
                id={this.props.id}
                preventDefault={true}
                visible={this.visible}
                footer={this.renderFooter()}
                width={350}
                open={this.open}
                title={this.getTitle()} // {'插入表格'}
            >
                <div>
                    <div className='editor-line'>
                        <span className='w-70'>名称(A)</span>
                        <div className='right-auto'>
                            <Input
                                name='tableName'
                                type={'string'}
                                value={this.table.tableName}
                                onChange={this.onChange}
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <span className='w-70'>列数(C)</span>
                        <div className='right-auto'>
                            <Input
                                name='cols'
                                type='number'
                                min={1}
                                // step={1}
                                max={this.colsNumInDoc}
                                value={this.table.cols}
                                focus={this.selectText}
                                onChange={this.onChange} // {this.numChange.bind(this, 'table', 'cols')}
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <span className='w-70'>行数(R)</span>
                        <div className='right-auto'>
                            <Input
                                name='rows'
                                type='number'
                                min={1}
                                // step={1}
                                max={this.rowsNumInDoc}
                                value={this.table.rows}
                                focus={this.selectText}
                                onChange={this.onChange} // {this.numChange.bind(this, 'table', 'rows')}
                            />
                        </div>
                    </div>
                    <div className='editor-line editor-multi-line'>
                        <span className='w-70'>表头</span>
                        <div className='right-auto'>
                            <div className='editor-line'>
                                <Input
                                    name='headerCount'
                                    type='number'
                                    min={0}
                                    step={1}
                                    disabled={!this.bRepeatHeader}
                                    value={this.table.headerCount}
                                    focus={this.selectText}
                                    onChange={this.onChange} // {this.numChange.bind(this, 'table', 'headerCount')}
                                />
                            </div>
                            <div className='editor-line'>
                                <Checkbox
                                    name='bRepeatHeader'
                                    value={this.bRepeatHeader}
                                    disabled={false}
                                    onChange={this.onChange}
                                >
                                    重复表头
                                </Checkbox>
                            </div>
                        </div>
                    </div>
                </div>
            </Dialog>
        );
    }

    public componentDidMount(): void {
        this.setState({bRefresh: !this.state.bRefresh});
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        if ( nextProps.visible ) {
            this.table.tableName = this.props.documentCore.getUniqueTableName();
        }
        this.visible = nextProps.visible;
    }

    public selectText = (name: string, e: any) => {
        e.target.select();
    }

    private renderFooter(): any {
        return (
            <span>
                <Button onClick={this.close}>取消</Button>
                <Button type='primary' onClick={this.confirm}>确认</Button>
            </span>
        );
    }

    private onChange = (value: any, name: string): void => {
        if ( 'bRepeatHeader' === name ) {
            this.bRepeatHeader = value;
            this.setState({bRefresh: !this.state.bRefresh});
            return;
        }

        this.table[name] = value;
    }

    private numChange(name: string, subname: string, e: any): void {
        if ( e.target.value && '' !== e.target.value ) {
            this[name][subname] = parseInt(e.target.value, 0);
        } else {
            this[name][subname] = '';
        }

        this.setState({bRefresh: !this.state.bRefresh});
    }

    private close = (bRefresh?: boolean): void => {
        this.props.close(this.props.id, bRefresh);
        this.visible = false;
        this.table = {
            tableName: 'table',
            rows: 2,
            cols: 2,
            headerCount: 0,
        };
        this.bRepeatHeader = false;
        this.colsNumInDoc = 50;
        this.rowsNumInDoc = TABLE_ROW_COUNT;
        this.colsNumInHF = 10;
        this.rowsNumInHF = 10;
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private confirm = (): void => {
        const documentCore = this.props.documentCore;
        IFRAME_MANAGER.setDocId(documentCore.getCurrentId());
        if ( documentCore.isProtectedMode() ) {
            this.close(true);
            message.error('当前文档保护，不可进行编辑');
            return ;
        }

        const table = this.table;
        const bInHF = documentCore.isInHeaderFooter();
        if (!isValidName(table.tableName)) {
            message.error('表格名不符合规范，请重新输入');
            return;
        }

        if ( true !== documentCore.checkTableName(this.table.tableName) ) {
            message.error('表格名重名，请重新输入');
            return ;
        }

        const cols = ( true === bInHF ? this.colsNumInHF : this.colsNumInDoc);
        if ( cols < table.cols || 0 >= table.cols || isNaN(table.cols) || (0 !== table.cols % 1) ) {
            message.error('表格列请输入：1---' + cols + '的整数');
            return;
        }

        const rows = ( true === bInHF ? this.rowsNumInHF : this.rowsNumInDoc);
        if ( rows < table.rows || 0 >= table.rows || isNaN(table.rows) || (0 !== table.rows % 1) ) {
            message.error('表格行请输入：1---' + rows + '的整数');
            return;
        }

        table.headerCount = this.bRepeatHeader ? table.headerCount : 0;

        if ( table.rows <= table.headerCount || (0 !== table.headerCount % 1) ) {
            message.error('重复表头的行数需小于总行数，请重新输入');
            return;
        }

        if ( !this.bInsertNISTable ) {
            documentCore.insertNewTable(table.cols, table.rows, table.headerCount, table.tableName);
        } else {
            documentCore.insertNISTable(table.cols, table.rows, table.headerCount, table.tableName);
        }
        this.close(true);
    }

    private open = (): void => {
        this.init();
        this.setState({});
    }

    private getTitle(): string {
        if ( !this.bInsertNISTable ) {
            return '插入表格';
        } else {
            return '插入多维表格';
        }
    }

    private init(): void {
        this.bInsertNISTable = this.props.bInsertNISTable;
        const name = !this.bInsertNISTable ? undefined : '多维表格';
        this.table = {
            tableName: this.props.documentCore.getUniqueTableName(name),
            rows: 2,
            cols: 2,
            headerCount: !this.bInsertNISTable ? 0 : 1,
        };
        this.bRepeatHeader = this.bInsertNISTable ? true : false;
    }
}
