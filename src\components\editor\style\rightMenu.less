@import './global.less';

.hz-editor-container .editor-rightMenu, .editor-sdk-container .editor-rightMenu {
    & * {
        box-sizing: border-box;
    }

    @borderColor: #dddde3;

    .box-boder {
        padding: 4px 0;
        margin: 0;
        background:#fafafa;
        box-shadow: 0px 2px 20px 0px rgba(39, 53, 70, 0.2);
        border-radius: 4px;
        border: 1px solid @borderColor;
    }

    .box {
        display: none;
        position: fixed;
        left: 0;
        top: 0;
        z-index: 1800;
        width: 124px;
        font-size: @fontSize;
        font-family: @fontFamily;
        font-weight: 500;
        color: @color3;
        .box-boder();
    }

    .box.right-menu-visible {
        display: block;
    }
    .box > ul {
        padding: 0;
        margin: 0;
    }

    .readonly-mode {
        li:first-child {
            height: 28px;
            &::after {
                display: block;
                width: 100%;
                height: 0;
                margin-bottom: 4px;
                padding-top: 4px;
                border-bottom: 2px solid @borderColor;
                content: ' ';
            }
        }
    }

    ul > li {
        display: block;
        position: relative;
        width: 100%;
        height: 20px;
        line-height: 20px;
        text-align: left;
        list-style: none;
        cursor: pointer;

        & > span {
            display: inline-block;
            line-height: 20px;
            padding: 0 20px;
        }

        &.delete, &.paragraph, &.deleteCol, &.splitCell, &.col, &.insert, &.struct, &.formulaAlign, &.imageAlign, &.deleteRegion, &.redo, &.cellProperty {
            margin-bottom: 10px;
            &:after {
                display: block;
                width: 100%;
                height: 0;
                margin-bottom: 4px;
                padding-top: 4px;
                border-bottom: 2px solid @borderColor;
                content: ' ';
            }
        }

        &.custom {
            height: auto;
        }

        &.rightMenu-split {
            margin-top: 10px;
            &::before {
                display: block;
                position: absolute;
                top: -5px;
                width: 100%;
                height: 0;
                border-top: 2px solid @borderColor;
                content: ' ';
            }
        }

        & > i {
            position: absolute;
            right: 0px;
            display: inline-block;
            width: 12px;
            height: 20px;
            margin-right: 5px;
            line-height: 20px;
            vertical-align: middle;
            pointer-events: none;

            &::before {
                display: inline-block;
                width: 0;
                height: 0;
                vertical-align: middle;
                border-left: 5px solid @color3;
                border-right: 5px solid transparent;
                border-top: 5px solid transparent;
                border-bottom: 5px solid transparent;

                content: " ";
                pointer-events: none;
            }
        }

        &:hover:not(.disabled) {
            color: #fff;
            background-color: @activeColor;
        }

        &.disabled {
            color: @color4;
            cursor: default;
        }

        &.abled {
            color: @color3;
            cursor: pointer;
        }
    }

    li:hover .inner-ul {
        display: block;
    }

    .inner-ul {
        display: none;
        position: absolute;
        left: 100%;
        top: -2px;
        width: 124px;
        .box-boder();

        & > li > span {
            padding: 0 15px;
        }
    }

    li.hide {
        display: none;
    }

}
