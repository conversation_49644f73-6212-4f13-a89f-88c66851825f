import * as React from 'react';
import '../../style/cascade.less';
import Dialog from '../../ui/Dialog';
import {CascadeActionType, CascadeTriggerCondition, CodeValueItem, ICascade, NewControlType} from '../../../../common/commonDefines';

import {message} from '../../../../common/Message';
import Input from '../../ui/Input';
import Select from '../../ui/select/Select';
import Button from '../../ui/Button';
import { IFRAME_MANAGER } from '../../../../common/IframeManager';

interface IDialogProps {
    documentCore: any;
    visible: boolean;
    controlName: string;
    name?: string;
    properties: ICascade[];
    list?: () => CodeValueItem[];
    onChange: (value: any, name: string) => void;
    close: (name: string | number, bRefresh?: boolean) => void;
    id: string;
    type?: NewControlType;
    disable?: boolean;
}

interface IState {
    bRefresh: boolean;
}

export default class CascadeBtn extends React.PureComponent<IDialogProps, IState> {
    private visible: boolean;
    constructor(props: IDialogProps) {
        super(props);
        this.state = {
            bRefresh: false,
        };
    }

    public render(): any {
        const className = (this.props.disable ? 'cascade-prop-btn disable' : 'cascade-prop-btn');
        return (
            <React.Fragment>
                <div className={className} onClick={this.onClick}>
                    <i>+</i>
                    <span>添加级联</span>
                </div>
                {this.renderDialog()}
            </React.Fragment>
        );
    }

    private onClick = (e: any): void => {
        if ( this.props.disable ) {
            return;
        }

        this.visible = true;
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private onClose = (name: string, bRefresh: boolean): void => {
        this.visible = false;
        if (bRefresh) {
            this.props.close(this.props.id, true);
        } else {
            // this.setState({bRefresh: !this.state.bRefresh});
        }
    }

    private renderDialog(): any {
        if (this.visible !== true) {
            return null;
        }
        const props = this.props;
        return (
            <CascadeDialog
                visible={this.visible}
                id={props.id}
                name={props.name}
                controlName={props.controlName}
                onChange={props.onChange}
                close={this.onClose}
                list={this.props.list}
                properties={props.properties}
                documentCore={props.documentCore}
                type={this.props.type}
            />
        );
    }
}

interface IPropKey {
    key: string;
    value: any;
    disabled?: boolean;
}

class CascadeDialog extends React.Component<IDialogProps, IState> {
    private cascadeProps: ICascade[];
    private currentProp: ICascade;
    private visible: boolean;
    private eventName: string;
    private actions: IPropKey[];
    private logicTypes: IPropKey[];
    private activeIndex: number;
    private curInputIndex: number;
    private logicDisabled: boolean;
    private mustInputDisable: boolean; // 是否启用必填项
    private logicTexts: any[];
    // private logicTypeDisabled: boolean;
    // private logicTextDisabled: boolean;

    constructor(props: any) {
        super(props);
        this.state = {
            bRefresh: false,
        };
        this.cascadeProps = [];
        const checkDisable = this.props.type !== NewControlType.CheckBox &&
                             this.props.type !== NewControlType.NumberBox &&
                             NewControlType.DateTimeBox !== this.props.type;
        this.mustInputDisable = [NewControlType.CheckBox, NewControlType.DateTimeBox,
            NewControlType.TextBox, NewControlType.Combox,
            NewControlType.MultiCombox, NewControlType.ListBox,
            NewControlType.MultiListBox].includes(this.props.type);
        if (NewControlType.DateTimeBox !== this.props.type) {
            this.actions = [
                {key: '文本', value: CascadeActionType.SetText},
                {key: '同步文本', value: CascadeActionType.SyncText, disabled: true},
                {key: '显示', value: CascadeActionType.Show},
                {key: '隐藏', value: CascadeActionType.Hidden},
                {key: '只读', value: CascadeActionType.UnEdit},
                {key: '可编辑', value: CascadeActionType.Edit},
                {key: '勾选', value: CascadeActionType.Checked, disabled: checkDisable},
                {key: '取消勾选', value: CascadeActionType.Unchecked, disabled: checkDisable},
            ];
        } else {
            this.actions = [];
        }

        if (this.mustInputDisable) {
            this.actions.push({key: '设置必填项', value: CascadeActionType.SetMustItem,
                        disabled: false});
            this.actions.push({key: '取消必填项', value: CascadeActionType.UnsetMustItem,
                        disabled: false});
        }
        if (this.props.type === NewControlType.DateTimeBox) {
            this.actions.push({key: '同步文本', value: CascadeActionType.SyncText, disabled: false});
        }
        this.logicTypes = [];
        this.visible = this.props.visible;
    }

    public render(): any {
        return (
            <Dialog
                visible={this.visible}
                width={700}
                top='25%'
                open={this.open}
                title='级联助手'
                footer={this.renderFooter()}
                id={this.props.id}
            >
                <div className='newcontrol-cascade'>
                    {this.renderContent()}
                </div>
            </Dialog>
        );
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        this.visible = nextProps.visible;
    }

    private renderContent(): any {
        if (!this.cascadeProps || this.cascadeProps.length === 0) {
            return (<div className='no-data prop-add' onClick={this.rowClick.bind(this, 0)}>点击添加</div>);
        }
        return (
            <ul>
                <li>
                    <div>触发条件</div>
                    <div>逻辑</div>
                    <div>逻辑值</div>
                    <div>动作</div>
                    <div>动作值</div>
                    <div>目标id</div>
                    <div>操作</div>
                </li>
                {this.renderList()}
            </ul>
        );
    }

    private renderList(): any {
        return this.cascadeProps.map((prop, index) => {
            let ismustLogic = false;
            let tmpActions;
            let action: string;
            let bUnDateCompare: boolean = true;
            const bDate =  this.props.type === NewControlType.DateTimeBox;
            if (prop.logic !== CascadeTriggerCondition.DateCompare) {
                ismustLogic = [CascadeTriggerCondition.Empty,
                               CascadeTriggerCondition.UnEmpty,
                               CascadeTriggerCondition.NoSelect].includes(prop.logic);
                tmpActions = this.actions.map((item: IPropKey) => {
                    return Object.assign({}, item);
                });
                if (this.props.type === NewControlType.ListBox && 
                    this.eventName === 'SelectedChanged' && 
                    prop.logic === CascadeTriggerCondition.Equal) {
                    tmpActions.forEach((item: IPropKey) => {
                        if (item.value === CascadeActionType.SetMustItem ||
                            item.value === CascadeActionType.UnsetMustItem) {
                            item.disabled = false;
                        }
                    });
                } else if (this.props.type !== NewControlType.DateTimeBox ||
                    (prop.logic !== CascadeTriggerCondition.Equal &&
                    prop.logic !== CascadeTriggerCondition.Neq)) {
                    if (this.mustInputDisable === true) {
                        tmpActions.forEach((item: IPropKey) => {
                            if (item.value === CascadeActionType.SetMustItem ||
                                item.value === CascadeActionType.UnsetMustItem) {
                                item.disabled = !ismustLogic;
                            }
                        });
                    }
                    if (!ismustLogic && (prop.action === CascadeActionType.SetMustItem ||
                        prop.action === CascadeActionType.UnsetMustItem)) {
                        prop.action = tmpActions[0].value;
                    }
                }
                if (bDate) {
                    ismustLogic = true;
                }
            } else {
                tmpActions = [{key: '提示预警', value: CascadeActionType.WarnTip, disabled: true}];
                action = CascadeActionType.WarnTip;
                bUnDateCompare = false;
            }
            return (
                <li
                    key={index}
                    onClick={this.rowClick.bind(this, index)}
                    onMouseEnter={this.mouseEnter.bind(this, index)}
                >
                    <div className='prop-name'>
                        <Input
                            value={this.eventName}
                            name='eventName'
                            disabled={true}
                            onChange={this.onChange.bind(this, index)}
                        />
                    </div>
                    <div>
                        <Select
                            data={this.logicTypes}
                            // disabled={true}
                            value={prop.logic}
                            name='logic'
                            width={135}
                            disabled={prop.action === CascadeActionType.SyncText}
                            onChange={this.typeChange.bind(this, index)}
                        />
                    </div>
                    <div className='prop-name'>
                        {this.rednerLogicText(prop, index, ismustLogic)}
                    </div>
                    <div>
                        <Select
                            data={tmpActions}
                            // disabled={true}
                            value={action || prop.action}
                            width={120}
                            name='action'
                            disabled={!bUnDateCompare}
                            onChange={this.typeChange.bind(this, index)}
                        />
                    </div>
                    <div className='prop-name'>
                        <Input
                            value={prop.actionText}
                            name='actionText'
                            disabled={prop.action !== CascadeActionType.SetText && bUnDateCompare}
                            onChange={this.onChange.bind(this, index)}
                            onBlur={this.onBlur.bind(this, index)}
                            focus={this.onFocus}
                        />
                    </div>
                    <div className='prop-name'>
                        <Input
                            value={prop.target}
                            name='target'
                            onChange={this.onChange.bind(this, index)}
                            onBlur={this.onBlur.bind(this, index)}
                            focus={this.onFocus}
                        />
                    </div>

                    <div className='prop-btns'>
                        <span className='prop-add'>+</span>
                        <label className='prop-delete'>-</label>
                    </div>
                </li>
            );
        });
    }

    private rednerLogicText(prop: ICascade, index: number, ismustLogic: boolean): any {
        if ([NewControlType.ListBox, NewControlType.MultiListBox, NewControlType.Combox, NewControlType.MultiCombox].includes(this.props.type)) {
            let datas = [];
            if (this.props.list) {
                datas = this.props.list() || [];
            }
            datas = datas.map((item) => {
                return {
                    key: item.code,
                    value: item.code
                }
            });
            return (
                <Select
                    data={datas}
                    width={130}
                    bWrite={true}
                    unDeleted={true}
                    value={prop.logicText}
                    name='logicText'
                    disabled={this.logicDisabled || prop.action === CascadeActionType.SyncText || ismustLogic}
                    onChange={this.typeChange.bind(this, index)}
                />
            );
        }

        if (prop.logic !== CascadeTriggerCondition.DateCompare) {
            let numLogicMessage;
            if (
                this.props.type === NewControlType.NumberBox &&
                ![
                    CascadeTriggerCondition.Range,
                    CascadeTriggerCondition.Empty,
                ].includes(prop.logic)
            ) {
                numLogicMessage = (
                    <div className='numberLogicMessage'>
                        允许输入数值框id
                        {/* 允许直接录入另外一个数值框的id, 逻辑变为2个数值框进行比较 */}
                    </div>
                );
            }
            return (
                <>
                    <Input
                        value={prop.logicText}
                        placeholder={prop.logic === CascadeTriggerCondition.Range ? '格式:[a,b]' : undefined}
                        name='logicText'
                        disabled={this.logicDisabled || prop.action === CascadeActionType.SyncText || ismustLogic}
                        onChange={this.onChange.bind(this, index)}
                        onBlur={this.onBlur.bind(this, index)}
                        focus={this.onFocus}
                    />
                    {numLogicMessage}
                </>
            );
        }

        return (
            <Select
                data={this.logicTexts}
                width={121}
                value={prop.logicText}
                name='logicText'
                onChange={this.typeChange.bind(this, index)}
            />
        );
    }

    private renderFooter(): any {
        return (
            <span>
                <Button onClick={this.close}>取消</Button>
                <Button type='primary' onClick={this.confirm}>确认</Button>
            </span>
        );
    }

    private mouseEnter = (index: number, e: any): void => {
        this.currentProp = this.cascadeProps[index];
    }

    private rowClick(index: number, e: any): void {
        this.activeIndex = index;
        const target = e.target as HTMLDivElement;
        const className = target.className;
        if (this.curInputIndex !== index && className === 'prop-name') {
            this.curInputIndex = index;
        } else if (className.indexOf('prop-add') > -1) {
            this.addData(index);
            this.setState({bRefresh: !this.state.bRefresh});
        } else if (className === 'prop-delete') {
            this.deleteData(index);
            this.setState({bRefresh: !this.state.bRefresh});
        }
    }

    private typeChange = (index: number, value: any, name: string): void => {
        if (name === 'logic' && this.props.type === NewControlType.DateTimeBox) {
            const prop = this.cascadeProps[index];
            if (value === CascadeTriggerCondition.DateCompare) {
                prop.action = CascadeActionType.WarnTip;
                prop.logicText = CascadeTriggerCondition.Less;
            } else {
                prop.logicText = '有内容';
                prop.action = CascadeActionType.SetMustItem;
            }
        }
        this.onChange(index, value, name);
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private onChange = (index: number, value: any, name: string): void => {
        this.currentProp = this.cascadeProps[index];
        this.currentProp[name] = value;
        if ([CascadeTriggerCondition.Empty,
            CascadeTriggerCondition.UnEmpty,
            CascadeTriggerCondition.NoSelect].includes(value)) {
            this.currentProp.logicText = ' ';
        } else if (value === CascadeTriggerCondition.Range) {
            this.currentProp.logicText = '';
        }
    }

    private addData = (index?: number): void => {
        let text = this.logicDisabled ? 'true' : undefined;
        if (NewControlType.DateTimeBox === this.props.type) {
            text = '有内容';
        }

        const data: ICascade = {
            event: this.eventName as any,
            logic: this.logicTypes[0].value,
            logicText: text,
            action: this.actions[0].value,
            actionText: undefined,
            target: undefined,
        };
        if (index === undefined) {
            this.cascadeProps.push(data);
        } else {
            this.cascadeProps.splice(index + 1, 0, data);
        }
        this.currentProp = data;
    }

    private deleteData = (index: number): void => {
        const data = this.cascadeProps[index];
        // delete this._names[data.name];
        this.cascadeProps.splice(index, 1);
        this.currentProp = undefined;
    }

    // private getPropName(): string {
    //     const datas = this.custompProps;
    //     if (!datas || datas.length === 0) {
    //         const actName = this._name + 1;
    //         this._names[actName] = true;
    //         return actName;
    //     }

    //     const names = this._names;
    //     const name = this._name;
    //     let res: string;
    //     for (let index = 1; true; index++) {
    //         res = name + index;
    //         if (names[res] !== true) {
    //             names[res] = true;
    //             break;
    //         }
    //     }

    //     return res;
    // }

    private onBlur = (index: number, name: string, input: any): void => {
        // this.currentProp = this.cascadeProps[index];
        // if (!currentProp) {
        //     return;
        // }

        // check xml element name validity
        // const nameVal = input.value;
        // if (this.checkXmlElementNameValidity(nameVal)) {
        //     input.classList.remove('warning');
        // } else {
        //     input.classList.add('warning');
        // }

        // const preName = this._activeName;
        // const currentName = currentProp.name;
        // if (currentName && currentName !== preName) {
        //     const currentIndex = this.custompProps.findIndex((item, i) => item.name === currentName && i !== index);
        //     if (currentIndex > -1) {
        //         IFRAME_MANAGER.setDocId(this.props.documentCore.getCurrentId());
        //         message.error('已存在相同的名称')
        //         .then(() => {
        //             currentProp.name = '';
        //             input.focus();
        //             this.setState({bRefresh: !this.state.bRefresh});
        //         });
        //         return;
        //     }
        //     delete this._names[preName];
        //     this._names[currentName] = true;
        // }
        // else if (!currentName) {
        //     message.error('属性名称不能为空')
        //     .then(() => {
        //         // input.focus();
        //         this.setState({bRefresh: !this.state.bRefresh});
        //     });
        // }
    }

    private onFocus = (name: string, e: any): void => {
        // this._activeName = e.target.value;
    }

    private open = (): void => {
        const props = this.props;
        let eventName: string;
        let logicTypes = [];
        this.logicDisabled = false;
        const syncNode = this.actions.find((item) => item.value === CascadeActionType.SyncText);
        if (syncNode) {
            syncNode.disabled = false;
        }
        logicTypes.push({key: '等于', value: CascadeTriggerCondition.Equal},
                {key: '不等于', value: CascadeTriggerCondition.Neq});
        switch (props.type) {
            case NewControlType.CheckBox: {
                eventName = 'CheckedChanged';
                syncNode.disabled = this.logicDisabled = true;
                break;
            }
            case NewControlType.RadioButton: {
                eventName = 'RadioChanged';
                syncNode.disabled = true;
                logicTypes.push({key: '未选中值', value: CascadeTriggerCondition.NoSelect});
                break;
            }
            case NewControlType.MultiRadio: {
                eventName = 'RadioChanged';
                syncNode.disabled = true;
                logicTypes = [];
                logicTypes.push({key: '包含', value: CascadeTriggerCondition.Includes});
                logicTypes.push({key: '不包含', value: CascadeTriggerCondition.UnIncludes});
                logicTypes.push({key: '未选中值', value: CascadeTriggerCondition.NoSelect});
                break;
            }
            case NewControlType.TextBox: {
                eventName = 'TextChanged';
                logicTypes.push({key: '包含', value: CascadeTriggerCondition.Includes});
                logicTypes.push({key: '不包含', value: CascadeTriggerCondition.UnIncludes});
                logicTypes.push({key: '为空', value: CascadeTriggerCondition.Empty});
                logicTypes.push({key: '不为空', value: CascadeTriggerCondition.UnEmpty});
                break;
            }
            case NewControlType.MultiCombox:
            case NewControlType.MultiListBox: {
                logicTypes = [];
                logicTypes.push({key: '包含', value: CascadeTriggerCondition.Includes});
                logicTypes.push({key: '不包含', value: CascadeTriggerCondition.UnIncludes});
            }
            case NewControlType.Combox:
            case NewControlType.ListBox: {
                eventName = 'SelectedChanged';
                logicTypes.push({key: '为空', value: CascadeTriggerCondition.Empty});
                logicTypes.push({key: '不为空', value: CascadeTriggerCondition.UnEmpty});
                break;
            }
            case NewControlType.NumberBox: {
                eventName = 'NumberChanged';
                logicTypes.push({key: '小于', value: CascadeTriggerCondition.Less},
                {key: '小于等于', value: CascadeTriggerCondition.LEQ},
                {key: '大于', value: CascadeTriggerCondition.Larger},
                {key: '大于等于', value: CascadeTriggerCondition.GEQ},
                {key: '范围', value: CascadeTriggerCondition.Range},
                {key: '等于空值', value: CascadeTriggerCondition.Empty});
                break;
            }
            case NewControlType.DateTimeBox: {
                eventName = 'TextChanged';
                logicTypes.push({key: '日期比较', value: CascadeTriggerCondition.DateCompare});
                this.logicTexts = [{key: '小于', value: CascadeTriggerCondition.Less},
                {key: '小于等于', value: CascadeTriggerCondition.LEQ},
                {key: '大于', value: CascadeTriggerCondition.Larger},
                {key: '大于等于', value: CascadeTriggerCondition.GEQ},
                {key: '等于', value: CascadeTriggerCondition.Equal}];
                this.logicDisabled = false;
                break;
            }
            default: {
                return;
            }
        }
        this.logicTypes = logicTypes;
        this.eventName = eventName;

        const arrs: ICascade[] = this.cascadeProps = [];
        let datas: ICascade[];
        if (props.properties) {
            datas = this.props.properties;
        } else {
            datas = [];
        }

        datas.forEach((data) => {
            arrs.push({...data});
        });
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private validData(): boolean {
        const datas = this.cascadeProps;
        if (datas.length === 0) {
            return true;
        }
        const documentCore = this.props.documentCore;

        for (let index = datas.length - 1; index >= 0; index--) {
            const data = datas[index];
            // 级联目标不能为自身
            if (data.target && data.target.split(',').includes(this.props.controlName)) {
                message.error(`第${index + 1}行的级联目标不能为自身`);
                return false;
            }
            const logicControl = documentCore.getNewControlByName(data.logicText);
            if (
                this.props.type === NewControlType.NumberBox &&
                logicControl &&
                !logicControl.isNumberBox()
            ) {
                message.error(`第${index + 1}行的逻辑值为元素id, 目前只支持数值框间的直接比较`);
                return false;
            }
            if (data.action !== CascadeActionType.SyncText && !data.logicText) {
                message.error(`第${index + 1}行的逻辑值为不能为空`);
                return false;
            }
            if (data.action === CascadeActionType.SetText && !data.actionText) {
                message.error(`第${index + 1}行的动作值为不能为空`);
                return false;
            }
            if (!data.target) {
                message.error(`第${index + 1}行的目标id值不能为空`);
                return false;
            }
            if (this.props.type === NewControlType.DateTimeBox && data.action === CascadeActionType.SyncText) {
                const targets = data.target.split(',');
                if (!targets.length) {
                    message.error(`第${index + 1}行的目标id值不能为空`);
                    return false;
                }
                for (const target of targets) {
                    const newControl = documentCore.getNewControlByName(target);
                    if (!newControl || !newControl.isNewDateBox()) {
                        message.error(`第${index + 1}行的目标结构化元素${target}不为日期框,日期同步只能针对日期框元素`);
                        return false;
                    }
                }
            } else if (data.logic === CascadeTriggerCondition.DateCompare) {
                if (data.target.split(',')
                .find((id) => {
                    const newControl = documentCore.getNewControlByName(id);
                    if (!newControl || !newControl.isNewDateBox()) {
                        return true;
                    }
                    return false;
                })) {
                    message.error(`级联第${index + 1}行的目标id值必须为日期框`);
                    return false;
                }
            }
        }

        return true;
    }

    private close = (id?: any): void => {
        this.props.close(id);
        this.visible = false;
        this.cascadeProps = [];
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private confirm = (id?: any): void => {
        IFRAME_MANAGER.setDocId(this.props.documentCore.getCurrentId());
        if (this.validData() === false) {
            return;
        }

        if (this.cascadeProps && this.cascadeProps.length > 0) {
            this.props.onChange(this.cascadeProps.slice(), this.props.name);
        } else {
            this.props.onChange([], this.props.name);
        }
        this.cascadeProps = [];
        this.props.close(id, true);
    }
}
