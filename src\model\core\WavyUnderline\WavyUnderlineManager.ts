import { ParagraphContentPos } from "../Paragraph/ParagraphContent";
import { WavyUnderline } from "./WavyUnderline";

export class WavyUnderlineManager {
    private data: Map<string, WavyUnderline>;
    private sortData: IWavyUnderlineSortData[];
    private doc: Document;
    private activeWavyUnderline: string | null = null;
    private nameCounter: number = 0;
    
    // ID生成相关静态属性
    private static idCounter: number = 0;
    private static readonly ID_PREFIX = 'wavy';
    
    constructor(doc: Document) {
        this.doc = doc;
        this.data = new Map();
        this.sortData = [];
    }
    
    /**
     * 添加波浪线对象
     */
    public addWavyUnderline(wavyUnderline: WavyUnderline): void {
        const name = this.makeUniqueName();
        wavyUnderline.setName(name);
        wavyUnderline.setLogicDocument(this.doc);
        
        this.data.set(name, wavyUnderline);
        this.sortData.push({
            id: wavyUnderline.getId(),
            name: name
        });
        
        // 按位置排序，便于查找
        this.sortWavyUnderlines();
    }
    
    /**
     * 添加波浪线对象并返回ID - 用于外部接口
     */
    public addWavyUnderlineWithId(wavyUnderline: WavyUnderline, customId?: string): string {
        const id = customId || this.generateWavyUnderlineId();
        
        // 验证ID格式
        if (!this.isValidWavyUnderlineId(id)) {
            throw new Error(`Invalid wavy underline ID format: ${id}`);
        }
        
        // 检查ID是否已存在
        if (this.data.has(id)) {
            throw new Error(`Wavy underline ID already exists: ${id}`);
        }
        
        wavyUnderline.setName(id);
        wavyUnderline.setLogicDocument(this.doc);
        
        this.data.set(id, wavyUnderline);
        this.sortData.push({
            id: wavyUnderline.getId(),
            name: id
        });
        
        // 按位置排序，便于查找
        this.sortWavyUnderlines();
        
        return id;
    }
    
    /**
     * 根据位置获取波浪线对象
     */
    // 在 WavyUnderlineManager.ts 中修改 getWavyUnderlineByPos 方法
    public getWavyUnderlineByPos(pos: ParagraphContentPos): WavyUnderline | null {
        for (const [name, wavyUnderline] of this.data) {
            const startPos = wavyUnderline.getDetectionStartPos();
            const endPos = wavyUnderline.getDetectionEndPos();
        
            // 如果没有检测位置，回退到渲染位置（向后兼容）
            const actualStartPos = startPos || wavyUnderline.getStartPos();
            const actualEndPos = endPos || wavyUnderline.getEndPos();
            
            // 智能位置匹配：支持不同深度的位置比较
            if (this.isPositionInRange(pos, actualStartPos, actualEndPos)) {
                return wavyUnderline;
            }
        }
        return null;
    }

    private isPositionInRange(mousePos: ParagraphContentPos, startPos: ParagraphContentPos, endPos: ParagraphContentPos): boolean {
        // 如果深度相同，直接比较
        if (mousePos.depth === startPos.depth) {
            return mousePos.compare(startPos) >= 0 && mousePos.compare(endPos) <= 0;
        }
        
        // 如果深度不同，进行层级匹配
        // 取较小深度作为比较基准
        const compareDepth = Math.min(mousePos.depth, startPos.depth);
        
        // 比较前N层数据
        const mouseData = mousePos.data.slice(0, compareDepth);
        const startData = startPos.data.slice(0, compareDepth);
        const endData = endPos.data.slice(0, compareDepth);
        
        // 实现数组比较逻辑
        return this.compareArrays(mouseData, startData) >= 0 && 
            this.compareArrays(mouseData, endData) <= 0;
    }

    private compareArrays(arr1: number[], arr2: number[]): number {
        for (let i = 0; i < Math.min(arr1.length, arr2.length); i++) {
            if (arr1[i] !== arr2[i]) {
                return arr1[i] - arr2[i];
            }
        }
        return arr1.length - arr2.length;
    }
    
    /**
     * 激活指定波浪线
     */
    public activateWavyUnderline(name: string): boolean {
        this.deactiveAll();
        
        const wavyUnderline = this.data.get(name);
        if (wavyUnderline) {
            wavyUnderline.setActive(true);
            this.activeWavyUnderline = name;
            return true;
        }
        return false;
    }
    
    /**
     * 取消所有激活状态
     */
    public deactiveAll(): void {
        if (this.activeWavyUnderline) {
            const wavyUnderline = this.data.get(this.activeWavyUnderline);
            if (wavyUnderline) {
                wavyUnderline.setActive(false);
            }
            this.activeWavyUnderline = null;
        }
    }
    
    /**
     * 获取当前激活的波浪线
     */
    public getActiveWavyUnderline(): WavyUnderline | null {
        if (this.activeWavyUnderline) {
            return this.data.get(this.activeWavyUnderline) || null;
        }
        return null;
    }
    
    /**
     * 删除波浪线
     */
    public removeWavyUnderline(name: string): boolean {
        const wavyUnderline = this.data.get(name);
        if (!wavyUnderline) {
            return false;
        }
        
        // 从数据映射中删除
        this.data.delete(name);
        
        // 从排序数组中删除
        const index = this.sortData.findIndex(item => item.name === name);
        if (index !== -1) {
            this.sortData.splice(index, 1);
        }
        
        // 如果是当前激活的，则取消激活
        if (this.activeWavyUnderline === name) {
            this.activeWavyUnderline = null;
        }
        
        return true;
    }
    
    /**
     * 获取所有波浪线
     */
    public getAllWavyUnderlines(): WavyUnderline[] {
        return Array.from(this.data.values());
    }
    
    /**
     * 清空所有波浪线
     */
    public clear(): void {
        this.data.clear();
        this.sortData = [];
        this.activeWavyUnderline = null;
    }
    
    /**
     * 生成唯一名称 - 保持向后兼容
     */
    private makeUniqueName(): string {
        return `wavy_${++this.nameCounter}_${Date.now()}`;
    }
    
    /**
     * 生成唯一的波浪线ID
     */
    public generateWavyUnderlineId(): string {
        const timestamp = Date.now();
        const counter = ++WavyUnderlineManager.idCounter;
        return `${WavyUnderlineManager.ID_PREFIX}_${timestamp}_${counter.toString().padStart(3, '0')}`;
    }
    
    /**
     * 验证ID格式是否有效
     */
    public isValidWavyUnderlineId(id: string): boolean {
        // 格式：wavy_1703123456789_001
        const pattern = /^wavy_\d{13}_\d{3}$/;
        return pattern.test(id);
    }
    
    /**
     * 按位置排序波浪线
     */
    private sortWavyUnderlines(): void {
        this.sortData.sort((a, b) => {
            const wavyA = this.data.get(a.name);
            const wavyB = this.data.get(b.name);
            
            if (!wavyA || !wavyB) return 0;
            
            return wavyA.getStartPos().compare(wavyB.getStartPos());
        });
    }
    
    /**
     * 二分查找位置对应的波浪线索引
     */
    private findWavyUnderlineLowBoundByPos(pos: ParagraphContentPos): number {
        let low = 0;
        let high = this.sortData.length;
        
        while (low < high) {
            const middle = Math.floor((low + high) / 2);
            const wavyUnderline = this.data.get(this.sortData[middle].name);
            
            if (!wavyUnderline) {
                high = middle;
                continue;
            }
            
            const startPos = wavyUnderline.getStartPos();
            const endPos = wavyUnderline.getEndPos();
            
            if (endPos.compare(pos) < 0) {
                low = middle + 1;
            } else if (startPos.compare(pos) > 0) {
                high = middle;
            } else {
                return middle;
            }
        }
        
        return low;
    }
    
    // ===== 基于ID的操作方法 =====
    
    /**
     * 根据ID获取波浪线对象
     */
    public getWavyUnderlineById(id: string): WavyUnderline | null {
        return this.data.get(id) || null;
    }
    
    /**
     * 根据ID移除波浪线
     */
    public removeWavyUnderlineById(id: string): boolean {
        return this.removeWavyUnderline(id);
    }
    
    /**
     * 根据ID更新波浪线内容
     */
    public updateWavyUnderlineContent(id: string, content: string): boolean {
        const wavyUnderline = this.getWavyUnderlineById(id);
        if (wavyUnderline) {
            wavyUnderline.setContent(content);
            return true;
        }
        return false;
    }
    
    /**
     * 根据ID更新波浪线类型
     */
    public updateWavyUnderlineType(id: string, type: any): boolean {
        const wavyUnderline = this.getWavyUnderlineById(id);
        if (wavyUnderline) {
            wavyUnderline.setType(type);
            return true;
        }
        return false;
    }
    
    /**
     * 获取所有波浪线ID列表
     */
    public getAllWavyUnderlineIds(): string[] {
        return Array.from(this.data.keys());
    }
    
    /**
     * 根据类型获取波浪线ID列表
     */
    public getWavyUnderlineIdsByType(type: any): string[] {
        const ids: string[] = [];
        for (const [id, wavyUnderline] of this.data) {
            if (wavyUnderline.getType() === type) {
                ids.push(id);
            }
        }
        return ids;
    }
    
    /**
     * 检查ID是否存在
     */
    public hasWavyUnderlineId(id: string): boolean {
        return this.data.has(id);
    }
    
    /**
     * 清理过期的波浪线（基于ID中的时间戳）
     */
    public cleanupExpiredWavyUnderlines(maxAge: number = 24 * 60 * 60 * 1000): number {
        const now = Date.now();
        let cleanedCount = 0;
        
        for (const id of this.data.keys()) {
            // 从ID中提取时间戳
            const match = id.match(/^wavy_(\d{13})_\d{3}$/);
            if (match) {
                const timestamp = parseInt(match[1]);
                if (now - timestamp > maxAge) {
                    this.removeWavyUnderline(id);
                    cleanedCount++;
                }
            }
        }
        
        return cleanedCount;
    }
    
    /**
     * 清空所有波浪线（兼容外部调用）
     */
    public clearAllWavyUnderlines(): void {
        this.clear();
    }
}

interface IWavyUnderlineSortData {
    id: number;
    name: string;
}