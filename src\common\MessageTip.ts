import {GlobalEvent as gEvent, GlobalEventName as gEventName} from './GlobalEvent';
import { getTheme } from '@hz-editor/theme';
import { getPagePadding } from './commonMethods';
import { ViewModeType } from './commonDefines';

export class MessageTip {
    private _tipDom: HTMLElement;
    private _host: any;
    private _bClose: boolean;
    private _timeout: any;
    private _documentCore: any;
    private className: string;
    constructor(host: any, className?: string) {
        this._host = host;
        this._documentCore = host.documentCore;
        this.className = className;
    }

    public open(content: string, x: number, y: number, bMaxWidthCompare: boolean = true,
                bTimeOut: boolean = true): void {
        const page = this._documentCore.getPageProperty();
        const scale = this._host.getScale();
        this._init();

        const dom = this._tipDom;
        const viewMode = this._documentCore.getViewMode();
        if (bMaxWidthCompare && viewMode !== ViewModeType.CompactView) {
            const doc = dom.ownerDocument;
            const width = doc.body.clientWidth - 18;
            const pageWidth = page.width * scale;
            if (width > pageWidth) {
                const offsetx = (width - pageWidth) / 2 ;
                if (x > (offsetx + pageWidth - page.paddingRight * scale + 5)
                || x < (offsetx + page.paddingLeft * scale - 5)) {
                    return;
                }
            }
        }

        this._bClose = false;
        (dom.firstChild as HTMLElement).innerText = content;
        dom.style.left = x + 'px';
        dom.style.top = y + 'px';
        const className = dom.className;
        if (className.indexOf('active') > -1) {
            return;
        }
        dom.className = className + ' active';
        if (bTimeOut) {
            clearTimeout(this._timeout);
            this._timeout = setTimeout(() => {
                this.close();
            }, 3000);
        }
    }

    public close(): void {
        if (this._bClose !== false) {
            return;
        }
        this._tipDom.className = this._tipDom.className.replace(/\s+active/g, '');
        this._bClose = true;
    }

    public getActivePosition(pageNum: number, x: number, y: number): {x: number, y: number} {
        const dom = this._host.currentRef.current.querySelector(`.page-wrapper[page-index='${pageNum}']`);
        if (!dom) {
            return;
        }
        const span = document.createElement('span');
        span.setAttribute('style', `visibility: hidden;position: absolute;left:${x - 10}px;top:${y - 35}px;`);
        dom.appendChild(span);
        const position = span.getBoundingClientRect();
        span.outerHTML = '';
        return {x: position.x, y: position.y};
    }

    private addEvent(): void {
        const docId = this._host.docId;
        gEvent.addEvent(docId, gEventName.Mousedown, this.mouseDown);
        gEvent.addEvent(docId, gEventName.UnMounted, this.unMounted);
    }

    private mouseDown = (e: any): void => {
        if (this._bClose !== false) {
            return;
        }

        clearTimeout(this._timeout);
        this.close();
    }

    private unMounted = (e: any): void => {
        const docId = this._host.docId;
        gEvent.deleteEvent(docId, gEventName.Mousedown, this.mouseDown);
        gEvent.deleteEvent(docId, gEventName.UnMounted, this.unMounted);
    }

    private _init(): void {
        let dom = this._tipDom;
        if (!dom) {
            dom = document.createElement('div');
            const className = this.className || '';
            dom.className = className + 'editor-revision-tips editor-tips';
            dom.style.backgroundColor = getTheme().NewControl.DefaultMessageTipBackgroundColor;
            dom.style.color = getTheme().NewControl.DefaultMessageTipColor;
            dom.innerHTML = `<div></div><i/>`;
            this._host.currentRef.current.appendChild(dom);
            this._tipDom = dom;
            this.addEvent();
            // dom.addEventListener(gEventName.Click, this.handleTips);
            // dom.addEventListener(gEventName.Mousemove, this.handleTips);
        }
        // (dom.firstChild as HTMLElement).innerText = content;
    }
}
