import * as React from 'react';
import Dialog from '../dialog/dialog';
import { DocumentCore } from 'src/model/DocumentCore';

interface IDialogProps {
    documentCore: DocumentCore;
    // tableProps: any;
    visible: boolean;
    id?: string;
    close: (name: string, bRefresh?: boolean) => void;
    children: React.ReactNode;
}

interface IState {
    bRefresh: boolean;
}

enum TablePropsDialogTabType {
    Table = 0,
    TableCellMargins,
    RowHeight,
    ColumnWidth,
}

export default class TableSettingDialog extends React.Component<IDialogProps, IState> {
    private table: any;
    private cellMargins: any;
    private column: any;
    private row: any;
    private cell: any;
    private tabIndex: TablePropsDialogTabType;
    private tableProps: any;
    private visible: any;
    constructor(props: any) {
        super(props);
        this.table = {
            tableName: '',
            tableCustomProp: '',
            bCanAddRow: true,
            bCanAddColumn: true,
            bCanntSetRowHeight: false,
            bCanntSetColumnWidth: false,
            bCanntDelete: false,
            bReadOnly: false,
            bRepeatHeader: false,
            repeatHeaderNum: 0,
        };
        this.tableProps = null;

        this.cellMargins = {
            top: 0,
            bottom: 0,
            left: 0,
            right: 0,
        };
        this.state = {
            bRefresh: false,
        };
        this.row = {
            rowHeight: 0,
            bAuto: true,
        };
        this.column = {
            columnIndex: [1, 2, 3, 4],
            columnWidth: [] = [],
        };
        this.cell = {
            ff: 0,
        };
        this.tabIndex = TablePropsDialogTabType.Table;
        this.visible = this.props.visible;
        // this.setDialogValue();
    }

    public componentDidMount(): void {
        this.setState({bRefresh: !this.state.bRefresh});
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        this.visible = nextProps.visible;
    }

    public render(): any {
        const nColumnNum = this.column.columnWidth.length;

        return (
            <Dialog
                id={this.props.id}
                visible={this.visible}
                top='middle'
                width={350}
                height={300}
                // close={this.close}
                open={this.open}
                preventDefault={true}
                title='表格属性'
                confirm={this.confirm}
                // id='table'
            >
                <div className='table-set'>
                    <div className='editor-tabs'>
                        <ul>
                            <li
                                className={this.tabIndex === TablePropsDialogTabType.Table ? 'active' : null}
                                onClick={this.tabClick.bind(this, TablePropsDialogTabType.Table)}
                            >
                                表格
                            </li>
                            <li
                                className={this.tabIndex === TablePropsDialogTabType.TableCellMargins ? 'active' : null}
                                onClick={this.tabClick.bind(this, TablePropsDialogTabType.TableCellMargins)}
                            >
                                单元格
                            </li>
                            <li
                                className={this.tabIndex === TablePropsDialogTabType.RowHeight ? 'active' : null}
                                onClick={this.tabClick.bind(this, TablePropsDialogTabType.RowHeight)}
                            >
                                行
                            </li>
                            <li
                                className={this.tabIndex === TablePropsDialogTabType.ColumnWidth ? 'active' : null}
                                onClick={this.tabClick.bind(this, TablePropsDialogTabType.ColumnWidth)}
                            >
                                列宽
                            </li>
                        </ul>
                    </div>
                    <div className='editor-tabs-content'>
                        <ul>
                            <li className={this.tabIndex === TablePropsDialogTabType.Table ? 'active' : null}>
                                <div className='full-line'>
                                    <span className='table-label'>名称</span>
                                    <input
                                        value={this.table.tableName}
                                        onChange={this.textChange.bind(this, 'table', 'tableName')}
                                    />
                                </div>
                                <div className='full-line'>
                                    <span className='table-label'>自定义属性</span>
                                    <input
                                        value={this.table.tableCustomProp}
                                        onChange={this.textChange.bind(this, 'table', 'tableCustomProp')}
                                    />
                                    <button className='table-btn'>添加</button>
                                </div>
                                <div className='full-line'>
                                    <span className='table-label'>样式保护</span>
                                    <span className='table-inline-block'>
                                        <input
                                            checked={this.table.bCanAddRow === true}
                                            type='checkbox'
                                            onChange={this.checkChange.bind(this, 'table', 'bCanAddRow')}
                                        />
                                        <label>允许新增行</label>
                                    </span>
                                    <span className='table-inline-block'>
                                        <input
                                            checked={this.table.bCanAddColumn === true}
                                            type='checkbox'
                                            onChange={this.checkChange.bind(this, 'table', 'bCanAddColumn')}
                                        />
                                        <label>允许删除行</label>
                                    </span>
                                </div>
                                <div className='full-line'>
                                    <span className='table-label'>表格属性</span>
                                    <span className='table-inline-block'>
                                        <input
                                            checked={this.table.bCanntSetRowHeight === true}
                                            type='checkbox'
                                            onChange={this.checkChange.bind(this, 'table', 'bCanntSetRowHeight')}
                                        />
                                        <label>行高固定</label>
                                    </span>
                                    <span className='table-inline-block'>
                                        <input
                                            checked={this.table.bCanntSetColumnWidth === true}
                                            type='checkbox'
                                            onChange={this.checkChange.bind(this, 'table', 'bCanntSetColumnWidth')}
                                        />
                                        <label>列宽固定</label>
                                    </span>
                                </div>
                                <div className='full-line'>
                                    <span className='table-label'/>
                                    <span className='table-inline-block'>
                                        <input
                                            checked={this.table.bCanntDelete === true}
                                            type='checkbox'
                                            onChange={this.checkChange.bind(this, 'table', 'bCanntDelete')}
                                        />
                                        <label>表格删除保护</label>
                                    </span>
                                    <span className='table-inline-block'>
                                        <input
                                            checked={this.table.bReadOnly === true}
                                            type='checkbox'
                                            onChange={this.checkChange.bind(this, 'table', 'bReadOnly')}
                                        />
                                        <label>表格只读保护</label>
                                    </span>
                                </div>
                                <div className='full-line'>
                                    <span className='table-label'>表头</span>
                                    <span className='table-inline-block'>
                                        <input
                                            checked={this.table.bRepeatHeader === true}
                                            type='checkbox'
                                            onChange={this.checkChange.bind(this, 'table', 'bRepeatHeader')}
                                        />
                                        <label>重复表头</label>
                                    </span>
                                    <span className='table-inline-block'>
                                        <label>前</label>
                                        <input
                                            type='number'
                                            min={0}
                                            step={1}
                                            value={this.table.repeatHeaderNum}
                                            style={{width: '50px'}}
                                            onChange={this.textChange.bind(this, 'table', 'repeatHeaderNum')}
                                        />
                                        <label>行</label>
                                    </span>
                                </div>
                            </li>
                            <li className={this.tabIndex === TablePropsDialogTabType.TableCellMargins ? 'active' : null}>
                                <div>
                                    <p className='common-title'>单元格边距</p>
                                    <div className='full-line'>
                                        <label className='common-label'>上</label>
                                        <input
                                            type='number'
                                            min={0}
                                            step={0.1}
                                            value={this.cellMargins.top}
                                            onChange={this.numChange.bind(this, 'cellMargins', 'top')}
                                        />
                                        <span>cm</span>
                                    </div>
                                    <div className='full-line'>
                                        <label className='common-label'>下</label>
                                        <input
                                            type='number'
                                            min={0}
                                            step={0.1}
                                            value={this.cellMargins.bottom}
                                            onChange={this.numChange.bind(this, 'cellMargins', 'bottom')}
                                        />
                                        <span>cm</span>
                                    </div>
                                    <div className='full-line'>
                                        <label className='common-label'>左</label>
                                        <input
                                            type='number'
                                            min={0}
                                            step={0.1}
                                            value={this.cellMargins.left}
                                            onChange={this.numChange.bind(this, 'cellMargins', 'left')}
                                        />
                                        <span>cm</span>
                                    </div>
                                    <div className='full-line'>
                                        <label className='common-label'>右</label>
                                        <input
                                            type='number'
                                            min={0}
                                            step={0.1}
                                            value={this.cellMargins.right}
                                            onChange={this.numChange.bind(this, 'cellMargins', 'right')}
                                        />
                                        <span>cm</span>
                                    </div>
                                </div>
                            </li>
                            <li className={this.tabIndex === TablePropsDialogTabType.RowHeight ? 'active' : null}>
                                <div>
                                    {/* <p className='common-title'>行高</p> */}
                                    <div className='full-line'>
                                        <label className='common-label'>高度</label>
                                        <input
                                            type='number'
                                            min={0}
                                            step={0.1}
                                            value={this.row.rowHeight}
                                            onChange={this.numChange.bind(this, 'row', 'rowHeight')}
                                        />
                                        <span>cm</span>
                                    </div>
                                    <div className='full-line'>
                                        <label className='common-label' />
                                        <input
                                            checked={this.row.bAuto === true}
                                            type='checkbox'
                                            onChange={this.numChange.bind(this, 'row', 'bAuto')}
                                        />
                                        <label>自动调整</label>
                                    </div>
                                </div>
                            </li>
                            <li className={this.tabIndex === TablePropsDialogTabType.ColumnWidth ? 'active' : null}>
                                <div>
                                    <div className='full-line'>
                                        <table>
                                        <tbody>
                                            <tr>
                                                <td>
                                                    <label className='prevColumn' onClick={this.handlerColumnClick.bind(this, 'prevColumn')}>prev</label>
                                                    <span style={{position: 'relative', right: '-20px'}}>
                                                        {this.column.columnIndex[0]}
                                                    </span>
                                                </td>
                                                <td style={{textAlign: 'center'}}>
                                                    <span>{this.column.columnIndex[1]}</span>
                                                </td>
                                                <td style={{textAlign: 'center'}}>
                                                    <span>{this.column.columnIndex[2]}</span>
                                                </td>
                                                <td>
                                                    <span>{this.column.columnIndex[3]}</span>
                                                    <label className='nextColumn' style={{position: 'relative', right: '-20px'}}
                                                        onClick={this.handlerColumnClick.bind(this, 'nextColumn')}>next</label>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <div className='full-line'>
                                                        <input
                                                            id={this.column.columnIndex[0]}
                                                            type='number'
                                                            min={0}
                                                            step={0.1}
                                                            disabled={1 > nColumnNum ? true : false}
                                                            value={this.setColumnValue(this.column.columnIndex[0])}
                                                            style={{width: '50px'}}
                                                            onChange={this.numChange.bind(this, 'column', 'width')}
                                                        />
                                                        <span>cm</span>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div className='full-line'>
                                                        <input
                                                            id={this.column.columnIndex[1]}
                                                            type='number'
                                                            min={0}
                                                            step={0.1}
                                                            disabled={2 > nColumnNum ? true : false}
                                                            value={this.setColumnValue(this.column.columnIndex[1])}
                                                            style={{width: '50px'}}
                                                            onChange={this.numChange.bind(this, 'column', 'width')}
                                                        />
                                                        <span>cm</span>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div className='full-line'>
                                                        <input
                                                            id={this.column.columnIndex[2]}
                                                            type='number'
                                                            min={0}
                                                            step={0.1}
                                                            disabled={3 > nColumnNum ? true : false}
                                                            value={this.setColumnValue(this.column.columnIndex[2])}
                                                            style={{width: '50px'}}
                                                            onChange={this.numChange.bind(this, 'column', 'width')}
                                                        />
                                                        <span>cm</span>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div className='full-line'>
                                                        <input
                                                            id={this.column.columnIndex[3]}
                                                            type='number'
                                                            min={0}
                                                            step={0.1}
                                                            disabled={4 > nColumnNum ? true : false}
                                                            value={this.setColumnValue(this.column.columnIndex[3])}
                                                            style={{width: '50px'}}
                                                            onChange={this.numChange.bind(this, 'column', 'width')}
                                                        />
                                                        <span>cm</span>
                                                    </div>
                                                </td>
                                            </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>
            </Dialog>
        );
    }

    private setDialogValue(): void {
        const tableProps = this.props.documentCore.getTableProperty();
        if ( null != tableProps ) {
            this.tableProps = tableProps;
            this.table.tableName = tableProps.tableName;
            this.table.repeatHeaderNum = tableProps.repeatHeaderNum;
            this.table.bRepeatHeader = 0 !== tableProps.repeatHeaderNum ? true : false;
            this.cellMargins.top = tableProps.tableDefaultMargins.top;
            this.cellMargins.bottom = tableProps.tableDefaultMargins.bottom;
            this.cellMargins.left = tableProps.tableDefaultMargins.left;
            this.cellMargins.right = tableProps.tableDefaultMargins.right;
            // this.row.rowHeight = tableProps.rowHeight;
            // this.column = {
            //     columnIndex: [1, 2, 3, 4],
            //     columnWidth: [],
            // };

            // for (const width of tableProps.columnWidth) {
            //     this.column.columnWidth.push(width);
            // }
        }
    }

    private tabClick(index: TablePropsDialogTabType): void {
        this.tabIndex = index;
    }

    private numChange(name: string, subname: string, e: any): void {
        this[name][subname] = parseFloat(e.target.value);
        if ( 'column' === name ) {
            const columnIndex = parseInt(e.target.id, 0) - 1;
            const oldWidth = this.column.columnWidth[columnIndex];
            let sumWidth = 0;
            let newWidth = parseFloat(e.target.value);

            for (const width of this.tableProps.columnWidth) {
                sumWidth += width;
            }

            if ( newWidth >= sumWidth ) {
                newWidth = sumWidth;

                for (let index = 0, count = this.column.columnWidth.length; index < count; index++) {
                    if ( columnIndex !== index ) {
                        this.column.columnWidth[index] = 0;
                    }
                }

                this.column.columnWidth[columnIndex] = newWidth;
            } else {
                let diff = oldWidth - newWidth;
                this.column.columnWidth[columnIndex] = newWidth;

                if ( 0 < diff ) {
                    if ( null != this.column.columnWidth[columnIndex + 1] ) {
                        this.column.columnWidth[columnIndex + 1] += diff;
                    } else {
                        this.column.columnWidth[0] += diff;
                    }
                } else if ( 0 > diff ) {
                    let bFlag = false;
                    diff = newWidth - oldWidth;

                    for (let index = columnIndex + 1, count = this.column.columnWidth.length; index < count; index++) {
                        if ( diff <= this.column.columnWidth[index] ) {
                            bFlag = true;
                            this.column.columnWidth[index] = parseFloat((this.column.columnWidth[index] - diff).toFixed(2));
                            break;
                        } else if ( 0 < diff ) {
                            diff = parseFloat((diff - this.column.columnWidth[index]).toFixed(2));
                            this.column.columnWidth[index] = 0;
                        }
                    }

                    if ( false === bFlag ) {
                        for (let index = 0; index < columnIndex; index++) {
                            if ( diff <= this.column.columnWidth[index] ) {
                                this.column.columnWidth[index] = parseFloat((this.column.columnWidth[index] - diff).toFixed(2));
                                break;
                            } else if ( 0 < diff ) {
                                diff = parseFloat((diff - this.column.columnWidth[index]).toFixed(2));
                                this.column.columnWidth[index] = 0;
                            }
                        }
                    }
                } else {
                    return;
                }
            }
        }

        this.setState({bRefresh: !this.state.bRefresh});
    }

    private checkChange(name: string, subname: string, e: any): void {
        this[name][subname] = !this[name][subname];
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private textChange(name: string, subname: string, e: any): void {
        this[name][subname] = e.target.value;
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private handlerColumnClick(position: string): void {
        switch (position) {
            case 'prevColumn':
                if ( 1 < this.column.columnIndex[0]) {
                    this.column.columnIndex[0]--;
                    this.column.columnIndex[1]--;
                    this.column.columnIndex[2]--;
                    this.column.columnIndex[3]--;
                    this.setState({bRefresh: !this.state.bRefresh});
                }
                break;

            case 'nextColumn':
                if ( this.column.columnWidth.length > this.column.columnIndex[3]) {
                    this.column.columnIndex[0]++;
                    this.column.columnIndex[1]++;
                    this.column.columnIndex[2]++;
                    this.column.columnIndex[3]++;
                    this.setState({bRefresh: !this.state.bRefresh});
                }
                break;

            default:
                break;
        }
    }

    private setColumnValue(columnIndex: number): any {

        if ( columnIndex <= this.column.columnWidth.length ) {
            return this.column.columnWidth[columnIndex - 1];
        }

        return '';
    }

    private open = (): void => {
        this.tabIndex = 0;
        // const table = this.table;
        this.setDialogValue();
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private close = (bRefresh?: boolean): void => {
        this.props.close(this.props.id, bRefresh);
        this.visible = false;
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private confirm = (id?: any): void => {
        const tableProps = {
            tableName: this.table.tableName,
            tableDefaultMargins: {
                top: this.cellMargins.top * 10,
                bottom: this.cellMargins.bottom * 10,
                left: this.cellMargins.left * 10,
                right: this.cellMargins.right * 10,
            },
            rowHeight: this.row.rowHeight * 10,
            columnWidth: this.column.columnWidth,
            bReadOnly: this.table.bReadOnly,
        };

        this.props.documentCore.setTableProperty(tableProps);
        this.close(true);
    }
}
