import * as React from 'react';
import { IDocumentParaPortion } from '../../../../model/ParaPortionProperty';
import { TEXT_SCARE_NUM } from '../../../../model/StyleProperty';
import {
    FONT_WEIGHT_TYPE,
    FONT_STYLE_TYPE,
    NewControlDefaultSetting,
    SelectButtonType,
    FONT_MAPPING,
    TRIANGLE_ARRAY,
    numtoFixed2,
    numtoFixed,
    PARATEXTEXTEND_RADIOFALSE_SYMBOLS,
    PARATEXTEXTEND_RADIOTRUE_SYMBOLS,
    PARATEXTEXTEND_CHECKFALSE_SYMBOLS,
    PARATEXTEXTEND_CHECKTRUE_SYMBOLS,
    isMacOs,
    DEFAULT_BUTTON_PROPS,
    PARA_BUTTON_PROP,
} from '../../../../common/commonDefines';
import { fonts } from '../../text/font';
import { TextVertAlign } from '../../../../model/core/TextProperty';
import { NewControl } from '../../../../model/core/NewControl/NewControl';
import * as radioboxActive from '../../images/radioboxActive.svg';
import * as radiobox from '../../images/radiobox.svg';
import * as checkboxActive from '../../images/checkboxActive.svg';
import * as checkbox from '../../images/checkbox.svg';

import * as radioboxActivePrint from '../../images/radioboxActivePrint.svg';
import * as radioboxPrint from '../../images/radioboxPrint.svg';
import * as checkboxActivePrint from '../../images/checkboxActivePrint.svg';
import * as checkboxPrint from '../../images/checkboxPrint.svg';
import ParaPageNum from '../../../../model/core/Paragraph/ParaPageNum';

// const isMacOs = navigator.userAgent.toLowerCase()
//         .indexOf('mac') > -1;
export default class ParaPortion extends React.Component<IDocumentParaPortion> {
    // private isMacOs: boolean;
    private borderEndElem: any;
    private borderEndElemBBox: any; // getBBox()
    private newControlInfo: any;

    constructor(props: IDocumentParaPortion) {
        super(props);
        // this.isMacOs = isMacOs;
        if (props && props.content.length === 1) {
            const borderEnd = props.content[0];
            const controlName = borderEnd.getNewControlName();
            if (controlName != null) {
                const newControl = props.documentCore.getNewControlByName(controlName);
                if (newControl != null && newControl.isAmongComboxOrListStruct()) {
                    this.borderEndElem = React.createRef();
                }
            }
        }
        // this.borderEndElem = React.createRef();
    }

    public componentDidMount(): void {
        if (this.newControlInfo && this.borderEndElem && this.borderEndElem.current != null) {
            // console.log('list/multi box refresh for triangle')
            this.borderEndElemBBox = this.borderEndElem.current.getBoundingClientRect();
            // this.forceUpdate();
            const {posX, posY, newControlBorder} = this.newControlInfo;
            const position = this.getNewControlPosition(posX, posY, newControlBorder);
            const next = this.borderEndElem.current.nextSibling;
            if (next) {
                next.setAttribute('points', position);
            }
        }
    }

    public render(): any {
        const textPro = this.props.textProperty;
        const vertAlign = textPro.vertAlign;
        const fontSize =
            vertAlign === TextVertAlign.Sub || vertAlign === TextVertAlign.Super
                ? (textPro.fontSize / 2) * TEXT_SCARE_NUM
                : textPro.fontSize;
        const weight = FONT_WEIGHT_TYPE.get(textPro.fontWeight);
        const fontStyle = FONT_STYLE_TYPE.get(textPro.fontStyle);
        // const textDecoration = TEXT_DECORATION_LINE_TYPE.get(this.props.textProperty.textDecorationLine);
        // if (vertAlign === TextVertAlign.Sub || vertAlign === TextVertAlign.Super) {
        //     console.log(333)
        // }

        let fontFamily = textPro.fontFamily;
        if (isMacOs) {
            if (fontFamily == null) {
                fontFamily = FONT_MAPPING['宋体'].mac;
            }
            // change render font family if mac
            fontFamily = JSON.parse(
                JSON.stringify(fontFamily),
            );
            const fontOptions = fonts[0].options;
            let index = -1;
            for (let i = 0; i < fontOptions.length; i++) {
                if (fontFamily === fontOptions[i].value) {
                    index = i;
                    break;
                }
            }
            if (index !== -1) {
                fontFamily = (fontOptions[index] as any).macValue;
            }
            textPro.fontFamily = fontFamily;
        }

        const { content, nHeaderFooter, documentCore, bNewLine } = this.props;
        const startPos = this.props.startPos;
        const endPos = this.props.endPos;
        const length = content.length;
        const bPrint = documentCore.getPrintStatus();

        const bNewControlBorder = ( 1 <= length &&
                                    (content[0].isNewControlStartBoder() || content[0].isNewControlEndBoder()) )
                                    ? true : false;
        const bNewControlBorderVisible = ( bNewControlBorder && content[0].isVisible() );
        const bNewControlContainerTitle = ( 1 < length && content[0].isNewControlStartBoder()) ? true : false;
        const bPopWinNewControl = (bNewControlBorder && content[0].isPopWinNewControl());
        // 页码
        const pageNums = [];
        this.newControlInfo = null;
        let text = '';
        let posX = null;
        let posY = null;
        let dy = null;
        let posX2 = null;
        let posY2 = null;
        let dy2 = null;
        let bParaEnd = false;
        let posX3 = null;
        let posY3 = null;
        let paraEnd = null;
        let item;

        let softLine;
        let mask: string;
        const cellId = this.props.cellId;
        if (cellId !== undefined) {
            const headerFooter = (nHeaderFooter === 1) ? '-header' : ((nHeaderFooter === 2) ? '-footer' : '');
            mask = 'url(#mask' + cellId + headerFooter + ')';
        }
        const buttons = [];
        for (let index = 0; index < length; index++) {
            item = content[index];

            // bNewControlBorder = (item.isNewControlStartBoder() || item.isNewControlEndBoder());
            // bNewControlBorderVisible = bNewControlBorder && item.isVisible();

            // TODO: is this the best way?
            if (item.isParaPageNum()) {
                pageNums.push(item);
                continue;
            }

            if ( item.isParaEnd() ) {
                bParaEnd = true;
                posX3 = '' + item.positionX;
                posY3 = '' + item.positionY;
                paraEnd = item.content;
                continue;
            }

            if (startPos <= index && endPos > index) {
                if (item.isSoftLine()) { // 段内换行
                    softLine = (
                        <text
                            x={item.positionX}
                            y={item.positionY}
                            dy='3'
                            clipPath={mask}
                            className='soft-line print-hide'
                            webkit-clip-path={mask}
                        >
                            {item.content}
                        </text>
                    );
                    break;

                } else if (item.isButton()) {
                    buttons.push(this.renderButton(item, mask))
                    continue;
                }
                text += item.bViewSecret
                    ? NewControlDefaultSetting.DefaultSecretReplaceChar
                    : item.content;
                if ( !(1 <= index && bNewControlContainerTitle) ) {
                    if (null != posX) {
                        posX = posX + ' ' + numtoFixed2(item.positionX);
                    } else {
                        posX = numtoFixed2(item.positionX);
                    }
                    if (null != posY /*&& posY !== item.positionY*/) {
                        posY = posY + ' ' + numtoFixed2(item.positionY);
                    } else {
                        posY = numtoFixed2(item.positionY);
                    }
                    if (null != dy) {
                        dy = dy + ' ' + (numtoFixed2(item.dy) || 0);
                    } else {
                        dy = numtoFixed2(item.dy);
                    }
                } else {
                    if (null != posX2) {
                        posX2 = posX2 + ' ' + numtoFixed2(item.positionX);
                    } else {
                        posX2 = numtoFixed2(item.positionX);
                    }
                    if (null != posY2 /*&& posY !== item.positionY*/) {
                        posY2 = posY2 + ' ' + numtoFixed2(item.positionY);
                    } else {
                        posY2 = numtoFixed2(item.positionY);
                    }
                    if (null != dy2) {
                        dy2 = dy2 + ' ' + (numtoFixed2(item.dy) || 0);
                    } else {
                        dy2 = numtoFixed2(item.dy);
                    }
                }
            }
        }

        posX = posX || item.positionX;
        posY = posY || item.positionY;

        // Check for duplicate posY values 去重 posY add by tinyzhi
        if (posY && typeof posY === 'string') {
            let posYList = posY.split(' ').map(parseFloat);
            if (posYList.every(y => y === posYList[0])) {
                    posY = posYList[0];
                }
        }
        //add by tinyzhi 上下标同样需要匹配
        if (dy && typeof dy === 'string') {
            let dyList = dy.split(' ').map(parseFloat);
            if (dyList.every(value => value === dyList[0])) {
                dy = dyList[0].toString(); // 只保留一个值
            }
        }
        
        // const documentCore = this.props.documentCore;
        if (bNewControlBorder) {
            const bLineBreak = ( bNewControlContainerTitle && 0 < startPos && text.length < length ) ? true : false;
            let sTitle = '';

            if ( bNewControlContainerTitle && 1 <= length ) {
                sTitle = bLineBreak ? text : text.substring(startPos + 1);
            }

            if ( bLineBreak ) {
                text = '';
            } else {
                text = ('' === sTitle ? text : text.substring(0, 1) );
            }

            if (false === bNewControlBorderVisible) {
                text = NewControlDefaultSetting.DefaultReplaceSpaceChar;
            }
            // get newcontrol instance
            // always the first one even if has ph etc
            // const newControlBorder: ParaNewControlBorder = content[0] as ParaNewControlBorder;
            const newControl = documentCore.getNewControlByName(content[0].getNewControlName());
            if (!bNewLine) {
                return (
                    <React.Fragment>
                        {this.renderNewControlBorder(text, posX, posY, fontSize, bPopWinNewControl, newControl, content[0], mask)}
                        {this.renderNewControlTitle(sTitle, mask, posX2, posY2, fontFamily, fontSize, weight, fontStyle, dy)}
                    </React.Fragment>
                );
            } else {
                return (
                    <g>
                        {this.renderNewControlBorder(text, posX, posY, fontSize, bPopWinNewControl, newControl, content[0], mask)}
                        {this.renderNewControlTitle(sTitle, mask, posX2, posY2, fontFamily, fontSize, weight, fontStyle, dy)}
                    </g>
                );
            }
        } else if (pageNums.length > 0) {
            const nHeaderFooter = this.props.nHeaderFooter;
            const bInHeaderFooter = (nHeaderFooter === 1 || nHeaderFooter === 2);
            const doc = documentCore.getDocument();
            const pageIndex = this.props.pageIndex;
            const pageNumDoms = pageNums.map((item: ParaPageNum, index) => {
                
                // const itemDetailed = (item as ParaPageNum);
                // tslint:disable-next-line: newline-per-chained-call
                // const bInHeaderFooter = item.getParent().isInHeaderFooter();
                const realPage = pageIndex + 1;
                item['parent'] = null;
                // console.log(documentCore.getDocument())
                // logicDocument may change, eg: print
                item.setLogicDocument(doc);

                const renderedText = item.getRenderedText(bInHeaderFooter, realPage);
                // const pageNumVal = itemDetailed.getPageNumValue(renderedText);

                // console.log(pageNumVal, realPage);
                const itemPositionX = numtoFixed2(item.positionX);
                // console.log(itemPositionX)
                // if (bInHeaderFooter === true) {
                //     // positionX is OK! should consider cursor instead, not here
                // }
                return (
                    <text
                        clipPath={mask}
                        key={pageIndex + '-' + index}
                        x={itemPositionX}
                        y={numtoFixed2(item.positionY)}
                        fontFamily={fontFamily}
                        fontSize={fontSize}
                        fontWeight={weight}
                        fontStyle={fontStyle}
                        fill={textPro.color}
                        textDecoration={textPro.textDecorationLine}
                        dy={numtoFixed2(dy)}
                        webkit-clip-path={mask}
                    >
                    {/* {bInHeaderFooter === true ? renderedText : (pageNumVal === realPage) ? renderedText : null} */}
                    {renderedText}
                    </text>
                );
            });

            return (
                <g>
                    <text
                        x={posX}
                        y={posY}
                        clipPath={mask}
                        fontFamily={fontFamily}
                        fontSize={fontSize}
                        fontWeight={weight}
                        fontStyle={fontStyle}
                        fill={textPro.color}
                        textDecoration={textPro.textDecorationLine}
                        dy={dy}
                        webkit-clip-path={mask}
                        overflow='hidden'
                    >
                        {' '}
                        {text}
                    </text>
                    {/* page number */}
                    {pageNumDoms}
                </g>
            );
        } else {
            const bSingleParaEnd = (bParaEnd && '' === text);
            if ( !bParaEnd || bSingleParaEnd ) {
                if (this.isParaTextExtendFalseSymbol(text)) {
                    const selectType = this.getParaTextExtendType(text);
                    let selectHref = radiobox;
                    // let isRed = content[0].isSelectButtonRed();

                    if (bPrint === true) {
                        switch (selectType) {
                            case SelectButtonType.Radiobox: {
                                selectHref = radioboxPrint;
                                break;
                            }
                            case SelectButtonType.RadioboxActive: {
                                selectHref = radioboxActivePrint;
                                // isRed = false;
                                break;
                            }
                            case SelectButtonType.Checkbox: {
                                selectHref = checkboxPrint;
                                break;
                            }
                            case SelectButtonType.CheckboxActive: {
                                selectHref = checkboxActivePrint;
                                // isRed = false;
                                break;
                            }
                            default: {
                                selectHref = radioboxPrint;
                                break;
                            }
                        }
                    } else {
                        switch (selectType) {
                            case SelectButtonType.Radiobox: {
                                selectHref = radiobox;
                                break;
                            }
                            case SelectButtonType.RadioboxActive: {
                                selectHref = radioboxActive;
                                // isRed = false;
                                break;
                            }
                            case SelectButtonType.Checkbox: {
                                selectHref = checkbox;
                                break;
                            }
                            case SelectButtonType.CheckboxActive: {
                                selectHref = checkboxActive;
                                // isRed = false;
                                break;
                            }
                            default: {
                                selectHref = radiobox;
                                break;
                            }
                        }
                    }
                    // console.log(content[0].width)
                    // console.log(this.props.textHeight) // height is according to portion
                    // console.log(selectHref);
                    const imgWidth = content[0].width * 0.8;
                    let imgHeight = this.props.textHeight * 0.8;
                    if (imgHeight === 0) {
                        imgHeight = imgWidth;
                    }

                    let classStr = 'select-button';
                    if (this.props.bRadioBoxRed === true) {
                        classStr += ' select-button-red';
                    }

                    const diff = (SelectButtonType.Checkbox === selectType ||
                        SelectButtonType.CheckboxActive === selectType) ? 1 : 0;

                    return (
                        // <text
                        //     x={posX}
                        //     y={posY}
                        //     fontFamily={fontFamily}
                        //     fontSize={fontSize}
                        //     fontWeight={weight}
                        //     fontStyle={fontStyle}
                        //     fill={textPro.color}
                        //     // fillOpacity={fillOpacity}
                        //     // textDecoration={textPro.textDecorationLine}
                        //     dy={dy}
                        // >
                        //     {' '}
                        //     {text}
                        // </text>
                        <image
                            x={posX}
                            y={numtoFixed2(posY - imgHeight + diff)}
                            clipPath={mask}
                            width={numtoFixed2(imgWidth)}
                            height={numtoFixed2(imgHeight)}
                            href={selectHref}
                            className={classStr}
                            webkit-clip-path={mask}
                            // transform={'translate(-0,-' + imgHeight + ')'}
                        />
                    );
                }

                const fillOpacity = bSingleParaEnd ? '40%' : undefined;
                if ( bSingleParaEnd ) {
                    text = paraEnd;
                    posX = posX3;
                    posY = posY3;
                }

                if (!bNewLine) {
                    return (
                        <React.Fragment>
                            <text
                                x={posX}
                                y={posY}
                                clipPath={mask}
                                fontFamily={fontFamily}
                                fontSize={fontSize}
                                fontWeight={weight}
                                fontStyle={fontStyle}
                                fill={textPro.color}
                                fillOpacity={fillOpacity}
                                // textDecoration={textPro.textDecorationLine}
                                dy={dy}
                                webkit-clip-path={mask}
                            >
                                {' '}
                            {text}
                            </text>
                            {buttons.length > 0 && this.renderButtonGradients()}
                            {buttons}
                            {softLine}
                        </React.Fragment>
                    );
                } else {
                    return (
                        <g>
                            <text
                                x={posX}
                                y={posY}
                                clipPath={mask}
                                fontFamily={fontFamily}
                                fontSize={fontSize}
                                fontWeight={weight}
                                fontStyle={fontStyle}
                                fill={textPro.color}
                                fillOpacity={fillOpacity}
                                // textDecoration={textPro.textDecorationLine}
                                dy={dy}
                                webkit-clip-path={mask}
                            >
                                {' '}
                            {text}
                            </text>
                            {buttons.length > 0 && this.renderButtonGradients()}
                            {buttons}
                            {softLine}
                        </g>
                    );
                }
            } else {
                return (
                    <g>
                        <text
                            x={posX}
                            y={posY}
                            clipPath={mask}
                            fontFamily={fontFamily}
                            fontSize={fontSize}
                            fontWeight={weight}
                            fontStyle={fontStyle}
                            fill={textPro.color}
                            // fillOpacity={'30%'}
                            // textDecoration={textPro.textDecorationLine}
                            dy={dy}
                            webkit-clip-path={mask}
                        >
                            {' '}
                            {text}
                        </text>
                        <text
                            x={posX3}
                            y={posY3}
                            clipPath={mask}
                            fontFamily={fontFamily}
                            fontSize={fontSize}
                            fontWeight={weight}
                            fontStyle={fontStyle}
                            fill={textPro.color}
                            fillOpacity={'40%'}
                            webkit-clip-path={mask}
                            // textDecoration={textPro.textDecorationLine}
                        >
                            {paraEnd}
                        </text>
                    </g>
                );
            }
        }
    }

    private renderButton(item: any, mask: string): any {
        const {fontFamily, fontSize, paddingLeft, paddingBottom} = DEFAULT_BUTTON_PROPS;
        // fill={PARA_BUTTON_PROP.color}
        return (
            <React.Fragment key={item.name}>
                <rect
                    x={numtoFixed2(item.positionX + 1.7)}
                    name={item.name} y={numtoFixed2(item.positionY - item.height + 1)}
                    width={numtoFixed2(item.width - 3.4)}
                    height={numtoFixed2(item.height)} 
                    className='button-rect'
                    clipPath={mask}
                    webkit-clip-path={mask}
                />
                <text
                    x={numtoFixed2(item.positionX + paddingLeft)}
                    y={numtoFixed2(item.positionY - paddingBottom)}
                    fontFamily={fontFamily}
                    fontSize={fontSize}
                    fill='#000'
                    clipPath={mask}
                    webkit-clip-path={mask}
                >
                    {item.content}
                </text>
                
            </React.Fragment>
        )
    }

    private renderButtonGradients(): any {
        return (
            <defs key="button-gradients">
                <linearGradient id="buttonGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                    <stop offset="0%" style={{stopColor: '#ffffff', stopOpacity: 1}} />
                    <stop offset="3%" style={{stopColor: '#f8f9fa', stopOpacity: 1}} />
                    <stop offset="100%" style={{stopColor: '#e9ecef', stopOpacity: 1}} />
                </linearGradient>
                <linearGradient id="buttonHoverGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                    <stop offset="0%" style={{stopColor: '#ffffff', stopOpacity: 1}} />
                    <stop offset="3%" style={{stopColor: '#f8f9fa', stopOpacity: 1}} />
                    <stop offset="100%" style={{stopColor: '#e2e6ea', stopOpacity: 1}} />
                </linearGradient>
                <linearGradient id="buttonActiveGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                    <stop offset="0%" style={{stopColor: '#e9ecef', stopOpacity: 1}} />
                    <stop offset="3%" style={{stopColor: '#dee2e6', stopOpacity: 1}} />
                    <stop offset="100%" style={{stopColor: '#ced4da', stopOpacity: 1}} />
                </linearGradient>
            </defs>
        );
    }

    private isParaTextExtendFalseSymbol(text: string): boolean {
        // const {paraTextExtendRadioFalseSymbols, paraTextExtendRadioTrueSymbols,
        //     paraTextExtendCheckFalseSymbols, paraTextExtendCheckTrueSymbols} = this.getParaTextExtendSymbols();
        return (PARATEXTEXTEND_RADIOFALSE_SYMBOLS.includes(text)
                || PARATEXTEXTEND_RADIOTRUE_SYMBOLS.includes(text)
                || PARATEXTEXTEND_CHECKFALSE_SYMBOLS.includes(text)
                || PARATEXTEXTEND_CHECKTRUE_SYMBOLS.includes(text));
    }

    private getParaTextExtendType(text: string): SelectButtonType {
        // const {paraTextExtendRadioFalseSymbols, paraTextExtendRadioTrueSymbols,
        //     paraTextExtendCheckFalseSymbols, paraTextExtendCheckTrueSymbols} = this.getParaTextExtendSymbols();
        let result = SelectButtonType.Radiobox;

        if (PARATEXTEXTEND_RADIOFALSE_SYMBOLS.includes(text)) {
            result = SelectButtonType.Radiobox;
        } else if (PARATEXTEXTEND_RADIOTRUE_SYMBOLS.includes(text)) {
            result = SelectButtonType.RadioboxActive;
        } else if (PARATEXTEXTEND_CHECKFALSE_SYMBOLS.includes(text)) {
            result = SelectButtonType.Checkbox;
        } else if (PARATEXTEXTEND_CHECKTRUE_SYMBOLS.includes(text)) {
            result = SelectButtonType.CheckboxActive;
        }

        return result;
    }

    private renderNewControlBorder(text: string, posX: any, posY: any, fontSize: any, bPopWinNewControl: boolean,
                                   newControl: NewControl, newControlBorder: any, mask: any): any {
        if ( text && 1 <= text.length ) {
            let fillColor = true !== bPopWinNewControl ? NewControlDefaultSetting.DefaultNewControlBorderColor :
                                                            NewControlDefaultSetting.DefaultNewControlBorderColor2;
            if (newControl != null && newControl.isMustInput()) {
                if (newControl.getNewControlTextLength() <= 0 ||
                    (newControl.isMultiAndRadio() === true && (null == (newControl as any).getSelected()))
                ) {
                    fillColor = NewControlDefaultSetting.DefaultMustInputBorderColor;
                }
            }
            const newControlType = (newControl != null) ? newControl.getType() : null;
            const bScale = 1 < this.props.documentCore.getViewScale();
            const diff = bScale ? 1 : (fontSize > 14 ? 1 : 0);

            posX = parseFloat(posX);
            posY = parseFloat(posY) - diff;
            const styleDom = {};
            if (fillColor === NewControlDefaultSetting.DefaultMustInputBorderColor) {
                styleDom['textShadow'] = 'none';
            }

            // section border
            if ( NewControlDefaultSetting.NewControlSectionStartBorder === text) {
                return (
                    <text
                        x={posX}
                        y={posY}
                        clipPath={mask}
                        textLength={fontSize / 2}
                        lengthAdjust='spacingAndGlyphs'
                        fill={fillColor}
                        style={styleDom}
                        fontStyle='normal'
                        fontSize={fontSize}
                        webkit-clip-path={mask}
                    >
                        {text}
                    </text>
                );
            } else if (NewControlDefaultSetting.NewControlSectionEndBorder === text) {
                if (!this.props.bNewLine) {
                    return (
                        <React.Fragment>
                            <text
                                x={posX}
                                y={posY}
                                clipPath={mask}
                                textLength={fontSize / 2}
                                lengthAdjust='spacingAndGlyphs'
                                fill={fillColor}
                                style={styleDom}
                                fontStyle='normal'
                                fontSize={fontSize}
                                webkit-clip-path={mask}
                            >
                                {text}
                            </text>
                        </React.Fragment>
                    );
                } else {
                    return (
                        <g>
                            <text
                                x={posX}
                                y={posY}
                                clipPath={mask}
                                textLength={fontSize / 2}
                                lengthAdjust='spacingAndGlyphs'
                                fill={fillColor}
                                style={styleDom}
                                fontStyle='normal'
                                fontSize={fontSize}
                                webkit-clip-path={mask}
                            >
                                {text}
                            </text>
                        </g>
                    );
                }
            }

            const bNewControlEndBorder = newControlBorder.content === ']';

            // console.log(points)
            let triangleElem: any;
            if (bNewControlEndBorder && TRIANGLE_ARRAY.includes(newControlType) === true &&
                this.props.documentCore.isProtectedMode() === false) {
                // default: 16: 5.34 * 22
                const points = this.getNewControlPosition(posX, posY, newControlBorder);
                triangleElem = (<polygon className='print-hide' points={points} fill={fillColor} clipPath={mask}/>);
            }

            // normal border
            if (!this.props.bNewLine) {
                return (
                    <React.Fragment>
                        <text
                            ref={this.borderEndElem}
                            x={posX}
                            y={posY}
                            clipPath={mask}
                            // textLength='2'
                            // lengthAdjust='spacingAndGlyphs'
                            fill={fillColor}
                            style={styleDom}
                            fontStyle='normal'
                            fontSize={fontSize}
                            webkit-clip-path={mask}
                        >
                            {text}
                        </text>
                        {triangleElem}
                    </React.Fragment>
                );
            } else {
                return (
                    <g>
                        <text
                            ref={this.borderEndElem}
                            x={posX}
                            y={posY}
                            clipPath={mask}
                            // textLength='2'
                            // lengthAdjust='spacingAndGlyphs'
                            fill={fillColor}
                            style={styleDom}
                            fontStyle='normal'
                            fontSize={fontSize}
                            webkit-clip-path={mask}
                        >
                            {text}
                        </text>
                        {triangleElem}
                    </g>
                );
            }
        }
    }

    private getNewControlPosition(posX: number, posY: number, newControlBorder: any): string {
        this.newControlInfo = {posX, posY, newControlBorder};
        let points = (posX + 4.9) + ',' + (posY - 5) + ' '
                + (posX + 4.9) + ',' + (posY + 1.4) + ' '
                + (posX + 9.4) + ',' + (posY + 1.4);
        // should change with control endborder's recalc width instead of ui width!
        if (this.borderEndElemBBox != null) {
            const bBoxWidth = this.borderEndElemBBox.width;
            const borderEndWidth = newControlBorder.width ? Math.min(newControlBorder.width, 8) : 8;
            const bBoxHeight = this.borderEndElemBBox.height;
            // console.log(bBoxWidth, bBoxHeight)
            const startX = posX + borderEndWidth;
            // 4.5 / 5.34 ; 1.4 / 22
            // points = (startX - bBoxWidth / 10) + ',' + (posY - bBoxHeight / 10) + ' '
            //     + (startX - bBoxWidth / 10) + ',' + (posY + bBoxHeight * 1.4 / 22) + ' '
            //     + (startX - bBoxWidth / 10 + (bBoxWidth * 4.5 / 5.34)) + ',' + (posY + bBoxHeight * 1.4 / 22);
            // const diff = 5.34 / 8;
            const edgeWidth = bBoxWidth * 4.5 / 5.34;
            let startXTrue = startX - borderEndWidth * 0.4;
            // if (borderEndWidth === 1) { // hide border
            //     startXTrue -= edgeWidth / 2;
            // }
            // startXTrue = numtoFixed(startXTrue);
            // points = startXTrue + ',' + numtoFixed2(posY - bBoxHeight / 10) + ' '
            //     + startXTrue + ',' + numtoFixed2(posY + bBoxHeight * 1.4 / 22) + ' '
            //     + numtoFixed2(startXTrue + edgeWidth) + ',' + numtoFixed2(posY + bBoxHeight * 1.4 / 22);

            if (newControlBorder.bVisible) {
                startXTrue -= edgeWidth / 2;
            } else if (!newControlBorder.bVisible || 0 < newControlBorder.remainWidth) {
                startXTrue -= borderEndWidth;
            }
            startXTrue = numtoFixed(startXTrue);
            points = startXTrue + ',' + numtoFixed2(posY - bBoxHeight / 8) + ' '
                + startXTrue + ',' + numtoFixed2(posY + bBoxHeight * 2 / 22) + ' '
                + numtoFixed2(startXTrue + bBoxWidth * 2 / 3) + ',' + numtoFixed2(posY + bBoxHeight * 2 / 22);
        }

        return points;
    }

    private renderNewControlTitle(title: string, mask: any, posX?: any, posY?: any, fontFamily?: any, fontSize?: any,
                                  weight?: any, fontStyle?: any, dy?: any): any {
        if ( title && 1 <= title.length ) {
            return (
                <text
                    x={posX}
                    y={posY}
                    clipPath={mask}
                    fontFamily={fontFamily}
                    fontSize={fontSize}
                    fontWeight={weight}
                    fontStyle={fontStyle}
                    fill={this.props.textProperty.color}
                    dy={dy}
                    webkit-clip-path={mask}
                >
                    {title}
                </text>
            );
        }
    }
}
