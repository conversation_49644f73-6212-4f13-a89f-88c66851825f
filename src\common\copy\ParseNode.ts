export class VNodeAttr {
    public key: string;
    public value: string;
}

export class VNode {
    public parentNode: VNode;
    public isBlock: boolean;
    public type: string;
    public tagName: string;
    public content: string;
    public attrs: object;
    public children: VNode[];
    public from?: string;
}

export let vNodeBlock = {
    'section': true,
    'header': true,
    'footer': true,
    'div': true,
    'p': true,
    'h1': true,
    'h2': true,
    'h3': true,
    'h4': true,
    'h5': true,
    'h6': true,
    'table': true,
    'tr': true,
    'dl': true,
    'ul': true,
    'li': true,
    'ol': true,
    'form': true,
    'w:p': true,
};
