import * as React from 'react';
import { INewControlProperty, NewControlType } from '../../../../common/commonDefines';
import Input from '../../ui/Input';
import Checkbox from '../../ui/CheckboxItem';
import Select from '../../ui/select/Select';
import CustomProperty from './CustomProperty';
import NewComboBoxList from '../modals/NewComboBoxList';
import { NewControlManageInfo } from './NewControlManage';

interface IDialogProps {
    documentCore: any;
    property: INewControlProperty;
    visible?: boolean;
}

export default class NewComboxBox extends React.Component<IDialogProps> {
    private newControlType: number;
    private isNewControlTypeDisable: boolean;
    private newControl: INewControlProperty;
    private docId: number;
    constructor(props: IDialogProps) {
        super(props);
        this.init();
    }

    public render(): any {
        return (
            <React.Fragment>
                <div className='editor-line'>
                    <span className='title'>常规</span>
                </div>
                <div className='editor-line'>
                    <div className='w-70'>名称</div>
                    <div className='right-auto'>
                        <Input
                            name='newControlName'
                            value={this.newControl.newControlName}
                            onChange={this.onChange}
                            onBlur={this.onBlur}
                        />
                    </div>
                </div>
                <div className='editor-line'>
                    <div className='w-70'>内部名称</div>
                    <div className='right-auto'>
                        <Input
                            name='newControlSerialNumber'
                            value={this.newControl.newControlSerialNumber}
                            onChange={this.onChange}
                            readonly={true}
                            placeholder={''}
                            disabled={true}
                        />
                    </div>
                </div>
                <div className='editor-line'>
                    <div className='w-70'>提示符</div>
                    <div className='right-auto'>
                        <Input
                            name='newControlInfo'
                            value={this.newControl.newControlInfo}
                            onChange={this.onChange}
                            onBlur={this.onBlur}
                        />
                    </div>
                </div>
                <div className='editor-line'>
                    <div className='w-70'>占位符</div>
                    <div className='right-auto'>
                        <Input
                            name='newControlPlaceHolder'
                            value={this.newControl.newControlPlaceHolder}
                            onChange={this.onChange}
                            onBlur={this.onBlur}
                        />
                    </div>
                </div>
                <div className='editor-line'>
                    <div className='w-70'>类型</div>
                    <div className='right-auto '>
                        <div className='w-050'>
                            <Select
                                data={[]}
                                value={this.newControlType}
                                name='newControlType'
                                disabled={true}
                            />
                        </div>
                        <div className='w-050'>
                            <Select
                                data={[]}
                                name='isNewControlTypeDisable'
                                value={this.isNewControlTypeDisable}
                                disabled={true}
                            />
                        </div>
                    </div>
                </div>
                <div className='editor-line editor-multi-line'>
                    <span className='title w-70'>属性</span>
                    <div className='right-auto newcontrol-prop'>
                        <div className='editor-line'>
                            <div className='w-050'>
                                <Checkbox
                                    name='isNewControlHidden'
                                    value={this.newControl.isNewControlHidden}
                                    onChange={this.checkedChange}
                                >
                                    隐藏
                                </Checkbox>
                            </div>
                            <div className='w-050'>
                                <Checkbox
                                    name='isNewControlCanntDelete'
                                    value={this.newControl.isNewControlCanntDelete}
                                    onChange={this.checkedChange}
                                >
                                    禁止删除
                                </Checkbox>
                            </div>
                        </div>

                        <div className='editor-line'>
                            <div className='w-050'>
                                <Checkbox
                                    name='isNewControlCanntEdit'
                                    value={this.newControl.isNewControlCanntEdit}
                                    onChange={this.checkedChange}
                                >
                                    禁止编辑
                                </Checkbox>
                            </div>
                            <div className='w-050'>
                                <Checkbox
                                    name='isNewControlMustInput'
                                    value={this.newControl.isNewControlMustInput}
                                    onChange={this.checkedChange}
                                >
                                    必填项
                                </Checkbox>
                            </div>
                        </div>

                        <div className='editor-line'>
                            <div className='w-050'>
                                <Checkbox
                                    name='isNewControlShowBorder'
                                    value={this.newControl.isNewControlShowBorder}
                                    onChange={this.checkedChange}
                                >
                                    显示边框
                                </Checkbox>
                            </div>

                            <div className='w-050'>
                                <Checkbox
                                    name='isNewControlReverseEdit'
                                    value={this.newControl.isNewControlReverseEdit}
                                    onChange={this.checkedChange}
                                >
                                    反向编辑
                                </Checkbox>
                            </div>
                        </div>

                        <div className='editor-line'>
                            <div className='w-050'>
                                <Checkbox
                                    name='isNewControlHiddenBackground'
                                    value={this.newControl.isNewControlHiddenBackground}
                                    onChange={this.checkedChange}
                                >
                                    隐藏背景色
                                </Checkbox>
                            </div>
                            <div className='w-050'>
                                <Checkbox
                                    name='retrieve'
                                    value={this.newControl.retrieve}
                                    onChange={this.checkedChange}
                                >
                                    索引检查
                                </Checkbox>
                            </div>
                        </div>
                        <div className='editor-line'>
                            <div className='w-050'>
                                <Checkbox
                                    name='tabJump'
                                    value={this.newControl.tabJump}
                                    onChange={this.checkedChange}
                                >
                                    键盘跳转
                                </Checkbox>
                            </div>
                        </div>
                    </div>

                    <div className='editor-line'>
                        <span className='title'>下拉选项</span>
                    </div>
                    <NewComboBoxList
                        value={this.newControl.newControlItems}
                        docId={this.docId}
                        onChange={this.onChange}
                        confirm={this.listChange}
                        name='newControlItems'
                    />
                    <div className='editor-line'>
                        <span className='title'>固有属性</span>
                    </div>
                    <div className='editor-line'>
                        <span className='w-050'>选中项前缀字符：</span>
                        <div className='w-050'>
                            <Input
                                value={this.newControl.selectPrefixContent}
                                name='selectPrefixContent'
                                onChange={this.onChange}
                                onBlur={this.onBlur}
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <span className='w-050'>未选中项前缀字符：</span>
                        <div className='w-050'>
                            <Input
                                value={this.newControl.prefixContent}
                                name='prefixContent'
                                onChange={this.onChange}
                                onBlur={this.onBlur}
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <span className='w-050'>内容分隔符：</span>
                        <div className='w-050'>
                            <Input
                                value={this.newControl.separator}
                                name='separator'
                                onChange={this.onChange}
                                onBlur={this.onBlur}
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <Checkbox
                            name='isShowValue'
                            value={this.newControl.isShowValue}
                            onChange={this.checkedChange}
                        >
                            显示value值
                        </Checkbox>
                    </div>
                </div>
                <div className='editor-line editor-multi-line'>
                    <span className='title w-70'>自定义属性</span>
                </div>
                <div className='editor-line'>
                    <CustomProperty
                        name='customProperty'
                        properties={this.newControl.customProperty}
                        documentCore={this.props.documentCore}
                    />
                </div>
            </React.Fragment>
        );
    }

    private init(): void {
        const newControl: any = this.newControl = {newControlName: null};
        const property = this.props.property;
        if (!property) {
            return;
        }
        this.docId = this.props.documentCore.getCurrentId();
        Object.keys(property)
        .forEach((key) => {
            newControl[key] = property[key];
        });
        switch (property.newControlType) {
            case NewControlType.Combox: {
                this.isNewControlTypeDisable = false;
                this.newControlType = NewControlType.Combox;
                break;
            }
            case NewControlType.ListBox: {
                this.isNewControlTypeDisable = false;
                this.newControlType = NewControlType.ListBox;
                break;
            }
            case NewControlType.MultiCombox: {
                this.isNewControlTypeDisable = true;
                this.newControlType = NewControlType.Combox;
                break;
            }
            case NewControlType.MultiListBox: {
                this.isNewControlTypeDisable = true;
                this.newControlType = NewControlType.ListBox;
                break;
            }
        }
        const items = newControl.newControlItems;
        if (!items || items.length === 0) {
            newControl.newControlItems = [{}];
        }
    }

    private onChange = (value: any, name: string): void => {
        this.newControl[name] = value;
    }

    private checkedChange = (value: any, name: string): void => {
        this.onChange(value, name);
        this.confirm(name);
    }

    private onBlur = (name: string): void => {
        // if (!this.isValidData(name)) {
        //     return;
        // }
        // const obj = {};
        // obj[name] = this.newControl[name];
        // const res = this.props.documentCore.setNewControlProperty(obj, this.props.property.newControlName);
        this.confirm(name);
    }

    private listChange = (value: any, name: string): void => {
        this.onChange(name, value);
        this.confirm(name);
    }

    private confirm = (name: string): void => {
        const value = this.newControl[name];
        NewControlManageInfo.confirm(name, value);
    }
}
