import * as ReactDOM from 'react-dom';
import * as React from 'react';
import { EmrEditorUI as EmrEditor } from './src/components/editor/Main2';
import {WasmInstance} from './src/common/WasmInstance';

WasmInstance.createWasmInstsanceAsync()
.then(() => {
  window['__EmrEditorComponent__'] = ReactDOM.render(

    <EmrEditor isTest={true} bShowMenu={true} bNeedCustomFont={false} bShowToolbar={true} textColorChangeInRevision={1}/>,
    document.getElementById('hz-editor-app')

  );
});
