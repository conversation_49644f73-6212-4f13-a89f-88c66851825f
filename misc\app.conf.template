gzip on;
gzip_min_length 1000;
gzip_types
application/atom+xml
application/x-javascript
application/javascript
application/json
application/ld+json
application/manifest+json
application/rss+xml
application/vnd.geo+json
application/vnd.ms-fontobject
application/x-font-ttf
application/x-web-app-manifest+json
application/xhtml+xml
application/xml
font/opentype
image/bmp
image/svg+xml
image/x-icon
text/cache-manifest
text/css
text/plain
text/vcard
text/vnd.rim.location.xloc
text/vtt
text/x-component
application/wasm
wasm
application/octet-stream
text/x-cross-domain-policy;
gunzip on;
gzip_static on;

server {
    listen	5000 default backlog=8192;
    # client_header_buffer_size       1k;
    # large_client_header_buffers     4 128k;

    location / {
        root /var/www/;
        index index.html index.htm;
        #try_files $uri /index.html;
    }
	location /api {
		proxy_pass http://$NGINX_WEBAPI_SERVER;
		client_max_body_size 0;
	}
    location ~ .*\.(jpg|jpeg|png|gif|ico|css|js|wasm)$ {
        root /var/www/;
        expires 365d;
    }
}
