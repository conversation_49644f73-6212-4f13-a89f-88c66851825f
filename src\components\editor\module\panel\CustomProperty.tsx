import * as React from 'react';
import '../../style/customProp.less';
import {ICustomProps, DataType, CustomPropertyElementType} from '../../../../common/commonDefines';
import {message} from '../../../../common/Message';
import Input from '../../ui/Input';
import Select from '../../ui/select/Select';
import { IFRAME_MANAGER } from '../../../../common/IframeManager';
import { NewControlManageInfo } from './NewControlManage';

interface IDialogProps {
    documentCore: any;
    name?: string;
    properties: ICustomProps[];
    type?: CustomPropertyElementType;
}

interface IState {
    bRefresh: boolean;
}

export default class CustomPropertyBtn extends React.Component<IDialogProps, IState> {
    private custompProps: ICustomProps[];
    private currentProp: ICustomProps;
    private activeIndex: number;
    private curInputIndex: number;
    private types: any[];
    private _names: object;
    private _activeName: string;
    private boolValues: any[];
    private activeProps: ICustomProps[];

    constructor(props: IDialogProps) {
        super(props);
        this.types = [{value: DataType.String, key: 'string'}, {value: DataType.Number,
            key: 'number'}, {value: DataType.Boolean, key: 'boolean'}];
        this.boolValues = [{key: 'true', value: 'true'}, {key: 'false', value: 'false'}];
        this.init();
    }

    public render(): any {
        return (
            <div className='newcontrol-custom'>
                {this.renderContent()}
            </div>
        );
    }

    private renderContent(): any {
        if (!this.custompProps || this.custompProps.length === 0) {
            return (<div className='custom-no-data custom-prop-add' onClick={this.rowClick.bind(this, 0)}>点击添加</div>);
        }
        return (
            <ul>
                <li>
                    <div>属性名</div>
                    <div>类型</div>
                    <div>属性值</div>
                    <div>操作</div>
                </li>
                {this.renderList()}
            </ul>
        );
    }

    private renderList(): any {
        return this.custompProps.map((prop, index) => {
            return (
                <li
                    key={index}
                    className={this.setClassName(index)}
                    onClick={this.rowClick.bind(this, index)}
                    onMouseEnter={this.mouseEnter.bind(this, index)}
                >
                    <div className='custom-prop-name'>
                        <Input
                            value={prop.name}
                            name='name'
                            onChange={this.onChange.bind(this, index)}
                            onBlur={this.onBlur.bind(this, index)}
                            focus={this.onFocus}
                        />
                    </div>
                    <div>
                        <Select
                            data={this.types}
                            // disabled={true}
                            value={prop.type}
                            name='type'
                            onChange={this.typeChange.bind(this, index)}
                        />
                    </div>
                    <div>
                        {this.renderValue(prop, index)}
                    </div>
                    <div className='custom-prop-btns'>
                        <span className='custom-prop-add'>+</span>
                        <label className='custom-prop-delete'>-</label>
                    </div>
                </li>
            );
        });
    }

    private renderValue(prop: ICustomProps, index: number): any {
        if (prop.type === DataType.Boolean) {
            return (
                <Select
                    data={this.boolValues}
                    // disabled={true}
                    value={prop.value}
                    name='value'
                    onChange={this.onChange.bind(this, index)}
                />
            );
        } else {
            return (
                <Input
                    value={prop.value}
                    name='value'
                    type={prop.type === DataType.Number ? 'number' : undefined}
                    onChange={this.onChange.bind(this, index)}
                />
            );
        }
    }

    private setClassName(index: number): string {
        let className: string;
        if (this.activeIndex === index) {
            className = 'active';
        }

        if (className && this.curInputIndex === index) {
            className += ' show-input';
        }

        return className;
    }

    private typeChange = (index: number, value: any, name: string): void => {
        this.onChange(index, value, name);
        this.setState({});
    }

    private onChange = (index: number, value: any, name: string): void => {
        this.currentProp = this.custompProps[index];
        this.currentProp[name] = value;
    }

    private addData = (index?: number): void => {
        const data: ICustomProps = {name: '', type: DataType.String, value: undefined};
        if (index === undefined) {
            this.custompProps.push(data);
        } else {
            this.custompProps.splice(index + 1, 0, data);
        }
        this.currentProp = data;
    }

    private init = (): void => {
        const names = this._names = {};
        const arrs: ICustomProps[] = this.custompProps = [];
        let datas: ICustomProps[];
        if (this.props.properties) {
            datas = this.props.properties;
        } else if (this.props.name && CustomPropertyElementType.NewControl === this.props.type) {
            datas = this.props.documentCore.getNewControlCustomPropsByName(this.props.name);
        } else {
            datas = [];
        }

        if (datas.length > 0) {
            datas.forEach((data) => {
                arrs.push({...data});
                names[data.name] = true;
            });
        } else {
            this.custompProps = [{type: DataType.String, name: '', value: ''}];
        }
    }

    private deleteData = (index: number): void => {
        const data = this.custompProps[index];
        delete this._names[data.name];
        this.custompProps.splice(index, 1);
        this.currentProp = undefined;
        this.confirm();
    }

    private onBlur = (index: number, name: string, input: any): void => {
        const currentProp = this.custompProps[index];
        if (!currentProp) {
            return;
        }

        // check xml element name validity
        const nameVal = input.value;
        if (this.checkXmlElementNameValidity(nameVal)) {
            input.classList.remove('warning');
        } else {
            input.classList.add('warning');
        }

        const preName = this._activeName;
        const currentName = currentProp.name;
        if (currentName && currentName !== preName) {
            const currentIndex = this.custompProps.findIndex((item, i) => item.name === currentName && i !== index);
            if (currentIndex > -1) {
                IFRAME_MANAGER.setDocId(this.props.documentCore.getCurrentId());
                message.error('已存在相同的名称')
                .then(() => {
                    currentProp.name = '';
                    input.focus();
                    this.setState({});
                });
                return;
            }
            delete this._names[preName];
            this._names[currentName] = true;
        }
        this.confirm();
        // else if (!currentName) {
        //     message.error('属性名称不能为空')
        //     .then(() => {
        //         // input.focus();
        //         this.setState({});
        //     });
        // }
    }

    private mouseEnter = (index: number, e: any): void => {
        this.currentProp = this.custompProps[index];
    }

    private rowClick(index: number, e: any): void {
        this.activeIndex = index;
        const target = e.target as HTMLDivElement;
        const className = target.className;
        if (this.curInputIndex !== index && className === 'custom-prop-name') {
            this.curInputIndex = index;
        } else if (className.indexOf('custom-prop-add') > -1) {
            this.addData(index);
            this.setState({});
        } else if (className === 'custom-prop-delete') {
            this.deleteData(index);
            this.setState({});
        }
    }

    private onFocus = (name: string, e: any): void => {
        this._activeName = e.target.value;
    }

    private checkXmlElementNameValidity(name: string): boolean {
        // Element names are case-sensitive
        // Element names must start with a letter or underscore
        // Element names cannot start with the letters xml (or XML, or Xml, etc)
        // Element names can contain letters, digits, hyphens, underscores, and periods
        // Element names cannot contain spaces

        let validity = !/^[xX][mM][lL].*/.test(name); // condition 3
        validity = validity && /^[a-zA-Z_].*/.test(name);  // condition 2
        validity = validity && /^[a-zA-Z0-9_\-\.]+$/.test(name); // condition 4
        return validity;
    }

    private validData(): boolean {
        const datas = this.custompProps.slice();
        if (datas.length === 0) {
            return true;
        }

        const obj = {};
        for (let index = 0, length = datas.length; index < length; index++) {
            const code = datas[index].name;
            if (!code) {
                continue;
            }
            if (obj[code] === true) {
                message.error(`第${index + 1}行，已存在相同的属性名`);
                return false;
            }
            obj[code] = true;
        }

        for (let index = datas.length - 1; index >= 0; index--) {
            const data = datas[index];
            if (!data.name) {
                datas.splice(index, 1);
                continue;
            }
            if (data.type === DataType.Number && Number.isNaN(Number(data.value))) {
                message.error(`第${index + 1}行的属性值为非法数字`);
                return false;
            }
            if (!this.checkXmlElementNameValidity(data.name)) {
                message.error(`属性名 不符合命名规范`);
                return false;
            }
        }
        this.activeProps = datas;
        return datas.length > 0;
    }

    private confirm = (id?: any): void => {
        if (!this.validData()) {
            return;
        }
        NewControlManageInfo.confirm(this.props.name, this.activeProps);
    }
}
