// http://officeopenxml.com/WPtext.php
// import { Break } from "./break";
// import { Caps, SmallCaps } from "./caps";
// import { Begin, End, Separate } from "./field";
// import { NumberOfPages, Page } from "./page-number";
import { RunProperties } from './properties';
// import { RunFonts } from "./run-fonts";
// import { SubScript, SuperScript } from "./script";
// import { Style } from "./style";
// import { Tab } from "./tab";
// import { Underline } from "./underline";

import { XmlComponent } from '../../xml-components';
import { Bold, Italics, Size, Color, HighLight, Hidden } from './formatting';
import { Underline } from './underline';
import { RunFonts } from './run-fonts';
import { SubScript, SuperScript } from './script';

/**
 * class that seem to only deal with PROPERTIES(w:rPr)
 */
export class Run extends XmlComponent {
    // protected readonly properties: RunProperties;
    protected properties: RunProperties;

    constructor() {
        super('w:r');

        // no redundant props
        // <w:p>
        //     <w:r></w:r>
        //     <w:r></w:r>
        // </w:p>
        // this.properties = new RunProperties();
        // this.root.push(this.properties);
    }

    public bold(): Run {
        this.initializeProperties();

        this.properties.push(new Bold());

        // no need <w:bcs>
        // this.properties.push(new BoldComplexScript());

        return this;
    }

    public italic(): Run {
        this.initializeProperties();

        this.properties.push(new Italics());
        // this.properties.push(new ItalicsComplexScript());
        return this;
    }

    public underline(underlineType?: string, color?: string): Run {
        this.initializeProperties();
        this.properties.push(new Underline(underlineType, color));
        return this;
    }

    public color(color: string): Run {
        this.initializeProperties();
        this.properties.push(new Color(color));
        return this;
    }

    public highLight(backgroundColor: string): Run {
        this.initializeProperties();
        this.properties.push(new HighLight(backgroundColor));
        return this;
    }

    public size(size: number): Run {
        this.initializeProperties();
        this.properties.push(new Size(size));
        // this.properties.push(new SizeComplexScript(size));
        return this;
    }

    // public rightToLeft(): Run {
    //     this.properties.push(new RightToLeft());
    //     return this;
    // }

    // public break(): Run {
    //     this.root.splice(1, 0, new Break());
    //     return this;
    // }

    // w:tab shouldn't be within w:rPr!
    // public tab(): Run {
    //     // order of "w:rPr" in "w:r" does not matter
    //     // this.root[0]['rootKey'] !== 'w:rPr' ?
    // this.root.splice(0, 0, new Tab()) : this.root.splice(1, 0, new Tab());
    //     this.initializeProperties();

    //     // w:tab is usually put at the last of the portion
    //     this.properties.push(new Tab());

    //     return this;
    // }

    // public pageNumber(): Run {
    //     this.root.push(new Begin());
    //     this.root.push(new Page());
    //     this.root.push(new Separate());
    //     this.root.push(new End());
    //     return this;
    // }

    // public numberOfTotalPages(): Run {
    //     this.root.push(new Begin());
    //     this.root.push(new NumberOfPages());
    //     this.root.push(new Separate());
    //     this.root.push(new End());
    //     return this;
    // }

    // public smallCaps(): Run {
    //     this.properties.push(new SmallCaps());
    //     return this;
    // }

    // public allCaps(): Run {
    //     this.properties.push(new Caps());
    //     return this;
    // }

    // public strike(): Run {
    //     this.properties.push(new Strike());
    //     return this;
    // }

    // public doubleStrike(): Run {
    //     this.properties.push(new DoubleStrike());
    //     return this;
    // }

    public subScript(): Run {
        this.initializeProperties();
        this.properties.push(new SubScript());
        return this;
    }

    public superScript(): Run {
        this.initializeProperties();
        this.properties.push(new SuperScript());
        return this;
    }

    public font(fontName: string, hint?: string | undefined): Run {
        this.initializeProperties();

        this.properties.push(new RunFonts(fontName, hint));
        return this;
    }

    public getRunProperty(): RunProperties {
        return this.properties;
    }

    public hidden(): Run {
        this.initializeProperties();
        this.properties.push(new Hidden());
        return this;
    }

    /**
     * initialize run properties if not
     */
    private initializeProperties(): void {
        if (!this.properties) {
            this.properties = new RunProperties();

            // should be the first child element
            // this.root.push(this.properties);
            this.root.unshift(this.properties);
        }
    }

    // public style(styleId: string): Run {
    //     this.properties.push(new Style(styleId));
    //     return this;
    // }
}
