import {GlobalEvent as gEvent, GlobalEventName as gEventName} from '../../../../common/GlobalEvent';
export class TextArea {
    /**
     * textArea
     */
    private textArea: HTMLTextAreaElement | null = null;
    /**
     * foreignObject
     */
    private textAreaWrapper: any | null = null;

    /**
     * 是否在进行中文输入法
     */
    private inComposition: boolean | object = false;

    private isPrint: boolean;
    private bContinue: boolean;

    /**
     * parentNode
     */
    private host: any = null;
    private taskList: any[];
    private bOutFocus: boolean;
    private inputWin: any;

    constructor(host: any, isPrint?: boolean) {
        // console.log(host)
        this.host = host;
        this.isPrint = isPrint;
        this.taskList = [];
        // this.initTextArea();
    }

    public wrapper = () => {
        return this.textAreaWrapper;
    }

    public focus = () => {
        if (this.bOutFocus === true || !this.textArea /*|| this.host.documentCore.isProtectedMode()*/) {
            return;
        }
        this.textArea.focus();
        if (this.textArea.getAttribute('tabindex')) {
            this.textArea.removeAttribute('tabindex');
        }
    }

    public getTextAreaElem(): HTMLTextAreaElement {
        return this.textArea;
    }

    public setTextAreaPosition(x: number, y: number): void {
        const wrapper = this.textAreaWrapper;
        wrapper.style.left = x + 'px';
        wrapper.style.top = y + 'px';
    }

    public addTextArea(textArea: any): void {
        this.textAreaWrapper = textArea;
        this.initTextArea(textArea);
    }

    // public insertHiddenTextArea(divDom: HTMLDivElement, parentNode: SVGElement): void {
    //     if (this.textAreaWrapper) {
    //         return;
    //     }
    //     const textArea = divDom.querySelector('#textarea_input');
    //     const foreignObject = document.createElement('div');
    //     foreignObject.className = 'textarea-wrapper';

    //     foreignObject.style.position = 'absolute';
    //     foreignObject.style.width = '1px';
    //     foreignObject.style.height = '1px';
    //     parentNode.parentNode.appendChild(foreignObject);
    //     foreignObject.appendChild(textArea);
    //     this.textAreaWrapper = foreignObject;
    //     this.initTextArea(textArea as HTMLTextAreaElement);
    // }

    public blur = () => {
        this.textArea.blur();
    }

    public addTabindex(): void {
        this.textArea.setAttribute('tabindex', '-1');
    }

    public getTaskList(): any[] {
        return this.taskList;
    }
    
    public reset() {
        if (this.inputWin) {
            this.inputWin.innerHTML = '';
            this.inputWin.style.display = 'none';
            this.textArea.value = '';
        }
    }

    /**
     * focus
     */
    private onFocus = (event: any) => {
        const cursorClassList = this.host.getCursor().classList;
        if (cursorClassList.contains('hidden')) {
            cursorClassList.remove('hidden');
        }
        event.stopPropagation();
    }

    /**
     * 去除光标闪动
     * textArea值清空
     */
    private onBlur = (event: any) => {
        // this.textArea.value = '';
        // this.host.cursorBlur();
        // this.host.setCursorVisible(false);
        this.reset();
        event.stopPropagation();
    }

    private removeCursor = (event: any): void => {
        this.host.resetMovingTableNewBorder();
        this.host.setCursorVisible(false);
    }

    /**
     * 中文输入开始
     */
    private onCompositionStart = (event: any) => {
        event.stopPropagation();
        if (this.bContinue === false || this.host.documentCore.isProtectedMode()) {
            return false;
        }
        this.inComposition = true;
        this.initInputDisplayWin();
        this.host.compositionStart();
    }

    private onCompositionUpdate = (event: any) => {
        event.stopPropagation();
        if (this.bContinue === false || this.host.documentCore.isProtectedMode()) {
            this.inComposition = false;
            return false;
        }
        if (this.inputWin) {
            this.inputWin.innerHTML = `<span>${event.data}</span>`;
        }
    }

    private onCompositionEnd = (event: any) => {
        event.stopPropagation();
        let filteredData = this.textArea.value.replace(/\n/g, '');//去除\n后再比较 修复回车后无法录入字符的bug
        if (this.bContinue === false || this.host.documentCore.isProtectedMode()
            || !this.inputWin.innerHTML || event.data !== filteredData) {
            this.inComposition = false;
            this.reset();
            return false;
        }
        this.host.compositionEnd(event.data, this.inComposition, event);
        this.inComposition = false;
        this.reset();

        gEvent.setEvent(this.host.docId, gEventName.SendMonitorData);
    }

    private onInput = (event: any) => {
        event.stopPropagation();
        if (this.bContinue === false || this.host.documentCore.isProtectedMode()) {
            return false;
        }
        // console.log("onInput")

        // 非中文输入
        if (false === this.inComposition) {
            if (event.data) {
                this.host.compositionStart();
            }
        } else {
            return;
        }

        const target = event.data;
        if (!this.taskList) {
            this.taskList = [];
        }

        if (!target && ('insertText' !== event.inputType || 'input' !== event.type)) {
            return;
        }

        //add by tinyzhi for new chromme 该代码可能会有副作用
        if( target && !this.inComposition && (event.inputType === 'insertFromPaste')) {
            return;
        }

        if (!this.inComposition) {
            const that = this;
            this.taskList.push(setTimeout(() => {
                that.host.insertCompositionInput(target, that.inComposition);
            }, 0));
        }

        this.textArea.value = '';
        gEvent.setEvent(this.host.docId, gEventName.SendMonitorData);
    }

    private onKeyDown = (event: any) => {
        // event.stopPropagation();
        // ctrl + c , ctrl + a, ctrl + f
        if (this.host.documentCore.isProtectedMode() &&
            (event.ctrlKey && 'KeyC' !== event.code && 'KeyF' !== event.code && 'KeyA' !== event.code)) {
            event.stopPropagation();
            return;
        }
        const host = this.host;
        if (host.externalEvent) {
            const bContinue = (this.bContinue = host.externalEvent.nsoKeyPressedEvent(
                event,
            ));
            if (bContinue === false) {
                return;
            }
        }
        if (event.ctrlKey === true && event.keyCode === 70) {
            gEvent.setEvent(this.host.docId, gEventName.Search);
            event.preventDefault();
            event.stopPropagation();
            return;
        }
        if (event.keyCode === 27) {
            gEvent.setEvent(this.host.docId, gEventName.Search, {visible: false});
            event.preventDefault();
            event.stopPropagation();
            return;
        }
        // this.host.onKeyDown(event, this.inComposition);
        if (!this.taskList) {
            this.taskList = [];
        }

        // 屏蔽Chrome的保存：Ctrl + S
        if (event.ctrlKey && 83 === event.keyCode) {
            event.preventDefault();
            return ;
        }

        if (!this.inComposition) {
            this.taskList.push(setTimeout(() => {
                this.host.onKeyDown(event, this.inComposition);
            }, 0));
        }
        if (event.key === 'Tab' || event.key === 'Backspace') {
            event.preventDefault();
        }

        gEvent.setEvent(this.host.docId, gEventName.SendMonitorData);
    }

    // todo: reset
    private onKeyUp = (event: any) => {
        event.stopPropagation();
        if (this.bContinue === false || this.host.documentCore.isProtectedMode()) {
            return false;
        }
        this.host.onKeyUp(event, this.inComposition);
    }

    /**
     * 设置textArea属性 绑定事件
     */
    private initTextArea(textArea: HTMLTextAreaElement): void {
        if (textArea) {
            textArea.setAttribute('wrap', 'off');
            textArea.setAttribute('autocorrect', 'off');
            textArea.setAttribute('autocapitalize', 'off');
            textArea.setAttribute('spellcheck', 'false');
            this.textArea = textArea;
            this.addEvents();
            this.taskList = [];
        }
    }
    private outFocus = (flag: boolean): void => {
        this.bOutFocus = flag;
    }

    private removeEvents = (): void => {
        const textArea = this.textArea;
        if (!textArea) {
            console.log('undelete event textArea');
            return;
        }
        textArea.removeEventListener('focus', this.onFocus);
        if (this.isPrint !== true) {
            textArea.removeEventListener(
                'compositionstart',
                this.onCompositionStart,
            );
            textArea.removeEventListener(
                'compositionupdate',
                this.onCompositionUpdate,
            );
            textArea.removeEventListener('compositionend', this.onCompositionEnd);
            textArea.removeEventListener('input', this.onInput);
        }
        textArea.removeEventListener('blur', this.onBlur);
        textArea.removeEventListener('keydown', this.onKeyDown);
        textArea.removeEventListener('keyup', this.onKeyUp);
        gEvent.deleteEvent(this.host.docId, gEventName.Blur, this.removeCursor);
        gEvent.deleteEvent(this.host.docId, gEventName.OutFocus, this.outFocus);
        this.textArea = null;
        this.textAreaWrapper = null;
        // this.textAreaWrapper.outerHTML = '';
        // const dom = this.host.getContainer().parentNode;
        // dom.removeEventListener('keydown', this.stopPropagation);
        // dom.removeEventListener('keyup', this.stopPropagation);
    }

    private addEvents(): void {
        const textArea = this.textArea;
        textArea.addEventListener('focus', this.onFocus);
        textArea.addEventListener('blur', this.onBlur);
        textArea.addEventListener('keydown', this.onKeyDown);
        textArea.addEventListener('keyup', this.onKeyUp);
        gEvent.addEvent(this.host.docId, gEventName.Blur, this.removeCursor);
        gEvent.addEvent(this.host.docId, gEventName.OutFocus, this.outFocus);
        if (this.isPrint !== true) {
            textArea.addEventListener(
                'compositionstart',
                this.onCompositionStart,
            );
            textArea.addEventListener(
                'compositionupdate',
                this.onCompositionUpdate,
            );
            textArea.addEventListener('compositionend', this.onCompositionEnd);
            textArea.addEventListener('input', this.onInput);
        }

        // const dom = this.host.getContainer().parentNode;
        // dom.addEventListener('keydown', this.stopPropagation);
        // dom.addEventListener('keyup', this.stopPropagation);
        // document.addEventListener('keydown', (e) => {
        //     console.log(e);
        // });

        gEvent.addEvent(this.host.docId, gEventName.UnMounted, this.removeEvents);
    }

    private initInputDisplayWin(): void {
        if (!this.inputWin) {
            this.inputWin = document.createElement('div');
            this.inputWin.id = 'input_display_win';
            this.inputWin.style.left = this.textAreaWrapper.style.left;
            this.inputWin.style.top = this.textAreaWrapper.style.top;
            this.inputWin.innerHTML = '';
            this.host?.currentRef.current.appendChild(this.inputWin);
        } else {
            this.inputWin.style.left = this.textAreaWrapper.style.left;
            this.inputWin.style.top = this.textAreaWrapper.style.top;
            this.inputWin.innerHTML = '';
            this.inputWin.style.display = 'inline-block';
        }
    }

    // private stopPropagation = (e: any): void => {
    //     e.stopPropagation();
    // }
}
