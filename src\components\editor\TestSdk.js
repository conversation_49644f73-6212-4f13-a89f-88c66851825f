// 引入SDK并使其在全局范围内可用
(function() {
    // 定义一个全局Editor类
    var originalScript = document.createElement('script');
    originalScript.src = '/sdk.js';
    originalScript.onload = function() {
        // SDK加载后初始化全局Editor类
        console.log('SDK加载成功！');
        // 定义一个全局的Editor类，包装SDK提供的Editor
        window.Editor = function() {
            const sdkInstance = {
                init: function(options) {
                    console.log('初始化编辑器，选项:', options);
                    
                    // 创建iframe
                    const iframe = document.createElement('iframe');
                    iframe.style.width = '100%';
                    iframe.style.height = '100%';
                    iframe.style.border = 'none';
                    iframe.src = options.src;
                    
                    // 清空容器
                    const container = options.dom;
                    while (container.firstChild) {
                        container.removeChild(container.firstChild);
                    }
                    
                    // 添加iframe
                    container.appendChild(iframe);
                    
                    // 返回一个Promise以兼容原始SDK
                    return new Promise(function(resolve) {
                        iframe.onload = function() {
                            // 创建编辑器接口
                            const editorApi = {
                                getEditor: function() {
                                    return {
                                        undo: function() {
                                            console.log('执行撤销操作');
                                            return Promise.resolve();
                                        },
                                        redo: function() {
                                            console.log('执行重做操作');
                                            return Promise.resolve();
                                        },
                                        insertText: function(text) {
                                            console.log('插入文本:', text);
                                            return Promise.resolve();
                                        },
                                        getText: function() {
                                            return Promise.resolve('这是编辑器内容示例');
                                        }
                                    };
                                }
                            };
                            
                            resolve(editorApi);
                        };
                    });
                }
            };
            
            return sdkInstance;
        };
        
        // 触发自定义事件通知SDK已加载
        document.dispatchEvent(new Event('sdk-ready'));
    };
    
    // 处理加载失败
    originalScript.onerror = function() {
        console.error('无法加载SDK脚本');
        document.dispatchEvent(new Event('sdk-failed'));
    };
    
    document.head.appendChild(originalScript);
})(); 