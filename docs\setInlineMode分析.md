# 内联模式高度变化事件集成指南

## 1. 概述

`setInlineMode` 是 hz-editor 中的一个重要功能，用于切换编辑器的显示模式。当启用内联模式（Inline Mode）时，编辑器会改变其页面属性和显示方式，使其能够更好地嵌入到其他容器中。

当内联模式下编辑器内容高度变化时，需要容器能够自适应调整高度。本文档详细说明了如何实现内联模式高度变化事件的监听和处理。

## 2. 内联模式高度变化事件实现

### 2.1 后端实现

在编辑器核心代码中，我们添加了 `inlineHeightChange` 事件，用于通知容器页面内联模式下的高度变化。

1. 在 `IExternalEvent` 接口中添加事件定义：

```typescript
interface IExternalEvent {
    // 其他事件...
    inlineHeightChange: (rectInfo: IRectInfo) => void;
}
```

2. 在 `ExternalEvent` 类中实现事件触发方法：

```typescript
public inlineHeightChange(rectInfo: IRectInfo): void {
    this.trigger('inlineHeightChange', rectInfo);
}
```

3. 在编辑器内容高度变化时触发事件：

```typescript
// 在适当的地方检测高度变化并触发事件
const rectInfo = this.getDocumentRect();
this.externalEvent.inlineHeightChange(rectInfo);
```

### 2.2 事件转发机制

为了确保事件能够从编辑器核心传递到容器页面，我们需要在入口文件中添加事件转发逻辑：

1. 在 `site.tsx` 中添加事件转发：

```typescript
// 注册内联高度变化事件
editor.externalEvent.on('inlineHeightChange', (rectInfo) => {
    // 将事件转发给父窗口
    window.parent.postMessage({
        __emrType: 'event',
        type: 'inlineHeightChange',
        data: rectInfo
    }, '*');
});
```

2. 在 `main.site.tsx` 中也添加类似的事件转发逻辑，确保第三方集成时也能正确接收事件。

## 3. 前端集成步骤

在集成编辑器的前端页面中，需要按照以下步骤实现内联模式高度变化的处理：

### 3.1 初始化编辑器

```javascript
// 获取编辑器容器
const editorContainer = document.getElementById('editor-container');

// 初始化编辑器
async function initEditor() {
    // 加载编辑器 SDK
    const editor = await window.EmrEditor.createNew('editor');
    
    // 设置内联模式
    editor.setInlineMode(true);
    
    // 返回编辑器实例
    return editor;
}
```

### 3.2 注册高度变化事件监听器

```javascript
// 初始化编辑器
const editor = await initEditor();

// 获取虚拟机实例
const vm = editor.getVM();

// 注册内联模式高度变化事件
vm.setEvent('inlineHeightChange', handleInlineEvent);
```

### 3.3 实现高度变化处理函数

```javascript
// 内联模式下的高度变化监听器
function handleInlineEvent(rectInfo) {
    if (!rectInfo) {
        return;
    }
    
    // 如果有高度信息，则处理高度变化事件
    if (rectInfo.height) {
        // 添加额外空间以确保内容不被裁剪
        const padding = 100; // 固定的额外空间
        const newHeight = Math.round(rectInfo.height) + padding;
        
        // 更新容器高度
        editorContainer.style.height = `${newHeight}px`;
    }
}
```

## 4. 注意事项和最佳实践

### 4.1 容器样式设置

在实现内联模式高度变化时，需要注意编辑器容器的样式设置：

```css
.editor-container {
    width: 100%;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-bottom: 20px;
    min-height: 300px; /* 初始最小高度 */
    position: relative;
    transition: height 0.3s ease; /* 平滑过渡效果 */
    overflow: hidden;
    display: block;
    box-sizing: border-box;
}
```

关键点：

- 不要在 CSS 中设置固定的 `height` 属性，而是使用 `min-height`
- 使用 JavaScript 动态设置 `style.height` 属性
- 添加过渡效果可以使高度变化更加平滑

### 4.2 高度计算策略

在处理高度变化时，建议添加额外的空间以避免内容被裁剪：

```javascript
// 添加额外空间以确保内容不被裁剪
const padding = 100; // 固定的额外空间
const newHeight = Math.round(rectInfo.height) + padding;
```

根据实际情况，可以调整 padding 的值或使用百分比计算：

```javascript
// 使用百分比计算额外空间
const padding = Math.max(50, rectInfo.height * 0.1); // 至少 50px 或者高度的 10%
```

### 4.3 性能优化

在处理高度变化事件时，可能会频繁触发事件。为了减少不必要的 DOM 操作，可以考虑使用节流或防抖技术：

```javascript
// 使用节流函数处理高度变化
let resizeTimer;
function handleInlineEvent(rectInfo) {
    if (!rectInfo || !rectInfo.height) return;
    
    clearTimeout(resizeTimer);
    resizeTimer = setTimeout(() => {
        const padding = 100;
        const newHeight = Math.round(rectInfo.height) + padding;
        editorContainer.style.height = `${newHeight}px`;
    }, 100); // 100ms 的节流延时
}
```

### 4.4 兼容性处理

在内联模式下，编辑器的 UI 渲染会有所不同：

1. **滚动条控制**：
   ```typescript
   overflow={documentCore.isInlineMode() ? 'auto hidden' : ''} // 控制滚动条
   ```

2. **页面高度计算**：
   ```typescript
   if (documentCore.isInlineMode()) {
       pageProps.height += pageProps.paddingBottom + pageProps.paddingTop;
   }
   ```

3. **页面边框显示**：
   ```typescript
   const padding = showPageBorder && !documentCore.isInlineMode() ? this.renderPageBorder() : null;
   ```

### 3.2 内容变化事件处理

内联模式下，编辑器会监听内容变化并触发相应事件：

```typescript
private handleContentChange = () => {
    if (!this.documentCore || !this.documentCore.isInlineMode() || !this.myRef.current) { 
        // 非内联模式下不触发事件，也不计算高度
        this._inlineOldRectInfo = {
            height: 0,
            width: 0,
        };
        return;
    }
    // 使用 requestAnimationFrame 确保在下一帧渲染前执行
    // 这样可以保证排版已完成
    requestAnimationFrame(() => {
        const pageProp = this.documentCore.render().pageProperty;
        // 获取文档实际高度
        const docHeight = this.documentCore.isInlineMode() ? 
            (pageProp.paddingTop + pageProp.paddingBottom) : 0;
        const curHeight = pageProp.height + docHeight;
        const oldRect = this._inlineOldRectInfo;
        if (Math.abs(curHeight - oldRect.height) > 1 || Math.abs(pageProp.width - oldRect.width) > 1) {
            const cursorPos = this.documentCore.getCursorPosition();
            this.traiggerInlineEvent({
                cursorX: cursorPos.x,
                cursorY: cursorPos.y2,
                height: curHeight,
                isInline: true,
                width: pageProp.width,
            });
            this._inlineOldRectInfo = {
                height: Math.ceil(curHeight),
                width: Math.ceil(pageProp.width),
            };
        }
    });
}
```

## 4. 内联模式的特点

1. **无限高度**：内联模式下，页面高度被设置为 `Infinity`，允许内容无限延伸。
   ```typescript
   height: Infinity
   ```

2. **简化页面边距**：内联模式使用更简单的页面边距设置。
   ```typescript
   paddingBottom: 5,
   paddingLeft: 5,
   paddingRight: 5,
   paddingTop: 5,
   ```

3. **自适应容器**：内联模式能够根据容器宽度自动调整编辑器宽度。

4. **状态保存与恢复**：切换内联模式时，会保存之前的状态，以便在退出内联模式时恢复。

## 5. 调用链路

`setInlineMode` 的调用链路如下：

1. 外部接口调用 `IExternalInterface.setInlineMode(flag: boolean)`
2. 调用 `OperateDocument.setInlineMode(flag: boolean)`
3. 调用 `DocumentCore.setInlineMode(flag: boolean)`
4. 最终调用 `Document.setInlineMode(flag: boolean)`

## 6. 内联模式高度变化的事件机制

通过对编辑器代码的深入分析，发现内联模式下高度变化的关键机制如下：

### 6.1 内联事件触发机制

在 `Main.tsx` 中，编辑器实现了一个专门的内联事件系统：

```typescript
// 内联事件数组，用于存储注册的事件处理函数
private _inlineEvents: Array<(rectInfo: Partial<IInlineRectInfo>) => void> = [];

// 注册内联事件监听器
public setInlineEvent(func: any): void {
    if (typeof func === 'function' ) {
        this._inlineEvents.push(func);
    }
}

// 触发内联事件
private traiggerInlineEvent = (rectInfo: Partial<IInlineRectInfo>): void => {
    this._inlineEvents.forEach(func => func(rectInfo));
}
```

### 6.2 内容变化时的高度更新

当编辑器内容发生变化时，会触发 `handleContentChange` 方法，该方法会计算当前内容高度，并在高度发生变化时触发内联事件：

```typescript
private handleContentChange = () => {
    if (!this.documentCore || !this.documentCore.isInlineMode() || !this.myRef.current) {
        this._inlineOldRectInfo = { height: 0, width: 0 };
        return;
    }
    requestAnimationFrame(() => {
        const pageProp = this.documentCore.render().pageProperty;
        const docHeight = this.documentCore.isInlineMode() ? 
            (pageProp.paddingTop + pageProp.paddingBottom) : 0;
        const curHeight = pageProp.height + docHeight;
        const oldRect = this._inlineOldRectInfo;
        if (Math.abs(curHeight - oldRect.height) > 1 || Math.abs(pageProp.width - oldRect.width) > 1) {
            const cursorPos = this.documentCore.getCursorPosition();
            this.traiggerInlineEvent({
                cursorX: cursorPos.x,
                cursorY: cursorPos.y2,
                height: curHeight,
                isInline: true,
                width: pageProp.width,
            });
            this._inlineOldRectInfo = {
                height: Math.ceil(curHeight),
                width: Math.ceil(pageProp.width),
            };
        }
    });
}
```

### 6.3 正确的事件监听方式

在使用内联模式时，应该通过以下方式监听高度变化：

1. 使用 `setInlineEvent` 方法注册一个内联事件监听器
2. 在监听器中处理 `height` 和 `width` 属性的变化
3. 根据这些变化调整外部容器的尺寸

示例代码：

```javascript
// 注册内联事件监听器
editor.setInlineEvent(function(rectInfo) {
    if (rectInfo && rectInfo.height) {
        // 更新容器高度，可以添加一些额外空间以防止内容被裁剪
        const newHeight = Math.round(rectInfo.height) + 20;
        editorContainer.style.height = `${newHeight}px`;
    }
});
```

### 6.4 模式切换时的事件

当调用 `setInlineMode` 切换模式时，会触发 `inlineModeChange` 方法，该方法会发送一个包含 `isInline` 标志的事件：

```typescript
public inlineModeChange(flag: boolean): void {
    this.traiggerInlineEvent({isInline: flag});
}
```

这使得外部容器可以知道编辑器的模式已经改变，并做出相应的调整。

## 7. 总结

`setInlineMode` 机制是 hz-editor 提供的一种灵活的显示模式切换功能，使编辑器能够更好地适应不同的使用场景。通过保存和恢复页面属性和缩放比例，它实现了在普通模式和内联模式之间的无缝切换。

内联模式主要用于将编辑器嵌入到网页的某个区域中，使其能够更好地与周围内容融合，同时保持编辑器的核心功能。这对于需要在网页中提供富文本编辑功能的应用场景非常有用。
