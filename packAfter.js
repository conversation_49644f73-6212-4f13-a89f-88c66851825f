const fs = require('fs');
fs.readFile('./dist/style.css', function(err, content) {
    if (err || !content) {
        return;
    }
    var text = 'export default `' + content.toString() + '`';

    writeFile(text);
})

function writeFile(content) {

    fs.writeFile('./dist/style.js', content, { flag: 'wx' },function(err) {

        if (err) {
            return;
        }
        console.log('Saved success!');
    });

}

