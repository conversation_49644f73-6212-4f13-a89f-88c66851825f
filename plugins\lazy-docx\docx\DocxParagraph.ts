import { ISerialParaObj, SerialObjType } from '../../serialize/serialInterface';
import { IParagraphOptions, Paragraph, ParagraphChild } from 'docx';
import DocxImage from './DocxImage';
import DocxText from './DocxText';
import { IAbstractDocx, IDocx } from './IDocx';

export default class DocxParagraph extends IAbstractDocx implements IDocx {

    public static demo(): Paragraph {
        const option: IParagraphOptions = {
            children: [
                DocxText.demo(),
            ]
        };
        return new Paragraph(option);
    }

    constructor(private readonly paraObj: ISerialParaObj) { super(); }

    public buildTo(collector: Paragraph[] = []): Paragraph[] {
        if (this.paraObj.type !== SerialObjType.Paragraph) {
            return collector;
        }
        const paraOption: any = Object.assign({}, this.paraObj);
        this.deleteUnUsefulProp(paraOption, 'type', 'children');
        const children: ParagraphChild[] = [];
        for (const portion of this.paraObj.children) {
            if (portion.type === SerialObjType.Portion) {
                for (const content of portion.children) {
                    if (content.type === SerialObjType.Image) {
                        new DocxImage(content as any).buildTo(children);
                    } else {
                        new DocxText(content as any).buildTo(children);
                    }
                }
            }
        }
        // ! 必须取代原有children
        paraOption.children = children;
        const paragraph = new Paragraph(paraOption);
        collector.push(paragraph);
        return collector;
    }

}
