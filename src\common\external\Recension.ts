import { DocumentCore } from '../../model/DocumentCore';
import { EmrEditor } from '../../components/editor/Main';
import { isValidRevisionColor, IRevisionSetting, ResultType, REVISION_LEVEL } from '../commonDefines';
import { ExternalAction } from './ExternalAction';
export default class Recension extends ExternalAction {
    private _documentCore: DocumentCore;
    private _host: EmrEditor;
    constructor(host: EmrEditor) {
        super(host.state.documentCore);
        this._host = host;
        this._documentCore = host.state.documentCore;
    }

    public setRecensionProtectMode(bFlag: boolean): number {
        if (typeof bFlag !== 'boolean') {
            return ResultType.ParamError;
        }
        return this._documentCore.setRevisionLevelEquMode(bFlag);
    }

    public enableTrackRevisions(flag: boolean): number {
        if (typeof flag !== 'boolean') {
            return ResultType.ParamError;
        }

        const bTrackRevision = this._documentCore.isTrackRevisions();
        if (bTrackRevision === flag) {
            return ResultType.UnEdited;
        }
        if (flag === true) {
            this._documentCore.startTrackRevisions();
        } else {
            this._documentCore.closeTrackRevisions();
        }

        // this._host.handleRefresh();

        return ResultType.Success;
    }

    public getTrackRevisions(): boolean {
        return this._documentCore.isTrackRevisions();
    }

    public showRecension(flag: number): number {
        if (typeof flag !== 'number' || ![1, 2].includes(flag)) {
            return ResultType.ParamError;
        }

        // const bFinalRevision = flag === 2;
        // if (this._documentCore.isFinalRevision() === bFinalRevision) {
        //     return ResultType.UnEdited;
        // }

        const res = this._documentCore.changeRevisionState(flag === 2);
        // if (res === ResultType.Success) {
        //     this._host.handleRefresh();
        // }
        return res;
    }

    /**
     * 当开启修订功能时，设置修订信息
     * @param strUserCode 修订者工号
     * @param strUserName 修订者姓名(必须保证姓名的唯一性,否则相同名字的人的修订会一样)
     * @param strMemo 备注
     * @param nMarkStyle 修订痕迹风格(1:单删除线、单下划线 2:双删除线、双下划线 3:三删除线、三下划线)
     * @param strMarkColor 修订颜色(可取以下颜色：红色,蓝色,绿色)
     */
    public setRecensionInfo(strUserCode: string, strUserName: string, strMemo: string,
                            nMarkStyle: number, strMarkColor: string): number {
        if (!strUserCode || typeof strUserCode !== 'string') {
            return ResultType.ParamError;
        }
        if (!strUserName || typeof strUserName !== 'string') {
            return ResultType.ParamError;
        }
        if (strMemo && typeof strMemo !== 'string') {
            return ResultType.ParamError;
        }
        if (strMarkColor && !isValidRevisionColor(strMarkColor)) {
            return ResultType.ParamError;
        }
        if (nMarkStyle === 0 || nMarkStyle && (typeof nMarkStyle !== 'number' || ![1, 2, 3].includes(nMarkStyle))) {
            return ResultType.ParamError;
        }

        const config: IRevisionSetting = {
            userName: strUserName || '',
            userId: strUserCode || '',
            description: strMemo || '',
            style: nMarkStyle ? nMarkStyle - 1 : undefined,
            color: strMarkColor || '',
            level: REVISION_LEVEL[0],
        };
        this._documentCore.setRevision(config);
        return ResultType.Success;
    }

    public getRecensionShowState(): number {
        const res = this._documentCore.isFinalRevision();
        return res ? 2 : 1;
    }

    public acceptRevisions(): void {
        this._documentCore.acceptRevisionChanges();
        this._host.handleRefresh();
    }

    public getRevisionCount(): {
        total: number;           // 总修订数
        additions: number;       // 新增修订数
        deletions: number;       // 删除修订数
        users: string[];         // 参与修订的用户列表
    } {
        return this._documentCore.getRevisionCount();
    }

    public getRevisionDetails(author?: string): Array<{
        userName: string;        // 修订者姓名
        userId: string;          // 修订者ID
        time: string;            // 修订时间 (ISO格式)
        level: number;           // 修订级别
        type: string;            // 修订类型：'add' | 'remove'
        value: string;           // 修订内容
    }> {
        return this._documentCore.getRevisionDetails(author);
    }
}
