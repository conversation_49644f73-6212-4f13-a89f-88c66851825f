// 引入 React Testing Library
import React from 'react'; // 导入 React
import { render, screen } from '@testing-library/react';
import { EmrEditor } from '../components/editor/Main';
import { WasmInstance } from '../common/WasmInstance';

// 设置 Jasmine 超时时间
beforeEach(() => {
    jasmine.DEFAULT_TIMEOUT_INTERVAL = 100000;
});

let editorVm: HTMLElement | null = null;
let runningVm: any;

export function editorInit(): Promise<void> {
    if (editorVm) {
        return Promise.resolve();
    }

    if (runningVm) {
        return runningVm;
    }

    return runningVm = new Promise<void>((resolve, reject) => {
        WasmInstance.createWasmInstsanceAsync().then(() => {
            // 使用 React Testing Library 渲染组件
            const { container } = render(
                <EmrEditor 
                    isTest={true} 
                    bShowMenu={false} 
                    bShowToolbar={false} 
                    textColorChangeInRevision={1} 
                />
            );

            // 将渲染结果存储在 window 对象上，便于其他测试访问
            editorVm = window['__EmrEditorComponent__'] = container;
            resolve();
        }).catch(reject);
    });
}


export function sleep(time: number): Promise<void> {
    return new Promise((resolve, reject) => {
        setTimeout(() => {
            resolve();
        }, time);
    });
}
