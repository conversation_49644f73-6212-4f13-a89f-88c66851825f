import * as React from 'react';
import Dialog from '../dialog/dialog';

interface IDialogProps {
    documentCore: any;
    visible: boolean;
    id?: string;
    close: (name: string, bReflash?: boolean) => void;
    children: React.ReactNode;
}

interface IState {
    bReflash: boolean;
}

export default class SplitCellDialog extends React.Component<IDialogProps, IState> {
    private table: {splitTableCols: number, splitTableRows: number};
    constructor(props: any) {
        super(props);
        this.state = {
            bReflash: false,
        };
        this.table = {
            splitTableCols: 2,
            splitTableRows: 2,
        };
    }

    public render(): any {
        return (
            <Dialog
                visible={this.props.visible}
                top='middle'
                width={300}
                height={200}
                close={this.close}
                open={this.open}
                preventDefault={true}
                title='拆分单元格'
                confirm={this.confirm}
                id='splitCell'
            >
                <div>
                    <div className='full-line'>
                        <label>列数：</label>
                        <input
                            type='number'
                            min={1}
                            step={1}
                            value={this.table.splitTableCols}
                            style={{width: 'calc(100% - 40px)'}}
                            onChange={this.numChange.bind(this, 'splitTableCols')}
                        />
                    </div>
                    <div className='full-line'>
                        <label>行数：</label>
                        <input
                            type='number'
                            min={1}
                            step={1}
                            value={this.table.splitTableRows}
                            style={{width: 'calc(100% - 40px)'}}
                            onChange={this.numChange.bind(this, 'splitTableRows')}
                        />
                    </div>
                </div>
            </Dialog>
        );
    }

    private numChange(name: string, e: any): void {
        this.table[name] = parseInt(e.target.value, 10) || 1;
        this.setState({bReflash: !this.state.bReflash});
    }

    private open = (): void => {
        const table = this.table;
        table.splitTableCols = 1;
        table.splitTableRows = 1;
        this.setState({bReflash: !this.state.bReflash});
    }

    private close = (id?: any): void => {
        this.props.close(id);
    }

    private confirm = (id?: any): void => {
        const documentCore = this.props.documentCore;
        const table = this.table;
        documentCore.splitTableCells(table.splitTableRows, table.splitTableCols);

        this.props.close(id, true);
    }
}
