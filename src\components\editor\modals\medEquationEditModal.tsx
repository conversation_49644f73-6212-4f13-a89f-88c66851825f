import * as React from 'react';
import { DocumentCore } from '../../../model/DocumentCore';
import ParaDrawing, { ParaEquation } from '../../../model/core/Paragraph/ParaDrawing';
import { ISvgWidthIncreasedProps } from '../../../model/MedEquationProperty';
import { EquationType } from '../../../common/commonDefines';
import { setEquationTextPos } from '../module/modals/equation/Base';

//该文件已经废弃

interface IOrdinaryEquationVals {
  firstVal: string;
  secondVal: string;
  thirdVal: string;
  fourthVal: string;
}

interface IFractionEquationVals {
  firstVal: string;
  secondVal: string;
}

interface IMenEquationVals {
  initialAge: string;
  duration: string;
  period: string;
  lastAge: string;
  unitEnabled: boolean;
  initialDateEnabled: boolean;
  lastDateEnabled: boolean;
}

interface IMedEquationEditModalProps {
  equationType: EquationType;
  documentCore: DocumentCore;
  handleModalState: (type: string) => void;
  refresh: () => void;
  showEquationEditModal?: boolean;
  equationRefs?: any;
}

interface IMedEquationEditModalState {
  ordinaryEquationVals: IOrdinaryEquationVals;
  fractionEquationVals: IFractionEquationVals;
  menEquationVals: IMenEquationVals;
  isEquationEditEnabled: boolean; // not in use yet
  svgWidthIncreasedProps?: ISvgWidthIncreasedProps;
}

export default class MedEquationEditModal extends React.Component<
  IMedEquationEditModalProps, IMedEquationEditModalState> {

  private revisedEquationContainerRef; // container to render svg for measure

  // equation form ref
  private ordinaryEquationForm;
  private fractionEquationForm;
  private menEquationForm;

  constructor(props) {
    super(props);    

    this.revisedEquationContainerRef = React.createRef();

    this.ordinaryEquationForm = React.createRef();
    this.fractionEquationForm = React.createRef();
    this.menEquationForm = React.createRef();

    this.state = {
      ordinaryEquationVals: {
        firstVal: "",
        secondVal: "",
        thirdVal: "",
        fourthVal: "",
      },
      fractionEquationVals: {
        firstVal: "",
        secondVal: "",
      },
      menEquationVals: {
        initialAge: "",
        duration: "",
        period: "",
        lastAge: "",
        unitEnabled: true,
        initialDateEnabled: false,
        lastDateEnabled: false,
      },
      isEquationEditEnabled: false,
      svgWidthIncreasedProps: {
        ordinaryEquation: {
          leftAmount: 0,
          rightAmount: 0,
        },
        fractionEquation: {
          amount: 0,
        },
        menEquation: {
          leftAmount: 0,
          middleAmount: 0,
          rightAmount: 0,
        },
        // unit: 80,
        fractionTextPadding: 110,
        ordinaryTextPadding: 35,
        menTextPadding: 20,
        menSVGPadding: 32,
      },
    };
  }

  UNSAFE_componentWillReceiveProps(nextProps) {

    // When equation edit modal is about to open
    if (this.props.showEquationEditModal === false && nextProps.showEquationEditModal === true) {

      const paraEquation = nextProps.documentCore.getSelectedImage() as ParaEquation;

      let equationElemDom = null;
      if (paraEquation.equationElem) {
        equationElemDom = new DOMParser().parseFromString(paraEquation.equationElem, 'text/xml').documentElement;
      }
      // console.log(equationElemDom)

      if (!equationElemDom) {
        const {equationRefs} = this.props;
        switch (paraEquation.equationType) {
          case EquationType.Fraction:
            equationElemDom = equationRefs.fractionEquationRef.current.cloneNode(true);
            break;

          case EquationType.Ordinary:
            equationElemDom = equationRefs.ordEquationRef.current.cloneNode(true);
            break;

          case EquationType.Menstruation:
            equationElemDom = equationRefs.menEquationRef.current.cloneNode(true);
            equationElemDom.setAttribute('width', 106);
            equationElemDom.setAttribute('height', 50);
            break;

          default:
            equationElemDom = equationRefs.fractionEquationRef.current.cloneNode(true);
            break;
        }

        // cast change to string and store in paraEquation
        const svgEquationElemStr = new XMLSerializer().serializeToString(equationElemDom);
        nextProps.documentCore
          //.getDrawingObjects()
          .applyDrawingSvgElemStr(svgEquationElemStr);
      }
      // console.log(paraEquation.equationElem)

      const texts = equationElemDom.querySelectorAll(".equation-text");

      let isEquationEditEnabled = false;

      switch (nextProps.equationType) {
        case EquationType.Fraction:
          const fractionEquationVals = 
            JSON.parse(JSON.stringify(this.state.fractionEquationVals)) as IFractionEquationVals;
          fractionEquationVals.firstVal = texts[0].innerHTML;
          fractionEquationVals.secondVal = texts[1].innerHTML;
          isEquationEditEnabled = this.checkEquationEditEnabled(fractionEquationVals);
          this.setState({fractionEquationVals, isEquationEditEnabled});
          break;

        case EquationType.Ordinary:
          const ordinaryEquationVals =
            JSON.parse(JSON.stringify(this.state.ordinaryEquationVals)) as IOrdinaryEquationVals;

          ordinaryEquationVals.firstVal = texts[0].innerHTML;
          ordinaryEquationVals.secondVal = texts[1].innerHTML;
          ordinaryEquationVals.thirdVal = texts[2].innerHTML;
          ordinaryEquationVals.fourthVal = texts[3].innerHTML;

          isEquationEditEnabled = this.checkEquationEditEnabled(ordinaryEquationVals);
          this.setState({ordinaryEquationVals, isEquationEditEnabled});
          break;
        case EquationType.Menstruation:
          const menEquationVals = 
            JSON.parse(JSON.stringify(this.state.menEquationVals)) as IMenEquationVals;
          const hiddenValueElem = equationElemDom.querySelector(".hidden-values");

          menEquationVals.initialAge = texts[0].innerHTML;
          menEquationVals.duration = texts[1].innerHTML;
          menEquationVals.period = texts[2].innerHTML;
          menEquationVals.lastAge = texts[3].innerHTML;
          menEquationVals.unitEnabled = hiddenValueElem.classList.contains("unitEnabled") ? true : false;
          menEquationVals.initialDateEnabled = hiddenValueElem.classList.contains("initialDateEnabled") ? true : false;
          menEquationVals.lastDateEnabled = hiddenValueElem.classList.contains("lastDateEnabled") ? true : false;

          // remove '岁'/'年' if unit checked
          if (menEquationVals.unitEnabled) {
            console.log("if you see this twice in a row, call out to Rowen");
            menEquationVals.initialAge = menEquationVals.initialDateEnabled ? menEquationVals.initialAge :
              menEquationVals.initialAge.slice(0, menEquationVals.initialAge.length - 1);
            menEquationVals.duration = menEquationVals.duration.slice(0, menEquationVals.duration.length - 1);
            menEquationVals.period = menEquationVals.period.slice(0, menEquationVals.period.length - 1);
            menEquationVals.lastAge = menEquationVals.lastDateEnabled ? menEquationVals.lastAge : 
              menEquationVals.lastAge.slice(0, menEquationVals.lastAge.length - 1);
          }

          isEquationEditEnabled = this.checkEquationEditEnabled(menEquationVals);
          // console.log(menEquationVals, isEquationEditEnabled)
          this.setState({menEquationVals, isEquationEditEnabled});
          break;

        default:
          break;
      }
    }

  }

  render() {
    
    return (
      <div className="config-window equation-window eqution-edit-window">
        <form 
          ref={this.ordinaryEquationForm} 
          onSubmit={this.preventSubmit} 
          style={{display: (this.props.equationType === EquationType.Ordinary) ? "block" : "none"}}
        >
          <div className="config-descriptor">
            普通公式编辑
          </div>
          <div className="detail-block equation-detail-block">
            <div className="detail-item left-item top-item">
              <div>No1 值</div>
              <input 
                id="ordinary-firstVal" 
                name="ordinary-firstVal" 
                value={this.state.ordinaryEquationVals.firstVal} 
                onChange={this.handleEquationEditModalChange} 
                maxLength={20}
              />
            </div>
            <div className="detail-item right-item top-item">
              <div>No2 值</div>
              <input 
                id="ordinary-secondVal" 
                name="ordinary-secondVal" 
                className="" 
                value={this.state.ordinaryEquationVals.secondVal} 
                onChange={this.handleEquationEditModalChange} 
                maxLength={20}
              />
            </div>
          </div>
          <div className="detail-block equation-detail-block">
            <div className="detail-item left-item bottom-item">
              <div>No3 值</div>
              <input 
                id="ordinary-thirdVal" 
                name="ordinary-thirdVal" 
                className="" 
                value={this.state.ordinaryEquationVals.thirdVal} 
                onChange={this.handleEquationEditModalChange} 
                maxLength={20}
              />
            </div>
            <div className="detail-item right-item bottom-item">
              <div>No4 值</div>
              <input 
                id="ordinary-fourthVal" 
                name="ordinary-fourthVal" 
                className="" 
                value={this.state.ordinaryEquationVals.fourthVal} 
                onChange={this.handleEquationEditModalChange} 
                maxLength={20}
              />
            </div>
          </div>
          <div className="button-container">
            <button className="button" type="reset" onClick={this.handleButtonClick}>取消</button>
            <button 
              className="button" 
              type="submit" 
              onClick={this.handleButtonClick} 
              disabled={this.state.isEquationEditEnabled ? false : true}
            >确定
            </button>
          </div>
        </form>

        <form 
          ref={this.fractionEquationForm} 
          onSubmit={this.preventSubmit} 
          style={{display: (this.props.equationType === EquationType.Fraction) ? "block" : "none"}}
        >
          <div className="config-descriptor">
            分式编辑
          </div>
          <div className="detail-block fraction-block">
            <div>No1 值</div>
            <input 
              id="fraction-firstVal" 
              name="fraction-firstVal" 
              className="" 
              value={this.state.fractionEquationVals.firstVal} 
              onChange={this.handleEquationEditModalChange} 
              maxLength={20} 
              required={true}
            />
          </div>
          <div className="detail-block fraction-block">
            <div>No2 值</div>
            <input 
              id="fraction-secondVal" 
              name="fraction-secondVal" 
              className="" 
              value={this.state.fractionEquationVals.secondVal} 
              onChange={this.handleEquationEditModalChange} 
              maxLength={20} 
              required={true}
            />
          </div>
          <div className="button-container">
            <button className="button" type="reset" onClick={this.handleButtonClick}>取消</button>
            <button 
              className="button" 
              type="submit" 
              onClick={this.handleButtonClick} 
              disabled={this.state.isEquationEditEnabled ? false : true}
            >确定
            </button>
          </div>
        </form>

        <form 
          ref={this.menEquationForm} 
          onSubmit={this.preventSubmit} 
          style={{display: (this.props.equationType === EquationType.Menstruation) ? "block" : "none"}}
        >
          <div className="config-descriptor">
            月经史表达式 编辑
          </div>
          <div className="menstruation-block">
            <input 
              id="menstruation-unit" 
              type="checkbox" 
              name="menstruation-unit" 
              className="menstruation-checkbox" 
              checked={this.state.menEquationVals.unitEnabled} 
              onChange={this.handleEquationEditModalChange}
            />单位
            <div>初潮年龄</div>
            {!this.state.menEquationVals.initialDateEnabled ? (
              <input 
                id="menstruation-initialAge" 
                name="menstruation-initialAge" 
                className="menstruation-text" 
                value={this.state.menEquationVals.initialAge} 
                onChange={this.handleEquationEditModalChange} 
                maxLength={20}
              />
            ) : (
              <input 
                type="date" 
                id="menstruation-initialAge" 
                name="menstruation-initialAge" 
                className="menstruation-date" 
                value={this.state.menEquationVals.initialAge} 
                onChange={this.handleEquationEditModalChange}
              />
            )}
            <input 
              id="menstruation-date-initial" 
              type="checkbox" 
              name="menstruation-date-initial" 
              className="menstruation-checkbox" 
              checked={this.state.menEquationVals.initialDateEnabled} 
              onChange={this.handleEquationEditModalChange}
            />使用日期
          </div>
          <div className="menstruation-block">
            <div className="menstruation-upper-block">
              <div>经期(天)</div>
              <input 
                id="menstruation-duration" 
                name="menstruation-duration" 
                className="menstruation-text" 
                value={this.state.menEquationVals.duration} 
                onChange={this.handleEquationEditModalChange} 
                maxLength={20}
              />
            </div>
            <div className="menstruation-bottom-block">
              <div>周期(天)</div>
              <input 
                id="menstruation-period" 
                name="menstruation-period" 
                className="menstruation-text" 
                value={this.state.menEquationVals.period} 
                onChange={this.handleEquationEditModalChange} 
                maxLength={20}
              />
            </div>
          </div>
          <div className="menstruation-block">
            <div>末次月经/<br />绝经年龄</div>
            {!this.state.menEquationVals.lastDateEnabled ? (
              <input 
                id="menstruation-lastAge" 
                name="menstruation-lastAge" 
                className="menstruation-text" 
                value={this.state.menEquationVals.lastAge} 
                onChange={this.handleEquationEditModalChange} 
                maxLength={20}
              />
            ) : (
              <input 
                type="date" 
                id="menstruation-lastAge" 
                name="menstruation-lastAge" 
                className="menstruation-date" 
                value={this.state.menEquationVals.lastAge} 
                onChange={this.handleEquationEditModalChange}
              />
            )}
            <input 
              id="menstruation-date-last" 
              type="checkbox" 
              name="menstruation-date-last" 
              className="menstruation-checkbox" 
              checked={this.state.menEquationVals.lastDateEnabled} 
              onChange={this.handleEquationEditModalChange}
            />使用日期
          </div>
          <div className="button-container">
            <button className="button" type="reset" onClick={this.handleButtonClick}>取消</button>
            <button 
              className="button" 
              type="submit" 
              onClick={this.handleButtonClick} 
              disabled={this.state.isEquationEditEnabled ? false : true}
            >确定
            </button>
          </div>
        </form>

        {/* container for revised equation to measure height/width */}
        <div ref={this.revisedEquationContainerRef} className="revised-equation-container" />

      </div>
    );
  }

  private handleEquationEditModalChange = (e) => {

    const {documentCore} = this.props;

    let isEquationEditEnabled = false;

    if (this.props.equationType === EquationType.Fraction) {

      const fractionEquationVals: IOrdinaryEquationVals = JSON.parse(JSON.stringify(this.state.fractionEquationVals));
      switch (e.target.id) {
        case "fraction-firstVal":
          fractionEquationVals.firstVal = e.target.value;
          break;
        
        case "fraction-secondVal":
          fractionEquationVals.secondVal = e.target.value;
          break;
  
        default:
          break;
      }
  
      isEquationEditEnabled = this.checkEquationEditEnabled(fractionEquationVals);
      this.setState({fractionEquationVals, isEquationEditEnabled});

    } else if (this.props.equationType === EquationType.Ordinary) {

      const ordinaryEquationVals: IOrdinaryEquationVals = JSON.parse(JSON.stringify(this.state.ordinaryEquationVals));
  
      switch (e.target.id) {
        case "ordinary-firstVal":
          ordinaryEquationVals.firstVal = e.target.value;
          break;
        
        case "ordinary-secondVal":
          ordinaryEquationVals.secondVal = e.target.value;
          break;
        
        case "ordinary-thirdVal":
          ordinaryEquationVals.thirdVal = e.target.value;
          break;
  
        case "ordinary-fourthVal":
          ordinaryEquationVals.fourthVal = e.target.value;
          break;
  
        default:
          break;
      }
  
      isEquationEditEnabled = this.checkEquationEditEnabled(ordinaryEquationVals);
      this.setState({ordinaryEquationVals, isEquationEditEnabled});

    } else if (this.props.equationType === EquationType.Menstruation) {
      const menEquationVals: IMenEquationVals = JSON.parse(JSON.stringify(this.state.menEquationVals));

      const paraEquation = this.props.documentCore.getSelectedImage() as ParaEquation;
      const equationElemDom: any = new DOMParser().parseFromString(paraEquation.equationElem, "text/xml").documentElement;
      const hiddenValueElem = equationElemDom.querySelector(".hidden-values");

      switch (e.target.id) {
        case "menstruation-initialAge":
          menEquationVals.initialAge = e.target.value;
          break;
        
        case "menstruation-duration":
          menEquationVals.duration = e.target.value;
          break;
        
        case "menstruation-period":
          menEquationVals.period = e.target.value;
          break;
  
        case "menstruation-lastAge":
          menEquationVals.lastAge = e.target.value;
          break;
        
        case "menstruation-unit":
          menEquationVals.unitEnabled = !menEquationVals.unitEnabled;
          if (menEquationVals.unitEnabled) {
            hiddenValueElem.classList.add("unitEnabled");
          } else {
            hiddenValueElem.classList.remove("unitEnabled");
          }
          
          // console.log(hiddenValueElem);
          break;
        
        case "menstruation-date-initial":
          menEquationVals.initialDateEnabled = !menEquationVals.initialDateEnabled;

          // remove string when changing from text to date and vice versa
          if (menEquationVals.initialDateEnabled) {
            menEquationVals.initialAge = "";
            hiddenValueElem.classList.add("initialDateEnabled");
          } else {
            menEquationVals.initialAge = "";
            hiddenValueElem.classList.remove("initialDateEnabled");
          }
          break;

        case "menstruation-date-last":
          menEquationVals.lastDateEnabled = !menEquationVals.lastDateEnabled;

          // remove string when changing from text to date and vice versa
          if (menEquationVals.lastDateEnabled) {
            menEquationVals.lastAge = "";
            hiddenValueElem.classList.add("lastDateEnabled");
          } else {
            menEquationVals.lastAge = "";
            hiddenValueElem.classList.remove("lastDateEnabled");
          }
          break;

        default:
          break;
      }
      // cast change to string and store in paraEquation
      const svgEquationElemStr = new XMLSerializer().serializeToString(equationElemDom);
      documentCore
        //.getDrawingObjects()
        .applyDrawingSvgElemStr(svgEquationElemStr);

      isEquationEditEnabled = this.checkEquationEditEnabled(menEquationVals);
      this.setState({menEquationVals, isEquationEditEnabled});
    }

  }

  private handleButtonClick = (e) => {

    if (e.target.innerHTML === "确定") {
      // get current form from focused image's edit modal
      let currentForm = null;
      switch (this.props.equationType) {
        case EquationType.Fraction:
          currentForm = this.fractionEquationForm.current;
          break;
        case EquationType.Ordinary:
          currentForm = this.ordinaryEquationForm.current;
          break;

        case EquationType.Menstruation:
          currentForm = this.menEquationForm.current;
          break;

        default:
          currentForm = this.fractionEquationForm.current;
          break;
      }

      if (currentForm.reportValidity()) {

        const {documentCore} = this.props;

        // close modal
        this.props.handleModalState("equation");

        const document = documentCore.getDocument();
        let svgEquationElemStr = (documentCore.getSelectedImage() as ParaEquation).equationElem;
        const svgEquationElem: any = new DOMParser().parseFromString(svgEquationElemStr, "text/xml").documentElement;
        const texts = svgEquationElem.querySelectorAll(".equation-text");
        // console.log(svgEquationElem, texts);

        let bChange = false;
        // update svg render
        if (this.props.equationType === EquationType.Ordinary) {
          const {firstVal, secondVal, thirdVal, fourthVal} = this.state.ordinaryEquationVals;

          // apply to svg template
          if ( firstVal !== texts[0].innerHTML || secondVal !== texts[1].innerHTML
              || thirdVal !== texts[2].innerHTML || firstVal !== texts[3].innerHTML) {
            bChange = true;
            texts[0].innerHTML = firstVal;
            texts[1].innerHTML = secondVal;
            texts[2].innerHTML = thirdVal;
            texts[3].innerHTML = fourthVal;
          }

        } else if (this.props.equationType === EquationType.Fraction) {
          const {firstVal, secondVal} = this.state.fractionEquationVals;
          if ( firstVal !== texts[0].innerHTML || secondVal !== texts[1].innerHTML ) {
            bChange = true;
            texts[0].innerHTML = firstVal;
            texts[1].innerHTML = secondVal;
          }
        } else if (this.props.equationType === EquationType.Menstruation ) {
          const {initialAge, duration, period, lastAge, unitEnabled, initialDateEnabled, lastDateEnabled}
            = this.state.menEquationVals;

          const newInitialAge = unitEnabled ? (!initialDateEnabled ? initialAge + "岁" : initialAge) : initialAge;
          const newDuration = unitEnabled ? duration + "天" : duration;
          const newPeriod = unitEnabled ? period + "天" : period;
          const newLastAge = unitEnabled ? (!lastDateEnabled ? lastAge + "岁" : lastAge) : lastAge;

          if ( newInitialAge !== texts[0].innerHTML || newDuration !== texts[1].innerHTML
            || newPeriod !== texts[2].innerHTML || newLastAge !== texts[3].innerHTML) {
            bChange = true;
            // apply to svg template
            texts[0].innerHTML = newInitialAge;
            texts[1].innerHTML = newDuration;
            texts[2].innerHTML = newPeriod;
            texts[3].innerHTML = newLastAge;
          }
        }

        if ( false === bChange ) {
          this.props.handleModalState("equation");
          return ;
        }

        console.time("convert and change href");

        // Calculate and set width of diff text parts in a realtime equation
        const svgWidthIncreasedProps = setEquationTextPos(svgEquationElem, this.revisedEquationContainerRef,
                                  this.state.svgWidthIncreasedProps, this.props.equationType, documentCore);

        this.setState({svgWidthIncreasedProps}, () => {

          const paraDrawing = documentCore.getSelectedImage() as ParaDrawing;
          const widthRatio = paraDrawing.width / (svgEquationElem.getAttribute("width") * 1);
          const textContainers = svgEquationElem.querySelectorAll("text");
          const lines = svgEquationElem.querySelectorAll("line");

          let newWidth = 50;
          const newHeight = paraDrawing.height;

          // Spread out all types of equation-related info first
          const {ordinaryEquation, fractionEquation, menEquation, fractionTextPadding, ordinaryTextPadding, 
                 menTextPadding, menSVGPadding} = this.state.svgWidthIncreasedProps;
          
          if (this.props.equationType === EquationType.Ordinary) {

            newWidth = (ordinaryTextPadding + ordinaryEquation.leftAmount + ordinaryTextPadding * 2 
                        + ordinaryEquation.rightAmount + ordinaryTextPadding) / 6;

          } else if (this.props.equationType === EquationType.Fraction) {

            newWidth = (fractionTextPadding + fractionEquation.amount + fractionTextPadding) / 6;

          } else if (this.props.equationType === EquationType.Menstruation) {

            newWidth = (menSVGPadding + menEquation.leftAmount + menTextPadding + menEquation.middleAmount 
                        + menTextPadding + menTextPadding + menEquation.rightAmount + menSVGPadding) / 6;
          
          }
          
          // get maxWidth
          const maxWidth = documentCore.getMaxWidth(true);

          if (newWidth > maxWidth) {
            newWidth = maxWidth;
            // TODO: shrink font size?
          }
          if (newWidth * widthRatio > maxWidth) {
            newWidth = maxWidth / widthRatio;
            // TODO: shrink font size?
          }

          const newViewBoxWidth = newWidth * 6;
          const viewBox = "0 0 " + newViewBoxWidth + " 300";

          // console.log(newWidth, newViewBoxWidth)
          // general svg stuff change first
          svgEquationElem.setAttribute("viewBox", viewBox);
          svgEquationElem.setAttribute("width", newWidth);

          if (this.props.equationType === EquationType.Fraction) {
            lines[0].setAttribute("x2", newViewBoxWidth);
            textContainers[0].setAttribute("x", newViewBoxWidth / 2);
            textContainers[1].setAttribute("x", newViewBoxWidth / 2);

          } else if (this.props.equationType === EquationType.Ordinary) {

            const middlePos = ordinaryTextPadding + ordinaryEquation.leftAmount + ordinaryTextPadding;

            textContainers[0].setAttribute("x", ordinaryTextPadding + ordinaryEquation.leftAmount / 2);
            textContainers[2].setAttribute("x", ordinaryTextPadding + ordinaryEquation.leftAmount / 2);
            textContainers[1].setAttribute("x", middlePos + ordinaryTextPadding + ordinaryEquation.rightAmount / 2);
            textContainers[3].setAttribute("x", middlePos + ordinaryTextPadding + ordinaryEquation.rightAmount / 2);
            
            lines[0].setAttribute("x2", newViewBoxWidth);
            lines[1].setAttribute("x1", middlePos);
            lines[1].setAttribute("x2", middlePos);
          
          } else if (this.props.equationType === EquationType.Menstruation) {
            const lineStart = menSVGPadding + menEquation.leftAmount + menTextPadding;
            const lineEnd = lineStart + menEquation.middleAmount + menTextPadding;

            lines[0].setAttribute("x1", lineStart);
            lines[0].setAttribute("x2", lineEnd);

            textContainers[0].setAttribute("x", lineStart - menTextPadding);
            textContainers[1].setAttribute("x", (lineStart + lineEnd) / 2);
            textContainers[2].setAttribute("x", (lineStart + lineEnd) / 2);
            textContainers[3].setAttribute("x", lineEnd + menTextPadding);
          }
          
          // Convert svg template to string and apply to paraEquation
          const svgConvertedURI = documentCore.getDrawingObjects().convertSVGToImageString(svgEquationElem);
          console.log(svgConvertedURI);

          if (widthRatio !== 1) {
            newWidth *= widthRatio;
          }
          documentCore
            //.getDrawingObjects()
            .applyDrawingHref(svgConvertedURI, newWidth, newHeight);

          // cast change to string and store in paraEquation
          svgEquationElemStr = new XMLSerializer().serializeToString(svgEquationElem);
          // console.log(svgEquationElemStr)
          documentCore
            //.getDrawingObjects()
            .applyDrawingSvgElemStr(svgEquationElemStr);

          console.timeEnd("convert and change href");

          // trigger rerender
          this.props.refresh();

        });
      }
    } else { // 取消
      // close modal
      this.props.handleModalState("equation");
    }
    
  }

  private checkEquationEditEnabled(EquationVals: IOrdinaryEquationVals | IFractionEquationVals | IMenEquationVals) {

    const isEquationEditEnabled = true;

    switch (this.props.equationType) {
      case EquationType.Fraction:
        break;
      case EquationType.Ordinary:
        break;
      case EquationType.Menstruation:
        break;

      default:
        break;
    }

    return isEquationEditEnabled;
  }

  private preventSubmit = (e) => {
    e.preventDefault();
  }
}
