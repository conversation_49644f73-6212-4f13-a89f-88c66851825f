import { DocumentCore } from '../../model/DocumentCore';
import { EmrEditor } from '../../components/editor/Main';
import { ResultType } from '../commonDefines';
import { AISuggestionPopup } from '../AI/AI_Suggestion/AISuggestionPopup';
import { AITextGenerationParams, AITextGenerationResponse } from '../AI/AI_Suggestion/AITextGenerationTypes';
import * as React from 'react';
import * as ReactDOMClient from 'react-dom/client';
import ReactDOM from 'react-dom'; 

/**
 * AI 建议操作类
 * 用于处理 AI 建议相关的操作
 */
export default class OperateAISuggestion {
    private _documentCore: DocumentCore;
    private _host: EmrEditor;
    private _aiSuggestionPopup: AISuggestionPopup | null = null;
    private _popupContainer: HTMLDivElement | null = null;
    private _root: ReactDOMClient.Root | null = null;
    
    // API配置
    private apiConfig = {
        baseUrl: 'http://localhost:7075',
        endpoints: {
            generateText: '/api/generate_text'
        },
        timeout: 30000 // 30秒超时
    };
    
    // 请求状态
    private requestStatus: {
        isLoading: boolean;
        error: Error | null;
        controller: AbortController | null;
    } = {
        isLoading: false,
        error: null,
        controller: null
    };

    constructor(host: EmrEditor) {
        this._host = host;
        this._documentCore = host.state.documentCore;
        
        // 为 EmrEditor 添加 AISuggestion 属性，并将当前实例赋值给它
        (this._host as any).AISuggestion = this;
    }
    
    /**
     * 更新API配置
     * @param config 新的配置
     */
    public updateApiConfig(config: Partial<typeof this.apiConfig>): void {
        this.apiConfig = { ...this.apiConfig, ...config };
    }
    
    /**
     * 获取当前请求状态
     * @returns 当前请求状态
     */
    public getRequestStatus() {
        return { ...this.requestStatus };
    }
    
    /**
     * 取消当前请求
     */
    public cancelCurrentRequest(): void {
        if (this.requestStatus.controller) {
            this.requestStatus.controller.abort();
            this.requestStatus.controller = null;
            this.requestStatus.isLoading = false;
        }
    }
    
    /**
     * 获取患者信息
     * 目前返回模拟数据，后期会修改为实际数据
     * @returns 患者信息
     */
    public getPatientInfo(): AITextGenerationParams['patientInfo'] {
        // 返回模拟数据
        return {
            age: 30,
            gender: "男",
            chief_complaint: "发热三天"
        };
    }
    
    /**
     * 获取实验室数据
     * 目前返回模拟数据，后期会修改为实际数据
     * @returns 实验室数据
     */
    public getLabData(): AITextGenerationParams['labData'] {
        // 返回模拟数据
        return {
            "血常规": "正常",
            "血糖": "5.6 mmol/L"
        };
    }
    
    /**
     * 获取历史文本
     * 目前使用模拟数据，后期会改为从实际文档中获取
     * @param region 当前区域
     * @returns 历史文本
     */
    public getHistoryText(region: any): string {
        // 这里暂时返回模拟数据
        // 后期会根据实际情况从region中获取文本
        return "患者，男，30岁，因发热三天入院。体温最高38.5℃，伴有咳嗽、咳痰，无胸痛，无呼吸困难。";
    }

    /**
     * 请求AI建议
     * @param params 文本生成参数
     * @param signal AbortSignal用于取消请求
     * @returns 返回一个Promise，解析为生成的文本建议数组
     */
    public async requestAISuggestion(
        params: AITextGenerationParams, 
        signal?: AbortSignal
    ): Promise<string[]> {
        try {
            // 更新请求状态
            this.requestStatus.isLoading = true;
            this.requestStatus.error = null;
            
            // 如果没有传入AbortSignal，创建一个新的AbortController
            if (!signal) {
                this.requestStatus.controller = new AbortController();
                signal = this.requestStatus.controller.signal;
            }
            
            // 构建请求体，与后端接口格式保持一致
            const requestBody = {
                patient_info: params.patientInfo || this.getPatientInfo(),
                lab_data: params.labData || this.getLabData(),
                history_existing: params.historyExisting,
                need_generate_length: params.needGenerateLength
            };
            
            // 使用配置的API地址
            const url = `${this.apiConfig.baseUrl}${this.apiConfig.endpoints.generateText}`;
            
            // 添加超时处理，但不创建新的AbortController，避免信号冲突
            const timeoutId = setTimeout(() => {
                // 如果请求还未完成，则中止请求
                if (!signal?.aborted) {
                    console.log('请求超时，自动中止');
                    this.requestStatus.controller?.abort();
                }
            }, this.apiConfig.timeout);
            
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestBody),
                signal: signal // 直接使用传入的signal，不尝试合并
            });
            
            // 请求完成，清除超时计时器
            clearTimeout(timeoutId);
            
            if (!response.ok) {
                throw new Error(`HTTP错误: ${response.status}`);
            }
            
            const data: AITextGenerationResponse = await response.json();
            
            // 更新请求状态
            this.requestStatus.isLoading = false;
            
            if (data.status === "success") {
                return data.data.choices;
            } else {
                throw new Error(data.message || '获取文本生成结果失败');
            }
        } catch (error) {
            // 更新请求状态
            this.requestStatus.isLoading = false;
            this.requestStatus.error = error;
            
            // 如果是取消请求导致的错误，不需要抛出
            if (error.name === 'AbortError') {
                console.log('AI建议请求已取消');
                return [];
            }
            console.error('调用文本生成API失败:', error);
            throw error;
        }
    }
    
    /**
     * 请求AI建议（带重试）
     * @param params 文本生成参数
     * @param signal AbortSignal用于取消请求
     * @param retryCount 重试次数，默认为3
     * @returns 返回一个Promise，解析为生成的文本建议数组
     */
    public async requestAISuggestionWithRetry(
        params: AITextGenerationParams, 
        signal?: AbortSignal,
        retryCount: number = 3
    ): Promise<string[]> {
        let lastError: Error | null = null;
        
        for (let i = 0; i < retryCount; i++) {
            try {
                return await this.requestAISuggestion(params, signal);
            } catch (error) {
                // 如果是取消请求，直接返回空数组
                if (error.name === 'AbortError') {
                    return [];
                }
                
                // 记录最后一次错误
                lastError = error;
                
                // 如果是网络错误或服务器错误，等待后重试
                if (error.message.includes('HTTP错误') || error.message.includes('网络错误')) {
                    // 指数退避算法：等待时间随重试次数增加
                    const waitTime = Math.pow(2, i) * 1000;
                    console.log(`请求失败，${waitTime}ms后重试(${i + 1}/${retryCount})...`);
                    await new Promise(resolve => setTimeout(resolve, waitTime));
                } else {
                    // 如果是其他错误（如参数错误），不再重试
                    break;
                }
            }
        }
        
        // 所有重试都失败
        throw lastError || new Error('请求失败，已达到最大重试次数');
    }

     /**
     * 在光标位置显示 AI 建议弹窗
     * @param suggestions 建议列表
     * @returns 是否成功显示弹窗
     */
     public showAISuggestionAtCursor_interface(suggestions: string[]): boolean {
        try {
            if (!suggestions || suggestions.length === 0) {
                console.error('建议列表为空');
                return false;
            }

            const documentCore = this._documentCore;
            if (!documentCore) {
                console.error('无法获取文档核心实例');
                return false;
            }

            // 获取 EmrEditor 的主 DOM 元素 (myRef.current)
            const editorMainDOMElement = (this._host as any).myRef?.current;
            if (!editorMainDOMElement) {
                console.error('无法获取编辑器主 DOM 元素 (EmrEditor.myRef.current)');
                return false;
            }

            // 在编辑器主 DOM 中找到 .ReactVirtualized__Grid 元素, 作为 editorViewElement
            const editorViewElement = editorMainDOMElement.querySelector('.ReactVirtualized__Grid') as HTMLElement | null;
            if (!editorViewElement) {
                console.error('无法在编辑器主 DOM 中找到 .ReactVirtualized__Grid 元素。');
                return false;
            }

            // 直接获取光标元素 光标是一个 SVG line 元素，具有 id="cursor" 属性
            let cursorElement = null;
            
            // 从 Document 组件获取光标元素
            if ((this._host as any).getDocumentComponent) {
                const documentComponent = (this._host as any).getDocumentComponent();
                if (documentComponent && documentComponent.getCursor) {
                    cursorElement = documentComponent.getCursor();
                }
            }
            
            // 如果上面的方法失败，尝试直接从 DOM 中查找光标元素
            if (!cursorElement) {
                cursorElement = editorMainDOMElement.querySelector('#cursor');
            }
            
            let finalPopupX = 0;
            let finalPopupY = 0;
            
            if (cursorElement) {
                // 如果找到光标元素，直接使用它的位置
                const cursorRect = cursorElement.getBoundingClientRect();
                
                // 光标是一个线段，我们使用它的底部作为弹窗的顶部位置
                finalPopupX = cursorRect.right + 12; // 在光标右侧偏移 12px
                finalPopupY = cursorRect.bottom + 5; // 在光标底部偏移 5px
            } else {
                // 如果找不到光标元素，回退到使用内部坐标系统
                // 获取编辑器的缩放因子
                const scale = (this._host as any).getScale ? (this._host as any).getScale() : 1;
                
                // 获取光标在文档中的相对位置
                const cursorPosition = documentCore.getCursorPosition();
                if (!cursorPosition) {
                    console.error('无法获取光标位置');
                    return false;
                }
                
                // 获取 editorViewElement 在视口中的位置和其内部滚动状态
                const editorViewRect = editorViewElement.getBoundingClientRect();
                const scrollLeft = editorViewElement.scrollLeft;
                const scrollTop = editorViewElement.scrollTop;
                
                // 根据 NewCombox.tsx 中的位置计算逻辑计算光标位置
                const { getPagePadding } = require('../commonMethods');
                const { ViewModeType } = require('../commonDefines');
                
                // 计算视图模式偏移
                let subHeight = 0;
                let subLeft = 0;
                const viewMode = documentCore.getViewMode();
                if (viewMode === ViewModeType.WebView) {
                    subHeight -= getPagePadding(documentCore).top;
                } else if (viewMode === ViewModeType.CompactView) {
                    subLeft = getPagePadding(documentCore).left + getPagePadding.PaddingLeft;
                }
                
                // 将光标的文档相对坐标转换为视口坐标
                const cursorViewportX = editorViewRect.left + (cursorPosition.x - subLeft) * scale - scrollLeft;
                const cursorViewportY = editorViewRect.top + (cursorPosition.y2 + subHeight) * scale - scrollTop;
                
                // 应用偏移量
                finalPopupX = cursorViewportX + 12;
                finalPopupY = cursorViewportY + 16;
            }
            
            // 创建弹窗容器（如果尚未创建）或者显示已存在的容器
            if (!this._popupContainer) {
                this._popupContainer = document.createElement('div');
                this._popupContainer.className = 'ai-suggestion-popup-container';
                document.body.appendChild(this._popupContainer);
            } else {
                // 如果容器已存在，确保它是可见的
                this._popupContainer.style.display = 'block';
                console.log('显示已存在的AI建议弹窗容器');
            }

            // 使用 React 18 的 createRoot API 渲染组件
            if (!this._root) {
                console.log('创建新的React根元素');
                this._root = ReactDOMClient.createRoot(this._popupContainer);
            } else {
                console.log('使用现有的React根元素');
            }
            
            // 估计弹窗尺寸（假设最大宽度和高度）
            const estimatedPopupWidth = 300; // 估计的弹窗宽度
            const estimatedPopupHeight = 200; // 估计的弹窗高度
            
            // 检查弹窗是否会超出视口右边界
            let adjustedX = finalPopupX;
            const viewportWidth = window.innerWidth;
            if (finalPopupX + estimatedPopupWidth > viewportWidth) {
                adjustedX = viewportWidth - estimatedPopupWidth - 10; // 10px 边距
            }
            
            // 检查弹窗是否会超出视口底部边界
            let adjustedY = finalPopupY;
            const viewportHeight = window.innerHeight;
            if (finalPopupY + estimatedPopupHeight > viewportHeight) {
                // 如果超出底部，则将弹窗显示在光标上方
                adjustedY = finalPopupY - estimatedPopupHeight - 20; // 20px 边距，确保不遮挡光标
            }
            
            // 确保坐标不为负
            adjustedX = Math.max(10, adjustedX);
            adjustedY = Math.max(10, adjustedY);

            // 渲染 AI 建议弹窗组件
            console.log('重新渲染AI建议弹窗组件，强制可见性为true');
            
            // 强制清除并重新创建根元素
            if (this._root) {
                this._root.unmount();
            }
            this._root = ReactDOMClient.createRoot(this._popupContainer);
            
            this._root.render(
                React.createElement(AISuggestionPopup, {
                    editorContainer: editorViewElement,  // 使用编辑器内容显示元素作为容器参考
                    initialSuggestions: suggestions,
                    initialPosition: { x: adjustedX, y: adjustedY },
                    containerWidth: editorViewElement.clientWidth || 800,  // 使用其视口内宽度
                    onAccept: (item: string) => {
                        // 在光标位置插入选中的文本
                        const editor = this._host.getEditor();
                        editor.insertTextAtCurrentCursor(item);
                        this._host.handleRefresh();
                        // 隐藏弹窗
                        this.hideAISuggestion();
                    },
                    onCancel: () => {
                        // 取消操作，不做任何处理
                        this.hideAISuggestion();
                    }
                })
            );
            
            // 添加窗口大小变化监听，以便在窗口大小变化时调整弹窗位置
            const resizeHandler = () => {
                if (this._root && this._popupContainer) {
                    this.hideAISuggestion();
                }
            };
            window.addEventListener('resize', resizeHandler);
            
            // 在组件卸载时移除事件监听
            const originalHideMethod = this.hideAISuggestion.bind(this);
            this.hideAISuggestion = () => {
                window.removeEventListener('resize', resizeHandler);
                originalHideMethod();
                // 恢复原始方法
                this.hideAISuggestion = originalHideMethod;
            };

            return true;
        } catch (error) {
            console.error('显示 AI 建议弹窗失败:', error);
            return false;
        }
    }
    
    /**
     * 在光标位置显示 AI 建议弹窗
     * @param params 文本生成参数
     * @returns 返回一个Promise，解析为是否成功显示弹窗
     */
    public async showAISuggestionAtCursor(params: AITextGenerationParams | string[]): Promise<boolean> {
        try {
            // 判断参数类型
            const isTextGenerationParams = !Array.isArray(params) && typeof params === 'object';
            
            // 如果是旧的字符串数组参数，则检查是否为空
            if (!isTextGenerationParams && (!params || (params as string[]).length === 0)) {
                console.error('建议列表为空');
                return false;
            }
            
            // 如果是文本生成参数，检查必填字段
            if (isTextGenerationParams) {
                const textGenParams = params as AITextGenerationParams;
                if (!textGenParams.historyExisting || !textGenParams.needGenerateLength) {
                    console.error('缺少必要的文本生成参数');
                    return false;
                }
            }

            const documentCore = this._documentCore;
            if (!documentCore) {
                console.error('无法获取文档核心实例');
                return false;
            }

            // 获取 EmrEditor 的主 DOM 元素 (myRef.current)
            const editorMainDOMElement = (this._host as any).myRef?.current;
            if (!editorMainDOMElement) {
                console.error('无法获取编辑器主 DOM 元素 (EmrEditor.myRef.current)');
                return false;
            }

            // 在编辑器主 DOM 中找到 .ReactVirtualized__Grid 元素, 作为 editorViewElement
            const editorViewElement = editorMainDOMElement.querySelector('.ReactVirtualized__Grid') as HTMLElement | null;
            if (!editorViewElement) {
                console.error('无法在编辑器主 DOM 中找到 .ReactVirtualized__Grid 元素。');
                return false;
            }

            // 直接获取光标元素 光标是一个 SVG line 元素，具有 id="cursor" 属性
            let cursorElement = null;
            
            // 从 Document 组件获取光标元素
            if ((this._host as any).getDocumentComponent) {
                const documentComponent = (this._host as any).getDocumentComponent();
                if (documentComponent && documentComponent.getCursor) {
                    cursorElement = documentComponent.getCursor();
                }
            }
            
            // 如果上面的方法失败，尝试直接从 DOM 中查找光标元素
            if (!cursorElement) {
                cursorElement = editorMainDOMElement.querySelector('#cursor');
            }
            
            let finalPopupX = 0;
            let finalPopupY = 0;
            
            if (cursorElement) {
                // 如果找到光标元素，直接使用它的位置
                const cursorRect = cursorElement.getBoundingClientRect();
                
                // 光标是一个线段，我们使用它的底部作为弹窗的顶部位置
                finalPopupX = cursorRect.right + 12; // 在光标右侧偏移 12px
                finalPopupY = cursorRect.bottom + 5; // 在光标底部偏移 5px
            } else {
                // 如果找不到光标元素，回退到使用内部坐标系统
                // 获取编辑器的缩放因子
                const scale = (this._host as any).getScale ? (this._host as any).getScale() : 1;
                
                // 获取光标在文档中的相对位置
                const cursorPosition = documentCore.getCursorPosition();
                if (!cursorPosition) {
                    console.error('无法获取光标位置');
                    return false;
                }
                
                // 获取 editorViewElement 在视口中的位置和其内部滚动状态
                const editorViewRect = editorViewElement.getBoundingClientRect();
                const scrollLeft = editorViewElement.scrollLeft;
                const scrollTop = editorViewElement.scrollTop;
                
                // 根据 NewCombox.tsx 中的位置计算逻辑计算光标位置
                const { getPagePadding } = require('../commonMethods');
                const { ViewModeType } = require('../commonDefines');
                
                // 计算视图模式偏移
                let subHeight = 0;
                let subLeft = 0;
                const viewMode = documentCore.getViewMode();
                if (viewMode === ViewModeType.WebView) {
                    subHeight -= getPagePadding(documentCore).top;
                } else if (viewMode === ViewModeType.CompactView) {
                    subLeft = getPagePadding(documentCore).left + getPagePadding.PaddingLeft;
                }
                
                // 将光标的文档相对坐标转换为视口坐标
                const cursorViewportX = editorViewRect.left + (cursorPosition.x - subLeft) * scale - scrollLeft;
                const cursorViewportY = editorViewRect.top + (cursorPosition.y2 + subHeight) * scale - scrollTop;
                
                // 应用偏移量
                finalPopupX = cursorViewportX + 12;
                finalPopupY = cursorViewportY + 16;
            }
            
            // 创建弹窗容器（如果尚未创建）或者显示已存在的容器
            if (!this._popupContainer) {
                this._popupContainer = document.createElement('div');
                this._popupContainer.className = 'ai-suggestion-popup-container';
                document.body.appendChild(this._popupContainer);
            } else {
                // 如果容器已存在，确保它是可见的
                this._popupContainer.style.display = 'block';
                console.log('显示已存在的AI建议弹窗容器');
            }

            // 使用 React 18 的 createRoot API 渲染组件
            if (!this._root) {
                console.log('创建新的React根元素');
                this._root = ReactDOMClient.createRoot(this._popupContainer);
            } else {
                console.log('使用现有的React根元素');
            }
            
            // 估计弹窗尺寸（假设最大宽度和高度）
            const estimatedPopupWidth = 300; // 估计的弹窗宽度
            const estimatedPopupHeight = 200; // 估计的弹窗高度
            
            // // 检查弹窗是否会超出视口右边界
            // let adjustedX = finalPopupX;
            // const viewportWidth = window.innerWidth;
            // if (finalPopupX + estimatedPopupWidth > viewportWidth) {
            //     adjustedX = viewportWidth - estimatedPopupWidth - 10; // 10px 边距
            // }
            
            // // 检查弹窗是否会超出视口底部边界
            // let adjustedY = finalPopupY;
            // const viewportHeight = window.innerHeight;
            // if (finalPopupY + estimatedPopupHeight > viewportHeight) {
            //     // 如果超出底部，则将弹窗显示在光标上方
            //     adjustedY = finalPopupY - estimatedPopupHeight - 20; // 20px 边距，确保不遮挡光标
            // }
            // 直接使用光标位置，不考虑边界检查
            let adjustedX = finalPopupX;
            let adjustedY = finalPopupY;
            
            // 确保坐标不为负（保留最小安全边距）
            adjustedX = Math.max(5, adjustedX);
            adjustedY = Math.max(5, adjustedY);

            // 渲染 AI 建议弹窗组件
            console.log('重新渲染AI建议弹窗组件，强制可见性为true');
            
            // 强制清除并重新创建根元素
            if (this._root) {
                this._root.unmount();
            }
            this._root = ReactDOMClient.createRoot(this._popupContainer);
            
            // 准备组件属性
            const componentProps: any = {
                editorContainer: editorViewElement,  // 使用编辑器内容显示元素作为容器参考
                initialPosition: { x: adjustedX, y: adjustedY },
                containerWidth: editorViewElement.clientWidth || 800,  // 使用其视口内宽度
                onAccept: (item: string) => {
                    // 在光标位置插入选中的文本
                    const editor = this._host.getEditor();
                    editor.insertTextAtCurrentCursor(item);
                    this._host.handleRefresh();
                    // 隐藏弹窗
                    this.hideAISuggestion();
                },
                onCancel: () => {
                    // 取消操作，不做任何处理
                    this.hideAISuggestion();
                }
            };
            
            // 根据参数类型设置不同的属性
            if (isTextGenerationParams) {
                // 如果是文本生成参数，传递给组件
                componentProps.textGenerationParams = params as AITextGenerationParams;
            } else {
                // 如果是字符串数组，直接作为建议传递
                componentProps.initialSuggestions = params as string[];
            }
            
            this._root.render(
                React.createElement(AISuggestionPopup, componentProps)
            );
            
            // 添加窗口大小变化监听，以便在窗口大小变化时调整弹窗位置
            const resizeHandler = () => {
                if (this._root && this._popupContainer) {
                    this.hideAISuggestion();
                }
            };
            window.addEventListener('resize', resizeHandler);
            
            // 在组件卸载时移除事件监听
            const originalHideMethod = this.hideAISuggestion.bind(this);
            this.hideAISuggestion = () => {
                window.removeEventListener('resize', resizeHandler);
                originalHideMethod();
                // 恢复原始方法
                this.hideAISuggestion = originalHideMethod;
            };

            // 创建一个Promise，在弹窗显示完成后解析
            return new Promise<boolean>((resolve) => {
                // 渲染完成，返回成功
                resolve(true);
            });
        } catch (error) {
            console.error('显示AI建议失败:', error);
            return Promise.resolve(false);
        }
    }

    /**
     * 隐藏AI 建议弹窗
     * 只隐藏弹窗而不销毁组件
     */
    public hideAISuggestion(): void {
        console.log('隐藏AI建议弹窗 - 只隐藏不销毁');
        
        // 保留React根元素和组件，不进行卸载
        // 只隐藏容器，这样可以保留组件状态
        if (this._popupContainer) {
            this._popupContainer.style.display = 'none';
            console.log('容器已隐藏，但组件未销毁');
        }
    }
}
