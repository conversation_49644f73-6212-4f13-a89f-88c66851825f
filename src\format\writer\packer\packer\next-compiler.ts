import * as JSZip from '@/common/jszip';

import { Formatter } from '../formatter';
import { File } from '../../file';
// import { HeaderWrapper } from '../../file/header-wrapper';

interface IXmlifyedFile {
  readonly data: string;
  readonly path: string;
}

interface IXmlifyedFileMapping {
  readonly Document: IXmlifyedFile;
  readonly Styles: IXmlifyedFile;
  // readonly Properties: IXmlifyedFile;
  // readonly Numbering: IXmlifyedFile;
  // readonly Relationships: IXmlifyedFile;
  // readonly FileRelationships: IXmlifyedFile;
  readonly Headers: IXmlifyedFile[];
  readonly Footer: IXmlifyedFile;
  // readonly HeaderRelationships: IXmlifyedFile[];
  // readonly FooterRelationships: IXmlifyedFile[];
  // readonly ContentTypes: IXmlifyedFile;
  // readonly AppProperties: IXmlifyedFile;
  // readonly FootNotes: IXmlifyedFile;
  readonly Settings: IXmlifyedFile;
  readonly Media: IXmlifyedFile;
  // readonly Cascade: IXmlifyedFile;
  readonly Html: IXmlifyedFile;
  readonly SourceBind: IXmlifyedFile;
  readonly NISTableJson: IXmlifyedFile;
}

export class Compiler {
  private readonly formatter: Formatter;

  constructor() {
    this.formatter = new Formatter();
  }

  public async compile(file: File): Promise<JSZip> {
    const zip = new JSZip();

    const xmlifiedFileMapping = this.xmlifyFile(file);
    // console.log(xmlifiedFileMapping)

    for (const key in xmlifiedFileMapping) {
      // console.log(key)
      if (!xmlifiedFileMapping[key]) {
        continue;
      }

      // desert empty html file
      if (key === 'Html' && xmlifiedFileMapping[key] != null && xmlifiedFileMapping[key].data.length === 0) {
        continue;
      }

      const obj = xmlifiedFileMapping[key] as IXmlifyedFile | IXmlifyedFile[];

      if (Array.isArray(obj)) {
        for (const subFile of obj) {
          zip.file(subFile.path, subFile.data);
        }
      } else {
        zip.file(obj.path, obj.data);
      }
    }

    // for (const data of file.Media.Array) {
    //     const mediaData = data.stream;
    //     zip.file(`word/media/${data.fileName}`, mediaData);
    // }

    // for (const header of file.Headers) {
    //     for (const data of header.Media.Array) {
    //         zip.file(`word/media/${data.fileName}`, data.stream);
    //     }
    // }

    // for (const footer of file.Footers) {
    //     for (const data of footer.Media.Array) {
    //         zip.file(`word/media/${data.fileName}`, data.stream);
    //     }
    // }

    return zip;
  }

  private xmlifyFile(file: File): IXmlifyedFileMapping {
    // console.log(file)
    // console.log(file.Html)
    file.verifyUpdateFields();
    // console.log(this.formatter.format(file.Document));
    // console.log(file.Headers);
    return {
      Document: {
        data: this.formatter.format(file.Document),
        path: 'Document.xml',
      },
      Styles: {
        data: this.formatter.format(file.Styles),
        path: 'Styles.xml',
      },
      // Properties: {
      //     data: xml(this.formatter.format(file.CoreProperties), {
      //         declaration: {
      //             standalone: "yes",
      //             encoding: "UTF-8",
      //         },
      //     }),
      //     path: "docProps/core.xml",
      // },
      // Numbering: {
      //     data: xml(this.formatter.format(file.Numbering)),
      //     path: "word/numbering.xml",
      // },
      // Relationships: {
      //     data: xml(this.formatter.format(file.DocumentRelationships)),
      //     path: "word/_rels/document.xml.rels",
      // },
      // FileRelationships: {
      //     data: xml(this.formatter.format(file.FileRelationships)),
      //     path: "_rels/.rels",
      // },
      Headers: file.Headers.length > 0 ? file.Headers.map((header, index) => ({
        data: this.formatter.format(header),
        path: `Header${index + 1}.xml`,
      })) : null,
      Footer: file.Footer ? {
        data: this.formatter.format(file.Footer),
        path: 'Footer.xml',
      } : null,
      // HeaderRelationships: file.Headers.map((headerWrapper, index) => ({
      //     data: xml(this.formatter.format(headerWrapper.Relationships)),
      //     path: `word/_rels/header${index + 1}.xml.rels`,
      // })),
      // FooterRelationships: file.Footers.map((footerWrapper, index) => ({
      //     data: xml(this.formatter.format(footerWrapper.Relationships)),
      //     path: `word/_rels/footer${index + 1}.xml.rels`,
      // })),
      // ContentTypes: {
      //     data: xml(this.formatter.format(file.ContentTypes)),
      //     path: "[Content_Types].xml",
      // },
      // AppProperties: {
      //     data: xml(this.formatter.format(file.AppProperties)),
      //     path: "docProps/app.xml",
      // },
      // FootNotes: {
      //     data: xml(this.formatter.format(file.FootNotes)),
      //     path: "word/footnotes.xml",
      // },
      Settings: {
        data: this.formatter.format(file.Settings),
        path: 'Settings.xml',
      },
      Media: {
        data: this.formatter.format(file.Media),
        path: 'Media.xml',
      },
      // Cascade: {
      //   data: xml(this.formatter.format(file.Cascade), {declaration: {
      //       standalone: 'yes',
      //     encoding: 'UTF-8',
      //   }}),
      //   path: 'Cascade.xml'
      // },
      Html: {
        data: file.Html,
        path: 'Editor-html.html',
      },

      SourceBind: {
        data: file.SourceBind,
        path: 'SourceBind.json',
      },

      NISTableJson: {
        data: file.NISTableJson,
        path: 'NISTable.json',
      },
    };
  }

  /* By default docx collapse empty tags. <a></a> -> <a/>. this function mimic it
     so comparing (diff) original docx file and the library output is easier
     Currently not used, so commenting out */
  // private collapseEmptyTags(xmlData: string): string {
  //     const regEx = /<(([^ <>]+)[^<>]*)><\/\2>/g;
  //     const collapsed = xmlData.replace(regEx, "<$1/>");
  //     return collapsed;
  // }
}
