#!/usr/bin/env node
const Git = require('nodegit');
const path = require('path');
const fs = require('fs');
const util = require('util');
const exec = util.promisify(require('child_process').exec);
(async () => {
  const repo = await Git.Repository.open(process.cwd());
  const head = await repo.getHeadCommit();
  const all = await history(head);

  const tmp = `/tmp/hz-editor-versions`;
  const outputPath = '/tmp/hz-editor-versions-ouput';

  /**获取指定日期当天最后一个提交*/
  //const versionNumbers = new Set(['2020-12-29', '2021-1-22', '2021-3-2', '2021-3-26', '2021-4-17']);
  const versionNumbers = new Set(['2020-12-29']);
  const versions = {};
  for (const commit of all) {
    if(versionNumbers.size <= 0) {
      break;
    }
    const d = commit.date();
    const commitId = commit.sha();
    const dataString = `${d.getFullYear()}-${d.getMonth()+1}-${d.getDate()}`;
    if(versionNumbers.has(dataString)) {
      versions[dataString] = commit;
      versionNumbers.delete(dataString);
    }
  }

  /**获取各版本源代码文件到tmp目录*/
  const versionNames = Object.keys(versions);
  for(const name of versionNames) {
    const commit = versions[name];
    const entries = await tsFileEntris(commit);
    for(entry of entries) {
      const file = await entry.getBlob();
      const data = file.content();
      const filePath = path.resolve(`${tmp}/${name}`, entry.path());
      const dirname = path.dirname(filePath);
      if(!fs.existsSync(dirname)) {
        fs.mkdirSync(dirname, {recursive: true});
      }
      fs.writeFileSync(filePath, data, {mode: entry.filemode()});
    };
  };

  /**编译各个版本*/
  const paths = Object.keys(versions).map(name => `${tmp}/${name}`);
  await build(paths);

  /**提取各个版本编译结果*/
  if(fs.existsSync(outputPath)) {
    await exec(`rm -rf ${outputPath}`);
  }
  fs.mkdirSync(outputPath, {recursive: true});
  for(let name of versionNames) {
    const p = `${tmp}/${name}/dist/`;
    const newPath = `${outputPath}/${name}/`;
    fs.renameSync(p, newPath);
  }

  /**清除各个版本源代码*/
  await exec(`rm -rf ${tmp}`);

})();


async function build(paths) {
  for(const p of paths) {
    const { stdout, stderr } = await exec(`cd ${p}; npm install; npm run build`);
    console.log('stdout:', stdout);
    console.error('stderr:', stderr);
  }
}

async function history(commit) {
    return new Promise((resolve, reject) => {
      commit.history()
        .on('end', all => resolve(all))
        .on('error', (e) => reject(e))
        .start();
  });
}

async function tsFileEntris(commit) {
  let tsEntries = [];
  const tree = await commit.getTree();
  const entries = await tree.entries();
  for (const entry of entries) {
      const tsfiles = await getTSFileEntries(entry);
      tsEntries = tsEntries.concat(tsfiles);
    }
  return tsEntries;
}

async function getTSFileEntries(entry) {
  let tsFileEntries = [];
  if (entry.isFile()) {
    tsFileEntries = tsFileEntries.concat(entry);
  } else {
    const tree = await entry.getTree();
    const children = await tree.entries();
    for (const child of children) {
      const childPaths = await getTSFileEntries(child);
      tsFileEntries = tsFileEntries.concat(childPaths);
    }
  }
  return tsFileEntries;
}


