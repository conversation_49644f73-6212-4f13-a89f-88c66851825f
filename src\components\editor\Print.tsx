import PrintModel from '../../common/print/Print';
import {doPrintStyle} from '../../common/css/style';
import {GlobalEvent as gEvent, GlobalEventName as gEventName} from '../../common/GlobalEvent';
import { getEditorDomContainer } from '../../common/commonDefines';
export default class Print extends PrintModel {
    private _iframe: any; // this is a <div>!  iframe container
    private _docId: number;
    constructor(host: any) {
        super(host);
        this._docId = host.docId;
        this.addFrame();
        this.addEvent();
    }

    public close(): void {
        this.deleteEvent();
    }

    private addFrame(): void {
        if (this.iframeContainer) {
            return;
        }
        const pagePro = this.getPager();
        const iframe = document.createElement('div');
        iframe.className = 'printFrame-box';
        iframe.id = 'printFrame-box';
        iframe.innerHTML = `<iframe id='printFrame' src='about:blank' frameborder='0'
            style='width: 100%; height: ${pagePro['height']}px;'></iframe>`;
        const container = getEditorDomContainer();
        container.appendChild(iframe);
        const win = this.iframeContainer = iframe.querySelector('iframe').contentWindow;
        const style = document.createElement('style');
        style.type = 'text/css';
        style.innerHTML = doPrintStyle();
        win.document.head.appendChild(style);
        this._iframe = iframe;
    }

    private deleteEvent = (): void => {
        this._iframe.outerHTML = '';
        gEvent.deleteEvent(this._docId, gEventName.UnMounted, this.deleteEvent);
    }

    private addEvent(): void {
        // window['printEvent'] = (type: number) => {
        //     this.print(type);
        // };
        // window['showPrintHeaderEvent'] = (showHeader: boolean) => {
        //     this.showHeader = showHeader;
        // };
    }
}
