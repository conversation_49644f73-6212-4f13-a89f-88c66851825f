import { SectionProperty } from './SectionProperty';
import HeaderFooter from './HeaderFooter';
import { WasmInstance } from '../../common/WasmInstance';
import { LOOP_THRESHOLD } from '../../common/commonDefines';
// import { WasmInstance } from '../../common/WasmInstance';

export class DocumentSectionsInfoElement {
    public sectProperty: SectionProperty;
    public index: number; // somewhat related with page.index, para index feeling
    public id: number; // pageindex

    constructor(sectPro: SectionProperty, index: number, id?: number) {
        this.sectProperty = sectPro;
        this.index = index;
        if (id != null) {
          this.id = id;
        }
    }
}

// in Document.js in Only
export class DocumentSectionsInfo {
    public elements: DocumentSectionsInfoElement[];

    constructor() {
        this.elements = [];
    }

    public add(sectPro: SectionProperty, index: number, pageIndex?: number): void {
        this.elements.push( new DocumentSectionsInfoElement( sectPro, index, pageIndex ) );
    }

    public getSectPr(index: number): DocumentSectionsInfoElement {
        // const count = this.elements.length;
        // for ( let pos = 0; pos < count; pos++ ) {
        //     if ( index <= this.elements[pos].index ) {
        //         return this.elements[pos];
        //     }
        // }
        // return this.elements[count - 1];
        return this.elements[0];
    }

    public getSectPr2(elementIndex: number): DocumentSectionsInfoElement {
      return this.elements[elementIndex];
    }

    public getIndex(index: number, pageIndex: number): number {
      const count = this.elements.length;
      // console.log('count: ' + count)
      // key-wasm by tinyzhi
      const elementsIndex = [];
      for (let pos = 0; pos < count; pos++) {
        elementsIndex[pos] = this.elements[pos].index;
      }
      const temp = WasmInstance.arrayToHeap(elementsIndex as any);
      let nResult = WasmInstance.instance._SectionInfo_getIndex(temp.byteOffset, elementsIndex.length, 0);
      WasmInstance.freeArray(temp);

      // double check id, if not equal, return sectIndex - 1;
      const candSectionInfoElem = this.elements[nResult];
      if (pageIndex != null && candSectionInfoElem.id != null) {
        // console.log('nResult: ' + nResult)
        // console.log(candSectionInfoElem.id)
        // console.log(pageIndex)
        if (pageIndex < candSectionInfoElem.id) {
          nResult--;
        } else if (pageIndex > candSectionInfoElem.id) {
          // find the real candSectionInfoElem
          const initial = nResult + 1;
          for (let i = initial; i < count; i++) {
            if (this.elements[i].id === pageIndex) {
              nResult = i;
              // console.log('inner nResult: ' + nResult)
              break;
            }
          }
        }
      }

      return nResult;
      // end by tinyzhi
    }

    public getIndex2(index: number, pageIndex?: number): number {
      const count = this.elements.length;
      // console.log('count: ' + count)
      let elementIndex = 0;
      if (count > 1) {
        if (pageIndex > 0 && pageIndex < this.elements[1].id) {
          //
        } else {
          // except 0 -> 1 condition, default to be count - 1 index
          elementIndex = count - 1;
        }
      }
      for (let pos = 0; pos < count; pos++) {
        if (index === this.elements[pos].index) {
          elementIndex = pos;
          break;
        }
      }

      // double check id, if not equal, return sectIndex - 1;
      const candSectionInfoElem = this.elements[elementIndex];
      // console.log('elementIndex: ' + elementIndex)
      // console.log(candSectionInfoElem.id)
      // console.log(pageIndex)
      if (pageIndex != null && candSectionInfoElem.id != null) {
        if ( pageIndex < candSectionInfoElem.id) {
          elementIndex--;
        } else if (pageIndex > candSectionInfoElem.id) {
          // find the real candSectionInfoElem with id closes to pageIndex
          const initial = elementIndex + 1;

          if (initial < count) {
            let flag = false;
            let tempPageIndex = pageIndex;
            let loopCount = 0;
            while (flag === false) {
              for (let i = initial; i < count; i++) {
                // console.log(this.elements[i].id)
                // console.log(tempPageIndex)
                if (this.elements[i].id === tempPageIndex) {
                  elementIndex = i;
                  // console.log('inner elementIndex: ' + elementIndex)
                  flag = true;
                  break;
                }
              }
              tempPageIndex--;
              loopCount++;
              if (loopCount > LOOP_THRESHOLD) {
                // tslint:disable-next-line:no-console
                console.warn('max loop reached');
                break;
              }
              if (tempPageIndex <= 0) {
                // tslint:disable-next-line:no-console
                // console.warn('cannot find expected sectIndex');
                break;
              }
            }
          }
        }
      }
      return elementIndex;
    }

    public findByHeaderFooter(headerFooter: HeaderFooter): number {
      const count = this.elements.length;
      for (let index = 0; index < count; index++) {
        const sectPr = this.elements[index].sectProperty;
        if (sectPr.getHeaderFirst() === headerFooter || sectPr.getHeaderDefault() === headerFooter ||
          sectPr.getFooterFirst() === headerFooter || sectPr.getFooterDefault() === headerFooter) {
          return index;
        }
      }
      return -1;
    }

    public updateSection(sectPr: SectionProperty, newSectPr: SectionProperty, isCheckHdrFtr: boolean): boolean {
      if (sectPr === newSectPr || !sectPr) {
        return false;
      }
      for (let index = 0, count = this.elements.length; index < count; index++) {
        if (sectPr === this.elements[index].sectProperty) {
          if (!newSectPr) {
            // Copy Word Behavior: If the next section doesn't have any footers at all,
            // then we copy the links to the footers from the removed section. If there's even one footer,
            // then we don't.
            if (isCheckHdrFtr === true && index < count - 1) {
              const curSectPr = this.elements[index].sectProperty;
              const nextSectPr = this.elements[index + 1].sectProperty;

              if (nextSectPr.isAllHeaderFooterNull() === true && curSectPr.isAllHeaderFooterNull() !== true) {
                nextSectPr.setHeaderFirst(curSectPr.getHeaderFirst());
                nextSectPr.setHeaderDefault(curSectPr.getHeaderDefault());
                nextSectPr.setFooterFirst(curSectPr.getFooterFirst());
                nextSectPr.setFooterDefault(curSectPr.getFooterDefault());
              }
            }

            this.elements.splice(index, 1);
          } else {
            this.elements[index].sectProperty = newSectPr;
          }

          return true;
        }
      }

      return false;
    }

    public clear(): void {
      this.elements.length = 0;
    }

    public getAllheaderFooters(): HeaderFooter[] {
      const headerFooters = [];
      const count = this.elements.length;
      for (let index = 0; index < count; index++) {
        const sectProperty = this.elements[index].sectProperty;
        sectProperty.getAllHeaderFooters(headerFooters);
      }

      return headerFooters;
    }

    public getHFInfo(): any[] {
      const info = [];
      this.elements.forEach((element) => {
        info.push({
          index: element.index,
          id: element.id,
        });
      });

      return info;
    }
}
