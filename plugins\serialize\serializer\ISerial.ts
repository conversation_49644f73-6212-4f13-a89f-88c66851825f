export default interface ISerial {

    /**
     * 获取序列化对象
     */
    serializedTo: (collector: any[]) => any[];
}

/**
 * 段落元素的类型
 */
export enum ParaElementType {
    Unkown = -1,
    ParaText = 1,
    ParaTextPr,
    ParaSpace,
    ParaEnd,
    ParaNewLine,
    ParaSym,
    ParaTab,
    // Para_NewLineRendered,
    ParaPortion,
    ParaPageNum,
    ParaDrawing,
    ParaComment,  // 批注
    ParaMedEquation,  // 医学公式
    ParaNewControlBorder,
    ParaFixedHeight, // 打印留白高度
    ParaSvgDrawing,
    ParaVideoDrawing,
    ParaAudioDrawing,
}

/**
 * 斜体
 */
export enum FontStyleType {
    Normal, Italic,
}

/**
 * 删除线，下划线
 */
export enum TextDecorationLineType {
    None, Underline, Overline, Blink,
}

/**
 * 粗体
 */
export enum FontWeightType {
    Normal, Bold,
}

export enum TextVertAlign {
    Baseline, // 默认。元素放置在父元素的基线上。
    Sub, // 垂直对齐文本的下标。
    Super, // 垂直对齐文本的上标
    Top,
    TextTop,
    Middle,
    Bottom, // 把元素的顶端与行中最低的元素的顶端对齐。
    TextBottom,
}
/**
 * 计算单位
 */
export enum TableWidthType {
    Auto,
    Mm,  // 毫米
    Pencent,  // 百分比
}

export enum VerticalMergeType {
    Restart,  // 无合并
    Continue,  // 合并
}

export enum TextDirectionType {
    LRTB,
}

/**
 * 背景颜色填充
 */
export enum ShadowFillType {
    None,  // 无填充
    Fill,  // 填充
}

export enum VertAlignType {
    Top = 0,
    Middle,
    Bottom,
}

export enum TableRowLineRule {
    Auto,
    Exact,
    AtLeast,
    Fixed,
}
export enum TableBorderLineStyle {
    None,  // 无线条
    Single,  // 实线
    Dash,  // 虚线
}

/**
 * 设置单元格，行的宽度
 */
export class TableMeasurement {
    public type: TableWidthType;  // 宽度类型
    public width: number;

    constructor(type: TableWidthType, width: number) {
        this.type = type;
        this.width = width;
    }

    public copy(): TableMeasurement {
        return new TableMeasurement(this.type, this.width);
    }
}
/**
 * 段落对齐方式
 */
export enum AlignType {
    Left, Center, Right, Justify,
}

export class GenericBox<T> {
    public bottom: T;
    public left: T;
    public right: T;
    public top: T;

    constructor(bottom: T, left: T, right: T, top: T) {
        this.bottom = bottom;
        this.left = left;
        this.right = right;
        this.top = top;
    }
}
