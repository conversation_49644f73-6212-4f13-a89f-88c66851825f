# 剪贴板格式控制需求文档

## 概述

本文档描述了编辑器剪贴板格式控制功能的完整需求，包括内部格式控制、外部格式控制、跨编辑器复制粘贴等功能。

## 功能需求

### 1. 内部格式控制 (bInnerFormat)

#### 1.1 功能描述
控制内部Apollo格式内容在复制粘贴时是否保持格式。

#### 1.2 接口设置
```javascript
editor.setClipboardFormat(bOuterFormat, bInnerFormat);
```

#### 1.3 行为规范

| bInnerFormat | 复制粘贴行为 | 说明 |
|-------------|-------------|------|
| true (默认) | 保持Apollo格式 | 复制粘贴时保留完整的结构化信息和格式 |
| false | 使用纯文本 | 复制粘贴时只保留文本内容，丢弃所有格式 |

#### 1.4 适用场景
- **同编辑器内复制粘贴**：Ctrl+C/Ctrl+V
- **右键菜单复制粘贴**：右键复制 → 右键粘贴
- **跨tab页复制粘贴**：不同tab页之间的复制粘贴

### 2. 外部格式控制 (bOuterFormat)

#### 2.1 功能描述
控制外部内容（来自其他应用程序）在粘贴时是否保持格式。

#### 2.2 行为规范

| bOuterFormat | 粘贴行为 | 说明 |
|-------------|----------|------|
| true (默认) | 保持外部格式 | 粘贴外部HTML/RTF内容时保留格式 |
| false | 转换为纯文本 | 粘贴外部内容时只保留文本，丢弃格式 |

#### 2.3 适用场景
- **从Word粘贴**：保持或丢弃Word文档格式
- **从网页粘贴**：保持或丢弃HTML格式
- **从其他富文本编辑器粘贴**

### 3. 跨编辑器复制粘贴

#### 3.1 功能描述
支持在不同tab页的编辑器实例之间进行复制粘贴操作。

#### 3.2 技术实现
- **编辑器实例识别**：使用唯一ID区分不同编辑器实例
- **数据存储**：使用localStorage存储复制数据
- **格式控制**：根据bInnerFormat设置选择数据格式

#### 3.3 数据方案

| 数据类型 | 使用场景 | 格式控制 |
|---------|----------|----------|
| Blob数据 | bInnerFormat=true时的跨编辑器复制 | 保持完整结构 |
| 纯文本数据 | bInnerFormat=false时的跨编辑器复制 | 只保留文本 |
| 降级方案 | Blob方案失败时 | 自动使用localStorage中的纯文本 |

### 4. 外部拷贝权限控制

#### 4.1 功能描述
控制是否允许从外部应用程序复制内容到编辑器。

#### 4.2 接口设置
```javascript
editor.enableCopyFromExternal(bEnable);
```

#### 4.3 行为规范

| bEnable | 行为 | 说明 |
|---------|------|------|
| true (默认) | 允许外部复制 | 可以从外部应用程序复制内容 |
| false | 禁止外部复制 | 拒绝外部内容，显示错误提示 |

### 5. 权限验证控制

#### 5.1 功能描述
通过额外的复制信息进行权限验证。

#### 5.2 接口设置
```javascript
editor.setExtraCopyInformation(sCopyInformation);
```

#### 5.3 行为规范
- 设置后，复制粘贴操作需要通过权限验证
- 用于防止不同病人间的病历复制等安全场景

## 技术实现

### 1. 数据流程

```mermaid
graph TD
    A[用户复制] --> B{检查数据来源}
    B -->|内部数据| C{检查bInnerFormat}
    B -->|外部数据| D{检查bOuterFormat}
    C -->|true| E[使用Apollo格式]
    C -->|false| F[使用纯文本]
    D -->|true| G[保持外部格式]
    D -->|false| H[转换为纯文本]
    E --> I[执行粘贴]
    F --> I
    G --> I
    H --> I
```

### 2. 跨编辑器数据存储

```javascript
// localStorage数据结构
{
    timestamp: 1234567890,           // 时间戳
    apolloData: "...",               // Apollo格式数据
    plainText: "纯文本内容",          // 纯文本数据
    blobString: "base64...",         // Blob数据(base64编码)
    editorId: "unique-editor-id"     // 编辑器实例ID
}
```

### 3. 格式控制优先级

1. **权限验证**：首先检查是否有权限进行复制粘贴
2. **数据来源判断**：区分内部数据和外部数据
3. **格式设置应用**：根据bInnerFormat/bOuterFormat选择处理方式
4. **降级处理**：复杂方案失败时自动降级到简单方案

## 测试场景

### 1. 基础功能测试

| 测试场景 | bInnerFormat | bOuterFormat | 预期结果 |
|---------|-------------|-------------|----------|
| 内部复制粘贴 | false | true | 使用纯文本 |
| 内部复制粘贴 | true | false | 使用Apollo格式 |
| 外部内容粘贴 | true | false | 使用纯文本 |
| 外部内容粘贴 | false | true | 保持外部格式 |

### 2. 跨编辑器测试

| 测试场景 | bInnerFormat | 预期结果 |
|---------|-------------|----------|
| 跨tab页复制粘贴 | false | 使用纯文本数据 |
| 跨tab页复制粘贴 | true | 使用Blob数据，失败时降级到纯文本 |

### 3. 权限控制测试

| 测试场景 | 设置 | 预期结果 |
|---------|------|----------|
| 禁止外部复制 | enableCopyFromExternal(false) | 拒绝外部内容 |
| 权限验证 | setExtraCopyInformation("xxx") | 需要权限验证 |

## 兼容性说明

### 1. 浏览器兼容性
- **Chrome/Edge**: 完全支持
- **Firefox**: 支持基础功能
- **Safari**: 支持基础功能，部分API可能受限

### 2. 降级方案
- Clipboard API不可用时，使用传统的复制粘贴方式
- 跨编辑器Blob方案失败时，自动降级到localStorage纯文本方案

## 更新日志

### v1.0.0 (当前版本)
- ✅ 实现bInnerFormat控制
- ✅ 实现bOuterFormat控制  
- ✅ 实现跨编辑器复制粘贴
- ✅ 实现权限控制
- ✅ 实现降级方案
