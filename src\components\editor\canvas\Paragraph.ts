import {ParaLineUI} from './ParaLine';
import { ICanvasProps } from './common';
import { IDocumentParagraph } from '../../../model/ParaProperty';

export class ParagraphUI {
    private props: IDocumentParagraph;
    private host: any;
    private ctx: any;
    constructor(props: ICanvasProps) {
        this.ctx = props.ctx;
        this.host = props.host;
        this.props = props.content;
        this.props.pageIndex = props.pageIndex;
        this.render();
    }

    public render(): any {
        const { content, lines, startLine, endLine, cellId, pageIndex} = this.props;
        return lines.map((item, index) => {
            if (startLine <= index && index <= endLine) {
                const curContent: any = {cellId, pageIndex, content};
                const props = {content: curContent, ctx: this.ctx, host: this.host, pageIndex};
                curContent.id = index;
                const obj = new ParaLineUI(props);
            }
        });
    }
}
