import * as React from 'react';
import Dialog from '../../ui/Dialog';
import { INewControlProperty, NewControlType,
    NewControlDefaultSetting,
    CodeValueItem, isValidName} from '../../../../common/commonDefines';
import Input from '../../ui/Input';
import Checkbox from '../../ui/CheckboxItem';
import Button from '../../ui/Button';
import Select from '../../ui/select/Select';
import NewControlCustomProps from './CustomProperty';
import NewComboBoxList from './NewComboBoxList';
import { message } from '../../../../common/Message';
import { IFRAME_MANAGER } from '../../../../common/IframeManager';
import CascadeBtn from './NewCascade';
import { ExternalDataBind } from './ExternalDataBind';

interface IDialogProps {
    documentCore: any;
    visible: boolean;
    id: string;
    property?: INewControlProperty;
    close: (name: string | number, bRefresh?: boolean) => void;
    type?: number;
}

interface IState {
    bRefresh: boolean;
}

export default class NewControlRadio extends React.Component<IDialogProps, IState> {
    private newControl: INewControlProperty;
    private bCustomProperty: boolean;
    private visible: any;
    private types: any[];
    private docId: number;
    private bCascade: boolean;
    private title: string;
    private resetSourceBind: boolean;
    private dataBind: any;
    private bCreateNew: boolean;
    constructor(props: any) {
        super(props);
        this.state = {
            bRefresh: false,
        };
        this.visible = this.props.visible;
        this.newControl = {newControlName: undefined};
        this.types = [
            {key: 'RadioBox', value: NewControlType.RadioButton},
            {key: 'CheckBox', value: NewControlType.MultiRadio},
        ];
        this.docId = this.props.documentCore.getCurrentId();
    }

    public render(): any {
        return (
            <Dialog
                visible={this.visible}
                width={380}
                open={this.open}
                title={this.title}
                footer={this.renderFooter()}
                id={this.props.id}
            >
                <div>
                    <div className='editor-line'>
                        <span className='title'>常规</span>
                    </div>
                    <div className='editor-line'>
                        <div className='w-70'>名称</div>
                        <div className='right-auto'>
                            <Input
                                name='newControlName'
                                value={this.newControl.newControlName}
                                onChange={this.onChange}
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <div className='w-70'>内部名称</div>
                        <div className='right-auto'>
                            <Input
                                name='newControlSerialNumber'
                                value={this.newControl.newControlSerialNumber}
                                onChange={this.onChange}
                                readonly={true}
                                placeholder={''}
                                disabled={true}
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <div className='w-70'>标识符</div>
                        <div className='right-auto'>
                            <Input
                                name='identifier'
                                value={this.newControl.identifier}
                                onChange={this.onChange}
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <div className='w-70'>提示符</div>
                        <div className='right-auto'>
                            <Input
                                name='newControlInfo'
                                value={this.newControl.newControlInfo}
                                onChange={this.onChange}
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <div className='w-70'>样式</div>
                        <div className='right-auto'>
                            <Select
                                data={this.types}
                                name='showType'
                                value={this.newControl.showType}
                                onChange={this.onChange}
                            />
                        </div>
                    </div>

                    <div className='editor-line editor-multi-line'>
                        <span className='title w-70'>属性</span>
                        <div className='right-auto newcontrol-prop'>
                            <div className='editor-line'>
                                <div className='w-050'>
                                    <Checkbox
                                        name='isNewControlHidden'
                                        value={this.newControl.isNewControlHidden}
                                        onChange={this.onChange}
                                    >
                                        隐藏
                                    </Checkbox>
                                </div>
                                <div className='w-050'>
                                    <Checkbox
                                        name='isNewControlCanntDelete'
                                        value={this.newControl.isNewControlCanntDelete}
                                        onChange={this.onChange}
                                    >
                                        禁止删除
                                    </Checkbox>
                                </div>
                            </div>

                            <div className='editor-line'>
                                <div className='w-050'>
                                    <Checkbox
                                        name='isNewControlCanntEdit'
                                        value={this.newControl.isNewControlCanntEdit}
                                        onChange={this.onChange}
                                    >
                                        禁止编辑
                                    </Checkbox>
                                </div>
                                <div className='w-050'>
                                    <Checkbox
                                        name='isNewControlMustInput'
                                        value={this.newControl.isNewControlMustInput}
                                        onChange={this.onChange}
                                    >
                                        必填项
                                    </Checkbox>
                                </div>
                            </div>

                            <div className='editor-line'>
                                <div className='w-050'>
                                    <Checkbox
                                        name='isNewControlShowBorder'
                                        value={this.newControl.isNewControlShowBorder}
                                        onChange={this.onChange}
                                    >
                                        显示边框
                                    </Checkbox>
                                </div>

                                <div className='w-050'>
                                    <Checkbox
                                        name='isNewControlReverseEdit'
                                        value={this.newControl.isNewControlReverseEdit}
                                        onChange={this.onChange}
                                    >
                                        反向编辑
                                    </Checkbox>
                                </div>
                            </div>

                            <div className='editor-line'>
                                <div className='w-050'>
                                    <Checkbox
                                        name='isNewControlHiddenBackground'
                                        value={this.newControl.isNewControlHiddenBackground}
                                        onChange={this.onChange}
                                    >
                                        隐藏背景色
                                    </Checkbox>
                                </div>
                                <div className='w-050'>
                                    <Checkbox
                                        name='showRight'
                                        value={this.newControl.showRight}
                                        onChange={this.onChange}
                                    >
                                        选择框居右
                                    </Checkbox>
                                </div>
                            </div>
                            <div className='editor-line'>
                                <div className='w-050'>
                                    <Checkbox
                                        name='tabJump'
                                        value={this.newControl.tabJump}
                                        onChange={this.onChange}
                                    >
                                        键盘跳转
                                    </Checkbox>
                                </div>
                                <div className='w-050'>
                                    <Checkbox
                                        name='supportMultLines'
                                        value={this.newControl.supportMultLines}
                                        onChange={this.onChange}
                                    >
                                        支持分行
                                    </Checkbox>
                                </div>
                            </div>

                            <div className='editor-line'>
                                <NewControlCustomProps
                                    name='customProperty'
                                    properties={this.newControl.customProperty}
                                    documentCore={this.props.documentCore}
                                    onChange={this.onChange}
                                    close={this.onClose}
                                    id='bCustomProperty'
                                    visible={this.bCustomProperty}
                                />
                            </div>
                        </div>

                        <div className='editor-line'>
                            <span className='title'>下拉选项</span>
                        </div>
                        <NewComboBoxList
                            value={this.newControl.newControlItems}
                            onChange={this.onChange}
                            docId={this.docId}
                            name='newControlItems'
                        />
                        <div className='editor-line'>
                            <span className='title'>固有属性</span>
                        </div>
                        <div className='editor-line'>
                            <span className='w-070'>值间距：</span>
                            <div className='right-auto'>
                                <Input
                                    value={this.newControl.spaceNum}
                                    name='spaceNum'
                                    type='number'
                                    onChange={this.onChange}
                                    disabled={true === this.newControl.supportMultLines}
                                    renderAppend={this.renderCell}
                                />
                            </div>
                        </div>
                        <div className='editor-line'>
                            <div className='w-70'>
                                级联
                            </div>
                            <div className='right-auto'>
                                <CascadeBtn
                                    visible={this.bCascade}
                                    id='bCascade'
                                    controlName={this.newControl.newControlName}
                                    documentCore={this.props.documentCore}
                                    properties={this.newControl.cascade}
                                    name='cascade'
                                    close={this.onClose}
                                    onChange={this.onChange}
                                    type={this.newControl.newControlType}
                                />
                            </div>
                        </div>
                        <div className='editor-line'>
                            <Checkbox
                                name='printSelected'
                                value={this.newControl.printSelected}
                                onChange={this.onChange}
                            >
                                未被勾选时不被打印
                            </Checkbox>
                        </div>
                    </div>
                    <ExternalDataBind
                            name={this.newControl.newControlName}
                            id='externalDataBind'
                            visible={this.visible}
                            documentCore={this.props.documentCore}
                            onChange={this.onChange}
                            close={this.onClose}
                            properties={this.dataBind}
                            resetId={'resetSourceBind'}
                    />
                </div>
            </Dialog>
        );
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        this.visible = nextProps.visible;
    }

    private renderCell(): any {
        return '字符';
    }

    private renderFooter(): any {
        return (
            <span>
                <Button onClick={this.close}>取消</Button>
                <Button type='primary' onClick={this.confirm}>确认</Button>
            </span>
        );
    }

    private onClose = (id: string, bRefresh: boolean): void => {
        this[id] = false;
        if (['bCustomProperty', 'bCascade'].includes(id) && bRefresh) {
            this.setState({bRefresh: !this.state.bRefresh});
        } else if ('externalDataBind' === id && bRefresh) {
            this.resetSourceBind = false;
            this.setState({bRefresh: !this.state.bRefresh});
        }
    }

    private open = (): void => {
        const props = this.props.property;
        const newControl: INewControlProperty = this.newControl = {} as any;
        if (props === undefined) {
            this.bCreateNew = true;
            this.init();
            newControl.showType = newControl.newControlType = this.props.type;
            newControl.newControlName = this.props.documentCore.makeUniqueNewControlName(NewControlType.RadioButton);
        } else {
            this.bCreateNew = false;
            const keys = Object.keys(props);
            keys.push('cascade');
            keys.forEach((key) => {
                if (key === 'newControlItems') {
                    return;
                }
                const val = props[key];
                newControl[key] = val;
            });
            const items = props.newControlItems;
            if (items && items.length > 0) {
                newControl.newControlItems = items.map((item) => {
                    return new CodeValueItem(item.code, item.value, item.bSelect);
                });
            }
            // switch (newControl.newControlType) {
            //     case NewControlType.Combox: {
            //         this.isNewControlTypeDisable = false;
            //         this.newControlType = NewControlType.Combox;
            //         break;
            //     }
            //     case NewControlType.ListBox: {
            //         this.isNewControlTypeDisable = false;
            //         this.newControlType = NewControlType.ListBox;
            //         break;
            //     }
            //     case NewControlType.MultiCombox: {
            //         this.isNewControlTypeDisable = true;
            //         this.newControlType = NewControlType.Combox;
            //         break;
            //     }
            //     case NewControlType.MultiListBox: {
            //         this.isNewControlTypeDisable = true;
            //         this.newControlType = NewControlType.ListBox;
            //         break;
            //     }
            // }
        }
        const title = '选项按钮';
        if (newControl.newControlType === NewControlType.RadioButton) {
            this.title = title + '（单选）';
        } else {
            this.title = title + '（多选）';
        }
        if (!props) {
            const bMult = this.title.includes('（多选）');
            const type = bMult ? NewControlType.MultiCheckBox : NewControlType.RadioButton;
            this.newControl.newControlName = this.props.documentCore.makeUniqueNewControlName(type);
        }

        this.dataBind = newControl.externalDataBind;
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private checkedData(): boolean {
        const listDatas = this.newControl.newControlItems;
        if (listDatas && listDatas.length > 0) {
            const obj = {};
            for (let index = 0, length = listDatas.length; index < length; index++) {
                const code = listDatas[index].code;
                if (obj[code] === true) {
                    message.error(`下拉选项第${index + 1}行，存在相同名称`);
                    return false;
                }
                obj[code] = true;
            }
            for (let index = listDatas.length - 1; index >= 0; index--) {
                const code = listDatas[index].code;
                if (!code) {
                    listDatas.splice(index, 1);
                    continue;
                }
            }
        }

        return true;
    }

    private close = (bRefresh?: boolean): void => {
        this.props.close(this.props.id, bRefresh);
        this.visible = false;
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private confirm = (): void => {
        const documentCore = this.props.documentCore;
        IFRAME_MANAGER.setDocId(this.docId);
        if (!this.checkedData()) {
            return;
        }

        const props = this.props.property;
        const newControl = this.newControl;
        if (!isValidName(newControl.newControlName)) {
            message.error('名称不符合规范，请重新输入');
            return;
        }
        if (newControl.identifier && !isValidName(newControl.identifier)) {
            message.error('标识符不符合规范，请重新输入');
            return;
        }
        if ((!props || props.newControlName !== newControl.newControlName)
            && !documentCore.checkNewControlName(newControl.newControlName)) {
            message.error('已存在该名字，请重新命名');
            return;
        }

        if (this.resetSourceBind) {
            this.newControl.externalDataBind = {
                sourceObj: '',
                sourceKey: '',
                bReadOnly: false,
                commandUpdate: 1,
            }

            this.resetSourceBind = false;
        } else if (this.dataBind) {
            this.newControl.externalDataBind = this.dataBind;
        }

        if (props === undefined) {
            documentCore.addNewControl(this.newControl);
        } else {
            documentCore.setNewControlProperty(this.newControl, props.newControlName);
        }

        this.close(true);
    }

    private init(): void {
        const newControl = this.newControl;
        newControl.newControlName = undefined;
        newControl.newControlInfo = undefined;
        newControl.newControlPlaceHolder = NewControlDefaultSetting.DefaultPlaceHolderContent;
        newControl.newControlType = NewControlType.RadioButton;
        newControl.isNewControlHidden = false;
        newControl.isNewControlCanntDelete = false;
        newControl.isNewControlCanntEdit = false;
        newControl.isNewControlMustInput = false;
        newControl.isNewControlShowBorder = true;
        newControl.isNewControlReverseEdit = false;
        newControl.isNewControlHiddenBackground = true;
        newControl.customProperty = undefined;
        newControl.newControlItems = undefined;
        newControl.showRight = false;
        newControl.showType = NewControlType.RadioButton;
        newControl.printSelected = false;
        newControl.spaceNum = 1;
        newControl.tabJump = true;
        newControl.cascade = undefined;
        newControl.identifier = undefined;
        newControl.externalDataBind = undefined;
        newControl.supportMultLines = false;
        this.resetSourceBind = false;
    }

    private onChange = (value: any, name: string): void => {
        if ('showType' === name && value !== this.newControl[name]) {
            const bMult = this.title.includes('（多选）');
            let type = bMult ? NewControlType.MultiRadio : NewControlType.RadioButton;
            if (NewControlType.MultiRadio === type) {
                type = (NewControlType.RadioButton === value ? NewControlType.MultiRadio : NewControlType.MultiCheckBox);
            } else {
                type = (NewControlType.RadioButton === value ? NewControlType.RadioButton : NewControlType.CheckBox);
            }
            if (this.bCreateNew) {
                this.newControl.newControlName = this.props.documentCore.makeUniqueNewControlName(type);
            }
            this.setState({bRefresh: !this.state.bRefresh});
        }

        if ('resetSourceBind' !== name) {
            if ('externalDataBind' !== name) {
                this.newControl[name] = value;
            } else {
                this.dataBind = value;
            }
        } else {
            this.resetSourceBind = true;
        }

        if (name === 'supportMultLines') {
            this.newControl.spaceNum = this.newControl[name] ? null : 1;
            this.setState({bRefresh: !this.state.bRefresh});
        }
    }
}
