import React from "react";
import { ToothMeasureMap, ToothSvgBuilder } from "./builder";
import './style.less';
import { IToothControl, IToothFacing, ToothControl, ToothTable } from "./ToothTable";


export interface IToothStruct {
    /** 中间控制条的标签 */
    controlTags: string[];
    /** 牙齿名称 */
    titles: string[];
    maxilla: IToothFacing;
    control: {
        maxilla: IToothControl,
        mandible: IToothControl,
    },
    mandible: IToothFacing,
    /** 从外向内的牙齿标记位(P,L,B,D,O,M) */
    tags: string[];
}

interface IProps {
    data: IToothStruct;
    measureNode: SVGTextElement;
}

interface IState {
    controlMaxilla: IToothControl,
    controlMandible: IToothControl,
    maxilla: IToothFacing;
    mandible: IToothFacing,
}
/**
 * 初始化牙面信息
 * @param {number} cols 列数
 * @param {boolean} isMandible  是否是下颌牙
 */
export function initialFacing(cols: number, isMandible = false) {
    const facing: IToothFacing = {
        isMandible: isMandible,
        flags: {
            left: [],
            right: [],
        }
    };
    const { flags } = facing;
    for (let i = 0; i < cols; i++) {
        flags.left.push([]);
        flags.right.push([]);
    }
    return facing;
}

/** 初始化牙面控制信息 */
export function initialControl(cols: number) {
    const control: IToothControl = {
        left: [],
        right: [],
        leftActive: [],
        rightActive: [],
    };
    for (let i = 0; i < cols; i++) {
        control.left.push([]);
        control.right.push([]);
    }
    return control;
}

export class DentalBitmap extends React.Component<IProps> {
    private svgBuilder: ToothSvgBuilder | null = null;
    private dataState: IState;

    constructor(props: IProps) {
        super(props);
        this.dataState = this.buildState(props.data);
    }

    public confirm() {
        const newStruct = this.rebuildStruct(this.dataState);
        return this.generateSvgHtml(newStruct);
    }

    public render() {
        const { data } = this.props;
        let titles = [...data.titles];
        const fullTitles = [...data.titles, ...titles.reverse()];
        const colLength = titles.length;

        return (
            <div className="graph" style={{width: `${fullTitles.length * 20 + 40}px`}}>
                <div className="header">上颌</div>
                <div className="panel">
                    <div className="side"></div>
                    <div className="center title" style={{ gridTemplateColumns: `repeat(${fullTitles.length}, 20px)` }}>
                        {fullTitles.map((text, index) => <div className="op-title" key={index}>{text}</div>)}

                    </div>
                    <div className="side"></div>
                </div>
                <div className="panel">
                    <div className="side">
                        <div>牙</div>
                        <div>面</div>
                    </div>
                    <div className="center">
                        <ToothTable
                            colLength={colLength}
                            facings={this.dataState.maxilla}
                            tags={data.tags}
                            title="maxilla"
                            onClick={this.handleClick}
                        />
                    </div>
                    <div className="side"></div>
                </div>
                <hr />
                <div className="panel">
                    <div className="side">
                        <div>右</div>
                    </div>
                    <div className="center">
                        <ToothControl
                            colLength={colLength}
                            control={this.dataState.controlMaxilla}
                            controlTags={data.controlTags}
                            tags={data.tags}
                            title="controlMaxilla"
                            onClick={this.handleControlClick}
                        />
                        <hr />
                        <ToothControl
                            colLength={colLength}
                            control={this.dataState.controlMandible}
                            controlTags={data.controlTags}
                            tags={data.tags}
                            title="controlMandible"
                            onClick={this.handleControlClick}
                        />
                    </div>
                    <div className="side">
                        <div>左</div>
                    </div>
                </div>
                <hr />
                <div className="panel">
                    <div className="side">
                        <div>牙</div>
                        <div>面</div>
                    </div>
                    <div className="center">
                        <ToothTable
                            colLength={colLength}
                            tags={data.tags}
                            title="mandible"
                            facings={this.dataState.mandible}
                            onClick={this.handleClick}
                        />
                    </div>
                    <div className="side"></div>
                </div>
                <div className="footer">下颌</div>
            </div>
        );
    }

    public shouldComponentUpdate(nextProps: Readonly<IProps>, nextState: Readonly<IState>, nextContext: any): boolean {
        const needUpdate = nextProps.data !== this.props.data;
        if (needUpdate) {
            this.dataState = this.buildState(nextProps.data);
        }
        return true;
    }

    /** 根据prop参数创建state */
    private buildState(struct: IToothStruct): IState {
        const { control, mandible, maxilla, tags, controlTags } = struct;


        // 完成基础测距
        // 字体测距
        // 标签的测距信息
        const svgBuilder = new ToothSvgBuilder(tags, controlTags);
        svgBuilder.setMeasure({
            tags: measureText(this.props.measureNode, tags.join(''), svgBuilder.tagFont),
            controlTags: measureText(this.props.measureNode, controlTags.join(''), svgBuilder.controlFont),
        });
        this.svgBuilder = svgBuilder;

        return {
            controlMandible: control.mandible,
            controlMaxilla: control.maxilla,
            mandible,
            maxilla,
        }
    }

    /** 根据state反向构造prop参数 */
    private rebuildStruct(state: IState): IToothStruct {
        const { controlMandible, controlMaxilla, mandible, maxilla } = state;
        const { titles, controlTags, tags } = this.props.data;
        return {
            controlTags,
            titles,
            mandible,
            maxilla,
            control: {
                mandible: controlMandible,
                maxilla: controlMaxilla,
            },
            tags,
        }
    }

    /**  处理压面位点击 */
    private handleClick = (title: string, row: number, col: number, isRight: boolean) => {
        const res = this.extractStateByTitle(title);
        if (!res.length || !res[0]) return;
        const [target, targetTitle, control, controlTitle] = res;
        // 点位调整
        const flags = isRight ? target.flags.right : target.flags.left;
        const rowFlags = flags[row];
        this.toggleItem(rowFlags, col);
        // 控制位调整 --- col 为对应控制位
        const controlFlags = isRight ? control.right : control.left;
        const controlRowFlags = controlFlags[col];
        this.toggleItem(controlRowFlags, row);

        const controlActive = isRight ? control.rightActive : control.leftActive;
        const controlIndex = controlActive.indexOf(col);
        if (controlIndex === -1) {
            if (rowFlags.length) {
                controlActive.push(col);
                controlActive.sort();
            }
        } else {
            // 判断当前控制位（列）是否存在牙位信息
            let isEmpty = true;
            for (const row of flags) {
                if (row.includes(col)) {
                    isEmpty = false;
                    break;
                }
            }
            if (isEmpty) {
                controlActive.splice(controlIndex, 1);
            }
        }
        this.dataState[title] = { ...target };
        this.dataState[controlTitle] = { ...control };
        this.setState({});
    }

    /** 处理中部控制位点击  */
    private handleControlClick = (title: string, pos: number, isRight: boolean) => {
        const res = this.extractStateByTitle(title);
        if (!res.length || !res[0]) return;
        const [target, targetTitle, control, controlTitle] = res;
        const controlFlags = isRight ? control.right : control.left;
        const controlActive = isRight ? control.rightActive : control.leftActive;
        const index = controlActive.indexOf(pos);
        if (index === -1) {
            controlActive.push(pos);
            controlActive.sort();
            this.dataState[controlTitle] = { ...control };
            this.setState({});
        } else { // 当前控制位已激活
            controlActive.splice(index, 1);
            if (controlFlags[pos].length) { // 清空当前牙位信息
                const flags = isRight ? target.flags.right : target.flags.left;
                for (const row of controlFlags[pos]) {
                    const tmpIndex = flags[row].indexOf(pos);
                    if (tmpIndex !== -1) {
                        flags[row].splice(tmpIndex, 1);
                    }
                }
                controlFlags[pos] = [];
                this.dataState[targetTitle] = { ...target };
                this.dataState[controlTitle] = { ...control };
                this.setState({});
            } else {
                this.dataState[controlTitle] = { ...control };
                this.setState({});
            }
        }
    }

    /** 根据title从state中提取牙位信息 */
    private extractStateByTitle(title: string): [] | [
        IToothFacing,
        string,
        IToothControl,
        string
    ] {
        let target: IToothFacing | null = null;
        let targetTitle = '';
        let control: IToothControl | null = null;
        let controlTitle = '';
        switch (title) {
            case 'controlMaxilla':
            case 'maxilla': {
                const { maxilla, controlMaxilla } = this.dataState;
                target = maxilla;
                targetTitle = 'maxilla';
                control = controlMaxilla;
                controlTitle = 'controlMaxilla';
                break;
            }
            case 'controlMandible':
            case 'mandible': {
                const { mandible, controlMandible } = this.dataState;
                target = mandible;
                targetTitle = 'mandible';
                control = controlMandible;
                controlTitle = 'controlMandible';
                break;
            }
            default: return [];
        }
        return [target, targetTitle, control, controlTitle];
    }

    private generateSvgHtml(struct: IToothStruct) {
        const { svgBuilder } = this;
        if (!svgBuilder) return {
            html: '',
            width: 0,
            height: 0,
        };
        // 构造字符串， 区分leftTop, rightTop, rightBottom, leftBottom
        const info = {
            leftTop: svgBuilder.buildSvgHtml(struct.control.maxilla.right),
            rightTop: svgBuilder.buildSvgHtml(struct.control.maxilla.left, true),
            rightBottom: svgBuilder.buildSvgHtml(struct.control.mandible.left, true),
            leftBottom: svgBuilder.buildSvgHtml(struct.control.mandible.right),
        }

        // 组装svg
        // 距中线边距
        const space = 5;
        const lineWidth = 1;
        const fullSpace = space * 2 + lineWidth;
        // 计算纵向中线 x
        // 最小宽度： 60
        const minWidth = 40;
        const leftWidth = Math.max(info.leftTop.width, info.leftBottom.width, minWidth);
        const rightWidth = Math.max(info.rightTop.width, info.rightBottom.width, minWidth);
        const xMiddle = leftWidth + space + lineWidth;
        const leftMiddle = leftWidth / 2;
        const rightMiddle = rightWidth / 2;
        const rightTransBase = leftWidth + fullSpace;

        // 计算横向中线 y
        // 最小高度： 16
        const minHeight = 16;
        const topHeight = Math.max(info.leftTop.height, info.rightTop.height, minHeight);
        const bottomHeight = Math.max(info.leftBottom.height, info.rightBottom.height, minHeight);
        const yMiddle = topHeight + space + lineWidth;


        // 图片边距为10
        const padding = 10;
        const svgWidth = rightTransBase + rightWidth + padding * 2;
        const svgHeight = topHeight + fullSpace + bottomHeight + padding * 2;
        // 将结构信息压在svg绑定属性中
        let svgHtml = `<svg data-source="${JSON.stringify(struct).replace(/"/g, "'")}" width="${svgWidth}" height="${svgHeight}" xmlns="http://www.w3.org/2000/svg" version="1.1"><g transform="translate(${padding}, ${padding})">`
        {
            // leftTop
            let startX = 0;
            startX += (leftMiddle - info.leftTop.width / 2);
            svgHtml += `<g transform="translate(${startX.toFixed(2)}, 0)">${info.leftTop.html}</g>`;
        }
        {
            // rightTop
            let startX = rightTransBase;
            startX += (rightMiddle - info.rightTop.width / 2);
            svgHtml += `<g transform="translate(${startX.toFixed(2)}, 0)">${info.rightTop.html}</g>`;
        }
        {
            // leftBottom
            let startX = 0;
            startX += (leftMiddle - info.leftBottom.width / 2);
            svgHtml += `<g transform="translate(${startX.toFixed(2)}, ${topHeight + fullSpace - space / 2})">${info.leftBottom.html}</g>`
        }
        {
            // rightBottom
            let startX = rightTransBase;
            startX += (rightMiddle - info.rightBottom.width / 2);
            svgHtml += `<g transform="translate(${startX.toFixed(2)}, ${topHeight + fullSpace - space / 2})">${info.rightBottom.html}</g>`
        }
        svgHtml += `<g style="stroke: black; stroke-width: ${lineWidth};">
                <line x1="0" y1="${yMiddle}" x2="${rightTransBase + rightWidth}" y2="${yMiddle}" />
                <line x1="${xMiddle}" y1="0" x2="${xMiddle}" y2="${topHeight + bottomHeight + fullSpace}" />
            </g>
            </g></svg>
        `;
        return {
            width: svgWidth,
            height: svgHeight,
            html: svgHtml
        };
    }

    private toggleItem(list: number[], target: number) {
        const index = list.indexOf(target);
        if (index === -1) {
            list.push(target);
            list.sort();
        } else {
            list.splice(index, 1);
        }
    }
}


export function measureText(node: SVGTextElement, text: string, style: {
    fontSize: number;
    fontFamily: string;
    fontStyle: string;
}): ToothMeasureMap {
    const map = new Map();
    node.style.fontFamily = style.fontFamily;
    node.style.fontSize = style.fontSize + 'px';
    node.textContent = text;
    for (let i = 0, len = text.length; i < len; i++) {
        const rect = node.getExtentOfChar(i);
        map.set(text[i], {
            width: rect.width,
            height: rect.height,
            ...style,
        });
    }
    return map;
}