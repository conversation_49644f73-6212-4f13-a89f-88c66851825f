import { XmlComponent } from '../xml-components';
import { ParagraphProperties } from './properties';
import { TextRun, Run } from './run';
import { IIndentAttributesProperties, Indent } from './formatting/indent';
import { Alignment, AlignmentOptions, Numbered } from './formatting/alignment';
import { AlignType, STD_START_DEFAULT, STD_END_DEFAULT, NewControlType,
  INewControlProperty} from '../../../../common/commonDefines';
import { Spacing } from './formatting/spacing';
import { NisSignAuthor, PageBreak, WordWrap, ParaHidden } from './formatting/pageBreak';
// tslint:disable-next-line: max-line-length
import { ContentControlStart, ContentControlEnd, IContentControlAttributesProperties, Sdt, SectionStart, SectionEnd, Section } from './contentControl/contentControl';
// tslint:disable-next-line: max-line-length
import { IContentControlStartElementVals, IContentControlEndElementVals } from './contentControl/contentControlElements';
import { RunProperties } from './run/properties';
import { Revision } from './revision/revision';
import { WriteStruct} from '@/common/struct/write';
import { XmlComment } from './comment/comment';

export class Paragraph extends XmlComponent {
  // private readonly properties: ParagraphProperties;
  private properties: ParagraphProperties;

  constructor(text?: string) {
    super('w:p');

    // need condition to decide if properties exist
    // <w:p>
    //     <w:r></w:r>
    //     <w:r></w:r>
    // </w:p>
    // this.properties = new ParagraphProperties();
    // this.root.push(this.properties);

    if (text !== undefined) {
      // console.log("para has text: ", text)
      this.root.push(new TextRun(text));
    }
  }

  public addComment(comment: XmlComment): Paragraph {
    this.root.push(comment);
    return this;
  }

  public addRun(run: Run): Paragraph {
    this.root.push(run);
    return this;
  }

  public addRevision(revision: Revision): Paragraph {
    this.root.push(revision);
    return this;
  }

  public addNumbered(numId: number, type: number): Paragraph {
    this.root.push(new Numbered(numId, type));
    return this;
  }

  public addContentControlStart(attrs: IContentControlAttributesProperties, portionProp: RunProperties,
                                contentControlStartElementVals: IContentControlStartElementVals,
                                showPlaceholder: boolean = false, bSignatureBox: boolean = false): Paragraph {

    // tslint:disable-next-line: max-line-length
    const {serialNumber, placeholder, helpTip, isMustFill, deleteProtect, editProtect, copyProtect, showBorder, borderString,
      editReverse, backgroundColorHidden, customProperty, tabJump, newControlHidden, showRight,
      secretType, fixedLength, maxLength, title, hideHasTitle,
      minValue, maxValue, precision, unit, forceValidate,
      retrieve, selectPrefixContent, prefixContent, separator, itemList, showValue,
      dateBoxFormat, customFormat, startDate, endDate, dateTime,
      checked, printSelected, label, labelCode, // checkbox
      showType, spaceNum, supportMultLines, // radiobox
      signatureCount, preText, signatureSeparator, postText, signaturePlaceholder, // signature box
      signatureRatio, rowHeightRestriction, // signature box
      bTextBorder, group, identifier,
    } = contentControlStartElementVals;

    const bRadioButton = attrs.type === NewControlType.RadioButton || attrs.type === NewControlType.MultiRadio;
    // TODO: alleviate attrs?
    const contentControlStart = new ContentControlStart(attrs, portionProp);

    // if values are default, do not save them
    if (serialNumber !== STD_START_DEFAULT.serialNumber) {
      contentControlStart.addSerialNumber(serialNumber);
    }

    // tslint:disable-next-line: triple-equals
    if (identifier != STD_START_DEFAULT.identifier) {
      contentControlStart.addIdentifier(identifier);
    }

    if (placeholder !== STD_START_DEFAULT.placeholder) {
      contentControlStart.addPlaceHolder(placeholder);
    }
    if (helpTip != null && helpTip !== STD_START_DEFAULT.helpTip) { // val passed undefined, should be bug
      contentControlStart.addHelpTip(helpTip);
    }
    if (isMustFill !== STD_START_DEFAULT.isMustFill) {
      contentControlStart.addIsMustFill(isMustFill + '');
    }
    if (deleteProtect !== STD_START_DEFAULT.deleteProtect) {
      contentControlStart.addDeleteProtect(deleteProtect + '');
    }
    if (editProtect !== STD_START_DEFAULT.editProtect) {
      contentControlStart.addEditProtect(editProtect + '');
    }
    if (bTextBorder != null) {
      contentControlStart.addTextBorder(bTextBorder + '');
    }
    if (copyProtect !== STD_START_DEFAULT.copyProtect) {
      contentControlStart.addCopyProtect(copyProtect + '');
    }
    if (showBorder !== STD_START_DEFAULT.showBorder) {
      contentControlStart.addShowBorder(showBorder + '');
    }
    if (borderString !== STD_START_DEFAULT.borderString) {
      contentControlStart.addBorderString(borderString);
    }
    if (editReverse !== STD_START_DEFAULT.editReverse) {
      contentControlStart.addEditReverse(editReverse + '');
    }
    if (backgroundColorHidden !== STD_START_DEFAULT.backgroundColorHidden) {
      contentControlStart.addBackgroundColorHidden(backgroundColorHidden + '');
    }
    if (customProperty instanceof Map && customProperty.size > 0) { // default empty map
      contentControlStart.addCustomProperty(customProperty);
    }
    if (tabJump !== STD_START_DEFAULT.tabJump) {
      contentControlStart.addTabJump(tabJump + '');
    }
    if (newControlHidden !== STD_START_DEFAULT.newControlHidden) {
      contentControlStart.addNewControlHidden(newControlHidden + '');
    }

    // ui prop
    let showPlaceholderTemp = (showPlaceholder === true) ? 1 : 0;
    if (bSignatureBox === true) {
      // signaturebox's sub text struts would remove content and show ph
      showPlaceholderTemp = 1;
    }
    if (showPlaceholderTemp !== STD_START_DEFAULT.showPlaceholder) {
      contentControlStart.addShowPlaceholder(showPlaceholderTemp + '');
    }

    // extent props
    if (attrs.type === NewControlType.TextBox || attrs.type === NewControlType.Section ) {
      if ( attrs.type === NewControlType.TextBox ) {
        if (secretType !== STD_START_DEFAULT.secretType) {
          contentControlStart.addSecretType(secretType + '');
        }
        if (fixedLength !== STD_START_DEFAULT.fixedLength) {
          contentControlStart.addFixedLength(fixedLength + '');
        }
        if (maxLength !== STD_START_DEFAULT.maxLength) {
          contentControlStart.addMaxLength(maxLength + '');
        }
        if (hideHasTitle !== STD_START_DEFAULT.hideHasTitle) {
          contentControlStart.addHideHasTitle(hideHasTitle + '');
        }
        if (printSelected !== STD_START_DEFAULT.printSelected) {
          contentControlStart.addPrintSelected(printSelected + '');
        }
      }

      if (title !== STD_START_DEFAULT.title) {
        contentControlStart.addTitle(title + '');
      }
    } else if (attrs.type === NewControlType.NumberBox) {
      if (secretType !== STD_START_DEFAULT.secretType) { // shared
        contentControlStart.addSecretType(secretType + '');
      }
      if (minValue != null && minValue !== STD_START_DEFAULT.minValue) {
        contentControlStart.addMinValue(minValue + '');
      }
      if (maxValue != null && maxValue !== STD_START_DEFAULT.maxValue) { 
        contentControlStart.addMaxValue(maxValue + '');
      }
      if (precision != null && precision !== STD_START_DEFAULT.precision) {
        contentControlStart.addPrecision(precision + '');
      }
      if (unit !== STD_START_DEFAULT.unit) {
        contentControlStart.addUnit(unit);
      }
      if (forceValidate !== STD_START_DEFAULT.forceValidate) {
        contentControlStart.addForceValidate(forceValidate + '');
      }
      if (printSelected !== STD_START_DEFAULT.printSelected) {
        contentControlStart.addPrintSelected(printSelected + '');
      }
    } else if (attrs.type === NewControlType.Combox || attrs.type === NewControlType.MultiCombox ||
      attrs.type === NewControlType.ListBox || attrs.type === NewControlType.MultiListBox) {

      if (retrieve !== STD_START_DEFAULT.retrieve) {
        contentControlStart.addRetrieve(retrieve + '');
      }
      if (selectPrefixContent !== STD_START_DEFAULT.selectPrefixContent) {
        contentControlStart.addSelectPrefixContent(selectPrefixContent);
      }
      if (prefixContent !== STD_START_DEFAULT.prefixContent) {
        contentControlStart.addPrefixContent(prefixContent);
      }
      if (separator !== STD_START_DEFAULT.separator) {
        contentControlStart.addSeparator(separator);
      }
      if (showValue !== STD_START_DEFAULT.showValue) {
        contentControlStart.addShowValue(showValue + '');
      }

      if (hideHasTitle !== STD_START_DEFAULT.hideHasTitle) {
        contentControlStart.addHideHasTitle(hideHasTitle + '');
      }

      if (title !== STD_START_DEFAULT.title) {
        contentControlStart.addTitle(title + '');
      }

      if (itemList.length > 0) {
        contentControlStart.addItemList(itemList);
      }
      if (printSelected !== STD_START_DEFAULT.printSelected) {
        contentControlStart.addPrintSelected(printSelected + '');
      }

    } else if (attrs.type === NewControlType.DateTimeBox) {
      if (dateBoxFormat !== STD_START_DEFAULT.dateBoxFormat) {
        contentControlStart.addDateBoxFormat(dateBoxFormat + '');
      }
      // also rule out {}
      if (customFormat !== STD_START_DEFAULT.customFormat) {
        if (customFormat instanceof Object && Object.keys(customFormat).length > 0) {
          contentControlStart.addCustomFormat(JSON.stringify(customFormat));
        }
      }
      if (startDate !== STD_START_DEFAULT.startDate) {
        contentControlStart.addStartDate(startDate);
      }
      if (endDate !== STD_START_DEFAULT.endDate) {
        contentControlStart.addEndDate(endDate);
      }
      // datetime: iso string
      if (dateTime !== STD_START_DEFAULT.dateTime) {
        // this is old, no need to add
      }
      if (printSelected !== STD_START_DEFAULT.printSelected) {
        contentControlStart.addPrintSelected(printSelected + '');
      }

    } else if (bRadioButton) {
      if (showRight !== STD_START_DEFAULT.showRight) {
        contentControlStart.addShowRight(showRight + '');
      }
      if (printSelected !== STD_START_DEFAULT.printSelected) {
        contentControlStart.addPrintSelected(printSelected + '');
      }
      if (itemList.length > 0) {
        contentControlStart.addItemList(itemList);
      }
      if (showType !== STD_START_DEFAULT.showType) {
        // why here can have 'NaN'?
        if (isNaN(showType) === false) {
          contentControlStart.addShowType(showType + '');
        }
      }
      if (spaceNum !== STD_START_DEFAULT.spaceNum) {
        contentControlStart.addSpaceNum(spaceNum + '');
      }
      if (supportMultLines !== STD_START_DEFAULT.supportMultLines) {
        contentControlStart.addSupportMultLines(supportMultLines + '');
      }

    } else if (attrs.type === NewControlType.CheckBox) {
      if (showRight !== STD_START_DEFAULT.showRight) {
        contentControlStart.addShowRight(showRight + '');
      }
      if (checked !== STD_START_DEFAULT.checked) {
        contentControlStart.addChecked(checked + '');
      }
      if (printSelected !== STD_START_DEFAULT.printSelected) {
        contentControlStart.addPrintSelected(printSelected + '');
      }
      if (label !== STD_START_DEFAULT.label) {
        contentControlStart.addLabel(label);
      }
      if (labelCode !== STD_START_DEFAULT.labelCode) {
        contentControlStart.addLabelCode(labelCode);
      }
      if (group !== STD_START_DEFAULT.group) {
        contentControlStart.addGroup(group);
      }
    } else if (attrs.type === NewControlType.SignatureBox) {
      if (signatureCount !== STD_START_DEFAULT.signatureCount) {
        contentControlStart.addSignatureCount(signatureCount + '');
      }
      if (preText !== STD_START_DEFAULT.preText) {
        contentControlStart.addPreText(preText);
      }
      if (signatureSeparator !== STD_START_DEFAULT.signatureSeparator) {
        contentControlStart.addSignatureSeparator(signatureSeparator);
      }
      if (postText !== STD_START_DEFAULT.postText) {
        contentControlStart.addPostText(postText);
      }
      if (signaturePlaceholder !== STD_START_DEFAULT.signaturePlaceholder) {
        contentControlStart.addSignaturePlaceholder(signaturePlaceholder);
      }
      if (signatureRatio !== STD_START_DEFAULT.signatureRatio) {
        contentControlStart.addSignatureRatio(signatureRatio + '');
      }
      if (rowHeightRestriction !== STD_START_DEFAULT.rowHeightRestriction) {
        contentControlStart.addRowHeightRestriction(rowHeightRestriction + '');
      }

    } else {
      //
    }

    this.root.push(contentControlStart);
    return this;
  }

  public addContentControlEnd(attrs: IContentControlAttributesProperties, portionProp: RunProperties,
                              contentControlEndElementVals: IContentControlEndElementVals): Paragraph {

    const {borderString} = contentControlEndElementVals;

    // TODO: alleviate attrs?
    const contentControlEnd = new ContentControlEnd(attrs, portionProp);

    if (borderString !== STD_END_DEFAULT.borderString) {
      contentControlEnd.addBorderString(borderString);
    }

    this.root.push(contentControlEnd);
    return this;
  }

  public addSdt(attrs: IContentControlAttributesProperties, portionProp: RunProperties,
                contentControlElementVals: INewControlProperty,
                showPlaceholder: boolean = false, bSignatureBox: boolean = false): Sdt {

    // tslint:disable-next-line: max-line-length
    // const { serialNumber, placeholder, helpTip, isMustFill, deleteProtect, editProtect, copyProtect, showBorder, borderString,
    //   editReverse, backgroundColorHidden, customProperty, tabJump, newControlHidden, showRight,
    //   secretType, fixedLength, maxLength, title, hideHasTitle,
    //   minValue, maxValue, precision, unit, forceValidate,
    //   retrieve, selectPrefixContent, prefixContent, separator, itemList, showValue,
    //   dateBoxFormat, customFormat, startDate, endDate, dateTime,
    //   checked, printSelected, label, labelCode, // checkbox
    //   showType, spaceNum, // radiobox
    //   signatureCount, preText, signatureSeparator, postText, signaturePlaceholder, // signature box
    //   signatureRatio, rowHeightRestriction, // signature box
    //   hierarchy, province, city, county, // address box
    //   cascade,
    //   bTextBorder,
    //   group,
    //   bShowCodeAndValue,
    //   signType, alwaysShow, showSignBorder, identifier,
    //   alignments, eventInfo, codeLabel, valueLabel,
    // } = contentControlElementVals;

    // TODO: alleviate attrs?
    const sdt = new Sdt(attrs, portionProp);
    const struct = new WriteStruct(contentControlElementVals, sdt);
    // const bRadioButton = attrs.type === NewControlType.RadioButton || attrs.type === NewControlType.MultiRadio;

    // // if values are default, do not save them
    // if (serialNumber !== STD_START_DEFAULT.serialNumber) {
    //   sdt.addSerialNumber(serialNumber);
    // }

    // if (codeLabel !== undefined) {
    //   sdt.addCodeLabel(codeLabel);
    // }
    // if (valueLabel !== undefined) {
    //   sdt.addValueLabel(valueLabel);
    // }

    // // tslint:disable-next-line: triple-equals
    // if (identifier != STD_START_DEFAULT.identifier) {
    //   sdt.addIdentifier(identifier);
    // }

    // if (placeholder !== STD_START_DEFAULT.placeholder) {
    //   sdt.addPlaceHolder(placeholder);
    // }
    // if (helpTip != null && helpTip !== STD_START_DEFAULT.helpTip) { // val passed undefined, should be bug
    //   sdt.addHelpTip(helpTip);
    // }
    // if (isMustFill !== STD_START_DEFAULT.isMustFill) {
    //   sdt.addIsMustFill(isMustFill + '');
    // }
    // if (deleteProtect !== STD_START_DEFAULT.deleteProtect) {
    //   sdt.addDeleteProtect(deleteProtect + '');
    // }
    // if (bShowCodeAndValue !== STD_START_DEFAULT.bShowCodeAndValue) {
    //   sdt.addShowCodeAndValue(bShowCodeAndValue + '');
    // }
    // if (editProtect !== STD_START_DEFAULT.editProtect) {
    //   sdt.addEditProtect(editProtect + '');
    // }
    // if (bTextBorder != null) {
    //   sdt.addTextBorder(bTextBorder + '');
    // }
    // if (copyProtect !== STD_START_DEFAULT.copyProtect) {
    //   sdt.addCopyProtect(copyProtect + '');
    // }
    // if (cascade && cascade.length > 0) {
    //   sdt.addLogicEvent(JSON.stringify(cascade));
    // }
    // if (showBorder !== STD_START_DEFAULT.showBorder) {
    //   sdt.addShowBorder(showBorder + '');
    // }
    // if (borderString !== STD_START_DEFAULT.borderString) {
    //   sdt.addBorderString(borderString);
    // }
    // if (editReverse !== STD_START_DEFAULT.editReverse) {
    //   sdt.addEditReverse(editReverse + '');
    // }
    // if (backgroundColorHidden !== STD_START_DEFAULT.backgroundColorHidden) {
    //   sdt.addBackgroundColorHidden(backgroundColorHidden + '');
    // }
    // if (customProperty instanceof Map && customProperty.size > 0) { // default empty map
    //   sdt.addCustomProperty(customProperty);
    // }
    // if (tabJump !== STD_START_DEFAULT.tabJump) {
    //   sdt.addTabJump(tabJump + '');
    // }
    // if (newControlHidden !== STD_START_DEFAULT.newControlHidden) {
    //   sdt.addNewControlHidden(newControlHidden + '');
    // }

    // // ui prop
    // let showPlaceholderTemp = (showPlaceholder === true) ? 1 : 0;
    // if (bSignatureBox === true) {
    //   // signaturebox's sub text struts would remove content and show ph
    //   showPlaceholderTemp = 1;
    // }
    // if (showPlaceholderTemp !== STD_START_DEFAULT.showPlaceholder) {
    //   sdt.addShowPlaceholder(showPlaceholderTemp + '');
    // }

    // if (typeof alignments === 'number' && alignments !== STD_START_DEFAULT.alignments) {
    //   sdt.addAlignments(ALIGN_TYPE[alignments]);
    // }

    // if (typeof eventInfo === 'object' && eventInfo) {
    //   try {
    //     sdt.addEventInfo(JSON.stringify(eventInfo));
    //   } catch (error) {
    //     // tslint:disable-next-line: no-console
    //     console.error(error);
    //   }
    // }

    // if (fixedLength !== STD_START_DEFAULT.fixedLength && typeof fixedLength === 'number') {
    //   sdt.addFixedLength(fixedLength + '');
    // }

    // // extent props
    // if (attrs.type === NewControlType.TextBox || attrs.type === NewControlType.Section) {
    //   if (attrs.type === NewControlType.TextBox) {
    //     if (secretType !== STD_START_DEFAULT.secretType) {
    //       sdt.addSecretType(secretType + '');
    //     }

    //     if (maxLength !== STD_START_DEFAULT.maxLength) {
    //       sdt.addMaxLength(maxLength + '');
    //     }
    //     if (hideHasTitle !== STD_START_DEFAULT.hideHasTitle) {
    //       sdt.addHideHasTitle(hideHasTitle + '');
    //     }
    //   }

    //   if (title !== STD_START_DEFAULT.title) {
    //     sdt.addTitle(title + '');
    //   }
    // } else if (attrs.type === NewControlType.NumberBox) {
    //   if (secretType !== STD_START_DEFAULT.secretType) { // shared
    //     sdt.addSecretType(secretType + '');
    //   }
    //   if (minValue !== STD_START_DEFAULT.minValue) {
    //     sdt.addMinValue(minValue + '');
    //   }
    //   if (maxValue != null && maxValue !== STD_START_DEFAULT.maxValue) { 
    //   sdt.addMaxValue(maxValue + '');
    //   }
    //   if (precision !== STD_START_DEFAULT.precision) {
    //     sdt.addPrecision(precision + '');
    //   }
    //   if (unit !== STD_START_DEFAULT.unit) {
    //     sdt.addUnit(unit);
    //   }
    //   if (forceValidate !== STD_START_DEFAULT.forceValidate) {
    //     sdt.addForceValidate(forceValidate + '');
    //   }
    // } else if (attrs.type === NewControlType.Combox || attrs.type === NewControlType.MultiCombox ||
    //   attrs.type === NewControlType.ListBox || attrs.type === NewControlType.MultiListBox) {

    //   if (retrieve !== STD_START_DEFAULT.retrieve) {
    //     sdt.addRetrieve(retrieve + '');
    //   }
    //   if (selectPrefixContent !== STD_START_DEFAULT.selectPrefixContent) {
    //     sdt.addSelectPrefixContent(selectPrefixContent);
    //   }
    //   if (prefixContent !== STD_START_DEFAULT.prefixContent) {
    //     sdt.addPrefixContent(prefixContent);
    //   }

    //   if (hideHasTitle !== STD_START_DEFAULT.hideHasTitle) {
    //     sdt.addHideHasTitle(hideHasTitle + '');
    //   }

    //   if (title !== STD_START_DEFAULT.title) {
    //     sdt.addTitle(title + '');
    //   }
    //   if (separator !== STD_START_DEFAULT.separator) {
    //     sdt.addSeparator(separator);
    //   }
    //   if (showValue !== STD_START_DEFAULT.showValue) {
    //     sdt.addShowValue(showValue + '');
    //   }

    //   if (itemList.length > 0) {
    //     sdt.addItemList(itemList);
    //   }

    // } else if (attrs.type === NewControlType.DateTimeBox) {
    //   if (dateBoxFormat !== STD_START_DEFAULT.dateBoxFormat) {
    //     sdt.addDateBoxFormat(dateBoxFormat + '');
    //   }
    //   // also rule out {}
    //   if (customFormat !== STD_START_DEFAULT.customFormat) {
    //     if (customFormat instanceof Object && Object.keys(customFormat).length > 0) {
    //       sdt.addCustomFormat(JSON.stringify(customFormat));
    //     }
    //   }
    //   if (startDate !== STD_START_DEFAULT.startDate) {
    //     sdt.addStartDate(startDate);
    //   }
    //   if (endDate !== STD_START_DEFAULT.endDate) {
    //     sdt.addEndDate(endDate);
    //   }
    //   // datetime: iso string
    //   if (dateTime !== STD_START_DEFAULT.dateTime) {
    //     sdt.addDateTime(dateTime);
    //   }

    // } else if (bRadioButton) {
    //   if (showRight !== STD_START_DEFAULT.showRight) {
    //     sdt.addShowRight(showRight + '');
    //   }
    //   if (printSelected !== STD_START_DEFAULT.printSelected) {
    //     sdt.addPrintSelected(printSelected + '');
    //   }
    //   if (itemList.length > 0) {
    //     sdt.addItemList(itemList);
    //   }
    //   if (showType !== STD_START_DEFAULT.showType) {
    //     // why here can have 'NaN'?
    //     if (isNaN(showType) === false) {
    //       sdt.addShowType(showType + '');
    //     }
    //   }
    //   if (spaceNum !== STD_START_DEFAULT.spaceNum) {
    //     sdt.addSpaceNum(spaceNum + '');
    //   }

    // } else if (attrs.type === NewControlType.CheckBox) {
    //   if (showRight !== STD_START_DEFAULT.showRight) {
    //     sdt.addShowRight(showRight + '');
    //   }
    //   if (checked !== STD_START_DEFAULT.checked) {
    //     sdt.addChecked(checked + '');
    //   }
    //   if (printSelected !== STD_START_DEFAULT.printSelected) {
    //     sdt.addPrintSelected(printSelected + '');
    //   }
    //   if (label !== STD_START_DEFAULT.label) {
    //     sdt.addLabel(label);
    //   }
    //   if (labelCode !== STD_START_DEFAULT.labelCode) {
    //     sdt.addLabelCode(labelCode);
    //   }
    //   if (group) {
    //     sdt.addGroup(group);
    //   }

    // } else if (attrs.type === NewControlType.SignatureBox) {
    //   if (signatureCount !== STD_START_DEFAULT.signatureCount) {
    //     sdt.addSignatureCount(signatureCount + '');
    //   }
    //   if (preText !== STD_START_DEFAULT.preText) {
    //     sdt.addPreText(preText);
    //   }
    //   if (signatureSeparator !== STD_START_DEFAULT.signatureSeparator) {
    //     sdt.addSignatureSeparator(signatureSeparator);
    //   }
    //   if (postText !== STD_START_DEFAULT.postText) {
    //     sdt.addPostText(postText);
    //   }
    //   if (signaturePlaceholder !== STD_START_DEFAULT.signaturePlaceholder) {
    //     sdt.addSignaturePlaceholder(signaturePlaceholder);
    //   }
    //   if (signatureRatio !== STD_START_DEFAULT.signatureRatio) {
    //     sdt.addSignatureRatio(signatureRatio + '');
    //   }
    //   if (rowHeightRestriction !== STD_START_DEFAULT.rowHeightRestriction) {
    //     sdt.addRowHeightRestriction(rowHeightRestriction + '');
    //   }
    //   if (signType !== STD_START_DEFAULT.signType) {
    //     sdt.addSignType(signType + '');
    //   }
    //   if (alwaysShow !== STD_START_DEFAULT.alwaysShow) {
    //     sdt.addAlwaysShow(alwaysShow + '');
    //   }
    //   if (showSignBorder !== STD_START_DEFAULT.showSignBorder) {
    //     sdt.addShowSignBorder(showSignBorder + '');
    //   }

    // } else if (attrs.type === NewControlType.AddressBox) {
    //   if (hierarchy != null) { // save the lvl will be more contextual
    //     sdt.addHierarchy(hierarchy + '');
    //   }
    //   if (province != STD_START_DEFAULT.province) {
    //     sdt.addProvince(JSON.stringify(province));
    //   }
    //   if (province != STD_START_DEFAULT.city) {
    //     sdt.addCity(JSON.stringify(city));
    //   }
    //   if (province != STD_START_DEFAULT.county) {
    //     sdt.addCounty(JSON.stringify(county));
    //   }
    // } else {
    //   //
    // }

    // new sdt (TODO: need the container always?)
    sdt.addSdtContent();

    this.root.push(sdt);
    return sdt;
  }

  public addSdt2(attrs: IContentControlAttributesProperties, portionProp: RunProperties,
                 contentControlElementVals: INewControlProperty,
                 showPlaceholder: boolean = false, bSignatureBox: boolean = false): Sdt {
    const sdt = new Sdt(attrs, portionProp);
    const struct = new WriteStruct(contentControlElementVals, sdt);

    sdt.addSdtContent();

    this.root.push(sdt);
    return sdt;
  }

  public addSectionStart(attrs: IContentControlAttributesProperties, portionProp: RunProperties,
                         contentControlStartElementVals: INewControlProperty, borderString: string): Paragraph {

    // tslint:disable-next-line: max-line-length
    // const { serialNumber, placeholder, helpTip, isMustFill, deleteProtect, editProtect, copyProtect, showBorder, borderString,
    //   editReverse, backgroundColorHidden, customProperty, tabJump, newControlHidden, showRight,
    //   secretType, fixedLength, maxLength, title,
    //   bTextBorder, identifier,
    // } = contentControlStartElementVals;

    // TODO: alleviate attrs?
    const contentControlStart = new SectionStart(attrs, portionProp);
    if (borderString !== STD_START_DEFAULT.borderString) {
      contentControlStart.addBorderString(borderString);
    }
    const struct = new WriteStruct(contentControlStartElementVals, contentControlStart);
    // if values are default, do not save them
    // if (serialNumber !== STD_START_DEFAULT.serialNumber) {
    //   contentControlStart.addSerialNumber(serialNumber);
    // }

    // if (identifier !== STD_START_DEFAULT.identifier) {
    //   contentControlStart.addIdentifier(identifier);
    // }

    // if (placeholder !== STD_START_DEFAULT.placeholder) {
    //   contentControlStart.addPlaceHolder(placeholder);
    // }
    // if (helpTip != null && helpTip !== STD_START_DEFAULT.helpTip) { // val passed undefined, should be bug
    //   contentControlStart.addHelpTip(helpTip);
    // }
    // if (isMustFill !== STD_START_DEFAULT.isMustFill) {
    //   contentControlStart.addIsMustFill(isMustFill + '');
    // }
    // if (deleteProtect !== STD_START_DEFAULT.deleteProtect) {
    //   contentControlStart.addDeleteProtect(deleteProtect + '');
    // }
    // if (editProtect !== STD_START_DEFAULT.editProtect) {
    //   contentControlStart.addEditProtect(editProtect + '');
    // }
    // if (bTextBorder != null) {
    //   contentControlStart.addTextBorder(bTextBorder + '');
    // }
    // if (copyProtect !== STD_START_DEFAULT.copyProtect) {
    //   contentControlStart.addCopyProtect(copyProtect + '');
    // }
    // if (showBorder !== STD_START_DEFAULT.showBorder) {
    //   contentControlStart.addShowBorder(showBorder + '');
    // }
    // if (borderString !== STD_START_DEFAULT.borderString) {
    //   contentControlStart.addBorderString(borderString);
    // }
    // if (editReverse !== STD_START_DEFAULT.editReverse) {
    //   contentControlStart.addEditReverse(editReverse + '');
    // }
    // if (backgroundColorHidden !== STD_START_DEFAULT.backgroundColorHidden) {
    //   contentControlStart.addBackgroundColorHidden(backgroundColorHidden + '');
    // }
    // if (customProperty instanceof Map && customProperty.size > 0) { // default empty map
    //   contentControlStart.addCustomProperty(customProperty);
    // }
    // if (tabJump !== STD_START_DEFAULT.tabJump) {
    //   contentControlStart.addTabJump(tabJump + '');
    // }
    // if (newControlHidden !== STD_START_DEFAULT.newControlHidden) {
    //   contentControlStart.addNewControlHidden(newControlHidden + '');
    // }

    // // ui prop
    // let showPlaceholderTemp = (showPlaceholder === true) ? 1 : 0;
    // if (bSignatureBox === true) {
    //   // signaturebox's sub text struts would remove content and show ph
    //   showPlaceholderTemp = 1;
    // }
    // if (showPlaceholderTemp !== STD_START_DEFAULT.showPlaceholder) {
    //   contentControlStart.addShowPlaceholder(showPlaceholderTemp + '');
    // }

    // // extent props
    // if (attrs.type === NewControlType.TextBox || attrs.type === NewControlType.Section) {
    //   if (attrs.type === NewControlType.TextBox) {
    //     if (secretType !== STD_START_DEFAULT.secretType) {
    //       contentControlStart.addSecretType(secretType + '');
    //     }
    //     if (fixedLength !== STD_START_DEFAULT.fixedLength) {
    //       contentControlStart.addFixedLength(fixedLength + '');
    //     }
    //     if (maxLength !== STD_START_DEFAULT.maxLength) {
    //       contentControlStart.addMaxLength(maxLength + '');
    //     }
    //   }

    //   if (title !== STD_START_DEFAULT.title) {
    //     contentControlStart.addTitle(title + '');
    //   }
    // } else {
    //   //
    // }

    this.root.push(contentControlStart);
    return this;
  }

  public addSectionEnd(attrs: IContentControlAttributesProperties, portionProp: RunProperties,
                       borderString: string): Paragraph {

    // const { borderString } = contentControlEndElementVals;

    // TODO: alleviate attrs?
    const contentControlEnd = new SectionEnd(attrs, portionProp);

    if (borderString !== STD_END_DEFAULT.borderString) {
      contentControlEnd.addBorderString(borderString);
    }

    this.root.push(contentControlEnd);
    return this;
  }

  public addSection(
    attrs: IContentControlAttributesProperties, portionProp: RunProperties,
    contentControlElementVals: INewControlProperty
    ): Section {
    const section = new Section(attrs, portionProp);
    const struct = new WriteStruct(contentControlElementVals, section);

    section.addSectionContent();

    this.root.push(section);
    return section;
  }

  public createTextRun(text: string): TextRun {
    const run = new TextRun(text);
    this.addRun(run);
    return run;
  }

  public indent(attrs: IIndentAttributesProperties): Paragraph {
    this.initializeProperties();

    this.properties.push(new Indent(attrs));
    return this;
  }

  public pageBreak(): Paragraph {
    this.initializeProperties();

    this.properties.push(new PageBreak());
    return this;
  }

  public alignment(alignType: number): Paragraph {
    this.initializeProperties();

    let alignmentOption = AlignmentOptions.BOTH; // default to be justified
    switch (alignType) {
      case AlignType.Justify:
        alignmentOption = AlignmentOptions.BOTH;
        break;
      case AlignType.Left:
        alignmentOption = AlignmentOptions.LEFT;
        break;
      case AlignType.Right:
        alignmentOption = AlignmentOptions.RIGHT;
        break;
      case AlignType.Center:
        alignmentOption = AlignmentOptions.CENTER;
        break;
      default:
        break;
    }

    this.properties.push(new Alignment(alignmentOption));
    return this;
  }

  // paraspacing
  public paraSpacing(lineType: number, line: number): Paragraph {
    this.initializeProperties();

    this.properties.push(new Spacing({lineType, line}));
    return this;
  }

  // paraspacing
  public paraWesNewLine(wordWrap: number): Paragraph {
    this.initializeProperties();

    this.properties.push(new WordWrap(wordWrap));
    return this;
  }

  // nisSignAuthor
  public nisSignAuthor(nisSignAuthor: string): Paragraph {
    this.initializeProperties();

    this.properties.push(new NisSignAuthor(nisSignAuthor));
    return this;
  }

  public paraHidden(bHidden: boolean): Paragraph {
    if (bHidden) {
      this.initializeProperties();
  
      this.properties.push(new ParaHidden());
    }
    return this;
  }

  /**
   * initialize run properties if not
   */
  private initializeProperties(): void {
    if (!this.properties) {
      this.properties = new ParagraphProperties();

      // should be the first child element
      // this.root.push(this.properties);
      this.root.unshift(this.properties);
    }
  }

}
