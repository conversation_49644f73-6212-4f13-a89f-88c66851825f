import JSEncrypt from './jsencrypt.js';

function encrypto(data) {
    const PUBLIC_KEY = `MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtYnKJe8XfhGYwyvNmWQs
    fLnTp/5DSuwRr7hbSOmgMnxExmdecYukSJoLN0pCzTCO7D91/pH+4+bbyQ7Q2FiT
    L1n7Q7UVQaGMRgNQNlKKSDHe6ctiM347RevKfx+1ZjgRUS3CNTgAAYYGn63pnflE
    HM8hFrnk1K3N6fSbEm5aAytyicFfFXrVpWCSDx1dm7A5E3pA6dkLxJ848iYcG/mJ
    bR1b/4XJ60ylrNmxbnNHgSi6FlcnNkCbFZ/+8sRDD5SxAsirjlOKgeiPg1IPeK4T
    ICcHu9DuWd6lpChi5xjRT1a9w4q8LthTkpXlQe9fPaWFDzibSIS8CFr1bF2CGGEk
    wwIDAQAB
    `;
    const decrypt = new JSEncrypt();
    decrypt.setPublicKey(PUBLIC_KEY);
    const res = decrypt.encrypt(data);
    return res;
}

function decrypto(data) {
    const PRIVATE_KEY = `MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC1icol7xd+EZjD
    K82ZZCx8udOn/kNK7BGvuFtI6aAyfETGZ15xi6RImgs3SkLNMI7sP3X+kf7j5tvJ
    DtDYWJMvWftDtRVBoYxGA1A2UopIMd7py2IzfjtF68p/H7VmOBFRLcI1OAABhgaf
    remd+UQczyEWueTUrc3p9JsSbloDK3KJwV8VetWlYJIPHV2bsDkTekDp2QvEnzjy
    Jhwb+YltHVv/hcnrTKWs2bFuc0eBKLoWVyc2QJsVn/7yxEMPlLECyKuOU4qB6I+D
    Ug94rhMgJwe70O5Z3qWkKGLnGNFPVr3Dirwu2FOSleVB7189pYUPOJtIhLwIWvVs
    XYIYYSTDAgMBAAECggEAGROwne7aIusRXByH6SGCV7RfvXwNFaNoigqAaaEEJRUv
    duy+ihbNGc5VsEyMz8VeaNoXVZQbTqYMREjMpqYNpuPaJq80AX3sX5uDkA101qY0
    8bzLkutkCrOYpFjf+P/TvJACnNFghtVZwuMGO3E8bHJcUsRZDNv5btEAPtqJiphR
    SgqNGablckaO78zN0AMX60Ai+BA5Qm3CVMuSkmDQJnsrEwaTxVj55iS3taDZ4c6o
    ihHsNyS89aGTTdp4nNVv8hgKkK1C7nN1/V4lSW7eFwQ/J/hGZ89GfK7hlntdqRY4
    j8TjisBgnpnShErwCNAVfDRrxMhX294ztAz8yXK4gQKBgQDXyqvupfmeDsPIT0I4
    4XU4UdWr8RuP4l4zHjGc3M/RsIwHyzWcPFlf2l2xij+8sxxKsbx9ah3w4TwGCv3B
    G/PjQ9c7+fV/pfGhTpB7v0nsZLDWENm5z+e2QvcxlVabwEV+V78QyFjgruef81tQ
    dqMwx/reNWxLgGKDm4EWN0FpAwKBgQDXXTfn7quwE1/fFtFmBsDQ5oQHH7rfi4Zs
    bs7DkUynfyrVg4eNaAIVWbhgiRow1Y4i2jLuigXgAdCBOJmeDSInM8JylcIEFKaU
    mSOR3qg60BhVsBJepHEWWQmnB7M3x67g+R2qrOJFBfDrIBrZbItwvvj3qSKLgJaW
    Ow2oaPwpQQKBgESR4UpnMUeZV3saPGfItK/dyTDm36Q8AUTrDkbTH7J5EDYVy4ZG
    4vuDbwxQaupyW8YcblHH25XEbIdbDMFFOZ143DbxuaIDQCHKzjyUT1AvusV6+0SB
    HRccdmIqGyhI04xPI6aYky1qRq3b7aNG35pLoZrkjPH87ND3I/DERCOzAoGBAM15
    4x0mWihkU9RFsQPm7ZBiXxVs8J7YQCi0kBGHT9t/OR79nxcLZPF56LAyHwr4teQK
    yw1MIs7W7Fhd6DMj1LIScDNjLvk1urmMYqOKDHgQkycuFli2DC+GQ+9ZKWTO38tF
    40g0Kq4wPOwYdYV8So4HpwKkHJF83i/3p9BGWlABAoGAIIbBU5uv2IS28YBwuo37
    qJxDTMZQmGkTPLAi8d33dIeMlKoOzMMB9shi8DtKlrIYI9Suhr0ICd8gjOY/9HYJ
    i/TWMxpDkWKdXklfyxE8Uabb82GeM8jMOc20UuQSFVdtmbVwWUk1Ti5jCTA38dMv
    4aaB1lJjJPW/gie+l9xaZ80=`;
    const decrypt = new JSEncrypt();
    decrypt.setPrivateKey(PRIVATE_KEY);
    const res = decrypt.decrypt(data);
    return res;
}

window.onload = () => {
    const tarStr = encrypto('演示版,仅供测试，禁止商用');
    console.log(tarStr);
    const srcStr = decrypto(encrypto('演示版,仅供测试，禁止商用'));
    console.log(srcStr);
    console.log('[' + Array.from(tarStr, item => `'${item}'`).join(',') + ']');
}
