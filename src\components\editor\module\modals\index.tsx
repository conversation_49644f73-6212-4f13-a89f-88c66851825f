import * as React from 'react';
import { EmrEditor } from '../../Main';
import NewControlNum from './NewControlNumber';
import NewControlText from './NewControlText';
import NewComboBox from './NewComboBox';
import Font from './Font';
import Image from './Image';
import Paragraph, { EmptyParagraghRemove } from './Paragraph';
import {GlobalEvent as gEvent, GlobalEventName as gEventName} from '../../../../common/GlobalEvent';
import { ToolbarIndex, MenuItemIndex, NewControlType, CommentOperatorType, ResultType, COMMENT_FLAG } from '../../../../common/commonDefines';
import Char from './SpecialCharacter';
import DeleteCell from './DeleteCell';
import InsertTable from './InsertTable';
import PageSetting from './PageSetting';
import NewDateBox from './NewDateBox';
import NewControlCheck from './NewControlCheck';
import NewControlRadio from './NewControlRadio';
import ViewScale from './ViewScale';
import Region from './Region';
import PrintDialog from './PrintDialog';
import NewHeaderFooter from './NewHeaderFooter';
import TableSettingDialog from './TableSetting';
import NavMenu from './NavMenu';
import ImageUrlPanel from './ImageUrlPanel';
// import AttributePanel from '../panel/';
import NewPageNum from './NewPageNum';
import Revision, { RevisionAcceptReject } from './Revision';
import TableCellSplit from './SplitCell';
import ParaButton from './ParaButton';

import { DocumentCore } from '../../../../model/DocumentCore';
import AutoTest from './AutoTest';
import AutoTestPlay from './AutoTestPlay';
import NewSignatureBox from './NewSignatureBox';
import NewWatermark from './NewWatermark';
import NISTableSetting from './NISTableSetting';
import { NISTableCellSetting } from './NISTableCellSetting';
import NISTableCellText from './NISTableCellText';
import NISTableCellList from './NISTableCellList';
import NISTableCellDate from './NISTableCellDate';
import NISTableCellTime from './NISTableCellTime';
import NISTableCellSignature from './NISTableCellSignature';
import NISTableCellQuick from './NISTableCellQuick';
import { NISTableCellContentSetting } from './NISTableCellContentSetting';
import FormulaCalc from './FormulaCalc';
import VertAlignSetting from './VertAlignSetting';
import { getPxForMM } from '../../../../model/core/util';
import NISTableCellNumber from './NISTableCellNumber';
import NewAddressBox from './NewAddressBox';
import NISTableCellBP from './NISTableCellBP';
import MediaPanel from './Media';
import { AsyncLoad } from '../AsyncLoad';
// import Barcode from './Barcode';
import { RevisionPanelDialog } from '../panel/revisionPanel';
import {LoadModal} from './LoadModal';
import { MODAL_COMPONENT_NAME } from './Components';
import CommentPanelDialog from '../panel/commentPanel';
import VersionDialog from '../../menuDialog/VersionDlg';
import AICheckDialog from '../../menuDialog/AICheckDlg';
import BigImagePanel from './BigImage';
import VideoUrlPanel from './VideoUrlPanel';
import InsertEquation from './InsertEquation';
import EditEquation from './equation';
import { clearAllWavyUnderlinesFromDocument } from '../../../../model/core/WavyUnderline/WavyUnderlineUtils';

interface IProps {
    host: any;
    equationRefs?: any;
}

interface IState {
    bRefresh: boolean;
}

const components = {
    cascadeManager: () => {
        return import('./cascade-manager/Cascade');
    },
    // testTextCache: () => {
    //     return import('./TestTextCache');
    // },
    barcode: () => {
        return import('./Barcode');
    },

    qrcode: () => {
        return import('./InsertQRCode');
    }
};

export default class Modal extends React.Component<IProps, IState> {
    private documentCore: any;
    private host: EmrEditor;
    private parent: any;
    private property: any;

    private bNewControlNum: boolean;
    private bNewControlText: boolean;
    private bNewComboBox: boolean;
    private bNewControlCheck: boolean;
    private bNewControlRadio: boolean;
    private bChart: boolean;
    private bFont: boolean;
    private bParagraph: boolean;
    private bImage: boolean;
    private bMedia: boolean;
    private bVersionDlg:boolean;
    private bAICheckDlg:boolean;
    private bBigImage: boolean;
    private bSplitCell: boolean;
    private bDeleteCell: boolean;
    private bTableProps: boolean;
    private bNISTableProps: boolean;
    private bInsertTable: boolean;
    private bInsertNISTable: boolean;
    private bNISTableCellProps: boolean;
    private bNISTableCellContentProps: boolean;
    private bNISTableCellText: boolean;
    private bNISTableCellDate: boolean;
    private bNISTableCellTime: boolean;
    private bNISTableCellList: boolean;
    private bNISTableCellSignature: boolean;
    private bNISTableCellQuick: boolean;
    private bNISTableCellNumber: boolean;
    private bFormulaCalc: boolean;
    private bTableCellVertAlign: boolean;
    private bSetPage: boolean;
    private bNewDateBox: boolean;
    private bNewSignatureBox: boolean;
    private bNewAddressBox: boolean;
    private bNewHeaderFooter: boolean;
    private bNewPageNum: boolean;
    private bRevisionPanel: boolean; // 修订面板
    private bCommentPanel: boolean; // 批注面板
    private bCommentInsert: boolean; // 是否是点击了插入批注
    private bWatermark: boolean;
    private bViewScale: boolean;
    private bRegion: boolean;
    private bNavMenu: boolean;
    private bImageUrl: boolean;
    private bImageBigUrl: boolean;
    private printDialog: PrintDialog;
    private bRevisionSetting: boolean;
    private bRevisionAcceptReject: boolean;
    private bAutoTest: boolean;
    private bAutoTestPlay: boolean;
    private callback: any;
    private width: number;
    private image: any;
    private tableName: string;
    private newControlType: number;
    private bAttributePanel: boolean;
    private bNISTableCellBP: boolean;

    private bVideoUrl: boolean;

    private bEmptyParagraghRemove: boolean;
    private bCascadeManagerUI: any;
    private bTestTextCache: boolean;
    private testTextCache: any;
    private imageVertAlign: number;
    private loadModal: LoadModal;
    private type: number;
    private option: any;

    private bBarcode: boolean;
    private bQRCode: boolean;
    private barcode: any;
    private qrcode: any;
    private bParaButton: boolean;
    private bInsertEquation: boolean;
    private bEditEquation: boolean;

    constructor(props: IProps) {
        super(props);
        if (this.isSDKContainer(props.host) === false) {
            this.documentCore = props.host.state.documentCore;
            this.host = props.host;
        } else {
            this.parent = props.host;
            if (props.host.getDocumentCore) {
                this.documentCore = props.host.getDocumentCore();
            }
        }

        this.state = {
            bRefresh: false,
        };
        this.loadModal = new LoadModal(this as any);
    }

    public render(): any {
        return (
            <React.Fragment>
                {this.renderNewControlNum()}
                {this.renderFont()}
                {this.renderParagraph()}
                {this.renderImage()}
                {this.renderMedia()}
                {this.renderBigImage()}
                {this.renderSplitCell()}
                {this.renderNewControlText()}
                {this.renderNewComboBox()}
                {this.renderChart()}
                {this.renderDeleteCell()}
                {this.renderInsertTable()}
                {this.renderTable()}
                {this.renderNISTable()}
                {this.renderNISTableCellProp()}
                {this.renderNISTableCellText()}
                {this.renderNISTableCellTime()}
                {this.renderNISTableCellDate()}
                {this.renderNISTableCellList()}
                {this.renderNISTableCellSignature()}
                {this.renderNISTableCellQuick()}
                {this.renderNISTableCellNumber()}
                {this.renderNISTableCellContentProp()}
                {this.renderFormulaCalc()}
                {this.renderCellVertAlign()}
                {this.renderSetPage()}
                {this.renderBarcode()}
                {this.renderQRCode()}
                {this.renderNewDateBox()}
                {this.renderNewSignatureBox()}
                {this.renderAddressBox()}
                {this.renderNewControlCheck()}
                {this.renderNewControlRadio()}
                {this.renderViewScale()}
                {this.renderHeaderFooterDialog()}
                {this.renderNavMenu()}
                {this.renderImageUrl()}
                {this.renderImageBigUrl()}
                {this.renderPageNumDialog()}
                {this.renderRevisionPanelDialog()}
                {this.renderCommentPanelDialog()}
                {this.renderWatermarkDialog()}
                {this.renderRevisionSetting()}
                {this.renderRevisionAcceptReject()}
                {this.renderRegion()}
                {this.renderAutoTest()}
                {this.renderAutoTestPlay()}
                {this.renderEmptyParaRemove()}
                {this.renderParaButton()}
                {this.renderNISTableCellBP()}
                {this.renderCascadeManagerUI()}
                {this.renderTestTextCache()}
                {this.renderVersionDialog()}
                {this.renderAICheckDialog()}
                {this.renderVideoUrl()}
                {this.renderInsertEquation()}
                {this.renderEditEquation()}
                {this.loadModal.render()}
            </React.Fragment>
        );
    }
    public UNSAFE_componentWillMount(): void {
        if (this.parent !== undefined) {
            return;
        }
        if (!this.documentCore && this.host?.docId) {
             this.setEditorInfo(this.host.docId);
        }
        if (this.documentCore) {
             gEvent.addEvent(this.documentCore.getCurrentId(), gEventName.DialogEvent, this.showTypeModal);
        } else if (this.host?.docId) {
            gEvent.addEvent(this.host.docId, gEventName.DialogEvent, this.showTypeModal);
        }
    }

    public componentDidMount(): void {
        //
    }

    public componentWillUnmount(): void {
        const docId = this.documentCore?.getCurrentId() || this.host?.docId;
        if (docId) {
            gEvent.deleteEvent(docId, gEventName.DialogEvent, this.showTypeModal);
        }
        if (this.printDialog) {
            this.printDialog.clear();
            this.printDialog = null;
        }
    }

    public clearDialog(): void {
        Object.keys(this).forEach(key => {
            if (key.startsWith('b') && typeof this[key] === 'boolean') {
                this[key] = false;
            }
        });
        this.property = null;
        this.option = null;
        this.type = null;
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private isSDKContainer(host: any): boolean {
        return host.name === 'SDKcontainerExt';
    }

    private setEditorInfo(id: number): void {
        if (this.parent !== undefined) {
            this.documentCore = this.parent.getDocumentCore();
        }
    }

    private showTypeModal = (type: number, option?: any, option2?: any): void => {
        this.setEditorInfo(gEvent.getActiveDocId());
        this.type = type;
        this.option = option;
        this.property = null;

        let flagName = "unknown";
        switch (type) {
            case ToolbarIndex.NumberBox: {
                this.bNewControlNum = true;
                this.property = option;
                break;
            }
            case ToolbarIndex.TextBox: {
                this.bNewControlText = true;
                this.property = option;
                this.newControlType = NewControlType.TextBox;
                break;
            }
            case ToolbarIndex.Section: {
                this.bNewControlText = true;
                this.property = option;
                this.newControlType = NewControlType.Section;
                break;
            }
            case ToolbarIndex.SingleOrMultipleBox: {
                this.bNewComboBox = true;
                this.property = option;
                break;
            }
            case ToolbarIndex.CascadeManager: {
                this.bCascadeManagerUI = true;
                break;
            }
            case ToolbarIndex.TestTextCache: {
                this.bTestTextCache = true;
                break;
            }
            case ToolbarIndex.ListBox: {
                this.newControlType = NewControlType.ListBox;
                this.bNewComboBox = true;
                this.property = option;
                break;
            }
            case ToolbarIndex.MultiListBox: {
                this.newControlType = NewControlType.MultiListBox;
                this.bNewComboBox = true;
                this.property = option;
                break;
            }
            case ToolbarIndex.Combox: {
                this.newControlType = NewControlType.Combox;
                this.bNewComboBox = true;
                this.property = option;
                break;
            }
            case ToolbarIndex.MultiCombox: {
                this.newControlType = NewControlType.MultiCombox;
                this.bNewComboBox = true;
                this.property = option;
                break;
            }
            case MenuItemIndex.Font:
            case ToolbarIndex.Char: {
                this.bFont = true;
                break;
            }
            case ToolbarIndex.AutoTest: {
                this.callback = option.callback;
                this.bAutoTest = true;
                this.property = option.data;
                break;
            }
            case ToolbarIndex.AutoTestPlay: {
                this.bAutoTestPlay = true;
                this.property = option || {};
                break;
            }
            case ToolbarIndex.AttributePanel: {
                this.bAttributePanel = true;
                break;
            }
            case MenuItemIndex.Paragraph:
            case ToolbarIndex.Paragraph: {
                this.bParagraph = true;
                break;
            }
            case ToolbarIndex.EmptyParagraphRemove: {
                this.bEmptyParagraghRemove = true;
                this.bParagraph = false;
                break;
            }
            case MenuItemIndex.Image: {
                this.image = option;
                this.bImage = true;
                break;
            }
            case MenuItemIndex.Media: {
                this.image = option;
                this.bMedia = true;
                break;
            }
            case ToolbarIndex.AboutVersion:{
                this.bVersionDlg = true;
                break;
            }
            case MenuItemIndex.AICheck:{
                this.bAICheckDlg = true;
                break;
            }
            case ToolbarIndex.AICheck:{
                this.bAICheckDlg = true;
                break;
            }
            case MenuItemIndex.BigImage: {
                this.image = option;
                this.bBigImage = true;
                break;
            }
            case MenuItemIndex.SplitCell: {
                this.bSplitCell = true;
                break;
            }
            case ToolbarIndex.SpecialCharacter: {
                this.bChart = true;
                break;
            }
            case MenuItemIndex.DeleteRow:
            case MenuItemIndex.DeleteCol: {
                this.property = type;
                this.bDeleteCell = true;
                break;
            }
            case ToolbarIndex.TableProps:
            case MenuItemIndex.Table: {
                const table = this.documentCore.getCurrentTable();
                if ( table ) {
                    const bNISTable = table.isNISTable();
                    if ( !bNISTable ) {
                        this.bTableProps = true;
                    } else {
                        this.bNISTableProps = true;
                    }
                    this.tableName = (option || {}).name;
                }
                break;
            }
            case MenuItemIndex.FormulaCalc: {
                this.bFormulaCalc = true;
                break;
            }
            case MenuItemIndex.TableCellVertAlign: {
                this.bTableCellVertAlign = true;
                break;
            }
            case MenuItemIndex.ImageAlign: {
                this.imageVertAlign = type;
                break;
            }
            case ToolbarIndex.MedEquation: {
                this.bInsertEquation = true;
                this.bEditEquation = false;
                this.property = option;
                break;
            }
            case MenuItemIndex.Formula: {
                this.bInsertEquation = false;
                this.bEditEquation = true;
                this.property = option;
                break;
            }
            case MenuItemIndex.Barcode: {
                this.bBarcode = true;
                this.barcode = option;
                break;
            }
            case MenuItemIndex.QRCode: {
                this.bQRCode = true;
                this.qrcode = option;
                break;
            }
            case ToolbarIndex.InsertTable: {
                this.bInsertTable = true;
                this.bInsertNISTable = false;
                break;
            }
            case ToolbarIndex.InsertNISTable: {
                if ( NURSING_FEATURE ) {
                    this.bInsertTable = false;
                    this.bInsertNISTable = true;
                }
                break;
            }
            case ToolbarIndex.NISTableProps: {
                if ( NURSING_FEATURE ) {
                    this.bNISTableProps = true;
                    this.tableName = (option || {}).name;
                }
                break;
            }
            case ToolbarIndex.NISTableCellProps: {
                if ( NURSING_FEATURE ) {
                    this.bNISTableCellProps = true;
                }
                break;
            }
            case ToolbarIndex.NISTableCellContentProps: {
                if ( NURSING_FEATURE ) {
                    this.bNISTableCellContentProps = true;
                }
                break;
            }
            case MenuItemIndex.NISTableCellText: {
                if ( NURSING_FEATURE ) {
                    this.bNISTableCellText = true;
                    this.property = option;
                }
                break;
            }
            case MenuItemIndex.NISTableCellDate: {
                if ( NURSING_FEATURE ) {
                    this.bNISTableCellDate = true;
                    this.property = option;
                }
                break;
            }
            case MenuItemIndex.NISTableCellTime: {
                if ( NURSING_FEATURE ) {
                    this.bNISTableCellTime = true;
                    this.property = option;
                }
                break;
            }
            case MenuItemIndex.BloodPressure: {
                if ( NURSING_FEATURE ) {
                    this.bNISTableCellBP = true;
                    this.property = option;
                }
                break;
            }
            case MenuItemIndex.NISTableCellList: {
                if ( NURSING_FEATURE ) {
                    this.bNISTableCellList = true;
                    this.property = option;
                }
                break;
            }
            case MenuItemIndex.NISTableCellSignature: {
                if ( NURSING_FEATURE ) {
                    this.bNISTableCellSignature = true;
                    this.property = option;
                }
                break;
            }
            case MenuItemIndex.NISTableCellQuick: {
                if ( NURSING_FEATURE ) {
                    this.bNISTableCellQuick = true;
                    this.property = option;
                }
                break;
            }
            case MenuItemIndex.NISTableCellNumber: {
                if ( NURSING_FEATURE ) {
                    this.bNISTableCellNumber = true;
                    this.property = option;
                }
                break;
            }
            case ToolbarIndex.PageSet: {
                this.bSetPage = true;
                break;
            }
            case ToolbarIndex.Region: {
                this.bRegion = true;
                this.property = option;
                break;
            }
			case ToolbarIndex.Barcode: {
			     this.bBarcode = true;
			     this.barcode = option;
			     break;
			 }
			case ToolbarIndex.QRCode: {
			     this.bQRCode = true;
			     this.qrcode = option;
			     break;
			  }
            case ToolbarIndex.Checkbox: {
                flagName = "bNewControlCheck";
                this.bNewControlCheck = true;
                this.property = option;
                break;
            }
            case ToolbarIndex.MultiRadio:
            case ToolbarIndex.Radio: {
                this.newControlType = type === ToolbarIndex.Radio ?
                NewControlType.RadioButton : NewControlType.MultiRadio;
                this.bNewControlRadio = true;
                this.property = option;
                break;
            }
            case ToolbarIndex.DateBox: {
                this.bNewDateBox = true;
                this.property = option;
                break;
            }
            case ToolbarIndex.SignatureBox: {
                this.bNewSignatureBox = true;
                this.property = option;
                break;
            }
            case ToolbarIndex.AddressBox: {
                this.bNewAddressBox = true;
                this.property = option;
                break;
            }
            case ToolbarIndex.Print: {
                this.initPrint();
                this.printDialog.open(true);
                break;
            }
            case ToolbarIndex.PrintOutpatient: {
                this.initPrint();
                this.printDialog.open(true, option);
                break;
            }
            case ToolbarIndex.CloseReviewPrint: {
                if (this.printDialog) {
                    this.printDialog.close();
                }
                break;
            }
            case ToolbarIndex.ReviewPrint: {
                if (option2) {
                    option2._startName = option2.startName;
                    option2._endName = option2.endName;
                }
                this.initPrint(option2);
                const flag = option === undefined ? false : option;
                
                this.printDialog.open(flag, option2);
                break;
            }
            case ToolbarIndex.CPrint: {
                this.initPrint(option);
                this.printDialog.openCDialog(option);
                break;
            }
            case ToolbarIndex.ViewScale: {
                this.bViewScale = true;
                this.width = this.host.myRef.current.clientWidth;
                break;
            }
            case ToolbarIndex.HeaderFooter: {
                this.bNewHeaderFooter = true;
                break;
            }
            case ToolbarIndex.ImageBigUrl: {
                this.bImageBigUrl = true;
                break;
            }
            case ToolbarIndex.ImageUrl: {
                this.bImageUrl = true;
                break;
            }
            case ToolbarIndex.NavMenu: {
                this.bNavMenu = true;
                break;
            }
            case ToolbarIndex.PageNum: {
                this.bNewPageNum = true;
                break;
            }
            case ToolbarIndex.CommentInsert: {
                if (!this.documentCore.isCommentInUnInsertNewControl()) {
                    if (this.documentCore.showCommentPanel(true) === ResultType.Success) {
                        this.refresh(true);
                    }
                    gEvent.setEvent(this.host.docId, gEventName.CommentChange, CommentOperatorType.New);
                }
                COMMENT_FLAG.addCommentFlag = true;
                break;
            }
            case ToolbarIndex.CommentShow: {
                const bShow = !this.documentCore.getCommentStatusInfo().bShowPanel;
                if (this.documentCore.showCommentPanel(bShow) === ResultType.Success) {
                    this.refresh(true);
                }
                break;
            }
            case ToolbarIndex.ReviewPanel: {
                this.bRevisionPanel = typeof option === 'boolean' ? option : true;
                break;
            }
            case ToolbarIndex.WaterMark: {
                this.bWatermark = true;
                break;
            }
            case ToolbarIndex.RevisionSetting: {
                this.bRevisionSetting = true;
                break;
            }
            case ToolbarIndex.RevisionAcceptReject: {
                this.bRevisionAcceptReject = true;
                break;
            }
            case ToolbarIndex.VideoUrl: {
                this.bVideoUrl = true;
                break;
            }
            case MenuItemIndex.ParaButton: {
                flagName = "bParaButton";
                this.bParaButton = true;
                this.property = option;
                break;
            }
            default:
                break;
        }
        if (!this.documentCore && this.host?.state?.documentCore) {
            this.documentCore = this.host.state.documentCore;
        }
        this.setState({ bRefresh: !this.state.bRefresh });
    }

    private initPrintDialog(): any {
        if (this.printDialog && this.printDialog.getHost() === this.host) {
            return this.printDialog.getDocumentCore();
        }
        this.printDialog = new PrintDialog(null, this.host);
        const document = new DocumentCore(undefined, false);
        this.printDialog.setDoc(document);
        return document;
    }

    private initPrint(options?: any): void {
        const document = this.initPrintDialog();
        const documentCore = this.documentCore;

        const bTrackRevisions = documentCore.isTrackRevisions();
        if ( bTrackRevisions ) {
            documentCore.setTrackRevisions(false);
        }
        const content = documentCore.getDocContent();
        const pageSize = this.getPageSize(options);
        let newOptions;
        if (pageSize) {
            newOptions = {pageSize};
            options.pageSize = pageSize;
        }
        let startName;
        if (options?._startName) {
            newOptions = newOptions || {};
            startName = newOptions._startName = options._startName;
            newOptions._endName = options._endName;
        }
        document.addDocContent(content, undefined, newOptions);
        
        // 清理打印文档中的波浪线
        clearAllWavyUnderlinesFromDocument(document.getDocument());
        
        document.changeRevisionState2(documentCore.isFinalRevision());
        document.getDocument()
                .setHeaderFooterInterface(documentCore.isSetHeadFooterInterface());
        document.getDocument()
                .setDynamicGridLine(documentCore.getDocument()
                            .isDynamicGridLine(), documentCore.getDocument()
                                    .getDynamicGridLineColor());

        if (startName) {
            options.startEle = newOptions.startEle;
            options.endEle = newOptions.endEle;
        }
        documentCore.removeSelection();
        this.printDialog.onClose(() => {
            if ( bTrackRevisions ) {
                documentCore.setTrackRevisions(bTrackRevisions);
            }
            this.printDialog.clearContent();
            this.printDialog.clearDom();
            documentCore.recalculateAllForce();
            this.refresh(true);
        });
    }

    private getPageSize(options: any): any {
        if (!options || !options.pageSize || typeof options.pageSize !== 'string') {
            return;
        }

        const pages = options.pageSize.split(',');
        const pageCols = ['width', 'height', 'top', 'bottom', 'left', 'right'];
        const obj = {};
        pages.forEach((item, index) => {
            const num = parseFloat(item);
            if (!item || isNaN(num) || isNaN(item) || num < 0) {
                obj[pageCols[index]] = undefined;
                return;
            }
            obj[pageCols[index]] = getPxForMM(num * 10);
        });
        return obj;
    }

    private renderAutoTest(): any {
        if (this.bAutoTest === undefined) {
            return null;
        }

        return (
            <AutoTest
                id='bAutoTest'
                visible={this.bAutoTest}
                documentCore={this.documentCore}
                callback={this.callback}
                data={this.property}
                close={this.close}
            />
        );
    }

    private renderAutoTestPlay(): any {
        if (this.bAutoTestPlay === undefined || !this.property) {
            return null;
        }

        return (
            <AutoTestPlay
                id='bAutoTestPlay'
                visible={this.bAutoTestPlay}
                documentCore={this.documentCore}
                host={this.host}
                autoId={this.property.id}
                callback={this.property.callback}
                close={this.close}
            />
        );
    }

    private renderRegion(): any {
        if (this.bRegion === undefined) {
            return null;
        }

        return (
            <Region
                id='bRegion'
                visible={this.bRegion}
                documentCore={this.documentCore}
                property={this.property}
                close={this.close}
            />
        );
    }

    private renderImageUrl(): any {
        if (this.bImageUrl !== true) {
            return null;
        }
        return (
            <ImageUrlPanel
                id='bImageUrl'
                documentCore={this.documentCore}
                close={this.close}
                visible={this.bImageUrl}
                host={this.host}
            />
        );
    }
    private renderImageBigUrl(): any {
        if (this.bImageBigUrl !== true) {
            return null;
        }
        return (
            <ImageUrlPanel
                id='bImageBigUrl'
                isBigImage={true}
                documentCore={this.documentCore}
                close={this.close}
                visible={this.bImageBigUrl}
                host={this.host}
            />
        );
    }

    private renderNavMenu(): any {
        if (this.bNavMenu === undefined) {
            return null;
        }

        return (
            <NavMenu
                id='bNavMenu'
                refresh={this.refresh}
                host={this.host}
                visible={this.bNavMenu}
                documentCore={this.documentCore}
                close={this.close}
            />
        );
    }

    private renderViewScale(): any {
        if (this.bViewScale === undefined) {
            return null;
        }

        return (
            <ViewScale
                id='bViewScale'
                width={this.width}
                visible={this.bViewScale}
                documentCore={this.documentCore}
                close={this.close}
            />
        );
    }

    private renderNewControlRadio(): any {
        if (this.bNewControlRadio === undefined) {
            return null;
        }

        return (
            <NewControlRadio
                id='bNewControlRadio'
                visible={this.bNewControlRadio}
                documentCore={this.documentCore}
                property={this.property}
                close={this.close}
                type={this.newControlType}
            />
        );
    }

    private renderNewControlCheck(): any {
        if (!this.bNewControlCheck) {
            return null;
        }
        const documentCore = this.documentCore || this.host?.state?.documentCore;
        const propertyData = this.property;
        return (
            <NewControlCheck
                visible={this.bNewControlCheck}
                documentCore={documentCore}
                property={propertyData}
                id='bNewControlCheck'
                close={this.close}
            />
        );
    }

    private renderNewDateBox(): any {
        if (this.bNewDateBox === undefined) {
            return null;
        }

        return (
            <NewDateBox
                id='bNewDateBox'
                visible={this.bNewDateBox}
                documentCore={this.documentCore}
                property={this.property}
                close={this.close}
            />
        );
    }

    private renderNewSignatureBox(): any {
        if (this.bNewSignatureBox === undefined) {
            return null;
        }

        return (
            <NewSignatureBox
                id='bNewSignatureBox'
                visible={this.bNewSignatureBox}
                documentCore={this.documentCore}
                property={this.property}
                close={this.close}
            />
        );
    }

    private renderAddressBox(): any {
        if (this.bNewAddressBox === undefined) {
            return null;
        }

        return (
            <NewAddressBox
                id='bNewAddressBox'
                visible={this.bNewAddressBox}
                documentCore={this.documentCore}
                property={this.property}
                close={this.close}
            />
        );
    }

    private renderHeaderFooterDialog(): any {
        if (this.bNewHeaderFooter === undefined) {
            return null;
        }

        return (
            <NewHeaderFooter
                id='bNewHeaderFooter'
                visible={this.bNewHeaderFooter}
                documentCore={this.documentCore}
                close={this.close}
            />
        );
    }

    private renderPageNumDialog(): any {
        if (this.bNewPageNum === undefined) {
            return null;
        }

        return (
            <NewPageNum
                id='bNewPageNum'
                visible={this.bNewPageNum}
                documentCore={this.documentCore}
                close={this.close}
            />
        );
    }

    private renderRevisionPanelDialog(): any {
        if (this.bRevisionPanel === undefined) {
            return null;
        }
        return (
            <RevisionPanelDialog
                id='bRevisionPanel'
                documentCore={this.documentCore}
                visible={this.bRevisionPanel}
                close={this.close}
                refresh={() => this.host.updateCursor()}
            />
        );
    }

    private renderCommentPanelDialog(): any {
        if (this.bCommentPanel === undefined) {
            return null;
        }
        return (
            <CommentPanelDialog
                id='bCommentPanel'
                bInsertNewComment={this.bCommentInsert}
                documentCore={this.documentCore}
                visible={this.bCommentPanel}
                close={this.close}
                refresh={this.refresh}
            />
        );
    }

    private renderWatermarkDialog(): any {
        if (this.bWatermark === undefined) {
            return null;
        }
        return (
            <NewWatermark
                id='bWatermark'
                visible={this.bWatermark}
                documentCore={this.documentCore}
                close={this.close}
            />
        );
    }

    private renderBarcode(): any {
        if (this.bBarcode === undefined) {
            return null;
        }
        const props = {close: this.close, documentCore: this.documentCore,
            image: this.barcode};

        return (
            <AsyncLoad
                host={this}
                name='bBarcode'
                component={components.barcode}
                props={props}
            />
        );
    }

    private renderQRCode(): any{
        if (this.bQRCode === undefined) {
            return null;
        }
        const props = {close: this.close, documentCore: this.documentCore,
            image: this.qrcode};

        return (
            <AsyncLoad
                host={this}
                name='bQRCode'
                component={components.qrcode}
                props={props}
            />
        );
    }

    private renderSetPage(): any {
        if (this.bSetPage === undefined) {
            return null;
        }

        return (
            <PageSetting
                id='bSetPage'
                visible={this.bSetPage}
                documentCore={this.documentCore}
                close={this.close}
            />
        );
    }

    private renderInsertTable(): any {
        if (this.bInsertTable === undefined && !this.bInsertNISTable) {
            return null;
        }

        const dialogId = this.bInsertNISTable ? 'bInsertNISTable' : 'bInsertTable';

        return (
            <InsertTable
                id={dialogId}
                visible={this.bInsertTable || this.bInsertNISTable}
                documentCore={this.documentCore}
                bInsertNISTable={this.bInsertNISTable}
                close={this.close}
            />
        );
    }

    private renderDeleteCell(): any {
        if (this.bDeleteCell === undefined) {
            return null;
        }

        return (
            <DeleteCell
                id='bDeleteCell'
                visible={this.bDeleteCell}
                documentCore={this.documentCore}
                type={this.property}
                close={this.close}
            />
        );
    }

    private renderTable(): any {
        if (this.bTableProps === undefined) {
            return null;
        }

        return (
            <TableSettingDialog
                id='bTableProps'
                tableName={this.tableName}
                visible={this.bTableProps}
                documentCore={this.documentCore}
                close={this.close}
                refresh={this.refresh}
            />
        );
    }

    private renderNISTable(): any {
        if (!this.bNISTableProps) {
            return null;
        }

        return (
            <NISTableSetting
                id='bNISTableProps'
                tableName={this.tableName}
                visible={this.bNISTableProps}
                documentCore={this.documentCore}
                close={this.close}
                refresh={this.refresh}
            />
        );
    }

    private renderNISTableCellProp(): any {
        if (!this.bNISTableCellProps) {
            return null;
        }

        return (
            <NISTableCellSetting
                id='bNISTableCellProps'
                visible={this.bNISTableCellProps}
                documentCore={this.documentCore}
                close={this.close}
            />
        );
    }

    private renderNISTableCellContentProp(): any {
        if (!this.bNISTableCellContentProps) {
            return null;
        }

        return (
            <NISTableCellContentSetting
                id='bNISTableCellContentProps'
                visible={this.bNISTableCellContentProps}
                documentCore={this.documentCore}
                close={this.close}
            />
        );
    }

    private renderNISTableCellText(): any {
        if (!this.bNISTableCellText) {
            return null;
        }

        return (
            <NISTableCellText
                id='bNISTableCellText'
                visible={this.bNISTableCellText}
                documentCore={this.documentCore}
                close={this.close}
                refresh={this.refresh}
            />
        );
    }

    private renderNISTableCellList(): any {
        if (!this.bNISTableCellList) {
            return null;
        }

        return (
            <NISTableCellList
                id='bNISTableCellList'
                visible={this.bNISTableCellList}
                documentCore={this.documentCore}
                close={this.close}
                refresh={this.refresh}
            />
        );
    }

    private renderNISTableCellSignature(): any {
        if (!this.bNISTableCellSignature) {
            return null;
        }

        return (
            <NISTableCellSignature
                id='bNISTableCellSignature'
                visible={this.bNISTableCellSignature}
                documentCore={this.documentCore}
                close={this.close}
                refresh={this.refresh}
            />
        );
    }

    private renderNISTableCellQuick(): any {
        if (!this.bNISTableCellQuick) {
            return null;
        }

        return (
            <NISTableCellQuick
                id='bNISTableCellQuick'
                visible={this.bNISTableCellQuick}
                documentCore={this.documentCore}
                close={this.close}
                refresh={this.refresh}
            />
        );
    }

    private renderNISTableCellBP(): any {
        if (this.bNISTableCellBP !== true) {
            return null;
        }
        const documentCore = this.documentCore || this.host?.state?.documentCore;
        if (!documentCore) {
            console.error('DocumentCore not available for NISTableCellBP');
            return null;
        }

        return (
            <NISTableCellBP
                visible={this.bNISTableCellBP}
                documentCore={documentCore}
                id={'bNISTableCellBP'}
                close={this.close}
                refresh={this.refresh}
            />
        );
    }

    private renderNISTableCellTime(): any {
        if (!this.bNISTableCellTime) {
            return null;
        }

        return (
            <NISTableCellTime
                id='bNISTableCellTime'
                visible={this.bNISTableCellTime}
                documentCore={this.documentCore}
                close={this.close}
                refresh={this.refresh}
            />
        );
    }

    private renderNISTableCellDate(): any {
        if (!this.bNISTableCellDate) {
            return null;
        }

        return (
            <NISTableCellDate
                id='bNISTableCellDate'
                visible={this.bNISTableCellDate}
                documentCore={this.documentCore}
                close={this.close}
                refresh={this.refresh}
            />
        );
    }

    private renderNISTableCellNumber(): any {
        if (!this.bNISTableCellNumber) {
            return null;
        }

        return (
            <NISTableCellNumber
                id='bNISTableCellNumber'
                visible={this.bNISTableCellNumber}
                documentCore={this.documentCore}
                close={this.close}
                refresh={this.refresh}
            />
        );
    }

    private renderFormulaCalc(): any {
        if (this.bFormulaCalc !== true) {
            return null;
        }

        return (
            <FormulaCalc
                id='bFormulaCalc'
                visible={this.bFormulaCalc}
                documentCore={this.documentCore}
                close={this.close}
            />
        );
    }

    private renderCellVertAlign(): any {
        if (null == this.bTableCellVertAlign && !this.imageVertAlign) {
            return null;
        }

        const type = this.bTableCellVertAlign ? MenuItemIndex.TableCellVertAlign : this.imageVertAlign;
        const id = this.bTableCellVertAlign ? 'bTableCellVertAlign' : 'imageVertAlign';

        return (
            <VertAlignSetting
                id={id}
                visible={this.bTableCellVertAlign || !!this.imageVertAlign}
                documentCore={this.documentCore}
                type={type}
                close={this.close}
            />
        );
    }

    private renderChart(): any {
        if (this.bChart === undefined) {
            return null;
        }

        return (
            <Char
                id='bChart'
                visible={this.bChart}
                documentCore={this.documentCore}
                close={this.close}
            />
        );
    }

    private renderFont(): any {
        if (this.bFont === undefined) {
            return null;
        }

        return (
            <Font
                documentCore={this.documentCore}
                visible={this.bFont}
                id='bFont'
                close={this.close}
            />
        );
    }

    private renderSplitCell(): any {
        if (this.bSplitCell === undefined) {
            return null;
        }

        return (
            <TableCellSplit
                documentCore={this.documentCore}
                visible={this.bSplitCell}
                id='bSplitCell'
                close={this.close}
            />
         );
    }

    private renderVersionDialog():any{
        if (this.bVersionDlg === undefined) {
            return null;
        }

        return (
            < VersionDialog
                documentCore={this.documentCore}
                visible={this.bVersionDlg}
                id='bVersionDlg'
                close={this.close}
            />
         );
    }

    private renderAICheckDialog():any{
        if (this.bAICheckDlg === undefined) {
            return null;
        }

        return (
            < AICheckDialog
                documentCore={this.documentCore}
                host={this.host}
                visible={this.bAICheckDlg}
                id='bAICheckDlg'
                close={this.close}
            />
         );
    }

    private renderParagraph(): any {
        if (this.bParagraph === undefined) {
            return null;
        }

        return (
            <Paragraph
                host={this.host}
                documentCore={this.documentCore}
                visible={this.bParagraph}
                id='bParagraph'
                close={this.close}
            />
        );
    }

    private renderImage(): any {
        if (this.bImage === undefined) {
            return null;
        }

        return (
            <Image
                documentCore={this.documentCore}
                id='bImage'
                image={this.image}
                visible={this.bImage}
                close={this.close}
            />
        );
    }

    private renderMedia(): any {
        if (this.bMedia === undefined) {
            return null;
        }
        return (
            <MediaPanel
                documentCore={this.documentCore}
                id='bMedia'
                image={this.image}
                visible={this.bMedia}
                close={this.close}
            />
        );
    }

    public renderBigImage(): any {
        if (this.bBigImage === undefined) {
            return null;
        }
        return (
            <BigImagePanel 
                documentCore={this.documentCore}
                id='bBigImage'
                image={this.image}
                visible={this.bBigImage}
                close={this.close}
            />
        );
    }

    private renderNewComboBox(): any {
        if (this.bNewComboBox === undefined) {
            return null;
        }

        return (
            <NewComboBox
                documentCore={this.documentCore}
                visible={this.bNewComboBox}
                property={this.property}
                id='bNewComboBox'
                close={this.close}
                type={this.newControlType}
            />
        );
    }

    private renderNewControlNum(): any {
        if (this.bNewControlNum === undefined) {
            return null;
        }

        return (
            <NewControlNum
                documentCore={this.documentCore}
                visible={this.bNewControlNum}
                property={this.property}
                id='bNewControlNum'
                close={this.close}
            />
        );
    }

    private renderNewControlText(): any {
        if (this.bNewControlText === undefined) {
            return null;
        }

        return (
            <NewControlText
                documentCore={this.documentCore}
                visible={this.bNewControlText}
                property={this.property}
                id='bNewControlText'
                close={this.close}
                type={this.newControlType}
            />
        );
    }

    private renderRevisionSetting(): any {
        if ( null == this.bRevisionSetting ) {
            return null;
        }

        return (
            <Revision
                documentCore={this.documentCore}
                visible={this.bRevisionSetting}
                id='bRevisionSetting'
                close={this.close}
            />
        );
    }

    private renderRevisionAcceptReject(): any {
        if ( null == this.bRevisionAcceptReject ) {
            return null;
        }

        if ( this.bRevisionAcceptReject && !this.documentCore.haveRevisionChanges() ) {
            this.bRevisionAcceptReject = false;
            return null;
        }

        return (
            <RevisionAcceptReject
                documentCore={this.documentCore}
                visible={this.bRevisionAcceptReject}
                id='bRevisionAcceptReject'
                close={this.close}
            />
        );
    }

    private renderEmptyParaRemove(): any {
        if ( null == this.bEmptyParagraghRemove ) {
            return null;
        }

        return (
            <EmptyParagraghRemove
                host={this.host}
                documentCore={this.documentCore}
                visible={this.bEmptyParagraghRemove}
                id='bEmptyParagraghRemove'
                close={this.close}
            />
        );
    }

    private renderParaButton(): any {
        if (!this.bParaButton) {
            return null;
        }
        const documentCore = this.documentCore || this.host?.state?.documentCore;
        const optionData = this.property;

        return (
            <ParaButton
                visible={this.bParaButton}
                documentCore={documentCore}
                property={optionData}
                id='bParaButton'
                close={this.close}
            />
        );
    }

    private renderCascadeManagerUI(): any {
        if (this.bCascadeManagerUI !== true) {
            return null;
        }

        const cascadeProps = {
            visible: this.bCascadeManagerUI,
            documentCore: this.documentCore,
            id: 'bCascadeManagerUI',
            host: this.host,
            close: this.close
        };

        return (
            <AsyncLoad
                host={this}
                name={'bCascadeManagerUI'}
                component={components.cascadeManager}
                props={cascadeProps}
            />
        );
    }

    private renderTestTextCache(): any {
        if (this.bTestTextCache !== true) {
             return null;
        }
        return null;
    }

    private renderVideoUrl(): any {
        if (this.bVideoUrl !== true) {
            return null;
        }
        return (
            <VideoUrlPanel
                id='bVideoUrl'
                isVideoUrl={true}
                documentCore={this.documentCore}
                close={this.close}
                visible={this.bVideoUrl}
                host={this.host}
            />
        );
    }

    private renderInsertEquation(): any {
        if (this.bInsertEquation !== true) {
            return null;
        }
        const currentDocumentCore = this.documentCore || this.host?.state?.documentCore;
        if (!currentDocumentCore) {
            console.error("DocumentCore not available for InsertEquation");
            return null;
        }

        return (
            <InsertEquation
                id='bInsertEquation'
                visible={this.bInsertEquation}
                documentCore={currentDocumentCore}
                property={this.property}
                close={this.close}
            />
        );
    }

    private renderEditEquation(): any {
        if (this.bEditEquation !== true) {
            return null;
        }
        const currentDocumentCore = this.documentCore || this.host?.state?.documentCore;
        if (!currentDocumentCore) {
            console.error("DocumentCore not available for equation editor");
            return null;
        }

        return (
            <EditEquation
                id='bEditEquation'
                visible={this.bEditEquation}
                equation={this.property}
                documentCore={currentDocumentCore}
                close={this.close}
            />
        );
    }

    private close = (id: string, bRefresh?: boolean, timeout?: number): void => {
        // 公式编辑的特殊处理
        const isEquationEdit = id === 'bEditEquation';
        
        if (id === 'bInsertEquation') {
            this.bInsertEquation = false;
        } else if (isEquationEdit) {
            this.bEditEquation = false;
        } else if (this[id] !== undefined) {
            this[id] = false;
        }

        if (this.type === ToolbarIndex.ReviewPrint) {
            this.initPrintDialog();
        }
        const modalContainer = document.querySelector('.modal-container') as HTMLElement;
        if (modalContainer) {
            modalContainer.focus();
        }

        this.setState({ bRefresh: !this.state.bRefresh });
        
        // 确保在公式编辑后强制刷新文档
        if (bRefresh === true || isEquationEdit) {
            // 公式编辑后使用较大的延迟确保文档更新完成
            const refreshTimeout = isEquationEdit ? 100 : (timeout || 10);
            
            // 公式编辑需要额外强制刷新文档
            if (isEquationEdit && this.documentCore) {
                try {
                    // 确保文档重新计算并更新
                    this.documentCore.getDocument().recalculate();
                    // 触发UI刷新
                    this.refresh(true, refreshTimeout);
                } catch (error) {
                    console.error("刷新文档时出错:", error);
                    // 仍然尝试基本刷新
                    this.refresh(true, refreshTimeout);
                }
            } else {
                // 其他普通刷新
                this.refresh(true, refreshTimeout);
            }
        }
    }

    private refresh = (bRefresh: boolean, timeout?: number): void => {
        if (timeout) {
            window.setTimeout(() => {
                this.host.handleRefresh();
            }, timeout);
        } else {
            this.host.handleRefresh();
        }
    }
}
