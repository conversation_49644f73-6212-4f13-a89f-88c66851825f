import { GraphicObjects } from './GraphicObjects';

export class GraphicPage {

  private pageIndex: number;
  private graphicObjects: GraphicObjects;
  private selectionInfo: any;
  private hdrFtrPage: GraphicPage;

  constructor(pageIndex: number, graphicObjects: GraphicObjects) {
    this.pageIndex = pageIndex;
    this.graphicObjects = graphicObjects;
    this.selectionInfo = null;

    if (graphicObjects) {
      this.hdrFtrPage = new GraphicPage(pageIndex, null);
    }
  }

}
