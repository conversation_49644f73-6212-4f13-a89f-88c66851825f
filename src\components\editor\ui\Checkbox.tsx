import * as React from 'react';
import '../style/checkbox.less';

interface IState {
    bReflash: boolean;
}

interface IPropKey {
    name: string;
    value: string;
    disabled: string;
}

interface IProps {
    onChange?: (value: any, name: string, item?: any) => void;
    data: object[];
    value: any[];
    prop?: IPropKey;
    disabled?: boolean;
    readonly?: boolean;
    name?: string;
}

export default class CheckboxUI extends React.Component<IProps, IState> {
    private values: any[];
    private prop: IPropKey;
    private containerRef: any;
    constructor(props: any) {
        super(props);
        this.init();
        this.state = {
            bReflash: false,
        };
        this.prop = {
            name: 'key',
            value: 'value',
            disabled: 'disabled',
        };
        this.containerRef = React.createRef();
    }

    public render(): any {
        return (
        <div className='editor-checkbox' ref={this.containerRef}>
            {this.renderCheckboxs()}
        </div>
        );
    }

    public componentDidMount(): void {
        this.containerRef.current.addEventListener('change', this.containerChange);
    }

    public componentWillUnmount(): void {
        this.containerRef.current?.removeEventListener('change', this.containerChange);
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        this.init();
    }

    private renderCheckboxs(): any {
        const props = this.props;
        const datas = props.data || [];
        if (datas.length === 0) {
            return null;
        }
        const prop = props.prop || this.prop;
        const values = this.values;
        const keyName = prop.name;
        const keyValue = prop.value;
        const disabled = prop.disabled;
        const allDisabled = props.disabled === true;
        return datas.map((data, index) => {
            const value = data[keyValue];
            const curIndex = values.findIndex((actValue) => actValue === value);
            // const name = 'checkbox' + index;
            let className = '';
            if (curIndex > -1) {
                className = 'checkbox-icon-checked';
            }

            let className1 = '';
            if (data[disabled] === true || allDisabled) {
                className1 = ' disabled';
            }

            return (
                <span
                    key={index}
                    className={'checkbox-item' + className1}
                >
                    <input type='checkbox' data-index={index}/>
                    <i className={className} />
                    <label>{data[keyName]}</label>
                </span>
            );
        });
    }

    private containerChange = (e: any): void => {
        const target = e.target;
        const index = target.getAttribute('data-index');
        if (!index) {
            return;
        }
        this.onChange(parseInt(index, 10));
    }

    private onChange = (index: number): void => {
        const {prop, data, onChange, name} = this.props;
        const item = data[index];
        const keyValue = (prop || this.prop).value;
        const value = item[keyValue];
        const actIndex = this.values.findIndex((act) => act === value);
        if (actIndex === -1) {
            this.values.push(value);
        } else {
            if (actIndex !== -1) {
                this.values.splice(actIndex, 1);
            }
        }
        if (typeof onChange === 'function') {
            onChange(this.values, name, item);
        }
        this.setState({bReflash: !this.state.bReflash});
    }

    private init(): void {
        this.values = (this.props.value || []).slice();
    }
}
