import { IDocumentContent } from '../../../model/DocumentCore';
import { TableBaseUI } from './TableBase';
import { ICanvasProps } from './common';
import {ParaBaseUI} from './ParaBase';

interface IContentProps {
    content: IDocumentContent;
    bFromHeaderFooter?: boolean;
}
export class ContentUI {
    private _props: IContentProps;
    private host: any;
    private pageIndex: number;
    private ctx: any;
    constructor(props: ICanvasProps) {
        this._props = props.content.content;
        this.host = props.host;
        this.pageIndex = props.pageIndex;
        this.ctx = props.ctx;
        this.render();
    }

    private render(): void {
        this.renderContent(this._props.content[0]);
    }

    private renderContent(content: IDocumentContent): any {
        if (!content.paragraphs.length && !content.tables.length) {
            return null;
        }
        // let paras: any[];
        // let tables: any;
        // const scale = 1; // this.scale;
        const {bFromHeaderFooter} = this._props;
        const host = this.host;
        const pageIndex = this.pageIndex;
        const ctx = this.ctx;

        if (content.paragraphs && content.paragraphs.length > 0) {
            content.paragraphs.forEach((para) => {
                const obj = new ParaBaseUI({content: {content: para, bFromHeaderFooter}, host, pageIndex, ctx});
                // return (
                //     <ParaBaseUI
                //         key={para.id}
                //         scale={scale}
                //         host={host}
                //         content={para}
                //         className={' outer'}
                //         pageIndex={pageIndex}
                //         bFromHeaderFooter={bFromHeaderFooter}
                //         ref={this._paraRefs[para.id]}
                //     />
                // );
            });
        }
        if (content.tables && content.tables.length > 0) {
            const option = {content: {content: null, bFromHeaderFooter}, host, pageIndex, ctx}
            content.tables.forEach((table) => {
                option.content.content = table;
                const obj = new TableBaseUI(option)
            });
        }

        // return (
        //     <React.Fragment>
        //         {paras}
        //         {tables}
        //     </React.Fragment>
        // );
    }
}
