import { XmlAttributeComponent } from '../../xml-components';
import { HeaderStyleInfoType } from './header-style-info-type';

export interface IHeaderStyleInfoAttributesProperties {
    type?: string;
}

export class HeaderStyleInfoAttributes extends XmlAttributeComponent<IHeaderStyleInfoAttributesProperties> {
    protected readonly xmlKeys: any = {
        type: 'w:type',
    };

    public setType(type: HeaderStyleInfoType): void {
        // console.log(this['root']['type']);
        this['root']['type'] = type;
    }
}
