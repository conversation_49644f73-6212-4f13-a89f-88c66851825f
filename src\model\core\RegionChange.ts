import { ChangeBaseBoolProperty, ChangeBaseObjectProperty, ChangeBaseStringProperty } from './HistoryChange';
import { HistroyItemType } from './HistoryDescription';
import { Region, RegionManager } from './Region';

export class ChangeRegionName extends ChangeBaseStringProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: Region, old: string, news: string, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.RegionName;
    }

    public setValue(value: string): void {
        const region = this.getClass();
        const regionManager = region ? region.getRegionManager() : null;
        if ( regionManager ) {
            regionManager.updateName(value, region.name);
            region.name = value;
        }
    }
}

export class ChangeRegionTitle extends ChangeBaseStringProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: Region, old: string, news: string, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.RegionTitle;
    }

    public setValue(value: string): void {
        const region = this.getClass();
        if ( region ) {
            region.title = value;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeRegionTitleVisible extends ChangeBaseBoolProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: Region, old: boolean, news: boolean, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.RegionTitleVisible;
    }

    public setValue(value: boolean): void {
        const region = this.getClass();
        if ( region ) {
            region.bShowTitle = value;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeRegionHidden extends ChangeBaseBoolProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: Region, old: boolean, news: boolean, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.RegionHidden;
    }

    public setValue(value: boolean): void {
        const region = this.getClass();
        if ( region ) {
            region.bHidden = value;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeRegionReverseEdit extends ChangeBaseBoolProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: Region, old: boolean, news: boolean, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.RegionReverseEdit;
    }

    public setValue(value: boolean): void {
        const region = this.getClass();
        if ( region ) {
            region.bReverseEdit = value;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeRegionDeleteProtect extends ChangeBaseBoolProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: Region, old: boolean, news: boolean, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.RegionDeleteProtect;
    }

    public setValue(value: boolean): void {
        const region = this.getClass();
        if ( region ) {
            region.bCanntDelete = value;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeRegionEditProtect extends ChangeBaseBoolProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: Region, old: boolean, news: boolean, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.RegionEditProtect;
    }

    public setValue(value: boolean): void {
        const region = this.getClass();
        if ( region ) {
            region.bCanntEdit = value;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeRegionSerialNumber extends ChangeBaseBoolProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: Region, old: string, news: string, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.NewControlSerialNumber;
    }

    public setValue(value: string): void {
        const region = this.getClass();
        if ( region ) {
            region.serialNumber = value;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeRegionShowBackgroundColor extends ChangeBaseBoolProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: Region, old: boolean, news: boolean, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.NewControlShowBackgroundColor;
    }

    public setValue(value: boolean): void {
        const region = this.getClass();
        if ( region ) {
            region.bShowBackgroundColor = value;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeRegionReviewType extends ChangeBaseObjectProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: Region, old: any, news: any, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.RegionReviewType;
    }

    public setValue( value: any ): void {
        const region = this.getClass();
        if ( region ) {
            region.reviewType = value.reviewType;
            region.reviewInfo = value.reviewInfo;
            region.updateTrackRevisions();
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeRegionManagerAddRegions extends ChangeBaseObjectProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: RegionManager, old: Region[], news: Region[], bAdd?: boolean ) {
        super(changeClass, old, news, false);
        this.type = HistroyItemType.ManagerAddRegions;
    }

    public undo(): void {
        if ( this.changeClass && this.changeClass.regionMap
            && this.new && 0 < this.new.length ) {
            this.new.forEach((element) => {
                this.changeClass.regionMap.delete(element.getName());
            });
        }
    }

    public redo(): void {
        if ( this.changeClass && this.changeClass.regionMap
            && this.new && 0 < this.new.length ) {
            this.new.forEach((element) => {
                this.changeClass.regionMap.set(element.getName(), element);
            });
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeRegionManagerAddRegionIndexs extends ChangeBaseObjectProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: RegionManager, old: string, news: number[], bAdd?: boolean ) {
        super(changeClass, old, news, false);
        this.type = HistroyItemType.ManagerAddRegionIndexs;
    }

    public undo(): void {
        if ( this.changeClass && this.changeClass.regionNameMap ) {
            this.changeClass.regionNameMap.delete(this.old);
            this.changeClass.updateRegionNode(this.old, this.new, true);
        }
    }

    public redo(): void {
        if ( this.changeClass && this.changeClass.regionNameMap ) {
            this.changeClass.regionNameMap.set(this.old, this.new);
            this.changeClass.updateRegionNode(this.old, this.new, false);
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeRegionManagerRemoveRegions extends ChangeBaseObjectProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: RegionManager, old: Region[], news: Region[], bAdd?: boolean ) {
        super(changeClass, old, news, false);
        this.type = HistroyItemType.ManagerRemoveRegions;
    }

    public undo(): void {
        if ( this.changeClass && this.changeClass.regionMap
            && this.new && 0 < this.new.length ) {
            this.new.forEach((element) => {
                this.changeClass.regionMap.set(element.getName(), element);
            });
        }
    }

    public redo(): void {
        if ( this.changeClass && this.changeClass.regionMap
            && this.new && 0 < this.new.length ) {
            this.new.forEach((element) => {
                this.changeClass.regionMap.delete(element.getName());
            });
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeRegionManagerRemoveRegionIndexs extends ChangeBaseObjectProperty {
    private type: HistroyItemType = null;

    constructor( changeClass: RegionManager, old: string, news: number[], bAdd?: boolean ) {
        super(changeClass, old, news, false);
        this.type = HistroyItemType.ManagerRemoveRegionIndexs;
    }

    public undo(): void {
        if ( this.changeClass && this.changeClass.regionNameMap ) {
            this.changeClass.regionNameMap.set(this.old, this.new);
            this.changeClass.updateRegionNode(this.old, this.new, false);
        }
    }

    public redo(): void {
        if ( this.changeClass && this.changeClass.regionNameMap ) {
            this.changeClass.regionNameMap.delete(this.old);
            this.changeClass.updateRegionNode(this.old, this.new, true);
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
// export class ChangeRegionManagerAddRegionNode extends ChangeBaseObjectProperty {
//     private type: HistroyItemType = null;

//     constructor( changeClass: RegionManager, pos: number, node: any, bAdd?: boolean ) {
//         super(changeClass, pos, node, bAdd);
//         this.type = HistroyItemType.NewControlRemoveLeafItems;
//     }

//     public undo(): void {
//         if ( this.changeClass && this.changeClass.regionNames && this.new ) {
//             if ( true === this.color ) {
//                 const parent = this.changeClass.regionNames[this.new[0]];
//                 if ( parent ) {
//                     parent.leafList.splice(this.old, 1);
//                 }
//             } else {
//                 this.changeClass.regionNames.splice(this.old, 1);
//             }
//         }
//     }

//     public redo(): void {
//         if ( this.changeClass && this.changeClass.regionNames && this.new ) {
//             if ( true === this.color ) {
//                 const parent = this.changeClass.regionNames[this.new[0]];
//                 if ( parent ) {
//                     parent.leafList.splice(this.old, 0, this.new);
//                 }
//             } else {
//                 this.changeClass.regionNames.splice(this.old, 0, this.new);
//             }
//         }
//     }
// }

// // tslint:disable-next-line: max-classes-per-file
// export class ChangeRegionManagerRemoveRegionNode extends ChangeBaseObjectProperty {
//     private type: HistroyItemType = null;

//     constructor( changeClass: RegionManager, pos: number, node: any, bAdd?: boolean ) {
//         super(changeClass, pos, node, bAdd);
//         this.type = HistroyItemType.NewControlRemoveLeafItems;
//     }

//     public undo(): void {
//         if ( this.changeClass && this.changeClass.regionNames && this.new ) {
//             if ( true === this.color ) {
//                 const parent = this.changeClass.regionNames[this.new[0]];
//                 if ( parent ) {
//                     parent.leafList.splice(this.old, 0, this.new);
//                 }
//             } else {
//                 this.changeClass.regionNames.splice(this.old, 0, this.new);
//             }
//         }
//     }

//     public redo(): void {
//         if ( this.changeClass && this.changeClass.regionNames && this.new ) {
//             if ( true === this.color ) {
//                 const parent = this.changeClass.regionNames[this.new[0]];
//                 if ( parent ) {
//                     parent.leafList.splice(this.old, 1);
//                 }
//             } else {
//                 this.changeClass.regionNames.splice(this.old, 1);
//             }
//         }
//     }
// }

// tslint:disable-next-line: max-classes-per-file
// export class ChangeRegionAddItem extends ChangeBaseObjectProperty {
//     private type: HistroyItemType = null;

//     constructor( changeClass: Region, pos: number, leafs: any[], bAdd?: boolean ) {
//         super(changeClass, pos, leafs, true);
//         this.type = HistroyItemType.NewControlAddItem;
//     }

//     public undo(): void {
//         const region = this.changeClass as Region;
//         if ( region && region.getRegionManager() ) {
//             const regionManager = region.getRegionManager();
//             const regionNameMap = regionManager.getRegionNameMap();
//             const indexs = regionNameMap.get(region.getName());
//             if ( null == this.new || 0 === this.new.length ) {
//                 const regions = regionManager.getRegionNames();
//                 regions.splice(this.old, 1);
//             } else {
//                 // const parent = newControl.getParent();
//                 const childs = regionManager.getChilds(region);
//                 const leafList = this.new.getLeafList();
//                 leafList.splice(this.old, 1);
//             }

//             regionManager.getRegionMap().delete(region.getName());
//             regionNameMap.delete(region.getName());
//         }
//     }

//     public redo(): void {
//         const region = this.changeClass as Region;
//         const regionManager = region.getRegionManager();

//         if ( null == this.new || 0 === this.new.length ) {
//             const regions = regionManager.getRegionNames();
//             regions.splice(this.old, 0, {name: region.getName(), leafList: this.new});
//         } else {
//             // const parent = newControl.getParent();
//             ;
//         }

//         regionManager.getRegionMap().set(region.getName(), region);
//         regionManager.getRegionNameMap().set(region.getName());
//     }
// }
