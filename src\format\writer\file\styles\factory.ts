import { Styles } from './';
import { DocumentAttributes } from '../document/document-attributes';

export class DefaultStylesFactory {
    public newInstance(): Styles {
        const documentAttributes = new DocumentAttributes({
            w: 'http://schemas.openxmlformats.org/wordprocessingml/2006/main',
        });

        const styles = new Styles(documentAttributes);

        // load defaults
        styles.createDocumentDefaults();

        // load header style info
        styles.createHeaderStyleInfo();

        // load watermark related info
        styles.createEditorBackground();

        return styles;
    }
}
