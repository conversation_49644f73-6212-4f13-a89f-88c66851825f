import { XmlComponent } from '../../xml-components';
import { Pages, Application, Version, ProtectMode } from './infoElements';

export class Info extends XmlComponent {

  constructor() {
      super('Info');
  }

  public addPages(pageNum: number): Info {
    this.root.push(new Pages(pageNum));
    return this;
  }

  public addApplication(appName: string): Info {
    this.root.push(new Application(appName));
    return this;
  }

  public addVersion(versionNum: number): Info {
    this.root.push(new Version(versionNum));
    return this;
  }

  public addProtectMode(bProtect: number): Info {
    this.root.push(new ProtectMode(bProtect));
    return this;
  }
}
