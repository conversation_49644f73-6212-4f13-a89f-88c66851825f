import { File } from '../../file';
import { Compiler } from './next-compiler';

export class Packer {
    private readonly compiler: Compiler;

    constructor() {
        this.compiler = new Compiler();
    }

    // public async toBuffer(file: File): Promise<Buffer> {
    //     const zip = await this.compiler.compile(file);
    //     const zipData = (await zip.generateAsync({
    //         type: "nodebuffer",
    //         mimeType: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    //     })) as Buffer;

    //     return zipData;
    // }

    // public async toBase64String(file: File): Promise<string> {
    //     const zip = await this.compiler.compile(file);
    //     const zipData = (await zip.generateAsync({
    //         type: "base64",
    //         mimeType: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    //     })) as string;

    //     return zipData;
    // }

    public async toBlob(file: File): Promise<Blob> {
        // console.log(file)

        const zip = await this.compiler.compile(file);
        // console.log(zip);
        // console.log(zip.file("[Content_Types].xml").name);
        

        const zipData = (await zip.generateAsync({
            type: 'blob',
            // mimeType: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            mimeType: 'application/zip',
            // compression: 'STORE',
            compression: 'DEFLATE',
            // compressionOptions: {
            //     level: 9,
            // },
        })) as Blob;

        return zipData;
    }

    public async toArray(file: File): Promise<ArrayBuffer> {
        // console.log(file)

        const zip = await this.compiler.compile(file);
        // console.log(zip);
        // console.log(zip.file("[Content_Types].xml").name);
        // zip.file('Document.xml')
        //     .async('text')
        //     .then((data) => {
        // // zip.file("Styles.xml").async("text").then((data) => {
        // // zip.folder("word").file("Settings.xml").async("text").then((data) => {
        // // zip.file("Footer.xml").async("text").then((data) => {
        // // zip.file("Header1.xml").async("text").then((data) => {

        //     // console.log(data);

        //     // let parser = new DOMParser();
        //     // let xmlDoc = parser.parseFromString(data,"text/xml");
        //     // console.log(xmlDoc);
        // });

        // zip.file('Header1.xml')
        //     .async('text')
        //     .then((data) => {
        //     // console.log(data);
        // });

        // zip.file("Footer.xml")
        //     .async("text")
        //     .then((data) => {
        //     // console.log(data);
        // });

        // zip.file('Styles.xml')
        //     .async('text')
        //     .then((data) => {

        //     // console.log(data);

        // });

        // zip.file('Settings.xml')
        //     .async('text')
        //     .then((data) => {

        //     // console.log(data);

        // });

        // zip.file('Media.xml')
        //     .async('text')
        //     .then((data) => {

        //     // console.log(data);

        // });

        // // zip.file('Cascade.xml')
        // //     .async('text');

        // const htmlFile = zip.file('Editor-html.html');
        // // console.log(htmlFile)
        // if (htmlFile != null) {
        //     htmlFile
        //     .async('text')
        //     .then((data) => {

        //         // console.log(data);

        //     });
        // }

        // const sourceBindFile = zip.file('sourceBind.json');
        // // console.log(htmlFile)
        // if (sourceBindFile != null) {
        //     sourceBindFile
        //     .async('text')
        //     .then((data) => {

        //         // console.log(data);

        //     });
        // }
        // const date = performance.now();
        const zipData = (await zip.generateAsync({
            type: 'arraybuffer',
            // mimeType: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            mimeType: 'application/zip',
            // compression: 'STORE',
            compression: 'DEFLATE',
            // compressionOptions: {
            //     level: 9,
            // },
        })) as ArrayBuffer;
        // console.log(performance.now() - date)
        return zipData;
    }

    public async getXmlData(file: File, xmlName: string): Promise<string> {
        const zip = await this.compiler.compile(file);

        return zip.file(xmlName)
                    .async('text');

    }

}
