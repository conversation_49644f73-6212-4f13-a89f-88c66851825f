import MouseEventHandler, { KeyBoardEvent } from '../../../../common/MouseEventHandler';
import {GlobalEvent as gEvent, GlobalEventName as gEventName} from '../../../../common/GlobalEvent';
import { isMacOs } from '@/common/commonDefines';

export default class KeyEvent {
    private host: any;
    private gKeyEvent: KeyBoardEvent;
    private gMouseEvent: MouseEventHandler;
    private selections: any;
    private keyEvent: any;
    constructor(host: any) {
        this.host = host;
        this.gKeyEvent = new KeyBoardEvent();
        this.gMouseEvent = new MouseEventHandler();
    }

    public onKeyDown = (event: any, bComposition: boolean): void => {
        const documentCore  = this.host.documentCore;
        this.gKeyEvent.keyCode = event.keyCode;
        this.gKeyEvent.bCtrlKey = (event.ctrlKey || event.metaKey);
        this.gKeyEvent.bMacCmdKey = (isMacOs && event.metaKey);
        this.gKeyEvent.bAltKey = event.altKey;
        this.gKeyEvent.bShiftKey = event.shiftKey;
        this.keyEvent = event;
        // 当前按键为功能键，不做任何处理
        if (true === this.isFuntionKey(this.gKeyEvent)) {
            return;
        }

        // if (event.keyCode === 33 || event.keyCode === 34) {
        //     event.preventDefault();
        //     this.scrollTool.jumpNewPage(event.keyCode);
        //     return;
        // }

        const oldSelection = documentCore.getDocumentSelection();
        const bOldSelection = oldSelection.bUse;

        const pageIndex = this.host.getPageIndex();
        // this.bJump = false;

        documentCore.onKeyDown(this.gKeyEvent);

        const newSelection = documentCore.getDocumentSelection();
        // console.log(bOldSelection, newSelection.bUse);

        // already del after onKeyDown()
        // console.log(circularParse(circularStringify(this.state.documentCore.getDocument())));

        // 选择：
        // 1. shift键 + left/right/up/down
        // 2. ctrl + a: 全选
        // 3. undo/redo : 有选择操作
        // 只要当前有内容选中，就ok
        if (true === newSelection.bUse) {

            // update gMouseEvent class
            const point = documentCore.getCursorPosition();
            this.gMouseEvent.pointX = point.x;
            this.gMouseEvent.pointY = point.y1;

            // always clear selection before render (before selections[] is cleared)
            this.host.clearSection();

            // const pageNode = getPageElement(event.target);
            // const pageId = this.getPageId(pageNode, event);
            const pageId = this.host.getPageIndex();

            this.selections = documentCore.getSelectionBounds(this.gMouseEvent, pageId);

            // console.log(selection)

            // undo/redo
            // todo: 只进行局部（某个段落、或某个页面）刷新
            if (true === newSelection.bUse && true === this.isUndoRedo(this.gKeyEvent)) {
                // this.forceUpdate();
                // this.changeDocument();
            }

            // 只有在有内容选中，并且当前是选中状态，才进行选中区域的渲染
            if (this.selections) {
                const imageFlags = documentCore.getImageFlags();
                if (!imageFlags.isImageOnClick) {
                    this.host.setSelections();
                }
            }

            if ( this.isUndoRedo(this.gKeyEvent) ) {
                // documentCore.removeSelection();
                this.host.updateCursor(true, true);
                this.gKeyEvent.keyCode = 0;
            } else if (this.isTabKey(this.gKeyEvent.keyCode)) {
                gEvent.setEvent(this.host.docId, gEventName.MoveCursor);
                this.gKeyEvent.keyCode = 0;
            }
        // } else if ((true === bOldSelection) && (true === event.shiftKey || true === event.ctrlKey)) {
        //     // 选择状态下，单击shift、ctrl键没有效果，不需要进行光标等刷新
        //     return;
        } else {
            // 上，下，左，右键不需要重刷页面，只有在跨页等需要重刷的情况下才重刷
            // redo/undo时不需要重刷页面，react会自动重刷一次页面，这样减少一次页面刷新

            if ( this.isLRUDKey(this.gKeyEvent.keyCode) ) {
                this.host.updateCursor(true, false);
                this.gKeyEvent.keyCode = 0;
                gEvent.setEvent(this.host.docId, gEventName.KeyDown);
            } else if ( this.isUndoRedo(this.gKeyEvent) ) {
                this.host.updateCursor(true, true);
                this.gKeyEvent.keyCode = 0;
                gEvent.setEvent(this.host.docId, gEventName.KeyDown);
            } else if ( this.isBackspaceOrDelKey(this.gKeyEvent.keyCode) || this.isEnterKey(this.gKeyEvent.keyCode)
                        || this.isTabKey(this.gKeyEvent.keyCode) ) {
                this.host.updateCursor(true, true);
                this.gKeyEvent.keyCode = 0;
            }
            // this.clearSection();

            // 之前选择的状态被清除，确保光标开始闪烁
            // if (true === bOldSelection && false === newSelection.bUse) {
            //     // 当前光标所在页面
            //     const curIndex = documentCore.getCursorPosition().pageNum;

            //     // if (curIndex !== this.state.currentIndex || this.pageIndex !== this.state.currentIndex) {
            //     //     this.bJump = true;
            //     //     this.pageIndex = curIndex;
            //     //     this.setState({ currentIndex: curIndex });
            //     // }

            //     // this.handleWindowVisible();
            // }
        }

        // newControl background
        // if (true === documentCore.isCursorInNewControl()) {
        //     this.renderFocusHightLightNewControl();
        //     this.showNewDropButton();
        // } else {
        //     this.clearHightLightNewControl();
        //     this.closeNewDropButton();
        // }
    }

    public onKeyUp = (event: any, bComposition: boolean): void => {
        this.keyEvent = null;
    }

    public getKeyEvent(): any {
        return this.keyEvent;
    }

    public getKeyBoardEvent(): any {
        return this.gKeyEvent;
    }

    private isUndoRedo( event: KeyBoardEvent ): boolean {
        return ( event.keyCode === 89 || event.keyCode === 90 ) && ( true === event.bCtrlKey );
    }

    private isFuntionKey( event: KeyBoardEvent ): boolean {
        return ( event.keyCode >= 112 && event.keyCode <= 123 );
    }

    private isLRUDKey( keyCode: number ): boolean {
        return (keyCode === 33 || keyCode === 34 || keyCode === 37 || keyCode === 38 || keyCode === 39 ||
                keyCode === 40);
    }

    private isTabKey( keyCode: number ): boolean {
        return ( 9 === keyCode );
    }

    private isEnterKey( keyCode: number ): boolean {
        return ( 13 === keyCode );
    }

    private isBackspaceOrDelKey( keyCode: number ): boolean {
        return ( 8 === keyCode || 46 === keyCode );
    }
}
