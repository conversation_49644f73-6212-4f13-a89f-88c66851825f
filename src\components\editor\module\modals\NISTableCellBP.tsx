import * as React from 'react';
import Dialog from '../../ui/Dialog';
import But<PERSON> from '../../ui/Button';
import Checkbox from '../../ui/CheckboxItem';
import { BloodPressureFormat, CustomPropertyElementType } from '../../../../common/commonDefines';
import CustomPropertyBtn from './CustomProperty';
import { IFRAME_MANAGER } from '../../../../common/IframeManager';
import Input from '../../ui/Input';

interface IDialogProps {
    documentCore: any;
    visible: boolean;
    id?: string;
    close: (name: string | number, bRefresh?: boolean) => void;
    refresh: (bRefresh: boolean, timeout?: number) => void;
}

interface IState {
    bRefresh: boolean;
}

export default class NISTableCellBP extends React.Component<IDialogProps, IState> {
    private cell: any;
    // private tableProps: any;
    private visible: any;
    private bCustomProperty: boolean;
    private formats: string[];

    constructor(props: any) {
        super(props);
        this.cell = {};
        this.state = {
            bRefresh: false,
        };

        this.visible = this.props.visible;
        // this.formats = [CellTimeFormat.HM, CellTimeFormat.HMS, CellTimeFormat.HM2, CellTimeFormat.HMS2];
        this.formats = [BloodPressureFormat.ASlashB, BloodPressureFormat.AColonB, BloodPressureFormat.ASpaceB];
        // this.setDialogValue();
    }

    public componentDidMount(): void {
        // this.boxRef.current.ownerDocument.addEventListener('click', this.docClick);
    }

    public componentWillUnmount(): void {
        // this.boxRef.current.ownerDocument.removeEventListener('click', this.docClick);
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        this.visible = nextProps.visible;
    }

    public render(): any {

        return (
            <Dialog
                id={this.props.id}
                visible={this.visible}
                width={380}
                open={this.open}
                preventDefault={false}
                title='血压单元格'
                confirm={this.confirm}
                footer={this.renderFooter()}
            >
                <div>
                    <div className='editor-line'>
                        <div className='w-70'>内部名称</div>
                        <div className='right-auto'>
                            <Input
                                name='serialNumber'
                                value={this.cell.serialNumber}
                                onChange={this.onChange}
                                readonly={true}
                                placeholder={''}
                                disabled={true}
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <span className='title'>属性：</span>
                    </div>
                    <div className='editor-line'>
                        <Checkbox
                            name='bProtected'
                            value={this.cell.bProtected}
                            disabled={false}
                            onChange={this.onChange}
                        >
                            禁止编辑
                        </Checkbox>
                        <Checkbox
                            name='visible'
                            value={this.cell.visible}
                            disabled={false}
                            onChange={this.onChange}
                        >
                            网格线
                        </Checkbox>
                    </div>

                    <div className='editor-line editor-multi-line'>
                        <label className='w-70'>血压格式</label>
                        <div className='right-auto'>
                            {this.renderFormat()}
                        </div>
                    </div>

                    <div className='editor-line'>
                        <CustomPropertyBtn
                            name='customProperty'
                            properties={this.cell.customProperty}
                            documentCore={this.props.documentCore}
                            onChange={this.onChange}
                            close={this.onClose}
                            id='bCustomProperty'
                            visible={this.bCustomProperty}
                            type={CustomPropertyElementType.TableCell}
                        />
                    </div>
                </div>
            </Dialog>
        );
    }

    private renderFooter(): any {
        return (
            <span>
                <Button onClick={this.close}>取消</Button>
                <Button type='primary' onClick={this.confirm}>确认</Button>
            </span>
        );
    }

    // private renderFormat(): any {
    //     const formats = this.formats;
    //     const format = this.cell.format;
    //     return formats.map((item, index) => {
    //         let className;
    //         if (format === item) {
    //             className = 'active';
    //         }
    //         return (<span key={index} className={className}>{item}</span>);
    //     });
    // }

    private renderFormat(): any {
        const activeValue = this.cell.format;
        const list = this.formats.map((item) => {
            let className = null;
            if (item === activeValue) {
                className = 'active';
            }
            return (
                <li className={className} key={item} onClick={this.onClick.bind(this, item)}>{item}</li>
            );
        });
        return (
            <ul className='format-list'>
                {list}
            </ul>
        );
    }

    private setDialogValue(): void {
        const cell = this.cell = {
            bProtected: false,
            customProperty: undefined,
            serialNumber: undefined,
            format: '',
            visible: null
        };

        const cellProps = this.props.documentCore.getTableCellProps();
        if (!cellProps) {
            return;
        }
        cell.bProtected = cellProps.bProtected;
        const property = cellProps.nisProperty;
        if (property) {
            cell.customProperty = property.customProperty;
            cell.serialNumber = property.serialNumber;
            cell.format = property.bloodPressureFormat;
            cell.visible = property.gridLine?.visible
        }
    }

    private onClick = (item: string): void => {
        this.cell.format = item;
        this.setState({});
    }

    private open = (): void => {
        // const table = this.table;
        this.setDialogValue();
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private close = (bRefresh?: boolean): void => {
        this.props.close(this.props.id, bRefresh);
        this.visible = false;
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private onClose = (id: string, bRefresh: boolean): void => {
        this[id] = false;
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private onChange = (value: any, name: string): void => {
        this.cell[name] = value;
    }

    private confirm = (id?: any): void => {
        const { documentCore } = this.props;
        IFRAME_MANAGER.setDocId(documentCore.getCurrentId());

        const cell = this.cell;
        const format: string = cell.format;
        documentCore.setTableCellProps({bProtected: cell.bProtected,
            nisProperty: {
                bloodPressureFormat: format,
                customProperty: cell.customProperty,
                serialNumber: cell.serialNumber,
                gridLine: {
                    visible: cell.visible
                }
            }});

        this.close(true);
    }

}
