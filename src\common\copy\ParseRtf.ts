
import { RTFJS } from './rtf';
import HtmlParse from './ParseHtml';

export default class RTF {
// tslint:disable-next-line: typedef
    public async parse(str: string) {
        if (!str) {
            return;
        }
        const rtf = this.stringToArrayBuffer(str);
        const htmlElements = await new RTFJS.Document(rtf, null).render();
        let html = '';
        for (let i = 0, ii = htmlElements.length; i < ii; i++) {
            const div = htmlElements[i].get(0);
            if ((i === ii - 1) && !div.innerHTML) {
                continue;
            }
            html += div.outerHTML;
        }

        if (!html) {
            return;
        }
        return new HtmlParse().parse(html);
    }
    private stringToArrayBuffer(str: string): ArrayBuffer {
        const buffer = new ArrayBuffer(str.length);
        const bufferView = new Uint8Array(buffer);
        for (let i = 0, ii = str.length; i < ii; i++) {
            bufferView[i] = str.charCodeAt(i);
        }
        return buffer;
    }
}
