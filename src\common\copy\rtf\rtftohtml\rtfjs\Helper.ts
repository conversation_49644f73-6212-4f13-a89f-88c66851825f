/*

The MIT License (MIT)

Copyright (c) 2015 Thomas <PERSON>

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.

*/

import { IColor } from './parser/destinations/ColortblDestinations';

// tslint:disable-next-line:interface-name
export interface RTFJSError {
    name: string;
    message: string;
    stack: string;
}

// tslint:disable-next-line: sy-global-const-name
export let RTFJSError = function(this: RTFJSError, message: string): void {
    this.name = 'RTFJSError';
    this.message = message;
    this.stack = (new Error()).stack;
} as any as new (message: string) => RTFJSError;
RTFJSError.prototype = new Error();

let isLoggingEnabled = true;
export function loggingEnabled(enabled: boolean): void {
    isLoggingEnabled = enabled;
}

export class Helper {
    // tslint:disable-next-line: sy-property-name
    public static JUSTIFICATION: any = {
        LEFT: 'left',
        CENTER: 'center',
        RIGHT: 'right',
        JUSTIFY: 'justify',
    };
    // tslint:disable-next-line: sy-property-name
    public static BREAKTYPE: any = {
        NONE: 'none',
        COL: 'col', // TODO: ???
        EVEN: 'even',
        ODD: 'odd',
        PAGE: 'page',
    };
    // tslint:disable-next-line: sy-property-name
    public static PAGENUMBER: any = {
        DECIMAL: 'decimal',
        UROM: 'urom', // TODO: ???
        LROM: 'lrom', // TODO: ???
        ULTR: 'ultr', // TODO: ???
        LLTR: 'lltr', // TODO: ???
    };
    // tslint:disable-next-line: sy-property-name
    public static UNDERLINE: any = {
        NONE: 'none',
        CONTINUOUS: 'continuous',
        DOTTED: 'dotted',
        DASHED: 'dashed',
        DASHDOTTED: 'dashdotted',
        DASHDOTDOTTED: ' dashdotdotted',
        DOUBLE: 'double',
        HEAVYWAVE: 'heavywave',
        LONGDASHED: 'longdashed',
        THICK: 'thick',
        THICKDOTTED: 'thickdotted',
        THICKDASHED: 'thickdashed',
        THICKDASHDOTTED: 'thickdashdotted',
        THICKDASHDOTDOTTED: 'thickdashdotdotted',
        THICKLONGDASH: 'thicklongdash',
        DOUBLEWAVE: 'doublewave',
        WORD: 'word',
        WAVE: 'wave',
    };
    // tslint:disable-next-line: sy-property-name
    public static FONTPITCH: any = {
        DEFAULT: 0,
        FIXED: 1,
        VARIABLE: 2,
    };
    // tslint:disable-next-line: sy-property-name
    public static CHARACTER_TYPE: any = {
        LOWANSI: 'loch',
        HIGHANSI: 'hich',
        DOUBLE: 'dbch',
    };

    private static _A: number = 'A'.charCodeAt(0);
    private static _a: number = 'a'.charCodeAt(0);
    private static _F: number = 'F'.charCodeAt(0);
    private static _f: number = 'f'.charCodeAt(0);
    private static _Z: number = 'Z'.charCodeAt(0);
    private static _z: number = 'z'.charCodeAt(0);
    private static _0: number = '0'.charCodeAt(0);
    private static _9: number = '9'.charCodeAt(0);

    private static _charsetMap: {[key: string]: number} = {
        0:   1252, // ANSI_CHARSET
        77:  10000, // Mac Roman
        78:  10001, // Mac Shift Jis
        79:  10003, // Mac Hangul
        80:  10008, // Mac GB2312
        81:  10002, // Mac Big5
        83:  10005, // Mac Hebrew
        84:  10004, // Mac Arabic
        85:  10006, // Mac Greek
        86:  10081, // Mac Turkish
        87:  10021, // Mac Thai
        88:  10029, // Mac East Europe
        89:  10007, // Mac Russian
        128: 932,  // SHIFTJIS_CHARSET
        129: 949,  // HANGEUL_CHARSET
        130: 1361, // JOHAB_CHARSET
        134: 936,  // GB2313_CHARSET
        136: 950,  // CHINESEBIG5_CHARSET
        161: 1253, // GREEK_CHARSET
        162: 1254, // TURKISH_CHARSET
        163: 1258, // VIETNAMESE_CHARSET
        177: 1255, // HEBREW_CHARSET
        178: 1256, // ARABIC_CHARSET
        186: 1257, // BALTIC_CHARSET
        204: 1251, // RUSSIAN_CHARSET
        222: 874,  // THAI_CHARSET
        238: 1250, // EE_CHARSET (Eastern European)
        254: 437,  // PC 437
        255: 850,  // OEM
    };

    private static _colorThemeMap: {[key: string]: null} = {
        // TODO
        maindarkone: null,
        mainlightone: null,
        maindarktwo: null,
        mainlighttwo: null,
        accentone: null,
        accenttwo: null,
        accentthree: null,
        accentfour: null,
        accentfive: null,
        accentsix: null,
        hyperlink: null,
        followedhyperlink: null,
        backgroundone: null,
        textone: null,
        backgroundtwo: null,
        texttwo: null,
    };

    public static log(message: string): void {
        // if (isLoggingEnabled) {
        //     // tslint:disable-next-line:no-console
        //     console.log(message);
        // }
    }

    // tslint:disable-next-line: sy-method-name
    public static _isalpha(str: string): boolean {
        const len = str.length;
        for (let i = 0; i < len; i++) {
            const ch = str.charCodeAt(i);
            if (!((ch >= this._A && ch <= this._Z) ||
                    (ch >= this._a && ch <= this._z))) {
                return false;
            }
        }
        return len > 0;
    }

    // tslint:disable-next-line: sy-method-name
    public static _isdigit(str: string): boolean {
        const len = str.length;
        for (let i = 0; i < len; i++) {
            const ch = str.charCodeAt(i);
            if (ch < this._0 || ch > this._9) {
                return false;
            }
        }
        return len > 0;
    }

    // tslint:disable-next-line: sy-method-name
    public static _parseHex(str: string): number {
        const len = str.length;
        for (let i = 0; i < len; i++) {
            const ch = str.charCodeAt(i);
            if (!((ch >= this._0 && ch <= this._9) ||
                    (ch >= this._a && ch <= this._f) ||
                    (ch >= this._A && ch <= this._F))) {
                return NaN;
            }
        }
        if (len > 0) {
            return parseInt(str, 16);
        }
        return NaN;
    }

    // tslint:disable-next-line: sy-method-name
    public static _blobToBinary(blob: ArrayBuffer): string {
        const view = new Uint8Array(blob);
        let ret = '';
        const len = view.length;
        for (let i = 0; i < len; i++) {
            ret += String.fromCharCode(view[i]);
        }
        return ret;
    }

    // tslint:disable-next-line: sy-method-name
    public static _hexToBlob(str: string): ArrayBuffer {
        let len = str.length;
        const buf = new ArrayBuffer(Math.floor(len-- / 2));
        const view = new Uint8Array(buf);
        let d = 0;
        for (let i = 0; i < len; i += 2) {
            const val = this._parseHex(str.substr(i, 2));
            if (isNaN(val)) {
                return null;
            }
            view[d++] = val;
        }
        return buf;
    }

    // tslint:disable-next-line: sy-method-name
    public static _hexToBinary(str: string): string {
        let bin = '';
        const len = str.length - 1;
        for (let i = 0; i < len; i += 2) {
            const val = this._parseHex(str.substr(i, 2));
            if (isNaN(val)) {
                return null;
            }
            bin += String.fromCharCode(val);
        }
        return bin;
    }

    // tslint:disable-next-line: sy-method-name
    public static _mapCharset(idx: number): number {
        return this._charsetMap[idx.toString()];
    }

    // tslint:disable-next-line: sy-method-name
    public static _mapColorTheme(name: string): any {
        return this._colorThemeMap[name];
    }

    // tslint:disable-next-line: sy-method-name
    public static _colorToStr(color: IColor): string {
        return 'rgb(' + color.r + ',' + color.g + ',' + color.b + ')';
    }

    // tslint:disable-next-line: sy-method-name
    public static _twipsToPt(twips: number): number {
        return Math.floor(twips / 20);
    }

    // tslint:disable-next-line: sy-method-name
    public static _twipsToPx(twips: number): number {
        return Math.floor(twips / 20 * 96 / 72);
    }

    // tslint:disable-next-line: sy-method-name
    public static _pxToTwips(px: number): number {
        return Math.floor(px * 20 * 72 / 96);
    }
}
