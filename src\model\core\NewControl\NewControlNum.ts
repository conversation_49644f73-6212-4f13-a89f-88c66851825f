import { NewControl } from './NewControl';
import DocumentContentBase from '../DocumentContentBase';
import { INewControlProperty, ResultType, NewControlErrorInfo, ErrorTextColor,
    isValidUnit,
    parseAndType,
    parseResultType,
    NewControlContentSecretType,
    IStructParamJson,
    AlignType,
    ICascadeEvent,
    NewControlErrorType} from '../../../common/commonDefines';
import ParaPortion from '../Paragraph/ParaPortion';
import { ChangeNewControlNumPrecision, ChangeNewControlNumUnit,
    ChangeNewControlNumMinValue, ChangeNewControlNumMaxValue,
    ChangeNewControlContentFixedLength,
    ChangeNewControlForceValidate,
    ChangeNewControlPrintChecked} from './NewControlChange';
import ParaTextExtend from '../Paragraph/ParaTextExtend';
import { ParaTextName } from '../Paragraph/ParagraphContent';

/**
 * 结构化元素: 文本框
 */
export class NewControlNumer extends NewControl {
    private maxValue: number;
    private minValue: number;
    private precision: number; // 精度
    private unit: string;
    private textReg: RegExp;
    private text: string;
    private unitPortion: ParaPortion;
    private errorInputInfo: string;
    private errorOutRangeInfo: string;
    private bUnValidNumber: boolean;
    private fixedLength: number;
    private bTextBorder: boolean;
    private alignments: AlignType;
    private forceValidate: boolean;
    private bPrintSelected: boolean;

    constructor(parent: DocumentContentBase, name: string, property: INewControlProperty, sText?: string ) {
        super(parent, name, property, sText && !isNaN(+sText) ? sText : null);
        this.maxValue = property.maxValue;
        this.minValue = property.minValue;
        this.precision = property.precision;
        this.unit = property.unit || '';
        this.bTextBorder = property.bTextBorder;
        this.fixedLength = property.newControlFixedLength;
        // property.newControlName = name;
        this.alignments = property.alignments;
        this.forceValidate = property.forceValidate;
        this.textReg = /^((?:-)?\d+(\.\d+)?)(.+)?$/i;
        this.bPrintSelected = property.printSelected == null ? false : property.printSelected;
        if (!property.bInsertFile) {
            this.setProperty(property, true);
        } else {
            this.loadProperty(property);
        }
    }

    public setProperty(property: INewControlProperty, bLoaded?: boolean): number {
        let res = !bLoaded ? super.setProperty(property) : ResultType.UnEdited;
        let num: number = ResultType.UnEdited;
        num = parseAndType(this.setMaxValue(property.maxValue));
        num = parseAndType(this.setMinValue(property.minValue)) & num;
        num = parseAndType(this.setPrecision(property.precision)) & num;
        num = parseAndType(this.setForceValidate(property.forceValidate)) & num;
        num = parseAndType(this.setFixedLength(property.newControlFixedLength)) && num;
        num = parseAndType(this.setTextBorder(property.bTextBorder)) && num;
        num = parseAndType(this.setAlignments(property.alignments)) && num;
        num = parseAndType(this.setEvent(property.eventInfo)) && num;
        num = parseAndType(this.setPrintSelected(property.printSelected)) & num;
        const unitType = this.setUnit(property.unit);
        if (unitType === ResultType.Failure || parseResultType(num) === ResultType.Success) {
            this.text = null;
            this.updateValidInput();
            // this.oldUnit = this.unit;
            return ResultType.Success;
        }
        // this.oldUnit = this.unit;
        res = parseAndType(res) & parseAndType(unitType) &  num;
        return parseResultType(res);
    }

    public getProperty(): INewControlProperty {
        const newControlProperty = super.getProperty();
        newControlProperty.minValue = this.minValue;
        newControlProperty.maxValue = this.maxValue;
        newControlProperty.precision = this.precision;
        newControlProperty.unit = this.unit;
        newControlProperty.forceValidate = this.forceValidate;
        newControlProperty.bTextBorder = this.isTextBorder();
        newControlProperty.alignments = this.alignments;
        newControlProperty.eventInfo = this.getEvent();
        newControlProperty.printSelected = this.bPrintSelected;

        return newControlProperty;
    }

    public setMaxLengthBgColor(color?: string): boolean {
        const startPortion = this.getStartBorderPortion();
        const endPortion = this.getEndBorderPortion();
        const contents = startPortion.paragraph.content;
        let bStart: boolean = false;
        for (let index = 0, length = contents.length; index < length; index++) {
            const portion = contents[index];
            if (portion === startPortion) {
                bStart = true;
                continue;
            }
            if (portion === endPortion) {
                break;
            }
            if (bStart) {
                portion.textProperty.setMaxLengthBackgroundColor(color);
            }
        }
        this.bUnValidNumber = color !== undefined;
        return true;
    }

    public setAlignments(type: AlignType): number {
        if (type === undefined || type === this.alignments) {
            return ResultType.UnEdited;
        }
        this.alignments = type;
        return ResultType.Success;
    }

    public getAlignments(): AlignType {
        return this.alignments;
    }

    public getFixedLength(): number {
        return this.fixedLength;
    }

    public setFixedLength( fixedLength: number ): number {
        if ( (fixedLength == null && fixedLength === this.fixedLength)
            || fixedLength === this.fixedLength ) {
            return ResultType.UnEdited;
        }

        const history = this.getHistory();
        if ( history ) {
            history.addChange(new ChangeNewControlContentFixedLength(this, this.fixedLength, fixedLength));
        }
        this.setDirty();
        this.fixedLength = fixedLength;
        return ResultType.Success;
    }

    public setEvent(event: ICascadeEvent): number {
        if (event === undefined) {
            return ResultType.UnEdited;
        }

        const cascadeManager = this.getCascadeManager();
        if (cascadeManager) {
            return cascadeManager.setEvent(event, this);
        }

        return ResultType.Failure;
    }

    public getEvent(): any {
        const cascadeManager = this.getCascadeManager();
        if (cascadeManager) {
            return cascadeManager.getEvent(this);
        }
        return;
    }

    public setTextBorder(flag: boolean): number {
        if (flag == null || flag === this.bTextBorder) {
            return ResultType.UnEdited;
        }

        this.bTextBorder = flag;

        return ResultType.Success;
    }

    public isTextBorder(): boolean {
        return this.bTextBorder;
    }

    public getValidNumberType(flagType?: number): NewControlErrorType {
        const text = (this.getNewControlText() || '')
            .trim();
        if (!text) {
            return NewControlErrorType.Success;
        }

        if (text === this.unit) {
            return NewControlErrorType.Success;
        }

        const matchs = this.textReg.exec(text);
        if (!matchs) {
            return NewControlErrorType.InvalidNumber;
        }

        if (!this.unit) {
            if (matchs[3]) {
                return NewControlErrorType.InvalidNumber;
            }
        }

        // if (flagType === 1) {
        //     return NewControlErrorType.Success;
        // }

        // if (text === this.text && !unit) {
        //     return NewControlErrorInfo.Success;
        // }

        // let result: string = '';
        const numStr = matchs[1];
        const num = Number.parseFloat(numStr);
        if (numStr && !Number.isNaN(num)) {
            const fixedNum: number = num;
            if (this.precision !== undefined) {
                const str = matchs[2];
                let count: number;
                if (str) {
                    count = str.length - 1;
                }
                // 根据需求，假如位数超出精准度才需要截断
                if (count != null && this.precision != null && count > this.precision) {
                    return NewControlErrorType.Precision;
                    // result = num.toFixed(this.precision);
                    // fixedNum = Number.parseFloat(result);
                }
                // if (count !== this.precision || count === undefined) {
                //     result = num.toFixed(this.precision);
                //     fixedNum = Number.parseFloat(result);
                // }
            }

            if (this.maxValue !== undefined) {
                if (fixedNum > this.maxValue) {
                    return NewControlErrorType.GreaterMaxValue;
                }
            }

            if (this.minValue !== undefined) {
                if (fixedNum < this.minValue) {
                    return NewControlErrorType.LessMinValue;
                }
            }
        }

        return NewControlErrorType.Success;
    }

    public setTextErrorBgColor(type: number): boolean {
        if (type === 1) {
            if (this.bUnValidNumber) {
                this.setMaxLengthBgColor();
                return true;
            }
            return false;
        }
        const res = this.isValidNumber();
        if (res !== NewControlErrorInfo.Success) {
            this.setMaxLengthBgColor(ErrorTextColor.Default);
            return true;
        } else if (this.bUnValidNumber) {
            this.setMaxLengthBgColor();
            return true;
        }
        return false;
    }

    public isErrorStatus(): boolean {
        return this.bUnValidNumber;
    }

    // public addContent(): void {
    //     if (typeof this.fixedLength !== 'number' || this.fixedLength <= 0) {
    //         return;
    //     }

    //     this.recalculate();
    // }

    public isValidNumber(bJump: boolean = true): string {
        const text = (this.getNewControlText() || '')
            .trim();
        if (!text) {
            return NewControlErrorInfo.Success;
        }

        if (text === this.unit) {
            return NewControlErrorInfo.Success;
        }

        // 强校验模式
        if (this.forceValidate === true) {
            //  - 合法
            if (text === '-') {
                return NewControlErrorInfo.Success;
            } else if (this.unit && text.indexOf(this.unit) !== -1) {
                return this.handleForceValidateError(bJump);
            }
        }

        const matchs = this.textReg.exec(text);
        if (!matchs) {
            if (this.forceValidate === true) {
                return this.handleForceValidateError(bJump);
            }
            return this.errorInputInfo || NewControlErrorInfo.InvalidNumber;
        }

        if (!this.unit) {
            if (matchs[3]) {
                if (this.forceValidate === true) {
                    return this.handleForceValidateError(bJump);
                }
                return this.errorInputInfo || NewControlErrorInfo.InvalidNumber;
            }
        }

        // if (text === this.text && !unit) {
        //     return NewControlErrorInfo.Success;
        // }

        let result: string = '';
        const numStr = matchs[1];
        const num = Number.parseFloat(numStr);
        if (numStr && !Number.isNaN(num)) {
            let fixedNum: number = num;
            if (this.precision != null) {
                const str = matchs[2];
                let count: number;
                if (str) {
                    count = str.length - 1;
                }
                // 根据需求，假如位数超出精准度才需要截断
                if (count != null && count > this.precision) {
                    result = num.toFixed(this.precision);
                    fixedNum = Number.parseFloat(result);
                }
                // if (count !== this.precision || count === undefined) {
                //     result = num.toFixed(this.precision);
                //     fixedNum = Number.parseFloat(result);
                // }
            }

            if (this.maxValue != null) {
                if (fixedNum > this.maxValue) {
                    return this.errorOutRangeInfo || (NewControlErrorInfo.GreaterMaxValue + this.maxValue);
                }
            }

            if (this.minValue != null) {
                if (fixedNum < this.minValue) {
                    return this.errorOutRangeInfo || (NewControlErrorInfo.LessMinValue + this.minValue);
                }
            }
        }

        return NewControlErrorInfo.Success;
    }

    /**
     * 校验输入字符是否正确
     * @return number 0: 校验正确，1：非法字符；2：小于最小值；3：大于最大值；4: 需要更新
     */
    public updateValidInput(): string {
        if (this.isPlaceHolderContent()) {
            return NewControlErrorInfo.Success;
        }
        let text = (this.getNewControlText() || '')
            .trim();
        if (!text) {
            return NewControlErrorInfo.Success;
        }

        const matchs = this.textReg.exec(text);
        if (!matchs) {
            return this.errorInputInfo || NewControlErrorInfo.InvalidNumber;
        }

        const unitStr = matchs[3];
        if (text === this.text && unitStr === this.unit) {
            return NewControlErrorInfo.Success;
        }

        let result: string = '';
        const numStr = matchs[1];
        const num = Number.parseFloat(numStr);
        if (numStr && !Number.isNaN(num)) {
            let fixedNum: number = num;
            if (this.precision !== undefined) {
                const str = matchs[2];
                let count: number;
                if (str) {
                    count = str.length - 1;
                }
                // 根据需求，假如位数超出精准度才需要截断
                if (count != null && count > this.precision) {
                    result = num.toFixed(this.precision);
                    fixedNum = Number.parseFloat(result);
                }
            } else if (this.unit && this.unit === unitStr) {
                return NewControlErrorInfo.Success;
            }

            // if (this.maxValue !== undefined) {
            //     if (fixedNum > this.maxValue) {
            //         return this.errorOutRangeInfo || NewControlErrorInfo.GreaterMaxValue;
            //     }
            // }

            // if (this.minValue !== undefined) {
            //     if (fixedNum < this.minValue) {
            //         return this.errorOutRangeInfo || NewControlErrorInfo.LessMinValue;
            //     }
            // }
        }

        let unit: string;
        if (this.unit && unitStr !== this.unit) {
            unit = this.unit;
        }

        if (result || unit !== undefined) {
            if (!result && !Number.isNaN(num)) {
                result = numStr;
            }
            text = result;
            this.addText(result);
            this.triggerCascade();
            this.setNewContentHidden();
            return NewControlErrorInfo.Refresh;
        }
        this.text = text + (unit || '');
        this.setNewContentHidden();
        return NewControlErrorInfo.Success;
    }

    public getUnit(): string {
        return this.unit;
    }

    public getPrecision(): number {
        let precision: any = this.precision;
        if (precision === '') {
            precision = undefined;
        }
        return precision;
    }

    public getForceValidate(): boolean {
        return this.forceValidate;
    }

    public getMaxValue(): number {
        let maxValue: any = this.maxValue;
        if (maxValue === '') {
            maxValue = undefined;
        }
        return maxValue;
    }

    public getMinValue(): number {
        let minValue: any = this.minValue;
        if (minValue === '') {
            minValue = undefined;
        }
        return minValue;
    }

    public getNumber(): number {
        let text = (this.getNewControlText() || '').trim();
        if (this.unit) {
            text = text.replace(this.unit, '')
            .trim();
        }

        if (text === '') {
            return ResultType.NumberNaN;
        }

        // todo: 超过十万亿的位数（15位）后，数据会有问题
        return +text;
    }

    public setErrorInputInfo(info: string): number {
        if (this.errorInputInfo === info) {
            return ResultType.UnEdited;
        }

        this.errorInputInfo = info;
        return ResultType.Success;
    }

    public setErrorOutRangeInfo(info: string): number {
        if (this.errorOutRangeInfo === info) {
            return ResultType.UnEdited;
        }
        this.errorOutRangeInfo = info;
        return ResultType.Success;
    }

    public setNewControlText(sText: string, bRefresh?: boolean): number {
        const res = super.setNewControlText(sText, false);
        if (res === ResultType.Success && this.unit) {
            const unit = this.unit;
            this.unit = null;
            const res1 = this.setUnit(unit, bRefresh);
            if (res1 !== ResultType.Success && bRefresh === true) {
                this.recalculate();
            }
        } else if (bRefresh === true) {
            this.recalculate();
        }

        return res;
    }

    /**
     * 向数值框插入值
     * @param num 插入的数值
     * @return number 0: 校验正确，1：非法字符；2：小于最小值；3：大于最大值；4: 需要更新
     */
    public setNumberBoxText(num: number, bRefresh?: boolean, bNoDirty?: boolean): number {
        if (isNaN(num)) {
            return 1;
        }

        let result: string;
        if (this.precision !== undefined && num !== Math.ceil(num)) {
            result = num.toFixed(this.precision);
            num = +result;
        }

        if (this.maxValue !== undefined && this.maxValue < num) {
            return 3;
        }

        if (this.minValue !== undefined && this.minValue > num) {
            return 2;
        }

        if (result === undefined) {
            result = num + '';
        }

        // if (this.unit !== undefined) {
        //     result += this.unit;
        // }

        this.addText(result, bRefresh);
        // if (bRefresh !== false) {
        //     this.recalculate();
        // }
        this.setTextFlag(true);
        this.triggerCascade();
        if (!bNoDirty) {
            this.setDirty();
        }
        return 0;
    }

    public setMaxValue(value: number): number {
        if (value === undefined || value === this.maxValue) {
            return ResultType.UnEdited;
        }

        if (typeof value !== 'number') {
            value = undefined;
            if (value === this.maxValue) {
                return ResultType.UnEdited;
            }
        }

        const minValue = this.minValue;
        if (typeof value === 'number' && typeof minValue === 'number' && value <= minValue) {
            return ResultType.Failure;
        }

        const history = this.getHistory();
        if ( history ) {
            history.addChange(new ChangeNewControlNumMaxValue(this, this.maxValue, value));
        }
        this.maxValue = value;
        this.setDirty();
        return ResultType.Success;
    }

    public setMinValue(value: number): number {
        if (value === undefined || value === this.minValue) {
            return ResultType.UnEdited;
        }

        if (typeof value !== 'number') {
            value = undefined;
            if (value === this.minValue) {
                return ResultType.UnEdited;
            }
        }

        const maxValue = this.maxValue;
        if (typeof value === 'number' && typeof maxValue === 'number' && value >= maxValue) {
            return ResultType.Failure;
        }

        const history = this.getHistory();
        if ( history ) {
            history.addChange(new ChangeNewControlNumMinValue(this, this.minValue, value));
        }
        this.minValue = value;
        this.setDirty();
        return ResultType.Success;
    }

    public setPrecision(value: number): number {
        if (value === undefined || value === this.precision) {
            return ResultType.UnEdited;
        }

        if (typeof value !== 'number') {
            value = undefined;
            if (value === this.precision) {
                return ResultType.UnEdited;
            }
        }

        const history = this.getHistory();
        if ( history ) {
            history.addChange(new ChangeNewControlNumPrecision(this, this.precision, value));
        }
        this.precision = value;
        this.setDirty();
        return ResultType.Success;
    }

    public setForceValidate(value: boolean): number {
        if (value === undefined || value === this.forceValidate) {
            return ResultType.UnEdited;
        }

        const history = this.getHistory();
        if ( history ) {
            history.addChange(new ChangeNewControlForceValidate(this, this.forceValidate, value));
        }
        this.forceValidate = value;
        this.setDirty();
        return ResultType.Success;
    }

    public setUnit(value: string, bRefresh?: boolean): number {
        if (value == null || value === this.unit) {
            return ResultType.UnEdited;
        }

        if (!isValidUnit(value)) {
            this.unit = value;
            return ResultType.Failure;
        }

        const history = this.getHistory();
        if ( history ) {
            history.addChange(new ChangeNewControlNumUnit(this, this.unit, value));
        }
        this.updateUintPortion(value, bRefresh);
        this.unit = value;
        this.updateUnit(value);
        this.setDirty();
        return ResultType.Success;
    }

    public setNewControlTextByJson(json: IStructParamJson): number {
        const text = json.content_text;
        if (text == null) {
            return ResultType.UnEdited;
        }
        if (!text || isNaN(text)) {
            super.setNewControlText(text, false);
            return ResultType.Success;
        }
        return this.setNumberBoxText(+text, false);
    }

    public isPrintSelected(): boolean {
        return this.bPrintSelected;
    }

    public getPrintSelected(): boolean {
        return this.bPrintSelected;
    }

    public setPrintSelected(bPrintSelected: boolean): number {
        if (this.bPrintSelected === bPrintSelected || bPrintSelected == null) {
            return ResultType.UnEdited;
        }

        const history = this.getHistory();
        if ( history ) {
            history.addChange(new ChangeNewControlPrintChecked(this, this.bPrintSelected, bPrintSelected));
        }

        this.bPrintSelected = bPrintSelected;
        this.setDirty();
        return ResultType.Success;
    }

    public toPrint(): void {
        super.toPrint();
        if (this.isHidden()) {
            return;
        }

        if (this.bPrintSelected) {
            this.setHidden(true, false);
        }
    }

    private handleForceValidateError(bJump: boolean = true): NewControlErrorInfo {
        if (bJump) {
            const doc = this.getDocumentParent() ? this.getDocumentParent()
                                    .getDocument() : null;
            // console.log(doc)
            doc.jumpInStruct(this.getNewControlName(), 3);
        }
        return NewControlErrorInfo.ForceValidateErr;
    }

    private createUnitPortion(unit: string, bRefresh: boolean = false): number {
        const endBorderPortion = this.getEndBorderPortion();
        const para = endBorderPortion.paragraph;
        const contents = para.content;
        const index = contents.findIndex((item) => item === endBorderPortion);
        const bPlaceHolder = this.isPlaceHolderContent();

        const newPortion = new ParaPortion(para);
        newPortion.addText(unit.slice(1));
        const text = new ParaTextExtend(unit.slice(0, 1), ParaTextName.PortionStyle);
        newPortion.addToContent(0, text);
        if (!bPlaceHolder) {
            // 对单位进行字体格式进行赋值，只有在当前不是占位符的时候进行
            const lastPortion = contents[index - 1];
            if (lastPortion) {
                const textProperty = lastPortion.textProperty.copy();
                newPortion.textProperty = textProperty;
            }
            para.addToContent(index, newPortion);
        }

        if ( this.isTrackRevisions() ) {
            newPortion.resetReviewTypeAndInfo();
        }

        this.unitPortion = newPortion;
        if (bRefresh) {
            this.recalculate();
        }

        return ResultType.Success;
    }

    private removeUnitPortion(bRefresh: boolean = false): number {
        const unitPortion = this.unitPortion;
        if (!unitPortion) {
            return ResultType.UnEdited;
        }
        const contents = unitPortion.paragraph.content;
        const index = contents.findIndex((item) => item === unitPortion);
        if (index === -1) {
            return ResultType.Failure;
        }
        contents.splice(index, 1);
        this.unitPortion = null;
        if (bRefresh) {
            this.recalculate();
        }
        return ResultType.Success;
    }

    private updateUintPortion(unit: string, bRefresh: boolean = false): number {
        // const oldUnit = this.unit;
        // if (oldUnit === unit) {
        //     return ResultType.UnEdited;
        // }

        if (!unit) {
            return this.removeUnitPortion(bRefresh);
        }

        const unitPortion = this.unitPortion;
        if (unitPortion) {
            const endBorderPortion = this.getEndBorderPortion();
            const para = endBorderPortion.paragraph;
            const contents = para.content;
            const index = contents.findIndex((item) => item === endBorderPortion);
            const oldUnit = contents[index - 1];
            const bInsert = oldUnit === unitPortion;
            if (bInsert) {
                const text = oldUnit.getText();
                if (text === unit) {
                    return ResultType.UnEdited;
                }
            }

            const firstText = new ParaTextExtend(unit.charAt(0), ParaTextName.PortionStyle);
            unitPortion.content = [];
            unitPortion.addText(unit.slice(1));
            unitPortion.addToContent(0, firstText);

            if (!bInsert && !this.isPlaceHolderContent()) {
                if (!unitPortion.paragraph) { // 只有第一次未绑定段落的时候才需要进行字体格式赋值，否则会影响上一次保存的样式
                    const lastPortion = contents[index - 1];
                    if (lastPortion) {
                        const textProperty = lastPortion.textProperty.copy();
                        unitPortion.textProperty = textProperty;
                    }
                }

                para.addToContent(index, unitPortion);
            }

            if ( this.isTrackRevisions() ) {
                unitPortion.resetReviewTypeAndInfo();
            }

            if (bRefresh) {
                this.recalculate();
            }
            return ResultType.Success;
        }

        return this.createUnitPortion(unit, bRefresh);
        // if (res === ResultType.UnEdited) {
        //     return res;
        // }
        // return res;
    }

    private updateUnit(unit: string): number {
        if (unit) {
            return;
        }

        const text = (this.getNewControlText() || '')
            .trim();
        if (!text) {
            return ResultType.Success;
        }

        const matchs = this.textReg.exec(text);
        if (!matchs) {
            return ResultType.UnEdited;
        }

        this.addText(matchs[1]);
    }

    private addText(result: string, bRefresh?: boolean): void {
        this.setTextFlag(true);
        const startBorderPortion = this.getStartBorderPortion();
        if (startBorderPortion.bPlaceHolder === true) {
            startBorderPortion.setPlaceHolder(false);
        }
        this.removeNewControlContent(false);
        const para = startBorderPortion.paragraph;
        const newPortion = new ParaPortion(para);
        this.text = result;
        newPortion.addText(result);
        newPortion.textProperty = startBorderPortion.textProperty.copy();
        let post = para.content.findIndex((item) => item === startBorderPortion);
        para.addToContent(++post, newPortion);
        if (this.unit && this.forceValidate !== true) {
            this.updateUintPortion(this.unit);
        }
        const type = this.getViewSecretType();
        if ( type === NewControlContentSecretType.AllSecret && (result + this.text)) {
            this.getNewControlContent()
            .setViewSecret(type);
        }
        if (this.isHidden()) {
            this.setNewContentHidden();
        }

        if (bRefresh !== false) {
            this.recalculate();
        }
    }

    private loadProperty(property: INewControlProperty): void {
        // super.setProperty(property);
        this.setMaxValue(property.maxValue);
        this.setMinValue(property.minValue);
        this.setPrecision(property.precision);
        this.setForceValidate(property.forceValidate);
        this.setFixedLength(property.newControlFixedLength);
        this.setTextBorder(property.bTextBorder);
        this.setAlignments(property.alignments);
        this.loadEvent(property.eventInfo);
        this.setUnit(property.unit);
    }

    private loadEvent(event: ICascadeEvent): void {
        if (event === undefined) {
            return ;
        }

        // let eventInfo: ICascadeEvent;
        // if (event) {
        //     eventInfo = event;
        //     eventInfo.action = event.action;
        //     eventInfo.target = event.target;
        //     eventInfo.key = event.key;
        //     eventInfo.event = (event.event || []).map((item) => {
        //         return {...item};
        //     });
        // }

        // this.eventInfo = eventInfo;
        this.setEvent(event);
    }
}
