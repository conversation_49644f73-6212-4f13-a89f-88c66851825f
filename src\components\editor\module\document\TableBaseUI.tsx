import React from 'react';
import { IRefreshPageData } from '../../../../model/DocumentCore';
import { numtoFixed, RenderSectionBackgroundType } from '../../../../common/commonDefines';
import { IDocumentTable } from '../../../../model/TableProperty';
import Table from './Table';
import { TableCellContent } from './TableCellContent';

interface IProps {
    content: IDocumentTable;
    className?: string;
    scale?: number;
    pageIndex?: number;
    host?: any;
    bFixedTable?: boolean;
    nHeaderFooter?: number;
    bShowContent?: boolean; // 是否显示水印
}

export class TableBaseUI extends React.Component<IProps, {}> {
    private _cellRefs: any;
    private _content: IDocumentTable;
    private scale: number;
    private documentCore: any;
    private _cellWarnBorder: any;
    constructor(props: IProps) {
        super(props);
        this.documentCore = props.host.documentCore;
    }

    public render(): any {
        this.scale = this.props.scale;
        let content = this.props.content;
        if (this._content) {
            content = this._content;
        }

        return (
            <React.Fragment>
                {this.renderTableContent(content)}
                {this.renderTable(content)}
                {this.renderFixedTable(content)}
                {this._cellWarnBorder}
            </React.Fragment>
        );
    }

    public refresh(options: IRefreshPageData): boolean {
        if (!options) {
            return false;
        }
        if (options.ids.length > 1) {
            const id = options.ids[1];
            const ref = this._cellRefs[id];
            if (ref && ref.current) {
                const content = options.content;
                return ref.current.refresh({paras: content.paragraphs, cellId: content.id});
            }
            return false;
        }
        this._content = options.content;
        this.setState({}, () => {
            this._content = null;
        });
        return true;
    }

    private renderFixedTable(content: IDocumentTable): any {
        return null;
    }

    private renderTable(item: IDocumentTable): any {
        const scale = this.scale;
        const bFixedTable = this.props.bFixedTable;
        return (
            <Table
                key={item.id}
                id={item.id}
                index={item.index}
                x={item.x}
                y={item.y}
                height={item.height}
                width={item.width}
                scale={scale}
                bFixedTable={bFixedTable}
                // content={item.content}
                tableBorderLines={item.tableBorderLines}
                tableBackground={item.tableBackground}
                tableCells={item.tableCells}
                bNISTable={item.bNISTable}
            />
        );
    }

    private renderTableContent(content: IDocumentTable): any {
        const result = {
            paraContainer: [],
            tableCellBackground: [],
            tablecell: [],
            fontBg: [],
            fontText: [],
            newControlFocus: [],
            mask: [],
            paras: [],
            cellName: [],
            cellContent: [],
        };
        this._cellRefs = {};
        this.renderTableCells(content, result);

        let mask;
        if (result.mask.length) {
            mask = (
            <defs>
                {result.mask}
            </defs>
            );
        }
        let tablecell;
        let tableCellBackground;
        if (result.tableCellBackground.length) {
            tableCellBackground = <g className={'tablecellBackground'}>{result.tableCellBackground}</g>;
        }
        if (result.tablecell.length) {
            tablecell = <g className={'tablecell-container'}>{result.tablecell}</g>;
        }

        let cellNames;
        if (result.cellName.length) {
            cellNames = result.cellName;
        }

        return (
            <React.Fragment>
                {mask}
                {tableCellBackground}
                {/* {tablecell} */}
                {result.cellContent}
                {cellNames}
            </React.Fragment>
        );
    }

    private renderTableCells = (table: IDocumentTable, result: any) => {
        const scale = this.scale;
        // const bFromHeaderFooter = this.props.bFromHeaderFooter;
        const bFixedTable: boolean = this.props.bFixedTable;
        const colors = ['#fff', 'none'];
        const nHeaderFooter = this.props.nHeaderFooter;
        const warnRect = [];
        const tableId: number = table.id;
        // const pageIndex = this.props.pageIndex;
        table.tableCells.forEach((cell) => {
            if (bFixedTable !== true && cell.bFixed === true || isNaN(cell.height)) {
                return;
            }
            // const { id, height, width, positionX, positionY } = items;
            const {pageIndex, id, cellName, bMask} = cell;
            let {height, width, x, y} = cell;
            height = numtoFixed(height * scale);
            width = numtoFixed(width * scale);
            x = numtoFixed(x * scale);
            y = numtoFixed(y * scale);
            const props = {
                height,
                width, //  would be overridden
                x, // would be overridden
                y,
            };

            const props2 = {
                height,
                width, //  would be overridden
                x, // would be overridden
                y,
                cellName,
            };
            const className = `selection ${RenderSectionBackgroundType.TableCellSelection}-${id}-${pageIndex}`;
            // const url = 'url(#' + cell.id + ')';
            if (cell.cellBackground && !colors.includes(cell.cellBackground)) {
                result.tableCellBackground.push(
                    <rect
                        key={id}
                        width={width}
                        height={height}
                        x={x}
                        y={y}
                        data-key={id}
                        fill={cell.cellBackground || '#fff'}
                    />
                );
            }

            if (cell.bWarnBorder) {
                warnRect.push(
                    <rect
                        key={id}
                        width={width}
                        height={height}
                        x={x}
                        y={y}
                        data-id={cell.cellId}
                        className='numberCell-valid-rect'
                        fill={'none'}
                    />
                );
            }

            result.tablecell.push(
                <rect key={id} className={className} cell-key={id} {...props}>
                    {id}
                </rect>
            );
            const cellId = cell.cellId;
            this._cellRefs[cellId] = React.createRef();
            const host = this.props.host;
            const gridLine = cell.gridLine
            result.cellContent.push(
                <TableCellContent
                    host={host}
                    key={id}
                    ref={this._cellRefs[cellId]}
                    paras={cell.content.paragraphs}
                    cellId={id}
                    option={{cellBoundX: cell.x + cell.width, cellBoundY: cell.y + cell.height, x: cell.x, y: cell.y, gridLine, index: cell.index, tableId, cellId}}
                    pageIndex={pageIndex}
                    scale={scale}
                    nHeaderFooter={nHeaderFooter}
                    cellRect={(bMask ? {x, y, height, width} : null)}
                    bShowContent={this.props.bShowContent}
                    documentCore={this.props.host.documentCore}
                />
            );

            if ( true ) {
                const headerFooter = (nHeaderFooter === 1) ? '-header' : ((nHeaderFooter === 2) ? '-footer' : '');
                const mask = (
                <clipPath id={'mask' + id + headerFooter} key={'mask' + id + headerFooter}>
                    <rect key={id + '-mask'} x={x} y={y} width={width} height={height} fill='#666' />
                </clipPath>
                );
                result.mask.push(mask);
            }
            const cellNames = this.renderTableCellNames(props2, id);
            if (cellNames) {
                result.cellName.push(cellNames);
            }
        });

        if (warnRect.length > 0) {
            this._cellWarnBorder = (<React.Fragment>{warnRect}</React.Fragment>);
        } else {
            this._cellWarnBorder = null;
        }
    }

    private renderTableCellNames(props: any, id?: any): any {
        if ( this.documentCore.isShowTableCellNames() ) {
            const unit = 16;
            const size = (null != props.cellName ? (unit + (props.cellName.length - 1) * 6) : unit) * this.scale;
            const props2 = {
                height: unit,
                width: size, //  would be overridden
                x: props.x + props.width - size, // would be overridden
                y: props.y,
                fillOpacity: 0.2,
                fill: '#fff000'
            };
            return (
                <React.Fragment key={id}>
                    <rect {...props2}  />
                    <text
                        x={props.x + props.width - size}
                        y={props.y + unit}
                        fontFamily={'宋体'}
                        fontSize={unit - 2}
                        fontWeight={'normal'}
                        fontStyle={'normal'}
                        fill={'#000000'}
                        fillOpacity={0.2}
                    >
                        {props.cellName}
                    </text>
                </React.Fragment>
            );
        } else {
            return null;
        }
    }
}
