import * as React from 'react';
import Dialog from '../../ui/Dialog';
import {message} from '../../../../common/Message';
import Input from '../../ui/Input';
import Button from '../../ui/Button';
import { IExternalDataProperty, isValidName } from '../../../../common/commonDefines';
import { IFRAME_MANAGER } from '../../../../common/IframeManager';
import { ExternalDataBind } from './ExternalDataBind';
import CheckboxItem from '../../ui/CheckboxItem';
import Select from '../../ui/select/Select';
import { genarateBarcode } from '@/common/commonMethods';

interface IProps {
    documentCore: any;
    visible?: boolean;
    id: string;
    image?: any;
    close: (name: string | number, bRefresh?: boolean) => void;
}

interface IState {
    bRefresh: boolean;
}

export default class Barcode extends React.Component<IProps, IState> {
    private image: {imageName: string, imageSource: string, imageWidth: number, imageHeight: number,
        content: string, bUse: boolean, textAlign: string,
        externalDataBind: IExternalDataProperty};

    // private showImageError: string = '';
    // private imageName: string;
    // private imageSizeRatio: number;
    // private timeout: any;
    private visible: boolean;
    private resetSourceBind: boolean;
    private dataBind: any;
    private alignments: any[];
    private bInputValid: boolean;
    constructor(props: any) {
        super(props);
        this.state = {
            bRefresh: false,
        };
        this.image = {
            imageName: '',
            imageSource: undefined,
            imageWidth: 0,
            imageHeight: 0,
            content: '',
            bUse: true,
            externalDataBind: undefined,
            textAlign: 'center',
        };
        // this.imageSizeRatio = 1;
        this.alignments = [
            {
                key: '左对齐',
                value: 'left',
            },
            {
                key: '右对齐',
                value: 'right',
            },
            {
                key: '居中',
                value: 'center',
            },
        ];
        this.visible = this.props.visible;
    }

    public render(): any {
        // TODO: bSignPic?
        const visibility = this.image.imageSource ? 'visible' : 'hidden';
        return (
            <Dialog
                visible={this.visible}
                width={320}
                open={this.open}
                title={'设置'}
                footer={this.renderFooter()}
                id={this.props.id}
            >
                <div>
                    <div className='editor-line'>
                        <div className='w-70'>名称</div>
                        <div className='right-auto'>
                            <Input
                                type="text"
                                value={this.image.imageName}
                                onChange={this.onChange}
                                name='imageName'
                                spellCheck="false"
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <div className='w-70'>内容</div>
                        <div className='right-auto'>
                            <Input
                                value={this.image.content}
                                onChange={this.onChange}
                                name='content'
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <div className='w-70'>大小</div>
                        <div className='w-020'>宽</div>
                        <div className='right-auto-90' >
                            <Input
                                type='number'
                                min={1}
                                value={this.image.imageWidth}
                                onChange={this.onChange}
                                name='imageWidth'
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <div className='w-70'></div>
                        <div className='w-020'>高</div>
                        <div className='right-auto-90'>
                            <Input
                                value={this.image.imageHeight}
                                type='number'
                                min={1}
                                max={160}
                                onChange={this.onChange}
                                name='imageHeight'
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <div className='w-70'>文本</div>
                        <div className='right-auto'>
                            <CheckboxItem
                                value={this.image.bUse}
                                name='bUse'
                                onChange={this.onChange}
                            >
                                显示文本
                            </CheckboxItem>
                        </div>
                    </div>
                    <div className='editor-line'>
                        <div className='w-70'></div>
                        <div className='right-auto'>
                            <Select
                                name='textAlign'
                                onChange={this.onChange}
                                disabled={true !== this.image.bUse}
                                data={this.alignments}
                                value={this.image.textAlign}
                            />
                        </div>
                    </div>
                    <div className='editor-line editor-multi-line'>
                        <div className='w-70'>预览</div>
                        <div className='right-auto' style={{marginTop: 5, marginBottom: 5, border: '1px solid #D5D4DC'}}>
                            <img id='barcode' width='100%' height={90} style={{visibility}} src={this.image.imageSource} />
                        </div>
                    </div>
                    <ExternalDataBind
                        name={this.image.imageName}
                        id='externalDataBind'
                        visible={this.visible}
                        documentCore={this.props.documentCore}
                        onChange={this.onChange}
                        close={this.onClose}
                        properties={this.dataBind}
                        resetId={'resetSourceBind'}
                        bBarcode={true}
                    />
                </div>
            </Dialog>
        );
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        this.visible = nextProps.visible;
    }

    private renderFooter(): any {
        return (
            <span>
                <Button onClick={this.close}>取消</Button>
                <Button type='primary' onClick={this.confirm}>确认</Button>
            </span>
        );
    }

    private onClose = (id: string, bRefresh: boolean): void => {
        this[id] = false;
        if ('externalDataBind' === id && bRefresh) {
            this.resetSourceBind = false;
            this.setState({bRefresh: !this.state.bRefresh});
        }
    }

    private close = (bRefresh?: boolean): void => {
        this.visible = false;
        this.setState({bRefresh: !this.state.bRefresh});
        this.props.close(this.props.id, bRefresh);
    }

    private open = (): void => {
        const { image, documentCore }= this.props;
        if (!image) {
            this.image = {
                imageName: documentCore.getBarcodeName(),
                imageSource: undefined,
                imageWidth: 160,
                imageHeight: 50,
                content: '',
                bUse: true,
                externalDataBind: undefined,
                textAlign: 'center',
            };
        } else {
            this.image.imageName = image.name;
            this.image.content = image.content;
            this.image.imageSource = image.src;
            this.image.imageWidth = image.width;
            this.image.imageHeight = image.height;
            this.image.externalDataBind = image.externalDataBind;
            this.image.bUse = image.bUse;
            this.image.textAlign = image.textAlign;
            this.dataBind = image.externalDataBind;
        }

        this.resetSourceBind = false;
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private onChange = (value: any, name: string): void => {
        if ('resetSourceBind' !== name) {
            if ('externalDataBind' !== name) {
                this.image[name] = value;
                if (name === 'content' || 'bUse' === name || 'textAlign' === name || name === 'imageHeight') {
                    this.genarate();
                    this.setState({bRefresh: !this.state.bRefresh});
                }
            } else {
                this.dataBind = value;
            }
        } else {
            this.resetSourceBind = true;
        }
    }

    private valid = (e: any): void => {
        this.bInputValid = e;
    }

    private confirm = (): void => {
        const documentCore = this.props.documentCore;
        if (documentCore.isProtectedMode()) {
            this.close(true);
            return;
        }
        IFRAME_MANAGER.setDocId(documentCore.getCurrentId());
        const image = this.image;
        if (!isValidName(image.imageName)) {
            message.error('名称不符合规范，请重新输入');
            return;
        }
        if ((!this.props.image && !documentCore.checkUniqueImageName(image.imageName)) ||
            (this.props.image && !documentCore.checkUniqueImageNameOtherThanSelectedImage(image.imageName))) {
            message.error('名称已存在，请重新命名');
            return null;
        }
        if (!image.imageWidth || !image.imageHeight || isNaN(image.imageWidth) || isNaN(image.imageHeight)) {
            message.error('请重新设置高/宽');
            return;
        }

        if (this.resetSourceBind) {
            this.image.externalDataBind = {
                sourceObj: '',
                sourceKey: '',
                bReadOnly: false,
                commandUpdate: 1,
            }

            this.resetSourceBind = false;
        } else if (this.dataBind) {
            this.image.externalDataBind = this.dataBind;
            this.dataBind = undefined;
        }

        if (this.props.image) {
            if (false === this.bInputValid) {
                message.error('输入内容非法，只能包含ASCII字符（数字、大小写字母及常用符号）');

                return;
            }

            documentCore.setDrawingProp(this.props.image.name, {name: image.imageName,
                width: image.imageWidth, height: image.imageHeight, content: image.content,
                bUse: image.bUse, textAlign: image.textAlign, externalDataBind: image.externalDataBind,
                src: image.imageSource
            });
        } else {
            if (!this.image.imageSource) {
                this.genarate();
            }

            if (false === this.bInputValid) {
                message.error('输入内容非法，只能包含ASCII字符（数字、大小写字母及常用符号）');

                return;
            }

            const datas = {
                content: image.content,
                bUse: image.bUse,
                textAlign: image.textAlign,
                externalDataBind: image.externalDataBind,
            }

            documentCore.addInlineImage(image.imageWidth, image.imageHeight, image.imageSource, image.imageName,
                3, image.imageSource, null, null, datas);
        }

        this.close(true);
    }

    private genarate(): void {
        this.image.imageSource = genarateBarcode({
            content: this.image.content,
            height: this.image.imageHeight,
            textAlign: this.image.textAlign,
            bUse: this.image.bUse,
            drawingObjects: this.props.documentCore.getDrawingObjects(),
            valid: this.valid,
        });
    }
}
