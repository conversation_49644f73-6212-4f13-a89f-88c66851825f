#!/bin/bash

set -x

mkdir -p ./dist
cp package.lib.json ./dist/package.json


export BRANCH_TAG=$(echo "$CI_COMMIT_REF_SLUG" | grep -Po "(?<=release\/)(.*)$")

(echo "$CI_COMMIT_REF_SLUG" | grep -Eq "^release\/") && DEV_V= || DEV_V=.dev0
export BUILD="$CI_JOB_ID$DEV_V"
export CURRENT_VER=$(jq -r '.version' package.json)
export CURRENT_MAIN_VER="${CURRENT_VER%?}"
export NEW_VER="$CURRENT_MAIN_VER$BUILD"
[[ ! -z $BRANCH_TAG ]] && NEW_VER="$NEW_VER-$BRANCH_TAG"

export VER_TEMPLATE=$(jq '.version="${NEW_VER}"' ./dist/package.json)
echo $VER_TEMPLATE | envsubst > ./dist/package.json



cat ./dist/package.json