import { Document, SectionPropertiesOptions } from './document';
import { Settings } from './settings';
import { Styles } from './styles';
import { DefaultStylesFactory } from './styles/factory';
import { Footer } from './footer';
import { Header } from './header';
import { Paragraph } from './paragraph';
import { IDefaultParaProperty, IDefaultTextProperty, ISettingsInfoDefaults } from '../../../model/DocumentCore';
// import { IXmlProps } from '../../../components/editor/Editor';
import { Media } from './media/media';
import { Region } from './region';
import { Table } from './table';
import { NISTable } from './table/nis-table';
import { dateFormat, IEditorBackground } from '../../../common/commonDefines';
import { Cascade } from './cascade';
import { IFRAME_MANAGER } from '@/common/IframeManager';

export class File {
    public structs: {start: {[name: string]: boolean}, end: {[name: string]: boolean}, type: {[name: string]: number}};
    public get Document(): Document {
        return this.document;
    }

    public get Styles(): Styles {
        return this.styles;
    }

    public get Settings(): Settings {
        return this.settings;
    }

    public get Media(): Media {
        return this.media;
    }

    public get Footer(): Footer {
        return this.footer;
    }

    public get Headers(): Header[] {
        return this.headers;
    }

    public get Html(): string {
        return this.html;
    }

    public get Cascade(): Cascade {
        return this.cascade;
    }

    public get SourceBind(): string {
        return this.sourceBind;
    }

    public get NISTableJson(): string {
        return this.nisTableJson;
    }

    private readonly document: Document;
    private readonly settings: Settings;
    // private readonly footer: Footer;
    private header: Header;
    private headers: Header[] = [];
    private footer: Footer;
    private styles: Styles;
    private media: Media;
    private html: string;
    private cascade: Cascade;
    private sourceBind: string;
    private nisTableJson: string;

    constructor() {
        this.document = new Document();
        this.settings = new Settings();
        this.structs = {start: {}, end: {}, type: {}};
        // fileProperties.template ?
        // this.styles = new Styles();
        const stylesFactory = new DefaultStylesFactory();
        this.styles = stylesFactory.newInstance();
        this.media = new Media();

        // only 1 footer
        this.footer = new Footer();

        // header
        // const headers = new HeaderWrapper();
        this.header = new Header();
        // this.cascade = new Cascade();
        // this.headers.push(this.header);
        // console.log(this.headers)
        // this.docRelationships.createRelationship(
        //     header.Header.ReferenceId,
        //     "http://schemas.openxmlformats.org/officeDocument/2006/relationships/header",
        //     `header${this.headers.length}.xml`,
        // );
        // this.contentTypes.addHeader(this.headers.length);
        this.html = '';
        this.sourceBind = '';
        this.nisTableJson = '';
    }

    public verifyUpdateFields(): void {
        //
    }

    public addParagraph(paragraph: Paragraph): File {
        this.document.addParagraph(paragraph);
        return this;
    }

    public createParagraph(text?: string): Paragraph {
        return this.document.createParagraph(text);
    }

    public addRegion(region: Region): File {
        this.document.addRegion(region);
        return this;
    }

    public addTable(table: Table): File {
        this.document.addTable(table);
        return this;
    }

    public addNISTable(table: NISTable): File {
        this.document.addNISTable(table);
        return this;
    }

    public createHeader(): Header {
        const header = new Header();
        this.addHeaderToDocument(header);

        // set header style info
        this.Styles.setHeaderStyleInfo(this.headers);

        return header;
    }

    public createCascade(): Cascade {
        const cascade = new Cascade();
        this.cascade = cascade;
        return cascade;
    }

    public setHeaderPageRange(headerIndex: number, startPage: number, endPage: number): void {
        this.Styles.setHeaderPage(headerIndex, startPage, endPage);
    }

    public createFooter(): Footer {
        const footer = new Footer();
        this.addFooterToDocument(footer);
        return footer;
    }

    public setDefaultTextProperty(DefaultTextProperty: IDefaultTextProperty): void {
        this.styles.setDefaultTextProperty(DefaultTextProperty);
    }

    public setDefaultParaProperty(DefaultParaProperty: IDefaultParaProperty): void {
        this.styles.setDefaultParaProperty(DefaultParaProperty);
    }

    public setEditorBackground(editorBackground: IEditorBackground): void {
        this.styles.setEditorBackground(editorBackground);
    }

    public setDynamicGridLine(bLine: boolean): void {
        this.styles.setDynamicGridLine(bLine ? 1 : 0);
    }

    public setSettings(props: any): void {
        const {documentCore} = props;
        const properties = props.properties || new Map();
        const defaultSettingsInfo: ISettingsInfoDefaults = documentCore.getSettingsInfoDefaults();
        const {application, version} = defaultSettingsInfo;
        let protectMode = defaultSettingsInfo.protectMode;
        const pages = documentCore.getDocument().pages.length;
        protectMode = documentCore.isProtectedMode() ? 1 : 0;
        this.settings.setInfo(pages, application, version, protectMode);

        const curDateStr = dateFormat(new Date(), 'yyyy-MM-dd HH:mm:ss');
        // const curDateStr = new Date(curDate.getTime() - curDate.getTimezoneOffset() * 60 * 1000).toISOString();
        let createdTime = curDateStr;
        let createdBy = '0.0.0.1';
        let lastButOne = curDateStr;
        const lastSavedT = curDateStr;
        const lastModifiedBy = 'hzeditor'; 
        const lastPrintedT = curDateStr;
        const docSavedInfo = documentCore.getDocSavedInfo();
        if (docSavedInfo) {
            lastButOne = docSavedInfo.lastSavedT ? docSavedInfo.lastSavedT : lastButOne;
            createdBy = docSavedInfo.createdBy ? docSavedInfo.createdBy : createdBy;
            createdTime = docSavedInfo.createdTime ? docSavedInfo.createdTime : createdTime;
        }

        const logs = IFRAME_MANAGER.getCatchMeasureLog();
        if (logs?.length) {
            properties.set('catchMeasureLog', JSON.stringify(logs));
        }
        this.settings.setSaveHistory(lastModifiedBy,
                                     lastSavedT,
                                     lastPrintedT,
                                     createdBy,
                                     createdTime,
                                     lastButOne);
        const properties2 = documentCore.getAllFileProperties();
        if (properties2 && properties2.size) {
            for ( const [name, value] of properties2) {
                properties.set(name, value);
            }
        }

        if (properties && properties.size > 0) {
            this.settings.setProperties(properties);
        }
    }

    public addSectionProperties(sectionPropertiesOptions?: SectionPropertiesOptions): File {
        this.document.addSectionProperties(sectionPropertiesOptions);
        return this;
    }

    public setHtml(html: string): File {
        this.html = html;
        return this;
    }

    public setSourceBind(sourceBind: string): File {
        this.sourceBind = sourceBind;
        return this;
    }

    public setNISTableJson(NISTableJson: string): File {
        this.nisTableJson = NISTableJson;
        return this;
    }

    private addHeaderToDocument(header: Header): void {
        this.headers.push(header);
        // this.contentTypes.addHeader(this.headers.length);
    }

    private addFooterToDocument(footer: Footer): void {
        // only 1 footer
        this.footer = footer;
        // this.contentTypes.addFooter(this.footers.length);
    }
}
