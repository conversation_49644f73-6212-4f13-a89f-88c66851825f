import * as React from 'react';
import {
    GlobalEvent as gEvent,
    GlobalEventName as gEventName,
} from '../../../../common/GlobalEvent';
import { DocumentCore } from '../../../../model/DocumentCore';
import {
    INISProperty,
    NISTableCellType,
    ViewModeType,
} from '../../../../common/commonDefines';
import '../../style/newBoxList.less';
import { SDKcontainer } from '../SDKcontainer';
import { NISTableCellDateBox } from '../modals/NISTableCellDateBox';
import { NISTableCellListBox } from '../modals/NISTableCellListBox';

interface IDocumentProps {
    host: any;
    pageIndex: number;
}

interface IState {
    bRefresh: boolean;
}

export class NISTableLayer extends React.Component<IDocumentProps, IState> {
    private host: any;
    private documentCore: DocumentCore;
    private isShowComboxList: boolean;
    private isShowDateList: boolean;
    private isShowTime: boolean;
    private isShowComboxDropButton: boolean;
    private isShowDateDropButton: boolean;
    private pageIndex: number;
    private bVisible: boolean;
    private id: number;
    private myRef: any;
    private scale: number;
    private boxInfo: any;
    private spanRef: any;
    private bFromEnter: boolean;
    private nisProperty: INISProperty;

    constructor(props: IDocumentProps) {
        super(props);
        this.host = props.host;
        this.documentCore = props.host.documentCore;
        this.myRef = React.createRef();
        this.spanRef = React.createRef();
        this.state = {
            bRefresh: false,
        };
    }

    public render(): any {
        this.scale = this.props.host.getScale();
        return (
            <div ref={this.myRef}>
                {this.renderComboxList()}
                {this.renderDateDropButton()}
                {this.renderDateList()}
                {/* {this.readerTips()} */}
            </div>
        );
    }

    public componentDidMount(): void {
        gEvent.addEvent(this.host.docId, gEventName.NewNISCellToShow, this.showTableCellLayer);
        gEvent.addEvent(this.host.docId, gEventName.Selection, this.showTableCellLayer);
        // gEvent.addEvent(this.host.docId, gEventName.NewControlTips, this.showTipsLayer);
        const dom = this.myRef.current as HTMLElement;
        dom.addEventListener('mousedown', this.preventDefault);
        // dom.addEventListener('mousemove', this.preventDefault);
        dom.addEventListener('mouseup', this.mouseUp);
        document.addEventListener('click', this.docClick);
        // if (iframe) {
        //   iframe.contentDocument.addEventListener('wheel', this.docScroll, false);
        // }
    }

    public componentWillUnmount = (): void => {
        gEvent.deleteEvent(this.host.docId, gEventName.NewNISCellToShow, this.showTableCellLayer);
        gEvent.deleteEvent(this.host.docId, gEventName.Selection, this.showTableCellLayer);
        // gEvent.deleteEvent(this.host.docId, gEventName.NewControlTips, this.showTipsLayer);
        const dom = this.myRef.current as HTMLElement;
        dom.removeEventListener('mousedown', this.preventDefault);
        // dom.removeEventListener('mousemove', this.preventDefault);
        dom.removeEventListener('mouseup', this.mouseUp);
        document.removeEventListener('click', this.docClick);
        // if (iframe) {
        //   iframe.contentDocument.removeEventListener('wheel', this.docScroll, false);
        // }
    }

    private docClick = () => {
        if (this.bVisible !== true) {
            return;
        }
        this.close();
    }

    private docScroll = () => {
        if (this.bVisible !== true) {
            return;
        }
        this.close();
    }

    private mouseUp = (e: any): void => {
        const target = e.target;
        const className = target.className || '';
        if (className.search(/drop-button(-triangle)?/) > -1) {
            this.handleDropButton(parseInt(target.getAttribute('data-value'), 10));
        }
        e.stopPropagation();
    }

    private renderPlaceholderDom(): any {
        this.boxInfo = undefined;
        const documentCore = this.documentCore;
        const newControlPropety = documentCore.getCursorInNewControlProperty();
        if (!newControlPropety) {
            return null;
        }

        const bounds = documentCore.getNewControlsFocusBounds();
        const lastLine = bounds.bounds[bounds.bounds.length - 1];
        if (lastLine.pageIndex !== this.props.pageIndex) {
            return null;
        }
        const viewMode = documentCore.getViewMode();
        let subHeight = 0;
        if (viewMode === ViewModeType.WebView) {
            const page = documentCore.getPagePositionInfo(0);
            subHeight -= page.y;
        }
        const scale = this.scale;
        let width = lastLine.width;
        const left: number = lastLine.x * scale;
        const height = lastLine.height;
        const top: number = (lastLine.y + height + subHeight) * scale;
        width = width < 150 ? 150 : width;
        this.boxInfo = {
            property: newControlPropety,
            left,
            width,
            lineHeight: height * scale,
        };

        return (<span className='newcontrol-box-placeholder' ref={this.spanRef} style={{left, top}}/>);
    }

    private preventDefault = (e: any): void => {
        e.stopPropagation();
    }

    private showTableCellLayer = (type: NISTableCellType, pageIndex: number,
                                  id: number, bFromEnter?: boolean, nisProperty?: INISProperty): void => {
        this.bFromEnter = bFromEnter;
        this.nisProperty = nisProperty;
        if (type === undefined) {
            if (this.bVisible === true) {
                this.close(true);
            }
            this.id = undefined;
            return;
        }

        switch (type) {
            case NISTableCellType.List:
            case NISTableCellType.Quick: {
                if (id === this.id && this.bVisible === true) {
                    this.close();
                    return;
                } else if (this.bVisible === true) {
                    this.close(false);
                }

                const bProtected = this.isNISCellProtected();
                if (bProtected === false) {
                    this.isShowComboxList = true;
                    this.isShowComboxDropButton = true;
                }

                break;
            }
            case NISTableCellType.Date: {
                if (this.id === id) {
                    const prevShowDateList = this.isShowDateList;
                    if (this.bVisible === true) {
                        this.close(false);
                    }
                    this.showBtn(type);
                    // if prev state is closed, show up again after mousedown
                    if (prevShowDateList === true) {
                        return;
                    }
                }
                this.close(false);

                const bProtected = this.isNISCellProtected();
                if (bProtected === false) {
                    this.isShowDateList = true;
                    this.isShowDateDropButton = true;
                }

                break;
            }
            case NISTableCellType.Time: {
                if (this.id === id) {
                    const prevShowDateList = this.isShowDateList;
                    if (this.bVisible === true) {
                        this.close(false);
                    }
                    this.showBtn(type);
                    // if prev state is closed, show up again after mousedown
                    if (prevShowDateList === true) {
                        return;
                    }
                }
                this.close(false);
                this.isShowTime = true;
                this.isShowDateDropButton = true;
                break;
            }
            default: {
                if (this.bVisible === true) {
                    this.close();
                }
                this.id = id;
                return;
            }
        }
        this.id = id;
        this.bVisible = true;
        this.pageIndex = pageIndex;
        setTimeout(() => {
            this.setState({bRefresh: !this.state.bRefresh}, () => {
                this.setSDKInfo(type);
            });
        }, 5);
    }

    private isNISCellProtected(): boolean {
        const curCell = this.documentCore.getCurNISCell();
        let bProtected = false;
        if (curCell != null && curCell.property != null) {
            bProtected = curCell.property.getCellProtected();
        }

        return bProtected;
    }

    private setSDKInfo(type: NISTableCellType): any {
        const boxInfo = this.boxInfo;
        if (!boxInfo) {
            return;
        }
        const span = this.spanRef.current;
        if (!span) {
            return;
        }
        const sdk = SDKcontainer.getSDK();
        const position = span.getBoundingClientRect();
        boxInfo.left = position.left;
        boxInfo.top = position.top;
        boxInfo.type = type;
        boxInfo.bFromEnter = this.bFromEnter;
        sdk.showNewControlBox(this.host.docId, boxInfo, this.close);
    }

    private showBtn(type: NISTableCellType): void {
        switch (type) {
            case NISTableCellType.List:
            case NISTableCellType.Quick: {
                if (this.isShowComboxList === false && this.isShowComboxDropButton === true) {
                    return;
                }
                this.isShowComboxList = false;
                this.isShowComboxDropButton = true;
                break;
            }
            case NISTableCellType.Date: {
                if (this.isShowDateList === false && this.isShowDateDropButton === true) {
                    return;
                }
                this.isShowDateList = false;
                this.isShowDateDropButton = true;
                break;
            }
            case NISTableCellType.Time:  {
                if (this.isShowTime === false) {
                    return;
                }
                this.isShowDateList = false;
                this.isShowDateDropButton = false;
                break;
            }
            default:
                if (this.bVisible === true) {
                    this.close();
                }
                return;
        }

        this.bVisible = true;
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private renderComboxList = () => {
        if (true !== this.isShowComboxList) {
            return null;
        }
        if (SDKcontainer.isActiveSDK()) {
            return this.renderPlaceholderDom();
        }

        const documentCore = this.documentCore;
        const bounds = documentCore.getNISCellBounds();
        const pageIndex: number = this.props.pageIndex;
        // const line = bounds[bounds.length - 1];
        // if (line.pageIndex !== pageIndex) {
        //     return null;
        // }
        // let a = false;
        // console.log(pageIndex)
        let bound;
        if (bounds) {
            bounds.forEach((item) => {
                if (item && this.pageIndex === item.pageIndex
                    && this.pageIndex === pageIndex) {
                    // a = true;
                    bound = item;
                    return;
                }
            });
        }
        if (null == bound) {
            return null;
        }
        // console.log(this.isShowDateList)
        // console.log(this.nisProperty)
        // console.log(bounds)
        // const lineHeight = bounds[0] != null ? bounds[0].height : 0;
        if ( true === documentCore.isInTableCell() ) {
            return (
                <NISTableCellListBox
                    // lineHeight={lineHeight * this.scale}
                    documentCore={documentCore}
                    scale={this.scale}
                    key={this.id}
                    bFromEnter={this.bFromEnter}
                    nisProperty={this.nisProperty}
                    closeNewComboxList={this.close}
                    refresh={this.refresh}
                    bound={bound}
                />
            );
        }

        return null;
    }

    private refresh = (bClose: boolean = true) => {
        bClose && this.close(false);
        this.host.setCursorVisible(true);
        this.host.refresh();
    }

    private close = (bRefresh: boolean = true, bFrame?: boolean): void => {
        let bChaged: boolean = false;
        if (this.isShowDateList === true) {
            this.isShowDateList = false;
            bChaged = true;
            if (bFrame !== true) {
                const sdk = SDKcontainer.getSDK();
                if (sdk) {
                    sdk.closeBox();
                }
            }
        }
        if (this.isShowComboxList === true) {
            this.isShowComboxList = false;
            bChaged = true;
            if (bFrame !== true) {
                const sdk = SDKcontainer.getSDK();
                if (sdk) {
                    sdk.closeBox();
                }
            }
        }
        if (this.isShowDateDropButton === true) {
            this.isShowDateDropButton = false;
            bChaged = true;
        }
        if (this.isShowComboxDropButton === true) {
            this.isShowComboxDropButton = false;
            bChaged = true;
        }
        this.bVisible = false;
        if (bChaged === true && bRefresh === true) {
            this.setState({bRefresh: !this.state.bRefresh});
        }
    }

    private renderDateList = () => {
        if (true !== this.isShowDateList) {
            return null;
        }
        if (SDKcontainer.isActiveSDK()) {
            return this.renderPlaceholderDom();
        }
        const documentCore = this.documentCore;
        const bounds = documentCore.getNISCellBounds();
        const pageIndex: number = this.props.pageIndex;
        // const line = bounds[bounds.length - 1];
        // if (line.pageIndex !== pageIndex) {
        //     return null;
        // }
        // console.log(this.isShowDateList)
        // console.log(this.nisProperty)
        // console.log(bounds)

        let bound;
        if (bounds) {
            bounds.forEach((item) => {
                if (item && this.pageIndex === item.pageIndex
                    && this.pageIndex === pageIndex) {
                    bound = item;
                    return;
                }
            });
        }
        if (null == bound) {
            return null;
        }
        // const lineHeight = bounds[0] != null ? bounds[0].height : 0;
        if ( true === documentCore.isInTableCell() ) {
            return (
                <NISTableCellDateBox
                    // lineHeight={lineHeight * this.scale}
                    documentCore={documentCore}
                    nisProperty={this.nisProperty}
                    scale={this.scale}
                    closeNewBoxList={this.close}
                    refresh={this.refresh}
                    host={this.host}
                    bFromEnter={this.bFromEnter}
                    bound={bound}
                />
            );
            // return (
            //     <NewDateBox
            //         // lineHeight={line.height * this.scale}
            //         documentCore={documentCore}
            //         // newControlPropety={documentCore.getCursorInNewControlProperty()}
            //         scale={this.scale}
            //         closeNewBoxList={this.close}
            //         refresh={this.refresh}
            //         host={this.host}
            //         bFromEnter={this.bFromEnter}
            //     />
            // );
        }

        return null;
    }

    private handleDropButton(type: NISTableCellType): void {
        let bShow: boolean;
        switch (type) {
            case NISTableCellType.Date: {
                bShow = this.isShowDateList = !this.isShowDateList;
                break;
            }
            default:
                return;
        }
        this.setState({bRefresh: !this.state.bRefresh}, () => {
            if (bShow === false) {
                const sdk = SDKcontainer.getSDK();
                if (sdk) {
                    sdk.closeBox();
                }
            } else {
                this.setSDKInfo(type);
            }
        });
    }

    private renderDateDropButton = () => {
        const documentCore = this.documentCore;
        if (
            true === this.isShowDateDropButton &&
            true === documentCore.isCursorInNewControl()
        ) {
            const scale = this.scale;
            const bounds = documentCore.getNewControlsFocusBounds();
            const pageIndex: number = this.props.pageIndex;
            if (
                bounds &&
                bounds.bounds &&
                pageIndex === bounds.bounds[bounds.bounds.length - 1].pageIndex
            ) {
                let subHeight = 0;
                const viewMode = documentCore.getViewMode();
                if (viewMode === ViewModeType.WebView) {
                    const page = documentCore.getPagePositionInfo(0);
                    subHeight -= page.y;
                }
                const lastLine = bounds.bounds[bounds.bounds.length - 1];
                const left = (lastLine.x + lastLine.width) * scale;
                const top = (lastLine.y + 3 + subHeight) * scale;
                const width = 10;

                return (
                    <div
                        className='new-control-drop-button'
                        data-value={NISTableCellType.Date}
                        style={{ top, left, width, height: lastLine.height * scale }}
                    >
                        <div className='drop-button-triangle' data-value={NISTableCellType.Date} />
                    </div>
                );
            }
        }

        return null;
    }
}
