import { CurPos } from './Document';
import Selection, { ParagraphSelection, TableSelection, ITableCellPos } from './Selection';
import { ParaCurPos } from './Paragraph';
import { ParagraphContentPos } from './Paragraph/ParagraphContent';

/**
 * 基类：history记录文档当前光标的各种状态，用于undo/redo
 */
export class DocumentElementState {
    constructor( curPos: any, selection: any ) {
        this.setValue(curPos, selection);
    }

    public setValue(curPos: any, selection: any): void {
        //
    }
}

/**
 * history记录文档当前光标的各种状态，用于undo/redo
 */
export class DocumentState extends DocumentElementState {
    public curPos: CurPos;  // 光标位置
    public selection: Selection;  // 选择位置
    public curPage: number;  // 页面位置

    constructor( curPos: CurPos, selection: Selection ) {
        super(curPos, selection);
    }

    public setValue( curPos: CurPos, selection: Selection ): void {
        this.curPos = new CurPos();
        this.selection = new Selection();

        this.curPos.x = curPos.x;
        this.curPos.y = curPos.y;
        this.curPos.realX = curPos.realX;
        this.curPos.realY = curPos.realY;
        this.curPos.type = curPos.type;
        this.curPos.contentPos = curPos.contentPos;

        this.selection.bStart = selection.bStart;
        this.selection.bUse = selection.bUse;
        this.selection.startPos = selection.startPos;
        this.selection.endPos = selection.endPos;
        this.selection.data = selection.data;
        this.selection.bWordSelected = selection.bWordSelected;
    }
}

/**
 * history记录文档当前光标的各种状态，用于undo/redo
 */
// tslint:disable-next-line: max-classes-per-file
export class ParagraphState extends DocumentElementState {
    public curPos: ParaCurPos;  // 光标位置
    public contentPos: ParagraphContentPos;  // 光标位置: portion + portionContentPos
    public selection: ParagraphSelection;  // 选择位置
    public startPos: ParagraphContentPos;  // 光标选择位置: portion + portionContentPos
    public endPos: ParagraphContentPos;  // 光标选择位置: portion + portionContentPos

    constructor( curPos: ParaCurPos, selection: ParagraphSelection ) {
        super(curPos, selection);
    }

    public setValue( curPos: ParaCurPos, selection: ParagraphSelection ): void {
        this.curPos = new ParaCurPos();
        this.selection = new Selection();

        this.curPos.x = curPos.x;
        this.curPos.y = curPos.y;
        this.curPos.realX = curPos.realX;
        this.curPos.realY = curPos.realY;
        this.curPos.line = curPos.line;
        this.curPos.contentPos = curPos.contentPos;
        this.curPos.pagePos = curPos.pagePos;

        this.selection.bStart = selection.bStart;
        this.selection.bUse = selection.bUse;
        this.selection.startPos = selection.startPos;
        this.selection.endPos = selection.endPos;
    }
}

// tslint:disable-next-line: max-classes-per-file
export class TableState extends DocumentElementState {
    public curCell: ITableCellPos;
    public selection: TableSelection;  // 选择位置

    constructor(curCell: ITableCellPos, selection: TableSelection) {
        super(curCell, selection);
        this.curCell = curCell;
        this.selection = selection;
    }

    public setValue( curCell: ITableCellPos, selection: TableSelection ): void {
        this.curCell = {
            cellIndex: curCell.cellIndex,
            rowIndex: curCell.rowIndex,
        };

        this.selection = new TableSelection();

        // this.curPos.x = curPos.x;
        // this.curPos.y = curPos.y;
        // this.curPos.realX = curPos.realX;
        // this.curPos.realY = curPos.realY;
        // this.curPos.line = curPos.line;
        // this.curPos.contentPos = curPos.contentPos;
        // this.curPos.pagePos = curPos.pagePos;

        this.selection.bStart = selection.bStart;
        this.selection.bUse = selection.bUse;
        this.selection.startPos = selection.startPos;
        this.selection.endPos = selection.endPos;
    }
}
