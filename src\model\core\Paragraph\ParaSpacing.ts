import { LineSpacingType, LineSpacingRatio } from '../../../common/commonDefines';

/**
 * 段落间距
 */
export default class ParaSpacing {
    public lineSpacing: number;  // 行间距: 默认为单倍行距，即1.3
    public paraSpacingAfter: number;  // 段后距
    public paraSpacingBefore: number;  // 段前距
    public lineSpacingType: LineSpacingType; // 行间距类型

    constructor(lineSpacing: number = LineSpacingRatio.Single) {
        this.lineSpacing = lineSpacing;
        this.paraSpacingAfter = 0;
        this.paraSpacingBefore = 0;
        this.lineSpacingType = LineSpacingType.Single;
    }

    public copy(): ParaSpacing {
        const spacing = new ParaSpacing();
        spacing.lineSpacing = this.lineSpacing;
        spacing.paraSpacingBefore = this.paraSpacingBefore;
        spacing.paraSpacingAfter = this.paraSpacingAfter;
        spacing.lineSpacingType = this.lineSpacingType;

        return spacing;
    }
}
