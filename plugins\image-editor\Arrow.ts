import { fabric } from 'fabric';

export interface IArrow {
  line: fabric.Line;
  triangle: fabric.Triangle;
}

interface IProps {
  color: string;
  size: number;
}

export function createArrow(x: number, y: number, props: IProps): IArrow {

  const { color, size } = props;
  const line = new fabric.Line([x, y, x, y], {
    strokeWidth: size,
    fill: '',
    stroke: color,
    originX: 'center',
    originY: 'center',
  });

  const centerX = (line.x1 + line.x2) / 2;
  const centerY = (line.y1 + line.y2) / 2;
  const deltaX = line.left - centerX;
  const deltaY = line.top - centerY;
  
  const triangle = new fabric.Triangle({
    left: x + deltaX,
    top: y + deltaY,
    originX: 'center',
    originY: 'center',
    selectable: false,
    angle: -45,
    width: 6 + size*1.1,
    height: 8 + size*1.1,
    fill: color,
  });

  return {line, triangle};
}

export function buildingArrow(arrow: IArrow, x: number, y: number): void {
  let {line, triangle} = arrow;

  line.set({x2: x, y2: y});

  const centerX = (line.x1 + line.x2) / 2;
  const centerY = (line.y1 + line.y2) / 2;
  const deltaX = line.left - centerX;
  const deltaY = line.top - centerY;
  triangle.set({
    'left': x + deltaX,
    'top': y + deltaY,
    'angle': _FabricCalcArrowAngle(line.x1, line.y1, line.x2, line.y2)
  });
}

export function updateArrowProps(arrow: IArrow, props: IProps): void {
  const { line, triangle } = arrow;
  const { color, size } = props;
  line.set({
    strokeWidth: size,
    stroke: color,
  });
  triangle.set({
    width: 6 + size*1.1,
    height: 8 + size*1.1,
    fill: color,
  });
}



function _FabricCalcArrowAngle(x1, y1, x2, y2) {
  var angle = 0, x, y;
  x = (x2 - x1);
  y = (y2 - y1);
  if (x === 0) {
    angle = (y === 0) ? 0 : (y > 0) ? Math.PI / 2 : Math.PI * 3 / 2;
  } else if (y === 0) {
    angle = (x > 0) ? 0 : Math.PI;
  } else {
    angle = (x < 0) ? Math.atan(y / x) + Math.PI :
      (y < 0) ? Math.atan(y / x) + (2 * Math.PI) : Math.atan(y / x);
  }
  return (angle * 180 / Math.PI + 90);
};
