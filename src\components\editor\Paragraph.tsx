import * as React from 'react';
import { IDocumentParagraph } from '../../model/ParaProperty';
import ParaLine from './ParaLine';

export default class Paragraph extends React.Component<IDocumentParagraph, {}> {
  constructor(props) {
    super(props);
    // console.log('Paragraph---------------constructor----------------')
  }

  renderLines() {
    const { content, lines, startLine, endLine } = this.props;

    return lines.map((item, index) => {
      if ( startLine <= index && index <= endLine )
        return <ParaLine key={item.id} index={item.id} id={index} content={content} startPos={item.getStartPos()} endPos={item.getEndPos()}/>;
      index++;
    });

    // return content.map((item)=>{
    //   return <ParaPortion key={item.id} id={item.id} content={item.content} type={item.type} textProperty={item.textProperty} 
    //           positionX={item.positionX} positionY={item.positionY} startPos={0} endPos={item.content.length}/>;
    // })
  }

  render() {
    const { index } = this.props;
    return (
      // <tspan para-id={index}>
      //   {this.renderLines()}
      // </tspan>
      <g>
        {this.renderLines()}
      </g>
    );
  }

  // shouldComponentUpdate(nextProps, nextState) {
  //   // console.log(this.props.bounds.top, nextProps.bounds.top)
  //   // todo: 光标所在段落
  //   if ( this.props.content.length === nextProps.content.length && true === this.props.bounds.compare(nextProps.bounds) ) {
  //       for (let index = 0, count = this.props.content.length; index < count; index++) {
  //           const element = this.props.content[index];
  //           if ( element.content.length !== nextProps.content[index].content.length ) {
  //               return true;
  //           }
  //       }

  //       return false;
  //   }
  //   return true;
  // }

  // componentWillUnmount() {
  //   console.log('Paragraph--------componentWillUnmount---------------')
  // }
}
