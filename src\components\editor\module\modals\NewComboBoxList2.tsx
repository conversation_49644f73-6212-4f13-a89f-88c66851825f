import * as React from 'react';
import '../../style/customProp.less';
import {message} from '../../../../common/Message';
import Input from '../../ui/Input';
import { CodeValueItem } from '../../../../common/commonDefines';
import { IFRAME_MANAGER } from '../../../../common/IframeManager';
import Checkbox from '../../ui/CheckboxItem';

interface IProps {
    name: string;
    value: any[];
    docId: number;
    codeLabel: string;
    valueLabel: string;
    onChange: (value: any, name: string, options?: any) => void;
    confirm?: (value: any, name: string) => void;
}

interface IState {
    bRefresh: boolean;
}

export default class NewComboBoxList extends React.Component<IProps, IState> {
    private custompProps: CodeValueItem[];
    private _name: string;
    private _names: object;
    private _activeName: string;
    private activeIndex: number;
    private curInputIndex: number;
    private currentProp: CodeValueItem;
    private newControl: {
        valueLabel?: string,
        codeLabel?: string,
        bSetTitle?: boolean,
    };

    constructor(props: IProps) {
        super(props);
        this._name = '标题';
        this.state = {
            bRefresh: false,
        };
        this._names = {};
        this.newControl = {};
        this.init(props.value, props);
    }

    public render(): any {
        if (!this.custompProps || this.custompProps.length === 0) {
            return (<div className='custom-no-data custom-prop-add' onClick={this.rowClick.bind(this, 0)}>点击添加</div>);
        }
        const newControl = this.newControl;
        const bSetTitle = newControl.bSetTitle;
        let label: string = newControl.codeLabel;
        let value: string = newControl.valueLabel;
        if (label == null) {
            label = '名称';
        }
        if (value == null) {
            value = '值';
        }

        // let className = 'newcombox';
        // if (!bSetTitle) {
        //     className += ' contentEditable';
        // }

        return (
            <div className='newcontrol-custom'>
                <div className='editor-line'>
                    <Checkbox value={this.newControl.bSetTitle} name='bSetTitle' onChange={this.onCheckboxChange}>
                        自定义下拉标题项
                    </Checkbox>
                </div>
                <div className='newcontrol-custom-edit'>
                    <div className='w-050'>
                        <Input
                            disabled={!bSetTitle}
                            value={label}
                            name='codeLabel'
                            onChange={this.onTextChange}
                            placeholder=''
                        />
                    </div>
                    <div className='w-050'>
                        <Input
                            disabled={!bSetTitle}
                            value={value}
                            name='valueLabel'
                            onChange={this.onTextChange}
                            placeholder=''
                        />
                    </div>
                </div>

                <ul className='newcombox'>
                    <li>
                        <div>显示名</div>
                        <div>属性值</div>
                        <div>操作</div>
                    </li>
                    {this.renderList()}
                </ul>
            </div>
        );
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        this.init(nextProps.value, nextProps);
    }

    private onTextChange = (value: string, name: string): void => {
        this.newControl[name] = value;
        this.props.onChange(value, name);
        this.setState({});
    }

    private onCheckboxChange = (value: string, name: string): void => {
        this.newControl[name] = value;
        this.setState({});
    }

    private init(value: any[], props: IProps): void {
        const newControl = this.newControl;
        newControl.bSetTitle = false;
        if (value) {
            newControl.codeLabel = props.codeLabel;
            newControl.valueLabel = props.valueLabel;
            this.custompProps = value.slice();
        } else {
            this.custompProps = [];
            newControl.codeLabel = undefined;
            newControl.valueLabel = undefined;
            this.addData();
        }
    }

    private renderList(): any {
        return this.custompProps.map((prop, index) => {
            return (
                <li
                    key={index}
                    onClick={this.rowClick.bind(this, index)}
                    onMouseEnter={this.mouseEnter.bind(this, index)}
                >
                    <div className='custom-prop-name'>
                        <Input
                            value={prop.code}
                            name='code'
                            onChange={this.onChange.bind(this, index)}
                            onBlur={this.onBlur.bind(this, index)}
                            focus={this.onFocus}
                        />
                    </div>
                    <div>
                        <Input
                            value={prop.value}
                            name='value'
                            onBlur={this.confirm}
                            onChange={this.onChange.bind(this, index)}
                        />
                    </div>
                    <div>
                        <i className='prev'>↑</i>
                        <i className='next'>↓</i>
                        <span className='custom-prop-add'>+</span>
                        <label className='custom-prop-delete'>-</label>
                    </div>
                </li>
            );
        });
    }

    private mouseEnter = (index: number, e: any): void => {
        this.currentProp = this.custompProps[index];
    }

    private rowClick(index: number, e: any): void {
        this.activeIndex = index;
        const target = e.target as HTMLDivElement;
        const className = target.className;
        if (this.curInputIndex !== index && className === 'custom-prop-name') {
            this.curInputIndex = index;
        } else if (className.indexOf('custom-prop-add') > -1) {
            this.addData(index);
            this.setState({bRefresh: !this.state.bRefresh});
        } else if (className === 'custom-prop-delete') {
            this.deleteData(index);
            this.setState({bRefresh: !this.state.bRefresh});
        } else if (className === 'prev') {
            if (index === 0) {
                return;
            }
            const datas = this.custompProps;
            const temp = datas[index - 1];
            datas[index - 1] = datas[index];
            datas[index] = temp;
            this.setState({bRefresh: !this.state.bRefresh});
            this.props.onChange(this.custompProps.slice(), this.props.name);
        } else if (className === 'next') {
            const datas = this.custompProps;
            if (index === datas.length - 1) {
                return;
            }
            const temp = datas[index];
            datas[index] = datas[index + 1];
            datas[index + 1] = temp;
            this.setState({bRefresh: !this.state.bRefresh});
            this.props.onChange(this.custompProps.slice(), this.props.name);
        }
        // this.currentProp = this.custompProps[index];
    }

    private onChange = (index: number, value: any, name: string): void => {
        this.currentProp = this.custompProps[index];
        this.currentProp[name] = value;
    }

    private addData = (index?: number): void => {
        const data: CodeValueItem = new CodeValueItem('', undefined, false);
        if (index === undefined) {
            this.custompProps.push(data);
        } else {
            this.custompProps.splice(index + 1, 0, data);
        }
        this.currentProp = data;
        this.props.onChange(this.custompProps.slice(), this.props.name);
    }

    private deleteData = (index: number): void => {
        const data = this.custompProps[index];
        delete this._names[data.code];
        this.custompProps.splice(index, 1);
        this.currentProp = undefined;
        this.props.onChange(this.custompProps.slice(), this.props.name);
        this.confirm();
    }

    // private getPropName(): string {
    //     const datas = this.custompProps;
    //     if (!datas || datas.length === 0) {
    //         const actName = this._name + 1;
    //         // this._name = actName;
    //         this._names[actName] = true;
    //         return actName;
    //     }

    //     const names = this._names;
    //     const name = this._name;
    //     let res: string;
    //     for (let index = 1; true; index++) {
    //         res = name + index;
    //         if (names[res] !== true) {
    //             names[res] = true;
    //             break;
    //         }
    //     }

    //     return res;
    // }

    private onBlur = (index: number, name: string, input: any): void => {
        const currentProp = this.custompProps[index];
        if (!currentProp) {
            return;
        }
        const preName = this._activeName;
        const currentName = currentProp.code;
        if (currentName) {
            const currentIndex = this.custompProps.findIndex((item, i) => item.code === currentName && i !== index);
            if (currentIndex > -1) {
                IFRAME_MANAGER.setDocId(this.props.docId);
                message.error('已存在相同的名称')
                .then(() => {
                    currentProp.code = '';
                    input.focus();
                    this.setState({bRefresh: !this.state.bRefresh});
                });
                return;
            }
            delete this._names[preName];
            this._names[currentName] = true;
        }
        this.confirm();
        // else if (!currentName) {
        //     message.error('显示名称不能为空')
        //     .then(() => {
        //         // input.focus();
        //         this.setState({bRefresh: !this.state.bRefresh});
        //     });
        // }
    }

    private onFocus = (name: string, e: any): void => {
        this._activeName = e.target.value;
    }

    private confirm = (): void => {
        if (typeof this.props.confirm !== 'function') {
            return;
        }

        this.props.confirm(this.custompProps, this.props.name);
    }
}
