import * as React from 'react';
import ParaDrawing from '../../model/core/Paragraph/ParaDrawing';
import { DocumentCore } from '../../model/DocumentCore';
import { DocCurPosType } from '../../model/core/Document';
import {GlobalEvent as gEvent, GlobalEventName as gEventName} from '../../common/GlobalEvent';
// import {parse as circularParse, stringify as circularStringify} from 'flatted/esm';

interface IImageProps {
  id: number;
  item: ParaDrawing;
  editorContainer?: any;
  documentCore?: DocumentCore;
  handleRefresh?: any;
  host?: any;
}

interface IImageState {
  imageFocused?: boolean;
  showContextMenu?: boolean;
  showBasicConfigModal?: boolean;
  showAdvancedConfigModal?: boolean;
  // preserveAspectRatio?: boolean;
}

export default class Image extends React.Component<IImageProps, IImageState> {
  private docId: number;
  private handlerNWRef;
  private handlerNERef;
  private handlerSERef;
  private handlerSWRef;
  private handlerERef;
  private handlerWRef;
  private handlerNRef;
  private handlerSRef;
  private imgContainerRef;
  private imageRef;
  private imageContextMenuRef;
  // private imageCloneRef;

  private tldot = null;
  private trdot = null;
  private brdot = null;
  private bldot = null;
  private mrdot = null;
  private mldot = null;
  private mtdot = null;
  private mbdot = null;
  private img = null;
  private imgContainer = null;
  private currentDotId = null;

  // calc
  private cursorInitials = {x: null, y: null};
  private brdotInitials = {x: 0, y: 0};
  private brdotCurrents = {x: 0, y: 0};
  private bldotInitials = {x: 0, y: 0};
  private bldotCurrents = {x: 0, y: 0};
  private trdotInitials = {x: 0, y: 0};
  private trdotCurrents = {x: 0, y: 0};
  private tldotInitials = {x: 0, y: 0};
  private tldotCurrents = {x: 0, y: 0};
  private mrdotInitials = {x: 0, y: 0};
  private mrdotCurrents = {x: 0, y: 0};
  private mldotInitials = {x: 0, y: 0};
  private mldotCurrents = {x: 0, y: 0};
  private mtdotInitials = {x: 0, y: 0};
  private mtdotCurrents = {x: 0, y: 0};
  private mbdotInitials = {x: 0, y: 0};
  private mbdotCurrents = {x: 0, y: 0};

  // image
  private imgInitials = {width: 0, height: 0}; // will be initialized once imageRef is created
  private imgCurrents = {width: 0, height: 0};
  private IMG_MIN = {width: 10, height: 10};
  private IMG_MAX = {width: 0, height: 0};
  private imgMinCounterpartFixedRatio = {width: this.IMG_MIN.width, height: this.IMG_MIN.height};
  private isImgMinCounterpartFixedRatioTriggered = false;
  private ratio = 1;
  private tempRatio = 0; // useful when not ratioperserved while moving image

  // starting coords should never be changed. only temp change when mousemove
  private imgStartingCoords = {x: 0, y: 0};
  private imgCurrentCoords = {x: 0, y: 0};

  private diff = 0;
  private polygonClicked = false;
  private imageJustResized = false; // preserve polygons existence after resizing

  // default to be ratio fixed. May change to state?
  // private isRatioPreserved = true;
  private bPrint = false;

  constructor(props) {
    super(props);

    // seems ref is the only way. Attr change shouldn't trigger rerender, thus state is not proper.
    // OR maybe modify it with shouldcomponentupdate()
    this.handlerNWRef = React.createRef();
    this.handlerNERef = React.createRef();
    this.handlerSERef = React.createRef();
    this.handlerSWRef = React.createRef();
    this.handlerERef = React.createRef();
    this.handlerWRef = React.createRef();
    this.handlerNRef = React.createRef();
    this.handlerSRef = React.createRef();
    this.imgContainerRef = React.createRef();
    this.imageRef = React.createRef();
    this.imageContextMenuRef = React.createRef();
    // this.imageCloneRef = React.createRef();

    // the binding is the same as (e) => {}. but only do bind(this) at constructor, never within element
    // https://reactjs.org/docs/handling-events.html
    // this.imageMousedown = this.imageMousedown.bind(this);
    // this.clickContainer = this.clickContainer.bind(this);

    this.state = {
      imageFocused: false,
      showContextMenu: false,
      showBasicConfigModal: false,
      showAdvancedConfigModal: false,
      // preserveAspectRatio: true,
    };

    if (this.props.host) {
      this.bPrint = this.props.host.isPrint;
    }
    this.docId = this.props.documentCore.getCurrentId();
  }

  renderImageWrapper() {
    const {id, item, editorContainer} = this.props;

    // this.img = this.imageRef.current;

    // document ref
    // if (editorContainer) {
    //   editorContainer.addEventListener('mouseup', this.handleMouseup); // look out for context
    //   editorContainer.addEventListener('mousemove', this.handleMousemove);
    //   // editorContainer.addEventListener('click', this.clickContainer);
    //   editorContainer.addEventListener('mousedown', this.mousedownContainer);
    //   // editorContainer.addEventListener('contextmenu', this.handleContextMenu);
    // }
    if (!gEvent.isExistEvent(this.docId, gEventName.Mouseup, this.handleMouseup)) {
      gEvent.addEvent(this.docId, gEventName.Mouseup, this.handleMouseup);
    }

    if (!gEvent.isExistEvent(this.docId, gEventName.Mousemove, this.handleMousemove)) {
      gEvent.addEvent(this.docId, gEventName.Mousemove, this.handleMousemove);
    }

    if (!gEvent.isExistEvent(this.docId, gEventName.Mousedown, this.mousedownContainer)) {
      gEvent.addEvent(this.docId, gEventName.Mousedown, this.mousedownContainer);
    }

    // document.addEventListener('mouseup', this.handleMouseup); // look out for context
    // document.addEventListener('mousemove', this.handleMousemove);
    // document.addEventListener('click', this.clickContainer);

    return (
      <React.Fragment>
        {this.renderImage(id, item)}
        {this.renderHandlers(id, item)}
      </React.Fragment>
    );
  }

  renderHandlers(id: number, item: ParaDrawing) {
    if (this.bPrint === true) {
      return '';
    }
    // pageX: 335,
    // pageY: 94,
    // console.log(item.positionX, item.positionY, item.width, item.height);
    // console.log(item.positionY + item.height)
    // predicament: if not recalculate(), {item.positionY + item.height} can never auto update correctly
    // console.log(item.width, item.height);
    // console.log(item.positionX, item.positionY)
    // console.log(this.isRatioPreserved)
    const documentCore = this.props.documentCore;
    let showPolygon = true;
    let showHorVerPolygon = true;
    if (documentCore.isProtectedMode()) {
      showPolygon = false;
      showHorVerPolygon = false;
    } else {
      showPolygon = this.state.imageFocused ? (item.sizeLocked ? false : true) : false;
      // const showHorVerPolygon = this.isRatioPreserved ? false : true;
      showHorVerPolygon = item.preserveAspectRatio ? false : true;
      // console.log(showHorVerPolygon)
    }

    return (
      <React.Fragment>
        <circle id={'svg-'+id+'-dot1'} className={showPolygon? 'image-processor image-processor-nw' : 'image-processor image-processor-nw hide'} ref={this.handlerNWRef} cx={item.positionX+2} cy={item.positionY+2} r='4' fill='white' stroke='blue' strokeWidth='1' onMouseDown={this.handleMousedownPolygon}/>
        <circle id={'svg-'+id+'-dot2'} className={showPolygon? 'image-processor image-processor-ne' : 'image-processor image-processor-ne hide'} ref={this.handlerNERef} cx={item.positionX + item.width-2} cy={item.positionY+2} r='4' fill='white' stroke='blue' strokeWidth='1' onMouseDown={this.handleMousedownPolygon}/>
        <circle id={'svg-'+id+'-dot3'} className={showPolygon? 'image-processor image-processor-se' : 'image-processor image-processor-se hide'} ref={this.handlerSERef} cx={item.positionX + item.width-2} cy={item.positionY + item.height-2} r='4' fill='white' stroke='blue' strokeWidth='1' onMouseDown={this.handleMousedownPolygon}/>
        <circle id={'svg-'+id+'-dot4'} className={showPolygon? 'image-processor image-processor-sw' : 'image-processor image-processor-sw hide'} ref={this.handlerSWRef} cx={item.positionX+2} cy={item.positionY + item.height-2} r='4' fill='white' stroke='blue' strokeWidth='1' onMouseDown={this.handleMousedownPolygon}/>
        <circle id={'svg-'+id+'-dotmr'} className={(showPolygon && showHorVerPolygon)? 'image-processor image-processor-e' : 'image-processor image-processor-e hide'} ref={this.handlerERef} cx={item.positionX + item.width-2} cy={item.positionY + item.height / 2} r='4' fill='white' stroke='blue' strokeWidth='1' onMouseDown={this.handleMousedownPolygon}/>
        <circle id={'svg-'+id+'-dotml'} className={(showPolygon && showHorVerPolygon)? 'image-processor image-processor-w' : 'image-processor image-processor-w hide'} ref={this.handlerWRef} cx={item.positionX+2} cy={item.positionY + item.height / 2} r='4' fill='white' stroke='blue' strokeWidth='1' onMouseDown={this.handleMousedownPolygon}/>
        <circle id={'svg-'+id+'-dotmt'} className={(showPolygon && showHorVerPolygon)? 'image-processor image-processor-n' : 'image-processor image-processor-n hide'} ref={this.handlerNRef} cx={item.positionX + item.width / 2} cy={item.positionY+2} r='4' fill='white' stroke='blue' strokeWidth='1' onMouseDown={this.handleMousedownPolygon}/>
        <circle id={'svg-'+id+'-dotmb'} className={(showPolygon && showHorVerPolygon)? 'image-processor image-processor-s' : 'image-processor image-processor-s hide'} ref={this.handlerSRef} cx={item.positionX + item.width / 2} cy={item.positionY + item.height-2} r='4' fill='white' stroke='blue' strokeWidth='1' onMouseDown={this.handleMousedownPolygon}/>
      </React.Fragment>
    );
  }

  renderImage(id, item) {
    // let logicDocument = this.props.documentCore.getDocument();
    // console.log(item.name)
    // console.log(this.img)
    // console.log(this.isRatioPreserved)
    if (this.img) {
      // fake preserveAspectRatio if already none
      // this.isRatioPreserved = this.img.getAttribute('preserveAspectRatio') === 'none' ? false : true;
      // console.log(this.isRatioPreserved)
    }

    // const isHeaderFooterOpaque = logicDocument.curPos.type === DocCurPosType.HdrFtr ? false : true;
    // const isImageOpaque = this.props.item.inHdrFtr ? (isHeaderFooterOpaque) : false;

    // return <image id={"image-"+id} name={item.name} ref={this.imageRef} href={item.src} width={item.width} height={item.height} preserveAspectRatio={this.state.preserveAspectRatio? "xMinYMin meet": "none"} x={item.positionX} y={item.positionY} onMouseDown={this.imageMousedown} />
    // return <image id={'svg-'+id} name={item.name} ref={this.imageRef} href={item.src} width={item.width} height={item.height} preserveAspectRatio={this.props.item.preserveAspectRatio? (this.isRatioPreserved ? 'xMinYMin meet': 'none'): 'none'} x={item.positionX} y={item.positionY} onMouseDown={this.imageMousedown} /> 
    return <image name={item.name} ref={this.imageRef} href={item.src} id={'svg-'+id}   x={item.positionX} y={item.positionY} width={item.width} height={item.height} preserveAspectRatio={this.props.item.preserveAspectRatio? 'xMinYMin meet': 'none'}  onMouseDown={this.imageMousedown} /> 
    // return <image id={"image-"+id} ref={this.imageRef} href={item.src} width={item.width} height={item.height} preserveAspectRatio="none" x={item.positionX} y={item.positionY} onClick={this.imageMousedown} />
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    // console.log("image UNSAFE_componentWillReceiveProps " + this.props.id + " page: " + this.props.item.pageIndex);
    // let document = this.props.documentCore.getDocument();
    // let selecetdImage = document.getDrawingObjects().getSelectedImage();
    // console.log(selecetdImage)
    // console.log(this.props.item.preserveAspectRatio, nextProps.item.preserveAspectRatio);
    if (this.props.item.preserveAspectRatio !== nextProps.item.preserveAspectRatio) {
      // For fun. Shouldn't trigger rerender
      // this.isRatioPreserved = nextProps.item.preserveAspectRatio;

      // this.setState({preserveAspectRatio: nextProps.item.preserveAspectRatio});
    }
    // if (item.src) {
    //   this.setState({preserveAspectRatio: item.preserveAspectRatio})
    // }

  }
//   public componentDidUpdate(prevProps: any): void {
//     if (this.props.item.preserveAspectRatio !== prevProps.item.preserveAspectRatio) {
//         // 处理 prop 变化，例如更新状态
//         // this.setState({ preserveAspectRatio: this.props.item.preserveAspectRatio });
//     }
// }

  componentDidMount() {

    const {item} = this.props;

    // if not preloaded, focus at first
    if (!item.preload) {
      this.imageMousedown();
    }
    // gEvent.addEvent('contextmenu', this.handleContextMenu);
  }

  render() {
    // console.log("image render")
    return (
      <g ref={this.imgContainerRef}>
        {this.renderImageWrapper()}
      </g>
    );
  }

  initializeRefProperties() {
    const {documentCore, item} = this.props;
    const img = this.imageRef.current;

    // initialize image-related properties
    if (img) {
      this.img = img;
      this.imgInitials = {width: img.getAttribute('width'), height: img.getAttribute('height')};
      this.imgCurrents = {width: img.getAttribute('width'), height: img.getAttribute('height')};
      this.imgStartingCoords = {x: img.getAttribute('x'), y: img.getAttribute('y')};
      this.imgCurrentCoords = {x: img.getAttribute('x'), y: img.getAttribute('y')};
      // this.ratio = this.imgInitials.width / this.imgInitials.height;
      this.ratio = item.imageRatio;
      // console.log(this.ratio)
      const maxWidth = documentCore.getMaxWidth(false);
      const maxHeight = documentCore.getMaxHeight(false);

      // console.log(maxWidth, maxHeight);
      this.IMG_MAX.width = maxWidth;
      this.IMG_MAX.height = maxHeight;

    } else {
      console.warn('imgRef is null')
    }

    if (this.imgContainerRef.current) {
      this.imgContainer = this.imgContainerRef.current;
    } else {
      console.warn('imgcontainerRef is null')
    }

    if (!this.handlerSERef.current) {
      return;
    }
    this.bldot = this.handlerSWRef.current;
    this.trdot = this.handlerNERef.current;
    this.tldot = this.handlerNWRef.current;
    this.brdot = this.handlerSERef.current;
    this.mrdot = this.handlerERef.current;
    this.mldot = this.handlerWRef.current;
    this.mtdot = this.handlerNRef.current;
    this.mbdot = this.handlerSRef.current;
    this.brdotInitials = {x: this.handlerSERef.current.getAttribute('cx'), y: this.handlerSERef.current.getAttribute('cy')};
    this.brdotCurrents = {x: this.handlerSERef.current.getAttribute('cx'), y: this.handlerSERef.current.getAttribute('cy')};
    this.bldotInitials = {x: this.handlerSWRef.current.getAttribute('cx'), y: this.handlerSWRef.current.getAttribute('cy')};
    this.bldotCurrents = {x: this.handlerSWRef.current.getAttribute('cx'), y: this.handlerSWRef.current.getAttribute('cy')};
    this.trdotInitials = {x: this.handlerNERef.current.getAttribute('cx'), y: this.handlerNERef.current.getAttribute('cy')};
    this.trdotCurrents = {x: this.handlerNERef.current.getAttribute('cx'), y: this.handlerNERef.current.getAttribute('cy')};
    this.tldotInitials = {x: this.handlerNWRef.current.getAttribute('cx'), y: this.handlerNWRef.current.getAttribute('cy')};
    this.tldotCurrents = {x: this.handlerNWRef.current.getAttribute('cx'), y: this.handlerNWRef.current.getAttribute('cy')};
    this.mrdotInitials = {x: this.handlerERef.current.getAttribute('cx'), y: this.handlerERef.current.getAttribute('cy')};
    this.mrdotCurrents = {x: this.handlerERef.current.getAttribute('cx'), y: this.handlerERef.current.getAttribute('cy')};
    this.mldotInitials = {x: this.handlerWRef.current.getAttribute('cx'), y: this.handlerWRef.current.getAttribute('cy')};
    this.mldotCurrents = {x: this.handlerWRef.current.getAttribute('cx'), y: this.handlerWRef.current.getAttribute('cy')};
    this.mtdotInitials = {x: this.handlerNRef.current.getAttribute('cx'), y: this.handlerNRef.current.getAttribute('cy')};
    this.mtdotCurrents = {x: this.handlerNRef.current.getAttribute('cx'), y: this.handlerNRef.current.getAttribute('cy')};
    this.mbdotInitials = {x: this.handlerSRef.current.getAttribute('cx'), y: this.handlerSRef.current.getAttribute('cy')};
    this.mbdotCurrents = {x: this.handlerSRef.current.getAttribute('cx'), y: this.handlerSRef.current.getAttribute('cy')};

    // console.log(this.mbdotCurrents)
  }

  imageMousedown = () => {
    console.log('image mousedown') 
    // console.log(e.pageX, e.pageY)
    //TODO: If click on image, looks like clickContainer always trigger first. But since bubbling, container should be
    // after image to trigger event. ?

    // This would trigger only for current image
    // this.imageJustResized = false;

    // e.stopPropagation();
    if (!this.state.imageFocused) {
      this.setState({imageFocused: true});
      // this.initializeRefProperties();
    }
    this.initializeRefProperties();

  }

  // clickContainer = (e) => {
  mousedownContainer = (e) => {
    if (this.bPrint === true) {
      return;
    }
    // console.log("left/right click container")
    // console.log("left/right mousedown container")
    // console.log(e.target.tagName)
    //TODO: If click on image, looks like clickContainer always trigger first. But since bubbling, container should be
    // after image to trigger event. ?

    // e.stopPropagation();

    // if ( (e.target.tagName === "DIV" && e.target.className.includes("contextMenu-item")) || (e.target.tagName === "BUTTON") ) {
    if (e.target.tagName == 'SELECT' || e.target.tagName == 'INPUT' || e.target.tagName == 'BUTTON' 
      || e.target.tagName === 'DIV' || e.target.tagName === 'SPAN' || e.target.tagName === 'LABEL') {
      return;
    }

    // if not focused on polygons or current image, remove image focus
    if ( !(this.isFocusedOnCurrentImage(e) || (e.target.tagName === 'circle' && e.target.className.baseVal.includes('image-processor')))  ) {
      if (this.state.imageFocused) {
        this.setState({imageFocused: false});
      }
    }

    // if (this.state.showContextMenu) {
    //   this.setState({showContextMenu: false});
    // }
  }

  handleContextMenu = (e) => {
    if (this.bPrint === true) {
      return;
    }
    console.log('image contextmenu')
    // console.log(e.target);
    // console.log(this.isFocusedOnCurrentImage(e))

    // if image is not focused but has focused state, blur it.
    if (!this.isFocusedOnCurrentImage(e)) {
      if (this.state.imageFocused && !this.imageJustResized) {
        this.setState({imageFocused: false});
      }
    }

  }

  handleButtonClick = (e) => {
    console.log(e.target.innerHTML)

    switch (e.target.innerHTML) {
      case '属性':
        this.setState({showBasicConfigModal: true});
        break;
      case '高级':
        this.setState({showAdvancedConfigModal: true});
        break;

      default:
        break;
    }
  }

  handleMousedownPolygon = (e) => {
    if (this.bPrint === true) {
      return;
    }
    const {id, editorContainer} = this.props;

    console.log('polygon mousedown')
    this.currentDotId = e.target.id;
    // console.log(this.currentDotId);
    // let img = this.imageRef.current;
    // let imgContainer = this.imgContainerRef.current;

    // initialize ref-related properties
    this.initializeRefProperties();

    this.polygonClicked = true;
    this.cursorInitials.x = e.pageX;
    this.cursorInitials.y = e.pageY;
    const imgClone = this.img.cloneNode(false);

    imgClone.setAttribute('id', 'imgClone-' + id);

    const clone = editorContainer.querySelector('#imgClone-' + id);
    // make sure only one clone is inserted
    if (!clone) {
      this.imgContainer.insertBefore(imgClone, this.img);
    }

    // set temp ratio if possible
    const curImage = this.props.item;
    if (!curImage.preserveAspectRatio) {
      this.tempRatio = curImage.width / curImage.height;
    } else {
      this.tempRatio = this.ratio;
    }

  }

  handleMouseup = (e) => {
    if (this.bPrint === true) {
      return;
    }
    // console.log("editorcontainer mouseup")

    // if (this.isFocusedOnCurrentImage(e)) {}

    this.imageJustResized = false;
    if (this.polygonClicked) { // was mousemoving/image resized
      // console.log("image mouseup this.clicked true")
      this.polygonClicked = false;
      this.imageJustResized = true;
      this.isImgMinCounterpartFixedRatioTriggered = false;
      this.updateDotInitialsAndCleanup();
    }

  }

  handleMousemove = (e) => {
    if (this.bPrint === true) {
      return;
    }
    // key: think about only one direction(shrink or enlarge); the other one can be inferred naturally
    if (this.polygonClicked) {
      // console.log("moving")

      // let img = this.imageRef.current;
      let dot = this.handlerSERef.current;
      const currentDotType = this.currentDotId.slice(this.currentDotId.lastIndexOf('-') + 1);
      // console.log(currentDotType);

      // improvised way. TODO: after perserveratio breaks, need to figure out a neat way to keep real ratio
      // this.isRatioPreserved = this.img.getAttribute('preserveAspectRatio') === 'none' ? false : true;
      let bRatioPreserved = this.props.item.preserveAspectRatio;
      switch (currentDotType) {
        case 'dot1':
          dot = this.handlerNWRef.current;
          if (bRatioPreserved) {
            this.img.setAttribute('preserveAspectRatio', 'xMaxYMax meet');
          }
          break;
        case 'dot2':
          dot = this.handlerNERef.current;
          if (bRatioPreserved) {
            this.img.setAttribute('preserveAspectRatio', 'xMinYMax meet');
          }
          break;
        case 'dot3':
          dot = this.handlerSERef.current;
          if (bRatioPreserved) {
            this.img.setAttribute('preserveAspectRatio', 'xMinYMin meet');
          }
          break;
        case 'dot4':
          dot = this.handlerSWRef.current;
          if (bRatioPreserved) {
            this.img.setAttribute('preserveAspectRatio', 'xMaxYMin meet');
          }
          break;
        case 'dotml':
          dot = this.handlerWRef.current;
          this.img.setAttribute('preserveAspectRatio', 'none');
          // if (this.isRatioPreserved) {
          //   this.props.documentCore.setAspectRatio(false);
          //   this.isRatioPreserved = false;
          // }
          break;

        case 'dotmt':
          dot = this.handlerNRef.current;
          this.img.setAttribute('preserveAspectRatio', 'none');
          // if (this.isRatioPreserved) {
          //   this.props.documentCore.setAspectRatio(false);
          //   this.isRatioPreserved = false;
          // }
          break;

        case 'dotmr':
          dot = this.handlerERef.current;
          this.img.setAttribute('preserveAspectRatio', 'none');
          // if (this.state.preserveAspectRatio) {
          // if (this.isRatioPreserved) {
          //   this.props.documentCore.setAspectRatio(false);
          //   this.isRatioPreserved = false;
          // }
            // this.setState({preserveAspectRatio: false});
          // }
          break;

        case 'dotmb':
          dot = this.handlerSRef.current;
          this.img.setAttribute('preserveAspectRatio', 'none');
          // if (this.isRatioPreserved) {
          //   this.props.documentCore.setAspectRatio(false);
          //   this.isRatioPreserved = false;
          // }
          break;

        default:
          break;
      }

      this.img.setAttribute('class', 'opacity');
      const diffX = this.cursorInitials.x - e.pageX;
      const diffY = this.cursorInitials.y - e.pageY;
      // console.log(diffX, diffY)
      let shift = 0;

      if (currentDotType === 'dot3' || currentDotType === 'dot1' || currentDotType === 'dot2' || currentDotType === 'dot4') {
        shift = Math.min(Math.abs(diffX), Math.abs(diffY));
      } else if (currentDotType === 'dotml') {
        // shift is useless for middle dots manipulation
        // shift = diffX;
      }

      if (currentDotType === 'dot3') { // when dimish, x-, y-
        this.diff = (Math.abs(diffX) === shift) ? diffX : diffY;
      } else if (currentDotType === 'dot4') { // when dimish, x+, y-
        this.diff = (Math.abs(diffX) === shift) ? -diffX : diffY;
      } else if (currentDotType === 'dot1') { // when dimish, x+, y+
        this.diff = (Math.abs(diffX) === shift) ? -diffX : -diffY;
      } else if (currentDotType === 'dot2') { // when dimish, x-, y+
        this.diff = (Math.abs(diffX) === shift) ? diffX : -diffY;
      } else if (currentDotType === 'dotml') { // x+
        this.diff = -diffX;
      } else if (currentDotType === 'dotmt') { // y+
        this.diff = -diffY;
      } else if (currentDotType === 'dotmr') { // x-
        this.diff = diffX;
      } else if (currentDotType === 'dotmb') { // y-
        this.diff = diffY;
      }

      if (bRatioPreserved) {
        // move faster when enlarge
        if (this.imgCurrents.width > this.imgInitials.width) {
          this.diff *= this.ratio;
        } else if (this.imgCurrents.width < this.imgInitials.width) {
          // this.diff /= ratio;
        }
      }

      // Observation: SVG has its calculated ratio. As long as "preserveAspectRatio" is not "none",
      // it would take effect.
      // If the change of height/width makes the "temp ratio" not the same as the "old ratio",
      // it would always GENERATE SMALLER IMAGE which follows the "old ratio".
      // It also means that once there is a change at height/width, after calculating with ratio,
      // if the calculated corresponding
      // width/height > current width/height, then the SVG image would not change.

      if (currentDotType === 'dot3' || currentDotType === 'dot1' || currentDotType === 'dot2' ||
      currentDotType === 'dot4') {
          this.imgCurrents.width = this.imgInitials.width - this.diff;
          this.imgCurrents.height = this.imgInitials.height - this.diff;
      } else if (currentDotType === 'dotml' || currentDotType === 'dotmr') {
        this.imgCurrents.width = this.imgInitials.width - this.diff;
      } else if (currentDotType === 'dotmt' || currentDotType === 'dotmb') {
        this.imgCurrents.height = this.imgInitials.height - this.diff;
      }

      // console.log(this.imgCurrents.height)
      // console.log(this.IMG_MAX.height);
      // console.log(this.imgCurrents.width);
      // console.log(this.IMG_MAX.width);

      // console.log(this.imgCurrents.width, this.IMG_MAX.width, this.imgCurrents.height, this.IMG_MAX.height)
      // negative handling. no need image reversing
      if (this.imgCurrents.width < this.IMG_MIN.width) {
        this.imgCurrents.width = this.IMG_MIN.width;
        this.diff = this.imgInitials.width - this.IMG_MIN.width;

        // the other dimension should no longer be smaller
        // if (imgCurrents.height <= IMG_MIN.height) imgCurrents.height = IMG_MIN.height;
        if (this.imgCurrents.width === this.IMG_MIN.width && !this.isImgMinCounterpartFixedRatioTriggered) {
          this.isImgMinCounterpartFixedRatioTriggered = true;
          this.imgMinCounterpartFixedRatio.height = this.imgCurrents.height;
        }

        this.imgCurrents.height = this.imgMinCounterpartFixedRatio.height;

      } else if (this.imgCurrents.height < this.IMG_MIN.height) {
        this.imgCurrents.height = this.IMG_MIN.height;
        this.diff = this.imgInitials.height - this.IMG_MIN.height;

        // if (imgCurrents.width <= IMG_MIN.width) imgCurrents.width = IMG_MIN.width;
        if (this.imgCurrents.height === this.IMG_MIN.height && !this.isImgMinCounterpartFixedRatioTriggered) {
          this.isImgMinCounterpartFixedRatioTriggered = true;
          // console.log("min");
          this.imgMinCounterpartFixedRatio.width = this.imgCurrents.width;
        }

        this.imgCurrents.width = this.imgMinCounterpartFixedRatio.width;

      } else if (this.imgCurrents.width > this.IMG_MAX.width) {
        this.imgCurrents.width = this.IMG_MAX.width;
        this.diff = this.IMG_MAX.width - this.imgInitials.width;

      } else if (this.imgCurrents.height > this.IMG_MAX.height) {
        this.imgCurrents.height = this.IMG_MAX.height;
        this.diff = this.IMG_MAX.height - this.imgInitials.height;
      }

      // image scaling
      if (currentDotType === 'dot3' || currentDotType === 'dot1' || currentDotType === 'dot2' || currentDotType === 'dot4') {
        this.img.setAttribute('width', this.imgCurrents.width);
        this.img.setAttribute('height', this.imgCurrents.height);
      } else if (currentDotType === 'dotml' || currentDotType === 'dotmr') {
        this.img.setAttribute('width', this.imgCurrents.width);
      } else if (currentDotType === 'dotmt' || currentDotType === 'dotmb') {
        this.img.setAttribute('height', this.imgCurrents.height);
      }

      // intend to make handlers accurate while moving, not within prd for now.
      if (currentDotType === 'dot3') {
        // current dot coord. not relative to svg
        // this.brdotCurrents.x = this.brdotInitials.x - this.diff;
        // this.brdotCurrents.y = this.brdotInitials.y - this.diff;
        // // console.log(this.brdotCurrents.x, this.brdotCurrents.y)

        // dot.setAttribute("cx", this.brdotCurrents.x);
        // dot.setAttribute("cy", this.brdotCurrents.y);

        // // when br : other dots update
        // this.bldotCurrents.y = this.bldotInitials.y - this.diff;
        // this.bldot.setAttribute("cy", this.bldotCurrents.y);
        // this.trdotCurrents.x = this.trdotInitials.x - this.diff;
        // this.trdot.setAttribute("cx", this.trdotCurrents.x);
        // this.mrdotCurrents.x = this.mrdotInitials.x - this.diff;
        // this.mrdotCurrents.y = this.mrdotInitials.y - this.diff/2;
        // this.mrdot.setAttribute("cx", this.mrdotCurrents.x);
        // this.mrdot.setAttribute("cy", this.mrdotCurrents.y);
        // this.mldotCurrents.y = this.mldotInitials.y - this.diff/2;
        // this.mldot.setAttribute("cy", this.mldotCurrents.y);
        // this.mtdotCurrents.x = this.mtdotInitials.x - this.diff/2;
        // this.mtdot.setAttribute("cx", this.mtdotCurrents.x);
        // this.mbdotCurrents.x = this.mbdotInitials.x - this.diff/2;
        // this.mbdotCurrents.y = this.mbdotInitials.y - this.diff;
        // this.mbdot.setAttribute("cx", this.mbdotCurrents.x);
        // this.mbdot.setAttribute("cy", this.mbdotCurrents.y);  
      } else if (currentDotType === 'dot4') {

        // deal with image start pos
        // fastest performance in string number conversion: *1  >o<
        this.imgCurrentCoords.x = this.imgStartingCoords.x * 1 + this.diff;
        this.img.setAttribute('x', this.imgCurrentCoords.x);

        // this.bldotCurrents.x = this.bldotInitials.x * 1 + this.diff;
        // this.bldotCurrents.y = this.bldotInitials.y - this.diff;
        // // // console.log(this.bldotCurrents.x, this.bldotCurrents.y)

        // // dot.setAttribute("cx", this.bldotCurrents.x);
        // // dot.setAttribute("cy", this.bldotCurrents.y);

        // // //when bl: other dots update
        // this.tldotCurrents.x = this.tldotInitials.x * 1 + this.diff;
        // // this.tldot.setAttribute("cx", this.tldotCurrents.x);
        // this.brdotCurrents.y = this.brdotInitials.y - this.diff;
        // // this.brdot.setAttribute("cy", this.brdotCurrents.y);
        // this.mldotCurrents.x = this.mldotInitials.x * 1 + this.diff;
        // this.mldotCurrents.y = this.mldotInitials.y - this.diff/2;
        // // this.mldot.setAttribute("cx", this.mldotCurrents.x);
        // // this.mldot.setAttribute("cy", this.mldotCurrents.y);
        // this.mtdotCurrents.x = this.mtdotInitials.x * 1 + this.diff/2;
        // // this.mtdot.setAttribute("cx", this.mtdotCurrents.x);
        // this.mrdotCurrents.y = this.mrdotInitials.y - this.diff/2;
        // // this.mrdot.setAttribute("cy", this.mrdotCurrents.y);
        // this.mbdotCurrents.x = this.mbdotInitials.x * 1 + this.diff/2;
        // this.mbdotCurrents.y = this.mbdotInitials.y - this.diff;
        // // this.mbdot.setAttribute("cx", this.mbdotCurrents.x);
        // // this.mbdot.setAttribute("cy", this.mbdotCurrents.y);
      } else if (currentDotType === 'dot2') {
        // should change image starting pos
        this.imgCurrentCoords.y = this.imgStartingCoords.y * 1 + this.diff;
        this.img.setAttribute('y', this.imgCurrentCoords.y);

        // when shrink, x- y+;
        // this.trdotCurrents.x = this.trdotInitials.x - this.diff;
        // this.trdotCurrents.y = this.trdotInitials.y * 1 + this.diff;

        // // console.log(this.trdotCurrents.x, this.trdotCurrents.y);
        // dot.setAttribute("cx", this.trdotCurrents.x);
        // dot.setAttribute("cy", this.trdotCurrents.y);

        // when tr: other dots update
        // if the dot is on horizontal line, x increament would be 1/2. vice versa
        // this.tldotCurrents.y = this.tldotInitials.y * 1 + this.diff;
        // this.brdotCurrents.x = this.brdotInitials.x - this.diff;
        // this.mtdotCurrents.x = this.mtdotInitials.x - this.diff / 2;
        // this.mtdotCurrents.y = this.mtdotInitials.y * 1 + this.diff;
        // this.mldotCurrents.y = this.mldotInitials.y * 1 + this.diff / 2;
        // this.mbdotCurrents.x = this.mbdotInitials.x - this.diff / 2;
        // this.mrdotCurrents.x = this.mrdotInitials.x - this.diff;
        // this.mrdotCurrents.y = this.mrdotInitials.y * 1 + this.diff / 2;

        // this.tldot.setAttribute("cy", this.tldotCurrents.y);
        // this.brdot.setAttribute("cx", this.brdotCurrents.x);
        // this.mtdot.setAttribute("cx", this.mtdotCurrents.x);
        // this.mtdot.setAttribute("cy", this.mtdotCurrents.y);
        // this.mldot.setAttribute("cy", this.mldotCurrents.y);
        // this.mbdot.setAttribute("cx", this.mbdotCurrents.x);
        // this.mrdot.setAttribute("cx", this.mrdotCurrents.x);
        // this.mrdot.setAttribute("cy", this.mrdotCurrents.y);

      } else if (currentDotType === 'dot1') {
        // should change image starting pos
        this.imgCurrentCoords.x = this.imgStartingCoords.x * 1 + this.diff;
        this.imgCurrentCoords.y = this.imgStartingCoords.y * 1 + this.diff;
        this.img.setAttribute('x', this.imgCurrentCoords.x);
        this.img.setAttribute('y', this.imgCurrentCoords.y);

        // when shrink, x+ y+;
        // this.tldotCurrents.x = this.tldotInitials.x * 1 + this.diff;
        // this.tldotCurrents.y = this.tldotInitials.y * 1 + this.diff;

        // console.log(this.tldotCurrents.x, this.tldotCurrents.y);
        // dot.setAttribute("cx", this.tldotCurrents.x);
        // dot.setAttribute("cy", this.tldotCurrents.y);

        // when tl: other dots update
        // this.trdotCurrents.y = this.trdotInitials.y * 1 + this.diff;
        // this.bldotCurrents.x = this.bldotInitials.x * 1 + this.diff;
        // this.mtdotCurrents.x = this.mtdotInitials.x * 1 + this.diff / 2;
        // this.mtdotCurrents.y = this.mtdotInitials.y * 1 + this.diff;
        // this.mldotCurrents.x = this.mldotInitials.x * 1 + this.diff;
        // this.mldotCurrents.y = this.mldotInitials.y * 1 + this.diff / 2;
        // this.mbdotCurrents.x = this.mbdotInitials.x * 1 + this.diff / 2;
        // this.mrdotCurrents.y = this.mrdotInitials.y * 1 + this.diff / 2;

        // this.trdot.setAttribute("cy", this.trdotCurrents.y);
        // this.bldot.setAttribute("cx", this.bldotCurrents.x);
        // this.mtdot.setAttribute("cx", this.mtdotCurrents.x);
        // this.mtdot.setAttribute("cy", this.mtdotCurrents.y);
        // this.mldot.setAttribute("cx", this.mldotCurrents.x);
        // this.mldot.setAttribute("cy", this.mldotCurrents.y);
        // this.mbdot.setAttribute("cx", this.mbdotCurrents.x);
        // this.mrdot.setAttribute("cy", this.mrdotCurrents.y);
      } else if (currentDotType === 'dotml') {
        // should change image starting pos
        this.imgCurrentCoords.x = this.imgStartingCoords.x * 1 + this.diff;
        this.img.setAttribute('x', this.imgCurrentCoords.x);

        // when shrink, x+
        // this.mldotCurrents.x = this.mldotInitials.x * 1 + this.diff;

        // // console.log(this.mldotCurrents.x);
        // dot.setAttribute("cx", this.mldotCurrents.x);

        // // when ml: other dots update
        // this.tldotCurrents.x = this.tldotInitials.x * 1 + this.diff;
        // this.bldotCurrents.x = this.bldotInitials.x * 1 + this.diff;
        // this.mtdotCurrents.x = this.mtdotInitials.x * 1 + this.diff / 2;
        // this.mbdotCurrents.x = this.mbdotInitials.x * 1 + this.diff / 2;

        // this.tldot.setAttribute("cx", this.tldotCurrents.x);
        // this.bldot.setAttribute("cx", this.bldotCurrents.x);
        // this.mtdot.setAttribute("cx", this.mtdotCurrents.x);
        // this.mbdot.setAttribute("cx", this.mbdotCurrents.x);

      } else if (currentDotType === 'dotmr') {
        // should not change image starting pos

        // when shrink, x-
        // this.mrdotCurrents.x = this.mrdotInitials.x - this.diff;

        // // console.log(this.mrdotCurrents.x);
        // dot.setAttribute("cx", this.mrdotCurrents.x);

        // // when mr: other dots update
        // this.trdotCurrents.x = this.trdotInitials.x - this.diff;
        // this.brdotCurrents.x = this.brdotInitials.x - this.diff;
        // this.mtdotCurrents.x = this.mtdotInitials.x - this.diff / 2;
        // this.mbdotCurrents.x = this.mbdotInitials.x - this.diff / 2;

        // this.trdot.setAttribute("cx", this.trdotCurrents.x);
        // this.brdot.setAttribute("cx", this.brdotCurrents.x);
        // this.mtdot.setAttribute("cx", this.mtdotCurrents.x);
        // this.mbdot.setAttribute("cx", this.mbdotCurrents.x);

      } else if (currentDotType === 'dotmt') {
        // should change image starting pos
        this.imgCurrentCoords.y = this.imgStartingCoords.y * 1 + this.diff;
        this.img.setAttribute('y', this.imgCurrentCoords.y);

        // when shrink, y+
        // this.mtdotCurrents.y = this.mtdotInitials.y * 1 + this.diff;

        // // console.log(this.mtdotCurrents.y);
        // dot.setAttribute("cy", this.mtdotCurrents.y);

        // // when mt: other dots update
        // this.tldotCurrents.y = this.tldotInitials.y * 1 + this.diff;
        // this.trdotCurrents.y = this.trdotInitials.y * 1 + this.diff;
        // this.mldotCurrents.y = this.mldotInitials.y * 1 + this.diff / 2;
        // this.mrdotCurrents.y = this.mrdotInitials.y * 1 + this.diff / 2;

        // this.tldot.setAttribute("cy", this.tldotCurrents.y);
        // this.trdot.setAttribute("cy", this.trdotCurrents.y);
        // this.mldot.setAttribute("cy", this.mldotCurrents.y);
        // this.mrdot.setAttribute("cy", this.mrdotCurrents.y);

      } else if (currentDotType === 'dotmb') {
        // should not change image starting pos

        // when shrink, y+
        // this.mbdotCurrents.y = this.mbdotInitials.y - this.diff;

        // // console.log(this.mbdotCurrents.y);
        // dot.setAttribute("cy", this.mbdotCurrents.y);

        // // when mb: other dots update
        // this.bldotCurrents.y = this.bldotInitials.y - this.diff;
        // this.brdotCurrents.y = this.brdotInitials.y - this.diff;
        // this.mldotCurrents.y = this.mldotInitials.y - this.diff / 2;
        // this.mrdotCurrents.y = this.mrdotInitials.y - this.diff / 2;

        // this.bldot.setAttribute("cy", this.bldotCurrents.y);
        // this.brdot.setAttribute("cy", this.brdotCurrents.y);
        // this.mldot.setAttribute("cy", this.mldotCurrents.y);
        // this.mrdot.setAttribute("cy", this.mrdotCurrents.y);

      }

      // KEEP THE RATIO? seems no need, preserveAspectRatio would control it

    }
  }

  updateDotInitialsAndCleanup() {

    // image coord should be fixed at top-left
    if (this.imgStartingCoords.x !== this.imgCurrentCoords.x || this.imgStartingCoords.y !== this.imgCurrentCoords.y) {
      this.img.setAttribute('x', this.imgStartingCoords.x);
      this.img.setAttribute('y', this.imgStartingCoords.y);
      // this.shiftX = this.imgCurrentCoords.x - this.imgStartingCoords.x;
      // this.shiftY = this.imgCurrentCoords.y - this.imgStartingCoords.y;
      // let shiftX = this.imgCurrentCoords.x - this.imgStartingCoords.x;
      // let shiftY = this.imgCurrentCoords.y - this.imgStartingCoords.y;
      console.log('image coord, mouseup');
      // console.log(shiftX, shiftY);

      // all dots update
      // this.tldotCurrents.x = this.tldotCurrents.x - shiftX;
      // this.tldotCurrents.y = this.tldotCurrents.y - shiftY;
      // this.trdotCurrents.x = this.trdotCurrents.x - shiftX;
      // this.trdotCurrents.y = this.trdotCurrents.y - shiftY;
      // this.bldotCurrents.x = this.bldotCurrents.x - shiftX;
      // this.bldotCurrents.y = this.bldotCurrents.y - shiftY;
      // this.brdotCurrents.x = this.brdotCurrents.x - shiftX;
      // this.brdotCurrents.y = this.brdotCurrents.y - shiftY;
      // this.mldotCurrents.x = this.mldotCurrents.x - shiftX;
      // this.mldotCurrents.y = this.mldotCurrents.y - shiftY;
      // this.mtdotCurrents.x = this.mtdotCurrents.x - shiftX;
      // this.mtdotCurrents.y = this.mtdotCurrents.y - shiftY;
      // this.mrdotCurrents.x = this.mrdotCurrents.x - shiftX;
      // this.mrdotCurrents.y = this.mrdotCurrents.y - shiftY;
      // this.mbdotCurrents.x = this.mbdotCurrents.x - shiftX;
      // this.mbdotCurrents.y = this.mbdotCurrents.y - shiftY;
      // this.tldot.setAttribute("cx", this.tldotCurrents.x);
      // this.tldot.setAttribute("cy", this.tldotCurrents.y);
      // this.trdot.setAttribute("cx", this.trdotCurrents.x);
      // this.trdot.setAttribute("cy", this.trdotCurrents.y);
      // this.bldot.setAttribute("cx", this.bldotCurrents.x);
      // this.bldot.setAttribute("cy", this.bldotCurrents.y);
      // this.brdot.setAttribute("cx", this.brdotCurrents.x);
      // this.brdot.setAttribute("cy", this.brdotCurrents.y);
      // this.mldot.setAttribute("cx", this.mldotCurrents.x);
      // this.mldot.setAttribute("cy", this.mldotCurrents.y);
      // this.mtdot.setAttribute("cx", this.mtdotCurrents.x);
      // this.mtdot.setAttribute("cy", this.mtdotCurrents.y);
      // this.mrdot.setAttribute("cx", this.mrdotCurrents.x);
      // this.mrdot.setAttribute("cy", this.mrdotCurrents.y);
      // this.mbdot.setAttribute("cx", this.mbdotCurrents.x);
      // this.mbdot.setAttribute("cy", this.mbdotCurrents.y);

      // reset image currents
      this.imgCurrentCoords.x = this.imgStartingCoords.x;
      this.imgCurrentCoords.y = this.imgStartingCoords.y;
    }

    // The image relies on perserveRatio to adjust, so weight/height prop
    // may not be accurate. They need to be accurate since polygons' positioning
    // relies on them
    const currentDotType = this.currentDotId.slice(this.currentDotId.lastIndexOf('-') + 1);
    if (currentDotType === 'dot3' || currentDotType === 'dot1' || currentDotType === 'dot2' || currentDotType === 'dot4') {
      if (this.tempRatio === 0) {
        this.tempRatio = this.ratio;
      }
      if (this.imgCurrents.width / this.imgCurrents.height > this.tempRatio) {
        // height is resized trully. Width should be calculated due to preserveratio
        this.imgCurrents.width = this.imgCurrents.height * this.tempRatio;
        this.img.setAttribute('width', this.imgCurrents.width);
      } else if (this.imgCurrents.width / this.imgCurrents.height < this.tempRatio) {
        // width counterpart
        this.imgCurrents.height = this.imgCurrents.width / this.tempRatio;
        this.img.setAttribute('height', this.imgCurrents.height);
      }

      // console.log(this.imgCurrents.width, this.imgCurrents.height)
    }

    // after mouseup, set dot initials
    this.brdotInitials.x = this.brdotCurrents.x;
    this.brdotInitials.y = this.brdotCurrents.y;
    this.imgInitials.width = this.imgCurrents.width;
    this.imgInitials.height = this.imgCurrents.height;
    // cursorInitials.x = currentCursor.x;
    // cursorInitials.y = currentCursor.y;
    // console.log(this.img.getAttribute("width"), this.img.getAttribute("height"));

    // other dots update
    this.bldotInitials.x = this.bldotCurrents.x;
    this.bldotInitials.y = this.bldotCurrents.y;
    this.trdotInitials.x = this.trdotCurrents.x;
    this.trdotInitials.y = this.trdotCurrents.y;
    this.tldotInitials.x = this.tldotCurrents.x;
    this.tldotInitials.y = this.tldotCurrents.y;
    this.mrdotInitials.x = this.mrdotCurrents.x;
    this.mrdotInitials.y = this.mrdotCurrents.y;
    this.mldotInitials.x = this.mldotCurrents.x;
    this.mldotInitials.y = this.mldotCurrents.y;
    this.mtdotInitials.x = this.mtdotCurrents.x;
    this.mtdotInitials.y = this.mtdotCurrents.y;
    this.mbdotInitials.x = this.mbdotCurrents.x;
    this.mbdotInitials.y = this.mbdotCurrents.y;

    // image coord update
    // imgStartingCoords.x = imgCurrentCoords.x;
    // imgStartingCoords.y = imgCurrentCoords.y;

    // cleanup
    const {id, editorContainer} = this.props;
    const clone = editorContainer.querySelector('#imgClone-' + id);
    if (clone) {
      clone.remove();
    }
    this.img.setAttribute('class', '');

    // set paradrawing properties
    this.setParaElemDrawing();

  }

  isFocusedOnCurrentImage(event) {
    if (event.target.tagName === 'image') {
      // console.log(typeof this.props.id)
      const curTargetId = event.target.getAttribute('id');
      const curTargetIdSeperator = event.target.getAttribute('id').indexOf('-');
      const curTargetIndex = curTargetId.slice(curTargetIdSeperator + 1);

      if (this.props.id === curTargetIndex * 1) {
        return true;
      }
    }
    return false;
  }

  componentWillUnmount() {
    // console.log('image component unmounted')
    const {editorContainer} = this.props;
    gEvent.deleteEvent(this.docId, gEventName.Mousemove, this.handleMousemove);
    gEvent.deleteEvent(this.docId, gEventName.Mouseup, this.handleMouseup);
    gEvent.deleteEvent(this.docId, gEventName.Mousedown, this.mousedownContainer);
    // document ref
    // editorContainer.removeEventListener('mouseup', this.handleMouseup); // look out for context
    // // editorContainer.onMouseup = this.handleMouseup.bind(this)); // look out for context
    // editorContainer.removeEventListener('mousemove', this.handleMousemove);
    // // editorContainer.removeEventListener('click', this.clickContainer);
    // editorContainer.removeEventListener('mousedown', this.mousedownContainer);
    // editorContainer.removeEventListener('contextmenu', this.handleContextMenu);
    gEvent.deleteEvent(this.docId, 'contextmenu', this.handleContextMenu);

    // document.removeEventListener('mouseup', this.handleMouseup); // look out for context
    // // editorContainer.onMouseup = this.handleMouseup.bind(this)); // look out for context
    // document.removeEventListener('mousemove', this.handleMousemove);
    // document.removeEventListener('click', this.clickContainer);
  }

  setParaElemDrawing() {

    const {documentCore, item} = this.props;
    // document.getDrawingObjects().applyDrawingProps(this.img.getAttribute("width") * 1, this.img.getAttribute("height") * 1, this.state.preserveAspectRatio);
    //documentCore.getDrawingObjects().applyDrawingProps(this.img.getAttribute('width') * 1, this.img.getAttribute('height') * 1, this.props.item.preserveAspectRatio);
    documentCore.applyDrawingProps(this.img.getAttribute('width') * 1, this.img.getAttribute('height') * 1, this.props.item.preserveAspectRatio);

    // const {item} = this.props;
    // fastest performance in string number conversion: *1  >o<
    // item.width = this.img.getAttribute("width") * 1;
    // item.widthVisible = item.width;
    // item.height = this.img.getAttribute("height") * 1;
    // console.log(item);
    // console.log(this.img);

    // trigger recalculate
    // predicament: if not recalculate(), {item.positionY + item.height} can never auto update correctly

    // trigger rerender
    this.props.handleRefresh();

  }

}
