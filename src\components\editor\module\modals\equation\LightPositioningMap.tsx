import * as React from 'react';
import Dialog from '../../../ui/Dialog';
import Input from '../../../ui/Input';
import Button from '../../../ui/Button';
// import { message } from '../../../../../common/Message';
import { ParaEquation } from '../../../../../model/core/Paragraph/ParaDrawing';
import {IFRAME_MANAGER} from '../../../../../common/IframeManager';

interface IDialogProps {
    documentCore: any;
    visible: boolean;
    id: string;
    equation: ParaEquation;
    close: (name: string | number, bRefresh?: boolean) => void;
}

interface ITestContent {
    value1?: string;
    value2?: string;
    value3?: string;
    value4?: string;
    value5?: string;
    value6?: string;
    value7?: string;
    value8?: string;
    value9?: string;
}

interface IState {
    bRefresh: boolean;
}

export default class ToothBitMap extends React.Component<IDialogProps> {
    private visible: boolean;
    private data: ITestContent;
    private equationElemDom: HTMLElement;
    private oldData: ITestContent;
    constructor(props: any) {
        super(props);
        this.visible = this.props.visible;
        this.data = {};
    }

    public render(): any {
        return (
            <Dialog
                id={this.props.id}
                preventDefault={true}
                visible={this.visible}
                open={this.open}
                footer={this.renderFooter()}
                width={400}
                title={'光定位图'}
            >
                <div className='tooth-bit-map'>
                    <div>
                        <div className='w-33-10'>
                            <span className='w-label'>a:</span>
                            <div className='w-input'>
                                <Input
                                    name='value1'
                                    onChange={this.onChange}
                                    value={this.data.value1}
                                />
                            </div>
                        </div>
                        <div className='w-33-10'>
                            <span className='w-label'>b:</span>
                            <div className='w-input'>
                                <Input
                                    name='value2'
                                    onChange={this.onChange}
                                    value={this.data.value2}
                                />
                            </div>
                        </div>
                        <div className='w-33-10'>
                            <span className='w-label'>c:</span>
                            <div className='w-input'>
                                <Input
                                    name='value3'
                                    onChange={this.onChange}
                                    value={this.data.value3}
                                />
                            </div>
                        </div>
                    </div>
                    <div>
                        <div className='w-33-10'>
                            <span className='w-label'>d:</span>
                            <div className='w-input'>
                                <Input
                                    name='value4'
                                    onChange={this.onChange}
                                    value={this.data.value4}
                                />
                            </div>
                        </div>
                        <div className='w-33-10'>
                            <span className='w-label'>e:</span>
                            <div className='w-input'>
                                <Input
                                    name='value5'
                                    onChange={this.onChange}
                                    value={this.data.value5}
                                />
                            </div>
                        </div>
                        <div className='w-33-10'>
                            <span className='w-label'>f:</span>
                            <div className='w-input'>
                                <Input
                                    name='value6'
                                    onChange={this.onChange}
                                    value={this.data.value6}
                                />
                            </div>
                        </div>
                    </div>
                    <div>
                        <div className='w-33-10'>
                            <span className='w-label'>g:</span>
                            <div className='w-input'>
                                <Input
                                    name='value7'
                                    onChange={this.onChange}
                                    value={this.data.value7}
                                />
                            </div>
                        </div>
                        <div className='w-33-10'>
                            <span className='w-label'>h:</span>
                            <div className='w-input'>
                                <Input
                                    name='value8'
                                    onChange={this.onChange}
                                    value={this.data.value8}
                                />
                            </div>
                        </div>
                        <div className='w-33-10'>
                            <span className='w-label'>i:</span>
                            <div className='w-input'>
                                <Input
                                    name='value9'
                                    onChange={this.onChange}
                                    value={this.data.value9}
                                />
                            </div>
                        </div>
                    </div>

                </div>

            </Dialog>
        );
    }

    public componentDidMount(): void {
        //
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        this.visible = nextProps.visible;
    }

    private renderFooter(): any {
        return (
            <span>
                <Button onClick={this.close}>取消</Button>
                <Button type='primary' onClick={this.confirm}>确认</Button>
            </span>
        );
    }

    private open = (): void => {
        const equation = this.props.equation;
        // let equationElemDom: HTMLElement;
        const data = this.data = {};
        const oldData = this.oldData = {};
        const svg = equation.equationElem;
        if (svg) {
            const equationElemDom = this.equationElemDom = new DOMParser().parseFromString(
                svg,
                'text/xml',
            ).documentElement;
            const texts = equationElemDom.querySelectorAll('text');
            if (texts.length > 0) {
                texts.forEach((text, index) => {
                    const curText = text.innerHTML.trim();
                    data['value' + ++index] = curText;
                    oldData['value' + index] = curText;
                });
            }
        }

        this.setState({});
    }

    private onChange = (value: any, name: string): void => {
        this.data[name] = value;
        this.setState({});
    }

    private close = (bRefresh: boolean = false): void => {
        this.props.close(this.props.id, bRefresh);
        this.visible = false;
        this.setState({});
    }

    private isSameData(): boolean {
        const datas = this.data;
        const oldDatas = this.oldData;

        const keys = Object.keys(datas);
        for (let index = 0, length = keys.length; index < length; index++) {
            const key = keys[index];
            if (datas[key] !== oldDatas[key]) {
                return false;
            }
        }

        return true;
    }

    private confirm = (): void => {
        if (this.isSameData()) {
            this.close();
            return;
        }
        const documentCore = this.props.documentCore;
        const datas = this.data;
        const dom = this.equationElemDom;
        const texts = dom.querySelectorAll('text');
        IFRAME_MANAGER.setDocId(documentCore.getCurrentId());
        const textDom = IFRAME_MANAGER.getLabelElement(); // IFRAME_MANAGER.getTextElement();
        const obj = {
            width0: 9,
            width1: 9,
            width2: 9,
            oldWidth0: 9,
            oldWidth1: 9,
            oldWidth2: 9,
        };
        textDom.style.fontSize = '14px';
        textDom.style.fontFamily = 'Arial';
        // let bChange = false;
        const oldData = this.oldData;
        Object.keys(datas)
        .forEach((key, index) => {
            const text = datas[key];
            const curIndex = index % 3;
            const oldText = oldData[key];
            if (text !== oldData[key]) {
                texts[index].innerHTML = text;
            }

            let curWidth: number;
            if (!text) {
                curWidth = 0;
            } else {
                textDom.innerHTML = text;
                curWidth = textDom.offsetWidth; // textDom.clientWidth;
            }
            if (curWidth > obj['width' + curIndex]) {
                obj['width' + curIndex] = curWidth;
            }

            if (!oldText) {
                curWidth = 0;
            } else {
                textDom.innerHTML = oldText;
                curWidth = textDom.offsetWidth; // textDom.clientWidth;
            }
            if (curWidth > obj['oldWidth' + curIndex]) {
                obj['oldWidth' + curIndex] = curWidth;
            }
        });
        const sub1 = obj.width0 - obj.oldWidth0;
        const sub2 = obj.width1 - obj.oldWidth1;
        const sub3 = obj.width2 - obj.oldWidth2;
        // const lines = dom.querySelectorAll('line');
        if (Math.abs(sub1) > 0.1) {
            this.setTextPosition(sub1, texts, 1);
        }
        let activeSub = sub2 + sub1;
        if (Math.abs(activeSub ) > 0.1) {
            this.setTextPosition(activeSub, texts, 2);
        }
        activeSub += sub3;
        let drawWidth: number;
        if (Math.abs(activeSub ) > 1) {
            const oldWidth = parseInt(dom.getAttribute('width'), 10);
            const width = (oldWidth + activeSub) + '';
            dom.setAttribute('width', width);
            // lines[0].setAttribute('x2', width);
            drawWidth = this.props.equation.width;
            drawWidth = drawWidth / oldWidth * activeSub + drawWidth;
        }

        // const height = parseInt(dom.getAttribute('height'), 10);
        // const x1 = obj.width0 + 10;
        // const x2 = obj.width1 + 10;
        // const x3 = obj.width2 + 20;
        // const width = x1;
        // console.log(dom);
        // textDom.innerHTML =
        // this.props.close(this.props.id, true);
        // window['dom'] = dom;

        // 修改属性
        const svgStr = dom.outerHTML;
        const drawObj = documentCore.getDrawingObjects();
        const svgConvertedURI = drawObj.convertSVGToImageString(svgStr);
        // drawObj.setDrawingProp(this.props.equation.name, {
        documentCore.setDrawingProp(this.props.equation.name, {
            width: drawWidth,
            src: svgConvertedURI,
            equationElem: svgStr,
        });
        this.close(true);
    }

    private setTextPosition(sub: number, texts: any, index: number, step: number = 3): void {
        const text1 = texts[index];
        const text2 = texts[index + step];
        const text3 = texts[index + 2 * step];
        const x = (parseFloat(text1.getAttribute('x')) + sub) + '';
        text1.setAttribute('x', x);
        text2.setAttribute('x', x);
        text3.setAttribute('x', x);
    }
}
