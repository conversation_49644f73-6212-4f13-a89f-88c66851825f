import { XmlComponent } from '../../xml-components';
import { LastModifiedBy, LastSavedT, CreatedBy, CreatedTime, LastButOne } from './saveHistoryElements';

export class SaveHistory extends XmlComponent {

  constructor() {
      super('SaveHistory');
  }

  public addLastModifiedBy(name: string): SaveHistory {
    this.root.push(new LastModifiedBy(name));
    return this;
  }

  public addLastSavedT(date: string): SaveHistory {
    this.root.push(new LastSavedT(date));
    return this;
  }

  // public addLastPrintedT(date: string): SaveHistory {
  //   this.root.push(new LastPrintedT(date));
  //   return this;
  // }

  public addCreatedBy(ip: string): SaveHistory {
    this.root.push(new CreatedBy(ip));
    return this;
  }

  public addCreatedTime(date: string): SaveHistory {
    this.root.push(new CreatedTime(date));
    return this;
  }

  public addLastButOne(date: string): SaveHistory {
    this.root.push(new LastButOne(date));
    return this;
  }

}
