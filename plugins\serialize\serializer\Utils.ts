import { WasmInstance } from '@/common/WasmInstance';
import { AlignmentType, BorderStyle, WidthType } from 'docx';
import { AlignType, GenericBox, TableBorderLineStyle, TableMeasurement, TableWidthType } from './ISerial';

let ppi;
// let measureSVGContainer;
// let textElement;
// let textNode;
/**
 * 获取当前屏幕ppi
 */
export function getCurPPI(): number {
    if (ppi === undefined) {
        const div = document.createElement('div');
        div.style.width = '1in';
        const body = document.getElementsByTagName('body')[0];
        body.appendChild(div);
        const curPPI: any = document.defaultView.getComputedStyle(div, null)
            .getPropertyValue('width');
        body.removeChild(div);
        return ppi = parseFloat(curPPI);
    } else {
        return ppi;
    }

}
/**
 * MM毫米转换Px像素
 * @param mValue
 */
export function getPxForMM(mValue: number): number {
    // key-wasm by tinyzhi
    // return mValue * getCurPPI() / 25.4;
    const temp = getCurPPI();
    return WasmInstance.instance._Util_getPM(mValue, temp);
    // end by tinyzhi
}

/**
 * Px像素转换MM毫米
 * @param pxValue
 */
export function getMMFromPx(pxValue: number): number {
    // key-wasm by tinyzhi
    // return pxValue * 25.4 / getCurPPI();
    const temp = getCurPPI();
    return WasmInstance.instance._Util_getMP(pxValue, temp);
    // end by tinyzhi
}

const pixelMultiple = 16 / 10.7;
const pixelToPtMap = new Map([
    [58.7, 42],
    [48, 36],
    [34.7, 26],
    [32, 24],
    [29.3, 22],
    [24, 18],
    [21.3, 16],
    [20, 15],
    [18.7, 14],
    [16, 12],
    [14, 10.5],
    [12, 9],
    [10, 7.5],
    [8.7, 6.5],
    [7.3, 5.5],
    [6.7, 5],
    [10.7, 8],
    [13.3, 10],
    [14.7, 11],
    [24, 18],
    [26.7, 20],
    [37.3, 28],
    [64, 48],
    [96, 72],
]);

/** px2pt */
export function pxToPt(px: number): number {
    // 以下为7版本使用
    if (pixelToPtMap.has(px)) {
        return pixelToPtMap.get(px);
    }
    return px * (72 / 96);
}

/** 去除十六进制颜色字符串中的# */
export function pureHexColor(color: string): string {
    if (!color) {
        return '';
    }
    const match = /rgb\((\d+)[^\d]+(\d+)[^\d]+(\d+)\)/i.exec(color);
    if (match) {
        color = rgbToHex(Number.parseInt(match[1]),
            Number.parseInt(match[2]),
            Number.parseInt(match[3]));
    }
    return color && color.replace('#', '').padStart(6, '0');
}

function rgbToHex(r: number, g: number, b: number): string {
    return ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
}
const colorNameMap = new Map([
    ['FFFF00', 'yellow'],
    ['008000', 'green'],
    ['0000FF', 'blue'],
    ['FF0000', 'red'],
    ['9073FE', 'darkBlue'],
    ['417505', 'darkGreen'],
    ['BD10E0', 'darkMagenta'],
    ['C00000', 'darkRed'],
    ['F5A623', 'darkYellow'],
    ['4A4A4A', 'darkGray'],
    ['9B9B9B', 'lightGray'],
    ['000000', 'black'],
]);
/** 将颜色转换为预设颜色 */
export function hexColorToName(color: string): string {
    if (!color) {
        return '';
    }

    const colorName = colorNameMap.get(pureHexColor(color)?.toUpperCase());
    return colorName || '';
}

/** 将毫米值转换为docx缩进的单位数值 */
export function mmToDocxIndentLength(mm: number): number {
    if (typeof mm !== 'number') {
        return 0;
    }
    // 1 cm
    const width = 567;
    return mm * width / 10;
}

/** 将docx缩进的单位数值转换为毫米值 */
export function transDocxLengthToMM(num: number): number {
    if (typeof num !== 'number') {
        return 0;
    }
    const width = 567;
    return num * 10 / width;
}

/** 映射对齐方式 */
export function convertAlignment(align: AlignType): AlignmentType | undefined {
    switch (align) {
        case AlignType.Center: {
            return AlignmentType.CENTER;
        }
        case AlignType.Left: {
            return AlignmentType.LEFT;
        }
        case AlignType.Right: {
            return AlignmentType.RIGHT;
        }
        case AlignType.Justify: {
            return AlignmentType.JUSTIFIED;
        }
    }
    return undefined;
}

/** 将像素转换为docx内部单位 */
export function transPxToDocxLength(px: number): number {
    return mmToDocxIndentLength(getMMFromPx(px));
}

/** 转换边框样式 */
export function convertBorderStyle(style: TableBorderLineStyle): BorderStyle {
    switch (style) {
        case TableBorderLineStyle.None: {
            return BorderStyle.NONE;
        }
        case TableBorderLineStyle.Dash: {
            return BorderStyle.DASHED;
        }
        case TableBorderLineStyle.Single: {
            return BorderStyle.SINGLE;
        }
    }
}

/** 转换表格宽度类型 */
export function convertWidthType(width: TableWidthType): WidthType {
    switch (width) {
        case TableWidthType.Auto: {
            return WidthType.AUTO;
        }
        case TableWidthType.Pencent: {
            return WidthType.PERCENTAGE;
        }
        case TableWidthType.Mm: {
            return WidthType.DXA;
        }
    }
}

/** 构造边框参数对象 */
export function buildBorderOption(border: any): any {
    if (!border || border.size === 0) {
        return {
            style: BorderStyle.NONE,
            size: 0,
            color: '000000',
        };
    }
    return {
        style: convertBorderStyle(border.value),
        color: pureHexColor(border.color.toHex()),
        size: border.size,
        space: border.space,
    };
}

/** 构造单元格边距 */
export function buildTableCellMargin(margin: GenericBox<TableMeasurement>): any {
    if (!margin) {
        return undefined;
    }
    return {
        top: transPxToDocxLength(margin.top.width),
        bottom: transPxToDocxLength(margin.bottom.width),
        right: transPxToDocxLength(margin.right.width),
        left: transPxToDocxLength(margin.left.width)
    };
}
