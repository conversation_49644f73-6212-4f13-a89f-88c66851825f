import { MenuItemIndex, NewControlType, ToolbarIndex } from '@/common/commonDefines';
import { DocumentCore } from '@/model/DocumentCore';

export interface ILoadComponentProp {
    [key: string]: { // showTypeModal方法内type值，设置弹窗可见值，否则必须在里面对象填写name属性
        [key: string]: any, // 其他参数，可传递进弹窗内
        component: () => Promise<any>; // 懒加载组件
    };
}

export interface ILoadModal {
    render(): any;
}

export const MODAL_COMPONENT_NAME = 'bModalIndex-';

export interface IModalHostProps {
    documentCore: DocumentCore;
    close: (name: string, bRefresh?: boolean) => void;
    refresh: (bRefresh: boolean) => void;
    host?: any;
    type?: number; // 打开弹窗的类型
    option?: any; // 打开弹窗时传递进来的参数
    [key: string]: any;
}

export class ModalComponent {
    private _host: IModalHostProps;
    constructor(host: IModalHostProps) {
        this._host = host;
    }

    public get components(): ILoadComponentProp {
        const host = this._host;
        const components: ILoadComponentProp = {
            // [ToolbarIndex.CascadeManager]: {
            //     component: () => import('./cascade-manager/Cascade')
            // },
            [MenuItemIndex.Formula]: {
                equation: host.option,
                component: () => import('./equation')
            },
            [MenuItemIndex.ParaButton]: {
                option: host.option,
                component: () => import('./ParaButton')
            },
            // [ToolbarIndex.InsertTable]: {
            //     image: host.option,
            //     component: () => import('./InsertTable')
            // },
            // [ToolbarIndex.InsertTable]: {
            //     component: () => import('./InsertTable')
            // },
            // [ToolbarIndex.PageSet]: {
            //     component: () => import('./PageSetting')
            // },
            // [ToolbarIndex.Region]: {
            //     property: host.option,
            //     component: () => import('./Region')
            // },
            [ToolbarIndex.MedEquation]: {
                component: () => import('./InsertEquation')
            },
        }
        return components;
        // const components: ILoadComponentProp = {
        //     [ToolbarIndex.CascadeManager]: {
        //         component: () => import('./cascade-manager/Cascade')
        //     },
        //     [ToolbarIndex.ReviewPrint]: {
        //         options: host.option,
        //         component: () => import('./PrintDialog')
        //     },
        //     [ToolbarIndex.NumberBox]: {
        //         property: host.option,
        //         component: () => import('./NewControlNumber')
        //     },
        //     [ToolbarIndex.TextBox]: {
        //         property: host.option,
        //         type: NewControlType.TextBox,
        //         component: () => import('./NewControlText')
        //     },
        //     [ToolbarIndex.Section]: {
        //         property: host.option,
        //         type: NewControlType.Section,
        //         component: () => import('./NewControlText')
        //     },
        //     [ToolbarIndex.SignatureBox]: {
        //         property: host.option,
        //         component: () => import('./NewSignatureBox')
        //     },
        //     [ToolbarIndex.SingleOrMultipleBox]: {
        //         property: host.option,
        //         component: () => import('./NewComboBox')
        //     },
        //     [ToolbarIndex.ListBox]: {
        //         property: host.option,
        //         type: NewControlType.ListBox,
        //         component: () => import('./NewComboBox')
        //     },
        //     [ToolbarIndex.Combox]: {
        //         property: host.option,
        //         type: NewControlType.Combox,
        //         component: () => import('./NewComboBox')
        //     },
        //     [ToolbarIndex.MultiCombox]: {
        //         property: host.option,
        //         type: NewControlType.MultiCombox,
        //         component: () => import('./NewComboBox')
        //     },
        //     [ToolbarIndex.MultiListBox]: {
        //         property: host.option,
        //         type: NewControlType.MultiListBox,
        //         component: () => import('./NewComboBox')
        //     },
        //     [ToolbarIndex.AutoTest]: {
        //         callback: host.callback,
        //         property: host.option?.data,
        //         component: () => import('./AutoTest')
        //     },
        //     [MenuItemIndex.Font]: {
        //         component: () => import('./Font')
        //     },
        //     [ToolbarIndex.AutoTestPlay]: {
        //         property: host.option || {},
        //         component: () => import('./AutoTestPlay')
        //     },
        //     [MenuItemIndex.Paragraph]: {
        //         component: () => import('./Paragraph')
        //     },
        //     [ToolbarIndex.Paragraph]: {
        //         component: () => import('./Paragraph')
        //     },
        //     [MenuItemIndex.Image]: {
        //         image: host.option,
        //         component: () => import('./Image')
        //     },
        //     [MenuItemIndex.SplitCell]: {
        //         component: () => import('./SplitCell')
        //     },
        //     [ToolbarIndex.SpecialCharacter]: {
        //         component: () => import('./SpecialCharacter')
        //     },
        //     [MenuItemIndex.DeleteRow]: {
        //         property: host.type,
        //         component: () => import('./DeleteCell')
        //     },
        //     [MenuItemIndex.DeleteCol]: {
        //         property: host.type,
        //         component: () => import('./DeleteCell')
        //     },
        //     [MenuItemIndex.Table]: {
        //         tableName: (host.option || {}).name,
        //         component: () => import('./TableSetting')
        //     },
        //     [ToolbarIndex.TableProps]: {
        //         tableName: (host.option || {}).name,
        //         component: () => import('./TableSetting')
        //     },
        //     [MenuItemIndex.FormulaCalc]: {
        //         component: () => import('./FormulaCalc')
        //     },
        //     [MenuItemIndex.Formula]: {
        //         image: host.option,
        //         component: () => import('./equation')
        //     },
        //     [ToolbarIndex.InsertTable]: {
        //         image: host.option,
        //         component: () => import('./InsertTable')
        //     },
        //     [ToolbarIndex.InsertTable]: {
        //         component: () => import('./InsertTable')
        //     },
        //     [ToolbarIndex.PageSet]: {
        //         component: () => import('./PageSetting')
        //     },
        //     [ToolbarIndex.Region]: {
        //         property: host.option,
        //         component: () => import('./Region')
        //     },
        //     [ToolbarIndex.MedEquation]: {
        //         component: () => import('./InsertEquation')
        //     },
        //     [ToolbarIndex.Checkbox]: {
        //         property: host.option,
        //         component: () => import('./NewControlCheck')
        //     },
        //     [ToolbarIndex.Checkbox]: {
        //         property: host.option,
        //         component: () => import('./NewControlCheck')
        //     },
        //     [ToolbarIndex.MultiRadio]: {
        //         property: host.option,
        //         type: NewControlType.MultiRadio,
        //         component: () => import('./NewControlRadio')
        //     },
        //     [ToolbarIndex.Radio]: {
        //         property: host.option,
        //         type: NewControlType.RadioButton,
        //         component: () => import('./NewControlRadio')
        //     },
        //     [ToolbarIndex.DateBox]: {
        //         property: host.option,
        //         component: () => import('./NewDateBox')
        //     },
        //     [ToolbarIndex.SignatureBox]: {
        //         property: host.option,
        //         component: () => import('./NewSignatureBox')
        //     },
        //     [ToolbarIndex.AddressBox]: {
        //         property: host.option,
        //         component: () => import('./NewAddressBox')
        //     },
        //     [ToolbarIndex.ViewScale]: {
        //         width: host.host.myRef.current.clientWidth,
        //         component: () => import('./ViewScale')
        //     },
        //     [ToolbarIndex.HeaderFooter]: {
        //         component: () => import('./NewHeaderFooter')
        //     },
        //     [ToolbarIndex.NavMenu]: {
        //         component: () => import('./NavMenu')
        //     },
        //     [ToolbarIndex.PageNum]: {
        //         component: () => import('./NewPageNum')
        //     },
        //     [ToolbarIndex.WaterMark]: {
        //         component: () => import('./NewWatermark')
        //     },
        //     [ToolbarIndex.RevisionSetting]: {
        //         component: () => import('./Revision')
        //     },
        //     [ToolbarIndex.RevisionAcceptReject]: {
        //         component: async () => {
        //             const obj = await import('./Revision');
        //             return {default: obj.RevisionAcceptReject};
        //         }
        //     },
        //     [ToolbarIndex.EmptyParagraphRemove]: {
        //         component: async () => {
        //             const obj = await import('./Paragraph');
        //             return {default: obj.EmptyParagraghRemove};
        //         }
        //     },
        // };
        return components;
    }
}
