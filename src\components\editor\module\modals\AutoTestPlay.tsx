import * as React from 'react';
import Dialog from '../../ui/Dialog';
import Input from '../../ui/Input';
import Button from '../../ui/Button';
import { message } from '../../../../common/Message';

interface IDialogProps {
    documentCore: any;
    visible: boolean;
    id: string;
    host?: any;
    callback?: (option: any) => void;
    close: (name: string | number, bRefresh?: boolean) => void;
    autoId?: string;
}

interface ITestContent {
    id?: string;
    step?: string;
}

export default class DeleteCellDialog extends React.Component<IDialogProps, {}> {
    private visible: boolean;
    private data: ITestContent;
    private result: any;
    constructor(props: any) {
        super(props);
        this.visible = this.props.visible;
        this.data = {};
    }

    public render(): any {
        return (
            <Dialog
                id={this.props.id}
                preventDefault={true}
                visible={this.visible}
                open={this.open}
                footer={this.renderFooter()}
                width={400}
                height={300}
                left={100}
                scale={true}
                title={'自动化测试'}
            >
                <div>
                    <div className='editor-line'>
                        <div className='w-70'>ID</div>
                        <div className='right-auto'>
                            <Input
                                name='id'
                                value={this.data.id}
                                onChange={this.onChange}
                            />
                        </div>
                    </div>
                    <div className='editor-line'>
                        <div className='w-70' style={{verticalAlign: 'top', lineHeight: '40px'}}>差异数据</div>
                        <div className='right-auto'>
                            <textarea
                                style={{width: '100%', height: '130px'}}
                                name='step'
                                value={this.data.step}
                                onChange={this.textareaChange}
                            >
                                {this.data.step}
                            </textarea>
                        </div>
                    </div>
                </div>
            </Dialog>
        );
    }

    public componentDidMount(): void {
        //
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        this.visible = nextProps.visible;
    }

    private renderFooter(): any {
        return (
            <span>
                <Button onClick={this.close}>取消</Button>
                <Button type='primary' onClick={this.confirm}>查询</Button>
                <Button type='primary' onClick={this.sendData}>提交</Button>
            </span>
        );
    }

    private open = (): void => {
        const id = this.props.autoId;
        this.data = {id};
        if (id) {
            this.confirm();
        } else {
            this.setState({});
        }
    }

    private onChange = (value: any, name: string): void => {
        this.data[name] = value;
    }

    private textareaChange = (e: any): void => {
        this.data.step = e.target.value;
        this.setState({});
    }

    private close = (bRefresh?: boolean): void => {
        this.props.close(this.props.id, bRefresh);
        this.visible = false;
        this.setState({});
    }

    private confirm = (): void => {
        const data = this.data;
        if (!data.id) {
            message.error('请输入ID');
            return;
        }
        // this.close(true);
        this.props.host.createAutoTestEvent()
        .then((res) => {
            res.playEvent(data.id)
            .then((result) => {
                const contents = result.content;
                if (contents.length !== 0) {
                    this.data.step = JSON.stringify(contents);
                    this.result = result;
                    this.setState({});
                }
            });
        });
    }

    private sendData = (): void => {
        if (this.props.callback) {
            this.props.callback(this.result);
        }
    }
}
