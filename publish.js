var npm = require('npm-utils')

var la = require('lazy-ass');
var is = require('check-more-types');
var npm_utils = require('npm-utils');

var run = npm_utils.test;

la(is.fn(run), 'expected function');

function publish() {
    var command = 'npm publish';
    if (process.argv.length > 2) {
        command += " " + process.argv.slice(2).join(" ");
    }
    console.log(command);
    return run(command);
}

function onError(err) {
    console.error(err)
    process.exit(-1)
}

npm.setAuthToken()
    .then(publish)
    .catch(onError);
