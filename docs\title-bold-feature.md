# NewControl标题粗体功能

## 功能概述

为NewControlText和NewControlSection添加了可配置的标题粗体功能。当启用此功能时：
- 标题文本显示为粗体
- 标题后录入的文字保持非粗体（继承其他字体属性如字体族、字号、颜色等）
- 占位符机制正常工作

## 配置方式

### 1. 通过INewControlProperty接口配置

```typescript
const property: INewControlProperty = {
    newControlName: "test_control",
    newControlType: NewControlType.TextBox,
    newControlTitle: "患者姓名",
    titleBold: true, // 启用标题粗体功能
    // 其他属性...
};
```

### 2. 通过外部接口配置

```typescript
// 设置标题粗体
documentCore.setNewControlProp("test_control", "TitleBold", true);

// 获取标题粗体状态
const isBold = documentCore.getNewControlProp("test_control", "TitleBold");
```

### 3. 通过NewControl实例配置

```typescript
const newControl = documentCore.getNewControlByName("test_control");
if (newControl) {
    // 设置标题粗体
    newControl.setTitleBold(true);
    
    // 获取标题粗体状态
    const isBold = newControl.isTitleBold();
}
```

## 实现原理

### 1. 标题粗体设置
- 在`setTitle`方法中，当`bTitleBold`为true时，使用粗体TextProperty添加标题文本
- 标题文本使用`FontWeightType.Bold`属性

### 2. 内容字体继承控制
- 在`addPlaceHolderContent`方法中，对于启用了标题粗体的TextBox/Section
- 明确设置`contentTextProperty.fontWeight = FontWeightType.Normal`
- 确保录入的文字不继承标题的粗体属性

### 3. 占位符机制兼容
- 利用现有的`bTextBox`判断机制
- 有标题时走else分支，使用`getDefaultFont()`并设置非粗体
- 无标题时走if分支，正常继承startBorderPortion属性

## 使用场景

### 场景1：创建带粗体标题的文本框
```typescript
const textBoxProperty: INewControlProperty = {
    newControlName: "patient_name",
    newControlType: NewControlType.TextBox,
    newControlTitle: "患者姓名：",
    titleBold: true,
    newControlPlaceHolder: "请输入患者姓名"
};

documentCore.addNewControl(textBoxProperty);
```

### 场景2：创建带粗体标题的节
```typescript
const sectionProperty: INewControlProperty = {
    newControlName: "medical_history",
    newControlType: NewControlType.Section,
    newControlTitle: "病史：",
    titleBold: true,
    newControlPlaceHolder: "请输入病史信息"
};

documentCore.addNewControl(sectionProperty);
```

### 场景3：动态切换标题粗体状态
```typescript
const newControl = documentCore.getNewControlByName("patient_name");
if (newControl) {
    // 启用标题粗体
    newControl.setTitleBold(true);
    
    // 禁用标题粗体
    newControl.setTitleBold(false);
}
```

## 注意事项

1. **默认状态**：titleBold默认为false，需要显式设置为true才启用
2. **适用范围**：仅适用于NewControlType.TextBox和NewControlType.Section
3. **兼容性**：完全兼容现有的占位符、字体继承等机制
4. **历史记录**：setTitleBold方法支持历史记录（需要实现对应的Change类）

## 技术细节

### 修改的文件
- `src/model/core/NewControl/NewControl.ts` - 核心实现
- `src/common/commonDefines.ts` - 接口定义和枚举
- `src/common/external/NewControl.ts` - 外部接口支持

### 新增的属性和方法
- `NewControl.bTitleBold: boolean` - 控制标题粗体的私有属性
- `NewControl.isTitleBold(): boolean` - 获取标题粗体状态
- `NewControl.setTitleBold(bold: boolean): number` - 设置标题粗体状态
- `INewControlProperty.titleBold?: boolean` - 接口属性
- `NewControlPropName.TitleBold` - 属性名枚举

### 核心逻辑
1. 在构造函数中初始化`bTitleBold`属性
2. 在`setTitle`方法中根据`bTitleBold`决定是否使用粗体属性
3. 在`addPlaceHolderContent`方法中确保录入文字不继承粗体
4. 在`setProperty`和`getProperty`方法中处理titleBold属性
