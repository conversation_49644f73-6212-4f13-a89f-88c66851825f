import { WasmInstance } from '@/common/WasmInstance';
import IAsyncExternalInterface, { IExternalEvent } from '../common/external/IAsyncExternalInterface';
import client from './client';

interface IEditorOptions {
  bShowMenu: boolean;
  bShowToolbar: boolean;
  id?: string;
  height?: number;
  isTest?: boolean;
  bEnableScroll?: boolean;
  bScrollX?: boolean;
  bScrollY?: boolean;
}
interface IInitOptions {
  dom: Element;
  src: string;
  option: IEditorOptions;
}

interface IEditorInstance {
  id: string;
  editor: IAsyncExternalInterface
}

export class Editor {

  private instance: IEditorInstance;

  private iframe: HTMLIFrameElement;

  public async init(options: IInitOptions): Promise<this> {
    const { dom, src } = options;
    const iframe = document.createElement('iframe');
    iframe.width = '100%';
    iframe.height = '100%';
    iframe.frameBorder = '0';
    iframe.src = src;
    iframe.scrolling = 'no';
    dom.appendChild(iframe);

    // iframe加载完成
    await new Promise((resolve, reject) => {
      iframe.onload = () => resolve(null);
    });
    this.iframe = iframe;

    // 在调用WASM相关功能前确保WASM已初始化
    if (!WasmInstance.isWasmReady()) {
      await WasmInstance.createWasmInstsanceAsync();
    }

    // 初始化编辑器
    await this.invork(null, 'init');

    const option = options.option || {} as any;

    await this.createEditor(option);
    return this;
  }

  /*
   * getAsyncEditor别名，仅仅是兼容老版本
   */
  public async getEditor(): Promise<IAsyncExternalInterface> {
    return this.getAsyncEditor();
  }

  public async getAsyncEditor(): Promise<IAsyncExternalInterface> {
    return this.instance.editor;
  }

  public setEvent(events: IExternalEvent): any {
    if (!events) {
      return;
    }
    return client.setEditorEvent(this.instance.id, events);
  }

  public removeEvent(sNames?: string[]): boolean {
    return client.removeEditorEvent(this.instance.id, sNames);
  }

  private async createEditor(options: {bShowMenu: boolean; bShowToolbar: boolean;}): Promise<void> {
    if (!this.instance) {
      const id: string = await this.invork(null, 'createEditor', [options]);
      const editor = deepProxy({} as any, (target, method) => {
        // console.log(method)
        if (method === 'then') {
          return;
        } else {
          return (...params) => {
            return this.invork(id, method, params);
          };
        }
      });
      // editor.NIS = {};
      editor.CTT = {};
      // const editor =  new Proxy({} as any, {
      //   get: (target, method: string, dir: any) => {
      //     console.log(method);
      //     console.log(dir);
      //     const result = Reflect.get(target, method);
      //     console.log(result);
      //     // 修复async函数自动调用then
      //     if (method === 'then') {
      //       return;
      //     } else {
      //       return async (...params) => {
      //         return await this.invork(id, method, params);
      //       }
      //     }
      //   }
      // });
      this.instance = {editor, id};
    }
  }

  private async invork(editorId: string, method: string, params: any[] = []): Promise<any> {
    if (!this.iframe) {
      console.log('编辑器已经关闭');
      return;
    }
    if (method === 'close') {
      this.removeEvent();
      if (this.iframe && this.iframe.parentNode) {
        this.iframe.outerHTML = '';
        this.iframe = null;
      }
      return Promise.resolve();
    }
    const res = await client.invork(this.iframe, editorId, method, params);
    return res;
  }

}

let parentKey: any;
/**
 * 对象、数组变化监听(增删改)
 * <AUTHOR>
 * @date 2020-04-22
 * @param {Object} obj
 * @param {Function} cb
 * @return {Proxy}
 */
function deepProxy(obj: any, cb: (a: any, b: any) => any): any {
    if (typeof obj === 'object') {
        for (const key in obj) {
            if (typeof obj[key] === 'object') {
                obj[key] = deepProxy(obj[key], cb);
            }
        }
    }

    return new Proxy(obj, {
        set: (target: any, key: any, value: any, receiver: any) => {
            if (typeof value === 'object') {
                value = deepProxy(value, cb);
            }

            const cbType = target[key] == null ? 'create' : 'modify';

            // 排除数组修改length回调
            if (!(Array.isArray(target) && key === 'length')) {
                cb(cbType, { target, key, value });
            }
            return Reflect.set(target, key, value, receiver);
        },
        get: (target: any, key: string, receiver: any) => {
          if (target.hasOwnProperty(key)) {
            parentKey = key;
            return Reflect.get(target, key, receiver);
          }
          if (parentKey) {
            key = parentKey + '.' + key;
            parentKey = undefined;
          }
          // console.log(target, key);
          const fn = cb(target, key);
          if (typeof fn === 'function') {
            return async (...params) => {
              return await fn(...params);
            };
          }

          return Reflect.get(target, parentKey || key, receiver);
        }
    });
}
