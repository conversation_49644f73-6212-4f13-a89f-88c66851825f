import React from 'react';
import { AsyncLoad } from '../AsyncLoad';
import { ILoadModal, IModalHostProps, ModalComponent, MODAL_COMPONENT_NAME } from './Components';

interface IProps {
    close: (name: string, bRefresh?: boolean) => void;
    host?: any;
    documentCore?: any;
    property?: any;
}

export class LoadModal implements ILoadModal {
    private _host: IModalHostProps;
    private _component: ModalComponent;
    private componentCache: { [key: string]: JSX.Element } = {}; // 新增缓存

    constructor(host: IModalHostProps) {
        this._host = host;
        this._component = new ModalComponent(this._host);
    }

    public render(): any {
        const components = this._component.components;
        const keys = Object.keys(components);
        const host: IModalHostProps = this._host;
        const obj = {
            close: host.close,
            documentCore: host.documentCore,
            host: host.host,
            refresh: host.refresh
        };

        return keys.map((key) => {
            const item = components[key];
            key = MODAL_COMPONENT_NAME + key;

            // 判断缓存中是否已存在
            if (!this.componentCache[key] && host[key] !== undefined) {
                const props = { ...obj, ...item };
                delete props.component;

                // 缓存生成的组件
                this.componentCache[key] = this.renderComponent(key, props, item.component);
            }

            return this.componentCache[key] || null;
        });
    }

    private renderComponent(name: string, props: IProps, component: () => Promise<any>): any {
        if (this._host[name] === undefined) {
            return null;
        }
        return (
            <AsyncLoad
                key={name}
                host={this._host}
                name={name}
                component={component}
                props={props}
            />
        );
    }
}
