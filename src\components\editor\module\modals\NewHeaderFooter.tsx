import * as React from 'react';
import '../../style/headerfooter.less';
import { INewControlProperty, IHeaderFooterProperty, HeaderFooterType } from '../../../../common/commonDefines';
import Dialog from '../../ui/Dialog';
import Button from '../../ui/Button';
import Checkbox from '../../ui/CheckboxItem';
import { layer } from '../../../../common/Message';
import { getMMFromPx, getPxForMM } from '../../../../model/core/util';

interface IDialogProps {
    documentCore: any;
    visible: boolean;
    id: string;
    property?: INewControlProperty;
    close: (name: string | number, bRefresh?: boolean) => void;
}

interface IState {
    bRefresh: boolean;
}

export default class NewHeaderFooter extends React.Component<IDialogProps, IState> {
    private headerFooterProperty: IHeaderFooterProperty;
    private visible: any;

    constructor(props: any) {
        super(props);
        this.state = {
            bRefresh: false,
        };
        this.visible = this.props.visible;
        this.headerFooterProperty = {
            isFirstPageDiff: false,
            isProtectHeaderFooter: false,
            isShowHeader: true,
            headerFromTop: '12.5',
            isShowFooter: true,
            footerFromBottom: '12.5',
            isShowHeaderBorder: true,
            isShowFooterBorder: false,
        };
        this.setHeaderFooterProps();
    }

    public render(): any {
        return (
            <Dialog
                visible={this.visible}
                width={350}
                open={this.open}
                title='页眉页脚设置'
                footer={this.renderFooter()}
                id={this.props.id}
            >
                <div className='headerfooter-box'>
                    <div className='editor-line'>
                        <div className='w-050'>
                            <Checkbox
                                name='isFirstPageDiff'
                                disabled={false}
                                value={this.headerFooterProperty.isFirstPageDiff}
                                onChange={this.onChange}
                            >
                                首页不同
                            </Checkbox>
                        </div>
                        <div className='w-050'>
                            <Checkbox
                                name='isProtectHeaderFooter'
                                disabled={false}
                                value={this.headerFooterProperty.isProtectHeaderFooter}
                                onChange={this.onChange}
                            >
                                保护页眉页脚
                            </Checkbox>
                        </div>
                    </div>
                    <div className='editor-line'>
                        <div className='w-050'>
                            <Checkbox
                                name='isShowHeader'
                                disabled={false}
                                value={this.headerFooterProperty.isShowHeader}
                                onChange={this.onChange}
                            >
                                页眉
                            </Checkbox>
                        </div>
                        <div className='w-050 section-distance'>
                            <label>距离边界</label>
                            <input
                                value={this.headerFooterProperty.headerFromTop}
                                onChange={this.numChange.bind(this, 'headerFromTop')}
                                name='headerFromTop'
                                onBlur={this.onBlur}
                                type='number'
                                min={0}
                                step={0.5}
                                disabled={this.headerFooterProperty.isShowHeader === true ? false : true}
                            />
                            {/* <span>毫米</span> */}
                        </div>
                    </div>
                    <div className='editor-line'>
                        <div className='w-050'>
                            <Checkbox
                                name='isShowFooter'
                                disabled={false}
                                value={this.headerFooterProperty.isShowFooter}
                                onChange={this.onChange}
                            >
                                页脚
                            </Checkbox>
                        </div>
                        <div className='w-050 section-distance'>
                            <label>距离边界</label>
                            <input
                                value={this.headerFooterProperty.footerFromBottom}
                                onChange={this.numChange.bind(this, 'footerFromBottom')}
                                name='footerFromBottom'
                                onBlur={this.onBlur}
                                type='number'
                                min={0}
                                step={0.5}
                                disabled={this.headerFooterProperty.isShowFooter === true ? false : true}
                            />
                            {/* <span>毫米</span> */}
                        </div>
                    </div>
                    <div className='editor-line'>
                        <div className='w-050'>
                            <Checkbox
                                name='isShowHeaderBorder'
                                value={this.headerFooterProperty.isShowHeaderBorder}
                                onChange={this.onChange}
                            >
                                页眉边框线
                            </Checkbox>
                        </div>
                        <div className='w-050'>
                            <Checkbox
                                name='isShowFooterBorder'
                                value={this.headerFooterProperty.isShowFooterBorder}
                                onChange={this.onChange}
                                // disabled={true}
                            >
                                页脚边框线
                            </Checkbox>
                        </div>
                    </div>

                </div>
            </Dialog>
        );
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        this.visible = nextProps.visible;
        if (this.visible === true) {
            this.setHeaderFooterProps();
        }
    }

    private renderFooter(): any { // return dom, no () => {}
        return (
            <span>
                <Button onClick={this.close}>取消</Button>
                <Button type='primary' onClick={this.confirm}>确认</Button>
            </span>
        );
    }

    private open = () => {
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private close = (bRefresh?: boolean): void => {
        this.props.close(this.props.id, bRefresh);
        this.visible = false;
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private confirm = (): void => {
        // console.log(this.headerFooterProperty);

        const documentCore = this.props.documentCore;
        const {isFirstPageDiff, isProtectHeaderFooter, isShowHeader,
            isShowFooter, headerFromTop, footerFromBottom} = this.headerFooterProperty;

        const oldIsShowHeader = documentCore.getShowHeader();
        const oldIsShowFooter = documentCore.getShowFooter();

        if (oldIsShowHeader !== isShowHeader) {
            if (isShowHeader === true) {
                // set to true
                documentCore.toggleShowHeaderFooter(HeaderFooterType.Header, isShowHeader);
                this.handleFooterToggle(oldIsShowFooter, isShowFooter);
            } else {
                const messageOptions = {cancelDefault: true, className: ' cancelDefault'};
                layer.confirm('取消"显示页眉"将删除掉现有的页眉，确认要删除吗？', messageOptions)
                .then((resolve) => {
                    // console.log(resolve);
                    documentCore.toggleShowHeaderFooter(HeaderFooterType.Header, isShowHeader);

                    this.handleFooterToggle(oldIsShowFooter, isShowFooter);
                }, (reject) => {
                    // console.log(reject)
                    this.handleFooterToggle(oldIsShowFooter, isShowFooter);
                })
                .catch((error) => {
                    // return ;
                    this.close(true);
                });
            }

        } else {
            this.handleFooterToggle(oldIsShowFooter, isShowFooter);
        }
    }

    private handleFooterToggle(oldIsShowFooter: boolean, isShowFooter: boolean): void {
        if (oldIsShowFooter !== isShowFooter) {
            const documentCore = this.props.documentCore;
            if (isShowFooter === true) {
                documentCore.toggleShowHeaderFooter(HeaderFooterType.Footer, isShowFooter);
                this.confirmNonPromiseMethods();
                /* IFTRUE_WATER */
                // 刷新随机水印
                documentCore.resetCorePosition(true, false);
                /* FITRUE_WATER */
                this.close(true);
            } else {
                const messageOptions = {cancelDefault: true, className: ' cancelDefault'};
                layer.confirm('取消"显示页脚"将删除掉现有的页脚，确认要删除吗？', messageOptions)
                .then((resolve) => {
                    // console.log(resolve);
                    documentCore.toggleShowHeaderFooter(HeaderFooterType.Footer, isShowFooter);
                    this.confirmNonPromiseMethods();
                    this.close(true);
                }, (reject) => {
                    // console.log(reject);
                    this.confirmNonPromiseMethods();
                    this.close(true);
                })
                .catch((error) => {
                    // return ;
                    this.close(true);
                });
            }

        } else {
            this.confirmNonPromiseMethods();
            this.close(true);
        }
    }

    private confirmNonPromiseMethods(): void {
        const documentCore = this.props.documentCore;
        const {isFirstPageDiff, isProtectHeaderFooter, isShowHeader,
            isShowFooter, headerFromTop, footerFromBottom,
            isShowHeaderBorder, isShowFooterBorder} = this.headerFooterProperty;

        const oldIsFirstPageDiff = documentCore.getFirstPageDiff();
        const oldIsProtectHeaderFooter = documentCore.getProtectHeaderFooter();
        const oldHeaderFromTop = getMMFromPx(documentCore.getPageMarginsHeader());
        const oldFooterFromBottom = getMMFromPx(documentCore.getPageMarginsFooter());
        const oldIsShowHeaderBorder = documentCore.getShowHeaderBorder();
        const oldIsShowFooterBorder = documentCore.getShowFooterBorder();
        let needRecalc = false;
        // let needMoveCursorToStart = false;

        if (oldIsFirstPageDiff !== isFirstPageDiff) {
            // logicDocument.setHdrFtrFirstPage(true);
            documentCore.toggleHdrFtrFirstPage(isFirstPageDiff);
            // logicDocument.setHdrFtrFirstPageDifferent();
        }

        if (oldIsProtectHeaderFooter !== isProtectHeaderFooter) {
            // if ( documentCore.isInHeaderFooter() ) {
            //     needMoveCursorToStart = true;
            // }

            documentCore.setProtectHeaderFooter(isProtectHeaderFooter);
            // logicDocument.setController(ControllerType.Document);
            // needRecalc = true;
        }

        if (oldHeaderFromTop !== +headerFromTop) {
            documentCore.setPageMarginsHeader(getPxForMM(+headerFromTop));
            needRecalc = true;
        }

        if (oldFooterFromBottom !== +footerFromBottom) {
            documentCore.setPageMarginsFooter(getPxForMM(+footerFromBottom));
            needRecalc = true;
        }

        if (oldIsShowHeaderBorder !== isShowHeaderBorder) {
            documentCore.setShowHeaderBorder(isShowHeaderBorder);
        }

        if (oldIsShowFooterBorder !== isShowFooterBorder) {
            documentCore.setShowFooterBorder(isShowFooterBorder);
        }

        if (needRecalc === true) {
            documentCore.recalculateAllForce();
        }
        // if (needMoveCursorToStart === true) {
        //     logicDocument.moveCursorToCurrentPage();
        // }

    }

    private onChange = (value: any, name: string): void => {
        // console.log(value, name)
        this.headerFooterProperty[name] = value;
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private onBlur = (): void => {
        //
    }

    private numChange(name: string, e: any): void {
        const target = e.target;
        let value = +target.value;
        // console.log(name, value)
        // let num = parseFloat(value);
        // if (Number.isNaN(num) || value === '') {
        //     num = undefined;
        // } else if (value && (value.slice(-1)[0] === '.' || value.slice(0, 1)[0] === '-')) {
        //     num = value;
        // }

        // console.log(value)
        let limit = getMMFromPx(this.props.documentCore.getPageHeight());
        if (name === 'headerFromTop') {
            limit /= 4;
        } else if (name === 'footerFromBottom') {
            // limit *= 2 / 3;
            limit *= 1 / 6;
        }
        // console.log(limit)
        if (value > limit) {
            value = limit;
        }
        if (value < 0) {
            value = 0;
        }
        if (name === 'footerFromBottom' && value === 0) {
            value = 1;
        }

        // console.log(value.toFixed(1))
        this.headerFooterProperty[name] = value + '';
        // console.log(this.headerFooterProperty)
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private setHeaderFooterProps(): void {
        const documentCore = this.props.documentCore;
        this.headerFooterProperty.isFirstPageDiff = documentCore.getFirstPageDiff();
        this.headerFooterProperty.isProtectHeaderFooter = documentCore.getProtectHeaderFooter();
        this.headerFooterProperty.isShowHeader = documentCore.getShowHeader();
        this.headerFooterProperty.isShowFooter = documentCore.getShowFooter();
        // tslint:disable-next-line: max-line-length
        this.headerFooterProperty.headerFromTop = getMMFromPx(documentCore.getPageMarginsHeader()).toFixed(1).toString();
        // tslint:disable-next-line: max-line-length
        this.headerFooterProperty.footerFromBottom = getMMFromPx(documentCore.getPageMarginsFooter()).toFixed(1).toString();
        this.headerFooterProperty.isShowHeaderBorder = documentCore.getShowHeaderBorder();
        this.headerFooterProperty.isShowFooterBorder = documentCore.getShowFooterBorder();
        // console.log(this.headerFooterProperty)
    }
}
