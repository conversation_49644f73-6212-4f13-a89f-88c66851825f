import { INISCellGridLine } from './../../../common/commonDefines';
import { TableRow } from './TableRow';
import { DocumentContent } from '../DocumentContent';
import { IDrawSelectionsCell } from '../../../model/DocumentCore';
import { TableCellProperty, VerticalMergeType, TextDirectionType } from './TableCellProperty';
import { ITableLimits } from '../DocumentPage';
import { DocumentBorder, DocumentContentType, DocumentShadow } from '../Style';
import { TableLayoutType, TableMeasurement, TableWidthType } from '../TableProperty';
import { IMouseEvent } from '../../../common/MouseEventHandler';
import { ICurrentCursorPos } from '../Paragraph/ParagraphSearchPosXY';
import { TableCellBorderInfo, TableCellInfo } from './TableCellInfo';
import HeaderFooter from '../HeaderFooter';
import { ICursorProperty } from 'src/model/CursorProperty';
import { idCounter } from '../util';
import { CellFormulaCalc, GenericBox, ITableCellFormulaPar, VertAlignType,
    FormulaType,
    ICustomProps,
    ITableCellProperty,
    ResultType,
    DateBoxFormat,
    INISProperty,
    ICustomFormatDateProps,
    CodeValueItem,
    NewControlDefaultSetting,
    ITableCellContentProps,
    NISDateBoxFormat,
    NISTableCellType,
    nisDateFormat,
    NISSelectType,
    CellTimeFormat,
    BloodPressureFormat,
    isValidBPFormat,
    IFixedCellType,
    isStrictDateFormat,
    isLooseDateFormat} from '../../../common/commonDefines';
import { ChangeTableCellBorderTop, ChangeTableCellBorderBottom, ChangeTableCellBorderRight,
    ChangeTableCellBorderLeft,
    ChangeTableCellMargins,
    ChangeTableCellWidth,
    ChangeTableCellVertAlign,
    ChangeTableCellVerticalMerge,
    ChangeTableCellGridSpan,
    ChangeTableCellSlash} from './TableCellChanges';
import Paragraph from '../Paragraph';
import DocumentContentElementBase from '../DocumentContentElementBase';
import History from '../History';
import { TableBase } from '../TableBase';
import ParaPortion from '../Paragraph/ParaPortion';
import { consoleLog, isGlobalTestData } from '../../../common/GlobalTest';
import { HistoryDescriptionType, HistroyItemType } from '../HistoryDescription';
import { ParagraphContentPos } from '../Paragraph/ParagraphContent';

/**
 * 表格单元格
 */
export class TableCell {
    public id: number;
    public index: number;
    public row: TableRow;

    public prev: TableCell;
    public next: TableCell;

    public content: DocumentContent;

    public property: TableCellProperty;

    public borderInfo: TableCellBorderInfo;

    public metrics: TableCellInfo; // 单元格的相关位置信息

    public pagesCount: number;
    public bValidData: boolean; // 用于记录当前单元格内容是否符合校验规则

    public temp: {
        y: number,
        curPage: number,

        xStart: number,
        xEnd: number,
        yStart: number,
        yEnd: number,
        xCellStart: number,
        xCellEnd: number,
        yCellStart: number,
        yCellEnd: number,
    };

    public selectionBounds: IDrawSelectionsCell[];
    public oldSelectionBounds: IDrawSelectionsCell[];

    private bUpdateTimeOnblur: boolean;
    private fixedType: IFixedCellType; // 临时属性，渲染后可以使用

    constructor( row: TableRow, colWidth?: number) {
        this.id = idCounter.getNewId();
        this.index = 0;
        this.row = row;

        this.prev = null;
        this.next = null;

        this.content = new DocumentContent(this, ( null != this.row ?
                                                    this.row.table.logicDocument : undefined ), 0, 0, 0, 0, false);
        this.content.setStartPage( ( row ? this.row.table.pageNum : 0) );

        this.property = new TableCellProperty();
        // this.property.init();

        if ( NURSING_FEATURE && row.table.isNISTable() ) {
            this.property.initNISProperty(row.table.logicDocument);
            // this.property.initCellContentProps();
        }

        if ( undefined !== colWidth ) {
            this.property.cellWidth = new TableMeasurement(TableWidthType.Mm, colWidth);
        }

        this.borderInfo = new TableCellBorderInfo();

        this.metrics = new TableCellInfo();
        this.pagesCount = 0;
        this.temp = {
            y: 0,
            curPage: 0,

            xStart: 0,
            xEnd: 0,
            yStart: 0,
            yEnd: 0,
            xCellStart: 0,
            xCellEnd: 0,
            yCellStart: 0,
            yCellEnd: 0,
        };

        this.selectionBounds = [];
        this.oldSelectionBounds = [];
    }

    public setMetrics(startGridCol: number, xGridStart: number, xGridEnd: number, xCellStart: number,
                      xCellEnd: number, xContentStart: number, xContentEnd: number): void {
        this.metrics.startGridCol = startGridCol;
        this.metrics.xGridStart = xGridStart;
        this.metrics.xGridEnd = xGridEnd;
        this.metrics.xCellStart = xCellStart;
        this.metrics.xCellEnd = xCellEnd;
        this.metrics.xContentStart = xContentStart;
        this.metrics.xContentEnd = xContentEnd;
    }

    public setDefaultContent(bDirty: boolean, props?: ITableCellContentProps): void {
        if (this.isTimeCell()) {
            this.setDefaultTimeCellContent(false);
        } else if ( this.isDateCell() ) {
            this.resetNISDateCellContent();
        } else if ( this.isBPCell() ) {
            this.setDefaultBPCellContent();
        }

        this.content.setCellContentProps(props, bDirty);
    }

    public isCell(): boolean {
        return true;
    }

    public getRow(): TableRow {
        return this.row;
    }

    public getIndex(): number {
        return this.index;
    }

    public setFixedProps(type: IFixedCellType): void {
        this.fixedType = type;
    }

    public getFixedProps(): IFixedCellType {
        return this.fixedType;
    }

    public getId(): number {
        return this.id;
    }

    public getVMerge(): VerticalMergeType {
        return this.property.verticalMerge;
    }

    public setVMerge(verticalMerge: VerticalMergeType): void {
        if ( verticalMerge === this.property.verticalMerge ) {
            return;
        }

        const history = this.row.table.getHistory();
        if ( history ) {
            history.addChange(new ChangeTableCellVerticalMerge(this, this.property.verticalMerge, verticalMerge));
        }
        this.property.verticalMerge = verticalMerge;
    }

    public setTextDirection(textDirection: TextDirectionType): void {
        if ( textDirection === this.property.textDirection ) {
            return;
        }

        // gHistory.addChange
        this.property.textDirection = textDirection;
    }

    /**
     * 获取单元格的位置信息
     */
    public getPosition(): { x: number, y: number, width: number, height: number}[] {
        const position = [];
        const table = this.row.table;
        const curCellWidth = this.metrics.xCellEnd - this.metrics.xCellStart;
        const vMergeCount = table.getVMergeCount(this.index, this.row.index);
        const upCount = this.row.index + vMergeCount;
        let curCellHeight = 0;
        let rowStartIndex = this.row.index;
        let pageIndex = table.rowsInfo[rowStartIndex].startPage;
        for (let rowIndex = this.row.index; rowIndex < upCount; rowIndex++) {
            const rowInfo = table.rowsInfo[rowIndex];
            if (vMergeCount === 1 && rowInfo.height.length > 1) {
                rowInfo.height.forEach((height) => {
                    position.push({
                        x: table.pages[pageIndex].x + this.metrics.xCellStart,
                        y: table.rowsInfo[rowStartIndex].y[pageIndex++],
                        width: curCellWidth,
                        height,
                    });
                });

                continue;
            }
            if (rowInfo.startPage !== pageIndex) {
                position.push({
                    x: table.pages[pageIndex].x + this.metrics.xCellStart,
                    y: table.rowsInfo[rowStartIndex].y[pageIndex],
                    width: curCellWidth,
                    height: curCellHeight
                });
                // reset
                pageIndex = rowInfo.startPage;
                rowStartIndex = rowIndex;
                curCellHeight = 0;
            }
            curCellHeight += rowInfo.height[pageIndex];
        }
        if (curCellHeight !== 0) {
            position.push({
                x: table.pages[pageIndex].x + this.metrics.xCellStart,
                y: table.rowsInfo[rowStartIndex].y[pageIndex],
                width: curCellWidth,
                height: curCellHeight
            });
        }
        return position;
    }

    public setParentPos(pos: ParagraphContentPos): void {
        pos.unShift(this.index);
        this.row.setParentPos(pos);
    }

    /**
     * 获取单元格边框
     */
    public getBorders(): { top: DocumentBorder, right: DocumentBorder,
                            bottom: DocumentBorder, left: DocumentBorder} {
        return {
            top: this.getBorder(0),
            right: this.getBorder(1),
            bottom: this.getBorder(2),
            left: this.getBorder(3),
        };
    }

    /**
     * 获取单元格边框中的一个
     * @param type ：0 -- top；1 -- right；2 -- bottom； 3 -- left
     */
    public getBorder(type: number): DocumentBorder {
        const tableBorders = this.row.table.getTableBorders();
        const borders = this.property.borders;
        let border = null;

        switch (type) {
            case 0: {
                if ( null != borders.top ) {
                    border = borders.top;
                } else {
                    if ( 0 !== this.row.index ) {
                        border = tableBorders.insideH;
                    } else {
                        border = tableBorders.top;
                    }
                }
                break;
            }

            case 1: {
                if ( null != borders.right ) {
                    border = borders.right;
                } else {
                    if ( this.row.content.length - 1 !== this.index ) {
                        border = tableBorders.insideV;
                    } else {
                        border = tableBorders.right;
                    }
                }
                break;
            }

            case 2: {
                if ( null != borders.bottom ) {
                    border = borders.bottom;
                } else {
                    if ( this.row.table.content.length - 1 !== this.row.index ) {
                        border = tableBorders.insideH;
                    } else {
                        border = tableBorders.bottom;
                    }
                }
                break;
            }

            case 3: {
                if ( null != borders.left ) {
                    border = borders.left;
                } else {
                    if ( 0 !== this.index ) {
                        border = tableBorders.insideV;
                    } else {
                        border = tableBorders.left;
                    }
                }
                break;
            }
            default:
                break;
        }

        return border;
    }

    public checkAllNullBorder(): boolean {
        const borders = this.property.borders;
        return !(borders.top || borders.bottom || borders.right || borders.left);
    }

    public isBPAndTimeValid(): boolean {
        if (!this.isBPCell() && !this.isTimeCell()) {
            return true;
        }
        const text = this.getCellContentText();
        if ((this.isBPCell() && text === '---/---') ||
            (this.isTimeCell() && text === '--:--')) {
            this.bValidData = true;
            return true;
        }
        this.bValidData = text.indexOf('--') === -1;
        return this.bValidData;
    }

    public getNumberboxWarn(): number {
        const nisProperty = this.property.nisProperty;
        if (!nisProperty) {
            return ResultType.UnEdited;
        }

        if (nisProperty.minValue == null && nisProperty.maxValue == null) {
            return ResultType.UnEdited;
        }

        const minWarn = nisProperty.minWarn;
        const maxWarn = nisProperty.maxWarn;
        if (minWarn == null && maxWarn == null) {
            return ResultType.UnEdited;
        }

        let text = this.content.getSelectText(true);
        if (!text) {
            return ResultType.UnEdited;
        }

        const unit = nisProperty.unit;
        if (unit) {
            text = text.replace(unit, '');
        }

        const num = +text;
        if (isNaN(num)) {
            return ResultType.Failure;
        }

        if (minWarn != null) {
            if (num <= minWarn) {
                return ResultType.Failure;
            }
        }

        if (maxWarn != null) {
            if (num >= maxWarn) {
                return ResultType.Failure;
            }
        }

        return ResultType.Success;
    }

    // public validNumBoxContent(): number {
    //     const nisProperty = this.property.nisProperty;
    //     if (nisProperty.minValue == null && nisProperty.maxValue == null) {
    //         return ResultType.UnEdited;
    //     }

    //     const text = this.content.getSelectText(true);
    //     if (!text) {
    //         return ResultType.UnEdited;
    //     }

    //     const num = +text;
    //     if (isNaN(num)) {
    //         return ResultType.Failure;
    //     }

    //     const maxValue = nisProperty.maxValue;
    //     if (maxValue != null) {
    //         if (num > maxValue) {
    //             this.setCellText(maxValue + '');
    //             return ResultType.Success;
    //         }
    //     }

    //     const minValue = nisProperty.minValue;
    //     if (minValue != null) {
    //         if (num < minValue) {
    //             this.setCellText(minValue + '');
    //             return ResultType.Success;
    //         }
    //     }

    //     return ResultType.UnEdited;
    // }

    public getLastVisibleLine(clientY: number): any {
        return this.content.getLastVisibleLine(clientY);
    }

    public getBorderInfo(): TableCellBorderInfo {
        return this.borderInfo;
    }

    public addBottomBorderSize(size: number): void {
        if (size == null) {
            return;
        }
        // const border: DocumentBorder = this.property.borders.bottom;
        // if (border) {
        //     border.size = size;
        // }
        // const bottoms = this.borderInfo.bottom;
        // if (bottoms && bottoms.length > 0) {
        //     const bottom = bottoms[0];
        //     bottom.size = size;
        //     this.property.borders.bottom = bottom;
        // }

        // const border2: DocumentBorder = this.property.borders.top;
        // if (border2) {
        //     border2.size = size;
        // }
        
        // this.row.table.setTableBorders(borders);
    }

    public setBottomAndTopBorderSize(size: number): void {
        if (size == null) {
            return;
        }
        const border: DocumentBorder = this.property.borders.bottom;
        if (border) {
            border.size = size;
        }
        const bottoms = this.borderInfo.bottom;
        if (bottoms && bottoms.length > 0) {
            const bottom = bottoms[0];
            bottom.size = size;
            this.property.borders.bottom = bottom;
        }

        const border2: DocumentBorder = this.property.borders.top;
        if (border2) {
            border2.size = size;
        }

        const tops = this.borderInfo.top;
        if (tops && tops.length > 0) {
            const top = tops[0];
            top.size = size;
            this.property.borders.top = top;
        }
    }

    public setBorderInfo(border: TableCellBorderInfo): boolean {
        const borderInfo: TableCellBorderInfo = this.borderInfo;
        borderInfo.top = border.top;
        borderInfo.bottom = border.bottom;
        borderInfo.left = border.left;
        borderInfo.right = border.right;
        borderInfo.bottomBeforeCount = border.bottomBeforeCount;
        borderInfo.bottomAfterCount = border.bottomAfterCount;
        borderInfo.maxLeft = border.maxLeft;
        borderInfo.maxRight = border.maxRight;
        return true;
    }

    public setBorder(border: DocumentBorder, type: number): boolean {
        let desBorder = this.property.borders.top;

        switch (type) {
            case 0:
                desBorder = this.property.borders.top;
                break;

            case 1:
                desBorder = this.property.borders.right;
                break;

            case 2:
                desBorder = this.property.borders.bottom;
                break;

            case 3:
                desBorder = this.property.borders.left;
                break;

            default:
                break;
        }

        if ( !border ) {
            if ( border === desBorder ) {
                return false;
            }

            return this.setBorder2(undefined, type);
        } else if ( null === desBorder ) {
            const newBorder = this.getBorder(type)
                                                .copy();

            newBorder.value = null != border.value ? border.value : newBorder.value;
            newBorder.size = null != border.size ? border.size : newBorder.size;
            newBorder.color = null != border.color ? border.color.copy() : newBorder.color.copy();

            return this.setBorder2(newBorder, type);
        } else {
            const newBorder = undefined === desBorder ? new DocumentBorder() : desBorder;

            newBorder.value = null != border.value ? border.value : desBorder.value;
            newBorder.size = null != border.size ? border.size : desBorder.size;
            newBorder.color = null != border.color ? border.color : desBorder.color.copy();
            return this.setBorder2(newBorder, type);
        }
    }

    /**
     * 设置单元格top
     * @param topInfo
     */
    public setBorderInfoTop(topInfo: DocumentBorder[]): void {
        this.borderInfo.top = topInfo;
    }

    public setBorderInfoBottom(bottomInfo: DocumentBorder[], beforeCount: number, afterCount: number): void {
        this.borderInfo.bottom = bottomInfo;
        this.borderInfo.bottomBeforeCount = beforeCount;
        this.borderInfo.bottomAfterCount = afterCount;
    }

    public setBorderInfoLeft(leftInfo: DocumentBorder[], max: number): void {
        this.borderInfo.left = leftInfo;
        this.borderInfo.maxLeft = max;
    }

    public setBorderInfoRight(rightInfo: DocumentBorder[], max: number): void {
        this.borderInfo.right = rightInfo;
        this.borderInfo.maxRight = max;
    }

    /**
     * 获取单元格margins
     */
    public getMargins(): GenericBox<TableMeasurement> {
        if ( this.property.margin ) {
            return this.property.margin;
        } else {
            return this.row.table.getTableCellMargins();
        }
    }

    public setMargins(margin: GenericBox<TableMeasurement>, type?: number): void {
        const oldMargin = ( this.property.margin ? this.property.margin : undefined );
        if ( !margin ) {
            if ( oldMargin !== margin ) {
                const history = this.row.table.getHistory();
                if ( history ) {
                    history.addChange(new ChangeTableCellMargins(this, oldMargin, undefined));
                }
                this.property.margin = undefined;
            }
            return;
        }

        let bNeedChange = false;
        const newMargin = this.property.margin;

        if ( !newMargin ) {
            const tableMargins = this.row.table.getTableCellMargins();
            newMargin.top = tableMargins.top.copy();
            newMargin.right = tableMargins.right.copy();
            newMargin.bottom = tableMargins.bottom.copy();
            newMargin.left = tableMargins.left.copy();
            bNeedChange = true;
        }

        switch (type) {
            case -1: {
                newMargin.top.width = margin.top.width;
                newMargin.top.type = margin.top.type;
                newMargin.right.width = margin.right.width;
                newMargin.right.type = margin.right.type;
                newMargin.bottom.width = margin.bottom.width;
                newMargin.bottom.type = margin.bottom.type;
                newMargin.left.width = margin.left.width;
                newMargin.left.type = margin.left.type;
                bNeedChange = true;
                break;
            }

            case 0: {
                if ( true !== bNeedChange && ( newMargin.top.width !== margin.top.width
                                                || newMargin.top.type !== margin.top.type ) ) {
                    bNeedChange = true;
                }

                newMargin.top.width = margin.top.width;
                newMargin.top.type = margin.top.type;
                break;
            }

            case 1: {
                if ( true !== bNeedChange && ( newMargin.right.width !== margin.right.width
                                                || newMargin.right.type !== margin.right.type ) ) {
                    bNeedChange = true;
                }

                newMargin.right.width = margin.right.width;
                newMargin.right.type = margin.right.type;
                break;
            }

            case 2: {
                if ( true !== bNeedChange && ( newMargin.bottom.width !== margin.bottom.width
                                                || newMargin.bottom.type !== margin.bottom.type ) ) {
                    bNeedChange = true;
                }

                newMargin.bottom.width = margin.bottom.width;
                newMargin.bottom.type = margin.bottom.type;
                break;
            }

            case 3: {
                if ( true !== bNeedChange && ( newMargin.left.width !== margin.left.width
                                                || newMargin.left.type !== margin.left.type ) ) {
                    bNeedChange = true;
                }

                newMargin.left.width = margin.left.width;
                newMargin.left.type = margin.left.type;
                break;
            }
            default:
                break;
        }

        if ( true === bNeedChange ) {
            const history = this.row.table.getHistory();
            if ( history ) {
                history.addChange(new ChangeTableCellMargins(this, oldMargin, newMargin));
            }

            this.property.margin = newMargin;
        }
    }

    /**
     * 获取单元格宽度信息
     */
    public getCellWidth(): TableMeasurement {
        return this.property.cellWidth;
    }

    public setCellWidth(width: TableMeasurement): void {
        const history = this.row.table.getHistory();
        if ( history ) {
            history.addChange(new ChangeTableCellWidth(this, this.property.cellWidth, width));
        }

        this.property.cellWidth = width;
    }

    public getTableCellContentWidth(): number {
        return this.metrics.xContentEnd - this.metrics.xContentStart;
    }

    public getTableCellContentHeight(): number {
        return this.row.getCurrentRowHeight();
    }

    public getTableCellMaxWidth(): number {
        let maxWidth = this.metrics.xContentEnd - this.metrics.xContentStart;
        const row = this.row;
        const table = row.table;

        if (NURSING_FEATURE && table.isNISTable()) {
            return maxWidth;
        }

        if ( table && (!table.isFixedColWidth() || 1 < row.getCellsCount()) ) {
            const columnWidths: number[] = [];
            const rowsInfo = table.getRowsInfo();
            maxWidth = 0;
            for (let index = 0, count = rowsInfo[row.index].length; index < count; index++) {
                const width = rowsInfo[row.index][index].width;
                columnWidths[index] = width;
                maxWidth += (width - 1);
            }
        }

        return maxWidth;
    }

    public getTableCellMaxHeight(): number {
        const row = this.row;
        const table = row.table;
        const {y, yLimit} = this.content.getPageLimits(0);
        let maxHeight = yLimit - y;

        if ( table && table.isFixedRowHeight() ) {
            maxHeight = row.getCurrentRowHeight();
        }

        return maxHeight;
    }

    /**
     * 获取单元格在当前row的合并信息
     */
    public getGridSpan(): number {
        return this.property.gridSpan;
    }

    public setGridSpan(gridSpan: number): void {
        if ( gridSpan === this.property.gridSpan ) {
            return;
        }

        const history = this.row.table.getHistory();
        if ( history ) {
            history.addChange(new ChangeTableCellGridSpan(this, this.property.gridSpan, gridSpan));
        }
        this.property.gridSpan = gridSpan;
    }

    public setIndex(index: number): void {
        if ( index !== this.index ) {
            this.index = index;
        }
    }

    public getGridLine(): INISCellGridLine {
        return this.property?.nisProperty?.gridLine;
    }


    public setGridLine(props: INISCellGridLine): number {
        const nisProperty = this.property.nisProperty;
        if (!nisProperty) {
            return ResultType.UnEdited;
        }

        let bChange = false;
        const gridLine: INISCellGridLine = (nisProperty.gridLine || {}) as any;
        if (props.visible != null && props.visible !== gridLine.visible) {
            gridLine.visible = props.visible;
            bChange = true;
        }

        if (props.height != null && props.height !== gridLine.height) {
            gridLine.height = props.height;
            bChange = true;
        }

        if (props.alignment != null && props.alignment !== gridLine.alignment) {
            gridLine.alignment = props.alignment;
            bChange = true;
        }

        if (props.bPrint != null && props.bPrint !== gridLine.bPrint) {
            gridLine.bPrint = props.bPrint;
            bChange = true;
        }

        return bChange ? ResultType.Success : ResultType.Failure;
    }

    /**
     * 是否为垂直文本
     */
    public isVerticalText(): boolean {
        return false;
    }

    public getShadow(): DocumentShadow {
        return this.property.shadow;
    }

    public isContentOnFirstPage(): boolean {
        return this.content.isContentOnFirstPage();
    }

    public getPageContentStartPos(pageNum: number): ITableLimits {
        return this.row.table.getPageContentStartPos(pageNum + this.content.startPage, this.row.index, this.index);
    }

    public getAbsolutePage(curPage: number): number {
        return this.row.table.getAbsolutePage(curPage);
    }

    public getAbsoluteStartPage(): number {
        return this.row.table.getAbsoluteStartPage();
    }

    /**
     * 当前元素的开始页面
     */
    public getRelativeStartPage(): number {
        return this.row.table.getRelativeStartPage();
    }

    public recalculateCurPosForContent(bUpdateX: boolean = true, bUpdateY: boolean = true): ICurrentCursorPos {
        return this.content.recalculateCurPos(bUpdateX, bUpdateY);
    }

    public removeContainedComments(bRemovePortion: boolean = false): void {
        return this.content.removeContainedComments(bRemovePortion);
    }

    public getCurPosXY(): ICursorProperty {
        return this.content.getCursorPosXY();
    }

    public setCurPosXY(pointX: number, pointY: number): void {
        this.content.setCurPosXY(pointX, pointY);
    }

    public moveCursorToStartPosContent(): void {
        this.content.moveCursorToStartPos();
    }

    public moveCursorToXYForContent(pageIndex: number, pointX: number, pointY: number,
                                    bLine: boolean = false, bDontChangeRealPos: boolean = true): void {
        this.content.moveCursorToXY(pageIndex, pointX, pointY, bLine, bDontChangeRealPos);
    }

    public moveCursorUpToLastRow(pointX: number, pointY: number, bShiftLey: boolean = false): void {
        this.content.moveCursorUpToLastRow(pointX, pointY, bShiftLey);
    }

    public moveCursorDownToFirstRow(pointX: number, pointY: number, bShiftLey: boolean = false): void {
        this.content.moveCursorDownToFirstRow(pointX, pointY, bShiftLey);
    }

    public moveCursorToStartPos(): void {
        this.content.moveCursorToEndPos();
    }

    public moveCursorToEndPos(bAddToSelect: boolean = false, bSelectFromStart: boolean = false): void {
        this.content.moveCursorToEndPos(bAddToSelect, bSelectFromStart);
    }

    public recalculateMinmaxContentWidth(bRotated: boolean): {Min: number; Max: number} {
        if ( undefined === bRotated ) {
            bRotated = false;
        }

        if ( true === this.isVerticalText() ) {
            bRotated = ( true === bRotated ) ? false : true;
        }

        const result = this.content.recalculateMinmaxContentWidth(bRotated);

        return result;
    }

    /**
     * 是否在table边框
     * @param pointX
     * @param pointY
     * @param curPage
     */
    public isTableBorder(pointX: number, pointY: number, curPage: number, options?: any): TableBase {
        return this.content.isTableBorder(pointX, pointY, curPage, options);
    }

    /**
     * 设置选择开始位置
     * @param pointX
     * @param pointY
     * @param curPage
     * @param mouseEvent
     */
    public setSelectionStart(pointX: number, pointY: number, curPage: number, mouseEvent: IMouseEvent): void {
        this.content.setSelectionStart(pointX, pointY, curPage, mouseEvent);
    }

    /**
     * 设置选择结束位置
     * @param pointX
     * @param pointY
     * @param curPage
     * @param mouseEvent
     */
    public setSelectionEnd(pointX: number, pointY: number, curPage: number, mouseEvent: IMouseEvent): void {
        this.content.setSelectionEnd(pointX, pointY, curPage, mouseEvent);
    }

    public getSelectionBounds(): IDrawSelectionsCell[] {
        const result: IDrawSelectionsCell[] = [];
        const table = this.row.table;
        const bInHeaderFooter = this.isHeaderFooter(false);

        for (let index = 0; index < this.content.pages.length; index++) {
            const contentBound = {
                id: this.id + index,
                x: 0,
                y: 0,
                height: 0,
                width: 0,
                table: table.index,
                pageIndex: (bInHeaderFooter ? 0 : this.content.getAbsoluteStartPage() + index),
            };

            const pageIndex = (bInHeaderFooter ? 0 : this.content.startPage + index);
            const tablePage = table.pages[pageIndex];
            contentBound.x = tablePage.x + this.metrics.xCellStart;
            contentBound.y = table.rowsInfo[this.row.index].y[pageIndex];

            contentBound.width = this.metrics.xCellEnd - this.metrics.xCellStart;
            contentBound.height = table.rowsInfo[this.row.index].height[pageIndex];

            result.push(contentBound);
        }
        // const page = table.pages[pageIndex];

        // contentBound.x = page.x + this.metrics.xCellStart;
        // contentBound.y = table.rowsInfo[this.row.index].y[0];

        // contentBound.width = this.metrics.xCellEnd - this.metrics.xCellStart;
        // contentBound.height = table.rowsInfo[this.row.index].height[0];
        // result.push(contentBound);

        return result;
    }

    public getSelectionBounds2(pageIndex: number): IDrawSelectionsCell[] {

        if (null == pageIndex) {
            const bSame = (this.oldSelectionBounds && this.oldSelectionBounds.length &&
                this.content.pages.length === this.oldSelectionBounds.length &&
                (!this.selectionBounds || (this.selectionBounds &&
              (0 === this.selectionBounds.length || this.oldSelectionBounds.length === this.selectionBounds.length))));
            if (bSame && this.oldSelectionBounds && this.oldSelectionBounds.length) {
                return this.oldSelectionBounds.slice();
            } else if (this.selectionBounds && 0 < this.selectionBounds.length) {
                return this.selectionBounds.slice();
            }
        }

        const result: IDrawSelectionsCell[] = [];
        this.selectionBounds.forEach((value) => {
            if (pageIndex === value.pageIndex) {
                result.push(value);
                return;
            }
        });

        if (0 === result.length) {
            const table = this.row.table;
            if (!table.content[this.row.index]) {
                return result;
            }

            const bInHeaderFooter = this.isHeaderFooter(false);
            const pages = this.content.pages;
            const rowsInfo = table.rowsInfo;
            let vMergeCount = table.getVMergeCount(this.index, this.row.index);

            for (let index = 0, length = pages.length; index < length; index++) {
                const contentBound = {
                    id: this.id + index,
                    x: 0,
                    y: 0,
                    height: 0,
                    width: 0,
                    table: table.index,
                    pageIndex: (bInHeaderFooter ? 0 : this.content.getAbsoluteStartPage() + index),
                };

                const page = pages[index];
                if ( pageIndex !== contentBound.pageIndex || !page ) {
                    continue;
                }

                const tablePage = table.pages[pageIndex];
                contentBound.x = tablePage.x + this.metrics.xCellStart;
                contentBound.y = rowsInfo[this.row.index].y[pageIndex];

                contentBound.width = this.metrics.xCellEnd - this.metrics.xCellStart;
                contentBound.height = rowsInfo[this.row.index].height[pageIndex];

                if (1 < vMergeCount) {
                    let rowIndex = this.row.index;
                    while (--vMergeCount) {
                        const rowInfo = rowsInfo[++rowIndex];
                        if (rowInfo && null != rowInfo.height[pageIndex]) {
                            contentBound.height += rowInfo.height[pageIndex];
                        } else {
                            break;
                        }
                    }
                }

                result.push(contentBound);
            }
        }

        if (0 === result.length && this.oldSelectionBounds.length) {
            result.concat(this.oldSelectionBounds.slice(0, this.content.pages.length));
        }
        return result;

        // const table = this.row.table;
        // const bInHeaderFooter = this.isHeaderFooter(false);
        // const vMergeCount = table.getVMergeCount(this.index, this.row.index);
        // const startPage = table.getAbsoluteStartPage();
        // const bSelectedCells = (null == pageIndex);

        // let row = this.row;
        // // for (let index = 0; index < vMergeCount; index++) {
        //     // const row = table.content[index];
        // const cell = row ? row.getCellByColIndex(this.metrics.startGridCol) : null;
        // if ( cell ) {
        //     const pages = cell.content.pages;
        //     for (let curPage = 0; curPage < pages.length; curPage++) {
        //         const contentBound = {
        //             id: cell.id + curPage,
        //             x: 0,
        //             y: 0,
        //             height: 0,
        //             width: 0,
        //             table: table.index,
        //             pageIndex: (bInHeaderFooter ? 0 : cell.content.getAbsoluteStartPage() + curPage),
        //         };

        //         pageIndex = bSelectedCells ? contentBound.pageIndex : pageIndex;
        //         // const pageIndex = (bInHeaderFooter ? 0 : cell.content.startPage + index2);
        //         const page = pages[curPage];
        //         if ( pageIndex !== contentBound.pageIndex || !page ) {
        //             continue;
        //         }

        //         // const tablePage = table.pages[pageIndex - startPage];
        //         // const rowInfo = table.rowsInfo[cell.row.index];

        //         // if (!rowInfo || null == rowInfo.y[pageIndex - startPage]
        //         //     || null == rowInfo.height[pageIndex - startPage]) {
        //         //     continue;
        //         // }

        //         // contentBound.x = tablePage.x + cell.metrics.xCellStart;
        //         // contentBound.y = rowInfo.y[pageIndex - startPage];

        //         // contentBound.width = cell.metrics.xCellEnd - cell.metrics.xCellStart;
        //         // contentBound.height = rowInfo.height[pageIndex - startPage];

        //         const cellMargins = cell.getMargins();
        //         const bounds = page.bounds;
        //         contentBound.x = bounds.left - cellMargins.left.width;
        //         contentBound.y = bounds.top - cellMargins.top.width;

        //         contentBound.width = bounds.right - bounds.left + cellMargins.left.width + cellMargins.right.width;
        //         contentBound.height = bounds.bottom - bounds.top + cellMargins.top.width + cellMargins.bottom.width;

        //         // tslint:disable-next-line: prefer-for-of
        //         for (let index2 = 0; index2 < result.length; index2++) {
        //             const element = result[index2];
        //             if ( element.pageIndex === pageIndex ) {
        //                 element.height += contentBound.height;
        //                 break;
        //             }
        //         }

        //         result.push(contentBound);

        //         if (!bSelectedCells) {
        //             break;
        //         }
        //     }

        //     if ( pages && 0 === pages.length ) {
        //         // tslint:disable-next-line: prefer-for-of
        //         for (let index2 = 0; index2 < result.length; index2++) {
        //             const element = result[index2];
        //             const rowInfo = table.rowsInfo[row.index];

        //             if ( element.pageIndex === pageIndex ) {
        //                 element.height +=
        //                     (rowInfo.height[pageIndex - startPage] ? rowInfo.height[pageIndex - startPage] : 0);
        //                 break;
        //             }
        //         }
        //     }
        // }
        // row = row ? row.next : null;
        // // }

        // return result;
    }

    /**
     * 单元格之间内容合并
     * @param content
     */
    public contentMerge(content: DocumentContent): void {
        const elements = content.content;
        if ( elements && 1 === elements.length && DocumentContentType.Paragraph === elements[0].getType()
            && elements[0].isEmpty() ) {
            return ;
        }

        const newControls = content.getAllNewControls();
        this.content.addContentElements(elements);

        const history = this.row.table.getHistory();
        newControls?.forEach((newControl) => {
            newControl.setContentParent(this.content, history);
        });
    }

    public copyProperty(otherProperty: TableCellProperty): void {
        this.setGridSpan(otherProperty.gridSpan);
        this.setVMerge(otherProperty.verticalMerge);

        if ( !otherProperty.borders.top ) {
            this.setBorder(undefined, 0);
        } else {
            const newBorderTop = otherProperty.borders.top.copy();
            this.setBorder(newBorderTop, 0);
        }

        if ( !otherProperty.borders.right ) {
            this.setBorder(undefined, 1);
        } else {
            const newBorderRight = otherProperty.borders.right.copy();
            this.setBorder(newBorderRight, 1);
        }

        if ( !otherProperty.borders.bottom ) {
            this.setBorder(undefined, 2);
        } else {
            const newBorderBottom = otherProperty.borders.bottom.copy();
            this.setBorder(newBorderBottom, 2);
        }

        if ( !otherProperty.borders.left ) {
            this.setBorder(undefined, 3);
        } else {
            const newBorderLeft = otherProperty.borders.left.copy();
            this.setBorder(newBorderLeft, 3);
        }

        if ( !otherProperty.cellWidth ) {
            this.setCellWidth(undefined);
        } else {
            this.setCellWidth(otherProperty.cellWidth.copy());
        }

        if ( !otherProperty.margin ) {
            this.setMargins(undefined);
        } else {
            const newMargin = !otherProperty.margin ? null : otherProperty.copyMargins();

            this.setMargins(newMargin, -1);
        }

        if ( this.property.getCellProtected() !== otherProperty.getCellProtected() ) {
            this.property.setCellProtected(otherProperty.getCellProtected());
        }
        this.setTextDirection(otherProperty.textDirection);

        this.property.setDiagonalLine2(otherProperty.isDiagonalLineCell());
        this.setCellType(otherProperty.getCellType());
    }

    public isCellProtected(): boolean {
        return this.property.getCellProtected();
    }

    public setCellProtected(bReadOnly: boolean, history?: History): void {
        this.property.setCellProtected(bReadOnly, history);
    }

    public copy(row: TableRow, option?: any): TableCell {
        const cell = new TableCell(row);
        cell.property = this.property.copy();
        cell.content.copy2(this.content, undefined, option);

        cell.borderInfo.top = this.borderInfo.top ? this.borderInfo.top.map((item) => item.copy()) : null;
        cell.borderInfo.left = this.borderInfo.left ? this.borderInfo.left.map((item) => item.copy()) : null;
        cell.borderInfo.right = this.borderInfo.right ? this.borderInfo.right.map((item) => item.copy()) : null;
        cell.borderInfo.bottom = this.borderInfo.bottom ? this.borderInfo.bottom.map((item) => item.copy()) : null;
        cell.borderInfo.bottomAfterCount = this.borderInfo.bottomAfterCount;
        cell.borderInfo.bottomBeforeCount = this.borderInfo.bottomBeforeCount;
        cell.borderInfo.maxLeft = this.borderInfo.maxLeft;
        cell.borderInfo.maxRight = this.borderInfo.maxRight;

        cell.metrics.startGridCol = this.metrics.startGridCol;
        cell.metrics.xCellStart = this.metrics.xCellStart;
        cell.metrics.xCellEnd = this.metrics.xCellEnd;
        cell.metrics.xContentStart = this.metrics.xContentStart;
        cell.metrics.xContentEnd = this.metrics.xContentEnd;
        cell.metrics.xGridStart = this.metrics.xGridStart;
        cell.metrics.xGridEnd = this.metrics.xGridEnd;

        return cell;
    }

    public borderInfoCopy(): TableCellBorderInfo {
        return this.borderInfo.copy();
    }

    public isTableFirstRowOnNewPage(): boolean {
        return this.row.table.isTableFirstRowOnNewPage(this.row.index);
    }

    public isHeaderFooter(bReturnHdrFtr: boolean): boolean | HeaderFooter {
        return this.row.table.parent.isHeaderFooter(bReturnHdrFtr);
    }

    public isDiagonalLineCell(): boolean {
        return this.property.isDiagonalLineCell();
    }

    public setDiagonalLine(bLine?: boolean): boolean {
        // if ( bLine === this.property.bDiagonalLine || null == bLine ) {
        //     return ;
        // }

        return this.property.setDiagonalLine();
    }

    public setCellType(type: number): boolean {
        return this.property.setCellType(type);
    }

    public getNISColID(): string {
        const table = this.row.table;
        if ( NURSING_FEATURE && table && table.isNISTable() ) {
            return table.property.getNISTableColIDs()[this.metrics.startGridCol];
        }

        return ResultType.StringEmpty;
    }

    public isDateTimeCellByCustom(propName: string): boolean {
        if (!this.isDateCell() && !this.isTimeCell()) {
            return false;
        }
        if (!propName) {
            return true;
        }
        const custom = this.property.nisProperty.customProperty;
        if (!custom) {
            return false;
        }
        const obj = custom.find((item) => item.name === propName);
        if (!obj) {
            return false;
        }
        if (obj.value === '1') {
            return true;
        }
        return false;
    }

    public getNISProperty(): INISProperty {
        const props = this.property.nisProperty;
        if (!props) {
            return;
        }

        const obj = {...props};
        if (obj.gridLine) {
            obj.gridLine = {...obj.gridLine};
        }
        obj.colID = this.getNISColID();
        obj.rowID = this.row.getNISRowID();
        return obj;
    }

    public getNISProperty2(): INISProperty {
        return this.property.nisProperty;
    }

    public setCellContentProps(props: ITableCellContentProps): boolean {
        let result = false;
        if ( !props ) {
            return result;
        }

        if (props.vertAlign != null && this.property.vertAlign !== props.vertAlign) {
            this.property.vertAlign = props.vertAlign;
            result = true;
        }

        result = (ResultType.Success === this.property.setNISProperty({
            fontFamily: props.fontFamily,
            fontSize: props.fontSize,
            paraSpacing: props.paraSpacing,
            alignType: props.alignType,
            range: props.range,
        })) || result;
        return (this.content.setCellContentProps(props) || result);
    }

    public getCellContentProps(): ITableCellContentProps {
        const pro = this.property.nisProperty;
        return {
            range: pro.range,
            fontFamily: pro.fontFamily,
            fontSize: pro.fontSize,
            paraSpacing: pro.paraSpacing,
            alignType: pro.alignType,
            vertAlign: this.property.vertAlign,
        };
    }

    public setNISDefaultProps(type: NISTableCellType): boolean {
        let result = false;
        if (this.property != null) {
            result = this.property.setNISDefaultProps(type, this.row.table.logicDocument);
            if (result) {
                this.content.clearContent(true);
            }

            if (this.isTimeCell()) {
                this.setDefaultTimeCellContent();
            } else if (this.isBPCell()) {
                this.setDefaultBPCellContent();
            }
        }

        return result;
    }

    public updateBPCellFormat(): boolean {
        const format = this.property.nisProperty.bloodPressureFormat;
        if (!isValidBPFormat(format)) {
            return false;
        }
        const para = this.content.content[0];
        if (!para || !para.isParagraph() || para.getContent().length !== 4) {
            this.setDefaultBPCellContent();
            return true;
        }
        const contents = para.getContent();
        const portion = contents[1] as ParaPortion;
        if (!portion || portion.isParaEndPortion()) {
            this.setDefaultBPCellContent();
            return true;
        }

        portion.content.splice(0, contents.length);
        portion.addText(format.slice(3, 4));
        this.setNisPropCellText(para as any);
        return true;
    }

    public updateTimeCellContent(text: string, bDelete: boolean): number {
        const para = this.content.content[0] as Paragraph;
        const contents = para.content;
        const pos = para.curPos.contentPos;
        const portion = contents[pos];
        if (!portion) {
            if (isGlobalTestData()) {
                consoleLog('段落异常');
                consoleLog(para);
            }
            return;
        }
        this.bUpdateTimeOnblur = true;
        if (bDelete) {
            return this.deleteTimeCellPortionContent(text, bDelete);
        } else if (/\d+/.test(text)) {
            let bChanged: number = 0;
            let bMove: boolean = false;
            const porContents = portion.content;
            if (portion.isSelectionUse()) {
                porContents[0].content = '0';
                porContents[1].content = text;
                bChanged = 1;
                para.curPos.line = portion.getLineByPos(1);
                portion.portionContentPos = 2;
            } else {
                if (porContents[0].content !== '0') {
                    return ResultType.UnEdited;
                }
                porContents[0].content = porContents[1].content;
                porContents[1].content = text;
                bMove = true;
                bChanged = 1;
            }
            // const porContents = portion.content;
            // const len = porContents.length - 1;
            // let porLen = len - 1;
            // let bChanged: number = 0;
            // // 超过两位数进行删除
            // if (porLen > 0) {
            //     portion.content.splice(2, porLen);
            //     if (portion.portionContentPos >= len) {
            //         portion.portionContentPos = len;
            //     }

            //     porLen = 0;
            //     bChanged = 1;

            //     // if (document) {
            //     //     document.recalculate();
            //     //     document.updateCursorXY();
            //     // }
            //     // return ResultType.Success;
            // }

            // 移位
            const curPos = pos + 2;
            if (bMove && curPos < contents.length - 1) {
                para.selectOnePortion(curPos);
                // const document = this.row.table.logicDocument;
                bChanged = 2;
                // if (document) {
                //     document.updateCursorXY();
                //     return ResultType.Success;
                // }
            }

            if (bChanged > 0) {
                const document = this.row.table.logicDocument;
                if (bChanged === 1) {
                    document.removeSelection();
                }
                this.setNisPropCellText(para);
                document.updateCursorXY();
                return ResultType.Success;
            }
        } else {
            if (isGlobalTestData()) {
                consoleLog(text, bDelete);
            }
        }

        return ResultType.Failure;
    }

    public setDefaultBPCellContent(bRefresh: boolean = true, bFormat?: boolean): void {
        const props = this.property.nisProperty;
        let format = props.bloodPressureFormat;
        if (!isValidBPFormat(format)) {
            format = props.bloodPressureFormat = BloodPressureFormat.ASlashB;
        }
        props.cellText = undefined;
        const contents = this.content.content;
        contents.splice(1, contents.length);
        const para = contents[0] as Paragraph;
        let textProp: any;
        if (bFormat) {
            textProp = para.content[0].textProperty;
        }
        if (bRefresh !== true) {
            this.moveCursorLeft(para);
        }
        para.content.splice(0, para.content.length - 1);
        // 时portion
        let index = 0;
        const hPortion = new ParaPortion(para);
        hPortion.addText('---');
        para.addToContent(index++, hPortion);

        // 分隔符
        const hAfterPortion = new ParaPortion(para);
        hAfterPortion.addText(format.slice(3, 4));
        para.addToContent(index++, hAfterPortion);

        // 分portion
        const mPortion = new ParaPortion(para);
        mPortion.addText('---');
        para.addToContent(index++, mPortion);

        this.content.setCellContentProps(this.getCellContentProps());
        if (textProp) {
            hPortion.textProperty = textProp.copy();
            // hAfterPortion.textProperty = textProp.copy();
            mPortion.textProperty = textProp.copy();
        }
        if (bRefresh) {
            // para.selectOnePortion(0);
            const document = para.getDocument();
            if (document) {
                document.recalculate();
            }
        }
    }

    public validBPCellContent(bRefresh?: boolean, bFormat?: boolean): number {
        const para = this.content.content[0] as Paragraph;
        const contents = para.content;
        // this.bUpdateTimeOnblur = false;
        const len = contents.length;
        let bChanged = false;
        if (len !== 4) {
            if (len > 4) {
                contents.splice(3, len - 4);
                bChanged = true;
            } else {
                this.setDefaultBPCellContent(bRefresh, bFormat);
                return ResultType.Success;
            }
        }
        const portion = contents[0];
        let text: string = portion.getSelectText(true);
        // let num: number = +text;
        // 小时进行判断初始化
        if (text === '---') {
            // TODo
        } else if (!text || Number.isNaN(text)) {
            portion.content.length = 0;
            portion.addText('---');
            bChanged = true;
        } else if (portion.content.length !== 3) {
            // text = text.padStart(2, ' ');
            portion.content.length = 0;
            portion.addText(text);
            bChanged = true;
        }

        // 分
        const portion2 = contents[2];
        text = portion2.getSelectText(true);
        if (text === '---') {
            // TODo
        } else if (!text || Number.isNaN(text)) {
            portion2.content.length = 0;
            portion2.addText('---');
            bChanged = true;
        } else if (portion2.content.length !== 3) {
            // text = text.padEnd(2, ' ');
            portion2.content.length = 0;
            portion2.addText(text);
            bChanged = true;
        }

        if (bChanged) {
            if (bRefresh) {
                para.parent.recalculate();
            }

            this.setNisPropCellText(para);
            return ResultType.Success;
        }
        return ResultType.UnEdited;
    }

    public deleteBPCellPortionContent(text: string, bDelete: boolean): number {
        const para = this.content.content[0] as Paragraph;
        const contents = para.content;
        const pos = para.curPos.contentPos;
        const portion = contents[pos];

        const index = pos % 2;
        const texts = portion.content;
        const doc = this.row.table.logicDocument;
        if (index === 0 && texts.length === 3) {
            texts[0].content = texts[1].content = texts[2].content = '-';
            let selectIndex = 0;
            // delete
            if (text === '46') {
                selectIndex = 2;
            }

            doc.removeSelection();
            para.selectOnePortion(selectIndex);
            if (doc) {
                doc.updateCursorXY();
            }
            this.setNisPropCellText(para);
            return ResultType.Success;
        } else if (this.content.isSelectionUse() && index === 0) {
            // 选中时，可以删除内容
            if (doc) {
                doc.removeSelection();
            }
            let selectIndex = 0;
            // delete
            if (text === '46') {
                selectIndex = 2;
            }
            portion.removeFromContent(0, portion.getTextLength());
            portion.addText('---');
            para.selectOnePortion(selectIndex);

            if (doc) {
                doc.recalculate();
                doc.updateCursorXY();
            }
            this.setNisPropCellText(para);
            return ResultType.Success;
        }

        return ResultType.UnEdited;
    }

    public updateBPCellContent(text: string, bDelete: boolean): number {
        const para = this.content.content[0] as Paragraph;
        const contents = para.content;
        if (contents.length !== 4) {
            this.validBPCellContent();
        }
        const pos = para.curPos.contentPos;
        const portion = contents[pos];
        if (!portion) {
            if (isGlobalTestData()) {
                consoleLog('段落异常');
                consoleLog(para);
            }
            return;
        }

        if (bDelete) {
            return this.deleteBPCellPortionContent(text, bDelete);
        } else if (/\d+/.test(text)) {
            let bChanged: number = 0;
            let bMove: boolean = false;
            const porContents = portion.content;
            // if (porContents.length !== 3) {
            //     return ResultType.Failure;
            // }
            const bSelectsUser = portion.isSelectionUse();
            if (bSelectsUser) {
                portion.removeFromContent(0, portion.content.length);
                portion.addText(text);
                bChanged = 1;
                portion.portionContentPos = 1;
                para.curPos.line = portion.getLineByPos(0);
            } else if (porContents.length ===  1) {
                bChanged = 1;
                portion.addText(text);
                portion.portionContentPos = 2;
                para.curPos.line = portion.getLineByPos(1);
            } else {
                if (porContents.length !== 2) {
                    return ResultType.UnEdited;
                }
                portion.addText(text);
                portion.portionContentPos = 3;
                para.curPos.line = portion.getLineByPos(2);
                if (pos !== 2) {
                    bMove = true;
                }

                bChanged = 1;
            }

            // 移位
            const curPos = pos + 2;
            if (bMove && curPos < contents.length - 1) {
                para.selectOnePortion(curPos);
                // const document = this.row.table.logicDocument;
                bChanged = 2;
                // if (document) {
                //     document.updateCursorXY();
                //     return ResultType.Success;
                // }
            }

            if (bChanged > 0) {
                const document = this.row.table.logicDocument;
                if (bSelectsUser) {
                    document.removeSelection();
                }
                document.recalculate();
                document.updateCursorXY();
                this.setNisPropCellText(para);
                return ResultType.Success;
            }
        } else {
            if (isGlobalTestData()) {
                consoleLog(text, bDelete);
            }
        }

        return ResultType.Failure;
    }

    public validTimeCellContent(bRefresh?: boolean, bFormat?: boolean): number {
        if (this.bUpdateTimeOnblur !== true) {
            return ResultType.UnEdited;
        }

        const para = this.content.content[0] as Paragraph;
        const contents = para.content;
        this.bUpdateTimeOnblur = false;
        const len = contents.length;
        let bChanged = false;
        if (len !== 4) {
            if (len > 4) {
                contents.splice(3, len - 4);
                bChanged = true;
            } else {
                this.setDefaultTimeCellContent(bRefresh, bFormat);
                return ResultType.Success;
            }
        }
        const portion = contents[0];
        let text: string = portion.getSelectText(true);
        let num: number = +text;

        // 小时进行判断初始化
        if (text === '--') {
            // TODo
        } else if (!text || Number.isNaN(num)) {
            portion.content.length = 0;
            portion.addText('--');
            bChanged = true;
        } else {
            let res: string = '';
            if (num > 23) {
                res += 23;
            } else if (num < 10 && ('0' + num !== text)) {
                res = '0' + num;
            }
            if (res) {
                portion.content.length = 0;
                portion.addText(res);
                bChanged = true;
            }
        }
        // 分
        const portion2 = contents[2];
        text = portion2.getSelectText(true);
        num = +text;
        if (text === '--') {
            // TODo
        } else if (!text || Number.isNaN(num)) {
            portion2.content.length = 0;
            portion2.addText('--');
            bChanged = true;
        } else {
            let res: string = '';
            if (num > 59) {
                res += 59;
            } else if (num < 10 && ('0' + num !== text)) {
                res = '0' + num;
            }
            if (res) {
                portion2.content.length = 0;
                portion2.addText(res);
                bChanged = true;
            }
        }
        if (bChanged) {
            if (bRefresh !== false) {
                const elem = para.canRecalcCurCell(undefined, undefined, (item) => {
                    if (item === para) {
                        return para.measureParaHeight();
                    }
                }, true);
                if (elem) {
                    elem.recalculatePage(0);
                } else {
                    this.content.recalculate();
                }
                this.setNisPropCellText(para);
            }
            return ResultType.Success;
        }
        return ResultType.UnEdited;
    }

    public deleteTimeCellPortionContent(text: string, bDelete: boolean): number {
        const para = this.content.content[0] as Paragraph;
        const contents = para.content;
        const pos = para.curPos.contentPos;
        const portion = contents[pos];

        const index = pos % 2;
        if (index === 0) {
            const texts = portion.content;
            texts[0].content = texts[1].content = '-';
            let selectIndex = 0;
            // delete
            if (text === '46') {
                selectIndex = 2;
            }

            const doc = this.row.table.logicDocument;
            doc.removeSelection();
            para.selectOnePortion(selectIndex);
            if (doc) {
                doc.updateCursorXY();
            }
            this.setNisPropCellText(para);
            return ResultType.Success;
        }

        // if (index === 1) {
        //     const format = this.property.nisProperty.timeFormat || '';
        //     if (!format) {
        //         return ResultType.UnEdited;
        //     }
        //     const reg = /HH([\s\S]+)MM(([\s\S]+)SS)?/g;
        //     const match = reg.exec(format);
        //     if (!match) {
        //         return ResultType.UnEdited;
        //     }
        //     portion.content = [];
        //     portion.addText(match[1]);
        //     if (text === 'Backspace') {
        //         pos--;
        //     } else {
        //         pos++;
        //     }
        //     const document = this.row.table.logicDocument;
        //     document.removeSelection();
        //     para.selectOnePortion(pos);
        //     document.recalculate();
        //     document.updateCursorXY();
        // } else if (portion.content.length === 0) {
        //     portion.addText('--');
        //     if (text === 'Backspace') {
        //         pos = pos - 2;
        //     } else {
        //         pos = pos + 2;
        //     }
        //     if (pos < 0) {
        //         pos = 0;
        //     }
        //     para.selectOnePortion(pos);
        //     const document = this.row.table.logicDocument;
        //     if (document) {
        //         document.recalculate();
        //         document.updateCursorXY();
        //     }
        // } else {
        //     return ResultType.UnEdited;
        // }

        return ResultType.UnEdited;
    }

    public moveCursor(keyCode: number): boolean {
        const para = this.content.content[0] as Paragraph;
        const contents = para.content;
        const pos = para.curPos.contentPos;
        const portion = contents[pos];
        const bSplit = pos % 2 === 1;
        let index: number;
        // 左箭头往左移
        if (keyCode === 37) {
            if (pos === 0) {
                return false;
            }

            // 光标在分隔符上
            if (bSplit) {
                index = pos - 1;
            } else {
                index = pos - 2;
            }

            if (index < 0) {
                index = 0;
            }
            const doc = this.row.table.logicDocument;
            doc.removeSelection();
            para.selectOnePortion(index);
            if (doc) {
                doc.updateCursorXY();
            }
            return true;
        }

        // 右箭头往右移动
        const len = contents.length - 1;
        if (pos >= len - 1 ) {
            return false;
        }

        if (bSplit) {
            index = pos + 1;
        } else {
            index = pos + 2;
        }

        if (index === len) {
            index = len - 1;
        }

        const document = this.row.table.logicDocument;
        document.removeSelection();
        para.selectOnePortion(index);
        if (document) {
            document.updateCursorXY();
        }
        return true;
    }

    public selectCellContent(): boolean {
        if (!this.isTimeCell() && !this.isBPCell()) {
            return false;
        }
        return this.content.selectCellContent();
    }

    public setCellReadonly(bReadonly: boolean): boolean {
        const props = this.property.nisProperty;
        const para = this.content.content[0] as Paragraph;
        const text = para.getSelectText(true);
        const type = props.type;
        if (bReadonly) {
            let bDelete = false;
            if (type === NISTableCellType.Time) {
                const index = text.indexOf('--');
                if (index > -1) {
                    bDelete = true;
                }
            } else if (type === NISTableCellType.BloodPressure) {
                if (/^---[\s\S]*---$/.test(text)) {
                    bDelete = true;
                } else {
                    const index = text.indexOf('---');
                    if (index === 0) {
                        this.moveCursorLeft(para);
                        para.content.splice(0, para.content.length - 2);
                        return true;
                    } else if (index > 0) {
                        this.moveCursorLeft(para);
                        para.content.splice(1, para.content.length - 2);
                        return true;
                    }
                }
            }
            if (bDelete) {
                this.moveCursorLeft(para);
                para.content.splice(1, para.content.length - 2);
                para.content[0].content.length = 0;
            }
            return bDelete;
        }

        if (text === props.cellText) {
            return false;
        }

        const cellText = props.cellText || '';
        if (type === NISTableCellType.Time) {
            const reg = /^([\d\-]{2})[\s\S]*([\d\-]{2})$/g;
            const match = reg.exec(cellText);
            if (match) {
                return this.setNISCellTimeText(`${match[1]}:${match[2]}`, true);
            }

            if (text.indexOf('--') === -1) {
                this.setDefaultTimeCellContent(false, true);
                return true;
            }
            return false;
        } else if (type === NISTableCellType.BloodPressure) {
            if (cellText && text) {
                const reg = /^([0-9\-\s]{3})[\s\S]*([0-9\-\s]{3})$/;
                const match = reg.exec(cellText);
                if (match) {
                    return this.setNISCellBPText(`${match[1]}:${match[2]}`, true) === ResultType.Success;
                }
            }

            if (text.indexOf('---') === -1) {
                this.setDefaultBPCellContent(false, true);
                return true;
            }
            return false;
        }

    }

    public moveCursorLeft(para: Paragraph): void {
        para.curPos.contentPos = para.selection.startPos = para.selection.endPos = 0;
        para.content[0].portionContentPos = 0;
    }

    /**
     * 根据页码数获取当前单元格内容是否被遮住
     * @param pageIndex 页码数
     * @returns true/false
     */
    public isOverCellBorder(pageIndex: number): boolean {
        const bounds = this.oldSelectionBounds;
        if (!bounds || bounds.length === 0) {
            return false;
        }
        // const cellContentPageIndex = pageIndex - this.content.getAbsoluteStartPage();
        const bound = bounds[0]; // .find((item) => item.pageIndex === cellContentPageIndex);
        if (!bound || !bound.contentHeight) {
            return false;
        }

        const row = this.row;
        const margins = row.table.property?.tableCellMargin;
        let top: number = 0;
        let bottom: number = 0;
        if (margins) {
            top = margins.top.width;
            if (!row.isFixed()) {
                bottom = margins.bottom.width;
            }
            // bottom = margins.bottom.width;
        }
        const subHeight = bound.contentHeight - bound.height - top - bottom;
        if (subHeight > 0.5) {
            return true;
        }

        return false;
    }

    public getCellTextWithAttr(attrName?: string): any {
        const text = this.content.getSelectTextNoHidden(true);
        if (typeof attrName !== 'string') {
            return text;
        }
        const attrNames = attrName.split(',');
        const obj = { text };
        const newControls = this.content.getAllNewControls();
        if (newControls && newControls.length > 0) {
            const newControl = newControls[0];
            const customProperty = newControl.getCustomProperty();
            if (customProperty) {
                for (const an of attrNames) {
                    if (customProperty.has(an)) {
                        const prop = customProperty.get(an);
                        if (prop) {
                            Object.assign(obj, { [an]: prop.value });
                        }
                    }
                }
            }
        }
        return obj;
    }

    /**
     * 给表格单元格添加斜线
     * @param slashType 1: 左斜线， 2：右斜线
     */
    public setTableCellSlash(slashType: number): boolean {
        const property = this.property;
        const oldSlash = {
            bCellLeftSlash: property.bCellLeftSlash,
            bCellRightSlash: property.bCellRightSlash
        };
        const newSlash = {...oldSlash};
        let result = false;
        if (slashType === 1) {
            newSlash.bCellLeftSlash = property.bCellLeftSlash = !property.bCellLeftSlash;
            result = true;
        } else if (slashType === 2) {
            newSlash.bCellLeftSlash = property.bCellRightSlash = !property.bCellRightSlash;
            result = true;
        } else if (slashType === -1 && (property.bCellLeftSlash || property.bCellRightSlash)) {
            newSlash.bCellLeftSlash = property.bCellLeftSlash = false;
            newSlash.bCellRightSlash = property.bCellRightSlash = false;
            result = true;
        }
        if (result) {
            const history = this.row.table.getHistory();
            if ( history ) {
                history.addChange(new ChangeTableCellSlash(this, oldSlash, newSlash));
            }
        }
        return result;
    }

    public getSlashBorders(): number[] {
        const borderTypes = [];
        if (this.property.bCellLeftSlash) {
            borderTypes.push(1);
        }
        if (this.property.bCellRightSlash) {
            borderTypes.push(2);
        }
        return borderTypes;
    }

    // private getTableCol(col: number): string {
    //     const colDiff = 52;
    //     let nCal = 0;
    //     let result: string = '';

    public setDefaultTimeCellContent(bRefresh: boolean = true, bFormat?: boolean): void {
        const props = this.property.nisProperty;
        const format = props.timeFormat || CellTimeFormat.HM;
        const reg = /HH([\s\S]+)MM(([\s\S]+)SS)?/g;
        const match = reg.exec(format);
        if (!match) {
            return;
        }

        props.cellText = undefined;
        const contents = this.content.content;
        let textProp: any;
        contents.splice(1, contents.length);
        const para = contents[0] as Paragraph;
        if (bFormat) {
            textProp = para.content[0].textProperty;
        }
        para.content.splice(0, para.content.length - 1);
        // 时portion
        let index = 0;
        const hPortion = new ParaPortion(para);
        hPortion.addText('--');
        para.addToContent(index++, hPortion);

        // 时分隔符
        const hAfterPortion = new ParaPortion(para);
        hAfterPortion.addText(match[1] || '');
        para.addToContent(index++, hAfterPortion);

        // 分portion
        const mPortion = new ParaPortion(para);
        mPortion.addText('--');
        para.addToContent(index++, mPortion);

        // 只要设置了秒才能
        if (match[3]) {
            // 分分隔符
            const mAfterPortion = new ParaPortion(para);
            mAfterPortion.addText(match[3] || '');
            para.addToContent(index++, mAfterPortion);

            // 秒
            const sPortion = new ParaPortion(para);
            sPortion.addText('--');
            para.addToContent(index++, sPortion);
        }

        this.content.setCellContentProps(this.getCellContentProps());
        if (textProp) {
            hPortion.textProperty = textProp.copy();
            // hAfterPortion.textProperty = textProp.copy();
            mPortion.textProperty = textProp.copy();
        }
        if (bRefresh) {
            // para.selectOnePortion(0);
            const document = para.getDocument();
            if (document) {
                document.recalculate();
            }
        }
    }

    public initOpenDefalutCellContent(): boolean {
        if (this.isBPCell()) {
            const props = this.property.nisProperty;
            let format = props.bloodPressureFormat;
            if (!isValidBPFormat(format)) {
                format = props.bloodPressureFormat = BloodPressureFormat.ASlashB;
            }
            const res = this.content.initOpenDefalutCellContent(format.slice(3, 4), props.cellText);
            if (res === false) {
                this.setDefaultBPCellContent();
            }
            return true;
        }

        return false;
    }

    public updateTimeCellTimeFormat(): void {
        const props = this.property.nisProperty;
        const format = props.timeFormat;
        const reg = /HH([\s\S]+)MM(([\s\S]+)SS)?/g;
        const match = reg.exec(format);
        if (!match || !match[1]) {
            this.setDefaultTimeCellContent();
            return;
        }
        const para = this.content.content[0];
        if (!para || !para.isParagraph()) {
            this.setDefaultTimeCellContent();
            return;
        }
        const contents = para.getContent();
        const portion = contents[1] as ParaPortion;
        if (!portion || portion.isParaEndPortion()) {
            this.setDefaultTimeCellContent();
            return;
        }

        portion.content.splice(0, contents.length);
        portion.addText(match[1]);
        this.setNisPropCellText(para as any);
    }

    public setNISCellTimeText(text: string, bFormat?: boolean): boolean {
        const time = text.split(':');
        if ( 2 !== time.length ) {
            return false;
        }

        this.bUpdateTimeOnblur = true;
        this.validTimeCellContent(false, bFormat);

        const contents = this.content.content;
        const para = contents[0] as Paragraph;

        const prePortion = para.content[0];
        const lastPortion = para.content[2];

        if ( prePortion ) {
            prePortion.removeFromContent(0, 2);
            let current = time[0];
            if (current.length === 1) {
                current = '0' + current;
            }
            prePortion.addText(current);
        }

        if ( lastPortion ) {
            lastPortion.removeFromContent(0, 2);
            let current = time[1];
            if (current.length === 1) {
                current = '0' + current;
            }
            lastPortion.addText(current);
        }
        this.setNisPropCellText(para);
        return true;
    }

    public setNISCellBPText(text: string, bRefresh: boolean = true, bFormat?: boolean): number {
        // const format = this.property.nisProperty.bloodPressureFormat || BloodPressureFormat.AColonB;
        const arrs = text.split(':');
        if ( 2 !== arrs.length ) {
            return ResultType.ParamError;
        }
        const num1: any = arrs[0];
        const num2: any = arrs[1];
        if (isNaN(num1) && num1 !== '---' || isNaN(num2) && num2 !== '---') {
            return ResultType.ParamError;
        }

        bRefresh = bFormat !== true;

        // this.bUpdateTimeOnblur = true;
        // this.validBPCellContent(bRefresh, bFormat);

        const contents = this.content.content;
        const para = contents[0] as Paragraph;
        const portions = para.content;
        if (portions.length !== 4) {
            this.validBPCellContent(bRefresh, bFormat);
        }
        const prePortion = para.content[0];
        const lastPortion = para.content[2];
        // let textContents: any[];
        let bChanged = false;
        if ( prePortion ) {
            prePortion.removeFromContent(0, 3);
            // num1 = num1.padStart(3);
            // textContents = prePortion.content;
            // for (let index = 0, length = textContents.length; index < length; index++) {
            //     const act = num1.slice(index, index + 1);
            //     if (act === undefined) {
            //         break;
            //     }
            //     textContents[index].content = act;
            // }
            prePortion.addText(num1);
            bChanged = true;
        }

        if ( lastPortion ) {
            lastPortion.removeFromContent(0, 3);
            // num2 = num2.padEnd(3);
            lastPortion.addText(num2);
            bChanged = true;
        }

        const document = this.row.table.logicDocument;
        if (bChanged && document) {
            this.setNisPropCellText(para);
            if (bRefresh === true) {
                document.recalculate();
            }
            document.updateCursorXY();
        }
        return ResultType.Success;
    }

    // public getNISProperty(): INISProperty {
    //     if (this.property != null) {
    //         return this.property.nisProperty;
    //     }
    //     return null;
    // }

    public getCellType(): number {
        return this.property.getCellType();
    }

    public isTextCell(): boolean {
        return this.property.isTextCell();
    }

    public isDateCell(): boolean {
        return this.property.isDateCell();
    }

    public isTimeCell(): boolean {
        return this.property.isTimeCell();
    }

    public isBPCell(): boolean {
        return this.property.isBPCell();
    }

    public isListCell(): boolean {
        return this.property.isListCell();
    }

    public isSignCell(): boolean {
        return this.property.isSignCell();
    }

    public isQuickCell(): boolean {
        return this.property.isQuickCell();
    }

    public isNumberCell(): boolean {
        return this.property.isNumberCell();
    }

    public getCustomProperty(): ICustomProps[] {
        return this.property.getCustomProperty();
    }

    public canFormularCalc(): boolean {
        return this.property.isTextCell();
    }

    public isNewControlReadOnly(): boolean {
        const table = this.row.table;
        if ( table.isAdminMode() ) {
            return false;
        }

        if ( this.isCellProtected() ) {
            return true;
        }

        if ( table.property.isTableReadOnlyProtect() ) {
            return true;
        }

        return false;
    }

    public updateCursorType(pointX: number, pointY: number, curPage: number): void {
        this.content.updateCursorType(pointX, pointY, curPage);
    }

    // public getTableCellName(col: number, row: number): string {
    //     return this.getTableCol(col) + row;
    // }

    public addToContent(nPos: number, item: Paragraph): void {
        this.content.addToContent(nPos, item);
    }

    public getContent(): DocumentContentElementBase[] {
        return this.content.content;
    }

    public getOperateContent(): DocumentContent {
        return this.content;
    }

    public getCellContentText(): string {
        if (this.getCellType() === NISTableCellType.Date) {
            const dateTime = this.getDateTime();
            if (isStrictDateFormat(dateTime) === true) {
                return dateTime;
            } else {
                if (isLooseDateFormat(dateTime) === true) {
                    const fIndex = 4;
                    const lIndex = dateTime.lastIndexOf('-');
                    let month = dateTime.slice(fIndex + 1, lIndex);
                    if (month.length < 2) {
                        month = '0' + month;
                    }
                    let day = dateTime.slice(lIndex + 1);
                    if (day.length < 2) {
                        day = '0' + day;
                    }
                    const result = dateTime.slice(0, fIndex) + '-' + month + '-' + day;
                    // console.log(result)
                    return result;

                } else {
                    // if not conform to 'xxxx-xx-xx', return empty str
                    // tslint:disable-next-line: no-console
                    console.log('日期单元格dateTime的格式有误');
                    return '';
                }
            }
        }
        return this.content.getContentText();
    }

    public getVertAlign(): VertAlignType {
        return this.property.vertAlign;
    }

    public setVertAlign(type: VertAlignType): boolean {
        if ( type === this.property.vertAlign ) {
            return false;
        }

        const history = this.row.table.getHistory();
        if ( history ) {
            history.addChange(new ChangeTableCellVertAlign(this, this.property.vertAlign, type));
        }

        this.property.vertAlign = type;
        return true;
    }

    public setVertAlign2(type: VertAlignType): void {
        if (null != type && !isNaN(type)) {
            this.property.vertAlign = type;
        }
    }

    public setFormula(par: ITableCellFormulaPar): boolean {
        if ( null == par || null == par.formulaType ) {
            return false;
        }
        if ( null == this.property.formula ) {
            this.property.formula = {
                formulaType: undefined,
                startCell: undefined,
                endCell: undefined,
                addFormula: undefined,
                multi1: undefined,
                multi2: undefined,
                mixFormula: undefined,
            };
        }

        const formula = this.property.formula;
        const prevFormulaType = par.formulaType;
        const prevStartCell = par.startCell;
        const prevEndCell = par.endCell;
        const prevAddFormula = par.addFormula;
        const prevMulti1 = par.multi1;
        const prevMulti2 = par.multi2;
        const prevMixFormula = par.mixFormula;
        if ( par.bClear ) {
            par.formulaType = undefined;
            par.startCell = undefined;
            par.endCell = undefined;
            par.addFormula = undefined;
            par.multi1 = undefined;
            par.multi2 = undefined;
            par.mixFormula = undefined;
        }

        const result = (formula.formulaType !== par.formulaType || formula.startCell !== par.startCell
            || formula.endCell !== par.endCell || formula.addFormula !== par.addFormula
            || formula.multi1 !== par.multi1 || formula.multi2 !== par.multi2
            || formula.mixFormula !== par.mixFormula);

        if ( result ) {
            formula.formulaType = par.formulaType;
            formula.startCell = par.startCell;
            formula.endCell = par.endCell;
            formula.addFormula = par.addFormula;
            formula.multi1 = par.multi1;
            formula.multi2 = par.multi2;
            formula.mixFormula = par.mixFormula;

            par.formulaType = prevFormulaType;
            par.startCell = prevStartCell;
            par.endCell = prevEndCell;
            par.addFormula = prevAddFormula;
            par.multi1 = prevMulti1;
            par.multi2 = prevMulti2;
            par.mixFormula = prevMixFormula;
        }

        return result;
    }

    public getFormula(): ITableCellFormulaPar {
        return this.property.formula;
    }

    public getCellFormula(): string {
        let result = '';
        const formula = this.property.formula;
        if ( formula ) {
            switch ( formula.formulaType) {
                case CellFormulaCalc.SUM:
                    result = FormulaType.SUM + '(' + formula.startCell + ',' + formula.endCell + ')';
                    break;
                case CellFormulaCalc.ADD:
                    result = FormulaType.ADD + '(' + formula.addFormula + ')';
                    break;
                case CellFormulaCalc.MUL:
                    result = FormulaType.MUL + '(' + formula.multi1 + ',' + formula.multi2 + ')';
                    break;
                case CellFormulaCalc.MIX:
                    result = FormulaType.MIX + '(' + formula.mixFormula + ')';
                    break;
            }
        }

        return result;
    }

    public setCellFormula(formula: string, rowIndex: number, colIndex: number): void {
        const paraments =  {
            formulaType: undefined,
            startCell: undefined,
            endCell: undefined,
            addFormula: undefined,
            multi1: undefined,
            multi2: undefined,
            mixFormula: undefined,
        };

        if ( formula && 0 < formula.length ) {
            const pos = formula.indexOf('(');
            const type = formula.substr(0, pos);
            switch (type) {
                case FormulaType.SUM:
                    const startCell = formula.substring(pos + 1, formula.indexOf(','));
                    const endCell = formula.substring(formula.indexOf(',') + 1, formula.length - 1);

                    paraments.formulaType = CellFormulaCalc.SUM,
                    paraments.startCell = startCell;
                    paraments.endCell = endCell;

                    this.setFormula(paraments);
                    break;
                case FormulaType.ADD:
                    paraments.formulaType = CellFormulaCalc.ADD,
                    paraments.addFormula = formula.substring(pos + 1, formula.length - 1);

                    this.setFormula(paraments);
                    break;
                case FormulaType.MUL:
                    const multi1 = formula.substring(pos + 1, formula.indexOf(','));
                    const multi2 = formula.substring(formula.indexOf(',') + 1, formula.length - 1);
                    paraments.formulaType = CellFormulaCalc.MUL,
                    paraments.multi1 = multi1;
                    paraments.multi2 = multi2;

                    this.setFormula(paraments);
                    break;
                case FormulaType.MIX:
                    paraments.formulaType = CellFormulaCalc.MIX,
                    paraments.mixFormula = formula.substring(pos + 1, formula.length - 1);

                    this.setFormula(paraments);
                    break;

                default:
                    // tslint:disable-next-line: no-console
                    console.log('formula error: ' + formula);
                    break;
            }

            const table = this.row.getTable();
            if ( table ) {
                table.setFormulaManager(paraments, this, table.getTableCellName(rowIndex, colIndex));
            }
        }
    }

    // this method also considers datebox' text refresh after set format
    public setCellProps(props: ITableCellProperty): number {
        this.setCellProtected(props.bProtected);
        // this.setDateFormat();
        // this.setTimeFormat();
        if (props.nisProperty != null) {
            if (this.property != null) {
                // // should put before set props
                // if (this.property.nisProperty.type === NISTableCellType.Date) {
                //     this.setDateProps(props.nisProperty);
                // }

                this.setNisProperty(props.nisProperty);

                // the setNISProperty() is after recalc()
                this.property.setNISProperty(props.nisProperty);

            }
        }
        // console.log(this.property)

        return ResultType.Success;
    }

    public setCellProps2(props: ITableCellProperty): number {
        this.setCellProtected(props.bProtected);

        if (props.nisProperty && this.property) {
            this.property.setNISProperty2(props.nisProperty);
        }

        return ResultType.Success;
    }

    public judgeTimeAndBpCell(): boolean {
        if (this.isTimeCell() || this.isBPCell()) {
            const cellText = this.getNisPropCellText();
            if (cellText == null || ~cellText.indexOf('-')) {
                return false;
            }
        }

        return true;
    }

    // this method also considers datebox' text refresh after set format
    public setNISCellProps(nisProps: INISProperty): number {
        // if the first returns other than 0, STILL need to look further
        const res1 = this.setNisProperty(nisProps);
        const res2 = this.property.setNISProperty(nisProps);
        return res1 || res2;
    }

    public setNisProperty(property: INISProperty): number {
        if (!property) {
            return ResultType.UnEdited;
        }

        let bChange: boolean = false;
        let needRecalc: boolean = false;
        const nisProperty = this.property.nisProperty;
        /*if (property.bloodPressureFormat != null && property.bloodPressureFormat !== nisProperty.bloodPressureFormat) {
            nisProperty.bloodPressureFormat = property.bloodPressureFormat;
            this.updateBPCellFormat();
            bChange = true;
            needRecalc = true;
        } else */if (this.isTimeCell() && property.timeFormat != null && property.timeFormat !== nisProperty.timeFormat) {
            nisProperty.timeFormat = property.timeFormat;
            this.updateTimeCellTimeFormat();
            bChange = true;
            needRecalc = true;
        } else if (nisProperty.type === NISTableCellType.Date) {
            // should put before set props since need compare
            this.setDateProps(property);
            bChange = true;
        } else if (nisProperty.type === NISTableCellType.Number) {
            bChange = (ResultType.Success === this.setNISNumCellProps(property));
            needRecalc = bChange;
            if (bChange && !this.isValidOfNumberCellContent()) {
                this.resetNumberCellContent();
            }
        }

        if (bChange) {
            const doc = this.row.table.logicDocument;
            if (doc && needRecalc === true) {
                doc.recalculate();
            }
            return ResultType.Success;
        }

        return ResultType.UnEdited;
    }

    public getSerialNumber(): string {
        return this.property.getSerialNumber();
    }

    // private getTableCol(col: number): string {
    //     const colDiff = 52;
    //     let nCal = 0;
    //     let result: string = '';

    //     do {
    //         nCal = col % colDiff;

    //         if ( nCal >= 26 ) {
    //             result = '' !== result ? String.fromCharCode(97 - 26 + nCal) + result :
    //                         String.fromCharCode(97 - 26 + nCal);
    //         } else {
    //             result = '' !== result ? String.fromCharCode(65 + nCal) + result :
    //                         String.fromCharCode(65 + nCal);
    //         }

    //         col = col - nCal;
    //         if ( 0 === col ) {
    //             break;
    //         }

    //         col /= colDiff;
    //         --col;
    //     } while ( true );

    //     return result;
    // }

    public setCellText(text: string): void {
        // console.log(text)
        // console.log(this)
        let bChanged: boolean = true;
        if ( this.isQuickCell() ) {
            const para = this.content.getCurrentParagraph();
            para.insertText(text);
        } else {
            const contents = this.getContent();
            let para = contents[0];
            let paraProp = null;
            let textProp = null;
            if (para != null && para instanceof Paragraph) {
                textProp = para.getDirectTextProperty().copy();
                paraProp = para.getParagraphProperty().copy();
            }
            if (contents.length > 1) {
                this.content.clearContent1();
            }

            para = this.getContent()[0]; // empty para
            if (para instanceof Paragraph) {
                if (paraProp != null) {
                    // para.setParagraphProperty(paraProp);
                    para.paraProperty = paraProp;
                }
                bChanged = para.setText(text, textProp);
            }
        }
        if (bChanged) {
            this.content.recalculate();
        }

        this.content.updateCursorXY();
    }

    public canRemoveAtCurCell(direction: number): any {
        if (this.oldSelectionBounds.length !== 1 || this.isTimeCell()) {
            return;
        }

        return this.content.canRemoveAtCurCell(direction);
    }

    public recalculatePage(page: number): void {
        this.content.recalculatePage(page, true);
    }

    public getMaxHeight(): number {
        const contents = this.row.content;
        let maxHeight: number = 0;
        for (let index = 0, len = contents.length; index < len; index++) {
            const cell = contents[index];
            if (cell === this) {
                continue;
            }
            const height = cell.oldSelectionBounds[0]?.contentHeight;
            if (maxHeight < height) {
                maxHeight = height;
            }
        }

        return maxHeight;
    }

    public setCellTextNoRecal(text: string): void {
        // console.log(text)
        // console.log(this)
        if ( !this.isQuickCell() ) {
            let para = this.getContent()[0];
            let paraProp = null;
            let textProp = null;
            if (para != null && para instanceof Paragraph) {
                textProp = para.getDirectTextProperty().copy();
                paraProp = para.getParagraphProperty().copy();
            }

            this.content.clearContent();

            para = this.getContent()[0]; // empty para

            if (para instanceof Paragraph) {
                if (paraProp != null) {
                    // para.setParagraphProperty(paraProp);
                    para.paraProperty = paraProp;
                }
                const portion = new ParaPortion(para);
                if (textProp != null) {
                    portion.textProperty = textProp;
                }
                portion.addText(text);
                para.addToContent(0, portion);
            }
        } else if ( this.isQuickCell() ) {
            const para = this.content.getCurrentParagraph();
            para.insertText(text);
        }
    }

    // public setNISQuickCellValueByArray(): boolean {
    //     ;
    // }

    /******************** NIS TABLE CELL START ********************/

    public getNisPropCellText(): string {
        return this.property?.nisProperty?.cellText;
    }

    // date
    public resetNISDateCellContent(): void {
        // keep the first para's props and textProps
        // let para = this.getContent()[0];
        // let textProp = null;
        // let paraProp = null;
        // if (para != null && para instanceof Paragraph) {
        //     textProp = para.getDirectTextProperty().copy();
        //     paraProp = para.getParagraphProperty().copy();
        // }

        // // clear table cell content
        // this.content.clearContent();

        // para = this.getContent()[0]; // empty para
        // if (para != null && para instanceof Paragraph) {
        //     if (paraProp != null) {
        //         // para.setParagraphProperty(paraProp);
        //         para.paraProperty = paraProp;
        //     }
        //     const portion = new ParaPortion(para);
        //     if (textProp != null) {
        //         portion.textProperty = textProp;
        //     }
        //     portion.addText('');
        //     para.addToContent(0, portion);
        // }
        // console.log(para)
        this.setCellText('');

        const nisProps = this.getNISProperty2();
        if (nisProps != null) {
            nisProps.dateTime = undefined;
            nisProps.text = '';
            nisProps.time = undefined;
        }
    }

    public setDateBoxFormat(type: NISDateBoxFormat, customFormat?: ICustomFormatDateProps): boolean {
        const nisProps = this.property != null ? this.property.getNISProperty() : null;
        if (nisProps == null) {
            return false;
        }
        if (type === undefined || type === nisProps.dateBoxFormat) {
          return this.setCustomDateBoxFormat(customFormat);
        }
        if (!(type in DateBoxFormat)) {
          // crash safe
          type = NISDateBoxFormat.DateSlash;
        }

        // const history = this.getHistory();
        // if ( history ) {
        //   history.addChange(new ChangeNewControlDateBoxFormat(this, this.dateBoxFormat, type));
        // }
        nisProps.dateBoxFormat = type;
        return this.setCustomDateBoxFormat(customFormat);
        // this.setDirty();
        // return true;
    }

    public setCustomDateBoxFormat(format: ICustomFormatDateProps): boolean {
        // console.log(format)
        const nisProps = this.property != null ? this.property.getNISProperty() : null;
        if (nisProps == null) {
            return false;
        }

        if (format === undefined || nisProps.dateBoxFormat !== NISDateBoxFormat.AutoCustom) {
          return false;
        }
        nisProps.customFormat = {
          day: '', //
          dayAfter: '', //
          hour: '', //
          hourAndMinute: '', //
          millisecond: '',
          minute: '', //
          minuteAndSecond: '',
          month: '', //
          monthAnyDay: '', //
          second: '',
          secondAfter: '',
          year: '', //
          yearAndMonth: '', //
        };

        // this.customFormat = format;

        for (const key in format) {
          if (key != null) {
            let val = format[key];
            if (val == null) {
              val = '';
            }
            nisProps.customFormat[key] = val;
          }
        }
        // this.setDirty();
        return true;
    }

    public setNISCellDateTime(selectedDate: string): boolean {
        const nisProps = this.property != null ? this.property.getNISProperty() : null;
        if (nisProps == null) {
            return false;
        }

        let res = this.setDateTime(selectedDate);
        res = (ResultType.Failure !== this.setDateBoxText());

        return res;
    }

    public setNISCellDateTimeNoRecal(selectedDate: string): boolean {
        const nisProps = this.property != null ? this.property.getNISProperty() : null;
        if (nisProps == null) {
            return false;
        }

        let res = this.setDateTime(selectedDate);
        res = (ResultType.Failure !== this.setDateBoxTextNoRecal());

        return res;
    }

    public getDateTime(): string {
        const nisProps = this.property != null ? this.property.getNISProperty() : null;
        if (nisProps == null) {
            return;
        }

        return nisProps.dateTime;
    }

    public getDateTime2(): string {
        const nisProps = this.property != null ? this.property.getNISProperty() : null;
        if (nisProps == null) {
            return;
        }

        return (nisProps.time ? '' + nisProps.time.getFullYear() + '-'
                    + (nisProps.time.getMonth() + 1) + '-' + nisProps.time.getDate() :
            '');
    }

    public getTimeText(): string {
        if (!this.isTimeCell()) {
            return '';
        }
        const text = this.content.getSelectText(true);
        const match = /^(\d{2})[\s\S]*(\d{2})$/.exec(text);
        if (!match) {
            return '';
        }

        return `${match[1]}:${match[2]}`;
    }

    public setDateTime(dateTime?: string): boolean {
        const nisProps = this.property != null ? this.property.getNISProperty() : null;
        if (nisProps == null) {
            return false;
        }
        // if (this.dateTime && this.dateTime.split('.')[0] === dateTime) {
        //   return false;
        // }
        let time = new Date();
        if (dateTime != null) {
          time = new Date(dateTime);
        }
        if (time && !isNaN(time.getTime())) {

        //   const history = this.getHistory();
        //   if ( history ) {
        //     history.addChange(new ChangeNewControlDateBoxTime(this, this.dateTime, dateTime));
        //   }

            nisProps.dateTime = dateTime;
            nisProps.time = time;
            return true;
        }
        // this.setDirty();
        return false;
    }

    public setDateBoxText(): number {
        const bChange = this.setDateTimeText();
        if (bChange === false) {
          return ResultType.UnEdited;
        }
        // return super.setNewControlText(this.text);
        // set text to table cell
        const nisProps = this.property != null ? this.property.getNISProperty() : null;
        if (nisProps == null) {
            return ResultType.Failure;
        }
        this.setCellText(nisProps.text);
        return ResultType.Success;
    }

    public setDateBoxTextNoRecal(): number {
        const bChange = this.setDateTimeText();
        if (bChange === false) {
          return ResultType.UnEdited;
        }
        // return super.setNewControlText(this.text);
        // set text to table cell
        const nisProps = this.property != null ? this.property.getNISProperty() : null;
        if (nisProps == null) {
            return ResultType.Failure;
        }
        this.setCellTextNoRecal(nisProps.text);
        return ResultType.Success;
    }

    public getCustomDateFormat(): ICustomFormatDateProps {
        const nisProps = this.property != null ? this.property.getNISProperty() : null;
        if (nisProps == null) {
            return null;
        }
        return nisProps.customFormat || {};
    }

    public setDateProps(property: INISProperty): void {
        const nisProps = this.property.nisProperty;
        if (this.needUpdateDateText(nisProps, property) === true) { // 0 is enum
            // refresh date text
            // if ( history ) {
            //     history.addChange(new ChangeNewControlDateBoxFormat(this, this.dateBoxFormat, property.dateBoxFormat));
            // }
            // res = ResultType.Success;
            nisProps.dateBoxFormat = property.dateBoxFormat;
            // also consider custom format!
            nisProps.customFormat = property.customFormat;
            nisProps.hideDateText = property.hideDateText;
            this.setDateBoxText();
        }
    }

    public needUpdateDateText(curProperty: INISProperty, newProperty: INISProperty): boolean {
        if (curProperty != null && newProperty != null) {
            if (newProperty.dateBoxFormat !== curProperty.dateBoxFormat) {
                return true;
            } else if (newProperty.hideDateText !== curProperty.hideDateText) {
                return true;
            } else {
                // custom format
                if (newProperty.dateBoxFormat === NISDateBoxFormat.AutoCustom &&
                curProperty.dateBoxFormat === NISDateBoxFormat.AutoCustom) {
                    if (JSON.parse(JSON.stringify(newProperty.customFormat)) !==
                    JSON.parse(JSON.stringify(curProperty.customFormat))) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    public setCompoundCellCodeAndValue(): boolean {
        return false;
    }

    // combox
    public updateSelectItemByText() {
        if (this.isQuickCell()) {
            return false;
        }
        const items = this.getItemList();
        if (!items || items.length === 0) {
            return false;
        }

        let text = this.getCellContentText();
        if (!text) {
            return this.resetItems();
        }
        
        const nisProps = this.getNISProperty();
        let key = 'code';
        if (nisProps.bShowValue) {
            key = 'value';
        }
        let curText = '';
        const separator = nisProps.separator || '';
        items.forEach((item) => {
            if (item.bSelect) {
                curText += item[key] + separator;
            }
        });
        if (separator) {
            curText = curText.slice(0, -(separator.length));
        }
        if (curText === text) {
            return false;
        }

        const selectedIndexs = [];
        let bResult = false;
        if (nisProps.bCheckMultiple) {
            // const separator = nisProps.separator;
            const texts = text.split(separator);
            items.forEach((item, itemIndex) => {
                if (texts.includes(item[key])) {
                    selectedIndexs.push(itemIndex);
                }
            });
            if (selectedIndexs.length > 0) {
                this.setNISCellListItems(selectedIndexs);
                bResult = true;
            }
        } else {
            const actIndex = items.findIndex((item) => item[key] === text);
            if (actIndex !== -1) {
                selectedIndexs.push(actIndex);
                if (items[actIndex].bSelect !== true) {
                    this.setNISCellListItems(selectedIndexs);
                }
                bResult = true;
            }
        }

        if (bResult) {
            this.content.updateCursorXY();
            return bResult;
        } else {
            bResult = this.resetItems();
        }
        
        return bResult;
    }

    public getItemList(): CodeValueItem[] {
        const nisProps = this.property != null ? this.property.getNISProperty() : null;
        if (nisProps == null) {
            return null;
        }
        return nisProps.items || [];
    }

    public resetItems(): boolean {
        let bChange: boolean = false;
        this.getItemList()
        .forEach((item) => {
            if (item.bSelect) {
                bChange = true;
                item.bSelect = false;
            }
        });
        if (bChange) {
            this.content.updateCursorXY();
            this.setCellText('');
        }
        return bChange;
    }

    public setNISCellListItems(selectItemsPos: number[]): number {
        // console.log(selectItemsPos)
        let text = '';

        const nisProps = this.getNISProperty();
        // console.log(nisProps)
        if (nisProps != null) {
            const length = nisProps.items.length;
            if ( 0 === length ) {
                return ResultType.UnEdited;
            }

            let bResetSelect = true;
            if ( !selectItemsPos && nisProps.items ) {
                selectItemsPos = [];
                bResetSelect = false;
                nisProps.items.forEach((valueItem, index) => {
                    if ( valueItem.bSelect ) {
                        selectItemsPos.push(index);
                    }
                });
            }

            for (let index = 0; index < length; index++) {
                const item = nisProps.items[index];
                if ( true === selectItemsPos.includes(index) ) {
                    // console.log(item)
                    text += (true !== nisProps.bShowValue ? item.code : item.value);
                    text += (nisProps.separator ? nisProps.separator : '');
                    if ( bResetSelect ) {
                        // TODO
                        // this.setItemSelectUndoRedo(true, item);
                        item.bSelect = true;
                    }
                } else {
                    if ( bResetSelect ) {
                        // TODO
                        // this.setItemSelectUndoRedo(false, item);
                        item.bSelect = false;
                    }
                }
            }

            const selectNumber = selectItemsPos.length;
            if ( 0 !== selectNumber ) {
                if (nisProps.separator) {
                    text = text.slice(0, text.length - nisProps.separator.length);
                }

                if ( null != nisProps.selectPrefixContent ) {
                    text = nisProps.selectPrefixContent + text;
                }
            }

            if ( selectNumber < length && null != nisProps.prefixContent && '' !== nisProps.prefixContent ) {
                if ( 0 < selectNumber ) {
                    text += NewControlDefaultSetting.DefaultPrefixSeparator;
                }

                text += nisProps.prefixContent;

                for (let index = 0; index < length; index++) {
                    const item = nisProps.items[index];
                    if ( false === selectItemsPos.includes(index) ) {
                        text += (true !== nisProps.bShowValue ? item.code : item.value);
                        text += (nisProps.separator ? nisProps.separator : '');
                        if ( bResetSelect ) {
                            // TODO
                            // this.setItemSelectUndoRedo(false, item);
                            item.bSelect = false;
                        }
                    }
                }
                if (nisProps.separator) {
                    text = text.slice(0, text.length - nisProps.separator.length);
                }
            }
        }

        // console.log(text)

        this.setCellText(text);
        return ResultType.Success;
    }

    public setNISCellListItemsNoRecal(selectItemsPos: number[]): number {
        // console.log(selectItemsPos)
        let text = '';

        const nisProps = this.getNISProperty();
        // console.log(nisProps)
        if (nisProps != null) {
            const length = nisProps.items.length;
            if ( 0 === length ) {
                return ResultType.UnEdited;
            }

            let bResetSelect = true;
            if ( !selectItemsPos && nisProps.items ) {
                selectItemsPos = [];
                bResetSelect = false;
                nisProps.items.forEach((valueItem, index) => {
                    if ( valueItem.bSelect ) {
                        selectItemsPos.push(index);
                    }
                });
            }

            for (let index = 0; index < length; index++) {
                const item = nisProps.items[index];
                if ( true === selectItemsPos.includes(index) ) {
                    // console.log(item)
                    text += (true !== nisProps.bShowValue ? item.code : item.value);
                    text += (nisProps.separator ? nisProps.separator : '');
                    if ( bResetSelect ) {
                        // TODO
                        // this.setItemSelectUndoRedo(true, item);
                        item.bSelect = true;
                    }
                } else {
                    if ( bResetSelect ) {
                        // TODO
                        // this.setItemSelectUndoRedo(false, item);
                        item.bSelect = false;
                    }
                }
            }

            const selectNumber = selectItemsPos.length;
            if ( 0 !== selectNumber ) {
                if (nisProps.separator) {
                    text = text.slice(0, text.length - nisProps.separator.length);
                }

                if ( null != nisProps.selectPrefixContent ) {
                    text = nisProps.selectPrefixContent + text;
                }
            }

            if ( selectNumber < length && null != nisProps.prefixContent && '' !== nisProps.prefixContent ) {
                if ( 0 < selectNumber ) {
                    text += NewControlDefaultSetting.DefaultPrefixSeparator;
                }

                text += nisProps.prefixContent;

                for (let index = 0; index < length; index++) {
                    const item = nisProps.items[index];
                    if ( false === selectItemsPos.includes(index) ) {
                        text += (true !== nisProps.bShowValue ? item.code : item.value);
                        text += (nisProps.separator ? nisProps.separator : '');
                        if ( bResetSelect ) {
                            // TODO
                            // this.setItemSelectUndoRedo(false, item);
                            item.bSelect = false;
                        }
                    }
                }
                if (nisProps.separator) {
                    text = text.slice(0, text.length - nisProps.separator.length);
                }
            }
        }
        this.content.resetContentByText(text, this.getCellContentProps());
        return ResultType.Success;
    }

    public setNISNumCellProps(property: INISProperty): number {
        let res = ResultType.Failure;
        const nisProps = this.property.nisProperty;
        if ( nisProps ) {
            res = this.setNISNumCellMaxValue(property.maxValue) && res;
            res = this.setNISNumCellMinValue(property.minValue) && res;
            res = this.setNISNumCellPrecision(property.precision) && res;

            res = this.setNumMinWarn(property.minWarn) && res;
            res = this.setNumMaxWarn(property.maxWarn) && res;
            res = this.setNISNumCellUnit(property.unit) && res;
        }

        return res;
    }

    public setNumMinWarn(value: number): number {
        const nisProps = this.property.getNISProperty();
        if (!nisProps) {
            return ResultType.UnEdited;
        }

        if (value === undefined || nisProps.minWarn === value) {
            return ResultType.UnEdited;
        }

        nisProps.minWarn = value;
        return ResultType.Success;
    }

    public getNumMinWarn(): number {
        return this.property.nisProperty.minWarn;
    }

    public getNumMaxWarn(): number {
        return this.property.nisProperty.maxWarn;
    }

    public setNumMaxWarn(value: number): number {
        const nisProps = this.property.getNISProperty();
        if (!nisProps) {
            return ResultType.UnEdited;
        }

        if (value === undefined || nisProps.maxWarn === value) {
            return ResultType.UnEdited;
        }

        nisProps.maxWarn = value;
        return ResultType.Success;
    }

    public setNISNumCellMaxValue(maxValue: number): number {
        const nisProps = this.property != null ? this.property.getNISProperty() : null;
        if (!nisProps) {
            return ResultType.Failure;
        }

        if ( nisProps.maxValue !== maxValue ) {
            if ( null != maxValue && maxValue < this.getNISNumCellMinValue() ) {
                return ResultType.Failure;
            }

            nisProps.maxValue = maxValue;
            return ResultType.Success;
        }

        return ResultType.UnEdited;
    }

    public getNISNumCellMaxValue(): number {
        const nisProps = this.property != null ? this.property.getNISProperty() : null;
        if (!nisProps) {
            return ResultType.NumberNaN;
        }

        return nisProps.maxValue;
    }

    public setNISNumCellMinValue(minValue: number): number {
        const nisProps = this.property != null ? this.property.getNISProperty() : null;
        if (nisProps == null) {
            return ResultType.Failure;
        }

        if ( nisProps.minValue !== minValue ) {
            if ( null != minValue && minValue > this.getNISNumCellMaxValue() ) {
                return ResultType.Failure;
            }

            nisProps.minValue = minValue;
            return ResultType.Success;
        }

        return ResultType.UnEdited;
    }

    public getNISNumCellMinValue(): number {
        const nisProps = this.property != null ? this.property.getNISProperty() : null;
        if (nisProps == null) {
            return ResultType.NumberNaN;
        }

        return nisProps.minValue;
    }

    public setNISNumCellPrecision(precision: number): number {
        const nisProps = this.property != null ? this.property.getNISProperty() : null;
        if (nisProps == null) {
            return ResultType.Failure;
        }

        if ( nisProps.precision !== precision ) {
            nisProps.precision = precision;
            return ResultType.Success;
        }

        return ResultType.UnEdited;
    }

    public getNISNumCellPrecision(): number {
        const nisProps = this.property != null ? this.property.getNISProperty() : null;
        if (nisProps == null) {
            return ResultType.NumberNaN;
        }

        return nisProps.precision;
    }

    public setNISNumCellUnit(unit: string): number {
        const nisProps = this.property != null ? this.property.getNISProperty() : null;
        if (nisProps == null) {
            return ResultType.Failure;
        }

        if (unit != null && nisProps.unit !== unit ) {
            let cellText = this.getCellContentText();
            if (nisProps.unit) {
                const pos = cellText.lastIndexOf(nisProps.unit);
                cellText = (-1 === pos ? cellText : cellText.substring(0, pos));
            }

            nisProps.unit = unit;
            if (cellText) {
                cellText += unit;
            }
            this.content.resetContentByText(cellText, this.getCellContentProps());
            return ResultType.Success;
        }

        return ResultType.UnEdited;
    }

    public getNISNumCellUnit(): string {
        const nisProps = this.property != null ? this.property.getNISProperty() : null;
        if (nisProps == null) {
            return ResultType.StringEmpty;
        }

        return nisProps.unit;
    }

    public isValidOfNumberCellContent(): boolean {
        const nisProps = this.property != null ? this.property.getNISProperty() : null;
        if (nisProps == null || this.content.isEmpty()) {
            return true;
        } else if (1 < this.content.getElementsCount()) {
            return false;
        }

        let cellText = this.getCellContentText();
        if (nisProps.unit) {
            const pos = cellText.lastIndexOf(nisProps.unit);
            if (-1 === pos) {
                return false;
            } else {
                if (pos + nisProps.unit.length !== cellText.length
                    || (0 === pos && nisProps.unit.length === cellText.length)) {
                    return false;
                }

                cellText = cellText.substring(0, pos);
            }
        }
        const content = +cellText;
        if ( (null != nisProps.maxValue && content > nisProps.maxValue) ||
             (null != nisProps.minValue && content < nisProps.minValue) ) {
            return false;
        } else if ( null != nisProps.precision ) {
            const precision = content.toString()
                                        .split('.')[1];
            return !(precision && precision.length > nisProps.precision);
        }

        return true;
    }

    public resetNumberCellContent(bRecal?: boolean): void {
        const nisProps = this.property != null ? this.property.getNISProperty() : null;
        if (nisProps == null) {
            return ;
        }

        let bAddUnit = false;
        let bClearContent = false;
        let cellText = this.getCellContentText();
        const unit = nisProps.unit ? nisProps.unit : '';
        if (1 < this.content.getElementsCount()) {
            bAddUnit = true;
            cellText = parseFloat(cellText) + '';
        } else if (unit) {
            const pos = cellText.lastIndexOf(unit);
            const prevContentLenght = cellText.length;

            cellText = (-1 === pos ? cellText : cellText.substring(0, pos));

            if (-1 === pos) {
                // 数值框内没有unit
                bAddUnit = true;
                cellText = parseFloat(cellText) + '';
            } else if (prevContentLenght !== cellText.length + unit.length) {
                // unit之后还有多余的数值
                bAddUnit = true;
            } else if (0 === pos && prevContentLenght === unit.length) {
                bClearContent = true;
            }
        }

        const content = (cellText ? +cellText : undefined);
        let result = false;
        if ( null != nisProps.maxValue && content > nisProps.maxValue ) {
            result = this.content.resetContentByText(nisProps.maxValue.toString() + unit, this.getCellContentProps());
        } else if ( null != nisProps.minValue && content < nisProps.minValue ) {
            result = this.content.resetContentByText(nisProps.minValue.toString() + unit, this.getCellContentProps());
        } else if ( null != nisProps.precision ) {
            const texts = cellText.split('.');
            if ( texts[1] && texts[1].length > nisProps.precision ) {
                cellText = (nisProps.precision ? texts[0] + '.' + texts[1].substr(0, nisProps.precision) : texts[0]);
                result = this.content.resetContentByText(cellText + unit, this.getCellContentProps());
            } else if (bAddUnit) {
                result = this.content.resetContentByText(cellText + unit, this.getCellContentProps());
            }
        } else if (bAddUnit) {
            result = this.content.resetContentByText(cellText + unit, this.getCellContentProps());
        } else if (bClearContent) {
            result = this.content.resetContentByText(cellText, this.getCellContentProps());
        }

        if (bRecal && this.row.table.logicDocument && result) {
            const document = this.row.table.logicDocument;
            document.recalculate();
            document.updateCursorXY();
        }
    }

    public deleteSignContentByAuthor(author: string): boolean {
        return this.content.deleteSignContentByAuthor(author);
    }

    /**
     * 获取签名人
     * @param bRepeat 是否包含重复签名人
     * @returns
     */
    public getSignNames(bRepeat: boolean = false): string[] {
        const result = [];
        if ( !this.content.isEmpty() ) {
            this.content.content.forEach((element) => {
                if ( element instanceof Paragraph && element.paraProperty.nisSignAuthor ) {
                    const author = element.paraProperty.nisSignAuthor;
                    if (bRepeat || !result.includes(author)) {
                        result.push(element.paraProperty.nisSignAuthor);
                    }
                }
            });
        }

        return result;
    }

    public canDeleteContent(): boolean {
        // nis type limit
        const nisProperty = this.property != null ? this.property.nisProperty : null;
        // console.log(nisProperty)
        if ( nisProperty != null) {
            const nisProtectCellType: NISTableCellType[] = [NISTableCellType.Date, NISTableCellType.List];
            if (nisProtectCellType.includes(nisProperty.type) === true) {
                if (nisProperty.selectType !== NISSelectType.Combo) {
                    return false;
                }
            }
        }

        return true;
    }

    public refreshRecalData(data: any): void {
        const table = this.row?.table;
        if (!table) {
            return;
        }

        let bNeedRecalc = false;

        switch (data.type) {
            case HistroyItemType.DocumentContentAddItem:
            case HistroyItemType.DocumentContentAddItem: {
                break;
            }
            default:
                break;
        }

        table.recalcInfo.recalcBorders();
        this.refreshRecalData2(0, 0);
    }

    public refreshRecalData2( index: number, pageIndex: number ): void {
        const row = this.row;
        const table = row?.table;
        if (!table) {
            return;
        }

        table.recalcInfo.addCell(this);

        const cellIndex = this.index;
        const cellsCount = row.getCellsCount();
        if (0 < cellIndex && 0 < cellsCount) {
            const preCell = row.getCell(cellIndex <= cellsCount ? cellIndex - 1 : cellsCount - 1);
            if (preCell) {
                table.recalcInfo.addCell(preCell);
            }
        }

        if (0 <= cellIndex && 0 < cellsCount && cellIndex < cellsCount - 1) {
            const nextCell = row.getCell(cellIndex + 1);
            if (nextCell) {
                table.recalcInfo.addCell(nextCell);
            }
        }

        if (TableLayoutType.AutoFit === table.property.tableLayout) {
            if (table.parent) {
                const history = table.getHistory();
                history.recalculateTableGridAdd(table.getId());
            } else {
                return table.refreshRecalData2(0, 0);
            }
        }

        row.refreshRecalData2(cellIndex, pageIndex);
    }

    private setDateTimeText(): boolean {
        const nisProps = this.property != null ? this.property.getNISProperty() : null;
        if (nisProps == null) {
            return false;
        }
        // if (!this.time) {
        //   if (!this.text) {
        //     return;
        //   }
        //   this.text = sText;
        //   // this.setDateBoxText(this.text);
        //   return true;
        // }
        // const date = this.dateTime;
        // let format = NIS_DATE_FORMAT_STRING[nisProps.dateBoxFormat];
        let format = nisProps.dateBoxFormat;
        if (format == null) { // 0: autocustom
        //   format = this.getDateFormat();
          format = NISDateBoxFormat.DateSlash;
          if (!format) {
            return false;
          }
        }
        const customFormat = nisProps.customFormat;

        let text = nisDateFormat(nisProps.time, format, customFormat);
        if (nisProps.hideDateText === true) {
            text = '';
        }
        if (text === nisProps.text) {
          return false;
        }

        // const history = this.getHistory();
        // if ( history ) {
        //   history.addChange(new ChangeNewControlDateBoxText(this, this.text, text));
        // }

        nisProps.text = text;
        return true;
    }

    private getDateFormat(): string {
        return this.getYear() + this.getMonth() + this.getDay() + this.getHour() + this.getMinute()
        + this.getSecond() + this.getMillisecond();
    }

    private getYear(): string {
        const nisProps = this.property != null ? this.property.getNISProperty() : null;
        if (nisProps == null) {
            return '';
        }
        const custom = nisProps.customFormat;
        if (custom != null && custom.year) {
            return custom.year + (custom.yearAndMonth || '');
        }
        return '';
    }

    private getMonth(): string {
        const nisProps = this.property != null ? this.property.getNISProperty() : null;
        if (nisProps == null) {
            return '';
        }
        const custom = nisProps.customFormat;
        if (custom != null && custom.month) {
            return custom.month + (custom.monthAnyDay || '');
        }
        return '';
    }

    private getDay(): string {
        const nisProps = this.property != null ? this.property.getNISProperty() : null;
        if (nisProps == null) {
            return '';
        }
        const custom = nisProps.customFormat;
        if (custom != null && custom.day) {
            return custom.day + (custom.dayAfter || '');
        }
        return '';
    }

    private getHour(): string {
        const nisProps = this.property != null ? this.property.getNISProperty() : null;
        if (nisProps == null) {
            return '';
        }
        const custom = nisProps.customFormat;
        if (custom != null && custom.hour) {
            return custom.hour + (custom.hourAndMinute || '');
        }
        return '';
    }

    private getMinute(): string {
        const nisProps = this.property != null ? this.property.getNISProperty() : null;
        if (nisProps == null) {
            return '';
        }
        const custom = nisProps.customFormat;
        if (custom != null && custom.minute) {
            return custom.minute + (custom.minuteAndSecond || '');
        }
        return '';
    }

    private getSecond(): string {
        const nisProps = this.property != null ? this.property.getNISProperty() : null;
        if (nisProps == null) {
            return '';
        }
        const custom = nisProps.customFormat;
        if (custom != null && custom.second) {
            return custom.second + (custom.secondAfter || '');
        }
        return '';
    }

    private getMillisecond(): string {
        const nisProps = this.property != null ? this.property.getNISProperty() : null;
        if (nisProps == null) {
            return '';
        }
        const custom = nisProps.customFormat;
        if (custom != null && custom.millisecond) {
            return custom.millisecond + (custom.millisecondAfter || '');
        }
        return '';
    }

    private setNisPropCellText(para: Paragraph): void {
        this.property.nisProperty.cellText = para.getSelectText(true);
    }

    /******************** NIS TABLE CELL END ********************/

    private setBorder2(border: DocumentBorder, type: number): boolean {
        const history = this.row.table.getHistory();

        switch (type) {
            case 0: {
                if ( history && history.canAdd() ) {
                    history.addChange(new ChangeTableCellBorderTop(this, this.property.borders.top, border));
                }
                this.property.borders.top = border;
                break;
            }

            case 1: {
                if ( history && history.canAdd() ) {
                    history.addChange(new ChangeTableCellBorderRight(this, this.property.borders.right, border));
                }
                this.property.borders.right = border;
                break;
            }

            case 2: {
                if ( history && history.canAdd() ) {
                    history.addChange(new ChangeTableCellBorderBottom(this, this.property.borders.bottom, border));
                }
                this.property.borders.bottom = border;
                break;
            }

            case 3: {
                if ( history && history.canAdd() ) {
                    history.addChange(new ChangeTableCellBorderLeft(this, this.property.borders.left, border));
                }
                this.property.borders.left = border;
                break;
            }
            default:
                break;
        }

        if ( 0 === type || 1 === type || 2 === type || 3 === type ) {
            return true;
        } else {
            return false;
        }
    }
}
