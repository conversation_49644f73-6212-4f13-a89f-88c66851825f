import { XmlComponent, XmlAttributeComponent } from '../xml-components';
import { RegionProperties } from './regionProperties';
import { Paragraph } from '../paragraph';
import { RegionType, RegionMode, REGION_PROPS_DEFAULT } from '../../../../common/commonDefines';
import { DocumentContent } from '../../../../model/core/DocumentContent';
import { Table } from '../table';
import { NISTable } from '../table/nis-table';
import { addNode } from '@/common/struct/write';

export interface IRegionAttributesProperties {
  name: string;
  type: RegionType;
  mode: RegionMode;
}

class RegionAttributes extends XmlAttributeComponent<IRegionAttributesProperties> {
  protected xmlKeys: any = {
    name: 'name',
    type: 'type',
    mode: 'mode',
  };
}

export class Region extends XmlComponent {
  // private readonly properties: ParagraphProperties;
  private properties: RegionProperties;

  constructor(attrs: IRegionAttributesProperties) {
    super('rg');
    this.root.push(new RegionAttributes(attrs));

    // this.properties = new ParagraphProperties();
    // this.root.push(this.properties);

    // rgpr
    this.initializeProperties();
  }

  public addParagraph(paragraph: Paragraph): Region {
    this.root.push(paragraph);
    return this;
  }

  public addTable(table: Table): Region {
    this.root.push(table);
    return this;
  }

  public addNISTable(table: NISTable): Region {
    // tslint:disable-next-line: no-console
    console.warn('nistable shouldn\'t be added to region');
    return this;
  }

  public addRegion(region: Region): Region {
    this.root.push(region);
    return this;
  }

  public getProperties(): RegionProperties {
    return this.properties;
  }

  public addRegionProps(regionProps: IRegionProps, regionContent: DocumentContent): void {
    const {bHidden, bCanntDelete, bCanntEdit, bReverseEdit, title, bShowTitle,
       customProperty, serialNumber, maxLength, newControlInfo, identifier,
       placeholder, regExp} = regionProps;
    const curRegionProperties = this.properties;

    if (bHidden !== REGION_PROPS_DEFAULT.bHidden) {
      curRegionProperties.addHidden(bHidden + '');
    }
    if (bCanntDelete !== REGION_PROPS_DEFAULT.bCanntDelete) {
      curRegionProperties.addDeleteProtect(bCanntDelete + '');
    }
    if (bCanntEdit !== REGION_PROPS_DEFAULT.bCanntEdit) {
      curRegionProperties.addEditProtect(bCanntEdit + '');
    }
    if (bReverseEdit !== REGION_PROPS_DEFAULT.bReverseEdit) {
      curRegionProperties.addEditReverse(bReverseEdit + '');
    }
    if (identifier !== REGION_PROPS_DEFAULT.identifier) {
      curRegionProperties.addIdentifier(identifier);
    }
    if (title !== REGION_PROPS_DEFAULT.title) {
      curRegionProperties.addTitle(title);
    }
    if (bShowTitle !== REGION_PROPS_DEFAULT.bShowTitle) {
      curRegionProperties.addShowTitle(bShowTitle + '');
    }
    if (newControlInfo !== REGION_PROPS_DEFAULT.newControlInfo) {
      curRegionProperties.addTipContent(newControlInfo);
    }
    if (serialNumber !== REGION_PROPS_DEFAULT.serialNumber) {
      curRegionProperties.addSerialNumber(serialNumber);
    }
    if (maxLength != null && maxLength !== REGION_PROPS_DEFAULT.maxLength) {
      curRegionProperties.addMaxLength(maxLength + '');
    }

    if (placeholder !== REGION_PROPS_DEFAULT.placeholder) {
      curRegionProperties.addPlaceholder(placeholder);
    }

    if (regExp != null) {
      addNode('regExp', regExp, curRegionProperties.root);
    }

    // beginPos, endPos
    const startPage = regionContent.getAbsoluteStartPage();
    const endPage = startPage + regionContent.getPages().length - 1;
    // console.log(startPage, endPage)
    const yRange = regionContent.getRegionYRange();
    // console.log(yRange)

    curRegionProperties.addBeginPos(startPage + ',' + yRange[0]);
    curRegionProperties.addEndPos(endPage + ',' + yRange[1]);

    // custom properteis (stringify map -> object)
    if (customProperty != null && customProperty.size > 0) {
      curRegionProperties.addCustomProperty(customProperty);
    }
  }

  /**
   * initialize properties if not
   */
  private initializeProperties(): void {
    if (!this.properties) {
      this.properties = new RegionProperties();

      // should be the first child element
      // this.root.push(this.properties);
      this.root.unshift(this.properties);
    }
  }

}

export interface IRegionProps { // region属性
  serialNumber?: string;
  bCanntDelete: number;
  bCanntEdit: number;
  bHidden: number;
  bReverseEdit: number;
  title?: string;
  bShowTitle?: number;
  customProperty: Map<string, any>;
  maxLength?: number;
  newControlInfo?: string;
  identifier?: string;
  placeholder?: string;
  regExp?: string;
}
