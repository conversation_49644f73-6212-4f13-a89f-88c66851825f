import * as React from 'react';
import Dialog from '../../../ui/Dialog';
import Input from '../../../ui/Input';
import Button from '../../../ui/Button';
// import { message } from '../../../../../common/Message';
import { ParaEquation } from '../../../../../model/core/Paragraph/ParaDrawing';

interface IDialogProps {
    documentCore: any;
    visible: boolean;
    id: string;
    equation: ParaEquation;
    close: (name: string | number, bRefresh?: boolean) => void;
}

interface ITestContent {
    value1?: string;
    value2?: string;
    value3?: string;
    value4?: string;
}

interface IState {
    bRefresh: boolean;
}

export default class Menstruation2 extends React.Component<IDialogProps, {}> {
    private visible: boolean;
    private data: ITestContent;
    private oldData: ITestContent;
    private equationElemDom: HTMLElement;

    constructor(props: any) {
        super(props);
        this.visible = this.props.visible;
        this.data = {};
    }

    public render(): any {
        return (
            <Dialog
                id={this.props.id}
                preventDefault={true}
                visible={this.visible}
                open={this.open}
                footer={this.renderFooter()}
                width={500}
                title={'月经史表达式2'}
            >
                <div className='menstruation2-map'>
                    <div className='editor-line'>
                        <div className='w-050'>
                           初潮年龄
                        </div>
                        <div className='w-050'>
                            <span style={{paddingLeft: '10px'}}>经期(天)</span>
                        </div>
                    </div>
                    <div className='editor-line'>
                        <div className='w-050'>
                            <Input
                                name='value1'
                                onChange={this.onChange}
                                value={this.data.value1}
                            />
                        </div>
                        <div className='w-050'>
                            <Input
                                name='value2'
                                onChange={this.onChange}
                                value={this.data.value2}
                            />
                        </div>
                    </div>
                    <div style={{height:'10px'}}></div>
                    <div className='editor-line'>
                        <div className='w-050'>
                            末次月经/绝经年龄
                        </div>
                        <div className='w-050'>
                            <span style={{paddingLeft: '10px'}}>周期(天)</span>
                        </div>
                    </div>
                    <div className='editor-line'>
                        <div className='w-050'>
                            <Input
                                name='value3'
                                onChange={this.onChange}
                                value={this.data.value3}
                            />
                        </div>
                        <div className='w-050'>
                            <Input
                                name='value4'
                                onChange={this.onChange}
                                value={this.data.value4}
                            />
                        </div>
                    </div>

                    <div className='menstruation2-line'>
                        <span></span>
                        <span></span>
                    </div>

                </div>

            </Dialog>
        );
    }

    public componentDidMount(): void {
        //
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        this.visible = nextProps.visible;
    }

    private renderFooter(): any {
        return (
            <span>
                <Button onClick={this.close}>取消</Button>
                <Button type='primary' onClick={this.confirm}>确认</Button>
            </span>
        );
    }

    private open = (): void => {
        const equation = this.props.equation;
        const data = this.data = {};
        const oldData = this.oldData = {};
        const svg = equation.equationElem;
    
        if (svg) {
            const equationElemDom = this.equationElemDom = new DOMParser().parseFromString(
                svg,
                'text/xml'
            ).documentElement;
            const texts = equationElemDom.querySelectorAll('text');
    
            if (texts.length > 0) {
                texts.forEach((text, index) => {
                    // 获取 text 元素中的所有文本内容
                    const tspanElements = text.querySelectorAll('tspan');
                    let curText = '';
                    
                    if (tspanElements.length > 0) {
                        // 拼接所有 tspan 元素的文本内容
                        tspanElements.forEach(tspan => {
                            curText += tspan.textContent.trim() + ' ';
                        });
                        curText = curText.trim(); // 去掉最后一个多余的空格
                    } else {
                        // 直接获取 text 元素的文本内容
                        curText = text.textContent.trim();
                    }
    
                    // 记录数据
                    data['value' + ++index] = curText;
                    oldData['value' + index] = curText;
                });
            }
        }
    
        this.setState({});
    }
    

    private onChange = (value: any, name: string): void => {
        this.data[name] = value;
        this.setState({});
    }

    private close = (bRefresh: boolean = false): void => {
        this.props.close(this.props.id, bRefresh);
        this.visible = false;
        this.setState({});
    }

    private isSameData(): boolean {
        const datas = this.data;
        const oldDatas = this.oldData;

        const keys = Object.keys(datas);
        for (let index = 0, length = keys.length; index < length; index++) {
            const key = keys[index];
            if (datas[key] !== oldDatas[key]) {
                return false;
            }
        }

        return true;
    }

    // private confirm = (): void => {
    //     if (this.isSameData()) {
    //         this.close();
    //         return;
    //     }
    //     const documentCore = this.props.documentCore;
    //     const dom = this.equationElemDom;
    //     const texts = dom.querySelectorAll('tspan');
    //     const datas = this.data;
    //     texts[0].innerHTML = datas.value1 || '';
    //     texts[1].innerHTML = datas.value2 || '';
    //     texts[2].innerHTML = datas.value3 || '';
    //     texts[3].innerHTML = datas.value4 || '';

    //     // 修改属性
    //     const svgStr = dom.outerHTML;
    //     const drawObj = documentCore.getDrawingObjects();
    //     const svgConvertedURI = drawObj.convertSVGToImageString(svgStr);
    //     // drawObj.setDrawingProp(this.props.equation.name, {
    //     documentCore.setDrawingProp(this.props.equation.name, {
    //         width: this.props.equation.width,
    //         src: svgConvertedURI,
    //         equationElem: svgStr,
    //     });
    //     this.close(true);
    // }
    private confirm = (): void => {
        if (this.isSameData()) {
            this.close();
            return;
        }
        
        const documentCore = this.props.documentCore;
        const dom = this.equationElemDom;
        const texts = dom.querySelectorAll('text');
        const datas = this.data;
        
        texts.forEach((text, index) => {
            const tspanElements = text.querySelectorAll('tspan');
            const newValue = datas['value' + (index + 1)] || '';
    
            if (tspanElements.length > 0) {
                // 设置所有 tspan 元素的文本内容
                tspanElements.forEach((tspan, tspanIndex) => {
                    tspan.innerHTML = newValue.split(' ')[tspanIndex] || '';
                });
            } else {
                // 设置 text 元素的文本内容
                text.innerHTML = newValue;
            }
        });
    
        // 修改属性
        const svgStr = dom.outerHTML;
        const drawObj = documentCore.getDrawingObjects();
        const svgConvertedURI = drawObj.convertSVGToImageString(svgStr);
        documentCore.setDrawingProp(this.props.equation.name, {
            width: this.props.equation.width,
            src: svgConvertedURI,
            equationElem: svgStr,
        });
        
        this.close(true);
    }
    

}
