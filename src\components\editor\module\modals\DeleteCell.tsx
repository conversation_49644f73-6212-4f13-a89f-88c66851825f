import * as React from 'react';
import Dialog from '../../ui/Dialog';
import Button from '../../ui/Button';
import Radio from '../../ui/Radio';
import { MenuItemIndex } from '../../../../common/commonDefines';

interface IDialogProps {
    documentCore: any;
    visible: boolean;
    id: string;
    type?: number;
    close: (name: string | number, bRefresh?: boolean) => void;
}

interface IState {
    bRefresh: boolean;
}
export default class DeleteCellDialog extends React.Component<IDialogProps, IState> {
    private visible: boolean;
    private value: number;
    private datas: any[];
    constructor(props: any) {
        super(props);
        this.state = {
            bRefresh: false,
        };
        this.value = props.type;
        this.visible = this.props.visible;
        this.datas = [
            {key: '删除整行', value: MenuItemIndex.DeleteRow},
            {key: '删除整列', value: MenuItemIndex.DeleteCol},
        ];
    }

    public render(): any {
        return (
            <Dialog
                id={this.props.id}
                preventDefault={true}
                visible={this.visible}
                open={this.open}
                footer={this.renderFooter()}
                width={250}
                title={'删除单元格'}
            >
                <div>
                    <div className='editor-line'/>
                    <div className='editor-line'>
                        <Radio value={this.value} data={this.datas} name='value' onChange={this.onChange} />
                    </div>
                </div>
            </Dialog>
        );
    }

    public componentDidMount(): void {
        this.setState({bRefresh: !this.state.bRefresh});
    }

    public UNSAFE_componentWillReceiveProps(nextProps: any): void {
        this.visible = nextProps.visible;
    }

    private renderFooter(): any {
        return (
            <span>
                <Button onClick={this.close}>取消</Button>
                <Button type='primary' onClick={this.confirm}>确认</Button>
            </span>
        );
    }

    private open = (): void => {
        this.value = this.props.type;
        this.setState({});
        //
    }

    private onChange = (value: any, name: string): void => {
        this.value = value;
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private close = (bRefresh?: boolean): void => {
        this.value = null;
        this.props.close(this.props.id, bRefresh);
        this.visible = false;
        this.setState({bRefresh: !this.state.bRefresh});
    }

    private confirm = (): void => {
        switch (this.value) {
            case MenuItemIndex.DeleteRow: {
                this.props.documentCore.removeTableRow();
                break;
            }
            case MenuItemIndex.DeleteCol: {
                this.props.documentCore.removeTableColumn();
                break;
            }
            default:
                return;
        }

        this.close(true);
    }

}
