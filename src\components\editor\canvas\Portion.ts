import { FONT_STYLE_TYPE, FONT_WEIGHT_TYPE, NewControlDefaultSetting } from '../../../common/commonDefines';
import { TextVertAlign } from '../../../model/core/TextProperty';
import { TEXT_SCARE_NUM } from '../../../model/StyleProperty';
import { IDocumentParaPortion } from '../../../model/ParaPortionProperty';
import { ICanvasProps } from './common';
import { ParaNewControlBorder } from '../../../model/core/Paragraph/ParaNewControlBorder';
import { NewControl } from '../../../model/core/NewControl/NewControl';
import { ParaElementType } from '../../../model/core/Paragraph/ParagraphContent';

export class PortionUI {
    private content: IDocumentParaPortion;
    private documentCore: any;
    private ctx: any;
    constructor(props: ICanvasProps) {
        this.content = props.content;
        this.documentCore = props.host.documentCore;
        this.ctx = props.ctx;
        this.render();
    }

    public render(): any {
        const textPro = this.content.textProperty;
        const vertAlign = textPro.vertAlign;
        const fontSize =
            vertAlign === TextVertAlign.Sub || vertAlign === TextVertAlign.Super
                ? (textPro.fontSize / 2) * TEXT_SCARE_NUM
                : textPro.fontSize;
        const weight = FONT_WEIGHT_TYPE.get(textPro.fontWeight);
        const fontStyle = FONT_STYLE_TYPE.get(textPro.fontStyle);

        const { content, startPos, endPos } = this.content;
        const length = content.length;

        const bNewControlBorder = ( 1 <= length &&
                                    (content[0].isNewControlStartBoder() || content[0].isNewControlEndBoder()) )
                                    ? true : false;
        const bNewControlBorderVisible = ( bNewControlBorder && content[0].isVisible() );
        const bNewControlContainerTitle = ( 1 < length && content[0].isNewControlStartBoder()) ? true : false;
        const bPopWinNewControl = (bNewControlBorder && content[0].isPopWinNewControl());
        // 页码
        const pageNums = [];

        let text = '';
        let posX = null;
        let posY = null;
        let dy = null;
        let posX2 = null;
        let posY2 = null;
        let dy2 = null;
        let bParaEnd = false;
        let posX3 = null;
        let posY3 = null;
        let paraEnd = null;
        const ctx = this.ctx;
        let color = textPro.color;
        // color = color.replace('#', '#6f');
        ctx.fillStyle = color;
        ctx.font = `${fontStyle} ${weight} ${fontSize}px ${textPro.fontFamily}`;
        for (let index = 0; index < length; index++) {
            const item = content[index];

            // bNewControlBorder = (item.isNewControlStartBoder() || item.isNewControlEndBoder());
            // bNewControlBorderVisible = bNewControlBorder && item.isVisible();

            // TODO: is this the best way?
            if (item.isParaPageNum()) {
                pageNums.push(item);
                continue;
            }

            if ( item.isParaEnd() ) {
                bParaEnd = true;
                posX3 = '' + item.positionX;
                posY3 = '' + item.positionY;
                paraEnd = item.content;
                continue;
            }

            if (startPos <= index && endPos > index) {
                // 正常绘制文本
                ctx.fillText(item.content, item.positionX, item.positionY);
                
                // text += item.bViewSecret
                //     ? NewControlDefaultSetting.DefaultSecretReplaceChar
                //     : item.content;

                // if ( !(1 <= index && bNewControlContainerTitle) ) {
                //     if (null != posX) {
                //         posX = posX + ' ' + item.positionX;
                //     } else {
                //         posX = item.positionX;
                //     }
                //     if (null != posY /*&& posY !== item.positionY*/) {
                //         posY = posY + ' ' + item.positionY;
                //     } else {
                //         posY = item.positionY;
                //     }
                //     if (null != dy) {
                //         dy = dy + ' ' + (item.dy || 0);
                //     } else {
                //         dy = item.dy;
                //     }
                // } else {
                //     if (null != posX2) {
                //         posX2 = posX2 + ' ' + item.positionX;
                //     } else {
                //         posX2 = item.positionX;
                //     }
                //     if (null != posY2 /*&& posY !== item.positionY*/) {
                //         posY2 = posY2 + ' ' + item.positionY;
                //     } else {
                //         posY2 = item.positionY;
                //     }
                //     if (null != dy2) {
                //         dy2 = dy2 + ' ' + (item.dy || 0);
                //     } else {
                //         dy2 = item.dy;
                //     }
                // }
            }
        }
        return;
        if (bNewControlBorder) {
            const bLineBreak = ( bNewControlContainerTitle && 0 < startPos && text.length < length ) ? true : false;
            let sTitle = '';

            if ( bNewControlContainerTitle && 1 <= length ) {
                sTitle = bLineBreak ? text : text.substring(startPos + 1);
            }

            if ( bLineBreak ) {
                text = '';
            } else {
                text = ('' === sTitle ? text : text.substring(0, 1) );
            }

            if (false === bNewControlBorderVisible) {
                text = NewControlDefaultSetting.DefaultReplaceSpaceChar;
            }
            const mask = undefined;
            const fontFamily = textPro.fontFamily;
            // get newcontrol instance
            // always the first one even if has ph etc
            const newControlBorder: ParaNewControlBorder = content[0] as ParaNewControlBorder;
            const newControl = this.documentCore.getNewControlByName(newControlBorder.getNewControlName());
            // tslint:disable-next-line: max-line-length
            this.renderNewControlBorder(text, posX, posY, fontSize, bPopWinNewControl, newControl, newControlBorder, mask);
            this.renderNewControlTitle(sTitle, mask, posX2, posY2, fontFamily, fontSize, weight, fontStyle, dy)
            return;
        }
        
        // const ctx = this.ctx;
        const bSingleParaEnd = (bParaEnd && '' === text);
        if ( !bParaEnd || bSingleParaEnd ) {
            // if (this.isParaTextExtendFalseSymbol(text)) {
            //     const selectType = this.getParaTextExtendType(text);
            //     let selectHref = radiobox;
            //     // console.log(selectType)
            //     switch (selectType) {
            //         case SelectButtonType.Radiobox: {
            //             selectHref = radiobox;
            //             break;
            //         }
            //         case SelectButtonType.RadioboxActive: {
            //             selectHref = radioboxActive;
            //             break;
            //         }
            //         case SelectButtonType.Checkbox: {
            //             selectHref = checkbox;
            //             break;
            //         }
            //         case SelectButtonType.CheckboxActive: {
            //             selectHref = checkboxActive;
            //             break;
            //         }
            //         default: {
            //             selectHref = radiobox;
            //             break;
            //         }
            //     }
            //     // console.log(content[0].width)
            //     // console.log(this.props.textHeight) // height is according to portion
            //     // console.log(selectHref);
            //     const imgWidth = content[0].width * 0.8;
            //     let imgHeight = this.props.textHeight * 0.8;
            //     if (imgHeight === 0) {
            //         imgHeight = imgWidth;
            //     }
            //     return (
            //         // <text
            //         //     x={posX}
            //         //     y={posY}
            //         //     fontFamily={fontFamily}
            //         //     fontSize={fontSize}
            //         //     fontWeight={weight}
            //         //     fontStyle={fontStyle}
            //         //     fill={textPro.color}
            //         //     // fillOpacity={fillOpacity}
            //         //     // textDecoration={textPro.textDecorationLine}
            //         //     dy={dy}
            //         // >
            //         //     {' '}
            //         //     {text}
            //         // </text>
            //         <image
            //             x={posX}
            //             y={posY - imgHeight}
            //             clipPath={mask}
            //             width={imgWidth}
            //             height={imgHeight}
            //             href={selectHref}
            //             className='select-button'
            //             // transform={'translate(-0,-' + imgHeight + ')'}
            //         />
            //     );
            // }

            const fillOpacity = bSingleParaEnd ? '40%' : undefined;
            if ( bSingleParaEnd ) {
                text = paraEnd;
                posX = posX3;
                posY = posY3;
            }
            let color = textPro.color;
            // color = color.replace('#', '#6f');
            ctx.fillStyle = color;
            ctx.font = `${fontStyle} ${weight} ${fontSize}px ${textPro.fontFamily}`;
            ctx.fillText(' ' + text, posX, posY + dy);
        } else {
            let color = textPro.color;
            // if (bSingleParaEnd) {
            //     color = color.replace('#', '#5f');
            // }
            ctx.fillStyle = color;
            ctx.font = `${fontStyle} ${weight} ${fontSize}px ${textPro.fontFamily}`;
            ctx.fillText(text, posX, posY + dy);

            // color = color.replace('#', '#6f');
            ctx.fillStyle = color;
            ctx.fillText(paraEnd, posX3, posY3);
        }
    }

    private renderNewControlBorder(text: string, posX: any, posY: any, fontSize: any, bPopWinNewControl: boolean,
                                   newControl: NewControl, newControlBorder: ParaNewControlBorder, mask: any): any {
        if ( text && 1 <= text.length ) {
            let fillColor = true !== bPopWinNewControl ? NewControlDefaultSetting.DefaultNewControlBorderColor :
                                                            NewControlDefaultSetting.DefaultNewControlBorderColor2;
            if (newControl != null && newControl.getProperty().isNewControlMustInput) {
                if (newControl.getNewControlTextLength() <= 0) { // if has title can < 0
                    fillColor = NewControlDefaultSetting.DefaultMustInputBorderColor;
                }
            }
            const newControlType = (newControl != null) ? newControl.getType() : null;

            const ctx = this.ctx;
            ctx.fillStyle = '#000';
            // section border
            if ( NewControlDefaultSetting.NewControlSectionStartBorder === text) {
                ctx.font = `normal normal ${fontSize}px 宋体`;
                ctx.fillText(text, posX, posY);
                return;
            } else if (NewControlDefaultSetting.NewControlSectionEndBorder === text) {
                ctx.font = `normal normal ${fontSize}px 宋体`;
                ctx.fillText(text, posX, posY);
                return;
            }

            const bNewControlEndBorder = newControlBorder.content === ']';
            ctx.font = `normal normal ${fontSize}px 宋体`;
            ctx.fillText(text, posX, posY);
        }
    }

    private renderNewControlTitle(title: string, mask: any, posX?: any, posY?: any, fontFamily?: any, fontSize?: any,
                                  weight?: any, fontStyle?: any, dy?: any): any {
        if ( title && 1 <= title.length ) {
            const ctx = this.ctx;
            ctx.fillStyle = this.content.textProperty.color;
            ctx.font = `${fontStyle} ${weight} ${fontSize}px ${fontFamily}`;
            ctx.fillText(title, posX, posY + dy);
        }
    }
}
