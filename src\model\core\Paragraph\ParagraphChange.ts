import { ChangeBaseContent, ChangeBaseObjectProperty, ChangeBaseLongProperty,
    ChangeBase, ChangeBaseDoubleProperty, ChangeBaseBoolProperty } from '../HistoryChange';
import Paragraph from '../Paragraph';
import { ParaElementType } from './ParagraphContent';
import { HistroyItemType, HistoryDescriptionType, changeObjectFactory } from '../HistoryDescription';
import ParaProperty from './ParaProperty';
import ParaPortion from './ParaPortion';

/**
 * 段落添加items
 */
export class ChangeParagraphAddItem extends ChangeBaseContent {
    private lengths: number[];

    constructor( changeClass: Paragraph, pos: number, items: any, bAdd?: boolean ) {
        super(changeClass, pos, items, true);
        this.type = HistroyItemType.ParagraphAddItem;
        this.lengths = [];
        if ( items ) {
            items.forEach((item) => {
                this.lengths.push(item.content.length);
            });
        }
    }

    public undo(): void {
        const para = this.changeClass;
        para.content.splice(this.position, this.items.length);
    }

    public redo(): void {
        const para = this.changeClass;
        const paraStart = para.content.slice(0, this.position);
        const paraEnd = para.content.slice(this.position);

        para.content = paraStart.concat(this.items, paraEnd);

        for (let index = 0, nCount = this.items.length; index < nCount; index++) {
            const element = this.items[index];

            element.parent = this.changeClass;
            if ( element.setParagragh ) {
                element.setParagragh(this.changeClass);
            }
        }
    }

    public isParagraphSimpleChanges(): boolean {
        for (let index = 0, nCount = this.items.length; index < nCount; index++) {
            const element = this.items[index];

            if ( ParaElementType.ParaPortion !== element.type ) {
                return false;
            }
        }

        return true;
    }

    public isDirty(): boolean {
        return (this.changeClass && this.changeClass.isTableCellContent());
    }

    public write(): any {
        const changeClass = this.changeClass.write(this.isAdd());
        const items: any[] = [];
        this.items.forEach((item, index) => {
            if ( item /*&& !(item.isNewControlStart() || item.isNewControlEnd())*/ ) {
                items.push({
                    content: (0 !== this.lengths[index] ? item.getTextContent() : ''),
                    textProperty: item.textProperty.toJson(),
                });
            }
        });
        return {
            type: this.type,
            position: this.position,
            items,
            changeClass,
        };
    }

    public loadIncrementDatas(datas: any): void {
        // const data = datas.data;
        this.position = datas.position;
        this.items = [];

        datas.items.forEach((item) => {
            const portion = new ParaPortion(this.changeClass, null, true);
            if ( 0 < item.content.length ) {
                portion.addText(item.content);
            }

            portion.textProperty.load(item.textProperty);

            this.items.push(portion);
        });

        if ( this.changeClass && this.changeClass.content[this.position] ) {
            const name = this.changeClass.content[this.position].getInsideNewControlName(this.position, true);
            if ( null !== name ) {
                this.changeClass.content[this.position - 1].bPlaceHolder = false;
                this.changeClass.content.splice(this.position, 1);
            }
        }
    }
}

/**
 * 段落删除添加items
 */
export class ChangeParagraphRemoveItem extends ChangeBaseContent {
    private newControlName: string;

    constructor( changeClass: Paragraph, pos: number, items: any, bAdd?: boolean ) {
        super(changeClass, pos, items, false);
        this.type = HistroyItemType.ParagraphRemoveItem;
    }

    public undo(): void {
        const para = this.changeClass;
        const paraStart = para.content.slice(0, this.position);
        const paraEnd = para.content.slice(this.position);

        para.content = paraStart.concat(this.items, paraEnd);

        for (let index = 0, nCount = this.items.length; index < nCount; index++) {
            const element = this.items[index];

            element.parent = this.changeClass;
            if ( element.setParagragh ) {
                element.setParagragh(this.changeClass);
            }
        }
    }

    public redo(): void {
        const para = this.changeClass;
        para.content.splice(this.position, this.items.length);

        if ( null != this.newControlName && '' !== this.newControlName ) {
            para.checkNewControlPlaceContent2(this.newControlName);
        }
    }

    public isDirty(): boolean {
        return (this.changeClass && this.changeClass.isTableCellContent());
    }

    public write(): any {
        const changeClass = this.changeClass.write(this.isAdd());
        const items: any[] = [];
        this.items.forEach((item) => {
            if ( item /*&& !(item.isNewControlStart() || item.isNewControlEnd())*/ ) {
                items.push({
                    content: item.getTextContent(),
                    textProperty: item.textProperty.toJson(),
                });
            }
        });
        return {
            type: this.type,
            position: this.position,
            items,
            changeClass,
        };
    }

    public loadIncrementDatas(datas: any): void {
        // const data = datas.data;
        this.position = datas.position;
        this.items = [];
        this.newControlName = datas.changeClass.newControlName;
        // const content = data.content.split(',');
        datas.items.forEach((item) => {
            const portion = new ParaPortion(this.changeClass, null, true);
            if ( 0 < item.content.length ) {
                portion.addText(item.content);
            }
            // const content = item.content.split(',');
            // item.content.forEach(text => {
            //     const paraText = new ParaText(text);
            //     portion.content.push(paraText);
            // });

            portion.textProperty.load(item.textProperty);

            this.items.push(portion);
        });

        // this.changeClass = ReadFromIncrementDatas(data.changeClass);
    }
}

/**
 * 改变段落属性
 */
// tslint:disable-next-line: max-classes-per-file
export class ChangeParagraphProperty extends ChangeBaseObjectProperty {
    private type: HistroyItemType|HistoryDescriptionType = null;

    constructor( changeClass: Paragraph, old: any, news: any, color: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.ParagraphProperty;
    }

    public setValue( value: ParaProperty ): void {
        const para = this.getClass();
        para.paraProperty = value;
    }
}

/**
 * 改变段落排版
 */
// tslint:disable-next-line: max-classes-per-file
export class ChangeParagraphAlign extends ChangeBaseLongProperty {
    private type: HistroyItemType|HistoryDescriptionType = null;

    constructor( changeClass: Paragraph, old: number, news: any, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.ParagraphAlign;
    }

    public setValue( value: number ): void {
        const para = this.getClass();
        para.paraProperty.alignment = value;
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeParagraphPageBreak extends ChangeBaseLongProperty {
    public bParaSimpleChange: boolean = false;
    private type: HistroyItemType | HistoryDescriptionType = null;

    constructor( changeClass: Paragraph, old: boolean, news: boolean, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.ParagraphPageBreak;
    }

    public setValue( value: boolean ): void {
        const para = this.getClass();
        para.paraProperty.bPageBreakBefore = value;
    }
}

/**
 * 段落属性变化
 */
// tslint:disable-next-line: max-classes-per-file
export class ChangeParagraphPropertyChange extends ChangeBase {
    private oldProperty: ParaProperty = null;
    private newProperty: ParaProperty = null;
    private type: HistroyItemType|HistoryDescriptionType = null;

    constructor( changeClass: Paragraph, old: ParaProperty, news: ParaProperty ) {
        super(changeClass);
        this.oldProperty = old;
        this.newProperty = news;
        this.type = HistroyItemType.ParagraphPropertyChange;
    }

    public undo(): void {
        const para = this.getClass();
        para.paraProperty.propertyChange = this.oldProperty.propertyChange;
    }

    public redo(): void {
        const para = this.getClass();
        para.paraProperty.propertyChange = this.newProperty.propertyChange;
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeParagraphLineSpacing extends ChangeBaseDoubleProperty {
    private type: HistroyItemType|HistoryDescriptionType = null;

    constructor( changeClass: Paragraph, old: number, news: number ) {
        super(changeClass, old, news);
        this.type = HistroyItemType.ParagraphLineSpacing;
    }

    public setValue( value: number ): void {
        const para = this.getClass();
        para.paraProperty.paraSpacing.lineSpacing = value;

        // 需要重新计算portion高度
        for (let i = 0, count = para.content.length; i < count; i++) {
            para.content[i].recalcInfo.bMeasure = true;
        }
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeParagraphLineSpacingRule extends ChangeBaseLongProperty {
    public type: HistroyItemType|HistoryDescriptionType = null;

    constructor( changeClass: Paragraph, old: number, news: number ) {
        super(changeClass, old, news);
        this.type = HistroyItemType.ParagraphLineSpacingRule;
    }

    public setValue( value: number ): void {
        const para = this.getClass();
        para.paraProperty.paraSpacing.lineSpacingType = value;

        // 需要重新计算portion高度
        for (let i = 0, count = para.content.length; i < count; i++) {
            para.content[i].recalcInfo.bMeasure = true;
        }
    }

}

// tslint:disable-next-line: max-classes-per-file
export class ChangeParagraphIndFisrt extends ChangeBaseDoubleProperty {
    public type: HistroyItemType|HistoryDescriptionType = null;

    constructor( changeClass: Paragraph, old: number, news: number ) {
        super(changeClass, old, news);
        this.type = HistroyItemType.ParagraphIndFisrt;
    }

    public setValue( value: number ): void {
        const para = this.getClass();
        para.paraProperty.paraInd.firstLine = value;
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeParagraphIndLeft extends ChangeBaseDoubleProperty {
    public type: HistroyItemType|HistoryDescriptionType = null;

    constructor( changeClass: Paragraph, old: number, news: number ) {
        super(changeClass, old, news);
        this.type = HistroyItemType.ParagraphIndLeft;
    }

    public setValue( value: number ): void {
        const para = this.getClass();
        para.paraProperty.paraInd.left = value;
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeParagraphIndRight extends ChangeBaseDoubleProperty {
    public type: HistroyItemType|HistoryDescriptionType = null;

    constructor( changeClass: Paragraph, old: number, news: number ) {
        super(changeClass, old, news);
        this.type = HistroyItemType.ParagraphIndRight;
    }

    public setValue( value: number ): void {
        const para = this.getClass();
        para.paraProperty.paraInd.right = value;
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeParagraphWordWrap extends ChangeBaseBoolProperty {
    public type: HistroyItemType|HistoryDescriptionType = null;

    constructor( changeClass: Paragraph, old: boolean, news: boolean, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.ParagraphWordWrap;
    }

    public setValue( value: boolean ): void {
        const para = this.getClass();
        para.paraProperty.bWordWrap = value;
    }
}

// tslint:disable-next-line: max-classes-per-file
export class ChangeParagraphHidden extends ChangeBaseBoolProperty {
    public type: HistroyItemType|HistoryDescriptionType = null;

    constructor( changeClass: Paragraph, old: boolean, news: boolean, color?: boolean ) {
        super(changeClass, old, news, color);
        this.type = HistroyItemType.ParagraphHidden;
    }

    public setValue( value: boolean ): void {
        const para = this.getClass();
        para.bHidden = value;
    }
}

changeObjectFactory[HistroyItemType.ParagraphAddItem] = ChangeParagraphAddItem;
changeObjectFactory[HistroyItemType.ParagraphRemoveItem] = ChangeParagraphRemoveItem;
