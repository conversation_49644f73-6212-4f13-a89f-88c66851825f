import EmrEditor from './src/index';
// import './style.css';
import styles from './style.js';
EmrEditor['emrEditorCss'] = styles;

class TestModel {
    private _data: Array<{name: string, id: number}>;
    private _activeId: number;
    private _id: number;
    private _titleDom: HTMLElement;
    private _contentDom: HTMLElement;
    private _addDom: HTMLElement;
    private _editors: any;
    private _defaultEeditor: any;

    constructor() {
        this._init();
    }

    public deleteItem(id: number): void {
        const datas = this._data;
        const index = datas.findIndex((item) => item.id === id);
        if (index === -1) {
            return;
        }

        const editor = this._editors[id];
        if (editor) {
            this._editors[id] = null;
            editor.close();
        }

        datas.splice(index, 1);

        // let dom = this._contentDom.querySelector('#editor-content-' + id);
        // if (dom) {
        //     dom.remove();
        // }
        let dom = this._titleDom.querySelector('#editor-title-' + id);
        if (dom) {
            dom.remove();
        }
    }

    public addItem(): void {
        const id = ++this._id;
        const item = {name: '选项卡' + id, id};
        this._data.push(item);
        this._addTitleDom(item);
        this._addContentDom(item);
        // this._tabClick(id);
    }

    private _initDom(): void {
        const dom = document.createElement('div');
        dom.className = 'editor-test-demo';
        dom.innerHTML = `
            <style>
                * {
                    margin: 0;
                    padding: 0;
                }
                .editor-test-header {
                    position: fixed;
                    left: 10px;
                    bottom: 70px;
                    z-index: 129;
                }
                .editor-test-header > ul > li {
                    position: relative;
                    float: left;
                    padding: 0 5px;
                    margin-right: 10px;
                    margin-bottom: 20px;
                    font-size: 13px;
                    line-height: 26px;
                    line-style: none;
                    cursor: pointer;
                }
                .editor-test-header > ul > li > label {
                    position: absolute;
                    bottom: -30px;
                    left: 48%;
                    cursor: pointer;
                }
                .editor-test-header > ul > li.active, .editor-test-header > ul > li:hover {
                    font-weight: bold;
                    color: #f00;
                }

                .editor-test-content {
                    position: absolute;
                    left: 0px;
                    top: 0;
                    width: 100%;
                    margin: 30px 0px;
                }

                .editor-test-content > div {
                    position: relative;
                    left: 300px;
                    width: 100%;
                    max-width: 1000px;
                    display: none;
                }

                .editor-test-content > div.active {
                    display: block;
                    height: 500px;
                }

                .editor-test-add, .editor-test-test, .editor-test-cursor {
                    position: fixed;
                    left: 30px;
                    bottom: 20px;
                    z-index: 129;
                    color: #f00;
                    cursor: pointer;
                }
                .editor-test-test {
                    left: 120px;
                }
                .editor-test-cursor {
                    left: 200px;
                }
            </style>
            <div class="editor-test-header">
                <ul></ul>
            </div>
            <div class="editor-test-content"></div>
            <div class="editor-test-add">新增选项卡</div>
            <div class="editor-test-test">模拟路由</div>
            <div class="editor-test-cursor">光标切换</div>
        `;
        document.body.appendChild(dom);
        this._titleDom = dom.querySelector('.editor-test-header > ul');
        this._contentDom = dom.querySelector('.editor-test-content');
        this._addDom = dom.querySelector('.editor-test-add');
    }

    private _init(): void {
        this._initDom();
        this._editors = {};
        const data = this._data = [];
        for (let i = 0; i < 1; i++) {
            const item = {name: '选项卡' + i, id: i};
            data.push(item);
            this._id = i;
            this._addTitleDom(item);
            this._addContentDom(item);
        }
        this._addEvent();
    }

    private _addTitleDom(item: any): void {
        const dom = document.createElement('li');
        dom.id = 'editor-title-' + item.id;
        dom.innerHTML = `<span data-title="${item.id}">${item.name}</span><label data-delete="${item.id}">X</label>`;
        this._titleDom.appendChild(dom);
    }

    private _addContentDom(item: any): void {
        const dom = document.createElement('div');
        // dom.innerText = item.name;
        dom.className = 'editor-content';
        dom.id = 'editor-content-' + item.id;
        this._contentDom.appendChild(dom);
        this._tabClick(item.id);
        new EmrEditor()
        .init({id: item.id, dom, isTest: true, bShowToolbar: true, bShowMenu: true,bNeedCustomFont:false,
            height: 0, textColorChangeInRevision: 2, toolbarAlignment: 2})
        .then((editor) => {
            const iframe = dom.querySelector('iframe');
            const editorInterface = editor.getEditor();
            this._editors[item.id] = editorInterface;
            // window['editor' + item.id] = editorInterface;
            // window['testEditor' + item.id] = editor;
            this._defaultEeditor = editor;
            iframe.style.border = '1px solid black';
            iframe.contentDocument.body.style.overflow = 'hidden';
        });
        // editor.setEvent({
        //     nsoKeyPressedEvent: (a, b, c, d) => {
        //         console.log(a, b, c, d);
        //         return true;
        //     },
        //     nsoFileOpenCompleted: (path) => {
        //         console.log(path);
        //     },
        //     nsoStructClick: (name) => {
        //         console.log(name);
        //     },
        //     nsoStructDBClick: (name) => {
        //         console.log(name);
        //     },
        //     nsoStructGainFocus: (name, type) => {
        //         console.log(name, type);
        //     },
        //     nsoStructChanged: (name) => {
        //         console.log(name);
        //     }
        // });
        // editor.setEvent({
        //     nsoStructLostFocus: (name) => {
        //         console.log(name, '结构化元素焦点离开');
        //         console.log(editor.removeEvent(['nsoStructLostFocus']))
        //     },
        // });

        // console.log(editor)
    }

    private _tabClick(id: number): void {
        if (this._activeId === id) {
            return;
        }
        let activeDom = this._titleDom.querySelector('li.active');
        if (activeDom) {
            activeDom.className = '';
        }
        const titleDom = this._titleDom.querySelector('#editor-title-' + id);
        if (titleDom) {
            titleDom.className = 'active';
        }
        activeDom = this._contentDom.querySelector('.editor-content.active');
        if (activeDom) {
            activeDom.className = activeDom.className.replace(' active', '');
        }
        const contentDom = this._contentDom.querySelector('#editor-content-' + id);
        if (contentDom) {
            contentDom.className += ' active';
        }

        this._activeId = id;
        this._defaultEeditor = window['testEditor' + id];
    }

    private _addEvent(): void {
        this._titleDom.addEventListener('click', (e) => {
            const target = e.target as HTMLElement;
            let id = target.getAttribute('data-title');
            if (id) {
                this._tabClick(+id);
                return;
            }
            id = target.getAttribute('data-delete');
            if (id) {
                this.deleteItem(+id);
                return;
            }
        });

        let bFocus = true;
        document.querySelector('.editor-test-cursor').addEventListener('click', (e: MouseEvent) => {
            if (flag) {
                this._defaultEeditor.getEditor().setFocus(false);
            } else {
                this._defaultEeditor.getEditor().setFocus(true);
            }
            flag = !flag;
        });

        this._addDom.addEventListener('click', (e) => {
            this.addItem();
        });

        let flag = true;
        document.querySelector('.editor-test-test').addEventListener('click', (e) => {
            // console.log(this._editors)
            if (flag) {
                this._defaultEeditor.hideEditor();
            } else {
                this._defaultEeditor.showEditor();
            }
            flag = !flag;
        })
    }
}

window.onload = () => {
    const abc = new TestModel();
};
