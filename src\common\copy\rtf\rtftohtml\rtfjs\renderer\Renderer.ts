/*

The MIT License (MIT)

Copyright (c) 2015 Thomas <PERSON>

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.

*/

import * as $ from 'jquery';
import { Document } from '../Document';
import { RTFJSError } from '../Helper';
import { RenderChp } from './RenderChp';
import { RenderPap } from './RenderPap';
import { CSS } from '../parser/CssContainer';

export interface IContainerElement {
    element: JQuery;
    content: JQuery;
}

export class Renderer {
    // tslint:disable-next-line: sy-property-name
    public _doc: Document;
    private _dom: JQuery[];

    private _curRChp: RenderChp;
    private _curRPap: RenderPap;
    private _curpar: JQuery;
    private _cursubpar: JQuery;
    private _curcont: IContainerElement[];
    private _rowIndex: number;
    private _tdIndex: number;
    private _colIndex: number;
    private _colTexts: object; // 存储表格每列的内容, key: rowIndex + colIndex

    constructor(doc: Document) {
        this._doc = doc;
        this._dom = null;

        this._curRChp = null;
        this._curRPap = null;
        this._curpar = null;
        this._cursubpar = null;
        this._curcont = [];
    }

    public pushContainer(contel: IContainerElement): void {
        if (this._curpar == null) {
            this.startPar();
        }

        const len = this._curcont.push(contel);
        if (len > 1) {
            const prevcontel = this._curcont[len - 1];
            prevcontel.content.append(contel.element);
        } else {
            if (this._cursubpar != null) {
                this._cursubpar.append(contel.element);
            } else {
                this._curpar.append(contel.element);
            }
        }
    }

    public popContainer(): void {
        const contel = this._curcont.pop();
        if (contel == null) {
            throw new RTFJSError('No container on rendering stack');
        }
    }

    public buildHyperlinkElement(url: string): JQuery {
        return $('<a>')
            .attr('href', url);
    }

    // tslint:disable-next-line: sy-method-name
    public _appendToPar(el: JQuery, newsubpar?: boolean): void {
        if (this._curpar == null) {
            this.startPar();
        }
        if (newsubpar === true) {
            let subpar = $('<div>');
            if (this._cursubpar == null) {
                this._curpar.children()
                    .appendTo(subpar);
                this._curpar.append(subpar);
                subpar = $('<div>');
            }
            if (el) {
                subpar.append(el);
            }
            if (this._curRPap != null) {
                this._curRPap.apply(this._doc, subpar, this._curRChp, false);
            }

            this._cursubpar = subpar;
            this._curpar.append(subpar);
        } else if (el) {
            const contelCnt = this._curcont.length;
            if (contelCnt > 0) {
                this._curcont[contelCnt - 1].content.append(el);
            } else if (this._cursubpar != null) {
                this._cursubpar.append(el);
            } else {
                this._curpar.append(el);
            }
        }
    }

    public startPar(): void {
        this._curpar = $('<div>');
        if (this._curRPap != null) {
            this._curRPap.apply(this._doc, this._curpar, this._curRChp, true);
            this._curRPap.apply(this._doc, this._curpar, this._curRChp, false);
        }
        this._cursubpar = null;
        this._curcont = [];
        this._dom.push(this._curpar);
    }

    public lineBreak(): void {
        this._appendToPar(null, true);
    }

    public setChp(rchp: RenderChp): void {
        this._curRChp = rchp;
    }

    public setPap(rpap: RenderPap): void {
        this._curRPap = rpap;
        if (this._cursubpar != null) {
            this._curRPap.apply(this._doc, this._cursubpar, null, false);
        } else if (this._curpar != null) {
            // Don't have a sub-paragraph at all, apply everything
            this._curRPap.apply(this._doc, this._curpar, null, true);
            this._curRPap.apply(this._doc, this._curpar, null, false);
        }
    }

    public appendElement(element: JQuery): void {
        this._appendToPar(element);
    }

    public buildRenderedPicture(element: JQuery): JQuery {
        if (element == null) {
            element = $('<span>')
                .text('[failed to render image]');
        }
        return element;
    }

    public renderedPicture(element: JQuery): void {
        this._appendToPar(this.buildRenderedPicture(element));
    }

    public buildPicture(mime: string, data: string): JQuery {
        if (data != null) {
            return $('<img>', {
                src: 'data:' + mime + ';base64,' + btoa(data),
            });
        } else {
            let err = 'image type not supported';
            if (typeof mime === 'string' && mime !== '') {
                err = mime;
            }
            return $('<span>')
                .text('[' + err + ']');
        }
    }

    public picture(mime: string, data: string): void {
        this._appendToPar(this.buildPicture(mime, data));
    }

    public buildTable(rows: string[][][]): string {
        const widths = this._doc._tableArea.shift();
        let trs = '';
        for (let i = 0, ii = rows.length; i < ii; i++) {
            const tr = rows[i];
            let tds = '';
            for (let j = 0, jj = tr.length; j < jj; j++) {
                // let td = tr[j];
                const key = i.toString() + j;
                const spans = this._colTexts[key] || '';
                tds += `<td width="${widths[j]}">${spans}</td>`;
            }
            trs += `<tr>${tds}</tr>`;
        }

        return `<table>${trs}</table>`;
    }

    /**
     * 查询rows中否还存在下一列
     * @param {Array} rows: 表格的行
     * @param {CSS} css: 获取样式类实例
     * @return {Boolean}: true|false
     */
    public isExistCol(rows: string[][][], css: CSS): boolean {
        for (let i = this._rowIndex, ii = rows.length; i < ii; i++) {
            const tr = rows[i];
            this._rowIndex = i;
            for (let j = this._tdIndex, jj = tr.length; j < jj; j++) {
                const td = tr[j];
                this._tdIndex = j;
                const len = td.length; // 一个td是否可分成多个col
                if (len === 0) {
                    this._colIndex = 0;
                    if (j + 1 === jj) { // 最后一列时进行置零
                        this._tdIndex = 0;
                    }
                    continue;
                }
                for (let k = this._colIndex; k < len; k++) {
                    const key = i.toString() + j;
                    this._colTexts[key] = (this._colTexts[key] || '')
                        + `<span style="${css.getStyle()}">${td[k]}</span>`;
                    // 达到最大值时
                    if (k === len - 1) {
                        this._colIndex = 0;
                        if (j + 1 === jj ) {
                            this._tdIndex = 0;
                            this._rowIndex++;
                        } else {
                            this._tdIndex++;
                        }
                    } else {
                        this._colIndex++;
                    }
                    return true;
                }
            }
        }

        return false;
    }

    public buildDom(): JQuery[] {
        if (this._dom != null) {
            return this._dom;
        }

        this._dom = [];

        this._curRChp = null;
        this._curRPap = null;
        this._curpar = null;
        const deleteArrs = ['¢', ':wy{', '£¥z'];
        const tables = this._doc._table;
        let firstTable = tables[0];
        let rows;
        let isTable = false;
        let flag = false;
        const len = this._doc._ins.length;
        // console.log(this._doc._tableArea)
        let i = 0;
        for (; i < len; i++) {
            const ins = this._doc._ins[i];
            if (typeof ins === 'string') {
                if (deleteArrs.length > 0 && deleteArrs.shift() === ins) {
                    continue;
                }
                let key: number;
                // tslint:disable-next-line: no-conditional-assignment
                if (firstTable && (key = firstTable.key) === i) {
                    rows = [];
                    let lastIndex: number;
                    for (let j = 0, jj = tables.length; j < jj; j++) {
                        const row = tables[j];
                        if (row.key === key) {
                            rows.push(row.value);
                            lastIndex = j;
                        } else {
                            break;
                        }
                    }
                    tables.splice(0, lastIndex + 1);
                    this._rowIndex = 0;
                    this._tdIndex = 0;
                    this._colIndex = 0;
                    this._colTexts = {};
                    isTable = true;
                    firstTable = null;
                }
                if (isTable === true) { // 表格单独处理
                    const css = new CSS();
                    if (this._curRChp != null) {
                        this._curRChp.apply(this._doc, css);
                    }
                    flag = this.isExistCol(rows, css); // 判断是否还存在下一列， 不存在生成table，存在继续遍历
                    if (flag === false) {
                        const table = this.buildTable(rows);
                        this._appendToPar($(table));
                        i--;
                        isTable = false;
                        firstTable = tables[0];
                    }
                } else {
                    const span = $('<span>')
                        .text(ins);
                    if (this._curRChp != null) {
                        this._curRChp.apply(this._doc, span);
                    }
                    this._appendToPar(span);
                }
            } else {
                ins(this);
            }
        }

        // 对表格进行补充：有可能输出的文字已经不存在，但是表格还没进行嵌入
        if (flag === true && isTable === true) {
            const table = this.buildTable(rows);
            this._appendToPar($(table));
            // console.log($(table))
        }

        // console.log(this._dom);

        return this._dom;
    }
}
