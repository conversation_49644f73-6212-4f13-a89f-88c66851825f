import { ChangeBaseContent } from '../HistoryChange';
import { GraphicObjects } from './GraphicObjects';
import { HistroyItemType } from '../HistoryDescription';

export class ChangeGraphicObjectsAddParaImage extends ChangeBaseContent {
    constructor( changeClass: GraphicObjects, pos: number, items: any, color?: boolean ) {
        super(changeClass, pos, items, true);

        this.type = HistroyItemType.GraphicObjectsAddParaImage;
    }

    /**
     * 删除加入的items
     */
    public undo(): void {
        const graphicObjects = this.changeClass as GraphicObjects;
        const drawingObjects = graphicObjects.getGraphicObject();
        if ( this.items ) {
            this.items.forEach((item) => {
                drawingObjects.delete(item.name);
            });
        }
    }

    /**
     * 重新加入items
     */
    public redo(): void {
        const graphicObjects = this.changeClass as GraphicObjects;
        const drawingObjects = graphicObjects.getGraphicObject();
        if ( this.items ) {
            this.items.forEach((item) => {
                drawingObjects.set(item.name, item);
            });
        }
    }

    public createReverseChange(): ChangeGraphicObjectsRemoveParaImage {
        return this.createReverseChangeBase(ChangeGraphicObjectsRemoveParaImage);
    }
}

export class ChangeGraphicObjectsRemoveParaImage extends ChangeBaseContent {
    constructor( changeClass: GraphicObjects, pos: number, items: any, color?: boolean ) {
        super(changeClass, pos, items, true);

        this.type = HistroyItemType.GraphicObjectsRemoveParaImage;
    }

    /**
     * 删除加入的items
     */
    public undo(): void {
        const graphicObjects = this.changeClass as GraphicObjects;
        const drawingObjects = graphicObjects.getGraphicObject();
        if ( this.items ) {
            this.items.forEach((item) => {
                drawingObjects.set(item.name, item);
            });
        }
    }

    /**
     * 重新加入items
     */
    public redo(): void {
        const graphicObjects = this.changeClass as GraphicObjects;
        const drawingObjects = graphicObjects.getGraphicObject();
        if ( this.items ) {
            this.items.forEach((item) => {
                drawingObjects.delete(item.name);
            });
        }
    }

    public createReverseChange(): ChangeGraphicObjectsAddParaImage {
        return this.createReverseChangeBase(ChangeGraphicObjectsAddParaImage);
    }
}
